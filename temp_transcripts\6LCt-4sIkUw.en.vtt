WEBVTT
Kind: captions
Language: en

00:00:01.480 --> 00:00:03.550 align:start position:0%
 
GitHub<00:00:01.839><c> co-pilot</c><00:00:02.240><c> for</c><00:00:02.399><c> Enterprise</c><00:00:03.120><c> stable</c>

00:00:03.550 --> 00:00:03.560 align:start position:0%
GitHub co-pilot for Enterprise stable
 

00:00:03.560 --> 00:00:06.269 align:start position:0%
GitHub co-pilot for Enterprise stable
diffusion<00:00:04.080><c> 3</c><00:00:04.880><c> the</c><00:00:05.040><c> White</c><00:00:05.319><c> House</c><00:00:05.640><c> wants</c><00:00:05.920><c> you</c><00:00:06.040><c> to</c>

00:00:06.269 --> 00:00:06.279 align:start position:0%
diffusion 3 the White House wants you to
 

00:00:06.279 --> 00:00:09.629 align:start position:0%
diffusion 3 the White House wants you to
stop<00:00:06.640><c> coding</c><00:00:07.040><c> and</c><00:00:07.240><c> c</c><00:00:07.600><c> and</c><00:00:08.040><c> C++</c><00:00:09.040><c> and</c><00:00:09.240><c> a</c><00:00:09.360><c> pick</c><00:00:09.519><c> of</c>

00:00:09.629 --> 00:00:09.639 align:start position:0%
stop coding and c and C++ and a pick of
 

00:00:09.639 --> 00:00:11.870 align:start position:0%
stop coding and c and C++ and a pick of
the<00:00:09.719><c> week</c><00:00:09.960><c> that</c><00:00:10.080><c> makes</c><00:00:10.360><c> a</c><00:00:10.519><c> hacker</c><00:00:10.800><c> tool</c><00:00:11.240><c> into</c><00:00:11.719><c> a</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
the week that makes a hacker tool into a
 

00:00:11.880 --> 00:00:14.110 align:start position:0%
the week that makes a hacker tool into a
Nintendo<00:00:12.400><c> Wii</c><00:00:12.759><c> controller</c><00:00:13.679><c> all</c><00:00:13.839><c> that</c><00:00:14.000><c> and</c>

00:00:14.110 --> 00:00:14.120 align:start position:0%
Nintendo Wii controller all that and
 

00:00:14.120 --> 00:00:18.110 align:start position:0%
Nintendo Wii controller all that and
more<00:00:14.320><c> on</c><00:00:14.480><c> this</c><00:00:14.639><c> episode</c><00:00:15.040><c> of</c><00:00:15.400><c> the</c>

00:00:18.110 --> 00:00:18.120 align:start position:0%
 
 

00:00:18.120 --> 00:00:20.830 align:start position:0%
 
[Music]

00:00:20.830 --> 00:00:20.840 align:start position:0%
[Music]
 

00:00:20.840 --> 00:00:22.990 align:start position:0%
[Music]
download<00:00:21.840><c> welcome</c><00:00:22.119><c> back</c><00:00:22.240><c> to</c><00:00:22.400><c> another</c><00:00:22.640><c> episode</c>

00:00:22.990 --> 00:00:23.000 align:start position:0%
download welcome back to another episode
 

00:00:23.000 --> 00:00:24.670 align:start position:0%
download welcome back to another episode
of<00:00:23.160><c> the</c><00:00:23.279><c> download</c><00:00:23.760><c> I'm</c><00:00:23.880><c> your</c><00:00:24.080><c> host</c><00:00:24.279><c> Christina</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
of the download I'm your host Christina
 

00:00:24.680 --> 00:00:26.189 align:start position:0%
of the download I'm your host Christina
Warren<00:00:25.000><c> senior</c><00:00:25.279><c> developer</c><00:00:25.680><c> Advocate</c><00:00:26.039><c> at</c>

00:00:26.189 --> 00:00:26.199 align:start position:0%
Warren senior developer Advocate at
 

00:00:26.199 --> 00:00:27.870 align:start position:0%
Warren senior developer Advocate at
GitHub<00:00:26.760><c> and</c><00:00:27.039><c> this</c><00:00:27.119><c> is</c><00:00:27.240><c> the</c><00:00:27.400><c> show</c><00:00:27.599><c> where</c><00:00:27.720><c> we</c>

00:00:27.870 --> 00:00:27.880 align:start position:0%
GitHub and this is the show where we
 

00:00:27.880 --> 00:00:29.550 align:start position:0%
GitHub and this is the show where we
cover<00:00:28.119><c> the</c><00:00:28.199><c> latest</c><00:00:28.480><c> developer</c><00:00:28.840><c> news</c><00:00:29.240><c> and</c><00:00:29.359><c> open</c>

00:00:29.550 --> 00:00:29.560 align:start position:0%
cover the latest developer news and open
 

00:00:29.560 --> 00:00:31.310 align:start position:0%
cover the latest developer news and open
source<00:00:30.000><c> Projects</c><00:00:30.720><c> please</c><00:00:31.000><c> like</c><00:00:31.160><c> And</c>

00:00:31.310 --> 00:00:31.320 align:start position:0%
source Projects please like And
 

00:00:31.320 --> 00:00:33.950 align:start position:0%
source Projects please like And
subscribe<00:00:32.320><c> and</c><00:00:32.680><c> my</c><00:00:32.800><c> shirt</c><00:00:33.160><c> this</c><00:00:33.320><c> week</c><00:00:33.559><c> is</c><00:00:33.719><c> from</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
subscribe and my shirt this week is from
 

00:00:33.960 --> 00:00:37.030 align:start position:0%
subscribe and my shirt this week is from
open<00:00:34.239><c> ai's</c><00:00:34.719><c> Dev</c><00:00:35.079><c> day</c><00:00:35.559><c> back</c><00:00:35.760><c> in</c><00:00:35.960><c> November</c><00:00:36.920><c> and</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
open ai's Dev day back in November and
 

00:00:37.040 --> 00:00:38.430 align:start position:0%
open ai's Dev day back in November and
you<00:00:37.120><c> know</c><00:00:37.320><c> that</c><00:00:37.399><c> was</c><00:00:37.559><c> the</c><00:00:37.640><c> dev</c><00:00:37.879><c> day</c><00:00:38.120><c> that</c><00:00:38.239><c> took</c>

00:00:38.430 --> 00:00:38.440 align:start position:0%
you know that was the dev day that took
 

00:00:38.440 --> 00:00:40.270 align:start position:0%
you know that was the dev day that took
place<00:00:38.719><c> like</c><00:00:38.960><c> a</c><00:00:39.120><c> week</c><00:00:39.360><c> before</c><00:00:39.719><c> that</c><00:00:39.879><c> weird</c>

00:00:40.270 --> 00:00:40.280 align:start position:0%
place like a week before that weird
 

00:00:40.280 --> 00:00:42.750 align:start position:0%
place like a week before that weird
5-day<00:00:40.879><c> period</c><00:00:41.320><c> where</c><00:00:41.600><c> CEO</c><00:00:42.000><c> Sam</c><00:00:42.239><c> Alman</c><00:00:42.600><c> was</c>

00:00:42.750 --> 00:00:42.760 align:start position:0%
5-day period where CEO Sam Alman was
 

00:00:42.760 --> 00:00:45.190 align:start position:0%
5-day period where CEO Sam Alman was
ousted<00:00:43.640><c> as</c><00:00:43.800><c> open</c><00:00:44.039><c> AI</c><00:00:44.399><c> CEO</c><00:00:44.840><c> and</c><00:00:44.960><c> then</c><00:00:45.079><c> the</c>

00:00:45.190 --> 00:00:45.200 align:start position:0%
ousted as open AI CEO and then the
 

00:00:45.200 --> 00:00:46.670 align:start position:0%
ousted as open AI CEO and then the
internet<00:00:45.520><c> or</c><00:00:45.680><c> at</c><00:00:45.760><c> least</c><00:00:46.079><c> my</c><00:00:46.239><c> part</c><00:00:46.399><c> of</c><00:00:46.520><c> the</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
internet or at least my part of the
 

00:00:46.680 --> 00:00:48.950 align:start position:0%
internet or at least my part of the
internet<00:00:47.680><c> couldn't</c><00:00:47.920><c> stop</c><00:00:48.160><c> talking</c><00:00:48.399><c> about</c><00:00:48.600><c> it</c>

00:00:48.950 --> 00:00:48.960 align:start position:0%
internet couldn't stop talking about it
 

00:00:48.960 --> 00:00:50.869 align:start position:0%
internet couldn't stop talking about it
yeah<00:00:49.199><c> so</c><00:00:49.440><c> that's</c><00:00:49.600><c> where</c><00:00:49.800><c> this</c><00:00:49.920><c> is</c><00:00:50.120><c> from</c><00:00:50.559><c> and</c><00:00:50.760><c> if</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
yeah so that's where this is from and if
 

00:00:50.879 --> 00:00:53.670 align:start position:0%
yeah so that's where this is from and if
Sam<00:00:51.239><c> had</c><00:00:51.480><c> not</c><00:00:51.760><c> been</c><00:00:52.199><c> reappointed</c><00:00:52.920><c> CEO</c><00:00:53.480><c> then</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
Sam had not been reappointed CEO then
 

00:00:53.680 --> 00:00:54.790 align:start position:0%
Sam had not been reappointed CEO then
this<00:00:53.800><c> thing</c><00:00:54.039><c> would</c><00:00:54.239><c> probably</c><00:00:54.559><c> be</c><00:00:54.680><c> a</c>

00:00:54.790 --> 00:00:54.800 align:start position:0%
this thing would probably be a
 

00:00:54.800 --> 00:00:57.029 align:start position:0%
this thing would probably be a
collector's<00:00:55.239><c> item</c><00:00:55.960><c> but</c><00:00:56.120><c> as</c><00:00:56.239><c> it</c><00:00:56.359><c> stands</c><00:00:56.920><c> it's</c>

00:00:57.029 --> 00:00:57.039 align:start position:0%
collector's item but as it stands it's
 

00:00:57.039 --> 00:00:58.790 align:start position:0%
collector's item but as it stands it's
just<00:00:57.160><c> a</c><00:00:57.280><c> crew</c><00:00:57.520><c> neck</c><00:00:57.760><c> with</c><00:00:57.840><c> a</c><00:00:57.960><c> good</c><00:00:58.120><c> story</c><00:00:58.559><c> so</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
just a crew neck with a good story so
 

00:00:58.800 --> 00:01:00.630 align:start position:0%
just a crew neck with a good story so
that's<00:00:59.000><c> it</c><00:00:59.719><c> um</c><00:00:59.960><c> all</c><00:01:00.079><c> right</c><00:01:00.199><c> we've</c><00:01:00.320><c> got</c><00:01:00.399><c> a</c><00:01:00.480><c> ton</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
that's it um all right we've got a ton
 

00:01:00.640 --> 00:01:02.029 align:start position:0%
that's it um all right we've got a ton
of<00:01:00.719><c> news</c><00:01:00.920><c> to</c><00:01:01.000><c> get</c><00:01:01.120><c> into</c><00:01:01.359><c> this</c><00:01:01.480><c> week</c><00:01:01.680><c> so</c><00:01:01.840><c> let's</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
of news to get into this week so let's
 

00:01:02.039 --> 00:01:05.229 align:start position:0%
of news to get into this week so let's
do<00:01:02.280><c> that</c><00:01:03.120><c> all</c><00:01:03.239><c> right</c><00:01:03.480><c> first</c><00:01:03.800><c> things</c><00:01:04.199><c> first</c><00:01:04.839><c> the</c>

00:01:05.229 --> 00:01:05.239 align:start position:0%
do that all right first things first the
 

00:01:05.239 --> 00:01:06.550 align:start position:0%
do that all right first things first the
uh<00:01:05.400><c> is</c><00:01:05.560><c> that</c><00:01:05.760><c> GitHub</c><00:01:06.040><c> co-pilot</c><00:01:06.439><c> for</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
uh is that GitHub co-pilot for
 

00:01:06.560 --> 00:01:08.830 align:start position:0%
uh is that GitHub co-pilot for
Enterprise<00:01:07.200><c> is</c><00:01:07.400><c> now</c><00:01:07.640><c> generally</c><00:01:08.200><c> available</c>

00:01:08.830 --> 00:01:08.840 align:start position:0%
Enterprise is now generally available
 

00:01:08.840 --> 00:01:10.270 align:start position:0%
Enterprise is now generally available
and<00:01:09.320><c> I</c><00:01:09.400><c> know</c><00:01:09.560><c> that</c><00:01:09.680><c> most</c><00:01:09.840><c> of</c><00:01:09.920><c> the</c><00:01:10.000><c> people</c><00:01:10.200><c> who</c>

00:01:10.270 --> 00:01:10.280 align:start position:0%
and I know that most of the people who
 

00:01:10.280 --> 00:01:12.030 align:start position:0%
and I know that most of the people who
are<00:01:10.479><c> watching</c><00:01:10.880><c> this</c><00:01:11.119><c> are</c><00:01:11.360><c> not</c><00:01:11.560><c> the</c><00:01:11.720><c> people</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
are watching this are not the people
 

00:01:12.040 --> 00:01:13.950 align:start position:0%
are watching this are not the people
making<00:01:12.520><c> the</c><00:01:12.720><c> purchasing</c><00:01:13.200><c> decisions</c><00:01:13.720><c> for</c><00:01:13.880><c> the</c>

00:01:13.950 --> 00:01:13.960 align:start position:0%
making the purchasing decisions for the
 

00:01:13.960 --> 00:01:15.469 align:start position:0%
making the purchasing decisions for the
companies<00:01:14.320><c> they</c><00:01:14.439><c> work</c><00:01:14.640><c> for</c><00:01:14.960><c> but</c><00:01:15.119><c> if</c><00:01:15.200><c> you</c><00:01:15.320><c> do</c>

00:01:15.469 --> 00:01:15.479 align:start position:0%
companies they work for but if you do
 

00:01:15.479 --> 00:01:18.030 align:start position:0%
companies they work for but if you do
control<00:01:15.880><c> Enterprise</c><00:01:16.240><c> spending</c><00:01:16.920><c> hi</c><00:01:17.720><c> uh</c><00:01:17.920><c> but</c>

00:01:18.030 --> 00:01:18.040 align:start position:0%
control Enterprise spending hi uh but
 

00:01:18.040 --> 00:01:19.350 align:start position:0%
control Enterprise spending hi uh but
hopefully<00:01:18.360><c> this</c><00:01:18.479><c> is</c><00:01:18.560><c> a</c><00:01:18.680><c> tool</c><00:01:18.960><c> that</c><00:01:19.080><c> some</c><00:01:19.200><c> of</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
hopefully this is a tool that some of
 

00:01:19.360 --> 00:01:20.990 align:start position:0%
hopefully this is a tool that some of
you<00:01:19.560><c> might</c><00:01:19.759><c> come</c><00:01:19.920><c> across</c><00:01:20.240><c> in</c><00:01:20.360><c> your</c><00:01:20.520><c> day</c><00:01:20.720><c> job</c><00:01:20.920><c> in</c>

00:01:20.990 --> 00:01:21.000 align:start position:0%
you might come across in your day job in
 

00:01:21.000 --> 00:01:23.149 align:start position:0%
you might come across in your day job in
the<00:01:21.119><c> future</c><00:01:21.600><c> and</c><00:01:21.720><c> so</c><00:01:22.400><c> get</c><00:01:22.560><c> up</c><00:01:22.720><c> go</c><00:01:22.840><c> pilot</c><00:01:23.079><c> for</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
the future and so get up go pilot for
 

00:01:23.159 --> 00:01:24.390 align:start position:0%
the future and so get up go pilot for
Enterprise<00:01:23.600><c> was</c><00:01:23.720><c> announced</c><00:01:24.119><c> back</c><00:01:24.240><c> in</c>

00:01:24.390 --> 00:01:24.400 align:start position:0%
Enterprise was announced back in
 

00:01:24.400 --> 00:01:25.990 align:start position:0%
Enterprise was announced back in
November<00:01:24.920><c> and</c><00:01:25.079><c> we've</c><00:01:25.280><c> been</c><00:01:25.400><c> testing</c><00:01:25.720><c> it</c><00:01:25.880><c> with</c>

00:01:25.990 --> 00:01:26.000 align:start position:0%
November and we've been testing it with
 

00:01:26.000 --> 00:01:28.469 align:start position:0%
November and we've been testing it with
select<00:01:26.360><c> customers</c><00:01:27.000><c> since</c><00:01:27.360><c> then</c><00:01:27.720><c> and</c><00:01:28.280><c> like</c><00:01:28.400><c> I</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
select customers since then and like I
 

00:01:28.479 --> 00:01:30.429 align:start position:0%
select customers since then and like I
said<00:01:28.680><c> now</c><00:01:28.799><c> it's</c><00:01:28.960><c> generally</c><00:01:29.360><c> available</c><00:01:30.240><c> and</c>

00:01:30.429 --> 00:01:30.439 align:start position:0%
said now it's generally available and
 

00:01:30.439 --> 00:01:32.350 align:start position:0%
said now it's generally available and
like<00:01:30.720><c> GitHub</c><00:01:31.079><c> co-pilot</c><00:01:31.560><c> and</c><00:01:31.680><c> GitHub</c><00:01:32.000><c> copilot</c>

00:01:32.350 --> 00:01:32.360 align:start position:0%
like GitHub co-pilot and GitHub copilot
 

00:01:32.360 --> 00:01:33.749 align:start position:0%
like GitHub co-pilot and GitHub copilot
for<00:01:32.479><c> business</c><00:01:32.920><c> GitHub</c><00:01:33.280><c> co-pilot</c><00:01:33.640><c> for</c>

00:01:33.749 --> 00:01:33.759 align:start position:0%
for business GitHub co-pilot for
 

00:01:33.759 --> 00:01:36.469 align:start position:0%
for business GitHub co-pilot for
Enterprise<00:01:34.759><c> is</c><00:01:34.880><c> your</c><00:01:35.119><c> AI</c><00:01:35.439><c> coding</c><00:01:35.840><c> assistant</c>

00:01:36.469 --> 00:01:36.479 align:start position:0%
Enterprise is your AI coding assistant
 

00:01:36.479 --> 00:01:38.429 align:start position:0%
Enterprise is your AI coding assistant
except<00:01:36.880><c> now</c><00:01:37.159><c> it</c><00:01:37.280><c> can</c><00:01:37.399><c> do</c><00:01:37.560><c> some</c><00:01:37.720><c> things</c><00:01:38.280><c> that</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
except now it can do some things that
 

00:01:38.439 --> 00:01:40.069 align:start position:0%
except now it can do some things that
Enterprise<00:01:38.920><c> customers</c><00:01:39.360><c> have</c><00:01:39.479><c> been</c><00:01:39.640><c> asking</c><00:01:39.920><c> us</c>

00:01:40.069 --> 00:01:40.079 align:start position:0%
Enterprise customers have been asking us
 

00:01:40.079 --> 00:01:42.270 align:start position:0%
Enterprise customers have been asking us
for<00:01:40.320><c> and</c><00:01:40.439><c> so</c><00:01:40.880><c> that</c><00:01:41.040><c> includes</c><00:01:41.680><c> fine-tuned</c>

00:01:42.270 --> 00:01:42.280 align:start position:0%
for and so that includes fine-tuned
 

00:01:42.280 --> 00:01:44.630 align:start position:0%
for and so that includes fine-tuned
models<00:01:42.640><c> based</c><00:01:42.880><c> on</c><00:01:42.960><c> your</c><00:01:43.119><c> organization's</c><00:01:43.840><c> code</c>

00:01:44.630 --> 00:01:44.640 align:start position:0%
models based on your organization's code
 

00:01:44.640 --> 00:01:46.310 align:start position:0%
models based on your organization's code
and<00:01:44.840><c> fine-tuning</c><00:01:45.479><c> is</c><00:01:45.640><c> actually</c><00:01:45.920><c> going</c><00:01:46.119><c> to</c><00:01:46.200><c> be</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
and fine-tuning is actually going to be
 

00:01:46.320 --> 00:01:48.350 align:start position:0%
and fine-tuning is actually going to be
launching<00:01:46.840><c> soon</c><00:01:47.360><c> so</c><00:01:47.520><c> that</c><00:01:47.600><c> you</c><00:01:47.680><c> can</c><00:01:47.880><c> customize</c>

00:01:48.350 --> 00:01:48.360 align:start position:0%
launching soon so that you can customize
 

00:01:48.360 --> 00:01:49.630 align:start position:0%
launching soon so that you can customize
co-pilot<00:01:48.880><c> so</c><00:01:49.000><c> that</c><00:01:49.079><c> it's</c><00:01:49.200><c> going</c><00:01:49.280><c> to</c><00:01:49.399><c> give</c><00:01:49.520><c> you</c>

00:01:49.630 --> 00:01:49.640 align:start position:0%
co-pilot so that it's going to give you
 

00:01:49.640 --> 00:01:52.429 align:start position:0%
co-pilot so that it's going to give you
better<00:01:49.880><c> results</c><00:01:50.600><c> for</c><00:01:50.840><c> your</c><00:01:51.280><c> organization</c><00:01:52.280><c> but</c>

00:01:52.429 --> 00:01:52.439 align:start position:0%
better results for your organization but
 

00:01:52.439 --> 00:01:54.230 align:start position:0%
better results for your organization but
GitHub<00:01:52.759><c> copilot</c><00:01:53.119><c> for</c><00:01:53.240><c> Enterprise</c><00:01:53.880><c> also</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
GitHub copilot for Enterprise also
 

00:01:54.240 --> 00:01:56.429 align:start position:0%
GitHub copilot for Enterprise also
integrates<00:01:54.960><c> with</c><00:01:55.159><c> github.com</c><00:01:55.799><c> so</c><00:01:56.240><c> that</c><00:01:56.360><c> you</c>

00:01:56.429 --> 00:01:56.439 align:start position:0%
integrates with github.com so that you
 

00:01:56.439 --> 00:01:58.510 align:start position:0%
integrates with github.com so that you
can<00:01:56.640><c> use</c><00:01:56.920><c> the</c><00:01:57.079><c> chat</c><00:01:57.360><c> feature</c><00:01:58.039><c> to</c><00:01:58.240><c> ask</c>

00:01:58.510 --> 00:01:58.520 align:start position:0%
can use the chat feature to ask
 

00:01:58.520 --> 00:02:00.670 align:start position:0%
can use the chat feature to ask
questions<00:01:58.840><c> about</c><00:01:59.000><c> your</c><00:01:59.159><c> codebase</c><00:01:59.640><c> or</c><00:01:59.960><c> docs</c><00:02:00.479><c> or</c>

00:02:00.670 --> 00:02:00.680 align:start position:0%
questions about your codebase or docs or
 

00:02:00.680 --> 00:02:02.910 align:start position:0%
questions about your codebase or docs or
knowledge<00:02:01.039><c> base</c><00:02:01.840><c> and</c><00:02:02.039><c> it</c><00:02:02.119><c> can</c><00:02:02.280><c> also</c><00:02:02.439><c> be</c><00:02:02.600><c> used</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
knowledge base and it can also be used
 

00:02:02.920 --> 00:02:05.510 align:start position:0%
knowledge base and it can also be used
with<00:02:03.119><c> pull</c><00:02:03.439><c> requests</c><00:02:04.320><c> um</c><00:02:04.479><c> basically</c><00:02:04.920><c> to</c><00:02:05.320><c> to</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
with pull requests um basically to to
 

00:02:05.520 --> 00:02:08.469 align:start position:0%
with pull requests um basically to to
analyze<00:02:06.200><c> um</c><00:02:06.399><c> their</c><00:02:06.560><c> diffs</c><00:02:07.280><c> uh</c><00:02:07.479><c> in</c><00:02:07.600><c> the</c><00:02:07.799><c> pr</c><00:02:08.280><c> so</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
analyze um their diffs uh in the pr so
 

00:02:08.479 --> 00:02:10.470 align:start position:0%
analyze um their diffs uh in the pr so
that<00:02:08.920><c> reviews</c><00:02:09.280><c> can</c><00:02:09.479><c> happen</c><00:02:09.679><c> more</c><00:02:09.879><c> quickly</c><00:02:10.160><c> and</c>

00:02:10.470 --> 00:02:10.480 align:start position:0%
that reviews can happen more quickly and
 

00:02:10.480 --> 00:02:12.150 align:start position:0%
that reviews can happen more quickly and
effectively<00:02:11.480><c> there</c><00:02:11.599><c> are</c><00:02:11.680><c> a</c><00:02:11.760><c> lot</c><00:02:11.879><c> of</c><00:02:12.000><c> other</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
effectively there are a lot of other
 

00:02:12.160 --> 00:02:13.949 align:start position:0%
effectively there are a lot of other
features<00:02:12.599><c> too</c><00:02:13.200><c> um</c><00:02:13.360><c> I've</c><00:02:13.480><c> got</c><00:02:13.599><c> links</c><00:02:13.800><c> in</c><00:02:13.879><c> the</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
features too um I've got links in the
 

00:02:13.959 --> 00:02:15.550 align:start position:0%
features too um I've got links in the
show<00:02:14.120><c> notes</c><00:02:14.319><c> in</c><00:02:14.480><c> description</c><00:02:14.959><c> to</c><00:02:15.160><c> the</c><00:02:15.280><c> blog</c>

00:02:15.550 --> 00:02:15.560 align:start position:0%
show notes in description to the blog
 

00:02:15.560 --> 00:02:17.550 align:start position:0%
show notes in description to the blog
post<00:02:16.000><c> the</c><00:02:16.120><c> docs</c><00:02:16.560><c> and</c><00:02:16.680><c> a</c><00:02:16.800><c> great</c><00:02:17.040><c> video</c><00:02:17.280><c> that</c><00:02:17.400><c> my</c>

00:02:17.550 --> 00:02:17.560 align:start position:0%
post the docs and a great video that my
 

00:02:17.560 --> 00:02:19.910 align:start position:0%
post the docs and a great video that my
pal<00:02:17.800><c> Christopher</c><00:02:18.239><c> made</c><00:02:18.879><c> showing</c><00:02:19.280><c> off</c><00:02:19.599><c> GitHub</c>

00:02:19.910 --> 00:02:19.920 align:start position:0%
pal Christopher made showing off GitHub
 

00:02:19.920 --> 00:02:21.949 align:start position:0%
pal Christopher made showing off GitHub
co-pilot<00:02:20.319><c> for</c><00:02:20.440><c> Enterprise</c><00:02:21.239><c> so</c><00:02:21.480><c> check</c><00:02:21.720><c> all</c>

00:02:21.949 --> 00:02:21.959 align:start position:0%
co-pilot for Enterprise so check all
 

00:02:21.959 --> 00:02:24.949 align:start position:0%
co-pilot for Enterprise so check all
that<00:02:22.120><c> out</c><00:02:22.400><c> down</c><00:02:22.640><c> below</c><00:02:23.640><c> and</c><00:02:24.080><c> speaking</c><00:02:24.440><c> of</c><00:02:24.640><c> fine</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
that out down below and speaking of fine
 

00:02:24.959 --> 00:02:27.670 align:start position:0%
that out down below and speaking of fine
tuning<00:02:25.360><c> and</c><00:02:25.560><c> customization</c><00:02:26.560><c> Nicole</c><00:02:27.000><c> Choy</c><00:02:27.560><c> uh</c>

00:02:27.670 --> 00:02:27.680 align:start position:0%
tuning and customization Nicole Choy uh
 

00:02:27.680 --> 00:02:29.830 align:start position:0%
tuning and customization Nicole Choy uh
wrote<00:02:27.920><c> a</c><00:02:28.160><c> great</c><00:02:28.640><c> blog</c><00:02:29.000><c> post</c><00:02:29.239><c> on</c><00:02:29.360><c> the</c><00:02:29.480><c> GitHub</c>

00:02:29.830 --> 00:02:29.840 align:start position:0%
wrote a great blog post on the GitHub
 

00:02:29.840 --> 00:02:31.470 align:start position:0%
wrote a great blog post on the GitHub
blog<00:02:30.360><c> that</c><00:02:30.519><c> covers</c><00:02:30.800><c> a</c><00:02:30.920><c> lot</c><00:02:31.000><c> of</c><00:02:31.120><c> the</c><00:02:31.239><c> best</c>

00:02:31.470 --> 00:02:31.480 align:start position:0%
blog that covers a lot of the best
 

00:02:31.480 --> 00:02:33.710 align:start position:0%
blog that covers a lot of the best
practices<00:02:32.080><c> around</c><00:02:32.400><c> fine-tuning</c><00:02:33.040><c> llms</c><00:02:33.599><c> for</c>

00:02:33.710 --> 00:02:33.720 align:start position:0%
practices around fine-tuning llms for
 

00:02:33.720 --> 00:02:36.430 align:start position:0%
practices around fine-tuning llms for
your<00:02:33.840><c> own</c><00:02:34.040><c> needs</c><00:02:34.800><c> including</c><00:02:35.560><c> um</c><00:02:35.920><c> what</c><00:02:36.040><c> we</c><00:02:36.200><c> do</c>

00:02:36.430 --> 00:02:36.440 align:start position:0%
your own needs including um what we do
 

00:02:36.440 --> 00:02:37.790 align:start position:0%
your own needs including um what we do
with<00:02:36.599><c> with</c><00:02:36.800><c> GitHub</c><00:02:37.200><c> a</c><00:02:37.319><c> co-pilot</c><00:02:37.680><c> for</c>

00:02:37.790 --> 00:02:37.800 align:start position:0%
with with GitHub a co-pilot for
 

00:02:37.800 --> 00:02:39.190 align:start position:0%
with with GitHub a co-pilot for
Enterprise<00:02:38.280><c> and</c><00:02:38.400><c> so</c><00:02:38.560><c> I've</c><00:02:38.680><c> got</c><00:02:38.879><c> that</c><00:02:39.000><c> link</c>

00:02:39.190 --> 00:02:39.200 align:start position:0%
Enterprise and so I've got that link
 

00:02:39.200 --> 00:02:41.630 align:start position:0%
Enterprise and so I've got that link
down<00:02:39.360><c> below</c><00:02:39.599><c> too</c><00:02:40.599><c> while</c><00:02:40.720><c> we're</c><00:02:40.879><c> talking</c><00:02:41.159><c> AI</c>

00:02:41.630 --> 00:02:41.640 align:start position:0%
down below too while we're talking AI
 

00:02:41.640 --> 00:02:42.790 align:start position:0%
down below too while we're talking AI
let's<00:02:41.920><c> talk</c><00:02:42.159><c> about</c><00:02:42.400><c> some</c><00:02:42.519><c> of</c><00:02:42.680><c> the</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
let's talk about some of the
 

00:02:42.800 --> 00:02:44.149 align:start position:0%
let's talk about some of the
announcements<00:02:43.400><c> that</c><00:02:43.519><c> have</c><00:02:43.640><c> happened</c><00:02:43.920><c> in</c><00:02:44.000><c> the</c>

00:02:44.149 --> 00:02:44.159 align:start position:0%
announcements that have happened in the
 

00:02:44.159 --> 00:02:46.790 align:start position:0%
announcements that have happened in the
space<00:02:44.519><c> over</c><00:02:44.680><c> the</c><00:02:44.879><c> last</c><00:02:45.120><c> week</c><00:02:45.599><c> so</c><00:02:45.959><c> first</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
space over the last week so first
 

00:02:46.800 --> 00:02:48.630 align:start position:0%
space over the last week so first
stability<00:02:47.319><c> AI</c><00:02:47.800><c> announced</c><00:02:48.200><c> that</c><00:02:48.280><c> stable</c>

00:02:48.630 --> 00:02:48.640 align:start position:0%
stability AI announced that stable
 

00:02:48.640 --> 00:02:51.229 align:start position:0%
stability AI announced that stable
diffusion<00:02:49.080><c> 3</c><00:02:49.519><c> will</c><00:02:49.680><c> be</c><00:02:49.840><c> coming</c><00:02:50.040><c> out</c><00:02:50.319><c> soon</c><00:02:51.040><c> it's</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
diffusion 3 will be coming out soon it's
 

00:02:51.239 --> 00:02:53.509 align:start position:0%
diffusion 3 will be coming out soon it's
being<00:02:51.560><c> tested</c><00:02:52.000><c> in</c><00:02:52.159><c> Early</c><00:02:52.519><c> Access</c><00:02:52.840><c> for</c><00:02:53.000><c> now</c><00:02:53.360><c> but</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
being tested in Early Access for now but
 

00:02:53.519 --> 00:02:55.509 align:start position:0%
being tested in Early Access for now but
stability<00:02:54.040><c> AI</c><00:02:54.400><c> says</c><00:02:54.680><c> that</c><00:02:54.840><c> it</c><00:02:54.959><c> is</c><00:02:55.159><c> committed</c>

00:02:55.509 --> 00:02:55.519 align:start position:0%
stability AI says that it is committed
 

00:02:55.519 --> 00:02:58.229 align:start position:0%
stability AI says that it is committed
to<00:02:55.680><c> the</c><00:02:55.800><c> open</c><00:02:56.080><c> model</c><00:02:56.959><c> and</c><00:02:57.280><c> similar</c><00:02:57.560><c> to</c><00:02:57.720><c> open</c><00:02:57.920><c> AI</c>

00:02:58.229 --> 00:02:58.239 align:start position:0%
to the open model and similar to open AI
 

00:02:58.239 --> 00:03:01.350 align:start position:0%
to the open model and similar to open AI
Sora<00:02:59.080><c> um</c><00:02:59.239><c> imod</c><00:02:59.599><c> who</c><00:02:59.840><c> stability</c><00:03:00.239><c> ai's</c><00:03:00.640><c> CEO</c><00:03:01.159><c> says</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
Sora um imod who stability ai's CEO says
 

00:03:01.360 --> 00:03:03.869 align:start position:0%
Sora um imod who stability ai's CEO says
that<00:03:01.519><c> stable</c><00:03:01.840><c> diffusion</c><00:03:02.239><c> 3</c><00:03:02.959><c> uses</c><00:03:03.319><c> a</c><00:03:03.480><c> new</c><00:03:03.720><c> type</c>

00:03:03.869 --> 00:03:03.879 align:start position:0%
that stable diffusion 3 uses a new type
 

00:03:03.879 --> 00:03:06.550 align:start position:0%
that stable diffusion 3 uses a new type
of<00:03:04.120><c> diffusion</c><00:03:04.920><c> Transformer</c><00:03:05.920><c> and</c><00:03:06.040><c> the</c><00:03:06.200><c> results</c>

00:03:06.550 --> 00:03:06.560 align:start position:0%
of diffusion Transformer and the results
 

00:03:06.560 --> 00:03:08.309 align:start position:0%
of diffusion Transformer and the results
that<00:03:06.640><c> are</c><00:03:06.799><c> being</c><00:03:06.959><c> shown</c><00:03:07.280><c> off</c><00:03:07.519><c> and</c><00:03:07.760><c> and</c><00:03:07.920><c> look</c><00:03:08.159><c> as</c>

00:03:08.309 --> 00:03:08.319 align:start position:0%
that are being shown off and and look as
 

00:03:08.319 --> 00:03:09.470 align:start position:0%
that are being shown off and and look as
always<00:03:08.640><c> these</c><00:03:08.760><c> are</c><00:03:08.879><c> going</c><00:03:09.000><c> to</c><00:03:09.080><c> be</c><00:03:09.239><c> very</c>

00:03:09.470 --> 00:03:09.480 align:start position:0%
always these are going to be very
 

00:03:09.480 --> 00:03:10.949 align:start position:0%
always these are going to be very
cherry-picked<00:03:10.200><c> to</c><00:03:10.360><c> look</c><00:03:10.519><c> as</c><00:03:10.640><c> good</c><00:03:10.760><c> as</c>

00:03:10.949 --> 00:03:10.959 align:start position:0%
cherry-picked to look as good as
 

00:03:10.959 --> 00:03:12.949 align:start position:0%
cherry-picked to look as good as
possible<00:03:11.680><c> but</c><00:03:11.920><c> these</c><00:03:12.080><c> results</c><00:03:12.440><c> do</c><00:03:12.680><c> look</c>

00:03:12.949 --> 00:03:12.959 align:start position:0%
possible but these results do look
 

00:03:12.959 --> 00:03:15.270 align:start position:0%
possible but these results do look
really<00:03:13.200><c> really</c><00:03:13.640><c> good</c><00:03:14.640><c> and</c><00:03:14.760><c> we'll</c><00:03:14.920><c> be</c><00:03:15.040><c> talking</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
really really good and we'll be talking
 

00:03:15.280 --> 00:03:16.949 align:start position:0%
really really good and we'll be talking
more<00:03:15.480><c> about</c><00:03:15.720><c> stable</c><00:03:15.959><c> diffusion</c><00:03:16.319><c> 3</c><00:03:16.720><c> and</c><00:03:16.840><c> the</c>

00:03:16.949 --> 00:03:16.959 align:start position:0%
more about stable diffusion 3 and the
 

00:03:16.959 --> 00:03:18.869 align:start position:0%
more about stable diffusion 3 and the
various<00:03:17.239><c> tools</c><00:03:17.480><c> and</c><00:03:17.640><c> front</c><00:03:17.840><c> ends</c><00:03:18.440><c> for</c><00:03:18.720><c> that</c>

00:03:18.869 --> 00:03:18.879 align:start position:0%
various tools and front ends for that
 

00:03:18.879 --> 00:03:20.390 align:start position:0%
various tools and front ends for that
that<00:03:19.000><c> the</c><00:03:19.080><c> community</c><00:03:19.480><c> develops</c><00:03:20.040><c> once</c><00:03:20.239><c> it</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
that the community develops once it
 

00:03:20.400 --> 00:03:22.830 align:start position:0%
that the community develops once it
comes<00:03:20.640><c> out</c><00:03:21.440><c> but</c><00:03:21.640><c> I've</c><00:03:21.799><c> got</c><00:03:21.920><c> the</c><00:03:22.040><c> stability</c><00:03:22.480><c> AI</c>

00:03:22.830 --> 00:03:22.840 align:start position:0%
comes out but I've got the stability AI
 

00:03:22.840 --> 00:03:24.589 align:start position:0%
comes out but I've got the stability AI
blog<00:03:23.120><c> post</c><00:03:23.319><c> linked</c><00:03:23.560><c> down</c><00:03:23.760><c> below</c><00:03:24.200><c> as</c><00:03:24.319><c> well</c><00:03:24.480><c> as</c>

00:03:24.589 --> 00:03:24.599 align:start position:0%
blog post linked down below as well as
 

00:03:24.599 --> 00:03:26.789 align:start position:0%
blog post linked down below as well as
some<00:03:24.760><c> other</c><00:03:24.920><c> statements</c><00:03:25.360><c> that</c><00:03:25.519><c> imod</c><00:03:25.920><c> has</c><00:03:26.080><c> made</c>

00:03:26.789 --> 00:03:26.799 align:start position:0%
some other statements that imod has made
 

00:03:26.799 --> 00:03:27.990 align:start position:0%
some other statements that imod has made
um<00:03:27.000><c> on</c><00:03:27.200><c> on</c><00:03:27.319><c> on</c>

00:03:27.990 --> 00:03:28.000 align:start position:0%
um on on on
 

00:03:28.000 --> 00:03:30.550 align:start position:0%
um on on on
Twitter<00:03:29.000><c> uh</c><00:03:29.120><c> and</c><00:03:29.239><c> speaking</c><00:03:29.480><c> of</c><00:03:29.760><c> open</c><00:03:29.959><c> models</c>

00:03:30.550 --> 00:03:30.560 align:start position:0%
Twitter uh and speaking of open models
 

00:03:30.560 --> 00:03:32.589 align:start position:0%
Twitter uh and speaking of open models
Google<00:03:30.879><c> announced</c><00:03:31.360><c> Gemma</c><00:03:31.879><c> which</c><00:03:32.040><c> is</c><00:03:32.319><c> its</c>

00:03:32.589 --> 00:03:32.599 align:start position:0%
Google announced Gemma which is its
 

00:03:32.599 --> 00:03:34.149 align:start position:0%
Google announced Gemma which is its
family<00:03:32.879><c> of</c><00:03:33.000><c> lightweight</c><00:03:33.439><c> state-of-the-art</c>

00:03:34.149 --> 00:03:34.159 align:start position:0%
family of lightweight state-of-the-art
 

00:03:34.159 --> 00:03:36.470 align:start position:0%
family of lightweight state-of-the-art
open<00:03:34.480><c> models</c><00:03:35.000><c> that</c><00:03:35.080><c> it</c><00:03:35.200><c> says</c><00:03:35.439><c> were</c><00:03:35.720><c> built</c><00:03:36.319><c> from</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
open models that it says were built from
 

00:03:36.480 --> 00:03:38.429 align:start position:0%
open models that it says were built from
the<00:03:36.640><c> same</c><00:03:36.840><c> research</c><00:03:37.200><c> and</c><00:03:37.319><c> technology</c><00:03:38.080><c> that</c><00:03:38.319><c> it</c>

00:03:38.429 --> 00:03:38.439 align:start position:0%
the same research and technology that it
 

00:03:38.439 --> 00:03:40.750 align:start position:0%
the same research and technology that it
used<00:03:38.760><c> to</c><00:03:38.959><c> create</c><00:03:39.480><c> um</c><00:03:39.640><c> its</c><00:03:39.879><c> commercial</c><00:03:40.319><c> Gemini</c>

00:03:40.750 --> 00:03:40.760 align:start position:0%
used to create um its commercial Gemini
 

00:03:40.760 --> 00:03:43.149 align:start position:0%
used to create um its commercial Gemini
models<00:03:41.599><c> and</c><00:03:41.760><c> similar</c><00:03:42.080><c> to</c><00:03:42.200><c> meta's</c><00:03:42.760><c> llama</c>

00:03:43.149 --> 00:03:43.159 align:start position:0%
models and similar to meta's llama
 

00:03:43.159 --> 00:03:45.910 align:start position:0%
models and similar to meta's llama
models<00:03:43.720><c> these</c><00:03:43.920><c> aren't</c><00:03:44.280><c> strictly</c><00:03:44.760><c> open</c><00:03:45.120><c> source</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
models these aren't strictly open source
 

00:03:45.920 --> 00:03:47.470 align:start position:0%
models these aren't strictly open source
um<00:03:46.040><c> but</c><00:03:46.159><c> Google</c><00:03:46.400><c> notes</c><00:03:46.720><c> that</c><00:03:46.879><c> the</c><00:03:46.959><c> terms</c><00:03:47.360><c> of</c>

00:03:47.470 --> 00:03:47.480 align:start position:0%
um but Google notes that the terms of
 

00:03:47.480 --> 00:03:49.350 align:start position:0%
um but Google notes that the terms of
use<00:03:47.760><c> permit</c><00:03:48.120><c> responsible</c><00:03:48.560><c> commercial</c><00:03:49.000><c> usage</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
use permit responsible commercial usage
 

00:03:49.360 --> 00:03:51.589 align:start position:0%
use permit responsible commercial usage
and<00:03:49.519><c> distribution</c><00:03:50.400><c> for</c><00:03:50.640><c> all</c><00:03:50.840><c> organizations</c>

00:03:51.589 --> 00:03:51.599 align:start position:0%
and distribution for all organizations
 

00:03:51.599 --> 00:03:53.390 align:start position:0%
and distribution for all organizations
regardless<00:03:52.000><c> of</c><00:03:52.159><c> size</c><00:03:52.400><c> so</c><00:03:52.519><c> that's</c><00:03:52.680><c> really</c><00:03:52.879><c> cool</c>

00:03:53.390 --> 00:03:53.400 align:start position:0%
regardless of size so that's really cool
 

00:03:53.400 --> 00:03:55.149 align:start position:0%
regardless of size so that's really cool
so<00:03:53.560><c> the</c><00:03:53.720><c> models</c><00:03:54.000><c> are</c><00:03:54.239><c> available</c><00:03:54.640><c> to</c><00:03:55.040><c> um</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
so the models are available to um
 

00:03:55.159 --> 00:03:57.030 align:start position:0%
so the models are available to um
download<00:03:55.560><c> directly</c><00:03:56.079><c> or</c><00:03:56.280><c> you</c><00:03:56.360><c> can</c><00:03:56.519><c> use</c><00:03:56.680><c> them</c><00:03:56.840><c> on</c>

00:03:57.030 --> 00:03:57.040 align:start position:0%
download directly or you can use them on
 

00:03:57.040 --> 00:03:59.309 align:start position:0%
download directly or you can use them on
kagle<00:03:57.400><c> or</c><00:03:57.519><c> hugging</c><00:03:57.920><c> face</c><00:03:58.560><c> but</c><00:03:58.720><c> the</c><00:03:58.879><c> thing</c><00:03:59.200><c> that</c>

00:03:59.309 --> 00:03:59.319 align:start position:0%
kagle or hugging face but the thing that
 

00:03:59.319 --> 00:04:01.589 align:start position:0%
kagle or hugging face but the thing that
might<00:03:59.680><c> appeal</c><00:03:59.920><c> to</c><00:04:00.040><c> a</c><00:04:00.120><c> lot</c><00:04:00.280><c> of</c><00:04:00.400><c> AI</c><00:04:00.720><c> devs</c><00:04:01.239><c> is</c><00:04:01.439><c> the</c>

00:04:01.589 --> 00:04:01.599 align:start position:0%
might appeal to a lot of AI devs is the
 

00:04:01.599 --> 00:04:05.030 align:start position:0%
might appeal to a lot of AI devs is the
gma.<00:04:02.200><c> CPP</c><00:04:03.200><c> uh</c><00:04:03.319><c> lightweight</c><00:04:03.720><c> Standalone</c><00:04:04.239><c> C++</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
gma. CPP uh lightweight Standalone C++
 

00:04:05.040 --> 00:04:07.470 align:start position:0%
gma. CPP uh lightweight Standalone C++
inference<00:04:05.519><c> engine</c><00:04:05.920><c> for</c><00:04:06.159><c> gimma</c><00:04:06.879><c> that</c><00:04:07.040><c> Google</c>

00:04:07.470 --> 00:04:07.480 align:start position:0%
inference engine for gimma that Google
 

00:04:07.480 --> 00:04:10.350 align:start position:0%
inference engine for gimma that Google
has<00:04:07.720><c> released</c><00:04:08.000><c> on</c><00:04:08.159><c> GitHub</c><00:04:08.760><c> and</c><00:04:08.920><c> jim.</c><00:04:09.439><c> CPP</c><00:04:10.079><c> was</c>

00:04:10.350 --> 00:04:10.360 align:start position:0%
has released on GitHub and jim. CPP was
 

00:04:10.360 --> 00:04:14.869 align:start position:0%
has released on GitHub and jim. CPP was
inspired<00:04:11.000><c> by</c><00:04:11.280><c> tools</c><00:04:11.599><c> like</c>

00:04:14.869 --> 00:04:14.879 align:start position:0%
 
 

00:04:14.879 --> 00:04:18.710 align:start position:0%
 
llama.com<00:04:15.879><c> llama</c><00:04:16.720><c> models</c><00:04:17.720><c> and</c><00:04:18.120><c> um</c><00:04:18.320><c> that</c><00:04:18.519><c> has</c>

00:04:18.710 --> 00:04:18.720 align:start position:0%
llama.com llama models and um that has
 

00:04:18.720 --> 00:04:20.670 align:start position:0%
llama.com llama models and um that has
been<00:04:18.959><c> implemented</c><00:04:19.519><c> in</c><00:04:19.759><c> dozens</c><00:04:20.079><c> of</c><00:04:20.199><c> tools</c><00:04:20.560><c> and</c>

00:04:20.670 --> 00:04:20.680 align:start position:0%
been implemented in dozens of tools and
 

00:04:20.680 --> 00:04:22.030 align:start position:0%
been implemented in dozens of tools and
so<00:04:20.840><c> it</c><00:04:20.880><c> was</c><00:04:21.040><c> really</c><00:04:21.239><c> cool</c><00:04:21.440><c> to</c><00:04:21.560><c> see</c><00:04:21.759><c> something</c>

00:04:22.030 --> 00:04:22.040 align:start position:0%
so it was really cool to see something
 

00:04:22.040 --> 00:04:24.150 align:start position:0%
so it was really cool to see something
similar<00:04:22.360><c> to</c><00:04:22.520><c> this</c><00:04:22.680><c> coming</c><00:04:22.960><c> first</c><00:04:23.280><c> party</c><00:04:23.759><c> from</c>

00:04:24.150 --> 00:04:24.160 align:start position:0%
similar to this coming first party from
 

00:04:24.160 --> 00:04:26.469 align:start position:0%
similar to this coming first party from
Google<00:04:24.680><c> I</c><00:04:24.880><c> really</c><00:04:25.080><c> really</c><00:04:25.320><c> like</c><00:04:25.600><c> that</c><00:04:26.160><c> and</c><00:04:26.320><c> I</c>

00:04:26.469 --> 00:04:26.479 align:start position:0%
Google I really really like that and I
 

00:04:26.479 --> 00:04:28.950 align:start position:0%
Google I really really like that and I
expect<00:04:26.880><c> tooling</c><00:04:27.360><c> like</c><00:04:27.560><c> AMA</c><00:04:28.240><c> and</c><00:04:28.440><c> and</c><00:04:28.600><c> others</c>

00:04:28.950 --> 00:04:28.960 align:start position:0%
expect tooling like AMA and and others
 

00:04:28.960 --> 00:04:31.790 align:start position:0%
expect tooling like AMA and and others
to<00:04:29.160><c> incorporate</c><00:04:29.680><c> gma.</c><00:04:30.120><c> CPP</c><00:04:31.000><c> in</c><00:04:31.120><c> the</c><00:04:31.280><c> future</c>

00:04:31.790 --> 00:04:31.800 align:start position:0%
to incorporate gma. CPP in the future
 

00:04:31.800 --> 00:04:33.710 align:start position:0%
to incorporate gma. CPP in the future
which<00:04:32.039><c> I</c><00:04:32.120><c> think</c><00:04:32.280><c> is</c><00:04:32.479><c> fantastic</c><00:04:33.479><c> I've</c><00:04:33.600><c> got</c>

00:04:33.710 --> 00:04:33.720 align:start position:0%
which I think is fantastic I've got
 

00:04:33.720 --> 00:04:35.950 align:start position:0%
which I think is fantastic I've got
links<00:04:33.960><c> to</c><00:04:34.080><c> Google's</c><00:04:34.479><c> blog</c><00:04:35.080><c> um</c><00:04:35.240><c> about</c><00:04:35.440><c> Gemma</c><00:04:35.880><c> as</c>

00:04:35.950 --> 00:04:35.960 align:start position:0%
links to Google's blog um about Gemma as
 

00:04:35.960 --> 00:04:38.830 align:start position:0%
links to Google's blog um about Gemma as
well<00:04:36.120><c> as</c><00:04:36.240><c> gma.</c><00:04:36.800><c> cpp's</c><00:04:37.360><c> repo</c><00:04:37.800><c> down</c><00:04:38.000><c> below</c><00:04:38.639><c> and</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
well as gma. cpp's repo down below and
 

00:04:38.840 --> 00:04:40.550 align:start position:0%
well as gma. cpp's repo down below and
honestly<00:04:39.240><c> I</c><00:04:39.320><c> feel</c><00:04:39.520><c> like</c><00:04:39.720><c> the</c><00:04:39.800><c> more</c><00:04:40.120><c> open</c><00:04:40.360><c> or</c>

00:04:40.550 --> 00:04:40.560 align:start position:0%
honestly I feel like the more open or
 

00:04:40.560 --> 00:04:42.310 align:start position:0%
honestly I feel like the more open or
open<00:04:40.840><c> source</c><00:04:41.080><c> models</c><00:04:41.520><c> that</c><00:04:41.680><c> we</c><00:04:41.880><c> have</c><00:04:42.160><c> the</c>

00:04:42.310 --> 00:04:42.320 align:start position:0%
open source models that we have the
 

00:04:42.320 --> 00:04:44.510 align:start position:0%
open source models that we have the
better<00:04:42.759><c> so</c><00:04:42.919><c> I</c><00:04:43.080><c> really</c><00:04:43.280><c> like</c><00:04:43.479><c> seeing</c><00:04:43.840><c> this</c><00:04:44.039><c> so</c>

00:04:44.510 --> 00:04:44.520 align:start position:0%
better so I really like seeing this so
 

00:04:44.520 --> 00:04:47.629 align:start position:0%
better so I really like seeing this so
Kos<00:04:44.880><c> goodle</c><00:04:45.759><c> speaking</c><00:04:46.039><c> of</c><00:04:46.199><c> C++</c><00:04:47.199><c> which</c><00:04:47.360><c> is</c><00:04:47.520><c> what</c>

00:04:47.629 --> 00:04:47.639 align:start position:0%
Kos goodle speaking of C++ which is what
 

00:04:47.639 --> 00:04:50.390 align:start position:0%
Kos goodle speaking of C++ which is what
Jimma<00:04:48.000><c> CPP</c><00:04:48.520><c> uses</c><00:04:49.520><c> the</c><00:04:49.639><c> United</c><00:04:49.919><c> States</c><00:04:50.160><c> White</c>

00:04:50.390 --> 00:04:50.400 align:start position:0%
Jimma CPP uses the United States White
 

00:04:50.400 --> 00:04:51.790 align:start position:0%
Jimma CPP uses the United States White
House<00:04:50.520><c> wants</c><00:04:50.800><c> developers</c><00:04:51.400><c> or</c><00:04:51.560><c> at</c><00:04:51.639><c> least</c>

00:04:51.790 --> 00:04:51.800 align:start position:0%
House wants developers or at least
 

00:04:51.800 --> 00:04:53.629 align:start position:0%
House wants developers or at least
developers<00:04:52.199><c> of</c><00:04:52.360><c> government</c><00:04:52.720><c> apps</c><00:04:53.080><c> to</c><00:04:53.280><c> move</c>

00:04:53.629 --> 00:04:53.639 align:start position:0%
developers of government apps to move
 

00:04:53.639 --> 00:04:56.590 align:start position:0%
developers of government apps to move
away<00:04:53.919><c> from</c><00:04:54.080><c> C</c><00:04:54.360><c> and</c><00:04:54.560><c> C++</c><00:04:55.560><c> and</c><00:04:55.720><c> embrace</c><00:04:56.199><c> memory</c>

00:04:56.590 --> 00:04:56.600 align:start position:0%
away from C and C++ and embrace memory
 

00:04:56.600 --> 00:04:58.350 align:start position:0%
away from C and C++ and embrace memory
safe<00:04:56.800><c> programming</c><00:04:57.199><c> languages</c><00:04:57.680><c> like</c><00:04:57.840><c> rust</c><00:04:58.160><c> or</c>

00:04:58.350 --> 00:04:58.360 align:start position:0%
safe programming languages like rust or
 

00:04:58.360 --> 00:05:00.070 align:start position:0%
safe programming languages like rust or
go<00:04:58.600><c> or</c><00:04:58.800><c> Java</c><00:04:59.160><c> instead</c>

00:05:00.070 --> 00:05:00.080 align:start position:0%
go or Java instead
 

00:05:00.080 --> 00:05:01.590 align:start position:0%
go or Java instead
there's<00:05:00.240><c> a</c><00:05:00.360><c> whole</c><00:05:00.520><c> government</c><00:05:00.919><c> report</c><00:05:01.360><c> about</c>

00:05:01.590 --> 00:05:01.600 align:start position:0%
there's a whole government report about
 

00:05:01.600 --> 00:05:03.029 align:start position:0%
there's a whole government report about
this<00:05:01.759><c> too</c><00:05:02.000><c> it's</c><00:05:02.120><c> from</c><00:05:02.280><c> the</c><00:05:02.440><c> White</c><00:05:02.720><c> House</c>

00:05:03.029 --> 00:05:03.039 align:start position:0%
this too it's from the White House
 

00:05:03.039 --> 00:05:05.590 align:start position:0%
this too it's from the White House
Office<00:05:03.600><c> of</c><00:05:03.759><c> the</c><00:05:03.919><c> national</c><00:05:04.280><c> cyber</c><00:05:04.639><c> director</c>

00:05:05.590 --> 00:05:05.600 align:start position:0%
Office of the national cyber director
 

00:05:05.600 --> 00:05:07.070 align:start position:0%
Office of the national cyber director
and<00:05:05.759><c> if</c><00:05:05.880><c> you've</c><00:05:06.160><c> you've</c><00:05:06.360><c> heard</c><00:05:06.560><c> about</c><00:05:06.840><c> this</c><00:05:07.000><c> if</c>

00:05:07.070 --> 00:05:07.080 align:start position:0%
and if you've you've heard about this if
 

00:05:07.080 --> 00:05:08.270 align:start position:0%
and if you've you've heard about this if
you've<00:05:07.240><c> heard</c><00:05:07.400><c> about</c><00:05:07.520><c> this</c><00:05:07.680><c> report</c><00:05:08.000><c> it's</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
you've heard about this report it's
 

00:05:08.280 --> 00:05:09.909 align:start position:0%
you've heard about this report it's
probably<00:05:08.720><c> because</c><00:05:09.280><c> your</c><00:05:09.479><c> friends</c><00:05:09.759><c> who</c>

00:05:09.909 --> 00:05:09.919 align:start position:0%
probably because your friends who
 

00:05:09.919 --> 00:05:12.070 align:start position:0%
probably because your friends who
develop<00:05:10.240><c> in</c><00:05:10.440><c> Rust</c><00:05:11.080><c> and</c><00:05:11.280><c> insist</c><00:05:11.639><c> on</c><00:05:11.840><c> letting</c>

00:05:12.070 --> 00:05:12.080 align:start position:0%
develop in Rust and insist on letting
 

00:05:12.080 --> 00:05:13.830 align:start position:0%
develop in Rust and insist on letting
you<00:05:12.199><c> know</c><00:05:12.360><c> that</c><00:05:12.479><c> they</c><00:05:12.600><c> use</c><00:05:12.840><c> rust</c><00:05:13.479><c> have</c><00:05:13.639><c> sent</c>

00:05:13.830 --> 00:05:13.840 align:start position:0%
you know that they use rust have sent
 

00:05:13.840 --> 00:05:15.670 align:start position:0%
you know that they use rust have sent
you<00:05:13.960><c> the</c><00:05:14.080><c> link</c><00:05:14.400><c> 500</c><00:05:14.800><c> times</c><00:05:15.039><c> over</c><00:05:15.240><c> the</c><00:05:15.320><c> last</c><00:05:15.520><c> few</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
you the link 500 times over the last few
 

00:05:15.680 --> 00:05:17.270 align:start position:0%
you the link 500 times over the last few
days<00:05:15.960><c> at</c><00:05:16.039><c> least</c><00:05:16.360><c> like</c><00:05:16.520><c> that's</c><00:05:16.680><c> how</c><00:05:16.919><c> I've</c><00:05:17.160><c> I've</c>

00:05:17.270 --> 00:05:17.280 align:start position:0%
days at least like that's how I've I've
 

00:05:17.280 --> 00:05:19.150 align:start position:0%
days at least like that's how I've I've
heard<00:05:17.479><c> about</c><00:05:17.639><c> it</c><00:05:18.319><c> look</c><00:05:18.520><c> I</c><00:05:18.600><c> don't</c><00:05:18.759><c> have</c><00:05:18.880><c> any</c>

00:05:19.150 --> 00:05:19.160 align:start position:0%
heard about it look I don't have any
 

00:05:19.160 --> 00:05:20.830 align:start position:0%
heard about it look I don't have any
strong<00:05:19.319><c> opinions</c><00:05:19.800><c> about</c><00:05:20.080><c> whether</c><00:05:20.319><c> businesses</c>

00:05:20.830 --> 00:05:20.840 align:start position:0%
strong opinions about whether businesses
 

00:05:20.840 --> 00:05:23.710 align:start position:0%
strong opinions about whether businesses
should<00:05:21.039><c> or</c><00:05:21.160><c> should</c><00:05:21.400><c> not</c><00:05:21.639><c> use</c><00:05:21.960><c> C</c><00:05:22.199><c> or</c><00:05:22.639><c> C++</c><00:05:23.639><c> I</c>

00:05:23.710 --> 00:05:23.720 align:start position:0%
should or should not use C or C++ I
 

00:05:23.720 --> 00:05:25.270 align:start position:0%
should or should not use C or C++ I
think<00:05:23.800><c> for</c><00:05:23.919><c> newer</c><00:05:24.319><c> projects</c><00:05:24.840><c> it</c><00:05:25.039><c> probably</c>

00:05:25.270 --> 00:05:25.280 align:start position:0%
think for newer projects it probably
 

00:05:25.280 --> 00:05:26.790 align:start position:0%
think for newer projects it probably
makes<00:05:25.560><c> sense</c><00:05:25.800><c> to</c><00:05:25.919><c> use</c><00:05:26.160><c> something</c><00:05:26.600><c> you</c><00:05:26.680><c> know</c>

00:05:26.790 --> 00:05:26.800 align:start position:0%
makes sense to use something you know
 

00:05:26.800 --> 00:05:29.590 align:start position:0%
makes sense to use something you know
like<00:05:26.960><c> go</c><00:05:27.160><c> or</c><00:05:27.319><c> rust</c><00:05:27.639><c> or</c><00:05:27.840><c> or</c><00:05:28.000><c> even</c><00:05:28.199><c> C</c><00:05:28.680><c> or</c><00:05:28.840><c> Java</c>

00:05:29.590 --> 00:05:29.600 align:start position:0%
like go or rust or or even C or Java
 

00:05:29.600 --> 00:05:31.230 align:start position:0%
like go or rust or or even C or Java
just<00:05:29.720><c> to</c><00:05:29.880><c> get</c><00:05:30.240><c> more</c><00:05:30.440><c> modern</c><00:05:30.759><c> tooling</c><00:05:31.080><c> and</c>

00:05:31.230 --> 00:05:31.240 align:start position:0%
just to get more modern tooling and
 

00:05:31.240 --> 00:05:34.070 align:start position:0%
just to get more modern tooling and
Frameworks<00:05:32.240><c> but</c><00:05:33.160><c> I</c><00:05:33.319><c> personally</c><00:05:33.639><c> just</c><00:05:33.800><c> find</c><00:05:33.960><c> it</c>

00:05:34.070 --> 00:05:34.080 align:start position:0%
Frameworks but I personally just find it
 

00:05:34.080 --> 00:05:36.230 align:start position:0%
Frameworks but I personally just find it
very<00:05:34.280><c> funny</c><00:05:34.720><c> that</c><00:05:35.080><c> a</c><00:05:35.240><c> government</c><00:05:35.639><c> report</c><00:05:36.039><c> is</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
very funny that a government report is
 

00:05:36.240 --> 00:05:37.670 align:start position:0%
very funny that a government report is
out<00:05:36.560><c> helping</c><00:05:36.840><c> fan</c><00:05:37.080><c> the</c><00:05:37.199><c> Flames</c><00:05:37.520><c> with</c><00:05:37.600><c> the</c>

00:05:37.670 --> 00:05:37.680 align:start position:0%
out helping fan the Flames with the
 

00:05:37.680 --> 00:05:40.309 align:start position:0%
out helping fan the Flames with the
language<00:05:38.039><c> Wars</c><00:05:39.039><c> I</c><00:05:39.160><c> appreciate</c><00:05:39.639><c> that</c><00:05:40.039><c> anyway</c>

00:05:40.309 --> 00:05:40.319 align:start position:0%
language Wars I appreciate that anyway
 

00:05:40.319 --> 00:05:41.590 align:start position:0%
language Wars I appreciate that anyway
I've<00:05:40.400><c> got</c><00:05:40.520><c> a</c><00:05:40.600><c> link</c><00:05:40.800><c> to</c><00:05:40.960><c> that</c><00:05:41.120><c> report</c><00:05:41.400><c> down</c>

00:05:41.590 --> 00:05:41.600 align:start position:0%
I've got a link to that report down
 

00:05:41.600 --> 00:05:43.189 align:start position:0%
I've got a link to that report down
below<00:05:41.880><c> if</c><00:05:41.960><c> you</c><00:05:42.039><c> want</c><00:05:42.120><c> to</c><00:05:42.240><c> give</c><00:05:42.319><c> it</c><00:05:42.400><c> a</c><00:05:42.520><c> read</c><00:05:42.919><c> or</c>

00:05:43.189 --> 00:05:43.199 align:start position:0%
below if you want to give it a read or
 

00:05:43.199 --> 00:05:45.870 align:start position:0%
below if you want to give it a read or
you<00:05:43.280><c> know</c><00:05:43.440><c> use</c><00:05:43.600><c> it</c><00:05:43.759><c> to</c><00:05:44.520><c> um</c><00:05:45.120><c> antagonize</c><00:05:45.759><c> your</c>

00:05:45.870 --> 00:05:45.880 align:start position:0%
you know use it to um antagonize your
 

00:05:45.880 --> 00:05:48.430 align:start position:0%
you know use it to um antagonize your
non-rust<00:05:46.400><c> using</c><00:05:46.759><c> friends</c><00:05:47.759><c> and</c><00:05:47.880><c> now</c><00:05:48.039><c> it's</c><00:05:48.240><c> time</c>

00:05:48.430 --> 00:05:48.440 align:start position:0%
non-rust using friends and now it's time
 

00:05:48.440 --> 00:05:50.629 align:start position:0%
non-rust using friends and now it's time
for<00:05:48.759><c> both</c><00:05:48.919><c> my</c><00:05:49.120><c> GitHub</c><00:05:49.479><c> project</c><00:05:49.840><c> Spotlight</c><00:05:50.400><c> and</c>

00:05:50.629 --> 00:05:50.639 align:start position:0%
for both my GitHub project Spotlight and
 

00:05:50.639 --> 00:05:53.150 align:start position:0%
for both my GitHub project Spotlight and
pick<00:05:50.800><c> of</c><00:05:50.919><c> the</c><00:05:51.000><c> week</c><00:05:51.479><c> allinone</c><00:05:52.479><c> so</c><00:05:52.800><c> this</c><00:05:52.919><c> time</c><00:05:53.080><c> I</c>

00:05:53.150 --> 00:05:53.160 align:start position:0%
pick of the week allinone so this time I
 

00:05:53.160 --> 00:05:55.590 align:start position:0%
pick of the week allinone so this time I
want<00:05:53.280><c> to</c><00:05:53.520><c> highlight</c><00:05:53.919><c> The</c><00:05:54.039><c> Flipper</c><00:05:54.440><c> zero</c><00:05:55.000><c> which</c>

00:05:55.590 --> 00:05:55.600 align:start position:0%
want to highlight The Flipper zero which
 

00:05:55.600 --> 00:05:57.550 align:start position:0%
want to highlight The Flipper zero which
describes<00:05:56.039><c> itself</c><00:05:56.360><c> as</c><00:05:56.479><c> a</c><00:05:56.680><c> multi-tool</c><00:05:57.280><c> device</c>

00:05:57.550 --> 00:05:57.560 align:start position:0%
describes itself as a multi-tool device
 

00:05:57.560 --> 00:05:59.990 align:start position:0%
describes itself as a multi-tool device
for<00:05:57.759><c> geeks</c><00:05:58.280><c> and</c><00:05:58.520><c> I've</c><00:05:58.639><c> got</c><00:05:58.800><c> one</c><00:05:58.919><c> and</c><00:05:59.039><c> I</c><00:05:59.160><c> love</c><00:05:59.319><c> it</c>

00:05:59.990 --> 00:06:00.000 align:start position:0%
for geeks and I've got one and I love it
 

00:06:00.000 --> 00:06:02.029 align:start position:0%
for geeks and I've got one and I love it
and<00:06:00.240><c> actually</c><00:06:00.560><c> how</c><00:06:00.840><c> flipper</c><00:06:01.319><c> zero</c><00:06:01.560><c> describes</c>

00:06:02.029 --> 00:06:02.039 align:start position:0%
and actually how flipper zero describes
 

00:06:02.039 --> 00:06:03.870 align:start position:0%
and actually how flipper zero describes
the<00:06:02.199><c> device</c><00:06:02.440><c> on</c><00:06:02.560><c> their</c><00:06:02.759><c> site</c><00:06:03.120><c> is</c><00:06:03.360><c> that</c><00:06:03.600><c> it's</c><00:06:03.720><c> a</c>

00:06:03.870 --> 00:06:03.880 align:start position:0%
the device on their site is that it's a
 

00:06:03.880 --> 00:06:06.029 align:start position:0%
the device on their site is that it's a
portable<00:06:04.319><c> multitool</c><00:06:04.919><c> for</c><00:06:05.120><c> pin</c><00:06:05.360><c> testers</c><00:06:05.840><c> and</c>

00:06:06.029 --> 00:06:06.039 align:start position:0%
portable multitool for pin testers and
 

00:06:06.039 --> 00:06:08.670 align:start position:0%
portable multitool for pin testers and
Geeks<00:06:06.639><c> and</c><00:06:06.759><c> a</c><00:06:06.880><c> toy</c><00:06:07.160><c> like</c><00:06:07.319><c> body</c><00:06:08.160><c> uh</c><00:06:08.280><c> it</c><00:06:08.400><c> loves</c>

00:06:08.670 --> 00:06:08.680 align:start position:0%
Geeks and a toy like body uh it loves
 

00:06:08.680 --> 00:06:10.510 align:start position:0%
Geeks and a toy like body uh it loves
hacking<00:06:09.039><c> digital</c><00:06:09.440><c> stuff</c><00:06:09.800><c> such</c><00:06:09.960><c> as</c><00:06:10.120><c> radio</c>

00:06:10.510 --> 00:06:10.520 align:start position:0%
hacking digital stuff such as radio
 

00:06:10.520 --> 00:06:12.309 align:start position:0%
hacking digital stuff such as radio
protocols<00:06:11.280><c> Access</c><00:06:11.560><c> Control</c><00:06:11.919><c> Systems</c>

00:06:12.309 --> 00:06:12.319 align:start position:0%
protocols Access Control Systems
 

00:06:12.319 --> 00:06:14.110 align:start position:0%
protocols Access Control Systems
hardware<00:06:12.680><c> and</c><00:06:12.800><c> more</c><00:06:13.319><c> it's</c><00:06:13.479><c> fully</c><00:06:13.720><c> open</c><00:06:13.919><c> source</c>

00:06:14.110 --> 00:06:14.120 align:start position:0%
hardware and more it's fully open source
 

00:06:14.120 --> 00:06:15.670 align:start position:0%
hardware and more it's fully open source
and<00:06:14.240><c> customizable</c><00:06:14.880><c> and</c><00:06:14.960><c> you</c><00:06:15.039><c> can</c><00:06:15.160><c> extend</c><00:06:15.560><c> it</c>

00:06:15.670 --> 00:06:15.680 align:start position:0%
and customizable and you can extend it
 

00:06:15.680 --> 00:06:18.110 align:start position:0%
and customizable and you can extend it
to<00:06:16.080><c> in</c><00:06:16.240><c> whatever</c><00:06:16.520><c> way</c><00:06:16.680><c> you</c><00:06:16.840><c> want</c><00:06:17.599><c> like</c><00:06:17.759><c> I</c><00:06:17.840><c> said</c>

00:06:18.110 --> 00:06:18.120 align:start position:0%
to in whatever way you want like I said
 

00:06:18.120 --> 00:06:20.950 align:start position:0%
to in whatever way you want like I said
I<00:06:18.240><c> love</c><00:06:18.520><c> mine</c><00:06:19.400><c> but</c><00:06:19.759><c> what</c><00:06:19.919><c> is</c><00:06:20.120><c> even</c><00:06:20.360><c> cooler</c><00:06:20.840><c> is</c>

00:06:20.950 --> 00:06:20.960 align:start position:0%
I love mine but what is even cooler is
 

00:06:20.960 --> 00:06:22.430 align:start position:0%
I love mine but what is even cooler is
that<00:06:21.080><c> there's</c><00:06:21.240><c> a</c><00:06:21.360><c> new</c><00:06:21.520><c> video</c><00:06:21.800><c> game</c><00:06:22.000><c> module</c><00:06:22.319><c> for</c>

00:06:22.430 --> 00:06:22.440 align:start position:0%
that there's a new video game module for
 

00:06:22.440 --> 00:06:23.790 align:start position:0%
that there's a new video game module for
The<00:06:22.520><c> Flipper</c><00:06:22.840><c> zero</c><00:06:23.199><c> powered</c><00:06:23.560><c> by</c><00:06:23.680><c> the</c>

00:06:23.790 --> 00:06:23.800 align:start position:0%
The Flipper zero powered by the
 

00:06:23.800 --> 00:06:26.070 align:start position:0%
The Flipper zero powered by the
Raspberry<00:06:24.440><c> Pi</c><00:06:25.160><c> and</c><00:06:25.319><c> with</c><00:06:25.479><c> this</c><00:06:25.599><c> module</c><00:06:26.000><c> you</c>

00:06:26.070 --> 00:06:26.080 align:start position:0%
Raspberry Pi and with this module you
 

00:06:26.080 --> 00:06:27.749 align:start position:0%
Raspberry Pi and with this module you
can<00:06:26.240><c> create</c><00:06:26.599><c> a</c><00:06:26.840><c> Nintendo</c><00:06:27.280><c> Wii</c><00:06:27.560><c> like</c>

00:06:27.749 --> 00:06:27.759 align:start position:0%
can create a Nintendo Wii like
 

00:06:27.759 --> 00:06:29.110 align:start position:0%
can create a Nintendo Wii like
controller<00:06:28.120><c> for</c><00:06:28.319><c> controlling</c><00:06:28.759><c> games</c><00:06:29.000><c> or</c>

00:06:29.110 --> 00:06:29.120 align:start position:0%
controller for controlling games or
 

00:06:29.120 --> 00:06:31.230 align:start position:0%
controller for controlling games or
other<00:06:29.440><c> typ</c><00:06:29.639><c> of</c><00:06:29.759><c> content</c><00:06:30.080><c> you</c><00:06:30.240><c> make</c><00:06:30.880><c> complete</c>

00:06:31.230 --> 00:06:31.240 align:start position:0%
other typ of content you make complete
 

00:06:31.240 --> 00:06:33.950 align:start position:0%
other typ of content you make complete
with<00:06:31.360><c> the</c><00:06:31.520><c> gyroscope</c><00:06:32.120><c> and</c><00:06:32.520><c> accelerometer</c><00:06:33.520><c> and</c>

00:06:33.950 --> 00:06:33.960 align:start position:0%
with the gyroscope and accelerometer and
 

00:06:33.960 --> 00:06:35.550 align:start position:0%
with the gyroscope and accelerometer and
the<00:06:34.160><c> the</c><00:06:34.240><c> video</c><00:06:34.479><c> game</c><00:06:34.639><c> module</c><00:06:34.960><c> is</c><00:06:35.120><c> powered</c><00:06:35.440><c> by</c>

00:06:35.550 --> 00:06:35.560 align:start position:0%
the the video game module is powered by
 

00:06:35.560 --> 00:06:38.350 align:start position:0%
the the video game module is powered by
the<00:06:35.680><c> Raspberry</c><00:06:36.120><c> Pi</c><00:06:36.800><c> rp240</c><00:06:37.800><c> which</c><00:06:37.919><c> is</c><00:06:38.120><c> the</c><00:06:38.280><c> the</c>

00:06:38.350 --> 00:06:38.360 align:start position:0%
the Raspberry Pi rp240 which is the the
 

00:06:38.360 --> 00:06:39.589 align:start position:0%
the Raspberry Pi rp240 which is the the
same<00:06:38.560><c> thing</c><00:06:38.680><c> that's</c><00:06:38.800><c> in</c><00:06:38.919><c> the</c><00:06:39.000><c> Raspberry</c><00:06:39.440><c> Pi</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
same thing that's in the Raspberry Pi
 

00:06:39.599 --> 00:06:42.150 align:start position:0%
same thing that's in the Raspberry Pi
Pico<00:06:40.440><c> and</c><00:06:40.639><c> because</c><00:06:40.919><c> of</c><00:06:41.080><c> that</c><00:06:41.199><c> lineage</c><00:06:42.080><c> there</c>

00:06:42.150 --> 00:06:42.160 align:start position:0%
Pico and because of that lineage there
 

00:06:42.160 --> 00:06:43.589 align:start position:0%
Pico and because of that lineage there
are<00:06:42.319><c> already</c><00:06:42.560><c> tons</c><00:06:42.759><c> of</c><00:06:42.880><c> Open</c><00:06:43.120><c> Source</c><00:06:43.360><c> games</c>

00:06:43.589 --> 00:06:43.599 align:start position:0%
are already tons of Open Source games
 

00:06:43.599 --> 00:06:45.350 align:start position:0%
are already tons of Open Source games
and<00:06:43.759><c> tools</c><00:06:44.160><c> that</c><00:06:44.280><c> will</c><00:06:44.440><c> work</c><00:06:44.639><c> with</c><00:06:44.800><c> it</c><00:06:45.199><c> and</c>

00:06:45.350 --> 00:06:45.360 align:start position:0%
and tools that will work with it and
 

00:06:45.360 --> 00:06:46.909 align:start position:0%
and tools that will work with it and
even<00:06:45.560><c> better</c><00:06:45.759><c> flipper</c><00:06:46.120><c> zero</c><00:06:46.360><c> maintains</c><00:06:46.800><c> a</c>

00:06:46.909 --> 00:06:46.919 align:start position:0%
even better flipper zero maintains a
 

00:06:46.919 --> 00:06:48.830 align:start position:0%
even better flipper zero maintains a
GitHub<00:06:47.240><c> repo</c><00:06:47.560><c> with</c><00:06:47.639><c> firmware</c><00:06:48.039><c> and</c><00:06:48.199><c> tooling</c>

00:06:48.830 --> 00:06:48.840 align:start position:0%
GitHub repo with firmware and tooling
 

00:06:48.840 --> 00:06:50.990 align:start position:0%
GitHub repo with firmware and tooling
and<00:06:48.960><c> even</c><00:06:49.160><c> a</c><00:06:49.280><c> game</c><00:06:49.479><c> engine</c><00:06:50.319><c> and</c><00:06:50.520><c> their</c><00:06:50.680><c> blog</c>

00:06:50.990 --> 00:06:51.000 align:start position:0%
and even a game engine and their blog
 

00:06:51.000 --> 00:06:53.589 align:start position:0%
and even a game engine and their blog
post<00:06:51.280><c> points</c><00:06:51.599><c> out</c><00:06:52.280><c> uh</c><00:06:52.400><c> the</c><00:06:52.520><c> project</c><00:06:53.080><c> uh</c><00:06:53.319><c> um</c>

00:06:53.589 --> 00:06:53.599 align:start position:0%
post points out uh the project uh um
 

00:06:53.599 --> 00:06:55.830 align:start position:0%
post points out uh the project uh um
scopy<00:06:54.360><c> which</c><00:06:54.520><c> turns</c><00:06:54.840><c> the</c><00:06:54.960><c> Raspberry</c><00:06:55.360><c> Pi</c><00:06:55.479><c> Pico</c>

00:06:55.830 --> 00:06:55.840 align:start position:0%
scopy which turns the Raspberry Pi Pico
 

00:06:55.840 --> 00:06:58.390 align:start position:0%
scopy which turns the Raspberry Pi Pico
into<00:06:56.000><c> an</c><00:06:56.440><c> oscilloscope</c><00:06:57.440><c> uh</c><00:06:57.720><c> and</c><00:06:58.160><c> it</c><00:06:58.280><c> that</c>

00:06:58.390 --> 00:06:58.400 align:start position:0%
into an oscilloscope uh and it that
 

00:06:58.400 --> 00:06:59.790 align:start position:0%
into an oscilloscope uh and it that
works<00:06:58.599><c> with</c><00:06:58.720><c> the</c><00:06:58.800><c> video</c><00:06:58.960><c> game</c><00:06:59.120><c> module</c><00:06:59.479><c> too</c><00:06:59.680><c> so</c>

00:06:59.790 --> 00:06:59.800 align:start position:0%
works with the video game module too so
 

00:06:59.800 --> 00:07:01.990 align:start position:0%
works with the video game module too so
I<00:06:59.960><c> love</c><00:07:00.240><c> that</c><00:07:00.759><c> I</c><00:07:00.879><c> love</c><00:07:01.120><c> projects</c><00:07:01.440><c> in</c><00:07:01.599><c> Hardware</c>

00:07:01.990 --> 00:07:02.000 align:start position:0%
I love that I love projects in Hardware
 

00:07:02.000 --> 00:07:03.869 align:start position:0%
I love that I love projects in Hardware
like<00:07:02.199><c> this</c><00:07:02.680><c> um</c><00:07:02.879><c> there</c><00:07:02.960><c> are</c><00:07:03.120><c> some</c><00:07:03.319><c> legislators</c>

00:07:03.869 --> 00:07:03.879 align:start position:0%
like this um there are some legislators
 

00:07:03.879 --> 00:07:05.110 align:start position:0%
like this um there are some legislators
in<00:07:04.000><c> some</c><00:07:04.199><c> countries</c><00:07:04.560><c> out</c><00:07:04.720><c> there</c><00:07:04.879><c> that</c><00:07:05.000><c> are</c>

00:07:05.110 --> 00:07:05.120 align:start position:0%
in some countries out there that are
 

00:07:05.120 --> 00:07:06.950 align:start position:0%
in some countries out there that are
trying<00:07:05.319><c> to</c><00:07:05.440><c> shut</c><00:07:05.720><c> things</c><00:07:05.919><c> like</c><00:07:06.080><c> this</c><00:07:06.280><c> down</c>

00:07:06.950 --> 00:07:06.960 align:start position:0%
trying to shut things like this down
 

00:07:06.960 --> 00:07:08.670 align:start position:0%
trying to shut things like this down
which<00:07:07.120><c> I</c><00:07:07.199><c> think</c><00:07:07.360><c> is</c><00:07:07.479><c> super</c><00:07:07.680><c> lame</c><00:07:08.280><c> um</c><00:07:08.400><c> so</c><00:07:08.520><c> I</c><00:07:08.599><c> just</c>

00:07:08.670 --> 00:07:08.680 align:start position:0%
which I think is super lame um so I just
 

00:07:08.680 --> 00:07:10.070 align:start position:0%
which I think is super lame um so I just
wanted<00:07:08.879><c> to</c><00:07:09.000><c> highlight</c><00:07:09.280><c> this</c><00:07:09.440><c> device</c><00:07:09.840><c> and</c><00:07:10.000><c> the</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
wanted to highlight this device and the
 

00:07:10.080 --> 00:07:11.270 align:start position:0%
wanted to highlight this device and the
open<00:07:10.280><c> source</c><00:07:10.479><c> software</c><00:07:10.800><c> around</c><00:07:10.960><c> it</c><00:07:11.080><c> because</c><00:07:11.240><c> I</c>

00:07:11.270 --> 00:07:11.280 align:start position:0%
open source software around it because I
 

00:07:11.280 --> 00:07:12.950 align:start position:0%
open source software around it because I
think<00:07:11.400><c> it's</c><00:07:11.520><c> super</c><00:07:11.800><c> cool</c><00:07:12.319><c> so</c><00:07:12.440><c> I've</c><00:07:12.560><c> got</c><00:07:12.680><c> links</c>

00:07:12.950 --> 00:07:12.960 align:start position:0%
think it's super cool so I've got links
 

00:07:12.960 --> 00:07:14.790 align:start position:0%
think it's super cool so I've got links
to<00:07:13.039><c> The</c><00:07:13.120><c> Flipper</c><00:07:13.479><c> zero</c><00:07:13.759><c> blog</c><00:07:14.199><c> uh</c><00:07:14.319><c> their</c><00:07:14.479><c> GitHub</c>

00:07:14.790 --> 00:07:14.800 align:start position:0%
to The Flipper zero blog uh their GitHub
 

00:07:14.800 --> 00:07:16.309 align:start position:0%
to The Flipper zero blog uh their GitHub
repo<00:07:15.160><c> and</c><00:07:15.280><c> some</c><00:07:15.440><c> other</c><00:07:15.599><c> fun</c><00:07:15.800><c> compatible</c>

00:07:16.309 --> 00:07:16.319 align:start position:0%
repo and some other fun compatible
 

00:07:16.319 --> 00:07:17.909 align:start position:0%
repo and some other fun compatible
projects<00:07:17.039><c> in</c><00:07:17.160><c> the</c><00:07:17.240><c> show</c><00:07:17.440><c> notes</c><00:07:17.680><c> in</c><00:07:17.759><c> the</c>

00:07:17.909 --> 00:07:17.919 align:start position:0%
projects in the show notes in the
 

00:07:17.919 --> 00:07:20.270 align:start position:0%
projects in the show notes in the
description<00:07:18.919><c> have</c><00:07:19.039><c> you</c><00:07:19.120><c> used</c><00:07:19.400><c> a</c><00:07:19.479><c> flipper</c><00:07:19.840><c> zero</c>

00:07:20.270 --> 00:07:20.280 align:start position:0%
description have you used a flipper zero
 

00:07:20.280 --> 00:07:22.070 align:start position:0%
description have you used a flipper zero
if<00:07:20.440><c> so</c><00:07:20.800><c> let</c><00:07:20.919><c> me</c><00:07:21.039><c> know</c><00:07:21.199><c> your</c><00:07:21.440><c> experiences</c><00:07:21.960><c> in</c>

00:07:22.070 --> 00:07:22.080 align:start position:0%
if so let me know your experiences in
 

00:07:22.080 --> 00:07:23.550 align:start position:0%
if so let me know your experiences in
the<00:07:22.240><c> comments</c><00:07:22.680><c> or</c><00:07:22.840><c> or</c><00:07:22.960><c> let</c><00:07:23.039><c> me</c><00:07:23.160><c> know</c><00:07:23.360><c> what</c><00:07:23.440><c> you</c>

00:07:23.550 --> 00:07:23.560 align:start position:0%
the comments or or let me know what you
 

00:07:23.560 --> 00:07:24.589 align:start position:0%
the comments or or let me know what you
think<00:07:23.720><c> about</c><00:07:23.960><c> anything</c><00:07:24.199><c> else</c><00:07:24.400><c> that</c><00:07:24.479><c> we</c>

00:07:24.589 --> 00:07:24.599 align:start position:0%
think about anything else that we
 

00:07:24.599 --> 00:07:26.029 align:start position:0%
think about anything else that we
covered<00:07:24.879><c> today</c><00:07:25.280><c> we'</c><00:07:25.440><c> love</c><00:07:25.560><c> to</c><00:07:25.680><c> hear</c><00:07:25.840><c> your</c>

00:07:26.029 --> 00:07:26.039 align:start position:0%
covered today we' love to hear your
 

00:07:26.039 --> 00:07:28.070 align:start position:0%
covered today we' love to hear your
thoughts<00:07:26.919><c> that's</c><00:07:27.080><c> going</c><00:07:27.199><c> to</c><00:07:27.280><c> do</c><00:07:27.400><c> it</c><00:07:27.520><c> for</c><00:07:27.720><c> me</c><00:07:28.000><c> if</c>

00:07:28.070 --> 00:07:28.080 align:start position:0%
thoughts that's going to do it for me if
 

00:07:28.080 --> 00:07:29.510 align:start position:0%
thoughts that's going to do it for me if
you<00:07:28.240><c> like</c><00:07:28.479><c> this</c><00:07:28.639><c> episode</c><00:07:28.960><c> go</c><00:07:29.039><c> ahead</c><00:07:29.199><c> and</c><00:07:29.440><c> give</c>

00:07:29.510 --> 00:07:29.520 align:start position:0%
you like this episode go ahead and give
 

00:07:29.520 --> 00:07:31.950 align:start position:0%
you like this episode go ahead and give
us<00:07:29.680><c> a</c><00:07:29.919><c> like</c><00:07:30.400><c> it</c><00:07:30.520><c> helps</c><00:07:30.759><c> the</c><00:07:30.879><c> algorithm</c><00:07:31.560><c> and</c><00:07:31.840><c> uh</c>

00:07:31.950 --> 00:07:31.960 align:start position:0%
us a like it helps the algorithm and uh
 

00:07:31.960 --> 00:07:33.110 align:start position:0%
us a like it helps the algorithm and uh
go<00:07:32.039><c> ahead</c><00:07:32.199><c> and</c><00:07:32.280><c> subscribe</c><00:07:32.599><c> to</c><00:07:32.720><c> github's</c>

00:07:33.110 --> 00:07:33.120 align:start position:0%
go ahead and subscribe to github's
 

00:07:33.120 --> 00:07:34.469 align:start position:0%
go ahead and subscribe to github's
YouTube<00:07:33.400><c> channel</c><00:07:33.759><c> for</c><00:07:33.919><c> all</c><00:07:34.039><c> of</c><00:07:34.080><c> your</c><00:07:34.199><c> nerd</c>

00:07:34.469 --> 00:07:34.479 align:start position:0%
YouTube channel for all of your nerd
 

00:07:34.479 --> 00:07:36.130 align:start position:0%
YouTube channel for all of your nerd
needs<00:07:35.199><c> see</c><00:07:35.360><c> you</c><00:07:35.520><c> next</c>

00:07:36.130 --> 00:07:36.140 align:start position:0%
needs see you next
 

00:07:36.140 --> 00:07:40.230 align:start position:0%
needs see you next
[Music]

00:07:40.230 --> 00:07:40.240 align:start position:0%
[Music]
 

00:07:40.240 --> 00:07:43.240 align:start position:0%
[Music]
time

