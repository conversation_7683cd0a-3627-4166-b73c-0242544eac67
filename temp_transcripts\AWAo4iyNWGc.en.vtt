WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.090 align:start position:0%
 
okay<00:00:00.539><c> in</c><00:00:01.079><c> today's</c><00:00:01.199><c> video</c><00:00:01.500><c> I'm</c><00:00:01.740><c> going</c><00:00:01.860><c> to</c><00:00:01.920><c> look</c>

00:00:02.090 --> 00:00:02.100 align:start position:0%
okay in today's video I'm going to look
 

00:00:02.100 --> 00:00:04.490 align:start position:0%
okay in today's video I'm going to look
at<00:00:02.280><c> a</c><00:00:02.520><c> new</c><00:00:02.760><c> large</c><00:00:03.060><c> language</c><00:00:03.419><c> model</c><00:00:03.840><c> or</c><00:00:04.200><c> rather</c>

00:00:04.490 --> 00:00:04.500 align:start position:0%
at a new large language model or rather
 

00:00:04.500 --> 00:00:07.789 align:start position:0%
at a new large language model or rather
a<00:00:05.100><c> new</c><00:00:05.339><c> fine</c><00:00:05.640><c> tuning</c><00:00:06.180><c> to</c><00:00:06.720><c> create</c><00:00:06.960><c> a</c><00:00:07.500><c> new</c><00:00:07.620><c> model</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
a new fine tuning to create a new model
 

00:00:07.799 --> 00:00:10.610 align:start position:0%
a new fine tuning to create a new model
so<00:00:08.160><c> late</c><00:00:08.400><c> last</c><00:00:08.639><c> week</c><00:00:09.000><c> databricks</c><00:00:09.960><c> announced</c>

00:00:10.610 --> 00:00:10.620 align:start position:0%
so late last week databricks announced
 

00:00:10.620 --> 00:00:13.730 align:start position:0%
so late last week databricks announced
their<00:00:11.340><c> own</c><00:00:11.639><c> kind</c><00:00:12.420><c> of</c><00:00:12.540><c> new</c><00:00:12.780><c> model</c><00:00:13.080><c> or</c><00:00:13.380><c> their</c><00:00:13.559><c> new</c>

00:00:13.730 --> 00:00:13.740 align:start position:0%
their own kind of new model or their new
 

00:00:13.740 --> 00:00:15.650 align:start position:0%
their own kind of new model or their new
fine-tuning<00:00:14.340><c> called</c><00:00:14.519><c> dolly</c>

00:00:15.650 --> 00:00:15.660 align:start position:0%
fine-tuning called dolly
 

00:00:15.660 --> 00:00:19.370 align:start position:0%
fine-tuning called dolly
and<00:00:16.320><c> this</c><00:00:16.740><c> has</c><00:00:16.920><c> been</c><00:00:17.100><c> response</c><00:00:17.820><c> to</c><00:00:18.180><c> alpaca</c><00:00:18.960><c> in</c>

00:00:19.370 --> 00:00:19.380 align:start position:0%
and this has been response to alpaca in
 

00:00:19.380 --> 00:00:21.470 align:start position:0%
and this has been response to alpaca in
that<00:00:19.619><c> the</c><00:00:19.740><c> Llama</c><00:00:20.039><c> models</c><00:00:20.640><c> were</c><00:00:21.000><c> never</c><00:00:21.180><c> really</c>

00:00:21.470 --> 00:00:21.480 align:start position:0%
that the Llama models were never really
 

00:00:21.480 --> 00:00:23.689 align:start position:0%
that the Llama models were never really
open<00:00:21.779><c> source</c><00:00:22.380><c> right</c><00:00:22.680><c> or</c><00:00:22.920><c> people</c><00:00:23.160><c> couldn't</c><00:00:23.460><c> use</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
open source right or people couldn't use
 

00:00:23.699 --> 00:00:25.849 align:start position:0%
open source right or people couldn't use
it<00:00:23.880><c> for</c><00:00:24.060><c> commercial</c><00:00:24.300><c> things</c><00:00:25.019><c> Etc</c>

00:00:25.849 --> 00:00:25.859 align:start position:0%
it for commercial things Etc
 

00:00:25.859 --> 00:00:28.730 align:start position:0%
it for commercial things Etc
so<00:00:26.460><c> what</c><00:00:26.580><c> they</c><00:00:26.760><c> did</c><00:00:26.939><c> was</c><00:00:27.119><c> they</c><00:00:28.019><c> looked</c><00:00:28.619><c> around</c>

00:00:28.730 --> 00:00:28.740 align:start position:0%
so what they did was they looked around
 

00:00:28.740 --> 00:00:31.429 align:start position:0%
so what they did was they looked around
and<00:00:29.039><c> they</c><00:00:29.279><c> found</c><00:00:29.640><c> a</c><00:00:30.119><c> model</c><00:00:30.300><c> that</c><00:00:30.840><c> was</c><00:00:31.019><c> released</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
and they found a model that was released
 

00:00:31.439 --> 00:00:33.790 align:start position:0%
and they found a model that was released
by<00:00:31.740><c> a</c><00:00:32.040><c> Luther</c><00:00:32.220><c> AI</c><00:00:32.820><c> called</c>

00:00:33.790 --> 00:00:33.800 align:start position:0%
by a Luther AI called
 

00:00:33.800 --> 00:00:37.190 align:start position:0%
by a Luther AI called
gptj6<00:00:34.800><c> billion</c><00:00:35.700><c> and</c><00:00:36.239><c> this</c><00:00:36.480><c> is</c><00:00:36.600><c> a</c><00:00:36.719><c> 6</c><00:00:36.899><c> billion</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
gptj6 billion and this is a 6 billion
 

00:00:37.200 --> 00:00:38.930 align:start position:0%
gptj6 billion and this is a 6 billion
parameter<00:00:37.739><c> model</c><00:00:37.920><c> and</c><00:00:38.399><c> they</c><00:00:38.640><c> basically</c>

00:00:38.930 --> 00:00:38.940 align:start position:0%
parameter model and they basically
 

00:00:38.940 --> 00:00:41.510 align:start position:0%
parameter model and they basically
trained<00:00:39.480><c> this</c><00:00:39.600><c> up</c><00:00:39.840><c> on</c><00:00:40.320><c> the</c><00:00:40.440><c> alpaca</c><00:00:41.040><c> data</c><00:00:41.399><c> set</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
trained this up on the alpaca data set
 

00:00:41.520 --> 00:00:43.670 align:start position:0%
trained this up on the alpaca data set
to<00:00:42.000><c> create</c><00:00:42.120><c> Dolly</c><00:00:42.780><c> and</c><00:00:43.020><c> this</c><00:00:43.140><c> is</c><00:00:43.260><c> what</c><00:00:43.500><c> they're</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
to create Dolly and this is what they're
 

00:00:43.680 --> 00:00:45.770 align:start position:0%
to create Dolly and this is what they're
calling<00:00:44.040><c> Dolly</c><00:00:44.579><c> I</c><00:00:44.820><c> think</c><00:00:44.940><c> they</c><00:00:45.239><c> talk</c><00:00:45.420><c> about</c><00:00:45.540><c> it</c>

00:00:45.770 --> 00:00:45.780 align:start position:0%
calling Dolly I think they talk about it
 

00:00:45.780 --> 00:00:48.110 align:start position:0%
calling Dolly I think they talk about it
in<00:00:45.960><c> this</c><00:00:46.079><c> blog</c><00:00:46.379><c> post</c><00:00:46.559><c> that</c><00:00:47.160><c> dolly</c><00:00:47.579><c> is</c><00:00:47.760><c> based</c><00:00:48.059><c> on</c>

00:00:48.110 --> 00:00:48.120 align:start position:0%
in this blog post that dolly is based on
 

00:00:48.120 --> 00:00:50.209 align:start position:0%
in this blog post that dolly is based on
the<00:00:48.300><c> Clone</c><00:00:48.600><c> sheep</c><00:00:49.020><c> that</c><00:00:49.379><c> was</c><00:00:49.559><c> called</c><00:00:49.739><c> Dolly</c>

00:00:50.209 --> 00:00:50.219 align:start position:0%
the Clone sheep that was called Dolly
 

00:00:50.219 --> 00:00:51.709 align:start position:0%
the Clone sheep that was called Dolly
that's<00:00:50.460><c> where</c><00:00:50.640><c> they</c><00:00:50.940><c> got</c><00:00:51.059><c> their</c><00:00:51.300><c> name</c><00:00:51.480><c> from</c>

00:00:51.709 --> 00:00:51.719 align:start position:0%
that's where they got their name from
 

00:00:51.719 --> 00:00:53.569 align:start position:0%
that's where they got their name from
they<00:00:52.079><c> didn't</c><00:00:52.260><c> release</c><00:00:52.500><c> the</c><00:00:52.800><c> weights</c><00:00:53.160><c> they</c>

00:00:53.569 --> 00:00:53.579 align:start position:0%
they didn't release the weights they
 

00:00:53.579 --> 00:00:54.709 align:start position:0%
they didn't release the weights they
basically<00:00:53.820><c> made</c><00:00:54.000><c> it</c><00:00:54.180><c> so</c><00:00:54.300><c> you</c><00:00:54.420><c> could</c><00:00:54.539><c> contact</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
basically made it so you could contact
 

00:00:54.719 --> 00:00:56.090 align:start position:0%
basically made it so you could contact
them<00:00:55.020><c> I</c><00:00:55.199><c> think</c><00:00:55.320><c> for</c><00:00:55.500><c> the</c><00:00:55.680><c> weights</c><00:00:55.980><c> or</c>

00:00:56.090 --> 00:00:56.100 align:start position:0%
them I think for the weights or
 

00:00:56.100 --> 00:00:57.709 align:start position:0%
them I think for the weights or
something<00:00:56.280><c> like</c><00:00:56.460><c> that</c><00:00:56.640><c> because</c><00:00:57.059><c> this</c><00:00:57.539><c> model</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
something like that because this model
 

00:00:57.719 --> 00:01:00.470 align:start position:0%
something like that because this model
is<00:00:58.079><c> already</c><00:00:58.500><c> open</c><00:00:58.860><c> source</c><00:00:59.460><c> and</c><00:01:00.000><c> has</c><00:01:00.180><c> been</c><00:01:00.360><c> up</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
is already open source and has been up
 

00:01:00.480 --> 00:01:02.389 align:start position:0%
is already open source and has been up
on<00:01:00.600><c> hugging</c><00:01:00.960><c> face</c><00:01:01.140><c> for</c><00:01:01.379><c> quite</c><00:01:01.620><c> a</c><00:01:01.800><c> while</c><00:01:01.920><c> I</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
on hugging face for quite a while I
 

00:01:02.399 --> 00:01:03.950 align:start position:0%
on hugging face for quite a while I
thought<00:01:02.520><c> I</c><00:01:02.699><c> would</c><00:01:02.820><c> just</c><00:01:03.059><c> train</c><00:01:03.300><c> up</c><00:01:03.539><c> and</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
thought I would just train up and
 

00:01:03.960 --> 00:01:06.590 align:start position:0%
thought I would just train up and
fine-tune<00:01:04.559><c> one</c><00:01:04.680><c> myself</c><00:01:04.920><c> and</c><00:01:05.760><c> rather</c><00:01:06.000><c> than</c><00:01:06.180><c> use</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
fine-tune one myself and rather than use
 

00:01:06.600 --> 00:01:10.130 align:start position:0%
fine-tune one myself and rather than use
the<00:01:07.200><c> original</c><00:01:07.760><c> alpaca</c><00:01:08.760><c> data</c><00:01:09.240><c> set</c><00:01:09.360><c> I</c><00:01:09.720><c> use</c><00:01:09.900><c> this</c>

00:01:10.130 --> 00:01:10.140 align:start position:0%
the original alpaca data set I use this
 

00:01:10.140 --> 00:01:12.170 align:start position:0%
the original alpaca data set I use this
data<00:01:10.500><c> set</c><00:01:10.560><c> which</c><00:01:10.920><c> is</c><00:01:11.040><c> basically</c><00:01:11.520><c> a</c><00:01:12.000><c> clean</c>

00:01:12.170 --> 00:01:12.180 align:start position:0%
data set which is basically a clean
 

00:01:12.180 --> 00:01:14.510 align:start position:0%
data set which is basically a clean
version<00:01:12.540><c> of</c><00:01:13.020><c> it</c><00:01:13.200><c> so</c><00:01:13.560><c> it</c><00:01:13.799><c> turns</c><00:01:13.979><c> out</c><00:01:14.100><c> apparently</c>

00:01:14.510 --> 00:01:14.520 align:start position:0%
version of it so it turns out apparently
 

00:01:14.520 --> 00:01:16.550 align:start position:0%
version of it so it turns out apparently
that<00:01:14.760><c> in</c><00:01:15.240><c> the</c><00:01:15.360><c> 52</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
that in the 52
 

00:01:16.560 --> 00:01:18.770 align:start position:0%
that in the 52
000<00:01:16.740><c> examples</c><00:01:17.280><c> there</c><00:01:18.060><c> are</c><00:01:18.180><c> actually</c><00:01:18.299><c> quite</c><00:01:18.600><c> a</c>

00:01:18.770 --> 00:01:18.780 align:start position:0%
000 examples there are actually quite a
 

00:01:18.780 --> 00:01:20.570 align:start position:0%
000 examples there are actually quite a
number<00:01:18.960><c> of</c><00:01:19.320><c> examples</c><00:01:19.740><c> that</c><00:01:19.979><c> were</c><00:01:20.159><c> not</c><00:01:20.340><c> very</c>

00:01:20.570 --> 00:01:20.580 align:start position:0%
number of examples that were not very
 

00:01:20.580 --> 00:01:23.390 align:start position:0%
number of examples that were not very
good<00:01:20.820><c> that</c><00:01:21.119><c> they</c><00:01:21.420><c> had</c><00:01:21.540><c> hallucinations</c><00:01:22.400><c> that</c>

00:01:23.390 --> 00:01:23.400 align:start position:0%
good that they had hallucinations that
 

00:01:23.400 --> 00:01:27.109 align:start position:0%
good that they had hallucinations that
they<00:01:23.700><c> had</c><00:01:24.000><c> things</c><00:01:24.299><c> done</c><00:01:24.780><c> incorrectly</c><00:01:25.640><c> and</c><00:01:26.640><c> so</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
they had things done incorrectly and so
 

00:01:27.119 --> 00:01:29.390 align:start position:0%
they had things done incorrectly and so
what<00:01:27.360><c> this</c><00:01:27.780><c> person</c><00:01:27.960><c> here</c><00:01:28.380><c> on</c><00:01:28.560><c> GitHub</c><00:01:28.860><c> has</c><00:01:29.100><c> done</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
what this person here on GitHub has done
 

00:01:29.400 --> 00:01:30.890 align:start position:0%
what this person here on GitHub has done
and<00:01:29.759><c> maybe</c><00:01:29.880><c> with</c><00:01:30.119><c> a</c><00:01:30.299><c> group</c><00:01:30.420><c> of</c><00:01:30.540><c> other</c><00:01:30.659><c> people</c>

00:01:30.890 --> 00:01:30.900 align:start position:0%
and maybe with a group of other people
 

00:01:30.900 --> 00:01:33.050 align:start position:0%
and maybe with a group of other people
as<00:01:31.140><c> well</c><00:01:31.380><c> is</c><00:01:31.920><c> they've</c><00:01:32.220><c> gone</c><00:01:32.400><c> through</c><00:01:32.700><c> and</c>

00:01:33.050 --> 00:01:33.060 align:start position:0%
as well is they've gone through and
 

00:01:33.060 --> 00:01:34.730 align:start position:0%
as well is they've gone through and
actually<00:01:33.240><c> cleaned</c><00:01:33.659><c> up</c><00:01:33.780><c> the</c><00:01:33.960><c> data</c><00:01:34.259><c> set</c><00:01:34.380><c> to</c><00:01:34.619><c> make</c>

00:01:34.730 --> 00:01:34.740 align:start position:0%
actually cleaned up the data set to make
 

00:01:34.740 --> 00:01:36.649 align:start position:0%
actually cleaned up the data set to make
it<00:01:34.979><c> more</c><00:01:35.220><c> accurate</c><00:01:35.759><c> so</c><00:01:36.180><c> I</c><00:01:36.299><c> thought</c><00:01:36.420><c> it'd</c><00:01:36.600><c> be</c>

00:01:36.649 --> 00:01:36.659 align:start position:0%
it more accurate so I thought it'd be
 

00:01:36.659 --> 00:01:39.170 align:start position:0%
it more accurate so I thought it'd be
also<00:01:36.960><c> interesting</c><00:01:37.380><c> to</c><00:01:37.740><c> take</c><00:01:37.979><c> a</c><00:01:38.579><c> Luther</c><00:01:38.759><c> model</c>

00:01:39.170 --> 00:01:39.180 align:start position:0%
also interesting to take a Luther model
 

00:01:39.180 --> 00:01:42.530 align:start position:0%
also interesting to take a Luther model
and<00:01:39.900><c> fine</c><00:01:40.320><c> tune</c><00:01:40.619><c> it</c><00:01:40.860><c> on</c><00:01:41.159><c> this</c><00:01:41.460><c> data</c><00:01:41.820><c> set</c>

00:01:42.530 --> 00:01:42.540 align:start position:0%
and fine tune it on this data set
 

00:01:42.540 --> 00:01:46.010 align:start position:0%
and fine tune it on this data set
and<00:01:43.320><c> what</c><00:01:43.740><c> we</c><00:01:43.920><c> ended</c><00:01:44.220><c> up</c><00:01:44.400><c> with</c><00:01:44.700><c> was</c><00:01:45.180><c> Laura</c>

00:01:46.010 --> 00:01:46.020 align:start position:0%
and what we ended up with was Laura
 

00:01:46.020 --> 00:01:49.249 align:start position:0%
and what we ended up with was Laura
fine-tuning<00:01:46.920><c> of</c><00:01:47.520><c> Dolly</c><00:01:48.060><c> so</c><00:01:48.479><c> here</c><00:01:48.960><c> is</c><00:01:49.140><c> The</c>

00:01:49.249 --> 00:01:49.259 align:start position:0%
fine-tuning of Dolly so here is The
 

00:01:49.259 --> 00:01:50.389 align:start position:0%
fine-tuning of Dolly so here is The
Notebook<00:01:49.560><c> that</c><00:01:49.860><c> I'm</c><00:01:49.979><c> just</c><00:01:50.100><c> going</c><00:01:50.159><c> to</c><00:01:50.280><c> go</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
Notebook that I'm just going to go
 

00:01:50.399 --> 00:01:51.770 align:start position:0%
Notebook that I'm just going to go
through<00:01:50.579><c> I'm</c><00:01:51.000><c> just</c><00:01:51.119><c> going</c><00:01:51.240><c> to</c><00:01:51.299><c> go</c><00:01:51.420><c> through</c><00:01:51.540><c> the</c>

00:01:51.770 --> 00:01:51.780 align:start position:0%
through I'm just going to go through the
 

00:01:51.780 --> 00:01:54.289 align:start position:0%
through I'm just going to go through the
inference<00:01:52.259><c> in</c><00:01:52.439><c> this</c><00:01:52.680><c> video</c><00:01:52.860><c> if</c><00:01:53.579><c> you</c><00:01:53.759><c> want</c><00:01:54.119><c> to</c>

00:01:54.289 --> 00:01:54.299 align:start position:0%
inference in this video if you want to
 

00:01:54.299 --> 00:01:55.910 align:start position:0%
inference in this video if you want to
see<00:01:54.420><c> me</c><00:01:54.600><c> talk</c><00:01:54.780><c> about</c><00:01:54.899><c> the</c><00:01:55.140><c> fine</c><00:01:55.320><c> tuning</c><00:01:55.740><c> or</c>

00:01:55.910 --> 00:01:55.920 align:start position:0%
see me talk about the fine tuning or
 

00:01:55.920 --> 00:01:57.710 align:start position:0%
see me talk about the fine tuning or
something<00:01:56.040><c> let</c><00:01:56.340><c> me</c><00:01:56.520><c> know</c><00:01:56.640><c> I</c><00:01:56.939><c> can</c><00:01:57.119><c> make</c><00:01:57.299><c> a</c><00:01:57.420><c> video</c>

00:01:57.710 --> 00:01:57.720 align:start position:0%
something let me know I can make a video
 

00:01:57.720 --> 00:02:00.830 align:start position:0%
something let me know I can make a video
for<00:01:58.079><c> that</c><00:01:58.380><c> it's</c><00:01:58.860><c> very</c><00:01:59.100><c> similar</c><00:01:59.579><c> to</c><00:01:59.939><c> what</c><00:02:00.540><c> I</c><00:02:00.659><c> did</c>

00:02:00.830 --> 00:02:00.840 align:start position:0%
for that it's very similar to what I did
 

00:02:00.840 --> 00:02:02.929 align:start position:0%
for that it's very similar to what I did
for<00:02:01.140><c> the</c><00:02:01.380><c> Llama</c><00:02:01.680><c> fine</c><00:02:01.979><c> tuning</c><00:02:02.460><c> and</c><00:02:02.640><c> I</c><00:02:02.820><c> might</c>

00:02:02.929 --> 00:02:02.939 align:start position:0%
for the Llama fine tuning and I might
 

00:02:02.939 --> 00:02:04.670 align:start position:0%
for the Llama fine tuning and I might
actually<00:02:03.119><c> make</c><00:02:03.299><c> an</c><00:02:03.540><c> updated</c><00:02:03.840><c> video</c><00:02:04.140><c> of</c><00:02:04.439><c> that</c>

00:02:04.670 --> 00:02:04.680 align:start position:0%
actually make an updated video of that
 

00:02:04.680 --> 00:02:06.709 align:start position:0%
actually make an updated video of that
one<00:02:04.860><c> because</c><00:02:05.219><c> things</c><00:02:05.759><c> have</c><00:02:06.000><c> changed</c><00:02:06.479><c> a</c><00:02:06.600><c> little</c>

00:02:06.709 --> 00:02:06.719 align:start position:0%
one because things have changed a little
 

00:02:06.719 --> 00:02:08.690 align:start position:0%
one because things have changed a little
bit<00:02:06.899><c> now</c><00:02:07.140><c> rather</c><00:02:07.619><c> than</c><00:02:07.799><c> import</c><00:02:08.039><c> any</c><00:02:08.399><c> special</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
bit now rather than import any special
 

00:02:08.700 --> 00:02:10.850 align:start position:0%
bit now rather than import any special
versions<00:02:09.179><c> of</c><00:02:09.599><c> hugging</c><00:02:10.020><c> face</c><00:02:10.200><c> I'm</c><00:02:10.679><c> just</c>

00:02:10.850 --> 00:02:10.860 align:start position:0%
versions of hugging face I'm just
 

00:02:10.860 --> 00:02:12.830 align:start position:0%
versions of hugging face I'm just
bringing<00:02:11.220><c> in</c><00:02:11.340><c> the</c><00:02:11.640><c> latest</c><00:02:11.819><c> version</c><00:02:12.300><c> from</c>

00:02:12.830 --> 00:02:12.840 align:start position:0%
bringing in the latest version from
 

00:02:12.840 --> 00:02:15.170 align:start position:0%
bringing in the latest version from
GitHub<00:02:13.319><c> here</c><00:02:13.860><c> and</c><00:02:14.400><c> we're</c><00:02:14.580><c> bringing</c><00:02:14.879><c> in</c><00:02:15.000><c> the</c>

00:02:15.170 --> 00:02:15.180 align:start position:0%
GitHub here and we're bringing in the
 

00:02:15.180 --> 00:02:17.449 align:start position:0%
GitHub here and we're bringing in the
pef<00:02:15.540><c> library</c><00:02:15.840><c> and</c><00:02:16.319><c> bits</c><00:02:16.680><c> and</c><00:02:16.739><c> bytes</c><00:02:17.160><c> to</c><00:02:17.340><c> be</c>

00:02:17.449 --> 00:02:17.459 align:start position:0%
pef library and bits and bytes to be
 

00:02:17.459 --> 00:02:19.850 align:start position:0%
pef library and bits and bytes to be
able<00:02:17.520><c> to</c><00:02:17.640><c> load</c><00:02:17.940><c> this</c><00:02:18.239><c> in</c><00:02:18.780><c> 8-bit</c><00:02:19.319><c> and</c><00:02:19.500><c> then</c><00:02:19.680><c> so</c>

00:02:19.850 --> 00:02:19.860 align:start position:0%
able to load this in 8-bit and then so
 

00:02:19.860 --> 00:02:22.070 align:start position:0%
able to load this in 8-bit and then so
we<00:02:20.040><c> can</c><00:02:20.160><c> do</c><00:02:20.400><c> a</c><00:02:20.580><c> Laura</c><00:02:21.000><c> fine</c><00:02:21.120><c> tuning</c><00:02:21.599><c> this</c><00:02:21.959><c> was</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
we can do a Laura fine tuning this was
 

00:02:22.080 --> 00:02:23.510 align:start position:0%
we can do a Laura fine tuning this was
pretty<00:02:22.200><c> standard</c><00:02:22.560><c> this</c><00:02:22.860><c> is</c><00:02:22.980><c> basically</c><00:02:23.280><c> just</c>

00:02:23.510 --> 00:02:23.520 align:start position:0%
pretty standard this is basically just
 

00:02:23.520 --> 00:02:26.089 align:start position:0%
pretty standard this is basically just
loading<00:02:23.940><c> it</c><00:02:24.120><c> up</c><00:02:24.300><c> also</c><00:02:24.720><c> for</c><00:02:25.319><c> you</c><00:02:25.440><c> to</c><00:02:25.620><c> be</c><00:02:25.680><c> able</c><00:02:25.860><c> to</c>

00:02:26.089 --> 00:02:26.099 align:start position:0%
loading it up also for you to be able to
 

00:02:26.099 --> 00:02:28.850 align:start position:0%
loading it up also for you to be able to
use<00:02:26.280><c> it</c><00:02:26.520><c> and</c><00:02:27.060><c> then</c><00:02:27.180><c> testing</c><00:02:27.660><c> it</c><00:02:27.840><c> out</c><00:02:28.020><c> here</c><00:02:28.620><c> I've</c>

00:02:28.850 --> 00:02:28.860 align:start position:0%
use it and then testing it out here I've
 

00:02:28.860 --> 00:02:31.490 align:start position:0%
use it and then testing it out here I've
put<00:02:29.160><c> a</c><00:02:29.580><c> simple</c><00:02:29.819><c> sort</c><00:02:30.599><c> of</c><00:02:30.720><c> example</c><00:02:31.080><c> of</c><00:02:31.260><c> the</c>

00:02:31.490 --> 00:02:31.500 align:start position:0%
put a simple sort of example of the
 

00:02:31.500 --> 00:02:33.710 align:start position:0%
put a simple sort of example of the
prompt<00:02:31.860><c> so</c><00:02:32.099><c> remember</c><00:02:32.280><c> it's</c><00:02:32.760><c> trained</c><00:02:33.239><c> on</c><00:02:33.480><c> the</c>

00:02:33.710 --> 00:02:33.720 align:start position:0%
prompt so remember it's trained on the
 

00:02:33.720 --> 00:02:36.530 align:start position:0%
prompt so remember it's trained on the
same<00:02:33.959><c> or</c><00:02:34.800><c> similar</c><00:02:35.580><c> data</c><00:02:35.940><c> set</c><00:02:36.000><c> to</c><00:02:36.300><c> the</c><00:02:36.420><c> original</c>

00:02:36.530 --> 00:02:36.540 align:start position:0%
same or similar data set to the original
 

00:02:36.540 --> 00:02:39.110 align:start position:0%
same or similar data set to the original
alpaca<00:02:37.260><c> it's</c><00:02:37.440><c> just</c><00:02:37.620><c> a</c><00:02:37.920><c> clean</c><00:02:38.099><c> version</c><00:02:38.520><c> of</c><00:02:38.879><c> that</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
alpaca it's just a clean version of that
 

00:02:39.120 --> 00:02:41.390 align:start position:0%
alpaca it's just a clean version of that
data<00:02:39.780><c> set</c><00:02:39.900><c> so</c><00:02:40.440><c> this</c><00:02:40.800><c> is</c><00:02:40.860><c> definitely</c><00:02:41.160><c> an</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
data set so this is definitely an
 

00:02:41.400 --> 00:02:44.570 align:start position:0%
data set so this is definitely an
instruction<00:02:42.000><c> find</c><00:02:42.480><c> tuning</c><00:02:43.200><c> data</c><00:02:43.500><c> set</c><00:02:43.620><c> the</c>

00:02:44.570 --> 00:02:44.580 align:start position:0%
instruction find tuning data set the
 

00:02:44.580 --> 00:02:46.430 align:start position:0%
instruction find tuning data set the
issues<00:02:45.000><c> that</c><00:02:45.540><c> is</c><00:02:45.840><c> this</c><00:02:46.019><c> going</c><00:02:46.200><c> to</c><00:02:46.379><c> be</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
issues that is this going to be
 

00:02:46.440 --> 00:02:49.250 align:start position:0%
issues that is this going to be
competitive<00:02:47.040><c> with</c><00:02:47.700><c> the</c><00:02:48.120><c> actual</c><00:02:48.420><c> original</c><00:02:48.780><c> one</c>

00:02:49.250 --> 00:02:49.260 align:start position:0%
competitive with the actual original one
 

00:02:49.260 --> 00:02:52.490 align:start position:0%
competitive with the actual original one
and<00:02:50.099><c> you</c><00:02:50.340><c> find</c><00:02:50.580><c> that</c><00:02:50.879><c> the</c><00:02:51.300><c> answers</c><00:02:51.660><c> that</c><00:02:52.319><c> it</c>

00:02:52.490 --> 00:02:52.500 align:start position:0%
and you find that the answers that it
 

00:02:52.500 --> 00:02:54.589 align:start position:0%
and you find that the answers that it
gives<00:02:52.620><c> not</c><00:02:53.160><c> bad</c><00:02:53.519><c> so</c><00:02:53.940><c> you</c><00:02:54.060><c> can</c><00:02:54.120><c> see</c><00:02:54.239><c> here</c><00:02:54.420><c> when</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
gives not bad so you can see here when
 

00:02:54.599 --> 00:02:56.869 align:start position:0%
gives not bad so you can see here when
we<00:02:54.780><c> ask</c><00:02:55.019><c> it</c><00:02:55.200><c> about</c><00:02:55.560><c> alpacas</c><00:02:56.400><c> what's</c><00:02:56.700><c> the</c>

00:02:56.869 --> 00:02:56.879 align:start position:0%
we ask it about alpacas what's the
 

00:02:56.879 --> 00:02:58.490 align:start position:0%
we ask it about alpacas what's the
difference<00:02:57.000><c> between</c><00:02:57.180><c> Packers</c><00:02:57.959><c> and</c><00:02:58.140><c> sheep</c>

00:02:58.490 --> 00:02:58.500 align:start position:0%
difference between Packers and sheep
 

00:02:58.500 --> 00:03:00.470 align:start position:0%
difference between Packers and sheep
because<00:02:58.680><c> Dolly's</c><00:02:59.160><c> a</c><00:02:59.400><c> sheep</c><00:02:59.640><c> it's</c><00:03:00.120><c> able</c><00:03:00.360><c> to</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
because Dolly's a sheep it's able to
 

00:03:00.480 --> 00:03:03.350 align:start position:0%
because Dolly's a sheep it's able to
tell<00:03:00.720><c> us</c><00:03:00.900><c> some</c><00:03:01.500><c> quite</c><00:03:01.680><c> good</c><00:03:01.920><c> results</c><00:03:02.280><c> and</c><00:03:03.000><c> it</c>

00:03:03.350 --> 00:03:03.360 align:start position:0%
tell us some quite good results and it
 

00:03:03.360 --> 00:03:05.150 align:start position:0%
tell us some quite good results and it
tends<00:03:03.720><c> to</c><00:03:03.780><c> go</c><00:03:03.959><c> on</c><00:03:04.080><c> and</c><00:03:04.319><c> gives</c><00:03:04.560><c> you</c><00:03:04.739><c> a</c><00:03:04.920><c> quite</c>

00:03:05.150 --> 00:03:05.160 align:start position:0%
tends to go on and gives you a quite
 

00:03:05.160 --> 00:03:08.150 align:start position:0%
tends to go on and gives you a quite
interesting<00:03:05.700><c> results</c><00:03:06.319><c> but</c><00:03:07.319><c> I</c><00:03:07.500><c> would</c><00:03:07.620><c> say</c><00:03:07.739><c> on</c>

00:03:08.150 --> 00:03:08.160 align:start position:0%
interesting results but I would say on
 

00:03:08.160 --> 00:03:11.570 align:start position:0%
interesting results but I would say on
the<00:03:08.340><c> whole</c><00:03:08.640><c> the</c><00:03:09.180><c> results</c><00:03:09.300><c> are</c><00:03:09.780><c> not</c><00:03:10.019><c> as</c><00:03:10.319><c> good</c><00:03:10.580><c> as</c>

00:03:11.570 --> 00:03:11.580 align:start position:0%
the whole the results are not as good as
 

00:03:11.580 --> 00:03:14.089 align:start position:0%
the whole the results are not as good as
the<00:03:12.180><c> original</c><00:03:12.360><c> alpaca</c><00:03:13.260><c> which</c><00:03:13.500><c> is</c><00:03:13.560><c> trained</c><00:03:13.920><c> on</c>

00:03:14.089 --> 00:03:14.099 align:start position:0%
the original alpaca which is trained on
 

00:03:14.099 --> 00:03:16.250 align:start position:0%
the original alpaca which is trained on
llama<00:03:14.400><c> now</c><00:03:15.060><c> obviously</c><00:03:15.480><c> that</c><00:03:15.840><c> doesn't</c><00:03:16.019><c> mean</c>

00:03:16.250 --> 00:03:16.260 align:start position:0%
llama now obviously that doesn't mean
 

00:03:16.260 --> 00:03:18.229 align:start position:0%
llama now obviously that doesn't mean
this<00:03:16.500><c> model</c><00:03:16.680><c> is</c><00:03:16.980><c> not</c><00:03:17.159><c> good</c><00:03:17.400><c> right</c><00:03:17.700><c> this</c><00:03:18.060><c> is</c>

00:03:18.229 --> 00:03:18.239 align:start position:0%
this model is not good right this is
 

00:03:18.239 --> 00:03:20.149 align:start position:0%
this model is not good right this is
also<00:03:18.540><c> really</c><00:03:18.780><c> great</c><00:03:19.019><c> because</c><00:03:19.379><c> this</c><00:03:19.800><c> we</c><00:03:19.980><c> can</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
also really great because this we can
 

00:03:20.159 --> 00:03:22.250 align:start position:0%
also really great because this we can
commercially<00:03:20.640><c> use</c><00:03:21.060><c> if</c><00:03:21.540><c> you</c><00:03:21.780><c> were</c><00:03:21.900><c> to</c><00:03:21.959><c> fine</c>

00:03:22.250 --> 00:03:22.260 align:start position:0%
commercially use if you were to fine
 

00:03:22.260 --> 00:03:23.990 align:start position:0%
commercially use if you were to fine
tune<00:03:22.500><c> this</c><00:03:22.680><c> on</c><00:03:22.860><c> your</c><00:03:22.980><c> own</c><00:03:23.099><c> synthetic</c><00:03:23.580><c> data</c><00:03:23.879><c> set</c>

00:03:23.990 --> 00:03:24.000 align:start position:0%
tune this on your own synthetic data set
 

00:03:24.000 --> 00:03:25.790 align:start position:0%
tune this on your own synthetic data set
you<00:03:24.239><c> would</c><00:03:24.360><c> be</c><00:03:24.540><c> able</c><00:03:24.659><c> to</c><00:03:24.780><c> use</c><00:03:25.019><c> it</c><00:03:25.260><c> which</c><00:03:25.560><c> you</c>

00:03:25.790 --> 00:03:25.800 align:start position:0%
you would be able to use it which you
 

00:03:25.800 --> 00:03:29.030 align:start position:0%
you would be able to use it which you
can't<00:03:25.920><c> do</c><00:03:26.280><c> with</c><00:03:27.060><c> the</c><00:03:27.300><c> Llama</c><00:03:27.540><c> model</c><00:03:27.840><c> there</c><00:03:28.379><c> so</c>

00:03:29.030 --> 00:03:29.040 align:start position:0%
can't do with the Llama model there so
 

00:03:29.040 --> 00:03:31.190 align:start position:0%
can't do with the Llama model there so
one<00:03:29.400><c> of</c><00:03:29.519><c> the</c><00:03:29.580><c> things</c><00:03:29.760><c> that</c><00:03:30.000><c> causes</c><00:03:30.720><c> this</c><00:03:30.959><c> to</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
one of the things that causes this to
 

00:03:31.200 --> 00:03:33.649 align:start position:0%
one of the things that causes this to
perhaps<00:03:31.680><c> not</c><00:03:31.860><c> be</c><00:03:32.099><c> as</c><00:03:32.340><c> good</c><00:03:32.580><c> as</c><00:03:32.940><c> the</c><00:03:33.239><c> Llama</c>

00:03:33.649 --> 00:03:33.659 align:start position:0%
perhaps not be as good as the Llama
 

00:03:33.659 --> 00:03:36.589 align:start position:0%
perhaps not be as good as the Llama
models<00:03:34.379><c> as</c><00:03:34.680><c> a</c><00:03:34.860><c> base</c><00:03:35.040><c> model</c><00:03:35.280><c> for</c><00:03:35.700><c> fine</c><00:03:36.000><c> tuning</c>

00:03:36.589 --> 00:03:36.599 align:start position:0%
models as a base model for fine tuning
 

00:03:36.599 --> 00:03:39.649 align:start position:0%
models as a base model for fine tuning
is<00:03:37.200><c> the</c><00:03:37.440><c> actual</c><00:03:37.680><c> number</c><00:03:38.040><c> of</c><00:03:38.280><c> tokens</c><00:03:39.120><c> that</c><00:03:39.480><c> it's</c>

00:03:39.649 --> 00:03:39.659 align:start position:0%
is the actual number of tokens that it's
 

00:03:39.659 --> 00:03:41.690 align:start position:0%
is the actual number of tokens that it's
trained<00:03:40.019><c> on</c><00:03:40.140><c> from</c><00:03:40.500><c> memory</c><00:03:40.920><c> somewhere</c><00:03:41.400><c> here</c><00:03:41.580><c> I</c>

00:03:41.690 --> 00:03:41.700 align:start position:0%
trained on from memory somewhere here I
 

00:03:41.700 --> 00:03:43.250 align:start position:0%
trained on from memory somewhere here I
was<00:03:41.819><c> looking</c><00:03:41.940><c> at</c><00:03:42.120><c> it</c><00:03:42.239><c> here</c><00:03:42.420><c> this</c><00:03:42.900><c> model</c><00:03:43.080><c> was</c>

00:03:43.250 --> 00:03:43.260 align:start position:0%
was looking at it here this model was
 

00:03:43.260 --> 00:03:46.430 align:start position:0%
was looking at it here this model was
trained<00:03:43.560><c> up</c><00:03:43.620><c> for</c><00:03:43.860><c> 402</c><00:03:44.700><c> billion</c><00:03:45.120><c> tokens</c><00:03:45.659><c> now</c><00:03:46.260><c> we</c>

00:03:46.430 --> 00:03:46.440 align:start position:0%
trained up for 402 billion tokens now we
 

00:03:46.440 --> 00:03:48.410 align:start position:0%
trained up for 402 billion tokens now we
know<00:03:46.560><c> that</c><00:03:46.799><c> the</c><00:03:46.980><c> Llama</c><00:03:47.220><c> model</c><00:03:47.519><c> was</c><00:03:47.940><c> trained</c>

00:03:48.410 --> 00:03:48.420 align:start position:0%
know that the Llama model was trained
 

00:03:48.420 --> 00:03:51.530 align:start position:0%
know that the Llama model was trained
for<00:03:49.019><c> a</c><00:03:49.319><c> trillion</c><00:03:49.680><c> tokens</c><00:03:50.159><c> for</c><00:03:50.760><c> the</c><00:03:50.879><c> 7</c><00:03:51.060><c> billion</c>

00:03:51.530 --> 00:03:51.540 align:start position:0%
for a trillion tokens for the 7 billion
 

00:03:51.540 --> 00:03:53.990 align:start position:0%
for a trillion tokens for the 7 billion
and<00:03:51.780><c> the</c><00:03:51.959><c> 13</c><00:03:52.140><c> billion</c><00:03:52.739><c> parameter</c><00:03:53.580><c> versions</c>

00:03:53.990 --> 00:03:54.000 align:start position:0%
and the 13 billion parameter versions
 

00:03:54.000 --> 00:03:58.250 align:start position:0%
and the 13 billion parameter versions
and<00:03:54.840><c> 1.4</c><00:03:55.680><c> trillion</c><00:03:56.220><c> for</c><00:03:56.879><c> the</c><00:03:57.060><c> 30</c><00:03:57.360><c> billion</c><00:03:57.900><c> and</c>

00:03:58.250 --> 00:03:58.260 align:start position:0%
and 1.4 trillion for the 30 billion and
 

00:03:58.260 --> 00:04:01.550 align:start position:0%
and 1.4 trillion for the 30 billion and
the<00:03:58.440><c> 65</c><00:03:58.920><c> billion</c><00:03:59.400><c> parameter</c><00:04:00.120><c> versions</c><00:04:00.599><c> so</c>

00:04:01.550 --> 00:04:01.560 align:start position:0%
the 65 billion parameter versions so
 

00:04:01.560 --> 00:04:04.850 align:start position:0%
the 65 billion parameter versions so
this<00:04:02.159><c> is</c><00:04:02.340><c> definitely</c><00:04:02.879><c> at</c><00:04:03.299><c> a</c><00:04:03.480><c> disadvantage</c><00:04:04.319><c> as</c>

00:04:04.850 --> 00:04:04.860 align:start position:0%
this is definitely at a disadvantage as
 

00:04:04.860 --> 00:04:07.130 align:start position:0%
this is definitely at a disadvantage as
a<00:04:05.099><c> base</c><00:04:05.280><c> model</c><00:04:05.519><c> compared</c><00:04:06.360><c> to</c><00:04:06.720><c> the</c><00:04:07.019><c> original</c>

00:04:07.130 --> 00:04:07.140 align:start position:0%
a base model compared to the original
 

00:04:07.140 --> 00:04:09.710 align:start position:0%
a base model compared to the original
llama<00:04:07.739><c> that</c><00:04:08.280><c> said</c><00:04:08.459><c> though</c><00:04:08.700><c> it's</c><00:04:09.120><c> still</c><00:04:09.360><c> not</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
llama that said though it's still not
 

00:04:09.720 --> 00:04:12.110 align:start position:0%
llama that said though it's still not
bad<00:04:09.959><c> you</c><00:04:10.319><c> can</c><00:04:10.439><c> put</c><00:04:10.680><c> in</c><00:04:10.920><c> your</c><00:04:11.400><c> instructions</c><00:04:11.879><c> and</c>

00:04:12.110 --> 00:04:12.120 align:start position:0%
bad you can put in your instructions and
 

00:04:12.120 --> 00:04:13.250 align:start position:0%
bad you can put in your instructions and
like<00:04:12.239><c> I</c><00:04:12.360><c> said</c><00:04:12.480><c> asking</c><00:04:12.900><c> it</c><00:04:13.019><c> the</c><00:04:13.140><c> difference</c>

00:04:13.250 --> 00:04:13.260 align:start position:0%
like I said asking it the difference
 

00:04:13.260 --> 00:04:15.050 align:start position:0%
like I said asking it the difference
between<00:04:13.439><c> alpacas</c><00:04:14.099><c> and</c><00:04:14.280><c> sheep</c><00:04:14.580><c> it's</c><00:04:14.760><c> giving</c>

00:04:15.050 --> 00:04:15.060 align:start position:0%
between alpacas and sheep it's giving
 

00:04:15.060 --> 00:04:18.710 align:start position:0%
between alpacas and sheep it's giving
good<00:04:15.480><c> coherent</c><00:04:16.139><c> responses</c><00:04:16.799><c> if</c><00:04:17.639><c> I</c><00:04:17.760><c> ask</c><00:04:18.000><c> it</c><00:04:18.239><c> to</c>

00:04:18.710 --> 00:04:18.720 align:start position:0%
good coherent responses if I ask it to
 

00:04:18.720 --> 00:04:21.110 align:start position:0%
good coherent responses if I ask it to
generate<00:04:19.260><c> some</c><00:04:19.620><c> code</c><00:04:19.979><c> you</c><00:04:20.400><c> can</c><00:04:20.519><c> see</c><00:04:20.639><c> that</c><00:04:20.880><c> okay</c>

00:04:21.110 --> 00:04:21.120 align:start position:0%
generate some code you can see that okay
 

00:04:21.120 --> 00:04:22.850 align:start position:0%
generate some code you can see that okay
it's<00:04:21.720><c> got</c><00:04:21.959><c> a</c><00:04:22.199><c> bunch</c><00:04:22.320><c> of</c><00:04:22.440><c> different</c><00:04:22.560><c> things</c>

00:04:22.850 --> 00:04:22.860 align:start position:0%
it's got a bunch of different things
 

00:04:22.860 --> 00:04:25.129 align:start position:0%
it's got a bunch of different things
that<00:04:23.100><c> it</c><00:04:23.220><c> can</c><00:04:23.340><c> look</c><00:04:23.520><c> at</c><00:04:23.639><c> doing</c><00:04:24.000><c> code</c><00:04:24.600><c> now</c>

00:04:25.129 --> 00:04:25.139 align:start position:0%
that it can look at doing code now
 

00:04:25.139 --> 00:04:26.990 align:start position:0%
that it can look at doing code now
sometimes<00:04:25.560><c> they</c><00:04:25.860><c> will</c><00:04:25.979><c> be</c><00:04:26.220><c> correct</c><00:04:26.699><c> and</c>

00:04:26.990 --> 00:04:27.000 align:start position:0%
sometimes they will be correct and
 

00:04:27.000 --> 00:04:28.790 align:start position:0%
sometimes they will be correct and
sometimes<00:04:27.300><c> they</c><00:04:27.600><c> will</c><00:04:27.780><c> not</c><00:04:27.900><c> be</c><00:04:28.080><c> correct</c><00:04:28.440><c> so</c>

00:04:28.790 --> 00:04:28.800 align:start position:0%
sometimes they will not be correct so
 

00:04:28.800 --> 00:04:30.650 align:start position:0%
sometimes they will not be correct so
this<00:04:29.340><c> one</c><00:04:29.460><c> I</c><00:04:29.639><c> think</c><00:04:29.759><c> when</c><00:04:30.000><c> I</c><00:04:30.120><c> did</c><00:04:30.240><c> it</c><00:04:30.419><c> earlier</c>

00:04:30.650 --> 00:04:30.660 align:start position:0%
this one I think when I did it earlier
 

00:04:30.660 --> 00:04:33.530 align:start position:0%
this one I think when I did it earlier
was<00:04:31.080><c> generating</c><00:04:31.680><c> something</c><00:04:32.040><c> that</c><00:04:32.460><c> was</c><00:04:32.759><c> pretty</c>

00:04:33.530 --> 00:04:33.540 align:start position:0%
was generating something that was pretty
 

00:04:33.540 --> 00:04:35.990 align:start position:0%
was generating something that was pretty
good<00:04:33.900><c> whereas</c><00:04:34.500><c> now</c><00:04:34.680><c> it</c><00:04:34.919><c> seems</c><00:04:35.220><c> to</c><00:04:35.340><c> be</c><00:04:35.520><c> maybe</c>

00:04:35.990 --> 00:04:36.000 align:start position:0%
good whereas now it seems to be maybe
 

00:04:36.000 --> 00:04:38.030 align:start position:0%
good whereas now it seems to be maybe
not<00:04:36.300><c> as</c><00:04:36.540><c> good</c><00:04:36.780><c> and</c><00:04:37.080><c> you</c><00:04:37.320><c> can</c><00:04:37.440><c> see</c><00:04:37.620><c> I've</c><00:04:37.740><c> asked</c>

00:04:38.030 --> 00:04:38.040 align:start position:0%
not as good and you can see I've asked
 

00:04:38.040 --> 00:04:40.189 align:start position:0%
not as good and you can see I've asked
it<00:04:38.100><c> okay</c><00:04:38.280><c> write</c><00:04:38.460><c> an</c><00:04:38.639><c> email</c><00:04:38.820><c> explaining</c><00:04:39.479><c> uh</c>

00:04:40.189 --> 00:04:40.199 align:start position:0%
it okay write an email explaining uh
 

00:04:40.199 --> 00:04:43.670 align:start position:0%
it okay write an email explaining uh
should<00:04:40.500><c> be</c><00:04:40.740><c> why</c><00:04:41.400><c> gpt4</c><00:04:42.419><c> should</c><00:04:42.780><c> be</c><00:04:42.960><c> open</c><00:04:43.139><c> source</c>

00:04:43.670 --> 00:04:43.680 align:start position:0%
should be why gpt4 should be open source
 

00:04:43.680 --> 00:04:45.409 align:start position:0%
should be why gpt4 should be open source
okay<00:04:43.979><c> and</c><00:04:44.400><c> we'll</c><00:04:44.580><c> see</c><00:04:44.759><c> little</c><00:04:44.940><c> things</c><00:04:45.240><c> that</c>

00:04:45.409 --> 00:04:45.419 align:start position:0%
okay and we'll see little things that
 

00:04:45.419 --> 00:04:47.870 align:start position:0%
okay and we'll see little things that
gpt's<00:04:46.020><c> for</c><00:04:46.320><c> us</c><00:04:46.500><c> from</c><00:04:46.800><c> Google</c><00:04:47.040><c> let's</c><00:04:47.520><c> just</c><00:04:47.699><c> run</c>

00:04:47.870 --> 00:04:47.880 align:start position:0%
gpt's for us from Google let's just run
 

00:04:47.880 --> 00:04:49.249 align:start position:0%
gpt's for us from Google let's just run
this<00:04:48.060><c> one</c><00:04:48.180><c> again</c><00:04:48.360><c> and</c><00:04:48.600><c> see</c><00:04:48.720><c> if</c><00:04:48.840><c> we</c><00:04:48.900><c> can</c><00:04:49.020><c> get</c><00:04:49.139><c> a</c>

00:04:49.249 --> 00:04:49.259 align:start position:0%
this one again and see if we can get a
 

00:04:49.259 --> 00:04:51.110 align:start position:0%
this one again and see if we can get a
better<00:04:49.320><c> response</c>

00:04:51.110 --> 00:04:51.120 align:start position:0%
better response
 

00:04:51.120 --> 00:04:53.689 align:start position:0%
better response
now<00:04:51.780><c> you'll</c><00:04:52.139><c> find</c><00:04:52.320><c> that</c><00:04:52.680><c> the</c><00:04:53.160><c> generation</c>

00:04:53.689 --> 00:04:53.699 align:start position:0%
now you'll find that the generation
 

00:04:53.699 --> 00:04:55.490 align:start position:0%
now you'll find that the generation
speed<00:04:54.120><c> is</c><00:04:54.600><c> actually</c><00:04:54.720><c> going</c><00:04:54.960><c> to</c><00:04:55.139><c> be</c><00:04:55.199><c> pretty</c>

00:04:55.490 --> 00:04:55.500 align:start position:0%
speed is actually going to be pretty
 

00:04:55.500 --> 00:04:57.409 align:start position:0%
speed is actually going to be pretty
slow<00:04:55.919><c> on</c><00:04:56.460><c> this</c><00:04:56.639><c> model</c>

00:04:57.409 --> 00:04:57.419 align:start position:0%
slow on this model
 

00:04:57.419 --> 00:04:59.870 align:start position:0%
slow on this model
and<00:04:57.960><c> you</c><00:04:58.080><c> will</c><00:04:58.199><c> need</c><00:04:58.380><c> a</c><00:04:58.620><c> pretty</c><00:04:58.740><c> beefy</c><00:04:59.220><c> GPU</c>

00:04:59.870 --> 00:04:59.880 align:start position:0%
and you will need a pretty beefy GPU
 

00:04:59.880 --> 00:05:01.670 align:start position:0%
and you will need a pretty beefy GPU
just<00:05:00.180><c> to</c><00:05:00.300><c> be</c><00:05:00.419><c> able</c><00:05:00.540><c> to</c><00:05:00.720><c> get</c><00:05:00.840><c> this</c><00:05:01.080><c> running</c><00:05:01.320><c> and</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
just to be able to get this running and
 

00:05:01.680 --> 00:05:03.950 align:start position:0%
just to be able to get this running and
get<00:05:01.800><c> it</c><00:05:01.979><c> working</c><00:05:02.160><c> okay</c><00:05:02.880><c> so</c><00:05:03.300><c> here's</c><00:05:03.660><c> our</c><00:05:03.780><c> result</c>

00:05:03.950 --> 00:05:03.960 align:start position:0%
get it working okay so here's our result
 

00:05:03.960 --> 00:05:05.870 align:start position:0%
get it working okay so here's our result
write<00:05:04.320><c> an</c><00:05:04.500><c> email</c><00:05:04.620><c> explaining</c><00:05:05.100><c> why</c><00:05:05.280><c> gpt4</c>

00:05:05.870 --> 00:05:05.880 align:start position:0%
write an email explaining why gpt4
 

00:05:05.880 --> 00:05:08.390 align:start position:0%
write an email explaining why gpt4
should<00:05:06.240><c> be</c><00:05:06.360><c> open</c><00:05:06.540><c> source</c><00:05:07.080><c> so</c><00:05:07.500><c> gpt4</c><00:05:08.100><c> has</c><00:05:08.280><c> been</c>

00:05:08.390 --> 00:05:08.400 align:start position:0%
should be open source so gpt4 has been
 

00:05:08.400 --> 00:05:10.129 align:start position:0%
should be open source so gpt4 has been
developed<00:05:08.759><c> by</c><00:05:08.880><c> Google</c><00:05:09.120><c> and</c><00:05:09.600><c> it's</c><00:05:09.720><c> important</c>

00:05:10.129 --> 00:05:10.139 align:start position:0%
developed by Google and it's important
 

00:05:10.139 --> 00:05:11.749 align:start position:0%
developed by Google and it's important
to<00:05:10.440><c> make</c><00:05:10.620><c> sure</c><00:05:10.740><c> its</c><00:05:11.100><c> algorithms</c><00:05:11.580><c> are</c>

00:05:11.749 --> 00:05:11.759 align:start position:0%
to make sure its algorithms are
 

00:05:11.759 --> 00:05:13.249 align:start position:0%
to make sure its algorithms are
accessible<00:05:12.120><c> to</c><00:05:12.360><c> everyone</c><00:05:12.540><c> including</c>

00:05:13.249 --> 00:05:13.259 align:start position:0%
accessible to everyone including
 

00:05:13.259 --> 00:05:15.469 align:start position:0%
accessible to everyone including
researchers<00:05:13.860><c> who</c><00:05:14.160><c> want</c><00:05:14.280><c> to</c><00:05:14.400><c> study</c><00:05:14.699><c> them</c><00:05:14.940><c> so</c>

00:05:15.469 --> 00:05:15.479 align:start position:0%
researchers who want to study them so
 

00:05:15.479 --> 00:05:16.969 align:start position:0%
researchers who want to study them so
you<00:05:15.540><c> can</c><00:05:15.660><c> see</c><00:05:15.780><c> that</c><00:05:15.900><c> it's</c><00:05:16.020><c> very</c><00:05:16.320><c> plausible</c>

00:05:16.969 --> 00:05:16.979 align:start position:0%
you can see that it's very plausible
 

00:05:16.979 --> 00:05:19.730 align:start position:0%
you can see that it's very plausible
responses<00:05:17.880><c> that</c><00:05:18.180><c> we're</c><00:05:18.300><c> getting</c><00:05:18.479><c> here</c><00:05:18.840><c> and</c><00:05:19.440><c> it</c>

00:05:19.730 --> 00:05:19.740 align:start position:0%
responses that we're getting here and it
 

00:05:19.740 --> 00:05:22.370 align:start position:0%
responses that we're getting here and it
is<00:05:19.979><c> good</c><00:05:20.280><c> in</c><00:05:20.580><c> many</c><00:05:20.759><c> ways</c><00:05:21.240><c> in</c><00:05:21.660><c> that</c><00:05:21.900><c> we</c><00:05:22.259><c> wouldn't</c>

00:05:22.370 --> 00:05:22.380 align:start position:0%
is good in many ways in that we wouldn't
 

00:05:22.380 --> 00:05:24.830 align:start position:0%
is good in many ways in that we wouldn't
expect<00:05:22.680><c> it</c><00:05:22.979><c> to</c><00:05:23.220><c> know</c><00:05:23.460><c> necessarily</c><00:05:24.000><c> that</c><00:05:24.180><c> gpt4</c>

00:05:24.830 --> 00:05:24.840 align:start position:0%
expect it to know necessarily that gpt4
 

00:05:24.840 --> 00:05:27.610 align:start position:0%
expect it to know necessarily that gpt4
is<00:05:25.080><c> streamed</c><00:05:25.500><c> by</c><00:05:25.560><c> open</c><00:05:25.740><c> AI</c><00:05:26.280><c> although</c><00:05:26.940><c> you</c>

00:05:27.610 --> 00:05:27.620 align:start position:0%
is streamed by open AI although you
 

00:05:27.620 --> 00:05:30.350 align:start position:0%
is streamed by open AI although you
possibly<00:05:28.620><c> you</c><00:05:28.800><c> could</c><00:05:29.039><c> anyway</c><00:05:29.520><c> obviously</c><00:05:30.120><c> the</c>

00:05:30.350 --> 00:05:30.360 align:start position:0%
possibly you could anyway obviously the
 

00:05:30.360 --> 00:05:32.990 align:start position:0%
possibly you could anyway obviously the
gbtj<00:05:31.139><c> model</c><00:05:31.500><c> was</c><00:05:32.039><c> trained</c><00:05:32.400><c> a</c><00:05:32.580><c> long</c><00:05:32.759><c> time</c>

00:05:32.990 --> 00:05:33.000 align:start position:0%
gbtj model was trained a long time
 

00:05:33.000 --> 00:05:35.930 align:start position:0%
gbtj model was trained a long time
before<00:05:33.620><c> gbt4</c><00:05:34.620><c> came</c><00:05:34.919><c> out</c><00:05:35.100><c> so</c><00:05:35.400><c> there</c><00:05:35.759><c> aren't</c>

00:05:35.930 --> 00:05:35.940 align:start position:0%
before gbt4 came out so there aren't
 

00:05:35.940 --> 00:05:37.969 align:start position:0%
before gbt4 came out so there aren't
facts<00:05:36.360><c> about</c><00:05:36.419><c> that</c><00:05:36.780><c> in</c><00:05:37.020><c> there</c><00:05:37.199><c> but</c><00:05:37.620><c> you</c><00:05:37.860><c> could</c>

00:05:37.969 --> 00:05:37.979 align:start position:0%
facts about that in there but you could
 

00:05:37.979 --> 00:05:40.310 align:start position:0%
facts about that in there but you could
fine<00:05:38.340><c> tune</c><00:05:38.580><c> this</c><00:05:38.880><c> model</c><00:05:39.060><c> for</c><00:05:39.479><c> a</c><00:05:39.660><c> very</c><00:05:39.840><c> specific</c>

00:05:40.310 --> 00:05:40.320 align:start position:0%
fine tune this model for a very specific
 

00:05:40.320 --> 00:05:41.390 align:start position:0%
fine tune this model for a very specific
use

00:05:41.390 --> 00:05:41.400 align:start position:0%
use
 

00:05:41.400 --> 00:05:43.490 align:start position:0%
use
here<00:05:41.880><c> because</c><00:05:42.180><c> you</c><00:05:42.479><c> see</c><00:05:42.660><c> that</c><00:05:42.900><c> what</c><00:05:43.199><c> it</c><00:05:43.380><c> is</c>

00:05:43.490 --> 00:05:43.500 align:start position:0%
here because you see that what it is
 

00:05:43.500 --> 00:05:46.670 align:start position:0%
here because you see that what it is
generating<00:05:43.979><c> is</c><00:05:44.639><c> very</c><00:05:45.000><c> plausible</c><00:05:45.960><c> text</c><00:05:46.259><c> and</c>

00:05:46.670 --> 00:05:46.680 align:start position:0%
generating is very plausible text and
 

00:05:46.680 --> 00:05:47.689 align:start position:0%
generating is very plausible text and
this<00:05:46.800><c> is</c><00:05:46.919><c> one</c><00:05:47.039><c> of</c><00:05:47.160><c> the</c><00:05:47.220><c> things</c><00:05:47.340><c> that</c><00:05:47.580><c> we're</c>

00:05:47.689 --> 00:05:47.699 align:start position:0%
this is one of the things that we're
 

00:05:47.699 --> 00:05:50.330 align:start position:0%
this is one of the things that we're
looking<00:05:47.880><c> for</c><00:05:48.240><c> in</c><00:05:48.900><c> these</c><00:05:49.139><c> models</c><00:05:49.560><c> so</c><00:05:50.039><c> this</c>

00:05:50.330 --> 00:05:50.340 align:start position:0%
looking for in these models so this
 

00:05:50.340 --> 00:05:53.510 align:start position:0%
looking for in these models so this
would<00:05:50.639><c> benefit</c><00:05:50.940><c> from</c><00:05:51.600><c> more</c><00:05:52.080><c> fine-tuning</c><00:05:52.919><c> and</c>

00:05:53.510 --> 00:05:53.520 align:start position:0%
would benefit from more fine-tuning and
 

00:05:53.520 --> 00:05:55.730 align:start position:0%
would benefit from more fine-tuning and
more<00:05:53.759><c> fine-tuning</c><00:05:54.360><c> on</c><00:05:54.479><c> a</c><00:05:54.660><c> very</c><00:05:54.840><c> specific</c><00:05:55.199><c> data</c>

00:05:55.730 --> 00:05:55.740 align:start position:0%
more fine-tuning on a very specific data
 

00:05:55.740 --> 00:05:59.689 align:start position:0%
more fine-tuning on a very specific data
set<00:05:55.860><c> for</c><00:05:56.520><c> a</c><00:05:56.699><c> very</c><00:05:56.880><c> specific</c><00:05:57.300><c> use</c><00:05:57.860><c> so</c><00:05:58.860><c> anyway</c><00:05:59.400><c> I</c>

00:05:59.689 --> 00:05:59.699 align:start position:0%
set for a very specific use so anyway I
 

00:05:59.699 --> 00:06:01.909 align:start position:0%
set for a very specific use so anyway I
just<00:05:59.880><c> made</c><00:06:00.060><c> this</c><00:06:00.300><c> quick</c><00:06:00.600><c> video</c><00:06:00.960><c> so</c><00:06:01.500><c> that</c><00:06:01.680><c> you</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
just made this quick video so that you
 

00:06:01.919 --> 00:06:04.189 align:start position:0%
just made this quick video so that you
could<00:06:02.160><c> see</c><00:06:02.639><c> how</c><00:06:02.940><c> to</c><00:06:03.000><c> do</c><00:06:03.180><c> inference</c><00:06:03.720><c> with</c><00:06:04.020><c> this</c>

00:06:04.189 --> 00:06:04.199 align:start position:0%
could see how to do inference with this
 

00:06:04.199 --> 00:06:06.230 align:start position:0%
could see how to do inference with this
The<00:06:04.500><c> Notebook</c><00:06:04.860><c> will</c><00:06:05.280><c> be</c><00:06:05.520><c> in</c><00:06:05.880><c> the</c><00:06:05.940><c> description</c>

00:06:06.230 --> 00:06:06.240 align:start position:0%
The Notebook will be in the description
 

00:06:06.240 --> 00:06:09.409 align:start position:0%
The Notebook will be in the description
as<00:06:06.840><c> always</c><00:06:07.280><c> this</c><00:06:08.280><c> is</c><00:06:08.400><c> Dolly</c><00:06:08.880><c> if</c><00:06:09.060><c> you've</c><00:06:09.240><c> seen</c>

00:06:09.409 --> 00:06:09.419 align:start position:0%
as always this is Dolly if you've seen
 

00:06:09.419 --> 00:06:11.210 align:start position:0%
as always this is Dolly if you've seen
people<00:06:09.600><c> talking</c><00:06:09.900><c> about</c><00:06:10.139><c> it</c><00:06:10.380><c> on</c><00:06:10.680><c> Twitter</c><00:06:10.800><c> have</c>

00:06:11.210 --> 00:06:11.220 align:start position:0%
people talking about it on Twitter have
 

00:06:11.220 --> 00:06:13.249 align:start position:0%
people talking about it on Twitter have
you<00:06:11.400><c> seen</c><00:06:11.580><c> people</c><00:06:11.880><c> twirling</c><00:06:12.300><c> around</c><00:06:12.479><c> you</c><00:06:13.139><c> can</c>

00:06:13.249 --> 00:06:13.259 align:start position:0%
you seen people twirling around you can
 

00:06:13.259 --> 00:06:15.050 align:start position:0%
you seen people twirling around you can
have<00:06:13.380><c> a</c><00:06:13.500><c> play</c><00:06:13.560><c> with</c><00:06:13.740><c> it</c><00:06:13.919><c> yourself</c><00:06:14.100><c> and</c><00:06:14.699><c> try</c><00:06:14.880><c> it</c>

00:06:15.050 --> 00:06:15.060 align:start position:0%
have a play with it yourself and try it
 

00:06:15.060 --> 00:06:18.170 align:start position:0%
have a play with it yourself and try it
out<00:06:15.180><c> and</c><00:06:15.900><c> compare</c><00:06:16.199><c> it</c><00:06:16.440><c> yourself</c><00:06:16.680><c> to</c><00:06:17.639><c> the</c>

00:06:18.170 --> 00:06:18.180 align:start position:0%
out and compare it yourself to the
 

00:06:18.180 --> 00:06:20.270 align:start position:0%
out and compare it yourself to the
alpaca<00:06:18.960><c> model</c><00:06:19.199><c> and</c><00:06:19.620><c> to</c><00:06:19.800><c> some</c><00:06:19.919><c> of</c><00:06:20.039><c> the</c><00:06:20.160><c> other</c>

00:06:20.270 --> 00:06:20.280 align:start position:0%
alpaca model and to some of the other
 

00:06:20.280 --> 00:06:22.490 align:start position:0%
alpaca model and to some of the other
models<00:06:20.639><c> that</c><00:06:20.820><c> we've</c><00:06:20.940><c> looked</c><00:06:21.300><c> at</c><00:06:21.360><c> in</c><00:06:22.199><c> the</c><00:06:22.319><c> next</c>

00:06:22.490 --> 00:06:22.500 align:start position:0%
models that we've looked at in the next
 

00:06:22.500 --> 00:06:24.529 align:start position:0%
models that we've looked at in the next
few<00:06:22.800><c> videos</c><00:06:23.100><c> later</c><00:06:23.699><c> this</c><00:06:23.880><c> week</c><00:06:24.060><c> I'm</c><00:06:24.300><c> going</c><00:06:24.419><c> to</c>

00:06:24.529 --> 00:06:24.539 align:start position:0%
few videos later this week I'm going to
 

00:06:24.539 --> 00:06:26.330 align:start position:0%
few videos later this week I'm going to
be<00:06:24.600><c> looking</c><00:06:24.780><c> at</c><00:06:24.960><c> some</c><00:06:25.199><c> other</c><00:06:25.380><c> models</c><00:06:25.860><c> too</c><00:06:25.979><c> that</c>

00:06:26.330 --> 00:06:26.340 align:start position:0%
be looking at some other models too that
 

00:06:26.340 --> 00:06:28.010 align:start position:0%
be looking at some other models too that
I<00:06:26.400><c> think</c><00:06:26.520><c> are</c><00:06:26.699><c> really</c><00:06:27.000><c> interesting</c><00:06:27.479><c> as</c>

00:06:28.010 --> 00:06:28.020 align:start position:0%
I think are really interesting as
 

00:06:28.020 --> 00:06:30.710 align:start position:0%
I think are really interesting as
alternatives<00:06:28.800><c> to</c><00:06:29.639><c> llama</c><00:06:30.000><c> that</c><00:06:30.479><c> are</c><00:06:30.600><c> coming</c>

00:06:30.710 --> 00:06:30.720 align:start position:0%
alternatives to llama that are coming
 

00:06:30.720 --> 00:06:33.110 align:start position:0%
alternatives to llama that are coming
out<00:06:30.960><c> as</c><00:06:31.319><c> well</c><00:06:31.500><c> so</c><00:06:32.280><c> this</c><00:06:32.520><c> is</c><00:06:32.639><c> something</c><00:06:32.819><c> that</c>

00:06:33.110 --> 00:06:33.120 align:start position:0%
out as well so this is something that
 

00:06:33.120 --> 00:06:35.809 align:start position:0%
out as well so this is something that
will<00:06:33.240><c> be</c><00:06:33.419><c> very</c><00:06:33.600><c> interesting</c><00:06:34.560><c> as</c><00:06:35.160><c> always</c><00:06:35.340><c> if</c>

00:06:35.809 --> 00:06:35.819 align:start position:0%
will be very interesting as always if
 

00:06:35.819 --> 00:06:37.909 align:start position:0%
will be very interesting as always if
this<00:06:36.060><c> was</c><00:06:36.240><c> useful</c><00:06:36.539><c> to</c><00:06:36.780><c> you</c><00:06:36.960><c> please</c><00:06:37.440><c> click</c><00:06:37.800><c> and</c>

00:06:37.909 --> 00:06:37.919 align:start position:0%
this was useful to you please click and
 

00:06:37.919 --> 00:06:39.710 align:start position:0%
this was useful to you please click and
subscribe<00:06:38.460><c> if</c><00:06:38.880><c> you</c><00:06:39.000><c> have</c><00:06:39.120><c> any</c><00:06:39.419><c> questions</c>

00:06:39.710 --> 00:06:39.720 align:start position:0%
subscribe if you have any questions
 

00:06:39.720 --> 00:06:41.689 align:start position:0%
subscribe if you have any questions
please<00:06:40.380><c> put</c><00:06:40.680><c> them</c><00:06:40.800><c> in</c><00:06:40.919><c> the</c><00:06:41.100><c> comments</c><00:06:41.400><c> below</c>

00:06:41.689 --> 00:06:41.699 align:start position:0%
please put them in the comments below
 

00:06:41.699 --> 00:06:43.430 align:start position:0%
please put them in the comments below
otherwise<00:06:42.360><c> I</c><00:06:42.600><c> will</c><00:06:42.780><c> see</c><00:06:42.900><c> you</c><00:06:43.020><c> in</c><00:06:43.139><c> the</c><00:06:43.259><c> next</c>

00:06:43.430 --> 00:06:43.440 align:start position:0%
otherwise I will see you in the next
 

00:06:43.440 --> 00:06:47.780 align:start position:0%
otherwise I will see you in the next
video<00:06:43.680><c> thank</c><00:06:44.520><c> you</c><00:06:44.639><c> bye</c><00:06:45.479><c> for</c><00:06:45.600><c> now</c>

