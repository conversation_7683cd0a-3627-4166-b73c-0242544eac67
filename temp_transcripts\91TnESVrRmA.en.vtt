WEBVTT
Kind: captions
Language: en

00:00:00.500 --> 00:00:03.830 align:start position:0%
 
hi<00:00:01.500><c> Matt</c><00:00:02.190><c> <PERSON></c><00:00:02.550><c> techno</c><00:00:03.120><c> evangelist</c><00:00:03.600><c> here</c>

00:00:03.830 --> 00:00:03.840 align:start position:0%
hi <PERSON> techno evangelist here
 

00:00:03.840 --> 00:00:07.130 align:start position:0%
hi Matt <PERSON> techno evangelist here
last<00:00:04.529><c> week</c><00:00:04.740><c> I</c><00:00:04.920><c> spoke</c><00:00:05.310><c> at</c><00:00:05.580><c> the</c><00:00:05.879><c> AWS</c><00:00:06.569><c> reinvent</c>

00:00:07.130 --> 00:00:07.140 align:start position:0%
last week I spoke at the AWS reinvent
 

00:00:07.140 --> 00:00:09.500 align:start position:0%
last week I spoke at the AWS reinvent
conference<00:00:07.620><c> in</c><00:00:07.770><c> Las</c><00:00:08.040><c> Vegas</c><00:00:08.250><c> I</c><00:00:08.639><c> was</c><00:00:09.150><c> on</c><00:00:09.300><c> stage</c>

00:00:09.500 --> 00:00:09.510 align:start position:0%
conference in Las Vegas I was on stage
 

00:00:09.510 --> 00:00:12.669 align:start position:0%
conference in Las Vegas I was on stage
with<00:00:09.570><c> Chris</c><00:00:10.139><c> Fuchs</c><00:00:10.410><c> from</c><00:00:10.800><c> Trek</c><00:00:11.400><c> 10</c><00:00:11.639><c> all</c><00:00:12.210><c> right</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
with Chris Fuchs from Trek 10 all right
 

00:00:12.679 --> 00:00:18.380 align:start position:0%
with Chris Fuchs from Trek 10 all right
so<00:00:13.679><c> you</c><00:00:13.769><c> are</c><00:00:14.250><c> all</c><00:00:14.549><c> in</c><00:00:14.940><c> the</c><00:00:15.059><c> room</c><00:00:15.240><c> for</c><00:00:16.250><c> SRV</c><00:00:17.250><c> 304</c><00:00:18.150><c> s</c>

00:00:18.380 --> 00:00:18.390 align:start position:0%
so you are all in the room for SRV 304 s
 

00:00:18.390 --> 00:00:21.620 align:start position:0%
so you are all in the room for SRV 304 s
how<00:00:19.109><c> Trek</c><00:00:19.529><c> 10</c><00:00:19.859><c> uses</c><00:00:20.279><c> data</c><00:00:20.609><c> dogs</c><00:00:20.939><c> distributed</c>

00:00:21.620 --> 00:00:21.630 align:start position:0%
how Trek 10 uses data dogs distributed
 

00:00:21.630 --> 00:00:24.950 align:start position:0%
how Trek 10 uses data dogs distributed
tracing<00:00:22.140><c> and</c><00:00:22.350><c> other</c><00:00:22.859><c> tools</c><00:00:23.130><c> to</c><00:00:23.340><c> improve</c><00:00:24.150><c> AWS</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
tracing and other tools to improve AWS
 

00:00:24.960 --> 00:00:26.960 align:start position:0%
tracing and other tools to improve AWS
lambda<00:00:25.289><c> projects</c><00:00:25.769><c> my</c><00:00:26.220><c> name</c><00:00:26.369><c> is</c><00:00:26.460><c> Matt</c><00:00:26.640><c> Williams</c>

00:00:26.960 --> 00:00:26.970 align:start position:0%
lambda projects my name is Matt Williams
 

00:00:26.970 --> 00:00:30.320 align:start position:0%
lambda projects my name is Matt Williams
and<00:00:27.180><c> this</c><00:00:27.960><c> is</c><00:00:28.019><c> okay</c><00:00:28.920><c> your</c><00:00:29.039><c> name</c><00:00:29.130><c> Chris</c><00:00:29.880><c> Fuchs</c>

00:00:30.320 --> 00:00:30.330 align:start position:0%
and this is okay your name Chris Fuchs
 

00:00:30.330 --> 00:00:33.170 align:start position:0%
and this is okay your name Chris Fuchs
Thank<00:00:30.869><c> You</c><00:00:30.960><c> Matt</c><00:00:31.140><c> and</c><00:00:31.850><c> I'll</c><00:00:32.850><c> let</c><00:00:33.030><c> you</c>

00:00:33.170 --> 00:00:33.180 align:start position:0%
Thank You Matt and I'll let you
 

00:00:33.180 --> 00:00:34.910 align:start position:0%
Thank You Matt and I'll let you
introduce<00:00:33.450><c> yourself</c><00:00:34.079><c> sure</c>

00:00:34.910 --> 00:00:34.920 align:start position:0%
introduce yourself sure
 

00:00:34.920 --> 00:00:37.100 align:start position:0%
introduce yourself sure
well<00:00:35.309><c> I</c><00:00:35.640><c> created</c><00:00:36.329><c> a</c><00:00:36.480><c> demo</c><00:00:36.690><c> for</c><00:00:36.989><c> the</c>

00:00:37.100 --> 00:00:37.110 align:start position:0%
well I created a demo for the
 

00:00:37.110 --> 00:00:38.959 align:start position:0%
well I created a demo for the
presentation<00:00:37.710><c> which</c><00:00:37.950><c> would</c><00:00:38.250><c> allow</c><00:00:38.760><c> me</c><00:00:38.790><c> to</c>

00:00:38.959 --> 00:00:38.969 align:start position:0%
presentation which would allow me to
 

00:00:38.969 --> 00:00:41.090 align:start position:0%
presentation which would allow me to
show<00:00:39.270><c> a</c><00:00:39.300><c> few</c><00:00:39.629><c> things</c><00:00:39.840><c> that</c><00:00:40.050><c> I</c><00:00:40.140><c> find</c><00:00:40.350><c> really</c>

00:00:41.090 --> 00:00:41.100 align:start position:0%
show a few things that I find really
 

00:00:41.100 --> 00:00:43.700 align:start position:0%
show a few things that I find really
special<00:00:41.640><c> with</c><00:00:41.940><c> x-ray</c><00:00:42.360><c> and</c><00:00:42.600><c> we</c><00:00:43.320><c> had</c><00:00:43.440><c> planned</c>

00:00:43.700 --> 00:00:43.710 align:start position:0%
special with x-ray and we had planned
 

00:00:43.710 --> 00:00:45.920 align:start position:0%
special with x-ray and we had planned
for<00:00:43.829><c> a</c><00:00:43.980><c> few</c><00:00:44.250><c> errors</c><00:00:44.520><c> to</c><00:00:44.940><c> happen</c><00:00:45.300><c> during</c><00:00:45.840><c> the</c>

00:00:45.920 --> 00:00:45.930 align:start position:0%
for a few errors to happen during the
 

00:00:45.930 --> 00:00:47.869 align:start position:0%
for a few errors to happen during the
demo<00:00:46.200><c> and</c><00:00:46.410><c> guess</c><00:00:46.800><c> what</c><00:00:46.980><c> we</c><00:00:47.430><c> saw</c><00:00:47.610><c> a</c><00:00:47.640><c> few</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
demo and guess what we saw a few
 

00:00:47.879 --> 00:00:50.540 align:start position:0%
demo and guess what we saw a few
problems<00:00:48.030><c> in</c><00:00:48.329><c> the</c><00:00:48.450><c> demo</c><00:00:48.719><c> but</c><00:00:49.250><c> they</c><00:00:50.250><c> weren't</c>

00:00:50.540 --> 00:00:50.550 align:start position:0%
problems in the demo but they weren't
 

00:00:50.550 --> 00:00:53.330 align:start position:0%
problems in the demo but they weren't
quite<00:00:50.730><c> the</c><00:00:50.940><c> problems</c><00:00:51.390><c> we</c><00:00:51.899><c> plan</c><00:00:52.230><c> to</c><00:00:52.440><c> see</c><00:00:52.620><c> they</c>

00:00:53.330 --> 00:00:53.340 align:start position:0%
quite the problems we plan to see they
 

00:00:53.340 --> 00:00:55.310 align:start position:0%
quite the problems we plan to see they
were<00:00:53.399><c> different</c><00:00:53.789><c> problems</c><00:00:54.149><c> so</c><00:00:54.930><c> in</c><00:00:55.050><c> this</c><00:00:55.170><c> video</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
were different problems so in this video
 

00:00:55.320 --> 00:00:58.099 align:start position:0%
were different problems so in this video
I<00:00:55.739><c> want</c><00:00:56.010><c> to</c><00:00:56.039><c> show</c><00:00:56.309><c> you</c><00:00:56.370><c> what</c><00:00:57.120><c> I</c><00:00:57.329><c> did</c><00:00:57.510><c> wrong</c><00:00:57.780><c> and</c>

00:00:58.099 --> 00:00:58.109 align:start position:0%
I want to show you what I did wrong and
 

00:00:58.109 --> 00:01:00.529 align:start position:0%
I want to show you what I did wrong and
how<00:00:58.199><c> I</c><00:00:58.649><c> fixed</c><00:00:58.980><c> it</c><00:00:59.070><c> now</c><00:00:59.850><c> if</c><00:00:59.969><c> you</c><00:01:00.000><c> like</c><00:01:00.090><c> videos</c>

00:01:00.529 --> 00:01:00.539 align:start position:0%
how I fixed it now if you like videos
 

00:01:00.539 --> 00:01:02.060 align:start position:0%
how I fixed it now if you like videos
like<00:01:00.690><c> this</c><00:01:00.840><c> consider</c><00:01:01.230><c> hitting</c><00:01:01.410><c> the</c><00:01:01.710><c> subscribe</c>

00:01:02.060 --> 00:01:02.070 align:start position:0%
like this consider hitting the subscribe
 

00:01:02.070 --> 00:01:04.250 align:start position:0%
like this consider hitting the subscribe
button<00:01:02.309><c> below</c><00:01:02.820><c> and</c><00:01:03.270><c> if</c><00:01:03.870><c> you</c><00:01:03.960><c> have</c><00:01:04.110><c> any</c>

00:01:04.250 --> 00:01:04.260 align:start position:0%
button below and if you have any
 

00:01:04.260 --> 00:01:05.810 align:start position:0%
button below and if you have any
experience<00:01:04.680><c> with</c><00:01:04.739><c> lambda</c><00:01:05.040><c> and</c><00:01:05.309><c> notice</c>

00:01:05.810 --> 00:01:05.820 align:start position:0%
experience with lambda and notice
 

00:01:05.820 --> 00:01:06.980 align:start position:0%
experience with lambda and notice
something<00:01:06.119><c> else</c><00:01:06.210><c> I</c><00:01:06.420><c> should</c><00:01:06.479><c> have</c><00:01:06.689><c> changed</c>

00:01:06.980 --> 00:01:06.990 align:start position:0%
something else I should have changed
 

00:01:06.990 --> 00:01:09.080 align:start position:0%
something else I should have changed
share<00:01:07.680><c> it</c><00:01:07.770><c> with</c><00:01:07.860><c> everyone</c><00:01:08.130><c> in</c><00:01:08.490><c> the</c><00:01:08.670><c> comment</c>

00:01:09.080 --> 00:01:09.090 align:start position:0%
share it with everyone in the comment
 

00:01:09.090 --> 00:01:11.179 align:start position:0%
share it with everyone in the comment
section<00:01:09.409><c> let</c><00:01:10.409><c> me</c><00:01:10.439><c> start</c><00:01:10.740><c> by</c><00:01:10.770><c> telling</c><00:01:11.040><c> you</c>

00:01:11.179 --> 00:01:11.189 align:start position:0%
section let me start by telling you
 

00:01:11.189 --> 00:01:13.520 align:start position:0%
section let me start by telling you
about<00:01:11.460><c> the</c><00:01:11.850><c> demo</c><00:01:12.150><c> on</c><00:01:12.689><c> the</c><00:01:12.869><c> first</c><00:01:13.080><c> slide</c><00:01:13.170><c> after</c>

00:01:13.520 --> 00:01:13.530 align:start position:0%
about the demo on the first slide after
 

00:01:13.530 --> 00:01:15.710 align:start position:0%
about the demo on the first slide after
we<00:01:13.770><c> introduced</c><00:01:14.220><c> ourselves</c><00:01:14.400><c> I</c><00:01:14.820><c> told</c><00:01:15.360><c> everyone</c>

00:01:15.710 --> 00:01:15.720 align:start position:0%
we introduced ourselves I told everyone
 

00:01:15.720 --> 00:01:17.840 align:start position:0%
we introduced ourselves I told everyone
that<00:01:15.869><c> I</c><00:01:16.080><c> was</c><00:01:16.259><c> making</c><00:01:16.619><c> a</c><00:01:16.770><c> few</c><00:01:17.009><c> assumptions</c><00:01:17.490><c> and</c>

00:01:17.840 --> 00:01:17.850 align:start position:0%
that I was making a few assumptions and
 

00:01:17.850 --> 00:01:20.090 align:start position:0%
that I was making a few assumptions and
then<00:01:18.509><c> I</c><00:01:18.540><c> wanted</c><00:01:18.869><c> to</c><00:01:18.990><c> know</c><00:01:19.170><c> if</c><00:01:19.380><c> the</c><00:01:19.530><c> folks</c><00:01:19.770><c> in</c>

00:01:20.090 --> 00:01:20.100 align:start position:0%
then I wanted to know if the folks in
 

00:01:20.100 --> 00:01:21.830 align:start position:0%
then I wanted to know if the folks in
the<00:01:20.220><c> audience</c><00:01:20.340><c> were</c><00:01:20.939><c> using</c><00:01:21.330><c> lambda</c><00:01:21.689><c> in</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
the audience were using lambda in
 

00:01:21.840 --> 00:01:24.260 align:start position:0%
the audience were using lambda in
production<00:01:22.259><c> so</c><00:01:23.009><c> I</c><00:01:23.040><c> gave</c><00:01:23.280><c> them</c><00:01:23.460><c> a</c><00:01:23.580><c> URL</c><00:01:23.759><c> for</c><00:01:24.240><c> a</c>

00:01:24.260 --> 00:01:24.270 align:start position:0%
production so I gave them a URL for a
 

00:01:24.270 --> 00:01:26.660 align:start position:0%
production so I gave them a URL for a
single<00:01:24.720><c> question</c><00:01:24.960><c> survey</c><00:01:25.500><c> are</c><00:01:26.159><c> you</c><00:01:26.369><c> using</c>

00:01:26.660 --> 00:01:26.670 align:start position:0%
single question survey are you using
 

00:01:26.670 --> 00:01:29.510 align:start position:0%
single question survey are you using
lambda<00:01:27.030><c> in</c><00:01:27.360><c> production</c><00:01:28.170><c> the</c><00:01:28.860><c> first</c><00:01:29.040><c> few</c><00:01:29.280><c> folks</c>

00:01:29.510 --> 00:01:29.520 align:start position:0%
lambda in production the first few folks
 

00:01:29.520 --> 00:01:31.039 align:start position:0%
lambda in production the first few folks
that<00:01:29.640><c> answered</c><00:01:30.090><c> the</c><00:01:30.150><c> question</c><00:01:30.270><c> got</c><00:01:30.900><c> the</c>

00:01:31.039 --> 00:01:31.049 align:start position:0%
that answered the question got the
 

00:01:31.049 --> 00:01:32.270 align:start position:0%
that answered the question got the
response<00:01:31.439><c> I</c><00:01:31.710><c> expected</c>

00:01:32.270 --> 00:01:32.280 align:start position:0%
response I expected
 

00:01:32.280 --> 00:01:35.240 align:start position:0%
response I expected
they<00:01:32.880><c> chose</c><00:01:33.150><c> yes</c><00:01:33.509><c> or</c><00:01:33.810><c> no</c><00:01:34.079><c> click</c><00:01:34.650><c> Submit</c><00:01:35.070><c> and</c>

00:01:35.240 --> 00:01:35.250 align:start position:0%
they chose yes or no click Submit and
 

00:01:35.250 --> 00:01:37.850 align:start position:0%
they chose yes or no click Submit and
then<00:01:35.729><c> within</c><00:01:35.939><c> a</c><00:01:36.150><c> second</c><00:01:36.509><c> got</c><00:01:36.780><c> a</c><00:01:36.960><c> pie</c><00:01:37.500><c> chart</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
then within a second got a pie chart
 

00:01:37.860 --> 00:01:39.940 align:start position:0%
then within a second got a pie chart
showing<00:01:38.460><c> the</c><00:01:38.579><c> percentage</c><00:01:39.119><c> of</c><00:01:39.270><c> each</c><00:01:39.600><c> answer</c>

00:01:39.940 --> 00:01:39.950 align:start position:0%
showing the percentage of each answer
 

00:01:39.950 --> 00:01:43.760 align:start position:0%
showing the percentage of each answer
unfortunately<00:01:41.210><c> most</c><00:01:42.210><c> folks</c><00:01:42.509><c> got</c><00:01:42.810><c> no</c><00:01:43.470><c> feedback</c>

00:01:43.760 --> 00:01:43.770 align:start position:0%
unfortunately most folks got no feedback
 

00:01:43.770 --> 00:01:45.920 align:start position:0%
unfortunately most folks got no feedback
so<00:01:44.579><c> they</c><00:01:44.700><c> kept</c><00:01:44.880><c> jamming</c><00:01:45.299><c> on</c><00:01:45.390><c> the</c><00:01:45.420><c> button</c><00:01:45.780><c> to</c>

00:01:45.920 --> 00:01:45.930 align:start position:0%
so they kept jamming on the button to
 

00:01:45.930 --> 00:01:48.319 align:start position:0%
so they kept jamming on the button to
get<00:01:46.140><c> their</c><00:01:46.409><c> response</c><00:01:46.829><c> heard</c><00:01:47.159><c> I</c><00:01:47.369><c> was</c><00:01:48.000><c> getting</c>

00:01:48.319 --> 00:01:48.329 align:start position:0%
get their response heard I was getting
 

00:01:48.329 --> 00:01:49.700 align:start position:0%
get their response heard I was getting
all<00:01:48.479><c> their</c><00:01:48.659><c> responses</c><00:01:49.200><c> but</c><00:01:49.409><c> I</c><00:01:49.439><c> wasn't</c>

00:01:49.700 --> 00:01:49.710 align:start position:0%
all their responses but I wasn't
 

00:01:49.710 --> 00:01:52.100 align:start position:0%
all their responses but I wasn't
updating<00:01:50.340><c> the</c><00:01:50.460><c> chart</c><00:01:50.759><c> to</c><00:01:51.149><c> show</c><00:01:51.180><c> them</c><00:01:51.600><c> that</c><00:01:51.840><c> I</c>

00:01:52.100 --> 00:01:52.110 align:start position:0%
updating the chart to show them that I
 

00:01:52.110 --> 00:01:53.420 align:start position:0%
updating the chart to show them that I
got<00:01:52.290><c> it</c><00:01:52.470><c> oops</c>

00:01:53.420 --> 00:01:53.430 align:start position:0%
got it oops
 

00:01:53.430 --> 00:01:55.999 align:start position:0%
got it oops
the<00:01:54.390><c> demo</c><00:01:54.720><c> was</c><00:01:54.840><c> created</c><00:01:55.320><c> using</c><00:01:55.500><c> a</c><00:01:55.710><c> pair</c><00:01:55.979><c> of</c>

00:01:55.999 --> 00:01:56.009 align:start position:0%
the demo was created using a pair of
 

00:01:56.009 --> 00:01:58.819 align:start position:0%
the demo was created using a pair of
lambda<00:01:56.729><c> functions</c><00:01:57.180><c> written</c><00:01:57.570><c> in</c><00:01:57.780><c> nodejs</c><00:01:58.200><c> along</c>

00:01:58.819 --> 00:01:58.829 align:start position:0%
lambda functions written in nodejs along
 

00:01:58.829 --> 00:02:01.940 align:start position:0%
lambda functions written in nodejs along
with<00:01:59.009><c> api</c><00:01:59.399><c> gateway</c><00:01:59.759><c> and</c><00:02:00.000><c> dynamodb</c><00:02:01.020><c> when</c><00:02:01.920><c> a</c>

00:02:01.940 --> 00:02:01.950 align:start position:0%
with api gateway and dynamodb when a
 

00:02:01.950 --> 00:02:03.770 align:start position:0%
with api gateway and dynamodb when a
user<00:02:02.219><c> hits</c><00:02:02.520><c> the</c><00:02:02.700><c> button</c><00:02:02.880><c> to</c><00:02:03.270><c> submit</c><00:02:03.479><c> their</c>

00:02:03.770 --> 00:02:03.780 align:start position:0%
user hits the button to submit their
 

00:02:03.780 --> 00:02:05.959 align:start position:0%
user hits the button to submit their
answer<00:02:04.110><c> the</c><00:02:04.290><c> first</c><00:02:04.560><c> lambda</c><00:02:05.040><c> invokes</c><00:02:05.430><c> another</c>

00:02:05.959 --> 00:02:05.969 align:start position:0%
answer the first lambda invokes another
 

00:02:05.969 --> 00:02:08.210 align:start position:0%
answer the first lambda invokes another
which<00:02:06.570><c> sends</c><00:02:06.990><c> the</c><00:02:07.049><c> vote</c><00:02:07.350><c> to</c><00:02:07.590><c> dynamo</c><00:02:08.009><c> the</c>

00:02:08.210 --> 00:02:08.220 align:start position:0%
which sends the vote to dynamo the
 

00:02:08.220 --> 00:02:10.639 align:start position:0%
which sends the vote to dynamo the
second<00:02:09.090><c> lambda</c><00:02:09.390><c> completes</c><00:02:09.840><c> and</c><00:02:10.140><c> then</c><00:02:10.289><c> returns</c>

00:02:10.639 --> 00:02:10.649 align:start position:0%
second lambda completes and then returns
 

00:02:10.649 --> 00:02:12.860 align:start position:0%
second lambda completes and then returns
to<00:02:10.739><c> the</c><00:02:10.890><c> first</c><00:02:11.129><c> which</c><00:02:11.370><c> does</c><00:02:11.610><c> a</c><00:02:11.640><c> scan</c><00:02:12.060><c> to</c>

00:02:12.860 --> 00:02:12.870 align:start position:0%
to the first which does a scan to
 

00:02:12.870 --> 00:02:13.850 align:start position:0%
to the first which does a scan to
collect<00:02:13.170><c> the</c><00:02:13.260><c> records</c>

00:02:13.850 --> 00:02:13.860 align:start position:0%
collect the records
 

00:02:13.860 --> 00:02:16.340 align:start position:0%
collect the records
responses<00:02:14.430><c> a</c><00:02:14.780><c> related</c><00:02:15.780><c> thing</c><00:02:15.960><c> that</c><00:02:16.170><c> needs</c>

00:02:16.340 --> 00:02:16.350 align:start position:0%
responses a related thing that needs
 

00:02:16.350 --> 00:02:18.800 align:start position:0%
responses a related thing that needs
responses<00:02:16.950><c> is</c><00:02:17.070><c> this</c><00:02:17.460><c> video</c><00:02:17.790><c> if</c><00:02:18.030><c> you</c><00:02:18.450><c> find</c><00:02:18.630><c> the</c>

00:02:18.800 --> 00:02:18.810 align:start position:0%
responses is this video if you find the
 

00:02:18.810 --> 00:02:20.150 align:start position:0%
responses is this video if you find the
video<00:02:19.020><c> interesting</c><00:02:19.290><c> please</c><00:02:19.500><c> respond</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
video interesting please respond
 

00:02:20.160 --> 00:02:22.540 align:start position:0%
video interesting please respond
correctly<00:02:20.370><c> by</c><00:02:21.180><c> subscribing</c><00:02:21.810><c> to</c><00:02:21.960><c> the</c><00:02:22.050><c> channel</c>

00:02:22.540 --> 00:02:22.550 align:start position:0%
correctly by subscribing to the channel
 

00:02:22.550 --> 00:02:25.100 align:start position:0%
correctly by subscribing to the channel
5%<00:02:23.550><c> of</c><00:02:23.760><c> folks</c><00:02:24.180><c> who</c><00:02:24.360><c> like</c><00:02:24.570><c> these</c><00:02:24.780><c> videos</c>

00:02:25.100 --> 00:02:25.110 align:start position:0%
5% of folks who like these videos
 

00:02:25.110 --> 00:02:28.010 align:start position:0%
5% of folks who like these videos
subscribe<00:02:25.560><c> to</c><00:02:25.710><c> the</c><00:02:25.800><c> channel</c><00:02:26.130><c> but</c><00:02:26.700><c> 100%</c><00:02:27.360><c> of</c><00:02:27.810><c> the</c>

00:02:28.010 --> 00:02:28.020 align:start position:0%
subscribe to the channel but 100% of the
 

00:02:28.020 --> 00:02:31.310 align:start position:0%
subscribe to the channel but 100% of the
really<00:02:28.440><c> cool</c><00:02:28.740><c> people</c><00:02:29.010><c> all</c><00:02:29.460><c> subscribe</c><00:02:30.270><c> I</c><00:02:30.570><c> think</c>

00:02:31.310 --> 00:02:31.320 align:start position:0%
really cool people all subscribe I think
 

00:02:31.320 --> 00:02:32.660 align:start position:0%
really cool people all subscribe I think
you<00:02:31.500><c> know</c><00:02:31.680><c> who</c><00:02:31.890><c> you</c><00:02:32.250><c> are</c>

00:02:32.660 --> 00:02:32.670 align:start position:0%
you know who you are
 

00:02:32.670 --> 00:02:35.360 align:start position:0%
you know who you are
well<00:02:33.480><c> back</c><00:02:33.720><c> to</c><00:02:33.750><c> dynamo</c><00:02:34.320><c> it</c><00:02:34.680><c> looked</c><00:02:35.190><c> like</c>

00:02:35.360 --> 00:02:35.370 align:start position:0%
well back to dynamo it looked like
 

00:02:35.370 --> 00:02:36.980 align:start position:0%
well back to dynamo it looked like
submitting<00:02:35.850><c> the</c><00:02:35.940><c> data</c><00:02:36.150><c> to</c><00:02:36.390><c> Dynamo</c><00:02:36.810><c> was</c>

00:02:36.980 --> 00:02:36.990 align:start position:0%
submitting the data to Dynamo was
 

00:02:36.990 --> 00:02:38.930 align:start position:0%
submitting the data to Dynamo was
working<00:02:37.380><c> but</c><00:02:37.560><c> retrieving</c><00:02:38.100><c> the</c><00:02:38.340><c> data</c><00:02:38.490><c> was</c>

00:02:38.930 --> 00:02:38.940 align:start position:0%
working but retrieving the data was
 

00:02:38.940 --> 00:02:41.300 align:start position:0%
working but retrieving the data was
timing<00:02:39.330><c> out</c><00:02:39.600><c> I</c><00:02:39.900><c> use</c><00:02:40.500><c> the</c><00:02:40.709><c> service</c><00:02:41.130><c> framework</c>

00:02:41.300 --> 00:02:41.310 align:start position:0%
timing out I use the service framework
 

00:02:41.310 --> 00:02:43.820 align:start position:0%
timing out I use the service framework
to<00:02:41.670><c> deploy</c><00:02:42.030><c> the</c><00:02:42.300><c> lamda</c><00:02:42.630><c> application</c><00:02:43.200><c> and</c><00:02:43.410><c> for</c>

00:02:43.820 --> 00:02:43.830 align:start position:0%
to deploy the lamda application and for
 

00:02:43.830 --> 00:02:45.770 align:start position:0%
to deploy the lamda application and for
some<00:02:43.980><c> reason</c><00:02:44.280><c> I</c><00:02:44.459><c> configured</c><00:02:45.000><c> the</c><00:02:45.150><c> database</c><00:02:45.570><c> to</c>

00:02:45.770 --> 00:02:45.780 align:start position:0%
some reason I configured the database to
 

00:02:45.780 --> 00:02:48.560 align:start position:0%
some reason I configured the database to
use<00:02:45.990><c> provision</c><00:02:46.530><c> throughput</c><00:02:47.190><c> option</c><00:02:47.670><c> and</c><00:02:47.880><c> then</c>

00:02:48.560 --> 00:02:48.570 align:start position:0%
use provision throughput option and then
 

00:02:48.570 --> 00:02:50.330 align:start position:0%
use provision throughput option and then
I<00:02:48.600><c> never</c><00:02:48.930><c> thought</c><00:02:49.260><c> about</c><00:02:49.290><c> it</c><00:02:49.530><c> afterward</c>

00:02:50.330 --> 00:02:50.340 align:start position:0%
I never thought about it afterward
 

00:02:50.340 --> 00:02:53.390 align:start position:0%
I never thought about it afterward
so<00:02:50.880><c> you</c><00:02:51.540><c> have</c><00:02:51.720><c> two</c><00:02:51.989><c> options</c><00:02:52.230><c> when</c><00:02:52.920><c> it</c><00:02:52.950><c> comes</c><00:02:53.070><c> to</c>

00:02:53.390 --> 00:02:53.400 align:start position:0%
so you have two options when it comes to
 

00:02:53.400 --> 00:02:56.090 align:start position:0%
so you have two options when it comes to
setting<00:02:53.580><c> throughput</c><00:02:54.180><c> for</c><00:02:54.540><c> dynamo</c><00:02:54.930><c> DB</c><00:02:55.320><c> first</c>

00:02:56.090 --> 00:02:56.100 align:start position:0%
setting throughput for dynamo DB first
 

00:02:56.100 --> 00:02:58.340 align:start position:0%
setting throughput for dynamo DB first
you<00:02:56.250><c> can</c><00:02:56.400><c> do</c><00:02:56.550><c> provision</c><00:02:57.060><c> to</c><00:02:57.209><c> throughput</c><00:02:57.660><c> this</c>

00:02:58.340 --> 00:02:58.350 align:start position:0%
you can do provision to throughput this
 

00:02:58.350 --> 00:03:00.380 align:start position:0%
you can do provision to throughput this
tells<00:02:58.620><c> AWS</c><00:02:59.220><c> that</c><00:02:59.459><c> you</c><00:02:59.550><c> know</c><00:02:59.790><c> the</c><00:02:59.970><c> throughput</c>

00:03:00.380 --> 00:03:00.390 align:start position:0%
tells AWS that you know the throughput
 

00:03:00.390 --> 00:03:02.630 align:start position:0%
tells AWS that you know the throughput
the<00:03:00.540><c> database</c><00:03:00.989><c> is</c><00:03:01.200><c> going</c><00:03:01.410><c> to</c><00:03:01.620><c> need</c><00:03:01.830><c> and</c><00:03:02.190><c> you</c>

00:03:02.630 --> 00:03:02.640 align:start position:0%
the database is going to need and you
 

00:03:02.640 --> 00:03:05.120 align:start position:0%
the database is going to need and you
pay<00:03:02.880><c> a</c><00:03:02.910><c> set</c><00:03:03.239><c> rate</c><00:03:03.450><c> for</c><00:03:03.870><c> that</c><00:03:04.080><c> throughput</c><00:03:04.590><c> per</c>

00:03:05.120 --> 00:03:05.130 align:start position:0%
pay a set rate for that throughput per
 

00:03:05.130 --> 00:03:07.400 align:start position:0%
pay a set rate for that throughput per
hour<00:03:05.370><c> regardless</c><00:03:05.730><c> of</c><00:03:06.360><c> whether</c><00:03:06.600><c> you</c><00:03:06.780><c> use</c><00:03:06.810><c> it</c><00:03:07.080><c> or</c>

00:03:07.400 --> 00:03:07.410 align:start position:0%
hour regardless of whether you use it or
 

00:03:07.410 --> 00:03:10.130 align:start position:0%
hour regardless of whether you use it or
not<00:03:07.470><c> if</c><00:03:08.280><c> you</c><00:03:08.610><c> sustain</c><00:03:08.850><c> that</c><00:03:09.239><c> rate</c><00:03:09.480><c> it</c><00:03:09.690><c> probably</c>

00:03:10.130 --> 00:03:10.140 align:start position:0%
not if you sustain that rate it probably
 

00:03:10.140 --> 00:03:12.830 align:start position:0%
not if you sustain that rate it probably
is<00:03:10.440><c> going</c><00:03:10.770><c> to</c><00:03:10.860><c> be</c><00:03:11.010><c> the</c><00:03:11.160><c> cheapest</c><00:03:11.580><c> route</c><00:03:11.760><c> if</c><00:03:12.060><c> you</c>

00:03:12.830 --> 00:03:12.840 align:start position:0%
is going to be the cheapest route if you
 

00:03:12.840 --> 00:03:16.040 align:start position:0%
is going to be the cheapest route if you
need<00:03:13.020><c> more</c><00:03:13.290><c> configure</c><00:03:14.220><c> auto</c><00:03:14.430><c> scaling</c><00:03:14.820><c> if</c><00:03:15.420><c> you</c>

00:03:16.040 --> 00:03:16.050 align:start position:0%
need more configure auto scaling if you
 

00:03:16.050 --> 00:03:17.930 align:start position:0%
need more configure auto scaling if you
don't<00:03:16.380><c> use</c><00:03:16.620><c> all</c><00:03:16.770><c> dose</c><00:03:16.920><c> scaling</c><00:03:17.280><c> then</c><00:03:17.430><c> dynamo</c>

00:03:17.930 --> 00:03:17.940 align:start position:0%
don't use all dose scaling then dynamo
 

00:03:17.940 --> 00:03:20.120 align:start position:0%
don't use all dose scaling then dynamo
will<00:03:18.150><c> be</c><00:03:18.269><c> throttled</c><00:03:18.810><c> I</c><00:03:19.019><c> was</c><00:03:19.650><c> getting</c><00:03:19.920><c> thrown</c>

00:03:20.120 --> 00:03:20.130 align:start position:0%
will be throttled I was getting thrown
 

00:03:20.130 --> 00:03:22.130 align:start position:0%
will be throttled I was getting thrown
just<00:03:20.610><c> when</c><00:03:20.760><c> I</c><00:03:20.790><c> needed</c><00:03:21.060><c> it</c><00:03:21.269><c> I</c><00:03:21.480><c> was</c><00:03:21.870><c> going</c><00:03:22.019><c> to</c>

00:03:22.130 --> 00:03:22.140 align:start position:0%
just when I needed it I was going to
 

00:03:22.140 --> 00:03:23.390 align:start position:0%
just when I needed it I was going to
mention<00:03:22.410><c> the</c><00:03:22.500><c> pricing</c><00:03:22.890><c> here</c><00:03:23.040><c> but</c><00:03:23.250><c> it's</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
mention the pricing here but it's
 

00:03:23.400 --> 00:03:25.520 align:start position:0%
mention the pricing here but it's
different<00:03:23.910><c> for</c><00:03:24.239><c> pretty</c><00:03:24.480><c> much</c><00:03:24.600><c> every</c><00:03:25.170><c> region</c>

00:03:25.520 --> 00:03:25.530 align:start position:0%
different for pretty much every region
 

00:03:25.530 --> 00:03:28.970 align:start position:0%
different for pretty much every region
the<00:03:26.489><c> other</c><00:03:26.519><c> option</c><00:03:27.299><c> is</c><00:03:27.390><c> on</c><00:03:27.630><c> demand</c><00:03:27.900><c> you</c><00:03:28.739><c> pay</c><00:03:28.950><c> a</c>

00:03:28.970 --> 00:03:28.980 align:start position:0%
the other option is on demand you pay a
 

00:03:28.980 --> 00:03:31.550 align:start position:0%
the other option is on demand you pay a
fixed<00:03:29.250><c> rate</c><00:03:29.610><c> per</c><00:03:30.150><c> million</c><00:03:30.540><c> write</c><00:03:30.900><c> or</c><00:03:31.170><c> read</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
fixed rate per million write or read
 

00:03:31.560 --> 00:03:34.190 align:start position:0%
fixed rate per million write or read
capacity<00:03:32.310><c> units</c><00:03:32.459><c> if</c><00:03:32.850><c> you're</c><00:03:33.630><c> using</c><00:03:33.840><c> your</c>

00:03:34.190 --> 00:03:34.200 align:start position:0%
capacity units if you're using your
 

00:03:34.200 --> 00:03:36.289 align:start position:0%
capacity units if you're using your
database<00:03:34.650><c> a</c><00:03:34.680><c> bit</c><00:03:35.010><c> less</c><00:03:35.250><c> on</c><00:03:35.459><c> average</c><00:03:35.940><c> but</c><00:03:36.150><c> then</c>

00:03:36.289 --> 00:03:36.299 align:start position:0%
database a bit less on average but then
 

00:03:36.299 --> 00:03:38.660 align:start position:0%
database a bit less on average but then
burst<00:03:36.660><c> to</c><00:03:36.810><c> higher</c><00:03:36.989><c> rates</c><00:03:37.200><c> paying</c><00:03:38.040><c> on</c><00:03:38.220><c> demand</c>

00:03:38.660 --> 00:03:38.670 align:start position:0%
burst to higher rates paying on demand
 

00:03:38.670 --> 00:03:41.420 align:start position:0%
burst to higher rates paying on demand
may<00:03:39.510><c> be</c><00:03:39.570><c> better</c><00:03:39.870><c> for</c><00:03:40.080><c> you</c><00:03:40.170><c> for</c><00:03:40.950><c> example</c><00:03:41.340><c> if</c>

00:03:41.420 --> 00:03:41.430 align:start position:0%
may be better for you for example if
 

00:03:41.430 --> 00:03:43.670 align:start position:0%
may be better for you for example if
your<00:03:41.640><c> regular</c><00:03:41.850><c> use</c><00:03:42.420><c> is</c><00:03:42.630><c> in</c><00:03:43.140><c> your</c><00:03:43.290><c> home</c><00:03:43.470><c> office</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
your regular use is in your home office
 

00:03:43.680 --> 00:03:45.890 align:start position:0%
your regular use is in your home office
or<00:03:44.040><c> hotel</c><00:03:44.430><c> room</c><00:03:44.670><c> where</c><00:03:45.090><c> you</c><00:03:45.269><c> might</c><00:03:45.450><c> get</c><00:03:45.630><c> one</c>

00:03:45.890 --> 00:03:45.900 align:start position:0%
or hotel room where you might get one
 

00:03:45.900 --> 00:03:48.080 align:start position:0%
or hotel room where you might get one
request<00:03:46.320><c> per</c><00:03:46.560><c> five</c><00:03:46.799><c> minutes</c><00:03:47.160><c> and</c><00:03:47.370><c> then</c>

00:03:48.080 --> 00:03:48.090 align:start position:0%
request per five minutes and then
 

00:03:48.090 --> 00:03:49.910 align:start position:0%
request per five minutes and then
there's<00:03:48.420><c> a</c><00:03:48.480><c> two</c><00:03:48.840><c> minute</c><00:03:49.019><c> spike</c><00:03:49.380><c> with</c><00:03:49.620><c> about</c><00:03:49.890><c> a</c>

00:03:49.910 --> 00:03:49.920 align:start position:0%
there's a two minute spike with about a
 

00:03:49.920 --> 00:03:52.039 align:start position:0%
there's a two minute spike with about a
thousand<00:03:50.580><c> requests</c><00:03:51.090><c> while</c><00:03:51.540><c> standing</c><00:03:51.930><c> in</c>

00:03:52.039 --> 00:03:52.049 align:start position:0%
thousand requests while standing in
 

00:03:52.049 --> 00:03:54.470 align:start position:0%
thousand requests while standing in
front<00:03:52.200><c> of</c><00:03:52.380><c> 500</c><00:03:52.650><c> people</c><00:03:53.280><c> and</c><00:03:53.430><c> getting</c><00:03:53.880><c> recorded</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
front of 500 people and getting recorded
 

00:03:54.480 --> 00:03:56.539 align:start position:0%
front of 500 people and getting recorded
in<00:03:54.600><c> a</c><00:03:54.690><c> video</c><00:03:54.930><c> that</c><00:03:55.739><c> might</c><00:03:55.950><c> be</c><00:03:56.070><c> a</c><00:03:56.100><c> good</c><00:03:56.340><c> use</c><00:03:56.519><c> for</c>

00:03:56.539 --> 00:03:56.549 align:start position:0%
in a video that might be a good use for
 

00:03:56.549 --> 00:03:59.509 align:start position:0%
in a video that might be a good use for
on<00:03:56.850><c> demand</c><00:03:57.200><c> you</c><00:03:58.200><c> also</c><00:03:58.650><c> need</c><00:03:58.830><c> to</c><00:03:58.920><c> consider</c><00:03:59.100><c> what</c>

00:03:59.509 --> 00:03:59.519 align:start position:0%
on demand you also need to consider what
 

00:03:59.519 --> 00:04:02.420 align:start position:0%
on demand you also need to consider what
a<00:03:59.580><c> capacity</c><00:04:00.269><c> unit</c><00:04:00.570><c> is</c><00:04:00.810><c> it's</c><00:04:01.560><c> not</c><00:04:01.769><c> only</c><00:04:02.070><c> the</c>

00:04:02.420 --> 00:04:02.430 align:start position:0%
a capacity unit is it's not only the
 

00:04:02.430 --> 00:04:04.400 align:start position:0%
a capacity unit is it's not only the
number<00:04:02.730><c> of</c><00:04:02.820><c> requests</c><00:04:03.299><c> but</c><00:04:03.420><c> the</c><00:04:04.140><c> throughput</c>

00:04:04.400 --> 00:04:04.410 align:start position:0%
number of requests but the throughput
 

00:04:04.410 --> 00:04:07.100 align:start position:0%
number of requests but the throughput
and<00:04:04.769><c> there</c><00:04:05.670><c> are</c><00:04:05.790><c> different</c><00:04:06.150><c> kinds</c><00:04:06.600><c> of</c><00:04:06.720><c> reads</c>

00:04:07.100 --> 00:04:07.110 align:start position:0%
and there are different kinds of reads
 

00:04:07.110 --> 00:04:09.110 align:start position:0%
and there are different kinds of reads
and<00:04:07.380><c> writes</c><00:04:07.530><c> depending</c><00:04:08.010><c> on</c><00:04:08.100><c> how</c><00:04:08.310><c> consistent</c>

00:04:09.110 --> 00:04:09.120 align:start position:0%
and writes depending on how consistent
 

00:04:09.120 --> 00:04:11.210 align:start position:0%
and writes depending on how consistent
you<00:04:09.299><c> need</c><00:04:09.600><c> the</c><00:04:09.660><c> result</c><00:04:09.959><c> to</c><00:04:10.079><c> be</c><00:04:10.110><c> the</c><00:04:11.010><c> less</c>

00:04:11.210 --> 00:04:11.220 align:start position:0%
you need the result to be the less
 

00:04:11.220 --> 00:04:13.220 align:start position:0%
you need the result to be the less
consistency<00:04:12.030><c> you</c><00:04:12.180><c> need</c><00:04:12.420><c> the</c><00:04:12.750><c> cheaper</c><00:04:13.110><c> it</c><00:04:13.200><c> gets</c>

00:04:13.220 --> 00:04:13.230 align:start position:0%
consistency you need the cheaper it gets
 

00:04:13.230 --> 00:04:15.979 align:start position:0%
consistency you need the cheaper it gets
now<00:04:14.190><c> I'm</c><00:04:14.400><c> fine</c><00:04:14.700><c> with</c><00:04:14.940><c> standard</c><00:04:15.360><c> writes</c><00:04:15.600><c> but</c><00:04:15.840><c> if</c>

00:04:15.979 --> 00:04:15.989 align:start position:0%
now I'm fine with standard writes but if
 

00:04:15.989 --> 00:04:18.620 align:start position:0%
now I'm fine with standard writes but if
one<00:04:16.200><c> person</c><00:04:16.560><c> sees</c><00:04:16.769><c> a</c><00:04:17.040><c> pie</c><00:04:17.609><c> chart</c><00:04:17.940><c> with</c><00:04:18.060><c> 500</c>

00:04:18.620 --> 00:04:18.630 align:start position:0%
one person sees a pie chart with 500
 

00:04:18.630 --> 00:04:21.289 align:start position:0%
one person sees a pie chart with 500
responses<00:04:19.200><c> and</c><00:04:19.380><c> the</c><00:04:19.859><c> next</c><00:04:20.070><c> person</c><00:04:20.190><c> sees</c><00:04:20.519><c> 503</c>

00:04:21.289 --> 00:04:21.299 align:start position:0%
responses and the next person sees 503
 

00:04:21.299 --> 00:04:24.200 align:start position:0%
responses and the next person sees 503
and<00:04:21.630><c> the</c><00:04:22.080><c> next</c><00:04:22.260><c> $4.99</c><00:04:22.860><c> well</c><00:04:23.520><c> that's</c><00:04:23.760><c> fine</c><00:04:24.000><c> for</c>

00:04:24.200 --> 00:04:24.210 align:start position:0%
and the next $4.99 well that's fine for
 

00:04:24.210 --> 00:04:26.420 align:start position:0%
and the next $4.99 well that's fine for
my<00:04:24.419><c> use</c><00:04:24.690><c> case</c><00:04:24.780><c> so</c><00:04:25.440><c> I</c><00:04:25.470><c> don't</c><00:04:25.560><c> need</c><00:04:25.919><c> quite</c><00:04:26.220><c> as</c>

00:04:26.420 --> 00:04:26.430 align:start position:0%
my use case so I don't need quite as
 

00:04:26.430 --> 00:04:27.740 align:start position:0%
my use case so I don't need quite as
many<00:04:26.610><c> read</c><00:04:26.880><c> units</c><00:04:27.240><c> for</c><00:04:27.390><c> that</c>

00:04:27.740 --> 00:04:27.750 align:start position:0%
many read units for that
 

00:04:27.750 --> 00:04:30.290 align:start position:0%
many read units for that
though<00:04:28.290><c> I</c><00:04:28.320><c> am</c><00:04:28.650><c> writing</c><00:04:29.310><c> a</c><00:04:29.460><c> minimal</c><00:04:30.000><c> amount</c><00:04:30.090><c> of</c>

00:04:30.290 --> 00:04:30.300 align:start position:0%
though I am writing a minimal amount of
 

00:04:30.300 --> 00:04:32.960 align:start position:0%
though I am writing a minimal amount of
data<00:04:30.480><c> and</c><00:04:30.630><c> reading</c><00:04:31.260><c> quite</c><00:04:31.560><c> a</c><00:04:31.590><c> bit</c><00:04:31.950><c> so</c><00:04:32.670><c> actually</c>

00:04:32.960 --> 00:04:32.970 align:start position:0%
data and reading quite a bit so actually
 

00:04:32.970 --> 00:04:35.960 align:start position:0%
data and reading quite a bit so actually
I<00:04:33.000><c> need</c><00:04:33.180><c> more</c><00:04:33.510><c> read</c><00:04:34.020><c> units</c><00:04:34.380><c> in</c><00:04:34.620><c> my</c><00:04:35.460><c> service</c>

00:04:35.960 --> 00:04:35.970 align:start position:0%
I need more read units in my service
 

00:04:35.970 --> 00:04:39.770 align:start position:0%
I need more read units in my service
llamo<00:04:36.480><c> file</c><00:04:36.780><c> I</c><00:04:37.020><c> said</c><00:04:37.380><c> 5</c><00:04:37.950><c> read</c><00:04:38.490><c> units</c><00:04:38.910><c> and</c><00:04:39.120><c> 10</c>

00:04:39.770 --> 00:04:39.780 align:start position:0%
llamo file I said 5 read units and 10
 

00:04:39.780 --> 00:04:42.230 align:start position:0%
llamo file I said 5 read units and 10
write<00:04:40.230><c> units</c><00:04:40.650><c> the</c><00:04:41.340><c> right</c><00:04:41.520><c> units</c><00:04:41.850><c> were</c><00:04:42.000><c> fine</c>

00:04:42.230 --> 00:04:42.240 align:start position:0%
write units the right units were fine
 

00:04:42.240 --> 00:04:44.660 align:start position:0%
write units the right units were fine
but<00:04:42.510><c> I</c><00:04:42.540><c> weigh</c><00:04:43.230><c> underestimated</c><00:04:44.130><c> the</c><00:04:44.430><c> reads</c>

00:04:44.660 --> 00:04:44.670 align:start position:0%
but I weigh underestimated the reads
 

00:04:44.670 --> 00:04:47.300 align:start position:0%
but I weigh underestimated the reads
when<00:04:45.540><c> I</c><00:04:45.570><c> looked</c><00:04:45.810><c> at</c><00:04:45.930><c> my</c><00:04:46.020><c> throttled</c><00:04:46.560><c> DynamoDB</c>

00:04:47.300 --> 00:04:47.310 align:start position:0%
when I looked at my throttled DynamoDB
 

00:04:47.310 --> 00:04:49.100 align:start position:0%
when I looked at my throttled DynamoDB
requests<00:04:47.730><c> there</c><00:04:47.940><c> was</c><00:04:48.060><c> an</c><00:04:48.180><c> enormous</c><00:04:48.570><c> spike</c>

00:04:49.100 --> 00:04:49.110 align:start position:0%
requests there was an enormous spike
 

00:04:49.110 --> 00:04:52.070 align:start position:0%
requests there was an enormous spike
when<00:04:49.560><c> they</c><00:04:49.680><c> asked</c><00:04:50.040><c> folks</c><00:04:50.280><c> for</c><00:04:50.670><c> responses</c><00:04:51.270><c> oops</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
when they asked folks for responses oops
 

00:04:52.080 --> 00:04:55.550 align:start position:0%
when they asked folks for responses oops
so<00:04:53.070><c> here</c><00:04:53.460><c> we</c><00:04:53.640><c> see</c><00:04:53.910><c> how</c><00:04:54.300><c> to</c><00:04:54.360><c> update</c><00:04:54.810><c> the</c>

00:04:55.550 --> 00:04:55.560 align:start position:0%
so here we see how to update the
 

00:04:55.560 --> 00:04:58.040 align:start position:0%
so here we see how to update the
Surrealists<00:04:56.190><c> llamo</c><00:04:56.400><c> file</c><00:04:56.700><c> just</c><00:04:57.570><c> delete</c><00:04:57.930><c> the</c>

00:04:58.040 --> 00:04:58.050 align:start position:0%
Surrealists llamo file just delete the
 

00:04:58.050 --> 00:04:59.870 align:start position:0%
Surrealists llamo file just delete the
provision<00:04:58.470><c> throughput</c><00:04:58.950><c> section</c><00:04:59.340><c> and</c><00:04:59.520><c> replace</c>

00:04:59.870 --> 00:04:59.880 align:start position:0%
provision throughput section and replace
 

00:04:59.880 --> 00:05:02.680 align:start position:0%
provision throughput section and replace
it<00:05:00.060><c> with</c><00:05:00.090><c> billing</c><00:05:00.570><c> mode</c><00:05:00.780><c> is</c><00:05:01.200><c> paper</c><00:05:01.920><c> request</c>

00:05:02.680 --> 00:05:02.690 align:start position:0%
it with billing mode is paper request
 

00:05:02.690 --> 00:05:05.000 align:start position:0%
it with billing mode is paper request
provisioned<00:05:03.690><c> is</c><00:05:03.900><c> the</c><00:05:04.110><c> default</c><00:05:04.500><c> which</c><00:05:04.680><c> is</c><00:05:04.830><c> why</c>

00:05:05.000 --> 00:05:05.010 align:start position:0%
provisioned is the default which is why
 

00:05:05.010 --> 00:05:07.700 align:start position:0%
provisioned is the default which is why
we<00:05:05.220><c> didn't</c><00:05:05.460><c> need</c><00:05:05.550><c> to</c><00:05:05.580><c> specify</c><00:05:06.120><c> it</c><00:05:06.440><c> also</c><00:05:07.440><c> in</c><00:05:07.620><c> the</c>

00:05:07.700 --> 00:05:07.710 align:start position:0%
we didn't need to specify it also in the
 

00:05:07.710 --> 00:05:09.530 align:start position:0%
we didn't need to specify it also in the
demo<00:05:08.010><c> I</c><00:05:08.160><c> started</c><00:05:08.610><c> the</c><00:05:08.760><c> read</c><00:05:08.970><c> before</c><00:05:09.270><c> I</c>

00:05:09.530 --> 00:05:09.540 align:start position:0%
demo I started the read before I
 

00:05:09.540 --> 00:05:11.780 align:start position:0%
demo I started the read before I
completed<00:05:09.960><c> the</c><00:05:10.200><c> write</c><00:05:10.380><c> that</c><00:05:11.310><c> was</c><00:05:11.430><c> something</c><00:05:11.730><c> I</c>

00:05:11.780 --> 00:05:11.790 align:start position:0%
completed the write that was something I
 

00:05:11.790 --> 00:05:13.550 align:start position:0%
completed the write that was something I
could<00:05:11.970><c> only</c><00:05:12.120><c> see</c><00:05:12.450><c> because</c><00:05:12.840><c> of</c><00:05:12.960><c> the</c><00:05:13.050><c> trace</c><00:05:13.320><c> and</c>

00:05:13.550 --> 00:05:13.560 align:start position:0%
could only see because of the trace and
 

00:05:13.560 --> 00:05:16.760 align:start position:0%
could only see because of the trace and
the<00:05:14.280><c> problem</c><00:05:14.730><c> was</c><00:05:14.940><c> because</c><00:05:15.270><c> I</c><00:05:15.840><c> had</c><00:05:16.050><c> written</c><00:05:16.500><c> a</c>

00:05:16.760 --> 00:05:16.770 align:start position:0%
the problem was because I had written a
 

00:05:16.770 --> 00:05:18.530 align:start position:0%
the problem was because I had written a
promise<00:05:17.220><c> that</c><00:05:17.250><c> spat</c><00:05:17.760><c> out</c><00:05:17.790><c> the</c><00:05:17.940><c> right</c><00:05:18.180><c> result</c>

00:05:18.530 --> 00:05:18.540 align:start position:0%
promise that spat out the right result
 

00:05:18.540 --> 00:05:21.860 align:start position:0%
promise that spat out the right result
but<00:05:19.260><c> wasn't</c><00:05:19.500><c> really</c><00:05:19.980><c> a</c><00:05:20.160><c> promise</c><00:05:20.870><c> everything</c>

00:05:21.860 --> 00:05:21.870 align:start position:0%
but wasn't really a promise everything
 

00:05:21.870 --> 00:05:24.320 align:start position:0%
but wasn't really a promise everything
that<00:05:21.990><c> needed</c><00:05:22.200><c> to</c><00:05:22.410><c> happen</c><00:05:22.620><c> happened</c><00:05:23.250><c> but</c><00:05:24.210><c> in</c>

00:05:24.320 --> 00:05:24.330 align:start position:0%
that needed to happen happened but in
 

00:05:24.330 --> 00:05:26.630 align:start position:0%
that needed to happen happened but in
the<00:05:24.419><c> wrong</c><00:05:24.570><c> order</c><00:05:24.810><c> again</c><00:05:25.110><c> I</c><00:05:25.980><c> don't</c><00:05:26.130><c> need</c><00:05:26.520><c> the</c>

00:05:26.630 --> 00:05:26.640 align:start position:0%
the wrong order again I don't need the
 

00:05:26.640 --> 00:05:28.880 align:start position:0%
the wrong order again I don't need the
results<00:05:27.000><c> to</c><00:05:27.120><c> be</c><00:05:27.240><c> super</c><00:05:27.630><c> accurate</c><00:05:27.840><c> but</c><00:05:28.710><c> that's</c>

00:05:28.880 --> 00:05:28.890 align:start position:0%
results to be super accurate but that's
 

00:05:28.890 --> 00:05:31.159 align:start position:0%
results to be super accurate but that's
not<00:05:29.070><c> what</c><00:05:29.190><c> I</c><00:05:29.220><c> intended</c><00:05:29.600><c> when</c><00:05:30.600><c> I</c><00:05:30.630><c> corrected</c><00:05:31.050><c> the</c>

00:05:31.159 --> 00:05:31.169 align:start position:0%
not what I intended when I corrected the
 

00:05:31.169 --> 00:05:32.930 align:start position:0%
not what I intended when I corrected the
promise<00:05:31.500><c> everything</c><00:05:32.100><c> ran</c><00:05:32.340><c> in</c><00:05:32.610><c> the</c><00:05:32.790><c> right</c>

00:05:32.930 --> 00:05:32.940 align:start position:0%
promise everything ran in the right
 

00:05:32.940 --> 00:05:36.020 align:start position:0%
promise everything ran in the right
order<00:05:33.440><c> there</c><00:05:34.440><c> is</c><00:05:34.470><c> one</c><00:05:34.950><c> more</c><00:05:35.250><c> critical</c><00:05:35.820><c> thing</c>

00:05:36.020 --> 00:05:36.030 align:start position:0%
order there is one more critical thing
 

00:05:36.030 --> 00:05:38.120 align:start position:0%
order there is one more critical thing
you<00:05:36.060><c> need</c><00:05:36.330><c> to</c><00:05:36.360><c> understand</c><00:05:36.900><c> at</c><00:05:37.050><c> this</c><00:05:37.140><c> point</c><00:05:37.440><c> you</c>

00:05:38.120 --> 00:05:38.130 align:start position:0%
you need to understand at this point you
 

00:05:38.130 --> 00:05:40.040 align:start position:0%
you need to understand at this point you
need<00:05:38.280><c> to</c><00:05:38.340><c> click</c><00:05:38.610><c> on</c><00:05:38.640><c> any</c><00:05:39.030><c> other</c><00:05:39.360><c> video</c><00:05:39.660><c> to</c><00:05:39.750><c> keep</c>

00:05:40.040 --> 00:05:40.050 align:start position:0%
need to click on any other video to keep
 

00:05:40.050 --> 00:05:42.170 align:start position:0%
need to click on any other video to keep
the<00:05:40.200><c> party</c><00:05:40.500><c> going</c><00:05:40.770><c> and</c><00:05:40.950><c> while</c><00:05:41.700><c> you're</c><00:05:41.910><c> at</c><00:05:41.940><c> it</c>

00:05:42.170 --> 00:05:42.180 align:start position:0%
the party going and while you're at it
 

00:05:42.180 --> 00:05:44.240 align:start position:0%
the party going and while you're at it
subscribe<00:05:42.360><c> to</c><00:05:42.720><c> the</c><00:05:42.900><c> channel</c><00:05:43.320><c> the</c><00:05:43.800><c> number</c><00:05:44.160><c> of</c>

00:05:44.240 --> 00:05:44.250 align:start position:0%
subscribe to the channel the number of
 

00:05:44.250 --> 00:05:46.250 align:start position:0%
subscribe to the channel the number of
subscribers<00:05:44.700><c> has</c><00:05:45.060><c> grown</c><00:05:45.330><c> so</c><00:05:45.720><c> much</c><00:05:45.750><c> in</c><00:05:45.990><c> the</c>

00:05:46.250 --> 00:05:46.260 align:start position:0%
subscribers has grown so much in the
 

00:05:46.260 --> 00:05:48.320 align:start position:0%
subscribers has grown so much in the
last<00:05:46.290><c> month</c><00:05:46.740><c> but</c><00:05:46.979><c> I'm</c><00:05:47.130><c> still</c><00:05:47.490><c> waiting</c><00:05:47.700><c> for</c><00:05:47.820><c> you</c>

00:05:48.320 --> 00:05:48.330 align:start position:0%
last month but I'm still waiting for you
 

00:05:48.330 --> 00:05:51.140 align:start position:0%
last month but I'm still waiting for you
to<00:05:48.360><c> subscribe</c><00:05:49.100><c> well</c><00:05:50.100><c> unless</c><00:05:50.430><c> you</c><00:05:50.580><c> have</c><00:05:50.610><c> and</c>

00:05:51.140 --> 00:05:51.150 align:start position:0%
to subscribe well unless you have and
 

00:05:51.150 --> 00:05:52.760 align:start position:0%
to subscribe well unless you have and
and<00:05:51.300><c> if</c><00:05:51.600><c> you</c><00:05:51.630><c> have</c><00:05:51.900><c> I</c><00:05:52.080><c> look</c><00:05:52.260><c> forward</c><00:05:52.500><c> to</c><00:05:52.590><c> seeing</c>

00:05:52.760 --> 00:05:52.770 align:start position:0%
and if you have I look forward to seeing
 

00:05:52.770 --> 00:05:55.490 align:start position:0%
and if you have I look forward to seeing
you<00:05:52.860><c> again</c><00:05:52.950><c> soon</c><00:05:53.310><c> bye</c>

