WEBVTT
Kind: captions
Language: en

00:00:00.760 --> 00:00:03.590 align:start position:0%
 
with<00:00:01.040><c> GitHub</c><00:00:01.599><c> co-pilot</c><00:00:02.240><c> chat</c><00:00:02.960><c> I</c><00:00:03.159><c> can</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
with GitHub co-pilot chat I can
 

00:00:03.600 --> 00:00:06.590 align:start position:0%
with GitHub co-pilot chat I can
iteratively<00:00:04.520><c> and</c><00:00:05.279><c> interactively</c><00:00:06.279><c> ask</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
iteratively and interactively ask
 

00:00:06.600 --> 00:00:08.950 align:start position:0%
iteratively and interactively ask
questions<00:00:07.000><c> about</c><00:00:07.279><c> my</c><00:00:07.520><c> code</c><00:00:08.240><c> look</c><00:00:08.440><c> for</c><00:00:08.679><c> bug</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
questions about my code look for bug
 

00:00:08.960 --> 00:00:12.030 align:start position:0%
questions about my code look for bug
fixes<00:00:09.679><c> and</c><00:00:09.960><c> other</c><00:00:10.280><c> potential</c><00:00:11.040><c> improvements</c>

00:00:12.030 --> 00:00:12.040 align:start position:0%
fixes and other potential improvements
 

00:00:12.040 --> 00:00:14.990 align:start position:0%
fixes and other potential improvements
in<00:00:12.200><c> my</c><00:00:12.440><c> example</c><00:00:12.960><c> here</c><00:00:13.639><c> I</c><00:00:13.880><c> have</c><00:00:14.200><c> a</c><00:00:14.400><c> Django</c>

00:00:14.990 --> 00:00:15.000 align:start position:0%
in my example here I have a Django
 

00:00:15.000 --> 00:00:18.390 align:start position:0%
in my example here I have a Django
application<00:00:15.879><c> that</c><00:00:16.199><c> built</c><00:00:16.520><c> for</c><00:00:17.039><c> a</c><00:00:17.400><c> conference</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
application that built for a conference
 

00:00:18.400 --> 00:00:20.870 align:start position:0%
application that built for a conference
I<00:00:18.560><c> have</c><00:00:18.680><c> a</c><00:00:18.880><c> view</c><00:00:19.600><c> that's</c><00:00:19.880><c> displaying</c><00:00:20.519><c> all</c><00:00:20.680><c> of</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
I have a view that's displaying all of
 

00:00:20.880 --> 00:00:23.830 align:start position:0%
I have a view that's displaying all of
my<00:00:21.080><c> talks</c><00:00:21.760><c> and</c><00:00:21.960><c> speakers</c><00:00:22.439><c> for</c><00:00:22.720><c> the</c><00:00:22.960><c> talks</c><00:00:23.680><c> and</c>

00:00:23.830 --> 00:00:23.840 align:start position:0%
my talks and speakers for the talks and
 

00:00:23.840 --> 00:00:26.870 align:start position:0%
my talks and speakers for the talks and
I<00:00:24.039><c> do</c><00:00:24.320><c> this</c><00:00:24.519><c> by</c><00:00:24.880><c> utilizing</c><00:00:25.880><c> a</c><00:00:26.160><c> default</c>

00:00:26.870 --> 00:00:26.880 align:start position:0%
I do this by utilizing a default
 

00:00:26.880 --> 00:00:30.349 align:start position:0%
I do this by utilizing a default
implementation<00:00:27.880><c> inside</c><00:00:28.679><c> of</c><00:00:28.960><c> D</c><00:00:29.000><c> Jango</c><00:00:30.199><c> but</c>

00:00:30.349 --> 00:00:30.359 align:start position:0%
implementation inside of D Jango but
 

00:00:30.359 --> 00:00:32.190 align:start position:0%
implementation inside of D Jango but
what<00:00:30.480><c> we're</c><00:00:30.679><c> going</c><00:00:30.800><c> to</c><00:00:31.000><c> notice</c><00:00:31.679><c> is</c><00:00:31.800><c> that</c><00:00:32.000><c> this</c>

00:00:32.190 --> 00:00:32.200 align:start position:0%
what we're going to notice is that this
 

00:00:32.200 --> 00:00:34.630 align:start position:0%
what we're going to notice is that this
default<00:00:32.840><c> implementation</c><00:00:33.840><c> uses</c><00:00:34.239><c> a</c><00:00:34.399><c> little</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
default implementation uses a little
 

00:00:34.640 --> 00:00:37.110 align:start position:0%
default implementation uses a little
thing<00:00:34.840><c> known</c><00:00:35.040><c> as</c><00:00:35.239><c> lazy</c><00:00:35.600><c> loading</c><00:00:36.480><c> meaning</c><00:00:36.840><c> it's</c>

00:00:37.110 --> 00:00:37.120 align:start position:0%
thing known as lazy loading meaning it's
 

00:00:37.120 --> 00:00:40.430 align:start position:0%
thing known as lazy loading meaning it's
not<00:00:37.440><c> going</c><00:00:37.760><c> to</c><00:00:38.320><c> grab</c><00:00:38.719><c> the</c><00:00:38.920><c> data</c><00:00:39.680><c> until</c><00:00:40.079><c> it</c>

00:00:40.430 --> 00:00:40.440 align:start position:0%
not going to grab the data until it
 

00:00:40.440 --> 00:00:43.709 align:start position:0%
not going to grab the data until it
actually<00:00:40.920><c> needs</c><00:00:41.280><c> it</c><00:00:42.079><c> in</c><00:00:42.280><c> this</c><00:00:42.520><c> example</c><00:00:43.079><c> though</c>

00:00:43.709 --> 00:00:43.719 align:start position:0%
actually needs it in this example though
 

00:00:43.719 --> 00:00:45.510 align:start position:0%
actually needs it in this example though
that's<00:00:43.920><c> going</c><00:00:44.039><c> to</c><00:00:44.239><c> wind</c><00:00:44.559><c> up</c><00:00:44.920><c> impacting</c>

00:00:45.510 --> 00:00:45.520 align:start position:0%
that's going to wind up impacting
 

00:00:45.520 --> 00:00:47.069 align:start position:0%
that's going to wind up impacting
performance<00:00:46.399><c> because</c><00:00:46.600><c> we're</c><00:00:46.719><c> going</c><00:00:46.840><c> to</c><00:00:46.960><c> have</c>

00:00:47.069 --> 00:00:47.079 align:start position:0%
performance because we're going to have
 

00:00:47.079 --> 00:00:49.069 align:start position:0%
performance because we're going to have
a<00:00:47.239><c> lot</c><00:00:47.399><c> of</c><00:00:47.559><c> round</c><00:00:47.840><c> trips</c><00:00:48.280><c> back</c><00:00:48.440><c> to</c><00:00:48.680><c> the</c>

00:00:49.069 --> 00:00:49.079 align:start position:0%
a lot of round trips back to the
 

00:00:49.079 --> 00:00:51.189 align:start position:0%
a lot of round trips back to the
database<00:00:50.079><c> let's</c><00:00:50.360><c> see</c><00:00:50.680><c> how</c><00:00:50.840><c> we</c><00:00:50.960><c> could</c>

00:00:51.189 --> 00:00:51.199 align:start position:0%
database let's see how we could
 

00:00:51.199 --> 00:00:53.869 align:start position:0%
database let's see how we could
potentially<00:00:51.760><c> improve</c><00:00:52.440><c> performance</c><00:00:53.440><c> I</c><00:00:53.559><c> open</c>

00:00:53.869 --> 00:00:53.879 align:start position:0%
potentially improve performance I open
 

00:00:53.879 --> 00:00:56.709 align:start position:0%
potentially improve performance I open
up<00:00:54.120><c> GitHub</c><00:00:54.520><c> co-pilot</c><00:00:55.079><c> chat</c><00:00:55.760><c> and</c><00:00:56.079><c> ask</c><00:00:56.440><c> that</c>

00:00:56.709 --> 00:00:56.719 align:start position:0%
up GitHub co-pilot chat and ask that
 

00:00:56.719 --> 00:00:59.229 align:start position:0%
up GitHub co-pilot chat and ask that
question<00:00:57.719><c> I</c><00:00:57.920><c> get</c><00:00:58.199><c> back</c><00:00:58.359><c> a</c><00:00:58.600><c> response</c><00:00:59.079><c> that's</c>

00:00:59.229 --> 00:00:59.239 align:start position:0%
question I get back a response that's
 

00:00:59.239 --> 00:01:00.750 align:start position:0%
question I get back a response that's
going<00:00:59.320><c> to</c><00:00:59.480><c> give</c><00:00:59.600><c> me</c><00:00:59.719><c> some</c><00:00:59.920><c> some</c><00:01:00.160><c> highle</c>

00:01:00.750 --> 00:01:00.760 align:start position:0%
going to give me some some highle
 

00:01:00.760 --> 00:01:02.549 align:start position:0%
going to give me some some highle
information<00:01:01.320><c> with</c><00:01:01.440><c> a</c><00:01:01.600><c> couple</c><00:01:01.840><c> of</c><00:01:02.079><c> possible</c>

00:01:02.549 --> 00:01:02.559 align:start position:0%
information with a couple of possible
 

00:01:02.559 --> 00:01:07.149 align:start position:0%
information with a couple of possible
solutions<00:01:03.559><c> things</c><00:01:03.840><c> like</c><00:01:04.080><c> caching</c><00:01:05.119><c> paging</c><00:01:06.159><c> or</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
solutions things like caching paging or
 

00:01:07.159 --> 00:01:09.950 align:start position:0%
solutions things like caching paging or
the<00:01:07.479><c> suggestion</c><00:01:08.280><c> that</c><00:01:08.560><c> I</c><00:01:08.799><c> go</c><00:01:09.119><c> grab</c><00:01:09.439><c> related</c>

00:01:09.950 --> 00:01:09.960 align:start position:0%
the suggestion that I go grab related
 

00:01:09.960 --> 00:01:12.630 align:start position:0%
the suggestion that I go grab related
data<00:01:10.640><c> in</c><00:01:10.799><c> this</c><00:01:11.000><c> case</c><00:01:11.360><c> speaker</c><00:01:12.360><c> that's</c><00:01:12.560><c> going</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
data in this case speaker that's going
 

00:01:12.640 --> 00:01:14.670 align:start position:0%
data in this case speaker that's going
to<00:01:12.799><c> be</c><00:01:12.920><c> the</c><00:01:13.080><c> solution</c><00:01:13.439><c> that</c><00:01:13.560><c> I'll</c><00:01:13.799><c> Implement</c>

00:01:14.670 --> 00:01:14.680 align:start position:0%
to be the solution that I'll Implement
 

00:01:14.680 --> 00:01:16.830 align:start position:0%
to be the solution that I'll Implement
so<00:01:14.880><c> I'll</c><00:01:15.159><c> paste</c><00:01:15.479><c> in</c><00:01:15.759><c> this</c><00:01:16.040><c> code</c><00:01:16.360><c> that's</c><00:01:16.560><c> been</c>

00:01:16.830 --> 00:01:16.840 align:start position:0%
so I'll paste in this code that's been
 

00:01:16.840 --> 00:01:19.950 align:start position:0%
so I'll paste in this code that's been
suggested<00:01:17.439><c> by</c><00:01:17.680><c> GitHub</c><00:01:18.360><c> co-pilot</c><00:01:19.360><c> if</c><00:01:19.479><c> I</c><00:01:19.640><c> save</c>

00:01:19.950 --> 00:01:19.960 align:start position:0%
suggested by GitHub co-pilot if I save
 

00:01:19.960 --> 00:01:22.310 align:start position:0%
suggested by GitHub co-pilot if I save
everything<00:01:20.280><c> and</c><00:01:20.520><c> refresh</c><00:01:20.920><c> the</c><00:01:21.119><c> page</c><00:01:22.079><c> I'll</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
everything and refresh the page I'll
 

00:01:22.320 --> 00:01:24.630 align:start position:0%
everything and refresh the page I'll
notice<00:01:22.600><c> that</c><00:01:22.799><c> the</c><00:01:23.000><c> page</c><00:01:23.320><c> looks</c><00:01:23.640><c> the</c><00:01:23.880><c> exact</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
notice that the page looks the exact
 

00:01:24.640 --> 00:01:28.630 align:start position:0%
notice that the page looks the exact
same<00:01:25.640><c> but</c><00:01:25.960><c> the</c><00:01:26.479><c> backend</c><00:01:27.040><c> query</c><00:01:27.880><c> is</c><00:01:28.159><c> now</c>

00:01:28.630 --> 00:01:28.640 align:start position:0%
same but the backend query is now
 

00:01:28.640 --> 00:01:31.469 align:start position:0%
same but the backend query is now
updated<00:01:29.520><c> but</c><00:01:29.640><c> it's</c><00:01:30.040><c> still</c><00:01:30.280><c> not</c><00:01:30.560><c> perfectly</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
updated but it's still not perfectly
 

00:01:31.479 --> 00:01:33.950 align:start position:0%
updated but it's still not perfectly
optimized<00:01:32.479><c> that</c><00:01:32.720><c> in</c><00:01:32.920><c> this</c><00:01:33.159><c> case</c><00:01:33.520><c> it's</c><00:01:33.759><c> going</c>

00:01:33.950 --> 00:01:33.960 align:start position:0%
optimized that in this case it's going
 

00:01:33.960 --> 00:01:36.990 align:start position:0%
optimized that in this case it's going
to<00:01:34.200><c> grab</c><00:01:34.560><c> the</c><00:01:34.720><c> entire</c><00:01:35.200><c> talk</c><00:01:35.880><c> and</c><00:01:36.040><c> the</c><00:01:36.240><c> entire</c>

00:01:36.990 --> 00:01:37.000 align:start position:0%
to grab the entire talk and the entire
 

00:01:37.000 --> 00:01:39.830 align:start position:0%
to grab the entire talk and the entire
speaker<00:01:38.000><c> all</c><00:01:38.159><c> that</c><00:01:38.280><c> I</c><00:01:38.479><c> really</c><00:01:38.759><c> need</c><00:01:39.320><c> is</c><00:01:39.600><c> just</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
speaker all that I really need is just
 

00:01:39.840 --> 00:01:42.870 align:start position:0%
speaker all that I really need is just
the<00:01:40.159><c> ID</c><00:01:40.720><c> and</c><00:01:40.920><c> the</c><00:01:41.119><c> title</c><00:01:41.439><c> for</c><00:01:41.720><c> the</c><00:01:41.920><c> talk</c><00:01:42.720><c> and</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
the ID and the title for the talk and
 

00:01:42.880 --> 00:01:44.469 align:start position:0%
the ID and the title for the talk and
the<00:01:43.079><c> name</c><00:01:43.439><c> of</c><00:01:43.640><c> the</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
the name of the
 

00:01:44.479 --> 00:01:47.510 align:start position:0%
the name of the
speaker<00:01:45.479><c> let's</c><00:01:45.719><c> ask</c><00:01:46.000><c> GitHub</c><00:01:46.360><c> co-pilot</c><00:01:47.159><c> how</c><00:01:47.360><c> we</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
speaker let's ask GitHub co-pilot how we
 

00:01:47.520 --> 00:01:50.310 align:start position:0%
speaker let's ask GitHub co-pilot how we
could<00:01:47.719><c> do</c><00:01:48.000><c> this</c><00:01:48.920><c> so</c><00:01:49.159><c> through</c><00:01:49.439><c> GitHub</c><00:01:49.799><c> co-pilot</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
could do this so through GitHub co-pilot
 

00:01:50.320 --> 00:01:53.950 align:start position:0%
could do this so through GitHub co-pilot
chat<00:01:50.960><c> I</c><00:01:51.240><c> update</c><00:01:51.640><c> my</c><00:01:51.880><c> query</c><00:01:52.880><c> and</c><00:01:53.240><c> it</c><00:01:53.360><c> will</c><00:01:53.640><c> then</c>

00:01:53.950 --> 00:01:53.960 align:start position:0%
chat I update my query and it will then
 

00:01:53.960 --> 00:01:56.029 align:start position:0%
chat I update my query and it will then
give<00:01:54.119><c> me</c><00:01:54.240><c> a</c><00:01:54.399><c> suggestion</c><00:01:55.000><c> on</c><00:01:55.280><c> how</c><00:01:55.479><c> I</c><00:01:55.600><c> could</c><00:01:55.759><c> go</c>

00:01:56.029 --> 00:01:56.039 align:start position:0%
give me a suggestion on how I could go
 

00:01:56.039 --> 00:01:58.950 align:start position:0%
give me a suggestion on how I could go
grab<00:01:56.360><c> just</c><00:01:56.640><c> that</c><00:01:57.039><c> information</c><00:01:58.039><c> I'll</c><00:01:58.360><c> paste</c><00:01:58.680><c> in</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
grab just that information I'll paste in
 

00:01:58.960 --> 00:02:01.429 align:start position:0%
grab just that information I'll paste in
that<00:01:59.119><c> code</c><00:01:59.399><c> suggest</c><00:01:59.840><c> question</c><00:02:00.719><c> refresh</c><00:02:01.240><c> the</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
that code suggest question refresh the
 

00:02:01.439 --> 00:02:03.590 align:start position:0%
that code suggest question refresh the
page<00:02:02.399><c> and</c><00:02:02.600><c> now</c><00:02:02.719><c> we're</c><00:02:02.880><c> going</c><00:02:03.000><c> to</c><00:02:03.159><c> notice</c><00:02:03.479><c> that</c>

00:02:03.590 --> 00:02:03.600 align:start position:0%
page and now we're going to notice that
 

00:02:03.600 --> 00:02:05.830 align:start position:0%
page and now we're going to notice that
we've<00:02:03.799><c> got</c><00:02:03.920><c> a</c><00:02:04.119><c> bug</c><00:02:04.960><c> we're</c><00:02:05.119><c> going</c><00:02:05.200><c> to</c><00:02:05.439><c> notice</c>

00:02:05.830 --> 00:02:05.840 align:start position:0%
we've got a bug we're going to notice
 

00:02:05.840 --> 00:02:08.630 align:start position:0%
we've got a bug we're going to notice
that<00:02:06.039><c> the</c><00:02:06.240><c> name</c><00:02:06.759><c> isn't</c><00:02:07.280><c> displaying</c><00:02:08.280><c> now</c><00:02:08.479><c> the</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
that the name isn't displaying now the
 

00:02:08.640 --> 00:02:11.309 align:start position:0%
that the name isn't displaying now the
good<00:02:08.879><c> news</c><00:02:09.399><c> is</c><00:02:09.520><c> that</c><00:02:09.679><c> the</c><00:02:09.800><c> query</c><00:02:10.160><c> is</c><00:02:10.360><c> optimized</c>

00:02:11.309 --> 00:02:11.319 align:start position:0%
good news is that the query is optimized
 

00:02:11.319 --> 00:02:13.150 align:start position:0%
good news is that the query is optimized
we're<00:02:11.480><c> going</c><00:02:11.599><c> to</c><00:02:11.800><c> notice</c><00:02:12.160><c> that</c><00:02:12.280><c> it</c><00:02:12.400><c> will</c><00:02:12.680><c> only</c>

00:02:13.150 --> 00:02:13.160 align:start position:0%
we're going to notice that it will only
 

00:02:13.160 --> 00:02:16.910 align:start position:0%
we're going to notice that it will only
grab<00:02:13.879><c> the</c><00:02:14.160><c> ID</c><00:02:14.720><c> title</c><00:02:15.200><c> and</c><00:02:15.519><c> name</c><00:02:16.040><c> as</c>

00:02:16.910 --> 00:02:16.920 align:start position:0%
grab the ID title and name as
 

00:02:16.920 --> 00:02:19.350 align:start position:0%
grab the ID title and name as
requested<00:02:17.920><c> but</c><00:02:18.319><c> it's</c><00:02:18.480><c> only</c><00:02:18.760><c> going</c><00:02:18.840><c> to</c><00:02:19.000><c> help</c><00:02:19.200><c> us</c>

00:02:19.350 --> 00:02:19.360 align:start position:0%
requested but it's only going to help us
 

00:02:19.360 --> 00:02:21.190 align:start position:0%
requested but it's only going to help us
if<00:02:19.480><c> we</c><00:02:19.599><c> can</c><00:02:19.840><c> actually</c><00:02:20.200><c> display</c><00:02:20.640><c> the</c><00:02:20.800><c> right</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
if we can actually display the right
 

00:02:21.200 --> 00:02:24.550 align:start position:0%
if we can actually display the right
information<00:02:22.200><c> so</c><00:02:22.480><c> let</c><00:02:22.599><c> me</c><00:02:22.840><c> ask</c><00:02:23.120><c> GitHub</c><00:02:23.560><c> copilot</c>

00:02:24.550 --> 00:02:24.560 align:start position:0%
information so let me ask GitHub copilot
 

00:02:24.560 --> 00:02:27.910 align:start position:0%
information so let me ask GitHub copilot
how<00:02:24.840><c> we</c><00:02:25.120><c> could</c><00:02:25.480><c> Now</c><00:02:25.800><c> display</c><00:02:26.360><c> out</c><00:02:26.680><c> the</c><00:02:26.840><c> name</c><00:02:27.800><c> it</c>

00:02:27.910 --> 00:02:27.920 align:start position:0%
how we could Now display out the name it
 

00:02:27.920 --> 00:02:30.229 align:start position:0%
how we could Now display out the name it
will<00:02:28.239><c> give</c><00:02:28.440><c> me</c><00:02:28.800><c> a</c><00:02:29.040><c> code</c><00:02:29.239><c> snippet</c><00:02:29.640><c> that</c><00:02:29.959><c> I</c><00:02:30.040><c> can</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
will give me a code snippet that I can
 

00:02:30.239 --> 00:02:32.470 align:start position:0%
will give me a code snippet that I can
use<00:02:30.959><c> I'm</c><00:02:31.080><c> going</c><00:02:31.160><c> to</c><00:02:31.440><c> grab</c><00:02:31.760><c> the</c><00:02:31.959><c> part</c><00:02:32.200><c> that</c><00:02:32.319><c> I</c>

00:02:32.470 --> 00:02:32.480 align:start position:0%
use I'm going to grab the part that I
 

00:02:32.480 --> 00:02:35.390 align:start position:0%
use I'm going to grab the part that I
need<00:02:33.239><c> go</c><00:02:33.519><c> back</c><00:02:33.680><c> to</c><00:02:33.840><c> the</c><00:02:34.040><c> template</c><00:02:34.879><c> and</c><00:02:35.120><c> make</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
need go back to the template and make
 

00:02:35.400 --> 00:02:38.229 align:start position:0%
need go back to the template and make
that<00:02:35.720><c> update</c><00:02:36.720><c> and</c><00:02:36.920><c> now</c><00:02:37.120><c> when</c><00:02:37.239><c> I</c><00:02:37.360><c> save</c><00:02:37.640><c> the</c><00:02:37.879><c> page</c>

00:02:38.229 --> 00:02:38.239 align:start position:0%
that update and now when I save the page
 

00:02:38.239 --> 00:02:40.430 align:start position:0%
that update and now when I save the page
and<00:02:38.480><c> hit</c><00:02:38.720><c> refresh</c><00:02:39.599><c> we're</c><00:02:39.800><c> going</c><00:02:39.879><c> to</c><00:02:40.080><c> notice</c>

00:02:40.430 --> 00:02:40.440 align:start position:0%
and hit refresh we're going to notice
 

00:02:40.440 --> 00:02:41.990 align:start position:0%
and hit refresh we're going to notice
that<00:02:40.680><c> everything</c><00:02:41.040><c> is</c><00:02:41.239><c> back</c><00:02:41.440><c> to</c><00:02:41.560><c> the</c><00:02:41.680><c> way</c><00:02:41.879><c> that</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
that everything is back to the way that
 

00:02:42.000 --> 00:02:44.190 align:start position:0%
that everything is back to the way that
it<00:02:42.159><c> was</c><00:02:42.840><c> it's</c><00:02:43.080><c> displaying</c><00:02:43.599><c> all</c><00:02:43.800><c> the</c><00:02:43.920><c> right</c>

00:02:44.190 --> 00:02:44.200 align:start position:0%
it was it's displaying all the right
 

00:02:44.200 --> 00:02:46.869 align:start position:0%
it was it's displaying all the right
information<00:02:45.200><c> and</c><00:02:45.400><c> doing</c><00:02:45.800><c> so</c><00:02:46.159><c> with</c><00:02:46.480><c> an</c>

00:02:46.869 --> 00:02:46.879 align:start position:0%
information and doing so with an
 

00:02:46.879 --> 00:02:50.350 align:start position:0%
information and doing so with an
optimized<00:02:47.599><c> query</c><00:02:48.599><c> what</c><00:02:48.760><c> we</c><00:02:48.879><c> saw</c><00:02:49.360><c> here</c><00:02:49.720><c> is</c><00:02:50.040><c> how</c>

00:02:50.350 --> 00:02:50.360 align:start position:0%
optimized query what we saw here is how
 

00:02:50.360 --> 00:02:54.030 align:start position:0%
optimized query what we saw here is how
we<00:02:50.680><c> can</c><00:02:51.080><c> utilize</c><00:02:52.000><c> GitHub</c><00:02:52.480><c> co-pilot</c><00:02:53.159><c> chat</c><00:02:53.879><c> to</c>

00:02:54.030 --> 00:02:54.040 align:start position:0%
we can utilize GitHub co-pilot chat to
 

00:02:54.040 --> 00:02:57.430 align:start position:0%
we can utilize GitHub co-pilot chat to
be<00:02:54.200><c> able</c><00:02:54.440><c> to</c><00:02:54.760><c> ask</c><00:02:55.159><c> questions</c><00:02:56.080><c> about</c><00:02:56.440><c> our</c><00:02:56.760><c> code</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
be able to ask questions about our code
 

00:02:57.440 --> 00:03:00.470 align:start position:0%
be able to ask questions about our code
and<00:02:57.720><c> get</c><00:02:58.040><c> contextual</c><00:02:58.920><c> answers</c><00:02:59.440><c> back</c><00:03:00.239><c> this</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
and get contextual answers back this
 

00:03:00.480 --> 00:03:04.030 align:start position:0%
and get contextual answers back this
allows<00:03:00.959><c> me</c><00:03:01.280><c> to</c><00:03:01.800><c> stay</c><00:03:02.200><c> in</c><00:03:02.400><c> the</c><00:03:02.640><c> zone</c><00:03:03.360><c> be</c><00:03:03.519><c> able</c><00:03:03.760><c> to</c>

00:03:04.030 --> 00:03:04.040 align:start position:0%
allows me to stay in the zone be able to
 

00:03:04.040 --> 00:03:07.110 align:start position:0%
allows me to stay in the zone be able to
find<00:03:04.599><c> information</c><00:03:05.599><c> that's</c><00:03:05.920><c> tailored</c><00:03:06.440><c> to</c><00:03:06.840><c> the</c>

00:03:07.110 --> 00:03:07.120 align:start position:0%
find information that's tailored to the
 

00:03:07.120 --> 00:03:09.949 align:start position:0%
find information that's tailored to the
specific<00:03:07.560><c> scenario</c><00:03:08.400><c> that</c><00:03:08.640><c> I'm</c><00:03:08.920><c> working</c><00:03:09.360><c> in</c>

00:03:09.949 --> 00:03:09.959 align:start position:0%
specific scenario that I'm working in
 

00:03:09.959 --> 00:03:14.710 align:start position:0%
specific scenario that I'm working in
and<00:03:10.159><c> be</c><00:03:10.360><c> able</c><00:03:11.040><c> to</c><00:03:11.360><c> be</c><00:03:11.640><c> more</c>

00:03:14.710 --> 00:03:14.720 align:start position:0%
 
 

00:03:14.720 --> 00:03:17.720 align:start position:0%
 
productive

