WEBVTT
Kind: captions
Language: en

00:00:00.640 --> 00:00:04.070 align:start position:0%
 
hello<00:00:00.880><c> everyone</c><00:00:01.839><c> Ravi</c><00:00:02.200><c> here</c><00:00:02.360><c> from</c><00:00:02.520><c> Lama</c><00:00:03.080><c> index</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
hello everyone Ravi here from <PERSON> index
 

00:00:04.080 --> 00:00:07.670 align:start position:0%
hello everyone Ravi here from <PERSON> index
welcome<00:00:04.359><c> to</c><00:00:04.560><c> another</c><00:00:05.240><c> tutorial</c><00:00:06.120><c> video</c><00:00:06.879><c> in</c><00:00:07.439><c> U</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
welcome to another tutorial video in U
 

00:00:07.680 --> 00:00:10.749 align:start position:0%
welcome to another tutorial video in U
agent<00:00:08.120><c> tutorial</c><00:00:08.679><c> video</c><00:00:09.000><c> series</c><00:00:10.000><c> so</c><00:00:10.200><c> in</c><00:00:10.360><c> this</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
agent tutorial video series so in this
 

00:00:10.759 --> 00:00:12.150 align:start position:0%
agent tutorial video series so in this
uh<00:00:10.920><c> video</c><00:00:11.200><c> we'll</c><00:00:11.400><c> look</c><00:00:11.559><c> into</c><00:00:11.759><c> retrial</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
uh video we'll look into retrial
 

00:00:12.160 --> 00:00:14.230 align:start position:0%
uh video we'll look into retrial
augmented<00:00:12.559><c> function</c><00:00:12.840><c> calling</c><00:00:13.160><c> agent</c><00:00:13.839><c> earlier</c>

00:00:14.230 --> 00:00:14.240 align:start position:0%
augmented function calling agent earlier
 

00:00:14.240 --> 00:00:18.070 align:start position:0%
augmented function calling agent earlier
we'll<00:00:14.839><c> uh</c><00:00:15.240><c> have</c><00:00:15.400><c> seen</c><00:00:16.279><c> on</c><00:00:16.800><c> um</c><00:00:17.119><c> using</c><00:00:17.680><c> react</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
we'll uh have seen on um using react
 

00:00:18.080 --> 00:00:21.150 align:start position:0%
we'll uh have seen on um using react
agent<00:00:18.359><c> and</c><00:00:18.520><c> function</c><00:00:18.840><c> calling</c><00:00:19.199><c> agent</c><00:00:20.160><c> uh</c><00:00:20.359><c> with</c>

00:00:21.150 --> 00:00:21.160 align:start position:0%
agent and function calling agent uh with
 

00:00:21.160 --> 00:00:24.269 align:start position:0%
agent and function calling agent uh with
simple<00:00:21.519><c> calculator</c><00:00:22.039><c> tools</c><00:00:22.560><c> and</c><00:00:23.000><c> uh</c><00:00:23.599><c> rag</c><00:00:24.039><c> qu</c>

00:00:24.269 --> 00:00:24.279 align:start position:0%
simple calculator tools and uh rag qu
 

00:00:24.279 --> 00:00:27.070 align:start position:0%
simple calculator tools and uh rag qu
engine<00:00:24.560><c> tools</c><00:00:25.400><c> right</c><00:00:25.960><c> so</c><00:00:26.560><c> there</c><00:00:26.760><c> the</c><00:00:26.880><c> number</c>

00:00:27.070 --> 00:00:27.080 align:start position:0%
engine tools right so there the number
 

00:00:27.080 --> 00:00:29.269 align:start position:0%
engine tools right so there the number
of<00:00:27.240><c> tools</c><00:00:27.599><c> are</c><00:00:27.800><c> limited</c><00:00:28.480><c> probably</c><00:00:28.920><c> three</c><00:00:29.119><c> or</c>

00:00:29.269 --> 00:00:29.279 align:start position:0%
of tools are limited probably three or
 

00:00:29.279 --> 00:00:32.589 align:start position:0%
of tools are limited probably three or
four<00:00:29.679><c> uh</c><00:00:30.000><c> tools</c><00:00:30.759><c> and</c><00:00:30.960><c> then</c><00:00:31.400><c> Pro</c><00:00:31.720><c> and</c><00:00:31.880><c> then</c><00:00:32.079><c> rqu</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
four uh tools and then Pro and then rqu
 

00:00:32.599 --> 00:00:34.830 align:start position:0%
four uh tools and then Pro and then rqu
engine<00:00:32.840><c> tools</c><00:00:33.120><c> are</c><00:00:33.320><c> two</c><00:00:33.480><c> or</c><00:00:33.640><c> three</c><00:00:34.040><c> right</c><00:00:34.640><c> so</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
engine tools are two or three right so
 

00:00:34.840 --> 00:00:37.910 align:start position:0%
engine tools are two or three right so
the<00:00:34.960><c> number</c><00:00:35.160><c> of</c><00:00:35.280><c> tools</c><00:00:35.520><c> are</c><00:00:35.800><c> very</c><00:00:36.280><c> uh</c><00:00:36.440><c> less</c><00:00:37.079><c> so</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
the number of tools are very uh less so
 

00:00:37.920 --> 00:00:41.190 align:start position:0%
the number of tools are very uh less so
uh<00:00:38.440><c> and</c><00:00:38.640><c> then</c><00:00:39.520><c> but</c><00:00:39.719><c> then</c><00:00:39.960><c> in</c><00:00:40.120><c> real</c><00:00:40.360><c> world</c><00:00:41.039><c> there</c>

00:00:41.190 --> 00:00:41.200 align:start position:0%
uh and then but then in real world there
 

00:00:41.200 --> 00:00:44.029 align:start position:0%
uh and then but then in real world there
may<00:00:41.360><c> be</c><00:00:41.760><c> like</c><00:00:41.960><c> 50</c><00:00:42.320><c> or</c><00:00:42.520><c> 100</c><00:00:42.800><c> number</c><00:00:43.239><c> 100</c><00:00:43.480><c> tools</c>

00:00:44.029 --> 00:00:44.039 align:start position:0%
may be like 50 or 100 number 100 tools
 

00:00:44.039 --> 00:00:46.590 align:start position:0%
may be like 50 or 100 number 100 tools
right<00:00:44.559><c> and</c><00:00:44.760><c> then</c><00:00:45.320><c> uh</c><00:00:45.520><c> if</c><00:00:45.640><c> you</c><00:00:45.760><c> want</c><00:00:46.039><c> to</c><00:00:46.280><c> select</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
right and then uh if you want to select
 

00:00:46.600 --> 00:00:48.549 align:start position:0%
right and then uh if you want to select
one<00:00:46.719><c> of</c><00:00:46.879><c> these</c><00:00:47.039><c> tools</c><00:00:47.440><c> probably</c><00:00:47.719><c> you</c><00:00:47.800><c> need</c><00:00:48.000><c> to</c>

00:00:48.549 --> 00:00:48.559 align:start position:0%
one of these tools probably you need to
 

00:00:48.559 --> 00:00:51.229 align:start position:0%
one of these tools probably you need to
include<00:00:49.000><c> all</c><00:00:49.239><c> the</c><00:00:49.719><c> description</c><00:00:50.239><c> of</c><00:00:50.879><c> each</c><00:00:51.079><c> and</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
include all the description of each and
 

00:00:51.239 --> 00:00:53.549 align:start position:0%
include all the description of each and
every<00:00:51.480><c> tool</c><00:00:51.840><c> into</c><00:00:52.079><c> the</c><00:00:52.239><c> prompt</c><00:00:52.800><c> and</c><00:00:53.120><c> the</c><00:00:53.199><c> llm</c>

00:00:53.549 --> 00:00:53.559 align:start position:0%
every tool into the prompt and the llm
 

00:00:53.559 --> 00:00:56.349 align:start position:0%
every tool into the prompt and the llm
will<00:00:53.680><c> select</c><00:00:53.960><c> one</c><00:00:54.120><c> of</c><00:00:54.359><c> these</c><00:00:55.079><c> tools</c><00:00:55.840><c> and</c><00:00:56.079><c> call</c>

00:00:56.349 --> 00:00:56.359 align:start position:0%
will select one of these tools and call
 

00:00:56.359 --> 00:00:58.709 align:start position:0%
will select one of these tools and call
the<00:00:56.520><c> specific</c><00:00:56.960><c> function</c><00:00:57.559><c> and</c><00:00:58.120><c> uh</c><00:00:58.239><c> obtain</c><00:00:58.559><c> the</c>

00:00:58.709 --> 00:00:58.719 align:start position:0%
the specific function and uh obtain the
 

00:00:58.719 --> 00:01:02.189 align:start position:0%
the specific function and uh obtain the
result<00:00:59.039><c> and</c><00:00:59.760><c> uh</c><00:01:00.320><c> iterate</c><00:01:00.760><c> over</c><00:01:00.920><c> it</c><00:01:01.199><c> right</c><00:01:02.039><c> uh</c>

00:01:02.189 --> 00:01:02.199 align:start position:0%
result and uh iterate over it right uh
 

00:01:02.199 --> 00:01:06.670 align:start position:0%
result and uh iterate over it right uh
this<00:01:02.840><c> might</c><00:01:03.840><c> add</c><00:01:04.080><c> latency</c><00:01:04.680><c> as</c><00:01:04.799><c> well</c><00:01:04.960><c> as</c><00:01:05.680><c> cost</c>

00:01:06.670 --> 00:01:06.680 align:start position:0%
this might add latency as well as cost
 

00:01:06.680 --> 00:01:09.070 align:start position:0%
this might add latency as well as cost
rather<00:01:06.960><c> than</c><00:01:07.159><c> that</c><00:01:07.439><c> of</c><00:01:07.759><c> 100</c><00:01:08.040><c> tools</c><00:01:08.520><c> probably</c>

00:01:09.070 --> 00:01:09.080 align:start position:0%
rather than that of 100 tools probably
 

00:01:09.080 --> 00:01:10.990 align:start position:0%
rather than that of 100 tools probably
you<00:01:09.280><c> retrieve</c><00:01:09.640><c> only</c><00:01:09.840><c> two</c><00:01:10.000><c> tools</c><00:01:10.479><c> and</c><00:01:10.680><c> use</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
you retrieve only two tools and use
 

00:01:11.000 --> 00:01:13.910 align:start position:0%
you retrieve only two tools and use
those<00:01:11.200><c> two</c><00:01:11.400><c> tools</c><00:01:12.240><c> to</c><00:01:12.520><c> compute</c><00:01:12.880><c> your</c><00:01:13.400><c> uh</c><00:01:13.520><c> user</c>

00:01:13.910 --> 00:01:13.920 align:start position:0%
those two tools to compute your uh user
 

00:01:13.920 --> 00:01:18.310 align:start position:0%
those two tools to compute your uh user
task<00:01:14.439><c> or</c><00:01:14.600><c> user</c><00:01:14.920><c> query</c><00:01:15.840><c> right</c><00:01:16.520><c> so</c><00:01:17.240><c> uh</c><00:01:17.759><c> these</c><00:01:18.080><c> are</c>

00:01:18.310 --> 00:01:18.320 align:start position:0%
task or user query right so uh these are
 

00:01:18.320 --> 00:01:20.870 align:start position:0%
task or user query right so uh these are
done<00:01:18.920><c> uh</c><00:01:19.080><c> based</c><00:01:19.400><c> on</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
done uh based on
 

00:01:20.880 --> 00:01:24.230 align:start position:0%
done uh based on
the<00:01:21.880><c> um</c><00:01:22.119><c> retrieval</c><00:01:22.799><c> process</c><00:01:23.360><c> which</c><00:01:23.520><c> is</c><00:01:24.119><c> uh</c>

00:01:24.230 --> 00:01:24.240 align:start position:0%
the um retrieval process which is uh
 

00:01:24.240 --> 00:01:25.830 align:start position:0%
the um retrieval process which is uh
using<00:01:24.680><c> the</c>

00:01:25.830 --> 00:01:25.840 align:start position:0%
using the
 

00:01:25.840 --> 00:01:29.109 align:start position:0%
using the
embeddings<00:01:26.840><c> so</c><00:01:27.159><c> by</c><00:01:27.360><c> taking</c><00:01:27.720><c> the</c><00:01:28.360><c> description</c>

00:01:29.109 --> 00:01:29.119 align:start position:0%
embeddings so by taking the description
 

00:01:29.119 --> 00:01:32.749 align:start position:0%
embeddings so by taking the description
and<00:01:29.720><c> um</c><00:01:30.040><c> and</c><00:01:30.200><c> the</c><00:01:30.320><c> name</c><00:01:30.759><c> of</c><00:01:30.960><c> the</c><00:01:31.280><c> tools</c><00:01:32.280><c> U</c><00:01:32.479><c> using</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
and um and the name of the tools U using
 

00:01:32.759 --> 00:01:35.230 align:start position:0%
and um and the name of the tools U using
the<00:01:33.079><c> embeddings</c><00:01:34.079><c> for</c><00:01:34.280><c> the</c><00:01:34.399><c> user</c><00:01:34.720><c> query</c><00:01:35.119><c> it</c>

00:01:35.230 --> 00:01:35.240 align:start position:0%
the embeddings for the user query it
 

00:01:35.240 --> 00:01:38.630 align:start position:0%
the embeddings for the user query it
will<00:01:35.399><c> retrieve</c><00:01:36.040><c> the</c><00:01:37.040><c> correct</c><00:01:37.759><c> uh</c><00:01:38.000><c> tools</c><00:01:38.399><c> based</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
will retrieve the correct uh tools based
 

00:01:38.640 --> 00:01:40.670 align:start position:0%
will retrieve the correct uh tools based
on<00:01:38.759><c> the</c><00:01:38.840><c> embeddings</c><00:01:39.439><c> and</c><00:01:39.600><c> then</c><00:01:39.799><c> use</c><00:01:40.119><c> those</c><00:01:40.560><c> uh</c>

00:01:40.670 --> 00:01:40.680 align:start position:0%
on the embeddings and then use those uh
 

00:01:40.680 --> 00:01:42.710 align:start position:0%
on the embeddings and then use those uh
two<00:01:40.880><c> or</c><00:01:41.079><c> three</c><00:01:41.280><c> tools</c><00:01:41.880><c> uh</c><00:01:42.000><c> based</c><00:01:42.240><c> on</c><00:01:42.360><c> the</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
two or three tools uh based on the
 

00:01:42.720 --> 00:01:45.310 align:start position:0%
two or three tools uh based on the
similarity<00:01:43.720><c> top</c><00:01:44.040><c> K</c><00:01:44.479><c> value</c><00:01:44.799><c> that</c><00:01:44.960><c> you</c><00:01:45.079><c> have</c>

00:01:45.310 --> 00:01:45.320 align:start position:0%
similarity top K value that you have
 

00:01:45.320 --> 00:01:48.990 align:start position:0%
similarity top K value that you have
provided<00:01:46.320><c> and</c><00:01:46.960><c> uh</c><00:01:47.960><c> generate</c><00:01:48.280><c> a</c><00:01:48.439><c> response</c>

00:01:48.990 --> 00:01:49.000 align:start position:0%
provided and uh generate a response
 

00:01:49.000 --> 00:01:51.789 align:start position:0%
provided and uh generate a response
accordingly<00:01:50.000><c> so</c><00:01:50.840><c> this</c><00:01:50.960><c> is</c><00:01:51.079><c> the</c><00:01:51.240><c> process</c><00:01:51.560><c> we'll</c>

00:01:51.789 --> 00:01:51.799 align:start position:0%
accordingly so this is the process we'll
 

00:01:51.799 --> 00:01:54.550 align:start position:0%
accordingly so this is the process we'll
follow<00:01:52.719><c> uh</c><00:01:52.880><c> we'll</c><00:01:53.399><c> uh</c><00:01:53.560><c> look</c><00:01:53.799><c> into</c><00:01:54.079><c> how</c><00:01:54.240><c> you</c><00:01:54.360><c> can</c>

00:01:54.550 --> 00:01:54.560 align:start position:0%
follow uh we'll uh look into how you can
 

00:01:54.560 --> 00:01:59.230 align:start position:0%
follow uh we'll uh look into how you can
use<00:01:54.920><c> that</c><00:01:56.280><c> so</c><00:01:57.280><c> we'll</c><00:01:57.960><c> go</c><00:01:58.200><c> with</c><00:01:58.560><c> uh</c><00:01:58.759><c> calculator</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
use that so we'll go with uh calculator
 

00:01:59.240 --> 00:02:02.910 align:start position:0%
use that so we'll go with uh calculator
tools<00:01:59.600><c> what</c><00:01:59.960><c> we</c><00:02:00.079><c> do</c><00:02:00.280><c> is</c><00:02:00.680><c> we'll</c><00:02:01.680><c> use</c><00:02:02.000><c> the</c><00:02:02.200><c> same</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
tools what we do is we'll use the same
 

00:02:02.920 --> 00:02:06.069 align:start position:0%
tools what we do is we'll use the same
uh<00:02:03.280><c> multiply</c><00:02:04.039><c> and</c><00:02:04.360><c> add</c><00:02:04.719><c> and</c><00:02:04.960><c> subtract</c><00:02:05.399><c> tools</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
uh multiply and add and subtract tools
 

00:02:06.079 --> 00:02:08.589 align:start position:0%
uh multiply and add and subtract tools
but<00:02:06.240><c> we'll</c><00:02:06.520><c> Define</c><00:02:06.880><c> some</c><00:02:07.320><c> uh</c><00:02:07.880><c> useless</c><00:02:08.440><c> uh</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
but we'll Define some uh useless uh
 

00:02:08.599 --> 00:02:11.070 align:start position:0%
but we'll Define some uh useless uh
function<00:02:09.160><c> along</c><00:02:09.440><c> with</c><00:02:09.640><c> it</c><00:02:10.280><c> and</c><00:02:10.879><c> just</c>

00:02:11.070 --> 00:02:11.080 align:start position:0%
function along with it and just
 

00:02:11.080 --> 00:02:13.510 align:start position:0%
function along with it and just
replicate<00:02:11.560><c> useless</c><00:02:12.040><c> function</c><00:02:12.400><c> like</c><00:02:12.720><c> uh</c><00:02:12.879><c> 50</c>

00:02:13.510 --> 00:02:13.520 align:start position:0%
replicate useless function like uh 50
 

00:02:13.520 --> 00:02:17.229 align:start position:0%
replicate useless function like uh 50
times<00:02:14.519><c> and</c><00:02:14.720><c> then</c><00:02:14.920><c> so</c><00:02:15.599><c> uh</c><00:02:15.800><c> here</c><00:02:16.080><c> we</c><00:02:16.239><c> have</c><00:02:16.440><c> 50</c><00:02:16.800><c> +</c><00:02:17.040><c> 3</c>

00:02:17.229 --> 00:02:17.239 align:start position:0%
times and then so uh here we have 50 + 3
 

00:02:17.239 --> 00:02:19.390 align:start position:0%
times and then so uh here we have 50 + 3
53<00:02:17.640><c> Tools</c><00:02:18.080><c> in</c><00:02:18.400><c> in</c>

00:02:19.390 --> 00:02:19.400 align:start position:0%
53 Tools in in
 

00:02:19.400 --> 00:02:24.190 align:start position:0%
53 Tools in in
total<00:02:20.400><c> right</c><00:02:21.319><c> and</c><00:02:22.080><c> uh</c><00:02:23.080><c> I</c><00:02:23.239><c> have</c><00:02:23.440><c> this</c><00:02:23.640><c> all</c><00:02:23.920><c> tools</c>

00:02:24.190 --> 00:02:24.200 align:start position:0%
total right and uh I have this all tools
 

00:02:24.200 --> 00:02:27.190 align:start position:0%
total right and uh I have this all tools
in<00:02:24.360><c> it</c><00:02:24.720><c> one</c><00:02:24.920><c> it</c><00:02:25.680><c> and</c><00:02:25.879><c> by</c><00:02:26.040><c> default</c><00:02:26.360><c> it</c><00:02:26.480><c> uses</c><00:02:26.959><c> open</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
in it one it and by default it uses open
 

00:02:27.200 --> 00:02:30.630 align:start position:0%
in it one it and by default it uses open
a<00:02:27.360><c> embeddings</c><00:02:28.319><c> um</c><00:02:28.720><c> and</c><00:02:28.959><c> we'll</c><00:02:29.239><c> create</c><00:02:30.040><c> object</c>

00:02:30.630 --> 00:02:30.640 align:start position:0%
a embeddings um and we'll create object
 

00:02:30.640 --> 00:02:34.270 align:start position:0%
a embeddings um and we'll create object
index<00:02:31.640><c> so</c><00:02:32.160><c> uh</c><00:02:32.319><c> what</c><00:02:32.480><c> is</c><00:02:32.959><c> uh</c><00:02:33.200><c> object</c><00:02:33.560><c> index</c><00:02:34.000><c> does</c>

00:02:34.270 --> 00:02:34.280 align:start position:0%
index so uh what is uh object index does
 

00:02:34.280 --> 00:02:38.070 align:start position:0%
index so uh what is uh object index does
is<00:02:34.720><c> uh</c><00:02:35.040><c> um</c><00:02:35.360><c> it's</c><00:02:35.560><c> like</c><00:02:35.959><c> vector</c><00:02:36.239><c> store</c><00:02:36.680><c> index</c><00:02:37.680><c> uh</c>

00:02:38.070 --> 00:02:38.080 align:start position:0%
is uh um it's like vector store index uh
 

00:02:38.080 --> 00:02:41.949 align:start position:0%
is uh um it's like vector store index uh
uses<00:02:38.720><c> all</c><00:02:39.000><c> these</c><00:02:40.040><c> tools</c><00:02:41.040><c> uh</c><00:02:41.239><c> whatever</c><00:02:41.599><c> we</c><00:02:41.760><c> have</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
uses all these tools uh whatever we have
 

00:02:41.959 --> 00:02:46.509 align:start position:0%
uses all these tools uh whatever we have
defined<00:02:42.519><c> over</c><00:02:43.000><c> here</c><00:02:44.000><c> and</c><00:02:44.560><c> uh</c><00:02:44.720><c> during</c><00:02:45.400><c> uh</c><00:02:45.599><c> the</c>

00:02:46.509 --> 00:02:46.519 align:start position:0%
defined over here and uh during uh the
 

00:02:46.519 --> 00:02:49.750 align:start position:0%
defined over here and uh during uh the
function<00:02:46.920><c> calling</c><00:02:47.800><c> uh</c><00:02:48.800><c> purposes</c><00:02:49.319><c> like</c><00:02:49.519><c> the</c>

00:02:49.750 --> 00:02:49.760 align:start position:0%
function calling uh purposes like the
 

00:02:49.760 --> 00:02:52.229 align:start position:0%
function calling uh purposes like the
function<00:02:50.080><c> calling</c><00:02:50.400><c> agent</c><00:02:51.280><c> uh</c><00:02:51.400><c> we</c><00:02:51.560><c> Define</c><00:02:51.920><c> tool</c>

00:02:52.229 --> 00:02:52.239 align:start position:0%
function calling agent uh we Define tool
 

00:02:52.239 --> 00:02:56.390 align:start position:0%
function calling agent uh we Define tool
retriever<00:02:53.200><c> so</c><00:02:53.560><c> using</c><00:02:54.000><c> this</c><00:02:54.519><c> whatever</c><00:02:55.400><c> uh</c>

00:02:56.390 --> 00:02:56.400 align:start position:0%
retriever so using this whatever uh
 

00:02:56.400 --> 00:02:59.070 align:start position:0%
retriever so using this whatever uh
tools<00:02:56.840><c> that</c><00:02:56.959><c> are</c><00:02:57.720><c> uh</c><00:02:57.879><c> retrieved</c><00:02:58.560><c> during</c><00:02:58.879><c> the</c>

00:02:59.070 --> 00:02:59.080 align:start position:0%
tools that are uh retrieved during the
 

00:02:59.080 --> 00:03:00.670 align:start position:0%
tools that are uh retrieved during the
quiring<00:02:59.480><c> stage</c>

00:03:00.670 --> 00:03:00.680 align:start position:0%
quiring stage
 

00:03:00.680 --> 00:03:03.030 align:start position:0%
quiring stage
uh<00:03:01.360><c> the</c><00:03:01.480><c> function</c><00:03:01.840><c> calling</c><00:03:02.239><c> agent</c><00:03:02.680><c> worker</c>

00:03:03.030 --> 00:03:03.040 align:start position:0%
uh the function calling agent worker
 

00:03:03.040 --> 00:03:05.589 align:start position:0%
uh the function calling agent worker
uses<00:03:03.440><c> only</c><00:03:03.799><c> those</c><00:03:04.040><c> tools</c><00:03:04.720><c> and</c><00:03:04.959><c> then</c><00:03:05.440><c> uh</c>

00:03:05.589 --> 00:03:05.599 align:start position:0%
uses only those tools and then uh
 

00:03:05.599 --> 00:03:09.710 align:start position:0%
uses only those tools and then uh
computes<00:03:06.040><c> the</c><00:03:06.200><c> task</c><00:03:07.159><c> right</c><00:03:08.040><c> so</c><00:03:09.040><c> let's</c><00:03:09.280><c> see</c>

00:03:09.710 --> 00:03:09.720 align:start position:0%
computes the task right so let's see
 

00:03:09.720 --> 00:03:13.229 align:start position:0%
computes the task right so let's see
like<00:03:10.159><c> um</c><00:03:11.120><c> one</c><00:03:11.280><c> of</c><00:03:11.400><c> the</c><00:03:11.560><c> query</c><00:03:11.920><c> is</c><00:03:12.360><c> what's</c><00:03:12.760><c> 500</c>

00:03:13.229 --> 00:03:13.239 align:start position:0%
like um one of the query is what's 500
 

00:03:13.239 --> 00:03:15.949 align:start position:0%
like um one of the query is what's 500
multiplied<00:03:13.799><c> by</c><00:03:13.920><c> 10</c><00:03:14.519><c> right</c>

00:03:15.949 --> 00:03:15.959 align:start position:0%
multiplied by 10 right
 

00:03:15.959 --> 00:03:19.190 align:start position:0%
multiplied by 10 right
so<00:03:16.959><c> uh</c><00:03:17.560><c> here</c><00:03:17.760><c> we'll</c>

00:03:19.190 --> 00:03:19.200 align:start position:0%
so uh here we'll
 

00:03:19.200 --> 00:03:23.229 align:start position:0%
so uh here we'll
see<00:03:20.200><c> so</c><00:03:20.519><c> first</c><00:03:20.760><c> it</c><00:03:20.959><c> used</c><00:03:21.680><c> multiply</c><00:03:22.680><c> uh</c><00:03:23.080><c> and</c>

00:03:23.229 --> 00:03:23.239 align:start position:0%
see so first it used multiply uh and
 

00:03:23.239 --> 00:03:26.750 align:start position:0%
see so first it used multiply uh and
then<00:03:23.519><c> got</c><00:03:23.760><c> the</c><00:03:23.959><c> response</c><00:03:25.040><c> right</c><00:03:26.040><c> and</c><00:03:26.519><c> let's</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
then got the response right and let's
 

00:03:26.760 --> 00:03:30.429 align:start position:0%
then got the response right and let's
say<00:03:27.599><c> uh</c><00:03:28.599><c> what's</c><00:03:29.040><c> 500</c><00:03:29.400><c> multiply</c><00:03:29.879><c> by</c><00:03:30.000><c> 10</c><00:03:30.280><c> and</c>

00:03:30.429 --> 00:03:30.439 align:start position:0%
say uh what's 500 multiply by 10 and
 

00:03:30.439 --> 00:03:35.309 align:start position:0%
say uh what's 500 multiply by 10 and
then<00:03:30.640><c> subtract</c><00:03:31.799><c> th000</c><00:03:32.799><c> so</c><00:03:33.640><c> here</c><00:03:33.840><c> it</c><00:03:34.000><c> used</c><00:03:34.319><c> to</c>

00:03:35.309 --> 00:03:35.319 align:start position:0%
then subtract th000 so here it used to
 

00:03:35.319 --> 00:03:37.990 align:start position:0%
then subtract th000 so here it used to
uh<00:03:35.480><c> multiply</c><00:03:36.080><c> and</c><00:03:36.239><c> then</c><00:03:36.519><c> subtract</c><00:03:37.519><c> so</c><00:03:37.799><c> that's</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
uh multiply and then subtract so that's
 

00:03:38.000 --> 00:03:41.589 align:start position:0%
uh multiply and then subtract so that's
how<00:03:38.319><c> it</c><00:03:38.799><c> two</c><00:03:38.959><c> tools</c><00:03:39.640><c> but</c><00:03:40.040><c> uh</c><00:03:40.159><c> but</c><00:03:40.360><c> then</c><00:03:40.959><c> uh</c><00:03:41.159><c> used</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
how it two tools but uh but then uh used
 

00:03:41.599 --> 00:03:43.750 align:start position:0%
how it two tools but uh but then uh used
multiply<00:03:42.080><c> tool</c><00:03:42.439><c> and</c><00:03:42.879><c> subtract</c><00:03:43.319><c> tool</c><00:03:43.599><c> to</c>

00:03:43.750 --> 00:03:43.760 align:start position:0%
multiply tool and subtract tool to
 

00:03:43.760 --> 00:03:45.309 align:start position:0%
multiply tool and subtract tool to
generate<00:03:44.040><c> a</c><00:03:44.200><c> response</c>

00:03:45.309 --> 00:03:45.319 align:start position:0%
generate a response
 

00:03:45.319 --> 00:03:47.990 align:start position:0%
generate a response
accordingly<00:03:46.319><c> uh</c><00:03:46.519><c> so</c><00:03:46.840><c> let's</c><00:03:47.040><c> see</c><00:03:47.680><c> uh</c><00:03:47.840><c> with</c>

00:03:47.990 --> 00:03:48.000 align:start position:0%
accordingly uh so let's see uh with
 

00:03:48.000 --> 00:03:51.149 align:start position:0%
accordingly uh so let's see uh with
Sonet<00:03:48.519><c> as</c><00:03:48.640><c> well</c><00:03:48.840><c> anthropic</c><00:03:49.720><c> Sonet</c><00:03:50.720><c> uh</c><00:03:50.879><c> we'll</c>

00:03:51.149 --> 00:03:51.159 align:start position:0%
Sonet as well anthropic Sonet uh we'll
 

00:03:51.159 --> 00:03:53.509 align:start position:0%
Sonet as well anthropic Sonet uh we'll
Define<00:03:52.000><c> um</c><00:03:52.280><c> the</c><00:03:52.439><c> function</c><00:03:52.799><c> calling</c><00:03:53.319><c> with</c><00:03:53.439><c> the</c>

00:03:53.509 --> 00:03:53.519 align:start position:0%
Define um the function calling with the
 

00:03:53.519 --> 00:03:55.990 align:start position:0%
Define um the function calling with the
tool<00:03:53.840><c> retri</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
tool retri
 

00:03:56.000 --> 00:04:02.110 align:start position:0%
tool retri
and<00:03:57.000><c> compute</c><00:03:57.360><c> the</c><00:03:57.519><c> same</c><00:03:58.439><c> uh</c><00:03:58.599><c> queries</c><00:03:59.120><c> again</c>

00:04:02.110 --> 00:04:02.120 align:start position:0%
 
 

00:04:02.120 --> 00:04:06.869 align:start position:0%
 
so<00:04:02.560><c> here</c><00:04:02.879><c> we</c><00:04:03.400><c> have</c><00:04:03.640><c> multiply</c><00:04:04.519><c> and</c><00:04:04.720><c> then</c><00:04:05.799><c> again</c>

00:04:06.869 --> 00:04:06.879 align:start position:0%
so here we have multiply and then again
 

00:04:06.879 --> 00:04:09.030 align:start position:0%
so here we have multiply and then again
multiply

00:04:09.030 --> 00:04:09.040 align:start position:0%
multiply
 

00:04:09.040 --> 00:04:14.229 align:start position:0%
multiply
and<00:04:10.439><c> subtract</c><00:04:11.439><c> uh</c><00:04:11.879><c> so</c><00:04:12.360><c> 4,000</c><00:04:13.360><c> so</c><00:04:13.599><c> even</c><00:04:14.079><c> there</c>

00:04:14.229 --> 00:04:14.239 align:start position:0%
and subtract uh so 4,000 so even there
 

00:04:14.239 --> 00:04:17.710 align:start position:0%
and subtract uh so 4,000 so even there
are<00:04:14.640><c> 53</c><00:04:15.159><c> tools</c><00:04:15.680><c> available</c><00:04:16.680><c> I</c><00:04:16.959><c> used</c><00:04:17.280><c> only</c><00:04:17.560><c> these</c>

00:04:17.710 --> 00:04:17.720 align:start position:0%
are 53 tools available I used only these
 

00:04:17.720 --> 00:04:21.710 align:start position:0%
are 53 tools available I used only these
two<00:04:17.919><c> tools</c><00:04:18.280><c> to</c><00:04:18.880><c> uh</c><00:04:19.120><c> complete</c><00:04:19.600><c> the</c><00:04:20.479><c> task</c><00:04:21.479><c> next</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
two tools to uh complete the task next
 

00:04:21.720 --> 00:04:25.990 align:start position:0%
two tools to uh complete the task next
we'll<00:04:22.120><c> go</c><00:04:22.400><c> with</c><00:04:22.840><c> mral</c><00:04:23.759><c> AI</c><00:04:24.759><c> uh</c><00:04:24.919><c> Mr</c>

00:04:25.990 --> 00:04:26.000 align:start position:0%
we'll go with mral AI uh Mr
 

00:04:26.000 --> 00:04:29.310 align:start position:0%
we'll go with mral AI uh Mr
llms<00:04:27.000><c> and</c><00:04:27.199><c> then</c><00:04:27.440><c> see</c><00:04:28.160><c> uh</c><00:04:28.280><c> if</c><00:04:28.400><c> it's</c><00:04:28.639><c> working</c><00:04:29.120><c> as</c>

00:04:29.310 --> 00:04:29.320 align:start position:0%
llms and then see uh if it's working as
 

00:04:29.320 --> 00:04:32.350 align:start position:0%
llms and then see uh if it's working as
expected

00:04:32.350 --> 00:04:32.360 align:start position:0%
 
 

00:04:32.360 --> 00:04:36.029 align:start position:0%
 
yeah<00:04:32.759><c> so</c><00:04:33.600><c> here</c><00:04:34.120><c> uh</c><00:04:34.320><c> same</c><00:04:34.600><c> way</c><00:04:35.240><c> uh</c><00:04:35.360><c> it</c><00:04:35.520><c> used</c>

00:04:36.029 --> 00:04:36.039 align:start position:0%
yeah so here uh same way uh it used
 

00:04:36.039 --> 00:04:38.310 align:start position:0%
yeah so here uh same way uh it used
multiply<00:04:36.560><c> tool</c><00:04:36.880><c> for</c><00:04:37.120><c> the</c><00:04:37.479><c> first</c><00:04:37.840><c> one</c><00:04:38.039><c> and</c><00:04:38.160><c> then</c>

00:04:38.310 --> 00:04:38.320 align:start position:0%
multiply tool for the first one and then
 

00:04:38.320 --> 00:04:41.150 align:start position:0%
multiply tool for the first one and then
multiply<00:04:39.120><c> and</c><00:04:39.400><c> addition</c><00:04:40.400><c> uh</c><00:04:40.560><c> subtraction</c>

00:04:41.150 --> 00:04:41.160 align:start position:0%
multiply and addition uh subtraction
 

00:04:41.160 --> 00:04:44.110 align:start position:0%
multiply and addition uh subtraction
sorry<00:04:41.960><c> uh</c><00:04:42.160><c> to</c><00:04:42.800><c> uh</c><00:04:42.960><c> get</c><00:04:43.120><c> the</c>

00:04:44.110 --> 00:04:44.120 align:start position:0%
sorry uh to uh get the
 

00:04:44.120 --> 00:04:46.909 align:start position:0%
sorry uh to uh get the
response<00:04:45.120><c> next</c><00:04:45.320><c> we'll</c><00:04:45.560><c> do</c><00:04:45.960><c> the</c><00:04:46.080><c> same</c><00:04:46.360><c> with</c><00:04:46.720><c> uh</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
response next we'll do the same with uh
 

00:04:46.919 --> 00:04:50.469 align:start position:0%
response next we'll do the same with uh
rag<00:04:47.720><c> uh</c><00:04:48.080><c> in</c><00:04:48.400><c> query</c><00:04:48.680><c> engine</c><00:04:48.960><c> tools</c><00:04:49.360><c> as</c><00:04:49.520><c> well</c><00:04:50.320><c> uh</c>

00:04:50.469 --> 00:04:50.479 align:start position:0%
rag uh in query engine tools as well uh
 

00:04:50.479 --> 00:04:54.110 align:start position:0%
rag uh in query engine tools as well uh
in<00:04:50.560><c> the</c><00:04:50.720><c> similar</c><00:04:51.120><c> way</c><00:04:51.360><c> we'll</c><00:04:51.960><c> uh</c><00:04:52.960><c> um</c><00:04:53.479><c> use</c><00:04:54.000><c> uh</c>

00:04:54.110 --> 00:04:54.120 align:start position:0%
in the similar way we'll uh um use uh
 

00:04:54.120 --> 00:04:59.070 align:start position:0%
in the similar way we'll uh um use uh
lift<00:04:54.639><c> and</c><00:04:54.919><c> Uber</c><00:04:55.680><c> 2021</c><00:04:56.680><c> um</c><00:04:56.880><c> 10kc</c><00:04:57.919><c> filings</c><00:04:58.919><c> uh</c>

00:04:59.070 --> 00:04:59.080 align:start position:0%
lift and Uber 2021 um 10kc filings uh
 

00:04:59.080 --> 00:05:01.189 align:start position:0%
lift and Uber 2021 um 10kc filings uh
create<00:04:59.400><c> in</c><00:04:59.720><c> index</c><00:05:00.080><c> load</c><00:05:00.360><c> the</c><00:05:00.520><c> data</c><00:05:00.720><c> and</c><00:05:00.880><c> create</c>

00:05:01.189 --> 00:05:01.199 align:start position:0%
create in index load the data and create
 

00:05:01.199 --> 00:05:02.870 align:start position:0%
create in index load the data and create
index<00:05:01.560><c> and</c><00:05:01.720><c> then</c><00:05:01.960><c> query</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
index and then query
 

00:05:02.880 --> 00:05:07.310 align:start position:0%
index and then query
engines<00:05:03.880><c> um</c><00:05:04.720><c> so</c><00:05:05.720><c> and</c><00:05:05.919><c> then</c><00:05:06.080><c> we'll</c><00:05:06.440><c> even</c><00:05:07.080><c> um</c>

00:05:07.310 --> 00:05:07.320 align:start position:0%
engines um so and then we'll even um
 

00:05:07.320 --> 00:05:09.950 align:start position:0%
engines um so and then we'll even um
Define<00:05:07.720><c> the</c><00:05:07.840><c> tools</c><00:05:08.800><c> on</c><00:05:09.039><c> top</c><00:05:09.199><c> of</c><00:05:09.360><c> that</c><00:05:09.840><c> uh</c>

00:05:09.950 --> 00:05:09.960 align:start position:0%
Define the tools on top of that uh
 

00:05:09.960 --> 00:05:11.710 align:start position:0%
Define the tools on top of that uh
create<00:05:10.320><c> the</c><00:05:10.600><c> object</c>

00:05:11.710 --> 00:05:11.720 align:start position:0%
create the object
 

00:05:11.720 --> 00:05:15.270 align:start position:0%
create the object
index<00:05:12.720><c> and</c><00:05:12.919><c> then</c><00:05:13.400><c> uh</c><00:05:14.080><c> create</c><00:05:14.440><c> a</c><00:05:14.680><c> retriever</c><00:05:15.120><c> on</c>

00:05:15.270 --> 00:05:15.280 align:start position:0%
index and then uh create a retriever on
 

00:05:15.280 --> 00:05:17.550 align:start position:0%
index and then uh create a retriever on
top<00:05:15.440><c> of</c><00:05:15.560><c> it</c><00:05:15.759><c> we'll</c><00:05:16.039><c> just</c><00:05:16.680><c> uh</c><00:05:16.919><c> retrieve</c><00:05:17.360><c> only</c>

00:05:17.550 --> 00:05:17.560 align:start position:0%
top of it we'll just uh retrieve only
 

00:05:17.560 --> 00:05:19.710 align:start position:0%
top of it we'll just uh retrieve only
one<00:05:17.680><c> of</c><00:05:17.840><c> these</c><00:05:18.000><c> tools</c><00:05:18.240><c> of</c><00:05:18.400><c> the</c><00:05:18.520><c> two</c><00:05:18.680><c> tools</c><00:05:19.520><c> and</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
one of these tools of the two tools and
 

00:05:19.720 --> 00:05:24.029 align:start position:0%
one of these tools of the two tools and
then<00:05:20.240><c> ask</c><00:05:20.759><c> uh</c><00:05:21.360><c> simple</c><00:05:21.680><c> queries</c><00:05:22.199><c> over</c><00:05:22.479><c> it</c><00:05:23.479><c> um</c>

00:05:24.029 --> 00:05:24.039 align:start position:0%
then ask uh simple queries over it um
 

00:05:24.039 --> 00:05:26.070 align:start position:0%
then ask uh simple queries over it um
still<00:05:24.400><c> loading</c><00:05:24.840><c> the</c>

00:05:26.070 --> 00:05:26.080 align:start position:0%
still loading the
 

00:05:26.080 --> 00:05:28.790 align:start position:0%
still loading the
data<00:05:27.080><c> yeah</c><00:05:27.280><c> obviously</c><00:05:27.680><c> this</c><00:05:27.800><c> will</c><00:05:28.039><c> take</c>

00:05:28.790 --> 00:05:28.800 align:start position:0%
data yeah obviously this will take
 

00:05:28.800 --> 00:05:33.110 align:start position:0%
data yeah obviously this will take
probably<00:05:29.319><c> a</c><00:05:29.919><c> or</c><00:05:30.080><c> two</c><00:05:31.039><c> because</c><00:05:31.680><c> of</c><00:05:32.319><c> huge</c><00:05:32.840><c> number</c>

00:05:33.110 --> 00:05:33.120 align:start position:0%
probably a or two because of huge number
 

00:05:33.120 --> 00:05:36.909 align:start position:0%
probably a or two because of huge number
of<00:05:33.560><c> pages</c><00:05:33.880><c> in</c><00:05:34.120><c> each</c><00:05:34.400><c> document</c><00:05:34.840><c> like</c><00:05:35.080><c> 300</c>

00:05:36.909 --> 00:05:36.919 align:start position:0%
of pages in each document like 300
 

00:05:36.919 --> 00:05:40.790 align:start position:0%
of pages in each document like 300
plus

00:05:40.790 --> 00:05:40.800 align:start position:0%
 
 

00:05:40.800 --> 00:05:45.830 align:start position:0%
 
and<00:05:41.800><c> should</c><00:05:42.000><c> be</c><00:05:42.160><c> done</c><00:05:43.000><c> um</c><00:05:43.919><c> in</c><00:05:44.160><c> some</c><00:05:44.479><c> time</c><00:05:45.440><c> yeah</c>

00:05:45.830 --> 00:05:45.840 align:start position:0%
and should be done um in some time yeah
 

00:05:45.840 --> 00:05:48.909 align:start position:0%
and should be done um in some time yeah
it's<00:05:46.080><c> mostly</c><00:05:46.520><c> done</c><00:05:47.520><c> and</c><00:05:47.800><c> then</c><00:05:48.319><c> we'll</c><00:05:48.600><c> create</c>

00:05:48.909 --> 00:05:48.919 align:start position:0%
it's mostly done and then we'll create
 

00:05:48.919 --> 00:05:50.309 align:start position:0%
it's mostly done and then we'll create
the<00:05:49.080><c> query</c>

00:05:50.309 --> 00:05:50.319 align:start position:0%
the query
 

00:05:50.319 --> 00:05:54.390 align:start position:0%
the query
engines<00:05:51.319><c> and</c><00:05:51.600><c> then</c><00:05:52.160><c> the</c><00:05:52.800><c> tools</c><00:05:53.800><c> and</c><00:05:53.919><c> then</c><00:05:54.120><c> the</c>

00:05:54.390 --> 00:05:54.400 align:start position:0%
engines and then the tools and then the
 

00:05:54.400 --> 00:05:56.270 align:start position:0%
engines and then the tools and then the
object

00:05:56.270 --> 00:05:56.280 align:start position:0%
object
 

00:05:56.280 --> 00:06:00.070 align:start position:0%
object
Index<00:05:57.400><c> right</c><00:05:58.400><c> and</c><00:05:58.560><c> then</c><00:05:58.759><c> we'll</c><00:05:59.080><c> have</c><00:05:59.479><c> uh</c><00:05:59.919><c> this</c>

00:06:00.070 --> 00:06:00.080 align:start position:0%
Index right and then we'll have uh this
 

00:06:00.080 --> 00:06:03.029 align:start position:0%
Index right and then we'll have uh this
tool<00:06:00.880><c> in</c><00:06:01.280><c> uh</c><00:06:01.400><c> function</c><00:06:01.759><c> calling</c><00:06:02.160><c> agent</c><00:06:02.520><c> worker</c>

00:06:03.029 --> 00:06:03.039 align:start position:0%
tool in uh function calling agent worker
 

00:06:03.039 --> 00:06:05.070 align:start position:0%
tool in uh function calling agent worker
and<00:06:03.199><c> then</c><00:06:03.440><c> Define</c><00:06:04.080><c> the</c><00:06:04.319><c> function</c><00:06:04.720><c> calling</c>

00:06:05.070 --> 00:06:05.080 align:start position:0%
and then Define the function calling
 

00:06:05.080 --> 00:06:08.510 align:start position:0%
and then Define the function calling
agent<00:06:06.039><c> uh</c><00:06:06.479><c> here</c><00:06:07.479><c> and</c><00:06:07.680><c> then</c><00:06:07.880><c> we'll</c><00:06:08.160><c> have</c><00:06:08.360><c> a</c>

00:06:08.510 --> 00:06:08.520 align:start position:0%
agent uh here and then we'll have a
 

00:06:08.520 --> 00:06:10.270 align:start position:0%
agent uh here and then we'll have a
simple<00:06:08.840><c> query</c><00:06:09.199><c> what</c><00:06:09.319><c> is</c><00:06:09.400><c> the</c><00:06:09.520><c> revenue</c><00:06:09.880><c> of</c><00:06:10.000><c> uber</c>

00:06:10.270 --> 00:06:10.280 align:start position:0%
simple query what is the revenue of uber
 

00:06:10.280 --> 00:06:14.710 align:start position:0%
simple query what is the revenue of uber
in<00:06:10.479><c> 2021</c><00:06:11.479><c> uh</c><00:06:11.599><c> it</c><00:06:11.720><c> should</c><00:06:11.919><c> ideally</c><00:06:12.280><c> use</c><00:06:13.000><c> Uber</c><00:06:14.000><c> um</c>

00:06:14.710 --> 00:06:14.720 align:start position:0%
in 2021 uh it should ideally use Uber um
 

00:06:14.720 --> 00:06:18.589 align:start position:0%
in 2021 uh it should ideally use Uber um
tool<00:06:15.560><c> rate</c><00:06:16.560><c> yeah</c><00:06:17.080><c> so</c><00:06:17.440><c> the</c><00:06:17.560><c> revenue</c><00:06:18.000><c> of</c><00:06:18.240><c> ubber</c>

00:06:18.589 --> 00:06:18.599 align:start position:0%
tool rate yeah so the revenue of ubber
 

00:06:18.599 --> 00:06:23.710 align:start position:0%
tool rate yeah so the revenue of ubber
is<00:06:19.560><c> 2021</c><00:06:20.319><c> was</c><00:06:21.319><c> um</c><00:06:21.880><c> 17.5</c><00:06:22.639><c> billion</c>

00:06:23.710 --> 00:06:23.720 align:start position:0%
is 2021 was um 17.5 billion
 

00:06:23.720 --> 00:06:26.189 align:start position:0%
is 2021 was um 17.5 billion
approximately<00:06:24.720><c> so</c><00:06:25.280><c> we</c><00:06:25.360><c> can</c><00:06:25.520><c> ask</c><00:06:25.800><c> similar</c>

00:06:26.189 --> 00:06:26.199 align:start position:0%
approximately so we can ask similar
 

00:06:26.199 --> 00:06:30.390 align:start position:0%
approximately so we can ask similar
query<00:06:26.599><c> for</c><00:06:26.919><c> Lift</c><00:06:27.360><c> as</c><00:06:27.960><c> well</c><00:06:28.960><c> so</c>

00:06:30.390 --> 00:06:30.400 align:start position:0%
query for Lift as well so
 

00:06:30.400 --> 00:06:33.070 align:start position:0%
query for Lift as well so
so<00:06:30.599><c> it</c><00:06:30.759><c> retrieved</c><00:06:31.520><c> uh</c><00:06:31.960><c> lift</c><00:06:32.400><c> query</c><00:06:32.720><c> engine</c>

00:06:33.070 --> 00:06:33.080 align:start position:0%
so it retrieved uh lift query engine
 

00:06:33.080 --> 00:06:37.390 align:start position:0%
so it retrieved uh lift query engine
tool<00:06:33.720><c> and</c><00:06:34.039><c> then</c><00:06:35.039><c> uh</c><00:06:35.639><c> gave</c><00:06:35.880><c> the</c><00:06:36.199><c> response</c><00:06:37.199><c> let's</c>

00:06:37.390 --> 00:06:37.400 align:start position:0%
tool and then uh gave the response let's
 

00:06:37.400 --> 00:06:39.670 align:start position:0%
tool and then uh gave the response let's
try<00:06:37.680><c> the</c><00:06:37.800><c> same</c><00:06:38.000><c> thing</c><00:06:38.240><c> with</c><00:06:38.840><c> anthropic</c><00:06:39.479><c> as</c>

00:06:39.670 --> 00:06:39.680 align:start position:0%
try the same thing with anthropic as
 

00:06:39.680 --> 00:06:42.550 align:start position:0%
try the same thing with anthropic as
well<00:06:40.520><c> so</c><00:06:40.720><c> we</c><00:06:40.919><c> defined</c><00:06:41.400><c> the</c><00:06:41.639><c> tool</c><00:06:41.960><c> retriever</c>

00:06:42.550 --> 00:06:42.560 align:start position:0%
well so we defined the tool retriever
 

00:06:42.560 --> 00:06:47.070 align:start position:0%
well so we defined the tool retriever
here<00:06:43.680><c> and</c><00:06:44.680><c> agent</c><00:06:45.199><c> here</c><00:06:45.639><c> and</c><00:06:45.800><c> then</c><00:06:46.280><c> similar</c>

00:06:47.070 --> 00:06:47.080 align:start position:0%
here and agent here and then similar
 

00:06:47.080 --> 00:06:51.309 align:start position:0%
here and agent here and then similar
queries<00:06:48.080><c> here</c><00:06:48.360><c> as</c>

00:06:51.309 --> 00:06:51.319 align:start position:0%
 
 

00:06:51.319 --> 00:06:56.029 align:start position:0%
 
well<00:06:52.759><c> so</c><00:06:53.759><c> so</c><00:06:54.120><c> yeah</c><00:06:54.880><c> uh</c><00:06:55.039><c> it</c><00:06:55.199><c> provide</c><00:06:55.599><c> the</c><00:06:55.759><c> right</c>

00:06:56.029 --> 00:06:56.039 align:start position:0%
well so so yeah uh it provide the right
 

00:06:56.039 --> 00:06:57.309 align:start position:0%
well so so yeah uh it provide the right
result

00:06:57.309 --> 00:06:57.319 align:start position:0%
result
 

00:06:57.319 --> 00:07:02.309 align:start position:0%
result
here<00:06:58.319><c> for</c><00:06:58.639><c> both</c><00:06:59.080><c> the</c><00:06:59.560><c> Uber</c><00:06:59.879><c> and</c><00:07:00.440><c> lift</c><00:07:01.440><c> right</c>

00:07:02.309 --> 00:07:02.319 align:start position:0%
here for both the Uber and lift right
 

00:07:02.319 --> 00:07:05.550 align:start position:0%
here for both the Uber and lift right
next<00:07:02.840><c> let's</c><00:07:03.080><c> try</c><00:07:03.440><c> with</c><00:07:03.680><c> Mr</c>

00:07:05.550 --> 00:07:05.560 align:start position:0%
next let's try with Mr
 

00:07:05.560 --> 00:07:07.550 align:start position:0%
next let's try with Mr
AI<00:07:06.560><c> all</c><00:07:06.800><c> the</c>

00:07:07.550 --> 00:07:07.560 align:start position:0%
AI all the
 

00:07:07.560 --> 00:07:09.670 align:start position:0%
AI all the
queries<00:07:08.560><c> uh</c><00:07:08.680><c> we</c><00:07:08.800><c> have</c><00:07:08.960><c> provided</c><00:07:09.319><c> the</c><00:07:09.440><c> tool</c>

00:07:09.670 --> 00:07:09.680 align:start position:0%
queries uh we have provided the tool
 

00:07:09.680 --> 00:07:11.629 align:start position:0%
queries uh we have provided the tool
retriever<00:07:10.280><c> here</c><00:07:10.759><c> uh</c><00:07:10.919><c> for</c><00:07:11.080><c> the</c><00:07:11.280><c> function</c>

00:07:11.629 --> 00:07:11.639 align:start position:0%
retriever here uh for the function
 

00:07:11.639 --> 00:07:12.510 align:start position:0%
retriever here uh for the function
calling

00:07:12.510 --> 00:07:12.520 align:start position:0%
calling
 

00:07:12.520 --> 00:07:14.189 align:start position:0%
calling
agent

00:07:14.189 --> 00:07:14.199 align:start position:0%
agent
 

00:07:14.199 --> 00:07:18.189 align:start position:0%
agent
and<00:07:15.199><c> yeah</c><00:07:15.720><c> uh</c><00:07:15.840><c> it</c><00:07:16.000><c> called</c><00:07:16.360><c> Uber</c><00:07:17.120><c> uh</c><00:07:17.479><c> 10kc</c>

00:07:18.189 --> 00:07:18.199 align:start position:0%
and yeah uh it called Uber uh 10kc
 

00:07:18.199 --> 00:07:20.909 align:start position:0%
and yeah uh it called Uber uh 10kc
filings<00:07:18.599><c> tool</c><00:07:19.080><c> and</c><00:07:19.280><c> got</c><00:07:19.520><c> the</c><00:07:19.720><c> response</c><00:07:20.720><c> and</c>

00:07:20.909 --> 00:07:20.919 align:start position:0%
filings tool and got the response and
 

00:07:20.919 --> 00:07:24.629 align:start position:0%
filings tool and got the response and
then<00:07:21.120><c> for</c><00:07:21.479><c> Lift</c><00:07:22.120><c> it's</c><00:07:22.360><c> lift</c><00:07:22.759><c> 10kc</c><00:07:23.360><c> filing</c><00:07:23.759><c> Tool</c>

00:07:24.629 --> 00:07:24.639 align:start position:0%
then for Lift it's lift 10kc filing Tool
 

00:07:24.639 --> 00:07:27.469 align:start position:0%
then for Lift it's lift 10kc filing Tool
uh<00:07:24.840><c> got</c><00:07:25.120><c> the</c><00:07:25.479><c> response</c><00:07:26.479><c> so</c><00:07:26.720><c> this</c><00:07:26.919><c> way</c><00:07:27.240><c> if</c><00:07:27.360><c> you</c>

00:07:27.469 --> 00:07:27.479 align:start position:0%
uh got the response so this way if you
 

00:07:27.479 --> 00:07:29.390 align:start position:0%
uh got the response so this way if you
have<00:07:27.680><c> large</c><00:07:27.960><c> number</c><00:07:28.199><c> of</c><00:07:28.360><c> tools</c><00:07:28.680><c> you</c><00:07:28.800><c> can</c><00:07:28.960><c> use</c>

00:07:29.390 --> 00:07:29.400 align:start position:0%
have large number of tools you can use
 

00:07:29.400 --> 00:07:31.749 align:start position:0%
have large number of tools you can use
uh<00:07:29.680><c> um</c><00:07:30.360><c> the</c><00:07:30.560><c> object</c><00:07:30.919><c> index</c><00:07:31.319><c> and</c><00:07:31.479><c> Tool</c>

00:07:31.749 --> 00:07:31.759 align:start position:0%
uh um the object index and Tool
 

00:07:31.759 --> 00:07:34.430 align:start position:0%
uh um the object index and Tool
retriever<00:07:32.240><c> abstractions</c><00:07:33.199><c> um</c><00:07:34.039><c> uh</c><00:07:34.160><c> with</c><00:07:34.319><c> the</c>

00:07:34.430 --> 00:07:34.440 align:start position:0%
retriever abstractions um uh with the
 

00:07:34.440 --> 00:07:36.189 align:start position:0%
retriever abstractions um uh with the
function<00:07:34.759><c> calling</c><00:07:35.120><c> agent</c><00:07:35.680><c> so</c><00:07:35.879><c> that</c><00:07:36.039><c> you</c>

00:07:36.189 --> 00:07:36.199 align:start position:0%
function calling agent so that you
 

00:07:36.199 --> 00:07:37.990 align:start position:0%
function calling agent so that you
retrieve<00:07:36.599><c> some</c><00:07:36.759><c> of</c><00:07:36.960><c> these</c><00:07:37.240><c> available</c><00:07:37.720><c> tools</c>

00:07:37.990 --> 00:07:38.000 align:start position:0%
retrieve some of these available tools
 

00:07:38.000 --> 00:07:41.110 align:start position:0%
retrieve some of these available tools
for<00:07:38.160><c> the</c><00:07:38.319><c> given</c><00:07:38.879><c> uh</c><00:07:38.960><c> user</c><00:07:39.560><c> query</c><00:07:39.919><c> or</c><00:07:40.039><c> the</c><00:07:40.240><c> task</c>

00:07:41.110 --> 00:07:41.120 align:start position:0%
for the given uh user query or the task
 

00:07:41.120 --> 00:07:44.430 align:start position:0%
for the given uh user query or the task
and<00:07:41.319><c> use</c><00:07:41.639><c> those</c><00:07:42.000><c> two</c><00:07:42.879><c> uh</c><00:07:43.199><c> specific</c><00:07:43.599><c> tools</c>

00:07:44.430 --> 00:07:44.440 align:start position:0%
and use those two uh specific tools
 

00:07:44.440 --> 00:07:48.510 align:start position:0%
and use those two uh specific tools
retri<00:07:45.000><c> tools</c><00:07:45.800><c> uh</c><00:07:45.960><c> to</c><00:07:46.639><c> complete</c><00:07:47.000><c> the</c><00:07:47.120><c> user</c><00:07:47.520><c> task</c>

00:07:48.510 --> 00:07:48.520 align:start position:0%
retri tools uh to complete the user task
 

00:07:48.520 --> 00:07:50.710 align:start position:0%
retri tools uh to complete the user task
so<00:07:49.080><c> that's</c><00:07:49.280><c> how</c><00:07:49.720><c> you</c><00:07:49.840><c> can</c><00:07:50.000><c> use</c><00:07:50.240><c> a</c><00:07:50.400><c> Ral</c>

00:07:50.710 --> 00:07:50.720 align:start position:0%
so that's how you can use a Ral
 

00:07:50.720 --> 00:07:52.950 align:start position:0%
so that's how you can use a Ral
augmented<00:07:51.159><c> function</c><00:07:51.440><c> calling</c><00:07:51.800><c> agent</c><00:07:52.159><c> you</c><00:07:52.280><c> can</c>

00:07:52.950 --> 00:07:52.960 align:start position:0%
augmented function calling agent you can
 

00:07:52.960 --> 00:07:58.390 align:start position:0%
augmented function calling agent you can
uh<00:07:53.520><c> use</c><00:07:53.720><c> a</c><00:07:53.960><c> similar</c><00:07:55.280><c> uh</c><00:07:56.280><c> way</c><00:07:57.080><c> with</c><00:07:57.599><c> react</c><00:07:58.039><c> agent</c>

00:07:58.390 --> 00:07:58.400 align:start position:0%
uh use a similar uh way with react agent
 

00:07:58.400 --> 00:08:01.110 align:start position:0%
uh use a similar uh way with react agent
as<00:07:58.560><c> well</c><00:07:58.879><c> uh</c><00:07:59.000><c> do</c><00:07:59.800><c> with</c><00:07:59.960><c> that</c><00:08:00.479><c> um</c><00:08:00.720><c> if</c><00:08:00.800><c> you</c><00:08:00.919><c> are</c>

00:08:01.110 --> 00:08:01.120 align:start position:0%
as well uh do with that um if you are
 

00:08:01.120 --> 00:08:02.990 align:start position:0%
as well uh do with that um if you are
curious<00:08:01.520><c> about</c><00:08:01.759><c> it</c><00:08:02.159><c> with</c><00:08:02.280><c> the</c><00:08:02.400><c> react</c><00:08:02.759><c> agent</c>

00:08:02.990 --> 00:08:03.000 align:start position:0%
curious about it with the react agent
 

00:08:03.000 --> 00:08:06.230 align:start position:0%
curious about it with the react agent
and<00:08:03.120><c> check</c><00:08:03.280><c> out</c><00:08:03.440><c> the</c><00:08:04.000><c> notebook</c><00:08:05.000><c> we'll</c><00:08:05.800><c> meet</c><00:08:06.120><c> in</c>

00:08:06.230 --> 00:08:06.240 align:start position:0%
and check out the notebook we'll meet in
 

00:08:06.240 --> 00:08:10.960 align:start position:0%
and check out the notebook we'll meet in
the<00:08:06.400><c> next</c><00:08:06.759><c> video</c><00:08:07.759><c> thank</c><00:08:07.960><c> you</c>

