WEBVTT
Kind: captions
Language: en

00:00:00.420 --> 00:00:00.709 align:start position:0%
 
[Music]

00:00:00.709 --> 00:00:00.719 align:start position:0%
[Music]
 

00:00:00.719 --> 00:00:06.470 align:start position:0%
[Music]
foreign

00:00:06.470 --> 00:00:06.480 align:start position:0%
 
 

00:00:06.480 --> 00:00:12.589 align:start position:0%
 
labs

00:00:12.589 --> 00:00:12.599 align:start position:0%
 
 

00:00:12.599 --> 00:00:14.629 align:start position:0%
 
I<00:00:13.080><c> would</c><00:00:13.259><c> like</c><00:00:13.380><c> to</c><00:00:13.559><c> share</c><00:00:13.740><c> some</c><00:00:14.099><c> of</c><00:00:14.219><c> the</c><00:00:14.400><c> latest</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
I would like to share some of the latest
 

00:00:14.639 --> 00:00:17.210 align:start position:0%
I would like to share some of the latest
features<00:00:15.240><c> we're</c><00:00:15.780><c> releasing</c><00:00:16.260><c> a</c><00:00:16.440><c> spark</c><00:00:16.740><c> NLP</c>

00:00:17.210 --> 00:00:17.220 align:start position:0%
features we're releasing a spark NLP
 

00:00:17.220 --> 00:00:19.130 align:start position:0%
features we're releasing a spark NLP
especially<00:00:18.060><c> in</c><00:00:18.359><c> the</c><00:00:18.539><c> fields</c><00:00:18.779><c> of</c><00:00:18.960><c> computer</c>

00:00:19.130 --> 00:00:19.140 align:start position:0%
especially in the fields of computer
 

00:00:19.140 --> 00:00:22.429 align:start position:0%
especially in the fields of computer
vision<00:00:19.699><c> and</c><00:00:20.699><c> automatic</c><00:00:21.420><c> speech</c><00:00:21.900><c> recognition</c>

00:00:22.429 --> 00:00:22.439 align:start position:0%
vision and automatic speech recognition
 

00:00:22.439 --> 00:00:26.029 align:start position:0%
vision and automatic speech recognition
a<00:00:23.400><c> quick</c><00:00:23.520><c> introduction</c><00:00:24.199><c> sparkly</c><00:00:25.199><c> is</c><00:00:25.800><c> an</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
a quick introduction sparkly is an
 

00:00:26.039 --> 00:00:29.689 align:start position:0%
a quick introduction sparkly is an
apache2<00:00:26.699><c> license</c><00:00:27.000><c> library</c><00:00:27.480><c> for</c><00:00:28.199><c> python</c><00:00:28.800><c> Java</c>

00:00:29.689 --> 00:00:29.699 align:start position:0%
apache2 license library for python Java
 

00:00:29.699 --> 00:00:32.749 align:start position:0%
apache2 license library for python Java
and<00:00:30.060><c> Escala</c><00:00:30.660><c> this</c><00:00:31.439><c> means</c><00:00:31.800><c> everything</c><00:00:32.220><c> in</c><00:00:32.520><c> its</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
and Escala this means everything in its
 

00:00:32.759 --> 00:00:35.330 align:start position:0%
and Escala this means everything in its
library<00:00:32.880><c> is</c><00:00:33.540><c> completely</c><00:00:34.079><c> free</c><00:00:34.500><c> and</c><00:00:35.100><c> open</c>

00:00:35.330 --> 00:00:35.340 align:start position:0%
library is completely free and open
 

00:00:35.340 --> 00:00:38.090 align:start position:0%
library is completely free and open
source<00:00:35.940><c> for</c><00:00:36.420><c> both</c><00:00:36.719><c> personal</c><00:00:37.320><c> and</c><00:00:37.860><c> commercial</c>

00:00:38.090 --> 00:00:38.100 align:start position:0%
source for both personal and commercial
 

00:00:38.100 --> 00:00:41.030 align:start position:0%
source for both personal and commercial
use<00:00:38.579><c> the</c><00:00:39.420><c> library</c><00:00:39.600><c> has</c><00:00:39.960><c> been</c><00:00:40.200><c> around</c><00:00:40.379><c> for</c><00:00:40.860><c> over</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
use the library has been around for over
 

00:00:41.040 --> 00:00:43.549 align:start position:0%
use the library has been around for over
four<00:00:41.340><c> years</c><00:00:41.640><c> and</c><00:00:42.300><c> we</c><00:00:42.540><c> have</c><00:00:42.660><c> had</c><00:00:42.960><c> more</c><00:00:43.320><c> than</c><00:00:43.440><c> 100</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
four years and we have had more than 100
 

00:00:43.559 --> 00:00:47.510 align:start position:0%
four years and we have had more than 100
releases<00:00:44.820><c> spark</c><00:00:45.480><c> NLP</c><00:00:46.079><c> has</c><00:00:46.680><c> been</c><00:00:46.860><c> one</c><00:00:47.100><c> of</c><00:00:47.219><c> the</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
releases spark NLP has been one of the
 

00:00:47.520 --> 00:00:49.729 align:start position:0%
releases spark NLP has been one of the
most<00:00:47.760><c> used</c><00:00:48.239><c> NLP</c><00:00:48.780><c> libraries</c><00:00:49.260><c> in</c><00:00:49.559><c> the</c>

00:00:49.729 --> 00:00:49.739 align:start position:0%
most used NLP libraries in the
 

00:00:49.739 --> 00:00:52.490 align:start position:0%
most used NLP libraries in the
Enterprise<00:00:49.920><c> for</c><00:00:50.820><c> the</c><00:00:51.000><c> past</c><00:00:51.120><c> few</c><00:00:51.360><c> years</c><00:00:51.600><c> the</c>

00:00:52.490 --> 00:00:52.500 align:start position:0%
Enterprise for the past few years the
 

00:00:52.500 --> 00:00:54.290 align:start position:0%
Enterprise for the past few years the
downloads<00:00:52.920><c> of</c><00:00:52.980><c> a</c><00:00:53.100><c> sparkling</c><00:00:53.460><c> FP</c><00:00:53.820><c> Library</c><00:00:53.940><c> have</c>

00:00:54.290 --> 00:00:54.300 align:start position:0%
downloads of a sparkling FP Library have
 

00:00:54.300 --> 00:00:58.189 align:start position:0%
downloads of a sparkling FP Library have
grown<00:00:54.660><c> over</c><00:00:55.020><c> 40</c><00:00:55.500><c> times</c><00:00:55.920><c> within</c><00:00:56.879><c> the</c><00:00:57.120><c> last</c><00:00:57.360><c> two</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
grown over 40 times within the last two
 

00:00:58.199 --> 00:01:02.029 align:start position:0%
grown over 40 times within the last two
years<00:00:58.520><c> in</c><00:00:59.520><c> fact</c><00:00:59.699><c> we</c><00:01:00.180><c> are</c><00:01:00.360><c> celebrating</c><00:01:00.960><c> our</c><00:01:01.500><c> 36</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
years in fact we are celebrating our 36
 

00:01:02.039 --> 00:01:04.670 align:start position:0%
years in fact we are celebrating our 36
million<00:01:02.399><c> downloads</c><00:01:02.940><c> for</c><00:01:03.239><c> python</c><00:01:03.840><c> on</c><00:01:04.379><c> Pious</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
million downloads for python on Pious
 

00:01:04.680 --> 00:01:06.469 align:start position:0%
million downloads for python on Pious
part<00:01:05.040><c> this</c><00:01:05.280><c> month</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
part this month
 

00:01:06.479 --> 00:01:09.469 align:start position:0%
part this month
there<00:01:07.020><c> is</c><00:01:07.140><c> a</c><00:01:07.320><c> clear</c><00:01:07.500><c> correlation</c><00:01:08.220><c> between</c><00:01:09.119><c> the</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
there is a clear correlation between the
 

00:01:09.479 --> 00:01:12.289 align:start position:0%
there is a clear correlation between the
features<00:01:09.840><c> we</c><00:01:10.080><c> released</c><00:01:10.439><c> and</c><00:01:11.220><c> the</c><00:01:11.400><c> growth</c><00:01:11.760><c> in</c>

00:01:12.289 --> 00:01:12.299 align:start position:0%
features we released and the growth in
 

00:01:12.299 --> 00:01:14.450 align:start position:0%
features we released and the growth in
our<00:01:12.479><c> downloads</c><00:01:13.020><c> by</c><00:01:13.320><c> adding</c><00:01:13.680><c> new</c><00:01:13.799><c> users</c><00:01:14.220><c> and</c>

00:01:14.450 --> 00:01:14.460 align:start position:0%
our downloads by adding new users and
 

00:01:14.460 --> 00:01:16.969 align:start position:0%
our downloads by adding new users and
more<00:01:14.700><c> usage</c><00:01:15.420><c> we</c><00:01:16.080><c> are</c><00:01:16.260><c> listening</c><00:01:16.680><c> to</c><00:01:16.799><c> the</c>

00:01:16.969 --> 00:01:16.979 align:start position:0%
more usage we are listening to the
 

00:01:16.979 --> 00:01:19.490 align:start position:0%
more usage we are listening to the
community<00:01:17.280><c> as</c><00:01:18.180><c> well</c><00:01:18.420><c> as</c><00:01:18.659><c> following</c><00:01:19.260><c> the</c>

00:01:19.490 --> 00:01:19.500 align:start position:0%
community as well as following the
 

00:01:19.500 --> 00:01:22.190 align:start position:0%
community as well as following the
latest<00:01:19.680><c> trends</c><00:01:20.220><c> in</c><00:01:20.400><c> NLP</c><00:01:20.820><c> research</c><00:01:21.299><c> making</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
latest trends in NLP research making
 

00:01:22.200 --> 00:01:26.990 align:start position:0%
latest trends in NLP research making
them<00:01:22.560><c> both</c><00:01:22.979><c> scalable</c><00:01:23.880><c> and</c><00:01:24.780><c> production</c><00:01:25.500><c> ready</c>

00:01:26.990 --> 00:01:27.000 align:start position:0%
them both scalable and production ready
 

00:01:27.000 --> 00:01:30.289 align:start position:0%
them both scalable and production ready
we<00:01:27.720><c> continuously</c><00:01:28.380><c> test</c><00:01:28.759><c> optimize</c><00:01:29.759><c> and</c><00:01:30.060><c> make</c>

00:01:30.289 --> 00:01:30.299 align:start position:0%
we continuously test optimize and make
 

00:01:30.299 --> 00:01:33.289 align:start position:0%
we continuously test optimize and make
sure<00:01:30.479><c> a</c><00:01:30.720><c> spark</c><00:01:30.960><c> NLP</c><00:01:31.759><c> supports</c><00:01:32.759><c> all</c><00:01:33.119><c> the</c>

00:01:33.289 --> 00:01:33.299 align:start position:0%
sure a spark NLP supports all the
 

00:01:33.299 --> 00:01:37.069 align:start position:0%
sure a spark NLP supports all the
platforms<00:01:33.900><c> with</c><00:01:34.799><c> every</c><00:01:35.100><c> new</c><00:01:35.400><c> release</c><00:01:35.900><c> all</c><00:01:36.900><c> you</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
platforms with every new release all you
 

00:01:37.079 --> 00:01:39.770 align:start position:0%
platforms with every new release all you
need<00:01:37.259><c> is</c><00:01:37.740><c> the</c><00:01:37.979><c> internet</c><00:01:38.220><c> and</c><00:01:39.060><c> a</c><00:01:39.180><c> browser</c><00:01:39.540><c> to</c>

00:01:39.770 --> 00:01:39.780 align:start position:0%
need is the internet and a browser to
 

00:01:39.780 --> 00:01:42.770 align:start position:0%
need is the internet and a browser to
use<00:01:40.020><c> the</c><00:01:40.200><c> spark</c><00:01:40.560><c> NLP</c><00:01:40.920><c> from</c><00:01:41.700><c> free</c><00:01:42.479><c> and</c>

00:01:42.770 --> 00:01:42.780 align:start position:0%
use the spark NLP from free and
 

00:01:42.780 --> 00:01:45.230 align:start position:0%
use the spark NLP from free and
self-hosted<00:01:43.500><c> notebooks</c><00:01:44.100><c> like</c><00:01:44.280><c> Google</c><00:01:44.579><c> collab</c>

00:01:45.230 --> 00:01:45.240 align:start position:0%
self-hosted notebooks like Google collab
 

00:01:45.240 --> 00:01:47.810 align:start position:0%
self-hosted notebooks like Google collab
and<00:01:45.600><c> kagyo</c><00:01:46.200><c> to</c><00:01:46.680><c> Jupiter</c><00:01:47.159><c> on</c><00:01:47.460><c> your</c><00:01:47.640><c> local</c>

00:01:47.810 --> 00:01:47.820 align:start position:0%
and kagyo to Jupiter on your local
 

00:01:47.820 --> 00:01:50.630 align:start position:0%
and kagyo to Jupiter on your local
machine<00:01:48.299><c> or</c><00:01:49.140><c> distributed</c><00:01:49.740><c> environments</c><00:01:50.399><c> like</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
machine or distributed environments like
 

00:01:50.640 --> 00:01:54.289 align:start position:0%
machine or distributed environments like
kubernetes<00:01:51.360><c> cluster</c><00:01:52.020><c> databricks</c><00:01:52.860><c> AWS</c><00:01:53.820><c> CMR</c>

00:01:54.289 --> 00:01:54.299 align:start position:0%
kubernetes cluster databricks AWS CMR
 

00:01:54.299 --> 00:01:56.030 align:start position:0%
kubernetes cluster databricks AWS CMR
and<00:01:54.600><c> so</c><00:01:54.840><c> on</c>

00:01:56.030 --> 00:01:56.040 align:start position:0%
and so on
 

00:01:56.040 --> 00:01:59.210 align:start position:0%
and so on
now<00:01:57.000><c> let's</c><00:01:57.180><c> talk</c><00:01:57.420><c> about</c><00:01:57.540><c> models</c><00:01:58.200><c> how</c>

00:01:59.210 --> 00:01:59.220 align:start position:0%
now let's talk about models how
 

00:01:59.220 --> 00:02:02.330 align:start position:0%
now let's talk about models how
creating<00:01:59.939><c> your</c><00:02:00.299><c> own</c><00:02:00.420><c> NLP</c><00:02:01.020><c> product</c><00:02:01.320><c> has</c><00:02:02.040><c> never</c>

00:02:02.330 --> 00:02:02.340 align:start position:0%
creating your own NLP product has never
 

00:02:02.340 --> 00:02:04.969 align:start position:0%
creating your own NLP product has never
been<00:02:02.640><c> easier</c><00:02:03.060><c> over</c><00:02:03.840><c> 11</c>

00:02:04.969 --> 00:02:04.979 align:start position:0%
been easier over 11
 

00:02:04.979 --> 00:02:08.089 align:start position:0%
been easier over 11
000<00:02:05.100><c> free</c><00:02:05.460><c> and</c><00:02:05.939><c> open</c><00:02:06.240><c> source</c><00:02:06.780><c> models</c><00:02:07.259><c> at</c><00:02:07.860><c> your</c>

00:02:08.089 --> 00:02:08.099 align:start position:0%
000 free and open source models at your
 

00:02:08.099 --> 00:02:10.969 align:start position:0%
000 free and open source models at your
fingertips<00:02:08.599><c> we</c><00:02:09.599><c> make</c><00:02:09.840><c> sure</c><00:02:10.020><c> the</c><00:02:10.380><c> sanity</c><00:02:10.739><c> of</c>

00:02:10.969 --> 00:02:10.979 align:start position:0%
fingertips we make sure the sanity of
 

00:02:10.979 --> 00:02:13.190 align:start position:0%
fingertips we make sure the sanity of
each<00:02:11.160><c> model</c><00:02:11.340><c> we</c><00:02:11.700><c> deliver</c><00:02:12.000><c> to</c><00:02:12.780><c> the</c><00:02:12.959><c> community</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
each model we deliver to the community
 

00:02:13.200 --> 00:02:16.430 align:start position:0%
each model we deliver to the community
from<00:02:14.160><c> their</c><00:02:14.400><c> accuracy</c><00:02:15.000><c> all</c><00:02:15.660><c> the</c><00:02:15.840><c> way</c><00:02:15.900><c> to</c><00:02:16.200><c> the</c>

00:02:16.430 --> 00:02:16.440 align:start position:0%
from their accuracy all the way to the
 

00:02:16.440 --> 00:02:19.369 align:start position:0%
from their accuracy all the way to the
overall<00:02:16.879><c> performance</c><00:02:17.879><c> as</c><00:02:18.780><c> we</c><00:02:18.959><c> can</c><00:02:19.080><c> see</c><00:02:19.200><c> there</c>

00:02:19.369 --> 00:02:19.379 align:start position:0%
overall performance as we can see there
 

00:02:19.379 --> 00:02:21.770 align:start position:0%
overall performance as we can see there
is<00:02:19.560><c> a</c><00:02:19.680><c> little</c><00:02:19.800><c> joke</c><00:02:20.040><c> here</c><00:02:20.340><c> even</c><00:02:21.000><c> our</c><00:02:21.239><c> designers</c>

00:02:21.770 --> 00:02:21.780 align:start position:0%
is a little joke here even our designers
 

00:02:21.780 --> 00:02:24.589 align:start position:0%
is a little joke here even our designers
cannot<00:02:22.500><c> keep</c><00:02:22.800><c> up</c><00:02:23.040><c> with</c><00:02:23.520><c> the</c><00:02:23.700><c> rapidly</c><00:02:24.180><c> growing</c>

00:02:24.589 --> 00:02:24.599 align:start position:0%
cannot keep up with the rapidly growing
 

00:02:24.599 --> 00:02:26.990 align:start position:0%
cannot keep up with the rapidly growing
numbers<00:02:24.840><c> of</c><00:02:25.140><c> features</c><00:02:25.560><c> and</c><00:02:26.280><c> high</c><00:02:26.580><c> quality</c>

00:02:26.990 --> 00:02:27.000 align:start position:0%
numbers of features and high quality
 

00:02:27.000 --> 00:02:30.589 align:start position:0%
numbers of features and high quality
models<00:02:27.540><c> introduced</c><00:02:28.319><c> in</c><00:02:28.860><c> Spock</c><00:02:29.400><c> NLP</c>

00:02:30.589 --> 00:02:30.599 align:start position:0%
models introduced in Spock NLP
 

00:02:30.599 --> 00:02:33.470 align:start position:0%
models introduced in Spock NLP
if<00:02:31.440><c> you</c><00:02:31.620><c> would</c><00:02:31.739><c> like</c><00:02:31.920><c> to</c><00:02:32.040><c> see</c><00:02:32.220><c> demos</c><00:02:32.700><c> made</c><00:02:33.120><c> by</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
if you would like to see demos made by
 

00:02:33.480 --> 00:02:35.390 align:start position:0%
if you would like to see demos made by
our<00:02:33.720><c> pre-trained</c><00:02:34.319><c> models</c><00:02:34.739><c> and</c><00:02:34.860><c> pipelines</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
our pre-trained models and pipelines
 

00:02:35.400 --> 00:02:38.449 align:start position:0%
our pre-trained models and pipelines
please<00:02:36.060><c> go</c><00:02:36.360><c> visit</c><00:02:36.599><c> the</c><00:02:36.959><c> official</c><00:02:37.379><c> website</c><00:02:37.920><c> for</c>

00:02:38.449 --> 00:02:38.459 align:start position:0%
please go visit the official website for
 

00:02:38.459 --> 00:02:42.589 align:start position:0%
please go visit the official website for
spark<00:02:38.760><c> NLP</c><00:02:39.300><c> and</c><00:02:39.900><c> check</c><00:02:40.140><c> out</c><00:02:40.319><c> the</c><00:02:40.620><c> demo</c><00:02:41.099><c> page</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
spark NLP and check out the demo page
 

00:02:42.599 --> 00:02:45.530 align:start position:0%
spark NLP and check out the demo page
now<00:02:43.080><c> let's</c><00:02:43.200><c> talk</c><00:02:43.440><c> about</c><00:02:43.620><c> sparkling</c><00:02:44.220><c> lp4</c>

00:02:45.530 --> 00:02:45.540 align:start position:0%
now let's talk about sparkling lp4
 

00:02:45.540 --> 00:02:49.190 align:start position:0%
now let's talk about sparkling lp4
in<00:02:45.840><c> a</c><00:02:46.019><c> spark</c><00:02:46.260><c> NLP</c><00:02:46.680><c> 4.0</c><00:02:47.280><c> we</c><00:02:47.940><c> introduced</c><00:02:48.660><c> a</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
in a spark NLP 4.0 we introduced a
 

00:02:49.200 --> 00:02:51.830 align:start position:0%
in a spark NLP 4.0 we introduced a
modern<00:02:49.580><c> extractive</c><00:02:50.580><c> Transformer</c><00:02:51.360><c> based</c>

00:02:51.830 --> 00:02:51.840 align:start position:0%
modern extractive Transformer based
 

00:02:51.840 --> 00:02:54.949 align:start position:0%
modern extractive Transformer based
question<00:02:52.260><c> answering</c><00:02:53.040><c> feature</c><00:02:53.519><c> for</c><00:02:54.300><c> seven</c><00:02:54.660><c> of</c>

00:02:54.949 --> 00:02:54.959 align:start position:0%
question answering feature for seven of
 

00:02:54.959 --> 00:02:57.470 align:start position:0%
question answering feature for seven of
the<00:02:55.140><c> most</c><00:02:55.440><c> popular</c><00:02:55.800><c> language</c><00:02:56.280><c> models</c><00:02:56.940><c> such</c><00:02:57.300><c> as</c>

00:02:57.470 --> 00:02:57.480 align:start position:0%
the most popular language models such as
 

00:02:57.480 --> 00:03:02.509 align:start position:0%
the most popular language models such as
bird<00:02:57.860><c> Electro</c><00:02:58.860><c> Roberta</c><00:02:59.700><c> di</c><00:03:00.060><c> Berto</c><00:03:00.420><c> and</c><00:03:01.019><c> so</c><00:03:01.200><c> on</c>

00:03:02.509 --> 00:03:02.519 align:start position:0%
bird Electro Roberta di Berto and so on
 

00:03:02.519 --> 00:03:05.869 align:start position:0%
bird Electro Roberta di Berto and so on
a<00:03:03.180><c> quick</c><00:03:03.420><c> demo</c><00:03:03.840><c> of</c><00:03:03.900><c> how</c><00:03:04.319><c> extractive</c><00:03:04.860><c> q</c><00:03:05.099><c> a</c><00:03:05.280><c> works</c>

00:03:05.869 --> 00:03:05.879 align:start position:0%
a quick demo of how extractive q a works
 

00:03:05.879 --> 00:03:08.449 align:start position:0%
a quick demo of how extractive q a works
is<00:03:06.420><c> where</c><00:03:06.660><c> you</c><00:03:06.840><c> have</c><00:03:07.019><c> the</c><00:03:07.260><c> context</c><00:03:07.620><c> and</c><00:03:08.280><c> you</c>

00:03:08.449 --> 00:03:08.459 align:start position:0%
is where you have the context and you
 

00:03:08.459 --> 00:03:11.089 align:start position:0%
is where you have the context and you
ask<00:03:08.640><c> questions</c><00:03:09.060><c> from</c><00:03:09.540><c> the</c><00:03:09.780><c> context</c><00:03:10.140><c> as</c><00:03:10.920><c> you</c>

00:03:11.089 --> 00:03:11.099 align:start position:0%
ask questions from the context as you
 

00:03:11.099 --> 00:03:13.250 align:start position:0%
ask questions from the context as you
can<00:03:11.220><c> see</c><00:03:11.340><c> in</c><00:03:11.640><c> the</c><00:03:11.760><c> right</c><00:03:11.940><c> example</c><00:03:12.480><c> the</c>

00:03:13.250 --> 00:03:13.260 align:start position:0%
can see in the right example the
 

00:03:13.260 --> 00:03:16.430 align:start position:0%
can see in the right example the
questions<00:03:13.560><c> are</c><00:03:14.340><c> not</c><00:03:14.640><c> always</c><00:03:14.879><c> obvious</c><00:03:15.599><c> even</c><00:03:16.080><c> to</c>

00:03:16.430 --> 00:03:16.440 align:start position:0%
questions are not always obvious even to
 

00:03:16.440 --> 00:03:18.770 align:start position:0%
questions are not always obvious even to
the<00:03:16.620><c> humans</c><00:03:17.040><c> let</c><00:03:17.580><c> alone</c><00:03:17.819><c> to</c><00:03:18.120><c> a</c><00:03:18.300><c> deep</c><00:03:18.420><c> learning</c>

00:03:18.770 --> 00:03:18.780 align:start position:0%
the humans let alone to a deep learning
 

00:03:18.780 --> 00:03:21.410 align:start position:0%
the humans let alone to a deep learning
model<00:03:18.900><c> but</c><00:03:19.860><c> the</c><00:03:20.099><c> result</c><00:03:20.280><c> is</c><00:03:20.940><c> pretty</c>

00:03:21.410 --> 00:03:21.420 align:start position:0%
model but the result is pretty
 

00:03:21.420 --> 00:03:23.570 align:start position:0%
model but the result is pretty
impressive

00:03:23.570 --> 00:03:23.580 align:start position:0%
impressive
 

00:03:23.580 --> 00:03:26.270 align:start position:0%
impressive
we<00:03:24.060><c> also</c><00:03:24.300><c> started</c><00:03:24.720><c> to</c><00:03:24.900><c> provide</c><00:03:25.319><c> experimental</c>

00:03:26.270 --> 00:03:26.280 align:start position:0%
we also started to provide experimental
 

00:03:26.280 --> 00:03:30.350 align:start position:0%
we also started to provide experimental
support<00:03:26.640><c> for</c><00:03:27.540><c> Apple</c><00:03:27.780><c> M1</c><00:03:28.379><c> and</c><00:03:28.920><c> M2</c><00:03:29.340><c> chips</c><00:03:29.819><c> in</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
support for Apple M1 and M2 chips in
 

00:03:30.360 --> 00:03:32.930 align:start position:0%
support for Apple M1 and M2 chips in
addition<00:03:30.599><c> to</c><00:03:30.959><c> welcoming</c><00:03:31.500><c> support</c><00:03:31.860><c> for</c><00:03:32.700><c> one</c>

00:03:32.930 --> 00:03:32.940 align:start position:0%
addition to welcoming support for one
 

00:03:32.940 --> 00:03:36.890 align:start position:0%
addition to welcoming support for one
DNN<00:03:33.360><c> by</c><00:03:33.780><c> one</c><00:03:34.019><c> API</c><00:03:34.500><c> for</c><00:03:35.220><c> optimization</c><00:03:35.940><c> on</c><00:03:36.420><c> Intel</c>

00:03:36.890 --> 00:03:36.900 align:start position:0%
DNN by one API for optimization on Intel
 

00:03:36.900 --> 00:03:38.690 align:start position:0%
DNN by one API for optimization on Intel
processors

00:03:38.690 --> 00:03:38.700 align:start position:0%
processors
 

00:03:38.700 --> 00:03:41.390 align:start position:0%
processors
speaking<00:03:39.540><c> of</c><00:03:39.599><c> the</c><00:03:39.780><c> optimizations</c><00:03:40.400><c> we</c>

00:03:41.390 --> 00:03:41.400 align:start position:0%
speaking of the optimizations we
 

00:03:41.400 --> 00:03:43.550 align:start position:0%
speaking of the optimizations we
improved<00:03:41.879><c> the</c><00:03:42.120><c> performance</c><00:03:42.659><c> of</c><00:03:43.140><c> all</c>

00:03:43.550 --> 00:03:43.560 align:start position:0%
improved the performance of all
 

00:03:43.560 --> 00:03:45.710 align:start position:0%
improved the performance of all
Transformer<00:03:44.340><c> based</c><00:03:44.760><c> and</c><00:03:44.879><c> biddings</c><00:03:45.299><c> in</c><00:03:45.599><c> your</c>

00:03:45.710 --> 00:03:45.720 align:start position:0%
Transformer based and biddings in your
 

00:03:45.720 --> 00:03:49.070 align:start position:0%
Transformer based and biddings in your
spark<00:03:46.019><c> NLP</c><00:03:46.379><c> up</c><00:03:47.220><c> to</c><00:03:47.400><c> eight</c><00:03:47.819><c> times</c><00:03:48.120><c> faster</c><00:03:48.659><c> on</c>

00:03:49.070 --> 00:03:49.080 align:start position:0%
spark NLP up to eight times faster on
 

00:03:49.080 --> 00:03:53.089 align:start position:0%
spark NLP up to eight times faster on
GPU<00:03:49.680><c> and</c><00:03:50.400><c> almost</c><00:03:50.640><c> twice</c><00:03:51.420><c> performance</c><00:03:52.019><c> gain</c><00:03:52.379><c> by</c>

00:03:53.089 --> 00:03:53.099 align:start position:0%
GPU and almost twice performance gain by
 

00:03:53.099 --> 00:03:56.509 align:start position:0%
GPU and almost twice performance gain by
only<00:03:53.459><c> enabling</c><00:03:54.120><c> one</c><00:03:54.840><c> DNN</c><00:03:55.379><c> on</c><00:03:56.040><c> Intel</c>

00:03:56.509 --> 00:03:56.519 align:start position:0%
only enabling one DNN on Intel
 

00:03:56.519 --> 00:03:58.910 align:start position:0%
only enabling one DNN on Intel
processors

00:03:58.910 --> 00:03:58.920 align:start position:0%
processors
 

00:03:58.920 --> 00:04:02.449 align:start position:0%
processors
now<00:03:59.519><c> it</c><00:03:59.819><c> gets</c><00:04:00.120><c> us</c><00:04:00.180><c> to</c><00:04:00.480><c> region</c><00:04:01.019><c> Transformer</c>

00:04:02.449 --> 00:04:02.459 align:start position:0%
now it gets us to region Transformer
 

00:04:02.459 --> 00:04:05.390 align:start position:0%
now it gets us to region Transformer
region<00:04:03.120><c> Transformer</c><00:04:03.720><c> focuses</c><00:04:04.319><c> on</c><00:04:04.860><c> higher</c>

00:04:05.390 --> 00:04:05.400 align:start position:0%
region Transformer focuses on higher
 

00:04:05.400 --> 00:04:08.509 align:start position:0%
region Transformer focuses on higher
accuracy<00:04:05.879><c> but</c><00:04:06.480><c> with</c><00:04:06.900><c> less</c><00:04:07.140><c> computer</c><00:04:07.440><c> you</c><00:04:08.340><c> can</c>

00:04:08.509 --> 00:04:08.519 align:start position:0%
accuracy but with less computer you can
 

00:04:08.519 --> 00:04:10.610 align:start position:0%
accuracy but with less computer you can
pre-train<00:04:09.120><c> and</c><00:04:09.599><c> fine-tune</c><00:04:10.319><c> your</c><00:04:10.379><c> vision</c>

00:04:10.610 --> 00:04:10.620 align:start position:0%
pre-train and fine-tune your vision
 

00:04:10.620 --> 00:04:12.949 align:start position:0%
pre-train and fine-tune your vision
transforming<00:04:11.280><c> models</c><00:04:11.700><c> just</c><00:04:12.420><c> as</c><00:04:12.599><c> you</c><00:04:12.720><c> do</c><00:04:12.900><c> in</c>

00:04:12.949 --> 00:04:12.959 align:start position:0%
transforming models just as you do in
 

00:04:12.959 --> 00:04:15.410 align:start position:0%
transforming models just as you do in
NLP<00:04:13.439><c> and</c><00:04:13.739><c> that's</c><00:04:14.040><c> pretty</c><00:04:14.220><c> cool</c><00:04:14.459><c> if</c><00:04:15.120><c> you</c><00:04:15.299><c> ever</c>

00:04:15.410 --> 00:04:15.420 align:start position:0%
NLP and that's pretty cool if you ever
 

00:04:15.420 --> 00:04:18.530 align:start position:0%
NLP and that's pretty cool if you ever
try<00:04:15.840><c> to</c><00:04:16.260><c> train</c><00:04:16.620><c> or</c><00:04:16.979><c> fine-tune</c><00:04:17.760><c> a</c><00:04:17.880><c> model</c><00:04:18.120><c> in</c>

00:04:18.530 --> 00:04:18.540 align:start position:0%
try to train or fine-tune a model in
 

00:04:18.540 --> 00:04:21.409 align:start position:0%
try to train or fine-tune a model in
computer<00:04:18.660><c> vision</c><00:04:19.139><c> by</c><00:04:19.560><c> using</c><00:04:19.919><c> cnns</c><00:04:20.579><c> you</c><00:04:21.299><c> would</c>

00:04:21.409 --> 00:04:21.419 align:start position:0%
computer vision by using cnns you would
 

00:04:21.419 --> 00:04:24.409 align:start position:0%
computer vision by using cnns you would
know<00:04:21.600><c> it</c><00:04:21.840><c> takes</c><00:04:22.079><c> a</c><00:04:22.320><c> long</c><00:04:22.440><c> time</c><00:04:23.000><c> requires</c><00:04:24.000><c> lots</c>

00:04:24.409 --> 00:04:24.419 align:start position:0%
know it takes a long time requires lots
 

00:04:24.419 --> 00:04:26.689 align:start position:0%
know it takes a long time requires lots
of<00:04:24.479><c> resources</c><00:04:25.080><c> and</c><00:04:25.919><c> a</c><00:04:26.100><c> large</c><00:04:26.280><c> number</c><00:04:26.520><c> of</c>

00:04:26.689 --> 00:04:26.699 align:start position:0%
of resources and a large number of
 

00:04:26.699 --> 00:04:29.689 align:start position:0%
of resources and a large number of
labeled<00:04:27.419><c> data</c><00:04:28.080><c> set</c>

00:04:29.689 --> 00:04:29.699 align:start position:0%
labeled data set
 

00:04:29.699 --> 00:04:33.590 align:start position:0%
labeled data set
in<00:04:30.180><c> your</c><00:04:30.419><c> spoken</c><00:04:30.720><c> LP</c><00:04:31.080><c> 4.1</c><00:04:31.860><c> we</c><00:04:32.639><c> introduced</c><00:04:33.360><c> the</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
in your spoken LP 4.1 we introduced the
 

00:04:33.600 --> 00:04:36.469 align:start position:0%
in your spoken LP 4.1 we introduced the
support<00:04:33.900><c> for</c><00:04:34.500><c> Transformer</c><00:04:35.160><c> vision</c><00:04:35.520><c> for</c><00:04:36.240><c> the</c>

00:04:36.469 --> 00:04:36.479 align:start position:0%
support for Transformer vision for the
 

00:04:36.479 --> 00:04:38.510 align:start position:0%
support for Transformer vision for the
state-of-the-art<00:04:37.259><c> image</c><00:04:37.919><c> classification</c>

00:04:38.510 --> 00:04:38.520 align:start position:0%
state-of-the-art image classification
 

00:04:38.520 --> 00:04:41.030 align:start position:0%
state-of-the-art image classification
task<00:04:39.000><c> the</c><00:04:39.900><c> first</c><00:04:40.080><c> feature</c><00:04:40.500><c> for</c><00:04:40.800><c> computer</c>

00:04:41.030 --> 00:04:41.040 align:start position:0%
task the first feature for computer
 

00:04:41.040 --> 00:04:44.090 align:start position:0%
task the first feature for computer
vision<00:04:41.460><c> task</c><00:04:41.880><c> ever</c><00:04:42.419><c> released</c><00:04:42.960><c> in</c><00:04:43.199><c> a</c><00:04:43.380><c> spark</c><00:04:43.620><c> NLP</c>

00:04:44.090 --> 00:04:44.100 align:start position:0%
vision task ever released in a spark NLP
 

00:04:44.100 --> 00:04:47.510 align:start position:0%
vision task ever released in a spark NLP
and<00:04:45.060><c> as</c><00:04:45.240><c> always</c><00:04:45.419><c> we</c><00:04:46.259><c> make</c><00:04:46.440><c> sure</c><00:04:46.680><c> it's</c><00:04:47.280><c> both</c>

00:04:47.510 --> 00:04:47.520 align:start position:0%
and as always we make sure it's both
 

00:04:47.520 --> 00:04:51.830 align:start position:0%
and as always we make sure it's both
easy<00:04:48.180><c> to</c><00:04:48.419><c> use</c><00:04:48.680><c> and</c><00:04:49.680><c> production</c><00:04:50.160><c> ready</c>

00:04:51.830 --> 00:04:51.840 align:start position:0%
easy to use and production ready
 

00:04:51.840 --> 00:04:53.990 align:start position:0%
easy to use and production ready
what<00:04:52.199><c> do</c><00:04:52.320><c> I</c><00:04:52.500><c> mean</c><00:04:52.620><c> by</c><00:04:52.740><c> production</c><00:04:53.160><c> ready</c>

00:04:53.990 --> 00:04:54.000 align:start position:0%
what do I mean by production ready
 

00:04:54.000 --> 00:04:57.050 align:start position:0%
what do I mean by production ready
as<00:04:54.419><c> you</c><00:04:54.600><c> can</c><00:04:54.720><c> see</c><00:04:54.900><c> a</c><00:04:55.139><c> Spock</c><00:04:55.440><c> NLP</c><00:04:56.060><c> performs</c>

00:04:57.050 --> 00:04:57.060 align:start position:0%
as you can see a Spock NLP performs
 

00:04:57.060 --> 00:04:58.909 align:start position:0%
as you can see a Spock NLP performs
better<00:04:57.419><c> than</c><00:04:57.660><c> other</c><00:04:57.900><c> python</c><00:04:58.440><c> libraries</c>

00:04:58.909 --> 00:04:58.919 align:start position:0%
better than other python libraries
 

00:04:58.919 --> 00:05:02.030 align:start position:0%
better than other python libraries
offering<00:04:59.820><c> the</c><00:05:00.240><c> same</c><00:05:00.540><c> feature</c><00:05:01.199><c> even</c><00:05:01.620><c> on</c><00:05:01.919><c> a</c>

00:05:02.030 --> 00:05:02.040 align:start position:0%
offering the same feature even on a
 

00:05:02.040 --> 00:05:06.050 align:start position:0%
offering the same feature even on a
single<00:05:02.400><c> machine</c><00:05:02.780><c> Spock</c><00:05:03.780><c> NLP</c><00:05:04.259><c> is</c><00:05:05.100><c> 65</c><00:05:05.699><c> percent</c>

00:05:06.050 --> 00:05:06.060 align:start position:0%
single machine Spock NLP is 65 percent
 

00:05:06.060 --> 00:05:09.350 align:start position:0%
single machine Spock NLP is 65 percent
faster<00:05:06.600><c> than</c><00:05:06.840><c> hug</c><00:05:07.139><c> and</c><00:05:07.259><c> face</c><00:05:07.440><c> on</c><00:05:07.979><c> CPUs</c><00:05:08.460><c> and</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
faster than hug and face on CPUs and
 

00:05:09.360 --> 00:05:12.770 align:start position:0%
faster than hug and face on CPUs and
almost<00:05:09.600><c> 80</c><00:05:10.080><c> percent</c><00:05:10.440><c> on</c><00:05:11.160><c> a</c><00:05:11.340><c> GPU</c><00:05:11.820><c> in</c><00:05:12.300><c> a</c><00:05:12.479><c> single</c>

00:05:12.770 --> 00:05:12.780 align:start position:0%
almost 80 percent on a GPU in a single
 

00:05:12.780 --> 00:05:14.930 align:start position:0%
almost 80 percent on a GPU in a single
machine

00:05:14.930 --> 00:05:14.940 align:start position:0%
machine
 

00:05:14.940 --> 00:05:17.689 align:start position:0%
machine
now<00:05:15.540><c> unlike</c><00:05:15.960><c> other</c><00:05:16.199><c> python</c><00:05:16.740><c> libraries</c><00:05:17.160><c> stuck</c>

00:05:17.689 --> 00:05:17.699 align:start position:0%
now unlike other python libraries stuck
 

00:05:17.699 --> 00:05:20.210 align:start position:0%
now unlike other python libraries stuck
in<00:05:17.880><c> one</c><00:05:18.060><c> machine</c><00:05:18.740><c> spontaneously</c><00:05:19.740><c> can</c><00:05:20.040><c> be</c>

00:05:20.210 --> 00:05:20.220 align:start position:0%
in one machine spontaneously can be
 

00:05:20.220 --> 00:05:23.270 align:start position:0%
in one machine spontaneously can be
easily<00:05:20.759><c> scaled</c><00:05:21.419><c> out</c><00:05:21.660><c> over</c><00:05:22.139><c> multiple</c><00:05:22.740><c> machines</c>

00:05:23.270 --> 00:05:23.280 align:start position:0%
easily scaled out over multiple machines
 

00:05:23.280 --> 00:05:25.969 align:start position:0%
easily scaled out over multiple machines
thanks<00:05:24.060><c> to</c><00:05:24.360><c> being</c><00:05:24.600><c> a</c><00:05:24.840><c> native</c><00:05:25.080><c> extension</c><00:05:25.620><c> of</c>

00:05:25.969 --> 00:05:25.979 align:start position:0%
thanks to being a native extension of
 

00:05:25.979 --> 00:05:27.409 align:start position:0%
thanks to being a native extension of
Apache<00:05:26.520><c> Spark</c>

00:05:27.409 --> 00:05:27.419 align:start position:0%
Apache Spark
 

00:05:27.419 --> 00:05:30.590 align:start position:0%
Apache Spark
on<00:05:27.900><c> a</c><00:05:28.080><c> cluster</c><00:05:28.380><c> of</c><00:05:28.560><c> 10</c><00:05:28.740><c> nodes</c><00:05:29.100><c> by</c><00:05:29.639><c> using</c><00:05:30.060><c> CPUs</c>

00:05:30.590 --> 00:05:30.600 align:start position:0%
on a cluster of 10 nodes by using CPUs
 

00:05:30.600 --> 00:05:34.670 align:start position:0%
on a cluster of 10 nodes by using CPUs
it<00:05:31.440><c> can</c><00:05:31.620><c> be</c><00:05:31.860><c> 11</c><00:05:32.220><c> times</c><00:05:32.759><c> faster</c><00:05:33.539><c> than</c><00:05:33.960><c> a</c><00:05:34.259><c> python</c>

00:05:34.670 --> 00:05:34.680 align:start position:0%
it can be 11 times faster than a python
 

00:05:34.680 --> 00:05:37.490 align:start position:0%
it can be 11 times faster than a python
library<00:05:34.919><c> on</c><00:05:35.400><c> a</c><00:05:35.580><c> single</c><00:05:35.880><c> machine</c>

00:05:37.490 --> 00:05:37.500 align:start position:0%
library on a single machine
 

00:05:37.500 --> 00:05:40.490 align:start position:0%
library on a single machine
and<00:05:37.919><c> this</c><00:05:38.100><c> is</c><00:05:38.340><c> the</c><00:05:38.940><c> same</c><00:05:39.060><c> for</c><00:05:39.300><c> GPU</c><00:05:39.840><c> on</c><00:05:40.320><c> a</c>

00:05:40.490 --> 00:05:40.500 align:start position:0%
and this is the same for GPU on a
 

00:05:40.500 --> 00:05:44.090 align:start position:0%
and this is the same for GPU on a
cluster<00:05:40.860><c> of</c><00:05:40.979><c> 10</c><00:05:41.220><c> nodes</c><00:05:41.520><c> by</c><00:05:42.240><c> using</c><00:05:42.600><c> gpus</c><00:05:43.139><c> it</c><00:05:43.919><c> can</c>

00:05:44.090 --> 00:05:44.100 align:start position:0%
cluster of 10 nodes by using gpus it can
 

00:05:44.100 --> 00:05:47.210 align:start position:0%
cluster of 10 nodes by using gpus it can
be<00:05:44.280><c> up</c><00:05:44.460><c> to</c><00:05:44.699><c> 13</c><00:05:45.000><c> times</c><00:05:45.479><c> faster</c><00:05:46.139><c> than</c><00:05:46.560><c> a</c><00:05:46.800><c> python</c>

00:05:47.210 --> 00:05:47.220 align:start position:0%
be up to 13 times faster than a python
 

00:05:47.220 --> 00:05:50.450 align:start position:0%
be up to 13 times faster than a python
library<00:05:47.460><c> on</c><00:05:48.000><c> a</c><00:05:48.240><c> single</c><00:05:48.539><c> machine</c><00:05:49.160><c> now</c><00:05:50.160><c> that</c>

00:05:50.450 --> 00:05:50.460 align:start position:0%
library on a single machine now that
 

00:05:50.460 --> 00:05:52.370 align:start position:0%
library on a single machine now that
gets<00:05:50.759><c> us</c><00:05:50.820><c> to</c><00:05:51.060><c> the</c><00:05:51.240><c> last</c><00:05:51.419><c> section</c><00:05:51.660><c> which</c><00:05:52.199><c> is</c>

00:05:52.370 --> 00:05:52.380 align:start position:0%
gets us to the last section which is
 

00:05:52.380 --> 00:05:54.409 align:start position:0%
gets us to the last section which is
automatic<00:05:52.979><c> speech</c><00:05:53.400><c> recognition</c>

00:05:54.409 --> 00:05:54.419 align:start position:0%
automatic speech recognition
 

00:05:54.419 --> 00:05:56.150 align:start position:0%
automatic speech recognition
I<00:05:55.020><c> would</c><00:05:55.139><c> like</c><00:05:55.259><c> to</c><00:05:55.440><c> share</c><00:05:55.620><c> some</c><00:05:55.860><c> of</c><00:05:55.979><c> these</c>

00:05:56.150 --> 00:05:56.160 align:start position:0%
I would like to share some of these
 

00:05:56.160 --> 00:05:57.770 align:start position:0%
I would like to share some of these
latest<00:05:56.340><c> features</c><00:05:56.940><c> that</c><00:05:57.120><c> we</c><00:05:57.240><c> added</c><00:05:57.479><c> in</c><00:05:57.660><c> the</c>

00:05:57.770 --> 00:05:57.780 align:start position:0%
latest features that we added in the
 

00:05:57.780 --> 00:06:00.110 align:start position:0%
latest features that we added in the
spoken<00:05:58.139><c> mp4.2</c>

00:06:00.110 --> 00:06:00.120 align:start position:0%
spoken mp4.2
 

00:06:00.120 --> 00:06:01.909 align:start position:0%
spoken mp4.2
but<00:06:00.600><c> a</c><00:06:00.720><c> little</c><00:06:00.840><c> bit</c><00:06:00.960><c> history</c><00:06:01.199><c> I'll</c><00:06:01.500><c> wave</c><00:06:01.740><c> to</c>

00:06:01.909 --> 00:06:01.919 align:start position:0%
but a little bit history I'll wave to
 

00:06:01.919 --> 00:06:04.730 align:start position:0%
but a little bit history I'll wave to
make<00:06:02.100><c> two</c><00:06:02.340><c> Wave</c><00:06:03.000><c> 2</c><00:06:03.240><c> vect2</c><00:06:03.780><c> is</c><00:06:04.139><c> one</c><00:06:04.440><c> of</c><00:06:04.560><c> the</c>

00:06:04.730 --> 00:06:04.740 align:start position:0%
make two Wave 2 vect2 is one of the
 

00:06:04.740 --> 00:06:06.890 align:start position:0%
make two Wave 2 vect2 is one of the
current<00:06:04.979><c> state</c><00:06:05.400><c> of</c><00:06:05.639><c> the</c><00:06:05.820><c> art</c><00:06:06.000><c> models</c><00:06:06.600><c> for</c>

00:06:06.890 --> 00:06:06.900 align:start position:0%
current state of the art models for
 

00:06:06.900 --> 00:06:09.370 align:start position:0%
current state of the art models for
automatic<00:06:07.560><c> speech</c><00:06:08.039><c> recognition</c><00:06:08.520><c> due</c><00:06:09.180><c> to</c>

00:06:09.370 --> 00:06:09.380 align:start position:0%
automatic speech recognition due to
 

00:06:09.380 --> 00:06:11.870 align:start position:0%
automatic speech recognition due to
self-supervised<00:06:10.380><c> training</c><00:06:10.860><c> which</c><00:06:11.340><c> is</c><00:06:11.639><c> quite</c>

00:06:11.870 --> 00:06:11.880 align:start position:0%
self-supervised training which is quite
 

00:06:11.880 --> 00:06:14.570 align:start position:0%
self-supervised training which is quite
A<00:06:12.060><c> New</c><00:06:12.240><c> Concept</c><00:06:12.600><c> in</c><00:06:12.780><c> this</c><00:06:12.960><c> field</c><00:06:13.340><c> using</c><00:06:14.340><c> one</c>

00:06:14.570 --> 00:06:14.580 align:start position:0%
A New Concept in this field using one
 

00:06:14.580 --> 00:06:17.629 align:start position:0%
A New Concept in this field using one
hour<00:06:14.759><c> of</c><00:06:15.060><c> labeled</c><00:06:15.479><c> data</c><00:06:15.900><c> wave</c><00:06:16.560><c> to</c><00:06:16.680><c> Vector</c><00:06:17.100><c> app</c>

00:06:17.629 --> 00:06:17.639 align:start position:0%
hour of labeled data wave to Vector app
 

00:06:17.639 --> 00:06:19.969 align:start position:0%
hour of labeled data wave to Vector app
performs<00:06:18.300><c> the</c><00:06:18.660><c> previous</c><00:06:18.840><c> state</c><00:06:19.440><c> of</c><00:06:19.680><c> the</c><00:06:19.800><c> art</c>

00:06:19.969 --> 00:06:19.979 align:start position:0%
performs the previous state of the art
 

00:06:19.979 --> 00:06:23.749 align:start position:0%
performs the previous state of the art
on<00:06:20.699><c> the</c><00:06:20.820><c> 100</c><00:06:21.000><c> hour</c><00:06:21.300><c> subset</c><00:06:21.960><c> while</c><00:06:22.620><c> using</c><00:06:23.160><c> 100</c>

00:06:23.749 --> 00:06:23.759 align:start position:0%
on the 100 hour subset while using 100
 

00:06:23.759 --> 00:06:26.510 align:start position:0%
on the 100 hour subset while using 100
times<00:06:24.300><c> less</c><00:06:24.720><c> labeled</c><00:06:25.500><c> data</c>

00:06:26.510 --> 00:06:26.520 align:start position:0%
times less labeled data
 

00:06:26.520 --> 00:06:30.409 align:start position:0%
times less labeled data
sparking<00:06:27.419><c> LP</c><00:06:27.780><c> 4.2</c><00:06:28.440><c> was</c><00:06:28.979><c> released</c><00:06:29.699><c> just</c><00:06:30.120><c> last</c>

00:06:30.409 --> 00:06:30.419 align:start position:0%
sparking LP 4.2 was released just last
 

00:06:30.419 --> 00:06:31.189 align:start position:0%
sparking LP 4.2 was released just last
week

00:06:31.189 --> 00:06:31.199 align:start position:0%
week
 

00:06:31.199 --> 00:06:34.010 align:start position:0%
week
we<00:06:31.740><c> delivered</c><00:06:32.100><c> wave</c><00:06:32.460><c> to</c><00:06:32.580><c> Wave</c><00:06:32.819><c> 2</c><00:06:33.000><c> as</c><00:06:33.600><c> the</c><00:06:33.900><c> first</c>

00:06:34.010 --> 00:06:34.020 align:start position:0%
we delivered wave to Wave 2 as the first
 

00:06:34.020 --> 00:06:36.830 align:start position:0%
we delivered wave to Wave 2 as the first
of<00:06:34.319><c> its</c><00:06:34.680><c> kind</c><00:06:34.880><c> while</c><00:06:35.880><c> maintaining</c><00:06:36.479><c> its</c>

00:06:36.830 --> 00:06:36.840 align:start position:0%
of its kind while maintaining its
 

00:06:36.840 --> 00:06:39.710 align:start position:0%
of its kind while maintaining its
accuracy<00:06:37.319><c> and</c><00:06:38.280><c> adding</c><00:06:38.639><c> the</c><00:06:38.759><c> ease</c><00:06:39.000><c> of</c><00:06:39.180><c> use</c><00:06:39.360><c> and</c>

00:06:39.710 --> 00:06:39.720 align:start position:0%
accuracy and adding the ease of use and
 

00:06:39.720 --> 00:06:43.189 align:start position:0%
accuracy and adding the ease of use and
scalability<00:06:40.380><c> one</c><00:06:41.220><c> can</c><00:06:41.460><c> expect</c><00:06:41.699><c> from</c><00:06:42.240><c> using</c><00:06:42.840><c> a</c>

00:06:43.189 --> 00:06:43.199 align:start position:0%
scalability one can expect from using a
 

00:06:43.199 --> 00:06:45.529 align:start position:0%
scalability one can expect from using a
feature<00:06:43.560><c> in</c><00:06:43.680><c> spark</c><00:06:44.039><c> NLP</c>

00:06:45.529 --> 00:06:45.539 align:start position:0%
feature in spark NLP
 

00:06:45.539 --> 00:06:48.170 align:start position:0%
feature in spark NLP
and<00:06:45.960><c> because</c><00:06:46.080><c> the</c><00:06:46.380><c> spark</c><00:06:46.620><c> NLP</c><00:06:47.100><c> is</c><00:06:47.520><c> a</c><00:06:47.819><c> fully</c>

00:06:48.170 --> 00:06:48.180 align:start position:0%
and because the spark NLP is a fully
 

00:06:48.180 --> 00:06:50.930 align:start position:0%
and because the spark NLP is a fully
featured<00:06:48.600><c> NLP</c><00:06:49.139><c> Library</c><00:06:49.380><c> you</c><00:06:50.280><c> can</c><00:06:50.460><c> have</c><00:06:50.639><c> the</c>

00:06:50.930 --> 00:06:50.940 align:start position:0%
featured NLP Library you can have the
 

00:06:50.940 --> 00:06:53.150 align:start position:0%
featured NLP Library you can have the
transcript<00:06:51.419><c> at</c><00:06:52.139><c> the</c><00:06:52.259><c> beginning</c><00:06:52.440><c> of</c><00:06:52.860><c> every</c>

00:06:53.150 --> 00:06:53.160 align:start position:0%
transcript at the beginning of every
 

00:06:53.160 --> 00:06:55.670 align:start position:0%
transcript at the beginning of every
possible<00:06:53.580><c> individual</c><00:06:54.240><c> pipeline</c><00:06:55.080><c> for</c>

00:06:55.670 --> 00:06:55.680 align:start position:0%
possible individual pipeline for
 

00:06:55.680 --> 00:06:58.550 align:start position:0%
possible individual pipeline for
instance<00:06:55.919><c> in</c><00:06:56.340><c> this</c><00:06:56.460><c> example</c><00:06:56.819><c> I</c><00:06:57.780><c> use</c><00:06:58.020><c> an</c><00:06:58.259><c> entity</c>

00:06:58.550 --> 00:06:58.560 align:start position:0%
instance in this example I use an entity
 

00:06:58.560 --> 00:07:00.890 align:start position:0%
instance in this example I use an entity
recognition<00:06:59.100><c> pipeline</c><00:06:59.699><c> by</c><00:07:00.120><c> using</c><00:07:00.419><c> the</c><00:07:00.600><c> ner</c>

00:07:00.890 --> 00:07:00.900 align:start position:0%
recognition pipeline by using the ner
 

00:07:00.900 --> 00:07:04.309 align:start position:0%
recognition pipeline by using the ner
model<00:07:01.319><c> trained</c><00:07:01.979><c> on</c><00:07:02.100><c> onto</c><00:07:02.580><c> nodes</c><00:07:02.880><c> and</c><00:07:03.840><c> Burton</c>

00:07:04.309 --> 00:07:04.319 align:start position:0%
model trained on onto nodes and Burton
 

00:07:04.319 --> 00:07:07.249 align:start position:0%
model trained on onto nodes and Burton
meetings<00:07:04.819><c> to</c><00:07:05.819><c> directly</c><00:07:06.300><c> extract</c><00:07:06.720><c> entities</c>

00:07:07.249 --> 00:07:07.259 align:start position:0%
meetings to directly extract entities
 

00:07:07.259 --> 00:07:10.450 align:start position:0%
meetings to directly extract entities
from<00:07:07.800><c> my</c><00:07:08.100><c> audio</c><00:07:08.340><c> files</c>

00:07:10.450 --> 00:07:10.460 align:start position:0%
from my audio files
 

00:07:10.460 --> 00:07:13.370 align:start position:0%
from my audio files
is<00:07:11.460><c> scaling</c><00:07:11.880><c> out</c><00:07:12.060><c> by</c><00:07:12.360><c> adding</c><00:07:12.780><c> more</c><00:07:12.960><c> machines</c>

00:07:13.370 --> 00:07:13.380 align:start position:0%
is scaling out by adding more machines
 

00:07:13.380 --> 00:07:15.409 align:start position:0%
is scaling out by adding more machines
has<00:07:13.800><c> its</c><00:07:14.160><c> own</c><00:07:14.220><c> overheads</c><00:07:14.880><c> and</c><00:07:15.240><c> other</c>

00:07:15.409 --> 00:07:15.419 align:start position:0%
has its own overheads and other
 

00:07:15.419 --> 00:07:18.290 align:start position:0%
has its own overheads and other
difficulties<00:07:16.199><c> that</c><00:07:16.800><c> come</c><00:07:17.039><c> with</c><00:07:17.300><c> distributed</c>

00:07:18.290 --> 00:07:18.300 align:start position:0%
difficulties that come with distributed
 

00:07:18.300 --> 00:07:21.529 align:start position:0%
difficulties that come with distributed
systems<00:07:19.139><c> that's</c><00:07:19.740><c> it</c><00:07:20.039><c> scaling</c><00:07:20.819><c> the</c><00:07:21.000><c> ASR</c>

00:07:21.529 --> 00:07:21.539 align:start position:0%
systems that's it scaling the ASR
 

00:07:21.539 --> 00:07:25.370 align:start position:0%
systems that's it scaling the ASR
pipeline<00:07:22.139><c> in</c><00:07:22.680><c> a</c><00:07:22.860><c> spark</c><00:07:23.099><c> NLP</c><00:07:23.580><c> is</c><00:07:24.120><c> linear</c><00:07:24.599><c> for</c><00:07:25.020><c> by</c>

00:07:25.370 --> 00:07:25.380 align:start position:0%
pipeline in a spark NLP is linear for by
 

00:07:25.380 --> 00:07:28.309 align:start position:0%
pipeline in a spark NLP is linear for by
adding<00:07:25.860><c> every</c><00:07:26.280><c> new</c><00:07:26.520><c> node</c><00:07:26.880><c> you</c><00:07:27.599><c> can</c><00:07:27.720><c> see</c><00:07:27.960><c> the</c>

00:07:28.309 --> 00:07:28.319 align:start position:0%
adding every new node you can see the
 

00:07:28.319 --> 00:07:30.890 align:start position:0%
adding every new node you can see the
increasing<00:07:28.919><c> performance</c><00:07:29.580><c> one</c><00:07:30.539><c> can</c><00:07:30.720><c> expect</c>

00:07:30.890 --> 00:07:30.900 align:start position:0%
increasing performance one can expect
 

00:07:30.900 --> 00:07:32.870 align:start position:0%
increasing performance one can expect
from<00:07:31.319><c> doubling</c><00:07:31.860><c> the</c><00:07:32.099><c> total</c><00:07:32.400><c> amount</c><00:07:32.759><c> of</c>

00:07:32.870 --> 00:07:32.880 align:start position:0%
from doubling the total amount of
 

00:07:32.880 --> 00:07:34.189 align:start position:0%
from doubling the total amount of
resources

00:07:34.189 --> 00:07:34.199 align:start position:0%
resources
 

00:07:34.199 --> 00:07:38.330 align:start position:0%
resources
spark<00:07:34.680><c> NLP</c><00:07:35.220><c> on</c><00:07:35.340><c> 10</c><00:07:35.520><c> nodes</c><00:07:35.880><c> by</c><00:07:36.780><c> using</c><00:07:37.139><c> CPUs</c><00:07:37.740><c> is</c>

00:07:38.330 --> 00:07:38.340 align:start position:0%
spark NLP on 10 nodes by using CPUs is
 

00:07:38.340 --> 00:07:41.390 align:start position:0%
spark NLP on 10 nodes by using CPUs is
up<00:07:38.759><c> to</c><00:07:38.940><c> nine</c><00:07:39.419><c> times</c><00:07:39.780><c> faster</c><00:07:40.500><c> than</c><00:07:40.860><c> a</c><00:07:41.099><c> single</c>

00:07:41.390 --> 00:07:41.400 align:start position:0%
up to nine times faster than a single
 

00:07:41.400 --> 00:07:43.490 align:start position:0%
up to nine times faster than a single
machine

00:07:43.490 --> 00:07:43.500 align:start position:0%
machine
 

00:07:43.500 --> 00:07:45.950 align:start position:0%
machine
and<00:07:44.099><c> the</c><00:07:44.280><c> linear</c><00:07:44.580><c> scaling</c><00:07:45.180><c> is</c><00:07:45.539><c> not</c><00:07:45.780><c> just</c>

00:07:45.950 --> 00:07:45.960 align:start position:0%
and the linear scaling is not just
 

00:07:45.960 --> 00:07:49.730 align:start position:0%
and the linear scaling is not just
limited<00:07:46.380><c> to</c><00:07:46.560><c> the</c><00:07:46.680><c> CPUs</c><00:07:47.160><c> spark</c><00:07:48.000><c> NLP</c><00:07:48.539><c> scales</c><00:07:49.500><c> out</c>

00:07:49.730 --> 00:07:49.740 align:start position:0%
limited to the CPUs spark NLP scales out
 

00:07:49.740 --> 00:07:54.110 align:start position:0%
limited to the CPUs spark NLP scales out
on<00:07:50.099><c> gpus</c><00:07:50.720><c> with</c><00:07:51.720><c> such</c><00:07:52.080><c> Simplicity</c><00:07:52.860><c> and</c><00:07:53.580><c> quality</c>

00:07:54.110 --> 00:07:54.120 align:start position:0%
on gpus with such Simplicity and quality
 

00:07:54.120 --> 00:07:58.010 align:start position:0%
on gpus with such Simplicity and quality
that<00:07:54.900><c> a</c><00:07:55.080><c> cluster</c><00:07:55.440><c> of</c><00:07:55.620><c> 10</c><00:07:55.919><c> nodes</c><00:07:56.340><c> with</c><00:07:56.819><c> GPU</c><00:07:57.419><c> can</c>

00:07:58.010 --> 00:07:58.020 align:start position:0%
that a cluster of 10 nodes with GPU can
 

00:07:58.020 --> 00:08:01.809 align:start position:0%
that a cluster of 10 nodes with GPU can
be<00:07:58.199><c> sped</c><00:07:58.680><c> up</c><00:07:59.039><c> to</c><00:07:59.280><c> 10</c><00:07:59.639><c> times</c><00:08:00.000><c> that's</c><00:08:00.960><c> 900</c>

00:08:01.809 --> 00:08:01.819 align:start position:0%
be sped up to 10 times that's 900
 

00:08:01.819 --> 00:08:05.210 align:start position:0%
be sped up to 10 times that's 900
Improvement<00:08:02.819><c> in</c><00:08:03.539><c> performance</c><00:08:04.080><c> compared</c><00:08:04.919><c> to</c><00:08:05.039><c> a</c>

00:08:05.210 --> 00:08:05.220 align:start position:0%
Improvement in performance compared to a
 

00:08:05.220 --> 00:08:07.909 align:start position:0%
Improvement in performance compared to a
single<00:08:05.520><c> machine</c><00:08:05.699><c> on</c><00:08:06.120><c> a</c><00:08:06.240><c> single</c><00:08:06.539><c> GPU</c>

00:08:07.909 --> 00:08:07.919 align:start position:0%
single machine on a single GPU
 

00:08:07.919 --> 00:08:10.550 align:start position:0%
single machine on a single GPU
this<00:08:08.639><c> simply</c><00:08:08.940><c> allows</c><00:08:09.360><c> you</c><00:08:09.479><c> to</c><00:08:09.720><c> save</c><00:08:09.900><c> money</c><00:08:10.259><c> and</c>

00:08:10.550 --> 00:08:10.560 align:start position:0%
this simply allows you to save money and
 

00:08:10.560 --> 00:08:12.950 align:start position:0%
this simply allows you to save money and
time<00:08:10.800><c> on</c><00:08:11.280><c> computation</c><00:08:11.880><c> in</c><00:08:12.599><c> production</c>

00:08:12.950 --> 00:08:12.960 align:start position:0%
time on computation in production
 

00:08:12.960 --> 00:08:17.270 align:start position:0%
time on computation in production
whether<00:08:13.860><c> you</c><00:08:14.039><c> decide</c><00:08:14.400><c> to</c><00:08:14.580><c> go</c><00:08:14.759><c> with</c><00:08:15.000><c> CPU</c><00:08:15.479><c> or</c><00:08:16.139><c> GPU</c>

00:08:17.270 --> 00:08:17.280 align:start position:0%
whether you decide to go with CPU or GPU
 

00:08:17.280 --> 00:08:19.670 align:start position:0%
whether you decide to go with CPU or GPU
I<00:08:18.000><c> would</c><00:08:18.180><c> like</c><00:08:18.300><c> to</c><00:08:18.479><c> thank</c><00:08:18.660><c> you</c><00:08:18.840><c> for</c><00:08:19.199><c> your</c><00:08:19.440><c> time</c>

00:08:19.670 --> 00:08:19.680 align:start position:0%
I would like to thank you for your time
 

00:08:19.680 --> 00:08:21.710 align:start position:0%
I would like to thank you for your time
and<00:08:20.099><c> wish</c><00:08:20.400><c> you</c><00:08:20.580><c> all</c><00:08:20.699><c> great</c><00:08:21.120><c> and</c><00:08:21.360><c> healthy</c>

00:08:21.710 --> 00:08:21.720 align:start position:0%
and wish you all great and healthy
 

00:08:21.720 --> 00:08:25.400 align:start position:0%
and wish you all great and healthy
something<00:08:21.900><c> ahead</c><00:08:22.500><c> thank</c><00:08:22.979><c> you</c><00:08:23.099><c> very</c><00:08:23.220><c> much</c>

