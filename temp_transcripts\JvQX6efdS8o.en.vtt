WEBVTT
Kind: captions
Language: en

00:00:02.510 --> 00:00:08.710 align:start position:0%
 
[Music]

00:00:08.710 --> 00:00:08.720 align:start position:0%
[Music]
 

00:00:08.720 --> 00:00:10.790 align:start position:0%
[Music]
java<00:00:09.120><c> provides</c><00:00:09.519><c> for</c><00:00:09.679><c> multiple</c><00:00:10.080><c> value</c><00:00:10.400><c> control</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
java provides for multiple value control
 

00:00:10.800 --> 00:00:12.709 align:start position:0%
java provides for multiple value control
flow<00:00:11.120><c> using</c><00:00:11.440><c> a</c><00:00:11.519><c> switch</c><00:00:11.840><c> statement</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
flow using a switch statement
 

00:00:12.719 --> 00:00:14.470 align:start position:0%
flow using a switch statement
unlike<00:00:13.200><c> if-then</c><00:00:13.679><c> and</c><00:00:13.840><c> if-then-else</c>

00:00:14.470 --> 00:00:14.480 align:start position:0%
unlike if-then and if-then-else
 

00:00:14.480 --> 00:00:15.990 align:start position:0%
unlike if-then and if-then-else
statements<00:00:15.040><c> the</c><00:00:15.200><c> switch</c><00:00:15.440><c> statement</c><00:00:15.759><c> can</c><00:00:15.839><c> have</c>

00:00:15.990 --> 00:00:16.000 align:start position:0%
statements the switch statement can have
 

00:00:16.000 --> 00:00:18.390 align:start position:0%
statements the switch statement can have
a<00:00:16.080><c> number</c><00:00:16.320><c> of</c><00:00:16.480><c> possible</c><00:00:16.880><c> execution</c><00:00:17.440><c> paths</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
a number of possible execution paths
 

00:00:18.400 --> 00:00:20.950 align:start position:0%
a number of possible execution paths
a<00:00:18.560><c> switch</c><00:00:18.880><c> works</c><00:00:19.119><c> with</c><00:00:19.279><c> the</c><00:00:19.439><c> byte</c><00:00:20.000><c> short</c><00:00:20.560><c> char</c>

00:00:20.950 --> 00:00:20.960 align:start position:0%
a switch works with the byte short char
 

00:00:20.960 --> 00:00:21.510 align:start position:0%
a switch works with the byte short char
and<00:00:21.119><c> int</c>

00:00:21.510 --> 00:00:21.520 align:start position:0%
and int
 

00:00:21.520 --> 00:00:23.349 align:start position:0%
and int
primitive<00:00:21.920><c> data</c><00:00:22.240><c> types</c><00:00:22.640><c> it</c><00:00:22.800><c> also</c><00:00:23.039><c> works</c><00:00:23.199><c> with</c>

00:00:23.349 --> 00:00:23.359 align:start position:0%
primitive data types it also works with
 

00:00:23.359 --> 00:00:24.790 align:start position:0%
primitive data types it also works with
enum<00:00:23.760><c> types</c><00:00:24.240><c> strings</c>

00:00:24.790 --> 00:00:24.800 align:start position:0%
enum types strings
 

00:00:24.800 --> 00:00:27.269 align:start position:0%
enum types strings
and<00:00:24.880><c> the</c><00:00:25.039><c> wrapper</c><00:00:25.359><c> classes</c><00:00:25.920><c> character</c><00:00:26.720><c> byte</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
and the wrapper classes character byte
 

00:00:27.279 --> 00:00:29.109 align:start position:0%
and the wrapper classes character byte
short<00:00:27.760><c> and</c><00:00:28.000><c> integer</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
short and integer
 

00:00:29.119 --> 00:00:32.870 align:start position:0%
short and integer
here<00:00:29.359><c> is</c><00:00:29.439><c> an</c><00:00:29.599><c> example</c><00:00:29.920><c> switch</c><00:00:30.240><c> statement</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
 
 

00:00:32.880 --> 00:00:34.389 align:start position:0%
 
the<00:00:33.040><c> body</c><00:00:33.280><c> of</c><00:00:33.360><c> a</c><00:00:33.440><c> switch</c><00:00:33.680><c> statement</c><00:00:34.079><c> is</c><00:00:34.160><c> known</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
the body of a switch statement is known
 

00:00:34.399 --> 00:00:36.549 align:start position:0%
the body of a switch statement is known
as<00:00:34.559><c> a</c><00:00:34.640><c> switch</c><00:00:35.040><c> block</c><00:00:35.680><c> a</c><00:00:35.840><c> statement</c><00:00:36.239><c> can</c><00:00:36.399><c> be</c>

00:00:36.549 --> 00:00:36.559 align:start position:0%
as a switch block a statement can be
 

00:00:36.559 --> 00:00:37.910 align:start position:0%
as a switch block a statement can be
labeled<00:00:36.880><c> with</c><00:00:37.040><c> one</c><00:00:37.360><c> or</c><00:00:37.520><c> more</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
labeled with one or more
 

00:00:37.920 --> 00:00:40.389 align:start position:0%
labeled with one or more
case<00:00:38.320><c> or</c><00:00:38.559><c> default</c><00:00:39.120><c> labels</c><00:00:39.920><c> the</c><00:00:40.079><c> switch</c>

00:00:40.389 --> 00:00:40.399 align:start position:0%
case or default labels the switch
 

00:00:40.399 --> 00:00:42.069 align:start position:0%
case or default labels the switch
statement<00:00:40.719><c> evaluates</c><00:00:41.280><c> the</c><00:00:41.440><c> expression</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
statement evaluates the expression
 

00:00:42.079 --> 00:00:43.910 align:start position:0%
statement evaluates the expression
and<00:00:42.160><c> causes</c><00:00:42.559><c> control</c><00:00:42.960><c> flow</c><00:00:43.280><c> to</c><00:00:43.440><c> jump</c><00:00:43.600><c> to</c><00:00:43.760><c> the</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
and causes control flow to jump to the
 

00:00:43.920 --> 00:00:45.430 align:start position:0%
and causes control flow to jump to the
code<00:00:44.160><c> location</c><00:00:44.800><c> labeled</c>

00:00:45.430 --> 00:00:45.440 align:start position:0%
code location labeled
 

00:00:45.440 --> 00:00:48.869 align:start position:0%
code location labeled
with<00:00:45.680><c> the</c><00:00:45.840><c> value</c><00:00:46.160><c> of</c><00:00:46.399><c> this</c><00:00:46.640><c> expression</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
with the value of this expression
 

00:00:48.879 --> 00:00:52.470 align:start position:0%
with the value of this expression
this<00:00:49.120><c> means</c><00:00:49.360><c> if</c><00:00:49.520><c> we</c><00:00:49.760><c> pass</c><00:00:50.320><c> wed</c><00:00:51.360><c> as</c><00:00:51.600><c> a</c><00:00:51.680><c> parameter</c>

00:00:52.470 --> 00:00:52.480 align:start position:0%
this means if we pass wed as a parameter
 

00:00:52.480 --> 00:00:55.510 align:start position:0%
this means if we pass wed as a parameter
then<00:00:52.719><c> it</c><00:00:52.800><c> jumps</c><00:00:53.039><c> to</c><00:00:53.199><c> the</c><00:00:53.360><c> label</c><00:00:53.840><c> or</c><00:00:54.079><c> case</c><00:00:54.559><c> wed</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
then it jumps to the label or case wed
 

00:00:55.520 --> 00:00:58.150 align:start position:0%
then it jumps to the label or case wed
and<00:00:55.680><c> will</c><00:00:55.920><c> print</c><00:00:56.239><c> that</c><00:00:56.480><c> statement</c><00:00:57.760><c> if</c><00:00:57.920><c> there</c>

00:00:58.150 --> 00:00:58.160 align:start position:0%
and will print that statement if there
 

00:00:58.160 --> 00:00:59.270 align:start position:0%
and will print that statement if there
is<00:00:58.239><c> no</c><00:00:58.480><c> matching</c><00:00:58.800><c> label</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
is no matching label
 

00:00:59.280 --> 00:01:01.110 align:start position:0%
is no matching label
then<00:00:59.440><c> control</c><00:00:59.840><c> flow</c><00:01:00.079><c> jumps</c><00:01:00.320><c> to</c><00:01:00.480><c> the</c><00:01:00.559><c> location</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
then control flow jumps to the location
 

00:01:01.120 --> 00:01:03.349 align:start position:0%
then control flow jumps to the location
labeled<00:01:01.520><c> default</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
labeled default
 

00:01:03.359 --> 00:01:06.390 align:start position:0%
labeled default
if<00:01:03.520><c> we</c><00:01:03.680><c> put</c><00:01:03.920><c> in</c><00:01:04.239><c> nop</c><00:01:05.600><c> it</c><00:01:05.760><c> will</c><00:01:05.920><c> print</c><00:01:06.159><c> out</c><00:01:06.240><c> the</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
if we put in nop it will print out the
 

00:01:06.400 --> 00:01:07.990 align:start position:0%
if we put in nop it will print out the
default<00:01:06.880><c> message</c>

00:01:07.990 --> 00:01:08.000 align:start position:0%
default message
 

00:01:08.000 --> 00:01:10.310 align:start position:0%
default message
this<00:01:08.159><c> is</c><00:01:08.320><c> the</c><00:01:08.560><c> only</c><00:01:08.960><c> explicit</c><00:01:09.600><c> jump</c><00:01:09.920><c> performed</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
this is the only explicit jump performed
 

00:01:10.320 --> 00:01:12.870 align:start position:0%
this is the only explicit jump performed
by<00:01:10.479><c> the</c><00:01:10.560><c> switch</c><00:01:10.880><c> statement</c><00:01:11.840><c> flow</c><00:01:12.159><c> of</c><00:01:12.240><c> control</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
by the switch statement flow of control
 

00:01:12.880 --> 00:01:14.950 align:start position:0%
by the switch statement flow of control
falls<00:01:13.280><c> through</c><00:01:13.840><c> to</c><00:01:14.000><c> the</c><00:01:14.080><c> next</c><00:01:14.320><c> case</c><00:01:14.640><c> if</c><00:01:14.799><c> the</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
falls through to the next case if the
 

00:01:14.960 --> 00:01:16.630 align:start position:0%
falls through to the next case if the
code<00:01:15.200><c> for</c><00:01:15.360><c> a</c><00:01:15.439><c> case</c><00:01:15.759><c> is</c><00:01:15.840><c> not</c><00:01:16.080><c> ended</c><00:01:16.320><c> with</c><00:01:16.479><c> a</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
code for a case is not ended with a
 

00:01:16.640 --> 00:01:17.590 align:start position:0%
code for a case is not ended with a
break<00:01:16.880><c> statement</c>

00:01:17.590 --> 00:01:17.600 align:start position:0%
break statement
 

00:01:17.600 --> 00:01:19.510 align:start position:0%
break statement
which<00:01:17.840><c> causes</c><00:01:18.159><c> control</c><00:01:18.560><c> flow</c><00:01:18.880><c> to</c><00:01:19.040><c> jump</c><00:01:19.200><c> to</c><00:01:19.360><c> the</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
which causes control flow to jump to the
 

00:01:19.520 --> 00:01:21.429 align:start position:0%
which causes control flow to jump to the
end

00:01:21.429 --> 00:01:21.439 align:start position:0%
end
 

00:01:21.439 --> 00:01:23.270 align:start position:0%
end
if<00:01:21.600><c> we</c><00:01:21.759><c> don't</c><00:01:21.920><c> use</c><00:01:22.159><c> a</c><00:01:22.240><c> break</c><00:01:22.640><c> then</c><00:01:22.880><c> we'll</c><00:01:23.040><c> print</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
if we don't use a break then we'll print
 

00:01:23.280 --> 00:01:25.030 align:start position:0%
if we don't use a break then we'll print
out<00:01:23.360><c> the</c><00:01:23.439><c> case</c><00:01:23.680><c> we</c><00:01:23.920><c> found</c><00:01:24.159><c> statement</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
out the case we found statement
 

00:01:25.040 --> 00:01:27.510 align:start position:0%
out the case we found statement
plus<00:01:25.360><c> the</c><00:01:25.520><c> ones</c><00:01:25.840><c> below</c><00:01:26.240><c> it</c><00:01:26.479><c> until</c><00:01:26.960><c> there</c><00:01:27.200><c> is</c><00:01:27.360><c> a</c>

00:01:27.510 --> 00:01:27.520 align:start position:0%
plus the ones below it until there is a
 

00:01:27.520 --> 00:01:28.310 align:start position:0%
plus the ones below it until there is a
break<00:01:27.759><c> statement</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
break statement
 

00:01:28.320 --> 00:01:31.270 align:start position:0%
break statement
or<00:01:28.560><c> we</c><00:01:28.720><c> get</c><00:01:28.880><c> to</c><00:01:29.040><c> the</c><00:01:29.200><c> default</c><00:01:29.680><c> statement</c><00:01:30.960><c> here</c>

00:01:31.270 --> 00:01:31.280 align:start position:0%
or we get to the default statement here
 

00:01:31.280 --> 00:01:32.149 align:start position:0%
or we get to the default statement here
if<00:01:31.439><c> we</c><00:01:31.600><c> put</c><00:01:31.840><c> in</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
if we put in
 

00:01:32.159 --> 00:01:34.870 align:start position:0%
if we put in
m-o-n<00:01:33.680><c> and</c><00:01:33.840><c> we</c><00:01:34.000><c> don't</c><00:01:34.159><c> have</c><00:01:34.400><c> any</c><00:01:34.640><c> break</c>

00:01:34.870 --> 00:01:34.880 align:start position:0%
m-o-n and we don't have any break
 

00:01:34.880 --> 00:01:36.230 align:start position:0%
m-o-n and we don't have any break
statements<00:01:35.280><c> it</c><00:01:35.439><c> will</c><00:01:35.600><c> print</c><00:01:35.840><c> out</c><00:01:36.000><c> all</c><00:01:36.079><c> the</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
statements it will print out all the
 

00:01:36.240 --> 00:01:37.670 align:start position:0%
statements it will print out all the
cases<00:01:36.640><c> below</c><00:01:36.880><c> it</c><00:01:37.119><c> as</c><00:01:37.280><c> well</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
cases below it as well
 

00:01:37.680 --> 00:01:39.429 align:start position:0%
cases below it as well
including<00:01:38.079><c> the</c><00:01:38.240><c> default</c><00:01:38.640><c> message</c><00:01:39.280><c> a</c>

00:01:39.429 --> 00:01:39.439 align:start position:0%
including the default message a
 

00:01:39.439 --> 00:01:41.030 align:start position:0%
including the default message a
statement<00:01:39.759><c> can</c><00:01:39.920><c> have</c><00:01:40.079><c> multiple</c><00:01:40.400><c> case</c><00:01:40.640><c> labels</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
statement can have multiple case labels
 

00:01:41.040 --> 00:01:42.469 align:start position:0%
statement can have multiple case labels
as<00:01:41.200><c> shown</c><00:01:41.439><c> here</c><00:01:41.680><c> in</c><00:01:41.759><c> this</c><00:01:41.920><c> code</c>

00:01:42.469 --> 00:01:42.479 align:start position:0%
as shown here in this code
 

00:01:42.479 --> 00:01:44.069 align:start position:0%
as shown here in this code
we<00:01:42.640><c> can</c><00:01:42.799><c> execute</c><00:01:43.200><c> the</c><00:01:43.280><c> code</c><00:01:43.520><c> with</c><00:01:43.840><c> any</c><00:01:44.000><c> of</c>

00:01:44.069 --> 00:01:44.079 align:start position:0%
we can execute the code with any of
 

00:01:44.079 --> 00:01:45.830 align:start position:0%
we can execute the code with any of
these<00:01:44.320><c> cases</c><00:01:44.720><c> and</c><00:01:44.880><c> will</c><00:01:45.040><c> print</c><00:01:45.280><c> out</c><00:01:45.439><c> the</c><00:01:45.600><c> same</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
these cases and will print out the same
 

00:01:45.840 --> 00:01:46.950 align:start position:0%
these cases and will print out the same
statement

00:01:46.950 --> 00:01:46.960 align:start position:0%
statement
 

00:01:46.960 --> 00:01:48.789 align:start position:0%
statement
deciding<00:01:47.520><c> whether</c><00:01:47.759><c> to</c><00:01:47.920><c> use</c><00:01:48.159><c> if</c><00:01:48.399><c> then</c><00:01:48.640><c> l</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
deciding whether to use if then l
 

00:01:48.799 --> 00:01:50.310 align:start position:0%
deciding whether to use if then l
statements<00:01:49.200><c> or</c><00:01:49.360><c> switch</c><00:01:49.680><c> statement</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
statements or switch statement
 

00:01:50.320 --> 00:01:51.749 align:start position:0%
statements or switch statement
is<00:01:50.399><c> based</c><00:01:50.720><c> on</c><00:01:50.880><c> readability</c><00:01:51.520><c> and</c><00:01:51.680><c> the</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
is based on readability and the
 

00:01:51.759 --> 00:01:53.749 align:start position:0%
is based on readability and the
expression<00:01:52.159><c> that</c><00:01:52.240><c> the</c><00:01:52.320><c> statement</c><00:01:52.640><c> is</c><00:01:52.799><c> testing</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
expression that the statement is testing
 

00:01:53.759 --> 00:01:55.270 align:start position:0%
expression that the statement is testing
an<00:01:53.920><c> if</c><00:01:54.159><c> an</c><00:01:54.320><c> else</c><00:01:54.479><c> statement</c><00:01:54.799><c> can</c><00:01:54.960><c> test</c>

00:01:55.270 --> 00:01:55.280 align:start position:0%
an if an else statement can test
 

00:01:55.280 --> 00:01:57.270 align:start position:0%
an if an else statement can test
expressions<00:01:55.680><c> based</c><00:01:55.920><c> on</c><00:01:56.159><c> ranges</c><00:01:56.479><c> of</c><00:01:56.640><c> values</c><00:01:57.040><c> or</c>

00:01:57.270 --> 00:01:57.280 align:start position:0%
expressions based on ranges of values or
 

00:01:57.280 --> 00:01:58.550 align:start position:0%
expressions based on ranges of values or
conditions

00:01:58.550 --> 00:01:58.560 align:start position:0%
conditions
 

00:01:58.560 --> 00:01:59.990 align:start position:0%
conditions
whereas<00:01:58.960><c> a</c><00:01:59.040><c> switch</c><00:01:59.280><c> statement</c><00:01:59.759><c> test</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
whereas a switch statement test
 

00:02:00.000 --> 00:02:01.590 align:start position:0%
whereas a switch statement test
expression<00:02:00.399><c> is</c><00:02:00.479><c> based</c><00:02:00.719><c> only</c><00:02:00.960><c> on</c><00:02:01.119><c> a</c><00:02:01.200><c> single</c>

00:02:01.590 --> 00:02:01.600 align:start position:0%
expression is based only on a single
 

00:02:01.600 --> 00:02:02.709 align:start position:0%
expression is based only on a single
entity

00:02:02.709 --> 00:02:02.719 align:start position:0%
entity
 

00:02:02.719 --> 00:02:09.760 align:start position:0%
entity
like<00:02:02.960><c> a</c><00:02:03.119><c> string</c><00:02:03.520><c> or</c><00:02:03.759><c> an</c><00:02:03.840><c> enum</c><00:02:06.840><c> type</c>

00:02:09.760 --> 00:02:09.770 align:start position:0%
like a string or an enum type
 

00:02:09.770 --> 00:02:13.990 align:start position:0%
like a string or an enum type
[Music]

00:02:13.990 --> 00:02:14.000 align:start position:0%
[Music]
 

00:02:14.000 --> 00:02:16.080 align:start position:0%
[Music]
you

