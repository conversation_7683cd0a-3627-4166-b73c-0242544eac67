WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:06.110 align:start position:0%
 
[Music]

00:00:06.110 --> 00:00:06.120 align:start position:0%
 
 

00:00:06.120 --> 00:00:08.350 align:start position:0%
 
hey<00:00:06.319><c> folks</c><00:00:06.720><c> I'm</c><00:00:06.839><c> Shay</c><00:00:07.279><c> I'm</c><00:00:07.359><c> a</c><00:00:07.600><c> solution</c>

00:00:08.350 --> 00:00:08.360 align:start position:0%
hey folks I'm Shay I'm a solution
 

00:00:08.360 --> 00:00:10.870 align:start position:0%
hey folks I'm Shay I'm a solution
Solutions<00:00:08.920><c> engineer</c><00:00:09.400><c> here</c><00:00:09.559><c> at</c><00:00:09.719><c> Clos</c><00:00:10.080><c> Loop</c><00:00:10.320><c> AI</c>

00:00:10.870 --> 00:00:10.880 align:start position:0%
Solutions engineer here at Clos Loop AI
 

00:00:10.880 --> 00:00:13.070 align:start position:0%
Solutions engineer here at Clos Loop AI
we<00:00:11.000><c> are</c><00:00:11.759><c> healthc</c><00:00:12.120><c> Care's</c><00:00:12.400><c> data</c><00:00:12.719><c> science</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
we are healthc Care's data science
 

00:00:13.080 --> 00:00:15.070 align:start position:0%
we are healthc Care's data science
platform<00:00:13.719><c> we</c><00:00:13.960><c> help</c><00:00:14.639><c> Healthcare</c>

00:00:15.070 --> 00:00:15.080 align:start position:0%
platform we help Healthcare
 

00:00:15.080 --> 00:00:16.950 align:start position:0%
platform we help Healthcare
organizations<00:00:15.839><c> improve</c><00:00:16.320><c> outcomes</c><00:00:16.760><c> and</c>

00:00:16.950 --> 00:00:16.960 align:start position:0%
organizations improve outcomes and
 

00:00:16.960 --> 00:00:20.390 align:start position:0%
organizations improve outcomes and
reduce<00:00:17.439><c> costs</c><00:00:18.039><c> with</c><00:00:18.760><c> accurate</c><00:00:19.480><c> and</c><00:00:19.760><c> also</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
reduce costs with accurate and also
 

00:00:20.400 --> 00:00:22.950 align:start position:0%
reduce costs with accurate and also
explainable<00:00:21.400><c> actionable</c><00:00:21.920><c> predictions</c><00:00:22.720><c> of</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
explainable actionable predictions of
 

00:00:22.960 --> 00:00:25.349 align:start position:0%
explainable actionable predictions of
individual<00:00:23.480><c> level</c><00:00:23.760><c> health</c><00:00:24.160><c> risks</c><00:00:24.800><c> our</c>

00:00:25.349 --> 00:00:25.359 align:start position:0%
individual level health risks our
 

00:00:25.359 --> 00:00:29.269 align:start position:0%
individual level health risks our
customers<00:00:26.240><c> data</c><00:00:26.920><c> will</c><00:00:27.920><c> um</c><00:00:28.080><c> or</c><00:00:28.359><c> our</c><00:00:28.640><c> customers</c>

00:00:29.269 --> 00:00:29.279 align:start position:0%
customers data will um or our customers
 

00:00:29.279 --> 00:00:30.589 align:start position:0%
customers data will um or our customers
will<00:00:29.480><c> include</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
will include
 

00:00:30.599 --> 00:00:33.590 align:start position:0%
will include
organizations<00:00:31.599><c> like</c><00:00:31.920><c> Health</c><00:00:32.279><c> Systems</c><00:00:33.079><c> risk</c>

00:00:33.590 --> 00:00:33.600 align:start position:0%
organizations like Health Systems risk
 

00:00:33.600 --> 00:00:35.950 align:start position:0%
organizations like Health Systems risk
taking<00:00:34.040><c> providers</c><00:00:35.040><c> and</c><00:00:35.280><c> also</c><00:00:35.680><c> health</c>

00:00:35.950 --> 00:00:35.960 align:start position:0%
taking providers and also health
 

00:00:35.960 --> 00:00:37.350 align:start position:0%
taking providers and also health
insurance

00:00:37.350 --> 00:00:37.360 align:start position:0%
insurance
 

00:00:37.360 --> 00:00:41.910 align:start position:0%
insurance
plans<00:00:38.360><c> today</c><00:00:39.200><c> I</c><00:00:39.399><c> will</c><00:00:39.800><c> demo</c><00:00:40.640><c> a</c><00:00:40.879><c> new</c><00:00:41.280><c> platform</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
plans today I will demo a new platform
 

00:00:41.920 --> 00:00:44.029 align:start position:0%
plans today I will demo a new platform
capability<00:00:42.760><c> that</c><00:00:42.920><c> we've</c><00:00:43.239><c> developed</c><00:00:43.800><c> in</c>

00:00:44.029 --> 00:00:44.039 align:start position:0%
capability that we've developed in
 

00:00:44.039 --> 00:00:46.709 align:start position:0%
capability that we've developed in
collaboration<00:00:44.640><c> with</c><00:00:44.800><c> Jon</c><00:00:45.079><c> Snow</c><00:00:45.520><c> Labs</c><00:00:46.520><c> I'm</c>

00:00:46.709 --> 00:00:46.719 align:start position:0%
collaboration with Jon Snow Labs I'm
 

00:00:46.719 --> 00:00:49.430 align:start position:0%
collaboration with Jon Snow Labs I'm
going<00:00:46.920><c> to</c><00:00:47.199><c> show</c><00:00:47.559><c> you</c><00:00:48.000><c> how</c><00:00:48.160><c> you</c><00:00:48.320><c> can</c><00:00:48.559><c> use</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
going to show you how you can use
 

00:00:49.440 --> 00:00:51.590 align:start position:0%
going to show you how you can use
generative<00:00:50.120><c> AI</c><00:00:50.480><c> models</c><00:00:50.920><c> or</c><00:00:51.079><c> or</c><00:00:51.239><c> large</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
generative AI models or or large
 

00:00:51.600 --> 00:00:54.430 align:start position:0%
generative AI models or or large
language<00:00:52.000><c> models</c><00:00:52.840><c> to</c><00:00:53.160><c> segment</c><00:00:53.719><c> highrisk</c>

00:00:54.430 --> 00:00:54.440 align:start position:0%
language models to segment highrisk
 

00:00:54.440 --> 00:00:58.189 align:start position:0%
language models to segment highrisk
patients<00:00:55.440><c> based</c><00:00:56.000><c> on</c><00:00:56.480><c> their</c><00:00:56.960><c> clinical</c><00:00:57.440><c> data</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
patients based on their clinical data
 

00:00:58.199 --> 00:01:02.830 align:start position:0%
patients based on their clinical data
and<00:00:58.760><c> this</c><00:00:59.039><c> tool</c><00:01:00.320><c> is</c><00:01:01.239><c> um</c><00:01:01.640><c> meant</c><00:01:01.879><c> to</c><00:01:02.039><c> be</c><00:01:02.239><c> useful</c>

00:01:02.830 --> 00:01:02.840 align:start position:0%
and this tool is um meant to be useful
 

00:01:02.840 --> 00:01:05.469 align:start position:0%
and this tool is um meant to be useful
for<00:01:03.199><c> care</c><00:01:03.440><c> managers</c><00:01:03.960><c> and</c><00:01:04.360><c> population</c><00:01:04.960><c> Health</c>

00:01:05.469 --> 00:01:05.479 align:start position:0%
for care managers and population Health
 

00:01:05.479 --> 00:01:08.350 align:start position:0%
for care managers and population Health
managers<00:01:06.000><c> to</c><00:01:06.400><c> identify</c><00:01:07.400><c> prevalent</c><00:01:07.920><c> and</c><00:01:08.119><c> and</c>

00:01:08.350 --> 00:01:08.360 align:start position:0%
managers to identify prevalent and and
 

00:01:08.360 --> 00:01:11.350 align:start position:0%
managers to identify prevalent and and
common<00:01:08.840><c> patients</c><00:01:09.840><c> and</c><00:01:10.119><c> design</c><00:01:10.720><c> targeted</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
common patients and design targeted
 

00:01:11.360 --> 00:01:14.310 align:start position:0%
common patients and design targeted
intervention<00:01:12.000><c> strategies</c><00:01:12.920><c> so</c><00:01:13.119><c> let's</c><00:01:13.360><c> dive</c><00:01:13.560><c> in</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
intervention strategies so let's dive in
 

00:01:14.320 --> 00:01:17.230 align:start position:0%
intervention strategies so let's dive in
this<00:01:14.840><c> web</c><00:01:15.119><c> interface</c><00:01:15.640><c> right</c><00:01:16.040><c> here</c><00:01:16.880><c> is</c><00:01:17.080><c> the</c>

00:01:17.230 --> 00:01:17.240 align:start position:0%
this web interface right here is the
 

00:01:17.240 --> 00:01:20.149 align:start position:0%
this web interface right here is the
closed<00:01:17.560><c> loop</c><00:01:17.880><c> platform</c><00:01:18.880><c> I've</c><00:01:19.080><c> loaded</c><00:01:19.520><c> in</c><00:01:19.759><c> some</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
closed loop platform I've loaded in some
 

00:01:20.159 --> 00:01:22.710 align:start position:0%
closed loop platform I've loaded in some
demo<00:01:20.600><c> data</c><00:01:21.040><c> there's</c><00:01:21.360><c> there's</c><00:01:21.600><c> about</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
demo data there's there's about
 

00:01:22.720 --> 00:01:26.990 align:start position:0%
demo data there's there's about
67,000<00:01:24.200><c> patients</c><00:01:25.200><c> and</c><00:01:25.759><c> this</c><00:01:26.040><c> PA</c><00:01:26.360><c> this</c><00:01:26.560><c> patient</c>

00:01:26.990 --> 00:01:27.000 align:start position:0%
67,000 patients and this PA this patient
 

00:01:27.000 --> 00:01:30.149 align:start position:0%
67,000 patients and this PA this patient
population<00:01:27.560><c> is</c><00:01:27.720><c> meant</c><00:01:28.000><c> to</c><00:01:28.280><c> mimic</c><00:01:29.280><c> a</c><00:01:29.720><c> uh</c><00:01:30.000><c> a</c>

00:01:30.149 --> 00:01:30.159 align:start position:0%
population is meant to mimic a uh a
 

00:01:30.159 --> 00:01:32.950 align:start position:0%
population is meant to mimic a uh a
typical<00:01:30.520><c> Medicare</c><00:01:31.119><c> population</c><00:01:31.799><c> so</c><00:01:32.600><c> we'll</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
typical Medicare population so we'll
 

00:01:32.960 --> 00:01:37.350 align:start position:0%
typical Medicare population so we'll
have<00:01:33.280><c> in</c><00:01:33.759><c> it</c><00:01:34.759><c> mostly</c><00:01:35.439><c> elderly</c><00:01:36.439><c> individuals</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
have in it mostly elderly individuals
 

00:01:37.360 --> 00:01:39.270 align:start position:0%
have in it mostly elderly individuals
individuals<00:01:37.920><c> with</c><00:01:38.119><c> chronic</c><00:01:38.520><c> diseases</c><00:01:39.040><c> and</c>

00:01:39.270 --> 00:01:39.280 align:start position:0%
individuals with chronic diseases and
 

00:01:39.280 --> 00:01:42.789 align:start position:0%
individuals with chronic diseases and
severe<00:01:40.079><c> illnesses</c><00:01:41.079><c> and</c><00:01:41.479><c> a</c><00:01:41.680><c> common</c><00:01:42.040><c> use</c><00:01:42.439><c> case</c>

00:01:42.789 --> 00:01:42.799 align:start position:0%
severe illnesses and a common use case
 

00:01:42.799 --> 00:01:45.870 align:start position:0%
severe illnesses and a common use case
of<00:01:43.040><c> the</c><00:01:43.360><c> platform</c><00:01:44.360><c> is</c><00:01:44.759><c> risk</c><00:01:45.119><c> stratifying</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
of the platform is risk stratifying
 

00:01:45.880 --> 00:01:47.469 align:start position:0%
of the platform is risk stratifying
these<00:01:46.119><c> patients</c><00:01:46.560><c> so</c><00:01:46.680><c> you</c><00:01:46.799><c> may</c><00:01:46.960><c> want</c><00:01:47.079><c> to</c><00:01:47.240><c> know</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
these patients so you may want to know
 

00:01:47.479 --> 00:01:51.069 align:start position:0%
these patients so you may want to know
for<00:01:47.840><c> example</c><00:01:48.840><c> who's</c><00:01:49.240><c> likely</c><00:01:49.640><c> to</c><00:01:50.040><c> have</c><00:01:50.920><c> an</c>

00:01:51.069 --> 00:01:51.079 align:start position:0%
for example who's likely to have an
 

00:01:51.079 --> 00:01:52.990 align:start position:0%
for example who's likely to have an
unplanned<00:01:51.759><c> hospital</c><00:01:52.200><c> admission</c><00:01:52.640><c> in</c><00:01:52.759><c> the</c><00:01:52.920><c> in</c>

00:01:52.990 --> 00:01:53.000 align:start position:0%
unplanned hospital admission in the in
 

00:01:53.000 --> 00:01:55.190 align:start position:0%
unplanned hospital admission in the in
the<00:01:53.159><c> next</c><00:01:53.399><c> six</c><00:01:53.640><c> months</c><00:01:54.320><c> and</c><00:01:54.439><c> if</c><00:01:54.560><c> you</c><00:01:54.719><c> knew</c><00:01:55.000><c> the</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
the next six months and if you knew the
 

00:01:55.200 --> 00:01:56.630 align:start position:0%
the next six months and if you knew the
answer<00:01:55.520><c> to</c><00:01:55.719><c> this</c><00:01:55.960><c> question</c><00:01:56.240><c> you</c><00:01:56.399><c> might</c>

00:01:56.630 --> 00:01:56.640 align:start position:0%
answer to this question you might
 

00:01:56.640 --> 00:01:58.830 align:start position:0%
answer to this question you might
allocate<00:01:57.240><c> resources</c><00:01:58.240><c> to</c><00:01:58.439><c> the</c><00:01:58.560><c> most</c>

00:01:58.830 --> 00:01:58.840 align:start position:0%
allocate resources to the most
 

00:01:58.840 --> 00:02:00.910 align:start position:0%
allocate resources to the most
vulnerable<00:01:59.360><c> en</c><00:01:59.560><c> rolling</c><00:01:59.920><c> in</c><00:02:00.079><c> them</c><00:02:00.200><c> in</c><00:02:00.320><c> a</c><00:02:00.640><c> in</c><00:02:00.719><c> a</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
vulnerable en rolling in them in a in a
 

00:02:00.920 --> 00:02:02.709 align:start position:0%
vulnerable en rolling in them in a in a
complex<00:02:01.439><c> Care</c><00:02:01.640><c> Management</c><00:02:02.159><c> program</c><00:02:02.560><c> for</c>

00:02:02.709 --> 00:02:02.719 align:start position:0%
complex Care Management program for
 

00:02:02.719 --> 00:02:06.590 align:start position:0%
complex Care Management program for
example<00:02:03.159><c> if</c><00:02:03.280><c> you</c><00:02:03.439><c> knew</c><00:02:04.240><c> who's</c><00:02:04.600><c> likely</c><00:02:05.360><c> for</c><00:02:06.360><c> um</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
example if you knew who's likely for um
 

00:02:06.600 --> 00:02:09.389 align:start position:0%
example if you knew who's likely for um
over<00:02:06.880><c> utilizing</c><00:02:07.439><c> the</c><00:02:07.560><c> ER</c><00:02:08.280><c> you</c><00:02:08.399><c> might</c><00:02:08.640><c> have</c><00:02:08.920><c> a</c>

00:02:09.389 --> 00:02:09.399 align:start position:0%
over utilizing the ER you might have a
 

00:02:09.399 --> 00:02:12.190 align:start position:0%
over utilizing the ER you might have a
another<00:02:09.840><c> Care</c><00:02:10.080><c> Management</c><00:02:10.640><c> program</c><00:02:11.039><c> for</c><00:02:11.360><c> that</c>

00:02:12.190 --> 00:02:12.200 align:start position:0%
another Care Management program for that
 

00:02:12.200 --> 00:02:14.190 align:start position:0%
another Care Management program for that
um<00:02:12.760><c> maybe</c><00:02:12.959><c> for</c><00:02:13.200><c> readmissions</c><00:02:13.840><c> you</c><00:02:13.959><c> might</c><00:02:14.120><c> want</c>

00:02:14.190 --> 00:02:14.200 align:start position:0%
um maybe for readmissions you might want
 

00:02:14.200 --> 00:02:15.990 align:start position:0%
um maybe for readmissions you might want
to<00:02:14.400><c> tr</c><00:02:14.879><c> uh</c><00:02:15.000><c> Target</c><00:02:15.360><c> them</c><00:02:15.480><c> for</c><00:02:15.800><c> for</c><00:02:15.879><c> a</c>

00:02:15.990 --> 00:02:16.000 align:start position:0%
to tr uh Target them for for a
 

00:02:16.000 --> 00:02:18.670 align:start position:0%
to tr uh Target them for for a
transitions<00:02:16.480><c> of</c><00:02:16.720><c> CARE</c><00:02:17.120><c> program</c><00:02:18.120><c> and</c><00:02:18.480><c> here's</c>

00:02:18.670 --> 00:02:18.680 align:start position:0%
transitions of CARE program and here's
 

00:02:18.680 --> 00:02:21.630 align:start position:0%
transitions of CARE program and here's
what<00:02:18.840><c> an</c><00:02:19.040><c> example</c><00:02:19.599><c> list</c><00:02:20.120><c> looks</c><00:02:20.480><c> like</c>

00:02:21.630 --> 00:02:21.640 align:start position:0%
what an example list looks like
 

00:02:21.640 --> 00:02:25.309 align:start position:0%
what an example list looks like
so<00:02:22.640><c> um</c><00:02:23.440><c> we</c><00:02:23.599><c> have</c><00:02:23.840><c> our</c><00:02:24.120><c> our</c><00:02:24.360><c> list</c><00:02:24.720><c> here</c><00:02:25.040><c> these</c>

00:02:25.309 --> 00:02:25.319 align:start position:0%
so um we have our our list here these
 

00:02:25.319 --> 00:02:27.830 align:start position:0%
so um we have our our list here these
patients<00:02:25.800><c> are</c><00:02:26.000><c> sorted</c><00:02:26.760><c> from</c><00:02:27.160><c> the</c><00:02:27.400><c> highest</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
patients are sorted from the highest
 

00:02:27.840 --> 00:02:30.990 align:start position:0%
patients are sorted from the highest
risk<00:02:28.640><c> to</c><00:02:28.840><c> the</c><00:02:29.000><c> lowest</c><00:02:29.440><c> risk</c>

00:02:30.990 --> 00:02:31.000 align:start position:0%
risk to the lowest risk
 

00:02:31.000 --> 00:02:34.790 align:start position:0%
risk to the lowest risk
and<00:02:32.000><c> the</c><00:02:32.519><c> most</c><00:02:32.800><c> organizations</c><00:02:33.640><c> will</c><00:02:34.000><c> will</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
and the most organizations will will
 

00:02:34.800 --> 00:02:37.070 align:start position:0%
and the most organizations will will
stratify<00:02:35.440><c> these</c><00:02:35.640><c> lists</c><00:02:36.080><c> based</c><00:02:36.440><c> on</c>

00:02:37.070 --> 00:02:37.080 align:start position:0%
stratify these lists based on
 

00:02:37.080 --> 00:02:39.470 align:start position:0%
stratify these lists based on
traditional<00:02:37.519><c> rules</c><00:02:38.000><c> based</c><00:02:38.400><c> approaches</c><00:02:39.319><c> but</c>

00:02:39.470 --> 00:02:39.480 align:start position:0%
traditional rules based approaches but
 

00:02:39.480 --> 00:02:42.229 align:start position:0%
traditional rules based approaches but
those<00:02:39.680><c> tend</c><00:02:39.879><c> to</c><00:02:40.040><c> fall</c><00:02:40.360><c> short</c><00:02:41.280><c> in</c><00:02:41.480><c> terms</c><00:02:41.920><c> of</c>

00:02:42.229 --> 00:02:42.239 align:start position:0%
those tend to fall short in terms of
 

00:02:42.239 --> 00:02:45.110 align:start position:0%
those tend to fall short in terms of
accuracy<00:02:43.080><c> and</c><00:02:43.319><c> actionability</c><00:02:43.920><c> the</c><00:02:44.920><c> the</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
accuracy and actionability the the
 

00:02:45.120 --> 00:02:47.630 align:start position:0%
accuracy and actionability the the
platform<00:02:45.680><c> will</c><00:02:45.920><c> use</c><00:02:46.599><c> um</c><00:02:46.959><c> machine</c><00:02:47.280><c> learning</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
platform will use um machine learning
 

00:02:47.640 --> 00:02:50.430 align:start position:0%
platform will use um machine learning
models<00:02:48.000><c> to</c><00:02:48.200><c> generate</c><00:02:48.640><c> these</c><00:02:48.920><c> lists</c><00:02:49.920><c> the</c><00:02:50.159><c> data</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
models to generate these lists the data
 

00:02:50.440 --> 00:02:54.550 align:start position:0%
models to generate these lists the data
is<00:02:50.640><c> pulled</c><00:02:51.000><c> in</c><00:02:51.440><c> from</c><00:02:52.280><c> sources</c><00:02:53.000><c> like</c><00:02:53.480><c> claims</c><00:02:54.200><c> or</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
is pulled in from sources like claims or
 

00:02:54.560 --> 00:03:00.509 align:start position:0%
is pulled in from sources like claims or
or<00:02:54.840><c> EHR</c><00:02:55.879><c> sources</c><00:02:57.000><c> and</c><00:02:58.000><c> this</c><00:02:58.760><c> score</c><00:02:59.480><c> right</c><00:02:59.959><c> here</c>

00:03:00.509 --> 00:03:00.519 align:start position:0%
or EHR sources and this score right here
 

00:03:00.519 --> 00:03:04.229 align:start position:0%
or EHR sources and this score right here
is<00:03:01.080><c> coming</c><00:03:02.040><c> from</c><00:03:03.040><c> uh</c><00:03:03.319><c> the</c><00:03:03.480><c> machine</c><00:03:03.799><c> learning</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
is coming from uh the machine learning
 

00:03:04.239 --> 00:03:07.949 align:start position:0%
is coming from uh the machine learning
model<00:03:04.959><c> on</c><00:03:05.080><c> the</c><00:03:05.280><c> right</c><00:03:05.560><c> hand</c><00:03:05.920><c> side</c><00:03:06.400><c> we</c><00:03:06.959><c> have</c>

00:03:07.949 --> 00:03:07.959 align:start position:0%
model on the right hand side we have
 

00:03:07.959 --> 00:03:10.670 align:start position:0%
model on the right hand side we have
some<00:03:08.440><c> of</c><00:03:09.000><c> the</c><00:03:09.239><c> individual</c><00:03:09.879><c> level</c><00:03:10.319><c> risk</c>

00:03:10.670 --> 00:03:10.680 align:start position:0%
some of the individual level risk
 

00:03:10.680 --> 00:03:12.789 align:start position:0%
some of the individual level risk
factors<00:03:11.360><c> that</c><00:03:11.480><c> are</c><00:03:11.760><c> most</c><00:03:12.120><c> important</c><00:03:12.599><c> in</c>

00:03:12.789 --> 00:03:12.799 align:start position:0%
factors that are most important in
 

00:03:12.799 --> 00:03:15.430 align:start position:0%
factors that are most important in
making<00:03:13.239><c> this</c><00:03:14.000><c> prediction</c><00:03:14.720><c> so</c><00:03:14.959><c> for</c><00:03:15.120><c> example</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
making this prediction so for example
 

00:03:15.440 --> 00:03:17.990 align:start position:0%
making this prediction so for example
for<00:03:15.720><c> this</c><00:03:15.879><c> particular</c><00:03:16.360><c> patient</c><00:03:17.040><c> the</c><00:03:17.560><c> the</c><00:03:17.760><c> fact</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
for this particular patient the the fact
 

00:03:18.000 --> 00:03:20.910 align:start position:0%
for this particular patient the the fact
that<00:03:18.200><c> they've</c><00:03:18.599><c> had</c><00:03:19.480><c> eight</c><00:03:20.239><c> unplanned</c>

00:03:20.910 --> 00:03:20.920 align:start position:0%
that they've had eight unplanned
 

00:03:20.920 --> 00:03:23.309 align:start position:0%
that they've had eight unplanned
inpatient<00:03:21.519><c> admissions</c><00:03:22.319><c> in</c><00:03:22.480><c> the</c><00:03:22.720><c> past</c><00:03:22.959><c> 12</c>

00:03:23.309 --> 00:03:23.319 align:start position:0%
inpatient admissions in the past 12
 

00:03:23.319 --> 00:03:26.149 align:start position:0%
inpatient admissions in the past 12
months<00:03:24.319><c> is</c><00:03:24.680><c> contributing</c><00:03:25.360><c> to</c><00:03:25.640><c> this</c><00:03:25.879><c> risk</c>

00:03:26.149 --> 00:03:26.159 align:start position:0%
months is contributing to this risk
 

00:03:26.159 --> 00:03:29.750 align:start position:0%
months is contributing to this risk
score<00:03:26.959><c> now</c><00:03:27.080><c> the</c><00:03:27.200><c> model's</c><00:03:27.680><c> been</c><00:03:27.920><c> trained</c><00:03:28.680><c> with</c>

00:03:29.750 --> 00:03:29.760 align:start position:0%
score now the model's been trained with
 

00:03:29.760 --> 00:03:30.789 align:start position:0%
score now the model's been trained with
over

00:03:30.789 --> 00:03:30.799 align:start position:0%
over
 

00:03:30.799 --> 00:03:34.110 align:start position:0%
over
800<00:03:31.799><c> measures</c><00:03:32.480><c> or</c><00:03:32.720><c> features</c><00:03:33.280><c> like</c><00:03:33.599><c> this</c><00:03:33.959><c> and</c>

00:03:34.110 --> 00:03:34.120 align:start position:0%
800 measures or features like this and
 

00:03:34.120 --> 00:03:37.309 align:start position:0%
800 measures or features like this and
the<00:03:34.760><c> platform</c><00:03:35.760><c> is</c><00:03:36.480><c> generating</c><00:03:37.120><c> these</c>

00:03:37.309 --> 00:03:37.319 align:start position:0%
the platform is generating these
 

00:03:37.319 --> 00:03:40.830 align:start position:0%
the platform is generating these
features<00:03:38.159><c> just</c><00:03:38.519><c> from</c><00:03:39.080><c> the</c><00:03:39.239><c> raw</c><00:03:39.840><c> and</c><00:03:40.000><c> clinical</c>

00:03:40.830 --> 00:03:40.840 align:start position:0%
features just from the raw and clinical
 

00:03:40.840 --> 00:03:45.149 align:start position:0%
features just from the raw and clinical
data<00:03:41.920><c> now</c><00:03:42.920><c> this</c><00:03:43.200><c> list</c><00:03:43.840><c> by</c><00:03:44.080><c> itself</c><00:03:44.519><c> is</c><00:03:44.720><c> is</c><00:03:44.920><c> great</c>

00:03:45.149 --> 00:03:45.159 align:start position:0%
data now this list by itself is is great
 

00:03:45.159 --> 00:03:47.869 align:start position:0%
data now this list by itself is is great
at<00:03:45.280><c> showing</c><00:03:45.599><c> you</c><00:03:45.959><c> individual</c><00:03:46.519><c> level</c><00:03:46.879><c> patterns</c>

00:03:47.869 --> 00:03:47.879 align:start position:0%
at showing you individual level patterns
 

00:03:47.879 --> 00:03:50.149 align:start position:0%
at showing you individual level patterns
but<00:03:48.200><c> what</c><00:03:48.319><c> if</c><00:03:48.439><c> you</c><00:03:48.560><c> wanted</c><00:03:48.840><c> to</c><00:03:49.080><c> spot</c><00:03:49.560><c> patterns</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
but what if you wanted to spot patterns
 

00:03:50.159 --> 00:03:52.710 align:start position:0%
but what if you wanted to spot patterns
across<00:03:50.920><c> your</c><00:03:51.280><c> entire</c><00:03:51.680><c> patient</c><00:03:52.120><c> population</c>

00:03:52.710 --> 00:03:52.720 align:start position:0%
across your entire patient population
 

00:03:52.720 --> 00:03:55.149 align:start position:0%
across your entire patient population
the<00:03:52.840><c> purpose</c><00:03:53.239><c> of</c><00:03:53.760><c> of</c><00:03:53.920><c> this</c><00:03:54.040><c> Nu</c><00:03:54.560><c> capabilities</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
the purpose of of this Nu capabilities
 

00:03:55.159 --> 00:03:56.910 align:start position:0%
the purpose of of this Nu capabilities
to<00:03:55.319><c> help</c><00:03:55.480><c> you</c><00:03:55.680><c> identify</c><00:03:56.280><c> these</c><00:03:56.720><c> these</c>

00:03:56.910 --> 00:03:56.920 align:start position:0%
to help you identify these these
 

00:03:56.920 --> 00:04:00.789 align:start position:0%
to help you identify these these
prevalent<00:03:57.319><c> cohorts</c><00:03:57.959><c> and</c><00:03:58.120><c> so</c><00:03:58.319><c> for</c><00:03:58.680><c> instance</c><00:04:00.000><c> um</c>

00:04:00.789 --> 00:04:00.799 align:start position:0%
prevalent cohorts and so for instance um
 

00:04:00.799 --> 00:04:05.390 align:start position:0%
prevalent cohorts and so for instance um
what<00:04:00.959><c> you</c><00:04:01.200><c> could</c><00:04:01.480><c> do</c><00:04:02.480><c> is</c><00:04:03.000><c> if</c><00:04:03.120><c> you</c><00:04:03.720><c> have</c><00:04:04.720><c> uh</c><00:04:05.280><c> if</c>

00:04:05.390 --> 00:04:05.400 align:start position:0%
what you could do is if you have uh if
 

00:04:05.400 --> 00:04:07.390 align:start position:0%
what you could do is if you have uh if
you<00:04:05.599><c> if</c><00:04:05.680><c> you</c><00:04:05.799><c> see</c><00:04:06.239><c> that</c><00:04:06.400><c> or</c><00:04:06.840><c> discover</c><00:04:07.040><c> a</c><00:04:07.200><c> high</c>

00:04:07.390 --> 00:04:07.400 align:start position:0%
you if you see that or discover a high
 

00:04:07.400 --> 00:04:10.030 align:start position:0%
you if you see that or discover a high
prevence<00:04:07.959><c> of</c><00:04:08.640><c> of</c><00:04:08.879><c> diabetes</c><00:04:09.439><c> severity</c><00:04:09.879><c> or</c>

00:04:10.030 --> 00:04:10.040 align:start position:0%
prevence of of diabetes severity or
 

00:04:10.040 --> 00:04:12.190 align:start position:0%
prevence of of diabetes severity or
chronic<00:04:10.360><c> kidney</c><00:04:10.680><c> disease</c><00:04:11.239><c> maybe</c><00:04:11.560><c> you</c><00:04:11.840><c> might</c>

00:04:12.190 --> 00:04:12.200 align:start position:0%
chronic kidney disease maybe you might
 

00:04:12.200 --> 00:04:16.670 align:start position:0%
chronic kidney disease maybe you might
offer<00:04:12.680><c> in</c><00:04:13.400><c> your</c><00:04:13.640><c> Care</c><00:04:13.920><c> Management</c><00:04:14.879><c> program</c><00:04:15.879><c> um</c>

00:04:16.670 --> 00:04:16.680 align:start position:0%
offer in your Care Management program um
 

00:04:16.680 --> 00:04:20.590 align:start position:0%
offer in your Care Management program um
a<00:04:17.479><c> an</c><00:04:18.160><c> additional</c><00:04:19.160><c> uh</c><00:04:19.400><c> item</c><00:04:19.759><c> or</c><00:04:19.919><c> or</c><00:04:20.120><c> resource</c>

00:04:20.590 --> 00:04:20.600 align:start position:0%
a an additional uh item or or resource
 

00:04:20.600 --> 00:04:22.550 align:start position:0%
a an additional uh item or or resource
to<00:04:20.759><c> do</c><00:04:21.120><c> things</c><00:04:21.440><c> like</c><00:04:21.639><c> blood</c><00:04:22.040><c> glucose</c>

00:04:22.550 --> 00:04:22.560 align:start position:0%
to do things like blood glucose
 

00:04:22.560 --> 00:04:26.030 align:start position:0%
to do things like blood glucose
monitoring<00:04:23.320><c> or</c><00:04:24.280><c> um</c><00:04:24.440><c> really</c><00:04:24.720><c> focus</c><00:04:25.199><c> on</c><00:04:25.479><c> dietary</c>

00:04:26.030 --> 00:04:26.040 align:start position:0%
monitoring or um really focus on dietary
 

00:04:26.040 --> 00:04:28.749 align:start position:0%
monitoring or um really focus on dietary
education<00:04:27.040><c> and</c><00:04:27.160><c> so</c><00:04:27.400><c> I'll</c><00:04:27.639><c> I'll</c><00:04:28.199><c> let's</c><00:04:28.479><c> um</c>

00:04:28.749 --> 00:04:28.759 align:start position:0%
education and so I'll I'll let's um
 

00:04:28.759 --> 00:04:31.469 align:start position:0%
education and so I'll I'll let's um
let's<00:04:29.120><c> ask</c><00:04:29.960><c> uh</c><00:04:30.199><c> a</c><00:04:30.320><c> couple</c><00:04:30.520><c> of</c><00:04:30.680><c> questions</c><00:04:31.080><c> about</c>

00:04:31.469 --> 00:04:31.479 align:start position:0%
let's ask uh a couple of questions about
 

00:04:31.479 --> 00:04:33.430 align:start position:0%
let's ask uh a couple of questions about
about<00:04:31.720><c> this</c><00:04:31.919><c> particular</c><00:04:32.360><c> list</c><00:04:32.759><c> so</c><00:04:33.160><c> starting</c>

00:04:33.430 --> 00:04:33.440 align:start position:0%
about this particular list so starting
 

00:04:33.440 --> 00:04:34.629 align:start position:0%
about this particular list so starting
off<00:04:33.600><c> with</c><00:04:33.759><c> something</c><00:04:34.000><c> really</c><00:04:34.240><c> simple</c><00:04:34.520><c> you</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
off with something really simple you
 

00:04:34.639 --> 00:04:37.430 align:start position:0%
off with something really simple you
might<00:04:34.800><c> want</c><00:04:34.919><c> to</c><00:04:35.199><c> know</c><00:04:36.199><c> um</c><00:04:36.479><c> all</c><00:04:36.639><c> right</c><00:04:36.919><c> well</c>

00:04:37.430 --> 00:04:37.440 align:start position:0%
might want to know um all right well
 

00:04:37.440 --> 00:04:42.270 align:start position:0%
might want to know um all right well
which<00:04:37.960><c> patients</c><00:04:38.880><c> are</c><00:04:39.440><c> in</c><00:04:39.800><c> the</c><00:04:40.280><c> top</c>

00:04:42.270 --> 00:04:42.280 align:start position:0%
which patients are in the top
 

00:04:42.280 --> 00:04:49.749 align:start position:0%
which patients are in the top
1%<00:04:43.280><c> of</c><00:04:43.800><c> risk</c><00:04:44.800><c> um</c><00:04:45.440><c> and</c><00:04:45.919><c> have</c><00:04:46.919><c> one</c><00:04:47.479><c> or</c><00:04:48.479><c> more</c><00:04:49.120><c> ER</c>

00:04:49.749 --> 00:04:49.759 align:start position:0%
1% of risk um and have one or more ER
 

00:04:49.759 --> 00:04:55.350 align:start position:0%
1% of risk um and have one or more ER
visits<00:04:50.520><c> in</c><00:04:50.720><c> the</c><00:04:51.000><c> past</c><00:04:51.680><c> six</c>

00:04:55.350 --> 00:04:55.360 align:start position:0%
 
 

00:04:55.360 --> 00:05:00.070 align:start position:0%
 
months<00:04:56.360><c> okay</c><00:04:57.360><c> so</c><00:04:58.360><c> there's</c><00:04:58.639><c> 180</c><00:04:59.240><c> C</c><00:04:59.720><c> such</c>

00:05:00.070 --> 00:05:00.080 align:start position:0%
months okay so there's 180 C such
 

00:05:00.080 --> 00:05:01.670 align:start position:0%
months okay so there's 180 C such
patients<00:05:00.720><c> we</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
patients we
 

00:05:01.680 --> 00:05:04.469 align:start position:0%
patients we
have<00:05:02.680><c> um</c><00:05:03.280><c> some</c><00:05:03.520><c> information</c><00:05:04.000><c> about</c><00:05:04.280><c> those</c>

00:05:04.469 --> 00:05:04.479 align:start position:0%
have um some information about those
 

00:05:04.479 --> 00:05:06.590 align:start position:0%
have um some information about those
patients<00:05:05.120><c> that</c><00:05:05.240><c> we</c><00:05:05.360><c> can</c>

00:05:06.590 --> 00:05:06.600 align:start position:0%
patients that we can
 

00:05:06.600 --> 00:05:10.469 align:start position:0%
patients that we can
use<00:05:07.600><c> look</c><00:05:07.840><c> them</c><00:05:08.080><c> up</c><00:05:08.880><c> um</c><00:05:09.039><c> so</c><00:05:09.240><c> here's</c><00:05:09.479><c> one</c><00:05:09.639><c> of</c><00:05:09.759><c> the</c>

00:05:10.469 --> 00:05:10.479 align:start position:0%
use look them up um so here's one of the
 

00:05:10.479 --> 00:05:14.070 align:start position:0%
use look them up um so here's one of the
patients<00:05:11.479><c> in</c><00:05:11.639><c> the</c><00:05:11.800><c> top</c><00:05:12.000><c> 1%</c><00:05:12.520><c> of</c><00:05:12.759><c> risk</c><00:05:13.720><c> this</c>

00:05:14.070 --> 00:05:14.080 align:start position:0%
patients in the top 1% of risk this
 

00:05:14.080 --> 00:05:17.390 align:start position:0%
patients in the top 1% of risk this
patient<00:05:15.080><c> uh</c><00:05:15.240><c> has</c><00:05:15.520><c> also</c><00:05:15.919><c> had</c><00:05:16.280><c> one</c><00:05:16.479><c> or</c><00:05:16.639><c> more</c><00:05:16.880><c> ER</c>

00:05:17.390 --> 00:05:17.400 align:start position:0%
patient uh has also had one or more ER
 

00:05:17.400 --> 00:05:22.710 align:start position:0%
patient uh has also had one or more ER
visits<00:05:18.400><c> in</c><00:05:19.039><c> the</c><00:05:19.240><c> past</c><00:05:19.479><c> six</c><00:05:20.120><c> months</c><00:05:21.120><c> that</c><00:05:21.919><c> data</c>

00:05:22.710 --> 00:05:22.720 align:start position:0%
visits in the past six months that data
 

00:05:22.720 --> 00:05:25.870 align:start position:0%
visits in the past six months that data
is<00:05:23.639><c> stored</c><00:05:24.639><c> in</c><00:05:24.800><c> the</c><00:05:25.039><c> platform</c><00:05:25.479><c> it's</c><00:05:25.639><c> not</c>

00:05:25.870 --> 00:05:25.880 align:start position:0%
is stored in the platform it's not
 

00:05:25.880 --> 00:05:27.909 align:start position:0%
is stored in the platform it's not
necessarily<00:05:26.440><c> visible</c><00:05:26.840><c> here</c><00:05:27.120><c> in</c><00:05:27.240><c> a</c><00:05:27.479><c> second</c>

00:05:27.909 --> 00:05:27.919 align:start position:0%
necessarily visible here in a second
 

00:05:27.919 --> 00:05:31.150 align:start position:0%
necessarily visible here in a second
we'll<00:05:28.319><c> we'll</c><00:05:28.479><c> see</c><00:05:28.800><c> behind</c><00:05:29.039><c> the</c><00:05:29.160><c> scenes</c><00:05:29.840><c> of</c><00:05:30.600><c> of</c>

00:05:31.150 --> 00:05:31.160 align:start position:0%
we'll we'll see behind the scenes of of
 

00:05:31.160 --> 00:05:35.270 align:start position:0%
we'll we'll see behind the scenes of of
um<00:05:31.440><c> how</c><00:05:31.600><c> the</c><00:05:31.759><c> generative</c><00:05:32.199><c> AI</c><00:05:32.520><c> model</c><00:05:32.960><c> is</c><00:05:33.759><c> is</c><00:05:34.440><c> uh</c>

00:05:35.270 --> 00:05:35.280 align:start position:0%
um how the generative AI model is is uh
 

00:05:35.280 --> 00:05:38.510 align:start position:0%
um how the generative AI model is is uh
surfacing<00:05:36.039><c> this</c><00:05:36.319><c> individual</c><00:05:37.039><c> as</c><00:05:37.400><c> as</c><00:05:38.080><c> um</c>

00:05:38.510 --> 00:05:38.520 align:start position:0%
surfacing this individual as as um
 

00:05:38.520 --> 00:05:41.390 align:start position:0%
surfacing this individual as as um
having<00:05:38.840><c> one</c><00:05:39.000><c> or</c><00:05:39.160><c> more</c><00:05:39.319><c> ER</c><00:05:39.880><c> visits</c><00:05:40.880><c> but</c><00:05:41.080><c> we</c><00:05:41.199><c> can</c>

00:05:41.390 --> 00:05:41.400 align:start position:0%
having one or more ER visits but we can
 

00:05:41.400 --> 00:05:43.150 align:start position:0%
having one or more ER visits but we can
we<00:05:41.520><c> can</c><00:05:41.680><c> ask</c><00:05:41.880><c> a</c><00:05:42.000><c> couple</c><00:05:42.240><c> more</c><00:05:42.479><c> questions</c><00:05:42.880><c> here</c>

00:05:43.150 --> 00:05:43.160 align:start position:0%
we can ask a couple more questions here
 

00:05:43.160 --> 00:05:46.430 align:start position:0%
we can ask a couple more questions here
so<00:05:44.000><c> um</c><00:05:44.120><c> well</c><00:05:44.280><c> this</c><00:05:44.360><c> is</c><00:05:44.560><c> 180</c><00:05:45.400><c> patients</c><00:05:45.960><c> maybe</c>

00:05:46.430 --> 00:05:46.440 align:start position:0%
so um well this is 180 patients maybe
 

00:05:46.440 --> 00:05:49.830 align:start position:0%
so um well this is 180 patients maybe
maybe<00:05:46.759><c> we</c><00:05:47.000><c> we</c><00:05:47.120><c> also</c><00:05:47.400><c> want</c><00:05:47.639><c> the</c><00:05:47.800><c> next</c><00:05:48.600><c> five</c><00:05:49.600><c> uh</c>

00:05:49.830 --> 00:05:49.840 align:start position:0%
maybe we we also want the next five uh
 

00:05:49.840 --> 00:05:58.469 align:start position:0%
maybe we we also want the next five uh
so<00:05:50.840><c> um</c><00:05:51.840><c> show</c><00:05:52.160><c> me</c><00:05:52.720><c> the</c><00:05:53.000><c> the</c><00:05:53.120><c> next</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
 
 

00:05:58.479 --> 00:06:07.510 align:start position:0%
 
SL

00:06:07.510 --> 00:06:07.520 align:start position:0%
 
 

00:06:07.520 --> 00:06:10.870 align:start position:0%
 
okay<00:06:08.520><c> um</c><00:06:09.160><c> what</c><00:06:09.280><c> are</c><00:06:09.440><c> what</c><00:06:09.560><c> are</c><00:06:09.759><c> examples</c><00:06:10.240><c> of</c><00:06:10.560><c> of</c>

00:06:10.870 --> 00:06:10.880 align:start position:0%
okay um what are what are examples of of
 

00:06:10.880 --> 00:06:12.270 align:start position:0%
okay um what are what are examples of of
perhaps<00:06:11.240><c> more</c><00:06:11.440><c> interesting</c><00:06:11.840><c> questions</c><00:06:12.199><c> we</c>

00:06:12.270 --> 00:06:12.280 align:start position:0%
perhaps more interesting questions we
 

00:06:12.280 --> 00:06:13.270 align:start position:0%
perhaps more interesting questions we
can

00:06:13.270 --> 00:06:13.280 align:start position:0%
can
 

00:06:13.280 --> 00:06:17.670 align:start position:0%
can
ask<00:06:14.280><c> which</c><00:06:14.639><c> patients</c><00:06:15.479><c> are</c><00:06:16.120><c> in</c><00:06:16.360><c> in</c><00:06:16.440><c> the</c><00:06:16.639><c> top</c>

00:06:17.670 --> 00:06:17.680 align:start position:0%
ask which patients are in in the top
 

00:06:17.680 --> 00:06:28.390 align:start position:0%
ask which patients are in in the top
1%<00:06:18.680><c> of</c><00:06:18.919><c> risk</c><00:06:19.360><c> and</c><00:06:19.680><c> have</c><00:06:20.759><c> chronic</c><00:06:21.759><c> kidney</c>

00:06:28.390 --> 00:06:28.400 align:start position:0%
 
 

00:06:28.400 --> 00:06:38.189 align:start position:0%
 
disease

00:06:38.189 --> 00:06:38.199 align:start position:0%
 
 

00:06:38.199 --> 00:06:41.430 align:start position:0%
 
uh<00:06:38.319><c> and</c><00:06:38.479><c> then</c><00:06:38.680><c> and</c><00:06:38.880><c> let's</c><00:06:39.039><c> do</c><00:06:39.240><c> one</c><00:06:39.919><c> more</c>

00:06:41.430 --> 00:06:41.440 align:start position:0%
uh and then and let's do one more
 

00:06:41.440 --> 00:06:43.430 align:start position:0%
uh and then and let's do one more
um<00:06:42.440><c> What</c>

00:06:43.430 --> 00:06:43.440 align:start position:0%
um What
 

00:06:43.440 --> 00:06:58.070 align:start position:0%
um What
patients<00:06:44.440><c> with</c><00:06:44.960><c> a</c><00:06:45.199><c> a</c>

00:06:58.070 --> 00:06:58.080 align:start position:0%
 
 

00:06:58.080 --> 00:07:02.230 align:start position:0%
 
behavioral<00:06:59.080><c> how</c><00:06:59.759><c> disorder</c><00:07:00.759><c> have</c><00:07:01.319><c> had</c><00:07:01.680><c> one</c><00:07:01.960><c> or</c>

00:07:02.230 --> 00:07:02.240 align:start position:0%
behavioral how disorder have had one or
 

00:07:02.240 --> 00:07:13.710 align:start position:0%
behavioral how disorder have had one or
more<00:07:02.680><c> inpatient</c><00:07:03.879><c> admissions</c><00:07:04.879><c> in</c><00:07:05.080><c> the</c><00:07:05.319><c> past</c>

00:07:13.710 --> 00:07:13.720 align:start position:0%
 
 

00:07:13.720 --> 00:07:17.550 align:start position:0%
 
year<00:07:14.960><c> right</c><00:07:15.960><c> so</c><00:07:16.440><c> now</c><00:07:16.599><c> I'm</c><00:07:16.680><c> going</c><00:07:16.800><c> to</c><00:07:17.080><c> show</c><00:07:17.319><c> you</c>

00:07:17.550 --> 00:07:17.560 align:start position:0%
year right so now I'm going to show you
 

00:07:17.560 --> 00:07:19.550 align:start position:0%
year right so now I'm going to show you
behind<00:07:17.879><c> the</c><00:07:18.039><c> scenes</c><00:07:18.639><c> of</c><00:07:18.919><c> what's</c><00:07:19.120><c> Happening</c>

00:07:19.550 --> 00:07:19.560 align:start position:0%
behind the scenes of what's Happening
 

00:07:19.560 --> 00:07:22.070 align:start position:0%
behind the scenes of what's Happening
Here<00:07:20.160><c> uh</c><00:07:20.319><c> just</c><00:07:20.440><c> a</c><00:07:20.639><c> little</c><00:07:20.879><c> bit</c><00:07:21.160><c> behind</c><00:07:21.440><c> the</c>

00:07:22.070 --> 00:07:22.080 align:start position:0%
Here uh just a little bit behind the
 

00:07:22.080 --> 00:07:25.070 align:start position:0%
Here uh just a little bit behind the
scenes<00:07:23.080><c> um</c><00:07:23.879><c> there's</c><00:07:24.160><c> there's</c><00:07:24.440><c> one</c><00:07:24.759><c> key</c>

00:07:25.070 --> 00:07:25.080 align:start position:0%
scenes um there's there's one key
 

00:07:25.080 --> 00:07:27.830 align:start position:0%
scenes um there's there's one key
mechanism<00:07:25.840><c> to</c><00:07:26.199><c> to</c><00:07:26.400><c> take</c><00:07:26.680><c> note</c><00:07:26.960><c> of</c><00:07:27.240><c> and</c><00:07:27.400><c> I</c><00:07:27.599><c> have</c>

00:07:27.830 --> 00:07:27.840 align:start position:0%
mechanism to to take note of and I have
 

00:07:27.840 --> 00:07:31.550 align:start position:0%
mechanism to to take note of and I have
here<00:07:28.240><c> a</c><00:07:28.800><c> a</c><00:07:28.960><c> separate</c><00:07:29.520><c> screen</c><00:07:29.919><c> that</c><00:07:30.160><c> that</c><00:07:30.560><c> shows</c>

00:07:31.550 --> 00:07:31.560 align:start position:0%
here a a separate screen that that shows
 

00:07:31.560 --> 00:07:35.670 align:start position:0%
here a a separate screen that that shows
uh<00:07:32.000><c> a</c><00:07:32.120><c> little</c><00:07:32.360><c> bit</c><00:07:32.520><c> of</c><00:07:32.720><c> how</c><00:07:32.960><c> this</c><00:07:33.440><c> the</c><00:07:34.400><c> the</c><00:07:34.680><c> um</c>

00:07:35.670 --> 00:07:35.680 align:start position:0%
uh a little bit of how this the the um
 

00:07:35.680 --> 00:07:37.510 align:start position:0%
uh a little bit of how this the the um
the<00:07:35.840><c> large</c><00:07:36.199><c> language</c><00:07:36.560><c> model</c><00:07:36.960><c> is</c><00:07:37.199><c> is</c>

00:07:37.510 --> 00:07:37.520 align:start position:0%
the large language model is is
 

00:07:37.520 --> 00:07:39.869 align:start position:0%
the large language model is is
generating<00:07:38.039><c> these</c><00:07:38.319><c> results</c><00:07:39.319><c> when</c><00:07:39.599><c> when</c><00:07:39.720><c> a</c>

00:07:39.869 --> 00:07:39.879 align:start position:0%
generating these results when when a
 

00:07:39.879 --> 00:07:42.430 align:start position:0%
generating these results when when a
user<00:07:40.400><c> asks</c><00:07:40.879><c> the</c><00:07:41.039><c> chat</c><00:07:41.400><c> interface</c><00:07:41.840><c> a</c><00:07:42.080><c> question</c>

00:07:42.430 --> 00:07:42.440 align:start position:0%
user asks the chat interface a question
 

00:07:42.440 --> 00:07:44.749 align:start position:0%
user asks the chat interface a question
so<00:07:42.599><c> we</c><00:07:42.720><c> just</c><00:07:42.919><c> asked</c><00:07:43.240><c> it</c><00:07:43.840><c> which</c><00:07:44.199><c> which</c><00:07:44.440><c> patients</c>

00:07:44.749 --> 00:07:44.759 align:start position:0%
so we just asked it which which patients
 

00:07:44.759 --> 00:07:46.830 align:start position:0%
so we just asked it which which patients
are<00:07:44.919><c> in</c><00:07:45.039><c> the</c><00:07:45.159><c> top</c><00:07:45.440><c> 1%</c><00:07:46.080><c> and</c><00:07:46.280><c> have</c><00:07:46.440><c> had</c><00:07:46.599><c> one</c><00:07:46.720><c> or</c>

00:07:46.830 --> 00:07:46.840 align:start position:0%
are in the top 1% and have had one or
 

00:07:46.840 --> 00:07:49.469 align:start position:0%
are in the top 1% and have had one or
more<00:07:47.039><c> ER</c><00:07:47.759><c> visits</c><00:07:48.759><c> there</c><00:07:48.919><c> there's</c><00:07:49.039><c> a</c><00:07:49.199><c> large</c>

00:07:49.469 --> 00:07:49.479 align:start position:0%
more ER visits there there's a large
 

00:07:49.479 --> 00:07:51.790 align:start position:0%
more ER visits there there's a large
language<00:07:49.879><c> model</c><00:07:50.199><c> that's</c><00:07:50.440><c> trained</c><00:07:51.000><c> by</c><00:07:51.440><c> Jon</c>

00:07:51.790 --> 00:07:51.800 align:start position:0%
language model that's trained by Jon
 

00:07:51.800 --> 00:07:54.629 align:start position:0%
language model that's trained by Jon
Snow<00:07:52.400><c> Labs</c><00:07:53.400><c> um</c><00:07:53.759><c> that</c><00:07:53.919><c> has</c><00:07:54.039><c> been</c><00:07:54.199><c> trained</c><00:07:54.520><c> by</c>

00:07:54.629 --> 00:07:54.639 align:start position:0%
Snow Labs um that has been trained by
 

00:07:54.639 --> 00:07:56.749 align:start position:0%
Snow Labs um that has been trained by
John<00:07:54.879><c> Snow</c><00:07:55.159><c> labs</c><00:07:55.400><c> and</c><00:07:55.520><c> it's</c><00:07:55.759><c> converting</c><00:07:56.360><c> that</c>

00:07:56.749 --> 00:07:56.759 align:start position:0%
John Snow labs and it's converting that
 

00:07:56.759 --> 00:08:00.670 align:start position:0%
John Snow labs and it's converting that
question<00:07:57.759><c> which</c><00:07:57.919><c> is</c><00:07:58.080><c> a</c><00:07:58.319><c> natural</c><00:07:58.840><c> text</c><00:07:59.680><c> here</c>

00:08:00.670 --> 00:08:00.680 align:start position:0%
question which is a natural text here
 

00:08:00.680 --> 00:08:03.070 align:start position:0%
question which is a natural text here
into<00:08:01.599><c> a</c><00:08:01.800><c> a</c><00:08:01.919><c> SQL</c>

00:08:03.070 --> 00:08:03.080 align:start position:0%
into a a SQL
 

00:08:03.080 --> 00:08:07.350 align:start position:0%
into a a SQL
query<00:08:04.080><c> um</c><00:08:04.520><c> so</c><00:08:04.879><c> this</c><00:08:05.319><c> is</c><00:08:05.479><c> an</c><00:08:05.759><c> example</c><00:08:06.759><c> of</c><00:08:07.080><c> how</c>

00:08:07.350 --> 00:08:07.360 align:start position:0%
query um so this is an example of how
 

00:08:07.360 --> 00:08:09.670 align:start position:0%
query um so this is an example of how
the<00:08:07.440><c> models</c><00:08:07.919><c> decided</c><00:08:08.280><c> to</c><00:08:08.520><c> encode</c>

00:08:09.670 --> 00:08:09.680 align:start position:0%
the models decided to encode
 

00:08:09.680 --> 00:08:12.950 align:start position:0%
the models decided to encode
that<00:08:10.680><c> as</c><00:08:11.000><c> as</c><00:08:11.080><c> a</c><00:08:11.199><c> SQL</c><00:08:11.560><c> query</c><00:08:11.960><c> now</c><00:08:12.120><c> the</c><00:08:12.280><c> SQL</c><00:08:12.639><c> query</c>

00:08:12.950 --> 00:08:12.960 align:start position:0%
that as as a SQL query now the SQL query
 

00:08:12.960 --> 00:08:15.869 align:start position:0%
that as as a SQL query now the SQL query
is<00:08:13.120><c> run</c><00:08:13.680><c> against</c><00:08:14.680><c> the</c>

00:08:15.869 --> 00:08:15.879 align:start position:0%
is run against the
 

00:08:15.879 --> 00:08:18.990 align:start position:0%
is run against the
platform<00:08:16.879><c> uh</c><00:08:17.120><c> and</c><00:08:17.720><c> the</c><00:08:17.919><c> result</c><00:08:18.479><c> is</c><00:08:18.680><c> now</c><00:08:18.879><c> going</c>

00:08:18.990 --> 00:08:19.000 align:start position:0%
platform uh and the result is now going
 

00:08:19.000 --> 00:08:22.990 align:start position:0%
platform uh and the result is now going
to<00:08:19.120><c> be</c><00:08:19.240><c> a</c><00:08:19.360><c> list</c><00:08:19.599><c> of</c><00:08:20.159><c> patients</c><00:08:21.159><c> with</c><00:08:22.159><c> the</c>

00:08:22.990 --> 00:08:23.000 align:start position:0%
to be a list of patients with the
 

00:08:23.000 --> 00:08:25.869 align:start position:0%
to be a list of patients with the
characteristics<00:08:24.000><c> having</c><00:08:24.280><c> a</c><00:08:24.720><c> percentile</c><00:08:25.720><c> in</c>

00:08:25.869 --> 00:08:25.879 align:start position:0%
characteristics having a percentile in
 

00:08:25.879 --> 00:08:28.749 align:start position:0%
characteristics having a percentile in
the<00:08:26.000><c> 99th</c><00:08:26.680><c> per</c><00:08:27.120><c> or</c><00:08:27.440><c> higher</c><00:08:27.960><c> and</c><00:08:28.080><c> then</c><00:08:28.360><c> also</c>

00:08:28.749 --> 00:08:28.759 align:start position:0%
the 99th per or higher and then also
 

00:08:28.759 --> 00:08:31.589 align:start position:0%
the 99th per or higher and then also
having<00:08:29.280><c> one</c><00:08:29.440><c> or</c><00:08:29.599><c> more</c><00:08:29.800><c> ER</c><00:08:30.240><c> visits</c><00:08:31.000><c> in</c><00:08:31.159><c> the</c><00:08:31.360><c> past</c>

00:08:31.589 --> 00:08:31.599 align:start position:0%
having one or more ER visits in the past
 

00:08:31.599 --> 00:08:35.709 align:start position:0%
having one or more ER visits in the past
six<00:08:32.279><c> months</c><00:08:33.479><c> um</c><00:08:34.479><c> and</c><00:08:34.800><c> because</c><00:08:35.080><c> of</c><00:08:35.279><c> this</c><00:08:35.440><c> large</c>

00:08:35.709 --> 00:08:35.719 align:start position:0%
six months um and because of this large
 

00:08:35.719 --> 00:08:38.550 align:start position:0%
six months um and because of this large
language<00:08:36.120><c> model</c><00:08:36.519><c> now</c><00:08:37.519><c> clinical</c><00:08:38.000><c> experts</c><00:08:38.320><c> and</c>

00:08:38.550 --> 00:08:38.560 align:start position:0%
language model now clinical experts and
 

00:08:38.560 --> 00:08:40.630 align:start position:0%
language model now clinical experts and
population<00:08:39.039><c> Health</c><00:08:39.399><c> experts</c><00:08:39.880><c> no</c><00:08:40.080><c> longer</c><00:08:40.399><c> need</c>

00:08:40.630 --> 00:08:40.640 align:start position:0%
population Health experts no longer need
 

00:08:40.640 --> 00:08:43.670 align:start position:0%
population Health experts no longer need
to<00:08:40.880><c> write</c><00:08:41.800><c> code</c><00:08:42.120><c> or</c><00:08:42.360><c> queries</c><00:08:43.000><c> to</c><00:08:43.159><c> be</c><00:08:43.279><c> able</c><00:08:43.479><c> to</c>

00:08:43.670 --> 00:08:43.680 align:start position:0%
to write code or queries to be able to
 

00:08:43.680 --> 00:08:45.750 align:start position:0%
to write code or queries to be able to
surface<00:08:44.200><c> these</c><00:08:44.480><c> insights</c>

00:08:45.750 --> 00:08:45.760 align:start position:0%
surface these insights
 

00:08:45.760 --> 00:08:48.590 align:start position:0%
surface these insights
themselves<00:08:46.760><c> uh</c><00:08:47.279><c> now</c><00:08:47.880><c> there's</c><00:08:48.040><c> a</c><00:08:48.200><c> little</c><00:08:48.360><c> bit</c>

00:08:48.590 --> 00:08:48.600 align:start position:0%
themselves uh now there's a little bit
 

00:08:48.600 --> 00:08:50.430 align:start position:0%
themselves uh now there's a little bit
more<00:08:49.000><c> that's</c><00:08:49.279><c> that's</c><00:08:49.519><c> happening</c><00:08:49.959><c> here</c><00:08:50.279><c> and</c>

00:08:50.430 --> 00:08:50.440 align:start position:0%
more that's that's happening here and
 

00:08:50.440 --> 00:08:52.350 align:start position:0%
more that's that's happening here and
I'll<00:08:50.600><c> just</c><00:08:50.760><c> touch</c><00:08:51.000><c> on</c><00:08:51.200><c> that</c><00:08:51.399><c> briefly</c><00:08:52.160><c> this</c>

00:08:52.350 --> 00:08:52.360 align:start position:0%
I'll just touch on that briefly this
 

00:08:52.360 --> 00:08:54.870 align:start position:0%
I'll just touch on that briefly this
this<00:08:52.480><c> is</c><00:08:52.800><c> the</c><00:08:53.160><c> the</c><00:08:53.440><c> question</c><00:08:53.959><c> that</c><00:08:54.160><c> one</c><00:08:54.399><c> might</c>

00:08:54.870 --> 00:08:54.880 align:start position:0%
this is the the question that one might
 

00:08:54.880 --> 00:08:59.509 align:start position:0%
this is the the question that one might
ask<00:08:55.880><c> and</c><00:08:56.200><c> this</c><00:08:56.519><c> leftand</c><00:08:57.080><c> cell</c><00:08:57.680><c> shows</c><00:08:58.680><c> what</c><00:08:58.839><c> the</c>

00:08:59.509 --> 00:08:59.519 align:start position:0%
ask and this leftand cell shows what the
 

00:08:59.519 --> 00:09:02.750 align:start position:0%
ask and this leftand cell shows what the
model<00:09:00.519><c> um</c><00:09:00.959><c> is</c><00:09:01.320><c> prompted</c><00:09:02.000><c> with</c><00:09:02.320><c> or</c><00:09:02.519><c> what</c><00:09:02.640><c> the</c>

00:09:02.750 --> 00:09:02.760 align:start position:0%
model um is prompted with or what the
 

00:09:02.760 --> 00:09:05.190 align:start position:0%
model um is prompted with or what the
large<00:09:03.079><c> language</c><00:09:03.519><c> model</c><00:09:03.959><c> is</c><00:09:04.279><c> actually</c><00:09:04.680><c> working</c>

00:09:05.190 --> 00:09:05.200 align:start position:0%
large language model is actually working
 

00:09:05.200 --> 00:09:08.389 align:start position:0%
large language model is actually working
with<00:09:05.920><c> so</c><00:09:06.480><c> you</c><00:09:06.600><c> can</c><00:09:06.760><c> see</c><00:09:07.240><c> the</c><00:09:07.440><c> question</c><00:09:07.760><c> is</c><00:09:08.079><c> down</c>

00:09:08.389 --> 00:09:08.399 align:start position:0%
with so you can see the question is down
 

00:09:08.399 --> 00:09:10.430 align:start position:0%
with so you can see the question is down
here<00:09:08.640><c> at</c><00:09:08.760><c> the</c><00:09:08.959><c> bottom</c><00:09:09.440><c> but</c><00:09:09.560><c> in</c><00:09:09.760><c> addition</c><00:09:10.200><c> to</c>

00:09:10.430 --> 00:09:10.440 align:start position:0%
here at the bottom but in addition to
 

00:09:10.440 --> 00:09:11.990 align:start position:0%
here at the bottom but in addition to
that<00:09:10.680><c> question</c><00:09:11.040><c> there's</c><00:09:11.200><c> a</c><00:09:11.360><c> little</c><00:09:11.519><c> bit</c><00:09:11.720><c> more</c>

00:09:11.990 --> 00:09:12.000 align:start position:0%
that question there's a little bit more
 

00:09:12.000 --> 00:09:14.230 align:start position:0%
that question there's a little bit more
work<00:09:12.320><c> that</c><00:09:12.440><c> needs</c><00:09:12.680><c> to</c><00:09:12.800><c> be</c><00:09:13.000><c> done</c><00:09:13.680><c> in</c><00:09:13.839><c> feeding</c>

00:09:14.230 --> 00:09:14.240 align:start position:0%
work that needs to be done in feeding
 

00:09:14.240 --> 00:09:16.790 align:start position:0%
work that needs to be done in feeding
the<00:09:14.360><c> model</c><00:09:14.760><c> relevant</c><00:09:15.480><c> information</c><00:09:16.480><c> that</c><00:09:16.640><c> it</c>

00:09:16.790 --> 00:09:16.800 align:start position:0%
the model relevant information that it
 

00:09:16.800 --> 00:09:20.990 align:start position:0%
the model relevant information that it
can<00:09:17.040><c> use</c><00:09:17.360><c> to</c><00:09:17.600><c> generate</c><00:09:18.079><c> this</c><00:09:18.320><c> query</c><00:09:19.200><c> and</c><00:09:19.800><c> so</c><00:09:20.800><c> in</c>

00:09:20.990 --> 00:09:21.000 align:start position:0%
can use to generate this query and so in
 

00:09:21.000 --> 00:09:22.470 align:start position:0%
can use to generate this query and so in
addition<00:09:21.320><c> to</c><00:09:21.440><c> feeding</c><00:09:21.800><c> in</c><00:09:21.959><c> the</c><00:09:22.160><c> question</c>

00:09:22.470 --> 00:09:22.480 align:start position:0%
addition to feeding in the question
 

00:09:22.480 --> 00:09:26.590 align:start position:0%
addition to feeding in the question
we're<00:09:22.760><c> also</c><00:09:23.399><c> passing</c><00:09:24.120><c> in</c><00:09:25.120><c> uh</c><00:09:25.279><c> a</c><00:09:25.519><c> list</c><00:09:26.079><c> of</c>

00:09:26.590 --> 00:09:26.600 align:start position:0%
we're also passing in uh a list of
 

00:09:26.600 --> 00:09:29.829 align:start position:0%
we're also passing in uh a list of
relevant<00:09:27.600><c> table</c><00:09:28.120><c> columns</c><00:09:28.839><c> so</c>

00:09:29.829 --> 00:09:29.839 align:start position:0%
relevant table columns so
 

00:09:29.839 --> 00:09:31.430 align:start position:0%
relevant table columns so
we<00:09:30.160><c> we</c><00:09:30.279><c> know</c><00:09:30.560><c> that</c><00:09:30.680><c> the</c><00:09:30.800><c> machine</c><00:09:31.120><c> learning</c>

00:09:31.430 --> 00:09:31.440 align:start position:0%
we we know that the machine learning
 

00:09:31.440 --> 00:09:34.310 align:start position:0%
we we know that the machine learning
model<00:09:31.800><c> has</c><00:09:31.920><c> been</c><00:09:32.120><c> trained</c><00:09:32.640><c> on</c><00:09:32.959><c> 800</c><00:09:33.800><c> or</c><00:09:34.000><c> so</c>

00:09:34.310 --> 00:09:34.320 align:start position:0%
model has been trained on 800 or so
 

00:09:34.320 --> 00:09:36.710 align:start position:0%
model has been trained on 800 or so
features<00:09:35.240><c> and</c><00:09:35.440><c> these</c><00:09:35.560><c> are</c><00:09:36.000><c> some</c><00:09:36.200><c> of</c><00:09:36.360><c> the</c>

00:09:36.710 --> 00:09:36.720 align:start position:0%
features and these are some of the
 

00:09:36.720 --> 00:09:39.829 align:start position:0%
features and these are some of the
columns<00:09:37.720><c> that</c><00:09:37.839><c> are</c><00:09:38.120><c> most</c><00:09:38.480><c> relevant</c><00:09:39.200><c> into</c><00:09:39.519><c> for</c>

00:09:39.829 --> 00:09:39.839 align:start position:0%
columns that are most relevant into for
 

00:09:39.839 --> 00:09:44.230 align:start position:0%
columns that are most relevant into for
answering<00:09:40.399><c> this</c><00:09:40.880><c> question</c><00:09:41.880><c> uh</c><00:09:42.079><c> so</c><00:09:42.880><c> you</c><00:09:43.880><c> um</c>

00:09:44.230 --> 00:09:44.240 align:start position:0%
answering this question uh so you um
 

00:09:44.240 --> 00:09:45.790 align:start position:0%
answering this question uh so you um
certainly<00:09:44.640><c> if</c><00:09:44.720><c> you're</c><00:09:44.839><c> asking</c><00:09:45.240><c> about</c><00:09:45.399><c> ER</c>

00:09:45.790 --> 00:09:45.800 align:start position:0%
certainly if you're asking about ER
 

00:09:45.800 --> 00:09:47.790 align:start position:0%
certainly if you're asking about ER
visits<00:09:46.560><c> there's</c><00:09:46.720><c> going</c><00:09:46.839><c> to</c><00:09:46.959><c> be</c><00:09:47.160><c> some</c><00:09:47.399><c> table</c>

00:09:47.790 --> 00:09:47.800 align:start position:0%
visits there's going to be some table
 

00:09:47.800 --> 00:09:51.150 align:start position:0%
visits there's going to be some table
columns<00:09:48.200><c> or</c><00:09:48.360><c> some</c><00:09:49.000><c> measures</c><00:09:50.000><c> that</c><00:09:50.800><c> you</c><00:09:50.959><c> will</c>

00:09:51.150 --> 00:09:51.160 align:start position:0%
columns or some measures that you will
 

00:09:51.160 --> 00:09:53.910 align:start position:0%
columns or some measures that you will
need<00:09:51.320><c> to</c><00:09:51.560><c> know</c><00:09:52.440><c> that</c><00:09:52.640><c> relate</c><00:09:53.160><c> to</c><00:09:53.279><c> ER</c><00:09:53.600><c> visits</c>

00:09:53.910 --> 00:09:53.920 align:start position:0%
need to know that relate to ER visits
 

00:09:53.920 --> 00:09:55.430 align:start position:0%
need to know that relate to ER visits
and<00:09:54.079><c> you'll</c><00:09:54.240><c> see</c><00:09:54.480><c> that</c><00:09:54.600><c> there's</c><00:09:54.880><c> plenty</c><00:09:55.200><c> of</c>

00:09:55.430 --> 00:09:55.440 align:start position:0%
and you'll see that there's plenty of
 

00:09:55.440 --> 00:09:58.190 align:start position:0%
and you'll see that there's plenty of
columns<00:09:56.000><c> here</c><00:09:57.000><c> um</c><00:09:57.399><c> that</c><00:09:57.600><c> that</c><00:09:57.680><c> are</c><00:09:57.880><c> being</c>

00:09:58.190 --> 00:09:58.200 align:start position:0%
columns here um that that are being
 

00:09:58.200 --> 00:10:00.829 align:start position:0%
columns here um that that are being
passed<00:09:58.519><c> into</c><00:09:58.800><c> the</c><00:09:58.880><c> mod</c><00:09:59.480><c> model</c><00:10:00.480><c> the</c><00:10:00.680><c> the</c>

00:10:00.829 --> 00:10:00.839 align:start position:0%
passed into the mod model the the
 

00:10:00.839 --> 00:10:04.630 align:start position:0%
passed into the mod model the the
question<00:10:01.240><c> now</c><00:10:01.519><c> is</c><00:10:02.440><c> how</c><00:10:02.600><c> do</c><00:10:02.720><c> you</c><00:10:03.000><c> actually</c><00:10:03.640><c> know</c>

00:10:04.630 --> 00:10:04.640 align:start position:0%
question now is how do you actually know
 

00:10:04.640 --> 00:10:07.590 align:start position:0%
question now is how do you actually know
which<00:10:05.120><c> columns</c><00:10:05.560><c> to</c><00:10:05.800><c> pass</c><00:10:06.079><c> in</c><00:10:06.720><c> and</c><00:10:06.880><c> even</c><00:10:07.279><c> before</c>

00:10:07.590 --> 00:10:07.600 align:start position:0%
which columns to pass in and even before
 

00:10:07.600 --> 00:10:08.829 align:start position:0%
which columns to pass in and even before
that<00:10:07.800><c> you</c><00:10:07.880><c> might</c><00:10:08.040><c> want</c><00:10:08.160><c> to</c><00:10:08.279><c> know</c><00:10:08.440><c> the</c><00:10:08.600><c> answer</c>

00:10:08.829 --> 00:10:08.839 align:start position:0%
that you might want to know the answer
 

00:10:08.839 --> 00:10:11.110 align:start position:0%
that you might want to know the answer
to<00:10:09.120><c> well</c><00:10:09.279><c> why</c><00:10:09.440><c> can't</c><00:10:09.640><c> we</c><00:10:09.800><c> just</c><00:10:10.040><c> pass</c><00:10:10.279><c> in</c><00:10:10.640><c> all</c><00:10:10.880><c> of</c>

00:10:11.110 --> 00:10:11.120 align:start position:0%
to well why can't we just pass in all of
 

00:10:11.120 --> 00:10:13.910 align:start position:0%
to well why can't we just pass in all of
the<00:10:11.440><c> columns</c><00:10:12.440><c> and</c><00:10:12.640><c> one</c><00:10:12.920><c> big</c><00:10:13.160><c> reason</c><00:10:13.480><c> for</c><00:10:13.720><c> that</c>

00:10:13.910 --> 00:10:13.920 align:start position:0%
the columns and one big reason for that
 

00:10:13.920 --> 00:10:16.670 align:start position:0%
the columns and one big reason for that
is<00:10:14.279><c> as</c><00:10:14.440><c> we</c><00:10:14.600><c> know</c><00:10:15.120><c> large</c><00:10:15.399><c> language</c><00:10:15.760><c> models</c><00:10:16.320><c> have</c>

00:10:16.670 --> 00:10:16.680 align:start position:0%
is as we know large language models have
 

00:10:16.680 --> 00:10:19.509 align:start position:0%
is as we know large language models have
a<00:10:16.839><c> limited</c><00:10:17.320><c> context</c><00:10:18.040><c> window</c><00:10:19.040><c> um</c><00:10:19.200><c> when</c><00:10:19.320><c> you're</c>

00:10:19.509 --> 00:10:19.519 align:start position:0%
a limited context window um when you're
 

00:10:19.519 --> 00:10:21.389 align:start position:0%
a limited context window um when you're
dealing<00:10:20.040><c> with</c><00:10:20.240><c> a</c><00:10:20.440><c> large</c><00:10:20.760><c> amount</c><00:10:21.000><c> of</c>

00:10:21.389 --> 00:10:21.399 align:start position:0%
dealing with a large amount of
 

00:10:21.399 --> 00:10:24.069 align:start position:0%
dealing with a large amount of
information<00:10:22.640><c> especially</c><00:10:23.640><c> in</c><00:10:23.800><c> in</c><00:10:23.920><c> the</c>

00:10:24.069 --> 00:10:24.079 align:start position:0%
information especially in in the
 

00:10:24.079 --> 00:10:26.949 align:start position:0%
information especially in in the
hundreds<00:10:24.600><c> of</c><00:10:24.959><c> of</c><00:10:25.200><c> features</c><00:10:25.600><c> and</c>

00:10:26.949 --> 00:10:26.959 align:start position:0%
hundreds of of features and
 

00:10:26.959 --> 00:10:29.389 align:start position:0%
hundreds of of features and
measures<00:10:27.959><c> you</c><00:10:28.160><c> you</c><00:10:28.320><c> must</c><00:10:28.640><c> limit</c><00:10:29.240><c> the</c>

00:10:29.389 --> 00:10:29.399 align:start position:0%
measures you you must limit the
 

00:10:29.399 --> 00:10:32.110 align:start position:0%
measures you you must limit the
information<00:10:29.959><c> that's</c><00:10:30.279><c> passed</c><00:10:30.600><c> in</c><00:10:31.399><c> and</c><00:10:31.560><c> so</c>

00:10:32.110 --> 00:10:32.120 align:start position:0%
information that's passed in and so
 

00:10:32.120 --> 00:10:33.829 align:start position:0%
information that's passed in and so
there's<00:10:32.480><c> a</c><00:10:32.600><c> little</c><00:10:32.800><c> bit</c><00:10:32.920><c> of</c><00:10:33.160><c> intelligent</c>

00:10:33.829 --> 00:10:33.839 align:start position:0%
there's a little bit of intelligent
 

00:10:33.839 --> 00:10:35.629 align:start position:0%
there's a little bit of intelligent
behind<00:10:34.160><c> the</c><00:10:34.320><c> work</c><00:10:34.680><c> additional</c><00:10:35.160><c> work</c><00:10:35.399><c> that's</c>

00:10:35.629 --> 00:10:35.639 align:start position:0%
behind the work additional work that's
 

00:10:35.639 --> 00:10:38.190 align:start position:0%
behind the work additional work that's
being<00:10:35.880><c> done</c><00:10:36.480><c> to</c><00:10:36.680><c> limit</c><00:10:37.079><c> the</c><00:10:37.519><c> the</c><00:10:37.720><c> the</c><00:10:37.800><c> use</c><00:10:38.000><c> of</c>

00:10:38.190 --> 00:10:38.200 align:start position:0%
being done to limit the the the use of
 

00:10:38.200 --> 00:10:41.269 align:start position:0%
being done to limit the the the use of
relevant<00:10:39.079><c> information</c><00:10:40.079><c> uh</c><00:10:40.519><c> just</c><00:10:40.720><c> touching</c><00:10:41.040><c> on</c>

00:10:41.269 --> 00:10:41.279 align:start position:0%
relevant information uh just touching on
 

00:10:41.279 --> 00:10:42.910 align:start position:0%
relevant information uh just touching on
how<00:10:41.480><c> that's</c><00:10:41.639><c> done</c><00:10:41.839><c> a</c><00:10:41.920><c> little</c><00:10:42.120><c> bit</c><00:10:42.360><c> that's</c><00:10:42.600><c> also</c>

00:10:42.910 --> 00:10:42.920 align:start position:0%
how that's done a little bit that's also
 

00:10:42.920 --> 00:10:48.030 align:start position:0%
how that's done a little bit that's also
being<00:10:43.240><c> done</c><00:10:43.600><c> using</c><00:10:44.120><c> generative</c><00:10:44.959><c> AI</c><00:10:45.959><c> um</c><00:10:46.760><c> and</c><00:10:47.440><c> in</c>

00:10:48.030 --> 00:10:48.040 align:start position:0%
being done using generative AI um and in
 

00:10:48.040 --> 00:10:50.069 align:start position:0%
being done using generative AI um and in
particular<00:10:49.040><c> uh</c><00:10:49.240><c> the</c><00:10:49.360><c> way</c><00:10:49.519><c> that</c><00:10:49.680><c> it's</c><00:10:49.839><c> being</c>

00:10:50.069 --> 00:10:50.079 align:start position:0%
particular uh the way that it's being
 

00:10:50.079 --> 00:10:53.750 align:start position:0%
particular uh the way that it's being
done<00:10:50.399><c> is</c><00:10:51.000><c> is</c><00:10:51.440><c> using</c><00:10:51.800><c> a</c><00:10:52.040><c> mechanism</c><00:10:52.639><c> called</c><00:10:53.440><c> um</c>

00:10:53.750 --> 00:10:53.760 align:start position:0%
done is is using a mechanism called um
 

00:10:53.760 --> 00:10:55.750 align:start position:0%
done is is using a mechanism called um
semantic<00:10:54.720><c> context</c>

00:10:55.750 --> 00:10:55.760 align:start position:0%
semantic context
 

00:10:55.760 --> 00:10:57.790 align:start position:0%
semantic context
encoding<00:10:56.760><c> uh</c><00:10:56.880><c> and</c><00:10:57.079><c> and</c><00:10:57.160><c> you</c><00:10:57.240><c> can</c><00:10:57.360><c> see</c><00:10:57.560><c> that</c><00:10:57.680><c> a</c>

00:10:57.790 --> 00:10:57.800 align:start position:0%
encoding uh and and you can see that a
 

00:10:57.800 --> 00:10:59.790 align:start position:0%
encoding uh and and you can see that a
little<00:10:58.000><c> bit</c><00:10:58.240><c> right</c><00:10:58.480><c> here</c><00:10:58.720><c> so</c>

00:10:59.790 --> 00:10:59.800 align:start position:0%
little bit right here so
 

00:10:59.800 --> 00:11:02.990 align:start position:0%
little bit right here so
there's<00:11:00.519><c> 982</c><00:11:01.519><c> different</c><00:11:01.920><c> columns</c><00:11:02.839><c> that's</c>

00:11:02.990 --> 00:11:03.000 align:start position:0%
there's 982 different columns that's
 

00:11:03.000 --> 00:11:04.670 align:start position:0%
there's 982 different columns that's
what<00:11:03.120><c> the</c><00:11:03.240><c> model</c><00:11:03.519><c> has</c><00:11:03.639><c> been</c><00:11:03.800><c> trained</c><00:11:04.279><c> with</c>

00:11:04.670 --> 00:11:04.680 align:start position:0%
what the model has been trained with
 

00:11:04.680 --> 00:11:06.069 align:start position:0%
what the model has been trained with
here's<00:11:04.880><c> an</c><00:11:05.079><c> example</c><00:11:05.440><c> of</c><00:11:05.560><c> some</c><00:11:05.680><c> of</c><00:11:05.839><c> those</c>

00:11:06.069 --> 00:11:06.079 align:start position:0%
here's an example of some of those
 

00:11:06.079 --> 00:11:09.030 align:start position:0%
here's an example of some of those
columns<00:11:06.600><c> so</c><00:11:06.880><c> you</c><00:11:07.320><c> have</c><00:11:08.320><c> information</c><00:11:08.760><c> about</c>

00:11:09.030 --> 00:11:09.040 align:start position:0%
columns so you have information about
 

00:11:09.040 --> 00:11:11.750 align:start position:0%
columns so you have information about
acute<00:11:09.320><c> organ</c><00:11:09.639><c> failure</c><00:11:10.279><c> Age</c><00:11:11.000><c> Medical</c>

00:11:11.750 --> 00:11:11.760 align:start position:0%
acute organ failure Age Medical
 

00:11:11.760 --> 00:11:14.550 align:start position:0%
acute organ failure Age Medical
Transport<00:11:12.760><c> um</c><00:11:12.959><c> unplanned</c><00:11:13.560><c> admissions</c><00:11:14.360><c> all</c>

00:11:14.550 --> 00:11:14.560 align:start position:0%
Transport um unplanned admissions all
 

00:11:14.560 --> 00:11:16.190 align:start position:0%
Transport um unplanned admissions all
sorts<00:11:14.839><c> of</c><00:11:15.079><c> information</c><00:11:15.560><c> about</c><00:11:15.839><c> that</c>

00:11:16.190 --> 00:11:16.200 align:start position:0%
sorts of information about that
 

00:11:16.200 --> 00:11:19.470 align:start position:0%
sorts of information about that
patient's<00:11:17.200><c> diagnosis</c><00:11:17.959><c> histories</c><00:11:18.480><c> procedure</c>

00:11:19.470 --> 00:11:19.480 align:start position:0%
patient's diagnosis histories procedure
 

00:11:19.480 --> 00:11:23.150 align:start position:0%
patient's diagnosis histories procedure
histories<00:11:20.480><c> um</c><00:11:21.279><c> and</c><00:11:22.040><c> out</c><00:11:22.200><c> of</c><00:11:22.360><c> these</c>

00:11:23.150 --> 00:11:23.160 align:start position:0%
histories um and out of these
 

00:11:23.160 --> 00:11:25.949 align:start position:0%
histories um and out of these
982<00:11:24.160><c> can't</c><00:11:24.399><c> pass</c><00:11:24.560><c> in</c><00:11:24.800><c> every</c><00:11:24.959><c> single</c><00:11:25.279><c> one</c><00:11:25.720><c> for</c>

00:11:25.949 --> 00:11:25.959 align:start position:0%
982 can't pass in every single one for
 

00:11:25.959 --> 00:11:27.790 align:start position:0%
982 can't pass in every single one for
for<00:11:26.200><c> every</c><00:11:26.399><c> single</c><00:11:26.760><c> question</c><00:11:27.320><c> and</c><00:11:27.440><c> so</c><00:11:27.600><c> you</c>

00:11:27.790 --> 00:11:27.800 align:start position:0%
for every single question and so you
 

00:11:27.800 --> 00:11:29.990 align:start position:0%
for every single question and so you
have<00:11:27.959><c> to</c><00:11:28.160><c> limit</c><00:11:28.800><c> what</c><00:11:29.160><c> information</c><00:11:29.560><c> is</c><00:11:29.720><c> being</c>

00:11:29.990 --> 00:11:30.000 align:start position:0%
have to limit what information is being
 

00:11:30.000 --> 00:11:34.470 align:start position:0%
have to limit what information is being
passed<00:11:30.279><c> in</c><00:11:31.079><c> that's</c><00:11:31.720><c> also</c><00:11:32.720><c> uh</c><00:11:33.079><c> part</c><00:11:33.600><c> of</c><00:11:34.279><c> and</c>

00:11:34.470 --> 00:11:34.480 align:start position:0%
passed in that's also uh part of and
 

00:11:34.480 --> 00:11:36.750 align:start position:0%
passed in that's also uh part of and
wrapped<00:11:34.959><c> into</c><00:11:35.639><c> the</c><00:11:35.800><c> capability</c><00:11:36.440><c> that</c><00:11:36.560><c> Jon</c>

00:11:36.750 --> 00:11:36.760 align:start position:0%
wrapped into the capability that Jon
 

00:11:36.760 --> 00:11:38.990 align:start position:0%
wrapped into the capability that Jon
Snow<00:11:37.120><c> laabs</c><00:11:37.480><c> was</c><00:11:37.839><c> was</c><00:11:38.120><c> was</c><00:11:38.279><c> able</c><00:11:38.519><c> to</c><00:11:38.720><c> provide</c>

00:11:38.990 --> 00:11:39.000 align:start position:0%
Snow laabs was was was able to provide
 

00:11:39.000 --> 00:11:43.670 align:start position:0%
Snow laabs was was was able to provide
us<00:11:39.240><c> with</c><00:11:40.000><c> and</c><00:11:40.279><c> that</c><00:11:41.200><c> um</c><00:11:41.680><c> touches</c><00:11:42.200><c> a</c><00:11:42.320><c> little</c><00:11:42.680><c> bit</c>

00:11:43.670 --> 00:11:43.680 align:start position:0%
us with and that um touches a little bit
 

00:11:43.680 --> 00:11:46.670 align:start position:0%
us with and that um touches a little bit
uh<00:11:44.040><c> on</c><00:11:44.360><c> what's</c><00:11:44.720><c> happening</c><00:11:45.320><c> with</c><00:11:45.560><c> that</c><00:11:45.760><c> widget</c>

00:11:46.670 --> 00:11:46.680 align:start position:0%
uh on what's happening with that widget
 

00:11:46.680 --> 00:11:49.790 align:start position:0%
uh on what's happening with that widget
in<00:11:46.920><c> this</c><00:11:47.200><c> first</c><00:11:47.519><c> screen</c><00:11:48.040><c> right</c><00:11:48.240><c> here</c><00:11:48.920><c> so</c><00:11:49.519><c> that</c>

00:11:49.790 --> 00:11:49.800 align:start position:0%
in this first screen right here so that
 

00:11:49.800 --> 00:11:53.190 align:start position:0%
in this first screen right here so that
I<00:11:49.920><c> think</c><00:11:50.279><c> um</c><00:11:51.279><c> this</c><00:11:51.440><c> is</c><00:11:52.160><c> uh</c><00:11:52.360><c> the</c><00:11:52.519><c> start</c><00:11:52.839><c> of</c><00:11:53.079><c> what</c>

00:11:53.190 --> 00:11:53.200 align:start position:0%
I think um this is uh the start of what
 

00:11:53.200 --> 00:11:54.710 align:start position:0%
I think um this is uh the start of what
we've<00:11:53.360><c> been</c><00:11:53.480><c> able</c><00:11:53.680><c> to</c><00:11:53.800><c> develop</c><00:11:54.160><c> and</c><00:11:54.480><c> we're</c>

00:11:54.710 --> 00:11:54.720 align:start position:0%
we've been able to develop and we're
 

00:11:54.720 --> 00:11:56.509 align:start position:0%
we've been able to develop and we're
really<00:11:54.959><c> excited</c><00:11:55.399><c> on</c><00:11:55.639><c> on</c><00:11:55.839><c> where</c><00:11:56.040><c> we</c><00:11:56.200><c> think</c><00:11:56.399><c> we</c>

00:11:56.509 --> 00:11:56.519 align:start position:0%
really excited on on where we think we
 

00:11:56.519 --> 00:11:59.629 align:start position:0%
really excited on on where we think we
can<00:11:56.720><c> take</c><00:11:57.000><c> this</c><00:11:57.920><c> um</c><00:11:58.279><c> in</c><00:11:58.480><c> in</c><00:11:58.600><c> ways</c><00:11:59.200><c> which</c><00:11:59.360><c> we</c><00:11:59.440><c> can</c>

00:11:59.629 --> 00:11:59.639 align:start position:0%
can take this um in in ways which we can
 

00:11:59.639 --> 00:12:01.870 align:start position:0%
can take this um in in ways which we can
enable<00:12:00.040><c> clinical</c><00:12:00.519><c> experts</c><00:12:01.200><c> and</c><00:12:01.440><c> population</c>

00:12:01.870 --> 00:12:01.880 align:start position:0%
enable clinical experts and population
 

00:12:01.880 --> 00:12:04.470 align:start position:0%
enable clinical experts and population
Health<00:12:02.120><c> managers</c><00:12:02.560><c> to</c><00:12:02.760><c> extract</c><00:12:03.240><c> more</c><00:12:03.920><c> insights</c>

00:12:04.470 --> 00:12:04.480 align:start position:0%
Health managers to extract more insights
 

00:12:04.480 --> 00:12:06.670 align:start position:0%
Health managers to extract more insights
from<00:12:04.760><c> from</c><00:12:04.920><c> their</c><00:12:05.160><c> data</c><00:12:05.959><c> um</c><00:12:06.120><c> and</c><00:12:06.240><c> to</c><00:12:06.399><c> be</c><00:12:06.519><c> able</c>

00:12:06.670 --> 00:12:06.680 align:start position:0%
from from their data um and to be able
 

00:12:06.680 --> 00:12:09.509 align:start position:0%
from from their data um and to be able
to<00:12:06.959><c> generate</c><00:12:07.639><c> and</c><00:12:07.800><c> take</c><00:12:08.360><c> actionable</c><00:12:09.360><c> uh</c>

00:12:09.509 --> 00:12:09.519 align:start position:0%
to generate and take actionable uh
 

00:12:09.519 --> 00:12:11.269 align:start position:0%
to generate and take actionable uh
intervention<00:12:10.160><c> strategies</c><00:12:10.720><c> for</c><00:12:11.000><c> their</c>

00:12:11.269 --> 00:12:11.279 align:start position:0%
intervention strategies for their
 

00:12:11.279 --> 00:12:13.470 align:start position:0%
intervention strategies for their
patients<00:12:11.959><c> on</c><00:12:12.120><c> a</c><00:12:12.279><c> wide</c><00:12:12.560><c> variety</c><00:12:13.079><c> of</c><00:12:13.320><c> of</c>

00:12:13.470 --> 00:12:13.480 align:start position:0%
patients on a wide variety of of
 

00:12:13.480 --> 00:12:15.670 align:start position:0%
patients on a wide variety of of
different<00:12:13.839><c> outcomes</c><00:12:14.839><c> um</c><00:12:15.120><c> please</c><00:12:15.320><c> feel</c><00:12:15.480><c> free</c>

00:12:15.670 --> 00:12:15.680 align:start position:0%
different outcomes um please feel free
 

00:12:15.680 --> 00:12:17.430 align:start position:0%
different outcomes um please feel free
to<00:12:15.839><c> reach</c><00:12:16.040><c> out</c><00:12:16.519><c> uh</c><00:12:16.639><c> if</c><00:12:16.680><c> you</c><00:12:16.760><c> want</c><00:12:16.880><c> to</c><00:12:17.000><c> learn</c><00:12:17.360><c> a</c>

00:12:17.430 --> 00:12:17.440 align:start position:0%
to reach out uh if you want to learn a
 

00:12:17.440 --> 00:12:19.230 align:start position:0%
to reach out uh if you want to learn a
little<00:12:17.560><c> bit</c><00:12:17.720><c> more</c><00:12:17.880><c> about</c><00:12:18.079><c> this</c><00:12:18.279><c> capability</c><00:12:19.079><c> or</c>

00:12:19.230 --> 00:12:19.240 align:start position:0%
little bit more about this capability or
 

00:12:19.240 --> 00:12:21.030 align:start position:0%
little bit more about this capability or
or<00:12:19.399><c> certainly</c><00:12:19.760><c> reach</c><00:12:19.920><c> out</c><00:12:20.040><c> to</c><00:12:20.160><c> johnell</c><00:12:20.680><c> labs</c>

00:12:21.030 --> 00:12:21.040 align:start position:0%
or certainly reach out to johnell labs
 

00:12:21.040 --> 00:12:23.110 align:start position:0%
or certainly reach out to johnell labs
and<00:12:21.480><c> thank</c><00:12:21.639><c> you</c><00:12:21.720><c> for</c><00:12:21.839><c> your</c>

00:12:23.110 --> 00:12:23.120 align:start position:0%
and thank you for your
 

00:12:23.120 --> 00:12:26.120 align:start position:0%
and thank you for your
time

