import { DatabaseService } from "@/lib/database";
import { 
    ExternalLinkIcon, 
    EyeIcon, 
    CalendarDaysIcon as Calendar, 
    ClockIcon as Clock,       
    PlayCircleIcon as Play, 
    MessageCircleIcon as MessageCircle 
} from "lucide-react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { formatDuration, formatViewCount, formatDate, truncateText } from "@/lib/utils";
import { Metadata } from 'next';
import { CategoryCopyButton, SummaryCopyButton, TranscriptCopyButton } from '@/components/CopyButtons'; 

interface VideoPageProps {
  params: { // No longer a Promise here, Next.js resolves it
    topic: string
    videoId: string
  }
}

// Helper to organize and render LLM response fields
const renderLlmResponse = (llmResponse: Record<string, unknown> | null) => {
  if (!llmResponse || Object.keys(llmResponse).length === 0) {
    return <p className="text-gray-500 dark:text-gray-400">No detailed summary information available.</p>;
  }
  // Define categories and their priority fields
  const categories: Record<string, string[]> = {
    "Summary": ['summary', 'short_summary', 'tldr', 'overview'],
    "Key Points": ['insights', 'key_insights', 'main_points', 'key_takeaways', 'key_points', 'important_points', 'highlights', 'Insights'],
    "Topics": ['keywords', 'topics', 'key_topics', 'subject_areas', 'categories', 'themes', 'important_concepts', 'Keywords'],
    "Questions": ['questions', 'key_questions', 'questions_answered', 'faqs', 'common_questions', 'Questions'],
    "Recommendations": ['recommendations', 'suggestions', 'advice', 'next_steps', 'Recommendations'],
    "Analysis": ['analysis', 'conclusion', 'evaluation', 'perspective', 'sentiment', 'sentiment_analysis'],
    "Technical Details": ['technical_details', 'specifications', 'methodology', 'technical_concepts'],
    "Other": [] 
  };
  
  const availableKeys = Object.keys(llmResponse);
  const organizedData: Record<string, Array<[string, unknown]>> = {};
  
  Object.keys(categories).forEach(category => {
    organizedData[category] = [];
  });
  
  // Ensure llmResponse is not null before processing
  if (llmResponse) {
    availableKeys.forEach(key => {
      if (['original_transcript_summary', 'title', 'channel_name'].includes(key)) {
        return;
      }
      let assigned = false; // Correctly scoped variable
      for (const [category, fields] of Object.entries(categories)) {
        if (fields.includes(key)) {
          organizedData[category].push([key, llmResponse[key]]);
          assigned = true;
          break;
        }
      }
      if (!assigned) {
        organizedData["Other"].push([key, llmResponse[key]]);
      }
    });
  }
  // Format field names nicely
  const formatFieldName = (key: string) => {
    return key
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatFieldValue = (key: string, value: unknown) => {
    if (value === null || value === undefined) return 'N/A';    if ((key.toLowerCase().includes('keyword') || key.toLowerCase().includes('topic') || key === 'tags' || key === 'Keywords') && Array.isArray(value)) {
      return (
        <div className="flex flex-wrap gap-2">
          {value.map((item, idx) => (
            <span key={idx} className="bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-full text-lg font-medium">
              {typeof item === 'object' ? JSON.stringify(item) : String(item)}
            </span>
          ))}
        </div>
      );
    }    if ((key.toLowerCase().includes('insight') || key.toLowerCase().includes('recommendation') || key.toLowerCase().includes('question') || key === 'Insights' || key === 'Recommendations' || key === 'Questions') && Array.isArray(value)) {
      return (
        <ul className="space-y-2 pl-0">
          {value.map((item, idx) => (
            <li key={idx} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700">
              <div className="text-gray-800 dark:text-gray-200 text-lg">
                {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}
              </div>
            </li>
          ))}
        </ul>
      );
    }    if (Array.isArray(value)) {
      return (
        <ul className="list-disc pl-5 space-y-1">
          {value.map((item, idx) => (
            <li key={idx} className="text-gray-800 dark:text-gray-200 text-lg">
              {typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)}
            </li>
          ))}
        </ul>
      );
    }    if (typeof value === 'object') {
      return (
        <pre className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md overflow-auto text-lg">
          {JSON.stringify(value, null, 2)}
        </pre>
      );
    }
    if (['key_insights', 'insights', 'main_points', 'key_takeaways', 'Insights'].includes(key) && typeof value === 'string') {
      if (value.includes("- ") || value.includes("• ")) {
        const bulletPoints = value.split(/\n\s*[-•]\s+/);        return (
          <ul className="space-y-2 pl-0">
            {bulletPoints.filter(Boolean).map((point, idx) => (
              <li key={idx} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700">
                <div className="text-gray-800 dark:text-gray-200 text-lg">{point.trim()}</div>
              </li>
            ))}
          </ul>
        );
      }
    }
    if ((key.toLowerCase().includes('question') || key.toLowerCase().includes('faq') || key === 'Questions') && typeof value === 'string') {
      if (value.includes("\n")) {
        const questions = value.split(/\n+/).filter(Boolean);        return (
          <ul className="space-y-2 pl-0">
            {questions.map((question, idx) => (
              <li key={idx} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-purple-500 dark:border-purple-700">
                <div className="text-gray-800 dark:text-gray-200 text-lg">{question.trim()}</div>
              </li>
            ))}
          </ul>
        );
      }
    }    if (['key_insights', 'insights', 'summary', 'tldr', 'key_takeaways', 'main_points', 'recommendations', 'Insights', 'Recommendations'].includes(key)) {
      return <div className="font-medium text-gray-900 dark:text-gray-100 text-lg">{String(value)}</div>;
    }
    return <span className="text-gray-800 dark:text-gray-200 text-lg">{String(value)}</span>;
  };
  
  return (
    <div className="space-y-6">
      {Object.entries(organizedData).map(([category, fields]) => {
        if (fields.length === 0) return null;
        return (          <div key={category} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow transition-shadow duration-200">
            <div className="bg-gray-100 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 text-xl">{category}</h3>
              {(category === "Key Points" || category === "Recommendations" || category === "Topics" || category === "Questions") && fields.length > 0 && (
                <CategoryCopyButton 
                  category={category} 
                  fields={fields} 
                />
              )}
            </div>            <dl className="divide-y divide-gray-200 dark:divide-gray-800">
              {fields.map(([key, value]) => (
                <div key={key} className="px-4 py-3 group">
                  <dt className="text-lg font-medium text-gray-500 dark:text-gray-400">{formatFieldName(key)}</dt>
                  <dd className="mt-1 text-lg text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                    {formatFieldValue(key, value)}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        );
      })}
    </div>
  );
}


export default async function VideoPage({ params }: VideoPageProps) {
  // In Next.js 14/15, using await with params is now required for dynamic path segments
  const topic = String(await Promise.resolve(params.topic));
  const videoId = String(await Promise.resolve(params.videoId));
  const video = await DatabaseService.getVideoById(topic, videoId);

  if (!video) {
    notFound();
  }

  const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;

  return (
    <div className="p-4 sm:p-6 lg:p-8"> {/* Removed max-w-6xl and mx-auto, kept padding */}
      {/* Top Section: Header Info (Left) + Video Player (Right) */}
      <div className="flex flex-col md:flex-row md:items-start gap-6 lg:gap-8 mb-8">
        {/* Left part: Header Content (Title, breadcrumbs, metadata, actions) */}
        <div className="flex-grow space-y-4 md:pr-6 lg:pr-8">
          {/* Breadcrumbs */}
          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <Link href="/" className="hover:text-primary dark:hover:text-primary-light">Home</Link>
            <span>/</span>
            <span className="bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs">
              {video.topic_category || 'Uncategorized'}
            </span>
          </div>
          
          <h1 className="text-3xl font-bold text-foreground leading-tight">
            {video.title}
          </h1>
          
          <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
            <span className="font-medium">{video.channel_name}</span>
            {video.view_count !== null && video.view_count !== undefined && (
              <div className="flex items-center gap-1">
                <EyeIcon className="w-4 h-4" />
                {formatViewCount(video.view_count)}
              </div>
            )}
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              {/* Ensure published_at is used */}
              {formatDate(video.published_at || new Date().toISOString())}
            </div>
            {video.duration && (
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {formatDuration(video.duration)}
              </div>
            )}
          </div>

          {/* Actions - Moved here */}
          <div className="flex flex-wrap gap-3 mt-4">
            {video.video_url && (
              <Link 
                href={video.video_url} 
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                <Play className="w-4 h-4 mr-2" />
                Watch on YouTube
              </Link>
            )}
            {video.channel_id && (
              <Link 
                href={`https://www.youtube.com/channel/${video.channel_id}`} 
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                <ExternalLinkIcon className="w-4 h-4 mr-2" />
                View Channel
              </Link>
            )}
          </div>
        </div>

        {/* Right part: Video Player (Smaller) */}
        <div className="w-full md:w-[320px] lg:w-[400px] xl:w-[480px] flex-shrink-0 self-start md:sticky md:top-6">
          {youtubeEmbedUrl ? (
            <div className="aspect-video overflow-hidden rounded-lg border border-border dark:border-gray-700 relative shadow-lg">
              <iframe
                width="100%"
                height="100%"
                src={youtubeEmbedUrl}
                title={video.title ?? "YouTube video player"}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                className="rounded-lg"
              ></iframe>
            </div>
          ) : (
             <div className="aspect-video overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 relative bg-muted flex items-center justify-center shadow-lg">
                <Play className="w-16 h-16 text-muted-foreground" />
                <p className="ml-2 text-muted-foreground">Video not available</p>
             </div>
          )}
        </div>
      </div> {/* End of Top Section */}

      {/* Main Content Section: AI Analysis and Transcript */}
      <div className="w-full space-y-8"> {/* Added w-full here */}
        {/* AI Analysis Section */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-2xl font-semibold flex items-center text-foreground">
              <MessageCircle className="w-6 h-6 mr-2" /> AI Analysis
            </h2>
            {/* Placeholder for any actions related to AI Analysis section */}
          </div>
          {/* Removed the bg-card styling from this div, it will now inherit the background */}
          <div>
            {/* Quick Summary */}
            {video.llm_response && (
              typeof video.llm_response.tldr === 'string' || 
              typeof video.llm_response.short_summary === 'string' ||
              typeof video.llm_response.summary === 'string'
            ) && (              <div className="mb-6 p-4 bg-primary/10 rounded-lg border border-primary/20 shadow-sm">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-primary mb-2 text-2xl">Quick Summary</h3>
                  <SummaryCopyButton 
                    text={typeof video.llm_response?.tldr === 'string' 
                      ? video.llm_response.tldr 
                      : typeof video.llm_response?.short_summary === 'string' 
                        ? video.llm_response.short_summary 
                        : typeof video.llm_response?.summary === 'string'
                          ? video.llm_response.summary
                          : 'No summary available'
                    }
                  />
                </div>
                <p className="text-foreground font-medium text-lg">
                  {(typeof video.llm_response.tldr === 'string' 
                    ? video.llm_response.tldr 
                    : typeof video.llm_response.short_summary === 'string' 
                      ? video.llm_response.short_summary 
                      : typeof video.llm_response.summary === 'string'
                        ? video.llm_response.summary
                        : 'No summary available'
                  )}
                </p> {/* Added closing p tag here */}
            </div>
            )}
            {/* Full LLM Response data */}
            {renderLlmResponse(video.llm_response)}
          </div>
        </div>
        
        {/* Transcript Section */}
        {video.transcript && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-2xl font-semibold flex items-center text-foreground">
                Transcript
              </h2>
              <TranscriptCopyButton transcript={video.transcript || ''} />
            </div>            <details className="mt-1 group">
              <summary className="cursor-pointer font-medium text-primary hover:underline flex items-center text-2xl">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 transform transition-transform group-open:rotate-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
                View Full Transcript
              </summary>
              <div className="mt-2 p-4 bg-muted rounded-lg shadow max-h-96 overflow-y-auto border border-border">
                <p className="text-muted-foreground whitespace-pre-wrap text-lg leading-relaxed">{video.transcript}</p>
              </div>
            </details>
          </div>
        )}
      </div> {/* End of Main Content Section */}
    </div>
  );
}

export async function generateMetadata({ params }: { params: { topic: string; videoId: string } }): Promise<Metadata> {
  // In Next.js 14/15, using await with params is now required for dynamic path segments
  const topic = String(await Promise.resolve(params.topic));
  const videoId = String(await Promise.resolve(params.videoId));
  const video = await DatabaseService.getVideoById(topic, videoId);

  if (!video) {
    return {
      title: 'Video Not Found',
      description: 'This video could not be found or is not available.',
    };
  }

  // Get the best summary from the JSONB data
  let metaDescription = 'No summary available.';
  let keywords: string[] = [];
  
  if (video.llm_response) {
    // Find the best summary source
    if (typeof video.llm_response.tldr === 'string') {
      metaDescription = video.llm_response.tldr;
    } else if (typeof video.llm_response.short_summary === 'string') {
      metaDescription = video.llm_response.short_summary;
    } else if (typeof video.llm_response.summary === 'string') {
      metaDescription = video.llm_response.summary;
    }
      // Extract keywords if available (handle both lowercase and capitalized keys)
    if (Array.isArray(video.llm_response.keywords)) {
      keywords = video.llm_response.keywords.map((k: unknown) => String(k));
    } else if (Array.isArray(video.llm_response.Keywords)) {
      keywords = video.llm_response.Keywords.map((k: unknown) => String(k));
    } else if (Array.isArray(video.llm_response.topics)) {
      keywords = video.llm_response.topics.map((k: unknown) => String(k));
    } else if (typeof video.llm_response.keywords === 'string') {
      // Try to parse keywords from string (comma separated)
      keywords = video.llm_response.keywords.split(/,\\s*/);
    } else if (typeof video.llm_response.Keywords === 'string') {
      // Try to parse keywords from string (comma separated)
      keywords = video.llm_response.Keywords.split(/,\\s*/);
    }
  }
  
  // Limit description length
  metaDescription = truncateText(metaDescription, 160);

  // Create a rich metadata object
  return {
    title: video.title ?? 'Video Summary',
    description: metaDescription,
    keywords: keywords.length > 0 ? keywords : undefined,
    openGraph: {
      title: video.title ?? 'Video Summary',
      description: metaDescription,
      images: [
        {
          url: video.thumbnail_url ?? '/placeholder.svg',
          width: 1200,
          height: 630,
          alt: video.title ?? 'Video thumbnail',
        },
      ],      type: 'video.other',
      url: `/video/${topic}/${videoId}`,
      siteName: 'YouTube Summaries',
    },
    twitter: {
      card: 'summary_large_image',
      title: video.title ?? 'Video Summary',
      description: metaDescription,
      images: [video.thumbnail_url ?? '/placeholder.svg'],
    },
    authors: video.channel_name ? [{ name: video.channel_name }] : undefined,
  };
}
