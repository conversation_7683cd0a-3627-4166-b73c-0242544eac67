WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:02.750 align:start position:0%
 
hey<00:00:01.280><c> everyone</c><00:00:01.640><c> it's</c><00:00:01.800><c> ashre</c><00:00:02.320><c> and</c><00:00:02.440><c> today</c><00:00:02.639><c> I'll</c>

00:00:02.750 --> 00:00:02.760 align:start position:0%
hey everyone it's ashre and today I'll
 

00:00:02.760 --> 00:00:04.150 align:start position:0%
hey everyone it's ashre and today I'll
show<00:00:02.919><c> you</c><00:00:03.000><c> how</c><00:00:03.120><c> to</c><00:00:03.240><c> build</c><00:00:03.480><c> a</c><00:00:03.679><c> research</c>

00:00:04.150 --> 00:00:04.160 align:start position:0%
show you how to build a research
 

00:00:04.160 --> 00:00:06.389 align:start position:0%
show you how to build a research
assistant<00:00:04.720><c> powered</c><00:00:05.080><c> by</c><00:00:05.240><c> the</c><00:00:05.400><c> latest</c><00:00:05.759><c> llama</c><00:00:06.120><c> 3</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
assistant powered by the latest llama 3
 

00:00:06.399 --> 00:00:08.669 align:start position:0%
assistant powered by the latest llama 3
models<00:00:06.960><c> running</c><00:00:07.279><c> on</c><00:00:07.480><c> Gro</c><00:00:08.280><c> what</c><00:00:08.400><c> we're</c><00:00:08.559><c> going</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
models running on Gro what we're going
 

00:00:08.679 --> 00:00:10.070 align:start position:0%
models running on Gro what we're going
to<00:00:08.800><c> do</c><00:00:08.960><c> today</c><00:00:09.280><c> is</c><00:00:09.400><c> we're</c><00:00:09.559><c> going</c><00:00:09.639><c> to</c><00:00:09.760><c> take</c><00:00:09.880><c> a</c>

00:00:10.070 --> 00:00:10.080 align:start position:0%
to do today is we're going to take a
 

00:00:10.080 --> 00:00:12.549 align:start position:0%
to do today is we're going to take a
complex<00:00:10.559><c> topic</c><00:00:11.360><c> search</c><00:00:11.719><c> the</c><00:00:11.880><c> web</c><00:00:12.040><c> for</c><00:00:12.280><c> of</c>

00:00:12.549 --> 00:00:12.559 align:start position:0%
complex topic search the web for of
 

00:00:12.559 --> 00:00:15.030 align:start position:0%
complex topic search the web for of
information<00:00:13.080><c> around</c><00:00:13.360><c> it</c><00:00:14.120><c> package</c><00:00:14.440><c> it</c><00:00:14.639><c> up</c><00:00:14.879><c> and</c>

00:00:15.030 --> 00:00:15.040 align:start position:0%
information around it package it up and
 

00:00:15.040 --> 00:00:17.790 align:start position:0%
information around it package it up and
send<00:00:15.240><c> it</c><00:00:15.360><c> to</c><00:00:15.559><c> llama</c><00:00:16.000><c> 3</c><00:00:16.279><c> running</c><00:00:16.600><c> on</c><00:00:16.840><c> Gro</c><00:00:17.640><c> which</c>

00:00:17.790 --> 00:00:17.800 align:start position:0%
send it to llama 3 running on Gro which
 

00:00:17.800 --> 00:00:20.550 align:start position:0%
send it to llama 3 running on Gro which
sends<00:00:18.199><c> us</c><00:00:18.480><c> back</c><00:00:18.800><c> a</c><00:00:19.039><c> proper</c><00:00:19.400><c> research</c><00:00:19.920><c> report</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
sends us back a proper research report
 

00:00:20.560 --> 00:00:21.790 align:start position:0%
sends us back a proper research report
we're<00:00:20.720><c> going</c><00:00:20.800><c> to</c><00:00:20.960><c> run</c><00:00:21.160><c> this</c><00:00:21.359><c> application</c>

00:00:21.790 --> 00:00:21.800 align:start position:0%
we're going to run this application
 

00:00:21.800 --> 00:00:24.230 align:start position:0%
we're going to run this application
using<00:00:22.080><c> streamlet</c><00:00:22.640><c> so</c><00:00:22.800><c> let's</c><00:00:23.039><c> get</c><00:00:23.160><c> started</c><00:00:24.080><c> the</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
using streamlet so let's get started the
 

00:00:24.240 --> 00:00:26.669 align:start position:0%
using streamlet so let's get started the
code<00:00:24.480><c> for</c><00:00:24.680><c> this</c><00:00:24.840><c> AI</c><00:00:25.199><c> app</c><00:00:25.519><c> is</c><00:00:25.720><c> in</c><00:00:26.000><c> the</c><00:00:26.119><c> FI</c><00:00:26.439><c> dat</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
code for this AI app is in the FI dat
 

00:00:26.679 --> 00:00:29.230 align:start position:0%
code for this AI app is in the FI dat
repo<00:00:27.400><c> so</c><00:00:27.599><c> you</c><00:00:27.720><c> can</c><00:00:27.920><c> fork</c><00:00:28.439><c> and</c><00:00:28.640><c> clone</c><00:00:29.000><c> this</c>

00:00:29.230 --> 00:00:29.240 align:start position:0%
repo so you can fork and clone this
 

00:00:29.240 --> 00:00:31.630 align:start position:0%
repo so you can fork and clone this
Repository<00:00:30.160><c> and</c><00:00:30.279><c> then</c><00:00:30.480><c> go</c><00:00:30.679><c> to</c><00:00:30.840><c> the</c><00:00:31.080><c> cookbook</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
Repository and then go to the cookbook
 

00:00:31.640 --> 00:00:34.069 align:start position:0%
Repository and then go to the cookbook
llm<00:00:32.320><c> groc</c><00:00:32.759><c> research</c><00:00:33.200><c> folder</c><00:00:33.719><c> don't</c><00:00:33.879><c> worry</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
llm groc research folder don't worry
 

00:00:34.079 --> 00:00:35.830 align:start position:0%
llm groc research folder don't worry
I'll<00:00:34.239><c> drop</c><00:00:34.440><c> a</c><00:00:34.559><c> link</c><00:00:34.760><c> under</c><00:00:35.000><c> this</c><00:00:35.160><c> video</c><00:00:35.399><c> as</c>

00:00:35.830 --> 00:00:35.840 align:start position:0%
I'll drop a link under this video as
 

00:00:35.840 --> 00:00:38.549 align:start position:0%
I'll drop a link under this video as
well<00:00:36.840><c> after</c><00:00:37.079><c> you've</c><00:00:37.280><c> cloned</c><00:00:37.600><c> this</c><00:00:37.719><c> repository</c>

00:00:38.549 --> 00:00:38.559 align:start position:0%
well after you've cloned this repository
 

00:00:38.559 --> 00:00:40.990 align:start position:0%
well after you've cloned this repository
go<00:00:38.719><c> to</c><00:00:38.840><c> the</c><00:00:39.000><c> cookbook</c><00:00:39.440><c> llms</c><00:00:40.200><c> Gro</c><00:00:40.559><c> research</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
go to the cookbook llms Gro research
 

00:00:41.000 --> 00:00:42.910 align:start position:0%
go to the cookbook llms Gro research
folder<00:00:41.520><c> where</c><00:00:41.640><c> you'll</c><00:00:41.879><c> find</c><00:00:42.160><c> the</c><00:00:42.280><c> readme</c>

00:00:42.910 --> 00:00:42.920 align:start position:0%
folder where you'll find the readme
 

00:00:42.920 --> 00:00:45.470 align:start position:0%
folder where you'll find the readme
which<00:00:43.079><c> has</c><00:00:43.360><c> stepbystep</c><00:00:44.239><c> instructions</c><00:00:45.160><c> on</c><00:00:45.360><c> how</c>

00:00:45.470 --> 00:00:45.480 align:start position:0%
which has stepbystep instructions on how
 

00:00:45.480 --> 00:00:47.430 align:start position:0%
which has stepbystep instructions on how
to<00:00:45.640><c> run</c><00:00:45.840><c> this</c><00:00:46.039><c> research</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
to run this research
 

00:00:47.440 --> 00:00:49.790 align:start position:0%
to run this research
assistant<00:00:48.440><c> fire</c><00:00:48.760><c> up</c><00:00:48.920><c> your</c>

00:00:49.790 --> 00:00:49.800 align:start position:0%
assistant fire up your
 

00:00:49.800 --> 00:00:52.709 align:start position:0%
assistant fire up your
terminal<00:00:50.800><c> and</c><00:00:51.000><c> create</c><00:00:51.280><c> a</c><00:00:51.440><c> virtual</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
terminal and create a virtual
 

00:00:52.719 --> 00:00:55.270 align:start position:0%
terminal and create a virtual
environment<00:00:53.719><c> then</c><00:00:53.960><c> export</c><00:00:54.320><c> your</c><00:00:54.480><c> grock</c><00:00:54.840><c> API</c>

00:00:55.270 --> 00:00:55.280 align:start position:0%
environment then export your grock API
 

00:00:55.280 --> 00:00:57.470 align:start position:0%
environment then export your grock API
key<00:00:55.640><c> and</c><00:00:55.840><c> the</c><00:00:56.000><c> T</c><00:00:56.399><c> API</c><00:00:56.800><c> key</c><00:00:57.039><c> which</c><00:00:57.160><c> is</c><00:00:57.359><c> what</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
key and the T API key which is what
 

00:00:57.480 --> 00:01:00.270 align:start position:0%
key and the T API key which is what
we're<00:00:57.680><c> going</c><00:00:57.840><c> to</c><00:00:58.000><c> use</c><00:00:58.199><c> for</c><00:00:58.440><c> web</c><00:00:58.680><c> search</c><00:01:00.039><c> then</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
we're going to use for web search then
 

00:01:00.280 --> 00:01:03.069 align:start position:0%
we're going to use for web search then
install<00:01:00.680><c> your</c><00:01:01.079><c> libraries</c><00:01:02.079><c> Gro</c><00:01:02.519><c> F</c><00:01:02.800><c> dat</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
install your libraries Gro F dat
 

00:01:03.079 --> 00:01:06.590 align:start position:0%
install your libraries Gro F dat
streamlet<00:01:03.760><c> tavil</c><00:01:04.640><c> Etc</c><00:01:05.640><c> and</c><00:01:05.840><c> then</c><00:01:06.200><c> run</c><00:01:06.479><c> the</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
streamlet tavil Etc and then run the
 

00:01:06.600 --> 00:01:08.429 align:start position:0%
streamlet tavil Etc and then run the
streamlet<00:01:07.240><c> application</c><00:01:07.960><c> which</c><00:01:08.119><c> has</c><00:01:08.280><c> the</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
streamlet application which has the
 

00:01:08.439 --> 00:01:10.990 align:start position:0%
streamlet application which has the
research<00:01:09.200><c> assistant</c><00:01:10.200><c> now</c><00:01:10.400><c> this</c><00:01:10.560><c> research</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
research assistant now this research
 

00:01:11.000 --> 00:01:13.590 align:start position:0%
research assistant now this research
assistant<00:01:11.479><c> will</c><00:01:11.680><c> take</c><00:01:11.840><c> a</c><00:01:12.080><c> topic</c><00:01:12.960><c> search</c><00:01:13.360><c> the</c>

00:01:13.590 --> 00:01:13.600 align:start position:0%
assistant will take a topic search the
 

00:01:13.600 --> 00:01:16.390 align:start position:0%
assistant will take a topic search the
web<00:01:14.600><c> package</c><00:01:14.920><c> it</c><00:01:15.040><c> up</c><00:01:15.200><c> and</c><00:01:15.320><c> send</c><00:01:15.520><c> it</c><00:01:15.600><c> to</c><00:01:15.759><c> L</c><00:01:16.080><c> 3</c><00:01:16.280><c> to</c>

00:01:16.390 --> 00:01:16.400 align:start position:0%
web package it up and send it to L 3 to
 

00:01:16.400 --> 00:01:18.789 align:start position:0%
web package it up and send it to L 3 to
produce<00:01:16.680><c> a</c><00:01:16.840><c> research</c><00:01:17.280><c> report</c><00:01:18.200><c> let's</c><00:01:18.439><c> check</c><00:01:18.600><c> it</c>

00:01:18.789 --> 00:01:18.799 align:start position:0%
produce a research report let's check it
 

00:01:18.799 --> 00:01:21.230 align:start position:0%
produce a research report let's check it
out<00:01:19.040><c> with</c><00:01:19.200><c> the</c><00:01:19.360><c> sample</c><00:01:19.920><c> Lama</c><00:01:20.280><c> 3</c><00:01:20.439><c> inference</c><00:01:20.840><c> on</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
out with the sample Lama 3 inference on
 

00:01:21.240 --> 00:01:23.990 align:start position:0%
out with the sample Lama 3 inference on
croud<00:01:22.240><c> it's</c><00:01:22.439><c> now</c><00:01:22.640><c> searching</c><00:01:23.079><c> the</c><00:01:23.240><c> web</c><00:01:23.799><c> and</c>

00:01:23.990 --> 00:01:24.000 align:start position:0%
croud it's now searching the web and
 

00:01:24.000 --> 00:01:25.990 align:start position:0%
croud it's now searching the web and
then<00:01:24.119><c> sending</c><00:01:24.439><c> it</c><00:01:24.560><c> to</c><00:01:24.680><c> Lama</c><00:01:25.000><c> 3</c><00:01:25.280><c> while</c><00:01:25.479><c> that</c><00:01:25.600><c> was</c>

00:01:25.990 --> 00:01:26.000 align:start position:0%
then sending it to Lama 3 while that was
 

00:01:26.000 --> 00:01:28.910 align:start position:0%
then sending it to Lama 3 while that was
fast

00:01:28.910 --> 00:01:28.920 align:start position:0%
fast
 

00:01:28.920 --> 00:01:31.429 align:start position:0%
fast
um<00:01:29.880><c> under</c><00:01:30.040><c> the</c><00:01:30.159><c> web</c><00:01:30.360><c> search</c><00:01:30.600><c> you</c><00:01:30.720><c> can</c><00:01:30.880><c> see</c><00:01:31.280><c> what</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
um under the web search you can see what
 

00:01:31.439 --> 00:01:33.910 align:start position:0%
um under the web search you can see what
web<00:01:31.680><c> search</c><00:01:31.960><c> we</c><00:01:32.159><c> had</c><00:01:32.560><c> we</c><00:01:32.759><c> packaged</c><00:01:33.200><c> it</c><00:01:33.360><c> up</c><00:01:33.640><c> into</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
web search we had we packaged it up into
 

00:01:33.920 --> 00:01:37.149 align:start position:0%
web search we had we packaged it up into
markdown<00:01:34.560><c> format</c><00:01:35.040><c> which</c><00:01:35.159><c> we</c><00:01:35.280><c> sent</c><00:01:35.520><c> to</c><00:01:35.680><c> Lama</c><00:01:36.159><c> 3</c>

00:01:37.149 --> 00:01:37.159 align:start position:0%
markdown format which we sent to Lama 3
 

00:01:37.159 --> 00:01:40.149 align:start position:0%
markdown format which we sent to Lama 3
and<00:01:37.399><c> then</c><00:01:37.880><c> Gro</c><00:01:38.200><c> produces</c><00:01:39.000><c> a</c><00:01:39.280><c> research</c><00:01:39.759><c> report</c>

00:01:40.149 --> 00:01:40.159 align:start position:0%
and then Gro produces a research report
 

00:01:40.159 --> 00:01:44.830 align:start position:0%
and then Gro produces a research report
for<00:01:40.439><c> us</c><00:01:41.280><c> pretty</c><00:01:41.520><c> much</c><00:01:41.840><c> within</c><00:01:42.840><c> seconds</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
for us pretty much within seconds
 

00:01:44.840 --> 00:01:47.350 align:start position:0%
for us pretty much within seconds
really<00:01:45.840><c> and</c><00:01:46.280><c> highlights</c><00:01:46.759><c> everything</c><00:01:47.159><c> gives</c>

00:01:47.350 --> 00:01:47.360 align:start position:0%
really and highlights everything gives
 

00:01:47.360 --> 00:01:51.149 align:start position:0%
really and highlights everything gives
us<00:01:47.600><c> references</c><00:01:48.520><c> Etc</c><00:01:49.520><c> now</c><00:01:49.799><c> this</c><00:01:50.079><c> is</c><00:01:50.360><c> powered</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
us references Etc now this is powered
 

00:01:51.159 --> 00:01:53.630 align:start position:0%
us references Etc now this is powered
again<00:01:51.479><c> if</c><00:01:51.600><c> you've</c><00:01:51.799><c> been</c><00:01:51.960><c> using</c><00:01:52.280><c> fi</c><00:01:52.560><c> data</c><00:01:53.360><c> This</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
again if you've been using fi data This
 

00:01:53.640 --> 00:01:55.550 align:start position:0%
again if you've been using fi data This
research<00:01:54.200><c> application</c><00:01:54.759><c> has</c><00:01:54.880><c> a</c><00:01:55.000><c> f</c><00:01:55.240><c> data</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
research application has a f data
 

00:01:55.560 --> 00:01:58.069 align:start position:0%
research application has a f data
assistant<00:01:56.320><c> let</c><00:01:56.439><c> me</c><00:01:56.600><c> just</c><00:01:56.759><c> hide</c><00:01:57.079><c> this</c><00:01:57.759><c> which</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
assistant let me just hide this which
 

00:01:58.079 --> 00:02:00.149 align:start position:0%
assistant let me just hide this which
has<00:01:58.759><c> all</c><00:01:59.000><c> the</c><00:01:59.159><c> descriptions</c><00:01:59.680><c> and</c><00:02:00.000><c> the</c>

00:02:00.149 --> 00:02:00.159 align:start position:0%
has all the descriptions and the
 

00:02:00.159 --> 00:02:01.630 align:start position:0%
has all the descriptions and the
instructions<00:02:00.600><c> on</c><00:02:00.799><c> how</c><00:02:00.920><c> to</c><00:02:01.079><c> produce</c><00:02:01.399><c> this</c>

00:02:01.630 --> 00:02:01.640 align:start position:0%
instructions on how to produce this
 

00:02:01.640 --> 00:02:04.630 align:start position:0%
instructions on how to produce this
report<00:02:02.320><c> alter</c><00:02:02.680><c> it</c><00:02:02.960><c> change</c><00:02:03.200><c> it</c><00:02:03.360><c> to</c><00:02:03.479><c> your</c><00:02:03.680><c> will</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
report alter it change it to your will
 

00:02:04.640 --> 00:02:06.749 align:start position:0%
report alter it change it to your will
also<00:02:05.079><c> we</c><00:02:05.240><c> have</c><00:02:05.439><c> the</c><00:02:05.600><c> report</c><00:02:06.000><c> format</c><00:02:06.439><c> which</c><00:02:06.560><c> is</c>

00:02:06.749 --> 00:02:06.759 align:start position:0%
also we have the report format which is
 

00:02:06.759 --> 00:02:09.990 align:start position:0%
also we have the report format which is
title<00:02:07.479><c> section</c><00:02:08.479><c> conclusion</c><00:02:09.039><c> and</c><00:02:09.239><c> references</c>

00:02:09.990 --> 00:02:10.000 align:start position:0%
title section conclusion and references
 

00:02:10.000 --> 00:02:12.550 align:start position:0%
title section conclusion and references
again<00:02:10.479><c> feel</c><00:02:10.720><c> free</c><00:02:10.920><c> to</c><00:02:11.080><c> change</c><00:02:11.319><c> it</c><00:02:11.520><c> and</c><00:02:11.680><c> produce</c>

00:02:12.550 --> 00:02:12.560 align:start position:0%
again feel free to change it and produce
 

00:02:12.560 --> 00:02:14.430 align:start position:0%
again feel free to change it and produce
the<00:02:12.760><c> report</c><00:02:13.200><c> according</c><00:02:13.520><c> to</c><00:02:13.720><c> your</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
the report according to your
 

00:02:14.440 --> 00:02:17.790 align:start position:0%
the report according to your
taste<00:02:15.440><c> let's</c><00:02:15.680><c> try</c><00:02:16.080><c> another</c><00:02:16.920><c> topic</c><00:02:17.319><c> AI</c>

00:02:17.790 --> 00:02:17.800 align:start position:0%
taste let's try another topic AI
 

00:02:17.800 --> 00:02:20.990 align:start position:0%
taste let's try another topic AI
Healthcare<00:02:18.360><c> again</c><00:02:18.519><c> it</c><00:02:18.680><c> searches</c><00:02:19.120><c> the</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
Healthcare again it searches the
 

00:02:21.000 --> 00:02:23.509 align:start position:0%
Healthcare again it searches the
web<00:02:22.000><c> and</c><00:02:22.160><c> then</c><00:02:22.319><c> generates</c><00:02:22.760><c> a</c>

00:02:23.509 --> 00:02:23.519 align:start position:0%
web and then generates a
 

00:02:23.519 --> 00:02:26.150 align:start position:0%
web and then generates a
report<00:02:24.519><c> you</c><00:02:24.680><c> can</c><00:02:24.879><c> see</c><00:02:25.200><c> most</c><00:02:25.360><c> of</c><00:02:25.519><c> the</c><00:02:25.760><c> time</c><00:02:26.000><c> is</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
report you can see most of the time is
 

00:02:26.160 --> 00:02:29.229 align:start position:0%
report you can see most of the time is
on<00:02:26.360><c> the</c><00:02:26.480><c> web</c><00:02:26.720><c> search</c><00:02:27.080><c> really</c><00:02:27.959><c> the</c><00:02:28.720><c> generation</c>

00:02:29.229 --> 00:02:29.239 align:start position:0%
on the web search really the generation
 

00:02:29.239 --> 00:02:32.270 align:start position:0%
on the web search really the generation
is<00:02:29.480><c> uh</c><00:02:30.000><c> very</c><00:02:30.200><c> very</c><00:02:30.440><c> quick</c><00:02:30.680><c> from</c><00:02:30.920><c> Lama</c><00:02:31.319><c> 3</c>

00:02:32.270 --> 00:02:32.280 align:start position:0%
is uh very very quick from Lama 3
 

00:02:32.280 --> 00:02:35.030 align:start position:0%
is uh very very quick from Lama 3
here<00:02:33.280><c> now</c><00:02:33.480><c> let's</c><00:02:33.640><c> see</c><00:02:34.000><c> one</c><00:02:34.280><c> complex</c><00:02:34.680><c> topic</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
here now let's see one complex topic
 

00:02:35.040 --> 00:02:37.150 align:start position:0%
here now let's see one complex topic
which<00:02:35.160><c> is</c><00:02:35.560><c> language</c><00:02:36.000><c> agent</c><00:02:36.400><c> research</c><00:02:36.879><c> this</c><00:02:37.000><c> is</c>

00:02:37.150 --> 00:02:37.160 align:start position:0%
which is language agent research this is
 

00:02:37.160 --> 00:02:38.830 align:start position:0%
which is language agent research this is
one<00:02:37.239><c> of</c><00:02:37.360><c> my</c><00:02:37.519><c> favorite</c><00:02:37.920><c> things</c><00:02:38.120><c> to</c><00:02:38.319><c> research</c>

00:02:38.830 --> 00:02:38.840 align:start position:0%
one of my favorite things to research
 

00:02:38.840 --> 00:02:40.550 align:start position:0%
one of my favorite things to research
right<00:02:39.040><c> now</c>

00:02:40.550 --> 00:02:40.560 align:start position:0%
right now
 

00:02:40.560 --> 00:02:44.670 align:start position:0%
right now
um<00:02:41.560><c> and</c><00:02:42.000><c> look</c><00:02:42.200><c> at</c><00:02:42.920><c> that</c><00:02:43.920><c> it's</c><00:02:44.159><c> actually</c><00:02:44.440><c> pretty</c>

00:02:44.670 --> 00:02:44.680 align:start position:0%
um and look at that it's actually pretty
 

00:02:44.680 --> 00:02:46.470 align:start position:0%
um and look at that it's actually pretty
good<00:02:44.879><c> I</c><00:02:45.000><c> might</c><00:02:45.400><c> uh</c><00:02:45.519><c> learn</c><00:02:45.879><c> a</c><00:02:46.000><c> thing</c><00:02:46.120><c> or</c><00:02:46.280><c> two</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
good I might uh learn a thing or two
 

00:02:46.480 --> 00:02:50.390 align:start position:0%
good I might uh learn a thing or two
from

00:02:50.390 --> 00:02:50.400 align:start position:0%
 
 

00:02:50.400 --> 00:02:52.630 align:start position:0%
 
here<00:02:51.400><c> all</c><00:02:51.560><c> right</c><00:02:51.760><c> this</c><00:02:51.959><c> gives</c><00:02:52.159><c> me</c><00:02:52.280><c> all</c><00:02:52.440><c> the</c>

00:02:52.630 --> 00:02:52.640 align:start position:0%
here all right this gives me all the
 

00:02:52.640 --> 00:02:55.229 align:start position:0%
here all right this gives me all the
resources<00:02:53.159><c> and</c><00:02:53.319><c> the</c>

00:02:55.229 --> 00:02:55.239 align:start position:0%
resources and the
 

00:02:55.239 --> 00:02:58.309 align:start position:0%
resources and the
references<00:02:56.239><c> so</c><00:02:56.920><c> here</c><00:02:57.080><c> we</c><00:02:57.239><c> are</c><00:02:57.800><c> this</c><00:02:57.920><c> is</c><00:02:58.080><c> your</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
references so here we are this is your
 

00:02:58.319 --> 00:03:00.630 align:start position:0%
references so here we are this is your
own<00:02:58.560><c> research</c><00:02:59.080><c> assistant</c><00:02:59.840><c> under</c><00:03:00.040><c> the</c><00:03:00.159><c> FI</c><00:03:00.440><c> data</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
own research assistant under the FI data
 

00:03:00.640 --> 00:03:03.830 align:start position:0%
own research assistant under the FI data
repo<00:03:01.519><c> in</c><00:03:01.640><c> the</c><00:03:01.840><c> cookbook</c><00:03:02.360><c> llm</c><00:03:02.840><c> Croc</c><00:03:03.200><c> research</c>

00:03:03.830 --> 00:03:03.840 align:start position:0%
repo in the cookbook llm Croc research
 

00:03:03.840 --> 00:03:06.550 align:start position:0%
repo in the cookbook llm Croc research
folder<00:03:04.840><c> it</c><00:03:05.040><c> first</c><00:03:05.319><c> takes</c><00:03:05.560><c> a</c><00:03:05.760><c> complex</c><00:03:06.200><c> topic</c>

00:03:06.550 --> 00:03:06.560 align:start position:0%
folder it first takes a complex topic
 

00:03:06.560 --> 00:03:08.750 align:start position:0%
folder it first takes a complex topic
searches<00:03:07.000><c> the</c><00:03:07.159><c> web</c><00:03:07.400><c> sends</c><00:03:07.640><c> it</c><00:03:07.799><c> to</c><00:03:07.920><c> Lama</c><00:03:08.280><c> 3</c><00:03:08.599><c> and</c>

00:03:08.750 --> 00:03:08.760 align:start position:0%
searches the web sends it to Lama 3 and
 

00:03:08.760 --> 00:03:10.750 align:start position:0%
searches the web sends it to Lama 3 and
produces<00:03:09.159><c> a</c><00:03:09.319><c> research</c><00:03:09.760><c> report</c><00:03:10.440><c> which</c><00:03:10.599><c> it</c>

00:03:10.750 --> 00:03:10.760 align:start position:0%
produces a research report which it
 

00:03:10.760 --> 00:03:13.270 align:start position:0%
produces a research report which it
displays<00:03:11.319><c> using</c><00:03:11.640><c> a</c><00:03:11.799><c> streamlet</c><00:03:12.360><c> application</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
displays using a streamlet application
 

00:03:13.280 --> 00:03:15.110 align:start position:0%
displays using a streamlet application
so<00:03:13.440><c> I</c><00:03:13.560><c> hope</c><00:03:13.680><c> you</c><00:03:13.799><c> like</c><00:03:14.040><c> this</c><00:03:14.200><c> video</c><00:03:14.840><c> thank</c><00:03:15.000><c> you</c>

00:03:15.110 --> 00:03:15.120 align:start position:0%
so I hope you like this video thank you
 

00:03:15.120 --> 00:03:18.519 align:start position:0%
so I hope you like this video thank you
so<00:03:15.239><c> much</c><00:03:15.480><c> have</c><00:03:15.560><c> a</c><00:03:15.680><c> great</c><00:03:16.000><c> day</c>

