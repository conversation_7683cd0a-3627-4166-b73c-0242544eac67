WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:02.750 align:start position:0%
 
and<00:00:00.240><c> welcome</c><00:00:00.420><c> back</c><00:00:00.780><c> so</c><00:00:01.709><c> we</c><00:00:02.190><c> did</c><00:00:02.370><c> in</c><00:00:02.490><c> the</c><00:00:02.580><c> last</c>

00:00:02.750 --> 00:00:02.760 align:start position:0%
and welcome back so we did in the last
 

00:00:02.760 --> 00:00:05.260 align:start position:0%
and welcome back so we did in the last
video<00:00:03.000><c> as</c><00:00:03.270><c> we</c><00:00:03.419><c> actually</c><00:00:03.570><c> merged</c><00:00:04.170><c> everything</c>

00:00:05.260 --> 00:00:05.270 align:start position:0%
video as we actually merged everything
 

00:00:05.270 --> 00:00:09.379 align:start position:0%
video as we actually merged everything
all<00:00:06.270><c> the</c><00:00:07.099><c> the</c><00:00:08.099><c> conflicts</c><00:00:08.730><c> that</c><00:00:08.940><c> were</c><00:00:09.120><c> there</c>

00:00:09.379 --> 00:00:09.389 align:start position:0%
all the the conflicts that were there
 

00:00:09.389 --> 00:00:14.089 align:start position:0%
all the the conflicts that were there
and<00:00:10.580><c> the</c><00:00:11.580><c> conflicts</c><00:00:12.059><c> that</c><00:00:12.240><c> were</c><00:00:12.450><c> there</c><00:00:13.099><c> here's</c>

00:00:14.089 --> 00:00:14.099 align:start position:0%
and the conflicts that were there here's
 

00:00:14.099 --> 00:00:16.070 align:start position:0%
and the conflicts that were there here's
what<00:00:14.309><c> the</c><00:00:14.429><c> app</c><00:00:14.549><c> looks</c><00:00:14.580><c> like</c><00:00:14.940><c> currently</c><00:00:15.599><c> if</c><00:00:15.929><c> I</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
what the app looks like currently if I
 

00:00:16.080 --> 00:00:20.540 align:start position:0%
what the app looks like currently if I
go<00:00:16.320><c> in</c><00:00:16.500><c> as</c><00:00:16.740><c> a</c><00:00:16.980><c> non</c><00:00:17.310><c> user</c><00:00:17.699><c> I</c><00:00:18.560><c> log</c><00:00:19.560><c> in</c><00:00:19.830><c> to</c><00:00:20.520><c> that</c>

00:00:20.540 --> 00:00:20.550 align:start position:0%
go in as a non user I log in to that
 

00:00:20.550 --> 00:00:22.580 align:start position:0%
go in as a non user I log in to that
specific<00:00:21.270><c> host</c><00:00:21.510><c> and</c><00:00:21.869><c> this</c><00:00:21.990><c> is</c><00:00:22.140><c> what</c><00:00:22.289><c> the</c><00:00:22.410><c> view</c>

00:00:22.580 --> 00:00:22.590 align:start position:0%
specific host and this is what the view
 

00:00:22.590 --> 00:00:26.720 align:start position:0%
specific host and this is what the view
is<00:00:22.769><c> it'll</c><00:00:23.130><c> say</c><00:00:23.279><c> login</c><00:00:23.789><c> or</c><00:00:23.970><c> signup</c><00:00:24.269><c> and</c><00:00:25.519><c> so</c><00:00:26.519><c> far</c>

00:00:26.720 --> 00:00:26.730 align:start position:0%
is it'll say login or signup and so far
 

00:00:26.730 --> 00:00:28.670 align:start position:0%
is it'll say login or signup and so far
these<00:00:26.910><c> don't</c><00:00:27.269><c> work</c><00:00:27.420><c> because</c><00:00:27.599><c> I</c><00:00:27.840><c> haven't</c><00:00:28.410><c> added</c>

00:00:28.670 --> 00:00:28.680 align:start position:0%
these don't work because I haven't added
 

00:00:28.680 --> 00:00:30.529 align:start position:0%
these don't work because I haven't added
any<00:00:28.920><c> templates</c><00:00:29.519><c> for</c><00:00:29.849><c> them</c><00:00:30.090><c> yet</c>

00:00:30.529 --> 00:00:30.539 align:start position:0%
any templates for them yet
 

00:00:30.539 --> 00:00:34.280 align:start position:0%
any templates for them yet
and<00:00:31.579><c> let's</c><00:00:32.579><c> see</c><00:00:32.850><c> filter</c><00:00:33.660><c> loads</c><00:00:34.050><c> ever</c>

00:00:34.280 --> 00:00:34.290 align:start position:0%
and let's see filter loads ever
 

00:00:34.290 --> 00:00:38.060 align:start position:0%
and let's see filter loads ever
responded<00:00:35.149><c> search</c><00:00:36.559><c> contact</c><00:00:37.559><c> none</c><00:00:37.829><c> of</c><00:00:37.920><c> them</c>

00:00:38.060 --> 00:00:38.070 align:start position:0%
responded search contact none of them
 

00:00:38.070 --> 00:00:39.530 align:start position:0%
responded search contact none of them
will<00:00:38.219><c> load</c><00:00:38.460><c> because</c><00:00:38.730><c> I</c><00:00:38.820><c> haven't</c><00:00:38.969><c> loaded</c><00:00:39.300><c> the</c>

00:00:39.530 --> 00:00:39.540 align:start position:0%
will load because I haven't loaded the
 

00:00:39.540 --> 00:00:41.660 align:start position:0%
will load because I haven't loaded the
templates<00:00:40.020><c> for</c><00:00:40.320><c> them</c><00:00:40.530><c> currently</c><00:00:41.399><c> the</c><00:00:41.579><c> way</c>

00:00:41.660 --> 00:00:41.670 align:start position:0%
templates for them currently the way
 

00:00:41.670 --> 00:00:43.610 align:start position:0%
templates for them currently the way
that<00:00:41.850><c> the</c><00:00:41.969><c> app</c><00:00:42.180><c> is</c><00:00:42.420><c> structured</c><00:00:42.960><c> is</c><00:00:43.110><c> that</c><00:00:43.140><c> we</c>

00:00:43.610 --> 00:00:43.620 align:start position:0%
that the app is structured is that we
 

00:00:43.620 --> 00:00:47.180 align:start position:0%
that the app is structured is that we
have<00:00:43.910><c> another</c><00:00:44.910><c> app</c><00:00:45.360><c> app</c><00:00:45.750><c> module</c><00:00:46.469><c> angular</c>

00:00:47.180 --> 00:00:47.190 align:start position:0%
have another app app module angular
 

00:00:47.190 --> 00:00:50.029 align:start position:0%
have another app app module angular
natural<00:00:47.850><c> blog</c><00:00:48.149><c> app</c><00:00:48.450><c> we</c><00:00:48.719><c> have</c><00:00:48.750><c> within</c><00:00:49.170><c> the</c><00:00:49.710><c> blog</c>

00:00:50.029 --> 00:00:50.039 align:start position:0%
natural blog app we have within the blog
 

00:00:50.039 --> 00:00:54.350 align:start position:0%
natural blog app we have within the blog
folder<00:00:50.640><c> and</c><00:00:50.879><c> within</c><00:00:51.809><c> public</c><00:00:52.350><c> j/s</c><00:00:52.980><c> and</c><00:00:53.399><c> cordage</c>

00:00:54.350 --> 00:00:54.360 align:start position:0%
folder and within public j/s and cordage
 

00:00:54.360 --> 00:00:56.840 align:start position:0%
folder and within public j/s and cordage
is<00:00:54.719><c> we</c><00:00:55.379><c> have</c><00:00:55.500><c> the</c><00:00:55.680><c> other</c><00:00:55.829><c> app</c><00:00:56.160><c> which</c><00:00:56.430><c> is</c><00:00:56.460><c> pretty</c>

00:00:56.840 --> 00:00:56.850 align:start position:0%
is we have the other app which is pretty
 

00:00:56.850 --> 00:01:00.020 align:start position:0%
is we have the other app which is pretty
much<00:00:57.030><c> the</c><00:00:57.719><c> app</c><00:00:57.899><c> that's</c><00:00:58.260><c> for</c><00:00:58.620><c> the</c><00:00:59.010><c> logged</c><00:00:59.730><c> in</c>

00:01:00.020 --> 00:01:00.030 align:start position:0%
much the app that's for the logged in
 

00:01:00.030 --> 00:01:04.700 align:start position:0%
much the app that's for the logged in
user<00:01:01.280><c> logged</c><00:01:02.280><c> in</c><00:01:02.489><c> user</c><00:01:02.730><c> will</c><00:01:03.390><c> will</c><00:01:03.960><c> use</c><00:01:04.350><c> this</c>

00:01:04.700 --> 00:01:04.710 align:start position:0%
user logged in user will will use this
 

00:01:04.710 --> 00:01:07.760 align:start position:0%
user logged in user will will use this
logic<00:01:05.250><c> which</c><00:01:05.430><c> is</c><00:01:05.580><c> in</c><00:01:05.729><c> this</c><00:01:05.939><c> app</c><00:01:06.180><c> over</c><00:01:07.080><c> here</c><00:01:07.560><c> and</c>

00:01:07.760 --> 00:01:07.770 align:start position:0%
logic which is in this app over here and
 

00:01:07.770 --> 00:01:09.859 align:start position:0%
logic which is in this app over here and
the<00:01:08.130><c> user</c><00:01:08.369><c> that's</c><00:01:08.670><c> kind</c><00:01:08.970><c> of</c><00:01:09.030><c> browsing</c><00:01:09.299><c> from</c>

00:01:09.859 --> 00:01:09.869 align:start position:0%
the user that's kind of browsing from
 

00:01:09.869 --> 00:01:13.219 align:start position:0%
the user that's kind of browsing from
the<00:01:10.020><c> web</c><00:01:10.200><c> will</c><00:01:11.130><c> see</c><00:01:11.430><c> this</c><00:01:12.090><c> chord</c><00:01:12.570><c> is</c>

00:01:13.219 --> 00:01:13.229 align:start position:0%
the web will see this chord is
 

00:01:13.229 --> 00:01:17.929 align:start position:0%
the web will see this chord is
everything<00:01:13.979><c> that's</c><00:01:14.130><c> in</c><00:01:15.200><c> let's</c><00:01:16.200><c> see</c><00:01:16.939><c> view</c>

00:01:17.929 --> 00:01:17.939 align:start position:0%
everything that's in let's see view
 

00:01:17.939 --> 00:01:20.719 align:start position:0%
everything that's in let's see view
which<00:01:18.420><c> bits</c><00:01:18.780><c> in</c><00:01:18.960><c> with</c><00:01:19.140><c> infuse</c><00:01:19.860><c> and</c><00:01:20.189><c> then</c><00:01:20.340><c> this</c>

00:01:20.719 --> 00:01:20.729 align:start position:0%
which bits in with infuse and then this
 

00:01:20.729 --> 00:01:23.690 align:start position:0%
which bits in with infuse and then this
one<00:01:21.000><c> chord</c><00:01:21.450><c> is</c><00:01:21.960><c> so</c><00:01:22.229><c> they're</c><00:01:22.799><c> both</c><00:01:22.920><c> cordage</c><00:01:23.490><c> is</c>

00:01:23.690 --> 00:01:23.700 align:start position:0%
one chord is so they're both cordage is
 

00:01:23.700 --> 00:01:26.359 align:start position:0%
one chord is so they're both cordage is
this<00:01:24.509><c> is</c><00:01:24.689><c> just</c><00:01:24.869><c> a</c><00:01:25.020><c> brand</c><00:01:25.290><c> new</c><00:01:25.530><c> one</c><00:01:25.770><c> this</c><00:01:26.189><c> is</c>

00:01:26.359 --> 00:01:26.369 align:start position:0%
this is just a brand new one this is
 

00:01:26.369 --> 00:01:27.980 align:start position:0%
this is just a brand new one this is
what<00:01:26.520><c> it</c><00:01:26.610><c> looks</c><00:01:26.759><c> like</c><00:01:26.820><c> for</c><00:01:27.119><c> a</c><00:01:27.180><c> user</c><00:01:27.450><c> that's</c><00:01:27.750><c> not</c>

00:01:27.980 --> 00:01:27.990 align:start position:0%
what it looks like for a user that's not
 

00:01:27.990 --> 00:01:29.929 align:start position:0%
what it looks like for a user that's not
logged<00:01:28.320><c> in</c><00:01:28.619><c> it'll</c><00:01:29.040><c> just</c><00:01:29.280><c> have</c><00:01:29.430><c> all</c><00:01:29.670><c> the</c>

00:01:29.929 --> 00:01:29.939 align:start position:0%
logged in it'll just have all the
 

00:01:29.939 --> 00:01:32.450 align:start position:0%
logged in it'll just have all the
articles<00:01:30.479><c> and</c><00:01:31.020><c> for</c><00:01:31.320><c> the</c><00:01:31.439><c> user</c><00:01:31.650><c> that</c><00:01:31.920><c> is</c><00:01:32.189><c> logged</c>

00:01:32.450 --> 00:01:32.460 align:start position:0%
articles and for the user that is logged
 

00:01:32.460 --> 00:01:33.649 align:start position:0%
articles and for the user that is logged
in<00:01:32.640><c> we're</c><00:01:32.820><c> going</c><00:01:32.909><c> to</c><00:01:32.970><c> show</c><00:01:33.180><c> a</c><00:01:33.210><c> little</c><00:01:33.450><c> bit</c><00:01:33.600><c> more</c>

00:01:33.649 --> 00:01:33.659 align:start position:0%
in we're going to show a little bit more
 

00:01:33.659 --> 00:01:36.499 align:start position:0%
in we're going to show a little bit more
information<00:01:34.619><c> and</c><00:01:35.369><c> that</c><00:01:35.549><c> is</c><00:01:35.729><c> the</c><00:01:36.030><c> ability</c><00:01:36.270><c> to</c>

00:01:36.499 --> 00:01:36.509 align:start position:0%
information and that is the ability to
 

00:01:36.509 --> 00:01:41.359 align:start position:0%
information and that is the ability to
add<00:01:36.780><c> photos</c><00:01:37.650><c> and</c><00:01:37.979><c> that</c><00:01:38.490><c> currently</c><00:01:39.119><c> works</c><00:01:40.369><c> and</c>

00:01:41.359 --> 00:01:41.369 align:start position:0%
add photos and that currently works and
 

00:01:41.369 --> 00:01:46.690 align:start position:0%
add photos and that currently works and
the<00:01:41.700><c> ability</c><00:01:42.119><c> to</c><00:01:42.150><c> add</c><00:01:43.280><c> add</c><00:01:44.280><c> the</c><00:01:44.549><c> HTML</c><00:01:45.450><c> as</c><00:01:45.720><c> well</c>

00:01:46.690 --> 00:01:46.700 align:start position:0%
the ability to add add the HTML as well
 

00:01:46.700 --> 00:01:49.850 align:start position:0%
the ability to add add the HTML as well
so<00:01:47.700><c> that's</c><00:01:47.790><c> gonna</c><00:01:48.060><c> be</c><00:01:48.180><c> really</c><00:01:48.360><c> cool</c><00:01:48.810><c> you</c><00:01:49.619><c> can</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
so that's gonna be really cool you can
 

00:01:49.860 --> 00:01:51.200 align:start position:0%
so that's gonna be really cool you can
search<00:01:50.130><c> you</c><00:01:50.369><c> can</c><00:01:50.490><c> add</c><00:01:50.610><c> a</c><00:01:50.670><c> new</c><00:01:50.880><c> blog</c><00:01:51.180><c> post</c>

00:01:51.200 --> 00:01:51.210 align:start position:0%
search you can add a new blog post
 

00:01:51.210 --> 00:01:53.330 align:start position:0%
search you can add a new blog post
you'll<00:01:51.899><c> have</c><00:01:52.049><c> an</c><00:01:52.170><c> admin</c><00:01:52.619><c> area</c><00:01:52.799><c> with</c>

00:01:53.330 --> 00:01:53.340 align:start position:0%
you'll have an admin area with
 

00:01:53.340 --> 00:01:56.510 align:start position:0%
you'll have an admin area with
information<00:01:54.180><c> about</c><00:01:54.390><c> all</c><00:01:54.869><c> of</c><00:01:54.930><c> your</c><00:01:55.350><c> of</c><00:01:56.100><c> your</c>

00:01:56.510 --> 00:01:56.520 align:start position:0%
information about all of your of your
 

00:01:56.520 --> 00:01:59.420 align:start position:0%
information about all of your of your
blog<00:01:56.759><c> posts</c><00:01:57.360><c> how</c><00:01:58.170><c> they're</c><00:01:58.469><c> maybe</c><00:01:59.130><c> you</c><00:01:59.280><c> have</c>

00:01:59.420 --> 00:01:59.430 align:start position:0%
blog posts how they're maybe you have
 

00:01:59.430 --> 00:02:01.730 align:start position:0%
blog posts how they're maybe you have
maybe<00:01:59.729><c> some</c><00:01:59.909><c> analytics</c><00:02:00.540><c> about</c><00:02:00.719><c> it</c><00:02:01.020><c> eventually</c>

00:02:01.730 --> 00:02:01.740 align:start position:0%
maybe some analytics about it eventually
 

00:02:01.740 --> 00:02:04.910 align:start position:0%
maybe some analytics about it eventually
we'll<00:02:01.950><c> get</c><00:02:02.100><c> there</c><00:02:02.420><c> and</c><00:02:03.420><c> let's</c><00:02:03.719><c> see</c><00:02:03.840><c> and</c><00:02:04.290><c> you</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
we'll get there and let's see and you
 

00:02:04.920 --> 00:02:06.830 align:start position:0%
we'll get there and let's see and you
also<00:02:05.070><c> see</c><00:02:05.399><c> over</c><00:02:05.640><c> here</c><00:02:05.670><c> what</c><00:02:06.060><c> your</c><00:02:06.240><c> blog</c><00:02:06.450><c> looks</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
also see over here what your blog looks
 

00:02:06.840 --> 00:02:09.740 align:start position:0%
also see over here what your blog looks
like<00:02:07.140><c> even</c><00:02:07.710><c> if</c><00:02:07.829><c> you're</c><00:02:07.979><c> a</c><00:02:08.009><c> logged</c><00:02:08.310><c> in</c><00:02:08.520><c> user</c><00:02:08.750><c> and</c>

00:02:09.740 --> 00:02:09.750 align:start position:0%
like even if you're a logged in user and
 

00:02:09.750 --> 00:02:11.270 align:start position:0%
like even if you're a logged in user and
it's<00:02:09.899><c> pretty</c><00:02:10.110><c> good</c><00:02:10.200><c> will</c><00:02:10.560><c> have</c><00:02:10.709><c> the</c><00:02:10.830><c> contact</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
it's pretty good will have the contact
 

00:02:11.280 --> 00:02:12.920 align:start position:0%
it's pretty good will have the contact
page<00:02:11.580><c> will</c><00:02:11.790><c> have</c><00:02:11.910><c> to</c><00:02:12.030><c> put</c><00:02:12.239><c> that</c><00:02:12.390><c> for</c><00:02:12.629><c> the</c>

00:02:12.920 --> 00:02:12.930 align:start position:0%
page will have to put that for the
 

00:02:12.930 --> 00:02:15.470 align:start position:0%
page will have to put that for the
laughing<00:02:13.500><c> user</c><00:02:13.799><c> which</c><00:02:14.280><c> is</c><00:02:14.310><c> this</c><00:02:14.849><c> view</c><00:02:15.150><c> will</c><00:02:15.329><c> be</c>

00:02:15.470 --> 00:02:15.480 align:start position:0%
laughing user which is this view will be
 

00:02:15.480 --> 00:02:18.770 align:start position:0%
laughing user which is this view will be
here<00:02:15.750><c> as</c><00:02:15.959><c> well</c><00:02:16.260><c> and</c><00:02:17.329><c> yeah</c><00:02:18.329><c> that's</c><00:02:18.540><c> pretty</c><00:02:18.599><c> much</c>

00:02:18.770 --> 00:02:18.780 align:start position:0%
here as well and yeah that's pretty much
 

00:02:18.780 --> 00:02:23.899 align:start position:0%
here as well and yeah that's pretty much
it<00:02:19.079><c> so</c><00:02:19.109><c> far</c><00:02:19.560><c> and</c><00:02:20.450><c> so</c><00:02:21.450><c> we</c><00:02:21.599><c> were</c><00:02:21.689><c> able</c><00:02:21.780><c> to</c><00:02:22.700><c> do</c><00:02:23.700><c> all</c>

00:02:23.899 --> 00:02:23.909 align:start position:0%
it so far and so we were able to do all
 

00:02:23.909 --> 00:02:26.300 align:start position:0%
it so far and so we were able to do all
the<00:02:24.090><c> changes</c><00:02:24.659><c> of</c><00:02:24.900><c> the</c><00:02:25.230><c> merge</c><00:02:25.439><c> conflicts</c><00:02:26.159><c> and</c>

00:02:26.300 --> 00:02:26.310 align:start position:0%
the changes of the merge conflicts and
 

00:02:26.310 --> 00:02:28.550 align:start position:0%
the changes of the merge conflicts and
finally<00:02:26.730><c> we</c><00:02:26.879><c> pushed</c><00:02:27.150><c> everything</c><00:02:27.510><c> as</c><00:02:27.810><c> well</c><00:02:28.049><c> and</c>

00:02:28.550 --> 00:02:28.560 align:start position:0%
finally we pushed everything as well and
 

00:02:28.560 --> 00:02:30.979 align:start position:0%
finally we pushed everything as well and
that's<00:02:29.310><c> what</c><00:02:29.519><c> the</c><00:02:29.639><c> app</c><00:02:29.760><c> is</c><00:02:30.000><c> moving</c><00:02:30.599><c> towards</c>

00:02:30.979 --> 00:02:30.989 align:start position:0%
that's what the app is moving towards
 

00:02:30.989 --> 00:02:34.129 align:start position:0%
that's what the app is moving towards
too<00:02:31.290><c> so</c><00:02:31.560><c> feel</c><00:02:31.799><c> free</c><00:02:31.829><c> to</c><00:02:32.689><c> comment</c><00:02:33.689><c> if</c><00:02:34.019><c> you</c><00:02:34.109><c> have</c>

00:02:34.129 --> 00:02:34.139 align:start position:0%
too so feel free to comment if you have
 

00:02:34.139 --> 00:02:39.679 align:start position:0%
too so feel free to comment if you have
any<00:02:34.379><c> questions</c><00:02:34.859><c> about</c><00:02:35.810><c> how</c><00:02:36.810><c> we</c><00:02:36.870><c> did</c><00:02:37.170><c> that</c><00:02:38.689><c> I'll</c>

00:02:39.679 --> 00:02:39.689 align:start position:0%
any questions about how we did that I'll
 

00:02:39.689 --> 00:02:41.209 align:start position:0%
any questions about how we did that I'll
quickly<00:02:39.959><c> say</c><00:02:40.290><c> a</c><00:02:40.319><c> word</c><00:02:40.560><c> or</c><00:02:40.680><c> two</c><00:02:40.709><c> about</c><00:02:40.829><c> how</c>

00:02:41.209 --> 00:02:41.219 align:start position:0%
quickly say a word or two about how
 

00:02:41.219 --> 00:02:48.589 align:start position:0%
quickly say a word or two about how
angular<00:02:41.609><c> how</c><00:02:42.569><c> the</c><00:02:43.549><c> s3</c><00:02:44.549><c> works</c><00:02:45.139><c> s3</c><00:02:46.139><c> if</c><00:02:47.099><c> I</c><00:02:47.400><c> go</c><00:02:47.639><c> this</c>

00:02:48.589 --> 00:02:48.599 align:start position:0%
angular how the s3 works s3 if I go this
 

00:02:48.599 --> 00:02:50.270 align:start position:0%
angular how the s3 works s3 if I go this
is<00:02:48.659><c> pretty</c><00:02:48.959><c> much</c><00:02:49.049><c> the</c><00:02:49.169><c> files</c><00:02:49.530><c> that</c><00:02:49.709><c> are</c><00:02:49.950><c> in</c><00:02:50.069><c> s3</c>

00:02:50.270 --> 00:02:50.280 align:start position:0%
is pretty much the files that are in s3
 

00:02:50.280 --> 00:02:52.819 align:start position:0%
is pretty much the files that are in s3
it's<00:02:50.939><c> for</c><00:02:51.329><c> it's</c><00:02:51.989><c> within</c><00:02:52.169><c> the</c><00:02:52.319><c> public</c><00:02:52.650><c> folder</c>

00:02:52.819 --> 00:02:52.829 align:start position:0%
it's for it's within the public folder
 

00:02:52.829 --> 00:02:55.550 align:start position:0%
it's for it's within the public folder
j/s<00:02:53.489><c> and</c><00:02:53.970><c> that's</c><00:02:54.239><c> where</c><00:02:54.450><c> all</c><00:02:54.599><c> the</c><00:02:54.840><c> logic</c><00:02:55.409><c> goes</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
j/s and that's where all the logic goes
 

00:02:55.560 --> 00:02:59.539 align:start position:0%
j/s and that's where all the logic goes
for<00:02:55.919><c> the</c><00:02:56.010><c> logged</c><00:02:56.250><c> in</c><00:02:56.489><c> user</c><00:02:56.870><c> and</c><00:02:57.870><c> it's</c><00:02:57.989><c> got</c><00:02:58.459><c> C</c><00:02:59.459><c> is</c>

00:02:59.539 --> 00:02:59.549 align:start position:0%
for the logged in user and it's got C is
 

00:02:59.549 --> 00:03:05.110 align:start position:0%
for the logged in user and it's got C is
it<00:02:59.669><c> filters</c><00:03:00.120><c> no</c><00:03:01.639><c> image</c><00:03:02.639><c> Jas</c><00:03:03.269><c> yeah</c><00:03:03.629><c> koala</c><00:03:04.079><c> CMS</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
it filters no image Jas yeah koala CMS
 

00:03:05.120 --> 00:03:08.959 align:start position:0%
it filters no image Jas yeah koala CMS
and<00:03:06.120><c> PT</c><00:03:07.019><c> image</c><00:03:07.319><c> uploader</c><00:03:07.799><c> this</c><00:03:08.280><c> is</c><00:03:08.430><c> also</c><00:03:08.790><c> it</c>

00:03:08.959 --> 00:03:08.969 align:start position:0%
and PT image uploader this is also it
 

00:03:08.969 --> 00:03:10.459 align:start position:0%
and PT image uploader this is also it
okay<00:03:09.419><c> so</c><00:03:09.480><c> as</c><00:03:09.689><c> you</c><00:03:09.750><c> can</c><00:03:09.959><c> see</c><00:03:10.019><c> this</c><00:03:10.260><c> is</c><00:03:10.439><c> a</c>

00:03:10.459 --> 00:03:10.469 align:start position:0%
okay so as you can see this is a
 

00:03:10.469 --> 00:03:13.729 align:start position:0%
okay so as you can see this is a
directive<00:03:11.299><c> the</c><00:03:12.299><c> directive</c><00:03:12.810><c> is</c><00:03:12.989><c> called</c><00:03:13.260><c> PT</c>

00:03:13.729 --> 00:03:13.739 align:start position:0%
directive the directive is called PT
 

00:03:13.739 --> 00:03:16.309 align:start position:0%
directive the directive is called PT
image<00:03:14.069><c> uploader</c><00:03:14.579><c> and</c><00:03:14.879><c> that</c><00:03:15.359><c> was</c><00:03:15.569><c> added</c><00:03:16.049><c> into</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
image uploader and that was added into
 

00:03:16.319 --> 00:03:21.319 align:start position:0%
image uploader and that was added into
our<00:03:16.979><c> community</c><00:03:18.650><c> folder</c><00:03:19.650><c> a</c><00:03:19.859><c> community</c><00:03:20.699><c> that</c><00:03:20.970><c> EJ</c>

00:03:21.319 --> 00:03:21.329 align:start position:0%
our community folder a community that EJ
 

00:03:21.329 --> 00:03:23.360 align:start position:0%
our community folder a community that EJ
has<00:03:21.449><c> file</c><00:03:21.720><c> and</c><00:03:21.959><c> that's</c><00:03:22.019><c> PT</c><00:03:22.500><c> image</c><00:03:22.829><c> uploader</c>

00:03:23.360 --> 00:03:23.370 align:start position:0%
has file and that's PT image uploader
 

00:03:23.370 --> 00:03:26.750 align:start position:0%
has file and that's PT image uploader
it's<00:03:23.729><c> it's</c><00:03:24.859><c> spinning</c><00:03:25.859><c> called</c><00:03:26.310><c> the</c><00:03:26.430><c> angular</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
it's it's spinning called the angular
 

00:03:26.760 --> 00:03:28.849 align:start position:0%
it's it's spinning called the angular
directive<00:03:27.629><c> and</c><00:03:27.780><c> every</c><00:03:27.989><c> time</c><00:03:28.139><c> that</c><00:03:28.169><c> the</c><00:03:28.439><c> HTML</c>

00:03:28.849 --> 00:03:28.859 align:start position:0%
directive and every time that the HTML
 

00:03:28.859 --> 00:03:30.740 align:start position:0%
directive and every time that the HTML
starts<00:03:29.430><c> reading</c><00:03:29.669><c> and</c><00:03:29.970><c> sees</c><00:03:30.150><c> PT</c><00:03:30.510><c> image</c>

00:03:30.740 --> 00:03:30.750 align:start position:0%
starts reading and sees PT image
 

00:03:30.750 --> 00:03:32.740 align:start position:0%
starts reading and sees PT image
uploader<00:03:31.169><c> is</c><00:03:31.319><c> gonna</c><00:03:31.500><c> automatically</c><00:03:32.040><c> go</c><00:03:32.340><c> to</c>

00:03:32.740 --> 00:03:32.750 align:start position:0%
uploader is gonna automatically go to
 

00:03:32.750 --> 00:03:35.869 align:start position:0%
uploader is gonna automatically go to
this<00:03:33.750><c> PT</c><00:03:34.260><c> image</c><00:03:34.470><c> upload</c><00:03:34.859><c> of</c><00:03:35.010><c> the</c><00:03:35.099><c> directive</c><00:03:35.669><c> as</c>

00:03:35.869 --> 00:03:35.879 align:start position:0%
this PT image upload of the directive as
 

00:03:35.879 --> 00:03:38.300 align:start position:0%
this PT image upload of the directive as
you<00:03:36.000><c> can</c><00:03:36.150><c> see</c><00:03:36.329><c> over</c><00:03:36.599><c> here</c><00:03:36.629><c> it's</c><00:03:36.989><c> camelcase</c><00:03:37.739><c> and</c>

00:03:38.300 --> 00:03:38.310 align:start position:0%
you can see over here it's camelcase and
 

00:03:38.310 --> 00:03:43.390 align:start position:0%
you can see over here it's camelcase and
over<00:03:38.489><c> there</c><00:03:38.669><c> it's</c><00:03:38.849><c> -</c><00:03:39.209><c> case</c><00:03:40.459><c> and</c><00:03:41.900><c> it's</c><00:03:42.900><c> here</c>

00:03:43.390 --> 00:03:43.400 align:start position:0%
over there it's - case and it's here
 

00:03:43.400 --> 00:03:49.819 align:start position:0%
over there it's - case and it's here
okay<00:03:44.430><c> we</c><00:03:45.180><c> haven't</c><00:03:46.430><c> we</c><00:03:47.430><c> made</c><00:03:47.609><c> an</c><00:03:47.729><c> H</c><00:03:48.560><c> HTTP</c><00:03:49.560><c> call</c>

00:03:49.819 --> 00:03:49.829 align:start position:0%
okay we haven't we made an H HTTP call
 

00:03:49.829 --> 00:03:56.679 align:start position:0%
okay we haven't we made an H HTTP call
to<00:03:51.139><c> /s</c><00:03:52.139><c> three</c><00:03:52.680><c> creds</c><00:03:53.189><c> and</c><00:03:53.549><c> when</c><00:03:54.540><c> we</c><00:03:54.659><c> do</c><00:03:54.810><c> that</c><00:03:55.129><c> we</c>

00:03:56.679 --> 00:03:56.689 align:start position:0%
to /s three creds and when we do that we
 

00:03:56.689 --> 00:04:00.229 align:start position:0%
to /s three creds and when we do that we
we<00:03:57.689><c> go</c><00:03:57.870><c> back</c><00:03:58.109><c> into</c><00:03:58.349><c> the</c><00:03:58.590><c> server</c><00:03:58.949><c> and</c><00:03:59.759><c> as</c><00:04:00.090><c> you</c>

00:04:00.229 --> 00:04:00.239 align:start position:0%
we go back into the server and as you
 

00:04:00.239 --> 00:04:03.770 align:start position:0%
we go back into the server and as you
can<00:04:00.389><c> see</c><00:04:00.540><c> the</c><00:04:00.689><c> server</c><00:04:01.019><c> badge</c><00:04:01.500><c> is</c><00:04:02.540><c> I'm</c><00:04:03.540><c> not</c>

00:04:03.770 --> 00:04:03.780 align:start position:0%
can see the server badge is I'm not
 

00:04:03.780 --> 00:04:07.879 align:start position:0%
can see the server badge is I'm not
sorry<00:04:04.019><c> we</c><00:04:04.259><c> go</c><00:04:04.409><c> to</c><00:04:04.470><c> routes</c><00:04:05.129><c> at</c><00:04:06.259><c> routes</c><00:04:07.259><c> that</c><00:04:07.590><c> J</c>

00:04:07.879 --> 00:04:07.889 align:start position:0%
sorry we go to routes at routes that J
 

00:04:07.889 --> 00:04:12.559 align:start position:0%
sorry we go to routes at routes that J
us<00:04:08.090><c> rest</c><00:04:09.090><c> rjs</c><00:04:09.889><c> will</c><00:04:10.889><c> go</c><00:04:11.040><c> to</c><00:04:11.099><c> /s</c><00:04:12.030><c> three</c><00:04:12.299><c> creds</c>

00:04:12.559 --> 00:04:12.569 align:start position:0%
us rest rjs will go to /s three creds
 

00:04:12.569 --> 00:04:14.479 align:start position:0%
us rest rjs will go to /s three creds
and<00:04:12.720><c> it</c><00:04:12.870><c> will</c><00:04:12.989><c> pull</c><00:04:13.229><c> it</c><00:04:13.409><c> processed</c><00:04:13.889><c> at</c><00:04:14.069><c> env</c>

00:04:14.479 --> 00:04:14.489 align:start position:0%
and it will pull it processed at env
 

00:04:14.489 --> 00:04:16.279 align:start position:0%
and it will pull it processed at env
that's<00:04:14.699><c> because</c><00:04:15.000><c> the</c><00:04:15.180><c> process</c><00:04:15.629><c> that</c><00:04:15.810><c> E&amp;V</c><00:04:16.199><c> is</c>

00:04:16.279 --> 00:04:16.289 align:start position:0%
that's because the process that E&amp;V is
 

00:04:16.289 --> 00:04:18.229 align:start position:0%
that's because the process that E&amp;V is
everything<00:04:16.680><c> in</c><00:04:16.799><c> the</c><00:04:16.889><c> dot</c><00:04:17.130><c> E</c><00:04:17.159><c> and</c><00:04:17.430><c> V</c><00:04:17.669><c> file</c><00:04:17.970><c> and</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
everything in the dot E and V file and
 

00:04:18.239 --> 00:04:19.759 align:start position:0%
everything in the dot E and V file and
that<00:04:18.359><c> can</c><00:04:18.509><c> only</c><00:04:18.659><c> be</c><00:04:18.780><c> viewed</c><00:04:19.139><c> from</c><00:04:19.349><c> the</c><00:04:19.500><c> browser</c>

00:04:19.759 --> 00:04:19.769 align:start position:0%
that can only be viewed from the browser
 

00:04:19.769 --> 00:04:21.469 align:start position:0%
that can only be viewed from the browser
side<00:04:20.190><c> of</c><00:04:20.340><c> things</c><00:04:20.579><c> that's</c><00:04:20.849><c> why</c><00:04:20.969><c> we're</c><00:04:21.120><c> making</c><00:04:21.419><c> a</c>

00:04:21.469 --> 00:04:21.479 align:start position:0%
side of things that's why we're making a
 

00:04:21.479 --> 00:04:25.570 align:start position:0%
side of things that's why we're making a
call<00:04:21.750><c> from</c><00:04:22.740><c> the</c><00:04:23.010><c> controller</c><00:04:23.520><c> which</c><00:04:24.120><c> is</c>

00:04:25.570 --> 00:04:25.580 align:start position:0%
call from the controller which is
 

00:04:25.580 --> 00:04:28.000 align:start position:0%
call from the controller which is
the<00:04:25.880><c> controller</c><00:04:26.330><c> of</c><00:04:26.480><c> the</c><00:04:26.930><c> PT</c><00:04:27.380><c> image</c><00:04:27.650><c> uploader</c>

00:04:28.000 --> 00:04:28.010 align:start position:0%
the controller of the PT image uploader
 

00:04:28.010 --> 00:04:30.220 align:start position:0%
the controller of the PT image uploader
directive<00:04:28.610><c> is</c><00:04:28.700><c> making</c><00:04:29.090><c> an</c><00:04:29.180><c> HTTP</c><00:04:29.720><c> call</c><00:04:29.960><c> and</c>

00:04:30.220 --> 00:04:30.230 align:start position:0%
directive is making an HTTP call and
 

00:04:30.230 --> 00:04:32.800 align:start position:0%
directive is making an HTTP call and
that<00:04:30.770><c> gets</c><00:04:30.980><c> the</c><00:04:31.190><c> cred</c><00:04:31.490><c> the</c><00:04:31.910><c> creds</c><00:04:32.330><c> and</c><00:04:32.540><c> then</c><00:04:32.690><c> it</c>

00:04:32.800 --> 00:04:32.810 align:start position:0%
that gets the cred the creds and then it
 

00:04:32.810 --> 00:04:34.840 align:start position:0%
that gets the cred the creds and then it
also<00:04:32.930><c> does</c><00:04:33.380><c> compression</c><00:04:34.070><c> automatically</c><00:04:34.730><c> on</c>

00:04:34.840 --> 00:04:34.850 align:start position:0%
also does compression automatically on
 

00:04:34.850 --> 00:04:37.530 align:start position:0%
also does compression automatically on
the<00:04:34.940><c> image</c><00:04:35.240><c> before</c><00:04:35.420><c> it's</c><00:04:35.720><c> uploaded</c><00:04:36.380><c> to</c><00:04:36.560><c> AWS</c><00:04:37.250><c> s3</c>

00:04:37.530 --> 00:04:37.540 align:start position:0%
the image before it's uploaded to AWS s3
 

00:04:37.540 --> 00:04:39.580 align:start position:0%
the image before it's uploaded to AWS s3
so<00:04:38.540><c> if</c><00:04:38.600><c> you</c><00:04:38.690><c> have</c><00:04:38.780><c> any</c><00:04:38.900><c> questions</c><00:04:39.320><c> about</c><00:04:39.440><c> that</c>

00:04:39.580 --> 00:04:39.590 align:start position:0%
so if you have any questions about that
 

00:04:39.590 --> 00:04:42.820 align:start position:0%
so if you have any questions about that
feel<00:04:39.830><c> free</c><00:04:40.280><c> to</c><00:04:40.310><c> go</c><00:04:40.490><c> ahead</c><00:04:40.520><c> and</c><00:04:40.880><c> ask</c><00:04:41.000><c> me</c><00:04:41.330><c> I</c><00:04:42.320><c> also</c>

00:04:42.820 --> 00:04:42.830 align:start position:0%
feel free to go ahead and ask me I also
 

00:04:42.830 --> 00:04:45.160 align:start position:0%
feel free to go ahead and ask me I also
put<00:04:43.580><c> this</c><00:04:43.880><c> link</c><00:04:44.240><c> in</c><00:04:44.390><c> the</c><00:04:44.510><c> description</c><00:04:44.990><c> which</c>

00:04:45.160 --> 00:04:45.170 align:start position:0%
put this link in the description which
 

00:04:45.170 --> 00:04:48.280 align:start position:0%
put this link in the description which
is<00:04:45.200><c> just</c><00:04:45.680><c> that</c><00:04:45.890><c> at</c><00:04:46.100><c> the</c><00:04:46.280><c> angular</c><00:04:46.700><c> s3</c><00:04:47.390><c> AWS</c><00:04:48.080><c> s3</c>

00:04:48.280 --> 00:04:48.290 align:start position:0%
is just that at the angular s3 AWS s3
 

00:04:48.290 --> 00:04:51.250 align:start position:0%
is just that at the angular s3 AWS s3
logic<00:04:49.220><c> before</c><00:04:49.850><c> it</c><00:04:50.000><c> was</c><00:04:50.180><c> merged</c><00:04:50.420><c> merged</c><00:04:51.020><c> into</c>

00:04:51.250 --> 00:04:51.260 align:start position:0%
logic before it was merged merged into
 

00:04:51.260 --> 00:04:55.000 align:start position:0%
logic before it was merged merged into
the<00:04:52.190><c> koala</c><00:04:52.760><c> CMS</c><00:04:53.510><c> project</c><00:04:53.810><c> so</c><00:04:54.290><c> hope</c><00:04:54.650><c> to</c><00:04:54.680><c> see</c><00:04:54.830><c> you</c>

00:04:55.000 --> 00:04:55.010 align:start position:0%
the koala CMS project so hope to see you
 

00:04:55.010 --> 00:04:58.930 align:start position:0%
the koala CMS project so hope to see you
in<00:04:55.130><c> the</c><00:04:55.250><c> next</c><00:04:55.610><c> video</c><00:04:55.910><c> and</c><00:04:56.150><c> have</c><00:04:56.510><c> a</c><00:04:56.540><c> good</c><00:04:56.780><c> one</c>

