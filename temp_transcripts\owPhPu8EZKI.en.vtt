WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:02.550 align:start position:0%
 
so<00:00:00.280><c> today</c><00:00:00.799><c> is</c><00:00:01.000><c> day</c><00:00:01.280><c> 29</c><00:00:01.839><c> of</c><00:00:01.959><c> the</c><00:00:02.040><c> 31-day</c>

00:00:02.550 --> 00:00:02.560 align:start position:0%
so today is day 29 of the 31-day
 

00:00:02.560 --> 00:00:04.150 align:start position:0%
so today is day 29 of the 31-day
challenge<00:00:03.000><c> but</c><00:00:03.120><c> today</c><00:00:03.320><c> I</c><00:00:03.360><c> want</c><00:00:03.480><c> to</c><00:00:03.560><c> show</c><00:00:03.760><c> you</c><00:00:03.959><c> a</c>

00:00:04.150 --> 00:00:04.160 align:start position:0%
challenge but today I want to show you a
 

00:00:04.160 --> 00:00:05.829 align:start position:0%
challenge but today I want to show you a
web<00:00:04.480><c> Surfer</c><00:00:04.960><c> agent</c><00:00:05.319><c> that</c><00:00:05.400><c> was</c><00:00:05.520><c> added</c><00:00:05.759><c> to</c>

00:00:05.829 --> 00:00:05.839 align:start position:0%
web Surfer agent that was added to
 

00:00:05.839 --> 00:00:07.829 align:start position:0%
web Surfer agent that was added to
autogen<00:00:06.319><c> a</c><00:00:06.440><c> little</c><00:00:06.600><c> while</c><00:00:06.799><c> ago</c><00:00:07.200><c> and</c><00:00:07.560><c> as</c><00:00:07.680><c> the</c>

00:00:07.829 --> 00:00:07.839 align:start position:0%
autogen a little while ago and as the
 

00:00:07.839 --> 00:00:10.830 align:start position:0%
autogen a little while ago and as the
name<00:00:08.240><c> suggest</c><00:00:09.040><c> we</c><00:00:09.200><c> will</c><00:00:09.360><c> be</c><00:00:09.519><c> surfing</c><00:00:10.200><c> the</c><00:00:10.480><c> web</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
name suggest we will be surfing the web
 

00:00:10.840 --> 00:00:13.789 align:start position:0%
name suggest we will be surfing the web
using<00:00:11.280><c> llm</c><00:00:11.840><c> and</c><00:00:12.040><c> having</c><00:00:12.400><c> human</c><00:00:12.719><c> feedback</c><00:00:13.440><c> to</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
using llm and having human feedback to
 

00:00:13.799 --> 00:00:15.310 align:start position:0%
using llm and having human feedback to
what<00:00:13.920><c> we</c><00:00:14.000><c> want</c><00:00:14.160><c> to</c><00:00:14.280><c> do</c><00:00:14.519><c> next</c><00:00:14.879><c> now</c><00:00:15.000><c> the</c><00:00:15.080><c> way</c><00:00:15.200><c> this</c>

00:00:15.310 --> 00:00:15.320 align:start position:0%
what we want to do next now the way this
 

00:00:15.320 --> 00:00:18.150 align:start position:0%
what we want to do next now the way this
is<00:00:15.440><c> set</c><00:00:15.599><c> up</c><00:00:15.799><c> is</c><00:00:16.000><c> you</c><00:00:16.160><c> will</c><00:00:16.440><c> need</c><00:00:16.760><c> a</c><00:00:17.000><c> bing</c><00:00:17.720><c> API</c>

00:00:18.150 --> 00:00:18.160 align:start position:0%
is set up is you will need a bing API
 

00:00:18.160 --> 00:00:19.630 align:start position:0%
is set up is you will need a bing API
key<00:00:18.439><c> that</c><00:00:18.560><c> you</c><00:00:18.680><c> see</c><00:00:18.960><c> here</c><00:00:19.240><c> I'll</c><00:00:19.400><c> have</c><00:00:19.520><c> the</c>

00:00:19.630 --> 00:00:19.640 align:start position:0%
key that you see here I'll have the
 

00:00:19.640 --> 00:00:21.150 align:start position:0%
key that you see here I'll have the
information<00:00:20.080><c> on</c><00:00:20.359><c> how</c><00:00:20.519><c> you</c><00:00:20.600><c> can</c><00:00:20.760><c> kind</c><00:00:20.880><c> of</c><00:00:20.960><c> set</c>

00:00:21.150 --> 00:00:21.160 align:start position:0%
information on how you can kind of set
 

00:00:21.160 --> 00:00:22.509 align:start position:0%
information on how you can kind of set
that<00:00:21.279><c> up</c><00:00:21.439><c> it</c><00:00:21.560><c> took</c><00:00:21.680><c> me</c><00:00:21.800><c> about</c><00:00:22.000><c> 5</c><00:00:22.160><c> minutes</c><00:00:22.400><c> to</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
that up it took me about 5 minutes to
 

00:00:22.519 --> 00:00:23.830 align:start position:0%
that up it took me about 5 minutes to
set<00:00:22.720><c> up</c><00:00:22.920><c> but</c><00:00:23.039><c> let's</c><00:00:23.199><c> just</c><00:00:23.320><c> go</c><00:00:23.480><c> through</c><00:00:23.680><c> this</c>

00:00:23.830 --> 00:00:23.840 align:start position:0%
set up but let's just go through this
 

00:00:23.840 --> 00:00:25.390 align:start position:0%
set up but let's just go through this
and<00:00:24.000><c> I'll</c><00:00:24.119><c> show</c><00:00:24.279><c> you</c><00:00:24.480><c> examples</c><00:00:24.920><c> and</c><00:00:25.160><c> what</c><00:00:25.279><c> it</c>

00:00:25.390 --> 00:00:25.400 align:start position:0%
and I'll show you examples and what it
 

00:00:25.400 --> 00:00:27.870 align:start position:0%
and I'll show you examples and what it
does<00:00:25.760><c> so</c><00:00:25.960><c> first</c><00:00:26.160><c> off</c><00:00:26.400><c> anytime</c><00:00:26.720><c> you</c><00:00:26.840><c> see</c><00:00:27.560><c> autog</c>

00:00:27.870 --> 00:00:27.880 align:start position:0%
does so first off anytime you see autog
 

00:00:27.880 --> 00:00:30.390 align:start position:0%
does so first off anytime you see autog
gen.<00:00:28.160><c> agent.</c><00:00:28.920><c> contrib</c><00:00:29.560><c> that</c><00:00:29.679><c> means</c><00:00:30.039><c> it</c><00:00:30.119><c> was</c><00:00:30.240><c> a</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
gen. agent. contrib that means it was a
 

00:00:30.400 --> 00:00:32.910 align:start position:0%
gen. agent. contrib that means it was a
contribution<00:00:31.039><c> so</c><00:00:31.279><c> somebody</c><00:00:32.119><c> uh</c><00:00:32.279><c> created</c><00:00:32.680><c> this</c>

00:00:32.910 --> 00:00:32.920 align:start position:0%
contribution so somebody uh created this
 

00:00:32.920 --> 00:00:35.310 align:start position:0%
contribution so somebody uh created this
agent<00:00:33.600><c> as</c><00:00:34.120><c> a</c><00:00:34.320><c> part</c><00:00:34.520><c> of</c><00:00:34.640><c> the</c><00:00:34.800><c> conversible</c>

00:00:35.310 --> 00:00:35.320 align:start position:0%
agent as a part of the conversible
 

00:00:35.320 --> 00:00:37.030 align:start position:0%
agent as a part of the conversible
agents<00:00:35.680><c> from</c><00:00:35.840><c> autogen</c><00:00:36.399><c> and</c><00:00:36.520><c> this</c><00:00:36.600><c> one</c><00:00:36.719><c> is</c><00:00:36.840><c> just</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
agents from autogen and this one is just
 

00:00:37.040 --> 00:00:38.430 align:start position:0%
agents from autogen and this one is just
slightly<00:00:37.480><c> different</c><00:00:37.800><c> so</c><00:00:38.040><c> let's</c><00:00:38.160><c> just</c><00:00:38.280><c> go</c>

00:00:38.430 --> 00:00:38.440 align:start position:0%
slightly different so let's just go
 

00:00:38.440 --> 00:00:40.470 align:start position:0%
slightly different so let's just go
through<00:00:38.640><c> this</c><00:00:39.040><c> we</c><00:00:39.160><c> have</c><00:00:39.239><c> the</c><00:00:39.360><c> llm</c><00:00:39.760><c> config</c><00:00:40.360><c> and</c>

00:00:40.470 --> 00:00:40.480 align:start position:0%
through this we have the llm config and
 

00:00:40.480 --> 00:00:42.470 align:start position:0%
through this we have the llm config and
we<00:00:40.600><c> have</c><00:00:40.719><c> the</c><00:00:40.840><c> summarizer</c><00:00:41.520><c> llm</c><00:00:42.000><c> config</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
we have the summarizer llm config
 

00:00:42.480 --> 00:00:44.630 align:start position:0%
we have the summarizer llm config
because<00:00:42.680><c> it</c><00:00:42.800><c> takes</c><00:00:43.120><c> two</c><00:00:43.559><c> different</c><00:00:43.920><c> configs</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
because it takes two different configs
 

00:00:44.640 --> 00:00:46.910 align:start position:0%
because it takes two different configs
so<00:00:44.800><c> we</c><00:00:44.879><c> say</c><00:00:45.120><c> web</c><00:00:45.360><c> Surfer</c><00:00:46.039><c> equals</c><00:00:46.520><c> the</c><00:00:46.680><c> web</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
so we say web Surfer equals the web
 

00:00:46.920 --> 00:00:49.430 align:start position:0%
so we say web Surfer equals the web
Surfer<00:00:47.440><c> agent</c><00:00:47.879><c> so</c><00:00:48.079><c> we</c><00:00:48.320><c> again</c><00:00:48.559><c> take</c><00:00:48.719><c> the</c><00:00:48.879><c> name</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
Surfer agent so we again take the name
 

00:00:49.440 --> 00:00:52.910 align:start position:0%
Surfer agent so we again take the name
the<00:00:49.559><c> llm</c><00:00:50.039><c> config</c><00:00:50.600><c> is</c><00:00:50.800><c> the</c><00:00:51.079><c> typical</c><00:00:51.719><c> llm</c><00:00:52.199><c> config</c>

00:00:52.910 --> 00:00:52.920 align:start position:0%
the llm config is the typical llm config
 

00:00:52.920 --> 00:00:55.549 align:start position:0%
the llm config is the typical llm config
but<00:00:53.079><c> there's</c><00:00:53.320><c> also</c><00:00:53.559><c> a</c><00:00:53.719><c> summarizer</c><00:00:54.440><c> llm</c><00:00:55.079><c> config</c>

00:00:55.549 --> 00:00:55.559 align:start position:0%
but there's also a summarizer llm config
 

00:00:55.559 --> 00:00:57.910 align:start position:0%
but there's also a summarizer llm config
property<00:00:56.000><c> and</c><00:00:56.160><c> the</c><00:00:56.320><c> difference</c><00:00:56.760><c> is</c><00:00:57.359><c> if</c><00:00:57.520><c> we</c><00:00:57.800><c> if</c>

00:00:57.910 --> 00:00:57.920 align:start position:0%
property and the difference is if we if
 

00:00:57.920 --> 00:01:00.470 align:start position:0%
property and the difference is if we if
we<00:00:58.039><c> say</c><00:00:58.480><c> hey</c><00:00:59.079><c> select</c><00:00:59.399><c> the</c><00:00:59.559><c> link</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
we say hey select the link
 

00:01:00.480 --> 00:01:02.430 align:start position:0%
we say hey select the link
that<00:01:00.840><c> um</c><00:01:01.160><c> like</c><00:01:01.519><c> say</c><00:01:01.719><c> there</c><00:01:01.840><c> were</c><00:01:02.079><c> several</c>

00:01:02.430 --> 00:01:02.440 align:start position:0%
that um like say there were several
 

00:01:02.440 --> 00:01:04.429 align:start position:0%
that um like say there were several
links<00:01:02.960><c> on</c><00:01:03.120><c> the</c><00:01:03.320><c> actual</c><00:01:03.840><c> page</c><00:01:04.119><c> it's</c><00:01:04.239><c> going</c><00:01:04.360><c> to</c>

00:01:04.429 --> 00:01:04.439 align:start position:0%
links on the actual page it's going to
 

00:01:04.439 --> 00:01:06.789 align:start position:0%
links on the actual page it's going to
give<00:01:04.559><c> us</c><00:01:04.680><c> in</c><00:01:04.799><c> a</c><00:01:05.000><c> text</c><00:01:05.280><c> form</c><00:01:06.200><c> what</c><00:01:06.360><c> those</c><00:01:06.560><c> links</c>

00:01:06.789 --> 00:01:06.799 align:start position:0%
give us in a text form what those links
 

00:01:06.799 --> 00:01:08.990 align:start position:0%
give us in a text form what those links
are<00:01:07.240><c> so</c><00:01:07.439><c> if</c><00:01:07.560><c> I</c><00:01:07.680><c> say</c><00:01:07.880><c> hey</c><00:01:08.080><c> select</c><00:01:08.439><c> that</c><00:01:08.600><c> link</c>

00:01:08.990 --> 00:01:09.000 align:start position:0%
are so if I say hey select that link
 

00:01:09.000 --> 00:01:11.109 align:start position:0%
are so if I say hey select that link
it's<00:01:09.159><c> going</c><00:01:09.280><c> to</c><00:01:09.360><c> use</c><00:01:09.560><c> this</c><00:01:09.680><c> LOL</c><00:01:10.119><c> config</c><00:01:11.000><c> and</c>

00:01:11.109 --> 00:01:11.119 align:start position:0%
it's going to use this LOL config and
 

00:01:11.119 --> 00:01:13.590 align:start position:0%
it's going to use this LOL config and
then<00:01:11.280><c> if</c><00:01:11.400><c> I</c><00:01:11.520><c> say</c><00:01:11.799><c> hey</c><00:01:12.200><c> summarize</c><00:01:13.200><c> like</c><00:01:13.400><c> if</c><00:01:13.479><c> we</c>

00:01:13.590 --> 00:01:13.600 align:start position:0%
then if I say hey summarize like if we
 

00:01:13.600 --> 00:01:15.749 align:start position:0%
then if I say hey summarize like if we
go<00:01:13.720><c> to</c><00:01:13.799><c> Wikipedia</c><00:01:14.400><c> and</c><00:01:14.479><c> there's</c><00:01:14.680><c> like</c><00:01:14.799><c> a</c><00:01:15.000><c> huge</c>

00:01:15.749 --> 00:01:15.759 align:start position:0%
go to Wikipedia and there's like a huge
 

00:01:15.759 --> 00:01:17.630 align:start position:0%
go to Wikipedia and there's like a huge
you<00:01:15.880><c> know</c><00:01:16.080><c> article</c><00:01:16.479><c> on</c><00:01:16.840><c> Sam</c><00:01:17.119><c> Alman</c><00:01:17.520><c> for</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
you know article on Sam Alman for
 

00:01:17.640 --> 00:01:20.350 align:start position:0%
you know article on Sam Alman for
instance<00:01:18.320><c> and</c><00:01:18.439><c> I</c><00:01:18.560><c> say</c><00:01:18.920><c> hey</c><00:01:19.200><c> summarize</c><00:01:20.200><c> what</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
instance and I say hey summarize what
 

00:01:20.360 --> 00:01:22.429 align:start position:0%
instance and I say hey summarize what
you<00:01:20.479><c> see</c><00:01:20.759><c> on</c><00:01:20.880><c> the</c><00:01:21.040><c> screen</c><00:01:21.479><c> right</c><00:01:21.640><c> now</c><00:01:22.280><c> then</c>

00:01:22.429 --> 00:01:22.439 align:start position:0%
you see on the screen right now then
 

00:01:22.439 --> 00:01:24.270 align:start position:0%
you see on the screen right now then
it's<00:01:22.600><c> going</c><00:01:22.720><c> to</c><00:01:22.880><c> use</c><00:01:23.360><c> this</c><00:01:23.600><c> config</c><00:01:24.119><c> and</c><00:01:24.200><c> then</c>

00:01:24.270 --> 00:01:24.280 align:start position:0%
it's going to use this config and then
 

00:01:24.280 --> 00:01:26.350 align:start position:0%
it's going to use this config and then
it<00:01:24.400><c> takes</c><00:01:24.920><c> a</c><00:01:25.119><c> browser</c><00:01:25.520><c> config</c><00:01:25.920><c> so</c><00:01:26.119><c> you</c><00:01:26.200><c> can</c>

00:01:26.350 --> 00:01:26.360 align:start position:0%
it takes a browser config so you can
 

00:01:26.360 --> 00:01:28.469 align:start position:0%
it takes a browser config so you can
change<00:01:26.640><c> the</c><00:01:26.799><c> viewport</c><00:01:27.320><c> size</c><00:01:27.880><c> and</c><00:01:28.000><c> this</c><00:01:28.119><c> is</c><00:01:28.320><c> how</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
change the viewport size and this is how
 

00:01:28.479 --> 00:01:30.670 align:start position:0%
change the viewport size and this is how
much<00:01:28.680><c> it</c><00:01:28.799><c> will</c><00:01:29.079><c> like</c><00:01:29.520><c> be</c><00:01:29.640><c> able</c><00:01:30.040><c> to</c><00:01:30.200><c> see</c><00:01:30.439><c> on</c><00:01:30.560><c> the</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
much it will like be able to see on the
 

00:01:30.680 --> 00:01:32.910 align:start position:0%
much it will like be able to see on the
screen<00:01:31.040><c> at</c><00:01:31.200><c> once</c><00:01:31.920><c> so</c><00:01:32.200><c> like</c><00:01:32.320><c> I</c><00:01:32.399><c> said</c><00:01:32.560><c> if</c><00:01:32.640><c> you</c><00:01:32.759><c> can</c>

00:01:32.910 --> 00:01:32.920 align:start position:0%
screen at once so like I said if you can
 

00:01:32.920 --> 00:01:34.910 align:start position:0%
screen at once so like I said if you can
scroll<00:01:33.280><c> down</c><00:01:33.880><c> it'll</c><00:01:34.399><c> it'll</c><00:01:34.640><c> kind</c><00:01:34.759><c> of</c>

00:01:34.910 --> 00:01:34.920 align:start position:0%
scroll down it'll it'll kind of
 

00:01:34.920 --> 00:01:36.630 align:start position:0%
scroll down it'll it'll kind of
calculate<00:01:35.680><c> how</c><00:01:35.840><c> many</c><00:01:36.040><c> times</c><00:01:36.280><c> it</c><00:01:36.399><c> would</c><00:01:36.520><c> have</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
calculate how many times it would have
 

00:01:36.640 --> 00:01:38.429 align:start position:0%
calculate how many times it would have
to<00:01:36.720><c> scroll</c><00:01:37.000><c> down</c><00:01:37.240><c> as</c><00:01:37.479><c> pages</c><00:01:38.040><c> and</c><00:01:38.159><c> I'll</c><00:01:38.280><c> show</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
to scroll down as pages and I'll show
 

00:01:38.439 --> 00:01:39.950 align:start position:0%
to scroll down as pages and I'll show
you<00:01:38.520><c> an</c><00:01:38.600><c> example</c><00:01:38.840><c> of</c><00:01:38.960><c> that</c><00:01:39.079><c> in</c><00:01:39.520><c> just</c><00:01:39.640><c> a</c><00:01:39.720><c> minute</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
you an example of that in just a minute
 

00:01:39.960 --> 00:01:41.190 align:start position:0%
you an example of that in just a minute
and<00:01:40.040><c> then</c><00:01:40.159><c> of</c><00:01:40.280><c> course</c><00:01:40.479><c> you</c><00:01:40.600><c> need</c><00:01:40.720><c> to</c><00:01:40.840><c> create</c><00:01:41.040><c> a</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
and then of course you need to create a
 

00:01:41.200 --> 00:01:43.109 align:start position:0%
and then of course you need to create a
bing<00:01:41.520><c> API</c><00:01:41.960><c> key</c><00:01:42.439><c> and</c><00:01:42.520><c> then</c><00:01:42.640><c> we</c><00:01:42.720><c> have</c><00:01:42.799><c> a</c><00:01:42.880><c> user</c>

00:01:43.109 --> 00:01:43.119 align:start position:0%
bing API key and then we have a user
 

00:01:43.119 --> 00:01:44.830 align:start position:0%
bing API key and then we have a user
proxy<00:01:43.479><c> agent</c><00:01:43.720><c> and</c><00:01:43.799><c> the</c><00:01:43.920><c> difference</c><00:01:44.240><c> is</c><00:01:44.680><c> the</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
proxy agent and the difference is the
 

00:01:44.840 --> 00:01:47.230 align:start position:0%
proxy agent and the difference is the
human<00:01:45.079><c> input</c><00:01:45.399><c> mode</c><00:01:45.680><c> here</c><00:01:46.000><c> is</c><00:01:46.240><c> always</c><00:01:46.960><c> because</c>

00:01:47.230 --> 00:01:47.240 align:start position:0%
human input mode here is always because
 

00:01:47.240 --> 00:01:49.030 align:start position:0%
human input mode here is always because
we're<00:01:47.439><c> going</c><00:01:47.520><c> to</c><00:01:47.640><c> be</c><00:01:47.799><c> chatting</c><00:01:48.600><c> with</c><00:01:48.719><c> the</c><00:01:48.840><c> web</c>

00:01:49.030 --> 00:01:49.040 align:start position:0%
we're going to be chatting with the web
 

00:01:49.040 --> 00:01:51.709 align:start position:0%
we're going to be chatting with the web
Surfer<00:01:49.360><c> agent</c><00:01:49.719><c> to</c><00:01:50.040><c> do</c><00:01:50.439><c> something</c><00:01:50.799><c> for</c><00:01:51.079><c> us</c><00:01:51.600><c> as</c>

00:01:51.709 --> 00:01:51.719 align:start position:0%
Surfer agent to do something for us as
 

00:01:51.719 --> 00:01:53.630 align:start position:0%
Surfer agent to do something for us as
it's<00:01:51.880><c> searching</c><00:01:52.240><c> on</c><00:01:52.399><c> the</c><00:01:52.560><c> web</c><00:01:53.079><c> okay</c><00:01:53.240><c> so</c><00:01:53.439><c> after</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
it's searching on the web okay so after
 

00:01:53.640 --> 00:01:55.270 align:start position:0%
it's searching on the web okay so after
we<00:01:53.759><c> ran</c><00:01:53.960><c> it</c><00:01:54.280><c> it</c><00:01:54.399><c> did</c><00:01:54.560><c> a</c><00:01:54.640><c> search</c><00:01:54.920><c> the</c><00:01:55.040><c> latest</c>

00:01:55.270 --> 00:01:55.280 align:start position:0%
we ran it it did a search the latest
 

00:01:55.280 --> 00:01:57.550 align:start position:0%
we ran it it did a search the latest
news<00:01:55.520><c> on</c><00:01:55.759><c> AI</c><00:01:56.479><c> and</c><00:01:56.600><c> when</c><00:01:56.719><c> it</c><00:01:56.920><c> did</c><00:01:57.159><c> that</c><00:01:57.439><c> it</c>

00:01:57.550 --> 00:01:57.560 align:start position:0%
news on AI and when it did that it
 

00:01:57.560 --> 00:01:59.190 align:start position:0%
news on AI and when it did that it
showed<00:01:57.920><c> only</c><00:01:58.320><c> I</c><00:01:58.399><c> think</c><00:01:58.520><c> it</c><00:01:58.600><c> only</c><00:01:58.719><c> shows</c><00:01:59.039><c> one</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
showed only I think it only shows one
 

00:01:59.200 --> 00:02:01.190 align:start position:0%
showed only I think it only shows one
page<00:01:59.399><c> whenever</c><00:01:59.640><c> you</c><00:02:00.039><c> looking</c><00:02:00.200><c> for</c><00:02:00.399><c> results</c><00:02:00.920><c> on</c>

00:02:01.190 --> 00:02:01.200 align:start position:0%
page whenever you looking for results on
 

00:02:01.200 --> 00:02:03.029 align:start position:0%
page whenever you looking for results on
say<00:02:01.439><c> like</c><00:02:01.759><c> a</c><00:02:01.880><c> search</c><00:02:02.200><c> page</c><00:02:02.360><c> of</c><00:02:02.479><c> Bing</c><00:02:02.719><c> it</c><00:02:02.840><c> only</c>

00:02:03.029 --> 00:02:03.039 align:start position:0%
say like a search page of Bing it only
 

00:02:03.039 --> 00:02:04.670 align:start position:0%
say like a search page of Bing it only
give<00:02:03.200><c> you</c><00:02:03.560><c> one</c><00:02:03.799><c> page</c><00:02:03.960><c> of</c><00:02:04.079><c> results</c><00:02:04.439><c> so</c><00:02:04.520><c> you</c><00:02:04.600><c> can</c>

00:02:04.670 --> 00:02:04.680 align:start position:0%
give you one page of results so you can
 

00:02:04.680 --> 00:02:06.270 align:start position:0%
give you one page of results so you can
see<00:02:04.840><c> there's</c><00:02:05.000><c> like</c><00:02:05.119><c> 15</c><00:02:05.479><c> different</c><00:02:05.840><c> addresses</c>

00:02:06.270 --> 00:02:06.280 align:start position:0%
see there's like 15 different addresses
 

00:02:06.280 --> 00:02:07.749 align:start position:0%
see there's like 15 different addresses
here<00:02:06.439><c> that</c><00:02:06.560><c> we</c><00:02:06.640><c> could</c><00:02:06.759><c> choose</c><00:02:07.079><c> from</c><00:02:07.600><c> and</c><00:02:07.680><c> then</c>

00:02:07.749 --> 00:02:07.759 align:start position:0%
here that we could choose from and then
 

00:02:07.759 --> 00:02:09.990 align:start position:0%
here that we could choose from and then
we<00:02:07.920><c> can</c><00:02:08.119><c> have</c><00:02:08.280><c> it</c><00:02:08.560><c> select</c><00:02:09.039><c> or</c><00:02:09.280><c> go</c><00:02:09.479><c> to</c><00:02:09.720><c> one</c><00:02:09.840><c> of</c>

00:02:09.990 --> 00:02:10.000 align:start position:0%
we can have it select or go to one of
 

00:02:10.000 --> 00:02:12.309 align:start position:0%
we can have it select or go to one of
these<00:02:10.160><c> links</c><00:02:10.840><c> so</c><00:02:11.000><c> if</c><00:02:11.080><c> we</c><00:02:11.200><c> come</c><00:02:11.400><c> back</c><00:02:11.520><c> up</c><00:02:11.680><c> to</c><00:02:12.120><c> the</c>

00:02:12.309 --> 00:02:12.319 align:start position:0%
these links so if we come back up to the
 

00:02:12.319 --> 00:02:13.949 align:start position:0%
these links so if we come back up to the
first<00:02:12.520><c> one</c><00:02:12.760><c> here</c><00:02:13.280><c> this</c><00:02:13.400><c> is</c><00:02:13.520><c> something</c><00:02:13.760><c> from</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
first one here this is something from
 

00:02:13.959 --> 00:02:16.190 align:start position:0%
first one here this is something from
Tech<00:02:14.239><c> crunch</c><00:02:14.599><c> I'm</c><00:02:14.680><c> going</c><00:02:14.760><c> to</c><00:02:14.879><c> say</c><00:02:15.120><c> go</c><00:02:15.360><c> to</c><00:02:16.000><c> this</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
Tech crunch I'm going to say go to this
 

00:02:16.200 --> 00:02:20.270 align:start position:0%
Tech crunch I'm going to say go to this
link<00:02:16.840><c> link</c><00:02:17.440><c> from</c><00:02:17.800><c> the</c><00:02:18.120><c> first</c><00:02:18.800><c> result</c><00:02:19.680><c> okay</c><00:02:19.879><c> so</c>

00:02:20.270 --> 00:02:20.280 align:start position:0%
link link from the first result okay so
 

00:02:20.280 --> 00:02:21.630 align:start position:0%
link link from the first result okay so
it<00:02:20.560><c> did</c><00:02:20.800><c> that</c><00:02:20.959><c> there's</c><00:02:21.120><c> a</c><00:02:21.239><c> couple</c><00:02:21.440><c> ways</c><00:02:21.560><c> you</c>

00:02:21.630 --> 00:02:21.640 align:start position:0%
it did that there's a couple ways you
 

00:02:21.640 --> 00:02:23.430 align:start position:0%
it did that there's a couple ways you
can<00:02:21.800><c> actually</c><00:02:22.200><c> tell</c><00:02:22.440><c> it</c><00:02:22.560><c> you</c><00:02:22.640><c> can</c><00:02:22.840><c> have</c><00:02:22.959><c> it</c>

00:02:23.430 --> 00:02:23.440 align:start position:0%
can actually tell it you can have it
 

00:02:23.440 --> 00:02:25.150 align:start position:0%
can actually tell it you can have it
select<00:02:23.720><c> the</c><00:02:23.879><c> link</c><00:02:24.120><c> with</c><00:02:24.239><c> the</c><00:02:24.440><c> title</c><00:02:24.959><c> that</c><00:02:25.040><c> it</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
select the link with the title that it
 

00:02:25.160 --> 00:02:27.070 align:start position:0%
select the link with the title that it
shows<00:02:25.480><c> there</c><00:02:25.680><c> but</c><00:02:26.000><c> this</c><00:02:26.160><c> works</c><00:02:26.440><c> so</c><00:02:26.560><c> it</c><00:02:26.680><c> says</c>

00:02:27.070 --> 00:02:27.080 align:start position:0%
shows there but this works so it says
 

00:02:27.080 --> 00:02:29.229 align:start position:0%
shows there but this works so it says
viewport<00:02:27.599><c> position</c><00:02:28.080><c> showing</c><00:02:28.440><c> page</c><00:02:28.599><c> 105</c><00:02:29.160><c> that</c>

00:02:29.229 --> 00:02:29.239 align:start position:0%
viewport position showing page 105 that
 

00:02:29.239 --> 00:02:31.390 align:start position:0%
viewport position showing page 105 that
means<00:02:29.680><c> that</c><00:02:30.040><c> could</c><00:02:30.200><c> it</c><00:02:30.280><c> could</c><00:02:30.440><c> scroll</c><00:02:30.840><c> down</c>

00:02:31.390 --> 00:02:31.400 align:start position:0%
means that could it could scroll down
 

00:02:31.400 --> 00:02:33.309 align:start position:0%
means that could it could scroll down
and<00:02:31.519><c> it</c><00:02:31.640><c> could</c><00:02:31.840><c> see</c><00:02:32.319><c> five</c><00:02:32.760><c> different</c><00:02:33.080><c> pieces</c>

00:02:33.309 --> 00:02:33.319 align:start position:0%
and it could see five different pieces
 

00:02:33.319 --> 00:02:34.750 align:start position:0%
and it could see five different pieces
of<00:02:33.480><c> information</c><00:02:34.000><c> but</c><00:02:34.239><c> it's</c><00:02:34.360><c> not</c><00:02:34.519><c> going</c><00:02:34.640><c> to</c>

00:02:34.750 --> 00:02:34.760 align:start position:0%
of information but it's not going to
 

00:02:34.760 --> 00:02:36.750 align:start position:0%
of information but it's not going to
show<00:02:35.000><c> us</c><00:02:35.599><c> the</c><00:02:35.760><c> first</c><00:02:35.959><c> one</c><00:02:36.200><c> we</c><00:02:36.319><c> have</c><00:02:36.400><c> to</c><00:02:36.480><c> tell</c><00:02:36.640><c> it</c>

00:02:36.750 --> 00:02:36.760 align:start position:0%
show us the first one we have to tell it
 

00:02:36.760 --> 00:02:38.589 align:start position:0%
show us the first one we have to tell it
to<00:02:36.879><c> scroll</c><00:02:37.160><c> down</c><00:02:37.319><c> if</c><00:02:37.440><c> you</c><00:02:37.519><c> want</c><00:02:37.599><c> to</c><00:02:37.720><c> see</c><00:02:37.959><c> more</c>

00:02:38.589 --> 00:02:38.599 align:start position:0%
to scroll down if you want to see more
 

00:02:38.599 --> 00:02:41.869 align:start position:0%
to scroll down if you want to see more
so<00:02:38.840><c> this</c><00:02:39.000><c> is</c><00:02:39.840><c> um</c><00:02:40.000><c> this</c><00:02:40.120><c> is</c><00:02:40.360><c> kind</c><00:02:40.519><c> of</c><00:02:41.040><c> like</c><00:02:41.599><c> how</c>

00:02:41.869 --> 00:02:41.879 align:start position:0%
so this is um this is kind of like how
 

00:02:41.879 --> 00:02:45.070 align:start position:0%
so this is um this is kind of like how
the<00:02:42.480><c> website</c><00:02:42.879><c> is</c><00:02:43.080><c> set</c><00:02:43.319><c> up</c><00:02:43.760><c> so</c><00:02:43.959><c> it</c><00:02:44.200><c> has</c><00:02:44.760><c> some</c>

00:02:45.070 --> 00:02:45.080 align:start position:0%
the website is set up so it has some
 

00:02:45.080 --> 00:02:46.830 align:start position:0%
the website is set up so it has some
articles<00:02:45.599><c> here</c><00:02:46.000><c> if</c><00:02:46.080><c> we</c><00:02:46.200><c> were</c><00:02:46.319><c> to</c><00:02:46.440><c> go</c><00:02:46.560><c> to</c><00:02:46.680><c> that</c>

00:02:46.830 --> 00:02:46.840 align:start position:0%
articles here if we were to go to that
 

00:02:46.840 --> 00:02:48.949 align:start position:0%
articles here if we were to go to that
link<00:02:47.040><c> we</c><00:02:47.159><c> would</c><00:02:47.280><c> see</c><00:02:47.440><c> it</c><00:02:47.599><c> this</c><00:02:47.760><c> way</c><00:02:47.959><c> as</c><00:02:48.080><c> well</c><00:02:48.640><c> so</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
link we would see it this way as well so
 

00:02:48.959 --> 00:02:50.430 align:start position:0%
link we would see it this way as well so
instead<00:02:49.280><c> I</c><00:02:49.360><c> want</c><00:02:49.440><c> to</c><00:02:49.519><c> say</c><00:02:49.800><c> scroll</c><00:02:50.120><c> down</c><00:02:50.239><c> to</c><00:02:50.360><c> the</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
instead I want to say scroll down to the
 

00:02:50.440 --> 00:02:54.470 align:start position:0%
instead I want to say scroll down to the
second<00:02:50.760><c> page</c><00:02:51.760><c> scroll</c><00:02:52.239><c> down</c><00:02:52.680><c> to</c><00:02:53.680><c> page</c><00:02:53.959><c> two</c><00:02:54.280><c> so</c>

00:02:54.470 --> 00:02:54.480 align:start position:0%
second page scroll down to page two so
 

00:02:54.480 --> 00:02:57.070 align:start position:0%
second page scroll down to page two so
now<00:02:54.640><c> it's</c><00:02:54.800><c> showing</c><00:02:55.319><c> page</c><00:02:55.599><c> two</c><00:02:55.959><c> of</c><00:02:56.239><c> five</c><00:02:56.800><c> okay</c>

00:02:57.070 --> 00:02:57.080 align:start position:0%
now it's showing page two of five okay
 

00:02:57.080 --> 00:03:00.070 align:start position:0%
now it's showing page two of five okay
and<00:02:57.280><c> now</c><00:02:57.840><c> here</c><00:02:58.280><c> is</c><00:02:58.959><c> here's</c><00:02:59.159><c> a</c><00:02:59.360><c> post</c><00:02:59.560><c> from</c><00:02:59.840><c> March</c>

00:03:00.070 --> 00:03:00.080 align:start position:0%
and now here is here's a post from March
 

00:03:00.080 --> 00:03:03.110 align:start position:0%
and now here is here's a post from March
26th<00:03:01.080><c> and</c><00:03:01.200><c> what</c><00:03:01.319><c> I</c><00:03:01.400><c> want</c><00:03:01.560><c> to</c><00:03:01.640><c> do</c><00:03:01.840><c> is</c><00:03:02.000><c> I</c><00:03:02.080><c> want</c><00:03:02.319><c> to</c>

00:03:03.110 --> 00:03:03.120 align:start position:0%
26th and what I want to do is I want to
 

00:03:03.120 --> 00:03:05.949 align:start position:0%
26th and what I want to do is I want to
um<00:03:03.360><c> go</c><00:03:03.519><c> to</c><00:03:03.760><c> this</c><00:03:03.879><c> link</c><00:03:04.159><c> cuz</c><00:03:04.360><c> this</c><00:03:04.519><c> is</c><00:03:05.519><c> I</c><00:03:05.599><c> believe</c>

00:03:05.949 --> 00:03:05.959 align:start position:0%
um go to this link cuz this is I believe
 

00:03:05.959 --> 00:03:07.190 align:start position:0%
um go to this link cuz this is I believe
like<00:03:06.080><c> if</c><00:03:06.159><c> you</c><00:03:06.360><c> actually</c><00:03:06.560><c> go</c><00:03:06.640><c> to</c><00:03:06.760><c> the</c><00:03:06.879><c> website</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
like if you actually go to the website
 

00:03:07.200 --> 00:03:08.550 align:start position:0%
like if you actually go to the website
you<00:03:07.280><c> know</c><00:03:07.400><c> they</c><00:03:07.519><c> give</c><00:03:07.640><c> a</c><00:03:07.799><c> quick</c><00:03:08.000><c> summary</c><00:03:08.400><c> of</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
you know they give a quick summary of
 

00:03:08.560 --> 00:03:10.110 align:start position:0%
you know they give a quick summary of
the<00:03:08.680><c> post</c><00:03:08.879><c> and</c><00:03:09.000><c> you</c><00:03:09.120><c> go</c><00:03:09.239><c> to</c><00:03:09.400><c> into</c><00:03:09.599><c> the</c><00:03:09.720><c> link</c><00:03:10.040><c> and</c>

00:03:10.110 --> 00:03:10.120 align:start position:0%
the post and you go to into the link and
 

00:03:10.120 --> 00:03:11.949 align:start position:0%
the post and you go to into the link and
you<00:03:10.239><c> get</c><00:03:10.400><c> the</c><00:03:10.560><c> rest</c><00:03:10.760><c> of</c><00:03:10.879><c> it</c><00:03:11.480><c> well</c><00:03:11.640><c> that's</c><00:03:11.799><c> what</c>

00:03:11.949 --> 00:03:11.959 align:start position:0%
you get the rest of it well that's what
 

00:03:11.959 --> 00:03:14.270 align:start position:0%
you get the rest of it well that's what
this<00:03:12.080><c> is</c><00:03:12.239><c> so</c><00:03:12.400><c> I'm</c><00:03:12.480><c> going</c><00:03:12.599><c> to</c><00:03:12.720><c> tell</c><00:03:12.920><c> it</c><00:03:13.080><c> to</c><00:03:13.360><c> go</c><00:03:13.599><c> to</c>

00:03:14.270 --> 00:03:14.280 align:start position:0%
this is so I'm going to tell it to go to
 

00:03:14.280 --> 00:03:16.309 align:start position:0%
this is so I'm going to tell it to go to
this<00:03:14.480><c> link</c><00:03:14.959><c> so</c><00:03:15.159><c> what</c><00:03:15.280><c> I</c><00:03:15.400><c> asked</c><00:03:15.640><c> it</c><00:03:15.799><c> to</c><00:03:15.920><c> do</c><00:03:16.120><c> was</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
this link so what I asked it to do was
 

00:03:16.319 --> 00:03:18.509 align:start position:0%
this link so what I asked it to do was
go<00:03:16.480><c> to</c><00:03:16.640><c> the</c><00:03:16.879><c> post</c><00:03:17.239><c> from</c><00:03:17.440><c> Sarah</c><00:03:17.799><c> Perez</c><00:03:18.239><c> about</c>

00:03:18.509 --> 00:03:18.519 align:start position:0%
go to the post from Sarah Perez about
 

00:03:18.519 --> 00:03:21.550 align:start position:0%
go to the post from Sarah Perez about
premium<00:03:18.959><c> subscribers</c><00:03:19.480><c> on</c><00:03:19.760><c> X</c><00:03:20.440><c> so</c><00:03:20.680><c> here</c><00:03:21.080><c> it</c><00:03:21.319><c> post</c>

00:03:21.550 --> 00:03:21.560 align:start position:0%
premium subscribers on X so here it post
 

00:03:21.560 --> 00:03:24.350 align:start position:0%
premium subscribers on X so here it post
from<00:03:21.720><c> Sarah</c><00:03:22.040><c> Perez</c><00:03:22.840><c> and</c><00:03:23.120><c> this</c><00:03:23.519><c> was</c><00:03:24.080><c> about</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
from Sarah Perez and this was about
 

00:03:24.360 --> 00:03:26.910 align:start position:0%
from Sarah Perez and this was about
premium<00:03:24.760><c> subscribers</c><00:03:25.319><c> on</c><00:03:25.519><c> X</c><00:03:26.200><c> so</c><00:03:26.440><c> then</c><00:03:26.599><c> it</c><00:03:26.760><c> went</c>

00:03:26.910 --> 00:03:26.920 align:start position:0%
premium subscribers on X so then it went
 

00:03:26.920 --> 00:03:29.390 align:start position:0%
premium subscribers on X so then it went
and<00:03:27.120><c> did</c><00:03:27.400><c> that</c><00:03:28.120><c> gives</c><00:03:28.319><c> you</c><00:03:28.439><c> the</c><00:03:28.640><c> title</c><00:03:29.239><c> and</c>

00:03:29.390 --> 00:03:29.400 align:start position:0%
and did that gives you the title and
 

00:03:29.400 --> 00:03:32.869 align:start position:0%
and did that gives you the title and
then<00:03:29.560><c> here</c><00:03:29.799><c> here</c><00:03:29.959><c> is</c><00:03:30.159><c> the</c><00:03:30.360><c> actual</c><00:03:31.080><c> post</c><00:03:32.080><c> from</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
then here here is the actual post from
 

00:03:32.879 --> 00:03:34.429 align:start position:0%
then here here is the actual post from
uh<00:03:33.040><c> from</c><00:03:33.280><c> that</c><00:03:33.480><c> link</c><00:03:33.920><c> so</c><00:03:34.080><c> whenever</c><00:03:34.319><c> we</c>

00:03:34.429 --> 00:03:34.439 align:start position:0%
uh from that link so whenever we
 

00:03:34.439 --> 00:03:35.509 align:start position:0%
uh from that link so whenever we
actually<00:03:34.599><c> go</c><00:03:34.720><c> into</c><00:03:34.879><c> that</c><00:03:35.000><c> link</c><00:03:35.159><c> here's</c><00:03:35.360><c> the</c>

00:03:35.509 --> 00:03:35.519 align:start position:0%
actually go into that link here's the
 

00:03:35.519 --> 00:03:38.229 align:start position:0%
actually go into that link here's the
post<00:03:36.319><c> now</c><00:03:36.680><c> let's</c><00:03:36.920><c> summarize</c><00:03:37.480><c> this</c><00:03:37.879><c> and</c><00:03:38.040><c> the</c>

00:03:38.229 --> 00:03:38.239 align:start position:0%
post now let's summarize this and the
 

00:03:38.239 --> 00:03:40.070 align:start position:0%
post now let's summarize this and the
difference<00:03:38.599><c> that's</c><00:03:38.799><c> going</c><00:03:38.879><c> to</c><00:03:39.040><c> be</c><00:03:39.319><c> here</c><00:03:39.879><c> is</c>

00:03:40.070 --> 00:03:40.080 align:start position:0%
difference that's going to be here is
 

00:03:40.080 --> 00:03:42.110 align:start position:0%
difference that's going to be here is
there's<00:03:40.239><c> a</c><00:03:40.519><c> different</c><00:03:40.920><c> function</c><00:03:41.760><c> there's</c><00:03:41.959><c> a</c>

00:03:42.110 --> 00:03:42.120 align:start position:0%
there's a different function there's a
 

00:03:42.120 --> 00:03:43.910 align:start position:0%
there's a different function there's a
different<00:03:42.360><c> function</c><00:03:42.680><c> called</c><00:03:42.959><c> summarize</c><00:03:43.599><c> page</c>

00:03:43.910 --> 00:03:43.920 align:start position:0%
different function called summarize page
 

00:03:43.920 --> 00:03:46.550 align:start position:0%
different function called summarize page
so<00:03:44.400><c> you</c><00:03:44.519><c> do</c><00:03:44.799><c> like</c><00:03:44.920><c> a</c><00:03:45.159><c> web</c><00:03:45.400><c> search</c><00:03:45.920><c> with</c><00:03:46.040><c> the</c><00:03:46.159><c> llm</c>

00:03:46.550 --> 00:03:46.560 align:start position:0%
so you do like a web search with the llm
 

00:03:46.560 --> 00:03:47.750 align:start position:0%
so you do like a web search with the llm
config<00:03:46.959><c> and</c><00:03:47.040><c> now</c><00:03:47.159><c> we're</c><00:03:47.280><c> using</c><00:03:47.560><c> the</c>

00:03:47.750 --> 00:03:47.760 align:start position:0%
config and now we're using the
 

00:03:47.760 --> 00:03:49.990 align:start position:0%
config and now we're using the
summarizer<00:03:48.400><c> llm</c><00:03:48.920><c> config</c><00:03:49.599><c> this</c><00:03:49.680><c> is</c><00:03:49.799><c> a</c>

00:03:49.990 --> 00:03:50.000 align:start position:0%
summarizer llm config this is a
 

00:03:50.000 --> 00:03:51.110 align:start position:0%
summarizer llm config this is a
different<00:03:50.239><c> function</c><00:03:50.519><c> that's</c><00:03:50.680><c> being</c><00:03:50.879><c> called</c>

00:03:51.110 --> 00:03:51.120 align:start position:0%
different function that's being called
 

00:03:51.120 --> 00:03:53.350 align:start position:0%
different function that's being called
to<00:03:51.280><c> summarize</c><00:03:51.799><c> the</c><00:03:51.920><c> page</c><00:03:52.360><c> and</c><00:03:52.519><c> so</c><00:03:52.959><c> here</c><00:03:53.200><c> it</c>

00:03:53.350 --> 00:03:53.360 align:start position:0%
to summarize the page and so here it
 

00:03:53.360 --> 00:03:55.069 align:start position:0%
to summarize the page and so here it
summarize<00:03:53.920><c> the</c><00:03:54.040><c> page</c><00:03:54.519><c> okay</c><00:03:54.640><c> so</c><00:03:54.799><c> I</c><00:03:54.879><c> think</c>

00:03:55.069 --> 00:03:55.079 align:start position:0%
summarize the page okay so I think
 

00:03:55.079 --> 00:03:56.869 align:start position:0%
summarize the page okay so I think
something<00:03:55.400><c> like</c><00:03:55.680><c> that</c><00:03:55.879><c> is</c><00:03:56.040><c> more</c><00:03:56.400><c> viable</c><00:03:56.760><c> if</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
something like that is more viable if
 

00:03:56.879 --> 00:03:59.429 align:start position:0%
something like that is more viable if
you<00:03:57.200><c> have</c><00:03:57.360><c> a</c><00:03:57.560><c> UI</c><00:03:58.280><c> and</c><00:03:58.400><c> you</c><00:03:58.480><c> can</c><00:03:58.599><c> kind</c><00:03:58.720><c> of</c><00:03:59.239><c> maybe</c>

00:03:59.429 --> 00:03:59.439 align:start position:0%
you have a UI and you can kind of maybe
 

00:03:59.439 --> 00:04:01.390 align:start position:0%
you have a UI and you can kind of maybe
have<00:03:59.560><c> a</c><00:03:59.920><c> visual</c><00:04:00.480><c> of</c><00:04:00.680><c> what</c><00:04:00.760><c> it</c><00:04:00.879><c> looks</c><00:04:01.120><c> like</c>

00:04:01.390 --> 00:04:01.400 align:start position:0%
have a visual of what it looks like
 

00:04:01.400 --> 00:04:03.190 align:start position:0%
have a visual of what it looks like
where<00:04:01.599><c> it's</c><00:04:01.840><c> going</c><00:04:02.360><c> and</c><00:04:02.439><c> then</c><00:04:02.599><c> say</c><00:04:02.840><c> hey</c><00:04:03.040><c> oh</c>

00:04:03.190 --> 00:04:03.200 align:start position:0%
where it's going and then say hey oh
 

00:04:03.200 --> 00:04:05.069 align:start position:0%
where it's going and then say hey oh
okay<00:04:03.319><c> I</c><00:04:03.400><c> want</c><00:04:03.519><c> to</c><00:04:03.599><c> go</c><00:04:03.840><c> there</c><00:04:04.280><c> but</c><00:04:04.560><c> now</c><00:04:04.799><c> what</c><00:04:04.920><c> if</c>

00:04:05.069 --> 00:04:05.079 align:start position:0%
okay I want to go there but now what if
 

00:04:05.079 --> 00:04:06.830 align:start position:0%
okay I want to go there but now what if
we<00:04:05.280><c> kind</c><00:04:05.400><c> of</c><00:04:05.640><c> already</c><00:04:05.959><c> know</c><00:04:06.239><c> what</c><00:04:06.360><c> we</c><00:04:06.480><c> want</c><00:04:06.640><c> to</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
we kind of already know what we want to
 

00:04:06.840 --> 00:04:09.429 align:start position:0%
we kind of already know what we want to
do<00:04:07.560><c> we</c><00:04:07.760><c> can</c><00:04:08.040><c> have</c><00:04:08.360><c> several</c><00:04:08.720><c> tasks</c><00:04:09.040><c> in</c><00:04:09.120><c> a</c><00:04:09.239><c> row</c>

00:04:09.429 --> 00:04:09.439 align:start position:0%
do we can have several tasks in a row
 

00:04:09.439 --> 00:04:11.309 align:start position:0%
do we can have several tasks in a row
and<00:04:09.560><c> then</c><00:04:09.720><c> tell</c><00:04:09.879><c> it</c><00:04:10.159><c> what</c><00:04:10.280><c> to</c><00:04:10.480><c> do</c><00:04:10.840><c> whenever</c><00:04:11.120><c> it</c>

00:04:11.309 --> 00:04:11.319 align:start position:0%
and then tell it what to do whenever it
 

00:04:11.319 --> 00:04:13.390 align:start position:0%
and then tell it what to do whenever it
gets<00:04:11.480><c> to</c><00:04:11.720><c> that</c><00:04:11.920><c> page</c><00:04:12.519><c> beforehand</c><00:04:13.000><c> so</c><00:04:13.159><c> here</c><00:04:13.319><c> I</c>

00:04:13.390 --> 00:04:13.400 align:start position:0%
gets to that page beforehand so here I
 

00:04:13.400 --> 00:04:14.670 align:start position:0%
gets to that page beforehand so here I
wanted<00:04:13.640><c> to</c><00:04:13.760><c> search</c><00:04:14.079><c> information</c><00:04:14.439><c> about</c>

00:04:14.670 --> 00:04:14.680 align:start position:0%
wanted to search information about
 

00:04:14.680 --> 00:04:17.629 align:start position:0%
wanted to search information about
autogen<00:04:15.480><c> summarize</c><00:04:16.079><c> the</c><00:04:16.199><c> results</c><00:04:17.040><c> and</c><00:04:17.239><c> then</c>

00:04:17.629 --> 00:04:17.639 align:start position:0%
autogen summarize the results and then
 

00:04:17.639 --> 00:04:19.509 align:start position:0%
autogen summarize the results and then
click<00:04:17.959><c> the</c><00:04:18.120><c> getting</c><00:04:18.440><c> started</c><00:04:18.799><c> result</c><00:04:19.239><c> so</c><00:04:19.400><c> I'll</c>

00:04:19.509 --> 00:04:19.519 align:start position:0%
click the getting started result so I'll
 

00:04:19.519 --> 00:04:20.830 align:start position:0%
click the getting started result so I'll
run<00:04:19.720><c> this</c><00:04:19.880><c> and</c><00:04:20.000><c> just</c><00:04:20.199><c> go</c><00:04:20.359><c> through</c><00:04:20.560><c> with</c><00:04:20.680><c> you</c>

00:04:20.830 --> 00:04:20.840 align:start position:0%
run this and just go through with you
 

00:04:20.840 --> 00:04:21.949 align:start position:0%
run this and just go through with you
what<00:04:20.919><c> it's</c><00:04:21.120><c> actually</c><00:04:21.280><c> doing</c><00:04:21.639><c> right</c><00:04:21.759><c> so</c><00:04:21.880><c> the</c>

00:04:21.949 --> 00:04:21.959 align:start position:0%
what it's actually doing right so the
 

00:04:21.959 --> 00:04:23.510 align:start position:0%
what it's actually doing right so the
first<00:04:22.240><c> task</c><00:04:22.520><c> was</c><00:04:22.639><c> to</c><00:04:22.759><c> search</c><00:04:23.000><c> the</c><00:04:23.120><c> web</c><00:04:23.320><c> for</c>

00:04:23.510 --> 00:04:23.520 align:start position:0%
first task was to search the web for
 

00:04:23.520 --> 00:04:25.550 align:start position:0%
first task was to search the web for
information<00:04:24.199><c> about</c><00:04:24.560><c> Microsoft</c><00:04:25.000><c> autogen</c><00:04:25.479><c> so</c>

00:04:25.550 --> 00:04:25.560 align:start position:0%
information about Microsoft autogen so
 

00:04:25.560 --> 00:04:27.990 align:start position:0%
information about Microsoft autogen so
it<00:04:25.680><c> does</c><00:04:25.880><c> that</c><00:04:26.199><c> the</c><00:04:26.440><c> address</c><00:04:26.800><c> is</c><00:04:27.240><c> autogen</c>

00:04:27.990 --> 00:04:28.000 align:start position:0%
it does that the address is autogen
 

00:04:28.000 --> 00:04:31.070 align:start position:0%
it does that the address is autogen
address<00:04:28.680><c> so</c><00:04:29.039><c> results</c><00:04:29.400><c> here</c><00:04:29.759><c> here</c><00:04:29.919><c> are</c><00:04:30.800><c> about</c>

00:04:31.070 --> 00:04:31.080 align:start position:0%
address so results here here are about
 

00:04:31.080 --> 00:04:33.469 align:start position:0%
address so results here here are about
10<00:04:31.360><c> results</c><00:04:31.800><c> I</c><00:04:31.919><c> think</c><00:04:32.800><c> yeah</c><00:04:32.919><c> so</c><00:04:33.039><c> it</c><00:04:33.160><c> came</c><00:04:33.320><c> up</c>

00:04:33.469 --> 00:04:33.479 align:start position:0%
10 results I think yeah so it came up
 

00:04:33.479 --> 00:04:35.390 align:start position:0%
10 results I think yeah so it came up
with<00:04:33.639><c> 10</c><00:04:33.880><c> different</c><00:04:34.160><c> results</c><00:04:34.639><c> on</c><00:04:34.960><c> B's</c>

00:04:35.390 --> 00:04:35.400 align:start position:0%
with 10 different results on B's
 

00:04:35.400 --> 00:04:37.070 align:start position:0%
with 10 different results on B's
homepage<00:04:35.840><c> about</c><00:04:36.039><c> autogen</c><00:04:36.600><c> then</c><00:04:36.680><c> what</c><00:04:36.800><c> I</c><00:04:36.880><c> asked</c>

00:04:37.070 --> 00:04:37.080 align:start position:0%
homepage about autogen then what I asked
 

00:04:37.080 --> 00:04:38.950 align:start position:0%
homepage about autogen then what I asked
it<00:04:37.160><c> to</c><00:04:37.280><c> do</c><00:04:37.520><c> was</c><00:04:37.720><c> to</c><00:04:37.840><c> summarize</c><00:04:38.320><c> these</c><00:04:38.520><c> results</c>

00:04:38.950 --> 00:04:38.960 align:start position:0%
it to do was to summarize these results
 

00:04:38.960 --> 00:04:41.110 align:start position:0%
it to do was to summarize these results
so<00:04:39.240><c> it's</c><00:04:39.400><c> now</c><00:04:39.560><c> using</c><00:04:39.960><c> the</c><00:04:40.479><c> web</c><00:04:40.720><c> Surfer</c>

00:04:41.110 --> 00:04:41.120 align:start position:0%
so it's now using the web Surfer
 

00:04:41.120 --> 00:04:43.430 align:start position:0%
so it's now using the web Surfer
summarizer<00:04:41.800><c> LM</c><00:04:42.280><c> config</c><00:04:42.840><c> so</c><00:04:43.000><c> then</c><00:04:43.120><c> it</c><00:04:43.280><c> just</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
summarizer LM config so then it just
 

00:04:43.440 --> 00:04:45.749 align:start position:0%
summarizer LM config so then it just
does<00:04:43.560><c> a</c><00:04:43.680><c> summary</c><00:04:44.000><c> of</c><00:04:44.120><c> it</c><00:04:44.880><c> and</c><00:04:45.080><c> then</c><00:04:45.320><c> on</c><00:04:45.520><c> the</c>

00:04:45.749 --> 00:04:45.759 align:start position:0%
does a summary of it and then on the
 

00:04:45.759 --> 00:04:47.550 align:start position:0%
does a summary of it and then on the
third<00:04:46.120><c> task</c><00:04:46.479><c> I</c><00:04:46.600><c> asked</c><00:04:46.800><c> you</c><00:04:46.919><c> to</c><00:04:47.080><c> click</c><00:04:47.400><c> the</c>

00:04:47.550 --> 00:04:47.560 align:start position:0%
third task I asked you to click the
 

00:04:47.560 --> 00:04:50.150 align:start position:0%
third task I asked you to click the
getting<00:04:47.880><c> started</c><00:04:48.440><c> result</c><00:04:49.440><c> now</c><00:04:49.600><c> if</c><00:04:49.680><c> we</c><00:04:49.880><c> come</c>

00:04:50.150 --> 00:04:50.160 align:start position:0%
getting started result now if we come
 

00:04:50.160 --> 00:04:51.550 align:start position:0%
getting started result now if we come
back<00:04:50.400><c> up</c><00:04:50.600><c> here</c><00:04:50.800><c> I</c><00:04:50.880><c> believe</c><00:04:51.080><c> it's</c><00:04:51.199><c> the</c><00:04:51.320><c> third</c>

00:04:51.550 --> 00:04:51.560 align:start position:0%
back up here I believe it's the third
 

00:04:51.560 --> 00:04:53.510 align:start position:0%
back up here I believe it's the third
one<00:04:52.000><c> yeah</c><00:04:52.120><c> so</c><00:04:52.320><c> the</c><00:04:52.440><c> third</c><00:04:52.639><c> one</c><00:04:53.120><c> this</c><00:04:53.280><c> was</c><00:04:53.440><c> the</c>

00:04:53.510 --> 00:04:53.520 align:start position:0%
one yeah so the third one this was the
 

00:04:53.520 --> 00:04:54.830 align:start position:0%
one yeah so the third one this was the
third<00:04:53.759><c> result</c><00:04:54.039><c> so</c><00:04:54.120><c> it</c><00:04:54.160><c> was</c><00:04:54.320><c> getting</c><00:04:54.520><c> started</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
third result so it was getting started
 

00:04:54.840 --> 00:04:57.550 align:start position:0%
third result so it was getting started
with<00:04:55.000><c> autogen</c><00:04:55.800><c> so</c><00:04:56.000><c> it</c><00:04:56.199><c> went</c><00:04:56.479><c> and</c><00:04:56.759><c> clicked</c><00:04:57.280><c> on</c>

00:04:57.550 --> 00:04:57.560 align:start position:0%
with autogen so it went and clicked on
 

00:04:57.560 --> 00:04:59.350 align:start position:0%
with autogen so it went and clicked on
that<00:04:57.800><c> and</c><00:04:57.880><c> then</c><00:04:58.000><c> it's</c><00:04:58.240><c> actually</c><00:04:58.520><c> going</c><00:04:58.919><c> to</c><00:04:59.160><c> the</c>

00:04:59.350 --> 00:04:59.360 align:start position:0%
that and then it's actually going to the
 

00:04:59.360 --> 00:05:01.150 align:start position:0%
that and then it's actually going to the
add<00:04:59.720><c> address</c><00:05:00.240><c> so</c><00:05:00.440><c> getting</c><00:05:00.680><c> started</c><00:05:01.039><c> with</c>

00:05:01.150 --> 00:05:01.160 align:start position:0%
add address so getting started with
 

00:05:01.160 --> 00:05:03.150 align:start position:0%
add address so getting started with
autogen<00:05:02.000><c> it's</c><00:05:02.160><c> saying</c><00:05:02.440><c> that</c><00:05:02.560><c> I</c><00:05:02.639><c> could</c><00:05:02.800><c> scroll</c>

00:05:03.150 --> 00:05:03.160 align:start position:0%
autogen it's saying that I could scroll
 

00:05:03.160 --> 00:05:05.110 align:start position:0%
autogen it's saying that I could scroll
down<00:05:03.440><c> to</c><00:05:03.759><c> find</c><00:05:04.080><c> to</c><00:05:04.240><c> view</c><00:05:04.560><c> a</c><00:05:04.639><c> whole</c><00:05:04.840><c> another</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
down to find to view a whole another
 

00:05:05.120 --> 00:05:07.590 align:start position:0%
down to find to view a whole another
page<00:05:05.560><c> but</c><00:05:05.800><c> this</c><00:05:05.880><c> is</c><00:05:06.280><c> github's</c><00:05:07.080><c> Microsoft</c>

00:05:07.590 --> 00:05:07.600 align:start position:0%
page but this is github's Microsoft
 

00:05:07.600 --> 00:05:10.029 align:start position:0%
page but this is github's Microsoft
github's<00:05:08.120><c> getting</c><00:05:08.440><c> started</c><00:05:08.800><c> link</c><00:05:09.720><c> and</c><00:05:09.840><c> so</c>

00:05:10.029 --> 00:05:10.039 align:start position:0%
github's getting started link and so
 

00:05:10.039 --> 00:05:13.070 align:start position:0%
github's getting started link and so
getting<00:05:10.400><c> started</c><00:05:11.400><c> um</c><00:05:11.639><c> main</c><00:05:12.000><c> features</c><00:05:12.880><c> I</c><00:05:12.960><c> think</c>

00:05:13.070 --> 00:05:13.080 align:start position:0%
getting started um main features I think
 

00:05:13.080 --> 00:05:15.749 align:start position:0%
getting started um main features I think
it<00:05:13.240><c> actually</c><00:05:13.680><c> has</c><00:05:14.199><c> so</c><00:05:14.520><c> quick</c><00:05:14.720><c> start</c><00:05:15.080><c> yeah</c><00:05:15.199><c> so</c><00:05:15.600><c> a</c>

00:05:15.749 --> 00:05:15.759 align:start position:0%
it actually has so quick start yeah so a
 

00:05:15.759 --> 00:05:17.790 align:start position:0%
it actually has so quick start yeah so a
quick<00:05:15.880><c> start</c><00:05:16.120><c> on</c><00:05:16.199><c> the</c><00:05:16.280><c> GitHub</c><00:05:16.639><c> it</c><00:05:16.800><c> has</c><00:05:17.520><c> um</c><00:05:17.720><c> how</c>

00:05:17.790 --> 00:05:17.800 align:start position:0%
quick start on the GitHub it has um how
 

00:05:17.800 --> 00:05:20.590 align:start position:0%
quick start on the GitHub it has um how
to<00:05:18.039><c> execute</c><00:05:18.560><c> like</c><00:05:18.720><c> a</c><00:05:18.960><c> sample</c><00:05:19.960><c> uh</c><00:05:20.240><c> agent</c>

00:05:20.590 --> 00:05:20.600 align:start position:0%
to execute like a sample uh agent
 

00:05:20.600 --> 00:05:22.029 align:start position:0%
to execute like a sample uh agent
framework<00:05:20.960><c> for</c><00:05:21.080><c> AIO</c><00:05:21.400><c> genen</c><00:05:21.680><c> so</c><00:05:21.840><c> if</c><00:05:21.880><c> we</c>

00:05:22.029 --> 00:05:22.039 align:start position:0%
framework for AIO genen so if we
 

00:05:22.039 --> 00:05:24.029 align:start position:0%
framework for AIO genen so if we
actually<00:05:22.280><c> go</c><00:05:22.520><c> to</c><00:05:22.919><c> this</c><00:05:23.160><c> page</c><00:05:23.680><c> this</c><00:05:23.759><c> is</c><00:05:23.840><c> what</c><00:05:23.960><c> it</c>

00:05:24.029 --> 00:05:24.039 align:start position:0%
actually go to this page this is what it
 

00:05:24.039 --> 00:05:25.350 align:start position:0%
actually go to this page this is what it
actually<00:05:24.319><c> looks</c><00:05:24.560><c> like</c><00:05:24.759><c> we're</c><00:05:24.919><c> just</c><00:05:25.039><c> seeing</c><00:05:25.280><c> it</c>

00:05:25.350 --> 00:05:25.360 align:start position:0%
actually looks like we're just seeing it
 

00:05:25.360 --> 00:05:26.830 align:start position:0%
actually looks like we're just seeing it
in<00:05:25.560><c> text</c><00:05:25.840><c> form</c><00:05:26.280><c> right</c><00:05:26.400><c> so</c><00:05:26.479><c> for</c><00:05:26.600><c> another</c>

00:05:26.830 --> 00:05:26.840 align:start position:0%
in text form right so for another
 

00:05:26.840 --> 00:05:28.629 align:start position:0%
in text form right so for another
example<00:05:27.280><c> I</c><00:05:27.360><c> can</c><00:05:27.680><c> have</c><00:05:27.880><c> three</c><00:05:28.039><c> different</c><00:05:28.280><c> tasks</c>

00:05:28.629 --> 00:05:28.639 align:start position:0%
example I can have three different tasks
 

00:05:28.639 --> 00:05:30.189 align:start position:0%
example I can have three different tasks
already<00:05:28.919><c> defined</c><00:05:29.319><c> again</c><00:05:29.720><c> so</c><00:05:29.919><c> find</c>

00:05:30.189 --> 00:05:30.199 align:start position:0%
already defined again so find
 

00:05:30.199 --> 00:05:32.270 align:start position:0%
already defined again so find
Microsoft's<00:05:30.759><c> Wikipedia</c><00:05:31.319><c> page</c><00:05:31.720><c> scroll</c><00:05:32.080><c> down</c>

00:05:32.270 --> 00:05:32.280 align:start position:0%
Microsoft's Wikipedia page scroll down
 

00:05:32.280 --> 00:05:33.390 align:start position:0%
Microsoft's Wikipedia page scroll down
because<00:05:32.479><c> there's</c><00:05:32.600><c> gonna</c><00:05:32.759><c> be</c><00:05:32.840><c> a</c><00:05:32.880><c> lot</c><00:05:33.000><c> of</c><00:05:33.080><c> pages</c>

00:05:33.390 --> 00:05:33.400 align:start position:0%
because there's gonna be a lot of pages
 

00:05:33.400 --> 00:05:35.230 align:start position:0%
because there's gonna be a lot of pages
there<00:05:33.880><c> and</c><00:05:34.039><c> then</c><00:05:34.280><c> ask</c><00:05:34.520><c> what</c><00:05:34.639><c> is</c><00:05:34.759><c> their</c><00:05:34.919><c> total</c>

00:05:35.230 --> 00:05:35.240 align:start position:0%
there and then ask what is their total
 

00:05:35.240 --> 00:05:36.749 align:start position:0%
there and then ask what is their total
revenue<00:05:35.680><c> because</c><00:05:35.880><c> they'll</c><00:05:36.080><c> see</c><00:05:36.280><c> it</c><00:05:36.400><c> on</c><00:05:36.560><c> that</c>

00:05:36.749 --> 00:05:36.759 align:start position:0%
revenue because they'll see it on that
 

00:05:36.759 --> 00:05:39.110 align:start position:0%
revenue because they'll see it on that
page<00:05:37.160><c> okay</c><00:05:37.400><c> Ran</c><00:05:37.720><c> So</c><00:05:38.160><c> find</c><00:05:38.520><c> Microsoft's</c>

00:05:39.110 --> 00:05:39.120 align:start position:0%
page okay Ran So find Microsoft's
 

00:05:39.120 --> 00:05:41.629 align:start position:0%
page okay Ran So find Microsoft's
Wikipedia<00:05:39.840><c> page</c><00:05:40.840><c> um</c><00:05:41.000><c> you</c><00:05:41.080><c> know</c><00:05:41.280><c> so</c><00:05:41.440><c> there's</c>

00:05:41.629 --> 00:05:41.639 align:start position:0%
Wikipedia page um you know so there's
 

00:05:41.639 --> 00:05:44.110 align:start position:0%
Wikipedia page um you know so there's
showing<00:05:41.960><c> page</c><00:05:42.120><c> 10</c><00:05:42.440><c> 166</c><00:05:42.960><c> so</c><00:05:43.360><c> their</c><00:05:43.560><c> Wikipedia</c>

00:05:44.110 --> 00:05:44.120 align:start position:0%
showing page 10 166 so their Wikipedia
 

00:05:44.120 --> 00:05:47.230 align:start position:0%
showing page 10 166 so their Wikipedia
is<00:05:44.560><c> huge</c><00:05:45.000><c> basically</c><00:05:45.919><c> uh</c><00:05:46.080><c> so</c><00:05:46.560><c> this</c><00:05:46.680><c> is</c><00:05:46.840><c> the</c><00:05:47.160><c> this</c>

00:05:47.230 --> 00:05:47.240 align:start position:0%
is huge basically uh so this is the this
 

00:05:47.240 --> 00:05:48.670 align:start position:0%
is huge basically uh so this is the this
is<00:05:47.639><c> like</c><00:05:47.840><c> the</c><00:05:47.960><c> first</c><00:05:48.199><c> thing</c><00:05:48.319><c> that</c><00:05:48.400><c> you'll</c><00:05:48.560><c> see</c>

00:05:48.670 --> 00:05:48.680 align:start position:0%
is like the first thing that you'll see
 

00:05:48.680 --> 00:05:49.830 align:start position:0%
is like the first thing that you'll see
when<00:05:48.759><c> you</c><00:05:48.840><c> go</c><00:05:48.960><c> to</c><00:05:49.120><c> their</c><00:05:49.319><c> page</c><00:05:49.600><c> right</c><00:05:49.720><c> all</c>

00:05:49.830 --> 00:05:49.840 align:start position:0%
when you go to their page right all
 

00:05:49.840 --> 00:05:52.070 align:start position:0%
when you go to their page right all
their<00:05:50.039><c> services</c><00:05:51.039><c> the</c><00:05:51.160><c> revenue</c><00:05:51.600><c> and</c><00:05:51.720><c> I</c><00:05:51.880><c> guess</c>

00:05:52.070 --> 00:05:52.080 align:start position:0%
their services the revenue and I guess
 

00:05:52.080 --> 00:05:54.029 align:start position:0%
their services the revenue and I guess
all<00:05:52.360><c> and</c><00:05:52.600><c> other</c><00:05:52.800><c> things</c><00:05:53.080><c> so</c><00:05:53.440><c> have</c><00:05:53.560><c> it</c><00:05:53.720><c> scroll</c>

00:05:54.029 --> 00:05:54.039 align:start position:0%
all and other things so have it scroll
 

00:05:54.039 --> 00:05:56.309 align:start position:0%
all and other things so have it scroll
down<00:05:54.600><c> and</c><00:05:54.880><c> it's</c><00:05:55.080><c> going</c><00:05:55.199><c> to</c><00:05:55.400><c> show</c><00:05:55.960><c> some</c><00:05:56.120><c> more</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
down and it's going to show some more
 

00:05:56.319 --> 00:05:58.710 align:start position:0%
down and it's going to show some more
information<00:05:56.880><c> Awards</c><00:05:57.639><c> companies</c><00:05:58.639><c> uh</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
information Awards companies uh
 

00:05:58.720 --> 00:06:00.309 align:start position:0%
information Awards companies uh
charitable<00:05:59.199><c> stuff</c><00:05:59.600><c> in</c><00:05:59.720><c> case</c><00:05:59.880><c> it's</c><00:06:00.039><c> all</c><00:06:00.199><c> kind</c>

00:06:00.309 --> 00:06:00.319 align:start position:0%
charitable stuff in case it's all kind
 

00:06:00.319 --> 00:06:02.230 align:start position:0%
charitable stuff in case it's all kind
of<00:06:00.520><c> like</c><00:06:00.800><c> before</c><00:06:01.039><c> it</c><00:06:01.120><c> gets</c><00:06:01.280><c> to</c><00:06:01.479><c> the</c><00:06:01.600><c> summary</c><00:06:02.000><c> of</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
of like before it gets to the summary of
 

00:06:02.240 --> 00:06:04.590 align:start position:0%
of like before it gets to the summary of
different<00:06:02.600><c> timelines</c><00:06:03.160><c> and</c><00:06:03.360><c> history</c><00:06:03.840><c> of</c><00:06:04.479><c> uh</c>

00:06:04.590 --> 00:06:04.600 align:start position:0%
different timelines and history of uh
 

00:06:04.600 --> 00:06:06.390 align:start position:0%
different timelines and history of uh
Microsoft<00:06:05.400><c> then</c><00:06:05.560><c> I</c><00:06:05.680><c> said</c><00:06:05.960><c> what</c><00:06:06.080><c> is</c><00:06:06.240><c> their</c>

00:06:06.390 --> 00:06:06.400 align:start position:0%
Microsoft then I said what is their
 

00:06:06.400 --> 00:06:09.670 align:start position:0%
Microsoft then I said what is their
total<00:06:07.160><c> revenue</c><00:06:08.160><c> So</c><00:06:08.680><c> based</c><00:06:08.960><c> out</c><00:06:09.120><c> of</c><00:06:09.280><c> Redmond</c>

00:06:09.670 --> 00:06:09.680 align:start position:0%
total revenue So based out of Redmond
 

00:06:09.680 --> 00:06:11.670 align:start position:0%
total revenue So based out of Redmond
Washington<00:06:10.560><c> they</c><00:06:10.680><c> have</c><00:06:10.800><c> a</c><00:06:10.919><c> total</c><00:06:11.280><c> reported</c>

00:06:11.670 --> 00:06:11.680 align:start position:0%
Washington they have a total reported
 

00:06:11.680 --> 00:06:15.430 align:start position:0%
Washington they have a total reported
total<00:06:11.840><c> revenue</c><00:06:12.120><c> of</c><00:06:12.319><c> 21.9</c><00:06:13.319><c> billion</c><00:06:13.680><c> in</c><00:06:14.440><c> 2023</c>

00:06:15.430 --> 00:06:15.440 align:start position:0%
total revenue of 21.9 billion in 2023
 

00:06:15.440 --> 00:06:17.070 align:start position:0%
total revenue of 21.9 billion in 2023
okay<00:06:15.639><c> awesome</c><00:06:15.919><c> that</c><00:06:16.039><c> was</c><00:06:16.280><c> just</c><00:06:16.520><c> a</c><00:06:16.800><c> quick</c>

00:06:17.070 --> 00:06:17.080 align:start position:0%
okay awesome that was just a quick
 

00:06:17.080 --> 00:06:19.189 align:start position:0%
okay awesome that was just a quick
overview<00:06:17.639><c> of</c><00:06:17.759><c> using</c><00:06:18.160><c> the</c><00:06:18.280><c> web</c><00:06:18.520><c> serfer</c><00:06:18.840><c> agent</c>

00:06:19.189 --> 00:06:19.199 align:start position:0%
overview of using the web serfer agent
 

00:06:19.199 --> 00:06:20.390 align:start position:0%
overview of using the web serfer agent
the<00:06:19.280><c> only</c><00:06:19.400><c> thing</c><00:06:19.520><c> I</c><00:06:19.560><c> would</c><00:06:19.720><c> say</c><00:06:19.840><c> about</c><00:06:20.039><c> this</c><00:06:20.280><c> is</c>

00:06:20.390 --> 00:06:20.400 align:start position:0%
the only thing I would say about this is
 

00:06:20.400 --> 00:06:21.270 align:start position:0%
the only thing I would say about this is
you<00:06:20.479><c> can't</c><00:06:20.639><c> really</c><00:06:20.759><c> have</c><00:06:20.880><c> it</c><00:06:21.000><c> go</c><00:06:21.080><c> out</c><00:06:21.160><c> and</c>

00:06:21.270 --> 00:06:21.280 align:start position:0%
you can't really have it go out and
 

00:06:21.280 --> 00:06:22.950 align:start position:0%
you can't really have it go out and
search<00:06:21.560><c> for</c><00:06:21.720><c> something</c><00:06:22.400><c> and</c><00:06:22.479><c> then</c><00:06:22.639><c> come</c><00:06:22.800><c> back</c>

00:06:22.950 --> 00:06:22.960 align:start position:0%
search for something and then come back
 

00:06:22.960 --> 00:06:25.029 align:start position:0%
search for something and then come back
with<00:06:23.120><c> information</c><00:06:23.759><c> without</c><00:06:24.080><c> giving</c><00:06:24.319><c> it</c><00:06:24.880><c> kind</c>

00:06:25.029 --> 00:06:25.039 align:start position:0%
with information without giving it kind
 

00:06:25.039 --> 00:06:27.230 align:start position:0%
with information without giving it kind
of<00:06:25.440><c> exactly</c><00:06:26.120><c> where</c><00:06:26.319><c> to</c><00:06:26.479><c> find</c><00:06:26.639><c> it</c><00:06:26.759><c> on</c><00:06:26.919><c> that</c><00:06:27.039><c> web</c>

00:06:27.230 --> 00:06:27.240 align:start position:0%
of exactly where to find it on that web
 

00:06:27.240 --> 00:06:28.990 align:start position:0%
of exactly where to find it on that web
page<00:06:27.440><c> so</c><00:06:27.599><c> there's</c><00:06:27.800><c> some</c><00:06:28.080><c> preor</c><00:06:28.599><c> that</c><00:06:28.720><c> kind</c><00:06:28.880><c> has</c>

00:06:28.990 --> 00:06:29.000 align:start position:0%
page so there's some preor that kind has
 

00:06:29.000 --> 00:06:31.990 align:start position:0%
page so there's some preor that kind has
to<00:06:29.160><c> go</c><00:06:29.639><c> into</c><00:06:29.880><c> it</c><00:06:30.639><c> uh</c><00:06:31.160><c> but</c><00:06:31.360><c> you</c><00:06:31.479><c> could</c><00:06:31.639><c> set</c><00:06:31.840><c> up</c>

00:06:31.990 --> 00:06:32.000 align:start position:0%
to go into it uh but you could set up
 

00:06:32.000 --> 00:06:34.230 align:start position:0%
to go into it uh but you could set up
like<00:06:32.080><c> a</c><00:06:32.280><c> template</c><00:06:33.039><c> for</c><00:06:33.800><c> gathering</c>

00:06:34.230 --> 00:06:34.240 align:start position:0%
like a template for gathering
 

00:06:34.240 --> 00:06:36.309 align:start position:0%
like a template for gathering
information<00:06:34.880><c> that</c><00:06:35.080><c> might</c><00:06:35.560><c> be</c><00:06:35.720><c> changing</c><00:06:36.120><c> every</c>

00:06:36.309 --> 00:06:36.319 align:start position:0%
information that might be changing every
 

00:06:36.319 --> 00:06:37.870 align:start position:0%
information that might be changing every
day<00:06:36.479><c> so</c><00:06:36.639><c> if</c><00:06:36.720><c> you</c><00:06:36.800><c> want</c><00:06:36.880><c> to</c><00:06:37.080><c> get</c><00:06:37.319><c> the</c><00:06:37.560><c> latest</c>

00:06:37.870 --> 00:06:37.880 align:start position:0%
day so if you want to get the latest
 

00:06:37.880 --> 00:06:39.629 align:start position:0%
day so if you want to get the latest
blog<00:06:38.199><c> post</c><00:06:38.479><c> from</c><00:06:38.639><c> a</c><00:06:38.800><c> specific</c><00:06:39.199><c> website</c><00:06:39.560><c> you</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
blog post from a specific website you
 

00:06:39.639 --> 00:06:41.749 align:start position:0%
blog post from a specific website you
could<00:06:39.919><c> know</c><00:06:40.400><c> to</c><00:06:40.560><c> go</c><00:06:40.639><c> to</c><00:06:40.800><c> that</c><00:06:40.960><c> website</c><00:06:41.560><c> click</c>

00:06:41.749 --> 00:06:41.759 align:start position:0%
could know to go to that website click
 

00:06:41.759 --> 00:06:43.830 align:start position:0%
could know to go to that website click
on<00:06:41.919><c> the</c><00:06:42.080><c> latest</c><00:06:42.599><c> link</c><00:06:42.919><c> and</c><00:06:43.039><c> then</c><00:06:43.199><c> summarize</c>

00:06:43.830 --> 00:06:43.840 align:start position:0%
on the latest link and then summarize
 

00:06:43.840 --> 00:06:45.670 align:start position:0%
on the latest link and then summarize
that<00:06:44.000><c> blog</c><00:06:44.280><c> post</c><00:06:44.479><c> from</c><00:06:44.680><c> that</c><00:06:44.800><c> link</c><00:06:45.360><c> again</c><00:06:45.560><c> if</c>

00:06:45.670 --> 00:06:45.680 align:start position:0%
that blog post from that link again if
 

00:06:45.680 --> 00:06:47.550 align:start position:0%
that blog post from that link again if
you're<00:06:45.800><c> using</c><00:06:46.360><c> like</c><00:06:46.479><c> a</c><00:06:46.599><c> local</c><00:06:46.880><c> server</c><00:06:47.199><c> for</c><00:06:47.400><c> an</c>

00:06:47.550 --> 00:06:47.560 align:start position:0%
you're using like a local server for an
 

00:06:47.560 --> 00:06:49.909 align:start position:0%
you're using like a local server for an
open<00:06:47.800><c> source</c><00:06:48.080><c> llm</c><00:06:48.720><c> then</c><00:06:48.840><c> you</c><00:06:48.960><c> have</c><00:06:49.080><c> no</c><00:06:49.240><c> issues</c>

00:06:49.909 --> 00:06:49.919 align:start position:0%
open source llm then you have no issues
 

00:06:49.919 --> 00:06:51.189 align:start position:0%
open source llm then you have no issues
anyways<00:06:50.280><c> thank</c><00:06:50.400><c> you</c><00:06:50.479><c> for</c><00:06:50.639><c> watching</c><00:06:50.880><c> this</c><00:06:51.000><c> is</c>

00:06:51.189 --> 00:06:51.199 align:start position:0%
anyways thank you for watching this is
 

00:06:51.199 --> 00:06:52.749 align:start position:0%
anyways thank you for watching this is
kind<00:06:51.319><c> of</c><00:06:51.440><c> showcase</c><00:06:52.000><c> another</c><00:06:52.280><c> agent</c><00:06:52.560><c> that</c><00:06:52.680><c> you</c>

00:06:52.749 --> 00:06:52.759 align:start position:0%
kind of showcase another agent that you
 

00:06:52.759 --> 00:06:54.270 align:start position:0%
kind of showcase another agent that you
can<00:06:52.840><c> use</c><00:06:53.039><c> with</c><00:06:53.160><c> autogen</c><00:06:53.840><c> tell</c><00:06:54.000><c> me</c><00:06:54.120><c> what</c><00:06:54.199><c> you</c>

00:06:54.270 --> 00:06:54.280 align:start position:0%
can use with autogen tell me what you
 

00:06:54.280 --> 00:06:55.430 align:start position:0%
can use with autogen tell me what you
think<00:06:54.440><c> about</c><00:06:54.560><c> in</c><00:06:54.680><c> the</c><00:06:54.759><c> comments</c><00:06:55.039><c> section</c><00:06:55.280><c> down</c>

00:06:55.430 --> 00:06:55.440 align:start position:0%
think about in the comments section down
 

00:06:55.440 --> 00:06:56.990 align:start position:0%
think about in the comments section down
below<00:06:56.000><c> thank</c><00:06:56.120><c> you</c><00:06:56.199><c> for</c><00:06:56.360><c> watching</c><00:06:56.720><c> I'll</c><00:06:56.840><c> see</c>

00:06:56.990 --> 00:06:57.000 align:start position:0%
below thank you for watching I'll see
 

00:06:57.000 --> 00:06:59.599 align:start position:0%
below thank you for watching I'll see
you<00:06:57.120><c> next</c><00:06:57.319><c> time</c>

