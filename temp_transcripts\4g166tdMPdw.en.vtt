WEBVTT
Kind: captions
Language: en

00:00:00.799 --> 00:00:03.189 align:start position:0%
 
hello<00:00:01.400><c> welcome</c><00:00:01.760><c> back</c><00:00:01.880><c> to</c><00:00:02.040><c> another</c><00:00:02.399><c> video</c><00:00:02.840><c> in</c>

00:00:03.189 --> 00:00:03.199 align:start position:0%
hello welcome back to another video in
 

00:00:03.199 --> 00:00:05.470 align:start position:0%
hello welcome back to another video in
this<00:00:03.439><c> video</c><00:00:03.639><c> series</c><00:00:03.959><c> on</c><00:00:04.120><c> property</c><00:00:04.520><c> graphs</c><00:00:05.240><c> so</c>

00:00:05.470 --> 00:00:05.480 align:start position:0%
this video series on property graphs so
 

00:00:05.480 --> 00:00:07.389 align:start position:0%
this video series on property graphs so
in<00:00:05.640><c> this</c><00:00:05.799><c> video</c><00:00:06.080><c> we'll</c><00:00:06.279><c> look</c><00:00:06.480><c> into</c><00:00:06.879><c> extractors</c>

00:00:07.389 --> 00:00:07.399 align:start position:0%
in this video we'll look into extractors
 

00:00:07.399 --> 00:00:10.390 align:start position:0%
in this video we'll look into extractors
and<00:00:07.600><c> rers</c><00:00:08.000><c> and</c><00:00:08.200><c> property</c><00:00:08.559><c> graphs</c><00:00:09.400><c> so</c><00:00:10.040><c> uh</c><00:00:10.160><c> we'll</c>

00:00:10.390 --> 00:00:10.400 align:start position:0%
and rers and property graphs so uh we'll
 

00:00:10.400 --> 00:00:12.669 align:start position:0%
and rers and property graphs so uh we'll
give<00:00:10.639><c> a</c><00:00:10.840><c> high</c><00:00:11.040><c> level</c><00:00:11.440><c> overview</c><00:00:12.000><c> of</c><00:00:12.320><c> how</c><00:00:12.519><c> you</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
give a high level overview of how you
 

00:00:12.679 --> 00:00:15.589 align:start position:0%
give a high level overview of how you
can<00:00:13.160><c> uh</c><00:00:13.679><c> Define</c><00:00:14.080><c> these</c><00:00:14.320><c> extractors</c><00:00:15.120><c> and</c><00:00:15.480><c> uh</c>

00:00:15.589 --> 00:00:15.599 align:start position:0%
can uh Define these extractors and uh
 

00:00:15.599 --> 00:00:17.750 align:start position:0%
can uh Define these extractors and uh
retrievers<00:00:16.240><c> different</c><00:00:16.560><c> retrievers</c><00:00:17.359><c> how</c><00:00:17.520><c> can</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
retrievers different retrievers how can
 

00:00:17.760 --> 00:00:20.429 align:start position:0%
retrievers different retrievers how can
you<00:00:17.920><c> plug</c><00:00:18.199><c> into</c><00:00:18.880><c> property</c><00:00:19.279><c> graphs</c><00:00:19.800><c> and</c><00:00:20.000><c> then</c>

00:00:20.429 --> 00:00:20.439 align:start position:0%
you plug into property graphs and then
 

00:00:20.439 --> 00:00:24.070 align:start position:0%
you plug into property graphs and then
uh<00:00:21.160><c> after</c><00:00:21.400><c> creating</c><00:00:22.199><c> indexes</c><00:00:23.199><c> uh</c><00:00:23.720><c> when</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
uh after creating indexes uh when
 

00:00:24.080 --> 00:00:25.349 align:start position:0%
uh after creating indexes uh when
querying<00:00:24.480><c> how</c><00:00:24.640><c> can</c><00:00:24.760><c> you</c><00:00:24.840><c> plug</c><00:00:25.080><c> into</c><00:00:25.240><c> the</c>

00:00:25.349 --> 00:00:25.359 align:start position:0%
querying how can you plug into the
 

00:00:25.359 --> 00:00:28.269 align:start position:0%
querying how can you plug into the
reters<00:00:25.840><c> different</c><00:00:26.160><c> reters</c><00:00:27.160><c> right</c><00:00:27.679><c> so</c><00:00:28.000><c> we</c><00:00:28.080><c> will</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
reters different reters right so we will
 

00:00:28.279 --> 00:00:31.509 align:start position:0%
reters different reters right so we will
not<00:00:28.560><c> run</c><00:00:28.960><c> any</c><00:00:29.160><c> of</c><00:00:29.359><c> these</c><00:00:30.320><c> uh</c><00:00:30.840><c> uh</c><00:00:31.000><c> parts</c><00:00:31.240><c> of</c><00:00:31.400><c> the</c>

00:00:31.509 --> 00:00:31.519 align:start position:0%
not run any of these uh uh parts of the
 

00:00:31.519 --> 00:00:33.709 align:start position:0%
not run any of these uh uh parts of the
notebook<00:00:32.279><c> so</c><00:00:32.480><c> we'll</c><00:00:32.800><c> just</c><00:00:33.040><c> give</c><00:00:33.280><c> a</c><00:00:33.440><c> walk</c>

00:00:33.709 --> 00:00:33.719 align:start position:0%
notebook so we'll just give a walk
 

00:00:33.719 --> 00:00:36.150 align:start position:0%
notebook so we'll just give a walk
through<00:00:34.120><c> of</c><00:00:34.360><c> how</c><00:00:34.520><c> you</c><00:00:34.680><c> can</c><00:00:35.160><c> uh</c><00:00:35.280><c> create</c><00:00:35.640><c> them</c><00:00:36.040><c> in</c>

00:00:36.150 --> 00:00:36.160 align:start position:0%
through of how you can uh create them in
 

00:00:36.160 --> 00:00:38.470 align:start position:0%
through of how you can uh create them in
the<00:00:36.280><c> next</c><00:00:36.480><c> notebook</c><00:00:37.040><c> we'll</c><00:00:37.559><c> uh</c><00:00:37.680><c> use</c><00:00:38.040><c> the</c><00:00:38.200><c> same</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
the next notebook we'll uh use the same
 

00:00:38.480 --> 00:00:41.990 align:start position:0%
the next notebook we'll uh use the same
things<00:00:39.239><c> and</c><00:00:40.000><c> um</c><00:00:40.440><c> plug</c><00:00:40.719><c> in</c><00:00:40.920><c> the</c><00:00:41.200><c> extractors</c><00:00:41.719><c> and</c>

00:00:41.990 --> 00:00:42.000 align:start position:0%
things and um plug in the extractors and
 

00:00:42.000 --> 00:00:44.830 align:start position:0%
things and um plug in the extractors and
reters<00:00:43.000><c> and</c><00:00:43.200><c> for</c><00:00:43.440><c> creating</c><00:00:43.840><c> property</c><00:00:44.200><c> graphs</c>

00:00:44.830 --> 00:00:44.840 align:start position:0%
reters and for creating property graphs
 

00:00:44.840 --> 00:00:46.750 align:start position:0%
reters and for creating property graphs
so<00:00:45.000><c> let's</c><00:00:45.239><c> get</c><00:00:45.399><c> started</c><00:00:45.719><c> with</c>

00:00:46.750 --> 00:00:46.760 align:start position:0%
so let's get started with
 

00:00:46.760 --> 00:00:50.630 align:start position:0%
so let's get started with
it<00:00:47.800><c> so</c><00:00:48.800><c> yeah</c><00:00:49.039><c> so</c><00:00:49.239><c> we'll</c><00:00:49.600><c> first</c><00:00:49.840><c> start</c><00:00:50.120><c> with</c><00:00:50.520><c> uh</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
it so yeah so we'll first start with uh
 

00:00:50.640 --> 00:00:52.950 align:start position:0%
it so yeah so we'll first start with uh
how<00:00:50.800><c> can</c><00:00:50.960><c> you</c><00:00:51.239><c> build</c><00:00:51.520><c> and</c><00:00:51.960><c> uh</c><00:00:52.079><c> use</c><00:00:52.520><c> property</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
how can you build and uh use property
 

00:00:52.960 --> 00:00:56.590 align:start position:0%
how can you build and uh use property
graphs<00:00:53.800><c> so</c><00:00:54.680><c> you</c><00:00:54.879><c> basically</c><00:00:55.320><c> get</c><00:00:55.680><c> documents</c>

00:00:56.590 --> 00:00:56.600 align:start position:0%
graphs so you basically get documents
 

00:00:56.600 --> 00:00:59.069 align:start position:0%
graphs so you basically get documents
and<00:00:56.800><c> then</c><00:00:57.199><c> the</c><00:00:57.320><c> llm</c><00:00:57.920><c> and</c><00:00:58.120><c> Ming</c><00:00:58.480><c> model</c><00:00:58.960><c> these</c>

00:00:59.069 --> 00:00:59.079 align:start position:0%
and then the llm and Ming model these
 

00:00:59.079 --> 00:01:00.990 align:start position:0%
and then the llm and Ming model these
are<00:00:59.280><c> the</c><00:00:59.399><c> default</c><00:00:59.719><c> ones</c><00:01:00.239><c> you</c><00:01:00.359><c> can</c><00:01:00.640><c> still</c>

00:01:00.990 --> 00:01:01.000 align:start position:0%
are the default ones you can still
 

00:01:01.000 --> 00:01:02.869 align:start position:0%
are the default ones you can still
Define<00:01:01.399><c> them</c><00:01:01.760><c> and</c><00:01:01.920><c> set</c><00:01:02.199><c> them</c><00:01:02.480><c> and</c><00:01:02.640><c> then</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
Define them and set them and then
 

00:01:02.879 --> 00:01:05.189 align:start position:0%
Define them and set them and then
include<00:01:03.280><c> them</c><00:01:04.000><c> here</c><00:01:04.400><c> so</c><00:01:04.600><c> property</c><00:01:04.960><c> graph</c>

00:01:05.189 --> 00:01:05.199 align:start position:0%
include them here so property graph
 

00:01:05.199 --> 00:01:07.190 align:start position:0%
include them here so property graph
index<00:01:05.519><c> from</c><00:01:05.799><c> documents</c><00:01:06.280><c> of</c><00:01:06.520><c> documents</c><00:01:06.920><c> will</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
index from documents of documents will
 

00:01:07.200 --> 00:01:10.990 align:start position:0%
index from documents of documents will
create<00:01:07.520><c> index</c><00:01:08.080><c> and</c><00:01:08.240><c> then</c><00:01:08.360><c> you</c><00:01:08.479><c> can</c><00:01:08.680><c> use</c><00:01:08.920><c> it</c><00:01:10.000><c> so</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
create index and then you can use it so
 

00:01:11.000 --> 00:01:12.830 align:start position:0%
create index and then you can use it so
uh<00:01:11.119><c> include</c><00:01:11.520><c> text</c><00:01:11.920><c> is</c><00:01:12.159><c> like</c><00:01:12.360><c> as</c><00:01:12.479><c> I</c><00:01:12.600><c> said</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
uh include text is like as I said
 

00:01:12.840 --> 00:01:15.830 align:start position:0%
uh include text is like as I said
earlier<00:01:13.240><c> in</c><00:01:13.360><c> the</c><00:01:13.640><c> previous</c><00:01:14.040><c> video</c><00:01:14.640><c> you</c><00:01:14.759><c> can</c><00:01:15.280><c> uh</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
earlier in the previous video you can uh
 

00:01:15.840 --> 00:01:17.749 align:start position:0%
earlier in the previous video you can uh
return<00:01:16.080><c> the</c><00:01:16.200><c> nodes</c><00:01:17.000><c> and</c><00:01:17.200><c> along</c><00:01:17.479><c> with</c><00:01:17.640><c> the</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
return the nodes and along with the
 

00:01:17.759 --> 00:01:20.190 align:start position:0%
return the nodes and along with the
source<00:01:18.119><c> text</c><00:01:18.439><c> as</c><00:01:18.600><c> well</c><00:01:19.040><c> or</c><00:01:19.200><c> else</c><00:01:19.400><c> you</c><00:01:19.520><c> can</c><00:01:20.040><c> uh</c>

00:01:20.190 --> 00:01:20.200 align:start position:0%
source text as well or else you can uh
 

00:01:20.200 --> 00:01:22.590 align:start position:0%
source text as well or else you can uh
just<00:01:20.400><c> return</c><00:01:20.759><c> the</c><00:01:21.119><c> uh</c><00:01:21.200><c> nodes</c><00:01:21.680><c> as</c><00:01:21.799><c> well</c><00:01:22.400><c> so</c>

00:01:22.590 --> 00:01:22.600 align:start position:0%
just return the uh nodes as well so
 

00:01:22.600 --> 00:01:24.429 align:start position:0%
just return the uh nodes as well so
include<00:01:23.000><c> text</c><00:01:23.280><c> includes</c><00:01:23.600><c> a</c><00:01:23.720><c> source</c><00:01:24.040><c> text</c><00:01:24.320><c> as</c>

00:01:24.429 --> 00:01:24.439 align:start position:0%
include text includes a source text as
 

00:01:24.439 --> 00:01:26.789 align:start position:0%
include text includes a source text as
well<00:01:24.600><c> for</c><00:01:24.799><c> these</c><00:01:25.000><c> notes</c><00:01:26.000><c> so</c><00:01:26.360><c> and</c><00:01:26.520><c> then</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
well for these notes so and then
 

00:01:26.799 --> 00:01:28.950 align:start position:0%
well for these notes so and then
retriever<00:01:27.280><c> do</c><00:01:27.479><c> retrieve</c><00:01:28.119><c> uh</c><00:01:28.240><c> we</c><00:01:28.520><c> retrieve</c><00:01:28.880><c> the</c>

00:01:28.950 --> 00:01:28.960 align:start position:0%
retriever do retrieve uh we retrieve the
 

00:01:28.960 --> 00:01:30.830 align:start position:0%
retriever do retrieve uh we retrieve the
notes<00:01:29.640><c> and</c><00:01:29.759><c> then</c><00:01:29.960><c> then</c><00:01:30.079><c> you</c><00:01:30.159><c> can</c><00:01:30.400><c> even</c><00:01:30.600><c> build</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
notes and then then you can even build
 

00:01:30.840 --> 00:01:33.270 align:start position:0%
notes and then then you can even build
the<00:01:31.040><c> query</c><00:01:31.360><c> engine</c><00:01:32.159><c> and</c><00:01:32.360><c> generate</c><00:01:32.680><c> a</c><00:01:32.840><c> response</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
the query engine and generate a response
 

00:01:33.280 --> 00:01:36.149 align:start position:0%
the query engine and generate a response
accordingly<00:01:34.159><c> you</c><00:01:34.280><c> can</c><00:01:35.040><c> save</c><00:01:35.360><c> the</c><00:01:35.520><c> index</c><00:01:35.960><c> and</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
accordingly you can save the index and
 

00:01:36.159 --> 00:01:40.109 align:start position:0%
accordingly you can save the index and
load<00:01:36.479><c> the</c><00:01:36.600><c> index</c><00:01:37.079><c> again</c><00:01:38.079><c> and</c><00:01:38.600><c> uh</c><00:01:39.600><c> and</c><00:01:39.799><c> also</c><00:01:40.040><c> if</c>

00:01:40.109 --> 00:01:40.119 align:start position:0%
load the index again and uh and also if
 

00:01:40.119 --> 00:01:42.950 align:start position:0%
load the index again and uh and also if
you<00:01:40.280><c> have</c><00:01:40.439><c> already</c><00:01:40.799><c> existing</c><00:01:41.159><c> index</c><00:01:41.960><c> you</c><00:01:42.119><c> can</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
you have already existing index you can
 

00:01:42.960 --> 00:01:45.550 align:start position:0%
you have already existing index you can
just<00:01:43.479><c> get</c><00:01:43.799><c> the</c><00:01:44.560><c> property</c><00:01:44.920><c> graph</c><00:01:45.159><c> store</c><00:01:45.439><c> from</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
just get the property graph store from
 

00:01:45.560 --> 00:01:47.469 align:start position:0%
just get the property graph store from
the<00:01:45.680><c> graph</c><00:01:45.920><c> store</c><00:01:46.200><c> and</c><00:01:46.360><c> then</c><00:01:46.640><c> Vector</c><00:01:46.960><c> store</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
the graph store and then Vector store
 

00:01:47.479 --> 00:01:50.510 align:start position:0%
the graph store and then Vector store
and<00:01:47.640><c> llm</c><00:01:48.280><c> get</c><00:01:48.479><c> the</c><00:01:48.640><c> index</c><00:01:49.479><c> and</c><00:01:49.719><c> again</c><00:01:50.159><c> uh</c><00:01:50.280><c> use</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
and llm get the index and again uh use
 

00:01:50.520 --> 00:01:52.709 align:start position:0%
and llm get the index and again uh use
it<00:01:51.119><c> for</c><00:01:51.320><c> the</c><00:01:51.439><c> retriever</c><00:01:52.000><c> as</c><00:01:52.119><c> well</c><00:01:52.280><c> as</c><00:01:52.439><c> Square</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
it for the retriever as well as Square
 

00:01:52.719 --> 00:01:54.670 align:start position:0%
it for the retriever as well as Square
engine<00:01:53.040><c> purposes</c><00:01:54.000><c> so</c><00:01:54.159><c> that's</c><00:01:54.320><c> how</c><00:01:54.439><c> you</c><00:01:54.520><c> can</c>

00:01:54.670 --> 00:01:54.680 align:start position:0%
engine purposes so that's how you can
 

00:01:54.680 --> 00:01:56.950 align:start position:0%
engine purposes so that's how you can
build<00:01:54.880><c> the</c><00:01:55.040><c> basic</c><00:01:55.479><c> uh</c><00:01:55.600><c> property</c><00:01:56.000><c> graphs</c><00:01:56.759><c> now</c>

00:01:56.950 --> 00:01:56.960 align:start position:0%
build the basic uh property graphs now
 

00:01:56.960 --> 00:01:59.069 align:start position:0%
build the basic uh property graphs now
if<00:01:57.039><c> you</c><00:01:57.159><c> want</c><00:01:57.360><c> to</c><00:01:57.560><c> have</c><00:01:57.759><c> different</c><00:01:58.119><c> extractors</c>

00:01:59.069 --> 00:01:59.079 align:start position:0%
if you want to have different extractors
 

00:01:59.079 --> 00:02:01.270 align:start position:0%
if you want to have different extractors
uh<00:01:59.200><c> involved</c><00:01:59.560><c> in</c><00:01:59.640><c> it</c><00:02:00.119><c> right</c><00:02:00.560><c> while</c><00:02:00.840><c> creating</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
uh involved in it right while creating
 

00:02:01.280 --> 00:02:04.310 align:start position:0%
uh involved in it right while creating
property<00:02:01.640><c> graph</c><00:02:01.880><c> index</c><00:02:02.799><c> so</c><00:02:03.360><c> you</c>

00:02:04.310 --> 00:02:04.320 align:start position:0%
property graph index so you
 

00:02:04.320 --> 00:02:07.990 align:start position:0%
property graph index so you
basically<00:02:05.320><c> assign</c><00:02:05.719><c> them</c><00:02:06.439><c> as</c><00:02:06.680><c> kg</c><00:02:07.039><c> extractors</c>

00:02:07.990 --> 00:02:08.000 align:start position:0%
basically assign them as kg extractors
 

00:02:08.000 --> 00:02:09.830 align:start position:0%
basically assign them as kg extractors
so<00:02:08.200><c> extractor</c><00:02:08.640><c> one</c><00:02:08.879><c> extractor</c><00:02:09.280><c> 2</c><00:02:09.560><c> we</c><00:02:09.679><c> have</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
so extractor one extractor 2 we have
 

00:02:09.840 --> 00:02:13.430 align:start position:0%
so extractor one extractor 2 we have
seen<00:02:10.479><c> uh</c><00:02:10.599><c> implicit</c><00:02:11.360><c> uh</c><00:02:12.080><c> path</c><00:02:12.440><c> extractor</c>

00:02:13.430 --> 00:02:13.440 align:start position:0%
seen uh implicit uh path extractor
 

00:02:13.440 --> 00:02:16.509 align:start position:0%
seen uh implicit uh path extractor
simple<00:02:13.760><c> LM</c><00:02:14.120><c> extractor</c><00:02:14.640><c> schema</c><00:02:15.319><c> llm</c><00:02:15.959><c> extractor</c>

00:02:16.509 --> 00:02:16.519 align:start position:0%
simple LM extractor schema llm extractor
 

00:02:16.519 --> 00:02:18.229 align:start position:0%
simple LM extractor schema llm extractor
right<00:02:16.760><c> so</c><00:02:17.120><c> you</c><00:02:17.200><c> can</c><00:02:17.360><c> include</c><00:02:17.680><c> all</c><00:02:17.879><c> three</c><00:02:18.040><c> of</c>

00:02:18.229 --> 00:02:18.239 align:start position:0%
right so you can include all three of
 

00:02:18.239 --> 00:02:21.030 align:start position:0%
right so you can include all three of
them<00:02:18.519><c> or</c><00:02:19.519><c> any</c><00:02:19.680><c> one</c><00:02:19.840><c> of</c><00:02:20.000><c> them</c><00:02:20.239><c> as</c><00:02:20.400><c> well</c><00:02:20.760><c> as</c><00:02:20.879><c> you</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
them or any one of them as well as you
 

00:02:21.040 --> 00:02:23.670 align:start position:0%
them or any one of them as well as you
like<00:02:21.959><c> okay</c><00:02:22.319><c> and</c><00:02:22.560><c> in</c><00:02:22.720><c> fact</c><00:02:23.040><c> once</c><00:02:23.239><c> you</c><00:02:23.400><c> have</c><00:02:23.560><c> the</c>

00:02:23.670 --> 00:02:23.680 align:start position:0%
like okay and in fact once you have the
 

00:02:23.680 --> 00:02:25.190 align:start position:0%
like okay and in fact once you have the
index<00:02:24.040><c> created</c><00:02:24.400><c> you</c><00:02:24.480><c> can</c><00:02:24.640><c> insert</c><00:02:25.000><c> the</c>

00:02:25.190 --> 00:02:25.200 align:start position:0%
index created you can insert the
 

00:02:25.200 --> 00:02:27.390 align:start position:0%
index created you can insert the
document<00:02:26.160><c> you</c><00:02:26.280><c> can</c><00:02:26.440><c> insert</c><00:02:26.800><c> nodes</c><00:02:27.120><c> different</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
document you can insert nodes different
 

00:02:27.400 --> 00:02:30.550 align:start position:0%
document you can insert nodes different
nodes<00:02:27.800><c> as</c><00:02:27.959><c> well</c><00:02:28.879><c> okay</c><00:02:29.480><c> so</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
nodes as well okay so
 

00:02:30.560 --> 00:02:33.309 align:start position:0%
nodes as well okay so
yeah<00:02:30.800><c> that's</c><00:02:31.000><c> it</c><00:02:31.280><c> so</c><00:02:31.560><c> let's</c><00:02:31.920><c> uh</c><00:02:32.040><c> look</c><00:02:32.239><c> into</c><00:02:32.959><c> uh</c>

00:02:33.309 --> 00:02:33.319 align:start position:0%
yeah that's it so let's uh look into uh
 

00:02:33.319 --> 00:02:37.309 align:start position:0%
yeah that's it so let's uh look into uh
these<00:02:34.040><c> extractors</c><00:02:34.720><c> now</c><00:02:35.319><c> so</c><00:02:35.560><c> by</c><00:02:35.760><c> default</c><00:02:36.400><c> uh</c><00:02:36.560><c> if</c>

00:02:37.309 --> 00:02:37.319 align:start position:0%
these extractors now so by default uh if
 

00:02:37.319 --> 00:02:39.910 align:start position:0%
these extractors now so by default uh if
extractors<00:02:37.879><c> are</c><00:02:38.120><c> not</c><00:02:38.440><c> provided</c><00:02:39.440><c> uh</c><00:02:39.519><c> we'll</c><00:02:39.720><c> use</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
extractors are not provided uh we'll use
 

00:02:39.920 --> 00:02:41.710 align:start position:0%
extractors are not provided uh we'll use
Simple<00:02:40.200><c> LM</c><00:02:40.760><c> path</c><00:02:41.000><c> extractor</c><00:02:41.440><c> and</c><00:02:41.560><c> then</c>

00:02:41.710 --> 00:02:41.720 align:start position:0%
Simple LM path extractor and then
 

00:02:41.720 --> 00:02:44.550 align:start position:0%
Simple LM path extractor and then
implicit<00:02:42.239><c> path</c><00:02:42.560><c> extractor</c><00:02:43.080><c> as</c><00:02:43.239><c> well</c><00:02:43.959><c> okay</c><00:02:44.400><c> and</c>

00:02:44.550 --> 00:02:44.560 align:start position:0%
implicit path extractor as well okay and
 

00:02:44.560 --> 00:02:47.030 align:start position:0%
implicit path extractor as well okay and
then<00:02:44.760><c> Max</c><00:02:45.200><c> paths</c><00:02:45.519><c> per</c><00:02:45.760><c> chk</c><00:02:46.080><c> equal</c><00:02:46.360><c> to</c><00:02:46.640><c> 10</c><00:02:46.920><c> that</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
then Max paths per chk equal to 10 that
 

00:02:47.040 --> 00:02:49.430 align:start position:0%
then Max paths per chk equal to 10 that
means<00:02:47.519><c> for</c><00:02:47.720><c> each</c><00:02:47.920><c> Chunk</c><00:02:48.280><c> we</c><00:02:48.440><c> create</c><00:02:48.680><c> 10</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
means for each Chunk we create 10
 

00:02:49.440 --> 00:02:52.270 align:start position:0%
means for each Chunk we create 10
triplets<00:02:50.440><c> right</c><00:02:50.760><c> so</c><00:02:51.519><c> so</c><00:02:51.680><c> this</c><00:02:51.760><c> is</c><00:02:51.879><c> how</c><00:02:52.159><c> a</c>

00:02:52.270 --> 00:02:52.280 align:start position:0%
triplets right so so this is how a
 

00:02:52.280 --> 00:02:54.670 align:start position:0%
triplets right so so this is how a
simple<00:02:52.760><c> LM</c><00:02:53.080><c> paath</c><00:02:53.360><c> extractor</c><00:02:53.800><c> is</c><00:02:54.080><c> created</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
simple LM paath extractor is created
 

00:02:54.680 --> 00:02:58.190 align:start position:0%
simple LM paath extractor is created
let's<00:02:55.159><c> look</c><00:02:55.400><c> into</c><00:02:56.000><c> more</c><00:02:56.920><c> in</c><00:02:57.159><c> details</c><00:02:57.680><c> here</c><00:02:58.000><c> so</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
let's look into more in details here so
 

00:02:58.200 --> 00:03:01.350 align:start position:0%
let's look into more in details here so
here<00:02:58.360><c> is</c><00:02:58.480><c> a</c><00:02:58.640><c> prompt</c><00:02:59.080><c> here</c><00:02:59.400><c> for</c><00:02:59.959><c> creating</c><00:03:00.360><c> those</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
here is a prompt here for creating those
 

00:03:01.360 --> 00:03:03.430 align:start position:0%
here is a prompt here for creating those
uh<00:03:01.519><c> tripletes</c><00:03:02.280><c> some</c><00:03:02.480><c> text</c><00:03:02.720><c> is</c><00:03:02.840><c> provided</c><00:03:03.200><c> below</c>

00:03:03.430 --> 00:03:03.440 align:start position:0%
uh tripletes some text is provided below
 

00:03:03.440 --> 00:03:06.550 align:start position:0%
uh tripletes some text is provided below
you<00:03:03.560><c> and</c><00:03:03.680><c> the</c><00:03:03.799><c> text</c><00:03:04.040><c> extract</c><00:03:04.400><c> up</c><00:03:04.560><c> to</c><00:03:05.440><c> uh</c><00:03:05.599><c> Max</c><00:03:05.920><c> 10</c>

00:03:06.550 --> 00:03:06.560 align:start position:0%
you and the text extract up to uh Max 10
 

00:03:06.560 --> 00:03:08.589 align:start position:0%
you and the text extract up to uh Max 10
template<00:03:07.239><c> uh</c><00:03:07.560><c> tripat</c><00:03:08.040><c> in</c><00:03:08.120><c> the</c><00:03:08.239><c> form</c><00:03:08.400><c> of</c>

00:03:08.589 --> 00:03:08.599 align:start position:0%
template uh tripat in the form of
 

00:03:08.599 --> 00:03:10.270 align:start position:0%
template uh tripat in the form of
subject<00:03:08.920><c> predicate</c><00:03:09.360><c> object</c><00:03:09.599><c> on</c><00:03:09.760><c> each</c><00:03:09.959><c> line</c>

00:03:10.270 --> 00:03:10.280 align:start position:0%
subject predicate object on each line
 

00:03:10.280 --> 00:03:12.670 align:start position:0%
subject predicate object on each line
and<00:03:10.440><c> avoid</c><00:03:10.760><c> stop</c><00:03:11.200><c> wordss</c><00:03:12.200><c> okay</c><00:03:12.360><c> this</c><00:03:12.440><c> is</c><00:03:12.519><c> a</c>

00:03:12.670 --> 00:03:12.680 align:start position:0%
and avoid stop wordss okay this is a
 

00:03:12.680 --> 00:03:14.270 align:start position:0%
and avoid stop wordss okay this is a
prompt<00:03:12.959><c> you</c><00:03:13.080><c> send</c><00:03:13.239><c> it</c><00:03:13.319><c> to</c><00:03:13.480><c> llm</c><00:03:13.840><c> along</c><00:03:14.080><c> with</c><00:03:14.200><c> the</c>

00:03:14.270 --> 00:03:14.280 align:start position:0%
prompt you send it to llm along with the
 

00:03:14.280 --> 00:03:16.190 align:start position:0%
prompt you send it to llm along with the
text<00:03:14.519><c> Chunk</c><00:03:14.920><c> it</c><00:03:15.080><c> creates</c><00:03:15.440><c> this</c>

00:03:16.190 --> 00:03:16.200 align:start position:0%
text Chunk it creates this
 

00:03:16.200 --> 00:03:18.630 align:start position:0%
text Chunk it creates this
prompts<00:03:17.200><c> uh</c><00:03:17.360><c> it</c><00:03:17.519><c> creates</c><00:03:17.920><c> these</c><00:03:18.080><c> triplets</c>

00:03:18.630 --> 00:03:18.640 align:start position:0%
prompts uh it creates these triplets
 

00:03:18.640 --> 00:03:21.350 align:start position:0%
prompts uh it creates these triplets
sorry<00:03:19.440><c> and</c><00:03:19.599><c> then</c><00:03:20.080><c> uh</c><00:03:20.200><c> once</c><00:03:20.400><c> you</c><00:03:20.560><c> have</c><00:03:20.799><c> this</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
sorry and then uh once you have this
 

00:03:21.360 --> 00:03:23.670 align:start position:0%
sorry and then uh once you have this
output<00:03:21.760><c> you</c><00:03:21.840><c> can</c><00:03:22.080><c> pass</c><00:03:22.400><c> them</c><00:03:23.120><c> and</c><00:03:23.400><c> you</c><00:03:23.519><c> can</c>

00:03:23.670 --> 00:03:23.680 align:start position:0%
output you can pass them and you can
 

00:03:23.680 --> 00:03:25.830 align:start position:0%
output you can pass them and you can
provide<00:03:24.000><c> the</c><00:03:24.120><c> pass</c><00:03:24.400><c> function</c><00:03:24.720><c> as</c><00:03:24.879><c> well</c><00:03:25.640><c> and</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
provide the pass function as well and
 

00:03:25.840 --> 00:03:30.149 align:start position:0%
provide the pass function as well and
then<00:03:26.519><c> um</c><00:03:27.040><c> then</c><00:03:27.200><c> use</c><00:03:27.640><c> it</c><00:03:28.640><c> um</c><00:03:29.000><c> triplets</c><00:03:29.879><c> index</c>

00:03:30.149 --> 00:03:30.159 align:start position:0%
then um then use it um triplets index
 

00:03:30.159 --> 00:03:30.869 align:start position:0%
then um then use it um triplets index
them

00:03:30.869 --> 00:03:30.879 align:start position:0%
them
 

00:03:30.879 --> 00:03:33.070 align:start position:0%
them
accordingly<00:03:31.879><c> so</c><00:03:32.280><c> and</c><00:03:32.439><c> the</c><00:03:32.560><c> next</c><00:03:32.760><c> one</c><00:03:32.920><c> is</c>

00:03:33.070 --> 00:03:33.080 align:start position:0%
accordingly so and the next one is
 

00:03:33.080 --> 00:03:35.070 align:start position:0%
accordingly so and the next one is
implicit<00:03:33.560><c> path</c><00:03:33.760><c> extractor</c><00:03:34.360><c> as</c><00:03:34.480><c> we</c><00:03:34.599><c> said</c><00:03:34.840><c> using</c>

00:03:35.070 --> 00:03:35.080 align:start position:0%
implicit path extractor as we said using
 

00:03:35.080 --> 00:03:38.110 align:start position:0%
implicit path extractor as we said using
the<00:03:35.239><c> relationships</c><00:03:35.879><c> of</c><00:03:36.280><c> nodes</c><00:03:36.920><c> we</c><00:03:37.360><c> can</c><00:03:37.560><c> get</c>

00:03:38.110 --> 00:03:38.120 align:start position:0%
the relationships of nodes we can get
 

00:03:38.120 --> 00:03:40.509 align:start position:0%
the relationships of nodes we can get
implicit<00:03:38.599><c> path</c><00:03:38.920><c> extractor</c><00:03:39.760><c> uh</c><00:03:40.159><c> property</c>

00:03:40.509 --> 00:03:40.519 align:start position:0%
implicit path extractor uh property
 

00:03:40.519 --> 00:03:43.229 align:start position:0%
implicit path extractor uh property
graph<00:03:40.799><c> created</c><00:03:41.799><c> so</c><00:03:42.120><c> KJ</c><00:03:42.439><c> extractor</c><00:03:42.840><c> equal</c><00:03:43.080><c> to</c>

00:03:43.229 --> 00:03:43.239 align:start position:0%
graph created so KJ extractor equal to
 

00:03:43.239 --> 00:03:45.070 align:start position:0%
graph created so KJ extractor equal to
implicit<00:03:43.680><c> path</c><00:03:43.879><c> extractor</c><00:03:44.360><c> will</c><00:03:44.519><c> give</c><00:03:44.720><c> you</c>

00:03:45.070 --> 00:03:45.080 align:start position:0%
implicit path extractor will give you
 

00:03:45.080 --> 00:03:47.869 align:start position:0%
implicit path extractor will give you
the<00:03:45.319><c> implicit</c><00:03:45.760><c> path</c><00:03:46.400><c> extractor</c><00:03:47.400><c> so</c><00:03:47.560><c> the</c><00:03:47.680><c> next</c>

00:03:47.869 --> 00:03:47.879 align:start position:0%
the implicit path extractor so the next
 

00:03:47.879 --> 00:03:50.630 align:start position:0%
the implicit path extractor so the next
one<00:03:48.040><c> is</c><00:03:48.439><c> schema</c><00:03:48.879><c> LM</c><00:03:49.159><c> path</c><00:03:49.400><c> extractor</c><00:03:50.000><c> in</c><00:03:50.200><c> this</c>

00:03:50.630 --> 00:03:50.640 align:start position:0%
one is schema LM path extractor in this
 

00:03:50.640 --> 00:03:53.550 align:start position:0%
one is schema LM path extractor in this
uh<00:03:50.799><c> schema</c><00:03:51.200><c> LM</c><00:03:51.519><c> path</c><00:03:51.720><c> extractor</c><00:03:52.239><c> we</c><00:03:52.760><c> impose</c>

00:03:53.550 --> 00:03:53.560 align:start position:0%
uh schema LM path extractor we impose
 

00:03:53.560 --> 00:03:56.630 align:start position:0%
uh schema LM path extractor we impose
what<00:03:53.959><c> the</c><00:03:54.079><c> entities</c><00:03:54.599><c> relations</c><00:03:55.599><c> and</c><00:03:56.120><c> uh</c>

00:03:56.630 --> 00:03:56.640 align:start position:0%
what the entities relations and uh
 

00:03:56.640 --> 00:03:58.069 align:start position:0%
what the entities relations and uh
relations<00:03:57.040><c> between</c><00:03:57.319><c> different</c><00:03:57.599><c> entities</c>

00:03:58.069 --> 00:03:58.079 align:start position:0%
relations between different entities
 

00:03:58.079 --> 00:04:01.309 align:start position:0%
relations between different entities
possible<00:03:59.000><c> so</c><00:03:59.959><c> as</c><00:04:00.159><c> seen</c><00:04:00.519><c> here</c><00:04:00.799><c> so</c><00:04:01.000><c> these</c><00:04:01.159><c> are</c>

00:04:01.309 --> 00:04:01.319 align:start position:0%
possible so as seen here so these are
 

00:04:01.319 --> 00:04:02.910 align:start position:0%
possible so as seen here so these are
the<00:04:01.480><c> entities</c><00:04:01.920><c> POS</c><00:04:02.280><c> person</c><00:04:02.599><c> place</c>

00:04:02.910 --> 00:04:02.920 align:start position:0%
the entities POS person place
 

00:04:02.920 --> 00:04:05.429 align:start position:0%
the entities POS person place
organization<00:04:03.599><c> relations</c><00:04:04.159><c> part</c><00:04:04.400><c> of</c><00:04:04.720><c> has</c>

00:04:05.429 --> 00:04:05.439 align:start position:0%
organization relations part of has
 

00:04:05.439 --> 00:04:08.030 align:start position:0%
organization relations part of has
worked<00:04:05.879><c> at</c><00:04:06.599><c> and</c><00:04:06.760><c> then</c><00:04:06.920><c> you</c><00:04:07.000><c> can</c><00:04:07.599><c> create</c><00:04:07.879><c> the</c>

00:04:08.030 --> 00:04:08.040 align:start position:0%
worked at and then you can create the
 

00:04:08.040 --> 00:04:11.630 align:start position:0%
worked at and then you can create the
schema<00:04:08.640><c> okay</c><00:04:09.120><c> this</c><00:04:09.840><c> place</c><00:04:10.280><c> has</c><00:04:10.680><c> person</c><00:04:11.319><c> person</c>

00:04:11.630 --> 00:04:11.640 align:start position:0%
schema okay this place has person person
 

00:04:11.640 --> 00:04:13.710 align:start position:0%
schema okay this place has person person
is<00:04:11.840><c> part</c><00:04:12.040><c> of</c><00:04:12.280><c> place</c><00:04:12.879><c> person</c><00:04:13.200><c> worked</c><00:04:13.519><c> at</c>

00:04:13.710 --> 00:04:13.720 align:start position:0%
is part of place person worked at
 

00:04:13.720 --> 00:04:16.629 align:start position:0%
is part of place person worked at
organization<00:04:14.640><c> so</c><00:04:14.920><c> these</c><00:04:15.040><c> are</c><00:04:15.200><c> the</c><00:04:15.360><c> only</c><00:04:16.199><c> edges</c>

00:04:16.629 --> 00:04:16.639 align:start position:0%
organization so these are the only edges
 

00:04:16.639 --> 00:04:19.949 align:start position:0%
organization so these are the only edges
possible<00:04:17.280><c> so</c><00:04:17.799><c> we</c><00:04:17.959><c> have</c><00:04:18.239><c> imposed</c><00:04:18.759><c> it</c><00:04:19.040><c> now</c><00:04:19.639><c> right</c>

00:04:19.949 --> 00:04:19.959 align:start position:0%
possible so we have imposed it now right
 

00:04:19.959 --> 00:04:22.830 align:start position:0%
possible so we have imposed it now right
now<00:04:20.680><c> when</c><00:04:20.919><c> we</c><00:04:21.160><c> pass</c><00:04:21.519><c> llm</c><00:04:22.240><c> and</c><00:04:22.479><c> possible</c>

00:04:22.830 --> 00:04:22.840 align:start position:0%
now when we pass llm and possible
 

00:04:22.840 --> 00:04:24.430 align:start position:0%
now when we pass llm and possible
entities<00:04:23.280><c> possible</c><00:04:23.639><c> relationships</c>

00:04:24.430 --> 00:04:24.440 align:start position:0%
entities possible relationships
 

00:04:24.440 --> 00:04:28.590 align:start position:0%
entities possible relationships
validation<00:04:25.360><c> schema</c><00:04:26.360><c> and</c><00:04:26.880><c> uh</c><00:04:27.240><c> so</c><00:04:27.639><c> if</c><00:04:28.160><c> uh</c><00:04:28.360><c> if</c><00:04:28.479><c> it</c>

00:04:28.590 --> 00:04:28.600 align:start position:0%
validation schema and uh so if uh if it
 

00:04:28.600 --> 00:04:32.390 align:start position:0%
validation schema and uh so if uh if it
is<00:04:29.080><c> false</c><00:04:29.440><c> we</c><00:04:30.039><c> uh</c><00:04:30.160><c> so</c><00:04:30.639><c> strict</c><00:04:31.000><c> parameter</c><00:04:31.680><c> true</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
is false we uh so strict parameter true
 

00:04:32.400 --> 00:04:34.390 align:start position:0%
is false we uh so strict parameter true
if<00:04:32.560><c> false</c><00:04:32.880><c> will</c><00:04:33.039><c> allow</c><00:04:33.280><c> tlets</c><00:04:33.720><c> out</c><00:04:33.880><c> of</c><00:04:34.080><c> outside</c>

00:04:34.390 --> 00:04:34.400 align:start position:0%
if false will allow tlets out of outside
 

00:04:34.400 --> 00:04:36.350 align:start position:0%
if false will allow tlets out of outside
of<00:04:34.560><c> the</c><00:04:34.680><c> schema</c><00:04:35.120><c> as</c><00:04:35.240><c> well</c><00:04:35.880><c> okay</c><00:04:36.039><c> and</c><00:04:36.199><c> then</c>

00:04:36.350 --> 00:04:36.360 align:start position:0%
of the schema as well okay and then
 

00:04:36.360 --> 00:04:38.790 align:start position:0%
of the schema as well okay and then
maximum<00:04:36.800><c> parts</c><00:04:37.199><c> per</c><00:04:37.440><c> chunk</c><00:04:37.880><c> which</c><00:04:38.039><c> is</c><00:04:38.720><c> a</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
maximum parts per chunk which is a
 

00:04:38.800 --> 00:04:41.430 align:start position:0%
maximum parts per chunk which is a
number<00:04:39.000><c> of</c><00:04:39.120><c> Tates</c><00:04:39.560><c> per</c><00:04:39.680><c> Chun</c><00:04:40.080><c> right</c><00:04:40.759><c> so</c><00:04:41.199><c> you</c>

00:04:41.430 --> 00:04:41.440 align:start position:0%
number of Tates per Chun right so you
 

00:04:41.440 --> 00:04:43.670 align:start position:0%
number of Tates per Chun right so you
define<00:04:41.880><c> these</c><00:04:42.240><c> entities</c><00:04:43.240><c> uh</c><00:04:43.360><c> and</c><00:04:43.520><c> the</c>

00:04:43.670 --> 00:04:43.680 align:start position:0%
define these entities uh and the
 

00:04:43.680 --> 00:04:45.909 align:start position:0%
define these entities uh and the
validation<00:04:44.039><c> scheme</c><00:04:44.320><c> and</c><00:04:44.400><c> the</c><00:04:44.520><c> relations</c><00:04:45.440><c> uh</c>

00:04:45.909 --> 00:04:45.919 align:start position:0%
validation scheme and the relations uh
 

00:04:45.919 --> 00:04:47.870 align:start position:0%
validation scheme and the relations uh
only<00:04:46.199><c> these</c><00:04:46.360><c> are</c><00:04:46.600><c> possible</c><00:04:47.320><c> and</c><00:04:47.479><c> then</c><00:04:47.600><c> scheme</c>

00:04:47.870 --> 00:04:47.880 align:start position:0%
only these are possible and then scheme
 

00:04:47.880 --> 00:04:50.469 align:start position:0%
only these are possible and then scheme
LM<00:04:48.360><c> ex</c><00:04:48.720><c> path</c><00:04:48.960><c> extractor</c><00:04:49.440><c> will</c><00:04:49.840><c> just</c><00:04:50.080><c> create</c>

00:04:50.469 --> 00:04:50.479 align:start position:0%
LM ex path extractor will just create
 

00:04:50.479 --> 00:04:53.870 align:start position:0%
LM ex path extractor will just create
them<00:04:51.039><c> um</c><00:04:51.720><c> nothing</c>

00:04:53.870 --> 00:04:53.880 align:start position:0%
them um nothing
 

00:04:53.880 --> 00:04:56.510 align:start position:0%
them um nothing
else<00:04:54.880><c> and</c><00:04:55.039><c> then</c><00:04:55.199><c> once</c><00:04:55.400><c> you</c><00:04:55.600><c> have</c><00:04:55.800><c> them</c><00:04:56.240><c> you</c>

00:04:56.510 --> 00:04:56.520 align:start position:0%
else and then once you have them you
 

00:04:56.520 --> 00:05:00.310 align:start position:0%
else and then once you have them you
have<00:04:57.039><c> uh</c><00:04:57.479><c> you</c><00:04:57.600><c> can</c><00:04:57.800><c> use</c><00:04:58.039><c> it</c><00:04:58.240><c> for</c><00:04:58.919><c> uh</c><00:04:59.680><c> retal</c><00:05:00.120><c> and</c>

00:05:00.310 --> 00:05:00.320 align:start position:0%
have uh you can use it for uh retal and
 

00:05:00.320 --> 00:05:02.909 align:start position:0%
have uh you can use it for uh retal and
quiring<00:05:00.759><c> right</c><00:05:01.000><c> so</c><00:05:01.840><c> we</c><00:05:02.000><c> have</c><00:05:02.160><c> different</c>

00:05:02.909 --> 00:05:02.919 align:start position:0%
quiring right so we have different
 

00:05:02.919 --> 00:05:10.830 align:start position:0%
quiring right so we have different
retrievers<00:05:03.919><c> uh</c><00:05:04.560><c> starting</c><00:05:05.000><c> with</c>

00:05:10.830 --> 00:05:10.840 align:start position:0%
 
 

00:05:10.840 --> 00:05:14.469 align:start position:0%
 
uh<00:05:11.840><c> llm</c><00:05:12.560><c> synonym</c><00:05:13.039><c> retriever</c><00:05:13.639><c> Vector</c><00:05:14.080><c> context</c>

00:05:14.469 --> 00:05:14.479 align:start position:0%
uh llm synonym retriever Vector context
 

00:05:14.479 --> 00:05:16.790 align:start position:0%
uh llm synonym retriever Vector context
Retriever<00:05:15.160><c> and</c><00:05:15.479><c> text</c><00:05:15.759><c> to</c><00:05:15.960><c> Cipher</c><00:05:16.280><c> and</c><00:05:16.440><c> Cipher</c>

00:05:16.790 --> 00:05:16.800 align:start position:0%
Retriever and text to Cipher and Cipher
 

00:05:16.800 --> 00:05:18.390 align:start position:0%
Retriever and text to Cipher and Cipher
template<00:05:17.160><c> retriever</c><00:05:17.680><c> right</c><00:05:17.840><c> so</c><00:05:18.120><c> different</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
template retriever right so different
 

00:05:18.400 --> 00:05:20.950 align:start position:0%
template retriever right so different
retrievers<00:05:18.840><c> are</c><00:05:18.960><c> available</c><00:05:19.720><c> so</c><00:05:19.880><c> you</c><00:05:19.960><c> can</c><00:05:20.160><c> use</c>

00:05:20.950 --> 00:05:20.960 align:start position:0%
retrievers are available so you can use
 

00:05:20.960 --> 00:05:23.270 align:start position:0%
retrievers are available so you can use
um<00:05:21.520><c> different</c><00:05:21.840><c> retrievers</c><00:05:22.560><c> and</c><00:05:22.759><c> then</c><00:05:23.160><c> uh</c>

00:05:23.270 --> 00:05:23.280 align:start position:0%
um different retrievers and then uh
 

00:05:23.280 --> 00:05:27.150 align:start position:0%
um different retrievers and then uh
create<00:05:23.520><c> a</c><00:05:23.680><c> quy</c><00:05:23.960><c> engine</c><00:05:24.880><c> and</c><00:05:25.720><c> uh</c><00:05:25.919><c> and</c><00:05:26.600><c> as</c><00:05:26.720><c> well</c>

00:05:27.150 --> 00:05:27.160 align:start position:0%
create a quy engine and uh and as well
 

00:05:27.160 --> 00:05:30.510 align:start position:0%
create a quy engine and uh and as well
right<00:05:27.520><c> so</c><00:05:28.520><c> yeah</c><00:05:28.840><c> so</c><00:05:29.039><c> as</c><00:05:29.240><c> seen</c><00:05:29.840><c> LM</c><00:05:30.160><c> synonym</c>

00:05:30.510 --> 00:05:30.520 align:start position:0%
right so yeah so as seen LM synonym
 

00:05:30.520 --> 00:05:31.950 align:start position:0%
right so yeah so as seen LM synonym
retriever<00:05:30.919><c> retries</c><00:05:31.280><c> notes</c><00:05:31.560><c> based</c><00:05:31.800><c> on</c>

00:05:31.950 --> 00:05:31.960 align:start position:0%
retriever retries notes based on
 

00:05:31.960 --> 00:05:34.270 align:start position:0%
retriever retries notes based on
keywords<00:05:32.360><c> and</c><00:05:32.600><c> synonyms</c><00:05:33.600><c> with</c><00:05:33.840><c> extract</c>

00:05:34.270 --> 00:05:34.280 align:start position:0%
keywords and synonyms with extract
 

00:05:34.280 --> 00:05:36.469 align:start position:0%
keywords and synonyms with extract
string<00:05:34.720><c> match</c><00:05:35.039><c> but</c><00:05:35.240><c> these</c><00:05:35.479><c> are</c><00:05:36.000><c> generated</c><00:05:36.400><c> by</c>

00:05:36.469 --> 00:05:36.479 align:start position:0%
string match but these are generated by
 

00:05:36.479 --> 00:05:38.469 align:start position:0%
string match but these are generated by
LM<00:05:36.840><c> from</c><00:05:36.960><c> the</c><00:05:37.080><c> query</c><00:05:37.680><c> and</c><00:05:37.840><c> Vector</c><00:05:38.120><c> context</c>

00:05:38.469 --> 00:05:38.479 align:start position:0%
LM from the query and Vector context
 

00:05:38.479 --> 00:05:40.590 align:start position:0%
LM from the query and Vector context
retriever<00:05:38.880><c> uses</c><00:05:39.479><c> embeddings</c><00:05:40.000><c> of</c><00:05:40.199><c> the</c><00:05:40.360><c> text</c>

00:05:40.590 --> 00:05:40.600 align:start position:0%
retriever uses embeddings of the text
 

00:05:40.600 --> 00:05:43.230 align:start position:0%
retriever uses embeddings of the text
notes<00:05:40.919><c> as</c><00:05:41.039><c> well</c><00:05:41.199><c> as</c><00:05:41.400><c> graph</c><00:05:41.680><c> notes</c><00:05:42.360><c> text</c><00:05:42.600><c> to</c>

00:05:43.230 --> 00:05:43.240 align:start position:0%
notes as well as graph notes text to
 

00:05:43.240 --> 00:05:47.550 align:start position:0%
notes as well as graph notes text to
Cipher<00:05:44.080><c> uh</c><00:05:44.440><c> retriever</c><00:05:45.400><c> basically</c><00:05:46.400><c> uh</c><00:05:47.400><c> it</c>

00:05:47.550 --> 00:05:47.560 align:start position:0%
Cipher uh retriever basically uh it
 

00:05:47.560 --> 00:05:50.150 align:start position:0%
Cipher uh retriever basically uh it
creates<00:05:47.960><c> Cipher</c><00:05:48.319><c> query</c><00:05:49.160><c> uh</c><00:05:49.520><c> from</c><00:05:49.720><c> the</c><00:05:49.840><c> user</c>

00:05:50.150 --> 00:05:50.160 align:start position:0%
creates Cipher query uh from the user
 

00:05:50.160 --> 00:05:53.870 align:start position:0%
creates Cipher query uh from the user
query<00:05:51.039><c> and</c><00:05:52.039><c> extracts</c><00:05:52.600><c> the</c><00:05:53.000><c> uh</c>

00:05:53.870 --> 00:05:53.880 align:start position:0%
query and extracts the uh
 

00:05:53.880 --> 00:05:57.189 align:start position:0%
query and extracts the uh
noes<00:05:54.880><c> for</c><00:05:55.120><c> cyer</c><00:05:55.680><c> template</c><00:05:56.360><c> retriever</c><00:05:57.039><c> it</c>

00:05:57.189 --> 00:05:57.199 align:start position:0%
noes for cyer template retriever it
 

00:05:57.199 --> 00:05:58.830 align:start position:0%
noes for cyer template retriever it
extracts<00:05:57.600><c> the</c><00:05:57.720><c> parameters</c><00:05:58.199><c> available</c><00:05:58.560><c> in</c><00:05:58.680><c> the</c>

00:05:58.830 --> 00:05:58.840 align:start position:0%
extracts the parameters available in the
 

00:05:58.840 --> 00:06:00.390 align:start position:0%
extracts the parameters available in the
cipher<00:05:59.199><c> templates</c>

00:06:00.390 --> 00:06:00.400 align:start position:0%
cipher templates
 

00:06:00.400 --> 00:06:03.150 align:start position:0%
cipher templates
uh<00:06:00.600><c> and</c><00:06:00.840><c> then</c><00:06:01.319><c> gets</c><00:06:01.600><c> the</c><00:06:01.800><c> data</c><00:06:02.600><c> and</c><00:06:02.759><c> the</c><00:06:02.880><c> custom</c>

00:06:03.150 --> 00:06:03.160 align:start position:0%
uh and then gets the data and the custom
 

00:06:03.160 --> 00:06:06.189 align:start position:0%
uh and then gets the data and the custom
P<00:06:03.440><c> retriever</c><00:06:03.840><c> it's</c><00:06:04.000><c> a</c><00:06:04.639><c> custom</c><00:06:05.199><c> uh</c><00:06:05.319><c> retriever</c>

00:06:06.189 --> 00:06:06.199 align:start position:0%
P retriever it's a custom uh retriever
 

00:06:06.199 --> 00:06:08.110 align:start position:0%
P retriever it's a custom uh retriever
uh<00:06:06.680><c> that</c><00:06:06.880><c> we</c><00:06:07.039><c> can</c><00:06:07.360><c> combine</c><00:06:07.800><c> different</c>

00:06:08.110 --> 00:06:08.120 align:start position:0%
uh that we can combine different
 

00:06:08.120 --> 00:06:09.950 align:start position:0%
uh that we can combine different
retrievers<00:06:08.639><c> let's</c><00:06:08.759><c> say</c><00:06:08.919><c> you</c><00:06:09.000><c> want</c><00:06:09.120><c> to</c><00:06:09.240><c> combine</c>

00:06:09.950 --> 00:06:09.960 align:start position:0%
retrievers let's say you want to combine
 

00:06:09.960 --> 00:06:11.629 align:start position:0%
retrievers let's say you want to combine
Vector<00:06:10.240><c> context</c><00:06:10.560><c> Retriever</c><00:06:10.919><c> and</c><00:06:11.039><c> text</c><00:06:11.400><c> Cipher</c>

00:06:11.629 --> 00:06:11.639 align:start position:0%
Vector context Retriever and text Cipher
 

00:06:11.639 --> 00:06:13.710 align:start position:0%
Vector context Retriever and text Cipher
retriever<00:06:12.080><c> or</c><00:06:12.840><c> uh</c><00:06:12.960><c> multiple</c><00:06:13.400><c> of</c><00:06:13.560><c> these</c>

00:06:13.710 --> 00:06:13.720 align:start position:0%
retriever or uh multiple of these
 

00:06:13.720 --> 00:06:15.670 align:start position:0%
retriever or uh multiple of these
retrievers<00:06:14.520><c> or</c><00:06:14.680><c> maybe</c><00:06:14.919><c> you</c><00:06:15.039><c> want</c><00:06:15.160><c> to</c><00:06:15.280><c> use</c><00:06:15.440><c> re</c>

00:06:15.670 --> 00:06:15.680 align:start position:0%
retrievers or maybe you want to use re
 

00:06:15.680 --> 00:06:17.950 align:start position:0%
retrievers or maybe you want to use re
ranker<00:06:16.000><c> on</c><00:06:16.199><c> top</c><00:06:16.360><c> of</c><00:06:16.520><c> it</c><00:06:17.000><c> you</c><00:06:17.120><c> can</c><00:06:17.599><c> uh</c><00:06:17.680><c> use</c>

00:06:17.950 --> 00:06:17.960 align:start position:0%
ranker on top of it you can uh use
 

00:06:17.960 --> 00:06:22.270 align:start position:0%
ranker on top of it you can uh use
custom<00:06:18.240><c> PG</c><00:06:18.880><c> retriever</c><00:06:19.880><c> so</c><00:06:20.599><c> here</c><00:06:21.160><c> yeah</c><00:06:21.680><c> so</c><00:06:21.960><c> here</c>

00:06:22.270 --> 00:06:22.280 align:start position:0%
custom PG retriever so here yeah so here
 

00:06:22.280 --> 00:06:25.589 align:start position:0%
custom PG retriever so here yeah so here
for<00:06:22.440><c> example</c><00:06:22.800><c> I</c><00:06:22.919><c> have</c><00:06:23.639><c> uh</c><00:06:24.639><c> Vector</c><00:06:25.199><c> context</c>

00:06:25.589 --> 00:06:25.599 align:start position:0%
for example I have uh Vector context
 

00:06:25.599 --> 00:06:30.510 align:start position:0%
for example I have uh Vector context
retriever<00:06:26.160><c> andm</c><00:06:27.199><c> retriever</c><00:06:28.199><c> and</c><00:06:28.440><c> then</c><00:06:28.960><c> uh</c>

00:06:30.510 --> 00:06:30.520 align:start position:0%
retriever andm retriever and then uh
 

00:06:30.520 --> 00:06:32.070 align:start position:0%
retriever andm retriever and then uh
Define<00:06:30.800><c> the</c><00:06:30.919><c> Retriever</c><00:06:31.520><c> and</c><00:06:31.680><c> then</c><00:06:31.800><c> you</c><00:06:31.919><c> can</c>

00:06:32.070 --> 00:06:32.080 align:start position:0%
Define the Retriever and then you can
 

00:06:32.080 --> 00:06:35.110 align:start position:0%
Define the Retriever and then you can
retrieve<00:06:32.840><c> so</c><00:06:33.160><c> let's</c><00:06:33.759><c> uh</c><00:06:34.120><c> see</c><00:06:34.440><c> how</c><00:06:34.720><c> each</c><00:06:34.919><c> of</c>

00:06:35.110 --> 00:06:35.120 align:start position:0%
retrieve so let's uh see how each of
 

00:06:35.120 --> 00:06:37.029 align:start position:0%
retrieve so let's uh see how each of
these<00:06:35.680><c> retrievers</c><00:06:36.199><c> are</c><00:06:36.360><c> defined</c><00:06:36.759><c> starting</c>

00:06:37.029 --> 00:06:37.039 align:start position:0%
these retrievers are defined starting
 

00:06:37.039 --> 00:06:39.749 align:start position:0%
these retrievers are defined starting
with<00:06:37.280><c> LM</c><00:06:37.560><c> syon</c><00:06:37.919><c> D</c><00:06:38.599><c> so</c><00:06:38.800><c> here</c><00:06:38.880><c> is</c><00:06:39.000><c> a</c><00:06:39.160><c> prompt</c><00:06:39.479><c> given</c>

00:06:39.749 --> 00:06:39.759 align:start position:0%
with LM syon D so here is a prompt given
 

00:06:39.759 --> 00:06:41.790 align:start position:0%
with LM syon D so here is a prompt given
some<00:06:39.919><c> initial</c><00:06:40.280><c> query</c><00:06:40.599><c> generate</c><00:06:40.919><c> synonyms</c><00:06:41.440><c> or</c>

00:06:41.790 --> 00:06:41.800 align:start position:0%
some initial query generate synonyms or
 

00:06:41.800 --> 00:06:44.110 align:start position:0%
some initial query generate synonyms or
related<00:06:42.199><c> keywords</c><00:06:42.560><c> up</c><00:06:42.720><c> to</c><00:06:43.440><c> a</c><00:06:43.560><c> number</c><00:06:43.800><c> of</c>

00:06:44.110 --> 00:06:44.120 align:start position:0%
related keywords up to a number of
 

00:06:44.120 --> 00:06:45.710 align:start position:0%
related keywords up to a number of
maximum<00:06:44.520><c> number</c><00:06:44.720><c> of</c><00:06:44.840><c> keywords</c><00:06:45.160><c> in</c><00:06:45.319><c> total</c>

00:06:45.710 --> 00:06:45.720 align:start position:0%
maximum number of keywords in total
 

00:06:45.720 --> 00:06:48.150 align:start position:0%
maximum number of keywords in total
considering<00:06:46.240><c> POS</c><00:06:46.639><c> possible</c><00:06:47.000><c> case</c><00:06:47.240><c> of</c>

00:06:48.150 --> 00:06:48.160 align:start position:0%
considering POS possible case of
 

00:06:48.160 --> 00:06:51.150 align:start position:0%
considering POS possible case of
capitation<00:06:49.160><c> plural</c><00:06:49.680><c> common</c><00:06:50.000><c> expression</c><00:06:50.440><c> Etc</c>

00:06:51.150 --> 00:06:51.160 align:start position:0%
capitation plural common expression Etc
 

00:06:51.160 --> 00:06:52.589 align:start position:0%
capitation plural common expression Etc
provide<00:06:51.479><c> all</c><00:06:51.680><c> synonyms</c><00:06:52.120><c> or</c><00:06:52.280><c> keywords</c>

00:06:52.589 --> 00:06:52.599 align:start position:0%
provide all synonyms or keywords
 

00:06:52.599 --> 00:06:56.309 align:start position:0%
provide all synonyms or keywords
separated<00:06:53.080><c> by</c><00:06:54.080><c> symbols</c><00:06:55.080><c> uh</c><00:06:55.360><c> and</c><00:06:55.599><c> node</c><00:06:56.000><c> result</c>

00:06:56.309 --> 00:06:56.319 align:start position:0%
separated by symbols uh and node result
 

00:06:56.319 --> 00:06:58.309 align:start position:0%
separated by symbols uh and node result
should<00:06:56.520><c> be</c><00:06:56.680><c> in</c><00:06:56.800><c> one</c><00:06:57.039><c> line</c><00:06:57.240><c> separated</c><00:06:57.800><c> by</c>

00:06:58.309 --> 00:06:58.319 align:start position:0%
should be in one line separated by
 

00:06:58.319 --> 00:07:00.510 align:start position:0%
should be in one line separated by
specific<00:06:58.680><c> symbols</c><00:06:59.160><c> so</c><00:06:59.319><c> here</c><00:06:59.759><c> query</c><00:07:00.080><c> and</c><00:07:00.199><c> then</c>

00:07:00.510 --> 00:07:00.520 align:start position:0%
specific symbols so here query and then
 

00:07:00.520 --> 00:07:03.710 align:start position:0%
specific symbols so here query and then
the<00:07:01.160><c> keywords</c><00:07:01.560><c> are</c><00:07:01.720><c> extracted</c><00:07:02.560><c> based</c><00:07:02.840><c> on</c><00:07:03.080><c> this</c>

00:07:03.710 --> 00:07:03.720 align:start position:0%
the keywords are extracted based on this
 

00:07:03.720 --> 00:07:07.550 align:start position:0%
the keywords are extracted based on this
symbol<00:07:04.280><c> you</c><00:07:04.520><c> pass</c><00:07:04.840><c> it</c><00:07:05.199><c> and</c><00:07:05.400><c> then</c><00:07:06.000><c> uh</c><00:07:06.440><c> get</c><00:07:06.759><c> the</c>

00:07:07.550 --> 00:07:07.560 align:start position:0%
symbol you pass it and then uh get the
 

00:07:07.560 --> 00:07:11.230 align:start position:0%
symbol you pass it and then uh get the
possible<00:07:08.280><c> keywords</c><00:07:09.759><c> so</c><00:07:10.759><c> here</c><00:07:10.919><c> are</c><00:07:11.120><c> the</c>

00:07:11.230 --> 00:07:11.240 align:start position:0%
possible keywords so here are the
 

00:07:11.240 --> 00:07:13.749 align:start position:0%
possible keywords so here are the
different<00:07:11.560><c> parameters</c><00:07:12.080><c> for</c><00:07:12.560><c> uh</c><00:07:12.680><c> serum</c><00:07:13.360><c> uh</c>

00:07:13.749 --> 00:07:13.759 align:start position:0%
different parameters for uh serum uh
 

00:07:13.759 --> 00:07:17.710 align:start position:0%
different parameters for uh serum uh
retriever<00:07:14.800><c> right</c><00:07:15.800><c> so</c><00:07:16.520><c> yeah</c><00:07:16.960><c> and</c><00:07:17.120><c> then</c><00:07:17.360><c> Vector</c>

00:07:17.710 --> 00:07:17.720 align:start position:0%
retriever right so yeah and then Vector
 

00:07:17.720 --> 00:07:20.070 align:start position:0%
retriever right so yeah and then Vector
context<00:07:18.240><c> retriever</c><00:07:19.240><c> uh</c><00:07:19.400><c> basically</c><00:07:19.720><c> uses</c>

00:07:20.070 --> 00:07:20.080 align:start position:0%
context retriever uh basically uses
 

00:07:20.080 --> 00:07:23.390 align:start position:0%
context retriever uh basically uses
embeddings<00:07:20.520><c> as</c><00:07:20.639><c> I</c><00:07:20.800><c> said</c><00:07:21.120><c> so</c><00:07:22.120><c> same</c><00:07:22.440><c> way</c><00:07:23.199><c> uh</c><00:07:23.280><c> you</c>

00:07:23.390 --> 00:07:23.400 align:start position:0%
embeddings as I said so same way uh you
 

00:07:23.400 --> 00:07:25.909 align:start position:0%
embeddings as I said so same way uh you
need<00:07:23.599><c> to</c><00:07:23.800><c> pass</c><00:07:24.000><c> index</c><00:07:24.360><c> embedding</c><00:07:24.800><c> model</c><00:07:25.800><c> you</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
need to pass index embedding model you
 

00:07:25.919 --> 00:07:27.830 align:start position:0%
need to pass index embedding model you
want<00:07:26.080><c> to</c><00:07:26.199><c> include</c><00:07:26.520><c> Source</c><00:07:26.800><c> text</c><00:07:27.080><c> or</c><00:07:27.240><c> not</c><00:07:27.560><c> for</c>

00:07:27.830 --> 00:07:27.840 align:start position:0%
want to include Source text or not for
 

00:07:27.840 --> 00:07:30.670 align:start position:0%
want to include Source text or not for
embedding<00:07:28.560><c> and</c><00:07:28.720><c> then</c><00:07:29.080><c> similar</c><00:07:29.400><c> to</c><00:07:29.599><c> top</c><00:07:29.840><c> can</c><00:07:30.440><c> uh</c>

00:07:30.670 --> 00:07:30.680 align:start position:0%
embedding and then similar to top can uh
 

00:07:30.680 --> 00:07:33.589 align:start position:0%
embedding and then similar to top can uh
other<00:07:31.479><c> parameters</c><00:07:32.479><c> and</c><00:07:32.680><c> then</c><00:07:33.120><c> create</c><00:07:33.400><c> a</c>

00:07:33.589 --> 00:07:33.599 align:start position:0%
other parameters and then create a
 

00:07:33.599 --> 00:07:36.469 align:start position:0%
other parameters and then create a
retriever<00:07:34.160><c> accordingly</c><00:07:35.160><c> for</c><00:07:35.360><c> text</c><00:07:35.599><c> to</c><00:07:35.800><c> Cipher</c>

00:07:36.469 --> 00:07:36.479 align:start position:0%
retriever accordingly for text to Cipher
 

00:07:36.479 --> 00:07:38.950 align:start position:0%
retriever accordingly for text to Cipher
uh<00:07:36.639><c> it</c><00:07:36.800><c> basically</c><00:07:37.160><c> creates</c><00:07:37.520><c> Cypher</c><00:07:37.840><c> query</c><00:07:38.840><c> uh</c>

00:07:38.950 --> 00:07:38.960 align:start position:0%
uh it basically creates Cypher query uh
 

00:07:38.960 --> 00:07:42.070 align:start position:0%
uh it basically creates Cypher query uh
for<00:07:39.160><c> the</c><00:07:39.319><c> given</c><00:07:39.680><c> query</c><00:07:40.680><c> based</c><00:07:40.960><c> on</c><00:07:41.280><c> the</c><00:07:42.000><c> uh</c>

00:07:42.070 --> 00:07:42.080 align:start position:0%
for the given query based on the uh
 

00:07:42.080 --> 00:07:45.510 align:start position:0%
for the given query based on the uh
Fields<00:07:42.520><c> allowed</c><00:07:43.160><c> so</c><00:07:44.120><c> yeah</c><00:07:44.800><c> so</c><00:07:44.960><c> we</c><00:07:45.120><c> have</c><00:07:45.280><c> a</c>

00:07:45.510 --> 00:07:45.520 align:start position:0%
Fields allowed so yeah so we have a
 

00:07:45.520 --> 00:07:47.350 align:start position:0%
Fields allowed so yeah so we have a
response<00:07:45.960><c> template</c><00:07:46.520><c> and</c><00:07:46.720><c> what</c><00:07:46.840><c> are</c><00:07:46.960><c> the</c><00:07:47.080><c> load</c>

00:07:47.350 --> 00:07:47.360 align:start position:0%
response template and what are the load
 

00:07:47.360 --> 00:07:50.149 align:start position:0%
response template and what are the load
fields<00:07:47.919><c> and</c><00:07:48.440><c> text</c><00:07:48.720><c> to</c><00:07:49.080><c> and</c><00:07:49.240><c> then</c><00:07:49.520><c> again</c><00:07:49.879><c> text</c>

00:07:50.149 --> 00:07:50.159 align:start position:0%
fields and text to and then again text
 

00:07:50.159 --> 00:07:53.869 align:start position:0%
fields and text to and then again text
to<00:07:50.400><c> Cipher</c><00:07:51.039><c> template</c><00:07:52.199><c> so</c><00:07:53.199><c> accordingly</c><00:07:53.759><c> you</c>

00:07:53.869 --> 00:07:53.879 align:start position:0%
to Cipher template so accordingly you
 

00:07:53.879 --> 00:07:55.710 align:start position:0%
to Cipher template so accordingly you
can<00:07:54.199><c> provide</c><00:07:54.560><c> all</c><00:07:54.800><c> these</c><00:07:55.000><c> parameters</c><00:07:55.479><c> into</c>

00:07:55.710 --> 00:07:55.720 align:start position:0%
can provide all these parameters into
 

00:07:55.720 --> 00:07:59.869 align:start position:0%
can provide all these parameters into
the<00:07:55.919><c> text</c><00:07:56.159><c> to</c><00:07:56.319><c> Cipher</c><00:07:56.759><c> Retriever</c><00:07:57.759><c> and</c><00:07:58.280><c> uh</c><00:07:59.720><c> get</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
the text to Cipher Retriever and uh get
 

00:07:59.879 --> 00:08:03.390 align:start position:0%
the text to Cipher Retriever and uh get
the<00:08:00.000><c> retriever</c><00:08:00.440><c> done</c><00:08:01.360><c> similarly</c><00:08:02.000><c> for</c><00:08:03.000><c> Cipher</c>

00:08:03.390 --> 00:08:03.400 align:start position:0%
the retriever done similarly for Cipher
 

00:08:03.400 --> 00:08:06.149 align:start position:0%
the retriever done similarly for Cipher
template<00:08:03.800><c> retriever</c><00:08:04.800><c> we</c><00:08:05.000><c> have</c><00:08:05.240><c> this</c><00:08:05.720><c> Cipher</c>

00:08:06.149 --> 00:08:06.159 align:start position:0%
template retriever we have this Cipher
 

00:08:06.159 --> 00:08:09.629 align:start position:0%
template retriever we have this Cipher
query<00:08:06.720><c> so</c><00:08:07.199><c> you</c><00:08:07.360><c> need</c><00:08:08.319><c> uh</c><00:08:08.520><c> specific</c><00:08:09.319><c> uh</c>

00:08:09.629 --> 00:08:09.639 align:start position:0%
query so you need uh specific uh
 

00:08:09.639 --> 00:08:12.510 align:start position:0%
query so you need uh specific uh
parameters<00:08:10.360><c> right</c><00:08:10.599><c> the</c><00:08:10.759><c> chunk</c><00:08:11.520><c> mentions</c>

00:08:12.510 --> 00:08:12.520 align:start position:0%
parameters right the chunk mentions
 

00:08:12.520 --> 00:08:16.230 align:start position:0%
parameters right the chunk mentions
right<00:08:12.759><c> and</c><00:08:13.000><c> then</c><00:08:13.759><c> uh</c><00:08:14.319><c> basically</c><00:08:14.879><c> create</c><00:08:15.879><c> uh</c>

00:08:16.230 --> 00:08:16.240 align:start position:0%
right and then uh basically create uh
 

00:08:16.240 --> 00:08:18.189 align:start position:0%
right and then uh basically create uh
get<00:08:16.479><c> these</c><00:08:16.759><c> parameters</c><00:08:17.240><c> send</c><00:08:17.440><c> it</c><00:08:17.560><c> to</c><00:08:17.800><c> Cipher</c>

00:08:18.189 --> 00:08:18.199 align:start position:0%
get these parameters send it to Cipher
 

00:08:18.199 --> 00:08:21.550 align:start position:0%
get these parameters send it to Cipher
query<00:08:18.960><c> and</c><00:08:19.240><c> get</c><00:08:19.440><c> the</c><00:08:19.879><c> data</c><00:08:20.879><c> so</c><00:08:21.159><c> Cipher</c>

00:08:21.550 --> 00:08:21.560 align:start position:0%
query and get the data so Cipher
 

00:08:21.560 --> 00:08:26.070 align:start position:0%
query and get the data so Cipher
template<00:08:22.159><c> ret</c><00:08:22.800><c> takes</c><00:08:23.720><c> the</c><00:08:24.720><c> uh</c><00:08:25.240><c> Cipher</c><00:08:25.680><c> query</c>

00:08:26.070 --> 00:08:26.080 align:start position:0%
template ret takes the uh Cipher query
 

00:08:26.080 --> 00:08:28.749 align:start position:0%
template ret takes the uh Cipher query
and<00:08:26.240><c> then</c><00:08:26.440><c> get</c><00:08:26.599><c> the</c><00:08:26.759><c> parameters</c><00:08:27.680><c> and</c><00:08:28.400><c> get</c><00:08:28.560><c> the</c>

00:08:28.749 --> 00:08:28.759 align:start position:0%
and then get the parameters and get the
 

00:08:28.759 --> 00:08:33.389 align:start position:0%
and then get the parameters and get the
r<00:08:29.560><c> ready</c><00:08:30.520><c> so</c><00:08:31.039><c> you</c><00:08:31.199><c> can</c><00:08:31.840><c> um</c><00:08:32.519><c> this</c><00:08:32.599><c> is</c><00:08:32.719><c> a</c><00:08:32.919><c> pantic</c>

00:08:33.389 --> 00:08:33.399 align:start position:0%
r ready so you can um this is a pantic
 

00:08:33.399 --> 00:08:35.670 align:start position:0%
r ready so you can um this is a pantic
class<00:08:33.760><c> to</c><00:08:34.080><c> represent</c><00:08:34.440><c> the</c><00:08:34.560><c> params</c><00:08:35.039><c> for</c><00:08:35.200><c> our</c>

00:08:35.670 --> 00:08:35.680 align:start position:0%
class to represent the params for our
 

00:08:35.680 --> 00:08:38.070 align:start position:0%
class to represent the params for our
query<00:08:36.680><c> uh</c><00:08:36.919><c> the</c><00:08:37.039><c> class</c><00:08:37.279><c> feeds</c><00:08:37.599><c> are</c><00:08:37.800><c> directly</c>

00:08:38.070 --> 00:08:38.080 align:start position:0%
query uh the class feeds are directly
 

00:08:38.080 --> 00:08:40.029 align:start position:0%
query uh the class feeds are directly
used<00:08:38.360><c> as</c><00:08:38.560><c> par</c><00:08:38.800><c> for</c><00:08:38.919><c> running</c><00:08:39.200><c> the</c><00:08:39.320><c> cipher</c><00:08:39.680><c> query</c>

00:08:40.029 --> 00:08:40.039 align:start position:0%
used as par for running the cipher query
 

00:08:40.039 --> 00:08:41.949 align:start position:0%
used as par for running the cipher query
so<00:08:40.320><c> here</c><00:08:40.440><c> is</c><00:08:40.519><c> a</c><00:08:40.719><c> description</c><00:08:41.519><c> a</c><00:08:41.640><c> list</c><00:08:41.839><c> of</c>

00:08:41.949 --> 00:08:41.959 align:start position:0%
so here is a description a list of
 

00:08:41.959 --> 00:08:43.949 align:start position:0%
so here is a description a list of
entity<00:08:42.279><c> names</c><00:08:42.479><c> or</c><00:08:42.640><c> cords</c><00:08:43.039><c> to</c><00:08:43.159><c> use</c><00:08:43.360><c> for</c><00:08:43.560><c> look</c><00:08:43.800><c> up</c>

00:08:43.949 --> 00:08:43.959 align:start position:0%
entity names or cords to use for look up
 

00:08:43.959 --> 00:08:47.710 align:start position:0%
entity names or cords to use for look up
in<00:08:44.920><c> uh</c><00:08:45.480><c> knowledge</c><00:08:45.839><c> gra</c><00:08:46.200><c> basically</c><00:08:47.200><c> so</c><00:08:47.600><c> this</c>

00:08:47.710 --> 00:08:47.720 align:start position:0%
in uh knowledge gra basically so this
 

00:08:47.720 --> 00:08:50.470 align:start position:0%
in uh knowledge gra basically so this
will<00:08:47.920><c> create</c><00:08:48.240><c> the</c><00:08:48.640><c> template</c><00:08:49.080><c> Retriever</c><00:08:49.839><c> and</c>

00:08:50.470 --> 00:08:50.480 align:start position:0%
will create the template Retriever and
 

00:08:50.480 --> 00:08:52.710 align:start position:0%
will create the template Retriever and
as<00:08:50.640><c> you</c><00:08:50.839><c> have</c><00:08:51.480><c> uh</c><00:08:51.839><c> all</c><00:08:52.080><c> these</c><00:08:52.320><c> different</c>

00:08:52.710 --> 00:08:52.720 align:start position:0%
as you have uh all these different
 

00:08:52.720 --> 00:08:54.509 align:start position:0%
as you have uh all these different
retrievers<00:08:53.360><c> now</c><00:08:53.720><c> what</c><00:08:53.839><c> you</c><00:08:53.920><c> can</c><00:08:54.080><c> do</c><00:08:54.279><c> is</c><00:08:54.399><c> you</c>

00:08:54.509 --> 00:08:54.519 align:start position:0%
retrievers now what you can do is you
 

00:08:54.519 --> 00:08:57.389 align:start position:0%
retrievers now what you can do is you
can<00:08:54.959><c> just</c><00:08:55.160><c> plug</c><00:08:55.399><c> in</c><00:08:56.360><c> Define</c><00:08:56.680><c> these</c><00:08:56.839><c> retri</c><00:08:57.279><c> and</c>

00:08:57.389 --> 00:08:57.399 align:start position:0%
can just plug in Define these retri and
 

00:08:57.399 --> 00:09:00.470 align:start position:0%
can just plug in Define these retri and
plug<00:08:57.640><c> in</c><00:08:57.760><c> to</c><00:08:58.000><c> the</c><00:08:59.000><c> pg</c><00:08:59.480><c> retriever</c><00:09:00.079><c> what</c><00:09:00.200><c> are</c><00:09:00.320><c> the</c>

00:09:00.470 --> 00:09:00.480 align:start position:0%
plug in to the pg retriever what are the
 

00:09:00.480 --> 00:09:04.630 align:start position:0%
plug in to the pg retriever what are the
sub<00:09:01.120><c> retrievers</c><00:09:02.120><c> okay</c><00:09:03.240><c> and</c><00:09:04.240><c> create</c><00:09:04.519><c> the</c>

00:09:04.630 --> 00:09:04.640 align:start position:0%
sub retrievers okay and create the
 

00:09:04.640 --> 00:09:06.550 align:start position:0%
sub retrievers okay and create the
retriever<00:09:05.040><c> accordingly</c><00:09:06.000><c> and</c><00:09:06.160><c> then</c><00:09:06.320><c> start</c>

00:09:06.550 --> 00:09:06.560 align:start position:0%
retriever accordingly and then start
 

00:09:06.560 --> 00:09:09.230 align:start position:0%
retriever accordingly and then start
quering<00:09:07.399><c> so</c><00:09:08.000><c> this</c><00:09:08.120><c> is</c><00:09:08.279><c> how</c><00:09:08.560><c> you</c><00:09:08.680><c> can</c><00:09:08.839><c> Define</c>

00:09:09.230 --> 00:09:09.240 align:start position:0%
quering so this is how you can Define
 

00:09:09.240 --> 00:09:11.710 align:start position:0%
quering so this is how you can Define
different<00:09:09.800><c> uh</c><00:09:09.959><c> extractors</c><00:09:10.600><c> and</c><00:09:10.839><c> retrievers</c>

00:09:11.710 --> 00:09:11.720 align:start position:0%
different uh extractors and retrievers
 

00:09:11.720 --> 00:09:15.150 align:start position:0%
different uh extractors and retrievers
and<00:09:12.720><c> using</c><00:09:13.079><c> property</c><00:09:13.519><c> graph</c><00:09:14.000><c> you</c><00:09:14.120><c> can</c><00:09:14.279><c> start</c>

00:09:15.150 --> 00:09:15.160 align:start position:0%
and using property graph you can start
 

00:09:15.160 --> 00:09:18.069 align:start position:0%
and using property graph you can start
querying<00:09:16.160><c> so</c><00:09:16.440><c> in</c><00:09:16.560><c> the</c><00:09:16.680><c> next</c><00:09:16.920><c> video</c><00:09:17.320><c> we'll</c><00:09:17.959><c> uh</c>

00:09:18.069 --> 00:09:18.079 align:start position:0%
querying so in the next video we'll uh
 

00:09:18.079 --> 00:09:20.870 align:start position:0%
querying so in the next video we'll uh
look<00:09:18.320><c> into</c><00:09:18.959><c> uh</c><00:09:19.120><c> how</c><00:09:19.279><c> you</c><00:09:19.399><c> can</c><00:09:19.560><c> use</c><00:09:20.079><c> C</c><00:09:20.560><c> uh</c><00:09:20.680><c> these</c>

00:09:20.870 --> 00:09:20.880 align:start position:0%
look into uh how you can use C uh these
 

00:09:20.880 --> 00:09:23.190 align:start position:0%
look into uh how you can use C uh these
retrievers<00:09:21.440><c> and</c><00:09:21.640><c> extractors</c><00:09:22.519><c> and</c><00:09:22.720><c> customize</c>

00:09:23.190 --> 00:09:23.200 align:start position:0%
retrievers and extractors and customize
 

00:09:23.200 --> 00:09:26.630 align:start position:0%
retrievers and extractors and customize
your<00:09:23.600><c> uh</c><00:09:23.720><c> property</c><00:09:24.160><c> graph</c><00:09:24.519><c> in</c><00:09:25.079><c> uh</c><00:09:25.440><c> and</c><00:09:25.600><c> make</c><00:09:25.800><c> it</c>

00:09:26.630 --> 00:09:26.640 align:start position:0%
your uh property graph in uh and make it
 

00:09:26.640 --> 00:09:28.829 align:start position:0%
your uh property graph in uh and make it
customizable<00:09:27.640><c> see</c><00:09:27.800><c> you</c><00:09:27.880><c> in</c><00:09:27.959><c> the</c><00:09:28.079><c> next</c><00:09:28.320><c> video</c>

00:09:28.829 --> 00:09:28.839 align:start position:0%
customizable see you in the next video
 

00:09:28.839 --> 00:09:31.360 align:start position:0%
customizable see you in the next video
thank<00:09:29.040><c> you</c>

