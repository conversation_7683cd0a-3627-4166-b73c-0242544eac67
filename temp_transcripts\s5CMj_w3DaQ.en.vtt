WEBVTT
Kind: captions
Language: en

00:00:02.550 --> 00:00:07.389 align:start position:0%
 
[Music]

00:00:07.389 --> 00:00:07.399 align:start position:0%
[Music]
 

00:00:07.399 --> 00:00:09.589 align:start position:0%
[Music]
hello<00:00:08.160><c> in</c><00:00:08.360><c> this</c><00:00:08.559><c> video</c><00:00:09.000><c> I'm</c><00:00:09.120><c> going</c><00:00:09.280><c> to</c><00:00:09.360><c> give</c><00:00:09.519><c> a</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
hello in this video I'm going to give a
 

00:00:09.599 --> 00:00:10.910 align:start position:0%
hello in this video I'm going to give a
short<00:00:09.840><c> overview</c><00:00:10.240><c> of</c><00:00:10.360><c> how</c><00:00:10.480><c> you</c><00:00:10.559><c> can</c><00:00:10.679><c> use</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
short overview of how you can use
 

00:00:10.920 --> 00:00:12.749 align:start position:0%
short overview of how you can use
weights<00:00:11.160><c> and</c><00:00:11.320><c> biases</c><00:00:11.799><c> to</c><00:00:11.960><c> deploy</c><00:00:12.280><c> your</c><00:00:12.480><c> custom</c>

00:00:12.749 --> 00:00:12.759 align:start position:0%
weights and biases to deploy your custom
 

00:00:12.759 --> 00:00:14.589 align:start position:0%
weights and biases to deploy your custom
models<00:00:13.080><c> to</c><00:00:13.240><c> sagemaker</c><00:00:13.799><c> endpoints</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
models to sagemaker endpoints
 

00:00:14.599 --> 00:00:16.430 align:start position:0%
models to sagemaker endpoints
automatically<00:00:15.280><c> from</c><00:00:15.519><c> weights</c><00:00:15.759><c> and</c><00:00:15.879><c> biases</c><00:00:16.320><c> in</c>

00:00:16.430 --> 00:00:16.440 align:start position:0%
automatically from weights and biases in
 

00:00:16.440 --> 00:00:18.910 align:start position:0%
automatically from weights and biases in
a<00:00:16.600><c> really</c><00:00:16.880><c> easy</c><00:00:17.199><c> way</c><00:00:17.800><c> capturing</c><00:00:18.279><c> the</c><00:00:18.560><c> pipeline</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
a really easy way capturing the pipeline
 

00:00:18.920 --> 00:00:21.710 align:start position:0%
a really easy way capturing the pipeline
for<00:00:19.400><c> reproducibility</c><00:00:20.400><c> as</c><00:00:20.560><c> we</c><00:00:20.720><c> do</c><00:00:20.920><c> so</c><00:00:21.640><c> and</c>

00:00:21.710 --> 00:00:21.720 align:start position:0%
for reproducibility as we do so and
 

00:00:21.720 --> 00:00:24.109 align:start position:0%
for reproducibility as we do so and
there's<00:00:21.880><c> a</c><00:00:22.000><c> few</c><00:00:22.199><c> key</c><00:00:22.439><c> steps</c><00:00:22.840><c> here</c><00:00:23.599><c> the</c><00:00:23.760><c> first</c>

00:00:24.109 --> 00:00:24.119 align:start position:0%
there's a few key steps here the first
 

00:00:24.119 --> 00:00:25.310 align:start position:0%
there's a few key steps here the first
is<00:00:24.240><c> we're</c><00:00:24.359><c> going</c><00:00:24.480><c> to</c><00:00:24.560><c> be</c><00:00:24.640><c> using</c><00:00:24.920><c> wits</c><00:00:25.160><c> and</c>

00:00:25.310 --> 00:00:25.320 align:start position:0%
is we're going to be using wits and
 

00:00:25.320 --> 00:00:27.310 align:start position:0%
is we're going to be using wits and
biases<00:00:25.720><c> for</c><00:00:25.920><c> experiment</c><00:00:26.359><c> tracking</c><00:00:27.080><c> so</c><00:00:27.199><c> we're</c>

00:00:27.310 --> 00:00:27.320 align:start position:0%
biases for experiment tracking so we're
 

00:00:27.320 --> 00:00:29.109 align:start position:0%
biases for experiment tracking so we're
going<00:00:27.400><c> to</c><00:00:27.519><c> be</c><00:00:27.599><c> logging</c><00:00:27.880><c> our</c><00:00:28.039><c> Matrix</c><00:00:28.560><c> or</c><00:00:28.800><c> hyper</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
going to be logging our Matrix or hyper
 

00:00:29.119 --> 00:00:31.749 align:start position:0%
going to be logging our Matrix or hyper
parameters<00:00:30.000><c> but</c><00:00:30.240><c> also</c><00:00:30.560><c> the</c><00:00:30.759><c> actual</c><00:00:31.080><c> models</c>

00:00:31.749 --> 00:00:31.759 align:start position:0%
parameters but also the actual models
 

00:00:31.759 --> 00:00:34.630 align:start position:0%
parameters but also the actual models
themselves<00:00:32.239><c> to</c><00:00:32.559><c> weights</c><00:00:32.840><c> and</c><00:00:33.520><c> biases</c><00:00:34.520><c> and</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
themselves to weights and biases and
 

00:00:34.640 --> 00:00:36.630 align:start position:0%
themselves to weights and biases and
this<00:00:34.760><c> means</c><00:00:35.079><c> from</c><00:00:35.320><c> here</c><00:00:35.559><c> we</c><00:00:35.719><c> can</c><00:00:36.280><c> do</c><00:00:36.480><c> the</c>

00:00:36.630 --> 00:00:36.640 align:start position:0%
this means from here we can do the
 

00:00:36.640 --> 00:00:39.790 align:start position:0%
this means from here we can do the
deployment<00:00:37.120><c> to</c><00:00:37.360><c> siemer</c><00:00:38.600><c> endpoints</c><00:00:39.600><c> we're</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
deployment to siemer endpoints we're
 

00:00:39.800 --> 00:00:42.110 align:start position:0%
deployment to siemer endpoints we're
also<00:00:39.960><c> going</c><00:00:40.079><c> to</c><00:00:40.239><c> support</c><00:00:40.840><c> complex</c><00:00:41.520><c> or</c><00:00:41.760><c> custom</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
also going to support complex or custom
 

00:00:42.120 --> 00:00:44.389 align:start position:0%
also going to support complex or custom
inference<00:00:42.680><c> pipelines</c><00:00:43.680><c> so</c><00:00:43.920><c> this</c><00:00:44.079><c> typically</c>

00:00:44.389 --> 00:00:44.399 align:start position:0%
inference pipelines so this typically
 

00:00:44.399 --> 00:00:45.630 align:start position:0%
inference pipelines so this typically
means<00:00:44.640><c> we</c><00:00:44.760><c> need</c><00:00:44.879><c> to</c><00:00:45.000><c> create</c><00:00:45.320><c> custom</c>

00:00:45.630 --> 00:00:45.640 align:start position:0%
means we need to create custom
 

00:00:45.640 --> 00:00:47.270 align:start position:0%
means we need to create custom
containers<00:00:46.160><c> to</c><00:00:46.320><c> run</c><00:00:46.480><c> them</c><00:00:46.600><c> on</c><00:00:46.719><c> siemer</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
containers to run them on siemer
 

00:00:47.280 --> 00:00:49.229 align:start position:0%
containers to run them on siemer
endpoints<00:00:48.079><c> but</c><00:00:48.199><c> the</c><00:00:48.320><c> cool</c><00:00:48.600><c> thing</c><00:00:48.760><c> is</c><00:00:48.920><c> we</c><00:00:49.000><c> can</c>

00:00:49.229 --> 00:00:49.239 align:start position:0%
endpoints but the cool thing is we can
 

00:00:49.239 --> 00:00:51.229 align:start position:0%
endpoints but the cool thing is we can
also<00:00:49.600><c> track</c><00:00:50.039><c> this</c><00:00:50.239><c> with</c><00:00:50.399><c> weights</c><00:00:50.640><c> and</c><00:00:50.760><c> biases</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
also track this with weights and biases
 

00:00:51.239 --> 00:00:53.630 align:start position:0%
also track this with weights and biases
as<00:00:51.399><c> well</c><00:00:51.879><c> and</c><00:00:52.000><c> this</c><00:00:52.120><c> means</c><00:00:52.399><c> we</c><00:00:52.520><c> can</c><00:00:52.680><c> see</c><00:00:53.399><c> which</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
as well and this means we can see which
 

00:00:53.640 --> 00:00:55.430 align:start position:0%
as well and this means we can see which
deployments<00:00:54.199><c> used</c><00:00:54.559><c> Which</c><00:00:54.760><c> models</c><00:00:55.280><c> which</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
deployments used Which models which
 

00:00:55.440 --> 00:00:57.069 align:start position:0%
deployments used Which models which
containers<00:00:55.879><c> they</c><00:00:56.000><c> were</c><00:00:56.160><c> deployed</c><00:00:56.559><c> to</c><00:00:56.879><c> who</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
containers they were deployed to who
 

00:00:57.079 --> 00:00:58.709 align:start position:0%
containers they were deployed to who
done<00:00:57.239><c> the</c><00:00:57.359><c> deployment</c><00:00:57.800><c> and</c><00:00:58.000><c> when</c><00:00:58.280><c> the</c><00:00:58.440><c> code</c>

00:00:58.709 --> 00:00:58.719 align:start position:0%
done the deployment and when the code
 

00:00:58.719 --> 00:01:00.389 align:start position:0%
done the deployment and when the code
the<00:00:58.840><c> hyper</c><00:00:59.120><c> parameters</c><00:00:59.559><c> and</c><00:00:59.640><c> so</c><00:01:00.000><c> on</c><00:01:00.160><c> all</c><00:01:00.280><c> of</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
the hyper parameters and so on all of
 

00:01:00.399 --> 00:01:03.069 align:start position:0%
the hyper parameters and so on all of
this<00:01:00.519><c> will</c><00:01:00.680><c> be</c><00:01:00.800><c> tracked</c><00:01:01.160><c> for</c><00:01:01.719><c> us</c><00:01:02.719><c> we're</c><00:01:02.920><c> also</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
this will be tracked for us we're also
 

00:01:03.079 --> 00:01:05.109 align:start position:0%
this will be tracked for us we're also
going<00:01:03.199><c> to</c><00:01:03.320><c> need</c><00:01:03.480><c> a</c><00:01:03.680><c> job</c><00:01:04.479><c> that</c><00:01:04.600><c> is</c><00:01:04.760><c> pretty</c><00:01:04.960><c> much</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
going to need a job that is pretty much
 

00:01:05.119 --> 00:01:07.190 align:start position:0%
going to need a job that is pretty much
just<00:01:05.239><c> a</c><00:01:05.360><c> run</c><00:01:05.720><c> template</c><00:01:06.200><c> where</c><00:01:06.320><c> we</c><00:01:06.439><c> can</c><00:01:06.680><c> pass</c><00:01:06.920><c> in</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
just a run template where we can pass in
 

00:01:07.200 --> 00:01:09.350 align:start position:0%
just a run template where we can pass in
different<00:01:07.520><c> settings</c><00:01:08.080><c> and</c><00:01:08.280><c> models</c><00:01:08.680><c> each</c><00:01:08.920><c> time</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
different settings and models each time
 

00:01:09.360 --> 00:01:11.749 align:start position:0%
different settings and models each time
there's<00:01:09.520><c> a</c><00:01:09.600><c> few</c><00:01:09.799><c> ways</c><00:01:09.960><c> of</c><00:01:10.119><c> doing</c><00:01:10.400><c> this</c><00:01:11.320><c> um</c>

00:01:11.749 --> 00:01:11.759 align:start position:0%
there's a few ways of doing this um
 

00:01:11.759 --> 00:01:13.510 align:start position:0%
there's a few ways of doing this um
programmatically<00:01:12.360><c> or</c><00:01:12.520><c> via</c><00:01:12.720><c> the</c><00:01:12.799><c> UI</c><00:01:13.200><c> and</c><00:01:13.320><c> we'll</c>

00:01:13.510 --> 00:01:13.520 align:start position:0%
programmatically or via the UI and we'll
 

00:01:13.520 --> 00:01:16.149 align:start position:0%
programmatically or via the UI and we'll
see<00:01:13.680><c> a</c><00:01:13.840><c> UI</c><00:01:14.159><c> based</c><00:01:14.439><c> workflow</c><00:01:14.920><c> today</c><00:01:15.920><c> and</c>

00:01:16.149 --> 00:01:16.159 align:start position:0%
see a UI based workflow today and
 

00:01:16.159 --> 00:01:17.550 align:start position:0%
see a UI based workflow today and
typically<00:01:16.520><c> this</c><00:01:16.680><c> deployment</c><00:01:17.080><c> step</c><00:01:17.360><c> is</c><00:01:17.439><c> going</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
typically this deployment step is going
 

00:01:17.560 --> 00:01:19.630 align:start position:0%
typically this deployment step is going
to<00:01:17.640><c> be</c><00:01:17.840><c> pretty</c><00:01:18.159><c> frequent</c><00:01:18.680><c> so</c><00:01:18.799><c> for</c><00:01:19.119><c> a</c><00:01:19.280><c> given</c>

00:01:19.630 --> 00:01:19.640 align:start position:0%
to be pretty frequent so for a given
 

00:01:19.640 --> 00:01:22.069 align:start position:0%
to be pretty frequent so for a given
project<00:01:20.119><c> or</c><00:01:20.240><c> a</c><00:01:20.400><c> given</c><00:01:20.640><c> inference</c><00:01:21.159><c> pipeline</c><00:01:21.960><c> we</c>

00:01:22.069 --> 00:01:22.079 align:start position:0%
project or a given inference pipeline we
 

00:01:22.079 --> 00:01:25.030 align:start position:0%
project or a given inference pipeline we
may<00:01:22.320><c> deploy</c><00:01:22.680><c> many</c><00:01:23.159><c> models</c><00:01:24.159><c> to</c><00:01:24.360><c> it</c><00:01:24.520><c> over</c><00:01:24.759><c> time</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
may deploy many models to it over time
 

00:01:25.040 --> 00:01:27.270 align:start position:0%
may deploy many models to it over time
as<00:01:25.159><c> we</c><00:01:25.280><c> iteratively</c><00:01:25.880><c> improve</c><00:01:26.240><c> on</c><00:01:26.400><c> the</c><00:01:26.520><c> models</c>

00:01:27.270 --> 00:01:27.280 align:start position:0%
as we iteratively improve on the models
 

00:01:27.280 --> 00:01:29.069 align:start position:0%
as we iteratively improve on the models
so<00:01:27.600><c> it</c><00:01:27.759><c> really</c><00:01:27.960><c> makes</c><00:01:28.200><c> sense</c><00:01:28.400><c> to</c><00:01:28.560><c> automate</c><00:01:28.960><c> the</c>

00:01:29.069 --> 00:01:29.079 align:start position:0%
so it really makes sense to automate the
 

00:01:29.079 --> 00:01:30.990 align:start position:0%
so it really makes sense to automate the
deployment<00:01:29.520><c> step</c><00:01:30.200><c> so</c><00:01:30.360><c> let's</c><00:01:30.520><c> have</c><00:01:30.640><c> a</c><00:01:30.720><c> look</c><00:01:30.880><c> at</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
deployment step so let's have a look at
 

00:01:31.000 --> 00:01:33.630 align:start position:0%
deployment step so let's have a look at
how<00:01:31.119><c> we</c><00:01:31.200><c> can</c><00:01:31.360><c> do</c><00:01:31.600><c> this</c><00:01:31.840><c> in</c><00:01:32.000><c> weights</c><00:01:32.280><c> and</c><00:01:32.560><c> biases</c>

00:01:33.630 --> 00:01:33.640 align:start position:0%
how we can do this in weights and biases
 

00:01:33.640 --> 00:01:37.109 align:start position:0%
how we can do this in weights and biases
so<00:01:34.640><c> let's</c><00:01:34.840><c> go</c><00:01:34.960><c> to</c><00:01:35.119><c> a</c><00:01:35.520><c> project</c><00:01:36.520><c> so</c><00:01:36.759><c> here</c><00:01:36.920><c> we</c><00:01:37.000><c> can</c>

00:01:37.109 --> 00:01:37.119 align:start position:0%
so let's go to a project so here we can
 

00:01:37.119 --> 00:01:39.030 align:start position:0%
so let's go to a project so here we can
see<00:01:37.320><c> we</c><00:01:37.439><c> have</c><00:01:37.520><c> a</c><00:01:37.640><c> project</c><00:01:38.119><c> there's</c><00:01:38.360><c> 99</c>

00:01:39.030 --> 00:01:39.040 align:start position:0%
see we have a project there's 99
 

00:01:39.040 --> 00:01:40.990 align:start position:0%
see we have a project there's 99
different<00:01:39.439><c> training</c><00:01:39.799><c> runs</c><00:01:40.560><c> we</c><00:01:40.680><c> can</c><00:01:40.759><c> see</c>

00:01:40.990 --> 00:01:41.000 align:start position:0%
different training runs we can see
 

00:01:41.000 --> 00:01:42.789 align:start position:0%
different training runs we can see
matrics<00:01:41.479><c> that</c><00:01:41.600><c> have</c><00:01:41.759><c> been</c><00:01:41.920><c> logged</c><00:01:42.479><c> training</c>

00:01:42.789 --> 00:01:42.799 align:start position:0%
matrics that have been logged training
 

00:01:42.799 --> 00:01:44.910 align:start position:0%
matrics that have been logged training
loss<00:01:43.200><c> training</c><00:01:43.520><c> accuracy</c><00:01:43.960><c> validation</c>

00:01:44.910 --> 00:01:44.920 align:start position:0%
loss training accuracy validation
 

00:01:44.920 --> 00:01:47.789 align:start position:0%
loss training accuracy validation
accuracy<00:01:45.920><c> um</c><00:01:46.360><c> and</c><00:01:46.479><c> I've</c><00:01:46.759><c> already</c><00:01:47.280><c> you</c><00:01:47.439><c> know</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
accuracy um and I've already you know
 

00:01:47.799 --> 00:01:49.990 align:start position:0%
accuracy um and I've already you know
sorted<00:01:48.600><c> our</c><00:01:48.880><c> experiments</c><00:01:49.399><c> by</c><00:01:49.560><c> validation</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
sorted our experiments by validation
 

00:01:50.000 --> 00:01:51.310 align:start position:0%
sorted our experiments by validation
accuracy<00:01:50.360><c> so</c><00:01:50.520><c> that's</c><00:01:50.680><c> what</c><00:01:50.920><c> we're</c><00:01:51.040><c> going</c><00:01:51.200><c> to</c>

00:01:51.310 --> 00:01:51.320 align:start position:0%
accuracy so that's what we're going to
 

00:01:51.320 --> 00:01:52.310 align:start position:0%
accuracy so that's what we're going to
choose<00:01:51.560><c> so</c><00:01:51.640><c> we're</c><00:01:51.759><c> going</c><00:01:51.840><c> to</c><00:01:51.920><c> have</c><00:01:52.000><c> a</c><00:01:52.079><c> look</c><00:01:52.200><c> at</c>

00:01:52.310 --> 00:01:52.320 align:start position:0%
choose so we're going to have a look at
 

00:01:52.320 --> 00:01:53.910 align:start position:0%
choose so we're going to have a look at
our<00:01:52.439><c> experiments</c><00:01:53.240><c> and</c><00:01:53.439><c> we're</c><00:01:53.560><c> going</c><00:01:53.680><c> to</c>

00:01:53.910 --> 00:01:53.920 align:start position:0%
our experiments and we're going to
 

00:01:53.920 --> 00:01:55.550 align:start position:0%
our experiments and we're going to
decide<00:01:54.520><c> we're</c><00:01:54.680><c> going</c><00:01:54.799><c> to</c><00:01:54.920><c> choose</c><00:01:55.119><c> the</c><00:01:55.280><c> model</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
decide we're going to choose the model
 

00:01:55.560 --> 00:01:57.630 align:start position:0%
decide we're going to choose the model
with<00:01:55.719><c> the</c><00:01:55.880><c> best</c><00:01:56.159><c> validation</c><00:01:56.759><c> accuracy</c><00:01:57.399><c> to</c>

00:01:57.630 --> 00:01:57.640 align:start position:0%
with the best validation accuracy to
 

00:01:57.640 --> 00:02:00.749 align:start position:0%
with the best validation accuracy to
deploy<00:01:58.399><c> so</c><00:01:58.600><c> how</c><00:01:58.759><c> would</c><00:01:58.920><c> we</c><00:01:59.039><c> do</c><00:01:59.280><c> that</c><00:02:00.000><c> well</c>

00:02:00.749 --> 00:02:00.759 align:start position:0%
deploy so how would we do that well
 

00:02:00.759 --> 00:02:03.469 align:start position:0%
deploy so how would we do that well
let's<00:02:01.439><c> select</c><00:02:02.000><c> this</c><00:02:02.240><c> experiment</c><00:02:03.159><c> and</c><00:02:03.280><c> we</c><00:02:03.360><c> can</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
let's select this experiment and we can
 

00:02:03.479 --> 00:02:05.190 align:start position:0%
let's select this experiment and we can
see<00:02:03.680><c> you</c><00:02:03.799><c> know</c><00:02:03.960><c> more</c><00:02:04.159><c> metrix</c><00:02:04.719><c> here</c><00:02:04.920><c> as</c><00:02:05.039><c> well</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
see you know more metrix here as well
 

00:02:05.200 --> 00:02:07.469 align:start position:0%
see you know more metrix here as well
some<00:02:05.360><c> more</c><00:02:05.560><c> information</c><00:02:06.039><c> about</c><00:02:06.240><c> it</c><00:02:07.159><c> if</c><00:02:07.240><c> we</c><00:02:07.360><c> go</c>

00:02:07.469 --> 00:02:07.479 align:start position:0%
some more information about it if we go
 

00:02:07.479 --> 00:02:10.190 align:start position:0%
some more information about it if we go
to<00:02:07.640><c> the</c><00:02:07.799><c> overview</c><00:02:08.239><c> tab</c><00:02:08.599><c> we</c><00:02:08.720><c> can</c><00:02:08.840><c> see</c><00:02:09.160><c> the</c><00:02:09.640><c> model</c>

00:02:10.190 --> 00:02:10.200 align:start position:0%
to the overview tab we can see the model
 

00:02:10.200 --> 00:02:12.270 align:start position:0%
to the overview tab we can see the model
that<00:02:10.319><c> was</c><00:02:10.520><c> logged</c><00:02:11.000><c> by</c><00:02:11.200><c> this</c><00:02:11.360><c> run</c><00:02:11.680><c> so</c><00:02:11.959><c> it</c><00:02:12.040><c> was</c><00:02:12.160><c> an</c>

00:02:12.270 --> 00:02:12.280 align:start position:0%
that was logged by this run so it was an
 

00:02:12.280 --> 00:02:15.869 align:start position:0%
that was logged by this run so it was an
artifact<00:02:12.879><c> output</c><00:02:13.760><c> so</c><00:02:14.080><c> it</c><00:02:14.239><c> was</c><00:02:14.599><c> clf</c><00:02:15.440><c> the</c><00:02:15.560><c> model</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
artifact output so it was clf the model
 

00:02:15.879 --> 00:02:17.309 align:start position:0%
artifact output so it was clf the model
name<00:02:16.200><c> version</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
name version
 

00:02:17.319 --> 00:02:20.030 align:start position:0%
name version
56<00:02:18.319><c> so</c><00:02:18.959><c> you</c><00:02:19.040><c> know</c><00:02:19.280><c> we've</c><00:02:19.519><c> had</c><00:02:19.640><c> a</c><00:02:19.720><c> look</c><00:02:19.840><c> at</c><00:02:19.920><c> the</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
56 so you know we've had a look at the
 

00:02:20.040 --> 00:02:21.229 align:start position:0%
56 so you know we've had a look at the
results<00:02:20.319><c> we're</c><00:02:20.480><c> happy</c><00:02:20.680><c> with</c><00:02:20.800><c> it</c><00:02:20.920><c> we</c><00:02:21.000><c> want</c><00:02:21.120><c> to</c>

00:02:21.229 --> 00:02:21.239 align:start position:0%
results we're happy with it we want to
 

00:02:21.239 --> 00:02:24.390 align:start position:0%
results we're happy with it we want to
deploy<00:02:21.640><c> it</c><00:02:22.120><c> so</c><00:02:22.319><c> let's</c><00:02:22.560><c> select</c><00:02:22.959><c> it</c><00:02:23.920><c> so</c><00:02:24.160><c> here</c><00:02:24.280><c> we</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
deploy it so let's select it so here we
 

00:02:24.400 --> 00:02:27.150 align:start position:0%
deploy it so let's select it so here we
can<00:02:24.560><c> see</c><00:02:25.360><c> we're</c><00:02:25.560><c> in</c><00:02:25.680><c> the</c><00:02:25.840><c> artifacts</c><00:02:26.400><c> tab</c><00:02:27.000><c> of</c>

00:02:27.150 --> 00:02:27.160 align:start position:0%
can see we're in the artifacts tab of
 

00:02:27.160 --> 00:02:28.270 align:start position:0%
can see we're in the artifacts tab of
the<00:02:27.280><c> weights</c><00:02:27.519><c> and</c>

00:02:28.270 --> 00:02:28.280 align:start position:0%
the weights and
 

00:02:28.280 --> 00:02:31.830 align:start position:0%
the weights and
biases<00:02:29.280><c> um</c><00:02:30.000><c> project</c><00:02:31.000><c> and</c><00:02:31.120><c> we</c><00:02:31.239><c> have</c><00:02:31.400><c> this</c><00:02:31.560><c> link</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
biases um project and we have this link
 

00:02:31.840 --> 00:02:35.030 align:start position:0%
biases um project and we have this link
to<00:02:32.040><c> registry</c><00:02:32.560><c> button</c><00:02:33.280><c> so</c><00:02:33.519><c> we</c><00:02:33.640><c> have</c><00:02:33.800><c> set</c><00:02:34.040><c> up</c><00:02:34.920><c> um</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
to registry button so we have set up um
 

00:02:35.040 --> 00:02:37.830 align:start position:0%
to registry button so we have set up um
an<00:02:35.200><c> automation</c><00:02:35.720><c> such</c><00:02:36.120><c> that</c><00:02:36.519><c> any</c><00:02:37.239><c> time</c><00:02:37.599><c> you</c>

00:02:37.830 --> 00:02:37.840 align:start position:0%
an automation such that any time you
 

00:02:37.840 --> 00:02:41.390 align:start position:0%
an automation such that any time you
autom<00:02:38.360><c> model</c><00:02:39.360><c> version</c><00:02:39.879><c> to</c><00:02:40.159><c> this</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
autom model version to this
 

00:02:41.400 --> 00:02:44.229 align:start position:0%
autom model version to this
AI<00:02:42.400><c> model</c><00:02:42.840><c> registry</c><00:02:43.400><c> it'll</c><00:02:43.599><c> be</c><00:02:43.760><c> deployed</c><00:02:44.120><c> to</c>

00:02:44.229 --> 00:02:44.239 align:start position:0%
AI model registry it'll be deployed to
 

00:02:44.239 --> 00:02:47.110 align:start position:0%
AI model registry it'll be deployed to
cemer<00:02:44.920><c> end</c><00:02:45.239><c> points</c><00:02:45.959><c> so</c><00:02:46.159><c> all</c><00:02:46.319><c> we</c><00:02:46.440><c> have</c><00:02:46.560><c> to</c><00:02:46.720><c> do</c><00:02:46.920><c> is</c>

00:02:47.110 --> 00:02:47.120 align:start position:0%
cemer end points so all we have to do is
 

00:02:47.120 --> 00:02:48.910 align:start position:0%
cemer end points so all we have to do is
link<00:02:47.440><c> this</c><00:02:47.599><c> version</c><00:02:47.879><c> to</c><00:02:48.080><c> the</c>

00:02:48.910 --> 00:02:48.920 align:start position:0%
link this version to the
 

00:02:48.920 --> 00:02:51.030 align:start position:0%
link this version to the
registry<00:02:49.920><c> we're</c><00:02:50.080><c> going</c><00:02:50.200><c> to</c><00:02:50.280><c> see</c><00:02:50.480><c> a</c><00:02:50.760><c> small</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
registry we're going to see a small
 

00:02:51.040 --> 00:02:53.149 align:start position:0%
registry we're going to see a small
notification<00:02:51.599><c> I</c><00:02:51.760><c> just</c><00:02:51.879><c> say</c><00:02:52.080><c> it's</c><00:02:52.280><c> been</c><00:02:52.560><c> added</c>

00:02:53.149 --> 00:02:53.159 align:start position:0%
notification I just say it's been added
 

00:02:53.159 --> 00:02:54.830 align:start position:0%
notification I just say it's been added
so<00:02:53.280><c> let's</c><00:02:53.440><c> go</c><00:02:53.560><c> and</c><00:02:53.680><c> have</c><00:02:53.840><c> a</c><00:02:53.959><c> look</c><00:02:54.080><c> at</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
so let's go and have a look at
 

00:02:54.840 --> 00:02:57.949 align:start position:0%
so let's go and have a look at
this<00:02:55.840><c> so</c><00:02:56.040><c> this</c><00:02:56.120><c> is</c><00:02:56.239><c> our</c><00:02:56.440><c> AI</c><00:02:57.120><c> registered</c><00:02:57.599><c> model</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
this so this is our AI registered model
 

00:02:57.959 --> 00:02:59.990 align:start position:0%
this so this is our AI registered model
here's<00:02:58.120><c> our</c><00:02:58.280><c> model</c><00:02:58.640><c> card</c><00:02:59.280><c> if</c><00:02:59.400><c> I</c><00:02:59.519><c> SC</c><00:02:59.720><c> scroll</c>

00:02:59.990 --> 00:03:00.000 align:start position:0%
here's our model card if I SC scroll
 

00:03:00.000 --> 00:03:02.550 align:start position:0%
here's our model card if I SC scroll
down<00:03:00.239><c> we</c><00:03:00.319><c> can</c><00:03:00.440><c> see</c><00:03:00.680><c> version</c><00:03:01.040><c> three</c><00:03:02.040><c> logged</c><00:03:02.400><c> by</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
down we can see version three logged by
 

00:03:02.560 --> 00:03:05.390 align:start position:0%
down we can see version three logged by
upbeat<00:03:02.959><c> sweep</c><00:03:03.319><c> 55</c><00:03:03.879><c> has</c><00:03:04.040><c> now</c><00:03:04.280><c> been</c><00:03:04.519><c> added</c><00:03:05.200><c> to</c>

00:03:05.390 --> 00:03:05.400 align:start position:0%
upbeat sweep 55 has now been added to
 

00:03:05.400 --> 00:03:07.710 align:start position:0%
upbeat sweep 55 has now been added to
our<00:03:05.640><c> model</c><00:03:06.239><c> registry</c><00:03:07.159><c> and</c><00:03:07.280><c> we</c><00:03:07.360><c> can</c><00:03:07.440><c> see</c><00:03:07.599><c> we</c>

00:03:07.710 --> 00:03:07.720 align:start position:0%
our model registry and we can see we
 

00:03:07.720 --> 00:03:10.710 align:start position:0%
our model registry and we can see we
have<00:03:07.840><c> an</c><00:03:07.959><c> automation</c><00:03:08.560><c> here</c><00:03:09.000><c> that's</c><00:03:09.200><c> going</c><00:03:09.720><c> to</c>

00:03:10.710 --> 00:03:10.720 align:start position:0%
have an automation here that's going to
 

00:03:10.720 --> 00:03:13.229 align:start position:0%
have an automation here that's going to
take<00:03:11.120><c> any</c><00:03:11.519><c> version</c><00:03:12.400><c> of</c><00:03:12.560><c> the</c><00:03:12.720><c> model</c><00:03:13.080><c> that's</c>

00:03:13.229 --> 00:03:13.239 align:start position:0%
take any version of the model that's
 

00:03:13.239 --> 00:03:14.710 align:start position:0%
take any version of the model that's
been<00:03:13.360><c> added</c><00:03:13.599><c> to</c><00:03:13.720><c> this</c><00:03:13.840><c> registry</c><00:03:14.239><c> and</c><00:03:14.400><c> deploy</c>

00:03:14.710 --> 00:03:14.720 align:start position:0%
been added to this registry and deploy
 

00:03:14.720 --> 00:03:16.550 align:start position:0%
been added to this registry and deploy
it<00:03:14.799><c> to</c><00:03:14.920><c> sagemaker</c><00:03:15.599><c> we</c><00:03:15.760><c> can</c><00:03:15.879><c> also</c><00:03:16.080><c> be</c><00:03:16.239><c> more</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
it to sagemaker we can also be more
 

00:03:16.560 --> 00:03:19.470 align:start position:0%
it to sagemaker we can also be more
advanced<00:03:16.959><c> and</c><00:03:17.159><c> say</c><00:03:17.640><c> only</c><00:03:17.879><c> do</c><00:03:18.200><c> this</c><00:03:18.720><c> deployment</c>

00:03:19.470 --> 00:03:19.480 align:start position:0%
advanced and say only do this deployment
 

00:03:19.480 --> 00:03:22.309 align:start position:0%
advanced and say only do this deployment
run<00:03:19.799><c> only</c><00:03:20.000><c> run</c><00:03:20.239><c> this</c><00:03:20.519><c> automation</c><00:03:21.519><c> if</c><00:03:22.159><c> the</c>

00:03:22.309 --> 00:03:22.319 align:start position:0%
run only run this automation if the
 

00:03:22.319 --> 00:03:25.030 align:start position:0%
run only run this automation if the
version<00:03:22.680><c> has</c><00:03:22.879><c> specific</c><00:03:23.280><c> tags</c><00:03:23.720><c> like</c>

00:03:25.030 --> 00:03:25.040 align:start position:0%
version has specific tags like
 

00:03:25.040 --> 00:03:27.190 align:start position:0%
version has specific tags like
production<00:03:26.040><c> so</c><00:03:26.200><c> let's</c><00:03:26.400><c> have</c><00:03:26.480><c> a</c><00:03:26.599><c> look</c><00:03:26.720><c> at</c><00:03:26.879><c> this</c>

00:03:27.190 --> 00:03:27.200 align:start position:0%
production so let's have a look at this
 

00:03:27.200 --> 00:03:30.830 align:start position:0%
production so let's have a look at this
inference<00:03:27.720><c> queue</c><00:03:28.400><c> which</c><00:03:28.519><c> is</c><00:03:28.720><c> where</c><00:03:29.080><c> our</c><00:03:29.840><c> model</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
inference queue which is where our model
 

00:03:30.840 --> 00:03:32.710 align:start position:0%
inference queue which is where our model
is<00:03:31.040><c> actually</c><00:03:31.319><c> going</c><00:03:31.480><c> to</c><00:03:31.640><c> be</c><00:03:32.439><c> um</c><00:03:32.599><c> the</c>

00:03:32.710 --> 00:03:32.720 align:start position:0%
is actually going to be um the
 

00:03:32.720 --> 00:03:33.910 align:start position:0%
is actually going to be um the
deployment<00:03:33.080><c> is</c><00:03:33.159><c> going</c><00:03:33.239><c> to</c><00:03:33.319><c> take</c><00:03:33.480><c> place</c><00:03:33.760><c> so</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
deployment is going to take place so
 

00:03:33.920 --> 00:03:35.830 align:start position:0%
deployment is going to take place so
let's<00:03:34.040><c> have</c><00:03:34.120><c> a</c><00:03:34.239><c> look</c><00:03:34.360><c> at</c><00:03:34.480><c> it</c><00:03:35.120><c> okay</c><00:03:35.480><c> so</c><00:03:35.640><c> we</c><00:03:35.720><c> can</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
let's have a look at it okay so we can
 

00:03:35.840 --> 00:03:38.110 align:start position:0%
let's have a look at it okay so we can
see<00:03:36.040><c> that</c><00:03:36.159><c> it's</c><00:03:36.319><c> currently</c><00:03:36.680><c> running</c><00:03:37.560><c> which</c><00:03:37.760><c> is</c>

00:03:38.110 --> 00:03:38.120 align:start position:0%
see that it's currently running which is
 

00:03:38.120 --> 00:03:41.630 align:start position:0%
see that it's currently running which is
great<00:03:39.120><c> um</c><00:03:39.840><c> so</c><00:03:40.400><c> let</c><00:03:40.799><c> this</c><00:03:40.959><c> might</c><00:03:41.200><c> take</c><00:03:41.360><c> a</c><00:03:41.480><c> few</c>

00:03:41.630 --> 00:03:41.640 align:start position:0%
great um so let this might take a few
 

00:03:41.640 --> 00:03:43.110 align:start position:0%
great um so let this might take a few
minutes<00:03:41.959><c> just</c><00:03:42.040><c> for</c><00:03:42.200><c> sagemaker</c><00:03:42.720><c> to</c><00:03:42.840><c> do</c><00:03:43.000><c> the</c>

00:03:43.110 --> 00:03:43.120 align:start position:0%
minutes just for sagemaker to do the
 

00:03:43.120 --> 00:03:45.710 align:start position:0%
minutes just for sagemaker to do the
actual<00:03:43.480><c> deployment</c><00:03:44.480><c> okay</c><00:03:44.640><c> so</c><00:03:44.840><c> I've</c><00:03:45.120><c> navigated</c>

00:03:45.710 --> 00:03:45.720 align:start position:0%
actual deployment okay so I've navigated
 

00:03:45.720 --> 00:03:47.990 align:start position:0%
actual deployment okay so I've navigated
to<00:03:46.000><c> Amazon</c><00:03:46.360><c> siemer</c><00:03:47.319><c> I'm</c><00:03:47.439><c> looking</c><00:03:47.680><c> at</c><00:03:47.840><c> our</c>

00:03:47.990 --> 00:03:48.000 align:start position:0%
to Amazon siemer I'm looking at our
 

00:03:48.000 --> 00:03:49.550 align:start position:0%
to Amazon siemer I'm looking at our
endpoints<00:03:48.640><c> and</c><00:03:48.760><c> I</c><00:03:48.840><c> can</c><00:03:48.959><c> see</c><00:03:49.159><c> that</c><00:03:49.319><c> this</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
endpoints and I can see that this
 

00:03:49.560 --> 00:03:53.229 align:start position:0%
endpoints and I can see that this
endpoint<00:03:50.560><c> is</c><00:03:50.799><c> currently</c><00:03:51.200><c> in</c><00:03:51.360><c> this</c><00:03:52.200><c> process</c><00:03:53.040><c> of</c>

00:03:53.229 --> 00:03:53.239 align:start position:0%
endpoint is currently in this process of
 

00:03:53.239 --> 00:03:56.390 align:start position:0%
endpoint is currently in this process of
being<00:03:53.560><c> created</c><00:03:54.560><c> okay</c><00:03:55.079><c> great</c><00:03:55.519><c> so</c><00:03:56.000><c> now</c><00:03:56.159><c> we</c><00:03:56.280><c> can</c>

00:03:56.390 --> 00:03:56.400 align:start position:0%
being created okay great so now we can
 

00:03:56.400 --> 00:03:58.270 align:start position:0%
being created okay great so now we can
see<00:03:56.599><c> our</c><00:03:56.799><c> model</c><00:03:57.239><c> that</c><00:03:57.439><c> we've</c><00:03:57.640><c> deployed</c><00:03:57.959><c> from</c><00:03:58.079><c> W</c>

00:03:58.270 --> 00:03:58.280 align:start position:0%
see our model that we've deployed from W
 

00:03:58.280 --> 00:04:00.069 align:start position:0%
see our model that we've deployed from W
ambass<00:03:58.720><c> is</c><00:03:58.879><c> finally</c><00:03:59.159><c> in</c><00:03:59.319><c> service</c><00:03:59.799><c> and</c><00:03:59.879><c> we</c><00:03:59.959><c> can</c>

00:04:00.069 --> 00:04:00.079 align:start position:0%
ambass is finally in service and we can
 

00:04:00.079 --> 00:04:03.670 align:start position:0%
ambass is finally in service and we can
see<00:04:00.239><c> it</c><00:04:00.360><c> has</c><00:04:00.640><c> this</c><00:04:00.799><c> name</c><00:04:01.120><c> here</c><00:04:01.400><c> clf</c><00:04:02.519><c> v56</c><00:04:03.519><c> so</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
see it has this name here clf v56 so
 

00:04:03.680 --> 00:04:06.429 align:start position:0%
see it has this name here clf v56 so
let's<00:04:03.879><c> go</c><00:04:04.120><c> back</c><00:04:04.319><c> to</c><00:04:04.959><c> where</c><00:04:05.159><c> it's</c><00:04:05.239><c> in</c><00:04:05.439><c> biases</c><00:04:06.280><c> so</c>

00:04:06.429 --> 00:04:06.439 align:start position:0%
let's go back to where it's in biases so
 

00:04:06.439 --> 00:04:07.470 align:start position:0%
let's go back to where it's in biases so
let's<00:04:06.599><c> have</c><00:04:06.720><c> a</c><00:04:06.799><c> look</c><00:04:06.920><c> and</c><00:04:07.040><c> we</c><00:04:07.120><c> should</c><00:04:07.239><c> see</c><00:04:07.400><c> that</c>

00:04:07.470 --> 00:04:07.480 align:start position:0%
let's have a look and we should see that
 

00:04:07.480 --> 00:04:08.869 align:start position:0%
let's have a look and we should see that
it's<00:04:07.640><c> finished</c><00:04:08.040><c> perfect</c><00:04:08.360><c> we</c><00:04:08.439><c> can</c><00:04:08.560><c> see</c><00:04:08.720><c> that</c>

00:04:08.869 --> 00:04:08.879 align:start position:0%
it's finished perfect we can see that
 

00:04:08.879 --> 00:04:10.509 align:start position:0%
it's finished perfect we can see that
this<00:04:08.959><c> is</c><00:04:09.120><c> the</c><00:04:09.280><c> run</c><00:04:09.799><c> so</c><00:04:09.959><c> let's</c><00:04:10.120><c> have</c><00:04:10.200><c> a</c><00:04:10.280><c> look</c><00:04:10.400><c> at</c>

00:04:10.509 --> 00:04:10.519 align:start position:0%
this is the run so let's have a look at
 

00:04:10.519 --> 00:04:12.149 align:start position:0%
this is the run so let's have a look at
the<00:04:10.599><c> Run</c><00:04:10.920><c> we</c><00:04:11.000><c> didn't</c><00:04:11.239><c> log</c><00:04:11.480><c> any</c><00:04:11.640><c> Matrix</c><00:04:12.040><c> so</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
the Run we didn't log any Matrix so
 

00:04:12.159 --> 00:04:15.270 align:start position:0%
the Run we didn't log any Matrix so
there's<00:04:12.319><c> no</c><00:04:12.519><c> charts</c><00:04:13.400><c> but</c><00:04:13.519><c> we</c><00:04:13.640><c> can</c><00:04:13.840><c> see</c><00:04:14.280><c> here</c>

00:04:15.270 --> 00:04:15.280 align:start position:0%
there's no charts but we can see here
 

00:04:15.280 --> 00:04:18.749 align:start position:0%
there's no charts but we can see here
that<00:04:16.040><c> um</c><00:04:16.720><c> we</c><00:04:17.120><c> have</c><00:04:17.479><c> the</c><00:04:17.639><c> endpoint</c><00:04:18.160><c> name</c><00:04:18.519><c> and</c><00:04:18.639><c> it</c>

00:04:18.749 --> 00:04:18.759 align:start position:0%
that um we have the endpoint name and it
 

00:04:18.759 --> 00:04:21.310 align:start position:0%
that um we have the endpoint name and it
is<00:04:18.919><c> indeed</c><00:04:19.239><c> the</c><00:04:19.359><c> same</c><00:04:19.600><c> endpoint</c><00:04:20.120><c> name</c><00:04:21.040><c> that</c><00:04:21.199><c> we</c>

00:04:21.310 --> 00:04:21.320 align:start position:0%
is indeed the same endpoint name that we
 

00:04:21.320 --> 00:04:24.110 align:start position:0%
is indeed the same endpoint name that we
saw<00:04:21.560><c> in</c><00:04:21.639><c> siemer</c><00:04:22.440><c> endpoint</c><00:04:22.960><c> so</c><00:04:23.160><c> we</c><00:04:23.320><c> can</c><00:04:23.880><c> keep</c>

00:04:24.110 --> 00:04:24.120 align:start position:0%
saw in siemer endpoint so we can keep
 

00:04:24.120 --> 00:04:25.870 align:start position:0%
saw in siemer endpoint so we can keep
track<00:04:24.479><c> of</c><00:04:24.759><c> which</c><00:04:24.960><c> runs</c><00:04:25.320><c> deployed</c><00:04:25.720><c> Which</c>

00:04:25.870 --> 00:04:25.880 align:start position:0%
track of which runs deployed Which
 

00:04:25.880 --> 00:04:28.550 align:start position:0%
track of which runs deployed Which
models<00:04:26.680><c> but</c><00:04:26.840><c> more</c><00:04:27.120><c> than</c><00:04:27.320><c> that</c><00:04:27.960><c> so</c><00:04:28.199><c> not</c><00:04:28.360><c> only</c>

00:04:28.550 --> 00:04:28.560 align:start position:0%
models but more than that so not only
 

00:04:28.560 --> 00:04:30.110 align:start position:0%
models but more than that so not only
have<00:04:28.680><c> we</c><00:04:28.840><c> just</c><00:04:29.080><c> deployed</c>

00:04:30.110 --> 00:04:30.120 align:start position:0%
have we just deployed
 

00:04:30.120 --> 00:04:32.830 align:start position:0%
have we just deployed
the<00:04:30.280><c> model</c><00:04:30.680><c> to</c><00:04:31.320><c> siemer</c><00:04:31.880><c> endpoints</c>

00:04:32.830 --> 00:04:32.840 align:start position:0%
the model to siemer endpoints
 

00:04:32.840 --> 00:04:35.510 align:start position:0%
the model to siemer endpoints
automatically<00:04:33.840><c> um</c><00:04:34.280><c> we</c><00:04:34.400><c> also</c><00:04:34.600><c> have</c><00:04:34.759><c> lineage</c><00:04:35.280><c> so</c>

00:04:35.510 --> 00:04:35.520 align:start position:0%
automatically um we also have lineage so
 

00:04:35.520 --> 00:04:37.830 align:start position:0%
automatically um we also have lineage so
let's<00:04:35.680><c> go</c><00:04:35.759><c> to</c><00:04:35.880><c> the</c><00:04:35.960><c> artifacts</c><00:04:36.479><c> tab</c><00:04:36.960><c> of</c><00:04:37.080><c> our</c>

00:04:37.830 --> 00:04:37.840 align:start position:0%
let's go to the artifacts tab of our
 

00:04:37.840 --> 00:04:39.390 align:start position:0%
let's go to the artifacts tab of our
project

00:04:39.390 --> 00:04:39.400 align:start position:0%
project
 

00:04:39.400 --> 00:04:43.189 align:start position:0%
project
so<00:04:40.400><c> we</c><00:04:40.520><c> can</c><00:04:40.680><c> actually</c><00:04:41.000><c> Group</c><00:04:41.400><c> by</c><00:04:41.840><c> job</c><00:04:42.160><c> type</c><00:04:43.120><c> and</c>

00:04:43.189 --> 00:04:43.199 align:start position:0%
so we can actually Group by job type and
 

00:04:43.199 --> 00:04:44.469 align:start position:0%
so we can actually Group by job type and
we<00:04:43.320><c> can</c><00:04:43.400><c> see</c><00:04:43.600><c> that</c><00:04:43.720><c> there's</c><00:04:43.960><c> different</c><00:04:44.240><c> types</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
we can see that there's different types
 

00:04:44.479 --> 00:04:46.710 align:start position:0%
we can see that there's different types
of<00:04:44.680><c> jobs</c><00:04:45.199><c> within</c><00:04:45.520><c> this</c><00:04:45.759><c> so</c><00:04:46.280><c> you</c><00:04:46.440><c> know</c><00:04:46.560><c> we</c><00:04:46.639><c> have</c>

00:04:46.710 --> 00:04:46.720 align:start position:0%
of jobs within this so you know we have
 

00:04:46.720 --> 00:04:49.029 align:start position:0%
of jobs within this so you know we have
training<00:04:47.039><c> jobs</c><00:04:47.320><c> there's</c><00:04:47.479><c> 97</c><00:04:48.120><c> of</c><00:04:48.280><c> them</c><00:04:48.840><c> there's</c>

00:04:49.029 --> 00:04:49.039 align:start position:0%
training jobs there's 97 of them there's
 

00:04:49.039 --> 00:04:51.110 align:start position:0%
training jobs there's 97 of them there's
been<00:04:49.240><c> two</c><00:04:49.479><c> deployments</c><00:04:50.360><c> the</c><00:04:50.520><c> most</c><00:04:50.720><c> recent</c><00:04:50.960><c> one</c>

00:04:51.110 --> 00:04:51.120 align:start position:0%
been two deployments the most recent one
 

00:04:51.120 --> 00:04:53.790 align:start position:0%
been two deployments the most recent one
we've<00:04:51.280><c> just</c><00:04:51.400><c> done</c><00:04:51.639><c> eager</c><00:04:52.120><c> Lan</c><00:04:53.120><c> and</c><00:04:53.320><c> we</c><00:04:53.440><c> have</c><00:04:53.600><c> a</c>

00:04:53.790 --> 00:04:53.800 align:start position:0%
we've just done eager Lan and we have a
 

00:04:53.800 --> 00:04:55.189 align:start position:0%
we've just done eager Lan and we have a
container<00:04:54.160><c> build</c><00:04:54.440><c> step</c><00:04:54.639><c> so</c><00:04:54.759><c> we</c><00:04:54.840><c> have</c><00:04:54.960><c> all</c><00:04:55.080><c> of</c>

00:04:55.189 --> 00:04:55.199 align:start position:0%
container build step so we have all of
 

00:04:55.199 --> 00:04:57.830 align:start position:0%
container build step so we have all of
this<00:04:55.320><c> has</c><00:04:55.440><c> been</c><00:04:55.639><c> tracked</c><00:04:56.400><c> so</c><00:04:57.400><c> what</c><00:04:57.520><c> does</c><00:04:57.680><c> this</c>

00:04:57.830 --> 00:04:57.840 align:start position:0%
this has been tracked so what does this
 

00:04:57.840 --> 00:05:01.749 align:start position:0%
this has been tracked so what does this
mean<00:04:58.039><c> so</c><00:04:58.240><c> let's</c><00:04:58.639><c> have</c><00:04:58.759><c> a</c><00:04:58.960><c> look</c><00:04:59.320><c> in</c><00:04:59.600><c> in</c><00:05:00.759><c> um</c>

00:05:01.749 --> 00:05:01.759 align:start position:0%
mean so let's have a look in in um
 

00:05:01.759 --> 00:05:03.469 align:start position:0%
mean so let's have a look in in um
artifacts<00:05:02.199><c> St</c><00:05:02.440><c> so</c><00:05:02.600><c> if</c><00:05:02.680><c> we</c><00:05:02.759><c> go</c><00:05:02.880><c> to</c><00:05:03.000><c> the</c><00:05:03.160><c> docker</c>

00:05:03.469 --> 00:05:03.479 align:start position:0%
artifacts St so if we go to the docker
 

00:05:03.479 --> 00:05:04.990 align:start position:0%
artifacts St so if we go to the docker
file<00:05:03.840><c> so</c><00:05:04.000><c> this</c><00:05:04.120><c> is</c><00:05:04.240><c> the</c><00:05:04.360><c> container</c><00:05:04.759><c> that</c><00:05:04.880><c> we</c>

00:05:04.990 --> 00:05:05.000 align:start position:0%
file so this is the container that we
 

00:05:05.000 --> 00:05:07.950 align:start position:0%
file so this is the container that we
used<00:05:05.400><c> we</c><00:05:05.479><c> can</c><00:05:05.680><c> see</c><00:05:06.560><c> which</c><00:05:07.120><c> inference</c><00:05:07.639><c> images</c>

00:05:07.950 --> 00:05:07.960 align:start position:0%
used we can see which inference images
 

00:05:07.960 --> 00:05:09.430 align:start position:0%
used we can see which inference images
were<00:05:08.120><c> used</c><00:05:08.320><c> for</c><00:05:08.479><c> which</c><00:05:08.639><c> deployments</c><00:05:09.240><c> so</c><00:05:09.360><c> we</c>

00:05:09.430 --> 00:05:09.440 align:start position:0%
were used for which deployments so we
 

00:05:09.440 --> 00:05:11.270 align:start position:0%
were used for which deployments so we
can<00:05:09.600><c> see</c><00:05:09.919><c> that</c><00:05:10.400><c> you</c><00:05:10.520><c> know</c><00:05:10.680><c> this</c><00:05:10.800><c> Docker</c><00:05:11.120><c> file</c>

00:05:11.270 --> 00:05:11.280 align:start position:0%
can see that you know this Docker file
 

00:05:11.280 --> 00:05:13.390 align:start position:0%
can see that you know this Docker file
has<00:05:11.360><c> been</c><00:05:11.440><c> used</c><00:05:11.639><c> for</c><00:05:11.800><c> these</c><00:05:12.000><c> two</c><00:05:12.560><c> different</c>

00:05:13.390 --> 00:05:13.400 align:start position:0%
has been used for these two different
 

00:05:13.400 --> 00:05:15.070 align:start position:0%
has been used for these two different
deployments<00:05:13.960><c> and</c><00:05:14.080><c> eager</c><00:05:14.320><c> Lan</c><00:05:14.560><c> is</c><00:05:14.680><c> the</c><00:05:14.800><c> one</c><00:05:14.919><c> we'</c>

00:05:15.070 --> 00:05:15.080 align:start position:0%
deployments and eager Lan is the one we'
 

00:05:15.080 --> 00:05:16.950 align:start position:0%
deployments and eager Lan is the one we'
recently<00:05:15.440><c> done</c><00:05:15.680><c> so</c><00:05:15.800><c> let's</c><00:05:15.960><c> have</c><00:05:16.080><c> a</c><00:05:16.160><c> look</c><00:05:16.280><c> at</c>

00:05:16.950 --> 00:05:16.960 align:start position:0%
recently done so let's have a look at
 

00:05:16.960 --> 00:05:19.590 align:start position:0%
recently done so let's have a look at
it<00:05:17.960><c> so</c><00:05:18.360><c> maybe</c><00:05:18.639><c> we</c><00:05:18.840><c> know</c><00:05:19.120><c> we've</c><00:05:19.280><c> done</c><00:05:19.440><c> a</c>

00:05:19.590 --> 00:05:19.600 align:start position:0%
it so maybe we know we've done a
 

00:05:19.600 --> 00:05:21.790 align:start position:0%
it so maybe we know we've done a
deployment<00:05:20.199><c> we</c><00:05:20.319><c> might</c><00:05:20.520><c> have</c><00:05:20.680><c> some</c><00:05:21.039><c> questions</c>

00:05:21.790 --> 00:05:21.800 align:start position:0%
deployment we might have some questions
 

00:05:21.800 --> 00:05:26.870 align:start position:0%
deployment we might have some questions
about<00:05:22.160><c> it</c><00:05:23.160><c> so</c><00:05:23.400><c> what</c><00:05:23.600><c> we</c><00:05:23.720><c> can</c><00:05:24.039><c> do</c><00:05:24.960><c> is</c><00:05:25.960><c> um</c><00:05:26.560><c> have</c><00:05:26.720><c> a</c>

00:05:26.870 --> 00:05:26.880 align:start position:0%
about it so what we can do is um have a
 

00:05:26.880 --> 00:05:29.710 align:start position:0%
about it so what we can do is um have a
look<00:05:27.080><c> and</c><00:05:27.280><c> see</c><00:05:27.680><c> the</c><00:05:27.800><c> lineage</c><00:05:28.319><c> around</c><00:05:28.639><c> it</c><00:05:29.240><c> so</c><00:05:29.639><c> we</c>

00:05:29.710 --> 00:05:29.720 align:start position:0%
look and see the lineage around it so we
 

00:05:29.720 --> 00:05:31.309 align:start position:0%
look and see the lineage around it so we
can<00:05:29.840><c> see</c><00:05:30.160><c> that</c><00:05:30.479><c> you</c><00:05:30.560><c> know</c><00:05:30.720><c> this</c><00:05:30.880><c> deployment</c>

00:05:31.309 --> 00:05:31.319 align:start position:0%
can see that you know this deployment
 

00:05:31.319 --> 00:05:35.029 align:start position:0%
can see that you know this deployment
eager<00:05:31.759><c> lion</c><00:05:32.800><c> used</c><00:05:33.800><c> version</c><00:05:34.120><c> 56</c><00:05:34.520><c> of</c><00:05:34.680><c> this</c><00:05:34.800><c> model</c>

00:05:35.029 --> 00:05:35.039 align:start position:0%
eager lion used version 56 of this model
 

00:05:35.039 --> 00:05:35.990 align:start position:0%
eager lion used version 56 of this model
and<00:05:35.120><c> this</c><00:05:35.199><c> is</c><00:05:35.319><c> the</c><00:05:35.400><c> training</c><00:05:35.680><c> run</c><00:05:35.880><c> that</c>

00:05:35.990 --> 00:05:36.000 align:start position:0%
and this is the training run that
 

00:05:36.000 --> 00:05:37.270 align:start position:0%
and this is the training run that
produced<00:05:36.280><c> it</c><00:05:36.440><c> and</c><00:05:36.560><c> clicking</c><00:05:36.840><c> into</c><00:05:37.039><c> any</c><00:05:37.199><c> of</c>

00:05:37.270 --> 00:05:37.280 align:start position:0%
produced it and clicking into any of
 

00:05:37.280 --> 00:05:38.870 align:start position:0%
produced it and clicking into any of
these<00:05:37.400><c> will</c><00:05:37.560><c> give</c><00:05:37.720><c> us</c><00:05:37.840><c> more</c><00:05:38.080><c> information</c><00:05:38.759><c> but</c>

00:05:38.870 --> 00:05:38.880 align:start position:0%
these will give us more information but
 

00:05:38.880 --> 00:05:40.230 align:start position:0%
these will give us more information but
we<00:05:39.120><c> can</c><00:05:39.240><c> have</c><00:05:39.319><c> a</c><00:05:39.440><c> look</c><00:05:39.520><c> at</c><00:05:39.639><c> a</c><00:05:39.720><c> more</c><00:05:39.880><c> complete</c>

00:05:40.230 --> 00:05:40.240 align:start position:0%
we can have a look at a more complete
 

00:05:40.240 --> 00:05:41.909 align:start position:0%
we can have a look at a more complete
lineage<00:05:40.960><c> and</c><00:05:41.080><c> we</c><00:05:41.199><c> can</c><00:05:41.319><c> see</c><00:05:41.520><c> that</c><00:05:41.720><c> this</c>

00:05:41.909 --> 00:05:41.919 align:start position:0%
lineage and we can see that this
 

00:05:41.919 --> 00:05:44.990 align:start position:0%
lineage and we can see that this
deployment<00:05:42.840><c> for</c><00:05:43.039><c> eager</c><00:05:43.319><c> line</c><00:05:43.639><c> use</c><00:05:43.919><c> this</c><00:05:44.080><c> model</c>

00:05:44.990 --> 00:05:45.000 align:start position:0%
deployment for eager line use this model
 

00:05:45.000 --> 00:05:48.230 align:start position:0%
deployment for eager line use this model
use<00:05:45.319><c> this</c><00:05:45.840><c> Docker</c><00:05:46.319><c> file</c><00:05:46.960><c> this</c><00:05:47.080><c> is</c><00:05:47.199><c> our</c><00:05:47.400><c> image</c>

00:05:48.230 --> 00:05:48.240 align:start position:0%
use this Docker file this is our image
 

00:05:48.240 --> 00:05:51.469 align:start position:0%
use this Docker file this is our image
and<00:05:48.360><c> we</c><00:05:48.440><c> can</c><00:05:48.600><c> see</c><00:05:49.360><c> the</c><00:05:49.560><c> entire</c><00:05:50.479><c> life</c><00:05:50.759><c> cycle</c><00:05:51.240><c> and</c>

00:05:51.469 --> 00:05:51.479 align:start position:0%
and we can see the entire life cycle and
 

00:05:51.479 --> 00:05:54.469 align:start position:0%
and we can see the entire life cycle and
pipeline<00:05:52.160><c> of</c><00:05:52.319><c> our</c><00:05:52.600><c> deployment</c><00:05:53.600><c> thank</c><00:05:53.759><c> you</c><00:05:53.919><c> for</c>

00:05:54.469 --> 00:05:54.479 align:start position:0%
pipeline of our deployment thank you for
 

00:05:54.479 --> 00:05:56.029 align:start position:0%
pipeline of our deployment thank you for
watching<00:05:54.759><c> this</c><00:05:54.919><c> video</c><00:05:55.440><c> and</c><00:05:55.560><c> we</c><00:05:55.680><c> have</c><00:05:55.800><c> lots</c>

00:05:56.029 --> 00:05:56.039 align:start position:0%
watching this video and we have lots
 

00:05:56.039 --> 00:05:57.469 align:start position:0%
watching this video and we have lots
more<00:05:56.199><c> information</c><00:05:56.560><c> on</c><00:05:56.680><c> how</c><00:05:56.759><c> you</c><00:05:56.880><c> can</c><00:05:57.000><c> set</c><00:05:57.319><c> this</c>

00:05:57.469 --> 00:05:57.479 align:start position:0%
more information on how you can set this
 

00:05:57.479 --> 00:06:02.570 align:start position:0%
more information on how you can set this
up<00:05:58.080><c> yourself</c><00:05:59.080><c> thank</c><00:05:59.240><c> you</c>

00:06:02.570 --> 00:06:02.580 align:start position:0%
 
 

00:06:02.580 --> 00:06:13.820 align:start position:0%
 
[Music]

