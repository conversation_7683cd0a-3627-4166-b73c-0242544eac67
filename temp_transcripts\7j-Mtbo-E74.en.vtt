WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.410 align:start position:0%
 
foreign

00:00:02.410 --> 00:00:02.420 align:start position:0%
 
 

00:00:02.420 --> 00:00:07.210 align:start position:0%
 
[Music]

00:00:07.210 --> 00:00:07.220 align:start position:0%
 
 

00:00:07.220 --> 00:00:10.790 align:start position:0%
 
hook<00:00:08.220><c> automations</c><00:00:08.760><c> and</c><00:00:09.300><c> weights</c><00:00:09.720><c> and</c><00:00:09.780><c> license</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
hook automations and weights and license
 

00:00:10.800 --> 00:00:12.470 align:start position:0%
hook automations and weights and license
this<00:00:11.280><c> is</c><00:00:11.400><c> going</c><00:00:11.519><c> to</c><00:00:11.700><c> allow</c><00:00:11.940><c> teams</c><00:00:12.360><c> to</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
this is going to allow teams to
 

00:00:12.480 --> 00:00:14.810 align:start position:0%
this is going to allow teams to
integrate<00:00:12.900><c> CI</c><00:00:13.380><c> CD</c><00:00:13.679><c> tools</c><00:00:14.099><c> like</c><00:00:14.400><c> GitHub</c>

00:00:14.810 --> 00:00:14.820 align:start position:0%
integrate CI CD tools like GitHub
 

00:00:14.820 --> 00:00:17.210 align:start position:0%
integrate CI CD tools like GitHub
actions<00:00:15.360><c> with</c><00:00:15.719><c> the</c><00:00:15.960><c> model</c><00:00:16.080><c> registry</c><00:00:16.680><c> so</c><00:00:17.100><c> let's</c>

00:00:17.210 --> 00:00:17.220 align:start position:0%
actions with the model registry so let's
 

00:00:17.220 --> 00:00:19.430 align:start position:0%
actions with the model registry so let's
first<00:00:17.460><c> set</c><00:00:17.640><c> up</c><00:00:17.760><c> a</c><00:00:17.880><c> web</c><00:00:18.000><c> hook</c><00:00:18.720><c> first</c><00:00:19.020><c> thing</c><00:00:19.260><c> we</c>

00:00:19.430 --> 00:00:19.440 align:start position:0%
first set up a web hook first thing we
 

00:00:19.440 --> 00:00:20.450 align:start position:0%
first set up a web hook first thing we
need<00:00:19.560><c> to</c><00:00:19.680><c> do</c>

00:00:20.450 --> 00:00:20.460 align:start position:0%
need to do
 

00:00:20.460 --> 00:00:23.510 align:start position:0%
need to do
is<00:00:20.820><c> set</c><00:00:21.060><c> up</c><00:00:21.180><c> a</c><00:00:21.359><c> secret</c><00:00:21.619><c> so</c><00:00:22.619><c> that</c><00:00:22.859><c> weights</c><00:00:23.400><c> and</c>

00:00:23.510 --> 00:00:23.520 align:start position:0%
is set up a secret so that weights and
 

00:00:23.520 --> 00:00:25.730 align:start position:0%
is set up a secret so that weights and
biases<00:00:23.880><c> can</c><00:00:24.060><c> authenticate</c><00:00:24.600><c> to</c><00:00:24.779><c> our</c><00:00:24.960><c> external</c>

00:00:25.730 --> 00:00:25.740 align:start position:0%
biases can authenticate to our external
 

00:00:25.740 --> 00:00:27.589 align:start position:0%
biases can authenticate to our external
tool

00:00:27.589 --> 00:00:27.599 align:start position:0%
tool
 

00:00:27.599 --> 00:00:29.330 align:start position:0%
tool
wait<00:00:27.840><c> to</c><00:00:28.080><c> mice</c><00:00:28.260><c> is</c><00:00:28.500><c> a</c><00:00:28.619><c> Secret</c><00:00:28.740><c> store</c><00:00:28.980><c> where</c><00:00:29.220><c> you</c>

00:00:29.330 --> 00:00:29.340 align:start position:0%
wait to mice is a Secret store where you
 

00:00:29.340 --> 00:00:32.150 align:start position:0%
wait to mice is a Secret store where you
can<00:00:29.460><c> add</c><00:00:29.640><c> your</c><00:00:29.820><c> secret</c><00:00:30.080><c> and</c><00:00:31.080><c> access</c><00:00:31.260><c> it</c><00:00:31.619><c> later</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
can add your secret and access it later
 

00:00:32.160 --> 00:00:35.030 align:start position:0%
can add your secret and access it later
on<00:00:32.399><c> when</c><00:00:32.759><c> you</c><00:00:32.940><c> configure</c><00:00:33.300><c> the</c><00:00:33.480><c> automation</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
on when you configure the automation
 

00:00:35.040 --> 00:00:37.729 align:start position:0%
on when you configure the automation
relaxing<00:00:35.940><c> to</c><00:00:36.180><c> create</c><00:00:36.360><c> the</c><00:00:36.600><c> web</c><00:00:36.719><c> hook</c><00:00:36.960><c> and</c><00:00:37.620><c> this</c>

00:00:37.729 --> 00:00:37.739 align:start position:0%
relaxing to create the web hook and this
 

00:00:37.739 --> 00:00:39.170 align:start position:0%
relaxing to create the web hook and this
just<00:00:37.920><c> consists</c><00:00:38.280><c> of</c><00:00:38.340><c> the</c><00:00:38.700><c> name</c><00:00:38.760><c> of</c><00:00:38.940><c> the</c><00:00:39.059><c> web</c>

00:00:39.170 --> 00:00:39.180 align:start position:0%
just consists of the name of the web
 

00:00:39.180 --> 00:00:41.350 align:start position:0%
just consists of the name of the web
hook

00:00:41.350 --> 00:00:41.360 align:start position:0%
hook
 

00:00:41.360 --> 00:00:44.569 align:start position:0%
hook
along<00:00:42.360><c> with</c><00:00:42.600><c> the</c><00:00:42.840><c> URL</c><00:00:43.200><c> that</c><00:00:44.100><c> we</c><00:00:44.219><c> want</c><00:00:44.340><c> to</c><00:00:44.460><c> post</c>

00:00:44.569 --> 00:00:44.579 align:start position:0%
along with the URL that we want to post
 

00:00:44.579 --> 00:00:46.670 align:start position:0%
along with the URL that we want to post
to<00:00:44.820><c> and</c><00:00:45.660><c> then</c><00:00:45.780><c> the</c><00:00:45.960><c> secret</c><00:00:46.140><c> that</c><00:00:46.440><c> we're</c><00:00:46.559><c> going</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
to and then the secret that we're going
 

00:00:46.680 --> 00:00:51.229 align:start position:0%
to and then the secret that we're going
to authenticate<00:00:47.160><c> with</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
 
 

00:00:51.239 --> 00:00:53.330 align:start position:0%
 
once<00:00:51.780><c> that's</c><00:00:51.840><c> created</c><00:00:52.260><c> we</c><00:00:52.739><c> need</c><00:00:52.920><c> to</c><00:00:52.980><c> go</c><00:00:53.100><c> to</c><00:00:53.219><c> the</c>

00:00:53.330 --> 00:00:53.340 align:start position:0%
once that's created we need to go to the
 

00:00:53.340 --> 00:00:54.889 align:start position:0%
once that's created we need to go to the
model<00:00:53.460><c> registry</c><00:00:53.940><c> to</c><00:00:54.360><c> configure</c><00:00:54.660><c> the</c>

00:00:54.889 --> 00:00:54.899 align:start position:0%
model registry to configure the
 

00:00:54.899 --> 00:00:57.470 align:start position:0%
model registry to configure the
automation<00:00:55.399><c> CI</c><00:00:56.399><c> CD</c><00:00:56.820><c> machine</c><00:00:57.059><c> learning</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
automation CI CD machine learning
 

00:00:57.480 --> 00:00:59.510 align:start position:0%
automation CI CD machine learning
consists<00:00:57.840><c> of</c><00:00:57.960><c> handing</c><00:00:58.320><c> off</c><00:00:58.379><c> model</c><00:00:58.800><c> artifacts</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
consists of handing off model artifacts
 

00:00:59.520 --> 00:01:04.850 align:start position:0%
consists of handing off model artifacts
to<00:01:00.239><c> be</c><00:01:00.480><c> containerized</c><00:01:01.440><c> tested</c><00:01:02.100><c> and</c><00:01:02.460><c> deployed</c>

00:01:04.850 --> 00:01:04.860 align:start position:0%
to be containerized tested and deployed
 

00:01:04.860 --> 00:01:06.950 align:start position:0%
to be containerized tested and deployed
here<00:01:05.339><c> I</c><00:01:05.580><c> have</c><00:01:05.700><c> a</c><00:01:05.820><c> registered</c><00:01:06.240><c> model</c><00:01:06.420><c> with</c>

00:01:06.950 --> 00:01:06.960 align:start position:0%
here I have a registered model with
 

00:01:06.960 --> 00:01:09.170 align:start position:0%
here I have a registered model with
several<00:01:07.380><c> past</c><00:01:07.560><c> versions</c><00:01:08.100><c> as</c><00:01:08.700><c> the</c><00:01:08.880><c> model</c><00:01:09.000><c> has</c>

00:01:09.170 --> 00:01:09.180 align:start position:0%
several past versions as the model has
 

00:01:09.180 --> 00:01:11.690 align:start position:0%
several past versions as the model has
been<00:01:09.360><c> retrained</c><00:01:09.780><c> over</c><00:01:09.960><c> time</c>

00:01:11.690 --> 00:01:11.700 align:start position:0%
been retrained over time
 

00:01:11.700 --> 00:01:14.090 align:start position:0%
been retrained over time
clicking<00:01:12.420><c> into</c><00:01:12.600><c> details</c><00:01:13.080><c> I</c><00:01:13.260><c> can</c><00:01:13.439><c> see</c><00:01:13.680><c> the</c>

00:01:14.090 --> 00:01:14.100 align:start position:0%
clicking into details I can see the
 

00:01:14.100 --> 00:01:16.609 align:start position:0%
clicking into details I can see the
model<00:01:14.280><c> card</c><00:01:14.659><c> describing</c><00:01:15.659><c> how</c><00:01:15.900><c> to</c><00:01:15.960><c> use</c><00:01:16.080><c> it</c><00:01:16.260><c> how</c>

00:01:16.609 --> 00:01:16.619 align:start position:0%
model card describing how to use it how
 

00:01:16.619 --> 00:01:18.230 align:start position:0%
model card describing how to use it how
it<00:01:16.740><c> was</c><00:01:16.860><c> made</c>

00:01:18.230 --> 00:01:18.240 align:start position:0%
it was made
 

00:01:18.240 --> 00:01:22.249 align:start position:0%
it was made
I'm<00:01:18.540><c> going</c><00:01:18.659><c> to</c><00:01:18.720><c> click</c><00:01:18.900><c> on</c><00:01:19.020><c> automations</c><00:01:19.380><c> here</c>

00:01:22.249 --> 00:01:22.259 align:start position:0%
 
 

00:01:22.259 --> 00:01:24.249 align:start position:0%
 
I'll<00:01:22.619><c> be</c><00:01:22.740><c> able</c><00:01:22.860><c> to</c><00:01:22.979><c> create</c><00:01:23.220><c> a</c><00:01:23.580><c> new</c><00:01:23.640><c> automation</c>

00:01:24.249 --> 00:01:24.259 align:start position:0%
I'll be able to create a new automation
 

00:01:24.259 --> 00:01:28.130 align:start position:0%
I'll be able to create a new automation
which<00:01:25.259><c> occurs</c><00:01:25.860><c> when</c><00:01:26.520><c> certain</c><00:01:27.000><c> events</c><00:01:27.900><c> happen</c>

00:01:28.130 --> 00:01:28.140 align:start position:0%
which occurs when certain events happen
 

00:01:28.140 --> 00:01:33.830 align:start position:0%
which occurs when certain events happen
to<00:01:28.439><c> this</c><00:01:28.619><c> registered</c><00:01:29.040><c> model</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
 
 

00:01:33.840 --> 00:01:36.050 align:start position:0%
 
creating<00:01:34.259><c> an</c><00:01:34.380><c> automation</c><00:01:34.740><c> I</c><00:01:35.400><c> have</c><00:01:35.640><c> two</c>

00:01:36.050 --> 00:01:36.060 align:start position:0%
creating an automation I have two
 

00:01:36.060 --> 00:01:38.749 align:start position:0%
creating an automation I have two
options<00:01:36.420><c> here</c><00:01:36.659><c> one</c><00:01:37.500><c> when</c><00:01:37.860><c> a</c><00:01:38.040><c> new</c><00:01:38.159><c> version</c><00:01:38.400><c> is</c>

00:01:38.749 --> 00:01:38.759 align:start position:0%
options here one when a new version is
 

00:01:38.759 --> 00:01:40.850 align:start position:0%
options here one when a new version is
added<00:01:39.060><c> to</c><00:01:39.180><c> this</c><00:01:39.299><c> registered</c><00:01:39.720><c> model</c><00:01:39.960><c> so</c><00:01:40.500><c> when</c><00:01:40.680><c> a</c>

00:01:40.850 --> 00:01:40.860 align:start position:0%
added to this registered model so when a
 

00:01:40.860 --> 00:01:42.649 align:start position:0%
added to this registered model so when a
new<00:01:40.979><c> candidate</c><00:01:41.340><c> model</c><00:01:41.520><c> is</c><00:01:41.759><c> added</c>

00:01:42.649 --> 00:01:42.659 align:start position:0%
new candidate model is added
 

00:01:42.659 --> 00:01:45.469 align:start position:0%
new candidate model is added
or<00:01:43.259><c> when</c><00:01:43.500><c> an</c><00:01:43.680><c> artifact</c><00:01:44.040><c> Alias</c><00:01:44.579><c> is</c><00:01:44.759><c> added</c><00:01:45.060><c> such</c>

00:01:45.469 --> 00:01:45.479 align:start position:0%
or when an artifact Alias is added such
 

00:01:45.479 --> 00:01:47.569 align:start position:0%
or when an artifact Alias is added such
as<00:01:45.600><c> when</c><00:01:45.900><c> someone</c><00:01:46.140><c> tagged</c><00:01:46.860><c> this</c><00:01:47.100><c> model</c><00:01:47.220><c> with</c>

00:01:47.569 --> 00:01:47.579 align:start position:0%
as when someone tagged this model with
 

00:01:47.579 --> 00:01:49.670 align:start position:0%
as when someone tagged this model with
the<00:01:47.700><c> production</c><00:01:47.939><c> alias</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
the production alias
 

00:01:49.680 --> 00:01:52.789 align:start position:0%
the production alias
I'm<00:01:50.040><c> going</c><00:01:50.159><c> to</c><00:01:50.220><c> click</c><00:01:50.399><c> the</c><00:01:50.579><c> first</c><00:01:50.700><c> one</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
I'm going to click the first one
 

00:01:52.799 --> 00:01:54.770 align:start position:0%
I'm going to click the first one
the<00:01:53.159><c> action</c><00:01:53.280><c> type</c><00:01:53.579><c> here</c><00:01:53.820><c> I</c><00:01:54.060><c> can</c><00:01:54.180><c> choose</c><00:01:54.479><c> either</c>

00:01:54.770 --> 00:01:54.780 align:start position:0%
the action type here I can choose either
 

00:01:54.780 --> 00:02:00.710 align:start position:0%
the action type here I can choose either
a<00:01:55.020><c> launch</c><00:01:55.380><c> job</c><00:01:55.560><c> or</c><00:01:56.399><c> a</c><00:01:56.579><c> output</c>

00:02:00.710 --> 00:02:00.720 align:start position:0%
 
 

00:02:00.720 --> 00:02:02.270 align:start position:0%
 
with<00:02:01.020><c> the</c><00:02:01.140><c> web</c><00:02:01.259><c> hook</c><00:02:01.439><c> I'll</c><00:02:01.740><c> select</c><00:02:02.040><c> the</c><00:02:02.159><c> one</c>

00:02:02.270 --> 00:02:02.280 align:start position:0%
with the web hook I'll select the one
 

00:02:02.280 --> 00:02:05.690 align:start position:0%
with the web hook I'll select the one
that<00:02:02.460><c> I</c><00:02:02.579><c> just</c><00:02:02.700><c> created</c>

00:02:05.690 --> 00:02:05.700 align:start position:0%
 
 

00:02:05.700 --> 00:02:09.949 align:start position:0%
 
and<00:02:06.060><c> then</c><00:02:06.180><c> I</c><00:02:06.420><c> need</c><00:02:06.540><c> to</c><00:02:06.840><c> add</c><00:02:07.079><c> in</c><00:02:07.200><c> a</c><00:02:07.439><c> payload</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
and then I need to add in a payload
 

00:02:09.959 --> 00:02:12.589 align:start position:0%
and then I need to add in a payload
the<00:02:10.140><c> payload</c><00:02:10.560><c> describes</c><00:02:11.099><c> what</c><00:02:11.940><c> context</c><00:02:12.360><c> I'm</c>

00:02:12.589 --> 00:02:12.599 align:start position:0%
the payload describes what context I'm
 

00:02:12.599 --> 00:02:14.390 align:start position:0%
the payload describes what context I'm
going<00:02:12.780><c> to</c><00:02:12.959><c> pass</c><00:02:13.140><c> from</c><00:02:13.440><c> weights</c><00:02:13.800><c> and</c><00:02:13.920><c> biases</c>

00:02:14.390 --> 00:02:14.400 align:start position:0%
going to pass from weights and biases
 

00:02:14.400 --> 00:02:16.970 align:start position:0%
going to pass from weights and biases
into<00:02:15.120><c> my</c><00:02:15.360><c> external</c><00:02:15.840><c> tool</c>

00:02:16.970 --> 00:02:16.980 align:start position:0%
into my external tool
 

00:02:16.980 --> 00:02:19.130 align:start position:0%
into my external tool
in<00:02:17.520><c> this</c><00:02:17.580><c> case</c><00:02:17.760><c> I</c><00:02:18.120><c> want</c><00:02:18.239><c> to</c><00:02:18.360><c> trigger</c><00:02:18.660><c> a</c><00:02:18.840><c> GitHub</c>

00:02:19.130 --> 00:02:19.140 align:start position:0%
in this case I want to trigger a GitHub
 

00:02:19.140 --> 00:02:21.290 align:start position:0%
in this case I want to trigger a GitHub
action<00:02:19.440><c> to</c><00:02:19.860><c> test</c><00:02:20.040><c> the</c><00:02:20.220><c> model</c><00:02:20.400><c> and</c><00:02:20.819><c> generate</c><00:02:21.120><c> a</c>

00:02:21.290 --> 00:02:21.300 align:start position:0%
action to test the model and generate a
 

00:02:21.300 --> 00:02:24.229 align:start position:0%
action to test the model and generate a
way<00:02:21.420><c> to</c><00:02:21.599><c> minus</c><00:02:21.959><c> report</c><00:02:22.319><c> for</c><00:02:22.980><c> inspection</c>

00:02:24.229 --> 00:02:24.239 align:start position:0%
way to minus report for inspection
 

00:02:24.239 --> 00:02:27.470 align:start position:0%
way to minus report for inspection
so<00:02:24.720><c> I</c><00:02:24.900><c> want</c><00:02:25.080><c> to</c><00:02:25.319><c> pass</c><00:02:25.980><c> in</c><00:02:26.220><c> information</c><00:02:26.819><c> about</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
so I want to pass in information about
 

00:02:27.480 --> 00:02:29.449 align:start position:0%
so I want to pass in information about
the<00:02:27.959><c> specific</c><00:02:28.560><c> model</c><00:02:28.860><c> version</c><00:02:29.160><c> that</c>

00:02:29.449 --> 00:02:29.459 align:start position:0%
the specific model version that
 

00:02:29.459 --> 00:02:31.430 align:start position:0%
the specific model version that
triggered<00:02:29.819><c> this</c><00:02:30.000><c> automation</c><00:02:30.420><c> through</c><00:02:31.200><c> these</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
triggered this automation through these
 

00:02:31.440 --> 00:02:33.290 align:start position:0%
triggered this automation through these
template<00:02:31.739><c> strings</c><00:02:32.099><c> we</c><00:02:32.400><c> can</c><00:02:32.520><c> add</c><00:02:32.819><c> all</c><00:02:33.060><c> that</c>

00:02:33.290 --> 00:02:33.300 align:start position:0%
template strings we can add all that
 

00:02:33.300 --> 00:02:35.210 align:start position:0%
template strings we can add all that
context

00:02:35.210 --> 00:02:35.220 align:start position:0%
context
 

00:02:35.220 --> 00:02:36.410 align:start position:0%
context
here<00:02:35.580><c> I'm</c><00:02:35.760><c> going</c><00:02:35.879><c> to</c><00:02:35.879><c> indicate</c><00:02:36.180><c> the</c><00:02:36.300><c> event</c>

00:02:36.410 --> 00:02:36.420 align:start position:0%
here I'm going to indicate the event
 

00:02:36.420 --> 00:02:39.170 align:start position:0%
here I'm going to indicate the event
type<00:02:36.720><c> along</c><00:02:37.319><c> with</c><00:02:37.739><c> the</c><00:02:38.340><c> Challenger</c><00:02:38.760><c> model</c><00:02:38.940><c> so</c>

00:02:39.170 --> 00:02:39.180 align:start position:0%
type along with the Challenger model so
 

00:02:39.180 --> 00:02:41.449 align:start position:0%
type along with the Challenger model so
the<00:02:39.360><c> new</c><00:02:39.540><c> model</c><00:02:39.660><c> that's</c><00:02:39.900><c> being</c><00:02:40.140><c> added</c><00:02:40.500><c> the</c>

00:02:41.449 --> 00:02:41.459 align:start position:0%
the new model that's being added the
 

00:02:41.459 --> 00:02:43.430 align:start position:0%
the new model that's being added the
incumbent<00:02:42.000><c> or</c><00:02:42.239><c> champion</c><00:02:42.480><c> model</c><00:02:42.900><c> that</c><00:02:43.200><c> I</c><00:02:43.379><c> want</c>

00:02:43.430 --> 00:02:43.440 align:start position:0%
incumbent or champion model that I want
 

00:02:43.440 --> 00:02:45.410 align:start position:0%
incumbent or champion model that I want
to<00:02:43.560><c> compare</c><00:02:43.800><c> it</c><00:02:43.920><c> to</c><00:02:44.099><c> along</c><00:02:44.879><c> with</c><00:02:45.120><c> the</c><00:02:45.300><c> test</c>

00:02:45.410 --> 00:02:45.420 align:start position:0%
to compare it to along with the test
 

00:02:45.420 --> 00:02:47.990 align:start position:0%
to compare it to along with the test
data<00:02:45.780><c> set</c><00:02:45.900><c> to</c><00:02:46.140><c> run</c><00:02:46.260><c> it</c><00:02:46.440><c> through</c><00:02:46.620><c> the</c><00:02:47.580><c> author</c>

00:02:47.990 --> 00:02:48.000 align:start position:0%
data set to run it through the author
 

00:02:48.000 --> 00:02:49.850 align:start position:0%
data set to run it through the author
and<00:02:48.480><c> then</c><00:02:48.540><c> some</c><00:02:48.840><c> other</c><00:02:48.959><c> way</c><00:02:49.140><c> to</c><00:02:49.319><c> biases</c>

00:02:49.850 --> 00:02:49.860 align:start position:0%
and then some other way to biases
 

00:02:49.860 --> 00:02:52.009 align:start position:0%
and then some other way to biases
information

00:02:52.009 --> 00:02:52.019 align:start position:0%
information
 

00:02:52.019 --> 00:02:53.509 align:start position:0%
information
and<00:02:52.500><c> with</c><00:02:52.680><c> that</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
and with that
 

00:02:53.519 --> 00:02:57.530 align:start position:0%
and with that
I<00:02:54.000><c> can</c><00:02:54.120><c> name</c><00:02:54.239><c> this</c><00:02:54.480><c> automation</c><00:02:54.959><c> test</c><00:02:55.319><c> model</c>

00:02:57.530 --> 00:02:57.540 align:start position:0%
I can name this automation test model
 

00:02:57.540 --> 00:03:00.650 align:start position:0%
I can name this automation test model
and<00:02:57.959><c> create</c>

00:03:00.650 --> 00:03:00.660 align:start position:0%
 
 

00:03:00.660 --> 00:03:02.930 align:start position:0%
 
we<00:03:01.080><c> now</c><00:03:01.200><c> have</c><00:03:01.379><c> an</c><00:03:01.680><c> automation</c><00:03:02.040><c> that</c><00:03:02.400><c> when</c><00:03:02.760><c> a</c>

00:03:02.930 --> 00:03:02.940 align:start position:0%
we now have an automation that when a
 

00:03:02.940 --> 00:03:05.449 align:start position:0%
we now have an automation that when a
new<00:03:03.060><c> model</c><00:03:03.239><c> is</c><00:03:03.420><c> added</c><00:03:03.780><c> to</c><00:03:03.900><c> this</c><00:03:04.080><c> registry</c><00:03:04.739><c> a</c>

00:03:05.449 --> 00:03:05.459 align:start position:0%
new model is added to this registry a
 

00:03:05.459 --> 00:03:07.490 align:start position:0%
new model is added to this registry a
post<00:03:05.640><c> request</c><00:03:06.120><c> will</c><00:03:06.540><c> trigger</c><00:03:06.959><c> a</c><00:03:07.200><c> GitHub</c>

00:03:07.490 --> 00:03:07.500 align:start position:0%
post request will trigger a GitHub
 

00:03:07.500 --> 00:03:10.430 align:start position:0%
post request will trigger a GitHub
action<00:03:07.860><c> to</c><00:03:08.459><c> run</c>

00:03:10.430 --> 00:03:10.440 align:start position:0%
action to run
 

00:03:10.440 --> 00:03:13.490 align:start position:0%
action to run
test<00:03:10.800><c> the</c><00:03:10.980><c> model</c><00:03:11.159><c> and</c><00:03:11.519><c> evaluate</c><00:03:12.000><c> it</c>

00:03:13.490 --> 00:03:13.500 align:start position:0%
test the model and evaluate it
 

00:03:13.500 --> 00:03:15.290 align:start position:0%
test the model and evaluate it
so<00:03:13.800><c> let's</c><00:03:13.920><c> go</c><00:03:14.099><c> ahead</c><00:03:14.220><c> and</c><00:03:14.280><c> do</c><00:03:14.459><c> that</c>

00:03:15.290 --> 00:03:15.300 align:start position:0%
so let's go ahead and do that
 

00:03:15.300 --> 00:03:18.649 align:start position:0%
so let's go ahead and do that
if<00:03:15.540><c> I</c><00:03:15.659><c> go</c><00:03:15.900><c> back</c><00:03:16.080><c> to</c><00:03:16.440><c> a</c><00:03:16.620><c> team</c><00:03:16.800><c> project</c>

00:03:18.649 --> 00:03:18.659 align:start position:0%
if I go back to a team project
 

00:03:18.659 --> 00:03:24.949 align:start position:0%
if I go back to a team project
which<00:03:18.900><c> has</c><00:03:19.080><c> a</c><00:03:19.260><c> bunch</c><00:03:19.379><c> of</c><00:03:19.500><c> candidate</c><00:03:19.860><c> models</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
 
 

00:03:24.959 --> 00:03:27.330 align:start position:0%
 
I<00:03:25.319><c> should</c><00:03:25.500><c> then</c><00:03:25.680><c> be</c><00:03:25.860><c> able</c><00:03:26.040><c> to</c><00:03:26.159><c> link</c><00:03:26.400><c> a</c><00:03:26.700><c> new</c><00:03:26.819><c> one</c>

00:03:27.330 --> 00:03:27.340 align:start position:0%
I should then be able to link a new one
 

00:03:27.340 --> 00:03:30.470 align:start position:0%
I should then be able to link a new one
[Music]

00:03:30.470 --> 00:03:30.480 align:start position:0%
 
 

00:03:30.480 --> 00:03:35.690 align:start position:0%
 
foreign

00:03:35.690 --> 00:03:35.700 align:start position:0%
 
 

00:03:35.700 --> 00:03:37.149 align:start position:0%
 
foreign

00:03:37.149 --> 00:03:37.159 align:start position:0%
foreign
 

00:03:37.159 --> 00:03:40.490 align:start position:0%
foreign
model<00:03:38.159><c> we</c><00:03:38.400><c> can</c><00:03:38.519><c> now</c><00:03:38.700><c> see</c><00:03:39.060><c> a</c><00:03:39.599><c> GitHub</c><00:03:40.019><c> action</c><00:03:40.260><c> not</c>

00:03:40.490 --> 00:03:40.500 align:start position:0%
model we can now see a GitHub action not
 

00:03:40.500 --> 00:03:43.070 align:start position:0%
model we can now see a GitHub action not
taking<00:03:40.739><c> place</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
 
 

00:03:43.080 --> 00:03:45.890 align:start position:0%
 
looking<00:03:43.560><c> into</c><00:03:43.860><c> it</c><00:03:44.099><c> we</c><00:03:44.340><c> can</c><00:03:44.519><c> see</c><00:03:44.760><c> that</c><00:03:45.360><c> we're</c>

00:03:45.890 --> 00:03:45.900 align:start position:0%
looking into it we can see that we're
 

00:03:45.900 --> 00:03:47.570 align:start position:0%
looking into it we can see that we're
installing<00:03:46.319><c> some</c><00:03:46.440><c> dependencies</c><00:03:46.920><c> logging</c>

00:03:47.570 --> 00:03:47.580 align:start position:0%
installing some dependencies logging
 

00:03:47.580 --> 00:03:49.550 align:start position:0%
installing some dependencies logging
into<00:03:47.700><c> weights</c><00:03:48.120><c> and</c><00:03:48.180><c> biases</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
into weights and biases
 

00:03:49.560 --> 00:03:51.229 align:start position:0%
into weights and biases
retrieving<00:03:50.220><c> the</c><00:03:50.340><c> artificial</c><00:03:50.819><c> weights</c><00:03:51.180><c> and</c>

00:03:51.229 --> 00:03:51.239 align:start position:0%
retrieving the artificial weights and
 

00:03:51.239 --> 00:03:53.449 align:start position:0%
retrieving the artificial weights and
biases<00:03:51.659><c> running</c><00:03:52.140><c> tests</c><00:03:52.620><c> and</c><00:03:52.739><c> generating</c><00:03:53.159><c> a</c>

00:03:53.449 --> 00:03:53.459 align:start position:0%
biases running tests and generating a
 

00:03:53.459 --> 00:03:55.009 align:start position:0%
biases running tests and generating a
comparison<00:03:53.879><c> report</c>

00:03:55.009 --> 00:03:55.019 align:start position:0%
comparison report
 

00:03:55.019 --> 00:03:56.449 align:start position:0%
comparison report
and<00:03:55.319><c> that's</c><00:03:55.440><c> all</c><00:03:55.620><c> you</c><00:03:55.739><c> need</c><00:03:55.860><c> to</c><00:03:55.980><c> do</c><00:03:56.099><c> to</c><00:03:56.280><c> get</c>

00:03:56.449 --> 00:03:56.459 align:start position:0%
and that's all you need to do to get
 

00:03:56.459 --> 00:03:58.190 align:start position:0%
and that's all you need to do to get
started<00:03:56.760><c> with</c><00:03:56.940><c> weights</c><00:03:57.360><c> and</c><00:03:57.420><c> biases</c><00:03:57.780><c> webhook</c>

00:03:58.190 --> 00:03:58.200 align:start position:0%
started with weights and biases webhook
 

00:03:58.200 --> 00:04:02.270 align:start position:0%
started with weights and biases webhook
automations

00:04:02.270 --> 00:04:02.280 align:start position:0%
 
 

00:04:02.280 --> 00:04:04.459 align:start position:0%
 
foreign

