WEBVTT
Kind: captions
Language: en

00:00:00.140 --> 00:00:10.629 align:start position:0%
 
[Music]

00:00:10.629 --> 00:00:10.639 align:start position:0%
[Music]
 

00:00:10.639 --> 00:00:14.390 align:start position:0%
[Music]
I<00:00:11.639><c> guess</c><00:00:11.820><c> the</c><00:00:12.059><c> question</c><00:00:12.240><c> is</c><00:00:12.540><c> like</c><00:00:13.019><c> uh</c>

00:00:14.390 --> 00:00:14.400 align:start position:0%
I guess the question is like uh
 

00:00:14.400 --> 00:00:17.930 align:start position:0%
I guess the question is like uh
like<00:00:14.820><c> is</c><00:00:14.940><c> there</c><00:00:15.120><c> a</c><00:00:15.299><c> job</c><00:00:15.420><c> to</c><00:00:15.660><c> use</c><00:00:16.139><c> the</c><00:00:16.379><c> AI</c><00:00:16.940><c> well</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
like is there a job to use the AI well
 

00:00:17.940 --> 00:00:20.150 align:start position:0%
like is there a job to use the AI well
and<00:00:18.240><c> it</c><00:00:18.420><c> certainly</c><00:00:18.720><c> does</c><00:00:19.020><c> seem</c><00:00:19.619><c> like</c><00:00:19.800><c> that</c><00:00:19.980><c> I</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
and it certainly does seem like that I
 

00:00:20.160 --> 00:00:21.349 align:start position:0%
and it certainly does seem like that I
think<00:00:20.279><c> someone</c><00:00:20.460><c> is</c><00:00:20.760><c> talking</c><00:00:20.880><c> about</c><00:00:21.000><c> you</c><00:00:21.240><c> know</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
think someone is talking about you know
 

00:00:21.359 --> 00:00:23.570 align:start position:0%
think someone is talking about you know
prompt<00:00:21.600><c> Engineers</c><00:00:21.960><c> does</c><00:00:22.260><c> sort</c><00:00:22.500><c> of</c><00:00:23.039><c> seem</c><00:00:23.400><c> like</c>

00:00:23.570 --> 00:00:23.580 align:start position:0%
prompt Engineers does sort of seem like
 

00:00:23.580 --> 00:00:25.910 align:start position:0%
prompt Engineers does sort of seem like
that<00:00:23.699><c> it's</c><00:00:24.000><c> interesting</c><00:00:24.420><c> right</c><00:00:24.660><c> like</c><00:00:25.199><c> um</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
that it's interesting right like um
 

00:00:25.920 --> 00:00:28.870 align:start position:0%
that it's interesting right like um
you<00:00:26.400><c> know</c><00:00:26.519><c> how</c><00:00:26.880><c> like</c><00:00:27.539><c> asking</c><00:00:28.019><c> a</c><00:00:28.199><c> question</c><00:00:28.439><c> well</c>

00:00:28.870 --> 00:00:28.880 align:start position:0%
you know how like asking a question well
 

00:00:28.880 --> 00:00:32.930 align:start position:0%
you know how like asking a question well
does<00:00:29.880><c> seem</c><00:00:30.240><c> like</c><00:00:30.539><c> a</c><00:00:30.720><c> critical</c><00:00:31.019><c> task</c><00:00:31.320><c> to</c><00:00:31.619><c> using</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
does seem like a critical task to using
 

00:00:32.940 --> 00:00:34.549 align:start position:0%
does seem like a critical task to using
the<00:00:33.480><c> GPT</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
the GPT
 

00:00:34.559 --> 00:00:36.709 align:start position:0%
the GPT
models<00:00:35.399><c> well</c><00:00:35.579><c> and</c><00:00:35.880><c> it's</c><00:00:36.059><c> a</c><00:00:36.180><c> it's</c><00:00:36.420><c> a</c><00:00:36.600><c> really</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
models well and it's a it's a really
 

00:00:36.719 --> 00:00:38.569 align:start position:0%
models well and it's a it's a really
interesting

00:00:38.569 --> 00:00:38.579 align:start position:0%
interesting
 

00:00:38.579 --> 00:00:41.810 align:start position:0%
interesting
scale<00:00:39.120><c> I</c><00:00:39.300><c> mean</c><00:00:39.420><c> you</c><00:00:39.780><c> know</c><00:00:39.840><c> I</c><00:00:40.620><c> I</c><00:00:40.860><c> it</c><00:00:41.399><c> totally</c>

00:00:41.810 --> 00:00:41.820 align:start position:0%
scale I mean you know I I it totally
 

00:00:41.820 --> 00:00:43.670 align:start position:0%
scale I mean you know I I it totally
seems<00:00:42.059><c> like</c><00:00:42.180><c> the</c><00:00:42.300><c> trends</c><00:00:42.660><c> are</c><00:00:42.780><c> in</c><00:00:42.960><c> are</c><00:00:43.320><c> in</c><00:00:43.559><c> that</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
seems like the trends are in are in that
 

00:00:43.680 --> 00:00:45.110 align:start position:0%
seems like the trends are in are in that
direction<00:00:43.860><c> and</c><00:00:44.219><c> I've</c><00:00:44.399><c> been</c><00:00:44.579><c> talking</c><00:00:44.700><c> to</c><00:00:44.879><c> a</c><00:00:45.059><c> lot</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
direction and I've been talking to a lot
 

00:00:45.120 --> 00:00:46.369 align:start position:0%
direction and I've been talking to a lot
of<00:00:45.239><c> our</c>

00:00:46.369 --> 00:00:46.379 align:start position:0%
of our
 

00:00:46.379 --> 00:00:48.229 align:start position:0%
of our
you<00:00:46.800><c> know</c><00:00:46.860><c> customers</c><00:00:47.280><c> that</c><00:00:47.460><c> are</c><00:00:47.579><c> are</c><00:00:47.820><c> kind</c><00:00:48.059><c> of</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
you know customers that are are kind of
 

00:00:48.239 --> 00:00:50.090 align:start position:0%
you know customers that are are kind of
like<00:00:48.420><c> you</c><00:00:49.020><c> know</c><00:00:49.079><c> thinking</c><00:00:49.379><c> about</c><00:00:49.500><c> this</c><00:00:49.800><c> and</c>

00:00:50.090 --> 00:00:50.100 align:start position:0%
like you know thinking about this and
 

00:00:50.100 --> 00:00:51.770 align:start position:0%
like you know thinking about this and
and<00:00:50.280><c> noticing</c><00:00:50.700><c> I</c><00:00:50.940><c> mean</c><00:00:51.059><c> I</c><00:00:51.539><c> don't</c><00:00:51.600><c> know</c><00:00:51.660><c> if</c><00:00:51.719><c> you</c>

00:00:51.770 --> 00:00:51.780 align:start position:0%
and noticing I mean I don't know if you
 

00:00:51.780 --> 00:00:53.630 align:start position:0%
and noticing I mean I don't know if you
saw<00:00:51.899><c> that</c><00:00:52.140><c> there's</c><00:00:52.379><c> a</c><00:00:52.559><c> really</c><00:00:53.160><c> hilarious</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
saw that there's a really hilarious
 

00:00:53.640 --> 00:00:55.970 align:start position:0%
saw that there's a really hilarious
result<00:00:53.879><c> right</c><00:00:54.300><c> where</c><00:00:54.600><c> if</c><00:00:55.140><c> you</c><00:00:55.260><c> ask</c><00:00:55.440><c> the</c><00:00:55.680><c> GPT</c>

00:00:55.970 --> 00:00:55.980 align:start position:0%
result right where if you ask the GPT
 

00:00:55.980 --> 00:00:57.950 align:start position:0%
result right where if you ask the GPT
question<00:00:56.399><c> then</c><00:00:56.760><c> you</c><00:00:56.940><c> say</c><00:00:57.120><c> show</c><00:00:57.780><c> your</c>

00:00:57.950 --> 00:00:57.960 align:start position:0%
question then you say show your
 

00:00:57.960 --> 00:01:00.049 align:start position:0%
question then you say show your
reasoning<00:00:58.379><c> step</c><00:00:58.559><c> by</c><00:00:58.739><c> step</c><00:00:59.039><c> after</c><00:00:59.699><c> the</c>

00:01:00.049 --> 00:01:00.059 align:start position:0%
reasoning step by step after the
 

00:01:00.059 --> 00:01:01.010 align:start position:0%
reasoning step by step after the
question

00:01:01.010 --> 00:01:01.020 align:start position:0%
question
 

00:01:01.020 --> 00:01:02.750 align:start position:0%
question
um<00:01:01.140><c> you</c><00:01:01.320><c> know</c><00:01:01.379><c> it</c><00:01:01.620><c> gets</c><00:01:01.860><c> a</c><00:01:01.920><c> lot</c><00:01:02.100><c> more</c><00:01:02.280><c> accurate</c>

00:01:02.750 --> 00:01:02.760 align:start position:0%
um you know it gets a lot more accurate
 

00:01:02.760 --> 00:01:04.509 align:start position:0%
um you know it gets a lot more accurate
you<00:01:03.059><c> know</c><00:01:03.120><c> which</c><00:01:03.300><c> is</c><00:01:03.480><c> just</c><00:01:03.719><c> like</c><00:01:03.899><c> absolutely</c>

00:01:04.509 --> 00:01:04.519 align:start position:0%
you know which is just like absolutely
 

00:01:04.519 --> 00:01:06.410 align:start position:0%
you know which is just like absolutely
fascinating<00:01:05.519><c> probably</c><00:01:05.820><c> good</c><00:01:06.000><c> advice</c><00:01:06.180><c> to</c>

00:01:06.410 --> 00:01:06.420 align:start position:0%
fascinating probably good advice to
 

00:01:06.420 --> 00:01:07.370 align:start position:0%
fascinating probably good advice to
humans

00:01:07.370 --> 00:01:07.380 align:start position:0%
humans
 

00:01:07.380 --> 00:01:09.050 align:start position:0%
humans
um<00:01:07.500><c> as</c><00:01:07.860><c> well</c>

00:01:09.050 --> 00:01:09.060 align:start position:0%
um as well
 

00:01:09.060 --> 00:01:11.750 align:start position:0%
um as well
um<00:01:09.240><c> but</c><00:01:09.540><c> I</c><00:01:09.659><c> think</c><00:01:09.840><c> uh</c><00:01:10.439><c> I</c><00:01:11.159><c> think</c><00:01:11.220><c> that</c><00:01:11.400><c> stuff</c><00:01:11.580><c> is</c>

00:01:11.750 --> 00:01:11.760 align:start position:0%
um but I think uh I think that stuff is
 

00:01:11.760 --> 00:01:13.429 align:start position:0%
um but I think uh I think that stuff is
is<00:01:12.000><c> really</c><00:01:12.180><c> really</c><00:01:12.420><c> interesting</c><00:01:12.900><c> I</c><00:01:13.140><c> don't</c>

00:01:13.429 --> 00:01:13.439 align:start position:0%
is really really interesting I don't
 

00:01:13.439 --> 00:01:15.170 align:start position:0%
is really really interesting I don't
think<00:01:13.560><c> it's</c><00:01:13.740><c> I</c><00:01:14.040><c> think</c><00:01:14.220><c> it'd</c><00:01:14.460><c> be</c><00:01:14.520><c> you</c><00:01:14.760><c> know</c><00:01:14.820><c> to</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
think it's I think it'd be you know to
 

00:01:15.180 --> 00:01:18.170 align:start position:0%
think it's I think it'd be you know to
pontificate<00:01:16.020><c> now</c><00:01:16.200><c> about</c><00:01:16.380><c> where</c><00:01:16.619><c> this</c><00:01:16.799><c> goes</c><00:01:17.580><c> um</c>

00:01:18.170 --> 00:01:18.180 align:start position:0%
pontificate now about where this goes um
 

00:01:18.180 --> 00:01:19.910 align:start position:0%
pontificate now about where this goes um
you'll<00:01:18.720><c> probably</c><00:01:18.900><c> be</c><00:01:19.020><c> wrong</c><00:01:19.200><c> but</c><00:01:19.500><c> I</c><00:01:19.619><c> agree</c>

00:01:19.910 --> 00:01:19.920 align:start position:0%
you'll probably be wrong but I agree
 

00:01:19.920 --> 00:01:21.469 align:start position:0%
you'll probably be wrong but I agree
that<00:01:20.220><c> it</c><00:01:20.520><c> does</c><00:01:20.700><c> seem</c><00:01:20.880><c> like</c><00:01:21.000><c> there's</c><00:01:21.180><c> a</c><00:01:21.360><c> new</c>

00:01:21.469 --> 00:01:21.479 align:start position:0%
that it does seem like there's a new
 

00:01:21.479 --> 00:01:23.749 align:start position:0%
that it does seem like there's a new
kind<00:01:21.659><c> of</c><00:01:22.259><c> um</c><00:01:22.320><c> job</c><00:01:22.860><c> being</c><00:01:23.220><c> created</c><00:01:23.580><c> that</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
kind of um job being created that
 

00:01:23.759 --> 00:01:26.870 align:start position:0%
kind of um job being created that
currently<00:01:24.119><c> is</c><00:01:24.240><c> called</c><00:01:24.360><c> prompt</c><00:01:24.720><c> engineer</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
currently is called prompt engineer
 

00:01:26.880 --> 00:01:30.830 align:start position:0%
currently is called prompt engineer
yeah<00:01:27.360><c> I</c><00:01:27.479><c> mean</c><00:01:28.140><c> um</c><00:01:28.560><c> especially</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
yeah I mean um especially
 

00:01:30.840 --> 00:01:34.190 align:start position:0%
yeah I mean um especially
uh<00:01:31.619><c> especially</c><00:01:32.400><c> when</c><00:01:32.880><c> you're</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
uh especially when you're
 

00:01:34.200 --> 00:01:37.429 align:start position:0%
uh especially when you're
trying<00:01:34.740><c> to</c><00:01:34.979><c> do</c><00:01:35.340><c> something</c><00:01:35.579><c> with</c><00:01:35.880><c> llms</c><00:01:36.540><c> that</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
trying to do something with llms that
 

00:01:37.439 --> 00:01:40.730 align:start position:0%
trying to do something with llms that
require<00:01:37.799><c> context</c><00:01:38.400><c> over</c><00:01:38.640><c> time</c>

00:01:40.730 --> 00:01:40.740 align:start position:0%
require context over time
 

00:01:40.740 --> 00:01:42.710 align:start position:0%
require context over time
prompt<00:01:41.340><c> engineering</c><00:01:41.759><c> becomes</c><00:01:42.180><c> incredibly</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
prompt engineering becomes incredibly
 

00:01:42.720 --> 00:01:45.830 align:start position:0%
prompt engineering becomes incredibly
important<00:01:43.140><c> the</c><00:01:43.920><c> conduct</c><00:01:44.280><c> size</c><00:01:44.579><c> of</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
important the conduct size of
 

00:01:45.840 --> 00:01:48.890 align:start position:0%
important the conduct size of
these<00:01:46.500><c> models</c><00:01:46.799><c> is</c><00:01:47.040><c> fairly</c><00:01:47.460><c> small</c><00:01:47.759><c> right</c><00:01:48.180><c> now</c>

00:01:48.890 --> 00:01:48.900 align:start position:0%
these models is fairly small right now
 

00:01:48.900 --> 00:01:50.690 align:start position:0%
these models is fairly small right now
it's<00:01:49.619><c> about</c>

00:01:50.690 --> 00:01:50.700 align:start position:0%
it's about
 

00:01:50.700 --> 00:01:54.350 align:start position:0%
it's about
2<00:01:51.840><c> 000</c><00:01:51.960><c> tokens</c><00:01:52.680><c> which</c><00:01:53.159><c> is</c>

00:01:54.350 --> 00:01:54.360 align:start position:0%
2 000 tokens which is
 

00:01:54.360 --> 00:01:57.770 align:start position:0%
2 000 tokens which is
maybe<00:01:54.840><c> like</c><00:01:55.079><c> four</c><00:01:55.320><c> megabytes</c><00:01:55.920><c> which</c><00:01:56.159><c> is</c><00:01:56.340><c> like</c>

00:01:57.770 --> 00:01:57.780 align:start position:0%
maybe like four megabytes which is like
 

00:01:57.780 --> 00:01:58.550 align:start position:0%
maybe like four megabytes which is like
um

00:01:58.550 --> 00:01:58.560 align:start position:0%
um
 

00:01:58.560 --> 00:02:00.950 align:start position:0%
um
of<00:01:58.920><c> taxes</c><00:01:59.399><c> which</c><00:01:59.579><c> is</c><00:01:59.700><c> like</c><00:01:59.939><c> similar</c><00:02:00.540><c> to</c><00:02:00.659><c> like</c>

00:02:00.950 --> 00:02:00.960 align:start position:0%
of taxes which is like similar to like
 

00:02:00.960 --> 00:02:02.990 align:start position:0%
of taxes which is like similar to like
programming<00:02:01.560><c> in</c><00:02:01.799><c> in</c><00:02:02.040><c> Atari</c><00:02:02.640><c> or</c><00:02:02.820><c> something</c>

00:02:02.990 --> 00:02:03.000 align:start position:0%
programming in in Atari or something
 

00:02:03.000 --> 00:02:04.249 align:start position:0%
programming in in Atari or something
like<00:02:03.299><c> it's</c>

00:02:04.249 --> 00:02:04.259 align:start position:0%
like it's
 

00:02:04.259 --> 00:02:07.490 align:start position:0%
like it's
it's uh<00:02:04.979><c> fairly</c><00:02:05.280><c> early</c><00:02:05.460><c> days</c><00:02:05.840><c> uh</c><00:02:06.840><c> requires</c>

00:02:07.490 --> 00:02:07.500 align:start position:0%
it's uh fairly early days uh requires
 

00:02:07.500 --> 00:02:10.309 align:start position:0%
it's uh fairly early days uh requires
similar<00:02:08.399><c> to</c><00:02:08.520><c> like</c><00:02:08.759><c> programming</c><00:02:09.420><c> early</c><00:02:10.080><c> video</c>

00:02:10.309 --> 00:02:10.319 align:start position:0%
similar to like programming early video
 

00:02:10.319 --> 00:02:12.410 align:start position:0%
similar to like programming early video
games<00:02:10.679><c> you</c><00:02:10.979><c> had</c><00:02:11.160><c> to</c><00:02:11.280><c> do</c><00:02:11.400><c> a</c><00:02:11.580><c> lot</c><00:02:11.640><c> of</c><00:02:11.760><c> trickery</c><00:02:12.120><c> to</c>

00:02:12.410 --> 00:02:12.420 align:start position:0%
games you had to do a lot of trickery to
 

00:02:12.420 --> 00:02:14.990 align:start position:0%
games you had to do a lot of trickery to
figure<00:02:12.540><c> out</c><00:02:12.780><c> how</c><00:02:13.200><c> to</c><00:02:13.379><c> you</c><00:02:14.160><c> know</c><00:02:14.280><c> fit</c><00:02:14.819><c> things</c>

00:02:14.990 --> 00:02:15.000 align:start position:0%
figure out how to you know fit things
 

00:02:15.000 --> 00:02:16.790 align:start position:0%
figure out how to you know fit things
into<00:02:15.239><c> memory</c><00:02:15.660><c> and</c><00:02:15.840><c> I</c><00:02:16.020><c> said</c><00:02:16.140><c> I</c><00:02:16.260><c> think</c><00:02:16.379><c> prompt</c>

00:02:16.790 --> 00:02:16.800 align:start position:0%
into memory and I said I think prompt
 

00:02:16.800 --> 00:02:19.130 align:start position:0%
into memory and I said I think prompt
engineering<00:02:17.160><c> is</c><00:02:17.280><c> pretty</c><00:02:17.520><c> low</c><00:02:17.760><c> level</c>

00:02:19.130 --> 00:02:19.140 align:start position:0%
engineering is pretty low level
 

00:02:19.140 --> 00:02:21.589 align:start position:0%
engineering is pretty low level
sort<00:02:19.800><c> of</c><00:02:19.920><c> thing</c><00:02:20.099><c> right</c><00:02:20.400><c> now</c><00:02:20.580><c> and</c><00:02:20.940><c> you</c><00:02:21.120><c> need</c><00:02:21.420><c> to</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
sort of thing right now and you need to
 

00:02:21.599 --> 00:02:23.510 align:start position:0%
sort of thing right now and you need to
be<00:02:21.780><c> I</c><00:02:22.260><c> see</c><00:02:22.440><c> a</c><00:02:22.620><c> lot</c><00:02:22.739><c> of</c>

00:02:23.510 --> 00:02:23.520 align:start position:0%
be I see a lot of
 

00:02:23.520 --> 00:02:25.970 align:start position:0%
be I see a lot of
amazing<00:02:24.480><c> engineering</c><00:02:25.080><c> that</c><00:02:25.319><c> goes</c><00:02:25.620><c> into</c><00:02:25.680><c> that</c>

00:02:25.970 --> 00:02:25.980 align:start position:0%
amazing engineering that goes into that
 

00:02:25.980 --> 00:02:28.970 align:start position:0%
amazing engineering that goes into that
you<00:02:26.400><c> know</c><00:02:26.520><c> for</c><00:02:27.000><c> example</c><00:02:27.300><c> like</c>

00:02:28.970 --> 00:02:28.980 align:start position:0%
you know for example like
 

00:02:28.980 --> 00:02:31.010 align:start position:0%
you know for example like
there's<00:02:29.580><c> this</c><00:02:29.879><c> trick</c><00:02:30.180><c> I</c><00:02:30.660><c> learned</c><00:02:30.900><c> about</c>

00:02:31.010 --> 00:02:31.020 align:start position:0%
there's this trick I learned about
 

00:02:31.020 --> 00:02:33.350 align:start position:0%
there's this trick I learned about
recently<00:02:31.620><c> where</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
recently where
 

00:02:33.360 --> 00:02:35.449 align:start position:0%
recently where
um<00:02:33.480><c> let's</c><00:02:33.959><c> say</c><00:02:34.140><c> you</c><00:02:34.260><c> have</c><00:02:34.379><c> a</c><00:02:34.560><c> large</c><00:02:34.680><c> Corpus</c><00:02:35.220><c> of</c>

00:02:35.449 --> 00:02:35.459 align:start position:0%
um let's say you have a large Corpus of
 

00:02:35.459 --> 00:02:37.850 align:start position:0%
um let's say you have a large Corpus of
things<00:02:35.700><c> that</c><00:02:36.360><c> the</c><00:02:36.599><c> LM</c>

00:02:37.850 --> 00:02:37.860 align:start position:0%
things that the LM
 

00:02:37.860 --> 00:02:39.290 align:start position:0%
things that the LM
is<00:02:38.280><c> supposed</c><00:02:38.400><c> to</c><00:02:38.580><c> be</c><00:02:38.640><c> answered</c><00:02:39.060><c> questions</c>

00:02:39.290 --> 00:02:39.300 align:start position:0%
is supposed to be answered questions
 

00:02:39.300 --> 00:02:41.270 align:start position:0%
is supposed to be answered questions
about

00:02:41.270 --> 00:02:41.280 align:start position:0%
about
 

00:02:41.280 --> 00:02:43.729 align:start position:0%
about
um<00:02:41.459><c> and</c><00:02:42.120><c> you</c><00:02:42.360><c> can't</c><00:02:42.480><c> put</c><00:02:42.900><c> the</c><00:02:43.200><c> entire</c><00:02:43.440><c> thing</c>

00:02:43.729 --> 00:02:43.739 align:start position:0%
um and you can't put the entire thing
 

00:02:43.739 --> 00:02:46.670 align:start position:0%
um and you can't put the entire thing
into<00:02:43.980><c> context</c><00:02:44.519><c> of</c><00:02:44.760><c> what</c><00:02:44.940><c> you</c><00:02:45.120><c> do</c><00:02:45.239><c> is</c><00:02:45.599><c> you</c><00:02:46.560><c> take</c>

00:02:46.670 --> 00:02:46.680 align:start position:0%
into context of what you do is you take
 

00:02:46.680 --> 00:02:48.830 align:start position:0%
into context of what you do is you take
the<00:02:46.860><c> embeddings</c><00:02:47.459><c> which</c><00:02:48.180><c> is</c><00:02:48.300><c> the</c><00:02:48.480><c> internal</c>

00:02:48.830 --> 00:02:48.840 align:start position:0%
the embeddings which is the internal
 

00:02:48.840 --> 00:02:51.949 align:start position:0%
the embeddings which is the internal
representation<00:02:49.319><c> of</c><00:02:49.560><c> a</c><00:02:49.800><c> text</c><00:02:50.040><c> of</c><00:02:50.519><c> the</c><00:02:50.700><c> question</c>

00:02:51.949 --> 00:02:51.959 align:start position:0%
representation of a text of the question
 

00:02:51.959 --> 00:02:54.110 align:start position:0%
representation of a text of the question
and<00:02:52.560><c> then</c><00:02:52.739><c> you</c><00:02:53.099><c> have</c><00:02:53.220><c> an</c><00:02:53.400><c> index</c><00:02:53.640><c> of</c><00:02:53.879><c> all</c><00:02:54.000><c> the</c>

00:02:54.110 --> 00:02:54.120 align:start position:0%
and then you have an index of all the
 

00:02:54.120 --> 00:02:56.869 align:start position:0%
and then you have an index of all the
embeddings<00:02:54.540><c> of</c><00:02:54.840><c> the</c><00:02:55.200><c> Corpus</c><00:02:55.800><c> or</c><00:02:56.160><c> the</c><00:02:56.340><c> data</c>

00:02:56.869 --> 00:02:56.879 align:start position:0%
embeddings of the Corpus or the data
 

00:02:56.879 --> 00:03:01.009 align:start position:0%
embeddings of the Corpus or the data
that<00:02:57.000><c> you</c><00:02:57.180><c> have</c><00:02:57.360><c> and</c><00:02:58.260><c> then</c><00:02:58.379><c> you</c><00:02:58.620><c> do</c><00:02:59.300><c> uh</c><00:03:00.300><c> you</c><00:03:00.900><c> do</c>

00:03:01.009 --> 00:03:01.019 align:start position:0%
that you have and then you do uh you do
 

00:03:01.019 --> 00:03:02.869 align:start position:0%
that you have and then you do uh you do
you<00:03:01.260><c> match</c><00:03:01.500><c> those</c><00:03:01.860><c> embeddings</c><00:03:02.400><c> in</c><00:03:02.640><c> order</c><00:03:02.760><c> to</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
you match those embeddings in order to
 

00:03:02.879 --> 00:03:04.990 align:start position:0%
you match those embeddings in order to
figure<00:03:03.060><c> out</c><00:03:03.300><c> which</c><00:03:03.599><c> context</c><00:03:04.019><c> it</c>

00:03:04.990 --> 00:03:05.000 align:start position:0%
figure out which context it
 

00:03:05.000 --> 00:03:08.270 align:start position:0%
figure out which context it
include<00:03:06.000><c> into</c><00:03:06.420><c> the</c><00:03:06.720><c> uh</c><00:03:07.019><c> prompt</c>

00:03:08.270 --> 00:03:08.280 align:start position:0%
include into the uh prompt
 

00:03:08.280 --> 00:03:12.410 align:start position:0%
include into the uh prompt
it<00:03:08.819><c> has</c><00:03:09.120><c> like</c><00:03:09.360><c> pretty</c><00:03:10.220><c> amazing</c><00:03:11.300><c> sort</c><00:03:12.300><c> of</c>

00:03:12.410 --> 00:03:12.420 align:start position:0%
it has like pretty amazing sort of
 

00:03:12.420 --> 00:03:15.229 align:start position:0%
it has like pretty amazing sort of
algorithm<00:03:12.959><c> and</c><00:03:14.099><c> um</c>

00:03:15.229 --> 00:03:15.239 align:start position:0%
algorithm and um
 

00:03:15.239 --> 00:03:16.850 align:start position:0%
algorithm and um
and<00:03:15.720><c> yeah</c><00:03:15.840><c> I</c><00:03:15.959><c> think</c><00:03:16.260><c> it's</c><00:03:16.379><c> going</c><00:03:16.500><c> to</c><00:03:16.500><c> be</c><00:03:16.680><c> a</c>

00:03:16.850 --> 00:03:16.860 align:start position:0%
and yeah I think it's going to be a
 

00:03:16.860 --> 00:03:19.309 align:start position:0%
and yeah I think it's going to be a
specialty<00:03:17.280><c> uh</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
specialty uh
 

00:03:19.319 --> 00:03:21.890 align:start position:0%
specialty uh
you<00:03:19.800><c> know</c><00:03:19.920><c> even</c><00:03:20.400><c> if</c><00:03:20.819><c> they</c><00:03:20.940><c> solve</c><00:03:21.239><c> the</c><00:03:21.480><c> context</c>

00:03:21.890 --> 00:03:21.900 align:start position:0%
you know even if they solve the context
 

00:03:21.900 --> 00:03:25.009 align:start position:0%
you know even if they solve the context
length<00:03:22.440><c> issue</c><00:03:23.239><c> you're</c><00:03:24.239><c> still</c><00:03:24.480><c> gonna</c><00:03:24.659><c> be</c>

00:03:25.009 --> 00:03:25.019 align:start position:0%
length issue you're still gonna be
 

00:03:25.019 --> 00:03:26.509 align:start position:0%
length issue you're still gonna be
you're<00:03:25.379><c> still</c><00:03:25.560><c> gonna</c><00:03:25.680><c> have</c><00:03:25.860><c> to</c><00:03:25.980><c> be</c><00:03:26.099><c> economical</c>

00:03:26.509 --> 00:03:26.519 align:start position:0%
you're still gonna have to be economical
 

00:03:26.519 --> 00:03:27.770 align:start position:0%
you're still gonna have to be economical
about<00:03:26.700><c> it</c><00:03:26.940><c> because</c><00:03:27.120><c> you</c><00:03:27.300><c> know</c><00:03:27.420><c> I'll</c><00:03:27.599><c> be</c>

00:03:27.770 --> 00:03:27.780 align:start position:0%
about it because you know I'll be
 

00:03:27.780 --> 00:03:29.330 align:start position:0%
about it because you know I'll be
slogging<00:03:28.200><c> this</c><00:03:28.379><c> data</c><00:03:28.739><c> back</c><00:03:28.980><c> and</c><00:03:29.099><c> forth</c>

00:03:29.330 --> 00:03:29.340 align:start position:0%
slogging this data back and forth
 

00:03:29.340 --> 00:03:31.430 align:start position:0%
slogging this data back and forth
between<00:03:29.519><c> server</c><00:03:30.000><c> and</c><00:03:30.120><c> the</c><00:03:30.300><c> client</c>

00:03:31.430 --> 00:03:31.440 align:start position:0%
between server and the client
 

00:03:31.440 --> 00:03:34.550 align:start position:0%
between server and the client
uh<00:03:32.159><c> and</c><00:03:32.340><c> so</c><00:03:32.519><c> in</c><00:03:32.879><c> addition</c><00:03:33.180><c> to</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
uh and so in addition to
 

00:03:34.560 --> 00:03:37.610 align:start position:0%
uh and so in addition to
sort<00:03:35.159><c> of</c><00:03:35.220><c> having</c><00:03:35.400><c> these</c><00:03:35.819><c> prompt</c><00:03:36.300><c> tricks</c>

00:03:37.610 --> 00:03:37.620 align:start position:0%
sort of having these prompt tricks
 

00:03:37.620 --> 00:03:39.830 align:start position:0%
sort of having these prompt tricks
that<00:03:38.159><c> Lucas</c><00:03:38.580><c> talked</c><00:03:38.879><c> about</c><00:03:38.940><c> there's</c><00:03:39.599><c> also</c>

00:03:39.830 --> 00:03:39.840 align:start position:0%
that Lucas talked about there's also
 

00:03:39.840 --> 00:03:42.229 align:start position:0%
that Lucas talked about there's also
just<00:03:40.080><c> like</c><00:03:40.260><c> low-level</c><00:03:40.739><c> engineering</c><00:03:41.159><c> of</c><00:03:41.340><c> like</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
just like low-level engineering of like
 

00:03:42.239 --> 00:03:45.350 align:start position:0%
just like low-level engineering of like
how<00:03:42.720><c> much</c><00:03:43.080><c> context</c><00:03:43.980><c> to</c><00:03:44.220><c> introduce</c><00:03:44.580><c> for</c><00:03:45.120><c> that</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
how much context to introduce for that
 

00:03:45.360 --> 00:03:46.300 align:start position:0%
how much context to introduce for that
llm2

00:03:46.300 --> 00:03:46.310 align:start position:0%
llm2
 

00:03:46.310 --> 00:03:52.339 align:start position:0%
llm2
[Music]

