WEBVTT
Kind: captions
Language: en

00:00:01.199 --> 00:00:03.869 align:start position:0%
 
hey<00:00:01.440><c> everyone</c><00:00:02.440><c> this</c><00:00:02.560><c> is</c><00:00:02.720><c> rava</c><00:00:03.480><c> developer</c>

00:00:03.869 --> 00:00:03.879 align:start position:0%
hey everyone this is rava developer
 

00:00:03.879 --> 00:00:06.349 align:start position:0%
hey everyone this is rava developer
Advocate<00:00:04.240><c> at</c><00:00:04.400><c> Lama</c><00:00:04.680><c> index</c><00:00:05.520><c> so</c><00:00:05.759><c> today</c><00:00:06.040><c> we</c><00:00:06.160><c> are</c>

00:00:06.349 --> 00:00:06.359 align:start position:0%
Advocate at Lama index so today we are
 

00:00:06.359 --> 00:00:08.709 align:start position:0%
Advocate at Lama index so today we are
here<00:00:06.520><c> to</c><00:00:06.680><c> discuss</c><00:00:07.080><c> about</c><00:00:07.680><c> ama's</c><00:00:08.360><c> recent</c>

00:00:08.709 --> 00:00:08.719 align:start position:0%
here to discuss about ama's recent
 

00:00:08.719 --> 00:00:12.390 align:start position:0%
here to discuss about ama's recent
release<00:00:09.200><c> on</c><00:00:09.880><c> AMA</c><00:00:10.360><c> support</c><00:00:10.840><c> for</c><00:00:11.320><c> multimodel</c>

00:00:12.390 --> 00:00:12.400 align:start position:0%
release on AMA support for multimodel
 

00:00:12.400 --> 00:00:16.029 align:start position:0%
release on AMA support for multimodel
models<00:00:13.400><c> so</c><00:00:13.759><c> if</c><00:00:14.120><c> people</c><00:00:14.360><c> are</c><00:00:14.519><c> not</c><00:00:14.799><c> aware</c><00:00:15.120><c> of</c>

00:00:16.029 --> 00:00:16.039 align:start position:0%
models so if people are not aware of
 

00:00:16.039 --> 00:00:19.630 align:start position:0%
models so if people are not aware of
AMA<00:00:17.039><c> is</c><00:00:17.119><c> a</c><00:00:17.240><c> tool</c><00:00:17.560><c> that</c><00:00:17.800><c> allows</c><00:00:18.640><c> users</c><00:00:19.039><c> to</c><00:00:19.199><c> run</c>

00:00:19.630 --> 00:00:19.640 align:start position:0%
AMA is a tool that allows users to run
 

00:00:19.640 --> 00:00:21.830 align:start position:0%
AMA is a tool that allows users to run
open<00:00:19.880><c> source</c><00:00:20.119><c> LMS</c><00:00:20.560><c> and</c><00:00:20.720><c> multimodel</c><00:00:21.240><c> models</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
open source LMS and multimodel models
 

00:00:21.840 --> 00:00:24.470 align:start position:0%
open source LMS and multimodel models
locally<00:00:22.240><c> on</c><00:00:22.439><c> their</c><00:00:22.640><c> local</c><00:00:23.119><c> machines</c><00:00:24.119><c> it</c><00:00:24.279><c> even</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
locally on their local machines it even
 

00:00:24.480 --> 00:00:26.630 align:start position:0%
locally on their local machines it even
supports<00:00:24.880><c> a</c><00:00:25.000><c> variety</c><00:00:25.320><c> of</c><00:00:25.439><c> models</c><00:00:26.160><c> including</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
supports a variety of models including
 

00:00:26.640 --> 00:00:29.470 align:start position:0%
supports a variety of models including
Lama<00:00:26.960><c> 2</c><00:00:27.240><c> code</c><00:00:27.480><c> Lama</c><00:00:28.320><c> lava</c><00:00:28.840><c> and</c><00:00:29.000><c> various</c><00:00:29.320><c> other</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
Lama 2 code Lama lava and various other
 

00:00:29.480 --> 00:00:30.870 align:start position:0%
Lama 2 code Lama lava and various other
models

00:00:30.870 --> 00:00:30.880 align:start position:0%
models
 

00:00:30.880 --> 00:00:33.110 align:start position:0%
models
um<00:00:31.199><c> so</c><00:00:31.439><c> you</c><00:00:31.560><c> can</c><00:00:31.720><c> run</c><00:00:32.079><c> all</c><00:00:32.320><c> these</c><00:00:32.559><c> open</c><00:00:32.840><c> source</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
um so you can run all these open source
 

00:00:33.120 --> 00:00:35.830 align:start position:0%
um so you can run all these open source
models<00:00:33.480><c> locally</c><00:00:34.160><c> in</c><00:00:34.360><c> any</c><00:00:34.840><c> uh</c><00:00:35.000><c> language</c>

00:00:35.830 --> 00:00:35.840 align:start position:0%
models locally in any uh language
 

00:00:35.840 --> 00:00:38.270 align:start position:0%
models locally in any uh language
quickly<00:00:36.680><c> on</c><00:00:36.840><c> your</c><00:00:37.040><c> local</c><00:00:37.280><c> machine</c><00:00:37.719><c> that's</c><00:00:37.960><c> the</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
quickly on your local machine that's the
 

00:00:38.280 --> 00:00:40.950 align:start position:0%
quickly on your local machine that's the
biggest<00:00:38.640><c> advantage</c><00:00:39.079><c> that</c><00:00:39.200><c> you</c><00:00:39.320><c> get</c><00:00:39.559><c> with</c><00:00:39.960><c> AMA</c>

00:00:40.950 --> 00:00:40.960 align:start position:0%
biggest advantage that you get with AMA
 

00:00:40.960 --> 00:00:42.750 align:start position:0%
biggest advantage that you get with AMA
and<00:00:41.160><c> there</c><00:00:41.239><c> are</c><00:00:41.399><c> various</c><00:00:41.719><c> other</c><00:00:42.120><c> benefits</c><00:00:42.600><c> as</c>

00:00:42.750 --> 00:00:42.760 align:start position:0%
and there are various other benefits as
 

00:00:42.760 --> 00:00:44.869 align:start position:0%
and there are various other benefits as
well<00:00:43.200><c> uh</c><00:00:43.360><c> such</c><00:00:43.559><c> as</c><00:00:43.760><c> the</c><00:00:44.039><c> cost</c><00:00:44.360><c> part</c><00:00:44.680><c> since</c>

00:00:44.869 --> 00:00:44.879 align:start position:0%
well uh such as the cost part since
 

00:00:44.879 --> 00:00:47.350 align:start position:0%
well uh such as the cost part since
you're<00:00:45.079><c> running</c><00:00:45.399><c> locally</c><00:00:46.360><c> all</c><00:00:46.520><c> these</c><00:00:46.719><c> models</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
you're running locally all these models
 

00:00:47.360 --> 00:00:50.549 align:start position:0%
you're running locally all these models
you<00:00:47.440><c> don't</c><00:00:47.680><c> have</c><00:00:47.840><c> to</c><00:00:48.760><c> uh</c><00:00:48.879><c> host</c><00:00:49.120><c> it</c><00:00:49.280><c> on</c><00:00:49.600><c> cloud</c><00:00:50.039><c> so</c>

00:00:50.549 --> 00:00:50.559 align:start position:0%
you don't have to uh host it on cloud so
 

00:00:50.559 --> 00:00:53.229 align:start position:0%
you don't have to uh host it on cloud so
you<00:00:50.640><c> can</c><00:00:50.800><c> avoid</c><00:00:51.239><c> all</c><00:00:51.399><c> the</c><00:00:51.520><c> cloud</c><00:00:51.879><c> costs</c><00:00:52.719><c> and</c><00:00:53.120><c> uh</c>

00:00:53.229 --> 00:00:53.239 align:start position:0%
you can avoid all the cloud costs and uh
 

00:00:53.239 --> 00:00:56.950 align:start position:0%
you can avoid all the cloud costs and uh
it's<00:00:53.440><c> fully</c><00:00:54.320><c> uh</c><00:00:54.840><c> private</c><00:00:55.320><c> right</c><00:00:55.559><c> so</c><00:00:56.480><c> it</c><00:00:56.600><c> allows</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
it's fully uh private right so it allows
 

00:00:56.960 --> 00:00:58.869 align:start position:0%
it's fully uh private right so it allows
you<00:00:57.120><c> to</c><00:00:57.359><c> keep</c><00:00:57.600><c> your</c><00:00:57.800><c> data</c><00:00:58.079><c> secure</c><00:00:58.440><c> by</c><00:00:58.559><c> running</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
you to keep your data secure by running
 

00:00:58.879 --> 00:01:01.029 align:start position:0%
you to keep your data secure by running
models<00:00:59.199><c> on</c><00:00:59.320><c> your</c><00:00:59.480><c> local</c><00:00:59.680><c> machine</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
models on your local machine
 

00:01:01.039 --> 00:01:03.910 align:start position:0%
models on your local machine
so<00:01:01.760><c> I</c><00:01:01.840><c> think</c><00:01:02.079><c> by</c><00:01:02.239><c> now</c><00:01:02.559><c> you</c><00:01:02.800><c> might</c><00:01:03.039><c> have</c><00:01:03.239><c> got</c><00:01:03.440><c> a</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
so I think by now you might have got a
 

00:01:03.920 --> 00:01:06.710 align:start position:0%
so I think by now you might have got a
brief<00:01:04.239><c> sense</c><00:01:04.519><c> of</c><00:01:04.799><c> what</c><00:01:05.040><c> AMA</c><00:01:05.519><c> is</c><00:01:05.840><c> and</c><00:01:06.280><c> so</c><00:01:06.439><c> let's</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
brief sense of what AMA is and so let's
 

00:01:06.720 --> 00:01:09.429 align:start position:0%
brief sense of what AMA is and so let's
get<00:01:06.960><c> back</c><00:01:07.119><c> to</c><00:01:07.400><c> the</c><00:01:07.520><c> focus</c><00:01:07.880><c> of</c><00:01:08.360><c> this</c><00:01:08.520><c> video</c>

00:01:09.429 --> 00:01:09.439 align:start position:0%
get back to the focus of this video
 

00:01:09.439 --> 00:01:11.950 align:start position:0%
get back to the focus of this video
which<00:01:09.600><c> is</c><00:01:10.040><c> AMA</c><00:01:10.479><c> support</c><00:01:10.840><c> for</c><00:01:11.240><c> multimodel</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
which is AMA support for multimodel
 

00:01:11.960 --> 00:01:15.670 align:start position:0%
which is AMA support for multimodel
models<00:01:12.960><c> so</c><00:01:13.320><c> with</c><00:01:13.600><c> AMA</c><00:01:14.159><c> you</c><00:01:14.280><c> can</c><00:01:14.640><c> actually</c><00:01:14.960><c> run</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
models so with AMA you can actually run
 

00:01:15.680 --> 00:01:21.069 align:start position:0%
models so with AMA you can actually run
um<00:01:16.680><c> uh</c><00:01:16.799><c> lava</c><00:01:17.159><c> models</c><00:01:17.840><c> locally</c><00:01:18.840><c> uh</c><00:01:19.280><c> so</c><00:01:20.280><c> all</c><00:01:20.880><c> uh</c>

00:01:21.069 --> 00:01:21.079 align:start position:0%
um uh lava models locally uh so all uh
 

00:01:21.079 --> 00:01:25.789 align:start position:0%
um uh lava models locally uh so all uh
you<00:01:21.320><c> basically</c><00:01:21.720><c> need</c><00:01:21.920><c> to</c><00:01:22.600><c> do</c><00:01:22.880><c> is</c><00:01:23.520><c> um</c><00:01:24.360><c> just</c><00:01:24.840><c> AMA</c>

00:01:25.789 --> 00:01:25.799 align:start position:0%
you basically need to do is um just AMA
 

00:01:25.799 --> 00:01:28.230 align:start position:0%
you basically need to do is um just AMA
run<00:01:26.119><c> lava</c><00:01:26.799><c> and</c><00:01:26.960><c> your</c><00:01:27.240><c> prom</c><00:01:27.640><c> probably</c>

00:01:28.230 --> 00:01:28.240 align:start position:0%
run lava and your prom probably
 

00:01:28.240 --> 00:01:30.350 align:start position:0%
run lava and your prom probably
something<00:01:28.600><c> like</c><00:01:28.799><c> describe</c><00:01:29.479><c> this</c><00:01:29.600><c> image</c><00:01:30.159><c> or</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
something like describe this image or
 

00:01:30.360 --> 00:01:31.710 align:start position:0%
something like describe this image or
what's<00:01:30.640><c> present</c><00:01:30.880><c> in</c><00:01:31.000><c> this</c><00:01:31.159><c> image</c><00:01:31.400><c> or</c><00:01:31.560><c> any</c>

00:01:31.710 --> 00:01:31.720 align:start position:0%
what's present in this image or any
 

00:01:31.720 --> 00:01:33.310 align:start position:0%
what's present in this image or any
other<00:01:31.960><c> thing</c><00:01:32.200><c> that</c><00:01:32.320><c> you</c><00:01:32.479><c> want</c><00:01:32.880><c> you</c><00:01:33.000><c> would</c><00:01:33.200><c> like</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
other thing that you want you would like
 

00:01:33.320 --> 00:01:36.870 align:start position:0%
other thing that you want you would like
to<00:01:33.479><c> know</c><00:01:33.640><c> about</c><00:01:33.840><c> an</c><00:01:34.079><c> image</c><00:01:35.079><c> and</c><00:01:35.280><c> then</c><00:01:35.840><c> uh</c><00:01:36.240><c> pass</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
to know about an image and then uh pass
 

00:01:36.880 --> 00:01:40.389 align:start position:0%
to know about an image and then uh pass
the<00:01:37.880><c> path</c><00:01:38.200><c> of</c><00:01:38.360><c> the</c><00:01:38.560><c> image</c><00:01:39.280><c> and</c><00:01:39.479><c> boom</c><00:01:40.079><c> uh</c><00:01:40.200><c> in</c><00:01:40.280><c> a</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
the path of the image and boom uh in a
 

00:01:40.399 --> 00:01:42.870 align:start position:0%
the path of the image and boom uh in a
matter<00:01:40.640><c> of</c><00:01:40.799><c> seconds</c><00:01:41.520><c> you'll</c><00:01:41.840><c> get</c><00:01:42.000><c> the</c><00:01:42.159><c> result</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
matter of seconds you'll get the result
 

00:01:42.880 --> 00:01:46.950 align:start position:0%
matter of seconds you'll get the result
all<00:01:43.560><c> locally</c><00:01:44.560><c> so</c><00:01:45.159><c> it</c><00:01:45.320><c> in</c><00:01:45.520><c> fact</c><00:01:45.960><c> supports</c><00:01:46.680><c> uh</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
all locally so it in fact supports uh
 

00:01:46.960 --> 00:01:49.749 align:start position:0%
all locally so it in fact supports uh
various<00:01:47.640><c> other</c><00:01:47.920><c> use</c><00:01:48.280><c> cases</c><00:01:48.719><c> with</c><00:01:49.079><c> the</c><00:01:49.399><c> lava</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
various other use cases with the lava
 

00:01:49.759 --> 00:01:52.830 align:start position:0%
various other use cases with the lava
model<00:01:50.240><c> something</c><00:01:50.640><c> like</c><00:01:51.079><c> uh</c><00:01:51.280><c> object</c><00:01:51.840><c> detection</c>

00:01:52.830 --> 00:01:52.840 align:start position:0%
model something like uh object detection
 

00:01:52.840 --> 00:01:56.270 align:start position:0%
model something like uh object detection
and<00:01:53.200><c> even</c><00:01:53.799><c> uh</c><00:01:54.280><c> text</c><00:01:54.600><c> recognition</c><00:01:55.119><c> use</c><00:01:55.479><c> case</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
and even uh text recognition use case
 

00:01:56.280 --> 00:01:58.950 align:start position:0%
and even uh text recognition use case
and<00:01:57.039><c> there</c><00:01:57.159><c> are</c><00:01:57.399><c> even</c><00:01:57.840><c> various</c><00:01:58.200><c> other</c><00:01:58.759><c> uh</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
and there are even various other uh
 

00:01:58.960 --> 00:02:00.630 align:start position:0%
and there are even various other uh
parameter<00:01:59.439><c> models</c>

00:02:00.630 --> 00:02:00.640 align:start position:0%
parameter models
 

00:02:00.640 --> 00:02:04.870 align:start position:0%
parameter models
uh<00:02:01.079><c> from</c><00:02:01.360><c> 7</c><00:02:01.719><c> B</c><00:02:02.280><c> billion</c><00:02:02.759><c> parameter</c><00:02:03.280><c> to</c><00:02:04.240><c> uh</c><00:02:04.520><c> 13</c>

00:02:04.870 --> 00:02:04.880 align:start position:0%
uh from 7 B billion parameter to uh 13
 

00:02:04.880 --> 00:02:09.710 align:start position:0%
uh from 7 B billion parameter to uh 13
billion<00:02:05.200><c> and</c><00:02:05.479><c> 24</c><00:02:06.479><c> billion</c><00:02:06.960><c> parameter</c><00:02:07.960><c> models</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
billion and 24 billion parameter models
 

00:02:09.720 --> 00:02:13.350 align:start position:0%
billion and 24 billion parameter models
so<00:02:10.720><c> so</c><00:02:10.959><c> in</c><00:02:11.120><c> this</c><00:02:11.599><c> um</c><00:02:12.239><c> video</c><00:02:12.680><c> we'll</c><00:02:12.959><c> have</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
so so in this um video we'll have
 

00:02:13.360 --> 00:02:15.470 align:start position:0%
so so in this um video we'll have
basically<00:02:13.800><c> two</c><00:02:14.040><c> demonstration</c><00:02:15.040><c> first</c><00:02:15.280><c> let</c><00:02:15.400><c> me</c>

00:02:15.470 --> 00:02:15.480 align:start position:0%
basically two demonstration first let me
 

00:02:15.480 --> 00:02:17.949 align:start position:0%
basically two demonstration first let me
show<00:02:15.680><c> you</c><00:02:15.920><c> how</c><00:02:16.120><c> you</c><00:02:16.239><c> can</c><00:02:16.360><c> run</c><00:02:16.920><c> AMA</c><00:02:17.440><c> locally</c><00:02:17.840><c> on</c>

00:02:17.949 --> 00:02:17.959 align:start position:0%
show you how you can run AMA locally on
 

00:02:17.959 --> 00:02:21.110 align:start position:0%
show you how you can run AMA locally on
your<00:02:18.319><c> laptop</c><00:02:19.319><c> and</c><00:02:19.519><c> then</c><00:02:19.879><c> we'll</c><00:02:20.120><c> look</c><00:02:20.319><c> into</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
your laptop and then we'll look into
 

00:02:21.120 --> 00:02:25.869 align:start position:0%
your laptop and then we'll look into
usage<00:02:21.519><c> of</c><00:02:21.720><c> AMA</c><00:02:22.720><c> Lo</c><00:02:23.440><c> models</c><00:02:24.080><c> with</c><00:02:24.400><c> the</c><00:02:24.879><c> Llama</c>

00:02:25.869 --> 00:02:25.879 align:start position:0%
usage of AMA Lo models with the Llama
 

00:02:25.879 --> 00:02:28.190 align:start position:0%
usage of AMA Lo models with the Llama
index<00:02:26.879><c> so</c><00:02:27.080><c> in</c><00:02:27.239><c> this</c><00:02:27.400><c> part</c><00:02:27.560><c> we'll</c><00:02:27.800><c> cover</c>

00:02:28.190 --> 00:02:28.200 align:start position:0%
index so in this part we'll cover
 

00:02:28.200 --> 00:02:30.110 align:start position:0%
index so in this part we'll cover
extracting<00:02:28.959><c> uh</c><00:02:29.080><c> structured</c><00:02:29.800><c> information</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
extracting uh structured information
 

00:02:30.120 --> 00:02:31.790 align:start position:0%
extracting uh structured information
from<00:02:30.280><c> an</c><00:02:30.440><c> image</c><00:02:31.000><c> as</c><00:02:31.120><c> well</c><00:02:31.280><c> as</c><00:02:31.440><c> building</c>

00:02:31.790 --> 00:02:31.800 align:start position:0%
from an image as well as building
 

00:02:31.800 --> 00:02:35.150 align:start position:0%
from an image as well as building
multimodel<00:02:32.440><c> rag</c><00:02:32.959><c> with</c><00:02:33.120><c> AMA</c><00:02:33.480><c> and</c><00:02:33.599><c> Lama</c><00:02:34.160><c> index</c>

00:02:35.150 --> 00:02:35.160 align:start position:0%
multimodel rag with AMA and Lama index
 

00:02:35.160 --> 00:02:37.949 align:start position:0%
multimodel rag with AMA and Lama index
so<00:02:35.560><c> let's</c><00:02:35.840><c> get</c><00:02:36.000><c> started</c><00:02:36.319><c> with</c>

00:02:37.949 --> 00:02:37.959 align:start position:0%
so let's get started with
 

00:02:37.959 --> 00:02:41.390 align:start position:0%
so let's get started with
it<00:02:38.959><c> so</c><00:02:39.120><c> we</c><00:02:39.280><c> have</c><00:02:39.440><c> two</c><00:02:39.959><c> images</c><00:02:40.959><c> uh</c><00:02:41.080><c> the</c><00:02:41.200><c> first</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
it so we have two images uh the first
 

00:02:41.400 --> 00:02:44.309 align:start position:0%
it so we have two images uh the first
one<00:02:41.599><c> is</c><00:02:41.800><c> an</c><00:02:42.000><c> image</c><00:02:42.360><c> with</c><00:02:42.480><c> a</c><00:02:42.640><c> text</c><00:02:43.040><c> Ama</c><00:02:43.519><c> on</c><00:02:43.680><c> it</c>

00:02:44.309 --> 00:02:44.319 align:start position:0%
one is an image with a text Ama on it
 

00:02:44.319 --> 00:02:48.270 align:start position:0%
one is an image with a text Ama on it
and<00:02:44.480><c> the</c><00:02:44.640><c> second</c><00:02:44.879><c> one</c><00:02:45.120><c> is</c><00:02:45.920><c> uh</c><00:02:46.920><c> just</c><00:02:47.440><c> a</c><00:02:47.680><c> receipt</c>

00:02:48.270 --> 00:02:48.280 align:start position:0%
and the second one is uh just a receipt
 

00:02:48.280 --> 00:02:51.550 align:start position:0%
and the second one is uh just a receipt
in<00:02:48.400><c> a</c><00:02:48.720><c> restaurant</c><00:02:49.720><c> uh</c><00:02:49.840><c> so</c><00:02:50.480><c> we'll</c><00:02:51.040><c> use</c><00:02:51.360><c> both</c>

00:02:51.550 --> 00:02:51.560 align:start position:0%
in a restaurant uh so we'll use both
 

00:02:51.560 --> 00:02:55.430 align:start position:0%
in a restaurant uh so we'll use both
these<00:02:51.720><c> imag</c><00:02:52.080><c> and</c><00:02:52.239><c> see</c><00:02:52.599><c> how</c><00:02:53.400><c> gives</c><00:02:53.800><c> the</c><00:02:54.200><c> text</c><00:02:55.200><c> as</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
these imag and see how gives the text as
 

00:02:55.440 --> 00:02:57.710 align:start position:0%
these imag and see how gives the text as
said<00:02:55.599><c> we'll</c><00:02:55.840><c> be</c><00:02:56.000><c> taking</c><00:02:56.280><c> two</c><00:02:56.519><c> images</c><00:02:57.080><c> one</c><00:02:57.319><c> with</c>

00:02:57.710 --> 00:02:57.720 align:start position:0%
said we'll be taking two images one with
 

00:02:57.720 --> 00:03:00.630 align:start position:0%
said we'll be taking two images one with
uh<00:02:57.879><c> an</c><00:02:58.080><c> image</c><00:02:58.680><c> with</c><00:02:59.000><c> embeded</c><00:02:59.440><c> text</c><00:02:59.920><c> AMA</c><00:03:00.440><c> and</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
uh an image with embeded text AMA and
 

00:03:00.640 --> 00:03:03.789 align:start position:0%
uh an image with embeded text AMA and
then<00:03:00.959><c> a</c><00:03:01.159><c> reip</c><00:03:01.480><c> image</c><00:03:02.400><c> so</c><00:03:03.080><c> we</c><00:03:03.239><c> have</c><00:03:03.400><c> both</c><00:03:03.640><c> these</c>

00:03:03.789 --> 00:03:03.799 align:start position:0%
then a reip image so we have both these
 

00:03:03.799 --> 00:03:05.910 align:start position:0%
then a reip image so we have both these
images<00:03:04.319><c> so</c><00:03:04.480><c> let's</c><00:03:04.680><c> run</c><00:03:05.200><c> uh</c><00:03:05.360><c> some</c><00:03:05.599><c> simple</c>

00:03:05.910 --> 00:03:05.920 align:start position:0%
images so let's run uh some simple
 

00:03:05.920 --> 00:03:08.990 align:start position:0%
images so let's run uh some simple
queries<00:03:06.319><c> on</c><00:03:06.480><c> it</c><00:03:07.280><c> so</c><00:03:07.560><c> let's</c><00:03:07.840><c> start</c>

00:03:08.990 --> 00:03:09.000 align:start position:0%
queries on it so let's start
 

00:03:09.000 --> 00:03:13.229 align:start position:0%
queries on it so let's start
withun<00:03:10.319><c> laa</c><00:03:11.319><c> and</c><00:03:11.519><c> then</c><00:03:11.959><c> some</c><00:03:12.360><c> question</c><00:03:13.000><c> let's</c>

00:03:13.229 --> 00:03:13.239 align:start position:0%
withun laa and then some question let's
 

00:03:13.239 --> 00:03:16.470 align:start position:0%
withun laa and then some question let's
say<00:03:14.080><c> what's</c><00:03:15.000><c> in</c><00:03:15.200><c> the</c>

00:03:16.470 --> 00:03:16.480 align:start position:0%
say what's in the
 

00:03:16.480 --> 00:03:19.270 align:start position:0%
say what's in the
image<00:03:17.480><c> and</c><00:03:17.799><c> then</c><00:03:18.040><c> you</c><00:03:18.159><c> need</c><00:03:18.319><c> to</c><00:03:18.560><c> view</c><00:03:18.879><c> the</c><00:03:19.040><c> path</c>

00:03:19.270 --> 00:03:19.280 align:start position:0%
image and then you need to view the path
 

00:03:19.280 --> 00:03:22.990 align:start position:0%
image and then you need to view the path
of<00:03:19.440><c> the</c><00:03:20.120><c> image</c>

00:03:22.990 --> 00:03:23.000 align:start position:0%
of the image
 

00:03:23.000 --> 00:03:25.149 align:start position:0%
of the image
.png<00:03:24.000><c> okay</c>

00:03:25.149 --> 00:03:25.159 align:start position:0%
.png okay
 

00:03:25.159 --> 00:03:28.270 align:start position:0%
.png okay
so<00:03:26.159><c> um</c><00:03:26.519><c> this</c><00:03:26.640><c> will</c><00:03:26.840><c> take</c><00:03:27.000><c> a</c><00:03:27.120><c> couple</c><00:03:27.360><c> of</c><00:03:27.519><c> seconds</c>

00:03:28.270 --> 00:03:28.280 align:start position:0%
so um this will take a couple of seconds
 

00:03:28.280 --> 00:03:30.949 align:start position:0%
so um this will take a couple of seconds
uh<00:03:28.560><c> for</c><00:03:28.760><c> you</c><00:03:28.879><c> to</c><00:03:29.120><c> get</c><00:03:29.360><c> the</c><00:03:29.760><c> output</c><00:03:30.400><c> and</c><00:03:30.640><c> the</c>

00:03:30.949 --> 00:03:30.959 align:start position:0%
uh for you to get the output and the
 

00:03:30.959 --> 00:03:35.270 align:start position:0%
uh for you to get the output and the
output<00:03:31.680><c> will</c><00:03:31.840><c> be</c><00:03:32.599><c> streaming</c><00:03:33.920><c> um</c><00:03:34.920><c> let's</c><00:03:35.120><c> check</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
output will be streaming um let's check
 

00:03:35.280 --> 00:03:37.149 align:start position:0%
output will be streaming um let's check
it

00:03:37.149 --> 00:03:37.159 align:start position:0%
it
 

00:03:37.159 --> 00:03:41.030 align:start position:0%
it
out<00:03:38.159><c> so</c><00:03:38.360><c> it</c><00:03:38.560><c> perfectly</c><00:03:38.959><c> generated</c><00:03:39.840><c> the</c><00:03:40.840><c> the</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
out so it perfectly generated the the
 

00:03:41.040 --> 00:03:44.070 align:start position:0%
out so it perfectly generated the the
output<00:03:41.879><c> I</c><00:03:41.959><c> mean</c><00:03:42.280><c> the</c><00:03:42.400><c> there</c><00:03:42.519><c> is</c><00:03:42.599><c> a</c><00:03:42.799><c> text</c><00:03:43.360><c> Ama</c>

00:03:44.070 --> 00:03:44.080 align:start position:0%
output I mean the there is a text Ama
 

00:03:44.080 --> 00:03:47.070 align:start position:0%
output I mean the there is a text Ama
and<00:03:44.560><c> then</c><00:03:45.560><c> um</c><00:03:45.879><c> some</c><00:03:46.040><c> 3D</c><00:03:46.400><c> letters</c><00:03:46.720><c> and</c><00:03:46.879><c> what</c><00:03:47.000><c> is</c>

00:03:47.070 --> 00:03:47.080 align:start position:0%
and then um some 3D letters and what is
 

00:03:47.080 --> 00:03:49.350 align:start position:0%
and then um some 3D letters and what is
the<00:03:47.159><c> meaning</c><00:03:47.439><c> of</c><00:03:47.640><c> AMA</c><00:03:48.120><c> as</c><00:03:48.239><c> well</c><00:03:48.519><c> to</c><00:03:48.840><c> there</c><00:03:48.959><c> is</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
the meaning of AMA as well to there is
 

00:03:49.360 --> 00:03:53.229 align:start position:0%
the meaning of AMA as well to there is
some<00:03:50.360><c> uh</c><00:03:50.680><c> misspellings</c><00:03:51.239><c> in</c><00:03:51.360><c> it</c><00:03:52.000><c> and</c><00:03:52.400><c> let's</c><00:03:52.599><c> run</c>

00:03:53.229 --> 00:03:53.239 align:start position:0%
some uh misspellings in it and let's run
 

00:03:53.239 --> 00:03:55.270 align:start position:0%
some uh misspellings in it and let's run
uh<00:03:53.599><c> the</c><00:03:53.840><c> on</c><00:03:54.040><c> the</c><00:03:54.200><c> other</c><00:03:54.480><c> image</c><00:03:54.840><c> as</c><00:03:55.000><c> well</c>

00:03:55.270 --> 00:03:55.280 align:start position:0%
uh the on the other image as well
 

00:03:55.280 --> 00:03:58.390 align:start position:0%
uh the on the other image as well
probably<00:03:55.720><c> Reed</c><00:03:56.720><c> and</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
probably Reed and
 

00:03:58.400 --> 00:04:07.589 align:start position:0%
probably Reed and
see<00:03:59.400><c> uh</c><00:03:59.720><c> whats</c><00:04:00.599><c> will</c><00:04:00.799><c> be</c><00:04:00.959><c> the</c>

00:04:07.589 --> 00:04:07.599 align:start position:0%
 
 

00:04:07.599 --> 00:04:09.309 align:start position:0%
 
output

00:04:09.309 --> 00:04:09.319 align:start position:0%
output
 

00:04:09.319 --> 00:04:14.350 align:start position:0%
output
so<00:04:10.319><c> so</c><00:04:10.519><c> the</c><00:04:10.720><c> image</c><00:04:11.560><c> shows</c><00:04:12.079><c> re</c><00:04:12.400><c> with</c><00:04:12.599><c> text</c><00:04:13.360><c> reads</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
so so the image shows re with text reads
 

00:04:14.360 --> 00:04:18.349 align:start position:0%
so so the image shows re with text reads
uh<00:04:14.480><c> some</c><00:04:14.760><c> information</c><00:04:15.720><c> about</c><00:04:16.759><c> something</c><00:04:17.759><c> ah</c><00:04:18.239><c> I</c>

00:04:18.349 --> 00:04:18.359 align:start position:0%
uh some information about something ah I
 

00:04:18.359 --> 00:04:21.710 align:start position:0%
uh some information about something ah I
think<00:04:18.560><c> it</c><00:04:18.759><c> just</c><00:04:19.000><c> hallucinated</c><00:04:19.639><c> with</c><00:04:20.079><c> it</c>

00:04:21.710 --> 00:04:21.720 align:start position:0%
think it just hallucinated with it
 

00:04:21.720 --> 00:04:25.270 align:start position:0%
think it just hallucinated with it
because<00:04:22.720><c> the</c><00:04:22.880><c> defense</c><00:04:23.280><c> ofice</c><00:04:24.120><c> actions</c><00:04:24.680><c> Judes</c>

00:04:25.270 --> 00:04:25.280 align:start position:0%
because the defense ofice actions Judes
 

00:04:25.280 --> 00:04:26.870 align:start position:0%
because the defense ofice actions Judes
something

00:04:26.870 --> 00:04:26.880 align:start position:0%
something
 

00:04:26.880 --> 00:04:29.350 align:start position:0%
something
and<00:04:27.880><c> and</c><00:04:28.080><c> yeah</c><00:04:28.199><c> I</c><00:04:28.320><c> bought</c><00:04:28.479><c> a</c><00:04:28.639><c> pizza</c><00:04:28.960><c> place</c><00:04:29.199><c> I</c>

00:04:29.350 --> 00:04:29.360 align:start position:0%
and and yeah I bought a pizza place I
 

00:04:29.360 --> 00:04:30.110 align:start position:0%
and and yeah I bought a pizza place I
guess

00:04:30.110 --> 00:04:30.120 align:start position:0%
guess
 

00:04:30.120 --> 00:04:32.270 align:start position:0%
guess
uh<00:04:30.440><c> but</c><00:04:30.639><c> yeah</c><00:04:30.919><c> but</c><00:04:31.120><c> that's</c><00:04:31.320><c> how</c><00:04:31.520><c> you</c><00:04:31.639><c> can</c><00:04:31.800><c> run</c>

00:04:32.270 --> 00:04:32.280 align:start position:0%
uh but yeah but that's how you can run
 

00:04:32.280 --> 00:04:35.950 align:start position:0%
uh but yeah but that's how you can run
AMA<00:04:32.759><c> on</c><00:04:32.960><c> your</c><00:04:33.440><c> uh</c><00:04:34.160><c> laptop</c><00:04:35.160><c> next</c><00:04:35.440><c> we'll</c><00:04:35.759><c> look</c>

00:04:35.950 --> 00:04:35.960 align:start position:0%
AMA on your uh laptop next we'll look
 

00:04:35.960 --> 00:04:39.350 align:start position:0%
AMA on your uh laptop next we'll look
into<00:04:36.919><c> how</c><00:04:37.120><c> you</c><00:04:37.240><c> can</c><00:04:37.600><c> use</c><00:04:38.479><c> models</c><00:04:38.880><c> with</c><00:04:39.039><c> llama</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
into how you can use models with llama
 

00:04:39.360 --> 00:04:42.070 align:start position:0%
into how you can use models with llama
index<00:04:39.800><c> to</c><00:04:39.960><c> build</c><00:04:40.479><c> uh</c><00:04:40.680><c> extract</c><00:04:41.440><c> information</c><00:04:41.960><c> as</c>

00:04:42.070 --> 00:04:42.080 align:start position:0%
index to build uh extract information as
 

00:04:42.080 --> 00:04:44.189 align:start position:0%
index to build uh extract information as
well<00:04:42.240><c> as</c><00:04:42.440><c> multimodel</c><00:04:43.000><c> rack</c>

00:04:44.189 --> 00:04:44.199 align:start position:0%
well as multimodel rack
 

00:04:44.199 --> 00:04:46.710 align:start position:0%
well as multimodel rack
stuff<00:04:45.199><c> now</c><00:04:45.360><c> we</c><00:04:45.600><c> look</c><00:04:45.759><c> into</c><00:04:46.080><c> how</c><00:04:46.280><c> you</c><00:04:46.400><c> can</c><00:04:46.520><c> use</c>

00:04:46.710 --> 00:04:46.720 align:start position:0%
stuff now we look into how you can use
 

00:04:46.720 --> 00:04:50.629 align:start position:0%
stuff now we look into how you can use
AMA<00:04:47.160><c> models</c><00:04:47.960><c> with</c><00:04:48.960><c> llama</c><00:04:49.320><c> index</c><00:04:50.120><c> so</c><00:04:50.360><c> here</c><00:04:50.560><c> we</c>

00:04:50.629 --> 00:04:50.639 align:start position:0%
AMA models with llama index so here we
 

00:04:50.639 --> 00:04:54.230 align:start position:0%
AMA models with llama index so here we
are<00:04:51.039><c> specifically</c><00:04:52.039><c> looking</c><00:04:52.400><c> at</c><00:04:52.840><c> using</c><00:04:53.759><c> lava</c>

00:04:54.230 --> 00:04:54.240 align:start position:0%
are specifically looking at using lava
 

00:04:54.240 --> 00:04:57.710 align:start position:0%
are specifically looking at using lava
multimodel<00:04:55.160><c> model</c><00:04:56.160><c> with</c><00:04:56.320><c> o</c><00:04:56.560><c> Lama</c><00:04:56.960><c> and</c><00:04:57.160><c> llama</c>

00:04:57.710 --> 00:04:57.720 align:start position:0%
multimodel model with o Lama and llama
 

00:04:57.720 --> 00:05:00.510 align:start position:0%
multimodel model with o Lama and llama
index<00:04:58.720><c> so</c><00:04:59.120><c> in</c><00:04:59.280><c> this</c><00:04:59.600><c> cookbook</c><00:05:00.000><c> We</c><00:05:00.199><c> have</c>

00:05:00.510 --> 00:05:00.520 align:start position:0%
index so in this cookbook We have
 

00:05:00.520 --> 00:05:02.070 align:start position:0%
index so in this cookbook We have
basically<00:05:00.919><c> three</c><00:05:01.080><c> use</c><00:05:01.360><c> cases</c><00:05:01.680><c> but</c>

00:05:02.070 --> 00:05:02.080 align:start position:0%
basically three use cases but
 

00:05:02.080 --> 00:05:04.749 align:start position:0%
basically three use cases but
specifically<00:05:02.520><c> I'll</c><00:05:02.720><c> be</c><00:05:02.919><c> looking</c><00:05:03.720><c> going</c><00:05:04.479><c> deep</c>

00:05:04.749 --> 00:05:04.759 align:start position:0%
specifically I'll be looking going deep
 

00:05:04.759 --> 00:05:07.230 align:start position:0%
specifically I'll be looking going deep
into<00:05:05.720><c> uh</c><00:05:05.880><c> data</c><00:05:06.120><c> extraction</c><00:05:06.560><c> from</c><00:05:06.680><c> the</c><00:05:06.800><c> images</c>

00:05:07.230 --> 00:05:07.240 align:start position:0%
into uh data extraction from the images
 

00:05:07.240 --> 00:05:09.749 align:start position:0%
into uh data extraction from the images
part<00:05:07.440><c> and</c><00:05:07.759><c> as</c><00:05:07.840><c> well</c><00:05:08.000><c> as</c><00:05:08.120><c> multimodel</c><00:05:08.639><c> rack</c><00:05:09.560><c> um</c>

00:05:09.749 --> 00:05:09.759 align:start position:0%
part and as well as multimodel rack um
 

00:05:09.759 --> 00:05:11.830 align:start position:0%
part and as well as multimodel rack um
you<00:05:09.880><c> can</c><00:05:10.199><c> always</c><00:05:10.520><c> check</c><00:05:10.759><c> the</c><00:05:10.880><c> second</c><00:05:11.160><c> one</c><00:05:11.440><c> Ral</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
you can always check the second one Ral
 

00:05:11.840 --> 00:05:13.590 align:start position:0%
you can always check the second one Ral
augmented<00:05:12.360><c> image</c><00:05:12.680><c> captioning</c><00:05:13.160><c> as</c><00:05:13.280><c> well</c><00:05:13.479><c> in</c>

00:05:13.590 --> 00:05:13.600 align:start position:0%
augmented image captioning as well in
 

00:05:13.600 --> 00:05:15.350 align:start position:0%
augmented image captioning as well in
this<00:05:13.759><c> notebook</c><00:05:14.320><c> so</c><00:05:14.560><c> let's</c><00:05:14.759><c> get</c><00:05:14.919><c> started</c><00:05:15.240><c> with</c>

00:05:15.350 --> 00:05:15.360 align:start position:0%
this notebook so let's get started with
 

00:05:15.360 --> 00:05:20.629 align:start position:0%
this notebook so let's get started with
it<00:05:16.320><c> so</c><00:05:16.880><c> we</c><00:05:17.039><c> have</c><00:05:17.680><c> the</c><00:05:18.400><c> model</c><00:05:18.759><c> loel</c><00:05:19.759><c> multimodel</c>

00:05:20.629 --> 00:05:20.639 align:start position:0%
it so we have the model loel multimodel
 

00:05:20.639 --> 00:05:22.909 align:start position:0%
it so we have the model loel multimodel
class<00:05:20.960><c> will</c><00:05:21.120><c> help</c><00:05:21.319><c> you</c><00:05:21.400><c> to</c><00:05:21.560><c> loow</c><00:05:21.800><c> the</c><00:05:21.880><c> model</c><00:05:22.520><c> um</c>

00:05:22.909 --> 00:05:22.919 align:start position:0%
class will help you to loow the model um
 

00:05:22.919 --> 00:05:24.230 align:start position:0%
class will help you to loow the model um
so<00:05:23.160><c> there</c><00:05:23.360><c> as</c><00:05:23.520><c> said</c><00:05:23.720><c> there</c><00:05:23.800><c> are</c><00:05:23.960><c> different</c>

00:05:24.230 --> 00:05:24.240 align:start position:0%
so there as said there are different
 

00:05:24.240 --> 00:05:27.189 align:start position:0%
so there as said there are different
variations<00:05:24.600><c> of</c><00:05:24.800><c> lava</c><00:05:25.080><c> model</c><00:05:25.400><c> rights</c><00:05:26.080><c> right</c><00:05:26.319><c> so</c>

00:05:27.189 --> 00:05:27.199 align:start position:0%
variations of lava model rights right so
 

00:05:27.199 --> 00:05:29.309 align:start position:0%
variations of lava model rights right so
we'll<00:05:27.400><c> use</c><00:05:27.600><c> 13</c><00:05:27.880><c> million</c><00:05:28.240><c> parameter</c><00:05:29.000><c> model</c>

00:05:29.309 --> 00:05:29.319 align:start position:0%
we'll use 13 million parameter model
 

00:05:29.319 --> 00:05:30.309 align:start position:0%
we'll use 13 million parameter model
here

00:05:30.309 --> 00:05:30.319 align:start position:0%
here
 

00:05:30.319 --> 00:05:32.950 align:start position:0%
here
and<00:05:30.520><c> then</c><00:05:30.720><c> all</c><00:05:30.880><c> you</c><00:05:31.000><c> need</c><00:05:31.199><c> to</c><00:05:31.360><c> do</c><00:05:31.840><c> is</c><00:05:32.520><c> uh</c><00:05:32.639><c> load</c>

00:05:32.950 --> 00:05:32.960 align:start position:0%
and then all you need to do is uh load
 

00:05:32.960 --> 00:05:33.629 align:start position:0%
and then all you need to do is uh load
the

00:05:33.629 --> 00:05:33.639 align:start position:0%
the
 

00:05:33.639 --> 00:05:36.550 align:start position:0%
the
image<00:05:34.639><c> you</c><00:05:34.800><c> can</c><00:05:35.000><c> download</c><00:05:35.400><c> it</c><00:05:35.600><c> and</c><00:05:35.960><c> load</c><00:05:36.319><c> load</c>

00:05:36.550 --> 00:05:36.560 align:start position:0%
image you can download it and load load
 

00:05:36.560 --> 00:05:39.230 align:start position:0%
image you can download it and load load
it<00:05:36.759><c> so</c><00:05:37.039><c> here</c><00:05:37.240><c> we</c><00:05:37.319><c> are</c><00:05:38.000><c> showcasing</c><00:05:38.639><c> some</c><00:05:39.039><c> uh</c>

00:05:39.230 --> 00:05:39.240 align:start position:0%
it so here we are showcasing some uh
 

00:05:39.240 --> 00:05:42.110 align:start position:0%
it so here we are showcasing some uh
image<00:05:40.199><c> um</c><00:05:40.759><c> of</c><00:05:41.000><c> eight</c><00:05:41.280><c> wings</c><00:05:41.600><c> or</c><00:05:41.800><c> chicken</c>

00:05:42.110 --> 00:05:42.120 align:start position:0%
image um of eight wings or chicken
 

00:05:42.120 --> 00:05:44.350 align:start position:0%
image um of eight wings or chicken
poppers<00:05:42.840><c> and</c><00:05:43.080><c> their</c><00:05:43.360><c> price</c><00:05:43.840><c> and</c><00:05:44.039><c> various</c>

00:05:44.350 --> 00:05:44.360 align:start position:0%
poppers and their price and various
 

00:05:44.360 --> 00:05:47.070 align:start position:0%
poppers and their price and various
other<00:05:44.600><c> things</c><00:05:44.880><c> present</c><00:05:45.160><c> in</c><00:05:45.319><c> this</c><00:05:45.840><c> image</c><00:05:46.840><c> so</c>

00:05:47.070 --> 00:05:47.080 align:start position:0%
other things present in this image so
 

00:05:47.080 --> 00:05:49.590 align:start position:0%
other things present in this image so
all<00:05:47.199><c> you</c><00:05:47.319><c> need</c><00:05:47.440><c> to</c><00:05:47.600><c> do</c><00:05:47.759><c> is</c><00:05:48.080><c> uh</c><00:05:48.199><c> to</c><00:05:48.560><c> get</c><00:05:48.840><c> some</c>

00:05:49.590 --> 00:05:49.600 align:start position:0%
all you need to do is uh to get some
 

00:05:49.600 --> 00:05:51.710 align:start position:0%
all you need to do is uh to get some
structured<00:05:50.160><c> extraction</c><00:05:50.639><c> you</c><00:05:50.720><c> need</c><00:05:50.880><c> to</c><00:05:51.000><c> define</c>

00:05:51.710 --> 00:05:51.720 align:start position:0%
structured extraction you need to define
 

00:05:51.720 --> 00:05:55.749 align:start position:0%
structured extraction you need to define
a<00:05:52.720><c> identic</c><00:05:53.639><c> class</c><00:05:54.600><c> here</c><00:05:54.759><c> we</c><00:05:54.880><c> are</c><00:05:55.280><c> defining</c><00:05:55.639><c> it</c>

00:05:55.749 --> 00:05:55.759 align:start position:0%
a identic class here we are defining it
 

00:05:55.759 --> 00:05:58.950 align:start position:0%
a identic class here we are defining it
as<00:05:56.160><c> restaurant</c><00:05:57.080><c> uh</c><00:05:57.240><c> so</c><00:05:57.560><c> this</c><00:05:57.680><c> will</c><00:05:58.560><c> extract</c>

00:05:58.950 --> 00:05:58.960 align:start position:0%
as restaurant uh so this will extract
 

00:05:58.960 --> 00:06:00.430 align:start position:0%
as restaurant uh so this will extract
all<00:05:59.080><c> this</c><00:05:59.240><c> inform</c><00:05:59.479><c> information</c><00:06:00.080><c> like</c>

00:06:00.430 --> 00:06:00.440 align:start position:0%
all this inform information like
 

00:06:00.440 --> 00:06:03.950 align:start position:0%
all this inform information like
restorant<00:06:01.080><c> food</c><00:06:02.080><c> discount</c><00:06:03.080><c> price</c><00:06:03.440><c> rating</c><00:06:03.800><c> and</c>

00:06:03.950 --> 00:06:03.960 align:start position:0%
restorant food discount price rating and
 

00:06:03.960 --> 00:06:06.790 align:start position:0%
restorant food discount price rating and
review<00:06:04.319><c> whatever</c><00:06:04.639><c> is</c><00:06:04.840><c> present</c><00:06:05.240><c> in</c><00:06:05.360><c> the</c><00:06:05.800><c> image</c>

00:06:06.790 --> 00:06:06.800 align:start position:0%
review whatever is present in the image
 

00:06:06.800 --> 00:06:09.230 align:start position:0%
review whatever is present in the image
so<00:06:07.520><c> and</c><00:06:07.720><c> then</c><00:06:07.880><c> you</c><00:06:08.240><c> basically</c><00:06:08.639><c> Define</c><00:06:08.919><c> a</c>

00:06:09.230 --> 00:06:09.240 align:start position:0%
so and then you basically Define a
 

00:06:09.240 --> 00:06:11.309 align:start position:0%
so and then you basically Define a
prompt<00:06:10.240><c> uh</c>

00:06:11.309 --> 00:06:11.319 align:start position:0%
prompt uh
 

00:06:11.319 --> 00:06:17.550 align:start position:0%
prompt uh
of<00:06:12.800><c> uh</c><00:06:13.800><c> what</c><00:06:13.960><c> is</c><00:06:14.240><c> to</c><00:06:14.400><c> be</c><00:06:15.199><c> passed</c><00:06:16.199><c> for</c><00:06:16.479><c> the</c><00:06:17.080><c> U</c>

00:06:17.550 --> 00:06:17.560 align:start position:0%
of uh what is to be passed for the U
 

00:06:17.560 --> 00:06:20.790 align:start position:0%
of uh what is to be passed for the U
model<00:06:18.560><c> and</c><00:06:18.840><c> then</c><00:06:19.520><c> and</c><00:06:19.680><c> then</c><00:06:19.880><c> the</c><00:06:20.319><c> penting</c>

00:06:20.790 --> 00:06:20.800 align:start position:0%
model and then and then the penting
 

00:06:20.800 --> 00:06:22.350 align:start position:0%
model and then and then the penting
output<00:06:21.160><c> parser</c><00:06:21.560><c> which</c><00:06:21.639><c> is</c><00:06:21.759><c> the</c><00:06:21.880><c> resturant</c>

00:06:22.350 --> 00:06:22.360 align:start position:0%
output parser which is the resturant
 

00:06:22.360 --> 00:06:25.950 align:start position:0%
output parser which is the resturant
class<00:06:22.639><c> we</c><00:06:22.759><c> have</c><00:06:22.919><c> defined</c><00:06:23.280><c> and</c><00:06:23.599><c> then</c><00:06:24.599><c> um</c><00:06:25.319><c> the</c>

00:06:25.950 --> 00:06:25.960 align:start position:0%
class we have defined and then um the
 

00:06:25.960 --> 00:06:28.589 align:start position:0%
class we have defined and then um the
doc<00:06:26.240><c> I</c><00:06:26.319><c> mean</c><00:06:26.599><c> the</c><00:06:26.720><c> image</c><00:06:27.039><c> documents</c><00:06:28.039><c> and</c><00:06:28.319><c> the</c>

00:06:28.589 --> 00:06:28.599 align:start position:0%
doc I mean the image documents and the
 

00:06:28.599 --> 00:06:32.550 align:start position:0%
doc I mean the image documents and the
prompt<00:06:29.720><c> and</c><00:06:29.880><c> the</c><00:06:30.000><c> model</c><00:06:30.919><c> and</c><00:06:31.919><c> just</c><00:06:32.199><c> Define</c>

00:06:32.550 --> 00:06:32.560 align:start position:0%
prompt and the model and just Define
 

00:06:32.560 --> 00:06:35.870 align:start position:0%
prompt and the model and just Define
this<00:06:32.759><c> program</c><00:06:33.319><c> and</c><00:06:33.520><c> then</c><00:06:34.080><c> can</c><00:06:34.360><c> pass</c><00:06:34.599><c> the</c><00:06:34.880><c> query</c>

00:06:35.870 --> 00:06:35.880 align:start position:0%
this program and then can pass the query
 

00:06:35.880 --> 00:06:37.950 align:start position:0%
this program and then can pass the query
here<00:06:36.160><c> it</c><00:06:36.280><c> is</c><00:06:36.639><c> can</c><00:06:36.759><c> you</c><00:06:36.840><c> summarize</c><00:06:37.280><c> what</c><00:06:37.360><c> is</c><00:06:37.520><c> in</c>

00:06:37.950 --> 00:06:37.960 align:start position:0%
here it is can you summarize what is in
 

00:06:37.960 --> 00:06:41.150 align:start position:0%
here it is can you summarize what is in
this<00:06:38.400><c> image</c><00:06:39.400><c> and</c><00:06:39.560><c> then</c><00:06:39.759><c> accordingly</c><00:06:40.280><c> it</c><00:06:40.400><c> will</c>

00:06:41.150 --> 00:06:41.160 align:start position:0%
this image and then accordingly it will
 

00:06:41.160 --> 00:06:44.550 align:start position:0%
this image and then accordingly it will
generate<00:06:41.919><c> the</c><00:06:42.360><c> structur</c><00:06:43.360><c> uh</c><00:06:43.919><c> data</c><00:06:44.280><c> in</c><00:06:44.400><c> the</c>

00:06:44.550 --> 00:06:44.560 align:start position:0%
generate the structur uh data in the
 

00:06:44.560 --> 00:06:46.390 align:start position:0%
generate the structur uh data in the
data<00:06:44.960><c> whatever</c><00:06:45.240><c> is</c>

00:06:46.390 --> 00:06:46.400 align:start position:0%
data whatever is
 

00:06:46.400 --> 00:06:49.270 align:start position:0%
data whatever is
present<00:06:47.400><c> so</c><00:06:48.120><c> yeah</c><00:06:48.560><c> uh</c><00:06:48.759><c> it</c><00:06:48.960><c> correctly</c>

00:06:49.270 --> 00:06:49.280 align:start position:0%
present so yeah uh it correctly
 

00:06:49.280 --> 00:06:52.749 align:start position:0%
present so yeah uh it correctly
generated<00:06:49.720><c> what</c><00:06:49.840><c> is</c><00:06:49.919><c> a</c><00:06:50.080><c> food</c><00:06:50.720><c> and</c><00:06:50.960><c> then</c><00:06:51.759><c> uh</c>

00:06:52.749 --> 00:06:52.759 align:start position:0%
generated what is a food and then uh
 

00:06:52.759 --> 00:06:54.870 align:start position:0%
generated what is a food and then uh
probably<00:06:53.039><c> not</c><00:06:53.440><c> something</c><00:06:54.039><c> on</c><00:06:54.199><c> the</c><00:06:54.360><c> resturant</c>

00:06:54.870 --> 00:06:54.880 align:start position:0%
probably not something on the resturant
 

00:06:54.880 --> 00:06:58.029 align:start position:0%
probably not something on the resturant
name<00:06:55.560><c> and</c><00:06:56.240><c> yeah</c><00:06:56.639><c> so</c><00:06:56.960><c> various</c><00:06:57.280><c> other</c>

00:06:58.029 --> 00:06:58.039 align:start position:0%
name and yeah so various other
 

00:06:58.039 --> 00:07:00.589 align:start position:0%
name and yeah so various other
things<00:06:59.039><c> so</c><00:06:59.400><c> that</c><00:06:59.520><c> is</c><00:06:59.680><c> one</c><00:06:59.960><c> the</c><00:07:00.080><c> first</c><00:07:00.280><c> part</c><00:07:00.520><c> the</c>

00:07:00.589 --> 00:07:00.599 align:start position:0%
things so that is one the first part the
 

00:07:00.599 --> 00:07:03.230 align:start position:0%
things so that is one the first part the
second<00:07:00.879><c> part</c><00:07:01.080><c> is</c><00:07:01.440><c> retal</c><00:07:01.879><c> augmented</c><00:07:02.319><c> image</c>

00:07:03.230 --> 00:07:03.240 align:start position:0%
second part is retal augmented image
 

00:07:03.240 --> 00:07:04.589 align:start position:0%
second part is retal augmented image
captioning<00:07:03.720><c> which</c><00:07:03.840><c> we</c><00:07:03.919><c> will</c><00:07:04.039><c> not</c><00:07:04.199><c> be</c><00:07:04.319><c> looking</c>

00:07:04.589 --> 00:07:04.599 align:start position:0%
captioning which we will not be looking
 

00:07:04.599 --> 00:07:08.110 align:start position:0%
captioning which we will not be looking
into<00:07:04.879><c> it</c><00:07:05.720><c> and</c><00:07:06.199><c> uh</c><00:07:06.759><c> yeah</c><00:07:07.120><c> the</c><00:07:07.240><c> other</c><00:07:07.479><c> one</c><00:07:07.680><c> is</c>

00:07:08.110 --> 00:07:08.120 align:start position:0%
into it and uh yeah the other one is
 

00:07:08.120 --> 00:07:09.390 align:start position:0%
into it and uh yeah the other one is
multimodel

00:07:09.390 --> 00:07:09.400 align:start position:0%
multimodel
 

00:07:09.400 --> 00:07:13.670 align:start position:0%
multimodel
rag<00:07:10.400><c> so</c><00:07:10.759><c> here</c><00:07:11.280><c> uh</c><00:07:11.440><c> we'll</c><00:07:11.840><c> download</c><00:07:12.400><c> some</c><00:07:12.680><c> data</c>

00:07:13.670 --> 00:07:13.680 align:start position:0%
rag so here uh we'll download some data
 

00:07:13.680 --> 00:07:16.550 align:start position:0%
rag so here uh we'll download some data
of<00:07:14.240><c> cars</c><00:07:14.639><c> and</c><00:07:14.759><c> the</c><00:07:15.080><c> descriptions</c><00:07:16.080><c> so</c><00:07:16.280><c> what</c><00:07:16.440><c> we</c>

00:07:16.550 --> 00:07:16.560 align:start position:0%
of cars and the descriptions so what we
 

00:07:16.560 --> 00:07:20.510 align:start position:0%
of cars and the descriptions so what we
do<00:07:16.800><c> is</c><00:07:17.240><c> uh</c><00:07:17.440><c> we</c><00:07:18.039><c> embed</c><00:07:18.599><c> both</c><00:07:19.560><c> text</c><00:07:20.080><c> as</c><00:07:20.199><c> well</c><00:07:20.319><c> as</c>

00:07:20.510 --> 00:07:20.520 align:start position:0%
do is uh we embed both text as well as
 

00:07:20.520 --> 00:07:22.629 align:start position:0%
do is uh we embed both text as well as
images<00:07:20.960><c> using</c><00:07:21.240><c> clip</c><00:07:21.520><c> embeddings</c><00:07:22.360><c> which</c><00:07:22.479><c> is</c>

00:07:22.629 --> 00:07:22.639 align:start position:0%
images using clip embeddings which is
 

00:07:22.639 --> 00:07:25.469 align:start position:0%
images using clip embeddings which is
totally<00:07:23.000><c> done</c><00:07:23.440><c> locally</c><00:07:24.440><c> uh</c><00:07:24.560><c> we'll</c><00:07:24.800><c> store</c><00:07:25.280><c> both</c>

00:07:25.469 --> 00:07:25.479 align:start position:0%
totally done locally uh we'll store both
 

00:07:25.479 --> 00:07:29.589 align:start position:0%
totally done locally uh we'll store both
of<00:07:25.720><c> them</c><00:07:26.240><c> in</c><00:07:26.720><c> uh</c><00:07:27.400><c> quadrant</c><00:07:28.080><c> client</c><00:07:29.080><c> and</c><00:07:29.400><c> and</c>

00:07:29.589 --> 00:07:29.599 align:start position:0%
of them in uh quadrant client and and
 

00:07:29.599 --> 00:07:33.270 align:start position:0%
of them in uh quadrant client and and
we'll<00:07:29.879><c> have</c><00:07:30.080><c> them</c><00:07:30.319><c> in</c><00:07:31.280><c> uh</c><00:07:32.280><c> text</c><00:07:32.599><c> collection</c><00:07:33.160><c> as</c>

00:07:33.270 --> 00:07:33.280 align:start position:0%
we'll have them in uh text collection as
 

00:07:33.280 --> 00:07:35.550 align:start position:0%
we'll have them in uh text collection as
well<00:07:33.440><c> as</c><00:07:33.639><c> image</c><00:07:33.919><c> collection</c><00:07:34.560><c> so</c><00:07:35.120><c> both</c><00:07:35.319><c> text</c>

00:07:35.550 --> 00:07:35.560 align:start position:0%
well as image collection so both text
 

00:07:35.560 --> 00:07:37.270 align:start position:0%
well as image collection so both text
and<00:07:35.720><c> images</c><00:07:36.039><c> are</c><00:07:36.160><c> stored</c><00:07:36.440><c> in</c><00:07:36.599><c> separate</c>

00:07:37.270 --> 00:07:37.280 align:start position:0%
and images are stored in separate
 

00:07:37.280 --> 00:07:39.629 align:start position:0%
and images are stored in separate
collections<00:07:38.280><c> so</c><00:07:38.680><c> we'll</c><00:07:38.960><c> Define</c><00:07:39.240><c> the</c><00:07:39.360><c> image</c>

00:07:39.629 --> 00:07:39.639 align:start position:0%
collections so we'll Define the image
 

00:07:39.639 --> 00:07:43.230 align:start position:0%
collections so we'll Define the image
embedding<00:07:40.280><c> model</c><00:07:41.280><c> and</c><00:07:41.560><c> then</c><00:07:42.000><c> you</c><00:07:42.120><c> can</c><00:07:42.400><c> index</c>

00:07:43.230 --> 00:07:43.240 align:start position:0%
embedding model and then you can index
 

00:07:43.240 --> 00:07:46.390 align:start position:0%
embedding model and then you can index
them<00:07:44.240><c> load</c><00:07:44.520><c> the</c><00:07:44.720><c> documents</c><00:07:45.520><c> as</c><00:07:45.639><c> well</c><00:07:45.840><c> as</c><00:07:46.000><c> index</c>

00:07:46.390 --> 00:07:46.400 align:start position:0%
them load the documents as well as index
 

00:07:46.400 --> 00:07:48.869 align:start position:0%
them load the documents as well as index
ZM<00:07:46.639><c> by</c><00:07:46.879><c> providing</c><00:07:47.360><c> all</c><00:07:47.599><c> the</c><00:07:47.840><c> so</c><00:07:48.520><c> what</c><00:07:48.639><c> is</c><00:07:48.720><c> a</c>

00:07:48.869 --> 00:07:48.879 align:start position:0%
ZM by providing all the so what is a
 

00:07:48.879 --> 00:07:50.029 align:start position:0%
ZM by providing all the so what is a
text<00:07:49.080><c> store</c><00:07:49.280><c> and</c><00:07:49.400><c> Image</c><00:07:49.639><c> store</c><00:07:49.879><c> in</c><00:07:49.960><c> the</c>

00:07:50.029 --> 00:07:50.039 align:start position:0%
text store and Image store in the
 

00:07:50.039 --> 00:07:54.110 align:start position:0%
text store and Image store in the
storage<00:07:50.720><c> context</c><00:07:51.720><c> and</c><00:07:52.560><c> index</c><00:07:53.000><c> ZM</c><00:07:53.800><c> and</c><00:07:53.960><c> then</c>

00:07:54.110 --> 00:07:54.120 align:start position:0%
storage context and index ZM and then
 

00:07:54.120 --> 00:07:56.430 align:start position:0%
storage context and index ZM and then
you<00:07:54.240><c> can</c><00:07:54.560><c> start</c><00:07:54.919><c> querying</c><00:07:55.400><c> it</c><00:07:56.039><c> uh</c><00:07:56.120><c> in</c><00:07:56.240><c> the</c>

00:07:56.430 --> 00:07:56.440 align:start position:0%
you can start querying it uh in the
 

00:07:56.440 --> 00:07:59.070 align:start position:0%
you can start querying it uh in the
usual<00:07:56.840><c> way</c><00:07:57.360><c> you</c><00:07:57.520><c> define</c><00:07:57.800><c> a</c><00:07:57.960><c> prompt</c><00:07:58.360><c> as</c><00:07:58.520><c> well</c>

00:07:59.070 --> 00:07:59.080 align:start position:0%
usual way you define a prompt as well
 

00:07:59.080 --> 00:08:01.749 align:start position:0%
usual way you define a prompt as well
and<00:07:59.360><c> then</c><00:07:59.479><c> you</c><00:07:59.599><c> define</c><00:08:00.000><c> both</c><00:08:00.280><c> the</c><00:08:00.919><c> uh</c><00:08:01.319><c> whatever</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
and then you define both the uh whatever
 

00:08:01.759 --> 00:08:03.670 align:start position:0%
and then you define both the uh whatever
the<00:08:01.879><c> model</c><00:08:02.120><c> is</c><00:08:02.599><c> we</c><00:08:02.680><c> are</c><00:08:02.800><c> using</c><00:08:03.120><c> Lama</c><00:08:03.400><c> 13</c>

00:08:03.670 --> 00:08:03.680 align:start position:0%
the model is we are using Lama 13
 

00:08:03.680 --> 00:08:07.270 align:start position:0%
the model is we are using Lama 13
billion<00:08:04.159><c> parameter</c><00:08:04.599><c> model</c><00:08:05.240><c> and</c><00:08:05.440><c> then</c><00:08:06.280><c> the</c>

00:08:07.270 --> 00:08:07.280 align:start position:0%
billion parameter model and then the
 

00:08:07.280 --> 00:08:08.189 align:start position:0%
billion parameter model and then the
prompt

00:08:08.189 --> 00:08:08.199 align:start position:0%
prompt
 

00:08:08.199 --> 00:08:09.869 align:start position:0%
prompt
here

00:08:09.869 --> 00:08:09.879 align:start position:0%
here
 

00:08:09.879 --> 00:08:13.390 align:start position:0%
here
so<00:08:10.879><c> you</c><00:08:11.039><c> can</c><00:08:11.560><c> start</c><00:08:12.199><c> quering</c><00:08:12.840><c> so</c><00:08:13.120><c> something</c>

00:08:13.390 --> 00:08:13.400 align:start position:0%
so you can start quering so something
 

00:08:13.400 --> 00:08:16.909 align:start position:0%
so you can start quering so something
like<00:08:13.599><c> tell</c><00:08:13.759><c> me</c><00:08:13.919><c> more</c><00:08:14.199><c> about</c><00:08:14.520><c> Po</c><00:08:15.520><c> and</c><00:08:15.720><c> then</c><00:08:16.319><c> um</c>

00:08:16.909 --> 00:08:16.919 align:start position:0%
like tell me more about Po and then um
 

00:08:16.919 --> 00:08:21.550 align:start position:0%
like tell me more about Po and then um
it<00:08:17.199><c> will</c><00:08:18.199><c> generate</c><00:08:18.560><c> a</c><00:08:19.199><c> response</c><00:08:20.240><c> so</c><00:08:21.240><c> you</c><00:08:21.360><c> can</c>

00:08:21.550 --> 00:08:21.560 align:start position:0%
it will generate a response so you can
 

00:08:21.560 --> 00:08:24.510 align:start position:0%
it will generate a response so you can
even<00:08:21.759><c> check</c><00:08:22.000><c> the</c><00:08:22.199><c> what</c><00:08:22.560><c> the</c><00:08:23.039><c> nodes</c><00:08:23.639><c> The</c><00:08:24.039><c> Source</c>

00:08:24.510 --> 00:08:24.520 align:start position:0%
even check the what the nodes The Source
 

00:08:24.520 --> 00:08:27.270 align:start position:0%
even check the what the nodes The Source
information<00:08:25.000><c> context</c><00:08:25.360><c> it</c><00:08:25.560><c> has</c><00:08:25.840><c> used</c><00:08:26.840><c> so</c><00:08:27.120><c> which</c>

00:08:27.270 --> 00:08:27.280 align:start position:0%
information context it has used so which
 

00:08:27.280 --> 00:08:30.589 align:start position:0%
information context it has used so which
is<00:08:27.759><c> uh</c><00:08:27.960><c> text</c><00:08:28.479><c> some</c><00:08:28.759><c> text</c><00:08:29.440><c> and</c><00:08:29.639><c> then</c><00:08:29.879><c> as</c><00:08:30.000><c> well</c><00:08:30.159><c> as</c>

00:08:30.589 --> 00:08:30.599 align:start position:0%
is uh text some text and then as well as
 

00:08:30.599 --> 00:08:33.350 align:start position:0%
is uh text some text and then as well as
two<00:08:30.800><c> images</c><00:08:31.199><c> so</c><00:08:31.759><c> similarity</c><00:08:32.279><c> top</c><00:08:32.479><c> case</c><00:08:32.760><c> two</c><00:08:33.159><c> by</c>

00:08:33.350 --> 00:08:33.360 align:start position:0%
two images so similarity top case two by
 

00:08:33.360 --> 00:08:36.310 align:start position:0%
two images so similarity top case two by
default<00:08:33.760><c> and</c><00:08:33.880><c> it</c><00:08:34.120><c> retrieves</c><00:08:35.120><c> two</c><00:08:35.880><c> top</c><00:08:36.080><c> two</c>

00:08:36.310 --> 00:08:36.320 align:start position:0%
default and it retrieves two top two
 

00:08:36.320 --> 00:08:39.310 align:start position:0%
default and it retrieves two top two
text<00:08:36.640><c> parts</c><00:08:36.959><c> and</c><00:08:37.080><c> then</c><00:08:37.560><c> um</c><00:08:38.399><c> two</c>

00:08:39.310 --> 00:08:39.320 align:start position:0%
text parts and then um two
 

00:08:39.320 --> 00:08:43.750 align:start position:0%
text parts and then um two
images<00:08:40.320><c> so</c><00:08:40.839><c> all</c><00:08:41.120><c> this</c><00:08:41.399><c> is</c><00:08:41.560><c> run</c><00:08:42.399><c> uh</c><00:08:42.919><c> locally</c>

00:08:43.750 --> 00:08:43.760 align:start position:0%
images so all this is run uh locally
 

00:08:43.760 --> 00:08:45.430 align:start position:0%
images so all this is run uh locally
since<00:08:43.959><c> we</c><00:08:44.039><c> are</c><00:08:44.159><c> using</c><00:08:44.519><c> clip</c><00:08:44.800><c> embeddings</c><00:08:45.320><c> as</c>

00:08:45.430 --> 00:08:45.440 align:start position:0%
since we are using clip embeddings as
 

00:08:45.440 --> 00:08:46.670 align:start position:0%
since we are using clip embeddings as
well<00:08:45.600><c> as</c>

00:08:46.670 --> 00:08:46.680 align:start position:0%
well as
 

00:08:46.680 --> 00:08:49.670 align:start position:0%
well as
lava<00:08:47.680><c> model</c><00:08:48.279><c> which</c><00:08:48.440><c> is</c><00:08:48.680><c> which</c><00:08:48.800><c> runs</c><00:08:49.120><c> locally</c>

00:08:49.670 --> 00:08:49.680 align:start position:0%
lava model which is which runs locally
 

00:08:49.680 --> 00:08:52.990 align:start position:0%
lava model which is which runs locally
so<00:08:50.320><c> everything</c><00:08:50.680><c> is</c><00:08:50.800><c> run</c><00:08:51.399><c> locally</c><00:08:51.760><c> in</c><00:08:51.920><c> your</c><00:08:52.480><c> um</c>

00:08:52.990 --> 00:08:53.000 align:start position:0%
so everything is run locally in your um
 

00:08:53.000 --> 00:08:55.870 align:start position:0%
so everything is run locally in your um
laptop<00:08:53.519><c> or</c><00:08:53.720><c> wherever</c><00:08:54.160><c> you</c><00:08:54.279><c> want</c><00:08:54.399><c> to</c><00:08:54.519><c> run</c><00:08:54.720><c> it</c><00:08:55.640><c> so</c>

00:08:55.870 --> 00:08:55.880 align:start position:0%
laptop or wherever you want to run it so
 

00:08:55.880 --> 00:08:57.870 align:start position:0%
laptop or wherever you want to run it so
that's<00:08:56.040><c> how</c><00:08:56.160><c> you</c><00:08:56.279><c> can</c><00:08:56.399><c> build</c><00:08:56.839><c> an</c><00:08:57.399><c> interesting</c>

00:08:57.870 --> 00:08:57.880 align:start position:0%
that's how you can build an interesting
 

00:08:57.880 --> 00:09:00.150 align:start position:0%
that's how you can build an interesting
applications<00:08:58.720><c> with</c>

00:09:00.150 --> 00:09:00.160 align:start position:0%
applications with
 

00:09:00.160 --> 00:09:03.310 align:start position:0%
applications with
lava<00:09:01.160><c> and</c><00:09:01.360><c> then</c><00:09:01.800><c> using</c><00:09:02.079><c> Lama</c><00:09:02.399><c> index</c><00:09:02.880><c> some</c><00:09:03.040><c> rag</c>

00:09:03.310 --> 00:09:03.320 align:start position:0%
lava and then using Lama index some rag
 

00:09:03.320 --> 00:09:05.550 align:start position:0%
lava and then using Lama index some rag
applications<00:09:03.839><c> as</c><00:09:03.959><c> well</c><00:09:04.200><c> and</c><00:09:04.560><c> structure</c><00:09:05.079><c> data</c>

00:09:05.550 --> 00:09:05.560 align:start position:0%
applications as well and structure data
 

00:09:05.560 --> 00:09:08.509 align:start position:0%
applications as well and structure data
extraction<00:09:06.079><c> as</c><00:09:06.200><c> well</c><00:09:07.040><c> so</c><00:09:07.720><c> I</c><00:09:07.880><c> hope</c><00:09:08.040><c> you</c><00:09:08.200><c> all</c>

00:09:08.509 --> 00:09:08.519 align:start position:0%
extraction as well so I hope you all
 

00:09:08.519 --> 00:09:11.509 align:start position:0%
extraction as well so I hope you all
liked<00:09:08.839><c> it</c><00:09:09.440><c> uh</c><00:09:09.600><c> please</c><00:09:09.800><c> do</c><00:09:10.040><c> experiment</c><00:09:10.760><c> with</c>

00:09:11.509 --> 00:09:11.519 align:start position:0%
liked it uh please do experiment with
 

00:09:11.519 --> 00:09:13.670 align:start position:0%
liked it uh please do experiment with
different<00:09:11.839><c> applications</c><00:09:12.360><c> with</c><00:09:13.160><c> multimodel</c>

00:09:13.670 --> 00:09:13.680 align:start position:0%
different applications with multimodel
 

00:09:13.680 --> 00:09:16.269 align:start position:0%
different applications with multimodel
stuff<00:09:13.959><c> and</c><00:09:14.079><c> llama</c><00:09:14.360><c> index</c><00:09:15.160><c> and</c><00:09:15.320><c> share</c><00:09:15.640><c> with</c><00:09:15.800><c> us</c>

00:09:16.269 --> 00:09:16.279 align:start position:0%
stuff and llama index and share with us
 

00:09:16.279 --> 00:09:19.710 align:start position:0%
stuff and llama index and share with us
uh<00:09:16.640><c> we'll</c><00:09:17.279><c> we</c><00:09:17.360><c> are</c><00:09:17.560><c> happy</c><00:09:17.800><c> to</c><00:09:18.399><c> Showcase</c><00:09:18.920><c> it</c><00:09:19.519><c> on</c>

00:09:19.710 --> 00:09:19.720 align:start position:0%
uh we'll we are happy to Showcase it on
 

00:09:19.720 --> 00:09:22.790 align:start position:0%
uh we'll we are happy to Showcase it on
our<00:09:20.079><c> handles</c><00:09:21.079><c> uh</c><00:09:21.200><c> see</c><00:09:21.399><c> you</c><00:09:21.519><c> in</c><00:09:21.640><c> the</c><00:09:21.760><c> next</c><00:09:22.000><c> video</c>

00:09:22.790 --> 00:09:22.800 align:start position:0%
our handles uh see you in the next video
 

00:09:22.800 --> 00:09:26.399 align:start position:0%
our handles uh see you in the next video
thank<00:09:23.399><c> you</c>

