WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.450 align:start position:0%
 
hey<00:00:00.719><c> there</c><00:00:00.960><c> it's</c><00:00:01.380><c> Matt</c><00:00:01.680><c> <PERSON></c><00:00:01.979><c> your</c>

00:00:02.450 --> 00:00:02.460 align:start position:0%
hey there it's <PERSON> your
 

00:00:02.460 --> 00:00:04.070 align:start position:0%
hey there it's <PERSON> your
favorite<00:00:02.580><c> evangelist</c><00:00:03.179><c> and</c><00:00:03.360><c> this</c><00:00:03.540><c> time</c><00:00:03.659><c> I</c><00:00:03.959><c> want</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
favorite evangelist and this time I want
 

00:00:04.080 --> 00:00:05.930 align:start position:0%
favorite evangelist and this time I want
to<00:00:04.200><c> do</c><00:00:04.380><c> an</c><00:00:04.620><c> update</c><00:00:04.860><c> to</c><00:00:05.100><c> a</c><00:00:05.220><c> video</c><00:00:05.339><c> that</c><00:00:05.640><c> I</c><00:00:05.819><c> made</c>

00:00:05.930 --> 00:00:05.940 align:start position:0%
to do an update to a video that I made
 

00:00:05.940 --> 00:00:08.450 align:start position:0%
to do an update to a video that I made
over<00:00:06.180><c> the</c><00:00:06.420><c> weekend</c><00:00:06.540><c> and</c><00:00:07.440><c> that</c><00:00:07.680><c> video</c><00:00:07.859><c> was</c><00:00:08.220><c> all</c>

00:00:08.450 --> 00:00:08.460 align:start position:0%
over the weekend and that video was all
 

00:00:08.460 --> 00:00:10.669 align:start position:0%
over the weekend and that video was all
about<00:00:08.580><c> how</c><00:00:09.059><c> easy</c><00:00:09.179><c> it</c><00:00:09.480><c> is</c><00:00:09.599><c> to</c><00:00:09.840><c> get</c><00:00:09.960><c> started</c><00:00:10.320><c> with</c>

00:00:10.669 --> 00:00:10.679 align:start position:0%
about how easy it is to get started with
 

00:00:10.679 --> 00:00:13.490 align:start position:0%
about how easy it is to get started with
olama<00:00:11.160><c> I</c><00:00:11.760><c> did</c><00:00:12.120><c> it</c><00:00:12.240><c> in</c><00:00:12.420><c> three</c><00:00:12.599><c> easy</c><00:00:12.840><c> steps</c><00:00:13.259><c> it</c>

00:00:13.490 --> 00:00:13.500 align:start position:0%
olama I did it in three easy steps it
 

00:00:13.500 --> 00:00:16.250 align:start position:0%
olama I did it in three easy steps it
was<00:00:13.620><c> download</c><00:00:13.860><c> the</c><00:00:14.400><c> executable</c><00:00:15.000><c> from</c><00:00:15.360><c> olamoda</c>

00:00:16.250 --> 00:00:16.260 align:start position:0%
was download the executable from olamoda
 

00:00:16.260 --> 00:00:18.830 align:start position:0%
was download the executable from olamoda
Ai<00:00:16.619><c> and</c><00:00:17.100><c> you</c><00:00:17.340><c> unzip</c><00:00:17.760><c> it</c><00:00:17.880><c> and</c><00:00:18.119><c> you</c><00:00:18.480><c> run</c><00:00:18.600><c> it</c>

00:00:18.830 --> 00:00:18.840 align:start position:0%
Ai and you unzip it and you run it
 

00:00:18.840 --> 00:00:20.810 align:start position:0%
Ai and you unzip it and you run it
copies<00:00:19.560><c> over</c><00:00:19.859><c> automatically</c><00:00:20.400><c> to</c><00:00:20.640><c> the</c>

00:00:20.810 --> 00:00:20.820 align:start position:0%
copies over automatically to the
 

00:00:20.820 --> 00:00:22.550 align:start position:0%
copies over automatically to the
applications<00:00:21.180><c> directory</c><00:00:21.720><c> but</c><00:00:22.020><c> then</c><00:00:22.260><c> you'd</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
applications directory but then you'd
 

00:00:22.560 --> 00:00:25.130 align:start position:0%
applications directory but then you'd
run<00:00:22.740><c> olama</c><00:00:23.460><c> run</c><00:00:23.939><c> and</c><00:00:24.480><c> then</c><00:00:24.600><c> the</c><00:00:24.779><c> name</c><00:00:24.900><c> of</c><00:00:25.019><c> the</c>

00:00:25.130 --> 00:00:25.140 align:start position:0%
run olama run and then the name of the
 

00:00:25.140 --> 00:00:26.750 align:start position:0%
run olama run and then the name of the
model<00:00:25.320><c> you</c><00:00:25.560><c> want</c><00:00:25.680><c> to</c><00:00:25.740><c> run</c><00:00:25.859><c> like</c><00:00:26.100><c> Orca</c><00:00:26.519><c> or</c>

00:00:26.750 --> 00:00:26.760 align:start position:0%
model you want to run like Orca or
 

00:00:26.760 --> 00:00:30.109 align:start position:0%
model you want to run like Orca or
vicuna<00:00:27.420><c> or</c><00:00:27.779><c> new</c><00:00:28.380><c> orme</c><00:00:28.920><c> and</c><00:00:29.519><c> then</c><00:00:29.640><c> you</c><00:00:29.939><c> will</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
vicuna or new orme and then you will
 

00:00:30.119 --> 00:00:32.930 align:start position:0%
vicuna or new orme and then you will
it'll<00:00:30.599><c> take</c><00:00:30.840><c> a</c><00:00:30.960><c> few</c><00:00:31.199><c> seconds</c><00:00:31.380><c> to</c><00:00:31.859><c> download</c><00:00:32.520><c> the</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
it'll take a few seconds to download the
 

00:00:32.940 --> 00:00:34.850 align:start position:0%
it'll take a few seconds to download the
model<00:00:33.059><c> and</c><00:00:33.840><c> then</c><00:00:33.960><c> it</c><00:00:34.200><c> drops</c><00:00:34.380><c> you</c><00:00:34.500><c> in</c><00:00:34.620><c> tools</c>

00:00:34.850 --> 00:00:34.860 align:start position:0%
model and then it drops you in tools
 

00:00:34.860 --> 00:00:36.889 align:start position:0%
model and then it drops you in tools
where<00:00:34.980><c> you</c><00:00:35.100><c> can</c><00:00:35.160><c> enter</c><00:00:35.399><c> your</c><00:00:35.940><c> prompt</c><00:00:36.300><c> and</c><00:00:36.719><c> you</c>

00:00:36.889 --> 00:00:36.899 align:start position:0%
where you can enter your prompt and you
 

00:00:36.899 --> 00:00:38.630 align:start position:0%
where you can enter your prompt and you
can<00:00:36.960><c> ask</c><00:00:37.140><c> a</c><00:00:37.320><c> question</c><00:00:37.500><c> and</c><00:00:37.800><c> it'll</c><00:00:38.160><c> spit</c><00:00:38.460><c> out</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
can ask a question and it'll spit out
 

00:00:38.640 --> 00:00:40.729 align:start position:0%
can ask a question and it'll spit out
the<00:00:38.820><c> answer</c><00:00:38.940><c> now</c><00:00:39.840><c> one</c><00:00:40.140><c> of</c><00:00:40.260><c> the</c><00:00:40.260><c> things</c><00:00:40.379><c> that</c>

00:00:40.729 --> 00:00:40.739 align:start position:0%
the answer now one of the things that
 

00:00:40.739 --> 00:00:42.889 align:start position:0%
the answer now one of the things that
people<00:00:41.100><c> are</c><00:00:41.399><c> very</c><00:00:41.760><c> curious</c><00:00:42.000><c> about</c><00:00:42.180><c> was</c><00:00:42.480><c> how</c><00:00:42.780><c> to</c>

00:00:42.889 --> 00:00:42.899 align:start position:0%
people are very curious about was how to
 

00:00:42.899 --> 00:00:45.229 align:start position:0%
people are very curious about was how to
run<00:00:43.020><c> it</c><00:00:43.200><c> with</c><00:00:43.440><c> llama2</c><00:00:44.219><c> now</c><00:00:44.760><c> if</c><00:00:44.879><c> you're</c><00:00:45.059><c> not</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
run it with llama2 now if you're not
 

00:00:45.239 --> 00:00:48.410 align:start position:0%
run it with llama2 now if you're not
familiar<00:00:45.540><c> with</c><00:00:45.660><c> llama</c><00:00:45.960><c> 2</c><00:00:46.260><c> llama</c><00:00:46.980><c> 2</c><00:00:47.280><c> is</c><00:00:47.879><c> the</c>

00:00:48.410 --> 00:00:48.420 align:start position:0%
familiar with llama 2 llama 2 is the
 

00:00:48.420 --> 00:00:51.830 align:start position:0%
familiar with llama 2 llama 2 is the
second<00:00:48.600><c> version</c><00:00:48.899><c> of</c><00:00:49.260><c> the</c><00:00:49.500><c> Llama</c><00:00:49.820><c> model</c><00:00:50.840><c> that</c>

00:00:51.830 --> 00:00:51.840 align:start position:0%
second version of the Llama model that
 

00:00:51.840 --> 00:00:54.590 align:start position:0%
second version of the Llama model that
you<00:00:52.079><c> know</c><00:00:52.200><c> kind</c><00:00:52.379><c> of</c><00:00:52.500><c> started</c><00:00:53.039><c> all</c><00:00:53.399><c> this</c><00:00:53.879><c> craze</c>

00:00:54.590 --> 00:00:54.600 align:start position:0%
you know kind of started all this craze
 

00:00:54.600 --> 00:00:58.130 align:start position:0%
you know kind of started all this craze
a<00:00:54.780><c> few</c><00:00:55.079><c> months</c><00:00:55.320><c> ago</c><00:00:55.559><c> it</c><00:00:56.399><c> comes</c><00:00:56.820><c> from</c><00:00:57.180><c> meta</c><00:00:57.840><c> you</c>

00:00:58.130 --> 00:00:58.140 align:start position:0%
a few months ago it comes from meta you
 

00:00:58.140 --> 00:01:00.369 align:start position:0%
a few months ago it comes from meta you
know<00:00:58.320><c> the</c><00:00:58.379><c> company</c><00:00:58.559><c> behind</c><00:00:59.160><c> Facebook</c>

00:01:00.369 --> 00:01:00.379 align:start position:0%
know the company behind Facebook
 

00:01:00.379 --> 00:01:03.770 align:start position:0%
know the company behind Facebook
and<00:01:01.379><c> llama</c><00:01:01.920><c> 2</c><00:01:02.219><c> is</c><00:01:02.520><c> this</c><00:01:02.879><c> new</c><00:01:03.059><c> model</c><00:01:03.300><c> and</c><00:01:03.600><c> people</c>

00:01:03.770 --> 00:01:03.780 align:start position:0%
and llama 2 is this new model and people
 

00:01:03.780 --> 00:01:05.810 align:start position:0%
and llama 2 is this new model and people
are<00:01:04.019><c> really</c><00:01:04.140><c> excited</c><00:01:04.559><c> about</c><00:01:04.619><c> it</c><00:01:04.860><c> so</c><00:01:05.100><c> how</c><00:01:05.700><c> easy</c>

00:01:05.810 --> 00:01:05.820 align:start position:0%
are really excited about it so how easy
 

00:01:05.820 --> 00:01:09.109 align:start position:0%
are really excited about it so how easy
is<00:01:06.060><c> it</c><00:01:06.240><c> to</c><00:01:06.420><c> get</c><00:01:06.540><c> started</c><00:01:06.840><c> with</c><00:01:07.200><c> llama</c><00:01:07.920><c> 2</c><00:01:08.159><c> on</c>

00:01:09.109 --> 00:01:09.119 align:start position:0%
is it to get started with llama 2 on
 

00:01:09.119 --> 00:01:11.810 align:start position:0%
is it to get started with llama 2 on
olama<00:01:09.780><c> so</c><00:01:10.260><c> all</c><00:01:10.439><c> I</c><00:01:10.560><c> have</c><00:01:10.680><c> to</c><00:01:10.799><c> do</c><00:01:10.920><c> I'm</c><00:01:11.340><c> here</c><00:01:11.520><c> at</c><00:01:11.700><c> a</c>

00:01:11.810 --> 00:01:11.820 align:start position:0%
olama so all I have to do I'm here at a
 

00:01:11.820 --> 00:01:13.490 align:start position:0%
olama so all I have to do I'm here at a
command<00:01:12.119><c> prompt</c><00:01:12.360><c> I'm</c><00:01:12.840><c> going</c><00:01:13.020><c> to</c><00:01:13.080><c> type</c><00:01:13.260><c> in</c>

00:01:13.490 --> 00:01:13.500 align:start position:0%
command prompt I'm going to type in
 

00:01:13.500 --> 00:01:14.990 align:start position:0%
command prompt I'm going to type in
olama

00:01:14.990 --> 00:01:15.000 align:start position:0%
olama
 

00:01:15.000 --> 00:01:17.929 align:start position:0%
olama
uh<00:01:15.659><c> run</c><00:01:16.080><c> llama2</c><00:01:16.920><c> as</c><00:01:17.280><c> you</c><00:01:17.400><c> can</c><00:01:17.520><c> see</c><00:01:17.580><c> I've</c><00:01:17.760><c> done</c>

00:01:17.929 --> 00:01:17.939 align:start position:0%
uh run llama2 as you can see I've done
 

00:01:17.939 --> 00:01:23.810 align:start position:0%
uh run llama2 as you can see I've done
this<00:01:18.060><c> a</c><00:01:18.180><c> few</c><00:01:18.299><c> times</c><00:01:18.479><c> and</c><00:01:19.320><c> then</c><00:01:19.439><c> I</c><00:01:19.619><c> can</c><00:01:19.740><c> say</c><00:01:19.920><c> hi</c>

00:01:23.810 --> 00:01:23.820 align:start position:0%
 
 

00:01:23.820 --> 00:01:25.550 align:start position:0%
 
and<00:01:24.180><c> it's</c><00:01:24.360><c> going</c><00:01:24.540><c> to</c><00:01:24.600><c> take</c><00:01:24.659><c> a</c><00:01:24.780><c> few</c><00:01:24.900><c> okay</c><00:01:25.320><c> there</c>

00:01:25.550 --> 00:01:25.560 align:start position:0%
and it's going to take a few okay there
 

00:01:25.560 --> 00:01:28.010 align:start position:0%
and it's going to take a few okay there
we<00:01:25.740><c> go</c><00:01:25.860><c> hello</c><00:01:26.340><c> how</c><00:01:26.580><c> can</c><00:01:26.939><c> I</c><00:01:27.060><c> assist</c><00:01:27.119><c> you</c><00:01:27.479><c> I've</c>

00:01:28.010 --> 00:01:28.020 align:start position:0%
we go hello how can I assist you I've
 

00:01:28.020 --> 00:01:30.350 align:start position:0%
we go hello how can I assist you I've
got<00:01:28.200><c> uh</c><00:01:28.560><c> olama</c><00:01:29.040><c> or</c><00:01:29.280><c> I've</c><00:01:29.400><c> got</c><00:01:29.520><c> llama</c><00:01:29.939><c> 2</c><00:01:30.119><c> running</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
got uh olama or I've got llama 2 running
 

00:01:30.360 --> 00:01:32.390 align:start position:0%
got uh olama or I've got llama 2 running
and<00:01:30.900><c> now</c><00:01:31.020><c> I</c><00:01:31.140><c> can</c><00:01:31.200><c> ask</c><00:01:31.380><c> you</c><00:01:31.500><c> a</c><00:01:31.680><c> question</c>

00:01:32.390 --> 00:01:32.400 align:start position:0%
and now I can ask you a question
 

00:01:32.400 --> 00:01:37.550 align:start position:0%
and now I can ask you a question
um<00:01:32.520><c> why</c><00:01:33.060><c> is</c><00:01:33.659><c> the</c><00:01:34.259><c> sky</c><00:01:34.680><c> blue</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
um why is the sky blue
 

00:01:37.560 --> 00:01:39.109 align:start position:0%
um why is the sky blue
we're<00:01:37.979><c> working</c><00:01:38.159><c> on</c><00:01:38.400><c> making</c><00:01:38.640><c> this</c><00:01:38.880><c> a</c><00:01:39.060><c> little</c>

00:01:39.109 --> 00:01:39.119 align:start position:0%
we're working on making this a little
 

00:01:39.119 --> 00:01:41.510 align:start position:0%
we're working on making this a little
bit<00:01:39.299><c> faster</c><00:01:39.680><c> and</c><00:01:40.680><c> that</c><00:01:40.920><c> should</c><00:01:41.040><c> be</c><00:01:41.159><c> coming</c><00:01:41.340><c> up</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
bit faster and that should be coming up
 

00:01:41.520 --> 00:01:43.249 align:start position:0%
bit faster and that should be coming up
that<00:01:41.820><c> update</c><00:01:42.060><c> should</c><00:01:42.299><c> be</c><00:01:42.420><c> coming</c><00:01:42.600><c> out</c><00:01:42.900><c> pretty</c>

00:01:43.249 --> 00:01:43.259 align:start position:0%
that update should be coming out pretty
 

00:01:43.259 --> 00:01:46.370 align:start position:0%
that update should be coming out pretty
soon<00:01:43.500><c> but</c><00:01:44.100><c> it's</c><00:01:44.220><c> already</c><00:01:44.400><c> pretty</c><00:01:44.759><c> fast</c>

00:01:46.370 --> 00:01:46.380 align:start position:0%
soon but it's already pretty fast
 

00:01:46.380 --> 00:01:49.429 align:start position:0%
soon but it's already pretty fast
so<00:01:46.799><c> we</c><00:01:46.979><c> got</c><00:01:47.159><c> a</c><00:01:47.280><c> nice</c><00:01:47.400><c> answer</c><00:01:47.759><c> here</c><00:01:48.600><c> I'm</c><00:01:49.320><c> going</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
so we got a nice answer here I'm going
 

00:01:49.439 --> 00:01:51.469 align:start position:0%
so we got a nice answer here I'm going
to<00:01:49.500><c> cancel</c><00:01:49.680><c> out</c><00:01:49.979><c> of</c><00:01:50.159><c> this</c><00:01:50.280><c> and</c><00:01:50.759><c> run</c><00:01:51.060><c> that</c><00:01:51.240><c> again</c>

00:01:51.469 --> 00:01:51.479 align:start position:0%
to cancel out of this and run that again
 

00:01:51.479 --> 00:01:54.350 align:start position:0%
to cancel out of this and run that again
but<00:01:51.780><c> with</c><00:01:52.020><c> that</c><00:01:52.200><c> verbose</c><00:01:52.680><c> option</c><00:01:53.220><c> now</c><00:01:53.880><c> verbose</c>

00:01:54.350 --> 00:01:54.360 align:start position:0%
but with that verbose option now verbose
 

00:01:54.360 --> 00:01:56.330 align:start position:0%
but with that verbose option now verbose
is<00:01:54.720><c> really</c><00:01:54.899><c> nice</c><00:01:55.140><c> if</c><00:01:55.380><c> you</c><00:01:55.500><c> want</c><00:01:55.619><c> to</c><00:01:55.740><c> know</c><00:01:55.920><c> how</c>

00:01:56.330 --> 00:01:56.340 align:start position:0%
is really nice if you want to know how
 

00:01:56.340 --> 00:01:58.310 align:start position:0%
is really nice if you want to know how
fast<00:01:56.579><c> is</c><00:01:56.880><c> this</c><00:01:57.060><c> model</c><00:01:57.240><c> running</c><00:01:57.540><c> how</c><00:01:58.079><c> long</c><00:01:58.200><c> does</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
fast is this model running how long does
 

00:01:58.320 --> 00:01:59.929 align:start position:0%
fast is this model running how long does
it<00:01:58.439><c> take</c><00:01:58.560><c> to</c><00:01:58.680><c> load</c><00:01:58.979><c> and</c><00:01:59.460><c> how</c><00:01:59.640><c> long</c><00:01:59.700><c> does</c><00:01:59.820><c> it</c>

00:01:59.929 --> 00:01:59.939 align:start position:0%
it take to load and how long does it
 

00:01:59.939 --> 00:02:02.270 align:start position:0%
it take to load and how long does it
take<00:02:00.060><c> to</c><00:02:00.240><c> process</c><00:02:00.540><c> the</c><00:02:00.899><c> the</c><00:02:01.140><c> answer</c><00:02:01.380><c> and</c><00:02:01.920><c> and</c>

00:02:02.270 --> 00:02:02.280 align:start position:0%
take to process the the answer and and
 

00:02:02.280 --> 00:02:04.370 align:start position:0%
take to process the the answer and and
eval<00:02:03.119><c> the</c><00:02:03.479><c> answer</c>

00:02:04.370 --> 00:02:04.380 align:start position:0%
eval the answer
 

00:02:04.380 --> 00:02:07.010 align:start position:0%
eval the answer
so<00:02:05.040><c> let's</c><00:02:05.579><c> go</c><00:02:05.820><c> with</c><00:02:06.060><c> let's</c><00:02:06.360><c> go</c><00:02:06.540><c> with</c><00:02:06.659><c> that</c><00:02:06.840><c> high</c>

00:02:07.010 --> 00:02:07.020 align:start position:0%
so let's go with let's go with that high
 

00:02:07.020 --> 00:02:10.130 align:start position:0%
so let's go with let's go with that high
again<00:02:07.259><c> because</c><00:02:07.920><c> I</c><00:02:08.759><c> know</c><00:02:08.880><c> I</c><00:02:09.060><c> like</c><00:02:09.239><c> to</c><00:02:09.420><c> say</c><00:02:09.780><c> hi</c><00:02:09.959><c> at</c>

00:02:10.130 --> 00:02:10.140 align:start position:0%
again because I know I like to say hi at
 

00:02:10.140 --> 00:02:12.350 align:start position:0%
again because I know I like to say hi at
the<00:02:10.259><c> beginning</c><00:02:10.380><c> my</c><00:02:10.860><c> conversations</c><00:02:11.700><c> with</c><00:02:12.120><c> my</c>

00:02:12.350 --> 00:02:12.360 align:start position:0%
the beginning my conversations with my
 

00:02:12.360 --> 00:02:14.690 align:start position:0%
the beginning my conversations with my
Ai<00:02:12.720><c> and</c><00:02:13.200><c> so</c><00:02:13.379><c> there</c><00:02:13.560><c> it</c><00:02:13.680><c> is</c><00:02:13.800><c> the</c><00:02:14.160><c> bottom</c><00:02:14.459><c> it's</c>

00:02:14.690 --> 00:02:14.700 align:start position:0%
Ai and so there it is the bottom it's
 

00:02:14.700 --> 00:02:18.170 align:start position:0%
Ai and so there it is the bottom it's
got<00:02:14.879><c> this</c><00:02:15.739><c> tokens</c><00:02:16.739><c> per</c><00:02:16.920><c> second</c><00:02:17.040><c> so</c><00:02:17.459><c> the</c><00:02:17.879><c> eval</c>

00:02:18.170 --> 00:02:18.180 align:start position:0%
got this tokens per second so the eval
 

00:02:18.180 --> 00:02:20.270 align:start position:0%
got this tokens per second so the eval
rate<00:02:18.420><c> was</c><00:02:18.599><c> about</c><00:02:18.780><c> 36</c><00:02:19.319><c> tokens</c><00:02:19.920><c> per</c><00:02:20.099><c> second</c>

00:02:20.270 --> 00:02:20.280 align:start position:0%
rate was about 36 tokens per second
 

00:02:20.280 --> 00:02:26.330 align:start position:0%
rate was about 36 tokens per second
which<00:02:21.239><c> is</c><00:02:21.360><c> pretty</c><00:02:21.720><c> cool</c><00:02:21.920><c> why</c><00:02:22.920><c> is</c><00:02:23.340><c> the</c><00:02:23.940><c> sky</c><00:02:24.420><c> blue</c>

00:02:26.330 --> 00:02:26.340 align:start position:0%
which is pretty cool why is the sky blue
 

00:02:26.340 --> 00:02:28.550 align:start position:0%
which is pretty cool why is the sky blue
but<00:02:26.879><c> here</c><00:02:27.180><c> we</c><00:02:27.300><c> go</c><00:02:27.360><c> there</c><00:02:27.599><c> he</c><00:02:27.840><c> is</c><00:02:28.020><c> uh</c><00:02:28.440><c> hello</c>

00:02:28.550 --> 00:02:28.560 align:start position:0%
but here we go there he is uh hello
 

00:02:28.560 --> 00:02:30.650 align:start position:0%
but here we go there he is uh hello
that's<00:02:28.920><c> a</c><00:02:29.040><c> great</c><00:02:29.160><c> question</c><00:02:29.459><c> this</c><00:02:30.180><c> is</c><00:02:30.239><c> why</c><00:02:30.420><c> the</c>

00:02:30.650 --> 00:02:30.660 align:start position:0%
that's a great question this is why the
 

00:02:30.660 --> 00:02:32.390 align:start position:0%
that's a great question this is why the
sky<00:02:30.780><c> is</c><00:02:30.959><c> blue</c><00:02:31.080><c> and</c><00:02:31.620><c> that</c><00:02:31.739><c> answered</c><00:02:32.099><c> in</c><00:02:32.220><c> about</c>

00:02:32.390 --> 00:02:32.400 align:start position:0%
sky is blue and that answered in about
 

00:02:32.400 --> 00:02:35.330 align:start position:0%
sky is blue and that answered in about
35<00:02:32.700><c> tokens</c><00:02:33.420><c> per</c><00:02:33.599><c> second</c><00:02:33.780><c> this</c><00:02:34.500><c> is</c><00:02:34.620><c> my</c><00:02:34.920><c> uh</c>

00:02:35.330 --> 00:02:35.340 align:start position:0%
35 tokens per second this is my uh
 

00:02:35.340 --> 00:02:37.910 align:start position:0%
35 tokens per second this is my uh
laptop<00:02:35.640><c> that</c><00:02:35.879><c> I</c><00:02:36.000><c> use</c><00:02:36.060><c> for</c><00:02:36.239><c> work</c><00:02:36.480><c> it</c><00:02:37.020><c> is</c><00:02:37.260><c> an</c><00:02:37.560><c> M1</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
laptop that I use for work it is an M1
 

00:02:37.920 --> 00:02:41.990 align:start position:0%
laptop that I use for work it is an M1
Max<00:02:38.400><c> with</c><00:02:39.180><c> 64</c><00:02:39.900><c> gigs</c><00:02:40.319><c> of</c><00:02:40.440><c> memory</c><00:02:40.980><c> I</c><00:02:41.700><c> did</c><00:02:41.760><c> just</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
Max with 64 gigs of memory I did just
 

00:02:42.000 --> 00:02:45.410 align:start position:0%
Max with 64 gigs of memory I did just
try<00:02:42.480><c> llama</c><00:02:43.080><c> this</c><00:02:43.560><c> particular</c><00:02:43.920><c> llama</c><00:02:44.400><c> to</c><00:02:44.640><c> uh</c>

00:02:45.410 --> 00:02:45.420 align:start position:0%
try llama this particular llama to uh
 

00:02:45.420 --> 00:02:48.229 align:start position:0%
try llama this particular llama to uh
model<00:02:45.920><c> on</c><00:02:46.920><c> a</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
model on a
 

00:02:48.239 --> 00:02:52.550 align:start position:0%
model on a
M<00:02:48.840><c> the</c><00:02:49.500><c> original</c><00:02:49.620><c> M1</c><00:02:50.580><c> Mac</c><00:02:51.060><c> Mini</c><00:02:51.480><c> with</c><00:02:52.260><c> eight</c>

00:02:52.550 --> 00:02:52.560 align:start position:0%
M the original M1 Mac Mini with eight
 

00:02:52.560 --> 00:02:54.470 align:start position:0%
M the original M1 Mac Mini with eight
gigs<00:02:53.040><c> base</c><00:02:53.459><c> model</c>

00:02:54.470 --> 00:02:54.480 align:start position:0%
gigs base model
 

00:02:54.480 --> 00:02:57.350 align:start position:0%
gigs base model
it<00:02:54.959><c> would</c><00:02:55.080><c> not</c><00:02:55.260><c> run</c><00:02:55.440><c> so</c><00:02:56.280><c> for</c><00:02:56.580><c> llama</c><00:02:56.879><c> 2</c><00:02:57.060><c> you</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
it would not run so for llama 2 you
 

00:02:57.360 --> 00:02:58.550 align:start position:0%
it would not run so for llama 2 you
definitely<00:02:57.540><c> want</c><00:02:57.720><c> to</c><00:02:57.900><c> have</c><00:02:58.019><c> a</c><00:02:58.319><c> little</c><00:02:58.440><c> bit</c>

00:02:58.550 --> 00:02:58.560 align:start position:0%
definitely want to have a little bit
 

00:02:58.560 --> 00:03:01.970 align:start position:0%
definitely want to have a little bit
more<00:02:58.739><c> than</c><00:02:58.860><c> eight</c><00:02:59.099><c> gig</c><00:02:59.340><c> so</c><00:02:59.640><c> maybe</c><00:02:59.879><c> 16</c><00:03:00.180><c> or</c><00:03:00.480><c> 32</c><00:03:00.980><c> is</c>

00:03:01.970 --> 00:03:01.980 align:start position:0%
more than eight gig so maybe 16 or 32 is
 

00:03:01.980 --> 00:03:04.910 align:start position:0%
more than eight gig so maybe 16 or 32 is
a<00:03:02.220><c> probably</c><00:03:02.340><c> pretty</c><00:03:02.760><c> normal</c><00:03:03.120><c> size</c><00:03:03.840><c> for</c><00:03:04.680><c> a</c>

00:03:04.910 --> 00:03:04.920 align:start position:0%
a probably pretty normal size for a
 

00:03:04.920 --> 00:03:08.330 align:start position:0%
a probably pretty normal size for a
MacBook<00:03:05.220><c> Pro</c><00:03:05.519><c> and</c><00:03:06.120><c> 64</c><00:03:06.599><c> it</c><00:03:06.959><c> runs</c><00:03:07.319><c> great</c><00:03:07.500><c> it</c>

00:03:08.330 --> 00:03:08.340 align:start position:0%
MacBook Pro and 64 it runs great it
 

00:03:08.340 --> 00:03:11.930 align:start position:0%
MacBook Pro and 64 it runs great it
takes<00:03:08.519><c> advantage</c><00:03:09.000><c> of</c><00:03:09.420><c> the</c><00:03:09.720><c> GPU</c><00:03:10.260><c> and</c><00:03:11.220><c> uh</c><00:03:11.760><c> yeah</c>

00:03:11.930 --> 00:03:11.940 align:start position:0%
takes advantage of the GPU and uh yeah
 

00:03:11.940 --> 00:03:13.790 align:start position:0%
takes advantage of the GPU and uh yeah
so<00:03:12.300><c> so</c><00:03:12.659><c> I</c><00:03:12.900><c> just</c><00:03:13.019><c> want</c><00:03:13.140><c> to</c><00:03:13.260><c> do</c><00:03:13.379><c> that</c><00:03:13.560><c> quick</c>

00:03:13.790 --> 00:03:13.800 align:start position:0%
so so I just want to do that quick
 

00:03:13.800 --> 00:03:15.890 align:start position:0%
so so I just want to do that quick
introduction<00:03:14.340><c> to</c><00:03:14.640><c> how</c><00:03:14.940><c> easy</c><00:03:15.060><c> is</c><00:03:15.300><c> it</c><00:03:15.480><c> to</c><00:03:15.720><c> run</c>

00:03:15.890 --> 00:03:15.900 align:start position:0%
introduction to how easy is it to run
 

00:03:15.900 --> 00:03:20.149 align:start position:0%
introduction to how easy is it to run
llama<00:03:16.680><c> 2</c><00:03:16.920><c> on</c><00:03:17.700><c> your</c><00:03:18.420><c> Mac</c><00:03:18.920><c> assuming</c><00:03:19.920><c> you're</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
llama 2 on your Mac assuming you're
 

00:03:20.159 --> 00:03:23.210 align:start position:0%
llama 2 on your Mac assuming you're
using<00:03:20.519><c> Apple</c><00:03:20.760><c> an</c><00:03:21.180><c> apple</c><00:03:21.599><c> silicon</c><00:03:22.140><c> Mac</c><00:03:22.379><c> we</c><00:03:23.040><c> will</c>

00:03:23.210 --> 00:03:23.220 align:start position:0%
using Apple an apple silicon Mac we will
 

00:03:23.220 --> 00:03:26.949 align:start position:0%
using Apple an apple silicon Mac we will
have<00:03:23.700><c> Intel</c><00:03:24.659><c> Mac</c><00:03:25.080><c> supported</c><00:03:25.920><c> sometime</c><00:03:26.760><c> soon</c>

00:03:26.949 --> 00:03:26.959 align:start position:0%
have Intel Mac supported sometime soon
 

00:03:26.959 --> 00:03:30.229 align:start position:0%
have Intel Mac supported sometime soon
as<00:03:27.959><c> well</c><00:03:28.140><c> as</c><00:03:28.260><c> Windows</c><00:03:28.920><c> and</c><00:03:29.220><c> Linux</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
as well as Windows and Linux
 

00:03:30.239 --> 00:03:32.390 align:start position:0%
as well as Windows and Linux
but<00:03:30.780><c> for</c><00:03:30.959><c> now</c><00:03:31.080><c> this</c><00:03:31.500><c> is</c><00:03:31.739><c> really</c><00:03:31.980><c> just</c><00:03:32.220><c> about</c>

00:03:32.390 --> 00:03:32.400 align:start position:0%
but for now this is really just about
 

00:03:32.400 --> 00:03:35.330 align:start position:0%
but for now this is really just about
running<00:03:32.819><c> on</c><00:03:33.239><c> Apple</c><00:03:33.480><c> silicon</c><00:03:34.080><c> with</c><00:03:34.860><c> the</c><00:03:35.099><c> you</c>

00:03:35.330 --> 00:03:35.340 align:start position:0%
running on Apple silicon with the you
 

00:03:35.340 --> 00:03:37.070 align:start position:0%
running on Apple silicon with the you
know<00:03:35.400><c> built-in</c><00:03:35.819><c> gpus</c>

00:03:37.070 --> 00:03:37.080 align:start position:0%
know built-in gpus
 

00:03:37.080 --> 00:03:38.869 align:start position:0%
know built-in gpus
okay<00:03:37.560><c> that's</c><00:03:37.920><c> it</c><00:03:38.159><c> thanks</c><00:03:38.459><c> so</c><00:03:38.580><c> much</c><00:03:38.700><c> for</c>

00:03:38.869 --> 00:03:38.879 align:start position:0%
okay that's it thanks so much for
 

00:03:38.879 --> 00:03:42.260 align:start position:0%
okay that's it thanks so much for
watching<00:03:39.239><c> and</c><00:03:39.780><c> goodbye</c>

