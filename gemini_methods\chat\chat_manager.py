# Google GenAI Chat Manager with Conversation Memory

import logging
import json
import uuid
from typing import Any, Dict, Optional, List, Union, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
import asyncio

from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from asyncpg.pool import Pool
import pydantic

# Handle imports - try relative imports first, then absolute imports as fallback
try:
    from ..genai_utils import DEFAULT_MODEL_NAME, DEFAULT_GENERATION_CONFIG, DEFAULT_SAFETY_SETTINGS, _async_client
    from ..token_utils import calculate_advanced_api_cost
    from ..pricing_models import CostCalculationRequest, ContentType, OutputType
except ImportError:
    # Fallback for direct execution - use absolute imports
    import sys
    from pathlib import Path
    
    # Add parent directory to path for absolute imports
    current_dir = Path(__file__).resolve().parent
    parent_dir = current_dir.parent
    if str(parent_dir) not in sys.path:
        sys.path.insert(0, str(parent_dir))
    
    from genai_utils import DEFAULT_MODEL_NAME, DEFAULT_GENERATION_CONFIG, DEFAULT_SAFETY_SETTINGS, _async_client
    from token_utils import calculate_advanced_api_cost
    from pricing_models import CostCalculationRequest, ContentType, OutputType

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """Represents a single message in a chat conversation."""
    role: str  # 'user' or 'model'
    content: str
    timestamp: datetime
    token_count: Optional[int] = None
    cost: Optional[float] = None

@dataclass
class ChatSession:
    """Represents a chat session with metadata."""
    session_id: str
    model_name: str
    system_instruction: Optional[str]
    created_at: datetime
    last_message_at: datetime
    total_messages: int
    total_input_tokens: int
    total_output_tokens: int
    total_cost: float
    is_active: bool

class ChatManager:
    """
    Manages persistent chat conversations using Google GenAI's built-in chat client.
    
    Provides conversation memory, cost tracking, and database persistence.
    """
    
    def __init__(
        self,
        db_pool: Optional[Pool] = None,
        client_schema: str = "client_nooob_085c35c4",
        default_model: str = DEFAULT_MODEL_NAME
    ):
        """
        Initialize the ChatManager.
        
        Args:
            db_pool: Database pool for persistence (optional)
            client_schema: Database schema to use
            default_model: Default model to use for chat sessions
        """
        self.db_pool = db_pool
        self.client_schema = client_schema
        self.default_model = default_model
        
        # Active chat sessions (in-memory storage)
        self._active_chats: Dict[str, genai.chats.Chat] = {}
        self._chat_histories: Dict[str, List[ChatMessage]] = {}
        self._chat_sessions: Dict[str, ChatSession] = {}
        
        if not _async_client:
            raise RuntimeError("Google GenAI client not initialized. Check your API key configuration.")
            
        self.client = _async_client
        logger.info("ChatManager initialized successfully")

    async def create_chat_session(
        self,
        model_name: Optional[str] = None,
        system_instruction: Optional[str] = None,
        generation_config_overrides: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> str:
        """
        Create a new chat session.
        
        Args:
            model_name: Model to use (defaults to default_model)
            system_instruction: Optional system instruction for the chat
            generation_config_overrides: Optional config overrides
            session_id: Optional custom session ID
            
        Returns:
            Session ID for the created chat
        """
        effective_model = model_name or self.default_model
        session_id = session_id or str(uuid.uuid4())
        
        try:
            # Prepare generation config
            config = DEFAULT_GENERATION_CONFIG.copy()
            if generation_config_overrides:
                config.update(generation_config_overrides)
            
            # Add safety settings if not present
            if "safety_settings" not in config:
                config["safety_settings"] = DEFAULT_SAFETY_SETTINGS
              # Create chat instance using the SDK's chat client
            chat_config = {}
            
            # Handle config creation with system instruction
            if config is None:
                config = {}
            
            # Add system instruction to the config if provided
            if system_instruction:
                config["system_instruction"] = system_instruction
            
            if config:
                chat_config["config"] = types.GenerateContentConfig(**config)
            
            chat = self.client.chats.create(
                model=effective_model,
                **chat_config
            )
            
            # Store the chat session
            self._active_chats[session_id] = chat
            self._chat_histories[session_id] = []
            
            # Create session metadata
            session = ChatSession(
                session_id=session_id,
                model_name=effective_model,
                system_instruction=system_instruction,
                created_at=datetime.now(timezone.utc),
                last_message_at=datetime.now(timezone.utc),
                total_messages=0,
                total_input_tokens=0,
                total_output_tokens=0,
                total_cost=0.0,
                is_active=True
            )
            self._chat_sessions[session_id] = session
            
            # Save to database if available
            if self.db_pool:
                await self._save_session_to_db(session)
            
            logger.info(f"Created chat session {session_id} with model {effective_model}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create chat session: {e}")
            raise

    async def send_message(
        self,
        session_id: str,
        message: str,
        include_files: Optional[List[types.Part]] = None
    ) -> Tuple[str, Optional[int], Optional[int], Optional[float]]:
        """
        Send a message to a chat session and get the response.
        
        Args:
            session_id: ID of the chat session
            message: User message to send
            include_files: Optional file parts to include
            
        Returns:
            Tuple of (response_text, input_tokens, output_tokens, cost)
        """
        if session_id not in self._active_chats:
            raise ValueError(f"Chat session {session_id} not found")
        
        chat = self._active_chats[session_id]
        session = self._chat_sessions[session_id]
        
        try:
            # Prepare the message content
            if include_files:
                # Create a content message with both text and files
                content_parts = [types.Part.from_text(message)]
                content_parts.extend(include_files)
                content = types.Content(parts=content_parts)
            else:
                content = message
            
            start_time = datetime.now(timezone.utc)
            
            # Send message using the chat client
            response = await chat.send_message(content)
            
            # Extract response text
            response_text = response.text if hasattr(response, 'text') else str(response)
            
            # Extract token counts
            input_tokens = None
            output_tokens = None
            if hasattr(response, 'usage_metadata'):
                input_tokens = getattr(response.usage_metadata, 'prompt_token_count', None)
                output_tokens = getattr(response.usage_metadata, 'candidates_token_count', None)
            
            # Calculate cost
            cost = None
            if input_tokens is not None and output_tokens is not None and self.db_pool:
                try:
                    cost_request = CostCalculationRequest(
                        model_name=session.model_name,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        content_type=ContentType.TEXT_IMAGE_VIDEO,
                        output_type=OutputType.NON_THINKING
                    )
                    cost_result = await calculate_advanced_api_cost(cost_request, self.db_pool)
                    cost = cost_result.total_cost if cost_result else None
                except Exception as cost_err:
                    logger.warning(f"Cost calculation failed: {cost_err}")
            
            # Store messages in history
            user_message = ChatMessage(
                role="user",
                content=message,
                timestamp=start_time,
                token_count=input_tokens,
                cost=cost
            )
            
            model_message = ChatMessage(
                role="model", 
                content=response_text,
                timestamp=datetime.now(timezone.utc),
                token_count=output_tokens,
                cost=None  # Cost is attributed to the input
            )
            
            self._chat_histories[session_id].extend([user_message, model_message])
            
            # Update session metadata
            session.last_message_at = datetime.now(timezone.utc)
            session.total_messages += 2
            if input_tokens:
                session.total_input_tokens += input_tokens
            if output_tokens:
                session.total_output_tokens += output_tokens
            if cost:
                session.total_cost += cost
            
            # Save to database if available
            if self.db_pool:
                await self._save_messages_to_db(session_id, [user_message, model_message])
                await self._update_session_in_db(session)
            
            logger.info(f"Message sent to session {session_id}. Tokens: {input_tokens}/{output_tokens}, Cost: ${cost or 0:.6f}")
            return response_text, input_tokens, output_tokens, cost
            
        except Exception as e:
            logger.error(f"Failed to send message to session {session_id}: {e}")
            raise

    async def get_chat_history(self, session_id: str) -> List[ChatMessage]:
        """
        Get the conversation history for a chat session.
        
        Args:
            session_id: ID of the chat session
            
        Returns:
            List of chat messages in chronological order
        """
        if session_id not in self._chat_histories:
            if self.db_pool:
                # Try to load from database
                history = await self._load_history_from_db(session_id)
                if history:
                    self._chat_histories[session_id] = history
                    return history
            raise ValueError(f"Chat session {session_id} not found")
        
        return self._chat_histories[session_id].copy()

    async def get_session_info(self, session_id: str) -> ChatSession:
        """
        Get metadata about a chat session.
        
        Args:
            session_id: ID of the chat session
            
        Returns:
            ChatSession metadata
        """
        if session_id not in self._chat_sessions:
            if self.db_pool:
                # Try to load from database
                session = await self._load_session_from_db(session_id)
                if session:
                    self._chat_sessions[session_id] = session
                    return session
            raise ValueError(f"Chat session {session_id} not found")
        
        return self._chat_sessions[session_id]

    async def list_active_sessions(self) -> List[ChatSession]:
        """
        List all active chat sessions.
        
        Returns:
            List of active ChatSession objects
        """
        active_sessions = [s for s in self._chat_sessions.values() if s.is_active]
        
        # If we have a database, also load recent sessions
        if self.db_pool:
            try:
                db_sessions = await self._load_recent_sessions_from_db()
                # Merge with in-memory sessions, avoiding duplicates
                session_ids = {s.session_id for s in active_sessions}
                for db_session in db_sessions:
                    if db_session.session_id not in session_ids:
                        active_sessions.append(db_session)
            except Exception as e:
                logger.warning(f"Failed to load sessions from database: {e}")
        
        return sorted(active_sessions, key=lambda s: s.last_message_at, reverse=True)

    async def restore_chat_session(self, session_id: str) -> bool:
        """
        Restore a chat session with its full history.
        
        Args:
            session_id: ID of the chat session to restore
            
        Returns:
            True if restored successfully, False otherwise
        """
        try:
            # Load session metadata
            session = await self.get_session_info(session_id)
            
            # Load chat history
            history = await self.get_chat_history(session_id)
              # Recreate the chat with history
            chat_config = {}
            config = {}
            
            # Add system instruction to config if present
            if session.system_instruction:
                config["system_instruction"] = session.system_instruction
            
            # Convert history to the format expected by the chat client
            chat_history = []
            for msg in history:
                if msg.role == "user":
                    chat_history.append(types.Content(
                        role="user",
                        parts=[types.Part.from_text(msg.content)]
                    ))
                elif msg.role == "model":
                    chat_history.append(types.Content(
                        role="model", 
                        parts=[types.Part.from_text(msg.content)]
                    ))
            
            if chat_history:
                chat_config["history"] = chat_history
            
            # Add config with system instruction if we have any config to add
            if config:
                chat_config["config"] = types.GenerateContentConfig(**config)
            
            # Create the chat instance
            chat = self.client.chats.create(
                model=session.model_name,
                **chat_config
            )
            
            # Store the restored chat
            self._active_chats[session_id] = chat
            self._chat_histories[session_id] = history
            self._chat_sessions[session_id] = session
            session.is_active = True
            
            logger.info(f"Restored chat session {session_id} with {len(history)} messages")
            return True
            
        except Exception as e:
            logger.error(f"Failed to restore chat session {session_id}: {e}")
            return False

    async def close_session(self, session_id: str) -> bool:
        """
        Close a chat session and clean up resources.
        
        Args:
            session_id: ID of the chat session to close
            
        Returns:
            True if closed successfully, False otherwise
        """
        try:
            if session_id in self._chat_sessions:
                self._chat_sessions[session_id].is_active = False
            
            # Update database if available
            if self.db_pool and session_id in self._chat_sessions:
                await self._update_session_in_db(self._chat_sessions[session_id])
            
            # Clean up in-memory resources
            self._active_chats.pop(session_id, None)
            # Keep history and session metadata for potential restoration
            
            logger.info(f"Closed chat session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to close chat session {session_id}: {e}")
            return False

    # Database persistence methods

    async def _save_session_to_db(self, session: ChatSession) -> None:
        """Save chat session metadata to database."""
        if not self.db_pool:
            return
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute(f"""
                    INSERT INTO {self.client_schema}.chat_sessions 
                    (session_id, model_name, system_instruction, created_at, last_message_at,
                     total_messages, total_input_tokens, total_output_tokens, total_cost, is_active)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (session_id) DO UPDATE SET
                        last_message_at = EXCLUDED.last_message_at,
                        total_messages = EXCLUDED.total_messages,
                        total_input_tokens = EXCLUDED.total_input_tokens,
                        total_output_tokens = EXCLUDED.total_output_tokens,
                        total_cost = EXCLUDED.total_cost,
                        is_active = EXCLUDED.is_active
                """, session.session_id, session.model_name, session.system_instruction,
                session.created_at, session.last_message_at, session.total_messages,
                session.total_input_tokens, session.total_output_tokens, session.total_cost,
                session.is_active)
        except Exception as e:
            logger.warning(f"Failed to save session to database: {e}")

    async def _save_messages_to_db(self, session_id: str, messages: List[ChatMessage]) -> None:
        """Save chat messages to database."""
        if not self.db_pool:
            return
        
        try:
            async with self.db_pool.acquire() as conn:
                for msg in messages:
                    await conn.execute(f"""
                        INSERT INTO {self.client_schema}.chat_messages
                        (session_id, role, content, timestamp, token_count, cost)
                        VALUES ($1, $2, $3, $4, $5, $6)
                    """, session_id, msg.role, msg.content, msg.timestamp,
                    msg.token_count, msg.cost)
        except Exception as e:
            logger.warning(f"Failed to save messages to database: {e}")

    async def _update_session_in_db(self, session: ChatSession) -> None:
        """Update session metadata in database."""
        if not self.db_pool:
            return
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute(f"""
                    UPDATE {self.client_schema}.chat_sessions 
                    SET last_message_at = $2, total_messages = $3, total_input_tokens = $4,
                        total_output_tokens = $5, total_cost = $6, is_active = $7
                    WHERE session_id = $1
                """, session.session_id, session.last_message_at, session.total_messages,
                session.total_input_tokens, session.total_output_tokens, session.total_cost,
                session.is_active)
        except Exception as e:
            logger.warning(f"Failed to update session in database: {e}")

    async def _load_session_from_db(self, session_id: str) -> Optional[ChatSession]:
        """Load session metadata from database."""
        if not self.db_pool:
            return None
        
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(f"""
                    SELECT * FROM {self.client_schema}.chat_sessions
                    WHERE session_id = $1
                """, session_id)
                
                if row:
                    return ChatSession(**dict(row))
        except Exception as e:
            logger.warning(f"Failed to load session from database: {e}")
        
        return None

    async def _load_history_from_db(self, session_id: str) -> List[ChatMessage]:
        """Load chat history from database."""
        if not self.db_pool:
            return []
        
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(f"""
                    SELECT * FROM {self.client_schema}.chat_messages
                    WHERE session_id = $1
                    ORDER BY timestamp ASC
                """, session_id)
                
                return [ChatMessage(**dict(row)) for row in rows]
        except Exception as e:
            logger.warning(f"Failed to load history from database: {e}")
            return []

    async def _load_recent_sessions_from_db(self, limit: int = 50) -> List[ChatSession]:
        """Load recent sessions from database."""
        if not self.db_pool:
            return []
        
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(f"""
                    SELECT * FROM {self.client_schema}.chat_sessions
                    ORDER BY last_message_at DESC
                    LIMIT $1
                """, limit)
                
                return [ChatSession(**dict(row)) for row in rows]
        except Exception as e:
            logger.warning(f"Failed to load recent sessions from database: {e}")
            return []

    # Utility methods

    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """
        Get a summary of a chat session.
        
        Args:
            session_id: ID of the chat session
            
        Returns:
            Dictionary containing session summary
        """
        if session_id not in self._chat_sessions:
            return {"error": "Session not found"}
        
        session = self._chat_sessions[session_id]
        history = self._chat_histories.get(session_id, [])
        
        return {
            "session_id": session.session_id,
            "model_name": session.model_name,
            "created_at": session.created_at.isoformat(),
            "last_message_at": session.last_message_at.isoformat(),
            "total_messages": session.total_messages,
            "total_input_tokens": session.total_input_tokens,
            "total_output_tokens": session.total_output_tokens,
            "total_cost": session.total_cost,
            "is_active": session.is_active,
            "system_instruction": session.system_instruction,
            "message_count": len(history)
        }

    async def export_session(self, session_id: str, format: str = "json") -> str:
        """
        Export a chat session to various formats.
        
        Args:
            session_id: ID of the chat session
            format: Export format ("json", "markdown")
            
        Returns:
            Exported session data as string
        """
        session = await self.get_session_info(session_id)
        history = await self.get_chat_history(session_id)
        
        if format == "json":
            export_data = {
                "session": asdict(session),
                "messages": [asdict(msg) for msg in history]
            }
            return json.dumps(export_data, indent=2, default=str)
        
        elif format == "markdown":
            lines = [
                f"# Chat Session: {session_id}",
                f"**Model:** {session.model_name}",
                f"**Created:** {session.created_at}",
                f"**Messages:** {session.total_messages}",
                f"**Total Cost:** ${session.total_cost:.6f}",
                ""
            ]
            
            if session.system_instruction:
                lines.extend([
                    "## System Instruction",
                    session.system_instruction,
                    ""
                ])
            
            lines.append("## Conversation")
            
            for msg in history:
                role_label = "🧑 **User**" if msg.role == "user" else "🤖 **Assistant**"
                lines.extend([
                    f"### {role_label} ({msg.timestamp})",
                    msg.content,
                    ""
                ])
            
            return "\n".join(lines)
        
        else:
            raise ValueError(f"Unsupported export format: {format}")


# Convenience functions for common use cases

async def create_simple_chat(
    model_name: Optional[str] = None,
    system_instruction: Optional[str] = None,
    db_pool: Optional[Pool] = None
) -> Tuple[ChatManager, str]:
    """
    Create a simple chat session for immediate use.
    
    Args:
        model_name: Model to use
        system_instruction: Optional system instruction
        db_pool: Optional database pool
        
    Returns:
        Tuple of (ChatManager instance, session_id)
    """
    manager = ChatManager(db_pool=db_pool, default_model=model_name or DEFAULT_MODEL_NAME)
    session_id = await manager.create_chat_session(
        model_name=model_name,
        system_instruction=system_instruction
    )
    return manager, session_id

async def quick_chat_exchange(
    message: str,
    model_name: Optional[str] = None,
    system_instruction: Optional[str] = None,
    db_pool: Optional[Pool] = None
) -> str:
    """
    Quick one-off chat exchange without session management.
    
    Args:
        message: User message
        model_name: Model to use
        system_instruction: Optional system instruction
        db_pool: Optional database pool
        
    Returns:
        Assistant response
    """
    manager, session_id = await create_simple_chat(model_name, system_instruction, db_pool)
    response, _, _, _ = await manager.send_message(session_id, message)
    await manager.close_session(session_id)
    return response 