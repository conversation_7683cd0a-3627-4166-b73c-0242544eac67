WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.389 align:start position:0%
 
hey<00:00:00.680><c> everyone</c><00:00:01.199><c> my</c><00:00:01.319><c> name</c><00:00:01.439><c> is</c><00:00:01.560><c> <PERSON><PERSON>ea</c><00:00:02.120><c> I'm</c><00:00:02.280><c> the</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
hey everyone my name is <PERSON><PERSON><PERSON> I'm the
 

00:00:02.399 --> 00:00:04.430 align:start position:0%
hey everyone my name is <PERSON><PERSON><PERSON> I'm the
instructor<00:00:02.919><c> for</c><00:00:03.240><c> the</c><00:00:03.399><c> data</c><00:00:03.719><c> validation</c><00:00:04.200><c> for</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
instructor for the data validation for
 

00:00:04.440 --> 00:00:06.749 align:start position:0%
instructor for the data validation for
machine<00:00:04.759><c> learning</c><00:00:05.160><c> pipelines</c><00:00:05.680><c> course</c><00:00:06.440><c> large</c>

00:00:06.749 --> 00:00:06.759 align:start position:0%
machine learning pipelines course large
 

00:00:06.759 --> 00:00:08.629 align:start position:0%
machine learning pipelines course large
language<00:00:07.120><c> model</c><00:00:07.480><c> pipelines</c><00:00:08.000><c> or</c><00:00:08.120><c> llm</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
language model pipelines or llm
 

00:00:08.639 --> 00:00:11.230 align:start position:0%
language model pipelines or llm
pipelines<00:00:09.160><c> are</c><00:00:09.320><c> essentially</c><00:00:09.679><c> a</c><00:00:09.840><c> subass</c><00:00:10.599><c> of</c><00:00:10.800><c> M</c>

00:00:11.230 --> 00:00:11.240 align:start position:0%
pipelines are essentially a subass of M
 

00:00:11.240 --> 00:00:13.110 align:start position:0%
pipelines are essentially a subass of M
pipelines<00:00:11.759><c> and</c><00:00:11.880><c> you</c><00:00:11.960><c> can</c><00:00:12.200><c> think</c><00:00:12.519><c> of</c><00:00:12.880><c> the</c>

00:00:13.110 --> 00:00:13.120 align:start position:0%
pipelines and you can think of the
 

00:00:13.120 --> 00:00:16.430 align:start position:0%
pipelines and you can think of the
inputs<00:00:13.920><c> to</c><00:00:14.120><c> these</c><00:00:14.360><c> pipelines</c><00:00:14.839><c> as</c><00:00:15.199><c> prompts</c><00:00:16.199><c> um</c>

00:00:16.430 --> 00:00:16.440 align:start position:0%
inputs to these pipelines as prompts um
 

00:00:16.440 --> 00:00:17.910 align:start position:0%
inputs to these pipelines as prompts um
and<00:00:16.600><c> these</c><00:00:16.720><c> are</c><00:00:16.880><c> kind</c><00:00:17.000><c> of</c><00:00:17.119><c> like</c><00:00:17.279><c> features</c><00:00:17.800><c> in</c>

00:00:17.910 --> 00:00:17.920 align:start position:0%
and these are kind of like features in
 

00:00:17.920 --> 00:00:20.029 align:start position:0%
and these are kind of like features in
the<00:00:18.039><c> ml</c><00:00:18.400><c> World</c><00:00:18.960><c> prompts</c><00:00:19.240><c> are</c><00:00:19.400><c> formatted</c><00:00:19.880><c> with</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
the ml World prompts are formatted with
 

00:00:20.039 --> 00:00:22.509 align:start position:0%
the ml World prompts are formatted with
data<00:00:20.279><c> at</c><00:00:20.480><c> runtime</c><00:00:21.320><c> for</c><00:00:21.560><c> example</c><00:00:22.320><c> if</c><00:00:22.439><c> you</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
data at runtime for example if you
 

00:00:22.519 --> 00:00:24.349 align:start position:0%
data at runtime for example if you
wanted<00:00:22.800><c> to</c><00:00:23.199><c> have</c><00:00:23.320><c> a</c><00:00:23.480><c> prompt</c><00:00:23.800><c> to</c><00:00:24.000><c> write</c><00:00:24.199><c> an</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
wanted to have a prompt to write an
 

00:00:24.359 --> 00:00:27.349 align:start position:0%
wanted to have a prompt to write an
email<00:00:25.279><c> you</c><00:00:25.439><c> might</c><00:00:25.720><c> have</c><00:00:26.199><c> some</c><00:00:26.800><c> runtime</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
email you might have some runtime
 

00:00:27.359 --> 00:00:29.310 align:start position:0%
email you might have some runtime
variables<00:00:27.920><c> like</c><00:00:28.080><c> user</c><00:00:28.439><c> information</c><00:00:29.119><c> and</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
variables like user information and
 

00:00:29.320 --> 00:00:31.070 align:start position:0%
variables like user information and
recipient<00:00:29.759><c> information</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
recipient information
 

00:00:31.080 --> 00:00:33.150 align:start position:0%
recipient information
and<00:00:31.199><c> we</c><00:00:31.279><c> can</c><00:00:31.480><c> think</c><00:00:31.640><c> of</c><00:00:31.960><c> the</c><00:00:32.160><c> response</c><00:00:33.000><c> from</c>

00:00:33.150 --> 00:00:33.160 align:start position:0%
and we can think of the response from
 

00:00:33.160 --> 00:00:36.630 align:start position:0%
and we can think of the response from
the<00:00:33.280><c> llm</c><00:00:34.200><c> as</c><00:00:34.480><c> the</c><00:00:34.680><c> output</c><00:00:35.120><c> or</c><00:00:35.399><c> prediction</c><00:00:36.399><c> um</c>

00:00:36.630 --> 00:00:36.640 align:start position:0%
the llm as the output or prediction um
 

00:00:36.640 --> 00:00:39.430 align:start position:0%
the llm as the output or prediction um
using<00:00:36.920><c> the</c><00:00:37.040><c> ml</c><00:00:37.480><c> pipeline</c><00:00:37.960><c> analogy</c><00:00:38.960><c> the</c><00:00:39.079><c> only</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
using the ml pipeline analogy the only
 

00:00:39.440 --> 00:00:41.950 align:start position:0%
using the ml pipeline analogy the only
difference<00:00:39.879><c> here</c><00:00:40.120><c> really</c><00:00:40.559><c> is</c><00:00:40.800><c> that</c><00:00:41.360><c> llm</c>

00:00:41.950 --> 00:00:41.960 align:start position:0%
difference here really is that llm
 

00:00:41.960 --> 00:00:43.590 align:start position:0%
difference here really is that llm
pipeline<00:00:42.440><c> inputs</c><00:00:42.800><c> and</c><00:00:42.920><c> outputs</c><00:00:43.320><c> are</c>

00:00:43.590 --> 00:00:43.600 align:start position:0%
pipeline inputs and outputs are
 

00:00:43.600 --> 00:00:46.189 align:start position:0%
pipeline inputs and outputs are
completely<00:00:44.000><c> unstructured</c><00:00:44.680><c> free</c><00:00:45.000><c> form</c><00:00:45.360><c> test</c>

00:00:46.189 --> 00:00:46.199 align:start position:0%
completely unstructured free form test
 

00:00:46.199 --> 00:00:48.869 align:start position:0%
completely unstructured free form test
so<00:00:46.719><c> if</c><00:00:46.960><c> llm</c><00:00:47.520><c> pipelines</c><00:00:47.920><c> are</c><00:00:48.039><c> a</c><00:00:48.160><c> class</c><00:00:48.360><c> of</c><00:00:48.520><c> ml</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
so if llm pipelines are a class of ml
 

00:00:48.879 --> 00:00:51.150 align:start position:0%
so if llm pipelines are a class of ml
pipelines<00:00:49.399><c> we</c><00:00:49.559><c> still</c><00:00:49.800><c> need</c><00:00:49.960><c> to</c><00:00:50.160><c> monitor</c><00:00:50.800><c> props</c>

00:00:51.150 --> 00:00:51.160 align:start position:0%
pipelines we still need to monitor props
 

00:00:51.160 --> 00:00:53.189 align:start position:0%
pipelines we still need to monitor props
and<00:00:51.399><c> responses</c><00:00:52.039><c> we</c><00:00:52.199><c> still</c><00:00:52.399><c> need</c><00:00:52.600><c> to</c><00:00:52.719><c> do</c><00:00:52.920><c> data</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
and responses we still need to do data
 

00:00:53.199 --> 00:00:55.510 align:start position:0%
and responses we still need to do data
validation<00:00:53.760><c> on</c><00:00:54.000><c> these</c><00:00:54.199><c> inputs</c><00:00:54.559><c> and</c><00:00:54.760><c> outputs</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
validation on these inputs and outputs
 

00:00:55.520 --> 00:00:57.310 align:start position:0%
validation on these inputs and outputs
structured<00:00:56.000><c> data</c><00:00:56.280><c> validation</c><00:00:56.800><c> techniques</c>

00:00:57.310 --> 00:00:57.320 align:start position:0%
structured data validation techniques
 

00:00:57.320 --> 00:01:00.229 align:start position:0%
structured data validation techniques
still<00:00:57.719><c> apply</c><00:00:58.280><c> to</c><00:00:58.440><c> the</c><00:00:58.559><c> L</c><00:00:59.079><c> setting</c><00:00:59.719><c> so</c><00:00:59.920><c> so</c><00:01:00.039><c> for</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
still apply to the L setting so so for
 

00:01:00.239 --> 00:01:02.670 align:start position:0%
still apply to the L setting so so for
example<00:01:01.039><c> let's</c><00:01:01.239><c> say</c><00:01:01.440><c> we</c><00:01:01.600><c> had</c><00:01:01.760><c> a</c><00:01:01.960><c> pipeline</c><00:01:02.559><c> with</c>

00:01:02.670 --> 00:01:02.680 align:start position:0%
example let's say we had a pipeline with
 

00:01:02.680 --> 00:01:06.070 align:start position:0%
example let's say we had a pipeline with
a<00:01:02.840><c> prompt</c><00:01:03.320><c> to</c><00:01:03.840><c> ask</c><00:01:04.080><c> an</c><00:01:04.239><c> llm</c><00:01:04.680><c> to</c><00:01:04.879><c> write</c><00:01:05.159><c> emails</c>

00:01:06.070 --> 00:01:06.080 align:start position:0%
a prompt to ask an llm to write emails
 

00:01:06.080 --> 00:01:08.870 align:start position:0%
a prompt to ask an llm to write emails
we<00:01:06.240><c> might</c><00:01:06.479><c> fill</c><00:01:06.720><c> in</c><00:01:06.960><c> some</c><00:01:07.560><c> information</c><00:01:08.400><c> at</c><00:01:08.560><c> run</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
we might fill in some information at run
 

00:01:08.880 --> 00:01:11.310 align:start position:0%
we might fill in some information at run
time<00:01:09.560><c> but</c><00:01:10.000><c> because</c><00:01:10.400><c> these</c><00:01:10.600><c> values</c><00:01:10.880><c> are</c><00:01:11.000><c> filled</c>

00:01:11.310 --> 00:01:11.320 align:start position:0%
time but because these values are filled
 

00:01:11.320 --> 00:01:13.910 align:start position:0%
time but because these values are filled
in<00:01:11.600><c> at</c><00:01:11.759><c> run</c><00:01:12.119><c> time</c><00:01:12.840><c> we</c><00:01:13.000><c> still</c><00:01:13.200><c> need</c><00:01:13.360><c> to</c><00:01:13.520><c> check</c><00:01:13.720><c> to</c>

00:01:13.910 --> 00:01:13.920 align:start position:0%
in at run time we still need to check to
 

00:01:13.920 --> 00:01:15.270 align:start position:0%
in at run time we still need to check to
make<00:01:14.040><c> sure</c><00:01:14.280><c> that</c><00:01:14.400><c> they're</c><00:01:14.600><c> all</c><00:01:14.840><c> completely</c>

00:01:15.270 --> 00:01:15.280 align:start position:0%
make sure that they're all completely
 

00:01:15.280 --> 00:01:17.749 align:start position:0%
make sure that they're all completely
sound<00:01:15.759><c> and</c><00:01:15.960><c> make</c><00:01:16.240><c> sense</c><00:01:17.000><c> so</c><00:01:17.280><c> variables</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
sound and make sense so variables
 

00:01:17.759 --> 00:01:19.710 align:start position:0%
sound and make sense so variables
shouldn't<00:01:18.080><c> be</c><00:01:18.280><c> null</c><00:01:18.799><c> for</c><00:01:19.000><c> example</c><00:01:19.400><c> otherwise</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
shouldn't be null for example otherwise
 

00:01:19.720 --> 00:01:21.910 align:start position:0%
shouldn't be null for example otherwise
who<00:01:19.840><c> knows</c><00:01:20.079><c> what</c><00:01:20.240><c> the</c><00:01:20.320><c> llm</c><00:01:20.759><c> could</c><00:01:21.079><c> return</c><00:01:21.759><c> or</c>

00:01:21.910 --> 00:01:21.920 align:start position:0%
who knows what the llm could return or
 

00:01:21.920 --> 00:01:24.149 align:start position:0%
who knows what the llm could return or
in<00:01:22.000><c> a</c><00:01:22.119><c> more</c><00:01:22.320><c> advanced</c><00:01:22.720><c> setting</c><00:01:23.680><c> the</c>

00:01:24.149 --> 00:01:24.159 align:start position:0%
in a more advanced setting the
 

00:01:24.159 --> 00:01:26.390 align:start position:0%
in a more advanced setting the
embeddings<00:01:25.159><c> of</c><00:01:25.439><c> each</c><00:01:25.600><c> of</c><00:01:25.799><c> these</c><00:01:26.040><c> values</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
embeddings of each of these values
 

00:01:26.400 --> 00:01:29.550 align:start position:0%
embeddings of each of these values
should<00:01:26.600><c> be</c><00:01:26.920><c> similar</c><00:01:27.600><c> to</c><00:01:28.200><c> what</c><00:01:28.360><c> we</c><00:01:28.600><c> expect</c><00:01:29.400><c> um</c>

00:01:29.550 --> 00:01:29.560 align:start position:0%
should be similar to what we expect um
 

00:01:29.560 --> 00:01:32.190 align:start position:0%
should be similar to what we expect um
previous<00:01:30.159><c> runs</c><00:01:30.720><c> maybe</c><00:01:31.119><c> or</c><00:01:31.400><c> partitions</c><00:01:31.920><c> or</c>

00:01:32.190 --> 00:01:32.200 align:start position:0%
previous runs maybe or partitions or
 

00:01:32.200 --> 00:01:35.580 align:start position:0%
previous runs maybe or partitions or
groups<00:01:32.600><c> of</c>

00:01:35.580 --> 00:01:35.590 align:start position:0%
 
 

00:01:35.590 --> 00:01:47.670 align:start position:0%
 
[Music]

00:01:47.670 --> 00:01:47.680 align:start position:0%
 
 

00:01:47.680 --> 00:01:50.680 align:start position:0%
 
runs

