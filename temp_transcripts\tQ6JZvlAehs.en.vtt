WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.230 align:start position:0%
 
want<00:00:00.280><c> to</c><00:00:00.440><c> add</c><00:00:00.680><c> grock</c><00:00:01.040><c> to</c><00:00:01.160><c> your</c><00:00:01.360><c> AI</c><00:00:01.800><c> project</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
want to add grock to your AI project
 

00:00:02.240 --> 00:00:04.789 align:start position:0%
want to add grock to your AI project
with<00:00:02.440><c> as</c><00:00:02.560><c> little</c><00:00:02.840><c> as</c><00:00:03.000><c> two</c><00:00:03.199><c> lines</c><00:00:03.480><c> of</c><00:00:03.719><c> code</c><00:00:04.640><c> now</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
with as little as two lines of code now
 

00:00:04.799 --> 00:00:10.549 align:start position:0%
with as little as two lines of code now
you<00:00:04.960><c> can</c><00:00:05.240><c> with</c><00:00:05.440><c> pocket</c><00:00:05.759><c> groc</c><00:00:06.879><c> pocket</c><00:00:07.879><c> in</c><00:00:08.120><c> your</c>

00:00:10.549 --> 00:00:10.559 align:start position:0%
 
 

00:00:10.559 --> 00:00:15.670 align:start position:0%
 
pocket<00:00:11.559><c> pocket</c><00:00:12.080><c> Croc</c><00:00:12.599><c> croc</c><00:00:13.040><c> in</c><00:00:13.200><c> your</c>

00:00:15.670 --> 00:00:15.680 align:start position:0%
 
 

00:00:15.680 --> 00:00:19.710 align:start position:0%
 
pocket<00:00:17.160><c> pocket</c><00:00:18.160><c> in</c><00:00:18.640><c> pocket</c><00:00:19.199><c> so</c><00:00:19.359><c> is</c><00:00:19.439><c> that</c><00:00:19.600><c> a</c>

00:00:19.710 --> 00:00:19.720 align:start position:0%
pocket pocket in pocket so is that a
 

00:00:19.720 --> 00:00:21.269 align:start position:0%
pocket pocket in pocket so is that a
grock<00:00:20.080><c> in</c><00:00:20.160><c> your</c><00:00:20.400><c> pocket</c><00:00:20.680><c> or</c><00:00:20.800><c> are</c><00:00:20.920><c> you</c><00:00:21.080><c> just</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
grock in your pocket or are you just
 

00:00:21.279 --> 00:00:24.230 align:start position:0%
grock in your pocket or are you just
happy<00:00:21.519><c> to</c><00:00:21.640><c> see</c><00:00:21.840><c> me</c><00:00:22.760><c> why</c><00:00:22.920><c> can't</c><00:00:23.119><c> it</c><00:00:23.240><c> be</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
happy to see me why can't it be
 

00:00:24.240 --> 00:00:27.950 align:start position:0%
happy to see me why can't it be
both<00:00:25.240><c> yuck</c><00:00:25.920><c> you</c><00:00:26.080><c> started</c><00:00:26.480><c> it</c><00:00:27.279><c> so</c><00:00:27.480><c> what</c><00:00:27.599><c> is</c><00:00:27.760><c> this</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
both yuck you started it so what is this
 

00:00:27.960 --> 00:00:30.150 align:start position:0%
both yuck you started it so what is this
computer<00:00:28.359><c> geekery</c><00:00:28.880><c> we're</c><00:00:29.039><c> looking</c><00:00:29.279><c> at</c><00:00:29.560><c> here</c>

00:00:30.150 --> 00:00:30.160 align:start position:0%
computer geekery we're looking at here
 

00:00:30.160 --> 00:00:32.109 align:start position:0%
computer geekery we're looking at here
it's<00:00:30.279><c> a</c><00:00:30.480><c> demonstration</c><00:00:31.119><c> of</c><00:00:31.279><c> our</c><00:00:31.439><c> new</c><00:00:31.759><c> pocket</c>

00:00:32.109 --> 00:00:32.119 align:start position:0%
it's a demonstration of our new pocket
 

00:00:32.119 --> 00:00:34.549 align:start position:0%
it's a demonstration of our new pocket
grock<00:00:32.520><c> library</c><00:00:33.360><c> that</c><00:00:33.520><c> allows</c><00:00:33.800><c> a</c><00:00:33.960><c> developer's</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
grock library that allows a developer's
 

00:00:34.559 --> 00:00:37.549 align:start position:0%
grock library that allows a developer's
AI<00:00:35.000><c> application</c><00:00:35.520><c> to</c><00:00:35.800><c> access</c><00:00:36.160><c> grock's</c><00:00:36.600><c> llms</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
AI application to access grock's llms
 

00:00:37.559 --> 00:00:40.110 align:start position:0%
AI application to access grock's llms
with<00:00:37.760><c> just</c><00:00:38.000><c> a</c><00:00:38.120><c> couple</c><00:00:38.440><c> lines</c><00:00:38.680><c> of</c><00:00:39.000><c> code</c><00:00:40.000><c> I</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
with just a couple lines of code I
 

00:00:40.120 --> 00:00:41.869 align:start position:0%
with just a couple lines of code I
suppose<00:00:40.360><c> you</c><00:00:40.440><c> need</c><00:00:40.600><c> a</c><00:00:40.760><c> PhD</c><00:00:41.320><c> in</c><00:00:41.559><c> computer</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
suppose you need a PhD in computer
 

00:00:41.879 --> 00:00:44.630 align:start position:0%
suppose you need a PhD in computer
engineering<00:00:42.360><c> to</c><00:00:42.520><c> install</c><00:00:42.879><c> it</c><00:00:43.160><c> right</c><00:00:44.160><c> actually</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
engineering to install it right actually
 

00:00:44.640 --> 00:00:52.630 align:start position:0%
engineering to install it right actually
all<00:00:44.760><c> you</c><00:00:44.920><c> have</c><00:00:45.039><c> to</c><00:00:45.160><c> do</c><00:00:45.320><c> is</c>

00:00:52.630 --> 00:00:52.640 align:start position:0%
 
 

00:00:52.640 --> 00:00:56.590 align:start position:0%
 
this<00:00:53.640><c> that's</c><00:00:53.840><c> it</c><00:00:54.680><c> that's</c><00:00:54.879><c> all</c><00:00:55.120><c> there</c><00:00:55.239><c> is</c><00:00:55.440><c> to</c><00:00:55.640><c> it</c>

00:00:56.590 --> 00:00:56.600 align:start position:0%
this that's it that's all there is to it
 

00:00:56.600 --> 00:00:58.630 align:start position:0%
this that's it that's all there is to it
just<00:00:56.879><c> add</c><00:00:57.039><c> your</c><00:00:57.239><c> grock</c><00:00:57.600><c> API</c><00:00:58.039><c> key</c><00:00:58.280><c> to</c><00:00:58.480><c> the</c>

00:00:58.630 --> 00:00:58.640 align:start position:0%
just add your grock API key to the
 

00:00:58.640 --> 00:01:01.029 align:start position:0%
just add your grock API key to the
environment<00:00:59.359><c> and</c><00:00:59.480><c> you're</c><00:00:59.680><c> all</c><00:01:00.160><c> set</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
environment and you're all set
 

00:01:01.039 --> 00:01:02.670 align:start position:0%
environment and you're all set
developers<00:01:01.559><c> can</c><00:01:01.719><c> call</c><00:01:02.000><c> grock's</c><00:01:02.399><c> chat</c>

00:01:02.670 --> 00:01:02.680 align:start position:0%
developers can call grock's chat
 

00:01:02.680 --> 00:01:05.830 align:start position:0%
developers can call grock's chat
completion<00:01:03.320><c> streaming</c><00:01:04.119><c> Json</c><00:01:04.960><c> tool</c><00:01:05.280><c> use</c><00:01:05.640><c> and</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
completion streaming Json tool use and
 

00:01:05.840 --> 00:01:08.310 align:start position:0%
completion streaming Json tool use and
more<00:01:06.799><c> and</c><00:01:06.960><c> thanks</c><00:01:07.200><c> to</c><00:01:07.400><c> pocket</c><00:01:07.640><c> grock</c><00:01:08.080><c> they</c><00:01:08.159><c> can</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
more and thanks to pocket grock they can
 

00:01:08.320 --> 00:01:10.950 align:start position:0%
more and thanks to pocket grock they can
do<00:01:08.439><c> it</c><00:01:08.600><c> with</c><00:01:08.840><c> just</c><00:01:09.040><c> a</c><00:01:09.200><c> few</c><00:01:09.479><c> lines</c><00:01:09.759><c> of</c><00:01:09.960><c> code</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
do it with just a few lines of code
 

00:01:10.960 --> 00:01:12.390 align:start position:0%
do it with just a few lines of code
we've<00:01:11.200><c> actually</c><00:01:11.439><c> replaced</c><00:01:11.880><c> the</c><00:01:12.000><c> entire</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
we've actually replaced the entire
 

00:01:12.400 --> 00:01:14.270 align:start position:0%
we've actually replaced the entire
provider<00:01:12.759><c> file</c><00:01:13.040><c> in</c><00:01:13.159><c> our</c><00:01:13.320><c> grole</c><00:01:13.720><c> web</c><00:01:14.000><c> search</c>

00:01:14.270 --> 00:01:14.280 align:start position:0%
provider file in our grole web search
 

00:01:14.280 --> 00:01:16.910 align:start position:0%
provider file in our grole web search
API<00:01:15.080><c> with</c><00:01:15.200><c> a</c><00:01:15.320><c> simple</c><00:01:15.600><c> call</c><00:01:15.799><c> the</c><00:01:16.000><c> pocket</c><00:01:16.280><c> groc</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
API with a simple call the pocket groc
 

00:01:16.920 --> 00:01:19.350 align:start position:0%
API with a simple call the pocket groc
it's<00:01:17.119><c> online</c><00:01:17.560><c> now</c><00:01:17.759><c> and</c><00:01:17.920><c> running</c><00:01:18.280><c> great</c><00:01:18.840><c> pocket</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
it's online now and running great pocket
 

00:01:19.360 --> 00:01:22.870 align:start position:0%
it's online now and running great pocket
Croc<00:01:19.680><c> a</c><00:01:19.880><c> croc</c><00:01:20.320><c> in</c><00:01:20.479><c> your</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
 
 

00:01:22.880 --> 00:01:28.390 align:start position:0%
 
pocket<00:01:23.880><c> pocket</c><00:01:24.439><c> Croc</c><00:01:24.960><c> COC</c><00:01:25.400><c> in</c><00:01:25.600><c> your</c>

00:01:28.390 --> 00:01:28.400 align:start position:0%
 
 

00:01:28.400 --> 00:01:32.429 align:start position:0%
 
pocket<00:01:29.400><c> KN</c><00:01:29.640><c> your</c><00:01:30.040><c> thing</c><00:01:30.759><c> cool</c><00:01:31.119><c> take</c><00:01:31.360><c> a</c><00:01:31.640><c> hike</c>

00:01:32.429 --> 00:01:32.439 align:start position:0%
pocket KN your thing cool take a hike
 

00:01:32.439 --> 00:01:36.149 align:start position:0%
pocket KN your thing cool take a hike
otherwise<00:01:33.159><c> subscribe</c><00:01:33.799><c> and</c><00:01:34.119><c> like</c><00:01:34.840><c> to</c><00:01:35.280><c> AI</c><00:01:35.759><c> tips</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
otherwise subscribe and like to AI tips
 

00:01:36.159 --> 00:01:37.789 align:start position:0%
otherwise subscribe and like to AI tips
with

00:01:37.789 --> 00:01:37.799 align:start position:0%
with
 

00:01:37.799 --> 00:01:43.429 align:start position:0%
with
J<00:01:38.799><c> AI</c><00:01:39.280><c> tips</c><00:01:39.640><c> with</c><00:01:39.880><c> J</c><00:01:40.640><c> hooray</c><00:01:41.159><c> a</c><00:01:41.320><c> i</c><00:01:41.680><c> a</c><00:01:41.880><c> i</c><00:01:42.240><c> a</c><00:01:42.399><c> i</c><00:01:42.720><c> a</c><00:01:42.960><c> i</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
J AI tips with J hooray a i a i a i a i
 

00:01:43.439 --> 00:01:45.510 align:start position:0%
J AI tips with J hooray a i a i a i a i
AI<00:01:43.920><c> tips</c><00:01:44.320><c> with</c>

00:01:45.510 --> 00:01:45.520 align:start position:0%
AI tips with
 

00:01:45.520 --> 00:01:48.230 align:start position:0%
AI tips with
J<00:01:46.520><c> AI</c><00:01:46.799><c> tips</c><00:01:47.040><c> with</c><00:01:47.159><c> J</c><00:01:47.399><c> is</c><00:01:47.479><c> a</c><00:01:47.680><c> copyrighted</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
J AI tips with J is a copyrighted
 

00:01:48.240 --> 00:01:49.469 align:start position:0%
J AI tips with J is a copyrighted
production<00:01:48.640><c> of</c>

00:01:49.469 --> 00:01:49.479 align:start position:0%
production of
 

00:01:49.479 --> 00:01:53.590 align:start position:0%
production of
j.g.<00:01:50.479><c> us</c><00:01:51.280><c> all</c><00:01:51.479><c> rights</c><00:01:51.759><c> reserved</c><00:01:52.240><c> by</c><00:01:52.799><c> AI</c><00:01:53.280><c> tips</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
j.g. us all rights reserved by AI tips
 

00:01:53.600 --> 00:01:56.799 align:start position:0%
j.g. us all rights reserved by AI tips
with<00:01:53.799><c> j</c>

