WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:01.880 align:start position:0%
 
welcome<00:00:00.719><c> to</c><00:00:00.900><c> another</c><00:00:01.079><c> data</c><00:00:01.319><c> structure</c><00:00:01.530><c> video</c>

00:00:01.880 --> 00:00:01.890 align:start position:0%
welcome to another data structure video
 

00:00:01.890 --> 00:00:03.470 align:start position:0%
welcome to another data structure video
and<00:00:02.190><c> this</c><00:00:02.550><c> time</c><00:00:02.700><c> around</c><00:00:02.790><c> we're</c><00:00:03.120><c> looking</c><00:00:03.360><c> at</c>

00:00:03.470 --> 00:00:03.480 align:start position:0%
and this time around we're looking at
 

00:00:03.480 --> 00:00:05.360 align:start position:0%
and this time around we're looking at
skip<00:00:03.720><c> lists</c><00:00:04.020><c> now</c><00:00:04.740><c> if</c><00:00:04.799><c> you</c><00:00:04.890><c> haven't</c><00:00:05.009><c> seen</c><00:00:05.250><c> as</c>

00:00:05.360 --> 00:00:05.370 align:start position:0%
skip lists now if you haven't seen as
 

00:00:05.370 --> 00:00:06.829 align:start position:0%
skip lists now if you haven't seen as
before<00:00:05.700><c> or</c><00:00:05.970><c> are</c><00:00:06.390><c> just</c><00:00:06.569><c> starting</c><00:00:06.690><c> to</c>

00:00:06.829 --> 00:00:06.839 align:start position:0%
before or are just starting to
 

00:00:06.839 --> 00:00:08.600 align:start position:0%
before or are just starting to
understand<00:00:07.259><c> it</c><00:00:07.379><c> then</c><00:00:07.890><c> as</c><00:00:08.040><c> you</c><00:00:08.160><c> can</c><00:00:08.280><c> see</c><00:00:08.429><c> look</c>

00:00:08.600 --> 00:00:08.610 align:start position:0%
understand it then as you can see look
 

00:00:08.610 --> 00:00:10.070 align:start position:0%
understand it then as you can see look
this<00:00:08.910><c> looks</c><00:00:09.150><c> very</c><00:00:09.480><c> different</c><00:00:09.750><c> to</c><00:00:09.929><c> where</c>

00:00:10.070 --> 00:00:10.080 align:start position:0%
this looks very different to where
 

00:00:10.080 --> 00:00:11.000 align:start position:0%
this looks very different to where
you've<00:00:10.170><c> probably</c><00:00:10.290><c> seen</c><00:00:10.530><c> before</c>

00:00:11.000 --> 00:00:11.010 align:start position:0%
you've probably seen before
 

00:00:11.010 --> 00:00:12.709 align:start position:0%
you've probably seen before
so<00:00:11.730><c> let's</c><00:00:11.849><c> take</c><00:00:11.969><c> a</c><00:00:12.030><c> couple</c><00:00:12.210><c> minutes</c><00:00:12.330><c> to</c><00:00:12.540><c> break</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
so let's take a couple minutes to break
 

00:00:12.719 --> 00:00:14.680 align:start position:0%
so let's take a couple minutes to break
it<00:00:12.870><c> down</c><00:00:13.019><c> so</c><00:00:13.380><c> it</c><00:00:13.469><c> doesn't</c><00:00:13.500><c> seem</c><00:00:13.799><c> so</c><00:00:14.009><c> daunting</c>

00:00:14.680 --> 00:00:14.690 align:start position:0%
it down so it doesn't seem so daunting
 

00:00:14.690 --> 00:00:17.630 align:start position:0%
it down so it doesn't seem so daunting
first<00:00:15.690><c> this</c><00:00:16.230><c> is</c><00:00:16.289><c> a</c><00:00:16.440><c> sort</c><00:00:16.830><c> of</c><00:00:16.890><c> map</c><00:00:17.100><c> that</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
first this is a sort of map that
 

00:00:17.640 --> 00:00:20.029 align:start position:0%
first this is a sort of map that
traverses<00:00:18.210><c> like</c><00:00:18.630><c> a</c><00:00:18.660><c> linked</c><00:00:19.080><c> list</c><00:00:19.140><c> you</c><00:00:19.949><c> can</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
traverses like a linked list you can
 

00:00:20.039 --> 00:00:21.620 align:start position:0%
traverses like a linked list you can
actually<00:00:20.160><c> instantiate</c><00:00:20.580><c> a</c><00:00:20.820><c> skip</c><00:00:21.029><c> list</c><00:00:21.210><c> in</c><00:00:21.359><c> Java</c>

00:00:21.620 --> 00:00:21.630 align:start position:0%
actually instantiate a skip list in Java
 

00:00:21.630 --> 00:00:25.279 align:start position:0%
actually instantiate a skip list in Java
with<00:00:22.410><c> the</c><00:00:22.529><c> concurrent</c><00:00:23.039><c> skip</c><00:00:23.279><c> list</c><00:00:23.519><c> map</c><00:00:24.230><c> as</c><00:00:25.230><c> you</c>

00:00:25.279 --> 00:00:25.289 align:start position:0%
with the concurrent skip list map as you
 

00:00:25.289 --> 00:00:27.830 align:start position:0%
with the concurrent skip list map as you
can<00:00:25.590><c> tell</c><00:00:25.769><c> it's</c><00:00:26.519><c> sorted</c><00:00:27.000><c> from</c><00:00:27.210><c> left</c><00:00:27.510><c> to</c><00:00:27.570><c> right</c>

00:00:27.830 --> 00:00:27.840 align:start position:0%
can tell it's sorted from left to right
 

00:00:27.840 --> 00:00:29.750 align:start position:0%
can tell it's sorted from left to right
an<00:00:28.050><c> ascending</c><00:00:28.500><c> order</c><00:00:28.710><c> that's</c><00:00:29.519><c> where</c><00:00:29.699><c> the</c>

00:00:29.750 --> 00:00:29.760 align:start position:0%
an ascending order that's where the
 

00:00:29.760 --> 00:00:31.759 align:start position:0%
an ascending order that's where the
sorting<00:00:30.060><c> part</c><00:00:30.240><c> comes</c><00:00:30.420><c> in</c><00:00:30.599><c> and</c><00:00:30.869><c> it's</c><00:00:31.410><c> a</c><00:00:31.470><c> map</c>

00:00:31.759 --> 00:00:31.769 align:start position:0%
sorting part comes in and it's a map
 

00:00:31.769 --> 00:00:34.250 align:start position:0%
sorting part comes in and it's a map
because<00:00:32.309><c> each</c><00:00:32.520><c> entry</c><00:00:33.090><c> is</c><00:00:33.270><c> a</c><00:00:33.329><c> key</c><00:00:33.690><c> value</c><00:00:33.719><c> pair</c>

00:00:34.250 --> 00:00:34.260 align:start position:0%
because each entry is a key value pair
 

00:00:34.260 --> 00:00:35.930 align:start position:0%
because each entry is a key value pair
or<00:00:34.559><c> I'm</c><00:00:34.770><c> just</c><00:00:34.950><c> showing</c><00:00:35.309><c> the</c><00:00:35.399><c> keys</c><00:00:35.640><c> for</c><00:00:35.820><c> this</c>

00:00:35.930 --> 00:00:35.940 align:start position:0%
or I'm just showing the keys for this
 

00:00:35.940 --> 00:00:38.930 align:start position:0%
or I'm just showing the keys for this
example<00:00:36.920><c> now</c><00:00:37.920><c> it's</c><00:00:38.280><c> like</c><00:00:38.460><c> a</c><00:00:38.489><c> linked</c><00:00:38.850><c> list</c>

00:00:38.930 --> 00:00:38.940 align:start position:0%
example now it's like a linked list
 

00:00:38.940 --> 00:00:40.910 align:start position:0%
example now it's like a linked list
because<00:00:39.480><c> each</c><00:00:39.750><c> of</c><00:00:39.809><c> these</c><00:00:40.079><c> entries</c><00:00:40.530><c> are</c><00:00:40.680><c> nodes</c>

00:00:40.910 --> 00:00:40.920 align:start position:0%
because each of these entries are nodes
 

00:00:40.920 --> 00:00:42.950 align:start position:0%
because each of these entries are nodes
every<00:00:41.489><c> node</c><00:00:41.940><c> has</c><00:00:41.969><c> a</c><00:00:42.270><c> reference</c><00:00:42.480><c> to</c><00:00:42.750><c> other</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
every node has a reference to other
 

00:00:42.960 --> 00:00:45.709 align:start position:0%
every node has a reference to other
nodes<00:00:43.230><c> to</c><00:00:43.620><c> the</c><00:00:43.649><c> right</c><00:00:43.950><c> to</c><00:00:44.460><c> the</c><00:00:44.579><c> left</c><00:00:44.760><c> above</c><00:00:45.420><c> in</c>

00:00:45.709 --> 00:00:45.719 align:start position:0%
nodes to the right to the left above in
 

00:00:45.719 --> 00:00:48.290 align:start position:0%
nodes to the right to the left above in
below<00:00:46.170><c> as</c><00:00:46.440><c> well</c><00:00:47.309><c> as</c><00:00:47.430><c> holding</c><00:00:47.730><c> the</c><00:00:47.820><c> key</c><00:00:48.000><c> value</c>

00:00:48.290 --> 00:00:48.300 align:start position:0%
below as well as holding the key value
 

00:00:48.300 --> 00:00:51.200 align:start position:0%
below as well as holding the key value
pairs<00:00:49.160><c> some</c><00:00:50.160><c> of</c><00:00:50.250><c> these</c><00:00:50.309><c> no</c><00:00:50.460><c> differences</c><00:00:50.910><c> can</c>

00:00:51.200 --> 00:00:51.210 align:start position:0%
pairs some of these no differences can
 

00:00:51.210 --> 00:00:52.970 align:start position:0%
pairs some of these no differences can
certainly<00:00:51.629><c> be</c><00:00:51.809><c> null</c><00:00:52.050><c> so</c><00:00:52.469><c> don't</c><00:00:52.620><c> forget</c><00:00:52.710><c> that</c>

00:00:52.970 --> 00:00:52.980 align:start position:0%
certainly be null so don't forget that
 

00:00:52.980 --> 00:00:55.220 align:start position:0%
certainly be null so don't forget that
all<00:00:53.820><c> right</c><00:00:54.180><c> so</c><00:00:54.390><c> let's</c><00:00:54.539><c> take</c><00:00:54.660><c> a</c><00:00:54.690><c> look</c><00:00:54.899><c> at</c><00:00:54.989><c> a</c><00:00:55.020><c> row</c>

00:00:55.220 --> 00:00:55.230 align:start position:0%
all right so let's take a look at a row
 

00:00:55.230 --> 00:00:58.099 align:start position:0%
all right so let's take a look at a row
of<00:00:55.379><c> data</c><00:00:55.530><c> this</c><00:00:56.489><c> is</c><00:00:56.699><c> called</c><00:00:56.910><c> a</c><00:00:56.969><c> level</c><00:00:57.180><c> and</c><00:00:57.600><c> the</c>

00:00:58.099 --> 00:00:58.109 align:start position:0%
of data this is called a level and the
 

00:00:58.109 --> 00:00:59.479 align:start position:0%
of data this is called a level and the
highest<00:00:58.469><c> level</c><00:00:58.680><c> determines</c><00:00:59.070><c> the</c><00:00:59.219><c> height</c><00:00:59.340><c> of</c>

00:00:59.479 --> 00:00:59.489 align:start position:0%
highest level determines the height of
 

00:00:59.489 --> 00:01:02.060 align:start position:0%
highest level determines the height of
the<00:00:59.579><c> Skip</c><00:00:59.820><c> list</c><00:01:00.079><c> so</c><00:01:01.079><c> the</c><00:01:01.199><c> height</c><00:01:01.410><c> here</c><00:01:01.649><c> is</c><00:01:01.680><c> 5</c>

00:01:02.060 --> 00:01:02.070 align:start position:0%
the Skip list so the height here is 5
 

00:01:02.070 --> 00:01:05.179 align:start position:0%
the Skip list so the height here is 5
because<00:01:02.789><c> the</c><00:01:02.879><c> highest</c><00:01:03.059><c> level</c><00:01:03.480><c> is</c><00:01:03.570><c> S</c><00:01:03.719><c> sub</c><00:01:03.780><c> 5</c><00:01:04.320><c> the</c>

00:01:05.179 --> 00:01:05.189 align:start position:0%
because the highest level is S sub 5 the
 

00:01:05.189 --> 00:01:08.240 align:start position:0%
because the highest level is S sub 5 the
bottom<00:01:05.549><c> level</c><00:01:05.700><c> is</c><00:01:05.970><c> s</c><00:01:06.330><c> sub</c><00:01:06.540><c> 0</c><00:01:06.780><c> the</c><00:01:07.250><c> negative</c>

00:01:08.240 --> 00:01:08.250 align:start position:0%
bottom level is s sub 0 the negative
 

00:01:08.250 --> 00:01:10.250 align:start position:0%
bottom level is s sub 0 the negative
infinity<00:01:08.460><c> and</c><00:01:08.850><c> positive</c><00:01:08.909><c> infinity</c><00:01:09.270><c> nodes</c><00:01:09.990><c> are</c>

00:01:10.250 --> 00:01:10.260 align:start position:0%
infinity and positive infinity nodes are
 

00:01:10.260 --> 00:01:11.990 align:start position:0%
infinity and positive infinity nodes are
called<00:01:10.560><c> Sentinel</c><00:01:11.010><c> nodes</c><00:01:11.159><c> and</c><00:01:11.460><c> they</c><00:01:11.880><c> never</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
called Sentinel nodes and they never
 

00:01:12.000 --> 00:01:15.950 align:start position:0%
called Sentinel nodes and they never
change<00:01:13.040><c> every</c><00:01:14.040><c> column</c><00:01:14.549><c> is</c><00:01:14.939><c> called</c><00:01:15.240><c> a</c><00:01:15.360><c> tower</c><00:01:15.630><c> as</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
change every column is called a tower as
 

00:01:15.960 --> 00:01:18.530 align:start position:0%
change every column is called a tower as
you<00:01:16.710><c> can</c><00:01:16.830><c> see</c><00:01:17.070><c> every</c><00:01:17.640><c> tower</c><00:01:17.850><c> holds</c><00:01:18.090><c> the</c><00:01:18.299><c> same</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
you can see every tower holds the same
 

00:01:18.540 --> 00:01:20.600 align:start position:0%
you can see every tower holds the same
key<00:01:18.869><c> and</c><00:01:19.170><c> we</c><00:01:19.799><c> will</c><00:01:19.890><c> discuss</c><00:01:20.280><c> that</c><00:01:20.310><c> in</c><00:01:20.520><c> the</c>

00:01:20.600 --> 00:01:20.610 align:start position:0%
key and we will discuss that in the
 

00:01:20.610 --> 00:01:22.390 align:start position:0%
key and we will discuss that in the
video<00:01:20.880><c> explaining</c><00:01:21.270><c> insertion</c><00:01:21.840><c> and</c><00:01:22.020><c> deletion</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
video explaining insertion and deletion
 

00:01:22.400 --> 00:01:24.710 align:start position:0%
video explaining insertion and deletion
one<00:01:23.400><c> last</c><00:01:23.610><c> thing</c><00:01:23.850><c> for</c><00:01:24.060><c> you</c><00:01:24.150><c> into</c><00:01:24.330><c> an</c><00:01:24.420><c> example</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
one last thing for you into an example
 

00:01:24.720 --> 00:01:27.410 align:start position:0%
one last thing for you into an example
is<00:01:25.049><c> that</c><00:01:25.590><c> every</c><00:01:25.950><c> level</c><00:01:26.220><c> has</c><00:01:26.670><c> about</c><00:01:27.000><c> half</c><00:01:27.299><c> of</c>

00:01:27.410 --> 00:01:27.420 align:start position:0%
is that every level has about half of
 

00:01:27.420 --> 00:01:29.270 align:start position:0%
is that every level has about half of
the<00:01:27.540><c> nodes</c><00:01:27.750><c> of</c><00:01:27.840><c> the</c><00:01:27.960><c> one</c><00:01:28.110><c> below</c><00:01:28.290><c> this</c><00:01:29.130><c> is</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
the nodes of the one below this is
 

00:01:29.280 --> 00:01:30.859 align:start position:0%
the nodes of the one below this is
because<00:01:29.520><c> skip</c><00:01:29.790><c> lists</c><00:01:30.090><c> are</c><00:01:30.210><c> probabilistic</c>

00:01:30.859 --> 00:01:30.869 align:start position:0%
because skip lists are probabilistic
 

00:01:30.869 --> 00:01:33.080 align:start position:0%
because skip lists are probabilistic
data<00:01:31.229><c> structure</c><00:01:31.650><c> and</c><00:01:31.860><c> this</c><00:01:32.430><c> all</c><00:01:32.640><c> happens</c>

00:01:33.080 --> 00:01:33.090 align:start position:0%
data structure and this all happens
 

00:01:33.090 --> 00:01:35.450 align:start position:0%
data structure and this all happens
during<00:01:33.270><c> insertion</c><00:01:33.979><c> essentially</c><00:01:34.979><c> we</c><00:01:35.130><c> flip</c><00:01:35.340><c> a</c>

00:01:35.450 --> 00:01:35.460 align:start position:0%
during insertion essentially we flip a
 

00:01:35.460 --> 00:01:37.340 align:start position:0%
during insertion essentially we flip a
coin<00:01:35.490><c> and</c><00:01:36.360><c> increase</c><00:01:36.689><c> the</c><00:01:36.810><c> size</c><00:01:37.020><c> of</c><00:01:37.079><c> the</c><00:01:37.170><c> tower</c>

00:01:37.340 --> 00:01:37.350 align:start position:0%
coin and increase the size of the tower
 

00:01:37.350 --> 00:01:39.910 align:start position:0%
coin and increase the size of the tower
if<00:01:37.470><c> it's</c><00:01:37.619><c> heads</c><00:01:37.799><c> and</c><00:01:38.130><c> stop</c><00:01:38.640><c> with</c><00:01:38.850><c> his</c><00:01:38.970><c> tails</c>

00:01:39.910 --> 00:01:39.920 align:start position:0%
if it's heads and stop with his tails
 

00:01:39.920 --> 00:01:43.700 align:start position:0%
if it's heads and stop with his tails
all<00:01:40.920><c> right</c><00:01:41.189><c> so</c><00:01:41.579><c> now</c><00:01:41.700><c> let's</c><00:01:41.759><c> do</c><00:01:41.970><c> a</c><00:01:42.000><c> search</c><00:01:42.270><c> ok</c><00:01:43.200><c> so</c>

00:01:43.700 --> 00:01:43.710 align:start position:0%
all right so now let's do a search ok so
 

00:01:43.710 --> 00:01:46.639 align:start position:0%
all right so now let's do a search ok so
let's<00:01:44.040><c> say</c><00:01:44.130><c> we're</c><00:01:44.280><c> searching</c><00:01:44.670><c> for</c><00:01:45.030><c> a</c><00:01:45.869><c> key</c><00:01:46.470><c> of</c>

00:01:46.639 --> 00:01:46.649 align:start position:0%
let's say we're searching for a key of
 

00:01:46.649 --> 00:01:49.490 align:start position:0%
let's say we're searching for a key of
50<00:01:47.040><c> all</c><00:01:47.880><c> right</c><00:01:48.270><c> so</c><00:01:48.509><c> we</c><00:01:48.630><c> can</c><00:01:48.659><c> see</c><00:01:48.899><c> key</c><00:01:49.140><c> 50</c>

00:01:49.490 --> 00:01:49.500 align:start position:0%
50 all right so we can see key 50
 

00:01:49.500 --> 00:01:52.969 align:start position:0%
50 all right so we can see key 50
already<00:01:50.369><c> but</c><00:01:50.790><c> how</c><00:01:51.180><c> do</c><00:01:51.240><c> we</c><00:01:51.299><c> get</c><00:01:51.509><c> there</c><00:01:51.829><c> well</c><00:01:52.829><c> we</c>

00:01:52.969 --> 00:01:52.979 align:start position:0%
already but how do we get there well we
 

00:01:52.979 --> 00:01:55.520 align:start position:0%
already but how do we get there well we
always<00:01:53.310><c> start</c><00:01:53.610><c> at</c><00:01:53.880><c> the</c><00:01:54.360><c> top</c><00:01:54.600><c> left</c><00:01:54.659><c> node</c><00:01:55.290><c> in</c><00:01:55.439><c> the</c>

00:01:55.520 --> 00:01:55.530 align:start position:0%
always start at the top left node in the
 

00:01:55.530 --> 00:01:58.039 align:start position:0%
always start at the top left node in the
Skip<00:01:55.710><c> list</c><00:01:55.920><c> the</c><00:01:56.850><c> reason</c><00:01:57.149><c> is</c><00:01:57.240><c> because</c><00:01:57.360><c> this</c><00:01:57.570><c> is</c>

00:01:58.039 --> 00:01:58.049 align:start position:0%
Skip list the reason is because this is
 

00:01:58.049 --> 00:02:00.410 align:start position:0%
Skip list the reason is because this is
the<00:01:58.079><c> highest</c><00:01:58.469><c> level</c><00:01:58.680><c> in</c><00:01:59.430><c> this</c><00:01:59.909><c> the</c><00:02:00.090><c> smallest</c>

00:02:00.410 --> 00:02:00.420 align:start position:0%
the highest level in this the smallest
 

00:02:00.420 --> 00:02:03.530 align:start position:0%
the highest level in this the smallest
key<00:02:00.840><c> value</c><00:02:01.259><c> on</c><00:02:01.530><c> that</c><00:02:02.219><c> level</c><00:02:02.490><c> so</c><00:02:03.180><c> it's</c><00:02:03.420><c> always</c>

00:02:03.530 --> 00:02:03.540 align:start position:0%
key value on that level so it's always
 

00:02:03.540 --> 00:02:04.969 align:start position:0%
key value on that level so it's always
been<00:02:03.750><c> negative</c><00:02:04.020><c> infinity</c><00:02:04.380><c> on</c><00:02:04.560><c> the</c><00:02:04.829><c> highest</c>

00:02:04.969 --> 00:02:04.979 align:start position:0%
been negative infinity on the highest
 

00:02:04.979 --> 00:02:08.029 align:start position:0%
been negative infinity on the highest
level<00:02:05.130><c> so</c><00:02:05.579><c> we</c><00:02:05.670><c> always</c><00:02:05.820><c> start</c><00:02:06.469><c> so</c><00:02:07.469><c> the</c><00:02:07.829><c> first</c>

00:02:08.029 --> 00:02:08.039 align:start position:0%
level so we always start so the first
 

00:02:08.039 --> 00:02:09.680 align:start position:0%
level so we always start so the first
thing<00:02:08.190><c> we</c><00:02:08.280><c> do</c><00:02:08.429><c> is</c><00:02:08.670><c> we</c><00:02:09.000><c> see</c><00:02:09.179><c> if</c><00:02:09.270><c> we</c><00:02:09.330><c> can</c><00:02:09.509><c> drop</c>

00:02:09.680 --> 00:02:09.690 align:start position:0%
thing we do is we see if we can drop
 

00:02:09.690 --> 00:02:12.470 align:start position:0%
thing we do is we see if we can drop
down<00:02:09.869><c> and</c><00:02:10.200><c> we</c><00:02:10.619><c> can</c><00:02:10.770><c> drop</c><00:02:10.950><c> down</c><00:02:11.009><c> if</c><00:02:11.489><c> the</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
down and we can drop down if the
 

00:02:12.480 --> 00:02:15.559 align:start position:0%
down and we can drop down if the
the<00:02:13.140><c> below</c><00:02:13.470><c> reference</c><00:02:13.920><c> of</c><00:02:14.190><c> this</c><00:02:14.730><c> node</c><00:02:15.030><c> is</c><00:02:15.390><c> not</c>

00:02:15.559 --> 00:02:15.569 align:start position:0%
the below reference of this node is not
 

00:02:15.569 --> 00:02:17.750 align:start position:0%
the below reference of this node is not
known<00:02:15.840><c> and</c><00:02:16.200><c> it's</c><00:02:16.709><c> not</c><00:02:16.890><c> because</c><00:02:17.489><c> it</c><00:02:17.610><c> holds</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
known and it's not because it holds
 

00:02:17.760 --> 00:02:20.509 align:start position:0%
known and it's not because it holds
negative<00:02:18.150><c> infinity</c><00:02:18.950><c> ok</c><00:02:19.950><c> so</c><00:02:20.010><c> now</c><00:02:20.220><c> that</c><00:02:20.250><c> we're</c>

00:02:20.509 --> 00:02:20.519 align:start position:0%
negative infinity ok so now that we're
 

00:02:20.519 --> 00:02:23.030 align:start position:0%
negative infinity ok so now that we're
down<00:02:20.730><c> a</c><00:02:21.060><c> level</c><00:02:21.239><c> once</c><00:02:22.050><c> you</c><00:02:22.170><c> go</c><00:02:22.319><c> down</c><00:02:22.349><c> level</c><00:02:22.800><c> you</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
down a level once you go down level you
 

00:02:23.040 --> 00:02:24.380 align:start position:0%
down a level once you go down level you
always<00:02:23.310><c> try</c><00:02:23.459><c> and</c><00:02:23.580><c> scan</c><00:02:23.790><c> forward</c><00:02:24.150><c> as</c><00:02:24.209><c> much</c><00:02:24.269><c> as</c>

00:02:24.380 --> 00:02:24.390 align:start position:0%
always try and scan forward as much as
 

00:02:24.390 --> 00:02:26.630 align:start position:0%
always try and scan forward as much as
you<00:02:24.510><c> can</c><00:02:24.720><c> and</c><00:02:24.959><c> then</c><00:02:25.560><c> go</c><00:02:25.739><c> down</c><00:02:25.920><c> again</c><00:02:26.190><c> and</c><00:02:26.459><c> then</c>

00:02:26.630 --> 00:02:26.640 align:start position:0%
you can and then go down again and then
 

00:02:26.640 --> 00:02:28.670 align:start position:0%
you can and then go down again and then
scan<00:02:26.940><c> forward</c><00:02:27.360><c> until</c><00:02:27.959><c> you</c><00:02:28.110><c> can't</c><00:02:28.349><c> anymore</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
scan forward until you can't anymore
 

00:02:28.680 --> 00:02:29.990 align:start position:0%
scan forward until you can't anymore
and<00:02:28.860><c> you</c><00:02:29.040><c> keep</c><00:02:29.250><c> we</c><00:02:29.519><c> keep</c><00:02:29.670><c> repeating</c><00:02:29.849><c> this</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
and you keep we keep repeating this
 

00:02:30.000 --> 00:02:35.720 align:start position:0%
and you keep we keep repeating this
process<00:02:31.819><c> so</c><00:02:33.140><c> we</c><00:02:34.140><c> now</c><00:02:34.980><c> that</c><00:02:35.130><c> we</c><00:02:35.220><c> went</c><00:02:35.550><c> down</c><00:02:35.700><c> a</c>

00:02:35.720 --> 00:02:35.730 align:start position:0%
process so we now that we went down a
 

00:02:35.730 --> 00:02:39.289 align:start position:0%
process so we now that we went down a
level<00:02:35.910><c> we'll</c><00:02:36.690><c> go</c><00:02:36.840><c> forward</c><00:02:37.170><c> and</c><00:02:37.440><c> say</c><00:02:37.590><c> hey</c><00:02:37.620><c> is</c><00:02:38.299><c> 17</c>

00:02:39.289 --> 00:02:39.299 align:start position:0%
level we'll go forward and say hey is 17
 

00:02:39.299 --> 00:02:43.690 align:start position:0%
level we'll go forward and say hey is 17
less<00:02:39.810><c> than</c><00:02:39.989><c> 50</c><00:02:40.349><c> it</c><00:02:40.560><c> is</c><00:02:41.099><c> so</c><00:02:41.940><c> we</c><00:02:42.180><c> can</c><00:02:42.360><c> go</c><00:02:42.480><c> forward</c>

00:02:43.690 --> 00:02:43.700 align:start position:0%
less than 50 it is so we can go forward
 

00:02:43.700 --> 00:02:45.890 align:start position:0%
less than 50 it is so we can go forward
we<00:02:44.700><c> really</c><00:02:44.730><c> we're</c><00:02:45.180><c> currently</c><00:02:45.299><c> going</c><00:02:45.510><c> to</c><00:02:45.569><c> stop</c>

00:02:45.890 --> 00:02:45.900 align:start position:0%
we really we're currently going to stop
 

00:02:45.900 --> 00:02:49.430 align:start position:0%
we really we're currently going to stop
going<00:02:46.170><c> forward</c><00:02:46.560><c> if</c><00:02:46.709><c> the</c><00:02:47.489><c> next</c><00:02:48.410><c> the</c><00:02:49.410><c> next</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
going forward if the next the next
 

00:02:49.440 --> 00:02:52.430 align:start position:0%
going forward if the next the next
reference<00:02:50.160><c> on</c><00:02:50.519><c> that</c><00:02:51.150><c> level</c><00:02:51.480><c> is</c><00:02:51.690><c> higher</c><00:02:52.230><c> than</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
reference on that level is higher than
 

00:02:52.440 --> 00:02:54.770 align:start position:0%
reference on that level is higher than
the<00:02:52.560><c> key</c><00:02:52.739><c> we're</c><00:02:52.890><c> searching</c><00:02:53.160><c> for</c><00:02:53.340><c> so</c><00:02:54.060><c> now</c><00:02:54.660><c> we're</c>

00:02:54.770 --> 00:02:54.780 align:start position:0%
the key we're searching for so now we're
 

00:02:54.780 --> 00:02:55.520 align:start position:0%
the key we're searching for so now we're
at<00:02:54.840><c> 17</c>

00:02:55.520 --> 00:02:55.530 align:start position:0%
at 17
 

00:02:55.530 --> 00:02:57.559 align:start position:0%
at 17
the<00:02:55.920><c> next</c><00:02:56.220><c> reference</c><00:02:56.670><c> is</c><00:02:57.060><c> positive</c><00:02:57.450><c> infinity</c>

00:02:57.559 --> 00:02:57.569 align:start position:0%
the next reference is positive infinity
 

00:02:57.569 --> 00:03:00.050 align:start position:0%
the next reference is positive infinity
that's<00:02:58.470><c> higher</c><00:02:58.739><c> than</c><00:02:58.950><c> 50</c><00:02:59.250><c> so</c><00:02:59.519><c> we</c><00:02:59.640><c> can't</c><00:02:59.910><c> go</c>

00:03:00.050 --> 00:03:00.060 align:start position:0%
that's higher than 50 so we can't go
 

00:03:00.060 --> 00:03:00.830 align:start position:0%
that's higher than 50 so we can't go
forward<00:03:00.239><c> any</c><00:03:00.510><c> more</c>

00:03:00.830 --> 00:03:00.840 align:start position:0%
forward any more
 

00:03:00.840 --> 00:03:03.710 align:start position:0%
forward any more
all<00:03:01.680><c> right</c><00:03:01.920><c> so</c><00:03:02.220><c> now</c><00:03:03.000><c> we</c><00:03:03.060><c> can't</c><00:03:03.329><c> afford</c><00:03:03.420><c> any</c>

00:03:03.710 --> 00:03:03.720 align:start position:0%
all right so now we can't afford any
 

00:03:03.720 --> 00:03:05.479 align:start position:0%
all right so now we can't afford any
more<00:03:03.870><c> we're</c><00:03:04.709><c> gonna</c><00:03:04.860><c> try</c><00:03:04.980><c> and</c><00:03:05.099><c> drop</c><00:03:05.190><c> down</c><00:03:05.430><c> a</c>

00:03:05.479 --> 00:03:05.489 align:start position:0%
more we're gonna try and drop down a
 

00:03:05.489 --> 00:03:05.809 align:start position:0%
more we're gonna try and drop down a
level

00:03:05.809 --> 00:03:05.819 align:start position:0%
level
 

00:03:05.819 --> 00:03:09.259 align:start position:0%
level
so<00:03:06.599><c> we'll</c><00:03:06.750><c> say</c><00:03:06.930><c> okay</c><00:03:07.410><c> is</c><00:03:07.620><c> the</c><00:03:08.579><c> below</c><00:03:08.819><c> reference</c>

00:03:09.259 --> 00:03:09.269 align:start position:0%
so we'll say okay is the below reference
 

00:03:09.269 --> 00:03:12.170 align:start position:0%
so we'll say okay is the below reference
null<00:03:09.540><c> it</c><00:03:10.110><c> is</c><00:03:10.260><c> not</c><00:03:10.410><c> it</c><00:03:11.130><c> is</c><00:03:11.160><c> it</c><00:03:11.519><c> holds</c><00:03:11.760><c> that</c><00:03:11.940><c> it</c>

00:03:12.170 --> 00:03:12.180 align:start position:0%
null it is not it is it holds that it
 

00:03:12.180 --> 00:03:15.020 align:start position:0%
null it is not it is it holds that it
holds<00:03:12.360><c> 17</c><00:03:12.950><c> so</c><00:03:13.950><c> now</c><00:03:14.579><c> that</c><00:03:14.640><c> we're</c><00:03:14.849><c> down</c><00:03:14.970><c> here</c>

00:03:15.020 --> 00:03:15.030 align:start position:0%
holds 17 so now that we're down here
 

00:03:15.030 --> 00:03:17.120 align:start position:0%
holds 17 so now that we're down here
we're<00:03:15.840><c> gonna</c><00:03:16.019><c> go</c><00:03:16.200><c> forward</c><00:03:16.560><c> and</c><00:03:16.739><c> say</c><00:03:16.889><c> alright</c>

00:03:17.120 --> 00:03:17.130 align:start position:0%
we're gonna go forward and say alright
 

00:03:17.130 --> 00:03:20.960 align:start position:0%
we're gonna go forward and say alright
it<00:03:17.370><c> is</c><00:03:17.970><c> 25</c><00:03:18.389><c> less</c><00:03:18.630><c> than</c><00:03:18.750><c> 50</c><00:03:19.019><c> it</c><00:03:19.319><c> is</c><00:03:19.859><c> so</c><00:03:20.609><c> we</c><00:03:20.850><c> can</c>

00:03:20.960 --> 00:03:20.970 align:start position:0%
it is 25 less than 50 it is so we can
 

00:03:20.970 --> 00:03:23.900 align:start position:0%
it is 25 less than 50 it is so we can
now<00:03:21.090><c> go</c><00:03:21.299><c> to</c><00:03:21.359><c> this</c><00:03:21.480><c> node</c><00:03:22.430><c> we</c><00:03:23.430><c> can't</c><00:03:23.639><c> go</c><00:03:23.730><c> forward</c>

00:03:23.900 --> 00:03:23.910 align:start position:0%
now go to this node we can't go forward
 

00:03:23.910 --> 00:03:25.670 align:start position:0%
now go to this node we can't go forward
anymore<00:03:24.209><c> because</c><00:03:24.780><c> positive</c><00:03:25.049><c> infinity</c><00:03:25.350><c> is</c>

00:03:25.670 --> 00:03:25.680 align:start position:0%
anymore because positive infinity is
 

00:03:25.680 --> 00:03:29.960 align:start position:0%
anymore because positive infinity is
greater<00:03:25.920><c> than</c><00:03:26.130><c> 50</c><00:03:27.410><c> so</c><00:03:28.430><c> now</c><00:03:29.430><c> that</c><00:03:29.489><c> we</c><00:03:29.670><c> can't</c><00:03:29.880><c> go</c>

00:03:29.960 --> 00:03:29.970 align:start position:0%
greater than 50 so now that we can't go
 

00:03:29.970 --> 00:03:31.250 align:start position:0%
greater than 50 so now that we can't go
forward<00:03:30.329><c> we're</c><00:03:30.600><c> gonna</c><00:03:30.720><c> try</c><00:03:30.840><c> and</c><00:03:30.930><c> drop</c><00:03:31.049><c> down</c>

00:03:31.250 --> 00:03:31.260 align:start position:0%
forward we're gonna try and drop down
 

00:03:31.260 --> 00:03:34.849 align:start position:0%
forward we're gonna try and drop down
and<00:03:32.209><c> we</c><00:03:33.209><c> can</c><00:03:33.389><c> because</c><00:03:33.780><c> this</c><00:03:34.200><c> below</c><00:03:34.560><c> reference</c>

00:03:34.849 --> 00:03:34.859 align:start position:0%
and we can because this below reference
 

00:03:34.859 --> 00:03:37.699 align:start position:0%
and we can because this below reference
is<00:03:34.980><c> not</c><00:03:35.160><c> known</c><00:03:35.780><c> this</c><00:03:36.780><c> holds</c><00:03:37.079><c> the</c><00:03:37.139><c> value</c><00:03:37.230><c> of</c><00:03:37.380><c> 25</c>

00:03:37.699 --> 00:03:37.709 align:start position:0%
is not known this holds the value of 25
 

00:03:37.709 --> 00:03:40.370 align:start position:0%
is not known this holds the value of 25
or<00:03:38.069><c> the</c><00:03:38.190><c> key</c><00:03:38.340><c> of</c><00:03:38.459><c> 25</c><00:03:38.819><c> so</c><00:03:39.359><c> now</c><00:03:40.019><c> we're</c><00:03:40.200><c> down</c><00:03:40.319><c> in</c>

00:03:40.370 --> 00:03:40.380 align:start position:0%
or the key of 25 so now we're down in
 

00:03:40.380 --> 00:03:43.009 align:start position:0%
or the key of 25 so now we're down in
the<00:03:40.560><c> next</c><00:03:40.680><c> level</c><00:03:41.069><c> we're</c><00:03:41.579><c> gonna</c><00:03:41.730><c> forward</c><00:03:42.239><c> and</c>

00:03:43.009 --> 00:03:43.019 align:start position:0%
the next level we're gonna forward and
 

00:03:43.019 --> 00:03:45.890 align:start position:0%
the next level we're gonna forward and
say<00:03:43.319><c> okay</c><00:03:43.799><c> it's</c><00:03:44.069><c> 31</c><00:03:44.489><c> less</c><00:03:45.389><c> than</c><00:03:45.540><c> or</c><00:03:45.630><c> equal</c><00:03:45.690><c> to</c>

00:03:45.890 --> 00:03:45.900 align:start position:0%
say okay it's 31 less than or equal to
 

00:03:45.900 --> 00:03:51.229 align:start position:0%
say okay it's 31 less than or equal to
50<00:03:46.200><c> it</c><00:03:46.650><c> is</c><00:03:47.370><c> so</c><00:03:48.299><c> we</c><00:03:49.260><c> can</c><00:03:49.560><c> go</c><00:03:49.680><c> forward</c><00:03:50.180><c> now</c><00:03:51.180><c> we</c>

00:03:51.229 --> 00:03:51.239 align:start position:0%
50 it is so we can go forward now we
 

00:03:51.239 --> 00:03:52.699 align:start position:0%
50 it is so we can go forward now we
can't<00:03:51.540><c> go</c><00:03:51.660><c> forward</c><00:03:51.810><c> again</c><00:03:52.019><c> because</c><00:03:52.440><c> positive</c>

00:03:52.699 --> 00:03:52.709 align:start position:0%
can't go forward again because positive
 

00:03:52.709 --> 00:03:55.789 align:start position:0%
can't go forward again because positive
infinity<00:03:53.989><c> that</c><00:03:54.989><c> is</c><00:03:55.109><c> that's</c><00:03:55.290><c> greater</c><00:03:55.530><c> than</c><00:03:55.560><c> 50</c>

00:03:55.789 --> 00:03:55.799 align:start position:0%
infinity that is that's greater than 50
 

00:03:55.799 --> 00:03:57.110 align:start position:0%
infinity that is that's greater than 50
so<00:03:55.920><c> we</c><00:03:55.950><c> can't</c><00:03:56.160><c> go</c><00:03:56.220><c> forward</c><00:03:56.370><c> so</c><00:03:56.790><c> we're</c><00:03:57.000><c> gonna</c>

00:03:57.110 --> 00:03:57.120 align:start position:0%
so we can't go forward so we're gonna
 

00:03:57.120 --> 00:04:00.640 align:start position:0%
so we can't go forward so we're gonna
drop<00:03:57.299><c> down</c><00:03:57.480><c> another</c><00:03:57.660><c> level</c><00:03:58.130><c> because</c><00:03:59.130><c> it</c><00:03:59.790><c> holds</c>

00:04:00.640 --> 00:04:00.650 align:start position:0%
drop down another level because it holds
 

00:04:00.650 --> 00:04:03.080 align:start position:0%
drop down another level because it holds
because<00:04:01.650><c> it</c><00:04:01.859><c> doesn't</c><00:04:02.160><c> hold</c><00:04:02.280><c> a</c><00:04:02.430><c> null</c><00:04:02.670><c> reference</c>

00:04:03.080 --> 00:04:03.090 align:start position:0%
because it doesn't hold a null reference
 

00:04:03.090 --> 00:04:05.539 align:start position:0%
because it doesn't hold a null reference
and<00:04:03.500><c> now</c><00:04:04.500><c> that</c><00:04:04.680><c> we're</c><00:04:04.769><c> down</c><00:04:04.859><c> the</c><00:04:04.980><c> next</c><00:04:05.130><c> level</c>

00:04:05.539 --> 00:04:05.549 align:start position:0%
and now that we're down the next level
 

00:04:05.549 --> 00:04:07.759 align:start position:0%
and now that we're down the next level
we're<00:04:05.730><c> gonna</c><00:04:05.819><c> go</c><00:04:06.090><c> try</c><00:04:06.150><c> to</c><00:04:06.359><c> go</c><00:04:06.480><c> forward</c><00:04:06.959><c> and</c><00:04:07.680><c> I'm</c>

00:04:07.759 --> 00:04:07.769 align:start position:0%
we're gonna go try to go forward and I'm
 

00:04:07.769 --> 00:04:09.800 align:start position:0%
we're gonna go try to go forward and I'm
gonna<00:04:07.889><c> say</c><00:04:08.099><c> okay</c><00:04:08.400><c> 39</c><00:04:08.970><c> is</c><00:04:09.239><c> less</c><00:04:09.540><c> than</c><00:04:09.690><c> or</c><00:04:09.750><c> equal</c>

00:04:09.800 --> 00:04:09.810 align:start position:0%
gonna say okay 39 is less than or equal
 

00:04:09.810 --> 00:04:14.420 align:start position:0%
gonna say okay 39 is less than or equal
to<00:04:09.989><c> 50</c><00:04:10.260><c> so</c><00:04:10.530><c> we</c><00:04:11.519><c> can</c><00:04:11.669><c> go</c><00:04:11.790><c> here</c><00:04:12.260><c> now</c><00:04:13.340><c> now</c><00:04:14.340><c> we're</c>

00:04:14.420 --> 00:04:14.430 align:start position:0%
to 50 so we can go here now now we're
 

00:04:14.430 --> 00:04:16.009 align:start position:0%
to 50 so we can go here now now we're
gonna<00:04:14.519><c> try</c><00:04:14.700><c> and</c><00:04:14.819><c> drop</c><00:04:14.880><c> down</c><00:04:15.150><c> another</c><00:04:15.359><c> level</c><00:04:15.660><c> we</c>

00:04:16.009 --> 00:04:16.019 align:start position:0%
gonna try and drop down another level we
 

00:04:16.019 --> 00:04:18.740 align:start position:0%
gonna try and drop down another level we
can<00:04:16.289><c> and</c><00:04:16.620><c> then</c><00:04:17.459><c> once</c><00:04:18.150><c> we're</c><00:04:18.299><c> here</c><00:04:18.479><c> we're</c><00:04:18.660><c> gonna</c>

00:04:18.740 --> 00:04:18.750 align:start position:0%
can and then once we're here we're gonna
 

00:04:18.750 --> 00:04:21.529 align:start position:0%
can and then once we're here we're gonna
try<00:04:18.930><c> and</c><00:04:19.019><c> go</c><00:04:19.169><c> forward</c><00:04:19.530><c> I'm</c><00:04:20.310><c> gonna</c><00:04:20.640><c> say</c><00:04:20.760><c> okay</c><00:04:20.789><c> 50</c>

00:04:21.529 --> 00:04:21.539 align:start position:0%
try and go forward I'm gonna say okay 50
 

00:04:21.539 --> 00:04:24.830 align:start position:0%
try and go forward I'm gonna say okay 50
is<00:04:21.720><c> less</c><00:04:21.959><c> than</c><00:04:22.109><c> equal</c><00:04:22.349><c> to</c><00:04:22.500><c> 50</c><00:04:23.340><c> so</c><00:04:23.880><c> we</c><00:04:24.539><c> can</c><00:04:24.690><c> go</c>

00:04:24.830 --> 00:04:24.840 align:start position:0%
is less than equal to 50 so we can go
 

00:04:24.840 --> 00:04:25.879 align:start position:0%
is less than equal to 50 so we can go
here

00:04:25.879 --> 00:04:25.889 align:start position:0%
here
 

00:04:25.889 --> 00:04:27.260 align:start position:0%
here
you<00:04:26.159><c> might</c><00:04:26.340><c> think</c><00:04:26.580><c> well</c><00:04:26.759><c> why</c><00:04:26.909><c> don't</c><00:04:26.969><c> you</c><00:04:27.120><c> just</c>

00:04:27.260 --> 00:04:27.270 align:start position:0%
you might think well why don't you just
 

00:04:27.270 --> 00:04:29.570 align:start position:0%
you might think well why don't you just
stop<00:04:27.479><c> here</c><00:04:27.659><c> we</c><00:04:27.810><c> see</c><00:04:28.020><c> as</c><00:04:28.110><c> 50</c><00:04:28.520><c> and</c><00:04:29.520><c> that's</c>

00:04:29.570 --> 00:04:29.580 align:start position:0%
stop here we see as 50 and that's
 

00:04:29.580 --> 00:04:31.670 align:start position:0%
stop here we see as 50 and that's
because<00:04:29.969><c> we're</c><00:04:30.780><c> gonna</c><00:04:30.870><c> be</c><00:04:30.960><c> using</c><00:04:31.050><c> a</c><00:04:31.289><c> generic</c>

00:04:31.670 --> 00:04:31.680 align:start position:0%
because we're gonna be using a generic
 

00:04:31.680 --> 00:04:33.950 align:start position:0%
because we're gonna be using a generic
utility<00:04:32.520><c> message</c><00:04:32.789><c> for</c><00:04:32.969><c> searching</c><00:04:33.330><c> because</c>

00:04:33.950 --> 00:04:33.960 align:start position:0%
utility message for searching because
 

00:04:33.960 --> 00:04:36.409 align:start position:0%
utility message for searching because
the<00:04:34.259><c> searching</c><00:04:34.710><c> inserting</c><00:04:35.610><c> and</c><00:04:35.789><c> deleting</c><00:04:35.849><c> are</c>

00:04:36.409 --> 00:04:36.419 align:start position:0%
the searching inserting and deleting are
 

00:04:36.419 --> 00:04:38.570 align:start position:0%
the searching inserting and deleting are
all<00:04:37.080><c> going</c><00:04:37.259><c> to</c><00:04:37.349><c> be</c><00:04:37.409><c> using</c><00:04:37.650><c> this</c><00:04:37.740><c> method</c><00:04:38.129><c> using</c>

00:04:38.570 --> 00:04:38.580 align:start position:0%
all going to be using this method using
 

00:04:38.580 --> 00:04:40.429 align:start position:0%
all going to be using this method using
this<00:04:38.699><c> general</c><00:04:39.029><c> method</c><00:04:39.330><c> killaby</c><00:04:39.779><c> method</c><00:04:40.080><c> and</c>

00:04:40.429 --> 00:04:40.439 align:start position:0%
this general method killaby method and
 

00:04:40.439 --> 00:04:41.809 align:start position:0%
this general method killaby method and
that's<00:04:40.560><c> because</c><00:04:40.830><c> we</c><00:04:40.979><c> always</c><00:04:41.189><c> have</c><00:04:41.430><c> to</c><00:04:41.520><c> search</c>

00:04:41.809 --> 00:04:41.819 align:start position:0%
that's because we always have to search
 

00:04:41.819 --> 00:04:43.640 align:start position:0%
that's because we always have to search
for<00:04:42.029><c> something</c><00:04:42.240><c> before</c><00:04:43.050><c> we</c><00:04:43.139><c> can</c><00:04:43.259><c> perform</c><00:04:43.379><c> an</c>

00:04:43.640 --> 00:04:43.650 align:start position:0%
for something before we can perform an
 

00:04:43.650 --> 00:04:47.209 align:start position:0%
for something before we can perform an
action<00:04:43.830><c> in</c><00:04:44.159><c> a</c><00:04:44.819><c> skip</c><00:04:45.029><c> list</c><00:04:45.180><c> okay</c><00:04:45.949><c> so</c><00:04:46.949><c> now</c><00:04:47.069><c> that</c>

00:04:47.209 --> 00:04:47.219 align:start position:0%
action in a skip list okay so now that
 

00:04:47.219 --> 00:04:50.330 align:start position:0%
action in a skip list okay so now that
we're<00:04:47.310><c> here</c><00:04:48.229><c> we're</c><00:04:49.229><c> gonna</c><00:04:49.349><c> say</c><00:04:49.620><c> can</c><00:04:50.069><c> we</c><00:04:50.099><c> drop</c>

00:04:50.330 --> 00:04:50.340 align:start position:0%
we're here we're gonna say can we drop
 

00:04:50.340 --> 00:04:53.510 align:start position:0%
we're here we're gonna say can we drop
down<00:04:50.400><c> to</c><00:04:50.639><c> Louisville</c><00:04:50.849><c> and</c><00:04:51.289><c> we</c><00:04:52.289><c> cannot</c><00:04:52.860><c> because</c>

00:04:53.510 --> 00:04:53.520 align:start position:0%
down to Louisville and we cannot because
 

00:04:53.520 --> 00:04:55.670 align:start position:0%
down to Louisville and we cannot because
it's<00:04:53.909><c> below</c><00:04:54.270><c> reference</c><00:04:54.539><c> is</c><00:04:54.719><c> null</c><00:04:55.020><c> so</c><00:04:55.560><c> that</c>

00:04:55.670 --> 00:04:55.680 align:start position:0%
it's below reference is null so that
 

00:04:55.680 --> 00:04:57.260 align:start position:0%
it's below reference is null so that
means<00:04:55.860><c> we're</c><00:04:55.979><c> done</c><00:04:56.099><c> whatever</c><00:04:56.669><c> you</c><00:04:56.849><c> can't</c><00:04:57.060><c> drop</c>

00:04:57.260 --> 00:04:57.270 align:start position:0%
means we're done whatever you can't drop
 

00:04:57.270 --> 00:04:59.659 align:start position:0%
means we're done whatever you can't drop
down<00:04:57.479><c> anymore</c><00:04:57.779><c> you're</c><00:04:58.439><c> done</c><00:04:58.650><c> you</c><00:04:58.800><c> found</c><00:04:59.069><c> the</c>

00:04:59.659 --> 00:04:59.669 align:start position:0%
down anymore you're done you found the
 

00:04:59.669 --> 00:05:03.019 align:start position:0%
down anymore you're done you found the
node<00:04:59.960><c> that</c><00:05:00.960><c> you're</c><00:05:01.110><c> looking</c><00:05:01.349><c> for</c><00:05:01.529><c> okay</c><00:05:01.919><c> so</c><00:05:02.729><c> now</c>

00:05:03.019 --> 00:05:03.029 align:start position:0%
node that you're looking for okay so now
 

00:05:03.029 --> 00:05:04.760 align:start position:0%
node that you're looking for okay so now
then<00:05:03.569><c> what</c><00:05:03.750><c> we</c><00:05:03.840><c> would</c><00:05:03.960><c> do</c><00:05:04.169><c> in</c><00:05:04.439><c> like</c><00:05:04.620><c> the</c>

00:05:04.760 --> 00:05:04.770 align:start position:0%
then what we would do in like the
 

00:05:04.770 --> 00:05:06.909 align:start position:0%
then what we would do in like the
searching<00:05:05.129><c> algorithm</c><00:05:05.520><c> we</c><00:05:05.639><c> would</c><00:05:05.759><c> say</c><00:05:05.909><c> okay</c><00:05:06.360><c> is</c>

00:05:06.909 --> 00:05:06.919 align:start position:0%
searching algorithm we would say okay is
 

00:05:06.919 --> 00:05:12.290 align:start position:0%
searching algorithm we would say okay is
50<00:05:08.060><c> equal</c><00:05:09.060><c> to</c><00:05:09.240><c> the</c><00:05:09.360><c> key</c><00:05:09.770><c> is</c><00:05:10.849><c> this</c><00:05:11.849><c> is</c><00:05:12.029><c> this</c>

00:05:12.290 --> 00:05:12.300 align:start position:0%
50 equal to the key is this is this
 

00:05:12.300 --> 00:05:16.219 align:start position:0%
50 equal to the key is this is this
nodes<00:05:12.629><c> key</c><00:05:13.139><c> that</c><00:05:13.680><c> we</c><00:05:13.830><c> found</c><00:05:14.569><c> is</c><00:05:15.569><c> equal</c><00:05:16.020><c> to</c><00:05:16.139><c> the</c>

00:05:16.219 --> 00:05:16.229 align:start position:0%
nodes key that we found is equal to the
 

00:05:16.229 --> 00:05:18.350 align:start position:0%
nodes key that we found is equal to the
one<00:05:16.409><c> we're</c><00:05:16.529><c> searching</c><00:05:16.860><c> for</c><00:05:17.039><c> and</c><00:05:17.279><c> the</c><00:05:18.150><c> answer</c>

00:05:18.350 --> 00:05:18.360 align:start position:0%
one we're searching for and the answer
 

00:05:18.360 --> 00:05:20.689 align:start position:0%
one we're searching for and the answer
is<00:05:18.449><c> yes</c><00:05:18.719><c> so</c><00:05:18.990><c> we</c><00:05:19.080><c> would</c><00:05:19.229><c> return</c><00:05:19.460><c> we</c><00:05:20.460><c> were</c><00:05:20.580><c> to</c>

00:05:20.689 --> 00:05:20.699 align:start position:0%
is yes so we would return we were to
 

00:05:20.699 --> 00:05:24.950 align:start position:0%
is yes so we would return we were to
turn<00:05:20.849><c> this</c><00:05:21.000><c> node</c><00:05:22.069><c> okay</c><00:05:23.069><c> so</c><00:05:23.599><c> that</c><00:05:24.599><c> was</c><00:05:24.689><c> kind</c><00:05:24.900><c> of</c>

00:05:24.950 --> 00:05:24.960 align:start position:0%
turn this node okay so that was kind of
 

00:05:24.960 --> 00:05:29.179 align:start position:0%
turn this node okay so that was kind of
easy<00:05:26.029><c> but</c><00:05:27.029><c> there's</c><00:05:27.360><c> a</c><00:05:27.569><c> couple</c><00:05:27.810><c> other</c><00:05:28.189><c> edge</c>

00:05:29.179 --> 00:05:29.189 align:start position:0%
easy but there's a couple other edge
 

00:05:29.189 --> 00:05:32.149 align:start position:0%
easy but there's a couple other edge
cases<00:05:29.490><c> well</c><00:05:30.120><c> one</c><00:05:30.449><c> of</c><00:05:30.599><c> the</c><00:05:31.319><c> first</c><00:05:31.860><c> one</c><00:05:32.009><c> being</c>

00:05:32.149 --> 00:05:32.159 align:start position:0%
cases well one of the first one being
 

00:05:32.159 --> 00:05:34.279 align:start position:0%
cases well one of the first one being
what<00:05:32.759><c> if</c><00:05:32.909><c> the</c><00:05:33.360><c> key</c><00:05:33.599><c> was</c><00:05:33.629><c> searching</c><00:05:33.960><c> for</c><00:05:34.020><c> word</c>

00:05:34.279 --> 00:05:34.289 align:start position:0%
what if the key was searching for word
 

00:05:34.289 --> 00:05:36.740 align:start position:0%
what if the key was searching for word
doesn't<00:05:34.949><c> exist</c><00:05:35.310><c> and</c><00:05:35.909><c> that's</c><00:05:36.089><c> a</c><00:05:36.180><c> good</c><00:05:36.300><c> question</c>

00:05:36.740 --> 00:05:36.750 align:start position:0%
doesn't exist and that's a good question
 

00:05:36.750 --> 00:05:39.579 align:start position:0%
doesn't exist and that's a good question
and<00:05:37.310><c> let's</c><00:05:38.310><c> go</c><00:05:38.520><c> through</c><00:05:38.550><c> that</c><00:05:38.849><c> real</c><00:05:39.180><c> quick</c>

00:05:39.579 --> 00:05:39.589 align:start position:0%
and let's go through that real quick
 

00:05:39.589 --> 00:05:42.499 align:start position:0%
and let's go through that real quick
okay<00:05:40.589><c> so</c><00:05:40.889><c> let's</c><00:05:41.310><c> say</c><00:05:41.430><c> we're</c><00:05:41.580><c> searching</c><00:05:41.879><c> for</c><00:05:41.909><c> 49</c>

00:05:42.499 --> 00:05:42.509 align:start position:0%
okay so let's say we're searching for 49
 

00:05:42.509 --> 00:05:44.360 align:start position:0%
okay so let's say we're searching for 49
that<00:05:43.199><c> doesn't</c><00:05:43.589><c> exist</c><00:05:43.860><c> on</c><00:05:43.919><c> the</c><00:05:44.009><c> Skip</c><00:05:44.219><c> list</c>

00:05:44.360 --> 00:05:44.370 align:start position:0%
that doesn't exist on the Skip list
 

00:05:44.370 --> 00:05:46.309 align:start position:0%
that doesn't exist on the Skip list
alright<00:05:44.939><c> so</c><00:05:45.210><c> let's</c><00:05:45.689><c> start</c><00:05:45.779><c> the</c><00:05:45.960><c> top</c><00:05:46.110><c> left</c>

00:05:46.309 --> 00:05:46.319 align:start position:0%
alright so let's start the top left
 

00:05:46.319 --> 00:05:48.529 align:start position:0%
alright so let's start the top left
again<00:05:46.610><c> always</c><00:05:47.610><c> trying</c><00:05:47.789><c> to</c><00:05:47.879><c> drop</c><00:05:47.969><c> down</c><00:05:48.210><c> first</c>

00:05:48.529 --> 00:05:48.539 align:start position:0%
again always trying to drop down first
 

00:05:48.539 --> 00:05:50.420 align:start position:0%
again always trying to drop down first
we<00:05:48.899><c> can</c><00:05:49.110><c> drop</c><00:05:49.319><c> down</c><00:05:49.469><c> because</c><00:05:50.009><c> this</c><00:05:50.219><c> blow</c>

00:05:50.420 --> 00:05:50.430 align:start position:0%
we can drop down because this blow
 

00:05:50.430 --> 00:05:52.129 align:start position:0%
we can drop down because this blow
reference<00:05:50.669><c> is</c><00:05:50.759><c> not</c><00:05:50.939><c> null</c><00:05:51.120><c> then</c><00:05:51.419><c> we're</c><00:05:51.990><c> just</c>

00:05:52.129 --> 00:05:52.139 align:start position:0%
reference is not null then we're just
 

00:05:52.139 --> 00:05:55.490 align:start position:0%
reference is not null then we're just
scan<00:05:52.770><c> forward</c><00:05:53.810><c> you</c><00:05:54.810><c> stop</c><00:05:55.050><c> where</c><00:05:55.199><c> I</c><00:05:55.229><c> stopped</c>

00:05:55.490 --> 00:05:55.500 align:start position:0%
scan forward you stop where I stopped
 

00:05:55.500 --> 00:05:57.139 align:start position:0%
scan forward you stop where I stopped
here<00:05:55.740><c> because</c><00:05:55.860><c> a</c><00:05:55.979><c> positive</c><00:05:56.370><c> infinity</c><00:05:56.550><c> it's</c>

00:05:57.139 --> 00:05:57.149 align:start position:0%
here because a positive infinity it's
 

00:05:57.149 --> 00:05:59.899 align:start position:0%
here because a positive infinity it's
greater<00:05:57.509><c> than</c><00:05:57.560><c> 49</c><00:05:58.560><c> then</c><00:05:59.310><c> we're</c><00:05:59.460><c> gonna</c><00:05:59.550><c> drop</c>

00:05:59.899 --> 00:05:59.909 align:start position:0%
greater than 49 then we're gonna drop
 

00:05:59.909 --> 00:06:04.070 align:start position:0%
greater than 49 then we're gonna drop
down<00:06:00.089><c> level</c><00:06:00.389><c> we</c><00:06:01.229><c> can</c><00:06:01.409><c> go</c><00:06:01.560><c> forward</c><00:06:02.839><c> we'll</c><00:06:03.839><c> stop</c>

00:06:04.070 --> 00:06:04.080 align:start position:0%
down level we can go forward we'll stop
 

00:06:04.080 --> 00:06:07.100 align:start position:0%
down level we can go forward we'll stop
here<00:06:04.379><c> we</c><00:06:04.680><c> go</c><00:06:04.919><c> down</c><00:06:05.389><c> then</c><00:06:06.389><c> we</c><00:06:06.569><c> can</c><00:06:06.689><c> go</c><00:06:06.779><c> forward</c>

00:06:07.100 --> 00:06:07.110 align:start position:0%
here we go down then we can go forward
 

00:06:07.110 --> 00:06:08.869 align:start position:0%
here we go down then we can go forward
again<00:06:07.430><c> stop</c><00:06:08.430><c> here</c>

00:06:08.869 --> 00:06:08.879 align:start position:0%
again stop here
 

00:06:08.879 --> 00:06:12.790 align:start position:0%
again stop here
we<00:06:09.509><c> can</c><00:06:09.719><c> go</c><00:06:09.839><c> down</c><00:06:10.189><c> then</c><00:06:11.189><c> we</c><00:06:11.310><c> can</c><00:06:11.490><c> go</c><00:06:11.610><c> forward</c>

00:06:12.790 --> 00:06:12.800 align:start position:0%
we can go down then we can go forward
 

00:06:12.800 --> 00:06:15.320 align:start position:0%
we can go down then we can go forward
okay<00:06:13.800><c> and</c><00:06:14.310><c> I'm</c><00:06:14.370><c> gonna</c><00:06:14.490><c> I'm</c><00:06:14.909><c> going</c><00:06:15.060><c> to</c><00:06:15.149><c> kind</c><00:06:15.300><c> of</c>

00:06:15.320 --> 00:06:15.330 align:start position:0%
okay and I'm gonna I'm going to kind of
 

00:06:15.330 --> 00:06:16.790 align:start position:0%
okay and I'm gonna I'm going to kind of
make<00:06:15.509><c> this</c><00:06:15.659><c> I</c><00:06:15.960><c> want</c><00:06:16.229><c> to</c><00:06:16.289><c> kinda</c><00:06:16.439><c> explain</c><00:06:16.649><c> it</c>

00:06:16.790 --> 00:06:16.800 align:start position:0%
make this I want to kinda explain it
 

00:06:16.800 --> 00:06:19.279 align:start position:0%
make this I want to kinda explain it
here<00:06:16.979><c> so</c><00:06:17.639><c> now</c><00:06:17.969><c> that</c><00:06:18.029><c> we're</c><00:06:18.240><c> here</c><00:06:18.539><c> we</c><00:06:18.899><c> can't</c><00:06:19.169><c> go</c>

00:06:19.279 --> 00:06:19.289 align:start position:0%
here so now that we're here we can't go
 

00:06:19.289 --> 00:06:20.600 align:start position:0%
here so now that we're here we can't go
forward<00:06:19.589><c> anymore</c><00:06:19.860><c> because</c><00:06:20.069><c> it</c><00:06:20.279><c> positive</c>

00:06:20.600 --> 00:06:20.610 align:start position:0%
forward anymore because it positive
 

00:06:20.610 --> 00:06:22.639 align:start position:0%
forward anymore because it positive
finding<00:06:20.729><c> its</c><00:06:21.060><c> greater</c><00:06:21.330><c> than</c><00:06:21.360><c> 49</c><00:06:21.810><c> so</c><00:06:22.529><c> we're</c>

00:06:22.639 --> 00:06:22.649 align:start position:0%
finding its greater than 49 so we're
 

00:06:22.649 --> 00:06:25.519 align:start position:0%
finding its greater than 49 so we're
going<00:06:22.740><c> to</c><00:06:22.860><c> drop</c><00:06:23.099><c> down</c><00:06:23.839><c> okay</c><00:06:24.839><c> we</c><00:06:24.960><c> can</c><00:06:25.169><c> drop</c><00:06:25.319><c> down</c>

00:06:25.519 --> 00:06:25.529 align:start position:0%
going to drop down okay we can drop down
 

00:06:25.529 --> 00:06:30.050 align:start position:0%
going to drop down okay we can drop down
so<00:06:26.399><c> now</c><00:06:26.639><c> we're</c><00:06:27.569><c> gonna</c><00:06:27.719><c> try</c><00:06:27.960><c> and</c><00:06:28.080><c> go</c><00:06:28.169><c> forward</c><00:06:29.060><c> we</c>

00:06:30.050 --> 00:06:30.060 align:start position:0%
so now we're gonna try and go forward we
 

00:06:30.060 --> 00:06:31.600 align:start position:0%
so now we're gonna try and go forward we
can't<00:06:30.389><c> because</c><00:06:30.839><c> v</c>

00:06:31.600 --> 00:06:31.610 align:start position:0%
can't because v
 

00:06:31.610 --> 00:06:34.629 align:start position:0%
can't because v
50<00:06:32.300><c> is</c><00:06:32.479><c> greater</c><00:06:32.930><c> than</c><00:06:33.110><c> 49</c><00:06:33.620><c> so</c><00:06:34.250><c> we</c><00:06:34.340><c> can't</c><00:06:34.580><c> go</c>

00:06:34.629 --> 00:06:34.639 align:start position:0%
50 is greater than 49 so we can't go
 

00:06:34.639 --> 00:06:36.429 align:start position:0%
50 is greater than 49 so we can't go
afford<00:06:34.939><c> any</c><00:06:35.090><c> more</c><00:06:35.240><c> so</c><00:06:35.719><c> what</c><00:06:35.960><c> we're</c><00:06:36.110><c> gonna</c><00:06:36.199><c> do</c>

00:06:36.429 --> 00:06:36.439 align:start position:0%
afford any more so what we're gonna do
 

00:06:36.439 --> 00:06:38.140 align:start position:0%
afford any more so what we're gonna do
is<00:06:36.949><c> we're</c><00:06:37.069><c> gonna</c><00:06:37.129><c> try</c><00:06:37.340><c> and</c><00:06:37.490><c> drop</c><00:06:37.669><c> down</c><00:06:37.939><c> again</c>

00:06:38.140 --> 00:06:38.150 align:start position:0%
is we're gonna try and drop down again
 

00:06:38.150 --> 00:06:41.439 align:start position:0%
is we're gonna try and drop down again
well<00:06:38.960><c> we</c><00:06:39.229><c> can't</c><00:06:39.650><c> drop</c><00:06:39.919><c> down</c><00:06:40.129><c> anymore</c><00:06:40.490><c> and</c><00:06:41.180><c> it's</c>

00:06:41.439 --> 00:06:41.449 align:start position:0%
well we can't drop down anymore and it's
 

00:06:41.449 --> 00:06:43.270 align:start position:0%
well we can't drop down anymore and it's
and<00:06:41.689><c> like</c><00:06:42.050><c> I</c><00:06:42.169><c> said</c><00:06:42.379><c> if</c><00:06:42.530><c> you</c><00:06:42.620><c> can't</c><00:06:42.889><c> drop</c><00:06:43.099><c> down</c>

00:06:43.270 --> 00:06:43.280 align:start position:0%
and like I said if you can't drop down
 

00:06:43.280 --> 00:06:44.770 align:start position:0%
and like I said if you can't drop down
any<00:06:43.370><c> more</c><00:06:43.520><c> that</c><00:06:43.849><c> means</c><00:06:44.000><c> you're</c><00:06:44.210><c> at</c><00:06:44.330><c> the</c><00:06:44.539><c> node</c>

00:06:44.770 --> 00:06:44.780 align:start position:0%
any more that means you're at the node
 

00:06:44.780 --> 00:06:47.050 align:start position:0%
any more that means you're at the node
that<00:06:45.770><c> year</c><00:06:45.949><c> that</c><00:06:46.460><c> that</c><00:06:46.610><c> means</c><00:06:46.819><c> you're</c><00:06:46.909><c> at</c><00:06:46.969><c> the</c>

00:06:47.050 --> 00:06:47.060 align:start position:0%
that year that that means you're at the
 

00:06:47.060 --> 00:06:48.909 align:start position:0%
that year that that means you're at the
node<00:06:47.300><c> that</c><00:06:47.900><c> you're</c><00:06:48.020><c> trying</c><00:06:48.259><c> to</c><00:06:48.319><c> find</c><00:06:48.560><c> now</c>

00:06:48.909 --> 00:06:48.919 align:start position:0%
node that you're trying to find now
 

00:06:48.919 --> 00:06:52.089 align:start position:0%
node that you're trying to find now
we're<00:06:49.039><c> not</c><00:06:49.159><c> trying</c><00:06:49.430><c> to</c><00:06:49.490><c> find</c><00:06:49.550><c> 39</c><00:06:50.199><c> but</c><00:06:51.199><c> we're</c>

00:06:52.089 --> 00:06:52.099 align:start position:0%
we're not trying to find 39 but we're
 

00:06:52.099 --> 00:06:53.950 align:start position:0%
we're not trying to find 39 but we're
gonna<00:06:52.189><c> return</c><00:06:52.400><c> this</c><00:06:52.849><c> node</c><00:06:53.090><c> because</c><00:06:53.539><c> it's</c><00:06:53.780><c> the</c>

00:06:53.950 --> 00:06:53.960 align:start position:0%
gonna return this node because it's the
 

00:06:53.960 --> 00:06:57.450 align:start position:0%
gonna return this node because it's the
largest<00:06:54.169><c> node</c><00:06:54.819><c> less</c><00:06:55.819><c> than</c><00:06:56.000><c> or</c><00:06:56.090><c> equal</c><00:06:56.210><c> to</c><00:06:56.539><c> 49</c><00:06:56.840><c> oh</c>

00:06:57.450 --> 00:06:57.460 align:start position:0%
largest node less than or equal to 49 oh
 

00:06:57.460 --> 00:06:59.529 align:start position:0%
largest node less than or equal to 49 oh
it's<00:06:58.460><c> that</c><00:06:58.520><c> made</c><00:06:58.669><c> sense</c><00:06:58.849><c> I'm</c><00:06:59.180><c> gonna</c><00:06:59.300><c> say</c><00:06:59.389><c> one</c>

00:06:59.529 --> 00:06:59.539 align:start position:0%
it's that made sense I'm gonna say one
 

00:06:59.539 --> 00:06:59.980 align:start position:0%
it's that made sense I'm gonna say one
more<00:06:59.629><c> time</c>

00:06:59.980 --> 00:06:59.990 align:start position:0%
more time
 

00:06:59.990 --> 00:07:03.189 align:start position:0%
more time
it's<00:07:00.409><c> the</c><00:07:00.620><c> largest</c><00:07:00.889><c> node</c><00:07:01.629><c> less</c><00:07:02.629><c> than</c><00:07:02.900><c> or</c><00:07:03.020><c> equal</c>

00:07:03.189 --> 00:07:03.199 align:start position:0%
it's the largest node less than or equal
 

00:07:03.199 --> 00:07:06.129 align:start position:0%
it's the largest node less than or equal
to<00:07:03.530><c> 49</c><00:07:03.889><c> and</c><00:07:04.330><c> that's</c><00:07:05.330><c> always</c><00:07:05.599><c> the</c><00:07:05.870><c> node</c><00:07:06.020><c> we're</c>

00:07:06.129 --> 00:07:06.139 align:start position:0%
to 49 and that's always the node we're
 

00:07:06.139 --> 00:07:08.740 align:start position:0%
to 49 and that's always the node we're
gonna<00:07:06.259><c> return</c><00:07:06.500><c> so</c><00:07:07.460><c> for</c><00:07:07.879><c> a</c><00:07:07.909><c> third</c><00:07:08.210><c> edge</c><00:07:08.449><c> case</c>

00:07:08.740 --> 00:07:08.750 align:start position:0%
gonna return so for a third edge case
 

00:07:08.750 --> 00:07:11.050 align:start position:0%
gonna return so for a third edge case
you<00:07:09.080><c> may</c><00:07:09.199><c> be</c><00:07:09.259><c> wondering</c><00:07:09.710><c> well</c><00:07:10.370><c> what</c><00:07:10.580><c> about</c><00:07:10.789><c> if</c>

00:07:11.050 --> 00:07:11.060 align:start position:0%
you may be wondering well what about if
 

00:07:11.060 --> 00:07:13.890 align:start position:0%
you may be wondering well what about if
we<00:07:11.120><c> find</c><00:07:11.449><c> a</c><00:07:11.539><c> key</c><00:07:11.810><c> on</c><00:07:12.080><c> one</c><00:07:12.169><c> of</c><00:07:12.259><c> these</c><00:07:12.349><c> towers</c>

00:07:13.890 --> 00:07:13.900 align:start position:0%
we find a key on one of these towers
 

00:07:13.900 --> 00:07:17.499 align:start position:0%
we find a key on one of these towers
like<00:07:14.900><c> how</c><00:07:15.169><c> does</c><00:07:15.229><c> that</c><00:07:15.379><c> work</c><00:07:16.060><c> because</c><00:07:17.060><c> for</c>

00:07:17.499 --> 00:07:17.509 align:start position:0%
like how does that work because for
 

00:07:17.509 --> 00:07:21.100 align:start position:0%
like how does that work because for
instance<00:07:17.779><c> 17</c><00:07:18.319><c> has</c><00:07:18.939><c> five</c><00:07:19.939><c> keys</c><00:07:20.389><c> all</c><00:07:20.719><c> in</c><00:07:21.050><c> the</c>

00:07:21.100 --> 00:07:21.110 align:start position:0%
instance 17 has five keys all in the
 

00:07:21.110 --> 00:07:24.100 align:start position:0%
instance 17 has five keys all in the
same<00:07:21.259><c> tower</c><00:07:21.529><c> which</c><00:07:22.189><c> one</c><00:07:22.430><c> do</c><00:07:22.550><c> we</c><00:07:22.639><c> return</c><00:07:23.110><c> okay</c>

00:07:24.100 --> 00:07:24.110 align:start position:0%
same tower which one do we return okay
 

00:07:24.110 --> 00:07:26.649 align:start position:0%
same tower which one do we return okay
good<00:07:24.469><c> question</c><00:07:24.650><c> again</c><00:07:25.009><c> so</c><00:07:25.460><c> let's</c><00:07:26.029><c> do</c><00:07:26.449><c> that</c>

00:07:26.649 --> 00:07:26.659 align:start position:0%
good question again so let's do that
 

00:07:26.659 --> 00:07:30.839 align:start position:0%
good question again so let's do that
that's<00:07:26.990><c> a</c><00:07:27.169><c> let's</c><00:07:27.860><c> perform</c><00:07:28.009><c> that</c><00:07:28.279><c> search</c><00:07:28.490><c> there</c>

00:07:30.839 --> 00:07:30.849 align:start position:0%
that's a let's perform that search there
 

00:07:30.849 --> 00:07:33.249 align:start position:0%
that's a let's perform that search there
all<00:07:31.849><c> right</c><00:07:32.000><c> so</c><00:07:32.180><c> now</c><00:07:32.330><c> we're</c><00:07:32.479><c> a</c><00:07:32.509><c> search</c><00:07:32.719><c> for</c><00:07:32.870><c> 17</c>

00:07:33.249 --> 00:07:33.259 align:start position:0%
all right so now we're a search for 17
 

00:07:33.259 --> 00:07:37.209 align:start position:0%
all right so now we're a search for 17
which<00:07:33.919><c> he</c><00:07:34.069><c> does</c><00:07:34.189><c> know</c><00:07:34.310><c> exists</c><00:07:34.610><c> here</c><00:07:34.789><c> so</c><00:07:35.029><c> Oh</c><00:07:36.219><c> as</c>

00:07:37.209 --> 00:07:37.219 align:start position:0%
which he does know exists here so Oh as
 

00:07:37.219 --> 00:07:38.829 align:start position:0%
which he does know exists here so Oh as
always<00:07:37.550><c> we're</c><00:07:37.789><c> gonna</c><00:07:37.879><c> start</c><00:07:38.240><c> the</c><00:07:38.419><c> top</c><00:07:38.629><c> left</c>

00:07:38.829 --> 00:07:38.839 align:start position:0%
always we're gonna start the top left
 

00:07:38.839 --> 00:07:42.309 align:start position:0%
always we're gonna start the top left
node<00:07:39.050><c> when</c><00:07:39.650><c> I</c><00:07:39.710><c> go</c><00:07:39.919><c> down</c><00:07:40.810><c> all</c><00:07:41.810><c> right</c><00:07:41.960><c> now</c><00:07:42.199><c> we're</c>

00:07:42.309 --> 00:07:42.319 align:start position:0%
node when I go down all right now we're
 

00:07:42.319 --> 00:07:44.740 align:start position:0%
node when I go down all right now we're
gonna<00:07:42.409><c> go</c><00:07:42.680><c> right</c><00:07:43.250><c> we</c><00:07:43.490><c> scan</c><00:07:43.699><c> forward</c><00:07:44.060><c> 17</c><00:07:44.629><c> is</c>

00:07:44.740 --> 00:07:44.750 align:start position:0%
gonna go right we scan forward 17 is
 

00:07:44.750 --> 00:07:47.709 align:start position:0%
gonna go right we scan forward 17 is
this<00:07:45.529><c> nodes</c><00:07:45.770><c> he</c><00:07:46.190><c> is</c><00:07:46.339><c> 17</c><00:07:46.729><c> it's</c><00:07:47.300><c> less</c><00:07:47.539><c> than</c><00:07:47.629><c> or</c>

00:07:47.709 --> 00:07:47.719 align:start position:0%
this nodes he is 17 it's less than or
 

00:07:47.719 --> 00:07:50.070 align:start position:0%
this nodes he is 17 it's less than or
equal<00:07:47.900><c> to</c><00:07:47.960><c> 17</c><00:07:48.289><c> or</c><00:07:48.319><c> so</c><00:07:48.529><c> we're</c><00:07:48.620><c> searching</c><00:07:48.949><c> for</c><00:07:48.979><c> so</c>

00:07:50.070 --> 00:07:50.080 align:start position:0%
equal to 17 or so we're searching for so
 

00:07:50.080 --> 00:07:52.689 align:start position:0%
equal to 17 or so we're searching for so
we<00:07:51.080><c> can</c><00:07:51.229><c> stop</c><00:07:51.379><c> here</c><00:07:51.620><c> we</c><00:07:52.219><c> can't</c><00:07:52.460><c> go</c><00:07:52.550><c> forward</c>

00:07:52.689 --> 00:07:52.699 align:start position:0%
we can stop here we can't go forward
 

00:07:52.699 --> 00:07:57.999 align:start position:0%
we can stop here we can't go forward
anymore<00:07:53.020><c> but</c><00:07:54.279><c> we</c><00:07:55.279><c> can</c><00:07:55.490><c> go</c><00:07:55.610><c> down</c><00:07:55.819><c> okay</c><00:07:57.009><c> remember</c>

00:07:57.999 --> 00:07:58.009 align:start position:0%
anymore but we can go down okay remember
 

00:07:58.009 --> 00:08:00.339 align:start position:0%
anymore but we can go down okay remember
I<00:07:58.159><c> said</c><00:07:58.610><c> in</c><00:07:58.699><c> the</c><00:07:58.759><c> algorithm</c><00:07:59.150><c> you</c><00:07:59.719><c> only</c><00:07:59.990><c> stop</c>

00:08:00.339 --> 00:08:00.349 align:start position:0%
I said in the algorithm you only stop
 

00:08:00.349 --> 00:08:03.070 align:start position:0%
I said in the algorithm you only stop
whenever<00:08:01.099><c> you</c><00:08:01.250><c> can't</c><00:08:01.520><c> go</c><00:08:01.639><c> down</c><00:08:01.819><c> any</c><00:08:02.060><c> more</c><00:08:02.180><c> so</c>

00:08:03.070 --> 00:08:03.080 align:start position:0%
whenever you can't go down any more so
 

00:08:03.080 --> 00:08:04.869 align:start position:0%
whenever you can't go down any more so
now<00:08:03.169><c> we're</c><00:08:03.289><c> gonna</c><00:08:03.379><c> go</c><00:08:03.560><c> down</c><00:08:03.740><c> to</c><00:08:03.860><c> this</c><00:08:03.979><c> node</c><00:08:04.250><c> we</c>

00:08:04.869 --> 00:08:04.879 align:start position:0%
now we're gonna go down to this node we
 

00:08:04.879 --> 00:08:06.969 align:start position:0%
now we're gonna go down to this node we
can't<00:08:05.120><c> go</c><00:08:05.240><c> 40</c><00:08:05.569><c> more</c><00:08:05.779><c> because</c><00:08:06.229><c> 25</c><00:08:06.620><c> is</c><00:08:06.680><c> greater</c>

00:08:06.969 --> 00:08:06.979 align:start position:0%
can't go 40 more because 25 is greater
 

00:08:06.979 --> 00:08:11.850 align:start position:0%
can't go 40 more because 25 is greater
than<00:08:07.039><c> 17</c><00:08:07.490><c> so</c><00:08:08.120><c> we</c><00:08:08.509><c> try</c><00:08:08.689><c> and</c><00:08:08.719><c> go</c><00:08:08.900><c> down</c><00:08:09.699><c> we</c><00:08:10.699><c> stop</c><00:08:10.909><c> 17</c>

00:08:11.850 --> 00:08:11.860 align:start position:0%
than 17 so we try and go down we stop 17
 

00:08:11.860 --> 00:08:15.219 align:start position:0%
than 17 so we try and go down we stop 17
we<00:08:12.860><c> can't</c><00:08:13.129><c> go</c><00:08:13.219><c> forward</c><00:08:13.490><c> anymore</c><00:08:13.849><c> because</c><00:08:14.750><c> 25</c>

00:08:15.219 --> 00:08:15.229 align:start position:0%
we can't go forward anymore because 25
 

00:08:15.229 --> 00:08:18.100 align:start position:0%
we can't go forward anymore because 25
is<00:08:15.289><c> greater</c><00:08:15.469><c> than</c><00:08:15.650><c> 17</c><00:08:16.039><c> so</c><00:08:16.460><c> we</c><00:08:16.490><c> go</c><00:08:16.819><c> down</c><00:08:17.029><c> and</c><00:08:17.270><c> we</c>

00:08:18.100 --> 00:08:18.110 align:start position:0%
is greater than 17 so we go down and we
 

00:08:18.110 --> 00:08:19.749 align:start position:0%
is greater than 17 so we go down and we
kind<00:08:18.319><c> of</c><00:08:18.379><c> keep</c><00:08:18.500><c> doing</c><00:08:18.830><c> this</c><00:08:19.069><c> because</c><00:08:19.339><c> all</c>

00:08:19.749 --> 00:08:19.759 align:start position:0%
kind of keep doing this because all
 

00:08:19.759 --> 00:08:21.040 align:start position:0%
kind of keep doing this because all
these<00:08:19.909><c> nodes</c><00:08:20.060><c> are</c><00:08:20.150><c> gonna</c><00:08:20.270><c> be</c><00:08:20.389><c> grids</c><00:08:20.599><c> and</c><00:08:20.719><c> 17</c>

00:08:21.040 --> 00:08:21.050 align:start position:0%
these nodes are gonna be grids and 17
 

00:08:21.050 --> 00:08:24.029 align:start position:0%
these nodes are gonna be grids and 17
Khalif<00:08:21.500><c> found</c><00:08:22.430><c> the</c><00:08:22.580><c> key</c><00:08:22.729><c> we're</c><00:08:22.849><c> searching</c><00:08:23.120><c> for</c>

00:08:24.029 --> 00:08:24.039 align:start position:0%
Khalif found the key we're searching for
 

00:08:24.039 --> 00:08:26.619 align:start position:0%
Khalif found the key we're searching for
but<00:08:25.039><c> as</c><00:08:25.550><c> far</c><00:08:25.759><c> as</c><00:08:25.789><c> the</c><00:08:25.879><c> algorithm</c><00:08:26.060><c> goes</c><00:08:26.240><c> we</c><00:08:26.539><c> can</c>

00:08:26.619 --> 00:08:26.629 align:start position:0%
but as far as the algorithm goes we can
 

00:08:26.629 --> 00:08:30.810 align:start position:0%
but as far as the algorithm goes we can
keep<00:08:26.750><c> going</c><00:08:27.050><c> down</c><00:08:27.199><c> right</c><00:08:28.099><c> so</c><00:08:28.990><c> we</c><00:08:29.990><c> finally</c><00:08:30.199><c> get</c>

00:08:30.810 --> 00:08:30.820 align:start position:0%
keep going down right so we finally get
 

00:08:30.820 --> 00:08:35.010 align:start position:0%
keep going down right so we finally get
this<00:08:30.940><c> note</c><00:08:31.210><c> and</c><00:08:31.510><c> on</c><00:08:32.500><c> the</c><00:08:33.400><c> bottom</c><00:08:33.940><c> level</c><00:08:34.300><c> and</c><00:08:34.479><c> we</c>

00:08:35.010 --> 00:08:35.020 align:start position:0%
this note and on the bottom level and we
 

00:08:35.020 --> 00:08:35.790 align:start position:0%
this note and on the bottom level and we
try<00:08:35.200><c> and</c><00:08:35.229><c> get</c><00:08:35.350><c> down</c><00:08:35.410><c> again</c>

00:08:35.790 --> 00:08:35.800 align:start position:0%
try and get down again
 

00:08:35.800 --> 00:08:39.450 align:start position:0%
try and get down again
and<00:08:36.070><c> we</c><00:08:36.490><c> can't</c><00:08:36.990><c> because</c><00:08:37.990><c> the</c><00:08:38.830><c> bottom</c><00:08:39.160><c> levels</c>

00:08:39.450 --> 00:08:39.460 align:start position:0%
and we can't because the bottom levels
 

00:08:39.460 --> 00:08:41.279 align:start position:0%
and we can't because the bottom levels
node<00:08:40.390><c> that's</c><00:08:40.660><c> twenty</c><00:08:40.900><c> eight</c><00:08:41.020><c> key</c><00:08:41.169><c> of</c>

00:08:41.279 --> 00:08:41.289 align:start position:0%
node that's twenty eight key of
 

00:08:41.289 --> 00:08:43.650 align:start position:0%
node that's twenty eight key of
seventeen<00:08:41.710><c> is</c><00:08:42.220><c> full</c><00:08:42.490><c> of</c><00:08:42.640><c> reference</c><00:08:43.210><c> is</c><00:08:43.360><c> null</c>

00:08:43.650 --> 00:08:43.660 align:start position:0%
seventeen is full of reference is null
 

00:08:43.660 --> 00:08:47.580 align:start position:0%
seventeen is full of reference is null
so<00:08:44.200><c> we</c><00:08:44.320><c> stop</c><00:08:44.560><c> so</c><00:08:45.280><c> we</c><00:08:45.670><c> would</c><00:08:45.820><c> return</c><00:08:46.230><c> this</c><00:08:47.230><c> node</c>

00:08:47.580 --> 00:08:47.590 align:start position:0%
so we stop so we would return this node
 

00:08:47.590 --> 00:08:51.210 align:start position:0%
so we stop so we would return this node
right<00:08:47.620><c> here</c><00:08:48.990><c> okay</c><00:08:49.990><c> not</c><00:08:50.380><c> though</c><00:08:50.590><c> not</c><00:08:50.920><c> any</c><00:08:51.160><c> of</c>

00:08:51.210 --> 00:08:51.220 align:start position:0%
right here okay not though not any of
 

00:08:51.220 --> 00:08:52.830 align:start position:0%
right here okay not though not any of
the<00:08:51.310><c> ones</c><00:08:51.490><c> not</c><00:08:52.120><c> any</c><00:08:52.300><c> of</c><00:08:52.390><c> the</c><00:08:52.450><c> other</c><00:08:52.570><c> ones</c><00:08:52.750><c> on</c>

00:08:52.830 --> 00:08:52.840 align:start position:0%
the ones not any of the other ones on
 

00:08:52.840 --> 00:08:54.360 align:start position:0%
the ones not any of the other ones on
the<00:08:52.900><c> tower</c><00:08:53.110><c> it'd</c><00:08:53.470><c> be</c><00:08:53.650><c> the</c><00:08:53.830><c> one</c><00:08:54.010><c> on</c><00:08:54.130><c> the</c><00:08:54.190><c> bottom</c>

00:08:54.360 --> 00:08:54.370 align:start position:0%
the tower it'd be the one on the bottom
 

00:08:54.370 --> 00:08:57.090 align:start position:0%
the tower it'd be the one on the bottom
level<00:08:54.990><c> okay</c><00:08:55.990><c> so</c><00:08:56.050><c> in</c><00:08:56.350><c> the</c><00:08:56.410><c> next</c><00:08:56.560><c> video</c><00:08:56.770><c> we're</c>

00:08:57.090 --> 00:08:57.100 align:start position:0%
level okay so in the next video we're
 

00:08:57.100 --> 00:08:59.280 align:start position:0%
level okay so in the next video we're
gonna<00:08:57.190><c> go</c><00:08:57.340><c> over</c><00:08:57.430><c> insertion</c><00:08:58.060><c> and</c><00:08:58.270><c> deletion</c><00:08:58.300><c> but</c>

00:08:59.280 --> 00:08:59.290 align:start position:0%
gonna go over insertion and deletion but
 

00:08:59.290 --> 00:09:00.210 align:start position:0%
gonna go over insertion and deletion but
we're<00:08:59.470><c> still</c><00:08:59.590><c> gonna</c><00:08:59.680><c> be</c><00:08:59.740><c> using</c><00:08:59.890><c> the</c><00:09:00.040><c> same</c>

00:09:00.210 --> 00:09:00.220 align:start position:0%
we're still gonna be using the same
 

00:09:00.220 --> 00:09:02.330 align:start position:0%
we're still gonna be using the same
searching<00:09:00.820><c> algorithm</c><00:09:01.210><c> to</c><00:09:01.510><c> return</c><00:09:01.870><c> a</c><00:09:01.900><c> node</c>

00:09:02.330 --> 00:09:02.340 align:start position:0%
searching algorithm to return a node
 

00:09:02.340 --> 00:09:05.900 align:start position:0%
searching algorithm to return a node
okay<00:09:03.340><c> and</c><00:09:03.550><c> then</c><00:09:03.640><c> based</c><00:09:03.910><c> on</c><00:09:04.030><c> what</c><00:09:04.090><c> we</c><00:09:04.300><c> find</c><00:09:04.620><c> is</c>

00:09:05.900 --> 00:09:05.910 align:start position:0%
okay and then based on what we find is
 

00:09:05.910 --> 00:09:08.220 align:start position:0%
okay and then based on what we find is
how<00:09:06.910><c> we're</c><00:09:07.060><c> going</c><00:09:07.180><c> to</c><00:09:07.240><c> execute</c><00:09:07.570><c> the</c><00:09:07.870><c> rest</c><00:09:08.020><c> of</c>

00:09:08.220 --> 00:09:08.230 align:start position:0%
how we're going to execute the rest of
 

00:09:08.230 --> 00:09:11.970 align:start position:0%
how we're going to execute the rest of
that<00:09:08.320><c> algorithm</c><00:09:09.480><c> all</c><00:09:10.480><c> right</c><00:09:10.690><c> so</c><00:09:11.530><c> I'll</c><00:09:11.740><c> see</c><00:09:11.920><c> you</c>

00:09:11.970 --> 00:09:11.980 align:start position:0%
that algorithm all right so I'll see you
 

00:09:11.980 --> 00:09:14.250 align:start position:0%
that algorithm all right so I'll see you
next<00:09:12.160><c> time</c>

