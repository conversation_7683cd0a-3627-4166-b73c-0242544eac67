WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:03.270 align:start position:0%
 
and<00:00:01.439><c> howdy</c><00:00:01.839><c> everybody</c><00:00:02.399><c> in</c><00:00:02.480><c> this</c><00:00:02.720><c> tutorial</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
and howdy everybody in this tutorial
 

00:00:03.280 --> 00:00:04.870 align:start position:0%
and howdy everybody in this tutorial
we're<00:00:03.520><c> going</c><00:00:03.600><c> to</c><00:00:03.679><c> show</c><00:00:03.840><c> you</c><00:00:04.080><c> how</c><00:00:04.240><c> to</c><00:00:04.400><c> deploy</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
we're going to show you how to deploy
 

00:00:04.880 --> 00:00:07.829 align:start position:0%
we're going to show you how to deploy
wordpress<00:00:05.600><c> onto</c><00:00:06.560><c> digitalocean</c><00:00:07.440><c> so</c><00:00:07.600><c> you'll</c>

00:00:07.829 --> 00:00:07.839 align:start position:0%
wordpress onto digitalocean so you'll
 

00:00:07.839 --> 00:00:11.669 align:start position:0%
wordpress onto digitalocean so you'll
see<00:00:08.000><c> in</c><00:00:08.160><c> the</c><00:00:08.960><c> description</c><00:00:09.679><c> a</c><00:00:09.840><c> link</c><00:00:10.240><c> to</c><00:00:10.800><c> um</c>

00:00:11.669 --> 00:00:11.679 align:start position:0%
see in the description a link to um
 

00:00:11.679 --> 00:00:14.709 align:start position:0%
see in the description a link to um
this<00:00:12.320><c> wordpress</c><00:00:13.360><c> default</c><00:00:14.000><c> droplet</c><00:00:14.559><c> on</c>

00:00:14.709 --> 00:00:14.719 align:start position:0%
this wordpress default droplet on
 

00:00:14.719 --> 00:00:16.710 align:start position:0%
this wordpress default droplet on
digitalocean<00:00:15.519><c> you're</c><00:00:15.679><c> just</c><00:00:15.839><c> gonna</c><00:00:16.320><c> go</c><00:00:16.480><c> ahead</c>

00:00:16.710 --> 00:00:16.720 align:start position:0%
digitalocean you're just gonna go ahead
 

00:00:16.720 --> 00:00:20.790 align:start position:0%
digitalocean you're just gonna go ahead
and<00:00:16.880><c> press</c><00:00:17.119><c> create</c><00:00:17.520><c> wordpress</c><00:00:18.160><c> droplet</c>

00:00:20.790 --> 00:00:20.800 align:start position:0%
 
 

00:00:20.800 --> 00:00:22.870 align:start position:0%
 
okay<00:00:21.199><c> once</c><00:00:21.520><c> we</c><00:00:21.680><c> do</c><00:00:21.920><c> that</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
okay once we do that
 

00:00:22.880 --> 00:00:23.910 align:start position:0%
okay once we do that
then

00:00:23.910 --> 00:00:23.920 align:start position:0%
then
 

00:00:23.920 --> 00:00:27.429 align:start position:0%
then
we'll<00:00:24.080><c> be</c><00:00:24.320><c> able</c><00:00:25.119><c> to</c><00:00:25.680><c> um</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
we'll be able to um
 

00:00:27.439 --> 00:00:28.870 align:start position:0%
we'll be able to um
to<00:00:27.680><c> just</c>

00:00:28.870 --> 00:00:28.880 align:start position:0%
to just
 

00:00:28.880 --> 00:00:31.910 align:start position:0%
to just
it'll<00:00:29.199><c> spin</c><00:00:29.599><c> up</c><00:00:29.840><c> a</c><00:00:30.000><c> wordpress</c><00:00:30.720><c> droplet</c><00:00:31.679><c> let's</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
it'll spin up a wordpress droplet let's
 

00:00:31.920 --> 00:00:34.310 align:start position:0%
it'll spin up a wordpress droplet let's
go<00:00:32.079><c> ahead</c><00:00:32.559><c> and</c><00:00:32.960><c> just</c><00:00:33.120><c> set</c><00:00:33.360><c> it</c><00:00:33.440><c> to</c><00:00:33.600><c> six</c><00:00:33.920><c> dollars</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
go ahead and just set it to six dollars
 

00:00:34.320 --> 00:00:37.030 align:start position:0%
go ahead and just set it to six dollars
per<00:00:34.559><c> month</c><00:00:34.960><c> just</c><00:00:35.200><c> as</c><00:00:35.360><c> simple</c><00:00:35.680><c> as</c><00:00:35.840><c> possible</c><00:00:36.719><c> set</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
per month just as simple as possible set
 

00:00:37.040 --> 00:00:38.470 align:start position:0%
per month just as simple as possible set
to<00:00:37.200><c> london</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
to london
 

00:00:38.480 --> 00:00:41.670 align:start position:0%
to london
give<00:00:38.640><c> it</c><00:00:38.879><c> a</c><00:00:39.520><c> unique</c><00:00:40.160><c> name</c><00:00:40.559><c> if</c><00:00:40.719><c> you</c><00:00:40.960><c> want</c><00:00:41.200><c> to</c><00:00:41.520><c> but</c>

00:00:41.670 --> 00:00:41.680 align:start position:0%
give it a unique name if you want to but
 

00:00:41.680 --> 00:00:44.229 align:start position:0%
give it a unique name if you want to but
you<00:00:41.840><c> don't</c><00:00:42.000><c> need</c><00:00:42.239><c> to</c><00:00:42.719><c> um</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
you don't need to um
 

00:00:44.239 --> 00:00:45.830 align:start position:0%
you don't need to um
i'll<00:00:44.480><c> just</c><00:00:44.719><c> call</c><00:00:44.960><c> it</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
i'll just call it
 

00:00:45.840 --> 00:00:47.590 align:start position:0%
i'll just call it
wwp

00:00:47.590 --> 00:00:47.600 align:start position:0%
wwp
 

00:00:47.600 --> 00:00:49.590 align:start position:0%
wwp
wp<00:00:48.320><c> droplet</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
wp droplet
 

00:00:49.600 --> 00:00:51.310 align:start position:0%
wp droplet
so<00:00:49.760><c> that</c><00:00:49.920><c> it's</c><00:00:50.160><c> easier</c><00:00:50.879><c> um</c>

00:00:51.310 --> 00:00:51.320 align:start position:0%
so that it's easier um
 

00:00:51.320 --> 00:00:53.029 align:start position:0%
so that it's easier um
[Music]

00:00:53.029 --> 00:00:53.039 align:start position:0%
[Music]
 

00:00:53.039 --> 00:00:54.069 align:start position:0%
[Music]
click<00:00:53.280><c> on</c>

00:00:54.069 --> 00:00:54.079 align:start position:0%
click on
 

00:00:54.079 --> 00:00:56.069 align:start position:0%
click on
identify<00:00:54.960><c> exactly</c><00:00:55.440><c> and</c><00:00:55.600><c> we're</c><00:00:55.760><c> going</c><00:00:55.840><c> to</c><00:00:55.920><c> go</c>

00:00:56.069 --> 00:00:56.079 align:start position:0%
identify exactly and we're going to go
 

00:00:56.079 --> 00:00:58.790 align:start position:0%
identify exactly and we're going to go
ahead<00:00:56.399><c> and</c><00:00:56.640><c> create</c><00:00:57.039><c> a</c><00:00:57.199><c> password</c><00:00:57.760><c> as</c><00:00:58.000><c> well</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
ahead and create a password as well
 

00:00:58.800 --> 00:01:00.069 align:start position:0%
ahead and create a password as well
um

00:01:00.069 --> 00:01:00.079 align:start position:0%
um
 

00:01:00.079 --> 00:01:01.670 align:start position:0%
um
and<00:01:00.239><c> we're</c><00:01:00.399><c> going</c><00:01:00.559><c> to</c><00:01:00.640><c> paste</c><00:01:00.960><c> the</c><00:01:01.039><c> password</c><00:01:01.520><c> in</c>

00:01:01.670 --> 00:01:01.680 align:start position:0%
and we're going to paste the password in
 

00:01:01.680 --> 00:01:03.349 align:start position:0%
and we're going to paste the password in
there

00:01:03.349 --> 00:01:03.359 align:start position:0%
there
 

00:01:03.359 --> 00:01:04.950 align:start position:0%
there
and<00:01:03.520><c> we're</c><00:01:03.680><c> going</c><00:01:03.760><c> to</c><00:01:03.840><c> need</c><00:01:04.000><c> that</c><00:01:04.239><c> password</c><00:01:04.799><c> in</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
and we're going to need that password in
 

00:01:04.960 --> 00:01:06.870 align:start position:0%
and we're going to need that password in
a<00:01:05.040><c> later</c><00:01:05.360><c> stage</c><00:01:05.680><c> very</c><00:01:06.000><c> shortly</c><00:01:06.400><c> when</c><00:01:06.560><c> we</c><00:01:06.720><c> try</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
a later stage very shortly when we try
 

00:01:06.880 --> 00:01:09.190 align:start position:0%
a later stage very shortly when we try
to<00:01:07.040><c> ssh</c><00:01:07.680><c> into</c><00:01:08.000><c> it</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
to ssh into it
 

00:01:09.200 --> 00:01:10.950 align:start position:0%
to ssh into it
via<00:01:09.520><c> the</c><00:01:09.600><c> command</c><00:01:10.000><c> line</c><00:01:10.400><c> and</c><00:01:10.560><c> we're</c><00:01:10.720><c> going</c><00:01:10.880><c> to</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
via the command line and we're going to
 

00:01:10.960 --> 00:01:14.630 align:start position:0%
via the command line and we're going to
press<00:01:11.439><c> go</c><00:01:11.600><c> ahead</c><00:01:11.840><c> and</c><00:01:12.000><c> press</c><00:01:12.640><c> create</c><00:01:13.119><c> droplet</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
press go ahead and press create droplet
 

00:01:14.640 --> 00:01:16.469 align:start position:0%
press go ahead and press create droplet
okay<00:01:15.119><c> so</c><00:01:15.280><c> that</c><00:01:15.439><c> looks</c><00:01:15.680><c> pretty</c><00:01:16.000><c> good</c><00:01:16.320><c> it's</c>

00:01:16.469 --> 00:01:16.479 align:start position:0%
okay so that looks pretty good it's
 

00:01:16.479 --> 00:01:18.550 align:start position:0%
okay so that looks pretty good it's
gonna<00:01:16.720><c> go</c><00:01:16.880><c> ahead</c><00:01:17.040><c> and</c><00:01:17.200><c> spin</c><00:01:17.520><c> up</c><00:01:18.159><c> on</c><00:01:18.400><c> your</c>

00:01:18.550 --> 00:01:18.560 align:start position:0%
gonna go ahead and spin up on your
 

00:01:18.560 --> 00:01:22.149 align:start position:0%
gonna go ahead and spin up on your
wordpress<00:01:19.200><c> machine</c><00:01:20.000><c> you</c><00:01:20.320><c> can</c><00:01:20.640><c> um</c>

00:01:22.149 --> 00:01:22.159 align:start position:0%
wordpress machine you can um
 

00:01:22.159 --> 00:01:25.190 align:start position:0%
wordpress machine you can um
you<00:01:22.320><c> can</c><00:01:22.479><c> go</c><00:01:22.640><c> into</c><00:01:22.880><c> your</c><00:01:23.520><c> into</c><00:01:23.840><c> your</c><00:01:24.080><c> uh</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
you can go into your into your uh
 

00:01:25.200 --> 00:01:26.710 align:start position:0%
you can go into your into your uh
your<00:01:25.439><c> command</c><00:01:25.920><c> open</c><00:01:26.159><c> up</c><00:01:26.240><c> something</c><00:01:26.560><c> called</c>

00:01:26.710 --> 00:01:26.720 align:start position:0%
your command open up something called
 

00:01:26.720 --> 00:01:29.350 align:start position:0%
your command open up something called
the<00:01:26.880><c> command</c><00:01:27.360><c> prompt</c><00:01:28.320><c> okay</c><00:01:28.640><c> so</c><00:01:28.799><c> you</c><00:01:28.880><c> just</c>

00:01:29.350 --> 00:01:29.360 align:start position:0%
the command prompt okay so you just
 

00:01:29.360 --> 00:01:31.910 align:start position:0%
the command prompt okay so you just
search<00:01:29.759><c> on</c><00:01:30.079><c> on</c><00:01:30.560><c> on</c><00:01:30.799><c> wordpress</c><00:01:31.360><c> for</c><00:01:31.520><c> command</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
search on on on wordpress for command
 

00:01:31.920 --> 00:01:33.190 align:start position:0%
search on on on wordpress for command
prompt<00:01:32.240><c> and</c><00:01:32.320><c> you'll</c><00:01:32.479><c> see</c><00:01:32.640><c> something</c><00:01:32.960><c> like</c>

00:01:33.190 --> 00:01:33.200 align:start position:0%
prompt and you'll see something like
 

00:01:33.200 --> 00:01:33.990 align:start position:0%
prompt and you'll see something like
this

00:01:33.990 --> 00:01:34.000 align:start position:0%
this
 

00:01:34.000 --> 00:01:36.310 align:start position:0%
this
if<00:01:34.159><c> you</c><00:01:34.240><c> go</c><00:01:34.479><c> back</c><00:01:34.799><c> into</c>

00:01:36.310 --> 00:01:36.320 align:start position:0%
if you go back into
 

00:01:36.320 --> 00:01:37.670 align:start position:0%
if you go back into
the<00:01:36.640><c> initial</c><00:01:37.040><c> link</c><00:01:37.280><c> you're</c><00:01:37.360><c> going</c><00:01:37.439><c> to</c><00:01:37.520><c> see</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
the initial link you're going to see
 

00:01:37.680 --> 00:01:39.510 align:start position:0%
the initial link you're going to see
that<00:01:37.840><c> you</c><00:01:38.000><c> have</c><00:01:38.159><c> a</c><00:01:38.240><c> command</c><00:01:38.640><c> that</c><00:01:38.799><c> you</c><00:01:38.880><c> need</c><00:01:39.119><c> to</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
that you have a command that you need to
 

00:01:39.520 --> 00:01:41.270 align:start position:0%
that you have a command that you need to
run<00:01:40.159><c> so</c><00:01:40.320><c> i'm</c><00:01:40.400><c> going</c><00:01:40.479><c> to</c><00:01:40.560><c> go</c><00:01:40.720><c> ahead</c><00:01:40.880><c> and</c><00:01:41.040><c> paste</c>

00:01:41.270 --> 00:01:41.280 align:start position:0%
run so i'm going to go ahead and paste
 

00:01:41.280 --> 00:01:42.550 align:start position:0%
run so i'm going to go ahead and paste
that<00:01:41.520><c> in</c><00:01:41.680><c> there</c>

00:01:42.550 --> 00:01:42.560 align:start position:0%
that in there
 

00:01:42.560 --> 00:01:46.149 align:start position:0%
that in there
and<00:01:42.880><c> your</c><00:01:43.119><c> droplet</c><00:01:43.920><c> ip</c><00:01:44.479><c> address</c><00:01:45.439><c> is</c>

00:01:46.149 --> 00:01:46.159 align:start position:0%
and your droplet ip address is
 

00:01:46.159 --> 00:01:49.109 align:start position:0%
and your droplet ip address is
recommended<00:01:46.880><c> ssh</c><00:01:47.600><c> root</c><00:01:48.399><c> at</c><00:01:48.640><c> whatever</c><00:01:48.960><c> the</c>

00:01:49.109 --> 00:01:49.119 align:start position:0%
recommended ssh root at whatever the
 

00:01:49.119 --> 00:01:51.429 align:start position:0%
recommended ssh root at whatever the
droplet<00:01:49.680><c> ip</c><00:01:50.079><c> address</c><00:01:50.479><c> which</c><00:01:50.720><c> is</c><00:01:50.799><c> about</c><00:01:51.040><c> to</c><00:01:51.200><c> get</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
droplet ip address which is about to get
 

00:01:51.439 --> 00:01:53.429 align:start position:0%
droplet ip address which is about to get
created<00:01:51.920><c> over</c><00:01:52.159><c> here</c><00:01:52.560><c> okay</c><00:01:52.880><c> now</c><00:01:53.040><c> that</c><00:01:53.200><c> it's</c>

00:01:53.429 --> 00:01:53.439 align:start position:0%
created over here okay now that it's
 

00:01:53.439 --> 00:01:55.590 align:start position:0%
created over here okay now that it's
done<00:01:53.680><c> it</c><00:01:53.840><c> creates</c><00:01:54.159><c> an</c><00:01:54.320><c> ip</c><00:01:54.640><c> address</c><00:01:55.360><c> i'm</c><00:01:55.520><c> going</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
done it creates an ip address i'm going
 

00:01:55.600 --> 00:01:57.190 align:start position:0%
done it creates an ip address i'm going
to<00:01:55.680><c> paste</c><00:01:56.000><c> that</c><00:01:56.159><c> as</c><00:01:56.240><c> well</c><00:01:56.479><c> into</c><00:01:56.640><c> the</c><00:01:56.719><c> command</c>

00:01:57.190 --> 00:01:57.200 align:start position:0%
to paste that as well into the command
 

00:01:57.200 --> 00:01:59.109 align:start position:0%
to paste that as well into the command
line<00:01:57.680><c> and</c><00:01:58.159><c> as</c><00:01:58.399><c> you</c><00:01:58.479><c> can</c><00:01:58.560><c> see</c><00:01:58.719><c> they</c><00:01:58.880><c> make</c><00:01:59.040><c> it</c>

00:01:59.109 --> 00:01:59.119 align:start position:0%
line and as you can see they make it
 

00:01:59.119 --> 00:02:00.230 align:start position:0%
line and as you can see they make it
really<00:01:59.360><c> easy</c><00:01:59.600><c> are</c><00:01:59.680><c> you</c><00:01:59.759><c> sure</c><00:01:59.920><c> you</c><00:02:00.000><c> want</c><00:02:00.159><c> to</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
really easy are you sure you want to
 

00:02:00.240 --> 00:02:01.429 align:start position:0%
really easy are you sure you want to
continue

00:02:01.429 --> 00:02:01.439 align:start position:0%
continue
 

00:02:01.439 --> 00:02:02.950 align:start position:0%
continue
yes

00:02:02.950 --> 00:02:02.960 align:start position:0%
yes
 

00:02:02.960 --> 00:02:05.510 align:start position:0%
yes
okay<00:02:03.520><c> oh</c><00:02:03.840><c> permanently</c><00:02:04.399><c> added</c><00:02:04.799><c> blank</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
okay oh permanently added blank
 

00:02:05.520 --> 00:02:08.150 align:start position:0%
okay oh permanently added blank
permission<00:02:06.079><c> denied</c><00:02:06.799><c> because</c><00:02:07.200><c> of</c><00:02:07.360><c> a</c><00:02:07.520><c> public</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
permission denied because of a public
 

00:02:08.160 --> 00:02:09.109 align:start position:0%
permission denied because of a public
key

00:02:09.109 --> 00:02:09.119 align:start position:0%
key
 

00:02:09.119 --> 00:02:11.029 align:start position:0%
key
oh<00:02:09.360><c> schneids</c><00:02:09.840><c> i</c><00:02:09.920><c> need</c><00:02:10.080><c> to</c><00:02:10.160><c> have</c><00:02:10.319><c> a</c><00:02:10.399><c> public</c><00:02:10.800><c> key</c>

00:02:11.029 --> 00:02:11.039 align:start position:0%
oh schneids i need to have a public key
 

00:02:11.039 --> 00:02:13.030 align:start position:0%
oh schneids i need to have a public key
to<00:02:11.200><c> cr</c><00:02:11.440><c> to</c><00:02:11.599><c> connect</c><00:02:12.000><c> to</c><00:02:12.080><c> this</c><00:02:12.319><c> thing</c><00:02:12.640><c> for</c><00:02:12.800><c> some</c>

00:02:13.030 --> 00:02:13.040 align:start position:0%
to cr to connect to this thing for some
 

00:02:13.040 --> 00:02:14.949 align:start position:0%
to cr to connect to this thing for some
reason<00:02:13.440><c> let's</c><00:02:13.680><c> try</c><00:02:13.840><c> it</c><00:02:14.000><c> again</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
reason let's try it again
 

00:02:14.959 --> 00:02:17.990 align:start position:0%
reason let's try it again
ssh<00:02:15.760><c> root</c><00:02:16.160><c> at</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
ssh root at
 

00:02:18.000 --> 00:02:19.910 align:start position:0%
ssh root at
like

00:02:19.910 --> 00:02:19.920 align:start position:0%
like
 

00:02:19.920 --> 00:02:22.229 align:start position:0%
like
oh<00:02:20.239><c> okay</c><00:02:20.560><c> good</c><00:02:20.959><c> that</c><00:02:21.200><c> time</c><00:02:21.440><c> i</c><00:02:21.520><c> can</c><00:02:21.680><c> just</c><00:02:21.920><c> put</c><00:02:22.080><c> in</c>

00:02:22.229 --> 00:02:22.239 align:start position:0%
oh okay good that time i can just put in
 

00:02:22.239 --> 00:02:24.229 align:start position:0%
oh okay good that time i can just put in
a<00:02:22.319><c> password</c><00:02:22.720><c> so</c><00:02:22.879><c> that's</c><00:02:23.120><c> positive</c>

00:02:24.229 --> 00:02:24.239 align:start position:0%
a password so that's positive
 

00:02:24.239 --> 00:02:26.309 align:start position:0%
a password so that's positive
and<00:02:24.480><c> that's</c><00:02:24.879><c> when</c><00:02:25.120><c> you</c><00:02:25.280><c> put</c><00:02:25.520><c> in</c><00:02:25.599><c> that</c><00:02:25.840><c> password</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
and that's when you put in that password
 

00:02:26.319 --> 00:02:28.949 align:start position:0%
and that's when you put in that password
that<00:02:26.480><c> we</c><00:02:26.640><c> created</c><00:02:27.040><c> a</c><00:02:27.200><c> second</c><00:02:27.520><c> ago</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
that we created a second ago
 

00:02:28.959 --> 00:02:30.710 align:start position:0%
that we created a second ago
okay

00:02:30.710 --> 00:02:30.720 align:start position:0%
okay
 

00:02:30.720 --> 00:02:32.630 align:start position:0%
okay
you<00:02:30.879><c> can</c><00:02:31.040><c> just</c><00:02:31.200><c> give</c><00:02:31.440><c> it</c><00:02:31.599><c> a</c><00:02:31.680><c> second</c>

00:02:32.630 --> 00:02:32.640 align:start position:0%
you can just give it a second
 

00:02:32.640 --> 00:02:34.830 align:start position:0%
you can just give it a second
um<00:02:33.360><c> after</c><00:02:33.680><c> you</c><00:02:33.840><c> create</c>

00:02:34.830 --> 00:02:34.840 align:start position:0%
um after you create
 

00:02:34.840 --> 00:02:37.030 align:start position:0%
um after you create
it<00:02:35.519><c> one</c><00:02:35.840><c> sec</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
it one sec
 

00:02:37.040 --> 00:02:41.910 align:start position:0%
it one sec
okay<00:02:37.599><c> copy</c><00:02:38.000><c> that</c><00:02:38.720><c> and</c>

00:02:41.910 --> 00:02:41.920 align:start position:0%
 
 

00:02:41.920 --> 00:02:48.229 align:start position:0%
 
let's<00:02:42.080><c> see</c><00:02:42.239><c> what</c><00:02:42.319><c> happens</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
 
 

00:02:48.239 --> 00:02:50.710 align:start position:0%
 
oh<00:02:48.560><c> permission</c><00:02:49.040><c> denied</c><00:02:49.840><c> okay</c><00:02:50.239><c> why</c><00:02:50.480><c> is</c><00:02:50.560><c> the</c>

00:02:50.710 --> 00:02:50.720 align:start position:0%
oh permission denied okay why is the
 

00:02:50.720 --> 00:02:52.309 align:start position:0%
oh permission denied okay why is the
permission<00:02:51.200><c> denied</c><00:02:51.599><c> maybe</c><00:02:51.840><c> i</c><00:02:51.920><c> put</c><00:02:52.160><c> in</c><00:02:52.239><c> the</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
permission denied maybe i put in the
 

00:02:52.319 --> 00:02:54.070 align:start position:0%
permission denied maybe i put in the
wrong<00:02:52.560><c> password</c>

00:02:54.070 --> 00:02:54.080 align:start position:0%
wrong password
 

00:02:54.080 --> 00:03:12.869 align:start position:0%
wrong password
let's<00:02:54.319><c> try</c><00:02:54.560><c> one</c><00:02:54.720><c> more</c><00:02:55.040><c> time</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
 
 

00:03:12.879 --> 00:03:15.350 align:start position:0%
 
that<00:03:13.040><c> time</c><00:03:13.280><c> i</c><00:03:13.440><c> just</c><00:03:13.840><c> pressed</c><00:03:14.080><c> the</c><00:03:14.400><c> right</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
that time i just pressed the right
 

00:03:15.360 --> 00:03:16.550 align:start position:0%
that time i just pressed the right
um

00:03:16.550 --> 00:03:16.560 align:start position:0%
um
 

00:03:16.560 --> 00:03:18.550 align:start position:0%
um
right<00:03:16.959><c> mouse</c><00:03:17.360><c> click</c><00:03:17.840><c> and</c><00:03:17.920><c> then</c><00:03:18.080><c> it</c><00:03:18.239><c> worked</c>

00:03:18.550 --> 00:03:18.560 align:start position:0%
right mouse click and then it worked
 

00:03:18.560 --> 00:03:20.630 align:start position:0%
right mouse click and then it worked
okay<00:03:18.879><c> domain</c><00:03:19.280><c> sub</c><00:03:19.519><c> domain</c><00:03:20.000><c> it's</c><00:03:20.319><c> king</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
okay domain sub domain it's king
 

00:03:20.640 --> 00:03:22.530 align:start position:0%
okay domain sub domain it's king
commerce

00:03:22.530 --> 00:03:22.540 align:start position:0%
commerce
 

00:03:22.540 --> 00:03:24.470 align:start position:0%
commerce
[Music]

00:03:24.470 --> 00:03:24.480 align:start position:0%
[Music]
 

00:03:24.480 --> 00:03:26.789 align:start position:0%
[Music]
commerce

00:03:26.789 --> 00:03:26.799 align:start position:0%
commerce
 

00:03:26.799 --> 00:03:28.229 align:start position:0%
commerce
okay

00:03:28.229 --> 00:03:28.239 align:start position:0%
okay
 

00:03:28.239 --> 00:03:29.670 align:start position:0%
okay
that<00:03:28.400><c> looks</c><00:03:28.640><c> pretty</c><00:03:28.879><c> good</c>

00:03:29.670 --> 00:03:29.680 align:start position:0%
that looks pretty good
 

00:03:29.680 --> 00:03:32.789 align:start position:0%
that looks pretty good
okay<00:03:30.159><c> your</c><00:03:30.319><c> email</c><00:03:30.720><c> address</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
okay your email address
 

00:03:32.799 --> 00:03:36.470 align:start position:0%
okay your email address
1346

00:03:36.470 --> 00:03:36.480 align:start position:0%
 
 

00:03:36.480 --> 00:03:38.309 align:start position:0%
 
gmail.com

00:03:38.309 --> 00:03:38.319 align:start position:0%
gmail.com
 

00:03:38.319 --> 00:03:42.070 align:start position:0%
gmail.com
and<00:03:38.560><c> my</c><00:03:38.720><c> username</c><00:03:39.599><c> framework1346</c>

00:03:42.070 --> 00:03:42.080 align:start position:0%
and my username framework1346
 

00:03:42.080 --> 00:03:45.430 align:start position:0%
and my username framework1346
and<00:03:42.319><c> my</c><00:03:42.480><c> password</c>

00:03:45.430 --> 00:03:45.440 align:start position:0%
 
 

00:03:45.440 --> 00:03:46.949 align:start position:0%
 
okay<00:03:46.080><c> and</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
okay and
 

00:03:46.959 --> 00:03:49.509 align:start position:0%
okay and
i'll<00:03:47.280><c> call</c><00:03:47.519><c> it</c><00:03:47.680><c> the</c><00:03:47.840><c> king</c><00:03:48.159><c> kong</c><00:03:48.560><c> site</c>

00:03:49.509 --> 00:03:49.519 align:start position:0%
i'll call it the king kong site
 

00:03:49.519 --> 00:03:51.270 align:start position:0%
i'll call it the king kong site
yes<00:03:49.840><c> it's</c><00:03:50.080><c> correct</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
yes it's correct
 

00:03:51.280 --> 00:03:53.429 align:start position:0%
yes it's correct
uh<00:03:51.599><c> i</c><00:03:51.680><c> don't</c><00:03:51.840><c> want</c><00:03:52.000><c> to</c><00:03:52.080><c> use</c><00:03:52.319><c> latin</c><00:03:52.799><c> crypt</c><00:03:53.120><c> in</c><00:03:53.200><c> my</c>

00:03:53.429 --> 00:03:53.439 align:start position:0%
uh i don't want to use latin crypt in my
 

00:03:53.439 --> 00:03:54.710 align:start position:0%
uh i don't want to use latin crypt in my
case<00:03:53.840><c> i'm</c><00:03:54.000><c> gonna</c>

00:03:54.710 --> 00:03:54.720 align:start position:0%
case i'm gonna
 

00:03:54.720 --> 00:03:56.229 align:start position:0%
case i'm gonna
uh<00:03:55.040><c> instead</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
uh instead
 

00:03:56.239 --> 00:03:57.910 align:start position:0%
uh instead
uh<00:03:56.560><c> do</c><00:03:56.799><c> the</c>

00:03:57.910 --> 00:03:57.920 align:start position:0%
uh do the
 

00:03:57.920 --> 00:04:01.350 align:start position:0%
uh do the
https<00:03:58.959><c> and</c><00:03:59.360><c> ssl</c><00:04:00.000><c> via</c>

00:04:01.350 --> 00:04:01.360 align:start position:0%
https and ssl via
 

00:04:01.360 --> 00:04:03.190 align:start position:0%
https and ssl via
cloudflare<00:04:02.000><c> and</c><00:04:02.159><c> part</c><00:04:02.400><c> two</c><00:04:02.640><c> which</c><00:04:02.799><c> you'll</c><00:04:02.959><c> see</c>

00:04:03.190 --> 00:04:03.200 align:start position:0%
cloudflare and part two which you'll see
 

00:04:03.200 --> 00:04:04.630 align:start position:0%
cloudflare and part two which you'll see
very<00:04:03.439><c> shortly</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
very shortly
 

00:04:04.640 --> 00:04:06.789 align:start position:0%
very shortly
okay<00:04:05.040><c> and</c><00:04:05.200><c> then</c><00:04:05.680><c> it</c><00:04:05.840><c> says</c><00:04:06.080><c> that</c><00:04:06.239><c> everything</c><00:04:06.640><c> is</c>

00:04:06.789 --> 00:04:06.799 align:start position:0%
okay and then it says that everything is
 

00:04:06.799 --> 00:04:10.710 align:start position:0%
okay and then it says that everything is
complete<00:04:07.439><c> i</c><00:04:07.760><c> just</c><00:04:08.480><c> uh</c><00:04:08.879><c> visit</c><00:04:09.200><c> that</c><00:04:09.519><c> ip</c><00:04:09.920><c> address</c>

00:04:10.710 --> 00:04:10.720 align:start position:0%
complete i just uh visit that ip address
 

00:04:10.720 --> 00:04:13.110 align:start position:0%
complete i just uh visit that ip address
within<00:04:11.120><c> my</c><00:04:11.280><c> browser</c><00:04:12.000><c> and</c><00:04:12.560><c> there's</c><00:04:12.959><c> our</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
within my browser and there's our
 

00:04:13.120 --> 00:04:15.350 align:start position:0%
within my browser and there's our
wordpress<00:04:13.680><c> site</c><00:04:14.080><c> up</c><00:04:14.239><c> and</c><00:04:14.400><c> running</c><00:04:15.120><c> all</c><00:04:15.200><c> right</c>

00:04:15.350 --> 00:04:15.360 align:start position:0%
wordpress site up and running all right
 

00:04:15.360 --> 00:04:17.430 align:start position:0%
wordpress site up and running all right
guys<00:04:15.760><c> so</c><00:04:16.000><c> i'll</c><00:04:16.239><c> see</c><00:04:16.400><c> you</c><00:04:16.560><c> in</c><00:04:16.639><c> the</c><00:04:16.799><c> next</c><00:04:17.120><c> video</c>

00:04:17.430 --> 00:04:17.440 align:start position:0%
guys so i'll see you in the next video
 

00:04:17.440 --> 00:04:22.680 align:start position:0%
guys so i'll see you in the next video
where<00:04:17.680><c> we'll</c><00:04:17.840><c> set</c><00:04:18.079><c> up</c><00:04:18.639><c> our</c><00:04:18.799><c> cloudflare</c><00:04:19.680><c> dns</c>

