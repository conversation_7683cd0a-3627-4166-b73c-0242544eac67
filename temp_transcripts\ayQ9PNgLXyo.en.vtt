WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:03.200 align:start position:0%
 
all<00:00:00.930><c> right</c><00:00:01.380><c> welcome</c><00:00:01.920><c> to</c><00:00:02.070><c> another</c><00:00:02.610><c> video</c><00:00:02.820><c> and</c>

00:00:03.200 --> 00:00:03.210 align:start position:0%
all right welcome to another video and
 

00:00:03.210 --> 00:00:05.180 align:start position:0%
all right welcome to another video and
this<00:00:03.840><c> time</c><00:00:04.080><c> we're</c><00:00:04.259><c> going</c><00:00:04.500><c> to</c><00:00:04.589><c> be</c><00:00:04.680><c> going</c><00:00:04.799><c> over</c>

00:00:05.180 --> 00:00:05.190 align:start position:0%
this time we're going to be going over
 

00:00:05.190 --> 00:00:07.789 align:start position:0%
this time we're going to be going over
to<00:00:05.430><c> two</c><00:00:06.299><c> more</c><00:00:06.509><c> big</c><00:00:06.750><c> o-notation</c><00:00:06.870><c> interview</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
to two more big o-notation interview
 

00:00:07.799 --> 00:00:11.240 align:start position:0%
to two more big o-notation interview
questions<00:00:08.130><c> and</c><00:00:08.599><c> as</c><00:00:09.599><c> always</c><00:00:09.920><c> when</c><00:00:10.920><c> we</c><00:00:11.010><c> go</c><00:00:11.130><c> over</c>

00:00:11.240 --> 00:00:11.250 align:start position:0%
questions and as always when we go over
 

00:00:11.250 --> 00:00:13.070 align:start position:0%
questions and as always when we go over
these<00:00:11.370><c> the</c><00:00:11.639><c> first</c><00:00:11.849><c> we</c><00:00:11.940><c> want</c><00:00:12.059><c> to</c><00:00:12.090><c> do</c><00:00:12.210><c> is</c><00:00:12.450><c> analyze</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
these the first we want to do is analyze
 

00:00:13.080 --> 00:00:14.539 align:start position:0%
these the first we want to do is analyze
the<00:00:13.320><c> code</c><00:00:13.469><c> make</c><00:00:13.860><c> sure</c><00:00:13.980><c> we</c><00:00:14.099><c> kind</c><00:00:14.250><c> of</c><00:00:14.309><c> understand</c>

00:00:14.539 --> 00:00:14.549 align:start position:0%
the code make sure we kind of understand
 

00:00:14.549 --> 00:00:15.230 align:start position:0%
the code make sure we kind of understand
what's<00:00:14.700><c> going</c><00:00:14.910><c> on</c>

00:00:15.230 --> 00:00:15.240 align:start position:0%
what's going on
 

00:00:15.240 --> 00:00:17.930 align:start position:0%
what's going on
okay<00:00:15.980><c> so</c><00:00:16.980><c> let's</c><00:00:17.160><c> going</c><00:00:17.369><c> in</c><00:00:17.460><c> the</c><00:00:17.520><c> first</c><00:00:17.699><c> example</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
okay so let's going in the first example
 

00:00:17.940 --> 00:00:21.080 align:start position:0%
okay so let's going in the first example
so<00:00:18.630><c> here</c><00:00:19.260><c> we</c><00:00:19.529><c> have</c><00:00:19.740><c> a</c><00:00:20.039><c> single</c><00:00:20.430><c> function</c><00:00:20.550><c> called</c>

00:00:21.080 --> 00:00:21.090 align:start position:0%
so here we have a single function called
 

00:00:21.090 --> 00:00:23.090 align:start position:0%
so here we have a single function called
print<00:00:21.180><c> order</c><00:00:21.720><c> unordered</c><00:00:22.199><c> pairs</c>

00:00:23.090 --> 00:00:23.100 align:start position:0%
print order unordered pairs
 

00:00:23.100 --> 00:00:25.820 align:start position:0%
print order unordered pairs
sometimes<00:00:23.670><c> note</c><00:00:23.880><c> here</c><00:00:24.150><c> is</c><00:00:24.480><c> that</c><00:00:25.350><c> there's</c><00:00:25.560><c> two</c>

00:00:25.820 --> 00:00:25.830 align:start position:0%
sometimes note here is that there's two
 

00:00:25.830 --> 00:00:28.519 align:start position:0%
sometimes note here is that there's two
inputs<00:00:26.220><c> here</c><00:00:26.369><c> we</c><00:00:26.580><c> have</c><00:00:26.779><c> one</c><00:00:27.779><c> we</c><00:00:28.080><c> have</c><00:00:28.170><c> two</c>

00:00:28.519 --> 00:00:28.529 align:start position:0%
inputs here we have one we have two
 

00:00:28.529 --> 00:00:32.630 align:start position:0%
inputs here we have one we have two
different<00:00:28.680><c> arrays</c><00:00:28.980><c> and</c><00:00:30.470><c> let's</c><00:00:31.470><c> make</c><00:00:32.160><c> sure</c><00:00:32.219><c> you</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
different arrays and let's make sure you
 

00:00:32.640 --> 00:00:34.130 align:start position:0%
different arrays and let's make sure you
just<00:00:32.790><c> understand</c><00:00:33.210><c> that</c><00:00:33.300><c> one</c><00:00:33.780><c> thing</c><00:00:33.960><c> to</c><00:00:34.020><c> look</c>

00:00:34.130 --> 00:00:34.140 align:start position:0%
just understand that one thing to look
 

00:00:34.140 --> 00:00:35.959 align:start position:0%
just understand that one thing to look
at<00:00:34.260><c> is</c><00:00:34.440><c> what</c><00:00:34.800><c> is</c><00:00:34.829><c> the</c><00:00:35.190><c> input</c><00:00:35.340><c> because</c><00:00:35.850><c> it</c>

00:00:35.959 --> 00:00:35.969 align:start position:0%
at is what is the input because it
 

00:00:35.969 --> 00:00:39.830 align:start position:0%
at is what is the input because it
matters<00:00:36.120><c> right</c><00:00:37.969><c> okay</c><00:00:38.969><c> so</c><00:00:39.030><c> let's</c><00:00:39.329><c> look</c><00:00:39.480><c> what</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
matters right okay so let's look what
 

00:00:39.840 --> 00:00:42.319 align:start position:0%
matters right okay so let's look what
it's<00:00:39.960><c> doing</c><00:00:40.110><c> look</c><00:00:40.469><c> there's</c><00:00:40.739><c> two</c><00:00:41.010><c> for</c><00:00:41.340><c> loops</c><00:00:41.520><c> an</c>

00:00:42.319 --> 00:00:42.329 align:start position:0%
it's doing look there's two for loops an
 

00:00:42.329 --> 00:00:45.080 align:start position:0%
it's doing look there's two for loops an
S&amp;S<00:00:43.050><c> for</c><00:00:43.350><c> loops</c><00:00:43.530><c> and</c><00:00:43.730><c> then</c><00:00:44.730><c> we</c><00:00:44.879><c> have</c><00:00:45.059><c> a</c>

00:00:45.080 --> 00:00:45.090 align:start position:0%
S&amp;S for loops and then we have a
 

00:00:45.090 --> 00:00:46.670 align:start position:0%
S&amp;S for loops and then we have a
comparator<00:00:45.750><c> operation</c><00:00:46.289><c> and</c><00:00:46.469><c> we're</c><00:00:46.620><c> just</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
comparator operation and we're just
 

00:00:46.680 --> 00:00:51.080 align:start position:0%
comparator operation and we're just
running<00:00:47.039><c> out</c><00:00:47.280><c> something</c><00:00:48.739><c> so</c><00:00:49.739><c> the</c><00:00:50.730><c> outer</c><00:00:50.850><c> loop</c>

00:00:51.080 --> 00:00:51.090 align:start position:0%
running out something so the outer loop
 

00:00:51.090 --> 00:00:53.689 align:start position:0%
running out something so the outer loop
is<00:00:51.270><c> going</c><00:00:51.480><c> through</c><00:00:52.250><c> going</c><00:00:53.250><c> through</c><00:00:53.399><c> all</c><00:00:53.670><c> of</c>

00:00:53.689 --> 00:00:53.699 align:start position:0%
is going through going through all of
 

00:00:53.699 --> 00:00:55.700 align:start position:0%
is going through going through all of
the<00:00:53.969><c> first</c><00:00:54.210><c> array</c><00:00:54.539><c> the</c><00:00:55.140><c> inner</c><00:00:55.350><c> loops</c><00:00:55.559><c> going</c>

00:00:55.700 --> 00:00:55.710 align:start position:0%
the first array the inner loops going
 

00:00:55.710 --> 00:00:57.500 align:start position:0%
the first array the inner loops going
through<00:00:55.980><c> all</c><00:00:56.160><c> the</c><00:00:56.309><c> second</c><00:00:56.640><c> array</c><00:00:56.850><c> and</c><00:00:57.449><c> then</c>

00:00:57.500 --> 00:00:57.510 align:start position:0%
through all the second array and then
 

00:00:57.510 --> 00:01:00.439 align:start position:0%
through all the second array and then
we're<00:00:57.660><c> comparing</c><00:00:58.079><c> if</c><00:00:58.289><c> the</c><00:00:58.410><c> value</c><00:00:58.890><c> at</c><00:00:59.670><c> index</c><00:01:00.149><c> I</c>

00:01:00.439 --> 00:01:00.449 align:start position:0%
we're comparing if the value at index I
 

00:01:00.449 --> 00:01:04.910 align:start position:0%
we're comparing if the value at index I
is<00:01:00.719><c> less</c><00:01:00.989><c> than</c><00:01:01.050><c> the</c><00:01:01.829><c> value</c><00:01:02.039><c> of</c><00:01:02.340><c> index</c><00:01:02.670><c> J</c><00:01:03.920><c> for</c>

00:01:04.910 --> 00:01:04.920 align:start position:0%
is less than the value of index J for
 

00:01:04.920 --> 00:01:06.590 align:start position:0%
is less than the value of index J for
each<00:01:04.979><c> array</c><00:01:05.280><c> and</c><00:01:05.790><c> then</c><00:01:05.880><c> we</c><00:01:05.939><c> read</c><00:01:06.030><c> print</c><00:01:06.330><c> amount</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
each array and then we read print amount
 

00:01:06.600 --> 00:01:10.190 align:start position:0%
each array and then we read print amount
of<00:01:06.689><c> in</c><00:01:06.810><c> it</c><00:01:06.900><c> if</c><00:01:07.020><c> they</c><00:01:07.170><c> are</c><00:01:07.200><c> okay</c><00:01:08.390><c> all</c><00:01:09.390><c> right</c><00:01:09.659><c> so</c>

00:01:10.190 --> 00:01:10.200 align:start position:0%
of in it if they are okay all right so
 

00:01:10.200 --> 00:01:11.000 align:start position:0%
of in it if they are okay all right so
those<00:01:10.290><c> are</c><00:01:10.439><c> gonna</c><00:01:10.590><c> print</c><00:01:10.770><c> out</c><00:01:10.830><c> we're</c><00:01:10.979><c> just</c>

00:01:11.000 --> 00:01:11.010 align:start position:0%
those are gonna print out we're just
 

00:01:11.010 --> 00:01:12.950 align:start position:0%
those are gonna print out we're just
printing<00:01:11.369><c> out</c><00:01:11.460><c> some</c><00:01:11.939><c> number</c><00:01:12.630><c> of</c><00:01:12.659><c> pairs</c>

00:01:12.950 --> 00:01:12.960 align:start position:0%
printing out some number of pairs
 

00:01:12.960 --> 00:01:15.280 align:start position:0%
printing out some number of pairs
depending<00:01:13.560><c> on</c><00:01:13.650><c> the</c><00:01:13.740><c> values</c><00:01:14.100><c> at</c><00:01:14.400><c> those</c><00:01:14.610><c> indexes</c>

00:01:15.280 --> 00:01:15.290 align:start position:0%
depending on the values at those indexes
 

00:01:15.290 --> 00:01:19.429 align:start position:0%
depending on the values at those indexes
all<00:01:16.290><c> right</c><00:01:17.720><c> well</c><00:01:18.720><c> one</c><00:01:19.080><c> of</c><00:01:19.140><c> the</c><00:01:19.200><c> first</c><00:01:19.350><c> thing</c>

00:01:19.429 --> 00:01:19.439 align:start position:0%
all right well one of the first thing
 

00:01:19.439 --> 00:01:21.080 align:start position:0%
all right well one of the first thing
you<00:01:19.530><c> want</c><00:01:19.680><c> to</c><00:01:19.710><c> do</c><00:01:19.830><c> is</c><00:01:20.130><c> when</c><00:01:20.340><c> eliminate</c><00:01:20.850><c> things</c>

00:01:21.080 --> 00:01:21.090 align:start position:0%
you want to do is when eliminate things
 

00:01:21.090 --> 00:01:25.749 align:start position:0%
you want to do is when eliminate things
that<00:01:21.540><c> we</c><00:01:21.720><c> don't</c><00:01:22.080><c> care</c><00:01:22.320><c> about</c><00:01:22.380><c> okay</c><00:01:23.189><c> and</c><00:01:24.619><c> one</c><00:01:25.619><c> is</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
that we don't care about okay and one is
 

00:01:25.759 --> 00:01:27.830 align:start position:0%
that we don't care about okay and one is
anything<00:01:26.759><c> that's</c><00:01:26.939><c> that</c><00:01:27.270><c> means</c><00:01:27.479><c> anything</c><00:01:27.659><c> is</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
anything that's that means anything is
 

00:01:27.840 --> 00:01:30.560 align:start position:0%
anything that's that means anything is
Big<00:01:28.049><c> O</c><00:01:28.200><c> of</c><00:01:28.229><c> one</c><00:01:28.740><c> or</c><00:01:28.950><c> constant</c><00:01:29.640><c> time</c><00:01:29.790><c> we</c><00:01:30.450><c> don't</c>

00:01:30.560 --> 00:01:30.570 align:start position:0%
Big O of one or constant time we don't
 

00:01:30.570 --> 00:01:33.590 align:start position:0%
Big O of one or constant time we don't
care<00:01:30.810><c> that</c><00:01:31.040><c> so</c><00:01:32.040><c> what</c><00:01:32.280><c> is</c><00:01:32.490><c> what</c><00:01:33.030><c> constitutes</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
care that so what is what constitutes
 

00:01:33.600 --> 00:01:35.660 align:start position:0%
care that so what is what constitutes
Big<00:01:33.810><c> O</c><00:01:33.930><c> of</c><00:01:33.960><c> one</c><00:01:34.200><c> well</c><00:01:34.650><c> anything</c><00:01:35.430><c> is</c><00:01:35.520><c> an</c>

00:01:35.660 --> 00:01:35.670 align:start position:0%
Big O of one well anything is an
 

00:01:35.670 --> 00:01:38.600 align:start position:0%
Big O of one well anything is an
assignment<00:01:36.060><c> operator</c><00:01:36.560><c> anytime</c><00:01:37.610><c> anytime</c>

00:01:38.600 --> 00:01:38.610 align:start position:0%
assignment operator anytime anytime
 

00:01:38.610 --> 00:01:40.580 align:start position:0%
assignment operator anytime anytime
there's<00:01:39.060><c> like</c><00:01:39.180><c> some</c><00:01:39.360><c> math</c><00:01:39.509><c> operation</c><00:01:40.079><c> whether</c>

00:01:40.580 --> 00:01:40.590 align:start position:0%
there's like some math operation whether
 

00:01:40.590 --> 00:01:43.340 align:start position:0%
there's like some math operation whether
you're<00:01:40.710><c> printing</c><00:01:40.890><c> returning</c><00:01:41.729><c> or</c><00:01:42.259><c> here</c><00:01:43.259><c> in</c>

00:01:43.340 --> 00:01:43.350 align:start position:0%
you're printing returning or here in
 

00:01:43.350 --> 00:01:47.210 align:start position:0%
you're printing returning or here in
this<00:01:43.409><c> case</c><00:01:43.619><c> comparing</c><00:01:45.380><c> no</c><00:01:46.380><c> matter</c><00:01:46.619><c> what</c><00:01:46.920><c> the</c>

00:01:47.210 --> 00:01:47.220 align:start position:0%
this case comparing no matter what the
 

00:01:47.220 --> 00:01:50.359 align:start position:0%
this case comparing no matter what the
size<00:01:47.460><c> of</c><00:01:47.820><c> the</c><00:01:47.909><c> input</c><00:01:48.329><c> is</c><00:01:48.740><c> those</c><00:01:49.740><c> operations</c>

00:01:50.359 --> 00:01:50.369 align:start position:0%
size of the input is those operations
 

00:01:50.369 --> 00:01:52.160 align:start position:0%
size of the input is those operations
always<00:01:51.329><c> take</c><00:01:51.509><c> the</c><00:01:51.600><c> same</c><00:01:51.750><c> amount</c><00:01:51.810><c> of</c><00:01:51.960><c> time</c>

00:01:52.160 --> 00:01:52.170 align:start position:0%
always take the same amount of time
 

00:01:52.170 --> 00:01:53.569 align:start position:0%
always take the same amount of time
which<00:01:52.470><c> you're</c><00:01:52.680><c> adding</c><00:01:52.920><c> something</c><00:01:53.159><c> or</c><00:01:53.490><c> you</c>

00:01:53.569 --> 00:01:53.579 align:start position:0%
which you're adding something or you
 

00:01:53.579 --> 00:01:55.370 align:start position:0%
which you're adding something or you
just<00:01:53.640><c> printing</c><00:01:54.149><c> something</c><00:01:54.600><c> it</c><00:01:55.079><c> doesn't</c>

00:01:55.370 --> 00:01:55.380 align:start position:0%
just printing something it doesn't
 

00:01:55.380 --> 00:01:57.649 align:start position:0%
just printing something it doesn't
matter<00:01:55.860><c> what</c><00:01:56.430><c> the</c><00:01:56.670><c> size</c><00:01:56.820><c> of</c><00:01:57.060><c> array</c><00:01:57.270><c> and</c><00:01:57.600><c> array</c>

00:01:57.649 --> 00:01:57.659 align:start position:0%
matter what the size of array and array
 

00:01:57.659 --> 00:01:59.690 align:start position:0%
matter what the size of array and array
B<00:01:57.899><c> is</c><00:01:58.079><c> those</c><00:01:58.649><c> are</c><00:01:58.799><c> always</c><00:01:59.009><c> going</c><00:01:59.310><c> to</c><00:01:59.369><c> take</c><00:01:59.610><c> the</c>

00:01:59.690 --> 00:01:59.700 align:start position:0%
B is those are always going to take the
 

00:01:59.700 --> 00:02:01.280 align:start position:0%
B is those are always going to take the
same<00:01:59.850><c> amount</c><00:01:59.909><c> of</c><00:02:00.030><c> time</c><00:02:00.210><c> to</c><00:02:00.299><c> perform</c><00:02:00.750><c> so</c>

00:02:01.280 --> 00:02:01.290 align:start position:0%
same amount of time to perform so
 

00:02:01.290 --> 00:02:02.749 align:start position:0%
same amount of time to perform so
there's<00:02:01.469><c> a</c><00:02:01.530><c> constant</c><00:02:01.890><c> time</c><00:02:02.159><c> I</c><00:02:02.460><c> mean</c><00:02:02.610><c> they</c>

00:02:02.749 --> 00:02:02.759 align:start position:0%
there's a constant time I mean they
 

00:02:02.759 --> 00:02:04.219 align:start position:0%
there's a constant time I mean they
don't<00:02:02.880><c> change</c><00:02:03.210><c> what</c><00:02:03.420><c> the</c><00:02:03.540><c> size</c><00:02:03.750><c> of</c><00:02:03.810><c> the</c><00:02:03.869><c> input</c>

00:02:04.219 --> 00:02:04.229 align:start position:0%
don't change what the size of the input
 

00:02:04.229 --> 00:02:05.889 align:start position:0%
don't change what the size of the input
they<00:02:04.409><c> stay</c><00:02:04.649><c> the</c><00:02:04.829><c> same</c>

00:02:05.889 --> 00:02:05.899 align:start position:0%
they stay the same
 

00:02:05.899 --> 00:02:11.330 align:start position:0%
they stay the same
okay<00:02:06.899><c> so</c><00:02:07.350><c> checking</c><00:02:08.220><c> for</c><00:02:08.520><c> if</c><00:02:08.989><c> array</c><00:02:09.989><c> a</c><00:02:10.020><c> of</c><00:02:10.340><c> index</c>

00:02:11.330 --> 00:02:11.340 align:start position:0%
okay so checking for if array a of index
 

00:02:11.340 --> 00:02:13.370 align:start position:0%
okay so checking for if array a of index
at<00:02:11.550><c> index</c><00:02:11.790><c> is</c><00:02:12.000><c> less</c><00:02:12.450><c> than</c>

00:02:13.370 --> 00:02:13.380 align:start position:0%
at index is less than
 

00:02:13.380 --> 00:02:16.460 align:start position:0%
at index is less than
Rabia<00:02:13.830><c> index</c><00:02:14.280><c> j</c><00:02:14.490><c> give</c><00:02:15.480><c> me</c><00:02:15.540><c> the</c><00:02:15.630><c> same</c><00:02:15.840><c> no</c><00:02:16.320><c> matter</c>

00:02:16.460 --> 00:02:16.470 align:start position:0%
Rabia index j give me the same no matter
 

00:02:16.470 --> 00:02:17.540 align:start position:0%
Rabia index j give me the same no matter
what<00:02:16.650><c> the</c><00:02:16.740><c> size</c><00:02:16.860><c> of</c><00:02:17.070><c> those</c><00:02:17.130><c> arrays</c><00:02:17.370><c> are</c>

00:02:17.540 --> 00:02:17.550 align:start position:0%
what the size of those arrays are
 

00:02:17.550 --> 00:02:20.030 align:start position:0%
what the size of those arrays are
alright<00:02:18.030><c> just</c><00:02:18.480><c> gonna</c><00:02:18.690><c> do</c><00:02:18.780><c> it</c><00:02:18.930><c> some</c><00:02:19.530><c> amount</c><00:02:19.830><c> of</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
alright just gonna do it some amount of
 

00:02:20.040 --> 00:02:21.980 align:start position:0%
alright just gonna do it some amount of
times<00:02:20.250><c> but</c><00:02:20.940><c> that's</c><00:02:21.390><c> not</c><00:02:21.570><c> what</c><00:02:21.690><c> worried</c><00:02:21.870><c> about</c>

00:02:21.980 --> 00:02:21.990 align:start position:0%
times but that's not what worried about
 

00:02:21.990 --> 00:02:23.840 align:start position:0%
times but that's not what worried about
worried<00:02:22.230><c> about</c><00:02:22.380><c> what's</c><00:02:23.160><c> the</c><00:02:23.220><c> time</c><00:02:23.400><c> to</c><00:02:23.550><c> perform</c>

00:02:23.840 --> 00:02:23.850 align:start position:0%
worried about what's the time to perform
 

00:02:23.850 --> 00:02:25.280 align:start position:0%
worried about what's the time to perform
the<00:02:24.060><c> algorithm</c><00:02:24.450><c> based</c><00:02:24.720><c> on</c><00:02:24.870><c> the</c><00:02:24.960><c> size</c><00:02:25.140><c> of</c><00:02:25.170><c> the</c>

00:02:25.280 --> 00:02:25.290 align:start position:0%
the algorithm based on the size of the
 

00:02:25.290 --> 00:02:25.580 align:start position:0%
the algorithm based on the size of the
input

00:02:25.580 --> 00:02:25.590 align:start position:0%
input
 

00:02:25.590 --> 00:02:28.040 align:start position:0%
input
that's<00:02:25.830><c> what</c><00:02:26.040><c> bigger</c><00:02:26.340><c> list</c><00:02:26.780><c> and</c><00:02:27.780><c> instead</c><00:02:28.020><c> a</c>

00:02:28.040 --> 00:02:28.050 align:start position:0%
that's what bigger list and instead a
 

00:02:28.050 --> 00:02:29.330 align:start position:0%
that's what bigger list and instead a
seat<00:02:28.320><c> here</c><00:02:28.500><c> we're</c><00:02:28.620><c> gonna</c><00:02:28.710><c> print</c><00:02:28.950><c> out</c><00:02:29.070><c> it's</c>

00:02:29.330 --> 00:02:29.340 align:start position:0%
seat here we're gonna print out it's
 

00:02:29.340 --> 00:02:31.130 align:start position:0%
seat here we're gonna print out it's
gonna<00:02:29.490><c> take</c><00:02:29.640><c> the</c><00:02:29.730><c> same</c><00:02:29.850><c> amount</c><00:02:30.000><c> of</c><00:02:30.030><c> time</c><00:02:30.240><c> so</c>

00:02:31.130 --> 00:02:31.140 align:start position:0%
gonna take the same amount of time so
 

00:02:31.140 --> 00:02:33.860 align:start position:0%
gonna take the same amount of time so
this<00:02:31.680><c> already</c><00:02:32.010><c> just</c><00:02:32.340><c> if</c><00:02:32.670><c> statement</c><00:02:33.480><c> and</c><00:02:33.660><c> the</c>

00:02:33.860 --> 00:02:33.870 align:start position:0%
this already just if statement and the
 

00:02:33.870 --> 00:02:35.780 align:start position:0%
this already just if statement and the
printout<00:02:34.260><c> we</c><00:02:34.740><c> don't</c><00:02:34.890><c> even</c><00:02:35.040><c> care</c><00:02:35.160><c> about</c><00:02:35.370><c> so</c><00:02:35.670><c> we</c>

00:02:35.780 --> 00:02:35.790 align:start position:0%
printout we don't even care about so we
 

00:02:35.790 --> 00:02:37.310 align:start position:0%
printout we don't even care about so we
can<00:02:35.880><c> eliminate</c><00:02:35.970><c> those</c><00:02:36.300><c> and</c><00:02:36.660><c> now</c><00:02:37.110><c> I</c><00:02:37.140><c> want</c><00:02:37.290><c> to</c>

00:02:37.310 --> 00:02:37.320 align:start position:0%
can eliminate those and now I want to
 

00:02:37.320 --> 00:02:40.490 align:start position:0%
can eliminate those and now I want to
focus<00:02:37.500><c> on</c><00:02:37.770><c> are</c><00:02:38.010><c> these</c><00:02:38.100><c> two</c><00:02:38.310><c> loops</c><00:02:38.580><c> right</c><00:02:39.500><c> now</c>

00:02:40.490 --> 00:02:40.500 align:start position:0%
focus on are these two loops right now
 

00:02:40.500 --> 00:02:43.190 align:start position:0%
focus on are these two loops right now
look<00:02:41.430><c> at</c><00:02:41.550><c> the</c><00:02:41.670><c> other</c><00:02:41.820><c> loop</c><00:02:42.060><c> like</c><00:02:42.720><c> before</c><00:02:43.050><c> if</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
look at the other loop like before if
 

00:02:43.200 --> 00:02:46.160 align:start position:0%
look at the other loop like before if
it's<00:02:43.410><c> going</c><00:02:43.650><c> it's</c><00:02:44.040><c> going</c><00:02:44.070><c> through</c><00:02:44.900><c> up</c><00:02:45.900><c> to</c><00:02:46.050><c> the</c>

00:02:46.160 --> 00:02:46.170 align:start position:0%
it's going it's going through up to the
 

00:02:46.170 --> 00:02:48.050 align:start position:0%
it's going it's going through up to the
length<00:02:46.380><c> of</c><00:02:46.500><c> the</c><00:02:46.620><c> array</c><00:02:46.830><c> goes</c><00:02:47.490><c> from</c><00:02:47.670><c> zero</c><00:02:47.970><c> to</c>

00:02:48.050 --> 00:02:48.060 align:start position:0%
length of the array goes from zero to
 

00:02:48.060 --> 00:02:49.400 align:start position:0%
length of the array goes from zero to
lace<00:02:48.240><c> and</c><00:02:48.390><c> read</c><00:02:48.540><c> meaning</c><00:02:48.780><c> it's</c><00:02:48.960><c> going</c><00:02:48.990><c> through</c>

00:02:49.400 --> 00:02:49.410 align:start position:0%
lace and read meaning it's going through
 

00:02:49.410 --> 00:02:51.710 align:start position:0%
lace and read meaning it's going through
this<00:02:49.530><c> whole</c><00:02:50.040><c> size</c><00:02:50.280><c> of</c><00:02:50.400><c> the</c><00:02:50.490><c> array</c><00:02:50.640><c> and</c><00:02:50.910><c> so</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
this whole size of the array and so
 

00:02:51.720 --> 00:02:56.480 align:start position:0%
this whole size of the array and so
that's<00:02:51.840><c> going</c><00:02:51.960><c> to</c><00:02:52.050><c> be</c><00:02:52.110><c> n</c><00:02:52.940><c> because</c><00:02:54.290><c> if</c><00:02:55.490><c> here</c>

00:02:56.480 --> 00:02:56.490 align:start position:0%
that's going to be n because if here
 

00:02:56.490 --> 00:03:00.110 align:start position:0%
that's going to be n because if here
it's<00:02:56.640><c> ten</c><00:02:57.120><c> if</c><00:02:57.420><c> array</c><00:02:58.080><c> length</c><00:02:58.410><c> is</c><00:02:58.650><c> ten</c><00:02:59.250><c> you</c><00:03:00.030><c> know</c>

00:03:00.110 --> 00:03:00.120 align:start position:0%
it's ten if array length is ten you know
 

00:03:00.120 --> 00:03:02.000 align:start position:0%
it's ten if array length is ten you know
we're<00:03:00.330><c> going</c><00:03:00.420><c> to</c><00:03:00.480><c> go</c><00:03:00.720><c> to</c><00:03:00.870><c> ten</c><00:03:01.020><c> elements</c><00:03:01.200><c> if</c>

00:03:02.000 --> 00:03:02.010 align:start position:0%
we're going to go to ten elements if
 

00:03:02.010 --> 00:03:03.740 align:start position:0%
we're going to go to ten elements if
it's<00:03:02.550><c> a</c><00:03:02.610><c> hundred</c><00:03:02.940><c> we're</c><00:03:03.150><c> gonna</c><00:03:03.240><c> go</c><00:03:03.360><c> hundred</c>

00:03:03.740 --> 00:03:03.750 align:start position:0%
it's a hundred we're gonna go hundred
 

00:03:03.750 --> 00:03:06.830 align:start position:0%
it's a hundred we're gonna go hundred
elements<00:03:03.900><c> so</c><00:03:04.290><c> it's</c><00:03:04.770><c> proportional</c><00:03:05.330><c> the</c><00:03:06.330><c> time</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
elements so it's proportional the time
 

00:03:06.840 --> 00:03:08.420 align:start position:0%
elements so it's proportional the time
it<00:03:07.020><c> takes</c><00:03:07.200><c> to</c><00:03:07.350><c> goes</c><00:03:07.650><c> through</c><00:03:07.890><c> the</c><00:03:07.980><c> for</c><00:03:08.130><c> loop</c><00:03:08.280><c> is</c>

00:03:08.420 --> 00:03:08.430 align:start position:0%
it takes to goes through the for loop is
 

00:03:08.430 --> 00:03:11.810 align:start position:0%
it takes to goes through the for loop is
proportional<00:03:09.210><c> to</c><00:03:09.980><c> the</c><00:03:10.980><c> size</c><00:03:11.400><c> of</c><00:03:11.610><c> the</c><00:03:11.670><c> input</c>

00:03:11.810 --> 00:03:11.820 align:start position:0%
proportional to the size of the input
 

00:03:11.820 --> 00:03:15.620 align:start position:0%
proportional to the size of the input
which<00:03:12.360><c> is</c><00:03:12.420><c> array</c><00:03:12.810><c> okay</c><00:03:13.470><c> and</c><00:03:14.330><c> if</c><00:03:15.330><c> we</c><00:03:15.390><c> look</c><00:03:15.540><c> at</c>

00:03:15.620 --> 00:03:15.630 align:start position:0%
which is array okay and if we look at
 

00:03:15.630 --> 00:03:17.600 align:start position:0%
which is array okay and if we look at
this<00:03:15.720><c> the</c><00:03:16.050><c> inner</c><00:03:16.230><c> for</c><00:03:16.500><c> loop</c><00:03:16.530><c> it's</c><00:03:17.220><c> kind</c><00:03:17.490><c> of</c><00:03:17.520><c> the</c>

00:03:17.600 --> 00:03:17.610 align:start position:0%
this the inner for loop it's kind of the
 

00:03:17.610 --> 00:03:19.550 align:start position:0%
this the inner for loop it's kind of the
same<00:03:17.760><c> thing</c><00:03:18.030><c> but</c><00:03:18.300><c> there's</c><00:03:18.810><c> a</c><00:03:18.900><c> difference</c><00:03:19.320><c> and</c>

00:03:19.550 --> 00:03:19.560 align:start position:0%
same thing but there's a difference and
 

00:03:19.560 --> 00:03:20.870 align:start position:0%
same thing but there's a difference and
like<00:03:19.710><c> I</c><00:03:19.830><c> said</c><00:03:19.890><c> there's</c><00:03:20.250><c> two</c><00:03:20.520><c> different</c><00:03:20.790><c> inputs</c>

00:03:20.870 --> 00:03:20.880 align:start position:0%
like I said there's two different inputs
 

00:03:20.880 --> 00:03:24.830 align:start position:0%
like I said there's two different inputs
here<00:03:21.300><c> and</c><00:03:21.510><c> so</c><00:03:22.410><c> this</c><00:03:22.950><c> is</c><00:03:23.130><c> going</c><00:03:23.340><c> from</c><00:03:23.600><c> zero</c><00:03:24.600><c> all</c>

00:03:24.830 --> 00:03:24.840 align:start position:0%
here and so this is going from zero all
 

00:03:24.840 --> 00:03:27.500 align:start position:0%
here and so this is going from zero all
the<00:03:24.870><c> way</c><00:03:24.960><c> to</c><00:03:25.050><c> the</c><00:03:25.290><c> second</c><00:03:25.770><c> arrays</c><00:03:26.010><c> length</c><00:03:26.510><c> now</c>

00:03:27.500 --> 00:03:27.510 align:start position:0%
the way to the second arrays length now
 

00:03:27.510 --> 00:03:30.290 align:start position:0%
the way to the second arrays length now
we<00:03:27.780><c> can't</c><00:03:28.050><c> denote</c><00:03:28.410><c> this</c><00:03:28.560><c> as</c><00:03:28.620><c> n</c><00:03:29.270><c> because</c><00:03:30.270><c> that</c>

00:03:30.290 --> 00:03:30.300 align:start position:0%
we can't denote this as n because that
 

00:03:30.300 --> 00:03:33.350 align:start position:0%
we can't denote this as n because that
would<00:03:30.690><c> mean</c><00:03:30.870><c> if</c><00:03:31.680><c> it</c><00:03:31.860><c> was</c><00:03:32.040><c> J</c><00:03:32.250><c> equals</c><00:03:32.520><c> zero</c><00:03:32.730><c> J</c>

00:03:33.350 --> 00:03:33.360 align:start position:0%
would mean if it was J equals zero J
 

00:03:33.360 --> 00:03:36.200 align:start position:0%
would mean if it was J equals zero J
less<00:03:33.600><c> than</c><00:03:33.630><c> array</c><00:03:33.930><c> any</c><00:03:34.260><c> of</c><00:03:34.500><c> that</c><00:03:34.740><c> length</c><00:03:35.210><c> we</c>

00:03:36.200 --> 00:03:36.210 align:start position:0%
less than array any of that length we
 

00:03:36.210 --> 00:03:38.210 align:start position:0%
less than array any of that length we
could<00:03:36.330><c> also</c><00:03:36.480><c> say</c><00:03:36.660><c> this</c><00:03:36.780><c> was</c><00:03:36.960><c> and</c><00:03:37.170><c> right</c><00:03:37.590><c> but</c><00:03:38.130><c> we</c>

00:03:38.210 --> 00:03:38.220 align:start position:0%
could also say this was and right but we
 

00:03:38.220 --> 00:03:41.030 align:start position:0%
could also say this was and right but we
can't<00:03:38.580><c> because</c><00:03:38.730><c> the</c><00:03:38.970><c> different</c><00:03:39.330><c> array</c><00:03:40.040><c> so</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
can't because the different array so
 

00:03:41.040 --> 00:03:42.440 align:start position:0%
can't because the different array so
what<00:03:41.220><c> you</c><00:03:41.550><c> would</c><00:03:41.670><c> doing</c><00:03:41.970><c> is</c><00:03:42.060><c> he's</c><00:03:42.209><c> kind</c><00:03:42.360><c> of</c>

00:03:42.440 --> 00:03:42.450 align:start position:0%
what you would doing is he's kind of
 

00:03:42.450 --> 00:03:44.240 align:start position:0%
what you would doing is he's kind of
like<00:03:42.540><c> the</c><00:03:42.660><c> next</c><00:03:42.780><c> letter</c><00:03:43.020><c> in</c><00:03:43.350><c> the</c><00:03:43.470><c> alphabet</c><00:03:43.560><c> so</c>

00:03:44.240 --> 00:03:44.250 align:start position:0%
like the next letter in the alphabet so
 

00:03:44.250 --> 00:03:47.660 align:start position:0%
like the next letter in the alphabet so
we<00:03:44.370><c> just</c><00:03:44.550><c> call</c><00:03:44.790><c> this</c><00:03:44.910><c> one</c><00:03:45.320><c> big</c><00:03:46.320><c> oaf</c><00:03:46.500><c> M</c><00:03:46.740><c> because</c>

00:03:47.660 --> 00:03:47.670 align:start position:0%
we just call this one big oaf M because
 

00:03:47.670 --> 00:03:49.220 align:start position:0%
we just call this one big oaf M because
we<00:03:47.730><c> have</c><00:03:47.850><c> to</c><00:03:47.910><c> go</c><00:03:48.030><c> through</c><00:03:48.270><c> the</c><00:03:48.780><c> lengths</c><00:03:49.020><c> of</c><00:03:49.110><c> the</c>

00:03:49.220 --> 00:03:49.230 align:start position:0%
we have to go through the lengths of the
 

00:03:49.230 --> 00:03:55.250 align:start position:0%
we have to go through the lengths of the
array<00:03:49.440><c> okay</c><00:03:50.360><c> now</c><00:03:52.400><c> is</c><00:03:53.400><c> what</c><00:03:53.700><c> does</c><00:03:53.880><c> this</c><00:03:54.000><c> mean</c><00:03:54.300><c> in</c>

00:03:55.250 --> 00:03:55.260 align:start position:0%
array okay now is what does this mean in
 

00:03:55.260 --> 00:03:57.340 align:start position:0%
array okay now is what does this mean in
the<00:03:55.530><c> second</c><00:03:55.920><c> example</c><00:03:56.100><c> we</c><00:03:56.340><c> did</c><00:03:56.520><c> in</c><00:03:56.640><c> Waris</c><00:03:56.820><c> video</c>

00:03:57.340 --> 00:03:57.350 align:start position:0%
the second example we did in Waris video
 

00:03:57.350 --> 00:04:00.949 align:start position:0%
the second example we did in Waris video
we<00:03:58.350><c> did</c><00:03:58.560><c> when</c><00:03:59.310><c> there</c><00:03:59.430><c> was</c><00:03:59.520><c> a</c><00:03:59.580><c> nested</c><00:04:00.209><c> loop</c><00:04:00.360><c> this</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
we did when there was a nested loop this
 

00:04:00.959 --> 00:04:03.980 align:start position:0%
we did when there was a nested loop this
was<00:04:01.140><c> it</c><00:04:01.320><c> was</c><00:04:01.440><c> N</c><00:04:01.620><c> squared</c><00:04:02.270><c> that's</c><00:04:03.270><c> not</c><00:04:03.630><c> the</c><00:04:03.840><c> case</c>

00:04:03.980 --> 00:04:03.990 align:start position:0%
was it was N squared that's not the case
 

00:04:03.990 --> 00:04:05.840 align:start position:0%
was it was N squared that's not the case
here<00:04:04.200><c> and</c><00:04:04.500><c> squared</c><00:04:04.800><c> is</c><00:04:04.890><c> not</c><00:04:05.160><c> the</c><00:04:05.370><c> answer</c><00:04:05.490><c> for</c>

00:04:05.840 --> 00:04:05.850 align:start position:0%
here and squared is not the answer for
 

00:04:05.850 --> 00:04:07.760 align:start position:0%
here and squared is not the answer for
this<00:04:06.030><c> algorithm</c><00:04:06.840><c> it's</c><00:04:07.230><c> not</c><00:04:07.410><c> the</c><00:04:07.500><c> Big</c><00:04:07.650><c> O</c>

00:04:07.760 --> 00:04:07.770 align:start position:0%
this algorithm it's not the Big O
 

00:04:07.770 --> 00:04:10.880 align:start position:0%
this algorithm it's not the Big O
complexity<00:04:08.370><c> the</c><00:04:09.090><c> reason</c><00:04:09.570><c> is</c><00:04:09.780><c> if</c><00:04:10.230><c> we</c><00:04:10.530><c> increase</c>

00:04:10.880 --> 00:04:10.890 align:start position:0%
complexity the reason is if we increase
 

00:04:10.890 --> 00:04:14.150 align:start position:0%
complexity the reason is if we increase
the<00:04:11.160><c> length</c><00:04:11.370><c> of</c><00:04:11.640><c> array</c><00:04:12.270><c> a</c><00:04:12.560><c> array</c><00:04:13.560><c> B</c><00:04:13.740><c> might</c><00:04:13.950><c> stay</c>

00:04:14.150 --> 00:04:14.160 align:start position:0%
the length of array a array B might stay
 

00:04:14.160 --> 00:04:16.340 align:start position:0%
the length of array a array B might stay
the<00:04:14.280><c> same</c><00:04:14.490><c> we</c><00:04:15.060><c> don't</c><00:04:15.270><c> know</c><00:04:15.600><c> because</c><00:04:15.959><c> these</c><00:04:16.200><c> are</c>

00:04:16.340 --> 00:04:16.350 align:start position:0%
the same we don't know because these are
 

00:04:16.350 --> 00:04:19.010 align:start position:0%
the same we don't know because these are
two<00:04:16.620><c> different</c><00:04:16.799><c> inputs</c><00:04:17.450><c> we</c><00:04:18.450><c> can't</c><00:04:18.780><c> say</c><00:04:18.989><c> for</c>

00:04:19.010 --> 00:04:19.020 align:start position:0%
two different inputs we can't say for
 

00:04:19.020 --> 00:04:21.800 align:start position:0%
two different inputs we can't say for
certain<00:04:19.260><c> that</c><00:04:19.890><c> they're</c><00:04:20.160><c> going</c><00:04:20.340><c> on</c><00:04:20.810><c> that</c>

00:04:21.800 --> 00:04:21.810 align:start position:0%
certain that they're going on that
 

00:04:21.810 --> 00:04:24.650 align:start position:0%
certain that they're going on that
they're<00:04:21.960><c> going</c><00:04:22.109><c> to</c><00:04:22.169><c> increase</c><00:04:23.450><c> together</c><00:04:24.450><c> or</c>

00:04:24.650 --> 00:04:24.660 align:start position:0%
they're going to increase together or
 

00:04:24.660 --> 00:04:26.370 align:start position:0%
they're going to increase together or
not<00:04:24.810><c> they're</c><00:04:25.590><c> two</c><00:04:25.770><c> different</c><00:04:25.890><c> post</c>

00:04:26.370 --> 00:04:26.380 align:start position:0%
not they're two different post
 

00:04:26.380 --> 00:04:30.090 align:start position:0%
not they're two different post
assume<00:04:26.680><c> that</c><00:04:26.830><c> they</c><00:04:26.920><c> don't</c><00:04:27.190><c> right</c><00:04:27.780><c> and</c><00:04:29.100><c> what</c>

00:04:30.090 --> 00:04:30.100 align:start position:0%
assume that they don't right and what
 

00:04:30.100 --> 00:04:32.490 align:start position:0%
assume that they don't right and what
what<00:04:30.430><c> that's</c><00:04:30.550><c> telling</c><00:04:30.820><c> us</c><00:04:31.170><c> what</c><00:04:32.170><c> this</c><00:04:32.260><c> really</c>

00:04:32.490 --> 00:04:32.500 align:start position:0%
what that's telling us what this really
 

00:04:32.500 --> 00:04:35.100 align:start position:0%
what that's telling us what this really
telling<00:04:32.770><c> us</c><00:04:32.890><c> is</c><00:04:33.070><c> all</c><00:04:33.430><c> we</c><00:04:33.640><c> can</c><00:04:33.790><c> say</c><00:04:34.030><c> is</c><00:04:34.330><c> this</c><00:04:34.930><c> is</c>

00:04:35.100 --> 00:04:35.110 align:start position:0%
telling us is all we can say is this is
 

00:04:35.110 --> 00:04:42.270 align:start position:0%
telling us is all we can say is this is
Big<00:04:35.410><c> O</c><00:04:35.680><c> of</c><00:04:36.070><c> n</c><00:04:38.070><c> times</c><00:04:39.240><c> mm</c><00:04:40.680><c> all</c><00:04:41.680><c> right</c><00:04:41.740><c> it's</c><00:04:41.980><c> not</c><00:04:42.100><c> N</c>

00:04:42.270 --> 00:04:42.280 align:start position:0%
Big O of n times mm all right it's not N
 

00:04:42.280 --> 00:04:45.600 align:start position:0%
Big O of n times mm all right it's not N
squared<00:04:42.580><c> just</c><00:04:43.210><c> n</c><00:04:43.360><c> times</c><00:04:43.600><c> m</c><00:04:43.840><c> and</c><00:04:44.190><c> that's</c><00:04:45.190><c> the</c>

00:04:45.600 --> 00:04:45.610 align:start position:0%
squared just n times m and that's the
 

00:04:45.610 --> 00:04:48.960 align:start position:0%
squared just n times m and that's the
answer<00:04:45.760><c> because</c><00:04:46.420><c> we</c><00:04:47.230><c> can't</c><00:04:47.560><c> determine</c><00:04:47.970><c> enter</c>

00:04:48.960 --> 00:04:48.970 align:start position:0%
answer because we can't determine enter
 

00:04:48.970 --> 00:04:52.080 align:start position:0%
answer because we can't determine enter
em<00:04:49.440><c> if</c><00:04:50.440><c> array</c><00:04:50.920><c> a</c><00:04:50.950><c> or</c><00:04:51.250><c> a</c><00:04:51.280><c> B</c><00:04:51.580><c> if</c><00:04:51.880><c> they're</c>

00:04:52.080 --> 00:04:52.090 align:start position:0%
em if array a or a B if they're
 

00:04:52.090 --> 00:04:53.640 align:start position:0%
em if array a or a B if they're
increasing<00:04:52.720><c> with</c><00:04:52.930><c> the</c><00:04:52.960><c> other</c><00:04:53.170><c> array</c><00:04:53.380><c> or</c><00:04:53.530><c> not</c>

00:04:53.640 --> 00:04:53.650 align:start position:0%
increasing with the other array or not
 

00:04:53.650 --> 00:04:57.360 align:start position:0%
increasing with the other array or not
okay<00:04:54.520><c> there</c><00:04:54.760><c> are</c><00:04:54.790><c> two</c><00:04:54.910><c> distinct</c><00:04:55.870><c> inputs</c><00:04:56.860><c> so</c><00:04:57.250><c> we</c>

00:04:57.360 --> 00:04:57.370 align:start position:0%
okay there are two distinct inputs so we
 

00:04:57.370 --> 00:04:58.770 align:start position:0%
okay there are two distinct inputs so we
have<00:04:57.460><c> to</c><00:04:57.550><c> leave</c><00:04:57.730><c> them</c><00:04:57.820><c> in</c><00:04:57.910><c> that</c><00:04:57.970><c> okay</c><00:04:58.720><c> so</c>

00:04:58.770 --> 00:04:58.780 align:start position:0%
have to leave them in that okay so
 

00:04:58.780 --> 00:05:00.120 align:start position:0%
have to leave them in that okay so
something<00:04:59.200><c> you</c><00:04:59.440><c> also</c><00:04:59.590><c> have</c><00:04:59.710><c> to</c><00:04:59.770><c> make</c><00:04:59.920><c> sure</c><00:04:59.950><c> of</c>

00:05:00.120 --> 00:05:00.130 align:start position:0%
something you also have to make sure of
 

00:05:00.130 --> 00:05:02.550 align:start position:0%
something you also have to make sure of
when<00:05:00.910><c> you're</c><00:05:01.030><c> looking</c><00:05:01.270><c> at</c><00:05:01.420><c> these</c><00:05:01.570><c> I'm</c><00:05:02.350><c> in</c><00:05:02.470><c> an</c>

00:05:02.550 --> 00:05:02.560 align:start position:0%
when you're looking at these I'm in an
 

00:05:02.560 --> 00:05:04.350 align:start position:0%
when you're looking at these I'm in an
interview<00:05:02.680><c> question</c><00:05:03.190><c> look</c><00:05:03.820><c> at</c><00:05:03.910><c> the</c><00:05:04.030><c> inputs</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
interview question look at the inputs
 

00:05:04.360 --> 00:05:06.720 align:start position:0%
interview question look at the inputs
are<00:05:04.630><c> they</c><00:05:04.840><c> distinct</c><00:05:05.350><c> names</c><00:05:06.070><c> they're</c><00:05:06.220><c> distinct</c>

00:05:06.720 --> 00:05:06.730 align:start position:0%
are they distinct names they're distinct
 

00:05:06.730 --> 00:05:08.240 align:start position:0%
are they distinct names they're distinct
they<00:05:06.970><c> should</c><00:05:07.150><c> not</c><00:05:07.270><c> try</c><00:05:07.390><c> to</c><00:05:07.420><c> trick</c><00:05:07.690><c> you</c><00:05:07.870><c> and</c>

00:05:08.240 --> 00:05:08.250 align:start position:0%
they should not try to trick you and
 

00:05:08.250 --> 00:05:11.790 align:start position:0%
they should not try to trick you and
maybe<00:05:09.250><c> the</c><00:05:09.340><c> reason</c><00:05:09.520><c> one</c><00:05:09.820><c> of</c><00:05:09.850><c> the</c><00:05:10.000><c> arrays</c><00:05:10.350><c> in</c><00:05:11.350><c> in</c>

00:05:11.790 --> 00:05:11.800 align:start position:0%
maybe the reason one of the arrays in in
 

00:05:11.800 --> 00:05:13.920 align:start position:0%
maybe the reason one of the arrays in in
the<00:05:12.280><c> actual</c><00:05:12.550><c> like</c><00:05:13.120><c> in</c><00:05:13.360><c> the</c><00:05:13.420><c> piece</c><00:05:13.630><c> of</c><00:05:13.750><c> code</c>

00:05:13.920 --> 00:05:13.930 align:start position:0%
the actual like in the piece of code
 

00:05:13.930 --> 00:05:15.750 align:start position:0%
the actual like in the piece of code
right<00:05:14.170><c> make</c><00:05:14.800><c> sure</c><00:05:14.920><c> you're</c><00:05:15.010><c> using</c><00:05:15.100><c> both</c><00:05:15.340><c> up</c><00:05:15.550><c> and</c>

00:05:15.750 --> 00:05:15.760 align:start position:0%
right make sure you're using both up and
 

00:05:15.760 --> 00:05:18.750 align:start position:0%
right make sure you're using both up and
if<00:05:16.030><c> this</c><00:05:16.600><c> was</c><00:05:16.840><c> a</c><00:05:17.140><c> notes</c><00:05:17.950><c> these</c><00:05:18.130><c> flips</c><00:05:18.490><c> reason</c>

00:05:18.750 --> 00:05:18.760 align:start position:0%
if this was a notes these flips reason
 

00:05:18.760 --> 00:05:22.050 align:start position:0%
if this was a notes these flips reason
RNA<00:05:19.090><c> that</c><00:05:19.360><c> links</c><00:05:19.630><c> negative</c><00:05:20.500><c> N</c><00:05:20.620><c> squared</c><00:05:21.060><c> over</c>

00:05:22.050 --> 00:05:22.060 align:start position:0%
RNA that links negative N squared over
 

00:05:22.060 --> 00:05:24.480 align:start position:0%
RNA that links negative N squared over
the<00:05:22.090><c> answer</c><00:05:22.390><c> but</c><00:05:22.690><c> it's</c><00:05:23.230><c> not</c><00:05:23.350><c> so</c><00:05:23.770><c> that's</c><00:05:24.400><c> the</c>

00:05:24.480 --> 00:05:24.490 align:start position:0%
the answer but it's not so that's the
 

00:05:24.490 --> 00:05:24.690 align:start position:0%
the answer but it's not so that's the
answer

00:05:24.690 --> 00:05:24.700 align:start position:0%
answer
 

00:05:24.700 --> 00:05:27.620 align:start position:0%
answer
okay<00:05:25.110><c> all</c><00:05:26.110><c> right</c><00:05:26.350><c> on</c><00:05:26.650><c> to</c><00:05:26.860><c> the</c><00:05:26.950><c> next</c><00:05:27.100><c> one</c><00:05:27.250><c> here</c>

00:05:27.620 --> 00:05:27.630 align:start position:0%
okay all right on to the next one here
 

00:05:27.630 --> 00:05:30.510 align:start position:0%
okay all right on to the next one here
as<00:05:28.630><c> you</c><00:05:28.780><c> can</c><00:05:28.810><c> see</c><00:05:28.960><c> this</c><00:05:29.440><c> is</c><00:05:29.620><c> gonna</c><00:05:29.770><c> look</c><00:05:29.920><c> fairly</c>

00:05:30.510 --> 00:05:30.520 align:start position:0%
as you can see this is gonna look fairly
 

00:05:30.520 --> 00:05:33.150 align:start position:0%
as you can see this is gonna look fairly
similar<00:05:30.670><c> with</c><00:05:31.630><c> one</c><00:05:31.930><c> difference</c><00:05:32.500><c> and</c><00:05:32.740><c> that</c><00:05:32.770><c> is</c>

00:05:33.150 --> 00:05:33.160 align:start position:0%
similar with one difference and that is
 

00:05:33.160 --> 00:05:35.100 align:start position:0%
similar with one difference and that is
that<00:05:33.340><c> we</c><00:05:33.580><c> have</c><00:05:33.670><c> a</c><00:05:33.700><c> third</c><00:05:34.210><c> for</c><00:05:34.570><c> loop</c><00:05:34.720><c> this</c><00:05:34.900><c> time</c>

00:05:35.100 --> 00:05:35.110 align:start position:0%
that we have a third for loop this time
 

00:05:35.110 --> 00:05:40.350 align:start position:0%
that we have a third for loop this time
okay<00:05:36.660><c> so</c><00:05:38.670><c> again</c><00:05:39.670><c> just</c><00:05:39.880><c> make</c><00:05:40.210><c> sure</c><00:05:40.240><c> you</c>

00:05:40.350 --> 00:05:40.360 align:start position:0%
okay so again just make sure you
 

00:05:40.360 --> 00:05:41.700 align:start position:0%
okay so again just make sure you
understand<00:05:40.600><c> peace</c><00:05:40.840><c> understand</c><00:05:41.350><c> the</c><00:05:41.440><c> code</c><00:05:41.560><c> and</c>

00:05:41.700 --> 00:05:41.710 align:start position:0%
understand peace understand the code and
 

00:05:41.710 --> 00:05:44.220 align:start position:0%
understand peace understand the code and
this<00:05:41.800><c> is</c><00:05:41.950><c> prim</c><00:05:42.310><c> same</c><00:05:42.580><c> as</c><00:05:42.670><c> the</c><00:05:42.730><c> last</c><00:05:42.910><c> one</c><00:05:43.230><c> so</c>

00:05:44.220 --> 00:05:44.230 align:start position:0%
this is prim same as the last one so
 

00:05:44.230 --> 00:05:46.140 align:start position:0%
this is prim same as the last one so
quickly<00:05:44.710><c> go</c><00:05:44.830><c> through</c><00:05:44.860><c> it</c><00:05:45.100><c> real</c><00:05:45.310><c> quick</c><00:05:45.490><c> we</c><00:05:46.060><c> have</c>

00:05:46.140 --> 00:05:46.150 align:start position:0%
quickly go through it real quick we have
 

00:05:46.150 --> 00:05:47.460 align:start position:0%
quickly go through it real quick we have
two<00:05:46.300><c> different</c><00:05:46.540><c> inputs</c><00:05:46.630><c> right</c><00:05:47.230><c> we</c><00:05:47.350><c> have</c>

00:05:47.460 --> 00:05:47.470 align:start position:0%
two different inputs right we have
 

00:05:47.470 --> 00:05:49.560 align:start position:0%
two different inputs right we have
already<00:05:47.650><c> a</c><00:05:47.830><c> and</c><00:05:48.040><c> rabies</c><00:05:48.310><c> or</c><00:05:48.490><c> twin</c><00:05:48.700><c> puts</c><00:05:48.910><c> we</c>

00:05:49.560 --> 00:05:49.570 align:start position:0%
already a and rabies or twin puts we
 

00:05:49.570 --> 00:05:51.510 align:start position:0%
already a and rabies or twin puts we
have<00:05:49.750><c> three</c><00:05:50.200><c> four</c><00:05:50.530><c> loops</c><00:05:50.740><c> this</c><00:05:50.860><c> time</c><00:05:51.070><c> and</c><00:05:51.370><c> then</c>

00:05:51.510 --> 00:05:51.520 align:start position:0%
have three four loops this time and then
 

00:05:51.520 --> 00:05:54.860 align:start position:0%
have three four loops this time and then
we're<00:05:51.640><c> printing</c><00:05:52.470><c> we're</c><00:05:53.470><c> printing</c><00:05:53.710><c> some</c><00:05:54.100><c> pair</c>

00:05:54.860 --> 00:05:54.870 align:start position:0%
we're printing we're printing some pair
 

00:05:54.870 --> 00:05:58.290 align:start position:0%
we're printing we're printing some pair
okay<00:05:55.870><c> so</c><00:05:56.740><c> what</c><00:05:56.890><c> does</c><00:05:57.070><c> that</c><00:05:57.100><c> mean</c><00:05:57.310><c> first</c><00:05:57.760><c> off</c><00:05:57.940><c> we</c>

00:05:58.290 --> 00:05:58.300 align:start position:0%
okay so what does that mean first off we
 

00:05:58.300 --> 00:06:01.320 align:start position:0%
okay so what does that mean first off we
know<00:05:58.480><c> that</c><00:05:58.960><c> printing</c><00:05:59.410><c> is</c><00:05:59.980><c> Big</c><00:06:00.310><c> O</c><00:06:00.400><c> one</c><00:06:00.670><c> because</c>

00:06:01.320 --> 00:06:01.330 align:start position:0%
know that printing is Big O one because
 

00:06:01.330 --> 00:06:02.790 align:start position:0%
know that printing is Big O one because
no<00:06:01.480><c> matter</c><00:06:01.660><c> what</c><00:06:01.810><c> side</c><00:06:02.020><c> of</c><00:06:02.050><c> the</c><00:06:02.110><c> input</c><00:06:02.470><c> for</c>

00:06:02.790 --> 00:06:02.800 align:start position:0%
no matter what side of the input for
 

00:06:02.800 --> 00:06:05.670 align:start position:0%
no matter what side of the input for
anything<00:06:03.330><c> printing</c><00:06:04.330><c> is</c><00:06:04.480><c> the</c><00:06:04.780><c> same</c><00:06:04.960><c> takes</c><00:06:05.590><c> the</c>

00:06:05.670 --> 00:06:05.680 align:start position:0%
anything printing is the same takes the
 

00:06:05.680 --> 00:06:08.760 align:start position:0%
anything printing is the same takes the
same<00:06:05.830><c> time</c><00:06:06.010><c> the</c><00:06:07.320><c> taste</c><00:06:08.320><c> the</c><00:06:08.410><c> same</c><00:06:08.560><c> time</c><00:06:08.710><c> to</c>

00:06:08.760 --> 00:06:08.770 align:start position:0%
same time the taste the same time to
 

00:06:08.770 --> 00:06:12.660 align:start position:0%
same time the taste the same time to
execute<00:06:08.950><c> one</c><00:06:09.670><c> right</c><00:06:10.800><c> so</c><00:06:11.800><c> now</c><00:06:12.250><c> we</c><00:06:12.340><c> can</c><00:06:12.460><c> get</c><00:06:12.520><c> rid</c>

00:06:12.660 --> 00:06:12.670 align:start position:0%
execute one right so now we can get rid
 

00:06:12.670 --> 00:06:13.650 align:start position:0%
execute one right so now we can get rid
of<00:06:12.730><c> that</c><00:06:12.850><c> now</c><00:06:13.060><c> we</c><00:06:13.150><c> just</c><00:06:13.300><c> worry</c><00:06:13.450><c> about</c><00:06:13.570><c> these</c>

00:06:13.650 --> 00:06:13.660 align:start position:0%
of that now we just worry about these
 

00:06:13.660 --> 00:06:16.290 align:start position:0%
of that now we just worry about these
spoilers<00:06:13.990><c> well</c><00:06:14.650><c> just</c><00:06:15.070><c> like</c><00:06:15.190><c> last</c><00:06:15.340><c> time</c><00:06:15.610><c> in</c><00:06:16.210><c> the</c>

00:06:16.290 --> 00:06:16.300 align:start position:0%
spoilers well just like last time in the
 

00:06:16.300 --> 00:06:18.300 align:start position:0%
spoilers well just like last time in the
last<00:06:16.420><c> example</c><00:06:16.600><c> the</c><00:06:17.140><c> first</c><00:06:17.470><c> loop</c><00:06:18.040><c> is</c><00:06:18.220><c> going</c>

00:06:18.300 --> 00:06:18.310 align:start position:0%
last example the first loop is going
 

00:06:18.310 --> 00:06:21.030 align:start position:0%
last example the first loop is going
through<00:06:18.850><c> the</c><00:06:19.660><c> first</c><00:06:19.930><c> array</c><00:06:20.140><c> from</c><00:06:20.530><c> 0</c><00:06:20.920><c> up</c><00:06:20.950><c> to</c>

00:06:21.030 --> 00:06:21.040 align:start position:0%
through the first array from 0 up to
 

00:06:21.040 --> 00:06:21.540 align:start position:0%
through the first array from 0 up to
length

00:06:21.540 --> 00:06:21.550 align:start position:0%
length
 

00:06:21.550 --> 00:06:24.870 align:start position:0%
length
okay<00:06:22.270><c> so</c><00:06:22.360><c> that's</c><00:06:22.540><c> Big</c><00:06:22.840><c> O</c><00:06:22.960><c> and</c><00:06:23.340><c> the</c><00:06:24.340><c> second</c><00:06:24.700><c> loop</c>

00:06:24.870 --> 00:06:24.880 align:start position:0%
okay so that's Big O and the second loop
 

00:06:24.880 --> 00:06:28.410 align:start position:0%
okay so that's Big O and the second loop
is<00:06:25.150><c> going</c><00:06:25.690><c> from</c><00:06:26.730><c> the</c><00:06:27.730><c> beginning</c><00:06:28.060><c> of</c><00:06:28.150><c> the</c><00:06:28.240><c> rate</c>

00:06:28.410 --> 00:06:28.420 align:start position:0%
is going from the beginning of the rate
 

00:06:28.420 --> 00:06:31.020 align:start position:0%
is going from the beginning of the rate
to<00:06:28.660><c> the</c><00:06:28.720><c> length</c><00:06:29.050><c> of</c><00:06:29.290><c> the</c><00:06:29.590><c> second</c><00:06:29.950><c> array</c><00:06:30.190><c> so</c>

00:06:31.020 --> 00:06:31.030 align:start position:0%
to the length of the second array so
 

00:06:31.030 --> 00:06:34.980 align:start position:0%
to the length of the second array so
that's<00:06:31.150><c> Big</c><00:06:31.600><c> O</c><00:06:31.750><c> of</c><00:06:31.780><c> M</c><00:06:32.140><c> and</c><00:06:33.330><c> then</c><00:06:34.330><c> this</c><00:06:34.570><c> third</c>

00:06:34.980 --> 00:06:34.990 align:start position:0%
that's Big O of M and then this third
 

00:06:34.990 --> 00:06:35.670 align:start position:0%
that's Big O of M and then this third
loop

00:06:35.670 --> 00:06:35.680 align:start position:0%
loop
 

00:06:35.680 --> 00:06:38.070 align:start position:0%
loop
what's<00:06:36.220><c> happening</c><00:06:36.550><c> here</c><00:06:36.669><c> we're</c><00:06:37.000><c> going</c><00:06:37.390><c> from</c>

00:06:38.070 --> 00:06:38.080 align:start position:0%
what's happening here we're going from
 

00:06:38.080 --> 00:06:39.900 align:start position:0%
what's happening here we're going from
zero<00:06:38.669><c> there's</c>

00:06:39.900 --> 00:06:39.910 align:start position:0%
zero there's
 

00:06:39.910 --> 00:06:42.480 align:start position:0%
zero there's
we're<00:06:40.540><c> going</c><00:06:40.810><c> we're</c><00:06:41.500><c> performing</c><00:06:41.860><c> a</c><00:06:42.130><c> hundred</c>

00:06:42.480 --> 00:06:42.490 align:start position:0%
we're going we're performing a hundred
 

00:06:42.490 --> 00:06:44.130 align:start position:0%
we're going we're performing a hundred
thousand<00:06:42.910><c> iterations</c><00:06:43.120><c> we're</c><00:06:43.930><c> going</c><00:06:44.050><c> from</c>

00:06:44.130 --> 00:06:44.140 align:start position:0%
thousand iterations we're going from
 

00:06:44.140 --> 00:06:45.840 align:start position:0%
thousand iterations we're going from
zero<00:06:44.440><c> to</c><00:06:44.560><c> ninety</c><00:06:45.400><c> nine</c><00:06:45.490><c> thousand</c><00:06:45.790><c> nine</c>

00:06:45.840 --> 00:06:45.850 align:start position:0%
zero to ninety nine thousand nine
 

00:06:45.850 --> 00:06:51.900 align:start position:0%
zero to ninety nine thousand nine
hundred<00:06:45.910><c> ninety</c><00:06:46.300><c> nine</c><00:06:47.610><c> every</c><00:06:48.610><c> time</c><00:06:49.080><c> so</c><00:06:50.910><c> what</c>

00:06:51.900 --> 00:06:51.910 align:start position:0%
hundred ninety nine every time so what
 

00:06:51.910 --> 00:06:55.070 align:start position:0%
hundred ninety nine every time so what
is<00:06:52.000><c> it</c><00:06:52.120><c> what's</c><00:06:52.420><c> the</c><00:06:52.570><c> Big</c><00:06:52.750><c> O</c><00:06:52.840><c> this</c><00:06:53.050><c> time</c><00:06:53.940><c> well</c>

00:06:55.070 --> 00:06:55.080 align:start position:0%
is it what's the Big O this time well
 

00:06:55.080 --> 00:06:58.020 align:start position:0%
is it what's the Big O this time well
it's<00:06:56.080><c> actually</c><00:06:56.200><c> the</c><00:06:56.770><c> same</c><00:06:57.130><c> this</c><00:06:57.730><c> is</c><00:06:57.850><c> still</c>

00:06:58.020 --> 00:06:58.030 align:start position:0%
it's actually the same this is still
 

00:06:58.030 --> 00:07:00.930 align:start position:0%
it's actually the same this is still
gonna<00:06:58.180><c> be</c><00:06:58.240><c> Big</c><00:06:58.570><c> O</c><00:06:58.600><c> of</c><00:06:58.720><c> n</c><00:06:58.930><c> times</c><00:06:59.320><c> M</c><00:06:59.560><c> the</c><00:07:00.400><c> reason</c>

00:07:00.930 --> 00:07:00.940 align:start position:0%
gonna be Big O of n times M the reason
 

00:07:00.940 --> 00:07:04.920 align:start position:0%
gonna be Big O of n times M the reason
is<00:07:01.210><c> that</c><00:07:02.670><c> we're</c><00:07:03.670><c> going</c><00:07:03.820><c> through</c><00:07:03.940><c> we're</c><00:07:04.690><c> going</c>

00:07:04.920 --> 00:07:04.930 align:start position:0%
is that we're going through we're going
 

00:07:04.930 --> 00:07:07.440 align:start position:0%
is that we're going through we're going
over<00:07:05.170><c> a</c><00:07:05.710><c> hundred</c><00:07:06.160><c> thousand</c><00:07:06.550><c> iterations</c><00:07:06.790><c> on</c>

00:07:07.440 --> 00:07:07.450 align:start position:0%
over a hundred thousand iterations on
 

00:07:07.450 --> 00:07:11.160 align:start position:0%
over a hundred thousand iterations on
this<00:07:07.630><c> third</c><00:07:08.050><c> for</c><00:07:08.410><c> loop</c><00:07:09.090><c> every</c><00:07:10.090><c> time</c><00:07:10.690><c> this</c><00:07:11.050><c> is</c>

00:07:11.160 --> 00:07:11.170 align:start position:0%
this third for loop every time this is
 

00:07:11.170 --> 00:07:13.920 align:start position:0%
this third for loop every time this is
hard<00:07:11.440><c> this</c><00:07:11.590><c> is</c><00:07:11.650><c> a</c><00:07:11.770><c> hard</c><00:07:12.040><c> coded</c><00:07:12.280><c> number</c><00:07:12.930><c> so</c>

00:07:13.920 --> 00:07:13.930 align:start position:0%
hard this is a hard coded number so
 

00:07:13.930 --> 00:07:16.590 align:start position:0%
hard this is a hard coded number so
whenever<00:07:14.380><c> the</c><00:07:14.620><c> inputs</c><00:07:15.190><c> of</c><00:07:15.430><c> array</c><00:07:15.880><c> and</c><00:07:16.210><c> array</c><00:07:16.300><c> B</c>

00:07:16.590 --> 00:07:16.600 align:start position:0%
whenever the inputs of array and array B
 

00:07:16.600 --> 00:07:20.280 align:start position:0%
whenever the inputs of array and array B
increase<00:07:17.320><c> or</c><00:07:17.740><c> however</c><00:07:18.250><c> much</c><00:07:18.730><c> they</c><00:07:18.970><c> grow</c><00:07:19.290><c> we're</c>

00:07:20.280 --> 00:07:20.290 align:start position:0%
increase or however much they grow we're
 

00:07:20.290 --> 00:07:22.080 align:start position:0%
increase or however much they grow we're
still<00:07:20.680><c> only</c><00:07:20.950><c> ever</c><00:07:21.340><c> going</c><00:07:21.670><c> to</c><00:07:21.700><c> go</c><00:07:21.820><c> through</c><00:07:22.030><c> a</c>

00:07:22.080 --> 00:07:22.090 align:start position:0%
still only ever going to go through a
 

00:07:22.090 --> 00:07:24.570 align:start position:0%
still only ever going to go through a
hundred<00:07:22.210><c> thousand</c><00:07:22.720><c> iterations</c><00:07:23.290><c> and</c><00:07:23.950><c> that's</c>

00:07:24.570 --> 00:07:24.580 align:start position:0%
hundred thousand iterations and that's
 

00:07:24.580 --> 00:07:27.600 align:start position:0%
hundred thousand iterations and that's
that's<00:07:25.060><c> kind</c><00:07:25.240><c> of</c><00:07:25.270><c> like</c><00:07:25.360><c> the</c><00:07:25.510><c> key</c><00:07:25.810><c> for</c><00:07:26.610><c> this</c>

00:07:27.600 --> 00:07:27.610 align:start position:0%
that's kind of like the key for this
 

00:07:27.610 --> 00:07:30.930 align:start position:0%
that's kind of like the key for this
kind<00:07:27.820><c> of</c><00:07:27.910><c> key</c><00:07:28.120><c> for</c><00:07:28.710><c> understanding</c><00:07:29.710><c> Big</c><00:07:30.220><c> O</c><00:07:30.340><c> it's</c>

00:07:30.930 --> 00:07:30.940 align:start position:0%
kind of key for understanding Big O it's
 

00:07:30.940 --> 00:07:34.110 align:start position:0%
kind of key for understanding Big O it's
the<00:07:31.120><c> time</c><00:07:31.360><c> to</c><00:07:31.510><c> perform</c><00:07:31.750><c> the</c><00:07:31.900><c> algorithm</c><00:07:33.120><c> how</c>

00:07:34.110 --> 00:07:34.120 align:start position:0%
the time to perform the algorithm how
 

00:07:34.120 --> 00:07:35.430 align:start position:0%
the time to perform the algorithm how
long<00:07:34.270><c> does</c><00:07:34.330><c> it</c><00:07:34.390><c> take</c><00:07:34.570><c> the</c><00:07:34.690><c> time</c><00:07:34.720><c> to</c><00:07:35.170><c> perform</c>

00:07:35.430 --> 00:07:35.440 align:start position:0%
long does it take the time to perform
 

00:07:35.440 --> 00:07:37.560 align:start position:0%
long does it take the time to perform
the<00:07:35.620><c> algorithm</c><00:07:36.100><c> based</c><00:07:36.790><c> on</c><00:07:36.970><c> the</c><00:07:37.060><c> growth</c><00:07:37.270><c> of</c><00:07:37.300><c> the</c>

00:07:37.560 --> 00:07:37.570 align:start position:0%
the algorithm based on the growth of the
 

00:07:37.570 --> 00:07:41.040 align:start position:0%
the algorithm based on the growth of the
input<00:07:37.810><c> right</c><00:07:39.030><c> here</c><00:07:40.030><c> no</c><00:07:40.420><c> matter</c><00:07:40.600><c> what</c><00:07:40.780><c> the</c><00:07:40.900><c> size</c>

00:07:41.040 --> 00:07:41.050 align:start position:0%
input right here no matter what the size
 

00:07:41.050 --> 00:07:42.900 align:start position:0%
input right here no matter what the size
the<00:07:41.230><c> input</c><00:07:41.470><c> is</c><00:07:41.590><c> how</c><00:07:41.980><c> much</c><00:07:42.100><c> it</c><00:07:42.250><c> grows</c><00:07:42.550><c> we're</c>

00:07:42.900 --> 00:07:42.910 align:start position:0%
the input is how much it grows we're
 

00:07:42.910 --> 00:07:45.420 align:start position:0%
the input is how much it grows we're
still<00:07:43.120><c> only</c><00:07:43.300><c> doing</c><00:07:43.510><c> 1,000</c><00:07:43.930><c> inputs</c><00:07:44.430><c> at</c>

00:07:45.420 --> 00:07:45.430 align:start position:0%
still only doing 1,000 inputs at
 

00:07:45.430 --> 00:07:48.420 align:start position:0%
still only doing 1,000 inputs at
thousand<00:07:46.000><c> iterations</c><00:07:46.150><c> so</c><00:07:46.960><c> this</c><00:07:47.110><c> is</c><00:07:47.260><c> still</c><00:07:47.500><c> Big</c>

00:07:48.420 --> 00:07:48.430 align:start position:0%
thousand iterations so this is still Big
 

00:07:48.430 --> 00:07:53.090 align:start position:0%
thousand iterations so this is still Big
O<00:07:48.550><c> of</c><00:07:48.580><c> one</c><00:07:48.940><c> okay</c><00:07:49.570><c> I</c><00:07:50.760><c> hope</c><00:07:51.760><c> that</c><00:07:51.820><c> makes</c><00:07:52.090><c> sense</c><00:07:52.210><c> so</c>

00:07:53.090 --> 00:07:53.100 align:start position:0%
O of one okay I hope that makes sense so
 

00:07:53.100 --> 00:07:55.470 align:start position:0%
O of one okay I hope that makes sense so
again<00:07:54.100><c> real</c><00:07:54.400><c> quick</c><00:07:54.610><c> just</c><00:07:54.880><c> to</c><00:07:55.060><c> kind</c><00:07:55.300><c> of</c><00:07:55.360><c> like</c>

00:07:55.470 --> 00:07:55.480 align:start position:0%
again real quick just to kind of like
 

00:07:55.480 --> 00:07:58.430 align:start position:0%
again real quick just to kind of like
reiterate<00:07:56.110><c> so</c><00:07:56.710><c> first</c><00:07:56.950><c> off</c><00:07:57.100><c> this</c><00:07:57.250><c> is</c><00:07:57.310><c> Big</c><00:07:57.700><c> O</c><00:07:57.880><c> and</c>

00:07:58.430 --> 00:07:58.440 align:start position:0%
reiterate so first off this is Big O and
 

00:07:58.440 --> 00:08:07.530 align:start position:0%
reiterate so first off this is Big O and
times<00:07:59.440><c> M</c><00:08:00.540><c> still</c><00:08:03.330><c> is</c><00:08:05.970><c> far</c><00:08:06.970><c> as</c><00:08:07.060><c> two</c><00:08:07.240><c> distinct</c>

00:08:07.530 --> 00:08:07.540 align:start position:0%
times M still is far as two distinct
 

00:08:07.540 --> 00:08:10.920 align:start position:0%
times M still is far as two distinct
inputs<00:08:07.870><c> so</c><00:08:08.440><c> what</c><00:08:08.830><c> when</c><00:08:09.430><c> we</c><00:08:09.520><c> loop</c><00:08:09.790><c> the</c><00:08:10.780><c> outer</c>

00:08:10.920 --> 00:08:10.930 align:start position:0%
inputs so what when we loop the outer
 

00:08:10.930 --> 00:08:12.480 align:start position:0%
inputs so what when we loop the outer
loop<00:08:11.110><c> is</c><00:08:11.530><c> happening</c><00:08:11.950><c> and</c><00:08:12.100><c> we</c><00:08:12.160><c> go</c><00:08:12.250><c> to</c><00:08:12.310><c> the</c><00:08:12.370><c> inner</c>

00:08:12.480 --> 00:08:12.490 align:start position:0%
loop is happening and we go to the inner
 

00:08:12.490 --> 00:08:15.000 align:start position:0%
loop is happening and we go to the inner
loop<00:08:12.700><c> there's</c><00:08:13.660><c> a</c><00:08:13.720><c> boat</c><00:08:13.930><c> two</c><00:08:14.500><c> different</c><00:08:14.650><c> arrays</c>

00:08:15.000 --> 00:08:15.010 align:start position:0%
loop there's a boat two different arrays
 

00:08:15.010 --> 00:08:16.830 align:start position:0%
loop there's a boat two different arrays
so<00:08:15.040><c> we</c><00:08:15.370><c> had</c><00:08:15.490><c> to</c><00:08:15.610><c> the</c><00:08:15.700><c> call</c><00:08:15.910><c> one</c><00:08:16.120><c> and</c><00:08:16.330><c> one</c><00:08:16.630><c> M</c>

00:08:16.830 --> 00:08:16.840 align:start position:0%
so we had to the call one and one M
 

00:08:16.840 --> 00:08:19.320 align:start position:0%
so we had to the call one and one M
because<00:08:17.770><c> we</c><00:08:18.430><c> don't</c><00:08:18.640><c> know</c><00:08:18.940><c> how</c><00:08:19.210><c> they're</c>

00:08:19.320 --> 00:08:19.330 align:start position:0%
because we don't know how they're
 

00:08:19.330 --> 00:08:20.970 align:start position:0%
because we don't know how they're
connected<00:08:19.840><c> and</c><00:08:20.080><c> we</c><00:08:20.230><c> can</c><00:08:20.350><c> we</c><00:08:20.560><c> had</c><00:08:20.650><c> to</c><00:08:20.770><c> assume</c>

00:08:20.970 --> 00:08:20.980 align:start position:0%
connected and we can we had to assume
 

00:08:20.980 --> 00:08:23.159 align:start position:0%
connected and we can we had to assume
that<00:08:21.130><c> they're</c><00:08:21.280><c> not</c><00:08:21.400><c> at</c><00:08:21.730><c> all</c><00:08:21.880><c> so</c><00:08:22.390><c> one</c><00:08:22.630><c> grows</c><00:08:22.900><c> the</c>

00:08:23.159 --> 00:08:23.169 align:start position:0%
that they're not at all so one grows the
 

00:08:23.169 --> 00:08:25.530 align:start position:0%
that they're not at all so one grows the
other<00:08:23.290><c> one</c><00:08:23.410><c> might</c><00:08:23.590><c> not</c><00:08:23.740><c> all</c><00:08:24.550><c> right</c><00:08:24.700><c> so</c><00:08:25.120><c> because</c>

00:08:25.530 --> 00:08:25.540 align:start position:0%
other one might not all right so because
 

00:08:25.540 --> 00:08:27.870 align:start position:0%
other one might not all right so because
are<00:08:25.750><c> two</c><00:08:25.900><c> distinct</c><00:08:26.350><c> inputs</c><00:08:27.310><c> way</c><00:08:27.490><c> to</c><00:08:27.550><c> keep</c><00:08:27.760><c> them</c>

00:08:27.870 --> 00:08:27.880 align:start position:0%
are two distinct inputs way to keep them
 

00:08:27.880 --> 00:08:29.760 align:start position:0%
are two distinct inputs way to keep them
as<00:08:28.000><c> such</c><00:08:28.180><c> you</c><00:08:28.960><c> know</c><00:08:28.990><c> with</c><00:08:29.169><c> this</c><00:08:29.260><c> third</c><00:08:29.470><c> loop</c>

00:08:29.760 --> 00:08:29.770 align:start position:0%
as such you know with this third loop
 

00:08:29.770 --> 00:08:31.650 align:start position:0%
as such you know with this third loop
it's<00:08:30.550><c> always</c><00:08:30.970><c> going</c><00:08:31.090><c> to</c><00:08:31.120><c> go</c><00:08:31.240><c> over</c><00:08:31.390><c> a</c><00:08:31.450><c> hundred</c>

00:08:31.650 --> 00:08:31.660 align:start position:0%
it's always going to go over a hundred
 

00:08:31.660 --> 00:08:34.589 align:start position:0%
it's always going to go over a hundred
thousand<00:08:31.990><c> iterations</c><00:08:32.280><c> no</c><00:08:33.280><c> matter</c><00:08:33.490><c> what</c><00:08:33.729><c> so</c>

00:08:34.589 --> 00:08:34.599 align:start position:0%
thousand iterations no matter what so
 

00:08:34.599 --> 00:08:37.800 align:start position:0%
thousand iterations no matter what so
windy<00:08:34.990><c> so</c><00:08:35.560><c> it's</c><00:08:36.280><c> not</c><00:08:36.550><c> going</c><00:08:36.700><c> to</c><00:08:36.789><c> increase</c><00:08:37.030><c> it's</c>

00:08:37.800 --> 00:08:37.810 align:start position:0%
windy so it's not going to increase it's
 

00:08:37.810 --> 00:08:39.839 align:start position:0%
windy so it's not going to increase it's
time<00:08:37.960><c> to</c><00:08:38.080><c> perform</c><00:08:38.440><c> that</c><00:08:38.860><c> third</c><00:08:39.310><c> for</c><00:08:39.669><c> loop</c>

00:08:39.839 --> 00:08:39.849 align:start position:0%
time to perform that third for loop
 

00:08:39.849 --> 00:08:42.240 align:start position:0%
time to perform that third for loop
isn't<00:08:40.599><c> going</c><00:08:40.719><c> to</c><00:08:40.780><c> change</c><00:08:40.890><c> all</c><00:08:41.890><c> right</c><00:08:42.039><c> it's</c>

00:08:42.240 --> 00:08:42.250 align:start position:0%
isn't going to change all right it's
 

00:08:42.250 --> 00:08:44.159 align:start position:0%
isn't going to change all right it's
never<00:08:42.520><c> going</c><00:08:42.700><c> to</c><00:08:42.760><c> change</c><00:08:42.969><c> with</c><00:08:43.360><c> this</c><00:08:43.479><c> what's</c>

00:08:44.159 --> 00:08:44.169 align:start position:0%
never going to change with this what's
 

00:08:44.169 --> 00:08:47.240 align:start position:0%
never going to change with this what's
the<00:08:44.229><c> increase</c><00:08:44.590><c> of</c><00:08:44.740><c> array</c><00:08:45.100><c> and</c><00:08:45.400><c> rate</c><00:08:45.550><c> be</c><00:08:45.720><c> okay</c>

00:08:47.240 --> 00:08:47.250 align:start position:0%
the increase of array and rate be okay
 

00:08:47.250 --> 00:08:49.170 align:start position:0%
the increase of array and rate be okay
all<00:08:48.250><c> right</c><00:08:48.400><c> so</c><00:08:48.520><c> that's</c><00:08:48.610><c> the</c><00:08:48.790><c> answer</c><00:08:48.910><c> for</c><00:08:49.150><c> that</c>

00:08:49.170 --> 00:08:49.180 align:start position:0%
all right so that's the answer for that
 

00:08:49.180 --> 00:08:49.500 align:start position:0%
all right so that's the answer for that
one

00:08:49.500 --> 00:08:49.510 align:start position:0%
one
 

00:08:49.510 --> 00:08:51.329 align:start position:0%
one
if<00:08:49.570><c> you</c><00:08:49.870><c> have</c><00:08:50.050><c> any</c><00:08:50.230><c> questions</c><00:08:50.650><c> or</c><00:08:50.890><c> if</c><00:08:51.100><c> you</c><00:08:51.220><c> have</c>

00:08:51.329 --> 00:08:51.339 align:start position:0%
if you have any questions or if you have
 

00:08:51.339 --> 00:08:52.260 align:start position:0%
if you have any questions or if you have
any<00:08:51.430><c> other</c><00:08:51.580><c> question</c>

00:08:52.260 --> 00:08:52.270 align:start position:0%
any other question
 

00:08:52.270 --> 00:08:54.000 align:start position:0%
any other question
that<00:08:52.660><c> you're</c><00:08:52.840><c> unsure</c><00:08:53.050><c> of</c><00:08:53.260><c> that</c><00:08:53.410><c> you</c><00:08:53.620><c> need</c><00:08:53.800><c> help</c>

00:08:54.000 --> 00:08:54.010 align:start position:0%
that you're unsure of that you need help
 

00:08:54.010 --> 00:08:56.610 align:start position:0%
that you're unsure of that you need help
with<00:08:54.220><c> you</c><00:08:55.180><c> leave</c><00:08:55.420><c> in</c><00:08:55.540><c> the</c><00:08:55.570><c> comments</c><00:08:55.960><c> below</c><00:08:56.050><c> and</c>

00:08:56.610 --> 00:08:56.620 align:start position:0%
with you leave in the comments below and
 

00:08:56.620 --> 00:08:57.690 align:start position:0%
with you leave in the comments below and
I'll<00:08:56.800><c> get</c><00:08:57.010><c> back</c><00:08:57.190><c> to</c><00:08:57.220><c> you</c><00:08:57.340><c> as</c><00:08:57.430><c> soon</c><00:08:57.520><c> as</c><00:08:57.640><c> possible</c>

00:08:57.690 --> 00:08:57.700 align:start position:0%
I'll get back to you as soon as possible
 

00:08:57.700 --> 00:09:00.540 align:start position:0%
I'll get back to you as soon as possible
and<00:08:58.330><c> in</c><00:08:59.080><c> the</c><00:08:59.140><c> meantime</c><00:08:59.290><c> I</c><00:08:59.800><c> will</c><00:09:00.310><c> see</c><00:09:00.400><c> you</c><00:09:00.460><c> next</c>

00:09:00.540 --> 00:09:00.550 align:start position:0%
and in the meantime I will see you next
 

00:09:00.550 --> 00:09:02.730 align:start position:0%
and in the meantime I will see you next
video

