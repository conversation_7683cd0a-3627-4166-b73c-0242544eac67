WEBVTT
Kind: captions
Language: en

00:00:00.880 --> 00:00:02.550 align:start position:0%
 
hey<00:00:01.040><c> everyone</c><00:00:01.319><c> it's</c><00:00:01.480><c> aspr</c><00:00:01.959><c> and</c><00:00:02.080><c> today</c><00:00:02.360><c> let's</c>

00:00:02.550 --> 00:00:02.560 align:start position:0%
hey everyone it's aspr and today let's
 

00:00:02.560 --> 00:00:05.110 align:start position:0%
hey everyone it's aspr and today let's
build<00:00:02.879><c> an</c><00:00:03.120><c> autonomous</c><00:00:03.639><c> rag</c><00:00:04.000><c> assistant</c><00:00:04.920><c> in</c>

00:00:05.110 --> 00:00:05.120 align:start position:0%
build an autonomous rag assistant in
 

00:00:05.120 --> 00:00:07.630 align:start position:0%
build an autonomous rag assistant in
Auto<00:00:05.520><c> rag</c><00:00:05.839><c> we</c><00:00:06.000><c> give</c><00:00:06.160><c> the</c><00:00:06.279><c> llm</c><00:00:07.120><c> long-term</c>

00:00:07.630 --> 00:00:07.640 align:start position:0%
Auto rag we give the llm long-term
 

00:00:07.640 --> 00:00:10.110 align:start position:0%
Auto rag we give the llm long-term
memory<00:00:08.280><c> back</c><00:00:08.519><c> by</c><00:00:08.719><c> database</c><00:00:09.599><c> we</c><00:00:09.760><c> give</c><00:00:09.880><c> it</c><00:00:10.000><c> a</c>

00:00:10.110 --> 00:00:10.120 align:start position:0%
memory back by database we give it a
 

00:00:10.120 --> 00:00:12.430 align:start position:0%
memory back by database we give it a
knowledge<00:00:10.480><c> base</c><00:00:10.960><c> back</c><00:00:11.200><c> by</c><00:00:11.400><c> Vector</c><00:00:11.719><c> DB</c><00:00:12.200><c> and</c><00:00:12.320><c> we</c>

00:00:12.430 --> 00:00:12.440 align:start position:0%
knowledge base back by Vector DB and we
 

00:00:12.440 --> 00:00:14.829 align:start position:0%
knowledge base back by Vector DB and we
give<00:00:12.559><c> it</c><00:00:12.759><c> tools</c><00:00:13.200><c> to</c><00:00:13.440><c> search</c><00:00:13.839><c> the</c><00:00:14.000><c> web</c><00:00:14.519><c> run</c>

00:00:14.829 --> 00:00:14.839 align:start position:0%
give it tools to search the web run
 

00:00:14.839 --> 00:00:17.750 align:start position:0%
give it tools to search the web run
database<00:00:15.320><c> queries</c><00:00:15.719><c> or</c><00:00:16.000><c> make</c><00:00:16.199><c> API</c><00:00:16.600><c> calls</c><00:00:17.520><c> so</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
database queries or make API calls so
 

00:00:17.760 --> 00:00:20.590 align:start position:0%
database queries or make API calls so
when<00:00:17.960><c> the</c><00:00:18.080><c> user</c><00:00:18.520><c> asks</c><00:00:18.800><c> a</c><00:00:19.039><c> question</c><00:00:19.800><c> the</c><00:00:19.960><c> llm</c>

00:00:20.590 --> 00:00:20.600 align:start position:0%
when the user asks a question the llm
 

00:00:20.600 --> 00:00:23.150 align:start position:0%
when the user asks a question the llm
decides<00:00:21.439><c> how</c><00:00:21.640><c> to</c><00:00:21.840><c> answer</c><00:00:22.199><c> that</c><00:00:22.439><c> question</c>

00:00:23.150 --> 00:00:23.160 align:start position:0%
decides how to answer that question
 

00:00:23.160 --> 00:00:25.109 align:start position:0%
decides how to answer that question
whether<00:00:23.400><c> it</c><00:00:23.519><c> needs</c><00:00:23.720><c> to</c><00:00:23.960><c> get</c><00:00:24.119><c> it</c><00:00:24.680><c> uh</c><00:00:24.800><c> get</c><00:00:24.960><c> it</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
whether it needs to get it uh get it
 

00:00:25.119 --> 00:00:27.029 align:start position:0%
whether it needs to get it uh get it
chat<00:00:25.400><c> history</c><00:00:25.680><c> from</c><00:00:25.840><c> the</c><00:00:25.920><c> memory</c><00:00:26.679><c> whether</c><00:00:26.880><c> it</c>

00:00:27.029 --> 00:00:27.039 align:start position:0%
chat history from the memory whether it
 

00:00:27.039 --> 00:00:28.990 align:start position:0%
chat history from the memory whether it
needs<00:00:27.279><c> to</c><00:00:27.439><c> search</c><00:00:27.720><c> the</c><00:00:27.840><c> knowledge</c><00:00:28.199><c> base</c><00:00:28.800><c> or</c>

00:00:28.990 --> 00:00:29.000 align:start position:0%
needs to search the knowledge base or
 

00:00:29.000 --> 00:00:30.589 align:start position:0%
needs to search the knowledge base or
whether<00:00:29.240><c> it</c><00:00:29.359><c> needs</c><00:00:29.560><c> to</c><00:00:29.679><c> make</c><00:00:29.800><c> a</c><00:00:30.039><c> tool</c><00:00:30.279><c> call</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
whether it needs to make a tool call
 

00:00:30.599 --> 00:00:32.510 align:start position:0%
whether it needs to make a tool call
like<00:00:30.759><c> searching</c><00:00:31.199><c> the</c><00:00:31.400><c> web</c><00:00:32.040><c> running</c><00:00:32.399><c> a</c>

00:00:32.510 --> 00:00:32.520 align:start position:0%
like searching the web running a
 

00:00:32.520 --> 00:00:35.270 align:start position:0%
like searching the web running a
database<00:00:32.960><c> query</c><00:00:33.559><c> or</c><00:00:33.840><c> making</c><00:00:34.120><c> an</c><00:00:34.320><c> API</c><00:00:34.719><c> call</c><00:00:35.079><c> to</c>

00:00:35.270 --> 00:00:35.280 align:start position:0%
database query or making an API call to
 

00:00:35.280 --> 00:00:37.350 align:start position:0%
database query or making an API call to
get<00:00:35.520><c> the</c><00:00:35.719><c> information</c><00:00:36.200><c> it</c><00:00:36.360><c> needs</c><00:00:36.840><c> to</c><00:00:37.040><c> answer</c>

00:00:37.350 --> 00:00:37.360 align:start position:0%
get the information it needs to answer
 

00:00:37.360 --> 00:00:40.389 align:start position:0%
get the information it needs to answer
that<00:00:37.840><c> question</c><00:00:38.840><c> this</c><00:00:38.960><c> is</c><00:00:39.239><c> how</c><00:00:39.520><c> our</c><00:00:39.800><c> autoag</c>

00:00:40.389 --> 00:00:40.399 align:start position:0%
that question this is how our autoag
 

00:00:40.399 --> 00:00:42.470 align:start position:0%
that question this is how our autoag
application<00:00:40.840><c> looks</c><00:00:41.120><c> like</c><00:00:41.920><c> we're</c><00:00:42.120><c> going</c><00:00:42.239><c> to</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
application looks like we're going to
 

00:00:42.480 --> 00:00:45.150 align:start position:0%
application looks like we're going to
give<00:00:42.640><c> it</c><00:00:43.000><c> a</c><00:00:43.760><c> uh</c><00:00:43.920><c> blog</c><00:00:44.280><c> post</c><00:00:44.559><c> in</c><00:00:44.680><c> the</c><00:00:44.800><c> knowledge</c>

00:00:45.150 --> 00:00:45.160 align:start position:0%
give it a uh blog post in the knowledge
 

00:00:45.160 --> 00:00:47.750 align:start position:0%
give it a uh blog post in the knowledge
base<00:00:45.840><c> and</c><00:00:46.000><c> then</c><00:00:46.120><c> we're</c><00:00:46.239><c> going</c><00:00:46.320><c> to</c><00:00:46.559><c> ask</c><00:00:47.559><c> what</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
base and then we're going to ask what
 

00:00:47.760 --> 00:00:49.869 align:start position:0%
base and then we're going to ask what
did<00:00:48.399><c> meta</c>

00:00:49.869 --> 00:00:49.879 align:start position:0%
did meta
 

00:00:49.879 --> 00:00:52.709 align:start position:0%
did meta
release<00:00:50.879><c> so</c><00:00:51.120><c> with</c><00:00:51.280><c> our</c><00:00:51.480><c> autoag</c><00:00:52.039><c> assistant</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
release so with our autoag assistant
 

00:00:52.719 --> 00:00:55.750 align:start position:0%
release so with our autoag assistant
instead<00:00:53.120><c> of</c><00:00:53.960><c> doing</c><00:00:54.440><c> a</c><00:00:54.920><c> semantic</c><00:00:55.359><c> search</c><00:00:55.600><c> on</c>

00:00:55.750 --> 00:00:55.760 align:start position:0%
instead of doing a semantic search on
 

00:00:55.760 --> 00:00:58.110 align:start position:0%
instead of doing a semantic search on
this<00:00:55.960><c> query</c><00:00:56.600><c> we</c><00:00:56.840><c> let</c><00:00:57.039><c> the</c><00:00:57.160><c> llm</c><00:00:57.680><c> search</c><00:00:57.960><c> the</c>

00:00:58.110 --> 00:00:58.120 align:start position:0%
this query we let the llm search the
 

00:00:58.120 --> 00:01:02.990 align:start position:0%
this query we let the llm search the
knowledge<00:00:58.519><c> base</c><00:00:58.840><c> for</c><00:00:59.160><c> what</c><00:00:59.320><c> it</c><00:00:59.480><c> needs</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
 
 

00:01:03.000 --> 00:01:05.070 align:start position:0%
 
so<00:01:03.239><c> this</c><00:01:03.399><c> time</c><00:01:03.559><c> is</c><00:01:03.680><c> search</c><00:01:03.960><c> for</c><00:01:04.280><c> meta</c><00:01:04.680><c> release</c>

00:01:05.070 --> 00:01:05.080 align:start position:0%
so this time is search for meta release
 

00:01:05.080 --> 00:01:07.230 align:start position:0%
so this time is search for meta release
instead<00:01:05.360><c> of</c><00:01:05.519><c> that</c><00:01:05.640><c> full</c><00:01:06.000><c> query</c><00:01:07.000><c> which</c>

00:01:07.230 --> 00:01:07.240 align:start position:0%
instead of that full query which
 

00:01:07.240 --> 00:01:09.149 align:start position:0%
instead of that full query which
provides<00:01:07.960><c> better</c><00:01:08.200><c> search</c><00:01:08.560><c> results</c><00:01:08.880><c> from</c><00:01:09.040><c> the</c>

00:01:09.149 --> 00:01:09.159 align:start position:0%
provides better search results from the
 

00:01:09.159 --> 00:01:12.310 align:start position:0%
provides better search results from the
knowledge<00:01:09.439><c> base</c><00:01:09.720><c> as</c><00:01:09.880><c> well</c><00:01:10.640><c> next</c><00:01:10.880><c> let's</c><00:01:11.240><c> ask</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
knowledge base as well next let's ask
 

00:01:12.320 --> 00:01:15.030 align:start position:0%
knowledge base as well next let's ask
uh<00:01:13.320><c> what's</c><00:01:13.600><c> happening</c><00:01:14.040><c> in</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
uh what's happening in
 

00:01:15.040 --> 00:01:17.710 align:start position:0%
uh what's happening in
France<00:01:16.040><c> so</c><00:01:16.159><c> in</c><00:01:16.360><c> this</c><00:01:16.560><c> case</c><00:01:17.000><c> we</c><00:01:17.200><c> wanted</c><00:01:17.560><c> to</c>

00:01:17.710 --> 00:01:17.720 align:start position:0%
France so in this case we wanted to
 

00:01:17.720 --> 00:01:19.270 align:start position:0%
France so in this case we wanted to
search<00:01:18.080><c> the</c><00:01:18.240><c> internet</c><00:01:18.640><c> to</c><00:01:18.799><c> provide</c><00:01:19.119><c> this</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
search the internet to provide this
 

00:01:19.280 --> 00:01:21.270 align:start position:0%
search the internet to provide this
information<00:01:19.759><c> for</c><00:01:19.960><c> us</c><00:01:20.320><c> which</c><00:01:20.479><c> it</c>

00:01:21.270 --> 00:01:21.280 align:start position:0%
information for us which it
 

00:01:21.280 --> 00:01:24.030 align:start position:0%
information for us which it
does<00:01:22.280><c> finally</c><00:01:22.600><c> we're</c><00:01:22.720><c> going</c><00:01:22.840><c> to</c>

00:01:24.030 --> 00:01:24.040 align:start position:0%
does finally we're going to
 

00:01:24.040 --> 00:01:27.429 align:start position:0%
does finally we're going to
ask<00:01:25.240><c> summarize</c><00:01:26.240><c> our</c>

00:01:27.429 --> 00:01:27.439 align:start position:0%
ask summarize our
 

00:01:27.439 --> 00:01:29.910 align:start position:0%
ask summarize our
conversation<00:01:28.439><c> now</c><00:01:28.640><c> when</c><00:01:28.759><c> we</c><00:01:28.880><c> send</c><00:01:29.240><c> that</c><00:01:29.600><c> we</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
conversation now when we send that we
 

00:01:29.920 --> 00:01:33.069 align:start position:0%
conversation now when we send that we
wanted<00:01:30.240><c> to</c><00:01:30.439><c> search</c><00:01:30.920><c> its</c><00:01:31.360><c> memory</c><00:01:31.920><c> for</c><00:01:32.479><c> the</c><00:01:32.680><c> chat</c>

00:01:33.069 --> 00:01:33.079 align:start position:0%
wanted to search its memory for the chat
 

00:01:33.079 --> 00:01:34.670 align:start position:0%
wanted to search its memory for the chat
history<00:01:33.640><c> and</c><00:01:33.759><c> then</c><00:01:33.920><c> give</c><00:01:34.040><c> us</c><00:01:34.159><c> a</c><00:01:34.280><c> summary</c><00:01:34.600><c> of</c>

00:01:34.670 --> 00:01:34.680 align:start position:0%
history and then give us a summary of
 

00:01:34.680 --> 00:01:35.910 align:start position:0%
history and then give us a summary of
our

00:01:35.910 --> 00:01:35.920 align:start position:0%
our
 

00:01:35.920 --> 00:01:37.749 align:start position:0%
our
conversation<00:01:36.920><c> now</c><00:01:37.159><c> these</c><00:01:37.360><c> types</c><00:01:37.560><c> of</c>

00:01:37.749 --> 00:01:37.759 align:start position:0%
conversation now these types of
 

00:01:37.759 --> 00:01:39.510 align:start position:0%
conversation now these types of
questions<00:01:38.280><c> would</c><00:01:38.439><c> be</c><00:01:38.640><c> pretty</c><00:01:39.159><c> difficult</c><00:01:39.320><c> for</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
questions would be pretty difficult for
 

00:01:39.520 --> 00:01:42.749 align:start position:0%
questions would be pretty difficult for
a<00:01:40.000><c> regular</c><00:01:40.680><c> llm</c><00:01:41.200><c> API</c><00:01:41.680><c> or</c><00:01:41.840><c> a</c><00:01:42.000><c> regular</c><00:01:42.360><c> rag</c>

00:01:42.749 --> 00:01:42.759 align:start position:0%
a regular llm API or a regular rag
 

00:01:42.759 --> 00:01:44.990 align:start position:0%
a regular llm API or a regular rag
application<00:01:43.200><c> to</c><00:01:43.399><c> answer</c><00:01:44.040><c> but</c><00:01:44.200><c> our</c><00:01:44.399><c> Auto</c><00:01:44.719><c> rag</c>

00:01:44.990 --> 00:01:45.000 align:start position:0%
application to answer but our Auto rag
 

00:01:45.000 --> 00:01:47.389 align:start position:0%
application to answer but our Auto rag
application<00:01:45.479><c> works</c><00:01:45.880><c> brilliantly</c><00:01:46.439><c> with</c><00:01:46.600><c> them</c>

00:01:47.389 --> 00:01:47.399 align:start position:0%
application works brilliantly with them
 

00:01:47.399 --> 00:01:51.510 align:start position:0%
application works brilliantly with them
not<00:01:47.600><c> only</c><00:01:47.960><c> that</c><00:01:48.399><c> this</c><00:01:48.600><c> can</c><00:01:48.799><c> be</c><00:01:49.280><c> extended</c>

00:01:51.510 --> 00:01:51.520 align:start position:0%
not only that this can be extended
 

00:01:51.520 --> 00:01:54.709 align:start position:0%
not only that this can be extended
to<00:01:52.520><c> um</c><00:01:53.040><c> to</c><00:01:53.360><c> questions</c><00:01:53.880><c> that</c><00:01:54.119><c> requires</c><00:01:54.520><c> some</c>

00:01:54.709 --> 00:01:54.719 align:start position:0%
to um to questions that requires some
 

00:01:54.719 --> 00:01:56.990 align:start position:0%
to um to questions that requires some
sort<00:01:54.960><c> of</c><00:01:55.320><c> uh</c><00:01:55.960><c> datetime</c><00:01:56.439><c> search</c><00:01:56.719><c> for</c><00:01:56.880><c> the</c>

00:01:56.990 --> 00:01:57.000 align:start position:0%
sort of uh datetime search for the
 

00:01:57.000 --> 00:01:58.230 align:start position:0%
sort of uh datetime search for the
answer<00:01:57.240><c> so</c><00:01:57.399><c> if</c><00:01:57.479><c> you</c><00:01:57.560><c> want</c><00:01:57.680><c> to</c><00:01:57.840><c> ask</c><00:01:58.079><c> the</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
answer so if you want to ask the
 

00:01:58.240 --> 00:02:00.270 align:start position:0%
answer so if you want to ask the
assistant<00:01:59.079><c> hey</c><00:01:59.280><c> what</c><00:01:59.399><c> did</c><00:01:59.520><c> I</c><00:01:59.640><c> do</c><00:01:59.920><c> yesterday</c>

00:02:00.270 --> 00:02:00.280 align:start position:0%
assistant hey what did I do yesterday
 

00:02:00.280 --> 00:02:01.870 align:start position:0%
assistant hey what did I do yesterday
and<00:02:00.640><c> then</c><00:02:00.759><c> your</c><00:02:00.880><c> knowledge</c><00:02:01.200><c> base</c><00:02:01.479><c> has</c>

00:02:01.870 --> 00:02:01.880 align:start position:0%
and then your knowledge base has
 

00:02:01.880 --> 00:02:04.990 align:start position:0%
and then your knowledge base has
information<00:02:02.479><c> about</c><00:02:02.719><c> your</c><00:02:02.960><c> task</c><00:02:03.840><c> each</c><00:02:04.159><c> day</c>

00:02:04.990 --> 00:02:05.000 align:start position:0%
information about your task each day
 

00:02:05.000 --> 00:02:06.870 align:start position:0%
information about your task each day
that's<00:02:05.200><c> not</c><00:02:05.320><c> a</c><00:02:05.520><c> question</c><00:02:05.880><c> a</c><00:02:06.119><c> regular</c><00:02:06.479><c> rag</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
that's not a question a regular rag
 

00:02:06.880 --> 00:02:09.029 align:start position:0%
that's not a question a regular rag
application<00:02:07.360><c> can</c><00:02:07.520><c> answer</c><00:02:08.080><c> but</c><00:02:08.200><c> your</c><00:02:08.399><c> autoag</c>

00:02:09.029 --> 00:02:09.039 align:start position:0%
application can answer but your autoag
 

00:02:09.039 --> 00:02:10.830 align:start position:0%
application can answer but your autoag
application<00:02:09.479><c> can</c><00:02:09.640><c> be</c><00:02:09.800><c> built</c><00:02:10.039><c> to</c><00:02:10.239><c> answer</c><00:02:10.599><c> those</c>

00:02:10.830 --> 00:02:10.840 align:start position:0%
application can be built to answer those
 

00:02:10.840 --> 00:02:12.910 align:start position:0%
application can be built to answer those
questions<00:02:11.239><c> pretty</c><00:02:11.520><c> well</c><00:02:12.200><c> so</c><00:02:12.360><c> you</c><00:02:12.440><c> see</c><00:02:12.680><c> over</c>

00:02:12.910 --> 00:02:12.920 align:start position:0%
questions pretty well so you see over
 

00:02:12.920 --> 00:02:14.869 align:start position:0%
questions pretty well so you see over
here<00:02:13.120><c> it</c><00:02:13.280><c> gave</c><00:02:13.480><c> me</c><00:02:13.760><c> a</c><00:02:13.959><c> summary</c><00:02:14.440><c> of</c><00:02:14.640><c> our</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
here it gave me a summary of our
 

00:02:14.879 --> 00:02:18.470 align:start position:0%
here it gave me a summary of our
conversation<00:02:15.400><c> as</c><00:02:16.000><c> well</c><00:02:17.000><c> so</c><00:02:17.800><c> the</c><00:02:18.040><c> code</c><00:02:18.280><c> for</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
conversation as well so the code for
 

00:02:18.480 --> 00:02:19.710 align:start position:0%
conversation as well so the code for
this<00:02:18.680><c> application</c><00:02:19.080><c> I'm</c><00:02:19.160><c> going</c><00:02:19.280><c> to</c><00:02:19.400><c> give</c><00:02:19.519><c> you</c>

00:02:19.710 --> 00:02:19.720 align:start position:0%
this application I'm going to give you
 

00:02:19.720 --> 00:02:21.070 align:start position:0%
this application I'm going to give you
the<00:02:19.840><c> entire</c><00:02:20.160><c> code</c><00:02:20.319><c> for</c><00:02:20.480><c> this</c><00:02:20.640><c> application</c><00:02:20.959><c> is</c>

00:02:21.070 --> 00:02:21.080 align:start position:0%
the entire code for this application is
 

00:02:21.080 --> 00:02:23.550 align:start position:0%
the entire code for this application is
fully<00:02:21.360><c> open</c><00:02:21.680><c> source</c><00:02:22.400><c> so</c><00:02:22.599><c> our</c><00:02:22.920><c> application</c>

00:02:23.550 --> 00:02:23.560 align:start position:0%
fully open source so our application
 

00:02:23.560 --> 00:02:28.550 align:start position:0%
fully open source so our application
today<00:02:24.560><c> uses</c><00:02:25.000><c> the</c><00:02:25.280><c> open</c><00:02:25.560><c> a</c><00:02:25.840><c> llm</c><00:02:26.599><c> gp4</c><00:02:27.599><c> it</c><00:02:27.879><c> has</c><00:02:28.440><c> uh</c>

00:02:28.550 --> 00:02:28.560 align:start position:0%
today uses the open a llm gp4 it has uh
 

00:02:28.560 --> 00:02:30.949 align:start position:0%
today uses the open a llm gp4 it has uh
a<00:02:28.720><c> memory</c><00:02:29.239><c> packed</c><00:02:29.560><c> by</c><00:02:29.879><c> postest</c><00:02:30.319><c> database</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
a memory packed by postest database
 

00:02:30.959 --> 00:02:32.949 align:start position:0%
a memory packed by postest database
which<00:02:31.040><c> is</c><00:02:31.200><c> storing</c><00:02:31.560><c> the</c><00:02:31.680><c> chat</c><00:02:32.000><c> history</c><00:02:32.840><c> it's</c>

00:02:32.949 --> 00:02:32.959 align:start position:0%
which is storing the chat history it's
 

00:02:32.959 --> 00:02:34.589 align:start position:0%
which is storing the chat history it's
called<00:02:33.160><c> a</c><00:02:33.280><c> knowledge</c><00:02:33.519><c> base</c><00:02:33.720><c> of</c><00:02:33.920><c> PDFs</c><00:02:34.360><c> and</c>

00:02:34.589 --> 00:02:34.599 align:start position:0%
called a knowledge base of PDFs and
 

00:02:34.599 --> 00:02:36.790 align:start position:0%
called a knowledge base of PDFs and
websites<00:02:35.280><c> we</c><00:02:35.440><c> haven't</c><00:02:35.760><c> added</c><00:02:36.040><c> the</c><00:02:36.160><c> rest</c><00:02:36.480><c> but</c>

00:02:36.790 --> 00:02:36.800 align:start position:0%
websites we haven't added the rest but
 

00:02:36.800 --> 00:02:39.030 align:start position:0%
websites we haven't added the rest but
you<00:02:36.920><c> can</c><00:02:37.080><c> do</c><00:02:37.280><c> that</c><00:02:37.480><c> yourself</c><00:02:37.879><c> if</c><00:02:38.040><c> you</c><00:02:38.160><c> need</c><00:02:38.920><c> and</c>

00:02:39.030 --> 00:02:39.040 align:start position:0%
you can do that yourself if you need and
 

00:02:39.040 --> 00:02:40.990 align:start position:0%
you can do that yourself if you need and
then<00:02:39.239><c> it's</c><00:02:39.480><c> got</c><00:02:39.720><c> the</c><00:02:39.920><c> search</c><00:02:40.280><c> web</c><00:02:40.519><c> tool</c><00:02:40.840><c> but</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
then it's got the search web tool but
 

00:02:41.000 --> 00:02:42.990 align:start position:0%
then it's got the search web tool but
you<00:02:41.080><c> can</c><00:02:41.360><c> add</c><00:02:41.680><c> a</c><00:02:41.879><c> tool</c><00:02:42.080><c> for</c><00:02:42.319><c> database</c><00:02:42.760><c> you</c><00:02:42.879><c> can</c>

00:02:42.990 --> 00:02:43.000 align:start position:0%
you can add a tool for database you can
 

00:02:43.000 --> 00:02:45.550 align:start position:0%
you can add a tool for database you can
add<00:02:43.120><c> a</c><00:02:43.239><c> tool</c><00:02:43.440><c> for</c><00:02:44.080><c> apis</c><00:02:44.560><c> or</c><00:02:44.760><c> workflows</c><00:02:45.400><c> I'll</c>

00:02:45.550 --> 00:02:45.560 align:start position:0%
add a tool for apis or workflows I'll
 

00:02:45.560 --> 00:02:47.910 align:start position:0%
add a tool for apis or workflows I'll
leave<00:02:45.760><c> that</c><00:02:45.959><c> up</c><00:02:46.080><c> to</c><00:02:46.200><c> you</c><00:02:46.400><c> as</c><00:02:46.560><c> well</c><00:02:47.319><c> so</c><00:02:47.480><c> the</c><00:02:47.640><c> code</c>

00:02:47.910 --> 00:02:47.920 align:start position:0%
leave that up to you as well so the code
 

00:02:47.920 --> 00:02:49.990 align:start position:0%
leave that up to you as well so the code
for<00:02:48.120><c> this</c><00:02:48.319><c> application</c><00:02:49.040><c> is</c><00:02:49.200><c> under</c><00:02:49.440><c> the</c><00:02:49.519><c> F</c><00:02:49.800><c> dat</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
for this application is under the F dat
 

00:02:50.000 --> 00:02:52.710 align:start position:0%
for this application is under the F dat
repo<00:02:50.599><c> so</c><00:02:50.800><c> for</c><00:02:51.200><c> can</c><00:02:51.400><c> clone</c><00:02:51.720><c> this</c><00:02:51.959><c> repository</c>

00:02:52.710 --> 00:02:52.720 align:start position:0%
repo so for can clone this repository
 

00:02:52.720 --> 00:02:55.550 align:start position:0%
repo so for can clone this repository
and<00:02:52.959><c> go</c><00:02:53.159><c> to</c><00:02:53.519><c> the</c><00:02:53.720><c> cookbooks</c><00:02:54.239><c> folder</c><00:02:55.159><c> under</c><00:02:55.400><c> the</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
and go to the cookbooks folder under the
 

00:02:55.560 --> 00:02:57.430 align:start position:0%
and go to the cookbooks folder under the
cookbooks<00:02:56.000><c> folder</c><00:02:56.319><c> you</c><00:02:56.440><c> can</c><00:02:56.680><c> find</c><00:02:57.000><c> the</c><00:02:57.120><c> code</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
cookbooks folder you can find the code
 

00:02:57.440 --> 00:03:01.149 align:start position:0%
cookbooks folder you can find the code
under<00:02:58.280><c> example</c><00:02:59.280><c> Auto</c><00:02:59.640><c> rap</c><00:03:00.080><c> directory</c><00:03:00.959><c> so</c>

00:03:01.149 --> 00:03:01.159 align:start position:0%
under example Auto rap directory so
 

00:03:01.159 --> 00:03:03.430 align:start position:0%
under example Auto rap directory so
after<00:03:01.319><c> you've</c><00:03:01.519><c> cloned</c><00:03:01.840><c> this</c><00:03:02.360><c> uh</c><00:03:02.440><c> repo</c><00:03:03.120><c> open</c><00:03:03.319><c> it</c>

00:03:03.430 --> 00:03:03.440 align:start position:0%
after you've cloned this uh repo open it
 

00:03:03.440 --> 00:03:05.110 align:start position:0%
after you've cloned this uh repo open it
up<00:03:03.560><c> in</c><00:03:03.640><c> the</c><00:03:03.760><c> code</c><00:03:03.920><c> editor</c><00:03:04.239><c> of</c><00:03:04.319><c> your</c><00:03:04.480><c> choice</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
up in the code editor of your choice
 

00:03:05.120 --> 00:03:07.869 align:start position:0%
up in the code editor of your choice
under<00:03:05.440><c> F</c><00:03:05.680><c> data</c><00:03:06.040><c> cookbook</c><00:03:06.640><c> examples</c><00:03:07.159><c> Auto</c><00:03:07.519><c> Rag</c>

00:03:07.869 --> 00:03:07.879 align:start position:0%
under F data cookbook examples Auto Rag
 

00:03:07.879 --> 00:03:09.750 align:start position:0%
under F data cookbook examples Auto Rag
and<00:03:08.000><c> you'll</c><00:03:08.239><c> find</c><00:03:08.560><c> the</c><00:03:08.879><c> step</c><00:03:09.159><c> bystep</c>

00:03:09.750 --> 00:03:09.760 align:start position:0%
and you'll find the step bystep
 

00:03:09.760 --> 00:03:12.470 align:start position:0%
and you'll find the step bystep
instructions<00:03:10.720><c> to</c><00:03:10.920><c> run</c><00:03:11.159><c> this</c><00:03:11.480><c> application</c>

00:03:12.470 --> 00:03:12.480 align:start position:0%
instructions to run this application
 

00:03:12.480 --> 00:03:14.830 align:start position:0%
instructions to run this application
let's<00:03:12.680><c> do</c><00:03:12.879><c> that</c><00:03:13.120><c> together</c><00:03:13.360><c> as</c><00:03:13.640><c> well</c><00:03:14.640><c> so</c><00:03:14.760><c> I'm</c>

00:03:14.830 --> 00:03:14.840 align:start position:0%
let's do that together as well so I'm
 

00:03:14.840 --> 00:03:16.750 align:start position:0%
let's do that together as well so I'm
going<00:03:14.959><c> to</c><00:03:15.159><c> stop</c><00:03:15.519><c> the</c><00:03:15.680><c> existing</c><00:03:16.000><c> running</c>

00:03:16.750 --> 00:03:16.760 align:start position:0%
going to stop the existing running
 

00:03:16.760 --> 00:03:19.470 align:start position:0%
going to stop the existing running
application<00:03:17.760><c> uh</c><00:03:17.879><c> so</c><00:03:18.040><c> to</c><00:03:18.200><c> run</c><00:03:18.560><c> the</c><00:03:18.840><c> autoag</c>

00:03:19.470 --> 00:03:19.480 align:start position:0%
application uh so to run the autoag
 

00:03:19.480 --> 00:03:22.589 align:start position:0%
application uh so to run the autoag
assistant<00:03:20.440><c> fire</c><00:03:20.720><c> up</c><00:03:20.879><c> your</c><00:03:21.319><c> terminal</c><00:03:22.319><c> and</c>

00:03:22.589 --> 00:03:22.599 align:start position:0%
assistant fire up your terminal and
 

00:03:22.599 --> 00:03:24.350 align:start position:0%
assistant fire up your terminal and
first<00:03:23.000><c> create</c><00:03:23.440><c> a</c><00:03:23.680><c> python</c><00:03:24.040><c> virtual</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
first create a python virtual
 

00:03:24.360 --> 00:03:26.190 align:start position:0%
first create a python virtual
environment<00:03:25.200><c> so</c><00:03:25.400><c> we</c><00:03:25.519><c> create</c><00:03:25.760><c> a</c><00:03:25.879><c> virtual</c>

00:03:26.190 --> 00:03:26.200 align:start position:0%
environment so we create a virtual
 

00:03:26.200 --> 00:03:27.949 align:start position:0%
environment so we create a virtual
environment<00:03:26.640><c> so</c><00:03:26.840><c> the</c><00:03:27.040><c> dependencies</c><00:03:27.799><c> the</c>

00:03:27.949 --> 00:03:27.959 align:start position:0%
environment so the dependencies the
 

00:03:27.959 --> 00:03:30.070 align:start position:0%
environment so the dependencies the
python<00:03:28.280><c> libraries</c><00:03:28.720><c> we</c><00:03:28.840><c> install</c><00:03:29.439><c> uh</c><00:03:29.560><c> they</c>

00:03:30.070 --> 00:03:30.080 align:start position:0%
python libraries we install uh they
 

00:03:30.080 --> 00:03:32.550 align:start position:0%
python libraries we install uh they
isolated<00:03:31.080><c> from</c><00:03:31.280><c> your</c><00:03:31.480><c> system</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
isolated from your system
 

00:03:32.560 --> 00:03:34.550 align:start position:0%
isolated from your system
dependencies<00:03:33.560><c> just</c><00:03:33.760><c> install</c><00:03:34.120><c> the</c><00:03:34.200><c> library</c>

00:03:34.550 --> 00:03:34.560 align:start position:0%
dependencies just install the library
 

00:03:34.560 --> 00:03:37.350 align:start position:0%
dependencies just install the library
then<00:03:34.640><c> you're</c><00:03:34.720><c> going</c><00:03:34.799><c> to</c><00:03:34.920><c> run</c><00:03:35.120><c> PG</c><00:03:35.760><c> Vector</c><00:03:36.760><c> uh</c>

00:03:37.350 --> 00:03:37.360 align:start position:0%
then you're going to run PG Vector uh
 

00:03:37.360 --> 00:03:39.030 align:start position:0%
then you're going to run PG Vector uh
you<00:03:37.480><c> can</c><00:03:37.760><c> run</c><00:03:37.879><c> it</c><00:03:38.000><c> by</c><00:03:38.159><c> the</c><00:03:38.280><c> dockor</c><00:03:38.560><c> Run</c><00:03:38.760><c> command</c>

00:03:39.030 --> 00:03:39.040 align:start position:0%
you can run it by the dockor Run command
 

00:03:39.040 --> 00:03:41.429 align:start position:0%
you can run it by the dockor Run command
or<00:03:39.159><c> the</c><00:03:39.280><c> script</c><00:03:39.760><c> if</c><00:03:39.879><c> you</c><00:03:40.159><c> have</c><00:03:40.400><c> Windows</c><00:03:40.959><c> these</c>

00:03:41.429 --> 00:03:41.439 align:start position:0%
or the script if you have Windows these
 

00:03:41.439 --> 00:03:44.110 align:start position:0%
or the script if you have Windows these
backs<00:03:41.720><c> slashes</c><00:03:42.120><c> won't</c><00:03:42.480><c> work</c><00:03:42.760><c> so</c><00:03:42.879><c> you</c><00:03:43.000><c> need</c><00:03:43.200><c> to</c>

00:03:44.110 --> 00:03:44.120 align:start position:0%
backs slashes won't work so you need to
 

00:03:44.120 --> 00:03:46.910 align:start position:0%
backs slashes won't work so you need to
run<00:03:44.319><c> it</c><00:03:44.560><c> as</c><00:03:45.080><c> one</c><00:03:45.360><c> line</c><00:03:46.239><c> so</c><00:03:46.400><c> for</c><00:03:46.560><c> me</c><00:03:46.720><c> it's</c><00:03:46.840><c> going</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
run it as one line so for me it's going
 

00:03:46.920 --> 00:03:49.030 align:start position:0%
run it as one line so for me it's going
to<00:03:47.080><c> throw</c><00:03:47.280><c> an</c><00:03:47.439><c> error</c><00:03:48.120><c> because</c><00:03:48.400><c> PG</c><00:03:48.680><c> Vector</c><00:03:48.920><c> is</c>

00:03:49.030 --> 00:03:49.040 align:start position:0%
to throw an error because PG Vector is
 

00:03:49.040 --> 00:03:50.789 align:start position:0%
to throw an error because PG Vector is
already<00:03:49.319><c> running</c><00:03:49.840><c> but</c><00:03:50.000><c> if</c><00:03:50.200><c> it's</c><00:03:50.360><c> not</c><00:03:50.519><c> running</c>

00:03:50.789 --> 00:03:50.799 align:start position:0%
already running but if it's not running
 

00:03:50.799 --> 00:03:53.309 align:start position:0%
already running but if it's not running
for<00:03:51.000><c> you</c><00:03:51.200><c> it'll</c><00:03:51.480><c> start</c><00:03:51.840><c> the</c><00:03:52.040><c> PG</c><00:03:52.360><c> Vector</c><00:03:53.200><c> uh</c>

00:03:53.309 --> 00:03:53.319 align:start position:0%
for you it'll start the PG Vector uh
 

00:03:53.319 --> 00:03:55.110 align:start position:0%
for you it'll start the PG Vector uh
database<00:03:53.840><c> in</c><00:03:53.959><c> a</c><00:03:54.159><c> Docker</c>

00:03:55.110 --> 00:03:55.120 align:start position:0%
database in a Docker
 

00:03:55.120 --> 00:03:56.990 align:start position:0%
database in a Docker
container<00:03:56.120><c> then</c><00:03:56.239><c> we're</c><00:03:56.400><c> going</c><00:03:56.519><c> to</c><00:03:56.640><c> run</c><00:03:56.840><c> the</c>

00:03:56.990 --> 00:03:57.000 align:start position:0%
container then we're going to run the
 

00:03:57.000 --> 00:03:58.910 align:start position:0%
container then we're going to run the
streamlet<00:03:57.560><c> application</c><00:03:58.239><c> and</c><00:03:58.439><c> that's</c><00:03:58.640><c> about</c>

00:03:58.910 --> 00:03:58.920 align:start position:0%
streamlet application and that's about
 

00:03:58.920 --> 00:04:02.030 align:start position:0%
streamlet application and that's about
it<00:03:59.959><c> now</c><00:04:00.120><c> let's</c><00:04:00.360><c> ask</c><00:04:00.680><c> again</c><00:04:01.000><c> a</c><00:04:01.239><c> question</c><00:04:01.760><c> from</c>

00:04:02.030 --> 00:04:02.040 align:start position:0%
it now let's ask again a question from
 

00:04:02.040 --> 00:04:04.550 align:start position:0%
it now let's ask again a question from
this<00:04:02.200><c> blog</c><00:04:02.560><c> post</c><00:04:03.120><c> we've</c><00:04:03.360><c> already</c><00:04:03.720><c> loaded</c><00:04:04.319><c> this</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
this blog post we've already loaded this
 

00:04:04.560 --> 00:04:06.589 align:start position:0%
this blog post we've already loaded this
into<00:04:04.920><c> our</c><00:04:05.439><c> uh</c><00:04:05.560><c> knowledge</c><00:04:05.920><c> base</c><00:04:06.200><c> so</c><00:04:06.319><c> we</c><00:04:06.400><c> don't</c>

00:04:06.589 --> 00:04:06.599 align:start position:0%
into our uh knowledge base so we don't
 

00:04:06.599 --> 00:04:09.309 align:start position:0%
into our uh knowledge base so we don't
need<00:04:06.760><c> to</c><00:04:06.879><c> load</c><00:04:07.120><c> it</c><00:04:07.319><c> again</c><00:04:08.000><c> but</c><00:04:08.760><c> we'll</c><00:04:08.959><c> just</c><00:04:09.120><c> go</c>

00:04:09.309 --> 00:04:09.319 align:start position:0%
need to load it again but we'll just go
 

00:04:09.319 --> 00:04:13.789 align:start position:0%
need to load it again but we'll just go
with<00:04:09.519><c> that</c><00:04:10.319><c> and</c><00:04:10.439><c> we'll</c><00:04:10.720><c> ask</c><00:04:11.560><c> uh</c><00:04:12.560><c> tell</c><00:04:12.799><c> me</c><00:04:13.040><c> about</c>

00:04:13.789 --> 00:04:13.799 align:start position:0%
with that and we'll ask uh tell me about
 

00:04:13.799 --> 00:04:17.909 align:start position:0%
with that and we'll ask uh tell me about
the<00:04:14.319><c> Lama</c><00:04:14.760><c> 3</c>

00:04:17.909 --> 00:04:17.919 align:start position:0%
 
 

00:04:17.919 --> 00:04:20.229 align:start position:0%
 
models<00:04:18.919><c> and</c><00:04:19.040><c> it's</c><00:04:19.199><c> going</c><00:04:19.400><c> to</c><00:04:19.799><c> search</c><00:04:20.079><c> the</c>

00:04:20.229 --> 00:04:20.239 align:start position:0%
models and it's going to search the
 

00:04:20.239 --> 00:04:22.909 align:start position:0%
models and it's going to search the
knowledge<00:04:20.519><c> base</c><00:04:20.759><c> for</c><00:04:20.840><c> Lama</c><00:04:21.160><c> 3</c><00:04:21.479><c> models</c><00:04:22.479><c> but</c>

00:04:22.909 --> 00:04:22.919 align:start position:0%
knowledge base for Lama 3 models but
 

00:04:22.919 --> 00:04:24.830 align:start position:0%
knowledge base for Lama 3 models but
there's<00:04:23.160><c> also</c><00:04:23.440><c> one</c><00:04:23.600><c> thing</c><00:04:24.199><c> how</c><00:04:24.400><c> does</c><00:04:24.520><c> it</c><00:04:24.639><c> know</c>

00:04:24.830 --> 00:04:24.840 align:start position:0%
there's also one thing how does it know
 

00:04:24.840 --> 00:04:26.390 align:start position:0%
there's also one thing how does it know
whether<00:04:25.000><c> to</c><00:04:25.120><c> search</c><00:04:25.400><c> the</c><00:04:25.520><c> knowledge</c><00:04:25.840><c> base</c><00:04:26.240><c> or</c>

00:04:26.390 --> 00:04:26.400 align:start position:0%
whether to search the knowledge base or
 

00:04:26.400 --> 00:04:28.430 align:start position:0%
whether to search the knowledge base or
search<00:04:26.720><c> the</c><00:04:26.919><c> web</c><00:04:27.199><c> or</c><00:04:27.320><c> search</c><00:04:27.600><c> the</c><00:04:27.759><c> database</c>

00:04:28.430 --> 00:04:28.440 align:start position:0%
search the web or search the database
 

00:04:28.440 --> 00:04:30.749 align:start position:0%
search the web or search the database
how<00:04:28.600><c> does</c><00:04:28.720><c> it</c><00:04:28.919><c> know</c><00:04:29.560><c> what</c><00:04:29.759><c> what</c><00:04:29.919><c> to</c><00:04:30.199><c> prefer</c>

00:04:30.749 --> 00:04:30.759 align:start position:0%
how does it know what what to prefer
 

00:04:30.759 --> 00:04:33.790 align:start position:0%
how does it know what what to prefer
which<00:04:31.560><c> tool</c><00:04:31.919><c> call</c><00:04:32.160><c> to</c><00:04:32.360><c> take</c><00:04:32.680><c> to</c><00:04:32.880><c> answer</c><00:04:33.160><c> this</c>

00:04:33.790 --> 00:04:33.800 align:start position:0%
which tool call to take to answer this
 

00:04:33.800 --> 00:04:36.150 align:start position:0%
which tool call to take to answer this
question<00:04:34.800><c> this</c>

00:04:36.150 --> 00:04:36.160 align:start position:0%
question this
 

00:04:36.160 --> 00:04:39.710 align:start position:0%
question this
is<00:04:37.160><c> written</c><00:04:37.520><c> up</c><00:04:37.919><c> in</c><00:04:38.240><c> the</c><00:04:38.440><c> F</c><00:04:38.800><c> data</c><00:04:39.120><c> assistant</c>

00:04:39.710 --> 00:04:39.720 align:start position:0%
is written up in the F data assistant
 

00:04:39.720 --> 00:04:41.230 align:start position:0%
is written up in the F data assistant
that<00:04:39.800><c> is</c><00:04:39.960><c> powering</c><00:04:40.400><c> this</c>

00:04:41.230 --> 00:04:41.240 align:start position:0%
that is powering this
 

00:04:41.240 --> 00:04:43.830 align:start position:0%
that is powering this
application<00:04:42.240><c> so</c><00:04:42.440><c> under</c><00:04:42.759><c> this</c><00:04:42.960><c> folder</c><00:04:43.520><c> you'll</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
application so under this folder you'll
 

00:04:43.840 --> 00:04:46.830 align:start position:0%
application so under this folder you'll
have<00:04:44.000><c> an</c><00:04:44.199><c> assistant</c><00:04:44.680><c> and</c><00:04:44.880><c> an</c><00:04:45.160><c> app</c><00:04:46.000><c> the</c><00:04:46.240><c> app</c><00:04:46.639><c> has</c>

00:04:46.830 --> 00:04:46.840 align:start position:0%
have an assistant and an app the app has
 

00:04:46.840 --> 00:04:48.350 align:start position:0%
have an assistant and an app the app has
the<00:04:47.000><c> streamlet</c><00:04:47.600><c> application</c><00:04:48.039><c> which</c><00:04:48.120><c> is</c><00:04:48.240><c> the</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
the streamlet application which is the
 

00:04:48.360 --> 00:04:50.749 align:start position:0%
the streamlet application which is the
front<00:04:48.639><c> end</c><00:04:49.280><c> now</c><00:04:49.440><c> the</c><00:04:49.520><c> streamlet</c>

00:04:50.749 --> 00:04:50.759 align:start position:0%
front end now the streamlet
 

00:04:50.759 --> 00:04:53.230 align:start position:0%
front end now the streamlet
application<00:04:51.759><c> gets</c><00:04:52.120><c> the</c><00:04:52.400><c> assistant</c><00:04:53.000><c> which</c><00:04:53.120><c> is</c>

00:04:53.230 --> 00:04:53.240 align:start position:0%
application gets the assistant which is
 

00:04:53.240 --> 00:04:57.510 align:start position:0%
application gets the assistant which is
under<00:04:53.479><c> the</c><00:04:53.680><c> assistant</c><00:04:54.520><c> file</c><00:04:55.520><c> over</c><00:04:56.280><c> here</c><00:04:57.280><c> so</c>

00:04:57.510 --> 00:04:57.520 align:start position:0%
under the assistant file over here so
 

00:04:57.520 --> 00:05:00.550 align:start position:0%
under the assistant file over here so
this<00:04:57.639><c> is</c><00:04:57.759><c> a</c><00:04:57.919><c> f</c><00:04:58.199><c> data</c><00:04:58.440><c> assistant</c><00:04:59.280><c> that</c><00:04:59.800><c> is</c><00:05:00.160><c> using</c>

00:05:00.550 --> 00:05:00.560 align:start position:0%
this is a f data assistant that is using
 

00:05:00.560 --> 00:05:02.150 align:start position:0%
this is a f data assistant that is using
the<00:05:00.800><c> openi</c><00:05:01.320><c> chat</c>

00:05:02.150 --> 00:05:02.160 align:start position:0%
the openi chat
 

00:05:02.160 --> 00:05:05.830 align:start position:0%
the openi chat
llm<00:05:03.160><c> it's</c><00:05:03.320><c> using</c><00:05:03.759><c> postest</c><00:05:04.320><c> for</c><00:05:04.520><c> storage</c><00:05:05.520><c> and</c>

00:05:05.830 --> 00:05:05.840 align:start position:0%
llm it's using postest for storage and
 

00:05:05.840 --> 00:05:08.710 align:start position:0%
llm it's using postest for storage and
PG<00:05:06.199><c> Vector</c><00:05:06.560><c> for</c><00:05:06.759><c> knowledge</c><00:05:07.360><c> base</c><00:05:08.360><c> and</c><00:05:08.479><c> we're</c>

00:05:08.710 --> 00:05:08.720 align:start position:0%
PG Vector for knowledge base and we're
 

00:05:08.720 --> 00:05:11.830 align:start position:0%
PG Vector for knowledge base and we're
giving<00:05:08.960><c> it</c><00:05:09.240><c> a</c><00:05:09.960><c> description</c><00:05:10.960><c> um</c><00:05:11.400><c> description</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
giving it a description um description
 

00:05:11.840 --> 00:05:13.110 align:start position:0%
giving it a description um description
and<00:05:11.960><c> inst</c><00:05:12.199><c> structures</c><00:05:12.479><c> are</c><00:05:12.639><c> just</c><00:05:12.759><c> a</c><00:05:12.880><c> way</c><00:05:13.000><c> to</c>

00:05:13.110 --> 00:05:13.120 align:start position:0%
and inst structures are just a way to
 

00:05:13.120 --> 00:05:15.150 align:start position:0%
and inst structures are just a way to
format<00:05:13.440><c> the</c><00:05:13.560><c> system</c><00:05:13.880><c> prompt</c><00:05:14.360><c> you</c><00:05:14.479><c> can</c><00:05:14.880><c> write</c>

00:05:15.150 --> 00:05:15.160 align:start position:0%
format the system prompt you can write
 

00:05:15.160 --> 00:05:18.309 align:start position:0%
format the system prompt you can write
the<00:05:15.360><c> system</c><00:05:15.759><c> prompt</c><00:05:16.720><c> yourself</c><00:05:17.720><c> using</c><00:05:18.120><c> the</c>

00:05:18.309 --> 00:05:18.319 align:start position:0%
the system prompt yourself using the
 

00:05:18.319 --> 00:05:21.230 align:start position:0%
the system prompt yourself using the
system<00:05:18.720><c> prompt</c><00:05:19.440><c> uh</c><00:05:19.560><c> parameter</c><00:05:20.560><c> but</c><00:05:20.800><c> we</c><00:05:21.000><c> just</c>

00:05:21.230 --> 00:05:21.240 align:start position:0%
system prompt uh parameter but we just
 

00:05:21.240 --> 00:05:23.029 align:start position:0%
system prompt uh parameter but we just
like<00:05:21.440><c> to</c><00:05:21.600><c> use</c><00:05:21.840><c> description</c><00:05:22.240><c> and</c><00:05:22.440><c> instructions</c>

00:05:23.029 --> 00:05:23.039 align:start position:0%
like to use description and instructions
 

00:05:23.039 --> 00:05:25.550 align:start position:0%
like to use description and instructions
because<00:05:23.280><c> it</c><00:05:23.800><c> comes</c><00:05:24.000><c> out</c><00:05:24.199><c> neatly</c><00:05:24.560><c> formatted</c><00:05:25.400><c> so</c>

00:05:25.550 --> 00:05:25.560 align:start position:0%
because it comes out neatly formatted so
 

00:05:25.560 --> 00:05:26.870 align:start position:0%
because it comes out neatly formatted so
the<00:05:25.720><c> description</c><00:05:26.120><c> is</c><00:05:26.240><c> you're</c><00:05:26.440><c> a</c><00:05:26.520><c> helpful</c>

00:05:26.870 --> 00:05:26.880 align:start position:0%
the description is you're a helpful
 

00:05:26.880 --> 00:05:28.670 align:start position:0%
the description is you're a helpful
assistant<00:05:27.319><c> called</c><00:05:27.560><c> autoag</c><00:05:28.160><c> and</c><00:05:28.240><c> your</c><00:05:28.400><c> goal</c><00:05:28.560><c> is</c>

00:05:28.670 --> 00:05:28.680 align:start position:0%
assistant called autoag and your goal is
 

00:05:28.680 --> 00:05:30.749 align:start position:0%
assistant called autoag and your goal is
to<00:05:28.960><c> assist</c><00:05:29.240><c> the</c><00:05:29.319><c> user</c><00:05:29.800><c> in</c><00:05:29.880><c> the</c><00:05:29.960><c> best</c><00:05:30.199><c> way</c>

00:05:30.749 --> 00:05:30.759 align:start position:0%
to assist the user in the best way
 

00:05:30.759 --> 00:05:33.870 align:start position:0%
to assist the user in the best way
possible<00:05:31.759><c> the</c><00:05:31.919><c> instructions</c><00:05:32.560><c> are</c><00:05:32.880><c> where</c><00:05:33.160><c> we</c>

00:05:33.870 --> 00:05:33.880 align:start position:0%
possible the instructions are where we
 

00:05:33.880 --> 00:05:37.230 align:start position:0%
possible the instructions are where we
teach<00:05:34.319><c> it</c><00:05:35.160><c> how</c><00:05:35.400><c> to</c><00:05:35.680><c> answer</c><00:05:35.960><c> a</c><00:05:36.120><c> user's</c><00:05:36.560><c> question</c>

00:05:37.230 --> 00:05:37.240 align:start position:0%
teach it how to answer a user's question
 

00:05:37.240 --> 00:05:39.870 align:start position:0%
teach it how to answer a user's question
so<00:05:37.440><c> given</c><00:05:37.680><c> a</c><00:05:37.800><c> user</c><00:05:38.039><c> query</c><00:05:38.880><c> first</c><00:05:39.479><c> always</c>

00:05:39.870 --> 00:05:39.880 align:start position:0%
so given a user query first always
 

00:05:39.880 --> 00:05:41.510 align:start position:0%
so given a user query first always
search<00:05:40.319><c> your</c><00:05:40.520><c> knowledge</c><00:05:40.840><c> base</c><00:05:41.120><c> using</c><00:05:41.400><c> the</c>

00:05:41.510 --> 00:05:41.520 align:start position:0%
search your knowledge base using the
 

00:05:41.520 --> 00:05:43.629 align:start position:0%
search your knowledge base using the
search<00:05:41.840><c> knowledge</c><00:05:42.160><c> base</c><00:05:42.400><c> tool</c><00:05:43.160><c> now</c><00:05:43.360><c> this</c><00:05:43.479><c> is</c>

00:05:43.629 --> 00:05:43.639 align:start position:0%
search knowledge base tool now this is
 

00:05:43.639 --> 00:05:45.309 align:start position:0%
search knowledge base tool now this is
where<00:05:43.759><c> we're</c><00:05:43.960><c> telling</c><00:05:44.720><c> hey</c><00:05:44.840><c> you</c><00:05:44.960><c> need</c><00:05:45.120><c> to</c>

00:05:45.309 --> 00:05:45.319 align:start position:0%
where we're telling hey you need to
 

00:05:45.319 --> 00:05:47.110 align:start position:0%
where we're telling hey you need to
always<00:05:45.600><c> search</c><00:05:45.800><c> your</c><00:05:45.960><c> knowledge</c><00:05:46.199><c> base</c><00:05:46.520><c> first</c>

00:05:47.110 --> 00:05:47.120 align:start position:0%
always search your knowledge base first
 

00:05:47.120 --> 00:05:49.189 align:start position:0%
always search your knowledge base first
because<00:05:47.440><c> we</c><00:05:47.639><c> wanted</c><00:05:48.039><c> to</c><00:05:48.400><c> prefer</c><00:05:48.960><c> the</c>

00:05:49.189 --> 00:05:49.199 align:start position:0%
because we wanted to prefer the
 

00:05:49.199 --> 00:05:51.670 align:start position:0%
because we wanted to prefer the
information<00:05:50.000><c> that</c><00:05:50.199><c> we</c><00:05:50.520><c> provided</c><00:05:51.000><c> it</c><00:05:51.360><c> before</c>

00:05:51.670 --> 00:05:51.680 align:start position:0%
information that we provided it before
 

00:05:51.680 --> 00:05:53.230 align:start position:0%
information that we provided it before
going<00:05:51.919><c> to</c><00:05:52.120><c> the</c>

00:05:53.230 --> 00:05:53.240 align:start position:0%
going to the
 

00:05:53.240 --> 00:05:55.350 align:start position:0%
going to the
internet<00:05:54.240><c> this</c><00:05:54.400><c> search</c><00:05:54.759><c> knowledge</c><00:05:55.080><c> based</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
internet this search knowledge based
 

00:05:55.360 --> 00:05:58.950 align:start position:0%
internet this search knowledge based
tool<00:05:56.080><c> is</c><00:05:56.479><c> added</c><00:05:57.479><c> by</c><00:05:57.840><c> setting</c><00:05:58.280><c> this</c><00:05:58.479><c> search</c>

00:05:58.950 --> 00:05:58.960 align:start position:0%
tool is added by setting this search
 

00:05:58.960 --> 00:06:00.790 align:start position:0%
tool is added by setting this search
knowledge<00:05:59.400><c> par</c><00:05:59.600><c> parameter</c><00:05:59.919><c> as</c><00:06:00.120><c> through</c><00:06:00.600><c> so</c>

00:06:00.790 --> 00:06:00.800 align:start position:0%
knowledge par parameter as through so
 

00:06:00.800 --> 00:06:02.670 align:start position:0%
knowledge par parameter as through so
this<00:06:00.880><c> is</c><00:06:01.000><c> a</c><00:06:01.240><c> default</c><00:06:01.639><c> five</c><00:06:01.919><c> data</c><00:06:02.120><c> tool</c><00:06:02.400><c> that</c><00:06:02.520><c> is</c>

00:06:02.670 --> 00:06:02.680 align:start position:0%
this is a default five data tool that is
 

00:06:02.680 --> 00:06:04.590 align:start position:0%
this is a default five data tool that is
available<00:06:03.039><c> to</c><00:06:03.319><c> all</c><00:06:03.639><c> assistants</c><00:06:04.280><c> when</c><00:06:04.400><c> you</c>

00:06:04.590 --> 00:06:04.600 align:start position:0%
available to all assistants when you
 

00:06:04.600 --> 00:06:06.589 align:start position:0%
available to all assistants when you
provide<00:06:04.960><c> them</c><00:06:05.080><c> a</c><00:06:05.199><c> knowledge</c><00:06:05.520><c> base</c><00:06:06.280><c> you</c><00:06:06.400><c> can</c>

00:06:06.589 --> 00:06:06.599 align:start position:0%
provide them a knowledge base you can
 

00:06:06.599 --> 00:06:08.870 align:start position:0%
provide them a knowledge base you can
search<00:06:07.199><c> this</c><00:06:07.479><c> variable</c><00:06:07.880><c> as</c><00:06:08.039><c> true</c><00:06:08.319><c> and</c><00:06:08.520><c> it</c><00:06:08.599><c> will</c>

00:06:08.870 --> 00:06:08.880 align:start position:0%
search this variable as true and it will
 

00:06:08.880 --> 00:06:12.350 align:start position:0%
search this variable as true and it will
give<00:06:09.039><c> it</c><00:06:09.319><c> give</c><00:06:09.479><c> the</c><00:06:09.639><c> assistant</c><00:06:10.280><c> a</c><00:06:11.280><c> um</c><00:06:11.680><c> a</c><00:06:11.880><c> tool</c>

00:06:12.350 --> 00:06:12.360 align:start position:0%
give it give the assistant a um a tool
 

00:06:12.360 --> 00:06:14.670 align:start position:0%
give it give the assistant a um a tool
to<00:06:12.599><c> search</c><00:06:12.880><c> its</c><00:06:13.039><c> knowledge</c><00:06:13.360><c> base</c><00:06:14.319><c> then</c><00:06:14.479><c> we're</c>

00:06:14.670 --> 00:06:14.680 align:start position:0%
to search its knowledge base then we're
 

00:06:14.680 --> 00:06:16.510 align:start position:0%
to search its knowledge base then we're
saying<00:06:15.080><c> if</c><00:06:15.160><c> you</c><00:06:15.360><c> don't</c><00:06:15.680><c> find</c><00:06:15.880><c> the</c><00:06:16.039><c> relevant</c>

00:06:16.510 --> 00:06:16.520 align:start position:0%
saying if you don't find the relevant
 

00:06:16.520 --> 00:06:19.350 align:start position:0%
saying if you don't find the relevant
information<00:06:17.199><c> use</c><00:06:17.400><c> the</c><00:06:17.560><c> duct</c><00:06:18.000><c> Co</c><00:06:18.160><c> Search</c><00:06:18.400><c> tool</c>

00:06:19.350 --> 00:06:19.360 align:start position:0%
information use the duct Co Search tool
 

00:06:19.360 --> 00:06:21.350 align:start position:0%
information use the duct Co Search tool
so<00:06:19.560><c> the</c><00:06:19.680><c> duct</c><00:06:20.080><c> Co</c><00:06:20.240><c> Search</c><00:06:20.440><c> tool</c><00:06:20.680><c> will</c><00:06:20.840><c> let</c><00:06:21.000><c> it</c>

00:06:21.350 --> 00:06:21.360 align:start position:0%
so the duct Co Search tool will let it
 

00:06:21.360 --> 00:06:23.150 align:start position:0%
so the duct Co Search tool will let it
search<00:06:21.720><c> the</c><00:06:21.960><c> internet</c><00:06:22.360><c> for</c><00:06:22.560><c> that</c><00:06:22.720><c> information</c>

00:06:23.150 --> 00:06:23.160 align:start position:0%
search the internet for that information
 

00:06:23.160 --> 00:06:25.110 align:start position:0%
search the internet for that information
so<00:06:23.319><c> if</c><00:06:23.400><c> you</c><00:06:23.639><c> were</c><00:06:23.800><c> to</c><00:06:24.039><c> ask</c><00:06:24.639><c> what's</c><00:06:24.840><c> happening</c>

00:06:25.110 --> 00:06:25.120 align:start position:0%
so if you were to ask what's happening
 

00:06:25.120 --> 00:06:27.270 align:start position:0%
so if you were to ask what's happening
in<00:06:25.240><c> France</c><00:06:25.560><c> or</c><00:06:26.080><c> tell</c><00:06:26.240><c> me</c><00:06:26.400><c> the</c><00:06:26.560><c> latest</c><00:06:26.960><c> news</c>

00:06:27.270 --> 00:06:27.280 align:start position:0%
in France or tell me the latest news
 

00:06:27.280 --> 00:06:30.430 align:start position:0%
in France or tell me the latest news
about<00:06:27.560><c> meta</c><00:06:28.000><c> AI</c><00:06:29.000><c> then</c><00:06:29.199><c> it's</c><00:06:29.759><c> to</c><00:06:29.960><c> search</c><00:06:30.319><c> the</c>

00:06:30.430 --> 00:06:30.440 align:start position:0%
about meta AI then it's to search the
 

00:06:30.440 --> 00:06:31.950 align:start position:0%
about meta AI then it's to search the
internet<00:06:31.000><c> because</c><00:06:31.199><c> it</c><00:06:31.319><c> doesn't</c><00:06:31.639><c> have</c><00:06:31.800><c> that</c>

00:06:31.950 --> 00:06:31.960 align:start position:0%
internet because it doesn't have that
 

00:06:31.960 --> 00:06:34.110 align:start position:0%
internet because it doesn't have that
information<00:06:32.400><c> its</c><00:06:32.520><c> knowledge</c><00:06:32.840><c> base</c><00:06:33.599><c> so</c><00:06:33.880><c> this</c>

00:06:34.110 --> 00:06:34.120 align:start position:0%
information its knowledge base so this
 

00:06:34.120 --> 00:06:37.430 align:start position:0%
information its knowledge base so this
is<00:06:34.280><c> a</c><00:06:34.520><c> custom</c><00:06:34.880><c> tool</c><00:06:35.280><c> that</c><00:06:35.440><c> we</c><00:06:35.680><c> provide</c><00:06:36.440><c> using</c>

00:06:37.430 --> 00:06:37.440 align:start position:0%
is a custom tool that we provide using
 

00:06:37.440 --> 00:06:39.670 align:start position:0%
is a custom tool that we provide using
um<00:06:37.840><c> we</c><00:06:38.039><c> provide</c><00:06:38.319><c> to</c><00:06:38.479><c> the</c><00:06:38.639><c> assistant</c>

00:06:39.670 --> 00:06:39.680 align:start position:0%
um we provide to the assistant
 

00:06:39.680 --> 00:06:41.830 align:start position:0%
um we provide to the assistant
separately<00:06:40.680><c> so</c><00:06:40.880><c> over</c><00:06:41.120><c> here</c><00:06:41.319><c> we're</c><00:06:41.440><c> going</c><00:06:41.560><c> to</c>

00:06:41.830 --> 00:06:41.840 align:start position:0%
separately so over here we're going to
 

00:06:41.840 --> 00:06:45.469 align:start position:0%
separately so over here we're going to
import<00:06:42.599><c> from</c><00:06:42.960><c> F</c><00:06:43.479><c> tools</c>

00:06:45.469 --> 00:06:45.479 align:start position:0%
import from F tools
 

00:06:45.479 --> 00:06:47.550 align:start position:0%
import from F tools
drco<00:06:46.479><c> and</c><00:06:46.599><c> then</c><00:06:46.720><c> we're</c><00:06:46.840><c> going</c><00:06:46.960><c> to</c><00:06:47.120><c> add</c><00:06:47.280><c> it</c><00:06:47.400><c> to</c>

00:06:47.550 --> 00:06:47.560 align:start position:0%
drco and then we're going to add it to
 

00:06:47.560 --> 00:06:50.390 align:start position:0%
drco and then we're going to add it to
the<00:06:47.720><c> assistant</c><00:06:48.479><c> F</c><00:06:48.759><c> data</c><00:06:49.120><c> has</c><00:06:49.599><c> many</c><00:06:49.880><c> many</c><00:06:50.160><c> many</c>

00:06:50.390 --> 00:06:50.400 align:start position:0%
the assistant F data has many many many
 

00:06:50.400 --> 00:06:52.430 align:start position:0%
the assistant F data has many many many
tools<00:06:50.800><c> that</c><00:06:50.919><c> you</c><00:06:51.039><c> can</c><00:06:51.280><c> use</c><00:06:51.720><c> from</c><00:06:52.080><c> so</c><00:06:52.240><c> you</c><00:06:52.319><c> can</c>

00:06:52.430 --> 00:06:52.440 align:start position:0%
tools that you can use from so you can
 

00:06:52.440 --> 00:06:55.710 align:start position:0%
tools that you can use from so you can
do<00:06:52.639><c> from</c><00:06:52.840><c> five</c><00:06:53.319><c> tools</c><00:06:54.319><c> uh</c><00:06:54.440><c> you</c>

00:06:55.710 --> 00:06:55.720 align:start position:0%
do from five tools uh you
 

00:06:55.720 --> 00:06:58.710 align:start position:0%
do from five tools uh you
can<00:06:56.720><c> um</c><00:06:57.199><c> this</c><00:06:57.319><c> is</c><00:06:57.560><c> actually</c><00:06:57.879><c> not</c><00:06:58.319><c> autoc</c><00:06:58.639><c> comp</c>

00:06:58.710 --> 00:06:58.720 align:start position:0%
can um this is actually not autoc comp
 

00:06:58.720 --> 00:07:00.749 align:start position:0%
can um this is actually not autoc comp
completing<00:06:59.000><c> so</c><00:06:59.120><c> I'll</c><00:06:59.240><c> show</c><00:06:59.400><c> that</c><00:06:59.680><c> to</c><00:06:59.759><c> you</c>

00:07:00.749 --> 00:07:00.759 align:start position:0%
completing so I'll show that to you
 

00:07:00.759 --> 00:07:03.150 align:start position:0%
completing so I'll show that to you
later<00:07:01.759><c> and</c><00:07:01.879><c> then</c><00:07:02.120><c> finally</c><00:07:02.639><c> we're</c><00:07:02.840><c> going</c><00:07:02.919><c> to</c>

00:07:03.150 --> 00:07:03.160 align:start position:0%
later and then finally we're going to
 

00:07:03.160 --> 00:07:04.990 align:start position:0%
later and then finally we're going to
ask<00:07:03.560><c> if</c><00:07:03.639><c> you</c><00:07:03.759><c> need</c><00:07:03.919><c> to</c><00:07:04.160><c> reference</c><00:07:04.520><c> the</c><00:07:04.680><c> chat</c>

00:07:04.990 --> 00:07:05.000 align:start position:0%
ask if you need to reference the chat
 

00:07:05.000 --> 00:07:07.909 align:start position:0%
ask if you need to reference the chat
history<00:07:05.759><c> use</c><00:07:06.120><c> the</c><00:07:06.560><c> chat</c><00:07:07.039><c> read</c><00:07:07.360><c> chat</c><00:07:07.639><c> history</c>

00:07:07.909 --> 00:07:07.919 align:start position:0%
history use the chat read chat history
 

00:07:07.919 --> 00:07:11.230 align:start position:0%
history use the chat read chat history
tool<00:07:08.639><c> this</c><00:07:08.759><c> is</c><00:07:09.000><c> also</c><00:07:09.280><c> a</c><00:07:09.520><c> default</c><00:07:10.000><c> tool</c><00:07:10.680><c> that</c><00:07:10.960><c> is</c>

00:07:11.230 --> 00:07:11.240 align:start position:0%
tool this is also a default tool that is
 

00:07:11.240 --> 00:07:14.270 align:start position:0%
tool this is also a default tool that is
provided<00:07:12.000><c> by</c><00:07:12.199><c> the</c><00:07:12.360><c> read</c><00:07:12.759><c> chat</c><00:07:13.039><c> history</c><00:07:13.919><c> equal</c>

00:07:14.270 --> 00:07:14.280 align:start position:0%
provided by the read chat history equal
 

00:07:14.280 --> 00:07:17.070 align:start position:0%
provided by the read chat history equal
to<00:07:14.520><c> True</c><00:07:15.199><c> parameter</c><00:07:16.199><c> now</c><00:07:16.360><c> if</c><00:07:16.440><c> you</c><00:07:16.560><c> want</c><00:07:16.680><c> to</c><00:07:16.800><c> see</c>

00:07:17.070 --> 00:07:17.080 align:start position:0%
to True parameter now if you want to see
 

00:07:17.080 --> 00:07:19.469 align:start position:0%
to True parameter now if you want to see
which<00:07:17.199><c> all</c><00:07:17.479><c> tools</c><00:07:17.800><c> that</c><00:07:17.960><c> are</c><00:07:18.360><c> available</c><00:07:19.360><c> you</c>

00:07:19.469 --> 00:07:19.479 align:start position:0%
which all tools that are available you
 

00:07:19.479 --> 00:07:21.710 align:start position:0%
which all tools that are available you
can<00:07:19.680><c> go</c><00:07:19.840><c> to</c><00:07:19.960><c> the</c><00:07:20.080><c> FI</c><00:07:20.400><c> data</c><00:07:20.680><c> documentation</c><00:07:21.560><c> and</c>

00:07:21.710 --> 00:07:21.720 align:start position:0%
can go to the FI data documentation and
 

00:07:21.720 --> 00:07:25.029 align:start position:0%
can go to the FI data documentation and
see<00:07:22.280><c> all</c><00:07:22.479><c> the</c><00:07:22.879><c> tools</c><00:07:23.879><c> that</c><00:07:24.039><c> are</c><00:07:24.360><c> available</c><00:07:24.720><c> for</c>

00:07:25.029 --> 00:07:25.039 align:start position:0%
see all the tools that are available for
 

00:07:25.039 --> 00:07:26.710 align:start position:0%
see all the tools that are available for
assistant<00:07:25.840><c> we're</c><00:07:26.000><c> trying</c><00:07:26.240><c> to</c><00:07:26.360><c> keep</c><00:07:26.520><c> the</c>

00:07:26.710 --> 00:07:26.720 align:start position:0%
assistant we're trying to keep the
 

00:07:26.720 --> 00:07:28.350 align:start position:0%
assistant we're trying to keep the
documentation<00:07:27.319><c> updated</c><00:07:27.800><c> but</c><00:07:27.919><c> if</c><00:07:28.000><c> you</c><00:07:28.080><c> want</c><00:07:28.199><c> to</c>

00:07:28.350 --> 00:07:28.360 align:start position:0%
documentation updated but if you want to
 

00:07:28.360 --> 00:07:31.070 align:start position:0%
documentation updated but if you want to
see<00:07:28.680><c> the</c><00:07:28.879><c> tools</c><00:07:29.199><c> that</c><00:07:29.319><c> are</c><00:07:29.680><c> not</c><00:07:29.919><c> in</c><00:07:30.080><c> the</c>

00:07:31.070 --> 00:07:31.080 align:start position:0%
see the tools that are not in the
 

00:07:31.080 --> 00:07:32.990 align:start position:0%
see the tools that are not in the
documentation<00:07:32.080><c> you</c><00:07:32.199><c> can</c><00:07:32.400><c> actually</c><00:07:32.680><c> just</c><00:07:32.840><c> go</c>

00:07:32.990 --> 00:07:33.000 align:start position:0%
documentation you can actually just go
 

00:07:33.000 --> 00:07:35.790 align:start position:0%
documentation you can actually just go
to<00:07:33.160><c> the</c><00:07:33.400><c> F</c><00:07:33.680><c> data</c><00:07:33.879><c> repo</c><00:07:34.240><c> and</c><00:07:34.360><c> check</c><00:07:34.560><c> them</c><00:07:34.800><c> out</c>

00:07:35.790 --> 00:07:35.800 align:start position:0%
to the F data repo and check them out
 

00:07:35.800 --> 00:07:37.430 align:start position:0%
to the F data repo and check them out
from<00:07:36.000><c> F</c>

00:07:37.430 --> 00:07:37.440 align:start position:0%
from F
 

00:07:37.440 --> 00:07:42.230 align:start position:0%
from F
data<00:07:38.759><c> F</c><00:07:39.960><c> tools</c><00:07:40.960><c> and</c><00:07:41.120><c> over</c><00:07:41.400><c> here</c><00:07:41.560><c> you</c><00:07:41.680><c> can</c><00:07:41.840><c> see</c>

00:07:42.230 --> 00:07:42.240 align:start position:0%
data F tools and over here you can see
 

00:07:42.240 --> 00:07:44.309 align:start position:0%
data F tools and over here you can see
all<00:07:42.440><c> the</c><00:07:42.560><c> tools</c><00:07:42.879><c> that</c><00:07:43.000><c> are</c><00:07:43.120><c> available</c><00:07:44.120><c> I</c><00:07:44.199><c> want</c>

00:07:44.309 --> 00:07:44.319 align:start position:0%
all the tools that are available I want
 

00:07:44.319 --> 00:07:46.070 align:start position:0%
all the tools that are available I want
to<00:07:44.440><c> make</c><00:07:44.560><c> it</c><00:07:44.680><c> a</c><00:07:44.879><c> point</c><00:07:45.120><c> to</c><00:07:45.520><c> update</c><00:07:45.879><c> the</c>

00:07:46.070 --> 00:07:46.080 align:start position:0%
to make it a point to update the
 

00:07:46.080 --> 00:07:47.670 align:start position:0%
to make it a point to update the
documentation<00:07:46.680><c> with</c><00:07:46.840><c> all</c><00:07:46.960><c> the</c><00:07:47.080><c> new</c><00:07:47.240><c> tools</c><00:07:47.560><c> we</c>

00:07:47.670 --> 00:07:47.680 align:start position:0%
documentation with all the new tools we
 

00:07:47.680 --> 00:07:49.830 align:start position:0%
documentation with all the new tools we
keep<00:07:47.840><c> on</c><00:07:48.000><c> adding</c><00:07:48.319><c> every</c><00:07:48.520><c> day</c><00:07:49.199><c> so</c><00:07:49.400><c> coming</c><00:07:49.720><c> back</c>

00:07:49.830 --> 00:07:49.840 align:start position:0%
keep on adding every day so coming back
 

00:07:49.840 --> 00:07:52.749 align:start position:0%
keep on adding every day so coming back
to<00:07:49.960><c> our</c><00:07:50.479><c> application</c><00:07:51.479><c> so</c><00:07:51.800><c> once</c><00:07:52.000><c> we</c><00:07:52.240><c> built</c><00:07:52.520><c> out</c>

00:07:52.749 --> 00:07:52.759 align:start position:0%
to our application so once we built out
 

00:07:52.759 --> 00:07:54.510 align:start position:0%
to our application so once we built out
this<00:07:53.000><c> assistant</c><00:07:53.599><c> we</c><00:07:53.720><c> give</c><00:07:53.840><c> it</c><00:07:53.960><c> the</c><00:07:54.080><c> tools</c><00:07:54.319><c> it</c>

00:07:54.510 --> 00:07:54.520 align:start position:0%
this assistant we give it the tools it
 

00:07:54.520 --> 00:07:56.629 align:start position:0%
this assistant we give it the tools it
needs<00:07:55.159><c> we</c><00:07:55.319><c> give</c><00:07:55.479><c> it</c><00:07:55.680><c> some</c><00:07:55.960><c> additional</c>

00:07:56.629 --> 00:07:56.639 align:start position:0%
needs we give it some additional
 

00:07:56.639 --> 00:07:59.790 align:start position:0%
needs we give it some additional
information<00:07:57.639><c> uh</c><00:07:57.840><c> like</c><00:07:58.599><c> answer</c><00:07:59.199><c> resp</c><00:07:59.680><c> in</c>

00:07:59.790 --> 00:07:59.800 align:start position:0%
information uh like answer resp in
 

00:07:59.800 --> 00:08:02.749 align:start position:0%
information uh like answer resp in
markdown<00:08:00.680><c> format</c><00:08:01.599><c> add</c><00:08:01.800><c> some</c><00:08:02.000><c> chat</c><00:08:02.319><c> history</c><00:08:02.639><c> to</c>

00:08:02.749 --> 00:08:02.759 align:start position:0%
markdown format add some chat history to
 

00:08:02.759 --> 00:08:05.710 align:start position:0%
markdown format add some chat history to
the<00:08:03.000><c> messages</c><00:08:04.000><c> uh</c><00:08:04.120><c> we</c><00:08:04.199><c> can</c><00:08:04.400><c> tune</c><00:08:04.720><c> this</c><00:08:04.960><c> by</c><00:08:05.360><c> num</c>

00:08:05.710 --> 00:08:05.720 align:start position:0%
the messages uh we can tune this by num
 

00:08:05.720 --> 00:08:07.270 align:start position:0%
the messages uh we can tune this by num
history<00:08:06.039><c> messages</c><00:08:06.479><c> we</c><00:08:06.560><c> can</c><00:08:06.680><c> say</c><00:08:06.879><c> add</c><00:08:07.039><c> four</c>

00:08:07.270 --> 00:08:07.280 align:start position:0%
history messages we can say add four
 

00:08:07.280 --> 00:08:09.550 align:start position:0%
history messages we can say add four
messages<00:08:07.720><c> three</c><00:08:08.000><c> messages</c><00:08:08.440><c> or</c><00:08:08.879><c> none</c><00:08:09.120><c> at</c><00:08:09.280><c> all</c>

00:08:09.550 --> 00:08:09.560 align:start position:0%
messages three messages or none at all
 

00:08:09.560 --> 00:08:12.070 align:start position:0%
messages three messages or none at all
that's<00:08:09.800><c> fine</c><00:08:10.199><c> too</c><00:08:11.199><c> and</c><00:08:11.400><c> that's</c><00:08:11.560><c> how</c><00:08:11.680><c> we</c><00:08:11.800><c> answer</c>

00:08:12.070 --> 00:08:12.080 align:start position:0%
that's fine too and that's how we answer
 

00:08:12.080 --> 00:08:14.869 align:start position:0%
that's fine too and that's how we answer
our<00:08:12.280><c> questions</c><00:08:12.759><c> so</c><00:08:12.960><c> let's</c><00:08:13.280><c> ask</c>

00:08:14.869 --> 00:08:14.879 align:start position:0%
our questions so let's ask
 

00:08:14.879 --> 00:08:18.790 align:start position:0%
our questions so let's ask
um<00:08:15.879><c> tell</c><00:08:16.080><c> me</c><00:08:16.280><c> the</c><00:08:16.680><c> latest</c>

00:08:18.790 --> 00:08:18.800 align:start position:0%
um tell me the latest
 

00:08:18.800 --> 00:08:22.869 align:start position:0%
um tell me the latest
news<00:08:19.800><c> about</c><00:08:20.360><c> meta</c><00:08:20.759><c> AI</c><00:08:21.599><c> so</c><00:08:21.759><c> in</c><00:08:21.960><c> this</c><00:08:22.199><c> case</c><00:08:22.680><c> we</c>

00:08:22.869 --> 00:08:22.879 align:start position:0%
news about meta AI so in this case we
 

00:08:22.879 --> 00:08:25.469 align:start position:0%
news about meta AI so in this case we
needed<00:08:23.319><c> to</c><00:08:23.800><c> search</c><00:08:24.199><c> the</c><00:08:24.400><c> news</c><00:08:24.919><c> inside</c><00:08:25.199><c> of</c><00:08:25.319><c> its</c>

00:08:25.469 --> 00:08:25.479 align:start position:0%
needed to search the news inside of its
 

00:08:25.479 --> 00:08:27.550 align:start position:0%
needed to search the news inside of its
knowledge<00:08:25.800><c> base</c><00:08:26.440><c> which</c><00:08:26.599><c> it</c><00:08:26.919><c> already</c><00:08:27.240><c> knows</c>

00:08:27.550 --> 00:08:27.560 align:start position:0%
knowledge base which it already knows
 

00:08:27.560 --> 00:08:30.830 align:start position:0%
knowledge base which it already knows
how<00:08:27.720><c> to</c><00:08:28.240><c> do</c><00:08:29.240><c> so</c><00:08:29.520><c> so</c><00:08:29.680><c> this</c><00:08:29.759><c> is</c><00:08:29.960><c> the</c><00:08:30.120><c> autoag</c>

00:08:30.830 --> 00:08:30.840 align:start position:0%
how to do so so this is the autoag
 

00:08:30.840 --> 00:08:32.909 align:start position:0%
how to do so so this is the autoag
assistant<00:08:31.599><c> which</c><00:08:31.800><c> depending</c><00:08:32.159><c> on</c><00:08:32.320><c> the</c><00:08:32.440><c> user's</c>

00:08:32.909 --> 00:08:32.919 align:start position:0%
assistant which depending on the user's
 

00:08:32.919 --> 00:08:35.829 align:start position:0%
assistant which depending on the user's
query<00:08:33.719><c> decides</c><00:08:34.360><c> how</c><00:08:34.519><c> to</c><00:08:34.760><c> answer</c><00:08:35.120><c> the</c><00:08:35.320><c> question</c>

00:08:35.829 --> 00:08:35.839 align:start position:0%
query decides how to answer the question
 

00:08:35.839 --> 00:08:37.550 align:start position:0%
query decides how to answer the question
either<00:08:36.039><c> from</c><00:08:36.240><c> his</c><00:08:36.440><c> memory</c><00:08:36.959><c> knowledge</c><00:08:37.360><c> or</c>

00:08:37.550 --> 00:08:37.560 align:start position:0%
either from his memory knowledge or
 

00:08:37.560 --> 00:08:39.709 align:start position:0%
either from his memory knowledge or
tools<00:08:38.360><c> I</c><00:08:38.479><c> hope</c><00:08:38.599><c> you</c><00:08:38.760><c> enjoyed</c><00:08:39.120><c> this</c><00:08:39.279><c> video</c><00:08:39.640><c> if</c>

00:08:39.709 --> 00:08:39.719 align:start position:0%
tools I hope you enjoyed this video if
 

00:08:39.719 --> 00:08:41.630 align:start position:0%
tools I hope you enjoyed this video if
you<00:08:39.880><c> have</c><00:08:40.039><c> any</c><00:08:40.320><c> questions</c><00:08:40.880><c> make</c><00:08:41.120><c> a</c><00:08:41.279><c> GitHub</c>

00:08:41.630 --> 00:08:41.640 align:start position:0%
you have any questions make a GitHub
 

00:08:41.640 --> 00:08:44.670 align:start position:0%
you have any questions make a GitHub
issue<00:08:42.000><c> drop</c><00:08:42.320><c> by</c><00:08:42.479><c> in</c><00:08:42.599><c> the</c><00:08:42.760><c> Discord</c><00:08:43.680><c> and</c><00:08:44.360><c> have</c><00:08:44.519><c> a</c>

00:08:44.670 --> 00:08:44.680 align:start position:0%
issue drop by in the Discord and have a
 

00:08:44.680 --> 00:08:47.200 align:start position:0%
issue drop by in the Discord and have a
great<00:08:44.920><c> day</c>

