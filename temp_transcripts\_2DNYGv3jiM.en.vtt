WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.969 align:start position:0%
 
the<00:00:00.359><c> weights</c><00:00:00.719><c> and</c><00:00:00.780><c> biases</c><00:00:01.140><c> platform</c><00:00:01.560><c> has</c>

00:00:01.969 --> 00:00:01.979 align:start position:0%
the weights and biases platform has
 

00:00:01.979 --> 00:00:04.130 align:start position:0%
the weights and biases platform has
evolved<00:00:02.399><c> into</c><00:00:02.520><c> so</c><00:00:02.820><c> much</c><00:00:02.940><c> more</c><00:00:03.179><c> than</c><00:00:03.780><c> just</c>

00:00:04.130 --> 00:00:04.140 align:start position:0%
evolved into so much more than just
 

00:00:04.140 --> 00:00:06.289 align:start position:0%
evolved into so much more than just
experiment<00:00:04.620><c> tracking</c><00:00:05.400><c> and</c><00:00:05.700><c> using</c><00:00:06.000><c> all</c><00:00:06.120><c> of</c><00:00:06.240><c> our</c>

00:00:06.289 --> 00:00:06.299 align:start position:0%
experiment tracking and using all of our
 

00:00:06.299 --> 00:00:08.570 align:start position:0%
experiment tracking and using all of our
tools<00:00:06.660><c> together</c><00:00:06.839><c> makes</c><00:00:07.560><c> the</c><00:00:07.799><c> ml</c><00:00:08.040><c> workflow</c>

00:00:08.570 --> 00:00:08.580 align:start position:0%
tools together makes the ml workflow
 

00:00:08.580 --> 00:00:10.730 align:start position:0%
tools together makes the ml workflow
even<00:00:08.940><c> more</c><00:00:09.179><c> seamless</c><00:00:09.599><c> what's</c><00:00:10.320><c> nice</c><00:00:10.500><c> about</c>

00:00:10.730 --> 00:00:10.740 align:start position:0%
even more seamless what's nice about
 

00:00:10.740 --> 00:00:12.830 align:start position:0%
even more seamless what's nice about
weights<00:00:11.160><c> and</c><00:00:11.219><c> biases</c><00:00:11.639><c> is</c><00:00:11.760><c> no</c><00:00:12.420><c> matter</c><00:00:12.540><c> what</c>

00:00:12.830 --> 00:00:12.840 align:start position:0%
weights and biases is no matter what
 

00:00:12.840 --> 00:00:14.870 align:start position:0%
weights and biases is no matter what
your<00:00:12.960><c> workflow</c><00:00:13.440><c> we</c><00:00:13.860><c> integrate</c><00:00:14.280><c> with</c><00:00:14.460><c> all</c><00:00:14.700><c> of</c>

00:00:14.870 --> 00:00:14.880 align:start position:0%
your workflow we integrate with all of
 

00:00:14.880 --> 00:00:17.090 align:start position:0%
your workflow we integrate with all of
the<00:00:15.000><c> popular</c><00:00:15.179><c> Cloud</c><00:00:15.900><c> providers</c><00:00:16.560><c> Hardware</c>

00:00:17.090 --> 00:00:17.100 align:start position:0%
the popular Cloud providers Hardware
 

00:00:17.100 --> 00:00:19.670 align:start position:0%
the popular Cloud providers Hardware
vendors<00:00:17.699><c> and</c><00:00:18.180><c> ml</c><00:00:18.480><c> Frameworks</c><00:00:19.080><c> and</c><00:00:19.199><c> libraries</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
vendors and ml Frameworks and libraries
 

00:00:19.680 --> 00:00:22.490 align:start position:0%
vendors and ml Frameworks and libraries
as<00:00:20.400><c> ml</c><00:00:20.820><c> projects</c><00:00:21.240><c> grow</c><00:00:21.480><c> Beyond</c><00:00:22.020><c> just</c><00:00:22.199><c> a</c><00:00:22.380><c> few</c>

00:00:22.490 --> 00:00:22.500 align:start position:0%
as ml projects grow Beyond just a few
 

00:00:22.500 --> 00:00:24.590 align:start position:0%
as ml projects grow Beyond just a few
people<00:00:22.680><c> that's</c><00:00:23.460><c> where</c><00:00:23.699><c> the</c><00:00:23.880><c> true</c><00:00:24.000><c> power</c><00:00:24.240><c> of</c>

00:00:24.590 --> 00:00:24.600 align:start position:0%
people that's where the true power of
 

00:00:24.600 --> 00:00:27.109 align:start position:0%
people that's where the true power of
weights<00:00:24.960><c> and</c><00:00:25.019><c> biases</c><00:00:25.439><c> can</c><00:00:25.619><c> be</c><00:00:25.740><c> felt</c><00:00:26.220><c> ways</c><00:00:27.000><c> and</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
weights and biases can be felt ways and
 

00:00:27.119 --> 00:00:29.689 align:start position:0%
weights and biases can be felt ways and
biases<00:00:27.420><c> helps</c><00:00:27.840><c> teams</c><00:00:28.199><c> improve</c><00:00:28.920><c> collaboration</c>

00:00:29.689 --> 00:00:29.699 align:start position:0%
biases helps teams improve collaboration
 

00:00:29.699 --> 00:00:31.730 align:start position:0%
biases helps teams improve collaboration
and<00:00:30.000><c> productivity</c><00:00:30.539><c> by</c><00:00:31.199><c> being</c><00:00:31.380><c> the</c><00:00:31.560><c> central</c>

00:00:31.730 --> 00:00:31.740 align:start position:0%
and productivity by being the central
 

00:00:31.740 --> 00:00:34.310 align:start position:0%
and productivity by being the central
system<00:00:32.220><c> of</c><00:00:32.520><c> record</c><00:00:32.759><c> across</c><00:00:33.660><c> an</c><00:00:33.840><c> organization</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
system of record across an organization
 

00:00:34.320 --> 00:00:36.770 align:start position:0%
system of record across an organization
okay<00:00:35.160><c> so</c><00:00:35.700><c> let</c><00:00:35.940><c> me</c><00:00:36.059><c> show</c><00:00:36.300><c> you</c><00:00:36.420><c> some</c><00:00:36.600><c> of</c><00:00:36.719><c> the</c>

00:00:36.770 --> 00:00:36.780 align:start position:0%
okay so let me show you some of the
 

00:00:36.780 --> 00:00:38.209 align:start position:0%
okay so let me show you some of the
other<00:00:36.960><c> things</c><00:00:37.200><c> that</c><00:00:37.440><c> you</c><00:00:37.559><c> can</c><00:00:37.620><c> do</c><00:00:37.739><c> at</c><00:00:37.860><c> weights</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
other things that you can do at weights
 

00:00:38.219 --> 00:00:39.830 align:start position:0%
other things that you can do at weights
and<00:00:38.280><c> biases</c><00:00:38.700><c> Beyond</c><00:00:39.180><c> just</c><00:00:39.360><c> experiment</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
and biases Beyond just experiment
 

00:00:39.840 --> 00:00:41.090 align:start position:0%
and biases Beyond just experiment
tracking

00:00:41.090 --> 00:00:41.100 align:start position:0%
tracking
 

00:00:41.100 --> 00:00:42.470 align:start position:0%
tracking
once<00:00:41.640><c> you've</c><00:00:41.760><c> started</c><00:00:42.000><c> tracking</c><00:00:42.300><c> your</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
once you've started tracking your
 

00:00:42.480 --> 00:00:44.150 align:start position:0%
once you've started tracking your
experiments<00:00:42.960><c> in</c><00:00:43.200><c> a</c><00:00:43.379><c> centralized</c><00:00:43.800><c> location</c>

00:00:44.150 --> 00:00:44.160 align:start position:0%
experiments in a centralized location
 

00:00:44.160 --> 00:00:47.510 align:start position:0%
experiments in a centralized location
the<00:00:44.940><c> next</c><00:00:45.059><c> step</c><00:00:45.239><c> is</c><00:00:45.600><c> often</c><00:00:45.960><c> to</c><00:00:46.500><c> optimize</c><00:00:47.219><c> model</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
the next step is often to optimize model
 

00:00:47.520 --> 00:00:49.610 align:start position:0%
the next step is often to optimize model
performance<00:00:48.059><c> and</c><00:00:48.780><c> we</c><00:00:48.899><c> have</c><00:00:49.079><c> a</c><00:00:49.200><c> tool</c><00:00:49.440><c> called</c>

00:00:49.610 --> 00:00:49.620 align:start position:0%
performance and we have a tool called
 

00:00:49.620 --> 00:00:51.950 align:start position:0%
performance and we have a tool called
sweeps<00:00:50.219><c> that</c><00:00:50.700><c> does</c><00:00:50.940><c> just</c><00:00:51.180><c> that</c>

00:00:51.950 --> 00:00:51.960 align:start position:0%
sweeps that does just that
 

00:00:51.960 --> 00:00:54.290 align:start position:0%
sweeps that does just that
it<00:00:52.440><c> provides</c><00:00:52.800><c> lightweight</c><00:00:53.280><c> automated</c><00:00:53.879><c> hyper</c>

00:00:54.290 --> 00:00:54.300 align:start position:0%
it provides lightweight automated hyper
 

00:00:54.300 --> 00:00:56.150 align:start position:0%
it provides lightweight automated hyper
parameter<00:00:54.719><c> optimization</c><00:00:55.260><c> and</c><00:00:55.739><c> a</c><00:00:55.920><c> fully</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
parameter optimization and a fully
 

00:00:56.160 --> 00:00:58.729 align:start position:0%
parameter optimization and a fully
scalable<00:00:56.640><c> and</c><00:00:57.180><c> customizable</c><00:00:57.960><c> way</c>

00:00:58.729 --> 00:00:58.739 align:start position:0%
scalable and customizable way
 

00:00:58.739 --> 00:01:00.590 align:start position:0%
scalable and customizable way
as<00:00:59.160><c> you</c><00:00:59.280><c> can</c><00:00:59.399><c> see</c><00:00:59.520><c> from</c><00:00:59.820><c> this</c><00:01:00.120><c> parallel</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
as you can see from this parallel
 

00:01:00.600 --> 00:01:02.930 align:start position:0%
as you can see from this parallel
coordinates<00:01:01.140><c> chart</c><00:01:01.500><c> you</c><00:01:02.340><c> can</c><00:01:02.460><c> quickly</c><00:01:02.699><c> draw</c>

00:01:02.930 --> 00:01:02.940 align:start position:0%
coordinates chart you can quickly draw
 

00:01:02.940 --> 00:01:04.969 align:start position:0%
coordinates chart you can quickly draw
insights<00:01:03.780><c> into</c><00:01:04.140><c> what</c><00:01:04.500><c> parameter</c>

00:01:04.969 --> 00:01:04.979 align:start position:0%
insights into what parameter
 

00:01:04.979 --> 00:01:06.649 align:start position:0%
insights into what parameter
combinations<00:01:05.519><c> are</c><00:01:05.939><c> leading</c><00:01:06.240><c> to</c><00:01:06.360><c> the</c><00:01:06.540><c> best</c>

00:01:06.649 --> 00:01:06.659 align:start position:0%
combinations are leading to the best
 

00:01:06.659 --> 00:01:07.850 align:start position:0%
combinations are leading to the best
models

00:01:07.850 --> 00:01:07.860 align:start position:0%
models
 

00:01:07.860 --> 00:01:09.830 align:start position:0%
models
and<00:01:08.280><c> if</c><00:01:08.400><c> I</c><00:01:08.520><c> drill</c><00:01:08.820><c> down</c><00:01:08.939><c> into</c><00:01:09.299><c> any</c><00:01:09.540><c> of</c><00:01:09.659><c> these</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
and if I drill down into any of these
 

00:01:09.840 --> 00:01:11.630 align:start position:0%
and if I drill down into any of these
hyper<00:01:10.140><c> parameters</c><00:01:10.560><c> even</c><00:01:10.799><c> further</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
hyper parameters even further
 

00:01:11.640 --> 00:01:13.670 align:start position:0%
hyper parameters even further
you'll<00:01:12.240><c> see</c><00:01:12.420><c> that</c><00:01:12.600><c> the</c><00:01:12.720><c> data</c><00:01:13.020><c> changes</c><00:01:13.439><c> to</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
you'll see that the data changes to
 

00:01:13.680 --> 00:01:15.950 align:start position:0%
you'll see that the data changes to
reflect<00:01:14.159><c> that</c><00:01:14.340><c> the</c><00:01:14.880><c> platform</c><00:01:15.180><c> also</c><00:01:15.540><c> allows</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
reflect that the platform also allows
 

00:01:15.960 --> 00:01:18.410 align:start position:0%
reflect that the platform also allows
you<00:01:16.380><c> to</c><00:01:16.680><c> easily</c><00:01:17.040><c> visualize</c><00:01:17.520><c> your</c><00:01:17.760><c> data</c><00:01:18.000><c> in</c>

00:01:18.410 --> 00:01:18.420 align:start position:0%
you to easily visualize your data in
 

00:01:18.420 --> 00:01:21.410 align:start position:0%
you to easily visualize your data in
various<00:01:18.720><c> charts</c><00:01:19.200><c> and</c><00:01:19.680><c> tables</c><00:01:20.159><c> here</c><00:01:20.400><c> too</c><00:01:20.700><c> here</c>

00:01:21.410 --> 00:01:21.420 align:start position:0%
various charts and tables here too here
 

00:01:21.420 --> 00:01:23.749 align:start position:0%
various charts and tables here too here
you'll<00:01:21.720><c> see</c><00:01:21.840><c> your</c><00:01:21.960><c> weights</c><00:01:22.259><c> and</c><00:01:22.320><c> biases</c><00:01:22.740><c> table</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
you'll see your weights and biases table
 

00:01:23.759 --> 00:01:26.570 align:start position:0%
you'll see your weights and biases table
this<00:01:24.479><c> interactive</c><00:01:25.140><c> data</c><00:01:25.619><c> visualization</c><00:01:26.100><c> tool</c>

00:01:26.570 --> 00:01:26.580 align:start position:0%
this interactive data visualization tool
 

00:01:26.580 --> 00:01:30.230 align:start position:0%
this interactive data visualization tool
allows<00:01:27.479><c> teams</c><00:01:27.900><c> to</c><00:01:28.080><c> easily</c><00:01:28.500><c> visualize</c><00:01:29.280><c> analyze</c>

00:01:30.230 --> 00:01:30.240 align:start position:0%
allows teams to easily visualize analyze
 

00:01:30.240 --> 00:01:33.350 align:start position:0%
allows teams to easily visualize analyze
and<00:01:30.540><c> debug</c><00:01:30.960><c> models</c><00:01:31.560><c> collaboratively</c><00:01:32.460><c> with</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
and debug models collaboratively with
 

00:01:33.360 --> 00:01:35.929 align:start position:0%
and debug models collaboratively with
tables<00:01:33.840><c> you</c><00:01:34.320><c> can</c><00:01:34.439><c> combine</c><00:01:34.799><c> rich</c><00:01:35.100><c> media</c><00:01:35.579><c> like</c>

00:01:35.929 --> 00:01:35.939 align:start position:0%
tables you can combine rich media like
 

00:01:35.939 --> 00:01:38.510 align:start position:0%
tables you can combine rich media like
images<00:01:36.299><c> video</c><00:01:36.960><c> and</c><00:01:37.320><c> audio</c><00:01:37.520><c> alongside</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
images video and audio alongside
 

00:01:38.520 --> 00:01:40.969 align:start position:0%
images video and audio alongside
performance<00:01:39.060><c> metrics</c><00:01:39.540><c> for</c><00:01:39.780><c> analysis</c><00:01:40.320><c> it's</c>

00:01:40.969 --> 00:01:40.979 align:start position:0%
performance metrics for analysis it's
 

00:01:40.979 --> 00:01:42.770 align:start position:0%
performance metrics for analysis it's
really<00:01:41.159><c> easy</c><00:01:41.400><c> to</c><00:01:41.640><c> manipulate</c><00:01:42.119><c> a</c><00:01:42.360><c> ways</c><00:01:42.659><c> and</c>

00:01:42.770 --> 00:01:42.780 align:start position:0%
really easy to manipulate a ways and
 

00:01:42.780 --> 00:01:44.630 align:start position:0%
really easy to manipulate a ways and
biases<00:01:43.079><c> table</c><00:01:43.320><c> and</c><00:01:43.619><c> derive</c><00:01:43.979><c> some</c><00:01:44.280><c> valuable</c>

00:01:44.630 --> 00:01:44.640 align:start position:0%
biases table and derive some valuable
 

00:01:44.640 --> 00:01:45.890 align:start position:0%
biases table and derive some valuable
insights

00:01:45.890 --> 00:01:45.900 align:start position:0%
insights
 

00:01:45.900 --> 00:01:48.170 align:start position:0%
insights
in<00:01:46.439><c> this</c><00:01:46.560><c> case</c><00:01:46.740><c> I'm</c><00:01:47.040><c> using</c><00:01:47.400><c> the</c><00:01:47.579><c> table</c><00:01:47.759><c> to</c>

00:01:48.170 --> 00:01:48.180 align:start position:0%
in this case I'm using the table to
 

00:01:48.180 --> 00:01:50.569 align:start position:0%
in this case I'm using the table to
create<00:01:48.299><c> a</c><00:01:48.600><c> new</c><00:01:48.659><c> plot</c><00:01:49.500><c> to</c><00:01:49.979><c> explore</c><00:01:50.220><c> my</c><00:01:50.460><c> model</c>

00:01:50.569 --> 00:01:50.579 align:start position:0%
create a new plot to explore my model
 

00:01:50.579 --> 00:01:53.630 align:start position:0%
create a new plot to explore my model
predictions<00:01:51.119><c> debug</c><00:01:51.840><c> issues</c><00:01:52.320><c> and</c><00:01:52.920><c> simply</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
predictions debug issues and simply
 

00:01:53.640 --> 00:01:55.670 align:start position:0%
predictions debug issues and simply
understand<00:01:53.880><c> how</c><00:01:54.420><c> my</c><00:01:54.659><c> data</c><00:01:54.960><c> sets</c><00:01:55.200><c> models</c><00:01:55.560><c> and</c>

00:01:55.670 --> 00:01:55.680 align:start position:0%
understand how my data sets models and
 

00:01:55.680 --> 00:01:58.490 align:start position:0%
understand how my data sets models and
outputs<00:01:56.159><c> are</c><00:01:56.340><c> related</c>

00:01:58.490 --> 00:01:58.500 align:start position:0%
outputs are related
 

00:01:58.500 --> 00:02:00.830 align:start position:0%
outputs are related
I<00:01:58.799><c> can</c><00:01:59.040><c> explore</c><00:01:59.340><c> Tables</c><00:01:59.880><c> by</c><00:02:00.180><c> dynamically</c>

00:02:00.830 --> 00:02:00.840 align:start position:0%
I can explore Tables by dynamically
 

00:02:00.840 --> 00:02:02.510 align:start position:0%
I can explore Tables by dynamically
filtering

00:02:02.510 --> 00:02:02.520 align:start position:0%
filtering
 

00:02:02.520 --> 00:02:05.209 align:start position:0%
filtering
grouping<00:02:03.180><c> by</c><00:02:03.360><c> classes</c><00:02:03.960><c> or</c><00:02:04.680><c> creating</c><00:02:05.040><c> new</c>

00:02:05.209 --> 00:02:05.219 align:start position:0%
grouping by classes or creating new
 

00:02:05.219 --> 00:02:06.649 align:start position:0%
grouping by classes or creating new
columns<00:02:05.640><c> to</c><00:02:05.939><c> tell</c><00:02:06.060><c> me</c><00:02:06.180><c> when</c><00:02:06.360><c> my</c><00:02:06.540><c> model</c>

00:02:06.649 --> 00:02:06.659 align:start position:0%
columns to tell me when my model
 

00:02:06.659 --> 00:02:08.930 align:start position:0%
columns to tell me when my model
predictions<00:02:07.200><c> were</c><00:02:07.380><c> incorrect</c><00:02:07.860><c> to</c><00:02:08.459><c> sum</c><00:02:08.580><c> it</c><00:02:08.759><c> up</c>

00:02:08.930 --> 00:02:08.940 align:start position:0%
predictions were incorrect to sum it up
 

00:02:08.940 --> 00:02:10.790 align:start position:0%
predictions were incorrect to sum it up
weights<00:02:09.599><c> and</c><00:02:09.660><c> biases</c><00:02:10.020><c> makes</c><00:02:10.259><c> tracking</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
weights and biases makes tracking
 

00:02:10.800 --> 00:02:12.890 align:start position:0%
weights and biases makes tracking
visualizing<00:02:11.640><c> and</c><00:02:11.879><c> sharing</c><00:02:12.239><c> your</c><00:02:12.420><c> work</c><00:02:12.599><c> easy</c>

00:02:12.890 --> 00:02:12.900 align:start position:0%
visualizing and sharing your work easy
 

00:02:12.900 --> 00:02:15.229 align:start position:0%
visualizing and sharing your work easy
because<00:02:13.440><c> we</c><00:02:13.680><c> offer</c><00:02:13.920><c> tools</c><00:02:14.280><c> like</c><00:02:14.580><c> experiment</c>

00:02:15.229 --> 00:02:15.239 align:start position:0%
because we offer tools like experiment
 

00:02:15.239 --> 00:02:18.110 align:start position:0%
because we offer tools like experiment
tracking<00:02:15.660><c> sweeps</c><00:02:16.500><c> and</c><00:02:16.860><c> tables</c><00:02:17.280><c> all</c><00:02:17.760><c> in</c><00:02:17.940><c> one</c>

00:02:18.110 --> 00:02:18.120 align:start position:0%
tracking sweeps and tables all in one
 

00:02:18.120 --> 00:02:20.030 align:start position:0%
tracking sweeps and tables all in one
unified<00:02:18.420><c> platform</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
unified platform
 

00:02:20.040 --> 00:02:22.250 align:start position:0%
unified platform
if<00:02:20.580><c> you</c><00:02:20.640><c> need</c><00:02:20.819><c> to</c><00:02:21.000><c> capture</c><00:02:21.300><c> the</c><00:02:21.780><c> data</c><00:02:22.140><c> you're</c>

00:02:22.250 --> 00:02:22.260 align:start position:0%
if you need to capture the data you're
 

00:02:22.260 --> 00:02:23.809 align:start position:0%
if you need to capture the data you're
using<00:02:22.620><c> and</c><00:02:23.040><c> producing</c><00:02:23.459><c> during</c>

00:02:23.809 --> 00:02:23.819 align:start position:0%
using and producing during
 

00:02:23.819 --> 00:02:26.390 align:start position:0%
using and producing during
experimentation<00:02:24.599><c> our</c><00:02:25.260><c> next</c><00:02:25.500><c> tool</c><00:02:25.860><c> artifacts</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
experimentation our next tool artifacts
 

00:02:26.400 --> 00:02:27.350 align:start position:0%
experimentation our next tool artifacts
can<00:02:26.580><c> help</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
can help
 

00:02:27.360 --> 00:02:29.330 align:start position:0%
can help
artifacts<00:02:28.260><c> tracks</c><00:02:28.620><c> your</c><00:02:28.739><c> data</c><00:02:29.040><c> lineage</c>

00:02:29.330 --> 00:02:29.340 align:start position:0%
artifacts tracks your data lineage
 

00:02:29.340 --> 00:02:32.270 align:start position:0%
artifacts tracks your data lineage
throughout<00:02:30.060><c> a</c><00:02:30.420><c> Project's</c><00:02:30.900><c> life</c><00:02:31.140><c> cycle</c><00:02:31.440><c> it</c>

00:02:32.270 --> 00:02:32.280 align:start position:0%
throughout a Project's life cycle it
 

00:02:32.280 --> 00:02:34.670 align:start position:0%
throughout a Project's life cycle it
allows<00:02:32.520><c> you</c><00:02:32.580><c> to</c><00:02:32.760><c> version</c><00:02:33.000><c> organize</c><00:02:33.840><c> and</c>

00:02:34.670 --> 00:02:34.680 align:start position:0%
allows you to version organize and
 

00:02:34.680 --> 00:02:37.729 align:start position:0%
allows you to version organize and
compare<00:02:35.040><c> data</c><00:02:35.459><c> and</c><00:02:35.580><c> model</c><00:02:35.700><c> assets</c><00:02:36.180><c> over</c><00:02:36.480><c> time</c>

00:02:37.729 --> 00:02:37.739 align:start position:0%
compare data and model assets over time
 

00:02:37.739 --> 00:02:40.130 align:start position:0%
compare data and model assets over time
built<00:02:38.340><c> on</c><00:02:38.400><c> top</c><00:02:38.520><c> of</c><00:02:38.700><c> artifacts</c><00:02:39.300><c> is</c><00:02:39.599><c> weights</c><00:02:40.080><c> and</c>

00:02:40.130 --> 00:02:40.140 align:start position:0%
built on top of artifacts is weights and
 

00:02:40.140 --> 00:02:42.650 align:start position:0%
built on top of artifacts is weights and
bias<00:02:40.440><c> models</c><00:02:41.040><c> here</c><00:02:41.640><c> you</c><00:02:41.819><c> can</c><00:02:41.940><c> store</c><00:02:42.120><c> models</c>

00:02:42.650 --> 00:02:42.660 align:start position:0%
bias models here you can store models
 

00:02:42.660 --> 00:02:44.509 align:start position:0%
bias models here you can store models
together<00:02:43.200><c> with</c><00:02:43.680><c> the</c><00:02:43.860><c> experiments</c><00:02:44.400><c> that</c>

00:02:44.509 --> 00:02:44.519 align:start position:0%
together with the experiments that
 

00:02:44.519 --> 00:02:46.850 align:start position:0%
together with the experiments that
produce<00:02:44.879><c> them</c><00:02:45.060><c> this</c><00:02:45.840><c> allows</c><00:02:46.260><c> all</c><00:02:46.620><c> work</c>

00:02:46.850 --> 00:02:46.860 align:start position:0%
produce them this allows all work
 

00:02:46.860 --> 00:02:48.770 align:start position:0%
produce them this allows all work
throughout<00:02:47.400><c> the</c><00:02:47.640><c> entire</c><00:02:47.879><c> model</c><00:02:48.239><c> development</c>

00:02:48.770 --> 00:02:48.780 align:start position:0%
throughout the entire model development
 

00:02:48.780 --> 00:02:51.710 align:start position:0%
throughout the entire model development
process<00:02:49.200><c> to</c><00:02:50.040><c> be</c><00:02:50.160><c> easily</c><00:02:50.519><c> reproducible</c><00:02:51.180><c> and</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
process to be easily reproducible and
 

00:02:51.720 --> 00:02:54.110 align:start position:0%
process to be easily reproducible and
discoverable<00:02:52.200><c> by</c><00:02:52.560><c> anyone</c><00:02:52.860><c> on</c><00:02:53.099><c> the</c><00:02:53.220><c> team</c><00:02:53.340><c> it's</c>

00:02:54.110 --> 00:02:54.120 align:start position:0%
discoverable by anyone on the team it's
 

00:02:54.120 --> 00:02:55.610 align:start position:0%
discoverable by anyone on the team it's
great<00:02:54.300><c> for</c><00:02:54.480><c> onboarding</c><00:02:54.959><c> and</c><00:02:55.080><c> off-boarding</c>

00:02:55.610 --> 00:02:55.620 align:start position:0%
great for onboarding and off-boarding
 

00:02:55.620 --> 00:02:58.790 align:start position:0%
great for onboarding and off-boarding
team<00:02:55.739><c> members</c><00:02:56.160><c> as</c><00:02:56.580><c> work</c><00:02:56.760><c> is</c><00:02:56.940><c> never</c><00:02:57.120><c> lost</c>

00:02:58.790 --> 00:02:58.800 align:start position:0%
team members as work is never lost
 

00:02:58.800 --> 00:03:00.470 align:start position:0%
team members as work is never lost
now<00:02:59.280><c> that</c><00:02:59.459><c> you</c><00:02:59.580><c> have</c><00:02:59.700><c> a</c><00:02:59.879><c> better</c><00:02:59.940><c> understanding</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
now that you have a better understanding
 

00:03:00.480 --> 00:03:02.990 align:start position:0%
now that you have a better understanding
of<00:03:00.780><c> your</c><00:03:00.900><c> experiments</c><00:03:01.500><c> and</c><00:03:02.220><c> what</c><00:03:02.459><c> worked</c><00:03:02.700><c> and</c>

00:03:02.990 --> 00:03:03.000 align:start position:0%
of your experiments and what worked and
 

00:03:03.000 --> 00:03:04.790 align:start position:0%
of your experiments and what worked and
what<00:03:03.180><c> didn't</c><00:03:03.360><c> it's</c><00:03:04.080><c> time</c><00:03:04.260><c> for</c><00:03:04.440><c> you</c><00:03:04.560><c> to</c><00:03:04.680><c> share</c>

00:03:04.790 --> 00:03:04.800 align:start position:0%
what didn't it's time for you to share
 

00:03:04.800 --> 00:03:07.970 align:start position:0%
what didn't it's time for you to share
your<00:03:04.980><c> results</c><00:03:05.160><c> with</c><00:03:05.760><c> your</c><00:03:06.060><c> team</c><00:03:06.739><c> I'm</c><00:03:07.739><c> going</c><00:03:07.920><c> to</c>

00:03:07.970 --> 00:03:07.980 align:start position:0%
your results with your team I'm going to
 

00:03:07.980 --> 00:03:10.550 align:start position:0%
your results with your team I'm going to
navigate<00:03:08.160><c> to</c><00:03:08.700><c> reports</c><00:03:09.300><c> and</c><00:03:09.959><c> here</c><00:03:10.140><c> you</c><00:03:10.260><c> can</c><00:03:10.379><c> see</c>

00:03:10.550 --> 00:03:10.560 align:start position:0%
navigate to reports and here you can see
 

00:03:10.560 --> 00:03:12.110 align:start position:0%
navigate to reports and here you can see
a<00:03:10.739><c> list</c><00:03:10.860><c> of</c><00:03:10.980><c> reports</c><00:03:11.400><c> from</c><00:03:11.640><c> this</c><00:03:11.760><c> team's</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
a list of reports from this team's
 

00:03:12.120 --> 00:03:13.009 align:start position:0%
a list of reports from this team's
project

00:03:13.009 --> 00:03:13.019 align:start position:0%
project
 

00:03:13.019 --> 00:03:14.570 align:start position:0%
project
and<00:03:13.319><c> I'm</c><00:03:13.440><c> going</c><00:03:13.560><c> to</c><00:03:13.680><c> select</c><00:03:13.980><c> the</c><00:03:14.280><c> publish</c>

00:03:14.570 --> 00:03:14.580 align:start position:0%
and I'm going to select the publish
 

00:03:14.580 --> 00:03:16.729 align:start position:0%
and I'm going to select the publish
report<00:03:14.760><c> image</c><00:03:15.540><c> classification</c><00:03:16.200><c> live</c>

00:03:16.729 --> 00:03:16.739 align:start position:0%
report image classification live
 

00:03:16.739 --> 00:03:18.290 align:start position:0%
report image classification live
dashboard

00:03:18.290 --> 00:03:18.300 align:start position:0%
dashboard
 

00:03:18.300 --> 00:03:20.690 align:start position:0%
dashboard
you<00:03:18.599><c> can</c><00:03:18.720><c> effectively</c><00:03:19.319><c> share</c><00:03:19.620><c> key</c><00:03:20.040><c> project</c>

00:03:20.690 --> 00:03:20.700 align:start position:0%
you can effectively share key project
 

00:03:20.700 --> 00:03:23.390 align:start position:0%
you can effectively share key project
metrics<00:03:21.300><c> and</c><00:03:21.540><c> Analysis</c><00:03:22.019><c> with</c><00:03:22.739><c> both</c><00:03:22.920><c> Technical</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
metrics and Analysis with both Technical
 

00:03:23.400 --> 00:03:25.610 align:start position:0%
metrics and Analysis with both Technical
and<00:03:23.700><c> non-technical</c><00:03:24.300><c> stakeholders</c>

00:03:25.610 --> 00:03:25.620 align:start position:0%
and non-technical stakeholders
 

00:03:25.620 --> 00:03:27.649 align:start position:0%
and non-technical stakeholders
without<00:03:26.159><c> having</c><00:03:26.459><c> to</c><00:03:26.640><c> take</c><00:03:26.760><c> screenshots</c><00:03:27.239><c> or</c>

00:03:27.649 --> 00:03:27.659 align:start position:0%
without having to take screenshots or
 

00:03:27.659 --> 00:03:30.170 align:start position:0%
without having to take screenshots or
clean<00:03:27.900><c> up</c><00:03:28.080><c> any</c><00:03:28.500><c> unorganized</c><00:03:29.220><c> notes</c>

00:03:30.170 --> 00:03:30.180 align:start position:0%
clean up any unorganized notes
 

00:03:30.180 --> 00:03:32.690 align:start position:0%
clean up any unorganized notes
these<00:03:30.720><c> dashboards</c><00:03:31.140><c> are</c><00:03:31.319><c> all</c><00:03:31.440><c> live</c><00:03:31.680><c> so</c><00:03:32.340><c> if</c><00:03:32.459><c> I</c><00:03:32.580><c> do</c>

00:03:32.690 --> 00:03:32.700 align:start position:0%
these dashboards are all live so if I do
 

00:03:32.700 --> 00:03:34.490 align:start position:0%
these dashboards are all live so if I do
any<00:03:32.879><c> more</c><00:03:33.060><c> work</c><00:03:33.239><c> this</c><00:03:33.840><c> will</c><00:03:34.019><c> automatically</c>

00:03:34.490 --> 00:03:34.500 align:start position:0%
any more work this will automatically
 

00:03:34.500 --> 00:03:37.490 align:start position:0%
any more work this will automatically
update<00:03:34.980><c> so</c><00:03:35.819><c> that</c><00:03:36.000><c> the</c><00:03:36.180><c> rest</c><00:03:36.300><c> of</c><00:03:36.420><c> my</c><00:03:36.599><c> team</c><00:03:36.780><c> or</c><00:03:37.319><c> my</c>

00:03:37.490 --> 00:03:37.500 align:start position:0%
update so that the rest of my team or my
 

00:03:37.500 --> 00:03:39.830 align:start position:0%
update so that the rest of my team or my
manager<00:03:37.860><c> can</c><00:03:38.099><c> see</c><00:03:38.340><c> my</c><00:03:38.640><c> latest</c><00:03:38.819><c> progress</c>

00:03:39.830 --> 00:03:39.840 align:start position:0%
manager can see my latest progress
 

00:03:39.840 --> 00:03:41.630 align:start position:0%
manager can see my latest progress
with<00:03:40.319><c> reports</c><00:03:40.739><c> I</c><00:03:40.920><c> can</c><00:03:41.040><c> show</c><00:03:41.159><c> how</c><00:03:41.340><c> my</c><00:03:41.519><c> model</c>

00:03:41.630 --> 00:03:41.640 align:start position:0%
with reports I can show how my model
 

00:03:41.640 --> 00:03:44.330 align:start position:0%
with reports I can show how my model
Works<00:03:42.180><c> show</c><00:03:42.599><c> graphs</c><00:03:43.140><c> and</c><00:03:43.260><c> visualizations</c><00:03:43.860><c> of</c>

00:03:44.330 --> 00:03:44.340 align:start position:0%
Works show graphs and visualizations of
 

00:03:44.340 --> 00:03:45.830 align:start position:0%
Works show graphs and visualizations of
how<00:03:44.519><c> the</c><00:03:44.700><c> model</c><00:03:44.819><c> versions</c><00:03:45.180><c> improved</c><00:03:45.659><c> over</c>

00:03:45.830 --> 00:03:45.840 align:start position:0%
how the model versions improved over
 

00:03:45.840 --> 00:03:48.350 align:start position:0%
how the model versions improved over
time<00:03:46.080><c> discuss</c><00:03:46.980><c> bugs</c><00:03:47.459><c> and</c><00:03:47.940><c> demonstrate</c>

00:03:48.350 --> 00:03:48.360 align:start position:0%
time discuss bugs and demonstrate
 

00:03:48.360 --> 00:03:50.449 align:start position:0%
time discuss bugs and demonstrate
progress<00:03:48.720><c> towards</c><00:03:49.140><c> key</c><00:03:49.379><c> Milestones</c><00:03:49.980><c> you</c><00:03:50.340><c> can</c>

00:03:50.449 --> 00:03:50.459 align:start position:0%
progress towards key Milestones you can
 

00:03:50.459 --> 00:03:52.070 align:start position:0%
progress towards key Milestones you can
even<00:03:50.580><c> share</c><00:03:50.760><c> your</c><00:03:50.940><c> reports</c><00:03:51.299><c> externally</c><00:03:51.659><c> if</c><00:03:51.900><c> an</c>

00:03:52.070 --> 00:03:52.080 align:start position:0%
even share your reports externally if an
 

00:03:52.080 --> 00:03:53.630 align:start position:0%
even share your reports externally if an
outside<00:03:52.200><c> audience</c><00:03:52.680><c> needs</c><00:03:53.040><c> to</c><00:03:53.159><c> consume</c><00:03:53.519><c> the</c>

00:03:53.630 --> 00:03:53.640 align:start position:0%
outside audience needs to consume the
 

00:03:53.640 --> 00:03:55.070 align:start position:0%
outside audience needs to consume the
results<00:03:53.819><c> as</c><00:03:54.060><c> well</c>

00:03:55.070 --> 00:03:55.080 align:start position:0%
results as well
 

00:03:55.080 --> 00:03:56.990 align:start position:0%
results as well
and<00:03:55.500><c> that's</c><00:03:55.620><c> all</c><00:03:55.860><c> for</c><00:03:56.040><c> this</c><00:03:56.400><c> short</c><00:03:56.700><c> video</c>

00:03:56.990 --> 00:03:57.000 align:start position:0%
and that's all for this short video
 

00:03:57.000 --> 00:03:59.449 align:start position:0%
and that's all for this short video
series<00:03:57.360><c> but</c><00:03:57.840><c> don't</c><00:03:58.019><c> let</c><00:03:58.260><c> that</c><00:03:58.500><c> stop</c><00:03:58.739><c> you</c><00:03:58.980><c> on</c>

00:03:59.449 --> 00:03:59.459 align:start position:0%
series but don't let that stop you on
 

00:03:59.459 --> 00:04:01.190 align:start position:0%
series but don't let that stop you on
your<00:03:59.640><c> Learning</c><00:04:00.000><c> Journey</c><00:04:00.299><c> here</c><00:04:00.900><c> are</c><00:04:01.080><c> some</c>

00:04:01.190 --> 00:04:01.200 align:start position:0%
your Learning Journey here are some
 

00:04:01.200 --> 00:04:03.170 align:start position:0%
your Learning Journey here are some
useful<00:04:01.500><c> links</c><00:04:01.860><c> for</c><00:04:02.040><c> you</c><00:04:02.159><c> to</c><00:04:02.280><c> dive</c><00:04:02.519><c> deeper</c><00:04:02.879><c> into</c>

00:04:03.170 --> 00:04:03.180 align:start position:0%
useful links for you to dive deeper into
 

00:04:03.180 --> 00:04:05.030 align:start position:0%
useful links for you to dive deeper into
what<00:04:03.360><c> we</c><00:04:03.480><c> covered</c><00:04:03.780><c> in</c><00:04:03.840><c> this</c><00:04:04.019><c> video</c><00:04:04.200><c> you</c><00:04:04.920><c> can</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
what we covered in this video you can
 

00:04:05.040 --> 00:04:07.009 align:start position:0%
what we covered in this video you can
check<00:04:05.220><c> out</c><00:04:05.400><c> our</c><00:04:05.580><c> documentation</c><00:04:06.180><c> if</c><00:04:06.900><c> you're</c>

00:04:07.009 --> 00:04:07.019 align:start position:0%
check out our documentation if you're
 

00:04:07.019 --> 00:04:08.930 align:start position:0%
check out our documentation if you're
having<00:04:07.200><c> any</c><00:04:07.500><c> issues</c><00:04:07.920><c> you</c><00:04:08.280><c> can</c><00:04:08.400><c> go</c><00:04:08.640><c> to</c><00:04:08.760><c> our</c>

00:04:08.930 --> 00:04:08.940 align:start position:0%
having any issues you can go to our
 

00:04:08.940 --> 00:04:10.970 align:start position:0%
having any issues you can go to our
support<00:04:09.180><c> forum</c><00:04:09.720><c> and</c><00:04:10.379><c> if</c><00:04:10.560><c> you</c><00:04:10.620><c> just</c><00:04:10.739><c> want</c><00:04:10.860><c> to</c>

00:04:10.970 --> 00:04:10.980 align:start position:0%
support forum and if you just want to
 

00:04:10.980 --> 00:04:12.350 align:start position:0%
support forum and if you just want to
learn<00:04:11.159><c> what</c><00:04:11.580><c> other</c><00:04:11.760><c> people</c><00:04:11.939><c> are</c><00:04:12.120><c> using</c>

00:04:12.350 --> 00:04:12.360 align:start position:0%
learn what other people are using
 

00:04:12.360 --> 00:04:14.449 align:start position:0%
learn what other people are using
weights<00:04:12.720><c> and</c><00:04:12.780><c> biases</c><00:04:13.200><c> for</c><00:04:13.379><c> you</c><00:04:14.099><c> can</c><00:04:14.159><c> browse</c>

00:04:14.449 --> 00:04:14.459 align:start position:0%
weights and biases for you can browse
 

00:04:14.459 --> 00:04:16.670 align:start position:0%
weights and biases for you can browse
our<00:04:14.760><c> community</c><00:04:15.000><c> blog</c><00:04:15.540><c> fully</c><00:04:16.260><c> connected</c>

00:04:16.670 --> 00:04:16.680 align:start position:0%
our community blog fully connected
 

00:04:16.680 --> 00:04:20.359 align:start position:0%
our community blog fully connected
thanks<00:04:17.400><c> very</c><00:04:17.579><c> much</c><00:04:17.760><c> and</c><00:04:18.000><c> good</c><00:04:18.180><c> luck</c>

