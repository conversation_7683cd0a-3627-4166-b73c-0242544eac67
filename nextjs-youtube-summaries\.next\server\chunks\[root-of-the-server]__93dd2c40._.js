module.exports = {

"[project]/.next-internal/server/app/api/videos/recent/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://nppxvpupvhszcdkspsiq.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5wcHh2cHVwdmhzemNka3Nwc2lxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTQzMzUyNzcsImV4cCI6MjAyOTkxMTI3N30.ltX4WYk2krVxnsSGOXSA72BSZCJGFhqn1X-H8fZ_q9k");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DatabaseService": (()=>DatabaseService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
class DatabaseService {
    // Get all available topic tables with better error handling
    static async getTopicTables() {
        try {
            // Use the actual YouTube topic table names discovered in the database
            const youtubeTopics = [
                'youtube_artificial_intelligence',
                'youtube_sustainability',
                'youtube_startups',
                'youtube_financial_markets',
                'youtube_gme',
                'youtube_general',
                'youtube_legal',
                'youtube_renewable_energy'
            ];
            const existingTables = [];
            console.log('Checking for YouTube topic tables...');
            for (const topic of youtubeTopics){
                try {
                    const { error, count } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topic).select('id', {
                        count: 'exact',
                        head: true
                    }).limit(1);
                    if (!error) {
                        console.log(`Found table: ${topic} with ${count || 0} videos`);
                        existingTables.push({
                            id: topic,
                            table_name: topic,
                            name: topic,
                            display_name: this.formatDisplayName(topic),
                            description: `${this.formatDisplayName(topic)} videos`,
                            video_count: count || 0,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString() // Placeholder
                        });
                    }
                } catch  {
                // Table doesn't exist, skip silently
                }
            }
            if (existingTables.length > 0) {
                return existingTables;
            }
            console.warn('No YouTube topic tables found in database, using fallback topics');
            // Return fallback topics for development
            const fallbackDate = new Date().toISOString();
            return [
                {
                    id: 'youtube_artificial_intelligence',
                    table_name: 'youtube_artificial_intelligence',
                    name: 'youtube_artificial_intelligence',
                    display_name: 'Artificial Intelligence',
                    description: 'AI and machine learning videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_sustainability',
                    table_name: 'youtube_sustainability',
                    name: 'youtube_sustainability',
                    display_name: 'Sustainability',
                    description: 'Sustainability and environment videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_startups',
                    table_name: 'youtube_startups',
                    name: 'youtube_startups',
                    display_name: 'Startups',
                    description: 'Startup and entrepreneurship videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_financial_markets',
                    table_name: 'youtube_financial_markets',
                    name: 'youtube_financial_markets',
                    display_name: 'Financial Markets',
                    description: 'Finance and market videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                }
            ];
        } catch (error) {
            console.error('Error fetching topic tables:', error);
            // Return fallback topics when database is not accessible
            const fallbackDate = new Date().toISOString();
            return [
                {
                    id: 'youtube_artificial_intelligence',
                    table_name: 'youtube_artificial_intelligence',
                    name: 'youtube_artificial_intelligence',
                    display_name: 'Artificial Intelligence',
                    description: 'AI and machine learning videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_sustainability',
                    table_name: 'youtube_sustainability',
                    name: 'youtube_sustainability',
                    display_name: 'Sustainability',
                    description: 'Sustainability and environment videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_startups',
                    table_name: 'youtube_startups',
                    name: 'youtube_startups',
                    display_name: 'Startups',
                    description: 'Startup and entrepreneurship videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_financial_markets',
                    table_name: 'youtube_financial_markets',
                    name: 'youtube_financial_markets',
                    display_name: 'Financial Markets',
                    description: 'Finance and market videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                }
            ];
        }
    }
    // Helper method to format display names
    static formatDisplayName(tableName) {
        return tableName.replace('youtube_', '') // Remove youtube_ prefix
        .replace('_', ' ').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
    // Get all unique channels from available topic tables
    static async getChannels(topicFilter) {
        try {
            const allChannels = new Set();
            // Get all available topic tables
            const allTopicTables = await this.getTopicTables();
            // Filter topic tables if topicFilter is provided
            const topicTables = topicFilter && topicFilter.length > 0 ? allTopicTables.filter((t)=>{
                const simpleName = t.name.startsWith('youtube_') ? t.name.substring(8) : t.name;
                return topicFilter.includes(simpleName) || topicFilter.includes(t.name);
            }) : allTopicTables;
            console.log('Fetching channels from tables:', topicTables.map((t)=>t.name));
            console.log('Topic filter applied:', topicFilter);
            // Query each table for unique channels
            for (const topic of topicTables){
                try {
                    // Use distinct to get unique channel names without arbitrary limits
                    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topic.name).select('channel_name').not('channel_name', 'is', null).order('channel_name');
                    if (!error && data) {
                        console.log(`Found ${data.length} channel entries in ${topic.name}`);
                        data.forEach((item)=>{
                            if (item.channel_name && item.channel_name.trim()) {
                                allChannels.add(item.channel_name.trim());
                            }
                        });
                    } else if (error) {
                        console.warn(`Error fetching channels from ${topic.name}:`, error.message);
                    }
                } catch (tableError) {
                    console.warn(`Error accessing table ${topic.name}:`, tableError);
                // Continue with other tables
                }
            }
            const channels = Array.from(allChannels).sort();
            console.log(`Total unique channels found: ${channels.length}`);
            // If no channels found in database, return some sample channels for development
            if (channels.length === 0) {
                console.warn('No channels found in database, returning sample channels for development');
                return [
                    'Sample Tech Channel',
                    'Sample Programming Channel',
                    'Sample AI Channel',
                    'Sample Science Channel'
                ];
            }
            return channels;
        } catch (error) {
            console.error('Error fetching channels:', error);
            // Return sample channels for development when database is not accessible
            return [
                'Sample Tech Channel',
                'Sample Programming Channel',
                'Sample AI Channel',
                'Sample Science Channel'
            ];
        }
    }
    static async getFilteredVideos(topicTables, channels, limit = 20, offset = 0, startDate, endDate) {
        try {
            const allVideos = [];
            const seenVideoIds = new Set() // Track video IDs to prevent duplicates
            ;
            // Get available topic tables if not provided
            const tablesToQuery = topicTables && topicTables.length > 0 ? topicTables : (await this.getTopicTables()).map((t)=>t.table_name); // Use table_name
            // Calculate how many videos to fetch from each table to account for deduplication
            // When we have filters (channels, dates), we need to be more generous with the limit
            // to ensure we get enough results after filtering and deduplication
            const hasFilters = channels && channels.length > 0 || startDate || endDate;
            const multiplier = hasFilters ? 3 : 2 // Be more generous when filtering
            ;
            const perTableLimit = Math.max((limit + offset) * multiplier, 200);
            // Query each table
            for (const tableName of tablesToQuery){
                try {
                    let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(tableName).select('*').order('published_at', {
                        ascending: false,
                        nullsLast: true
                    });
                    // Filter by channels if provided
                    if (channels && channels.length > 0) {
                        query = query.in('channel_name', channels);
                    }
                    // Filter by date range if provided
                    if (startDate) {
                        query = query.gte('published_at', startDate);
                    }
                    if (endDate) {
                        query = query.lte('published_at', endDate);
                    }
                    const { data, error } = await query.limit(perTableLimit);
                    if (!error && data) {
                        // Add videos, but only if we haven't seen this video_id before
                        for (const video of data){
                            if (!seenVideoIds.has(video.video_id)) {
                                seenVideoIds.add(video.video_id);
                                allVideos.push(video);
                            }
                        }
                    }
                } catch (tableError) {
                    console.warn(`Error fetching videos from ${tableName}:`, tableError);
                // Continue with other tables
                }
            }
            // Sort all videos by published date (most recent first)
            // Only use published_at, don't fall back to database timestamps
            const sortedVideos = allVideos.sort((a, b)=>{
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            });
            // Apply pagination after sorting and deduplication
            return sortedVideos.slice(offset, offset + limit);
        } catch (error) {
            console.error('Error fetching filtered videos:', error);
            return [];
        }
    }
    // Get videos from a specific topic table
    static async getVideosByTopic(topicTable, limit = 20, offset = 0) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topicTable).select('*')// Get more records to account for sorting
            .limit(limit * 2);
            if (error) throw error;
            // Sort by published_at only and apply pagination
            const sortedData = (data || []).sort((a, b)=>{
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            });
            return sortedData.slice(offset, offset + limit);
        } catch (error) {
            console.error(`Error fetching videos from ${topicTable}:`, error);
            return [];
        }
    }
    // Get a single video by ID from a specific topic table or across all tables
    static async getVideoById(topicNameFromUrl, videoId) {
        let video = null;
        let attemptedSpecificTable = false;
        let specificTableQueryError = null;
        // Validate topicNameFromUrl and videoId
        if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {
            console.error(`getVideoById: Called with invalid videoId: '${videoId}'. Cannot fetch video.`);
            return null;
        }
        if (topicNameFromUrl && typeof topicNameFromUrl === 'string' && topicNameFromUrl.trim() !== '') {
            const actualTableName = topicNameFromUrl.startsWith('youtube_') ? topicNameFromUrl : `youtube_${topicNameFromUrl}`;
            console.log(`getVideoById: Attempting to fetch videoId '${videoId}' from specific table '${actualTableName}' (original topic: '${topicNameFromUrl}')`);
            attemptedSpecificTable = true;
            try {
                const { data, error: supabaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(actualTableName).select('*').eq('video_id', videoId).single();
                if (supabaseError) {
                    specificTableQueryError = supabaseError; // Store error for logging
                    console.warn(`getVideoById: Supabase error fetching videoId '${videoId}' from table '${actualTableName}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}. Will try searching all tables.`);
                // Do not throw here, let it fall through to findVideoAcrossAllTables logic
                } else if (data) {
                    console.log(`getVideoById: Successfully found videoId '${videoId}' in table '${actualTableName}'`);
                    const simpleTopicName = actualTableName.startsWith('youtube_') ? actualTableName.substring(8) : actualTableName;
                    // Ensure llm_response is at least an empty object if null/undefined from DB
                    video = {
                        ...data,
                        topic_category: simpleTopicName,
                        llm_response: data.llm_response || {}
                    };
                } else {
                    // This case (no error, no data with .single()) should ideally not happen as .single() errors out.
                    console.warn(`getVideoById: No data and no error for videoId '${videoId}' from table '${actualTableName}'. This is unexpected with .single(). Will try searching all tables.`);
                }
            } catch (catchedError) {
                specificTableQueryError = catchedError;
                const errorMessage = catchedError instanceof Error ? catchedError.message : String(catchedError);
                console.error(`getVideoById: Exception during fetch from specific table '${actualTableName}' for videoId '${videoId}'. Error: ${errorMessage}. Will try searching all tables.`);
            }
        } else {
            console.warn(`getVideoById: Called with invalid or empty topicNameFromUrl ('${topicNameFromUrl}'). Proceeding directly to search across all tables for videoId: '${videoId}'.`);
        }
        // If video not found in specific table (or specific table was not attempted/valid)
        if (!video) {
            if (attemptedSpecificTable) {
                let errMessage = "No specific error object";
                if (specificTableQueryError) {
                    if (specificTableQueryError instanceof Error) {
                        errMessage = specificTableQueryError.message;
                    } else {
                        try {
                            errMessage = JSON.stringify(specificTableQueryError);
                        } catch  {
                            errMessage = "Could not stringify error object";
                        }
                    }
                }
                console.warn(`getVideoById: VideoId '${videoId}' not found in specific table or an error occurred. Specific error (if any): ${errMessage}`);
            }
            console.log(`getVideoById: Falling back to findVideoAcrossAllTables for videoId '${videoId}'.`);
            try {
                const videoFromFallback = await this.findVideoAcrossAllTables(videoId);
                if (videoFromFallback) {
                    console.log(`getVideoById: Found videoId '${videoId}' via findVideoAcrossAllTables.`);
                    // Ensure llm_response is at least an empty object if null/undefined from DB
                    video = {
                        ...videoFromFallback,
                        llm_response: videoFromFallback.llm_response || {}
                    };
                } else {
                    console.error(`getVideoById: VideoId '${videoId}' ultimately NOT FOUND even after searching all tables.`);
                }
            } catch (fallbackError) {
                const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
                console.error(`getVideoById: CRITICAL ERROR during findVideoAcrossAllTables for videoId '${videoId}'. Error: ${fallbackErrorMessage}`);
            // video remains null
            }
        }
        if (!video) {
            const specificErrorMsg = specificTableQueryError instanceof Error ? specificTableQueryError.message : specificTableQueryError ? "Error object present" : "N/A";
            console.error(`[FINAL RESULT] getVideoById for videoId '${videoId}' (original topic: '${topicNameFromUrl}'): Video NOT FOUND. Specific table attempt error (if any): ${specificErrorMsg}`);
        } else {
            // Ensure topic_category is sensible
            if (!video.topic_category && topicNameFromUrl) {
                video.topic_category = topicNameFromUrl.replace('youtube_', '');
            } else if (!video.topic_category) {
                video.topic_category = 'unknown'; // Default if no topic info at all
            }
        }
        return video;
    }
    // Find a video by ID across all topic tables
    static async findVideoAcrossAllTables(videoId) {
        console.log(`findVideoAcrossAllTables: Searching for videoId '${videoId}'`);
        if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {
            console.error(`findVideoAcrossAllTables: Called with invalid videoId: '${videoId}'.`);
            return null;
        }
        try {
            const topics = await this.getTopicTables();
            if (!topics || topics.length === 0) {
                console.warn("findVideoAcrossAllTables: No topic tables found to search in.");
                return null;
            }
            for (const topic of topics){
                try {
                    const tableNameToQuery = topic.table_name;
                    if (!tableNameToQuery) {
                        console.warn(`findVideoAcrossAllTables: Topic '${topic.name}' has no valid table_name. Skipping.`);
                        continue;
                    }
                    console.log(`findVideoAcrossAllTables: Checking table '${tableNameToQuery}' for videoId '${videoId}'`);
                    const { data, error: supabaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(tableNameToQuery).select('*').eq('video_id', videoId).single();
                    if (!supabaseError && data) {
                        console.log(`findVideoAcrossAllTables: Found videoId '${videoId}' in table '${tableNameToQuery}'`);
                        const simpleTopicName = topic.name.startsWith('youtube_') ? topic.name.substring(8) : topic.name;
                        // Ensure llm_response is at least an empty object if null/undefined from DB
                        return {
                            ...data,
                            topic_category: simpleTopicName,
                            llm_response: data.llm_response || {}
                        };
                    }
                    if (supabaseError && supabaseError.code !== 'PGRST116') {
                        console.warn(`findVideoAcrossAllTables: Supabase error querying table '${tableNameToQuery}' for videoId '${videoId}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}`);
                    }
                } catch (tableQueryError) {
                    const tableQueryErrorMessage = tableQueryError instanceof Error ? tableQueryError.message : String(tableQueryError);
                    console.warn(`findVideoAcrossAllTables: Exception while querying table '${topic.table_name}' for videoId '${videoId}'. Message: ${tableQueryErrorMessage}`);
                }
            }
            console.log(`findVideoAcrossAllTables: VideoId '${videoId}' not found in any topic table after checking all.`);
            return null;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(`findVideoAcrossAllTables: General error searching for videoId '${videoId}' across all tables. Message: ${errorMessage}`);
            return null;
        }
    }
    // Search videos across all topic tables using vector similarity
    static async searchVideos(query, topicFilters = [], limit = 10) {
        try {
            // This will call our Python AI service to generate embeddings
            const embeddingResponse = await fetch('/api/embeddings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: query
                })
            });
            if (!embeddingResponse.ok) throw new Error('Failed to generate embedding');
            const { embedding } = await embeddingResponse.json();
            // Search across specified topic tables or all tables
            const searchPromises = topicFilters.length > 0 ? topicFilters.map((table)=>this.searchInTable(table, embedding, limit)) : await this.searchAllTables(embedding, limit);
            const results = await Promise.all(searchPromises);
            // Flatten and sort by similarity score
            return results.flat().sort((a, b)=>b.similarity_score - a.similarity_score).slice(0, limit);
        } catch (error) {
            console.error('Error searching videos:', error);
            return [];
        }
    }
    static async searchInTable(tableName, embedding, limit) {
        try {
            // Use pgvector similarity search
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabaseAdmin"].rpc('search_videos_by_embedding', {
                table_name: tableName,
                query_embedding: embedding,
                match_threshold: 0.7,
                match_count: limit
            });
            if (error) throw error;
            return (data || []).map((item)=>({
                    video: item.video,
                    similarity_score: item.similarity_score,
                    relevant_transcript_chunks: item.relevant_chunks
                }));
        } catch (error) {
            console.error(`Error searching in table ${tableName}:`, error);
            return [];
        }
    }
    static async searchAllTables(embedding, limit) {
        const topics = await this.getTopicTables();
        return Promise.all(topics.map((topic)=>this.searchInTable(topic.name, embedding, limit)));
    }
    // Get recent videos across all topics
    static async getRecentVideos(limit = 20) {
        try {
            console.log('🔄 getRecentVideos called with limit:', limit);
            const topics = await this.getTopicTables();
            console.log('🔄 Found topics:', topics.length, topics.map((t)=>t.table_name));
            const videoPromises = topics.map((topic)=>this.getVideosByTopic(topic.table_name, Math.ceil(limit / topics.length)) // Use table_name
            );
            const videoArrays = await Promise.all(videoPromises);
            console.log('🔄 Video arrays lengths:', videoArrays.map((arr)=>arr.length));
            const allVideos = videoArrays.flat();
            console.log('🔄 Total videos before sorting:', allVideos.length);
            // Sort by published date and return top results
            // Only use published_at, don't fall back to database timestamps
            const sortedVideos = allVideos.sort((a, b)=>{
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            }).slice(0, limit);
            console.log('🔄 Final sorted videos:', sortedVideos.length);
            return sortedVideos;
        } catch (error) {
            console.error('Error fetching recent videos:', error);
            return [];
        }
    }
}
}}),
"[project]/src/app/api/videos/recent/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '20');
        const topics = searchParams.get('topics')?.split(',').filter(Boolean) || [];
        let videos;
        if (topics.length > 0) {
            const videoPromises = topics.map((topic)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DatabaseService"].getVideosByTopic(topic, Math.ceil(limit / topics.length)));
            const videoArrays = await Promise.all(videoPromises);
            videos = videoArrays.flat().sort((a, b)=>{
                // Only use published_at, don't fall back to database timestamps
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            }).slice(0, limit);
        } else {
            // Get recent videos across all topics
            console.log('🎯 Calling getRecentVideos with limit:', limit);
            videos = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DatabaseService"].getRecentVideos(limit);
            console.log('🎯 getRecentVideos returned:', videos.length, 'videos');
            console.log('🎯 First video sample:', videos[0]);
        }
        const responseData = {
            videos,
            count: videos.length,
            topics: topics.length > 0 ? topics : 'all'
        };
        console.log('🎯 Final API response structure:', {
            videosCount: videos.length,
            videosIsArray: Array.isArray(videos),
            firstVideoTitle: videos[0]?.title,
            actualResponseKeys: Object.keys(responseData)
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(responseData);
    } catch (error) {
        console.error('Error in recent videos API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch recent videos'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__93dd2c40._.js.map