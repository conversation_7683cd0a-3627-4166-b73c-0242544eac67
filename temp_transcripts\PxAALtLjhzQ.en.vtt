WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.630 align:start position:0%
 
hey<00:00:00.199><c> and</c><00:00:00.320><c> welcome</c><00:00:00.560><c> back</c><00:00:00.680><c> to</c><00:00:00.880><c> another</c><00:00:01.280><c> video</c>

00:00:01.630 --> 00:00:01.640 align:start position:0%
hey and welcome back to another video
 

00:00:01.640 --> 00:00:03.990 align:start position:0%
hey and welcome back to another video
and<00:00:01.800><c> today</c><00:00:02.200><c> is</c><00:00:02.440><c> day</c><00:00:02.639><c> 21</c><00:00:03.159><c> of</c><00:00:03.280><c> the</c><00:00:03.399><c> 31-day</c>

00:00:03.990 --> 00:00:04.000 align:start position:0%
and today is day 21 of the 31-day
 

00:00:04.000 --> 00:00:05.510 align:start position:0%
and today is day 21 of the 31-day
challenge<00:00:04.440><c> and</c><00:00:04.600><c> we</c><00:00:04.720><c> are</c><00:00:04.880><c> going</c><00:00:05.040><c> to</c><00:00:05.120><c> be</c><00:00:05.279><c> talking</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
challenge and we are going to be talking
 

00:00:05.520 --> 00:00:07.789 align:start position:0%
challenge and we are going to be talking
about<00:00:05.799><c> nested</c><00:00:06.240><c> chatting</c><00:00:06.799><c> in</c><00:00:07.000><c> Auto</c><00:00:07.279><c> gem</c><00:00:07.560><c> so</c><00:00:07.680><c> in</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
about nested chatting in Auto gem so in
 

00:00:07.799 --> 00:00:09.549 align:start position:0%
about nested chatting in Auto gem so in
this<00:00:08.000><c> diagram</c><00:00:08.679><c> the</c><00:00:08.880><c> thing</c><00:00:09.040><c> about</c><00:00:09.200><c> nested</c>

00:00:09.549 --> 00:00:09.559 align:start position:0%
this diagram the thing about nested
 

00:00:09.559 --> 00:00:11.150 align:start position:0%
this diagram the thing about nested
chatting<00:00:09.920><c> is</c><00:00:10.040><c> it's</c><00:00:10.200><c> just</c><00:00:10.360><c> another</c><00:00:10.639><c> way</c><00:00:10.800><c> to</c>

00:00:11.150 --> 00:00:11.160 align:start position:0%
chatting is it's just another way to
 

00:00:11.160 --> 00:00:13.070 align:start position:0%
chatting is it's just another way to
organize<00:00:11.799><c> how</c><00:00:11.960><c> your</c><00:00:12.160><c> agents</c><00:00:12.519><c> interact</c><00:00:12.920><c> with</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
organize how your agents interact with
 

00:00:13.080 --> 00:00:14.869 align:start position:0%
organize how your agents interact with
each<00:00:13.200><c> other</c><00:00:13.639><c> let's</c><00:00:13.799><c> take</c><00:00:13.960><c> this</c><00:00:14.160><c> example</c><00:00:14.639><c> where</c>

00:00:14.869 --> 00:00:14.879 align:start position:0%
each other let's take this example where
 

00:00:14.879 --> 00:00:17.550 align:start position:0%
each other let's take this example where
we<00:00:15.080><c> have</c><00:00:15.480><c> three</c><00:00:15.799><c> total</c><00:00:16.199><c> agents</c><00:00:16.600><c> we</c><00:00:16.760><c> have</c><00:00:17.160><c> user</c>

00:00:17.550 --> 00:00:17.560 align:start position:0%
we have three total agents we have user
 

00:00:17.560 --> 00:00:19.710 align:start position:0%
we have three total agents we have user
writer<00:00:18.000><c> and</c><00:00:18.359><c> critic</c><00:00:18.960><c> now</c><00:00:19.080><c> on</c><00:00:19.199><c> the</c><00:00:19.320><c> right</c><00:00:19.520><c> here</c>

00:00:19.710 --> 00:00:19.720 align:start position:0%
writer and critic now on the right here
 

00:00:19.720 --> 00:00:21.950 align:start position:0%
writer and critic now on the right here
I<00:00:19.840><c> have</c><00:00:20.240><c> Snippets</c><00:00:20.760><c> of</c><00:00:20.960><c> code</c><00:00:21.359><c> of</c><00:00:21.560><c> what</c><00:00:21.680><c> it</c><00:00:21.760><c> would</c>

00:00:21.950 --> 00:00:21.960 align:start position:0%
I have Snippets of code of what it would
 

00:00:21.960 --> 00:00:23.710 align:start position:0%
I have Snippets of code of what it would
look<00:00:22.160><c> like</c><00:00:22.439><c> you're</c><00:00:22.560><c> used</c><00:00:22.800><c> to</c><00:00:22.880><c> the</c><00:00:23.000><c> user</c><00:00:23.279><c> proxy</c>

00:00:23.710 --> 00:00:23.720 align:start position:0%
look like you're used to the user proxy
 

00:00:23.720 --> 00:00:25.830 align:start position:0%
look like you're used to the user proxy
initiate<00:00:24.199><c> chat</c><00:00:24.800><c> the</c><00:00:24.960><c> recipient</c><00:00:25.480><c> is</c><00:00:25.599><c> who</c><00:00:25.760><c> the</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
initiate chat the recipient is who the
 

00:00:25.840 --> 00:00:27.310 align:start position:0%
initiate chat the recipient is who the
user<00:00:26.080><c> proxy</c><00:00:26.359><c> is</c><00:00:26.400><c> going</c><00:00:26.480><c> to</c><00:00:26.560><c> initiate</c><00:00:26.880><c> the</c><00:00:26.960><c> chat</c>

00:00:27.310 --> 00:00:27.320 align:start position:0%
user proxy is going to initiate the chat
 

00:00:27.320 --> 00:00:28.830 align:start position:0%
user proxy is going to initiate the chat
with<00:00:27.880><c> well</c><00:00:28.119><c> this</c><00:00:28.279><c> time</c><00:00:28.519><c> we're</c><00:00:28.679><c> going</c><00:00:28.760><c> to</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
with well this time we're going to
 

00:00:28.840 --> 00:00:30.390 align:start position:0%
with well this time we're going to
initiate<00:00:29.119><c> the</c><00:00:29.199><c> chat</c><00:00:29.359><c> with</c><00:00:29.480><c> the</c><00:00:29.599><c> writer</c><00:00:30.240><c> and</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
initiate the chat with the writer and
 

00:00:30.400 --> 00:00:32.150 align:start position:0%
initiate the chat with the writer and
the<00:00:30.560><c> message</c><00:00:30.800><c> is</c><00:00:30.920><c> going</c><00:00:31.039><c> to</c><00:00:31.160><c> be</c><00:00:31.279><c> some</c><00:00:31.640><c> task</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
the message is going to be some task
 

00:00:32.160 --> 00:00:33.310 align:start position:0%
the message is going to be some task
this<00:00:32.239><c> is</c><00:00:32.360><c> something</c><00:00:32.640><c> that</c><00:00:32.800><c> you'll</c><00:00:32.960><c> see</c><00:00:33.120><c> in</c><00:00:33.200><c> the</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
this is something that you'll see in the
 

00:00:33.320 --> 00:00:34.430 align:start position:0%
this is something that you'll see in the
code<00:00:33.520><c> that</c><00:00:33.600><c> we</c><00:00:33.719><c> give</c><00:00:33.840><c> it</c><00:00:34.000><c> you</c><00:00:34.120><c> see</c><00:00:34.280><c> that</c><00:00:34.360><c> we</c>

00:00:34.430 --> 00:00:34.440 align:start position:0%
code that we give it you see that we
 

00:00:34.440 --> 00:00:35.990 align:start position:0%
code that we give it you see that we
initiate<00:00:34.760><c> the</c><00:00:34.840><c> chat</c><00:00:35.040><c> with</c><00:00:35.160><c> the</c><00:00:35.320><c> writer</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
initiate the chat with the writer
 

00:00:36.000 --> 00:00:38.830 align:start position:0%
initiate the chat with the writer
however<00:00:36.480><c> we</c><00:00:36.800><c> also</c><00:00:37.200><c> have</c><00:00:37.600><c> a</c><00:00:38.079><c> another</c><00:00:38.559><c> agent</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
however we also have a another agent
 

00:00:38.840 --> 00:00:41.110 align:start position:0%
however we also have a another agent
called<00:00:39.120><c> critic</c><00:00:39.760><c> well</c><00:00:40.160><c> before</c><00:00:40.640><c> this</c><00:00:40.960><c> we're</c>

00:00:41.110 --> 00:00:41.120 align:start position:0%
called critic well before this we're
 

00:00:41.120 --> 00:00:43.470 align:start position:0%
called critic well before this we're
going<00:00:41.239><c> to</c><00:00:41.360><c> say</c><00:00:41.520><c> user</c><00:00:41.960><c> proxy</c><00:00:42.719><c> do</c><00:00:43.000><c> register</c>

00:00:43.470 --> 00:00:43.480 align:start position:0%
going to say user proxy do register
 

00:00:43.480 --> 00:00:45.270 align:start position:0%
going to say user proxy do register
nested<00:00:43.960><c> chats</c><00:00:44.680><c> and</c><00:00:44.760><c> we're</c><00:00:44.879><c> going</c><00:00:44.960><c> to</c><00:00:45.039><c> say</c><00:00:45.160><c> the</c>

00:00:45.270 --> 00:00:45.280 align:start position:0%
nested chats and we're going to say the
 

00:00:45.280 --> 00:00:48.430 align:start position:0%
nested chats and we're going to say the
recipient<00:00:45.879><c> is</c><00:00:46.079><c> the</c><00:00:46.239><c> critic</c><00:00:47.039><c> and</c><00:00:47.239><c> the</c><00:00:47.480><c> trigger</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
recipient is the critic and the trigger
 

00:00:48.440 --> 00:00:51.029 align:start position:0%
recipient is the critic and the trigger
is<00:00:48.600><c> the</c><00:00:48.840><c> writer</c><00:00:49.480><c> what</c><00:00:49.640><c> that</c><00:00:49.800><c> means</c><00:00:50.320><c> is</c><00:00:50.800><c> after</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
is the writer what that means is after
 

00:00:51.039 --> 00:00:53.029 align:start position:0%
is the writer what that means is after
the<00:00:51.120><c> user</c><00:00:51.360><c> is</c><00:00:51.520><c> done</c><00:00:51.719><c> talking</c><00:00:52.000><c> to</c><00:00:52.199><c> the</c><00:00:52.359><c> writer</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
the user is done talking to the writer
 

00:00:53.039 --> 00:00:55.750 align:start position:0%
the user is done talking to the writer
whenever<00:00:53.960><c> that</c><00:00:54.160><c> chat</c><00:00:54.520><c> is</c><00:00:54.800><c> triggered</c><00:00:55.600><c> when</c>

00:00:55.750 --> 00:00:55.760 align:start position:0%
whenever that chat is triggered when
 

00:00:55.760 --> 00:00:57.950 align:start position:0%
whenever that chat is triggered when
they're<00:00:55.960><c> done</c><00:00:56.520><c> then</c><00:00:56.960><c> the</c><00:00:57.079><c> user</c><00:00:57.440><c> proxy</c><00:00:57.840><c> is</c>

00:00:57.950 --> 00:00:57.960 align:start position:0%
they're done then the user proxy is
 

00:00:57.960 --> 00:00:59.709 align:start position:0%
they're done then the user proxy is
going<00:00:58.079><c> to</c><00:00:58.239><c> start</c><00:00:58.480><c> talking</c><00:00:58.800><c> to</c><00:00:59.000><c> the</c><00:00:59.199><c> critic</c><00:00:59.600><c> so</c>

00:00:59.709 --> 00:00:59.719 align:start position:0%
going to start talking to the critic so
 

00:00:59.719 --> 00:01:01.389 align:start position:0%
going to start talking to the critic so
the<00:01:00.000><c> flow</c><00:01:00.320><c> is</c><00:01:00.480><c> the</c><00:01:00.600><c> user</c><00:01:00.800><c> is</c><00:01:00.879><c> going</c><00:01:01.000><c> to</c><00:01:01.120><c> talk</c><00:01:01.280><c> to</c>

00:01:01.389 --> 00:01:01.399 align:start position:0%
the flow is the user is going to talk to
 

00:01:01.399 --> 00:01:03.389 align:start position:0%
the flow is the user is going to talk to
the<00:01:01.519><c> writer</c><00:01:02.199><c> the</c><00:01:02.359><c> writer</c><00:01:02.719><c> will</c><00:01:03.160><c> give</c>

00:01:03.389 --> 00:01:03.399 align:start position:0%
the writer the writer will give
 

00:01:03.399 --> 00:01:05.789 align:start position:0%
the writer the writer will give
something<00:01:03.840><c> back</c><00:01:04.199><c> from</c><00:01:04.439><c> the</c><00:01:04.600><c> llm</c><00:01:05.159><c> call</c><00:01:05.519><c> back</c><00:01:05.640><c> to</c>

00:01:05.789 --> 00:01:05.799 align:start position:0%
something back from the llm call back to
 

00:01:05.799 --> 00:01:08.710 align:start position:0%
something back from the llm call back to
the<00:01:05.920><c> user</c><00:01:06.600><c> and</c><00:01:06.760><c> then</c><00:01:07.200><c> because</c><00:01:07.920><c> the</c><00:01:08.360><c> user</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
the user and then because the user
 

00:01:08.720 --> 00:01:11.030 align:start position:0%
the user and then because the user
initiated<00:01:09.240><c> the</c><00:01:09.400><c> chat</c><00:01:09.840><c> with</c><00:01:10.040><c> the</c><00:01:10.200><c> writer</c><00:01:10.840><c> this</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
initiated the chat with the writer this
 

00:01:11.040 --> 00:01:13.149 align:start position:0%
initiated the chat with the writer this
triggered<00:01:11.920><c> the</c><00:01:12.080><c> nested</c><00:01:12.520><c> chat</c><00:01:12.880><c> with</c><00:01:13.040><c> the</c>

00:01:13.149 --> 00:01:13.159 align:start position:0%
triggered the nested chat with the
 

00:01:13.159 --> 00:01:16.109 align:start position:0%
triggered the nested chat with the
critic<00:01:13.840><c> so</c><00:01:14.080><c> now</c><00:01:14.400><c> the</c><00:01:14.520><c> user</c><00:01:14.920><c> proxy</c><00:01:15.680><c> is</c><00:01:15.799><c> going</c><00:01:16.000><c> to</c>

00:01:16.109 --> 00:01:16.119 align:start position:0%
critic so now the user proxy is going to
 

00:01:16.119 --> 00:01:18.149 align:start position:0%
critic so now the user proxy is going to
start<00:01:16.360><c> talking</c><00:01:16.840><c> with</c><00:01:17.000><c> the</c><00:01:17.159><c> critic</c><00:01:17.680><c> and</c><00:01:18.000><c> here</c>

00:01:18.149 --> 00:01:18.159 align:start position:0%
start talking with the critic and here
 

00:01:18.159 --> 00:01:20.350 align:start position:0%
start talking with the critic and here
we<00:01:18.280><c> only</c><00:01:18.439><c> have</c><00:01:18.560><c> a</c><00:01:18.680><c> Max</c><00:01:19.000><c> turn</c><00:01:19.360><c> of</c><00:01:19.560><c> one</c><00:01:19.960><c> so</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
we only have a Max turn of one so
 

00:01:20.360 --> 00:01:22.749 align:start position:0%
we only have a Max turn of one so
they'll<00:01:20.640><c> just</c><00:01:20.960><c> talk</c><00:01:21.479><c> one</c><00:01:21.759><c> time</c><00:01:21.920><c> to</c><00:01:22.119><c> each</c><00:01:22.240><c> other</c>

00:01:22.749 --> 00:01:22.759 align:start position:0%
they'll just talk one time to each other
 

00:01:22.759 --> 00:01:24.710 align:start position:0%
they'll just talk one time to each other
and<00:01:22.920><c> then</c><00:01:23.079><c> once</c><00:01:23.320><c> that's</c><00:01:23.640><c> done</c><00:01:24.200><c> then</c><00:01:24.320><c> the</c><00:01:24.400><c> user</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
and then once that's done then the user
 

00:01:24.720 --> 00:01:26.590 align:start position:0%
and then once that's done then the user
will<00:01:25.040><c> again</c><00:01:25.320><c> talk</c><00:01:25.520><c> to</c><00:01:25.680><c> the</c><00:01:25.799><c> writer</c><00:01:26.280><c> then</c><00:01:26.400><c> we're</c>

00:01:26.590 --> 00:01:26.600 align:start position:0%
will again talk to the writer then we're
 

00:01:26.600 --> 00:01:28.830 align:start position:0%
will again talk to the writer then we're
going<00:01:26.680><c> to</c><00:01:26.840><c> be</c><00:01:27.000><c> done</c><00:01:27.360><c> we're</c><00:01:27.600><c> inserting</c><00:01:28.320><c> a</c><00:01:28.520><c> sub</c>

00:01:28.830 --> 00:01:28.840 align:start position:0%
going to be done we're inserting a sub
 

00:01:28.840 --> 00:01:31.749 align:start position:0%
going to be done we're inserting a sub
chat<00:01:29.520><c> inside</c><00:01:30.000><c> side</c><00:01:30.400><c> of</c><00:01:30.680><c> the</c><00:01:30.840><c> agent</c><00:01:31.200><c> chat</c><00:01:31.640><c> so</c>

00:01:31.749 --> 00:01:31.759 align:start position:0%
chat inside side of the agent chat so
 

00:01:31.759 --> 00:01:33.149 align:start position:0%
chat inside side of the agent chat so
the<00:01:31.920><c> critic</c><00:01:32.200><c> is</c><00:01:32.320><c> never</c><00:01:32.520><c> going</c><00:01:32.640><c> to</c><00:01:32.799><c> talk</c>

00:01:33.149 --> 00:01:33.159 align:start position:0%
the critic is never going to talk
 

00:01:33.159 --> 00:01:34.910 align:start position:0%
the critic is never going to talk
directly<00:01:33.680><c> back</c><00:01:33.840><c> to</c><00:01:34.000><c> the</c><00:01:34.159><c> writer</c><00:01:34.479><c> it's</c><00:01:34.799><c> it's</c>

00:01:34.910 --> 00:01:34.920 align:start position:0%
directly back to the writer it's it's
 

00:01:34.920 --> 00:01:36.389 align:start position:0%
directly back to the writer it's it's
just<00:01:35.000><c> going</c><00:01:35.079><c> to</c><00:01:35.159><c> be</c><00:01:35.240><c> the</c><00:01:35.320><c> user</c><00:01:35.720><c> talking</c><00:01:36.040><c> to</c><00:01:36.240><c> the</c>

00:01:36.389 --> 00:01:36.399 align:start position:0%
just going to be the user talking to the
 

00:01:36.399 --> 00:01:37.749 align:start position:0%
just going to be the user talking to the
critic<00:01:36.799><c> let's</c><00:01:36.920><c> go</c><00:01:37.040><c> over</c><00:01:37.159><c> the</c><00:01:37.280><c> code</c><00:01:37.479><c> and</c><00:01:37.600><c> see</c>

00:01:37.749 --> 00:01:37.759 align:start position:0%
critic let's go over the code and see
 

00:01:37.759 --> 00:01:39.190 align:start position:0%
critic let's go over the code and see
how<00:01:37.960><c> it</c><00:01:38.079><c> looks</c><00:01:38.479><c> now</c><00:01:38.600><c> with</c><00:01:38.680><c> the</c><00:01:38.840><c> code</c><00:01:39.079><c> we're</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
how it looks now with the code we're
 

00:01:39.200 --> 00:01:40.950 align:start position:0%
how it looks now with the code we're
going<00:01:39.320><c> to</c><00:01:39.479><c> only</c><00:01:39.880><c> have</c><00:01:40.200><c> two</c><00:01:40.399><c> different</c><00:01:40.640><c> files</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
going to only have two different files
 

00:01:40.960 --> 00:01:42.510 align:start position:0%
going to only have two different files
we're<00:01:41.079><c> going</c><00:01:41.159><c> to</c><00:01:41.240><c> have</c><00:01:41.399><c> the</c><00:01:41.479><c> main</c><00:01:41.759><c> python</c><00:01:42.159><c> file</c>

00:01:42.510 --> 00:01:42.520 align:start position:0%
we're going to have the main python file
 

00:01:42.520 --> 00:01:44.230 align:start position:0%
we're going to have the main python file
and<00:01:42.600><c> then</c><00:01:42.720><c> the</c><00:01:42.840><c> config</c><00:01:43.240><c> list</c><00:01:43.399><c> Json</c><00:01:43.840><c> file</c><00:01:44.119><c> so</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
and then the config list Json file so
 

00:01:44.240 --> 00:01:45.709 align:start position:0%
and then the config list Json file so
with<00:01:44.320><c> the</c><00:01:44.439><c> main</c><00:01:44.719><c> python</c><00:01:45.079><c> file</c><00:01:45.439><c> we're</c><00:01:45.600><c> just</c>

00:01:45.709 --> 00:01:45.719 align:start position:0%
with the main python file we're just
 

00:01:45.719 --> 00:01:47.749 align:start position:0%
with the main python file we're just
going<00:01:45.799><c> to</c><00:01:45.920><c> be</c><00:01:46.079><c> importing</c><00:01:46.719><c> this</c><00:01:46.880><c> Json</c><00:01:47.439><c> we</c><00:01:47.560><c> set</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
going to be importing this Json we set
 

00:01:47.759 --> 00:01:49.990 align:start position:0%
going to be importing this Json we set
the<00:01:47.880><c> llm</c><00:01:48.399><c> to</c><00:01:48.560><c> the</c><00:01:48.680><c> config</c><00:01:49.079><c> list</c><00:01:49.439><c> our</c><00:01:49.600><c> simple</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
the llm to the config list our simple
 

00:01:50.000 --> 00:01:52.030 align:start position:0%
the llm to the config list our simple
task<00:01:50.280><c> is</c><00:01:50.399><c> to</c><00:01:50.560><c> write</c><00:01:50.759><c> a</c><00:01:50.920><c> concise</c><00:01:51.360><c> but</c><00:01:51.520><c> engaging</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
task is to write a concise but engaging
 

00:01:52.040 --> 00:01:53.910 align:start position:0%
task is to write a concise but engaging
blog<00:01:52.399><c> post</c><00:01:52.759><c> about</c><00:01:53.000><c> meta</c><00:01:53.399><c> now</c><00:01:53.520><c> again</c><00:01:53.680><c> we</c><00:01:53.759><c> have</c>

00:01:53.910 --> 00:01:53.920 align:start position:0%
blog post about meta now again we have
 

00:01:53.920 --> 00:01:55.590 align:start position:0%
blog post about meta now again we have
three<00:01:54.079><c> agents</c><00:01:54.360><c> so</c><00:01:54.479><c> we</c><00:01:54.600><c> have</c><00:01:54.719><c> the</c><00:01:54.840><c> writer</c><00:01:55.159><c> agent</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
three agents so we have the writer agent
 

00:01:55.600 --> 00:01:57.429 align:start position:0%
three agents so we have the writer agent
with<00:01:55.759><c> a</c><00:01:55.920><c> system</c><00:01:56.200><c> message</c><00:01:56.479><c> about</c><00:01:56.680><c> them</c><00:01:56.840><c> being</c><00:01:57.240><c> a</c>

00:01:57.429 --> 00:01:57.439 align:start position:0%
with a system message about them being a
 

00:01:57.439 --> 00:01:59.310 align:start position:0%
with a system message about them being a
professional<00:01:57.840><c> writer</c><00:01:58.240><c> we</c><00:01:58.360><c> have</c><00:01:58.520><c> a</c><00:01:58.680><c> user</c><00:01:59.000><c> proxy</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
professional writer we have a user proxy
 

00:01:59.320 --> 00:02:00.910 align:start position:0%
professional writer we have a user proxy
agent<00:01:59.680><c> with</c><00:02:00.079><c> if</c><00:02:00.200><c> there</c><00:02:00.320><c> is</c><00:02:00.479><c> any</c><00:02:00.680><c> code</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
agent with if there is any code
 

00:02:00.920 --> 00:02:02.429 align:start position:0%
agent with if there is any code
execution<00:02:01.439><c> it's</c><00:02:01.560><c> going</c><00:02:01.680><c> to</c><00:02:02.039><c> create</c><00:02:02.320><c> a</c>

00:02:02.429 --> 00:02:02.439 align:start position:0%
execution it's going to create a
 

00:02:02.439 --> 00:02:04.550 align:start position:0%
execution it's going to create a
directory<00:02:02.840><c> called</c><00:02:03.039><c> myor</c><00:02:03.759><c> code</c><00:02:04.000><c> and</c><00:02:04.119><c> store</c><00:02:04.360><c> it</c>

00:02:04.550 --> 00:02:04.560 align:start position:0%
directory called myor code and store it
 

00:02:04.560 --> 00:02:05.910 align:start position:0%
directory called myor code and store it
there<00:02:04.799><c> and</c><00:02:04.880><c> then</c><00:02:05.000><c> we</c><00:02:05.119><c> have</c><00:02:05.280><c> the</c><00:02:05.399><c> critic</c><00:02:05.799><c> and</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
there and then we have the critic and
 

00:02:05.920 --> 00:02:07.870 align:start position:0%
there and then we have the critic and
the<00:02:06.039><c> critic</c><00:02:06.399><c> just</c><00:02:06.840><c> is</c><00:02:07.360><c> known</c><00:02:07.560><c> for</c><00:02:07.719><c> the</c>

00:02:07.870 --> 00:02:07.880 align:start position:0%
the critic just is known for the
 

00:02:07.880 --> 00:02:09.070 align:start position:0%
the critic just is known for the
thoroughness<00:02:08.319><c> and</c><00:02:08.440><c> commitment</c><00:02:08.800><c> to</c><00:02:08.920><c> the</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
thoroughness and commitment to the
 

00:02:09.080 --> 00:02:10.430 align:start position:0%
thoroughness and commitment to the
standards<00:02:09.560><c> basically</c><00:02:10.000><c> they're</c><00:02:10.200><c> just</c><00:02:10.319><c> going</c>

00:02:10.430 --> 00:02:10.440 align:start position:0%
standards basically they're just going
 

00:02:10.440 --> 00:02:11.990 align:start position:0%
standards basically they're just going
to<00:02:10.640><c> critique</c><00:02:11.039><c> what</c><00:02:11.160><c> the</c><00:02:11.239><c> writer</c><00:02:11.520><c> has</c><00:02:11.599><c> to</c><00:02:11.760><c> say</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
to critique what the writer has to say
 

00:02:12.000 --> 00:02:13.510 align:start position:0%
to critique what the writer has to say
I'll<00:02:12.120><c> come</c><00:02:12.280><c> back</c><00:02:12.400><c> to</c><00:02:12.840><c> I'll</c><00:02:13.000><c> come</c><00:02:13.120><c> back</c><00:02:13.239><c> to</c><00:02:13.360><c> this</c>

00:02:13.510 --> 00:02:13.520 align:start position:0%
I'll come back to I'll come back to this
 

00:02:13.520 --> 00:02:14.910 align:start position:0%
I'll come back to I'll come back to this
reflection<00:02:13.920><c> message</c><00:02:14.160><c> in</c><00:02:14.280><c> a</c><00:02:14.400><c> second</c><00:02:14.680><c> once</c><00:02:14.800><c> we</c>

00:02:14.910 --> 00:02:14.920 align:start position:0%
reflection message in a second once we
 

00:02:14.920 --> 00:02:16.750 align:start position:0%
reflection message in a second once we
go<00:02:15.040><c> over</c><00:02:15.160><c> the</c><00:02:15.440><c> nested</c><00:02:15.760><c> chat</c><00:02:16.040><c> so</c><00:02:16.280><c> before</c><00:02:16.560><c> we</c>

00:02:16.750 --> 00:02:16.760 align:start position:0%
go over the nested chat so before we
 

00:02:16.760 --> 00:02:18.270 align:start position:0%
go over the nested chat so before we
initiate<00:02:17.160><c> the</c><00:02:17.319><c> chat</c><00:02:17.800><c> we're</c><00:02:17.959><c> going</c><00:02:18.040><c> to</c>

00:02:18.270 --> 00:02:18.280 align:start position:0%
initiate the chat we're going to
 

00:02:18.280 --> 00:02:20.229 align:start position:0%
initiate the chat we're going to
register<00:02:18.720><c> nested</c><00:02:19.160><c> chats</c><00:02:19.599><c> with</c><00:02:19.800><c> the</c><00:02:19.920><c> user</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
register nested chats with the user
 

00:02:20.239 --> 00:02:21.910 align:start position:0%
register nested chats with the user
proxy<00:02:20.599><c> agent</c><00:02:20.920><c> so</c><00:02:21.040><c> the</c><00:02:21.160><c> recipient</c><00:02:21.640><c> again</c><00:02:21.800><c> is</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
proxy agent so the recipient again is
 

00:02:21.920 --> 00:02:23.270 align:start position:0%
proxy agent so the recipient again is
going<00:02:22.040><c> to</c><00:02:22.120><c> be</c><00:02:22.239><c> the</c><00:02:22.319><c> critic</c><00:02:22.720><c> the</c><00:02:22.840><c> message</c><00:02:23.160><c> is</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
going to be the critic the message is
 

00:02:23.280 --> 00:02:25.309 align:start position:0%
going to be the critic the message is
going<00:02:23.360><c> to</c><00:02:23.480><c> be</c><00:02:23.720><c> the</c><00:02:23.959><c> reflection</c><00:02:24.640><c> message</c><00:02:25.200><c> and</c>

00:02:25.309 --> 00:02:25.319 align:start position:0%
going to be the reflection message and
 

00:02:25.319 --> 00:02:26.750 align:start position:0%
going to be the reflection message and
what<00:02:25.440><c> this</c><00:02:25.560><c> is</c><00:02:25.680><c> going</c><00:02:25.760><c> to</c><00:02:25.879><c> be</c><00:02:26.000><c> doing</c><00:02:26.480><c> is</c><00:02:26.680><c> going</c>

00:02:26.750 --> 00:02:26.760 align:start position:0%
what this is going to be doing is going
 

00:02:26.760 --> 00:02:28.790 align:start position:0%
what this is going to be doing is going
to<00:02:26.920><c> call</c><00:02:27.200><c> this</c><00:02:27.360><c> message</c><00:02:27.800><c> up</c><00:02:27.959><c> here</c><00:02:28.560><c> it's</c><00:02:28.680><c> going</c>

00:02:28.790 --> 00:02:28.800 align:start position:0%
to call this message up here it's going
 

00:02:28.800 --> 00:02:30.710 align:start position:0%
to call this message up here it's going
to<00:02:29.000><c> return</c><00:02:29.360><c> reflect</c><00:02:29.720><c> and</c><00:02:30.000><c> provide</c><00:02:30.360><c> critique</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
to return reflect and provide critique
 

00:02:30.720 --> 00:02:32.390 align:start position:0%
to return reflect and provide critique
on<00:02:30.879><c> the</c><00:02:31.040><c> following</c><00:02:31.640><c> writing</c><00:02:32.080><c> and</c><00:02:32.160><c> we're</c><00:02:32.280><c> going</c>

00:02:32.390 --> 00:02:32.400 align:start position:0%
on the following writing and we're going
 

00:02:32.400 --> 00:02:35.070 align:start position:0%
on the following writing and we're going
to<00:02:32.599><c> take</c><00:02:33.000><c> the</c><00:02:33.200><c> recipient.</c><00:02:34.160><c> chat</c><00:02:34.400><c> messages</c><00:02:34.800><c> for</c>

00:02:35.070 --> 00:02:35.080 align:start position:0%
to take the recipient. chat messages for
 

00:02:35.080 --> 00:02:37.070 align:start position:0%
to take the recipient. chat messages for
summary<00:02:35.680><c> from</c><00:02:35.840><c> the</c><00:02:35.959><c> sender</c><00:02:36.560><c> and</c><00:02:36.720><c> get</c><00:02:36.879><c> the</c>

00:02:37.070 --> 00:02:37.080 align:start position:0%
summary from the sender and get the
 

00:02:37.080 --> 00:02:39.110 align:start position:0%
summary from the sender and get the
content<00:02:37.720><c> this</c><00:02:37.879><c> is</c><00:02:38.040><c> basically</c><00:02:38.360><c> taking</c><00:02:38.959><c> what</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
content this is basically taking what
 

00:02:39.120 --> 00:02:42.670 align:start position:0%
content this is basically taking what
the<00:02:39.280><c> writer</c><00:02:39.840><c> gives</c><00:02:40.120><c> to</c><00:02:40.280><c> the</c><00:02:40.400><c> user</c><00:02:41.159><c> proxy</c><00:02:42.159><c> as</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
the writer gives to the user proxy as
 

00:02:42.680 --> 00:02:45.710 align:start position:0%
the writer gives to the user proxy as
added<00:02:43.200><c> context</c><00:02:44.159><c> to</c><00:02:44.680><c> the</c><00:02:44.840><c> critic</c><00:02:45.280><c> and</c><00:02:45.440><c> then</c><00:02:45.599><c> we</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
added context to the critic and then we
 

00:02:45.720 --> 00:02:48.149 align:start position:0%
added context to the critic and then we
say<00:02:46.000><c> trigger</c><00:02:46.400><c> equals</c><00:02:46.800><c> writer</c><00:02:47.280><c> so</c><00:02:47.760><c> the</c><00:02:47.920><c> way</c>

00:02:48.149 --> 00:02:48.159 align:start position:0%
say trigger equals writer so the way
 

00:02:48.159 --> 00:02:50.030 align:start position:0%
say trigger equals writer so the way
this<00:02:48.280><c> is</c><00:02:48.400><c> going</c><00:02:48.519><c> to</c><00:02:48.680><c> get</c><00:02:48.840><c> triggered</c><00:02:49.440><c> is</c><00:02:49.680><c> only</c>

00:02:50.030 --> 00:02:50.040 align:start position:0%
this is going to get triggered is only
 

00:02:50.040 --> 00:02:52.949 align:start position:0%
this is going to get triggered is only
when<00:02:50.400><c> the</c><00:02:50.519><c> user</c><00:02:50.879><c> proxy</c><00:02:51.400><c> initiates</c><00:02:51.959><c> a</c><00:02:52.159><c> chat</c>

00:02:52.949 --> 00:02:52.959 align:start position:0%
when the user proxy initiates a chat
 

00:02:52.959 --> 00:02:54.509 align:start position:0%
when the user proxy initiates a chat
with<00:02:53.239><c> the</c><00:02:53.360><c> writer</c><00:02:53.760><c> and</c><00:02:53.879><c> then</c><00:02:54.000><c> we</c><00:02:54.080><c> have</c><00:02:54.200><c> user</c>

00:02:54.509 --> 00:02:54.519 align:start position:0%
with the writer and then we have user
 

00:02:54.519 --> 00:02:56.470 align:start position:0%
with the writer and then we have user
proxy<00:02:54.879><c> do</c><00:02:55.080><c> initiate</c><00:02:55.480><c> chat</c><00:02:55.760><c> so</c><00:02:55.879><c> the</c><00:02:55.959><c> user</c><00:02:56.200><c> proxy</c>

00:02:56.470 --> 00:02:56.480 align:start position:0%
proxy do initiate chat so the user proxy
 

00:02:56.480 --> 00:02:57.869 align:start position:0%
proxy do initiate chat so the user proxy
is<00:02:56.519><c> going</c><00:02:56.640><c> to</c><00:02:56.720><c> initiate</c><00:02:57.080><c> chat</c><00:02:57.480><c> with</c><00:02:57.760><c> the</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
is going to initiate chat with the
 

00:02:57.879 --> 00:03:00.070 align:start position:0%
is going to initiate chat with the
writer<00:02:58.680><c> and</c><00:02:58.959><c> the</c><00:02:59.120><c> task</c><00:02:59.400><c> from</c><00:02:59.560><c> above</c><00:02:59.879><c> where</c><00:03:00.000><c> it</c>

00:03:00.070 --> 00:03:00.080 align:start position:0%
writer and the task from above where it
 

00:03:00.080 --> 00:03:01.710 align:start position:0%
writer and the task from above where it
creates<00:03:00.280><c> a</c><00:03:00.400><c> blog</c><00:03:00.680><c> post</c><00:03:00.920><c> about</c><00:03:01.120><c> meta</c><00:03:01.440><c> so</c><00:03:01.560><c> let's</c>

00:03:01.710 --> 00:03:01.720 align:start position:0%
creates a blog post about meta so let's
 

00:03:01.720 --> 00:03:03.229 align:start position:0%
creates a blog post about meta so let's
run<00:03:01.920><c> this</c><00:03:02.120><c> and</c><00:03:02.280><c> see</c><00:03:02.480><c> what</c><00:03:02.599><c> it</c><00:03:02.720><c> does</c><00:03:03.120><c> it</c>

00:03:03.229 --> 00:03:03.239 align:start position:0%
run this and see what it does it
 

00:03:03.239 --> 00:03:05.350 align:start position:0%
run this and see what it does it
finished<00:03:03.720><c> now</c><00:03:03.920><c> let's</c><00:03:04.319><c> see</c><00:03:04.560><c> how</c><00:03:04.760><c> this</c><00:03:04.879><c> was</c><00:03:05.040><c> done</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
finished now let's see how this was done
 

00:03:05.360 --> 00:03:06.550 align:start position:0%
finished now let's see how this was done
so<00:03:05.560><c> started</c><00:03:05.760><c> with</c><00:03:05.879><c> the</c><00:03:05.920><c> user</c><00:03:06.159><c> talking</c><00:03:06.360><c> to</c><00:03:06.480><c> the</c>

00:03:06.550 --> 00:03:06.560 align:start position:0%
so started with the user talking to the
 

00:03:06.560 --> 00:03:08.030 align:start position:0%
so started with the user talking to the
Raider<00:03:07.040><c> which</c><00:03:07.159><c> we</c><00:03:07.280><c> know</c><00:03:07.480><c> with</c><00:03:07.599><c> the</c><00:03:07.720><c> message</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
Raider which we know with the message
 

00:03:08.040 --> 00:03:09.550 align:start position:0%
Raider which we know with the message
about<00:03:08.200><c> creating</c><00:03:08.480><c> a</c><00:03:08.560><c> blog</c><00:03:08.799><c> post</c><00:03:09.000><c> with</c><00:03:09.120><c> meta</c>

00:03:09.550 --> 00:03:09.560 align:start position:0%
about creating a blog post with meta
 

00:03:09.560 --> 00:03:11.670 align:start position:0%
about creating a blog post with meta
then<00:03:09.720><c> the</c><00:03:09.840><c> writer</c><00:03:10.360><c> came</c><00:03:10.560><c> back</c><00:03:10.680><c> to</c><00:03:10.840><c> the</c><00:03:11.000><c> user</c>

00:03:11.670 --> 00:03:11.680 align:start position:0%
then the writer came back to the user
 

00:03:11.680 --> 00:03:13.949 align:start position:0%
then the writer came back to the user
and<00:03:12.120><c> created</c><00:03:12.480><c> this</c><00:03:12.680><c> blog</c><00:03:12.959><c> post</c><00:03:13.200><c> with</c><00:03:13.319><c> a</c><00:03:13.480><c> title</c>

00:03:13.949 --> 00:03:13.959 align:start position:0%
and created this blog post with a title
 

00:03:13.959 --> 00:03:16.470 align:start position:0%
and created this blog post with a title
and<00:03:14.159><c> now</c><00:03:14.680><c> again</c><00:03:15.080><c> because</c><00:03:16.080><c> that</c><00:03:16.239><c> was</c>

00:03:16.470 --> 00:03:16.480 align:start position:0%
and now again because that was
 

00:03:16.480 --> 00:03:18.270 align:start position:0%
and now again because that was
triggering<00:03:17.159><c> the</c><00:03:17.319><c> Nesta</c><00:03:17.760><c> chat</c><00:03:18.000><c> because</c><00:03:18.200><c> the</c>

00:03:18.270 --> 00:03:18.280 align:start position:0%
triggering the Nesta chat because the
 

00:03:18.280 --> 00:03:19.990 align:start position:0%
triggering the Nesta chat because the
nest<00:03:18.599><c> chat</c><00:03:18.799><c> trigger</c><00:03:19.239><c> was</c><00:03:19.400><c> the</c><00:03:19.519><c> writer</c><00:03:19.799><c> so</c><00:03:19.920><c> in</c>

00:03:19.990 --> 00:03:20.000 align:start position:0%
nest chat trigger was the writer so in
 

00:03:20.000 --> 00:03:21.910 align:start position:0%
nest chat trigger was the writer so in
the<00:03:20.080><c> user</c><00:03:20.360><c> proxy</c><00:03:20.680><c> talked</c><00:03:20.920><c> to</c><00:03:21.000><c> the</c><00:03:21.080><c> writer</c><00:03:21.599><c> now</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
the user proxy talked to the writer now
 

00:03:21.920 --> 00:03:24.830 align:start position:0%
the user proxy talked to the writer now
we're<00:03:22.080><c> going</c><00:03:22.200><c> to</c><00:03:22.360><c> start</c><00:03:22.760><c> a</c><00:03:22.959><c> new</c><00:03:23.239><c> chat</c><00:03:24.040><c> so</c><00:03:24.640><c> up</c>

00:03:24.830 --> 00:03:24.840 align:start position:0%
we're going to start a new chat so up
 

00:03:24.840 --> 00:03:27.110 align:start position:0%
we're going to start a new chat so up
here<00:03:25.239><c> in</c><00:03:25.760><c> the</c><00:03:26.000><c> print</c><00:03:26.239><c> statement</c><00:03:26.560><c> so</c><00:03:26.720><c> we</c><00:03:26.879><c> say</c>

00:03:27.110 --> 00:03:27.120 align:start position:0%
here in the print statement so we say
 

00:03:27.120 --> 00:03:29.550 align:start position:0%
here in the print statement so we say
reflecting<00:03:27.680><c> now</c><00:03:28.159><c> see</c><00:03:28.439><c> reflecting</c><00:03:28.920><c> now</c><00:03:29.439><c> and</c>

00:03:29.550 --> 00:03:29.560 align:start position:0%
reflecting now see reflecting now and
 

00:03:29.560 --> 00:03:31.750 align:start position:0%
reflecting now see reflecting now and
then<00:03:29.760><c> we're</c><00:03:29.879><c> going</c><00:03:29.959><c> to</c><00:03:30.080><c> start</c><00:03:30.400><c> a</c><00:03:30.640><c> new</c><00:03:31.040><c> chat</c><00:03:31.480><c> is</c>

00:03:31.750 --> 00:03:31.760 align:start position:0%
then we're going to start a new chat is
 

00:03:31.760 --> 00:03:33.110 align:start position:0%
then we're going to start a new chat is
reflect<00:03:32.120><c> and</c><00:03:32.360><c> provide</c><00:03:32.640><c> critique</c><00:03:32.959><c> on</c><00:03:33.040><c> the</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
reflect and provide critique on the
 

00:03:33.120 --> 00:03:34.350 align:start position:0%
reflect and provide critique on the
following<00:03:33.400><c> writing</c><00:03:33.720><c> which</c><00:03:33.840><c> which</c><00:03:33.959><c> you</c><00:03:34.040><c> see</c><00:03:34.200><c> up</c>

00:03:34.350 --> 00:03:34.360 align:start position:0%
following writing which which you see up
 

00:03:34.360 --> 00:03:37.390 align:start position:0%
following writing which which you see up
here<00:03:34.680><c> and</c><00:03:34.799><c> then</c><00:03:34.959><c> we</c><00:03:35.200><c> also</c><00:03:36.000><c> got</c><00:03:36.400><c> the</c><00:03:36.599><c> summary</c><00:03:37.200><c> or</c>

00:03:37.390 --> 00:03:37.400 align:start position:0%
here and then we also got the summary or
 

00:03:37.400 --> 00:03:40.710 align:start position:0%
here and then we also got the summary or
basically<00:03:38.239><c> the</c><00:03:38.760><c> the</c><00:03:38.959><c> last</c><00:03:39.280><c> message</c><00:03:40.159><c> that</c><00:03:40.560><c> was</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
basically the the last message that was
 

00:03:40.720 --> 00:03:42.229 align:start position:0%
basically the the last message that was
written<00:03:41.040><c> to</c><00:03:41.200><c> the</c><00:03:41.319><c> user</c><00:03:41.599><c> so</c><00:03:41.760><c> from</c><00:03:41.920><c> the</c><00:03:42.000><c> writer</c>

00:03:42.229 --> 00:03:42.239 align:start position:0%
written to the user so from the writer
 

00:03:42.239 --> 00:03:44.190 align:start position:0%
written to the user so from the writer
to<00:03:42.360><c> the</c><00:03:42.480><c> user</c><00:03:42.760><c> and</c><00:03:42.879><c> that's</c><00:03:43.159><c> added</c><00:03:43.599><c> context</c>

00:03:44.190 --> 00:03:44.200 align:start position:0%
to the user and that's added context
 

00:03:44.200 --> 00:03:45.910 align:start position:0%
to the user and that's added context
here<00:03:44.400><c> so</c><00:03:44.519><c> we're</c><00:03:44.640><c> saying</c><00:03:44.799><c> user</c><00:03:45.040><c> to</c><00:03:45.200><c> the</c><00:03:45.319><c> critic</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
here so we're saying user to the critic
 

00:03:45.920 --> 00:03:47.949 align:start position:0%
here so we're saying user to the critic
reflect<00:03:46.599><c> and</c><00:03:46.799><c> provide</c><00:03:47.200><c> critique</c><00:03:47.640><c> on</c><00:03:47.799><c> the</c>

00:03:47.949 --> 00:03:47.959 align:start position:0%
reflect and provide critique on the
 

00:03:47.959 --> 00:03:49.550 align:start position:0%
reflect and provide critique on the
following<00:03:48.360><c> writing</c><00:03:48.720><c> then</c><00:03:48.840><c> the</c><00:03:48.959><c> critique</c><00:03:49.439><c> is</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
following writing then the critique is
 

00:03:49.560 --> 00:03:51.190 align:start position:0%
following writing then the critique is
going<00:03:49.640><c> to</c><00:03:49.760><c> send</c><00:03:50.000><c> back</c><00:03:50.120><c> to</c><00:03:50.239><c> the</c><00:03:50.360><c> user</c><00:03:51.040><c> um</c>

00:03:51.190 --> 00:03:51.200 align:start position:0%
going to send back to the user um
 

00:03:51.200 --> 00:03:52.910 align:start position:0%
going to send back to the user um
whatever<00:03:51.480><c> it's</c><00:03:51.640><c> critiquing</c><00:03:52.239><c> it</c><00:03:52.400><c> about</c><00:03:52.680><c> so</c><00:03:52.840><c> at</c>

00:03:52.910 --> 00:03:52.920 align:start position:0%
whatever it's critiquing it about so at
 

00:03:52.920 --> 00:03:54.069 align:start position:0%
whatever it's critiquing it about so at
some<00:03:53.040><c> point</c><00:03:53.200><c> says</c><00:03:53.400><c> to</c><00:03:53.480><c> enhance</c><00:03:53.760><c> the</c><00:03:53.840><c> overall</c>

00:03:54.069 --> 00:03:54.079 align:start position:0%
some point says to enhance the overall
 

00:03:54.079 --> 00:03:55.869 align:start position:0%
some point says to enhance the overall
quality<00:03:54.480><c> writing</c><00:03:55.000><c> it</c><00:03:55.120><c> can</c><00:03:55.239><c> benefit</c><00:03:55.560><c> from</c><00:03:55.680><c> more</c>

00:03:55.869 --> 00:03:55.879 align:start position:0%
quality writing it can benefit from more
 

00:03:55.879 --> 00:03:58.149 align:start position:0%
quality writing it can benefit from more
detailed<00:03:56.280><c> exploration</c><00:03:56.920><c> okay</c><00:03:57.200><c> and</c><00:03:57.400><c> then</c><00:03:57.920><c> that</c>

00:03:58.149 --> 00:03:58.159 align:start position:0%
detailed exploration okay and then that
 

00:03:58.159 --> 00:04:00.190 align:start position:0%
detailed exploration okay and then that
conversation<00:03:58.760><c> is</c><00:03:59.079><c> done</c><00:03:59.319><c> that</c><00:03:59.439><c> convers</c><00:04:00.040><c> is</c>

00:04:00.190 --> 00:04:00.200 align:start position:0%
conversation is done that convers is
 

00:04:00.200 --> 00:04:02.030 align:start position:0%
conversation is done that convers is
done<00:04:00.480><c> because</c><00:04:00.680><c> we</c><00:04:00.840><c> also</c><00:04:01.040><c> set</c><00:04:01.280><c> to</c><00:04:01.400><c> a</c><00:04:01.480><c> Max</c><00:04:01.720><c> turn</c>

00:04:02.030 --> 00:04:02.040 align:start position:0%
done because we also set to a Max turn
 

00:04:02.040 --> 00:04:04.149 align:start position:0%
done because we also set to a Max turn
of<00:04:02.200><c> one</c><00:04:02.879><c> so</c><00:04:03.040><c> now</c><00:04:03.200><c> the</c><00:04:03.280><c> user</c><00:04:03.480><c> is</c><00:04:03.599><c> going</c><00:04:03.720><c> to</c><00:04:03.879><c> talk</c>

00:04:04.149 --> 00:04:04.159 align:start position:0%
of one so now the user is going to talk
 

00:04:04.159 --> 00:04:05.429 align:start position:0%
of one so now the user is going to talk
back<00:04:04.319><c> to</c><00:04:04.439><c> the</c><00:04:04.560><c> writer</c><00:04:04.920><c> then</c><00:04:05.000><c> it</c><00:04:05.079><c> kind</c><00:04:05.159><c> of</c><00:04:05.200><c> sends</c>

00:04:05.429 --> 00:04:05.439 align:start position:0%
back to the writer then it kind of sends
 

00:04:05.439 --> 00:04:07.390 align:start position:0%
back to the writer then it kind of sends
what<00:04:05.560><c> the</c><00:04:05.640><c> critic</c><00:04:06.200><c> said</c><00:04:06.760><c> back</c><00:04:06.920><c> to</c><00:04:07.040><c> the</c><00:04:07.159><c> writer</c>

00:04:07.390 --> 00:04:07.400 align:start position:0%
what the critic said back to the writer
 

00:04:07.400 --> 00:04:09.470 align:start position:0%
what the critic said back to the writer
then<00:04:07.519><c> the</c><00:04:07.599><c> WR</c><00:04:07.799><c> to</c><00:04:07.920><c> the</c><00:04:08.040><c> user</c><00:04:08.439><c> is</c><00:04:08.599><c> going</c><00:04:08.799><c> to</c><00:04:09.280><c> give</c>

00:04:09.470 --> 00:04:09.480 align:start position:0%
then the WR to the user is going to give
 

00:04:09.480 --> 00:04:11.270 align:start position:0%
then the WR to the user is going to give
an<00:04:09.720><c> update</c><00:04:10.159><c> and</c><00:04:10.280><c> then</c><00:04:10.400><c> we're</c><00:04:10.680><c> done</c><00:04:11.000><c> and</c><00:04:11.079><c> if</c><00:04:11.159><c> you</c>

00:04:11.270 --> 00:04:11.280 align:start position:0%
an update and then we're done and if you
 

00:04:11.280 --> 00:04:12.350 align:start position:0%
an update and then we're done and if you
just<00:04:11.360><c> wanted</c><00:04:11.519><c> to</c><00:04:11.640><c> see</c><00:04:11.799><c> what</c><00:04:11.920><c> the</c><00:04:12.040><c> difference</c>

00:04:12.350 --> 00:04:12.360 align:start position:0%
just wanted to see what the difference
 

00:04:12.360 --> 00:04:14.710 align:start position:0%
just wanted to see what the difference
was<00:04:12.760><c> so</c><00:04:13.040><c> here</c><00:04:13.200><c> on</c><00:04:13.319><c> the</c><00:04:13.519><c> left</c><00:04:13.920><c> is</c><00:04:14.159><c> when</c><00:04:14.439><c> the</c>

00:04:14.710 --> 00:04:14.720 align:start position:0%
was so here on the left is when the
 

00:04:14.720 --> 00:04:16.509 align:start position:0%
was so here on the left is when the
writer<00:04:15.000><c> initially</c><00:04:15.400><c> created</c><00:04:15.680><c> a</c><00:04:15.840><c> blog</c><00:04:16.079><c> post</c><00:04:16.400><c> and</c>

00:04:16.509 --> 00:04:16.519 align:start position:0%
writer initially created a blog post and
 

00:04:16.519 --> 00:04:18.670 align:start position:0%
writer initially created a blog post and
then<00:04:16.720><c> after</c><00:04:16.959><c> the</c><00:04:17.199><c> critic</c><00:04:17.919><c> from</c><00:04:18.079><c> the</c><00:04:18.239><c> Nesta</c>

00:04:18.670 --> 00:04:18.680 align:start position:0%
then after the critic from the Nesta
 

00:04:18.680 --> 00:04:21.229 align:start position:0%
then after the critic from the Nesta
chat<00:04:19.160><c> gave</c><00:04:19.359><c> its</c><00:04:19.639><c> response</c><00:04:20.359><c> here</c><00:04:20.680><c> is</c><00:04:21.040><c> the</c>

00:04:21.229 --> 00:04:21.239 align:start position:0%
chat gave its response here is the
 

00:04:21.239 --> 00:04:22.990 align:start position:0%
chat gave its response here is the
revised<00:04:21.720><c> copy</c><00:04:22.000><c> that</c><00:04:22.120><c> the</c><00:04:22.199><c> writer</c><00:04:22.520><c> created</c>

00:04:22.990 --> 00:04:23.000 align:start position:0%
revised copy that the writer created
 

00:04:23.000 --> 00:04:24.830 align:start position:0%
revised copy that the writer created
this<00:04:23.120><c> was</c><00:04:23.280><c> a</c><00:04:23.479><c> quick</c><00:04:23.800><c> video</c><00:04:24.080><c> on</c><00:04:24.280><c> how</c><00:04:24.400><c> to</c><00:04:24.560><c> use</c>

00:04:24.830 --> 00:04:24.840 align:start position:0%
this was a quick video on how to use
 

00:04:24.840 --> 00:04:26.629 align:start position:0%
this was a quick video on how to use
Nesta<00:04:25.280><c> chat</c><00:04:25.560><c> which</c><00:04:25.680><c> is</c><00:04:25.880><c> another</c><00:04:26.240><c> way</c><00:04:26.400><c> to</c>

00:04:26.629 --> 00:04:26.639 align:start position:0%
Nesta chat which is another way to
 

00:04:26.639 --> 00:04:28.189 align:start position:0%
Nesta chat which is another way to
orchestrate<00:04:27.199><c> all</c><00:04:27.280><c> of</c><00:04:27.400><c> your</c><00:04:27.520><c> agents</c><00:04:27.960><c> talking</c>

00:04:28.189 --> 00:04:28.199 align:start position:0%
orchestrate all of your agents talking
 

00:04:28.199 --> 00:04:29.830 align:start position:0%
orchestrate all of your agents talking
to<00:04:28.360><c> each</c><00:04:28.479><c> other</c><00:04:28.880><c> thank</c><00:04:29.000><c> you</c><00:04:29.080><c> for</c><00:04:29.320><c> watching</c><00:04:29.759><c> if</c>

00:04:29.830 --> 00:04:29.840 align:start position:0%
to each other thank you for watching if
 

00:04:29.840 --> 00:04:31.310 align:start position:0%
to each other thank you for watching if
you<00:04:29.919><c> have</c><00:04:30.039><c> any</c><00:04:30.240><c> comments</c><00:04:30.759><c> or</c><00:04:31.000><c> if</c><00:04:31.120><c> you're</c>

00:04:31.310 --> 00:04:31.320 align:start position:0%
you have any comments or if you're
 

00:04:31.320 --> 00:04:32.990 align:start position:0%
you have any comments or if you're
interested<00:04:31.639><c> in</c><00:04:31.759><c> learning</c><00:04:32.120><c> more</c><00:04:32.479><c> because</c><00:04:32.840><c> you</c>

00:04:32.990 --> 00:04:33.000 align:start position:0%
interested in learning more because you
 

00:04:33.000 --> 00:04:34.629 align:start position:0%
interested in learning more because you
can<00:04:33.160><c> go</c><00:04:33.400><c> in</c><00:04:33.560><c> more</c><00:04:33.840><c> depth</c><00:04:34.080><c> about</c><00:04:34.199><c> the</c><00:04:34.280><c> nested</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
can go in more depth about the nested
 

00:04:34.639 --> 00:04:36.590 align:start position:0%
can go in more depth about the nested
chats<00:04:35.160><c> leave</c><00:04:35.320><c> a</c><00:04:35.440><c> comment</c><00:04:35.720><c> below</c><00:04:36.199><c> give</c><00:04:36.320><c> me</c><00:04:36.440><c> your</c>

00:04:36.590 --> 00:04:36.600 align:start position:0%
chats leave a comment below give me your
 

00:04:36.600 --> 00:04:37.909 align:start position:0%
chats leave a comment below give me your
thoughts<00:04:36.759><c> on</c><00:04:36.919><c> what</c><00:04:37.039><c> you</c><00:04:37.160><c> think</c><00:04:37.320><c> about</c><00:04:37.479><c> it</c><00:04:37.800><c> we</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
thoughts on what you think about it we
 

00:04:37.919 --> 00:04:39.390 align:start position:0%
thoughts on what you think about it we
are<00:04:38.080><c> almost</c><00:04:38.400><c> finished</c><00:04:38.759><c> with</c><00:04:38.880><c> the</c><00:04:39.080><c> third</c>

00:04:39.390 --> 00:04:39.400 align:start position:0%
are almost finished with the third
 

00:04:39.400 --> 00:04:40.790 align:start position:0%
are almost finished with the third
quarter<00:04:39.919><c> of</c><00:04:40.080><c> the</c><00:04:40.160><c> challenge</c><00:04:40.520><c> and</c><00:04:40.600><c> we're</c><00:04:40.720><c> going</c>

00:04:40.790 --> 00:04:40.800 align:start position:0%
quarter of the challenge and we're going
 

00:04:40.800 --> 00:04:42.670 align:start position:0%
quarter of the challenge and we're going
to<00:04:40.880><c> start</c><00:04:41.240><c> the</c><00:04:41.440><c> last</c><00:04:41.680><c> quarter</c><00:04:42.240><c> so</c><00:04:42.440><c> I</c><00:04:42.520><c> would</c>

00:04:42.670 --> 00:04:42.680 align:start position:0%
to start the last quarter so I would
 

00:04:42.680 --> 00:04:43.950 align:start position:0%
to start the last quarter so I would
love<00:04:42.840><c> to</c><00:04:42.960><c> hear</c><00:04:43.080><c> what</c><00:04:43.199><c> you</c><00:04:43.360><c> think</c><00:04:43.639><c> here's</c><00:04:43.800><c> some</c>

00:04:43.950 --> 00:04:43.960 align:start position:0%
love to hear what you think here's some
 

00:04:43.960 --> 00:04:45.790 align:start position:0%
love to hear what you think here's some
more<00:04:44.120><c> videos</c><00:04:44.400><c> on</c><00:04:44.560><c> aogen</c><00:04:45.080><c> and</c><00:04:45.199><c> the</c><00:04:45.320><c> playlist</c>

00:04:45.790 --> 00:04:45.800 align:start position:0%
more videos on aogen and the playlist
 

00:04:45.800 --> 00:04:46.990 align:start position:0%
more videos on aogen and the playlist
thanks<00:04:45.919><c> you</c><00:04:46.000><c> for</c><00:04:46.160><c> watching</c><00:04:46.560><c> I'll</c><00:04:46.720><c> see</c><00:04:46.840><c> you</c>

00:04:46.990 --> 00:04:47.000 align:start position:0%
thanks you for watching I'll see you
 

00:04:47.000 --> 00:04:49.600 align:start position:0%
thanks you for watching I'll see you
next<00:04:47.240><c> video</c>

