WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.470 align:start position:0%
 
okay<00:00:00.399><c> so</c><00:00:00.680><c> in</c><00:00:00.799><c> the</c><00:00:01.079><c> previous</c><00:00:01.719><c> video</c><00:00:02.280><c> I</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
okay so in the previous video I
 

00:00:02.480 --> 00:00:04.789 align:start position:0%
okay so in the previous video I
basically<00:00:02.919><c> looked</c><00:00:03.280><c> at</c><00:00:03.600><c> doing</c><00:00:04.040><c> sort</c><00:00:04.240><c> of</c><00:00:04.520><c> a</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
basically looked at doing sort of a
 

00:00:04.799 --> 00:00:08.629 align:start position:0%
basically looked at doing sort of a
custom<00:00:05.720><c> crew</c><00:00:06.040><c> AI</c><00:00:06.680><c> crew</c><00:00:07.560><c> agent</c><00:00:08.120><c> with</c><00:00:08.280><c> sub</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
custom crew AI crew agent with sub
 

00:00:08.639 --> 00:00:11.030 align:start position:0%
custom crew AI crew agent with sub
agents<00:00:09.200><c> defining</c><00:00:09.719><c> the</c><00:00:09.920><c> agents</c><00:00:10.559><c> setting</c><00:00:10.880><c> up</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
agents defining the agents setting up
 

00:00:11.040 --> 00:00:13.669 align:start position:0%
agents defining the agents setting up
the<00:00:11.280><c> tasks</c><00:00:12.120><c> the</c><00:00:12.240><c> various</c><00:00:12.559><c> things</c><00:00:12.799><c> in</c><00:00:13.000><c> there</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
the tasks the various things in there
 

00:00:13.679 --> 00:00:15.669 align:start position:0%
the tasks the various things in there
one<00:00:13.799><c> of</c><00:00:13.960><c> the</c><00:00:14.160><c> the</c><00:00:14.280><c> challenges</c><00:00:14.799><c> with</c><00:00:15.000><c> that</c><00:00:15.360><c> is</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
one of the the challenges with that is
 

00:00:15.679 --> 00:00:19.310 align:start position:0%
one of the the challenges with that is
that<00:00:16.160><c> it's</c><00:00:16.840><c> by</c><00:00:17.080><c> default</c><00:00:17.520><c> crew</c><00:00:17.840><c> AI</c><00:00:18.480><c> is</c><00:00:18.720><c> using</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
that it's by default crew AI is using
 

00:00:19.320 --> 00:00:23.150 align:start position:0%
that it's by default crew AI is using
gp4<00:00:20.160><c> now</c><00:00:20.279><c> in</c><00:00:20.400><c> the</c><00:00:20.519><c> last</c><00:00:20.680><c> one</c><00:00:20.800><c> I</c><00:00:20.920><c> used</c><00:00:21.279><c> gp4</c><00:00:22.160><c> turbo</c>

00:00:23.150 --> 00:00:23.160 align:start position:0%
gp4 now in the last one I used gp4 turbo
 

00:00:23.160 --> 00:00:25.349 align:start position:0%
gp4 now in the last one I used gp4 turbo
which<00:00:23.480><c> seems</c><00:00:23.720><c> to</c><00:00:23.840><c> work</c><00:00:24.160><c> reasonably</c><00:00:24.720><c> well</c><00:00:25.240><c> the</c>

00:00:25.349 --> 00:00:25.359 align:start position:0%
which seems to work reasonably well the
 

00:00:25.359 --> 00:00:27.509 align:start position:0%
which seems to work reasonably well the
challenge<00:00:25.680><c> is</c><00:00:25.920><c> even</c><00:00:26.119><c> with</c><00:00:26.240><c> gbd4</c><00:00:26.880><c> turbo</c><00:00:27.240><c> it</c><00:00:27.359><c> can</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
challenge is even with gbd4 turbo it can
 

00:00:27.519 --> 00:00:29.470 align:start position:0%
challenge is even with gbd4 turbo it can
get<00:00:27.800><c> quite</c><00:00:28.199><c> expensive</c><00:00:28.760><c> so</c><00:00:28.960><c> what</c><00:00:29.039><c> I</c><00:00:29.160><c> thought</c><00:00:29.320><c> i'</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
get quite expensive so what I thought i'
 

00:00:29.480 --> 00:00:32.549 align:start position:0%
get quite expensive so what I thought i'
do<00:00:29.640><c> is</c><00:00:30.000><c> basically</c><00:00:30.320><c> show</c><00:00:30.960><c> how</c><00:00:31.119><c> you</c><00:00:31.240><c> can</c><00:00:31.439><c> do</c><00:00:31.800><c> this</c>

00:00:32.549 --> 00:00:32.559 align:start position:0%
do is basically show how you can do this
 

00:00:32.559 --> 00:00:35.430 align:start position:0%
do is basically show how you can do this
with<00:00:33.200><c> claw</c><00:00:34.040><c> Haiku</c><00:00:34.480><c> model</c><00:00:34.800><c> which</c><00:00:34.920><c> is</c><00:00:35.200><c> super</c>

00:00:35.430 --> 00:00:35.440 align:start position:0%
with claw Haiku model which is super
 

00:00:35.440 --> 00:00:36.950 align:start position:0%
with claw Haiku model which is super
cheap<00:00:35.680><c> model</c><00:00:36.000><c> I've</c><00:00:36.160><c> covered</c><00:00:36.480><c> that</c><00:00:36.640><c> quite</c><00:00:36.800><c> a</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
cheap model I've covered that quite a
 

00:00:36.960 --> 00:00:38.709 align:start position:0%
cheap model I've covered that quite a
lot<00:00:37.559><c> and</c><00:00:37.640><c> I'll</c><00:00:37.800><c> also</c><00:00:38.040><c> show</c><00:00:38.360><c> some</c><00:00:38.480><c> of</c><00:00:38.600><c> the</c>

00:00:38.709 --> 00:00:38.719 align:start position:0%
lot and I'll also show some of the
 

00:00:38.719 --> 00:00:41.069 align:start position:0%
lot and I'll also show some of the
issues<00:00:39.079><c> that</c><00:00:39.200><c> you're</c><00:00:39.320><c> going</c><00:00:39.399><c> to</c><00:00:39.559><c> run</c><00:00:39.879><c> into</c><00:00:40.879><c> and</c>

00:00:41.069 --> 00:00:41.079 align:start position:0%
issues that you're going to run into and
 

00:00:41.079 --> 00:00:43.389 align:start position:0%
issues that you're going to run into and
some<00:00:41.440><c> considerations</c><00:00:42.160><c> that</c><00:00:42.320><c> you</c><00:00:42.399><c> can</c><00:00:42.640><c> try</c><00:00:43.079><c> for</c>

00:00:43.389 --> 00:00:43.399 align:start position:0%
some considerations that you can try for
 

00:00:43.399 --> 00:00:45.670 align:start position:0%
some considerations that you can try for
various<00:00:43.760><c> things</c><00:00:44.039><c> as</c><00:00:44.160><c> you</c><00:00:44.320><c> go</c><00:00:44.480><c> through</c><00:00:44.800><c> this</c><00:00:45.440><c> so</c>

00:00:45.670 --> 00:00:45.680 align:start position:0%
various things as you go through this so
 

00:00:45.680 --> 00:00:47.830 align:start position:0%
various things as you go through this so
first<00:00:45.960><c> up</c><00:00:46.399><c> basically</c><00:00:47.039><c> the</c><00:00:47.199><c> things</c><00:00:47.440><c> you</c><00:00:47.680><c> need</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
first up basically the things you need
 

00:00:47.840 --> 00:00:51.950 align:start position:0%
first up basically the things you need
to<00:00:48.079><c> install</c><00:00:49.079><c> is</c><00:00:49.719><c> anthropic</c><00:00:50.719><c> and</c><00:00:51.039><c> Lang</c><00:00:51.440><c> chain</c>

00:00:51.950 --> 00:00:51.960 align:start position:0%
to install is anthropic and Lang chain
 

00:00:51.960 --> 00:00:54.670 align:start position:0%
to install is anthropic and Lang chain
anthropic<00:00:52.960><c> in</c><00:00:53.199><c> here</c><00:00:53.680><c> remember</c><00:00:54.039><c> crew</c><00:00:54.280><c> AI</c><00:00:54.559><c> is</c>

00:00:54.670 --> 00:00:54.680 align:start position:0%
anthropic in here remember crew AI is
 

00:00:54.680 --> 00:00:57.270 align:start position:0%
anthropic in here remember crew AI is
just<00:00:54.800><c> an</c><00:00:54.960><c> abstraction</c><00:00:55.520><c> on</c><00:00:55.719><c> top</c><00:00:55.879><c> of</c><00:00:56.079><c> Lang</c><00:00:56.399><c> chain</c>

00:00:57.270 --> 00:00:57.280 align:start position:0%
just an abstraction on top of Lang chain
 

00:00:57.280 --> 00:00:59.389 align:start position:0%
just an abstraction on top of Lang chain
so<00:00:57.480><c> we</c><00:00:57.640><c> come</c><00:00:57.840><c> in</c><00:00:58.120><c> we're</c><00:00:58.280><c> not</c><00:00:58.480><c> importing</c><00:00:59.079><c> open</c>

00:00:59.389 --> 00:00:59.399 align:start position:0%
so we come in we're not importing open
 

00:00:59.399 --> 00:01:01.270 align:start position:0%
so we come in we're not importing open
AI<00:01:00.039><c> key</c><00:01:00.199><c> anymore</c><00:01:00.519><c> we're</c><00:01:00.680><c> importing</c><00:01:01.079><c> the</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
AI key anymore we're importing the
 

00:01:01.280 --> 00:01:04.910 align:start position:0%
AI key anymore we're importing the
anthropic<00:01:02.079><c> key</c><00:01:03.079><c> I'm</c><00:01:03.399><c> not</c><00:01:03.640><c> using</c><00:01:03.960><c> open</c><00:01:04.280><c> AI</c><00:01:04.600><c> for</c>

00:01:04.910 --> 00:01:04.920 align:start position:0%
anthropic key I'm not using open AI for
 

00:01:04.920 --> 00:01:07.270 align:start position:0%
anthropic key I'm not using open AI for
anything<00:01:05.439><c> in</c><00:01:05.720><c> these</c><00:01:06.400><c> I</c><00:01:06.520><c> will</c><00:01:06.720><c> talk</c><00:01:06.920><c> about</c><00:01:07.119><c> on</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
anything in these I will talk about on
 

00:01:07.280 --> 00:01:09.550 align:start position:0%
anything in these I will talk about on
the<00:01:07.439><c> hierarchical</c><00:01:08.159><c> things</c><00:01:08.600><c> where</c><00:01:09.080><c> you</c><00:01:09.280><c> could</c>

00:01:09.550 --> 00:01:09.560 align:start position:0%
the hierarchical things where you could
 

00:01:09.560 --> 00:01:12.510 align:start position:0%
the hierarchical things where you could
actually<00:01:10.040><c> combine</c><00:01:11.040><c> the</c><00:01:11.200><c> clawed</c><00:01:11.640><c> models</c><00:01:12.200><c> and</c>

00:01:12.510 --> 00:01:12.520 align:start position:0%
actually combine the clawed models and
 

00:01:12.520 --> 00:01:14.710 align:start position:0%
actually combine the clawed models and
the<00:01:12.640><c> open</c><00:01:12.920><c> AI</c><00:01:13.320><c> models</c><00:01:13.680><c> for</c><00:01:13.960><c> certain</c><00:01:14.280><c> things</c><00:01:14.600><c> as</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
the open AI models for certain things as
 

00:01:14.720 --> 00:01:16.870 align:start position:0%
the open AI models for certain things as
well<00:01:14.880><c> to</c><00:01:15.000><c> get</c><00:01:15.159><c> better</c><00:01:15.479><c> results</c><00:01:16.479><c> I</c><00:01:16.600><c> got</c><00:01:16.759><c> the</c>

00:01:16.870 --> 00:01:16.880 align:start position:0%
well to get better results I got the
 

00:01:16.880 --> 00:01:19.350 align:start position:0%
well to get better results I got the
same<00:01:17.080><c> utilities</c><00:01:17.720><c> as</c><00:01:17.880><c> the</c><00:01:18.080><c> last</c><00:01:18.280><c> one</c><00:01:18.560><c> in</c><00:01:18.799><c> there</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
same utilities as the last one in there
 

00:01:19.360 --> 00:01:20.749 align:start position:0%
same utilities as the last one in there
in<00:01:19.520><c> fact</c><00:01:19.840><c> I've</c><00:01:19.960><c> tried</c><00:01:20.159><c> to</c><00:01:20.280><c> make</c><00:01:20.439><c> as</c><00:01:20.560><c> few</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
in fact I've tried to make as few
 

00:01:20.759 --> 00:01:23.429 align:start position:0%
in fact I've tried to make as few
adjustments<00:01:21.280><c> as</c><00:01:21.479><c> possible</c><00:01:21.880><c> to</c><00:01:22.079><c> basically</c><00:01:22.640><c> get</c>

00:01:23.429 --> 00:01:23.439 align:start position:0%
adjustments as possible to basically get
 

00:01:23.439 --> 00:01:26.550 align:start position:0%
adjustments as possible to basically get
the<00:01:23.799><c> old</c><00:01:24.200><c> code</c><00:01:24.640><c> working</c><00:01:25.119><c> with</c><00:01:25.560><c> anthropic</c><00:01:26.360><c> and</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
the old code working with anthropic and
 

00:01:26.560 --> 00:01:29.710 align:start position:0%
the old code working with anthropic and
particular<00:01:27.000><c> with</c><00:01:27.119><c> the</c><00:01:27.280><c> Hau</c><00:01:28.240><c> model</c><00:01:28.560><c> in</c><00:01:28.799><c> here</c><00:01:29.400><c> so</c>

00:01:29.710 --> 00:01:29.720 align:start position:0%
particular with the Hau model in here so
 

00:01:29.720 --> 00:01:31.550 align:start position:0%
particular with the Hau model in here so
okay<00:01:30.040><c> because</c><00:01:30.439><c> we're</c><00:01:30.640><c> bringing</c><00:01:31.000><c> in</c><00:01:31.280><c> Lang</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
okay because we're bringing in Lang
 

00:01:31.560 --> 00:01:33.310 align:start position:0%
okay because we're bringing in Lang
chain<00:01:31.840><c> anthropic</c><00:01:32.399><c> we</c><00:01:32.520><c> want</c><00:01:32.640><c> to</c><00:01:32.759><c> bring</c><00:01:32.960><c> in</c><00:01:33.159><c> the</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
chain anthropic we want to bring in the
 

00:01:33.320 --> 00:01:36.149 align:start position:0%
chain anthropic we want to bring in the
chat<00:01:33.799><c> anthropic</c><00:01:34.799><c> so</c><00:01:35.119><c> here</c><00:01:35.399><c> we</c><00:01:35.520><c> can</c><00:01:35.680><c> bring</c><00:01:36.000><c> this</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
chat anthropic so here we can bring this
 

00:01:36.159 --> 00:01:39.149 align:start position:0%
chat anthropic so here we can bring this
in<00:01:36.520><c> and</c><00:01:36.640><c> we</c><00:01:36.759><c> can</c><00:01:36.960><c> actually</c><00:01:37.200><c> bring</c><00:01:37.479><c> in</c><00:01:38.439><c> Hau</c><00:01:39.079><c> we</c>

00:01:39.149 --> 00:01:39.159 align:start position:0%
in and we can actually bring in Hau we
 

00:01:39.159 --> 00:01:41.630 align:start position:0%
in and we can actually bring in Hau we
can<00:01:39.320><c> bring</c><00:01:39.520><c> in</c><00:01:39.680><c> Sonet</c><00:01:40.240><c> we</c><00:01:40.320><c> can</c><00:01:40.479><c> bring</c><00:01:40.640><c> in</c><00:01:40.880><c> Opus</c>

00:01:41.630 --> 00:01:41.640 align:start position:0%
can bring in Sonet we can bring in Opus
 

00:01:41.640 --> 00:01:43.990 align:start position:0%
can bring in Sonet we can bring in Opus
for<00:01:41.880><c> any</c><00:01:42.040><c> of</c><00:01:42.240><c> these</c><00:01:42.560><c> but</c><00:01:42.720><c> we've</c><00:01:43.159><c> Now</c><00:01:43.399><c> set</c><00:01:43.600><c> up</c>

00:01:43.990 --> 00:01:44.000 align:start position:0%
for any of these but we've Now set up
 

00:01:44.000 --> 00:01:47.830 align:start position:0%
for any of these but we've Now set up
this<00:01:44.399><c> Claude</c><00:01:44.960><c> Hau</c><00:01:45.960><c> as</c><00:01:46.200><c> being</c><00:01:46.600><c> our</c><00:01:46.840><c> main</c><00:01:47.240><c> llm</c>

00:01:47.830 --> 00:01:47.840 align:start position:0%
this Claude Hau as being our main llm
 

00:01:47.840 --> 00:01:49.389 align:start position:0%
this Claude Hau as being our main llm
that<00:01:47.920><c> we're</c><00:01:48.040><c> going</c><00:01:48.159><c> to</c><00:01:48.280><c> use</c><00:01:48.479><c> in</c><00:01:48.719><c> here</c><00:01:49.280><c> all</c>

00:01:49.389 --> 00:01:49.399 align:start position:0%
that we're going to use in here all
 

00:01:49.399 --> 00:01:50.709 align:start position:0%
that we're going to use in here all
right<00:01:49.600><c> some</c><00:01:49.719><c> of</c><00:01:49.840><c> the</c><00:01:49.960><c> changes</c><00:01:50.320><c> that</c><00:01:50.439><c> I</c><00:01:50.560><c> did</c>

00:01:50.709 --> 00:01:50.719 align:start position:0%
right some of the changes that I did
 

00:01:50.719 --> 00:01:52.230 align:start position:0%
right some of the changes that I did
make<00:01:50.880><c> from</c><00:01:51.040><c> the</c><00:01:51.159><c> code</c><00:01:51.399><c> from</c><00:01:51.560><c> the</c><00:01:51.719><c> last</c><00:01:51.920><c> time</c><00:01:52.079><c> is</c>

00:01:52.230 --> 00:01:52.240 align:start position:0%
make from the code from the last time is
 

00:01:52.240 --> 00:01:54.670 align:start position:0%
make from the code from the last time is
just<00:01:52.439><c> improving</c><00:01:52.960><c> it</c><00:01:53.439><c> I</c><00:01:53.560><c> put</c><00:01:53.920><c> input</c><00:01:54.360><c> is</c><00:01:54.479><c> a</c>

00:01:54.670 --> 00:01:54.680 align:start position:0%
just improving it I put input is a
 

00:01:54.680 --> 00:01:57.109 align:start position:0%
just improving it I put input is a
string<00:01:55.280><c> here</c><00:01:55.560><c> to</c><00:01:55.799><c> basically</c><00:01:56.159><c> help</c><00:01:56.360><c> for</c><00:01:56.640><c> saving</c>

00:01:57.109 --> 00:01:57.119 align:start position:0%
string here to basically help for saving
 

00:01:57.119 --> 00:01:58.630 align:start position:0%
string here to basically help for saving
and<00:01:57.280><c> stuff</c><00:01:57.520><c> like</c><00:01:57.719><c> that</c><00:01:58.200><c> and</c><00:01:58.280><c> then</c><00:01:58.439><c> also</c>

00:01:58.630 --> 00:01:58.640 align:start position:0%
and stuff like that and then also
 

00:01:58.640 --> 00:01:59.950 align:start position:0%
and stuff like that and then also
someone<00:01:58.880><c> pointed</c><00:01:59.159><c> out</c><00:01:59.280><c> in</c><00:01:59.360><c> the</c><00:01:59.439><c> comments</c><00:01:59.719><c> that</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
someone pointed out in the comments that
 

00:01:59.960 --> 00:02:01.709 align:start position:0%
someone pointed out in the comments that
I<00:02:00.079><c> wasn't</c><00:02:00.280><c> using</c><00:02:00.560><c> the</c><00:02:00.719><c> context</c><00:02:01.240><c> in</c><00:02:01.479><c> there</c>

00:02:01.709 --> 00:02:01.719 align:start position:0%
I wasn't using the context in there
 

00:02:01.719 --> 00:02:03.709 align:start position:0%
I wasn't using the context in there
which<00:02:02.240><c> I</c><00:02:02.360><c> I</c><00:02:02.479><c> just</c><00:02:02.640><c> totally</c><00:02:02.960><c> forgot</c><00:02:03.280><c> about</c><00:02:03.520><c> so</c>

00:02:03.709 --> 00:02:03.719 align:start position:0%
which I I just totally forgot about so
 

00:02:03.719 --> 00:02:05.990 align:start position:0%
which I I just totally forgot about so
yes<00:02:03.880><c> I</c><00:02:04.039><c> put</c><00:02:04.280><c> those</c><00:02:04.560><c> back</c><00:02:04.759><c> in</c><00:02:05.399><c> there</c><00:02:05.560><c> at</c><00:02:05.759><c> this</c>

00:02:05.990 --> 00:02:06.000 align:start position:0%
yes I put those back in there at this
 

00:02:06.000 --> 00:02:09.029 align:start position:0%
yes I put those back in there at this
time<00:02:06.719><c> tools</c><00:02:07.200><c> we've</c><00:02:07.399><c> got</c><00:02:07.560><c> everything</c><00:02:08.000><c> the</c><00:02:08.160><c> same</c>

00:02:09.029 --> 00:02:09.039 align:start position:0%
time tools we've got everything the same
 

00:02:09.039 --> 00:02:11.589 align:start position:0%
time tools we've got everything the same
agents<00:02:09.599><c> I've</c><00:02:09.759><c> left</c><00:02:10.000><c> the</c><00:02:10.280><c> prompts</c><00:02:10.800><c> the</c><00:02:10.959><c> same</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
agents I've left the prompts the same
 

00:02:11.599 --> 00:02:13.229 align:start position:0%
agents I've left the prompts the same
but<00:02:11.720><c> I</c><00:02:11.800><c> will</c><00:02:11.959><c> say</c><00:02:12.120><c> from</c><00:02:12.280><c> the</c><00:02:12.400><c> get-go</c><00:02:12.920><c> that</c>

00:02:13.229 --> 00:02:13.239 align:start position:0%
but I will say from the get-go that
 

00:02:13.239 --> 00:02:15.869 align:start position:0%
but I will say from the get-go that
anthropic<00:02:13.879><c> uses</c><00:02:14.400><c> different</c><00:02:14.800><c> prompting</c><00:02:15.480><c> than</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
anthropic uses different prompting than
 

00:02:15.879 --> 00:02:18.470 align:start position:0%
anthropic uses different prompting than
open<00:02:16.239><c> Ai</c><00:02:17.239><c> and</c><00:02:17.360><c> so</c><00:02:17.560><c> you</c><00:02:17.800><c> probably</c><00:02:18.080><c> want</c><00:02:18.200><c> to</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
open Ai and so you probably want to
 

00:02:18.480 --> 00:02:20.710 align:start position:0%
open Ai and so you probably want to
revisit<00:02:19.080><c> some</c><00:02:19.280><c> of</c><00:02:19.400><c> your</c><00:02:19.640><c> prompting</c><00:02:20.200><c> in</c><00:02:20.440><c> here</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
revisit some of your prompting in here
 

00:02:20.720 --> 00:02:23.350 align:start position:0%
revisit some of your prompting in here
to<00:02:20.959><c> get</c><00:02:21.120><c> the</c><00:02:21.319><c> best</c><00:02:21.599><c> results</c><00:02:22.160><c> out</c><00:02:22.319><c> of</c><00:02:22.640><c> this</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
to get the best results out of this
 

00:02:23.360 --> 00:02:25.470 align:start position:0%
to get the best results out of this
you'll<00:02:23.519><c> see</c><00:02:23.720><c> in</c><00:02:23.920><c> here</c><00:02:24.200><c> I've</c><00:02:24.400><c> got</c><00:02:24.640><c> the</c><00:02:24.920><c> Claude</c>

00:02:25.470 --> 00:02:25.480 align:start position:0%
you'll see in here I've got the Claude
 

00:02:25.480 --> 00:02:27.390 align:start position:0%
you'll see in here I've got the Claude
Haiku<00:02:26.040><c> as</c><00:02:26.200><c> being</c><00:02:26.440><c> the</c><00:02:26.560><c> llm</c><00:02:27.080><c> that</c><00:02:27.200><c> we're</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
Haiku as being the llm that we're
 

00:02:27.400 --> 00:02:29.350 align:start position:0%
Haiku as being the llm that we're
passing<00:02:27.720><c> in</c><00:02:27.920><c> there</c><00:02:28.120><c> now</c><00:02:28.840><c> and</c><00:02:28.959><c> in</c><00:02:29.080><c> fact</c><00:02:29.239><c> I'm</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
passing in there now and in fact I'm
 

00:02:29.360 --> 00:02:31.670 align:start position:0%
passing in there now and in fact I'm
passing<00:02:29.640><c> that</c><00:02:29.959><c> in</c><00:02:30.120><c> as</c><00:02:30.280><c> the</c><00:02:30.400><c> llm</c><00:02:31.000><c> for</c><00:02:31.280><c> each</c><00:02:31.480><c> of</c>

00:02:31.670 --> 00:02:31.680 align:start position:0%
passing that in as the llm for each of
 

00:02:31.680 --> 00:02:33.910 align:start position:0%
passing that in as the llm for each of
the<00:02:31.920><c> agents</c><00:02:32.720><c> in</c><00:02:32.920><c> here</c><00:02:33.120><c> now</c><00:02:33.280><c> this</c><00:02:33.400><c> is</c><00:02:33.480><c> doing</c><00:02:33.760><c> the</c>

00:02:33.910 --> 00:02:33.920 align:start position:0%
the agents in here now this is doing the
 

00:02:33.920 --> 00:02:37.150 align:start position:0%
the agents in here now this is doing the
sequential<00:02:34.599><c> process</c><00:02:35.040><c> in</c><00:02:35.200><c> this</c><00:02:35.440><c> first</c><00:02:35.720><c> one</c><00:02:36.599><c> the</c>

00:02:37.150 --> 00:02:37.160 align:start position:0%
sequential process in this first one the
 

00:02:37.160 --> 00:02:40.350 align:start position:0%
sequential process in this first one the
tasks<00:02:38.160><c> again</c><00:02:38.720><c> like</c><00:02:38.879><c> I</c><00:02:39.040><c> mentioned</c><00:02:39.440><c> before</c><00:02:40.080><c> I</c><00:02:40.239><c> I</c>

00:02:40.350 --> 00:02:40.360 align:start position:0%
tasks again like I mentioned before I I
 

00:02:40.360 --> 00:02:42.149 align:start position:0%
tasks again like I mentioned before I I
really<00:02:40.560><c> haven't</c><00:02:40.800><c> changed</c><00:02:41.280><c> anything</c><00:02:41.760><c> the</c><00:02:41.879><c> only</c>

00:02:42.149 --> 00:02:42.159 align:start position:0%
really haven't changed anything the only
 

00:02:42.159 --> 00:02:44.750 align:start position:0%
really haven't changed anything the only
thing<00:02:42.360><c> I've</c><00:02:42.599><c> added</c><00:02:43.000><c> is</c><00:02:43.200><c> the</c><00:02:43.400><c> context</c><00:02:44.319><c> so</c><00:02:44.519><c> that</c>

00:02:44.750 --> 00:02:44.760 align:start position:0%
thing I've added is the context so that
 

00:02:44.760 --> 00:02:46.550 align:start position:0%
thing I've added is the context so that
basically<00:02:45.120><c> takes</c><00:02:45.400><c> the</c><00:02:45.599><c> context</c><00:02:46.159><c> from</c><00:02:46.360><c> the</c>

00:02:46.550 --> 00:02:46.560 align:start position:0%
basically takes the context from the
 

00:02:46.560 --> 00:02:49.830 align:start position:0%
basically takes the context from the
previous<00:02:47.120><c> task</c><00:02:47.920><c> as</c><00:02:48.080><c> the</c><00:02:48.280><c> input</c><00:02:48.640><c> for</c><00:02:48.879><c> this</c><00:02:49.720><c> this</c>

00:02:49.830 --> 00:02:49.840 align:start position:0%
previous task as the input for this this
 

00:02:49.840 --> 00:02:51.509 align:start position:0%
previous task as the input for this this
shouldn't<00:02:50.159><c> be</c><00:02:50.319><c> a</c><00:02:50.560><c> huge</c><00:02:50.879><c> change</c><00:02:51.200><c> for</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
shouldn't be a huge change for
 

00:02:51.519 --> 00:02:53.070 align:start position:0%
shouldn't be a huge change for
sequential<00:02:52.120><c> this</c><00:02:52.239><c> is</c><00:02:52.400><c> more</c><00:02:52.640><c> for</c><00:02:52.840><c> the</c>

00:02:53.070 --> 00:02:53.080 align:start position:0%
sequential this is more for the
 

00:02:53.080 --> 00:02:56.309 align:start position:0%
sequential this is more for the
hierarchical<00:02:54.080><c> models</c><00:02:54.480><c> in</c><00:02:54.720><c> there</c><00:02:55.560><c> and</c><00:02:55.760><c> finally</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
hierarchical models in there and finally
 

00:02:56.319 --> 00:02:58.710 align:start position:0%
hierarchical models in there and finally
I<00:02:56.519><c> instantiate</c><00:02:57.159><c> the</c><00:02:57.319><c> crew</c><00:02:57.879><c> and</c><00:02:58.080><c> kick</c><00:02:58.239><c> it</c><00:02:58.400><c> off</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
I instantiate the crew and kick it off
 

00:02:58.720 --> 00:03:01.110 align:start position:0%
I instantiate the crew and kick it off
so<00:02:58.920><c> that</c><00:02:59.200><c> everything</c><00:02:59.480><c> gets</c><00:02:59.920><c> going</c><00:03:00.360><c> we</c><00:03:00.480><c> can</c><00:03:00.680><c> see</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
so that everything gets going we can see
 

00:03:01.120 --> 00:03:03.550 align:start position:0%
so that everything gets going we can see
with<00:03:01.400><c> this</c><00:03:01.599><c> one</c><00:03:02.200><c> it</c><00:03:02.360><c> does</c><00:03:02.800><c> quite</c><00:03:02.959><c> a</c><00:03:03.080><c> good</c><00:03:03.319><c> job</c>

00:03:03.550 --> 00:03:03.560 align:start position:0%
with this one it does quite a good job
 

00:03:03.560 --> 00:03:05.550 align:start position:0%
with this one it does quite a good job
of<00:03:03.680><c> knowing</c><00:03:04.040><c> straight</c><00:03:04.400><c> away</c><00:03:05.159><c> that</c><00:03:05.280><c> it</c><00:03:05.360><c> needs</c>

00:03:05.550 --> 00:03:05.560 align:start position:0%
of knowing straight away that it needs
 

00:03:05.560 --> 00:03:07.630 align:start position:0%
of knowing straight away that it needs
to<00:03:05.720><c> ask</c><00:03:05.920><c> for</c><00:03:06.080><c> a</c><00:03:06.239><c> human</c><00:03:06.720><c> input</c><00:03:07.120><c> so</c><00:03:07.319><c> this</c><00:03:07.400><c> is</c><00:03:07.519><c> the</c>

00:03:07.630 --> 00:03:07.640 align:start position:0%
to ask for a human input so this is the
 

00:03:07.640 --> 00:03:09.309 align:start position:0%
to ask for a human input so this is the
human<00:03:08.000><c> input</c><00:03:08.319><c> I</c><00:03:08.440><c> put</c><00:03:08.560><c> in</c><00:03:08.680><c> the</c><00:03:08.799><c> same</c><00:03:09.040><c> human</c>

00:03:09.309 --> 00:03:09.319 align:start position:0%
human input I put in the same human
 

00:03:09.319 --> 00:03:11.509 align:start position:0%
human input I put in the same human
input<00:03:09.680><c> as</c><00:03:09.959><c> last</c><00:03:10.239><c> time</c><00:03:11.000><c> you're</c><00:03:11.120><c> going</c><00:03:11.239><c> to</c><00:03:11.319><c> see</c>

00:03:11.509 --> 00:03:11.519 align:start position:0%
input as last time you're going to see
 

00:03:11.519 --> 00:03:13.509 align:start position:0%
input as last time you're going to see
with<00:03:11.640><c> the</c><00:03:11.840><c> hierarchical</c><00:03:12.560><c> one</c><00:03:12.879><c> this</c><00:03:13.000><c> is</c><00:03:13.239><c> not</c>

00:03:13.509 --> 00:03:13.519 align:start position:0%
with the hierarchical one this is not
 

00:03:13.519 --> 00:03:16.149 align:start position:0%
with the hierarchical one this is not
the<00:03:13.720><c> case</c><00:03:14.400><c> and</c><00:03:14.519><c> I'll</c><00:03:14.720><c> go</c><00:03:14.920><c> through</c><00:03:15.239><c> the</c><00:03:15.400><c> reasons</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
the case and I'll go through the reasons
 

00:03:16.159 --> 00:03:17.550 align:start position:0%
the case and I'll go through the reasons
why<00:03:16.519><c> in</c><00:03:16.640><c> a</c><00:03:16.760><c> second</c><00:03:17.000><c> when</c><00:03:17.159><c> we</c><00:03:17.239><c> look</c><00:03:17.360><c> at</c><00:03:17.480><c> the</c>

00:03:17.550 --> 00:03:17.560 align:start position:0%
why in a second when we look at the
 

00:03:17.560 --> 00:03:19.750 align:start position:0%
why in a second when we look at the
hierarchical<00:03:18.239><c> one</c><00:03:18.879><c> now</c><00:03:19.040><c> it</c><00:03:19.200><c> goes</c><00:03:19.440><c> off</c>

00:03:19.750 --> 00:03:19.760 align:start position:0%
hierarchical one now it goes off
 

00:03:19.760 --> 00:03:22.509 align:start position:0%
hierarchical one now it goes off
basically<00:03:20.159><c> does</c><00:03:20.440><c> the</c><00:03:20.599><c> same</c><00:03:20.920><c> thing</c><00:03:21.400><c> as</c><00:03:21.640><c> before</c>

00:03:22.509 --> 00:03:22.519 align:start position:0%
basically does the same thing as before
 

00:03:22.519 --> 00:03:24.110 align:start position:0%
basically does the same thing as before
except<00:03:22.840><c> now</c><00:03:23.000><c> the</c><00:03:23.120><c> tokens</c><00:03:23.440><c> are</c><00:03:23.599><c> just</c><00:03:23.840><c> much</c>

00:03:24.110 --> 00:03:24.120 align:start position:0%
except now the tokens are just much
 

00:03:24.120 --> 00:03:27.470 align:start position:0%
except now the tokens are just much
cheaper<00:03:25.000><c> so</c><00:03:25.760><c> really</c><00:03:26.480><c> with</c><00:03:26.720><c> the</c><00:03:26.840><c> tokens</c><00:03:27.239><c> being</c>

00:03:27.470 --> 00:03:27.480 align:start position:0%
cheaper so really with the tokens being
 

00:03:27.480 --> 00:03:29.350 align:start position:0%
cheaper so really with the tokens being
much<00:03:27.680><c> cheaper</c><00:03:28.120><c> we</c><00:03:28.360><c> should</c><00:03:28.640><c> actually</c><00:03:28.920><c> take</c>

00:03:29.350 --> 00:03:29.360 align:start position:0%
much cheaper we should actually take
 

00:03:29.360 --> 00:03:33.149 align:start position:0%
much cheaper we should actually take
this<00:03:30.120><c> and</c><00:03:30.439><c> then</c><00:03:31.159><c> add</c><00:03:31.680><c> more</c><00:03:32.120><c> agents</c><00:03:32.560><c> to</c><00:03:32.720><c> it</c><00:03:32.920><c> add</c>

00:03:33.149 --> 00:03:33.159 align:start position:0%
this and then add more agents to it add
 

00:03:33.159 --> 00:03:34.990 align:start position:0%
this and then add more agents to it add
more<00:03:33.400><c> steps</c><00:03:33.760><c> to</c><00:03:33.920><c> it</c><00:03:34.159><c> and</c><00:03:34.319><c> go</c><00:03:34.519><c> through</c><00:03:34.760><c> and</c>

00:03:34.990 --> 00:03:35.000 align:start position:0%
more steps to it and go through and
 

00:03:35.000 --> 00:03:38.030 align:start position:0%
more steps to it and go through and
basically<00:03:35.640><c> look</c><00:03:35.799><c> at</c><00:03:35.920><c> it</c><00:03:36.439><c> in</c><00:03:36.680><c> here</c><00:03:37.239><c> so</c><00:03:37.519><c> finally</c>

00:03:38.030 --> 00:03:38.040 align:start position:0%
basically look at it in here so finally
 

00:03:38.040 --> 00:03:40.229 align:start position:0%
basically look at it in here so finally
final<00:03:38.519><c> output</c><00:03:39.040><c> we</c><00:03:39.120><c> can</c><00:03:39.280><c> see</c><00:03:39.560><c> we've</c><00:03:39.760><c> got</c><00:03:39.920><c> an</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
final output we can see we've got an
 

00:03:40.239 --> 00:03:42.550 align:start position:0%
final output we can see we've got an
article<00:03:41.239><c> it</c><00:03:41.439><c> hasn't</c><00:03:41.720><c> done</c><00:03:41.879><c> a</c><00:03:42.040><c> good</c><00:03:42.239><c> job</c><00:03:42.400><c> of</c>

00:03:42.550 --> 00:03:42.560 align:start position:0%
article it hasn't done a good job of
 

00:03:42.560 --> 00:03:45.390 align:start position:0%
article it hasn't done a good job of
putting<00:03:43.040><c> these</c><00:03:43.280><c> highlights</c><00:03:43.680><c> at</c><00:03:43.879><c> the</c><00:03:44.080><c> end</c><00:03:45.000><c> it</c>

00:03:45.390 --> 00:03:45.400 align:start position:0%
putting these highlights at the end it
 

00:03:45.400 --> 00:03:46.949 align:start position:0%
putting these highlights at the end it
seems<00:03:45.760><c> to</c><00:03:45.920><c> have</c><00:03:46.080><c> more</c><00:03:46.319><c> put</c><00:03:46.560><c> them</c><00:03:46.720><c> in</c><00:03:46.840><c> the</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
seems to have more put them in the
 

00:03:46.959 --> 00:03:49.470 align:start position:0%
seems to have more put them in the
middle<00:03:47.360><c> on</c><00:03:47.640><c> this</c><00:03:47.840><c> particular</c><00:03:48.280><c> approach</c><00:03:49.200><c> if</c><00:03:49.319><c> we</c>

00:03:49.470 --> 00:03:49.480 align:start position:0%
middle on this particular approach if we
 

00:03:49.480 --> 00:03:52.030 align:start position:0%
middle on this particular approach if we
look<00:03:49.640><c> at</c><00:03:49.840><c> our</c><00:03:50.319><c> callback</c><00:03:50.799><c> logs</c><00:03:51.360><c> here</c><00:03:51.720><c> we</c><00:03:51.840><c> can</c>

00:03:52.030 --> 00:03:52.040 align:start position:0%
look at our callback logs here we can
 

00:03:52.040 --> 00:03:54.550 align:start position:0%
look at our callback logs here we can
see<00:03:52.360><c> that</c><00:03:52.560><c> there's</c><00:03:52.840><c> quite</c><00:03:53.040><c> a</c><00:03:53.159><c> number</c><00:03:53.400><c> of</c><00:03:53.760><c> steps</c>

00:03:54.550 --> 00:03:54.560 align:start position:0%
see that there's quite a number of steps
 

00:03:54.560 --> 00:03:57.350 align:start position:0%
see that there's quite a number of steps
going<00:03:54.840><c> on</c><00:03:55.599><c> you</c><00:03:55.760><c> will</c><00:03:56.000><c> find</c><00:03:56.519><c> sometimes</c><00:03:56.920><c> with</c>

00:03:57.350 --> 00:03:57.360 align:start position:0%
going on you will find sometimes with
 

00:03:57.360 --> 00:03:59.470 align:start position:0%
going on you will find sometimes with
especially<00:03:57.680><c> with</c><00:03:57.799><c> the</c><00:03:57.920><c> hierarchical</c><00:03:58.640><c> one</c>

00:03:59.470 --> 00:03:59.480 align:start position:0%
especially with the hierarchical one
 

00:03:59.480 --> 00:04:02.710 align:start position:0%
especially with the hierarchical one
when<00:03:59.959><c> The</c><00:04:00.159><c> Prompt</c><00:04:00.640><c> doesn't</c><00:04:01.200><c> match</c><00:04:01.599><c> the</c><00:04:01.799><c> model</c>

00:04:02.710 --> 00:04:02.720 align:start position:0%
when The Prompt doesn't match the model
 

00:04:02.720 --> 00:04:05.149 align:start position:0%
when The Prompt doesn't match the model
as<00:04:02.840><c> much</c><00:04:03.040><c> as</c><00:04:03.159><c> we</c><00:04:03.280><c> would</c><00:04:03.519><c> like</c><00:04:03.920><c> we</c><00:04:04.040><c> will</c><00:04:04.280><c> see</c><00:04:05.040><c> you</c>

00:04:05.149 --> 00:04:05.159 align:start position:0%
as much as we would like we will see you
 

00:04:05.159 --> 00:04:06.990 align:start position:0%
as much as we would like we will see you
know<00:04:05.319><c> where</c><00:04:05.480><c> it</c><00:04:05.680><c> goes</c><00:04:06.000><c> through</c><00:04:06.400><c> multiple</c>

00:04:06.990 --> 00:04:07.000 align:start position:0%
know where it goes through multiple
 

00:04:07.000 --> 00:04:09.589 align:start position:0%
know where it goes through multiple
steps<00:04:07.599><c> trying</c><00:04:07.840><c> to</c><00:04:07.959><c> work</c><00:04:08.239><c> things</c><00:04:08.519><c> out</c><00:04:08.920><c> here</c><00:04:09.480><c> one</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
steps trying to work things out here one
 

00:04:09.599 --> 00:04:12.509 align:start position:0%
steps trying to work things out here one
of<00:04:09.760><c> the</c><00:04:09.920><c> things</c><00:04:10.280><c> that</c><00:04:10.519><c> I</c><00:04:10.599><c> would</c><00:04:10.840><c> change</c><00:04:11.519><c> here</c>

00:04:12.509 --> 00:04:12.519 align:start position:0%
of the things that I would change here
 

00:04:12.519 --> 00:04:16.430 align:start position:0%
of the things that I would change here
again<00:04:12.959><c> would</c><00:04:13.239><c> be</c><00:04:13.760><c> rewriting</c><00:04:14.720><c> the</c><00:04:15.000><c> save</c><00:04:15.799><c> tool</c>

00:04:16.430 --> 00:04:16.440 align:start position:0%
again would be rewriting the save tool
 

00:04:16.440 --> 00:04:19.030 align:start position:0%
again would be rewriting the save tool
to<00:04:16.680><c> basically</c><00:04:17.199><c> accept</c><00:04:17.519><c> a</c><00:04:17.720><c> dictionary</c><00:04:18.440><c> or</c><00:04:18.880><c> a</c>

00:04:19.030 --> 00:04:19.040 align:start position:0%
to basically accept a dictionary or a
 

00:04:19.040 --> 00:04:22.110 align:start position:0%
to basically accept a dictionary or a
string<00:04:20.040><c> in</c><00:04:20.239><c> in</c><00:04:20.440><c> this</c><00:04:20.680><c> case</c><00:04:21.040><c> so</c><00:04:21.440><c> we</c><00:04:21.560><c> can</c><00:04:21.720><c> see</c>

00:04:22.110 --> 00:04:22.120 align:start position:0%
string in in this case so we can see
 

00:04:22.120 --> 00:04:24.870 align:start position:0%
string in in this case so we can see
that<00:04:22.400><c> the</c><00:04:22.880><c> challenge</c><00:04:23.400><c> here</c><00:04:23.840><c> is</c><00:04:24.000><c> that</c><00:04:24.240><c> a</c><00:04:24.360><c> lot</c><00:04:24.520><c> of</c>

00:04:24.870 --> 00:04:24.880 align:start position:0%
that the challenge here is that a lot of
 

00:04:24.880 --> 00:04:27.150 align:start position:0%
that the challenge here is that a lot of
this<00:04:25.120><c> repetition</c><00:04:26.000><c> is</c><00:04:26.240><c> that</c><00:04:26.440><c> where</c><00:04:26.560><c> it</c><00:04:26.720><c> goes</c><00:04:26.919><c> to</c>

00:04:27.150 --> 00:04:27.160 align:start position:0%
this repetition is that where it goes to
 

00:04:27.160 --> 00:04:29.350 align:start position:0%
this repetition is that where it goes to
save<00:04:27.479><c> it</c><00:04:28.160><c> and</c><00:04:28.280><c> it</c><00:04:28.440><c> basically</c><00:04:28.800><c> is</c><00:04:28.960><c> getting</c><00:04:29.240><c> the</c>

00:04:29.350 --> 00:04:29.360 align:start position:0%
save it and it basically is getting the
 

00:04:29.360 --> 00:04:31.310 align:start position:0%
save it and it basically is getting the
action<00:04:29.720><c> input</c><00:04:29.960><c> is</c><00:04:30.080><c> not</c><00:04:30.199><c> a</c><00:04:30.320><c> valid</c><00:04:30.600><c> key</c><00:04:31.039><c> valid</c>

00:04:31.310 --> 00:04:31.320 align:start position:0%
action input is not a valid key valid
 

00:04:31.320 --> 00:04:33.270 align:start position:0%
action input is not a valid key valid
dictionary<00:04:32.320><c> you'll</c><00:04:32.479><c> see</c><00:04:32.639><c> a</c><00:04:32.720><c> lot</c><00:04:32.840><c> of</c><00:04:33.000><c> times</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
dictionary you'll see a lot of times
 

00:04:33.280 --> 00:04:35.590 align:start position:0%
dictionary you'll see a lot of times
when<00:04:33.479><c> we</c><00:04:33.680><c> see</c><00:04:33.919><c> it</c><00:04:34.039><c> where</c><00:04:34.199><c> it</c><00:04:34.320><c> goes</c><00:04:34.520><c> to</c><00:04:34.720><c> save</c><00:04:35.039><c> it</c>

00:04:35.590 --> 00:04:35.600 align:start position:0%
when we see it where it goes to save it
 

00:04:35.600 --> 00:04:38.029 align:start position:0%
when we see it where it goes to save it
it's<00:04:35.800><c> getting</c><00:04:36.160><c> errors</c><00:04:37.120><c> because</c><00:04:37.400><c> it's</c><00:04:37.680><c> not</c>

00:04:38.029 --> 00:04:38.039 align:start position:0%
it's getting errors because it's not
 

00:04:38.039 --> 00:04:39.590 align:start position:0%
it's getting errors because it's not
accepting<00:04:38.400><c> a</c><00:04:38.560><c> dictionary</c><00:04:39.039><c> in</c><00:04:39.199><c> there</c><00:04:39.400><c> and</c><00:04:39.479><c> it</c>

00:04:39.590 --> 00:04:39.600 align:start position:0%
accepting a dictionary in there and it
 

00:04:39.600 --> 00:04:42.510 align:start position:0%
accepting a dictionary in there and it
just<00:04:39.720><c> goes</c><00:04:40.000><c> in</c><00:04:40.199><c> this</c><00:04:40.520><c> repeat</c><00:04:41.160><c> processes</c><00:04:42.160><c> until</c>

00:04:42.510 --> 00:04:42.520 align:start position:0%
just goes in this repeat processes until
 

00:04:42.520 --> 00:04:45.070 align:start position:0%
just goes in this repeat processes until
eventually<00:04:43.000><c> it</c><00:04:43.280><c> gets</c><00:04:43.600><c> something</c><00:04:44.600><c> that</c><00:04:44.759><c> logs</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
eventually it gets something that logs
 

00:04:45.080 --> 00:04:46.670 align:start position:0%
eventually it gets something that logs
it<00:04:45.240><c> out</c><00:04:45.520><c> so</c><00:04:45.800><c> that</c><00:04:45.919><c> means</c><00:04:46.120><c> I</c><00:04:46.280><c> probably</c><00:04:46.479><c> should</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
it out so that means I probably should
 

00:04:46.680 --> 00:04:49.430 align:start position:0%
it out so that means I probably should
rewrite<00:04:47.120><c> that</c><00:04:47.280><c> tool</c><00:04:48.039><c> in</c><00:04:48.280><c> there</c><00:04:48.960><c> finally</c><00:04:49.280><c> we</c>

00:04:49.430 --> 00:04:49.440 align:start position:0%
rewrite that tool in there finally we
 

00:04:49.440 --> 00:04:51.110 align:start position:0%
rewrite that tool in there finally we
come<00:04:49.639><c> back</c><00:04:49.840><c> we</c><00:04:49.919><c> can</c><00:04:50.039><c> look</c><00:04:50.199><c> at</c><00:04:50.320><c> the</c><00:04:50.479><c> output</c><00:04:51.000><c> we</c>

00:04:51.110 --> 00:04:51.120 align:start position:0%
come back we can look at the output we
 

00:04:51.120 --> 00:04:53.070 align:start position:0%
come back we can look at the output we
can<00:04:51.240><c> see</c><00:04:51.440><c> that</c><00:04:51.560><c> we've</c><00:04:51.759><c> got</c><00:04:51.919><c> our</c><00:04:52.160><c> article</c><00:04:52.759><c> there</c>

00:04:53.070 --> 00:04:53.080 align:start position:0%
can see that we've got our article there
 

00:04:53.080 --> 00:04:56.270 align:start position:0%
can see that we've got our article there
now<00:04:53.240><c> let's</c><00:04:53.360><c> jump</c><00:04:53.560><c> into</c><00:04:53.919><c> the</c><00:04:54.479><c> hierarchical</c><00:04:55.280><c> one</c>

00:04:56.270 --> 00:04:56.280 align:start position:0%
now let's jump into the hierarchical one
 

00:04:56.280 --> 00:04:58.150 align:start position:0%
now let's jump into the hierarchical one
so<00:04:56.440><c> the</c><00:04:56.600><c> hierarchical</c><00:04:57.199><c> one</c><00:04:57.400><c> is</c><00:04:57.639><c> much</c><00:04:57.800><c> more</c><00:04:58.039><c> of</c>

00:04:58.150 --> 00:04:58.160 align:start position:0%
so the hierarchical one is much more of
 

00:04:58.160 --> 00:04:59.990 align:start position:0%
so the hierarchical one is much more of
a<00:04:58.360><c> challenge</c><00:04:59.039><c> not</c><00:04:59.160><c> so</c><00:04:59.280><c> much</c><00:04:59.440><c> because</c><00:04:59.759><c> we</c><00:04:59.840><c> need</c>

00:04:59.990 --> 00:05:00.000 align:start position:0%
a challenge not so much because we need
 

00:05:00.000 --> 00:05:02.870 align:start position:0%
a challenge not so much because we need
to<00:05:00.120><c> change</c><00:05:00.440><c> much</c><00:05:00.639><c> in</c><00:05:00.759><c> the</c><00:05:01.160><c> code</c><00:05:02.160><c> but</c><00:05:02.400><c> the</c><00:05:02.600><c> big</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
to change much in the code but the big
 

00:05:02.880 --> 00:05:05.749 align:start position:0%
to change much in the code but the big
thing<00:05:03.240><c> here</c><00:05:03.840><c> is</c><00:05:04.240><c> that</c><00:05:04.880><c> we've</c><00:05:05.240><c> and</c><00:05:05.360><c> you'll</c><00:05:05.520><c> see</c>

00:05:05.749 --> 00:05:05.759 align:start position:0%
thing here is that we've and you'll see
 

00:05:05.759 --> 00:05:07.870 align:start position:0%
thing here is that we've and you'll see
here<00:05:05.919><c> I</c><00:05:06.120><c> basically</c><00:05:06.440><c> set</c><00:05:06.680><c> up</c><00:05:06.880><c> the</c><00:05:07.039><c> Claude</c><00:05:07.400><c> hiu</c>

00:05:07.870 --> 00:05:07.880 align:start position:0%
here I basically set up the Claude hiu
 

00:05:07.880 --> 00:05:10.909 align:start position:0%
here I basically set up the Claude hiu
clae<00:05:08.199><c> Sonic</c><00:05:08.639><c> Claude</c><00:05:09.039><c> Opus</c><00:05:09.960><c> models</c><00:05:10.479><c> for</c><00:05:10.720><c> this</c>

00:05:10.909 --> 00:05:10.919 align:start position:0%
clae Sonic Claude Opus models for this
 

00:05:10.919 --> 00:05:13.430 align:start position:0%
clae Sonic Claude Opus models for this
one<00:05:11.759><c> the</c><00:05:11.919><c> Big</c><00:05:12.080><c> Challenge</c><00:05:12.400><c> in</c><00:05:12.639><c> here</c><00:05:13.000><c> is</c><00:05:13.240><c> that</c>

00:05:13.430 --> 00:05:13.440 align:start position:0%
one the Big Challenge in here is that
 

00:05:13.440 --> 00:05:15.270 align:start position:0%
one the Big Challenge in here is that
you've<00:05:13.840><c> got</c><00:05:14.720><c> when</c><00:05:14.840><c> you're</c><00:05:14.960><c> doing</c><00:05:15.160><c> the</c>

00:05:15.270 --> 00:05:15.280 align:start position:0%
you've got when you're doing the
 

00:05:15.280 --> 00:05:17.749 align:start position:0%
you've got when you're doing the
hierarchical<00:05:15.919><c> one</c><00:05:16.080><c> you've</c><00:05:16.280><c> got</c><00:05:16.479><c> your</c><00:05:16.800><c> agents</c>

00:05:17.749 --> 00:05:17.759 align:start position:0%
hierarchical one you've got your agents
 

00:05:17.759 --> 00:05:19.990 align:start position:0%
hierarchical one you've got your agents
but<00:05:17.880><c> you've</c><00:05:18.120><c> also</c><00:05:18.360><c> got</c><00:05:18.520><c> the</c><00:05:18.720><c> manager</c><00:05:19.360><c> agent</c>

00:05:19.990 --> 00:05:20.000 align:start position:0%
but you've also got the manager agent
 

00:05:20.000 --> 00:05:21.469 align:start position:0%
but you've also got the manager agent
and<00:05:20.120><c> the</c><00:05:20.240><c> manager</c><00:05:20.600><c> agent</c><00:05:20.919><c> you</c><00:05:21.080><c> don't</c><00:05:21.280><c> have</c><00:05:21.400><c> a</c>

00:05:21.469 --> 00:05:21.479 align:start position:0%
and the manager agent you don't have a
 

00:05:21.479 --> 00:05:23.230 align:start position:0%
and the manager agent you don't have a
lot<00:05:21.639><c> of</c><00:05:21.759><c> control</c><00:05:22.080><c> over</c><00:05:22.440><c> from</c><00:05:22.600><c> what</c><00:05:22.800><c> I</c><00:05:23.000><c> I</c><00:05:23.080><c> can</c>

00:05:23.230 --> 00:05:23.240 align:start position:0%
lot of control over from what I I can
 

00:05:23.240 --> 00:05:25.629 align:start position:0%
lot of control over from what I I can
see<00:05:23.560><c> and</c><00:05:23.720><c> it</c><00:05:23.880><c> seems</c><00:05:24.120><c> to</c><00:05:24.240><c> me</c><00:05:24.560><c> that</c><00:05:24.840><c> is</c><00:05:25.160><c> very</c><00:05:25.400><c> much</c>

00:05:25.629 --> 00:05:25.639 align:start position:0%
see and it seems to me that is very much
 

00:05:25.639 --> 00:05:28.550 align:start position:0%
see and it seems to me that is very much
written<00:05:26.039><c> for</c><00:05:26.520><c> open</c><00:05:26.919><c> AI</c><00:05:27.759><c> so</c><00:05:27.919><c> you</c><00:05:28.039><c> run</c><00:05:28.240><c> into</c><00:05:28.440><c> a</c>

00:05:28.550 --> 00:05:28.560 align:start position:0%
written for open AI so you run into a
 

00:05:28.560 --> 00:05:30.749 align:start position:0%
written for open AI so you run into a
lot<00:05:28.639><c> of</c><00:05:28.840><c> issues</c><00:05:29.280><c> with</c><00:05:29.479><c> that</c><00:05:29.880><c> of</c><00:05:30.120><c> where</c><00:05:30.400><c> things</c>

00:05:30.749 --> 00:05:30.759 align:start position:0%
lot of issues with that of where things
 

00:05:30.759 --> 00:05:33.029 align:start position:0%
lot of issues with that of where things
like<00:05:31.000><c> this</c><00:05:31.360><c> where</c><00:05:31.919><c> it</c><00:05:32.080><c> starts</c><00:05:32.400><c> off</c><00:05:32.680><c> it</c><00:05:32.800><c> works</c>

00:05:33.029 --> 00:05:33.039 align:start position:0%
like this where it starts off it works
 

00:05:33.039 --> 00:05:35.870 align:start position:0%
like this where it starts off it works
out<00:05:33.199><c> that</c><00:05:33.280><c> it</c><00:05:33.400><c> should</c><00:05:33.800><c> use</c><00:05:34.039><c> the</c><00:05:34.199><c> action</c><00:05:34.880><c> human</c>

00:05:35.870 --> 00:05:35.880 align:start position:0%
out that it should use the action human
 

00:05:35.880 --> 00:05:38.870 align:start position:0%
out that it should use the action human
but<00:05:36.479><c> the</c><00:05:36.680><c> formatting</c><00:05:37.479><c> this</c><00:05:37.840><c> doesn't</c><00:05:38.400><c> exist</c>

00:05:38.870 --> 00:05:38.880 align:start position:0%
but the formatting this doesn't exist
 

00:05:38.880 --> 00:05:40.670 align:start position:0%
but the formatting this doesn't exist
for<00:05:39.080><c> the</c><00:05:39.199><c> tool</c><00:05:39.440><c> to</c><00:05:39.639><c> work</c><00:05:39.919><c> it</c><00:05:40.039><c> needs</c><00:05:40.240><c> to</c><00:05:40.400><c> be</c>

00:05:40.670 --> 00:05:40.680 align:start position:0%
for the tool to work it needs to be
 

00:05:40.680 --> 00:05:43.950 align:start position:0%
for the tool to work it needs to be
action<00:05:41.479><c> human</c><00:05:42.000><c> without</c><00:05:42.440><c> the</c><00:05:42.919><c> brackets</c><00:05:43.560><c> around</c>

00:05:43.950 --> 00:05:43.960 align:start position:0%
action human without the brackets around
 

00:05:43.960 --> 00:05:47.189 align:start position:0%
action human without the brackets around
it<00:05:44.520><c> so</c><00:05:44.840><c> in</c><00:05:45.039><c> this</c><00:05:45.240><c> case</c><00:05:45.960><c> errors</c><00:05:46.400><c> out</c><00:05:46.800><c> it</c><00:05:46.919><c> does</c>

00:05:47.189 --> 00:05:47.199 align:start position:0%
it so in this case errors out it does
 

00:05:47.199 --> 00:05:49.830 align:start position:0%
it so in this case errors out it does
finally<00:05:47.560><c> work</c><00:05:48.000><c> where</c><00:05:48.120><c> it</c><00:05:48.280><c> gets</c><00:05:48.440><c> it</c><00:05:48.759><c> right</c><00:05:49.039><c> here</c>

00:05:49.830 --> 00:05:49.840 align:start position:0%
finally work where it gets it right here
 

00:05:49.840 --> 00:05:51.830 align:start position:0%
finally work where it gets it right here
but<00:05:49.960><c> then</c><00:05:50.039><c> it</c><00:05:50.199><c> goes</c><00:05:50.400><c> on</c><00:05:50.639><c> to</c><00:05:51.039><c> get</c><00:05:51.280><c> a</c><00:05:51.400><c> lot</c><00:05:51.520><c> of</c>

00:05:51.830 --> 00:05:51.840 align:start position:0%
but then it goes on to get a lot of
 

00:05:51.840 --> 00:05:54.629 align:start position:0%
but then it goes on to get a lot of
these<00:05:52.600><c> kind</c><00:05:52.840><c> of</c><00:05:53.160><c> errors</c><00:05:54.039><c> and</c><00:05:54.199><c> the</c><00:05:54.319><c> error</c>

00:05:54.629 --> 00:05:54.639 align:start position:0%
these kind of errors and the error
 

00:05:54.639 --> 00:05:56.830 align:start position:0%
these kind of errors and the error
coming<00:05:54.880><c> with</c><00:05:55.000><c> the</c><00:05:55.120><c> duck</c><00:05:55.440><c> ducko</c><00:05:56.440><c> the</c><00:05:56.560><c> error</c>

00:05:56.830 --> 00:05:56.840 align:start position:0%
coming with the duck ducko the error
 

00:05:56.840 --> 00:05:58.469 align:start position:0%
coming with the duck ducko the error
coming<00:05:57.240><c> with</c><00:05:57.440><c> a</c><00:05:57.560><c> number</c><00:05:57.759><c> of</c><00:05:57.919><c> these</c><00:05:58.080><c> things</c><00:05:58.360><c> and</c>

00:05:58.469 --> 00:05:58.479 align:start position:0%
coming with a number of these things and
 

00:05:58.479 --> 00:06:00.029 align:start position:0%
coming with a number of these things and
it<00:05:58.520><c> seems</c><00:05:58.720><c> to</c><00:05:58.800><c> me</c><00:05:58.919><c> that</c><00:05:59.039><c> a</c><00:05:59.160><c> number</c><00:05:59.360><c> of</c><00:05:59.720><c> these</c>

00:06:00.029 --> 00:06:00.039 align:start position:0%
it seems to me that a number of these
 

00:06:00.039 --> 00:06:03.029 align:start position:0%
it seems to me that a number of these
are<00:06:00.240><c> coming</c><00:06:00.759><c> because</c><00:06:00.960><c> of</c><00:06:01.120><c> the</c><00:06:01.319><c> manager</c><00:06:02.160><c> llm</c><00:06:02.840><c> so</c>

00:06:03.029 --> 00:06:03.039 align:start position:0%
are coming because of the manager llm so
 

00:06:03.039 --> 00:06:04.950 align:start position:0%
are coming because of the manager llm so
what<00:06:03.120><c> I</c><00:06:03.280><c> did</c><00:06:03.440><c> was</c><00:06:03.680><c> actually</c><00:06:04.360><c> originally</c><00:06:04.759><c> I</c><00:06:04.840><c> was</c>

00:06:04.950 --> 00:06:04.960 align:start position:0%
what I did was actually originally I was
 

00:06:04.960 --> 00:06:07.110 align:start position:0%
what I did was actually originally I was
using<00:06:05.280><c> Claude</c><00:06:05.680><c> Haiku</c><00:06:06.160><c> here</c><00:06:06.400><c> that</c><00:06:06.520><c> didn't</c><00:06:06.840><c> work</c>

00:06:07.110 --> 00:06:07.120 align:start position:0%
using Claude Haiku here that didn't work
 

00:06:07.120 --> 00:06:09.909 align:start position:0%
using Claude Haiku here that didn't work
I<00:06:07.240><c> swapped</c><00:06:07.759><c> out</c><00:06:08.240><c> Claude</c><00:06:08.599><c> Sonet</c><00:06:09.120><c> and</c><00:06:09.520><c> Claude</c>

00:06:09.909 --> 00:06:09.919 align:start position:0%
I swapped out Claude Sonet and Claude
 

00:06:09.919 --> 00:06:12.150 align:start position:0%
I swapped out Claude Sonet and Claude
Opus<00:06:10.919><c> I</c><00:06:11.039><c> found</c><00:06:11.199><c> that</c><00:06:11.360><c> Sonet</c><00:06:11.720><c> will</c><00:06:11.880><c> probably</c>

00:06:12.150 --> 00:06:12.160 align:start position:0%
Opus I found that Sonet will probably
 

00:06:12.160 --> 00:06:14.990 align:start position:0%
Opus I found that Sonet will probably
work<00:06:12.520><c> about</c><00:06:12.840><c> as</c><00:06:13.000><c> good</c><00:06:13.240><c> as</c><00:06:13.440><c> Opus</c><00:06:14.199><c> there</c><00:06:14.720><c> still</c>

00:06:14.990 --> 00:06:15.000 align:start position:0%
work about as good as Opus there still
 

00:06:15.000 --> 00:06:17.749 align:start position:0%
work about as good as Opus there still
the<00:06:15.120><c> challenge</c><00:06:15.520><c> is</c><00:06:15.759><c> that</c><00:06:15.960><c> the</c><00:06:16.280><c> actual</c><00:06:17.120><c> prompt</c>

00:06:17.749 --> 00:06:17.759 align:start position:0%
the challenge is that the actual prompt
 

00:06:17.759 --> 00:06:21.189 align:start position:0%
the challenge is that the actual prompt
for<00:06:18.039><c> the</c><00:06:18.160><c> manager</c><00:06:18.639><c> llm</c><00:06:19.520><c> is</c><00:06:19.720><c> not</c><00:06:20.000><c> being</c><00:06:20.479><c> exposed</c>

00:06:21.189 --> 00:06:21.199 align:start position:0%
for the manager llm is not being exposed
 

00:06:21.199 --> 00:06:23.830 align:start position:0%
for the manager llm is not being exposed
from<00:06:21.560><c> what</c><00:06:21.680><c> I</c><00:06:21.800><c> can</c><00:06:21.960><c> see</c><00:06:22.840><c> and</c><00:06:22.960><c> for</c><00:06:23.120><c> me</c><00:06:23.360><c> this</c><00:06:23.599><c> is</c>

00:06:23.830 --> 00:06:23.840 align:start position:0%
from what I can see and for me this is
 

00:06:23.840 --> 00:06:26.230 align:start position:0%
from what I can see and for me this is
what<00:06:24.080><c> causes</c><00:06:24.479><c> us</c><00:06:24.639><c> to</c><00:06:24.800><c> have</c><00:06:25.160><c> not</c><00:06:25.479><c> good</c><00:06:25.759><c> results</c>

00:06:26.230 --> 00:06:26.240 align:start position:0%
what causes us to have not good results
 

00:06:26.240 --> 00:06:29.029 align:start position:0%
what causes us to have not good results
out<00:06:27.120><c> here</c><00:06:27.520><c> we</c><00:06:27.639><c> do</c><00:06:27.800><c> end</c><00:06:27.960><c> up</c><00:06:28.120><c> getting</c><00:06:28.479><c> an</c><00:06:28.639><c> article</c>

00:06:29.029 --> 00:06:29.039 align:start position:0%
out here we do end up getting an article
 

00:06:29.039 --> 00:06:31.070 align:start position:0%
out here we do end up getting an article
out<00:06:29.160><c> of</c><00:06:29.360><c> this</c><00:06:29.759><c> where</c><00:06:30.120><c> okay</c><00:06:30.280><c> the</c><00:06:30.400><c> article</c><00:06:30.800><c> is</c>

00:06:31.070 --> 00:06:31.080 align:start position:0%
out of this where okay the article is
 

00:06:31.080 --> 00:06:33.950 align:start position:0%
out of this where okay the article is
quite<00:06:31.280><c> nice</c><00:06:31.520><c> cuz</c><00:06:31.680><c> it's</c><00:06:31.919><c> used</c><00:06:32.639><c> more</c><00:06:33.000><c> stuff</c><00:06:33.400><c> I</c><00:06:33.560><c> do</c>

00:06:33.950 --> 00:06:33.960 align:start position:0%
quite nice cuz it's used more stuff I do
 

00:06:33.960 --> 00:06:35.390 align:start position:0%
quite nice cuz it's used more stuff I do
think<00:06:34.280><c> that</c><00:06:34.520><c> we've</c><00:06:34.720><c> definitely</c><00:06:35.080><c> got</c><00:06:35.199><c> some</c>

00:06:35.390 --> 00:06:35.400 align:start position:0%
think that we've definitely got some
 

00:06:35.400 --> 00:06:38.110 align:start position:0%
think that we've definitely got some
hallucination<00:06:36.199><c> going</c><00:06:36.400><c> on</c><00:06:36.720><c> in</c><00:06:36.960><c> here</c><00:06:37.840><c> and</c><00:06:37.960><c> it's</c>

00:06:38.110 --> 00:06:38.120 align:start position:0%
hallucination going on in here and it's
 

00:06:38.120 --> 00:06:40.390 align:start position:0%
hallucination going on in here and it's
done<00:06:38.280><c> a</c><00:06:38.440><c> nicer</c><00:06:38.880><c> job</c><00:06:39.280><c> of</c><00:06:39.520><c> sticking</c><00:06:39.880><c> to</c><00:06:40.160><c> putting</c>

00:06:40.390 --> 00:06:40.400 align:start position:0%
done a nicer job of sticking to putting
 

00:06:40.400 --> 00:06:42.189 align:start position:0%
done a nicer job of sticking to putting
the<00:06:40.560><c> key</c><00:06:40.800><c> highlights</c><00:06:41.240><c> at</c><00:06:41.360><c> the</c><00:06:41.520><c> end</c><00:06:41.800><c> and</c><00:06:41.960><c> stuff</c>

00:06:42.189 --> 00:06:42.199 align:start position:0%
the key highlights at the end and stuff
 

00:06:42.199 --> 00:06:44.309 align:start position:0%
the key highlights at the end and stuff
like<00:06:42.400><c> that</c><00:06:43.039><c> I</c><00:06:43.120><c> feel</c><00:06:43.520><c> it's</c><00:06:43.840><c> definitely</c><00:06:44.160><c> not</c>

00:06:44.309 --> 00:06:44.319 align:start position:0%
like that I feel it's definitely not
 

00:06:44.319 --> 00:06:46.150 align:start position:0%
like that I feel it's definitely not
been<00:06:44.560><c> succinct</c><00:06:45.120><c> in</c><00:06:45.280><c> here</c><00:06:45.560><c> it's</c><00:06:45.759><c> including</c>

00:06:46.150 --> 00:06:46.160 align:start position:0%
been succinct in here it's including
 

00:06:46.160 --> 00:06:48.150 align:start position:0%
been succinct in here it's including
information<00:06:46.560><c> about</c><00:06:46.759><c> T5</c><00:06:47.280><c> and</c><00:06:47.479><c> Bert</c><00:06:47.960><c> where</c>

00:06:48.150 --> 00:06:48.160 align:start position:0%
information about T5 and Bert where
 

00:06:48.160 --> 00:06:51.070 align:start position:0%
information about T5 and Bert where
these<00:06:48.319><c> are</c><00:06:48.560><c> really</c><00:06:48.840><c> not</c><00:06:49.520><c> that</c><00:06:49.759><c> applicable</c><00:06:50.720><c> to</c>

00:06:51.070 --> 00:06:51.080 align:start position:0%
these are really not that applicable to
 

00:06:51.080 --> 00:06:54.309 align:start position:0%
these are really not that applicable to
what<00:06:51.240><c> we</c><00:06:51.440><c> wanted</c><00:06:52.240><c> out</c><00:06:52.400><c> of</c><00:06:52.759><c> this</c><00:06:53.479><c> so</c><00:06:53.680><c> anyway</c><00:06:54.080><c> but</c>

00:06:54.309 --> 00:06:54.319 align:start position:0%
what we wanted out of this so anyway but
 

00:06:54.319 --> 00:06:55.430 align:start position:0%
what we wanted out of this so anyway but
this<00:06:54.440><c> certainly</c><00:06:54.759><c> shows</c><00:06:55.039><c> some</c><00:06:55.160><c> of</c><00:06:55.280><c> the</c>

00:06:55.430 --> 00:06:55.440 align:start position:0%
this certainly shows some of the
 

00:06:55.440 --> 00:06:57.070 align:start position:0%
this certainly shows some of the
challenges<00:06:55.960><c> that</c><00:06:56.039><c> you</c><00:06:56.240><c> face</c><00:06:56.520><c> when</c><00:06:56.639><c> you</c><00:06:56.759><c> do</c>

00:06:57.070 --> 00:06:57.080 align:start position:0%
challenges that you face when you do
 

00:06:57.080 --> 00:06:58.830 align:start position:0%
challenges that you face when you do
this<00:06:57.639><c> uh</c><00:06:57.759><c> and</c><00:06:57.879><c> this</c><00:06:57.960><c> is</c><00:06:58.080><c> one</c><00:06:58.199><c> of</c><00:06:58.319><c> my</c><00:06:58.479><c> biggest</c>

00:06:58.830 --> 00:06:58.840 align:start position:0%
this uh and this is one of my biggest
 

00:06:58.840 --> 00:07:01.510 align:start position:0%
this uh and this is one of my biggest
frustrations<00:06:59.879><c> with</c><00:07:00.160><c> using</c><00:07:00.840><c> these</c><00:07:01.120><c> kind</c><00:07:01.280><c> of</c>

00:07:01.510 --> 00:07:01.520 align:start position:0%
frustrations with using these kind of
 

00:07:01.520 --> 00:07:05.350 align:start position:0%
frustrations with using these kind of
things<00:07:01.960><c> like</c><00:07:02.280><c> crew</c><00:07:02.680><c> AI</c><00:07:03.680><c> is</c><00:07:03.919><c> it</c><00:07:04.240><c> in</c><00:07:04.400><c> the</c><00:07:04.680><c> end</c><00:07:05.160><c> you</c>

00:07:05.350 --> 00:07:05.360 align:start position:0%
things like crew AI is it in the end you
 

00:07:05.360 --> 00:07:07.670 align:start position:0%
things like crew AI is it in the end you
want<00:07:05.560><c> to</c><00:07:05.759><c> be</c><00:07:05.919><c> able</c><00:07:06.160><c> to</c><00:07:06.440><c> have</c><00:07:06.680><c> full</c><00:07:07.039><c> control</c>

00:07:07.670 --> 00:07:07.680 align:start position:0%
want to be able to have full control
 

00:07:07.680 --> 00:07:10.070 align:start position:0%
want to be able to have full control
over<00:07:08.000><c> every</c><00:07:08.280><c> single</c><00:07:08.680><c> prompt</c><00:07:09.560><c> and</c><00:07:09.680><c> you</c><00:07:09.800><c> want</c><00:07:09.919><c> to</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
over every single prompt and you want to
 

00:07:10.080 --> 00:07:12.550 align:start position:0%
over every single prompt and you want to
be<00:07:10.199><c> able</c><00:07:10.400><c> to</c><00:07:10.680><c> clearly</c><00:07:11.160><c> see</c><00:07:11.879><c> the</c><00:07:12.039><c> prompts</c>

00:07:12.550 --> 00:07:12.560 align:start position:0%
be able to clearly see the prompts
 

00:07:12.560 --> 00:07:15.189 align:start position:0%
be able to clearly see the prompts
what's<00:07:12.800><c> going</c><00:07:13.039><c> in</c><00:07:13.400><c> what's</c><00:07:13.960><c> coming</c><00:07:14.400><c> out</c><00:07:14.960><c> in</c>

00:07:15.189 --> 00:07:15.199 align:start position:0%
what's going in what's coming out in
 

00:07:15.199 --> 00:07:16.950 align:start position:0%
what's going in what's coming out in
here<00:07:16.039><c> this</c><00:07:16.120><c> is</c><00:07:16.280><c> something</c><00:07:16.560><c> that</c><00:07:16.720><c> something</c>

00:07:16.950 --> 00:07:16.960 align:start position:0%
here this is something that something
 

00:07:16.960 --> 00:07:19.070 align:start position:0%
here this is something that something
like<00:07:17.120><c> crew</c><00:07:17.440><c> AI</c><00:07:17.960><c> where</c><00:07:18.080><c> it's</c><00:07:18.400><c> operating</c><00:07:18.840><c> at</c><00:07:18.960><c> a</c>

00:07:19.070 --> 00:07:19.080 align:start position:0%
like crew AI where it's operating at a
 

00:07:19.080 --> 00:07:21.270 align:start position:0%
like crew AI where it's operating at a
much<00:07:19.360><c> higher</c><00:07:19.720><c> level</c><00:07:20.560><c> you're</c><00:07:20.720><c> going</c><00:07:20.840><c> to</c><00:07:20.960><c> end</c><00:07:21.120><c> up</c>

00:07:21.270 --> 00:07:21.280 align:start position:0%
much higher level you're going to end up
 

00:07:21.280 --> 00:07:23.189 align:start position:0%
much higher level you're going to end up
bumping<00:07:21.639><c> your</c><00:07:21.879><c> head</c><00:07:22.240><c> against</c><00:07:22.680><c> these</c><00:07:22.919><c> kind</c><00:07:23.039><c> of</c>

00:07:23.189 --> 00:07:23.199 align:start position:0%
bumping your head against these kind of
 

00:07:23.199 --> 00:07:24.710 align:start position:0%
bumping your head against these kind of
things<00:07:23.560><c> when</c><00:07:23.720><c> you</c><00:07:23.800><c> want</c><00:07:23.919><c> to</c><00:07:24.080><c> start</c><00:07:24.319><c> doing</c>

00:07:24.710 --> 00:07:24.720 align:start position:0%
things when you want to start doing
 

00:07:24.720 --> 00:07:27.029 align:start position:0%
things when you want to start doing
stuff<00:07:25.400><c> that</c><00:07:25.599><c> perhaps</c><00:07:25.919><c> is</c><00:07:26.120><c> not</c><00:07:26.680><c> directly</c>

00:07:27.029 --> 00:07:27.039 align:start position:0%
stuff that perhaps is not directly
 

00:07:27.039 --> 00:07:29.869 align:start position:0%
stuff that perhaps is not directly
supported<00:07:27.639><c> now</c><00:07:27.800><c> my</c><00:07:27.960><c> guess</c><00:07:28.160><c> is</c><00:07:28.440><c> that</c><00:07:28.840><c> with</c><00:07:29.039><c> crew</c>

00:07:29.869 --> 00:07:29.879 align:start position:0%
supported now my guess is that with crew
 

00:07:29.879 --> 00:07:32.029 align:start position:0%
supported now my guess is that with crew
they<00:07:30.000><c> will</c><00:07:30.280><c> add</c><00:07:30.599><c> these</c><00:07:30.759><c> things</c><00:07:31.080><c> over</c><00:07:31.400><c> time</c><00:07:31.919><c> I</c>

00:07:32.029 --> 00:07:32.039 align:start position:0%
they will add these things over time I
 

00:07:32.039 --> 00:07:34.189 align:start position:0%
they will add these things over time I
can't<00:07:32.199><c> see</c><00:07:32.400><c> any</c><00:07:32.599><c> reason</c><00:07:33.000><c> why</c><00:07:33.240><c> they</c><00:07:33.360><c> wouldn't</c>

00:07:34.189 --> 00:07:34.199 align:start position:0%
can't see any reason why they wouldn't
 

00:07:34.199 --> 00:07:37.629 align:start position:0%
can't see any reason why they wouldn't
add<00:07:34.840><c> the</c><00:07:35.039><c> clae</c><00:07:35.479><c> support</c><00:07:35.960><c> in</c><00:07:36.199><c> here</c><00:07:37.120><c> but</c><00:07:37.280><c> we</c><00:07:37.479><c> are</c>

00:07:37.629 --> 00:07:37.639 align:start position:0%
add the clae support in here but we are
 

00:07:37.639 --> 00:07:39.390 align:start position:0%
add the clae support in here but we are
in<00:07:37.800><c> this</c><00:07:37.919><c> sort</c><00:07:38.080><c> of</c><00:07:38.240><c> transitioning</c><00:07:38.960><c> period</c><00:07:39.280><c> at</c>

00:07:39.390 --> 00:07:39.400 align:start position:0%
in this sort of transitioning period at
 

00:07:39.400 --> 00:07:41.589 align:start position:0%
in this sort of transitioning period at
the<00:07:39.560><c> moment</c><00:07:40.039><c> of</c><00:07:40.280><c> where</c><00:07:40.520><c> we're</c><00:07:40.759><c> going</c><00:07:41.160><c> from</c>

00:07:41.589 --> 00:07:41.599 align:start position:0%
the moment of where we're going from
 

00:07:41.599 --> 00:07:44.309 align:start position:0%
the moment of where we're going from
things<00:07:41.919><c> that</c><00:07:42.160><c> only</c><00:07:42.440><c> worked</c><00:07:42.759><c> with</c><00:07:42.960><c> open</c><00:07:43.280><c> AI</c><00:07:44.080><c> to</c>

00:07:44.309 --> 00:07:44.319 align:start position:0%
things that only worked with open AI to
 

00:07:44.319 --> 00:07:47.270 align:start position:0%
things that only worked with open AI to
now<00:07:44.639><c> working</c><00:07:45.039><c> with</c><00:07:45.319><c> multiple</c><00:07:45.919><c> models</c><00:07:46.599><c> both</c>

00:07:47.270 --> 00:07:47.280 align:start position:0%
now working with multiple models both
 

00:07:47.280 --> 00:07:49.469 align:start position:0%
now working with multiple models both
proprietary<00:07:47.919><c> models</c><00:07:48.360><c> like</c><00:07:48.520><c> the</c><00:07:48.680><c> cord</c><00:07:49.039><c> models</c>

00:07:49.469 --> 00:07:49.479 align:start position:0%
proprietary models like the cord models
 

00:07:49.479 --> 00:07:51.830 align:start position:0%
proprietary models like the cord models
but<00:07:49.639><c> also</c><00:07:50.159><c> open</c><00:07:50.479><c> source</c><00:07:50.840><c> models</c><00:07:51.520><c> that</c><00:07:51.639><c> are</c>

00:07:51.830 --> 00:07:51.840 align:start position:0%
but also open source models that are
 

00:07:51.840 --> 00:07:54.230 align:start position:0%
but also open source models that are
actually<00:07:52.159><c> quite</c><00:07:52.400><c> good</c><00:07:52.680><c> out</c><00:07:53.000><c> there</c><00:07:53.800><c> and</c><00:07:54.039><c> a</c><00:07:54.120><c> lot</c>

00:07:54.230 --> 00:07:54.240 align:start position:0%
actually quite good out there and a lot
 

00:07:54.240 --> 00:07:56.390 align:start position:0%
actually quite good out there and a lot
of<00:07:54.479><c> the</c><00:07:54.879><c> Frameworks</c><00:07:55.360><c> have</c><00:07:55.560><c> just</c><00:07:55.680><c> been</c><00:07:56.000><c> so</c>

00:07:56.390 --> 00:07:56.400 align:start position:0%
of the Frameworks have just been so
 

00:07:56.400 --> 00:07:59.790 align:start position:0%
of the Frameworks have just been so
optimized<00:07:57.240><c> for</c><00:07:57.599><c> the</c><00:07:57.759><c> open</c><00:07:58.120><c> a</c><00:07:58.599><c> system</c><00:07:59.639><c> they're</c>

00:07:59.790 --> 00:07:59.800 align:start position:0%
optimized for the open a system they're
 

00:07:59.800 --> 00:08:01.670 align:start position:0%
optimized for the open a system they're
often<00:08:00.039><c> only</c><00:08:00.280><c> getting</c><00:08:00.560><c> the</c><00:08:00.680><c> best</c><00:08:00.960><c> results</c><00:08:01.400><c> from</c>

00:08:01.670 --> 00:08:01.680 align:start position:0%
often only getting the best results from
 

00:08:01.680 --> 00:08:03.230 align:start position:0%
often only getting the best results from
that<00:08:02.159><c> one</c><00:08:02.280><c> of</c><00:08:02.360><c> the</c><00:08:02.440><c> things</c><00:08:02.639><c> you</c><00:08:02.759><c> could</c><00:08:02.960><c> always</c>

00:08:03.230 --> 00:08:03.240 align:start position:0%
that one of the things you could always
 

00:08:03.240 --> 00:08:06.869 align:start position:0%
that one of the things you could always
do<00:08:03.479><c> in</c><00:08:03.680><c> here</c><00:08:04.080><c> is</c><00:08:04.319><c> then</c><00:08:04.520><c> bring</c><00:08:04.800><c> in</c><00:08:05.080><c> open</c><00:08:05.680><c> Ai</c><00:08:06.680><c> and</c>

00:08:06.869 --> 00:08:06.879 align:start position:0%
do in here is then bring in open Ai and
 

00:08:06.879 --> 00:08:11.230 align:start position:0%
do in here is then bring in open Ai and
we<00:08:07.000><c> could</c><00:08:07.199><c> use</c><00:08:07.680><c> the</c><00:08:07.960><c> open</c><00:08:08.680><c> AI</c><00:08:09.680><c> as</c><00:08:10.199><c> the</c><00:08:10.759><c> manager</c>

00:08:11.230 --> 00:08:11.240 align:start position:0%
we could use the open AI as the manager
 

00:08:11.240 --> 00:08:13.510 align:start position:0%
we could use the open AI as the manager
llm<00:08:11.800><c> so</c><00:08:11.960><c> don't</c><00:08:12.120><c> forget</c><00:08:12.479><c> we</c><00:08:12.639><c> can</c><00:08:12.879><c> actually</c><00:08:13.159><c> set</c>

00:08:13.510 --> 00:08:13.520 align:start position:0%
llm so don't forget we can actually set
 

00:08:13.520 --> 00:08:16.550 align:start position:0%
llm so don't forget we can actually set
the<00:08:13.680><c> manager</c><00:08:14.120><c> llm</c><00:08:14.919><c> to</c><00:08:15.159><c> be</c><00:08:15.479><c> open</c><00:08:15.759><c> Ai</c><00:08:16.240><c> and</c><00:08:16.360><c> then</c>

00:08:16.550 --> 00:08:16.560 align:start position:0%
the manager llm to be open Ai and then
 

00:08:16.560 --> 00:08:19.670 align:start position:0%
the manager llm to be open Ai and then
have<00:08:16.720><c> all</c><00:08:16.960><c> the</c><00:08:17.199><c> agents</c><00:08:18.199><c> actually</c><00:08:18.680><c> be</c><00:08:19.280><c> clawed</c>

00:08:19.670 --> 00:08:19.680 align:start position:0%
have all the agents actually be clawed
 

00:08:19.680 --> 00:08:20.950 align:start position:0%
have all the agents actually be clawed
and<00:08:19.800><c> stuff</c><00:08:20.039><c> like</c><00:08:20.199><c> that</c><00:08:20.479><c> that's</c><00:08:20.680><c> something</c>

00:08:20.950 --> 00:08:20.960 align:start position:0%
and stuff like that that's something
 

00:08:20.960 --> 00:08:22.589 align:start position:0%
and stuff like that that's something
that<00:08:21.039><c> you</c><00:08:21.159><c> can</c><00:08:21.280><c> certainly</c><00:08:21.680><c> try</c><00:08:22.039><c> out</c><00:08:22.360><c> if</c><00:08:22.479><c> you</c>

00:08:22.589 --> 00:08:22.599 align:start position:0%
that you can certainly try out if you
 

00:08:22.599 --> 00:08:26.149 align:start position:0%
that you can certainly try out if you
want<00:08:22.840><c> to</c><00:08:23.560><c> do</c><00:08:23.919><c> this</c><00:08:24.919><c> anyway</c><00:08:25.240><c> I</c><00:08:25.360><c> might</c><00:08:25.520><c> Revis</c>

00:08:26.149 --> 00:08:26.159 align:start position:0%
want to do this anyway I might Revis
 

00:08:26.159 --> 00:08:28.990 align:start position:0%
want to do this anyway I might Revis
this<00:08:26.440><c> a</c><00:08:26.560><c> little</c><00:08:26.759><c> bit</c><00:08:27.000><c> more</c><00:08:27.599><c> this</c><00:08:27.759><c> sort</c><00:08:27.960><c> of</c><00:08:28.280><c> idea</c>

00:08:28.990 --> 00:08:29.000 align:start position:0%
this a little bit more this sort of idea
 

00:08:29.000 --> 00:08:30.670 align:start position:0%
this a little bit more this sort of idea
and<00:08:29.159><c> as</c><00:08:29.599><c> at</c><00:08:29.680><c> some</c><00:08:29.840><c> other</c><00:08:30.039><c> ways</c><00:08:30.280><c> that</c><00:08:30.400><c> we</c><00:08:30.520><c> could</c>

00:08:30.670 --> 00:08:30.680 align:start position:0%
and as at some other ways that we could
 

00:08:30.680 --> 00:08:32.870 align:start position:0%
and as at some other ways that we could
do<00:08:30.919><c> this</c><00:08:31.240><c> in</c><00:08:31.360><c> a</c><00:08:31.520><c> future</c><00:08:31.879><c> video</c><00:08:32.279><c> and</c><00:08:32.479><c> stuff</c><00:08:32.719><c> as</c>

00:08:32.870 --> 00:08:32.880 align:start position:0%
do this in a future video and stuff as
 

00:08:32.880 --> 00:08:35.350 align:start position:0%
do this in a future video and stuff as
well<00:08:33.839><c> anyway</c><00:08:34.279><c> as</c><00:08:34.440><c> always</c><00:08:34.680><c> if</c><00:08:34.760><c> You'</c><00:08:34.880><c> got</c><00:08:35.080><c> any</c>

00:08:35.350 --> 00:08:35.360 align:start position:0%
well anyway as always if You' got any
 

00:08:35.360 --> 00:08:36.949 align:start position:0%
well anyway as always if You' got any
comments<00:08:35.719><c> or</c><00:08:35.959><c> questions</c><00:08:36.399><c> or</c><00:08:36.560><c> spotted</c>

00:08:36.949 --> 00:08:36.959 align:start position:0%
comments or questions or spotted
 

00:08:36.959 --> 00:08:38.630 align:start position:0%
comments or questions or spotted
something<00:08:37.279><c> that</c><00:08:37.479><c> I've</c><00:08:37.680><c> left</c><00:08:37.919><c> out</c><00:08:38.159><c> here</c><00:08:38.440><c> please</c>

00:08:38.630 --> 00:08:38.640 align:start position:0%
something that I've left out here please
 

00:08:38.640 --> 00:08:40.829 align:start position:0%
something that I've left out here please
let<00:08:38.760><c> me</c><00:08:38.880><c> know</c><00:08:39.080><c> in</c><00:08:39.159><c> the</c><00:08:39.279><c> comments</c><00:08:39.680><c> below</c><00:08:40.680><c> plan</c>

00:08:40.829 --> 00:08:40.839 align:start position:0%
let me know in the comments below plan
 

00:08:40.839 --> 00:08:42.750 align:start position:0%
let me know in the comments below plan
on<00:08:40.959><c> doing</c><00:08:41.320><c> a</c><00:08:41.399><c> number</c><00:08:41.599><c> of</c><00:08:41.760><c> series</c><00:08:42.159><c> about</c><00:08:42.599><c> these</c>

00:08:42.750 --> 00:08:42.760 align:start position:0%
on doing a number of series about these
 

00:08:42.760 --> 00:08:45.269 align:start position:0%
on doing a number of series about these
kind<00:08:42.880><c> of</c><00:08:43.000><c> things</c><00:08:43.279><c> like</c><00:08:43.479><c> autogen</c><00:08:44.920><c> Lang</c>

00:08:45.269 --> 00:08:45.279 align:start position:0%
kind of things like autogen Lang
 

00:08:45.279 --> 00:08:47.150 align:start position:0%
kind of things like autogen Lang
graph<00:08:45.600><c> and</c><00:08:45.760><c> stuff</c><00:08:46.000><c> like</c><00:08:46.240><c> that</c><00:08:46.880><c> and</c><00:08:47.000><c> I'll</c>

00:08:47.150 --> 00:08:47.160 align:start position:0%
graph and stuff like that and I'll
 

00:08:47.160 --> 00:08:48.630 align:start position:0%
graph and stuff like that and I'll
probably<00:08:47.480><c> also</c><00:08:47.760><c> show</c><00:08:48.000><c> you</c><00:08:48.200><c> you</c><00:08:48.279><c> know</c><00:08:48.399><c> how</c><00:08:48.519><c> I</c>

00:08:48.630 --> 00:08:48.640 align:start position:0%
probably also show you you know how I
 

00:08:48.640 --> 00:08:50.030 align:start position:0%
probably also show you you know how I
build<00:08:48.880><c> some</c><00:08:49.000><c> of</c><00:08:49.120><c> these</c><00:08:49.279><c> things</c><00:08:49.519><c> just</c><00:08:49.680><c> from</c>

00:08:50.030 --> 00:08:50.040 align:start position:0%
build some of these things just from
 

00:08:50.040 --> 00:08:52.470 align:start position:0%
build some of these things just from
scratch<00:08:50.839><c> with</c><00:08:51.040><c> normal</c><00:08:51.480><c> python</c><00:08:51.959><c> to</c><00:08:52.200><c> sort</c><00:08:52.360><c> of</c>

00:08:52.470 --> 00:08:52.480 align:start position:0%
scratch with normal python to sort of
 

00:08:52.480 --> 00:08:54.870 align:start position:0%
scratch with normal python to sort of
run<00:08:52.760><c> these</c><00:08:52.959><c> things</c><00:08:53.240><c> as</c><00:08:53.399><c> well</c><00:08:54.279><c> anyway</c><00:08:54.720><c> as</c>

00:08:54.870 --> 00:08:54.880 align:start position:0%
run these things as well anyway as
 

00:08:54.880 --> 00:08:56.430 align:start position:0%
run these things as well anyway as
always<00:08:55.160><c> if</c><00:08:55.279><c> you</c><00:08:55.399><c> found</c><00:08:55.600><c> the</c><00:08:55.720><c> video</c><00:08:56.000><c> useful</c>

00:08:56.430 --> 00:08:56.440 align:start position:0%
always if you found the video useful
 

00:08:56.440 --> 00:08:58.430 align:start position:0%
always if you found the video useful
please<00:08:56.680><c> click</c><00:08:56.920><c> like</c><00:08:57.040><c> And</c><00:08:57.240><c> subscribe</c><00:08:58.200><c> I</c><00:08:58.279><c> will</c>

00:08:58.430 --> 00:08:58.440 align:start position:0%
please click like And subscribe I will
 

00:08:58.440 --> 00:09:00.110 align:start position:0%
please click like And subscribe I will
talk<00:08:58.600><c> to</c><00:08:58.680><c> you</c><00:08:58.760><c> in</c><00:08:58.839><c> the</c><00:08:58.959><c> next</c><00:08:59.120><c> video</c><00:08:59.720><c> bye</c><00:08:59.920><c> for</c>

00:09:00.110 --> 00:09:00.120 align:start position:0%
talk to you in the next video bye for
 

00:09:00.120 --> 00:09:03.079 align:start position:0%
talk to you in the next video bye for
now

