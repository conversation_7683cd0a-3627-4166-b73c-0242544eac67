WEBVTT
Kind: captions
Language: en

00:00:00.680 --> 00:00:02.430 align:start position:0%
 
hey<00:00:00.840><c> everyone</c><00:00:01.400><c> this</c><00:00:01.520><c> is</c><00:00:01.680><c> Ravi</c><00:00:02.000><c> from</c><00:00:02.159><c> Lama</c>

00:00:02.430 --> 00:00:02.440 align:start position:0%
hey everyone this is <PERSON> from <PERSON>
 

00:00:02.440 --> 00:00:04.950 align:start position:0%
hey everyone this is <PERSON> from <PERSON>
index<00:00:03.439><c> and</c><00:00:03.919><c> welcome</c><00:00:04.200><c> to</c><00:00:04.359><c> this</c><00:00:04.520><c> series</c><00:00:04.799><c> of</c>

00:00:04.950 --> 00:00:04.960 align:start position:0%
index and welcome to this series of
 

00:00:04.960 --> 00:00:07.269 align:start position:0%
index and welcome to this series of
videos<00:00:05.240><c> on</c><00:00:05.680><c> building</c><00:00:06.120><c> llm</c><00:00:06.560><c> applications</c><00:00:07.120><c> with</c>

00:00:07.269 --> 00:00:07.279 align:start position:0%
videos on building llm applications with
 

00:00:07.279 --> 00:00:10.990 align:start position:0%
videos on building llm applications with
Lama<00:00:07.600><c> index</c><00:00:07.839><c> and</c><00:00:08.880><c> cloud3</c><00:00:09.880><c> so</c><00:00:10.120><c> recently</c>

00:00:10.990 --> 00:00:11.000 align:start position:0%
Lama index and cloud3 so recently
 

00:00:11.000 --> 00:00:13.950 align:start position:0%
Lama index and cloud3 so recently
anthropic<00:00:11.519><c> has</c><00:00:11.679><c> released</c><00:00:12.639><c> um</c><00:00:13.320><c> a</c><00:00:13.519><c> series</c><00:00:13.799><c> of</c>

00:00:13.950 --> 00:00:13.960 align:start position:0%
anthropic has released um a series of
 

00:00:13.960 --> 00:00:18.710 align:start position:0%
anthropic has released um a series of
models<00:00:14.360><c> with</c><00:00:14.559><c> cloud3</c><00:00:15.200><c> Opus</c><00:00:15.719><c> Sonet</c><00:00:16.440><c> and</c><00:00:16.840><c> uh</c><00:00:17.720><c> IU</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
models with cloud3 Opus Sonet and uh IU
 

00:00:18.720 --> 00:00:21.269 align:start position:0%
models with cloud3 Opus Sonet and uh IU
um<00:00:19.119><c> so</c><00:00:19.640><c> we</c><00:00:19.800><c> have</c><00:00:19.920><c> released</c><00:00:20.359><c> a</c><00:00:20.519><c> set</c><00:00:20.760><c> of</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
um so we have released a set of
 

00:00:21.279 --> 00:00:23.589 align:start position:0%
um so we have released a set of
notebooks<00:00:22.160><c> under</c><00:00:22.519><c> anthropic</c><00:00:23.000><c> cookbooks</c><00:00:23.439><c> to</c>

00:00:23.589 --> 00:00:23.599 align:start position:0%
notebooks under anthropic cookbooks to
 

00:00:23.599 --> 00:00:26.029 align:start position:0%
notebooks under anthropic cookbooks to
help<00:00:23.800><c> you</c><00:00:24.599><c> quickly</c><00:00:25.039><c> start</c><00:00:25.320><c> building</c><00:00:25.640><c> LM</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
help you quickly start building LM
 

00:00:26.039 --> 00:00:28.710 align:start position:0%
help you quickly start building LM
applications<00:00:26.480><c> with</c><00:00:26.640><c> Lama</c><00:00:26.920><c> index</c><00:00:27.160><c> and</c><00:00:27.439><c> cla3</c><00:00:28.439><c> so</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
applications with Lama index and cla3 so
 

00:00:28.720 --> 00:00:31.550 align:start position:0%
applications with Lama index and cla3 so
in<00:00:28.880><c> this</c><00:00:29.080><c> video</c><00:00:29.800><c> uh</c><00:00:30.119><c> um</c><00:00:30.880><c> we</c><00:00:31.080><c> look</c><00:00:31.279><c> into</c>

00:00:31.550 --> 00:00:31.560 align:start position:0%
in this video uh um we look into
 

00:00:31.560 --> 00:00:34.150 align:start position:0%
in this video uh um we look into
building<00:00:31.960><c> a</c><00:00:32.160><c> rag</c><00:00:32.480><c> Pipeline</c><00:00:32.960><c> with</c><00:00:33.520><c> Lama</c><00:00:33.840><c> index</c>

00:00:34.150 --> 00:00:34.160 align:start position:0%
building a rag Pipeline with Lama index
 

00:00:34.160 --> 00:00:37.549 align:start position:0%
building a rag Pipeline with Lama index
and<00:00:34.320><c> cla3</c><00:00:35.160><c> which</c><00:00:35.280><c> is</c><00:00:35.399><c> the</c><00:00:35.600><c> first</c><00:00:36.200><c> notebook</c><00:00:36.879><c> of</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
and cla3 which is the first notebook of
 

00:00:37.559 --> 00:00:40.110 align:start position:0%
and cla3 which is the first notebook of
the<00:00:37.680><c> available</c><00:00:38.160><c> cookbooks</c><00:00:38.800><c> here</c><00:00:39.640><c> so</c><00:00:39.879><c> let's</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
the available cookbooks here so let's
 

00:00:40.120 --> 00:00:48.750 align:start position:0%
the available cookbooks here so let's
get<00:00:40.280><c> started</c><00:00:40.640><c> with</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
 
 

00:00:48.760 --> 00:00:51.350 align:start position:0%
 
it<00:00:49.760><c> so</c><00:00:49.920><c> in</c><00:00:50.039><c> this</c><00:00:50.160><c> notebook</c><00:00:50.520><c> we'll</c><00:00:50.680><c> look</c><00:00:50.840><c> into</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
it so in this notebook we'll look into
 

00:00:51.360 --> 00:00:53.189 align:start position:0%
it so in this notebook we'll look into
building<00:00:51.640><c> R</c><00:00:51.960><c> Pip</c><00:00:52.120><c> planine</c><00:00:52.280><c> with</c><00:00:52.399><c> Lama</c><00:00:52.680><c> index</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
building R Pip planine with Lama index
 

00:00:53.199 --> 00:00:55.510 align:start position:0%
building R Pip planine with Lama index
so<00:00:53.399><c> here</c><00:00:53.520><c> are</c><00:00:53.680><c> the</c><00:00:53.800><c> steps</c><00:00:54.280><c> that</c><00:00:54.399><c> are</c><00:00:55.000><c> involved</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
so here are the steps that are involved
 

00:00:55.520 --> 00:00:58.150 align:start position:0%
so here are the steps that are involved
to<00:00:55.680><c> build</c><00:00:55.879><c> a</c><00:00:56.079><c> basic</c><00:00:56.399><c> rack</c><00:00:56.680><c> pipeline</c><00:00:57.480><c> we'll</c><00:00:57.719><c> set</c>

00:00:58.150 --> 00:00:58.160 align:start position:0%
to build a basic rack pipeline we'll set
 

00:00:58.160 --> 00:01:02.270 align:start position:0%
to build a basic rack pipeline we'll set
llm<00:00:58.879><c> and</c><00:00:59.359><c> uh</c><00:00:59.920><c> embedding</c><00:01:00.359><c> model</c><00:01:01.280><c> download</c><00:01:01.760><c> data</c>

00:01:02.270 --> 00:01:02.280 align:start position:0%
llm and uh embedding model download data
 

00:01:02.280 --> 00:01:04.789 align:start position:0%
llm and uh embedding model download data
load<00:01:02.680><c> data</c><00:01:03.120><c> index</c><00:01:03.519><c> the</c><00:01:03.719><c> data</c><00:01:04.119><c> and</c><00:01:04.239><c> then</c><00:01:04.559><c> create</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
load data index the data and then create
 

00:01:04.799 --> 00:01:08.030 align:start position:0%
load data index the data and then create
a<00:01:04.960><c> query</c><00:01:05.360><c> engine</c><00:01:06.360><c> um</c><00:01:06.680><c> that</c><00:01:06.799><c> will</c><00:01:07.159><c> bind</c><00:01:07.520><c> the</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
a query engine um that will bind the
 

00:01:08.040 --> 00:01:10.270 align:start position:0%
a query engine um that will bind the
response<00:01:08.439><c> senses</c><00:01:08.759><c> and</c><00:01:09.040><c> tral</c><00:01:09.400><c> process</c><00:01:10.119><c> and</c>

00:01:10.270 --> 00:01:10.280 align:start position:0%
response senses and tral process and
 

00:01:10.280 --> 00:01:12.870 align:start position:0%
response senses and tral process and
then<00:01:10.400><c> start</c><00:01:10.960><c> querying</c><00:01:11.960><c> so</c><00:01:12.280><c> we</c><00:01:12.400><c> need</c><00:01:12.600><c> certain</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
then start querying so we need certain
 

00:01:12.880 --> 00:01:16.190 align:start position:0%
then start querying so we need certain
modules<00:01:13.360><c> to</c><00:01:13.960><c> build</c><00:01:14.240><c> this</c><00:01:14.479><c> pipeline</c><00:01:15.280><c> uh</c><00:01:15.479><c> the</c><00:01:15.680><c> B</c>

00:01:16.190 --> 00:01:16.200 align:start position:0%
modules to build this pipeline uh the B
 

00:01:16.200 --> 00:01:18.710 align:start position:0%
modules to build this pipeline uh the B
the<00:01:16.320><c> basic</c><00:01:16.600><c> Rama</c><00:01:16.960><c> index</c><00:01:17.520><c> subtractions</c><00:01:18.520><c> and</c>

00:01:18.710 --> 00:01:18.720 align:start position:0%
the basic Rama index subtractions and
 

00:01:18.720 --> 00:01:22.710 align:start position:0%
the basic Rama index subtractions and
then<00:01:19.200><c> uh</c><00:01:19.840><c> anthropic</c><00:01:20.479><c> llm</c><00:01:21.479><c> and</c><00:01:21.680><c> then</c><00:01:22.119><c> uhing</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
then uh anthropic llm and then uhing
 

00:01:22.720 --> 00:01:24.990 align:start position:0%
then uh anthropic llm and then uhing
face<00:01:22.920><c> embeding</c><00:01:23.880><c> model</c><00:01:24.280><c> since</c><00:01:24.560><c> anthropic</c>

00:01:24.990 --> 00:01:25.000 align:start position:0%
face embeding model since anthropic
 

00:01:25.000 --> 00:01:26.789 align:start position:0%
face embeding model since anthropic
doesn't<00:01:25.280><c> provide</c><00:01:25.520><c> any</c><00:01:25.640><c> embedding</c><00:01:26.040><c> models</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
doesn't provide any embedding models
 

00:01:26.799 --> 00:01:28.670 align:start position:0%
doesn't provide any embedding models
we'll<00:01:27.000><c> use</c><00:01:27.240><c> huging</c><00:01:27.600><c> pH</c><00:01:27.759><c> embedding</c><00:01:28.159><c> model</c><00:01:28.439><c> Open</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
we'll use huging pH embedding model Open
 

00:01:28.680 --> 00:01:30.990 align:start position:0%
we'll use huging pH embedding model Open
Source<00:01:28.920><c> One</c><00:01:30.000><c> and</c><00:01:30.159><c> to</c><00:01:30.280><c> run</c><00:01:30.479><c> this</c><00:01:30.600><c> notebook</c><00:01:30.920><c> you</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
Source One and to run this notebook you
 

00:01:31.000 --> 00:01:33.950 align:start position:0%
Source One and to run this notebook you
need<00:01:31.200><c> anthropic</c><00:01:31.680><c> AP</c><00:01:32.040><c> key</c><00:01:32.399><c> so</c><00:01:33.119><c> uh</c><00:01:33.560><c> before</c><00:01:33.799><c> you</c>

00:01:33.950 --> 00:01:33.960 align:start position:0%
need anthropic AP key so uh before you
 

00:01:33.960 --> 00:01:36.389 align:start position:0%
need anthropic AP key so uh before you
run<00:01:34.200><c> this</c><00:01:34.360><c> notebook</c><00:01:35.000><c> get</c><00:01:35.159><c> your</c><00:01:35.320><c> anthropic</c><00:01:35.759><c> AP</c>

00:01:36.389 --> 00:01:36.399 align:start position:0%
run this notebook get your anthropic AP
 

00:01:36.399 --> 00:01:41.230 align:start position:0%
run this notebook get your anthropic AP
key<00:01:37.399><c> and</c><00:01:37.600><c> then</c><00:01:38.280><c> uh</c><00:01:38.399><c> we'll</c><00:01:38.640><c> set</c><00:01:39.000><c> the</c><00:01:39.759><c> llm</c><00:01:40.640><c> here</c>

00:01:41.230 --> 00:01:41.240 align:start position:0%
key and then uh we'll set the llm here
 

00:01:41.240 --> 00:01:45.230 align:start position:0%
key and then uh we'll set the llm here
we<00:01:41.360><c> are</c><00:01:41.520><c> using</c><00:01:42.439><c> obos</c><00:01:43.439><c> uh</c><00:01:43.600><c> model</c><00:01:44.079><c> of</c><00:01:44.280><c> cloud</c><00:01:44.680><c> 3</c>

00:01:45.230 --> 00:01:45.240 align:start position:0%
we are using obos uh model of cloud 3
 

00:01:45.240 --> 00:01:49.190 align:start position:0%
we are using obos uh model of cloud 3
and<00:01:45.560><c> then</c><00:01:46.560><c> bgb</c><00:01:47.119><c> English</c><00:01:47.920><c> v1.5</c><00:01:48.920><c> as</c><00:01:49.040><c> an</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
and then bgb English v1.5 as an
 

00:01:49.200 --> 00:01:52.590 align:start position:0%
and then bgb English v1.5 as an
embedding<00:01:49.560><c> model</c><00:01:49.840><c> from</c><00:01:50.000><c> the</c><00:01:50.119><c> hugging</c><00:01:50.920><c> pH</c><00:01:51.920><c> um</c>

00:01:52.590 --> 00:01:52.600 align:start position:0%
embedding model from the hugging pH um
 

00:01:52.600 --> 00:01:55.510 align:start position:0%
embedding model from the hugging pH um
and<00:01:52.759><c> then</c><00:01:53.000><c> we'll</c><00:01:53.280><c> set</c><00:01:54.000><c> uh</c><00:01:54.280><c> the</c><00:01:54.719><c> these</c><00:01:54.880><c> are</c><00:01:55.119><c> the</c>

00:01:55.510 --> 00:01:55.520 align:start position:0%
and then we'll set uh the these are the
 

00:01:55.520 --> 00:01:58.749 align:start position:0%
and then we'll set uh the these are the
llms<00:01:56.119><c> and</c><00:01:56.520><c> uh</c><00:01:57.119><c> embedding</c><00:01:57.560><c> models</c><00:01:58.439><c> that</c><00:01:58.560><c> we'll</c>

00:01:58.749 --> 00:01:58.759 align:start position:0%
llms and uh embedding models that we'll
 

00:01:58.759 --> 00:02:02.069 align:start position:0%
llms and uh embedding models that we'll
be<00:01:58.920><c> using</c><00:01:59.200><c> for</c><00:01:59.479><c> this</c><00:02:00.280><c> entire</c><00:02:01.159><c> Pipeline</c><00:02:01.880><c> and</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
be using for this entire Pipeline and
 

00:02:02.079 --> 00:02:06.069 align:start position:0%
be using for this entire Pipeline and
then<00:02:02.240><c> even</c><00:02:02.439><c> the</c><00:02:02.520><c> chunk</c><00:02:02.799><c> CH</c><00:02:03.439><c> to</c><00:02:03.640><c> be</c><00:02:03.960><c> 512</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
then even the chunk CH to be 512
 

00:02:06.079 --> 00:02:09.510 align:start position:0%
then even the chunk CH to be 512
okay<00:02:07.079><c> and</c><00:02:07.399><c> once</c><00:02:07.560><c> you</c><00:02:07.799><c> set</c><00:02:08.319><c> all</c><00:02:08.520><c> these</c><00:02:08.759><c> things</c>

00:02:09.510 --> 00:02:09.520 align:start position:0%
okay and once you set all these things
 

00:02:09.520 --> 00:02:12.750 align:start position:0%
okay and once you set all these things
you<00:02:09.759><c> need</c><00:02:09.959><c> to</c><00:02:10.119><c> download</c><00:02:10.520><c> the</c><00:02:10.879><c> data</c><00:02:11.879><c> and</c><00:02:12.239><c> then</c>

00:02:12.750 --> 00:02:12.760 align:start position:0%
you need to download the data and then
 

00:02:12.760 --> 00:02:15.509 align:start position:0%
you need to download the data and then
uh<00:02:13.520><c> load</c><00:02:13.840><c> the</c><00:02:14.040><c> data</c><00:02:14.560><c> using</c><00:02:15.040><c> uh</c><00:02:15.160><c> simple</c>

00:02:15.509 --> 00:02:15.519 align:start position:0%
uh load the data using uh simple
 

00:02:15.519 --> 00:02:19.150 align:start position:0%
uh load the data using uh simple
director<00:02:15.920><c> reader</c><00:02:16.800><c> uh</c><00:02:17.800><c> and</c><00:02:18.400><c> start</c><00:02:18.760><c> indexing</c>

00:02:19.150 --> 00:02:19.160 align:start position:0%
director reader uh and start indexing
 

00:02:19.160 --> 00:02:21.110 align:start position:0%
director reader uh and start indexing
the<00:02:19.360><c> data</c><00:02:19.680><c> so</c><00:02:19.879><c> Vector</c><00:02:20.160><c> store</c><00:02:20.400><c> index</c><00:02:20.680><c> from</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
the data so Vector store index from
 

00:02:21.120 --> 00:02:23.710 align:start position:0%
the data so Vector store index from
documents<00:02:22.120><c> will</c><00:02:22.599><c> uh</c><00:02:22.720><c> create</c><00:02:22.959><c> an</c><00:02:23.160><c> index</c><00:02:23.599><c> which</c>

00:02:23.710 --> 00:02:23.720 align:start position:0%
documents will uh create an index which
 

00:02:23.720 --> 00:02:26.790 align:start position:0%
documents will uh create an index which
is<00:02:23.879><c> an</c><00:02:24.000><c> in</c><00:02:24.160><c> memory</c><00:02:24.879><c> one</c><00:02:25.480><c> with</c><00:02:25.879><c> whatever</c><00:02:26.480><c> chunk</c>

00:02:26.790 --> 00:02:26.800 align:start position:0%
is an in memory one with whatever chunk
 

00:02:26.800 --> 00:02:28.949 align:start position:0%
is an in memory one with whatever chunk
size<00:02:27.040><c> we</c><00:02:27.160><c> have</c><00:02:27.319><c> mentioned</c><00:02:27.879><c> and</c><00:02:28.280><c> the</c><00:02:28.599><c> IT</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
size we have mentioned and the IT
 

00:02:28.959 --> 00:02:31.990 align:start position:0%
size we have mentioned and the IT
internal<00:02:29.400><c> uses</c><00:02:29.879><c> embedding</c><00:02:30.280><c> model</c><00:02:31.040><c> to</c><00:02:31.879><c> uh</c>

00:02:31.990 --> 00:02:32.000 align:start position:0%
internal uses embedding model to uh
 

00:02:32.000 --> 00:02:33.390 align:start position:0%
internal uses embedding model to uh
generate<00:02:32.360><c> the</c><00:02:32.480><c> embeddings</c><00:02:32.879><c> for</c><00:02:33.040><c> each</c><00:02:33.200><c> of</c><00:02:33.319><c> the</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
generate the embeddings for each of the
 

00:02:33.400 --> 00:02:36.190 align:start position:0%
generate the embeddings for each of the
nodes<00:02:34.160><c> created</c><00:02:35.160><c> and</c><00:02:35.319><c> then</c><00:02:35.519><c> create</c><00:02:35.800><c> a</c><00:02:35.920><c> query</c>

00:02:36.190 --> 00:02:36.200 align:start position:0%
nodes created and then create a query
 

00:02:36.200 --> 00:02:38.550 align:start position:0%
nodes created and then create a query
engine<00:02:36.920><c> U</c><00:02:37.080><c> with</c><00:02:37.319><c> similarity</c><00:02:37.840><c> top</c><00:02:38.080><c> k</c><00:02:38.200><c> equal</c><00:02:38.440><c> to</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
engine U with similarity top k equal to
 

00:02:38.560 --> 00:02:42.070 align:start position:0%
engine U with similarity top k equal to
3<00:02:39.400><c> and</c><00:02:39.640><c> then</c><00:02:40.120><c> start</c><00:02:40.480><c> querying</c><00:02:41.480><c> so</c><00:02:41.800><c> query</c>

00:02:42.070 --> 00:02:42.080 align:start position:0%
3 and then start querying so query
 

00:02:42.080 --> 00:02:44.790 align:start position:0%
3 and then start querying so query
engine.<00:02:42.560><c> query</c><00:02:43.519><c> what</c><00:02:43.720><c> did</c><00:02:43.879><c> other</c><00:02:44.200><c> two</c><00:02:44.400><c> growing</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
engine. query what did other two growing
 

00:02:44.800 --> 00:02:48.030 align:start position:0%
engine. query what did other two growing
up<00:02:45.080><c> as</c><00:02:45.159><c> a</c><00:02:45.319><c> sample</c><00:02:46.239><c> query</c><00:02:47.040><c> uh</c><00:02:47.200><c> it</c><00:02:47.440><c> gave</c><00:02:47.760><c> a</c>

00:02:48.030 --> 00:02:48.040 align:start position:0%
up as a sample query uh it gave a
 

00:02:48.040 --> 00:02:49.149 align:start position:0%
up as a sample query uh it gave a
response

00:02:49.149 --> 00:02:49.159 align:start position:0%
response
 

00:02:49.159 --> 00:02:52.190 align:start position:0%
response
accordingly<00:02:50.159><c> so</c><00:02:50.640><c> that's</c><00:02:50.879><c> how</c><00:02:51.159><c> you</c><00:02:51.319><c> can</c><00:02:51.920><c> um</c>

00:02:52.190 --> 00:02:52.200 align:start position:0%
accordingly so that's how you can um
 

00:02:52.200 --> 00:02:54.630 align:start position:0%
accordingly so that's how you can um
build<00:02:52.519><c> a</c><00:02:52.680><c> simple</c><00:02:53.000><c> rag</c><00:02:53.360><c> application</c><00:02:54.120><c> or</c><00:02:54.360><c> R</c>

00:02:54.630 --> 00:02:54.640 align:start position:0%
build a simple rag application or R
 

00:02:54.640 --> 00:02:56.630 align:start position:0%
build a simple rag application or R
Pipeline<00:02:55.159><c> with</c><00:02:55.319><c> Lama</c><00:02:55.599><c> induction</c><00:02:55.959><c> cloud3</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
Pipeline with Lama induction cloud3
 

00:02:56.640 --> 00:02:59.879 align:start position:0%
Pipeline with Lama induction cloud3
model<00:02:57.640><c> um</c>

