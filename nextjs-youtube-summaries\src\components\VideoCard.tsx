import Link from 'next/link'
import Image from 'next/image'
import { Play, Clock, Calendar } from 'lucide-react'
import { VideoSummary } from '@/types'
import { formatDuration, formatDate, truncateText } from '@/lib/utils'
import React, { useState } from 'react';

interface VideoCardProps {
  video: VideoSummary;
  showTopic?: boolean;
}

export function VideoCard({ video, showTopic = false }: VideoCardProps) {
  const topicPath = video.topic_category || 'general';
  const [showEmbed, setShowEmbed] = useState(false);

  let displayContent = 'Keywords not available yet...';
  // Add null check for video.llm_response before accessing keywords
  if (video.llm_response && video.llm_response.keywords) {
    if (Array.isArray(video.llm_response.keywords) && video.llm_response.keywords.length > 0) {
      displayContent = video.llm_response.keywords
        .filter(kw => typeof kw === 'string' && kw.trim() !== '')
        .join(', ');
      if (displayContent.length === 0) {
        displayContent = 'Keywords not available yet...';
      }
    } else if (typeof video.llm_response.keywords === 'string' && video.llm_response.keywords.trim() !== '') {
      displayContent = video.llm_response.keywords;
    }
  } else if (video.llm_response && typeof video.llm_response.summary === 'string' && video.llm_response.summary.trim() !== '') {
    // Fallback to summary if keywords are not available, as a simple string
    // displayContent = truncateText(video.llm_response.summary, 100); // Or some other formatting
  } // Further fallback logic can be added if needed


  const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;

  const handleThumbnailError = () => {
    if (youtubeEmbedUrl) {
      setShowEmbed(true);
    }
  };

  const initialShowEmbed = !video.thumbnail_url && !!youtubeEmbedUrl;

  return (
    <div className="bg-white dark:bg-gray-800 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col h-full">
      <Link href={`/video/${topicPath}/${video.video_id}`} className="block">
        <div className="relative aspect-video overflow-hidden">
          {(showEmbed || initialShowEmbed) && youtubeEmbedUrl ? (
            <iframe
              width="100%"
              height="100%"
              src={youtubeEmbedUrl}
              title={video.title ?? "YouTube video player"}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
              className="w-full h-full"
            ></iframe>
          ) : video.thumbnail_url ? (
            <Image 
              src={video.thumbnail_url} 
              alt={video.title || 'Video thumbnail'} 
              className="w-full h-full object-cover"
              width={640}
              height={360}
              unoptimized={video.thumbnail_url.includes('youtube.com') || video.thumbnail_url.includes('ytimg.com')}
              onError={handleThumbnailError}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <Play className="w-12 h-12 text-gray-400 dark:text-gray-500" />
              <span className="ml-2 text-gray-500 dark:text-gray-400">Thumbnail not available</span>
            </div>
          )}
          {!(showEmbed || initialShowEmbed) && video.duration && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {formatDuration(video.duration)}
            </div>
          )}
          {!(showEmbed || initialShowEmbed) && (
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
            </div>
          )}
        </div>
      </Link>
      <div className="p-4 flex flex-col flex-grow">
        <Link href={`/video/${topicPath}/${video.video_id}`} className="block mb-1">
          <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2" title={video.title || 'Untitled Video'}>
            {video.title || 'Untitled Video'}
          </h3>
        </Link>
        <p className="text-gray-600 dark:text-gray-400 text-sm mt-1 line-clamp-1" title={video.channel_name || 'Unknown Channel'}>
          {video.channel_name || 'Unknown Channel'}
        </p>
        
        <div className="flex items-center justify-between mt-3 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            {formatDate(video.published_at || new Date().toISOString())}
          </div>
          {showTopic && video.topic_category && (
            <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full line-clamp-1" title={video.topic_category}>
              {video.topic_category}
            </span>
          )}
        </div>
        <div className="mt-3 flex-grow">
          <p className="text-gray-700 dark:text-gray-300 text-sm line-clamp-3" title={displayContent}>
            {truncateText(displayContent, 150)}
          </p>
        </div>
        
        <div className="mt-auto pt-3">
          <Link 
            href={`/video/${topicPath}/${video.video_id}`}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            Read full summary →
          </Link>
        </div>
      </div>
    </div>
  )
}
