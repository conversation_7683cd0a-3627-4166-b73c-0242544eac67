WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.390 align:start position:0%
 
hey<00:00:00.240><c> and</c><00:00:00.320><c> welcome</c><00:00:00.560><c> back</c><00:00:00.680><c> to</c><00:00:00.799><c> another</c><00:00:01.079><c> video</c>

00:00:01.390 --> 00:00:01.400 align:start position:0%
hey and welcome back to another video
 

00:00:01.400 --> 00:00:03.750 align:start position:0%
hey and welcome back to another video
and<00:00:01.480><c> it's</c><00:00:01.719><c> day</c><00:00:01.920><c> 24</c><00:00:02.399><c> of</c><00:00:02.520><c> the</c><00:00:02.600><c> 31-day</c><00:00:03.240><c> challenge</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
and it's day 24 of the 31-day challenge
 

00:00:03.760 --> 00:00:05.470 align:start position:0%
and it's day 24 of the 31-day challenge
and<00:00:03.959><c> today</c><00:00:04.200><c> we're</c><00:00:04.359><c> going</c><00:00:04.480><c> to</c><00:00:04.560><c> be</c><00:00:04.720><c> adding</c><00:00:05.000><c> on</c><00:00:05.240><c> to</c>

00:00:05.470 --> 00:00:05.480 align:start position:0%
and today we're going to be adding on to
 

00:00:05.480 --> 00:00:07.070 align:start position:0%
and today we're going to be adding on to
the<00:00:05.720><c> YouTube</c><00:00:06.200><c> service</c><00:00:06.520><c> that</c><00:00:06.640><c> we</c><00:00:06.720><c> created</c><00:00:07.000><c> a</c>

00:00:07.070 --> 00:00:07.080 align:start position:0%
the YouTube service that we created a
 

00:00:07.080 --> 00:00:08.910 align:start position:0%
the YouTube service that we created a
couple<00:00:07.319><c> days</c><00:00:07.520><c> ago</c><00:00:07.799><c> where</c><00:00:08.000><c> we</c><00:00:08.200><c> transcribes</c>

00:00:08.910 --> 00:00:08.920 align:start position:0%
couple days ago where we transcribes
 

00:00:08.920 --> 00:00:11.070 align:start position:0%
couple days ago where we transcribes
videos<00:00:09.480><c> just</c><00:00:09.639><c> from</c><00:00:09.800><c> a</c><00:00:09.960><c> video</c><00:00:10.240><c> URL</c><00:00:10.880><c> now</c><00:00:10.960><c> we're</c>

00:00:11.070 --> 00:00:11.080 align:start position:0%
videos just from a video URL now we're
 

00:00:11.080 --> 00:00:13.589 align:start position:0%
videos just from a video URL now we're
going<00:00:11.160><c> to</c><00:00:11.240><c> have</c><00:00:11.400><c> autogen</c><00:00:12.320><c> summarize</c><00:00:13.040><c> that</c><00:00:13.440><c> and</c>

00:00:13.589 --> 00:00:13.599 align:start position:0%
going to have autogen summarize that and
 

00:00:13.599 --> 00:00:15.669 align:start position:0%
going to have autogen summarize that and
then<00:00:13.759><c> send</c><00:00:14.040><c> that</c><00:00:14.240><c> into</c><00:00:14.519><c> a</c><00:00:14.719><c> format</c><00:00:15.160><c> ready</c><00:00:15.440><c> for</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
then send that into a format ready for
 

00:00:15.679 --> 00:00:17.429 align:start position:0%
then send that into a format ready for
email<00:00:16.119><c> we'll</c><00:00:16.279><c> simply</c><00:00:16.520><c> tell</c><00:00:16.800><c> to</c><00:00:16.920><c> send</c><00:00:17.119><c> to</c>

00:00:17.429 --> 00:00:17.439 align:start position:0%
email we'll simply tell to send to
 

00:00:17.439 --> 00:00:18.990 align:start position:0%
email we'll simply tell to send to
somebody's<00:00:17.920><c> email</c><00:00:18.400><c> and</c><00:00:18.480><c> it's</c><00:00:18.600><c> going</c><00:00:18.680><c> to</c><00:00:18.800><c> do</c>

00:00:18.990 --> 00:00:19.000 align:start position:0%
somebody's email and it's going to do
 

00:00:19.000 --> 00:00:20.269 align:start position:0%
somebody's email and it's going to do
that<00:00:19.119><c> for</c><00:00:19.359><c> us</c><00:00:19.640><c> basically</c><00:00:19.920><c> going</c><00:00:20.039><c> to</c><00:00:20.080><c> have</c><00:00:20.160><c> a</c>

00:00:20.269 --> 00:00:20.279 align:start position:0%
that for us basically going to have a
 

00:00:20.279 --> 00:00:22.070 align:start position:0%
that for us basically going to have a
prompt<00:00:20.519><c> that</c><00:00:20.640><c> says</c><00:00:20.880><c> can</c><00:00:21.039><c> you</c><00:00:21.199><c> send</c><00:00:21.439><c> an</c><00:00:21.640><c> email</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
prompt that says can you send an email
 

00:00:22.080 --> 00:00:25.310 align:start position:0%
prompt that says can you send an email
to<00:00:22.800><c> my</c><00:00:23.039><c> email</c><00:00:23.880><c> with</c><00:00:24.320><c> the</c><00:00:24.480><c> post</c><00:00:24.720><c> created</c><00:00:25.039><c> above</c>

00:00:25.310 --> 00:00:25.320 align:start position:0%
to my email with the post created above
 

00:00:25.320 --> 00:00:27.710 align:start position:0%
to my email with the post created above
which<00:00:25.400><c> will</c><00:00:25.519><c> be</c><00:00:25.720><c> formatted</c><00:00:26.359><c> for</c><00:00:26.880><c> email</c><00:00:27.560><c> and</c>

00:00:27.710 --> 00:00:27.720 align:start position:0%
which will be formatted for email and
 

00:00:27.720 --> 00:00:30.150 align:start position:0%
which will be formatted for email and
then<00:00:27.960><c> it</c><00:00:28.039><c> will</c><00:00:28.199><c> look</c><00:00:28.480><c> something</c><00:00:28.920><c> like</c><00:00:29.240><c> this</c><00:00:30.000><c> so</c>

00:00:30.150 --> 00:00:30.160 align:start position:0%
then it will look something like this so
 

00:00:30.160 --> 00:00:31.230 align:start position:0%
then it will look something like this so
let's<00:00:30.320><c> get</c><00:00:30.439><c> started</c><00:00:30.800><c> all</c><00:00:30.880><c> right</c><00:00:31.000><c> the</c><00:00:31.080><c> first</c>

00:00:31.230 --> 00:00:31.240 align:start position:0%
let's get started all right the first
 

00:00:31.240 --> 00:00:32.389 align:start position:0%
let's get started all right the first
thing<00:00:31.359><c> you'll</c><00:00:31.519><c> need</c><00:00:31.840><c> is</c><00:00:31.960><c> to</c><00:00:32.119><c> have</c><00:00:32.239><c> a</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
thing you'll need is to have a
 

00:00:32.399 --> 00:00:34.630 align:start position:0%
thing you'll need is to have a
credentials.<00:00:33.160><c> Json</c><00:00:33.600><c> file</c><00:00:34.360><c> which</c><00:00:34.480><c> is</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
credentials. Json file which is
 

00:00:34.640 --> 00:00:37.190 align:start position:0%
credentials. Json file which is
basically<00:00:35.040><c> set</c><00:00:35.280><c> up</c><00:00:35.520><c> from</c><00:00:35.800><c> the</c><00:00:36.000><c> Gmail</c><00:00:36.520><c> service</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
basically set up from the Gmail service
 

00:00:37.200 --> 00:00:38.630 align:start position:0%
basically set up from the Gmail service
so<00:00:37.320><c> if</c><00:00:37.440><c> you</c><00:00:37.559><c> go</c><00:00:37.680><c> to</c><00:00:37.960><c> this</c><00:00:38.120><c> link</c><00:00:38.360><c> which</c><00:00:38.480><c> I'll</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
so if you go to this link which I'll
 

00:00:38.640 --> 00:00:40.590 align:start position:0%
so if you go to this link which I'll
have<00:00:38.719><c> in</c><00:00:38.800><c> the</c><00:00:38.920><c> description</c><00:00:39.399><c> you</c><00:00:39.559><c> just</c><00:00:39.760><c> go</c><00:00:40.000><c> here</c>

00:00:40.590 --> 00:00:40.600 align:start position:0%
have in the description you just go here
 

00:00:40.600 --> 00:00:42.750 align:start position:0%
have in the description you just go here
and<00:00:40.840><c> you'll</c><00:00:41.239><c> follow</c><00:00:41.800><c> this</c><00:00:42.000><c> it</c><00:00:42.120><c> took</c><00:00:42.280><c> me</c><00:00:42.520><c> only</c>

00:00:42.750 --> 00:00:42.760 align:start position:0%
and you'll follow this it took me only
 

00:00:42.760 --> 00:00:45.069 align:start position:0%
and you'll follow this it took me only
about<00:00:43.000><c> 5</c><00:00:43.239><c> minutes</c><00:00:44.000><c> once</c><00:00:44.160><c> I</c><00:00:44.360><c> had</c><00:00:44.520><c> it</c><00:00:44.800><c> it</c><00:00:44.879><c> just</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
about 5 minutes once I had it it just
 

00:00:45.079 --> 00:00:47.310 align:start position:0%
about 5 minutes once I had it it just
downloads<00:00:45.480><c> a</c><00:00:45.600><c> credentials.</c><00:00:46.320><c> Json</c><00:00:46.800><c> for</c><00:00:47.039><c> you</c>

00:00:47.310 --> 00:00:47.320 align:start position:0%
downloads a credentials. Json for you
 

00:00:47.320 --> 00:00:48.189 align:start position:0%
downloads a credentials. Json for you
which<00:00:47.480><c> then</c><00:00:47.600><c> you</c><00:00:47.680><c> just</c><00:00:47.840><c> put</c><00:00:47.960><c> in</c><00:00:48.079><c> your</c>

00:00:48.189 --> 00:00:48.199 align:start position:0%
which then you just put in your
 

00:00:48.199 --> 00:00:49.270 align:start position:0%
which then you just put in your
directory<00:00:48.680><c> here</c><00:00:48.960><c> I'll</c><00:00:49.079><c> have</c><00:00:49.199><c> the</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
directory here I'll have the
 

00:00:49.280 --> 00:00:51.430 align:start position:0%
directory here I'll have the
requirements.txt<00:00:50.039><c> for</c><00:00:50.480><c> you</c><00:00:50.600><c> so</c><00:00:50.800><c> you</c><00:00:50.879><c> can</c><00:00:51.120><c> have</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
requirements.txt for you so you can have
 

00:00:51.440 --> 00:00:53.950 align:start position:0%
requirements.txt for you so you can have
all<00:00:51.600><c> of</c><00:00:51.800><c> these</c><00:00:52.079><c> Imports</c><00:00:52.840><c> we</c><00:00:53.000><c> just</c><00:00:53.120><c> set</c><00:00:53.320><c> up</c><00:00:53.559><c> our</c>

00:00:53.950 --> 00:00:53.960 align:start position:0%
all of these Imports we just set up our
 

00:00:53.960 --> 00:00:55.630 align:start position:0%
all of these Imports we just set up our
environment<00:00:54.480><c> properties</c><00:00:55.160><c> so</c><00:00:55.280><c> you</c><00:00:55.359><c> need</c><00:00:55.520><c> to</c>

00:00:55.630 --> 00:00:55.640 align:start position:0%
environment properties so you need to
 

00:00:55.640 --> 00:00:57.349 align:start position:0%
environment properties so you need to
get<00:00:55.760><c> your</c><00:00:55.920><c> credentials</c><00:00:56.640><c> first</c><00:00:56.960><c> right</c><00:00:57.079><c> so</c><00:00:57.239><c> this</c>

00:00:57.349 --> 00:00:57.359 align:start position:0%
get your credentials first right so this
 

00:00:57.359 --> 00:00:58.830 align:start position:0%
get your credentials first right so this
piece<00:00:57.520><c> of</c><00:00:57.680><c> code</c><00:00:58.079><c> basically</c><00:00:58.440><c> takes</c><00:00:58.680><c> your</c>

00:00:58.830 --> 00:00:58.840 align:start position:0%
piece of code basically takes your
 

00:00:58.840 --> 00:01:01.189 align:start position:0%
piece of code basically takes your
credentials.<00:00:59.600><c> J</c><00:01:00.079><c> on</c><00:01:00.519><c> and</c><00:01:00.640><c> it'll</c><00:01:00.840><c> create</c><00:01:01.039><c> a</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
credentials. J on and it'll create a
 

00:01:01.199 --> 00:01:03.270 align:start position:0%
credentials. J on and it'll create a
token<00:01:01.559><c> so</c><00:01:01.760><c> that</c><00:01:02.000><c> locally</c><00:01:02.519><c> it</c><00:01:02.640><c> will</c><00:01:02.840><c> allow</c><00:01:03.079><c> you</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
token so that locally it will allow you
 

00:01:03.280 --> 00:01:05.509 align:start position:0%
token so that locally it will allow you
to<00:01:03.480><c> send</c><00:01:03.840><c> an</c><00:01:04.080><c> email</c><00:01:04.559><c> so</c><00:01:04.720><c> we're</c><00:01:04.920><c> going</c><00:01:05.080><c> to</c><00:01:05.280><c> build</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
to send an email so we're going to build
 

00:01:05.519 --> 00:01:07.429 align:start position:0%
to send an email so we're going to build
an<00:01:05.680><c> API</c><00:01:06.119><c> resource</c><00:01:06.720><c> which</c><00:01:06.840><c> is</c><00:01:06.960><c> from</c><00:01:07.119><c> a</c><00:01:07.200><c> link</c>

00:01:07.429 --> 00:01:07.439 align:start position:0%
an API resource which is from a link
 

00:01:07.439 --> 00:01:09.510 align:start position:0%
an API resource which is from a link
chain<00:01:07.680><c> utility</c><00:01:08.439><c> and</c><00:01:08.520><c> then</c><00:01:08.680><c> the</c><00:01:08.799><c> toolkit</c><00:01:09.280><c> comes</c>

00:01:09.510 --> 00:01:09.520 align:start position:0%
chain utility and then the toolkit comes
 

00:01:09.520 --> 00:01:11.630 align:start position:0%
chain utility and then the toolkit comes
from<00:01:09.640><c> the</c><00:01:09.759><c> Gmail</c><00:01:10.159><c> toolkit</c><00:01:10.799><c> which</c><00:01:11.040><c> is</c><00:01:11.360><c> from</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
from the Gmail toolkit which is from
 

00:01:11.640 --> 00:01:12.910 align:start position:0%
from the Gmail toolkit which is from
linkchain<00:01:11.920><c> as</c><00:01:12.240><c> well</c><00:01:12.479><c> and</c><00:01:12.560><c> that's</c><00:01:12.720><c> kind</c><00:01:12.799><c> of</c>

00:01:12.910 --> 00:01:12.920 align:start position:0%
linkchain as well and that's kind of
 

00:01:12.920 --> 00:01:14.310 align:start position:0%
linkchain as well and that's kind of
just<00:01:13.040><c> the</c><00:01:13.240><c> setup</c><00:01:13.600><c> that</c><00:01:13.720><c> we</c><00:01:13.840><c> need</c><00:01:14.119><c> all</c><00:01:14.240><c> right</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
just the setup that we need all right
 

00:01:14.320 --> 00:01:15.830 align:start position:0%
just the setup that we need all right
I'm<00:01:14.400><c> going</c><00:01:14.520><c> to</c><00:01:14.600><c> use</c><00:01:14.720><c> this</c><00:01:14.880><c> video</c><00:01:15.159><c> from</c><00:01:15.400><c> IBM</c><00:01:15.720><c> as</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
I'm going to use this video from IBM as
 

00:01:15.840 --> 00:01:17.510 align:start position:0%
I'm going to use this video from IBM as
well<00:01:16.159><c> that</c><00:01:16.320><c> talks</c><00:01:16.560><c> about</c><00:01:16.759><c> linkchain</c><00:01:17.080><c> so</c><00:01:17.439><c> I'm</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
well that talks about linkchain so I'm
 

00:01:17.520 --> 00:01:19.149 align:start position:0%
well that talks about linkchain so I'm
going<00:01:17.600><c> to</c><00:01:17.840><c> put</c><00:01:17.960><c> that</c><00:01:18.159><c> video</c><00:01:18.439><c> in</c><00:01:18.600><c> here</c><00:01:18.880><c> cuz</c><00:01:19.080><c> we</c>

00:01:19.149 --> 00:01:19.159 align:start position:0%
going to put that video in here cuz we
 

00:01:19.159 --> 00:01:21.109 align:start position:0%
going to put that video in here cuz we
have<00:01:19.280><c> a</c><00:01:19.400><c> function</c><00:01:19.759><c> called</c><00:01:20.119><c> get</c><00:01:20.439><c> transcription</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
have a function called get transcription
 

00:01:21.119 --> 00:01:23.550 align:start position:0%
have a function called get transcription
from<00:01:21.360><c> YouTube</c><00:01:21.759><c> video</c><00:01:22.240><c> so</c><00:01:22.400><c> if</c><00:01:22.479><c> we</c><00:01:22.640><c> go</c><00:01:22.799><c> into</c><00:01:23.079><c> here</c>

00:01:23.550 --> 00:01:23.560 align:start position:0%
from YouTube video so if we go into here
 

00:01:23.560 --> 00:01:26.469 align:start position:0%
from YouTube video so if we go into here
this<00:01:23.680><c> is</c><00:01:23.960><c> in</c><00:01:24.159><c> my</c><00:01:24.439><c> functions.</c><00:01:25.079><c> py</c><00:01:25.520><c> file</c><00:01:26.240><c> and</c><00:01:26.360><c> all</c>

00:01:26.469 --> 00:01:26.479 align:start position:0%
this is in my functions. py file and all
 

00:01:26.479 --> 00:01:29.230 align:start position:0%
this is in my functions. py file and all
this<00:01:26.640><c> does</c><00:01:26.799><c> is</c><00:01:26.960><c> take</c><00:01:27.079><c> a</c><00:01:27.280><c> video</c><00:01:27.720><c> URL</c><00:01:28.720><c> and</c><00:01:28.880><c> then</c>

00:01:29.230 --> 00:01:29.240 align:start position:0%
this does is take a video URL and then
 

00:01:29.240 --> 00:01:31.109 align:start position:0%
this does is take a video URL and then
Returns<00:01:29.600><c> the</c><00:01:29.920><c> transcription</c><00:01:30.720><c> from</c><00:01:30.960><c> that</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
Returns the transcription from that
 

00:01:31.119 --> 00:01:32.950 align:start position:0%
Returns the transcription from that
video<00:01:31.479><c> so</c><00:01:31.640><c> now</c><00:01:31.759><c> we</c><00:01:31.840><c> have</c><00:01:32.000><c> the</c><00:01:32.159><c> text</c><00:01:32.560><c> from</c><00:01:32.799><c> that</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
video so now we have the text from that
 

00:01:32.960 --> 00:01:35.149 align:start position:0%
video so now we have the text from that
video<00:01:33.240><c> that</c><00:01:33.320><c> we're</c><00:01:33.479><c> going</c><00:01:33.560><c> to</c><00:01:33.799><c> use</c><00:01:34.520><c> as</c><00:01:34.759><c> context</c>

00:01:35.149 --> 00:01:35.159 align:start position:0%
video that we're going to use as context
 

00:01:35.159 --> 00:01:36.910 align:start position:0%
video that we're going to use as context
for<00:01:35.320><c> the</c><00:01:35.399><c> autogen</c><00:01:35.920><c> to</c><00:01:36.079><c> create</c><00:01:36.280><c> a</c><00:01:36.360><c> summary</c><00:01:36.720><c> of</c>

00:01:36.910 --> 00:01:36.920 align:start position:0%
for the autogen to create a summary of
 

00:01:36.920 --> 00:01:38.510 align:start position:0%
for the autogen to create a summary of
and<00:01:37.000><c> then</c><00:01:37.079><c> I</c><00:01:37.159><c> have</c><00:01:37.280><c> the</c><00:01:37.399><c> config</c><00:01:37.759><c> list</c><00:01:38.240><c> and</c><00:01:38.439><c> the</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
and then I have the config list and the
 

00:01:38.520 --> 00:01:41.069 align:start position:0%
and then I have the config list and the
llm<00:01:39.040><c> config</c><00:01:39.720><c> then</c><00:01:39.840><c> we</c><00:01:39.960><c> have</c><00:01:40.119><c> two</c><00:01:40.320><c> agents</c><00:01:40.920><c> we</c>

00:01:41.069 --> 00:01:41.079 align:start position:0%
llm config then we have two agents we
 

00:01:41.079 --> 00:01:42.870 align:start position:0%
llm config then we have two agents we
have<00:01:41.240><c> a</c><00:01:41.439><c> writer</c><00:01:41.840><c> assistant</c><00:01:42.240><c> agent</c><00:01:42.640><c> and</c><00:01:42.759><c> then</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
have a writer assistant agent and then
 

00:01:42.880 --> 00:01:45.429 align:start position:0%
have a writer assistant agent and then
the<00:01:43.040><c> user</c><00:01:43.399><c> proxy</c><00:01:43.759><c> agent</c><00:01:44.360><c> and</c><00:01:44.520><c> now</c><00:01:44.840><c> and</c><00:01:45.040><c> then</c><00:01:45.280><c> I</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
the user proxy agent and now and then I
 

00:01:45.439 --> 00:01:47.469 align:start position:0%
the user proxy agent and now and then I
have<00:01:45.680><c> one</c><00:01:46.079><c> function</c><00:01:46.479><c> call</c><00:01:46.799><c> and</c><00:01:47.000><c> this</c><00:01:47.200><c> really</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
have one function call and this really
 

00:01:47.479 --> 00:01:49.149 align:start position:0%
have one function call and this really
is<00:01:47.640><c> what</c><00:01:47.840><c> does</c><00:01:48.240><c> uh</c><00:01:48.360><c> the</c><00:01:48.439><c> brunt</c><00:01:48.719><c> of</c><00:01:48.799><c> the</c><00:01:48.920><c> work</c>

00:01:49.149 --> 00:01:49.159 align:start position:0%
is what does uh the brunt of the work
 

00:01:49.159 --> 00:01:50.670 align:start position:0%
is what does uh the brunt of the work
for<00:01:49.399><c> us</c><00:01:49.719><c> I</c><00:01:49.799><c> have</c><00:01:49.920><c> a</c><00:01:50.000><c> function</c><00:01:50.240><c> called</c><00:01:50.399><c> email</c>

00:01:50.670 --> 00:01:50.680 align:start position:0%
for us I have a function called email
 

00:01:50.680 --> 00:01:52.830 align:start position:0%
for us I have a function called email
blog<00:01:51.079><c> which</c><00:01:51.280><c> the</c><00:01:51.479><c> content</c><00:01:51.960><c> is</c><00:01:52.280><c> the</c><00:01:52.439><c> YouTube</c>

00:01:52.830 --> 00:01:52.840 align:start position:0%
blog which the content is the YouTube
 

00:01:52.840 --> 00:01:54.389 align:start position:0%
blog which the content is the YouTube
transcription<00:01:53.479><c> which</c><00:01:53.600><c> will</c><00:01:53.719><c> be</c><00:01:53.880><c> passed</c><00:01:54.119><c> into</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
transcription which will be passed into
 

00:01:54.399 --> 00:01:56.709 align:start position:0%
transcription which will be passed into
here<00:01:54.799><c> now</c><00:01:54.960><c> the</c><00:01:55.119><c> only</c><00:01:55.560><c> thing</c><00:01:55.840><c> about</c><00:01:56.159><c> this</c><00:01:56.399><c> is</c><00:01:56.520><c> I</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
here now the only thing about this is I
 

00:01:56.719 --> 00:01:58.590 align:start position:0%
here now the only thing about this is I
could<00:01:56.880><c> not</c><00:01:57.079><c> get</c><00:01:57.280><c> this</c><00:01:57.399><c> to</c><00:01:57.600><c> work</c><00:01:57.920><c> with</c><00:01:58.079><c> a</c><00:01:58.280><c> local</c>

00:01:58.590 --> 00:01:58.600 align:start position:0%
could not get this to work with a local
 

00:01:58.600 --> 00:02:01.230 align:start position:0%
could not get this to work with a local
llm<00:01:59.320><c> you</c><00:01:59.439><c> can</c><00:01:59.600><c> have</c><00:01:59.719><c> the</c><00:01:59.960><c> llm</c><00:02:00.399><c> config</c><00:02:00.920><c> you</c><00:02:01.000><c> can</c>

00:02:01.230 --> 00:02:01.240 align:start position:0%
llm you can have the llm config you can
 

00:02:01.240 --> 00:02:03.670 align:start position:0%
llm you can have the llm config you can
have<00:02:01.479><c> that</c><00:02:01.759><c> set</c><00:02:01.920><c> up</c><00:02:02.079><c> with</c><00:02:02.200><c> ol</c><00:02:02.560><c> or</c><00:02:02.680><c> LM</c><00:02:02.920><c> Studio</c><00:02:03.280><c> or</c>

00:02:03.670 --> 00:02:03.680 align:start position:0%
have that set up with ol or LM Studio or
 

00:02:03.680 --> 00:02:06.429 align:start position:0%
have that set up with ol or LM Studio or
any<00:02:03.920><c> other</c><00:02:04.159><c> local</c><00:02:04.439><c> server</c><00:02:05.320><c> but</c><00:02:06.000><c> uh</c><00:02:06.240><c> with</c>

00:02:06.429 --> 00:02:06.439 align:start position:0%
any other local server but uh with
 

00:02:06.439 --> 00:02:09.350 align:start position:0%
any other local server but uh with
there's<00:02:06.600><c> a</c><00:02:06.920><c> previous</c><00:02:07.719><c> uh</c><00:02:07.960><c> version</c><00:02:08.560><c> of</c><00:02:08.759><c> llms</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
there's a previous uh version of llms
 

00:02:09.360 --> 00:02:11.949 align:start position:0%
there's a previous uh version of llms
from<00:02:09.560><c> linkchain</c><00:02:09.920><c> you</c><00:02:10.319><c> can</c><00:02:10.440><c> use</c><00:02:10.640><c> chat</c><00:02:10.920><c> open</c><00:02:11.239><c> AI</c>

00:02:11.949 --> 00:02:11.959 align:start position:0%
from linkchain you can use chat open AI
 

00:02:11.959 --> 00:02:14.190 align:start position:0%
from linkchain you can use chat open AI
which<00:02:12.160><c> basically</c><00:02:12.800><c> only</c><00:02:13.040><c> allows</c><00:02:13.400><c> you</c><00:02:13.720><c> to</c><00:02:13.920><c> use</c>

00:02:14.190 --> 00:02:14.200 align:start position:0%
which basically only allows you to use
 

00:02:14.200 --> 00:02:17.190 align:start position:0%
which basically only allows you to use
open<00:02:14.440><c> ai's</c><00:02:14.840><c> API</c><00:02:15.640><c> there's</c><00:02:15.959><c> a</c><00:02:16.120><c> new</c><00:02:16.319><c> one</c><00:02:16.879><c> called</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
open ai's API there's a new one called
 

00:02:17.200 --> 00:02:19.470 align:start position:0%
open ai's API there's a new one called
just<00:02:17.599><c> open</c><00:02:17.959><c> AI</c><00:02:18.599><c> which</c><00:02:18.720><c> allows</c><00:02:19.000><c> you</c><00:02:19.080><c> to</c><00:02:19.239><c> bring</c>

00:02:19.470 --> 00:02:19.480 align:start position:0%
just open AI which allows you to bring
 

00:02:19.480 --> 00:02:22.150 align:start position:0%
just open AI which allows you to bring
in<00:02:19.640><c> a</c><00:02:19.840><c> base</c><00:02:20.120><c> URL</c><00:02:20.879><c> and</c><00:02:21.000><c> a</c><00:02:21.120><c> model</c><00:02:21.519><c> as</c><00:02:21.680><c> well</c><00:02:22.040><c> which</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
in a base URL and a model as well which
 

00:02:22.160 --> 00:02:24.390 align:start position:0%
in a base URL and a model as well which
means<00:02:22.400><c> that</c><00:02:22.560><c> you</c><00:02:22.640><c> can</c><00:02:22.760><c> use</c><00:02:22.959><c> a</c><00:02:23.080><c> local</c><00:02:23.400><c> server</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
means that you can use a local server
 

00:02:24.400 --> 00:02:26.750 align:start position:0%
means that you can use a local server
however<00:02:24.840><c> I</c><00:02:25.000><c> tried</c><00:02:25.640><c> so</c><00:02:25.840><c> many</c><00:02:26.080><c> different</c><00:02:26.400><c> ways</c>

00:02:26.750 --> 00:02:26.760 align:start position:0%
however I tried so many different ways
 

00:02:26.760 --> 00:02:28.949 align:start position:0%
however I tried so many different ways
to<00:02:27.000><c> use</c><00:02:27.280><c> that</c><00:02:27.519><c> here</c><00:02:28.040><c> but</c><00:02:28.239><c> I</c><00:02:28.400><c> kept</c><00:02:28.640><c> getting</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
to use that here but I kept getting
 

00:02:28.959 --> 00:02:31.750 align:start position:0%
to use that here but I kept getting
errors<00:02:29.599><c> so</c><00:02:29.959><c> I</c><00:02:30.080><c> just</c><00:02:30.480><c> I</c><00:02:30.599><c> just</c><00:02:30.720><c> stuck</c><00:02:31.040><c> with</c><00:02:31.200><c> using</c>

00:02:31.750 --> 00:02:31.760 align:start position:0%
errors so I just I just stuck with using
 

00:02:31.760 --> 00:02:33.949 align:start position:0%
errors so I just I just stuck with using
open<00:02:32.080><c> ai's</c><00:02:32.440><c> API</c><00:02:32.959><c> here</c><00:02:33.239><c> which</c><00:02:33.360><c> is</c><00:02:33.480><c> the</c><00:02:33.640><c> chat</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
open ai's API here which is the chat
 

00:02:33.959 --> 00:02:35.630 align:start position:0%
open ai's API here which is the chat
open<00:02:34.239><c> Ai</c><00:02:34.680><c> and</c><00:02:34.760><c> then</c><00:02:34.840><c> I</c><00:02:34.959><c> call</c><00:02:35.120><c> this</c><00:02:35.239><c> markdown</c>

00:02:35.630 --> 00:02:35.640 align:start position:0%
open Ai and then I call this markdown
 

00:02:35.640 --> 00:02:38.229 align:start position:0%
open Ai and then I call this markdown
Library<00:02:36.480><c> which</c><00:02:36.680><c> converts</c><00:02:37.200><c> a</c><00:02:37.360><c> markdown</c><00:02:37.920><c> string</c>

00:02:38.229 --> 00:02:38.239 align:start position:0%
Library which converts a markdown string
 

00:02:38.239 --> 00:02:40.990 align:start position:0%
Library which converts a markdown string
to<00:02:38.440><c> HTML</c><00:02:39.000><c> then</c><00:02:39.280><c> returns</c><00:02:39.599><c> HTML</c><00:02:40.239><c> as</c><00:02:40.319><c> a</c><00:02:40.480><c> Unicode</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
to HTML then returns HTML as a Unicode
 

00:02:41.000 --> 00:02:43.630 align:start position:0%
to HTML then returns HTML as a Unicode
string<00:02:41.879><c> I</c><00:02:42.000><c> found</c><00:02:42.280><c> that</c><00:02:42.440><c> aogen</c><00:02:42.959><c> is</c><00:02:43.200><c> great</c><00:02:43.440><c> at</c>

00:02:43.630 --> 00:02:43.640 align:start position:0%
string I found that aogen is great at
 

00:02:43.640 --> 00:02:46.710 align:start position:0%
string I found that aogen is great at
formatting<00:02:44.319><c> in</c><00:02:44.560><c> markdown</c><00:02:45.480><c> and</c><00:02:45.680><c> not</c><00:02:46.040><c> so</c><00:02:46.280><c> much</c>

00:02:46.710 --> 00:02:46.720 align:start position:0%
formatting in markdown and not so much
 

00:02:46.720 --> 00:02:49.550 align:start position:0%
formatting in markdown and not so much
in<00:02:47.280><c> a</c><00:02:48.000><c> regular</c><00:02:48.360><c> email</c><00:02:48.720><c> formats</c><00:02:49.120><c> so</c><00:02:49.239><c> I</c><00:02:49.360><c> kind</c><00:02:49.440><c> of</c>

00:02:49.550 --> 00:02:49.560 align:start position:0%
in a regular email formats so I kind of
 

00:02:49.560 --> 00:02:51.350 align:start position:0%
in a regular email formats so I kind of
had<00:02:49.720><c> dysfunction</c><00:02:50.360><c> do</c><00:02:50.599><c> all</c><00:02:50.760><c> that</c><00:02:50.879><c> for</c><00:02:51.040><c> me</c><00:02:51.239><c> and</c>

00:02:51.350 --> 00:02:51.360 align:start position:0%
had dysfunction do all that for me and
 

00:02:51.360 --> 00:02:53.190 align:start position:0%
had dysfunction do all that for me and
this<00:02:51.480><c> next</c><00:02:51.680><c> pit</c><00:02:51.879><c> of</c><00:02:52.000><c> code</c><00:02:52.200><c> is</c><00:02:52.360><c> basically</c><00:02:52.760><c> where</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
this next pit of code is basically where
 

00:02:53.200 --> 00:02:54.670 align:start position:0%
this next pit of code is basically where
we<00:02:53.319><c> are</c><00:02:53.440><c> going</c><00:02:53.560><c> to</c><00:02:53.640><c> be</c><00:02:53.879><c> sending</c><00:02:54.120><c> the</c><00:02:54.280><c> email</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
we are going to be sending the email
 

00:02:54.680 --> 00:02:57.030 align:start position:0%
we are going to be sending the email
from<00:02:54.840><c> the</c><00:02:55.040><c> Gmail</c><00:02:55.800><c> L</c><00:02:56.040><c> chain</c><00:02:56.280><c> service</c><00:02:56.720><c> so</c><00:02:56.840><c> what</c><00:02:56.959><c> I</c>

00:02:57.030 --> 00:02:57.040 align:start position:0%
from the Gmail L chain service so what I
 

00:02:57.040 --> 00:02:59.470 align:start position:0%
from the Gmail L chain service so what I
want<00:02:57.200><c> to</c><00:02:57.360><c> do</c><00:02:57.720><c> is</c><00:02:58.080><c> ask</c><00:02:58.319><c> can</c><00:02:58.440><c> you</c><00:02:58.519><c> send</c><00:02:58.720><c> an</c><00:02:58.879><c> email</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
want to do is ask can you send an email
 

00:02:59.480 --> 00:03:02.589 align:start position:0%
want to do is ask can you send an email
to<00:03:00.000><c> my</c><00:03:00.200><c> email</c><00:03:01.040><c> from</c><00:03:01.280><c> the</c><00:03:01.440><c> post</c><00:03:01.800><c> created</c><00:03:02.200><c> above</c>

00:03:02.589 --> 00:03:02.599 align:start position:0%
to my email from the post created above
 

00:03:02.599 --> 00:03:04.910 align:start position:0%
to my email from the post created above
which<00:03:02.720><c> will</c><00:03:02.920><c> be</c><00:03:03.159><c> the</c><00:03:03.360><c> content</c><00:03:03.799><c> coming</c><00:03:04.080><c> in</c><00:03:04.840><c> and</c>

00:03:04.910 --> 00:03:04.920 align:start position:0%
which will be the content coming in and
 

00:03:04.920 --> 00:03:07.390 align:start position:0%
which will be the content coming in and
it<00:03:05.000><c> should</c><00:03:05.200><c> end</c><00:03:05.480><c> with</c><00:03:05.760><c> sincerely</c><00:03:06.480><c> Tyler</c><00:03:07.239><c> and</c>

00:03:07.390 --> 00:03:07.400 align:start position:0%
it should end with sincerely Tyler and
 

00:03:07.400 --> 00:03:09.910 align:start position:0%
it should end with sincerely Tyler and
we<00:03:07.519><c> don't</c><00:03:07.799><c> want</c><00:03:07.920><c> to</c><00:03:08.040><c> CC</c><00:03:08.519><c> anybody</c><00:03:08.879><c> if</c><00:03:09.080><c> I</c><00:03:09.560><c> I</c><00:03:09.680><c> found</c>

00:03:09.910 --> 00:03:09.920 align:start position:0%
we don't want to CC anybody if I I found
 

00:03:09.920 --> 00:03:11.830 align:start position:0%
we don't want to CC anybody if I I found
out<00:03:10.120><c> that</c><00:03:10.280><c> if</c><00:03:10.440><c> I</c><00:03:10.640><c> don't</c><00:03:10.879><c> have</c><00:03:11.080><c> this</c><00:03:11.280><c> here</c><00:03:11.519><c> it'll</c>

00:03:11.830 --> 00:03:11.840 align:start position:0%
out that if I don't have this here it'll
 

00:03:11.840 --> 00:03:14.710 align:start position:0%
out that if I don't have this here it'll
attempt<00:03:12.080><c> to</c><00:03:12.239><c> CC</c><00:03:13.239><c> and</c><00:03:13.560><c> because</c><00:03:13.920><c> I</c><00:03:14.319><c> don't</c><00:03:14.560><c> have</c>

00:03:14.710 --> 00:03:14.720 align:start position:0%
attempt to CC and because I don't have
 

00:03:14.720 --> 00:03:16.390 align:start position:0%
attempt to CC and because I don't have
any<00:03:14.879><c> other</c><00:03:15.080><c> emails</c><00:03:15.440><c> for</c><00:03:15.560><c> it</c><00:03:15.640><c> to</c><00:03:15.799><c> CC</c><00:03:16.159><c> it</c><00:03:16.239><c> just</c>

00:03:16.390 --> 00:03:16.400 align:start position:0%
any other emails for it to CC it just
 

00:03:16.400 --> 00:03:18.190 align:start position:0%
any other emails for it to CC it just
kind<00:03:16.480><c> of</c><00:03:16.640><c> airs</c><00:03:17.080><c> out</c><00:03:17.360><c> so</c><00:03:17.640><c> I</c><00:03:17.720><c> had</c><00:03:17.840><c> to</c><00:03:18.040><c> kind</c><00:03:18.120><c> of</c>

00:03:18.190 --> 00:03:18.200 align:start position:0%
kind of airs out so I had to kind of
 

00:03:18.200 --> 00:03:20.070 align:start position:0%
kind of airs out so I had to kind of
change<00:03:18.400><c> up</c><00:03:18.519><c> the</c><00:03:18.720><c> prompting</c><00:03:19.200><c> so</c><00:03:19.640><c> I</c><00:03:19.720><c> don't</c><00:03:19.920><c> get</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
change up the prompting so I don't get
 

00:03:20.080 --> 00:03:21.390 align:start position:0%
change up the prompting so I don't get
those<00:03:20.280><c> errors</c><00:03:20.760><c> and</c><00:03:20.879><c> finally</c><00:03:21.159><c> we</c><00:03:21.280><c> just</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
those errors and finally we just
 

00:03:21.400 --> 00:03:23.149 align:start position:0%
those errors and finally we just
initiate<00:03:21.879><c> chat</c><00:03:22.120><c> with</c><00:03:22.239><c> the</c><00:03:22.360><c> writer</c><00:03:22.840><c> and</c><00:03:22.959><c> I</c><00:03:23.040><c> want</c>

00:03:23.149 --> 00:03:23.159 align:start position:0%
initiate chat with the writer and I want
 

00:03:23.159 --> 00:03:25.070 align:start position:0%
initiate chat with the writer and I want
to<00:03:23.280><c> create</c><00:03:23.480><c> a</c><00:03:23.560><c> blog</c><00:03:23.920><c> post</c><00:03:24.480><c> with</c><00:03:24.640><c> the</c><00:03:24.760><c> following</c>

00:03:25.070 --> 00:03:25.080 align:start position:0%
to create a blog post with the following
 

00:03:25.080 --> 00:03:27.229 align:start position:0%
to create a blog post with the following
YouTube<00:03:25.480><c> script</c><00:03:25.799><c> with</c><00:03:25.920><c> the</c><00:03:26.080><c> title</c><00:03:26.720><c> outline</c><00:03:27.080><c> of</c>

00:03:27.229 --> 00:03:27.239 align:start position:0%
YouTube script with the title outline of
 

00:03:27.239 --> 00:03:28.589 align:start position:0%
YouTube script with the title outline of
the<00:03:27.319><c> main</c><00:03:27.560><c> points</c><00:03:27.840><c> and</c><00:03:27.959><c> then</c><00:03:28.120><c> paragraphs</c>

00:03:28.589 --> 00:03:28.599 align:start position:0%
the main points and then paragraphs
 

00:03:28.599 --> 00:03:30.350 align:start position:0%
the main points and then paragraphs
explaining<00:03:29.000><c> and</c><00:03:29.120><c> summarizing</c><00:03:29.519><c> the</c><00:03:29.799><c> script</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
explaining and summarizing the script
 

00:03:30.360 --> 00:03:31.630 align:start position:0%
explaining and summarizing the script
make<00:03:30.480><c> sure</c><00:03:30.640><c> this</c><00:03:30.760><c> is</c><00:03:30.959><c> formatted</c><00:03:31.480><c> with</c>

00:03:31.630 --> 00:03:31.640 align:start position:0%
make sure this is formatted with
 

00:03:31.640 --> 00:03:33.830 align:start position:0%
make sure this is formatted with
markdown<00:03:32.280><c> okay</c><00:03:32.640><c> let's</c><00:03:32.840><c> run</c><00:03:33.080><c> this</c><00:03:33.319><c> and</c><00:03:33.640><c> see</c>

00:03:33.830 --> 00:03:33.840 align:start position:0%
markdown okay let's run this and see
 

00:03:33.840 --> 00:03:35.350 align:start position:0%
markdown okay let's run this and see
what<00:03:33.959><c> it</c><00:03:34.080><c> gives</c><00:03:34.280><c> us</c><00:03:34.480><c> and</c><00:03:34.640><c> see</c><00:03:34.760><c> if</c><00:03:34.879><c> it</c><00:03:35.080><c> actually</c>

00:03:35.350 --> 00:03:35.360 align:start position:0%
what it gives us and see if it actually
 

00:03:35.360 --> 00:03:37.229 align:start position:0%
what it gives us and see if it actually
sends<00:03:35.760><c> to</c><00:03:35.959><c> my</c><00:03:36.120><c> email</c><00:03:36.680><c> all</c><00:03:36.760><c> right</c><00:03:36.879><c> so</c><00:03:36.959><c> I</c><00:03:37.040><c> think</c><00:03:37.159><c> I</c>

00:03:37.229 --> 00:03:37.239 align:start position:0%
sends to my email all right so I think I
 

00:03:37.239 --> 00:03:38.589 align:start position:0%
sends to my email all right so I think I
executed<00:03:37.560><c> this</c><00:03:37.680><c> like</c><00:03:37.799><c> 2</c><00:03:37.959><c> 3</c><00:03:38.080><c> minutes</c><00:03:38.280><c> ago</c><00:03:38.480><c> which</c>

00:03:38.589 --> 00:03:38.599 align:start position:0%
executed this like 2 3 minutes ago which
 

00:03:38.599 --> 00:03:41.910 align:start position:0%
executed this like 2 3 minutes ago which
was<00:03:38.760><c> like</c><00:03:38.920><c> at</c><00:03:39.239><c> 12:42</c><00:03:40.439><c> 12:43</c><00:03:41.439><c> um</c><00:03:41.560><c> and</c><00:03:41.640><c> it</c><00:03:41.760><c> looks</c>

00:03:41.910 --> 00:03:41.920 align:start position:0%
was like at 12:42 12:43 um and it looks
 

00:03:41.920 --> 00:03:44.990 align:start position:0%
was like at 12:42 12:43 um and it looks
like<00:03:42.080><c> it</c><00:03:42.239><c> did</c><00:03:42.840><c> summarize</c><00:03:43.840><c> uh</c><00:03:44.120><c> the</c><00:03:44.360><c> Youtube</c>

00:03:44.990 --> 00:03:45.000 align:start position:0%
like it did summarize uh the Youtube
 

00:03:45.000 --> 00:03:47.270 align:start position:0%
like it did summarize uh the Youtube
transcript<00:03:45.560><c> anyway</c><00:03:46.000><c> so</c><00:03:46.159><c> it</c><00:03:46.280><c> took</c><00:03:46.560><c> the</c><00:03:46.840><c> script</c>

00:03:47.270 --> 00:03:47.280 align:start position:0%
transcript anyway so it took the script
 

00:03:47.280 --> 00:03:49.390 align:start position:0%
transcript anyway so it took the script
which<00:03:47.480><c> was</c><00:03:47.959><c> about</c><00:03:48.439><c> Lang</c><00:03:48.720><c> chain</c><00:03:49.000><c> right</c><00:03:49.080><c> so</c><00:03:49.200><c> L</c>

00:03:49.390 --> 00:03:49.400 align:start position:0%
which was about Lang chain right so L
 

00:03:49.400 --> 00:03:52.030 align:start position:0%
which was about Lang chain right so L
chain<00:03:49.640><c> revolutionizing</c><00:03:50.400><c> llm</c><00:03:51.040><c> applications</c>

00:03:52.030 --> 00:03:52.040 align:start position:0%
chain revolutionizing llm applications
 

00:03:52.040 --> 00:03:53.750 align:start position:0%
chain revolutionizing llm applications
now<00:03:52.239><c> let's</c><00:03:52.400><c> just</c><00:03:52.560><c> check</c><00:03:52.760><c> my</c><00:03:52.959><c> mail</c><00:03:53.280><c> to</c><00:03:53.400><c> see</c><00:03:53.519><c> if</c><00:03:53.599><c> I</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
now let's just check my mail to see if I
 

00:03:53.760 --> 00:03:56.190 align:start position:0%
now let's just check my mail to see if I
got<00:03:53.879><c> it</c><00:03:54.079><c> and</c><00:03:54.200><c> if</c><00:03:54.360><c> I</c><00:03:54.560><c> look</c><00:03:54.879><c> in</c><00:03:55.079><c> my</c><00:03:55.360><c> email</c><00:03:55.920><c> yeah</c><00:03:56.079><c> it</c>

00:03:56.190 --> 00:03:56.200 align:start position:0%
got it and if I look in my email yeah it
 

00:03:56.200 --> 00:03:59.069 align:start position:0%
got it and if I look in my email yeah it
was<00:03:56.319><c> sent</c><00:03:56.599><c> at</c><00:03:56.920><c> 12:44</c><00:03:57.920><c> and</c><00:03:58.200><c> it's</c><00:03:58.360><c> about</c><00:03:58.560><c> L</c><00:03:58.840><c> chain</c>

00:03:59.069 --> 00:03:59.079 align:start position:0%
was sent at 12:44 and it's about L chain
 

00:03:59.079 --> 00:04:01.830 align:start position:0%
was sent at 12:44 and it's about L chain
revolutionizing<00:03:59.920><c> llm</c><00:04:00.720><c> applications</c><00:04:01.720><c> all</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
revolutionizing llm applications all
 

00:04:01.840 --> 00:04:04.190 align:start position:0%
revolutionizing llm applications all
right<00:04:01.959><c> and</c><00:04:02.400><c> this</c><00:04:02.519><c> is</c><00:04:02.720><c> in</c><00:04:02.920><c> a</c><00:04:03.120><c> readable</c><00:04:03.680><c> format</c>

00:04:04.190 --> 00:04:04.200 align:start position:0%
right and this is in a readable format
 

00:04:04.200 --> 00:04:06.589 align:start position:0%
right and this is in a readable format
through<00:04:04.439><c> email</c><00:04:04.959><c> I've</c><00:04:05.159><c> now</c><00:04:05.560><c> been</c><00:04:05.760><c> able</c><00:04:06.000><c> to</c><00:04:06.319><c> take</c>

00:04:06.589 --> 00:04:06.599 align:start position:0%
through email I've now been able to take
 

00:04:06.599 --> 00:04:10.509 align:start position:0%
through email I've now been able to take
a<00:04:06.799><c> YouTube</c><00:04:07.239><c> URL</c><00:04:08.239><c> and</c><00:04:08.400><c> then</c><00:04:08.760><c> have</c><00:04:09.159><c> ai</c><00:04:09.840><c> take</c><00:04:10.079><c> that</c>

00:04:10.509 --> 00:04:10.519 align:start position:0%
a YouTube URL and then have ai take that
 

00:04:10.519 --> 00:04:12.750 align:start position:0%
a YouTube URL and then have ai take that
transcribe<00:04:11.120><c> it</c><00:04:11.319><c> with</c><00:04:11.519><c> Lang</c><00:04:11.799><c> chain</c><00:04:12.480><c> and</c><00:04:12.599><c> then</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
transcribe it with Lang chain and then
 

00:04:12.760 --> 00:04:15.830 align:start position:0%
transcribe it with Lang chain and then
we<00:04:12.879><c> can</c><00:04:13.120><c> have</c><00:04:13.360><c> autogen</c><00:04:14.079><c> agents</c><00:04:14.920><c> take</c><00:04:15.200><c> that</c>

00:04:15.830 --> 00:04:15.840 align:start position:0%
we can have autogen agents take that
 

00:04:15.840 --> 00:04:18.349 align:start position:0%
we can have autogen agents take that
convert<00:04:16.160><c> it</c><00:04:16.400><c> into</c><00:04:16.880><c> a</c><00:04:17.479><c> readable</c><00:04:17.880><c> format</c><00:04:18.199><c> for</c>

00:04:18.349 --> 00:04:18.359 align:start position:0%
convert it into a readable format for
 

00:04:18.359 --> 00:04:20.390 align:start position:0%
convert it into a readable format for
the<00:04:18.479><c> email</c><00:04:19.000><c> through</c><00:04:19.199><c> a</c><00:04:19.320><c> function</c><00:04:19.680><c> call</c><00:04:20.160><c> then</c>

00:04:20.390 --> 00:04:20.400 align:start position:0%
the email through a function call then
 

00:04:20.400 --> 00:04:22.150 align:start position:0%
the email through a function call then
actually<00:04:20.639><c> send</c><00:04:21.000><c> the</c><00:04:21.199><c> email</c><00:04:21.720><c> and</c><00:04:21.880><c> that's</c>

00:04:22.150 --> 00:04:22.160 align:start position:0%
actually send the email and that's
 

00:04:22.160 --> 00:04:23.590 align:start position:0%
actually send the email and that's
amazing<00:04:22.600><c> imagine</c><00:04:22.880><c> if</c><00:04:22.960><c> you</c><00:04:23.040><c> had</c><00:04:23.160><c> a</c><00:04:23.280><c> flash</c>

00:04:23.590 --> 00:04:23.600 align:start position:0%
amazing imagine if you had a flash
 

00:04:23.600 --> 00:04:25.670 align:start position:0%
amazing imagine if you had a flash
server<00:04:24.320><c> that</c><00:04:24.440><c> could</c><00:04:24.680><c> grab</c><00:04:25.000><c> a</c><00:04:25.240><c> different</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
server that could grab a different
 

00:04:25.680 --> 00:04:28.189 align:start position:0%
server that could grab a different
YouTube<00:04:26.120><c> video</c><00:04:26.440><c> about</c><00:04:26.720><c> AI</c><00:04:27.360><c> every</c><00:04:27.600><c> day</c><00:04:28.080><c> and</c>

00:04:28.189 --> 00:04:28.199 align:start position:0%
YouTube video about AI every day and
 

00:04:28.199 --> 00:04:30.550 align:start position:0%
YouTube video about AI every day and
then<00:04:28.400><c> send</c><00:04:28.720><c> that</c><00:04:28.919><c> as</c><00:04:29.160><c> either</c><00:04:29.400><c> a</c><00:04:29.720><c> newsletter</c><00:04:30.400><c> or</c>

00:04:30.550 --> 00:04:30.560 align:start position:0%
then send that as either a newsletter or
 

00:04:30.560 --> 00:04:32.590 align:start position:0%
then send that as either a newsletter or
send<00:04:30.800><c> that</c><00:04:30.960><c> to</c><00:04:31.600><c> um</c><00:04:31.720><c> you</c><00:04:31.840><c> know</c><00:04:32.080><c> your</c><00:04:32.240><c> friends</c>

00:04:32.590 --> 00:04:32.600 align:start position:0%
send that to um you know your friends
 

00:04:32.600 --> 00:04:34.990 align:start position:0%
send that to um you know your friends
emails<00:04:33.160><c> whatever</c><00:04:33.479><c> it</c><00:04:33.639><c> is</c><00:04:34.280><c> but</c><00:04:34.440><c> this</c><00:04:34.639><c> could</c><00:04:34.800><c> be</c>

00:04:34.990 --> 00:04:35.000 align:start position:0%
emails whatever it is but this could be
 

00:04:35.000 --> 00:04:37.029 align:start position:0%
emails whatever it is but this could be
scheduled<00:04:35.680><c> and</c><00:04:35.840><c> automated</c><00:04:36.360><c> every</c><00:04:36.560><c> day</c><00:04:36.840><c> here's</c>

00:04:37.029 --> 00:04:37.039 align:start position:0%
scheduled and automated every day here's
 

00:04:37.039 --> 00:04:38.150 align:start position:0%
scheduled and automated every day here's
another<00:04:37.240><c> video</c><00:04:37.479><c> that</c><00:04:37.600><c> can</c><00:04:37.759><c> actually</c><00:04:38.000><c> have</c>

00:04:38.150 --> 00:04:38.160 align:start position:0%
another video that can actually have
 

00:04:38.160 --> 00:04:39.950 align:start position:0%
another video that can actually have
automated<00:04:38.840><c> because</c><00:04:39.039><c> I</c><00:04:39.160><c> know</c><00:04:39.320><c> it's</c><00:04:39.440><c> not</c><00:04:39.639><c> easy</c>

00:04:39.950 --> 00:04:39.960 align:start position:0%
automated because I know it's not easy
 

00:04:39.960 --> 00:04:42.550 align:start position:0%
automated because I know it's not easy
to<00:04:40.280><c> watch</c><00:04:40.840><c> all</c><00:04:41.080><c> these</c><00:04:41.320><c> YouTube</c><00:04:42.039><c> videos</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
to watch all these YouTube videos
 

00:04:42.560 --> 00:04:44.189 align:start position:0%
to watch all these YouTube videos
because<00:04:42.759><c> you</c><00:04:42.840><c> want</c><00:04:42.960><c> to</c><00:04:43.080><c> learn</c><00:04:43.320><c> so</c><00:04:43.560><c> much</c>

00:04:44.189 --> 00:04:44.199 align:start position:0%
because you want to learn so much
 

00:04:44.199 --> 00:04:46.150 align:start position:0%
because you want to learn so much
especially<00:04:44.919><c> learning</c><00:04:45.199><c> at</c><00:04:45.400><c> regular</c><00:04:45.840><c> speed</c>

00:04:46.150 --> 00:04:46.160 align:start position:0%
especially learning at regular speed
 

00:04:46.160 --> 00:04:47.350 align:start position:0%
especially learning at regular speed
which<00:04:46.320><c> I</c><00:04:46.479><c> tend</c><00:04:46.639><c> to</c><00:04:46.759><c> watch</c><00:04:46.919><c> things</c><00:04:47.039><c> at</c><00:04:47.160><c> one</c><00:04:47.280><c> and</c>

00:04:47.350 --> 00:04:47.360 align:start position:0%
which I tend to watch things at one and
 

00:04:47.360 --> 00:04:48.670 align:start position:0%
which I tend to watch things at one and
a<00:04:47.479><c> half</c><00:04:47.600><c> to</c><00:04:47.720><c> two</c><00:04:47.919><c> times</c><00:04:48.120><c> speed</c><00:04:48.320><c> depending</c><00:04:48.600><c> on</c>

00:04:48.670 --> 00:04:48.680 align:start position:0%
a half to two times speed depending on
 

00:04:48.680 --> 00:04:50.110 align:start position:0%
a half to two times speed depending on
what<00:04:48.800><c> it</c><00:04:48.919><c> is</c><00:04:49.320><c> but</c><00:04:49.479><c> this</c><00:04:49.600><c> can</c><00:04:49.759><c> actually</c><00:04:49.960><c> help</c>

00:04:50.110 --> 00:04:50.120 align:start position:0%
what it is but this can actually help
 

00:04:50.120 --> 00:04:51.550 align:start position:0%
what it is but this can actually help
you<00:04:50.280><c> get</c><00:04:50.400><c> an</c><00:04:50.639><c> idea</c><00:04:50.919><c> of</c><00:04:51.039><c> what</c><00:04:51.120><c> they're</c><00:04:51.320><c> talking</c>

00:04:51.550 --> 00:04:51.560 align:start position:0%
you get an idea of what they're talking
 

00:04:51.560 --> 00:04:52.870 align:start position:0%
you get an idea of what they're talking
about<00:04:51.840><c> or</c><00:04:51.960><c> in</c><00:04:52.039><c> the</c><00:04:52.199><c> last</c><00:04:52.360><c> leg</c><00:04:52.600><c> of</c><00:04:52.759><c> this</c>

00:04:52.870 --> 00:04:52.880 align:start position:0%
about or in the last leg of this
 

00:04:52.880 --> 00:04:54.749 align:start position:0%
about or in the last leg of this
challenge<00:04:53.759><c> thank</c><00:04:53.880><c> you</c><00:04:53.960><c> for</c><00:04:54.120><c> sticking</c><00:04:54.440><c> with</c><00:04:54.600><c> me</c>

00:04:54.749 --> 00:04:54.759 align:start position:0%
challenge thank you for sticking with me
 

00:04:54.759 --> 00:04:56.749 align:start position:0%
challenge thank you for sticking with me
if<00:04:54.840><c> you</c><00:04:55.000><c> have</c><00:04:55.120><c> so</c><00:04:55.440><c> far</c><00:04:55.600><c> if</c><00:04:55.720><c> you</c><00:04:55.919><c> en</c><00:04:56.039><c> joined</c><00:04:56.360><c> at</c>

00:04:56.749 --> 00:04:56.759 align:start position:0%
if you have so far if you en joined at
 

00:04:56.759 --> 00:04:58.110 align:start position:0%
if you have so far if you en joined at
whatever<00:04:57.160><c> point</c><00:04:57.479><c> just</c><00:04:57.680><c> thank</c><00:04:57.800><c> you</c><00:04:57.919><c> for</c>

00:04:58.110 --> 00:04:58.120 align:start position:0%
whatever point just thank you for
 

00:04:58.120 --> 00:04:59.790 align:start position:0%
whatever point just thank you for
watching<00:04:58.440><c> these</c><00:04:58.639><c> videos</c><00:04:59.080><c> here's</c><00:04:59.240><c> a</c><00:04:59.360><c> playlist</c>

00:04:59.790 --> 00:04:59.800 align:start position:0%
watching these videos here's a playlist
 

00:04:59.800 --> 00:05:01.749 align:start position:0%
watching these videos here's a playlist
on<00:04:59.960><c> another</c><00:05:00.280><c> video</c><00:05:00.720><c> please</c><00:05:01.000><c> watch</c><00:05:01.240><c> them</c><00:05:01.600><c> like</c>

00:05:01.749 --> 00:05:01.759 align:start position:0%
on another video please watch them like
 

00:05:01.759 --> 00:05:03.270 align:start position:0%
on another video please watch them like
And<00:05:01.919><c> subscribe</c><00:05:02.520><c> and</c><00:05:02.639><c> I'll</c><00:05:02.759><c> see</c><00:05:02.919><c> you</c><00:05:03.080><c> next</c>

00:05:03.270 --> 00:05:03.280 align:start position:0%
And subscribe and I'll see you next
 

00:05:03.280 --> 00:05:05.600 align:start position:0%
And subscribe and I'll see you next
video

