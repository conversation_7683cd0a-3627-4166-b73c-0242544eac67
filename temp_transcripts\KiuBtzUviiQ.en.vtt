WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:02.550 align:start position:0%
 
i'm<00:00:01.199><c> pamela</c><00:00:01.599><c> foxx</c><00:00:02.000><c> i'm</c><00:00:02.080><c> going</c><00:00:02.159><c> to</c><00:00:02.240><c> talk</c><00:00:02.399><c> about</c>

00:00:02.550 --> 00:00:02.560 align:start position:0%
i'm pamela foxx i'm going to talk about
 

00:00:02.560 --> 00:00:04.550 align:start position:0%
i'm pamela foxx i'm going to talk about
the<00:00:02.720><c> benefits</c><00:00:03.199><c> of</c><00:00:03.439><c> html</c><00:00:04.000><c> slides</c><00:00:04.319><c> for</c>

00:00:04.550 --> 00:00:04.560 align:start position:0%
the benefits of html slides for
 

00:00:04.560 --> 00:00:06.389 align:start position:0%
the benefits of html slides for
programming<00:00:05.120><c> classes</c>

00:00:06.389 --> 00:00:06.399 align:start position:0%
programming classes
 

00:00:06.399 --> 00:00:07.990 align:start position:0%
programming classes
now<00:00:06.640><c> you</c><00:00:06.799><c> might</c><00:00:06.960><c> be</c><00:00:07.120><c> writing</c><00:00:07.440><c> slides</c><00:00:07.839><c> in</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
now you might be writing slides in
 

00:00:08.000 --> 00:00:10.150 align:start position:0%
now you might be writing slides in
powerpoint<00:00:08.720><c> google</c><00:00:09.040><c> slides</c><00:00:09.679><c> maybe</c><00:00:09.920><c> even</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
powerpoint google slides maybe even
 

00:00:10.160 --> 00:00:13.030 align:start position:0%
powerpoint google slides maybe even
writing<00:00:10.559><c> your</c><00:00:10.719><c> own</c><00:00:11.200><c> pdfs</c><00:00:11.759><c> using</c><00:00:12.000><c> latex</c><00:00:12.880><c> so</c>

00:00:13.030 --> 00:00:13.040 align:start position:0%
writing your own pdfs using latex so
 

00:00:13.040 --> 00:00:15.669 align:start position:0%
writing your own pdfs using latex so
those<00:00:13.360><c> all</c><00:00:13.599><c> have</c><00:00:14.080><c> a</c><00:00:14.240><c> lot</c><00:00:14.400><c> of</c><00:00:14.480><c> benefits</c><00:00:15.200><c> but</c><00:00:15.519><c> for</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
those all have a lot of benefits but for
 

00:00:15.679 --> 00:00:18.390 align:start position:0%
those all have a lot of benefits but for
programming<00:00:16.160><c> classes</c><00:00:16.560><c> they</c><00:00:16.800><c> are</c><00:00:17.039><c> not</c><00:00:17.359><c> ideal</c>

00:00:18.390 --> 00:00:18.400 align:start position:0%
programming classes they are not ideal
 

00:00:18.400 --> 00:00:20.550 align:start position:0%
programming classes they are not ideal
it's<00:00:18.560><c> hard</c><00:00:18.800><c> to</c><00:00:18.880><c> write</c><00:00:19.119><c> syntax</c><00:00:19.520><c> highly</c><00:00:19.840><c> encoded</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
it's hard to write syntax highly encoded
 

00:00:20.560 --> 00:00:22.310 align:start position:0%
it's hard to write syntax highly encoded
as<00:00:20.720><c> the</c><00:00:20.880><c> instructor</c><00:00:21.600><c> and</c><00:00:21.760><c> it's</c><00:00:21.920><c> hard</c><00:00:22.160><c> for</c>

00:00:22.310 --> 00:00:22.320 align:start position:0%
as the instructor and it's hard for
 

00:00:22.320 --> 00:00:23.990 align:start position:0%
as the instructor and it's hard for
students<00:00:22.720><c> to</c><00:00:22.880><c> copy</c><00:00:23.199><c> that</c><00:00:23.359><c> code</c><00:00:23.600><c> and</c><00:00:23.760><c> actually</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
students to copy that code and actually
 

00:00:24.000 --> 00:00:25.349 align:start position:0%
students to copy that code and actually
try<00:00:24.160><c> it</c><00:00:24.320><c> themselves</c>

00:00:25.349 --> 00:00:25.359 align:start position:0%
try it themselves
 

00:00:25.359 --> 00:00:27.589 align:start position:0%
try it themselves
so<00:00:25.680><c> what</c><00:00:25.840><c> i</c><00:00:25.920><c> want</c><00:00:26.080><c> to</c><00:00:26.240><c> recommend</c><00:00:27.119><c> for</c><00:00:27.359><c> those</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
so what i want to recommend for those
 

00:00:27.599 --> 00:00:31.029 align:start position:0%
so what i want to recommend for those
classes<00:00:28.080><c> is</c><00:00:28.240><c> to</c><00:00:28.400><c> write</c><00:00:28.640><c> your</c><00:00:28.880><c> slides</c><00:00:29.359><c> in</c><00:00:29.679><c> html</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
classes is to write your slides in html
 

00:00:31.039 --> 00:00:33.830 align:start position:0%
classes is to write your slides in html
so<00:00:31.199><c> you</c><00:00:31.439><c> start</c><00:00:31.679><c> with</c><00:00:31.920><c> some</c><00:00:32.160><c> semantic</c><00:00:32.719><c> html</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
so you start with some semantic html
 

00:00:33.840 --> 00:00:36.790 align:start position:0%
so you start with some semantic html
you<00:00:34.000><c> can</c><00:00:34.239><c> see</c><00:00:34.559><c> this</c><00:00:34.800><c> slide</c><00:00:35.120><c> here</c><00:00:35.600><c> in</c><00:00:35.760><c> html</c><00:00:36.719><c> and</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
you can see this slide here in html and
 

00:00:36.800 --> 00:00:39.110 align:start position:0%
you can see this slide here in html and
then<00:00:36.960><c> you</c><00:00:37.120><c> add</c><00:00:37.360><c> some</c><00:00:37.600><c> javascript</c><00:00:38.640><c> the</c><00:00:38.800><c> most</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
then you add some javascript the most
 

00:00:39.120 --> 00:00:40.869 align:start position:0%
then you add some javascript the most
popular<00:00:39.760><c> fully</c><00:00:40.079><c> featured</c><00:00:40.399><c> javascript</c>

00:00:40.869 --> 00:00:40.879 align:start position:0%
popular fully featured javascript
 

00:00:40.879 --> 00:00:43.590 align:start position:0%
popular fully featured javascript
library<00:00:41.360><c> is</c><00:00:41.520><c> reveal.js</c><00:00:42.640><c> it</c><00:00:42.879><c> concludes</c>

00:00:43.590 --> 00:00:43.600 align:start position:0%
library is reveal.js it concludes
 

00:00:43.600 --> 00:00:45.590 align:start position:0%
library is reveal.js it concludes
support<00:00:44.000><c> for</c><00:00:44.160><c> all</c><00:00:44.320><c> the</c><00:00:44.399><c> things</c><00:00:44.559><c> you'd</c><00:00:44.879><c> expect</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
support for all the things you'd expect
 

00:00:45.600 --> 00:00:48.150 align:start position:0%
support for all the things you'd expect
out<00:00:45.760><c> of</c><00:00:45.920><c> slides</c><00:00:46.320><c> like</c><00:00:46.719><c> keyboard</c><00:00:47.120><c> navigation</c>

00:00:48.150 --> 00:00:48.160 align:start position:0%
out of slides like keyboard navigation
 

00:00:48.160 --> 00:00:50.790 align:start position:0%
out of slides like keyboard navigation
speaker<00:00:48.559><c> notes</c><00:00:48.960><c> animations</c><00:00:49.840><c> but</c><00:00:50.160><c> also</c><00:00:50.480><c> has</c>

00:00:50.790 --> 00:00:50.800 align:start position:0%
speaker notes animations but also has
 

00:00:50.800 --> 00:00:52.950 align:start position:0%
speaker notes animations but also has
much<00:00:51.039><c> more</c><00:00:51.360><c> especially</c><00:00:51.920><c> with</c><00:00:52.079><c> the</c><00:00:52.239><c> plugins</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
much more especially with the plugins
 

00:00:52.960 --> 00:00:55.189 align:start position:0%
much more especially with the plugins
that<00:00:53.280><c> many</c><00:00:53.520><c> people</c><00:00:53.840><c> have</c><00:00:54.079><c> written</c><00:00:54.559><c> to</c><00:00:54.719><c> extend</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
that many people have written to extend
 

00:00:55.199 --> 00:00:56.790 align:start position:0%
that many people have written to extend
it<00:00:55.440><c> and</c><00:00:55.600><c> you</c><00:00:55.680><c> can</c><00:00:55.920><c> even</c><00:00:56.160><c> write</c><00:00:56.399><c> your</c><00:00:56.559><c> own</c>

00:00:56.790 --> 00:00:56.800 align:start position:0%
it and you can even write your own
 

00:00:56.800 --> 00:00:58.709 align:start position:0%
it and you can even write your own
plugins<00:00:57.280><c> to</c><00:00:57.440><c> extend</c><00:00:57.920><c> it</c>

00:00:58.709 --> 00:00:58.719 align:start position:0%
plugins to extend it
 

00:00:58.719 --> 00:01:01.990 align:start position:0%
plugins to extend it
so<00:00:59.039><c> highly</c><00:00:59.359><c> capable</c><00:00:59.920><c> highly</c><00:01:00.239><c> flexible</c>

00:01:01.990 --> 00:01:02.000 align:start position:0%
so highly capable highly flexible
 

00:01:02.000 --> 00:01:03.189 align:start position:0%
so highly capable highly flexible
so<00:01:02.480><c> let's</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
so let's
 

00:01:03.199 --> 00:01:04.789 align:start position:0%
so let's
check<00:01:03.359><c> out</c><00:01:03.440><c> what</c><00:01:03.680><c> we</c><00:01:03.680><c> can</c><00:01:03.840><c> do</c><00:01:03.920><c> with</c><00:01:04.159><c> code</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
check out what we can do with code
 

00:01:04.799 --> 00:01:06.550 align:start position:0%
check out what we can do with code
starting<00:01:05.119><c> with</c><00:01:05.280><c> the</c><00:01:05.360><c> highlight.js</c><00:01:06.080><c> plugin</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
starting with the highlight.js plugin
 

00:01:06.560 --> 00:01:08.230 align:start position:0%
starting with the highlight.js plugin
that<00:01:06.720><c> will</c><00:01:06.960><c> highlight</c><00:01:07.360><c> code</c><00:01:07.680><c> for</c><00:01:07.840><c> us</c><00:01:08.000><c> so</c><00:01:08.159><c> we</c>

00:01:08.230 --> 00:01:08.240 align:start position:0%
that will highlight code for us so we
 

00:01:08.240 --> 00:01:10.310 align:start position:0%
that will highlight code for us so we
just<00:01:08.400><c> have</c><00:01:08.560><c> to</c><00:01:08.640><c> write</c><00:01:08.880><c> the</c><00:01:08.960><c> code</c><00:01:09.760><c> put</c><00:01:09.920><c> it</c><00:01:10.080><c> in</c><00:01:10.240><c> a</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
just have to write the code put it in a
 

00:01:10.320 --> 00:01:12.550 align:start position:0%
just have to write the code put it in a
pre<00:01:10.560><c> block</c><00:01:11.200><c> and</c><00:01:11.360><c> say</c><00:01:11.600><c> what</c><00:01:11.760><c> type</c><00:01:12.000><c> of</c><00:01:12.159><c> language</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
pre block and say what type of language
 

00:01:12.560 --> 00:01:14.070 align:start position:0%
pre block and say what type of language
it<00:01:12.640><c> is</c><00:01:13.040><c> and</c><00:01:13.200><c> it'll</c><00:01:13.360><c> get</c><00:01:13.600><c> highlighted</c>

00:01:14.070 --> 00:01:14.080 align:start position:0%
it is and it'll get highlighted
 

00:01:14.080 --> 00:01:16.310 align:start position:0%
it is and it'll get highlighted
according<00:01:14.320><c> to</c><00:01:14.479><c> that</c><00:01:14.640><c> language</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
according to that language
 

00:01:16.320 --> 00:01:18.550 align:start position:0%
according to that language
now<00:01:16.560><c> i</c><00:01:16.720><c> like</c><00:01:16.880><c> to</c><00:01:16.960><c> take</c><00:01:17.119><c> it</c><00:01:17.200><c> a</c><00:01:17.759><c> step</c><00:01:18.000><c> farther</c><00:01:18.479><c> and</c>

00:01:18.550 --> 00:01:18.560 align:start position:0%
now i like to take it a step farther and
 

00:01:18.560 --> 00:01:20.789 align:start position:0%
now i like to take it a step farther and
make<00:01:18.799><c> it</c><00:01:18.880><c> easy</c><00:01:19.119><c> to</c><00:01:19.439><c> copy</c><00:01:19.759><c> the</c><00:01:19.920><c> code</c><00:01:20.240><c> so</c><00:01:20.400><c> i</c><00:01:20.560><c> added</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
make it easy to copy the code so i added
 

00:01:20.799 --> 00:01:22.950 align:start position:0%
make it easy to copy the code so i added
the<00:01:20.960><c> highlight.js</c><00:01:22.000><c> badge</c><00:01:22.320><c> plug</c><00:01:22.560><c> into</c><00:01:22.799><c> my</c>

00:01:22.950 --> 00:01:22.960 align:start position:0%
the highlight.js badge plug into my
 

00:01:22.960 --> 00:01:24.950 align:start position:0%
the highlight.js badge plug into my
slides<00:01:23.600><c> and</c><00:01:23.759><c> that</c><00:01:24.000><c> adds</c><00:01:24.240><c> this</c><00:01:24.479><c> nice</c><00:01:24.720><c> little</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
slides and that adds this nice little
 

00:01:24.960 --> 00:01:26.390 align:start position:0%
slides and that adds this nice little
copy<00:01:25.200><c> to</c><00:01:25.360><c> clipboard</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
copy to clipboard
 

00:01:26.400 --> 00:01:29.510 align:start position:0%
copy to clipboard
i<00:01:26.640><c> click</c><00:01:26.880><c> that</c><00:01:27.280><c> boom</c><00:01:27.680><c> it's</c><00:01:28.000><c> in</c><00:01:28.240><c> my</c><00:01:28.400><c> clipboard</c>

00:01:29.510 --> 00:01:29.520 align:start position:0%
i click that boom it's in my clipboard
 

00:01:29.520 --> 00:01:31.990 align:start position:0%
i click that boom it's in my clipboard
this<00:01:30.000><c> is</c><00:01:30.400><c> so</c><00:01:30.799><c> helpful</c><00:01:31.360><c> when</c><00:01:31.600><c> i'm</c><00:01:31.759><c> going</c>

00:01:31.990 --> 00:01:32.000 align:start position:0%
this is so helpful when i'm going
 

00:01:32.000 --> 00:01:33.270 align:start position:0%
this is so helpful when i'm going
through<00:01:32.159><c> the</c><00:01:32.320><c> slides</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
through the slides
 

00:01:33.280 --> 00:01:35.270 align:start position:0%
through the slides
and<00:01:33.520><c> i</c><00:01:33.759><c> just</c><00:01:34.000><c> want</c><00:01:34.159><c> to</c><00:01:34.400><c> try</c><00:01:34.640><c> out</c><00:01:34.720><c> code</c><00:01:34.960><c> try</c><00:01:35.200><c> out</c>

00:01:35.270 --> 00:01:35.280 align:start position:0%
and i just want to try out code try out
 

00:01:35.280 --> 00:01:36.630 align:start position:0%
and i just want to try out code try out
code<00:01:35.520><c> trial</c><00:01:35.759><c> code</c><00:01:36.000><c> and</c><00:01:36.079><c> i</c><00:01:36.159><c> also</c><00:01:36.400><c> want</c><00:01:36.560><c> the</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
code trial code and i also want the
 

00:01:36.640 --> 00:01:38.310 align:start position:0%
code trial code and i also want the
students<00:01:36.880><c> to</c><00:01:37.040><c> try</c><00:01:37.280><c> out</c><00:01:37.360><c> code</c><00:01:37.600><c> trial</c><00:01:38.079><c> trial</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
students to try out code trial trial
 

00:01:38.320 --> 00:01:39.830 align:start position:0%
students to try out code trial trial
code<00:01:38.560><c> right</c><00:01:39.040><c> so</c><00:01:39.200><c> instead</c><00:01:39.439><c> of</c><00:01:39.520><c> having</c><00:01:39.680><c> to</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
code right so instead of having to
 

00:01:39.840 --> 00:01:42.950 align:start position:0%
code right so instead of having to
fiddling<00:01:40.640><c> with</c><00:01:40.880><c> selection</c><00:01:41.759><c> i</c><00:01:41.920><c> just</c><00:01:42.159><c> boom</c><00:01:42.799><c> put</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
fiddling with selection i just boom put
 

00:01:42.960 --> 00:01:45.429 align:start position:0%
fiddling with selection i just boom put
it<00:01:43.119><c> in</c><00:01:43.200><c> my</c><00:01:43.360><c> clipboard</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
it in my clipboard
 

00:01:45.439 --> 00:01:47.270 align:start position:0%
it in my clipboard
you<00:01:45.600><c> could</c><00:01:45.840><c> even</c><00:01:46.079><c> do</c><00:01:46.320><c> runnable</c><00:01:46.720><c> code</c><00:01:46.960><c> so</c><00:01:47.119><c> this</c>

00:01:47.270 --> 00:01:47.280 align:start position:0%
you could even do runnable code so this
 

00:01:47.280 --> 00:01:49.990 align:start position:0%
you could even do runnable code so this
is<00:01:47.439><c> a</c><00:01:47.520><c> quick</c><00:01:47.759><c> demo</c><00:01:48.079><c> i</c><00:01:48.240><c> put</c><00:01:48.399><c> together</c><00:01:48.880><c> using</c><00:01:49.759><c> uh</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
is a quick demo i put together using uh
 

00:01:50.000 --> 00:01:53.270 align:start position:0%
is a quick demo i put together using uh
brython<00:01:50.799><c> or</c><00:01:51.040><c> piodide</c><00:01:52.079><c> and</c><00:01:52.640><c> here</c><00:01:52.799><c> we</c><00:01:52.960><c> have</c><00:01:53.119><c> some</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
brython or piodide and here we have some
 

00:01:53.280 --> 00:01:55.590 align:start position:0%
brython or piodide and here we have some
actual<00:01:53.680><c> runnable</c><00:01:54.159><c> code</c><00:01:54.880><c> and</c><00:01:55.040><c> we</c><00:01:55.200><c> could</c><00:01:55.360><c> try</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
actual runnable code and we could try
 

00:01:55.600 --> 00:01:57.429 align:start position:0%
actual runnable code and we could try
running<00:01:55.920><c> this</c><00:01:56.479><c> and</c><00:01:56.640><c> here</c><00:01:56.880><c> this</c><00:01:57.119><c> actually</c><00:01:57.360><c> has</c>

00:01:57.429 --> 00:01:57.439 align:start position:0%
running this and here this actually has
 

00:01:57.439 --> 00:01:59.510 align:start position:0%
running this and here this actually has
a<00:01:57.600><c> bug</c><00:01:58.240><c> we</c><00:01:58.399><c> could</c><00:01:58.560><c> fix</c><00:01:58.880><c> it</c><00:01:59.040><c> i</c><00:01:59.119><c> could</c><00:01:59.280><c> ask</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
a bug we could fix it i could ask
 

00:01:59.520 --> 00:02:02.069 align:start position:0%
a bug we could fix it i could ask
students<00:01:59.759><c> for</c><00:01:59.920><c> their</c><00:02:00.159><c> input</c><00:02:00.880><c> and</c><00:02:01.119><c> boom</c><00:02:01.759><c> fixed</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
students for their input and boom fixed
 

00:02:02.079 --> 00:02:04.149 align:start position:0%
students for their input and boom fixed
it<00:02:02.560><c> so</c><00:02:02.799><c> depending</c><00:02:03.280><c> on</c><00:02:03.360><c> the</c><00:02:03.439><c> language</c><00:02:04.000><c> you</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
it so depending on the language you
 

00:02:04.159 --> 00:02:06.230 align:start position:0%
it so depending on the language you
could<00:02:04.399><c> even</c><00:02:04.799><c> run</c><00:02:05.040><c> the</c><00:02:05.200><c> code</c><00:02:05.600><c> inside</c><00:02:06.079><c> the</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
could even run the code inside the
 

00:02:06.240 --> 00:02:08.710 align:start position:0%
could even run the code inside the
slides<00:02:06.719><c> and</c><00:02:06.799><c> that</c><00:02:07.040><c> is</c><00:02:07.200><c> just</c><00:02:07.759><c> just</c><00:02:08.000><c> so</c><00:02:08.239><c> fun</c><00:02:08.479><c> so</c>

00:02:08.710 --> 00:02:08.720 align:start position:0%
slides and that is just just so fun so
 

00:02:08.720 --> 00:02:11.029 align:start position:0%
slides and that is just just so fun so
interactive

00:02:11.029 --> 00:02:11.039 align:start position:0%
interactive
 

00:02:11.039 --> 00:02:13.030 align:start position:0%
interactive
in<00:02:11.280><c> addition</c><00:02:11.840><c> your</c><00:02:12.080><c> slides</c><00:02:12.560><c> are</c><00:02:12.720><c> still</c>

00:02:13.030 --> 00:02:13.040 align:start position:0%
in addition your slides are still
 

00:02:13.040 --> 00:02:14.869 align:start position:0%
in addition your slides are still
printable<00:02:13.520><c> so</c><00:02:13.760><c> if</c><00:02:13.840><c> you</c><00:02:14.000><c> press</c><00:02:14.239><c> print</c><00:02:14.560><c> you</c><00:02:14.640><c> will</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
printable so if you press print you will
 

00:02:14.879 --> 00:02:17.030 align:start position:0%
printable so if you press print you will
get<00:02:15.120><c> something</c><00:02:15.440><c> that</c><00:02:15.599><c> looks</c><00:02:15.840><c> like</c><00:02:16.000><c> slides</c>

00:02:17.030 --> 00:02:17.040 align:start position:0%
get something that looks like slides
 

00:02:17.040 --> 00:02:18.470 align:start position:0%
get something that looks like slides
you<00:02:17.200><c> can</c><00:02:17.360><c> even</c><00:02:17.599><c> automate</c><00:02:17.920><c> the</c><00:02:18.000><c> creation</c><00:02:18.400><c> of</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
you can even automate the creation of
 

00:02:18.480 --> 00:02:20.150 align:start position:0%
you can even automate the creation of
that<00:02:18.640><c> so</c><00:02:18.800><c> we</c><00:02:19.040><c> always</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
that so we always
 

00:02:20.160 --> 00:02:22.070 align:start position:0%
that so we always
have<00:02:20.560><c> the</c><00:02:20.640><c> pdf</c><00:02:21.120><c> version</c><00:02:21.520><c> on</c><00:02:21.680><c> our</c><00:02:21.840><c> course</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
have the pdf version on our course
 

00:02:22.080 --> 00:02:23.830 align:start position:0%
have the pdf version on our course
website<00:02:22.480><c> right</c><00:02:22.720><c> next</c><00:02:22.879><c> to</c><00:02:22.959><c> html</c><00:02:23.360><c> version</c><00:02:23.599><c> so</c>

00:02:23.830 --> 00:02:23.840 align:start position:0%
website right next to html version so
 

00:02:23.840 --> 00:02:25.270 align:start position:0%
website right next to html version so
someone<00:02:24.160><c> can</c><00:02:24.319><c> use</c><00:02:24.480><c> that</c><00:02:24.720><c> if</c><00:02:24.800><c> they</c><00:02:24.959><c> want</c><00:02:25.120><c> to</c><00:02:25.200><c> you</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
someone can use that if they want to you
 

00:02:25.280 --> 00:02:26.869 align:start position:0%
someone can use that if they want to you
know<00:02:25.440><c> take</c><00:02:25.680><c> notes</c><00:02:26.000><c> on</c><00:02:26.160><c> an</c><00:02:26.239><c> ipad</c><00:02:26.640><c> during</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
know take notes on an ipad during
 

00:02:26.879 --> 00:02:30.229 align:start position:0%
know take notes on an ipad during
lecture<00:02:27.280><c> whatever</c><00:02:27.599><c> they</c><00:02:27.760><c> want</c><00:02:27.840><c> to</c><00:02:27.920><c> use</c><00:02:28.160><c> it</c><00:02:28.239><c> for</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
lecture whatever they want to use it for
 

00:02:30.239 --> 00:02:31.830 align:start position:0%
lecture whatever they want to use it for
see<00:02:30.480><c> slides</c><00:02:30.800><c> are</c><00:02:30.879><c> also</c><00:02:31.120><c> accessible</c><00:02:31.599><c> because</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
see slides are also accessible because
 

00:02:31.840 --> 00:02:34.150 align:start position:0%
see slides are also accessible because
they're<00:02:32.000><c> written</c><00:02:32.319><c> in</c><00:02:32.400><c> html</c><00:02:32.879><c> and</c><00:02:33.040><c> html</c><00:02:34.000><c> is</c>

00:02:34.150 --> 00:02:34.160 align:start position:0%
they're written in html and html is
 

00:02:34.160 --> 00:02:35.670 align:start position:0%
they're written in html and html is
built<00:02:34.400><c> for</c><00:02:34.560><c> accessibility</c><00:02:35.200><c> so</c><00:02:35.360><c> you</c><00:02:35.440><c> just</c><00:02:35.519><c> have</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
built for accessibility so you just have
 

00:02:35.680 --> 00:02:37.509 align:start position:0%
built for accessibility so you just have
to<00:02:35.840><c> follow</c><00:02:36.080><c> best</c><00:02:36.400><c> practices</c><00:02:37.200><c> like</c><00:02:37.360><c> when</c>

00:02:37.509 --> 00:02:37.519 align:start position:0%
to follow best practices like when
 

00:02:37.519 --> 00:02:39.110 align:start position:0%
to follow best practices like when
you're<00:02:37.599><c> doing</c><00:02:37.840><c> an</c><00:02:38.080><c> image</c><00:02:38.319><c> tag</c><00:02:38.640><c> make</c><00:02:38.800><c> sure</c><00:02:39.040><c> it</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
you're doing an image tag make sure it
 

00:02:39.120 --> 00:02:40.949 align:start position:0%
you're doing an image tag make sure it
has<00:02:39.280><c> an</c><00:02:39.440><c> alt</c><00:02:39.760><c> attribute</c><00:02:40.400><c> so</c><00:02:40.560><c> that</c><00:02:40.720><c> somebody</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
has an alt attribute so that somebody
 

00:02:40.959 --> 00:02:44.070 align:start position:0%
has an alt attribute so that somebody
using<00:02:41.280><c> a</c><00:02:41.360><c> screen</c><00:02:41.680><c> reader</c><00:02:42.000><c> can</c><00:02:42.160><c> understand</c><00:02:42.640><c> it</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
using a screen reader can understand it
 

00:02:44.080 --> 00:02:47.030 align:start position:0%
using a screen reader can understand it
now<00:02:44.400><c> animations</c><00:02:45.599><c> are</c><00:02:45.760><c> a</c><00:02:45.840><c> big</c><00:02:46.080><c> part</c><00:02:46.319><c> of</c><00:02:46.560><c> slides</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
now animations are a big part of slides
 

00:02:47.040 --> 00:02:48.309 align:start position:0%
now animations are a big part of slides
a<00:02:47.120><c> lot</c><00:02:47.280><c> of</c><00:02:47.360><c> times</c><00:02:47.680><c> and</c><00:02:47.760><c> they</c><00:02:47.920><c> can</c><00:02:48.080><c> you</c><00:02:48.160><c> know</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
a lot of times and they can you know
 

00:02:48.319 --> 00:02:49.110 align:start position:0%
a lot of times and they can you know
make

00:02:49.110 --> 00:02:49.120 align:start position:0%
make
 

00:02:49.120 --> 00:02:51.589 align:start position:0%
make
diagrams<00:02:49.760><c> easier</c><00:02:50.080><c> to</c><00:02:50.160><c> understand</c><00:02:50.879><c> and</c>

00:02:51.589 --> 00:02:51.599 align:start position:0%
diagrams easier to understand and
 

00:02:51.599 --> 00:02:53.830 align:start position:0%
diagrams easier to understand and
reveal<00:02:52.000><c> stuff</c><00:02:52.239><c> in</c><00:02:52.319><c> order</c><00:02:52.720><c> so</c><00:02:52.959><c> in</c>

00:02:53.830 --> 00:02:53.840 align:start position:0%
reveal stuff in order so in
 

00:02:53.840 --> 00:02:55.589 align:start position:0%
reveal stuff in order so in
reveal<00:02:54.160><c> js</c><00:02:54.480><c> you</c><00:02:54.560><c> do</c><00:02:54.720><c> that</c><00:02:54.879><c> with</c><00:02:55.120><c> fragments</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
reveal js you do that with fragments
 

00:02:55.599 --> 00:02:57.190 align:start position:0%
reveal js you do that with fragments
just<00:02:55.760><c> by</c><00:02:55.920><c> adding</c><00:02:56.239><c> a</c><00:02:56.319><c> class</c><00:02:56.640><c> attribute</c><00:02:57.120><c> of</c>

00:02:57.190 --> 00:02:57.200 align:start position:0%
just by adding a class attribute of
 

00:02:57.200 --> 00:02:58.710 align:start position:0%
just by adding a class attribute of
fragment<00:02:57.599><c> to</c><00:02:57.760><c> whatever</c><00:02:58.080><c> tag</c><00:02:58.400><c> you</c><00:02:58.480><c> want</c><00:02:58.640><c> to</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
fragment to whatever tag you want to
 

00:02:58.720 --> 00:02:59.830 align:start position:0%
fragment to whatever tag you want to
reveal

00:02:59.830 --> 00:02:59.840 align:start position:0%
reveal
 

00:02:59.840 --> 00:03:01.670 align:start position:0%
reveal
so<00:03:00.080><c> for</c><00:03:00.239><c> this</c><00:03:00.400><c> paragraph</c><00:03:00.879><c> we</c><00:03:01.040><c> add</c><00:03:01.200><c> that</c><00:03:01.360><c> class</c>

00:03:01.670 --> 00:03:01.680 align:start position:0%
so for this paragraph we add that class
 

00:03:01.680 --> 00:03:03.589 align:start position:0%
so for this paragraph we add that class
attribute<00:03:02.239><c> that</c><00:03:02.400><c> reveals</c><00:03:02.879><c> it</c><00:03:03.200><c> and</c><00:03:03.360><c> you</c><00:03:03.519><c> can</c>

00:03:03.589 --> 00:03:03.599 align:start position:0%
attribute that reveals it and you can
 

00:03:03.599 --> 00:03:06.149 align:start position:0%
attribute that reveals it and you can
use<00:03:03.920><c> fragments</c><00:03:04.400><c> for</c><00:03:04.720><c> anything</c><00:03:05.360><c> even</c><00:03:05.680><c> parts</c><00:03:06.000><c> of</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
use fragments for anything even parts of
 

00:03:06.159 --> 00:03:08.710 align:start position:0%
use fragments for anything even parts of
code<00:03:06.480><c> blocks</c><00:03:07.120><c> so</c><00:03:07.360><c> here's</c><00:03:07.599><c> a</c><00:03:07.680><c> code</c><00:03:08.000><c> block</c><00:03:08.480><c> and</c><00:03:08.560><c> i</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
code blocks so here's a code block and i
 

00:03:08.720 --> 00:03:11.190 align:start position:0%
code blocks so here's a code block and i
want<00:03:08.879><c> to</c><00:03:09.120><c> wait</c><00:03:09.760><c> to</c><00:03:09.920><c> reveal</c><00:03:10.319><c> the</c><00:03:10.480><c> results</c>

00:03:11.190 --> 00:03:11.200 align:start position:0%
want to wait to reveal the results
 

00:03:11.200 --> 00:03:12.470 align:start position:0%
want to wait to reveal the results
because<00:03:11.440><c> i</c><00:03:11.519><c> want</c><00:03:11.680><c> to</c><00:03:11.760><c> you</c><00:03:11.840><c> know</c><00:03:12.080><c> ask</c><00:03:12.239><c> my</c>

00:03:12.470 --> 00:03:12.480 align:start position:0%
because i want to you know ask my
 

00:03:12.480 --> 00:03:13.670 align:start position:0%
because i want to you know ask my
students<00:03:12.879><c> what</c><00:03:13.040><c> they</c><00:03:13.120><c> think</c><00:03:13.280><c> it's</c><00:03:13.440><c> going</c><00:03:13.599><c> to</c>

00:03:13.670 --> 00:03:13.680 align:start position:0%
students what they think it's going to
 

00:03:13.680 --> 00:03:16.869 align:start position:0%
students what they think it's going to
be<00:03:14.239><c> so</c><00:03:14.400><c> i</c><00:03:14.560><c> wrap</c><00:03:14.879><c> that</c><00:03:15.120><c> in</c><00:03:15.200><c> a</c><00:03:15.360><c> fragment</c><00:03:16.080><c> and</c><00:03:16.319><c> boom</c>

00:03:16.869 --> 00:03:16.879 align:start position:0%
be so i wrap that in a fragment and boom
 

00:03:16.879 --> 00:03:18.949 align:start position:0%
be so i wrap that in a fragment and boom
there's<00:03:17.200><c> my</c><00:03:17.360><c> result</c><00:03:17.760><c> and</c><00:03:17.920><c> i</c><00:03:18.000><c> do</c><00:03:18.159><c> this</c><00:03:18.560><c> all</c><00:03:18.800><c> the</c>

00:03:18.949 --> 00:03:18.959 align:start position:0%
there's my result and i do this all the
 

00:03:18.959 --> 00:03:21.110 align:start position:0%
there's my result and i do this all the
time<00:03:19.519><c> when</c><00:03:19.680><c> i'm</c><00:03:19.920><c> asking</c><00:03:20.319><c> students</c><00:03:20.640><c> to</c><00:03:20.720><c> predict</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
time when i'm asking students to predict
 

00:03:21.120 --> 00:03:22.710 align:start position:0%
time when i'm asking students to predict
code

00:03:22.710 --> 00:03:22.720 align:start position:0%
code
 

00:03:22.720 --> 00:03:25.270 align:start position:0%
code
you<00:03:22.879><c> can</c><00:03:23.120><c> even</c><00:03:23.440><c> add</c><00:03:23.920><c> you</c><00:03:24.000><c> can</c><00:03:24.159><c> animate</c><00:03:24.560><c> svgs</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
you can even add you can animate svgs
 

00:03:25.280 --> 00:03:27.030 align:start position:0%
you can even add you can animate svgs
using<00:03:25.680><c> class</c><00:03:26.000><c> of</c><00:03:26.080><c> fragment</c><00:03:26.560><c> so</c><00:03:26.720><c> this</c><00:03:26.799><c> is</c><00:03:26.959><c> an</c>

00:03:27.030 --> 00:03:27.040 align:start position:0%
using class of fragment so this is an
 

00:03:27.040 --> 00:03:28.710 align:start position:0%
using class of fragment so this is an
svg<00:03:27.519><c> i</c><00:03:27.599><c> made</c><00:03:27.760><c> in</c><00:03:27.920><c> illustrator</c><00:03:28.400><c> to</c><00:03:28.480><c> show</c><00:03:28.640><c> a</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
svg i made in illustrator to show a
 

00:03:28.720 --> 00:03:30.630 align:start position:0%
svg i made in illustrator to show a
nested<00:03:29.120><c> call</c><00:03:29.360><c> expression</c>

00:03:30.630 --> 00:03:30.640 align:start position:0%
nested call expression
 

00:03:30.640 --> 00:03:32.789 align:start position:0%
nested call expression
and<00:03:31.120><c> i</c><00:03:31.440><c> just</c><00:03:31.760><c> you</c><00:03:31.920><c> know</c><00:03:32.239><c> made</c><00:03:32.480><c> it</c><00:03:32.560><c> into</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
and i just you know made it into
 

00:03:32.799 --> 00:03:35.270 align:start position:0%
and i just you know made it into
fragments<00:03:33.680><c> and</c><00:03:33.840><c> then</c><00:03:34.080><c> boom</c><00:03:34.720><c> go</c><00:03:34.959><c> through</c><00:03:35.120><c> it</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
fragments and then boom go through it
 

00:03:35.280 --> 00:03:36.229 align:start position:0%
fragments and then boom go through it
all

00:03:36.229 --> 00:03:36.239 align:start position:0%
all
 

00:03:36.239 --> 00:03:37.910 align:start position:0%
all
now<00:03:36.480><c> if</c><00:03:36.640><c> you</c><00:03:36.879><c> need</c><00:03:37.040><c> to</c><00:03:37.120><c> go</c><00:03:37.360><c> a</c><00:03:37.360><c> little</c><00:03:37.599><c> farther</c>

00:03:37.910 --> 00:03:37.920 align:start position:0%
now if you need to go a little farther
 

00:03:37.920 --> 00:03:39.910 align:start position:0%
now if you need to go a little farther
and<00:03:38.080><c> even</c><00:03:38.480><c> you</c><00:03:38.560><c> know</c><00:03:38.799><c> write</c><00:03:38.959><c> something</c><00:03:39.360><c> more</c>

00:03:39.910 --> 00:03:39.920 align:start position:0%
and even you know write something more
 

00:03:39.920 --> 00:03:41.830 align:start position:0%
and even you know write something more
intricate<00:03:40.400><c> you</c><00:03:40.560><c> can</c><00:03:40.799><c> you</c><00:03:40.959><c> can</c><00:03:41.040><c> use</c><00:03:41.280><c> javascript</c>

00:03:41.830 --> 00:03:41.840 align:start position:0%
intricate you can you can use javascript
 

00:03:41.840 --> 00:03:44.470 align:start position:0%
intricate you can you can use javascript
so<00:03:42.000><c> here's</c><00:03:42.239><c> some</c><00:03:42.480><c> javascript</c><00:03:43.040><c> and</c><00:03:43.200><c> css</c><00:03:44.159><c> to</c>

00:03:44.470 --> 00:03:44.480 align:start position:0%
so here's some javascript and css to
 

00:03:44.480 --> 00:03:47.430 align:start position:0%
so here's some javascript and css to
show<00:03:45.200><c> how</c><00:03:45.440><c> slow</c><00:03:45.680><c> it</c><00:03:45.920><c> is</c><00:03:46.000><c> to</c><00:03:46.319><c> insert</c><00:03:46.799><c> things</c>

00:03:47.430 --> 00:03:47.440 align:start position:0%
show how slow it is to insert things
 

00:03:47.440 --> 00:03:49.350 align:start position:0%
show how slow it is to insert things
into<00:03:47.760><c> an</c><00:03:47.920><c> array</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
into an array
 

00:03:49.360 --> 00:03:50.869 align:start position:0%
into an array
so<00:03:49.840><c> slow</c>

00:03:50.869 --> 00:03:50.879 align:start position:0%
so slow
 

00:03:50.879 --> 00:03:51.830 align:start position:0%
so slow
so

00:03:51.830 --> 00:03:51.840 align:start position:0%
so
 

00:03:51.840 --> 00:03:53.750 align:start position:0%
so
you<00:03:52.000><c> can</c><00:03:52.239><c> really</c><00:03:52.400><c> do</c><00:03:52.640><c> a</c><00:03:52.720><c> lot</c><00:03:53.280><c> in</c><00:03:53.439><c> terms</c><00:03:53.680><c> of</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
you can really do a lot in terms of
 

00:03:53.760 --> 00:03:55.670 align:start position:0%
you can really do a lot in terms of
making<00:03:54.080><c> your</c><00:03:54.239><c> slides</c><00:03:54.799><c> you</c><00:03:54.879><c> know</c><00:03:55.439><c> doing</c>

00:03:55.670 --> 00:03:55.680 align:start position:0%
making your slides you know doing
 

00:03:55.680 --> 00:03:57.750 align:start position:0%
making your slides you know doing
simulations<00:03:56.239><c> and</c><00:03:56.400><c> animations</c><00:03:56.959><c> and</c><00:03:57.040><c> all</c><00:03:57.200><c> that</c>

00:03:57.750 --> 00:03:57.760 align:start position:0%
simulations and animations and all that
 

00:03:57.760 --> 00:03:59.750 align:start position:0%
simulations and animations and all that
so<00:03:57.920><c> to</c><00:03:58.080><c> get</c><00:03:58.319><c> started</c><00:03:59.040><c> you</c><00:03:59.200><c> could</c><00:03:59.360><c> start</c><00:03:59.599><c> from</c>

00:03:59.750 --> 00:03:59.760 align:start position:0%
so to get started you could start from
 

00:03:59.760 --> 00:04:01.030 align:start position:0%
so to get started you could start from
this<00:03:59.920><c> slide</c><00:04:00.159><c> deck</c><00:04:00.480><c> you</c><00:04:00.640><c> could</c><00:04:00.720><c> follow</c><00:04:00.959><c> the</c>

00:04:01.030 --> 00:04:01.040 align:start position:0%
this slide deck you could follow the
 

00:04:01.040 --> 00:04:02.789 align:start position:0%
this slide deck you could follow the
reveal.js<00:04:01.680><c> tutorial</c>

00:04:02.789 --> 00:04:02.799 align:start position:0%
reveal.js tutorial
 

00:04:02.799 --> 00:04:04.470 align:start position:0%
reveal.js tutorial
however<00:04:03.200><c> you</c><00:04:03.360><c> want</c><00:04:03.439><c> to</c><00:04:03.519><c> get</c><00:04:03.680><c> started</c><00:04:04.239><c> and</c><00:04:04.319><c> then</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
however you want to get started and then
 

00:04:04.480 --> 00:04:05.990 align:start position:0%
however you want to get started and then
you<00:04:04.640><c> could</c><00:04:04.879><c> you</c><00:04:04.959><c> know</c><00:04:05.120><c> upload</c><00:04:05.439><c> your</c><00:04:05.599><c> slides</c>

00:04:05.990 --> 00:04:06.000 align:start position:0%
you could you know upload your slides
 

00:04:06.000 --> 00:04:07.190 align:start position:0%
you could you know upload your slides
somewhere

00:04:07.190 --> 00:04:07.200 align:start position:0%
somewhere
 

00:04:07.200 --> 00:04:08.949 align:start position:0%
somewhere
if<00:04:07.280><c> you</c><00:04:07.439><c> prefer</c><00:04:07.760><c> markdown</c><00:04:08.239><c> you</c><00:04:08.319><c> can</c><00:04:08.480><c> also</c><00:04:08.720><c> use</c>

00:04:08.949 --> 00:04:08.959 align:start position:0%
if you prefer markdown you can also use
 

00:04:08.959 --> 00:04:11.270 align:start position:0%
if you prefer markdown you can also use
that<00:04:09.200><c> with</c><00:04:09.439><c> a</c><00:04:09.519><c> real</c><00:04:09.760><c> js</c><00:04:10.560><c> if</c><00:04:10.720><c> you</c><00:04:10.879><c> don't</c><00:04:11.040><c> like</c>

00:04:11.270 --> 00:04:11.280 align:start position:0%
that with a real js if you don't like
 

00:04:11.280 --> 00:04:14.229 align:start position:0%
that with a real js if you don't like
html<00:04:11.760><c> or</c><00:04:11.920><c> markdown</c><00:04:12.720><c> you</c><00:04:12.959><c> can</c><00:04:13.360><c> use</c><00:04:13.519><c> the</c><00:04:13.680><c> wysiwyg</c>

00:04:14.229 --> 00:04:14.239 align:start position:0%
html or markdown you can use the wysiwyg
 

00:04:14.239 --> 00:04:16.629 align:start position:0%
html or markdown you can use the wysiwyg
editor<00:04:14.560><c> slide.com</c><00:04:15.599><c> and</c><00:04:15.680><c> that</c><00:04:15.920><c> would</c><00:04:16.160><c> be</c><00:04:16.400><c> both</c>

00:04:16.629 --> 00:04:16.639 align:start position:0%
editor slide.com and that would be both
 

00:04:16.639 --> 00:04:18.390 align:start position:0%
editor slide.com and that would be both
for<00:04:16.799><c> creating</c><00:04:17.199><c> and</c><00:04:17.440><c> hosting</c><00:04:17.759><c> the</c><00:04:17.919><c> slides</c><00:04:18.239><c> so</c>

00:04:18.390 --> 00:04:18.400 align:start position:0%
for creating and hosting the slides so
 

00:04:18.400 --> 00:04:20.390 align:start position:0%
for creating and hosting the slides so
that<00:04:18.560><c> is</c><00:04:18.720><c> an</c><00:04:18.880><c> option</c><00:04:19.440><c> i</c><00:04:19.600><c> do</c><00:04:19.759><c> think</c><00:04:20.000><c> html</c><00:04:20.320><c> is</c>

00:04:20.390 --> 00:04:20.400 align:start position:0%
that is an option i do think html is
 

00:04:20.400 --> 00:04:22.310 align:start position:0%
that is an option i do think html is
really<00:04:20.639><c> fun</c><00:04:21.359><c> if</c><00:04:21.519><c> you</c><00:04:21.600><c> want</c><00:04:21.680><c> to</c><00:04:21.759><c> get</c><00:04:21.919><c> into</c><00:04:22.160><c> it</c>

00:04:22.310 --> 00:04:22.320 align:start position:0%
really fun if you want to get into it
 

00:04:22.320 --> 00:04:23.670 align:start position:0%
really fun if you want to get into it
but<00:04:22.479><c> you</c><00:04:22.560><c> don't</c><00:04:22.720><c> have</c><00:04:22.960><c> to</c>

00:04:23.670 --> 00:04:23.680 align:start position:0%
but you don't have to
 

00:04:23.680 --> 00:04:25.670 align:start position:0%
but you don't have to
and<00:04:24.000><c> if</c><00:04:24.080><c> you</c><00:04:24.160><c> need</c><00:04:24.320><c> inspiration</c><00:04:25.360><c> here</c><00:04:25.520><c> are</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
and if you need inspiration here are
 

00:04:25.680 --> 00:04:28.150 align:start position:0%
and if you need inspiration here are
some<00:04:25.840><c> slide</c><00:04:26.160><c> decks</c><00:04:26.400><c> that</c><00:04:26.560><c> i've</c><00:04:26.720><c> made</c><00:04:27.280><c> and</c><00:04:27.520><c> i</c><00:04:28.000><c> if</c>

00:04:28.150 --> 00:04:28.160 align:start position:0%
some slide decks that i've made and i if
 

00:04:28.160 --> 00:04:30.150 align:start position:0%
some slide decks that i've made and i if
you<00:04:28.400><c> yourself</c><00:04:28.720><c> have</c><00:04:29.040><c> made</c><00:04:29.280><c> some</c><00:04:29.520><c> slides</c><00:04:30.080><c> you</c>

00:04:30.150 --> 00:04:30.160 align:start position:0%
you yourself have made some slides you
 

00:04:30.160 --> 00:04:31.510 align:start position:0%
you yourself have made some slides you
know<00:04:30.320><c> let</c><00:04:30.479><c> me</c><00:04:30.560><c> know</c><00:04:30.800><c> we</c><00:04:30.960><c> could</c><00:04:31.120><c> add</c><00:04:31.360><c> more</c>

00:04:31.510 --> 00:04:31.520 align:start position:0%
know let me know we could add more
 

00:04:31.520 --> 00:04:33.189 align:start position:0%
know let me know we could add more
examples<00:04:31.919><c> so</c><00:04:32.000><c> that</c><00:04:32.160><c> others</c><00:04:32.400><c> can</c><00:04:32.560><c> be</c><00:04:32.639><c> inspired</c>

00:04:33.189 --> 00:04:33.199 align:start position:0%
examples so that others can be inspired
 

00:04:33.199 --> 00:04:34.950 align:start position:0%
examples so that others can be inspired
so<00:04:33.360><c> we</c><00:04:33.520><c> can</c><00:04:33.600><c> build</c><00:04:33.759><c> an</c><00:04:33.919><c> ecosystem</c><00:04:34.479><c> of</c><00:04:34.639><c> cs</c>

00:04:34.950 --> 00:04:34.960 align:start position:0%
so we can build an ecosystem of cs
 

00:04:34.960 --> 00:04:36.390 align:start position:0%
so we can build an ecosystem of cs
teachers<00:04:35.360><c> that</c><00:04:35.520><c> are</c><00:04:35.600><c> making</c><00:04:35.919><c> these</c>

00:04:36.390 --> 00:04:36.400 align:start position:0%
teachers that are making these
 

00:04:36.400 --> 00:04:38.230 align:start position:0%
teachers that are making these
interactive<00:04:37.199><c> accessible</c><00:04:37.680><c> slides</c><00:04:38.080><c> for</c>

00:04:38.230 --> 00:04:38.240 align:start position:0%
interactive accessible slides for
 

00:04:38.240 --> 00:04:39.749 align:start position:0%
interactive accessible slides for
programming<00:04:38.720><c> places</c>

00:04:39.749 --> 00:04:39.759 align:start position:0%
programming places
 

00:04:39.759 --> 00:04:43.000 align:start position:0%
programming places
thank<00:04:40.000><c> you</c>

