@import "tailwindcss";

@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --ring: #3b82f6;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --border: #334155;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose {
  max-width: none;
}

.dark .prose {
  color: #d1d5db;
}

.prose p {
  margin-bottom: 1rem;
}

.prose h1,
.prose h2,
.prose h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3 {
  color: #f3f4f6;
}

.prose ul,
.prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

.prose a {
  color: #2563eb;
}

.prose a:hover {
  text-decoration: underline;
}

.dark .prose a {
  color: #60a5fa;
}
