WEBVTT
Kind: captions
Language: en

00:00:01.680 --> 00:00:05.510 align:start position:0%
 
the<00:00:01.920><c> last</c><00:00:02.240><c> video</c><00:00:02.600><c> I</c><00:00:02.760><c> put</c><00:00:02.960><c> out</c><00:00:03.320><c> was</c><00:00:04.040><c> 2</c><00:00:04.359><c> days</c><00:00:04.640><c> ago</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
the last video I put out was 2 days ago
 

00:00:05.520 --> 00:00:08.310 align:start position:0%
the last video I put out was 2 days ago
and<00:00:05.759><c> that</c><00:00:05.879><c> was</c><00:00:06.040><c> about</c><00:00:06.319><c> version</c>

00:00:08.310 --> 00:00:08.320 align:start position:0%
and that was about version
 

00:00:08.320 --> 00:00:11.830 align:start position:0%
and that was about version
0.118<00:00:09.320><c> and</c><00:00:09.480><c> now</c><00:00:09.760><c> we</c><00:00:10.000><c> have</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
0.118 and now we have
 

00:00:11.840 --> 00:00:14.589 align:start position:0%
0.118 and now we have
0.1.19<00:00:12.840><c> there</c><00:00:13.000><c> are</c><00:00:13.280><c> a</c><00:00:13.519><c> bunch</c><00:00:13.799><c> of</c><00:00:14.000><c> fixes</c><00:00:14.400><c> in</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
0.1.19 there are a bunch of fixes in
 

00:00:14.599 --> 00:00:17.390 align:start position:0%
0.1.19 there are a bunch of fixes in
this<00:00:14.719><c> one</c><00:00:15.200><c> rather</c><00:00:15.480><c> than</c><00:00:15.839><c> any</c><00:00:16.199><c> new</c><00:00:16.520><c> features</c>

00:00:17.390 --> 00:00:17.400 align:start position:0%
this one rather than any new features
 

00:00:17.400 --> 00:00:20.150 align:start position:0%
this one rather than any new features
but<00:00:17.760><c> most</c><00:00:18.000><c> of</c><00:00:18.160><c> them</c><00:00:18.400><c> are</c><00:00:18.760><c> very</c><00:00:19.160><c> important</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
but most of them are very important
 

00:00:20.160 --> 00:00:23.670 align:start position:0%
but most of them are very important
especially<00:00:21.160><c> if</c><00:00:21.320><c> you</c><00:00:21.480><c> are</c><00:00:21.920><c> affected</c><00:00:22.359><c> by</c><00:00:22.680><c> them</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
especially if you are affected by them
 

00:00:23.680 --> 00:00:25.670 align:start position:0%
especially if you are affected by them
the<00:00:23.800><c> maintainers</c><00:00:24.320><c> of</c><00:00:24.480><c> Al</c><00:00:24.640><c> Lama</c><00:00:25.240><c> have</c><00:00:25.400><c> worked</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
the maintainers of Al Lama have worked
 

00:00:25.680 --> 00:00:27.710 align:start position:0%
the maintainers of Al Lama have worked
hard<00:00:25.880><c> at</c><00:00:26.000><c> supporting</c><00:00:26.400><c> new</c><00:00:26.640><c> platforms</c><00:00:27.160><c> like</c>

00:00:27.710 --> 00:00:27.720 align:start position:0%
hard at supporting new platforms like
 

00:00:27.720 --> 00:00:29.950 align:start position:0%
hard at supporting new platforms like
AMD<00:00:28.320><c> cards</c><00:00:28.840><c> as</c><00:00:28.960><c> well</c><00:00:29.080><c> as</c><00:00:29.199><c> a</c><00:00:29.400><c> native</c><00:00:29.720><c> wind</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
AMD cards as well as a native wind
 

00:00:29.960 --> 00:00:32.389 align:start position:0%
AMD cards as well as a native wind
Windows<00:00:30.279><c> app</c><00:00:30.519><c> coming</c><00:00:30.800><c> soon</c><00:00:31.599><c> and</c><00:00:31.880><c> improvements</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
Windows app coming soon and improvements
 

00:00:32.399 --> 00:00:35.030 align:start position:0%
Windows app coming soon and improvements
to<00:00:32.520><c> the</c><00:00:32.680><c> mac</c><00:00:33.640><c> and</c><00:00:33.920><c> gradually</c><00:00:34.320><c> as</c><00:00:34.480><c> more</c><00:00:34.680><c> users</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
to the mac and gradually as more users
 

00:00:35.040 --> 00:00:37.389 align:start position:0%
to the mac and gradually as more users
have<00:00:35.200><c> started</c><00:00:35.480><c> to</c><00:00:35.640><c> work</c><00:00:35.920><c> with</c><00:00:36.079><c> it</c><00:00:36.440><c> we</c><00:00:36.559><c> saw</c><00:00:36.920><c> more</c>

00:00:37.389 --> 00:00:37.399 align:start position:0%
have started to work with it we saw more
 

00:00:37.399 --> 00:00:40.549 align:start position:0%
have started to work with it we saw more
memory<00:00:38.000><c> problems</c><00:00:38.840><c> there</c><00:00:39.040><c> are</c><00:00:39.640><c> a</c><00:00:39.800><c> number</c><00:00:40.160><c> of</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
memory problems there are a number of
 

00:00:40.559 --> 00:00:44.069 align:start position:0%
memory problems there are a number of
variations<00:00:41.280><c> on</c><00:00:42.079><c> memory</c><00:00:42.840><c> issues</c><00:00:43.680><c> many</c><00:00:43.879><c> of</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
variations on memory issues many of
 

00:00:44.079 --> 00:00:46.709 align:start position:0%
variations on memory issues many of
which<00:00:44.239><c> are</c><00:00:44.480><c> addressed</c><00:00:45.000><c> here</c><00:00:45.360><c> in</c>

00:00:46.709 --> 00:00:46.719 align:start position:0%
which are addressed here in
 

00:00:46.719 --> 00:00:49.189 align:start position:0%
which are addressed here in
0.1.19<00:00:47.719><c> so</c><00:00:48.120><c> there</c><00:00:48.239><c> are</c><00:00:48.360><c> a</c><00:00:48.440><c> few</c><00:00:48.600><c> issues</c><00:00:48.879><c> cleared</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
0.1.19 so there are a few issues cleared
 

00:00:49.199 --> 00:00:51.910 align:start position:0%
0.1.19 so there are a few issues cleared
up<00:00:49.399><c> around</c><00:00:49.800><c> Contex</c><00:00:50.320><c> size</c><00:00:50.800><c> as</c><00:00:50.960><c> well</c><00:00:51.160><c> as</c><00:00:51.520><c> out</c><00:00:51.719><c> of</c>

00:00:51.910 --> 00:00:51.920 align:start position:0%
up around Contex size as well as out of
 

00:00:51.920 --> 00:00:53.950 align:start position:0%
up around Contex size as well as out of
memory<00:00:52.280><c> errors</c><00:00:53.120><c> there</c><00:00:53.239><c> were</c><00:00:53.399><c> some</c><00:00:53.600><c> problems</c>

00:00:53.950 --> 00:00:53.960 align:start position:0%
memory errors there were some problems
 

00:00:53.960 --> 00:00:57.270 align:start position:0%
memory errors there were some problems
with<00:00:54.280><c> older</c><00:00:54.680><c> Nvidia</c><00:00:55.480><c> gpus</c><00:00:56.480><c> some</c><00:00:56.760><c> improvements</c>

00:00:57.270 --> 00:00:57.280 align:start position:0%
with older Nvidia gpus some improvements
 

00:00:57.280 --> 00:00:59.150 align:start position:0%
with older Nvidia gpus some improvements
were<00:00:57.480><c> made</c><00:00:57.719><c> to</c><00:00:57.879><c> lower</c><00:00:58.280><c> the</c><00:00:58.559><c> requirements</c><00:00:59.000><c> for</c>

00:00:59.150 --> 00:00:59.160 align:start position:0%
were made to lower the requirements for
 

00:00:59.160 --> 00:01:00.590 align:start position:0%
were made to lower the requirements for
mixol

00:01:00.590 --> 00:01:00.600 align:start position:0%
mixol
 

00:01:00.600 --> 00:01:02.229 align:start position:0%
mixol
Intel<00:01:01.000><c> Max</c><00:01:01.399><c> got</c><00:01:01.600><c> some</c><00:01:01.760><c> performance</c>

00:01:02.229 --> 00:01:02.239 align:start position:0%
Intel Max got some performance
 

00:01:02.239 --> 00:01:04.030 align:start position:0%
Intel Max got some performance
improvements<00:01:02.840><c> and</c><00:01:03.199><c> one</c><00:01:03.320><c> of</c><00:01:03.440><c> the</c><00:01:03.559><c> big</c><00:01:03.680><c> ones</c><00:01:03.879><c> for</c>

00:01:04.030 --> 00:01:04.040 align:start position:0%
improvements and one of the big ones for
 

00:01:04.040 --> 00:01:05.990 align:start position:0%
improvements and one of the big ones for
a<00:01:04.159><c> lot</c><00:01:04.280><c> of</c><00:01:04.400><c> users</c><00:01:04.960><c> of</c><00:01:05.159><c> some</c><00:01:05.320><c> of</c><00:01:05.439><c> the</c><00:01:05.560><c> community</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
a lot of users of some of the community
 

00:01:06.000 --> 00:01:08.429 align:start position:0%
a lot of users of some of the community
Integrations<00:01:07.000><c> is</c><00:01:07.159><c> support</c><00:01:07.520><c> for</c><00:01:07.920><c> browser</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
Integrations is support for browser
 

00:01:08.439 --> 00:01:12.230 align:start position:0%
Integrations is support for browser
extension<00:01:08.920><c> URLs</c><00:01:09.759><c> in</c><00:01:10.040><c> olama</c><00:01:10.960><c> Origins</c><00:01:11.960><c> and</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
extension URLs in olama Origins and
 

00:01:12.240 --> 00:01:15.390 align:start position:0%
extension URLs in olama Origins and
olama<00:01:12.840><c> will</c><00:01:13.080><c> offload</c><00:01:13.560><c> to</c><00:01:13.720><c> gpus</c><00:01:14.280><c> more</c><00:01:15.000><c> now</c>

00:01:15.390 --> 00:01:15.400 align:start position:0%
olama will offload to gpus more now
 

00:01:15.400 --> 00:01:17.670 align:start position:0%
olama will offload to gpus more now
where<00:01:15.720><c> possible</c><00:01:16.640><c> there's</c><00:01:16.880><c> also</c><00:01:17.080><c> a</c><00:01:17.200><c> new</c><00:01:17.400><c> cool</c>

00:01:17.670 --> 00:01:17.680 align:start position:0%
where possible there's also a new cool
 

00:01:17.680 --> 00:01:20.230 align:start position:0%
where possible there's also a new cool
model<00:01:17.960><c> to</c><00:01:18.200><c> take</c><00:01:18.360><c> a</c><00:01:18.479><c> look</c><00:01:18.640><c> at</c><00:01:19.600><c> one</c><00:01:19.720><c> of</c><00:01:19.880><c> the</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
model to take a look at one of the
 

00:01:20.240 --> 00:01:22.789 align:start position:0%
model to take a look at one of the
biggest<00:01:20.720><c> and</c><00:01:21.240><c> weirdest</c><00:01:21.920><c> issues</c><00:01:22.320><c> and</c><00:01:22.479><c> maybe</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
biggest and weirdest issues and maybe
 

00:01:22.799 --> 00:01:24.870 align:start position:0%
biggest and weirdest issues and maybe
the<00:01:22.960><c> most</c><00:01:23.200><c> ordinary</c><00:01:23.720><c> to</c><00:01:23.920><c> figure</c><00:01:24.200><c> out</c><00:01:24.680><c> was</c>

00:01:24.870 --> 00:01:24.880 align:start position:0%
the most ordinary to figure out was
 

00:01:24.880 --> 00:01:27.030 align:start position:0%
the most ordinary to figure out was
around<00:01:25.240><c> contact</c><00:01:25.720><c> size</c><00:01:26.479><c> there</c><00:01:26.600><c> were</c><00:01:26.840><c> new</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
around contact size there were new
 

00:01:27.040 --> 00:01:29.630 align:start position:0%
around contact size there were new
models<00:01:27.479><c> supported</c><00:01:27.960><c> that</c><00:01:28.119><c> offered</c><00:01:28.439><c> up</c><00:01:28.600><c> to</c><00:01:28.799><c> 32k</c>

00:01:29.630 --> 00:01:29.640 align:start position:0%
models supported that offered up to 32k
 

00:01:29.640 --> 00:01:32.149 align:start position:0%
models supported that offered up to 32k
or<00:01:29.880><c> or</c><00:01:30.040><c> even</c><00:01:30.280><c> bigger</c><00:01:30.720><c> contact</c><00:01:31.159><c> sizes</c>

00:01:32.149 --> 00:01:32.159 align:start position:0%
or or even bigger contact sizes
 

00:01:32.159 --> 00:01:33.789 align:start position:0%
or or even bigger contact sizes
calculating<00:01:32.640><c> the</c><00:01:32.759><c> memory</c><00:01:33.159><c> required</c><00:01:33.520><c> for</c><00:01:33.680><c> the</c>

00:01:33.789 --> 00:01:33.799 align:start position:0%
calculating the memory required for the
 

00:01:33.799 --> 00:01:37.469 align:start position:0%
calculating the memory required for the
models<00:01:34.479><c> themselves</c><00:01:34.920><c> is</c><00:01:35.560><c> pretty</c><00:01:35.880><c> easy</c><00:01:36.439><c> 7B</c><00:01:37.280><c> well</c>

00:01:37.469 --> 00:01:37.479 align:start position:0%
models themselves is pretty easy 7B well
 

00:01:37.479 --> 00:01:41.190 align:start position:0%
models themselves is pretty easy 7B well
at<00:01:37.560><c> least</c><00:01:37.880><c> 8</c><00:01:38.200><c> gigs</c><00:01:38.600><c> is</c><00:01:39.280><c> 13B</c><00:01:40.079><c> well</c><00:01:40.320><c> at</c><00:01:40.439><c> least</c><00:01:40.680><c> 16</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
at least 8 gigs is 13B well at least 16
 

00:01:41.200 --> 00:01:44.190 align:start position:0%
at least 8 gigs is 13B well at least 16
gigs<00:01:41.600><c> is</c><00:01:42.600><c> but</c><00:01:42.799><c> figuring</c><00:01:43.159><c> out</c><00:01:43.360><c> the</c><00:01:43.560><c> memory</c><00:01:43.920><c> for</c>

00:01:44.190 --> 00:01:44.200 align:start position:0%
gigs is but figuring out the memory for
 

00:01:44.200 --> 00:01:46.590 align:start position:0%
gigs is but figuring out the memory for
context<00:01:44.680><c> has</c><00:01:44.840><c> been</c><00:01:45.119><c> harder</c><00:01:46.119><c> and</c><00:01:46.200><c> so</c><00:01:46.399><c> if</c><00:01:46.479><c> you</c>

00:01:46.590 --> 00:01:46.600 align:start position:0%
context has been harder and so if you
 

00:01:46.600 --> 00:01:48.789 align:start position:0%
context has been harder and so if you
didn't<00:01:46.880><c> have</c><00:01:47.040><c> the</c><00:01:47.159><c> memory</c><00:01:47.479><c> needed</c><00:01:48.200><c> and</c><00:01:48.320><c> AMA</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
didn't have the memory needed and AMA
 

00:01:48.799 --> 00:01:50.830 align:start position:0%
didn't have the memory needed and AMA
didn't<00:01:49.079><c> expect</c><00:01:49.360><c> it</c><00:01:49.680><c> you</c><00:01:49.799><c> could</c><00:01:50.040><c> hit</c><00:01:50.280><c> a</c><00:01:50.520><c> rather</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
didn't expect it you could hit a rather
 

00:01:50.840 --> 00:01:53.550 align:start position:0%
didn't expect it you could hit a rather
severe<00:01:51.320><c> crash</c><00:01:52.079><c> either</c><00:01:52.320><c> just</c><00:01:52.520><c> Ama</c><00:01:53.119><c> or</c>

00:01:53.550 --> 00:01:53.560 align:start position:0%
severe crash either just Ama or
 

00:01:53.560 --> 00:01:56.310 align:start position:0%
severe crash either just Ama or
sometimes<00:01:54.240><c> the</c><00:01:54.439><c> whole</c><00:01:54.759><c> system</c><00:01:55.159><c> comes</c><00:01:55.479><c> down</c><00:01:56.240><c> I</c>

00:01:56.310 --> 00:01:56.320 align:start position:0%
sometimes the whole system comes down I
 

00:01:56.320 --> 00:01:59.270 align:start position:0%
sometimes the whole system comes down I
saw<00:01:56.719><c> my</c><00:01:57.039><c> MacBook</c><00:01:57.880><c> reboot</c><00:01:58.520><c> a</c><00:01:58.640><c> few</c><00:01:58.920><c> times</c>

00:01:59.270 --> 00:01:59.280 align:start position:0%
saw my MacBook reboot a few times
 

00:01:59.280 --> 00:02:01.990 align:start position:0%
saw my MacBook reboot a few times
unexpected<00:01:59.799><c> Ed</c><00:02:00.079><c> ly</c><00:02:01.039><c> most</c><00:02:01.280><c> folks</c><00:02:01.560><c> wanted</c><00:02:01.840><c> to</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
unexpected Ed ly most folks wanted to
 

00:02:02.000 --> 00:02:04.029 align:start position:0%
unexpected Ed ly most folks wanted to
try<00:02:02.200><c> the</c><00:02:02.320><c> models</c><00:02:02.799><c> but</c><00:02:03.159><c> didn't</c><00:02:03.479><c> really</c><00:02:03.799><c> take</c>

00:02:04.029 --> 00:02:04.039 align:start position:0%
try the models but didn't really take
 

00:02:04.039 --> 00:02:06.510 align:start position:0%
try the models but didn't really take
advantage<00:02:04.640><c> of</c><00:02:04.799><c> the</c><00:02:04.960><c> bigger</c><00:02:05.360><c> context</c><00:02:06.360><c> which</c>

00:02:06.510 --> 00:02:06.520 align:start position:0%
advantage of the bigger context which
 

00:02:06.520 --> 00:02:08.350 align:start position:0%
advantage of the bigger context which
were<00:02:06.759><c> enabled</c><00:02:07.159><c> in</c><00:02:07.280><c> the</c><00:02:07.360><c> model</c><00:02:07.640><c> files</c><00:02:08.000><c> using</c><00:02:08.239><c> a</c>

00:02:08.350 --> 00:02:08.360 align:start position:0%
were enabled in the model files using a
 

00:02:08.360 --> 00:02:11.190 align:start position:0%
were enabled in the model files using a
larger<00:02:08.720><c> value</c><00:02:08.959><c> of</c><00:02:09.039><c> than</c><00:02:09.160><c> num</c><00:02:09.440><c> CTX</c><00:02:10.200><c> parameter</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
larger value of than num CTX parameter
 

00:02:11.200 --> 00:02:13.350 align:start position:0%
larger value of than num CTX parameter
so<00:02:11.560><c> a</c><00:02:11.640><c> few</c><00:02:11.840><c> weeks</c><00:02:12.080><c> ago</c><00:02:12.520><c> all</c><00:02:12.720><c> the</c><00:02:12.840><c> models</c><00:02:13.160><c> were</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
so a few weeks ago all the models were
 

00:02:13.360 --> 00:02:15.470 align:start position:0%
so a few weeks ago all the models were
updated<00:02:13.920><c> to</c><00:02:14.040><c> use</c><00:02:14.239><c> the</c><00:02:14.400><c> default</c><00:02:14.800><c> contact</c><00:02:15.200><c> size</c>

00:02:15.470 --> 00:02:15.480 align:start position:0%
updated to use the default contact size
 

00:02:15.480 --> 00:02:19.110 align:start position:0%
updated to use the default contact size
of<00:02:16.160><c> either</c><00:02:16.400><c> 2K</c><00:02:16.800><c> or</c><00:02:17.239><c> 4K</c><00:02:18.239><c> if</c><00:02:18.319><c> you</c><00:02:18.440><c> wanted</c><00:02:18.680><c> to</c><00:02:18.920><c> use</c>

00:02:19.110 --> 00:02:19.120 align:start position:0%
of either 2K or 4K if you wanted to use
 

00:02:19.120 --> 00:02:20.509 align:start position:0%
of either 2K or 4K if you wanted to use
something<00:02:19.360><c> bigger</c><00:02:19.640><c> you</c><00:02:19.760><c> could</c><00:02:19.920><c> force</c><00:02:20.360><c> a</c>

00:02:20.509 --> 00:02:20.519 align:start position:0%
something bigger you could force a
 

00:02:20.519 --> 00:02:23.030 align:start position:0%
something bigger you could force a
larger<00:02:20.920><c> contacts</c><00:02:21.360><c> using</c><00:02:21.640><c> the</c><00:02:21.840><c> numb</c><00:02:22.200><c> CTX</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
larger contacts using the numb CTX
 

00:02:23.040 --> 00:02:25.869 align:start position:0%
larger contacts using the numb CTX
option<00:02:24.000><c> only</c><00:02:24.360><c> problem</c><00:02:24.640><c> was</c><00:02:24.840><c> that</c><00:02:25.040><c> it</c><00:02:25.560><c> didn't</c>

00:02:25.869 --> 00:02:25.879 align:start position:0%
option only problem was that it didn't
 

00:02:25.879 --> 00:02:28.550 align:start position:0%
option only problem was that it didn't
really<00:02:26.280><c> always</c><00:02:26.720><c> work</c><00:02:27.120><c> right</c><00:02:27.879><c> well</c><00:02:28.200><c> now</c><00:02:28.360><c> it</c>

00:02:28.550 --> 00:02:28.560 align:start position:0%
really always work right well now it
 

00:02:28.560 --> 00:02:30.949 align:start position:0%
really always work right well now it
does<00:02:29.400><c> let's</c><00:02:29.560><c> take</c><00:02:29.920><c> a</c><00:02:30.000><c> look</c><00:02:30.120><c> at</c><00:02:30.239><c> how</c><00:02:30.360><c> to</c><00:02:30.519><c> set</c><00:02:30.800><c> the</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
does let's take a look at how to set the
 

00:02:30.959 --> 00:02:33.550 align:start position:0%
does let's take a look at how to set the
contact<00:02:31.440><c> size</c><00:02:31.920><c> first</c><00:02:32.200><c> in</c><00:02:32.319><c> the</c><00:02:32.480><c> CLI</c><00:02:32.959><c> or</c><00:02:33.160><c> reppel</c>

00:02:33.550 --> 00:02:33.560 align:start position:0%
contact size first in the CLI or reppel
 

00:02:33.560 --> 00:02:35.710 align:start position:0%
contact size first in the CLI or reppel
so<00:02:33.760><c> we'll</c><00:02:33.959><c> use</c><00:02:34.239><c> mistol</c><00:02:35.040><c> this</c><00:02:35.160><c> is</c><00:02:35.319><c> one</c><00:02:35.519><c> that</c>

00:02:35.710 --> 00:02:35.720 align:start position:0%
so we'll use mistol this is one that
 

00:02:35.720 --> 00:02:39.670 align:start position:0%
so we'll use mistol this is one that
accepts<00:02:36.040><c> a</c><00:02:36.160><c> 32k</c><00:02:37.000><c> contact</c><00:02:37.440><c> size</c><00:02:38.319><c> olama</c><00:02:39.080><c> run</c>

00:02:39.670 --> 00:02:39.680 align:start position:0%
accepts a 32k contact size olama run
 

00:02:39.680 --> 00:02:42.869 align:start position:0%
accepts a 32k contact size olama run
mistol<00:02:40.560><c> according</c><00:02:40.879><c> to</c><00:02:41.040><c> the</c><00:02:41.159><c> olama</c><00:02:41.720><c> AI</c><00:02:42.280><c> Library</c>

00:02:42.869 --> 00:02:42.879 align:start position:0%
mistol according to the olama AI Library
 

00:02:42.879 --> 00:02:45.589 align:start position:0%
mistol according to the olama AI Library
this<00:02:43.000><c> model</c><00:02:43.480><c> is</c><00:02:43.680><c> version</c><00:02:44.120><c> 0.2</c><00:02:45.120><c> so</c><00:02:45.280><c> going</c><00:02:45.480><c> to</c>

00:02:45.589 --> 00:02:45.599 align:start position:0%
this model is version 0.2 so going to
 

00:02:45.599 --> 00:02:47.910 align:start position:0%
this model is version 0.2 so going to
the<00:02:45.800><c> source</c><00:02:46.040><c> repo</c><00:02:46.560><c> in</c><00:02:46.680><c> hug</c><00:02:46.879><c> and</c><00:02:47.080><c> face</c><00:02:47.599><c> I</c><00:02:47.680><c> saw</c>

00:02:47.910 --> 00:02:47.920 align:start position:0%
the source repo in hug and face I saw
 

00:02:47.920 --> 00:02:49.830 align:start position:0%
the source repo in hug and face I saw
the<00:02:48.040><c> initial</c><00:02:48.360><c> version</c><00:02:48.599><c> of</c><00:02:48.720><c> mistol</c><00:02:49.120><c> had</c><00:02:49.239><c> an</c><00:02:49.319><c> 8K</c>

00:02:49.830 --> 00:02:49.840 align:start position:0%
the initial version of mistol had an 8K
 

00:02:49.840 --> 00:02:53.390 align:start position:0%
the initial version of mistol had an 8K
context<00:02:50.640><c> but</c><00:02:50.800><c> V2</c><00:02:51.319><c> has</c><00:02:51.440><c> a</c><00:02:51.680><c> 32k</c><00:02:52.480><c> context</c><00:02:53.239><c> so</c>

00:02:53.390 --> 00:02:53.400 align:start position:0%
context but V2 has a 32k context so
 

00:02:53.400 --> 00:02:55.589 align:start position:0%
context but V2 has a 32k context so
let's<00:02:53.560><c> go</c><00:02:53.680><c> ahead</c><00:02:53.920><c> and</c><00:02:54.040><c> use</c><00:02:54.239><c> that</c><00:02:54.480><c> now</c><00:02:55.239><c> type</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
let's go ahead and use that now type
 

00:02:55.599 --> 00:02:57.869 align:start position:0%
let's go ahead and use that now type
slash<00:02:56.080><c> question</c><00:02:56.440><c> mark</c><00:02:56.959><c> and</c><00:02:57.080><c> we</c><00:02:57.159><c> see</c><00:02:57.360><c> the</c><00:02:57.480><c> AMA</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
slash question mark and we see the AMA
 

00:02:57.879 --> 00:03:00.630 align:start position:0%
slash question mark and we see the AMA
help<00:02:58.120><c> screen</c><00:02:58.640><c> SL</c><00:02:59.040><c> question</c><00:02:59.319><c> mark</c><00:02:59.519><c> and</c><00:02:59.920><c> set</c><00:03:00.560><c> and</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
help screen SL question mark and set and
 

00:03:00.640 --> 00:03:02.309 align:start position:0%
help screen SL question mark and set and
we<00:03:00.720><c> saw</c><00:03:01.120><c> all</c><00:03:01.280><c> the</c><00:03:01.440><c> things</c><00:03:01.640><c> that</c><00:03:01.760><c> we</c><00:03:01.879><c> can</c><00:03:02.040><c> set</c>

00:03:02.309 --> 00:03:02.319 align:start position:0%
we saw all the things that we can set
 

00:03:02.319 --> 00:03:04.229 align:start position:0%
we saw all the things that we can set
here<00:03:02.480><c> in</c><00:03:02.599><c> the</c><00:03:02.720><c> reppel</c><00:03:03.560><c> I</c><00:03:03.640><c> want</c><00:03:03.760><c> to</c><00:03:03.879><c> set</c><00:03:04.040><c> a</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
here in the reppel I want to set a
 

00:03:04.239 --> 00:03:09.670 align:start position:0%
here in the reppel I want to set a
parameter<00:03:04.760><c> so/</c><00:03:05.720><c> set</c><00:03:06.280><c> parameter</c><00:03:07.040><c> numor</c><00:03:08.000><c> CTX</c>

00:03:09.670 --> 00:03:09.680 align:start position:0%
parameter so/ set parameter numor CTX
 

00:03:09.680 --> 00:03:12.309 align:start position:0%
parameter so/ set parameter numor CTX
32768<00:03:10.680><c> and</c><00:03:10.840><c> press</c><00:03:11.080><c> enter</c><00:03:11.840><c> I</c><00:03:11.920><c> don't</c><00:03:12.040><c> have</c><00:03:12.159><c> an</c>

00:03:12.309 --> 00:03:12.319 align:start position:0%
32768 and press enter I don't have an
 

00:03:12.319 --> 00:03:15.430 align:start position:0%
32768 and press enter I don't have an
example<00:03:12.840><c> large</c><00:03:13.400><c> context</c><00:03:13.920><c> to</c><00:03:14.159><c> use</c><00:03:14.959><c> but</c><00:03:15.200><c> this</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
example large context to use but this
 

00:03:15.440 --> 00:03:17.670 align:start position:0%
example large context to use but this
now<00:03:15.840><c> actually</c><00:03:16.080><c> works</c><00:03:16.879><c> though</c><00:03:17.080><c> you</c><00:03:17.360><c> definitely</c>

00:03:17.670 --> 00:03:17.680 align:start position:0%
now actually works though you definitely
 

00:03:17.680 --> 00:03:19.710 align:start position:0%
now actually works though you definitely
are<00:03:17.840><c> going</c><00:03:18.000><c> to</c><00:03:18.159><c> need</c><00:03:18.400><c> some</c><00:03:18.599><c> memory</c><00:03:19.120><c> to</c><00:03:19.360><c> handle</c>

00:03:19.710 --> 00:03:19.720 align:start position:0%
are going to need some memory to handle
 

00:03:19.720 --> 00:03:22.149 align:start position:0%
are going to need some memory to handle
this<00:03:20.440><c> and</c><00:03:20.560><c> now</c><00:03:20.720><c> let's</c><00:03:20.920><c> use</c><00:03:21.159><c> the</c><00:03:21.319><c> API</c><00:03:21.760><c> to</c><00:03:21.879><c> set</c>

00:03:22.149 --> 00:03:22.159 align:start position:0%
this and now let's use the API to set
 

00:03:22.159 --> 00:03:24.430 align:start position:0%
this and now let's use the API to set
the<00:03:22.319><c> size</c><00:03:22.959><c> I'm</c><00:03:23.080><c> in</c><00:03:23.239><c> vs</c><00:03:23.599><c> code</c><00:03:23.879><c> and</c><00:03:24.000><c> I</c><00:03:24.120><c> find</c><00:03:24.319><c> the</c>

00:03:24.430 --> 00:03:24.440 align:start position:0%
the size I'm in vs code and I find the
 

00:03:24.440 --> 00:03:26.710 align:start position:0%
the size I'm in vs code and I find the
easiest<00:03:24.799><c> way</c><00:03:24.920><c> to</c><00:03:25.080><c> test</c><00:03:25.319><c> rest</c><00:03:25.640><c> endpoints</c><00:03:26.480><c> is</c><00:03:26.560><c> to</c>

00:03:26.710 --> 00:03:26.720 align:start position:0%
easiest way to test rest endpoints is to
 

00:03:26.720 --> 00:03:30.190 align:start position:0%
easiest way to test rest endpoints is to
use<00:03:26.879><c> the</c><00:03:27.040><c> rest</c><00:03:27.319><c> client</c><00:03:27.680><c> from</c><00:03:28.319><c> whatow</c><00:03:29.319><c> Mau</c>

00:03:30.190 --> 00:03:30.200 align:start position:0%
use the rest client from whatow Mau
 

00:03:30.200 --> 00:03:32.550 align:start position:0%
use the rest client from whatow Mau
it's<00:03:30.360><c> at</c><00:03:30.519><c> 4</c><00:03:30.720><c> million</c><00:03:31.080><c> download</c><00:03:31.560><c> so</c><00:03:31.879><c> I</c><00:03:31.959><c> am</c><00:03:32.239><c> not</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
it's at 4 million download so I am not
 

00:03:32.560 --> 00:03:36.270 align:start position:0%
it's at 4 million download so I am not
alone<00:03:33.519><c> so</c><00:03:33.920><c> post</c><00:03:34.680><c> to</c><00:03:34.879><c> the</c><00:03:35.080><c> API</c><00:03:35.680><c> generate</c>

00:03:36.270 --> 00:03:36.280 align:start position:0%
alone so post to the API generate
 

00:03:36.280 --> 00:03:39.070 align:start position:0%
alone so post to the API generate
endpoint<00:03:37.280><c> the</c><00:03:37.439><c> next</c><00:03:37.720><c> line</c><00:03:37.920><c> is</c><00:03:38.000><c> for</c><00:03:38.200><c> headers</c><00:03:38.840><c> so</c>

00:03:39.070 --> 00:03:39.080 align:start position:0%
endpoint the next line is for headers so
 

00:03:39.080 --> 00:03:41.630 align:start position:0%
endpoint the next line is for headers so
skip<00:03:39.319><c> a</c><00:03:39.480><c> line</c><00:03:39.840><c> and</c><00:03:40.040><c> add</c><00:03:40.280><c> your</c><00:03:40.519><c> object</c><00:03:41.280><c> I'll</c><00:03:41.439><c> set</c>

00:03:41.630 --> 00:03:41.640 align:start position:0%
skip a line and add your object I'll set
 

00:03:41.640 --> 00:03:44.750 align:start position:0%
skip a line and add your object I'll set
a<00:03:41.760><c> model</c><00:03:42.280><c> to</c><00:03:42.599><c> mistol</c><00:03:43.439><c> prompt</c><00:03:43.799><c> to</c><00:03:44.319><c> why</c><00:03:44.480><c> is</c><00:03:44.599><c> the</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
a model to mistol prompt to why is the
 

00:03:44.760 --> 00:03:47.030 align:start position:0%
a model to mistol prompt to why is the
sky<00:03:45.040><c> blue</c><00:03:45.720><c> stream</c><00:03:46.040><c> to</c><00:03:46.239><c> false</c><00:03:46.599><c> just</c><00:03:46.720><c> to</c><00:03:46.879><c> make</c>

00:03:47.030 --> 00:03:47.040 align:start position:0%
sky blue stream to false just to make
 

00:03:47.040 --> 00:03:49.270 align:start position:0%
sky blue stream to false just to make
this<00:03:47.200><c> easier</c><00:03:47.959><c> and</c><00:03:48.120><c> then</c><00:03:48.400><c> add</c><00:03:48.560><c> an</c><00:03:48.840><c> options</c>

00:03:49.270 --> 00:03:49.280 align:start position:0%
this easier and then add an options
 

00:03:49.280 --> 00:03:51.910 align:start position:0%
this easier and then add an options
object<00:03:49.799><c> with</c><00:03:50.000><c> num</c><00:03:50.360><c> CTX</c><00:03:50.920><c> set</c><00:03:51.120><c> to</c>

00:03:51.910 --> 00:03:51.920 align:start position:0%
object with num CTX set to
 

00:03:51.920 --> 00:03:54.350 align:start position:0%
object with num CTX set to
32768<00:03:52.920><c> send</c><00:03:53.239><c> that</c><00:03:53.400><c> request</c><00:03:53.760><c> and</c><00:03:53.879><c> we</c><00:03:54.000><c> see</c><00:03:54.200><c> that</c>

00:03:54.350 --> 00:03:54.360 align:start position:0%
32768 send that request and we see that
 

00:03:54.360 --> 00:03:57.429 align:start position:0%
32768 send that request and we see that
it<00:03:54.840><c> processed</c><00:03:55.840><c> the</c><00:03:56.040><c> last</c><00:03:56.280><c> example</c><00:03:56.640><c> I'll</c><00:03:56.799><c> do</c><00:03:57.200><c> is</c>

00:03:57.429 --> 00:03:57.439 align:start position:0%
it processed the last example I'll do is
 

00:03:57.439 --> 00:03:59.069 align:start position:0%
it processed the last example I'll do is
I<00:03:57.560><c> want</c><00:03:57.680><c> to</c><00:03:57.799><c> create</c><00:03:58.000><c> a</c><00:03:58.079><c> new</c><00:03:58.280><c> model</c><00:03:58.599><c> that</c><00:03:58.799><c> always</c>

00:03:59.069 --> 00:03:59.079 align:start position:0%
I want to create a new model that always
 

00:03:59.079 --> 00:04:01.270 align:start position:0%
I want to create a new model that always
has<00:03:59.280><c> that</c><00:03:59.400><c> set</c><00:03:59.760><c> that</c><00:04:00.200><c> so</c><00:04:00.360><c> in</c><00:04:00.480><c> a</c><00:04:00.599><c> new</c><00:04:00.799><c> directory</c>

00:04:01.270 --> 00:04:01.280 align:start position:0%
has that set that so in a new directory
 

00:04:01.280 --> 00:04:04.110 align:start position:0%
has that set that so in a new directory
I'll<00:04:01.400><c> create</c><00:04:01.599><c> a</c><00:04:01.680><c> model</c><00:04:02.000><c> file</c><00:04:02.799><c> from</c><00:04:03.280><c> mistl</c>

00:04:04.110 --> 00:04:04.120 align:start position:0%
I'll create a model file from mistl
 

00:04:04.120 --> 00:04:05.990 align:start position:0%
I'll create a model file from mistl
parameter<00:04:04.640><c> num</c><00:04:04.920><c> CTX</c>

00:04:05.990 --> 00:04:06.000 align:start position:0%
parameter num CTX
 

00:04:06.000 --> 00:04:07.830 align:start position:0%
parameter num CTX
32768<00:04:07.000><c> and</c><00:04:07.120><c> since</c><00:04:07.319><c> that's</c><00:04:07.480><c> all</c><00:04:07.680><c> we're</c>

00:04:07.830 --> 00:04:07.840 align:start position:0%
32768 and since that's all we're
 

00:04:07.840 --> 00:04:10.110 align:start position:0%
32768 and since that's all we're
changing<00:04:08.400><c> that's</c><00:04:08.560><c> all</c><00:04:08.720><c> we</c><00:04:08.840><c> need</c><00:04:09.640><c> now</c><00:04:09.840><c> back</c><00:04:09.959><c> at</c>

00:04:10.110 --> 00:04:10.120 align:start position:0%
changing that's all we need now back at
 

00:04:10.120 --> 00:04:12.670 align:start position:0%
changing that's all we need now back at
the<00:04:10.239><c> command</c><00:04:10.599><c> line</c><00:04:10.840><c> I</c><00:04:10.920><c> can</c><00:04:11.040><c> run</c><00:04:11.319><c> olama</c><00:04:11.959><c> create</c>

00:04:12.670 --> 00:04:12.680 align:start position:0%
the command line I can run olama create
 

00:04:12.680 --> 00:04:15.429 align:start position:0%
the command line I can run olama create
big<00:04:12.920><c> mistl</c><00:04:13.840><c> normally</c><00:04:14.200><c> I</c><00:04:14.280><c> would</c><00:04:14.439><c> need</c><00:04:14.560><c> to</c><00:04:14.720><c> say-</c>

00:04:15.429 --> 00:04:15.439 align:start position:0%
big mistl normally I would need to say-
 

00:04:15.439 --> 00:04:17.870 align:start position:0%
big mistl normally I would need to say-
F<00:04:15.840><c> model</c><00:04:16.239><c> file</c><00:04:16.639><c> but</c><00:04:17.040><c> if</c><00:04:17.160><c> we're</c><00:04:17.320><c> in</c><00:04:17.440><c> the</c><00:04:17.600><c> same</c>

00:04:17.870 --> 00:04:17.880 align:start position:0%
F model file but if we're in the same
 

00:04:17.880 --> 00:04:20.030 align:start position:0%
F model file but if we're in the same
directory<00:04:18.359><c> and</c><00:04:18.479><c> it's</c><00:04:18.639><c> called</c><00:04:18.919><c> Model</c><00:04:19.280><c> file</c><00:04:19.919><c> we</c>

00:04:20.030 --> 00:04:20.040 align:start position:0%
directory and it's called Model file we
 

00:04:20.040 --> 00:04:22.749 align:start position:0%
directory and it's called Model file we
can<00:04:20.239><c> skip</c><00:04:20.639><c> that</c><00:04:21.280><c> and</c><00:04:21.400><c> now</c><00:04:21.600><c> olama</c><00:04:22.160><c> run</c><00:04:22.520><c> big</c>

00:04:22.749 --> 00:04:22.759 align:start position:0%
can skip that and now olama run big
 

00:04:22.759 --> 00:04:25.670 align:start position:0%
can skip that and now olama run big
mistl<00:04:23.759><c> and</c><00:04:23.919><c> now</c><00:04:24.120><c> we</c><00:04:24.320><c> have</c><00:04:24.600><c> our</c><00:04:24.919><c> mistol</c><00:04:25.440><c> with</c><00:04:25.560><c> a</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
mistl and now we have our mistol with a
 

00:04:25.680 --> 00:04:28.110 align:start position:0%
mistl and now we have our mistol with a
big<00:04:25.919><c> context</c><00:04:26.919><c> it's</c><00:04:27.120><c> hard</c><00:04:27.320><c> to</c><00:04:27.479><c> demonstrate</c><00:04:27.960><c> the</c>

00:04:28.110 --> 00:04:28.120 align:start position:0%
big context it's hard to demonstrate the
 

00:04:28.120 --> 00:04:29.870 align:start position:0%
big context it's hard to demonstrate the
rest<00:04:28.240><c> of</c><00:04:28.400><c> the</c><00:04:28.520><c> improvements</c><00:04:29.080><c> but</c><00:04:29.240><c> I</c><00:04:29.600><c> defitely</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
rest of the improvements but I defitely
 

00:04:29.880 --> 00:04:32.310 align:start position:0%
rest of the improvements but I defitely
want<00:04:30.039><c> to</c><00:04:30.360><c> create</c><00:04:30.720><c> a</c><00:04:30.919><c> video</c><00:04:31.639><c> that</c><00:04:31.759><c> shows</c><00:04:32.120><c> the</c>

00:04:32.310 --> 00:04:32.320 align:start position:0%
want to create a video that shows the
 

00:04:32.320 --> 00:04:34.670 align:start position:0%
want to create a video that shows the
benefit<00:04:32.720><c> of</c><00:04:32.840><c> a</c><00:04:32.960><c> larger</c><00:04:33.400><c> context</c><00:04:34.240><c> as</c><00:04:34.360><c> well</c><00:04:34.520><c> as</c>

00:04:34.670 --> 00:04:34.680 align:start position:0%
benefit of a larger context as well as
 

00:04:34.680 --> 00:04:36.870 align:start position:0%
benefit of a larger context as well as
its<00:04:34.960><c> impact</c><00:04:35.320><c> on</c><00:04:35.520><c> memory</c><00:04:35.880><c> usage</c><00:04:36.560><c> if</c><00:04:36.720><c> that's</c>

00:04:36.870 --> 00:04:36.880 align:start position:0%
its impact on memory usage if that's
 

00:04:36.880 --> 00:04:38.390 align:start position:0%
its impact on memory usage if that's
something<00:04:37.120><c> you're</c><00:04:37.320><c> interested</c><00:04:37.800><c> in</c><00:04:38.039><c> or</c><00:04:38.199><c> you'd</c>

00:04:38.390 --> 00:04:38.400 align:start position:0%
something you're interested in or you'd
 

00:04:38.400 --> 00:04:40.350 align:start position:0%
something you're interested in or you'd
like<00:04:38.560><c> to</c><00:04:38.639><c> see</c><00:04:38.919><c> anything</c><00:04:39.240><c> else</c><00:04:39.840><c> let</c><00:04:39.960><c> me</c><00:04:40.120><c> know</c><00:04:40.240><c> in</c>

00:04:40.350 --> 00:04:40.360 align:start position:0%
like to see anything else let me know in
 

00:04:40.360 --> 00:04:42.629 align:start position:0%
like to see anything else let me know in
the<00:04:40.520><c> comments</c><00:04:40.919><c> below</c><00:04:41.440><c> I'd</c><00:04:41.639><c> also</c><00:04:41.840><c> love</c><00:04:42.000><c> to</c><00:04:42.199><c> hear</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
the comments below I'd also love to hear
 

00:04:42.639 --> 00:04:44.550 align:start position:0%
the comments below I'd also love to hear
about<00:04:42.919><c> how</c><00:04:43.120><c> some</c><00:04:43.240><c> of</c><00:04:43.400><c> these</c><00:04:43.520><c> fixes</c><00:04:44.120><c> affected</c>

00:04:44.550 --> 00:04:44.560 align:start position:0%
about how some of these fixes affected
 

00:04:44.560 --> 00:04:49.670 align:start position:0%
about how some of these fixes affected
you<00:04:45.280><c> thanks</c><00:04:45.479><c> so</c><00:04:45.639><c> much</c><00:04:45.800><c> for</c><00:04:46.000><c> watching</c>

00:04:49.670 --> 00:04:49.680 align:start position:0%
 
 

00:04:49.680 --> 00:04:52.680 align:start position:0%
 
goodbye

