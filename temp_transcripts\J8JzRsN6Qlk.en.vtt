WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.350 align:start position:0%
 
if<00:00:00.199><c> you're</c><00:00:00.399><c> thinking</c><00:00:00.680><c> about</c><00:00:00.880><c> moving</c><00:00:01.160><c> to</c>

00:00:01.350 --> 00:00:01.360 align:start position:0%
if you're thinking about moving to
 

00:00:01.360 --> 00:00:03.510 align:start position:0%
if you're thinking about moving to
GitHub<00:00:02.040><c> you</c><00:00:02.280><c> likely</c><00:00:02.639><c> have</c><00:00:02.840><c> some</c><00:00:03.080><c> existing</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
GitHub you likely have some existing
 

00:00:03.520 --> 00:00:05.869 align:start position:0%
GitHub you likely have some existing
data<00:00:03.879><c> that</c><00:00:04.040><c> you</c><00:00:04.200><c> want</c><00:00:04.359><c> to</c><00:00:04.560><c> bring</c><00:00:04.880><c> with</c><00:00:05.080><c> you</c><00:00:05.600><c> so</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
data that you want to bring with you so
 

00:00:05.879 --> 00:00:08.390 align:start position:0%
data that you want to bring with you so
your<00:00:06.040><c> team</c><00:00:06.319><c> can</c><00:00:06.480><c> hit</c><00:00:06.640><c> the</c><00:00:06.759><c> ground</c><00:00:07.400><c> running</c>

00:00:08.390 --> 00:00:08.400 align:start position:0%
your team can hit the ground running
 

00:00:08.400 --> 00:00:10.430 align:start position:0%
your team can hit the ground running
well<00:00:08.679><c> you're</c><00:00:08.880><c> in</c><00:00:09.080><c> luck</c><00:00:09.559><c> because</c><00:00:09.920><c> we've</c><00:00:10.200><c> got</c>

00:00:10.430 --> 00:00:10.440 align:start position:0%
well you're in luck because we've got
 

00:00:10.440 --> 00:00:13.230 align:start position:0%
well you're in luck because we've got
tools<00:00:11.040><c> and</c><00:00:11.320><c> guidance</c><00:00:11.759><c> to</c><00:00:11.960><c> help</c><00:00:12.200><c> you</c><00:00:12.639><c> to</c><00:00:12.880><c> adopt</c>

00:00:13.230 --> 00:00:13.240 align:start position:0%
tools and guidance to help you to adopt
 

00:00:13.240 --> 00:00:15.270 align:start position:0%
tools and guidance to help you to adopt
GitHub<00:00:13.960><c> and</c><00:00:14.120><c> to</c><00:00:14.280><c> make</c><00:00:14.440><c> your</c><00:00:14.599><c> migration</c><00:00:15.120><c> and</c>

00:00:15.270 --> 00:00:15.280 align:start position:0%
GitHub and to make your migration and
 

00:00:15.280 --> 00:00:17.990 align:start position:0%
GitHub and to make your migration and
setup<00:00:15.679><c> process</c><00:00:16.400><c> as</c><00:00:16.640><c> quick</c><00:00:17.160><c> and</c><00:00:17.359><c> painless</c><00:00:17.800><c> as</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
setup process as quick and painless as
 

00:00:18.000 --> 00:00:20.230 align:start position:0%
setup process as quick and painless as
possible<00:00:18.760><c> no</c><00:00:18.920><c> matter</c><00:00:19.279><c> how</c><00:00:19.560><c> big</c><00:00:19.960><c> your</c>

00:00:20.230 --> 00:00:20.240 align:start position:0%
possible no matter how big your
 

00:00:20.240 --> 00:00:21.470 align:start position:0%
possible no matter how big your
organization

00:00:21.470 --> 00:00:21.480 align:start position:0%
organization
 

00:00:21.480 --> 00:00:24.630 align:start position:0%
organization
is<00:00:22.480><c> so</c><00:00:22.680><c> I'm</c><00:00:22.840><c> going</c><00:00:23.000><c> to</c><00:00:23.199><c> assume</c><00:00:23.680><c> you</c><00:00:24.279><c> understand</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
is so I'm going to assume you understand
 

00:00:24.640 --> 00:00:27.550 align:start position:0%
is so I'm going to assume you understand
the<00:00:24.840><c> concept</c><00:00:25.400><c> of</c><00:00:25.560><c> a</c><00:00:25.920><c> migration</c><00:00:26.920><c> moving</c><00:00:27.359><c> your</c>

00:00:27.550 --> 00:00:27.560 align:start position:0%
the concept of a migration moving your
 

00:00:27.560 --> 00:00:31.070 align:start position:0%
the concept of a migration moving your
existing<00:00:27.960><c> work</c><00:00:28.320><c> from</c><00:00:28.679><c> one</c><00:00:28.960><c> place</c><00:00:29.480><c> to</c><00:00:29.679><c> another</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
existing work from one place to another
 

00:00:31.080 --> 00:00:32.429 align:start position:0%
existing work from one place to another
however<00:00:31.279><c> there</c><00:00:31.400><c> are</c><00:00:31.599><c> different</c><00:00:31.920><c> ways</c><00:00:32.279><c> that</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
however there are different ways that
 

00:00:32.439 --> 00:00:34.910 align:start position:0%
however there are different ways that
you<00:00:32.599><c> can</c><00:00:32.840><c> make</c><00:00:33.120><c> that</c><00:00:33.440><c> transition</c><00:00:34.440><c> each</c><00:00:34.680><c> way</c>

00:00:34.910 --> 00:00:34.920 align:start position:0%
you can make that transition each way
 

00:00:34.920 --> 00:00:36.950 align:start position:0%
you can make that transition each way
will<00:00:35.120><c> have</c><00:00:35.360><c> pros</c><00:00:35.680><c> and</c><00:00:35.879><c> cons</c><00:00:36.559><c> which</c><00:00:36.719><c> will</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
will have pros and cons which will
 

00:00:36.960 --> 00:00:39.470 align:start position:0%
will have pros and cons which will
differ<00:00:37.320><c> based</c><00:00:37.600><c> upon</c><00:00:37.800><c> your</c><00:00:38.160><c> circumstances</c><00:00:39.160><c> and</c>

00:00:39.470 --> 00:00:39.480 align:start position:0%
differ based upon your circumstances and
 

00:00:39.480 --> 00:00:41.950 align:start position:0%
differ based upon your circumstances and
exact<00:00:39.840><c> set</c><00:00:40.039><c> of</c><00:00:40.640><c> requirements</c><00:00:41.640><c> now</c><00:00:41.800><c> when</c>

00:00:41.950 --> 00:00:41.960 align:start position:0%
exact set of requirements now when
 

00:00:41.960 --> 00:00:44.110 align:start position:0%
exact set of requirements now when
migrating<00:00:42.399><c> to</c><00:00:42.600><c> GitHub</c><00:00:43.160><c> you</c><00:00:43.280><c> can</c><00:00:43.520><c> think</c><00:00:43.840><c> of</c>

00:00:44.110 --> 00:00:44.120 align:start position:0%
migrating to GitHub you can think of
 

00:00:44.120 --> 00:00:45.950 align:start position:0%
migrating to GitHub you can think of
three<00:00:44.440><c> different</c><00:00:44.760><c> ways</c><00:00:45.280><c> to</c><00:00:45.440><c> move</c><00:00:45.680><c> your</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
three different ways to move your
 

00:00:45.960 --> 00:00:48.709 align:start position:0%
three different ways to move your
existing<00:00:46.320><c> work</c><00:00:46.640><c> from</c><00:00:46.879><c> place</c><00:00:47.120><c> a</c><00:00:47.640><c> the</c><00:00:47.800><c> source</c><00:00:48.480><c> to</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
existing work from place a the source to
 

00:00:48.719 --> 00:00:50.510 align:start position:0%
existing work from place a the source to
GitHub<00:00:49.440><c> the</c>

00:00:50.510 --> 00:00:50.520 align:start position:0%
GitHub the
 

00:00:50.520 --> 00:00:53.189 align:start position:0%
GitHub the
destination<00:00:51.520><c> first</c><00:00:52.079><c> a</c><00:00:52.239><c> source</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
destination first a source
 

00:00:53.199 --> 00:00:55.310 align:start position:0%
destination first a source
snapshot<00:00:54.199><c> this</c><00:00:54.399><c> method</c><00:00:54.719><c> migrates</c><00:00:55.199><c> the</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
snapshot this method migrates the
 

00:00:55.320 --> 00:00:57.110 align:start position:0%
snapshot this method migrates the
current<00:00:55.680><c> state</c><00:00:55.879><c> of</c><00:00:56.000><c> your</c><00:00:56.199><c> code</c><00:00:56.680><c> as</c><00:00:56.800><c> it</c><00:00:56.960><c> is</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
current state of your code as it is
 

00:00:57.120 --> 00:00:59.270 align:start position:0%
current state of your code as it is
today<00:00:57.559><c> but</c><00:00:57.800><c> doesn't</c><00:00:58.120><c> include</c><00:00:58.840><c> any</c><00:00:59.039><c> of</c><00:00:59.160><c> the</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
today but doesn't include any of the
 

00:00:59.280 --> 00:01:00.910 align:start position:0%
today but doesn't include any of the
revision<00:00:59.640><c> history</c>

00:01:00.910 --> 00:01:00.920 align:start position:0%
revision history
 

00:01:00.920 --> 00:01:02.549 align:start position:0%
revision history
you<00:01:01.079><c> don't</c><00:01:01.359><c> typically</c><00:01:01.760><c> need</c><00:01:02.079><c> in-depth</c>

00:01:02.549 --> 00:01:02.559 align:start position:0%
you don't typically need in-depth
 

00:01:02.559 --> 00:01:04.189 align:start position:0%
you don't typically need in-depth
specialized<00:01:03.079><c> tools</c><00:01:03.480><c> for</c><00:01:03.680><c> these</c><00:01:03.840><c> types</c><00:01:04.040><c> of</c>

00:01:04.189 --> 00:01:04.199 align:start position:0%
specialized tools for these types of
 

00:01:04.199 --> 00:01:06.750 align:start position:0%
specialized tools for these types of
migrations<00:01:05.080><c> and</c><00:01:05.239><c> can</c><00:01:05.479><c> migrate</c><00:01:06.119><c> from</c><00:01:06.479><c> other</c>

00:01:06.750 --> 00:01:06.760 align:start position:0%
migrations and can migrate from other
 

00:01:06.760 --> 00:01:09.350 align:start position:0%
migrations and can migrate from other
version<00:01:07.080><c> control</c><00:01:07.520><c> systems</c><00:01:08.280><c> or</c><00:01:08.520><c> code</c><00:01:09.000><c> that</c><00:01:09.200><c> may</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
version control systems or code that may
 

00:01:09.360 --> 00:01:11.390 align:start position:0%
version control systems or code that may
not<00:01:09.560><c> be</c><00:01:09.720><c> version</c><00:01:10.200><c> controlled</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
not be version controlled
 

00:01:11.400 --> 00:01:14.190 align:start position:0%
not be version controlled
yet<00:01:12.400><c> Second</c><00:01:13.000><c> Source</c><00:01:13.360><c> and</c>

00:01:14.190 --> 00:01:14.200 align:start position:0%
yet Second Source and
 

00:01:14.200 --> 00:01:16.670 align:start position:0%
yet Second Source and
history<00:01:15.200><c> this</c><00:01:15.439><c> method</c><00:01:15.759><c> migrates</c><00:01:16.240><c> the</c><00:01:16.360><c> current</c>

00:01:16.670 --> 00:01:16.680 align:start position:0%
history this method migrates the current
 

00:01:16.680 --> 00:01:18.670 align:start position:0%
history this method migrates the current
state<00:01:16.880><c> of</c><00:01:17.000><c> your</c><00:01:17.159><c> code</c><00:01:17.640><c> and</c><00:01:17.840><c> its</c><00:01:18.040><c> revision</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
state of your code and its revision
 

00:01:18.680 --> 00:01:20.749 align:start position:0%
state of your code and its revision
history<00:01:19.680><c> this</c><00:01:19.799><c> is</c><00:01:20.040><c> possible</c><00:01:20.400><c> if</c><00:01:20.520><c> you've</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
history this is possible if you've
 

00:01:20.759 --> 00:01:22.270 align:start position:0%
history this is possible if you've
already<00:01:21.040><c> been</c><00:01:21.240><c> tracking</c><00:01:21.560><c> your</c><00:01:21.720><c> changes</c><00:01:22.040><c> in</c>

00:01:22.270 --> 00:01:22.280 align:start position:0%
already been tracking your changes in
 

00:01:22.280 --> 00:01:25.469 align:start position:0%
already been tracking your changes in
git<00:01:22.920><c> or</c><00:01:23.439><c> another</c><00:01:24.000><c> version</c><00:01:24.360><c> control</c><00:01:24.799><c> system</c>

00:01:25.469 --> 00:01:25.479 align:start position:0%
git or another version control system
 

00:01:25.479 --> 00:01:27.230 align:start position:0%
git or another version control system
where<00:01:25.600><c> you</c><00:01:25.720><c> can</c><00:01:25.920><c> convert</c><00:01:26.360><c> the</c><00:01:26.520><c> history</c><00:01:26.840><c> into</c>

00:01:27.230 --> 00:01:27.240 align:start position:0%
where you can convert the history into
 

00:01:27.240 --> 00:01:30.950 align:start position:0%
where you can convert the history into
git<00:01:27.880><c> before</c><00:01:28.200><c> the</c><00:01:28.840><c> migration</c><00:01:30.079><c> and</c><00:01:30.360><c> finally</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
git before the migration and finally
 

00:01:30.960 --> 00:01:33.190 align:start position:0%
git before the migration and finally
Source<00:01:31.479><c> history</c><00:01:32.079><c> and</c>

00:01:33.190 --> 00:01:33.200 align:start position:0%
Source history and
 

00:01:33.200 --> 00:01:35.749 align:start position:0%
Source history and
metadata<00:01:34.200><c> this</c><00:01:34.520><c> is</c><00:01:34.680><c> the</c><00:01:34.840><c> most</c><00:01:35.119><c> complex</c><00:01:35.520><c> of</c><00:01:35.640><c> the</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
metadata this is the most complex of the
 

00:01:35.759 --> 00:01:37.310 align:start position:0%
metadata this is the most complex of the
migration<00:01:36.240><c> options</c><00:01:36.680><c> and</c><00:01:36.840><c> requires</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
migration options and requires
 

00:01:37.320 --> 00:01:39.630 align:start position:0%
migration options and requires
specialist<00:01:37.720><c> tools</c><00:01:38.200><c> which</c><00:01:38.320><c> are</c><00:01:38.560><c> not</c><00:01:38.960><c> available</c>

00:01:39.630 --> 00:01:39.640 align:start position:0%
specialist tools which are not available
 

00:01:39.640 --> 00:01:42.830 align:start position:0%
specialist tools which are not available
for<00:01:39.880><c> all</c><00:01:40.119><c> migration</c><00:01:40.759><c> paths</c><00:01:41.759><c> in</c><00:01:41.920><c> this</c><00:01:42.119><c> scenario</c>

00:01:42.830 --> 00:01:42.840 align:start position:0%
for all migration paths in this scenario
 

00:01:42.840 --> 00:01:44.630 align:start position:0%
for all migration paths in this scenario
the<00:01:43.000><c> current</c><00:01:43.280><c> state</c><00:01:43.520><c> of</c><00:01:43.680><c> your</c><00:01:43.880><c> code</c><00:01:44.360><c> its</c>

00:01:44.630 --> 00:01:44.640 align:start position:0%
the current state of your code its
 

00:01:44.640 --> 00:01:46.630 align:start position:0%
the current state of your code its
revision<00:01:45.119><c> history</c><00:01:45.920><c> and</c><00:01:46.200><c> additional</c>

00:01:46.630 --> 00:01:46.640 align:start position:0%
revision history and additional
 

00:01:46.640 --> 00:01:49.109 align:start position:0%
revision history and additional
collaborative<00:01:47.240><c> items</c><00:01:47.719><c> such</c><00:01:47.920><c> as</c><00:01:48.320><c> work</c><00:01:48.680><c> items</c>

00:01:49.109 --> 00:01:49.119 align:start position:0%
collaborative items such as work items
 

00:01:49.119 --> 00:01:51.389 align:start position:0%
collaborative items such as work items
or<00:01:49.320><c> issues</c><00:01:49.680><c> and</c><00:01:49.840><c> pull</c><00:01:50.079><c> requests</c><00:01:50.880><c> and</c>

00:01:51.389 --> 00:01:51.399 align:start position:0%
or issues and pull requests and
 

00:01:51.399 --> 00:01:53.350 align:start position:0%
or issues and pull requests and
potentially<00:01:52.000><c> some</c><00:01:52.280><c> configuration</c><00:01:52.880><c> settings</c>

00:01:53.350 --> 00:01:53.360 align:start position:0%
potentially some configuration settings
 

00:01:53.360 --> 00:01:54.230 align:start position:0%
potentially some configuration settings
can<00:01:53.479><c> be</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
can be
 

00:01:54.240 --> 00:01:56.830 align:start position:0%
can be
migrated<00:01:55.240><c> however</c><00:01:55.719><c> this</c><00:01:55.920><c> option</c><00:01:56.159><c> is</c><00:01:56.360><c> entirely</c>

00:01:56.830 --> 00:01:56.840 align:start position:0%
migrated however this option is entirely
 

00:01:56.840 --> 00:01:59.230 align:start position:0%
migrated however this option is entirely
dependent<00:01:57.399><c> on</c><00:01:57.560><c> the</c><00:01:57.719><c> migration</c><00:01:58.159><c> Tooling</c><00:01:59.079><c> in</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
dependent on the migration Tooling in
 

00:01:59.240 --> 00:02:01.510 align:start position:0%
dependent on the migration Tooling in
other<00:01:59.439><c> words</c><00:02:00.000><c> how</c><00:02:00.240><c> rich</c><00:02:00.479><c> or</c><00:02:00.680><c> High</c><00:02:00.880><c> Fidelity</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
other words how rich or High Fidelity
 

00:02:01.520 --> 00:02:03.590 align:start position:0%
other words how rich or High Fidelity
its<00:02:01.759><c> capabilities</c><00:02:02.399><c> are</c><00:02:03.000><c> and</c><00:02:03.200><c> whether</c><00:02:03.439><c> the</c>

00:02:03.590 --> 00:02:03.600 align:start position:0%
its capabilities are and whether the
 

00:02:03.600 --> 00:02:05.950 align:start position:0%
its capabilities are and whether the
migration<00:02:04.039><c> source</c><00:02:04.360><c> is</c><00:02:04.960><c> supported</c>

00:02:05.950 --> 00:02:05.960 align:start position:0%
migration source is supported
 

00:02:05.960 --> 00:02:07.870 align:start position:0%
migration source is supported
fortunately<00:02:06.640><c> we've</c><00:02:06.880><c> been</c><00:02:07.240><c> working</c><00:02:07.520><c> on</c><00:02:07.640><c> some</c>

00:02:07.870 --> 00:02:07.880 align:start position:0%
fortunately we've been working on some
 

00:02:07.880 --> 00:02:10.190 align:start position:0%
fortunately we've been working on some
tools<00:02:08.200><c> to</c><00:02:08.360><c> help</c><00:02:08.640><c> accelerate</c><00:02:09.119><c> your</c><00:02:09.319><c> migration</c>

00:02:10.190 --> 00:02:10.200 align:start position:0%
tools to help accelerate your migration
 

00:02:10.200 --> 00:02:12.910 align:start position:0%
tools to help accelerate your migration
so<00:02:10.399><c> you</c><00:02:10.520><c> can</c><00:02:10.759><c> focus</c><00:02:11.280><c> on</c><00:02:11.480><c> the</c><00:02:11.640><c> important</c><00:02:12.160><c> part</c>

00:02:12.910 --> 00:02:12.920 align:start position:0%
so you can focus on the important part
 

00:02:12.920 --> 00:02:15.830 align:start position:0%
so you can focus on the important part
enabling<00:02:13.520><c> your</c><00:02:13.760><c> teams</c><00:02:14.400><c> to</c><00:02:14.599><c> be</c><00:02:14.840><c> productive</c><00:02:15.560><c> and</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
enabling your teams to be productive and
 

00:02:15.840 --> 00:02:17.750 align:start position:0%
enabling your teams to be productive and
focusing<00:02:16.319><c> on</c><00:02:16.560><c> building</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
focusing on building
 

00:02:17.760 --> 00:02:20.589 align:start position:0%
focusing on building
software<00:02:18.760><c> so</c><00:02:18.959><c> check</c><00:02:19.120><c> out</c><00:02:19.440><c> this</c><00:02:19.640><c> link</c><00:02:20.200><c> to</c><00:02:20.360><c> find</c>

00:02:20.589 --> 00:02:20.599 align:start position:0%
software so check out this link to find
 

00:02:20.599 --> 00:02:22.229 align:start position:0%
software so check out this link to find
the<00:02:20.760><c> latest</c><00:02:21.120><c> about</c><00:02:21.400><c> GitHub</c><00:02:21.800><c> Enterprise</c>

00:02:22.229 --> 00:02:22.239 align:start position:0%
the latest about GitHub Enterprise
 

00:02:22.239 --> 00:02:24.869 align:start position:0%
the latest about GitHub Enterprise
importer<00:02:22.800><c> our</c><00:02:23.040><c> self-service</c><00:02:23.800><c> migration</c><00:02:24.200><c> tool</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
importer our self-service migration tool
 

00:02:24.879 --> 00:02:26.390 align:start position:0%
importer our self-service migration tool
which<00:02:25.040><c> supports</c><00:02:25.400><c> a</c><00:02:25.560><c> variety</c><00:02:25.959><c> of</c><00:02:26.080><c> source</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
which supports a variety of source
 

00:02:26.400 --> 00:02:29.270 align:start position:0%
which supports a variety of source
platforms<00:02:27.040><c> migrating</c><00:02:27.640><c> into</c><00:02:28.280><c> GitHub</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
platforms migrating into GitHub
 

00:02:29.280 --> 00:02:31.509 align:start position:0%
platforms migrating into GitHub
alternative<00:02:30.040><c> ly</c><00:02:30.360><c> our</c><00:02:30.640><c> GitHub</c><00:02:31.120><c> expert</c>

00:02:31.509 --> 00:02:31.519 align:start position:0%
alternative ly our GitHub expert
 

00:02:31.519 --> 00:02:33.790 align:start position:0%
alternative ly our GitHub expert
Services<00:02:31.959><c> team</c><00:02:32.280><c> may</c><00:02:32.440><c> be</c><00:02:32.560><c> able</c><00:02:32.760><c> to</c><00:02:32.920><c> help</c><00:02:33.160><c> you</c><00:02:33.560><c> in</c>

00:02:33.790 --> 00:02:33.800 align:start position:0%
Services team may be able to help you in
 

00:02:33.800 --> 00:02:35.390 align:start position:0%
Services team may be able to help you in
migrating<00:02:34.280><c> from</c><00:02:34.560><c> additional</c><00:02:34.959><c> Source</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
migrating from additional Source
 

00:02:35.400 --> 00:02:37.229 align:start position:0%
migrating from additional Source
platforms<00:02:36.239><c> with</c><00:02:36.360><c> their</c><00:02:36.640><c> extensive</c>

00:02:37.229 --> 00:02:37.239 align:start position:0%
platforms with their extensive
 

00:02:37.239 --> 00:02:40.270 align:start position:0%
platforms with their extensive
experience<00:02:37.720><c> in</c><00:02:37.920><c> migrating</c><00:02:38.560><c> customers</c><00:02:39.239><c> to</c>

00:02:40.270 --> 00:02:40.280 align:start position:0%
experience in migrating customers to
 

00:02:40.280 --> 00:02:43.470 align:start position:0%
experience in migrating customers to
GitHub<00:02:41.280><c> but</c><00:02:41.959><c> before</c><00:02:42.239><c> we</c><00:02:42.360><c> even</c><00:02:42.640><c> start</c><00:02:43.040><c> talking</c>

00:02:43.470 --> 00:02:43.480 align:start position:0%
GitHub but before we even start talking
 

00:02:43.480 --> 00:02:45.550 align:start position:0%
GitHub but before we even start talking
technology<00:02:44.000><c> and</c><00:02:44.200><c> migration</c><00:02:44.680><c> options</c><00:02:45.360><c> it's</c>

00:02:45.550 --> 00:02:45.560 align:start position:0%
technology and migration options it's
 

00:02:45.560 --> 00:02:47.750 align:start position:0%
technology and migration options it's
important<00:02:46.120><c> that</c><00:02:46.239><c> you</c><00:02:46.440><c> first</c><00:02:46.760><c> take</c><00:02:47.040><c> stock</c><00:02:47.599><c> of</c>

00:02:47.750 --> 00:02:47.760 align:start position:0%
important that you first take stock of
 

00:02:47.760 --> 00:02:49.509 align:start position:0%
important that you first take stock of
what's<00:02:48.040><c> currently</c><00:02:48.440><c> available</c><00:02:48.920><c> in</c><00:02:49.080><c> your</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
what's currently available in your
 

00:02:49.519 --> 00:02:52.509 align:start position:0%
what's currently available in your
environment<00:02:50.519><c> after</c><00:02:50.800><c> all</c><00:02:51.440><c> failing</c><00:02:51.800><c> to</c><00:02:52.000><c> plan</c><00:02:52.360><c> is</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
environment after all failing to plan is
 

00:02:52.519 --> 00:02:54.910 align:start position:0%
environment after all failing to plan is
planning<00:02:52.840><c> to</c><00:02:53.000><c> fail</c><00:02:53.640><c> so</c><00:02:53.879><c> here</c><00:02:54.000><c> are</c><00:02:54.159><c> a</c><00:02:54.280><c> few</c><00:02:54.519><c> tips</c>

00:02:54.910 --> 00:02:54.920 align:start position:0%
planning to fail so here are a few tips
 

00:02:54.920 --> 00:02:56.670 align:start position:0%
planning to fail so here are a few tips
so<00:02:55.080><c> that</c><00:02:55.239><c> you</c><00:02:55.400><c> can</c><00:02:55.599><c> make</c><00:02:55.800><c> your</c><00:02:56.040><c> migration</c><00:02:56.480><c> to</c>

00:02:56.670 --> 00:02:56.680 align:start position:0%
so that you can make your migration to
 

00:02:56.680 --> 00:03:00.070 align:start position:0%
so that you can make your migration to
GitHub<00:02:57.440><c> a</c><00:02:58.120><c> success</c><00:02:59.120><c> start</c><00:02:59.440><c> with</c><00:02:59.560><c> a</c><00:02:59.800><c> thorough</c>

00:03:00.070 --> 00:03:00.080 align:start position:0%
GitHub a success start with a thorough
 

00:03:00.080 --> 00:03:01.990 align:start position:0%
GitHub a success start with a thorough
audit<00:03:00.400><c> of</c><00:03:00.560><c> your</c><00:03:00.720><c> repositories</c><00:03:01.440><c> so</c><00:03:01.599><c> you</c><00:03:01.720><c> know</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
audit of your repositories so you know
 

00:03:02.000 --> 00:03:04.949 align:start position:0%
audit of your repositories so you know
exactly<00:03:02.920><c> what</c><00:03:03.080><c> you</c><00:03:03.200><c> need</c><00:03:03.360><c> to</c><00:03:03.599><c> migrate</c><00:03:04.599><c> you</c><00:03:04.720><c> can</c>

00:03:04.949 --> 00:03:04.959 align:start position:0%
exactly what you need to migrate you can
 

00:03:04.959 --> 00:03:07.270 align:start position:0%
exactly what you need to migrate you can
take<00:03:05.400><c> the</c><00:03:05.599><c> opportunity</c><00:03:06.159><c> at</c><00:03:06.360><c> this</c><00:03:06.560><c> point</c><00:03:07.080><c> to</c>

00:03:07.270 --> 00:03:07.280 align:start position:0%
take the opportunity at this point to
 

00:03:07.280 --> 00:03:09.309 align:start position:0%
take the opportunity at this point to
identify<00:03:07.879><c> inactive</c><00:03:08.400><c> repositories</c><00:03:09.040><c> that</c><00:03:09.159><c> you</c>

00:03:09.309 --> 00:03:09.319 align:start position:0%
identify inactive repositories that you
 

00:03:09.319 --> 00:03:12.910 align:start position:0%
identify inactive repositories that you
might<00:03:09.519><c> want</c><00:03:09.680><c> to</c><00:03:09.879><c> Archive</c><00:03:10.640><c> and</c><00:03:10.879><c> not</c><00:03:11.360><c> bring</c>

00:03:12.910 --> 00:03:12.920 align:start position:0%
might want to Archive and not bring
 

00:03:12.920 --> 00:03:15.149 align:start position:0%
might want to Archive and not bring
across<00:03:13.920><c> you</c><00:03:14.080><c> may</c><00:03:14.280><c> find</c><00:03:14.519><c> that</c><00:03:14.680><c> some</c><00:03:14.840><c> of</c><00:03:14.959><c> your</c>

00:03:15.149 --> 00:03:15.159 align:start position:0%
across you may find that some of your
 

00:03:15.159 --> 00:03:17.270 align:start position:0%
across you may find that some of your
initial<00:03:15.480><c> assumptions</c><00:03:15.959><c> were</c><00:03:16.159><c> too</c><00:03:16.440><c> high</c><00:03:16.760><c> so</c>

00:03:17.270 --> 00:03:17.280 align:start position:0%
initial assumptions were too high so
 

00:03:17.280 --> 00:03:20.110 align:start position:0%
initial assumptions were too high so
maybe<00:03:17.560><c> you</c><00:03:17.680><c> can</c><00:03:17.840><c> opt</c><00:03:18.120><c> for</c><00:03:18.280><c> a</c><00:03:18.400><c> simpler</c>

00:03:20.110 --> 00:03:20.120 align:start position:0%
maybe you can opt for a simpler
 

00:03:20.120 --> 00:03:22.350 align:start position:0%
maybe you can opt for a simpler
migration<00:03:21.120><c> ensure</c><00:03:21.560><c> you</c><00:03:21.720><c> have</c><00:03:21.920><c> clearly</c>

00:03:22.350 --> 00:03:22.360 align:start position:0%
migration ensure you have clearly
 

00:03:22.360 --> 00:03:24.550 align:start position:0%
migration ensure you have clearly
documented<00:03:22.920><c> the</c><00:03:23.040><c> results</c><00:03:23.400><c> of</c><00:03:23.560><c> that</c><00:03:23.799><c> audit</c><00:03:24.400><c> and</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
documented the results of that audit and
 

00:03:24.560 --> 00:03:26.630 align:start position:0%
documented the results of that audit and
are<00:03:24.760><c> tracking</c><00:03:25.120><c> the</c><00:03:25.239><c> results</c><00:03:25.599><c> in</c><00:03:25.760><c> some</c><00:03:26.040><c> way</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
are tracking the results in some way
 

00:03:26.640 --> 00:03:28.630 align:start position:0%
are tracking the results in some way
whether<00:03:26.959><c> that</c><00:03:27.120><c> is</c><00:03:27.280><c> using</c><00:03:27.640><c> GitHub</c><00:03:28.080><c> issues</c><00:03:28.439><c> and</c>

00:03:28.630 --> 00:03:28.640 align:start position:0%
whether that is using GitHub issues and
 

00:03:28.640 --> 00:03:30.990 align:start position:0%
whether that is using GitHub issues and
GitHub<00:03:29.040><c> projects</c><00:03:29.799><c> a</c><00:03:29.959><c> third-party</c><00:03:30.680><c> project</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
GitHub projects a third-party project
 

00:03:31.000 --> 00:03:33.309 align:start position:0%
GitHub projects a third-party project
management<00:03:31.439><c> tool</c><00:03:32.000><c> or</c><00:03:32.319><c> a</c>

00:03:33.309 --> 00:03:33.319 align:start position:0%
management tool or a
 

00:03:33.319 --> 00:03:35.949 align:start position:0%
management tool or a
spreadsheet<00:03:34.319><c> run</c><00:03:34.599><c> a</c><00:03:34.840><c> pilot</c><00:03:35.159><c> migration</c><00:03:35.680><c> with</c><00:03:35.799><c> a</c>

00:03:35.949 --> 00:03:35.959 align:start position:0%
spreadsheet run a pilot migration with a
 

00:03:35.959 --> 00:03:38.110 align:start position:0%
spreadsheet run a pilot migration with a
few<00:03:36.239><c> repositories</c><00:03:36.760><c> and</c><00:03:36.920><c> teams</c><00:03:37.760><c> rather</c><00:03:37.959><c> than</c>

00:03:38.110 --> 00:03:38.120 align:start position:0%
few repositories and teams rather than
 

00:03:38.120 --> 00:03:40.869 align:start position:0%
few repositories and teams rather than
going<00:03:38.360><c> straight</c><00:03:38.720><c> to</c><00:03:38.920><c> a</c><00:03:39.080><c> big</c><00:03:39.319><c> bang</c><00:03:39.879><c> migration</c>

00:03:40.869 --> 00:03:40.879 align:start position:0%
going straight to a big bang migration
 

00:03:40.879 --> 00:03:42.949 align:start position:0%
going straight to a big bang migration
this<00:03:41.040><c> lets</c><00:03:41.280><c> you</c><00:03:41.439><c> figure</c><00:03:41.680><c> out</c><00:03:41.920><c> how</c><00:03:42.120><c> things</c><00:03:42.400><c> work</c>

00:03:42.949 --> 00:03:42.959 align:start position:0%
this lets you figure out how things work
 

00:03:42.959 --> 00:03:45.309 align:start position:0%
this lets you figure out how things work
build<00:03:43.319><c> confidence</c><00:03:44.080><c> and</c><00:03:44.360><c> identify</c><00:03:45.080><c> any</c>

00:03:45.309 --> 00:03:45.319 align:start position:0%
build confidence and identify any
 

00:03:45.319 --> 00:03:48.470 align:start position:0%
build confidence and identify any
important<00:03:45.840><c> gaps</c><00:03:46.400><c> before</c><00:03:46.640><c> you</c><00:03:46.840><c> try</c><00:03:47.080><c> to</c><00:03:47.280><c> migrate</c>

00:03:48.470 --> 00:03:48.480 align:start position:0%
important gaps before you try to migrate
 

00:03:48.480 --> 00:03:50.830 align:start position:0%
important gaps before you try to migrate
everyone<00:03:49.480><c> we</c><00:03:49.599><c> know</c><00:03:49.799><c> in</c><00:03:50.000><c> technology</c><00:03:50.519><c> that</c><00:03:50.680><c> a</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
everyone we know in technology that a
 

00:03:50.840 --> 00:03:52.630 align:start position:0%
everyone we know in technology that a
large<00:03:51.200><c> percentage</c><00:03:51.640><c> of</c><00:03:51.799><c> issues</c><00:03:52.280><c> typically</c>

00:03:52.630 --> 00:03:52.640 align:start position:0%
large percentage of issues typically
 

00:03:52.640 --> 00:03:54.910 align:start position:0%
large percentage of issues typically
stem<00:03:52.959><c> from</c><00:03:53.159><c> people</c><00:03:53.400><c> or</c><00:03:53.599><c> process</c><00:03:54.519><c> rather</c><00:03:54.760><c> than</c>

00:03:54.910 --> 00:03:54.920 align:start position:0%
stem from people or process rather than
 

00:03:54.920 --> 00:03:57.229 align:start position:0%
stem from people or process rather than
the<00:03:55.079><c> technology</c><00:03:55.640><c> itself</c><00:03:56.519><c> so</c><00:03:56.760><c> make</c><00:03:56.920><c> sure</c><00:03:57.120><c> that</c>

00:03:57.229 --> 00:03:57.239 align:start position:0%
the technology itself so make sure that
 

00:03:57.239 --> 00:03:59.110 align:start position:0%
the technology itself so make sure that
you<00:03:57.360><c> have</c><00:03:57.560><c> clearly</c><00:03:58.000><c> defined</c><00:03:58.400><c> your</c><00:03:58.599><c> migration</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
you have clearly defined your migration
 

00:03:59.120 --> 00:04:01.830 align:start position:0%
you have clearly defined your migration
plan<00:03:59.680><c> including</c><00:04:00.079><c> any</c><00:04:00.319><c> pre</c><00:04:00.920><c> and</c><00:04:01.280><c> post</c>

00:04:01.830 --> 00:04:01.840 align:start position:0%
plan including any pre and post
 

00:04:01.840 --> 00:04:03.390 align:start position:0%
plan including any pre and post
migration

00:04:03.390 --> 00:04:03.400 align:start position:0%
migration
 

00:04:03.400 --> 00:04:05.869 align:start position:0%
migration
steps<00:04:04.400><c> make</c><00:04:04.560><c> sure</c><00:04:04.720><c> you</c><00:04:04.840><c> run</c><00:04:05.040><c> a</c><00:04:05.200><c> test</c><00:04:05.480><c> migration</c>

00:04:05.869 --> 00:04:05.879 align:start position:0%
steps make sure you run a test migration
 

00:04:05.879 --> 00:04:08.270 align:start position:0%
steps make sure you run a test migration
of<00:04:06.120><c> every</c><00:04:06.439><c> repository</c><00:04:07.360><c> before</c><00:04:07.720><c> you</c><00:04:07.840><c> do</c><00:04:08.040><c> your</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
of every repository before you do your
 

00:04:08.280 --> 00:04:10.750 align:start position:0%
of every repository before you do your
final<00:04:08.760><c> production</c><00:04:09.439><c> migration</c><00:04:10.439><c> that</c><00:04:10.560><c> will</c>

00:04:10.750 --> 00:04:10.760 align:start position:0%
final production migration that will
 

00:04:10.760 --> 00:04:12.509 align:start position:0%
final production migration that will
give<00:04:10.879><c> you</c><00:04:11.079><c> confidence</c><00:04:11.599><c> that</c><00:04:11.920><c> everything</c><00:04:12.280><c> will</c>

00:04:12.509 --> 00:04:12.519 align:start position:0%
give you confidence that everything will
 

00:04:12.519 --> 00:04:14.670 align:start position:0%
give you confidence that everything will
work<00:04:13.079><c> on</c><00:04:13.280><c> the</c><00:04:13.439><c> big</c>

00:04:14.670 --> 00:04:14.680 align:start position:0%
work on the big
 

00:04:14.680 --> 00:04:17.349 align:start position:0%
work on the big
day<00:04:15.680><c> we're</c><00:04:15.959><c> excited</c><00:04:16.400><c> that</c><00:04:16.560><c> you've</c><00:04:16.840><c> decided</c><00:04:17.199><c> to</c>

00:04:17.349 --> 00:04:17.359 align:start position:0%
day we're excited that you've decided to
 

00:04:17.359 --> 00:04:19.509 align:start position:0%
day we're excited that you've decided to
take<00:04:17.560><c> your</c><00:04:17.759><c> journey</c><00:04:18.160><c> with</c><00:04:18.320><c> GitHub</c><00:04:18.959><c> and</c><00:04:19.239><c> can't</c>

00:04:19.509 --> 00:04:19.519 align:start position:0%
take your journey with GitHub and can't
 

00:04:19.519 --> 00:04:22.270 align:start position:0%
take your journey with GitHub and can't
wait<00:04:19.880><c> to</c><00:04:20.040><c> see</c><00:04:20.280><c> what</c><00:04:20.400><c> you</c><00:04:20.560><c> do</c><00:04:20.840><c> next</c><00:04:21.840><c> so</c><00:04:22.040><c> let's</c>

00:04:22.270 --> 00:04:22.280 align:start position:0%
wait to see what you do next so let's
 

00:04:22.280 --> 00:04:27.440 align:start position:0%
wait to see what you do next so let's
build<00:04:22.639><c> from</c><00:04:23.000><c> here</c><00:04:24.440><c> together</c>

