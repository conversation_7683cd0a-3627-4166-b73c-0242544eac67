WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.470 align:start position:0%
 
all<00:00:00.240><c> right</c><00:00:00.440><c> here</c><00:00:00.560><c> we</c><00:00:00.719><c> are</c><00:00:00.880><c> with</c><00:00:01.000><c> autogen</c>

00:00:01.470 --> 00:00:01.480 align:start position:0%
all right here we are with autogen
 

00:00:01.480 --> 00:00:04.510 align:start position:0%
all right here we are with autogen
version<00:00:02.120><c> 0.2.0</c><00:00:03.120><c> and</c><00:00:03.240><c> this</c><00:00:03.320><c> is</c><00:00:03.480><c> a</c><00:00:03.760><c> big</c><00:00:04.120><c> update</c>

00:00:04.510 --> 00:00:04.520 align:start position:0%
version 0.2.0 and this is a big update
 

00:00:04.520 --> 00:00:06.510 align:start position:0%
version 0.2.0 and this is a big update
and<00:00:04.640><c> the</c><00:00:04.799><c> reason</c><00:00:05.240><c> I</c><00:00:05.400><c> say</c><00:00:05.680><c> that</c><00:00:06.040><c> is</c><00:00:06.200><c> because</c><00:00:06.399><c> it</c>

00:00:06.510 --> 00:00:06.520 align:start position:0%
and the reason I say that is because it
 

00:00:06.520 --> 00:00:09.030 align:start position:0%
and the reason I say that is because it
has<00:00:06.640><c> a</c><00:00:06.839><c> breaking</c><00:00:07.360><c> change</c><00:00:08.360><c> and</c><00:00:08.639><c> maybe</c><00:00:08.840><c> you're</c>

00:00:09.030 --> 00:00:09.040 align:start position:0%
has a breaking change and maybe you're
 

00:00:09.040 --> 00:00:10.830 align:start position:0%
has a breaking change and maybe you're
wondering<00:00:09.679><c> well</c><00:00:10.200><c> why</c><00:00:10.320><c> is</c><00:00:10.440><c> this</c><00:00:10.599><c> such</c><00:00:10.719><c> a</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
wondering well why is this such a
 

00:00:10.840 --> 00:00:12.070 align:start position:0%
wondering well why is this such a
breaking<00:00:11.120><c> change</c><00:00:11.360><c> or</c><00:00:11.519><c> what</c><00:00:11.599><c> does</c><00:00:11.719><c> that</c><00:00:11.880><c> even</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
breaking change or what does that even
 

00:00:12.080 --> 00:00:14.310 align:start position:0%
breaking change or what does that even
mean<00:00:12.759><c> well</c><00:00:13.080><c> first</c><00:00:13.280><c> off</c><00:00:13.440><c> that</c><00:00:13.559><c> means</c><00:00:13.880><c> that</c><00:00:14.240><c> if</c>

00:00:14.310 --> 00:00:14.320 align:start position:0%
mean well first off that means that if
 

00:00:14.320 --> 00:00:16.070 align:start position:0%
mean well first off that means that if
you<00:00:14.440><c> were</c><00:00:14.559><c> to</c><00:00:14.719><c> upgrade</c><00:00:15.280><c> right</c><00:00:15.440><c> now</c><00:00:15.759><c> to</c><00:00:16.000><c> the</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
you were to upgrade right now to the
 

00:00:16.080 --> 00:00:18.470 align:start position:0%
you were to upgrade right now to the
newer<00:00:16.359><c> version</c><00:00:17.000><c> and</c><00:00:17.119><c> you</c><00:00:17.240><c> tried</c><00:00:17.480><c> to</c><00:00:17.640><c> run</c><00:00:18.160><c> the</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
newer version and you tried to run the
 

00:00:18.480 --> 00:00:20.590 align:start position:0%
newer version and you tried to run the
current<00:00:18.760><c> code</c><00:00:19.000><c> you</c><00:00:19.160><c> had</c><00:00:19.439><c> right</c><00:00:19.720><c> before</c><00:00:20.400><c> you</c>

00:00:20.590 --> 00:00:20.600 align:start position:0%
current code you had right before you
 

00:00:20.600 --> 00:00:22.910 align:start position:0%
current code you had right before you
upgraded<00:00:21.400><c> there's</c><00:00:21.600><c> a</c><00:00:21.840><c> good</c><00:00:22.080><c> chance</c><00:00:22.400><c> it's</c><00:00:22.640><c> not</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
upgraded there's a good chance it's not
 

00:00:22.920 --> 00:00:24.269 align:start position:0%
upgraded there's a good chance it's not
going<00:00:23.039><c> to</c><00:00:23.240><c> work</c><00:00:23.439><c> and</c><00:00:23.560><c> it's</c><00:00:23.680><c> not</c><00:00:23.760><c> going</c><00:00:23.880><c> to</c><00:00:24.000><c> run</c>

00:00:24.269 --> 00:00:24.279 align:start position:0%
going to work and it's not going to run
 

00:00:24.279 --> 00:00:26.349 align:start position:0%
going to work and it's not going to run
and<00:00:24.400><c> you're</c><00:00:24.480><c> going</c><00:00:24.599><c> to</c><00:00:24.720><c> get</c><00:00:24.800><c> a</c><00:00:25.000><c> specific</c><00:00:25.519><c> error</c>

00:00:26.349 --> 00:00:26.359 align:start position:0%
and you're going to get a specific error
 

00:00:26.359 --> 00:00:29.029 align:start position:0%
and you're going to get a specific error
and<00:00:26.519><c> the</c><00:00:26.760><c> reason</c><00:00:27.240><c> is</c><00:00:27.439><c> because</c><00:00:27.880><c> now</c><00:00:28.640><c> code</c>

00:00:29.029 --> 00:00:29.039 align:start position:0%
and the reason is because now code
 

00:00:29.039 --> 00:00:31.669 align:start position:0%
and the reason is because now code
execution<00:00:29.800><c> by</c><00:00:30.039><c> by</c><00:00:30.199><c> default</c><00:00:30.960><c> happens</c><00:00:31.400><c> inside</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
execution by by default happens inside
 

00:00:31.679 --> 00:00:33.630 align:start position:0%
execution by by default happens inside
of<00:00:31.800><c> a</c><00:00:31.960><c> Docker</c><00:00:32.360><c> container</c><00:00:33.040><c> instead</c><00:00:33.320><c> of</c><00:00:33.480><c> just</c>

00:00:33.630 --> 00:00:33.640 align:start position:0%
of a Docker container instead of just
 

00:00:33.640 --> 00:00:35.510 align:start position:0%
of a Docker container instead of just
locally<00:00:34.120><c> on</c><00:00:34.280><c> your</c><00:00:34.520><c> machine</c><00:00:35.079><c> now</c><00:00:35.280><c> this</c>

00:00:35.510 --> 00:00:35.520 align:start position:0%
locally on your machine now this
 

00:00:35.520 --> 00:00:37.389 align:start position:0%
locally on your machine now this
property<00:00:35.960><c> use</c><00:00:36.200><c> Docker</c><00:00:36.760><c> it's</c><00:00:36.960><c> always</c><00:00:37.160><c> been</c>

00:00:37.389 --> 00:00:37.399 align:start position:0%
property use Docker it's always been
 

00:00:37.399 --> 00:00:39.030 align:start position:0%
property use Docker it's always been
there<00:00:37.879><c> I've</c><00:00:38.079><c> never</c><00:00:38.280><c> used</c><00:00:38.520><c> it</c><00:00:38.680><c> there's</c><00:00:38.840><c> a</c><00:00:38.920><c> good</c>

00:00:39.030 --> 00:00:39.040 align:start position:0%
there I've never used it there's a good
 

00:00:39.040 --> 00:00:40.590 align:start position:0%
there I've never used it there's a good
chance<00:00:39.280><c> you've</c><00:00:39.480><c> never</c><00:00:39.640><c> used</c><00:00:39.840><c> it</c><00:00:40.120><c> and</c><00:00:40.239><c> we</c><00:00:40.360><c> never</c>

00:00:40.590 --> 00:00:40.600 align:start position:0%
chance you've never used it and we never
 

00:00:40.600 --> 00:00:42.110 align:start position:0%
chance you've never used it and we never
had<00:00:40.760><c> to</c><00:00:41.320><c> because</c><00:00:41.559><c> if</c><00:00:41.680><c> you</c><00:00:41.760><c> didn't</c><00:00:41.920><c> even</c><00:00:42.039><c> know</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
had to because if you didn't even know
 

00:00:42.120 --> 00:00:43.590 align:start position:0%
had to because if you didn't even know
what<00:00:42.280><c> Docker</c><00:00:42.760><c> is</c><00:00:43.000><c> or</c><00:00:43.079><c> you</c><00:00:43.200><c> don't</c><00:00:43.320><c> know</c><00:00:43.440><c> what</c>

00:00:43.590 --> 00:00:43.600 align:start position:0%
what Docker is or you don't know what
 

00:00:43.600 --> 00:00:44.549 align:start position:0%
what Docker is or you don't know what
you<00:00:43.640><c> didn't</c><00:00:43.800><c> know</c><00:00:43.879><c> what</c><00:00:43.960><c> it</c><00:00:44.079><c> was</c><00:00:44.320><c> you</c><00:00:44.399><c> don't</c>

00:00:44.549 --> 00:00:44.559 align:start position:0%
you didn't know what it was you don't
 

00:00:44.559 --> 00:00:46.470 align:start position:0%
you didn't know what it was you don't
know<00:00:44.680><c> what</c><00:00:44.760><c> it</c><00:00:44.879><c> is</c><00:00:45.079><c> now</c><00:00:45.760><c> that</c><00:00:45.960><c> was</c><00:00:46.160><c> fine</c><00:00:46.399><c> it</c>

00:00:46.470 --> 00:00:46.480 align:start position:0%
know what it is now that was fine it
 

00:00:46.480 --> 00:00:48.189 align:start position:0%
know what it is now that was fine it
didn't<00:00:46.719><c> matter</c><00:00:47.079><c> before</c><00:00:47.480><c> but</c><00:00:47.640><c> now</c><00:00:47.760><c> it</c><00:00:47.920><c> kind</c><00:00:48.039><c> of</c>

00:00:48.189 --> 00:00:48.199 align:start position:0%
didn't matter before but now it kind of
 

00:00:48.199 --> 00:00:50.389 align:start position:0%
didn't matter before but now it kind of
does<00:00:48.879><c> right</c><00:00:49.000><c> you</c><00:00:49.160><c> still</c><00:00:49.399><c> don't</c><00:00:49.840><c> have</c><00:00:50.000><c> to</c><00:00:50.160><c> use</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
does right you still don't have to use
 

00:00:50.399 --> 00:00:51.990 align:start position:0%
does right you still don't have to use
Docker<00:00:51.079><c> you</c><00:00:51.160><c> don't</c><00:00:51.360><c> have</c><00:00:51.440><c> to</c><00:00:51.559><c> download</c><00:00:51.879><c> the</c>

00:00:51.990 --> 00:00:52.000 align:start position:0%
Docker you don't have to download the
 

00:00:52.000 --> 00:00:53.229 align:start position:0%
Docker you don't have to download the
software<00:00:52.320><c> which</c><00:00:52.440><c> I'll</c><00:00:52.520><c> show</c><00:00:52.680><c> you</c><00:00:52.800><c> how</c><00:00:52.920><c> to</c><00:00:53.160><c> you</c>

00:00:53.229 --> 00:00:53.239 align:start position:0%
software which I'll show you how to you
 

00:00:53.239 --> 00:00:55.229 align:start position:0%
software which I'll show you how to you
don't<00:00:53.399><c> have</c><00:00:53.480><c> to</c><00:00:53.559><c> do</c><00:00:53.760><c> any</c><00:00:53.960><c> of</c><00:00:54.120><c> that</c><00:00:54.320><c> stuff</c><00:00:55.039><c> but</c>

00:00:55.229 --> 00:00:55.239 align:start position:0%
don't have to do any of that stuff but
 

00:00:55.239 --> 00:00:57.990 align:start position:0%
don't have to do any of that stuff but
you<00:00:55.440><c> have</c><00:00:55.559><c> to</c><00:00:55.760><c> know</c><00:00:56.600><c> what</c><00:00:56.719><c> to</c><00:00:56.960><c> do</c><00:00:57.399><c> so</c><00:00:57.600><c> you</c><00:00:57.760><c> don't</c>

00:00:57.990 --> 00:00:58.000 align:start position:0%
you have to know what to do so you don't
 

00:00:58.000 --> 00:01:00.389 align:start position:0%
you have to know what to do so you don't
need<00:00:58.199><c> to</c><00:00:58.359><c> use</c><00:00:58.559><c> it</c><00:00:59.359><c> or</c><00:00:59.559><c> if</c><00:00:59.680><c> you</c><00:00:59.960><c> want</c><00:01:00.079><c> to</c><00:01:00.160><c> try</c><00:01:00.320><c> it</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
need to use it or if you want to try it
 

00:01:00.399 --> 00:01:01.950 align:start position:0%
need to use it or if you want to try it
out<00:01:00.600><c> you</c><00:01:00.719><c> can</c><00:01:00.960><c> as</c><00:01:01.079><c> well</c><00:01:01.440><c> so</c><00:01:01.559><c> let's</c><00:01:01.719><c> just</c><00:01:01.840><c> look</c>

00:01:01.950 --> 00:01:01.960 align:start position:0%
out you can as well so let's just look
 

00:01:01.960 --> 00:01:03.270 align:start position:0%
out you can as well so let's just look
at<00:01:02.079><c> some</c><00:01:02.239><c> code</c><00:01:02.640><c> I'm</c><00:01:02.760><c> going</c><00:01:02.840><c> to</c><00:01:02.920><c> show</c><00:01:03.039><c> you</c><00:01:03.160><c> what</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
at some code I'm going to show you what
 

00:01:03.280 --> 00:01:04.590 align:start position:0%
at some code I'm going to show you what
the<00:01:03.399><c> error</c><00:01:03.719><c> that</c><00:01:03.800><c> you're</c><00:01:04.040><c> probably</c><00:01:04.280><c> going</c><00:01:04.400><c> to</c>

00:01:04.590 --> 00:01:04.600 align:start position:0%
the error that you're probably going to
 

00:01:04.600 --> 00:01:05.910 align:start position:0%
the error that you're probably going to
get<00:01:04.760><c> in</c><00:01:04.879><c> when</c><00:01:05.040><c> you</c><00:01:05.159><c> upgrade</c><00:01:05.600><c> and</c><00:01:05.720><c> then</c><00:01:05.799><c> I'll</c>

00:01:05.910 --> 00:01:05.920 align:start position:0%
get in when you upgrade and then I'll
 

00:01:05.920 --> 00:01:07.190 align:start position:0%
get in when you upgrade and then I'll
show<00:01:06.040><c> you</c><00:01:06.159><c> how</c><00:01:06.240><c> to</c><00:01:06.400><c> fix</c><00:01:06.560><c> it</c><00:01:06.799><c> all</c><00:01:06.920><c> right</c><00:01:07.000><c> here</c><00:01:07.119><c> we</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
show you how to fix it all right here we
 

00:01:07.200 --> 00:01:08.429 align:start position:0%
show you how to fix it all right here we
have<00:01:07.280><c> a</c><00:01:07.360><c> simple</c><00:01:07.560><c> example</c><00:01:08.000><c> but</c><00:01:08.200><c> first</c><00:01:08.320><c> thing</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
have a simple example but first thing
 

00:01:08.439 --> 00:01:10.749 align:start position:0%
have a simple example but first thing
you<00:01:08.600><c> need</c><00:01:08.720><c> to</c><00:01:08.840><c> do</c><00:01:09.119><c> is</c><00:01:09.360><c> upgrade</c><00:01:09.759><c> autogen</c><00:01:10.479><c> so</c><00:01:10.640><c> to</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
you need to do is upgrade autogen so to
 

00:01:10.759 --> 00:01:12.630 align:start position:0%
you need to do is upgrade autogen so to
do<00:01:10.920><c> that</c><00:01:11.119><c> open</c><00:01:11.320><c> up</c><00:01:11.439><c> your</c><00:01:11.600><c> terminal</c><00:01:12.320><c> and</c><00:01:12.400><c> you'll</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
do that open up your terminal and you'll
 

00:01:12.640 --> 00:01:18.030 align:start position:0%
do that open up your terminal and you'll
say<00:01:12.960><c> pip</c><00:01:13.880><c> install</c><00:01:14.880><c> D-</c><00:01:15.600><c> upgrade</c><00:01:16.600><c> Pi</c><00:01:17.040><c> autogen</c>

00:01:18.030 --> 00:01:18.040 align:start position:0%
say pip install D- upgrade Pi autogen
 

00:01:18.040 --> 00:01:19.990 align:start position:0%
say pip install D- upgrade Pi autogen
okay<00:01:18.240><c> and</c><00:01:18.560><c> I</c><00:01:18.680><c> already</c><00:01:18.920><c> have</c><00:01:19.080><c> it</c><00:01:19.360><c> upgraded</c><00:01:19.840><c> so</c>

00:01:19.990 --> 00:01:20.000 align:start position:0%
okay and I already have it upgraded so
 

00:01:20.000 --> 00:01:21.109 align:start position:0%
okay and I already have it upgraded so
you'll<00:01:20.159><c> get</c><00:01:20.280><c> something</c><00:01:20.479><c> a</c><00:01:20.560><c> little</c><00:01:20.759><c> different</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
you'll get something a little different
 

00:01:21.119 --> 00:01:23.950 align:start position:0%
you'll get something a little different
but<00:01:21.240><c> it's</c><00:01:21.400><c> going</c><00:01:21.479><c> to</c><00:01:21.600><c> goe</c><00:01:21.880><c> upgrade</c><00:01:22.400><c> to</c><00:01:22.960><c> 0.2.0</c>

00:01:23.950 --> 00:01:23.960 align:start position:0%
but it's going to goe upgrade to 0.2.0
 

00:01:23.960 --> 00:01:25.950 align:start position:0%
but it's going to goe upgrade to 0.2.0
now<00:01:24.119><c> when</c><00:01:24.240><c> it's</c><00:01:24.400><c> done</c><00:01:24.920><c> this</c><00:01:25.119><c> example</c><00:01:25.680><c> we</c><00:01:25.840><c> have</c>

00:01:25.950 --> 00:01:25.960 align:start position:0%
now when it's done this example we have
 

00:01:25.960 --> 00:01:28.310 align:start position:0%
now when it's done this example we have
a<00:01:26.079><c> simple</c><00:01:26.400><c> config</c><00:01:26.840><c> list</c><00:01:27.280><c> where</c><00:01:27.640><c> we</c><00:01:27.799><c> get</c><00:01:28.040><c> the</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
a simple config list where we get the
 

00:01:28.320 --> 00:01:32.190 align:start position:0%
a simple config list where we get the
open<00:01:28.680><c> AI</c><00:01:29.520><c> key</c><00:01:30.119><c> and</c><00:01:30.320><c> our</c><00:01:30.520><c> model</c><00:01:31.040><c> from</c><00:01:31.240><c> this</c><00:01:31.479><c> file</c>

00:01:32.190 --> 00:01:32.200 align:start position:0%
open AI key and our model from this file
 

00:01:32.200 --> 00:01:34.350 align:start position:0%
open AI key and our model from this file
oi<00:01:32.720><c> config</c><00:01:33.119><c> list</c><00:01:33.439><c> this</c><00:01:33.560><c> will</c><00:01:33.680><c> be</c><00:01:33.759><c> in</c><00:01:33.880><c> my</c><00:01:34.000><c> GitHub</c>

00:01:34.350 --> 00:01:34.360 align:start position:0%
oi config list this will be in my GitHub
 

00:01:34.360 --> 00:01:36.109 align:start position:0%
oi config list this will be in my GitHub
so<00:01:34.439><c> you</c><00:01:34.520><c> can</c><00:01:34.640><c> look</c><00:01:34.759><c> at</c><00:01:34.960><c> this</c><00:01:35.680><c> um</c><00:01:35.840><c> and</c><00:01:35.920><c> then</c><00:01:36.000><c> we</c>

00:01:36.109 --> 00:01:36.119 align:start position:0%
so you can look at this um and then we
 

00:01:36.119 --> 00:01:37.310 align:start position:0%
so you can look at this um and then we
have<00:01:36.200><c> two</c><00:01:36.360><c> agents</c><00:01:36.680><c> we</c><00:01:36.799><c> have</c><00:01:36.880><c> the</c><00:01:37.000><c> assistant</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
have two agents we have the assistant
 

00:01:37.320 --> 00:01:39.310 align:start position:0%
have two agents we have the assistant
agent<00:01:37.560><c> where</c><00:01:37.680><c> we</c><00:01:37.799><c> just</c><00:01:37.920><c> give</c><00:01:38.040><c> it</c><00:01:38.119><c> a</c><00:01:38.240><c> name</c><00:01:39.000><c> we</c>

00:01:39.310 --> 00:01:39.320 align:start position:0%
agent where we just give it a name we
 

00:01:39.320 --> 00:01:41.469 align:start position:0%
agent where we just give it a name we
assign<00:01:39.799><c> the</c><00:01:39.920><c> L</c><00:01:40.399><c> config</c><00:01:40.920><c> basically</c><00:01:41.240><c> to</c><00:01:41.360><c> the</c>

00:01:41.469 --> 00:01:41.479 align:start position:0%
assign the L config basically to the
 

00:01:41.479 --> 00:01:43.429 align:start position:0%
assign the L config basically to the
config<00:01:41.840><c> list</c><00:01:42.399><c> and</c><00:01:42.560><c> then</c><00:01:42.759><c> we</c><00:01:42.880><c> have</c><00:01:43.000><c> the</c><00:01:43.119><c> user</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
config list and then we have the user
 

00:01:43.439 --> 00:01:45.550 align:start position:0%
config list and then we have the user
proxy<00:01:43.840><c> agent</c><00:01:44.159><c> and</c><00:01:44.479><c> this</c><00:01:44.680><c> is</c><00:01:44.840><c> where</c><00:01:45.000><c> it</c><00:01:45.119><c> matters</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
proxy agent and this is where it matters
 

00:01:45.560 --> 00:01:47.590 align:start position:0%
proxy agent and this is where it matters
okay<00:01:45.759><c> this</c><00:01:45.880><c> is</c><00:01:46.040><c> the</c><00:01:46.159><c> difference</c><00:01:46.799><c> so</c><00:01:47.119><c> we</c><00:01:47.360><c> create</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
okay this is the difference so we create
 

00:01:47.600 --> 00:01:50.109 align:start position:0%
okay this is the difference so we create
the<00:01:47.719><c> user</c><00:01:48.000><c> proxy</c><00:01:48.320><c> agent</c><00:01:48.840><c> we</c><00:01:49.040><c> give</c><00:01:49.159><c> it</c><00:01:49.280><c> a</c><00:01:49.439><c> name</c>

00:01:50.109 --> 00:01:50.119 align:start position:0%
the user proxy agent we give it a name
 

00:01:50.119 --> 00:01:51.950 align:start position:0%
the user proxy agent we give it a name
that's<00:01:50.280><c> not</c><00:01:50.479><c> different</c><00:01:51.200><c> and</c><00:01:51.399><c> we've</c><00:01:51.560><c> seen</c><00:01:51.759><c> this</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
that's not different and we've seen this
 

00:01:51.960 --> 00:01:54.550 align:start position:0%
that's not different and we've seen this
before<00:01:52.280><c> you</c><00:01:52.399><c> have</c><00:01:52.520><c> a</c><00:01:52.680><c> code</c><00:01:53.040><c> execution</c><00:01:53.560><c> config</c>

00:01:54.550 --> 00:01:54.560 align:start position:0%
before you have a code execution config
 

00:01:54.560 --> 00:01:56.709 align:start position:0%
before you have a code execution config
and<00:01:54.880><c> this</c><00:01:55.399><c> basically</c><00:01:55.759><c> has</c><00:01:56.039><c> single</c><00:01:56.360><c> property</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
and this basically has single property
 

00:01:56.719 --> 00:01:58.950 align:start position:0%
and this basically has single property
for<00:01:56.920><c> working</c><00:01:57.479><c> directory</c><00:01:58.399><c> and</c><00:01:58.560><c> this</c><00:01:58.680><c> means</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
for working directory and this means
 

00:01:58.960 --> 00:02:01.510 align:start position:0%
for working directory and this means
that<00:01:59.159><c> if</c><00:01:59.640><c> we</c><00:01:59.840><c> we</c><00:02:00.000><c> execute</c><00:02:00.439><c> this</c><00:02:00.920><c> and</c><00:02:01.360><c> the</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
that if we we execute this and the
 

00:02:01.520 --> 00:02:03.389 align:start position:0%
that if we we execute this and the
assistant<00:02:01.880><c> agent</c><00:02:02.280><c> actually</c><00:02:02.640><c> executes</c><00:02:03.200><c> the</c>

00:02:03.389 --> 00:02:03.399 align:start position:0%
assistant agent actually executes the
 

00:02:03.399 --> 00:02:05.830 align:start position:0%
assistant agent actually executes the
code<00:02:03.799><c> that</c><00:02:04.079><c> or</c><00:02:04.759><c> creates</c><00:02:05.119><c> code</c><00:02:05.360><c> that</c><00:02:05.479><c> we</c><00:02:05.560><c> wanted</c>

00:02:05.830 --> 00:02:05.840 align:start position:0%
code that or creates code that we wanted
 

00:02:05.840 --> 00:02:08.150 align:start position:0%
code that or creates code that we wanted
to<00:02:06.520><c> the</c><00:02:06.640><c> user</c><00:02:06.960><c> proxy</c><00:02:07.280><c> agent</c><00:02:07.479><c> will</c><00:02:07.719><c> execute</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
to the user proxy agent will execute
 

00:02:08.160 --> 00:02:10.430 align:start position:0%
to the user proxy agent will execute
that<00:02:08.319><c> code</c><00:02:09.000><c> and</c><00:02:09.360><c> if</c><00:02:09.479><c> when</c><00:02:09.640><c> we're</c><00:02:09.840><c> done</c><00:02:10.160><c> it'll</c>

00:02:10.430 --> 00:02:10.440 align:start position:0%
that code and if when we're done it'll
 

00:02:10.440 --> 00:02:12.470 align:start position:0%
that code and if when we're done it'll
put<00:02:10.599><c> it</c><00:02:10.720><c> into</c><00:02:10.920><c> a</c><00:02:11.080><c> directory</c><00:02:11.879><c> inside</c><00:02:12.160><c> of</c><00:02:12.239><c> our</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
put it into a directory inside of our
 

00:02:12.480 --> 00:02:14.470 align:start position:0%
put it into a directory inside of our
project<00:02:12.879><c> called</c><00:02:13.200><c> coding</c><00:02:13.879><c> okay</c><00:02:14.080><c> so</c><00:02:14.239><c> we</c><00:02:14.360><c> just</c>

00:02:14.470 --> 00:02:14.480 align:start position:0%
project called coding okay so we just
 

00:02:14.480 --> 00:02:16.589 align:start position:0%
project called coding okay so we just
upgraded<00:02:15.319><c> let's</c><00:02:15.519><c> see</c><00:02:15.920><c> what</c><00:02:16.080><c> happens</c><00:02:16.360><c> when</c><00:02:16.480><c> we</c>

00:02:16.589 --> 00:02:16.599 align:start position:0%
upgraded let's see what happens when we
 

00:02:16.599 --> 00:02:19.309 align:start position:0%
upgraded let's see what happens when we
just<00:02:16.760><c> run</c><00:02:17.080><c> this</c><00:02:17.440><c> so</c><00:02:17.599><c> you'll</c><00:02:17.760><c> say</c><00:02:18.040><c> Python</c><00:02:18.400><c> 3</c>

00:02:19.309 --> 00:02:19.319 align:start position:0%
just run this so you'll say Python 3
 

00:02:19.319 --> 00:02:22.110 align:start position:0%
just run this so you'll say Python 3
main.py<00:02:20.319><c> and</c><00:02:20.519><c> run</c><00:02:20.720><c> it</c><00:02:21.160><c> and</c><00:02:21.400><c> look</c><00:02:21.680><c> at</c><00:02:21.920><c> this</c>

00:02:22.110 --> 00:02:22.120 align:start position:0%
main.py and run it and look at this
 

00:02:22.120 --> 00:02:24.670 align:start position:0%
main.py and run it and look at this
error<00:02:22.720><c> make</c><00:02:22.879><c> sure</c><00:02:23.239><c> Docker</c><00:02:23.680><c> is</c><00:02:23.920><c> running</c><00:02:24.400><c> set</c>

00:02:24.670 --> 00:02:24.680 align:start position:0%
error make sure Docker is running set
 

00:02:24.680 --> 00:02:27.470 align:start position:0%
error make sure Docker is running set
use<00:02:24.959><c> Docker</c><00:02:25.319><c> to</c><00:02:25.599><c> false</c><00:02:26.120><c> in</c><00:02:26.440><c> code</c><00:02:26.840><c> execution</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
use Docker to false in code execution
 

00:02:27.480 --> 00:02:29.350 align:start position:0%
use Docker to false in code execution
config<00:02:28.120><c> okay</c><00:02:28.319><c> this</c><00:02:28.400><c> is</c><00:02:28.519><c> the</c><00:02:28.640><c> era</c><00:02:28.879><c> that</c><00:02:29.000><c> you</c><00:02:29.080><c> are</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
config okay this is the era that you are
 

00:02:29.360 --> 00:02:30.790 align:start position:0%
config okay this is the era that you are
probably<00:02:29.920><c> going</c><00:02:30.000><c> to</c><00:02:30.080><c> see</c><00:02:30.280><c> when</c><00:02:30.400><c> you</c><00:02:30.519><c> try</c><00:02:30.640><c> to</c>

00:02:30.790 --> 00:02:30.800 align:start position:0%
probably going to see when you try to
 

00:02:30.800 --> 00:02:33.190 align:start position:0%
probably going to see when you try to
run<00:02:30.959><c> this</c><00:02:31.120><c> the</c><00:02:31.280><c> first</c><00:02:31.519><c> time</c><00:02:31.920><c> okay</c><00:02:32.400><c> and</c><00:02:32.840><c> this</c><00:02:33.040><c> is</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
run this the first time okay and this is
 

00:02:33.200 --> 00:02:34.710 align:start position:0%
run this the first time okay and this is
the<00:02:33.360><c> breaking</c><00:02:33.680><c> change</c><00:02:34.080><c> this</c><00:02:34.160><c> is</c><00:02:34.239><c> what</c><00:02:34.360><c> I</c><00:02:34.480><c> mean</c>

00:02:34.710 --> 00:02:34.720 align:start position:0%
the breaking change this is what I mean
 

00:02:34.720 --> 00:02:36.390 align:start position:0%
the breaking change this is what I mean
that<00:02:35.040><c> if</c><00:02:35.120><c> you</c><00:02:35.239><c> upgrade</c><00:02:35.640><c> it</c><00:02:35.959><c> and</c><00:02:36.080><c> then</c><00:02:36.160><c> you</c><00:02:36.280><c> try</c>

00:02:36.390 --> 00:02:36.400 align:start position:0%
that if you upgrade it and then you try
 

00:02:36.400 --> 00:02:37.869 align:start position:0%
that if you upgrade it and then you try
to<00:02:36.519><c> run</c><00:02:36.760><c> something</c><00:02:37.239><c> without</c><00:02:37.480><c> changing</c>

00:02:37.869 --> 00:02:37.879 align:start position:0%
to run something without changing
 

00:02:37.879 --> 00:02:40.070 align:start position:0%
to run something without changing
anything<00:02:38.120><c> else</c><00:02:38.599><c> it's</c><00:02:38.720><c> not</c><00:02:38.840><c> going</c><00:02:38.959><c> to</c><00:02:39.080><c> work</c><00:02:39.680><c> now</c>

00:02:40.070 --> 00:02:40.080 align:start position:0%
anything else it's not going to work now
 

00:02:40.080 --> 00:02:42.070 align:start position:0%
anything else it's not going to work now
in<00:02:40.200><c> order</c><00:02:40.560><c> to</c><00:02:40.760><c> make</c><00:02:41.000><c> this</c><00:02:41.200><c> work</c><00:02:41.480><c> say</c><00:02:41.680><c> you</c><00:02:41.879><c> don't</c>

00:02:42.070 --> 00:02:42.080 align:start position:0%
in order to make this work say you don't
 

00:02:42.080 --> 00:02:44.630 align:start position:0%
in order to make this work say you don't
want<00:02:42.200><c> to</c><00:02:42.319><c> use</c><00:02:42.560><c> Docker</c><00:02:43.400><c> all</c><00:02:43.599><c> we</c><00:02:43.760><c> do</c><00:02:44.040><c> in</c><00:02:44.200><c> the</c><00:02:44.319><c> user</c>

00:02:44.630 --> 00:02:44.640 align:start position:0%
want to use Docker all we do in the user
 

00:02:44.640 --> 00:02:46.509 align:start position:0%
want to use Docker all we do in the user
proxy<00:02:44.959><c> agent</c><00:02:45.280><c> up</c><00:02:45.440><c> here</c><00:02:45.920><c> in</c><00:02:46.040><c> the</c><00:02:46.200><c> code</c>

00:02:46.509 --> 00:02:46.519 align:start position:0%
proxy agent up here in the code
 

00:02:46.519 --> 00:02:48.270 align:start position:0%
proxy agent up here in the code
execution<00:02:47.040><c> config</c><00:02:47.720><c> this</c><00:02:47.840><c> is</c><00:02:47.959><c> the</c><00:02:48.080><c> new</c>

00:02:48.270 --> 00:02:48.280 align:start position:0%
execution config this is the new
 

00:02:48.280 --> 00:02:50.350 align:start position:0%
execution config this is the new
property<00:02:48.560><c> use</c><00:02:48.800><c> Docker</c><00:02:49.200><c> it's</c><00:02:49.360><c> not</c><00:02:49.519><c> new</c><00:02:49.879><c> but</c><00:02:50.239><c> we</c>

00:02:50.350 --> 00:02:50.360 align:start position:0%
property use Docker it's not new but we
 

00:02:50.360 --> 00:02:51.830 align:start position:0%
property use Docker it's not new but we
just<00:02:50.480><c> never</c><00:02:50.680><c> really</c><00:02:50.800><c> used</c><00:02:50.959><c> it</c><00:02:51.120><c> that</c><00:02:51.280><c> much</c><00:02:51.720><c> so</c>

00:02:51.830 --> 00:02:51.840 align:start position:0%
just never really used it that much so
 

00:02:51.840 --> 00:02:56.309 align:start position:0%
just never really used it that much so
you<00:02:52.080><c> just</c><00:02:52.239><c> say</c><00:02:52.800><c> comma</c><00:02:54.159><c> _</c><00:02:55.159><c> Docker</c><00:02:56.080><c> and</c><00:02:56.200><c> then</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
you just say comma _ Docker and then
 

00:02:56.319 --> 00:02:59.030 align:start position:0%
you just say comma _ Docker and then
you'll<00:02:56.560><c> just</c><00:02:56.720><c> say</c><00:02:57.159><c> false</c><00:02:58.159><c> okay</c><00:02:58.760><c> that's</c><00:02:58.920><c> all</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
you'll just say false okay that's all
 

00:02:59.040 --> 00:03:00.309 align:start position:0%
you'll just say false okay that's all
you<00:02:59.159><c> got</c><00:02:59.239><c> to</c><00:02:59.400><c> do</c><00:02:59.560><c> if</c><00:02:59.760><c> if</c><00:02:59.840><c> you</c><00:02:59.959><c> don't</c><00:03:00.120><c> want</c><00:03:00.239><c> to</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
you got to do if if you don't want to
 

00:03:00.319 --> 00:03:04.910 align:start position:0%
you got to do if if you don't want to
use<00:03:00.519><c> Docker</c><00:03:01.200><c> now</c><00:03:02.200><c> if</c><00:03:02.360><c> we</c><00:03:02.599><c> rerun</c><00:03:03.239><c> this</c><00:03:04.239><c> Python</c><00:03:04.560><c> 3</c>

00:03:04.910 --> 00:03:04.920 align:start position:0%
use Docker now if we rerun this Python 3
 

00:03:04.920 --> 00:03:07.229 align:start position:0%
use Docker now if we rerun this Python 3
main.py<00:03:05.920><c> it</c><00:03:06.040><c> should</c><00:03:06.239><c> start</c><00:03:06.440><c> working</c><00:03:06.879><c> and</c><00:03:07.120><c> as</c>

00:03:07.229 --> 00:03:07.239 align:start position:0%
main.py it should start working and as
 

00:03:07.239 --> 00:03:09.070 align:start position:0%
main.py it should start working and as
you<00:03:07.319><c> can</c><00:03:07.440><c> see</c><00:03:07.840><c> it</c><00:03:08.040><c> is</c><00:03:08.400><c> it's</c><00:03:08.560><c> now</c><00:03:08.879><c> actually</c>

00:03:09.070 --> 00:03:09.080 align:start position:0%
you can see it is it's now actually
 

00:03:09.080 --> 00:03:10.789 align:start position:0%
you can see it is it's now actually
going<00:03:09.200><c> to</c><00:03:09.400><c> execute</c><00:03:09.879><c> because</c><00:03:10.080><c> we</c><00:03:10.200><c> told</c><00:03:10.400><c> it</c><00:03:10.640><c> not</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
going to execute because we told it not
 

00:03:10.799 --> 00:03:13.470 align:start position:0%
going to execute because we told it not
to<00:03:11.360><c> try</c><00:03:11.519><c> to</c><00:03:11.680><c> run</c><00:03:11.959><c> code</c><00:03:12.440><c> inside</c><00:03:12.720><c> of</c><00:03:12.840><c> a</c><00:03:12.959><c> Docker</c>

00:03:13.470 --> 00:03:13.480 align:start position:0%
to try to run code inside of a Docker
 

00:03:13.480 --> 00:03:16.190 align:start position:0%
to try to run code inside of a Docker
container<00:03:14.480><c> okay</c><00:03:14.680><c> so</c><00:03:15.120><c> it</c><00:03:15.319><c> finally</c><00:03:15.799><c> executed</c>

00:03:16.190 --> 00:03:16.200 align:start position:0%
container okay so it finally executed
 

00:03:16.200 --> 00:03:18.350 align:start position:0%
container okay so it finally executed
the<00:03:16.319><c> code</c><00:03:16.959><c> uh</c><00:03:17.120><c> created</c><00:03:17.519><c> the</c><00:03:17.760><c> python</c><00:03:18.120><c> file</c>

00:03:18.350 --> 00:03:18.360 align:start position:0%
the code uh created the python file
 

00:03:18.360 --> 00:03:20.750 align:start position:0%
the code uh created the python file
under<00:03:18.560><c> the</c><00:03:18.720><c> coding</c><00:03:19.000><c> directory</c><00:03:19.480><c> over</c><00:03:19.760><c> here</c><00:03:20.400><c> and</c>

00:03:20.750 --> 00:03:20.760 align:start position:0%
under the coding directory over here and
 

00:03:20.760 --> 00:03:24.110 align:start position:0%
under the coding directory over here and
it<00:03:20.920><c> showed</c><00:03:21.280><c> me</c><00:03:21.760><c> a</c><00:03:22.040><c> plot</c><00:03:22.560><c> of</c><00:03:23.440><c> what</c><00:03:23.560><c> is</c><00:03:23.720><c> this</c><00:03:23.959><c> uh</c>

00:03:24.110 --> 00:03:24.120 align:start position:0%
it showed me a plot of what is this uh
 

00:03:24.120 --> 00:03:26.869 align:start position:0%
it showed me a plot of what is this uh
by<00:03:24.319><c> land</c><00:03:24.560><c> mass</c><00:03:24.799><c> the</c><00:03:24.920><c> top</c><00:03:25.080><c> 10</c><00:03:25.280><c> countries</c><00:03:26.159><c> okay</c>

00:03:26.869 --> 00:03:26.879 align:start position:0%
by land mass the top 10 countries okay
 

00:03:26.879 --> 00:03:29.750 align:start position:0%
by land mass the top 10 countries okay
apparently<00:03:27.640><c> Earth</c><00:03:28.000><c> is</c><00:03:28.120><c> a</c><00:03:28.319><c> country</c><00:03:29.200><c> perfect</c>

00:03:29.750 --> 00:03:29.760 align:start position:0%
apparently Earth is a country perfect
 

00:03:29.760 --> 00:03:30.670 align:start position:0%
apparently Earth is a country perfect
now<00:03:29.879><c> something</c><00:03:30.040><c> else</c><00:03:30.200><c> you</c><00:03:30.280><c> can</c><00:03:30.360><c> do</c><00:03:30.519><c> if</c><00:03:30.599><c> you</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
now something else you can do if you
 

00:03:30.680 --> 00:03:32.030 align:start position:0%
now something else you can do if you
don't<00:03:30.799><c> want</c><00:03:30.920><c> to</c><00:03:31.040><c> execute</c><00:03:31.400><c> code</c><00:03:31.560><c> at</c><00:03:31.680><c> all</c><00:03:31.920><c> which</c>

00:03:32.030 --> 00:03:32.040 align:start position:0%
don't want to execute code at all which
 

00:03:32.040 --> 00:03:33.949 align:start position:0%
don't want to execute code at all which
is<00:03:32.319><c> perfectly</c><00:03:32.760><c> reasonable</c><00:03:33.280><c> in</c><00:03:33.400><c> some</c><00:03:33.560><c> cases</c>

00:03:33.949 --> 00:03:33.959 align:start position:0%
is perfectly reasonable in some cases
 

00:03:33.959 --> 00:03:35.270 align:start position:0%
is perfectly reasonable in some cases
you<00:03:34.080><c> don't</c><00:03:34.239><c> necessarily</c><00:03:34.640><c> want</c><00:03:34.760><c> to</c><00:03:34.920><c> execute</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
you don't necessarily want to execute
 

00:03:35.280 --> 00:03:38.429 align:start position:0%
you don't necessarily want to execute
code<00:03:35.920><c> you</c><00:03:36.040><c> can</c><00:03:36.439><c> just</c><00:03:37.319><c> delete</c><00:03:37.799><c> this</c><00:03:38.000><c> part</c><00:03:38.280><c> and</c>

00:03:38.429 --> 00:03:38.439 align:start position:0%
code you can just delete this part and
 

00:03:38.439 --> 00:03:41.110 align:start position:0%
code you can just delete this part and
just<00:03:38.640><c> set</c><00:03:39.000><c> that</c><00:03:39.159><c> to</c><00:03:39.480><c> false</c><00:03:40.480><c> and</c><00:03:40.640><c> then</c><00:03:41.000><c> you</c>

00:03:41.110 --> 00:03:41.120 align:start position:0%
just set that to false and then you
 

00:03:41.120 --> 00:03:41.990 align:start position:0%
just set that to false and then you
don't<00:03:41.239><c> have</c><00:03:41.360><c> to</c><00:03:41.400><c> worry</c><00:03:41.599><c> about</c><00:03:41.680><c> it</c><00:03:41.840><c> either</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
don't have to worry about it either
 

00:03:42.000 --> 00:03:43.190 align:start position:0%
don't have to worry about it either
because<00:03:42.159><c> you're</c><00:03:42.280><c> not</c><00:03:42.439><c> actually</c><00:03:42.720><c> executing</c>

00:03:43.190 --> 00:03:43.200 align:start position:0%
because you're not actually executing
 

00:03:43.200 --> 00:03:58.670 align:start position:0%
because you're not actually executing
code<00:03:43.560><c> and</c><00:03:43.680><c> you</c><00:03:43.799><c> don't</c><00:03:44.159><c> have</c><00:03:44.280><c> to</c><00:03:44.439><c> do</c><00:03:44.640><c> anything</c>

00:03:58.670 --> 00:03:58.680 align:start position:0%
 
 

00:03:58.680 --> 00:04:01.350 align:start position:0%
 
else

00:04:01.350 --> 00:04:01.360 align:start position:0%
else
 

00:04:01.360 --> 00:04:03.589 align:start position:0%
else
now<00:04:01.560><c> what</c><00:04:01.640><c> if</c><00:04:01.799><c> we</c><00:04:01.959><c> wanted</c><00:04:02.239><c> to</c><00:04:02.400><c> use</c><00:04:02.599><c> Docker</c><00:04:03.400><c> well</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
now what if we wanted to use Docker well
 

00:04:03.599 --> 00:04:05.830 align:start position:0%
now what if we wanted to use Docker well
what<00:04:03.720><c> we</c><00:04:03.840><c> have</c><00:04:03.959><c> to</c><00:04:04.120><c> first</c><00:04:04.360><c> do</c><00:04:04.760><c> is</c><00:04:05.200><c> go</c><00:04:05.439><c> to</c><00:04:05.640><c> the</c>

00:04:05.830 --> 00:04:05.840 align:start position:0%
what we have to first do is go to the
 

00:04:05.840 --> 00:04:07.710 align:start position:0%
what we have to first do is go to the
website<00:04:06.239><c> to</c><00:04:06.519><c> download</c><00:04:06.840><c> the</c><00:04:06.959><c> software</c><00:04:07.400><c> and</c>

00:04:07.710 --> 00:04:07.720 align:start position:0%
website to download the software and
 

00:04:07.720 --> 00:04:09.470 align:start position:0%
website to download the software and
install<00:04:08.120><c> it</c><00:04:08.400><c> let's</c><00:04:08.560><c> see</c><00:04:08.680><c> how</c><00:04:08.799><c> we</c><00:04:08.920><c> do</c><00:04:09.079><c> that</c><00:04:09.360><c> okay</c>

00:04:09.470 --> 00:04:09.480 align:start position:0%
install it let's see how we do that okay
 

00:04:09.480 --> 00:04:11.229 align:start position:0%
install it let's see how we do that okay
so<00:04:09.599><c> now</c><00:04:09.760><c> how</c><00:04:09.840><c> do</c><00:04:10.000><c> we</c><00:04:10.120><c> get</c><00:04:10.280><c> Docker</c><00:04:10.720><c> well</c><00:04:10.920><c> Docker</c>

00:04:11.229 --> 00:04:11.239 align:start position:0%
so now how do we get Docker well Docker
 

00:04:11.239 --> 00:04:12.910 align:start position:0%
so now how do we get Docker well Docker
is<00:04:11.319><c> a</c><00:04:11.480><c> software</c><00:04:11.879><c> that</c><00:04:12.000><c> you</c><00:04:12.120><c> have</c><00:04:12.239><c> to</c><00:04:12.480><c> download</c>

00:04:12.910 --> 00:04:12.920 align:start position:0%
is a software that you have to download
 

00:04:12.920 --> 00:04:14.869 align:start position:0%
is a software that you have to download
so<00:04:13.079><c> the</c><00:04:13.200><c> first</c><00:04:13.400><c> thing</c><00:04:13.560><c> is</c><00:04:14.079><c> let's</c><00:04:14.319><c> go</c><00:04:14.439><c> to</c><00:04:14.599><c> the</c>

00:04:14.869 --> 00:04:14.879 align:start position:0%
so the first thing is let's go to the
 

00:04:14.879 --> 00:04:17.710 align:start position:0%
so the first thing is let's go to the
website<00:04:15.400><c> and</c><00:04:15.640><c> that</c><00:04:15.799><c> is</c><00:04:16.000><c> to</c><00:04:16.320><c> doer.</c><00:04:17.320><c> so</c><00:04:17.440><c> if</c><00:04:17.560><c> you</c>

00:04:17.710 --> 00:04:17.720 align:start position:0%
website and that is to doer. so if you
 

00:04:17.720 --> 00:04:20.909 align:start position:0%
website and that is to doer. so if you
come<00:04:17.919><c> here</c><00:04:18.160><c> click</c><00:04:18.359><c> on</c><00:04:18.919><c> this</c><00:04:19.639><c> uh</c><00:04:19.880><c> search</c><00:04:20.280><c> page</c>

00:04:20.909 --> 00:04:20.919 align:start position:0%
come here click on this uh search page
 

00:04:20.919 --> 00:04:23.070 align:start position:0%
come here click on this uh search page
then<00:04:21.040><c> you</c><00:04:21.280><c> just</c><00:04:21.560><c> click</c><00:04:21.880><c> download</c><00:04:22.360><c> for</c><00:04:22.560><c> Mac</c>

00:04:23.070 --> 00:04:23.080 align:start position:0%
then you just click download for Mac
 

00:04:23.080 --> 00:04:25.230 align:start position:0%
then you just click download for Mac
okay<00:04:23.360><c> I</c><00:04:23.440><c> already</c><00:04:23.720><c> have</c><00:04:23.800><c> it</c><00:04:23.919><c> downloaded</c><00:04:24.680><c> so</c>

00:04:25.230 --> 00:04:25.240 align:start position:0%
okay I already have it downloaded so
 

00:04:25.240 --> 00:04:27.590 align:start position:0%
okay I already have it downloaded so
it's<00:04:25.360><c> going</c><00:04:25.479><c> to</c><00:04:25.600><c> download</c><00:04:25.960><c> it</c><00:04:26.080><c> for</c><00:04:26.320><c> me</c><00:04:27.160><c> but</c>

00:04:27.590 --> 00:04:27.600 align:start position:0%
it's going to download it for me but
 

00:04:27.600 --> 00:04:29.230 align:start position:0%
it's going to download it for me but
once<00:04:27.759><c> you</c><00:04:27.880><c> have</c><00:04:28.000><c> it</c><00:04:28.160><c> downloaded</c><00:04:28.960><c> you</c><00:04:29.120><c> just</c>

00:04:29.230 --> 00:04:29.240 align:start position:0%
once you have it downloaded you just
 

00:04:29.240 --> 00:04:31.270 align:start position:0%
once you have it downloaded you just
simply<00:04:29.840><c> install</c><00:04:30.199><c> it</c><00:04:30.680><c> and</c><00:04:30.800><c> now</c><00:04:30.919><c> it's</c><00:04:31.039><c> just</c><00:04:31.160><c> a</c>

00:04:31.270 --> 00:04:31.280 align:start position:0%
simply install it and now it's just a
 

00:04:31.280 --> 00:04:32.909 align:start position:0%
simply install it and now it's just a
software<00:04:31.639><c> on</c><00:04:31.800><c> your</c><00:04:31.960><c> computer</c><00:04:32.560><c> and</c><00:04:32.680><c> whenever</c>

00:04:32.909 --> 00:04:32.919 align:start position:0%
software on your computer and whenever
 

00:04:32.919 --> 00:04:34.710 align:start position:0%
software on your computer and whenever
you're<00:04:33.160><c> done</c><00:04:33.400><c> installing</c><00:04:33.840><c> it</c><00:04:34.000><c> just</c><00:04:34.199><c> run</c><00:04:34.440><c> it</c>

00:04:34.710 --> 00:04:34.720 align:start position:0%
you're done installing it just run it
 

00:04:34.720 --> 00:04:37.230 align:start position:0%
you're done installing it just run it
and<00:04:34.960><c> for</c><00:04:35.160><c> the</c><00:04:35.360><c> first</c><00:04:35.680><c> time</c><00:04:35.919><c> you</c><00:04:36.400><c> run</c><00:04:36.639><c> it</c><00:04:37.000><c> it'll</c>

00:04:37.230 --> 00:04:37.240 align:start position:0%
and for the first time you run it it'll
 

00:04:37.240 --> 00:04:38.749 align:start position:0%
and for the first time you run it it'll
probably<00:04:37.479><c> ask</c><00:04:37.759><c> you</c><00:04:38.080><c> if</c><00:04:38.160><c> you</c><00:04:38.280><c> want</c><00:04:38.400><c> to</c><00:04:38.520><c> create</c>

00:04:38.749 --> 00:04:38.759 align:start position:0%
probably ask you if you want to create
 

00:04:38.759 --> 00:04:40.629 align:start position:0%
probably ask you if you want to create
an<00:04:38.919><c> account</c><00:04:39.520><c> I</c><00:04:39.639><c> didn't</c><00:04:39.880><c> create</c><00:04:40.080><c> an</c><00:04:40.240><c> account</c><00:04:40.560><c> as</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
an account I didn't create an account as
 

00:04:40.639 --> 00:04:42.629 align:start position:0%
an account I didn't create an account as
you<00:04:40.720><c> can</c><00:04:40.840><c> see</c><00:04:41.080><c> here</c><00:04:41.280><c> it</c><00:04:41.400><c> says</c><00:04:41.759><c> I'm</c><00:04:41.960><c> not</c><00:04:42.240><c> signed</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
you can see here it says I'm not signed
 

00:04:42.639 --> 00:04:45.430 align:start position:0%
you can see here it says I'm not signed
in<00:04:43.320><c> I</c><00:04:43.479><c> just</c><00:04:44.000><c> I</c><00:04:44.080><c> just</c><00:04:44.240><c> clicked</c><00:04:44.600><c> the</c><00:04:44.960><c> option</c><00:04:45.199><c> to</c>

00:04:45.430 --> 00:04:45.440 align:start position:0%
in I just I just clicked the option to
 

00:04:45.440 --> 00:04:46.749 align:start position:0%
in I just I just clicked the option to
continue<00:04:45.840><c> as</c><00:04:45.919><c> a</c><00:04:46.039><c> guest</c><00:04:46.280><c> and</c><00:04:46.360><c> you</c><00:04:46.440><c> can</c><00:04:46.560><c> do</c><00:04:46.680><c> the</c>

00:04:46.749 --> 00:04:46.759 align:start position:0%
continue as a guest and you can do the
 

00:04:46.759 --> 00:04:48.710 align:start position:0%
continue as a guest and you can do the
same<00:04:46.960><c> thing</c><00:04:47.080><c> so</c><00:04:47.240><c> you</c><00:04:47.360><c> don't</c><00:04:47.560><c> have</c><00:04:47.680><c> to</c><00:04:48.240><c> run</c><00:04:48.479><c> an</c>

00:04:48.710 --> 00:04:48.720 align:start position:0%
same thing so you don't have to run an
 

00:04:48.720 --> 00:04:49.790 align:start position:0%
same thing so you don't have to run an
account<00:04:48.960><c> you</c><00:04:49.039><c> don't</c><00:04:49.160><c> have</c><00:04:49.280><c> to</c><00:04:49.479><c> create</c><00:04:49.680><c> an</c>

00:04:49.790 --> 00:04:49.800 align:start position:0%
account you don't have to create an
 

00:04:49.800 --> 00:04:51.350 align:start position:0%
account you don't have to create an
account<00:04:50.000><c> you</c><00:04:50.120><c> don't</c><00:04:50.199><c> have</c><00:04:50.280><c> to</c><00:04:50.400><c> run</c><00:04:50.560><c> it</c><00:04:51.000><c> with</c><00:04:51.160><c> an</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
account you don't have to run it with an
 

00:04:51.360 --> 00:04:53.150 align:start position:0%
account you don't have to run it with an
account<00:04:51.960><c> just</c><00:04:52.199><c> continue</c><00:04:52.560><c> as</c><00:04:52.680><c> a</c><00:04:52.800><c> guest</c><00:04:53.080><c> and</c>

00:04:53.150 --> 00:04:53.160 align:start position:0%
account just continue as a guest and
 

00:04:53.160 --> 00:04:55.430 align:start position:0%
account just continue as a guest and
then<00:04:53.320><c> we</c><00:04:53.400><c> can</c><00:04:54.000><c> see</c><00:04:54.199><c> what's</c><00:04:54.400><c> going</c><00:04:54.560><c> on</c><00:04:55.039><c> now</c><00:04:55.320><c> they</c>

00:04:55.430 --> 00:04:55.440 align:start position:0%
then we can see what's going on now they
 

00:04:55.440 --> 00:04:57.189 align:start position:0%
then we can see what's going on now they
have<00:04:55.560><c> a</c><00:04:55.680><c> couple</c><00:04:55.960><c> options</c><00:04:56.320><c> here</c><00:04:56.639><c> is</c><00:04:56.840><c> what</c><00:04:57.000><c> is</c><00:04:57.080><c> a</c>

00:04:57.189 --> 00:04:57.199 align:start position:0%
have a couple options here is what is a
 

00:04:57.199 --> 00:04:59.310 align:start position:0%
have a couple options here is what is a
container<00:04:57.680><c> how</c><00:04:57.759><c> do</c><00:04:57.880><c> I</c><00:04:58.000><c> run</c><00:04:58.160><c> a</c><00:04:58.280><c> container</c><00:04:59.199><c> um</c>

00:04:59.310 --> 00:04:59.320 align:start position:0%
container how do I run a container um
 

00:04:59.320 --> 00:05:00.830 align:start position:0%
container how do I run a container um
you<00:04:59.560><c> you</c><00:04:59.639><c> can</c><00:04:59.880><c> view</c><00:05:00.160><c> that</c><00:05:00.280><c> separately</c><00:05:00.639><c> I'm</c><00:05:00.720><c> not</c>

00:05:00.830 --> 00:05:00.840 align:start position:0%
you you can view that separately I'm not
 

00:05:00.840 --> 00:05:02.870 align:start position:0%
you you can view that separately I'm not
going<00:05:00.919><c> to</c><00:05:01.000><c> really</c><00:05:01.160><c> get</c><00:05:01.280><c> into</c><00:05:01.560><c> what</c><00:05:01.720><c> Docker</c><00:05:02.199><c> is</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
going to really get into what Docker is
 

00:05:02.880 --> 00:05:05.110 align:start position:0%
going to really get into what Docker is
but<00:05:03.080><c> whenever</c><00:05:03.400><c> you</c><00:05:03.720><c> execute</c><00:05:04.160><c> code</c><00:05:04.479><c> you</c><00:05:04.560><c> can</c>

00:05:05.110 --> 00:05:05.120 align:start position:0%
but whenever you execute code you can
 

00:05:05.120 --> 00:05:07.110 align:start position:0%
but whenever you execute code you can
have<00:05:05.280><c> it</c><00:05:05.440><c> inside</c><00:05:05.680><c> of</c><00:05:05.840><c> a</c><00:05:06.400><c> inside</c><00:05:06.639><c> of</c><00:05:06.759><c> a</c>

00:05:07.110 --> 00:05:07.120 align:start position:0%
have it inside of a inside of a
 

00:05:07.120 --> 00:05:09.310 align:start position:0%
have it inside of a inside of a
container<00:05:07.680><c> here</c><00:05:08.320><c> that</c><00:05:08.520><c> whenever</c><00:05:08.840><c> you</c><00:05:08.960><c> execute</c>

00:05:09.310 --> 00:05:09.320 align:start position:0%
container here that whenever you execute
 

00:05:09.320 --> 00:05:10.430 align:start position:0%
container here that whenever you execute
it<00:05:09.520><c> something</c><00:05:09.720><c> were</c><00:05:09.840><c> to</c><00:05:09.960><c> happen</c><00:05:10.199><c> well</c><00:05:10.320><c> it's</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
it something were to happen well it's
 

00:05:10.440 --> 00:05:12.390 align:start position:0%
it something were to happen well it's
just<00:05:10.600><c> contain</c><00:05:11.039><c> it's</c><00:05:11.240><c> just</c><00:05:11.479><c> within</c><00:05:12.199><c> this</c>

00:05:12.390 --> 00:05:12.400 align:start position:0%
just contain it's just within this
 

00:05:12.400 --> 00:05:13.830 align:start position:0%
just contain it's just within this
container<00:05:13.080><c> you</c><00:05:13.160><c> don't</c><00:05:13.320><c> have</c><00:05:13.400><c> to</c><00:05:13.520><c> worry</c><00:05:13.720><c> about</c>

00:05:13.830 --> 00:05:13.840 align:start position:0%
container you don't have to worry about
 

00:05:13.840 --> 00:05:16.550 align:start position:0%
container you don't have to worry about
running<00:05:14.120><c> it</c><00:05:14.360><c> locally</c><00:05:14.880><c> in</c><00:05:15.039><c> case</c><00:05:15.880><c> there's</c><00:05:16.240><c> some</c>

00:05:16.550 --> 00:05:16.560 align:start position:0%
running it locally in case there's some
 

00:05:16.560 --> 00:05:18.150 align:start position:0%
running it locally in case there's some
breach<00:05:16.880><c> of</c><00:05:17.080><c> security</c><00:05:17.520><c> and</c><00:05:17.639><c> that's</c><00:05:17.800><c> really</c><00:05:18.000><c> the</c>

00:05:18.150 --> 00:05:18.160 align:start position:0%
breach of security and that's really the
 

00:05:18.160 --> 00:05:21.029 align:start position:0%
breach of security and that's really the
whole<00:05:18.400><c> thing</c><00:05:18.919><c> why</c><00:05:19.680><c> they</c><00:05:19.840><c> are</c><00:05:20.120><c> wanting</c><00:05:20.479><c> to</c><00:05:20.840><c> by</c>

00:05:21.029 --> 00:05:21.039 align:start position:0%
whole thing why they are wanting to by
 

00:05:21.039 --> 00:05:23.510 align:start position:0%
whole thing why they are wanting to by
default<00:05:21.840><c> have</c><00:05:22.000><c> you</c><00:05:22.160><c> used</c><00:05:22.479><c> Docker</c><00:05:23.080><c> is</c><00:05:23.199><c> for</c>

00:05:23.510 --> 00:05:23.520 align:start position:0%
default have you used Docker is for
 

00:05:23.520 --> 00:05:27.150 align:start position:0%
default have you used Docker is for
security<00:05:24.000><c> purposes</c><00:05:25.000><c> so</c><00:05:25.360><c> with</c><00:05:25.479><c> that</c><00:05:25.680><c> said</c><00:05:26.600><c> now</c>

00:05:27.150 --> 00:05:27.160 align:start position:0%
security purposes so with that said now
 

00:05:27.160 --> 00:05:28.790 align:start position:0%
security purposes so with that said now
once<00:05:27.319><c> it's</c><00:05:27.520><c> running</c><00:05:27.960><c> like</c><00:05:28.080><c> if</c><00:05:28.160><c> you</c><00:05:28.240><c> have</c><00:05:28.360><c> a</c><00:05:28.479><c> Mac</c>

00:05:28.790 --> 00:05:28.800 align:start position:0%
once it's running like if you have a Mac
 

00:05:28.800 --> 00:05:30.790 align:start position:0%
once it's running like if you have a Mac
up<00:05:28.960><c> here</c><00:05:29.199><c> you</c><00:05:29.319><c> know</c><00:05:29.639><c> you</c><00:05:29.720><c> can</c><00:05:29.880><c> see</c><00:05:30.199><c> that</c><00:05:30.400><c> Docker</c>

00:05:30.790 --> 00:05:30.800 align:start position:0%
up here you know you can see that Docker
 

00:05:30.800 --> 00:05:33.189 align:start position:0%
up here you know you can see that Docker
desktop<00:05:31.160><c> is</c><00:05:31.360><c> running</c><00:05:32.360><c> that's</c><00:05:32.560><c> it</c><00:05:32.960><c> that's</c><00:05:33.120><c> all</c>

00:05:33.189 --> 00:05:33.199 align:start position:0%
desktop is running that's it that's all
 

00:05:33.199 --> 00:05:35.350 align:start position:0%
desktop is running that's it that's all
you<00:05:33.280><c> need</c><00:05:33.440><c> to</c><00:05:33.560><c> do</c><00:05:34.240><c> now</c><00:05:34.400><c> we</c><00:05:34.520><c> can</c><00:05:34.840><c> go</c><00:05:35.039><c> back</c><00:05:35.160><c> to</c><00:05:35.240><c> the</c>

00:05:35.350 --> 00:05:35.360 align:start position:0%
you need to do now we can go back to the
 

00:05:35.360 --> 00:05:37.950 align:start position:0%
you need to do now we can go back to the
code<00:05:35.600><c> that</c><00:05:35.720><c> we</c><00:05:35.919><c> had</c><00:05:36.840><c> okay</c><00:05:37.000><c> we</c><00:05:37.120><c> can</c><00:05:37.360><c> get</c><00:05:37.639><c> rid</c><00:05:37.840><c> of</c>

00:05:37.950 --> 00:05:37.960 align:start position:0%
code that we had okay we can get rid of
 

00:05:37.960 --> 00:05:40.629 align:start position:0%
code that we had okay we can get rid of
this<00:05:38.080><c> used</c><00:05:38.440><c> Docker</c><00:05:39.440><c> because</c><00:05:39.800><c> by</c><00:05:40.039><c> default</c><00:05:40.520><c> it's</c>

00:05:40.629 --> 00:05:40.639 align:start position:0%
this used Docker because by default it's
 

00:05:40.639 --> 00:05:43.350 align:start position:0%
this used Docker because by default it's
going<00:05:40.759><c> to</c><00:05:40.919><c> be</c><00:05:41.160><c> true</c><00:05:42.080><c> and</c><00:05:42.360><c> if</c><00:05:42.479><c> we</c><00:05:42.639><c> try</c><00:05:42.840><c> to</c><00:05:43.080><c> run</c>

00:05:43.350 --> 00:05:43.360 align:start position:0%
going to be true and if we try to run
 

00:05:43.360 --> 00:05:45.749 align:start position:0%
going to be true and if we try to run
this<00:05:43.600><c> again</c><00:05:44.039><c> Python</c><00:05:44.560><c> 3</c>

00:05:45.749 --> 00:05:45.759 align:start position:0%
this again Python 3
 

00:05:45.759 --> 00:05:48.590 align:start position:0%
this again Python 3
main.py<00:05:46.759><c> with</c><00:05:46.960><c> Docker</c><00:05:47.319><c> running</c><00:05:48.160><c> and</c><00:05:48.319><c> it's</c><00:05:48.479><c> now</c>

00:05:48.590 --> 00:05:48.600 align:start position:0%
main.py with Docker running and it's now
 

00:05:48.600 --> 00:05:51.870 align:start position:0%
main.py with Docker running and it's now
set<00:05:48.840><c> to</c><00:05:49.039><c> True</c><00:05:49.479><c> by</c><00:05:49.680><c> default</c><00:05:50.680><c> it</c><00:05:50.840><c> runs</c><00:05:51.639><c> right</c><00:05:51.800><c> as</c>

00:05:51.870 --> 00:05:51.880 align:start position:0%
set to True by default it runs right as
 

00:05:51.880 --> 00:05:54.469 align:start position:0%
set to True by default it runs right as
you<00:05:52.000><c> can</c><00:05:52.160><c> see</c><00:05:52.440><c> it</c><00:05:52.600><c> runs</c><00:05:53.080><c> if</c><00:05:53.199><c> it</c><00:05:53.520><c> didn't</c>

00:05:54.469 --> 00:05:54.479 align:start position:0%
you can see it runs if it didn't
 

00:05:54.479 --> 00:05:56.150 align:start position:0%
you can see it runs if it didn't
successfully<00:05:55.039><c> run</c><00:05:55.360><c> we</c><00:05:55.440><c> would</c><00:05:55.639><c> get</c><00:05:55.800><c> the</c><00:05:55.919><c> same</c>

00:05:56.150 --> 00:05:56.160 align:start position:0%
successfully run we would get the same
 

00:05:56.160 --> 00:05:58.550 align:start position:0%
successfully run we would get the same
error<00:05:56.520><c> before</c><00:05:57.160><c> because</c><00:05:57.720><c> we</c><00:05:57.880><c> didn't</c><00:05:58.280><c> have</c>

00:05:58.550 --> 00:05:58.560 align:start position:0%
error before because we didn't have
 

00:05:58.560 --> 00:06:01.150 align:start position:0%
error before because we didn't have
Docker<00:05:59.000><c> running</c><00:05:59.560><c> but</c><00:05:59.720><c> now</c><00:05:59.919><c> that</c><00:06:00.080><c> we</c><00:06:00.240><c> do</c><00:06:00.960><c> it's</c>

00:06:01.150 --> 00:06:01.160 align:start position:0%
Docker running but now that we do it's
 

00:06:01.160 --> 00:06:03.909 align:start position:0%
Docker running but now that we do it's
trying<00:06:01.440><c> to</c><00:06:01.680><c> actually</c><00:06:02.199><c> run</c><00:06:03.160><c> uh</c><00:06:03.400><c> it's</c><00:06:03.560><c> trying</c><00:06:03.759><c> to</c>

00:06:03.909 --> 00:06:03.919 align:start position:0%
trying to actually run uh it's trying to
 

00:06:03.919 --> 00:06:05.749 align:start position:0%
trying to actually run uh it's trying to
run<00:06:04.400><c> the</c><00:06:04.560><c> chat</c><00:06:04.880><c> that</c><00:06:05.000><c> I</c><00:06:05.080><c> want</c><00:06:05.360><c> with</c><00:06:05.520><c> between</c>

00:06:05.749 --> 00:06:05.759 align:start position:0%
run the chat that I want with between
 

00:06:05.759 --> 00:06:08.189 align:start position:0%
run the chat that I want with between
the<00:06:05.840><c> user</c><00:06:06.120><c> proxy</c><00:06:06.400><c> and</c><00:06:06.560><c> assistant</c><00:06:07.080><c> agent</c><00:06:08.080><c> okay</c>

00:06:08.189 --> 00:06:08.199 align:start position:0%
the user proxy and assistant agent okay
 

00:06:08.199 --> 00:06:09.670 align:start position:0%
the user proxy and assistant agent okay
I<00:06:08.280><c> know</c><00:06:08.440><c> this</c><00:06:08.520><c> was</c><00:06:08.639><c> a</c><00:06:08.759><c> quick</c><00:06:09.000><c> video</c><00:06:09.440><c> but</c><00:06:09.599><c> I</c>

00:06:09.670 --> 00:06:09.680 align:start position:0%
I know this was a quick video but I
 

00:06:09.680 --> 00:06:11.350 align:start position:0%
I know this was a quick video but I
needed<00:06:10.000><c> to</c><00:06:10.120><c> show</c><00:06:10.280><c> you</c><00:06:10.440><c> this</c><00:06:10.639><c> because</c><00:06:10.919><c> if</c><00:06:11.039><c> you</c>

00:06:11.350 --> 00:06:11.360 align:start position:0%
needed to show you this because if you
 

00:06:11.360 --> 00:06:13.230 align:start position:0%
needed to show you this because if you
happen<00:06:11.680><c> to</c><00:06:12.080><c> upgrade</c><00:06:12.599><c> and</c><00:06:12.720><c> you</c><00:06:12.840><c> didn't</c><00:06:13.039><c> know</c>

00:06:13.230 --> 00:06:13.240 align:start position:0%
happen to upgrade and you didn't know
 

00:06:13.240 --> 00:06:15.189 align:start position:0%
happen to upgrade and you didn't know
about<00:06:13.520><c> this</c><00:06:14.280><c> then</c><00:06:14.479><c> you</c><00:06:14.599><c> were</c><00:06:14.800><c> just</c><00:06:14.960><c> going</c><00:06:15.080><c> to</c>

00:06:15.189 --> 00:06:15.199 align:start position:0%
about this then you were just going to
 

00:06:15.199 --> 00:06:16.990 align:start position:0%
about this then you were just going to
wonder<00:06:15.599><c> what</c><00:06:15.720><c> in</c><00:06:15.800><c> the</c><00:06:15.919><c> world</c><00:06:16.080><c> is</c><00:06:16.280><c> going</c><00:06:16.479><c> on</c>

00:06:16.990 --> 00:06:17.000 align:start position:0%
wonder what in the world is going on
 

00:06:17.000 --> 00:06:18.909 align:start position:0%
wonder what in the world is going on
okay<00:06:17.160><c> not</c><00:06:17.360><c> maybe</c><00:06:17.520><c> not</c><00:06:17.680><c> all</c><00:06:17.759><c> of</c><00:06:17.880><c> you</c><00:06:18.400><c> but</c><00:06:18.680><c> even</c>

00:06:18.909 --> 00:06:18.919 align:start position:0%
okay not maybe not all of you but even
 

00:06:18.919 --> 00:06:20.430 align:start position:0%
okay not maybe not all of you but even
me<00:06:19.160><c> I</c><00:06:19.240><c> would</c><00:06:19.360><c> have</c><00:06:19.479><c> been</c><00:06:19.680><c> like</c><00:06:20.000><c> wait</c><00:06:20.199><c> why</c><00:06:20.319><c> was</c>

00:06:20.430 --> 00:06:20.440 align:start position:0%
me I would have been like wait why was
 

00:06:20.440 --> 00:06:21.870 align:start position:0%
me I would have been like wait why was
it<00:06:20.599><c> such</c><00:06:20.720><c> a</c><00:06:20.800><c> breaking</c><00:06:21.080><c> change</c><00:06:21.400><c> if</c><00:06:21.520><c> I</c><00:06:21.639><c> didn't</c>

00:06:21.870 --> 00:06:21.880 align:start position:0%
it such a breaking change if I didn't
 

00:06:21.880 --> 00:06:24.309 align:start position:0%
it such a breaking change if I didn't
see<00:06:22.759><c> that</c><00:06:23.000><c> now</c><00:06:23.160><c> they're</c><00:06:23.280><c> using</c><00:06:23.639><c> Docker</c><00:06:24.080><c> by</c>

00:06:24.309 --> 00:06:24.319 align:start position:0%
see that now they're using Docker by
 

00:06:24.319 --> 00:06:26.710 align:start position:0%
see that now they're using Docker by
default<00:06:24.680><c> for</c><00:06:24.840><c> code</c><00:06:25.080><c> execution</c><00:06:26.039><c> so</c><00:06:26.520><c> if</c><00:06:26.599><c> you</c>

00:06:26.710 --> 00:06:26.720 align:start position:0%
default for code execution so if you
 

00:06:26.720 --> 00:06:28.629 align:start position:0%
default for code execution so if you
don't<00:06:26.840><c> want</c><00:06:26.960><c> to</c><00:06:27.080><c> care</c><00:06:27.240><c> about</c><00:06:27.440><c> Docker</c><00:06:27.800><c> again</c><00:06:28.520><c> I</c>

00:06:28.629 --> 00:06:28.639 align:start position:0%
don't want to care about Docker again I
 

00:06:28.639 --> 00:06:29.790 align:start position:0%
don't want to care about Docker again I
just<00:06:28.880><c> had</c><00:06:28.960><c> a</c><00:06:29.039><c> couple</c><00:06:29.199><c> examp</c><00:06:29.440><c> examples</c><00:06:29.720><c> where</c>

00:06:29.790 --> 00:06:29.800 align:start position:0%
just had a couple examp examples where
 

00:06:29.800 --> 00:06:31.390 align:start position:0%
just had a couple examp examples where
you<00:06:29.919><c> just</c><00:06:30.000><c> set</c><00:06:30.160><c> the</c><00:06:30.280><c> property</c><00:06:30.639><c> use</c><00:06:30.919><c> Docker</c><00:06:31.199><c> to</c>

00:06:31.390 --> 00:06:31.400 align:start position:0%
you just set the property use Docker to
 

00:06:31.400 --> 00:06:34.550 align:start position:0%
you just set the property use Docker to
false<00:06:31.919><c> in</c><00:06:32.440><c> the</c><00:06:32.639><c> user</c><00:06:33.120><c> proxy</c><00:06:33.520><c> agent</c><00:06:34.120><c> so</c><00:06:34.319><c> that</c><00:06:34.479><c> if</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
false in the user proxy agent so that if
 

00:06:34.560 --> 00:06:36.710 align:start position:0%
false in the user proxy agent so that if
you<00:06:34.680><c> want</c><00:06:34.800><c> to</c><00:06:34.919><c> execute</c><00:06:35.319><c> code</c><00:06:35.960><c> you</c><00:06:36.280><c> just</c><00:06:36.440><c> do</c><00:06:36.560><c> it</c>

00:06:36.710 --> 00:06:36.720 align:start position:0%
you want to execute code you just do it
 

00:06:36.720 --> 00:06:37.749 align:start position:0%
you want to execute code you just do it
locally<00:06:37.080><c> you</c><00:06:37.160><c> don't</c><00:06:37.280><c> have</c><00:06:37.360><c> to</c><00:06:37.440><c> worry</c><00:06:37.639><c> about</c>

00:06:37.749 --> 00:06:37.759 align:start position:0%
locally you don't have to worry about
 

00:06:37.759 --> 00:06:39.629 align:start position:0%
locally you don't have to worry about
doing<00:06:37.919><c> it</c><00:06:38.039><c> inside</c><00:06:38.240><c> of</c><00:06:38.319><c> a</c><00:06:38.400><c> Docker</c><00:06:38.720><c> container</c><00:06:39.520><c> if</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
doing it inside of a Docker container if
 

00:06:39.639 --> 00:06:40.909 align:start position:0%
doing it inside of a Docker container if
you<00:06:39.759><c> have</c><00:06:39.960><c> any</c><00:06:40.080><c> more</c><00:06:40.280><c> questions</c><00:06:40.639><c> or</c><00:06:40.759><c> if</c><00:06:40.840><c> you</c>

00:06:40.909 --> 00:06:40.919 align:start position:0%
you have any more questions or if you
 

00:06:40.919 --> 00:06:43.070 align:start position:0%
you have any more questions or if you
have<00:06:41.039><c> any</c><00:06:41.280><c> comments</c><00:06:41.680><c> please</c><00:06:41.919><c> post</c><00:06:42.120><c> them</c><00:06:42.319><c> below</c>

00:06:43.070 --> 00:06:43.080 align:start position:0%
have any comments please post them below
 

00:06:43.080 --> 00:06:45.909 align:start position:0%
have any comments please post them below
now<00:06:43.759><c> one</c><00:06:43.960><c> more</c><00:06:44.240><c> thing</c><00:06:44.479><c> is</c><00:06:44.800><c> I</c><00:06:44.919><c> have</c><00:06:45.039><c> to</c><00:06:45.240><c> update</c>

00:06:45.909 --> 00:06:45.919 align:start position:0%
now one more thing is I have to update
 

00:06:45.919 --> 00:06:48.309 align:start position:0%
now one more thing is I have to update
all<00:06:46.039><c> of</c><00:06:46.199><c> my</c><00:06:46.400><c> projects</c><00:06:46.919><c> okay</c><00:06:47.440><c> so</c><00:06:48.080><c> by</c><00:06:48.240><c> the</c>

00:06:48.309 --> 00:06:48.319 align:start position:0%
all of my projects okay so by the
 

00:06:48.319 --> 00:06:51.189 align:start position:0%
all of my projects okay so by the
weekend<00:06:49.000><c> they'll</c><00:06:49.240><c> all</c><00:06:49.400><c> be</c><00:06:49.560><c> updated</c><00:06:50.199><c> so</c><00:06:50.479><c> if</c><00:06:50.599><c> you</c>

00:06:51.189 --> 00:06:51.199 align:start position:0%
weekend they'll all be updated so if you
 

00:06:51.199 --> 00:06:52.550 align:start position:0%
weekend they'll all be updated so if you
upgrade<00:06:51.639><c> and</c><00:06:51.759><c> then</c><00:06:51.840><c> you</c><00:06:51.919><c> try</c><00:06:52.039><c> to</c><00:06:52.160><c> run</c><00:06:52.360><c> one</c><00:06:52.479><c> of</c>

00:06:52.550 --> 00:06:52.560 align:start position:0%
upgrade and then you try to run one of
 

00:06:52.560 --> 00:06:53.909 align:start position:0%
upgrade and then you try to run one of
my<00:06:52.720><c> projects</c><00:06:52.960><c> on</c><00:06:53.160><c> GitHub</c><00:06:53.479><c> and</c><00:06:53.560><c> it</c><00:06:53.680><c> doesn't</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
my projects on GitHub and it doesn't
 

00:06:53.919 --> 00:06:56.790 align:start position:0%
my projects on GitHub and it doesn't
work<00:06:54.680><c> well</c><00:06:54.880><c> it's</c><00:06:55.120><c> probably</c><00:06:55.440><c> because</c><00:06:56.319><c> I</c><00:06:56.560><c> never</c>

00:06:56.790 --> 00:06:56.800 align:start position:0%
work well it's probably because I never
 

00:06:56.800 --> 00:06:58.189 align:start position:0%
work well it's probably because I never
really<00:06:57.000><c> used</c><00:06:57.240><c> it</c><00:06:57.440><c> because</c><00:06:57.599><c> it</c><00:06:57.720><c> didn't</c><00:06:57.960><c> really</c>

00:06:58.189 --> 00:06:58.199 align:start position:0%
really used it because it didn't really
 

00:06:58.199 --> 00:07:00.150 align:start position:0%
really used it because it didn't really
matter<00:06:58.560><c> before</c><00:06:58.840><c> but</c><00:06:59.000><c> now</c><00:06:59.120><c> it</c><00:06:59.199><c> does</c>

00:07:00.150 --> 00:07:00.160 align:start position:0%
matter before but now it does
 

00:07:00.160 --> 00:07:01.990 align:start position:0%
matter before but now it does
so<00:07:00.280><c> I</c><00:07:00.400><c> need</c><00:07:00.520><c> to</c><00:07:00.680><c> update</c><00:07:01.000><c> them</c><00:07:01.440><c> to</c><00:07:01.599><c> set</c><00:07:01.759><c> them</c><00:07:01.879><c> all</c>

00:07:01.990 --> 00:07:02.000 align:start position:0%
so I need to update them to set them all
 

00:07:02.000 --> 00:07:03.710 align:start position:0%
so I need to update them to set them all
to<00:07:02.160><c> false</c><00:07:02.520><c> by</c><00:07:02.680><c> default</c><00:07:03.199><c> and</c><00:07:03.319><c> then</c><00:07:03.479><c> that</c><00:07:03.560><c> will</c>

00:07:03.710 --> 00:07:03.720 align:start position:0%
to false by default and then that will
 

00:07:03.720 --> 00:07:06.070 align:start position:0%
to false by default and then that will
give<00:07:03.879><c> you</c><00:07:04.039><c> the</c><00:07:04.199><c> option</c><00:07:04.879><c> to</c><00:07:05.080><c> set</c><00:07:05.240><c> them</c><00:07:05.400><c> to</c><00:07:05.560><c> true</c>

00:07:06.070 --> 00:07:06.080 align:start position:0%
give you the option to set them to true
 

00:07:06.080 --> 00:07:08.309 align:start position:0%
give you the option to set them to true
if<00:07:06.319><c> you</c><00:07:06.800><c> if</c><00:07:06.879><c> you</c><00:07:07.000><c> want</c><00:07:07.160><c> to</c><00:07:07.879><c> thank</c><00:07:08.039><c> you</c><00:07:08.120><c> for</c>

00:07:08.309 --> 00:07:08.319 align:start position:0%
if you if you want to thank you for
 

00:07:08.319 --> 00:07:09.830 align:start position:0%
if you if you want to thank you for
watching<00:07:08.879><c> if</c><00:07:08.960><c> you</c><00:07:09.039><c> want</c><00:07:09.240><c> to</c><00:07:09.440><c> watch</c><00:07:09.639><c> more</c>

00:07:09.830 --> 00:07:09.840 align:start position:0%
watching if you want to watch more
 

00:07:09.840 --> 00:07:11.350 align:start position:0%
watching if you want to watch more
content<00:07:10.160><c> about</c><00:07:10.280><c> other</c><00:07:10.520><c> genen</c><00:07:10.800><c> please</c><00:07:11.039><c> click</c>

00:07:11.350 --> 00:07:11.360 align:start position:0%
content about other genen please click
 

00:07:11.360 --> 00:07:13.230 align:start position:0%
content about other genen please click
this<00:07:11.479><c> video</c><00:07:11.800><c> above</c><00:07:12.400><c> and</c><00:07:12.520><c> I'll</c><00:07:12.680><c> see</c><00:07:12.840><c> you</c><00:07:13.000><c> next</c>

00:07:13.230 --> 00:07:13.240 align:start position:0%
this video above and I'll see you next
 

00:07:13.240 --> 00:07:15.560 align:start position:0%
this video above and I'll see you next
time

