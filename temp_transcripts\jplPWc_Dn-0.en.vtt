WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.670 align:start position:0%
 
hi<00:00:00.269><c> guys</c><00:00:00.480><c> in</c><00:00:00.810><c> this</c><00:00:00.870><c> video</c><00:00:01.050><c> I'm</c><00:00:01.290><c> going</c><00:00:01.469><c> to</c><00:00:01.530><c> talk</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
hi guys in this video I'm going to talk
 

00:00:01.680 --> 00:00:02.720 align:start position:0%
hi guys in this video I'm going to talk
about<00:00:01.770><c> a</c><00:00:01.890><c> technique</c><00:00:02.220><c> called</c><00:00:02.250><c> literate</c>

00:00:02.720 --> 00:00:02.730 align:start position:0%
about a technique called literate
 

00:00:02.730 --> 00:00:03.949 align:start position:0%
about a technique called literate
programming<00:00:03.149><c> and</c><00:00:03.330><c> how</c><00:00:03.389><c> we</c><00:00:03.449><c> can</c><00:00:03.600><c> use</c><00:00:03.750><c> it</c><00:00:03.870><c> to</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
programming and how we can use it to
 

00:00:03.959 --> 00:00:05.210 align:start position:0%
programming and how we can use it to
write<00:00:04.049><c> documentation</c><00:00:04.650><c> that</c><00:00:04.680><c> doesn't</c><00:00:05.009><c> go</c><00:00:05.100><c> out</c>

00:00:05.210 --> 00:00:05.220 align:start position:0%
write documentation that doesn't go out
 

00:00:05.220 --> 00:00:07.039 align:start position:0%
write documentation that doesn't go out
of<00:00:05.310><c> date</c><00:00:05.430><c> this</c><00:00:05.819><c> video</c><00:00:05.970><c> is</c><00:00:06.150><c> can</c><00:00:06.270><c> be</c><00:00:06.390><c> fast</c><00:00:06.629><c> I</c><00:00:06.870><c> need</c>

00:00:07.039 --> 00:00:07.049 align:start position:0%
of date this video is can be fast I need
 

00:00:07.049 --> 00:00:08.360 align:start position:0%
of date this video is can be fast I need
you<00:00:07.170><c> to</c><00:00:07.259><c> read</c><00:00:07.440><c> and</c><00:00:07.589><c> listen</c><00:00:07.859><c> at</c><00:00:07.919><c> the</c><00:00:07.980><c> same</c><00:00:08.040><c> time</c>

00:00:08.360 --> 00:00:08.370 align:start position:0%
you to read and listen at the same time
 

00:00:08.370 --> 00:00:09.830 align:start position:0%
you to read and listen at the same time
I<00:00:08.580><c> believe</c><00:00:09.030><c> in</c><00:00:09.090><c> you</c><00:00:09.150><c> one</c><00:00:09.360><c> of</c><00:00:09.480><c> the</c><00:00:09.540><c> problems</c>

00:00:09.830 --> 00:00:09.840 align:start position:0%
I believe in you one of the problems
 

00:00:09.840 --> 00:00:11.540 align:start position:0%
I believe in you one of the problems
with<00:00:09.929><c> tech</c><00:00:10.139><c> documentation</c><00:00:10.769><c> API</c><00:00:11.099><c> Docs</c><00:00:11.370><c> -</c>

00:00:11.540 --> 00:00:11.550 align:start position:0%
with tech documentation API Docs -
 

00:00:11.550 --> 00:00:13.220 align:start position:0%
with tech documentation API Docs -
installation<00:00:12.000><c> instructions</c><00:00:12.450><c> etc</c><00:00:12.900><c> is</c><00:00:13.049><c> that</c>

00:00:13.220 --> 00:00:13.230 align:start position:0%
installation instructions etc is that
 

00:00:13.230 --> 00:00:15.499 align:start position:0%
installation instructions etc is that
they<00:00:13.469><c> gets</c><00:00:13.650><c> stale</c><00:00:13.980><c> api's</c><00:00:14.849><c> change</c><00:00:15.150><c> software</c>

00:00:15.499 --> 00:00:15.509 align:start position:0%
they gets stale api's change software
 

00:00:15.509 --> 00:00:16.880 align:start position:0%
they gets stale api's change software
and<00:00:15.599><c> plugins</c><00:00:15.900><c> also</c><00:00:16.139><c> their</c><00:00:16.289><c> interface</c><00:00:16.680><c> and</c>

00:00:16.880 --> 00:00:16.890 align:start position:0%
and plugins also their interface and
 

00:00:16.890 --> 00:00:18.769 align:start position:0%
and plugins also their interface and
commands<00:00:17.279><c> and</c><00:00:17.430><c> websites</c><00:00:17.910><c> go</c><00:00:18.119><c> away</c><00:00:18.300><c> as</c><00:00:18.660><c> you</c>

00:00:18.769 --> 00:00:18.779 align:start position:0%
commands and websites go away as you
 

00:00:18.779 --> 00:00:19.910 align:start position:0%
commands and websites go away as you
probably<00:00:18.990><c> know</c><00:00:19.140><c> this</c><00:00:19.260><c> is</c><00:00:19.410><c> called</c><00:00:19.619><c> software</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
probably know this is called software
 

00:00:19.920 --> 00:00:22.130 align:start position:0%
probably know this is called software
rot<00:00:20.189><c> to</c><00:00:20.760><c> solve</c><00:00:20.970><c> this</c><00:00:21.090><c> I</c><00:00:21.270><c> propose</c><00:00:21.660><c> we</c><00:00:21.840><c> write</c><00:00:21.990><c> our</c>

00:00:22.130 --> 00:00:22.140 align:start position:0%
rot to solve this I propose we write our
 

00:00:22.140 --> 00:00:23.570 align:start position:0%
rot to solve this I propose we write our
documentation<00:00:22.560><c> in</c><00:00:23.010><c> a</c><00:00:23.189><c> way</c><00:00:23.310><c> that</c><00:00:23.460><c> can</c><00:00:23.550><c> be</c>

00:00:23.570 --> 00:00:23.580 align:start position:0%
documentation in a way that can be
 

00:00:23.580 --> 00:00:25.939 align:start position:0%
documentation in a way that can be
executed<00:00:23.939><c> or</c><00:00:24.240><c> tested</c><00:00:24.720><c> before</c><00:00:25.560><c> I</c><00:00:25.590><c> show</c><00:00:25.740><c> you</c><00:00:25.769><c> my</c>

00:00:25.939 --> 00:00:25.949 align:start position:0%
executed or tested before I show you my
 

00:00:25.949 --> 00:00:27.380 align:start position:0%
executed or tested before I show you my
solution<00:00:26.130><c> I'd</c><00:00:26.609><c> like</c><00:00:26.789><c> to</c><00:00:26.880><c> talk</c><00:00:27.000><c> about</c><00:00:27.060><c> a</c><00:00:27.240><c> key</c>

00:00:27.380 --> 00:00:27.390 align:start position:0%
solution I'd like to talk about a key
 

00:00:27.390 --> 00:00:28.820 align:start position:0%
solution I'd like to talk about a key
concept<00:00:27.599><c> I've</c><00:00:27.900><c> used</c><00:00:28.109><c> called</c><00:00:28.500><c> literate</c>

00:00:28.820 --> 00:00:28.830 align:start position:0%
concept I've used called literate
 

00:00:28.830 --> 00:00:30.620 align:start position:0%
concept I've used called literate
programming<00:00:29.279><c> Don</c><00:00:29.640><c> Knuth</c><00:00:29.910><c> a</c><00:00:30.150><c> computing</c>

00:00:30.620 --> 00:00:30.630 align:start position:0%
programming Don Knuth a computing
 

00:00:30.630 --> 00:00:32.269 align:start position:0%
programming Don Knuth a computing
pioneer<00:00:31.050><c> known</c><00:00:31.289><c> for</c><00:00:31.439><c> his</c><00:00:31.560><c> books</c><00:00:31.800><c> the</c><00:00:32.009><c> art</c><00:00:32.160><c> of</c>

00:00:32.269 --> 00:00:32.279 align:start position:0%
pioneer known for his books the art of
 

00:00:32.279 --> 00:00:33.979 align:start position:0%
pioneer known for his books the art of
computer<00:00:32.489><c> programming</c><00:00:32.669><c> surreal</c><00:00:33.420><c> numbers</c><00:00:33.600><c> and</c>

00:00:33.979 --> 00:00:33.989 align:start position:0%
computer programming surreal numbers and
 

00:00:33.989 --> 00:00:36.410 align:start position:0%
computer programming surreal numbers and
for<00:00:34.500><c> his</c><00:00:34.620><c> invention</c><00:00:35.070><c> of</c><00:00:35.190><c> tech</c><00:00:35.430><c> typesetting</c><00:00:36.030><c> he</c>

00:00:36.410 --> 00:00:36.420 align:start position:0%
for his invention of tech typesetting he
 

00:00:36.420 --> 00:00:38.209 align:start position:0%
for his invention of tech typesetting he
also<00:00:36.540><c> popularized</c><00:00:36.960><c> Big</c><00:00:37.260><c> O</c><00:00:37.410><c> notation</c><00:00:37.620><c> we</c><00:00:38.190><c> have</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
also popularized Big O notation we have
 

00:00:38.219 --> 00:00:39.590 align:start position:0%
also popularized Big O notation we have
a<00:00:38.309><c> lot</c><00:00:38.489><c> to</c><00:00:38.579><c> thank</c><00:00:38.700><c> him</c><00:00:38.850><c> for</c><00:00:39.000><c> the</c><00:00:39.300><c> literate</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
a lot to thank him for the literate
 

00:00:39.600 --> 00:00:41.450 align:start position:0%
a lot to thank him for the literate
programming<00:00:40.050><c> paradigm</c><00:00:40.500><c> as</c><00:00:40.710><c> conceived</c><00:00:41.280><c> by</c>

00:00:41.450 --> 00:00:41.460 align:start position:0%
programming paradigm as conceived by
 

00:00:41.460 --> 00:00:43.250 align:start position:0%
programming paradigm as conceived by
Knuth<00:00:41.760><c> represents</c><00:00:42.719><c> a</c><00:00:42.840><c> move</c><00:00:43.020><c> away</c><00:00:43.140><c> from</c>

00:00:43.250 --> 00:00:43.260 align:start position:0%
Knuth represents a move away from
 

00:00:43.260 --> 00:00:45.200 align:start position:0%
Knuth represents a move away from
writing<00:00:43.770><c> programs</c><00:00:44.160><c> in</c><00:00:44.370><c> the</c><00:00:44.489><c> manner</c><00:00:44.670><c> and</c><00:00:44.760><c> order</c>

00:00:45.200 --> 00:00:45.210 align:start position:0%
writing programs in the manner and order
 

00:00:45.210 --> 00:00:47.180 align:start position:0%
writing programs in the manner and order
imposed<00:00:45.570><c> by</c><00:00:45.750><c> the</c><00:00:45.809><c> computer</c><00:00:46.260><c> and</c><00:00:46.530><c> instead</c>

00:00:47.180 --> 00:00:47.190 align:start position:0%
imposed by the computer and instead
 

00:00:47.190 --> 00:00:48.889 align:start position:0%
imposed by the computer and instead
enables<00:00:47.640><c> programmers</c><00:00:48.059><c> to</c><00:00:48.210><c> develop</c><00:00:48.239><c> programs</c>

00:00:48.889 --> 00:00:48.899 align:start position:0%
enables programmers to develop programs
 

00:00:48.899 --> 00:00:50.869 align:start position:0%
enables programmers to develop programs
in<00:00:49.110><c> the</c><00:00:49.469><c> order</c><00:00:49.800><c> demanded</c><00:00:50.219><c> by</c><00:00:50.340><c> the</c><00:00:50.370><c> logic</c><00:00:50.760><c> and</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
in the order demanded by the logic and
 

00:00:50.879 --> 00:00:51.819 align:start position:0%
in the order demanded by the logic and
flow<00:00:51.030><c> of</c><00:00:51.059><c> their</c><00:00:51.300><c> thoughts</c>

00:00:51.819 --> 00:00:51.829 align:start position:0%
flow of their thoughts
 

00:00:51.829 --> 00:00:54.170 align:start position:0%
flow of their thoughts
Knuth<00:00:52.829><c> wrote</c><00:00:53.039><c> his</c><00:00:53.160><c> first</c><00:00:53.399><c> version</c><00:00:53.550><c> for</c><00:00:53.850><c> Pascal</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
Knuth wrote his first version for Pascal
 

00:00:54.180 --> 00:00:55.880 align:start position:0%
Knuth wrote his first version for Pascal
in<00:00:54.360><c> modern</c><00:00:54.870><c> languages</c><00:00:55.260><c> you</c><00:00:55.469><c> can</c><00:00:55.620><c> write</c><00:00:55.770><c> your</c>

00:00:55.880 --> 00:00:55.890 align:start position:0%
in modern languages you can write your
 

00:00:55.890 --> 00:00:57.229 align:start position:0%
in modern languages you can write your
functions<00:00:56.219><c> in</c><00:00:56.340><c> any</c><00:00:56.460><c> order</c><00:00:56.789><c> so</c><00:00:56.820><c> modern</c>

00:00:57.229 --> 00:00:57.239 align:start position:0%
functions in any order so modern
 

00:00:57.239 --> 00:00:58.880 align:start position:0%
functions in any order so modern
literate<00:00:57.690><c> programming</c><00:00:58.020><c> focuses</c><00:00:58.500><c> on</c><00:00:58.649><c> writing</c>

00:00:58.880 --> 00:00:58.890 align:start position:0%
literate programming focuses on writing
 

00:00:58.890 --> 00:01:00.889 align:start position:0%
literate programming focuses on writing
for<00:00:59.160><c> humans</c><00:00:59.520><c> first</c><00:00:59.850><c> two</c><00:01:00.300><c> languages</c><00:01:00.690><c> you</c><00:01:00.809><c> may</c>

00:01:00.889 --> 00:01:00.899 align:start position:0%
for humans first two languages you may
 

00:01:00.899 --> 00:01:01.910 align:start position:0%
for humans first two languages you may
have<00:01:01.020><c> heard</c><00:01:01.199><c> of</c><00:01:01.289><c> support</c><00:01:01.559><c> this</c><00:01:01.620><c> model</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
have heard of support this model
 

00:01:01.920 --> 00:01:04.250 align:start position:0%
have heard of support this model
natively<00:01:02.219><c> CoffeeScript</c><00:01:03.090><c> and</c><00:01:03.210><c> Haskell</c><00:01:03.449><c> so</c>

00:01:04.250 --> 00:01:04.260 align:start position:0%
natively CoffeeScript and Haskell so
 

00:01:04.260 --> 00:01:05.479 align:start position:0%
natively CoffeeScript and Haskell so
what<00:01:04.379><c> does</c><00:01:04.470><c> this</c><00:01:04.589><c> hippie</c><00:01:04.830><c> paradigm</c><00:01:05.159><c> look</c><00:01:05.339><c> like</c>

00:01:05.479 --> 00:01:05.489 align:start position:0%
what does this hippie paradigm look like
 

00:01:05.489 --> 00:01:06.740 align:start position:0%
what does this hippie paradigm look like
literate<00:01:05.850><c> CoffeeScript</c><00:01:06.360><c> looks</c><00:01:06.570><c> like</c><00:01:06.720><c> a</c>

00:01:06.740 --> 00:01:06.750 align:start position:0%
literate CoffeeScript looks like a
 

00:01:06.750 --> 00:01:08.179 align:start position:0%
literate CoffeeScript looks like a
readme<00:01:06.990><c> file</c><00:01:07.290><c> interspersed</c><00:01:07.860><c> with</c><00:01:08.010><c> code</c>

00:01:08.179 --> 00:01:08.189 align:start position:0%
readme file interspersed with code
 

00:01:08.189 --> 00:01:10.250 align:start position:0%
readme file interspersed with code
examples<00:01:08.670><c> the</c><00:01:09.299><c> CoffeeScript</c><00:01:09.810><c> interpreter</c>

00:01:10.250 --> 00:01:10.260 align:start position:0%
examples the CoffeeScript interpreter
 

00:01:10.260 --> 00:01:12.260 align:start position:0%
examples the CoffeeScript interpreter
can<00:01:10.470><c> run</c><00:01:10.619><c> this</c><00:01:10.710><c> file</c><00:01:10.890><c> as</c><00:01:11.070><c> normal</c><00:01:11.369><c> this</c><00:01:12.119><c> is</c>

00:01:12.260 --> 00:01:12.270 align:start position:0%
can run this file as normal this is
 

00:01:12.270 --> 00:01:13.370 align:start position:0%
can run this file as normal this is
typical<00:01:12.630><c> of</c><00:01:12.689><c> how</c><00:01:12.810><c> we</c><00:01:12.869><c> do</c><00:01:13.080><c> literate</c>

00:01:13.370 --> 00:01:13.380 align:start position:0%
typical of how we do literate
 

00:01:13.380 --> 00:01:15.230 align:start position:0%
typical of how we do literate
programming<00:01:13.770><c> nowadays</c><00:01:14.100><c> by</c><00:01:14.640><c> simply</c><00:01:14.939><c> inserting</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
programming nowadays by simply inserting
 

00:01:15.240 --> 00:01:16.880 align:start position:0%
programming nowadays by simply inserting
executive<00:01:15.869><c> or</c><00:01:15.960><c> code</c><00:01:16.110><c> in</c><00:01:16.350><c> the</c><00:01:16.439><c> correct</c><00:01:16.680><c> order</c>

00:01:16.880 --> 00:01:16.890 align:start position:0%
executive or code in the correct order
 

00:01:16.890 --> 00:01:19.280 align:start position:0%
executive or code in the correct order
within<00:01:17.280><c> some</c><00:01:17.490><c> markup</c><00:01:17.850><c> Eve</c><00:01:18.420><c> is</c><00:01:18.630><c> an</c><00:01:18.750><c> example</c><00:01:19.140><c> of</c>

00:01:19.280 --> 00:01:19.290 align:start position:0%
within some markup Eve is an example of
 

00:01:19.290 --> 00:01:20.810 align:start position:0%
within some markup Eve is an example of
a<00:01:19.380><c> proper</c><00:01:19.710><c> literate</c><00:01:20.070><c> programming</c><00:01:20.460><c> language</c>

00:01:20.810 --> 00:01:20.820 align:start position:0%
a proper literate programming language
 

00:01:20.820 --> 00:01:22.399 align:start position:0%
a proper literate programming language
as<00:01:21.030><c> conceived</c><00:01:21.479><c> by</c><00:01:21.600><c> Knuth</c><00:01:21.869><c> code</c><00:01:22.170><c> can</c><00:01:22.320><c> be</c>

00:01:22.399 --> 00:01:22.409 align:start position:0%
as conceived by Knuth code can be
 

00:01:22.409 --> 00:01:24.080 align:start position:0%
as conceived by Knuth code can be
written<00:01:22.530><c> in</c><00:01:22.770><c> any</c><00:01:22.979><c> order</c><00:01:23.400><c> to</c><00:01:23.670><c> create</c><00:01:23.850><c> the</c><00:01:23.970><c> Eve</c>

00:01:24.080 --> 00:01:24.090 align:start position:0%
written in any order to create the Eve
 

00:01:24.090 --> 00:01:25.910 align:start position:0%
written in any order to create the Eve
site<00:01:24.330><c> a</c><00:01:24.540><c> literate</c><00:01:25.140><c> programming</c><00:01:25.470><c> document</c>

00:01:25.910 --> 00:01:25.920 align:start position:0%
site a literate programming document
 

00:01:25.920 --> 00:01:27.020 align:start position:0%
site a literate programming document
tells<00:01:26.100><c> the</c><00:01:26.189><c> story</c><00:01:26.490><c> with</c><00:01:26.670><c> your</c><00:01:26.759><c> source</c>

00:01:27.020 --> 00:01:27.030 align:start position:0%
tells the story with your source
 

00:01:27.030 --> 00:01:28.730 align:start position:0%
tells the story with your source
featuring<00:01:27.689><c> a</c><00:01:27.750><c> table</c><00:01:27.960><c> of</c><00:01:28.020><c> contents</c><00:01:28.409><c> text</c>

00:01:28.730 --> 00:01:28.740 align:start position:0%
featuring a table of contents text
 

00:01:28.740 --> 00:01:30.530 align:start position:0%
featuring a table of contents text
references<00:01:29.280><c> images</c><00:01:29.759><c> and</c><00:01:29.909><c> of</c><00:01:30.060><c> course</c><00:01:30.240><c> code</c>

00:01:30.530 --> 00:01:30.540 align:start position:0%
references images and of course code
 

00:01:30.540 --> 00:01:32.390 align:start position:0%
references images and of course code
these<00:01:31.229><c> elements</c><00:01:31.650><c> add</c><00:01:31.770><c> context</c><00:01:32.250><c> to</c><00:01:32.340><c> your</c>

00:01:32.390 --> 00:01:32.400 align:start position:0%
these elements add context to your
 

00:01:32.400 --> 00:01:34.340 align:start position:0%
these elements add context to your
program<00:01:32.850><c> making</c><00:01:33.420><c> the</c><00:01:33.600><c> intent</c><00:01:33.930><c> behind</c><00:01:34.079><c> your</c>

00:01:34.340 --> 00:01:34.350 align:start position:0%
program making the intent behind your
 

00:01:34.350 --> 00:01:36.890 align:start position:0%
program making the intent behind your
code<00:01:34.530><c> concrete</c><00:01:35.150><c> interested</c><00:01:36.150><c> users</c><00:01:36.299><c> and</c><00:01:36.689><c> maybe</c>

00:01:36.890 --> 00:01:36.900 align:start position:0%
code concrete interested users and maybe
 

00:01:36.900 --> 00:01:38.630 align:start position:0%
code concrete interested users and maybe
your<00:01:37.079><c> future</c><00:01:37.350><c> self</c><00:01:37.619><c> will</c><00:01:37.860><c> thank</c><00:01:37.920><c> you</c><00:01:38.250><c> this</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
your future self will thank you this
 

00:01:38.640 --> 00:01:40.340 align:start position:0%
your future self will thank you this
sounds<00:01:38.909><c> amazing</c><00:01:39.240><c> I</c><00:01:39.540><c> thought</c><00:01:39.750><c> to</c><00:01:39.840><c> myself</c><00:01:39.960><c> last</c>

00:01:40.340 --> 00:01:40.350 align:start position:0%
sounds amazing I thought to myself last
 

00:01:40.350 --> 00:01:41.960 align:start position:0%
sounds amazing I thought to myself last
month<00:01:40.530><c> so</c><00:01:41.130><c> I</c><00:01:41.159><c> wrote</c><00:01:41.340><c> a</c><00:01:41.369><c> small</c><00:01:41.549><c> wrapper</c><00:01:41.850><c> that</c>

00:01:41.960 --> 00:01:41.970 align:start position:0%
month so I wrote a small wrapper that
 

00:01:41.970 --> 00:01:43.429 align:start position:0%
month so I wrote a small wrapper that
lets<00:01:42.240><c> you</c><00:01:42.360><c> write</c><00:01:42.509><c> executive</c><00:01:42.960><c> markdown</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
lets you write executive markdown
 

00:01:43.439 --> 00:01:45.200 align:start position:0%
lets you write executive markdown
with<00:01:43.560><c> any</c><00:01:43.799><c> language</c><00:01:44.040><c> embedded</c><00:01:44.729><c> and</c><00:01:44.970><c> blogged</c>

00:01:45.200 --> 00:01:45.210 align:start position:0%
with any language embedded and blogged
 

00:01:45.210 --> 00:01:46.789 align:start position:0%
with any language embedded and blogged
about<00:01:45.270><c> it</c><00:01:45.420><c> just</c><00:01:46.020><c> missed</c><00:01:46.259><c> hitting</c><00:01:46.439><c> the</c><00:01:46.619><c> front</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
about it just missed hitting the front
 

00:01:46.799 --> 00:01:49.340 align:start position:0%
about it just missed hitting the front
page<00:01:47.070><c> I</c><00:01:47.310><c> call</c><00:01:47.729><c> this</c><00:01:47.850><c> tool</c><00:01:48.060><c> plays</c><00:01:48.270><c> place</c><00:01:49.110><c> code</c>

00:01:49.340 --> 00:01:49.350 align:start position:0%
page I call this tool plays place code
 

00:01:49.350 --> 00:01:51.380 align:start position:0%
page I call this tool plays place code
is<00:01:49.560><c> just</c><00:01:49.829><c> regular</c><00:01:50.009><c> code</c><00:01:50.340><c> fenced</c><00:01:50.670><c> markdown</c><00:01:51.060><c> you</c>

00:01:51.380 --> 00:01:51.390 align:start position:0%
is just regular code fenced markdown you
 

00:01:51.390 --> 00:01:52.639 align:start position:0%
is just regular code fenced markdown you
can<00:01:51.540><c> render</c><00:01:51.720><c> it</c><00:01:51.840><c> as</c><00:01:51.960><c> documentation</c><00:01:52.500><c> or</c>

00:01:52.639 --> 00:01:52.649 align:start position:0%
can render it as documentation or
 

00:01:52.649 --> 00:01:55.010 align:start position:0%
can render it as documentation or
execute<00:01:53.159><c> it</c><00:01:53.280><c> as</c><00:01:53.399><c> code</c><00:01:53.640><c> by</c><00:01:54.540><c> the</c><00:01:54.600><c> way</c><00:01:54.780><c> if</c><00:01:54.930><c> you</c>

00:01:55.010 --> 00:01:55.020 align:start position:0%
execute it as code by the way if you
 

00:01:55.020 --> 00:01:56.149 align:start position:0%
execute it as code by the way if you
think<00:01:55.049><c> I</c><00:01:55.259><c> should</c><00:01:55.380><c> have</c><00:01:55.470><c> a</c><00:01:55.530><c> semicolon</c><00:01:56.009><c> at</c><00:01:56.100><c> the</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
think I should have a semicolon at the
 

00:01:56.159 --> 00:01:57.620 align:start position:0%
think I should have a semicolon at the
end<00:01:56.219><c> of</c><00:01:56.369><c> the</c><00:01:56.430><c> JavaScript</c><00:01:56.820><c> example</c><00:01:57.210><c> take</c><00:01:57.600><c> a</c>

00:01:57.620 --> 00:01:57.630 align:start position:0%
end of the JavaScript example take a
 

00:01:57.630 --> 00:01:58.880 align:start position:0%
end of the JavaScript example take a
look<00:01:57.689><c> at</c><00:01:57.869><c> standard</c><00:01:58.170><c> Jack</c><00:01:58.290><c> as</c><00:01:58.409><c> your</c><00:01:58.530><c> mind</c><00:01:58.740><c> will</c>

00:01:58.880 --> 00:01:58.890 align:start position:0%
look at standard Jack as your mind will
 

00:01:58.890 --> 00:01:59.209 align:start position:0%
look at standard Jack as your mind will
be<00:01:58.920><c> blown</c>

00:01:59.209 --> 00:01:59.219 align:start position:0%
be blown
 

00:01:59.219 --> 00:02:01.160 align:start position:0%
be blown
what<00:01:59.670><c> blaze</c><00:01:59.850><c> does</c><00:02:00.090><c> is</c><00:02:00.299><c> very</c><00:02:00.329><c> simple</c><00:02:00.960><c> place</c>

00:02:01.160 --> 00:02:01.170 align:start position:0%
what blaze does is very simple place
 

00:02:01.170 --> 00:02:02.569 align:start position:0%
what blaze does is very simple place
strips<00:02:01.469><c> out</c><00:02:01.619><c> anything</c><00:02:01.890><c> that's</c><00:02:02.070><c> not</c><00:02:02.340><c> inside</c>

00:02:02.569 --> 00:02:02.579 align:start position:0%
strips out anything that's not inside
 

00:02:02.579 --> 00:02:04.520 align:start position:0%
strips out anything that's not inside
code<00:02:02.850><c> fences</c><00:02:03.240><c> then</c><00:02:03.750><c> passes</c><00:02:04.110><c> the</c><00:02:04.140><c> code</c><00:02:04.380><c> along</c>

00:02:04.520 --> 00:02:04.530 align:start position:0%
code fences then passes the code along
 

00:02:04.530 --> 00:02:06.109 align:start position:0%
code fences then passes the code along
to<00:02:04.680><c> the</c><00:02:04.770><c> selected</c><00:02:05.130><c> interpreter</c><00:02:05.579><c> it's</c><00:02:05.969><c> not</c>

00:02:06.109 --> 00:02:06.119 align:start position:0%
to the selected interpreter it's not
 

00:02:06.119 --> 00:02:07.819 align:start position:0%
to the selected interpreter it's not
magic<00:02:06.450><c> but</c><00:02:06.630><c> it's</c><00:02:07.020><c> a</c><00:02:07.049><c> powerful</c><00:02:07.229><c> idea</c><00:02:07.380><c> it's</c><00:02:07.740><c> just</c>

00:02:07.819 --> 00:02:07.829 align:start position:0%
magic but it's a powerful idea it's just
 

00:02:07.829 --> 00:02:09.469 align:start position:0%
magic but it's a powerful idea it's just
a<00:02:08.160><c> short</c><00:02:08.429><c> shell</c><00:02:08.789><c> script</c><00:02:09.090><c> with</c><00:02:09.360><c> no</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
a short shell script with no
 

00:02:09.479 --> 00:02:11.180 align:start position:0%
a short shell script with no
dependencies<00:02:10.020><c> and</c><00:02:10.200><c> I've</c><00:02:10.619><c> released</c><00:02:10.770><c> it</c><00:02:11.009><c> under</c>

00:02:11.180 --> 00:02:11.190 align:start position:0%
dependencies and I've released it under
 

00:02:11.190 --> 00:02:12.710 align:start position:0%
dependencies and I've released it under
an<00:02:11.280><c> MIT</c><00:02:11.520><c> license</c><00:02:11.970><c> this</c><00:02:12.270><c> means</c><00:02:12.510><c> you</c><00:02:12.599><c> can</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
an MIT license this means you can
 

00:02:12.720 --> 00:02:13.900 align:start position:0%
an MIT license this means you can
distribute<00:02:13.110><c> it</c><00:02:13.140><c> with</c><00:02:13.260><c> your</c><00:02:13.349><c> containers</c>

00:02:13.900 --> 00:02:13.910 align:start position:0%
distribute it with your containers
 

00:02:13.910 --> 00:02:15.280 align:start position:0%
distribute it with your containers
better<00:02:14.060><c> in</c><00:02:14.210><c> your</c><00:02:14.240><c> app</c><00:02:14.420><c> and</c><00:02:14.630><c> run</c><00:02:14.810><c> it</c><00:02:14.930><c> anywhere</c>

00:02:15.280 --> 00:02:15.290 align:start position:0%
better in your app and run it anywhere
 

00:02:15.290 --> 00:02:16.930 align:start position:0%
better in your app and run it anywhere
SH<00:02:15.620><c> can</c><00:02:15.860><c> run</c><00:02:16.040><c> back</c><00:02:16.370><c> to</c><00:02:16.460><c> our</c><00:02:16.550><c> documentation</c>

00:02:16.930 --> 00:02:16.940 align:start position:0%
SH can run back to our documentation
 

00:02:16.940 --> 00:02:18.970 align:start position:0%
SH can run back to our documentation
problem<00:02:17.390><c> I</c><00:02:17.540><c> realized</c><00:02:18.170><c> that</c><00:02:18.260><c> I</c><00:02:18.320><c> already</c><00:02:18.560><c> have</c>

00:02:18.970 --> 00:02:18.980 align:start position:0%
problem I realized that I already have
 

00:02:18.980 --> 00:02:20.200 align:start position:0%
problem I realized that I already have
this<00:02:19.100><c> tool</c><00:02:19.340><c> that</c><00:02:19.550><c> can</c><00:02:19.610><c> be</c><00:02:19.730><c> used</c><00:02:19.850><c> to</c><00:02:19.940><c> make</c><00:02:20.060><c> our</c>

00:02:20.200 --> 00:02:20.210 align:start position:0%
this tool that can be used to make our
 

00:02:20.210 --> 00:02:22.330 align:start position:0%
this tool that can be used to make our
documentation<00:02:20.630><c> verifiable</c><00:02:21.290><c> a</c><00:02:21.470><c> testable</c><00:02:22.100><c> dog</c>

00:02:22.330 --> 00:02:22.340 align:start position:0%
documentation verifiable a testable dog
 

00:02:22.340 --> 00:02:24.040 align:start position:0%
documentation verifiable a testable dog
looks<00:02:22.700><c> exactly</c><00:02:23.000><c> like</c><00:02:23.210><c> the</c><00:02:23.420><c> documentation</c><00:02:23.960><c> of</c>

00:02:24.040 --> 00:02:24.050 align:start position:0%
looks exactly like the documentation of
 

00:02:24.050 --> 00:02:26.110 align:start position:0%
looks exactly like the documentation of
even<00:02:24.260><c> writing</c><00:02:24.560><c> all</c><00:02:24.650><c> along</c><00:02:24.890><c> in</c><00:02:25.220><c> fact</c><00:02:25.460><c> this</c><00:02:25.910><c> file</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
even writing all along in fact this file
 

00:02:26.120 --> 00:02:27.700 align:start position:0%
even writing all along in fact this file
only<00:02:26.300><c> has</c><00:02:26.390><c> one</c><00:02:26.570><c> small</c><00:02:26.720><c> change</c><00:02:27.020><c> it's</c><00:02:27.470><c> got</c><00:02:27.590><c> the</c>

00:02:27.700 --> 00:02:27.710 align:start position:0%
only has one small change it's got the
 

00:02:27.710 --> 00:02:28.990 align:start position:0%
only has one small change it's got the
blaze<00:02:27.860><c> shebang</c><00:02:28.220><c> at</c><00:02:28.340><c> the</c><00:02:28.430><c> top</c><00:02:28.610><c> of</c><00:02:28.730><c> the</c><00:02:28.820><c> file</c>

00:02:28.990 --> 00:02:29.000 align:start position:0%
blaze shebang at the top of the file
 

00:02:29.000 --> 00:02:30.730 align:start position:0%
blaze shebang at the top of the file
making<00:02:29.450><c> it</c><00:02:29.690><c> executable</c><00:02:30.170><c> so</c><00:02:30.500><c> what</c><00:02:30.590><c> have</c><00:02:30.680><c> I</c>

00:02:30.730 --> 00:02:30.740 align:start position:0%
making it executable so what have I
 

00:02:30.740 --> 00:02:31.840 align:start position:0%
making it executable so what have I
learned<00:02:30.920><c> through</c><00:02:31.100><c> this</c><00:02:31.250><c> there</c><00:02:31.760><c> are</c>

00:02:31.840 --> 00:02:31.850 align:start position:0%
learned through this there are
 

00:02:31.850 --> 00:02:33.670 align:start position:0%
learned through this there are
challenges<00:02:32.030><c> and</c><00:02:32.480><c> they</c><00:02:32.810><c> are</c><00:02:32.930><c> as</c><00:02:33.050><c> ever</c><00:02:33.200><c> of</c><00:02:33.350><c> human</c>

00:02:33.670 --> 00:02:33.680 align:start position:0%
challenges and they are as ever of human
 

00:02:33.680 --> 00:02:35.320 align:start position:0%
challenges and they are as ever of human
not<00:02:33.920><c> technical</c><00:02:34.370><c> if</c><00:02:34.760><c> you</c><00:02:34.910><c> want</c><00:02:35.030><c> to</c><00:02:35.120><c> make</c><00:02:35.210><c> you</c><00:02:35.300><c> a</c>

00:02:35.320 --> 00:02:35.330 align:start position:0%
not technical if you want to make you a
 

00:02:35.330 --> 00:02:36.730 align:start position:0%
not technical if you want to make you a
documentation<00:02:35.750><c> testable</c><00:02:36.200><c> you</c><00:02:36.380><c> can</c><00:02:36.500><c> do</c><00:02:36.590><c> so</c>

00:02:36.730 --> 00:02:36.740 align:start position:0%
documentation testable you can do so
 

00:02:36.740 --> 00:02:38.290 align:start position:0%
documentation testable you can do so
today<00:02:36.890><c> with</c><00:02:37.340><c> almost</c><00:02:37.550><c> no</c><00:02:37.760><c> changes</c><00:02:38.090><c> to</c><00:02:38.150><c> your</c>

00:02:38.290 --> 00:02:38.300 align:start position:0%
today with almost no changes to your
 

00:02:38.300 --> 00:02:42.370 align:start position:0%
today with almost no changes to your
code<00:02:38.420><c> bass</c><00:02:38.630><c> plays</c><00:02:39.350><c> even</c><00:02:39.800><c> supports</c><00:02:40.190><c> Lisp</c>

