WEBVTT
Kind: captions
Language: en

00:00:02.030 --> 00:00:06.470 align:start position:0%
 
hi<00:00:03.030><c> guys</c><00:00:04.520><c> today</c><00:00:05.520><c> we're</c><00:00:05.730><c> going</c><00:00:05.879><c> to</c><00:00:05.940><c> be</c><00:00:06.089><c> covering</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
hi guys today we're going to be covering
 

00:00:06.480 --> 00:00:10.419 align:start position:0%
hi guys today we're going to be covering
four<00:00:06.899><c> different</c><00:00:07.440><c> books</c><00:00:08.000><c> learning</c><00:00:09.000><c> my</c><00:00:09.389><c> sequel</c>

00:00:10.419 --> 00:00:10.429 align:start position:0%
four different books learning my sequel
 

00:00:10.429 --> 00:00:14.060 align:start position:0%
four different books learning my sequel
elasticsearch<00:00:11.429><c> the</c><00:00:11.969><c> definitive</c><00:00:12.269><c> guide</c><00:00:13.070><c> my</c>

00:00:14.060 --> 00:00:14.070 align:start position:0%
elasticsearch the definitive guide my
 

00:00:14.070 --> 00:00:21.740 align:start position:0%
elasticsearch the definitive guide my
sequel<00:00:14.730><c> the</c><00:00:15.240><c> big</c><00:00:15.480><c> one</c><00:00:16.400><c> and</c><00:00:19.430><c> MongoDB</c><00:00:20.449><c> so</c><00:00:21.449><c> I</c><00:00:21.480><c> want</c>

00:00:21.740 --> 00:00:21.750 align:start position:0%
sequel the big one and MongoDB so I want
 

00:00:21.750 --> 00:00:24.170 align:start position:0%
sequel the big one and MongoDB so I want
to<00:00:21.810><c> just</c><00:00:21.990><c> take</c><00:00:22.170><c> a</c><00:00:22.230><c> small</c><00:00:22.740><c> moment</c><00:00:23.670><c> to</c><00:00:24.000><c> talk</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
to just take a small moment to talk
 

00:00:24.180 --> 00:00:25.939 align:start position:0%
to just take a small moment to talk
about<00:00:24.330><c> each</c><00:00:24.630><c> one</c><00:00:24.660><c> of</c><00:00:24.900><c> them</c><00:00:25.170><c> if</c><00:00:25.769><c> you're</c>

00:00:25.939 --> 00:00:25.949 align:start position:0%
about each one of them if you're
 

00:00:25.949 --> 00:00:27.589 align:start position:0%
about each one of them if you're
interested<00:00:26.310><c> in</c><00:00:26.430><c> getting</c><00:00:26.670><c> into</c><00:00:26.820><c> databasing</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
interested in getting into databasing
 

00:00:27.599 --> 00:00:28.849 align:start position:0%
interested in getting into databasing
which<00:00:27.750><c> probably</c><00:00:27.960><c> a</c><00:00:28.109><c> lot</c><00:00:28.230><c> of</c><00:00:28.349><c> the</c><00:00:28.439><c> meta</c><00:00:28.619><c> base</c>

00:00:28.849 --> 00:00:28.859 align:start position:0%
which probably a lot of the meta base
 

00:00:28.859 --> 00:00:31.279 align:start position:0%
which probably a lot of the meta base
users<00:00:29.310><c> of</c><00:00:29.490><c> the</c><00:00:29.580><c> channel</c><00:00:29.939><c> are</c><00:00:30.090><c> you</c><00:00:30.960><c> might</c><00:00:31.109><c> want</c>

00:00:31.279 --> 00:00:31.289 align:start position:0%
users of the channel are you might want
 

00:00:31.289 --> 00:00:32.840 align:start position:0%
users of the channel are you might want
to<00:00:31.320><c> start</c><00:00:31.529><c> with</c><00:00:31.650><c> a</c><00:00:31.679><c> simple</c><00:00:31.949><c> book</c><00:00:32.219><c> like</c><00:00:32.430><c> this</c><00:00:32.640><c> a</c>

00:00:32.840 --> 00:00:32.850 align:start position:0%
to start with a simple book like this a
 

00:00:32.850 --> 00:00:35.569 align:start position:0%
to start with a simple book like this a
PHP<00:00:33.450><c> my</c><00:00:33.840><c> sequel</c><00:00:34.260><c> in</c><00:00:34.380><c> JavaScript</c><00:00:35.040><c> the</c><00:00:35.309><c> reason</c>

00:00:35.569 --> 00:00:35.579 align:start position:0%
PHP my sequel in JavaScript the reason
 

00:00:35.579 --> 00:00:37.250 align:start position:0%
PHP my sequel in JavaScript the reason
why<00:00:35.700><c> it's</c><00:00:35.880><c> really</c><00:00:36.180><c> good</c><00:00:36.390><c> is</c><00:00:36.540><c> because</c><00:00:36.899><c> it's</c><00:00:37.110><c> an</c>

00:00:37.250 --> 00:00:37.260 align:start position:0%
why it's really good is because it's an
 

00:00:37.260 --> 00:00:40.040 align:start position:0%
why it's really good is because it's an
introduction<00:00:37.950><c> a</c><00:00:38.790><c> lot</c><00:00:39.390><c> of</c><00:00:39.540><c> different</c>

00:00:40.040 --> 00:00:40.050 align:start position:0%
introduction a lot of different
 

00:00:40.050 --> 00:00:42.350 align:start position:0%
introduction a lot of different
frameworks<00:00:40.260><c> using</c><00:00:40.950><c> PHP</c><00:00:41.520><c> and</c><00:00:41.670><c> my</c><00:00:41.790><c> sequel</c><00:00:42.239><c> and</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
frameworks using PHP and my sequel and
 

00:00:42.360 --> 00:00:44.690 align:start position:0%
frameworks using PHP and my sequel and
JavaScript<00:00:42.960><c> such</c><00:00:43.200><c> as</c><00:00:43.379><c> WordPress</c><00:00:43.920><c> for</c><00:00:44.129><c> example</c>

00:00:44.690 --> 00:00:44.700 align:start position:0%
JavaScript such as WordPress for example
 

00:00:44.700 --> 00:00:46.430 align:start position:0%
JavaScript such as WordPress for example
so<00:00:45.329><c> that's</c><00:00:45.450><c> definitely</c><00:00:45.660><c> a</c><00:00:45.930><c> really</c><00:00:46.140><c> good</c><00:00:46.289><c> book</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
so that's definitely a really good book
 

00:00:46.440 --> 00:00:48.650 align:start position:0%
so that's definitely a really good book
to<00:00:46.620><c> start</c><00:00:46.770><c> off</c><00:00:47.010><c> with</c><00:00:47.219><c> if</c><00:00:47.730><c> you</c><00:00:47.940><c> want</c><00:00:48.149><c> to</c><00:00:48.270><c> get</c><00:00:48.480><c> the</c>

00:00:48.650 --> 00:00:48.660 align:start position:0%
to start off with if you want to get the
 

00:00:48.660 --> 00:00:51.590 align:start position:0%
to start off with if you want to get the
authoritative<00:00:49.260><c> guy</c><00:00:49.649><c> guide</c><00:00:50.190><c> by</c><00:00:50.579><c> Paul</c><00:00:50.910><c> Dubois</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
authoritative guy guide by Paul Dubois
 

00:00:51.600 --> 00:00:55.250 align:start position:0%
authoritative guy guide by Paul Dubois
that's<00:00:52.500><c> also</c><00:00:52.800><c> a</c><00:00:52.829><c> pretty</c><00:00:53.070><c> good</c><00:00:53.219><c> one</c><00:00:53.719><c> link</c><00:00:54.719><c> to</c>

00:00:55.250 --> 00:00:55.260 align:start position:0%
that's also a pretty good one link to
 

00:00:55.260 --> 00:00:57.380 align:start position:0%
that's also a pretty good one link to
the<00:00:55.350><c> description</c><00:00:55.949><c> in</c><00:00:56.489><c> the</c><00:00:56.640><c> description</c><00:00:56.670><c> to</c><00:00:57.360><c> a</c>

00:00:57.380 --> 00:00:57.390 align:start position:0%
the description in the description to a
 

00:00:57.390 --> 00:01:00.470 align:start position:0%
the description in the description to a
bunch<00:00:57.629><c> of</c><00:00:57.750><c> the</c><00:00:57.809><c> the</c><00:00:58.680><c> actual</c><00:00:59.160><c> books</c><00:00:59.820><c> on</c><00:00:59.969><c> Amazon</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
bunch of the the actual books on Amazon
 

00:01:00.480 --> 00:01:02.569 align:start position:0%
bunch of the the actual books on Amazon
another<00:01:01.289><c> pretty</c><00:01:01.590><c> good</c><00:01:01.829><c> one</c><00:01:01.980><c> a</c><00:01:02.160><c> lot</c><00:01:02.430><c> of</c><00:01:02.460><c> people</c>

00:01:02.569 --> 00:01:02.579 align:start position:0%
another pretty good one a lot of people
 

00:01:02.579 --> 00:01:06.200 align:start position:0%
another pretty good one a lot of people
are<00:01:02.850><c> using</c><00:01:03.030><c> these</c><00:01:03.359><c> days</c><00:01:03.830><c> MongoDB</c><00:01:05.030><c> and</c><00:01:06.030><c> that's</c>

00:01:06.200 --> 00:01:06.210 align:start position:0%
are using these days MongoDB and that's
 

00:01:06.210 --> 00:01:08.270 align:start position:0%
are using these days MongoDB and that's
a<00:01:06.299><c> pretty</c><00:01:06.479><c> good</c><00:01:06.570><c> good</c><00:01:07.020><c> one</c><00:01:07.200><c> by</c><00:01:07.380><c> christina</c><00:01:07.860><c> shot</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
a pretty good good one by christina shot
 

00:01:08.280 --> 00:01:10.070 align:start position:0%
a pretty good good one by christina shot
chador<00:01:08.670><c> oh</c><00:01:08.939><c> it</c><00:01:09.180><c> was</c><00:01:09.299><c> actually</c><00:01:09.600><c> a</c><00:01:09.659><c> pretty</c><00:01:09.869><c> good</c>

00:01:10.070 --> 00:01:10.080 align:start position:0%
chador oh it was actually a pretty good
 

00:01:10.080 --> 00:01:12.320 align:start position:0%
chador oh it was actually a pretty good
book<00:01:10.320><c> but</c><00:01:10.890><c> I've</c><00:01:11.159><c> only</c><00:01:11.280><c> gone</c><00:01:11.580><c> through</c><00:01:11.939><c> about</c>

00:01:12.320 --> 00:01:12.330 align:start position:0%
book but I've only gone through about
 

00:01:12.330 --> 00:01:13.760 align:start position:0%
book but I've only gone through about
half<00:01:12.570><c> of</c><00:01:12.689><c> it</c><00:01:12.840><c> because</c><00:01:13.110><c> the</c><00:01:13.200><c> second</c><00:01:13.500><c> half</c><00:01:13.619><c> is</c>

00:01:13.760 --> 00:01:13.770 align:start position:0%
half of it because the second half is
 

00:01:13.770 --> 00:01:15.649 align:start position:0%
half of it because the second half is
all<00:01:13.890><c> about</c><00:01:14.130><c> charting</c><00:01:14.640><c> your</c><00:01:14.700><c> database</c><00:01:15.299><c> if</c><00:01:15.509><c> it's</c>

00:01:15.649 --> 00:01:15.659 align:start position:0%
all about charting your database if it's
 

00:01:15.659 --> 00:01:17.600 align:start position:0%
all about charting your database if it's
really<00:01:15.990><c> really</c><00:01:16.200><c> big</c><00:01:16.470><c> but</c><00:01:17.130><c> a</c><00:01:17.159><c> lot</c><00:01:17.340><c> of</c><00:01:17.369><c> companies</c>

00:01:17.600 --> 00:01:17.610 align:start position:0%
really really big but a lot of companies
 

00:01:17.610 --> 00:01:19.940 align:start position:0%
really really big but a lot of companies
these<00:01:17.970><c> days</c><00:01:18.030><c> are</c><00:01:18.330><c> using</c><00:01:18.390><c> MongoDB</c><00:01:18.950><c> because</c>

00:01:19.940 --> 00:01:19.950 align:start position:0%
these days are using MongoDB because
 

00:01:19.950 --> 00:01:21.920 align:start position:0%
these days are using MongoDB because
it's<00:01:20.220><c> that</c><00:01:20.580><c> it's</c><00:01:20.729><c> got</c><00:01:20.880><c> really</c><00:01:21.090><c> fast</c><00:01:21.420><c> queries</c>

00:01:21.920 --> 00:01:21.930 align:start position:0%
it's that it's got really fast queries
 

00:01:21.930 --> 00:01:25.070 align:start position:0%
it's that it's got really fast queries
and<00:01:23.000><c> it</c><00:01:24.000><c> just</c><00:01:24.180><c> talks</c><00:01:24.390><c> to</c><00:01:24.570><c> you</c><00:01:24.659><c> all</c><00:01:24.810><c> about</c><00:01:24.960><c> the</c>

00:01:25.070 --> 00:01:25.080 align:start position:0%
and it just talks to you all about the
 

00:01:25.080 --> 00:01:26.660 align:start position:0%
and it just talks to you all about the
API<00:01:25.530><c> gives</c><00:01:25.830><c> you</c><00:01:25.979><c> a</c><00:01:26.009><c> lot</c><00:01:26.189><c> of</c><00:01:26.220><c> really</c><00:01:26.460><c> nice</c>

00:01:26.660 --> 00:01:26.670 align:start position:0%
API gives you a lot of really nice
 

00:01:26.670 --> 00:01:28.760 align:start position:0%
API gives you a lot of really nice
examples<00:01:27.330><c> and</c><00:01:27.509><c> stuff</c><00:01:27.720><c> so</c><00:01:27.869><c> I</c><00:01:27.930><c> highly</c><00:01:28.320><c> recommend</c>

00:01:28.760 --> 00:01:28.770 align:start position:0%
examples and stuff so I highly recommend
 

00:01:28.770 --> 00:01:32.390 align:start position:0%
examples and stuff so I highly recommend
that<00:01:29.299><c> and</c><00:01:30.590><c> finally</c><00:01:31.590><c> a</c><00:01:31.619><c> book</c><00:01:31.890><c> that</c><00:01:32.040><c> I</c><00:01:32.070><c> les</c>

00:01:32.390 --> 00:01:32.400 align:start position:0%
that and finally a book that I les
 

00:01:32.400 --> 00:01:33.890 align:start position:0%
that and finally a book that I les
recommend<00:01:33.030><c> unless</c><00:01:33.329><c> you</c><00:01:33.450><c> really</c><00:01:33.659><c> really</c>

00:01:33.890 --> 00:01:33.900 align:start position:0%
recommend unless you really really
 

00:01:33.900 --> 00:01:36.080 align:start position:0%
recommend unless you really really
needed<00:01:34.350><c> is</c><00:01:34.470><c> elastic</c><00:01:35.130><c> search</c><00:01:35.400><c> elastic</c><00:01:35.970><c> search</c>

00:01:36.080 --> 00:01:36.090 align:start position:0%
needed is elastic search elastic search
 

00:01:36.090 --> 00:01:37.550 align:start position:0%
needed is elastic search elastic search
is<00:01:36.299><c> something</c><00:01:36.630><c> like</c><00:01:36.750><c> if</c><00:01:36.930><c> a</c><00:01:37.049><c> company</c><00:01:37.380><c> like</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
is something like if a company like
 

00:01:37.560 --> 00:01:39.350 align:start position:0%
is something like if a company like
Amazon<00:01:37.799><c> wants</c><00:01:38.400><c> to</c><00:01:38.490><c> build</c><00:01:38.670><c> a</c><00:01:38.790><c> really</c><00:01:39.000><c> really</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
Amazon wants to build a really really
 

00:01:39.360 --> 00:01:42.560 align:start position:0%
Amazon wants to build a really really
big<00:01:39.630><c> search</c><00:01:40.130><c> search</c><00:01:41.130><c> engine</c><00:01:41.909><c> for</c><00:01:42.180><c> something</c>

00:01:42.560 --> 00:01:42.570 align:start position:0%
big search search engine for something
 

00:01:42.570 --> 00:01:45.319 align:start position:0%
big search search engine for something
like<00:01:42.689><c> products</c><00:01:43.350><c> different</c><00:01:44.030><c> searching</c><00:01:45.030><c> across</c>

00:01:45.319 --> 00:01:45.329 align:start position:0%
like products different searching across
 

00:01:45.329 --> 00:01:46.700 align:start position:0%
like products different searching across
different<00:01:45.720><c> products</c><00:01:46.110><c> and</c><00:01:46.229><c> making</c><00:01:46.470><c> it</c><00:01:46.560><c> really</c>

00:01:46.700 --> 00:01:46.710 align:start position:0%
different products and making it really
 

00:01:46.710 --> 00:01:47.960 align:start position:0%
different products and making it really
fast<00:01:47.070><c> you</c><00:01:47.250><c> can</c><00:01:47.399><c> kind</c><00:01:47.579><c> of</c><00:01:47.610><c> build</c><00:01:47.729><c> your</c><00:01:47.850><c> own</c>

00:01:47.960 --> 00:01:47.970 align:start position:0%
fast you can kind of build your own
 

00:01:47.970 --> 00:01:50.060 align:start position:0%
fast you can kind of build your own
search<00:01:48.509><c> engine</c><00:01:48.720><c> it's</c><00:01:49.079><c> just</c><00:01:49.290><c> much</c><00:01:49.500><c> more</c><00:01:49.530><c> much</c>

00:01:50.060 --> 00:01:50.070 align:start position:0%
search engine it's just much more much
 

00:01:50.070 --> 00:01:52.280 align:start position:0%
search engine it's just much more much
faster<00:01:50.579><c> and</c><00:01:50.729><c> with</c><00:01:50.939><c> really</c><00:01:51.630><c> big</c><00:01:51.840><c> amounts</c><00:01:52.200><c> of</c>

00:01:52.280 --> 00:01:52.290 align:start position:0%
faster and with really big amounts of
 

00:01:52.290 --> 00:01:55.819 align:start position:0%
faster and with really big amounts of
data<00:01:52.439><c> elastic</c><00:01:53.100><c> search</c><00:01:54.079><c> and</c><00:01:55.079><c> that's</c><00:01:55.619><c> also</c>

00:01:55.819 --> 00:01:55.829 align:start position:0%
data elastic search and that's also
 

00:01:55.829 --> 00:01:58.219 align:start position:0%
data elastic search and that's also
really<00:01:56.189><c> great</c><00:01:56.460><c> I</c><00:01:56.610><c> mean</c><00:01:56.670><c> it's</c><00:01:57.119><c> I</c><00:01:57.360><c> didn't</c><00:01:58.020><c> really</c>

00:01:58.219 --> 00:01:58.229 align:start position:0%
really great I mean it's I didn't really
 

00:01:58.229 --> 00:01:59.780 align:start position:0%
really great I mean it's I didn't really
use<00:01:58.259><c> this</c><00:01:58.619><c> book</c><00:01:58.890><c> so</c><00:01:59.100><c> much</c><00:01:59.250><c> just</c><00:01:59.490><c> because</c><00:01:59.610><c> it</c>

00:01:59.780 --> 00:01:59.790 align:start position:0%
use this book so much just because it
 

00:01:59.790 --> 00:02:01.710 align:start position:0%
use this book so much just because it
wasn't<00:02:00.060><c> practical</c><00:02:00.270><c> so</c><00:02:00.780><c> I</c><00:02:00.810><c> always</c><00:02:01.020><c> recommend</c>

00:02:01.710 --> 00:02:01.720 align:start position:0%
wasn't practical so I always recommend
 

00:02:01.720 --> 00:02:04.290 align:start position:0%
wasn't practical so I always recommend
we're<00:02:01.840><c> going</c><00:02:02.020><c> to</c><00:02:02.080><c> be</c><00:02:02.370><c> doing</c><00:02:03.370><c> something</c><00:02:04.030><c> or</c>

00:02:04.290 --> 00:02:04.300 align:start position:0%
we're going to be doing something or
 

00:02:04.300 --> 00:02:05.730 align:start position:0%
we're going to be doing something or
studying<00:02:04.720><c> a</c><00:02:04.780><c> new</c><00:02:04.900><c> technology</c><00:02:05.560><c> you</c><00:02:05.710><c> should</c>

00:02:05.730 --> 00:02:05.740 align:start position:0%
studying a new technology you should
 

00:02:05.740 --> 00:02:07.860 align:start position:0%
studying a new technology you should
study<00:02:06.100><c> just</c><00:02:06.550><c> in</c><00:02:06.760><c> time</c><00:02:07.000><c> the</c><00:02:07.330><c> moment</c><00:02:07.630><c> that</c><00:02:07.750><c> you</c>

00:02:07.860 --> 00:02:07.870 align:start position:0%
study just in time the moment that you
 

00:02:07.870 --> 00:02:09.780 align:start position:0%
study just in time the moment that you
need<00:02:08.020><c> to</c><00:02:08.140><c> study</c><00:02:08.350><c> that</c><00:02:08.500><c> specific</c><00:02:09.190><c> technology</c>

00:02:09.780 --> 00:02:09.790 align:start position:0%
need to study that specific technology
 

00:02:09.790 --> 00:02:10.980 align:start position:0%
need to study that specific technology
that's<00:02:09.970><c> when</c><00:02:10.119><c> you</c><00:02:10.210><c> should</c><00:02:10.390><c> get</c><00:02:10.479><c> the</c><00:02:10.630><c> books</c><00:02:10.840><c> on</c>

00:02:10.980 --> 00:02:10.990 align:start position:0%
that's when you should get the books on
 

00:02:10.990 --> 00:02:13.490 align:start position:0%
that's when you should get the books on
it<00:02:11.020><c> so</c><00:02:11.710><c> there</c><00:02:11.860><c> you</c><00:02:11.950><c> have</c><00:02:12.160><c> it</c><00:02:12.370><c> four</c><00:02:12.730><c> books</c><00:02:13.030><c> on</c>

00:02:13.490 --> 00:02:13.500 align:start position:0%
it so there you have it four books on
 

00:02:13.500 --> 00:02:15.660 align:start position:0%
it so there you have it four books on
databases<00:02:14.500><c> I'll</c><00:02:14.980><c> link</c><00:02:15.220><c> to</c><00:02:15.310><c> them</c><00:02:15.520><c> in</c><00:02:15.550><c> the</c>

00:02:15.660 --> 00:02:15.670 align:start position:0%
databases I'll link to them in the
 

00:02:15.670 --> 00:02:17.640 align:start position:0%
databases I'll link to them in the
description<00:02:16.030><c> as</c><00:02:16.450><c> well</c><00:02:16.480><c> and</c><00:02:16.930><c> hope</c><00:02:17.350><c> you</c><00:02:17.470><c> guys</c>

00:02:17.640 --> 00:02:17.650 align:start position:0%
description as well and hope you guys
 

00:02:17.650 --> 00:02:20.010 align:start position:0%
description as well and hope you guys
enjoy<00:02:18.010><c> them</c><00:02:18.190><c> as</c><00:02:18.340><c> much</c><00:02:18.490><c> as</c><00:02:18.670><c> I</c><00:02:18.700><c> did</c><00:02:19.060><c> as</c><00:02:19.180><c> well</c><00:02:19.420><c> have</c>

00:02:20.010 --> 00:02:20.020 align:start position:0%
enjoy them as much as I did as well have
 

00:02:20.020 --> 00:02:22.560 align:start position:0%
enjoy them as much as I did as well have
a<00:02:20.140><c> good</c><00:02:20.230><c> one</c>

