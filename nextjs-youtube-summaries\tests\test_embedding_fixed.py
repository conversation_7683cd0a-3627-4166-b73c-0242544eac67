#!/usr/bin/env python3
"""
Test script for the generate_embedding function with proper syntax.
"""

import asyncio
import sys
import os

# Add the current directory to sys.path to import gemini_methods
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_embedding():
    """Test the generate_embedding function."""
    try:
        from gemini_methods.genai_utils import generate_embedding
        
        # Test with a simple text
        test_text = "This is a test sentence for generating embeddings."
        
        print(f"Testing embedding generation for text: '{test_text}'")
        print("Calling generate_embedding...")
        
        result = await generate_embedding(
            text=test_text,
            model_name="text-embedding-004"
        )
        
        # Validate the result
        if not isinstance(result, dict):
            print(f"Error: Expected dict, got {type(result)}")
            return False
            
        if "values" not in result:
            print("Error: 'values' key not found in result")
            return False
            
        if not isinstance(result["values"], list):
            print(f"Error: Expected list for 'values', got {type(result['values'])}")
            return False
            
        if len(result["values"]) == 0:
            print("Error: Empty embedding values")
            return False
            
        # Print results
        print("✅ Embedding generation successful!")
        print(f"Model: {result['model']}")
        print(f"Dimensions: {result['dimensions']}")
        print(f"First 5 values: {result['values'][:5]}")
        
        if result.get("cost_estimate"):
            print(f"Cost estimate: ${result['cost_estimate']:.6f}")
        else:
            print("Cost estimate: Not available")
            
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Main function to run the test."""
    print("Starting embedding function test...")
    
    try:
        success = asyncio.run(test_embedding())
        result_text = "PASSED" if success else "FAILED"
        print(f"\nTest result: {result_text}")
        
        if success:
            print("🎉 The generate_embedding function is working correctly!")
        else:
            print("❌ The generate_embedding function has issues.")
            
    except Exception as e:
        print(f"Test runner error: {e}")
        print("❌ Test FAILED due to runner error.")

if __name__ == "__main__":
    main()
