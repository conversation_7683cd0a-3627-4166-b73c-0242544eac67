WEBVTT
Kind: captions
Language: en

00:00:00.780 --> 00:00:03.590 align:start position:0%
 
hey<00:00:01.260><c> there</c><00:00:01.500><c> we're</c><00:00:02.159><c> excited</c><00:00:02.580><c> to</c><00:00:02.820><c> show</c><00:00:03.000><c> you</c><00:00:03.120><c> the</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
hey there we're excited to show you the
 

00:00:03.600 --> 00:00:06.650 align:start position:0%
hey there we're excited to show you the
new<00:00:03.780><c> model</c><00:00:04.020><c> file</c><00:00:04.440><c> to</c><00:00:04.740><c> use</c><00:00:04.860><c> with</c><00:00:05.160><c> olama</c><00:00:05.700><c> it's</c><00:00:06.420><c> a</c>

00:00:06.650 --> 00:00:06.660 align:start position:0%
new model file to use with olama it's a
 

00:00:06.660 --> 00:00:08.330 align:start position:0%
new model file to use with olama it's a
simple<00:00:06.779><c> file</c><00:00:07.080><c> that</c><00:00:07.379><c> encapsulates</c><00:00:07.919><c> everything</c>

00:00:08.330 --> 00:00:08.340 align:start position:0%
simple file that encapsulates everything
 

00:00:08.340 --> 00:00:10.669 align:start position:0%
simple file that encapsulates everything
about<00:00:08.639><c> using</c><00:00:09.179><c> a</c><00:00:09.420><c> model</c><00:00:09.540><c> including</c><00:00:10.440><c> the</c><00:00:10.559><c> model</c>

00:00:10.669 --> 00:00:10.679 align:start position:0%
about using a model including the model
 

00:00:10.679 --> 00:00:12.470 align:start position:0%
about using a model including the model
weights<00:00:11.099><c> The</c><00:00:11.340><c> Prompt</c><00:00:11.639><c> and</c><00:00:12.120><c> all</c><00:00:12.360><c> the</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
weights The Prompt and all the
 

00:00:12.480 --> 00:00:14.930 align:start position:0%
weights The Prompt and all the
parameters<00:00:12.900><c> it's</c><00:00:13.740><c> now</c><00:00:13.920><c> super</c><00:00:14.219><c> easy</c><00:00:14.460><c> to</c><00:00:14.820><c> build</c>

00:00:14.930 --> 00:00:14.940 align:start position:0%
parameters it's now super easy to build
 

00:00:14.940 --> 00:00:17.090 align:start position:0%
parameters it's now super easy to build
something<00:00:15.240><c> that</c><00:00:15.540><c> works</c><00:00:15.839><c> for</c><00:00:16.199><c> a</c><00:00:16.560><c> specific</c><00:00:16.800><c> use</c>

00:00:17.090 --> 00:00:17.100 align:start position:0%
something that works for a specific use
 

00:00:17.100 --> 00:00:19.430 align:start position:0%
something that works for a specific use
case<00:00:17.340><c> and</c><00:00:18.060><c> then</c><00:00:18.180><c> share</c><00:00:18.420><c> that</c><00:00:18.660><c> model</c><00:00:18.840><c> file</c><00:00:19.199><c> with</c>

00:00:19.430 --> 00:00:19.440 align:start position:0%
case and then share that model file with
 

00:00:19.440 --> 00:00:20.990 align:start position:0%
case and then share that model file with
your<00:00:19.560><c> colleagues</c><00:00:19.920><c> and</c><00:00:20.160><c> they</c><00:00:20.520><c> can</c><00:00:20.699><c> have</c><00:00:20.820><c> the</c>

00:00:20.990 --> 00:00:21.000 align:start position:0%
your colleagues and they can have the
 

00:00:21.000 --> 00:00:23.090 align:start position:0%
your colleagues and they can have the
same<00:00:21.119><c> setup</c><00:00:21.539><c> right</c><00:00:22.080><c> away</c>

00:00:23.090 --> 00:00:23.100 align:start position:0%
same setup right away
 

00:00:23.100 --> 00:00:24.890 align:start position:0%
same setup right away
soon<00:00:23.520><c> you'll</c><00:00:24.060><c> also</c><00:00:24.300><c> be</c><00:00:24.420><c> able</c><00:00:24.539><c> to</c><00:00:24.660><c> share</c>

00:00:24.890 --> 00:00:24.900 align:start position:0%
soon you'll also be able to share
 

00:00:24.900 --> 00:00:27.109 align:start position:0%
soon you'll also be able to share
specific<00:00:25.320><c> trainings</c><00:00:25.800><c> and</c><00:00:26.220><c> maybe</c><00:00:26.640><c> even</c><00:00:26.820><c> the</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
specific trainings and maybe even the
 

00:00:27.119 --> 00:00:29.929 align:start position:0%
specific trainings and maybe even the
state<00:00:27.300><c> of</c><00:00:27.539><c> your</c><00:00:27.720><c> model</c><00:00:27.840><c> quickly</c><00:00:28.380><c> and</c><00:00:28.800><c> easily</c>

00:00:29.929 --> 00:00:29.939 align:start position:0%
state of your model quickly and easily
 

00:00:29.939 --> 00:00:32.330 align:start position:0%
state of your model quickly and easily
we're<00:00:30.480><c> working</c><00:00:30.720><c> on</c><00:00:31.080><c> a</c><00:00:31.199><c> registry</c><00:00:31.619><c> and</c><00:00:32.099><c> should</c>

00:00:32.330 --> 00:00:32.340 align:start position:0%
we're working on a registry and should
 

00:00:32.340 --> 00:00:33.590 align:start position:0%
we're working on a registry and should
have<00:00:32.520><c> something</c><00:00:32.700><c> that</c><00:00:33.000><c> you</c><00:00:33.120><c> can</c><00:00:33.180><c> push</c><00:00:33.360><c> your</c>

00:00:33.590 --> 00:00:33.600 align:start position:0%
have something that you can push your
 

00:00:33.600 --> 00:00:36.290 align:start position:0%
have something that you can push your
model<00:00:33.780><c> files</c><00:00:34.260><c> to</c><00:00:34.500><c> soon</c><00:00:34.920><c> and</c><00:00:35.760><c> then</c><00:00:35.820><c> it</c><00:00:35.940><c> becomes</c>

00:00:36.290 --> 00:00:36.300 align:start position:0%
model files to soon and then it becomes
 

00:00:36.300 --> 00:00:37.670 align:start position:0%
model files to soon and then it becomes
a<00:00:36.420><c> very</c><00:00:36.540><c> similar</c><00:00:36.840><c> experience</c><00:00:37.200><c> to</c><00:00:37.380><c> what</c><00:00:37.559><c> you</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
a very similar experience to what you
 

00:00:37.680 --> 00:00:40.670 align:start position:0%
a very similar experience to what you
have<00:00:37.860><c> today</c><00:00:38.219><c> with</c><00:00:38.760><c> docker</c><00:00:39.660><c> in</c><00:00:40.140><c> Docker</c><00:00:40.500><c> you</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
have today with docker in Docker you
 

00:00:40.680 --> 00:00:42.770 align:start position:0%
have today with docker in Docker you
work<00:00:40.860><c> with</c><00:00:41.100><c> layers</c><00:00:41.460><c> that</c><00:00:41.760><c> stack</c><00:00:42.000><c> on</c><00:00:42.420><c> top</c><00:00:42.540><c> of</c>

00:00:42.770 --> 00:00:42.780 align:start position:0%
work with layers that stack on top of
 

00:00:42.780 --> 00:00:44.889 align:start position:0%
work with layers that stack on top of
each<00:00:42.899><c> other</c><00:00:43.079><c> to</c><00:00:43.860><c> create</c><00:00:43.980><c> your</c><00:00:44.520><c> environment</c>

00:00:44.889 --> 00:00:44.899 align:start position:0%
each other to create your environment
 

00:00:44.899 --> 00:00:47.270 align:start position:0%
each other to create your environment
and<00:00:45.899><c> we</c><00:00:46.079><c> have</c><00:00:46.200><c> the</c><00:00:46.440><c> same</c><00:00:46.500><c> thing</c><00:00:46.680><c> with</c><00:00:46.920><c> Obama</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
and we have the same thing with Obama
 

00:00:47.280 --> 00:00:49.190 align:start position:0%
and we have the same thing with Obama
there's<00:00:48.000><c> a</c><00:00:48.180><c> layer</c><00:00:48.420><c> for</c><00:00:48.539><c> the</c><00:00:48.600><c> model</c><00:00:48.719><c> weights</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
there's a layer for the model weights
 

00:00:49.200 --> 00:00:50.990 align:start position:0%
there's a layer for the model weights
another<00:00:49.620><c> for</c><00:00:49.920><c> the</c><00:00:50.100><c> parameters</c><00:00:50.460><c> and</c><00:00:50.700><c> another</c>

00:00:50.990 --> 00:00:51.000 align:start position:0%
another for the parameters and another
 

00:00:51.000 --> 00:00:53.330 align:start position:0%
another for the parameters and another
for<00:00:51.360><c> the</c><00:00:51.480><c> prompt</c><00:00:52.200><c> when</c><00:00:52.680><c> you</c><00:00:52.860><c> share</c><00:00:53.039><c> a</c><00:00:53.219><c> model</c>

00:00:53.330 --> 00:00:53.340 align:start position:0%
for the prompt when you share a model
 

00:00:53.340 --> 00:00:55.549 align:start position:0%
for the prompt when you share a model
file<00:00:53.700><c> and</c><00:00:54.000><c> run</c><00:00:54.180><c> it</c><00:00:54.360><c> if</c><00:00:54.960><c> you</c><00:00:55.079><c> use</c><00:00:55.260><c> the</c><00:00:55.440><c> same</c>

00:00:55.549 --> 00:00:55.559 align:start position:0%
file and run it if you use the same
 

00:00:55.559 --> 00:00:57.229 align:start position:0%
file and run it if you use the same
model<00:00:55.739><c> only</c><00:00:56.100><c> the</c><00:00:56.340><c> layers</c><00:00:56.640><c> with</c><00:00:56.940><c> the</c><00:00:57.059><c> different</c>

00:00:57.229 --> 00:00:57.239 align:start position:0%
model only the layers with the different
 

00:00:57.239 --> 00:00:59.510 align:start position:0%
model only the layers with the different
parameters<00:00:57.719><c> or</c><00:00:57.960><c> prompts</c><00:00:58.320><c> get</c><00:00:58.500><c> downloaded</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
parameters or prompts get downloaded
 

00:00:59.520 --> 00:01:01.549 align:start position:0%
parameters or prompts get downloaded
soon<00:01:00.059><c> we'll</c><00:01:00.420><c> also</c><00:01:00.719><c> support</c><00:01:00.960><c> additional</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
soon we'll also support additional
 

00:01:01.559 --> 00:01:03.410 align:start position:0%
soon we'll also support additional
layers<00:01:01.860><c> for</c><00:01:02.160><c> training</c><00:01:02.579><c> and</c><00:01:02.879><c> maybe</c><00:01:03.120><c> other</c>

00:01:03.410 --> 00:01:03.420 align:start position:0%
layers for training and maybe other
 

00:01:03.420 --> 00:01:04.549 align:start position:0%
layers for training and maybe other
functionality

00:01:04.549 --> 00:01:04.559 align:start position:0%
functionality
 

00:01:04.559 --> 00:01:06.410 align:start position:0%
functionality
when<00:01:04.860><c> you</c><00:01:04.979><c> look</c><00:01:05.100><c> at</c><00:01:05.280><c> the</c><00:01:05.400><c> API</c><00:01:05.700><c> calls</c><00:01:06.119><c> that</c><00:01:06.240><c> go</c>

00:01:06.410 --> 00:01:06.420 align:start position:0%
when you look at the API calls that go
 

00:01:06.420 --> 00:01:08.149 align:start position:0%
when you look at the API calls that go
back<00:01:06.540><c> and</c><00:01:06.720><c> forth</c><00:01:07.020><c> when</c><00:01:07.500><c> you</c><00:01:07.619><c> grab</c><00:01:07.860><c> a</c><00:01:07.979><c> model</c>

00:01:08.149 --> 00:01:08.159 align:start position:0%
back and forth when you grab a model
 

00:01:08.159 --> 00:01:10.429 align:start position:0%
back and forth when you grab a model
file<00:01:08.520><c> you</c><00:01:09.060><c> see</c><00:01:09.180><c> that</c><00:01:09.479><c> it</c><00:01:09.659><c> looks</c><00:01:09.960><c> a</c><00:01:10.080><c> lot</c><00:01:10.200><c> like</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
file you see that it looks a lot like
 

00:01:10.439 --> 00:01:11.990 align:start position:0%
file you see that it looks a lot like
docker

00:01:11.990 --> 00:01:12.000 align:start position:0%
docker
 

00:01:12.000 --> 00:01:14.510 align:start position:0%
docker
when<00:01:12.540><c> we</c><00:01:12.659><c> started</c><00:01:12.960><c> building</c><00:01:13.140><c> olama</c><00:01:13.740><c> we</c><00:01:14.280><c> knew</c>

00:01:14.510 --> 00:01:14.520 align:start position:0%
when we started building olama we knew
 

00:01:14.520 --> 00:01:15.830 align:start position:0%
when we started building olama we knew
one<00:01:14.760><c> of</c><00:01:14.820><c> the</c><00:01:14.939><c> biggest</c><00:01:15.060><c> challenges</c><00:01:15.540><c> with</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
one of the biggest challenges with
 

00:01:15.840 --> 00:01:18.289 align:start position:0%
one of the biggest challenges with
getting<00:01:16.080><c> up</c><00:01:16.260><c> and</c><00:01:16.439><c> running</c><00:01:16.619><c> with</c><00:01:17.280><c> llms</c><00:01:17.939><c> on</c><00:01:18.180><c> your</c>

00:01:18.289 --> 00:01:18.299 align:start position:0%
getting up and running with llms on your
 

00:01:18.299 --> 00:01:20.870 align:start position:0%
getting up and running with llms on your
laptop<00:01:18.659><c> is</c><00:01:19.500><c> distribution</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
laptop is distribution
 

00:01:20.880 --> 00:01:22.969 align:start position:0%
laptop is distribution
finding<00:01:21.479><c> a</c><00:01:21.659><c> model</c><00:01:21.720><c> that</c><00:01:22.080><c> works</c><00:01:22.439><c> with</c><00:01:22.860><c> your</c>

00:01:22.969 --> 00:01:22.979 align:start position:0%
finding a model that works with your
 

00:01:22.979 --> 00:01:24.770 align:start position:0%
finding a model that works with your
setup<00:01:23.340><c> is</c><00:01:23.580><c> the</c><00:01:23.759><c> first</c><00:01:23.880><c> challenge</c>

00:01:24.770 --> 00:01:24.780 align:start position:0%
setup is the first challenge
 

00:01:24.780 --> 00:01:27.050 align:start position:0%
setup is the first challenge
do<00:01:25.080><c> you</c><00:01:25.200><c> go</c><00:01:25.320><c> to</c><00:01:25.380><c> hugging</c><00:01:25.680><c> face</c><00:01:25.860><c> what</c><00:01:26.580><c> files</c><00:01:26.939><c> do</c>

00:01:27.050 --> 00:01:27.060 align:start position:0%
do you go to hugging face what files do
 

00:01:27.060 --> 00:01:28.969 align:start position:0%
do you go to hugging face what files do
you<00:01:27.119><c> look</c><00:01:27.240><c> for</c><00:01:27.420><c> then</c><00:01:28.320><c> tweaking</c><00:01:28.740><c> the</c>

00:01:28.969 --> 00:01:28.979 align:start position:0%
you look for then tweaking the
 

00:01:28.979 --> 00:01:31.190 align:start position:0%
you look for then tweaking the
parameters<00:01:29.340><c> is</c><00:01:29.580><c> next</c><00:01:29.759><c> what's</c><00:01:30.659><c> this</c>

00:01:31.190 --> 00:01:31.200 align:start position:0%
parameters is next what's this
 

00:01:31.200 --> 00:01:34.130 align:start position:0%
parameters is next what's this
temperature<00:01:31.439><c> all</c><00:01:31.860><c> about</c><00:01:32.040><c> top</c><00:01:32.820><c> P</c><00:01:33.060><c> top</c><00:01:33.420><c> K</c><00:01:33.720><c> what</c>

00:01:34.130 --> 00:01:34.140 align:start position:0%
temperature all about top P top K what
 

00:01:34.140 --> 00:01:36.530 align:start position:0%
temperature all about top P top K what
are<00:01:34.320><c> those</c><00:01:34.439><c> and</c><00:01:35.400><c> there</c><00:01:35.700><c> are</c><00:01:35.880><c> so</c><00:01:35.939><c> many</c><00:01:36.119><c> others</c>

00:01:36.530 --> 00:01:36.540 align:start position:0%
are those and there are so many others
 

00:01:36.540 --> 00:01:38.690 align:start position:0%
are those and there are so many others
and<00:01:37.320><c> then</c><00:01:37.439><c> of</c><00:01:37.619><c> course</c><00:01:37.740><c> there's</c><00:01:38.159><c> a</c><00:01:38.340><c> prompt</c>

00:01:38.690 --> 00:01:38.700 align:start position:0%
and then of course there's a prompt
 

00:01:38.700 --> 00:01:40.670 align:start position:0%
and then of course there's a prompt
often<00:01:39.479><c> you</c><00:01:39.600><c> can</c><00:01:39.720><c> stick</c><00:01:39.900><c> with</c><00:01:40.079><c> the</c><00:01:40.200><c> defaults</c>

00:01:40.670 --> 00:01:40.680 align:start position:0%
often you can stick with the defaults
 

00:01:40.680 --> 00:01:42.890 align:start position:0%
often you can stick with the defaults
but<00:01:40.920><c> sometimes</c><00:01:41.400><c> tweaking</c><00:01:41.759><c> The</c><00:01:42.000><c> Prompt</c><00:01:42.299><c> is</c>

00:01:42.890 --> 00:01:42.900 align:start position:0%
but sometimes tweaking The Prompt is
 

00:01:42.900 --> 00:01:44.090 align:start position:0%
but sometimes tweaking The Prompt is
really<00:01:43.200><c> helpful</c>

00:01:44.090 --> 00:01:44.100 align:start position:0%
really helpful
 

00:01:44.100 --> 00:01:46.130 align:start position:0%
really helpful
you<00:01:44.640><c> can</c><00:01:44.759><c> tell</c><00:01:44.939><c> the</c><00:01:45.180><c> model</c><00:01:45.360><c> what</c><00:01:45.840><c> you're</c><00:01:45.960><c> going</c>

00:01:46.130 --> 00:01:46.140 align:start position:0%
you can tell the model what you're going
 

00:01:46.140 --> 00:01:47.630 align:start position:0%
you can tell the model what you're going
to<00:01:46.259><c> provide</c><00:01:46.560><c> and</c><00:01:46.860><c> what</c><00:01:47.100><c> you</c><00:01:47.159><c> expect</c><00:01:47.340><c> the</c>

00:01:47.630 --> 00:01:47.640 align:start position:0%
to provide and what you expect the
 

00:01:47.640 --> 00:01:48.890 align:start position:0%
to provide and what you expect the
output<00:01:47.880><c> to</c><00:01:48.119><c> look</c><00:01:48.240><c> like</c>

00:01:48.890 --> 00:01:48.900 align:start position:0%
output to look like
 

00:01:48.900 --> 00:01:51.649 align:start position:0%
output to look like
but<00:01:49.439><c> sharing</c><00:01:50.280><c> those</c><00:01:50.460><c> prompts</c><00:01:50.880><c> is</c><00:01:51.420><c> one</c>

00:01:51.649 --> 00:01:51.659 align:start position:0%
but sharing those prompts is one
 

00:01:51.659 --> 00:01:53.569 align:start position:0%
but sharing those prompts is one
Challenge<00:01:51.899><c> and</c><00:01:52.380><c> then</c><00:01:52.500><c> knowing</c><00:01:52.920><c> where</c><00:01:53.159><c> to</c><00:01:53.280><c> put</c>

00:01:53.569 --> 00:01:53.579 align:start position:0%
Challenge and then knowing where to put
 

00:01:53.579 --> 00:01:57.170 align:start position:0%
Challenge and then knowing where to put
them<00:01:53.759><c> in</c><00:01:54.240><c> the</c><00:01:54.360><c> UI</c><00:01:54.720><c> is</c><00:01:55.500><c> another</c><00:01:55.759><c> we</c><00:01:56.759><c> knew</c><00:01:56.939><c> we</c>

00:01:57.170 --> 00:01:57.180 align:start position:0%
them in the UI is another we knew we
 

00:01:57.180 --> 00:01:59.030 align:start position:0%
them in the UI is another we knew we
could<00:01:57.299><c> make</c><00:01:57.479><c> it</c><00:01:57.720><c> easier</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
could make it easier
 

00:01:59.040 --> 00:02:02.090 align:start position:0%
could make it easier
so<00:01:59.579><c> let's</c><00:02:00.119><c> take</c><00:02:00.360><c> a</c><00:02:00.479><c> look</c><00:02:00.659><c> at</c><00:02:01.079><c> how</c><00:02:01.380><c> to</c><00:02:01.500><c> use</c><00:02:01.740><c> model</c>

00:02:02.090 --> 00:02:02.100 align:start position:0%
so let's take a look at how to use model
 

00:02:02.100 --> 00:02:05.450 align:start position:0%
so let's take a look at how to use model
files<00:02:02.640><c> first</c><00:02:03.479><c> let's</c><00:02:03.899><c> just</c><00:02:04.020><c> run</c><00:02:04.200><c> a</c><00:02:04.380><c> model</c><00:02:04.560><c> it's</c>

00:02:05.450 --> 00:02:05.460 align:start position:0%
files first let's just run a model it's
 

00:02:05.460 --> 00:02:08.630 align:start position:0%
files first let's just run a model it's
just<00:02:05.640><c> like</c><00:02:05.820><c> it</c><00:02:06.000><c> was</c><00:02:06.119><c> before</c><00:02:06.299><c> Obama</c><00:02:07.200><c> run</c><00:02:07.640><c> llama2</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
just like it was before Obama run llama2
 

00:02:08.640 --> 00:02:11.150 align:start position:0%
just like it was before Obama run llama2
this<00:02:09.479><c> will</c><00:02:09.660><c> download</c><00:02:09.840><c> the</c><00:02:10.140><c> Llama</c><00:02:10.440><c> 2</c><00:02:10.679><c> model</c><00:02:10.920><c> for</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
this will download the Llama 2 model for
 

00:02:11.160 --> 00:02:13.250 align:start position:0%
this will download the Llama 2 model for
meta<00:02:11.580><c> but</c><00:02:12.120><c> we're</c><00:02:12.480><c> actually</c><00:02:12.660><c> downloading</c><00:02:13.140><c> it</c>

00:02:13.250 --> 00:02:13.260 align:start position:0%
meta but we're actually downloading it
 

00:02:13.260 --> 00:02:16.070 align:start position:0%
meta but we're actually downloading it
from<00:02:13.500><c> our</c><00:02:13.860><c> Hub</c><00:02:14.420><c> and</c><00:02:15.420><c> then</c><00:02:15.540><c> you</c><00:02:15.720><c> can</c><00:02:15.840><c> run</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
from our Hub and then you can run
 

00:02:16.080 --> 00:02:19.490 align:start position:0%
from our Hub and then you can run
prompts<00:02:16.800><c> against</c><00:02:17.040><c> it</c><00:02:17.400><c> why</c><00:02:18.239><c> is</c><00:02:18.540><c> the</c><00:02:18.720><c> sky</c><00:02:18.959><c> blue</c>

00:02:19.490 --> 00:02:19.500 align:start position:0%
prompts against it why is the sky blue
 

00:02:19.500 --> 00:02:21.770 align:start position:0%
prompts against it why is the sky blue
and<00:02:20.280><c> there's</c><00:02:20.580><c> our</c><00:02:20.819><c> answer</c>

00:02:21.770 --> 00:02:21.780 align:start position:0%
and there's our answer
 

00:02:21.780 --> 00:02:23.630 align:start position:0%
and there's our answer
but<00:02:22.379><c> let's</c><00:02:22.680><c> say</c><00:02:22.800><c> you</c><00:02:23.040><c> want</c><00:02:23.160><c> to</c><00:02:23.280><c> customize</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
but let's say you want to customize
 

00:02:23.640 --> 00:02:25.550 align:start position:0%
but let's say you want to customize
things<00:02:23.879><c> a</c><00:02:24.060><c> bit</c><00:02:24.120><c> we'll</c><00:02:24.720><c> start</c><00:02:24.959><c> just</c><00:02:25.260><c> by</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
things a bit we'll start just by
 

00:02:25.560 --> 00:02:28.070 align:start position:0%
things a bit we'll start just by
defining<00:02:26.459><c> a</c><00:02:26.700><c> new</c><00:02:26.760><c> model</c><00:02:26.940><c> file</c><00:02:27.300><c> and</c><00:02:27.480><c> using</c><00:02:27.780><c> a</c>

00:02:28.070 --> 00:02:28.080 align:start position:0%
defining a new model file and using a
 

00:02:28.080 --> 00:02:31.430 align:start position:0%
defining a new model file and using a
from<00:02:28.260><c> instruction</c><00:02:29.120><c> from</c><00:02:30.120><c> llama2</c>

00:02:31.430 --> 00:02:31.440 align:start position:0%
from instruction from llama2
 

00:02:31.440 --> 00:02:33.949 align:start position:0%
from instruction from llama2
just<00:02:31.920><c> like</c><00:02:32.040><c> Docker</c><00:02:32.520><c> we</c><00:02:33.000><c> also</c><00:02:33.239><c> support</c><00:02:33.420><c> tags</c>

00:02:33.949 --> 00:02:33.959 align:start position:0%
just like Docker we also support tags
 

00:02:33.959 --> 00:02:35.869 align:start position:0%
just like Docker we also support tags
you<00:02:34.200><c> can</c><00:02:34.319><c> add</c><00:02:34.500><c> on</c><00:02:34.680><c> with</c><00:02:34.980><c> a</c><00:02:35.160><c> colon</c><00:02:35.459><c> and</c><00:02:35.760><c> then</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
you can add on with a colon and then
 

00:02:35.879 --> 00:02:38.930 align:start position:0%
you can add on with a colon and then
attack<00:02:36.120><c> like</c><00:02:36.900><c> latest</c><00:02:37.260><c> or</c><00:02:37.980><c> and</c><00:02:38.459><c> maybe</c><00:02:38.700><c> with</c>

00:02:38.930 --> 00:02:38.940 align:start position:0%
attack like latest or and maybe with
 

00:02:38.940 --> 00:02:41.809 align:start position:0%
attack like latest or and maybe with
llama2<00:02:39.599><c> you</c><00:02:39.780><c> can</c><00:02:39.900><c> add</c><00:02:40.080><c> the</c><00:02:40.260><c> 13B</c><00:02:40.920><c> tag</c>

00:02:41.809 --> 00:02:41.819 align:start position:0%
llama2 you can add the 13B tag
 

00:02:41.819 --> 00:02:43.850 align:start position:0%
llama2 you can add the 13B tag
I'll<00:02:42.300><c> save</c><00:02:42.540><c> that</c><00:02:42.720><c> to</c><00:02:42.959><c> a</c><00:02:43.140><c> file</c><00:02:43.319><c> called</c><00:02:43.500><c> video</c>

00:02:43.850 --> 00:02:43.860 align:start position:0%
I'll save that to a file called video
 

00:02:43.860 --> 00:02:46.309 align:start position:0%
I'll save that to a file called video
example<00:02:44.459><c> you</c><00:02:45.180><c> can</c><00:02:45.239><c> call</c><00:02:45.360><c> the</c><00:02:45.540><c> file</c><00:02:46.019><c> anything</c>

00:02:46.309 --> 00:02:46.319 align:start position:0%
example you can call the file anything
 

00:02:46.319 --> 00:02:48.949 align:start position:0%
example you can call the file anything
you<00:02:46.620><c> like</c><00:02:46.800><c> and</c><00:02:47.700><c> then</c><00:02:47.879><c> use</c><00:02:48.060><c> the</c><00:02:48.300><c> olama</c><00:02:48.660><c> create</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
you like and then use the olama create
 

00:02:48.959 --> 00:02:54.050 align:start position:0%
you like and then use the olama create
command<00:02:49.459><c> olama</c><00:02:50.459><c> creates</c><00:02:51.120><c> my</c><00:02:52.019><c> L2</c><00:02:52.739><c> Dash</c><00:02:53.519><c> F</c><00:02:53.760><c> and</c>

00:02:54.050 --> 00:02:54.060 align:start position:0%
command olama creates my L2 Dash F and
 

00:02:54.060 --> 00:02:56.030 align:start position:0%
command olama creates my L2 Dash F and
the<00:02:54.180><c> path</c><00:02:54.360><c> of</c><00:02:54.599><c> the</c><00:02:54.720><c> model</c><00:02:54.840><c> file</c><00:02:55.140><c> in</c><00:02:55.860><c> this</c><00:02:55.920><c> case</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
the path of the model file in this case
 

00:02:56.040 --> 00:02:59.270 align:start position:0%
the path of the model file in this case
my<00:02:56.459><c> L2</c><00:02:56.879><c> is</c><00:02:57.180><c> just</c><00:02:57.480><c> what</c><00:02:57.840><c> I</c><00:02:58.019><c> want</c><00:02:58.140><c> to</c><00:02:58.260><c> call</c><00:02:58.319><c> it</c><00:02:58.860><c> the</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
my L2 is just what I want to call it the
 

00:02:59.280 --> 00:03:02.089 align:start position:0%
my L2 is just what I want to call it the
name<00:02:59.459><c> can</c><00:02:59.640><c> be</c><00:02:59.819><c> anything</c><00:03:00.300><c> you</c><00:03:00.599><c> like</c><00:03:01.260><c> now</c><00:03:01.800><c> I</c><00:03:01.980><c> can</c>

00:03:02.089 --> 00:03:02.099 align:start position:0%
name can be anything you like now I can
 

00:03:02.099 --> 00:03:05.990 align:start position:0%
name can be anything you like now I can
use<00:03:02.220><c> the</c><00:03:02.459><c> Run</c><00:03:02.580><c> command</c><00:03:02.940><c> olama</c><00:03:03.780><c> run</c><00:03:04.140><c> my</c><00:03:04.680><c> L2</c><00:03:05.099><c> and</c>

00:03:05.990 --> 00:03:06.000 align:start position:0%
use the Run command olama run my L2 and
 

00:03:06.000 --> 00:03:07.970 align:start position:0%
use the Run command olama run my L2 and
I'm<00:03:06.300><c> dropped</c><00:03:06.660><c> into</c><00:03:06.720><c> a</c><00:03:06.959><c> prompt</c><00:03:07.260><c> so</c><00:03:07.620><c> I</c><00:03:07.739><c> can</c><00:03:07.860><c> ask</c>

00:03:07.970 --> 00:03:07.980 align:start position:0%
I'm dropped into a prompt so I can ask
 

00:03:07.980 --> 00:03:09.949 align:start position:0%
I'm dropped into a prompt so I can ask
my<00:03:08.220><c> favorite</c><00:03:08.340><c> question</c><00:03:08.700><c> well</c><00:03:09.360><c> why</c><00:03:09.660><c> is</c><00:03:09.780><c> this</c>

00:03:09.949 --> 00:03:09.959 align:start position:0%
my favorite question well why is this
 

00:03:09.959 --> 00:03:11.030 align:start position:0%
my favorite question well why is this
guy<00:03:10.140><c> blue</c>

00:03:11.030 --> 00:03:11.040 align:start position:0%
guy blue
 

00:03:11.040 --> 00:03:13.070 align:start position:0%
guy blue
maybe<00:03:11.519><c> we</c><00:03:11.819><c> want</c><00:03:12.060><c> to</c><00:03:12.180><c> customize</c><00:03:12.480><c> the</c><00:03:12.720><c> prompt</c><00:03:12.959><c> a</c>

00:03:13.070 --> 00:03:13.080 align:start position:0%
maybe we want to customize the prompt a
 

00:03:13.080 --> 00:03:15.470 align:start position:0%
maybe we want to customize the prompt a
bit<00:03:13.200><c> sometimes</c><00:03:13.980><c> I</c><00:03:14.220><c> have</c><00:03:14.340><c> to</c><00:03:14.459><c> cook</c><00:03:14.700><c> and</c><00:03:15.180><c> I</c><00:03:15.420><c> don't</c>

00:03:15.470 --> 00:03:15.480 align:start position:0%
bit sometimes I have to cook and I don't
 

00:03:15.480 --> 00:03:16.970 align:start position:0%
bit sometimes I have to cook and I don't
want<00:03:15.599><c> to</c><00:03:15.720><c> go</c><00:03:15.840><c> to</c><00:03:15.959><c> the</c><00:03:16.080><c> store</c>

00:03:16.970 --> 00:03:16.980 align:start position:0%
want to go to the store
 

00:03:16.980 --> 00:03:19.430 align:start position:0%
want to go to the store
I<00:03:17.340><c> have</c><00:03:17.459><c> some</c><00:03:17.580><c> ingredients</c><00:03:18.000><c> in</c><00:03:18.180><c> the</c><00:03:18.360><c> fridge</c>

00:03:19.430 --> 00:03:19.440 align:start position:0%
I have some ingredients in the fridge
 

00:03:19.440 --> 00:03:21.649 align:start position:0%
I have some ingredients in the fridge
so<00:03:19.920><c> what</c><00:03:20.159><c> can</c><00:03:20.280><c> I</c><00:03:20.400><c> do</c><00:03:20.519><c> with</c><00:03:20.640><c> that</c><00:03:20.819><c> I</c><00:03:21.480><c> want</c><00:03:21.540><c> to</c>

00:03:21.649 --> 00:03:21.659 align:start position:0%
so what can I do with that I want to
 

00:03:21.659 --> 00:03:23.390 align:start position:0%
so what can I do with that I want to
give<00:03:21.780><c> the</c><00:03:21.959><c> model</c><00:03:22.080><c> a</c><00:03:22.379><c> list</c><00:03:22.560><c> of</c><00:03:22.739><c> ingredients</c><00:03:23.099><c> and</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
give the model a list of ingredients and
 

00:03:23.400 --> 00:03:25.729 align:start position:0%
give the model a list of ingredients and
have<00:03:23.580><c> it</c><00:03:23.760><c> spit</c><00:03:24.000><c> out</c><00:03:24.180><c> a</c><00:03:24.420><c> recipe</c><00:03:24.620><c> here's</c><00:03:25.620><c> a</c>

00:03:25.729 --> 00:03:25.739 align:start position:0%
have it spit out a recipe here's a
 

00:03:25.739 --> 00:03:27.770 align:start position:0%
have it spit out a recipe here's a
simple<00:03:25.920><c> prompt</c><00:03:26.340><c> for</c><00:03:26.459><c> that</c><00:03:26.640><c> notice</c><00:03:27.360><c> this</c><00:03:27.599><c> part</c>

00:03:27.770 --> 00:03:27.780 align:start position:0%
simple prompt for that notice this part
 

00:03:27.780 --> 00:03:29.750 align:start position:0%
simple prompt for that notice this part
shows<00:03:28.140><c> what</c><00:03:28.379><c> I</c><00:03:28.500><c> want</c><00:03:28.680><c> to</c><00:03:28.800><c> use</c><00:03:28.980><c> to</c><00:03:29.340><c> start</c><00:03:29.519><c> the</c>

00:03:29.750 --> 00:03:29.760 align:start position:0%
shows what I want to use to start the
 

00:03:29.760 --> 00:03:31.850 align:start position:0%
shows what I want to use to start the
conversation<00:03:30.180><c> and</c><00:03:30.540><c> not</c><00:03:30.959><c> to</c><00:03:31.140><c> include</c><00:03:31.379><c> in</c><00:03:31.620><c> every</c>

00:03:31.850 --> 00:03:31.860 align:start position:0%
conversation and not to include in every
 

00:03:31.860 --> 00:03:34.369 align:start position:0%
conversation and not to include in every
question<00:03:32.159><c> and</c><00:03:33.060><c> so</c><00:03:33.239><c> now</c><00:03:33.360><c> I'll</c><00:03:33.540><c> give</c><00:03:33.720><c> it</c><00:03:33.900><c> salmon</c>

00:03:34.369 --> 00:03:34.379 align:start position:0%
question and so now I'll give it salmon
 

00:03:34.379 --> 00:03:38.449 align:start position:0%
question and so now I'll give it salmon
orange<00:03:35.540><c> whiskey</c><00:03:36.540><c> and</c><00:03:37.080><c> radish</c>

00:03:38.449 --> 00:03:38.459 align:start position:0%
orange whiskey and radish
 

00:03:38.459 --> 00:03:40.850 align:start position:0%
orange whiskey and radish
and<00:03:39.000><c> here</c><00:03:39.180><c> comes</c><00:03:39.420><c> the</c><00:03:39.540><c> recipe</c>

00:03:40.850 --> 00:03:40.860 align:start position:0%
and here comes the recipe
 

00:03:40.860 --> 00:03:42.649 align:start position:0%
and here comes the recipe
what<00:03:41.340><c> if</c><00:03:41.459><c> we</c><00:03:41.580><c> want</c><00:03:41.700><c> to</c><00:03:41.819><c> tweak</c><00:03:42.120><c> the</c><00:03:42.239><c> parameters</c>

00:03:42.649 --> 00:03:42.659 align:start position:0%
what if we want to tweak the parameters
 

00:03:42.659 --> 00:03:45.289 align:start position:0%
what if we want to tweak the parameters
there's<00:03:43.379><c> a</c><00:03:43.680><c> dock</c><00:03:43.980><c> in</c><00:03:44.220><c> the</c><00:03:44.340><c> repo</c><00:03:44.700><c> that</c><00:03:44.940><c> I</c><00:03:45.000><c> added</c>

00:03:45.289 --> 00:03:45.299 align:start position:0%
there's a dock in the repo that I added
 

00:03:45.299 --> 00:03:46.970 align:start position:0%
there's a dock in the repo that I added
yesterday<00:03:45.480><c> that</c><00:03:46.019><c> covers</c><00:03:46.319><c> the</c><00:03:46.560><c> parameters</c>

00:03:46.970 --> 00:03:46.980 align:start position:0%
yesterday that covers the parameters
 

00:03:46.980 --> 00:03:49.190 align:start position:0%
yesterday that covers the parameters
available<00:03:47.220><c> in</c><00:03:47.519><c> the</c><00:03:47.640><c> model</c><00:03:47.760><c> file</c><00:03:48.480><c> I'll</c><00:03:48.959><c> add</c>

00:03:49.190 --> 00:03:49.200 align:start position:0%
available in the model file I'll add
 

00:03:49.200 --> 00:03:52.070 align:start position:0%
available in the model file I'll add
temperature<00:03:49.440><c> top</c><00:03:49.980><c> p</c><00:03:50.220><c> and</c><00:03:50.400><c> top</c><00:03:50.580><c> K</c><00:03:50.819><c> we</c><00:03:51.599><c> also</c><00:03:51.840><c> have</c>

00:03:52.070 --> 00:03:52.080 align:start position:0%
temperature top p and top K we also have
 

00:03:52.080 --> 00:03:54.350 align:start position:0%
temperature top p and top K we also have
license<00:03:52.379><c> in</c><00:03:52.860><c> case</c><00:03:53.040><c> you</c><00:03:53.220><c> need</c><00:03:53.400><c> to</c><00:03:53.580><c> distribute</c><00:03:54.060><c> a</c>

00:03:54.350 --> 00:03:54.360 align:start position:0%
license in case you need to distribute a
 

00:03:54.360 --> 00:03:56.449 align:start position:0%
license in case you need to distribute a
license<00:03:54.540><c> with</c><00:03:54.900><c> your</c><00:03:55.019><c> model</c><00:03:55.200><c> does</c><00:03:56.040><c> it</c><00:03:56.220><c> react</c>

00:03:56.449 --> 00:03:56.459 align:start position:0%
license with your model does it react
 

00:03:56.459 --> 00:03:58.369 align:start position:0%
license with your model does it react
differently<00:03:56.760><c> to</c><00:03:57.000><c> the</c><00:03:57.180><c> my</c><00:03:57.420><c> ingredients</c><00:03:58.080><c> with</c>

00:03:58.369 --> 00:03:58.379 align:start position:0%
differently to the my ingredients with
 

00:03:58.379 --> 00:04:00.470 align:start position:0%
differently to the my ingredients with
those<00:03:58.560><c> parameters</c><00:03:59.580><c> sometimes</c><00:04:00.120><c> you'll</c><00:04:00.360><c> have</c>

00:04:00.470 --> 00:04:00.480 align:start position:0%
those parameters sometimes you'll have
 

00:04:00.480 --> 00:04:02.330 align:start position:0%
those parameters sometimes you'll have
to<00:04:00.540><c> tweak</c><00:04:00.959><c> it</c><00:04:01.080><c> more</c><00:04:01.440><c> or</c><00:04:01.560><c> less</c><00:04:01.680><c> depending</c><00:04:02.159><c> on</c>

00:04:02.330 --> 00:04:02.340 align:start position:0%
to tweak it more or less depending on
 

00:04:02.340 --> 00:04:04.309 align:start position:0%
to tweak it more or less depending on
the<00:04:02.459><c> model</c><00:04:02.640><c> and</c><00:04:03.239><c> your</c><00:04:03.480><c> goals</c><00:04:03.720><c> for</c><00:04:03.900><c> the</c><00:04:04.019><c> prompt</c>

00:04:04.309 --> 00:04:04.319 align:start position:0%
the model and your goals for the prompt
 

00:04:04.319 --> 00:04:07.250 align:start position:0%
the model and your goals for the prompt
but<00:04:05.040><c> it's</c><00:04:05.159><c> really</c><00:04:05.340><c> easy</c><00:04:05.580><c> to</c><00:04:05.819><c> do</c><00:04:06.000><c> here</c><00:04:06.659><c> we'll</c>

00:04:07.250 --> 00:04:07.260 align:start position:0%
but it's really easy to do here we'll
 

00:04:07.260 --> 00:04:09.289 align:start position:0%
but it's really easy to do here we'll
also<00:04:07.560><c> be</c><00:04:07.680><c> updating</c><00:04:08.040><c> the</c><00:04:08.220><c> menu</c><00:04:08.340><c> bar</c><00:04:08.640><c> soon</c><00:04:08.940><c> to</c>

00:04:09.289 --> 00:04:09.299 align:start position:0%
also be updating the menu bar soon to
 

00:04:09.299 --> 00:04:10.910 align:start position:0%
also be updating the menu bar soon to
make<00:04:09.480><c> all</c><00:04:09.900><c> of</c><00:04:10.019><c> this</c><00:04:10.200><c> possible</c><00:04:10.560><c> without</c>

00:04:10.910 --> 00:04:10.920 align:start position:0%
make all of this possible without
 

00:04:10.920 --> 00:04:13.250 align:start position:0%
make all of this possible without
touching<00:04:11.340><c> the</c><00:04:11.459><c> CLI</c><00:04:11.939><c> but</c><00:04:12.420><c> we</c><00:04:12.599><c> wanted</c><00:04:12.780><c> to</c><00:04:13.019><c> focus</c>

00:04:13.250 --> 00:04:13.260 align:start position:0%
touching the CLI but we wanted to focus
 

00:04:13.260 --> 00:04:15.530 align:start position:0%
touching the CLI but we wanted to focus
on<00:04:13.500><c> the</c><00:04:13.739><c> most</c><00:04:13.980><c> important</c><00:04:14.519><c> stuff</c><00:04:14.819><c> first</c><00:04:15.180><c> you</c>

00:04:15.530 --> 00:04:15.540 align:start position:0%
on the most important stuff first you
 

00:04:15.540 --> 00:04:17.150 align:start position:0%
on the most important stuff first you
know<00:04:15.599><c> the</c><00:04:15.959><c> fundamentals</c>

00:04:17.150 --> 00:04:17.160 align:start position:0%
know the fundamentals
 

00:04:17.160 --> 00:04:19.370 align:start position:0%
know the fundamentals
there's<00:04:17.699><c> a</c><00:04:17.820><c> lot</c><00:04:17.940><c> more</c><00:04:18.120><c> we</c><00:04:18.359><c> plan</c><00:04:18.540><c> to</c><00:04:18.720><c> add</c><00:04:18.900><c> we</c>

00:04:19.370 --> 00:04:19.380 align:start position:0%
there's a lot more we plan to add we
 

00:04:19.380 --> 00:04:20.689 align:start position:0%
there's a lot more we plan to add we
already<00:04:19.560><c> have</c><00:04:19.799><c> a</c><00:04:19.919><c> few</c><00:04:20.040><c> examples</c><00:04:20.400><c> in</c><00:04:20.579><c> the</c>

00:04:20.689 --> 00:04:20.699 align:start position:0%
already have a few examples in the
 

00:04:20.699 --> 00:04:23.030 align:start position:0%
already have a few examples in the
GitHub<00:04:20.940><c> repo</c><00:04:21.419><c> I'm</c><00:04:21.900><c> loving</c><00:04:22.440><c> this</c><00:04:22.620><c> one</c><00:04:22.740><c> to</c>

00:04:23.030 --> 00:04:23.040 align:start position:0%
GitHub repo I'm loving this one to
 

00:04:23.040 --> 00:04:25.070 align:start position:0%
GitHub repo I'm loving this one to
generate<00:04:23.340><c> mid-journey</c><00:04:24.060><c> prompts</c><00:04:24.419><c> using</c><00:04:24.960><c> a</c>

00:04:25.070 --> 00:04:25.080 align:start position:0%
generate mid-journey prompts using a
 

00:04:25.080 --> 00:04:27.650 align:start position:0%
generate mid-journey prompts using a
prompt<00:04:25.320><c> I</c><00:04:25.440><c> found</c><00:04:25.620><c> online</c><00:04:25.860><c> I</c><00:04:26.580><c> just</c><00:04:26.759><c> fed</c><00:04:27.000><c> it</c><00:04:27.240><c> new</c>

00:04:27.650 --> 00:04:27.660 align:start position:0%
prompt I found online I just fed it new
 

00:04:27.660 --> 00:04:30.050 align:start position:0%
prompt I found online I just fed it new
llama<00:04:28.020><c> Runner</c><00:04:28.560><c> and</c><00:04:29.160><c> it</c><00:04:29.280><c> generated</c><00:04:29.639><c> a</c><00:04:29.940><c> great</c>

00:04:30.050 --> 00:04:30.060 align:start position:0%
llama Runner and it generated a great
 

00:04:30.060 --> 00:04:31.909 align:start position:0%
llama Runner and it generated a great
prompt<00:04:30.479><c> to</c><00:04:30.660><c> give</c><00:04:30.780><c> to</c><00:04:31.020><c> Mid</c><00:04:31.139><c> journey</c><00:04:31.500><c> and</c><00:04:31.740><c> I</c>

00:04:31.909 --> 00:04:31.919 align:start position:0%
prompt to give to Mid journey and I
 

00:04:31.919 --> 00:04:34.670 align:start position:0%
prompt to give to Mid journey and I
ended<00:04:32.160><c> up</c><00:04:32.280><c> with</c><00:04:32.639><c> this</c><00:04:32.880><c> speeding</c><00:04:33.479><c> llama</c><00:04:33.840><c> that</c><00:04:34.560><c> I</c>

00:04:34.670 --> 00:04:34.680 align:start position:0%
ended up with this speeding llama that I
 

00:04:34.680 --> 00:04:37.430 align:start position:0%
ended up with this speeding llama that I
used<00:04:34.860><c> for</c><00:04:35.100><c> the</c><00:04:35.220><c> thumbnail</c><00:04:35.639><c> for</c><00:04:36.479><c> the</c><00:04:37.320><c> previous</c>

00:04:37.430 --> 00:04:37.440 align:start position:0%
used for the thumbnail for the previous
 

00:04:37.440 --> 00:04:38.930 align:start position:0%
used for the thumbnail for the previous
video

00:04:38.930 --> 00:04:38.940 align:start position:0%
video
 

00:04:38.940 --> 00:04:41.270 align:start position:0%
video
here<00:04:39.540><c> are</c><00:04:39.720><c> the</c><00:04:39.840><c> results</c><00:04:40.080><c> of</c><00:04:40.680><c> using</c><00:04:41.040><c> the</c><00:04:41.160><c> term</c>

00:04:41.270 --> 00:04:41.280 align:start position:0%
here are the results of using the term
 

00:04:41.280 --> 00:04:43.370 align:start position:0%
here are the results of using the term
platform<00:04:41.820><c> engineering</c><00:04:42.360><c> which</c><00:04:42.660><c> is</c><00:04:42.840><c> a</c><00:04:43.199><c> popular</c>

00:04:43.370 --> 00:04:43.380 align:start position:0%
platform engineering which is a popular
 

00:04:43.380 --> 00:04:46.189 align:start position:0%
platform engineering which is a popular
buzzword<00:04:43.979><c> today</c><00:04:44.340><c> in</c><00:04:44.639><c> the</c><00:04:44.759><c> world</c><00:04:44.880><c> of</c><00:04:45.120><c> devops</c>

00:04:46.189 --> 00:04:46.199 align:start position:0%
buzzword today in the world of devops
 

00:04:46.199 --> 00:04:48.469 align:start position:0%
buzzword today in the world of devops
how<00:04:46.680><c> do</c><00:04:46.860><c> you</c><00:04:46.919><c> make</c><00:04:47.100><c> an</c><00:04:47.280><c> image</c><00:04:47.520><c> about</c><00:04:47.820><c> platform</c>

00:04:48.469 --> 00:04:48.479 align:start position:0%
how do you make an image about platform
 

00:04:48.479 --> 00:04:51.290 align:start position:0%
how do you make an image about platform
engineering<00:04:49.220><c> nothing</c><00:04:50.220><c> I</c><00:04:50.759><c> would</c><00:04:51.060><c> create</c>

00:04:51.290 --> 00:04:51.300 align:start position:0%
engineering nothing I would create
 

00:04:51.300 --> 00:04:53.930 align:start position:0%
engineering nothing I would create
myself<00:04:51.600><c> would</c><00:04:52.080><c> look</c><00:04:52.380><c> anywhere</c><00:04:53.100><c> near</c><00:04:53.460><c> as</c><00:04:53.699><c> cool</c>

00:04:53.930 --> 00:04:53.940 align:start position:0%
myself would look anywhere near as cool
 

00:04:53.940 --> 00:04:54.950 align:start position:0%
myself would look anywhere near as cool
as<00:04:54.120><c> these</c>

00:04:54.950 --> 00:04:54.960 align:start position:0%
as these
 

00:04:54.960 --> 00:04:57.350 align:start position:0%
as these
when<00:04:55.440><c> I</c><00:04:55.620><c> used</c><00:04:55.740><c> to</c><00:04:55.919><c> own</c><00:04:56.100><c> the</c><00:04:56.280><c> blog</c><00:04:56.639><c> at</c><00:04:56.820><c> datadog</c>

00:04:57.350 --> 00:04:57.360 align:start position:0%
when I used to own the blog at datadog
 

00:04:57.360 --> 00:05:00.110 align:start position:0%
when I used to own the blog at datadog
for<00:04:57.660><c> a</c><00:04:57.720><c> short</c><00:04:57.840><c> while</c><00:04:58.020><c> I</c><00:04:58.919><c> was</c><00:04:59.100><c> terrible</c><00:04:59.639><c> at</c>

00:05:00.110 --> 00:05:00.120 align:start position:0%
for a short while I was terrible at
 

00:05:00.120 --> 00:05:02.330 align:start position:0%
for a short while I was terrible at
coming<00:05:00.300><c> up</c><00:05:00.479><c> with</c><00:05:00.600><c> images</c><00:05:00.840><c> but</c><00:05:01.320><c> if</c><00:05:01.560><c> I</c><00:05:01.860><c> had</c><00:05:02.040><c> this</c>

00:05:02.330 --> 00:05:02.340 align:start position:0%
coming up with images but if I had this
 

00:05:02.340 --> 00:05:05.689 align:start position:0%
coming up with images but if I had this
back<00:05:02.639><c> then</c><00:05:03.139><c> we</c><00:05:04.139><c> may</c><00:05:04.320><c> be</c><00:05:04.440><c> in</c><00:05:04.560><c> a</c><00:05:04.740><c> different</c><00:05:04.860><c> place</c>

00:05:05.689 --> 00:05:05.699 align:start position:0%
back then we may be in a different place
 

00:05:05.699 --> 00:05:08.270 align:start position:0%
back then we may be in a different place
well<00:05:06.240><c> we</c><00:05:06.720><c> are</c><00:05:06.900><c> sure</c><00:05:07.259><c> you</c><00:05:07.500><c> will</c><00:05:07.620><c> come</c><00:05:07.800><c> up</c><00:05:07.979><c> with</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
well we are sure you will come up with
 

00:05:08.280 --> 00:05:10.909 align:start position:0%
well we are sure you will come up with
other<00:05:08.580><c> great</c><00:05:08.940><c> ideas</c><00:05:09.240><c> I</c><00:05:10.080><c> mentioned</c><00:05:10.440><c> that</c><00:05:10.620><c> soon</c>

00:05:10.909 --> 00:05:10.919 align:start position:0%
other great ideas I mentioned that soon
 

00:05:10.919 --> 00:05:12.950 align:start position:0%
other great ideas I mentioned that soon
we'll<00:05:11.160><c> open</c><00:05:11.460><c> up</c><00:05:11.639><c> the</c><00:05:11.820><c> registry</c><00:05:12.240><c> for</c><00:05:12.540><c> sharing</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
we'll open up the registry for sharing
 

00:05:12.960 --> 00:05:15.290 align:start position:0%
we'll open up the registry for sharing
their<00:05:13.080><c> models</c><00:05:13.560><c> but</c><00:05:13.979><c> we</c><00:05:14.160><c> can</c><00:05:14.340><c> also</c><00:05:14.639><c> add</c><00:05:14.940><c> any</c>

00:05:15.290 --> 00:05:15.300 align:start position:0%
their models but we can also add any
 

00:05:15.300 --> 00:05:17.629 align:start position:0%
their models but we can also add any
other<00:05:15.540><c> mod</c><00:05:15.720><c> examples</c><00:05:16.380><c> that</c><00:05:17.100><c> you</c><00:05:17.220><c> come</c><00:05:17.340><c> up</c><00:05:17.460><c> with</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
other mod examples that you come up with
 

00:05:17.639 --> 00:05:20.330 align:start position:0%
other mod examples that you come up with
to<00:05:18.000><c> our</c><00:05:18.180><c> repo</c><00:05:18.900><c> and</c><00:05:19.320><c> so</c><00:05:19.440><c> that's</c><00:05:19.560><c> it</c><00:05:19.800><c> for</c><00:05:20.100><c> a</c><00:05:20.160><c> quick</c>

00:05:20.330 --> 00:05:20.340 align:start position:0%
to our repo and so that's it for a quick
 

00:05:20.340 --> 00:05:23.150 align:start position:0%
to our repo and so that's it for a quick
intro<00:05:20.759><c> to</c><00:05:21.000><c> the</c><00:05:21.120><c> model</c><00:05:21.300><c> file</c><00:05:21.660><c> check</c><00:05:22.259><c> it</c><00:05:22.440><c> out</c><00:05:22.620><c> and</c>

00:05:23.150 --> 00:05:23.160 align:start position:0%
intro to the model file check it out and
 

00:05:23.160 --> 00:05:24.950 align:start position:0%
intro to the model file check it out and
the<00:05:23.340><c> rest</c><00:05:23.460><c> of</c><00:05:23.580><c> a</c><00:05:23.759><c> llama</c><00:05:24.000><c> and</c><00:05:24.360><c> let</c><00:05:24.539><c> us</c><00:05:24.660><c> know</c><00:05:24.780><c> what</c>

00:05:24.950 --> 00:05:24.960 align:start position:0%
the rest of a llama and let us know what
 

00:05:24.960 --> 00:05:26.689 align:start position:0%
the rest of a llama and let us know what
you<00:05:25.080><c> think</c><00:05:25.259><c> thanks</c><00:05:25.919><c> so</c><00:05:26.100><c> much</c><00:05:26.220><c> for</c><00:05:26.340><c> watching</c>

00:05:26.689 --> 00:05:26.699 align:start position:0%
you think thanks so much for watching
 

00:05:26.699 --> 00:05:29.479 align:start position:0%
you think thanks so much for watching
bye

