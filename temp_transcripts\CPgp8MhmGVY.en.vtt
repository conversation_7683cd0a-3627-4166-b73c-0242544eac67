WEBVTT
Kind: captions
Language: en

00:00:00.659 --> 00:00:02.869 align:start position:0%
 
can<00:00:01.199><c> you</c><00:00:01.380><c> talk</c><00:00:01.560><c> to</c><00:00:01.800><c> your</c><00:00:01.920><c> own</c><00:00:02.040><c> documents</c><00:00:02.580><c> to</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
can you talk to your own documents to
 

00:00:02.879 --> 00:00:05.450 align:start position:0%
can you talk to your own documents to
get<00:00:03.000><c> answers</c><00:00:03.419><c> out</c><00:00:03.840><c> of</c><00:00:04.020><c> them</c><00:00:04.140><c> that's</c><00:00:04.980><c> the</c><00:00:05.220><c> goal</c>

00:00:05.450 --> 00:00:05.460 align:start position:0%
get answers out of them that's the goal
 

00:00:05.460 --> 00:00:07.789 align:start position:0%
get answers out of them that's the goal
for<00:00:05.640><c> many</c><00:00:05.819><c> tools</c><00:00:06.240><c> out</c><00:00:06.480><c> there</c><00:00:06.600><c> and</c><00:00:07.259><c> the</c><00:00:07.440><c> concept</c>

00:00:07.789 --> 00:00:07.799 align:start position:0%
for many tools out there and the concept
 

00:00:07.799 --> 00:00:10.730 align:start position:0%
for many tools out there and the concept
is<00:00:08.280><c> called</c><00:00:08.580><c> embeddings</c><00:00:09.240><c> feed</c><00:00:10.080><c> your</c><00:00:10.320><c> notes</c>

00:00:10.730 --> 00:00:10.740 align:start position:0%
is called embeddings feed your notes
 

00:00:10.740 --> 00:00:13.070 align:start position:0%
is called embeddings feed your notes
your<00:00:11.099><c> collected</c><00:00:11.519><c> PDFs</c><00:00:12.120><c> or</c><00:00:12.300><c> well</c><00:00:12.840><c> anything</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
your collected PDFs or well anything
 

00:00:13.080 --> 00:00:15.770 align:start position:0%
your collected PDFs or well anything
else<00:00:13.440><c> that</c><00:00:13.740><c> you</c><00:00:13.920><c> have</c><00:00:14.099><c> to</c><00:00:14.580><c> your</c><00:00:14.759><c> llm</c><00:00:15.240><c> and</c><00:00:15.480><c> ask</c>

00:00:15.770 --> 00:00:15.780 align:start position:0%
else that you have to your llm and ask
 

00:00:15.780 --> 00:00:18.230 align:start position:0%
else that you have to your llm and ask
it<00:00:15.960><c> questions</c><00:00:16.199><c> to</c><00:00:16.560><c> generate</c><00:00:16.920><c> new</c><00:00:17.220><c> insights</c>

00:00:18.230 --> 00:00:18.240 align:start position:0%
it questions to generate new insights
 

00:00:18.240 --> 00:00:20.929 align:start position:0%
it questions to generate new insights
olama<00:00:18.960><c> is</c><00:00:19.500><c> already</c><00:00:19.680><c> the</c><00:00:20.100><c> easiest</c><00:00:20.340><c> way</c><00:00:20.580><c> to</c><00:00:20.820><c> work</c>

00:00:20.929 --> 00:00:20.939 align:start position:0%
olama is already the easiest way to work
 

00:00:20.939 --> 00:00:23.810 align:start position:0%
olama is already the easiest way to work
with<00:00:21.300><c> llms</c><00:00:22.080><c> on</c><00:00:22.320><c> your</c><00:00:22.439><c> laptop</c><00:00:22.740><c> but</c><00:00:23.220><c> can</c><00:00:23.640><c> we</c>

00:00:23.810 --> 00:00:23.820 align:start position:0%
with llms on your laptop but can we
 

00:00:23.820 --> 00:00:26.509 align:start position:0%
with llms on your laptop but can we
support<00:00:24.060><c> embeddings</c><00:00:24.840><c> in</c><00:00:25.740><c> this</c><00:00:25.859><c> video</c><00:00:26.039><c> I'll</c>

00:00:26.509 --> 00:00:26.519 align:start position:0%
support embeddings in this video I'll
 

00:00:26.519 --> 00:00:28.790 align:start position:0%
support embeddings in this video I'll
show<00:00:26.699><c> you</c><00:00:26.820><c> how</c><00:00:27.180><c> a</c><00:00:27.359><c> python</c><00:00:27.779><c> developer</c><00:00:28.199><c> can</c><00:00:28.560><c> use</c>

00:00:28.790 --> 00:00:28.800 align:start position:0%
show you how a python developer can use
 

00:00:28.800 --> 00:00:32.089 align:start position:0%
show you how a python developer can use
embeddings<00:00:29.340><c> on</c><00:00:29.640><c> olama</c><00:00:30.119><c> using</c><00:00:30.900><c> Lang</c><00:00:31.199><c> chain</c>

00:00:32.089 --> 00:00:32.099 align:start position:0%
embeddings on olama using Lang chain
 

00:00:32.099 --> 00:00:34.370 align:start position:0%
embeddings on olama using Lang chain
so<00:00:32.640><c> let's</c><00:00:32.940><c> say</c><00:00:33.120><c> you</c><00:00:33.239><c> have</c><00:00:33.360><c> a</c><00:00:33.480><c> document</c><00:00:33.780><c> that</c>

00:00:34.370 --> 00:00:34.380 align:start position:0%
so let's say you have a document that
 

00:00:34.380 --> 00:00:36.170 align:start position:0%
so let's say you have a document that
you<00:00:34.559><c> want</c><00:00:34.680><c> to</c><00:00:34.800><c> ask</c><00:00:34.920><c> a</c><00:00:35.219><c> question</c><00:00:35.399><c> about</c>

00:00:36.170 --> 00:00:36.180 align:start position:0%
you want to ask a question about
 

00:00:36.180 --> 00:00:38.389 align:start position:0%
you want to ask a question about
I'm<00:00:36.719><c> going</c><00:00:36.840><c> to</c><00:00:37.020><c> use</c><00:00:37.079><c> this</c><00:00:37.380><c> Wikipedia</c><00:00:38.040><c> article</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
I'm going to use this Wikipedia article
 

00:00:38.399 --> 00:00:40.670 align:start position:0%
I'm going to use this Wikipedia article
about<00:00:38.579><c> the</c><00:00:38.820><c> horrible</c><00:00:39.120><c> fires</c><00:00:39.600><c> on</c><00:00:39.899><c> Maui</c><00:00:40.320><c> just</c><00:00:40.559><c> a</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
about the horrible fires on Maui just a
 

00:00:40.680 --> 00:00:41.750 align:start position:0%
about the horrible fires on Maui just a
few<00:00:40.800><c> days</c><00:00:40.920><c> ago</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
few days ago
 

00:00:41.760 --> 00:00:44.930 align:start position:0%
few days ago
the<00:00:42.239><c> URL</c><00:00:42.600><c> is</c><00:00:42.960><c> on</c><00:00:43.200><c> the</c><00:00:43.320><c> screen</c><00:00:43.440><c> so</c><00:00:44.399><c> how</c><00:00:44.700><c> do</c><00:00:44.820><c> you</c>

00:00:44.930 --> 00:00:44.940 align:start position:0%
the URL is on the screen so how do you
 

00:00:44.940 --> 00:00:45.950 align:start position:0%
the URL is on the screen so how do you
get<00:00:45.000><c> started</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
get started
 

00:00:45.960 --> 00:00:48.290 align:start position:0%
get started
well<00:00:46.379><c> since</c><00:00:46.680><c> we're</c><00:00:46.860><c> using</c><00:00:47.280><c> Lang</c><00:00:47.700><c> chain</c><00:00:48.059><c> in</c>

00:00:48.290 --> 00:00:48.300 align:start position:0%
well since we're using Lang chain in
 

00:00:48.300 --> 00:00:50.510 align:start position:0%
well since we're using Lang chain in
this<00:00:48.480><c> video</c><00:00:48.660><c> let's</c><00:00:49.320><c> start</c><00:00:49.559><c> with</c><00:00:49.860><c> Pip</c><00:00:50.280><c> install</c>

00:00:50.510 --> 00:00:50.520 align:start position:0%
this video let's start with Pip install
 

00:00:50.520 --> 00:00:53.630 align:start position:0%
this video let's start with Pip install
Lang<00:00:51.120><c> chain</c><00:00:51.480><c> okay</c><00:00:52.140><c> now</c><00:00:52.680><c> create</c><00:00:52.860><c> a</c><00:00:53.100><c> python</c><00:00:53.399><c> file</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
Lang chain okay now create a python file
 

00:00:53.640 --> 00:00:55.670 align:start position:0%
Lang chain okay now create a python file
and<00:00:53.940><c> start</c><00:00:54.120><c> building</c><00:00:54.360><c> we</c><00:00:55.140><c> first</c><00:00:55.320><c> need</c><00:00:55.500><c> to</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
and start building we first need to
 

00:00:55.680 --> 00:00:57.670 align:start position:0%
and start building we first need to
import<00:00:55.800><c> olama</c><00:00:56.399><c> from</c>

00:00:57.670 --> 00:00:57.680 align:start position:0%
import olama from
 

00:00:57.680 --> 00:01:01.010 align:start position:0%
import olama from
langchain.lm's<00:00:58.680><c> import</c><00:00:59.160><c> olama</c><00:01:00.000><c> and</c><00:01:00.719><c> then</c><00:01:00.780><c> we</c>

00:01:01.010 --> 00:01:01.020 align:start position:0%
langchain.lm's import olama and then we
 

00:01:01.020 --> 00:01:02.689 align:start position:0%
langchain.lm's import olama and then we
need<00:01:01.140><c> to</c><00:01:01.260><c> initiate</c><00:01:01.620><c> our</c><00:01:01.920><c> connection</c><00:01:02.100><c> to</c><00:01:02.520><c> a</c>

00:01:02.689 --> 00:01:02.699 align:start position:0%
need to initiate our connection to a
 

00:01:02.699 --> 00:01:06.170 align:start position:0%
need to initiate our connection to a
model<00:01:02.820><c> on</c><00:01:03.180><c> olama</c><00:01:03.660><c> olama</c><00:01:04.500><c> equals</c><00:01:05.040><c> a</c><00:01:05.700><c> llama</c><00:01:05.820><c> base</c>

00:01:06.170 --> 00:01:06.180 align:start position:0%
model on olama olama equals a llama base
 

00:01:06.180 --> 00:01:10.609 align:start position:0%
model on olama olama equals a llama base
URL<00:01:06.659><c> equals</c><00:01:07.260><c> HP</c><00:01:07.860><c> localhost</c><00:01:08.840><c> 11434</c><00:01:09.840><c> and</c><00:01:10.380><c> model</c>

00:01:10.609 --> 00:01:10.619 align:start position:0%
URL equals HP localhost 11434 and model
 

00:01:10.619 --> 00:01:11.990 align:start position:0%
URL equals HP localhost 11434 and model
llama2

00:01:11.990 --> 00:01:12.000 align:start position:0%
llama2
 

00:01:12.000 --> 00:01:14.690 align:start position:0%
llama2
we<00:01:12.540><c> can</c><00:01:12.720><c> specify</c><00:01:13.140><c> any</c><00:01:13.500><c> model</c><00:01:13.740><c> here</c><00:01:14.040><c> that</c>

00:01:14.690 --> 00:01:14.700 align:start position:0%
we can specify any model here that
 

00:01:14.700 --> 00:01:16.850 align:start position:0%
we can specify any model here that
either<00:01:15.060><c> is</c><00:01:15.360><c> on</c><00:01:15.600><c> the</c><00:01:15.720><c> Alama</c><00:01:16.080><c> Hub</c><00:01:16.320><c> or</c><00:01:16.680><c> that</c>

00:01:16.850 --> 00:01:16.860 align:start position:0%
either is on the Alama Hub or that
 

00:01:16.860 --> 00:01:19.670 align:start position:0%
either is on the Alama Hub or that
you've<00:01:17.100><c> created</c><00:01:17.400><c> locally</c><00:01:17.880><c> now</c><00:01:18.780><c> to</c><00:01:19.200><c> test</c><00:01:19.380><c> that</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
you've created locally now to test that
 

00:01:19.680 --> 00:01:23.510 align:start position:0%
you've created locally now to test that
that<00:01:19.860><c> works</c><00:01:20.280><c> print</c><00:01:20.820><c> olama</c><00:01:21.360><c> Y</c><00:01:21.780><c> is</c><00:01:22.020><c> the</c><00:01:22.200><c> sky</c><00:01:22.380><c> blue</c>

00:01:23.510 --> 00:01:23.520 align:start position:0%
that works print olama Y is the sky blue
 

00:01:23.520 --> 00:01:26.210 align:start position:0%
that works print olama Y is the sky blue
now<00:01:23.939><c> if</c><00:01:24.180><c> we</c><00:01:24.299><c> run</c><00:01:24.420><c> that</c><00:01:24.659><c> we</c><00:01:25.259><c> will</c><00:01:25.380><c> see</c><00:01:25.680><c> why</c><00:01:25.979><c> the</c>

00:01:26.210 --> 00:01:26.220 align:start position:0%
now if we run that we will see why the
 

00:01:26.220 --> 00:01:28.370 align:start position:0%
now if we run that we will see why the
sky<00:01:26.340><c> is</c><00:01:26.520><c> blue</c><00:01:26.759><c> perfect</c>

00:01:28.370 --> 00:01:28.380 align:start position:0%
sky is blue perfect
 

00:01:28.380 --> 00:01:31.070 align:start position:0%
sky is blue perfect
now<00:01:29.100><c> we</c><00:01:29.460><c> can</c><00:01:29.640><c> work</c><00:01:29.820><c> on</c><00:01:30.180><c> loading</c><00:01:30.600><c> the</c><00:01:30.720><c> document</c>

00:01:31.070 --> 00:01:31.080 align:start position:0%
now we can work on loading the document
 

00:01:31.080 --> 00:01:33.350 align:start position:0%
now we can work on loading the document
when<00:01:31.799><c> I</c><00:01:31.979><c> did</c><00:01:32.100><c> this</c><00:01:32.280><c> the</c><00:01:32.460><c> first</c><00:01:32.580><c> time</c><00:01:32.759><c> I</c><00:01:33.119><c> needed</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
when I did this the first time I needed
 

00:01:33.360 --> 00:01:36.109 align:start position:0%
when I did this the first time I needed
the<00:01:33.540><c> python</c><00:01:33.900><c> module</c><00:01:34.200><c> bs4</c><00:01:34.740><c> so</c><00:01:34.979><c> run</c><00:01:35.280><c> pip</c><00:01:35.880><c> install</c>

00:01:36.109 --> 00:01:36.119 align:start position:0%
the python module bs4 so run pip install
 

00:01:36.119 --> 00:01:37.550 align:start position:0%
the python module bs4 so run pip install
bs4

00:01:37.550 --> 00:01:37.560 align:start position:0%
bs4
 

00:01:37.560 --> 00:01:39.830 align:start position:0%
bs4
now<00:01:38.100><c> we</c><00:01:38.280><c> can</c><00:01:38.400><c> add</c><00:01:38.579><c> the</c><00:01:38.759><c> import</c><00:01:38.880><c> for</c><00:01:39.360><c> the</c><00:01:39.540><c> loader</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
now we can add the import for the loader
 

00:01:39.840 --> 00:01:43.010 align:start position:0%
now we can add the import for the loader
from<00:01:40.880><c> langchain.document</c><00:01:41.880><c> loaders</c><00:01:42.420><c> import</c>

00:01:43.010 --> 00:01:43.020 align:start position:0%
from langchain.document loaders import
 

00:01:43.020 --> 00:01:44.810 align:start position:0%
from langchain.document loaders import
web-based<00:01:43.979><c> loader</c>

00:01:44.810 --> 00:01:44.820 align:start position:0%
web-based loader
 

00:01:44.820 --> 00:01:47.450 align:start position:0%
web-based loader
then<00:01:45.180><c> load</c><00:01:45.540><c> the</c><00:01:45.659><c> document</c><00:01:46.040><c> loader</c><00:01:47.040><c> equals</c>

00:01:47.450 --> 00:01:47.460 align:start position:0%
then load the document loader equals
 

00:01:47.460 --> 00:01:50.389 align:start position:0%
then load the document loader equals
web-based<00:01:48.060><c> loader</c><00:01:48.420><c> and</c><00:01:48.840><c> the</c><00:01:48.960><c> URL</c><00:01:49.380><c> then</c><00:01:49.920><c> data</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
web-based loader and the URL then data
 

00:01:50.399 --> 00:01:52.850 align:start position:0%
web-based loader and the URL then data
equals<00:01:50.880><c> loader.load</c>

00:01:52.850 --> 00:01:52.860 align:start position:0%
equals loader.load
 

00:01:52.860 --> 00:01:55.429 align:start position:0%
equals loader.load
now<00:01:53.399><c> when</c><00:01:53.880><c> we</c><00:01:54.060><c> ask</c><00:01:54.180><c> it</c><00:01:54.420><c> a</c><00:01:54.600><c> question</c><00:01:54.780><c> chances</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
now when we ask it a question chances
 

00:01:55.439 --> 00:01:57.469 align:start position:0%
now when we ask it a question chances
are<00:01:55.619><c> the</c><00:01:55.860><c> whole</c><00:01:56.040><c> document</c><00:01:56.399><c> isn't</c><00:01:56.880><c> needed</c><00:01:57.240><c> to</c>

00:01:57.469 --> 00:01:57.479 align:start position:0%
are the whole document isn't needed to
 

00:01:57.479 --> 00:01:59.450 align:start position:0%
are the whole document isn't needed to
answer<00:01:57.659><c> the</c><00:01:57.960><c> whole</c><00:01:58.079><c> the</c><00:01:58.320><c> question</c><00:01:58.560><c> but</c><00:01:59.220><c> rather</c>

00:01:59.450 --> 00:01:59.460 align:start position:0%
answer the whole the question but rather
 

00:01:59.460 --> 00:02:01.130 align:start position:0%
answer the whole the question but rather
just<00:01:59.700><c> the</c><00:01:59.939><c> parts</c><00:02:00.060><c> that</c><00:02:00.360><c> talk</c><00:02:00.600><c> about</c><00:02:00.780><c> the</c>

00:02:01.130 --> 00:02:01.140 align:start position:0%
just the parts that talk about the
 

00:02:01.140 --> 00:02:03.770 align:start position:0%
just the parts that talk about the
concept<00:02:01.439><c> that</c><00:02:02.100><c> I'm</c><00:02:02.159><c> interested</c><00:02:02.640><c> in</c><00:02:02.759><c> but</c><00:02:03.540><c> how</c>

00:02:03.770 --> 00:02:03.780 align:start position:0%
concept that I'm interested in but how
 

00:02:03.780 --> 00:02:06.170 align:start position:0%
concept that I'm interested in but how
do<00:02:03.899><c> we</c><00:02:04.020><c> limit</c><00:02:04.200><c> what</c><00:02:04.680><c> gets</c><00:02:04.979><c> sent</c><00:02:05.100><c> to</c><00:02:05.280><c> the</c><00:02:05.460><c> model</c>

00:02:06.170 --> 00:02:06.180 align:start position:0%
do we limit what gets sent to the model
 

00:02:06.180 --> 00:02:08.510 align:start position:0%
do we limit what gets sent to the model
databases<00:02:07.079><c> are</c><00:02:07.439><c> great</c><00:02:07.619><c> for</c><00:02:07.799><c> searching</c><00:02:08.160><c> for</c><00:02:08.340><c> a</c>

00:02:08.510 --> 00:02:08.520 align:start position:0%
databases are great for searching for a
 

00:02:08.520 --> 00:02:10.309 align:start position:0%
databases are great for searching for a
subset<00:02:08.880><c> of</c><00:02:09.060><c> content</c><00:02:09.360><c> and</c><00:02:09.599><c> spitting</c><00:02:10.020><c> out</c><00:02:10.140><c> the</c>

00:02:10.309 --> 00:02:10.319 align:start position:0%
subset of content and spitting out the
 

00:02:10.319 --> 00:02:12.530 align:start position:0%
subset of content and spitting out the
results<00:02:10.440><c> so</c><00:02:11.160><c> let's</c><00:02:11.340><c> use</c><00:02:11.640><c> a</c><00:02:11.879><c> simple</c><00:02:12.060><c> Vector</c>

00:02:12.530 --> 00:02:12.540 align:start position:0%
results so let's use a simple Vector
 

00:02:12.540 --> 00:02:14.630 align:start position:0%
results so let's use a simple Vector
database<00:02:12.900><c> called</c><00:02:13.140><c> chroma</c><00:02:13.560><c> DB</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
database called chroma DB
 

00:02:14.640 --> 00:02:17.330 align:start position:0%
database called chroma DB
but<00:02:15.180><c> any</c><00:02:15.360><c> Vector</c><00:02:15.720><c> DB</c><00:02:16.080><c> doesn't</c><00:02:16.620><c> really</c><00:02:17.040><c> want</c>

00:02:17.330 --> 00:02:17.340 align:start position:0%
but any Vector DB doesn't really want
 

00:02:17.340 --> 00:02:19.070 align:start position:0%
but any Vector DB doesn't really want
the<00:02:17.580><c> full</c><00:02:17.760><c> document</c><00:02:18.120><c> it</c><00:02:18.420><c> wants</c><00:02:18.660><c> the</c><00:02:18.780><c> document</c>

00:02:19.070 --> 00:02:19.080 align:start position:0%
the full document it wants the document
 

00:02:19.080 --> 00:02:21.770 align:start position:0%
the full document it wants the document
chunked<00:02:19.620><c> up</c><00:02:19.739><c> into</c><00:02:20.220><c> smaller</c><00:02:20.700><c> pieces</c><00:02:21.000><c> so</c><00:02:21.599><c> we</c>

00:02:21.770 --> 00:02:21.780 align:start position:0%
chunked up into smaller pieces so we
 

00:02:21.780 --> 00:02:24.229 align:start position:0%
chunked up into smaller pieces so we
need<00:02:21.900><c> to</c><00:02:22.020><c> split</c><00:02:22.319><c> it</c><00:02:22.440><c> up</c><00:02:22.560><c> first</c><00:02:22.800><c> Lang</c><00:02:23.580><c> chain</c><00:02:23.879><c> has</c>

00:02:24.229 --> 00:02:24.239 align:start position:0%
need to split it up first Lang chain has
 

00:02:24.239 --> 00:02:25.670 align:start position:0%
need to split it up first Lang chain has
something<00:02:24.480><c> for</c><00:02:24.780><c> this</c><00:02:24.959><c> called</c><00:02:25.260><c> a</c><00:02:25.560><c> text</c>

00:02:25.670 --> 00:02:25.680 align:start position:0%
something for this called a text
 

00:02:25.680 --> 00:02:29.030 align:start position:0%
something for this called a text
splitter<00:02:26.040><c> so</c><00:02:26.879><c> let's</c><00:02:27.180><c> add</c><00:02:27.420><c> it</c><00:02:27.599><c> from</c><00:02:28.379><c> langchain</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
splitter so let's add it from langchain
 

00:02:29.040 --> 00:02:31.670 align:start position:0%
splitter so let's add it from langchain
text<00:02:29.220><c> splitter</c><00:02:29.640><c> import</c><00:02:30.420><c> recursive</c><00:02:31.379><c> character</c>

00:02:31.670 --> 00:02:31.680 align:start position:0%
text splitter import recursive character
 

00:02:31.680 --> 00:02:33.830 align:start position:0%
text splitter import recursive character
text<00:02:31.920><c> splitter</c><00:02:32.340><c> and</c><00:02:32.879><c> now</c><00:02:33.000><c> we</c><00:02:33.120><c> can</c><00:02:33.239><c> say</c><00:02:33.420><c> how</c><00:02:33.660><c> we</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
text splitter and now we can say how we
 

00:02:33.840 --> 00:02:35.869 align:start position:0%
text splitter and now we can say how we
want<00:02:33.959><c> the</c><00:02:34.140><c> text</c><00:02:34.260><c> split</c><00:02:34.680><c> up</c><00:02:34.800><c> text</c><00:02:35.520><c> splitter</c>

00:02:35.869 --> 00:02:35.879 align:start position:0%
want the text split up text splitter
 

00:02:35.879 --> 00:02:37.910 align:start position:0%
want the text split up text splitter
equals<00:02:36.420><c> recursive</c><00:02:36.959><c> character</c><00:02:37.379><c> text</c><00:02:37.560><c> splitter</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
equals recursive character text splitter
 

00:02:37.920 --> 00:02:42.350 align:start position:0%
equals recursive character text splitter
chunk<00:02:38.700><c> size</c><00:02:38.940><c> is</c><00:02:39.239><c> 500</c><00:02:39.840><c> and</c><00:02:40.020><c> overlap</c><00:02:40.560><c> is</c><00:02:40.980><c> zero</c><00:02:41.640><c> so</c>

00:02:42.350 --> 00:02:42.360 align:start position:0%
chunk size is 500 and overlap is zero so
 

00:02:42.360 --> 00:02:43.970 align:start position:0%
chunk size is 500 and overlap is zero so
every<00:02:42.660><c> chunk</c><00:02:42.900><c> is</c><00:02:43.200><c> going</c><00:02:43.319><c> to</c><00:02:43.440><c> be</c><00:02:43.560><c> 500</c>

00:02:43.970 --> 00:02:43.980 align:start position:0%
every chunk is going to be 500
 

00:02:43.980 --> 00:02:45.710 align:start position:0%
every chunk is going to be 500
characters<00:02:44.220><c> and</c><00:02:44.940><c> there's</c><00:02:45.060><c> no</c><00:02:45.239><c> overlap</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
characters and there's no overlap
 

00:02:45.720 --> 00:02:48.350 align:start position:0%
characters and there's no overlap
between<00:02:45.959><c> the</c><00:02:46.200><c> chunks</c><00:02:46.500><c> and</c><00:02:47.280><c> then</c><00:02:47.459><c> all</c><00:02:47.879><c> splits</c>

00:02:48.350 --> 00:02:48.360 align:start position:0%
between the chunks and then all splits
 

00:02:48.360 --> 00:02:50.990 align:start position:0%
between the chunks and then all splits
equals<00:02:48.840><c> textplitter</c><00:02:49.500><c> dot</c><00:02:50.099><c> split</c><00:02:50.340><c> documents</c>

00:02:50.990 --> 00:02:51.000 align:start position:0%
equals textplitter dot split documents
 

00:02:51.000 --> 00:02:53.150 align:start position:0%
equals textplitter dot split documents
and<00:02:51.480><c> feed</c><00:02:51.660><c> it</c><00:02:51.900><c> our</c><00:02:52.080><c> data</c>

00:02:53.150 --> 00:02:53.160 align:start position:0%
and feed it our data
 

00:02:53.160 --> 00:02:55.070 align:start position:0%
and feed it our data
now<00:02:53.640><c> we</c><00:02:53.819><c> can</c><00:02:53.940><c> add</c><00:02:54.120><c> those</c><00:02:54.360><c> chunks</c><00:02:54.780><c> to</c><00:02:54.959><c> the</c>

00:02:55.070 --> 00:02:55.080 align:start position:0%
now we can add those chunks to the
 

00:02:55.080 --> 00:02:57.589 align:start position:0%
now we can add those chunks to the
database<00:02:55.440><c> but</c><00:02:56.160><c> Vector</c><00:02:56.640><c> stores</c><00:02:56.819><c> don't</c><00:02:57.300><c> store</c>

00:02:57.589 --> 00:02:57.599 align:start position:0%
database but Vector stores don't store
 

00:02:57.599 --> 00:03:00.410 align:start position:0%
database but Vector stores don't store
just<00:02:58.019><c> regular</c><00:02:58.440><c> words</c><00:02:58.620><c> they</c><00:02:58.920><c> store</c><00:02:59.160><c> vectors</c><00:02:59.760><c> so</c>

00:03:00.410 --> 00:03:00.420 align:start position:0%
just regular words they store vectors so
 

00:03:00.420 --> 00:03:02.089 align:start position:0%
just regular words they store vectors so
the<00:03:00.660><c> embeddings</c><00:03:01.080><c> function</c><00:03:01.379><c> is</c><00:03:01.560><c> what</c><00:03:01.739><c> converts</c>

00:03:02.089 --> 00:03:02.099 align:start position:0%
the embeddings function is what converts
 

00:03:02.099 --> 00:03:04.550 align:start position:0%
the embeddings function is what converts
the<00:03:02.280><c> words</c><00:03:02.459><c> as</c><00:03:03.360><c> of</c><00:03:03.599><c> this</c><00:03:03.780><c> recording</c><00:03:04.140><c> we</c><00:03:04.440><c> don't</c>

00:03:04.550 --> 00:03:04.560 align:start position:0%
the words as of this recording we don't
 

00:03:04.560 --> 00:03:06.110 align:start position:0%
the words as of this recording we don't
have<00:03:04.800><c> an</c><00:03:04.980><c> embeddings</c><00:03:05.400><c> function</c><00:03:05.700><c> that</c><00:03:05.940><c> Lang</c>

00:03:06.110 --> 00:03:06.120 align:start position:0%
have an embeddings function that Lang
 

00:03:06.120 --> 00:03:08.990 align:start position:0%
have an embeddings function that Lang
chain<00:03:06.420><c> can</c><00:03:06.780><c> use</c><00:03:06.959><c> so</c><00:03:07.620><c> we'll</c><00:03:07.800><c> use</c><00:03:08.099><c> the</c><00:03:08.340><c> GPT</c><00:03:08.640><c> for</c>

00:03:08.990 --> 00:03:09.000 align:start position:0%
chain can use so we'll use the GPT for
 

00:03:09.000 --> 00:03:11.630 align:start position:0%
chain can use so we'll use the GPT for
all<00:03:09.239><c> embeddings</c><00:03:09.840><c> first</c><00:03:10.680><c> install</c><00:03:11.040><c> the</c><00:03:11.340><c> python</c>

00:03:11.630 --> 00:03:11.640 align:start position:0%
all embeddings first install the python
 

00:03:11.640 --> 00:03:14.990 align:start position:0%
all embeddings first install the python
modules<00:03:12.000><c> pip</c><00:03:12.659><c> install</c><00:03:12.920><c> GPD</c><00:03:13.920><c> for</c><00:03:14.099><c> all</c><00:03:14.340><c> and</c>

00:03:14.990 --> 00:03:15.000 align:start position:0%
modules pip install GPD for all and
 

00:03:15.000 --> 00:03:16.309 align:start position:0%
modules pip install GPD for all and
chromodb

00:03:16.309 --> 00:03:16.319 align:start position:0%
chromodb
 

00:03:16.319 --> 00:03:19.070 align:start position:0%
chromodb
then<00:03:16.739><c> in</c><00:03:17.040><c> the</c><00:03:17.159><c> file</c><00:03:17.340><c> we</c><00:03:17.700><c> add</c><00:03:18.000><c> from</c><00:03:18.480><c> langchain</c>

00:03:19.070 --> 00:03:19.080 align:start position:0%
then in the file we add from langchain
 

00:03:19.080 --> 00:03:21.830 align:start position:0%
then in the file we add from langchain
embeddings<00:03:19.560><c> import</c><00:03:20.040><c> GPT</c><00:03:20.640><c> for</c><00:03:21.000><c> all</c><00:03:21.180><c> embeddings</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
embeddings import GPT for all embeddings
 

00:03:21.840 --> 00:03:24.350 align:start position:0%
embeddings import GPT for all embeddings
and<00:03:22.560><c> then</c><00:03:22.620><c> import</c><00:03:22.800><c> the</c><00:03:23.159><c> database</c><00:03:23.519><c> from</c><00:03:24.180><c> line</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
and then import the database from line
 

00:03:24.360 --> 00:03:26.750 align:start position:0%
and then import the database from line
chain<00:03:24.659><c> Vector</c><00:03:25.080><c> stores</c><00:03:25.260><c> import</c><00:03:25.620><c> chroma</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
chain Vector stores import chroma
 

00:03:26.760 --> 00:03:29.809 align:start position:0%
chain Vector stores import chroma
now<00:03:27.420><c> instantiate</c><00:03:28.140><c> that</c><00:03:28.379><c> data</c><00:03:28.800><c> store</c><00:03:28.920><c> Vector</c>

00:03:29.809 --> 00:03:29.819 align:start position:0%
now instantiate that data store Vector
 

00:03:29.819 --> 00:03:32.270 align:start position:0%
now instantiate that data store Vector
store<00:03:30.000><c> equals</c><00:03:30.480><c> chroma</c><00:03:30.840><c> Dot</c><00:03:31.500><c> from</c><00:03:31.620><c> documents</c>

00:03:32.270 --> 00:03:32.280 align:start position:0%
store equals chroma Dot from documents
 

00:03:32.280 --> 00:03:35.089 align:start position:0%
store equals chroma Dot from documents
and<00:03:32.640><c> documents</c><00:03:33.180><c> equals</c><00:03:33.720><c> all</c><00:03:33.900><c> splits</c><00:03:34.379><c> and</c>

00:03:35.089 --> 00:03:35.099 align:start position:0%
and documents equals all splits and
 

00:03:35.099 --> 00:03:38.149 align:start position:0%
and documents equals all splits and
embedding<00:03:35.580><c> is</c><00:03:35.879><c> our</c><00:03:36.180><c> GPT</c><00:03:36.540><c> for</c><00:03:36.840><c> all</c><00:03:37.019><c> embeddings</c>

00:03:38.149 --> 00:03:38.159 align:start position:0%
embedding is our GPT for all embeddings
 

00:03:38.159 --> 00:03:40.490 align:start position:0%
embedding is our GPT for all embeddings
in<00:03:38.640><c> line</c><00:03:38.819><c> chain</c><00:03:39.120><c> the</c><00:03:39.360><c> central</c><00:03:39.540><c> concept</c><00:03:40.019><c> is</c><00:03:40.319><c> the</c>

00:03:40.490 --> 00:03:40.500 align:start position:0%
in line chain the central concept is the
 

00:03:40.500 --> 00:03:42.410 align:start position:0%
in line chain the central concept is the
chain<00:03:40.739><c> which</c><00:03:41.400><c> connects</c><00:03:41.760><c> a</c><00:03:41.879><c> bunch</c><00:03:42.000><c> of</c><00:03:42.120><c> tasks</c>

00:03:42.410 --> 00:03:42.420 align:start position:0%
chain which connects a bunch of tasks
 

00:03:42.420 --> 00:03:44.330 align:start position:0%
chain which connects a bunch of tasks
together<00:03:42.599><c> to</c><00:03:42.959><c> complete</c><00:03:43.260><c> a</c><00:03:43.379><c> larger</c><00:03:43.620><c> class</c><00:03:43.799><c> task</c>

00:03:44.330 --> 00:03:44.340 align:start position:0%
together to complete a larger class task
 

00:03:44.340 --> 00:03:47.270 align:start position:0%
together to complete a larger class task
we<00:03:45.000><c> can</c><00:03:45.180><c> use</c><00:03:45.299><c> the</c><00:03:45.599><c> retrieval</c><00:03:46.019><c> QA</c><00:03:46.379><c> Chain</c><00:03:46.620><c> by</c>

00:03:47.270 --> 00:03:47.280 align:start position:0%
we can use the retrieval QA Chain by
 

00:03:47.280 --> 00:03:49.910 align:start position:0%
we can use the retrieval QA Chain by
adding<00:03:47.640><c> to</c><00:03:47.819><c> our</c><00:03:48.000><c> file</c><00:03:48.239><c> from</c><00:03:48.920><c> linechain.chains</c>

00:03:49.910 --> 00:03:49.920 align:start position:0%
adding to our file from linechain.chains
 

00:03:49.920 --> 00:03:52.850 align:start position:0%
adding to our file from linechain.chains
import<00:03:50.220><c> retrieval</c><00:03:51.120><c> QA</c><00:03:51.480><c> and</c><00:03:52.080><c> then</c><00:03:52.200><c> QA</c><00:03:52.620><c> chain</c>

00:03:52.850 --> 00:03:52.860 align:start position:0%
import retrieval QA and then QA chain
 

00:03:52.860 --> 00:03:56.630 align:start position:0%
import retrieval QA and then QA chain
equals<00:03:53.340><c> retrieval</c><00:03:54.060><c> QA</c><00:03:54.659><c> from</c><00:03:55.440><c> chain</c><00:03:55.739><c> type</c><00:03:56.040><c> and</c>

00:03:56.630 --> 00:03:56.640 align:start position:0%
equals retrieval QA from chain type and
 

00:03:56.640 --> 00:03:59.809 align:start position:0%
equals retrieval QA from chain type and
then<00:03:56.760><c> olama</c><00:03:57.360><c> and</c><00:03:58.080><c> retriever</c><00:03:58.799><c> equals</c><00:03:59.459><c> Vector</c>

00:03:59.809 --> 00:03:59.819 align:start position:0%
then olama and retriever equals Vector
 

00:03:59.819 --> 00:04:01.490 align:start position:0%
then olama and retriever equals Vector
store<00:04:00.000><c> as</c><00:04:00.360><c> retriever</c>

00:04:01.490 --> 00:04:01.500 align:start position:0%
store as retriever
 

00:04:01.500 --> 00:04:03.589 align:start position:0%
store as retriever
now<00:04:01.980><c> we</c><00:04:02.159><c> can</c><00:04:02.280><c> ask</c><00:04:02.580><c> our</c><00:04:02.819><c> question</c><00:04:03.180><c> to</c><00:04:03.480><c> our</c>

00:04:03.589 --> 00:04:03.599 align:start position:0%
now we can ask our question to our
 

00:04:03.599 --> 00:04:06.830 align:start position:0%
now we can ask our question to our
document<00:04:04.379><c> question</c><00:04:04.920><c> is</c><00:04:05.220><c> when</c><00:04:05.879><c> was</c><00:04:06.180><c> Hawaii's</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
document question is when was Hawaii's
 

00:04:06.840 --> 00:04:08.930 align:start position:0%
document question is when was Hawaii's
request<00:04:07.319><c> for</c><00:04:07.560><c> major</c><00:04:07.739><c> disaster</c><00:04:08.280><c> declaration</c>

00:04:08.930 --> 00:04:08.940 align:start position:0%
request for major disaster declaration
 

00:04:08.940 --> 00:04:12.110 align:start position:0%
request for major disaster declaration
approved<00:04:09.540><c> so</c><00:04:09.959><c> QA</c><00:04:10.439><c> chain</c><00:04:10.680><c> with</c><00:04:11.159><c> a</c><00:04:11.340><c> query</c><00:04:11.580><c> of</c><00:04:11.879><c> the</c>

00:04:12.110 --> 00:04:12.120 align:start position:0%
approved so QA chain with a query of the
 

00:04:12.120 --> 00:04:14.390 align:start position:0%
approved so QA chain with a query of the
question<00:04:12.360><c> and</c><00:04:12.900><c> that's</c><00:04:13.019><c> it</c><00:04:13.260><c> pretty</c><00:04:13.799><c> quickly</c><00:04:14.099><c> we</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
question and that's it pretty quickly we
 

00:04:14.400 --> 00:04:16.610 align:start position:0%
question and that's it pretty quickly we
get<00:04:14.580><c> an</c><00:04:14.700><c> answer</c><00:04:14.840><c> sometimes</c><00:04:15.840><c> the</c><00:04:16.019><c> results</c><00:04:16.199><c> of</c>

00:04:16.610 --> 00:04:16.620 align:start position:0%
get an answer sometimes the results of
 

00:04:16.620 --> 00:04:19.370 align:start position:0%
get an answer sometimes the results of
the<00:04:16.739><c> query</c><00:04:16.979><c> isn't</c><00:04:17.940><c> spot</c><00:04:18.239><c> on</c><00:04:18.479><c> I</c><00:04:19.019><c> think</c><00:04:19.139><c> one</c><00:04:19.320><c> of</c>

00:04:19.370 --> 00:04:19.380 align:start position:0%
the query isn't spot on I think one of
 

00:04:19.380 --> 00:04:21.229 align:start position:0%
the query isn't spot on I think one of
the<00:04:19.440><c> biggest</c><00:04:19.620><c> factors</c><00:04:20.160><c> in</c><00:04:20.459><c> this</c><00:04:20.639><c> is</c><00:04:20.940><c> how</c><00:04:21.120><c> we</c>

00:04:21.229 --> 00:04:21.239 align:start position:0%
the biggest factors in this is how we
 

00:04:21.239 --> 00:04:23.810 align:start position:0%
the biggest factors in this is how we
split<00:04:21.540><c> up</c><00:04:21.660><c> that</c><00:04:22.199><c> Source</c><00:04:22.560><c> information</c><00:04:22.759><c> I</c><00:04:23.759><c> think</c>

00:04:23.810 --> 00:04:23.820 align:start position:0%
split up that Source information I think
 

00:04:23.820 --> 00:04:25.610 align:start position:0%
split up that Source information I think
it's<00:04:23.940><c> better</c><00:04:24.180><c> to</c><00:04:24.360><c> have</c><00:04:24.479><c> a</c><00:04:24.720><c> bit</c><00:04:24.840><c> of</c><00:04:24.960><c> a</c><00:04:25.080><c> overlap</c>

00:04:25.610 --> 00:04:25.620 align:start position:0%
it's better to have a bit of a overlap
 

00:04:25.620 --> 00:04:28.430 align:start position:0%
it's better to have a bit of a overlap
so<00:04:26.160><c> if</c><00:04:26.280><c> we</c><00:04:26.400><c> set</c><00:04:26.580><c> our</c><00:04:26.759><c> overlap</c><00:04:27.120><c> to</c><00:04:27.300><c> 50</c><00:04:27.540><c> we</c><00:04:28.199><c> often</c>

00:04:28.430 --> 00:04:28.440 align:start position:0%
so if we set our overlap to 50 we often
 

00:04:28.440 --> 00:04:30.350 align:start position:0%
so if we set our overlap to 50 we often
get<00:04:28.560><c> better</c><00:04:28.740><c> answers</c><00:04:29.160><c> but</c><00:04:29.639><c> you</c><00:04:29.940><c> may</c><00:04:30.120><c> need</c><00:04:30.300><c> to</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
get better answers but you may need to
 

00:04:30.360 --> 00:04:33.110 align:start position:0%
get better answers but you may need to
play<00:04:30.540><c> with</c><00:04:30.720><c> that</c><00:04:31.320><c> and</c><00:04:32.040><c> that</c><00:04:32.280><c> is</c><00:04:32.580><c> how</c><00:04:32.820><c> you</c><00:04:33.000><c> can</c>

00:04:33.110 --> 00:04:33.120 align:start position:0%
play with that and that is how you can
 

00:04:33.120 --> 00:04:36.530 align:start position:0%
play with that and that is how you can
use<00:04:33.300><c> olama</c><00:04:33.900><c> with</c><00:04:34.440><c> line</c><00:04:34.680><c> chain</c><00:04:34.979><c> and</c><00:04:35.460><c> python</c><00:04:35.880><c> to</c>

00:04:36.530 --> 00:04:36.540 align:start position:0%
use olama with line chain and python to
 

00:04:36.540 --> 00:04:38.629 align:start position:0%
use olama with line chain and python to
ask<00:04:36.660><c> a</c><00:04:36.840><c> document</c><00:04:37.199><c> a</c><00:04:37.500><c> question</c><00:04:37.680><c> you</c><00:04:38.400><c> could</c><00:04:38.520><c> use</c>

00:04:38.629 --> 00:04:38.639 align:start position:0%
ask a document a question you could use
 

00:04:38.639 --> 00:04:40.249 align:start position:0%
ask a document a question you could use
a<00:04:38.880><c> different</c><00:04:39.000><c> loader</c><00:04:39.419><c> to</c><00:04:39.600><c> point</c><00:04:39.780><c> to</c><00:04:40.020><c> a</c>

00:04:40.249 --> 00:04:40.259 align:start position:0%
a different loader to point to a
 

00:04:40.259 --> 00:04:42.710 align:start position:0%
a different loader to point to a
directory<00:04:40.620><c> instead</c><00:04:41.160><c> of</c><00:04:41.280><c> one</c><00:04:41.580><c> document</c><00:04:41.940><c> and</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
directory instead of one document and
 

00:04:42.720 --> 00:04:44.150 align:start position:0%
directory instead of one document and
you<00:04:42.900><c> would</c><00:04:43.020><c> probably</c><00:04:43.139><c> want</c><00:04:43.320><c> to</c><00:04:43.440><c> keep</c><00:04:43.620><c> the</c><00:04:43.800><c> data</c>

00:04:44.150 --> 00:04:44.160 align:start position:0%
you would probably want to keep the data
 

00:04:44.160 --> 00:04:45.950 align:start position:0%
you would probably want to keep the data
store<00:04:44.280><c> up</c><00:04:44.759><c> between</c><00:04:45.000><c> questions</c><00:04:45.360><c> so</c><00:04:45.720><c> you</c><00:04:45.840><c> don't</c>

00:04:45.950 --> 00:04:45.960 align:start position:0%
store up between questions so you don't
 

00:04:45.960 --> 00:04:47.930 align:start position:0%
store up between questions so you don't
have<00:04:46.080><c> to</c><00:04:46.199><c> keep</c><00:04:46.320><c> importing</c><00:04:46.860><c> I'll</c><00:04:47.639><c> share</c>

00:04:47.930 --> 00:04:47.940 align:start position:0%
have to keep importing I'll share
 

00:04:47.940 --> 00:04:49.969 align:start position:0%
have to keep importing I'll share
examples<00:04:48.300><c> on</c><00:04:48.540><c> how</c><00:04:48.780><c> to</c><00:04:48.900><c> do</c><00:04:49.020><c> that</c><00:04:49.199><c> later</c>

00:04:49.969 --> 00:04:49.979 align:start position:0%
examples on how to do that later
 

00:04:49.979 --> 00:04:52.129 align:start position:0%
examples on how to do that later
and<00:04:50.699><c> if</c><00:04:51.000><c> you</c><00:04:51.120><c> want</c><00:04:51.240><c> to</c><00:04:51.360><c> see</c><00:04:51.479><c> how</c><00:04:51.660><c> to</c><00:04:51.780><c> do</c><00:04:51.960><c> this</c>

00:04:52.129 --> 00:04:52.139 align:start position:0%
and if you want to see how to do this
 

00:04:52.139 --> 00:04:54.290 align:start position:0%
and if you want to see how to do this
with<00:04:52.440><c> JavaScript</c><00:04:52.860><c> keep</c><00:04:53.520><c> an</c><00:04:53.639><c> eye</c><00:04:53.820><c> out</c><00:04:53.940><c> for</c><00:04:54.180><c> that</c>

00:04:54.290 --> 00:04:54.300 align:start position:0%
with JavaScript keep an eye out for that
 

00:04:54.300 --> 00:04:55.850 align:start position:0%
with JavaScript keep an eye out for that
video<00:04:54.479><c> thanks</c><00:04:55.080><c> so</c><00:04:55.259><c> much</c><00:04:55.380><c> for</c><00:04:55.500><c> watching</c>

00:04:55.850 --> 00:04:55.860 align:start position:0%
video thanks so much for watching
 

00:04:55.860 --> 00:04:58.580 align:start position:0%
video thanks so much for watching
goodbye

