WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:03.270 align:start position:0%
 
and<00:00:00.719><c> howdy</c><00:00:01.240><c> guys</c><00:00:01.439><c> so</c><00:00:02.240><c> I</c><00:00:02.399><c> figured</c><00:00:02.720><c> out</c><00:00:03.000><c> what</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
and howdy guys so I figured out what
 

00:00:03.280 --> 00:00:06.829 align:start position:0%
and howdy guys so I figured out what
happened<00:00:04.160><c> I</c><00:00:04.759><c> everything</c><00:00:05.759><c> um</c><00:00:06.520><c> everything</c>

00:00:06.829 --> 00:00:06.839 align:start position:0%
happened I everything um everything
 

00:00:06.839 --> 00:00:08.790 align:start position:0%
happened I everything um everything
worked<00:00:07.240><c> I'm</c><00:00:07.319><c> going</c><00:00:07.439><c> to</c><00:00:07.560><c> press</c><00:00:07.799><c> save</c><00:00:08.200><c> password</c>

00:00:08.790 --> 00:00:08.800 align:start position:0%
worked I'm going to press save password
 

00:00:08.800 --> 00:00:10.910 align:start position:0%
worked I'm going to press save password
no<00:00:09.320><c> your</c><00:00:09.519><c> database</c><00:00:10.040><c> has</c><00:00:10.200><c> been</c><00:00:10.480><c> added</c><00:00:10.840><c> I'm</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
no your database has been added I'm
 

00:00:10.920 --> 00:00:12.709 align:start position:0%
no your database has been added I'm
going<00:00:11.040><c> to</c><00:00:11.160><c> press</c><00:00:11.440><c> explore</c><00:00:12.000><c> this</c><00:00:12.240><c> data</c><00:00:12.559><c> I'll</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
going to press explore this data I'll
 

00:00:12.719 --> 00:00:14.629 align:start position:0%
going to press explore this data I'll
show<00:00:12.880><c> you</c><00:00:13.000><c> in</c><00:00:13.120><c> a</c><00:00:13.240><c> moment</c><00:00:13.599><c> what</c><00:00:13.799><c> that</c><00:00:14.000><c> does</c><00:00:14.400><c> it's</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
show you in a moment what that does it's
 

00:00:14.639 --> 00:00:17.950 align:start position:0%
show you in a moment what that does it's
really<00:00:15.279><c> cool</c><00:00:16.279><c> and</c><00:00:16.600><c> here's</c><00:00:16.880><c> my</c><00:00:17.119><c> database</c><00:00:17.720><c> over</c>

00:00:17.950 --> 00:00:17.960 align:start position:0%
really cool and here's my database over
 

00:00:17.960 --> 00:00:20.590 align:start position:0%
really cool and here's my database over
here<00:00:18.199><c> I</c><00:00:18.279><c> think</c><00:00:18.400><c> it's</c><00:00:18.560><c> called</c><00:00:18.840><c> CSV</c><00:00:19.760><c> yeah</c><00:00:19.960><c> CSV</c><00:00:20.439><c> to</c>

00:00:20.590 --> 00:00:20.600 align:start position:0%
here I think it's called CSV yeah CSV to
 

00:00:20.600 --> 00:00:22.550 align:start position:0%
here I think it's called CSV yeah CSV to
metabase<00:00:21.359><c> the</c><00:00:21.439><c> only</c><00:00:21.640><c> thing</c><00:00:21.840><c> was</c><00:00:22.000><c> missing</c><00:00:22.359><c> was</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
metabase the only thing was missing was
 

00:00:22.560 --> 00:00:25.670 align:start position:0%
metabase the only thing was missing was
my<00:00:22.760><c> host</c><00:00:23.199><c> was</c><00:00:23.599><c> was</c><00:00:23.960><c> uh</c><00:00:24.439><c> was</c><00:00:24.680><c> misnamed</c><00:00:25.279><c> I</c><00:00:25.359><c> forgot</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
my host was was uh was misnamed I forgot
 

00:00:25.680 --> 00:00:27.950 align:start position:0%
my host was was uh was misnamed I forgot
the<00:00:25.800><c> J</c><00:00:26.119><c> at</c><00:00:26.240><c> the</c><00:00:26.400><c> beginning</c><00:00:27.400><c> everything</c><00:00:27.720><c> else</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
the J at the beginning everything else
 

00:00:27.960 --> 00:00:30.029 align:start position:0%
the J at the beginning everything else
was<00:00:28.119><c> all</c><00:00:28.480><c> good</c><00:00:29.039><c> okay</c><00:00:29.199><c> I</c><00:00:29.279><c> don't</c><00:00:29.400><c> need</c><00:00:29.519><c> to</c><00:00:29.640><c> use</c><00:00:29.759><c> an</c>

00:00:30.029 --> 00:00:30.039 align:start position:0%
was all good okay I don't need to use an
 

00:00:30.039 --> 00:00:32.670 align:start position:0%
was all good okay I don't need to use an
SSH<00:00:30.640><c> tunnel</c><00:00:31.039><c> for</c><00:00:31.320><c> database</c><00:00:31.880><c> connection</c><00:00:32.559><c> I</c>

00:00:32.670 --> 00:00:32.680 align:start position:0%
SSH tunnel for database connection I
 

00:00:32.680 --> 00:00:34.630 align:start position:0%
SSH tunnel for database connection I
press<00:00:32.960><c> save</c><00:00:33.280><c> changes</c><00:00:33.960><c> everything</c><00:00:34.239><c> was</c><00:00:34.399><c> all</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
press save changes everything was all
 

00:00:34.640 --> 00:00:36.709 align:start position:0%
press save changes everything was all
good<00:00:34.840><c> and</c><00:00:34.960><c> it</c><00:00:35.160><c> worked</c><00:00:36.120><c> if</c><00:00:36.200><c> you</c><00:00:36.320><c> have</c><00:00:36.480><c> any</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
good and it worked if you have any
 

00:00:36.719 --> 00:00:38.229 align:start position:0%
good and it worked if you have any
questions<00:00:37.160><c> and</c><00:00:37.480><c> you</c><00:00:37.559><c> can't</c><00:00:37.800><c> get</c><00:00:37.960><c> it</c><00:00:38.079><c> to</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
questions and you can't get it to
 

00:00:38.239 --> 00:00:40.910 align:start position:0%
questions and you can't get it to
connect<00:00:38.600><c> just</c><00:00:38.800><c> make</c><00:00:38.960><c> sure</c><00:00:39.320><c> that</c><00:00:39.600><c> the</c><00:00:40.280><c> that</c><00:00:40.600><c> all</c>

00:00:40.910 --> 00:00:40.920 align:start position:0%
connect just make sure that the that all
 

00:00:40.920 --> 00:00:44.389 align:start position:0%
connect just make sure that the that all
the<00:00:41.640><c> all</c><00:00:41.920><c> the</c><00:00:42.600><c> The</c><00:00:43.039><c> Columns</c><00:00:43.480><c> are</c><00:00:43.719><c> correct</c><00:00:44.120><c> all</c>

00:00:44.389 --> 00:00:44.399 align:start position:0%
the all the The Columns are correct all
 

00:00:44.399 --> 00:00:47.110 align:start position:0%
the all the The Columns are correct all
the<00:00:44.719><c> these</c><00:00:44.920><c> names</c><00:00:45.399><c> the</c><00:00:45.559><c> host</c><00:00:46.079><c> the</c><00:00:46.600><c> database</c>

00:00:47.110 --> 00:00:47.120 align:start position:0%
the these names the host the database
 

00:00:47.120 --> 00:00:48.549 align:start position:0%
the these names the host the database
name<00:00:47.360><c> username</c><00:00:47.800><c> and</c><00:00:47.920><c> the</c><00:00:48.039><c> password</c><00:00:48.360><c> are</c>

00:00:48.549 --> 00:00:48.559 align:start position:0%
name username and the password are
 

00:00:48.559 --> 00:00:50.750 align:start position:0%
name username and the password are
correct<00:00:48.879><c> and</c><00:00:49.120><c> everything</c><00:00:49.399><c> should</c><00:00:49.640><c> work</c><00:00:50.520><c> if</c><00:00:50.640><c> it</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
correct and everything should work if it
 

00:00:50.760 --> 00:00:53.150 align:start position:0%
correct and everything should work if it
doesn't<00:00:51.039><c> work</c><00:00:51.280><c> feel</c><00:00:51.440><c> free</c><00:00:51.640><c> to</c><00:00:51.800><c> reach</c><00:00:52.000><c> out</c><00:00:52.879><c> um</c>

00:00:53.150 --> 00:00:53.160 align:start position:0%
doesn't work feel free to reach out um
 

00:00:53.160 --> 00:00:56.029 align:start position:0%
doesn't work feel free to reach out um
in<00:00:53.320><c> the</c><00:00:53.559><c> comments</c><00:00:54.520><c> All</c><00:00:54.680><c> right</c><00:00:55.000><c> so</c><00:00:55.920><c> uh</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
in the comments All right so uh
 

00:00:56.039 --> 00:01:00.229 align:start position:0%
in the comments All right so uh
everything<00:00:56.359><c> is</c><00:00:56.520><c> all</c><00:00:56.840><c> good</c><00:00:57.440><c> or</c><00:00:57.800><c> slth</c><00:00:58.320><c> QA</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
everything is all good or slth QA
 

00:01:00.239 --> 00:01:04.310 align:start position:0%
everything is all good or slth QA
um<00:01:01.120><c> and</c><00:01:01.600><c> yeah</c><00:01:01.800><c> and</c><00:01:02.000><c> now</c><00:01:02.480><c> it</c><00:01:02.600><c> tells</c><00:01:02.920><c> me</c><00:01:03.760><c> it</c><00:01:03.920><c> took</c>

00:01:04.310 --> 00:01:04.320 align:start position:0%
um and yeah and now it tells me it took
 

00:01:04.320 --> 00:01:08.590 align:start position:0%
um and yeah and now it tells me it took
that<00:01:05.439><c> metabase</c><00:01:06.439><c> uh</c><00:01:06.560><c> takes</c><00:01:07.000><c> this</c><00:01:07.280><c> data</c><00:01:08.200><c> right</c>

00:01:08.590 --> 00:01:08.600 align:start position:0%
that metabase uh takes this data right
 

00:01:08.600 --> 00:01:11.830 align:start position:0%
that metabase uh takes this data right
and<00:01:09.119><c> it</c><00:01:09.400><c> offers</c><00:01:10.280><c> uh</c><00:01:10.400><c> to</c><00:01:10.600><c> show</c><00:01:10.840><c> me</c><00:01:11.240><c> what</c><00:01:11.520><c> what</c><00:01:11.680><c> it</c>

00:01:11.830 --> 00:01:11.840 align:start position:0%
and it offers uh to show me what what it
 

00:01:11.840 --> 00:01:14.310 align:start position:0%
and it offers uh to show me what what it
can<00:01:12.000><c> do</c><00:01:12.240><c> with</c><00:01:12.400><c> that</c><00:01:12.640><c> data</c><00:01:13.520><c> so</c><00:01:13.799><c> now</c><00:01:14.040><c> that</c><00:01:14.200><c> the</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
can do with that data so now that the
 

00:01:14.320 --> 00:01:16.630 align:start position:0%
can do with that data so now that the
data<00:01:14.560><c> is</c><00:01:14.680><c> in</c><00:01:14.840><c> metabase</c><00:01:15.400><c> this</c><00:01:15.479><c> is</c><00:01:15.680><c> the</c><00:01:15.880><c> best</c><00:01:16.400><c> the</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
data is in metabase this is the best the
 

00:01:16.640 --> 00:01:18.469 align:start position:0%
data is in metabase this is the best the
the<00:01:16.840><c> best</c><00:01:17.119><c> part</c><00:01:17.320><c> of</c><00:01:17.479><c> it</c><00:01:17.759><c> right</c><00:01:17.880><c> I</c><00:01:17.960><c> can</c><00:01:18.119><c> go</c><00:01:18.240><c> to</c>

00:01:18.469 --> 00:01:18.479 align:start position:0%
the best part of it right I can go to
 

00:01:18.479 --> 00:01:22.190 align:start position:0%
the best part of it right I can go to
ask<00:01:18.720><c> a</c><00:01:19.000><c> question</c><00:01:19.840><c> custom</c><00:01:20.680><c> question</c><00:01:21.680><c> I</c><00:01:21.840><c> choose</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
ask a question custom question I choose
 

00:01:22.200 --> 00:01:24.149 align:start position:0%
ask a question custom question I choose
my<00:01:22.439><c> data</c><00:01:22.799><c> source</c><00:01:23.159><c> what's</c><00:01:23.360><c> that</c><00:01:23.520><c> data</c><00:01:23.799><c> source</c>

00:01:24.149 --> 00:01:24.159 align:start position:0%
my data source what's that data source
 

00:01:24.159 --> 00:01:26.630 align:start position:0%
my data source what's that data source
CSV<00:01:24.680><c> to</c><00:01:24.799><c> metabase</c><00:01:25.360><c> whatever</c><00:01:25.680><c> I</c><00:01:25.880><c> named</c><00:01:26.280><c> it</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
CSV to metabase whatever I named it
 

00:01:26.640 --> 00:01:29.030 align:start position:0%
CSV to metabase whatever I named it
while<00:01:26.840><c> I</c><00:01:26.920><c> was</c><00:01:27.200><c> adding</c><00:01:27.520><c> a</c><00:01:27.680><c> new</c><00:01:28.000><c> database</c><00:01:28.680><c> over</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
while I was adding a new database over
 

00:01:29.040 --> 00:01:32.789 align:start position:0%
while I was adding a new database over
here<00:01:30.320><c> and</c><00:01:30.799><c> in</c><00:01:30.920><c> this</c><00:01:31.119><c> case</c><00:01:31.320><c> I</c><00:01:31.479><c> called</c><00:01:31.840><c> it</c><00:01:32.079><c> CSV</c><00:01:32.640><c> to</c>

00:01:32.789 --> 00:01:32.799 align:start position:0%
here and in this case I called it CSV to
 

00:01:32.799 --> 00:01:35.230 align:start position:0%
here and in this case I called it CSV to
metabase<00:01:33.560><c> and</c><00:01:33.759><c> there's</c><00:01:34.000><c> my</c><00:01:34.240><c> table</c><00:01:34.680><c> conversion</c>

00:01:35.230 --> 00:01:35.240 align:start position:0%
metabase and there's my table conversion
 

00:01:35.240 --> 00:01:37.630 align:start position:0%
metabase and there's my table conversion
data<00:01:35.640><c> which</c><00:01:35.799><c> is</c><00:01:36.360><c> that</c><00:01:36.600><c> straight</c><00:01:36.880><c> up</c><00:01:37.159><c> export</c>

00:01:37.630 --> 00:01:37.640 align:start position:0%
data which is that straight up export
 

00:01:37.640 --> 00:01:39.789 align:start position:0%
data which is that straight up export
from<00:01:37.920><c> CSV</c><00:01:38.640><c> I'm</c><00:01:38.759><c> going</c><00:01:38.840><c> to</c><00:01:38.960><c> choose</c><00:01:39.280><c> conversion</c>

00:01:39.789 --> 00:01:39.799 align:start position:0%
from CSV I'm going to choose conversion
 

00:01:39.799 --> 00:01:42.590 align:start position:0%
from CSV I'm going to choose conversion
data<00:01:40.720><c> and</c><00:01:40.920><c> now</c><00:01:41.200><c> let's</c><00:01:41.479><c> actually</c><00:01:41.920><c> look</c><00:01:42.119><c> at</c><00:01:42.320><c> that</c>

00:01:42.590 --> 00:01:42.600 align:start position:0%
data and now let's actually look at that
 

00:01:42.600 --> 00:01:46.429 align:start position:0%
data and now let's actually look at that
question<00:01:43.439><c> that</c><00:01:43.640><c> we</c><00:01:43.920><c> originally</c><00:01:44.920><c> had</c><00:01:45.600><c> from</c>

00:01:46.429 --> 00:01:46.439 align:start position:0%
question that we originally had from
 

00:01:46.439 --> 00:01:48.469 align:start position:0%
question that we originally had from
this<00:01:46.640><c> potential</c><00:01:47.119><c> employer</c><00:01:47.680><c> what</c><00:01:47.759><c> did</c><00:01:47.920><c> the</c><00:01:48.079><c> guy</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
this potential employer what did the guy
 

00:01:48.479 --> 00:01:51.109 align:start position:0%
this potential employer what did the guy
ask<00:01:49.479><c> they</c><00:01:49.600><c> said</c><00:01:49.880><c> please</c><00:01:50.119><c> identify</c><00:01:50.799><c> five</c>

00:01:51.109 --> 00:01:51.119 align:start position:0%
ask they said please identify five
 

00:01:51.119 --> 00:01:54.670 align:start position:0%
ask they said please identify five
traffic<00:01:51.600><c> sources</c><00:01:52.200><c> that</c><00:01:52.399><c> exceeded</c><00:01:52.960><c> the</c><00:01:53.159><c> target</c>

00:01:54.670 --> 00:01:54.680 align:start position:0%
traffic sources that exceeded the target
 

00:01:54.680 --> 00:01:57.950 align:start position:0%
traffic sources that exceeded the target
quality<00:01:55.680><c> um</c><00:01:56.439><c> a</c><00:01:56.640><c> traffic</c><00:01:56.960><c> source</c><00:01:57.240><c> is</c><00:01:57.360><c> a</c><00:01:57.560><c> subp</c>

00:01:57.950 --> 00:01:57.960 align:start position:0%
quality um a traffic source is a subp
 

00:01:57.960 --> 00:02:00.709 align:start position:0%
quality um a traffic source is a subp
publisher<00:01:58.640><c> that</c><00:01:58.880><c> came</c><00:01:59.200><c> in</c><00:01:59.439><c> under</c><00:01:59.960><c> a</c><00:02:00.119><c> publisher</c>

00:02:00.709 --> 00:02:00.719 align:start position:0%
publisher that came in under a publisher
 

00:02:00.719 --> 00:02:02.469 align:start position:0%
publisher that came in under a publisher
right<00:02:00.840><c> so</c><00:02:00.960><c> if</c><00:02:01.079><c> we</c><00:02:01.200><c> remember</c><00:02:01.560><c> over</c>

00:02:02.469 --> 00:02:02.479 align:start position:0%
right so if we remember over
 

00:02:02.479 --> 00:02:06.670 align:start position:0%
right so if we remember over
here<00:02:03.479><c> uh</c><00:02:03.680><c> sub</c><00:02:04.600><c> publisher</c><00:02:05.600><c> that</c><00:02:06.119><c> what</c><00:02:06.240><c> were</c><00:02:06.479><c> the</c>

00:02:06.670 --> 00:02:06.680 align:start position:0%
here uh sub publisher that what were the
 

00:02:06.680 --> 00:02:09.869 align:start position:0%
here uh sub publisher that what were the
most<00:02:07.479><c> the</c><00:02:07.640><c> sub</c><00:02:08.200><c> Publishers</c><00:02:09.200><c> that</c><00:02:09.520><c> uh</c><00:02:09.720><c> that</c>

00:02:09.869 --> 00:02:09.879 align:start position:0%
most the sub Publishers that uh that
 

00:02:09.879 --> 00:02:11.990 align:start position:0%
most the sub Publishers that uh that
gave<00:02:10.080><c> us</c><00:02:10.239><c> the</c><00:02:10.399><c> best</c><00:02:10.679><c> results</c><00:02:11.120><c> in</c><00:02:11.280><c> terms</c><00:02:11.520><c> of</c><00:02:11.680><c> new</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
gave us the best results in terms of new
 

00:02:12.000 --> 00:02:14.869 align:start position:0%
gave us the best results in terms of new
accounts<00:02:12.400><c> and</c><00:02:12.599><c> first</c><00:02:13.000><c> deposits</c><00:02:14.000><c> all</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
accounts and first deposits all
 

00:02:14.879 --> 00:02:17.830 align:start position:0%
accounts and first deposits all
right<00:02:15.879><c> let's</c><00:02:16.040><c> just</c><00:02:16.200><c> double</c><00:02:16.519><c> check</c><00:02:17.200><c> that</c><00:02:17.640><c> that</c>

00:02:17.830 --> 00:02:17.840 align:start position:0%
right let's just double check that that
 

00:02:17.840 --> 00:02:19.910 align:start position:0%
right let's just double check that that
exceeded<00:02:18.280><c> the</c><00:02:18.480><c> target</c><00:02:19.000><c> quality</c><00:02:19.599><c> okay</c><00:02:19.760><c> so</c>

00:02:19.910 --> 00:02:19.920 align:start position:0%
exceeded the target quality okay so
 

00:02:19.920 --> 00:02:21.990 align:start position:0%
exceeded the target quality okay so
let's<00:02:20.080><c> go</c><00:02:20.200><c> into</c><00:02:20.440><c> metabase</c><00:02:20.920><c> what</c><00:02:21.000><c> we</c><00:02:21.120><c> say</c><00:02:21.360><c> here</c>

00:02:21.990 --> 00:02:22.000 align:start position:0%
let's go into metabase what we say here
 

00:02:22.000 --> 00:02:23.350 align:start position:0%
let's go into metabase what we say here
we're<00:02:22.200><c> going</c><00:02:22.319><c> to</c><00:02:22.440><c> search</c><00:02:22.760><c> the</c><00:02:22.920><c> conversion</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
we're going to search the conversion
 

00:02:23.360 --> 00:02:25.030 align:start position:0%
we're going to search the conversion
data<00:02:23.680><c> I'm</c><00:02:23.879><c> going</c><00:02:23.959><c> to</c><00:02:24.120><c> go</c><00:02:24.239><c> into</c><00:02:24.599><c> subp</c>

00:02:25.030 --> 00:02:25.040 align:start position:0%
data I'm going to go into subp
 

00:02:25.040 --> 00:02:28.830 align:start position:0%
data I'm going to go into subp
Publishers<00:02:25.959><c> I'm</c><00:02:26.040><c> going</c><00:02:26.160><c> to</c><00:02:26.360><c> go</c><00:02:26.599><c> into</c><00:02:27.480><c> what</c><00:02:27.800><c> sum</c>

00:02:28.830 --> 00:02:28.840 align:start position:0%
Publishers I'm going to go into what sum
 

00:02:28.840 --> 00:02:31.110 align:start position:0%
Publishers I'm going to go into what sum
of<00:02:29.920><c> Su</c>

00:02:31.110 --> 00:02:31.120 align:start position:0%
of Su
 

00:02:31.120 --> 00:02:34.350 align:start position:0%
of Su
of<00:02:32.120><c> uh</c><00:02:32.680><c> let's</c><00:02:32.879><c> see</c><00:02:33.280><c> first</c>

00:02:34.350 --> 00:02:34.360 align:start position:0%
of uh let's see first
 

00:02:34.360 --> 00:02:37.190 align:start position:0%
of uh let's see first
deposit<00:02:35.360><c> right</c><00:02:35.640><c> pick</c><00:02:35.800><c> a</c><00:02:36.000><c> column</c><00:02:36.280><c> to</c><00:02:36.519><c> group</c><00:02:36.920><c> by</c>

00:02:37.190 --> 00:02:37.200 align:start position:0%
deposit right pick a column to group by
 

00:02:37.200 --> 00:02:39.110 align:start position:0%
deposit right pick a column to group by
Group<00:02:37.519><c> by</c><00:02:37.879><c> sub</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
Group by sub
 

00:02:39.120 --> 00:02:43.110 align:start position:0%
Group by sub
publisher<00:02:40.120><c> sub</c><00:02:40.519><c> publisher</c><00:02:41.280><c> and</c>

00:02:43.110 --> 00:02:43.120 align:start position:0%
publisher sub publisher and
 

00:02:43.120 --> 00:02:46.430 align:start position:0%
publisher sub publisher and
visualize<00:02:44.120><c> okay</c><00:02:44.599><c> and</c><00:02:44.840><c> then</c><00:02:45.159><c> I'm</c><00:02:45.280><c> going</c><00:02:45.400><c> to</c><00:02:46.239><c> uh</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
visualize okay and then I'm going to uh
 

00:02:46.440 --> 00:02:48.309 align:start position:0%
visualize okay and then I'm going to uh
this<00:02:46.640><c> gives</c><00:02:46.840><c> me</c><00:02:47.000><c> the</c><00:02:47.159><c> sub</c><00:02:47.519><c> Publishers</c><00:02:48.159><c> with</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
this gives me the sub Publishers with
 

00:02:48.319 --> 00:02:50.309 align:start position:0%
this gives me the sub Publishers with
the<00:02:48.440><c> most</c><00:02:48.640><c> amount</c><00:02:48.879><c> of</c><00:02:49.120><c> deposits</c><00:02:49.760><c> over</c><00:02:50.000><c> here</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
the most amount of deposits over here
 

00:02:50.319 --> 00:02:52.190 align:start position:0%
the most amount of deposits over here
right<00:02:50.599><c> so</c><00:02:51.000><c> I</c><00:02:51.080><c> want</c><00:02:51.200><c> to</c><00:02:51.440><c> actually</c><00:02:51.879><c> play</c><00:02:52.080><c> with</c>

00:02:52.190 --> 00:02:52.200 align:start position:0%
right so I want to actually play with
 

00:02:52.200 --> 00:02:54.949 align:start position:0%
right so I want to actually play with
the<00:02:52.360><c> data</c><00:02:52.680><c> I'm</c><00:02:52.879><c> going</c><00:02:53.159><c> going</c><00:02:53.400><c> into</c><00:02:53.959><c> settings</c>

00:02:54.949 --> 00:02:54.959 align:start position:0%
the data I'm going going into settings
 

00:02:54.959 --> 00:02:56.869 align:start position:0%
the data I'm going going into settings
I'm<00:02:55.040><c> going</c><00:02:55.159><c> to</c><00:02:55.280><c> say</c><00:02:55.519><c> table</c><00:02:56.000><c> options</c><00:02:56.680><c> let's</c>

00:02:56.869 --> 00:02:56.879 align:start position:0%
I'm going to say table options let's
 

00:02:56.879 --> 00:03:00.430 align:start position:0%
I'm going to say table options let's
just<00:02:57.040><c> view</c><00:02:57.280><c> it</c><00:02:57.400><c> as</c><00:02:57.519><c> a</c><00:02:57.680><c> bar</c><00:02:58.040><c> chart</c><00:02:58.519><c> here</c>

00:03:00.430 --> 00:03:00.440 align:start position:0%
just view it as a bar chart here
 

00:03:00.440 --> 00:03:02.070 align:start position:0%
just view it as a bar chart here
which<00:03:00.640><c> were</c><00:03:00.840><c> the</c><00:03:01.000><c> sub</c><00:03:01.400><c> Publishers</c><00:03:01.879><c> that</c>

00:03:02.070 --> 00:03:02.080 align:start position:0%
which were the sub Publishers that
 

00:03:02.080 --> 00:03:04.910 align:start position:0%
which were the sub Publishers that
exceeded<00:03:02.640><c> expectation</c><00:03:03.480><c> wow</c><00:03:04.120><c> this</c><00:03:04.480><c> specific</c>

00:03:04.910 --> 00:03:04.920 align:start position:0%
exceeded expectation wow this specific
 

00:03:04.920 --> 00:03:06.830 align:start position:0%
exceeded expectation wow this specific
sub<00:03:05.280><c> publisher</c><00:03:05.720><c> got</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
sub publisher got
 

00:03:06.840 --> 00:03:11.990 align:start position:0%
sub publisher got
71<00:03:07.840><c> 71</c><00:03:08.680><c> deposits</c><00:03:09.680><c> okay</c><00:03:10.480><c> uh</c><00:03:10.640><c> so</c><00:03:10.920><c> let's</c><00:03:11.239><c> actually</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
71 71 deposits okay uh so let's actually
 

00:03:12.000 --> 00:03:14.550 align:start position:0%
71 71 deposits okay uh so let's actually
so<00:03:12.280><c> that</c><00:03:12.400><c> seems</c><00:03:12.680><c> to</c><00:03:12.840><c> be</c><00:03:12.959><c> an</c><00:03:13.200><c> answer</c><00:03:13.560><c> to</c><00:03:13.879><c> that</c><00:03:14.319><c> to</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
so that seems to be an answer to that to
 

00:03:14.560 --> 00:03:15.430 align:start position:0%
so that seems to be an answer to that to
that

00:03:15.430 --> 00:03:15.440 align:start position:0%
that
 

00:03:15.440 --> 00:03:18.110 align:start position:0%
that
question<00:03:16.440><c> um</c><00:03:17.000><c> so</c><00:03:17.200><c> let's</c><00:03:17.400><c> go</c><00:03:17.519><c> ahead</c><00:03:17.720><c> and</c><00:03:17.920><c> just</c>

00:03:18.110 --> 00:03:18.120 align:start position:0%
question um so let's go ahead and just
 

00:03:18.120 --> 00:03:20.070 align:start position:0%
question um so let's go ahead and just
start<00:03:18.440><c> labeling</c><00:03:18.959><c> our</c><00:03:19.239><c> answers</c><00:03:19.640><c> over</c><00:03:19.840><c> here</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
start labeling our answers over here
 

00:03:20.080 --> 00:03:21.789 align:start position:0%
start labeling our answers over here
this<00:03:20.159><c> is</c><00:03:20.319><c> the</c><00:03:20.440><c> sub</c><00:03:20.760><c> Publishers</c><00:03:21.239><c> with</c><00:03:21.400><c> the</c><00:03:21.519><c> most</c>

00:03:21.789 --> 00:03:21.799 align:start position:0%
this is the sub Publishers with the most
 

00:03:21.799 --> 00:03:23.030 align:start position:0%
this is the sub Publishers with the most
amount<00:03:22.040><c> of</c>

00:03:23.030 --> 00:03:23.040 align:start position:0%
amount of
 

00:03:23.040 --> 00:03:25.550 align:start position:0%
amount of
deposits<00:03:24.040><c> um</c><00:03:24.400><c> and</c><00:03:24.519><c> let's</c><00:03:24.680><c> go</c><00:03:24.799><c> ahead</c><00:03:25.000><c> and</c><00:03:25.159><c> save</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
deposits um and let's go ahead and save
 

00:03:25.560 --> 00:03:29.390 align:start position:0%
deposits um and let's go ahead and save
this<00:03:25.799><c> over</c><00:03:26.080><c> here</c><00:03:26.879><c> okay</c><00:03:27.879><c> um</c><00:03:28.439><c> okay</c><00:03:28.760><c> this</c><00:03:28.920><c> is</c>

00:03:29.390 --> 00:03:29.400 align:start position:0%
this over here okay um okay this is
 

00:03:29.400 --> 00:03:31.229 align:start position:0%
this over here okay um okay this is
count

00:03:31.229 --> 00:03:31.239 align:start position:0%
count
 

00:03:31.239 --> 00:03:36.229 align:start position:0%
count
of<00:03:31.920><c> deposits</c><00:03:32.879><c> oh</c><00:03:33.159><c> sum</c><00:03:33.720><c> of</c><00:03:34.720><c> right</c><00:03:34.920><c> sum</c><00:03:35.239><c> of</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
of deposits oh sum of right sum of
 

00:03:36.239 --> 00:03:38.910 align:start position:0%
of deposits oh sum of right sum of
deposits<00:03:37.239><c> per</c><00:03:37.879><c> sub</c>

00:03:38.910 --> 00:03:38.920 align:start position:0%
deposits per sub
 

00:03:38.920 --> 00:03:43.309 align:start position:0%
deposits per sub
publisher<00:03:39.920><c> okay</c><00:03:40.400><c> cool</c><00:03:41.400><c> per</c><00:03:41.720><c> sub</c><00:03:42.319><c> publisher</c>

00:03:43.309 --> 00:03:43.319 align:start position:0%
publisher okay cool per sub publisher
 

00:03:43.319 --> 00:03:46.670 align:start position:0%
publisher okay cool per sub publisher
sum<00:03:43.599><c> of</c><00:03:43.799><c> deposits</c><00:03:44.319><c> per</c><00:03:44.560><c> sub</c><00:03:45.239><c> per</c><00:03:45.480><c> sub</c>

00:03:46.670 --> 00:03:46.680 align:start position:0%
sum of deposits per sub per sub
 

00:03:46.680 --> 00:03:49.229 align:start position:0%
sum of deposits per sub per sub
publisher<00:03:47.680><c> and</c><00:03:48.159><c> uh</c><00:03:48.360><c> yeah</c><00:03:48.680><c> where</c><00:03:48.840><c> is</c><00:03:48.959><c> it</c><00:03:49.120><c> going</c>

00:03:49.229 --> 00:03:49.239 align:start position:0%
publisher and uh yeah where is it going
 

00:03:49.239 --> 00:03:51.750 align:start position:0%
publisher and uh yeah where is it going
to<00:03:49.400><c> go</c><00:03:49.680><c> into</c><00:03:50.680><c> uh</c><00:03:50.799><c> we</c><00:03:50.879><c> should</c><00:03:51.080><c> really</c><00:03:51.319><c> create</c><00:03:51.599><c> a</c>

00:03:51.750 --> 00:03:51.760 align:start position:0%
to go into uh we should really create a
 

00:03:51.760 --> 00:03:55.429 align:start position:0%
to go into uh we should really create a
new<00:03:52.360><c> collection</c><00:03:53.360><c> all</c><00:03:53.760><c> personal</c><00:03:54.439><c> collections</c>

00:03:55.429 --> 00:03:55.439 align:start position:0%
new collection all personal collections
 

00:03:55.439 --> 00:03:56.830 align:start position:0%
new collection all personal collections
you<00:03:55.560><c> know</c><00:03:55.720><c> what</c><00:03:55.879><c> maybe</c><00:03:56.120><c> we</c><00:03:56.200><c> should</c><00:03:56.400><c> create</c><00:03:56.680><c> a</c>

00:03:56.830 --> 00:03:56.840 align:start position:0%
you know what maybe we should create a
 

00:03:56.840 --> 00:03:58.990 align:start position:0%
you know what maybe we should create a
new<00:03:57.079><c> collection</c><00:03:57.599><c> before</c><00:03:57.840><c> we</c><00:03:58.079><c> actually</c><00:03:58.519><c> save</c>

00:03:58.990 --> 00:03:59.000 align:start position:0%
new collection before we actually save
 

00:03:59.000 --> 00:04:02.869 align:start position:0%
new collection before we actually save
this<00:03:59.959><c> okay</c><00:04:00.120><c> so</c><00:04:00.319><c> I'm</c><00:04:00.400><c> going</c><00:04:00.519><c> to</c><00:04:00.640><c> go</c><00:04:01.280><c> into</c><00:04:02.280><c> uh</c>

00:04:02.869 --> 00:04:02.879 align:start position:0%
this okay so I'm going to go into uh
 

00:04:02.879 --> 00:04:05.069 align:start position:0%
this okay so I'm going to go into uh
into

00:04:05.069 --> 00:04:05.079 align:start position:0%
into
 

00:04:05.079 --> 00:04:07.589 align:start position:0%
into
metabase<00:04:06.079><c> should</c><00:04:06.319><c> we</c><00:04:06.480><c> create</c><00:04:06.840><c> let's</c><00:04:07.079><c> create</c><00:04:07.439><c> a</c>

00:04:07.589 --> 00:04:07.599 align:start position:0%
metabase should we create let's create a
 

00:04:07.599 --> 00:04:10.030 align:start position:0%
metabase should we create let's create a
new<00:04:08.079><c> new</c><00:04:08.400><c> collection</c><00:04:08.959><c> I'm</c><00:04:09.079><c> going</c><00:04:09.200><c> to</c><00:04:09.319><c> go</c><00:04:09.519><c> into</c>

00:04:10.030 --> 00:04:10.040 align:start position:0%
new new collection I'm going to go into
 

00:04:10.040 --> 00:04:13.670 align:start position:0%
new new collection I'm going to go into
create<00:04:11.040><c> new</c><00:04:11.319><c> dashboard</c><00:04:11.840><c> New</c><00:04:12.120><c> Pulse</c>

00:04:13.670 --> 00:04:13.680 align:start position:0%
create new dashboard New Pulse
 

00:04:13.680 --> 00:04:17.509 align:start position:0%
create new dashboard New Pulse
no<00:04:14.680><c> browse</c><00:04:15.040><c> all</c><00:04:15.280><c> items</c><00:04:16.239><c> okay</c><00:04:16.720><c> I</c><00:04:16.959><c> press</c><00:04:17.280><c> new</c>

00:04:17.509 --> 00:04:17.519 align:start position:0%
no browse all items okay I press new
 

00:04:17.519 --> 00:04:19.110 align:start position:0%
no browse all items okay I press new
collection<00:04:18.120><c> what's</c><00:04:18.440><c> the</c><00:04:18.560><c> name</c><00:04:18.720><c> of</c><00:04:18.919><c> this</c>

00:04:19.110 --> 00:04:19.120 align:start position:0%
collection what's the name of this
 

00:04:19.120 --> 00:04:21.350 align:start position:0%
collection what's the name of this
company<00:04:19.959><c> is</c><00:04:20.280><c> start</c>

00:04:21.350 --> 00:04:21.360 align:start position:0%
company is start
 

00:04:21.360 --> 00:04:25.350 align:start position:0%
company is start
app<00:04:22.360><c> the</c><00:04:22.560><c> start</c><00:04:22.840><c> app</c><00:04:23.320><c> collection</c><00:04:24.320><c> okay</c>

00:04:25.350 --> 00:04:25.360 align:start position:0%
app the start app collection okay
 

00:04:25.360 --> 00:04:28.030 align:start position:0%
app the start app collection okay
create<00:04:26.360><c> cool</c><00:04:26.880><c> now</c><00:04:27.160><c> whenever</c><00:04:27.479><c> I</c><00:04:27.560><c> want</c><00:04:27.680><c> to</c><00:04:27.800><c> see</c>

00:04:28.030 --> 00:04:28.040 align:start position:0%
create cool now whenever I want to see
 

00:04:28.040 --> 00:04:31.310 align:start position:0%
create cool now whenever I want to see
all<00:04:28.280><c> the</c><00:04:28.680><c> dashboards</c><00:04:29.520><c> the</c><00:04:29.840><c> the</c><00:04:30.520><c> pulses</c><00:04:31.120><c> and</c>

00:04:31.310 --> 00:04:31.320 align:start position:0%
all the dashboards the the pulses and
 

00:04:31.320 --> 00:04:33.590 align:start position:0%
all the dashboards the the pulses and
the<00:04:31.479><c> questions</c><00:04:32.000><c> around</c><00:04:32.320><c> a</c><00:04:32.560><c> specific</c><00:04:33.160><c> client</c>

00:04:33.590 --> 00:04:33.600 align:start position:0%
the questions around a specific client
 

00:04:33.600 --> 00:04:35.830 align:start position:0%
the questions around a specific client
for<00:04:33.800><c> example</c><00:04:34.520><c> I'll</c><00:04:34.759><c> just</c><00:04:35.039><c> have</c><00:04:35.320><c> be</c><00:04:35.479><c> able</c><00:04:35.680><c> to</c>

00:04:35.830 --> 00:04:35.840 align:start position:0%
for example I'll just have be able to
 

00:04:35.840 --> 00:04:38.189 align:start position:0%
for example I'll just have be able to
put<00:04:35.960><c> it</c><00:04:36.199><c> within</c><00:04:36.479><c> start</c><00:04:36.840><c> app</c><00:04:37.800><c> uh</c><00:04:37.919><c> the</c><00:04:38.000><c> nice</c>

00:04:38.189 --> 00:04:38.199 align:start position:0%
put it within start app uh the nice
 

00:04:38.199 --> 00:04:41.189 align:start position:0%
put it within start app uh the nice
thing<00:04:38.360><c> about</c><00:04:38.520><c> metabase</c><00:04:39.080><c> is</c><00:04:39.240><c> you</c><00:04:39.320><c> can</c><00:04:39.560><c> reload</c>

00:04:41.189 --> 00:04:41.199 align:start position:0%
thing about metabase is you can reload
 

00:04:41.199 --> 00:04:45.230 align:start position:0%
thing about metabase is you can reload
it<00:04:42.199><c> um</c><00:04:43.000><c> controlr</c><00:04:44.000><c> and</c><00:04:44.120><c> it'll</c><00:04:44.400><c> take</c><00:04:44.600><c> me</c><00:04:44.840><c> back</c><00:04:45.039><c> to</c>

00:04:45.230 --> 00:04:45.240 align:start position:0%
it um controlr and it'll take me back to
 

00:04:45.240 --> 00:04:47.909 align:start position:0%
it um controlr and it'll take me back to
this<00:04:45.400><c> view</c><00:04:46.400><c> this</c><00:04:46.639><c> is</c><00:04:47.080><c> and</c><00:04:47.240><c> now</c><00:04:47.479><c> I'm</c><00:04:47.600><c> going</c><00:04:47.720><c> to</c>

00:04:47.909 --> 00:04:47.919 align:start position:0%
this view this is and now I'm going to
 

00:04:47.919 --> 00:04:50.830 align:start position:0%
this view this is and now I'm going to
save<00:04:48.560><c> I'm</c><00:04:48.919><c> I'm</c><00:04:49.360><c> just</c><00:04:49.520><c> need</c><00:04:49.720><c> to</c><00:04:49.919><c> do</c><00:04:50.120><c> that</c><00:04:50.360><c> again</c>

00:04:50.830 --> 00:04:50.840 align:start position:0%
save I'm I'm just need to do that again
 

00:04:50.840 --> 00:04:53.550 align:start position:0%
save I'm I'm just need to do that again
table<00:04:51.400><c> options</c><00:04:52.120><c> I'm</c><00:04:52.199><c> going</c><00:04:52.320><c> to</c><00:04:52.479><c> go</c><00:04:52.600><c> into</c><00:04:52.800><c> a</c><00:04:52.960><c> bar</c>

00:04:53.550 --> 00:04:53.560 align:start position:0%
table options I'm going to go into a bar
 

00:04:53.560 --> 00:04:56.310 align:start position:0%
table options I'm going to go into a bar
chart<00:04:54.560><c> this</c><00:04:54.800><c> is</c><00:04:55.520><c> and</c><00:04:55.639><c> I'm</c><00:04:55.759><c> going</c><00:04:55.880><c> to</c><00:04:56.039><c> press</c>

00:04:56.310 --> 00:04:56.320 align:start position:0%
chart this is and I'm going to press
 

00:04:56.320 --> 00:04:58.710 align:start position:0%
chart this is and I'm going to press
save<00:04:57.120><c> I'm</c><00:04:57.280><c> going</c><00:04:57.400><c> to</c><00:04:57.560><c> save</c><00:04:57.800><c> it</c><00:04:57.919><c> in</c><00:04:58.039><c> a</c><00:04:58.240><c> specific</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
save I'm going to save it in a specific
 

00:04:58.720 --> 00:05:00.510 align:start position:0%
save I'm going to save it in a specific
collection<00:04:59.720><c> in</c><00:04:59.840><c> this</c><00:05:00.039><c> case</c><00:05:00.199><c> I'm</c><00:05:00.280><c> going</c><00:05:00.400><c> to</c>

00:05:00.510 --> 00:05:00.520 align:start position:0%
collection in this case I'm going to
 

00:05:00.520 --> 00:05:03.070 align:start position:0%
collection in this case I'm going to
save<00:05:00.680><c> it</c><00:05:00.800><c> in</c><00:05:00.919><c> the</c><00:05:01.080><c> start</c><00:05:01.400><c> app</c><00:05:01.960><c> collection</c><00:05:02.960><c> what</c>

00:05:03.070 --> 00:05:03.080 align:start position:0%
save it in the start app collection what
 

00:05:03.080 --> 00:05:04.390 align:start position:0%
save it in the start app collection what
am<00:05:03.199><c> I</c><00:05:03.280><c> going</c><00:05:03.400><c> to</c><00:05:03.560><c> call</c><00:05:03.840><c> this</c><00:05:04.000><c> I'm</c><00:05:04.120><c> going</c><00:05:04.240><c> to</c>

00:05:04.390 --> 00:05:04.400 align:start position:0%
am I going to call this I'm going to
 

00:05:04.400 --> 00:05:06.510 align:start position:0%
am I going to call this I'm going to
call<00:05:04.680><c> this</c><00:05:05.080><c> Su</c>

00:05:06.510 --> 00:05:06.520 align:start position:0%
call this Su
 

00:05:06.520 --> 00:05:10.350 align:start position:0%
call this Su
of<00:05:07.520><c> uh</c><00:05:07.840><c> deposits</c><00:05:08.639><c> oh</c><00:05:08.800><c> there</c><00:05:08.919><c> it</c><00:05:09.120><c> go</c><00:05:09.720><c> okay</c><00:05:10.000><c> Pur</c>

00:05:10.350 --> 00:05:10.360 align:start position:0%
of uh deposits oh there it go okay Pur
 

00:05:10.360 --> 00:05:12.029 align:start position:0%
of uh deposits oh there it go okay Pur
sub<00:05:10.600><c> publisher</c>

00:05:12.029 --> 00:05:12.039 align:start position:0%
sub publisher
 

00:05:12.039 --> 00:05:15.390 align:start position:0%
sub publisher
save<00:05:13.039><c> Okay</c><00:05:13.400><c> add</c><00:05:13.600><c> this</c><00:05:13.680><c> to</c><00:05:13.800><c> a</c><00:05:13.960><c> dashboard</c>

00:05:15.390 --> 00:05:15.400 align:start position:0%
save Okay add this to a dashboard
 

00:05:15.400 --> 00:05:18.150 align:start position:0%
save Okay add this to a dashboard
correct<00:05:16.400><c> okay</c><00:05:16.560><c> within</c><00:05:16.919><c> start</c><00:05:17.280><c> app</c><00:05:17.600><c> collection</c>

00:05:18.150 --> 00:05:18.160 align:start position:0%
correct okay within start app collection
 

00:05:18.160 --> 00:05:20.189 align:start position:0%
correct okay within start app collection
I'm<00:05:18.280><c> going</c><00:05:18.400><c> to</c><00:05:18.560><c> create</c><00:05:18.800><c> a</c><00:05:18.960><c> new</c><00:05:19.280><c> dashboard</c><00:05:20.039><c> and</c>

00:05:20.189 --> 00:05:20.199 align:start position:0%
I'm going to create a new dashboard and
 

00:05:20.199 --> 00:05:22.150 align:start position:0%
I'm going to create a new dashboard and
what<00:05:20.319><c> am</c><00:05:20.400><c> I</c><00:05:20.479><c> going</c><00:05:20.600><c> to</c><00:05:20.759><c> call</c><00:05:21.120><c> this</c><00:05:21.919><c> I'm</c><00:05:22.039><c> going</c>

00:05:22.150 --> 00:05:22.160 align:start position:0%
what am I going to call this I'm going
 

00:05:22.160 --> 00:05:26.110 align:start position:0%
what am I going to call this I'm going
to<00:05:22.360><c> call</c><00:05:22.759><c> this</c><00:05:23.600><c> um</c><00:05:24.600><c> this</c><00:05:24.919><c> dashboard</c><00:05:25.880><c> I'm</c><00:05:26.000><c> going</c>

00:05:26.110 --> 00:05:26.120 align:start position:0%
to call this um this dashboard I'm going
 

00:05:26.120 --> 00:05:30.830 align:start position:0%
to call this um this dashboard I'm going
to<00:05:26.280><c> do</c><00:05:26.680><c> start</c><00:05:27.520><c> app</c><00:05:28.520><c> assignment</c>

00:05:30.830 --> 00:05:30.840 align:start position:0%
to do start app assignment
 

00:05:30.840 --> 00:05:32.430 align:start position:0%
to do start app assignment
okay

00:05:32.430 --> 00:05:32.440 align:start position:0%
okay
 

00:05:32.440 --> 00:05:34.909 align:start position:0%
okay
create

00:05:34.909 --> 00:05:34.919 align:start position:0%
create
 

00:05:34.919 --> 00:05:37.230 align:start position:0%
create
cool<00:05:35.919><c> all</c><00:05:36.120><c> righty</c><00:05:36.479><c> cool</c><00:05:36.720><c> and</c><00:05:36.880><c> this</c><00:05:37.000><c> I'm</c><00:05:37.120><c> going</c>

00:05:37.230 --> 00:05:37.240 align:start position:0%
cool all righty cool and this I'm going
 

00:05:37.240 --> 00:05:39.029 align:start position:0%
cool all righty cool and this I'm going
to<00:05:37.400><c> spread</c><00:05:37.759><c> out</c><00:05:38.000><c> all</c><00:05:38.160><c> the</c><00:05:38.319><c> way</c><00:05:38.639><c> just</c><00:05:38.800><c> because</c>

00:05:39.029 --> 00:05:39.039 align:start position:0%
to spread out all the way just because
 

00:05:39.039 --> 00:05:41.230 align:start position:0%
to spread out all the way just because
it's<00:05:39.240><c> got</c><00:05:39.600><c> so</c><00:05:39.800><c> many</c><00:05:40.160><c> different</c><00:05:40.680><c> columns</c><00:05:41.120><c> so</c>

00:05:41.230 --> 00:05:41.240 align:start position:0%
it's got so many different columns so
 

00:05:41.240 --> 00:05:43.710 align:start position:0%
it's got so many different columns so
many<00:05:41.880><c> Publishers</c><00:05:42.880><c> and</c><00:05:43.039><c> I'm</c><00:05:43.160><c> going</c><00:05:43.280><c> to</c><00:05:43.440><c> press</c>

00:05:43.710 --> 00:05:43.720 align:start position:0%
many Publishers and I'm going to press
 

00:05:43.720 --> 00:05:47.029 align:start position:0%
many Publishers and I'm going to press
save<00:05:44.120><c> over</c><00:05:44.840><c> here</c><00:05:45.840><c> all</c><00:05:46.000><c> right</c><00:05:46.240><c> guys</c><00:05:46.520><c> cool</c><00:05:46.800><c> so</c>

00:05:47.029 --> 00:05:47.039 align:start position:0%
save over here all right guys cool so
 

00:05:47.039 --> 00:05:49.309 align:start position:0%
save over here all right guys cool so
now<00:05:47.280><c> we</c><00:05:47.440><c> know</c><00:05:48.039><c> over</c><00:05:48.240><c> here</c><00:05:48.440><c> we</c><00:05:48.560><c> have</c><00:05:48.680><c> the</c><00:05:48.840><c> sum</c><00:05:49.080><c> of</c>

00:05:49.309 --> 00:05:49.319 align:start position:0%
now we know over here we have the sum of
 

00:05:49.319 --> 00:05:52.990 align:start position:0%
now we know over here we have the sum of
deposits<00:05:50.080><c> per</c><00:05:50.360><c> sub</c><00:05:51.199><c> publisher</c><00:05:52.199><c> now</c><00:05:52.720><c> there's</c>

00:05:52.990 --> 00:05:53.000 align:start position:0%
deposits per sub publisher now there's
 

00:05:53.000 --> 00:05:55.350 align:start position:0%
deposits per sub publisher now there's
so<00:05:53.280><c> much</c><00:05:53.919><c> there's</c><00:05:54.120><c> so</c><00:05:54.319><c> much</c><00:05:54.639><c> data</c><00:05:55.000><c> over</c><00:05:55.199><c> here</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
so much there's so much data over here
 

00:05:55.360 --> 00:05:57.629 align:start position:0%
so much there's so much data over here
it's<00:05:55.479><c> a</c><00:05:55.600><c> little</c><00:05:55.960><c> hard</c><00:05:56.280><c> to</c><00:05:56.600><c> really</c><00:05:56.960><c> hover</c><00:05:57.360><c> over</c>

00:05:57.629 --> 00:05:57.639 align:start position:0%
it's a little hard to really hover over
 

00:05:57.639 --> 00:06:00.590 align:start position:0%
it's a little hard to really hover over
it<00:05:58.080><c> maybe</c><00:05:58.960><c> we</c><00:05:59.120><c> can</c><00:05:59.360><c> if</c><00:05:59.639><c> if</c><00:05:59.680><c> we</c><00:05:59.840><c> wanted</c><00:06:00.120><c> to</c><00:06:00.520><c> we</c>

00:06:00.590 --> 00:06:00.600 align:start position:0%
it maybe we can if if we wanted to we
 

00:06:00.600 --> 00:06:03.749 align:start position:0%
it maybe we can if if we wanted to we
can<00:06:00.840><c> also</c><00:06:01.759><c> cut</c><00:06:02.000><c> out</c><00:06:02.360><c> all</c><00:06:02.560><c> the</c><00:06:02.759><c> sub</c><00:06:03.120><c> Publishers</c>

00:06:03.749 --> 00:06:03.759 align:start position:0%
can also cut out all the sub Publishers
 

00:06:03.759 --> 00:06:07.790 align:start position:0%
can also cut out all the sub Publishers
that<00:06:04.080><c> had</c><00:06:05.080><c> Le</c><00:06:05.560><c> zero</c><00:06:06.280><c> deposits</c><00:06:07.280><c> all</c><00:06:07.440><c> right</c><00:06:07.639><c> so</c>

00:06:07.790 --> 00:06:07.800 align:start position:0%
that had Le zero deposits all right so
 

00:06:07.800 --> 00:06:09.710 align:start position:0%
that had Le zero deposits all right so
let's<00:06:08.000><c> go</c><00:06:08.120><c> ahead</c><00:06:08.319><c> and</c><00:06:08.560><c> edit</c><00:06:09.039><c> and</c><00:06:09.199><c> now</c><00:06:09.440><c> that</c><00:06:09.599><c> we</c>

00:06:09.710 --> 00:06:09.720 align:start position:0%
let's go ahead and edit and now that we
 

00:06:09.720 --> 00:06:11.029 align:start position:0%
let's go ahead and edit and now that we
have<00:06:09.880><c> this</c><00:06:10.000><c> in</c><00:06:10.120><c> the</c><00:06:10.360><c> dashboard</c><00:06:10.800><c> we're</c><00:06:10.919><c> going</c>

00:06:11.029 --> 00:06:11.039 align:start position:0%
have this in the dashboard we're going
 

00:06:11.039 --> 00:06:12.950 align:start position:0%
have this in the dashboard we're going
to<00:06:11.199><c> click</c>

00:06:12.950 --> 00:06:12.960 align:start position:0%
to click
 

00:06:12.960 --> 00:06:16.430 align:start position:0%
to click
it<00:06:13.960><c> and</c><00:06:14.560><c> I'm</c><00:06:14.680><c> going</c><00:06:14.800><c> to</c><00:06:15.080><c> show</c><00:06:15.440><c> the</c><00:06:15.680><c> editor</c><00:06:16.319><c> and</c>

00:06:16.430 --> 00:06:16.440 align:start position:0%
it and I'm going to show the editor and
 

00:06:16.440 --> 00:06:17.510 align:start position:0%
it and I'm going to show the editor and
I'm<00:06:16.520><c> going</c><00:06:16.639><c> to</c>

00:06:17.510 --> 00:06:17.520 align:start position:0%
I'm going to
 

00:06:17.520 --> 00:06:21.670 align:start position:0%
I'm going to
say<00:06:18.520><c> uh</c><00:06:18.960><c> visualize</c><00:06:19.960><c> okay</c><00:06:20.919><c> the</c><00:06:21.199><c> this</c><00:06:21.319><c> is</c><00:06:21.479><c> the</c>

00:06:21.670 --> 00:06:21.680 align:start position:0%
say uh visualize okay the this is the
 

00:06:21.680 --> 00:06:23.790 align:start position:0%
say uh visualize okay the this is the
conversion<00:06:22.280><c> data</c>

00:06:23.790 --> 00:06:23.800 align:start position:0%
conversion data
 

00:06:23.800 --> 00:06:26.430 align:start position:0%
conversion data
filter<00:06:24.800><c> filters</c><00:06:25.400><c> sub</c>

00:06:26.430 --> 00:06:26.440 align:start position:0%
filter filters sub
 

00:06:26.440 --> 00:06:29.309 align:start position:0%
filter filters sub
publisher<00:06:27.440><c> is</c><00:06:27.720><c> blank</c><00:06:28.319><c> nah</c><00:06:28.840><c> I</c><00:06:28.919><c> think</c><00:06:29.080><c> at</c><00:06:29.199><c> this</c>

00:06:29.309 --> 00:06:29.319 align:start position:0%
publisher is blank nah I think at this
 

00:06:29.319 --> 00:06:31.150 align:start position:0%
publisher is blank nah I think at this
point<00:06:29.599><c> we</c><00:06:29.680><c> need</c><00:06:29.759><c> to</c><00:06:29.919><c> actually</c><00:06:30.199><c> move</c><00:06:30.360><c> on</c><00:06:30.520><c> to</c><00:06:30.720><c> SQL</c>

00:06:31.150 --> 00:06:31.160 align:start position:0%
point we need to actually move on to SQL
 

00:06:31.160 --> 00:06:34.430 align:start position:0%
point we need to actually move on to SQL
so<00:06:31.319><c> let's</c><00:06:31.520><c> go</c><00:06:31.680><c> ahead</c><00:06:31.880><c> and</c><00:06:32.880><c> oh</c><00:06:33.039><c> wait</c>

00:06:34.430 --> 00:06:34.440 align:start position:0%
so let's go ahead and oh wait
 

00:06:34.440 --> 00:06:37.950 align:start position:0%
so let's go ahead and oh wait
summarize<00:06:35.440><c> some</c><00:06:35.880><c> of</c><00:06:36.520><c> okay</c><00:06:37.160><c> let's</c><00:06:37.360><c> go</c><00:06:37.639><c> into</c>

00:06:37.950 --> 00:06:37.960 align:start position:0%
summarize some of okay let's go into
 

00:06:37.960 --> 00:06:40.070 align:start position:0%
summarize some of okay let's go into
view<00:06:38.199><c> the</c><00:06:38.360><c> SQL</c><00:06:38.960><c> and</c><00:06:39.120><c> just</c><00:06:39.520><c> convert</c><00:06:39.919><c> this</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
view the SQL and just convert this
 

00:06:40.080 --> 00:06:43.070 align:start position:0%
view the SQL and just convert this
question<00:06:40.400><c> to</c><00:06:40.599><c> SQL</c><00:06:41.080><c> all</c><00:06:41.240><c> right</c><00:06:42.080><c> Group</c><00:06:42.479><c> by</c><00:06:42.720><c> blank</c>

00:06:43.070 --> 00:06:43.080 align:start position:0%
question to SQL all right Group by blank
 

00:06:43.080 --> 00:06:46.589 align:start position:0%
question to SQL all right Group by blank
order<00:06:43.520><c> by</c><00:06:44.039><c> blank</c>

00:06:46.589 --> 00:06:46.599 align:start position:0%
order by blank
 

00:06:46.599 --> 00:06:51.909 align:start position:0%
order by blank
where<00:06:47.680><c> okay</c><00:06:48.680><c> that</c><00:06:48.880><c> conversion</c><00:06:49.400><c> data</c><00:06:49.919><c> as</c><00:06:50.400><c> sum</c>

00:06:51.909 --> 00:06:51.919 align:start position:0%
where okay that conversion data as sum
 

00:06:51.919 --> 00:06:57.070 align:start position:0%
where okay that conversion data as sum
okay<00:06:52.919><c> as</c>

00:06:57.070 --> 00:06:57.080 align:start position:0%
 
 

00:06:57.080 --> 00:07:00.390 align:start position:0%
 
analysis<00:06:58.080><c> okay</c><00:06:58.400><c> I'm</c><00:06:58.520><c> going</c><00:06:58.639><c> to</c><00:06:58.759><c> turn</c><00:06:59.120><c> this</c>

00:07:00.390 --> 00:07:00.400 align:start position:0%
analysis okay I'm going to turn this
 

00:07:00.400 --> 00:07:03.230 align:start position:0%
analysis okay I'm going to turn this
select

00:07:03.230 --> 00:07:03.240 align:start position:0%
select
 

00:07:03.240 --> 00:07:05.270 align:start position:0%
select
all

00:07:05.270 --> 00:07:05.280 align:start position:0%
all
 

00:07:05.280 --> 00:07:08.670 align:start position:0%
all
from<00:07:06.280><c> from</c><00:07:06.520><c> the</c><00:07:06.759><c> analysis</c><00:07:07.599><c> table</c><00:07:08.240><c> this</c><00:07:08.360><c> is</c><00:07:08.479><c> a</c>

00:07:08.670 --> 00:07:08.680 align:start position:0%
from from the analysis table this is a
 

00:07:08.680 --> 00:07:13.869 align:start position:0%
from from the analysis table this is a
nested<00:07:09.160><c> query</c><00:07:09.520><c> it's</c><00:07:09.639><c> called</c><00:07:09.840><c> in</c><00:07:10.319><c> SQL</c>

00:07:13.869 --> 00:07:13.879 align:start position:0%
 
 

00:07:13.879 --> 00:07:17.390 align:start position:0%
 
where<00:07:14.879><c> um</c><00:07:15.720><c> where</c><00:07:16.280><c> What's</c><00:07:16.560><c> this</c><00:07:16.800><c> called</c><00:07:17.120><c> where</c>

00:07:17.390 --> 00:07:17.400 align:start position:0%
where um where What's this called where
 

00:07:17.400 --> 00:07:20.909 align:start position:0%
where um where What's this called where
sum<00:07:18.120><c> is</c><00:07:18.440><c> more</c>

00:07:20.909 --> 00:07:20.919 align:start position:0%
 
 

00:07:20.919 --> 00:07:25.110 align:start position:0%
 
sum<00:07:21.919><c> is</c><00:07:22.360><c> greater</c><00:07:22.960><c> than</c><00:07:23.639><c> zero</c><00:07:24.479><c> okay</c><00:07:24.680><c> let's</c><00:07:24.919><c> run</c>

00:07:25.110 --> 00:07:25.120 align:start position:0%
sum is greater than zero okay let's run
 

00:07:25.120 --> 00:07:26.869 align:start position:0%
sum is greater than zero okay let's run
it

00:07:26.869 --> 00:07:26.879 align:start position:0%
it
 

00:07:26.879 --> 00:07:29.790 align:start position:0%
it
again<00:07:27.879><c> okay</c><00:07:28.160><c> much</c><00:07:28.440><c> cleaner</c><00:07:28.960><c> now</c><00:07:29.199><c> right</c><00:07:29.599><c> guys</c>

00:07:29.790 --> 00:07:29.800 align:start position:0%
again okay much cleaner now right guys
 

00:07:29.800 --> 00:07:31.869 align:start position:0%
again okay much cleaner now right guys
so<00:07:30.000><c> let's</c><00:07:30.280><c> let's</c><00:07:30.479><c> go</c><00:07:30.639><c> ahead</c><00:07:30.960><c> and</c><00:07:31.199><c> save</c><00:07:31.520><c> that</c><00:07:31.759><c> if</c>

00:07:31.869 --> 00:07:31.879 align:start position:0%
so let's let's go ahead and save that if
 

00:07:31.879 --> 00:07:33.909 align:start position:0%
so let's let's go ahead and save that if
you<00:07:32.039><c> need</c><00:07:32.759><c> uh</c><00:07:32.919><c> you</c><00:07:33.000><c> need</c><00:07:33.160><c> to</c><00:07:33.280><c> brush</c><00:07:33.520><c> up</c><00:07:33.639><c> on</c><00:07:33.800><c> your</c>

00:07:33.909 --> 00:07:33.919 align:start position:0%
you need uh you need to brush up on your
 

00:07:33.919 --> 00:07:36.390 align:start position:0%
you need uh you need to brush up on your
SQL<00:07:34.360><c> skills</c><00:07:35.120><c> feel</c><00:07:35.360><c> free</c><00:07:35.599><c> to</c><00:07:35.759><c> brush</c><00:07:36.000><c> up</c><00:07:36.160><c> on</c><00:07:36.280><c> your</c>

00:07:36.390 --> 00:07:36.400 align:start position:0%
SQL skills feel free to brush up on your
 

00:07:36.400 --> 00:07:38.390 align:start position:0%
SQL skills feel free to brush up on your
SQL<00:07:36.800><c> skills</c><00:07:37.160><c> but</c><00:07:37.400><c> the</c><00:07:37.520><c> main</c><00:07:37.720><c> thing</c><00:07:37.840><c> I</c><00:07:37.960><c> did</c><00:07:38.160><c> over</c>

00:07:38.390 --> 00:07:38.400 align:start position:0%
SQL skills but the main thing I did over
 

00:07:38.400 --> 00:07:41.390 align:start position:0%
SQL skills but the main thing I did over
here<00:07:38.560><c> is</c><00:07:39.240><c> I</c><00:07:39.479><c> removed</c><00:07:39.960><c> all</c><00:07:40.160><c> the</c><00:07:40.319><c> sub</c><00:07:40.680><c> Publishers</c>

00:07:41.390 --> 00:07:41.400 align:start position:0%
here is I removed all the sub Publishers
 

00:07:41.400 --> 00:07:44.029 align:start position:0%
here is I removed all the sub Publishers
where<00:07:41.599><c> the</c><00:07:41.759><c> sum</c><00:07:42.479><c> was</c><00:07:42.800><c> Zero</c><00:07:43.400><c> all</c><00:07:43.520><c> the</c><00:07:43.639><c> sub</c>

00:07:44.029 --> 00:07:44.039 align:start position:0%
where the sum was Zero all the sub
 

00:07:44.039 --> 00:07:47.469 align:start position:0%
where the sum was Zero all the sub
Publishers<00:07:45.039><c> that</c><00:07:45.159><c> were</c><00:07:45.360><c> a</c><00:07:45.599><c> total</c><00:07:46.400><c> that</c><00:07:46.680><c> that</c>

00:07:47.469 --> 00:07:47.479 align:start position:0%
Publishers that were a total that that
 

00:07:47.479 --> 00:07:50.510 align:start position:0%
Publishers that were a total that that
brought<00:07:47.960><c> zero</c><00:07:48.479><c> deposits</c><00:07:49.159><c> are</c><00:07:49.479><c> now</c><00:07:49.960><c> not</c><00:07:50.159><c> in</c><00:07:50.319><c> the</c>

00:07:50.510 --> 00:07:50.520 align:start position:0%
brought zero deposits are now not in the
 

00:07:50.520 --> 00:07:52.350 align:start position:0%
brought zero deposits are now not in the
table<00:07:50.879><c> and</c><00:07:51.000><c> now</c><00:07:51.159><c> I</c><00:07:51.280><c> can</c><00:07:51.400><c> view</c><00:07:51.680><c> the</c><00:07:51.800><c> table</c><00:07:52.120><c> much</c>

00:07:52.350 --> 00:07:52.360 align:start position:0%
table and now I can view the table much
 

00:07:52.360 --> 00:07:55.270 align:start position:0%
table and now I can view the table much
cleanly<00:07:53.080><c> you</c><00:07:53.199><c> know</c><00:07:53.479><c> users</c><00:07:54.440><c> decision</c><00:07:54.879><c> makers</c>

00:07:55.270 --> 00:07:55.280 align:start position:0%
cleanly you know users decision makers
 

00:07:55.280 --> 00:07:57.830 align:start position:0%
cleanly you know users decision makers
can<00:07:55.440><c> hover</c><00:07:55.759><c> over</c><00:07:56.000><c> the</c><00:07:56.479><c> dashboard</c><00:07:57.479><c> a</c><00:07:57.639><c> little</c>

00:07:57.830 --> 00:07:57.840 align:start position:0%
can hover over the dashboard a little
 

00:07:57.840 --> 00:08:00.550 align:start position:0%
can hover over the dashboard a little
bit<00:07:58.440><c> a</c><00:07:58.560><c> little</c><00:07:58.720><c> bit</c><00:07:59.159><c> uh</c><00:07:59.520><c> more</c><00:07:59.720><c> easily</c><00:08:00.280><c> okay</c><00:08:00.400><c> so</c>

00:08:00.550 --> 00:08:00.560 align:start position:0%
bit a little bit uh more easily okay so
 

00:08:00.560 --> 00:08:04.430 align:start position:0%
bit a little bit uh more easily okay so
I<00:08:00.720><c> press</c><00:08:01.000><c> save</c><00:08:01.319><c> the</c><00:08:01.639><c> query</c><00:08:02.639><c> and</c><00:08:02.840><c> I</c><00:08:03.039><c> save</c><00:08:03.360><c> it</c><00:08:04.039><c> and</c>

00:08:04.430 --> 00:08:04.440 align:start position:0%
I press save the query and I save it and
 

00:08:04.440 --> 00:08:06.230 align:start position:0%
I press save the query and I save it and
I<00:08:04.520><c> can</c><00:08:04.680><c> always</c><00:08:04.919><c> revert</c><00:08:05.400><c> back</c><00:08:05.520><c> to</c><00:08:05.680><c> the</c><00:08:05.840><c> previous</c>

00:08:06.230 --> 00:08:06.240 align:start position:0%
I can always revert back to the previous
 

00:08:06.240 --> 00:08:07.950 align:start position:0%
I can always revert back to the previous
version<00:08:06.560><c> if</c><00:08:06.720><c> I</c><00:08:06.840><c> want</c><00:08:07.240><c> but</c><00:08:07.560><c> everything</c><00:08:07.800><c> is</c>

00:08:07.950 --> 00:08:07.960 align:start position:0%
version if I want but everything is
 

00:08:07.960 --> 00:08:10.189 align:start position:0%
version if I want but everything is
looking<00:08:08.280><c> really</c><00:08:08.599><c> good</c><00:08:09.280><c> that's</c><00:08:09.520><c> the</c><00:08:09.720><c> answer</c><00:08:10.000><c> to</c>

00:08:10.189 --> 00:08:10.199 align:start position:0%
looking really good that's the answer to
 

00:08:10.199 --> 00:08:12.390 align:start position:0%
looking really good that's the answer to
number<00:08:10.440><c> one</c><00:08:10.759><c> we've</c><00:08:11.000><c> gotten</c><00:08:11.280><c> the</c><00:08:11.560><c> data</c><00:08:12.000><c> into</c>

00:08:12.390 --> 00:08:12.400 align:start position:0%
number one we've gotten the data into
 

00:08:12.400 --> 00:08:16.029 align:start position:0%
number one we've gotten the data into
metabase<00:08:13.039><c> we've</c><00:08:13.319><c> created</c><00:08:14.159><c> our</c><00:08:14.720><c> first</c>

00:08:16.029 --> 00:08:16.039 align:start position:0%
metabase we've created our first
 

00:08:16.039 --> 00:08:19.510 align:start position:0%
metabase we've created our first
dashboard<00:08:17.039><c> uh</c><00:08:17.159><c> so</c><00:08:17.360><c> that's</c><00:08:17.599><c> really</c><00:08:18.280><c> awesome</c><00:08:19.280><c> uh</c>

00:08:19.510 --> 00:08:19.520 align:start position:0%
dashboard uh so that's really awesome uh
 

00:08:19.520 --> 00:08:21.390 align:start position:0%
dashboard uh so that's really awesome uh
and<00:08:19.840><c> if</c><00:08:19.919><c> we</c><00:08:20.080><c> go</c><00:08:20.240><c> into</c><00:08:20.440><c> our</c><00:08:20.639><c> collection</c><00:08:21.080><c> we</c><00:08:21.199><c> see</c>

00:08:21.390 --> 00:08:21.400 align:start position:0%
and if we go into our collection we see
 

00:08:21.400 --> 00:08:23.149 align:start position:0%
and if we go into our collection we see
we<00:08:21.560><c> have</c><00:08:21.680><c> a</c><00:08:21.879><c> question</c><00:08:22.360><c> and</c><00:08:22.479><c> we</c><00:08:22.599><c> have</c><00:08:22.720><c> a</c>

00:08:23.149 --> 00:08:23.159 align:start position:0%
we have a question and we have a
 

00:08:23.159 --> 00:08:25.629 align:start position:0%
we have a question and we have a
dashboard<00:08:24.159><c> and</c><00:08:24.360><c> this</c><00:08:24.599><c> specific</c><00:08:25.120><c> dashboard</c>

00:08:25.629 --> 00:08:25.639 align:start position:0%
dashboard and this specific dashboard
 

00:08:25.639 --> 00:08:28.350 align:start position:0%
dashboard and this specific dashboard
contains<00:08:26.120><c> the</c><00:08:26.639><c> question</c><00:08:27.639><c> uh</c><00:08:27.720><c> we</c><00:08:27.840><c> can</c><00:08:28.000><c> go</c><00:08:28.159><c> ahead</c>

00:08:28.350 --> 00:08:28.360 align:start position:0%
contains the question uh we can go ahead
 

00:08:28.360 --> 00:08:30.149 align:start position:0%
contains the question uh we can go ahead
and<00:08:28.560><c> add</c><00:08:28.759><c> more</c><00:08:29.000><c> fancy</c><00:08:29.599><c> stuff</c><00:08:29.879><c> which</c><00:08:30.000><c> we're</c>

00:08:30.149 --> 00:08:30.159 align:start position:0%
and add more fancy stuff which we're
 

00:08:30.159 --> 00:08:32.389 align:start position:0%
and add more fancy stuff which we're
going<00:08:30.240><c> to</c><00:08:30.360><c> do</c><00:08:30.479><c> in</c><00:08:30.599><c> the</c><00:08:30.680><c> later</c><00:08:31.080><c> videos</c><00:08:31.800><c> as</c><00:08:32.039><c> we</c>

00:08:32.389 --> 00:08:32.399 align:start position:0%
going to do in the later videos as we
 

00:08:32.399 --> 00:08:34.870 align:start position:0%
going to do in the later videos as we
continue<00:08:33.080><c> to</c><00:08:33.440><c> work</c><00:08:33.760><c> through</c><00:08:34.039><c> the</c><00:08:34.240><c> assignment</c>

00:08:34.870 --> 00:08:34.880 align:start position:0%
continue to work through the assignment
 

00:08:34.880 --> 00:08:37.149 align:start position:0%
continue to work through the assignment
thanks<00:08:35.200><c> guys</c><00:08:35.320><c> for</c><00:08:35.519><c> tuning</c><00:08:35.839><c> in</c><00:08:36.560><c> if</c><00:08:36.680><c> you</c><00:08:36.959><c> have</c>

00:08:37.149 --> 00:08:37.159 align:start position:0%
thanks guys for tuning in if you have
 

00:08:37.159 --> 00:08:40.029 align:start position:0%
thanks guys for tuning in if you have
any<00:08:37.479><c> questions</c><00:08:37.919><c> along</c><00:08:38.279><c> the</c><00:08:38.880><c> um</c><00:08:39.080><c> up</c><00:08:39.240><c> to</c><00:08:39.479><c> this</c>

00:08:40.029 --> 00:08:40.039 align:start position:0%
any questions along the um up to this
 

00:08:40.039 --> 00:08:42.709 align:start position:0%
any questions along the um up to this
point<00:08:40.519><c> of</c><00:08:40.800><c> the</c><00:08:41.440><c> of</c><00:08:41.560><c> the</c><00:08:41.719><c> tutorial</c><00:08:42.240><c> feel</c><00:08:42.479><c> free</c>

00:08:42.709 --> 00:08:42.719 align:start position:0%
point of the of the tutorial feel free
 

00:08:42.719 --> 00:08:44.949 align:start position:0%
point of the of the tutorial feel free
to<00:08:42.839><c> reach</c><00:08:43.080><c> out</c><00:08:43.760><c> more</c><00:08:43.959><c> than</c><00:08:44.200><c> happy</c><00:08:44.560><c> maybe</c><00:08:44.800><c> the</c>

00:08:44.949 --> 00:08:44.959 align:start position:0%
to reach out more than happy maybe the
 

00:08:44.959 --> 00:08:47.230 align:start position:0%
to reach out more than happy maybe the
community<00:08:45.480><c> can</c><00:08:45.680><c> help</c><00:08:45.880><c> you</c><00:08:46.040><c> out</c><00:08:46.360><c> maybe</c><00:08:46.680><c> I'll</c>

00:08:47.230 --> 00:08:47.240 align:start position:0%
community can help you out maybe I'll
 

00:08:47.240 --> 00:08:50.430 align:start position:0%
community can help you out maybe I'll
maybe<00:08:47.480><c> I'll</c><00:08:47.720><c> be</c><00:08:47.839><c> able</c><00:08:48.040><c> to</c><00:08:48.200><c> help</c><00:08:48.399><c> you</c><00:08:48.880><c> out</c><00:08:49.880><c> and</c>

00:08:50.430 --> 00:08:50.440 align:start position:0%
maybe I'll be able to help you out and
 

00:08:50.440 --> 00:08:52.949 align:start position:0%
maybe I'll be able to help you out and
uh<00:08:50.839><c> looking</c><00:08:51.120><c> forward</c><00:08:51.519><c> to</c><00:08:51.880><c> digging</c><00:08:52.240><c> in</c><00:08:52.680><c> in</c><00:08:52.800><c> the</c>

00:08:52.949 --> 00:08:52.959 align:start position:0%
uh looking forward to digging in in the
 

00:08:52.959 --> 00:08:54.509 align:start position:0%
uh looking forward to digging in in the
next<00:08:53.160><c> videos</c><00:08:53.519><c> we're</c><00:08:53.640><c> going</c><00:08:53.760><c> to</c><00:08:53.880><c> go</c><00:08:54.000><c> ahead</c><00:08:54.200><c> and</c>

00:08:54.509 --> 00:08:54.519 align:start position:0%
next videos we're going to go ahead and
 

00:08:54.519 --> 00:08:56.190 align:start position:0%
next videos we're going to go ahead and
answer<00:08:54.839><c> try</c><00:08:55.000><c> to</c><00:08:55.200><c> answer</c><00:08:55.560><c> the</c><00:08:55.680><c> rest</c><00:08:55.839><c> of</c><00:08:55.959><c> the</c>

00:08:56.190 --> 00:08:56.200 align:start position:0%
answer try to answer the rest of the
 

00:08:56.200 --> 00:08:58.110 align:start position:0%
answer try to answer the rest of the
questions<00:08:57.160><c> and</c><00:08:57.279><c> we're</c><00:08:57.440><c> going</c><00:08:57.519><c> to</c><00:08:57.640><c> try</c><00:08:57.800><c> to</c><00:08:57.959><c> put</c>

00:08:58.110 --> 00:08:58.120 align:start position:0%
questions and we're going to try to put
 

00:08:58.120 --> 00:09:00.230 align:start position:0%
questions and we're going to try to put
the<00:08:58.279><c> answers</c><00:08:58.720><c> straight</c><00:08:59.079><c> into</c><00:08:59.440><c> to</c><00:08:59.920><c> our</c>

00:09:00.230 --> 00:09:00.240 align:start position:0%
the answers straight into to our
 

00:09:00.240 --> 00:09:02.389 align:start position:0%
the answers straight into to our
metabase<00:09:00.920><c> dashboard</c><00:09:01.680><c> all</c><00:09:01.800><c> right</c><00:09:02.040><c> guys</c><00:09:02.160><c> so</c>

00:09:02.389 --> 00:09:02.399 align:start position:0%
metabase dashboard all right guys so
 

00:09:02.399 --> 00:09:04.509 align:start position:0%
metabase dashboard all right guys so
plenty<00:09:02.640><c> of</c><00:09:02.839><c> questions</c><00:09:03.560><c> plenty</c><00:09:03.839><c> more</c><00:09:04.040><c> analysis</c>

00:09:04.509 --> 00:09:04.519 align:start position:0%
plenty of questions plenty more analysis
 

00:09:04.519 --> 00:09:06.670 align:start position:0%
plenty of questions plenty more analysis
to<00:09:04.760><c> do</c><00:09:05.279><c> now</c><00:09:05.440><c> that</c><00:09:05.560><c> we</c><00:09:05.680><c> have</c><00:09:05.839><c> the</c><00:09:06.000><c> data</c><00:09:06.320><c> in</c><00:09:06.560><c> in</c>

00:09:06.670 --> 00:09:06.680 align:start position:0%
to do now that we have the data in in
 

00:09:06.680 --> 00:09:09.670 align:start position:0%
to do now that we have the data in in
metabase<00:09:07.240><c> it's</c><00:09:07.480><c> going</c><00:09:07.560><c> to</c><00:09:07.720><c> be</c><00:09:08.560><c> uh</c><00:09:08.720><c> much</c><00:09:08.959><c> easier</c>

00:09:09.670 --> 00:09:09.680 align:start position:0%
metabase it's going to be uh much easier
 

00:09:09.680 --> 00:09:12.750 align:start position:0%
metabase it's going to be uh much easier
to<00:09:10.160><c> uh</c><00:09:10.279><c> to</c><00:09:10.480><c> really</c><00:09:10.839><c> visualize</c><00:09:11.519><c> the</c><00:09:11.760><c> answers</c>

00:09:12.750 --> 00:09:12.760 align:start position:0%
to uh to really visualize the answers
 

00:09:12.760 --> 00:09:15.190 align:start position:0%
to uh to really visualize the answers
and<00:09:13.320><c> uh</c><00:09:13.880><c> um</c><00:09:14.160><c> feel</c><00:09:14.399><c> free</c><00:09:14.560><c> to</c><00:09:14.680><c> reach</c><00:09:14.839><c> out</c><00:09:15.000><c> if</c><00:09:15.120><c> you</c>

00:09:15.190 --> 00:09:15.200 align:start position:0%
and uh um feel free to reach out if you
 

00:09:15.200 --> 00:09:16.630 align:start position:0%
and uh um feel free to reach out if you
have<00:09:15.399><c> questions</c><00:09:15.720><c> and</c><00:09:15.880><c> looking</c><00:09:16.120><c> forward</c><00:09:16.480><c> to</c>

00:09:16.630 --> 00:09:16.640 align:start position:0%
have questions and looking forward to
 

00:09:16.640 --> 00:09:21.320 align:start position:0%
have questions and looking forward to
seeing<00:09:16.959><c> you</c><00:09:17.320><c> in</c><00:09:17.680><c> the</c><00:09:17.880><c> next</c><00:09:18.320><c> videos</c>

