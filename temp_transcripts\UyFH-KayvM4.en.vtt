WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:01.510 align:start position:0%
 
hey<00:00:00.359><c> coders</c><00:00:00.719><c> and</c><00:00:00.880><c> welcome</c><00:00:01.120><c> to</c><00:00:01.280><c> another</c>

00:00:01.510 --> 00:00:01.520 align:start position:0%
hey coders and welcome to another
 

00:00:01.520 --> 00:00:03.230 align:start position:0%
hey coders and welcome to another
sorting<00:00:01.880><c> algorithm</c><00:00:02.440><c> and</c><00:00:02.600><c> today</c><00:00:02.879><c> we're</c><00:00:03.040><c> going</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
sorting algorithm and today we're going
 

00:00:03.240 --> 00:00:05.030 align:start position:0%
sorting algorithm and today we're going
over<00:00:03.520><c> radic</c><00:00:03.919><c> sort</c><00:00:04.440><c> now</c><00:00:04.600><c> the</c><00:00:04.720><c> difference</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
over radic sort now the difference
 

00:00:05.040 --> 00:00:06.789 align:start position:0%
over radic sort now the difference
between<00:00:05.359><c> this</c><00:00:05.600><c> and</c><00:00:05.799><c> other</c><00:00:06.040><c> algorithms</c><00:00:06.520><c> you've</c>

00:00:06.789 --> 00:00:06.799 align:start position:0%
between this and other algorithms you've
 

00:00:06.799 --> 00:00:08.990 align:start position:0%
between this and other algorithms you've
probably<00:00:07.520><c> been</c><00:00:07.720><c> through</c><00:00:08.000><c> so</c><00:00:08.240><c> far</c><00:00:08.639><c> is</c><00:00:08.760><c> that</c>

00:00:08.990 --> 00:00:09.000 align:start position:0%
probably been through so far is that
 

00:00:09.000 --> 00:00:11.270 align:start position:0%
probably been through so far is that
this<00:00:09.200><c> compares</c><00:00:09.639><c> the</c><00:00:09.960><c> digits</c><00:00:10.480><c> of</c><00:00:10.639><c> the</c><00:00:10.880><c> values</c>

00:00:11.270 --> 00:00:11.280 align:start position:0%
this compares the digits of the values
 

00:00:11.280 --> 00:00:13.669 align:start position:0%
this compares the digits of the values
in<00:00:11.400><c> the</c><00:00:11.519><c> array</c><00:00:12.320><c> okay</c><00:00:12.519><c> not</c><00:00:12.719><c> the</c><00:00:12.880><c> whole</c><00:00:13.280><c> integer</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
in the array okay not the whole integer
 

00:00:13.679 --> 00:00:15.869 align:start position:0%
in the array okay not the whole integer
value<00:00:13.960><c> if</c><00:00:14.040><c> you're</c><00:00:14.160><c> sorting</c><00:00:14.480><c> by</c><00:00:14.599><c> integers</c><00:00:15.280><c> or</c>

00:00:15.869 --> 00:00:15.879 align:start position:0%
value if you're sorting by integers or
 

00:00:15.879 --> 00:00:17.029 align:start position:0%
value if you're sorting by integers or
not<00:00:16.039><c> the</c><00:00:16.160><c> whole</c><00:00:16.279><c> string</c><00:00:16.560><c> if</c><00:00:16.640><c> you're</c><00:00:16.720><c> doing</c>

00:00:17.029 --> 00:00:17.039 align:start position:0%
not the whole string if you're doing
 

00:00:17.039 --> 00:00:19.710 align:start position:0%
not the whole string if you're doing
strings<00:00:17.680><c> it's</c><00:00:17.840><c> the</c><00:00:18.160><c> digits</c><00:00:18.920><c> or</c><00:00:19.119><c> characters</c><00:00:19.560><c> of</c>

00:00:19.710 --> 00:00:19.720 align:start position:0%
strings it's the digits or characters of
 

00:00:19.720 --> 00:00:21.269 align:start position:0%
strings it's the digits or characters of
each<00:00:19.880><c> of</c><00:00:20.000><c> those</c><00:00:20.199><c> values</c><00:00:20.480><c> in</c><00:00:20.600><c> the</c><00:00:20.680><c> array</c><00:00:21.119><c> you're</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
each of those values in the array you're
 

00:00:21.279 --> 00:00:22.870 align:start position:0%
each of those values in the array you're
going<00:00:21.359><c> to</c><00:00:21.439><c> need</c><00:00:21.600><c> to</c><00:00:21.680><c> know</c><00:00:21.880><c> what</c><00:00:22.039><c> the</c><00:00:22.199><c> LSD</c><00:00:22.720><c> and</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
going to need to know what the LSD and
 

00:00:22.880 --> 00:00:24.990 align:start position:0%
going to need to know what the LSD and
MSD<00:00:23.359><c> are</c><00:00:23.519><c> so</c><00:00:23.680><c> LSD</c><00:00:24.160><c> is</c><00:00:24.279><c> the</c><00:00:24.400><c> least</c><00:00:24.599><c> significant</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
MSD are so LSD is the least significant
 

00:00:25.000 --> 00:00:27.269 align:start position:0%
MSD are so LSD is the least significant
digit<00:00:25.359><c> and</c><00:00:25.560><c> MSD</c><00:00:26.039><c> is</c><00:00:26.240><c> the</c><00:00:26.480><c> most</c><00:00:26.800><c> significant</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
digit and MSD is the most significant
 

00:00:27.279 --> 00:00:29.669 align:start position:0%
digit and MSD is the most significant
digit<00:00:27.679><c> so</c><00:00:27.800><c> if</c><00:00:27.880><c> we</c><00:00:28.000><c> have</c><00:00:28.080><c> the</c><00:00:28.199><c> number</c><00:00:28.439><c> 389</c><00:00:29.320><c> 9</c><00:00:29.599><c> is</c>

00:00:29.669 --> 00:00:29.679 align:start position:0%
digit so if we have the number 389 9 is
 

00:00:29.679 --> 00:00:31.269 align:start position:0%
digit so if we have the number 389 9 is
the<00:00:29.759><c> least</c><00:00:30.119><c> significant</c><00:00:30.480><c> digit</c><00:00:30.759><c> where</c><00:00:31.000><c> three</c>

00:00:31.269 --> 00:00:31.279 align:start position:0%
the least significant digit where three
 

00:00:31.279 --> 00:00:33.750 align:start position:0%
the least significant digit where three
is<00:00:31.439><c> the</c><00:00:31.719><c> most</c><00:00:32.719><c> the</c><00:00:32.880><c> steps</c><00:00:33.360><c> that</c><00:00:33.480><c> are</c><00:00:33.600><c> going</c><00:00:33.680><c> to</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
is the most the steps that are going to
 

00:00:33.760 --> 00:00:35.110 align:start position:0%
is the most the steps that are going to
be<00:00:33.879><c> involved</c><00:00:34.239><c> with</c><00:00:34.399><c> this</c><00:00:34.680><c> are</c><00:00:34.800><c> we're</c><00:00:34.920><c> going</c><00:00:35.000><c> to</c>

00:00:35.110 --> 00:00:35.120 align:start position:0%
be involved with this are we're going to
 

00:00:35.120 --> 00:00:36.430 align:start position:0%
be involved with this are we're going to
first<00:00:35.320><c> find</c><00:00:35.480><c> the</c><00:00:35.640><c> maximum</c><00:00:36.040><c> value</c><00:00:36.239><c> in</c><00:00:36.320><c> the</c>

00:00:36.430 --> 00:00:36.440 align:start position:0%
first find the maximum value in the
 

00:00:36.440 --> 00:00:38.229 align:start position:0%
first find the maximum value in the
array<00:00:37.040><c> then</c><00:00:37.160><c> we're</c><00:00:37.280><c> going</c><00:00:37.360><c> to</c><00:00:37.559><c> iterate</c><00:00:38.040><c> over</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
array then we're going to iterate over
 

00:00:38.239 --> 00:00:40.549 align:start position:0%
array then we're going to iterate over
that<00:00:38.399><c> array</c><00:00:38.960><c> number</c><00:00:39.320><c> of</c><00:00:39.559><c> digits</c><00:00:40.000><c> in</c><00:00:40.280><c> that</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
that array number of digits in that
 

00:00:40.559 --> 00:00:42.910 align:start position:0%
that array number of digits in that
value<00:00:41.039><c> so</c><00:00:41.239><c> if</c><00:00:41.360><c> the</c><00:00:41.480><c> value</c><00:00:41.719><c> is</c><00:00:41.879><c> 389</c><00:00:42.640><c> we're</c><00:00:42.800><c> going</c>

00:00:42.910 --> 00:00:42.920 align:start position:0%
value so if the value is 389 we're going
 

00:00:42.920 --> 00:00:45.069 align:start position:0%
value so if the value is 389 we're going
to<00:00:43.039><c> iterate</c><00:00:43.360><c> over</c><00:00:43.520><c> the</c><00:00:43.640><c> array</c><00:00:44.039><c> three</c><00:00:44.360><c> times</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
to iterate over the array three times
 

00:00:45.079 --> 00:00:46.590 align:start position:0%
to iterate over the array three times
then<00:00:45.239><c> for</c><00:00:45.440><c> each</c><00:00:45.640><c> iteration</c><00:00:46.199><c> we're</c><00:00:46.360><c> going</c><00:00:46.440><c> to</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
then for each iteration we're going to
 

00:00:46.600 --> 00:00:49.029 align:start position:0%
then for each iteration we're going to
sort<00:00:47.280><c> all</c><00:00:47.440><c> the</c><00:00:47.559><c> integers</c><00:00:47.960><c> in</c><00:00:48.079><c> the</c><00:00:48.160><c> array</c><00:00:48.800><c> based</c>

00:00:49.029 --> 00:00:49.039 align:start position:0%
sort all the integers in the array based
 

00:00:49.039 --> 00:00:51.069 align:start position:0%
sort all the integers in the array based
on<00:00:49.160><c> the</c><00:00:49.440><c> digits</c><00:00:49.920><c> starting</c><00:00:50.399><c> with</c><00:00:50.680><c> the</c><00:00:50.800><c> least</c>

00:00:51.069 --> 00:00:51.079 align:start position:0%
on the digits starting with the least
 

00:00:51.079 --> 00:00:52.869 align:start position:0%
on the digits starting with the least
significant<00:00:51.600><c> digit</c><00:00:51.960><c> and</c><00:00:52.079><c> then</c><00:00:52.320><c> each</c><00:00:52.559><c> time</c><00:00:52.800><c> the</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
significant digit and then each time the
 

00:00:52.879 --> 00:00:54.590 align:start position:0%
significant digit and then each time the
loop<00:00:53.120><c> jumps</c><00:00:53.359><c> to</c><00:00:53.480><c> the</c><00:00:53.600><c> next</c><00:00:53.800><c> digit</c><00:00:54.160><c> and</c><00:00:54.320><c> then</c><00:00:54.480><c> by</c>

00:00:54.590 --> 00:00:54.600 align:start position:0%
loop jumps to the next digit and then by
 

00:00:54.600 --> 00:00:56.189 align:start position:0%
loop jumps to the next digit and then by
the<00:00:54.719><c> end</c><00:00:54.960><c> the</c><00:00:55.079><c> array</c><00:00:55.359><c> is</c><00:00:55.440><c> sorted</c><00:00:55.960><c> here</c><00:00:56.079><c> we're</c>

00:00:56.189 --> 00:00:56.199 align:start position:0%
the end the array is sorted here we're
 

00:00:56.199 --> 00:00:57.990 align:start position:0%
the end the array is sorted here we're
going<00:00:56.280><c> to</c><00:00:56.359><c> have</c><00:00:56.440><c> an</c><00:00:56.559><c> array</c><00:00:56.879><c> of</c><00:00:57.039><c> 10</c><00:00:57.359><c> digits</c><00:00:57.840><c> and</c>

00:00:57.990 --> 00:00:58.000 align:start position:0%
going to have an array of 10 digits and
 

00:00:58.000 --> 00:00:59.509 align:start position:0%
going to have an array of 10 digits and
the<00:00:58.239><c> first</c><00:00:58.519><c> thing</c><00:00:59.000><c> that</c><00:00:59.120><c> we're</c><00:00:59.239><c> going</c><00:00:59.320><c> to</c><00:00:59.440><c> have</c>

00:00:59.509 --> 00:00:59.519 align:start position:0%
the first thing that we're going to have
 

00:00:59.519 --> 00:01:02.389 align:start position:0%
the first thing that we're going to have
to<00:00:59.640><c> do</c><00:01:00.039><c> is</c><00:01:00.239><c> find</c><00:01:00.600><c> the</c><00:01:00.920><c> maximum</c><00:01:01.559><c> value</c><00:01:01.960><c> in</c><00:01:02.239><c> the</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
to do is find the maximum value in the
 

00:01:02.399 --> 00:01:05.670 align:start position:0%
to do is find the maximum value in the
array<00:01:03.239><c> in</c><00:01:03.399><c> this</c><00:01:03.559><c> array</c><00:01:04.360><c> 741</c><00:01:05.040><c> is</c><00:01:05.159><c> the</c><00:01:05.280><c> maximum</c>

00:01:05.670 --> 00:01:05.680 align:start position:0%
array in this array 741 is the maximum
 

00:01:05.680 --> 00:01:07.149 align:start position:0%
array in this array 741 is the maximum
value<00:01:06.280><c> this</c><00:01:06.400><c> means</c><00:01:06.680><c> we're</c><00:01:06.799><c> going</c><00:01:06.880><c> to</c><00:01:06.960><c> have</c><00:01:07.040><c> to</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
value this means we're going to have to
 

00:01:07.159 --> 00:01:09.630 align:start position:0%
value this means we're going to have to
iterate<00:01:07.680><c> three</c><00:01:08.119><c> times</c><00:01:09.040><c> on</c><00:01:09.159><c> the</c><00:01:09.400><c> first</c>

00:01:09.630 --> 00:01:09.640 align:start position:0%
iterate three times on the first
 

00:01:09.640 --> 00:01:10.990 align:start position:0%
iterate three times on the first
iteration<00:01:10.200><c> we're</c><00:01:10.320><c> going</c><00:01:10.439><c> to</c><00:01:10.560><c> look</c><00:01:10.680><c> at</c><00:01:10.840><c> the</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
iteration we're going to look at the
 

00:01:11.000 --> 00:01:13.550 align:start position:0%
iteration we're going to look at the
one's<00:01:11.479><c> place</c><00:01:11.880><c> in</c><00:01:12.200><c> all</c><00:01:12.360><c> of</c><00:01:12.600><c> these</c><00:01:12.840><c> integers</c>

00:01:13.550 --> 00:01:13.560 align:start position:0%
one's place in all of these integers
 

00:01:13.560 --> 00:01:15.109 align:start position:0%
one's place in all of these integers
remember<00:01:13.840><c> we're</c><00:01:14.000><c> start</c><00:01:14.280><c> at</c><00:01:14.479><c> the</c><00:01:14.720><c> least</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
remember we're start at the least
 

00:01:15.119 --> 00:01:17.270 align:start position:0%
remember we're start at the least
significant<00:01:15.680><c> digit</c><00:01:16.360><c> so</c><00:01:16.680><c> here</c><00:01:17.000><c> it's</c><00:01:17.119><c> going</c><00:01:17.240><c> to</c>

00:01:17.270 --> 00:01:17.280 align:start position:0%
significant digit so here it's going to
 

00:01:17.280 --> 00:01:18.990 align:start position:0%
significant digit so here it's going to
be<00:01:17.400><c> the</c><00:01:17.479><c> ones</c><00:01:17.759><c> place</c><00:01:17.960><c> in</c><00:01:18.119><c> all</c><00:01:18.200><c> of</c><00:01:18.360><c> these</c><00:01:18.720><c> so</c><00:01:18.880><c> for</c>

00:01:18.990 --> 00:01:19.000 align:start position:0%
be the ones place in all of these so for
 

00:01:19.000 --> 00:01:20.749 align:start position:0%
be the ones place in all of these so for
instance<00:01:19.320><c> 492</c><00:01:20.119><c> we're</c><00:01:20.240><c> going</c><00:01:20.320><c> to</c><00:01:20.400><c> look</c><00:01:20.520><c> at</c><00:01:20.640><c> the</c>

00:01:20.749 --> 00:01:20.759 align:start position:0%
instance 492 we're going to look at the
 

00:01:20.759 --> 00:01:23.710 align:start position:0%
instance 492 we're going to look at the
2<00:01:21.200><c> 588</c><00:01:21.840><c> we're</c><00:01:21.960><c> going</c><00:01:22.079><c> to</c><00:01:22.159><c> look</c><00:01:22.280><c> at</c><00:01:22.400><c> the</c><00:01:22.479><c> 8</c><00:01:23.200><c> F5</c>

00:01:23.710 --> 00:01:23.720 align:start position:0%
2 588 we're going to look at the 8 F5
 

00:01:23.720 --> 00:01:25.469 align:start position:0%
2 588 we're going to look at the 8 F5
we're<00:01:23.880><c> just</c><00:01:23.960><c> looking</c><00:01:24.119><c> at</c><00:01:24.240><c> the</c><00:01:24.400><c> five</c><00:01:25.119><c> and</c>

00:01:25.469 --> 00:01:25.479 align:start position:0%
we're just looking at the five and
 

00:01:25.479 --> 00:01:27.030 align:start position:0%
we're just looking at the five and
remember<00:01:26.000><c> these</c><00:01:26.119><c> aren't</c><00:01:26.360><c> going</c><00:01:26.439><c> to</c><00:01:26.520><c> be</c><00:01:26.640><c> sorted</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
remember these aren't going to be sorted
 

00:01:27.040 --> 00:01:29.069 align:start position:0%
remember these aren't going to be sorted
in<00:01:27.240><c> order</c><00:01:27.680><c> this</c><00:01:27.840><c> first</c><00:01:28.119><c> time</c><00:01:28.360><c> through</c><00:01:28.880><c> don't</c>

00:01:29.069 --> 00:01:29.079 align:start position:0%
in order this first time through don't
 

00:01:29.079 --> 00:01:32.190 align:start position:0%
in order this first time through don't
be<00:01:29.240><c> surprised</c><00:01:29.920><c> whenever</c><00:01:30.200><c> you</c><00:01:30.320><c> see</c><00:01:30.720><c> 741</c><00:01:31.720><c> before</c>

00:01:32.190 --> 00:01:32.200 align:start position:0%
be surprised whenever you see 741 before
 

00:01:32.200 --> 00:01:34.789 align:start position:0%
be surprised whenever you see 741 before
492<00:01:33.200><c> because</c><00:01:33.520><c> the</c><00:01:33.720><c> one</c><00:01:34.360><c> which</c><00:01:34.479><c> is</c><00:01:34.600><c> in</c><00:01:34.720><c> the</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
492 because the one which is in the
 

00:01:34.799 --> 00:01:37.910 align:start position:0%
492 because the one which is in the
one's<00:01:35.159><c> place</c><00:01:35.640><c> is</c><00:01:35.880><c> less</c><00:01:36.119><c> than</c><00:01:36.320><c> the</c><00:01:36.439><c> two</c><00:01:36.759><c> in</c><00:01:36.920><c> 492</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
one's place is less than the two in 492
 

00:01:37.920 --> 00:01:39.950 align:start position:0%
one's place is less than the two in 492
which<00:01:38.040><c> is</c><00:01:38.200><c> also</c><00:01:38.399><c> in</c><00:01:38.479><c> the</c><00:01:38.600><c> ones</c><00:01:38.920><c> place</c><00:01:39.680><c> now</c><00:01:39.840><c> we</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
which is also in the ones place now we
 

00:01:39.960 --> 00:01:41.670 align:start position:0%
which is also in the ones place now we
go<00:01:40.079><c> ahead</c><00:01:40.280><c> and</c><00:01:40.360><c> sort</c><00:01:40.600><c> the</c><00:01:40.680><c> integers</c><00:01:41.119><c> based</c><00:01:41.399><c> on</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
go ahead and sort the integers based on
 

00:01:41.680 --> 00:01:43.469 align:start position:0%
go ahead and sort the integers based on
these<00:01:41.920><c> ones</c><00:01:42.479><c> place</c>

00:01:43.469 --> 00:01:43.479 align:start position:0%
these ones place
 

00:01:43.479 --> 00:01:46.830 align:start position:0%
these ones place
digits<00:01:44.479><c> as</c><00:01:44.560><c> you</c><00:01:44.640><c> can</c><00:01:44.759><c> see</c><00:01:44.920><c> we</c><00:01:45.040><c> have</c><00:01:45.159><c> 91</c><00:01:45.880><c> 741</c>

00:01:46.830 --> 00:01:46.840 align:start position:0%
digits as you can see we have 91 741
 

00:01:46.840 --> 00:01:49.870 align:start position:0%
digits as you can see we have 91 741
then<00:01:47.000><c> 492</c><00:01:47.680><c> and</c><00:01:47.840><c> 23</c><00:01:48.479><c> and</c><00:01:48.640><c> so</c><00:01:48.920><c> forth</c><00:01:49.600><c> that</c><00:01:49.719><c> was</c>

00:01:49.870 --> 00:01:49.880 align:start position:0%
then 492 and 23 and so forth that was
 

00:01:49.880 --> 00:01:51.429 align:start position:0%
then 492 and 23 and so forth that was
just<00:01:50.000><c> the</c><00:01:50.159><c> first</c><00:01:50.399><c> iteration</c><00:01:50.920><c> we</c><00:01:51.000><c> have</c><00:01:51.240><c> two</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
just the first iteration we have two
 

00:01:51.439 --> 00:01:53.709 align:start position:0%
just the first iteration we have two
more<00:01:52.159><c> the</c><00:01:52.360><c> next</c><00:01:52.600><c> iteration</c><00:01:53.320><c> we're</c><00:01:53.479><c> going</c><00:01:53.600><c> to</c>

00:01:53.709 --> 00:01:53.719 align:start position:0%
more the next iteration we're going to
 

00:01:53.719 --> 00:01:56.270 align:start position:0%
more the next iteration we're going to
look<00:01:53.960><c> at</c><00:01:54.079><c> the</c><00:01:54.280><c> 10</c><00:01:54.840><c> place</c><00:01:55.759><c> for</c><00:01:55.880><c> instance</c><00:01:56.200><c> this</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
look at the 10 place for instance this
 

00:01:56.280 --> 00:02:00.149 align:start position:0%
look at the 10 place for instance this
will<00:01:56.439><c> be</c><00:01:56.600><c> the</c><00:01:56.759><c> 4</c><00:01:57.079><c> and</c><00:01:57.320><c> 741</c><00:01:58.320><c> the</c><00:01:58.439><c> 3</c><00:01:58.719><c> and</c><00:01:58.880><c> 134</c><00:01:59.960><c> and</c>

00:02:00.149 --> 00:02:00.159 align:start position:0%
will be the 4 and 741 the 3 and 134 and
 

00:02:00.159 --> 00:02:03.910 align:start position:0%
will be the 4 and 741 the 3 and 134 and
the<00:02:00.280><c> 8</c><00:02:00.600><c> and</c><00:02:00.799><c> 588</c><00:02:01.600><c> well</c><00:02:01.960><c> the</c><00:02:02.079><c> second</c><00:02:02.360><c> 8</c><00:02:02.600><c> and</c><00:02:02.920><c> 588</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
the 8 and 588 well the second 8 and 588
 

00:02:03.920 --> 00:02:06.230 align:start position:0%
the 8 and 588 well the second 8 and 588
but<00:02:04.079><c> look</c><00:02:04.200><c> at</c><00:02:04.360><c> the</c><00:02:04.560><c> five</c><00:02:04.960><c> there</c><00:02:05.159><c> is</c><00:02:05.399><c> no</c><00:02:05.920><c> digit</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
but look at the five there is no digit
 

00:02:06.240 --> 00:02:08.630 align:start position:0%
but look at the five there is no digit
in<00:02:06.399><c> the</c><00:02:06.560><c> 10's</c><00:02:06.920><c> place</c><00:02:07.399><c> what</c><00:02:07.479><c> do</c><00:02:07.640><c> we</c><00:02:07.840><c> do</c><00:02:08.160><c> here</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
in the 10's place what do we do here
 

00:02:08.640 --> 00:02:11.029 align:start position:0%
in the 10's place what do we do here
well<00:02:08.800><c> if</c><00:02:08.879><c> a</c><00:02:09.039><c> number</c><00:02:09.360><c> doesn't</c><00:02:09.679><c> have</c><00:02:09.800><c> a</c><00:02:10.039><c> value</c><00:02:10.840><c> in</c>

00:02:11.029 --> 00:02:11.039 align:start position:0%
well if a number doesn't have a value in
 

00:02:11.039 --> 00:02:13.030 align:start position:0%
well if a number doesn't have a value in
the<00:02:11.560><c> digit</c><00:02:11.920><c> place</c><00:02:12.200><c> that</c><00:02:12.319><c> you're</c><00:02:12.520><c> looking</c><00:02:12.800><c> at</c>

00:02:13.030 --> 00:02:13.040 align:start position:0%
the digit place that you're looking at
 

00:02:13.040 --> 00:02:15.949 align:start position:0%
the digit place that you're looking at
in<00:02:13.200><c> this</c><00:02:13.400><c> iteration</c><00:02:14.280><c> it's</c><00:02:14.519><c> considered</c><00:02:15.160><c> zero</c>

00:02:15.949 --> 00:02:15.959 align:start position:0%
in this iteration it's considered zero
 

00:02:15.959 --> 00:02:17.430 align:start position:0%
in this iteration it's considered zero
whenever<00:02:16.239><c> we</c><00:02:16.360><c> sort</c><00:02:16.720><c> the</c><00:02:16.920><c> five</c><00:02:17.200><c> in</c><00:02:17.319><c> this</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
whenever we sort the five in this
 

00:02:17.440 --> 00:02:18.990 align:start position:0%
whenever we sort the five in this
iteration<00:02:18.120><c> we're</c><00:02:18.239><c> going</c><00:02:18.319><c> to</c><00:02:18.480><c> pretend</c><00:02:18.800><c> that</c>

00:02:18.990 --> 00:02:19.000 align:start position:0%
iteration we're going to pretend that
 

00:02:19.000 --> 00:02:21.030 align:start position:0%
iteration we're going to pretend that
that<00:02:19.239><c> digit</c><00:02:19.599><c> has</c><00:02:19.720><c> a</c><00:02:20.000><c> zero</c><00:02:20.560><c> the</c><00:02:20.680><c> next</c><00:02:20.800><c> thing</c><00:02:20.920><c> we</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
that digit has a zero the next thing we
 

00:02:21.040 --> 00:02:22.270 align:start position:0%
that digit has a zero the next thing we
do<00:02:21.160><c> is</c><00:02:21.280><c> go</c><00:02:21.360><c> ahead</c><00:02:21.519><c> and</c><00:02:21.599><c> sort</c><00:02:21.920><c> all</c><00:02:22.040><c> of</c><00:02:22.160><c> these</c>

00:02:22.270 --> 00:02:22.280 align:start position:0%
do is go ahead and sort all of these
 

00:02:22.280 --> 00:02:24.949 align:start position:0%
do is go ahead and sort all of these
integers<00:02:22.840><c> based</c><00:02:23.120><c> on</c><00:02:23.360><c> the</c><00:02:23.519><c> 10</c><00:02:23.879><c> digit</c><00:02:24.800><c> and</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
integers based on the 10 digit and
 

00:02:24.959 --> 00:02:26.790 align:start position:0%
integers based on the 10 digit and
whenever<00:02:25.280><c> we</c><00:02:25.480><c> did</c><00:02:25.760><c> that</c><00:02:26.200><c> the</c><00:02:26.400><c> five</c><00:02:26.640><c> is</c>

00:02:26.790 --> 00:02:26.800 align:start position:0%
whenever we did that the five is
 

00:02:26.800 --> 00:02:29.030 align:start position:0%
whenever we did that the five is
definitely<00:02:27.160><c> going</c><00:02:27.239><c> to</c><00:02:27.440><c> be</c><00:02:27.720><c> first</c><00:02:28.519><c> and</c><00:02:28.680><c> then</c><00:02:28.879><c> we</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
definitely going to be first and then we
 

00:02:29.040 --> 00:02:32.670 align:start position:0%
definitely going to be first and then we
have<00:02:29.200><c> the</c><00:02:29.319><c> two</c><00:02:29.560><c> and</c><00:02:29.840><c> 23</c><00:02:30.640><c> but</c><00:02:31.360><c> 629</c><00:02:32.239><c> is</c><00:02:32.400><c> still</c>

00:02:32.670 --> 00:02:32.680 align:start position:0%
have the two and 23 but 629 is still
 

00:02:32.680 --> 00:02:36.589 align:start position:0%
have the two and 23 but 629 is still
before<00:02:33.280><c> 134</c><00:02:34.280><c> because</c><00:02:34.560><c> the</c><00:02:34.800><c> two</c><00:02:35.800><c> the</c><00:02:35.920><c> 10</c><00:02:36.280><c> place</c>

00:02:36.589 --> 00:02:36.599 align:start position:0%
before 134 because the two the 10 place
 

00:02:36.599 --> 00:02:38.949 align:start position:0%
before 134 because the two the 10 place
was<00:02:36.800><c> less</c><00:02:37.080><c> than</c><00:02:37.200><c> the</c><00:02:37.360><c> three</c><00:02:37.680><c> and</c><00:02:37.879><c> 134</c><00:02:38.760><c> but</c><00:02:38.879><c> that</c>

00:02:38.949 --> 00:02:38.959 align:start position:0%
was less than the three and 134 but that
 

00:02:38.959 --> 00:02:40.470 align:start position:0%
was less than the three and 134 but that
was<00:02:39.080><c> just</c><00:02:39.200><c> the</c><00:02:39.319><c> second</c><00:02:39.560><c> iteration</c><00:02:40.120><c> we</c><00:02:40.239><c> have</c>

00:02:40.470 --> 00:02:40.480 align:start position:0%
was just the second iteration we have
 

00:02:40.480 --> 00:02:42.589 align:start position:0%
was just the second iteration we have
one<00:02:40.680><c> more</c><00:02:40.879><c> to</c><00:02:41.080><c> go</c><00:02:41.440><c> for</c><00:02:41.680><c> this</c><00:02:41.879><c> last</c><00:02:42.080><c> iteration</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
one more to go for this last iteration
 

00:02:42.599 --> 00:02:45.270 align:start position:0%
one more to go for this last iteration
we<00:02:42.760><c> look</c><00:02:42.920><c> at</c><00:02:43.080><c> the</c><00:02:43.360><c> hundreds</c><00:02:43.879><c> place</c><00:02:44.720><c> now</c><00:02:44.920><c> only</c>

00:02:45.270 --> 00:02:45.280 align:start position:0%
we look at the hundreds place now only
 

00:02:45.280 --> 00:02:46.949 align:start position:0%
we look at the hundreds place now only
five<00:02:45.480><c> of</c><00:02:45.599><c> these</c><00:02:45.800><c> numbers</c><00:02:46.280><c> actually</c><00:02:46.599><c> have</c><00:02:46.760><c> a</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
five of these numbers actually have a
 

00:02:46.959 --> 00:02:48.910 align:start position:0%
five of these numbers actually have a
digit<00:02:47.239><c> in</c><00:02:47.360><c> the</c><00:02:47.560><c> hundred's</c><00:02:48.080><c> place</c><00:02:48.560><c> which</c><00:02:48.680><c> means</c>

00:02:48.910 --> 00:02:48.920 align:start position:0%
digit in the hundred's place which means
 

00:02:48.920 --> 00:02:50.509 align:start position:0%
digit in the hundred's place which means
the<00:02:49.080><c> rest</c><00:02:49.239><c> of</c><00:02:49.400><c> them</c><00:02:49.720><c> are</c><00:02:49.879><c> going</c><00:02:49.959><c> to</c><00:02:50.040><c> be</c><00:02:50.159><c> counted</c>

00:02:50.509 --> 00:02:50.519 align:start position:0%
the rest of them are going to be counted
 

00:02:50.519 --> 00:02:53.110 align:start position:0%
the rest of them are going to be counted
as<00:02:50.720><c> having</c><00:02:51.000><c> a</c><00:02:51.239><c> zero</c><00:02:51.680><c> in</c><00:02:51.840><c> their</c><00:02:52.080><c> digits</c><00:02:52.480><c> place</c>

00:02:53.110 --> 00:02:53.120 align:start position:0%
as having a zero in their digits place
 

00:02:53.120 --> 00:02:55.990 align:start position:0%
as having a zero in their digits place
now<00:02:53.319><c> when</c><00:02:53.440><c> we</c><00:02:53.560><c> go</c><00:02:53.680><c> to</c><00:02:53.879><c> sort</c><00:02:54.400><c> this</c><00:02:55.400><c> based</c><00:02:55.680><c> on</c><00:02:55.800><c> the</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
now when we go to sort this based on the
 

00:02:56.000 --> 00:02:58.470 align:start position:0%
now when we go to sort this based on the
hundredth<00:02:56.480><c> digit</c><00:02:57.360><c> we'll</c><00:02:57.680><c> finally</c><00:02:58.040><c> get</c><00:02:58.200><c> our</c>

00:02:58.470 --> 00:02:58.480 align:start position:0%
hundredth digit we'll finally get our
 

00:02:58.480 --> 00:03:00.949 align:start position:0%
hundredth digit we'll finally get our
full<00:02:59.040><c> sorted</c><00:02:59.480><c> out</c><00:02:59.800><c> algorithm</c><00:03:00.560><c> and</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
full sorted out algorithm and
 

00:03:00.959 --> 00:03:02.869 align:start position:0%
full sorted out algorithm and
congratulations<00:03:01.959><c> you</c><00:03:02.159><c> just</c><00:03:02.319><c> went</c><00:03:02.519><c> through</c><00:03:02.720><c> a</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
congratulations you just went through a
 

00:03:02.879 --> 00:03:05.760 align:start position:0%
congratulations you just went through a
Ric<00:03:03.239><c> sort</c>

