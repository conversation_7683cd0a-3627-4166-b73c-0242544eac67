WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:01.910 align:start position:0%
 
and<00:00:00.480><c> howy</c><00:00:00.719><c> you</c><00:00:00.880><c> guys</c><00:00:01.079><c> so</c><00:00:01.240><c> we've</c><00:00:01.439><c> answered</c>

00:00:01.910 --> 00:00:01.920 align:start position:0%
and howy you guys so we've answered
 

00:00:01.920 --> 00:00:04.870 align:start position:0%
and howy you guys so we've answered
question<00:00:02.520><c> number</c><00:00:02.919><c> one</c><00:00:03.320><c> and</c><00:00:03.560><c> number</c><00:00:03.879><c> two</c><00:00:04.600><c> but</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
question number one and number two but
 

00:00:04.880 --> 00:00:07.030 align:start position:0%
question number one and number two but
now<00:00:05.080><c> we</c><00:00:05.200><c> need</c><00:00:05.359><c> to</c><00:00:05.640><c> start</c><00:00:06.160><c> digging</c><00:00:06.560><c> into</c>

00:00:07.030 --> 00:00:07.040 align:start position:0%
now we need to start digging into
 

00:00:07.040 --> 00:00:09.110 align:start position:0%
now we need to start digging into
question<00:00:07.399><c> number</c><00:00:07.720><c> three</c><00:00:08.160><c> over</c><00:00:08.440><c> here</c><00:00:08.840><c> I'm</c><00:00:08.960><c> just</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
question number three over here I'm just
 

00:00:09.120 --> 00:00:12.509 align:start position:0%
question number three over here I'm just
going<00:00:09.200><c> to</c><00:00:09.639><c> make</c><00:00:09.920><c> this</c><00:00:10.120><c> red</c><00:00:10.400><c> over</c><00:00:10.679><c> here</c><00:00:11.559><c> okay</c><00:00:12.320><c> uh</c>

00:00:12.509 --> 00:00:12.519 align:start position:0%
going to make this red over here okay uh
 

00:00:12.519 --> 00:00:13.990 align:start position:0%
going to make this red over here okay uh
let's<00:00:12.679><c> look</c><00:00:12.840><c> at</c><00:00:12.960><c> the</c><00:00:13.120><c> question</c><00:00:13.440><c> the</c><00:00:13.599><c> question</c>

00:00:13.990 --> 00:00:14.000 align:start position:0%
let's look at the question the question
 

00:00:14.000 --> 00:00:16.590 align:start position:0%
let's look at the question the question
is<00:00:14.240><c> do</c><00:00:14.360><c> you</c><00:00:14.480><c> see</c><00:00:14.799><c> anything</c><00:00:15.759><c> uh</c><00:00:16.039><c> interesting</c><00:00:16.440><c> in</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
is do you see anything uh interesting in
 

00:00:16.600 --> 00:00:18.750 align:start position:0%
is do you see anything uh interesting in
the<00:00:16.840><c> data</c><00:00:17.560><c> that</c><00:00:17.720><c> can</c><00:00:17.840><c> help</c><00:00:18.000><c> you</c><00:00:18.520><c> understand</c>

00:00:18.750 --> 00:00:18.760 align:start position:0%
the data that can help you understand
 

00:00:18.760 --> 00:00:21.109 align:start position:0%
the data that can help you understand
user<00:00:19.240><c> behavior</c><00:00:19.880><c> and</c><00:00:20.039><c> focus</c><00:00:20.400><c> your</c><00:00:20.640><c> campaign</c>

00:00:21.109 --> 00:00:21.119 align:start position:0%
user behavior and focus your campaign
 

00:00:21.119 --> 00:00:23.870 align:start position:0%
user behavior and focus your campaign
targeting<00:00:21.640><c> for</c><00:00:21.880><c> better</c><00:00:22.320><c> performance</c><00:00:23.320><c> moving</c>

00:00:23.870 --> 00:00:23.880 align:start position:0%
targeting for better performance moving
 

00:00:23.880 --> 00:00:27.070 align:start position:0%
targeting for better performance moving
forward<00:00:24.880><c> all</c><00:00:25.039><c> right</c><00:00:25.320><c> so</c><00:00:25.840><c> um</c><00:00:26.320><c> yeah</c><00:00:26.480><c> I</c><00:00:26.599><c> think</c>

00:00:27.070 --> 00:00:27.080 align:start position:0%
forward all right so um yeah I think
 

00:00:27.080 --> 00:00:30.830 align:start position:0%
forward all right so um yeah I think
that<00:00:27.760><c> met</c><00:00:28.279><c> the</c><00:00:28.439><c> data</c><00:00:28.720><c> is</c><00:00:28.800><c> in</c><00:00:29.000><c> metabase</c><00:00:30.279><c> um</c>

00:00:30.830 --> 00:00:30.840 align:start position:0%
that met the data is in metabase um
 

00:00:30.840 --> 00:00:33.670 align:start position:0%
that met the data is in metabase um
again<00:00:31.519><c> we</c><00:00:31.759><c> have</c><00:00:32.200><c> these</c><00:00:32.480><c> subp</c><00:00:32.920><c> Publishers</c><00:00:33.559><c> that</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
again we have these subp Publishers that
 

00:00:33.680 --> 00:00:36.910 align:start position:0%
again we have these subp Publishers that
were<00:00:33.960><c> grouping</c><00:00:34.360><c> the</c><00:00:34.559><c> data</c><00:00:34.920><c> ACC</c><00:00:35.680><c> um</c><00:00:36.520><c> grouping</c>

00:00:36.910 --> 00:00:36.920 align:start position:0%
were grouping the data ACC um grouping
 

00:00:36.920 --> 00:00:39.590 align:start position:0%
were grouping the data ACC um grouping
the<00:00:37.079><c> data</c><00:00:37.440><c> by</c><00:00:38.120><c> so</c><00:00:38.280><c> let's</c><00:00:38.440><c> go</c><00:00:38.600><c> into</c><00:00:39.239><c> subp</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
the data by so let's go into subp
 

00:00:39.600 --> 00:00:40.950 align:start position:0%
the data by so let's go into subp
publisher<00:00:39.920><c> let</c><00:00:40.039><c> me</c><00:00:40.120><c> show</c><00:00:40.239><c> you</c><00:00:40.360><c> a</c><00:00:40.480><c> little</c><00:00:40.719><c> trick</c>

00:00:40.950 --> 00:00:40.960 align:start position:0%
publisher let me show you a little trick
 

00:00:40.960 --> 00:00:44.069 align:start position:0%
publisher let me show you a little trick
in<00:00:41.239><c> Excel</c><00:00:41.840><c> actually</c><00:00:42.239><c> if</c><00:00:42.360><c> you</c><00:00:42.480><c> move</c><00:00:43.399><c> the</c><00:00:43.719><c> sub</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
in Excel actually if you move the sub
 

00:00:44.079 --> 00:00:47.029 align:start position:0%
in Excel actually if you move the sub
publisher<00:00:44.559><c> column</c><00:00:45.079><c> all</c><00:00:45.280><c> the</c><00:00:45.399><c> way</c><00:00:45.680><c> to</c><00:00:46.079><c> the</c><00:00:46.320><c> left</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
publisher column all the way to the left
 

00:00:47.039 --> 00:00:50.270 align:start position:0%
publisher column all the way to the left
one<00:00:47.360><c> second</c><00:00:48.320><c> sub</c><00:00:48.719><c> publisher</c><00:00:49.160><c> I</c><00:00:49.199><c> want</c><00:00:49.320><c> to</c><00:00:49.480><c> drag</c>

00:00:50.270 --> 00:00:50.280 align:start position:0%
one second sub publisher I want to drag
 

00:00:50.280 --> 00:00:54.389 align:start position:0%
one second sub publisher I want to drag
drag<00:00:51.280><c> drag</c><00:00:51.559><c> it</c><00:00:51.760><c> over</c><00:00:52.440><c> there</c><00:00:53.440><c> okay</c><00:00:53.680><c> cool</c><00:00:54.039><c> subp</c>

00:00:54.389 --> 00:00:54.399 align:start position:0%
drag drag it over there okay cool subp
 

00:00:54.399 --> 00:00:57.310 align:start position:0%
drag drag it over there okay cool subp
publisher<00:00:55.039><c> goes</c><00:00:55.359><c> over</c><00:00:55.680><c> there</c><00:00:56.520><c> and</c><00:00:56.960><c> there's</c>

00:00:57.310 --> 00:00:57.320 align:start position:0%
publisher goes over there and there's
 

00:00:57.320 --> 00:01:00.069 align:start position:0%
publisher goes over there and there's
this<00:00:57.520><c> little</c><00:00:57.800><c> thing</c><00:00:58.039><c> over</c><00:00:58.280><c> here</c><00:00:58.480><c> so</c><00:00:58.719><c> that</c><00:00:59.719><c> uh</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
this little thing over here so that uh
 

00:01:00.079 --> 00:01:02.349 align:start position:0%
this little thing over here so that uh
that<00:01:00.239><c> way</c><00:01:00.480><c> no</c><00:01:00.640><c> matter</c><00:01:01.120><c> how</c><00:01:01.280><c> much</c><00:01:01.480><c> I</c><00:01:01.600><c> scroll</c>

00:01:02.349 --> 00:01:02.359 align:start position:0%
that way no matter how much I scroll
 

00:01:02.359 --> 00:01:05.270 align:start position:0%
that way no matter how much I scroll
that<00:01:02.719><c> that</c><00:01:02.840><c> one</c><00:01:03.199><c> stays</c><00:01:04.199><c> uh</c><00:01:04.400><c> that</c><00:01:04.559><c> column</c><00:01:04.920><c> stays</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
that that one stays uh that column stays
 

00:01:05.280 --> 00:01:07.630 align:start position:0%
that that one stays uh that column stays
stagnant<00:01:05.920><c> all</c><00:01:06.040><c> right</c><00:01:06.240><c> so</c><00:01:06.720><c> per</c><00:01:07.040><c> sub</c><00:01:07.280><c> publisher</c>

00:01:07.630 --> 00:01:07.640 align:start position:0%
stagnant all right so per sub publisher
 

00:01:07.640 --> 00:01:10.230 align:start position:0%
stagnant all right so per sub publisher
we<00:01:07.759><c> have</c><00:01:08.000><c> the</c><00:01:08.640><c> um</c><00:01:09.040><c> whether</c><00:01:09.280><c> a</c><00:01:09.439><c> new</c><00:01:09.680><c> account</c><00:01:09.960><c> was</c>

00:01:10.230 --> 00:01:10.240 align:start position:0%
we have the um whether a new account was
 

00:01:10.240 --> 00:01:13.469 align:start position:0%
we have the um whether a new account was
created<00:01:10.799><c> this</c><00:01:10.880><c> is</c><00:01:11.080><c> really</c><00:01:11.400><c> each</c><00:01:11.600><c> row</c><00:01:12.600><c> signify</c>

00:01:13.469 --> 00:01:13.479 align:start position:0%
created this is really each row signify
 

00:01:13.479 --> 00:01:16.350 align:start position:0%
created this is really each row signify
uh<00:01:13.640><c> symbolizes</c><00:01:14.240><c> a</c><00:01:14.439><c> click</c><00:01:14.799><c> so</c><00:01:15.600><c> we</c><00:01:15.759><c> have</c><00:01:16.159><c> did</c>

00:01:16.350 --> 00:01:16.360 align:start position:0%
uh symbolizes a click so we have did
 

00:01:16.360 --> 00:01:18.149 align:start position:0%
uh symbolizes a click so we have did
that<00:01:16.520><c> click</c><00:01:16.759><c> turn</c><00:01:16.960><c> into</c><00:01:17.159><c> a</c><00:01:17.280><c> new</c><00:01:17.520><c> account</c><00:01:17.920><c> did</c>

00:01:18.149 --> 00:01:18.159 align:start position:0%
that click turn into a new account did
 

00:01:18.159 --> 00:01:20.830 align:start position:0%
that click turn into a new account did
that<00:01:18.360><c> click</c><00:01:18.600><c> turn</c><00:01:18.920><c> into</c><00:01:19.159><c> a</c><00:01:19.320><c> first</c><00:01:19.720><c> deposit</c><00:01:20.720><c> I</c>

00:01:20.830 --> 00:01:20.840 align:start position:0%
that click turn into a first deposit I
 

00:01:20.840 --> 00:01:22.870 align:start position:0%
that click turn into a first deposit I
think<00:01:20.960><c> we</c><00:01:21.079><c> need</c><00:01:21.200><c> to</c><00:01:21.360><c> start</c><00:01:21.720><c> thinking</c><00:01:22.079><c> about</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
think we need to start thinking about
 

00:01:22.880 --> 00:01:24.630 align:start position:0%
think we need to start thinking about
how<00:01:23.000><c> many</c><00:01:23.280><c> clicks</c><00:01:23.640><c> came</c><00:01:23.920><c> from</c><00:01:24.079><c> each</c><00:01:24.280><c> subp</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
how many clicks came from each subp
 

00:01:24.640 --> 00:01:27.910 align:start position:0%
how many clicks came from each subp
publisher<00:01:25.520><c> and</c><00:01:25.720><c> what</c><00:01:25.840><c> was</c><00:01:26.119><c> the</c><00:01:26.680><c> and</c><00:01:27.200><c> how</c><00:01:27.400><c> many</c>

00:01:27.910 --> 00:01:27.920 align:start position:0%
publisher and what was the and how many
 

00:01:27.920 --> 00:01:29.429 align:start position:0%
publisher and what was the and how many
and<00:01:28.040><c> then</c><00:01:28.240><c> how</c><00:01:28.400><c> many</c><00:01:28.640><c> new</c><00:01:28.880><c> accounts</c><00:01:29.200><c> how</c><00:01:29.280><c> many</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
and then how many new accounts how many
 

00:01:29.439 --> 00:01:30.870 align:start position:0%
and then how many new accounts how many
first<00:01:29.640><c> dep</c><00:01:29.920><c> deposits</c><00:01:30.400><c> and</c><00:01:30.479><c> then</c><00:01:30.680><c> the</c>

00:01:30.870 --> 00:01:30.880 align:start position:0%
first dep deposits and then the
 

00:01:30.880 --> 00:01:34.350 align:start position:0%
first dep deposits and then the
conversion<00:01:31.640><c> rate</c><00:01:32.640><c> uh</c><00:01:33.119><c> right</c><00:01:33.399><c> if</c><00:01:33.520><c> I</c><00:01:33.640><c> got</c><00:01:34.000><c> 100</c>

00:01:34.350 --> 00:01:34.360 align:start position:0%
conversion rate uh right if I got 100
 

00:01:34.360 --> 00:01:37.350 align:start position:0%
conversion rate uh right if I got 100
clicks<00:01:34.720><c> from</c><00:01:34.920><c> One</c><00:01:35.399><c> Source</c><00:01:36.399><c> what</c><00:01:36.720><c> percentage</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
clicks from One Source what percentage
 

00:01:37.360 --> 00:01:40.149 align:start position:0%
clicks from One Source what percentage
of<00:01:37.720><c> of</c><00:01:38.280><c> uh</c><00:01:38.560><c> of</c><00:01:38.799><c> those</c><00:01:39.079><c> clicks</c><00:01:39.439><c> turned</c><00:01:39.799><c> into</c>

00:01:40.149 --> 00:01:40.159 align:start position:0%
of of uh of those clicks turned into
 

00:01:40.159 --> 00:01:42.510 align:start position:0%
of of uh of those clicks turned into
conversions<00:01:41.159><c> so</c><00:01:41.320><c> I</c><00:01:41.399><c> think</c><00:01:41.560><c> we</c><00:01:41.680><c> can</c><00:01:41.920><c> actually</c>

00:01:42.510 --> 00:01:42.520 align:start position:0%
conversions so I think we can actually
 

00:01:42.520 --> 00:01:44.830 align:start position:0%
conversions so I think we can actually
build<00:01:42.840><c> on</c><00:01:43.000><c> our</c><00:01:43.280><c> previous</c><00:01:43.759><c> query</c><00:01:44.119><c> to</c><00:01:44.399><c> question</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
build on our previous query to question
 

00:01:44.840 --> 00:01:47.310 align:start position:0%
build on our previous query to question
two<00:01:45.520><c> and</c><00:01:45.680><c> now</c><00:01:45.960><c> we're</c><00:01:46.439><c> we're</c><00:01:46.719><c> coming</c><00:01:46.960><c> into</c>

00:01:47.310 --> 00:01:47.320 align:start position:0%
two and now we're we're coming into
 

00:01:47.320 --> 00:01:49.990 align:start position:0%
two and now we're we're coming into
question<00:01:47.600><c> number</c><00:01:48.119><c> three</c><00:01:49.119><c> uh</c><00:01:49.280><c> this</c><00:01:49.520><c> question</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
question number three uh this question
 

00:01:50.000 --> 00:01:52.030 align:start position:0%
question number three uh this question
by<00:01:50.159><c> the</c><00:01:50.280><c> way</c><00:01:50.520><c> I</c><00:01:50.600><c> think</c><00:01:51.240><c> uh</c><00:01:51.399><c> even</c><00:01:51.600><c> when</c><00:01:51.759><c> it's</c><00:01:51.880><c> a</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
by the way I think uh even when it's a
 

00:01:52.040 --> 00:01:54.749 align:start position:0%
by the way I think uh even when it's a
SQL<00:01:52.439><c> query</c><00:01:52.840><c> we</c><00:01:52.960><c> can</c><00:01:53.159><c> actually</c><00:01:54.119><c> again</c><00:01:54.399><c> turn</c><00:01:54.600><c> it</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
SQL query we can actually again turn it
 

00:01:54.759 --> 00:01:57.789 align:start position:0%
SQL query we can actually again turn it
into<00:01:54.960><c> a</c><00:01:55.159><c> bar</c><00:01:55.880><c> chart</c><00:01:56.880><c> and</c><00:01:57.079><c> it'll</c><00:01:57.360><c> look</c><00:01:57.520><c> a</c><00:01:57.600><c> little</c>

00:01:57.789 --> 00:01:57.799 align:start position:0%
into a bar chart and it'll look a little
 

00:01:57.799 --> 00:02:01.350 align:start position:0%
into a bar chart and it'll look a little
bit<00:01:58.000><c> cleaner</c><00:01:58.759><c> x</c><00:01:59.479><c> uh</c><00:01:59.640><c> let's</c><00:02:00.079><c> see</c><00:02:00.719><c> H</c><00:02:01.200><c> what</c>

00:02:01.350 --> 00:02:01.360 align:start position:0%
bit cleaner x uh let's see H what
 

00:02:01.360 --> 00:02:04.230 align:start position:0%
bit cleaner x uh let's see H what
happened<00:02:01.840><c> here</c><00:02:02.799><c> this</c><00:02:03.039><c> question</c><00:02:03.360><c> is</c><00:02:03.920><c> sub</c>

00:02:04.230 --> 00:02:04.240 align:start position:0%
happened here this question is sub
 

00:02:04.240 --> 00:02:06.270 align:start position:0%
happened here this question is sub
publisher

00:02:06.270 --> 00:02:06.280 align:start position:0%
publisher
 

00:02:06.280 --> 00:02:08.990 align:start position:0%
publisher
display<00:02:07.280><c> uh</c><00:02:07.479><c> let's</c><00:02:07.680><c> see</c><00:02:08.000><c> the</c><00:02:08.160><c> sum</c><00:02:08.640><c> the</c><00:02:08.759><c> number</c>

00:02:08.990 --> 00:02:09.000 align:start position:0%
display uh let's see the sum the number
 

00:02:09.000 --> 00:02:10.990 align:start position:0%
display uh let's see the sum the number
of

00:02:10.990 --> 00:02:11.000 align:start position:0%
of
 

00:02:11.000 --> 00:02:13.390 align:start position:0%
of
clicks<00:02:12.000><c> sum</c><00:02:12.200><c> of</c><00:02:12.360><c> blank</c><00:02:12.800><c> all</c><00:02:12.920><c> right</c><00:02:13.080><c> whatever</c>

00:02:13.390 --> 00:02:13.400 align:start position:0%
clicks sum of blank all right whatever
 

00:02:13.400 --> 00:02:14.630 align:start position:0%
clicks sum of blank all right whatever
we'll<00:02:13.560><c> forget</c><00:02:13.879><c> about</c><00:02:14.080><c> that</c><00:02:14.200><c> for</c><00:02:14.360><c> now</c><00:02:14.519><c> it's</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
we'll forget about that for now it's
 

00:02:14.640 --> 00:02:16.630 align:start position:0%
we'll forget about that for now it's
good<00:02:14.840><c> enough</c><00:02:15.080><c> as</c><00:02:15.280><c> is</c><00:02:15.959><c> let's</c><00:02:16.160><c> go</c><00:02:16.280><c> into</c><00:02:16.480><c> the</c>

00:02:16.630 --> 00:02:16.640 align:start position:0%
good enough as is let's go into the
 

00:02:16.640 --> 00:02:18.589 align:start position:0%
good enough as is let's go into the
original<00:02:17.200><c> question</c><00:02:17.640><c> let's</c><00:02:17.840><c> open</c><00:02:18.080><c> the</c><00:02:18.200><c> editor</c>

00:02:18.589 --> 00:02:18.599 align:start position:0%
original question let's open the editor
 

00:02:18.599 --> 00:02:21.910 align:start position:0%
original question let's open the editor
and<00:02:18.760><c> start</c><00:02:19.080><c> thinking</c><00:02:19.599><c> through</c><00:02:20.000><c> here</c><00:02:20.560><c> okay</c><00:02:21.200><c> so</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
and start thinking through here okay so
 

00:02:21.920 --> 00:02:23.630 align:start position:0%
and start thinking through here okay so
I<00:02:22.040><c> think</c><00:02:22.680><c> the</c><00:02:22.800><c> thing</c><00:02:23.000><c> that</c><00:02:23.120><c> we're</c><00:02:23.280><c> missing</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
I think the thing that we're missing
 

00:02:23.640 --> 00:02:27.509 align:start position:0%
I think the thing that we're missing
over<00:02:24.160><c> here</c><00:02:25.160><c> um</c><00:02:25.800><c> is</c><00:02:26.040><c> the</c><00:02:26.239><c> conversion</c><00:02:26.840><c> rates</c><00:02:27.280><c> so</c>

00:02:27.509 --> 00:02:27.519 align:start position:0%
over here um is the conversion rates so
 

00:02:27.519 --> 00:02:30.990 align:start position:0%
over here um is the conversion rates so
forget<00:02:27.879><c> about</c><00:02:28.239><c> the</c><00:02:28.440><c> where</c><00:02:28.680><c> sum</c><00:02:29.040><c> equals</c><00:02:29.440><c> blank</c>

00:02:30.990 --> 00:02:31.000 align:start position:0%
forget about the where sum equals blank
 

00:02:31.000 --> 00:02:33.430 align:start position:0%
forget about the where sum equals blank
and<00:02:31.160><c> we</c><00:02:31.239><c> want</c><00:02:31.360><c> to</c><00:02:31.519><c> do</c><00:02:31.840><c> select</c>

00:02:33.430 --> 00:02:33.440 align:start position:0%
and we want to do select
 

00:02:33.440 --> 00:02:36.309 align:start position:0%
and we want to do select
all<00:02:34.440><c> okay</c><00:02:34.680><c> we</c><00:02:34.800><c> got</c><00:02:34.959><c> the</c><00:02:35.120><c> number</c><00:02:35.360><c> of</c>

00:02:36.309 --> 00:02:36.319 align:start position:0%
all okay we got the number of
 

00:02:36.319 --> 00:02:38.990 align:start position:0%
all okay we got the number of
clicks<00:02:37.319><c> uh</c><00:02:37.440><c> let's</c><00:02:37.599><c> just</c><00:02:37.840><c> run</c><00:02:38.080><c> this</c><00:02:38.319><c> again</c>

00:02:38.990 --> 00:02:39.000 align:start position:0%
clicks uh let's just run this again
 

00:02:39.000 --> 00:02:41.030 align:start position:0%
clicks uh let's just run this again
verify<00:02:39.400><c> that</c><00:02:39.599><c> everything</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
verify that everything
 

00:02:41.040 --> 00:02:44.589 align:start position:0%
verify that everything
works<00:02:42.040><c> okay</c><00:02:42.280><c> number</c><00:02:42.560><c> of</c><00:02:42.800><c> clicks</c><00:02:43.519><c> ah</c><00:02:43.879><c> okay</c><00:02:44.080><c> fine</c>

00:02:44.589 --> 00:02:44.599 align:start position:0%
works okay number of clicks ah okay fine
 

00:02:44.599 --> 00:02:46.589 align:start position:0%
works okay number of clicks ah okay fine
this<00:02:44.840><c> top</c><00:02:45.200><c> Source</c><00:02:45.560><c> sub</c>

00:02:46.589 --> 00:02:46.599 align:start position:0%
this top Source sub
 

00:02:46.599 --> 00:02:49.630 align:start position:0%
this top Source sub
publisher<00:02:47.599><c> uh</c><00:02:48.280><c> with</c><00:02:48.480><c> the</c><00:02:48.680><c> most</c><00:02:49.120><c> conversion</c>

00:02:49.630 --> 00:02:49.640 align:start position:0%
publisher uh with the most conversion
 

00:02:49.640 --> 00:02:52.229 align:start position:0%
publisher uh with the most conversion
rates<00:02:50.000><c> also</c><00:02:50.239><c> had</c><00:02:50.400><c> the</c><00:02:50.519><c> most</c><00:02:50.760><c> number</c><00:02:51.000><c> of</c><00:02:51.239><c> clicks</c>

00:02:52.229 --> 00:02:52.239 align:start position:0%
rates also had the most number of clicks
 

00:02:52.239 --> 00:02:53.630 align:start position:0%
rates also had the most number of clicks
okay<00:02:52.519><c> so</c><00:02:52.720><c> that's</c><00:02:52.920><c> interesting</c><00:02:53.360><c> it</c><00:02:53.440><c> was</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
okay so that's interesting it was
 

00:02:53.640 --> 00:02:55.470 align:start position:0%
okay so that's interesting it was
probably<00:02:54.120><c> the</c><00:02:54.280><c> most</c><00:02:54.560><c> expensive</c><00:02:55.080><c> traffic</c>

00:02:55.470 --> 00:02:55.480 align:start position:0%
probably the most expensive traffic
 

00:02:55.480 --> 00:02:57.869 align:start position:0%
probably the most expensive traffic
Source<00:02:55.840><c> if</c><00:02:55.920><c> it</c><00:02:56.120><c> had</c><00:02:56.319><c> that</c><00:02:56.519><c> many</c><00:02:56.840><c> clicks</c><00:02:57.680><c> that's</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
Source if it had that many clicks that's
 

00:02:57.879 --> 00:03:00.630 align:start position:0%
Source if it had that many clicks that's
why<00:02:58.040><c> conversion</c><00:02:58.480><c> rate</c><00:02:58.680><c> is</c><00:02:58.879><c> important</c><00:02:59.879><c> so</c><00:03:00.519><c> you</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
why conversion rate is important so you
 

00:03:00.640 --> 00:03:02.270 align:start position:0%
why conversion rate is important so you
know<00:03:00.840><c> what</c><00:03:01.000><c> we</c><00:03:01.120><c> also</c><00:03:01.319><c> need</c><00:03:01.480><c> to</c><00:03:01.640><c> add</c><00:03:01.879><c> one</c><00:03:02.040><c> more</c>

00:03:02.270 --> 00:03:02.280 align:start position:0%
know what we also need to add one more
 

00:03:02.280 --> 00:03:03.949 align:start position:0%
know what we also need to add one more
data<00:03:02.599><c> point</c><00:03:02.879><c> right</c><00:03:03.040><c> cuz</c><00:03:03.200><c> so</c><00:03:03.360><c> far</c><00:03:03.560><c> we're</c><00:03:03.680><c> only</c>

00:03:03.949 --> 00:03:03.959 align:start position:0%
data point right cuz so far we're only
 

00:03:03.959 --> 00:03:06.350 align:start position:0%
data point right cuz so far we're only
counting<00:03:04.400><c> the</c><00:03:04.640><c> first</c><00:03:05.040><c> deposit</c><00:03:05.680><c> we're</c><00:03:05.879><c> not</c>

00:03:06.350 --> 00:03:06.360 align:start position:0%
counting the first deposit we're not
 

00:03:06.360 --> 00:03:08.149 align:start position:0%
counting the first deposit we're not
we're<00:03:06.480><c> not</c><00:03:06.680><c> counting</c><00:03:07.040><c> the</c><00:03:07.200><c> new</c><00:03:07.480><c> account</c><00:03:07.959><c> so</c>

00:03:08.149 --> 00:03:08.159 align:start position:0%
we're not counting the new account so
 

00:03:08.159 --> 00:03:12.030 align:start position:0%
we're not counting the new account so
let's<00:03:08.840><c> let's</c><00:03:09.080><c> just</c><00:03:09.280><c> go</c><00:03:09.480><c> ahead</c><00:03:10.200><c> and</c><00:03:10.680><c> take</c><00:03:11.680><c> um</c>

00:03:12.030 --> 00:03:12.040 align:start position:0%
let's let's just go ahead and take um
 

00:03:12.040 --> 00:03:14.750 align:start position:0%
let's let's just go ahead and take um
just<00:03:12.319><c> copy</c><00:03:12.720><c> paste</c><00:03:13.200><c> this</c><00:03:13.799><c> and</c><00:03:13.959><c> rename</c><00:03:14.360><c> the</c>

00:03:14.750 --> 00:03:14.760 align:start position:0%
just copy paste this and rename the
 

00:03:14.760 --> 00:03:18.229 align:start position:0%
just copy paste this and rename the
columns<00:03:15.760><c> uh</c><00:03:16.040><c> let's</c><00:03:16.280><c> call</c><00:03:16.599><c> this</c><00:03:17.440><c> uh</c><00:03:17.599><c> let's</c><00:03:17.799><c> see</c>

00:03:18.229 --> 00:03:18.239 align:start position:0%
columns uh let's call this uh let's see
 

00:03:18.239 --> 00:03:19.789 align:start position:0%
columns uh let's call this uh let's see
sum<00:03:18.680><c> of</c>

00:03:19.789 --> 00:03:19.799 align:start position:0%
sum of
 

00:03:19.799 --> 00:03:21.309 align:start position:0%
sum of
deposits

00:03:21.309 --> 00:03:21.319 align:start position:0%
deposits
 

00:03:21.319 --> 00:03:24.990 align:start position:0%
deposits
okay<00:03:22.319><c> and</c><00:03:22.599><c> I'm</c><00:03:22.720><c> going</c><00:03:22.840><c> to</c><00:03:23.040><c> count</c><00:03:23.400><c> I'm</c><00:03:23.519><c> going</c><00:03:24.000><c> to</c>

00:03:24.990 --> 00:03:25.000 align:start position:0%
okay and I'm going to count I'm going to
 

00:03:25.000 --> 00:03:28.190 align:start position:0%
okay and I'm going to count I'm going to
I'm<00:03:25.120><c> going</c><00:03:25.239><c> to</c><00:03:25.400><c> just</c><00:03:25.640><c> copy</c><00:03:26.040><c> paste</c><00:03:26.680><c> it</c><00:03:27.680><c> and</c><00:03:28.080><c> what</c>

00:03:28.190 --> 00:03:28.200 align:start position:0%
I'm going to just copy paste it and what
 

00:03:28.200 --> 00:03:30.229 align:start position:0%
I'm going to just copy paste it and what
am<00:03:28.319><c> I</c><00:03:28.439><c> going</c><00:03:28.519><c> to</c><00:03:28.680><c> call</c><00:03:29.000><c> this</c><00:03:29.519><c> I'm</c><00:03:29.840><c> going</c><00:03:29.920><c> to</c><00:03:30.000><c> do</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
am I going to call this I'm going to do
 

00:03:30.239 --> 00:03:33.190 align:start position:0%
am I going to call this I'm going to do
another<00:03:30.560><c> a</c><00:03:30.720><c> new</c><00:03:31.040><c> Ro</c><00:03:31.360><c> a</c><00:03:31.519><c> new</c><00:03:32.080><c> uh</c><00:03:32.319><c> also</c><00:03:32.680><c> count</c><00:03:33.040><c> the</c>

00:03:33.190 --> 00:03:33.200 align:start position:0%
another a new Ro a new uh also count the
 

00:03:33.200 --> 00:03:37.949 align:start position:0%
another a new Ro a new uh also count the
new<00:03:33.480><c> account</c><00:03:33.920><c> sum</c><00:03:34.400><c> of</c><00:03:35.400><c> new</c><00:03:36.239><c> accounts</c><00:03:37.239><c> okay</c><00:03:37.760><c> sum</c>

00:03:37.949 --> 00:03:37.959 align:start position:0%
new account sum of new accounts okay sum
 

00:03:37.959 --> 00:03:38.750 align:start position:0%
new account sum of new accounts okay sum
of

00:03:38.750 --> 00:03:38.760 align:start position:0%
of
 

00:03:38.760 --> 00:03:41.710 align:start position:0%
of
registrations<00:03:39.959><c> okay</c>

00:03:41.710 --> 00:03:41.720 align:start position:0%
registrations okay
 

00:03:41.720 --> 00:03:45.070 align:start position:0%
registrations okay
registrations<00:03:42.720><c> okay</c><00:03:43.319><c> sum</c><00:03:43.560><c> of</c><00:03:44.080><c> registrations</c>

00:03:45.070 --> 00:03:45.080 align:start position:0%
registrations okay sum of registrations
 

00:03:45.080 --> 00:03:48.470 align:start position:0%
registrations okay sum of registrations
we're<00:03:45.280><c> going</c><00:03:45.720><c> to</c><00:03:46.720><c> just</c><00:03:46.959><c> rename</c><00:03:47.519><c> this</c>

00:03:48.470 --> 00:03:48.480 align:start position:0%
we're going to just rename this
 

00:03:48.480 --> 00:03:53.270 align:start position:0%
we're going to just rename this
newcore

00:03:53.270 --> 00:03:53.280 align:start position:0%
 
 

00:03:53.280 --> 00:03:55.869 align:start position:0%
 
account<00:03:54.280><c> okay</c><00:03:55.000><c> and</c><00:03:55.319><c> make</c><00:03:55.480><c> sure</c><00:03:55.680><c> that</c>

00:03:55.869 --> 00:03:55.879 align:start position:0%
account okay and make sure that
 

00:03:55.879 --> 00:04:00.110 align:start position:0%
account okay and make sure that
everything<00:03:56.560><c> that</c><00:03:56.720><c> the</c><00:03:56.959><c> data</c><00:03:57.280><c> comes</c><00:03:57.560><c> in</c>

00:04:00.110 --> 00:04:00.120 align:start position:0%
everything that the data comes in
 

00:04:00.120 --> 00:04:02.270 align:start position:0%
everything that the data comes in
okay<00:04:00.480><c> of</c><00:04:00.680><c> course</c><00:04:01.159><c> there's</c><00:04:01.360><c> an</c><00:04:01.519><c> error</c><00:04:02.000><c> okay</c><00:04:02.159><c> I</c>

00:04:02.270 --> 00:04:02.280 align:start position:0%
okay of course there's an error okay I
 

00:04:02.280 --> 00:04:04.350 align:start position:0%
okay of course there's an error okay I
forgot<00:04:02.599><c> the</c>

00:04:04.350 --> 00:04:04.360 align:start position:0%
forgot the
 

00:04:04.360 --> 00:04:09.030 align:start position:0%
forgot the
comma<00:04:05.360><c> and</c><00:04:06.079><c> we</c><00:04:06.200><c> want</c><00:04:06.319><c> to</c><00:04:06.480><c> run</c><00:04:06.680><c> it</c>

00:04:09.030 --> 00:04:09.040 align:start position:0%
 
 

00:04:09.040 --> 00:04:12.429 align:start position:0%
 
again<00:04:10.040><c> okay</c><00:04:10.280><c> cool</c><00:04:10.560><c> so</c><00:04:10.840><c> per</c><00:04:11.120><c> sub</c><00:04:11.519><c> publisher</c>

00:04:12.429 --> 00:04:12.439 align:start position:0%
again okay cool so per sub publisher
 

00:04:12.439 --> 00:04:14.350 align:start position:0%
again okay cool so per sub publisher
I've<00:04:12.640><c> got</c><00:04:12.959><c> the</c><00:04:13.120><c> number</c><00:04:13.319><c> of</c><00:04:13.480><c> clicks</c><00:04:14.000><c> the</c><00:04:14.120><c> number</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
I've got the number of clicks the number
 

00:04:14.360 --> 00:04:18.150 align:start position:0%
I've got the number of clicks the number
of<00:04:14.560><c> deposits</c><00:04:15.159><c> and</c><00:04:15.280><c> the</c><00:04:15.400><c> number</c><00:04:15.599><c> of</c>

00:04:18.150 --> 00:04:18.160 align:start position:0%
 
 

00:04:18.160 --> 00:04:20.270 align:start position:0%
 
registrations<00:04:19.160><c> and</c><00:04:19.359><c> now</c><00:04:19.639><c> I</c><00:04:19.720><c> need</c><00:04:19.880><c> to</c><00:04:20.040><c> also</c>

00:04:20.270 --> 00:04:20.280 align:start position:0%
registrations and now I need to also
 

00:04:20.280 --> 00:04:22.590 align:start position:0%
registrations and now I need to also
have<00:04:20.440><c> the</c><00:04:20.639><c> conversion</c><00:04:21.239><c> rate</c><00:04:21.680><c> so</c><00:04:22.240><c> what</c><00:04:22.360><c> can</c><00:04:22.479><c> I</c>

00:04:22.590 --> 00:04:22.600 align:start position:0%
have the conversion rate so what can I
 

00:04:22.600 --> 00:04:24.189 align:start position:0%
have the conversion rate so what can I
do<00:04:22.759><c> in</c><00:04:22.880><c> terms</c><00:04:23.080><c> of</c><00:04:23.240><c> the</c><00:04:23.440><c> how</c><00:04:23.560><c> do</c><00:04:23.639><c> I</c><00:04:23.840><c> get</c><00:04:24.000><c> those</c>

00:04:24.189 --> 00:04:24.199 align:start position:0%
do in terms of the how do I get those
 

00:04:24.199 --> 00:04:27.270 align:start position:0%
do in terms of the how do I get those
conversion<00:04:24.720><c> rates</c><00:04:25.280><c> I'm</c><00:04:25.479><c> counting</c><00:04:25.840><c> it</c><00:04:26.199><c> here</c><00:04:27.080><c> uh</c>

00:04:27.270 --> 00:04:27.280 align:start position:0%
conversion rates I'm counting it here uh
 

00:04:27.280 --> 00:04:29.870 align:start position:0%
conversion rates I'm counting it here uh
within<00:04:27.880><c> within</c><00:04:28.160><c> this</c><00:04:28.320><c> nested</c><00:04:28.840><c> query</c><00:04:29.320><c> but</c><00:04:29.520><c> I</c><00:04:29.800><c> I</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
within within this nested query but I I
 

00:04:29.880 --> 00:04:32.029 align:start position:0%
within within this nested query but I I
want<00:04:30.000><c> to</c><00:04:30.120><c> do</c><00:04:30.320><c> my</c><00:04:30.600><c> conversion</c><00:04:31.039><c> rate</c><00:04:31.360><c> analysis</c>

00:04:32.029 --> 00:04:32.039 align:start position:0%
want to do my conversion rate analysis
 

00:04:32.039 --> 00:04:34.870 align:start position:0%
want to do my conversion rate analysis
within<00:04:32.759><c> the</c><00:04:32.960><c> popup</c><00:04:33.400><c> query</c><00:04:34.160><c> right</c><00:04:34.400><c> which</c><00:04:34.520><c> is</c><00:04:34.680><c> in</c>

00:04:34.870 --> 00:04:34.880 align:start position:0%
within the popup query right which is in
 

00:04:34.880 --> 00:04:38.270 align:start position:0%
within the popup query right which is in
the<00:04:35.039><c> enveloping</c><00:04:35.600><c> query</c><00:04:36.039><c> so</c><00:04:36.600><c> I</c><00:04:36.720><c> do</c><00:04:37.039><c> select</c><00:04:37.479><c> all</c>

00:04:38.270 --> 00:04:38.280 align:start position:0%
the enveloping query so I do select all
 

00:04:38.280 --> 00:04:41.710 align:start position:0%
the enveloping query so I do select all
and<00:04:38.600><c> I</c><00:04:38.720><c> want</c><00:04:39.039><c> some</c><00:04:39.240><c> more</c><00:04:39.560><c> data</c><00:04:39.919><c> points</c><00:04:40.400><c> I</c><00:04:40.720><c> want</c>

00:04:41.710 --> 00:04:41.720 align:start position:0%
and I want some more data points I want
 

00:04:41.720 --> 00:04:46.990 align:start position:0%
and I want some more data points I want
the<00:04:42.639><c> um</c><00:04:43.639><c> uh</c><00:04:44.039><c> let's</c><00:04:44.240><c> see</c><00:04:44.680><c> deposits</c>

00:04:46.990 --> 00:04:47.000 align:start position:0%
the um uh let's see deposits
 

00:04:47.000 --> 00:04:49.350 align:start position:0%
the um uh let's see deposits
conversion

00:04:49.350 --> 00:04:49.360 align:start position:0%
conversion
 

00:04:49.360 --> 00:04:51.990 align:start position:0%
conversion
okay<00:04:50.479><c> deposit</c>

00:04:51.990 --> 00:04:52.000 align:start position:0%
okay deposit
 

00:04:52.000 --> 00:04:56.909 align:start position:0%
okay deposit
conversion<00:04:53.280><c> rate</c><00:04:54.280><c> okay</c><00:04:54.800><c> and</c><00:04:55.360><c> I</c><00:04:55.479><c> want</c><00:04:56.080><c> the</c>

00:04:56.909 --> 00:04:56.919 align:start position:0%
conversion rate okay and I want the
 

00:04:56.919 --> 00:05:03.469 align:start position:0%
conversion rate okay and I want the
registrations<00:04:57.919><c> conversion</c><00:04:58.639><c> rate</c>

00:05:03.469 --> 00:05:03.479 align:start position:0%
 
 

00:05:03.479 --> 00:05:09.029 align:start position:0%
 
okay<00:05:04.520><c> re</c>

00:05:09.029 --> 00:05:09.039 align:start position:0%
 
 

00:05:09.039 --> 00:05:11.070 align:start position:0%
 
registrations

00:05:11.070 --> 00:05:11.080 align:start position:0%
registrations
 

00:05:11.080 --> 00:05:16.070 align:start position:0%
registrations
okay<00:05:12.080><c> um</c><00:05:12.600><c> as</c><00:05:13.479><c> okay</c><00:05:14.080><c> and</c><00:05:14.880><c> as</c><00:05:15.479><c> okay</c><00:05:15.720><c> what</c><00:05:15.840><c> do</c><00:05:15.960><c> I</c>

00:05:16.070 --> 00:05:16.080 align:start position:0%
okay um as okay and as okay what do I
 

00:05:16.080 --> 00:05:17.150 align:start position:0%
okay um as okay and as okay what do I
need

00:05:17.150 --> 00:05:17.160 align:start position:0%
need
 

00:05:17.160 --> 00:05:20.230 align:start position:0%
need
to<00:05:18.160><c> which</c><00:05:18.360><c> two</c><00:05:18.720><c> which</c><00:05:18.919><c> two</c><00:05:19.199><c> columns</c><00:05:19.639><c> can</c><00:05:19.759><c> I</c><00:05:19.960><c> use</c>

00:05:20.230 --> 00:05:20.240 align:start position:0%
to which two which two columns can I use
 

00:05:20.240 --> 00:05:22.390 align:start position:0%
to which two which two columns can I use
in<00:05:20.360><c> order</c><00:05:20.600><c> to</c><00:05:20.840><c> calculate</c><00:05:21.440><c> this</c><00:05:21.800><c> this</c>

00:05:22.390 --> 00:05:22.400 align:start position:0%
in order to calculate this this
 

00:05:22.400 --> 00:05:26.390 align:start position:0%
in order to calculate this this
conversion<00:05:23.400><c> I</c><00:05:23.520><c> want</c><00:05:23.720><c> to</c><00:05:23.919><c> use</c><00:05:24.680><c> my</c><00:05:25.120><c> sum</c><00:05:25.440><c> of</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
conversion I want to use my sum of
 

00:05:26.400 --> 00:05:29.230 align:start position:0%
conversion I want to use my sum of
deposits<00:05:27.400><c> ah</c><00:05:27.720><c> okay</c><00:05:27.960><c> select</c><00:05:28.440><c> count</c><00:05:28.759><c> as</c><00:05:28.960><c> number</c>

00:05:29.230 --> 00:05:29.240 align:start position:0%
deposits ah okay select count as number
 

00:05:29.240 --> 00:05:30.790 align:start position:0%
deposits ah okay select count as number
of<00:05:29.720><c> number</c><00:05:29.919><c> of</c>

00:05:30.790 --> 00:05:30.800 align:start position:0%
of number of
 

00:05:30.800 --> 00:05:34.550 align:start position:0%
of number of
clicks<00:05:31.800><c> okay</c><00:05:32.319><c> let</c><00:05:32.400><c> me</c><00:05:32.639><c> rename</c><00:05:33.160><c> this</c><00:05:33.639><c> so</c>

00:05:34.550 --> 00:05:34.560 align:start position:0%
clicks okay let me rename this so
 

00:05:34.560 --> 00:05:38.070 align:start position:0%
clicks okay let me rename this so
that<00:05:35.560><c> okay</c><00:05:35.919><c> that</c><00:05:36.039><c> is</c><00:05:36.199><c> clear</c><00:05:36.479><c> to</c><00:05:37.080><c> everybody</c>

00:05:38.070 --> 00:05:38.080 align:start position:0%
that okay that is clear to everybody
 

00:05:38.080 --> 00:05:43.629 align:start position:0%
that okay that is clear to everybody
number<00:05:38.440><c> of</c><00:05:38.720><c> clicks</c><00:05:39.400><c> okay</c><00:05:39.800><c> so</c><00:05:40.800><c> I</c><00:05:41.080><c> want</c><00:05:42.280><c> the</c><00:05:43.280><c> sum</c>

00:05:43.629 --> 00:05:43.639 align:start position:0%
number of clicks okay so I want the sum
 

00:05:43.639 --> 00:05:47.029 align:start position:0%
number of clicks okay so I want the sum
of

00:05:47.029 --> 00:05:47.039 align:start position:0%
 
 

00:05:47.039 --> 00:05:50.990 align:start position:0%
 
deposits<00:05:48.400><c> deposits</c><00:05:49.400><c> sum</c><00:05:49.759><c> of</c><00:05:50.080><c> deposits</c><00:05:50.880><c> which</c>

00:05:50.990 --> 00:05:51.000 align:start position:0%
deposits deposits sum of deposits which
 

00:05:51.000 --> 00:05:53.110 align:start position:0%
deposits deposits sum of deposits which
is<00:05:51.160><c> the</c><00:05:51.319><c> number</c><00:05:51.600><c> of</c><00:05:51.840><c> deposits</c>

00:05:53.110 --> 00:05:53.120 align:start position:0%
is the number of deposits
 

00:05:53.120 --> 00:05:56.430 align:start position:0%
is the number of deposits
over<00:05:54.120><c> over</c><00:05:55.000><c> number</c><00:05:55.440><c> of</c>

00:05:56.430 --> 00:05:56.440 align:start position:0%
over over number of
 

00:05:56.440 --> 00:06:00.469 align:start position:0%
over over number of
clicks<00:05:57.720><c> okay</c><00:05:58.720><c> and</c><00:05:59.000><c> I</c><00:05:59.120><c> want</c><00:05:59.400><c> the</c><00:05:59.720><c> sum</c><00:06:00.120><c> of</c>

00:06:00.469 --> 00:06:00.479 align:start position:0%
clicks okay and I want the sum of
 

00:06:00.479 --> 00:06:03.909 align:start position:0%
clicks okay and I want the sum of
registrations<00:06:01.360><c> over</c><00:06:01.639><c> number</c><00:06:01.919><c> of</c><00:06:02.080><c> clicks</c><00:06:02.720><c> okay</c>

00:06:03.909 --> 00:06:03.919 align:start position:0%
registrations over number of clicks okay
 

00:06:03.919 --> 00:06:06.990 align:start position:0%
registrations over number of clicks okay
cool<00:06:04.919><c> sum</c>

00:06:06.990 --> 00:06:07.000 align:start position:0%
cool sum
 

00:06:07.000 --> 00:06:10.550 align:start position:0%
cool sum
of

00:06:10.550 --> 00:06:10.560 align:start position:0%
 
 

00:06:10.560 --> 00:06:13.150 align:start position:0%
 
registrations<00:06:11.560><c> sum</c><00:06:11.800><c> of</c><00:06:12.039><c> registrations</c><00:06:12.880><c> over</c>

00:06:13.150 --> 00:06:13.160 align:start position:0%
registrations sum of registrations over
 

00:06:13.160 --> 00:06:15.830 align:start position:0%
registrations sum of registrations over
number<00:06:13.360><c> of</c><00:06:13.520><c> clicks</c><00:06:13.960><c> let's</c><00:06:14.120><c> see</c><00:06:14.280><c> if</c><00:06:14.479><c> everything</c>

00:06:15.830 --> 00:06:15.840 align:start position:0%
number of clicks let's see if everything
 

00:06:15.840 --> 00:06:19.350 align:start position:0%
number of clicks let's see if everything
works<00:06:16.840><c> okay</c><00:06:17.080><c> cool</c><00:06:17.960><c> uh</c><00:06:18.199><c> nice</c><00:06:18.680><c> deposits</c>

00:06:19.350 --> 00:06:19.360 align:start position:0%
works okay cool uh nice deposits
 

00:06:19.360 --> 00:06:21.550 align:start position:0%
works okay cool uh nice deposits
conversion<00:06:19.960><c> rate</c><00:06:20.440><c> and</c><00:06:20.599><c> the</c><00:06:20.800><c> registration</c>

00:06:21.550 --> 00:06:21.560 align:start position:0%
conversion rate and the registration
 

00:06:21.560 --> 00:06:23.189 align:start position:0%
conversion rate and the registration
conversion<00:06:22.039><c> rate</c><00:06:22.280><c> seem</c><00:06:22.479><c> to</c><00:06:22.599><c> be</c><00:06:22.759><c> coming</c><00:06:22.960><c> out</c>

00:06:23.189 --> 00:06:23.199 align:start position:0%
conversion rate seem to be coming out
 

00:06:23.199 --> 00:06:25.629 align:start position:0%
conversion rate seem to be coming out
cleanly<00:06:23.680><c> so</c><00:06:23.880><c> that's</c><00:06:24.120><c> awesome</c><00:06:25.000><c> now</c><00:06:25.319><c> in</c><00:06:25.440><c> order</c>

00:06:25.629 --> 00:06:25.639 align:start position:0%
cleanly so that's awesome now in order
 

00:06:25.639 --> 00:06:27.950 align:start position:0%
cleanly so that's awesome now in order
to<00:06:25.800><c> find</c><00:06:26.000><c> our</c><00:06:26.240><c> top</c><00:06:26.479><c> performers</c><00:06:27.240><c> here</c><00:06:27.639><c> we</c><00:06:27.800><c> just</c>

00:06:27.950 --> 00:06:27.960 align:start position:0%
to find our top performers here we just
 

00:06:27.960 --> 00:06:30.629 align:start position:0%
to find our top performers here we just
need<00:06:28.160><c> to</c><00:06:28.520><c> order</c><00:06:28.919><c> the</c><00:06:29.120><c> data</c><00:06:29.680><c> according</c><00:06:30.000><c> to</c><00:06:30.240><c> the</c>

00:06:30.629 --> 00:06:30.639 align:start position:0%
need to order the data according to the
 

00:06:30.639 --> 00:06:34.350 align:start position:0%
need to order the data according to the
conversion<00:06:31.280><c> rate</c><00:06:31.919><c> so</c><00:06:32.199><c> what's</c><00:06:33.039><c> which</c><00:06:33.599><c> um</c><00:06:34.160><c> and</c><00:06:34.280><c> I</c>

00:06:34.350 --> 00:06:34.360 align:start position:0%
conversion rate so what's which um and I
 

00:06:34.360 --> 00:06:35.469 align:start position:0%
conversion rate so what's which um and I
think<00:06:34.479><c> we</c><00:06:34.599><c> can</c><00:06:34.759><c> actually</c><00:06:35.000><c> do</c><00:06:35.160><c> that</c><00:06:35.319><c> through</c>

00:06:35.469 --> 00:06:35.479 align:start position:0%
think we can actually do that through
 

00:06:35.479 --> 00:06:38.589 align:start position:0%
think we can actually do that through
the<00:06:35.599><c> guey</c><00:06:36.000><c> over</c><00:06:36.199><c> here</c><00:06:36.840><c> wait</c><00:06:37.080><c> let</c><00:06:37.199><c> me</c><00:06:37.400><c> read</c><00:06:37.960><c> no</c>

00:06:38.589 --> 00:06:38.599 align:start position:0%
the guey over here wait let me read no
 

00:06:38.599 --> 00:06:41.350 align:start position:0%
the guey over here wait let me read no
Okay<00:06:39.280><c> order</c><00:06:39.680><c> by</c><00:06:40.280><c> Okay</c><00:06:40.479><c> order</c><00:06:40.800><c> by</c><00:06:41.039><c> the</c><00:06:41.120><c> number</c>

00:06:41.350 --> 00:06:41.360 align:start position:0%
Okay order by Okay order by the number
 

00:06:41.360 --> 00:06:46.150 align:start position:0%
Okay order by Okay order by the number
of<00:06:41.560><c> clicks</c><00:06:41.919><c> descending</c><00:06:42.479><c> now</c><00:06:42.880><c> I</c><00:06:43.000><c> want</c><00:06:43.840><c> bu</c><00:06:44.840><c> the</c>

00:06:46.150 --> 00:06:46.160 align:start position:0%
of clicks descending now I want bu the
 

00:06:46.160 --> 00:06:50.550 align:start position:0%
of clicks descending now I want bu the
um<00:06:47.160><c> uh</c><00:06:47.319><c> let's</c><00:06:47.560><c> see</c><00:06:48.280><c> deposits</c><00:06:49.080><c> conversion</c><00:06:49.720><c> rate</c>

00:06:50.550 --> 00:06:50.560 align:start position:0%
um uh let's see deposits conversion rate
 

00:06:50.560 --> 00:06:52.870 align:start position:0%
um uh let's see deposits conversion rate
that's<00:06:50.759><c> the</c><00:06:50.880><c> most</c><00:06:51.120><c> important</c><00:06:51.560><c> metrics</c><00:06:52.080><c> for</c>

00:06:52.870 --> 00:06:52.880 align:start position:0%
that's the most important metrics for
 

00:06:52.880 --> 00:06:56.670 align:start position:0%
that's the most important metrics for
us<00:06:53.880><c> H</c><00:06:54.360><c> deposits</c><00:06:55.000><c> conversion</c><00:06:55.560><c> rate</c><00:06:55.919><c> ah</c>

00:06:56.670 --> 00:06:56.680 align:start position:0%
us H deposits conversion rate ah
 

00:06:56.680 --> 00:06:59.029 align:start position:0%
us H deposits conversion rate ah
interesting<00:06:57.680><c> so</c><00:06:57.840><c> for</c><00:06:58.039><c> some</c><00:06:58.240><c> of</c><00:06:58.400><c> these</c><00:06:58.560><c> sources</c>

00:06:59.029 --> 00:06:59.039 align:start position:0%
interesting so for some of these sources
 

00:06:59.039 --> 00:07:01.270 align:start position:0%
interesting so for some of these sources
we<00:06:59.160><c> only</c><00:06:59.360><c> have</c><00:06:59.759><c> one</c><00:07:00.000><c> click</c><00:07:00.360><c> but</c><00:07:00.479><c> we</c><00:07:00.639><c> also</c><00:07:00.960><c> had</c>

00:07:01.270 --> 00:07:01.280 align:start position:0%
we only have one click but we also had
 

00:07:01.280 --> 00:07:02.230 align:start position:0%
we only have one click but we also had
one

00:07:02.230 --> 00:07:02.240 align:start position:0%
one
 

00:07:02.240 --> 00:07:04.869 align:start position:0%
one
registration<00:07:03.240><c> and</c><00:07:03.440><c> one</c><00:07:03.759><c> deposit</c><00:07:04.319><c> so</c><00:07:04.520><c> that's</c>

00:07:04.869 --> 00:07:04.879 align:start position:0%
registration and one deposit so that's
 

00:07:04.879 --> 00:07:06.950 align:start position:0%
registration and one deposit so that's
great<00:07:05.319><c> actually</c><00:07:05.960><c> this</c><00:07:06.199><c> maybe</c><00:07:06.520><c> this</c><00:07:06.639><c> is</c><00:07:06.759><c> our</c>

00:07:06.950 --> 00:07:06.960 align:start position:0%
great actually this maybe this is our
 

00:07:06.960 --> 00:07:08.629 align:start position:0%
great actually this maybe this is our
top<00:07:07.199><c> performing</c><00:07:07.680><c> sources</c><00:07:08.160><c> but</c><00:07:08.280><c> they</c><00:07:08.360><c> don't</c>

00:07:08.629 --> 00:07:08.639 align:start position:0%
top performing sources but they don't
 

00:07:08.639 --> 00:07:09.629 align:start position:0%
top performing sources but they don't
actually

00:07:09.629 --> 00:07:09.639 align:start position:0%
actually
 

00:07:09.639 --> 00:07:12.430 align:start position:0%
actually
scale<00:07:10.639><c> okay</c><00:07:10.960><c> interesting</c><00:07:11.759><c> deposits</c>

00:07:12.430 --> 00:07:12.440 align:start position:0%
scale okay interesting deposits
 

00:07:12.440 --> 00:07:14.830 align:start position:0%
scale okay interesting deposits
conversion<00:07:13.120><c> right</c><00:07:13.360><c> 0.</c>

00:07:14.830 --> 00:07:14.840 align:start position:0%
conversion right 0.
 

00:07:14.840 --> 00:07:18.710 align:start position:0%
conversion right 0.
five<00:07:15.840><c> nice</c><00:07:16.560><c> Okay</c><00:07:16.800><c> cool</c><00:07:17.080><c> so</c><00:07:17.280><c> maybe</c><00:07:17.560><c> let's</c><00:07:17.840><c> do</c>

00:07:18.710 --> 00:07:18.720 align:start position:0%
five nice Okay cool so maybe let's do
 

00:07:18.720 --> 00:07:21.710 align:start position:0%
five nice Okay cool so maybe let's do
show<00:07:18.960><c> me</c><00:07:19.360><c> the</c><00:07:19.759><c> the</c><00:07:19.960><c> highest</c><00:07:20.319><c> converting</c>

00:07:21.710 --> 00:07:21.720 align:start position:0%
show me the the highest converting
 

00:07:21.720 --> 00:07:24.390 align:start position:0%
show me the the highest converting
sources<00:07:22.720><c> highest</c><00:07:22.960><c> converting</c><00:07:23.360><c> subp</c>

00:07:24.390 --> 00:07:24.400 align:start position:0%
sources highest converting subp
 

00:07:24.400 --> 00:07:31.189 align:start position:0%
sources highest converting subp
Publishers<00:07:25.400><c> um</c><00:07:26.000><c> let's</c><00:07:26.280><c> see</c><00:07:27.280><c> let's</c><00:07:27.560><c> see</c><00:07:28.039><c> where</c>

00:07:31.189 --> 00:07:31.199 align:start position:0%
 
 

00:07:31.199 --> 00:07:35.230 align:start position:0%
 
the<00:07:32.199><c> let's</c><00:07:32.440><c> see</c><00:07:32.960><c> sum</c><00:07:33.599><c> of</c>

00:07:35.230 --> 00:07:35.240 align:start position:0%
the let's see sum of
 

00:07:35.240 --> 00:07:38.029 align:start position:0%
the let's see sum of
deposits<00:07:36.240><c> is</c><00:07:36.479><c> more</c>

00:07:38.029 --> 00:07:38.039 align:start position:0%
deposits is more
 

00:07:38.039 --> 00:07:41.830 align:start position:0%
deposits is more
than<00:07:39.039><c> is</c><00:07:39.240><c> greater</c><00:07:39.560><c> than</c><00:07:39.759><c> 10</c><00:07:40.199><c> let's</c><00:07:40.440><c> say</c>

00:07:41.830 --> 00:07:41.840 align:start position:0%
than is greater than 10 let's say
 

00:07:41.840 --> 00:07:44.749 align:start position:0%
than is greater than 10 let's say
okay<00:07:42.840><c> I</c><00:07:42.960><c> think</c><00:07:43.120><c> they'll</c><00:07:43.360><c> kind</c><00:07:43.479><c> of</c><00:07:43.720><c> get</c><00:07:44.000><c> get</c><00:07:44.240><c> the</c>

00:07:44.749 --> 00:07:44.759 align:start position:0%
okay I think they'll kind of get get the
 

00:07:44.759 --> 00:07:46.350 align:start position:0%
okay I think they'll kind of get get the
get<00:07:44.919><c> the</c><00:07:45.080><c> point</c><00:07:45.400><c> over</c><00:07:45.639><c> here</c><00:07:45.840><c> ah</c><00:07:46.000><c> no</c><00:07:46.120><c> there</c><00:07:46.240><c> were</c>

00:07:46.350 --> 00:07:46.360 align:start position:0%
get the point over here ah no there were
 

00:07:46.360 --> 00:07:48.550 align:start position:0%
get the point over here ah no there were
only<00:07:46.599><c> four</c><00:07:46.840><c> sources</c><00:07:47.400><c> with</c><00:07:47.560><c> that</c><00:07:47.879><c> that</c><00:07:48.039><c> many</c>

00:07:48.550 --> 00:07:48.560 align:start position:0%
only four sources with that that many
 

00:07:48.560 --> 00:07:50.790 align:start position:0%
only four sources with that that many
what<00:07:48.680><c> about</c><00:07:49.599><c> where</c><00:07:49.800><c> the</c><00:07:49.879><c> sum</c><00:07:50.080><c> of</c><00:07:50.199><c> deposits</c><00:07:50.639><c> is</c>

00:07:50.790 --> 00:07:50.800 align:start position:0%
what about where the sum of deposits is
 

00:07:50.800 --> 00:07:53.790 align:start position:0%
what about where the sum of deposits is
greater<00:07:51.120><c> than</c><00:07:51.400><c> five</c><00:07:52.360><c> okay</c><00:07:53.240><c> that's</c><00:07:53.479><c> a</c><00:07:53.599><c> little</c>

00:07:53.790 --> 00:07:53.800 align:start position:0%
greater than five okay that's a little
 

00:07:53.800 --> 00:07:56.469 align:start position:0%
greater than five okay that's a little
bit<00:07:54.319><c> better</c><00:07:55.319><c> greater</c><00:07:55.639><c> than</c>

00:07:56.469 --> 00:07:56.479 align:start position:0%
bit better greater than
 

00:07:56.479 --> 00:07:58.670 align:start position:0%
bit better greater than
three<00:07:57.479><c> the</c><00:07:57.599><c> whole</c><00:07:57.879><c> question</c><00:07:58.159><c> is</c><00:07:58.319><c> how</c><00:07:58.440><c> much</c>

00:07:58.670 --> 00:07:58.680 align:start position:0%
three the whole question is how much
 

00:07:58.680 --> 00:08:00.589 align:start position:0%
three the whole question is how much
this<00:07:58.879><c> specific</c><00:07:59.199><c> client</c><00:07:59.639><c> wants</c><00:07:59.840><c> to</c><00:08:00.000><c> scale</c><00:08:00.360><c> but</c>

00:08:00.589 --> 00:08:00.599 align:start position:0%
this specific client wants to scale but
 

00:08:00.599 --> 00:08:02.790 align:start position:0%
this specific client wants to scale but
okay<00:08:00.879><c> pretty</c><00:08:01.159><c> good</c><00:08:01.599><c> so</c><00:08:01.759><c> we</c><00:08:01.879><c> see</c><00:08:02.280><c> this</c><00:08:02.440><c> sub</c>

00:08:02.790 --> 00:08:02.800 align:start position:0%
okay pretty good so we see this sub
 

00:08:02.800 --> 00:08:06.189 align:start position:0%
okay pretty good so we see this sub
publisher<00:08:03.639><c> the</c><00:08:03.759><c> number</c><00:08:03.960><c> of</c><00:08:04.120><c> clicks</c><00:08:04.400><c> was</c><00:08:05.039><c> 47</c><00:08:06.039><c> uh</c>

00:08:06.189 --> 00:08:06.199 align:start position:0%
publisher the number of clicks was 47 uh
 

00:08:06.199 --> 00:08:07.670 align:start position:0%
publisher the number of clicks was 47 uh
you<00:08:06.280><c> know</c><00:08:06.440><c> what</c><00:08:06.639><c> we</c><00:08:06.840><c> probably</c><00:08:07.120><c> want</c><00:08:07.280><c> to</c><00:08:07.479><c> take</c>

00:08:07.670 --> 00:08:07.680 align:start position:0%
you know what we probably want to take
 

00:08:07.680 --> 00:08:11.110 align:start position:0%
you know what we probably want to take
our<00:08:08.199><c> registration</c><00:08:09.080><c> conversion</c><00:08:09.879><c> rate</c><00:08:10.879><c> and</c>

00:08:11.110 --> 00:08:11.120 align:start position:0%
our registration conversion rate and
 

00:08:11.120 --> 00:08:13.469 align:start position:0%
our registration conversion rate and
bring<00:08:11.440><c> it</c><00:08:11.759><c> all</c><00:08:11.919><c> the</c><00:08:12.039><c> way</c><00:08:12.280><c> over</c>

00:08:13.469 --> 00:08:13.479 align:start position:0%
bring it all the way over
 

00:08:13.479 --> 00:08:16.749 align:start position:0%
bring it all the way over
here<00:08:14.479><c> and</c><00:08:14.639><c> our</c><00:08:14.879><c> deposits</c><00:08:15.560><c> conversion</c>

00:08:16.749 --> 00:08:16.759 align:start position:0%
here and our deposits conversion
 

00:08:16.759 --> 00:08:21.510 align:start position:0%
here and our deposits conversion
rate<00:08:17.759><c> uh</c><00:08:18.039><c> nice</c><00:08:18.680><c> Okay</c><00:08:18.919><c> cool</c><00:08:20.000><c> so</c><00:08:21.000><c> uh</c><00:08:21.120><c> it</c><00:08:21.240><c> really</c>

00:08:21.510 --> 00:08:21.520 align:start position:0%
rate uh nice Okay cool so uh it really
 

00:08:21.520 --> 00:08:23.309 align:start position:0%
rate uh nice Okay cool so uh it really
depends<00:08:21.879><c> on</c><00:08:22.240><c> depends</c><00:08:22.520><c> on</c><00:08:22.680><c> the</c><00:08:22.800><c> client</c><00:08:23.080><c> and</c><00:08:23.240><c> the</c>

00:08:23.309 --> 00:08:23.319 align:start position:0%
depends on depends on the client and the
 

00:08:23.319 --> 00:08:25.510 align:start position:0%
depends on depends on the client and the
metrics<00:08:23.759><c> and</c><00:08:23.879><c> the</c><00:08:24.039><c> strategy</c><00:08:24.720><c> but</c><00:08:25.000><c> we</c><00:08:25.120><c> do</c><00:08:25.400><c> kind</c>

00:08:25.510 --> 00:08:25.520 align:start position:0%
metrics and the strategy but we do kind
 

00:08:25.520 --> 00:08:27.110 align:start position:0%
metrics and the strategy but we do kind
of<00:08:25.680><c> have</c><00:08:25.919><c> everything</c><00:08:26.319><c> kind</c><00:08:26.440><c> of</c><00:08:26.560><c> clean</c><00:08:26.919><c> over</c>

00:08:27.110 --> 00:08:27.120 align:start position:0%
of have everything kind of clean over
 

00:08:27.120 --> 00:08:29.670 align:start position:0%
of have everything kind of clean over
here<00:08:27.400><c> this</c><00:08:27.520><c> is</c><00:08:28.199><c> the</c><00:08:28.479><c> subp</c><00:08:28.879><c> publishers</c><00:08:29.520><c> that</c>

00:08:29.670 --> 00:08:29.680 align:start position:0%
here this is the subp publishers that
 

00:08:29.680 --> 00:08:32.829 align:start position:0%
here this is the subp publishers that
had<00:08:29.879><c> at</c><00:08:30.000><c> least</c><00:08:30.360><c> three</c><00:08:31.000><c> deposits</c><00:08:32.000><c> ordered</c><00:08:32.599><c> by</c>

00:08:32.829 --> 00:08:32.839 align:start position:0%
had at least three deposits ordered by
 

00:08:32.839 --> 00:08:35.469 align:start position:0%
had at least three deposits ordered by
the<00:08:33.000><c> best</c><00:08:33.279><c> conversion</c><00:08:33.959><c> rates</c><00:08:34.959><c> uh</c><00:08:35.159><c> the</c><00:08:35.279><c> best</c>

00:08:35.469 --> 00:08:35.479 align:start position:0%
the best conversion rates uh the best
 

00:08:35.479 --> 00:08:38.750 align:start position:0%
the best conversion rates uh the best
deposits<00:08:36.039><c> conversion</c><00:08:36.479><c> rates</c><00:08:36.800><c> so</c><00:08:37.240><c> this</c><00:08:37.760><c> Source</c>

00:08:38.750 --> 00:08:38.760 align:start position:0%
deposits conversion rates so this Source
 

00:08:38.760 --> 00:08:40.670 align:start position:0%
deposits conversion rates so this Source
uh<00:08:38.959><c> the</c><00:08:39.159><c> these</c><00:08:39.279><c> are</c><00:08:39.440><c> some</c><00:08:39.640><c> great</c><00:08:39.880><c> sources</c><00:08:40.360><c> over</c>

00:08:40.670 --> 00:08:40.680 align:start position:0%
uh the these are some great sources over
 

00:08:40.680 --> 00:08:42.589 align:start position:0%
uh the these are some great sources over
here<00:08:41.200><c> let's</c><00:08:41.440><c> just</c><00:08:41.800><c> let's</c><00:08:41.959><c> just</c><00:08:42.120><c> go</c><00:08:42.240><c> ahead</c><00:08:42.440><c> and</c>

00:08:42.589 --> 00:08:42.599 align:start position:0%
here let's just let's just go ahead and
 

00:08:42.599 --> 00:08:45.110 align:start position:0%
here let's just let's just go ahead and
wrap<00:08:42.839><c> up</c><00:08:43.039><c> question</c><00:08:43.320><c> 3D</c><00:08:44.120><c> see</c><00:08:44.360><c> anything</c>

00:08:45.110 --> 00:08:45.120 align:start position:0%
wrap up question 3D see anything
 

00:08:45.120 --> 00:08:46.389 align:start position:0%
wrap up question 3D see anything
anything<00:08:45.480><c> interesting</c><00:08:45.839><c> in</c><00:08:45.959><c> the</c><00:08:46.080><c> data</c><00:08:46.279><c> that</c>

00:08:46.389 --> 00:08:46.399 align:start position:0%
anything interesting in the data that
 

00:08:46.399 --> 00:08:49.110 align:start position:0%
anything interesting in the data that
can<00:08:46.519><c> help</c><00:08:46.640><c> you</c><00:08:47.080><c> understand</c><00:08:47.240><c> user</c><00:08:48.120><c> Behavior</c>

00:08:49.110 --> 00:08:49.120 align:start position:0%
can help you understand user Behavior
 

00:08:49.120 --> 00:08:53.110 align:start position:0%
can help you understand user Behavior
Focus<00:08:49.519><c> your</c><00:08:49.800><c> campaign</c><00:08:50.360><c> targeting</c><00:08:51.440><c> okay</c><00:08:52.440><c> um</c>

00:08:53.110 --> 00:08:53.120 align:start position:0%
Focus your campaign targeting okay um
 

00:08:53.120 --> 00:08:54.070 align:start position:0%
Focus your campaign targeting okay um
let's

00:08:54.070 --> 00:08:54.080 align:start position:0%
let's
 

00:08:54.080 --> 00:08:56.829 align:start position:0%
let's
see<00:08:55.080><c> we</c><00:08:55.200><c> might</c><00:08:55.399><c> be</c><00:08:55.519><c> able</c><00:08:55.680><c> to</c><00:08:55.800><c> run</c><00:08:56.000><c> an</c><00:08:56.200><c> analysis</c>

00:08:56.829 --> 00:08:56.839 align:start position:0%
see we might be able to run an analysis
 

00:08:56.839 --> 00:08:57.949 align:start position:0%
see we might be able to run an analysis
and<00:08:56.959><c> you</c><00:08:57.080><c> can</c><00:08:57.240><c> really</c><00:08:57.399><c> go</c><00:08:57.560><c> a</c><00:08:57.680><c> lot</c><00:08:57.800><c> of</c>

00:08:57.949 --> 00:08:57.959 align:start position:0%
and you can really go a lot of
 

00:08:57.959 --> 00:08:59.910 align:start position:0%
and you can really go a lot of
directions<00:08:58.440><c> with</c><00:08:58.640><c> this</c><00:08:58.880><c> question</c><00:08:59.519><c> and</c><00:08:59.680><c> be</c>

00:08:59.910 --> 00:08:59.920 align:start position:0%
directions with this question and be
 

00:08:59.920 --> 00:09:02.150 align:start position:0%
directions with this question and be
creative<00:09:00.440><c> with</c><00:09:00.600><c> it</c><00:09:00.920><c> but</c><00:09:01.480><c> we</c><00:09:01.600><c> can</c><00:09:01.760><c> run</c><00:09:01.959><c> an</c>

00:09:02.150 --> 00:09:02.160 align:start position:0%
creative with it but we can run an
 

00:09:02.160 --> 00:09:04.430 align:start position:0%
creative with it but we can run an
analysis<00:09:02.720><c> on</c><00:09:03.160><c> the</c><00:09:03.279><c> amount</c><00:09:03.440><c> of</c><00:09:03.640><c> registrations</c>

00:09:04.430 --> 00:09:04.440 align:start position:0%
analysis on the amount of registrations
 

00:09:04.440 --> 00:09:07.550 align:start position:0%
analysis on the amount of registrations
or<00:09:05.040><c> uh</c><00:09:05.360><c> the</c><00:09:05.519><c> amount</c><00:09:05.720><c> of</c><00:09:05.920><c> deposits</c><00:09:06.839><c> I</c><00:09:06.920><c> mean</c><00:09:07.399><c> if</c>

00:09:07.550 --> 00:09:07.560 align:start position:0%
or uh the amount of deposits I mean if
 

00:09:07.560 --> 00:09:09.430 align:start position:0%
or uh the amount of deposits I mean if
you<00:09:08.000><c> what</c><00:09:08.079><c> does</c><00:09:08.200><c> a</c><00:09:08.399><c> registration</c><00:09:09.000><c> really</c><00:09:09.200><c> mean</c>

00:09:09.430 --> 00:09:09.440 align:start position:0%
you what does a registration really mean
 

00:09:09.440 --> 00:09:10.990 align:start position:0%
you what does a registration really mean
does<00:09:09.560><c> it</c><00:09:09.680><c> mean</c><00:09:09.839><c> you</c><00:09:09.959><c> get</c><00:09:10.160><c> that</c><00:09:10.320><c> guy's</c><00:09:10.600><c> email</c>

00:09:10.990 --> 00:09:11.000 align:start position:0%
does it mean you get that guy's email
 

00:09:11.000 --> 00:09:13.269 align:start position:0%
does it mean you get that guy's email
can<00:09:11.160><c> you</c><00:09:11.279><c> put</c><00:09:11.440><c> them</c><00:09:11.600><c> into</c><00:09:12.320><c> an</c><00:09:12.519><c> email</c><00:09:12.839><c> marketing</c>

00:09:13.269 --> 00:09:13.279 align:start position:0%
can you put them into an email marketing
 

00:09:13.279 --> 00:09:15.870 align:start position:0%
can you put them into an email marketing
sequence<00:09:13.800><c> and</c><00:09:13.959><c> then</c><00:09:14.240><c> maybe</c><00:09:14.760><c> improve</c><00:09:15.760><c> the</c>

00:09:15.870 --> 00:09:15.880 align:start position:0%
sequence and then maybe improve the
 

00:09:15.880 --> 00:09:17.910 align:start position:0%
sequence and then maybe improve the
amount<00:09:16.120><c> of</c><00:09:16.320><c> deposits</c><00:09:16.920><c> down</c><00:09:17.120><c> the</c><00:09:17.320><c> line</c><00:09:17.760><c> there's</c>

00:09:17.910 --> 00:09:17.920 align:start position:0%
amount of deposits down the line there's
 

00:09:17.920 --> 00:09:19.630 align:start position:0%
amount of deposits down the line there's
a<00:09:18.000><c> lot</c><00:09:18.120><c> of</c><00:09:18.240><c> ways</c><00:09:18.399><c> that</c><00:09:18.560><c> you</c><00:09:18.640><c> can</c><00:09:18.839><c> take</c><00:09:19.000><c> it</c><00:09:19.279><c> but</c><00:09:19.560><c> I</c>

00:09:19.630 --> 00:09:19.640 align:start position:0%
a lot of ways that you can take it but I
 

00:09:19.640 --> 00:09:21.949 align:start position:0%
a lot of ways that you can take it but I
think<00:09:19.800><c> this</c><00:09:19.920><c> is</c><00:09:20.040><c> a</c><00:09:20.240><c> good</c><00:09:20.519><c> a</c><00:09:20.680><c> good</c><00:09:20.920><c> start</c><00:09:21.680><c> for</c>

00:09:21.949 --> 00:09:21.959 align:start position:0%
think this is a good a good start for
 

00:09:21.959 --> 00:09:25.110 align:start position:0%
think this is a good a good start for
that<00:09:22.200><c> type</c><00:09:22.360><c> of</c><00:09:22.560><c> analysis</c><00:09:23.120><c> of</c><00:09:23.440><c> question</c><00:09:24.120><c> three</c>

00:09:25.110 --> 00:09:25.120 align:start position:0%
that type of analysis of question three
 

00:09:25.120 --> 00:09:27.110 align:start position:0%
that type of analysis of question three
um<00:09:25.680><c> and</c><00:09:25.920><c> yeah</c><00:09:26.040><c> so</c><00:09:26.200><c> I</c><00:09:26.320><c> think</c><00:09:26.519><c> I</c><00:09:26.600><c> think</c><00:09:26.800><c> this</c><00:09:26.920><c> is</c>

00:09:27.110 --> 00:09:27.120 align:start position:0%
um and yeah so I think I think this is
 

00:09:27.120 --> 00:09:28.550 align:start position:0%
um and yeah so I think I think this is
great<00:09:27.320><c> so</c><00:09:27.480><c> we're</c><00:09:27.600><c> going</c><00:09:27.680><c> to</c><00:09:27.839><c> save</c><00:09:28.160><c> this</c><00:09:28.279><c> as</c><00:09:28.399><c> a</c>

00:09:28.550 --> 00:09:28.560 align:start position:0%
great so we're going to save this as a
 

00:09:28.560 --> 00:09:30.150 align:start position:0%
great so we're going to save this as a
new<00:09:28.920><c> question</c>

00:09:30.150 --> 00:09:30.160 align:start position:0%
new question
 

00:09:30.160 --> 00:09:33.269 align:start position:0%
new question
this<00:09:30.760><c> is</c>

00:09:33.269 --> 00:09:33.279 align:start position:0%
this is
 

00:09:33.279 --> 00:09:36.150 align:start position:0%
this is
okay<00:09:34.279><c> save</c><00:09:34.560><c> as</c><00:09:34.760><c> new</c><00:09:35.079><c> question</c><00:09:35.800><c> I'm</c><00:09:35.920><c> going</c><00:09:36.000><c> to</c>

00:09:36.150 --> 00:09:36.160 align:start position:0%
okay save as new question I'm going to
 

00:09:36.160 --> 00:09:39.269 align:start position:0%
okay save as new question I'm going to
save<00:09:36.360><c> it</c><00:09:36.560><c> as</c><00:09:37.320><c> highest</c>

00:09:39.269 --> 00:09:39.279 align:start position:0%
save it as highest
 

00:09:39.279 --> 00:09:43.350 align:start position:0%
save it as highest
converting<00:09:40.279><c> sub</c>

00:09:43.350 --> 00:09:43.360 align:start position:0%
 
 

00:09:43.360 --> 00:09:48.550 align:start position:0%
 
Publishers<00:09:44.360><c> with</c><00:09:45.120><c> at</c><00:09:45.640><c> least</c><00:09:46.640><c> three</c><00:09:47.560><c> deposits</c>

00:09:48.550 --> 00:09:48.560 align:start position:0%
Publishers with at least three deposits
 

00:09:48.560 --> 00:09:50.509 align:start position:0%
Publishers with at least three deposits
all<00:09:48.720><c> right</c><00:09:49.000><c> cool</c><00:09:49.560><c> and</c><00:09:49.720><c> we're</c><00:09:49.880><c> going</c><00:09:50.000><c> to</c><00:09:50.160><c> save</c>

00:09:50.509 --> 00:09:50.519 align:start position:0%
all right cool and we're going to save
 

00:09:50.519 --> 00:09:55.670 align:start position:0%
all right cool and we're going to save
that<00:09:50.760><c> as</c><00:09:51.600><c> qu</c>

00:09:55.670 --> 00:09:55.680 align:start position:0%
 
 

00:09:55.680 --> 00:09:59.790 align:start position:0%
 
Q3<00:09:56.680><c> nice</c><00:09:57.880><c> save</c><00:09:58.880><c> all</c><00:09:59.040><c> right</c><00:09:59.399><c> cool</c><00:09:59.600><c> and</c><00:09:59.720><c> I'm</c>

00:09:59.790 --> 00:09:59.800 align:start position:0%
Q3 nice save all right cool and I'm
 

00:09:59.800 --> 00:10:02.069 align:start position:0%
Q3 nice save all right cool and I'm
going<00:09:59.920><c> to</c><00:10:00.040><c> add</c><00:10:00.200><c> it</c><00:10:00.320><c> to</c><00:10:00.440><c> the</c><00:10:00.920><c> dashboard</c><00:10:01.920><c> uh</c>

00:10:02.069 --> 00:10:02.079 align:start position:0%
going to add it to the dashboard uh
 

00:10:02.079 --> 00:10:04.949 align:start position:0%
going to add it to the dashboard uh
start<00:10:02.440><c> app</c>

00:10:04.949 --> 00:10:04.959 align:start position:0%
 
 

00:10:04.959 --> 00:10:07.389 align:start position:0%
 
dashboard<00:10:05.959><c> all</c><00:10:06.120><c> right</c>

00:10:07.389 --> 00:10:07.399 align:start position:0%
dashboard all right
 

00:10:07.399 --> 00:10:10.069 align:start position:0%
dashboard all right
nice<00:10:08.399><c> and</c><00:10:08.600><c> the</c><00:10:08.760><c> nice</c><00:10:08.959><c> thing</c><00:10:09.160><c> is</c><00:10:09.399><c> once</c><00:10:09.640><c> it</c><00:10:09.839><c> once</c>

00:10:10.069 --> 00:10:10.079 align:start position:0%
nice and the nice thing is once it once
 

00:10:10.079 --> 00:10:13.269 align:start position:0%
nice and the nice thing is once it once
the<00:10:10.240><c> data</c><00:10:10.519><c> is</c><00:10:10.680><c> in</c><00:10:11.000><c> here</c><00:10:11.480><c> also</c><00:10:12.480><c> um</c><00:10:12.760><c> within</c><00:10:13.040><c> the</c>

00:10:13.269 --> 00:10:13.279 align:start position:0%
the data is in here also um within the
 

00:10:13.279 --> 00:10:15.710 align:start position:0%
the data is in here also um within the
dashboard<00:10:14.279><c> then</c><00:10:14.399><c> you</c><00:10:14.519><c> can</c><00:10:14.680><c> also</c><00:10:15.000><c> group</c><00:10:15.440><c> it</c>

00:10:15.710 --> 00:10:15.720 align:start position:0%
dashboard then you can also group it
 

00:10:15.720 --> 00:10:17.870 align:start position:0%
dashboard then you can also group it
according<00:10:16.040><c> to</c><00:10:16.279><c> each</c><00:10:16.600><c> column</c><00:10:16.959><c> so</c><00:10:17.160><c> let's</c><00:10:17.399><c> say</c>

00:10:17.870 --> 00:10:17.880 align:start position:0%
according to each column so let's say
 

00:10:17.880 --> 00:10:19.550 align:start position:0%
according to each column so let's say
right<00:10:18.040><c> you</c><00:10:18.200><c> get</c><00:10:18.320><c> to</c><00:10:18.480><c> the</c><00:10:18.600><c> interview</c><00:10:19.079><c> and</c><00:10:19.279><c> they</c>

00:10:19.550 --> 00:10:19.560 align:start position:0%
right you get to the interview and they
 

00:10:19.560 --> 00:10:21.870 align:start position:0%
right you get to the interview and they
they<00:10:19.680><c> ask</c><00:10:20.000><c> well</c><00:10:20.360><c> I</c><00:10:20.480><c> want</c><00:10:20.760><c> the</c><00:10:21.519><c> the</c><00:10:21.640><c> most</c>

00:10:21.870 --> 00:10:21.880 align:start position:0%
they ask well I want the the most
 

00:10:21.880 --> 00:10:24.590 align:start position:0%
they ask well I want the the most
important<00:10:22.360><c> kpi</c><00:10:23.079><c> is</c><00:10:23.399><c> is</c><00:10:23.560><c> the</c><00:10:23.680><c> amount</c><00:10:23.880><c> of</c><00:10:24.079><c> reg</c><00:10:24.440><c> is</c>

00:10:24.590 --> 00:10:24.600 align:start position:0%
important kpi is is the amount of reg is
 

00:10:24.600 --> 00:10:26.870 align:start position:0%
important kpi is is the amount of reg is
the<00:10:24.880><c> registrations</c><00:10:25.760><c> conversion</c><00:10:26.320><c> rate</c><00:10:26.720><c> can</c>

00:10:26.870 --> 00:10:26.880 align:start position:0%
the registrations conversion rate can
 

00:10:26.880 --> 00:10:28.670 align:start position:0%
the registrations conversion rate can
you<00:10:27.000><c> order</c><00:10:27.360><c> the</c><00:10:27.480><c> data</c><00:10:27.800><c> according</c><00:10:28.120><c> to</c><00:10:28.360><c> that</c>

00:10:28.670 --> 00:10:28.680 align:start position:0%
you order the data according to that
 

00:10:28.680 --> 00:10:30.269 align:start position:0%
you order the data according to that
yeah<00:10:28.839><c> no</c><00:10:29.040><c> problem</c><00:10:29.440><c> you</c><00:10:29.560><c> just</c><00:10:29.720><c> click</c><00:10:30.079><c> that</c>

00:10:30.269 --> 00:10:30.279 align:start position:0%
yeah no problem you just click that
 

00:10:30.279 --> 00:10:33.190 align:start position:0%
yeah no problem you just click that
title<00:10:30.800><c> there</c><00:10:31.519><c> twice</c><00:10:31.880><c> and</c><00:10:32.000><c> you</c><00:10:32.120><c> see</c><00:10:32.680><c> okay</c><00:10:32.920><c> now</c>

00:10:33.190 --> 00:10:33.200 align:start position:0%
title there twice and you see okay now
 

00:10:33.200 --> 00:10:35.110 align:start position:0%
title there twice and you see okay now
all<00:10:33.440><c> the</c><00:10:33.560><c> rows</c><00:10:34.000><c> are</c><00:10:34.200><c> ordered</c><00:10:34.680><c> according</c><00:10:34.959><c> to</c>

00:10:35.110 --> 00:10:35.120 align:start position:0%
all the rows are ordered according to
 

00:10:35.120 --> 00:10:37.150 align:start position:0%
all the rows are ordered according to
the<00:10:35.279><c> sub</c><00:10:35.639><c> Publishers</c><00:10:36.560><c> with</c><00:10:36.720><c> the</c><00:10:36.880><c> best</c>

00:10:37.150 --> 00:10:37.160 align:start position:0%
the sub Publishers with the best
 

00:10:37.160 --> 00:10:39.750 align:start position:0%
the sub Publishers with the best
registrations<00:10:38.160><c> conversion</c><00:10:38.760><c> rate</c><00:10:39.360><c> all</c><00:10:39.519><c> right</c>

00:10:39.750 --> 00:10:39.760 align:start position:0%
registrations conversion rate all right
 

00:10:39.760 --> 00:10:41.550 align:start position:0%
registrations conversion rate all right
guys<00:10:39.880><c> so</c><00:10:40.079><c> that</c><00:10:40.560><c> uh</c><00:10:40.680><c> hopefully</c><00:10:41.079><c> that</c><00:10:41.279><c> kind</c><00:10:41.399><c> of</c>

00:10:41.550 --> 00:10:41.560 align:start position:0%
guys so that uh hopefully that kind of
 

00:10:41.560 --> 00:10:44.910 align:start position:0%
guys so that uh hopefully that kind of
answers<00:10:41.959><c> that</c><00:10:42.360><c> question</c><00:10:43.360><c> and</c><00:10:44.000><c> uh</c><00:10:44.240><c> in</c><00:10:44.399><c> the</c><00:10:44.600><c> next</c>

00:10:44.910 --> 00:10:44.920 align:start position:0%
answers that question and uh in the next
 

00:10:44.920 --> 00:10:47.230 align:start position:0%
answers that question and uh in the next
video<00:10:45.560><c> we're</c><00:10:45.680><c> going</c><00:10:45.800><c> to</c><00:10:46.000><c> go</c><00:10:46.160><c> ahead</c><00:10:46.519><c> and</c><00:10:46.959><c> uh</c><00:10:47.120><c> and</c>

00:10:47.230 --> 00:10:47.240 align:start position:0%
video we're going to go ahead and uh and
 

00:10:47.240 --> 00:10:48.790 align:start position:0%
video we're going to go ahead and uh and
dig<00:10:47.440><c> into</c><00:10:47.720><c> the</c><00:10:47.839><c> rest</c><00:10:48.000><c> of</c><00:10:48.200><c> them</c><00:10:48.480><c> if</c><00:10:48.560><c> you</c><00:10:48.639><c> have</c>

00:10:48.790 --> 00:10:48.800 align:start position:0%
dig into the rest of them if you have
 

00:10:48.800 --> 00:10:50.470 align:start position:0%
dig into the rest of them if you have
any<00:10:49.000><c> questions</c><00:10:49.320><c> feel</c><00:10:49.560><c> free</c><00:10:49.760><c> to</c><00:10:49.880><c> reach</c><00:10:50.079><c> out</c><00:10:50.360><c> and</c>

00:10:50.470 --> 00:10:50.480 align:start position:0%
any questions feel free to reach out and
 

00:10:50.480 --> 00:10:54.560 align:start position:0%
any questions feel free to reach out and
I'll<00:10:50.639><c> see</c><00:10:50.839><c> you</c><00:10:51.040><c> in</c><00:10:51.160><c> the</c><00:10:51.360><c> next</c><00:10:51.560><c> one</c>

