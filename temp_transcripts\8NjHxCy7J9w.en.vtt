WEBVTT
Kind: captions
Language: en

00:00:00.640 --> 00:00:02.350 align:start position:0%
 
hey<00:00:00.799><c> everyone</c><00:00:01.160><c> welcome</c><00:00:01.439><c> to</c><00:00:01.640><c> part</c><00:00:01.839><c> two</c><00:00:02.080><c> of</c><00:00:02.200><c> the</c>

00:00:02.350 --> 00:00:02.360 align:start position:0%
hey everyone welcome to part two of the
 

00:00:02.360 --> 00:00:05.230 align:start position:0%
hey everyone welcome to part two of the
local<00:00:02.679><c> agents</c><00:00:03.080><c> R</c><00:00:03.480><c> where</c><00:00:03.679><c> now</c><00:00:03.879><c> we'll</c><00:00:04.160><c> use</c><00:00:05.000><c> the</c>

00:00:05.230 --> 00:00:05.240 align:start position:0%
local agents R where now we'll use the
 

00:00:05.240 --> 00:00:06.269 align:start position:0%
local agents R where now we'll use the
Llama

00:00:06.269 --> 00:00:06.279 align:start position:0%
Llama
 

00:00:06.279 --> 00:00:08.830 align:start position:0%
Llama
3.18<00:00:07.279><c> billion</c><00:00:07.600><c> parameter</c><00:00:08.080><c> model</c><00:00:08.440><c> instead</c><00:00:08.719><c> of</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
3.18 billion parameter model instead of
 

00:00:08.840 --> 00:00:11.150 align:start position:0%
3.18 billion parameter model instead of
the<00:00:08.920><c> Llama</c><00:00:09.240><c> 3.2</c><00:00:10.200><c> and</c><00:00:10.360><c> hopefully</c><00:00:10.679><c> see</c><00:00:10.920><c> better</c>

00:00:11.150 --> 00:00:11.160 align:start position:0%
the Llama 3.2 and hopefully see better
 

00:00:11.160 --> 00:00:13.070 align:start position:0%
the Llama 3.2 and hopefully see better
results<00:00:11.920><c> uh</c><00:00:12.040><c> let's</c><00:00:12.240><c> dive</c><00:00:12.480><c> straight</c><00:00:12.719><c> into</c><00:00:12.920><c> it</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
results uh let's dive straight into it
 

00:00:13.080 --> 00:00:15.510 align:start position:0%
results uh let's dive straight into it
the<00:00:13.240><c> code</c><00:00:13.559><c> is</c><00:00:13.839><c> under</c><00:00:14.120><c> the</c><00:00:14.240><c> F</c><00:00:14.519><c> data</c><00:00:14.719><c> repo</c><00:00:15.120><c> under</c>

00:00:15.510 --> 00:00:15.520 align:start position:0%
the code is under the F data repo under
 

00:00:15.520 --> 00:00:18.470 align:start position:0%
the code is under the F data repo under
cookbook<00:00:16.520><c> uh</c><00:00:16.680><c> playground</c><00:00:17.520><c> and</c><00:00:18.000><c> uh</c><00:00:18.240><c> with</c><00:00:18.359><c> the</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
cookbook uh playground and uh with the
 

00:00:18.480 --> 00:00:20.950 align:start position:0%
cookbook uh playground and uh with the
readme<00:00:18.880><c> you'll</c><00:00:19.359><c> find</c><00:00:19.600><c> all</c><00:00:19.760><c> the</c><00:00:19.960><c> instructions</c>

00:00:20.950 --> 00:00:20.960 align:start position:0%
readme you'll find all the instructions
 

00:00:20.960 --> 00:00:23.269 align:start position:0%
readme you'll find all the instructions
right<00:00:21.320><c> here</c><00:00:22.279><c> and</c><00:00:22.560><c> the</c><00:00:22.720><c> code</c><00:00:22.960><c> for</c><00:00:23.119><c> this</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
right here and the code for this
 

00:00:23.279 --> 00:00:25.670 align:start position:0%
right here and the code for this
application<00:00:23.720><c> is</c><00:00:23.880><c> under</c><00:00:24.160><c> AMA</c><00:00:24.560><c> agents</c><00:00:25.199><c> for</c><00:00:25.519><c> can</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
application is under AMA agents for can
 

00:00:25.680 --> 00:00:27.429 align:start position:0%
application is under AMA agents for can
clone<00:00:25.920><c> the</c><00:00:26.000><c> repo</c><00:00:26.320><c> and</c><00:00:26.439><c> open</c><00:00:26.840><c> open</c><00:00:27.000><c> it</c><00:00:27.119><c> up</c><00:00:27.279><c> in</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
clone the repo and open open it up in
 

00:00:27.439 --> 00:00:29.950 align:start position:0%
clone the repo and open open it up in
the<00:00:27.679><c> in</c><00:00:27.840><c> your</c><00:00:28.039><c> favorite</c><00:00:28.599><c> uh</c><00:00:28.720><c> code</c><00:00:29.000><c> editor</c><00:00:29.800><c> it</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
the in your favorite uh code editor it
 

00:00:29.960 --> 00:00:31.910 align:start position:0%
the in your favorite uh code editor it
it's<00:00:30.080><c> under</c><00:00:30.359><c> cookbooks</c><00:00:31.119><c> playground</c><00:00:31.720><c> and</c><00:00:31.840><c> the</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
it's under cookbooks playground and the
 

00:00:31.920 --> 00:00:34.709 align:start position:0%
it's under cookbooks playground and the
readme<00:00:32.360><c> file</c><00:00:33.320><c> uh</c><00:00:33.960><c> and</c><00:00:34.160><c> first</c><00:00:34.360><c> we're</c><00:00:34.520><c> going</c><00:00:34.600><c> to</c>

00:00:34.709 --> 00:00:34.719 align:start position:0%
readme file uh and first we're going to
 

00:00:34.719 --> 00:00:36.229 align:start position:0%
readme file uh and first we're going to
do<00:00:34.920><c> is</c><00:00:35.040><c> we're</c><00:00:35.160><c> going</c><00:00:35.280><c> to</c><00:00:35.399><c> pull</c><00:00:35.600><c> the</c><00:00:35.719><c> 3.1</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
do is we're going to pull the 3.1
 

00:00:36.239 --> 00:00:37.549 align:start position:0%
do is we're going to pull the 3.1
billion<00:00:36.480><c> parameter</c><00:00:36.879><c> model</c><00:00:37.120><c> open</c><00:00:37.320><c> up</c><00:00:37.440><c> your</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
billion parameter model open up your
 

00:00:37.559 --> 00:00:40.029 align:start position:0%
billion parameter model open up your
terminal<00:00:38.559><c> in</c><00:00:38.680><c> a</c><00:00:38.879><c> separate</c><00:00:39.280><c> tab</c><00:00:39.719><c> pull</c><00:00:39.960><c> the</c>

00:00:40.029 --> 00:00:40.039 align:start position:0%
terminal in a separate tab pull the
 

00:00:40.039 --> 00:00:41.510 align:start position:0%
terminal in a separate tab pull the
model<00:00:40.360><c> I've</c><00:00:40.480><c> just</c><00:00:40.600><c> pulled</c><00:00:40.840><c> it</c><00:00:40.920><c> it's</c><00:00:41.039><c> about</c><00:00:41.239><c> 4</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
model I've just pulled it it's about 4
 

00:00:41.520 --> 00:00:44.229 align:start position:0%
model I've just pulled it it's about 4
gig<00:00:41.800><c> so</c><00:00:42.200><c> uh</c><00:00:42.320><c> give</c><00:00:42.440><c> it</c><00:00:42.520><c> a</c><00:00:42.640><c> bit</c><00:00:42.760><c> of</c><00:00:42.920><c> time</c><00:00:43.760><c> uh</c><00:00:44.000><c> then</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
gig so uh give it a bit of time uh then
 

00:00:44.239 --> 00:00:45.750 align:start position:0%
gig so uh give it a bit of time uh then
install<00:00:44.600><c> the</c><00:00:44.719><c> libraries</c><00:00:45.320><c> which</c><00:00:45.440><c> we're</c><00:00:45.600><c> going</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
install the libraries which we're going
 

00:00:45.760 --> 00:00:47.470 align:start position:0%
install the libraries which we're going
to<00:00:45.960><c> skip</c><00:00:46.280><c> we're</c><00:00:46.399><c> going</c><00:00:46.480><c> to</c><00:00:46.600><c> skip</c><00:00:46.879><c> the</c><00:00:46.960><c> fiot</c>

00:00:47.470 --> 00:00:47.480 align:start position:0%
to skip we're going to skip the fiot
 

00:00:47.480 --> 00:00:50.590 align:start position:0%
to skip we're going to skip the fiot
step<00:00:47.800><c> as</c><00:00:47.960><c> well</c><00:00:48.520><c> uh</c><00:00:49.039><c> and</c><00:00:49.280><c> straight</c><00:00:49.680><c> away</c><00:00:50.039><c> open</c>

00:00:50.590 --> 00:00:50.600 align:start position:0%
step as well uh and straight away open
 

00:00:50.600 --> 00:00:53.990 align:start position:0%
step as well uh and straight away open
uh<00:00:50.760><c> run</c><00:00:51.039><c> the</c><00:00:51.160><c> AMA</c><00:00:51.640><c> agents</c><00:00:52.320><c> file</c><00:00:53.320><c> uh</c><00:00:53.559><c> this</c><00:00:53.719><c> time</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
uh run the AMA agents file uh this time
 

00:00:54.000 --> 00:00:56.029 align:start position:0%
uh run the AMA agents file uh this time
we're<00:00:54.160><c> going</c><00:00:54.239><c> to</c><00:00:54.359><c> use</c><00:00:54.559><c> the</c><00:00:54.719><c> Llama</c><00:00:55.120><c> 3.1</c><00:00:55.760><c> 8</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
we're going to use the Llama 3.1 8
 

00:00:56.039 --> 00:00:58.150 align:start position:0%
we're going to use the Llama 3.1 8
billion<00:00:56.359><c> parameter</c><00:00:56.760><c> model</c><00:00:57.559><c> I've</c><00:00:57.800><c> also</c>

00:00:58.150 --> 00:00:58.160 align:start position:0%
billion parameter model I've also
 

00:00:58.160 --> 00:01:00.029 align:start position:0%
billion parameter model I've also
removed<00:00:58.640><c> one</c><00:00:58.760><c> of</c><00:00:58.920><c> the</c><00:00:59.079><c> instructions</c><00:00:59.640><c> because</c>

00:01:00.029 --> 00:01:00.039 align:start position:0%
removed one of the instructions because
 

00:01:00.039 --> 00:01:02.189 align:start position:0%
removed one of the instructions because
we<00:01:00.160><c> don't</c><00:01:00.399><c> need</c><00:01:00.680><c> it</c><00:01:01.039><c> with</c><00:01:01.239><c> this</c><00:01:01.399><c> model</c>

00:01:02.189 --> 00:01:02.199 align:start position:0%
we don't need it with this model
 

00:01:02.199 --> 00:01:03.869 align:start position:0%
we don't need it with this model
hopefully<00:01:02.559><c> we'll</c><00:01:02.760><c> see</c><00:01:02.960><c> better</c><00:01:03.239><c> results</c><00:01:03.719><c> uh</c>

00:01:03.869 --> 00:01:03.879 align:start position:0%
hopefully we'll see better results uh
 

00:01:03.879 --> 00:01:06.469 align:start position:0%
hopefully we'll see better results uh
let's<00:01:04.320><c> uh</c><00:01:04.439><c> let's</c><00:01:04.640><c> run</c><00:01:04.839><c> it</c><00:01:05.000><c> up</c><00:01:05.519><c> and</c><00:01:05.920><c> by</c><00:01:06.080><c> the</c><00:01:06.200><c> way</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
let's uh let's run it up and by the way
 

00:01:06.479 --> 00:01:08.830 align:start position:0%
let's uh let's run it up and by the way
I<00:01:06.600><c> haven't</c><00:01:06.920><c> ran</c><00:01:07.360><c> any</c><00:01:07.600><c> tests</c><00:01:08.000><c> with</c><00:01:08.200><c> this</c><00:01:08.400><c> yet</c>

00:01:08.830 --> 00:01:08.840 align:start position:0%
I haven't ran any tests with this yet
 

00:01:08.840 --> 00:01:10.190 align:start position:0%
I haven't ran any tests with this yet
this<00:01:08.960><c> is</c><00:01:09.119><c> not</c><00:01:09.400><c> pre-planned</c><00:01:09.840><c> we're</c><00:01:10.040><c> going</c><00:01:10.119><c> to</c>

00:01:10.190 --> 00:01:10.200 align:start position:0%
this is not pre-planned we're going to
 

00:01:10.200 --> 00:01:12.030 align:start position:0%
this is not pre-planned we're going to
do<00:01:10.360><c> our</c><00:01:10.600><c> 10</c><00:01:10.880><c> questions</c><00:01:11.280><c> see</c><00:01:11.520><c> how</c><00:01:11.680><c> it</c><00:01:11.799><c> is</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
do our 10 questions see how it is
 

00:01:12.040 --> 00:01:15.270 align:start position:0%
do our 10 questions see how it is
completely<00:01:12.479><c> raw</c><00:01:13.000><c> unedited</c><00:01:14.000><c> and</c><00:01:14.920><c> I</c><00:01:15.000><c> just</c><00:01:15.159><c> want</c>

00:01:15.270 --> 00:01:15.280 align:start position:0%
completely raw unedited and I just want
 

00:01:15.280 --> 00:01:17.630 align:start position:0%
completely raw unedited and I just want
to<00:01:15.439><c> get</c><00:01:15.560><c> in</c><00:01:15.680><c> the</c><00:01:15.960><c> habit</c><00:01:16.360><c> of</c><00:01:17.040><c> talking</c><00:01:17.360><c> about</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
to get in the habit of talking about
 

00:01:17.640 --> 00:01:20.230 align:start position:0%
to get in the habit of talking about
things<00:01:18.080><c> as</c><00:01:18.280><c> they</c><00:01:18.400><c> are</c><00:01:19.400><c> right</c><00:01:19.560><c> so</c><00:01:19.840><c> okay</c><00:01:20.000><c> so</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
things as they are right so okay so
 

00:01:20.240 --> 00:01:22.069 align:start position:0%
things as they are right so okay so
let's<00:01:20.600><c> so</c><00:01:20.840><c> this</c><00:01:20.920><c> is</c><00:01:21.040><c> the</c><00:01:21.200><c> web</c>

00:01:22.069 --> 00:01:22.079 align:start position:0%
let's so this is the web
 

00:01:22.079 --> 00:01:26.190 align:start position:0%
let's so this is the web
agent<00:01:23.079><c> it's</c><00:01:23.400><c> got</c><00:01:24.240><c> ducko</c><00:01:24.920><c> as</c><00:01:25.000><c> a</c><00:01:25.119><c> toolkit</c><00:01:26.000><c> couple</c>

00:01:26.190 --> 00:01:26.200 align:start position:0%
agent it's got ducko as a toolkit couple
 

00:01:26.200 --> 00:01:28.270 align:start position:0%
agent it's got ducko as a toolkit couple
of<00:01:26.360><c> instructions</c><00:01:27.240><c> uh</c><00:01:27.360><c> it</c><00:01:27.439><c> sends</c><00:01:27.840><c> messages</c>

00:01:28.270 --> 00:01:28.280 align:start position:0%
of instructions uh it sends messages
 

00:01:28.280 --> 00:01:29.390 align:start position:0%
of instructions uh it sends messages
back<00:01:28.400><c> to</c><00:01:28.560><c> it</c><00:01:28.640><c> it's</c><00:01:28.759><c> called</c><00:01:28.960><c> markdown</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
back to it it's called markdown
 

00:01:29.400 --> 00:01:30.710 align:start position:0%
back to it it's called markdown
instructions<00:01:29.920><c> and</c><00:01:30.000><c> we</c><00:01:30.119><c> give</c><00:01:30.200><c> it</c><00:01:30.320><c> the</c><00:01:30.439><c> current</c>

00:01:30.710 --> 00:01:30.720 align:start position:0%
instructions and we give it the current
 

00:01:30.720 --> 00:01:34.149 align:start position:0%
instructions and we give it the current
date<00:01:30.880><c> and</c><00:01:31.119><c> time</c><00:01:31.799><c> uh</c><00:01:31.920><c> we</c><00:01:32.079><c> also</c><00:01:32.479><c> add</c><00:01:32.799><c> the</c><00:01:33.079><c> name</c><00:01:33.960><c> to</c>

00:01:34.149 --> 00:01:34.159 align:start position:0%
date and time uh we also add the name to
 

00:01:34.159 --> 00:01:35.749 align:start position:0%
date and time uh we also add the name to
the<00:01:34.320><c> instructions</c><00:01:34.920><c> just</c><00:01:35.040><c> to</c><00:01:35.200><c> help</c><00:01:35.360><c> the</c><00:01:35.479><c> model</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
the instructions just to help the model
 

00:01:35.759 --> 00:01:37.510 align:start position:0%
the instructions just to help the model
respond<00:01:36.119><c> better</c><00:01:36.640><c> then</c><00:01:36.759><c> we</c><00:01:36.880><c> have</c><00:01:36.960><c> a</c><00:01:37.079><c> financial</c>

00:01:37.510 --> 00:01:37.520 align:start position:0%
respond better then we have a financial
 

00:01:37.520 --> 00:01:39.469 align:start position:0%
respond better then we have a financial
agent<00:01:37.920><c> with</c><00:01:38.119><c> Yahoo</c><00:01:38.439><c> finance</c><00:01:38.799><c> as</c><00:01:38.920><c> a</c><00:01:39.040><c> toolkit</c>

00:01:39.469 --> 00:01:39.479 align:start position:0%
agent with Yahoo finance as a toolkit
 

00:01:39.479 --> 00:01:41.030 align:start position:0%
agent with Yahoo finance as a toolkit
and<00:01:39.600><c> a</c><00:01:39.720><c> YouTube</c><00:01:40.040><c> agent</c><00:01:40.320><c> with</c><00:01:40.479><c> YouTube</c><00:01:40.759><c> tools</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
and a YouTube agent with YouTube tools
 

00:01:41.040 --> 00:01:43.149 align:start position:0%
and a YouTube agent with YouTube tools
as<00:01:41.119><c> a</c><00:01:41.240><c> toolkit</c><00:01:42.079><c> uh</c><00:01:42.200><c> let's</c><00:01:42.399><c> get</c><00:01:42.520><c> into</c><00:01:42.759><c> it</c><00:01:42.880><c> so</c>

00:01:43.149 --> 00:01:43.159 align:start position:0%
as a toolkit uh let's get into it so
 

00:01:43.159 --> 00:01:44.469 align:start position:0%
as a toolkit uh let's get into it so
what<00:01:43.240><c> first</c><00:01:43.439><c> we</c><00:01:43.680><c> l</c><00:01:43.880><c> what's</c><00:01:44.040><c> your</c><00:01:44.159><c> special</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
what first we l what's your special
 

00:01:44.479 --> 00:01:46.310 align:start position:0%
what first we l what's your special
skill<00:01:45.079><c> hopefully</c><00:01:45.360><c> it</c><00:01:45.479><c> gives</c><00:01:45.640><c> a</c><00:01:45.759><c> right</c><00:01:45.960><c> answer</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
skill hopefully it gives a right answer
 

00:01:46.320 --> 00:01:48.510 align:start position:0%
skill hopefully it gives a right answer
three<00:01:46.719><c> billion</c><00:01:47.040><c> parameter</c><00:01:47.399><c> model</c><00:01:47.960><c> uh</c><00:01:48.159><c> just</c>

00:01:48.510 --> 00:01:48.520 align:start position:0%
three billion parameter model uh just
 

00:01:48.520 --> 00:01:51.069 align:start position:0%
three billion parameter model uh just
runs<00:01:48.840><c> a</c><00:01:49.439><c> tool</c>

00:01:51.069 --> 00:01:51.079 align:start position:0%
runs a tool
 

00:01:51.079 --> 00:01:54.550 align:start position:0%
runs a tool
so<00:01:52.079><c> this</c><00:01:52.200><c> one</c><00:01:52.399><c> also</c><00:01:52.600><c> ran</c><00:01:52.840><c> a</c>

00:01:54.550 --> 00:01:54.560 align:start position:0%
so this one also ran a
 

00:01:54.560 --> 00:01:58.190 align:start position:0%
so this one also ran a
tool<00:01:55.560><c> okay</c><00:01:56.159><c> not</c><00:01:56.439><c> what</c><00:01:56.560><c> I'm</c><00:01:56.759><c> looking</c><00:01:57.039><c> for</c><00:01:57.719><c> but</c>

00:01:58.190 --> 00:01:58.200 align:start position:0%
tool okay not what I'm looking for but
 

00:01:58.200 --> 00:02:01.630 align:start position:0%
tool okay not what I'm looking for but
that's<00:01:58.600><c> okay</c><00:02:00.039><c> tell</c><00:02:00.240><c> me</c><00:02:00.399><c> about</c>

00:02:01.630 --> 00:02:01.640 align:start position:0%
that's okay tell me about
 

00:02:01.640 --> 00:02:05.149 align:start position:0%
that's okay tell me about
yourself<00:02:02.640><c> it's</c><00:02:03.600><c> going</c><00:02:04.200><c> into</c><00:02:04.680><c> some</c><00:02:04.840><c> sort</c><00:02:05.039><c> of</c>

00:02:05.149 --> 00:02:05.159 align:start position:0%
yourself it's going into some sort of
 

00:02:05.159 --> 00:02:08.309 align:start position:0%
yourself it's going into some sort of
Chain<00:02:05.399><c> of</c><00:02:05.759><c> Thought</c><00:02:06.759><c> maybe</c><00:02:07.200><c> because</c><00:02:07.600><c> we've</c><00:02:08.039><c> ask</c>

00:02:08.309 --> 00:02:08.319 align:start position:0%
Chain of Thought maybe because we've ask
 

00:02:08.319 --> 00:02:09.710 align:start position:0%
Chain of Thought maybe because we've ask
if<00:02:08.399><c> you</c><00:02:08.520><c> need</c><00:02:08.640><c> to</c><00:02:08.759><c> search</c><00:02:09.039><c> the</c><00:02:09.160><c> web</c><00:02:09.319><c> break</c><00:02:09.599><c> down</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
if you need to search the web break down
 

00:02:09.720 --> 00:02:12.949 align:start position:0%
if you need to search the web break down
the<00:02:09.840><c> user</c><00:02:10.200><c> request</c><00:02:10.959><c> so</c><00:02:11.319><c> again</c><00:02:12.120><c> when</c><00:02:12.280><c> you</c><00:02:12.480><c> ask</c>

00:02:12.949 --> 00:02:12.959 align:start position:0%
the user request so again when you ask
 

00:02:12.959 --> 00:02:15.390 align:start position:0%
the user request so again when you ask
conversational<00:02:13.640><c> questions</c><00:02:14.360><c> not</c><00:02:14.879><c> really</c><00:02:15.200><c> the</c>

00:02:15.390 --> 00:02:15.400 align:start position:0%
conversational questions not really the
 

00:02:15.400 --> 00:02:17.990 align:start position:0%
conversational questions not really the
best<00:02:15.959><c> maybe</c><00:02:16.239><c> we</c><00:02:16.400><c> need</c><00:02:16.560><c> to</c><00:02:17.120><c> prompt</c><00:02:17.519><c> the</c><00:02:17.640><c> agents</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
best maybe we need to prompt the agents
 

00:02:18.000 --> 00:02:22.190 align:start position:0%
best maybe we need to prompt the agents
better<00:02:18.280><c> but</c><00:02:18.480><c> let's</c><00:02:18.760><c> ask</c>

00:02:22.190 --> 00:02:22.200 align:start position:0%
 
 

00:02:22.200 --> 00:02:26.110 align:start position:0%
 
um<00:02:23.200><c> tell</c><00:02:23.400><c> me</c><00:02:23.640><c> about</c><00:02:24.080><c> the</c><00:02:24.400><c> US</c><00:02:25.400><c> elections</c><00:02:25.959><c> and</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
um tell me about the US elections and
 

00:02:26.120 --> 00:02:31.790 align:start position:0%
um tell me about the US elections and
let's<00:02:26.280><c> see</c><00:02:26.440><c> what</c><00:02:26.560><c> it</c><00:02:26.720><c> does</c>

00:02:31.790 --> 00:02:31.800 align:start position:0%
 
 

00:02:31.800 --> 00:02:34.430 align:start position:0%
 
okay<00:02:31.959><c> it's</c><00:02:32.160><c> breaking</c><00:02:32.640><c> everything</c><00:02:33.080><c> down</c><00:02:34.080><c> doing</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
okay it's breaking everything down doing
 

00:02:34.440 --> 00:02:35.670 align:start position:0%
okay it's breaking everything down doing
three

00:02:35.670 --> 00:02:35.680 align:start position:0%
three
 

00:02:35.680 --> 00:02:37.910 align:start position:0%
three
queries<00:02:36.680><c> I</c><00:02:36.800><c> think</c><00:02:36.920><c> the</c><00:02:37.120><c> 70</c><00:02:37.400><c> billion</c><00:02:37.599><c> parameter</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
queries I think the 70 billion parameter
 

00:02:37.920 --> 00:02:40.509 align:start position:0%
queries I think the 70 billion parameter
of<00:02:38.000><c> the</c><00:02:38.080><c> 4o5</c><00:02:38.959><c> it</c><00:02:39.080><c> would</c><00:02:39.440><c> actually</c><00:02:39.920><c> work</c><00:02:40.280><c> really</c>

00:02:40.509 --> 00:02:40.519 align:start position:0%
of the 4o5 it would actually work really
 

00:02:40.519 --> 00:02:42.270 align:start position:0%
of the 4o5 it would actually work really
really<00:02:40.720><c> well</c><00:02:40.959><c> I'm</c><00:02:41.120><c> excited</c><00:02:41.400><c> to</c><00:02:41.519><c> try</c><00:02:41.720><c> out</c><00:02:42.000><c> the</c>

00:02:42.270 --> 00:02:42.280 align:start position:0%
really well I'm excited to try out the
 

00:02:42.280 --> 00:02:46.869 align:start position:0%
really well I'm excited to try out the
new<00:02:43.040><c> um</c><00:02:44.040><c> Nvidia</c><00:02:45.040><c> model</c><00:02:45.440><c> as</c>

00:02:46.869 --> 00:02:46.879 align:start position:0%
new um Nvidia model as
 

00:02:46.879 --> 00:02:49.670 align:start position:0%
new um Nvidia model as
well<00:02:47.879><c> okay</c><00:02:48.239><c> not</c><00:02:48.480><c> bad</c><00:02:49.040><c> uh</c><00:02:49.159><c> let's</c><00:02:49.319><c> go</c><00:02:49.440><c> to</c><00:02:49.560><c> the</c>

00:02:49.670 --> 00:02:49.680 align:start position:0%
well okay not bad uh let's go to the
 

00:02:49.680 --> 00:02:52.110 align:start position:0%
well okay not bad uh let's go to the
finance<00:02:50.080><c> agent</c><00:02:50.640><c> and</c><00:02:50.920><c> we'll</c>

00:02:52.110 --> 00:02:52.120 align:start position:0%
finance agent and we'll
 

00:02:52.120 --> 00:02:56.030 align:start position:0%
finance agent and we'll
ask<00:02:53.120><c> compared</c><00:02:53.519><c> Tesla</c><00:02:53.920><c> to</c><00:02:54.120><c> Nvidia</c><00:02:54.599><c> for</c><00:02:54.959><c> me</c><00:02:55.959><c> I</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
ask compared Tesla to Nvidia for me I
 

00:02:56.040 --> 00:03:08.990 align:start position:0%
ask compared Tesla to Nvidia for me I
just<00:02:56.159><c> want</c><00:02:56.280><c> to</c><00:02:56.400><c> see</c><00:02:56.640><c> what</c><00:02:56.840><c> happens</c>

00:03:08.990 --> 00:03:09.000 align:start position:0%
 
 

00:03:09.000 --> 00:03:10.789 align:start position:0%
 
okay<00:03:09.159><c> it</c><00:03:09.319><c> ran</c><00:03:09.560><c> it</c><00:03:09.760><c> already</c><00:03:10.120><c> I</c><00:03:10.239><c> thought</c><00:03:10.519><c> let</c><00:03:10.680><c> me</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
okay it ran it already I thought let me
 

00:03:10.799 --> 00:03:11.910 align:start position:0%
okay it ran it already I thought let me
know<00:03:10.920><c> if</c><00:03:11.000><c> you'd</c><00:03:11.159><c> like</c><00:03:11.280><c> to</c><00:03:11.400><c> proceed</c><00:03:11.680><c> but</c><00:03:11.799><c> then</c>

00:03:11.910 --> 00:03:11.920 align:start position:0%
know if you'd like to proceed but then
 

00:03:11.920 --> 00:03:14.910 align:start position:0%
know if you'd like to proceed but then
it<00:03:12.040><c> ran</c><00:03:12.959><c> gave</c><00:03:13.400><c> ran</c><00:03:13.640><c> the</c><00:03:13.799><c> right</c><00:03:13.920><c> tool</c><00:03:14.200><c> calls</c><00:03:14.760><c> uh</c>

00:03:14.910 --> 00:03:14.920 align:start position:0%
it ran gave ran the right tool calls uh
 

00:03:14.920 --> 00:03:17.149 align:start position:0%
it ran gave ran the right tool calls uh
rendered<00:03:15.400><c> it</c><00:03:15.519><c> in</c><00:03:15.680><c> a</c><00:03:15.920><c> nice</c><00:03:16.159><c> markdown</c><00:03:16.720><c> table</c>

00:03:17.149 --> 00:03:17.159 align:start position:0%
rendered it in a nice markdown table
 

00:03:17.159 --> 00:03:18.910 align:start position:0%
rendered it in a nice markdown table
which<00:03:17.480><c> is</c><00:03:17.680><c> comes</c><00:03:17.920><c> on</c><00:03:18.040><c> the</c><00:03:18.159><c> agent</c><00:03:18.440><c> UI</c><00:03:18.720><c> looking</c>

00:03:18.910 --> 00:03:18.920 align:start position:0%
which is comes on the agent UI looking
 

00:03:18.920 --> 00:03:21.869 align:start position:0%
which is comes on the agent UI looking
very<00:03:19.159><c> beautiful</c><00:03:19.760><c> let's</c><00:03:19.879><c> see</c><00:03:20.120><c> what</c><00:03:20.440><c> a</c><00:03:21.000><c> um</c><00:03:21.680><c> by</c>

00:03:21.869 --> 00:03:21.879 align:start position:0%
very beautiful let's see what a um by
 

00:03:21.879 --> 00:03:24.309 align:start position:0%
very beautiful let's see what a um by
way<00:03:22.000><c> I</c><00:03:22.080><c> want</c><00:03:22.200><c> to</c><00:03:22.360><c> see</c><00:03:23.080><c> so</c><00:03:23.239><c> you</c><00:03:23.319><c> can</c><00:03:23.760><c> uh</c><00:03:23.959><c> go</c><00:03:24.120><c> to</c>

00:03:24.309 --> 00:03:24.319 align:start position:0%
way I want to see so you can uh go to
 

00:03:24.319 --> 00:03:27.430 align:start position:0%
way I want to see so you can uh go to
our<00:03:24.599><c> demo</c><00:03:25.040><c> agents</c><00:03:25.760><c> and</c><00:03:26.080><c> over</c><00:03:26.440><c> there</c><00:03:26.720><c> let's</c><00:03:27.000><c> see</c>

00:03:27.430 --> 00:03:27.440 align:start position:0%
our demo agents and over there let's see
 

00:03:27.440 --> 00:03:30.070 align:start position:0%
our demo agents and over there let's see
what<00:03:27.680><c> our</c><00:03:28.319><c> financial</c><00:03:28.799><c> agents</c><00:03:29.599><c> uh</c><00:03:29.799><c> which</c><00:03:29.920><c> is</c>

00:03:30.070 --> 00:03:30.080 align:start position:0%
what our financial agents uh which is
 

00:03:30.080 --> 00:03:31.190 align:start position:0%
what our financial agents uh which is
using

00:03:31.190 --> 00:03:31.200 align:start position:0%
using
 

00:03:31.200 --> 00:03:35.910 align:start position:0%
using
gbd4<00:03:32.200><c> oh</c><00:03:32.840><c> does</c><00:03:33.439><c> that</c><00:03:33.599><c> is</c><00:03:33.760><c> a</c><00:03:34.040><c> good</c>

00:03:35.910 --> 00:03:35.920 align:start position:0%
gbd4 oh does that is a good
 

00:03:35.920 --> 00:03:38.309 align:start position:0%
gbd4 oh does that is a good
answer<00:03:36.920><c> that</c><00:03:37.040><c> is</c><00:03:37.159><c> a</c><00:03:37.319><c> very</c><00:03:37.480><c> good</c><00:03:37.680><c> answer</c><00:03:38.080><c> and</c>

00:03:38.309 --> 00:03:38.319 align:start position:0%
answer that is a very good answer and
 

00:03:38.319 --> 00:03:39.589 align:start position:0%
answer that is a very good answer and
our<00:03:38.519><c> demo</c><00:03:38.799><c> agents</c><00:03:39.159><c> they</c><00:03:39.280><c> have</c><00:03:39.439><c> this</c>

00:03:39.589 --> 00:03:39.599 align:start position:0%
our demo agents they have this
 

00:03:39.599 --> 00:03:41.309 align:start position:0%
our demo agents they have this
additional<00:03:40.080><c> instruction</c><00:03:40.519><c> to</c><00:03:40.640><c> be</c><00:03:40.840><c> concise</c>

00:03:41.309 --> 00:03:41.319 align:start position:0%
additional instruction to be concise
 

00:03:41.319 --> 00:03:44.309 align:start position:0%
additional instruction to be concise
like<00:03:41.519><c> not</c><00:03:41.760><c> like</c><00:03:42.680><c> uh</c><00:03:42.879><c> Yap</c><00:03:43.159><c> a</c><00:03:43.319><c> lot</c><00:03:43.599><c> so</c><00:03:44.000><c> that's</c><00:03:44.159><c> why</c>

00:03:44.309 --> 00:03:44.319 align:start position:0%
like not like uh Yap a lot so that's why
 

00:03:44.319 --> 00:03:46.110 align:start position:0%
like not like uh Yap a lot so that's why
it's<00:03:44.439><c> giving</c><00:03:44.720><c> to</c><00:03:44.959><c> the</c><00:03:45.080><c> point</c><00:03:45.360><c> answers</c><00:03:45.920><c> okay</c>

00:03:46.110 --> 00:03:46.120 align:start position:0%
it's giving to the point answers okay
 

00:03:46.120 --> 00:03:47.470 align:start position:0%
it's giving to the point answers okay
coming<00:03:46.400><c> back</c><00:03:46.560><c> to</c><00:03:46.720><c> the</c>

00:03:47.470 --> 00:03:47.480 align:start position:0%
coming back to the
 

00:03:47.480 --> 00:03:51.190 align:start position:0%
coming back to the
7777<00:03:48.480><c> all</c><00:03:48.599><c> right</c><00:03:48.799><c> final</c><00:03:49.200><c> the</c><00:03:49.360><c> YouTube</c><00:03:50.200><c> One</c>

00:03:51.190 --> 00:03:51.200 align:start position:0%
7777 all right final the YouTube One
 

00:03:51.200 --> 00:03:53.470 align:start position:0%
7777 all right final the YouTube One
let's<00:03:51.480><c> give</c><00:03:51.640><c> it</c><00:03:51.840><c> merin's</c><00:03:52.560><c> latest</c><00:03:52.879><c> video</c><00:03:53.319><c> which</c>

00:03:53.470 --> 00:03:53.480 align:start position:0%
let's give it merin's latest video which
 

00:03:53.480 --> 00:03:55.190 align:start position:0%
let's give it merin's latest video which
I'm<00:03:53.760><c> yet</c><00:03:53.920><c> to</c>

00:03:55.190 --> 00:03:55.200 align:start position:0%
I'm yet to
 

00:03:55.200 --> 00:04:01.710 align:start position:0%
I'm yet to
watch<00:03:56.200><c> I'm</c><00:03:56.360><c> such</c><00:03:56.560><c> a</c><00:03:56.680><c> big</c><00:03:56.840><c> fan</c><00:03:57.000><c> of</c><00:03:57.120><c> Morin</c>

00:04:01.710 --> 00:04:01.720 align:start position:0%
 
 

00:04:01.720 --> 00:04:05.990 align:start position:0%
 
it's<00:04:01.920><c> going</c><00:04:02.200><c> through</c>

00:04:05.990 --> 00:04:06.000 align:start position:0%
 
 

00:04:06.000 --> 00:04:08.869 align:start position:0%
 
things<00:04:07.000><c> really</c><00:04:07.360><c> I</c><00:04:07.519><c> think</c><00:04:08.239><c> this</c><00:04:08.400><c> needs</c><00:04:08.599><c> to</c><00:04:08.760><c> be</c>

00:04:08.869 --> 00:04:08.879 align:start position:0%
things really I think this needs to be
 

00:04:08.879 --> 00:04:10.550 align:start position:0%
things really I think this needs to be
prompted<00:04:09.319><c> better</c><00:04:09.560><c> so</c><00:04:09.720><c> if</c><00:04:09.840><c> anyone's</c><00:04:10.239><c> watching</c>

00:04:10.550 --> 00:04:10.560 align:start position:0%
prompted better so if anyone's watching
 

00:04:10.560 --> 00:04:12.910 align:start position:0%
prompted better so if anyone's watching
this<00:04:10.760><c> video</c><00:04:11.680><c> uh</c><00:04:11.799><c> you</c><00:04:11.959><c> get</c><00:04:12.200><c> these</c><00:04:12.360><c> agents</c><00:04:12.720><c> to</c>

00:04:12.910 --> 00:04:12.920 align:start position:0%
this video uh you get these agents to
 

00:04:12.920 --> 00:04:16.310 align:start position:0%
this video uh you get these agents to
work<00:04:13.239><c> better</c><00:04:13.879><c> send</c><00:04:14.159><c> a</c><00:04:14.799><c> PR</c><00:04:15.799><c> there's</c><00:04:16.040><c> another</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
work better send a PR there's another
 

00:04:16.320 --> 00:04:18.229 align:start position:0%
work better send a PR there's another
thing<00:04:16.479><c> which</c><00:04:16.639><c> I</c><00:04:16.720><c> want</c><00:04:16.799><c> to</c><00:04:16.959><c> do</c><00:04:17.199><c> is</c><00:04:17.519><c> I</c><00:04:17.600><c> want</c><00:04:17.720><c> to</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
thing which I want to do is I want to
 

00:04:18.239 --> 00:04:21.990 align:start position:0%
thing which I want to do is I want to
turn<00:04:19.239><c> I</c><00:04:19.320><c> want</c><00:04:19.440><c> to</c><00:04:19.639><c> stop</c><00:04:19.959><c> using</c><00:04:20.519><c> the</c><00:04:21.519><c> native</c>

00:04:21.990 --> 00:04:22.000 align:start position:0%
turn I want to stop using the native
 

00:04:22.000 --> 00:04:24.430 align:start position:0%
turn I want to stop using the native
Alama<00:04:22.440><c> tool</c><00:04:22.720><c> calling</c><00:04:23.280><c> and</c><00:04:23.440><c> go</c><00:04:23.880><c> back</c><00:04:24.120><c> to</c><00:04:24.320><c> the</c>

00:04:24.430 --> 00:04:24.440 align:start position:0%
Alama tool calling and go back to the
 

00:04:24.440 --> 00:04:27.189 align:start position:0%
Alama tool calling and go back to the
XML<00:04:24.919><c> tags</c><00:04:25.320><c> which</c><00:04:25.479><c> we</c><00:04:25.720><c> have</c><00:04:26.479><c> with</c><00:04:26.680><c> five</c><00:04:26.960><c> data</c>

00:04:27.189 --> 00:04:27.199 align:start position:0%
XML tags which we have with five data
 

00:04:27.199 --> 00:04:28.590 align:start position:0%
XML tags which we have with five data
assistance<00:04:27.639><c> so</c><00:04:27.880><c> over</c><00:04:28.080><c> there</c><00:04:28.199><c> we</c><00:04:28.360><c> have</c><00:04:28.440><c> a</c>

00:04:28.590 --> 00:04:28.600 align:start position:0%
assistance so over there we have a
 

00:04:28.600 --> 00:04:30.749 align:start position:0%
assistance so over there we have a
separate<00:04:28.919><c> like</c><00:04:29.080><c> llm</c><00:04:29.560><c> where</c><00:04:30.000><c> we</c><00:04:30.199><c> put</c><00:04:30.400><c> them</c><00:04:30.600><c> put</c>

00:04:30.749 --> 00:04:30.759 align:start position:0%
separate like llm where we put them put
 

00:04:30.759 --> 00:04:32.350 align:start position:0%
separate like llm where we put them put
the<00:04:30.840><c> tool</c><00:04:31.039><c> calls</c><00:04:31.280><c> in</c><00:04:31.400><c> XML</c><00:04:31.759><c> tags</c><00:04:32.000><c> and</c><00:04:32.120><c> then</c><00:04:32.240><c> we</c>

00:04:32.350 --> 00:04:32.360 align:start position:0%
the tool calls in XML tags and then we
 

00:04:32.360 --> 00:04:35.710 align:start position:0%
the tool calls in XML tags and then we
run<00:04:32.560><c> it</c><00:04:33.240><c> um</c><00:04:34.240><c> and</c><00:04:34.600><c> I</c><00:04:34.720><c> think</c><00:04:35.039><c> that's</c><00:04:35.400><c> uh</c><00:04:35.560><c> that's</c>

00:04:35.710 --> 00:04:35.720 align:start position:0%
run it um and I think that's uh that's
 

00:04:35.720 --> 00:04:40.350 align:start position:0%
run it um and I think that's uh that's
where<00:04:35.880><c> we</c><00:04:36.000><c> want</c><00:04:36.120><c> to</c><00:04:36.320><c> get</c><00:04:36.639><c> with</c>

00:04:40.350 --> 00:04:40.360 align:start position:0%
 
 

00:04:40.360 --> 00:04:42.590 align:start position:0%
 
this<00:04:41.360><c> by</c><00:04:41.479><c> the</c><00:04:41.600><c> way</c><00:04:41.720><c> I</c><00:04:41.800><c> know</c><00:04:41.960><c> this</c><00:04:42.039><c> shows</c><00:04:42.320><c> Lama</c>

00:04:42.590 --> 00:04:42.600 align:start position:0%
this by the way I know this shows Lama
 

00:04:42.600 --> 00:04:44.790 align:start position:0%
this by the way I know this shows Lama
3.2<00:04:43.199><c> this</c><00:04:43.320><c> is</c><00:04:43.639><c> this</c><00:04:43.840><c> just</c><00:04:43.960><c> an</c><00:04:44.120><c> error</c><00:04:44.360><c> on</c><00:04:44.520><c> our</c>

00:04:44.790 --> 00:04:44.800 align:start position:0%
3.2 this is this just an error on our
 

00:04:44.800 --> 00:04:48.070 align:start position:0%
3.2 this is this just an error on our
side<00:04:45.360><c> uh</c><00:04:46.280><c> so</c><00:04:46.800><c> that's</c><00:04:47.160><c> it's</c><00:04:47.400><c> really</c><00:04:47.680><c> is</c><00:04:47.840><c> Lama</c>

00:04:48.070 --> 00:04:48.080 align:start position:0%
side uh so that's it's really is Lama
 

00:04:48.080 --> 00:04:52.189 align:start position:0%
side uh so that's it's really is Lama
3.1<00:04:48.759><c> I've</c><00:04:48.919><c> got</c><00:04:49.039><c> to</c><00:04:49.160><c> fix</c><00:04:49.440><c> this</c>

00:04:52.189 --> 00:04:52.199 align:start position:0%
 
 

00:04:52.199 --> 00:04:54.870 align:start position:0%
 
piece<00:04:53.199><c> okay</c><00:04:53.320><c> I'm</c><00:04:53.440><c> going</c><00:04:53.520><c> to</c><00:04:53.720><c> ask</c><00:04:53.919><c> it</c><00:04:54.199><c> ask</c><00:04:54.680><c> a</c>

00:04:54.870 --> 00:04:54.880 align:start position:0%
piece okay I'm going to ask it ask a
 

00:04:54.880 --> 00:04:59.310 align:start position:0%
piece okay I'm going to ask it ask a
question<00:04:55.440><c> again</c><00:04:56.440><c> summarize</c><00:04:57.199><c> for</c><00:04:57.720><c> me</c><00:04:58.720><c> break</c><00:04:59.039><c> it</c>

00:04:59.310 --> 00:04:59.320 align:start position:0%
question again summarize for me break it
 

00:04:59.320 --> 00:05:00.830 align:start position:0%
question again summarize for me break it
down

00:05:00.830 --> 00:05:00.840 align:start position:0%
down
 

00:05:00.840 --> 00:05:07.310 align:start position:0%
down
into<00:05:01.840><c> section</c><00:05:02.479><c> and</c><00:05:03.240><c> provide</c><00:05:04.080><c> a</c>

00:05:07.310 --> 00:05:07.320 align:start position:0%
 
 

00:05:07.320 --> 00:05:09.870 align:start position:0%
 
conclusion<00:05:08.320><c> maybe</c><00:05:08.639><c> we</c><00:05:08.840><c> could</c><00:05:09.360><c> uh</c><00:05:09.479><c> open</c><00:05:09.680><c> up</c>

00:05:09.870 --> 00:05:09.880 align:start position:0%
conclusion maybe we could uh open up
 

00:05:09.880 --> 00:05:12.350 align:start position:0%
conclusion maybe we could uh open up
this<00:05:10.039><c> input</c><00:05:10.400><c> like</c><00:05:10.520><c> you</c><00:05:10.600><c> know</c><00:05:10.800><c> go</c><00:05:11.360><c> vertically</c>

00:05:12.350 --> 00:05:12.360 align:start position:0%
this input like you know go vertically
 

00:05:12.360 --> 00:05:14.670 align:start position:0%
this input like you know go vertically
as

00:05:14.670 --> 00:05:14.680 align:start position:0%
 
 

00:05:14.680 --> 00:05:17.749 align:start position:0%
 
well

00:05:17.749 --> 00:05:17.759 align:start position:0%
 
 

00:05:17.759 --> 00:05:19.710 align:start position:0%
 
okay<00:05:18.759><c> see</c><00:05:18.960><c> what's</c><00:05:19.120><c> happening</c><00:05:19.400><c> behind</c><00:05:19.639><c> the</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
okay see what's happening behind the
 

00:05:19.720 --> 00:05:29.990 align:start position:0%
okay see what's happening behind the
scenes<00:05:20.639><c> with</c><00:05:20.840><c> this</c>

00:05:29.990 --> 00:05:30.000 align:start position:0%
 
 

00:05:30.000 --> 00:05:32.870 align:start position:0%
 
random<00:05:30.479><c> function</c><00:05:30.960><c> not</c><00:05:31.280><c> bad</c><00:05:31.960><c> see</c><00:05:32.199><c> it</c><00:05:32.440><c> calls</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
random function not bad see it calls
 

00:05:32.880 --> 00:05:40.670 align:start position:0%
random function not bad see it calls
these<00:05:33.560><c> uh</c><00:05:34.560><c> random</c><00:05:35.039><c> functions</c><00:05:35.840><c> uh</c><00:05:36.000><c> null</c>

00:05:40.670 --> 00:05:40.680 align:start position:0%
 
 

00:05:40.680 --> 00:05:51.590 align:start position:0%
 
null

00:05:51.590 --> 00:05:51.600 align:start position:0%
 
 

00:05:51.600 --> 00:05:54.710 align:start position:0%
 
can<00:05:52.600><c> not</c><00:05:52.800><c> bad</c><00:05:53.039><c> at</c><00:05:53.120><c> all</c><00:05:53.520><c> listen</c><00:05:54.280><c> instead</c><00:05:54.600><c> of</c>

00:05:54.710 --> 00:05:54.720 align:start position:0%
can not bad at all listen instead of
 

00:05:54.720 --> 00:05:56.510 align:start position:0%
can not bad at all listen instead of
using<00:05:55.039><c> like</c><00:05:55.120><c> a</c><00:05:55.280><c> completely</c><00:05:55.680><c> autonomous</c><00:05:56.160><c> agent</c>

00:05:56.510 --> 00:05:56.520 align:start position:0%
using like a completely autonomous agent
 

00:05:56.520 --> 00:05:59.070 align:start position:0%
using like a completely autonomous agent
like<00:05:56.800><c> we</c><00:05:56.919><c> are</c><00:05:57.039><c> trying</c><00:05:57.280><c> to</c><00:05:57.440><c> build</c><00:05:57.759><c> here</c><00:05:58.680><c> if</c><00:05:58.880><c> we</c>

00:05:59.070 --> 00:05:59.080 align:start position:0%
like we are trying to build here if we
 

00:05:59.080 --> 00:06:01.350 align:start position:0%
like we are trying to build here if we
took<00:05:59.319><c> the</c><00:05:59.680><c> captions</c><00:06:00.600><c> then</c><00:06:00.840><c> gave</c><00:06:01.000><c> it</c><00:06:01.120><c> to</c><00:06:01.240><c> the</c>

00:06:01.350 --> 00:06:01.360 align:start position:0%
took the captions then gave it to the
 

00:06:01.360 --> 00:06:03.430 align:start position:0%
took the captions then gave it to the
model<00:06:01.680><c> told</c><00:06:01.919><c> it</c><00:06:02.080><c> to</c><00:06:02.240><c> summarize</c><00:06:02.960><c> like</c><00:06:03.120><c> we</c><00:06:03.240><c> used</c>

00:06:03.430 --> 00:06:03.440 align:start position:0%
model told it to summarize like we used
 

00:06:03.440 --> 00:06:05.590 align:start position:0%
model told it to summarize like we used
to<00:06:03.520><c> do</c><00:06:03.759><c> previously</c><00:06:04.400><c> with</c><00:06:04.639><c> a</c><00:06:04.720><c> lot</c><00:06:04.880><c> of</c><00:06:05.000><c> our</c><00:06:05.160><c> demos</c>

00:06:05.590 --> 00:06:05.600 align:start position:0%
to do previously with a lot of our demos
 

00:06:05.600 --> 00:06:07.790 align:start position:0%
to do previously with a lot of our demos
where<00:06:05.759><c> we</c><00:06:05.919><c> built</c><00:06:06.160><c> deterministic</c><00:06:06.880><c> workflows</c>

00:06:07.790 --> 00:06:07.800 align:start position:0%
where we built deterministic workflows
 

00:06:07.800 --> 00:06:11.909 align:start position:0%
where we built deterministic workflows
where<00:06:07.960><c> we</c><00:06:08.599><c> got</c><00:06:08.880><c> the</c><00:06:09.039><c> video</c><00:06:09.680><c> first</c><00:06:10.800><c> then</c><00:06:11.800><c> kind</c>

00:06:11.909 --> 00:06:11.919 align:start position:0%
where we got the video first then kind
 

00:06:11.919 --> 00:06:13.790 align:start position:0%
where we got the video first then kind
of<00:06:12.160><c> asked</c><00:06:12.440><c> them</c><00:06:12.560><c> all</c><00:06:12.759><c> to</c><00:06:12.919><c> generate</c><00:06:13.360><c> sections</c>

00:06:13.790 --> 00:06:13.800 align:start position:0%
of asked them all to generate sections
 

00:06:13.800 --> 00:06:15.350 align:start position:0%
of asked them all to generate sections
of<00:06:13.960><c> it</c><00:06:14.160><c> then</c><00:06:14.280><c> a</c><00:06:14.400><c> full</c><00:06:14.599><c> summary</c><00:06:15.000><c> I</c><00:06:15.080><c> think</c><00:06:15.240><c> this</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
of it then a full summary I think this
 

00:06:15.360 --> 00:06:18.309 align:start position:0%
of it then a full summary I think this
would<00:06:15.599><c> work</c><00:06:15.960><c> pretty</c><00:06:16.240><c> well</c><00:06:17.199><c> um</c><00:06:17.960><c> and</c><00:06:18.120><c> again</c>

00:06:18.309 --> 00:06:18.319 align:start position:0%
would work pretty well um and again
 

00:06:18.319 --> 00:06:19.710 align:start position:0%
would work pretty well um and again
this's<00:06:18.479><c> an</c><00:06:18.560><c> 8</c><00:06:18.759><c> billion</c><00:06:19.000><c> parameter</c><00:06:19.360><c> model</c><00:06:19.560><c> so</c>

00:06:19.710 --> 00:06:19.720 align:start position:0%
this's an 8 billion parameter model so
 

00:06:19.720 --> 00:06:23.390 align:start position:0%
this's an 8 billion parameter model so
let's<00:06:19.919><c> not</c><00:06:20.240><c> uh</c><00:06:20.319><c> be</c><00:06:20.479><c> too</c><00:06:20.639><c> hard</c><00:06:20.880><c> on</c><00:06:21.000><c> it</c><00:06:22.199><c> okay</c><00:06:23.199><c> get</c>

00:06:23.390 --> 00:06:23.400 align:start position:0%
let's not uh be too hard on it okay get
 

00:06:23.400 --> 00:06:27.270 align:start position:0%
let's not uh be too hard on it okay get
me<00:06:23.880><c> stock</c><00:06:24.520><c> prices</c><00:06:25.039><c> for</c><00:06:25.800><c> NVIDIA</c><00:06:26.720><c> meta</c><00:06:27.080><c> and</c>

00:06:27.270 --> 00:06:27.280 align:start position:0%
me stock prices for NVIDIA meta and
 

00:06:27.280 --> 00:06:29.990 align:start position:0%
me stock prices for NVIDIA meta and
Tesla

00:06:29.990 --> 00:06:30.000 align:start position:0%
Tesla
 

00:06:30.000 --> 00:06:32.870 align:start position:0%
Tesla
three<00:06:30.319><c> Qui</c><00:06:30.560><c> tool</c><00:06:30.759><c> calls</c><00:06:31.120><c> let's</c><00:06:31.319><c> see</c><00:06:31.520><c> what</c>

00:06:32.870 --> 00:06:32.880 align:start position:0%
three Qui tool calls let's see what
 

00:06:32.880 --> 00:06:39.189 align:start position:0%
three Qui tool calls let's see what
happens<00:06:33.880><c> easy</c><00:06:34.160><c> tool</c><00:06:34.440><c> calls</c><00:06:34.800><c> one</c><00:06:35.080><c> round</c>

00:06:39.189 --> 00:06:39.199 align:start position:0%
 
 

00:06:39.199 --> 00:06:42.150 align:start position:0%
 
um<00:06:40.199><c> okay</c><00:06:40.400><c> that's</c><00:06:40.639><c> actually</c><00:06:41.000><c> nice</c><00:06:41.639><c> come</c><00:06:41.759><c> on</c><00:06:42.080><c> I</c>

00:06:42.150 --> 00:06:42.160 align:start position:0%
um okay that's actually nice come on I
 

00:06:42.160 --> 00:06:44.029 align:start position:0%
um okay that's actually nice come on I
know<00:06:42.319><c> it's</c><00:06:42.479><c> not</c><00:06:42.759><c> like</c><00:06:43.160><c> the</c><00:06:43.319><c> toughest</c><00:06:43.759><c> question</c>

00:06:44.029 --> 00:06:44.039 align:start position:0%
know it's not like the toughest question
 

00:06:44.039 --> 00:06:46.189 align:start position:0%
know it's not like the toughest question
we<00:06:44.199><c> post</c><00:06:44.479><c> to</c><00:06:44.639><c> it</c><00:06:44.800><c> but</c><00:06:44.919><c> it</c><00:06:45.080><c> gave</c><00:06:45.240><c> a</c><00:06:45.400><c> NIC</c><00:06:45.720><c> formed</c>

00:06:46.189 --> 00:06:46.199 align:start position:0%
we post to it but it gave a NIC formed
 

00:06:46.199 --> 00:06:47.790 align:start position:0%
we post to it but it gave a NIC formed
mark<00:06:46.440><c> down</c><00:06:46.599><c> on</c><00:06:46.759><c> so</c><00:06:46.919><c> things</c><00:06:47.120><c> look</c><00:06:47.319><c> bright</c><00:06:47.560><c> in</c><00:06:47.680><c> a</c>

00:06:47.790 --> 00:06:47.800 align:start position:0%
mark down on so things look bright in a
 

00:06:47.800 --> 00:06:55.029 align:start position:0%
mark down on so things look bright in a
year<00:06:47.960><c> or</c><00:06:48.080><c> two</c><00:06:48.319><c> with</c><00:06:48.440><c> those</c><00:06:48.720><c> things</c><00:06:49.319><c> all</c><00:06:49.479><c> right</c>

00:06:55.029 --> 00:06:55.039 align:start position:0%
 
 

00:06:55.039 --> 00:06:58.150 align:start position:0%
 
um<00:06:56.039><c> excuse</c><00:06:56.319><c> me</c><00:06:56.479><c> folks</c><00:06:56.800><c> I'm</c><00:06:57.080><c> um</c><00:06:57.599><c> running</c><00:06:57.919><c> a</c><00:06:58.039><c> bit</c>

00:06:58.150 --> 00:06:58.160 align:start position:0%
um excuse me folks I'm um running a bit
 

00:06:58.160 --> 00:07:10.629 align:start position:0%
um excuse me folks I'm um running a bit
of<00:06:58.240><c> a</c><00:06:58.360><c> cold</c><00:06:58.759><c> so</c><00:06:59.639><c> voice</c><00:06:59.879><c> is</c><00:07:00.039><c> not</c>

00:07:10.629 --> 00:07:10.639 align:start position:0%
 
 

00:07:10.639 --> 00:07:13.510 align:start position:0%
 
perfect<00:07:11.639><c> N</c><00:07:11.800><c> I</c><00:07:11.919><c> don't</c><00:07:12.199><c> think</c><00:07:12.440><c> I'd</c><00:07:13.199><c> really</c><00:07:13.400><c> want</c>

00:07:13.510 --> 00:07:13.520 align:start position:0%
perfect N I don't think I'd really want
 

00:07:13.520 --> 00:07:16.390 align:start position:0%
perfect N I don't think I'd really want
to<00:07:13.639><c> use</c><00:07:13.879><c> this</c><00:07:14.039><c> model</c><00:07:14.319><c> with</c><00:07:14.520><c> anything</c><00:07:15.440><c> but</c><00:07:16.120><c> I</c>

00:07:16.390 --> 00:07:16.400 align:start position:0%
to use this model with anything but I
 

00:07:16.400 --> 00:07:18.869 align:start position:0%
to use this model with anything but I
also<00:07:16.759><c> feel</c>

00:07:18.869 --> 00:07:18.879 align:start position:0%
also feel
 

00:07:18.879 --> 00:07:21.230 align:start position:0%
also feel
that<00:07:19.879><c> someone</c><00:07:20.240><c> could</c><00:07:20.400><c> do</c><00:07:20.560><c> a</c><00:07:20.680><c> better</c><00:07:20.919><c> job</c><00:07:21.080><c> at</c>

00:07:21.230 --> 00:07:21.240 align:start position:0%
that someone could do a better job at
 

00:07:21.240 --> 00:07:23.110 align:start position:0%
that someone could do a better job at
prompting<00:07:21.720><c> them</c><00:07:22.120><c> I've</c><00:07:22.440><c> uh</c><00:07:22.520><c> we've</c><00:07:22.720><c> been</c><00:07:22.919><c> very</c>

00:07:23.110 --> 00:07:23.120 align:start position:0%
prompting them I've uh we've been very
 

00:07:23.120 --> 00:07:27.469 align:start position:0%
prompting them I've uh we've been very
lenient<00:07:23.639><c> without</c><00:07:23.759><c> our</c><00:07:24.000><c> prompting</c><00:07:25.000><c> on</c><00:07:25.319><c> this</c>

00:07:27.469 --> 00:07:27.479 align:start position:0%
lenient without our prompting on this
 

00:07:27.479 --> 00:07:29.830 align:start position:0%
lenient without our prompting on this
because<00:07:28.479><c> I'm</c><00:07:28.639><c> looking</c><00:07:28.960><c> at</c><00:07:29.120><c> it</c><00:07:29.360><c> like</c><00:07:29.560><c> like</c><00:07:29.720><c> you</c>

00:07:29.830 --> 00:07:29.840 align:start position:0%
because I'm looking at it like like you
 

00:07:29.840 --> 00:07:31.390 align:start position:0%
because I'm looking at it like like you
know<00:07:30.120><c> if</c><00:07:30.360><c> I</c>

00:07:31.390 --> 00:07:31.400 align:start position:0%
know if I
 

00:07:31.400 --> 00:07:34.270 align:start position:0%
know if I
um<00:07:32.400><c> if</c><00:07:32.520><c> I</c><00:07:32.680><c> just</c><00:07:32.879><c> went</c><00:07:33.280><c> to</c><00:07:33.479><c> a</c><00:07:33.599><c> demo</c><00:07:33.840><c> agents</c><00:07:34.160><c> which</c>

00:07:34.270 --> 00:07:34.280 align:start position:0%
um if I just went to a demo agents which
 

00:07:34.280 --> 00:07:37.670 align:start position:0%
um if I just went to a demo agents which
is<00:07:34.400><c> gb4</c><00:07:34.840><c> Turbo</c><00:07:35.120><c> and</c><00:07:35.240><c> I</c><00:07:35.360><c> asked</c><00:07:36.120><c> uh</c><00:07:37.120><c> tell</c><00:07:37.319><c> me</c>

00:07:37.670 --> 00:07:37.680 align:start position:0%
is gb4 Turbo and I asked uh tell me
 

00:07:37.680 --> 00:07:40.189 align:start position:0%
is gb4 Turbo and I asked uh tell me
about<00:07:38.440><c> the</c><00:07:38.639><c> US</c><00:07:39.000><c> elections</c><00:07:39.720><c> let's</c><00:07:39.879><c> see</c><00:07:40.080><c> what</c>

00:07:40.189 --> 00:07:40.199 align:start position:0%
about the US elections let's see what
 

00:07:40.199 --> 00:07:49.990 align:start position:0%
about the US elections let's see what
kind<00:07:40.319><c> of</c><00:07:40.520><c> results</c><00:07:41.520><c> that</c><00:07:41.759><c> agent</c>

00:07:49.990 --> 00:07:50.000 align:start position:0%
 
 

00:07:50.000 --> 00:07:52.909 align:start position:0%
 
gives<00:07:51.000><c> that's</c><00:07:51.159><c> not</c><00:07:51.400><c> bad</c><00:07:51.560><c> at</c><00:07:51.680><c> all</c><00:07:52.120><c> that's</c><00:07:52.680><c> not</c>

00:07:52.909 --> 00:07:52.919 align:start position:0%
gives that's not bad at all that's not
 

00:07:52.919 --> 00:07:55.149 align:start position:0%
gives that's not bad at all that's not
bad<00:07:53.120><c> at</c><00:07:53.199><c> all</c><00:07:54.039><c> all</c><00:07:54.120><c> right</c><00:07:54.280><c> anyway</c><00:07:54.560><c> folks</c><00:07:54.960><c> this</c>

00:07:55.149 --> 00:07:55.159 align:start position:0%
bad at all all right anyway folks this
 

00:07:55.159 --> 00:07:58.230 align:start position:0%
bad at all all right anyway folks this
was<00:07:55.400><c> the</c><00:07:55.879><c> uh</c><00:07:56.039><c> 8</c><00:07:56.319><c> billion</c><00:07:56.639><c> parameter</c><00:07:57.039><c> model</c><00:07:58.000><c> and</c>

00:07:58.230 --> 00:07:58.240 align:start position:0%
was the uh 8 billion parameter model and
 

00:07:58.240 --> 00:08:00.270 align:start position:0%
was the uh 8 billion parameter model and
hope<00:07:58.360><c> you</c><00:07:58.479><c> enjoyed</c><00:07:58.759><c> the</c><00:07:58.879><c> video</c><00:07:59.919><c> if</c><00:08:00.000><c> you</c><00:08:00.159><c> have</c>

00:08:00.270 --> 00:08:00.280 align:start position:0%
hope you enjoyed the video if you have
 

00:08:00.280 --> 00:08:01.990 align:start position:0%
hope you enjoyed the video if you have
any<00:08:00.440><c> feedback</c><00:08:00.840><c> please</c><00:08:01.039><c> do</c><00:08:01.240><c> let</c><00:08:01.400><c> me</c><00:08:01.560><c> know</c><00:08:01.919><c> if</c>

00:08:01.990 --> 00:08:02.000 align:start position:0%
any feedback please do let me know if
 

00:08:02.000 --> 00:08:03.869 align:start position:0%
any feedback please do let me know if
you<00:08:02.120><c> can</c><00:08:02.360><c> improve</c><00:08:02.720><c> the</c><00:08:02.840><c> agents</c><00:08:03.280><c> by</c><00:08:03.400><c> prompting</c>

00:08:03.869 --> 00:08:03.879 align:start position:0%
you can improve the agents by prompting
 

00:08:03.879 --> 00:08:06.430 align:start position:0%
you can improve the agents by prompting
them<00:08:04.159><c> better</c><00:08:04.560><c> please</c><00:08:04.960><c> submit</c><00:08:05.280><c> a</c><00:08:05.440><c> PR</c><00:08:05.720><c> as</c><00:08:05.879><c> well</c>

00:08:06.430 --> 00:08:06.440 align:start position:0%
them better please submit a PR as well
 

00:08:06.440 --> 00:08:07.550 align:start position:0%
them better please submit a PR as well
I'm<00:08:06.599><c> looking</c><00:08:06.800><c> forward</c><00:08:07.039><c> to</c><00:08:07.159><c> hearing</c><00:08:07.400><c> your</c>

00:08:07.550 --> 00:08:07.560 align:start position:0%
I'm looking forward to hearing your
 

00:08:07.560 --> 00:08:11.599 align:start position:0%
I'm looking forward to hearing your
thoughts<00:08:07.800><c> have</c><00:08:07.879><c> a</c><00:08:08.000><c> good</c><00:08:08.159><c> weekend</c><00:08:08.599><c> bye</c>

