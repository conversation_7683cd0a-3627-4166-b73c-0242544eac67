WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.030 align:start position:0%
 
[Music]

00:00:05.030 --> 00:00:05.040 align:start position:0%
 
 

00:00:05.040 --> 00:00:08.110 align:start position:0%
 
one<00:00:05.160><c> of</c><00:00:05.359><c> the</c><00:00:05.560><c> best</c><00:00:05.839><c> ways</c><00:00:06.240><c> to</c><00:00:06.960><c> understand</c><00:00:07.720><c> large</c>

00:00:08.110 --> 00:00:08.120 align:start position:0%
one of the best ways to understand large
 

00:00:08.120 --> 00:00:11.549 align:start position:0%
one of the best ways to understand large
language<00:00:08.440><c> model</c><00:00:09.120><c> apis</c><00:00:10.120><c> is</c><00:00:10.280><c> to</c><00:00:10.759><c> interact</c><00:00:11.360><c> with</c>

00:00:11.549 --> 00:00:11.559 align:start position:0%
language model apis is to interact with
 

00:00:11.559 --> 00:00:14.789 align:start position:0%
language model apis is to interact with
them<00:00:12.120><c> via</c><00:00:12.440><c> code</c><00:00:12.840><c> and</c><00:00:13.080><c> experiment</c><00:00:13.719><c> with</c><00:00:13.880><c> them</c>

00:00:14.789 --> 00:00:14.799 align:start position:0%
them via code and experiment with them
 

00:00:14.799 --> 00:00:16.150 align:start position:0%
them via code and experiment with them
and<00:00:14.960><c> this</c><00:00:15.080><c> is</c><00:00:15.200><c> what</c><00:00:15.320><c> we're</c><00:00:15.480><c> going</c><00:00:15.559><c> to</c><00:00:15.719><c> do</c><00:00:15.920><c> in</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
and this is what we're going to do in
 

00:00:16.160 --> 00:00:19.230 align:start position:0%
and this is what we're going to do in
this<00:00:16.359><c> video</c><00:00:17.279><c> we</c><00:00:17.400><c> will</c><00:00:17.560><c> use</c><00:00:17.760><c> Jupiter</c><00:00:18.240><c> notebooks</c>

00:00:19.230 --> 00:00:19.240 align:start position:0%
this video we will use Jupiter notebooks
 

00:00:19.240 --> 00:00:22.029 align:start position:0%
this video we will use Jupiter notebooks
uh<00:00:19.400><c> via</c><00:00:19.760><c> vs</c><00:00:20.199><c> code</c><00:00:21.080><c> and</c><00:00:21.359><c> the</c><00:00:21.480><c> reason</c><00:00:21.720><c> we</c><00:00:21.840><c> use</c>

00:00:22.029 --> 00:00:22.039 align:start position:0%
uh via vs code and the reason we use
 

00:00:22.039 --> 00:00:24.670 align:start position:0%
uh via vs code and the reason we use
Jupiter<00:00:22.400><c> notebooks</c><00:00:23.199><c> in</c><00:00:23.400><c> this</c><00:00:23.599><c> lesson</c><00:00:24.439><c> is</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
Jupiter notebooks in this lesson is
 

00:00:24.680 --> 00:00:26.390 align:start position:0%
Jupiter notebooks in this lesson is
because<00:00:25.000><c> this</c><00:00:25.080><c> is</c><00:00:25.240><c> an</c><00:00:25.760><c> interactive</c>

00:00:26.390 --> 00:00:26.400 align:start position:0%
because this is an interactive
 

00:00:26.400 --> 00:00:28.189 align:start position:0%
because this is an interactive
environment<00:00:27.320><c> which</c><00:00:27.480><c> allows</c><00:00:27.800><c> us</c><00:00:27.960><c> to</c>

00:00:28.189 --> 00:00:28.199 align:start position:0%
environment which allows us to
 

00:00:28.199 --> 00:00:30.349 align:start position:0%
environment which allows us to
experiment<00:00:28.880><c> with</c><00:00:29.039><c> this</c><00:00:29.199><c> models</c><00:00:30.039><c> are</c><00:00:30.160><c> not</c>

00:00:30.349 --> 00:00:30.359 align:start position:0%
experiment with this models are not
 

00:00:30.359 --> 00:00:32.429 align:start position:0%
experiment with this models are not
building<00:00:30.759><c> a</c><00:00:30.960><c> production</c><00:00:31.359><c> grade</c><00:00:31.679><c> application</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
building a production grade application
 

00:00:32.439 --> 00:00:34.549 align:start position:0%
building a production grade application
yet<00:00:33.160><c> uh</c><00:00:33.280><c> we're</c><00:00:33.480><c> just</c><00:00:33.719><c> exploring</c><00:00:34.280><c> we're</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
yet uh we're just exploring we're
 

00:00:34.559 --> 00:00:36.990 align:start position:0%
yet uh we're just exploring we're
experimenting<00:00:35.559><c> and</c><00:00:35.719><c> seeing</c><00:00:36.040><c> the</c><00:00:36.239><c> output</c><00:00:36.840><c> of</c>

00:00:36.990 --> 00:00:37.000 align:start position:0%
experimenting and seeing the output of
 

00:00:37.000 --> 00:00:40.670 align:start position:0%
experimenting and seeing the output of
these<00:00:37.160><c> llms</c><00:00:38.079><c> in</c><00:00:38.440><c> an</c><00:00:38.680><c> interactive</c><00:00:39.680><c> way</c><00:00:40.360><c> uh</c><00:00:40.480><c> will</c>

00:00:40.670 --> 00:00:40.680 align:start position:0%
these llms in an interactive way uh will
 

00:00:40.680 --> 00:00:42.830 align:start position:0%
these llms in an interactive way uh will
help<00:00:40.840><c> us</c><00:00:41.039><c> better</c><00:00:41.600><c> understand</c><00:00:41.719><c> these</c>

00:00:42.830 --> 00:00:42.840 align:start position:0%
help us better understand these
 

00:00:42.840 --> 00:00:45.750 align:start position:0%
help us better understand these
models<00:00:43.840><c> uh</c><00:00:44.039><c> in</c><00:00:44.239><c> this</c><00:00:44.440><c> video</c><00:00:44.800><c> we</c><00:00:44.920><c> will</c><00:00:45.120><c> use</c><00:00:45.440><c> open</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
models uh in this video we will use open
 

00:00:45.760 --> 00:00:48.750 align:start position:0%
models uh in this video we will use open
AI<00:00:46.440><c> API</c><00:00:47.239><c> I</c><00:00:47.399><c> encourage</c><00:00:47.879><c> you</c><00:00:48.280><c> to</c><00:00:48.480><c> also</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
AI API I encourage you to also
 

00:00:48.760 --> 00:00:50.709 align:start position:0%
AI API I encourage you to also
experiment<00:00:49.360><c> with</c><00:00:49.719><c> other</c>

00:00:50.709 --> 00:00:50.719 align:start position:0%
experiment with other
 

00:00:50.719 --> 00:00:54.670 align:start position:0%
experiment with other
apis<00:00:51.719><c> in</c><00:00:51.879><c> order</c><00:00:52.160><c> to</c><00:00:52.320><c> use</c><00:00:52.800><c> um</c><00:00:53.120><c> open</c><00:00:53.399><c> a</c><00:00:53.640><c> API</c><00:00:54.559><c> we</c>

00:00:54.670 --> 00:00:54.680 align:start position:0%
apis in order to use um open a API we
 

00:00:54.680 --> 00:00:57.110 align:start position:0%
apis in order to use um open a API we
will<00:00:54.879><c> need</c><00:00:55.039><c> to</c><00:00:55.359><c> install</c><00:00:55.920><c> some</c><00:00:56.199><c> libraries</c><00:00:56.800><c> we</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
will need to install some libraries we
 

00:00:57.120 --> 00:01:01.590 align:start position:0%
will need to install some libraries we
also<00:00:57.960><c> install</c><00:00:58.600><c> 1B</c><00:00:59.600><c> uh</c><00:00:59.680><c> for</c><00:00:59.920><c> for</c><00:01:00.239><c> tracking</c><00:01:00.920><c> our</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
also install 1B uh for for tracking our
 

00:01:01.600 --> 00:01:04.750 align:start position:0%
also install 1B uh for for tracking our
experiments<00:01:02.600><c> then</c><00:01:02.760><c> we</c><00:01:02.920><c> need</c><00:01:03.079><c> to</c><00:01:03.680><c> import</c><00:01:04.439><c> um</c>

00:01:04.750 --> 00:01:04.760 align:start position:0%
experiments then we need to import um
 

00:01:04.760 --> 00:01:06.990 align:start position:0%
experiments then we need to import um
all<00:01:04.920><c> of</c><00:01:05.199><c> the</c><00:01:05.920><c> uh</c><00:01:06.040><c> the</c><00:01:06.200><c> libraries</c><00:01:06.720><c> that</c><00:01:06.840><c> we'll</c>

00:01:06.990 --> 00:01:07.000 align:start position:0%
all of the uh the libraries that we'll
 

00:01:07.000 --> 00:01:09.190 align:start position:0%
all of the uh the libraries that we'll
need<00:01:07.159><c> in</c><00:01:07.320><c> this</c><00:01:07.600><c> experiments</c><00:01:08.600><c> and</c><00:01:08.799><c> we</c><00:01:08.920><c> need</c><00:01:09.040><c> to</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
need in this experiments and we need to
 

00:01:09.200 --> 00:01:12.030 align:start position:0%
need in this experiments and we need to
make<00:01:09.360><c> sure</c><00:01:09.600><c> that</c><00:01:09.720><c> we</c><00:01:09.880><c> have</c><00:01:10.119><c> open</c><00:01:10.400><c> AI</c><00:01:10.920><c> API</c><00:01:11.400><c> key</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
make sure that we have open AI API key
 

00:01:12.040 --> 00:01:14.830 align:start position:0%
make sure that we have open AI API key
environment<00:01:12.640><c> variable</c><00:01:13.200><c> set</c><00:01:14.200><c> so</c><00:01:14.400><c> let's</c><00:01:14.600><c> double</c>

00:01:14.830 --> 00:01:14.840 align:start position:0%
environment variable set so let's double
 

00:01:14.840 --> 00:01:17.550 align:start position:0%
environment variable set so let's double
check<00:01:15.240><c> I</c><00:01:15.360><c> already</c><00:01:15.640><c> set</c><00:01:15.960><c> it</c><00:01:16.200><c> on</c><00:01:16.360><c> my</c><00:01:16.560><c> system</c><00:01:17.439><c> if</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
check I already set it on my system if
 

00:01:17.560 --> 00:01:20.630 align:start position:0%
check I already set it on my system if
you<00:01:17.720><c> don't</c><00:01:18.040><c> have</c><00:01:18.640><c> uh</c><00:01:18.880><c> this</c><00:01:19.439><c> uh</c><00:01:19.600><c> key</c><00:01:20.280><c> um</c><00:01:20.560><c> you</c>

00:01:20.630 --> 00:01:20.640 align:start position:0%
you don't have uh this uh key um you
 

00:01:20.640 --> 00:01:22.710 align:start position:0%
you don't have uh this uh key um you
will<00:01:20.880><c> see</c><00:01:21.159><c> an</c><00:01:21.439><c> instruction</c><00:01:21.920><c> on</c><00:01:22.159><c> how</c><00:01:22.280><c> to</c><00:01:22.479><c> get</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
will see an instruction on how to get
 

00:01:22.720 --> 00:01:26.830 align:start position:0%
will see an instruction on how to get
that<00:01:22.920><c> key</c><00:01:23.720><c> uh</c><00:01:23.920><c> below</c><00:01:24.280><c> this</c>

00:01:26.830 --> 00:01:26.840 align:start position:0%
 
 

00:01:26.840 --> 00:01:30.789 align:start position:0%
 
video<00:01:27.840><c> because</c><00:01:28.439><c> uh</c><00:01:28.680><c> we</c><00:01:28.840><c> are</c><00:01:29.320><c> uh</c><00:01:29.960><c> tracking</c><00:01:30.520><c> our</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
video because uh we are uh tracking our
 

00:01:30.799 --> 00:01:32.789 align:start position:0%
video because uh we are uh tracking our
work<00:01:31.159><c> because</c><00:01:31.400><c> we</c><00:01:31.520><c> want</c><00:01:31.720><c> to</c><00:01:32.040><c> get</c><00:01:32.240><c> into</c><00:01:32.479><c> the</c>

00:01:32.789 --> 00:01:32.799 align:start position:0%
work because we want to get into the
 

00:01:32.799 --> 00:01:35.310 align:start position:0%
work because we want to get into the
habit<00:01:33.520><c> of</c><00:01:33.720><c> recording</c><00:01:34.479><c> what</c><00:01:34.600><c> we're</c><00:01:34.799><c> doing</c><00:01:35.159><c> with</c>

00:01:35.310 --> 00:01:35.320 align:start position:0%
habit of recording what we're doing with
 

00:01:35.320 --> 00:01:37.990 align:start position:0%
habit of recording what we're doing with
llms<00:01:36.200><c> we</c><00:01:36.320><c> will</c><00:01:36.520><c> also</c><00:01:36.799><c> enable</c><00:01:37.360><c> logging</c><00:01:37.880><c> to</c>

00:01:37.990 --> 00:01:38.000 align:start position:0%
llms we will also enable logging to
 

00:01:38.000 --> 00:01:39.749 align:start position:0%
llms we will also enable logging to
weights<00:01:38.200><c> and</c><00:01:38.360><c> biases</c><00:01:39.159><c> and</c><00:01:39.320><c> we</c><00:01:39.439><c> have</c><00:01:39.600><c> the</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
weights and biases and we have the
 

00:01:39.759 --> 00:01:42.910 align:start position:0%
weights and biases and we have the
convenient<00:01:40.320><c> outo</c><00:01:40.920><c> function</c><00:01:41.640><c> for</c><00:01:41.920><c> open</c><00:01:42.200><c> AI</c>

00:01:42.910 --> 00:01:42.920 align:start position:0%
convenient outo function for open AI
 

00:01:42.920 --> 00:01:45.749 align:start position:0%
convenient outo function for open AI
which<00:01:43.079><c> will</c><00:01:43.600><c> log</c><00:01:44.000><c> the</c><00:01:44.200><c> results</c><00:01:44.840><c> the</c><00:01:45.000><c> the</c><00:01:45.119><c> calls</c>

00:01:45.749 --> 00:01:45.759 align:start position:0%
which will log the results the the calls
 

00:01:45.759 --> 00:01:48.709 align:start position:0%
which will log the results the the calls
uh<00:01:46.159><c> the</c><00:01:46.479><c> to</c><00:01:46.680><c> to</c><00:01:46.799><c> the</c><00:01:46.920><c> API</c><00:01:47.320><c> and</c><00:01:47.479><c> the</c><00:01:47.600><c> results</c><00:01:48.479><c> in</c>

00:01:48.709 --> 00:01:48.719 align:start position:0%
uh the to to the API and the results in
 

00:01:48.719 --> 00:01:50.510 align:start position:0%
uh the to to the API and the results in
a<00:01:48.880><c> weight</c><00:01:49.079><c> Andis</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
a weight Andis
 

00:01:50.520 --> 00:01:53.109 align:start position:0%
a weight Andis
table<00:01:51.520><c> now</c><00:01:51.840><c> the</c><00:01:52.000><c> first</c><00:01:52.240><c> thing</c><00:01:52.479><c> that</c><00:01:52.680><c> we</c><00:01:53.040><c> uh</c>

00:01:53.109 --> 00:01:53.119 align:start position:0%
table now the first thing that we uh
 

00:01:53.119 --> 00:01:55.270 align:start position:0%
table now the first thing that we uh
would<00:01:53.320><c> like</c><00:01:53.520><c> to</c><00:01:53.880><c> to</c><00:01:54.040><c> experiment</c><00:01:54.560><c> with</c><00:01:54.759><c> is</c>

00:01:55.270 --> 00:01:55.280 align:start position:0%
would like to to experiment with is
 

00:01:55.280 --> 00:01:58.270 align:start position:0%
would like to to experiment with is
tokenization<00:01:56.280><c> open</c><00:01:56.560><c> AI</c><00:01:57.000><c> has</c><00:01:57.240><c> this</c><00:01:57.479><c> tick</c><00:01:57.840><c> token</c>

00:01:58.270 --> 00:01:58.280 align:start position:0%
tokenization open AI has this tick token
 

00:01:58.280 --> 00:02:01.429 align:start position:0%
tokenization open AI has this tick token
Library<00:01:59.240><c> which</c><00:01:59.399><c> allows</c><00:01:59.920><c> us</c><00:02:00.159><c> to</c><00:02:00.320><c> tokenize</c><00:02:00.960><c> text</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
Library which allows us to tokenize text
 

00:02:01.439 --> 00:02:04.310 align:start position:0%
Library which allows us to tokenize text
to<00:02:01.680><c> decode</c><00:02:02.200><c> text</c><00:02:02.479><c> that</c><00:02:02.640><c> has</c><00:02:02.799><c> been</c><00:02:03.320><c> tokenized</c>

00:02:04.310 --> 00:02:04.320 align:start position:0%
to decode text that has been tokenized
 

00:02:04.320 --> 00:02:07.270 align:start position:0%
to decode text that has been tokenized
and<00:02:04.960><c> it's</c><00:02:05.200><c> good</c><00:02:05.360><c> to</c><00:02:05.600><c> to</c><00:02:05.920><c> get</c><00:02:06.079><c> an</c><00:02:06.280><c> intuition</c><00:02:06.960><c> on</c>

00:02:07.270 --> 00:02:07.280 align:start position:0%
and it's good to to get an intuition on
 

00:02:07.280 --> 00:02:10.470 align:start position:0%
and it's good to to get an intuition on
how<00:02:08.080><c> sentences</c><00:02:08.879><c> or</c><00:02:09.080><c> words</c><00:02:09.360><c> are</c><00:02:09.520><c> split</c><00:02:09.879><c> into</c>

00:02:10.470 --> 00:02:10.480 align:start position:0%
how sentences or words are split into
 

00:02:10.480 --> 00:02:15.070 align:start position:0%
how sentences or words are split into
tokens<00:02:11.480><c> so</c><00:02:11.680><c> let's</c><00:02:11.840><c> use</c><00:02:12.160><c> tick</c><00:02:12.520><c> token</c><00:02:13.520><c> um</c><00:02:13.879><c> to</c><00:02:14.440><c> uh</c>

00:02:15.070 --> 00:02:15.080 align:start position:0%
tokens so let's use tick token um to uh
 

00:02:15.080 --> 00:02:18.150 align:start position:0%
tokens so let's use tick token um to uh
to<00:02:15.360><c> D</c><00:02:15.720><c> to</c><00:02:15.920><c> encode</c><00:02:16.440><c> and</c><00:02:16.599><c> decode</c><00:02:17.519><c> a</c><00:02:17.680><c> sentence</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
to D to encode and decode a sentence
 

00:02:18.160 --> 00:02:20.509 align:start position:0%
to D to encode and decode a sentence
weights<00:02:18.440><c> and</c><00:02:18.599><c> biases</c><00:02:19.000><c> is</c><00:02:19.200><c> awesome</c><00:02:20.080><c> and</c><00:02:20.239><c> see</c>

00:02:20.509 --> 00:02:20.519 align:start position:0%
weights and biases is awesome and see
 

00:02:20.519 --> 00:02:23.070 align:start position:0%
weights and biases is awesome and see
how<00:02:20.680><c> this</c><00:02:20.800><c> is</c><00:02:20.920><c> going</c><00:02:21.000><c> to</c><00:02:21.120><c> be</c><00:02:21.280><c> split</c><00:02:21.720><c> in</c><00:02:22.080><c> tokens</c>

00:02:23.070 --> 00:02:23.080 align:start position:0%
how this is going to be split in tokens
 

00:02:23.080 --> 00:02:25.030 align:start position:0%
how this is going to be split in tokens
if<00:02:23.200><c> you</c><00:02:23.360><c> think</c><00:02:23.519><c> about</c><00:02:23.879><c> splitting</c><00:02:24.480><c> this</c>

00:02:25.030 --> 00:02:25.040 align:start position:0%
if you think about splitting this
 

00:02:25.040 --> 00:02:27.750 align:start position:0%
if you think about splitting this
probably<00:02:25.560><c> the</c><00:02:25.800><c> most</c><00:02:26.160><c> natural</c><00:02:26.760><c> way</c><00:02:27.080><c> is</c><00:02:27.239><c> to</c><00:02:27.519><c> just</c>

00:02:27.750 --> 00:02:27.760 align:start position:0%
probably the most natural way is to just
 

00:02:27.760 --> 00:02:31.270 align:start position:0%
probably the most natural way is to just
use<00:02:28.360><c> spaces</c><00:02:29.360><c> as</c><00:02:29.480><c> a</c><00:02:29.599><c> way</c><00:02:29.879><c> of</c><00:02:30.040><c> indicating</c><00:02:30.680><c> the</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
use spaces as a way of indicating the
 

00:02:31.280 --> 00:02:33.710 align:start position:0%
use spaces as a way of indicating the
tokens<00:02:32.280><c> however</c><00:02:32.480><c> if</c><00:02:32.599><c> you</c><00:02:32.760><c> think</c><00:02:32.959><c> about</c><00:02:33.360><c> all</c><00:02:33.560><c> of</c>

00:02:33.710 --> 00:02:33.720 align:start position:0%
tokens however if you think about all of
 

00:02:33.720 --> 00:02:36.470 align:start position:0%
tokens however if you think about all of
the<00:02:33.879><c> text</c><00:02:34.360><c> that</c><00:02:34.519><c> is</c><00:02:34.680><c> on</c><00:02:34.879><c> the</c><00:02:35.080><c> internet</c><00:02:35.959><c> and</c><00:02:36.160><c> on</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
the text that is on the internet and on
 

00:02:36.480 --> 00:02:39.430 align:start position:0%
the text that is on the internet and on
GitHub<00:02:37.319><c> and</c><00:02:37.480><c> in</c><00:02:37.640><c> wikkipedia</c><00:02:38.200><c> and</c><00:02:39.200><c> just</c>

00:02:39.430 --> 00:02:39.440 align:start position:0%
GitHub and in wikkipedia and just
 

00:02:39.440 --> 00:02:42.470 align:start position:0%
GitHub and in wikkipedia and just
splitting<00:02:39.920><c> it</c><00:02:40.080><c> on</c><00:02:40.680><c> Spaces</c><00:02:41.680><c> that</c><00:02:42.159><c> may</c>

00:02:42.470 --> 00:02:42.480 align:start position:0%
splitting it on Spaces that may
 

00:02:42.480 --> 00:02:45.229 align:start position:0%
splitting it on Spaces that may
potentially<00:02:43.040><c> result</c><00:02:43.400><c> in</c><00:02:43.680><c> millions</c><00:02:44.159><c> of</c><00:02:44.360><c> tokens</c>

00:02:45.229 --> 00:02:45.239 align:start position:0%
potentially result in millions of tokens
 

00:02:45.239 --> 00:02:47.149 align:start position:0%
potentially result in millions of tokens
and<00:02:45.400><c> we</c><00:02:45.560><c> know</c><00:02:45.840><c> that</c><00:02:46.000><c> we</c><00:02:46.120><c> need</c><00:02:46.239><c> to</c><00:02:46.400><c> have</c><00:02:46.519><c> a</c><00:02:46.720><c> set</c>

00:02:47.149 --> 00:02:47.159 align:start position:0%
and we know that we need to have a set
 

00:02:47.159 --> 00:02:49.630 align:start position:0%
and we know that we need to have a set
vocabulary<00:02:47.879><c> size</c><00:02:48.319><c> because</c><00:02:48.640><c> this</c><00:02:48.760><c> is</c><00:02:49.000><c> how</c><00:02:49.519><c> the</c>

00:02:49.630 --> 00:02:49.640 align:start position:0%
vocabulary size because this is how the
 

00:02:49.640 --> 00:02:53.110 align:start position:0%
vocabulary size because this is how the
llm<00:02:50.159><c> is</c><00:02:50.400><c> predicting</c><00:02:51.400><c> the</c><00:02:51.599><c> output</c><00:02:52.159><c> probab</c>

00:02:53.110 --> 00:02:53.120 align:start position:0%
llm is predicting the output probab
 

00:02:53.120 --> 00:02:56.070 align:start position:0%
llm is predicting the output probab
probabilities<00:02:54.120><c> so</c><00:02:54.319><c> we</c><00:02:54.440><c> need</c><00:02:54.599><c> to</c><00:02:55.120><c> contain</c><00:02:55.879><c> the</c>

00:02:56.070 --> 00:02:56.080 align:start position:0%
probabilities so we need to contain the
 

00:02:56.080 --> 00:02:58.149 align:start position:0%
probabilities so we need to contain the
size<00:02:56.280><c> of</c><00:02:56.440><c> our</c><00:02:56.720><c> vocabulary</c><00:02:57.599><c> and</c><00:02:57.760><c> for</c><00:02:58.000><c> that</c>

00:02:58.149 --> 00:02:58.159 align:start position:0%
size of our vocabulary and for that
 

00:02:58.159 --> 00:03:00.550 align:start position:0%
size of our vocabulary and for that
reason<00:02:58.720><c> some</c><00:02:59.280><c> some</c><00:02:59.440><c> of</c><00:02:59.560><c> the</c><00:02:59.840><c> words</c><00:03:00.239><c> that</c><00:03:00.360><c> we</c>

00:03:00.550 --> 00:03:00.560 align:start position:0%
reason some some of the words that we
 

00:03:00.560 --> 00:03:03.270 align:start position:0%
reason some some of the words that we
have<00:03:00.840><c> maybe</c><00:03:01.200><c> split</c><00:03:01.680><c> into</c><00:03:02.120><c> into</c><00:03:02.360><c> subwords</c><00:03:03.000><c> into</c>

00:03:03.270 --> 00:03:03.280 align:start position:0%
have maybe split into into subwords into
 

00:03:03.280 --> 00:03:07.190 align:start position:0%
have maybe split into into subwords into
units<00:03:04.280><c> and</c><00:03:04.840><c> this</c><00:03:04.959><c> will</c><00:03:05.200><c> create</c><00:03:05.840><c> tokens</c><00:03:06.840><c> so</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
units and this will create tokens so
 

00:03:07.200 --> 00:03:09.670 align:start position:0%
units and this will create tokens so
let's<00:03:07.480><c> run</c><00:03:07.840><c> this</c><00:03:08.040><c> and</c><00:03:08.159><c> see</c><00:03:08.400><c> what</c><00:03:08.599><c> happens</c><00:03:09.560><c> by</c>

00:03:09.670 --> 00:03:09.680 align:start position:0%
let's run this and see what happens by
 

00:03:09.680 --> 00:03:11.750 align:start position:0%
let's run this and see what happens by
the<00:03:09.799><c> way</c><00:03:10.319><c> different</c><00:03:10.720><c> models</c><00:03:11.319><c> may</c><00:03:11.560><c> have</c>

00:03:11.750 --> 00:03:11.760 align:start position:0%
the way different models may have
 

00:03:11.760 --> 00:03:13.869 align:start position:0%
the way different models may have
different<00:03:12.120><c> tokenizers</c><00:03:13.120><c> so</c><00:03:13.319><c> in</c><00:03:13.480><c> this</c><00:03:13.680><c> case</c>

00:03:13.869 --> 00:03:13.879 align:start position:0%
different tokenizers so in this case
 

00:03:13.879 --> 00:03:16.190 align:start position:0%
different tokenizers so in this case
we're<00:03:14.120><c> picking</c><00:03:14.400><c> a</c><00:03:14.560><c> tokenizer</c><00:03:15.239><c> for</c><00:03:15.760><c> text</c><00:03:16.080><c> Da</c>

00:03:16.190 --> 00:03:16.200 align:start position:0%
we're picking a tokenizer for text Da
 

00:03:16.200 --> 00:03:20.309 align:start position:0%
we're picking a tokenizer for text Da
Vinci<00:03:16.560><c> 003</c><00:03:17.640><c> model</c><00:03:18.640><c> um</c><00:03:19.280><c> but</c><00:03:19.720><c> uh</c><00:03:19.840><c> in</c><00:03:20.000><c> case</c><00:03:20.159><c> you're</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
Vinci 003 model um but uh in case you're
 

00:03:20.319 --> 00:03:21.990 align:start position:0%
Vinci 003 model um but uh in case you're
using<00:03:20.599><c> a</c><00:03:20.760><c> different</c><00:03:21.040><c> model</c><00:03:21.400><c> you</c><00:03:21.519><c> may</c><00:03:21.680><c> need</c><00:03:21.799><c> to</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
using a different model you may need to
 

00:03:22.000 --> 00:03:23.550 align:start position:0%
using a different model you may need to
pick<00:03:22.319><c> a</c><00:03:22.560><c> different</c>

00:03:23.550 --> 00:03:23.560 align:start position:0%
pick a different
 

00:03:23.560 --> 00:03:26.670 align:start position:0%
pick a different
tokenizer<00:03:24.560><c> so</c><00:03:25.080><c> we</c><00:03:25.159><c> can</c><00:03:25.319><c> see</c><00:03:25.560><c> now</c><00:03:26.120><c> the</c><00:03:26.360><c> text</c>

00:03:26.670 --> 00:03:26.680 align:start position:0%
tokenizer so we can see now the text
 

00:03:26.680 --> 00:03:30.270 align:start position:0%
tokenizer so we can see now the text
weights<00:03:26.920><c> Andes</c><00:03:27.400><c> is</c><00:03:27.640><c> awesome</c><00:03:28.640><c> is</c><00:03:29.319><c> a</c><00:03:29.439><c> com</c><00:03:29.920><c> ation</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
weights Andes is awesome is a com ation
 

00:03:30.280 --> 00:03:33.710 align:start position:0%
weights Andes is awesome is a com ation
of<00:03:30.680><c> tokens</c><00:03:31.360><c> with</c><00:03:31.519><c> the</c><00:03:31.720><c> following</c><00:03:32.439><c> uh</c><00:03:32.720><c> numbers</c>

00:03:33.710 --> 00:03:33.720 align:start position:0%
of tokens with the following uh numbers
 

00:03:33.720 --> 00:03:36.030 align:start position:0%
of tokens with the following uh numbers
and<00:03:34.000><c> we</c><00:03:34.120><c> can</c><00:03:34.239><c> see</c><00:03:34.480><c> this</c><00:03:34.599><c> is</c><00:03:34.720><c> a</c><00:03:34.920><c> list</c><00:03:35.159><c> of</c>

00:03:36.030 --> 00:03:36.040 align:start position:0%
and we can see this is a list of
 

00:03:36.040 --> 00:03:39.149 align:start position:0%
and we can see this is a list of
numbers<00:03:37.040><c> uh</c><00:03:37.319><c> but</c><00:03:37.640><c> uh</c><00:03:38.040><c> that's</c><00:03:38.239><c> still</c><00:03:38.959><c> difficult</c>

00:03:39.149 --> 00:03:39.159 align:start position:0%
numbers uh but uh that's still difficult
 

00:03:39.159 --> 00:03:41.190 align:start position:0%
numbers uh but uh that's still difficult
to<00:03:39.599><c> understand</c><00:03:39.760><c> what</c><00:03:39.879><c> the</c><00:03:40.000><c> tokens</c><00:03:40.360><c> are</c><00:03:40.959><c> so</c><00:03:41.120><c> we</c>

00:03:41.190 --> 00:03:41.200 align:start position:0%
to understand what the tokens are so we
 

00:03:41.200 --> 00:03:44.429 align:start position:0%
to understand what the tokens are so we
can<00:03:41.439><c> decode</c><00:03:42.040><c> the</c><00:03:42.200><c> tokens</c><00:03:42.599><c> one</c><00:03:42.799><c> by</c><00:03:43.000><c> one</c><00:03:43.720><c> and</c><00:03:43.879><c> see</c>

00:03:44.429 --> 00:03:44.439 align:start position:0%
can decode the tokens one by one and see
 

00:03:44.439 --> 00:03:47.350 align:start position:0%
can decode the tokens one by one and see
what<00:03:44.760><c> each</c><00:03:45.000><c> of</c><00:03:45.120><c> the</c><00:03:45.280><c> values</c><00:03:45.720><c> corresponds</c><00:03:46.360><c> to</c>

00:03:47.350 --> 00:03:47.360 align:start position:0%
what each of the values corresponds to
 

00:03:47.360 --> 00:03:50.070 align:start position:0%
what each of the values corresponds to
and<00:03:47.480><c> we</c><00:03:47.599><c> can</c><00:03:47.720><c> see</c><00:03:48.000><c> the</c><00:03:48.159><c> word</c><00:03:48.640><c> we</c><00:03:49.360><c> is</c><00:03:49.519><c> a</c><00:03:49.680><c> separate</c>

00:03:50.070 --> 00:03:50.080 align:start position:0%
and we can see the word we is a separate
 

00:03:50.080 --> 00:03:52.030 align:start position:0%
and we can see the word we is a separate
token<00:03:51.040><c> and</c>

00:03:52.030 --> 00:03:52.040 align:start position:0%
token and
 

00:03:52.040 --> 00:03:55.069 align:start position:0%
token and
then<00:03:53.040><c> I</c><00:03:53.560><c> so</c><00:03:53.840><c> weights</c><00:03:54.239><c> actually</c><00:03:54.519><c> was</c><00:03:54.720><c> split</c>

00:03:55.069 --> 00:03:55.079 align:start position:0%
then I so weights actually was split
 

00:03:55.079 --> 00:03:57.670 align:start position:0%
then I so weights actually was split
into<00:03:55.360><c> two</c><00:03:55.760><c> tokens</c><00:03:56.760><c> uh</c><00:03:56.920><c> with</c><00:03:57.079><c> the</c><00:03:57.200><c> following</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
into two tokens uh with the following
 

00:03:57.680 --> 00:04:01.670 align:start position:0%
into two tokens uh with the following
numbers<00:03:58.640><c> then</c><00:03:59.120><c> the</c><00:03:59.280><c> ERS</c><00:03:59.760><c> s</c><00:04:00.239><c> is</c><00:04:00.519><c> its</c><00:04:00.680><c> own</c><00:04:00.920><c> token</c>

00:04:01.670 --> 00:04:01.680 align:start position:0%
numbers then the ERS s is its own token
 

00:04:01.680 --> 00:04:04.509 align:start position:0%
numbers then the ERS s is its own token
then<00:04:02.040><c> biases</c><00:04:03.000><c> is</c><00:04:03.319><c> again</c><00:04:03.799><c> like</c><00:04:03.959><c> combined</c><00:04:04.360><c> of</c>

00:04:04.509 --> 00:04:04.519 align:start position:0%
then biases is again like combined of
 

00:04:04.519 --> 00:04:07.789 align:start position:0%
then biases is again like combined of
two<00:04:04.720><c> tokens</c><00:04:05.599><c> is</c><00:04:06.599><c> H</c><00:04:06.720><c> is</c><00:04:06.840><c> a</c><00:04:07.000><c> single</c><00:04:07.280><c> token</c>

00:04:07.789 --> 00:04:07.799 align:start position:0%
two tokens is H is a single token
 

00:04:07.799 --> 00:04:10.550 align:start position:0%
two tokens is H is a single token
Awesome<00:04:08.360><c> Again</c><00:04:08.560><c> a</c><00:04:08.760><c> popular</c><00:04:09.159><c> word</c><00:04:09.599><c> as</c><00:04:09.760><c> a</c><00:04:09.959><c> token</c>

00:04:10.550 --> 00:04:10.560 align:start position:0%
Awesome Again a popular word as a token
 

00:04:10.560 --> 00:04:13.949 align:start position:0%
Awesome Again a popular word as a token
and<00:04:11.280><c> the</c><00:04:11.480><c> exclamation</c><00:04:12.040><c> mark</c><00:04:12.599><c> is</c><00:04:12.840><c> also</c><00:04:13.120><c> a</c><00:04:13.280><c> token</c>

00:04:13.949 --> 00:04:13.959 align:start position:0%
and the exclamation mark is also a token
 

00:04:13.959 --> 00:04:16.069 align:start position:0%
and the exclamation mark is also a token
with<00:04:14.079><c> a</c><00:04:14.239><c> value</c><00:04:14.560><c> zero</c><00:04:14.920><c> it's</c><00:04:15.079><c> interesting</c><00:04:15.680><c> that</c>

00:04:16.069 --> 00:04:16.079 align:start position:0%
with a value zero it's interesting that
 

00:04:16.079 --> 00:04:18.590 align:start position:0%
with a value zero it's interesting that
the<00:04:16.199><c> whole</c><00:04:16.479><c> vocabulary</c><00:04:17.079><c> of</c><00:04:17.239><c> token</c><00:04:18.120><c> tokens</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
the whole vocabulary of token tokens
 

00:04:18.600 --> 00:04:21.189 align:start position:0%
the whole vocabulary of token tokens
starts<00:04:19.040><c> with</c><00:04:19.199><c> the</c><00:04:19.479><c> exclamation</c>

00:04:21.189 --> 00:04:21.199 align:start position:0%
starts with the exclamation
 

00:04:21.199 --> 00:04:23.670 align:start position:0%
starts with the exclamation
mark<00:04:22.199><c> and</c><00:04:22.400><c> you</c><00:04:22.520><c> can</c><00:04:22.720><c> also</c><00:04:22.919><c> see</c><00:04:23.160><c> that</c><00:04:23.320><c> some</c><00:04:23.479><c> of</c>

00:04:23.670 --> 00:04:23.680 align:start position:0%
mark and you can also see that some of
 

00:04:23.680 --> 00:04:27.430 align:start position:0%
mark and you can also see that some of
these<00:04:23.840><c> tokens</c><00:04:24.680><c> contain</c><00:04:25.160><c> spacing</c><00:04:26.040><c> so</c><00:04:26.919><c> uh</c><00:04:27.240><c> and</c>

00:04:27.430 --> 00:04:27.440 align:start position:0%
these tokens contain spacing so uh and
 

00:04:27.440 --> 00:04:29.670 align:start position:0%
these tokens contain spacing so uh and
that's<00:04:27.720><c> because</c><00:04:28.040><c> like</c><00:04:28.240><c> once</c><00:04:28.479><c> we</c><00:04:29.080><c> decode</c><00:04:29.479><c> this</c>

00:04:29.670 --> 00:04:29.680 align:start position:0%
that's because like once we decode this
 

00:04:29.680 --> 00:04:31.830 align:start position:0%
that's because like once we decode this
tokens<00:04:30.120><c> once</c><00:04:30.320><c> we</c><00:04:30.720><c> um</c><00:04:31.120><c> once</c><00:04:31.280><c> we</c><00:04:31.400><c> want</c><00:04:31.560><c> to</c><00:04:31.680><c> put</c>

00:04:31.830 --> 00:04:31.840 align:start position:0%
tokens once we um once we want to put
 

00:04:31.840 --> 00:04:33.469 align:start position:0%
tokens once we um once we want to put
them<00:04:32.120><c> together</c><00:04:32.360><c> we</c><00:04:32.479><c> want</c><00:04:32.600><c> to</c><00:04:32.759><c> make</c><00:04:32.880><c> sure</c><00:04:33.120><c> that</c>

00:04:33.469 --> 00:04:33.479 align:start position:0%
them together we want to make sure that
 

00:04:33.479 --> 00:04:36.710 align:start position:0%
them together we want to make sure that
we<00:04:33.639><c> also</c><00:04:33.960><c> preserve</c><00:04:34.560><c> spaces</c><00:04:35.120><c> so</c><00:04:35.360><c> spaces</c><00:04:36.360><c> uh</c><00:04:36.479><c> are</c>

00:04:36.710 --> 00:04:36.720 align:start position:0%
we also preserve spaces so spaces uh are
 

00:04:36.720 --> 00:04:38.670 align:start position:0%
we also preserve spaces so spaces uh are
part<00:04:36.880><c> of</c><00:04:37.039><c> the</c><00:04:37.199><c> tokens</c><00:04:37.720><c> and</c><00:04:38.039><c> maybe</c><00:04:38.320><c> its</c><00:04:38.440><c> own</c>

00:04:38.670 --> 00:04:38.680 align:start position:0%
part of the tokens and maybe its own
 

00:04:38.680 --> 00:04:48.870 align:start position:0%
part of the tokens and maybe its own
token<00:04:39.000><c> as</c>

00:04:48.870 --> 00:04:48.880 align:start position:0%
 
 

00:04:48.880 --> 00:04:51.880 align:start position:0%
 
well

