WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:03.990 align:start position:0%
 
this<00:00:00.960><c> is</c><00:00:01.560><c> autog</c><00:00:01.920><c> Gro</c><00:00:02.720><c> Lesson</c><00:00:03.040><c> Four</c><00:00:03.719><c> in</c><00:00:03.840><c> our</c>

00:00:03.990 --> 00:00:04.000 align:start position:0%
this is autog Gro Lesson Four in our
 

00:00:04.000 --> 00:00:06.349 align:start position:0%
this is autog Gro Lesson Four in our
freaking<00:00:04.359><c> video</c><00:00:04.680><c> tutorial</c><00:00:05.240><c> series</c><00:00:06.000><c> making</c>

00:00:06.349 --> 00:00:06.359 align:start position:0%
freaking video tutorial series making
 

00:00:06.359 --> 00:00:09.250 align:start position:0%
freaking video tutorial series making
agents<00:00:07.040><c> skills</c><00:00:08.000><c> and</c>

00:00:09.250 --> 00:00:09.260 align:start position:0%
agents skills and
 

00:00:09.260 --> 00:00:16.349 align:start position:0%
agents skills and
[Music]

00:00:16.349 --> 00:00:16.359 align:start position:0%
[Music]
 

00:00:16.359 --> 00:00:18.630 align:start position:0%
[Music]
workflows<00:00:17.359><c> at</c><00:00:17.480><c> first</c><00:00:17.720><c> autog</c><00:00:18.279><c> would</c><00:00:18.439><c> just</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
workflows at first autog would just
 

00:00:18.640 --> 00:00:21.429 align:start position:0%
workflows at first autog would just
accept<00:00:18.880><c> a</c><00:00:19.080><c> prompt</c><00:00:19.480><c> and</c><00:00:19.640><c> spit</c><00:00:19.880><c> out</c><00:00:20.279><c> agents</c><00:00:21.279><c> I</c>

00:00:21.429 --> 00:00:21.439 align:start position:0%
accept a prompt and spit out agents I
 

00:00:21.439 --> 00:00:23.230 align:start position:0%
accept a prompt and spit out agents I
called<00:00:21.640><c> it</c><00:00:21.840><c> Auto</c><00:00:22.199><c> Experts</c><00:00:22.600><c> or</c><00:00:22.760><c> something</c><00:00:23.080><c> like</c>

00:00:23.230 --> 00:00:23.240 align:start position:0%
called it Auto Experts or something like
 

00:00:23.240 --> 00:00:25.630 align:start position:0%
called it Auto Experts or something like
that<00:00:23.439><c> we've</c><00:00:23.599><c> come</c><00:00:23.760><c> a</c><00:00:24.279><c> way</c><00:00:25.039><c> today</c><00:00:25.359><c> you</c><00:00:25.480><c> can</c>

00:00:25.630 --> 00:00:25.640 align:start position:0%
that we've come a way today you can
 

00:00:25.640 --> 00:00:27.630 align:start position:0%
that we've come a way today you can
configure<00:00:26.160><c> almost</c><00:00:26.480><c> any</c><00:00:26.760><c> provider</c><00:00:27.240><c> select</c>

00:00:27.630 --> 00:00:27.640 align:start position:0%
configure almost any provider select
 

00:00:27.640 --> 00:00:29.310 align:start position:0%
configure almost any provider select
almost<00:00:27.960><c> any</c><00:00:28.160><c> model</c><00:00:28.480><c> set</c><00:00:28.640><c> the</c><00:00:28.800><c> temperature</c><00:00:29.160><c> and</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
almost any model set the temperature and
 

00:00:29.320 --> 00:00:30.550 align:start position:0%
almost any model set the temperature and
type<00:00:29.480><c> in</c><00:00:29.560><c> your</c><00:00:29.679><c> request</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
type in your request
 

00:00:30.560 --> 00:00:32.150 align:start position:0%
type in your request
the<00:00:30.679><c> first</c><00:00:30.920><c> thing</c><00:00:31.039><c> you'll</c><00:00:31.279><c> see</c><00:00:31.560><c> is</c><00:00:31.679><c> that</c><00:00:31.800><c> autog</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
the first thing you'll see is that autog
 

00:00:32.160 --> 00:00:34.709 align:start position:0%
the first thing you'll see is that autog
Gro<00:00:32.759><c> very</c><00:00:33.040><c> quickly</c><00:00:33.760><c> created</c><00:00:34.200><c> these</c><00:00:34.440><c> eight</c>

00:00:34.709 --> 00:00:34.719 align:start position:0%
Gro very quickly created these eight
 

00:00:34.719 --> 00:00:36.470 align:start position:0%
Gro very quickly created these eight
agents<00:00:35.200><c> as</c><00:00:35.320><c> well</c><00:00:35.480><c> as</c><00:00:35.680><c> this</c><00:00:35.840><c> commentary</c><00:00:36.320><c> from</c>

00:00:36.470 --> 00:00:36.480 align:start position:0%
agents as well as this commentary from
 

00:00:36.480 --> 00:00:38.470 align:start position:0%
agents as well as this commentary from
our<00:00:36.719><c> project</c><00:00:37.040><c> manager</c><00:00:37.800><c> that</c><00:00:38.000><c> includes</c><00:00:38.320><c> a</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
our project manager that includes a
 

00:00:38.480 --> 00:00:40.389 align:start position:0%
our project manager that includes a
project<00:00:38.840><c> outline</c><00:00:39.360><c> including</c><00:00:39.719><c> an</c><00:00:39.920><c> objective</c>

00:00:40.389 --> 00:00:40.399 align:start position:0%
project outline including an objective
 

00:00:40.399 --> 00:00:42.470 align:start position:0%
project outline including an objective
and<00:00:40.600><c> key</c><00:00:40.879><c> deliverables</c><00:00:41.800><c> what</c><00:00:41.920><c> you</c><00:00:42.079><c> don't</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
and key deliverables what you don't
 

00:00:42.480 --> 00:00:44.229 align:start position:0%
and key deliverables what you don't
immediately<00:00:43.000><c> notice</c><00:00:43.399><c> is</c><00:00:43.520><c> that</c><00:00:43.680><c> our</c><00:00:43.879><c> prompt</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
immediately notice is that our prompt
 

00:00:44.239 --> 00:00:45.790 align:start position:0%
immediately notice is that our prompt
was<00:00:44.480><c> professionally</c><00:00:44.960><c> re-engineered</c><00:00:45.600><c> in</c><00:00:45.680><c> the</c>

00:00:45.790 --> 00:00:45.800 align:start position:0%
was professionally re-engineered in the
 

00:00:45.800 --> 00:00:47.790 align:start position:0%
was professionally re-engineered in the
background<00:00:46.480><c> and</c><00:00:46.640><c> stored</c><00:00:47.000><c> as</c><00:00:47.120><c> our</c><00:00:47.360><c> project's</c>

00:00:47.790 --> 00:00:47.800 align:start position:0%
background and stored as our project's
 

00:00:47.800 --> 00:00:49.830 align:start position:0%
background and stored as our project's
goal<00:00:48.480><c> this</c><00:00:48.680><c> text</c><00:00:49.079><c> and</c><00:00:49.239><c> the</c><00:00:49.360><c> ongoing</c>

00:00:49.830 --> 00:00:49.840 align:start position:0%
goal this text and the ongoing
 

00:00:49.840 --> 00:00:51.950 align:start position:0%
goal this text and the ongoing
discussion<00:00:50.640><c> is</c><00:00:50.840><c> constantly</c><00:00:51.280><c> referenced</c><00:00:51.800><c> by</c>

00:00:51.950 --> 00:00:51.960 align:start position:0%
discussion is constantly referenced by
 

00:00:51.960 --> 00:00:53.950 align:start position:0%
discussion is constantly referenced by
our<00:00:52.160><c> agents</c><00:00:52.520><c> to</c><00:00:52.680><c> keep</c><00:00:52.879><c> them</c><00:00:53.039><c> focused</c><00:00:53.480><c> on</c><00:00:53.680><c> the</c>

00:00:53.950 --> 00:00:53.960 align:start position:0%
our agents to keep them focused on the
 

00:00:53.960 --> 00:00:56.389 align:start position:0%
our agents to keep them focused on the
specifics<00:00:54.440><c> of</c><00:00:54.559><c> the</c><00:00:54.719><c> task</c><00:00:55.000><c> at</c><00:00:55.160><c> hand</c><00:00:55.719><c> so</c><00:00:56.000><c> now</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
specifics of the task at hand so now
 

00:00:56.399 --> 00:00:58.069 align:start position:0%
specifics of the task at hand so now
when<00:00:56.559><c> I</c><00:00:56.760><c> click</c><00:00:57.000><c> on</c><00:00:57.160><c> the</c><00:00:57.320><c> next</c><00:00:57.600><c> agent</c><00:00:57.960><c> it's</c>

00:00:58.069 --> 00:00:58.079 align:start position:0%
when I click on the next agent it's
 

00:00:58.079 --> 00:01:00.310 align:start position:0%
when I click on the next agent it's
going<00:00:58.239><c> to</c><00:00:58.440><c> respond</c><00:00:58.840><c> to</c><00:00:59.000><c> our</c><00:00:59.280><c> project</c><00:00:59.559><c> manager</c>

00:01:00.310 --> 00:01:00.320 align:start position:0%
going to respond to our project manager
 

00:01:00.320 --> 00:01:02.709 align:start position:0%
going to respond to our project manager
with<00:01:00.440><c> a</c><00:01:00.640><c> focus</c><00:01:00.920><c> on</c><00:01:01.120><c> our</c><00:01:01.359><c> goal</c><00:01:02.120><c> this</c><00:01:02.280><c> next</c><00:01:02.519><c> part</c>

00:01:02.709 --> 00:01:02.719 align:start position:0%
with a focus on our goal this next part
 

00:01:02.719 --> 00:01:04.509 align:start position:0%
with a focus on our goal this next part
is<00:01:02.879><c> very</c><00:01:03.120><c> cool</c><00:01:03.680><c> having</c><00:01:03.920><c> thought</c><00:01:04.159><c> about</c><00:01:04.400><c> what</c>

00:01:04.509 --> 00:01:04.519 align:start position:0%
is very cool having thought about what
 

00:01:04.519 --> 00:01:05.830 align:start position:0%
is very cool having thought about what
their<00:01:04.760><c> specific</c><00:01:05.159><c> involvement</c><00:01:05.560><c> in</c><00:01:05.680><c> the</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
their specific involvement in the
 

00:01:05.840 --> 00:01:07.710 align:start position:0%
their specific involvement in the
project<00:01:06.119><c> will</c><00:01:06.320><c> be</c><00:01:06.760><c> this</c><00:01:06.920><c> agent</c><00:01:07.200><c> can</c><00:01:07.360><c> reflect</c>

00:01:07.710 --> 00:01:07.720 align:start position:0%
project will be this agent can reflect
 

00:01:07.720 --> 00:01:09.429 align:start position:0%
project will be this agent can reflect
upon<00:01:07.960><c> the</c><00:01:08.119><c> enhanced</c><00:01:08.520><c> goal</c><00:01:09.000><c> the</c><00:01:09.159><c> project</c>

00:01:09.429 --> 00:01:09.439 align:start position:0%
upon the enhanced goal the project
 

00:01:09.439 --> 00:01:11.510 align:start position:0%
upon the enhanced goal the project
manager's<00:01:09.960><c> instructions</c><00:01:10.920><c> and</c><00:01:11.080><c> its</c><00:01:11.240><c> own</c>

00:01:11.510 --> 00:01:11.520 align:start position:0%
manager's instructions and its own
 

00:01:11.520 --> 00:01:13.670 align:start position:0%
manager's instructions and its own
feedback<00:01:12.040><c> and</c><00:01:12.200><c> redefine</c><00:01:12.759><c> itself</c><00:01:13.159><c> as</c><00:01:13.280><c> an</c><00:01:13.439><c> even</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
feedback and redefine itself as an even
 

00:01:13.680 --> 00:01:16.109 align:start position:0%
feedback and redefine itself as an even
more<00:01:14.040><c> expertly</c><00:01:14.640><c> concise</c><00:01:15.119><c> AI</c><00:01:15.520><c> agent</c><00:01:16.000><c> what</c>

00:01:16.109 --> 00:01:16.119 align:start position:0%
more expertly concise AI agent what
 

00:01:16.119 --> 00:01:17.749 align:start position:0%
more expertly concise AI agent what
you're<00:01:16.280><c> witnessing</c><00:01:16.720><c> is</c><00:01:16.840><c> an</c><00:01:17.040><c> AI</c><00:01:17.400><c> agent</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
you're witnessing is an AI agent
 

00:01:17.759 --> 00:01:20.109 align:start position:0%
you're witnessing is an AI agent
demonstrating<00:01:18.479><c> rudimentary</c><00:01:19.119><c> introspection</c>

00:01:20.109 --> 00:01:20.119 align:start position:0%
demonstrating rudimentary introspection
 

00:01:20.119 --> 00:01:21.789 align:start position:0%
demonstrating rudimentary introspection
this<00:01:20.240><c> is</c><00:01:20.360><c> where</c><00:01:20.520><c> autog</c><00:01:20.880><c> Gro</c><00:01:21.200><c> has</c><00:01:21.320><c> carved</c><00:01:21.640><c> out</c>

00:01:21.789 --> 00:01:21.799 align:start position:0%
this is where autog Gro has carved out
 

00:01:21.799 --> 00:01:24.109 align:start position:0%
this is where autog Gro has carved out
its<00:01:21.960><c> own</c><00:01:22.400><c> unique</c><00:01:22.799><c> niche</c><00:01:23.159><c> in</c><00:01:23.280><c> the</c><00:01:23.360><c> world</c><00:01:23.560><c> of</c><00:01:23.720><c> AI</c>

00:01:24.109 --> 00:01:24.119 align:start position:0%
its own unique niche in the world of AI
 

00:01:24.119 --> 00:01:25.990 align:start position:0%
its own unique niche in the world of AI
programming<00:01:24.759><c> by</c><00:01:24.920><c> producing</c><00:01:25.439><c> goals</c>

00:01:25.990 --> 00:01:26.000 align:start position:0%
programming by producing goals
 

00:01:26.000 --> 00:01:28.429 align:start position:0%
programming by producing goals
benchmarks<00:01:26.759><c> objectives</c><00:01:27.280><c> deliverables</c><00:01:28.280><c> and</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
benchmarks objectives deliverables and
 

00:01:28.439 --> 00:01:29.990 align:start position:0%
benchmarks objectives deliverables and
providing<00:01:28.799><c> your</c><00:01:28.960><c> agents</c><00:01:29.240><c> a</c><00:01:29.360><c> forum</c><00:01:29.640><c> for</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
providing your agents a forum for
 

00:01:30.000 --> 00:01:32.030 align:start position:0%
providing your agents a forum for
enhancing<00:01:30.600><c> themselves</c><00:01:30.960><c> via</c><00:01:31.280><c> cooperation</c><00:01:31.840><c> and</c>

00:01:32.030 --> 00:01:32.040 align:start position:0%
enhancing themselves via cooperation and
 

00:01:32.040 --> 00:01:33.710 align:start position:0%
enhancing themselves via cooperation and
interaction<00:01:32.560><c> with</c><00:01:32.799><c> the</c><00:01:32.960><c> potential</c><00:01:33.280><c> for</c><00:01:33.479><c> this</c>

00:01:33.710 --> 00:01:33.720 align:start position:0%
interaction with the potential for this
 

00:01:33.720 --> 00:01:35.870 align:start position:0%
interaction with the potential for this
project<00:01:34.000><c> as</c><00:01:34.119><c> it</c><00:01:34.280><c> matures</c><00:01:34.960><c> simply</c><00:01:35.280><c> can't</c><00:01:35.520><c> be</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
project as it matures simply can't be
 

00:01:35.880 --> 00:01:37.870 align:start position:0%
project as it matures simply can't be
overstated<00:01:36.880><c> I'll</c><00:01:37.079><c> quickly</c><00:01:37.360><c> mention</c><00:01:37.640><c> our</c>

00:01:37.870 --> 00:01:37.880 align:start position:0%
overstated I'll quickly mention our
 

00:01:37.880 --> 00:01:40.429 align:start position:0%
overstated I'll quickly mention our
whiteboard<00:01:38.680><c> autog</c><00:01:39.079><c> Gro</c><00:01:39.479><c> extracts</c><00:01:39.920><c> any</c><00:01:40.159><c> code</c>

00:01:40.429 --> 00:01:40.439 align:start position:0%
whiteboard autog Gro extracts any code
 

00:01:40.439 --> 00:01:42.510 align:start position:0%
whiteboard autog Gro extracts any code
from<00:01:40.600><c> the</c><00:01:40.759><c> conversation</c><00:01:41.479><c> and</c><00:01:41.640><c> parses</c><00:01:42.040><c> it</c><00:01:42.200><c> into</c>

00:01:42.510 --> 00:01:42.520 align:start position:0%
from the conversation and parses it into
 

00:01:42.520 --> 00:01:44.870 align:start position:0%
from the conversation and parses it into
this<00:01:42.720><c> tab</c><00:01:43.000><c> for</c><00:01:43.200><c> easy</c><00:01:43.600><c> access</c><00:01:44.479><c> I</c><00:01:44.600><c> hope</c><00:01:44.759><c> to</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
this tab for easy access I hope to
 

00:01:44.880 --> 00:01:46.749 align:start position:0%
this tab for easy access I hope to
enable<00:01:45.240><c> our</c><00:01:45.399><c> whiteboard</c><00:01:45.920><c> to</c><00:01:46.119><c> validate</c><00:01:46.560><c> and</c>

00:01:46.749 --> 00:01:46.759 align:start position:0%
enable our whiteboard to validate and
 

00:01:46.759 --> 00:01:50.310 align:start position:0%
enable our whiteboard to validate and
run<00:01:47.079><c> code</c><00:01:47.920><c> someday</c><00:01:48.759><c> I</c><00:01:48.920><c> hope</c><00:01:49.680><c> our</c><00:01:49.880><c> discussion</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
run code someday I hope our discussion
 

00:01:50.320 --> 00:01:52.389 align:start position:0%
run code someday I hope our discussion
tab<00:01:50.600><c> offers</c><00:01:50.880><c> a</c><00:01:51.040><c> nicely</c><00:01:51.439><c> formatted</c><00:01:51.960><c> record</c><00:01:52.240><c> of</c>

00:01:52.389 --> 00:01:52.399 align:start position:0%
tab offers a nicely formatted record of
 

00:01:52.399 --> 00:01:54.590 align:start position:0%
tab offers a nicely formatted record of
our<00:01:52.560><c> agent's</c><00:01:52.960><c> entire</c><00:01:53.399><c> conversation</c><00:01:54.360><c> down</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
our agent's entire conversation down
 

00:01:54.600 --> 00:01:55.830 align:start position:0%
our agent's entire conversation down
here<00:01:54.759><c> are</c><00:01:54.960><c> the</c><00:01:55.119><c> options</c><00:01:55.399><c> for</c><00:01:55.640><c> either</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
here are the options for either
 

00:01:55.840 --> 00:01:57.550 align:start position:0%
here are the options for either
downloading<00:01:56.280><c> our</c><00:01:56.479><c> agents</c><00:01:56.920><c> workflows</c><00:01:57.399><c> and</c>

00:01:57.550 --> 00:01:57.560 align:start position:0%
downloading our agents workflows and
 

00:01:57.560 --> 00:01:59.950 align:start position:0%
downloading our agents workflows and
skill<00:01:57.880><c> files</c><00:01:58.200><c> as</c><00:01:58.399><c> zip</c><00:01:58.640><c> archives</c><00:01:59.360><c> or</c><00:01:59.479><c> sending</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
skill files as zip archives or sending
 

00:01:59.960 --> 00:02:01.510 align:start position:0%
skill files as zip archives or sending
them<00:02:00.119><c> directly</c><00:02:00.399><c> to</c><00:02:00.560><c> our</c><00:02:00.680><c> local</c><00:02:01.000><c> install</c><00:02:01.399><c> of</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
them directly to our local install of
 

00:02:01.520 --> 00:02:03.789 align:start position:0%
them directly to our local install of
the<00:02:01.600><c> autogen</c><00:02:02.280><c> app</c><00:02:03.079><c> and</c><00:02:03.280><c> that's</c><00:02:03.479><c> what</c><00:02:03.640><c> I've</c>

00:02:03.789 --> 00:02:03.799 align:start position:0%
the autogen app and that's what I've
 

00:02:03.799 --> 00:02:05.630 align:start position:0%
the autogen app and that's what I've
done<00:02:04.320><c> you'll</c><00:02:04.520><c> see</c><00:02:04.680><c> our</c><00:02:04.840><c> workflow</c><00:02:05.320><c> has</c><00:02:05.479><c> been</c>

00:02:05.630 --> 00:02:05.640 align:start position:0%
done you'll see our workflow has been
 

00:02:05.640 --> 00:02:07.950 align:start position:0%
done you'll see our workflow has been
created<00:02:06.000><c> in</c><00:02:06.119><c> autogen</c><00:02:06.960><c> and</c><00:02:07.119><c> inside</c><00:02:07.479><c> we</c><00:02:07.600><c> find</c>

00:02:07.950 --> 00:02:07.960 align:start position:0%
created in autogen and inside we find
 

00:02:07.960 --> 00:02:09.469 align:start position:0%
created in autogen and inside we find
all<00:02:08.080><c> the</c><00:02:08.200><c> agents</c><00:02:08.599><c> that</c><00:02:08.720><c> were</c><00:02:08.920><c> created</c><00:02:09.280><c> and</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
all the agents that were created and
 

00:02:09.479 --> 00:02:11.869 align:start position:0%
all the agents that were created and
defined<00:02:09.840><c> for</c><00:02:10.000><c> us</c><00:02:10.160><c> in</c><00:02:10.480><c> autog</c><00:02:11.480><c> if</c><00:02:11.599><c> you</c><00:02:11.720><c> think</c>

00:02:11.869 --> 00:02:11.879 align:start position:0%
defined for us in autog if you think
 

00:02:11.879 --> 00:02:14.229 align:start position:0%
defined for us in autog if you think
making<00:02:12.239><c> agents</c><00:02:12.640><c> went</c><00:02:12.959><c> quickly</c><00:02:13.760><c> wait</c><00:02:13.959><c> until</c>

00:02:14.229 --> 00:02:14.239 align:start position:0%
making agents went quickly wait until
 

00:02:14.239 --> 00:02:16.710 align:start position:0%
making agents went quickly wait until
you<00:02:14.360><c> see</c><00:02:14.599><c> how</c><00:02:14.800><c> easy</c><00:02:15.040><c> it</c><00:02:15.160><c> is</c><00:02:15.319><c> to</c><00:02:15.519><c> add</c><00:02:15.879><c> skills</c><00:02:16.200><c> to</c>

00:02:16.710 --> 00:02:16.720 align:start position:0%
you see how easy it is to add skills to
 

00:02:16.720 --> 00:02:19.070 align:start position:0%
you see how easy it is to add skills to
autogen<00:02:17.720><c> we've</c><00:02:17.959><c> packaged</c><00:02:18.319><c> a</c><00:02:18.440><c> dozen</c><00:02:18.680><c> or</c><00:02:18.840><c> so</c>

00:02:19.070 --> 00:02:19.080 align:start position:0%
autogen we've packaged a dozen or so
 

00:02:19.080 --> 00:02:21.030 align:start position:0%
autogen we've packaged a dozen or so
open-<00:02:19.400><c> Source</c><00:02:19.640><c> skills</c><00:02:19.959><c> in</c><00:02:20.120><c> autogo</c><00:02:20.680><c> to</c><00:02:20.800><c> get</c><00:02:20.920><c> you</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
open- Source skills in autogo to get you
 

00:02:21.040 --> 00:02:23.270 align:start position:0%
open- Source skills in autogo to get you
started<00:02:21.680><c> you</c><00:02:21.800><c> can</c><00:02:21.959><c> select</c><00:02:22.319><c> one</c><00:02:22.599><c> or</c><00:02:22.760><c> a</c><00:02:22.879><c> few</c><00:02:23.120><c> or</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
started you can select one or a few or
 

00:02:23.280 --> 00:02:24.710 align:start position:0%
started you can select one or a few or
all<00:02:23.440><c> of</c><00:02:23.560><c> them</c><00:02:23.680><c> and</c><00:02:23.879><c> Export</c><00:02:24.160><c> them</c><00:02:24.280><c> in</c><00:02:24.400><c> the</c><00:02:24.480><c> blink</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
all of them and Export them in the blink
 

00:02:24.720 --> 00:02:27.309 align:start position:0%
all of them and Export them in the blink
of<00:02:24.840><c> a</c><00:02:24.959><c> button</c><00:02:25.319><c> I</c><00:02:25.400><c> mean</c><00:02:25.879><c> a</c><00:02:26.040><c> click</c><00:02:26.239><c> of</c><00:02:26.360><c> an</c><00:02:26.519><c> eye</c><00:02:27.080><c> a</c>

00:02:27.309 --> 00:02:27.319 align:start position:0%
of a button I mean a click of an eye a
 

00:02:27.319 --> 00:02:29.350 align:start position:0%
of a button I mean a click of an eye a
hell<00:02:27.560><c> you</c><00:02:27.680><c> know</c><00:02:27.800><c> what</c><00:02:27.920><c> I</c><00:02:28.040><c> mean</c><00:02:28.760><c> we'll</c><00:02:29.040><c> refresh</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
hell you know what I mean we'll refresh
 

00:02:29.360 --> 00:02:31.470 align:start position:0%
hell you know what I mean we'll refresh
our<00:02:29.480><c> browser</c><00:02:30.000><c> and</c><00:02:30.160><c> voila</c><00:02:30.640><c> there</c><00:02:30.800><c> they</c><00:02:30.920><c> are</c><00:02:31.360><c> a</c>

00:02:31.470 --> 00:02:31.480 align:start position:0%
our browser and voila there they are a
 

00:02:31.480 --> 00:02:33.030 align:start position:0%
our browser and voila there they are a
bunch<00:02:31.680><c> of</c><00:02:31.800><c> new</c><00:02:31.959><c> toys</c><00:02:32.239><c> for</c><00:02:32.400><c> all</c><00:02:32.519><c> your</c><00:02:32.680><c> robot</c>

00:02:33.030 --> 00:02:33.040 align:start position:0%
bunch of new toys for all your robot
 

00:02:33.040 --> 00:02:35.670 align:start position:0%
bunch of new toys for all your robot
kids<00:02:33.280><c> to</c><00:02:33.440><c> play</c><00:02:33.720><c> with</c><00:02:34.360><c> you</c><00:02:34.519><c> might</c><00:02:34.720><c> be</c><00:02:34.879><c> wondering</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
kids to play with you might be wondering
 

00:02:35.680 --> 00:02:37.589 align:start position:0%
kids to play with you might be wondering
but<00:02:35.879><c> Jay</c><00:02:36.200><c> you</c><00:02:36.400><c> handsome</c><00:02:36.840><c> genius</c><00:02:37.239><c> how</c><00:02:37.360><c> can</c><00:02:37.480><c> I</c>

00:02:37.589 --> 00:02:37.599 align:start position:0%
but Jay you handsome genius how can I
 

00:02:37.599 --> 00:02:40.229 align:start position:0%
but Jay you handsome genius how can I
make<00:02:37.879><c> new</c><00:02:38.200><c> skills</c><00:02:39.200><c> well</c><00:02:39.400><c> you</c><00:02:39.640><c> my</c><00:02:39.840><c> friend</c><00:02:40.080><c> are</c>

00:02:40.229 --> 00:02:40.239 align:start position:0%
make new skills well you my friend are
 

00:02:40.239 --> 00:02:43.110 align:start position:0%
make new skills well you my friend are
in<00:02:40.480><c> luck</c><00:02:41.239><c> just</c><00:02:41.480><c> click</c><00:02:41.720><c> on</c><00:02:42.159><c> add</c><00:02:42.319><c> a</c><00:02:42.560><c> skill</c><00:02:42.879><c> and</c>

00:02:43.110 --> 00:02:43.120 align:start position:0%
in luck just click on add a skill and
 

00:02:43.120 --> 00:02:44.790 align:start position:0%
in luck just click on add a skill and
type<00:02:43.360><c> in</c><00:02:43.599><c> what</c><00:02:43.720><c> it</c><00:02:43.840><c> is</c><00:02:44.000><c> you</c><00:02:44.120><c> want</c><00:02:44.319><c> your</c><00:02:44.480><c> skill</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
type in what it is you want your skill
 

00:02:44.800 --> 00:02:47.869 align:start position:0%
type in what it is you want your skill
to<00:02:44.959><c> do</c><00:02:45.879><c> auto</c><00:02:46.120><c> Gro</c><00:02:46.480><c> will</c><00:02:46.640><c> take</c><00:02:46.800><c> it</c><00:02:46.959><c> from</c><00:02:47.200><c> there</c>

00:02:47.869 --> 00:02:47.879 align:start position:0%
to do auto Gro will take it from there
 

00:02:47.879 --> 00:02:49.869 align:start position:0%
to do auto Gro will take it from there
we<00:02:48.040><c> just</c><00:02:48.200><c> added</c><00:02:48.480><c> this</c><00:02:48.640><c> feature</c><00:02:49.040><c> and</c><00:02:49.519><c> I</c><00:02:49.599><c> got</c><00:02:49.720><c> to</c>

00:02:49.869 --> 00:02:49.879 align:start position:0%
we just added this feature and I got to
 

00:02:49.879 --> 00:02:51.110 align:start position:0%
we just added this feature and I got to
admit<00:02:50.200><c> the</c><00:02:50.319><c> first</c><00:02:50.519><c> time</c><00:02:50.640><c> I</c><00:02:50.760><c> clicked</c><00:02:51.040><c> the</c>

00:02:51.110 --> 00:02:51.120 align:start position:0%
admit the first time I clicked the
 

00:02:51.120 --> 00:02:53.670 align:start position:0%
admit the first time I clicked the
button<00:02:51.360><c> and</c><00:02:51.480><c> it</c><00:02:51.560><c> worked</c><00:02:51.879><c> I</c><00:02:51.959><c> got</c><00:02:52.120><c> all</c><00:02:52.480><c> tingly</c><00:02:53.480><c> in</c>

00:02:53.670 --> 00:02:53.680 align:start position:0%
button and it worked I got all tingly in
 

00:02:53.680 --> 00:02:55.869 align:start position:0%
button and it worked I got all tingly in
my<00:02:53.959><c> happy</c><00:02:54.319><c> place</c><00:02:55.159><c> okay</c><00:02:55.400><c> that's</c><00:02:55.560><c> enough</c><00:02:55.760><c> for</c>

00:02:55.869 --> 00:02:55.879 align:start position:0%
my happy place okay that's enough for
 

00:02:55.879 --> 00:02:58.350 align:start position:0%
my happy place okay that's enough for
today's<00:02:56.400><c> demonstration</c><00:02:57.400><c> a</c><00:02:57.720><c> but</c><00:02:57.840><c> I</c><00:02:57.959><c> got</c><00:02:58.120><c> more</c>

00:02:58.350 --> 00:02:58.360 align:start position:0%
today's demonstration a but I got more
 

00:02:58.360 --> 00:03:00.309 align:start position:0%
today's demonstration a but I got more
features<00:02:58.800><c> to</c><00:02:58.959><c> show</c><00:02:59.440><c> come</c><00:02:59.519><c> on</c><00:02:59.840><c> big</c><00:02:59.959><c> fell</c><00:03:00.200><c> let's</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
features to show come on big fell let's
 

00:03:00.319 --> 00:03:01.230 align:start position:0%
features to show come on big fell let's
go<00:03:00.480><c> take</c><00:03:00.599><c> our</c>

00:03:01.230 --> 00:03:01.240 align:start position:0%
go take our
 

00:03:01.240 --> 00:03:07.110 align:start position:0%
go take our
[Music]

00:03:07.110 --> 00:03:07.120 align:start position:0%
 
 

00:03:07.120 --> 00:03:14.190 align:start position:0%
 
medication<00:03:08.120><c> aut</c><00:03:09.200><c> Auto</c><00:03:10.200><c> auto</c><00:03:10.760><c> auto</c><00:03:11.640><c> auto</c><00:03:12.760><c> auto</c>

00:03:14.190 --> 00:03:14.200 align:start position:0%
medication aut Auto auto auto auto auto
 

00:03:14.200 --> 00:03:18.200 align:start position:0%
medication aut Auto auto auto auto auto
auto<00:03:15.200><c> auto</c>

