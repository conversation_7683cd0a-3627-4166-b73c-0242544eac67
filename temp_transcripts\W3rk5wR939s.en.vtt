WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.550 align:start position:0%
 
hey<00:00:00.280><c> and</c><00:00:00.399><c> welcome</c><00:00:00.640><c> back</c><00:00:00.760><c> to</c><00:00:00.960><c> another</c><00:00:01.280><c> video</c>

00:00:01.550 --> 00:00:01.560 align:start position:0%
hey and welcome back to another video
 

00:00:01.560 --> 00:00:03.189 align:start position:0%
hey and welcome back to another video
and<00:00:01.719><c> today</c><00:00:02.000><c> we're</c><00:00:02.159><c> going</c><00:00:02.240><c> to</c><00:00:02.360><c> be</c><00:00:02.520><c> going</c><00:00:02.840><c> over</c>

00:00:03.189 --> 00:00:03.199 align:start position:0%
and today we're going to be going over
 

00:00:03.199 --> 00:00:05.670 align:start position:0%
and today we're going to be going over
another<00:00:03.560><c> AI</c><00:00:03.959><c> agency</c><00:00:04.520><c> but</c><00:00:04.839><c> this</c><00:00:05.080><c> time</c><00:00:05.520><c> we're</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
another AI agency but this time we're
 

00:00:05.680 --> 00:00:07.470 align:start position:0%
another AI agency but this time we're
going<00:00:05.799><c> to</c><00:00:05.920><c> have</c><00:00:06.040><c> a</c><00:00:06.160><c> front</c><00:00:06.440><c> end</c><00:00:06.839><c> as</c><00:00:06.960><c> you</c><00:00:07.040><c> can</c><00:00:07.160><c> see</c>

00:00:07.470 --> 00:00:07.480 align:start position:0%
going to have a front end as you can see
 

00:00:07.480 --> 00:00:09.589 align:start position:0%
going to have a front end as you can see
here<00:00:08.000><c> and</c><00:00:08.120><c> we're</c><00:00:08.320><c> be</c><00:00:08.440><c> connecting</c><00:00:08.800><c> to</c><00:00:09.000><c> an</c><00:00:09.240><c> open</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
here and we're be connecting to an open
 

00:00:09.599 --> 00:00:12.070 align:start position:0%
here and we're be connecting to an open
source<00:00:09.880><c> llm</c><00:00:10.400><c> using</c><00:00:10.639><c> LM</c><00:00:11.000><c> Studio</c><00:00:11.679><c> which</c><00:00:11.799><c> means</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
source llm using LM Studio which means
 

00:00:12.080 --> 00:00:13.390 align:start position:0%
source llm using LM Studio which means
this<00:00:12.200><c> is</c><00:00:12.280><c> going</c><00:00:12.400><c> to</c><00:00:12.480><c> be</c><00:00:12.599><c> completely</c><00:00:12.960><c> free</c><00:00:13.200><c> to</c>

00:00:13.390 --> 00:00:13.400 align:start position:0%
this is going to be completely free to
 

00:00:13.400 --> 00:00:15.430 align:start position:0%
this is going to be completely free to
run<00:00:13.639><c> locally</c><00:00:14.200><c> and</c><00:00:14.320><c> what</c><00:00:14.400><c> we'll</c><00:00:14.559><c> be</c><00:00:14.679><c> able</c><00:00:14.839><c> to</c><00:00:15.000><c> do</c>

00:00:15.430 --> 00:00:15.440 align:start position:0%
run locally and what we'll be able to do
 

00:00:15.440 --> 00:00:17.830 align:start position:0%
run locally and what we'll be able to do
is<00:00:15.599><c> choose</c><00:00:16.080><c> any</c><00:00:16.279><c> body</c><00:00:16.560><c> part</c><00:00:16.800><c> that</c><00:00:16.920><c> you</c><00:00:17.039><c> want</c>

00:00:17.830 --> 00:00:17.840 align:start position:0%
is choose any body part that you want
 

00:00:17.840 --> 00:00:19.510 align:start position:0%
is choose any body part that you want
how<00:00:17.960><c> many</c><00:00:18.199><c> days</c><00:00:18.400><c> you</c><00:00:18.480><c> want</c><00:00:18.640><c> the</c><00:00:18.760><c> workout</c><00:00:19.119><c> to</c><00:00:19.240><c> be</c>

00:00:19.510 --> 00:00:19.520 align:start position:0%
how many days you want the workout to be
 

00:00:19.520 --> 00:00:21.509 align:start position:0%
how many days you want the workout to be
and<00:00:19.640><c> then</c><00:00:19.760><c> choose</c><00:00:20.000><c> your</c><00:00:20.199><c> Fitness</c><00:00:20.680><c> level</c><00:00:21.400><c> and</c>

00:00:21.509 --> 00:00:21.519 align:start position:0%
and then choose your Fitness level and
 

00:00:21.519 --> 00:00:23.630 align:start position:0%
and then choose your Fitness level and
once<00:00:21.640><c> you</c><00:00:21.800><c> hit</c><00:00:22.000><c> submit</c><00:00:22.720><c> we're</c><00:00:22.920><c> going</c><00:00:23.000><c> to</c><00:00:23.359><c> talk</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
once you hit submit we're going to talk
 

00:00:23.640 --> 00:00:26.390 align:start position:0%
once you hit submit we're going to talk
to<00:00:24.000><c> the</c><00:00:24.119><c> llm</c><00:00:24.640><c> that</c><00:00:24.760><c> you</c><00:00:24.920><c> choose</c><00:00:25.279><c> in</c><00:00:25.439><c> LM</c><00:00:25.760><c> Studio</c>

00:00:26.390 --> 00:00:26.400 align:start position:0%
to the llm that you choose in LM Studio
 

00:00:26.400 --> 00:00:28.150 align:start position:0%
to the llm that you choose in LM Studio
it's<00:00:26.560><c> going</c><00:00:26.679><c> to</c><00:00:26.800><c> spit</c><00:00:27.039><c> us</c><00:00:27.199><c> back</c><00:00:27.359><c> a</c><00:00:27.599><c> response</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
it's going to spit us back a response
 

00:00:28.160 --> 00:00:29.390 align:start position:0%
it's going to spit us back a response
it's<00:00:28.279><c> going</c><00:00:28.400><c> to</c><00:00:28.480><c> show</c><00:00:28.640><c> up</c><00:00:28.760><c> on</c><00:00:28.880><c> the</c><00:00:29.000><c> right</c><00:00:29.199><c> hand</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
it's going to show up on the right hand
 

00:00:29.400 --> 00:00:31.269 align:start position:0%
it's going to show up on the right hand
side<00:00:29.800><c> and</c><00:00:30.000><c> and</c><00:00:30.119><c> then</c><00:00:30.279><c> we</c><00:00:30.359><c> can</c><00:00:30.640><c> download</c><00:00:31.000><c> it</c><00:00:31.199><c> as</c>

00:00:31.269 --> 00:00:31.279 align:start position:0%
side and and then we can download it as
 

00:00:31.279 --> 00:00:32.790 align:start position:0%
side and and then we can download it as
a<00:00:31.560><c> document</c><00:00:32.000><c> I</c><00:00:32.079><c> have</c><00:00:32.200><c> a</c><00:00:32.279><c> few</c><00:00:32.399><c> things</c><00:00:32.559><c> to</c><00:00:32.680><c> go</c>

00:00:32.790 --> 00:00:32.800 align:start position:0%
a document I have a few things to go
 

00:00:32.800 --> 00:00:34.830 align:start position:0%
a document I have a few things to go
over<00:00:33.079><c> so</c><00:00:33.440><c> let's</c><00:00:33.640><c> get</c><00:00:33.800><c> started</c><00:00:34.360><c> all</c><00:00:34.520><c> right</c><00:00:34.680><c> well</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
over so let's get started all right well
 

00:00:34.840 --> 00:00:35.709 align:start position:0%
over so let's get started all right well
the<00:00:34.920><c> first</c><00:00:35.079><c> thing</c><00:00:35.200><c> we</c><00:00:35.280><c> need</c><00:00:35.360><c> to</c><00:00:35.480><c> do</c><00:00:35.600><c> is</c>

00:00:35.709 --> 00:00:35.719 align:start position:0%
the first thing we need to do is
 

00:00:35.719 --> 00:00:37.630 align:start position:0%
the first thing we need to do is
download<00:00:36.120><c> install</c><00:00:36.399><c> LM</c><00:00:36.680><c> studio</c><00:00:37.280><c> and</c><00:00:37.399><c> I</c><00:00:37.480><c> will</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
download install LM studio and I will
 

00:00:37.640 --> 00:00:39.150 align:start position:0%
download install LM studio and I will
have<00:00:37.719><c> a</c><00:00:37.840><c> link</c><00:00:38.040><c> in</c><00:00:38.120><c> the</c><00:00:38.239><c> description</c><00:00:38.719><c> for</c><00:00:38.960><c> my</c>

00:00:39.150 --> 00:00:39.160 align:start position:0%
have a link in the description for my
 

00:00:39.160 --> 00:00:40.470 align:start position:0%
have a link in the description for my
last<00:00:39.360><c> video</c><00:00:39.640><c> where</c><00:00:39.800><c> I</c><00:00:39.920><c> kind</c><00:00:40.000><c> of</c><00:00:40.079><c> explained</c><00:00:40.399><c> how</c>

00:00:40.470 --> 00:00:40.480 align:start position:0%
last video where I kind of explained how
 

00:00:40.480 --> 00:00:42.229 align:start position:0%
last video where I kind of explained how
to<00:00:40.600><c> do</c><00:00:40.760><c> all</c><00:00:40.920><c> that</c><00:00:41.360><c> but</c><00:00:41.520><c> once</c><00:00:41.680><c> you</c><00:00:41.760><c> go</c><00:00:41.840><c> to</c><00:00:41.960><c> LM</c>

00:00:42.229 --> 00:00:42.239 align:start position:0%
to do all that but once you go to LM
 

00:00:42.239 --> 00:00:45.150 align:start position:0%
to do all that but once you go to LM
studio.<00:00:42.879><c> a</c><00:00:43.680><c> then</c><00:00:43.800><c> you</c><00:00:44.039><c> download</c><00:00:44.520><c> the</c><00:00:44.640><c> LM</c>

00:00:45.150 --> 00:00:45.160 align:start position:0%
studio. a then you download the LM
 

00:00:45.160 --> 00:00:48.350 align:start position:0%
studio. a then you download the LM
software<00:00:45.600><c> for</c><00:00:45.960><c> your</c><00:00:46.360><c> system</c><00:00:47.360><c> once</c><00:00:47.520><c> you</c><00:00:47.680><c> run</c><00:00:47.920><c> it</c>

00:00:48.350 --> 00:00:48.360 align:start position:0%
software for your system once you run it
 

00:00:48.360 --> 00:00:49.470 align:start position:0%
software for your system once you run it
this<00:00:48.480><c> will</c><00:00:48.600><c> be</c><00:00:48.760><c> the</c><00:00:48.960><c> page</c><00:00:49.160><c> that</c><00:00:49.239><c> you'll</c><00:00:49.399><c> be</c>

00:00:49.470 --> 00:00:49.480 align:start position:0%
this will be the page that you'll be
 

00:00:49.480 --> 00:00:50.869 align:start position:0%
this will be the page that you'll be
greeted<00:00:49.800><c> to</c><00:00:49.960><c> First</c><00:00:50.199><c> all</c><00:00:50.320><c> right</c><00:00:50.440><c> this</c><00:00:50.559><c> is</c><00:00:50.719><c> the</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
greeted to First all right this is the
 

00:00:50.879 --> 00:00:53.709 align:start position:0%
greeted to First all right this is the
homage<00:00:51.360><c> or</c><00:00:51.480><c> the</c><00:00:51.600><c> Home</c><00:00:51.840><c> tab</c><00:00:52.600><c> and</c><00:00:52.920><c> the</c><00:00:53.120><c> llm</c><00:00:53.600><c> that</c>

00:00:53.709 --> 00:00:53.719 align:start position:0%
homage or the Home tab and the llm that
 

00:00:53.719 --> 00:00:55.709 align:start position:0%
homage or the Home tab and the llm that
I<00:00:53.800><c> use</c><00:00:54.079><c> was</c><00:00:54.160><c> a</c><00:00:54.280><c> zephyr</c><00:00:54.640><c> model</c><00:00:55.199><c> and</c><00:00:55.359><c> to</c><00:00:55.520><c> get</c>

00:00:55.709 --> 00:00:55.719 align:start position:0%
I use was a zephyr model and to get
 

00:00:55.719 --> 00:00:57.470 align:start position:0%
I use was a zephyr model and to get
there<00:00:55.960><c> if</c><00:00:56.039><c> you</c><00:00:56.160><c> just</c><00:00:56.359><c> scroll</c><00:00:56.760><c> down</c><00:00:57.280><c> I</c><00:00:57.359><c> think</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
there if you just scroll down I think
 

00:00:57.480 --> 00:00:59.310 align:start position:0%
there if you just scroll down I think
it's<00:00:57.640><c> the</c><00:00:57.760><c> fourth</c><00:00:58.120><c> model</c><00:00:58.519><c> yeah</c><00:00:58.640><c> so</c><00:00:58.920><c> right</c><00:00:59.079><c> here</c>

00:00:59.310 --> 00:00:59.320 align:start position:0%
it's the fourth model yeah so right here
 

00:00:59.320 --> 00:01:00.430 align:start position:0%
it's the fourth model yeah so right here
this<00:00:59.440><c> is</c><00:00:59.559><c> the</c><00:00:59.680><c> Z</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
this is the Z
 

00:01:00.440 --> 00:01:03.029 align:start position:0%
this is the Z
3<00:01:00.760><c> billion</c><00:01:01.039><c> parameter</c><00:01:01.440><c> model</c><00:01:02.160><c> and</c><00:01:02.320><c> I</c><00:01:02.440><c> use</c><00:01:02.760><c> the</c>

00:01:03.029 --> 00:01:03.039 align:start position:0%
3 billion parameter model and I use the
 

00:01:03.039 --> 00:01:04.910 align:start position:0%
3 billion parameter model and I use the
quantized<00:01:03.640><c> 4</c><00:01:03.960><c> version</c><00:01:04.280><c> so</c><00:01:04.439><c> as</c><00:01:04.519><c> you</c><00:01:04.600><c> can</c><00:01:04.720><c> see</c>

00:01:04.910 --> 00:01:04.920 align:start position:0%
quantized 4 version so as you can see
 

00:01:04.920 --> 00:01:07.149 align:start position:0%
quantized 4 version so as you can see
here<00:01:05.159><c> I</c><00:01:05.320><c> downloaded</c><00:01:05.720><c> it</c><00:01:06.159><c> so</c><00:01:06.520><c> you</c><00:01:06.840><c> you'll</c><00:01:06.960><c> see</c><00:01:07.080><c> a</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
here I downloaded it so you you'll see a
 

00:01:07.159 --> 00:01:09.230 align:start position:0%
here I downloaded it so you you'll see a
button<00:01:07.439><c> here</c><00:01:07.560><c> you</c><00:01:07.680><c> just</c><00:01:07.840><c> click</c><00:01:08.040><c> the</c><00:01:08.240><c> download</c>

00:01:09.230 --> 00:01:09.240 align:start position:0%
button here you just click the download
 

00:01:09.240 --> 00:01:10.870 align:start position:0%
button here you just click the download
um<00:01:09.479><c> and</c><00:01:09.640><c> then</c><00:01:09.960><c> give</c><00:01:10.119><c> it</c><00:01:10.240><c> a</c><00:01:10.320><c> couple</c><00:01:10.520><c> minutes</c><00:01:10.759><c> for</c>

00:01:10.870 --> 00:01:10.880 align:start position:0%
um and then give it a couple minutes for
 

00:01:10.880 --> 00:01:12.429 align:start position:0%
um and then give it a couple minutes for
it<00:01:10.960><c> to</c><00:01:11.119><c> download</c><00:01:11.799><c> and</c><00:01:11.880><c> then</c><00:01:12.000><c> you'll</c><00:01:12.159><c> be</c><00:01:12.280><c> ready</c>

00:01:12.429 --> 00:01:12.439 align:start position:0%
it to download and then you'll be ready
 

00:01:12.439 --> 00:01:14.429 align:start position:0%
it to download and then you'll be ready
to<00:01:12.560><c> go</c><00:01:13.000><c> okay</c><00:01:13.159><c> but</c><00:01:13.320><c> after</c><00:01:13.520><c> that's</c><00:01:13.720><c> done</c><00:01:14.080><c> on</c><00:01:14.280><c> the</c>

00:01:14.429 --> 00:01:14.439 align:start position:0%
to go okay but after that's done on the
 

00:01:14.439 --> 00:01:15.950 align:start position:0%
to go okay but after that's done on the
left-<00:01:14.680><c> hand</c><00:01:14.880><c> side</c><00:01:15.119><c> here</c><00:01:15.360><c> you</c><00:01:15.439><c> will</c><00:01:15.600><c> see</c><00:01:15.880><c> kind</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
left- hand side here you will see kind
 

00:01:15.960 --> 00:01:17.910 align:start position:0%
left- hand side here you will see kind
of<00:01:16.080><c> the</c><00:01:16.200><c> double</c><00:01:16.479><c> arrow</c><00:01:16.799><c> for</c><00:01:17.080><c> local</c><00:01:17.320><c> server</c><00:01:17.759><c> you</c>

00:01:17.910 --> 00:01:17.920 align:start position:0%
of the double arrow for local server you
 

00:01:17.920 --> 00:01:20.830 align:start position:0%
of the double arrow for local server you
click<00:01:18.159><c> on</c><00:01:18.360><c> that</c><00:01:19.159><c> and</c><00:01:19.640><c> here</c><00:01:20.280><c> uh</c><00:01:20.400><c> all</c><00:01:20.520><c> we</c><00:01:20.640><c> have</c><00:01:20.720><c> to</c>

00:01:20.830 --> 00:01:20.840 align:start position:0%
click on that and here uh all we have to
 

00:01:20.840 --> 00:01:22.830 align:start position:0%
click on that and here uh all we have to
do<00:01:21.159><c> is</c><00:01:21.400><c> go</c><00:01:21.560><c> up</c><00:01:21.680><c> to</c><00:01:21.840><c> the</c><00:01:22.040><c> top</c><00:01:22.320><c> you</c><00:01:22.439><c> select</c><00:01:22.720><c> the</c>

00:01:22.830 --> 00:01:22.840 align:start position:0%
do is go up to the top you select the
 

00:01:22.840 --> 00:01:25.270 align:start position:0%
do is go up to the top you select the
model<00:01:23.079><c> to</c><00:01:23.200><c> load</c><00:01:23.880><c> so</c><00:01:24.079><c> you</c><00:01:24.240><c> click</c><00:01:24.560><c> this</c><00:01:25.079><c> as</c><00:01:25.159><c> you</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
model to load so you click this as you
 

00:01:25.280 --> 00:01:27.149 align:start position:0%
model to load so you click this as you
can<00:01:25.400><c> see</c><00:01:25.720><c> the</c><00:01:25.920><c> last</c><00:01:26.079><c> one</c><00:01:26.240><c> was</c><00:01:26.400><c> my</c><00:01:26.560><c> zeph</c><00:01:26.880><c> model</c>

00:01:27.149 --> 00:01:27.159 align:start position:0%
can see the last one was my zeph model
 

00:01:27.159 --> 00:01:28.910 align:start position:0%
can see the last one was my zeph model
so<00:01:27.400><c> I</c><00:01:27.640><c> click</c><00:01:27.840><c> on</c><00:01:28.079><c> that</c><00:01:28.560><c> and</c><00:01:28.680><c> what</c><00:01:28.759><c> it's</c><00:01:28.840><c> going</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
so I click on that and what it's going
 

00:01:28.920 --> 00:01:30.789 align:start position:0%
so I click on that and what it's going
to<00:01:29.000><c> do</c><00:01:29.119><c> is</c><00:01:29.280><c> now</c><00:01:29.360><c> it's</c><00:01:29.479><c> going</c><00:01:29.600><c> to</c><00:01:29.920><c> load</c><00:01:30.600><c> the</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
to do is now it's going to load the
 

00:01:30.799 --> 00:01:32.429 align:start position:0%
to do is now it's going to load the
zephra<00:01:31.159><c> model</c><00:01:31.439><c> that</c><00:01:31.560><c> we</c><00:01:31.759><c> downloaded</c><00:01:32.280><c> or</c>

00:01:32.429 --> 00:01:32.439 align:start position:0%
zephra model that we downloaded or
 

00:01:32.439 --> 00:01:34.030 align:start position:0%
zephra model that we downloaded or
whichever<00:01:32.720><c> model</c><00:01:33.000><c> you</c><00:01:33.159><c> used</c><00:01:33.680><c> it's</c><00:01:33.799><c> going</c><00:01:33.920><c> to</c>

00:01:34.030 --> 00:01:34.040 align:start position:0%
whichever model you used it's going to
 

00:01:34.040 --> 00:01:35.990 align:start position:0%
whichever model you used it's going to
load<00:01:34.280><c> that</c><00:01:34.360><c> into</c><00:01:34.520><c> Elm</c><00:01:34.799><c> Studio</c><00:01:35.119><c> for</c><00:01:35.240><c> us</c><00:01:35.399><c> to</c><00:01:35.520><c> use</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
load that into Elm Studio for us to use
 

00:01:36.000 --> 00:01:37.550 align:start position:0%
load that into Elm Studio for us to use
and<00:01:36.119><c> then</c><00:01:36.320><c> the</c><00:01:36.600><c> last</c><00:01:36.840><c> thing</c><00:01:37.040><c> is</c><00:01:37.280><c> you</c><00:01:37.439><c> just</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
and then the last thing is you just
 

00:01:37.560 --> 00:01:40.389 align:start position:0%
and then the last thing is you just
simply<00:01:38.079><c> press</c><00:01:38.320><c> this</c><00:01:38.520><c> button</c><00:01:39.200><c> start</c><00:01:39.600><c> server</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
simply press this button start server
 

00:01:40.399 --> 00:01:42.109 align:start position:0%
simply press this button start server
and<00:01:40.520><c> you're</c><00:01:40.680><c> ready</c><00:01:40.880><c> to</c><00:01:41.040><c> go</c><00:01:41.399><c> this</c><00:01:41.520><c> is</c><00:01:41.759><c> just</c><00:01:41.960><c> a</c>

00:01:42.109 --> 00:01:42.119 align:start position:0%
and you're ready to go this is just a
 

00:01:42.119 --> 00:01:43.830 align:start position:0%
and you're ready to go this is just a
flask<00:01:42.560><c> application</c><00:01:43.040><c> you</c><00:01:43.159><c> see</c><00:01:43.360><c> get</c><00:01:43.479><c> imported</c>

00:01:43.830 --> 00:01:43.840 align:start position:0%
flask application you see get imported
 

00:01:43.840 --> 00:01:45.709 align:start position:0%
flask application you see get imported
flask<00:01:44.280><c> you</c><00:01:44.360><c> need</c><00:01:44.479><c> a</c><00:01:44.600><c> render</c><00:01:44.920><c> template</c><00:01:45.439><c> so</c><00:01:45.600><c> you</c>

00:01:45.709 --> 00:01:45.719 align:start position:0%
flask you need a render template so you
 

00:01:45.719 --> 00:01:47.830 align:start position:0%
flask you need a render template so you
can<00:01:45.880><c> have</c><00:01:46.000><c> HTML</c><00:01:46.399><c> pages</c><00:01:47.240><c> and</c><00:01:47.399><c> I'm</c><00:01:47.520><c> not</c><00:01:47.640><c> going</c><00:01:47.719><c> to</c>

00:01:47.830 --> 00:01:47.840 align:start position:0%
can have HTML pages and I'm not going to
 

00:01:47.840 --> 00:01:50.870 align:start position:0%
can have HTML pages and I'm not going to
go<00:01:48.040><c> over</c><00:01:48.320><c> all</c><00:01:48.520><c> the</c><00:01:48.680><c> HTML</c><00:01:49.560><c> because</c><00:01:50.320><c> honestly</c><00:01:50.759><c> I</c>

00:01:50.870 --> 00:01:50.880 align:start position:0%
go over all the HTML because honestly I
 

00:01:50.880 --> 00:01:53.069 align:start position:0%
go over all the HTML because honestly I
just<00:01:51.040><c> found</c><00:01:51.399><c> an</c><00:01:51.560><c> HTML</c><00:01:52.040><c> template</c><00:01:52.360><c> online</c>

00:01:53.069 --> 00:01:53.079 align:start position:0%
just found an HTML template online
 

00:01:53.079 --> 00:01:54.590 align:start position:0%
just found an HTML template online
modified<00:01:53.520><c> it</c><00:01:53.640><c> slightly</c><00:01:53.960><c> for</c><00:01:54.119><c> what</c><00:01:54.240><c> I</c><00:01:54.320><c> needed</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
modified it slightly for what I needed
 

00:01:54.600 --> 00:01:56.510 align:start position:0%
modified it slightly for what I needed
for<00:01:54.799><c> like</c><00:01:54.960><c> the</c><00:01:55.119><c> request</c><00:01:55.479><c> and</c><00:01:55.600><c> the</c><00:01:55.680><c> forms</c><00:01:56.439><c> and</c>

00:01:56.510 --> 00:01:56.520 align:start position:0%
for like the request and the forms and
 

00:01:56.520 --> 00:01:58.069 align:start position:0%
for like the request and the forms and
then<00:01:56.680><c> I</c><00:01:56.759><c> just</c><00:01:56.880><c> used</c><00:01:57.159><c> bootstrap</c><00:01:57.759><c> and</c><00:01:57.840><c> there's</c>

00:01:58.069 --> 00:01:58.079 align:start position:0%
then I just used bootstrap and there's
 

00:01:58.079 --> 00:02:00.190 align:start position:0%
then I just used bootstrap and there's
not<00:01:58.200><c> a</c><00:01:58.399><c> lot</c><00:01:58.520><c> of</c><00:01:58.719><c> CSS</c><00:01:59.320><c> but</c><00:01:59.600><c> when</c><00:01:59.840><c> whenever</c><00:02:00.079><c> you</c>

00:02:00.190 --> 00:02:00.200 align:start position:0%
not a lot of CSS but when whenever you
 

00:02:00.200 --> 00:02:01.789 align:start position:0%
not a lot of CSS but when whenever you
go<00:02:00.320><c> to</c><00:02:00.439><c> my</c><00:02:00.600><c> GitHub</c><00:02:00.960><c> and</c><00:02:01.039><c> you</c><00:02:01.119><c> want</c><00:02:01.240><c> to</c><00:02:01.399><c> download</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
go to my GitHub and you want to download
 

00:02:01.799 --> 00:02:04.069 align:start position:0%
go to my GitHub and you want to download
this<00:02:02.360><c> you'll</c><00:02:02.560><c> see</c><00:02:02.759><c> it</c><00:02:02.920><c> here</c><00:02:03.439><c> uh</c><00:02:03.640><c> you</c><00:02:03.719><c> can</c><00:02:03.840><c> see</c>

00:02:04.069 --> 00:02:04.079 align:start position:0%
this you'll see it here uh you can see
 

00:02:04.079 --> 00:02:06.310 align:start position:0%
this you'll see it here uh you can see
in<00:02:04.200><c> the</c><00:02:04.360><c> static</c><00:02:04.680><c> I</c><00:02:04.759><c> have</c><00:02:04.880><c> a</c><00:02:04.960><c> main.css</c><00:02:05.759><c> and</c><00:02:06.159><c> the</c>

00:02:06.310 --> 00:02:06.320 align:start position:0%
in the static I have a main.css and the
 

00:02:06.320 --> 00:02:08.749 align:start position:0%
in the static I have a main.css and the
templates<00:02:06.680><c> there's</c><00:02:06.840><c> an</c><00:02:07.399><c> index.html</c><00:02:08.399><c> you</c><00:02:08.520><c> can</c>

00:02:08.749 --> 00:02:08.759 align:start position:0%
templates there's an index.html you can
 

00:02:08.759 --> 00:02:09.790 align:start position:0%
templates there's an index.html you can
do<00:02:08.959><c> whatever</c><00:02:09.200><c> you</c><00:02:09.280><c> want</c><00:02:09.440><c> with</c><00:02:09.599><c> that</c><00:02:09.720><c> and</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
do whatever you want with that and
 

00:02:09.800 --> 00:02:11.630 align:start position:0%
do whatever you want with that and
modify<00:02:10.160><c> it</c><00:02:10.360><c> however</c><00:02:10.479><c> you</c><00:02:10.599><c> want</c><00:02:11.280><c> with</c><00:02:11.440><c> that</c>

00:02:11.630 --> 00:02:11.640 align:start position:0%
modify it however you want with that
 

00:02:11.640 --> 00:02:13.750 align:start position:0%
modify it however you want with that
said<00:02:12.239><c> what</c><00:02:12.360><c> I</c><00:02:12.480><c> did</c><00:02:12.680><c> this</c><00:02:12.840><c> time</c><00:02:13.040><c> around</c><00:02:13.480><c> was</c><00:02:13.640><c> I</c>

00:02:13.750 --> 00:02:13.760 align:start position:0%
said what I did this time around was I
 

00:02:13.760 --> 00:02:16.110 align:start position:0%
said what I did this time around was I
kind<00:02:13.879><c> of</c><00:02:14.080><c> separated</c><00:02:14.599><c> things</c><00:02:14.840><c> out</c><00:02:15.519><c> so</c><00:02:15.959><c> for</c>

00:02:16.110 --> 00:02:16.120 align:start position:0%
kind of separated things out so for
 

00:02:16.120 --> 00:02:18.670 align:start position:0%
kind of separated things out so for
instance<00:02:16.480><c> in</c><00:02:16.640><c> this</c><00:02:16.840><c> agent</c><00:02:17.400><c> python</c><00:02:17.800><c> file</c><00:02:18.599><c> this</c>

00:02:18.670 --> 00:02:18.680 align:start position:0%
instance in this agent python file this
 

00:02:18.680 --> 00:02:20.550 align:start position:0%
instance in this agent python file this
is<00:02:18.760><c> where</c><00:02:18.879><c> I</c><00:02:19.000><c> actually</c><00:02:19.200><c> import</c><00:02:19.480><c> autogen</c><00:02:20.280><c> so</c><00:02:20.480><c> I</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
is where I actually import autogen so I
 

00:02:20.560 --> 00:02:22.229 align:start position:0%
is where I actually import autogen so I
have<00:02:20.640><c> a</c><00:02:20.760><c> user</c><00:02:21.040><c> proxy</c><00:02:21.360><c> and</c><00:02:21.480><c> a</c><00:02:21.599><c> fitness</c><00:02:21.920><c> expert</c>

00:02:22.229 --> 00:02:22.239 align:start position:0%
have a user proxy and a fitness expert
 

00:02:22.239 --> 00:02:24.150 align:start position:0%
have a user proxy and a fitness expert
and<00:02:22.360><c> this</c><00:02:22.440><c> is</c><00:02:22.599><c> where</c><00:02:22.879><c> I</c><00:02:23.160><c> create</c><00:02:23.560><c> the</c><00:02:23.800><c> agents</c>

00:02:24.150 --> 00:02:24.160 align:start position:0%
and this is where I create the agents
 

00:02:24.160 --> 00:02:25.390 align:start position:0%
and this is where I create the agents
that<00:02:24.280><c> I'm</c><00:02:24.400><c> going</c><00:02:24.560><c> to</c><00:02:24.640><c> use</c><00:02:24.879><c> so</c><00:02:25.000><c> I</c><00:02:25.080><c> have</c><00:02:25.239><c> two</c>

00:02:25.390 --> 00:02:25.400 align:start position:0%
that I'm going to use so I have two
 

00:02:25.400 --> 00:02:27.830 align:start position:0%
that I'm going to use so I have two
agents<00:02:25.720><c> total</c><00:02:26.200><c> I</c><00:02:26.280><c> have</c><00:02:26.400><c> the</c><00:02:26.519><c> user</c><00:02:26.840><c> proxy</c><00:02:27.400><c> and</c><00:02:27.680><c> I</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
agents total I have the user proxy and I
 

00:02:27.840 --> 00:02:30.390 align:start position:0%
agents total I have the user proxy and I
have<00:02:28.080><c> the</c><00:02:28.319><c> fitness</c><00:02:29.080><c> assistant</c><00:02:29.440><c> agent</c><00:02:30.200><c> and</c>

00:02:30.390 --> 00:02:30.400 align:start position:0%
have the fitness assistant agent and
 

00:02:30.400 --> 00:02:33.470 align:start position:0%
have the fitness assistant agent and
then<00:02:30.640><c> over</c><00:02:30.959><c> in</c><00:02:31.120><c> the</c><00:02:31.400><c> config</c><00:02:32.000><c> python</c><00:02:32.440><c> file</c><00:02:33.360><c> this</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
then over in the config python file this
 

00:02:33.480 --> 00:02:35.350 align:start position:0%
then over in the config python file this
is<00:02:33.680><c> where</c><00:02:34.000><c> you</c><00:02:34.120><c> can</c><00:02:34.280><c> see</c><00:02:34.560><c> that</c><00:02:34.680><c> I</c><00:02:34.800><c> used</c><00:02:35.040><c> LM</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
is where you can see that I used LM
 

00:02:35.360 --> 00:02:37.470 align:start position:0%
is where you can see that I used LM
studio<00:02:35.720><c> so</c><00:02:35.920><c> this</c><00:02:36.200><c> this</c><00:02:36.360><c> config</c><00:02:36.720><c> list</c><00:02:37.040><c> here</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
studio so this this config list here
 

00:02:37.480 --> 00:02:40.949 align:start position:0%
studio so this this config list here
just<00:02:37.640><c> has</c><00:02:37.879><c> the</c><00:02:38.080><c> base</c><00:02:38.800><c> URL</c><00:02:39.680><c> of</c><00:02:40.000><c> the</c><00:02:40.200><c> Local</c><00:02:40.599><c> Host</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
just has the base URL of the Local Host
 

00:02:40.959 --> 00:02:43.949 align:start position:0%
just has the base URL of the Local Host
that<00:02:41.120><c> is</c><00:02:41.319><c> in</c><00:02:41.560><c> LM</c><00:02:41.920><c> studio</c><00:02:42.560><c> so</c><00:02:42.720><c> if</c><00:02:42.840><c> I</c><00:02:42.959><c> go</c><00:02:43.159><c> over</c><00:02:43.480><c> to</c>

00:02:43.949 --> 00:02:43.959 align:start position:0%
that is in LM studio so if I go over to
 

00:02:43.959 --> 00:02:46.430 align:start position:0%
that is in LM studio so if I go over to
the<00:02:44.159><c> environment</c><00:02:44.800><c> file</c><00:02:45.560><c> right</c><00:02:45.760><c> here</c><00:02:46.040><c> is</c><00:02:46.200><c> the</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
the environment file right here is the
 

00:02:46.440 --> 00:02:49.030 align:start position:0%
the environment file right here is the
base<00:02:46.879><c> URL</c><00:02:47.680><c> and</c><00:02:47.840><c> lastly</c><00:02:48.239><c> I</c><00:02:48.360><c> have</c><00:02:48.480><c> a</c><00:02:48.680><c> system</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
base URL and lastly I have a system
 

00:02:49.040 --> 00:02:51.270 align:start position:0%
base URL and lastly I have a system
messages<00:02:49.599><c> python</c><00:02:49.959><c> file</c><00:02:50.280><c> and</c><00:02:50.440><c> this</c><00:02:50.640><c> really</c><00:02:51.200><c> you</c>

00:02:51.270 --> 00:02:51.280 align:start position:0%
messages python file and this really you
 

00:02:51.280 --> 00:02:53.390 align:start position:0%
messages python file and this really you
know<00:02:51.400><c> is</c><00:02:51.519><c> just</c><00:02:51.680><c> the</c><00:02:52.159><c> the</c><00:02:52.319><c> fitness</c><00:02:52.599><c> expert</c><00:02:52.959><c> name</c>

00:02:53.390 --> 00:02:53.400 align:start position:0%
know is just the the fitness expert name
 

00:02:53.400 --> 00:02:56.030 align:start position:0%
know is just the the fitness expert name
the<00:02:53.519><c> message</c><00:02:54.239><c> and</c><00:02:54.400><c> the</c><00:02:54.519><c> user</c><00:02:54.879><c> proxy</c><00:02:55.519><c> message</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
the message and the user proxy message
 

00:02:56.040 --> 00:02:57.350 align:start position:0%
the message and the user proxy message
and<00:02:56.239><c> then</c><00:02:56.720><c> and</c><00:02:56.800><c> then</c><00:02:56.920><c> we</c><00:02:57.000><c> have</c><00:02:57.120><c> the</c><00:02:57.200><c> get</c>

00:02:57.350 --> 00:02:57.360 align:start position:0%
and then and then we have the get
 

00:02:57.360 --> 00:02:59.750 align:start position:0%
and then and then we have the get
initiate<00:02:57.840><c> message</c><00:02:58.239><c> where</c><00:02:58.760><c> I</c><00:02:58.920><c> have</c><00:02:59.280><c> the</c>

00:02:59.750 --> 00:02:59.760 align:start position:0%
initiate message where I have the
 

00:02:59.760 --> 00:03:01.830 align:start position:0%
initiate message where I have the
options<00:03:00.000><c> from</c><00:03:00.159><c> the</c><00:03:00.239><c> form</c><00:03:00.640><c> so</c><00:03:01.319><c> what</c><00:03:01.519><c> how</c><00:03:01.640><c> many</c>

00:03:01.830 --> 00:03:01.840 align:start position:0%
options from the form so what how many
 

00:03:01.840 --> 00:03:03.910 align:start position:0%
options from the form so what how many
days<00:03:02.080><c> do</c><00:03:02.159><c> you</c><00:03:02.280><c> want</c><00:03:02.519><c> the</c><00:03:02.640><c> workout</c><00:03:02.959><c> to</c><00:03:03.080><c> be</c><00:03:03.239><c> for</c>

00:03:03.910 --> 00:03:03.920 align:start position:0%
days do you want the workout to be for
 

00:03:03.920 --> 00:03:05.550 align:start position:0%
days do you want the workout to be for
what<00:03:04.120><c> body</c><00:03:04.440><c> parts</c><00:03:04.760><c> and</c><00:03:04.879><c> then</c><00:03:05.080><c> what</c><00:03:05.200><c> is</c><00:03:05.319><c> your</c>

00:03:05.550 --> 00:03:05.560 align:start position:0%
what body parts and then what is your
 

00:03:05.560 --> 00:03:08.070 align:start position:0%
what body parts and then what is your
level<00:03:05.920><c> of</c><00:03:06.080><c> expertise</c><00:03:06.760><c> and</c><00:03:06.920><c> then</c><00:03:07.159><c> this</c><00:03:07.440><c> is</c><00:03:07.760><c> the</c>

00:03:08.070 --> 00:03:08.080 align:start position:0%
level of expertise and then this is the
 

00:03:08.080 --> 00:03:10.949 align:start position:0%
level of expertise and then this is the
custom<00:03:08.959><c> message</c><00:03:09.640><c> that</c><00:03:09.760><c> will</c><00:03:09.879><c> be</c><00:03:10.040><c> sent</c><00:03:10.319><c> to</c><00:03:10.599><c> LM</c>

00:03:10.949 --> 00:03:10.959 align:start position:0%
custom message that will be sent to LM
 

00:03:10.959 --> 00:03:13.550 align:start position:0%
custom message that will be sent to LM
studio<00:03:11.440><c> so</c><00:03:11.640><c> now</c><00:03:11.959><c> back</c><00:03:12.159><c> to</c><00:03:12.440><c> the</c><00:03:12.680><c> main</c><00:03:13.200><c> file</c>

00:03:13.550 --> 00:03:13.560 align:start position:0%
studio so now back to the main file
 

00:03:13.560 --> 00:03:16.070 align:start position:0%
studio so now back to the main file
where<00:03:13.720><c> we'll</c><00:03:13.920><c> be</c><00:03:14.200><c> executing</c><00:03:14.720><c> everything</c><00:03:15.560><c> is</c><00:03:15.920><c> I</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
where we'll be executing everything is I
 

00:03:16.080 --> 00:03:18.430 align:start position:0%
where we'll be executing everything is I
have<00:03:16.400><c> a</c><00:03:16.560><c> route</c><00:03:16.920><c> here</c><00:03:17.319><c> which</c><00:03:17.440><c> is</c><00:03:17.680><c> just</c><00:03:18.120><c> you</c><00:03:18.200><c> know</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
have a route here which is just you know
 

00:03:18.440 --> 00:03:19.750 align:start position:0%
have a route here which is just you know
this<00:03:18.519><c> is</c><00:03:18.680><c> just</c><00:03:18.799><c> a</c><00:03:18.959><c> standard</c><00:03:19.360><c> route</c><00:03:19.599><c> for</c>

00:03:19.750 --> 00:03:19.760 align:start position:0%
this is just a standard route for
 

00:03:19.760 --> 00:03:21.949 align:start position:0%
this is just a standard route for
whenever<00:03:20.000><c> you</c><00:03:20.120><c> just</c><00:03:20.319><c> go</c><00:03:20.720><c> to</c><00:03:20.879><c> your</c><00:03:21.080><c> local</c><00:03:21.360><c> host</c>

00:03:21.949 --> 00:03:21.959 align:start position:0%
whenever you just go to your local host
 

00:03:21.959 --> 00:03:23.949 align:start position:0%
whenever you just go to your local host
I<00:03:22.080><c> have</c><00:03:22.239><c> a</c><00:03:22.519><c> get</c><00:03:22.720><c> and</c><00:03:22.879><c> a</c><00:03:23.000><c> post</c><00:03:23.280><c> request</c><00:03:23.720><c> right</c><00:03:23.840><c> so</c>

00:03:23.949 --> 00:03:23.959 align:start position:0%
I have a get and a post request right so
 

00:03:23.959 --> 00:03:26.430 align:start position:0%
I have a get and a post request right so
for<00:03:24.120><c> the</c><00:03:24.280><c> standard</c><00:03:25.239><c> uh</c><00:03:25.519><c> get</c><00:03:25.840><c> request</c><00:03:26.319><c> it's</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
for the standard uh get request it's
 

00:03:26.440 --> 00:03:28.789 align:start position:0%
for the standard uh get request it's
just<00:03:26.560><c> going</c><00:03:26.680><c> to</c><00:03:26.879><c> return</c><00:03:27.080><c> the</c><00:03:27.200><c> index</c><00:03:27.599><c> HTML</c><00:03:28.120><c> page</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
just going to return the index HTML page
 

00:03:28.799 --> 00:03:32.070 align:start position:0%
just going to return the index HTML page
however<00:03:29.400><c> if</c><00:03:29.760><c> it's</c><00:03:29.879><c> a</c><00:03:30.159><c> post</c><00:03:31.159><c> there's</c><00:03:31.599><c> the</c><00:03:31.720><c> form</c>

00:03:32.070 --> 00:03:32.080 align:start position:0%
however if it's a post there's the form
 

00:03:32.080 --> 00:03:33.190 align:start position:0%
however if it's a post there's the form
section<00:03:32.319><c> that</c><00:03:32.439><c> you</c><00:03:32.519><c> see</c><00:03:32.640><c> on</c><00:03:32.760><c> the</c><00:03:32.840><c> left</c><00:03:33.080><c> hand</c>

00:03:33.190 --> 00:03:33.200 align:start position:0%
section that you see on the left hand
 

00:03:33.200 --> 00:03:34.830 align:start position:0%
section that you see on the left hand
side<00:03:33.360><c> of</c><00:03:33.439><c> the</c><00:03:33.560><c> web</c><00:03:33.760><c> page</c><00:03:34.439><c> uh</c><00:03:34.519><c> it's</c><00:03:34.640><c> going</c><00:03:34.760><c> to</c>

00:03:34.830 --> 00:03:34.840 align:start position:0%
side of the web page uh it's going to
 

00:03:34.840 --> 00:03:36.830 align:start position:0%
side of the web page uh it's going to
get<00:03:34.959><c> the</c><00:03:35.120><c> days</c><00:03:35.480><c> option</c><00:03:35.959><c> levels</c><00:03:36.519><c> and</c><00:03:36.640><c> this</c><00:03:36.720><c> is</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
get the days option levels and this is
 

00:03:36.840 --> 00:03:38.990 align:start position:0%
get the days option levels and this is
something<00:03:37.000><c> you've</c><00:03:37.159><c> seen</c><00:03:37.360><c> before</c><00:03:38.080><c> user</c><00:03:38.480><c> proxy</c>

00:03:38.990 --> 00:03:39.000 align:start position:0%
something you've seen before user proxy
 

00:03:39.000 --> 00:03:40.630 align:start position:0%
something you've seen before user proxy
do<00:03:39.200><c> initiate</c><00:03:39.680><c> chat</c><00:03:39.920><c> we've</c><00:03:40.120><c> always</c><00:03:40.280><c> seen</c><00:03:40.560><c> the</c>

00:03:40.630 --> 00:03:40.640 align:start position:0%
do initiate chat we've always seen the
 

00:03:40.640 --> 00:03:42.350 align:start position:0%
do initiate chat we've always seen the
user<00:03:40.920><c> agent</c><00:03:41.239><c> initiate</c><00:03:41.680><c> the</c><00:03:41.760><c> chat</c><00:03:42.000><c> so</c><00:03:42.120><c> this</c><00:03:42.200><c> is</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
user agent initiate the chat so this is
 

00:03:42.360 --> 00:03:44.270 align:start position:0%
user agent initiate the chat so this is
really<00:03:42.519><c> no</c><00:03:42.720><c> different</c><00:03:43.280><c> this</c><00:03:43.360><c> is</c><00:03:43.560><c> just</c><00:03:43.840><c> inside</c>

00:03:44.270 --> 00:03:44.280 align:start position:0%
really no different this is just inside
 

00:03:44.280 --> 00:03:46.789 align:start position:0%
really no different this is just inside
of<00:03:44.560><c> a</c><00:03:44.959><c> post</c><00:03:45.360><c> request</c><00:03:46.159><c> that's</c><00:03:46.319><c> it</c><00:03:46.519><c> it</c><00:03:46.599><c> doesn't</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
of a post request that's it it doesn't
 

00:03:46.799 --> 00:03:48.190 align:start position:0%
of a post request that's it it doesn't
matter<00:03:47.000><c> where</c><00:03:47.120><c> you</c><00:03:47.280><c> put</c><00:03:47.400><c> it</c><00:03:47.840><c> because</c><00:03:48.080><c> the</c>

00:03:48.190 --> 00:03:48.200 align:start position:0%
matter where you put it because the
 

00:03:48.200 --> 00:03:50.270 align:start position:0%
matter where you put it because the
thing<00:03:48.360><c> is</c><00:03:48.560><c> the</c><00:03:48.720><c> response</c><00:03:49.120><c> that</c><00:03:49.239><c> we</c><00:03:49.439><c> get</c><00:03:49.760><c> here</c>

00:03:50.270 --> 00:03:50.280 align:start position:0%
thing is the response that we get here
 

00:03:50.280 --> 00:03:51.429 align:start position:0%
thing is the response that we get here
is<00:03:50.439><c> what</c><00:03:50.560><c> we're</c><00:03:50.640><c> going</c><00:03:50.720><c> to</c><00:03:50.760><c> be</c><00:03:50.879><c> putting</c><00:03:51.120><c> into</c>

00:03:51.429 --> 00:03:51.439 align:start position:0%
is what we're going to be putting into
 

00:03:51.439 --> 00:03:53.149 align:start position:0%
is what we're going to be putting into
the<00:03:51.680><c> right</c><00:03:51.920><c> side</c><00:03:52.079><c> of</c><00:03:52.239><c> the</c><00:03:52.360><c> front</c><00:03:52.599><c> end</c><00:03:53.079><c> all</c>

00:03:53.149 --> 00:03:53.159 align:start position:0%
the right side of the front end all
 

00:03:53.159 --> 00:03:54.589 align:start position:0%
the right side of the front end all
right<00:03:53.360><c> now</c><00:03:53.640><c> when</c><00:03:53.760><c> I</c><00:03:53.840><c> go</c><00:03:53.959><c> through</c><00:03:54.079><c> the</c><00:03:54.200><c> example</c>

00:03:54.589 --> 00:03:54.599 align:start position:0%
right now when I go through the example
 

00:03:54.599 --> 00:03:56.149 align:start position:0%
right now when I go through the example
you'll<00:03:54.760><c> see</c><00:03:55.000><c> this</c><00:03:55.439><c> and</c><00:03:55.560><c> then</c><00:03:55.680><c> the</c><00:03:55.840><c> message</c>

00:03:56.149 --> 00:03:56.159 align:start position:0%
you'll see this and then the message
 

00:03:56.159 --> 00:03:58.589 align:start position:0%
you'll see this and then the message
here<00:03:56.840><c> the</c><00:03:56.959><c> system</c><00:03:57.239><c> message</c><00:03:57.599><c> python</c><00:03:58.040><c> file</c><00:03:58.439><c> we</c>

00:03:58.589 --> 00:03:58.599 align:start position:0%
here the system message python file we
 

00:03:58.599 --> 00:04:01.069 align:start position:0%
here the system message python file we
call<00:03:58.879><c> the</c><00:03:59.120><c> get</c><00:03:59.720><c> initiate</c><00:04:00.280><c> message</c><00:04:00.720><c> where</c><00:04:00.879><c> we</c>

00:04:01.069 --> 00:04:01.079 align:start position:0%
call the get initiate message where we
 

00:04:01.079 --> 00:04:03.910 align:start position:0%
call the get initiate message where we
pass<00:04:01.360><c> in</c><00:04:02.079><c> the</c><00:04:02.360><c> form</c><00:04:02.799><c> options</c><00:04:03.200><c> so</c><00:04:03.439><c> if</c><00:04:03.519><c> we</c><00:04:03.680><c> go</c>

00:04:03.910 --> 00:04:03.920 align:start position:0%
pass in the form options so if we go
 

00:04:03.920 --> 00:04:06.309 align:start position:0%
pass in the form options so if we go
back<00:04:04.079><c> to</c><00:04:04.239><c> the</c><00:04:04.400><c> system</c><00:04:04.680><c> messages</c><00:04:05.120><c> python</c><00:04:05.480><c> file</c>

00:04:06.309 --> 00:04:06.319 align:start position:0%
back to the system messages python file
 

00:04:06.319 --> 00:04:08.830 align:start position:0%
back to the system messages python file
we<00:04:06.560><c> pass</c><00:04:07.159><c> the</c><00:04:07.280><c> form</c><00:04:07.680><c> options</c><00:04:08.040><c> that</c><00:04:08.159><c> you</c><00:04:08.360><c> chose</c>

00:04:08.830 --> 00:04:08.840 align:start position:0%
we pass the form options that you chose
 

00:04:08.840 --> 00:04:10.630 align:start position:0%
we pass the form options that you chose
on<00:04:08.959><c> the</c><00:04:09.040><c> front</c><00:04:09.280><c> end</c><00:04:09.439><c> you</c><00:04:09.599><c> pass</c><00:04:09.799><c> them</c><00:04:09.920><c> in</c><00:04:10.159><c> here</c>

00:04:10.630 --> 00:04:10.640 align:start position:0%
on the front end you pass them in here
 

00:04:10.640 --> 00:04:11.789 align:start position:0%
on the front end you pass them in here
and<00:04:10.720><c> then</c><00:04:10.840><c> it's</c><00:04:10.959><c> going</c><00:04:11.040><c> to</c><00:04:11.239><c> create</c><00:04:11.640><c> the</c>

00:04:11.789 --> 00:04:11.799 align:start position:0%
and then it's going to create the
 

00:04:11.799 --> 00:04:13.869 align:start position:0%
and then it's going to create the
message<00:04:12.200><c> based</c><00:04:12.480><c> on</c><00:04:12.720><c> that</c><00:04:13.319><c> and</c><00:04:13.439><c> so</c><00:04:13.640><c> then</c><00:04:13.760><c> when</c>

00:04:13.869 --> 00:04:13.879 align:start position:0%
message based on that and so then when
 

00:04:13.879 --> 00:04:16.749 align:start position:0%
message based on that and so then when
we<00:04:14.040><c> come</c><00:04:14.360><c> back</c><00:04:14.680><c> here</c><00:04:15.439><c> the</c><00:04:15.840><c> last</c><00:04:16.079><c> message</c><00:04:16.479><c> is</c>

00:04:16.749 --> 00:04:16.759 align:start position:0%
we come back here the last message is
 

00:04:16.759 --> 00:04:19.430 align:start position:0%
we come back here the last message is
this<00:04:16.880><c> is</c><00:04:17.120><c> the</c><00:04:17.759><c> last</c><00:04:18.000><c> message</c><00:04:18.479><c> that</c><00:04:18.720><c> we</c>

00:04:19.430 --> 00:04:19.440 align:start position:0%
this is the last message that we
 

00:04:19.440 --> 00:04:21.150 align:start position:0%
this is the last message that we
received<00:04:19.959><c> uh</c><00:04:20.079><c> back</c><00:04:20.239><c> from</c><00:04:20.400><c> LM</c><00:04:20.680><c> Studio</c><00:04:20.959><c> or</c><00:04:21.079><c> the</c>

00:04:21.150 --> 00:04:21.160 align:start position:0%
received uh back from LM Studio or the
 

00:04:21.160 --> 00:04:23.790 align:start position:0%
received uh back from LM Studio or the
llm<00:04:21.600><c> that</c><00:04:21.720><c> you</c><00:04:21.840><c> used</c><00:04:22.199><c> inside</c><00:04:22.560><c> LM</c><00:04:22.840><c> Studio</c><00:04:23.639><c> we</c>

00:04:23.790 --> 00:04:23.800 align:start position:0%
llm that you used inside LM Studio we
 

00:04:23.800 --> 00:04:25.909 align:start position:0%
llm that you used inside LM Studio we
send<00:04:24.000><c> it</c><00:04:24.240><c> back</c><00:04:24.520><c> here</c><00:04:25.120><c> and</c><00:04:25.240><c> then</c><00:04:25.440><c> I</c><00:04:25.560><c> have</c><00:04:25.720><c> a</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
send it back here and then I have a
 

00:04:25.919 --> 00:04:28.030 align:start position:0%
send it back here and then I have a
global<00:04:26.440><c> variable</c><00:04:27.080><c> that</c><00:04:27.199><c> is</c><00:04:27.400><c> that</c><00:04:27.639><c> response</c>

00:04:28.030 --> 00:04:28.040 align:start position:0%
global variable that is that response
 

00:04:28.040 --> 00:04:29.350 align:start position:0%
global variable that is that response
text<00:04:28.320><c> whenever</c><00:04:28.560><c> you</c><00:04:28.680><c> click</c><00:04:28.880><c> the</c><00:04:29.039><c> submit</c>

00:04:29.350 --> 00:04:29.360 align:start position:0%
text whenever you click the submit
 

00:04:29.360 --> 00:04:31.830 align:start position:0%
text whenever you click the submit
button<00:04:29.840><c> it's</c><00:04:30.000><c> going</c><00:04:30.080><c> to</c><00:04:30.560><c> call</c><00:04:30.800><c> this</c><00:04:31.000><c> function</c>

00:04:31.830 --> 00:04:31.840 align:start position:0%
button it's going to call this function
 

00:04:31.840 --> 00:04:34.629 align:start position:0%
button it's going to call this function
and<00:04:32.039><c> it's</c><00:04:32.199><c> going</c><00:04:32.280><c> to</c><00:04:32.520><c> use</c><00:04:33.280><c> that</c><00:04:33.880><c> uh</c><00:04:34.240><c> document</c>

00:04:34.629 --> 00:04:34.639 align:start position:0%
and it's going to use that uh document
 

00:04:34.639 --> 00:04:36.909 align:start position:0%
and it's going to use that uh document
or<00:04:34.800><c> the</c><00:04:34.960><c> summarized</c><00:04:35.600><c> document</c><00:04:36.039><c> that</c><00:04:36.160><c> the</c><00:04:36.360><c> AI</c>

00:04:36.909 --> 00:04:36.919 align:start position:0%
or the summarized document that the AI
 

00:04:36.919 --> 00:04:38.469 align:start position:0%
or the summarized document that the AI
gave<00:04:37.120><c> us</c><00:04:37.639><c> and</c><00:04:37.720><c> then</c><00:04:37.840><c> it's</c><00:04:37.919><c> going</c><00:04:38.039><c> to</c><00:04:38.160><c> download</c>

00:04:38.469 --> 00:04:38.479 align:start position:0%
gave us and then it's going to download
 

00:04:38.479 --> 00:04:41.430 align:start position:0%
gave us and then it's going to download
it<00:04:38.639><c> into</c><00:04:39.560><c> workout.</c><00:04:40.560><c> dooc</c><00:04:41.000><c> for</c><00:04:41.160><c> whatever</c>

00:04:41.430 --> 00:04:41.440 align:start position:0%
it into workout. dooc for whatever
 

00:04:41.440 --> 00:04:44.110 align:start position:0%
it into workout. dooc for whatever
reason<00:04:41.919><c> I</c><00:04:42.039><c> couldn't</c><00:04:42.280><c> get</c><00:04:42.840><c> do</c><00:04:43.080><c> X</c><00:04:43.320><c> to</c><00:04:43.520><c> work</c><00:04:43.960><c> so</c>

00:04:44.110 --> 00:04:44.120 align:start position:0%
reason I couldn't get do X to work so
 

00:04:44.120 --> 00:04:45.469 align:start position:0%
reason I couldn't get do X to work so
whenever<00:04:44.759><c> I'll</c><00:04:44.960><c> go</c><00:04:45.120><c> through</c><00:04:45.240><c> it</c><00:04:45.320><c> you're</c><00:04:45.400><c> going</c>

00:04:45.469 --> 00:04:45.479 align:start position:0%
whenever I'll go through it you're going
 

00:04:45.479 --> 00:04:47.110 align:start position:0%
whenever I'll go through it you're going
to<00:04:45.600><c> see</c><00:04:45.720><c> an</c><00:04:45.840><c> example</c><00:04:46.199><c> here</c><00:04:46.320><c> in</c><00:04:46.400><c> a</c><00:04:46.520><c> minute</c><00:04:47.039><c> uh</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
to see an example here in a minute uh
 

00:04:47.120 --> 00:04:48.430 align:start position:0%
to see an example here in a minute uh
whenever<00:04:47.360><c> you</c><00:04:47.479><c> go</c><00:04:47.600><c> to</c><00:04:47.759><c> download</c><00:04:48.160><c> this</c><00:04:48.280><c> it's</c>

00:04:48.430 --> 00:04:48.440 align:start position:0%
whenever you go to download this it's
 

00:04:48.440 --> 00:04:49.469 align:start position:0%
whenever you go to download this it's
just<00:04:48.600><c> going</c><00:04:48.680><c> to</c><00:04:49.000><c> you're</c><00:04:49.120><c> going</c><00:04:49.199><c> to</c><00:04:49.280><c> have</c><00:04:49.360><c> to</c>

00:04:49.469 --> 00:04:49.479 align:start position:0%
just going to you're going to have to
 

00:04:49.479 --> 00:04:52.230 align:start position:0%
just going to you're going to have to
say<00:04:49.759><c> Okay</c><00:04:50.000><c> on</c><00:04:50.160><c> Microsoft</c><00:04:50.600><c> Word</c><00:04:51.000><c> so</c><00:04:51.160><c> all</c><00:04:51.280><c> in</c><00:04:51.440><c> all</c>

00:04:52.230 --> 00:04:52.240 align:start position:0%
say Okay on Microsoft Word so all in all
 

00:04:52.240 --> 00:04:53.670 align:start position:0%
say Okay on Microsoft Word so all in all
this<00:04:52.360><c> is</c><00:04:52.520><c> really</c><00:04:52.759><c> what's</c><00:04:52.919><c> happening</c><00:04:53.240><c> here</c>

00:04:53.670 --> 00:04:53.680 align:start position:0%
this is really what's happening here
 

00:04:53.680 --> 00:04:55.150 align:start position:0%
this is really what's happening here
we're<00:04:53.840><c> just</c><00:04:53.960><c> having</c><00:04:54.120><c> the</c><00:04:54.240><c> user</c><00:04:54.560><c> proxy</c>

00:04:55.150 --> 00:04:55.160 align:start position:0%
we're just having the user proxy
 

00:04:55.160 --> 00:04:56.990 align:start position:0%
we're just having the user proxy
initiate<00:04:55.600><c> chat</c><00:04:55.800><c> with</c><00:04:55.880><c> the</c><00:04:56.000><c> fitness</c><00:04:56.360><c> expert</c>

00:04:56.990 --> 00:04:57.000 align:start position:0%
initiate chat with the fitness expert
 

00:04:57.000 --> 00:04:58.749 align:start position:0%
initiate chat with the fitness expert
and<00:04:57.160><c> we're</c><00:04:57.560><c> creating</c><00:04:57.919><c> a</c><00:04:58.080><c> custom</c><00:04:58.400><c> message</c>

00:04:58.749 --> 00:04:58.759 align:start position:0%
and we're creating a custom message
 

00:04:58.759 --> 00:05:00.189 align:start position:0%
and we're creating a custom message
based<00:04:58.960><c> on</c><00:04:59.160><c> whatever</c><00:04:59.400><c> you</c><00:04:59.560><c> you</c><00:04:59.680><c> chose</c><00:05:00.000><c> in</c><00:05:00.120><c> the</c>

00:05:00.189 --> 00:05:00.199 align:start position:0%
based on whatever you you chose in the
 

00:05:00.199 --> 00:05:02.469 align:start position:0%
based on whatever you you chose in the
form<00:05:00.560><c> so</c><00:05:00.720><c> let's</c><00:05:00.880><c> do</c><00:05:01.080><c> that</c><00:05:01.600><c> okay</c><00:05:01.800><c> so</c><00:05:02.160><c> how</c><00:05:02.280><c> do</c><00:05:02.360><c> you</c>

00:05:02.469 --> 00:05:02.479 align:start position:0%
form so let's do that okay so how do you
 

00:05:02.479 --> 00:05:03.670 align:start position:0%
form so let's do that okay so how do you
run<00:05:02.720><c> this</c><00:05:02.880><c> so</c><00:05:03.000><c> when</c><00:05:03.120><c> you</c><00:05:03.199><c> go</c><00:05:03.280><c> to</c><00:05:03.400><c> run</c><00:05:03.560><c> this</c>

00:05:03.670 --> 00:05:03.680 align:start position:0%
run this so when you go to run this
 

00:05:03.680 --> 00:05:05.590 align:start position:0%
run this so when you go to run this
locally<00:05:04.440><c> just</c><00:05:04.639><c> open</c><00:05:04.840><c> up</c><00:05:04.960><c> the</c><00:05:05.039><c> terminal</c><00:05:05.400><c> so</c><00:05:05.520><c> if</c>

00:05:05.590 --> 00:05:05.600 align:start position:0%
locally just open up the terminal so if
 

00:05:05.600 --> 00:05:07.110 align:start position:0%
locally just open up the terminal so if
you're<00:05:05.720><c> in</c><00:05:05.759><c> an</c><00:05:05.919><c> IDE</c><00:05:06.520><c> just</c><00:05:06.680><c> open</c><00:05:06.880><c> up</c><00:05:07.000><c> the</c>

00:05:07.110 --> 00:05:07.120 align:start position:0%
you're in an IDE just open up the
 

00:05:07.120 --> 00:05:09.270 align:start position:0%
you're in an IDE just open up the
terminal<00:05:07.520><c> window</c><00:05:08.320><c> when</c><00:05:08.440><c> you</c><00:05:08.560><c> open</c><00:05:08.800><c> that</c><00:05:09.000><c> up</c>

00:05:09.270 --> 00:05:09.280 align:start position:0%
terminal window when you open that up
 

00:05:09.280 --> 00:05:11.350 align:start position:0%
terminal window when you open that up
you're<00:05:09.400><c> just</c><00:05:09.520><c> going</c><00:05:09.639><c> to</c><00:05:09.800><c> type</c><00:05:10.000><c> in</c><00:05:10.360><c> Python</c><00:05:10.800><c> 3</c><00:05:11.199><c> or</c>

00:05:11.350 --> 00:05:11.360 align:start position:0%
you're just going to type in Python 3 or
 

00:05:11.360 --> 00:05:12.830 align:start position:0%
you're just going to type in Python 3 or
just<00:05:11.520><c> python</c><00:05:11.840><c> depending</c><00:05:12.080><c> on</c><00:05:12.160><c> the</c><00:05:12.280><c> version</c><00:05:12.720><c> and</c>

00:05:12.830 --> 00:05:12.840 align:start position:0%
just python depending on the version and
 

00:05:12.840 --> 00:05:13.550 align:start position:0%
just python depending on the version and
then

00:05:13.550 --> 00:05:13.560 align:start position:0%
then
 

00:05:13.560 --> 00:05:15.790 align:start position:0%
then
main.py<00:05:14.560><c> okay</c><00:05:14.919><c> and</c><00:05:15.039><c> whenever</c><00:05:15.280><c> you</c><00:05:15.520><c> press</c>

00:05:15.790 --> 00:05:15.800 align:start position:0%
main.py okay and whenever you press
 

00:05:15.800 --> 00:05:17.629 align:start position:0%
main.py okay and whenever you press
enter<00:05:16.320><c> it's</c><00:05:16.479><c> going</c><00:05:16.560><c> to</c><00:05:16.680><c> start</c><00:05:16.960><c> up</c><00:05:17.160><c> a</c><00:05:17.240><c> flask</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
enter it's going to start up a flask
 

00:05:17.639 --> 00:05:19.710 align:start position:0%
enter it's going to start up a flask
server<00:05:17.960><c> and</c><00:05:18.039><c> then</c><00:05:18.199><c> give</c><00:05:18.319><c> you</c><00:05:18.520><c> a</c><00:05:18.759><c> local</c><00:05:19.600><c> uh</c>

00:05:19.710 --> 00:05:19.720 align:start position:0%
server and then give you a local uh
 

00:05:19.720 --> 00:05:23.430 align:start position:0%
server and then give you a local uh
server<00:05:20.080><c> to</c><00:05:20.240><c> run</c><00:05:20.600><c> so</c><00:05:21.240><c> now</c><00:05:21.680><c> if</c><00:05:21.960><c> I</c><00:05:22.160><c> go</c><00:05:22.400><c> to</c><00:05:23.120><c> this</c><00:05:23.360><c> if</c>

00:05:23.430 --> 00:05:23.440 align:start position:0%
server to run so now if I go to this if
 

00:05:23.440 --> 00:05:27.230 align:start position:0%
server to run so now if I go to this if
I<00:05:23.600><c> click</c><00:05:23.759><c> on</c><00:05:23.840><c> this</c><00:05:23.960><c> to</c><00:05:24.240><c> 127</c><00:05:25.240><c> Port</c><00:05:25.600><c> 5000</c><00:05:26.600><c> which</c><00:05:26.919><c> I</c>

00:05:27.230 --> 00:05:27.240 align:start position:0%
I click on this to 127 Port 5000 which I
 

00:05:27.240 --> 00:05:30.150 align:start position:0%
I click on this to 127 Port 5000 which I
have<00:05:27.759><c> here</c><00:05:28.759><c> it's</c><00:05:29.000><c> going</c><00:05:29.120><c> you're</c><00:05:29.240><c> going</c><00:05:29.360><c> to</c><00:05:29.639><c> see</c>

00:05:30.150 --> 00:05:30.160 align:start position:0%
have here it's going you're going to see
 

00:05:30.160 --> 00:05:32.270 align:start position:0%
have here it's going you're going to see
this<00:05:31.160><c> okay</c><00:05:31.440><c> this</c><00:05:31.560><c> is</c><00:05:31.800><c> what</c><00:05:31.919><c> you're</c><00:05:32.039><c> going</c><00:05:32.120><c> to</c>

00:05:32.270 --> 00:05:32.280 align:start position:0%
this okay this is what you're going to
 

00:05:32.280 --> 00:05:34.510 align:start position:0%
this okay this is what you're going to
see<00:05:33.000><c> and</c><00:05:33.120><c> then</c><00:05:33.280><c> now</c><00:05:33.440><c> that</c><00:05:33.560><c> we're</c><00:05:33.840><c> here</c><00:05:34.319><c> you</c>

00:05:34.510 --> 00:05:34.520 align:start position:0%
see and then now that we're here you
 

00:05:34.520 --> 00:05:36.189 align:start position:0%
see and then now that we're here you
also<00:05:34.720><c> need</c><00:05:34.840><c> to</c><00:05:34.960><c> make</c><00:05:35.080><c> sure</c><00:05:35.240><c> that</c><00:05:35.360><c> LM</c><00:05:35.680><c> Studio</c>

00:05:36.189 --> 00:05:36.199 align:start position:0%
also need to make sure that LM Studio
 

00:05:36.199 --> 00:05:38.070 align:start position:0%
also need to make sure that LM Studio
make<00:05:36.319><c> sure</c><00:05:36.600><c> the</c><00:05:36.720><c> server</c><00:05:36.960><c> is</c><00:05:37.120><c> running</c><00:05:37.440><c> so</c><00:05:37.720><c> let's</c>

00:05:38.070 --> 00:05:38.080 align:start position:0%
make sure the server is running so let's
 

00:05:38.080 --> 00:05:39.309 align:start position:0%
make sure the server is running so let's
make<00:05:38.240><c> sure</c><00:05:38.400><c> of</c><00:05:38.520><c> that</c><00:05:38.680><c> really</c><00:05:38.880><c> quick</c><00:05:39.080><c> cuz</c><00:05:39.199><c> I</c>

00:05:39.309 --> 00:05:39.319 align:start position:0%
make sure of that really quick cuz I
 

00:05:39.319 --> 00:05:41.070 align:start position:0%
make sure of that really quick cuz I
think<00:05:39.440><c> I</c><00:05:39.520><c> stopped</c><00:05:39.880><c> it</c><00:05:40.360><c> yeah</c><00:05:40.520><c> so</c><00:05:40.720><c> let's</c><00:05:40.880><c> start</c>

00:05:41.070 --> 00:05:41.080 align:start position:0%
think I stopped it yeah so let's start
 

00:05:41.080 --> 00:05:43.749 align:start position:0%
think I stopped it yeah so let's start
the<00:05:41.199><c> server</c><00:05:41.800><c> and</c><00:05:42.039><c> let's</c><00:05:42.240><c> say</c><00:05:42.440><c> we</c><00:05:42.560><c> want</c><00:05:42.680><c> to</c><00:05:42.919><c> do</c><00:05:43.520><c> a</c>

00:05:43.749 --> 00:05:43.759 align:start position:0%
the server and let's say we want to do a
 

00:05:43.759 --> 00:05:46.230 align:start position:0%
the server and let's say we want to do a
back<00:05:44.000><c> workout</c><00:05:44.560><c> and</c><00:05:44.759><c> shoulders</c><00:05:45.240><c> for</c><00:05:45.600><c> 3</c><00:05:45.840><c> days</c>

00:05:46.230 --> 00:05:46.240 align:start position:0%
back workout and shoulders for 3 days
 

00:05:46.240 --> 00:05:47.590 align:start position:0%
back workout and shoulders for 3 days
and<00:05:46.400><c> that</c><00:05:46.520><c> we</c><00:05:46.639><c> are</c><00:05:46.840><c> beginner</c><00:05:47.240><c> okay</c><00:05:47.319><c> so</c><00:05:47.479><c> now</c>

00:05:47.590 --> 00:05:47.600 align:start position:0%
and that we are beginner okay so now
 

00:05:47.600 --> 00:05:49.950 align:start position:0%
and that we are beginner okay so now
what's<00:05:47.720><c> going</c><00:05:47.800><c> to</c><00:05:47.960><c> happen</c><00:05:48.280><c> is</c><00:05:48.800><c> I</c><00:05:48.960><c> hit</c><00:05:49.240><c> submit</c>

00:05:49.950 --> 00:05:49.960 align:start position:0%
what's going to happen is I hit submit
 

00:05:49.960 --> 00:05:52.510 align:start position:0%
what's going to happen is I hit submit
and<00:05:50.080><c> then</c><00:05:50.240><c> if</c><00:05:50.360><c> we</c><00:05:50.520><c> go</c><00:05:50.840><c> back</c><00:05:50.960><c> to</c><00:05:51.199><c> LM</c><00:05:51.600><c> Studio</c><00:05:52.280><c> here</c>

00:05:52.510 --> 00:05:52.520 align:start position:0%
and then if we go back to LM Studio here
 

00:05:52.520 --> 00:05:54.590 align:start position:0%
and then if we go back to LM Studio here
you<00:05:52.639><c> can</c><00:05:52.840><c> see</c><00:05:53.280><c> that</c><00:05:53.560><c> it</c><00:05:53.800><c> got</c><00:05:54.080><c> the</c><00:05:54.240><c> message</c>

00:05:54.590 --> 00:05:54.600 align:start position:0%
you can see that it got the message
 

00:05:54.600 --> 00:05:56.309 align:start position:0%
you can see that it got the message
where<00:05:54.759><c> we</c><00:05:54.960><c> want</c><00:05:55.080><c> to</c><00:05:55.240><c> create</c><00:05:55.400><c> a</c><00:05:55.520><c> plan</c><00:05:55.759><c> for</c><00:05:56.080><c> 3</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
where we want to create a plan for 3
 

00:05:56.319 --> 00:05:57.990 align:start position:0%
where we want to create a plan for 3
Days<00:05:56.479><c> of</c><00:05:56.680><c> body</c><00:05:56.919><c> workout</c><00:05:57.319><c> for</c><00:05:57.720><c> back</c><00:05:57.880><c> and</c>

00:05:57.990 --> 00:05:58.000 align:start position:0%
Days of body workout for back and
 

00:05:58.000 --> 00:06:00.070 align:start position:0%
Days of body workout for back and
shoulders<00:05:58.600><c> and</c><00:05:58.840><c> we</c><00:05:58.960><c> are</c><00:05:59.080><c> a</c><00:05:59.160><c> beginner</c>

00:06:00.070 --> 00:06:00.080 align:start position:0%
shoulders and we are a beginner
 

00:06:00.080 --> 00:06:01.710 align:start position:0%
shoulders and we are a beginner
okay<00:06:00.479><c> great</c><00:06:00.800><c> this</c><00:06:00.880><c> is</c><00:06:01.120><c> that's</c><00:06:01.319><c> exactly</c><00:06:01.639><c> what</c>

00:06:01.710 --> 00:06:01.720 align:start position:0%
okay great this is that's exactly what
 

00:06:01.720 --> 00:06:03.590 align:start position:0%
okay great this is that's exactly what
we<00:06:01.840><c> wanted</c><00:06:02.080><c> so</c><00:06:02.160><c> if</c><00:06:02.240><c> we</c><00:06:02.360><c> go</c><00:06:02.479><c> to</c><00:06:02.720><c> pie</c><00:06:02.919><c> charm</c><00:06:03.520><c> you</c>

00:06:03.590 --> 00:06:03.600 align:start position:0%
we wanted so if we go to pie charm you
 

00:06:03.600 --> 00:06:04.790 align:start position:0%
we wanted so if we go to pie charm you
know<00:06:03.720><c> we</c><00:06:03.800><c> can</c><00:06:03.919><c> see</c><00:06:04.039><c> the</c><00:06:04.160><c> same</c><00:06:04.360><c> thing</c><00:06:04.600><c> right</c>

00:06:04.790 --> 00:06:04.800 align:start position:0%
know we can see the same thing right
 

00:06:04.800 --> 00:06:05.909 align:start position:0%
know we can see the same thing right
this<00:06:04.960><c> LM</c><00:06:05.199><c> stud</c><00:06:05.520><c> one</c><00:06:05.600><c> that's</c><00:06:05.680><c> going</c><00:06:05.800><c> to</c><00:06:05.840><c> be</c>

00:06:05.909 --> 00:06:05.919 align:start position:0%
this LM stud one that's going to be
 

00:06:05.919 --> 00:06:07.870 align:start position:0%
this LM stud one that's going to be
handling<00:06:06.319><c> the</c><00:06:06.479><c> response</c><00:06:06.960><c> and</c><00:06:07.080><c> then</c><00:06:07.280><c> if</c><00:06:07.440><c> we</c><00:06:07.599><c> go</c>

00:06:07.870 --> 00:06:07.880 align:start position:0%
handling the response and then if we go
 

00:06:07.880 --> 00:06:10.629 align:start position:0%
handling the response and then if we go
back<00:06:08.039><c> to</c><00:06:08.199><c> LM</c><00:06:08.520><c> Studio</c><00:06:09.400><c> you</c><00:06:09.520><c> know</c><00:06:09.800><c> I'm</c><00:06:09.919><c> going</c><00:06:10.039><c> to</c>

00:06:10.629 --> 00:06:10.639 align:start position:0%
back to LM Studio you know I'm going to
 

00:06:10.639 --> 00:06:12.390 align:start position:0%
back to LM Studio you know I'm going to
let<00:06:10.880><c> this</c><00:06:11.080><c> run</c><00:06:11.759><c> but</c><00:06:11.880><c> you</c><00:06:11.960><c> can</c><00:06:12.080><c> see</c><00:06:12.240><c> it's</c>

00:06:12.390 --> 00:06:12.400 align:start position:0%
let this run but you can see it's
 

00:06:12.400 --> 00:06:14.189 align:start position:0%
let this run but you can see it's
starting<00:06:12.680><c> to</c><00:06:13.240><c> we're</c><00:06:13.360><c> not</c><00:06:13.479><c> streaming</c><00:06:13.840><c> it</c><00:06:14.080><c> right</c>

00:06:14.189 --> 00:06:14.199 align:start position:0%
starting to we're not streaming it right
 

00:06:14.199 --> 00:06:16.029 align:start position:0%
starting to we're not streaming it right
so<00:06:14.360><c> if</c><00:06:14.520><c> you</c><00:06:14.960><c> it</c><00:06:15.039><c> stream</c><00:06:15.280><c> is</c><00:06:15.479><c> by</c><00:06:15.599><c> default</c><00:06:15.840><c> set</c>

00:06:16.029 --> 00:06:16.039 align:start position:0%
so if you it stream is by default set
 

00:06:16.039 --> 00:06:17.790 align:start position:0%
so if you it stream is by default set
the<00:06:16.199><c> false</c><00:06:16.759><c> it's</c><00:06:16.919><c> going</c><00:06:17.039><c> to</c><00:06:17.400><c> do</c><00:06:17.599><c> this</c>

00:06:17.790 --> 00:06:17.800 align:start position:0%
the false it's going to do this
 

00:06:17.800 --> 00:06:19.510 align:start position:0%
the false it's going to do this
accumulation<00:06:18.280><c> of</c><00:06:18.440><c> tokens</c><00:06:18.840><c> thing</c><00:06:19.120><c> right</c><00:06:19.360><c> so</c>

00:06:19.510 --> 00:06:19.520 align:start position:0%
accumulation of tokens thing right so
 

00:06:19.520 --> 00:06:20.749 align:start position:0%
accumulation of tokens thing right so
then<00:06:19.639><c> we</c><00:06:19.720><c> can</c><00:06:19.840><c> just</c><00:06:20.000><c> get</c><00:06:20.120><c> the</c><00:06:20.240><c> response</c><00:06:20.639><c> once</c>

00:06:20.749 --> 00:06:20.759 align:start position:0%
then we can just get the response once
 

00:06:20.759 --> 00:06:22.469 align:start position:0%
then we can just get the response once
it's<00:06:21.039><c> completely</c><00:06:21.520><c> done</c><00:06:21.720><c> in</c><00:06:21.840><c> Elm</c><00:06:22.080><c> Studio</c><00:06:22.319><c> we</c>

00:06:22.469 --> 00:06:22.479 align:start position:0%
it's completely done in Elm Studio we
 

00:06:22.479 --> 00:06:24.070 align:start position:0%
it's completely done in Elm Studio we
get<00:06:22.599><c> the</c><00:06:22.680><c> response</c><00:06:23.039><c> back</c><00:06:23.319><c> um</c><00:06:23.560><c> so</c><00:06:23.720><c> I'm</c><00:06:23.800><c> going</c><00:06:23.880><c> to</c>

00:06:24.070 --> 00:06:24.080 align:start position:0%
get the response back um so I'm going to
 

00:06:24.080 --> 00:06:25.350 align:start position:0%
get the response back um so I'm going to
let<00:06:24.240><c> this</c><00:06:24.520><c> I'm</c><00:06:24.599><c> going</c><00:06:24.759><c> let</c><00:06:24.880><c> this</c><00:06:24.960><c> run</c><00:06:25.160><c> and</c><00:06:25.240><c> I'll</c>

00:06:25.350 --> 00:06:25.360 align:start position:0%
let this I'm going let this run and I'll
 

00:06:25.360 --> 00:06:26.670 align:start position:0%
let this I'm going let this run and I'll
be<00:06:25.479><c> back</c><00:06:25.599><c> whenever</c><00:06:25.840><c> it's</c><00:06:25.960><c> finished</c><00:06:26.319><c> and</c><00:06:26.440><c> we're</c>

00:06:26.670 --> 00:06:26.680 align:start position:0%
be back whenever it's finished and we're
 

00:06:26.680 --> 00:06:29.189 align:start position:0%
be back whenever it's finished and we're
back<00:06:26.919><c> and</c><00:06:27.080><c> LM</c><00:06:27.360><c> Studio</c><00:06:27.880><c> finished</c><00:06:28.599><c> and</c><00:06:28.720><c> if</c><00:06:28.800><c> we</c><00:06:29.000><c> go</c>

00:06:29.189 --> 00:06:29.199 align:start position:0%
back and LM Studio finished and if we go
 

00:06:29.199 --> 00:06:31.790 align:start position:0%
back and LM Studio finished and if we go
back<00:06:29.560><c> to</c><00:06:29.680><c> the</c><00:06:29.800><c> front</c><00:06:30.039><c> end</c><00:06:30.599><c> you</c><00:06:30.720><c> can</c><00:06:30.919><c> see</c><00:06:31.199><c> here</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
back to the front end you can see here
 

00:06:31.800 --> 00:06:34.070 align:start position:0%
back to the front end you can see here
that<00:06:32.440><c> this</c><00:06:32.520><c> is</c><00:06:32.720><c> scrollable</c><00:06:33.400><c> so</c><00:06:33.599><c> it</c><00:06:33.720><c> created</c><00:06:34.000><c> a</c>

00:06:34.070 --> 00:06:34.080 align:start position:0%
that this is scrollable so it created a
 

00:06:34.080 --> 00:06:35.670 align:start position:0%
that this is scrollable so it created a
three<00:06:34.479><c> workout</c><00:06:34.800><c> plan</c><00:06:35.000><c> for</c><00:06:35.160><c> beginners</c>

00:06:35.670 --> 00:06:35.680 align:start position:0%
three workout plan for beginners
 

00:06:35.680 --> 00:06:38.430 align:start position:0%
three workout plan for beginners
focusing<00:06:36.160><c> on</c><00:06:36.440><c> back</c><00:06:36.720><c> and</c><00:06:37.120><c> shoulders</c><00:06:38.120><c> and</c>

00:06:38.430 --> 00:06:38.440 align:start position:0%
focusing on back and shoulders and
 

00:06:38.440 --> 00:06:41.670 align:start position:0%
focusing on back and shoulders and
here's<00:06:39.000><c> day</c><00:06:39.240><c> one</c><00:06:40.240><c> uh</c><00:06:40.400><c> pull-ups</c><00:06:41.240><c> blah</c><00:06:41.479><c> blah</c>

00:06:41.670 --> 00:06:41.680 align:start position:0%
here's day one uh pull-ups blah blah
 

00:06:41.680 --> 00:06:46.230 align:start position:0%
here's day one uh pull-ups blah blah
blah<00:06:42.400><c> day</c><00:06:42.840><c> two</c><00:06:43.840><c> and</c><00:06:44.039><c> then</c><00:06:44.520><c> day</c><00:06:44.800><c> three</c><00:06:45.440><c> okay</c><00:06:46.039><c> and</c>

00:06:46.230 --> 00:06:46.240 align:start position:0%
blah day two and then day three okay and
 

00:06:46.240 --> 00:06:47.749 align:start position:0%
blah day two and then day three okay and
then<00:06:46.680><c> um</c><00:06:46.840><c> it</c><00:06:46.960><c> gives</c><00:06:47.120><c> you</c><00:06:47.280><c> like</c><00:06:47.400><c> kind</c><00:06:47.520><c> of</c><00:06:47.599><c> a</c>

00:06:47.749 --> 00:06:47.759 align:start position:0%
then um it gives you like kind of a
 

00:06:47.759 --> 00:06:49.430 align:start position:0%
then um it gives you like kind of a
summary<00:06:48.120><c> of</c><00:06:48.240><c> the</c><00:06:48.440><c> plan</c><00:06:48.759><c> and</c><00:06:49.039><c> a</c><00:06:49.120><c> little</c><00:06:49.240><c> bit</c>

00:06:49.430 --> 00:06:49.440 align:start position:0%
summary of the plan and a little bit
 

00:06:49.440 --> 00:06:52.430 align:start position:0%
summary of the plan and a little bit
what<00:06:49.560><c> to</c><00:06:49.680><c> do</c><00:06:50.039><c> right</c><00:06:50.720><c> uh</c><00:06:50.840><c> but</c><00:06:51.039><c> now</c><00:06:51.759><c> if</c><00:06:51.919><c> I</c><00:06:52.199><c> click</c>

00:06:52.430 --> 00:06:52.440 align:start position:0%
what to do right uh but now if I click
 

00:06:52.440 --> 00:06:53.790 align:start position:0%
what to do right uh but now if I click
the<00:06:52.560><c> download</c>

00:06:53.790 --> 00:06:53.800 align:start position:0%
the download
 

00:06:53.800 --> 00:06:56.550 align:start position:0%
the download
button<00:06:54.800><c> see</c><00:06:55.240><c> it</c><00:06:55.680><c> well</c><00:06:55.800><c> I</c><00:06:55.919><c> test</c><00:06:56.160><c> this</c><00:06:56.280><c> a</c><00:06:56.360><c> lot</c>

00:06:56.550 --> 00:06:56.560 align:start position:0%
button see it well I test this a lot
 

00:06:56.560 --> 00:06:58.350 align:start position:0%
button see it well I test this a lot
this<00:06:56.639><c> is</c><00:06:56.720><c> the</c><00:06:56.919><c> 13th</c><00:06:57.360><c> of</c><00:06:57.479><c> the</c><00:06:57.599><c> same</c><00:06:57.879><c> document</c>

00:06:58.350 --> 00:06:58.360 align:start position:0%
this is the 13th of the same document
 

00:06:58.360 --> 00:07:01.749 align:start position:0%
this is the 13th of the same document
but<00:06:58.960><c> uh</c><00:06:59.240><c> if</c><00:06:59.400><c> if</c><00:06:59.520><c> you</c><00:06:59.680><c> open</c><00:07:00.479><c> this</c><00:07:01.479><c> this</c><00:07:01.560><c> is</c><00:07:01.680><c> what</c>

00:07:01.749 --> 00:07:01.759 align:start position:0%
but uh if if you open this this is what
 

00:07:01.759 --> 00:07:04.589 align:start position:0%
but uh if if you open this this is what
I<00:07:01.840><c> meant</c><00:07:02.039><c> earlier</c><00:07:02.479><c> because</c><00:07:02.960><c> I</c><00:07:03.080><c> say</c><00:07:03.400><c> as</c><00:07:03.520><c> a</c><00:07:04.319><c> doc</c>

00:07:04.589 --> 00:07:04.599 align:start position:0%
I meant earlier because I say as a doc
 

00:07:04.599 --> 00:07:08.230 align:start position:0%
I meant earlier because I say as a doc
instead<00:07:04.800><c> of</c><00:07:05.319><c> docx</c><00:07:06.319><c> now</c><00:07:06.479><c> if</c><00:07:06.639><c> I</c><00:07:06.800><c> just</c><00:07:06.919><c> hit</c>

00:07:08.230 --> 00:07:08.240 align:start position:0%
instead of docx now if I just hit
 

00:07:08.240 --> 00:07:11.150 align:start position:0%
instead of docx now if I just hit
okay<00:07:09.240><c> here</c><00:07:09.360><c> it</c><00:07:09.520><c> is</c><00:07:09.960><c> here</c><00:07:10.199><c> is</c><00:07:10.440><c> the</c><00:07:10.639><c> downloaded</c>

00:07:11.150 --> 00:07:11.160 align:start position:0%
okay here it is here is the downloaded
 

00:07:11.160 --> 00:07:13.869 align:start position:0%
okay here it is here is the downloaded
version<00:07:11.800><c> of</c><00:07:12.160><c> what</c><00:07:12.319><c> Elm</c><00:07:12.639><c> Studio</c><00:07:13.160><c> gave</c><00:07:13.360><c> us</c><00:07:13.599><c> as</c><00:07:13.720><c> a</c>

00:07:13.869 --> 00:07:13.879 align:start position:0%
version of what Elm Studio gave us as a
 

00:07:13.879 --> 00:07:16.309 align:start position:0%
version of what Elm Studio gave us as a
response<00:07:14.280><c> to</c><00:07:14.759><c> a</c><00:07:15.000><c> custom</c><00:07:15.440><c> message</c><00:07:15.800><c> based</c><00:07:16.080><c> on</c><00:07:16.199><c> a</c>

00:07:16.309 --> 00:07:16.319 align:start position:0%
response to a custom message based on a
 

00:07:16.319 --> 00:07:18.550 align:start position:0%
response to a custom message based on a
form<00:07:16.759><c> that</c><00:07:16.919><c> we</c><00:07:17.080><c> filled</c><00:07:17.440><c> out</c><00:07:17.680><c> on</c><00:07:17.800><c> the</c><00:07:17.919><c> front</c><00:07:18.160><c> end</c>

00:07:18.550 --> 00:07:18.560 align:start position:0%
form that we filled out on the front end
 

00:07:18.560 --> 00:07:20.270 align:start position:0%
form that we filled out on the front end
this<00:07:18.639><c> is</c><00:07:18.840><c> great</c><00:07:19.120><c> this</c><00:07:19.240><c> is</c><00:07:19.360><c> a</c><00:07:19.599><c> great</c><00:07:19.840><c> starter</c>

00:07:20.270 --> 00:07:20.280 align:start position:0%
this is great this is a great starter
 

00:07:20.280 --> 00:07:23.070 align:start position:0%
this is great this is a great starter
for<00:07:20.560><c> getting</c><00:07:21.120><c> a</c><00:07:21.319><c> simple</c><00:07:21.919><c> AI</c><00:07:22.240><c> agency</c><00:07:22.599><c> to</c><00:07:22.840><c> work</c>

00:07:23.070 --> 00:07:23.080 align:start position:0%
for getting a simple AI agency to work
 

00:07:23.080 --> 00:07:25.270 align:start position:0%
for getting a simple AI agency to work
and<00:07:23.360><c> seeing</c><00:07:23.919><c> a</c><00:07:24.039><c> full</c><00:07:24.360><c> stack</c><00:07:24.759><c> where</c><00:07:24.960><c> we</c><00:07:25.039><c> had</c><00:07:25.199><c> the</c>

00:07:25.270 --> 00:07:25.280 align:start position:0%
and seeing a full stack where we had the
 

00:07:25.280 --> 00:07:27.270 align:start position:0%
and seeing a full stack where we had the
front<00:07:25.520><c> end</c><00:07:26.000><c> we</c><00:07:26.120><c> had</c><00:07:26.240><c> the</c><00:07:26.400><c> back</c><00:07:26.599><c> end</c><00:07:27.000><c> that</c><00:07:27.120><c> can</c>

00:07:27.270 --> 00:07:27.280 align:start position:0%
front end we had the back end that can
 

00:07:27.280 --> 00:07:29.670 align:start position:0%
front end we had the back end that can
talk<00:07:27.479><c> to</c><00:07:27.680><c> an</c><00:07:27.919><c> open-</c><00:07:28.280><c> source</c><00:07:28.599><c> llm</c><00:07:29.440><c> and</c><00:07:29.560><c> it</c>

00:07:29.670 --> 00:07:29.680 align:start position:0%
talk to an open- source llm and it
 

00:07:29.680 --> 00:07:31.670 align:start position:0%
talk to an open- source llm and it
didn't<00:07:29.960><c> cost</c><00:07:30.240><c> us</c><00:07:30.520><c> anything</c><00:07:31.120><c> okay</c><00:07:31.280><c> so</c><00:07:31.440><c> here</c><00:07:31.560><c> it</c>

00:07:31.670 --> 00:07:31.680 align:start position:0%
didn't cost us anything okay so here it
 

00:07:31.680 --> 00:07:34.189 align:start position:0%
didn't cost us anything okay so here it
is<00:07:31.879><c> here</c><00:07:32.039><c> is</c><00:07:32.160><c> an</c><00:07:32.400><c> example</c><00:07:32.800><c> of</c><00:07:32.919><c> a</c><00:07:33.199><c> local</c><00:07:33.800><c> open</c>

00:07:34.189 --> 00:07:34.199 align:start position:0%
is here is an example of a local open
 

00:07:34.199 --> 00:07:36.670 align:start position:0%
is here is an example of a local open
source<00:07:34.479><c> llm</c><00:07:35.240><c> it</c><00:07:35.360><c> was</c><00:07:35.599><c> free</c><00:07:36.120><c> didn't</c><00:07:36.400><c> cost</c>

00:07:36.670 --> 00:07:36.680 align:start position:0%
source llm it was free didn't cost
 

00:07:36.680 --> 00:07:38.670 align:start position:0%
source llm it was free didn't cost
anything<00:07:37.400><c> and</c><00:07:37.520><c> it</c><00:07:37.680><c> works</c><00:07:38.199><c> if</c><00:07:38.280><c> you</c><00:07:38.400><c> have</c><00:07:38.520><c> any</c>

00:07:38.670 --> 00:07:38.680 align:start position:0%
anything and it works if you have any
 

00:07:38.680 --> 00:07:40.070 align:start position:0%
anything and it works if you have any
comments<00:07:39.000><c> or</c><00:07:39.160><c> questions</c><00:07:39.560><c> please</c><00:07:39.759><c> leave</c><00:07:39.960><c> them</c>

00:07:40.070 --> 00:07:40.080 align:start position:0%
comments or questions please leave them
 

00:07:40.080 --> 00:07:41.430 align:start position:0%
comments or questions please leave them
down<00:07:40.240><c> below</c><00:07:40.479><c> if</c><00:07:40.560><c> you</c><00:07:40.639><c> have</c><00:07:40.759><c> any</c><00:07:40.919><c> ways</c><00:07:41.120><c> to</c>

00:07:41.430 --> 00:07:41.440 align:start position:0%
down below if you have any ways to
 

00:07:41.440 --> 00:07:43.350 align:start position:0%
down below if you have any ways to
improve<00:07:42.000><c> this</c><00:07:42.160><c> service</c><00:07:42.800><c> you</c><00:07:42.879><c> know</c><00:07:43.039><c> let</c><00:07:43.199><c> me</c>

00:07:43.350 --> 00:07:43.360 align:start position:0%
improve this service you know let me
 

00:07:43.360 --> 00:07:45.390 align:start position:0%
improve this service you know let me
know<00:07:43.720><c> cuz</c><00:07:44.240><c> I'm</c><00:07:44.360><c> not</c><00:07:44.479><c> a</c><00:07:44.560><c> friend</c><00:07:44.759><c> and</c><00:07:44.919><c> developer</c>

00:07:45.390 --> 00:07:45.400 align:start position:0%
know cuz I'm not a friend and developer
 

00:07:45.400 --> 00:07:47.110 align:start position:0%
know cuz I'm not a friend and developer
right<00:07:45.840><c> I'm</c><00:07:46.039><c> I'm</c><00:07:46.120><c> a</c><00:07:46.240><c> backend</c><00:07:46.560><c> developer</c><00:07:46.960><c> by</c>

00:07:47.110 --> 00:07:47.120 align:start position:0%
right I'm I'm a backend developer by
 

00:07:47.120 --> 00:07:49.270 align:start position:0%
right I'm I'm a backend developer by
trade<00:07:47.840><c> so</c><00:07:48.199><c> I</c><00:07:48.360><c> kind</c><00:07:48.479><c> of</c><00:07:48.639><c> just</c><00:07:48.759><c> put</c><00:07:48.960><c> something</c>

00:07:49.270 --> 00:07:49.280 align:start position:0%
trade so I kind of just put something
 

00:07:49.280 --> 00:07:50.909 align:start position:0%
trade so I kind of just put something
together<00:07:49.479><c> that</c><00:07:49.560><c> I</c><00:07:49.680><c> found</c><00:07:49.960><c> online</c><00:07:50.520><c> and</c><00:07:50.759><c> it</c>

00:07:50.909 --> 00:07:50.919 align:start position:0%
together that I found online and it
 

00:07:50.919 --> 00:07:53.390 align:start position:0%
together that I found online and it
worked<00:07:51.520><c> for</c><00:07:51.720><c> me</c><00:07:51.879><c> for</c><00:07:52.039><c> this</c><00:07:52.240><c> example</c><00:07:52.560><c> anyways</c>

00:07:53.390 --> 00:07:53.400 align:start position:0%
worked for me for this example anyways
 

00:07:53.400 --> 00:07:54.990 align:start position:0%
worked for me for this example anyways
so<00:07:53.560><c> if</c><00:07:53.639><c> you</c><00:07:53.759><c> have</c><00:07:53.919><c> any</c><00:07:54.120><c> suggestions</c><00:07:54.759><c> please</c>

00:07:54.990 --> 00:07:55.000 align:start position:0%
so if you have any suggestions please
 

00:07:55.000 --> 00:07:56.670 align:start position:0%
so if you have any suggestions please
let<00:07:55.120><c> me</c><00:07:55.240><c> know</c><00:07:55.680><c> with</c><00:07:55.800><c> that</c><00:07:55.960><c> said</c><00:07:56.199><c> have</c><00:07:56.280><c> a</c><00:07:56.440><c> great</c>

00:07:56.670 --> 00:07:56.680 align:start position:0%
let me know with that said have a great
 

00:07:56.680 --> 00:08:00.080 align:start position:0%
let me know with that said have a great
day<00:07:56.879><c> and</c><00:07:56.960><c> I'll</c><00:07:57.080><c> see</c><00:07:57.240><c> you</c><00:07:57.440><c> next</c><00:07:57.639><c> video</c>

