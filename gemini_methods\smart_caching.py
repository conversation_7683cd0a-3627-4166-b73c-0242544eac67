"""
Smart Caching Service for Gemini API

This module provides intelligent caching strategies for multi-agent document analysis.
It integrates with existing gemini_methods infrastructure to optimize API costs and performance.

Key Features:
- PDF usage pattern analysis with database persistence
- Automatic caching strategy selection (explicit vs sequential implicit)
- Cost optimization through intelligent cache management
- Integration with existing genai_utils.py and gemini_caching.py
- Performance tracking and reporting with database storage
- Real-time cache usage analytics

Usage:
    smart_cache = SmartCachingService(client, db_pool, client_schema)
    await smart_cache.initialize()
    
    # Analyze and optimize caching for a set of questions
    strategy = await smart_cache.analyze_optimal_caching_strategy(questions, project_id)
    
    # Execute questions with optimal caching
    results = await smart_cache.execute_with_optimal_caching(questions, strategy)
"""

import asyncio
import hashlib
import json
import logging
import time
import uuid
from datetime import datetime, timedelta, date, timezone
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, asdict
from collections import defaultdict

from google import genai  # CORRECT SDK - not google.generativeai
from asyncpg.pool import Pool
from pydantic import BaseModel, Field

from AI_workflows_pydantic.gemini_methods.genai_utils import call_gemini_api
from AI_workflows_pydantic.services.config_service import ConfigService
from AI_workflows_pydantic.utils.db_utils import fetch_all, fetch_one, execute_query
from .gemini_caching import create_cache, delete_cache, get_cache, list_caches
from .file_manager import GenaiFileManager

logger = logging.getLogger(__name__)

@dataclass
class PDFGroup:
    """Represents a group of questions that use the same PDF set."""
    pdf_files: Tuple[str, ...]
    questions: List[Dict[str, Any]]
    total_specialist_calls: int
    optimal_strategy: str  # "explicit" or "sequential_implicit"
    estimated_cost_savings: float
    cache_name: Optional[str] = None
    cache_group_id: Optional[str] = None  # Database ID for tracking
    group_hash: Optional[str] = None  # SHA256 hash for consistent identification

@dataclass
class CachingMetrics:
    """Tracks caching performance metrics."""
    total_calls: int = 0
    cached_calls: int = 0
    explicit_cache_hits: int = 0
    implicit_cache_hits: int = 0
    total_cost_savings: float = 0.0
    actual_cost_savings: float = 0.0
    cache_hit_rate: float = 0.0
    cost_reduction_percentage: float = 0.0

@dataclass
class SmartCachingReport:
    """Comprehensive report of smart caching analysis and execution."""
    project_id: str
    analysis_timestamp: str
    pdf_groups: List[PDFGroup]
    caching_metrics: CachingMetrics
    execution_time: float
    strategy_summary: Dict[str, Any]
    created_caches: List[str]

class SmartCachingService:
    """
    Intelligent PDF caching service for multi-agent document analysis.
    Analyzes PDF usage patterns and applies optimal caching strategies.
    """
    
    def __init__(
        self, 
        client: genai.Client, 
        db_pool: Pool,
        client_schema: str = "client_nooob_085c35c4",
    ):
        self.client = client
        self.db_pool = db_pool
        self.client_schema = client_schema
        self.metrics = CachingMetrics()
        self.created_caches: List[str] = []
        
        # Initialize config service for dynamic model loading
        client_id = client_schema.replace("client_nooob_", "") if "client_nooob_" in client_schema else None
        self.config_service = ConfigService(client_id=client_id)
        
        # Model configuration will be loaded dynamically
        self.model_name: Optional[str] = None
        self.model_temperature: float = 0.1
        self.max_tokens: int = 4000
        self.rate_limit_delay: float = 1.0  # Add 1 second delay between API calls
        
        # Caching strategy thresholds (restored missing attributes)
        self.EXPLICIT_CACHE_THRESHOLD = 6  # Use explicit caching for 6+ calls per PDF set
        self.CACHE_TTL = timedelta(hours=24)  # Default cache TTL
        
        logger.info(f"Initialized SmartCachingService for schema {client_schema}")
    
    async def initialize(self) -> bool:
        """
        Initialize the service with dynamic configuration from workflow_settings.
        This replaces hardcoded model configuration with database-driven config.
        """
        try:
            # Load dynamic model configuration from workflow_settings
            workflow_exec_config, models_config = await self.config_service.load_from_workflow_settings()
            
            # Use chapter_agent_model as the primary model for smart caching
            self.model_name = workflow_exec_config.chapter_agent_model_config_name or "gemini-2.5-flash-preview-05-20"
            self.model_temperature = workflow_exec_config.chapter_temperature or 0.1
            self.max_tokens = workflow_exec_config.max_tokens_per_call_override or 4000
            
            logger.info(f"✅ SmartCachingService initialized with model: {self.model_name}")
            logger.info(f"✅ Model temperature: {self.model_temperature}")
            logger.info(f"✅ Max tokens: {self.max_tokens}")
            
            # Ensure database tables exist
            await self._ensure_database_tables()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize SmartCachingService: {str(e)}")
            # Set fallback configuration
            self.model_name = "gemini-2.5-flash-preview-05-20"
            self.model_temperature = 0.1
            self.max_tokens = 4000
            logger.warning(f"⚠️ Using fallback model configuration: {self.model_name}")
            return False
    
    async def _ensure_database_tables(self) -> None:
        """Ensure required database tables exist (restored missing method)."""
        if not self.db_pool:
            logger.warning("No database pool available, skipping table verification")
            return
        
        try:
            async with self.db_pool.acquire() as conn:
                # Check if smart cache tables exist
                result = await conn.fetchval(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = '{self.client_schema}' 
                        AND table_name = 'smart_cache_groups'
                    );
                """)
                if not result:
                    logger.error(f"Smart cache tables not found in schema {self.client_schema}")
                    raise Exception(f"Smart cache tables missing in {self.client_schema}")
                
                logger.debug(f"Database tables verified for schema {self.client_schema}")
                
        except Exception as e:
            logger.error(f"Error verifying database tables: {e}")
            # Don't raise exception - let it continue with warnings
            logger.warning("Continuing without full database table verification")
    
    def _generate_group_hash(self, gemini_file_ids: List[str]) -> str:
        """Generate a consistent hash for a group of Gemini file IDs."""
        # Sort file IDs for consistent hashing regardless of order
        sorted_ids = sorted(gemini_file_ids)
        hash_input = "|".join(sorted_ids)
        return hashlib.sha256(hash_input.encode()).hexdigest()
    
    async def _get_gemini_file_ids_for_pdfs(self, pdf_files: List[str], project_id: str) -> List[str]:
        """
        Get Gemini file IDs for PDF files from the database.
        
        Args:
            pdf_files: List of PDF file names or paths
            project_id: Project ID to filter files
            
        Returns:
            List of Gemini file IDs
        """
        if not self.db_pool:
            logger.warning("No database pool available, using mock file IDs")
            return [f"files/mock_{i}" for i in range(len(pdf_files))]
        
        try:
            async with self.db_pool.acquire() as conn:
                # Query files_uploaded_gemini table for matching files
                query = f"""
                    SELECT DISTINCT gemini_file_id, file_name
                    FROM {self.client_schema}.files_uploaded_gemini
                    WHERE project_id = $1 
                    AND gemini_file_state = 'ACTIVE'
                    AND (file_name = ANY($2) OR gemini_file_id = ANY($2))
                    ORDER BY file_name;
                """
                
                rows = await conn.fetch(query, project_id, pdf_files)
                
                if not rows:
                    logger.warning(f"No Gemini file IDs found for PDFs: {pdf_files}")
                    return []
                
                gemini_file_ids = [row['gemini_file_id'] for row in rows]
                logger.debug(f"Found {len(gemini_file_ids)} Gemini file IDs for {len(pdf_files)} PDFs")
                return gemini_file_ids
                
        except Exception as e:
            logger.error(f"Error retrieving Gemini file IDs: {e}")
            return []
    
    async def _save_cache_group_to_db(
        self, 
        pdf_group: PDFGroup, 
        project_id: str,
        gemini_file_ids: List[str]
    ) -> Optional[str]:
        """
        Save a cache group to the database.
        
        Args:
            pdf_group: The PDF group to save
            project_id: Project ID
            gemini_file_ids: List of Gemini file IDs in this group
            
        Returns:
            Cache group ID if successful, None otherwise
        """
        if not self.db_pool:
            logger.warning("No database pool available, skipping cache group persistence")
            return None
        
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # Generate group hash and ID
                    group_hash = self._generate_group_hash(gemini_file_ids)
                    cache_group_id = str(uuid.uuid4())
                    
                    # Insert cache group
                    group_name = f"smart_cache_{len(pdf_group.pdf_files)}files_{len(pdf_group.questions)}q"
                    expires_at = datetime.now() + self.CACHE_TTL
                    
                    await conn.execute(f"""
                        INSERT INTO {self.client_schema}.smart_cache_groups (
                            cache_group_id, project_id, group_name, group_hash,
                            caching_strategy, gemini_cache_id, gemini_cache_name,
                            cache_ttl_hours, total_questions, total_specialist_calls,
                            estimated_cost_savings, status, expires_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                        ON CONFLICT (project_id, group_hash) DO UPDATE SET
                            updated_at = CURRENT_TIMESTAMP,
                            status = 'active',
                            expires_at = $13
                        RETURNING cache_group_id;
                    """, 
                        cache_group_id, project_id, group_name, group_hash,
                        pdf_group.optimal_strategy, pdf_group.cache_name, pdf_group.cache_name,
                        int(self.CACHE_TTL.total_seconds() / 3600), len(pdf_group.questions),
                        pdf_group.total_specialist_calls, pdf_group.estimated_cost_savings,
                        'active', expires_at
                    )
                    
                    # Insert file associations
                    for i, gemini_file_id in enumerate(gemini_file_ids):
                        await conn.execute(f"""
                            INSERT INTO {self.client_schema}.smart_cache_group_files (
                                cache_group_id, gemini_file_id, file_order
                            ) VALUES ($1, $2, $3)
                            ON CONFLICT (cache_group_id, gemini_file_id) DO NOTHING;
                        """, cache_group_id, gemini_file_id, i)
                    
                    # Update PDFGroup with database info
                    pdf_group.cache_group_id = cache_group_id
                    pdf_group.group_hash = group_hash
                    
                    logger.info(f"Saved cache group {cache_group_id} to database with {len(gemini_file_ids)} files")
                    return cache_group_id
                    
        except Exception as e:
            logger.error(f"Error saving cache group to database: {e}")
            return None
    
    async def _log_cache_usage(
        self,
        cache_group_id: str,
        question_id: Optional[str],
        specialist_type: str,
        cache_hit: bool,
        response_time_ms: Optional[int] = None,
        token_count_input: Optional[int] = None,
        token_count_output: Optional[int] = None,
        cost_saved: Optional[float] = None,
        actual_cost: Optional[float] = None
    ) -> None:
        """Log cache usage to the database for analytics."""
        if not self.db_pool:
            return
        
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute(f"""
                    INSERT INTO {self.client_schema}.smart_cache_usage_log (
                        cache_group_id, question_id, specialist_type, cache_hit,
                        response_time_ms, token_count_input, token_count_output,
                        token_count_total, cost_saved, actual_cost, gemini_model
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11);
                """, 
                    cache_group_id, question_id, specialist_type, cache_hit,
                    response_time_ms, token_count_input, token_count_output,
                    (token_count_input or 0) + (token_count_output or 0),
                    cost_saved, actual_cost, self.model_name
                )
                
        except Exception as e:
            logger.error(f"Error logging cache usage: {e}")
    
    async def analyze_pdf_usage_patterns(
        self, 
        questions: List[Dict[str, Any]], 
        project_id: str
    ) -> List[PDFGroup]:
        """
        Analyze PDF usage patterns across questions to identify optimal grouping.
        
        Args:
            questions: List of question dictionaries with PDF assignments
            project_id: Project ID for database operations
            
        Returns:
            List of PDFGroup objects representing optimal groupings
        """
        logger.info(f"Analyzing PDF usage patterns for {len(questions)} questions in project {project_id}")
        
        # Group questions by their PDF sets
        pdf_groups_dict: Dict[Tuple[str, ...], List[Dict[str, Any]]] = defaultdict(list)
        
        for question in questions:
            # Extract PDF files for this question
            pdf_files = question.get("relevant_pdfs", [])
            if not pdf_files:
                logger.warning(f"Question {question.get('id', 'unknown')} has no PDF files")
                continue
            
            # Create a sorted tuple as the key for consistent grouping
            pdf_set_key = tuple(sorted(pdf_files))
            pdf_groups_dict[pdf_set_key].append(question)
        
        # Convert to PDFGroup objects with strategy analysis
        pdf_groups = []
        for pdf_set, group_questions in pdf_groups_dict.items():
            # Calculate specialist calls (assuming 3 specialists per question: 1 primary + 2 secondary)
            specialists_per_question = 3
            total_calls = len(group_questions) * specialists_per_question
            
            # Determine optimal strategy
            if total_calls >= self.EXPLICIT_CACHE_THRESHOLD:
                strategy = "explicit"
                # Explicit: First call full cost, subsequent calls 75% discount
                estimated_savings = (total_calls - 1) * 0.75
            else:
                strategy = "sequential_implicit"
                # Sequential implicit: 2 cached calls per question (secondary specialists)
                estimated_savings = len(group_questions) * 2 * 0.75
            
            pdf_group = PDFGroup(
                pdf_files=pdf_set,
                questions=group_questions,
                total_specialist_calls=total_calls,
                optimal_strategy=strategy,
                estimated_cost_savings=estimated_savings
            )
            
            # Get Gemini file IDs and save to database
            gemini_file_ids = await self._get_gemini_file_ids_for_pdfs(list(pdf_set), project_id)
            if gemini_file_ids:
                await self._save_cache_group_to_db(pdf_group, project_id, gemini_file_ids)
            
            pdf_groups.append(pdf_group)
        
        logger.info(f"Identified {len(pdf_groups)} PDF groups with optimal strategies")
        return pdf_groups
    
    async def create_explicit_cache_for_pdf_group(
        self, 
        pdf_group: PDFGroup,
        project_id: str,
        system_instruction: Optional[str] = None
    ) -> Optional[str]:
        """
        Create an explicit cache for a PDF group that benefits from explicit caching.
        
        Args:
            pdf_group: The PDF group to create cache for
            project_id: Project ID for file retrieval
            system_instruction: Optional system instruction for the cache
            
        Returns:
            Cache name if successful, None otherwise
        """
        if pdf_group.optimal_strategy != "explicit":
            logger.warning(f"PDF group with {len(pdf_group.pdf_files)} files doesn't benefit from explicit caching")
            return None
        
        # Ensure model_name is available
        if not self.model_name:
            logger.error("Model name not initialized - cannot create cache")
            return None
        
        try:
            # Get Gemini file IDs for the PDF files
            gemini_file_ids = await self._get_gemini_file_ids_for_pdfs(list(pdf_group.pdf_files), project_id)
            if not gemini_file_ids:
                logger.error(f"No Gemini file IDs found for PDF group")
                return None
            
            # Prepare cache contents with actual file references
            cache_contents = []
            
            # Add system instruction if provided
            if system_instruction:
                cache_contents.append({
                    "role": "system",
                    "parts": [{"text": system_instruction}]
                })
            
            # Add PDF file references using actual Gemini file URIs
            for gemini_file_id in gemini_file_ids:
                cache_contents.append({
                    "role": "user",
                    "parts": [{"fileData": {"fileUri": gemini_file_id}}]
                })
            
            # Add context about the cache purpose
            cache_context = f"""
            This cache contains {len(pdf_group.pdf_files)} PDF documents for analysis.
            The cache will be used for {len(pdf_group.questions)} questions with {pdf_group.total_specialist_calls} total specialist calls.
            Expected cost savings: {pdf_group.estimated_cost_savings:.2f} units.
            """
            cache_contents.append({
                "role": "user", 
                "parts": [{"text": cache_context}]
            })
            
            # Create the cache
            display_name = f"smart_cache_{len(pdf_group.pdf_files)}pdfs_{len(pdf_group.questions)}q_{int(time.time())}"
            cache_name = await create_cache(
                client=self.client,
                model_name=self.model_name,  # Now guaranteed to be str, not None
                contents=cache_contents,
                system_instruction=system_instruction,
                display_name=display_name,
                ttl=self.CACHE_TTL
            )
            
            if cache_name:
                pdf_group.cache_name = cache_name
                self.created_caches.append(cache_name)
                
                # Update database with cache information
                if self.db_pool and pdf_group.cache_group_id:
                    try:
                        async with self.db_pool.acquire() as conn:
                            await conn.execute(f"""
                                UPDATE {self.client_schema}.smart_cache_groups 
                                SET gemini_cache_id = $1, gemini_cache_name = $2, updated_at = CURRENT_TIMESTAMP
                                WHERE cache_group_id = $3;
                            """, cache_name, display_name, pdf_group.cache_group_id)
                    except Exception as e:
                        logger.error(f"Error updating cache info in database: {e}")
                
                logger.info(f"Created explicit cache '{cache_name}' for PDF group with {len(pdf_group.pdf_files)} files")
                return cache_name
            else:
                logger.error(f"Failed to create explicit cache for PDF group")
                return None
                
        except Exception as e:
            logger.error(f"Error creating explicit cache for PDF group: {e}")
            return None
    
    async def execute_question_with_optimal_caching(
        self,
        question: Dict[str, Any],
        pdf_group: PDFGroup,
        project_id: str,
        response_schema: Any
    ) -> List[Any]:
        """
        Execute a question with its optimal caching strategy.
        
        Args:
            question: Question dictionary
            pdf_group: The PDF group this question belongs to
            project_id: Project ID for database operations
            response_schema: Pydantic model for response validation
            
        Returns:
            List of specialist responses
        """
        question_id = question.get("id", "unknown")
        question_text = question.get("question_text", question.get("title", ""))
        primary_specialist = question.get("primary_specialist", "")
        secondary_specialists = question.get("secondary_specialists", [])
        
        logger.info(f"Executing question {question_id} with {pdf_group.optimal_strategy} caching")
        
        specialist_responses = []
        
        # Execute primary specialist (no cache benefit for first call)
        primary_response = await self._call_specialist_with_caching(
            specialist_type=primary_specialist,
            question_id=question_id,
            question_text=question_text,
            pdf_files=list(pdf_group.pdf_files),
            cache_name=pdf_group.cache_name,
            cache_group_id=pdf_group.cache_group_id,
            is_primary=True,
            response_schema=response_schema
        )
        specialist_responses.append(primary_response)
        
        # Execute secondary specialists (these benefit from caching)
        for specialist_type in secondary_specialists:
            secondary_response = await self._call_specialist_with_caching(
                specialist_type=specialist_type,
                question_id=question_id,
                question_text=question_text,
                pdf_files=list(pdf_group.pdf_files),
                cache_name=pdf_group.cache_name,
                cache_group_id=pdf_group.cache_group_id,
                is_primary=False,
                response_schema=response_schema
            )
            specialist_responses.append(secondary_response)
        
        return specialist_responses
    
    async def _call_specialist_with_caching(
        self,
        specialist_type: str,
        question_id: str,
        question_text: str,
        pdf_files: List[str],
        cache_name: Optional[str],
        cache_group_id: Optional[str],
        is_primary: bool,
        response_schema: Any
    ) -> Any:
        """
        Call a specialist with appropriate caching strategy.
        
        Args:
            specialist_type: Type of specialist
            question_id: Question identifier
            question_text: Question text
            pdf_files: List of relevant PDF files
            cache_name: Cache name for explicit caching
            cache_group_id: Database cache group ID for logging
            is_primary: Whether this is the primary specialist call
            response_schema: Pydantic model for response validation
            
        Returns:
            Specialist response
        """
        start_time = time.time()
        
        # Ensure model_name is available
        if not self.model_name:
            logger.error("Model name not initialized - cannot make API call")
            raise ValueError("Model name not initialized")
        
        # Build specialist prompt
        specialist_prompt = f"""
        You are a {specialist_type} analyzing a solar energy project.
        
        Question: {question_text}
        
        Relevant documents: {', '.join(pdf_files)}
        
        Please provide a detailed analysis including:
        1. Key findings based on the documents
        2. Specific recommendations
        3. Risk assessments where applicable
        4. Confidence level in your analysis
        
        Focus on aspects most relevant to your expertise as a {specialist_type}.
        """
        
        # Determine cache usage
        cache_type = "none"
        effective_cache_name = None
        
        if cache_name:  # Explicit cache available
            cache_type = "explicit"
            effective_cache_name = cache_name
        elif not is_primary:  # Secondary specialist benefits from implicit caching
            cache_type = "implicit"
        
        # Track metrics
        self.metrics.total_calls += 1
        cache_hit = cache_type in ["explicit", "implicit"]
        
        # Add rate limiting to prevent API overload
        if not is_primary:  # Add delay for secondary specialists
            logger.debug(f"Adding {self.rate_limit_delay}s delay before secondary specialist call")
            await asyncio.sleep(self.rate_limit_delay)
        
        try:
            # Call Gemini API with caching
            response, input_tokens, output_tokens, calculated_cost = await call_gemini_api(
                model_name=self.model_name,  # Now guaranteed to be str, not None
                db_pool=self.db_pool,
                contents=[specialist_prompt],
                response_schema=response_schema,
                cached_content_name=effective_cache_name,
                system_instruction_text=f"You are an expert {specialist_type} providing detailed analysis."
            )
            
            response_time_ms = int((time.time() - start_time) * 1000)
            cost_saved = 0.0
            
            # Update metrics
            if cache_hit:
                self.metrics.cached_calls += 1
                if cache_type == "explicit":
                    self.metrics.explicit_cache_hits += 1
                else:
                    self.metrics.implicit_cache_hits += 1
                
                # Calculate cost savings (75% discount for cached calls)
                if calculated_cost:
                    cost_saved = calculated_cost * 0.75
                    self.metrics.total_cost_savings += cost_saved
                    self.metrics.actual_cost_savings += cost_saved
            
            # 🆕 SAVE SPECIALIST RESPONSE TO DATABASE
            await self._save_specialist_response_to_db(
                specialist_type=specialist_type,
                question_id=question_id,
                question_text=question_text,
                response=response,
                pdf_files=pdf_files,
                processing_time_ms=response_time_ms,
                calculated_cost=calculated_cost
            )
            
            # Log usage to database
            if cache_group_id:
                await self._log_cache_usage(
                    cache_group_id=cache_group_id,
                    question_id=question_id,
                    specialist_type=specialist_type,
                    cache_hit=cache_hit,
                    response_time_ms=response_time_ms,
                    token_count_input=input_tokens,
                    token_count_output=output_tokens,
                    cost_saved=cost_saved,
                    actual_cost=calculated_cost
                )
            
            logger.debug(f"Specialist {specialist_type} completed with {cache_type} caching in {response_time_ms}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error calling specialist {specialist_type}: {e}")
            
            # Log failed usage
            if cache_group_id:
                await self._log_cache_usage(
                    cache_group_id=cache_group_id,
                    question_id=question_id,
                    specialist_type=specialist_type,
                    cache_hit=False,
                    response_time_ms=int((time.time() - start_time) * 1000)
                )
            
            raise
    
    async def _save_specialist_response_to_db(
        self,
        specialist_type: str,
        question_id: str,
        question_text: str,
        response: Any,
        pdf_files: List[str],
        processing_time_ms: int,
        calculated_cost: Optional[float]
    ) -> None:
        """
        Save specialist response to the specialist_analysis_answers table.
        
        Args:
            specialist_type: Type of specialist
            question_id: Question identifier
            question_text: Question text
            response: Specialist response object
            pdf_files: List of PDF files analyzed
            processing_time_ms: Processing time in milliseconds
            calculated_cost: API cost for this call
        """
        if not self.db_pool:
            logger.warning("No database pool available - cannot save specialist response")
            return
        
        try:
            # Generate unique ID for this response
            import uuid
            response_id = str(uuid.uuid4())
            
            # Get document IDs for the PDF files analyzed
            document_ids = []
            if pdf_files:
                async with self.db_pool.acquire() as conn:
                    # Get document IDs from files_uploaded_gemini table
                    doc_query = f"""
                        SELECT DISTINCT original_document_id 
                        FROM {self.client_schema}.files_uploaded_gemini
                        WHERE file_name = ANY($1)
                        AND gemini_file_state = 'ACTIVE'
                    """
                    doc_rows = await conn.fetch(doc_query, pdf_files)
                    document_ids = [str(row['original_document_id']) for row in doc_rows]
            
            # Parse response data
            key_findings = []
            recommendations = []
            risk_assessment = {}
            supporting_evidence = []
            confidence_score = 0.8  # Default
            
            # Extract data from response object
            if hasattr(response, 'dict'):
                response_dict = response.dict()
            elif isinstance(response, dict):
                response_dict = response
            else:
                response_dict = {"analysis": str(response)}
            
            # Extract structured data from response
            if "analysis" in response_dict:
                analysis = response_dict["analysis"]
                if isinstance(analysis, dict):
                    key_findings = analysis.get("key_findings", [])
                    recommendations = analysis.get("recommendations", [])
                    risk_assessment = analysis.get("risk_assessment", {})
                    supporting_evidence = analysis.get("supporting_evidence", [])
                    confidence_score = analysis.get("confidence_score", 0.8)
                elif isinstance(analysis, str):
                    # If analysis is a string, wrap it in basic structure
                    key_findings = [{"finding": analysis[:500] + "..." if len(analysis) > 500 else analysis}]
                    recommendations = [{"recommendation": "Detailed analysis provided"}]
                    supporting_evidence = [{"evidence": f"Analysis based on {len(pdf_files)} documents"}]
            
            # Ensure confidence_score is within bounds
            confidence_score = max(0.0, min(1.0, float(confidence_score)))
            
            # Save to database
            async with self.db_pool.acquire() as conn:
                insert_query = f"""
                    INSERT INTO {self.client_schema}.specialist_analysis_answers (
                        id,
                        project_id,
                        session_id,
                        question_text,
                        specialist_type,
                        analyzed_document_ids,
                        key_findings,
                        recommendations,
                        risk_assessment,
                        supporting_evidence,
                        confidence_score,
                        created_at,
                        updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """
                
                # Extract project_id from context (use DEFAULT_PROJECT_ID for now)
                project_id = "4e60c6cb-fdbf-4fa2-93e6-006c19709a75"  # DEFAULT_PROJECT_ID
                session_id = f"smart_cache_{int(time.time())}"
                
                await conn.execute(
                    insert_query,
                    response_id,
                    project_id,
                    session_id,
                    question_text,
                    specialist_type,
                    document_ids,
                    json.dumps(key_findings),
                    json.dumps(recommendations),
                    json.dumps(risk_assessment),
                    json.dumps(supporting_evidence),
                    confidence_score
                )
            
            logger.info(f"✅ Saved {specialist_type} response to specialist_analysis_answers table (ID: {response_id})")
            
        except Exception as e:
            logger.error(f"❌ Error saving specialist response to database: {e}")
            # Don't raise - this shouldn't fail the analysis process
    
    async def execute_with_optimal_caching(
        self,
        questions: List[Dict[str, Any]],
        pdf_groups: List[PDFGroup],
        project_id: str,
        response_schema: Any,
        system_instruction: Optional[str] = None
    ) -> SmartCachingReport:
        """
        Execute all questions with optimal caching strategies.
        
        Args:
            questions: List of questions to execute
            pdf_groups: PDF groups with optimal strategies
            project_id: Project ID for database operations
            response_schema: Pydantic model for response validation
            system_instruction: Optional system instruction for caches
            
        Returns:
            Comprehensive smart caching report
        """
        start_time = time.time()
        logger.info(f"Executing {len(questions)} questions with optimal caching for project {project_id}")
        
        # Create explicit caches for groups that benefit from them
        for pdf_group in pdf_groups:
            if pdf_group.optimal_strategy == "explicit":
                await self.create_explicit_cache_for_pdf_group(pdf_group, project_id, system_instruction)
        
        # Execute questions grouped by their PDF sets
        all_results = []
        for pdf_group in pdf_groups:
            for question in pdf_group.questions:
                question_results = await self.execute_question_with_optimal_caching(
                    question, pdf_group, project_id, response_schema
                )
                all_results.extend(question_results)
        
        # Calculate final metrics
        if self.metrics.total_calls > 0:
            self.metrics.cache_hit_rate = self.metrics.cached_calls / self.metrics.total_calls
            # FIX: Calculate cost reduction as percentage of cached calls with standard 75% discount
            # This shows the actual percentage reduction achieved through caching
            if self.metrics.cached_calls > 0:
                # If we have cache hits, the reduction is proportional to cache hit rate * discount rate
                cache_discount_rate = 0.75  # 75% discount for cached calls
                self.metrics.cost_reduction_percentage = (self.metrics.cache_hit_rate * cache_discount_rate) * 100
            else:
                self.metrics.cost_reduction_percentage = 0.0
        
        execution_time = time.time() - start_time
        
        # Update database with final performance metrics
        await self._update_performance_summary(pdf_groups)
        
        # Create strategy summary
        strategy_summary = {
            "total_pdf_groups": len(pdf_groups),
            "explicit_cache_groups": sum(1 for g in pdf_groups if g.optimal_strategy == "explicit"),
            "sequential_implicit_groups": sum(1 for g in pdf_groups if g.optimal_strategy == "sequential_implicit"),
            "total_questions": len(questions),
            "estimated_total_savings": sum(g.estimated_cost_savings for g in pdf_groups),
            "actual_total_savings": self.metrics.actual_cost_savings
        }
        
        # Generate report
        report = SmartCachingReport(
            project_id=project_id,
            analysis_timestamp=datetime.now().isoformat(),
            pdf_groups=pdf_groups,
            caching_metrics=self.metrics,
            execution_time=execution_time,
            strategy_summary=strategy_summary,
            created_caches=self.created_caches.copy()
        )
        
        logger.info(f"Smart caching execution completed in {execution_time:.2f}s with {self.metrics.cache_hit_rate:.1%} cache hit rate")
        return report
    
    async def _update_performance_summary(self, pdf_groups: List[PDFGroup]) -> None:
        """Update daily performance summary in the database."""
        if not self.db_pool:
            return
        
        try:
            today = date.today()
            async with self.db_pool.acquire() as conn:
                for pdf_group in pdf_groups:
                    if not pdf_group.cache_group_id:
                        continue
                    
                    # Calculate metrics for this group
                    group_calls = pdf_group.total_specialist_calls
                    group_hits = int(self.metrics.cached_calls * (group_calls / self.metrics.total_calls)) if self.metrics.total_calls > 0 else 0
                    group_misses = group_calls - group_hits
                    
                    await conn.execute(f"""
                        INSERT INTO {self.client_schema}.smart_cache_performance_summary (
                            cache_group_id, date_bucket, total_calls, cache_hits, cache_misses,
                            total_cost_saved, avg_response_time_ms
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                        ON CONFLICT (cache_group_id, date_bucket) DO UPDATE SET
                            total_calls = EXCLUDED.total_calls,
                            cache_hits = EXCLUDED.cache_hits,
                            cache_misses = EXCLUDED.cache_misses,
                            total_cost_saved = EXCLUDED.total_cost_saved,
                            avg_response_time_ms = EXCLUDED.avg_response_time_ms,
                            updated_at = CURRENT_TIMESTAMP;
                    """, 
                        pdf_group.cache_group_id, today, group_calls, group_hits, group_misses,
                        pdf_group.estimated_cost_savings, 500  # Placeholder avg response time
                    )
                    
        except Exception as e:
            logger.error(f"Error updating performance summary: {e}")
    
    async def cleanup_created_caches(self) -> None:
        """Clean up all caches created during this session."""
        if not self.created_caches:
            logger.info("No caches to clean up")
            return
        
        logger.info(f"Cleaning up {len(self.created_caches)} created caches")
        
        for cache_name in self.created_caches:
            try:
                success = await delete_cache(self.client, cache_name)
                if success:
                    logger.debug(f"Successfully deleted cache: {cache_name}")
                    
                    # Update database status
                    if self.db_pool:
                        try:
                            async with self.db_pool.acquire() as conn:
                                await conn.execute(f"""
                                    UPDATE {self.client_schema}.smart_cache_groups 
                                    SET status = 'deleted', updated_at = CURRENT_TIMESTAMP
                                    WHERE gemini_cache_id = $1;
                                """, cache_name)
                        except Exception as e:
                            logger.error(f"Error updating cache status in database: {e}")
                else:
                    logger.warning(f"Failed to delete cache: {cache_name}")
            except Exception as e:
                logger.error(f"Error deleting cache {cache_name}: {e}")
        
        self.created_caches.clear()
        logger.info("Cache cleanup completed")
    
    async def get_cache_analytics(self, project_id: str, days: int = 7) -> Dict[str, Any]:
        """
        Get cache analytics for a project over the specified number of days.
        
        Args:
            project_id: Project ID to analyze
            days: Number of days to look back
            
        Returns:
            Dictionary with cache analytics
        """
        if not self.db_pool:
            return {"error": "No database connection available"}
        
        try:
            async with self.db_pool.acquire() as conn:
                # Get cache groups for the project
                groups = await conn.fetch(f"""
                    SELECT cache_group_id, group_name, caching_strategy, 
                           total_questions, total_specialist_calls, estimated_cost_savings,
                           actual_cost_savings, cache_hit_rate, status, created_at
                    FROM {self.client_schema}.smart_cache_groups
                    WHERE project_id = $1 
                    AND created_at >= CURRENT_DATE - INTERVAL '{days} days'
                    ORDER BY created_at DESC;
                """, project_id)
                
                # Get usage statistics
                usage_stats = await conn.fetch(f"""
                    SELECT scg.caching_strategy, 
                           COUNT(*) as total_calls,
                           SUM(CASE WHEN scul.cache_hit THEN 1 ELSE 0 END) as cache_hits,
                           AVG(scul.response_time_ms) as avg_response_time,
                           SUM(scul.cost_saved) as total_cost_saved
                    FROM {self.client_schema}.smart_cache_usage_log scul
                    JOIN {self.client_schema}.smart_cache_groups scg ON scul.cache_group_id = scg.cache_group_id
                    WHERE scg.project_id = $1 
                    AND scul.created_at >= CURRENT_DATE - INTERVAL '{days} days'
                    GROUP BY scg.caching_strategy;
                """, project_id)
                
                return {
                    "project_id": project_id,
                    "analysis_period_days": days,
                    "cache_groups": [dict(row) for row in groups],
                    "usage_statistics": [dict(row) for row in usage_stats],
                    "total_groups": len(groups),
                    "generated_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error getting cache analytics: {e}")
            return {"error": str(e)}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of caching performance metrics."""
        return {
            "total_calls": self.metrics.total_calls,
            "cached_calls": self.metrics.cached_calls,
            "cache_hit_rate": f"{self.metrics.cache_hit_rate:.1%}",
            "explicit_cache_hits": self.metrics.explicit_cache_hits,
            "implicit_cache_hits": self.metrics.implicit_cache_hits,
            "total_cost_savings": self.metrics.total_cost_savings,
            "actual_cost_savings": self.metrics.actual_cost_savings,
            "cost_reduction_percentage": f"{self.metrics.cost_reduction_percentage:.1f}%",
            "created_caches": len(self.created_caches)
        }

# Convenience function for quick analysis
async def analyze_optimal_caching_strategy(
    questions: List[Dict[str, Any]],
    project_id: str,
    client: genai.Client,
    db_pool: Pool,
    client_schema: str = "client_nooob_085c35c4"
) -> List[PDFGroup]:
    """
    Quick function to analyze optimal caching strategy for a set of questions.
    
    Args:
        questions: List of question dictionaries
        project_id: Project ID for database operations
        client: Gemini client
        db_pool: Database pool
        client_schema: Client schema name
        
    Returns:
        List of PDFGroup objects with optimal strategies
    """
    service = SmartCachingService(client, db_pool, client_schema)
    await service.initialize()
    return await service.analyze_pdf_usage_patterns(questions, project_id) 