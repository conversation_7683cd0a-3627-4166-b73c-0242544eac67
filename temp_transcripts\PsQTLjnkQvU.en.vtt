WEBVTT
Kind: captions
Language: en

00:00:09.120 --> 00:00:12.629 align:start position:0%
 
hello<00:00:09.719><c> I'm</c><00:00:09.920><c> <PERSON><PERSON></c><00:00:10.759><c> from</c><00:00:11.200><c> DCU</c><00:00:12.200><c> in</c><00:00:12.400><c> this</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
hello I'm <PERSON><PERSON> from DCU in this
 

00:00:12.639 --> 00:00:14.430 align:start position:0%
hello I'm <PERSON><PERSON> from DCU in this
presentation<00:00:13.519><c> I'll</c><00:00:13.759><c> be</c><00:00:13.880><c> showing</c><00:00:14.240><c> the</c>

00:00:14.430 --> 00:00:14.440 align:start position:0%
presentation I'll be showing the
 

00:00:14.440 --> 00:00:17.109 align:start position:0%
presentation I'll be showing the
concentrated<00:00:15.200><c> solar</c><00:00:15.679><c> power</c><00:00:16.000><c> model</c><00:00:16.640><c> used</c><00:00:16.920><c> in</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
concentrated solar power model used in
 

00:00:17.119 --> 00:00:19.550 align:start position:0%
concentrated solar power model used in
PCD<00:00:17.640><c> version</c>

00:00:19.550 --> 00:00:19.560 align:start position:0%
PCD version
 

00:00:19.560 --> 00:00:22.390 align:start position:0%
PCD version
4.2<00:00:20.560><c> we'll</c><00:00:20.760><c> be</c><00:00:20.920><c> looking</c><00:00:21.160><c> at</c><00:00:21.279><c> the</c><00:00:21.439><c> key</c><00:00:21.720><c> parts</c><00:00:22.240><c> of</c>

00:00:22.390 --> 00:00:22.400 align:start position:0%
4.2 we'll be looking at the key parts of
 

00:00:22.400 --> 00:00:25.509 align:start position:0%
4.2 we'll be looking at the key parts of
the<00:00:22.599><c> model</c><00:00:23.599><c> and</c><00:00:23.840><c> then</c><00:00:24.039><c> the</c><00:00:24.519><c> simulated</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
the model and then the simulated
 

00:00:25.519 --> 00:00:31.109 align:start position:0%
the model and then the simulated
generation<00:00:26.119><c> cases</c><00:00:26.519><c> for</c><00:00:27.119><c> PCD</c>

00:00:31.109 --> 00:00:31.119 align:start position:0%
 
 

00:00:31.119 --> 00:00:34.310 align:start position:0%
 
the<00:00:31.399><c> model</c><00:00:32.399><c> start</c><00:00:32.759><c> from</c><00:00:33.000><c> direct</c><00:00:33.320><c> normal</c><00:00:33.680><c> IR</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
the model start from direct normal IR
 

00:00:34.320 --> 00:00:37.110 align:start position:0%
the model start from direct normal IR
Radiance<00:00:35.320><c> which</c><00:00:35.520><c> go</c><00:00:35.800><c> through</c><00:00:35.960><c> a</c><00:00:36.079><c> solar</c><00:00:36.440><c> field</c>

00:00:37.110 --> 00:00:37.120 align:start position:0%
Radiance which go through a solar field
 

00:00:37.120 --> 00:00:40.709 align:start position:0%
Radiance which go through a solar field
power<00:00:37.440><c> block</c><00:00:38.239><c> to</c><00:00:38.480><c> produce</c><00:00:38.800><c> electrical</c><00:00:39.719><c> power</c>

00:00:40.709 --> 00:00:40.719 align:start position:0%
power block to produce electrical power
 

00:00:40.719 --> 00:00:42.510 align:start position:0%
power block to produce electrical power
we<00:00:40.800><c> can</c><00:00:41.000><c> also</c><00:00:41.360><c> have</c><00:00:41.559><c> a</c><00:00:41.719><c> thermal</c><00:00:42.120><c> energy</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
we can also have a thermal energy
 

00:00:42.520 --> 00:00:44.670 align:start position:0%
we can also have a thermal energy
storage<00:00:43.039><c> of</c><00:00:43.280><c> varying</c>

00:00:44.670 --> 00:00:44.680 align:start position:0%
storage of varying
 

00:00:44.680 --> 00:00:47.069 align:start position:0%
storage of varying
size<00:00:45.680><c> the</c><00:00:45.800><c> model</c><00:00:46.120><c> is</c><00:00:46.239><c> actually</c><00:00:46.520><c> unchanged</c>

00:00:47.069 --> 00:00:47.079 align:start position:0%
size the model is actually unchanged
 

00:00:47.079 --> 00:00:50.110 align:start position:0%
size the model is actually unchanged
from<00:00:47.239><c> PCD</c><00:00:47.800><c> version</c><00:00:48.120><c> 3</c><00:00:48.800><c> so</c><00:00:49.199><c> on</c><00:00:49.360><c> the</c><00:00:49.480><c> model</c><00:00:49.879><c> side</c>

00:00:50.110 --> 00:00:50.120 align:start position:0%
from PCD version 3 so on the model side
 

00:00:50.120 --> 00:00:52.549 align:start position:0%
from PCD version 3 so on the model side
there's<00:00:50.399><c> nothing</c><00:00:50.680><c> new</c><00:00:51.680><c> but</c><00:00:51.879><c> of</c><00:00:52.039><c> course</c><00:00:52.399><c> we</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
there's nothing new but of course we
 

00:00:52.559 --> 00:00:54.549 align:start position:0%
there's nothing new but of course we
have<00:00:52.760><c> access</c><00:00:53.000><c> to</c><00:00:53.199><c> dni</c><00:00:53.680><c> now</c><00:00:54.000><c> both</c><00:00:54.199><c> from</c><00:00:54.399><c> the</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
have access to dni now both from the
 

00:00:54.559 --> 00:00:57.389 align:start position:0%
have access to dni now both from the
historical<00:00:55.199><c> weather</c><00:00:55.719><c> AI</c><00:00:56.280><c> based</c><00:00:57.120><c> and</c><00:00:57.239><c> the</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
historical weather AI based and the
 

00:00:57.399 --> 00:00:59.270 align:start position:0%
historical weather AI based and the
climate<00:00:57.760><c> projections</c><00:00:58.280><c> so</c><00:00:58.480><c> we</c><00:00:58.600><c> can</c><00:00:58.800><c> apply</c><00:00:59.120><c> the</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
climate projections so we can apply the
 

00:00:59.280 --> 00:01:05.270 align:start position:0%
climate projections so we can apply the
model<00:01:00.039><c> also</c><00:01:00.320><c> under</c><00:01:00.600><c> the</c><00:01:00.760><c> climate</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
 
 

00:01:05.280 --> 00:01:08.950 align:start position:0%
 
projections<00:01:06.280><c> in</c><00:01:06.439><c> the</c><00:01:06.600><c> model</c><00:01:07.560><c> we</c><00:01:07.759><c> have</c><00:01:08.159><c> several</c>

00:01:08.950 --> 00:01:08.960 align:start position:0%
projections in the model we have several
 

00:01:08.960 --> 00:01:11.950 align:start position:0%
projections in the model we have several
parameters<00:01:09.960><c> out</c><00:01:10.119><c> of</c><00:01:10.360><c> which</c><00:01:10.600><c> some</c><00:01:11.360><c> are</c><00:01:11.600><c> assumed</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
parameters out of which some are assumed
 

00:01:11.960 --> 00:01:14.990 align:start position:0%
parameters out of which some are assumed
to<00:01:12.119><c> be</c><00:01:12.360><c> given</c><00:01:12.840><c> as</c><00:01:13.080><c> inputs</c><00:01:13.520><c> for</c><00:01:13.880><c> every</c><00:01:14.600><c> model</c>

00:01:14.990 --> 00:01:15.000 align:start position:0%
to be given as inputs for every model
 

00:01:15.000 --> 00:01:17.990 align:start position:0%
to be given as inputs for every model
CSD<00:01:15.520><c> plant</c><00:01:16.400><c> like</c><00:01:16.600><c> the</c><00:01:16.720><c> install</c><00:01:17.200><c> capacity</c><00:01:17.840><c> and</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
CSD plant like the install capacity and
 

00:01:18.000 --> 00:01:19.789 align:start position:0%
CSD plant like the install capacity and
the<00:01:18.119><c> storage</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
the storage
 

00:01:19.799 --> 00:01:22.590 align:start position:0%
the storage
capacity<00:01:20.799><c> there</c><00:01:20.920><c> are</c><00:01:21.159><c> also</c><00:01:21.640><c> some</c><00:01:22.000><c> parameters</c>

00:01:22.590 --> 00:01:22.600 align:start position:0%
capacity there are also some parameters
 

00:01:22.600 --> 00:01:24.710 align:start position:0%
capacity there are also some parameters
like<00:01:22.759><c> the</c><00:01:22.880><c> solar</c><00:01:23.200><c> multiple</c><00:01:23.960><c> which</c><00:01:24.119><c> can</c><00:01:24.280><c> be</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
like the solar multiple which can be
 

00:01:24.720 --> 00:01:27.069 align:start position:0%
like the solar multiple which can be
estimated<00:01:25.720><c> from</c><00:01:25.920><c> the</c><00:01:26.079><c> storage</c>

00:01:27.069 --> 00:01:27.079 align:start position:0%
estimated from the storage
 

00:01:27.079 --> 00:01:30.950 align:start position:0%
estimated from the storage
size<00:01:28.079><c> basically</c><00:01:28.600><c> a</c><00:01:29.040><c> larger</c><00:01:29.520><c> storage</c><00:01:30.320><c> implies</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
size basically a larger storage implies
 

00:01:30.960 --> 00:01:33.830 align:start position:0%
size basically a larger storage implies
a<00:01:31.200><c> lar</c><00:01:31.759><c> lar</c><00:01:32.000><c> of</c><00:01:32.119><c> solar</c><00:01:32.399><c> multiple</c><00:01:33.320><c> so</c><00:01:33.520><c> that</c><00:01:33.680><c> we</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
a lar lar of solar multiple so that we
 

00:01:33.840 --> 00:01:35.990 align:start position:0%
a lar lar of solar multiple so that we
have<00:01:34.040><c> enough</c><00:01:34.360><c> production</c><00:01:34.920><c> to</c><00:01:35.119><c> put</c><00:01:35.360><c> something</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
have enough production to put something
 

00:01:36.000 --> 00:01:38.310 align:start position:0%
have enough production to put something
in<00:01:36.240><c> the</c><00:01:36.560><c> energy</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
in the energy
 

00:01:38.320 --> 00:01:40.950 align:start position:0%
in the energy
storage<00:01:39.320><c> then</c><00:01:39.520><c> there</c><00:01:39.640><c> are</c><00:01:39.880><c> also</c><00:01:40.320><c> a</c><00:01:40.479><c> number</c><00:01:40.720><c> of</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
storage then there are also a number of
 

00:01:40.960 --> 00:01:43.670 align:start position:0%
storage then there are also a number of
variables<00:01:41.759><c> which</c><00:01:41.880><c> are</c><00:01:42.159><c> simply</c><00:01:42.600><c> estimated</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
variables which are simply estimated
 

00:01:43.680 --> 00:01:45.950 align:start position:0%
variables which are simply estimated
empirically<00:01:44.680><c> and</c><00:01:44.840><c> it's</c><00:01:45.079><c> important</c><00:01:45.439><c> to</c><00:01:45.600><c> know</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
empirically and it's important to know
 

00:01:45.960 --> 00:01:47.950 align:start position:0%
empirically and it's important to know
that<00:01:46.159><c> this</c><00:01:46.360><c> model</c><00:01:46.799><c> is</c><00:01:47.000><c> not</c><00:01:47.280><c> based</c><00:01:47.560><c> on</c><00:01:47.719><c> any</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
that this model is not based on any
 

00:01:47.960 --> 00:01:51.990 align:start position:0%
that this model is not based on any
physical<00:01:48.520><c> modeling</c><00:01:49.520><c> this</c><00:01:49.680><c> is</c><00:01:49.960><c> simply</c><00:01:51.000><c> an</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
physical modeling this is simply an
 

00:01:52.000 --> 00:01:54.749 align:start position:0%
physical modeling this is simply an
empirical<00:01:52.600><c> model</c><00:01:53.479><c> where</c><00:01:53.759><c> the</c><00:01:53.960><c> parameters</c><00:01:54.479><c> are</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
empirical model where the parameters are
 

00:01:54.759 --> 00:01:58.350 align:start position:0%
empirical model where the parameters are
estimated<00:01:55.360><c> based</c><00:01:55.640><c> on</c><00:01:55.920><c> measure</c><00:01:56.880><c> data</c><00:01:57.880><c> the</c><00:01:58.079><c> heat</c>

00:01:58.350 --> 00:01:58.360 align:start position:0%
estimated based on measure data the heat
 

00:01:58.360 --> 00:02:00.910 align:start position:0%
estimated based on measure data the heat
transfer<00:01:58.920><c> fluid</c><00:01:59.680><c> is</c><00:01:59.920><c> is</c><00:02:00.039><c> modeled</c><00:02:00.399><c> as</c><00:02:00.520><c> a</c><00:02:00.680><c> first</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
transfer fluid is is modeled as a first
 

00:02:00.920 --> 00:02:04.350 align:start position:0%
transfer fluid is is modeled as a first
order<00:02:01.280><c> nomical</c><00:02:02.320><c> system</c><00:02:03.320><c> characterized</c><00:02:04.039><c> by</c><00:02:04.200><c> a</c>

00:02:04.350 --> 00:02:04.360 align:start position:0%
order nomical system characterized by a
 

00:02:04.360 --> 00:02:07.389 align:start position:0%
order nomical system characterized by a
time<00:02:04.759><c> constant</c><00:02:05.759><c> and</c><00:02:05.920><c> this</c><00:02:06.159><c> time</c><00:02:06.479><c> constant</c>

00:02:07.389 --> 00:02:07.399 align:start position:0%
time constant and this time constant
 

00:02:07.399 --> 00:02:09.550 align:start position:0%
time constant and this time constant
creates<00:02:07.799><c> some</c><00:02:08.039><c> inertia</c><00:02:08.560><c> in</c><00:02:08.679><c> the</c><00:02:08.840><c> model</c>

00:02:09.550 --> 00:02:09.560 align:start position:0%
creates some inertia in the model
 

00:02:09.560 --> 00:02:12.030 align:start position:0%
creates some inertia in the model
meaning<00:02:09.879><c> that</c><00:02:10.000><c> a</c><00:02:10.160><c> sudden</c><00:02:10.440><c> change</c><00:02:10.879><c> the</c><00:02:11.040><c> dni</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
meaning that a sudden change the dni
 

00:02:12.040 --> 00:02:14.830 align:start position:0%
meaning that a sudden change the dni
doesn't<00:02:12.400><c> cause</c><00:02:12.720><c> an</c><00:02:12.959><c> immediate</c><00:02:13.599><c> response</c><00:02:14.599><c> on</c>

00:02:14.830 --> 00:02:14.840 align:start position:0%
doesn't cause an immediate response on
 

00:02:14.840 --> 00:02:17.190 align:start position:0%
doesn't cause an immediate response on
the<00:02:15.160><c> electrical</c><00:02:15.720><c> power</c><00:02:16.519><c> and</c><00:02:16.680><c> of</c><00:02:16.840><c> course</c><00:02:17.080><c> the</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
the electrical power and of course the
 

00:02:17.200 --> 00:02:20.350 align:start position:0%
the electrical power and of course the
whole<00:02:17.440><c> thermal</c><00:02:17.840><c> energy</c><00:02:18.200><c> storage</c><00:02:19.120><c> can</c><00:02:19.400><c> create</c>

00:02:20.350 --> 00:02:20.360 align:start position:0%
whole thermal energy storage can create
 

00:02:20.360 --> 00:02:22.309 align:start position:0%
whole thermal energy storage can create
additional<00:02:20.920><c> inertia</c><00:02:21.519><c> meaning</c><00:02:21.840><c> that</c><00:02:22.080><c> when</c><00:02:22.200><c> the</c>

00:02:22.309 --> 00:02:22.319 align:start position:0%
additional inertia meaning that when the
 

00:02:22.319 --> 00:02:25.949 align:start position:0%
additional inertia meaning that when the
dni<00:02:23.080><c> changes</c><00:02:24.080><c> there</c><00:02:24.239><c> is</c><00:02:24.400><c> a</c><00:02:24.840><c> delay</c><00:02:25.519><c> until</c><00:02:25.800><c> we</c>

00:02:25.949 --> 00:02:25.959 align:start position:0%
dni changes there is a delay until we
 

00:02:25.959 --> 00:02:28.949 align:start position:0%
dni changes there is a delay until we
see<00:02:26.440><c> an</c><00:02:26.720><c> impact</c><00:02:27.360><c> in</c><00:02:27.480><c> the</c><00:02:27.640><c> electrical</c><00:02:28.160><c> power</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
see an impact in the electrical power
 

00:02:28.959 --> 00:02:30.949 align:start position:0%
see an impact in the electrical power
and<00:02:29.120><c> this</c><00:02:29.239><c> is</c><00:02:29.360><c> something</c><00:02:29.680><c> that</c><00:02:30.000><c> that</c><00:02:30.599><c> we</c><00:02:30.760><c> can</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
and this is something that that we can
 

00:02:30.959 --> 00:02:33.350 align:start position:0%
and this is something that that we can
also<00:02:31.519><c> calibrate</c><00:02:32.120><c> based</c><00:02:32.360><c> on</c><00:02:32.519><c> measure</c><00:02:32.879><c> data</c><00:02:33.160><c> so</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
also calibrate based on measure data so
 

00:02:33.360 --> 00:02:35.790 align:start position:0%
also calibrate based on measure data so
we<00:02:33.519><c> get</c><00:02:33.800><c> both</c><00:02:34.080><c> the</c><00:02:34.280><c> efficiencies</c><00:02:34.879><c> correct</c><00:02:35.640><c> and</c>

00:02:35.790 --> 00:02:35.800 align:start position:0%
we get both the efficiencies correct and
 

00:02:35.800 --> 00:02:38.630 align:start position:0%
we get both the efficiencies correct and
this<00:02:36.200><c> inertia</c><00:02:37.200><c> and</c><00:02:37.360><c> so</c><00:02:37.599><c> of</c><00:02:37.720><c> sless</c><00:02:38.319><c> in</c><00:02:38.440><c> the</c>

00:02:38.630 --> 00:02:38.640 align:start position:0%
this inertia and so of sless in the
 

00:02:38.640 --> 00:02:43.670 align:start position:0%
this inertia and so of sless in the
system<00:02:39.040><c> to</c><00:02:39.200><c> be</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
 
 

00:02:43.680 --> 00:02:46.470 align:start position:0%
 
correct<00:02:44.680><c> we</c><00:02:44.840><c> have</c><00:02:45.040><c> used</c><00:02:45.440><c> measure</c><00:02:45.920><c> data</c><00:02:46.280><c> from</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
correct we have used measure data from
 

00:02:46.480 --> 00:02:50.509 align:start position:0%
correct we have used measure data from
Spain<00:02:47.440><c> uh</c><00:02:47.560><c> to</c><00:02:47.760><c> calibrate</c><00:02:48.239><c> the</c><00:02:48.400><c> model</c><00:02:49.360><c> first</c><00:02:50.360><c> we</c>

00:02:50.509 --> 00:02:50.519 align:start position:0%
Spain uh to calibrate the model first we
 

00:02:50.519 --> 00:02:52.149 align:start position:0%
Spain uh to calibrate the model first we
took<00:02:50.879><c> data</c><00:02:51.120><c> from</c><00:02:51.280><c> the</c><00:02:51.440><c> plants</c><00:02:51.840><c> without</c>

00:02:52.149 --> 00:02:52.159 align:start position:0%
took data from the plants without
 

00:02:52.159 --> 00:02:54.830 align:start position:0%
took data from the plants without
storage<00:02:52.800><c> to</c><00:02:53.000><c> calibrate</c><00:02:53.480><c> the</c><00:02:53.599><c> other</c><00:02:54.040><c> parts</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
storage to calibrate the other parts
 

00:02:54.840 --> 00:02:58.949 align:start position:0%
storage to calibrate the other parts
than<00:02:55.120><c> the</c><00:02:55.319><c> storage</c><00:02:55.959><c> part</c><00:02:56.959><c> and</c><00:02:57.200><c> then</c><00:02:57.959><c> we</c><00:02:58.159><c> took</c>

00:02:58.949 --> 00:02:58.959 align:start position:0%
than the storage part and then we took
 

00:02:58.959 --> 00:03:01.750 align:start position:0%
than the storage part and then we took
data<00:02:59.319><c> from</c><00:02:59.480><c> the</c><00:02:59.640><c> part</c><00:02:59.959><c> plant</c><00:03:00.239><c> with</c><00:03:00.519><c> storage</c><00:03:01.280><c> to</c>

00:03:01.750 --> 00:03:01.760 align:start position:0%
data from the part plant with storage to
 

00:03:01.760 --> 00:03:04.710 align:start position:0%
data from the part plant with storage to
calibrate<00:03:02.280><c> also</c><00:03:02.599><c> the</c><00:03:02.760><c> thermal</c><00:03:03.120><c> and</c><00:03:03.560><c> storage</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
calibrate also the thermal and storage
 

00:03:04.720 --> 00:03:07.309 align:start position:0%
calibrate also the thermal and storage
part<00:03:05.720><c> and</c><00:03:06.200><c> just</c><00:03:06.360><c> to</c><00:03:06.560><c> remind</c><00:03:06.879><c> that</c><00:03:07.080><c> this</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
part and just to remind that this
 

00:03:07.319 --> 00:03:10.070 align:start position:0%
part and just to remind that this
calibration<00:03:08.080><c> as</c><00:03:08.280><c> all</c><00:03:08.400><c> of</c><00:03:08.560><c> the</c><00:03:08.680><c> CSP</c><00:03:09.159><c> model</c><00:03:09.840><c> uh</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
calibration as all of the CSP model uh
 

00:03:10.080 --> 00:03:12.110 align:start position:0%
calibration as all of the CSP model uh
development<00:03:10.640><c> were</c><00:03:10.879><c> already</c><00:03:11.239><c> done</c><00:03:11.440><c> in</c><00:03:11.599><c> PCD</c>

00:03:12.110 --> 00:03:12.120 align:start position:0%
development were already done in PCD
 

00:03:12.120 --> 00:03:14.589 align:start position:0%
development were already done in PCD
version<00:03:12.400><c> three</c><00:03:13.080><c> so</c><00:03:13.280><c> there</c><00:03:13.360><c> is</c><00:03:13.519><c> no</c><00:03:13.799><c> change</c><00:03:14.400><c> in</c>

00:03:14.589 --> 00:03:14.599 align:start position:0%
version three so there is no change in
 

00:03:14.599 --> 00:03:16.910 align:start position:0%
version three so there is no change in
PCD

00:03:16.910 --> 00:03:16.920 align:start position:0%
PCD
 

00:03:16.920 --> 00:03:19.869 align:start position:0%
PCD
4<00:03:17.920><c> the</c><00:03:18.120><c> fit</c><00:03:18.640><c> to</c><00:03:18.840><c> the</c><00:03:18.959><c> measure</c><00:03:19.400><c> data</c><00:03:19.640><c> is</c>

00:03:19.869 --> 00:03:19.879 align:start position:0%
4 the fit to the measure data is
 

00:03:19.879 --> 00:03:22.949 align:start position:0%
4 the fit to the measure data is
considered<00:03:20.760><c> adequate</c><00:03:21.760><c> we</c><00:03:21.920><c> get</c><00:03:22.080><c> the</c><00:03:22.280><c> histogram</c>

00:03:22.949 --> 00:03:22.959 align:start position:0%
considered adequate we get the histogram
 

00:03:22.959 --> 00:03:25.229 align:start position:0%
considered adequate we get the histogram
on<00:03:23.120><c> the</c><00:03:23.360><c> right</c><00:03:24.239><c> quite</c><00:03:24.480><c> similar</c><00:03:24.920><c> if</c><00:03:25.080><c> we</c>

00:03:25.229 --> 00:03:25.239 align:start position:0%
on the right quite similar if we
 

00:03:25.239 --> 00:03:27.710 align:start position:0%
on the right quite similar if we
measured<00:03:25.560><c> and</c><00:03:25.680><c> simulated</c><00:03:26.239><c> data</c><00:03:27.239><c> capacity</c>

00:03:27.710 --> 00:03:27.720 align:start position:0%
measured and simulated data capacity
 

00:03:27.720 --> 00:03:29.509 align:start position:0%
measured and simulated data capacity
Factor<00:03:28.159><c> standard</c><00:03:28.560><c> deviation</c><00:03:29.000><c> mean</c><00:03:29.159><c> and</c><00:03:29.319><c> Max</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
Factor standard deviation mean and Max
 

00:03:29.519 --> 00:03:32.990 align:start position:0%
Factor standard deviation mean and Max
produ<00:03:29.760><c> ction</c><00:03:30.760><c> quite</c><00:03:31.280><c> well</c><00:03:31.519><c> aligned</c><00:03:32.519><c> we</c><00:03:32.680><c> get</c><00:03:32.840><c> a</c>

00:03:32.990 --> 00:03:33.000 align:start position:0%
produ ction quite well aligned we get a
 

00:03:33.000 --> 00:03:35.229 align:start position:0%
produ ction quite well aligned we get a
production<00:03:33.879><c> on</c><00:03:34.120><c> those</c><00:03:34.360><c> months</c><00:03:34.920><c> where</c><00:03:35.120><c> we</c>

00:03:35.229 --> 00:03:35.239 align:start position:0%
production on those months where we
 

00:03:35.239 --> 00:03:37.350 align:start position:0%
production on those months where we
would<00:03:35.480><c> expect</c><00:03:35.760><c> to</c><00:03:35.920><c> see</c><00:03:36.200><c> that</c><00:03:36.879><c> but</c><00:03:37.040><c> of</c><00:03:37.159><c> course</c>

00:03:37.350 --> 00:03:37.360 align:start position:0%
would expect to see that but of course
 

00:03:37.360 --> 00:03:40.110 align:start position:0%
would expect to see that but of course
the<00:03:37.519><c> model</c><00:03:38.080><c> is</c><00:03:38.519><c> somewhat</c><00:03:38.959><c> rough</c><00:03:39.680><c> it</c><00:03:39.799><c> doesn't</c>

00:03:40.110 --> 00:03:40.120 align:start position:0%
the model is somewhat rough it doesn't
 

00:03:40.120 --> 00:03:42.429 align:start position:0%
the model is somewhat rough it doesn't
model<00:03:40.519><c> any</c><00:03:40.760><c> detail</c><00:03:41.319><c> characteristics</c><00:03:42.120><c> of</c>

00:03:42.429 --> 00:03:42.439 align:start position:0%
model any detail characteristics of
 

00:03:42.439 --> 00:03:44.750 align:start position:0%
model any detail characteristics of
individual<00:03:43.159><c> CSV</c><00:03:43.680><c> plants</c><00:03:44.080><c> so</c><00:03:44.239><c> we</c><00:03:44.360><c> do</c><00:03:44.519><c> not</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
individual CSV plants so we do not
 

00:03:44.760 --> 00:03:49.949 align:start position:0%
individual CSV plants so we do not
capture<00:03:45.239><c> the</c><00:03:46.040><c> Generation</c><00:03:46.680><c> profile</c>

00:03:49.949 --> 00:03:49.959 align:start position:0%
 
 

00:03:49.959 --> 00:03:53.069 align:start position:0%
 
property<00:03:50.959><c> then</c><00:03:51.280><c> when</c><00:03:51.480><c> we</c><00:03:51.680><c> have</c><00:03:51.879><c> the</c><00:03:52.079><c> model</c>

00:03:53.069 --> 00:03:53.079 align:start position:0%
property then when we have the model
 

00:03:53.079 --> 00:03:55.550 align:start position:0%
property then when we have the model
calibrated<00:03:54.079><c> we</c><00:03:54.239><c> have</c><00:03:54.360><c> a</c><00:03:54.519><c> few</c><00:03:55.239><c> different</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
calibrated we have a few different
 

00:03:55.560 --> 00:03:59.830 align:start position:0%
calibrated we have a few different
simulation<00:03:56.200><c> cases</c><00:03:56.799><c> we</c><00:03:56.959><c> do</c><00:03:57.200><c> for</c><00:03:57.920><c> PCD</c><00:03:58.920><c> one</c>

00:03:59.830 --> 00:03:59.840 align:start position:0%
simulation cases we do for PCD one
 

00:03:59.840 --> 00:04:00.990 align:start position:0%
simulation cases we do for PCD one
without<00:04:00.159><c> any</c>

00:04:00.990 --> 00:04:01.000 align:start position:0%
without any
 

00:04:01.000 --> 00:04:04.550 align:start position:0%
without any
storage<00:04:02.000><c> and</c><00:04:02.159><c> then</c><00:04:02.360><c> one</c><00:04:02.560><c> with</c><00:04:02.760><c> storage</c><00:04:03.680><c> 7.5</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
storage and then one with storage 7.5
 

00:04:04.560 --> 00:04:06.190 align:start position:0%
storage and then one with storage 7.5
hours<00:04:05.040><c> of</c><00:04:05.319><c> energy</c>

00:04:06.190 --> 00:04:06.200 align:start position:0%
hours of energy
 

00:04:06.200 --> 00:04:08.670 align:start position:0%
hours of energy
storage<00:04:07.200><c> that</c><00:04:07.400><c> of</c><00:04:07.519><c> course</c><00:04:07.840><c> implies</c><00:04:08.280><c> different</c>

00:04:08.670 --> 00:04:08.680 align:start position:0%
storage that of course implies different
 

00:04:08.680 --> 00:04:11.110 align:start position:0%
storage that of course implies different
solar<00:04:09.040><c> multiple</c><00:04:09.760><c> also</c><00:04:10.040><c> imply</c><00:04:10.680><c> implying</c>

00:04:11.110 --> 00:04:11.120 align:start position:0%
solar multiple also imply implying
 

00:04:11.120 --> 00:04:13.350 align:start position:0%
solar multiple also imply implying
different<00:04:11.480><c> capacity</c><00:04:11.959><c> factors</c><00:04:12.959><c> with</c><00:04:13.120><c> when</c><00:04:13.239><c> we</c>

00:04:13.350 --> 00:04:13.360 align:start position:0%
different capacity factors with when we
 

00:04:13.360 --> 00:04:15.589 align:start position:0%
different capacity factors with when we
have<00:04:13.640><c> storage</c><00:04:14.480><c> then</c><00:04:14.680><c> we</c><00:04:14.799><c> have</c><00:04:15.000><c> significantly</c>

00:04:15.589 --> 00:04:15.599 align:start position:0%
have storage then we have significantly
 

00:04:15.599 --> 00:04:18.430 align:start position:0%
have storage then we have significantly
higher<00:04:15.879><c> capacity</c><00:04:16.639><c> Factor</c><00:04:17.639><c> it</c><00:04:17.759><c> is</c><00:04:18.000><c> important</c>

00:04:18.430 --> 00:04:18.440 align:start position:0%
higher capacity Factor it is important
 

00:04:18.440 --> 00:04:21.069 align:start position:0%
higher capacity Factor it is important
to<00:04:18.639><c> know</c><00:04:19.160><c> that</c><00:04:19.320><c> the</c><00:04:19.479><c> storage</c><00:04:19.919><c> cases</c><00:04:20.479><c> use</c><00:04:20.840><c> a</c>

00:04:21.069 --> 00:04:21.079 align:start position:0%
to know that the storage cases use a
 

00:04:21.079 --> 00:04:23.990 align:start position:0%
to know that the storage cases use a
dispatch<00:04:21.639><c> as</c><00:04:21.759><c> soon</c><00:04:22.000><c> as</c><00:04:22.280><c> possible</c><00:04:23.000><c> strategy</c>

00:04:23.990 --> 00:04:24.000 align:start position:0%
dispatch as soon as possible strategy
 

00:04:24.000 --> 00:04:25.749 align:start position:0%
dispatch as soon as possible strategy
that<00:04:24.160><c> can</c><00:04:24.320><c> be</c><00:04:24.440><c> seen</c><00:04:24.639><c> in</c><00:04:24.759><c> the</c><00:04:24.880><c> figure</c><00:04:25.160><c> in</c><00:04:25.280><c> the</c>

00:04:25.749 --> 00:04:25.759 align:start position:0%
that can be seen in the figure in the
 

00:04:25.759 --> 00:04:29.790 align:start position:0%
that can be seen in the figure in the
below<00:04:26.759><c> the</c><00:04:26.919><c> green</c><00:04:27.240><c> dash</c><00:04:27.639><c> line</c><00:04:28.600><c> shows</c><00:04:29.120><c> the</c><00:04:29.479><c> uh</c>

00:04:29.790 --> 00:04:29.800 align:start position:0%
below the green dash line shows the uh
 

00:04:29.800 --> 00:04:32.189 align:start position:0%
below the green dash line shows the uh
power<00:04:30.080><c> from</c><00:04:30.199><c> the</c><00:04:30.320><c> solar</c><00:04:30.639><c> field</c><00:04:31.639><c> which</c><00:04:31.800><c> can</c><00:04:31.960><c> be</c>

00:04:32.189 --> 00:04:32.199 align:start position:0%
power from the solar field which can be
 

00:04:32.199 --> 00:04:33.749 align:start position:0%
power from the solar field which can be
much<00:04:32.440><c> higher</c><00:04:32.720><c> than</c><00:04:32.880><c> the</c><00:04:33.000><c> installed</c><00:04:33.400><c> collap</c>

00:04:33.749 --> 00:04:33.759 align:start position:0%
much higher than the installed collap
 

00:04:33.759 --> 00:04:35.749 align:start position:0%
much higher than the installed collap
capacity<00:04:34.199><c> so</c><00:04:34.320><c> it</c><00:04:34.479><c> cannot</c><00:04:34.960><c> be</c><00:04:35.199><c> dispatched</c>

00:04:35.749 --> 00:04:35.759 align:start position:0%
capacity so it cannot be dispatched
 

00:04:35.759 --> 00:04:38.830 align:start position:0%
capacity so it cannot be dispatched
directly<00:04:36.560><c> so</c><00:04:36.759><c> it</c><00:04:36.880><c> goes</c><00:04:37.080><c> into</c><00:04:37.320><c> the</c><00:04:37.720><c> storage</c><00:04:38.720><c> but</c>

00:04:38.830 --> 00:04:38.840 align:start position:0%
directly so it goes into the storage but
 

00:04:38.840 --> 00:04:42.350 align:start position:0%
directly so it goes into the storage but
then<00:04:39.000><c> the</c><00:04:39.160><c> Orange</c><00:04:39.520><c> Line</c><00:04:39.880><c> shows</c><00:04:40.600><c> that</c><00:04:41.600><c> after</c><00:04:42.160><c> we</c>

00:04:42.350 --> 00:04:42.360 align:start position:0%
then the Orange Line shows that after we
 

00:04:42.360 --> 00:04:45.189 align:start position:0%
then the Orange Line shows that after we
can<00:04:42.720><c> dispatch</c><00:04:43.720><c> we</c><00:04:43.960><c> dispatch</c><00:04:44.560><c> as</c><00:04:44.680><c> soon</c><00:04:44.919><c> as</c>

00:04:45.189 --> 00:04:45.199 align:start position:0%
can dispatch we dispatch as soon as
 

00:04:45.199 --> 00:04:49.029 align:start position:0%
can dispatch we dispatch as soon as
possible<00:04:45.680><c> so</c><00:04:45.840><c> there's</c><00:04:46.160><c> no</c><00:04:46.360><c> no</c><00:04:46.720><c> Delay</c><00:04:48.039><c> from</c>

00:04:49.029 --> 00:04:49.039 align:start position:0%
possible so there's no no Delay from
 

00:04:49.039 --> 00:04:50.670 align:start position:0%
possible so there's no no Delay from
when<00:04:49.199><c> we</c><00:04:49.360><c> have</c><00:04:49.520><c> it</c><00:04:49.639><c> in</c><00:04:49.759><c> the</c><00:04:49.880><c> storage</c><00:04:50.360><c> and</c><00:04:50.520><c> when</c>

00:04:50.670 --> 00:04:50.680 align:start position:0%
when we have it in the storage and when
 

00:04:50.680 --> 00:04:53.150 align:start position:0%
when we have it in the storage and when
we<00:04:50.840><c> dispatch</c><00:04:51.320><c> it</c><00:04:52.320><c> this</c><00:04:52.440><c> is</c><00:04:52.560><c> a</c><00:04:52.720><c> dispatch</c>

00:04:53.150 --> 00:04:53.160 align:start position:0%
we dispatch it this is a dispatch
 

00:04:53.160 --> 00:04:55.710 align:start position:0%
we dispatch it this is a dispatch
strategy<00:04:54.000><c> that</c><00:04:54.160><c> is</c><00:04:54.320><c> simple</c><00:04:54.840><c> but</c><00:04:55.039><c> also</c><00:04:55.320><c> it</c><00:04:55.479><c> does</c>

00:04:55.710 --> 00:04:55.720 align:start position:0%
strategy that is simple but also it does
 

00:04:55.720 --> 00:04:58.469 align:start position:0%
strategy that is simple but also it does
not<00:04:55.960><c> necessarily</c><00:04:56.840><c> reflect</c><00:04:57.520><c> in</c><00:04:57.759><c> reality</c><00:04:58.199><c> how</c>

00:04:58.469 --> 00:04:58.479 align:start position:0%
not necessarily reflect in reality how
 

00:04:58.479 --> 00:05:02.230 align:start position:0%
not necessarily reflect in reality how
CSP<00:04:59.400><c> is</c><00:04:59.880><c> dispatch</c><00:05:00.880><c> where</c><00:05:01.680><c> maybe</c><00:05:01.919><c> du</c><00:05:02.120><c> to</c>

00:05:02.230 --> 00:05:02.240 align:start position:0%
CSP is dispatch where maybe du to
 

00:05:02.240 --> 00:05:04.469 align:start position:0%
CSP is dispatch where maybe du to
electricity<00:05:02.759><c> prices</c><00:05:03.400><c> or</c><00:05:03.600><c> or</c><00:05:03.759><c> something</c><00:05:04.120><c> else</c>

00:05:04.469 --> 00:05:04.479 align:start position:0%
electricity prices or or something else
 

00:05:04.479 --> 00:05:07.110 align:start position:0%
electricity prices or or something else
we<00:05:04.600><c> might</c><00:05:04.800><c> see</c><00:05:04.960><c> a</c><00:05:05.440><c> delay</c><00:05:06.440><c> and</c><00:05:06.560><c> the</c><00:05:06.680><c> dispatch</c>

00:05:07.110 --> 00:05:07.120 align:start position:0%
we might see a delay and the dispatch
 

00:05:07.120 --> 00:05:09.310 align:start position:0%
we might see a delay and the dispatch
only<00:05:07.360><c> happens</c><00:05:07.759><c> maybe</c><00:05:08.440><c> during</c><00:05:08.759><c> the</c><00:05:08.919><c> night</c>

00:05:09.310 --> 00:05:09.320 align:start position:0%
only happens maybe during the night
 

00:05:09.320 --> 00:05:10.909 align:start position:0%
only happens maybe during the night
rather<00:05:09.600><c> than</c><00:05:09.800><c> directly</c><00:05:10.160><c> in</c><00:05:10.280><c> the</c>

00:05:10.909 --> 00:05:10.919 align:start position:0%
rather than directly in the
 

00:05:10.919 --> 00:05:13.670 align:start position:0%
rather than directly in the
evening<00:05:11.919><c> we</c><00:05:12.039><c> do</c><00:05:12.199><c> not</c><00:05:12.440><c> model</c><00:05:13.120><c> any</c><00:05:13.320><c> more</c>

00:05:13.670 --> 00:05:13.680 align:start position:0%
evening we do not model any more
 

00:05:13.680 --> 00:05:15.469 align:start position:0%
evening we do not model any more
complicated<00:05:14.199><c> disect</c><00:05:14.639><c> strategies</c><00:05:15.199><c> but</c><00:05:15.320><c> the</c>

00:05:15.469 --> 00:05:15.479 align:start position:0%
complicated disect strategies but the
 

00:05:15.479 --> 00:05:18.029 align:start position:0%
complicated disect strategies but the
pre<00:05:15.840><c> dispatch</c><00:05:16.440><c> meaning</c><00:05:16.720><c> the</c><00:05:16.919><c> dash</c><00:05:17.240><c> Green</c><00:05:17.560><c> Line</c>

00:05:18.029 --> 00:05:18.039 align:start position:0%
pre dispatch meaning the dash Green Line
 

00:05:18.039 --> 00:05:20.749 align:start position:0%
pre dispatch meaning the dash Green Line
in<00:05:18.280><c> the</c><00:05:18.600><c> the</c><00:05:18.759><c> bottom</c><00:05:19.199><c> bottom</c><00:05:19.520><c> picture</c><00:05:20.479><c> that</c>

00:05:20.749 --> 00:05:20.759 align:start position:0%
in the the bottom bottom picture that
 

00:05:20.759 --> 00:05:23.790 align:start position:0%
in the the bottom bottom picture that
data<00:05:21.039><c> is</c><00:05:21.280><c> also</c><00:05:21.680><c> saved</c><00:05:22.680><c> so</c><00:05:22.919><c> it's</c><00:05:23.199><c> possible</c><00:05:23.600><c> in</c>

00:05:23.790 --> 00:05:23.800 align:start position:0%
data is also saved so it's possible in
 

00:05:23.800 --> 00:05:26.510 align:start position:0%
data is also saved so it's possible in
post<00:05:24.120><c> processing</c><00:05:24.880><c> to</c><00:05:25.080><c> model</c><00:05:25.600><c> more</c><00:05:26.000><c> complex</c>

00:05:26.510 --> 00:05:26.520 align:start position:0%
post processing to model more complex
 

00:05:26.520 --> 00:05:28.390 align:start position:0%
post processing to model more complex
dispatch

00:05:28.390 --> 00:05:28.400 align:start position:0%
dispatch
 

00:05:28.400 --> 00:05:30.830 align:start position:0%
dispatch
strategies<00:05:29.400><c> that's</c><00:05:29.800><c> was</c><00:05:29.919><c> the</c><00:05:30.039><c> overview</c><00:05:30.600><c> on</c>

00:05:30.830 --> 00:05:30.840 align:start position:0%
strategies that's was the overview on
 

00:05:30.840 --> 00:05:35.680 align:start position:0%
strategies that's was the overview on
CSP<00:05:31.440><c> modeling</c><00:05:32.160><c> thank</c><00:05:32.280><c> you</c><00:05:32.479><c> very</c><00:05:32.680><c> much</c>

