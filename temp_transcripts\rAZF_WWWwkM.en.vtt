WEBVTT
Kind: captions
Language: en

00:00:01.199 --> 00:00:03.270 align:start position:0%
 
hey<00:00:01.360><c> there</c><00:00:01.920><c> i'm</c><00:00:02.080><c> matt</c><00:00:02.320><c> williams</c><00:00:02.800><c> the</c><00:00:02.960><c> techno</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
hey there i'm matt williams the techno
 

00:00:03.280 --> 00:00:05.829 align:start position:0%
hey there i'm matt williams the techno
evangelist<00:00:04.240><c> and</c><00:00:04.720><c> in</c><00:00:04.880><c> this</c><00:00:05.120><c> video</c><00:00:05.520><c> i</c><00:00:05.600><c> want</c><00:00:05.759><c> to</c>

00:00:05.829 --> 00:00:05.839 align:start position:0%
evangelist and in this video i want to
 

00:00:05.839 --> 00:00:07.110 align:start position:0%
evangelist and in this video i want to
show<00:00:06.000><c> you</c><00:00:06.319><c> something</c><00:00:06.720><c> that</c><00:00:06.879><c> i've</c><00:00:07.040><c> been</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
show you something that i've been
 

00:00:07.120 --> 00:00:09.589 align:start position:0%
show you something that i've been
working<00:00:07.440><c> on</c><00:00:07.759><c> over</c><00:00:08.080><c> the</c><00:00:08.160><c> last</c><00:00:08.400><c> few</c><00:00:08.639><c> days</c><00:00:09.440><c> you</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
working on over the last few days you
 

00:00:09.599 --> 00:00:12.390 align:start position:0%
working on over the last few days you
see<00:00:10.320><c> i</c><00:00:10.400><c> work</c><00:00:10.719><c> as</c><00:00:10.960><c> an</c><00:00:11.200><c> evangelist</c><00:00:11.840><c> at</c><00:00:12.000><c> a</c><00:00:12.080><c> company</c>

00:00:12.390 --> 00:00:12.400 align:start position:0%
see i work as an evangelist at a company
 

00:00:12.400 --> 00:00:13.990 align:start position:0%
see i work as an evangelist at a company
called<00:00:12.799><c> infra</c>

00:00:13.990 --> 00:00:14.000 align:start position:0%
called infra
 

00:00:14.000 --> 00:00:14.950 align:start position:0%
called infra
and

00:00:14.950 --> 00:00:14.960 align:start position:0%
and
 

00:00:14.960 --> 00:00:17.670 align:start position:0%
and
one<00:00:15.120><c> of</c><00:00:15.200><c> my</c><00:00:15.920><c> roles</c><00:00:16.720><c> is</c><00:00:16.960><c> to</c><00:00:17.039><c> create</c><00:00:17.359><c> videos</c>

00:00:17.670 --> 00:00:17.680 align:start position:0%
one of my roles is to create videos
 

00:00:17.680 --> 00:00:20.390 align:start position:0%
one of my roles is to create videos
about<00:00:18.480><c> who</c><00:00:18.640><c> we</c><00:00:18.880><c> are</c><00:00:19.199><c> about</c><00:00:19.439><c> what</c><00:00:19.680><c> we</c><00:00:19.840><c> do</c><00:00:20.160><c> about</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
about who we are about what we do about
 

00:00:20.400 --> 00:00:22.550 align:start position:0%
about who we are about what we do about
the<00:00:20.640><c> technologies</c><00:00:21.199><c> that</c><00:00:21.359><c> are</c><00:00:21.439><c> used</c><00:00:21.840><c> about</c><00:00:22.400><c> you</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
the technologies that are used about you
 

00:00:22.560 --> 00:00:24.230 align:start position:0%
the technologies that are used about you
know<00:00:22.720><c> all</c><00:00:22.960><c> those</c><00:00:23.279><c> things</c><00:00:23.519><c> that</c><00:00:23.680><c> are</c><00:00:23.760><c> relevant</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
know all those things that are relevant
 

00:00:24.240 --> 00:00:26.710 align:start position:0%
know all those things that are relevant
to<00:00:24.480><c> infra</c><00:00:24.960><c> by</c><00:00:25.119><c> the</c><00:00:25.199><c> way</c><00:00:25.439><c> infra</c><00:00:26.000><c> is</c>

00:00:26.710 --> 00:00:26.720 align:start position:0%
to infra by the way infra is
 

00:00:26.720 --> 00:00:28.550 align:start position:0%
to infra by the way infra is
single<00:00:27.119><c> sign-on</c><00:00:27.760><c> for</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
single sign-on for
 

00:00:28.560 --> 00:00:31.189 align:start position:0%
single sign-on for
kubernetes<00:00:29.279><c> today</c><00:00:30.240><c> and</c>

00:00:31.189 --> 00:00:31.199 align:start position:0%
kubernetes today and
 

00:00:31.199 --> 00:00:33.830 align:start position:0%
kubernetes today and
postgres<00:00:31.840><c> and</c><00:00:32.000><c> ssh</c><00:00:32.559><c> and</c><00:00:32.960><c> other</c><00:00:33.200><c> things</c><00:00:33.600><c> in</c><00:00:33.680><c> the</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
postgres and ssh and other things in the
 

00:00:33.840 --> 00:00:34.790 align:start position:0%
postgres and ssh and other things in the
future

00:00:34.790 --> 00:00:34.800 align:start position:0%
future
 

00:00:34.800 --> 00:00:35.670 align:start position:0%
future
so

00:00:35.670 --> 00:00:35.680 align:start position:0%
so
 

00:00:35.680 --> 00:00:38.229 align:start position:0%
so
i<00:00:35.840><c> created</c><00:00:36.320><c> a</c><00:00:36.640><c> series</c><00:00:36.960><c> of</c><00:00:37.040><c> videos</c><00:00:37.840><c> and</c><00:00:38.160><c> i'm</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
i created a series of videos and i'm
 

00:00:38.239 --> 00:00:39.990 align:start position:0%
i created a series of videos and i'm
going<00:00:38.320><c> to</c><00:00:38.480><c> continue</c><00:00:38.960><c> with</c><00:00:39.120><c> that</c><00:00:39.360><c> series</c><00:00:39.680><c> going</c>

00:00:39.990 --> 00:00:40.000 align:start position:0%
going to continue with that series going
 

00:00:40.000 --> 00:00:40.950 align:start position:0%
going to continue with that series going
forward

00:00:40.950 --> 00:00:40.960 align:start position:0%
forward
 

00:00:40.960 --> 00:00:43.430 align:start position:0%
forward
and<00:00:41.680><c> you</c><00:00:41.840><c> know</c><00:00:41.920><c> it's</c><00:00:42.160><c> a</c><00:00:42.399><c> mostly</c><00:00:42.960><c> a</c><00:00:43.040><c> command</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
and you know it's a mostly a command
 

00:00:43.440 --> 00:00:45.029 align:start position:0%
and you know it's a mostly a command
line<00:00:43.680><c> product</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
line product
 

00:00:45.039 --> 00:00:47.029 align:start position:0%
line product
so<00:00:45.360><c> i'm</c><00:00:45.760><c> spending</c><00:00:46.079><c> a</c><00:00:46.160><c> lot</c><00:00:46.320><c> of</c><00:00:46.480><c> time</c><00:00:46.719><c> in</c><00:00:46.800><c> these</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
so i'm spending a lot of time in these
 

00:00:47.039 --> 00:00:49.270 align:start position:0%
so i'm spending a lot of time in these
videos<00:00:47.520><c> in</c><00:00:47.680><c> the</c><00:00:47.760><c> command</c><00:00:48.160><c> line</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
videos in the command line
 

00:00:49.280 --> 00:00:52.869 align:start position:0%
videos in the command line
and<00:00:50.000><c> when</c><00:00:50.320><c> doing</c><00:00:50.640><c> that</c><00:00:51.360><c> you</c><00:00:51.440><c> know</c><00:00:51.600><c> i've</c><00:00:51.760><c> got</c><00:00:52.320><c> a</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
and when doing that you know i've got a
 

00:00:52.879 --> 00:00:55.110 align:start position:0%
and when doing that you know i've got a
script<00:00:53.280><c> that</c><00:00:53.440><c> i</c><00:00:53.600><c> read</c>

00:00:55.110 --> 00:00:55.120 align:start position:0%
script that i read
 

00:00:55.120 --> 00:00:56.150 align:start position:0%
script that i read
and

00:00:56.150 --> 00:00:56.160 align:start position:0%
and
 

00:00:56.160 --> 00:00:58.549 align:start position:0%
and
and<00:00:56.239><c> then</c><00:00:56.480><c> i'm</c><00:00:56.879><c> recording</c><00:00:57.920><c> what</c><00:00:58.160><c> i</c><00:00:58.239><c> actually</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
and then i'm recording what i actually
 

00:00:58.559 --> 00:01:00.549 align:start position:0%
and then i'm recording what i actually
do<00:00:58.800><c> in</c><00:00:58.960><c> the</c><00:00:59.199><c> terminal</c><00:00:59.920><c> as</c><00:01:00.079><c> a</c><00:01:00.160><c> separate</c>

00:01:00.549 --> 00:01:00.559 align:start position:0%
do in the terminal as a separate
 

00:01:00.559 --> 00:01:02.069 align:start position:0%
do in the terminal as a separate
recording

00:01:02.069 --> 00:01:02.079 align:start position:0%
recording
 

00:01:02.079 --> 00:01:04.149 align:start position:0%
recording
and<00:01:02.239><c> when</c><00:01:02.399><c> i</c><00:01:02.480><c> record</c><00:01:03.039><c> that</c><00:01:03.280><c> demo</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
and when i record that demo
 

00:01:04.159 --> 00:01:05.590 align:start position:0%
and when i record that demo
i'm<00:01:04.400><c> basically</c><00:01:04.720><c> looking</c><00:01:05.040><c> at</c><00:01:05.119><c> my</c><00:01:05.280><c> script</c>

00:01:05.590 --> 00:01:05.600 align:start position:0%
i'm basically looking at my script
 

00:01:05.600 --> 00:01:07.190 align:start position:0%
i'm basically looking at my script
trying<00:01:05.760><c> to</c><00:01:05.840><c> figure</c><00:01:06.080><c> out</c><00:01:06.240><c> okay</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
trying to figure out okay
 

00:01:07.200 --> 00:01:09.270 align:start position:0%
trying to figure out okay
we're<00:01:07.360><c> at</c><00:01:07.600><c> this</c><00:01:07.760><c> point</c><00:01:08.080><c> in</c><00:01:08.159><c> the</c><00:01:08.320><c> script</c><00:01:09.040><c> here's</c>

00:01:09.270 --> 00:01:09.280 align:start position:0%
we're at this point in the script here's
 

00:01:09.280 --> 00:01:11.270 align:start position:0%
we're at this point in the script here's
the<00:01:09.439><c> command</c><00:01:09.760><c> i</c><00:01:09.840><c> need</c><00:01:10.000><c> to</c><00:01:10.159><c> run</c>

00:01:11.270 --> 00:01:11.280 align:start position:0%
the command i need to run
 

00:01:11.280 --> 00:01:13.830 align:start position:0%
the command i need to run
luckily<00:01:11.600><c> i</c><00:01:11.760><c> know</c><00:01:12.159><c> info</c><00:01:12.560><c> pretty</c><00:01:12.720><c> well</c><00:01:13.280><c> and</c><00:01:13.600><c> so</c><00:01:13.760><c> i</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
luckily i know info pretty well and so i
 

00:01:13.840 --> 00:01:16.310 align:start position:0%
luckily i know info pretty well and so i
can<00:01:14.000><c> see</c><00:01:14.240><c> oh</c><00:01:14.479><c> i</c><00:01:14.560><c> need</c><00:01:14.720><c> to</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
can see oh i need to
 

00:01:16.320 --> 00:01:18.710 align:start position:0%
can see oh i need to
add<00:01:16.560><c> a</c><00:01:16.720><c> grant</c><00:01:17.200><c> for</c><00:01:17.439><c> a</c><00:01:17.600><c> particular</c><00:01:18.000><c> user</c><00:01:18.400><c> i</c><00:01:18.560><c> know</c>

00:01:18.710 --> 00:01:18.720 align:start position:0%
add a grant for a particular user i know
 

00:01:18.720 --> 00:01:22.870 align:start position:0%
add a grant for a particular user i know
i<00:01:18.799><c> need</c><00:01:18.960><c> to</c><00:01:19.119><c> run</c><00:01:19.360><c> infra</c><00:01:19.759><c> grants</c><00:01:20.400><c> ad</c><00:01:20.799><c> etc</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
i need to run infra grants ad etc
 

00:01:22.880 --> 00:01:25.030 align:start position:0%
i need to run infra grants ad etc
but<00:01:23.280><c> it</c><00:01:23.360><c> requires</c><00:01:23.759><c> a</c><00:01:23.759><c> little</c><00:01:23.920><c> bit</c><00:01:24.080><c> of</c><00:01:24.159><c> thought</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
but it requires a little bit of thought
 

00:01:25.040 --> 00:01:27.190 align:start position:0%
but it requires a little bit of thought
and<00:01:25.520><c> sometimes</c><00:01:25.920><c> i</c><00:01:26.000><c> get</c><00:01:26.080><c> the</c><00:01:26.159><c> command</c><00:01:26.560><c> wrong</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
and sometimes i get the command wrong
 

00:01:27.200 --> 00:01:29.990 align:start position:0%
and sometimes i get the command wrong
and<00:01:27.520><c> then</c><00:01:27.759><c> i</c><00:01:28.240><c> do</c><00:01:28.479><c> i</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
and then i do i
 

00:01:30.000 --> 00:01:32.469 align:start position:0%
and then i do i
start<00:01:30.400><c> over</c><00:01:30.960><c> because</c><00:01:31.280><c> i</c><00:01:31.360><c> want</c><00:01:31.520><c> to</c><00:01:31.680><c> have</c><00:01:31.840><c> a</c><00:01:32.079><c> nice</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
start over because i want to have a nice
 

00:01:32.479 --> 00:01:34.230 align:start position:0%
start over because i want to have a nice
clean<00:01:33.200><c> demo</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
clean demo
 

00:01:34.240 --> 00:01:35.670 align:start position:0%
clean demo
when<00:01:34.479><c> i</c><00:01:34.560><c> record</c><00:01:34.880><c> it</c>

00:01:35.670 --> 00:01:35.680 align:start position:0%
when i record it
 

00:01:35.680 --> 00:01:37.510 align:start position:0%
when i record it
or<00:01:36.400><c> do</c><00:01:36.640><c> i</c><00:01:36.720><c> just</c>

00:01:37.510 --> 00:01:37.520 align:start position:0%
or do i just
 

00:01:37.520 --> 00:01:40.870 align:start position:0%
or do i just
creatively<00:01:38.159><c> edit</c><00:01:38.720><c> that</c><00:01:39.040><c> out</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
creatively edit that out
 

00:01:40.880 --> 00:01:43.270 align:start position:0%
creatively edit that out
so<00:01:41.200><c> what</c><00:01:41.360><c> i've</c><00:01:41.600><c> come</c><00:01:41.840><c> up</c><00:01:42.000><c> with</c><00:01:42.240><c> so</c><00:01:42.399><c> in</c><00:01:42.560><c> fact</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
so what i've come up with so in fact
 

00:01:43.280 --> 00:01:45.670 align:start position:0%
so what i've come up with so in fact
here<00:01:43.600><c> is</c><00:01:44.000><c> the</c><00:01:44.320><c> the</c><00:01:44.479><c> script</c><00:01:44.799><c> that</c><00:01:44.880><c> i've</c><00:01:45.040><c> been</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
here is the the script that i've been
 

00:01:45.680 --> 00:01:47.429 align:start position:0%
here is the the script that i've been
working<00:01:46.079><c> with</c><00:01:46.560><c> for</c>

00:01:47.429 --> 00:01:47.439 align:start position:0%
working with for
 

00:01:47.439 --> 00:01:49.749 align:start position:0%
working with for
the<00:01:47.520><c> most</c><00:01:47.759><c> recent</c><00:01:48.159><c> video</c><00:01:48.880><c> it's</c><00:01:49.040><c> kind</c><00:01:49.280><c> of</c><00:01:49.439><c> a</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
the most recent video it's kind of a
 

00:01:49.759 --> 00:01:52.550 align:start position:0%
the most recent video it's kind of a
introduction<00:01:50.399><c> to</c><00:01:50.720><c> what</c><00:01:51.040><c> is</c><00:01:51.280><c> infra</c>

00:01:52.550 --> 00:01:52.560 align:start position:0%
introduction to what is infra
 

00:01:52.560 --> 00:01:54.630 align:start position:0%
introduction to what is infra
and<00:01:53.040><c> showing</c><00:01:53.360><c> how</c><00:01:53.520><c> you</c><00:01:53.680><c> can</c>

00:01:54.630 --> 00:01:54.640 align:start position:0%
and showing how you can
 

00:01:54.640 --> 00:01:57.590 align:start position:0%
and showing how you can
work<00:01:54.880><c> with</c><00:01:55.040><c> infra</c><00:01:55.600><c> to</c><00:01:56.000><c> give</c><00:01:56.240><c> access</c><00:01:56.719><c> to</c><00:01:57.119><c> two</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
work with infra to give access to two
 

00:01:57.600 --> 00:01:59.590 align:start position:0%
work with infra to give access to two
separate<00:01:58.079><c> clusters</c><00:01:58.799><c> running</c><00:01:59.119><c> on</c><00:01:59.200><c> digital</c>

00:01:59.590 --> 00:01:59.600 align:start position:0%
separate clusters running on digital
 

00:01:59.600 --> 00:02:00.789 align:start position:0%
separate clusters running on digital
ocean

00:02:00.789 --> 00:02:00.799 align:start position:0%
ocean
 

00:02:00.799 --> 00:02:02.789 align:start position:0%
ocean
and

00:02:02.789 --> 00:02:02.799 align:start position:0%
and
 

00:02:02.799 --> 00:02:04.950 align:start position:0%
and
and<00:02:02.960><c> i</c><00:02:03.200><c> do</c><00:02:03.439><c> that</c><00:02:03.680><c> in</c><00:02:03.840><c> about</c>

00:02:04.950 --> 00:02:04.960 align:start position:0%
and i do that in about
 

00:02:04.960 --> 00:02:06.870 align:start position:0%
and i do that in about
two<00:02:05.200><c> hours</c><00:02:05.439><c> and</c><00:02:05.600><c> oh</c><00:02:05.840><c> sorry</c><00:02:06.079><c> two</c><00:02:06.240><c> minutes</c><00:02:06.640><c> and</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
two hours and oh sorry two minutes and
 

00:02:06.880 --> 00:02:08.469 align:start position:0%
two hours and oh sorry two minutes and
40<00:02:07.200><c> seconds</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
40 seconds
 

00:02:08.479 --> 00:02:10.790 align:start position:0%
40 seconds
so<00:02:08.720><c> it's</c><00:02:08.959><c> a</c><00:02:09.119><c> series</c><00:02:09.520><c> of</c><00:02:09.679><c> steps</c><00:02:09.920><c> i</c><00:02:10.080><c> need</c><00:02:10.239><c> to</c><00:02:10.319><c> run</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
so it's a series of steps i need to run
 

00:02:10.800 --> 00:02:12.229 align:start position:0%
so it's a series of steps i need to run
i<00:02:10.879><c> want</c><00:02:10.959><c> to</c><00:02:11.120><c> make</c><00:02:11.200><c> sure</c><00:02:11.360><c> i</c><00:02:11.440><c> do</c><00:02:11.599><c> it</c><00:02:11.840><c> in</c><00:02:11.920><c> the</c><00:02:12.000><c> right</c>

00:02:12.229 --> 00:02:12.239 align:start position:0%
i want to make sure i do it in the right
 

00:02:12.239 --> 00:02:15.030 align:start position:0%
i want to make sure i do it in the right
order<00:02:12.959><c> and</c><00:02:13.760><c> everything's</c><00:02:14.400><c> flawless</c><00:02:14.879><c> i</c><00:02:14.959><c> get</c>

00:02:15.030 --> 00:02:15.040 align:start position:0%
order and everything's flawless i get
 

00:02:15.040 --> 00:02:16.390 align:start position:0%
order and everything's flawless i get
the<00:02:15.120><c> right</c><00:02:15.280><c> commands</c><00:02:15.680><c> each</c><00:02:15.920><c> time</c><00:02:16.160><c> i</c><00:02:16.239><c> don't</c>

00:02:16.390 --> 00:02:16.400 align:start position:0%
the right commands each time i don't
 

00:02:16.400 --> 00:02:18.150 align:start position:0%
the right commands each time i don't
have<00:02:16.560><c> to</c><00:02:16.720><c> figure</c><00:02:17.040><c> you</c><00:02:17.120><c> know</c><00:02:17.280><c> if</c><00:02:17.440><c> i</c><00:02:17.520><c> have</c><00:02:17.680><c> to</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
have to figure you know if i have to
 

00:02:18.160 --> 00:02:20.229 align:start position:0%
have to figure you know if i have to
figure<00:02:18.480><c> out</c><00:02:18.640><c> the</c><00:02:18.720><c> command</c><00:02:19.520><c> as</c><00:02:19.760><c> i'm</c><00:02:20.000><c> reading</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
figure out the command as i'm reading
 

00:02:20.239 --> 00:02:21.589 align:start position:0%
figure out the command as i'm reading
through<00:02:20.400><c> the</c><00:02:20.480><c> script</c><00:02:20.959><c> that's</c><00:02:21.200><c> not</c><00:02:21.360><c> very</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
through the script that's not very
 

00:02:21.599 --> 00:02:22.470 align:start position:0%
through the script that's not very
efficient

00:02:22.470 --> 00:02:22.480 align:start position:0%
efficient
 

00:02:22.480 --> 00:02:24.150 align:start position:0%
efficient
and<00:02:22.560><c> so</c><00:02:22.720><c> what</c><00:02:22.879><c> i</c><00:02:23.040><c> ended</c><00:02:23.360><c> up</c><00:02:23.440><c> doing</c><00:02:23.760><c> was</c>

00:02:24.150 --> 00:02:24.160 align:start position:0%
and so what i ended up doing was
 

00:02:24.160 --> 00:02:26.309 align:start position:0%
and so what i ended up doing was
creating<00:02:24.800><c> a</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
creating a
 

00:02:26.319 --> 00:02:28.229 align:start position:0%
creating a
basically<00:02:26.720><c> a</c><00:02:26.879><c> list</c><00:02:27.200><c> of</c><00:02:27.440><c> all</c><00:02:27.599><c> the</c><00:02:27.680><c> commands</c><00:02:28.080><c> i'm</c>

00:02:28.229 --> 00:02:28.239 align:start position:0%
basically a list of all the commands i'm
 

00:02:28.239 --> 00:02:30.150 align:start position:0%
basically a list of all the commands i'm
going<00:02:28.400><c> to</c><00:02:28.560><c> run</c><00:02:29.120><c> you</c><00:02:29.280><c> know</c><00:02:29.360><c> it</c><00:02:29.520><c> starts</c><00:02:29.760><c> off</c><00:02:29.920><c> with</c>

00:02:30.150 --> 00:02:30.160 align:start position:0%
going to run you know it starts off with
 

00:02:30.160 --> 00:02:32.869 align:start position:0%
going to run you know it starts off with
a<00:02:30.239><c> brew</c><00:02:30.640><c> install</c><00:02:31.280><c> infra</c><00:02:31.920><c> command</c>

00:02:32.869 --> 00:02:32.879 align:start position:0%
a brew install infra command
 

00:02:32.879 --> 00:02:35.430 align:start position:0%
a brew install infra command
and<00:02:33.040><c> then</c><00:02:33.200><c> i'm</c><00:02:33.280><c> looking</c><00:02:33.599><c> at</c><00:02:33.760><c> the</c><00:02:34.000><c> uh</c><00:02:34.640><c> the</c><00:02:34.959><c> files</c>

00:02:35.430 --> 00:02:35.440 align:start position:0%
and then i'm looking at the uh the files
 

00:02:35.440 --> 00:02:37.030 align:start position:0%
and then i'm looking at the uh the files
in<00:02:35.519><c> the</c><00:02:35.680><c> directory</c><00:02:36.239><c> i'm</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
in the directory i'm
 

00:02:37.040 --> 00:02:40.229 align:start position:0%
in the directory i'm
exporting<00:02:37.519><c> the</c><00:02:37.680><c> coop</c><00:02:37.840><c> config</c><00:02:38.239><c> to</c><00:02:38.480><c> uh</c><00:02:39.120><c> um</c><00:02:39.760><c> or</c>

00:02:40.229 --> 00:02:40.239 align:start position:0%
exporting the coop config to uh um or
 

00:02:40.239 --> 00:02:41.509 align:start position:0%
exporting the coop config to uh um or
setting<00:02:40.560><c> the</c>

00:02:41.509 --> 00:02:41.519 align:start position:0%
setting the
 

00:02:41.519 --> 00:02:43.670 align:start position:0%
setting the
coupe<00:02:41.760><c> config</c><00:02:42.160><c> file</c><00:02:42.640><c> to</c><00:02:43.040><c> an</c><00:02:43.200><c> environment</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
coupe config file to an environment
 

00:02:43.680 --> 00:02:45.509 align:start position:0%
coupe config file to an environment
variable<00:02:44.160><c> and</c><00:02:44.480><c> so</c><00:02:44.720><c> forth</c><00:02:45.040><c> and</c><00:02:45.200><c> i'm</c><00:02:45.280><c> going</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
variable and so forth and i'm going
 

00:02:45.519 --> 00:02:48.309 align:start position:0%
variable and so forth and i'm going
through<00:02:45.760><c> the</c><00:02:45.840><c> entire</c><00:02:46.239><c> process</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
through the entire process
 

00:02:48.319 --> 00:02:49.990 align:start position:0%
through the entire process
so<00:02:48.480><c> that's</c><00:02:48.720><c> pretty</c><00:02:48.959><c> good</c><00:02:49.280><c> that's</c><00:02:49.680><c> super</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
so that's pretty good that's super
 

00:02:50.000 --> 00:02:52.470 align:start position:0%
so that's pretty good that's super
useful<00:02:50.640><c> having</c><00:02:51.040><c> that</c><00:02:51.519><c> there</c>

00:02:52.470 --> 00:02:52.480 align:start position:0%
useful having that there
 

00:02:52.480 --> 00:02:55.589 align:start position:0%
useful having that there
but<00:02:53.040><c> now</c><00:02:53.840><c> you</c><00:02:54.000><c> know</c><00:02:54.160><c> if</c><00:02:54.319><c> i</c><00:02:54.640><c> squeeze</c><00:02:55.200><c> down</c><00:02:55.440><c> my</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
but now you know if i squeeze down my
 

00:02:55.599 --> 00:02:59.270 align:start position:0%
but now you know if i squeeze down my
obsidian<00:02:56.640><c> editor</c><00:02:57.440><c> so</c><00:02:57.760><c> that</c><00:02:58.000><c> i</c><00:02:58.159><c> just</c><00:02:58.480><c> see</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
obsidian editor so that i just see
 

00:02:59.280 --> 00:03:01.670 align:start position:0%
obsidian editor so that i just see
the<00:02:59.440><c> commands</c><00:02:59.920><c> and</c><00:03:00.080><c> i've</c><00:03:00.159><c> got</c><00:03:00.400><c> this</c><00:03:00.720><c> over</c><00:03:00.959><c> here</c>

00:03:01.670 --> 00:03:01.680 align:start position:0%
the commands and i've got this over here
 

00:03:01.680 --> 00:03:04.149 align:start position:0%
the commands and i've got this over here
you<00:03:01.760><c> know</c><00:03:01.920><c> i'm</c><00:03:02.159><c> going</c><00:03:02.400><c> to</c><00:03:03.120><c> i</c><00:03:03.280><c> usually</c><00:03:03.599><c> use</c>

00:03:04.149 --> 00:03:04.159 align:start position:0%
you know i'm going to i usually use
 

00:03:04.159 --> 00:03:05.350 align:start position:0%
you know i'm going to i usually use
clean<00:03:04.480><c> shot</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
clean shot
 

00:03:05.360 --> 00:03:08.710 align:start position:0%
clean shot
clean<00:03:05.599><c> shot</c><00:03:05.920><c> x</c><00:03:06.239><c> to</c><00:03:06.800><c> record</c><00:03:07.360><c> the</c><00:03:07.519><c> video</c><00:03:08.000><c> here</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
clean shot x to record the video here
 

00:03:08.720 --> 00:03:10.149 align:start position:0%
clean shot x to record the video here
even<00:03:08.959><c> though</c><00:03:09.120><c> i</c><00:03:09.280><c> have</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
even though i have
 

00:03:10.159 --> 00:03:12.630 align:start position:0%
even though i have
uh<00:03:10.400><c> screenflow</c><00:03:11.200><c> and</c><00:03:11.680><c> other</c><00:03:11.840><c> tools</c><00:03:12.239><c> i</c><00:03:12.400><c> really</c>

00:03:12.630 --> 00:03:12.640 align:start position:0%
uh screenflow and other tools i really
 

00:03:12.640 --> 00:03:16.149 align:start position:0%
uh screenflow and other tools i really
like<00:03:12.879><c> how</c><00:03:13.519><c> clean</c><00:03:13.840><c> shot</c><00:03:14.480><c> does</c><00:03:14.800><c> that</c><00:03:15.040><c> recording</c>

00:03:16.149 --> 00:03:16.159 align:start position:0%
like how clean shot does that recording
 

00:03:16.159 --> 00:03:18.790 align:start position:0%
like how clean shot does that recording
so<00:03:16.400><c> i</c><00:03:17.040><c> start</c><00:03:17.200><c> the</c><00:03:17.360><c> recording</c><00:03:18.159><c> but</c><00:03:18.319><c> then</c><00:03:18.560><c> i</c><00:03:18.640><c> have</c>

00:03:18.790 --> 00:03:18.800 align:start position:0%
so i start the recording but then i have
 

00:03:18.800 --> 00:03:20.630 align:start position:0%
so i start the recording but then i have
to<00:03:18.959><c> come</c><00:03:19.200><c> over</c><00:03:19.519><c> to</c><00:03:19.760><c> this</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
to come over to this
 

00:03:20.640 --> 00:03:21.509 align:start position:0%
to come over to this
thing

00:03:21.509 --> 00:03:21.519 align:start position:0%
thing
 

00:03:21.519 --> 00:03:22.309 align:start position:0%
thing
and

00:03:22.309 --> 00:03:22.319 align:start position:0%
and
 

00:03:22.319 --> 00:03:24.309 align:start position:0%
and
one<00:03:22.480><c> of</c><00:03:22.560><c> the</c><00:03:22.640><c> other</c><00:03:22.800><c> benefits</c><00:03:23.280><c> here</c><00:03:23.519><c> is</c><00:03:23.760><c> that</c><00:03:24.080><c> i</c>

00:03:24.309 --> 00:03:24.319 align:start position:0%
one of the other benefits here is that i
 

00:03:24.319 --> 00:03:25.990 align:start position:0%
one of the other benefits here is that i
want<00:03:24.640><c> to</c><00:03:24.879><c> make</c><00:03:25.040><c> sure</c><00:03:25.200><c> i</c><00:03:25.360><c> get</c><00:03:25.519><c> the</c><00:03:25.599><c> command</c>

00:03:25.990 --> 00:03:26.000 align:start position:0%
want to make sure i get the command
 

00:03:26.000 --> 00:03:28.070 align:start position:0%
want to make sure i get the command
right<00:03:26.400><c> and</c><00:03:26.640><c> you</c><00:03:26.720><c> don't</c><00:03:26.879><c> see</c><00:03:27.040><c> a</c><00:03:27.120><c> lot</c><00:03:27.280><c> of</c><00:03:27.360><c> typos</c>

00:03:28.070 --> 00:03:28.080 align:start position:0%
right and you don't see a lot of typos
 

00:03:28.080 --> 00:03:29.670 align:start position:0%
right and you don't see a lot of typos
you<00:03:28.239><c> know</c><00:03:28.319><c> if</c><00:03:28.480><c> i</c><00:03:28.560><c> have</c><00:03:28.640><c> to</c><00:03:28.799><c> type</c><00:03:29.040><c> in</c><00:03:29.280><c> brew</c>

00:03:29.670 --> 00:03:29.680 align:start position:0%
you know if i have to type in brew
 

00:03:29.680 --> 00:03:31.830 align:start position:0%
you know if i have to type in brew
install<00:03:30.159><c> in</c><00:03:30.239><c> for</c><00:03:30.400><c> hq</c>

00:03:31.830 --> 00:03:31.840 align:start position:0%
install in for hq
 

00:03:31.840 --> 00:03:34.149 align:start position:0%
install in for hq
infra<00:03:32.720><c> i</c><00:03:32.879><c> might</c><00:03:33.280><c> get</c><00:03:33.440><c> a</c><00:03:33.519><c> letter</c><00:03:33.760><c> wrong</c><00:03:34.000><c> and</c><00:03:34.159><c> i</c>

00:03:34.149 --> 00:03:34.159 align:start position:0%
infra i might get a letter wrong and i
 

00:03:34.159 --> 00:03:36.229 align:start position:0%
infra i might get a letter wrong and i
have<00:03:34.239><c> to</c><00:03:34.319><c> go</c><00:03:34.560><c> back</c><00:03:34.959><c> and</c><00:03:35.360><c> again</c><00:03:35.680><c> i</c><00:03:35.760><c> have</c><00:03:35.840><c> to</c><00:03:36.000><c> edit</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
have to go back and again i have to edit
 

00:03:36.239 --> 00:03:37.990 align:start position:0%
have to go back and again i have to edit
that<00:03:36.480><c> out</c><00:03:36.720><c> and</c><00:03:36.799><c> that's</c><00:03:37.360><c> that's</c><00:03:37.680><c> kind</c><00:03:37.840><c> of</c><00:03:37.920><c> a</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
that out and that's that's kind of a
 

00:03:38.000 --> 00:03:39.350 align:start position:0%
that out and that's that's kind of a
pain<00:03:38.400><c> and</c>

00:03:39.350 --> 00:03:39.360 align:start position:0%
pain and
 

00:03:39.360 --> 00:03:41.990 align:start position:0%
pain and
sometimes<00:03:40.000><c> my</c><00:03:40.319><c> typing</c><00:03:40.720><c> is</c><00:03:40.879><c> not</c><00:03:41.120><c> very</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
sometimes my typing is not very
 

00:03:42.000 --> 00:03:45.190 align:start position:0%
sometimes my typing is not very
consistent<00:03:42.640><c> speed</c><00:03:43.599><c> um</c><00:03:44.239><c> and</c><00:03:44.480><c> i</c><00:03:44.640><c> have</c><00:03:44.720><c> to</c><00:03:44.879><c> edit</c>

00:03:45.190 --> 00:03:45.200 align:start position:0%
consistent speed um and i have to edit
 

00:03:45.200 --> 00:03:47.589 align:start position:0%
consistent speed um and i have to edit
that<00:03:45.680><c> so</c><00:03:45.920><c> it</c><00:03:46.080><c> looks</c><00:03:46.319><c> a</c><00:03:46.400><c> little</c><00:03:46.640><c> bit</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
that so it looks a little bit
 

00:03:47.599 --> 00:03:50.149 align:start position:0%
that so it looks a little bit
i<00:03:47.840><c> i</c><00:03:47.920><c> don't</c><00:03:48.560><c> the</c><00:03:48.720><c> last</c><00:03:48.879><c> thing</c><00:03:49.040><c> i</c><00:03:49.120><c> want</c><00:03:49.760><c> an</c><00:03:50.000><c> end</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
i i don't the last thing i want an end
 

00:03:50.159 --> 00:03:53.429 align:start position:0%
i i don't the last thing i want an end
user<00:03:50.560><c> or</c><00:03:50.959><c> a</c><00:03:51.440><c> viewer</c><00:03:51.920><c> to</c><00:03:52.159><c> do</c><00:03:52.480><c> is</c><00:03:52.720><c> have</c><00:03:52.879><c> to</c><00:03:53.200><c> sit</c>

00:03:53.429 --> 00:03:53.439 align:start position:0%
user or a viewer to do is have to sit
 

00:03:53.439 --> 00:03:57.190 align:start position:0%
user or a viewer to do is have to sit
through<00:03:53.760><c> me</c><00:03:54.159><c> typing</c><00:03:54.720><c> badly</c>

00:03:57.190 --> 00:03:57.200 align:start position:0%
through me typing badly
 

00:03:57.200 --> 00:03:58.309 align:start position:0%
through me typing badly
and<00:03:57.360><c> so</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
and so
 

00:03:58.319 --> 00:03:59.190 align:start position:0%
and so
i<00:03:58.480><c> can</c>

00:03:59.190 --> 00:03:59.200 align:start position:0%
i can
 

00:03:59.200 --> 00:04:00.949 align:start position:0%
i can
select<00:03:59.599><c> this</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
select this
 

00:04:00.959 --> 00:04:04.390 align:start position:0%
select this
uh<00:04:01.280><c> let's</c><00:04:01.439><c> see</c><00:04:01.920><c> i</c><00:04:02.000><c> can</c><00:04:02.159><c> select</c><00:04:02.480><c> this</c><00:04:02.959><c> copy</c>

00:04:04.390 --> 00:04:04.400 align:start position:0%
uh let's see i can select this copy
 

00:04:04.400 --> 00:04:06.470 align:start position:0%
uh let's see i can select this copy
and<00:04:04.640><c> paste</c><00:04:04.959><c> it</c><00:04:05.120><c> over</c><00:04:05.360><c> here</c>

00:04:06.470 --> 00:04:06.480 align:start position:0%
and paste it over here
 

00:04:06.480 --> 00:04:08.710 align:start position:0%
and paste it over here
but<00:04:07.120><c> that's</c><00:04:07.439><c> not</c><00:04:07.680><c> really</c>

00:04:08.710 --> 00:04:08.720 align:start position:0%
but that's not really
 

00:04:08.720 --> 00:04:11.030 align:start position:0%
but that's not really
it's<00:04:08.879><c> not</c><00:04:09.120><c> typing</c><00:04:09.840><c> and</c><00:04:10.000><c> so</c><00:04:10.159><c> i</c><00:04:10.239><c> thought</c><00:04:10.480><c> well</c><00:04:10.959><c> i</c>

00:04:11.030 --> 00:04:11.040 align:start position:0%
it's not typing and so i thought well i
 

00:04:11.040 --> 00:04:13.110 align:start position:0%
it's not typing and so i thought well i
can<00:04:11.200><c> use</c><00:04:11.439><c> keyboard</c><00:04:11.760><c> mice</c><00:04:12.159><c> in</c><00:04:12.239><c> fact</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
can use keyboard mice in fact
 

00:04:13.120 --> 00:04:15.030 align:start position:0%
can use keyboard mice in fact
as<00:04:13.280><c> soon</c><00:04:13.519><c> as</c><00:04:13.680><c> i</c><00:04:13.680><c> do</c><00:04:13.920><c> this</c><00:04:14.080><c> you</c><00:04:14.239><c> can</c><00:04:14.400><c> see</c><00:04:14.799><c> i've</c>

00:04:15.030 --> 00:04:15.040 align:start position:0%
as soon as i do this you can see i've
 

00:04:15.040 --> 00:04:18.229 align:start position:0%
as soon as i do this you can see i've
got<00:04:15.360><c> this</c><00:04:16.079><c> uh</c><00:04:16.400><c> selected</c><00:04:16.959><c> so</c><00:04:17.120><c> it's</c><00:04:17.280><c> got</c><00:04:17.600><c> a</c><00:04:17.919><c> black</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
got this uh selected so it's got a black
 

00:04:18.239 --> 00:04:20.310 align:start position:0%
got this uh selected so it's got a black
text<00:04:18.560><c> on</c><00:04:18.639><c> a</c><00:04:18.720><c> white</c><00:04:18.959><c> background</c><00:04:19.840><c> rather</c><00:04:20.160><c> than</c>

00:04:20.310 --> 00:04:20.320 align:start position:0%
text on a white background rather than
 

00:04:20.320 --> 00:04:22.069 align:start position:0%
text on a white background rather than
just<00:04:20.560><c> the</c>

00:04:22.069 --> 00:04:22.079 align:start position:0%
just the
 

00:04:22.079 --> 00:04:23.510 align:start position:0%
just the
oh<00:04:22.320><c> i</c><00:04:22.400><c> have</c><00:04:22.479><c> to</c><00:04:22.560><c> press</c><00:04:22.800><c> the</c><00:04:22.960><c> base</c><00:04:23.120><c> bar</c><00:04:23.360><c> to</c><00:04:23.440><c> get</c>

00:04:23.510 --> 00:04:23.520 align:start position:0%
oh i have to press the base bar to get
 

00:04:23.520 --> 00:04:25.909 align:start position:0%
oh i have to press the base bar to get
rid<00:04:23.680><c> of</c><00:04:23.840><c> it</c><00:04:23.919><c> so</c><00:04:24.560><c> it's</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
rid of it so it's
 

00:04:25.919 --> 00:04:28.870 align:start position:0%
rid of it so it's
even<00:04:26.160><c> that's</c><00:04:26.479><c> not</c><00:04:26.720><c> that</c><00:04:26.960><c> great</c><00:04:28.240><c> what</c><00:04:28.400><c> i</c><00:04:28.560><c> really</c>

00:04:28.870 --> 00:04:28.880 align:start position:0%
even that's not that great what i really
 

00:04:28.880 --> 00:04:31.909 align:start position:0%
even that's not that great what i really
want<00:04:29.040><c> to</c><00:04:29.120><c> do</c><00:04:29.520><c> is</c><00:04:29.680><c> be</c><00:04:29.840><c> able</c><00:04:30.160><c> to</c><00:04:30.800><c> see</c><00:04:31.040><c> that</c><00:04:31.680><c> and</c>

00:04:31.909 --> 00:04:31.919 align:start position:0%
want to do is be able to see that and
 

00:04:31.919 --> 00:04:34.070 align:start position:0%
want to do is be able to see that and
type<00:04:32.240><c> it</c><00:04:32.400><c> in</c><00:04:32.720><c> over</c><00:04:33.040><c> here</c>

00:04:34.070 --> 00:04:34.080 align:start position:0%
type it in over here
 

00:04:34.080 --> 00:04:35.749 align:start position:0%
type it in over here
and<00:04:34.240><c> so</c><00:04:34.400><c> what</c><00:04:34.479><c> i</c><00:04:34.639><c> ended</c><00:04:34.880><c> up</c><00:04:34.960><c> doing</c><00:04:35.199><c> was</c><00:04:35.440><c> create</c>

00:04:35.749 --> 00:04:35.759 align:start position:0%
and so what i ended up doing was create
 

00:04:35.759 --> 00:04:38.150 align:start position:0%
and so what i ended up doing was create
a<00:04:36.080><c> keyboard</c><00:04:36.560><c> maestro</c>

00:04:38.150 --> 00:04:38.160 align:start position:0%
a keyboard maestro
 

00:04:38.160 --> 00:04:39.350 align:start position:0%
a keyboard maestro
macro

00:04:39.350 --> 00:04:39.360 align:start position:0%
macro
 

00:04:39.360 --> 00:04:43.749 align:start position:0%
macro
so<00:04:39.520><c> let's</c><00:04:39.840><c> bring</c><00:04:40.160><c> that</c><00:04:40.560><c> up</c><00:04:41.199><c> keyboard</c><00:04:41.759><c> maestro</c>

00:04:43.749 --> 00:04:43.759 align:start position:0%
so let's bring that up keyboard maestro
 

00:04:43.759 --> 00:04:46.310 align:start position:0%
so let's bring that up keyboard maestro
and<00:04:43.919><c> here</c><00:04:44.160><c> it</c><00:04:44.320><c> is</c>

00:04:46.310 --> 00:04:46.320 align:start position:0%
and here it is
 

00:04:46.320 --> 00:04:48.230 align:start position:0%
and here it is
you<00:04:46.400><c> can</c><00:04:46.560><c> see</c><00:04:46.800><c> that</c><00:04:47.520><c> basically</c><00:04:47.919><c> i've</c><00:04:48.000><c> got</c><00:04:48.160><c> a</c>

00:04:48.230 --> 00:04:48.240 align:start position:0%
you can see that basically i've got a
 

00:04:48.240 --> 00:04:50.390 align:start position:0%
you can see that basically i've got a
command<00:04:48.639><c> that</c><00:04:48.800><c> just</c><00:04:49.040><c> says</c><00:04:49.520><c> okay</c><00:04:49.840><c> if</c><00:04:49.919><c> you</c><00:04:50.160><c> type</c>

00:04:50.390 --> 00:04:50.400 align:start position:0%
command that just says okay if you type
 

00:04:50.400 --> 00:04:51.749 align:start position:0%
command that just says okay if you type
in<00:04:50.560><c> the</c>

00:04:51.749 --> 00:04:51.759 align:start position:0%
in the
 

00:04:51.759 --> 00:04:54.790 align:start position:0%
in the
the<00:04:51.840><c> hyperkey</c><00:04:52.639><c> so</c><00:04:52.800><c> i</c><00:04:52.960><c> have</c><00:04:53.199><c> caps</c><00:04:53.520><c> lock</c><00:04:54.240><c> remap</c>

00:04:54.790 --> 00:04:54.800 align:start position:0%
the hyperkey so i have caps lock remap
 

00:04:54.800 --> 00:04:55.510 align:start position:0%
the hyperkey so i have caps lock remap
to

00:04:55.510 --> 00:04:55.520 align:start position:0%
to
 

00:04:55.520 --> 00:04:57.270 align:start position:0%
to
control<00:04:56.320><c> option</c>

00:04:57.270 --> 00:04:57.280 align:start position:0%
control option
 

00:04:57.280 --> 00:04:59.110 align:start position:0%
control option
command<00:04:58.160><c> shift</c>

00:04:59.110 --> 00:04:59.120 align:start position:0%
command shift
 

00:04:59.120 --> 00:05:01.670 align:start position:0%
command shift
uh<00:04:59.360><c> if</c><00:04:59.520><c> i</c><00:04:59.680><c> press</c><00:05:00.080><c> that</c><00:05:00.479><c> command</c><00:05:00.960><c> or</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
uh if i press that command or
 

00:05:01.680 --> 00:05:03.189 align:start position:0%
uh if i press that command or
hyper<00:05:02.240><c> v</c>

00:05:03.189 --> 00:05:03.199 align:start position:0%
hyper v
 

00:05:03.199 --> 00:05:04.469 align:start position:0%
hyper v
then

00:05:04.469 --> 00:05:04.479 align:start position:0%
then
 

00:05:04.479 --> 00:05:08.230 align:start position:0%
then
um<00:05:05.199><c> go</c><00:05:05.360><c> ahead</c><00:05:05.840><c> and</c><00:05:06.400><c> copy</c><00:05:06.800><c> whatever</c><00:05:07.680><c> is</c><00:05:08.000><c> under</c>

00:05:08.230 --> 00:05:08.240 align:start position:0%
um go ahead and copy whatever is under
 

00:05:08.240 --> 00:05:11.430 align:start position:0%
um go ahead and copy whatever is under
here<00:05:08.880><c> oh</c><00:05:09.680><c> yeah</c><00:05:09.919><c> i</c><00:05:10.000><c> forgot</c><00:05:10.400><c> another</c><00:05:10.720><c> reason</c><00:05:11.039><c> why</c>

00:05:11.430 --> 00:05:11.440 align:start position:0%
here oh yeah i forgot another reason why
 

00:05:11.440 --> 00:05:13.029 align:start position:0%
here oh yeah i forgot another reason why
i<00:05:11.520><c> wanted</c><00:05:11.759><c> to</c><00:05:11.919><c> do</c><00:05:12.080><c> this</c><00:05:12.240><c> keyboard</c><00:05:12.639><c> maestro</c>

00:05:13.029 --> 00:05:13.039 align:start position:0%
i wanted to do this keyboard maestro
 

00:05:13.039 --> 00:05:14.310 align:start position:0%
i wanted to do this keyboard maestro
thing<00:05:13.280><c> is</c>

00:05:14.310 --> 00:05:14.320 align:start position:0%
thing is
 

00:05:14.320 --> 00:05:16.870 align:start position:0%
thing is
you<00:05:14.479><c> know</c><00:05:14.800><c> i'll</c><00:05:15.039><c> copy</c><00:05:15.360><c> this</c><00:05:15.600><c> here</c><00:05:16.320><c> and</c><00:05:16.479><c> then</c>

00:05:16.870 --> 00:05:16.880 align:start position:0%
you know i'll copy this here and then
 

00:05:16.880 --> 00:05:18.469 align:start position:0%
you know i'll copy this here and then
tab<00:05:17.440><c> over</c>

00:05:18.469 --> 00:05:18.479 align:start position:0%
tab over
 

00:05:18.479 --> 00:05:19.430 align:start position:0%
tab over
and

00:05:19.430 --> 00:05:19.440 align:start position:0%
and
 

00:05:19.440 --> 00:05:20.629 align:start position:0%
and
you<00:05:19.520><c> know</c><00:05:19.600><c> it</c><00:05:19.680><c> doesn't</c><00:05:19.919><c> have</c><00:05:20.080><c> to</c><00:05:20.160><c> be</c><00:05:20.400><c> this</c>

00:05:20.629 --> 00:05:20.639 align:start position:0%
you know it doesn't have to be this
 

00:05:20.639 --> 00:05:22.070 align:start position:0%
you know it doesn't have to be this
particular

00:05:22.070 --> 00:05:22.080 align:start position:0%
particular
 

00:05:22.080 --> 00:05:24.710 align:start position:0%
particular
command<00:05:22.560><c> tab</c><00:05:23.280><c> window</c><00:05:23.680><c> but</c>

00:05:24.710 --> 00:05:24.720 align:start position:0%
command tab window but
 

00:05:24.720 --> 00:05:27.830 align:start position:0%
command tab window but
now<00:05:24.960><c> my</c><00:05:25.440><c> video</c><00:05:26.000><c> capture</c><00:05:26.479><c> is</c><00:05:26.639><c> capturing</c><00:05:27.120><c> this</c>

00:05:27.830 --> 00:05:27.840 align:start position:0%
now my video capture is capturing this
 

00:05:27.840 --> 00:05:30.950 align:start position:0%
now my video capture is capturing this
this<00:05:28.400><c> this</c><00:05:28.960><c> command</c><00:05:29.440><c> tab</c><00:05:29.919><c> prompt</c><00:05:30.400><c> and</c><00:05:30.639><c> again</c><00:05:30.880><c> i</c>

00:05:30.950 --> 00:05:30.960 align:start position:0%
this this command tab prompt and again i
 

00:05:30.960 --> 00:05:33.029 align:start position:0%
this this command tab prompt and again i
have<00:05:31.120><c> to</c><00:05:31.199><c> edit</c><00:05:31.520><c> that</c><00:05:31.759><c> out</c>

00:05:33.029 --> 00:05:33.039 align:start position:0%
have to edit that out
 

00:05:33.039 --> 00:05:35.189 align:start position:0%
have to edit that out
and<00:05:33.199><c> it's</c><00:05:33.360><c> just</c><00:05:33.600><c> one</c><00:05:33.840><c> more</c><00:05:34.479><c> it's</c><00:05:34.720><c> it's</c><00:05:34.880><c> another</c>

00:05:35.189 --> 00:05:35.199 align:start position:0%
and it's just one more it's it's another
 

00:05:35.199 --> 00:05:37.110 align:start position:0%
and it's just one more it's it's another
set<00:05:35.440><c> of</c><00:05:35.600><c> things</c><00:05:35.840><c> to</c><00:05:36.000><c> type</c><00:05:36.479><c> and</c><00:05:36.720><c> i</c><00:05:36.880><c> want</c><00:05:36.880><c> wanted</c>

00:05:37.110 --> 00:05:37.120 align:start position:0%
set of things to type and i want wanted
 

00:05:37.120 --> 00:05:39.270 align:start position:0%
set of things to type and i want wanted
really<00:05:37.280><c> wanted</c><00:05:37.520><c> to</c><00:05:37.680><c> simplify</c><00:05:38.160><c> this</c><00:05:38.400><c> so</c><00:05:39.039><c> back</c>

00:05:39.270 --> 00:05:39.280 align:start position:0%
really wanted to simplify this so back
 

00:05:39.280 --> 00:05:41.189 align:start position:0%
really wanted to simplify this so back
here

00:05:41.189 --> 00:05:41.199 align:start position:0%
here
 

00:05:41.199 --> 00:05:44.150 align:start position:0%
here
whenever<00:05:41.680><c> i</c><00:05:42.320><c> select</c><00:05:42.639><c> something</c><00:05:43.120><c> i</c><00:05:43.280><c> just</c><00:05:43.600><c> press</c>

00:05:44.150 --> 00:05:44.160 align:start position:0%
whenever i select something i just press
 

00:05:44.160 --> 00:05:46.550 align:start position:0%
whenever i select something i just press
hyper-v

00:05:46.550 --> 00:05:46.560 align:start position:0%
hyper-v
 

00:05:46.560 --> 00:05:48.310 align:start position:0%
hyper-v
and<00:05:46.800><c> what</c><00:05:46.960><c> that's</c><00:05:47.280><c> going</c><00:05:47.440><c> to</c><00:05:47.600><c> do</c><00:05:48.000><c> is</c><00:05:48.160><c> it's</c>

00:05:48.310 --> 00:05:48.320 align:start position:0%
and what that's going to do is it's
 

00:05:48.320 --> 00:05:50.629 align:start position:0%
and what that's going to do is it's
going<00:05:48.479><c> to</c><00:05:48.639><c> copy</c><00:05:48.960><c> whatever</c><00:05:49.360><c> is</c><00:05:49.440><c> selected</c><00:05:50.320><c> copy</c>

00:05:50.629 --> 00:05:50.639 align:start position:0%
going to copy whatever is selected copy
 

00:05:50.639 --> 00:05:52.550 align:start position:0%
going to copy whatever is selected copy
that<00:05:50.880><c> into</c><00:05:51.120><c> the</c><00:05:51.199><c> clipboard</c>

00:05:52.550 --> 00:05:52.560 align:start position:0%
that into the clipboard
 

00:05:52.560 --> 00:05:55.909 align:start position:0%
that into the clipboard
it's<00:05:52.800><c> going</c><00:05:52.880><c> to</c><00:05:53.199><c> switch</c><00:05:53.520><c> over</c><00:05:53.840><c> to</c><00:05:54.240><c> item</c>

00:05:55.909 --> 00:05:55.919 align:start position:0%
it's going to switch over to item
 

00:05:55.919 --> 00:05:58.629 align:start position:0%
it's going to switch over to item
not<00:05:56.240><c> all</c><00:05:56.479><c> the</c><00:05:56.639><c> windows</c><00:05:57.039><c> just</c><00:05:57.600><c> the</c><00:05:58.080><c> most</c><00:05:58.319><c> recent</c>

00:05:58.629 --> 00:05:58.639 align:start position:0%
not all the windows just the most recent
 

00:05:58.639 --> 00:05:59.909 align:start position:0%
not all the windows just the most recent
window

00:05:59.909 --> 00:05:59.919 align:start position:0%
window
 

00:05:59.919 --> 00:06:02.230 align:start position:0%
window
and<00:06:00.000><c> then</c><00:06:00.319><c> it's</c><00:06:00.479><c> going</c><00:06:00.560><c> to</c><00:06:00.720><c> pause</c><00:06:01.199><c> for</c><00:06:01.840><c> about</c><00:06:02.160><c> a</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
and then it's going to pause for about a
 

00:06:02.240 --> 00:06:05.189 align:start position:0%
and then it's going to pause for about a
third<00:06:02.479><c> of</c><00:06:02.560><c> a</c><00:06:02.639><c> second</c><00:06:03.120><c> just</c><00:06:03.360><c> to</c><00:06:03.440><c> make</c><00:06:03.680><c> sure</c><00:06:04.080><c> that</c>

00:06:05.189 --> 00:06:05.199 align:start position:0%
third of a second just to make sure that
 

00:06:05.199 --> 00:06:07.270 align:start position:0%
third of a second just to make sure that
you<00:06:05.360><c> know</c><00:06:05.440><c> if</c><00:06:05.600><c> i</c><00:06:05.680><c> do</c><00:06:05.840><c> it</c><00:06:06.000><c> right</c><00:06:06.160><c> away</c><00:06:06.880><c> then</c><00:06:07.120><c> i</c>

00:06:07.270 --> 00:06:07.280 align:start position:0%
you know if i do it right away then i
 

00:06:07.280 --> 00:06:10.150 align:start position:0%
you know if i do it right away then i
might<00:06:07.520><c> not</c><00:06:08.160><c> capture</c><00:06:08.639><c> the</c><00:06:08.960><c> edit</c><00:06:09.360><c> point</c>

00:06:10.150 --> 00:06:10.160 align:start position:0%
might not capture the edit point
 

00:06:10.160 --> 00:06:12.390 align:start position:0%
might not capture the edit point
correctly<00:06:10.720><c> and</c><00:06:11.199><c> and</c><00:06:11.600><c> i</c><00:06:11.759><c> wanted</c><00:06:12.000><c> to</c><00:06:12.080><c> make</c><00:06:12.240><c> sure</c>

00:06:12.390 --> 00:06:12.400 align:start position:0%
correctly and and i wanted to make sure
 

00:06:12.400 --> 00:06:13.670 align:start position:0%
correctly and and i wanted to make sure
there<00:06:12.560><c> was</c><00:06:12.639><c> a</c><00:06:12.720><c> little</c><00:06:12.880><c> bit</c><00:06:13.039><c> of</c><00:06:13.199><c> delay</c><00:06:13.520><c> so</c><00:06:13.600><c> i</c>

00:06:13.670 --> 00:06:13.680 align:start position:0%
there was a little bit of delay so i
 

00:06:13.680 --> 00:06:14.870 align:start position:0%
there was a little bit of delay so i
could

00:06:14.870 --> 00:06:14.880 align:start position:0%
could
 

00:06:14.880 --> 00:06:17.749 align:start position:0%
could
uh<00:06:15.520><c> edit</c><00:06:15.840><c> that</c><00:06:16.240><c> correctly</c>

00:06:17.749 --> 00:06:17.759 align:start position:0%
uh edit that correctly
 

00:06:17.759 --> 00:06:18.950 align:start position:0%
uh edit that correctly
and<00:06:17.840><c> then</c>

00:06:18.950 --> 00:06:18.960 align:start position:0%
and then
 

00:06:18.960 --> 00:06:21.189 align:start position:0%
and then
i<00:06:19.039><c> want</c><00:06:19.199><c> to</c><00:06:19.360><c> use</c><00:06:20.080><c> keyboard</c><00:06:20.479><c> moisture</c><00:06:21.039><c> too</c>

00:06:21.189 --> 00:06:21.199 align:start position:0%
i want to use keyboard moisture too
 

00:06:21.199 --> 00:06:23.590 align:start position:0%
i want to use keyboard moisture too
rather<00:06:21.520><c> than</c><00:06:21.680><c> just</c><00:06:22.080><c> paste</c><00:06:22.400><c> it</c><00:06:22.560><c> in</c><00:06:23.199><c> i</c><00:06:23.280><c> want</c><00:06:23.520><c> it</c>

00:06:23.590 --> 00:06:23.600 align:start position:0%
rather than just paste it in i want it
 

00:06:23.600 --> 00:06:25.189 align:start position:0%
rather than just paste it in i want it
to<00:06:23.840><c> type</c><00:06:24.080><c> it</c><00:06:24.240><c> out</c>

00:06:25.189 --> 00:06:25.199 align:start position:0%
to type it out
 

00:06:25.199 --> 00:06:27.749 align:start position:0%
to type it out
that's<00:06:25.440><c> pretty</c><00:06:25.680><c> magical</c><00:06:26.160><c> in</c><00:06:26.240><c> here</c><00:06:26.960><c> but</c><00:06:27.520><c> if</c><00:06:27.680><c> you</c>

00:06:27.749 --> 00:06:27.759 align:start position:0%
that's pretty magical in here but if you
 

00:06:27.759 --> 00:06:30.469 align:start position:0%
that's pretty magical in here but if you
just<00:06:27.919><c> type</c><00:06:28.160><c> it</c><00:06:28.319><c> out</c><00:06:28.479><c> it</c><00:06:28.800><c> types</c><00:06:29.360><c> pretty</c><00:06:30.000><c> quickly</c>

00:06:30.469 --> 00:06:30.479 align:start position:0%
just type it out it types pretty quickly
 

00:06:30.479 --> 00:06:34.150 align:start position:0%
just type it out it types pretty quickly
i<00:06:30.560><c> think</c><00:06:30.880><c> by</c><00:06:31.120><c> default</c><00:06:31.600><c> so</c><00:06:32.080><c> i</c><00:06:32.319><c> set</c><00:06:32.560><c> the</c>

00:06:34.150 --> 00:06:34.160 align:start position:0%
i think by default so i set the
 

00:06:34.160 --> 00:06:37.909 align:start position:0%
i think by default so i set the
simulate<00:06:34.639><c> normal</c><00:06:35.039><c> keystroke</c><00:06:35.520><c> delay</c><00:06:36.319><c> to</c><00:06:36.840><c> 0.07</c>

00:06:37.909 --> 00:06:37.919 align:start position:0%
simulate normal keystroke delay to 0.07
 

00:06:37.919 --> 00:06:41.350 align:start position:0%
simulate normal keystroke delay to 0.07
seconds<00:06:38.800><c> and</c><00:06:38.960><c> that</c><00:06:39.280><c> is</c><00:06:39.440><c> a</c><00:06:39.680><c> nice</c><00:06:40.160><c> flow</c><00:06:41.039><c> that</c><00:06:41.280><c> i</c>

00:06:41.350 --> 00:06:41.360 align:start position:0%
seconds and that is a nice flow that i
 

00:06:41.360 --> 00:06:43.990 align:start position:0%
seconds and that is a nice flow that i
can<00:06:41.520><c> make</c><00:06:41.840><c> faster</c><00:06:42.319><c> or</c><00:06:42.560><c> slower</c><00:06:43.199><c> in</c><00:06:43.360><c> final</c><00:06:43.680><c> cut</c>

00:06:43.990 --> 00:06:44.000 align:start position:0%
can make faster or slower in final cut
 

00:06:44.000 --> 00:06:46.550 align:start position:0%
can make faster or slower in final cut
but<00:06:44.560><c> it's</c><00:06:44.720><c> a</c><00:06:44.800><c> nice</c><00:06:45.120><c> good</c><00:06:45.520><c> base</c>

00:06:46.550 --> 00:06:46.560 align:start position:0%
but it's a nice good base
 

00:06:46.560 --> 00:06:49.589 align:start position:0%
but it's a nice good base
i<00:06:46.880><c> kind</c><00:06:47.199><c> of</c><00:06:47.440><c> wish</c><00:06:47.919><c> i</c><00:06:48.160><c> had</c>

00:06:49.589 --> 00:06:49.599 align:start position:0%
i kind of wish i had
 

00:06:49.599 --> 00:06:51.909 align:start position:0%
i kind of wish i had
um

00:06:51.909 --> 00:06:51.919 align:start position:0%
um
 

00:06:51.919 --> 00:06:53.589 align:start position:0%
um
like<00:06:52.160><c> a</c><00:06:52.400><c> variable</c>

00:06:53.589 --> 00:06:53.599 align:start position:0%
like a variable
 

00:06:53.599 --> 00:06:55.749 align:start position:0%
like a variable
a<00:06:53.759><c> variable</c><00:06:54.560><c> number</c><00:06:54.880><c> of</c><00:06:54.960><c> seconds</c><00:06:55.360><c> you</c><00:06:55.440><c> know</c><00:06:55.599><c> so</c>

00:06:55.749 --> 00:06:55.759 align:start position:0%
a variable number of seconds you know so
 

00:06:55.759 --> 00:06:57.589 align:start position:0%
a variable number of seconds you know so
i<00:06:55.840><c> could</c><00:06:56.080><c> simulate</c>

00:06:57.589 --> 00:06:57.599 align:start position:0%
i could simulate
 

00:06:57.599 --> 00:06:59.110 align:start position:0%
i could simulate
real<00:06:57.840><c> life</c><00:06:58.080><c> typing</c><00:06:58.400><c> you</c><00:06:58.560><c> know</c><00:06:58.639><c> it's</c><00:06:58.720><c> sometimes</c>

00:06:59.110 --> 00:06:59.120 align:start position:0%
real life typing you know it's sometimes
 

00:06:59.120 --> 00:07:00.390 align:start position:0%
real life typing you know it's sometimes
a little<00:06:59.280><c> bit</c><00:06:59.440><c> faster</c><00:06:59.840><c> sometimes</c><00:07:00.240><c> a</c><00:07:00.240><c> little</c>

00:07:00.390 --> 00:07:00.400 align:start position:0%
a little bit faster sometimes a little
 

00:07:00.400 --> 00:07:01.510 align:start position:0%
a little bit faster sometimes a little
bit<00:07:00.560><c> slower</c>

00:07:01.510 --> 00:07:01.520 align:start position:0%
bit slower
 

00:07:01.520 --> 00:07:03.189 align:start position:0%
bit slower
i<00:07:01.680><c> do</c><00:07:01.840><c> wish</c><00:07:02.080><c> i</c><00:07:02.160><c> could</c><00:07:02.319><c> do</c><00:07:02.479><c> that</c><00:07:02.639><c> and</c><00:07:02.720><c> maybe</c><00:07:03.039><c> i</c>

00:07:03.189 --> 00:07:03.199 align:start position:0%
i do wish i could do that and maybe i
 

00:07:03.199 --> 00:07:06.710 align:start position:0%
i do wish i could do that and maybe i
can

00:07:06.710 --> 00:07:06.720 align:start position:0%
 
 

00:07:06.720 --> 00:07:09.670 align:start position:0%
 
and<00:07:06.960><c> then</c><00:07:07.759><c> it's</c><00:07:08.080><c> going</c><00:07:08.400><c> to</c><00:07:09.039><c> go</c><00:07:09.199><c> ahead</c><00:07:09.520><c> and</c>

00:07:09.670 --> 00:07:09.680 align:start position:0%
and then it's going to go ahead and
 

00:07:09.680 --> 00:07:11.589 align:start position:0%
and then it's going to go ahead and
insert<00:07:10.080><c> text</c><00:07:10.400><c> by</c><00:07:10.560><c> typing</c>

00:07:11.589 --> 00:07:11.599 align:start position:0%
insert text by typing
 

00:07:11.599 --> 00:07:13.189 align:start position:0%
insert text by typing
so<00:07:12.000><c> that's</c><00:07:12.240><c> pretty</c><00:07:12.400><c> cool</c>

00:07:13.189 --> 00:07:13.199 align:start position:0%
so that's pretty cool
 

00:07:13.199 --> 00:07:15.189 align:start position:0%
so that's pretty cool
so<00:07:13.440><c> let's</c><00:07:13.680><c> see</c><00:07:13.840><c> this</c><00:07:14.080><c> in</c><00:07:14.240><c> action</c>

00:07:15.189 --> 00:07:15.199 align:start position:0%
so let's see this in action
 

00:07:15.199 --> 00:07:16.710 align:start position:0%
so let's see this in action
if<00:07:15.520><c> i</c>

00:07:16.710 --> 00:07:16.720 align:start position:0%
if i
 

00:07:16.720 --> 00:07:20.710 align:start position:0%
if i
get<00:07:16.880><c> rid</c><00:07:17.039><c> of</c><00:07:17.199><c> this</c>

00:07:20.710 --> 00:07:20.720 align:start position:0%
 
 

00:07:20.720 --> 00:07:22.710 align:start position:0%
 
and<00:07:20.880><c> i</c><00:07:21.120><c> press</c><00:07:21.680><c> um</c>

00:07:22.710 --> 00:07:22.720 align:start position:0%
and i press um
 

00:07:22.720 --> 00:07:24.469 align:start position:0%
and i press um
let's<00:07:22.960><c> see</c><00:07:23.120><c> is</c><00:07:23.280><c> it</c><00:07:23.599><c> yeah</c>

00:07:24.469 --> 00:07:24.479 align:start position:0%
let's see is it yeah
 

00:07:24.479 --> 00:07:26.230 align:start position:0%
let's see is it yeah
if<00:07:24.639><c> i</c><00:07:24.800><c> press</c><00:07:25.440><c> this</c>

00:07:26.230 --> 00:07:26.240 align:start position:0%
if i press this
 

00:07:26.240 --> 00:07:28.390 align:start position:0%
if i press this
it<00:07:26.400><c> switches</c><00:07:26.880><c> over</c><00:07:27.440><c> and</c><00:07:27.680><c> it</c><00:07:27.759><c> types</c><00:07:28.240><c> the</c>

00:07:28.390 --> 00:07:28.400 align:start position:0%
it switches over and it types the
 

00:07:28.400 --> 00:07:31.270 align:start position:0%
it switches over and it types the
command<00:07:29.280><c> just</c><00:07:29.919><c> perfectly</c>

00:07:31.270 --> 00:07:31.280 align:start position:0%
command just perfectly
 

00:07:31.280 --> 00:07:33.110 align:start position:0%
command just perfectly
and<00:07:31.440><c> now</c><00:07:31.599><c> i</c><00:07:31.680><c> can</c><00:07:31.840><c> press</c><00:07:32.160><c> enter</c>

00:07:33.110 --> 00:07:33.120 align:start position:0%
and now i can press enter
 

00:07:33.120 --> 00:07:34.790 align:start position:0%
and now i can press enter
it's<00:07:33.440><c> i</c><00:07:33.599><c> think</c><00:07:33.759><c> it's</c><00:07:33.919><c> already</c><00:07:34.240><c> installed</c><00:07:34.639><c> so</c>

00:07:34.790 --> 00:07:34.800 align:start position:0%
it's i think it's already installed so
 

00:07:34.800 --> 00:07:37.749 align:start position:0%
it's i think it's already installed so
it's<00:07:35.520><c> going</c><00:07:35.680><c> to</c><00:07:35.759><c> complain</c>

00:07:37.749 --> 00:07:37.759 align:start position:0%
it's going to complain
 

00:07:37.759 --> 00:07:40.550 align:start position:0%
it's going to complain
and<00:07:37.840><c> then</c><00:07:38.080><c> i</c><00:07:38.160><c> can</c><00:07:38.319><c> come</c><00:07:38.560><c> over</c><00:07:38.800><c> i</c><00:07:39.039><c> or</c><00:07:39.680><c> ls</c><00:07:40.160><c> that's</c>

00:07:40.550 --> 00:07:40.560 align:start position:0%
and then i can come over i or ls that's
 

00:07:40.560 --> 00:07:42.390 align:start position:0%
and then i can come over i or ls that's
quick<00:07:40.800><c> enough</c><00:07:40.960><c> to</c><00:07:41.120><c> type</c><00:07:41.280><c> so</c><00:07:41.360><c> i</c><00:07:41.440><c> can</c><00:07:41.599><c> type</c><00:07:41.840><c> in</c><00:07:42.000><c> ls</c>

00:07:42.390 --> 00:07:42.400 align:start position:0%
quick enough to type so i can type in ls
 

00:07:42.400 --> 00:07:44.550 align:start position:0%
quick enough to type so i can type in ls
okay<00:07:42.639><c> so</c><00:07:42.800><c> those</c><00:07:43.039><c> are</c><00:07:43.120><c> the</c><00:07:43.199><c> files</c><00:07:43.520><c> in</c><00:07:43.599><c> there</c>

00:07:44.550 --> 00:07:44.560 align:start position:0%
okay so those are the files in there
 

00:07:44.560 --> 00:07:46.390 align:start position:0%
okay so those are the files in there
and<00:07:44.720><c> then</c><00:07:44.960><c> i</c><00:07:45.039><c> can</c><00:07:45.440><c> copy</c>

00:07:46.390 --> 00:07:46.400 align:start position:0%
and then i can copy
 

00:07:46.400 --> 00:07:47.350 align:start position:0%
and then i can copy
that

00:07:47.350 --> 00:07:47.360 align:start position:0%
that
 

00:07:47.360 --> 00:07:49.589 align:start position:0%
that
so<00:07:47.680><c> again</c><00:07:48.000><c> all</c><00:07:48.240><c> i</c><00:07:48.400><c> have</c><00:07:48.479><c> to</c><00:07:48.560><c> do</c><00:07:48.720><c> is</c><00:07:48.879><c> select</c><00:07:49.280><c> it</c>

00:07:49.589 --> 00:07:49.599 align:start position:0%
so again all i have to do is select it
 

00:07:49.599 --> 00:07:52.469 align:start position:0%
so again all i have to do is select it
and<00:07:49.680><c> then</c><00:07:49.919><c> press</c><00:07:50.720><c> hyper-v</c><00:07:51.759><c> and</c><00:07:51.919><c> it</c><00:07:52.080><c> types</c><00:07:52.319><c> it</c>

00:07:52.469 --> 00:07:52.479 align:start position:0%
and then press hyper-v and it types it
 

00:07:52.479 --> 00:07:53.350 align:start position:0%
and then press hyper-v and it types it
out

00:07:53.350 --> 00:07:53.360 align:start position:0%
out
 

00:07:53.360 --> 00:07:54.469 align:start position:0%
out
enter

00:07:54.469 --> 00:07:54.479 align:start position:0%
enter
 

00:07:54.479 --> 00:07:55.589 align:start position:0%
enter
and<00:07:54.720><c> now</c>

00:07:55.589 --> 00:07:55.599 align:start position:0%
and now
 

00:07:55.599 --> 00:07:57.189 align:start position:0%
and now
you<00:07:55.680><c> know</c><00:07:55.919><c> after</c><00:07:56.160><c> i've</c><00:07:56.319><c> got</c><00:07:56.479><c> that</c><00:07:56.639><c> recording</c>

00:07:57.189 --> 00:07:57.199 align:start position:0%
you know after i've got that recording
 

00:07:57.199 --> 00:07:59.350 align:start position:0%
you know after i've got that recording
now<00:07:57.360><c> i</c><00:07:57.440><c> can</c><00:07:57.680><c> easily</c><00:07:58.160><c> bring</c><00:07:58.319><c> that</c><00:07:58.560><c> over</c><00:07:58.879><c> to</c>

00:07:59.350 --> 00:07:59.360 align:start position:0%
now i can easily bring that over to
 

00:07:59.360 --> 00:08:01.909 align:start position:0%
now i can easily bring that over to
final<00:07:59.680><c> cut</c><00:08:00.400><c> and</c><00:08:00.560><c> yes</c><00:08:00.800><c> i</c><00:08:00.960><c> have</c><00:08:01.039><c> to</c><00:08:01.280><c> edit</c><00:08:01.599><c> out</c><00:08:01.759><c> a</c>

00:08:01.909 --> 00:08:01.919 align:start position:0%
final cut and yes i have to edit out a
 

00:08:01.919 --> 00:08:04.629 align:start position:0%
final cut and yes i have to edit out a
section<00:08:02.639><c> um</c><00:08:03.120><c> with</c><00:08:03.280><c> each</c><00:08:03.520><c> command</c><00:08:04.000><c> where</c><00:08:04.240><c> the</c>

00:08:04.629 --> 00:08:04.639 align:start position:0%
section um with each command where the
 

00:08:04.639 --> 00:08:06.790 align:start position:0%
section um with each command where the
the<00:08:05.199><c> terminal</c><00:08:05.599><c> goes</c><00:08:05.840><c> black</c>

00:08:06.790 --> 00:08:06.800 align:start position:0%
the terminal goes black
 

00:08:06.800 --> 00:08:07.990 align:start position:0%
the terminal goes black
but

00:08:07.990 --> 00:08:08.000 align:start position:0%
but
 

00:08:08.000 --> 00:08:10.830 align:start position:0%
but
one<00:08:08.240><c> nice</c><00:08:08.479><c> thing</c><00:08:08.720><c> about</c><00:08:09.039><c> that</c><00:08:09.360><c> is</c><00:08:09.759><c> since</c><00:08:10.000><c> i</c><00:08:10.080><c> can</c>

00:08:10.830 --> 00:08:10.840 align:start position:0%
one nice thing about that is since i can
 

00:08:10.840 --> 00:08:16.070 align:start position:0%
one nice thing about that is since i can
see<00:08:11.919><c> you</c><00:08:12.080><c> know</c><00:08:12.240><c> if</c><00:08:12.400><c> i</c><00:08:12.800><c> zoom</c><00:08:13.039><c> in</c><00:08:13.199><c> enough</c><00:08:13.919><c> i</c><00:08:14.240><c> can</c>

00:08:16.070 --> 00:08:16.080 align:start position:0%
see you know if i zoom in enough i can
 

00:08:16.080 --> 00:08:18.230 align:start position:0%
see you know if i zoom in enough i can
see<00:08:16.319><c> that</c><00:08:16.560><c> visually</c><00:08:17.199><c> that</c><00:08:17.520><c> the</c><00:08:17.680><c> terminal</c><00:08:18.080><c> is</c>

00:08:18.230 --> 00:08:18.240 align:start position:0%
see that visually that the terminal is
 

00:08:18.240 --> 00:08:19.909 align:start position:0%
see that visually that the terminal is
black<00:08:18.479><c> so</c><00:08:18.560><c> i</c><00:08:18.639><c> can</c><00:08:18.800><c> very</c><00:08:19.039><c> easily</c><00:08:19.360><c> select</c><00:08:19.680><c> what</c>

00:08:19.909 --> 00:08:19.919 align:start position:0%
black so i can very easily select what
 

00:08:19.919 --> 00:08:20.869 align:start position:0%
black so i can very easily select what
is<00:08:20.080><c> bad</c>

00:08:20.869 --> 00:08:20.879 align:start position:0%
is bad
 

00:08:20.879 --> 00:08:24.150 align:start position:0%
is bad
delete<00:08:21.280><c> that</c><00:08:21.840><c> and</c><00:08:22.000><c> now</c><00:08:22.240><c> i've</c><00:08:22.400><c> got</c>

00:08:24.150 --> 00:08:24.160 align:start position:0%
delete that and now i've got
 

00:08:24.160 --> 00:08:26.550 align:start position:0%
delete that and now i've got
just<00:08:24.479><c> all</c><00:08:24.720><c> the</c><00:08:24.800><c> things</c><00:08:25.039><c> that</c><00:08:25.199><c> i</c><00:08:25.360><c> type</c><00:08:26.080><c> and</c><00:08:26.160><c> then</c>

00:08:26.550 --> 00:08:26.560 align:start position:0%
just all the things that i type and then
 

00:08:26.560 --> 00:08:28.790 align:start position:0%
just all the things that i type and then
depending<00:08:27.199><c> on</c><00:08:28.000><c> you</c><00:08:28.160><c> know</c><00:08:28.240><c> whether</c><00:08:28.479><c> it's</c><00:08:28.639><c> a</c>

00:08:28.790 --> 00:08:28.800 align:start position:0%
depending on you know whether it's a
 

00:08:28.800 --> 00:08:30.309 align:start position:0%
depending on you know whether it's a
really<00:08:29.039><c> long</c><00:08:29.280><c> command</c><00:08:29.680><c> or</c><00:08:29.759><c> a</c><00:08:29.840><c> really</c><00:08:30.080><c> short</c>

00:08:30.309 --> 00:08:30.319 align:start position:0%
really long command or a really short
 

00:08:30.319 --> 00:08:31.430 align:start position:0%
really long command or a really short
command

00:08:31.430 --> 00:08:31.440 align:start position:0%
command
 

00:08:31.440 --> 00:08:35.670 align:start position:0%
command
i<00:08:31.680><c> can</c><00:08:32.479><c> um</c><00:08:32.959><c> speed</c><00:08:33.279><c> it</c><00:08:33.440><c> up</c><00:08:34.000><c> or</c><00:08:34.240><c> slow</c><00:08:34.560><c> it</c><00:08:34.640><c> down</c><00:08:35.360><c> uh</c>

00:08:35.670 --> 00:08:35.680 align:start position:0%
i can um speed it up or slow it down uh
 

00:08:35.680 --> 00:08:40.149 align:start position:0%
i can um speed it up or slow it down uh
using<00:08:36.000><c> the</c><00:08:36.719><c> um</c><00:08:37.279><c> time</c>

00:08:40.149 --> 00:08:40.159 align:start position:0%
using the um time
 

00:08:40.159 --> 00:08:42.310 align:start position:0%
using the um time
you<00:08:40.320><c> can</c><00:08:40.560><c> reset</c><00:08:40.959><c> the</c><00:08:41.120><c> time</c><00:08:41.519><c> in</c><00:08:41.680><c> the</c><00:08:41.839><c> uh</c><00:08:42.159><c> in</c>

00:08:42.310 --> 00:08:42.320 align:start position:0%
you can reset the time in the uh in
 

00:08:42.320 --> 00:08:45.030 align:start position:0%
you can reset the time in the uh in
final<00:08:42.560><c> cut</c><00:08:42.959><c> so</c><00:08:43.120><c> i</c><00:08:43.200><c> make</c><00:08:43.440><c> it</c><00:08:43.599><c> faster</c><00:08:44.080><c> or</c><00:08:44.240><c> slower</c>

00:08:45.030 --> 00:08:45.040 align:start position:0%
final cut so i make it faster or slower
 

00:08:45.040 --> 00:08:47.030 align:start position:0%
final cut so i make it faster or slower
depending<00:08:45.360><c> on</c><00:08:45.440><c> what</c><00:08:45.600><c> i</c><00:08:45.680><c> want</c><00:08:45.839><c> to</c><00:08:46.000><c> show</c>

00:08:47.030 --> 00:08:47.040 align:start position:0%
depending on what i want to show
 

00:08:47.040 --> 00:08:48.870 align:start position:0%
depending on what i want to show
so<00:08:47.200><c> i</c><00:08:47.360><c> think</c><00:08:47.519><c> that's</c><00:08:47.839><c> really</c><00:08:48.160><c> cool</c><00:08:48.480><c> i'm</c><00:08:48.720><c> going</c>

00:08:48.870 --> 00:08:48.880 align:start position:0%
so i think that's really cool i'm going
 

00:08:48.880 --> 00:08:51.269 align:start position:0%
so i think that's really cool i'm going
to<00:08:49.040><c> be</c><00:08:49.200><c> using</c><00:08:49.519><c> this</c><00:08:49.920><c> in</c><00:08:50.240><c> all</c><00:08:50.480><c> my</c><00:08:50.640><c> videos</c><00:08:50.959><c> going</c>

00:08:51.269 --> 00:08:51.279 align:start position:0%
to be using this in all my videos going
 

00:08:51.279 --> 00:08:52.710 align:start position:0%
to be using this in all my videos going
forward

00:08:52.710 --> 00:08:52.720 align:start position:0%
forward
 

00:08:52.720 --> 00:08:55.110 align:start position:0%
forward
it<00:08:52.880><c> just</c><00:08:53.040><c> makes</c><00:08:53.279><c> things</c><00:08:53.519><c> super</c><00:08:53.920><c> easy</c><00:08:54.480><c> and</c><00:08:54.880><c> and</c>

00:08:55.110 --> 00:08:55.120 align:start position:0%
it just makes things super easy and and
 

00:08:55.120 --> 00:08:57.590 align:start position:0%
it just makes things super easy and and
fun<00:08:55.680><c> so</c><00:08:56.000><c> i</c><00:08:56.080><c> thought</c><00:08:56.320><c> hopefully</c><00:08:56.959><c> you</c><00:08:57.120><c> find</c><00:08:57.360><c> this</c>

00:08:57.590 --> 00:08:57.600 align:start position:0%
fun so i thought hopefully you find this
 

00:08:57.600 --> 00:08:59.509 align:start position:0%
fun so i thought hopefully you find this
interesting<00:08:58.160><c> uh</c><00:08:58.480><c> it's</c><00:08:58.800><c> i</c><00:08:58.880><c> think</c><00:08:59.120><c> it's</c><00:08:59.279><c> really</c>

00:08:59.509 --> 00:08:59.519 align:start position:0%
interesting uh it's i think it's really
 

00:08:59.519 --> 00:09:01.990 align:start position:0%
interesting uh it's i think it's really
cool<00:09:00.000><c> uh</c><00:09:00.320><c> way</c><00:09:00.640><c> of</c><00:09:00.880><c> working</c>

00:09:01.990 --> 00:09:02.000 align:start position:0%
cool uh way of working
 

00:09:02.000 --> 00:09:02.949 align:start position:0%
cool uh way of working
and

00:09:02.949 --> 00:09:02.959 align:start position:0%
and
 

00:09:02.959 --> 00:09:04.230 align:start position:0%
and
if<00:09:03.040><c> you</c><00:09:03.120><c> have</c><00:09:03.200><c> any</c><00:09:03.360><c> questions</c><00:09:03.760><c> leave</c><00:09:03.920><c> them</c><00:09:04.160><c> in</c>

00:09:04.230 --> 00:09:04.240 align:start position:0%
if you have any questions leave them in
 

00:09:04.240 --> 00:09:06.230 align:start position:0%
if you have any questions leave them in
the<00:09:04.320><c> comments</c><00:09:04.720><c> below</c><00:09:05.360><c> and</c><00:09:05.600><c> thanks</c><00:09:05.839><c> so</c><00:09:06.000><c> much</c>

00:09:06.230 --> 00:09:06.240 align:start position:0%
the comments below and thanks so much
 

00:09:06.240 --> 00:09:07.670 align:start position:0%
the comments below and thanks so much
for<00:09:06.560><c> watching</c>

00:09:07.670 --> 00:09:07.680 align:start position:0%
for watching
 

00:09:07.680 --> 00:09:24.550 align:start position:0%
for watching
bye

00:09:24.550 --> 00:09:24.560 align:start position:0%
 
 

00:09:24.560 --> 00:09:26.640 align:start position:0%
 
you

