"""
Utilities for managing Google Gemini API tokens, costs, and budgets.
"""

import logging
import sys
from typing import Dict, Optional, Union
from datetime import date

import google.genai as genai
import asyncpg
from asyncpg.pool import Pool
from typing import Tuple

# Import the new pricing models
from .pricing_models import (
    ModelPricingInfo, CostCalculationRequest, CostCalculationResult,
    ContentType, OutputType, PricingModel
)

# Configure standard logging
logger = logging.getLogger(__name__)
# Ensure handler is added only once if module is reloaded
if not logger.handlers:
    handler = logging.StreamHandler(sys.stderr)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.DEBUG) # Adjust level as needed

class TokenManager:
    """
    Token management for Gemini API.

    This class handles token counting, rate limiting, and budget tracking
    for Gemini API calls.
    """

    def __init__(
        self,
        client: genai.Client,  # The google-genai client instance
        model_name: str,  # Name of the model being used (e.g., 'gemini-1.5-pro')
        token_cost_input: Optional[float] = None, # Cost per 1 million input tokens
        token_cost_output: Optional[float] = None, # Cost per 1 million output tokens
        daily_budget: float = 1.00,  # Example daily budget in USD
    ):
        """
        Initialize the token manager.

        Args:
            client: The google-genai client instance
            model_name: Name of the model being used
            token_cost_input: Cost per 1,000,000 input tokens in USD (same scale as database).
            token_cost_output: Cost per 1,000,000 output tokens in USD (same scale as database).
            daily_budget: Daily budget in USD
        """
        self.client = client
        self.model_name = model_name
        self.daily_budget = daily_budget

        # Store actual costs, defaulting to 0.0 if not provided, with a warning
        if token_cost_input is None:
            logger.warning(f"TokenManager for {model_name}: token_cost_input not provided, defaulting to 0.0. Cost calculations may be inaccurate.")
            self.token_cost_input = 0.0
        else:
            self.token_cost_input = token_cost_input

        if token_cost_output is None:
            logger.warning(f"TokenManager for {model_name}: token_cost_output not provided, defaulting to 0.0. Cost calculations may be inaccurate.")
            self.token_cost_output = 0.0
        else:
            self.token_cost_output = token_cost_output

        # Token usage tracking
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_cost = 0.0
        self.last_request_time = 0 # Consider using this for rate limiting if needed

        # Reset daily tracking
        self.last_reset_date = date.today()
        self.daily_input_tokens = 0
        self.daily_output_tokens = 0
        self.daily_cost = 0.0

        logger.info(f"Token manager initialized for model {model_name}")

    def reset_daily_tracking(self):
        """Reset daily token tracking"""
        # Check if it's already a new day
        today = date.today()
        if today > self.last_reset_date:
            self.daily_input_tokens = 0
            self.daily_output_tokens = 0
            self.daily_cost = 0.0
            self.last_reset_date = today
            logger.info(f"Daily token/cost tracking reset for {today}.")
        else:
            logger.debug("Daily budget reset check: Not a new day yet.")

    def check_daily_budget(self) -> bool:
        """
        Check if daily budget has been exceeded after potentially resetting.

        Returns:
            bool: True if budget is OK, False if exceeded
        """
        self.reset_daily_tracking()  # Ensure tracking is up-to-date for the day
        within_budget = self.daily_cost < self.daily_budget
        if not within_budget:
            logger.warning(
                f"Daily budget exceeded! Current cost: ${self.daily_cost:.4f}, Budget: ${self.daily_budget:.4f}"
            )
        return within_budget

    def get_usage_stats(self) -> Dict[str, Union[str, int, float]]:
        """Get current usage statistics

        Returns:
            Dict: Usage statistics including daily and total figures.
        """
        self.reset_daily_tracking() # Ensure daily stats are current before reporting
        return {
            "model": self.model_name,
            "token_cost_input_per_million": self.token_cost_input,
            "token_cost_output_per_million": self.token_cost_output,
            "total_input_tokens": self.total_input_tokens,
            "total_output_tokens": self.total_output_tokens,
            "total_cost_usd": round(self.total_cost, 6),
            "daily_input_tokens": self.daily_input_tokens,
            "daily_output_tokens": self.daily_output_tokens,
            "daily_cost_usd": round(self.daily_cost, 6),
            "daily_budget_usd": self.daily_budget,
            "budget_remaining_usd": round(self.daily_budget - self.daily_cost, 6),
            "daily_tracking_reset_date": self.last_reset_date.isoformat(),
        }

    def update_usage_and_cost(
        self,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None
    ) -> Dict[str, Union[int, float]]:
        """Update token usage and cost tracking based on provided counts.
        Returns the details of the cost calculation for this specific update.

        Args:
            input_tokens: Number of input tokens used in a call.
            output_tokens: Number of output tokens generated in a call.

        Returns:
            Dict: {'input_tokens': int, 'output_tokens': int, 'calculated_cost': float}
        """
        self.reset_daily_tracking() # Ensure cost is added to the correct day

        cost = 0.0
        input_tokens_int = input_tokens if input_tokens is not None else 0
        output_tokens_int = output_tokens if output_tokens is not None else 0

        # Update totals
        self.total_input_tokens += input_tokens_int
        self.total_output_tokens += output_tokens_int
        self.daily_input_tokens += input_tokens_int
        self.daily_output_tokens += output_tokens_int

        # Calculate cost based on provided counts (harmonized with database scale)
        try:
            input_cost = (input_tokens_int / 1_000_000.0) * self.token_cost_input
            output_cost = (output_tokens_int / 1_000_000.0) * self.token_cost_output
            cost = input_cost + output_cost
            self.total_cost += cost
            self.daily_cost += cost
            logger.debug(f"Updated usage: Input={input_tokens_int}, Output={output_tokens_int}, Cost=${cost:.6f}. Daily Cost: ${self.daily_cost:.6f}")
        except Exception as e:
             logger.error(f"Error calculating cost: {e}", exc_info=True)

        return {
            "input_tokens": input_tokens_int,
            "output_tokens": output_tokens_int,
            "calculated_cost": round(cost, 6) # Return calculated cost for this call
        }

# --- Standalone Cost Calculation Utilities ---

async def fetch_model_costs(
    model_name: str,
    db_pool: Pool
) -> Tuple[Optional[float], Optional[float]]:
    """
    Fetches the input and output token costs for a given model
    from the workflow_settings.available_models table.

    Args:
        model_name: The name of the model (e.g., 'gemini-1.5-flash-latest').
        db_pool: The asyncpg database connection pool.

    Returns:
        A tuple containing (token_cost_input_per_million, token_cost_output_per_million).
        Returns (None, None) if the model is not found or on error.
    """
    query = """
    SELECT token_cost_input, token_cost_output
    FROM workflow_settings.available_models
    WHERE model_name = $1;
    """
    try:
        async with db_pool.acquire() as conn:
            result = await conn.fetchrow(query, model_name)
            if result:
                cost_input = result.get("token_cost_input")
                cost_output = result.get("token_cost_output")
                # Convert from Decimal to float if necessary, handle None
                cost_input_float = float(cost_input) if cost_input is not None else None
                cost_output_float = float(cost_output) if cost_output is not None else None
                return cost_input_float, cost_output_float
            else:
                logger.warning(f"No cost information found for model '{model_name}' in available_models table.")
                return None, None
    except (asyncpg.PostgresError, OSError) as e:
        logger.error(f"Database error fetching costs for model '{model_name}': {e}", exc_info=True)
        return None, None
    except Exception as e:
        logger.error(f"Unexpected error fetching costs for model '{model_name}': {e}", exc_info=True)
        return None, None


async def calculate_api_cost(
    model_name: str,
    input_tokens: Optional[int],
    output_tokens: Optional[int],
    db_pool: Pool
) -> Optional[float]:
    """
    Calculates the estimated cost of a Gemini API call based on token counts
    and costs fetched from the database.

    Args:
        model_name: The name of the model used.
        input_tokens: The number of input tokens.
        output_tokens: The number of output tokens.
        db_pool: The asyncpg database connection pool.

    Returns:
        The calculated cost in USD, or None if costs couldn't be determined.
    """
    input_tokens_int = input_tokens or 0
    output_tokens_int = output_tokens or 0

    if input_tokens_int == 0 and output_tokens_int == 0:
        return 0.0 # No cost if no tokens used

    cost_input_per_million, cost_output_per_million = await fetch_model_costs(model_name, db_pool)

    if cost_input_per_million is None or cost_output_per_million is None:
        logger.warning(f"Could not calculate cost for model '{model_name}' due to missing DB cost info.")
        return None

    try:
        # Database stores costs per million tokens, so divide tokens by 1 million
        input_cost = (input_tokens_int / 1_000_000.0) * cost_input_per_million
        output_cost = (output_tokens_int / 1_000_000.0) * cost_output_per_million
        total_cost = input_cost + output_cost
        logger.debug(f"Calculated cost for {model_name} (In={input_tokens_int}, Out={output_tokens_int}): ${total_cost:.6f}")
        return round(total_cost, 6) # Round to 6 decimal places
    except Exception as e:
        logger.error(f"Error during cost calculation for model '{model_name}': {e}", exc_info=True)
        return None


async def model_supports_caching(model_name: str, db_pool: Pool) -> bool:
    """
    Determines if a model supports token caching by checking the database.
    
    Args:
        model_name: The name of the model to check
        db_pool: Database connection pool
        
    Returns:
        Boolean indicating whether the model supports caching
    """
    query = """
    SELECT supports_caching 
    FROM workflow_settings.available_models 
    WHERE model_name = $1
    """
    
    try:
        async with db_pool.acquire() as conn:
            row = await conn.fetchrow(query, model_name)
            if row and 'supports_caching' in row:
                return row['supports_caching']
            # Fallback for specific models if DB information not available
            # This is a safety measure in case DB hasn't been updated yet
            known_caching_models = ['gemini-2.5-pro-exp-03-25']
            return model_name in known_caching_models
    except Exception as e:
        logger.warning(f"Error checking caching support for model {model_name}: {e}")
        # Fallback to specific models if DB query fails
        known_caching_models = ['gemini-2.5-pro-exp-03-25']
        return model_name in known_caching_models

async def create_token_manager_from_db(
    client: genai.Client,
    model_name: str,
    db_pool: Pool,
    daily_budget: float = 1.00
) -> TokenManager:
    """
    Creates a TokenManager instance with costs fetched from the database.
    This ensures consistency between TokenManager and the database costs.
    
    Args:
        client: The google-genai client instance
        model_name: Name of the model being used
        db_pool: Database connection pool
        daily_budget: Daily budget in USD
        
    Returns:
        TokenManager instance with database-consistent costs
    """
    cost_input_per_million, cost_output_per_million = await fetch_model_costs(model_name, db_pool)
    
    return TokenManager(
        client=client,
        model_name=model_name,
        token_cost_input=cost_input_per_million,
        token_cost_output=cost_output_per_million,
        daily_budget=daily_budget
    )

# --- Advanced Cost Calculation Utilities ---

async def fetch_detailed_model_pricing(
    model_name: str,
    db_pool: Pool
) -> Optional[ModelPricingInfo]:
    """
    Fetches complete pricing information for a model from the database.
    
    Args:
        model_name: The name of the model
        db_pool: Database connection pool
        
    Returns:
        ModelPricingInfo object with all pricing details, or None if not found
    """
    query = """
    SELECT 
        model_name, pricing_model,
        input_cost_text_image_video, input_cost_audio,
        output_cost_non_thinking, output_cost_thinking,
        input_cost_small_prompt, input_cost_large_prompt,
        output_cost_small_prompt, output_cost_large_prompt,
        context_caching_cost_text_image_video, context_caching_cost_audio,
        context_caching_cost_small_prompt, context_caching_cost_large_prompt,
        context_caching_cost_per_hour,
        grounding_free_requests_per_day, grounding_cost_per_1000_requests,
        prompt_size_threshold,
        supports_caching, supports_thinking, supports_search_grounding
    FROM workflow_settings.available_models
    WHERE model_name = $1 AND is_enabled = true;
    """
    
    try:
        async with db_pool.acquire() as conn:
            result = await conn.fetchrow(query, model_name)
            if result:
                return ModelPricingInfo(
                    model_name=result['model_name'],
                    pricing_model=PricingModel(result['pricing_model']),
                    input_cost_text_image_video=float(result['input_cost_text_image_video'] or 0),
                    input_cost_audio=float(result['input_cost_audio'] or 0),
                    output_cost_non_thinking=float(result['output_cost_non_thinking'] or 0),
                    output_cost_thinking=float(result['output_cost_thinking'] or 0),
                    input_cost_small_prompt=float(result['input_cost_small_prompt'] or 0),
                    input_cost_large_prompt=float(result['input_cost_large_prompt'] or 0),
                    output_cost_small_prompt=float(result['output_cost_small_prompt'] or 0),
                    output_cost_large_prompt=float(result['output_cost_large_prompt'] or 0),
                    context_caching_cost_text_image_video=float(result['context_caching_cost_text_image_video'] or 0),
                    context_caching_cost_audio=float(result['context_caching_cost_audio'] or 0),
                    context_caching_cost_small_prompt=float(result['context_caching_cost_small_prompt'] or 0),
                    context_caching_cost_large_prompt=float(result['context_caching_cost_large_prompt'] or 0),
                    context_caching_cost_per_hour=float(result['context_caching_cost_per_hour'] or 0),
                    grounding_free_requests_per_day=result['grounding_free_requests_per_day'] or 1500,
                    grounding_cost_per_1000_requests=float(result['grounding_cost_per_1000_requests'] or 35.0),
                    prompt_size_threshold=result['prompt_size_threshold'] or 200000,
                    supports_caching=result['supports_caching'] or False,
                    supports_thinking=result['supports_thinking'] or False,
                    supports_grounding=result['supports_search_grounding'] or False
                )
            else:
                logger.warning(f"No pricing information found for model '{model_name}' in available_models table.")
                return None
    except Exception as e:
        logger.error(f"Error fetching detailed pricing for model '{model_name}': {e}", exc_info=True)
        return None


async def calculate_advanced_api_cost(
    request: CostCalculationRequest,
    db_pool: Pool
) -> Optional[CostCalculationResult]:
    """
    Advanced cost calculation that handles all Gemini pricing complexities.
    
    Args:
        request: CostCalculationRequest with all parameters
        db_pool: Database connection pool
        
    Returns:
        CostCalculationResult with detailed cost breakdown
    """
    # Fetch detailed pricing information
    pricing_info = await fetch_detailed_model_pricing(request.model_name, db_pool)
    if not pricing_info:
        logger.warning(f"Could not fetch pricing info for model '{request.model_name}'")
        return None
    
    try:
        # Calculate input cost
        input_rate = pricing_info.get_input_cost(request.content_type, request.input_tokens)
        input_cost = (request.input_tokens / 1_000_000.0) * input_rate
        
        # Calculate output cost 
        output_rate = pricing_info.get_output_cost(request.output_type, request.input_tokens)
        output_cost = (request.output_tokens / 1_000_000.0) * output_rate
        
        # Calculate caching cost
        caching_cost = 0.0
        caching_rate = None
        if request.use_context_caching and request.cached_tokens > 0:
            caching_rate = pricing_info.get_caching_cost(request.content_type, request.input_tokens)
            # Context caching cost = tokens cached × rate + storage cost per hour
            token_caching_cost = (request.cached_tokens / 1_000_000.0) * caching_rate
            storage_cost = (request.cached_tokens / 1_000_000.0) * pricing_info.context_caching_cost_per_hour * request.cache_duration_hours
            caching_cost = token_caching_cost + storage_cost
        
        # Calculate grounding cost
        grounding_cost = 0.0
        if request.use_grounding and request.grounding_requests > 0:
            # Subtract free requests
            billable_requests = max(0, request.grounding_requests - pricing_info.grounding_free_requests_per_day)
            if billable_requests > 0:
                grounding_cost = (billable_requests / 1000) * pricing_info.grounding_cost_per_1000_requests
        
        # Calculate total
        total_cost = input_cost + output_cost + caching_cost + grounding_cost
        
        result = CostCalculationResult(
            model_name=request.model_name,
            total_cost=round(total_cost, 8),
            input_cost=round(input_cost, 8),
            output_cost=round(output_cost, 8),
            caching_cost=round(caching_cost, 8),
            grounding_cost=round(grounding_cost, 8),
            input_tokens=request.input_tokens,
            output_tokens=request.output_tokens,
            content_type=request.content_type,
            output_type=request.output_type,
            pricing_model=pricing_info.pricing_model,
            input_rate_used=input_rate,
            output_rate_used=output_rate,
            caching_rate_used=caching_rate
        )
        
        logger.debug(f"Advanced cost calculation for {request.model_name}: ${total_cost:.8f} "
                    f"(Input: ${input_cost:.6f}, Output: ${output_cost:.6f}, "
                    f"Caching: ${caching_cost:.6f}, Grounding: ${grounding_cost:.6f})")
        
        return result
        
    except Exception as e:
        logger.error(f"Error during advanced cost calculation for model '{request.model_name}': {e}", exc_info=True)
        return None
