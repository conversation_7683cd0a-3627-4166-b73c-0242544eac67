WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.910 align:start position:0%
 
I'm<00:00:00.400><c> excited</c><00:00:00.640><c> to</c><00:00:00.880><c> announce</c><00:00:01.199><c> the</c><00:00:01.520><c> release</c><00:00:01.760><c> of</c>

00:00:01.910 --> 00:00:01.920 align:start position:0%
I'm excited to announce the release of
 

00:00:01.920 --> 00:00:04.230 align:start position:0%
I'm excited to announce the release of
our<00:00:02.159><c> latest</c><00:00:02.399><c> Langchain</c><00:00:03.040><c> Academy</c><00:00:03.439><c> course</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
our latest Langchain Academy course
 

00:00:04.240 --> 00:00:06.950 align:start position:0%
our latest Langchain Academy course
building<00:00:04.640><c> ambient</c><00:00:05.120><c> agents</c><00:00:05.600><c> with</c><00:00:05.839><c> Langraph.</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
building ambient agents with Langraph.
 

00:00:06.960 --> 00:00:09.190 align:start position:0%
building ambient agents with Langraph.
At<00:00:07.120><c> Langchain,</c><00:00:07.839><c> we</c><00:00:08.000><c> aim</c><00:00:08.160><c> to</c><00:00:08.320><c> make</c><00:00:08.480><c> it</c><00:00:08.639><c> as</c><00:00:08.880><c> easy</c>

00:00:09.190 --> 00:00:09.200 align:start position:0%
At Langchain, we aim to make it as easy
 

00:00:09.200 --> 00:00:11.030 align:start position:0%
At Langchain, we aim to make it as easy
as<00:00:09.440><c> possible</c><00:00:10.000><c> to</c><00:00:10.160><c> build</c><00:00:10.480><c> agentic</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
as possible to build agentic
 

00:00:11.040 --> 00:00:13.830 align:start position:0%
as possible to build agentic
applications.<00:00:12.480><c> Most</c><00:00:12.800><c> agents</c><00:00:13.120><c> we've</c><00:00:13.360><c> seen</c><00:00:13.599><c> so</c>

00:00:13.830 --> 00:00:13.840 align:start position:0%
applications. Most agents we've seen so
 

00:00:13.840 --> 00:00:16.550 align:start position:0%
applications. Most agents we've seen so
far<00:00:14.400><c> operate</c><00:00:14.880><c> through</c><00:00:15.280><c> chat</c><00:00:15.679><c> interfaces</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
far operate through chat interfaces
 

00:00:16.560 --> 00:00:19.269 align:start position:0%
far operate through chat interfaces
handling<00:00:17.039><c> one</c><00:00:17.359><c> interaction</c><00:00:17.840><c> at</c><00:00:18.080><c> a</c><00:00:18.240><c> time,</c><00:00:19.039><c> but</c>

00:00:19.269 --> 00:00:19.279 align:start position:0%
handling one interaction at a time, but
 

00:00:19.279 --> 00:00:21.429 align:start position:0%
handling one interaction at a time, but
this<00:00:19.520><c> is</c><00:00:19.680><c> starting</c><00:00:20.000><c> to</c><00:00:20.160><c> change</c><00:00:20.560><c> as</c><00:00:20.880><c> models</c><00:00:21.279><c> get</c>

00:00:21.429 --> 00:00:21.439 align:start position:0%
this is starting to change as models get
 

00:00:21.439 --> 00:00:24.630 align:start position:0%
this is starting to change as models get
better.<00:00:22.240><c> The</c><00:00:22.480><c> length</c><00:00:22.800><c> of</c><00:00:23.039><c> tasks</c><00:00:23.760><c> that</c><00:00:24.000><c> AI</c><00:00:24.400><c> can</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
better. The length of tasks that AI can
 

00:00:24.640 --> 00:00:28.150 align:start position:0%
better. The length of tasks that AI can
perform<00:00:25.119><c> autonomously</c><00:00:26.240><c> is</c><00:00:26.560><c> doubling</c><00:00:27.439><c> every</c><00:00:27.920><c> 7</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
perform autonomously is doubling every 7
 

00:00:28.160 --> 00:00:29.910 align:start position:0%
perform autonomously is doubling every 7
months.<00:00:28.800><c> And</c><00:00:28.960><c> this</c><00:00:29.119><c> expanding</c><00:00:29.599><c> model</c>

00:00:29.910 --> 00:00:29.920 align:start position:0%
months. And this expanding model
 

00:00:29.920 --> 00:00:31.830 align:start position:0%
months. And this expanding model
capability<00:00:30.400><c> opens</c><00:00:30.720><c> up</c><00:00:30.880><c> new</c><00:00:31.119><c> kinds</c><00:00:31.359><c> of</c><00:00:31.439><c> agent</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
capability opens up new kinds of agent
 

00:00:31.840 --> 00:00:33.990 align:start position:0%
capability opens up new kinds of agent
interactions.<00:00:33.040><c> Rather</c><00:00:33.280><c> than</c><00:00:33.520><c> handling</c>

00:00:33.990 --> 00:00:34.000 align:start position:0%
interactions. Rather than handling
 

00:00:34.000 --> 00:00:35.670 align:start position:0%
interactions. Rather than handling
single<00:00:34.399><c> user</c><00:00:34.640><c> requests</c><00:00:35.120><c> in</c><00:00:35.280><c> a</c><00:00:35.440><c> chat</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
single user requests in a chat
 

00:00:35.680 --> 00:00:37.670 align:start position:0%
single user requests in a chat
interface,<00:00:36.399><c> agents</c><00:00:36.880><c> can</c><00:00:37.120><c> now</c><00:00:37.360><c> be</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
interface, agents can now be
 

00:00:37.680 --> 00:00:39.830 align:start position:0%
interface, agents can now be
autonomously<00:00:38.399><c> triggered</c><00:00:38.800><c> by</c><00:00:38.960><c> event</c><00:00:39.280><c> streams</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
autonomously triggered by event streams
 

00:00:39.840 --> 00:00:42.470 align:start position:0%
autonomously triggered by event streams
like<00:00:40.160><c> Slack</c><00:00:40.559><c> messages</c><00:00:40.960><c> or</c><00:00:41.200><c> GitHub</c><00:00:41.600><c> issues.</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
like Slack messages or GitHub issues.
 

00:00:42.480 --> 00:00:44.229 align:start position:0%
like Slack messages or GitHub issues.
They<00:00:42.640><c> can</c><00:00:42.800><c> now</c><00:00:42.960><c> work</c><00:00:43.200><c> in</c><00:00:43.360><c> the</c><00:00:43.520><c> background</c><00:00:43.920><c> for</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
They can now work in the background for
 

00:00:44.239 --> 00:00:46.229 align:start position:0%
They can now work in the background for
long<00:00:44.480><c> periods</c><00:00:44.719><c> of</c><00:00:44.879><c> time</c><00:00:45.360><c> and</c><00:00:45.600><c> perform</c><00:00:46.000><c> many</c>

00:00:46.229 --> 00:00:46.239 align:start position:0%
long periods of time and perform many
 

00:00:46.239 --> 00:00:48.630 align:start position:0%
long periods of time and perform many
tasks<00:00:46.559><c> at</c><00:00:46.719><c> once.</c><00:00:47.360><c> Unlike</c><00:00:47.760><c> chat</c><00:00:48.079><c> agents</c><00:00:48.399><c> which</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
tasks at once. Unlike chat agents which
 

00:00:48.640 --> 00:00:50.869 align:start position:0%
tasks at once. Unlike chat agents which
need<00:00:48.800><c> to</c><00:00:48.960><c> respond</c><00:00:49.520><c> instantaneously</c><00:00:50.320><c> to</c><00:00:50.559><c> user</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
need to respond instantaneously to user
 

00:00:50.879 --> 00:00:52.950 align:start position:0%
need to respond instantaneously to user
input,<00:00:51.360><c> these</c><00:00:51.680><c> new</c><00:00:51.840><c> kinds</c><00:00:52.079><c> of</c><00:00:52.160><c> ambient</c><00:00:52.640><c> agents</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
input, these new kinds of ambient agents
 

00:00:52.960 --> 00:00:55.110 align:start position:0%
input, these new kinds of ambient agents
are<00:00:53.199><c> designed</c><00:00:53.520><c> for</c><00:00:53.680><c> longunning</c><00:00:54.640><c> complex</c>

00:00:55.110 --> 00:00:55.120 align:start position:0%
are designed for longunning complex
 

00:00:55.120 --> 00:00:57.670 align:start position:0%
are designed for longunning complex
operations.<00:00:55.600><c> Often</c><00:00:56.079><c> times</c><00:00:57.039><c> with</c><00:00:57.280><c> ambient</c>

00:00:57.670 --> 00:00:57.680 align:start position:0%
operations. Often times with ambient
 

00:00:57.680 --> 00:00:59.189 align:start position:0%
operations. Often times with ambient
agents,<00:00:58.079><c> you</c><00:00:58.239><c> can</c><00:00:58.399><c> scale</c><00:00:58.640><c> your</c><00:00:58.800><c> impact</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
agents, you can scale your impact
 

00:00:59.199 --> 00:01:01.270 align:start position:0%
agents, you can scale your impact
dramatically.<00:01:00.160><c> Thousands</c><00:01:00.719><c> can</c><00:01:00.879><c> run</c><00:01:01.039><c> in</c><00:01:01.199><c> the</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
dramatically. Thousands can run in the
 

00:01:01.280 --> 00:01:03.990 align:start position:0%
dramatically. Thousands can run in the
background.<00:01:02.559><c> Automating</c><00:01:03.039><c> and</c><00:01:03.280><c> orchestrating</c>

00:01:03.990 --> 00:01:04.000 align:start position:0%
background. Automating and orchestrating
 

00:01:04.000 --> 00:01:06.070 align:start position:0%
background. Automating and orchestrating
complex<00:01:04.400><c> workflows.</c>

00:01:06.070 --> 00:01:06.080 align:start position:0%
complex workflows.
 

00:01:06.080 --> 00:01:08.630 align:start position:0%
complex workflows.
Importantly<00:01:06.560><c> though,</c><00:01:07.280><c> ambient</c><00:01:07.920><c> doesn't</c><00:01:08.240><c> mean</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
Importantly though, ambient doesn't mean
 

00:01:08.640 --> 00:01:11.350 align:start position:0%
Importantly though, ambient doesn't mean
fully<00:01:08.960><c> autonomous.</c><00:01:10.000><c> Having</c><00:01:10.320><c> a</c><00:01:10.560><c> human</c><00:01:10.960><c> in</c><00:01:11.200><c> the</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
fully autonomous. Having a human in the
 

00:01:11.360 --> 00:01:13.670 align:start position:0%
fully autonomous. Having a human in the
loop<00:01:11.680><c> is</c><00:01:11.920><c> critical.</c><00:01:12.640><c> You</c><00:01:12.880><c> can</c><00:01:13.040><c> approve</c><00:01:13.439><c> or</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
loop is critical. You can approve or
 

00:01:13.680 --> 00:01:16.230 align:start position:0%
loop is critical. You can approve or
reject<00:01:14.080><c> actions,</c><00:01:14.720><c> edit</c><00:01:15.040><c> tool</c><00:01:15.360><c> calls,</c><00:01:15.920><c> answer</c>

00:01:16.230 --> 00:01:16.240 align:start position:0%
reject actions, edit tool calls, answer
 

00:01:16.240 --> 00:01:18.950 align:start position:0%
reject actions, edit tool calls, answer
clarifying<00:01:16.880><c> questions,</c><00:01:17.840><c> or</c><00:01:18.080><c> even</c><00:01:18.479><c> revisit</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
clarifying questions, or even revisit
 

00:01:18.960 --> 00:01:22.310 align:start position:0%
clarifying questions, or even revisit
and<00:01:19.280><c> modify</c><00:01:19.920><c> the</c><00:01:20.240><c> agent's</c><00:01:20.640><c> state.</c><00:01:21.759><c> This</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
and modify the agent's state. This
 

00:01:22.320 --> 00:01:25.109 align:start position:0%
and modify the agent's state. This
interactivity<00:01:23.280><c> helps</c><00:01:23.680><c> improve</c><00:01:24.080><c> outcomes,</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
interactivity helps improve outcomes,
 

00:01:25.119 --> 00:01:27.590 align:start position:0%
interactivity helps improve outcomes,
increases<00:01:25.680><c> trust,</c><00:01:26.320><c> and</c><00:01:26.640><c> enhances</c><00:01:27.200><c> agent</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
increases trust, and enhances agent
 

00:01:27.600 --> 00:01:30.950 align:start position:0%
increases trust, and enhances agent
memory<00:01:28.000><c> and</c><00:01:28.240><c> performance</c><00:01:28.880><c> over</c><00:01:29.200><c> time.</c><00:01:30.560><c> We've</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
memory and performance over time. We've
 

00:01:30.960 --> 00:01:32.870 align:start position:0%
memory and performance over time. We've
designed<00:01:31.520><c> Langraph,</c><00:01:32.320><c> our</c><00:01:32.560><c> agent</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
designed Langraph, our agent
 

00:01:32.880 --> 00:01:35.590 align:start position:0%
designed Langraph, our agent
orchestration<00:01:33.600><c> framework</c><00:01:34.479><c> with</c><00:01:34.720><c> these</c><00:01:35.040><c> needs</c>

00:01:35.590 --> 00:01:35.600 align:start position:0%
orchestration framework with these needs
 

00:01:35.600 --> 00:01:37.350 align:start position:0%
orchestration framework with these needs
in<00:01:35.840><c> mind.</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
in mind.
 

00:01:37.360 --> 00:01:39.990 align:start position:0%
in mind.
Our<00:01:37.680><c> persistence</c><00:01:38.479><c> layer</c><00:01:38.799><c> enables</c><00:01:39.200><c> many</c><00:01:39.520><c> human</c>

00:01:39.990 --> 00:01:40.000 align:start position:0%
Our persistence layer enables many human
 

00:01:40.000 --> 00:01:42.630 align:start position:0%
Our persistence layer enables many human
loop<00:01:40.320><c> interaction</c><00:01:40.799><c> patterns,</c><00:01:41.680><c> letting</c><00:01:42.079><c> users</c>

00:01:42.630 --> 00:01:42.640 align:start position:0%
loop interaction patterns, letting users
 

00:01:42.640 --> 00:01:45.749 align:start position:0%
loop interaction patterns, letting users
view,<00:01:43.439><c> revisit,</c><00:01:44.079><c> and</c><00:01:44.400><c> modify</c><00:01:44.960><c> agent</c><00:01:45.439><c> state</c>

00:01:45.749 --> 00:01:45.759 align:start position:0%
view, revisit, and modify agent state
 

00:01:45.759 --> 00:01:48.550 align:start position:0%
view, revisit, and modify agent state
easily.<00:01:46.880><c> We've</c><00:01:47.280><c> also</c><00:01:47.520><c> built</c><00:01:47.840><c> Langraph</c>

00:01:48.550 --> 00:01:48.560 align:start position:0%
easily. We've also built Langraph
 

00:01:48.560 --> 00:01:50.389 align:start position:0%
easily. We've also built Langraph
platform<00:01:49.280><c> to</c><00:01:49.600><c> provide</c><00:01:49.840><c> you</c><00:01:50.000><c> with</c><00:01:50.159><c> the</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
platform to provide you with the
 

00:01:50.399 --> 00:01:53.030 align:start position:0%
platform to provide you with the
infrastructure<00:01:51.040><c> to</c><00:01:51.280><c> run</c><00:01:51.520><c> agents</c><00:01:52.000><c> at</c><00:01:52.320><c> scale</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
infrastructure to run agents at scale
 

00:01:53.040 --> 00:01:55.510 align:start position:0%
infrastructure to run agents at scale
which<00:01:53.280><c> is</c><00:01:53.439><c> ideal</c><00:01:53.759><c> for</c><00:01:54.000><c> longunning</c><00:01:54.799><c> or</c><00:01:55.040><c> bursty</c>

00:01:55.510 --> 00:01:55.520 align:start position:0%
which is ideal for longunning or bursty
 

00:01:55.520 --> 00:01:58.389 align:start position:0%
which is ideal for longunning or bursty
workloads.<00:01:56.640><c> And</c><00:01:56.799><c> with</c><00:01:57.040><c> Langmith,</c><00:01:57.840><c> you</c><00:01:58.079><c> have</c><00:01:58.159><c> a</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
workloads. And with Langmith, you have a
 

00:01:58.399 --> 00:02:01.270 align:start position:0%
workloads. And with Langmith, you have a
platform<00:01:58.799><c> for</c><00:01:59.280><c> observability,</c><00:02:00.399><c> evaluation,</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
platform for observability, evaluation,
 

00:02:01.280 --> 00:02:03.510 align:start position:0%
platform for observability, evaluation,
and<00:02:01.520><c> prompt</c><00:02:01.920><c> engineering.</c><00:02:02.960><c> Great</c><00:02:03.280><c> for</c>

00:02:03.510 --> 00:02:03.520 align:start position:0%
and prompt engineering. Great for
 

00:02:03.520 --> 00:02:05.990 align:start position:0%
and prompt engineering. Great for
monitoring<00:02:04.240><c> and</c><00:02:04.560><c> improving</c><00:02:05.360><c> longunning</c>

00:02:05.990 --> 00:02:06.000 align:start position:0%
monitoring and improving longunning
 

00:02:06.000 --> 00:02:08.229 align:start position:0%
monitoring and improving longunning
agents.<00:02:06.960><c> In</c><00:02:07.119><c> this</c><00:02:07.360><c> course,</c><00:02:07.600><c> we'll</c><00:02:07.840><c> guide</c><00:02:08.000><c> you</c>

00:02:08.229 --> 00:02:08.239 align:start position:0%
agents. In this course, we'll guide you
 

00:02:08.239 --> 00:02:09.830 align:start position:0%
agents. In this course, we'll guide you
step<00:02:08.479><c> by</c><00:02:08.640><c> step</c><00:02:08.879><c> through</c><00:02:09.119><c> building</c><00:02:09.440><c> your</c><00:02:09.599><c> own</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
step by step through building your own
 

00:02:09.840 --> 00:02:12.070 align:start position:0%
step by step through building your own
ambient<00:02:10.239><c> agent</c><00:02:10.479><c> for</c><00:02:10.640><c> managing</c><00:02:10.959><c> your</c><00:02:11.200><c> email.</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
ambient agent for managing your email.
 

00:02:12.080 --> 00:02:13.990 align:start position:0%
ambient agent for managing your email.
This<00:02:12.239><c> is</c><00:02:12.319><c> a</c><00:02:12.480><c> near</c><00:02:12.800><c> universal</c><00:02:13.280><c> task,</c><00:02:13.520><c> but</c><00:02:13.760><c> also</c>

00:02:13.990 --> 00:02:14.000 align:start position:0%
This is a near universal task, but also
 

00:02:14.000 --> 00:02:15.510 align:start position:0%
This is a near universal task, but also
one<00:02:14.239><c> that</c><00:02:14.400><c> many</c><00:02:14.640><c> of</c><00:02:14.800><c> us</c><00:02:14.959><c> would</c><00:02:15.120><c> love</c><00:02:15.360><c> to</c>

00:02:15.510 --> 00:02:15.520 align:start position:0%
one that many of us would love to
 

00:02:15.520 --> 00:02:17.430 align:start position:0%
one that many of us would love to
automate.<00:02:16.480><c> You'll</c><00:02:16.720><c> learn</c><00:02:16.879><c> the</c><00:02:17.040><c> basics</c><00:02:17.280><c> of</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
automate. You'll learn the basics of
 

00:02:17.440 --> 00:02:19.190 align:start position:0%
automate. You'll learn the basics of
Langraphth,<00:02:18.160><c> and</c><00:02:18.319><c> you'll</c><00:02:18.560><c> build</c><00:02:18.720><c> an</c><00:02:18.959><c> email</c>

00:02:19.190 --> 00:02:19.200 align:start position:0%
Langraphth, and you'll build an email
 

00:02:19.200 --> 00:02:21.110 align:start position:0%
Langraphth, and you'll build an email
agent<00:02:19.520><c> using</c><00:02:19.840><c> Langraph.</c><00:02:20.560><c> You'll</c><00:02:20.800><c> learn</c><00:02:20.959><c> how</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
agent using Langraph. You'll learn how
 

00:02:21.120 --> 00:02:23.110 align:start position:0%
agent using Langraph. You'll learn how
to<00:02:21.200><c> evaluate</c><00:02:21.599><c> this</c><00:02:21.760><c> agent</c><00:02:22.000><c> with</c><00:02:22.160><c> Langsmith.</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
to evaluate this agent with Langsmith.
 

00:02:23.120 --> 00:02:24.869 align:start position:0%
to evaluate this agent with Langsmith.
You<00:02:23.360><c> learn</c><00:02:23.520><c> how</c><00:02:23.680><c> to</c><00:02:23.760><c> add</c><00:02:24.000><c> human</c><00:02:24.400><c> loop</c><00:02:24.560><c> to</c><00:02:24.720><c> this</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
You learn how to add human loop to this
 

00:02:24.879 --> 00:02:27.190 align:start position:0%
You learn how to add human loop to this
agent<00:02:25.120><c> to</c><00:02:25.280><c> approve</c><00:02:25.680><c> sensitive</c><00:02:26.160><c> actions</c><00:02:26.879><c> like</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
agent to approve sensitive actions like
 

00:02:27.200 --> 00:02:29.510 align:start position:0%
agent to approve sensitive actions like
sending<00:02:27.599><c> emails.</c><00:02:28.640><c> You</c><00:02:28.800><c> also</c><00:02:29.040><c> learn</c><00:02:29.280><c> how</c><00:02:29.360><c> to</c>

00:02:29.510 --> 00:02:29.520 align:start position:0%
sending emails. You also learn how to
 

00:02:29.520 --> 00:02:30.869 align:start position:0%
sending emails. You also learn how to
incorporate<00:02:29.920><c> memory</c><00:02:30.319><c> so</c><00:02:30.480><c> your</c><00:02:30.560><c> agent</c>

00:02:30.869 --> 00:02:30.879 align:start position:0%
incorporate memory so your agent
 

00:02:30.879 --> 00:02:32.630 align:start position:0%
incorporate memory so your agent
remembers<00:02:31.280><c> and</c><00:02:31.440><c> adapts</c><00:02:31.840><c> to</c><00:02:32.000><c> your</c><00:02:32.160><c> feedback</c>

00:02:32.630 --> 00:02:32.640 align:start position:0%
remembers and adapts to your feedback
 

00:02:32.640 --> 00:02:34.949 align:start position:0%
remembers and adapts to your feedback
over<00:02:32.959><c> time.</c><00:02:33.760><c> And</c><00:02:34.000><c> finally,</c><00:02:34.400><c> you</c><00:02:34.560><c> learn</c><00:02:34.720><c> how</c><00:02:34.879><c> to</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
over time. And finally, you learn how to
 

00:02:34.959 --> 00:02:36.470 align:start position:0%
over time. And finally, you learn how to
deploy<00:02:35.280><c> this</c><00:02:35.440><c> agent</c><00:02:35.760><c> using</c><00:02:35.920><c> Langraph</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
deploy this agent using Langraph
 

00:02:36.480 --> 00:02:38.229 align:start position:0%
deploy this agent using Langraph
platform.<00:02:37.200><c> And</c><00:02:37.360><c> by</c><00:02:37.599><c> the</c><00:02:37.680><c> end</c><00:02:37.760><c> of</c><00:02:37.840><c> this</c><00:02:38.000><c> course,</c>

00:02:38.229 --> 00:02:38.239 align:start position:0%
platform. And by the end of this course,
 

00:02:38.239 --> 00:02:39.670 align:start position:0%
platform. And by the end of this course,
you'll<00:02:38.480><c> have</c><00:02:38.560><c> a</c><00:02:38.720><c> working</c><00:02:38.959><c> email</c><00:02:39.200><c> agent</c><00:02:39.519><c> and</c>

00:02:39.670 --> 00:02:39.680 align:start position:0%
you'll have a working email agent and
 

00:02:39.680 --> 00:02:41.030 align:start position:0%
you'll have a working email agent and
you'll<00:02:39.840><c> be</c><00:02:39.920><c> equipped</c><00:02:40.160><c> to</c><00:02:40.319><c> build</c><00:02:40.560><c> and</c><00:02:40.720><c> deploy</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
you'll be equipped to build and deploy
 

00:02:41.040 --> 00:02:43.190 align:start position:0%
you'll be equipped to build and deploy
Amid<00:02:41.440><c> agents</c><00:02:41.760><c> for</c><00:02:41.920><c> a</c><00:02:42.160><c> wide</c><00:02:42.480><c> range</c><00:02:42.720><c> of</c><00:02:42.879><c> real</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
Amid agents for a wide range of real
 

00:02:43.200 --> 00:02:45.670 align:start position:0%
Amid agents for a wide range of real
world<00:02:43.440><c> tasks.</c><00:02:44.480><c> I'm</c><00:02:44.800><c> really</c><00:02:45.040><c> excited</c><00:02:45.360><c> for</c><00:02:45.519><c> you</c>

00:02:45.670 --> 00:02:45.680 align:start position:0%
world tasks. I'm really excited for you
 

00:02:45.680 --> 00:02:47.030 align:start position:0%
world tasks. I'm really excited for you
to<00:02:45.760><c> take</c><00:02:45.920><c> this</c><00:02:46.160><c> course</c><00:02:46.400><c> and</c><00:02:46.560><c> I</c><00:02:46.720><c> hope</c><00:02:46.879><c> it</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
to take this course and I hope it
 

00:02:47.040 --> 00:02:48.470 align:start position:0%
to take this course and I hope it
empowers<00:02:47.440><c> you</c><00:02:47.599><c> to</c><00:02:47.840><c> build</c><00:02:48.000><c> the</c><00:02:48.239><c> next</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
empowers you to build the next
 

00:02:48.480 --> 00:02:50.790 align:start position:0%
empowers you to build the next
generation<00:02:49.040><c> of</c><00:02:49.280><c> agentic</c><00:02:49.920><c> applications.</c>

00:02:50.790 --> 00:02:50.800 align:start position:0%
generation of agentic applications.
 

00:02:50.800 --> 00:02:52.710 align:start position:0%
generation of agentic applications.
Whether<00:02:51.200><c> you're</c><00:02:51.599><c> experimenting</c><00:02:52.239><c> with</c><00:02:52.480><c> new</c>

00:02:52.710 --> 00:02:52.720 align:start position:0%
Whether you're experimenting with new
 

00:02:52.720 --> 00:02:55.270 align:start position:0%
Whether you're experimenting with new
ideas<00:02:53.280><c> or</c><00:02:53.680><c> bringing</c><00:02:54.160><c> robust</c><00:02:54.800><c> real</c><00:02:55.120><c> world</c>

00:02:55.270 --> 00:02:55.280 align:start position:0%
ideas or bringing robust real world
 

00:02:55.280 --> 00:02:59.080 align:start position:0%
ideas or bringing robust real world
agents<00:02:55.760><c> into</c><00:02:56.080><c> production,</c>

