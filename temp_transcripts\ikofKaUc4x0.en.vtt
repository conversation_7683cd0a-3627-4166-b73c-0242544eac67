WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:03.030 align:start position:0%
 
hello<00:00:01.000><c> in</c><00:00:01.199><c> this</c><00:00:01.400><c> video</c><00:00:01.880><c> I</c><00:00:01.959><c> want</c><00:00:02.120><c> to</c><00:00:02.280><c> show</c><00:00:02.520><c> you</c>

00:00:03.030 --> 00:00:03.040 align:start position:0%
hello in this video I want to show you
 

00:00:03.040 --> 00:00:05.190 align:start position:0%
hello in this video I want to show you
how<00:00:03.199><c> you</c><00:00:03.320><c> can</c><00:00:03.600><c> expose</c><00:00:04.120><c> your</c><00:00:04.319><c> quar</c><00:00:04.720><c> database</c>

00:00:05.190 --> 00:00:05.200 align:start position:0%
how you can expose your quar database
 

00:00:05.200 --> 00:00:08.509 align:start position:0%
how you can expose your quar database
cluster<00:00:05.600><c> running</c><00:00:05.920><c> on</c><00:00:06.120><c> Quant</c><00:00:06.560><c> hybrid</c><00:00:06.919><c> Cloud</c><00:00:07.600><c> to</c>

00:00:08.509 --> 00:00:08.519 align:start position:0%
cluster running on Quant hybrid Cloud to
 

00:00:08.519 --> 00:00:11.150 align:start position:0%
cluster running on Quant hybrid Cloud to
applications<00:00:09.320><c> or</c><00:00:09.519><c> users</c><00:00:10.360><c> external</c><00:00:10.880><c> to</c><00:00:11.040><c> your</c>

00:00:11.150 --> 00:00:11.160 align:start position:0%
applications or users external to your
 

00:00:11.160 --> 00:00:14.070 align:start position:0%
applications or users external to your
kubernetes<00:00:12.160><c> cluster</c><00:00:13.160><c> how</c><00:00:13.320><c> you</c><00:00:13.440><c> can</c><00:00:13.679><c> configure</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
kubernetes cluster how you can configure
 

00:00:14.080 --> 00:00:16.590 align:start position:0%
kubernetes cluster how you can configure
TLS<00:00:14.519><c> certificates</c><00:00:15.040><c> for</c><00:00:15.240><c> your</c><00:00:15.480><c> database</c><00:00:16.359><c> and</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
TLS certificates for your database and
 

00:00:16.600 --> 00:00:19.230 align:start position:0%
TLS certificates for your database and
authentication<00:00:17.439><c> by</c><00:00:17.600><c> default</c><00:00:18.279><c> if</c><00:00:18.439><c> you</c><00:00:18.880><c> create</c>

00:00:19.230 --> 00:00:19.240 align:start position:0%
authentication by default if you create
 

00:00:19.240 --> 00:00:21.910 align:start position:0%
authentication by default if you create
a<00:00:19.560><c> quadrant</c><00:00:20.000><c> database</c><00:00:20.480><c> cluster</c><00:00:20.960><c> on</c><00:00:21.160><c> hybrid</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
a quadrant database cluster on hybrid
 

00:00:21.920 --> 00:00:25.109 align:start position:0%
a quadrant database cluster on hybrid
Cloud<00:00:22.920><c> the</c><00:00:23.519><c> database</c><00:00:24.080><c> will</c><00:00:24.279><c> only</c><00:00:24.640><c> expose</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
Cloud the database will only expose
 

00:00:25.119 --> 00:00:27.349 align:start position:0%
Cloud the database will only expose
through<00:00:25.359><c> an</c><00:00:25.519><c> internal</c><00:00:26.000><c> cluster</c><00:00:26.400><c> IP</c><00:00:26.760><c> service</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
through an internal cluster IP service
 

00:00:27.359 --> 00:00:30.230 align:start position:0%
through an internal cluster IP service
within<00:00:27.599><c> your</c><00:00:27.800><c> kubernetes</c><00:00:28.519><c> cluster</c><00:00:29.519><c> but</c><00:00:30.000><c> it's</c>

00:00:30.230 --> 00:00:30.240 align:start position:0%
within your kubernetes cluster but it's
 

00:00:30.240 --> 00:00:33.350 align:start position:0%
within your kubernetes cluster but it's
very<00:00:30.480><c> easy</c><00:00:31.199><c> to</c><00:00:31.519><c> also</c><00:00:31.920><c> expose</c><00:00:32.360><c> this</c><00:00:32.880><c> outside</c><00:00:33.280><c> of</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
very easy to also expose this outside of
 

00:00:33.360 --> 00:00:36.350 align:start position:0%
very easy to also expose this outside of
your<00:00:33.520><c> kubernetes</c><00:00:34.399><c> tust</c><00:00:35.399><c> if</c><00:00:35.520><c> you</c><00:00:35.680><c> look</c><00:00:36.079><c> into</c>

00:00:36.350 --> 00:00:36.360 align:start position:0%
your kubernetes tust if you look into
 

00:00:36.360 --> 00:00:39.950 align:start position:0%
your kubernetes tust if you look into
our<00:00:36.719><c> documentation</c><00:00:38.000><c> we</c><00:00:39.000><c> document</c><00:00:39.600><c> different</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
our documentation we document different
 

00:00:39.960 --> 00:00:42.310 align:start position:0%
our documentation we document different
ways<00:00:40.200><c> to</c><00:00:40.360><c> do</c><00:00:40.600><c> this</c><00:00:41.079><c> either</c><00:00:41.440><c> for</c><00:00:41.680><c> example</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
ways to do this either for example
 

00:00:42.320 --> 00:00:45.150 align:start position:0%
ways to do this either for example
through<00:00:42.960><c> a</c><00:00:43.200><c> low</c><00:00:43.520><c> balancer</c><00:00:44.039><c> service</c><00:00:44.920><c> through</c>

00:00:45.150 --> 00:00:45.160 align:start position:0%
through a low balancer service through
 

00:00:45.160 --> 00:00:47.590 align:start position:0%
through a low balancer service through
an<00:00:45.320><c> Ingress</c><00:00:45.800><c> or</c><00:00:46.120><c> just</c><00:00:46.360><c> with</c><00:00:46.480><c> a</c><00:00:46.600><c> plain</c><00:00:46.879><c> port</c><00:00:47.199><c> for</c>

00:00:47.590 --> 00:00:47.600 align:start position:0%
an Ingress or just with a plain port for
 

00:00:47.600 --> 00:00:50.790 align:start position:0%
an Ingress or just with a plain port for
let's<00:00:48.199><c> look</c><00:00:48.960><c> how</c><00:00:49.160><c> we</c><00:00:49.280><c> can</c><00:00:49.559><c> expose</c><00:00:50.320><c> the</c>

00:00:50.790 --> 00:00:50.800 align:start position:0%
let's look how we can expose the
 

00:00:50.800 --> 00:00:53.470 align:start position:0%
let's look how we can expose the
database<00:00:51.800><c> with</c><00:00:51.960><c> a</c><00:00:52.079><c> low</c><00:00:52.280><c> balance</c><00:00:52.760><c> I</c><00:00:52.879><c> already</c>

00:00:53.470 --> 00:00:53.480 align:start position:0%
database with a low balance I already
 

00:00:53.480 --> 00:00:56.389 align:start position:0%
database with a low balance I already
prepared<00:00:54.480><c> uh</c><00:00:55.480><c> quadrant</c><00:00:55.879><c> hybrid</c><00:00:56.160><c> Cloud</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
prepared uh quadrant hybrid Cloud
 

00:00:56.399 --> 00:00:58.509 align:start position:0%
prepared uh quadrant hybrid Cloud
database<00:00:56.800><c> cluster</c><00:00:57.239><c> running</c><00:00:57.559><c> on</c><00:00:57.719><c> digital</c><00:00:58.039><c> Lo</c>

00:00:58.509 --> 00:00:58.519 align:start position:0%
database cluster running on digital Lo
 

00:00:58.519 --> 00:01:00.990 align:start position:0%
database cluster running on digital Lo
once<00:00:58.800><c> the</c><00:00:58.920><c> cluster</c><00:00:59.199><c> is</c><00:00:59.359><c> created</c><00:01:00.079><c> you</c><00:01:00.239><c> can</c><00:01:00.519><c> go</c>

00:01:00.990 --> 00:01:01.000 align:start position:0%
once the cluster is created you can go
 

00:01:01.000 --> 00:01:03.069 align:start position:0%
once the cluster is created you can go
to<00:01:01.199><c> the</c><00:01:01.359><c> kubernetes</c><00:01:01.920><c> configuration</c><00:01:02.640><c> section</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
to the kubernetes configuration section
 

00:01:03.079 --> 00:01:05.350 align:start position:0%
to the kubernetes configuration section
on<00:01:03.199><c> your</c><00:01:03.359><c> cluster</c><00:01:03.680><c> detail</c><00:01:04.040><c> page</c><00:01:04.839><c> and</c><00:01:05.080><c> there</c>

00:01:05.350 --> 00:01:05.360 align:start position:0%
on your cluster detail page and there
 

00:01:05.360 --> 00:01:07.630 align:start position:0%
on your cluster detail page and there
there<00:01:05.479><c> is</c><00:01:05.600><c> a</c><00:01:05.840><c> section</c><00:01:06.119><c> to</c><00:01:06.320><c> configure</c><00:01:06.720><c> the</c>

00:01:07.630 --> 00:01:07.640 align:start position:0%
there is a section to configure the
 

00:01:07.640 --> 00:01:10.030 align:start position:0%
there is a section to configure the
service<00:01:08.640><c> by</c><00:01:08.840><c> default</c><00:01:09.240><c> this</c><00:01:09.360><c> will</c><00:01:09.520><c> be</c><00:01:09.680><c> cluster</c>

00:01:10.030 --> 00:01:10.040 align:start position:0%
service by default this will be cluster
 

00:01:10.040 --> 00:01:12.350 align:start position:0%
service by default this will be cluster
IP<00:01:10.880><c> but</c><00:01:11.040><c> you</c><00:01:11.159><c> also</c><00:01:11.360><c> can</c><00:01:11.520><c> change</c><00:01:11.759><c> it</c><00:01:11.880><c> to</c><00:01:12.000><c> a</c><00:01:12.119><c> low</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
IP but you also can change it to a low
 

00:01:12.360 --> 00:01:14.950 align:start position:0%
IP but you also can change it to a low
balancer<00:01:12.960><c> service</c><00:01:13.960><c> to</c><00:01:14.280><c> instruct</c><00:01:14.799><c> the</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
balancer service to instruct the
 

00:01:14.960 --> 00:01:17.149 align:start position:0%
balancer service to instruct the
kubernetes<00:01:15.520><c> cluster</c><00:01:15.960><c> to</c><00:01:16.119><c> create</c><00:01:16.360><c> a</c><00:01:16.479><c> cloud</c><00:01:16.840><c> Lo</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
kubernetes cluster to create a cloud Lo
 

00:01:17.159 --> 00:01:19.109 align:start position:0%
kubernetes cluster to create a cloud Lo
load<00:01:17.400><c> balancer</c><00:01:17.880><c> for</c><00:01:18.080><c> the</c>

00:01:19.109 --> 00:01:19.119 align:start position:0%
load balancer for the
 

00:01:19.119 --> 00:01:21.030 align:start position:0%
load balancer for the
database<00:01:20.119><c> depending</c><00:01:20.439><c> on</c><00:01:20.560><c> your</c><00:01:20.720><c> cloud</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
database depending on your cloud
 

00:01:21.040 --> 00:01:23.069 align:start position:0%
database depending on your cloud
provider<00:01:21.920><c> there</c><00:01:22.159><c> are</c><00:01:22.640><c> additional</c>

00:01:23.069 --> 00:01:23.079 align:start position:0%
provider there are additional
 

00:01:23.079 --> 00:01:25.749 align:start position:0%
provider there are additional
configuration<00:01:23.840><c> options</c><00:01:24.280><c> to</c><00:01:24.560><c> configure</c><00:01:25.560><c> how</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
configuration options to configure how
 

00:01:25.759 --> 00:01:27.789 align:start position:0%
configuration options to configure how
this<00:01:25.920><c> slow</c><00:01:26.159><c> balancer</c><00:01:26.520><c> should</c><00:01:26.680><c> be</c><00:01:26.799><c> configured</c>

00:01:27.789 --> 00:01:27.799 align:start position:0%
this slow balancer should be configured
 

00:01:27.799 --> 00:01:29.390 align:start position:0%
this slow balancer should be configured
for<00:01:28.040><c> example</c><00:01:28.439><c> if</c><00:01:28.520><c> you</c><00:01:28.640><c> want</c><00:01:28.720><c> to</c><00:01:28.920><c> add</c><00:01:29.040><c> it</c><00:01:29.159><c> to</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
for example if you want to add it to
 

00:01:29.400 --> 00:01:30.990 align:start position:0%
for example if you want to add it to
certain<00:01:29.640><c> secur</c><00:01:29.920><c> Security</c><00:01:30.240><c> Group</c><00:01:30.680><c> if</c><00:01:30.759><c> you</c><00:01:30.880><c> want</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
certain secur Security Group if you want
 

00:01:31.000 --> 00:01:33.069 align:start position:0%
certain secur Security Group if you want
to<00:01:31.200><c> give</c><00:01:31.320><c> it</c><00:01:31.439><c> a</c><00:01:31.560><c> certain</c><00:01:31.920><c> name</c><00:01:32.560><c> if</c><00:01:32.680><c> you</c><00:01:32.759><c> want</c><00:01:32.920><c> to</c>

00:01:33.069 --> 00:01:33.079 align:start position:0%
to give it a certain name if you want to
 

00:01:33.079 --> 00:01:34.830 align:start position:0%
to give it a certain name if you want to
configure<00:01:33.479><c> health</c><00:01:33.759><c> checks</c><00:01:34.119><c> all</c><00:01:34.280><c> of</c><00:01:34.439><c> that</c><00:01:34.640><c> can</c>

00:01:34.830 --> 00:01:34.840 align:start position:0%
configure health checks all of that can
 

00:01:34.840 --> 00:01:37.069 align:start position:0%
configure health checks all of that can
also<00:01:35.479><c> be</c><00:01:35.720><c> added</c><00:01:36.159><c> depending</c><00:01:36.520><c> on</c><00:01:36.640><c> your</c><00:01:36.799><c> cloud</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
also be added depending on your cloud
 

00:01:37.079 --> 00:01:39.270 align:start position:0%
also be added depending on your cloud
provider<00:01:37.479><c> for</c><00:01:37.720><c> digital</c><00:01:38.040><c> ocean</c><00:01:38.360><c> for</c><00:01:38.600><c> example</c>

00:01:39.270 --> 00:01:39.280 align:start position:0%
provider for digital ocean for example
 

00:01:39.280 --> 00:01:41.310 align:start position:0%
provider for digital ocean for example
there's<00:01:39.479><c> a</c><00:01:39.640><c> huge</c><00:01:39.960><c> list</c><00:01:40.280><c> of</c><00:01:40.840><c> service</c>

00:01:41.310 --> 00:01:41.320 align:start position:0%
there's a huge list of service
 

00:01:41.320 --> 00:01:43.550 align:start position:0%
there's a huge list of service
annotations<00:01:42.040><c> where</c><00:01:42.240><c> I</c><00:01:42.399><c> can</c><00:01:43.000><c> provide</c><00:01:43.360><c> these</c>

00:01:43.550 --> 00:01:43.560 align:start position:0%
annotations where I can provide these
 

00:01:43.560 --> 00:01:46.030 align:start position:0%
annotations where I can provide these
documentations<00:01:44.520><c> uh</c><00:01:44.640><c> and</c><00:01:44.840><c> configuration</c><00:01:45.600><c> so</c>

00:01:46.030 --> 00:01:46.040 align:start position:0%
documentations uh and configuration so
 

00:01:46.040 --> 00:01:47.789 align:start position:0%
documentations uh and configuration so
for<00:01:46.240><c> example</c><00:01:46.640><c> if</c><00:01:46.719><c> I</c><00:01:46.840><c> wanted</c><00:01:47.079><c> to</c><00:01:47.280><c> change</c><00:01:47.600><c> the</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
for example if I wanted to change the
 

00:01:47.799 --> 00:01:49.950 align:start position:0%
for example if I wanted to change the
name<00:01:48.159><c> from</c><00:01:48.320><c> an</c><00:01:48.479><c> autogenerated</c><00:01:49.240><c> one</c><00:01:49.520><c> to</c><00:01:49.680><c> some</c>

00:01:49.950 --> 00:01:49.960 align:start position:0%
name from an autogenerated one to some
 

00:01:49.960 --> 00:01:52.990 align:start position:0%
name from an autogenerated one to some
custom<00:01:50.280><c> one</c><00:01:50.840><c> I</c><00:01:50.960><c> could</c><00:01:51.200><c> use</c><00:01:51.600><c> this</c><00:01:51.880><c> Doan</c><00:01:52.680><c> a</c><00:01:52.799><c> name</c>

00:01:52.990 --> 00:01:53.000 align:start position:0%
custom one I could use this Doan a name
 

00:01:53.000 --> 00:01:55.350 align:start position:0%
custom one I could use this Doan a name
annotation<00:01:53.719><c> and</c><00:01:54.079><c> I</c><00:01:54.159><c> can</c><00:01:54.360><c> add</c><00:01:54.560><c> this</c><00:01:54.719><c> annotation</c>

00:01:55.350 --> 00:01:55.360 align:start position:0%
annotation and I can add this annotation
 

00:01:55.360 --> 00:01:57.709 align:start position:0%
annotation and I can add this annotation
here<00:01:56.079><c> inside</c><00:01:56.399><c> of</c><00:01:56.520><c> the</c><00:01:56.640><c> service</c><00:01:57.000><c> section</c><00:01:57.479><c> and</c>

00:01:57.709 --> 00:01:57.719 align:start position:0%
here inside of the service section and
 

00:01:57.719 --> 00:02:00.270 align:start position:0%
here inside of the service section and
again<00:01:58.280><c> there</c><00:01:58.399><c> are</c><00:01:58.880><c> way</c><00:01:59.079><c> more</c><00:01:59.520><c> conf</c><00:02:00.039><c> ation</c>

00:02:00.270 --> 00:02:00.280 align:start position:0%
again there are way more conf ation
 

00:02:00.280 --> 00:02:03.789 align:start position:0%
again there are way more conf ation
options<00:02:00.640><c> available</c><00:02:01.520><c> that</c><00:02:01.799><c> depend</c><00:02:02.240><c> then</c><00:02:02.799><c> on</c>

00:02:03.789 --> 00:02:03.799 align:start position:0%
options available that depend then on
 

00:02:03.799 --> 00:02:05.550 align:start position:0%
options available that depend then on
cloud<00:02:04.119><c> provider</c><00:02:04.759><c> best</c><00:02:05.000><c> refer</c><00:02:05.320><c> to</c><00:02:05.439><c> the</c>

00:02:05.550 --> 00:02:05.560 align:start position:0%
cloud provider best refer to the
 

00:02:05.560 --> 00:02:06.870 align:start position:0%
cloud provider best refer to the
documentation<00:02:06.039><c> of</c><00:02:06.159><c> the</c><00:02:06.280><c> cloud</c><00:02:06.560><c> provider</c>

00:02:06.870 --> 00:02:06.880 align:start position:0%
documentation of the cloud provider
 

00:02:06.880 --> 00:02:09.350 align:start position:0%
documentation of the cloud provider
you're<00:02:07.079><c> using</c><00:02:07.600><c> for</c><00:02:07.960><c> what</c><00:02:08.119><c> annotation</c><00:02:08.640><c> are</c><00:02:09.280><c> if</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
you're using for what annotation are if
 

00:02:09.360 --> 00:02:12.270 align:start position:0%
you're using for what annotation are if
I<00:02:09.479><c> save</c><00:02:09.759><c> this</c><00:02:10.200><c> change</c><00:02:11.200><c> then</c><00:02:11.560><c> the</c><00:02:11.680><c> service</c><00:02:12.040><c> type</c>

00:02:12.270 --> 00:02:12.280 align:start position:0%
I save this change then the service type
 

00:02:12.280 --> 00:02:14.949 align:start position:0%
I save this change then the service type
will<00:02:12.400><c> be</c><00:02:12.520><c> changed</c><00:02:13.000><c> and</c><00:02:13.520><c> in</c><00:02:13.640><c> a</c><00:02:13.720><c> few</c><00:02:13.959><c> minutes</c><00:02:14.800><c> a</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
will be changed and in a few minutes a
 

00:02:14.959 --> 00:02:17.070 align:start position:0%
will be changed and in a few minutes a
low<00:02:15.160><c> balancer</c><00:02:15.519><c> will</c><00:02:15.640><c> be</c><00:02:15.760><c> created</c><00:02:16.640><c> the</c><00:02:16.800><c> time</c>

00:02:17.070 --> 00:02:17.080 align:start position:0%
low balancer will be created the time
 

00:02:17.080 --> 00:02:19.309 align:start position:0%
low balancer will be created the time
this<00:02:17.239><c> takes</c><00:02:17.640><c> depends</c><00:02:18.000><c> on</c><00:02:18.160><c> the</c><00:02:18.280><c> cloud</c><00:02:18.599><c> provider</c>

00:02:19.309 --> 00:02:19.319 align:start position:0%
this takes depends on the cloud provider
 

00:02:19.319 --> 00:02:20.869 align:start position:0%
this takes depends on the cloud provider
and<00:02:19.480><c> for</c><00:02:19.680><c> example</c><00:02:20.000><c> on</c><00:02:20.120><c> digital</c><00:02:20.440><c> ocean</c><00:02:20.760><c> this</c>

00:02:20.869 --> 00:02:20.879 align:start position:0%
and for example on digital ocean this
 

00:02:20.879 --> 00:02:23.309 align:start position:0%
and for example on digital ocean this
will<00:02:21.120><c> take</c><00:02:21.280><c> 2</c><00:02:21.480><c> or</c><00:02:21.599><c> 3</c><00:02:21.800><c> minutes</c><00:02:22.440><c> to</c><00:02:22.640><c> save</c><00:02:22.920><c> us</c><00:02:23.080><c> this</c>

00:02:23.309 --> 00:02:23.319 align:start position:0%
will take 2 or 3 minutes to save us this
 

00:02:23.319 --> 00:02:24.949 align:start position:0%
will take 2 or 3 minutes to save us this
time<00:02:23.599><c> I</c><00:02:23.879><c> already</c><00:02:24.080><c> prepared</c><00:02:24.440><c> the</c><00:02:24.599><c> second</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
time I already prepared the second
 

00:02:24.959 --> 00:02:28.030 align:start position:0%
time I already prepared the second
cluster<00:02:25.959><c> where</c><00:02:26.239><c> I</c><00:02:26.800><c> did</c><00:02:27.040><c> this</c><00:02:27.239><c> configuration</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
cluster where I did this configuration
 

00:02:28.040 --> 00:02:30.229 align:start position:0%
cluster where I did this configuration
already<00:02:29.040><c> so</c><00:02:29.239><c> you</c><00:02:29.360><c> can</c><00:02:29.519><c> see</c><00:02:29.920><c> here</c><00:02:30.040><c> in</c><00:02:30.120><c> the</c>

00:02:30.229 --> 00:02:30.239 align:start position:0%
already so you can see here in the
 

00:02:30.239 --> 00:02:32.630 align:start position:0%
already so you can see here in the
commun<00:02:30.720><c> configuration</c><00:02:31.360><c> section</c><00:02:32.160><c> the</c><00:02:32.319><c> service</c>

00:02:32.630 --> 00:02:32.640 align:start position:0%
commun configuration section the service
 

00:02:32.640 --> 00:02:35.710 align:start position:0%
commun configuration section the service
type<00:02:32.840><c> is</c><00:02:32.920><c> low</c><00:02:33.160><c> balancer</c><00:02:34.080><c> and</c><00:02:34.239><c> I</c><00:02:34.440><c> also</c><00:02:35.200><c> choose</c><00:02:35.519><c> a</c>

00:02:35.710 --> 00:02:35.720 align:start position:0%
type is low balancer and I also choose a
 

00:02:35.720 --> 00:02:38.589 align:start position:0%
type is low balancer and I also choose a
custom<00:02:36.000><c> low</c><00:02:36.239><c> balancer</c><00:02:37.080><c> name</c><00:02:38.080><c> you</c><00:02:38.200><c> can</c><00:02:38.360><c> also</c>

00:02:38.589 --> 00:02:38.599 align:start position:0%
custom low balancer name you can also
 

00:02:38.599 --> 00:02:41.949 align:start position:0%
custom low balancer name you can also
see<00:02:39.080><c> if</c><00:02:39.200><c> you</c><00:02:39.599><c> switch</c><00:02:40.040><c> to</c><00:02:40.319><c> the</c><00:02:40.720><c> digital</c><00:02:41.080><c> ocean</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
see if you switch to the digital ocean
 

00:02:41.959 --> 00:02:44.990 align:start position:0%
see if you switch to the digital ocean
portal<00:02:42.480><c> here</c><00:02:43.239><c> that</c><00:02:43.680><c> the</c><00:02:43.800><c> low</c><00:02:44.120><c> balancer</c><00:02:44.840><c> with</c>

00:02:44.990 --> 00:02:45.000 align:start position:0%
portal here that the low balancer with
 

00:02:45.000 --> 00:02:47.710 align:start position:0%
portal here that the low balancer with
the<00:02:45.120><c> same</c><00:02:45.360><c> name</c><00:02:45.560><c> is</c><00:02:45.760><c> actually</c><00:02:46.159><c> created</c><00:02:47.159><c> and</c>

00:02:47.710 --> 00:02:47.720 align:start position:0%
the same name is actually created and
 

00:02:47.720 --> 00:02:50.229 align:start position:0%
the same name is actually created and
accepting<00:02:48.640><c> what</c><00:02:48.760><c> you</c><00:02:48.879><c> can</c><00:02:49.120><c> also</c><00:02:49.440><c> see</c><00:02:49.720><c> here</c><00:02:50.000><c> is</c>

00:02:50.229 --> 00:02:50.239 align:start position:0%
accepting what you can also see here is
 

00:02:50.239 --> 00:02:52.229 align:start position:0%
accepting what you can also see here is
on<00:02:50.360><c> the</c><00:02:50.480><c> cluster</c><00:02:50.840><c> detail</c><00:02:51.200><c> page</c><00:02:51.560><c> that</c><00:02:51.720><c> the</c><00:02:51.920><c> IP</c>

00:02:52.229 --> 00:02:52.239 align:start position:0%
on the cluster detail page that the IP
 

00:02:52.239 --> 00:02:53.910 align:start position:0%
on the cluster detail page that the IP
address<00:02:52.640><c> of</c><00:02:52.760><c> the</c><00:02:52.879><c> low</c><00:02:53.440><c> is</c><00:02:53.599><c> actually</c>

00:02:53.910 --> 00:02:53.920 align:start position:0%
address of the low is actually
 

00:02:53.920 --> 00:02:56.550 align:start position:0%
address of the low is actually
recognized<00:02:54.519><c> now</c><00:02:54.840><c> and</c><00:02:55.159><c> also</c><00:02:55.879><c> shown</c><00:02:56.239><c> here</c><00:02:56.400><c> in</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
recognized now and also shown here in
 

00:02:56.560 --> 00:02:59.070 align:start position:0%
recognized now and also shown here in
the<00:02:56.680><c> endpoint</c><00:02:57.519><c> and</c><00:02:57.680><c> there</c><00:02:57.800><c> is</c><00:02:57.959><c> even</c><00:02:58.319><c> a</c><00:02:58.480><c> link</c><00:02:58.840><c> to</c>

00:02:59.070 --> 00:02:59.080 align:start position:0%
the endpoint and there is even a link to
 

00:02:59.080 --> 00:03:03.670 align:start position:0%
the endpoint and there is even a link to
quickly<00:02:59.879><c> open</c><00:03:00.640><c> the</c><00:03:01.239><c> database</c><00:03:02.040><c> dashboard</c><00:03:02.680><c> UI</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
quickly open the database dashboard UI
 

00:03:03.680 --> 00:03:07.030 align:start position:0%
quickly open the database dashboard UI
in<00:03:03.799><c> a</c><00:03:03.959><c> new</c><00:03:04.280><c> tab</c><00:03:05.280><c> on</c><00:03:05.599><c> this</c><00:03:05.920><c> newly</c><00:03:06.280><c> created</c><00:03:06.640><c> loal</c>

00:03:07.030 --> 00:03:07.040 align:start position:0%
in a new tab on this newly created loal
 

00:03:07.040 --> 00:03:09.509 align:start position:0%
in a new tab on this newly created loal
Lancer<00:03:08.040><c> if</c><00:03:08.159><c> you</c><00:03:08.280><c> click</c><00:03:08.480><c> on</c><00:03:08.599><c> it</c><00:03:09.080><c> you'll</c><00:03:09.360><c> get</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
Lancer if you click on it you'll get
 

00:03:09.519 --> 00:03:11.869 align:start position:0%
Lancer if you click on it you'll get
access<00:03:09.760><c> to</c><00:03:09.879><c> the</c><00:03:10.400><c> database</c><00:03:11.400><c> and</c><00:03:11.519><c> here</c><00:03:11.640><c> you</c><00:03:11.760><c> can</c>

00:03:11.869 --> 00:03:11.879 align:start position:0%
access to the database and here you can
 

00:03:11.879 --> 00:03:14.390 align:start position:0%
access to the database and here you can
already<00:03:12.200><c> see</c><00:03:12.480><c> two</c><00:03:12.680><c> things</c><00:03:13.640><c> the</c><00:03:13.879><c> first</c><00:03:14.159><c> thing</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
already see two things the first thing
 

00:03:14.400 --> 00:03:18.030 align:start position:0%
already see two things the first thing
is<00:03:15.400><c> we</c><00:03:15.640><c> don't</c><00:03:16.360><c> have</c><00:03:16.599><c> authentication</c><00:03:17.280><c> set</c><00:03:17.480><c> up</c>

00:03:18.030 --> 00:03:18.040 align:start position:0%
is we don't have authentication set up
 

00:03:18.040 --> 00:03:20.270 align:start position:0%
is we don't have authentication set up
and<00:03:18.239><c> also</c><00:03:18.480><c> we</c><00:03:18.640><c> don't</c><00:03:18.879><c> have</c><00:03:18.959><c> a</c><00:03:19.080><c> TLS</c><00:03:19.599><c> the</c><00:03:19.720><c> current</c>

00:03:20.270 --> 00:03:20.280 align:start position:0%
and also we don't have a TLS the current
 

00:03:20.280 --> 00:03:22.229 align:start position:0%
and also we don't have a TLS the current
detail<00:03:20.680><c> page</c><00:03:20.959><c> even</c><00:03:21.159><c> tells</c><00:03:21.360><c> you</c><00:03:21.599><c> that</c><00:03:21.879><c> that</c><00:03:22.000><c> you</c>

00:03:22.229 --> 00:03:22.239 align:start position:0%
detail page even tells you that that you
 

00:03:22.239 --> 00:03:24.030 align:start position:0%
detail page even tells you that that you
expose<00:03:22.680><c> the</c><00:03:22.879><c> database</c><00:03:23.480><c> outside</c><00:03:23.840><c> of</c><00:03:23.959><c> the</c>

00:03:24.030 --> 00:03:24.040 align:start position:0%
expose the database outside of the
 

00:03:24.040 --> 00:03:25.830 align:start position:0%
expose the database outside of the
kubernetes<00:03:24.519><c> cluster</c><00:03:25.000><c> without</c><00:03:25.360><c> configuring</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
kubernetes cluster without configuring
 

00:03:25.840 --> 00:03:28.750 align:start position:0%
kubernetes cluster without configuring
an<00:03:26.000><c> API</c><00:03:26.440><c> key</c><00:03:27.239><c> so</c><00:03:27.400><c> let's</c><00:03:27.640><c> change</c><00:03:27.959><c> this</c><00:03:28.159><c> first</c><00:03:28.640><c> in</c>

00:03:28.750 --> 00:03:28.760 align:start position:0%
an API key so let's change this first in
 

00:03:28.760 --> 00:03:30.990 align:start position:0%
an API key so let's change this first in
order<00:03:29.000><c> to</c><00:03:29.319><c> configure</c><00:03:29.840><c> API</c><00:03:30.239><c> key</c><00:03:30.560><c> you</c><00:03:30.720><c> need</c><00:03:30.840><c> to</c>

00:03:30.990 --> 00:03:31.000 align:start position:0%
order to configure API key you need to
 

00:03:31.000 --> 00:03:32.949 align:start position:0%
order to configure API key you need to
go<00:03:31.120><c> to</c><00:03:31.319><c> the</c><00:03:31.480><c> configuration</c><00:03:32.120><c> section</c><00:03:32.480><c> of</c><00:03:32.680><c> the</c>

00:03:32.949 --> 00:03:32.959 align:start position:0%
go to the configuration section of the
 

00:03:32.959 --> 00:03:35.750 align:start position:0%
go to the configuration section of the
cluster<00:03:33.360><c> detail</c><00:03:33.720><c> page</c><00:03:34.360><c> and</c><00:03:34.519><c> we</c><00:03:34.720><c> have</c><00:03:35.000><c> to</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
cluster detail page and we have to
 

00:03:35.760 --> 00:03:38.030 align:start position:0%
cluster detail page and we have to
reference<00:03:36.200><c> a</c><00:03:36.360><c> kubernetes</c><00:03:37.000><c> secret</c><00:03:37.560><c> that</c><00:03:37.760><c> holds</c>

00:03:38.030 --> 00:03:38.040 align:start position:0%
reference a kubernetes secret that holds
 

00:03:38.040 --> 00:03:40.710 align:start position:0%
reference a kubernetes secret that holds
our<00:03:38.280><c> authentication</c><00:03:39.239><c> information</c><00:03:40.239><c> you</c><00:03:40.400><c> don't</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
our authentication information you don't
 

00:03:40.720 --> 00:03:42.869 align:start position:0%
our authentication information you don't
have<00:03:40.840><c> to</c><00:03:41.239><c> pass</c><00:03:41.519><c> in</c><00:03:41.760><c> and</c><00:03:41.920><c> paste</c><00:03:42.159><c> in</c><00:03:42.319><c> your</c><00:03:42.480><c> API</c>

00:03:42.869 --> 00:03:42.879 align:start position:0%
have to pass in and paste in your API
 

00:03:42.879 --> 00:03:45.190 align:start position:0%
have to pass in and paste in your API
key<00:03:43.080><c> here</c><00:03:43.319><c> directly</c><00:03:44.319><c> you</c><00:03:44.519><c> only</c><00:03:44.840><c> have</c><00:03:44.959><c> to</c>

00:03:45.190 --> 00:03:45.200 align:start position:0%
key here directly you only have to
 

00:03:45.200 --> 00:03:47.550 align:start position:0%
key here directly you only have to
reference<00:03:45.599><c> a</c><00:03:45.760><c> secret</c><00:03:46.480><c> that</c><00:03:46.599><c> is</c><00:03:46.879><c> present</c><00:03:47.360><c> in</c>

00:03:47.550 --> 00:03:47.560 align:start position:0%
reference a secret that is present in
 

00:03:47.560 --> 00:03:50.110 align:start position:0%
reference a secret that is present in
your<00:03:47.920><c> kubernetes</c><00:03:48.519><c> cluster</c><00:03:49.439><c> so</c><00:03:49.599><c> let's</c><00:03:49.840><c> first</c>

00:03:50.110 --> 00:03:50.120 align:start position:0%
your kubernetes cluster so let's first
 

00:03:50.120 --> 00:03:52.830 align:start position:0%
your kubernetes cluster so let's first
create<00:03:50.360><c> a</c><00:03:50.879><c> this</c><00:03:51.080><c> ccj</c><00:03:51.680><c> command</c><00:03:52.319><c> creates</c><00:03:52.680><c> a</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
create a this ccj command creates a
 

00:03:52.840 --> 00:03:55.949 align:start position:0%
create a this ccj command creates a
secret<00:03:53.360><c> called</c><00:03:53.760><c> my</c><00:03:54.000><c> API</c><00:03:54.439><c> key</c><00:03:55.200><c> with</c><00:03:55.439><c> a</c><00:03:55.599><c> field</c>

00:03:55.949 --> 00:03:55.959 align:start position:0%
secret called my API key with a field
 

00:03:55.959 --> 00:03:58.229 align:start position:0%
secret called my API key with a field
called<00:03:56.200><c> API</c><00:03:56.680><c> key</c><00:03:57.239><c> and</c><00:03:57.480><c> the</c><00:03:57.599><c> value</c><00:03:57.920><c> of</c><00:03:58.079><c> this</c>

00:03:58.229 --> 00:03:58.239 align:start position:0%
called API key and the value of this
 

00:03:58.239 --> 00:04:00.789 align:start position:0%
called API key and the value of this
field<00:03:58.640><c> is</c><00:03:58.879><c> called</c><00:03:59.400><c> top</c><00:03:59.799><c> Secret</c><00:04:00.120><c> in</c><00:04:00.280><c> this</c><00:04:00.480><c> case</c>

00:04:00.789 --> 00:04:00.799 align:start position:0%
field is called top Secret in this case
 

00:04:00.799 --> 00:04:03.190 align:start position:0%
field is called top Secret in this case
in<00:04:00.959><c> the</c><00:04:01.439><c> quadrant</c><00:04:01.840><c> name</c><00:04:02.519><c> you</c><00:04:02.640><c> can</c><00:04:02.879><c> create</c>

00:04:03.190 --> 00:04:03.200 align:start position:0%
in the quadrant name you can create
 

00:04:03.200 --> 00:04:05.670 align:start position:0%
in the quadrant name you can create
kubernetes<00:04:03.720><c> Secrets</c><00:04:04.360><c> not</c><00:04:04.519><c> only</c><00:04:04.760><c> with</c><00:04:05.040><c> CLI</c><00:04:05.599><c> you</c>

00:04:05.670 --> 00:04:05.680 align:start position:0%
kubernetes Secrets not only with CLI you
 

00:04:05.680 --> 00:04:08.309 align:start position:0%
kubernetes Secrets not only with CLI you
can<00:04:05.920><c> also</c><00:04:06.680><c> use</c><00:04:07.040><c> any</c><00:04:07.319><c> gitops</c><00:04:07.760><c> tool</c><00:04:07.959><c> of</c><00:04:08.120><c> your</c>

00:04:08.309 --> 00:04:08.319 align:start position:0%
can also use any gitops tool of your
 

00:04:08.319 --> 00:04:11.429 align:start position:0%
can also use any gitops tool of your
choice<00:04:08.840><c> or</c><00:04:09.519><c> use</c><00:04:10.519><c> any</c><00:04:10.760><c> other</c><00:04:11.079><c> secret</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
choice or use any other secret
 

00:04:11.439 --> 00:04:13.110 align:start position:0%
choice or use any other secret
management<00:04:11.840><c> tools</c><00:04:12.159><c> like</c><00:04:12.319><c> Hashi</c><00:04:12.640><c> cop</c><00:04:12.840><c> world</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
management tools like Hashi cop world
 

00:04:13.120 --> 00:04:15.509 align:start position:0%
management tools like Hashi cop world
that<00:04:13.239><c> has</c><00:04:13.400><c> kubernetes</c><00:04:14.000><c> connections</c><00:04:14.720><c> now</c><00:04:15.280><c> that</c>

00:04:15.509 --> 00:04:15.519 align:start position:0%
that has kubernetes connections now that
 

00:04:15.519 --> 00:04:18.229 align:start position:0%
that has kubernetes connections now that
this<00:04:15.879><c> secret</c><00:04:16.320><c> is</c><00:04:16.759><c> present</c><00:04:17.320><c> I</c><00:04:17.440><c> can</c><00:04:17.720><c> reference</c>

00:04:18.229 --> 00:04:18.239 align:start position:0%
this secret is present I can reference
 

00:04:18.239 --> 00:04:20.550 align:start position:0%
this secret is present I can reference
this<00:04:19.239><c> for</c><00:04:19.560><c> in</c><00:04:19.680><c> the</c><00:04:19.840><c> configuration</c><00:04:20.320><c> of</c><00:04:20.440><c> my</c>

00:04:20.550 --> 00:04:20.560 align:start position:0%
this for in the configuration of my
 

00:04:20.560 --> 00:04:22.629 align:start position:0%
this for in the configuration of my
quadrant<00:04:20.919><c> clust</c><00:04:21.519><c> so</c><00:04:21.680><c> I'm</c><00:04:21.840><c> going</c><00:04:21.919><c> to</c><00:04:22.120><c> copy</c><00:04:22.479><c> the</c>

00:04:22.629 --> 00:04:22.639 align:start position:0%
quadrant clust so I'm going to copy the
 

00:04:22.639 --> 00:04:25.629 align:start position:0%
quadrant clust so I'm going to copy the
name<00:04:22.880><c> of</c><00:04:23.040><c> the</c><00:04:23.160><c> secret</c><00:04:24.160><c> in</c><00:04:24.400><c> here</c><00:04:25.160><c> and</c><00:04:25.320><c> then</c><00:04:25.479><c> the</c>

00:04:25.629 --> 00:04:25.639 align:start position:0%
name of the secret in here and then the
 

00:04:25.639 --> 00:04:28.790 align:start position:0%
name of the secret in here and then the
name<00:04:25.960><c> of</c><00:04:26.120><c> the</c><00:04:26.280><c> field</c><00:04:27.160><c> where</c><00:04:27.440><c> my</c><00:04:27.759><c> actual</c><00:04:28.280><c> value</c>

00:04:28.790 --> 00:04:28.800 align:start position:0%
name of the field where my actual value
 

00:04:28.800 --> 00:04:30.310 align:start position:0%
name of the field where my actual value
that<00:04:28.960><c> I</c><00:04:29.120><c> want</c><00:04:29.240><c> to</c><00:04:29.400><c> enter</c>

00:04:30.310 --> 00:04:30.320 align:start position:0%
that I want to enter
 

00:04:30.320 --> 00:04:33.390 align:start position:0%
that I want to enter
later<00:04:30.639><c> on</c><00:04:31.240><c> when</c><00:04:31.440><c> logging</c><00:04:31.759><c> in</c><00:04:32.400><c> is</c><00:04:32.600><c> configured</c>

00:04:33.390 --> 00:04:33.400 align:start position:0%
later on when logging in is configured
 

00:04:33.400 --> 00:04:36.390 align:start position:0%
later on when logging in is configured
save<00:04:33.720><c> this</c><00:04:34.160><c> and</c><00:04:34.520><c> you</c><00:04:34.720><c> can</c><00:04:34.960><c> see</c><00:04:35.759><c> now</c><00:04:35.960><c> in</c><00:04:36.080><c> a</c><00:04:36.160><c> few</c>

00:04:36.390 --> 00:04:36.400 align:start position:0%
save this and you can see now in a few
 

00:04:36.400 --> 00:04:38.710 align:start position:0%
save this and you can see now in a few
seconds<00:04:36.960><c> that</c><00:04:37.199><c> the</c><00:04:37.680><c> database</c><00:04:38.240><c> needs</c><00:04:38.520><c> to</c>

00:04:38.710 --> 00:04:38.720 align:start position:0%
seconds that the database needs to
 

00:04:38.720 --> 00:04:41.390 align:start position:0%
seconds that the database needs to
restart<00:04:39.240><c> once</c><00:04:39.960><c> with</c><00:04:40.160><c> this</c><00:04:40.320><c> new</c><00:04:40.560><c> configuration</c>

00:04:41.390 --> 00:04:41.400 align:start position:0%
restart once with this new configuration
 

00:04:41.400 --> 00:04:45.110 align:start position:0%
restart once with this new configuration
and<00:04:41.720><c> once</c><00:04:42.560><c> this</c><00:04:42.720><c> is</c><00:04:42.919><c> done</c><00:04:43.360><c> if</c><00:04:43.880><c> we</c><00:04:44.880><c> let's</c>

00:04:45.110 --> 00:04:45.120 align:start position:0%
and once this is done if we let's
 

00:04:45.120 --> 00:04:46.430 align:start position:0%
and once this is done if we let's
actually<00:04:45.400><c> check</c><00:04:45.680><c> if</c><00:04:45.840><c> the</c><00:04:45.960><c> pot</c><00:04:46.160><c> already</c>

00:04:46.430 --> 00:04:46.440 align:start position:0%
actually check if the pot already
 

00:04:46.440 --> 00:04:48.790 align:start position:0%
actually check if the pot already
restarted<00:04:47.000><c> maybe</c><00:04:47.240><c> it</c><00:04:47.400><c> just</c><00:04:47.639><c> was</c><00:04:48.240><c> too</c><00:04:48.520><c> fast</c>

00:04:48.790 --> 00:04:48.800 align:start position:0%
restarted maybe it just was too fast
 

00:04:48.800 --> 00:04:50.790 align:start position:0%
restarted maybe it just was too fast
already<00:04:49.360><c> yeah</c><00:04:49.520><c> you</c><00:04:49.639><c> can</c><00:04:49.759><c> see</c><00:04:50.039><c> here</c><00:04:50.440><c> the</c><00:04:50.600><c> pot</c>

00:04:50.790 --> 00:04:50.800 align:start position:0%
already yeah you can see here the pot
 

00:04:50.800 --> 00:04:53.150 align:start position:0%
already yeah you can see here the pot
restarted<00:04:51.280><c> 16</c><00:04:51.600><c> seconds</c><00:04:51.880><c> ago</c><00:04:52.320><c> so</c><00:04:52.479><c> it</c><00:04:52.600><c> was</c><00:04:52.919><c> too</c>

00:04:53.150 --> 00:04:53.160 align:start position:0%
restarted 16 seconds ago so it was too
 

00:04:53.160 --> 00:04:55.070 align:start position:0%
restarted 16 seconds ago so it was too
fast<00:04:53.360><c> for</c><00:04:53.600><c> me</c><00:04:53.840><c> even</c><00:04:54.000><c> to</c><00:04:54.120><c> see</c><00:04:54.240><c> in</c><00:04:54.360><c> the</c><00:04:54.440><c> UI</c><00:04:54.919><c> and</c>

00:04:55.070 --> 00:04:55.080 align:start position:0%
fast for me even to see in the UI and
 

00:04:55.080 --> 00:04:57.590 align:start position:0%
fast for me even to see in the UI and
now<00:04:55.680><c> if</c><00:04:55.800><c> I</c><00:04:55.960><c> reload</c><00:04:56.440><c> the</c><00:04:56.600><c> dashboard</c><00:04:57.039><c> here</c><00:04:57.360><c> it's</c>

00:04:57.590 --> 00:04:57.600 align:start position:0%
now if I reload the dashboard here it's
 

00:04:57.600 --> 00:04:59.710 align:start position:0%
now if I reload the dashboard here it's
actually<00:04:57.880><c> ask</c><00:04:58.199><c> me</c><00:04:58.280><c> for</c><00:04:58.440><c> an</c><00:04:58.560><c> API</c><00:04:58.919><c> key</c><00:04:59.280><c> and</c><00:04:59.400><c> if</c><00:04:59.479><c> I</c>

00:04:59.710 --> 00:04:59.720 align:start position:0%
actually ask me for an API key and if I
 

00:04:59.720 --> 00:05:02.830 align:start position:0%
actually ask me for an API key and if I
enter<00:05:00.000><c> my</c><00:05:00.160><c> API</c><00:05:00.600><c> key</c><00:05:01.199><c> not</c><00:05:01.840><c> which</c><00:05:01.960><c> is</c><00:05:02.240><c> called</c><00:05:02.560><c> Top</c>

00:05:02.830 --> 00:05:02.840 align:start position:0%
enter my API key not which is called Top
 

00:05:02.840 --> 00:05:05.150 align:start position:0%
enter my API key not which is called Top
Secret<00:05:03.199><c> in</c><00:05:03.400><c> this</c><00:05:03.600><c> case</c><00:05:04.000><c> I</c><00:05:04.120><c> can</c><00:05:04.560><c> loog</c><00:05:04.800><c> in</c><00:05:04.919><c> into</c>

00:05:05.150 --> 00:05:05.160 align:start position:0%
Secret in this case I can loog in into
 

00:05:05.160 --> 00:05:07.870 align:start position:0%
Secret in this case I can loog in into
my<00:05:05.320><c> database</c><00:05:05.960><c> and</c><00:05:06.320><c> that</c><00:05:06.560><c> with</c><00:05:06.960><c> next</c><00:05:07.680><c> let's</c>

00:05:07.870 --> 00:05:07.880 align:start position:0%
my database and that with next let's
 

00:05:07.880 --> 00:05:09.749 align:start position:0%
my database and that with next let's
configure<00:05:08.360><c> a</c><00:05:08.479><c> TLS</c><00:05:08.880><c> certificate</c><00:05:09.360><c> you</c><00:05:09.479><c> can</c>

00:05:09.749 --> 00:05:09.759 align:start position:0%
configure a TLS certificate you can
 

00:05:09.759 --> 00:05:11.909 align:start position:0%
configure a TLS certificate you can
configure<00:05:10.240><c> TLS</c><00:05:10.560><c> certificates</c><00:05:11.080><c> the</c><00:05:11.199><c> same</c><00:05:11.520><c> way</c>

00:05:11.909 --> 00:05:11.919 align:start position:0%
configure TLS certificates the same way
 

00:05:11.919 --> 00:05:13.790 align:start position:0%
configure TLS certificates the same way
you<00:05:12.039><c> can</c><00:05:12.479><c> configure</c><00:05:12.880><c> API</c><00:05:13.280><c> keys</c><00:05:13.600><c> by</c>

00:05:13.790 --> 00:05:13.800 align:start position:0%
you can configure API keys by
 

00:05:13.800 --> 00:05:15.790 align:start position:0%
you can configure API keys by
referencing<00:05:14.520><c> a</c><00:05:14.639><c> secret</c><00:05:15.320><c> where</c><00:05:15.520><c> the</c>

00:05:15.790 --> 00:05:15.800 align:start position:0%
referencing a secret where the
 

00:05:15.800 --> 00:05:17.550 align:start position:0%
referencing a secret where the
certificate<00:05:16.360><c> and</c><00:05:16.639><c> certificate</c><00:05:17.120><c> key</c><00:05:17.400><c> are</c>

00:05:17.550 --> 00:05:17.560 align:start position:0%
certificate and certificate key are
 

00:05:17.560 --> 00:05:20.150 align:start position:0%
certificate and certificate key are
stored<00:05:17.960><c> in</c><00:05:18.440><c> again</c><00:05:18.720><c> you</c><00:05:18.919><c> don't</c><00:05:19.360><c> enter</c><00:05:19.720><c> the</c>

00:05:20.150 --> 00:05:20.160 align:start position:0%
stored in again you don't enter the
 

00:05:20.160 --> 00:05:21.870 align:start position:0%
stored in again you don't enter the
certificates<00:05:20.800><c> directly</c><00:05:21.240><c> here</c><00:05:21.479><c> you</c><00:05:21.680><c> just</c>

00:05:21.870 --> 00:05:21.880 align:start position:0%
certificates directly here you just
 

00:05:21.880 --> 00:05:22.950 align:start position:0%
certificates directly here you just
reference<00:05:22.240><c> a</c>

00:05:22.950 --> 00:05:22.960 align:start position:0%
reference a
 

00:05:22.960 --> 00:05:25.830 align:start position:0%
reference a
secret<00:05:23.960><c> so</c><00:05:24.120><c> let's</c><00:05:24.360><c> create</c><00:05:24.600><c> a</c><00:05:24.840><c> secret</c><00:05:25.280><c> first</c>

00:05:25.830 --> 00:05:25.840 align:start position:0%
secret so let's create a secret first
 

00:05:25.840 --> 00:05:28.830 align:start position:0%
secret so let's create a secret first
this<00:05:26.000><c> command</c><00:05:26.479><c> here</c><00:05:27.479><c> creates</c><00:05:27.880><c> a</c><00:05:28.039><c> secret</c>

00:05:28.830 --> 00:05:28.840 align:start position:0%
this command here creates a secret
 

00:05:28.840 --> 00:05:30.830 align:start position:0%
this command here creates a secret
called<00:05:29.160><c> quarantine</c><00:05:29.840><c> s</c><00:05:30.000><c> test</c><00:05:30.440><c> and</c><00:05:30.560><c> in</c><00:05:30.680><c> the</c>

00:05:30.830 --> 00:05:30.840 align:start position:0%
called quarantine s test and in the
 

00:05:30.840 --> 00:05:34.309 align:start position:0%
called quarantine s test and in the
secret<00:05:31.319><c> it</c><00:05:31.560><c> will</c><00:05:32.560><c> have</c><00:05:32.800><c> two</c><00:05:33.000><c> Fields</c><00:05:33.680><c> a</c><00:05:33.840><c> field</c>

00:05:34.309 --> 00:05:34.319 align:start position:0%
secret it will have two Fields a field
 

00:05:34.319 --> 00:05:35.270 align:start position:0%
secret it will have two Fields a field
called

00:05:35.270 --> 00:05:35.280 align:start position:0%
called
 

00:05:35.280 --> 00:05:39.390 align:start position:0%
called
CT<00:05:36.280><c> for</c><00:05:36.960><c> which</c><00:05:37.160><c> contains</c><00:05:37.919><c> the</c><00:05:38.919><c> TLs</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
CT for which contains the TLs
 

00:05:39.400 --> 00:05:41.430 align:start position:0%
CT for which contains the TLs
certificate<00:05:40.080><c> and</c><00:05:40.199><c> a</c><00:05:40.319><c> key</c><00:05:40.520><c> field</c><00:05:41.280><c> that</c>

00:05:41.430 --> 00:05:41.440 align:start position:0%
certificate and a key field that
 

00:05:41.440 --> 00:05:43.390 align:start position:0%
certificate and a key field that
contains<00:05:41.960><c> the</c><00:05:42.120><c> key</c><00:05:42.479><c> of</c><00:05:42.639><c> the</c><00:05:42.800><c> certificate</c>

00:05:43.390 --> 00:05:43.400 align:start position:0%
contains the key of the certificate
 

00:05:43.400 --> 00:05:45.150 align:start position:0%
contains the key of the certificate
again<00:05:43.759><c> there</c><00:05:43.880><c> are</c><00:05:44.160><c> multiple</c><00:05:44.600><c> other</c><00:05:44.800><c> ways</c><00:05:45.000><c> to</c>

00:05:45.150 --> 00:05:45.160 align:start position:0%
again there are multiple other ways to
 

00:05:45.160 --> 00:05:47.189 align:start position:0%
again there are multiple other ways to
also<00:05:45.400><c> create</c><00:05:45.800><c> cubern</c><00:05:46.360><c> secrets</c><00:05:46.800><c> with</c><00:05:46.919><c> a</c><00:05:47.039><c> tool</c>

00:05:47.189 --> 00:05:47.199 align:start position:0%
also create cubern secrets with a tool
 

00:05:47.199 --> 00:05:47.870 align:start position:0%
also create cubern secrets with a tool
of

00:05:47.870 --> 00:05:47.880 align:start position:0%
of
 

00:05:47.880 --> 00:05:50.870 align:start position:0%
of
FS<00:05:48.880><c> let's</c><00:05:49.080><c> have</c><00:05:49.160><c> a</c><00:05:49.319><c> look</c><00:05:49.720><c> actually</c><00:05:50.360><c> at</c><00:05:50.639><c> the</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
FS let's have a look actually at the
 

00:05:50.880 --> 00:05:53.950 align:start position:0%
FS let's have a look actually at the
secret<00:05:51.600><c> and</c><00:05:52.080><c> what</c><00:05:52.199><c> it</c><00:05:52.520><c> contains</c><00:05:53.520><c> here</c><00:05:53.680><c> you</c><00:05:53.800><c> can</c>

00:05:53.950 --> 00:05:53.960 align:start position:0%
secret and what it contains here you can
 

00:05:53.960 --> 00:05:56.390 align:start position:0%
secret and what it contains here you can
see<00:05:54.479><c> the</c><00:05:54.800><c> actual</c><00:05:55.160><c> Yama</c><00:05:55.479><c> specification</c><00:05:56.120><c> of</c><00:05:56.240><c> the</c>

00:05:56.390 --> 00:05:56.400 align:start position:0%
see the actual Yama specification of the
 

00:05:56.400 --> 00:05:59.309 align:start position:0%
see the actual Yama specification of the
quants<00:05:57.120><c> test</c><00:05:57.520><c> secret</c><00:05:58.520><c> and</c><00:05:58.680><c> you</c><00:05:58.800><c> can</c><00:05:58.919><c> see</c><00:05:59.160><c> two</c>

00:05:59.309 --> 00:05:59.319 align:start position:0%
quants test secret and you can see two
 

00:05:59.319 --> 00:06:02.710 align:start position:0%
quants test secret and you can see two
fields<00:05:59.759><c> TLS</c><00:06:00.400><c> CRT</c><00:06:00.840><c> and</c><00:06:00.960><c> ts.</c><00:06:01.560><c> key</c><00:06:02.120><c> with</c><00:06:02.319><c> the</c>

00:06:02.710 --> 00:06:02.720 align:start position:0%
fields TLS CRT and ts. key with the
 

00:06:02.720 --> 00:06:04.550 align:start position:0%
fields TLS CRT and ts. key with the
respective<00:06:03.319><c> certificating</c>

00:06:04.550 --> 00:06:04.560 align:start position:0%
respective certificating
 

00:06:04.560 --> 00:06:07.230 align:start position:0%
respective certificating
keys<00:06:05.560><c> so</c><00:06:05.759><c> let's</c><00:06:05.960><c> enter</c><00:06:06.360><c> this</c><00:06:06.560><c> information</c>

00:06:07.230 --> 00:06:07.240 align:start position:0%
keys so let's enter this information
 

00:06:07.240 --> 00:06:10.270 align:start position:0%
keys so let's enter this information
inside<00:06:07.680><c> of</c><00:06:07.759><c> the</c><00:06:07.880><c> quent</c><00:06:08.599><c> UI</c><00:06:09.599><c> so</c><00:06:09.960><c> that</c><00:06:10.120><c> the</c>

00:06:10.270 --> 00:06:10.280 align:start position:0%
inside of the quent UI so that the
 

00:06:10.280 --> 00:06:12.950 align:start position:0%
inside of the quent UI so that the
secret<00:06:10.599><c> is</c><00:06:10.759><c> pick</c><00:06:10.960><c> up</c><00:06:11.599><c> and</c><00:06:11.759><c> TLS</c><00:06:12.199><c> is</c><00:06:12.360><c> configured</c>

00:06:12.950 --> 00:06:12.960 align:start position:0%
secret is pick up and TLS is configured
 

00:06:12.960 --> 00:06:14.909 align:start position:0%
secret is pick up and TLS is configured
so<00:06:13.120><c> it's</c><00:06:13.199><c> the</c><00:06:13.360><c> secret</c><00:06:13.680><c> name</c><00:06:13.919><c> for</c><00:06:14.319><c> both</c><00:06:14.800><c> the</c>

00:06:14.909 --> 00:06:14.919 align:start position:0%
so it's the secret name for both the
 

00:06:14.919 --> 00:06:18.070 align:start position:0%
so it's the secret name for both the
certificate<00:06:15.360><c> and</c><00:06:15.440><c> the</c><00:06:15.599><c> key</c><00:06:15.759><c> is</c><00:06:15.919><c> SC</c><00:06:16.160><c> in</c><00:06:16.599><c> TLS</c>

00:06:18.070 --> 00:06:18.080 align:start position:0%
certificate and the key is SC in TLS
 

00:06:18.080 --> 00:06:20.990 align:start position:0%
certificate and the key is SC in TLS
test<00:06:19.080><c> and</c><00:06:19.280><c> then</c><00:06:19.440><c> the</c><00:06:19.639><c> certificate</c><00:06:20.479><c> is</c><00:06:20.599><c> stored</c>

00:06:20.990 --> 00:06:21.000 align:start position:0%
test and then the certificate is stored
 

00:06:21.000 --> 00:06:25.870 align:start position:0%
test and then the certificate is stored
in<00:06:21.160><c> the</c><00:06:21.360><c> ts.</c><00:06:22.440><c> CRT</c><00:06:23.720><c> field</c><00:06:24.720><c> and</c><00:06:25.199><c> the</c><00:06:25.400><c> key</c><00:06:25.720><c> is</c>

00:06:25.870 --> 00:06:25.880 align:start position:0%
in the ts. CRT field and the key is
 

00:06:25.880 --> 00:06:28.390 align:start position:0%
in the ts. CRT field and the key is
stored<00:06:26.280><c> in</c><00:06:26.360><c> the</c><00:06:26.479><c> TLs</c>

00:06:28.390 --> 00:06:28.400 align:start position:0%
stored in the TLs
 

00:06:28.400 --> 00:06:30.430 align:start position:0%
stored in the TLs
dokey<00:06:29.400><c> again</c>

00:06:30.430 --> 00:06:30.440 align:start position:0%
dokey again
 

00:06:30.440 --> 00:06:34.390 align:start position:0%
dokey again
let's<00:06:30.680><c> save</c><00:06:30.960><c> all</c><00:06:31.199><c> changes</c><00:06:31.759><c> and</c><00:06:32.160><c> the</c><00:06:32.880><c> quadrant</c>

00:06:34.390 --> 00:06:34.400 align:start position:0%
let's save all changes and the quadrant
 

00:06:34.400 --> 00:06:36.950 align:start position:0%
let's save all changes and the quadrant
database<00:06:35.400><c> will</c><00:06:35.800><c> restart</c><00:06:36.360><c> with</c><00:06:36.479><c> a</c><00:06:36.639><c> new</c>

00:06:36.950 --> 00:06:36.960 align:start position:0%
database will restart with a new
 

00:06:36.960 --> 00:06:40.230 align:start position:0%
database will restart with a new
configuration<00:06:37.720><c> again</c><00:06:38.080><c> in</c><00:06:38.240><c> a</c><00:06:38.360><c> few</c><00:06:39.080><c> seconds</c><00:06:40.080><c> you</c>

00:06:40.230 --> 00:06:40.240 align:start position:0%
configuration again in a few seconds you
 

00:06:40.240 --> 00:06:42.510 align:start position:0%
configuration again in a few seconds you
can<00:06:40.400><c> see</c><00:06:40.960><c> it's</c><00:06:41.199><c> terminating</c>

00:06:42.510 --> 00:06:42.520 align:start position:0%
can see it's terminating
 

00:06:42.520 --> 00:06:45.230 align:start position:0%
can see it's terminating
now<00:06:43.520><c> and</c><00:06:44.319><c> new</c><00:06:44.520><c> container</c><00:06:44.919><c> with</c><00:06:45.080><c> new</c>

00:06:45.230 --> 00:06:45.240 align:start position:0%
now and new container with new
 

00:06:45.240 --> 00:06:48.710 align:start position:0%
now and new container with new
configuration<00:06:45.840><c> is</c><00:06:46.039><c> created</c><00:06:47.199><c> and</c><00:06:48.199><c> in</c><00:06:48.360><c> a</c><00:06:48.479><c> few</c>

00:06:48.710 --> 00:06:48.720 align:start position:0%
configuration is created and in a few
 

00:06:48.720 --> 00:06:51.950 align:start position:0%
configuration is created and in a few
seconds<00:06:49.360><c> more</c><00:06:50.360><c> it's</c><00:06:50.560><c> up</c><00:06:50.720><c> and</c><00:06:50.880><c> running</c><00:06:51.599><c> now</c><00:06:51.759><c> we</c>

00:06:51.950 --> 00:06:51.960 align:start position:0%
seconds more it's up and running now we
 

00:06:51.960 --> 00:06:53.790 align:start position:0%
seconds more it's up and running now we
just<00:06:52.120><c> have</c><00:06:52.240><c> to</c><00:06:52.400><c> wait</c><00:06:52.599><c> until</c><00:06:52.800><c> the</c><00:06:52.919><c> health</c><00:06:53.160><c> check</c>

00:06:53.790 --> 00:06:53.800 align:start position:0%
just have to wait until the health check
 

00:06:53.800 --> 00:06:57.550 align:start position:0%
just have to wait until the health check
is<00:06:54.000><c> ready</c><00:06:54.639><c> and</c><00:06:54.919><c> it's</c><00:06:55.120><c> done</c><00:06:55.479><c> and</c><00:06:55.680><c> now</c><00:06:56.000><c> if</c><00:06:56.160><c> we</c>

00:06:57.550 --> 00:06:57.560 align:start position:0%
is ready and it's done and now if we
 

00:06:57.560 --> 00:07:00.270 align:start position:0%
is ready and it's done and now if we
refresh<00:06:58.560><c> here</c><00:06:59.000><c> our</c><00:06:59.360><c> page</c><00:06:59.800><c> and</c><00:06:59.919><c> we</c><00:07:00.080><c> actually</c>

00:07:00.270 --> 00:07:00.280 align:start position:0%
refresh here our page and we actually
 

00:07:00.280 --> 00:07:03.150 align:start position:0%
refresh here our page and we actually
get<00:07:00.599><c> a</c><00:07:01.599><c> bad</c><00:07:01.879><c> response</c><00:07:02.360><c> because</c><00:07:02.720><c> now</c><00:07:02.879><c> we</c><00:07:03.039><c> have</c>

00:07:03.150 --> 00:07:03.160 align:start position:0%
get a bad response because now we have
 

00:07:03.160 --> 00:07:06.550 align:start position:0%
get a bad response because now we have
to<00:07:03.319><c> go</c><00:07:03.479><c> to</c><00:07:03.879><c> htps</c><00:07:04.879><c> for</c><00:07:05.039><c> the</c><00:07:05.199><c> dashboard</c><00:07:06.080><c> and</c><00:07:06.240><c> also</c>

00:07:06.550 --> 00:07:06.560 align:start position:0%
to go to htps for the dashboard and also
 

00:07:06.560 --> 00:07:08.189 align:start position:0%
to go to htps for the dashboard and also
the<00:07:06.680><c> link</c><00:07:07.000><c> here</c><00:07:07.240><c> actually</c><00:07:07.560><c> changed</c><00:07:07.919><c> so</c><00:07:08.080><c> this</c>

00:07:08.189 --> 00:07:08.199 align:start position:0%
the link here actually changed so this
 

00:07:08.199 --> 00:07:10.950 align:start position:0%
the link here actually changed so this
is<00:07:08.319><c> notp</c><00:07:09.000><c> but</c><00:07:09.120><c> https</c><00:07:09.720><c> anymore</c><00:07:10.199><c> we</c><00:07:10.400><c> now</c><00:07:10.639><c> get</c><00:07:10.800><c> a</c>

00:07:10.950 --> 00:07:10.960 align:start position:0%
is notp but https anymore we now get a
 

00:07:10.960 --> 00:07:12.830 align:start position:0%
is notp but https anymore we now get a
certificate<00:07:11.520><c> warning</c><00:07:12.120><c> inside</c><00:07:12.479><c> of</c><00:07:12.680><c> the</c>

00:07:12.830 --> 00:07:12.840 align:start position:0%
certificate warning inside of the
 

00:07:12.840 --> 00:07:15.029 align:start position:0%
certificate warning inside of the
browser<00:07:13.599><c> this</c><00:07:13.720><c> is</c><00:07:13.919><c> because</c><00:07:14.280><c> I</c><00:07:14.440><c> just</c><00:07:14.680><c> used</c><00:07:14.919><c> a</c>

00:07:15.029 --> 00:07:15.039 align:start position:0%
browser this is because I just used a
 

00:07:15.039 --> 00:07:16.670 align:start position:0%
browser this is because I just used a
self<00:07:15.360><c> sign</c><00:07:15.599><c> certificate</c><00:07:16.240><c> that</c><00:07:16.360><c> is</c><00:07:16.520><c> not</c>

00:07:16.670 --> 00:07:16.680 align:start position:0%
self sign certificate that is not
 

00:07:16.680 --> 00:07:18.790 align:start position:0%
self sign certificate that is not
created<00:07:17.080><c> from</c><00:07:17.240><c> the</c><00:07:17.400><c> ca</c><00:07:18.120><c> that</c><00:07:18.280><c> my</c><00:07:18.440><c> browser</c>

00:07:18.790 --> 00:07:18.800 align:start position:0%
created from the ca that my browser
 

00:07:18.800 --> 00:07:20.749 align:start position:0%
created from the ca that my browser
passs<00:07:19.319><c> and</c><00:07:19.440><c> this</c><00:07:19.560><c> is</c><00:07:19.680><c> just</c><00:07:19.800><c> for</c><00:07:19.960><c> demo</c><00:07:20.280><c> purposes</c>

00:07:20.749 --> 00:07:20.759 align:start position:0%
passs and this is just for demo purposes
 

00:07:20.759 --> 00:07:22.990 align:start position:0%
passs and this is just for demo purposes
then<00:07:20.919><c> I</c><00:07:21.039><c> can</c><00:07:21.240><c> loog</c><00:07:21.440><c> in</c><00:07:21.639><c> again</c><00:07:22.000><c> by</c><00:07:22.120><c> the</c><00:07:22.240><c> way</c><00:07:22.800><c> now</c>

00:07:22.990 --> 00:07:23.000 align:start position:0%
then I can loog in again by the way now
 

00:07:23.000 --> 00:07:25.390 align:start position:0%
then I can loog in again by the way now
that<00:07:23.160><c> we</c><00:07:23.280><c> have</c><00:07:23.639><c> configured</c><00:07:24.120><c> an</c><00:07:24.280><c> API</c><00:07:24.759><c> key</c><00:07:25.240><c> and</c>

00:07:25.390 --> 00:07:25.400 align:start position:0%
that we have configured an API key and
 

00:07:25.400 --> 00:07:28.029 align:start position:0%
that we have configured an API key and
we<00:07:25.560><c> configured</c><00:07:26.000><c> TLS</c><00:07:26.680><c> we</c><00:07:26.800><c> can</c><00:07:27.080><c> also</c><00:07:27.639><c> create</c>

00:07:28.029 --> 00:07:28.039 align:start position:0%
we configured TLS we can also create
 

00:07:28.039 --> 00:07:31.469 align:start position:0%
we configured TLS we can also create
more<00:07:28.360><c> granular</c><00:07:29.039><c> JW</c><00:07:29.800><c> tokens</c><00:07:30.400><c> inside</c><00:07:30.720><c> of</c><00:07:30.840><c> our</c>

00:07:31.469 --> 00:07:31.479 align:start position:0%
more granular JW tokens inside of our
 

00:07:31.479 --> 00:07:34.830 align:start position:0%
more granular JW tokens inside of our
database<00:07:32.479><c> um</c><00:07:32.800><c> in</c><00:07:32.919><c> order</c><00:07:33.240><c> to</c><00:07:33.520><c> Showcase</c><00:07:34.199><c> this</c>

00:07:34.830 --> 00:07:34.840 align:start position:0%
database um in order to Showcase this
 

00:07:34.840 --> 00:07:39.710 align:start position:0%
database um in order to Showcase this
let's<00:07:35.560><c> actually</c><00:07:36.560><c> um</c><00:07:36.919><c> creating</c><00:07:37.680><c> some</c><00:07:38.199><c> data</c><00:07:39.199><c> so</c>

00:07:39.710 --> 00:07:39.720 align:start position:0%
let's actually um creating some data so
 

00:07:39.720 --> 00:07:41.309 align:start position:0%
let's actually um creating some data so
let's<00:07:39.919><c> go</c><00:07:40.039><c> to</c><00:07:40.199><c> the</c><00:07:40.319><c> quick</c><00:07:40.560><c> start</c><00:07:40.919><c> and</c><00:07:41.080><c> let's</c>

00:07:41.309 --> 00:07:41.319 align:start position:0%
let's go to the quick start and let's
 

00:07:41.319 --> 00:07:44.629 align:start position:0%
let's go to the quick start and let's
maybe<00:07:42.199><c> create</c><00:07:42.720><c> two</c><00:07:43.360><c> collections</c><00:07:44.080><c> one</c><00:07:44.360><c> called</c>

00:07:44.629 --> 00:07:44.639 align:start position:0%
maybe create two collections one called
 

00:07:44.639 --> 00:07:46.629 align:start position:0%
maybe create two collections one called
star<00:07:45.000><c> shards</c><00:07:45.400><c> and</c><00:07:45.560><c> one</c><00:07:45.800><c> called</c><00:07:46.120><c> te</c><00:07:46.440><c> and</c><00:07:46.520><c> you</c>

00:07:46.629 --> 00:07:46.639 align:start position:0%
star shards and one called te and you
 

00:07:46.639 --> 00:07:48.749 align:start position:0%
star shards and one called te and you
can<00:07:46.759><c> see</c><00:07:47.000><c> with</c><00:07:47.159><c> my</c><00:07:47.319><c> admin</c><00:07:47.759><c> key</c><00:07:48.440><c> that</c><00:07:48.599><c> I</c>

00:07:48.749 --> 00:07:48.759 align:start position:0%
can see with my admin key that I
 

00:07:48.759 --> 00:07:51.510 align:start position:0%
can see with my admin key that I
configured<00:07:49.479><c> I</c><00:07:49.560><c> have</c><00:07:49.800><c> access</c><00:07:50.039><c> to</c><00:07:50.520><c> everything</c>

00:07:51.510 --> 00:07:51.520 align:start position:0%
configured I have access to everything
 

00:07:51.520 --> 00:07:53.550 align:start position:0%
configured I have access to everything
can<00:07:51.720><c> now</c><00:07:52.000><c> go</c><00:07:52.199><c> to</c><00:07:52.360><c> the</c><00:07:52.520><c> access</c><00:07:52.840><c> token</c><00:07:53.199><c> section</c>

00:07:53.550 --> 00:07:53.560 align:start position:0%
can now go to the access token section
 

00:07:53.560 --> 00:07:55.950 align:start position:0%
can now go to the access token section
here<00:07:53.680><c> in</c><00:07:53.759><c> the</c><00:07:53.879><c> UI</c><00:07:54.680><c> and</c><00:07:54.879><c> create</c><00:07:55.199><c> more</c><00:07:55.400><c> granular</c>

00:07:55.950 --> 00:07:55.960 align:start position:0%
here in the UI and create more granular
 

00:07:55.960 --> 00:07:58.990 align:start position:0%
here in the UI and create more granular
tokens<00:07:56.800><c> that</c><00:07:56.960><c> can</c><00:07:57.240><c> either</c><00:07:58.240><c> give</c><00:07:58.479><c> a</c><00:07:58.639><c> user</c>

00:07:58.990 --> 00:07:59.000 align:start position:0%
tokens that can either give a user
 

00:07:59.000 --> 00:08:03.270 align:start position:0%
tokens that can either give a user
global<00:07:59.759><c> access</c><00:08:00.759><c> or</c><00:08:01.159><c> maybe</c><00:08:01.560><c> only</c><00:08:02.159><c> access</c><00:08:02.840><c> to</c>

00:08:03.270 --> 00:08:03.280 align:start position:0%
global access or maybe only access to
 

00:08:03.280 --> 00:08:06.070 align:start position:0%
global access or maybe only access to
certain<00:08:03.720><c> collections</c><00:08:04.479><c> so</c><00:08:04.680><c> for</c><00:08:04.919><c> example</c><00:08:05.759><c> let's</c>

00:08:06.070 --> 00:08:06.080 align:start position:0%
certain collections so for example let's
 

00:08:06.080 --> 00:08:08.790 align:start position:0%
certain collections so for example let's
choose<00:08:06.599><c> our</c><00:08:07.479><c> Star</c><00:08:07.800><c> Charts</c><00:08:08.199><c> collection</c><00:08:08.639><c> and</c>

00:08:08.790 --> 00:08:08.800 align:start position:0%
choose our Star Charts collection and
 

00:08:08.800 --> 00:08:11.029 align:start position:0%
choose our Star Charts collection and
allow<00:08:09.120><c> access</c><00:08:09.400><c> to</c><00:08:09.639><c> this</c><00:08:10.280><c> and</c><00:08:10.440><c> I</c><00:08:10.560><c> also</c><00:08:10.800><c> want</c><00:08:10.919><c> to</c>

00:08:11.029 --> 00:08:11.039 align:start position:0%
allow access to this and I also want to
 

00:08:11.039 --> 00:08:12.629 align:start position:0%
allow access to this and I also want to
allow<00:08:11.319><c> right</c>

00:08:12.629 --> 00:08:12.639 align:start position:0%
allow right
 

00:08:12.639 --> 00:08:17.149 align:start position:0%
allow right
operations<00:08:13.639><c> and</c><00:08:13.960><c> click</c><00:08:14.319><c> save</c><00:08:15.319><c> and</c><00:08:15.560><c> see</c><00:08:16.159><c> now</c>

00:08:17.149 --> 00:08:17.159 align:start position:0%
operations and click save and see now
 

00:08:17.159 --> 00:08:19.670 align:start position:0%
operations and click save and see now
what<00:08:17.360><c> is</c><00:08:17.520><c> configured</c><00:08:17.960><c> in</c><00:08:18.080><c> the</c><00:08:18.159><c> JWT</c><00:08:18.759><c> token</c><00:08:19.520><c> and</c>

00:08:19.670 --> 00:08:19.680 align:start position:0%
what is configured in the JWT token and
 

00:08:19.680 --> 00:08:22.149 align:start position:0%
what is configured in the JWT token and
if<00:08:19.840><c> I</c><00:08:20.000><c> now</c><00:08:20.520><c> copy</c><00:08:21.080><c> this</c><00:08:21.360><c> token</c><00:08:21.720><c> that</c><00:08:21.840><c> has</c><00:08:21.960><c> been</c>

00:08:22.149 --> 00:08:22.159 align:start position:0%
if I now copy this token that has been
 

00:08:22.159 --> 00:08:24.909 align:start position:0%
if I now copy this token that has been
generated<00:08:22.720><c> here</c><00:08:23.599><c> and</c><00:08:23.720><c> if</c><00:08:23.800><c> I</c><00:08:24.000><c> go</c><00:08:24.280><c> back</c><00:08:24.560><c> to</c><00:08:24.720><c> my</c>

00:08:24.909 --> 00:08:24.919 align:start position:0%
generated here and if I go back to my
 

00:08:24.919 --> 00:08:26.990 align:start position:0%
generated here and if I go back to my
collections<00:08:25.520><c> where</c><00:08:25.639><c> I</c><00:08:25.720><c> can</c><00:08:25.919><c> still</c><00:08:26.159><c> see</c><00:08:26.560><c> both</c>

00:08:26.990 --> 00:08:27.000 align:start position:0%
collections where I can still see both
 

00:08:27.000 --> 00:08:29.029 align:start position:0%
collections where I can still see both
because<00:08:27.199><c> I'm</c><00:08:27.319><c> logged</c><00:08:27.560><c> in</c><00:08:27.680><c> as</c><00:08:27.759><c> an</c><00:08:27.919><c> admin</c><00:08:28.720><c> and</c><00:08:28.879><c> I</c>

00:08:29.029 --> 00:08:29.039 align:start position:0%
because I'm logged in as an admin and I
 

00:08:29.039 --> 00:08:32.790 align:start position:0%
because I'm logged in as an admin and I
now<00:08:29.520><c> Lo</c><00:08:29.720><c> in</c><00:08:30.080><c> with</c><00:08:30.479><c> this</c><00:08:30.639><c> JWT</c><00:08:31.240><c> token</c><00:08:32.039><c> you</c><00:08:32.200><c> can</c>

00:08:32.790 --> 00:08:32.800 align:start position:0%
now Lo in with this JWT token you can
 

00:08:32.800 --> 00:08:36.350 align:start position:0%
now Lo in with this JWT token you can
see<00:08:33.800><c> uh</c><00:08:34.000><c> I</c><00:08:34.200><c> only</c><00:08:35.120><c> get</c><00:08:35.399><c> access</c><00:08:35.680><c> to</c><00:08:35.880><c> the</c><00:08:36.039><c> star</c>

00:08:36.350 --> 00:08:36.360 align:start position:0%
see uh I only get access to the star
 

00:08:36.360 --> 00:08:37.870 align:start position:0%
see uh I only get access to the star
shards<00:08:36.599><c> and</c><00:08:36.719><c> not</c><00:08:36.839><c> the</c><00:08:36.959><c> test</c><00:08:37.159><c> collection</c><00:08:37.719><c> and</c>

00:08:37.870 --> 00:08:37.880 align:start position:0%
shards and not the test collection and
 

00:08:37.880 --> 00:08:39.509 align:start position:0%
shards and not the test collection and
there<00:08:37.959><c> are</c><00:08:38.279><c> and</c><00:08:38.519><c> also</c><00:08:38.839><c> don't</c><00:08:39.039><c> have</c><00:08:39.240><c> the</c>

00:08:39.509 --> 00:08:39.519 align:start position:0%
there are and also don't have the
 

00:08:39.519 --> 00:08:42.550 align:start position:0%
there are and also don't have the
permissions<00:08:40.039><c> to</c><00:08:40.279><c> actually</c><00:08:40.640><c> create</c><00:08:41.000><c> new</c>

00:08:42.550 --> 00:08:42.560 align:start position:0%
permissions to actually create new
 

00:08:42.560 --> 00:08:45.590 align:start position:0%
permissions to actually create new
Js<00:08:43.560><c> and</c><00:08:44.320><c> there</c><00:08:44.399><c> are</c><00:08:44.560><c> multiple</c><00:08:44.959><c> more</c><00:08:45.160><c> options</c>

00:08:45.590 --> 00:08:45.600 align:start position:0%
Js and there are multiple more options
 

00:08:45.600 --> 00:08:47.230 align:start position:0%
Js and there are multiple more options
that<00:08:45.720><c> you</c><00:08:45.800><c> can</c><00:08:45.959><c> do</c><00:08:46.240><c> there</c><00:08:46.519><c> you</c><00:08:46.640><c> can</c><00:08:46.920><c> create</c>

00:08:47.230 --> 00:08:47.240 align:start position:0%
that you can do there you can create
 

00:08:47.240 --> 00:08:50.150 align:start position:0%
that you can do there you can create
payload<00:08:47.680><c> fi</c><00:08:48.000><c> filters</c><00:08:48.600><c> you</c><00:08:48.720><c> can</c><00:08:49.000><c> have</c><00:08:49.519><c> readon</c>

00:08:50.150 --> 00:08:50.160 align:start position:0%
payload fi filters you can have readon
 

00:08:50.160 --> 00:08:52.389 align:start position:0%
payload fi filters you can have readon
tokens<00:08:50.839><c> and</c><00:08:51.040><c> you</c><00:08:51.160><c> can</c><00:08:51.399><c> create</c><00:08:51.680><c> a</c><00:08:51.920><c> granular</c>

00:08:52.389 --> 00:08:52.399 align:start position:0%
tokens and you can create a granular
 

00:08:52.399 --> 00:08:55.829 align:start position:0%
tokens and you can create a granular
access<00:08:53.040><c> control</c><00:08:53.560><c> system</c><00:08:54.120><c> that</c><00:08:54.320><c> matches</c><00:08:54.839><c> yours</c>

00:08:55.829 --> 00:08:55.839 align:start position:0%
access control system that matches yours
 

00:08:55.839 --> 00:08:58.590 align:start position:0%
access control system that matches yours
and<00:08:56.000><c> that's</c><00:08:56.200><c> it</c><00:08:56.720><c> so</c><00:08:56.959><c> what</c><00:08:57.080><c> we</c><00:08:57.200><c> did</c><00:08:57.800><c> do</c><00:08:58.120><c> is</c><00:08:58.399><c> we</c>

00:08:58.590 --> 00:08:58.600 align:start position:0%
and that's it so what we did do is we
 

00:08:58.600 --> 00:09:02.870 align:start position:0%
and that's it so what we did do is we
created<00:08:59.760><c> a</c><00:08:59.920><c> low</c><00:09:00.160><c> balancer</c><00:09:00.720><c> for</c><00:09:01.519><c> our</c><00:09:02.519><c> hybrid</c>

00:09:02.870 --> 00:09:02.880 align:start position:0%
created a low balancer for our hybrid
 

00:09:02.880 --> 00:09:06.590 align:start position:0%
created a low balancer for our hybrid
Cloud<00:09:03.440><c> database</c><00:09:04.040><c> cluster</c><00:09:05.040><c> we</c><00:09:05.839><c> explored</c><00:09:06.440><c> how</c>

00:09:06.590 --> 00:09:06.600 align:start position:0%
Cloud database cluster we explored how
 

00:09:06.600 --> 00:09:08.509 align:start position:0%
Cloud database cluster we explored how
we<00:09:06.720><c> can</c><00:09:06.959><c> configure</c><00:09:07.399><c> this</c><00:09:07.560><c> low</c><00:09:07.800><c> balancer</c><00:09:08.320><c> to</c>

00:09:08.509 --> 00:09:08.519 align:start position:0%
we can configure this low balancer to
 

00:09:08.519 --> 00:09:11.030 align:start position:0%
we can configure this low balancer to
make<00:09:08.680><c> it</c><00:09:08.959><c> internal</c><00:09:09.440><c> to</c><00:09:09.680><c> restrict</c><00:09:10.160><c> access</c><00:09:10.600><c> to</c>

00:09:11.030 --> 00:09:11.040 align:start position:0%
make it internal to restrict access to
 

00:09:11.040 --> 00:09:12.870 align:start position:0%
make it internal to restrict access to
give<00:09:11.240><c> different</c><00:09:11.519><c> names</c><00:09:11.760><c> to</c><00:09:11.880><c> it</c><00:09:12.040><c> for</c><00:09:12.240><c> example</c>

00:09:12.870 --> 00:09:12.880 align:start position:0%
give different names to it for example
 

00:09:12.880 --> 00:09:16.710 align:start position:0%
give different names to it for example
we<00:09:13.079><c> created</c><00:09:13.959><c> an</c><00:09:14.279><c> admin</c><00:09:14.680><c> API</c><00:09:15.120><c> key</c><00:09:16.040><c> that</c><00:09:16.200><c> we</c>

00:09:16.710 --> 00:09:16.720 align:start position:0%
we created an admin API key that we
 

00:09:16.720 --> 00:09:19.030 align:start position:0%
we created an admin API key that we
referenced<00:09:17.519><c> in</c><00:09:17.800><c> through</c><00:09:18.000><c> a</c><00:09:18.120><c> secret</c><00:09:18.600><c> insert</c><00:09:18.920><c> of</c>

00:09:19.030 --> 00:09:19.040 align:start position:0%
referenced in through a secret insert of
 

00:09:19.040 --> 00:09:21.110 align:start position:0%
referenced in through a secret insert of
our<00:09:19.200><c> configuration</c><00:09:20.000><c> we</c><00:09:20.120><c> can</c><00:09:20.560><c> Rec</c><00:09:20.760><c> configured</c>

00:09:21.110 --> 00:09:21.120 align:start position:0%
our configuration we can Rec configured
 

00:09:21.120 --> 00:09:23.910 align:start position:0%
our configuration we can Rec configured
a<00:09:21.200><c> TLS</c><00:09:21.519><c> certificate</c><00:09:22.000><c> in</c><00:09:22.120><c> our</c><00:09:22.360><c> database</c><00:09:23.079><c> and</c><00:09:23.560><c> we</c>

00:09:23.910 --> 00:09:23.920 align:start position:0%
a TLS certificate in our database and we
 

00:09:23.920 --> 00:09:26.710 align:start position:0%
a TLS certificate in our database and we
also<00:09:24.360><c> explored</c><00:09:25.040><c> JWT</c><00:09:25.680><c> tokens</c><00:09:26.120><c> and</c><00:09:26.279><c> how</c><00:09:26.440><c> you</c><00:09:26.560><c> can</c>

00:09:26.710 --> 00:09:26.720 align:start position:0%
also explored JWT tokens and how you can
 

00:09:26.720 --> 00:09:29.509 align:start position:0%
also explored JWT tokens and how you can
use<00:09:27.000><c> them</c><00:09:27.560><c> to</c><00:09:27.760><c> support</c><00:09:28.120><c> granual</c><00:09:28.560><c> access</c><00:09:29.360><c> thank</c>

00:09:29.509 --> 00:09:29.519 align:start position:0%
use them to support granual access thank
 

00:09:29.519 --> 00:09:32.519 align:start position:0%
use them to support granual access thank
you

