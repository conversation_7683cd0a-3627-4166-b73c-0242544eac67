WEBVTT
Kind: captions
Language: en

00:00:04.319 --> 00:00:05.670 align:start position:0%
 
hello<00:00:04.640><c> everyone</c><00:00:05.040><c> i'm</c><00:00:05.279><c> fern</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
hello everyone i'm fern
 

00:00:05.680 --> 00:00:07.510 align:start position:0%
hello everyone i'm fern
now<00:00:05.920><c> an</c><00:00:06.080><c> assistant</c><00:00:06.480><c> professor</c><00:00:06.960><c> of</c><00:00:07.120><c> marketing</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
now an assistant professor of marketing
 

00:00:07.520 --> 00:00:09.509 align:start position:0%
now an assistant professor of marketing
at<00:00:07.680><c> jordan</c><00:00:08.080><c> university</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
at jordan university
 

00:00:09.519 --> 00:00:11.350 align:start position:0%
at jordan university
as<00:00:09.679><c> well</c><00:00:09.920><c> as</c><00:00:10.080><c> a</c><00:00:10.160><c> research</c><00:00:10.480><c> fellow</c><00:00:10.880><c> of</c><00:00:11.040><c> water</c>

00:00:11.350 --> 00:00:11.360 align:start position:0%
as well as a research fellow of water
 

00:00:11.360 --> 00:00:13.190 align:start position:0%
as well as a research fellow of water
neuroscience<00:00:12.000><c> initiative</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
neuroscience initiative
 

00:00:13.200 --> 00:00:15.110 align:start position:0%
neuroscience initiative
today<00:00:13.440><c> i'm</c><00:00:13.679><c> going</c><00:00:13.840><c> to</c><00:00:13.920><c> talk</c><00:00:14.160><c> about</c><00:00:14.559><c> how</c><00:00:14.719><c> we</c><00:00:14.880><c> can</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
today i'm going to talk about how we can
 

00:00:15.120 --> 00:00:16.870 align:start position:0%
today i'm going to talk about how we can
detect<00:00:15.599><c> a</c><00:00:15.759><c> private</c><00:00:16.080><c> virus</c><00:00:16.400><c> in</c><00:00:16.480><c> decision</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
detect a private virus in decision
 

00:00:16.880 --> 00:00:17.670 align:start position:0%
detect a private virus in decision
making

00:00:17.670 --> 00:00:17.680 align:start position:0%
making
 

00:00:17.680 --> 00:00:22.150 align:start position:0%
making
loss<00:00:18.000><c> version</c><00:00:18.720><c> from</c><00:00:18.960><c> our</c><00:00:19.199><c> eye</c><00:00:19.520><c> activity</c>

00:00:22.150 --> 00:00:22.160 align:start position:0%
 
 

00:00:22.160 --> 00:00:23.910 align:start position:0%
 
loss<00:00:22.400><c> and</c><00:00:22.560><c> gain</c><00:00:22.800><c> have</c><00:00:22.960><c> different</c><00:00:23.359><c> impacts</c><00:00:23.760><c> on</c>

00:00:23.910 --> 00:00:23.920 align:start position:0%
loss and gain have different impacts on
 

00:00:23.920 --> 00:00:26.230 align:start position:0%
loss and gain have different impacts on
our<00:00:24.080><c> decisions</c><00:00:24.880><c> loss</c><00:00:25.199><c> version</c><00:00:25.599><c> describes</c><00:00:26.080><c> our</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
our decisions loss version describes our
 

00:00:26.240 --> 00:00:28.470 align:start position:0%
our decisions loss version describes our
tendency<00:00:26.800><c> to</c><00:00:26.960><c> prove</c><00:00:27.279><c> avoiding</c><00:00:27.760><c> losses</c>

00:00:28.470 --> 00:00:28.480 align:start position:0%
tendency to prove avoiding losses
 

00:00:28.480 --> 00:00:30.710 align:start position:0%
tendency to prove avoiding losses
acquiring<00:00:28.960><c> equivalents</c><00:00:29.599><c> or</c><00:00:29.840><c> larger</c><00:00:30.160><c> gains</c><00:00:30.640><c> in</c>

00:00:30.710 --> 00:00:30.720 align:start position:0%
acquiring equivalents or larger gains in
 

00:00:30.720 --> 00:00:32.150 align:start position:0%
acquiring equivalents or larger gains in
decision<00:00:31.119><c> making</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
decision making
 

00:00:32.160 --> 00:00:33.670 align:start position:0%
decision making
for<00:00:32.320><c> example</c><00:00:32.719><c> just</c><00:00:32.960><c> imagine</c><00:00:33.440><c> you</c><00:00:33.600><c> are</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
for example just imagine you are
 

00:00:33.680 --> 00:00:35.590 align:start position:0%
for example just imagine you are
presented<00:00:34.239><c> with</c><00:00:34.480><c> a</c><00:00:34.640><c> gamble</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
presented with a gamble
 

00:00:35.600 --> 00:00:37.190 align:start position:0%
presented with a gamble
in<00:00:35.760><c> which</c><00:00:36.160><c> winning</c><00:00:36.480><c> and</c><00:00:36.719><c> losing</c><00:00:37.040><c> is</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
in which winning and losing is
 

00:00:37.200 --> 00:00:39.350 align:start position:0%
in which winning and losing is
determined<00:00:37.680><c> by</c><00:00:37.840><c> flipping</c><00:00:38.239><c> a</c><00:00:38.320><c> call</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
determined by flipping a call
 

00:00:39.360 --> 00:00:43.190 align:start position:0%
determined by flipping a call
if<00:00:39.680><c> the</c><00:00:39.840><c> kong</c><00:00:40.160><c> lands</c><00:00:40.399><c> with</c><00:00:40.640><c> heads</c><00:00:41.200><c> you</c><00:00:41.360><c> win</c><00:00:41.520><c> 120</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
if the kong lands with heads you win 120
 

00:00:43.200 --> 00:00:47.190 align:start position:0%
if the kong lands with heads you win 120
and<00:00:43.440><c> if</c><00:00:43.680><c> it</c><00:00:43.920><c> lands</c><00:00:44.160><c> with</c><00:00:44.559><c> tails</c><00:00:45.120><c> you</c><00:00:45.280><c> lose</c><00:00:45.600><c> 100</c>

00:00:47.190 --> 00:00:47.200 align:start position:0%
and if it lands with tails you lose 100
 

00:00:47.200 --> 00:00:49.190 align:start position:0%
and if it lands with tails you lose 100
that<00:00:47.440><c> is</c><00:00:47.680><c> in</c><00:00:47.760><c> this</c><00:00:48.000><c> gamble</c><00:00:48.399><c> you</c><00:00:48.640><c> have</c><00:00:48.879><c> equal</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
that is in this gamble you have equal
 

00:00:49.200 --> 00:00:50.229 align:start position:0%
that is in this gamble you have equal
chance<00:00:49.680><c> to</c><00:00:49.760><c> win</c><00:00:50.000><c> and</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
chance to win and
 

00:00:50.239 --> 00:00:52.549 align:start position:0%
chance to win and
lose<00:00:50.480><c> money</c><00:00:51.039><c> but</c><00:00:51.280><c> potential</c><00:00:51.760><c> gain</c><00:00:52.160><c> is</c><00:00:52.320><c> not</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
lose money but potential gain is not
 

00:00:52.559 --> 00:00:54.470 align:start position:0%
lose money but potential gain is not
much<00:00:52.879><c> loss</c>

00:00:54.470 --> 00:00:54.480 align:start position:0%
much loss
 

00:00:54.480 --> 00:00:57.830 align:start position:0%
much loss
we<00:00:54.719><c> accept</c><00:00:55.120><c> gamble</c><00:00:55.600><c> for</c><00:00:55.760><c> rejection</c><00:00:57.440><c> according</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
we accept gamble for rejection according
 

00:00:57.840 --> 00:00:58.229 align:start position:0%
we accept gamble for rejection according
to

00:00:58.229 --> 00:00:58.239 align:start position:0%
to
 

00:00:58.239 --> 00:01:00.229 align:start position:0%
to
traditional<00:00:58.640><c> economic</c><00:00:59.120><c> theory</c><00:00:59.520><c> like</c><00:00:59.840><c> expect</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
traditional economic theory like expect
 

00:01:00.239 --> 00:01:02.869 align:start position:0%
traditional economic theory like expect
value<00:01:00.559><c> theory</c><00:01:01.120><c> you</c><00:01:01.359><c> should</c><00:01:01.680><c> accept</c><00:01:02.000><c> scandal</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
value theory you should accept scandal
 

00:01:02.879 --> 00:01:04.869 align:start position:0%
value theory you should accept scandal
because<00:01:03.280><c> expect</c><00:01:03.760><c> value</c><00:01:04.080><c> of</c><00:01:04.159><c> the</c><00:01:04.239><c> gamble</c><00:01:04.640><c> is</c>

00:01:04.869 --> 00:01:04.879 align:start position:0%
because expect value of the gamble is
 

00:01:04.879 --> 00:01:06.390 align:start position:0%
because expect value of the gamble is
larger<00:01:05.199><c> than</c><00:01:05.360><c> zero</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
larger than zero
 

00:01:06.400 --> 00:01:08.630 align:start position:0%
larger than zero
but<00:01:06.640><c> researchers</c><00:01:07.200><c> found</c><00:01:07.439><c> that</c><00:01:08.000><c> most</c><00:01:08.240><c> people</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
but researchers found that most people
 

00:01:08.640 --> 00:01:09.429 align:start position:0%
but researchers found that most people
actually

00:01:09.429 --> 00:01:09.439 align:start position:0%
actually
 

00:01:09.439 --> 00:01:12.550 align:start position:0%
actually
rejected<00:01:10.799><c> such</c><00:01:11.040><c> a</c><00:01:11.119><c> gamble</c><00:01:11.760><c> unless</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
rejected such a gamble unless
 

00:01:12.560 --> 00:01:14.550 align:start position:0%
rejected such a gamble unless
the<00:01:12.720><c> magnitude</c><00:01:13.280><c> or</c><00:01:13.439><c> gain</c><00:01:13.760><c> was</c><00:01:14.000><c> much</c><00:01:14.240><c> larger</c>

00:01:14.550 --> 00:01:14.560 align:start position:0%
the magnitude or gain was much larger
 

00:01:14.560 --> 00:01:17.749 align:start position:0%
the magnitude or gain was much larger
than<00:01:14.720><c> loss</c><00:01:15.119><c> like</c><00:01:15.360><c> twice</c><00:01:15.680><c> of</c><00:01:15.840><c> loss</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
than loss like twice of loss
 

00:01:17.759 --> 00:01:19.830 align:start position:0%
than loss like twice of loss
this<00:01:18.000><c> phenomenon</c><00:01:18.640><c> was</c><00:01:18.880><c> first</c><00:01:19.200><c> observed</c><00:01:19.680><c> by</c>

00:01:19.830 --> 00:01:19.840 align:start position:0%
this phenomenon was first observed by
 

00:01:19.840 --> 00:01:20.870 align:start position:0%
this phenomenon was first observed by
the<00:01:20.000><c> true</c><00:01:20.240><c> founders</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
the true founders
 

00:01:20.880 --> 00:01:24.390 align:start position:0%
the true founders
of<00:01:21.119><c> hebrew</c><00:01:21.439><c> economics</c><00:01:22.960><c> danman</c><00:01:23.520><c> and</c>

00:01:24.390 --> 00:01:24.400 align:start position:0%
of hebrew economics danman and
 

00:01:24.400 --> 00:01:27.830 align:start position:0%
of hebrew economics danman and
amherst<00:01:24.840><c> trevorski</c><00:01:26.159><c> uh</c><00:01:26.560><c> denman</c><00:01:27.280><c> later</c><00:01:27.680><c> won</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
amherst trevorski uh denman later won
 

00:01:27.840 --> 00:01:28.710 align:start position:0%
amherst trevorski uh denman later won
nobel<00:01:28.240><c> prize</c>

00:01:28.710 --> 00:01:28.720 align:start position:0%
nobel prize
 

00:01:28.720 --> 00:01:31.749 align:start position:0%
nobel prize
in<00:01:28.799><c> economics</c><00:01:30.079><c> and</c><00:01:30.320><c> he</c><00:01:30.479><c> considers</c><00:01:31.280><c> a</c>

00:01:31.749 --> 00:01:31.759 align:start position:0%
in economics and he considers a
 

00:01:31.759 --> 00:01:33.830 align:start position:0%
in economics and he considers a
concept<00:01:32.240><c> of</c><00:01:32.320><c> lost</c><00:01:32.640><c> version</c><00:01:33.119><c> as</c><00:01:33.280><c> their</c><00:01:33.520><c> most</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
concept of lost version as their most
 

00:01:33.840 --> 00:01:35.109 align:start position:0%
concept of lost version as their most
useful<00:01:34.159><c> contribution</c>

00:01:35.109 --> 00:01:35.119 align:start position:0%
useful contribution
 

00:01:35.119 --> 00:01:38.710 align:start position:0%
useful contribution
to<00:01:35.280><c> the</c><00:01:35.520><c> study</c><00:01:35.840><c> of</c><00:01:35.920><c> decision-making</c>

00:01:38.710 --> 00:01:38.720 align:start position:0%
 
 

00:01:38.720 --> 00:01:41.429 align:start position:0%
 
but<00:01:38.960><c> an</c><00:01:39.119><c> unresolved</c><00:01:39.759><c> question</c><00:01:40.240><c> is</c><00:01:40.880><c> why</c><00:01:41.119><c> people</c>

00:01:41.429 --> 00:01:41.439 align:start position:0%
but an unresolved question is why people
 

00:01:41.439 --> 00:01:44.789 align:start position:0%
but an unresolved question is why people
make<00:01:41.680><c> lost</c><00:01:41.840><c> adverse</c><00:01:42.240><c> decisions</c>

00:01:44.789 --> 00:01:44.799 align:start position:0%
make lost adverse decisions
 

00:01:44.799 --> 00:01:47.030 align:start position:0%
make lost adverse decisions
there<00:01:45.040><c> are</c><00:01:45.200><c> different</c><00:01:45.520><c> explanations</c><00:01:46.560><c> here</c><00:01:46.880><c> i</c>

00:01:47.030 --> 00:01:47.040 align:start position:0%
there are different explanations here i
 

00:01:47.040 --> 00:01:49.990 align:start position:0%
there are different explanations here i
focus<00:01:47.439><c> on</c><00:01:47.600><c> two</c><00:01:47.840><c> of</c><00:01:48.000><c> them</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
focus on two of them
 

00:01:50.000 --> 00:01:52.069 align:start position:0%
focus on two of them
first<00:01:50.399><c> kind</c><00:01:50.720><c> man</c><00:01:50.880><c> tversky</c><00:01:51.439><c> can</c><00:01:51.600><c> stabilize</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
first kind man tversky can stabilize
 

00:01:52.079 --> 00:01:54.149 align:start position:0%
first kind man tversky can stabilize
philosophy<00:01:52.720><c> as</c><00:01:52.880><c> a</c><00:01:52.960><c> valuation</c><00:01:53.520><c> bias</c>

00:01:54.149 --> 00:01:54.159 align:start position:0%
philosophy as a valuation bias
 

00:01:54.159 --> 00:01:56.310 align:start position:0%
philosophy as a valuation bias
they<00:01:54.320><c> assume</c><00:01:54.720><c> that</c><00:01:55.119><c> when</c><00:01:55.280><c> people</c><00:01:55.600><c> evaluate</c>

00:01:56.310 --> 00:01:56.320 align:start position:0%
they assume that when people evaluate
 

00:01:56.320 --> 00:01:57.429 align:start position:0%
they assume that when people evaluate
potential<00:01:56.799><c> gain</c><00:01:57.200><c> and</c>

00:01:57.429 --> 00:01:57.439 align:start position:0%
potential gain and
 

00:01:57.439 --> 00:02:00.630 align:start position:0%
potential gain and
loss<00:01:58.000><c> the</c><00:01:58.240><c> overweight</c><00:01:58.719><c> loss</c><00:01:59.119><c> relatively</c><00:01:59.680><c> gain</c>

00:02:00.630 --> 00:02:00.640 align:start position:0%
loss the overweight loss relatively gain
 

00:02:00.640 --> 00:02:03.030 align:start position:0%
loss the overweight loss relatively gain
consequently<00:02:01.360><c> even</c><00:02:01.600><c> when</c><00:02:01.840><c> amount</c><00:02:02.320><c> of</c><00:02:02.479><c> gain</c><00:02:02.799><c> is</c>

00:02:03.030 --> 00:02:03.040 align:start position:0%
consequently even when amount of gain is
 

00:02:03.040 --> 00:02:04.230 align:start position:0%
consequently even when amount of gain is
larger<00:02:03.439><c> than</c><00:02:03.680><c> loss</c>

00:02:04.230 --> 00:02:04.240 align:start position:0%
larger than loss
 

00:02:04.240 --> 00:02:06.389 align:start position:0%
larger than loss
after<00:02:04.479><c> waiting</c><00:02:05.119><c> people</c><00:02:05.360><c> may</c><00:02:05.520><c> still</c><00:02:05.840><c> feel</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
after waiting people may still feel
 

00:02:06.399 --> 00:02:07.510 align:start position:0%
after waiting people may still feel
potential<00:02:06.960><c> loss</c>

00:02:07.510 --> 00:02:07.520 align:start position:0%
potential loss
 

00:02:07.520 --> 00:02:10.790 align:start position:0%
potential loss
is<00:02:07.759><c> larger</c><00:02:08.160><c> than</c><00:02:08.319><c> potential</c><00:02:08.800><c> gain</c><00:02:10.399><c> but</c><00:02:10.560><c> can</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
is larger than potential gain but can
 

00:02:10.800 --> 00:02:11.350 align:start position:0%
is larger than potential gain but can
trust

00:02:11.350 --> 00:02:11.360 align:start position:0%
trust
 

00:02:11.360 --> 00:02:13.589 align:start position:0%
trust
an<00:02:11.599><c> alternative</c><00:02:12.160><c> will</c><00:02:12.319><c> argue</c><00:02:12.640><c> that</c><00:02:13.200><c> it's</c><00:02:13.440><c> not</c>

00:02:13.589 --> 00:02:13.599 align:start position:0%
an alternative will argue that it's not
 

00:02:13.599 --> 00:02:15.510 align:start position:0%
an alternative will argue that it's not
about<00:02:14.000><c> that</c><00:02:14.239><c> asian</c><00:02:14.560><c> virus</c><00:02:14.879><c> toward</c><00:02:15.200><c> the</c><00:02:15.280><c> loss</c>

00:02:15.510 --> 00:02:15.520 align:start position:0%
about that asian virus toward the loss
 

00:02:15.520 --> 00:02:16.150 align:start position:0%
about that asian virus toward the loss
of<00:02:15.680><c> gain</c>

00:02:16.150 --> 00:02:16.160 align:start position:0%
of gain
 

00:02:16.160 --> 00:02:18.070 align:start position:0%
of gain
but<00:02:16.400><c> about</c><00:02:16.720><c> response</c><00:02:17.360><c> fires</c><00:02:17.680><c> towards</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
but about response fires towards
 

00:02:18.080 --> 00:02:20.470 align:start position:0%
but about response fires towards
accepting<00:02:18.640><c> or</c><00:02:18.800><c> rejecting</c><00:02:19.200><c> gambles</c>

00:02:20.470 --> 00:02:20.480 align:start position:0%
accepting or rejecting gambles
 

00:02:20.480 --> 00:02:22.550 align:start position:0%
accepting or rejecting gambles
according<00:02:20.959><c> to</c><00:02:21.120><c> this</c><00:02:21.360><c> view</c><00:02:21.920><c> people</c><00:02:22.239><c> have</c><00:02:22.400><c> an</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
according to this view people have an
 

00:02:22.560 --> 00:02:24.150 align:start position:0%
according to this view people have an
initial<00:02:22.959><c> response</c><00:02:23.360><c> tendency</c>

00:02:24.150 --> 00:02:24.160 align:start position:0%
initial response tendency
 

00:02:24.160 --> 00:02:26.949 align:start position:0%
initial response tendency
to<00:02:24.319><c> reach</c><00:02:24.560><c> a</c><00:02:24.720><c> gamble</c><00:02:25.760><c> which</c><00:02:26.080><c> is</c><00:02:26.239><c> irrelevant</c><00:02:26.800><c> to</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
to reach a gamble which is irrelevant to
 

00:02:26.959 --> 00:02:28.949 align:start position:0%
to reach a gamble which is irrelevant to
the<00:02:27.120><c> amount</c><00:02:27.360><c> of</c><00:02:27.520><c> gain</c><00:02:27.760><c> and</c><00:02:27.920><c> loss</c>

00:02:28.949 --> 00:02:28.959 align:start position:0%
the amount of gain and loss
 

00:02:28.959 --> 00:02:30.869 align:start position:0%
the amount of gain and loss
because<00:02:29.360><c> once</c><00:02:29.520><c> you</c><00:02:29.760><c> accept</c><00:02:30.080><c> a</c><00:02:30.239><c> gamble</c><00:02:30.720><c> you</c>

00:02:30.869 --> 00:02:30.879 align:start position:0%
because once you accept a gamble you
 

00:02:30.879 --> 00:02:32.470 align:start position:0%
because once you accept a gamble you
will<00:02:31.120><c> have</c><00:02:31.360><c> the</c><00:02:31.440><c> possibility</c><00:02:32.080><c> of</c><00:02:32.160><c> losing</c>

00:02:32.470 --> 00:02:32.480 align:start position:0%
will have the possibility of losing
 

00:02:32.480 --> 00:02:33.190 align:start position:0%
will have the possibility of losing
money

00:02:33.190 --> 00:02:33.200 align:start position:0%
money
 

00:02:33.200 --> 00:02:35.830 align:start position:0%
money
but<00:02:33.440><c> if</c><00:02:33.519><c> you</c><00:02:33.680><c> reject</c><00:02:34.160><c> it</c><00:02:34.800><c> you</c><00:02:35.040><c> can</c><00:02:35.200><c> totally</c>

00:02:35.830 --> 00:02:35.840 align:start position:0%
but if you reject it you can totally
 

00:02:35.840 --> 00:02:36.470 align:start position:0%
but if you reject it you can totally
avoid

00:02:36.470 --> 00:02:36.480 align:start position:0%
avoid
 

00:02:36.480 --> 00:02:39.910 align:start position:0%
avoid
the<00:02:36.640><c> possibility</c><00:02:37.280><c> of</c><00:02:37.440><c> losing</c><00:02:37.760><c> money</c>

00:02:39.910 --> 00:02:39.920 align:start position:0%
the possibility of losing money
 

00:02:39.920 --> 00:02:41.990 align:start position:0%
the possibility of losing money
both<00:02:40.160><c> of</c><00:02:40.319><c> the</c><00:02:40.480><c> true</c><00:02:40.640><c> accounts</c><00:02:41.120><c> can</c><00:02:41.360><c> explain</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
both of the true accounts can explain
 

00:02:42.000 --> 00:02:44.070 align:start position:0%
both of the true accounts can explain
why<00:02:42.239><c> people</c><00:02:42.560><c> reject</c><00:02:42.959><c> gambles</c><00:02:43.440><c> or</c><00:02:43.680><c> positive</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
why people reject gambles or positive
 

00:02:44.080 --> 00:02:45.670 align:start position:0%
why people reject gambles or positive
expected<00:02:44.480><c> value</c>

00:02:45.670 --> 00:02:45.680 align:start position:0%
expected value
 

00:02:45.680 --> 00:02:48.309 align:start position:0%
expected value
and<00:02:46.080><c> only</c><00:02:46.319><c> based</c><00:02:46.640><c> on</c><00:02:46.879><c> their</c><00:02:47.280><c> choices</c><00:02:48.080><c> it's</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
and only based on their choices it's
 

00:02:48.319 --> 00:02:51.509 align:start position:0%
and only based on their choices it's
hard<00:02:48.640><c> to</c><00:02:48.879><c> distinguish</c><00:02:49.519><c> the</c><00:02:49.680><c> two</c><00:02:49.920><c> accounts</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
hard to distinguish the two accounts
 

00:02:51.519 --> 00:02:53.350 align:start position:0%
hard to distinguish the two accounts
so<00:02:51.840><c> is</c><00:02:52.000><c> there</c><00:02:52.239><c> any</c><00:02:52.480><c> way</c><00:02:52.720><c> that</c><00:02:52.879><c> we</c><00:02:53.120><c> can</c>

00:02:53.350 --> 00:02:53.360 align:start position:0%
so is there any way that we can
 

00:02:53.360 --> 00:02:56.309 align:start position:0%
so is there any way that we can
reconcile<00:02:54.080><c> the</c><00:02:54.239><c> true</c><00:02:54.400><c> accounts</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
reconcile the true accounts
 

00:02:56.319 --> 00:02:57.910 align:start position:0%
reconcile the true accounts
my<00:02:56.560><c> cardigans</c><00:02:57.120><c> and</c><00:02:57.280><c> i</c><00:02:57.440><c> have</c><00:02:57.519><c> worked</c><00:02:57.840><c> and</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
my cardigans and i have worked and
 

00:02:57.920 --> 00:03:01.509 align:start position:0%
my cardigans and i have worked and
developed<00:02:58.319><c> a</c><00:02:58.480><c> computation</c><00:02:59.120><c> model</c>

00:03:01.509 --> 00:03:01.519 align:start position:0%
 
 

00:03:01.519 --> 00:03:04.229 align:start position:0%
 
for<00:03:01.760><c> gambling</c><00:03:02.239><c> tasks</c><00:03:02.800><c> to</c><00:03:03.040><c> incorporate</c><00:03:03.920><c> both</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
for gambling tasks to incorporate both
 

00:03:04.239 --> 00:03:05.270 align:start position:0%
for gambling tasks to incorporate both
of<00:03:04.319><c> the</c><00:03:04.400><c> two</c><00:03:04.560><c> accounts</c>

00:03:05.270 --> 00:03:05.280 align:start position:0%
of the two accounts
 

00:03:05.280 --> 00:03:08.229 align:start position:0%
of the two accounts
we<00:03:05.440><c> assume</c><00:03:05.840><c> that</c><00:03:06.800><c> the</c><00:03:06.879><c> decision</c><00:03:07.440><c> accepting</c><00:03:08.080><c> or</c>

00:03:08.229 --> 00:03:08.239 align:start position:0%
we assume that the decision accepting or
 

00:03:08.239 --> 00:03:09.910 align:start position:0%
we assume that the decision accepting or
reject<00:03:08.720><c> and</c><00:03:08.800><c> mixed</c><00:03:09.040><c> gamble</c>

00:03:09.910 --> 00:03:09.920 align:start position:0%
reject and mixed gamble
 

00:03:09.920 --> 00:03:12.149 align:start position:0%
reject and mixed gamble
is<00:03:10.080><c> annoying</c><00:03:10.480><c> the</c><00:03:10.640><c> evidence</c><00:03:11.599><c> accumulation</c>

00:03:12.149 --> 00:03:12.159 align:start position:0%
is annoying the evidence accumulation
 

00:03:12.159 --> 00:03:13.430 align:start position:0%
is annoying the evidence accumulation
process

00:03:13.430 --> 00:03:13.440 align:start position:0%
process
 

00:03:13.440 --> 00:03:16.550 align:start position:0%
process
here<00:03:13.680><c> the</c><00:03:13.840><c> x-axis</c><00:03:14.800><c> is</c><00:03:15.200><c> time</c><00:03:15.519><c> and</c><00:03:15.840><c> y-axis</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
here the x-axis is time and y-axis
 

00:03:16.560 --> 00:03:18.790 align:start position:0%
here the x-axis is time and y-axis
is<00:03:16.800><c> relatively</c><00:03:17.360><c> stream</c><00:03:17.519><c> value</c><00:03:18.239><c> in</c><00:03:18.400><c> the</c><00:03:18.560><c> same</c>

00:03:18.790 --> 00:03:18.800 align:start position:0%
is relatively stream value in the same
 

00:03:18.800 --> 00:03:20.470 align:start position:0%
is relatively stream value in the same
process<00:03:19.280><c> the</c><00:03:19.360><c> relatively</c><00:03:19.840><c> simple</c><00:03:20.080><c> value</c>

00:03:20.470 --> 00:03:20.480 align:start position:0%
process the relatively simple value
 

00:03:20.480 --> 00:03:22.070 align:start position:0%
process the relatively simple value
moves<00:03:20.720><c> towards</c><00:03:21.120><c> at</c><00:03:21.360><c> the</c><00:03:21.440><c> bottom</c>

00:03:22.070 --> 00:03:22.080 align:start position:0%
moves towards at the bottom
 

00:03:22.080 --> 00:03:24.550 align:start position:0%
moves towards at the bottom
but<00:03:22.319><c> up</c><00:03:22.480><c> boundaries</c><00:03:23.440><c> when</c><00:03:23.760><c> reaches</c><00:03:24.239><c> bottom</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
but up boundaries when reaches bottom
 

00:03:24.560 --> 00:03:26.869 align:start position:0%
but up boundaries when reaches bottom
boundary<00:03:25.120><c> the</c><00:03:25.200><c> gamble</c><00:03:25.599><c> is</c><00:03:25.680><c> to</c><00:03:25.840><c> be</c><00:03:26.000><c> rejected</c>

00:03:26.869 --> 00:03:26.879 align:start position:0%
boundary the gamble is to be rejected
 

00:03:26.879 --> 00:03:29.110 align:start position:0%
boundary the gamble is to be rejected
and<00:03:27.040><c> when</c><00:03:27.280><c> it</c><00:03:27.440><c> reaches</c><00:03:28.000><c> the</c><00:03:28.080><c> up</c><00:03:28.319><c> boundary</c><00:03:28.959><c> the</c>

00:03:29.110 --> 00:03:29.120 align:start position:0%
and when it reaches the up boundary the
 

00:03:29.120 --> 00:03:32.229 align:start position:0%
and when it reaches the up boundary the
gamble<00:03:29.440><c> is</c><00:03:29.519><c> to</c><00:03:29.680><c> be</c><00:03:30.840><c> accepted</c>

00:03:32.229 --> 00:03:32.239 align:start position:0%
gamble is to be accepted
 

00:03:32.239 --> 00:03:34.470 align:start position:0%
gamble is to be accepted
then<00:03:32.480><c> what</c><00:03:32.640><c> determines</c><00:03:33.280><c> the</c><00:03:33.440><c> velocity</c><00:03:34.080><c> of</c><00:03:34.319><c> the</c>

00:03:34.470 --> 00:03:34.480 align:start position:0%
then what determines the velocity of the
 

00:03:34.480 --> 00:03:35.589 align:start position:0%
then what determines the velocity of the
process

00:03:35.589 --> 00:03:35.599 align:start position:0%
process
 

00:03:35.599 --> 00:03:39.110 align:start position:0%
process
that's<00:03:35.920><c> the</c><00:03:36.400><c> random</c><00:03:36.879><c> size</c><00:03:37.519><c> of</c><00:03:37.680><c> gain</c><00:03:37.920><c> and</c><00:03:38.159><c> loss</c>

00:03:39.110 --> 00:03:39.120 align:start position:0%
that's the random size of gain and loss
 

00:03:39.120 --> 00:03:41.830 align:start position:0%
that's the random size of gain and loss
when<00:03:39.519><c> gain</c><00:03:39.840><c> is</c><00:03:39.920><c> much</c><00:03:40.239><c> larger</c><00:03:40.560><c> than</c><00:03:40.799><c> loss</c><00:03:41.599><c> it</c>

00:03:41.830 --> 00:03:41.840 align:start position:0%
when gain is much larger than loss it
 

00:03:41.840 --> 00:03:43.190 align:start position:0%
when gain is much larger than loss it
tends<00:03:42.159><c> to</c><00:03:42.319><c> move</c><00:03:42.560><c> up</c>

00:03:43.190 --> 00:03:43.200 align:start position:0%
tends to move up
 

00:03:43.200 --> 00:03:45.110 align:start position:0%
tends to move up
and<00:03:43.360><c> when</c><00:03:43.599><c> loss</c><00:03:43.920><c> is</c><00:03:44.000><c> much</c><00:03:44.239><c> larger</c><00:03:44.560><c> than</c><00:03:44.720><c> gain</c>

00:03:45.110 --> 00:03:45.120 align:start position:0%
and when loss is much larger than gain
 

00:03:45.120 --> 00:03:46.949 align:start position:0%
and when loss is much larger than gain
it<00:03:45.280><c> tends</c><00:03:45.519><c> to</c><00:03:45.599><c> move</c><00:03:45.840><c> down</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
it tends to move down
 

00:03:46.959 --> 00:03:49.190 align:start position:0%
it tends to move down
and<00:03:47.120><c> the</c><00:03:47.200><c> coefficients</c><00:03:48.000><c> for</c><00:03:48.159><c> gain</c><00:03:48.400><c> and</c><00:03:48.640><c> loss</c>

00:03:49.190 --> 00:03:49.200 align:start position:0%
and the coefficients for gain and loss
 

00:03:49.200 --> 00:03:50.390 align:start position:0%
and the coefficients for gain and loss
represent<00:03:49.760><c> their</c>

00:03:50.390 --> 00:03:50.400 align:start position:0%
represent their
 

00:03:50.400 --> 00:03:53.270 align:start position:0%
represent their
weights<00:03:51.120><c> in</c><00:03:51.280><c> the</c><00:03:51.360><c> velocity</c><00:03:52.319><c> accordingly</c><00:03:53.120><c> the</c>

00:03:53.270 --> 00:03:53.280 align:start position:0%
weights in the velocity accordingly the
 

00:03:53.280 --> 00:03:55.110 align:start position:0%
weights in the velocity accordingly the
ratio<00:03:53.680><c> of</c><00:03:53.760><c> the</c><00:03:53.840><c> two</c><00:03:54.080><c> coefficients</c>

00:03:55.110 --> 00:03:55.120 align:start position:0%
ratio of the two coefficients
 

00:03:55.120 --> 00:03:59.270 align:start position:0%
ratio of the two coefficients
indicates<00:03:55.760><c> the</c><00:03:56.080><c> size</c><00:03:56.720><c> of</c><00:03:56.959><c> valuation</c><00:03:57.519><c> bars</c>

00:03:59.270 --> 00:03:59.280 align:start position:0%
indicates the size of valuation bars
 

00:03:59.280 --> 00:04:01.750 align:start position:0%
indicates the size of valuation bars
then<00:03:59.519><c> there</c><00:03:59.760><c> is</c><00:03:59.920><c> a</c><00:04:00.000><c> response</c><00:04:00.400><c> balance</c><00:04:01.439><c> it</c><00:04:01.599><c> is</c>

00:04:01.750 --> 00:04:01.760 align:start position:0%
then there is a response balance it is
 

00:04:01.760 --> 00:04:03.670 align:start position:0%
then there is a response balance it is
indicated<00:04:02.319><c> by</c><00:04:02.480><c> the</c><00:04:02.640><c> starting</c><00:04:03.040><c> points</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
indicated by the starting points
 

00:04:03.680 --> 00:04:06.149 align:start position:0%
indicated by the starting points
of<00:04:03.840><c> the</c><00:04:04.000><c> process</c><00:04:05.200><c> when</c><00:04:05.439><c> starting</c><00:04:05.760><c> point</c><00:04:06.000><c> is</c>

00:04:06.149 --> 00:04:06.159 align:start position:0%
of the process when starting point is
 

00:04:06.159 --> 00:04:07.190 align:start position:0%
of the process when starting point is
right<00:04:06.400><c> in</c><00:04:06.480><c> the</c><00:04:06.560><c> middle</c><00:04:06.799><c> of</c><00:04:06.879><c> the</c><00:04:07.040><c> two</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
right in the middle of the two
 

00:04:07.200 --> 00:04:07.990 align:start position:0%
right in the middle of the two
boundaries

00:04:07.990 --> 00:04:08.000 align:start position:0%
boundaries
 

00:04:08.000 --> 00:04:09.750 align:start position:0%
boundaries
it<00:04:08.159><c> means</c><00:04:08.480><c> no</c><00:04:08.640><c> initial</c><00:04:09.040><c> bias</c><00:04:09.360><c> for</c><00:04:09.519><c> the</c><00:04:09.599><c> two</c>

00:04:09.750 --> 00:04:09.760 align:start position:0%
it means no initial bias for the two
 

00:04:09.760 --> 00:04:12.630 align:start position:0%
it means no initial bias for the two
response<00:04:10.799><c> but</c><00:04:11.040><c> when</c><00:04:11.360><c> the</c><00:04:11.519><c> starting</c><00:04:11.840><c> point</c><00:04:12.239><c> is</c>

00:04:12.630 --> 00:04:12.640 align:start position:0%
response but when the starting point is
 

00:04:12.640 --> 00:04:15.350 align:start position:0%
response but when the starting point is
closer<00:04:13.280><c> to</c><00:04:13.439><c> the</c><00:04:13.599><c> bottom</c><00:04:14.000><c> boundary</c><00:04:14.480><c> it</c><00:04:14.640><c> means</c>

00:04:15.350 --> 00:04:15.360 align:start position:0%
closer to the bottom boundary it means
 

00:04:15.360 --> 00:04:17.270 align:start position:0%
closer to the bottom boundary it means
there<00:04:15.599><c> is</c><00:04:15.760><c> an</c><00:04:15.920><c> initial</c><00:04:16.320><c> virus</c><00:04:16.720><c> to</c><00:04:16.880><c> reject</c>

00:04:17.270 --> 00:04:17.280 align:start position:0%
there is an initial virus to reject
 

00:04:17.280 --> 00:04:18.550 align:start position:0%
there is an initial virus to reject
gambles

00:04:18.550 --> 00:04:18.560 align:start position:0%
gambles
 

00:04:18.560 --> 00:04:21.990 align:start position:0%
gambles
we<00:04:18.720><c> can</c><00:04:19.040><c> formalize</c><00:04:19.919><c> formulates</c><00:04:20.639><c> the</c><00:04:21.440><c> size</c><00:04:21.840><c> of</c>

00:04:21.990 --> 00:04:22.000 align:start position:0%
we can formalize formulates the size of
 

00:04:22.000 --> 00:04:24.390 align:start position:0%
we can formalize formulates the size of
response<00:04:22.479><c> bias</c><00:04:22.880><c> at</c><00:04:23.120><c> this</c><00:04:23.360><c> distance</c>

00:04:24.390 --> 00:04:24.400 align:start position:0%
response bias at this distance
 

00:04:24.400 --> 00:04:28.950 align:start position:0%
response bias at this distance
from<00:04:24.639><c> the</c><00:04:24.880><c> middle</c><00:04:25.199><c> point</c><00:04:27.199><c> so</c><00:04:27.360><c> by</c><00:04:27.520><c> doing</c><00:04:27.840><c> so</c><00:04:28.160><c> the</c>

00:04:28.950 --> 00:04:28.960 align:start position:0%
from the middle point so by doing so the
 

00:04:28.960 --> 00:04:31.270 align:start position:0%
from the middle point so by doing so the
uh<00:04:29.199><c> doing</c><00:04:29.520><c> this</c><00:04:30.000><c> the</c><00:04:30.240><c> model</c><00:04:30.639><c> accommodates</c>

00:04:31.270 --> 00:04:31.280 align:start position:0%
uh doing this the model accommodates
 

00:04:31.280 --> 00:04:33.350 align:start position:0%
uh doing this the model accommodates
both<00:04:31.600><c> valuation</c><00:04:32.080><c> virus</c><00:04:32.400><c> and</c><00:04:32.560><c> response</c><00:04:32.960><c> virus</c>

00:04:33.350 --> 00:04:33.360 align:start position:0%
both valuation virus and response virus
 

00:04:33.360 --> 00:04:36.710 align:start position:0%
both valuation virus and response virus
in<00:04:33.440><c> the</c><00:04:33.520><c> computational</c><00:04:34.000><c> process</c><00:04:34.479><c> model</c>

00:04:36.710 --> 00:04:36.720 align:start position:0%
in the computational process model
 

00:04:36.720 --> 00:04:39.909 align:start position:0%
in the computational process model
moreover<00:04:37.680><c> based</c><00:04:37.919><c> on</c><00:04:38.160><c> this</c><00:04:38.400><c> model</c><00:04:38.880><c> we</c><00:04:39.280><c> further</c>

00:04:39.909 --> 00:04:39.919 align:start position:0%
moreover based on this model we further
 

00:04:39.919 --> 00:04:42.390 align:start position:0%
moreover based on this model we further
predict<00:04:40.400><c> that</c><00:04:40.960><c> the</c><00:04:41.120><c> two</c><00:04:41.280><c> viruses</c><00:04:42.000><c> can</c><00:04:42.160><c> be</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
predict that the two viruses can be
 

00:04:42.400 --> 00:04:43.189 align:start position:0%
predict that the two viruses can be
observed

00:04:43.189 --> 00:04:43.199 align:start position:0%
observed
 

00:04:43.199 --> 00:04:45.990 align:start position:0%
observed
in<00:04:43.360><c> gate</c><00:04:43.680><c> allocation</c><00:04:44.479><c> and</c><00:04:44.639><c> pupil</c><00:04:44.960><c> dilation</c><00:04:45.840><c> in</c>

00:04:45.990 --> 00:04:46.000 align:start position:0%
in gate allocation and pupil dilation in
 

00:04:46.000 --> 00:04:48.870 align:start position:0%
in gate allocation and pupil dilation in
the<00:04:46.080><c> design</c><00:04:46.479><c> process</c>

00:04:48.870 --> 00:04:48.880 align:start position:0%
the design process
 

00:04:48.880 --> 00:04:50.870 align:start position:0%
the design process
this<00:04:49.120><c> allocation</c><00:04:49.759><c> often</c><00:04:50.080><c> reflects</c><00:04:50.720><c> our</c>

00:04:50.870 --> 00:04:50.880 align:start position:0%
this allocation often reflects our
 

00:04:50.880 --> 00:04:52.550 align:start position:0%
this allocation often reflects our
validation<00:04:51.440><c> virus</c>

00:04:52.550 --> 00:04:52.560 align:start position:0%
validation virus
 

00:04:52.560 --> 00:04:55.189 align:start position:0%
validation virus
for<00:04:52.800><c> example</c><00:04:53.680><c> research</c><00:04:54.160><c> found</c><00:04:54.400><c> that</c><00:04:54.960><c> when</c>

00:04:55.189 --> 00:04:55.199 align:start position:0%
for example research found that when
 

00:04:55.199 --> 00:04:56.070 align:start position:0%
for example research found that when
males<00:04:55.680><c> judge</c>

00:04:56.070 --> 00:04:56.080 align:start position:0%
males judge
 

00:04:56.080 --> 00:04:58.710 align:start position:0%
males judge
attractiveness<00:04:56.880><c> or</c><00:04:57.040><c> two</c><00:04:57.280><c> female</c><00:04:57.680><c> faces</c><00:04:58.560><c> they</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
attractiveness or two female faces they
 

00:04:58.720 --> 00:05:00.710 align:start position:0%
attractiveness or two female faces they
tended<00:04:59.040><c> to</c><00:04:59.199><c> look</c><00:04:59.440><c> more</c><00:04:59.759><c> at</c><00:04:59.919><c> the</c><00:05:00.080><c> face</c>

00:05:00.710 --> 00:05:00.720 align:start position:0%
tended to look more at the face
 

00:05:00.720 --> 00:05:03.749 align:start position:0%
tended to look more at the face
they<00:05:00.880><c> finally</c><00:05:01.280><c> chosen</c><00:05:02.880><c> similarly</c>

00:05:03.749 --> 00:05:03.759 align:start position:0%
they finally chosen similarly
 

00:05:03.759 --> 00:05:06.150 align:start position:0%
they finally chosen similarly
research<00:05:04.160><c> found</c><00:05:04.400><c> that</c><00:05:04.960><c> when</c><00:05:05.280><c> consumers</c><00:05:05.919><c> make</c>

00:05:06.150 --> 00:05:06.160 align:start position:0%
research found that when consumers make
 

00:05:06.160 --> 00:05:07.510 align:start position:0%
research found that when consumers make
purchase<00:05:06.639><c> decisions</c>

00:05:07.510 --> 00:05:07.520 align:start position:0%
purchase decisions
 

00:05:07.520 --> 00:05:09.350 align:start position:0%
purchase decisions
if<00:05:07.680><c> they</c><00:05:07.919><c> look</c><00:05:08.160><c> more</c><00:05:08.479><c> on</c><00:05:08.720><c> the</c><00:05:08.880><c> product</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
if they look more on the product
 

00:05:09.360 --> 00:05:11.909 align:start position:0%
if they look more on the product
products<00:05:10.080><c> they</c><00:05:10.320><c> probably</c><00:05:10.720><c> will</c><00:05:10.960><c> buy</c>

00:05:11.909 --> 00:05:11.919 align:start position:0%
products they probably will buy
 

00:05:11.919 --> 00:05:14.790 align:start position:0%
products they probably will buy
and<00:05:12.960><c> but</c><00:05:13.120><c> if</c><00:05:13.759><c> if</c><00:05:13.919><c> they</c><00:05:14.080><c> look</c><00:05:14.320><c> more</c><00:05:14.479><c> at</c><00:05:14.639><c> the</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
and but if if they look more at the
 

00:05:14.800 --> 00:05:15.430 align:start position:0%
and but if if they look more at the
price

00:05:15.430 --> 00:05:15.440 align:start position:0%
price
 

00:05:15.440 --> 00:05:18.390 align:start position:0%
price
they<00:05:15.680><c> probably</c><00:05:16.000><c> won't</c><00:05:16.240><c> buy</c><00:05:17.680><c> so</c><00:05:17.840><c> go</c><00:05:18.080><c> back</c><00:05:18.240><c> to</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
they probably won't buy so go back to
 

00:05:18.400 --> 00:05:21.430 align:start position:0%
they probably won't buy so go back to
our<00:05:18.560><c> gambling</c><00:05:18.960><c> tasks</c><00:05:19.360><c> we</c><00:05:19.600><c> have</c><00:05:19.840><c> success</c><00:05:20.320><c> that</c>

00:05:21.430 --> 00:05:21.440 align:start position:0%
our gambling tasks we have success that
 

00:05:21.440 --> 00:05:24.950 align:start position:0%
our gambling tasks we have success that
people<00:05:21.840><c> always</c><00:05:22.400><c> lost</c><00:05:23.360><c> probably</c><00:05:23.840><c> because</c><00:05:24.240><c> they</c>

00:05:24.950 --> 00:05:24.960 align:start position:0%
people always lost probably because they
 

00:05:24.960 --> 00:05:27.110 align:start position:0%
people always lost probably because they
look<00:05:25.199><c> more</c><00:05:25.360><c> at</c><00:05:25.600><c> loss</c><00:05:26.000><c> than</c><00:05:26.240><c> gain</c><00:05:26.560><c> in</c><00:05:26.720><c> the</c><00:05:26.880><c> same</c>

00:05:27.110 --> 00:05:27.120 align:start position:0%
look more at loss than gain in the same
 

00:05:27.120 --> 00:05:28.310 align:start position:0%
look more at loss than gain in the same
process

00:05:28.310 --> 00:05:28.320 align:start position:0%
process
 

00:05:28.320 --> 00:05:30.790 align:start position:0%
process
in<00:05:28.560><c> short</c><00:05:28.880><c> we</c><00:05:29.120><c> predict</c><00:05:29.600><c> that</c><00:05:30.240><c> case</c><00:05:30.560><c> bias</c>

00:05:30.790 --> 00:05:30.800 align:start position:0%
in short we predict that case bias
 

00:05:30.800 --> 00:05:31.909 align:start position:0%
in short we predict that case bias
toward<00:05:31.120><c> the</c><00:05:31.199><c> loss</c>

00:05:31.909 --> 00:05:31.919 align:start position:0%
toward the loss
 

00:05:31.919 --> 00:05:34.390 align:start position:0%
toward the loss
is<00:05:32.320><c> related</c><00:05:32.880><c> to</c><00:05:33.039><c> valuation</c><00:05:33.600><c> virus</c><00:05:34.000><c> toward</c><00:05:34.320><c> the</c>

00:05:34.390 --> 00:05:34.400 align:start position:0%
is related to valuation virus toward the
 

00:05:34.400 --> 00:05:37.029 align:start position:0%
is related to valuation virus toward the
loss

00:05:37.029 --> 00:05:37.039 align:start position:0%
 
 

00:05:37.039 --> 00:05:40.150 align:start position:0%
 
next<00:05:37.759><c> pupil</c><00:05:39.039><c> pupil</c><00:05:39.360><c> dilation</c><00:05:39.840><c> has</c><00:05:40.000><c> been</c>

00:05:40.150 --> 00:05:40.160 align:start position:0%
next pupil pupil dilation has been
 

00:05:40.160 --> 00:05:41.830 align:start position:0%
next pupil pupil dilation has been
related<00:05:40.560><c> to</c><00:05:40.639><c> many</c><00:05:40.960><c> different</c><00:05:41.360><c> psychological</c>

00:05:41.830 --> 00:05:41.840 align:start position:0%
related to many different psychological
 

00:05:41.840 --> 00:05:42.790 align:start position:0%
related to many different psychological
processes

00:05:42.790 --> 00:05:42.800 align:start position:0%
processes
 

00:05:42.800 --> 00:05:45.670 align:start position:0%
processes
like<00:05:43.120><c> surprise</c><00:05:43.919><c> fear</c><00:05:44.720><c> leaf</c><00:05:45.039><c> updating</c><00:05:45.600><c> and</c>

00:05:45.670 --> 00:05:45.680 align:start position:0%
like surprise fear leaf updating and
 

00:05:45.680 --> 00:05:47.350 align:start position:0%
like surprise fear leaf updating and
working<00:05:46.080><c> memory</c>

00:05:47.350 --> 00:05:47.360 align:start position:0%
working memory
 

00:05:47.360 --> 00:05:49.510 align:start position:0%
working memory
a<00:05:47.520><c> recent</c><00:05:47.919><c> convergent</c><00:05:48.400><c> view</c><00:05:48.720><c> is</c><00:05:48.800><c> that</c><00:05:49.199><c> pupil</c>

00:05:49.510 --> 00:05:49.520 align:start position:0%
a recent convergent view is that pupil
 

00:05:49.520 --> 00:05:52.629 align:start position:0%
a recent convergent view is that pupil
dilation<00:05:50.000><c> reflects</c><00:05:50.560><c> mental</c><00:05:50.880><c> efforts</c><00:05:51.840><c> that</c><00:05:52.080><c> is</c>

00:05:52.629 --> 00:05:52.639 align:start position:0%
dilation reflects mental efforts that is
 

00:05:52.639 --> 00:05:54.629 align:start position:0%
dilation reflects mental efforts that is
when<00:05:52.800><c> your</c><00:05:53.039><c> mind</c><00:05:53.440><c> is</c><00:05:53.600><c> working</c><00:05:54.000><c> hard</c><00:05:54.479><c> your</c>

00:05:54.629 --> 00:05:54.639 align:start position:0%
when your mind is working hard your
 

00:05:54.639 --> 00:05:56.629 align:start position:0%
when your mind is working hard your
purpose<00:05:54.960><c> is</c><00:05:55.120><c> dilated</c>

00:05:56.629 --> 00:05:56.639 align:start position:0%
purpose is dilated
 

00:05:56.639 --> 00:05:59.189 align:start position:0%
purpose is dilated
surprise<00:05:57.759><c> fear</c><00:05:58.240><c> belief</c><00:05:58.560><c> updating</c><00:05:59.120><c> and</c>

00:05:59.189 --> 00:05:59.199 align:start position:0%
surprise fear belief updating and
 

00:05:59.199 --> 00:06:00.150 align:start position:0%
surprise fear belief updating and
working<00:05:59.520><c> memory</c>

00:06:00.150 --> 00:06:00.160 align:start position:0%
working memory
 

00:06:00.160 --> 00:06:03.990 align:start position:0%
working memory
all<00:06:00.400><c> imply</c><00:06:00.800><c> much</c><00:06:01.600><c> mental</c><00:06:02.000><c> effort</c>

00:06:03.990 --> 00:06:04.000 align:start position:0%
all imply much mental effort
 

00:06:04.000 --> 00:06:06.070 align:start position:0%
all imply much mental effort
so<00:06:04.240><c> in</c><00:06:04.479><c> our</c><00:06:04.639><c> framework</c><00:06:05.440><c> if</c><00:06:05.600><c> there</c><00:06:05.840><c> is</c><00:06:06.000><c> a</c>

00:06:06.070 --> 00:06:06.080 align:start position:0%
so in our framework if there is a
 

00:06:06.080 --> 00:06:07.189 align:start position:0%
so in our framework if there is a
response<00:06:06.479><c> virus</c>

00:06:07.189 --> 00:06:07.199 align:start position:0%
response virus
 

00:06:07.199 --> 00:06:10.550 align:start position:0%
response virus
to<00:06:07.360><c> reject</c><00:06:07.759><c> gambles</c><00:06:09.039><c> we</c><00:06:09.280><c> would</c><00:06:09.680><c> predict</c>

00:06:10.550 --> 00:06:10.560 align:start position:0%
to reject gambles we would predict
 

00:06:10.560 --> 00:06:13.189 align:start position:0%
to reject gambles we would predict
that<00:06:11.199><c> accepting</c><00:06:11.759><c> a</c><00:06:11.840><c> gamble</c><00:06:12.560><c> would</c><00:06:12.720><c> be</c><00:06:12.960><c> more</c>

00:06:13.189 --> 00:06:13.199 align:start position:0%
that accepting a gamble would be more
 

00:06:13.199 --> 00:06:15.029 align:start position:0%
that accepting a gamble would be more
effort<00:06:13.520><c> of</c><00:06:13.680><c> the</c><00:06:13.759><c> same</c><00:06:14.080><c> process</c>

00:06:15.029 --> 00:06:15.039 align:start position:0%
effort of the same process
 

00:06:15.039 --> 00:06:18.150 align:start position:0%
effort of the same process
than<00:06:15.440><c> rejecting</c><00:06:16.000><c> gamble</c><00:06:17.280><c> thus</c><00:06:17.840><c> we</c><00:06:18.000><c> would</c>

00:06:18.150 --> 00:06:18.160 align:start position:0%
than rejecting gamble thus we would
 

00:06:18.160 --> 00:06:18.710 align:start position:0%
than rejecting gamble thus we would
expect

00:06:18.710 --> 00:06:18.720 align:start position:0%
expect
 

00:06:18.720 --> 00:06:21.270 align:start position:0%
expect
to<00:06:18.880><c> observe</c><00:06:19.520><c> larger</c><00:06:19.919><c> pupil</c><00:06:20.160><c> dilations</c><00:06:21.039><c> when</c>

00:06:21.270 --> 00:06:21.280 align:start position:0%
to observe larger pupil dilations when
 

00:06:21.280 --> 00:06:23.189 align:start position:0%
to observe larger pupil dilations when
gamble<00:06:21.600><c> are</c><00:06:21.680><c> being</c><00:06:21.919><c> accepted</c>

00:06:23.189 --> 00:06:23.199 align:start position:0%
gamble are being accepted
 

00:06:23.199 --> 00:06:26.390 align:start position:0%
gamble are being accepted
than<00:06:23.520><c> when</c><00:06:23.680><c> the</c><00:06:23.840><c> gamble</c><00:06:24.240><c> are</c><00:06:24.319><c> being</c><00:06:24.960><c> rejected</c>

00:06:26.390 --> 00:06:26.400 align:start position:0%
than when the gamble are being rejected
 

00:06:26.400 --> 00:06:28.950 align:start position:0%
than when the gamble are being rejected
in<00:06:26.639><c> short</c><00:06:27.120><c> we</c><00:06:27.360><c> predict</c><00:06:28.080><c> pupil</c><00:06:28.400><c> dilation</c><00:06:28.880><c> in</c>

00:06:28.950 --> 00:06:28.960 align:start position:0%
in short we predict pupil dilation in
 

00:06:28.960 --> 00:06:29.830 align:start position:0%
in short we predict pupil dilation in
this<00:06:29.120><c> thing</c><00:06:29.360><c> making</c>

00:06:29.830 --> 00:06:29.840 align:start position:0%
this thing making
 

00:06:29.840 --> 00:06:33.749 align:start position:0%
this thing making
is<00:06:30.000><c> related</c><00:06:30.560><c> to</c><00:06:31.039><c> response</c><00:06:31.520><c> virus</c>

00:06:33.749 --> 00:06:33.759 align:start position:0%
is related to response virus
 

00:06:33.759 --> 00:06:37.830 align:start position:0%
is related to response virus
this<00:06:34.000><c> is</c><00:06:34.240><c> summary</c><00:06:35.120><c> of</c><00:06:35.360><c> our</c><00:06:35.520><c> framework</c><00:06:36.639><c> we</c>

00:06:37.830 --> 00:06:37.840 align:start position:0%
this is summary of our framework we
 

00:06:37.840 --> 00:06:39.909 align:start position:0%
this is summary of our framework we
assume<00:06:38.240><c> that</c><00:06:38.800><c> loss</c><00:06:39.120><c> variant</c><00:06:39.440><c> can</c><00:06:39.680><c> be</c>

00:06:39.909 --> 00:06:39.919 align:start position:0%
assume that loss variant can be
 

00:06:39.919 --> 00:06:41.029 align:start position:0%
assume that loss variant can be
decomposed

00:06:41.029 --> 00:06:41.039 align:start position:0%
decomposed
 

00:06:41.039 --> 00:06:43.670 align:start position:0%
decomposed
into<00:06:41.360><c> a</c><00:06:41.440><c> validation</c><00:06:42.000><c> virus</c><00:06:42.880><c> and</c><00:06:43.120><c> a</c><00:06:43.199><c> response</c>

00:06:43.670 --> 00:06:43.680 align:start position:0%
into a validation virus and a response
 

00:06:43.680 --> 00:06:44.230 align:start position:0%
into a validation virus and a response
bias

00:06:44.230 --> 00:06:44.240 align:start position:0%
bias
 

00:06:44.240 --> 00:06:45.990 align:start position:0%
bias
which<00:06:44.479><c> can</c><00:06:44.720><c> be</c><00:06:45.199><c> formulated</c><00:06:45.840><c> in</c><00:06:45.919><c> a</c>

00:06:45.990 --> 00:06:46.000 align:start position:0%
which can be formulated in a
 

00:06:46.000 --> 00:06:47.510 align:start position:0%
which can be formulated in a
computational<00:06:46.560><c> model</c>

00:06:47.510 --> 00:06:47.520 align:start position:0%
computational model
 

00:06:47.520 --> 00:06:50.150 align:start position:0%
computational model
and<00:06:48.080><c> validation</c><00:06:48.560><c> bias</c><00:06:49.039><c> is</c><00:06:49.199><c> related</c><00:06:49.680><c> to</c><00:06:49.919><c> case</c>

00:06:50.150 --> 00:06:50.160 align:start position:0%
and validation bias is related to case
 

00:06:50.160 --> 00:06:50.870 align:start position:0%
and validation bias is related to case
allocation

00:06:50.870 --> 00:06:50.880 align:start position:0%
allocation
 

00:06:50.880 --> 00:06:53.909 align:start position:0%
allocation
to<00:06:51.120><c> loss</c><00:06:51.840><c> and</c><00:06:52.000><c> gain</c><00:06:52.639><c> and</c><00:06:52.960><c> response</c><00:06:53.360><c> virus</c><00:06:53.759><c> is</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
to loss and gain and response virus is
 

00:06:53.919 --> 00:06:55.510 align:start position:0%
to loss and gain and response virus is
related<00:06:54.319><c> to</c><00:06:54.479><c> publication</c>

00:06:55.510 --> 00:06:55.520 align:start position:0%
related to publication
 

00:06:55.520 --> 00:06:59.749 align:start position:0%
related to publication
to<00:06:55.680><c> accepting</c><00:06:56.479><c> and</c><00:06:56.639><c> rejecting</c><00:06:57.120><c> gambles</c>

00:06:59.749 --> 00:06:59.759 align:start position:0%
 
 

00:06:59.759 --> 00:07:02.230 align:start position:0%
 
we<00:06:59.919><c> tested</c><00:07:00.479><c> our</c><00:07:00.639><c> hypothesis</c><00:07:01.360><c> in</c><00:07:01.520><c> eye</c><00:07:01.840><c> checking</c>

00:07:02.230 --> 00:07:02.240 align:start position:0%
we tested our hypothesis in eye checking
 

00:07:02.240 --> 00:07:03.110 align:start position:0%
we tested our hypothesis in eye checking
experiments

00:07:03.110 --> 00:07:03.120 align:start position:0%
experiments
 

00:07:03.120 --> 00:07:05.110 align:start position:0%
experiments
participants<00:07:03.840><c> participants</c><00:07:04.400><c> were</c><00:07:04.639><c> presented</c>

00:07:05.110 --> 00:07:05.120 align:start position:0%
participants participants were presented
 

00:07:05.120 --> 00:07:06.469 align:start position:0%
participants participants were presented
with<00:07:05.360><c> 100</c><00:07:05.840><c> gambles</c>

00:07:06.469 --> 00:07:06.479 align:start position:0%
with 100 gambles
 

00:07:06.479 --> 00:07:09.589 align:start position:0%
with 100 gambles
for<00:07:06.639><c> equal</c><00:07:07.039><c> chance</c><00:07:07.680><c> to</c><00:07:07.840><c> win</c><00:07:08.319><c> and</c><00:07:08.560><c> lose</c><00:07:08.800><c> money</c>

00:07:09.589 --> 00:07:09.599 align:start position:0%
for equal chance to win and lose money
 

00:07:09.599 --> 00:07:11.749 align:start position:0%
for equal chance to win and lose money
for<00:07:09.759><c> each</c><00:07:10.000><c> gamble</c><00:07:10.560><c> they</c><00:07:10.800><c> press</c><00:07:11.199><c> one</c><00:07:11.360><c> or</c><00:07:11.520><c> two</c>

00:07:11.749 --> 00:07:11.759 align:start position:0%
for each gamble they press one or two
 

00:07:11.759 --> 00:07:14.309 align:start position:0%
for each gamble they press one or two
keys<00:07:12.160><c> to</c><00:07:12.400><c> accept</c><00:07:12.880><c> or</c><00:07:12.960><c> reject</c><00:07:13.360><c> dates</c>

00:07:14.309 --> 00:07:14.319 align:start position:0%
keys to accept or reject dates
 

00:07:14.319 --> 00:07:16.870 align:start position:0%
keys to accept or reject dates
this<00:07:14.560><c> is</c><00:07:14.800><c> one</c><00:07:15.120><c> example</c><00:07:16.000><c> uh</c><00:07:16.240><c> the</c><00:07:16.400><c> left</c><00:07:16.720><c> is</c>

00:07:16.870 --> 00:07:16.880 align:start position:0%
this is one example uh the left is
 

00:07:16.880 --> 00:07:18.309 align:start position:0%
this is one example uh the left is
potential<00:07:17.360><c> gain</c><00:07:17.680><c> and</c><00:07:17.840><c> the</c><00:07:17.919><c> right</c><00:07:18.160><c> is</c>

00:07:18.309 --> 00:07:18.319 align:start position:0%
potential gain and the right is
 

00:07:18.319 --> 00:07:20.150 align:start position:0%
potential gain and the right is
potential<00:07:18.800><c> loss</c>

00:07:20.150 --> 00:07:20.160 align:start position:0%
potential loss
 

00:07:20.160 --> 00:07:23.589 align:start position:0%
potential loss
there<00:07:20.319><c> are</c><00:07:20.400><c> 100</c><00:07:20.960><c> different</c><00:07:21.280><c> gambles</c><00:07:21.840><c> in</c><00:07:22.000><c> total</c>

00:07:23.589 --> 00:07:23.599 align:start position:0%
there are 100 different gambles in total
 

00:07:23.599 --> 00:07:26.070 align:start position:0%
there are 100 different gambles in total
we<00:07:23.840><c> recorded</c><00:07:24.560><c> participants</c><00:07:25.199><c> choice</c><00:07:25.919><c> and</c>

00:07:26.070 --> 00:07:26.080 align:start position:0%
we recorded participants choice and
 

00:07:26.080 --> 00:07:29.189 align:start position:0%
we recorded participants choice and
response<00:07:26.560><c> time</c><00:07:26.960><c> for</c><00:07:27.120><c> each</c><00:07:27.360><c> gamble</c>

00:07:29.189 --> 00:07:29.199 align:start position:0%
response time for each gamble
 

00:07:29.199 --> 00:07:31.510 align:start position:0%
response time for each gamble
then<00:07:29.440><c> we</c><00:07:29.599><c> can</c><00:07:29.840><c> fit</c><00:07:30.080><c> our</c><00:07:30.319><c> model</c><00:07:30.720><c> with</c><00:07:30.960><c> observed</c>

00:07:31.510 --> 00:07:31.520 align:start position:0%
then we can fit our model with observed
 

00:07:31.520 --> 00:07:33.270 align:start position:0%
then we can fit our model with observed
choice<00:07:31.919><c> and</c><00:07:32.080><c> response</c><00:07:32.479><c> time</c><00:07:32.720><c> data</c>

00:07:33.270 --> 00:07:33.280 align:start position:0%
choice and response time data
 

00:07:33.280 --> 00:07:35.270 align:start position:0%
choice and response time data
to<00:07:33.440><c> identify</c><00:07:34.080><c> each</c><00:07:34.240><c> participant's</c><00:07:34.800><c> valuation</c>

00:07:35.270 --> 00:07:35.280 align:start position:0%
to identify each participant's valuation
 

00:07:35.280 --> 00:07:38.790 align:start position:0%
to identify each participant's valuation
bars<00:07:35.919><c> and</c><00:07:36.160><c> response</c><00:07:36.639><c> bars</c>

00:07:38.790 --> 00:07:38.800 align:start position:0%
bars and response bars
 

00:07:38.800 --> 00:07:41.589 align:start position:0%
bars and response bars
we<00:07:39.039><c> found</c><00:07:39.280><c> that</c><00:07:40.160><c> most</c><00:07:40.479><c> participants</c><00:07:41.280><c> showed</c>

00:07:41.589 --> 00:07:41.599 align:start position:0%
we found that most participants showed
 

00:07:41.599 --> 00:07:43.350 align:start position:0%
we found that most participants showed
both<00:07:42.000><c> validation</c><00:07:42.560><c> parts</c>

00:07:43.350 --> 00:07:43.360 align:start position:0%
both validation parts
 

00:07:43.360 --> 00:07:47.510 align:start position:0%
both validation parts
and<00:07:44.400><c> response</c><00:07:44.879><c> bars</c>

00:07:47.510 --> 00:07:47.520 align:start position:0%
 
 

00:07:47.520 --> 00:07:49.909 align:start position:0%
 
next<00:07:48.000><c> let's</c><00:07:48.319><c> see</c><00:07:48.720><c> whether</c><00:07:49.039><c> the</c><00:07:49.120><c> true</c><00:07:49.360><c> viruses</c>

00:07:49.909 --> 00:07:49.919 align:start position:0%
next let's see whether the true viruses
 

00:07:49.919 --> 00:07:52.150 align:start position:0%
next let's see whether the true viruses
can<00:07:50.080><c> be</c><00:07:50.240><c> identified</c><00:07:50.879><c> from</c><00:07:51.039><c> gate</c><00:07:51.360><c> allocation</c>

00:07:52.150 --> 00:07:52.160 align:start position:0%
can be identified from gate allocation
 

00:07:52.160 --> 00:07:55.830 align:start position:0%
can be identified from gate allocation
and<00:07:52.400><c> public</c><00:07:52.720><c> direction</c>

00:07:55.830 --> 00:07:55.840 align:start position:0%
 
 

00:07:55.840 --> 00:07:57.749 align:start position:0%
 
this<00:07:56.080><c> heat</c><00:07:56.319><c> map</c><00:07:56.560><c> shows</c><00:07:56.879><c> the</c><00:07:57.039><c> probability</c><00:07:57.680><c> of</c>

00:07:57.749 --> 00:07:57.759 align:start position:0%
this heat map shows the probability of
 

00:07:57.759 --> 00:08:00.070 align:start position:0%
this heat map shows the probability of
gaze<00:07:58.080><c> allocation</c><00:07:58.639><c> on</c><00:07:58.800><c> the</c><00:07:58.879><c> screen</c>

00:08:00.070 --> 00:08:00.080 align:start position:0%
gaze allocation on the screen
 

00:08:00.080 --> 00:08:02.309 align:start position:0%
gaze allocation on the screen
the<00:08:00.240><c> brighter</c><00:08:00.800><c> pixel</c><00:08:01.280><c> is</c><00:08:01.599><c> the</c><00:08:02.000><c> higher</c>

00:08:02.309 --> 00:08:02.319 align:start position:0%
the brighter pixel is the higher
 

00:08:02.319 --> 00:08:05.189 align:start position:0%
the brighter pixel is the higher
probability<00:08:03.039><c> it</c><00:08:03.199><c> is</c><00:08:03.440><c> fixated</c><00:08:04.080><c> at</c>

00:08:05.189 --> 00:08:05.199 align:start position:0%
probability it is fixated at
 

00:08:05.199 --> 00:08:07.909 align:start position:0%
probability it is fixated at
we<00:08:05.360><c> can</c><00:08:05.520><c> see</c><00:08:05.759><c> that</c><00:08:06.319><c> the</c><00:08:06.560><c> loss</c><00:08:06.800><c> region</c><00:08:07.759><c> is</c>

00:08:07.909 --> 00:08:07.919 align:start position:0%
we can see that the loss region is
 

00:08:07.919 --> 00:08:09.189 align:start position:0%
we can see that the loss region is
slightly<00:08:08.479><c> brighter</c>

00:08:09.189 --> 00:08:09.199 align:start position:0%
slightly brighter
 

00:08:09.199 --> 00:08:11.430 align:start position:0%
slightly brighter
than<00:08:09.360><c> the</c><00:08:09.440><c> gain</c><00:08:09.680><c> region</c><00:08:10.400><c> which</c><00:08:10.720><c> suggests</c><00:08:11.199><c> a</c>

00:08:11.430 --> 00:08:11.440 align:start position:0%
than the gain region which suggests a
 

00:08:11.440 --> 00:08:12.309 align:start position:0%
than the gain region which suggests a
case<00:08:11.759><c> virus</c>

00:08:12.309 --> 00:08:12.319 align:start position:0%
case virus
 

00:08:12.319 --> 00:08:15.990 align:start position:0%
case virus
toward<00:08:12.720><c> loss</c><00:08:14.080><c> a</c><00:08:14.240><c> question</c><00:08:14.720><c> is</c><00:08:15.280><c> is</c><00:08:15.440><c> this</c><00:08:15.680><c> case</c>

00:08:15.990 --> 00:08:16.000 align:start position:0%
toward loss a question is is this case
 

00:08:16.000 --> 00:08:16.469 align:start position:0%
toward loss a question is is this case
virus

00:08:16.469 --> 00:08:16.479 align:start position:0%
virus
 

00:08:16.479 --> 00:08:19.909 align:start position:0%
virus
related<00:08:16.960><c> to</c><00:08:17.120><c> these</c><00:08:17.360><c> decisions</c>

00:08:19.909 --> 00:08:19.919 align:start position:0%
related to these decisions
 

00:08:19.919 --> 00:08:21.749 align:start position:0%
related to these decisions
yes<00:08:20.319><c> we</c><00:08:20.479><c> found</c><00:08:20.720><c> a</c><00:08:20.879><c> negative</c><00:08:21.199><c> correlation</c>

00:08:21.749 --> 00:08:21.759 align:start position:0%
yes we found a negative correlation
 

00:08:21.759 --> 00:08:23.270 align:start position:0%
yes we found a negative correlation
between<00:08:22.080><c> gas</c><00:08:22.400><c> virus</c><00:08:22.800><c> toward</c>

00:08:23.270 --> 00:08:23.280 align:start position:0%
between gas virus toward
 

00:08:23.280 --> 00:08:25.909 align:start position:0%
between gas virus toward
loss<00:08:23.759><c> and</c><00:08:23.919><c> probability</c><00:08:24.560><c> of</c><00:08:24.840><c> acceptance</c>

00:08:25.909 --> 00:08:25.919 align:start position:0%
loss and probability of acceptance
 

00:08:25.919 --> 00:08:27.830 align:start position:0%
loss and probability of acceptance
participants<00:08:26.560><c> who</c><00:08:26.720><c> spend</c><00:08:27.039><c> more</c><00:08:27.280><c> time</c><00:08:27.599><c> looking</c>

00:08:27.830 --> 00:08:27.840 align:start position:0%
participants who spend more time looking
 

00:08:27.840 --> 00:08:28.629 align:start position:0%
participants who spend more time looking
at<00:08:27.919><c> the</c><00:08:28.080><c> laws</c>

00:08:28.629 --> 00:08:28.639 align:start position:0%
at the laws
 

00:08:28.639 --> 00:08:31.909 align:start position:0%
at the laws
also<00:08:28.960><c> accepted</c><00:08:29.680><c> fewer</c><00:08:30.000><c> gambles</c>

00:08:31.909 --> 00:08:31.919 align:start position:0%
also accepted fewer gambles
 

00:08:31.919 --> 00:08:34.949 align:start position:0%
also accepted fewer gambles
next<00:08:32.719><c> we</c><00:08:32.880><c> would</c><00:08:33.120><c> like</c><00:08:33.440><c> to</c><00:08:33.680><c> know</c><00:08:34.399><c> whether</c><00:08:34.719><c> these</c>

00:08:34.949 --> 00:08:34.959 align:start position:0%
next we would like to know whether these
 

00:08:34.959 --> 00:08:36.310 align:start position:0%
next we would like to know whether these
guild<00:08:35.200><c> values</c><00:08:35.599><c> reflect</c>

00:08:36.310 --> 00:08:36.320 align:start position:0%
guild values reflect
 

00:08:36.320 --> 00:08:40.709 align:start position:0%
guild values reflect
valuation<00:08:36.880><c> balance</c><00:08:37.680><c> or</c><00:08:38.080><c> response</c><00:08:38.839><c> virus</c>

00:08:40.709 --> 00:08:40.719 align:start position:0%
valuation balance or response virus
 

00:08:40.719 --> 00:08:43.909 align:start position:0%
valuation balance or response virus
as<00:08:41.039><c> predicted</c><00:08:41.680><c> we</c><00:08:41.919><c> found</c><00:08:42.240><c> that</c><00:08:42.560><c> case</c><00:08:42.880><c> bias</c><00:08:43.680><c> is</c>

00:08:43.909 --> 00:08:43.919 align:start position:0%
as predicted we found that case bias is
 

00:08:43.919 --> 00:08:46.630 align:start position:0%
as predicted we found that case bias is
mainly<00:08:44.320><c> associated</c><00:08:45.040><c> with</c><00:08:45.279><c> validation</c><00:08:45.839><c> bias</c>

00:08:46.630 --> 00:08:46.640 align:start position:0%
mainly associated with validation bias
 

00:08:46.640 --> 00:08:50.150 align:start position:0%
mainly associated with validation bias
not<00:08:46.880><c> response</c><00:08:47.360><c> virus</c>

00:08:50.150 --> 00:08:50.160 align:start position:0%
 
 

00:08:50.160 --> 00:08:53.350 align:start position:0%
 
next<00:08:50.640><c> pupil</c><00:08:51.360><c> we</c><00:08:51.600><c> predict</c><00:08:52.080><c> that</c><00:08:52.720><c> when</c><00:08:52.880><c> there</c><00:08:53.200><c> is</c>

00:08:53.350 --> 00:08:53.360 align:start position:0%
next pupil we predict that when there is
 

00:08:53.360 --> 00:08:55.750 align:start position:0%
next pupil we predict that when there is
a<00:08:53.519><c> response</c><00:08:53.920><c> balance</c><00:08:54.320><c> to</c><00:08:54.560><c> reject</c><00:08:54.959><c> gambles</c>

00:08:55.750 --> 00:08:55.760 align:start position:0%
a response balance to reject gambles
 

00:08:55.760 --> 00:08:58.550 align:start position:0%
a response balance to reject gambles
accepting<00:08:56.480><c> gambles</c><00:08:57.200><c> would</c><00:08:57.519><c> be</c><00:08:57.760><c> effortful</c><00:08:58.399><c> and</c>

00:08:58.550 --> 00:08:58.560 align:start position:0%
accepting gambles would be effortful and
 

00:08:58.560 --> 00:09:00.070 align:start position:0%
accepting gambles would be effortful and
thus<00:08:58.800><c> delivered</c><00:08:59.120><c> people</c>

00:09:00.070 --> 00:09:00.080 align:start position:0%
thus delivered people
 

00:09:00.080 --> 00:09:02.470 align:start position:0%
thus delivered people
as<00:09:00.320><c> predicted</c><00:09:00.880><c> we</c><00:09:01.040><c> found</c><00:09:01.279><c> that</c><00:09:01.760><c> when</c><00:09:02.000><c> gambles</c>

00:09:02.470 --> 00:09:02.480 align:start position:0%
as predicted we found that when gambles
 

00:09:02.480 --> 00:09:03.670 align:start position:0%
as predicted we found that when gambles
were<00:09:02.640><c> being</c><00:09:02.880><c> accepted</c>

00:09:03.670 --> 00:09:03.680 align:start position:0%
were being accepted
 

00:09:03.680 --> 00:09:06.230 align:start position:0%
were being accepted
relative<00:09:04.160><c> to</c><00:09:04.320><c> being</c><00:09:04.560><c> rejected</c><00:09:05.519><c> participants</c>

00:09:06.230 --> 00:09:06.240 align:start position:0%
relative to being rejected participants
 

00:09:06.240 --> 00:09:08.790 align:start position:0%
relative to being rejected participants
showed<00:09:06.640><c> larger</c><00:09:06.959><c> purple</c><00:09:07.279><c> direction</c>

00:09:08.790 --> 00:09:08.800 align:start position:0%
showed larger purple direction
 

00:09:08.800 --> 00:09:11.269 align:start position:0%
showed larger purple direction
moreover<00:09:09.519><c> we</c><00:09:09.760><c> found</c><00:09:10.000><c> a</c><00:09:10.240><c> negative</c><00:09:10.640><c> correlation</c>

00:09:11.269 --> 00:09:11.279 align:start position:0%
moreover we found a negative correlation
 

00:09:11.279 --> 00:09:13.110 align:start position:0%
moreover we found a negative correlation
between<00:09:11.519><c> probability</c><00:09:12.160><c> of</c><00:09:12.240><c> acceptance</c>

00:09:13.110 --> 00:09:13.120 align:start position:0%
between probability of acceptance
 

00:09:13.120 --> 00:09:15.670 align:start position:0%
between probability of acceptance
and<00:09:13.279><c> differential</c><00:09:13.760><c> publication</c><00:09:14.800><c> to</c><00:09:14.959><c> accept</c>

00:09:15.670 --> 00:09:15.680 align:start position:0%
and differential publication to accept
 

00:09:15.680 --> 00:09:17.829 align:start position:0%
and differential publication to accept
and<00:09:15.839><c> reject</c><00:09:16.240><c> gambles</c>

00:09:17.829 --> 00:09:17.839 align:start position:0%
and reject gambles
 

00:09:17.839 --> 00:09:20.870 align:start position:0%
and reject gambles
that<00:09:18.080><c> is</c><00:09:18.480><c> participants</c><00:09:19.360><c> who</c><00:09:19.600><c> tend</c><00:09:19.920><c> to</c><00:09:20.399><c> reject</c>

00:09:20.870 --> 00:09:20.880 align:start position:0%
that is participants who tend to reject
 

00:09:20.880 --> 00:09:23.750 align:start position:0%
that is participants who tend to reject
gambles<00:09:21.839><c> show</c><00:09:22.000><c> the</c><00:09:22.240><c> larger</c><00:09:22.640><c> publications</c>

00:09:23.750 --> 00:09:23.760 align:start position:0%
gambles show the larger publications
 

00:09:23.760 --> 00:09:27.190 align:start position:0%
gambles show the larger publications
when<00:09:24.080><c> accepting</c><00:09:24.560><c> the</c><00:09:24.640><c> gamble</c>

00:09:27.190 --> 00:09:27.200 align:start position:0%
 
 

00:09:27.200 --> 00:09:29.030 align:start position:0%
 
finally<00:09:27.680><c> we</c><00:09:27.839><c> would</c><00:09:28.080><c> like</c><00:09:28.320><c> to</c><00:09:28.480><c> know</c><00:09:28.720><c> whether</c>

00:09:29.030 --> 00:09:29.040 align:start position:0%
finally we would like to know whether
 

00:09:29.040 --> 00:09:30.310 align:start position:0%
finally we would like to know whether
this<00:09:29.279><c> pupil</c><00:09:29.600><c> dilation</c>

00:09:30.310 --> 00:09:30.320 align:start position:0%
this pupil dilation
 

00:09:30.320 --> 00:09:32.710 align:start position:0%
this pupil dilation
reflects<00:09:30.800><c> evaluation</c><00:09:31.440><c> virus</c><00:09:32.000><c> or</c><00:09:32.240><c> response</c>

00:09:32.710 --> 00:09:32.720 align:start position:0%
reflects evaluation virus or response
 

00:09:32.720 --> 00:09:33.750 align:start position:0%
reflects evaluation virus or response
virus

00:09:33.750 --> 00:09:33.760 align:start position:0%
virus
 

00:09:33.760 --> 00:09:36.550 align:start position:0%
virus
we<00:09:34.000><c> found</c><00:09:34.240><c> that</c><00:09:34.880><c> larger</c><00:09:35.200><c> pupil</c><00:09:35.440><c> dilation</c><00:09:36.399><c> to</c>

00:09:36.550 --> 00:09:36.560 align:start position:0%
we found that larger pupil dilation to
 

00:09:36.560 --> 00:09:38.070 align:start position:0%
we found that larger pupil dilation to
accepting<00:09:37.040><c> gambles</c><00:09:37.680><c> may</c>

00:09:38.070 --> 00:09:38.080 align:start position:0%
accepting gambles may
 

00:09:38.080 --> 00:09:40.790 align:start position:0%
accepting gambles may
reflect<00:09:38.720><c> response</c><00:09:39.120><c> bars</c><00:09:39.680><c> but</c><00:09:39.920><c> not</c><00:09:40.240><c> validation</c>

00:09:40.790 --> 00:09:40.800 align:start position:0%
reflect response bars but not validation
 

00:09:40.800 --> 00:09:43.430 align:start position:0%
reflect response bars but not validation
bars

00:09:43.430 --> 00:09:43.440 align:start position:0%
 
 

00:09:43.440 --> 00:09:49.750 align:start position:0%
 
so<00:09:43.920><c> all</c><00:09:44.240><c> results</c><00:09:44.880><c> confirm</c><00:09:45.839><c> our</c><00:09:46.080><c> framework</c>

00:09:49.750 --> 00:09:49.760 align:start position:0%
 
 

00:09:49.760 --> 00:09:52.070 align:start position:0%
 
our<00:09:50.000><c> fundings</c><00:09:50.560><c> have</c><00:09:50.880><c> important</c><00:09:51.600><c> practical</c>

00:09:52.070 --> 00:09:52.080 align:start position:0%
our fundings have important practical
 

00:09:52.080 --> 00:09:53.430 align:start position:0%
our fundings have important practical
implications

00:09:53.430 --> 00:09:53.440 align:start position:0%
implications
 

00:09:53.440 --> 00:09:55.829 align:start position:0%
implications
uh<00:09:53.680><c> in</c><00:09:53.839><c> our</c><00:09:54.000><c> study</c><00:09:54.480><c> we</c><00:09:54.720><c> showed</c><00:09:54.959><c> that</c><00:09:55.600><c> this</c>

00:09:55.829 --> 00:09:55.839 align:start position:0%
uh in our study we showed that this
 

00:09:55.839 --> 00:09:58.630 align:start position:0%
uh in our study we showed that this
allocation<00:09:56.399><c> can</c><00:09:56.720><c> impact</c><00:09:57.279><c> the</c><00:09:57.360><c> loss</c><00:09:57.600><c> of</c><00:09:57.680><c> bergen</c>

00:09:58.630 --> 00:09:58.640 align:start position:0%
allocation can impact the loss of bergen
 

00:09:58.640 --> 00:10:00.630 align:start position:0%
allocation can impact the loss of bergen
consequently<00:09:59.279><c> we</c><00:09:59.440><c> would</c><00:09:59.600><c> predict</c><00:10:00.080><c> that</c>

00:10:00.630 --> 00:10:00.640 align:start position:0%
consequently we would predict that
 

00:10:00.640 --> 00:10:01.829 align:start position:0%
consequently we would predict that
manipulation<00:10:01.360><c> gains</c>

00:10:01.829 --> 00:10:01.839 align:start position:0%
manipulation gains
 

00:10:01.839 --> 00:10:04.870 align:start position:0%
manipulation gains
allocation<00:10:02.720><c> may</c><00:10:02.959><c> change</c><00:10:03.440><c> raspberry</c><00:10:04.399><c> and</c><00:10:04.640><c> one</c>

00:10:04.870 --> 00:10:04.880 align:start position:0%
allocation may change raspberry and one
 

00:10:04.880 --> 00:10:05.670 align:start position:0%
allocation may change raspberry and one
simple<00:10:05.279><c> way</c>

00:10:05.670 --> 00:10:05.680 align:start position:0%
simple way
 

00:10:05.680 --> 00:10:07.829 align:start position:0%
simple way
to<00:10:05.839><c> manipulate</c><00:10:06.399><c> the</c><00:10:06.480><c> gaze</c><00:10:06.720><c> allocation</c><00:10:07.519><c> is</c><00:10:07.680><c> to</c>

00:10:07.829 --> 00:10:07.839 align:start position:0%
to manipulate the gaze allocation is to
 

00:10:07.839 --> 00:10:09.430 align:start position:0%
to manipulate the gaze allocation is to
change<00:10:08.079><c> the</c><00:10:08.160><c> view</c><00:10:08.560><c> ceilings</c>

00:10:09.430 --> 00:10:09.440 align:start position:0%
change the view ceilings
 

00:10:09.440 --> 00:10:12.230 align:start position:0%
change the view ceilings
of<00:10:09.600><c> gain</c><00:10:09.839><c> and</c><00:10:10.000><c> loss</c><00:10:10.800><c> for</c><00:10:10.959><c> example</c><00:10:11.680><c> making</c><00:10:12.000><c> the</c>

00:10:12.230 --> 00:10:12.240 align:start position:0%
of gain and loss for example making the
 

00:10:12.240 --> 00:10:12.870 align:start position:0%
of gain and loss for example making the
gains

00:10:12.870 --> 00:10:12.880 align:start position:0%
gains
 

00:10:12.880 --> 00:10:15.269 align:start position:0%
gains
from<00:10:13.120><c> size</c><00:10:13.519><c> larger</c><00:10:13.839><c> than</c><00:10:14.079><c> loss</c><00:10:14.560><c> of</c><00:10:14.800><c> making</c><00:10:15.120><c> the</c>

00:10:15.269 --> 00:10:15.279 align:start position:0%
from size larger than loss of making the
 

00:10:15.279 --> 00:10:18.310 align:start position:0%
from size larger than loss of making the
loss<00:10:15.600><c> from</c><00:10:15.920><c> size</c><00:10:16.160><c> larger</c><00:10:16.480><c> than</c><00:10:16.839><c> gain</c>

00:10:18.310 --> 00:10:18.320 align:start position:0%
loss from size larger than gain
 

00:10:18.320 --> 00:10:19.910 align:start position:0%
loss from size larger than gain
we<00:10:18.480><c> tested</c><00:10:18.959><c> our</c><00:10:19.120><c> hypothesis</c><00:10:19.680><c> in</c><00:10:19.839><c> the</c>

00:10:19.910 --> 00:10:19.920 align:start position:0%
we tested our hypothesis in the
 

00:10:19.920 --> 00:10:21.990 align:start position:0%
we tested our hypothesis in the
follow-up<00:10:20.399><c> experiment</c><00:10:20.959><c> we</c><00:10:21.120><c> found</c><00:10:21.360><c> that</c>

00:10:21.990 --> 00:10:22.000 align:start position:0%
follow-up experiment we found that
 

00:10:22.000 --> 00:10:24.550 align:start position:0%
follow-up experiment we found that
when<00:10:22.240><c> gainfund</c><00:10:22.800><c> size</c><00:10:23.040><c> is</c><00:10:23.279><c> larger</c>

00:10:24.550 --> 00:10:24.560 align:start position:0%
when gainfund size is larger
 

00:10:24.560 --> 00:10:25.509 align:start position:0%
when gainfund size is larger
participants

00:10:25.509 --> 00:10:25.519 align:start position:0%
participants
 

00:10:25.519 --> 00:10:28.630 align:start position:0%
participants
accept<00:10:26.000><c> more</c><00:10:26.240><c> gambles</c><00:10:27.120><c> and</c><00:10:27.600><c> when</c><00:10:27.920><c> lost</c><00:10:28.399><c> font</c>

00:10:28.630 --> 00:10:28.640 align:start position:0%
accept more gambles and when lost font
 

00:10:28.640 --> 00:10:29.750 align:start position:0%
accept more gambles and when lost font
size<00:10:28.959><c> is</c><00:10:29.120><c> larger</c>

00:10:29.750 --> 00:10:29.760 align:start position:0%
size is larger
 

00:10:29.760 --> 00:10:32.949 align:start position:0%
size is larger
participants<00:10:30.399><c> rejected</c><00:10:30.959><c> more</c><00:10:31.120><c> gambles</c><00:10:32.560><c> and</c>

00:10:32.949 --> 00:10:32.959 align:start position:0%
participants rejected more gambles and
 

00:10:32.959 --> 00:10:35.110 align:start position:0%
participants rejected more gambles and
nowadays<00:10:33.440><c> we</c><00:10:33.600><c> know</c><00:10:33.760><c> that</c><00:10:34.079><c> consumer</c><00:10:34.640><c> finance</c>

00:10:35.110 --> 00:10:35.120 align:start position:0%
nowadays we know that consumer finance
 

00:10:35.120 --> 00:10:37.269 align:start position:0%
nowadays we know that consumer finance
apps<00:10:35.680><c> can</c><00:10:35.920><c> easily</c><00:10:36.480><c> change</c>

00:10:37.269 --> 00:10:37.279 align:start position:0%
apps can easily change
 

00:10:37.279 --> 00:10:40.470 align:start position:0%
apps can easily change
the<00:10:37.440><c> phone</c><00:10:37.760><c> size</c><00:10:38.320><c> or</c><00:10:38.480><c> gain</c><00:10:38.800><c> and</c><00:10:39.040><c> loss</c><00:10:39.839><c> or</c>

00:10:40.470 --> 00:10:40.480 align:start position:0%
the phone size or gain and loss or
 

00:10:40.480 --> 00:10:43.750 align:start position:0%
the phone size or gain and loss or
revenue<00:10:40.959><c> and</c><00:10:41.200><c> cost</c><00:10:41.839><c> on</c><00:10:41.920><c> their</c><00:10:42.160><c> apps</c><00:10:43.200><c> so</c><00:10:43.440><c> if</c><00:10:43.680><c> a</c>

00:10:43.750 --> 00:10:43.760 align:start position:0%
revenue and cost on their apps so if a
 

00:10:43.760 --> 00:10:44.630 align:start position:0%
revenue and cost on their apps so if a
consumer<00:10:44.160><c> finance</c>

00:10:44.630 --> 00:10:44.640 align:start position:0%
consumer finance
 

00:10:44.640 --> 00:10:47.910 align:start position:0%
consumer finance
app<00:10:44.880><c> is</c><00:10:45.040><c> playing</c><00:10:45.600><c> a</c><00:10:46.079><c> game</c><00:10:46.560><c> with</c><00:10:46.800><c> a</c><00:10:47.040><c> larger</c><00:10:47.519><c> font</c>

00:10:47.910 --> 00:10:47.920 align:start position:0%
app is playing a game with a larger font
 

00:10:47.920 --> 00:10:48.550 align:start position:0%
app is playing a game with a larger font
size

00:10:48.550 --> 00:10:48.560 align:start position:0%
size
 

00:10:48.560 --> 00:10:51.829 align:start position:0%
size
than<00:10:48.800><c> loss</c><00:10:49.680><c> it</c><00:10:49.920><c> can</c><00:10:50.160><c> tamp</c><00:10:50.480><c> consumers</c><00:10:51.440><c> to</c><00:10:51.600><c> make</c>

00:10:51.829 --> 00:10:51.839 align:start position:0%
than loss it can tamp consumers to make
 

00:10:51.839 --> 00:10:53.670 align:start position:0%
than loss it can tamp consumers to make
more<00:10:52.079><c> risky</c><00:10:52.480><c> decisions</c>

00:10:53.670 --> 00:10:53.680 align:start position:0%
more risky decisions
 

00:10:53.680 --> 00:10:56.790 align:start position:0%
more risky decisions
this<00:10:53.920><c> may</c><00:10:54.240><c> involve</c><00:10:54.959><c> many</c><00:10:55.279><c> issues</c>

00:10:56.790 --> 00:10:56.800 align:start position:0%
this may involve many issues
 

00:10:56.800 --> 00:11:00.790 align:start position:0%
this may involve many issues
we<00:10:56.959><c> can</c><00:10:58.839><c> discuss</c>

00:11:00.790 --> 00:11:00.800 align:start position:0%
we can discuss
 

00:11:00.800 --> 00:11:08.990 align:start position:0%
we can discuss
okay<00:11:01.040><c> this</c><00:11:01.279><c> is</c><00:11:01.600><c> my</c><00:11:01.760><c> talk</c><00:11:02.000><c> today</c><00:11:02.560><c> thank</c><00:11:02.800><c> you</c>

00:11:08.990 --> 00:11:09.000 align:start position:0%
 
 

00:11:09.000 --> 00:11:12.000 align:start position:0%
 
everyone

