WEBVTT
Kind: captions
Language: en

00:00:05.200 --> 00:00:05.749 align:start position:0%
 
hi

00:00:05.749 --> 00:00:05.759 align:start position:0%
hi
 

00:00:05.759 --> 00:00:07.590 align:start position:0%
hi
my<00:00:05.920><c> name</c><00:00:06.080><c> is</c><00:00:06.240><c> karen</c><00:00:06.480><c> shen</c><00:00:06.960><c> and</c><00:00:07.279><c> i</c><00:00:07.440><c> just</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
my name is karen shen and i just
 

00:00:07.600 --> 00:00:09.110 align:start position:0%
my name is karen shen and i just
graduated<00:00:08.080><c> from</c><00:00:08.160><c> penn</c><00:00:08.400><c> last</c><00:00:08.639><c> year</c><00:00:08.880><c> where</c><00:00:09.040><c> i</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
graduated from penn last year where i
 

00:00:09.120 --> 00:00:11.110 align:start position:0%
graduated from penn last year where i
did<00:00:09.360><c> my</c><00:00:09.519><c> undergrad</c><00:00:09.920><c> in</c><00:00:10.080><c> systems</c><00:00:10.719><c> and</c><00:00:10.960><c> my</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
did my undergrad in systems and my
 

00:00:11.120 --> 00:00:12.629 align:start position:0%
did my undergrad in systems and my
master's<00:00:11.519><c> in</c><00:00:11.599><c> data</c><00:00:11.920><c> science</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
master's in data science
 

00:00:12.639 --> 00:00:14.549 align:start position:0%
master's in data science
today<00:00:12.960><c> i'll</c><00:00:13.120><c> be</c><00:00:13.200><c> telling</c><00:00:13.519><c> you</c><00:00:13.759><c> about</c><00:00:14.080><c> my</c><00:00:14.240><c> dad's</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
today i'll be telling you about my dad's
 

00:00:14.559 --> 00:00:17.109 align:start position:0%
today i'll be telling you about my dad's
practicum<00:00:15.120><c> project</c><00:00:15.839><c> on</c><00:00:16.080><c> predicting</c><00:00:16.560><c> academic</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
practicum project on predicting academic
 

00:00:17.119 --> 00:00:17.670 align:start position:0%
practicum project on predicting academic
success

00:00:17.670 --> 00:00:17.680 align:start position:0%
success
 

00:00:17.680 --> 00:00:19.670 align:start position:0%
success
of<00:00:17.840><c> master's</c><00:00:18.320><c> students</c><00:00:18.800><c> using</c><00:00:19.119><c> application</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
of master's students using application
 

00:00:19.680 --> 00:00:22.710 align:start position:0%
of master's students using application
data<00:00:20.400><c> i</c><00:00:20.560><c> was</c><00:00:20.800><c> advised</c><00:00:21.279><c> by</c><00:00:21.520><c> dr</c><00:00:21.840><c> blunthalu</c>

00:00:22.710 --> 00:00:22.720 align:start position:0%
data i was advised by dr blunthalu
 

00:00:22.720 --> 00:00:26.310 align:start position:0%
data i was advised by dr blunthalu
and<00:00:22.960><c> ira</c><00:00:23.279><c> winston</c><00:00:24.880><c> uh</c><00:00:25.199><c> wild</c><00:00:25.599><c> admissions</c><00:00:26.000><c> staff</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
and ira winston uh wild admissions staff
 

00:00:26.320 --> 00:00:28.070 align:start position:0%
and ira winston uh wild admissions staff
has<00:00:26.480><c> lots</c><00:00:26.720><c> of</c><00:00:26.960><c> experience</c><00:00:27.439><c> and</c><00:00:27.599><c> intuition</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
has lots of experience and intuition
 

00:00:28.080 --> 00:00:29.509 align:start position:0%
has lots of experience and intuition
with<00:00:28.240><c> identifying</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
with identifying
 

00:00:29.519 --> 00:00:31.349 align:start position:0%
with identifying
good<00:00:29.840><c> students</c><00:00:30.400><c> every</c><00:00:30.640><c> year</c><00:00:30.880><c> there's</c><00:00:31.119><c> always</c>

00:00:31.349 --> 00:00:31.359 align:start position:0%
good students every year there's always
 

00:00:31.359 --> 00:00:33.670 align:start position:0%
good students every year there's always
a<00:00:31.439><c> handful</c><00:00:31.840><c> they're</c><00:00:32.000><c> not</c><00:00:32.239><c> able</c><00:00:32.399><c> to</c><00:00:32.559><c> graduate</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
a handful they're not able to graduate
 

00:00:33.680 --> 00:00:35.110 align:start position:0%
a handful they're not able to graduate
this<00:00:33.920><c> is</c><00:00:34.000><c> frustrating</c><00:00:34.559><c> for</c><00:00:34.719><c> both</c><00:00:34.960><c> the</c>

00:00:35.110 --> 00:00:35.120 align:start position:0%
this is frustrating for both the
 

00:00:35.120 --> 00:00:36.950 align:start position:0%
this is frustrating for both the
students<00:00:35.520><c> and</c><00:00:35.680><c> the</c><00:00:35.840><c> administration</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
students and the administration
 

00:00:36.960 --> 00:00:39.030 align:start position:0%
students and the administration
so<00:00:37.200><c> we</c><00:00:37.280><c> want</c><00:00:37.440><c> to</c><00:00:37.600><c> create</c><00:00:37.920><c> a</c><00:00:38.000><c> more</c><00:00:38.320><c> accurate</c><00:00:38.719><c> way</c>

00:00:39.030 --> 00:00:39.040 align:start position:0%
so we want to create a more accurate way
 

00:00:39.040 --> 00:00:40.790 align:start position:0%
so we want to create a more accurate way
to<00:00:39.200><c> identify</c><00:00:39.840><c> the</c><00:00:40.000><c> right</c><00:00:40.239><c> students</c><00:00:40.640><c> from</c>

00:00:40.790 --> 00:00:40.800 align:start position:0%
to identify the right students from
 

00:00:40.800 --> 00:00:42.470 align:start position:0%
to identify the right students from
their<00:00:41.040><c> applications</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
their applications
 

00:00:42.480 --> 00:00:44.950 align:start position:0%
their applications
we<00:00:42.800><c> have</c><00:00:43.040><c> only</c><00:00:43.360><c> recently</c><00:00:43.920><c> accumulated</c><00:00:44.640><c> enough</c>

00:00:44.950 --> 00:00:44.960 align:start position:0%
we have only recently accumulated enough
 

00:00:44.960 --> 00:00:46.869 align:start position:0%
we have only recently accumulated enough
application<00:00:45.520><c> data</c><00:00:45.760><c> to</c><00:00:45.920><c> analyze</c>

00:00:46.869 --> 00:00:46.879 align:start position:0%
application data to analyze
 

00:00:46.879 --> 00:00:49.190 align:start position:0%
application data to analyze
so<00:00:47.200><c> this</c><00:00:47.440><c> project</c><00:00:47.920><c> aims</c><00:00:48.239><c> to</c><00:00:48.399><c> create</c><00:00:48.800><c> a</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
so this project aims to create a
 

00:00:49.200 --> 00:00:51.430 align:start position:0%
so this project aims to create a
data-driven<00:00:50.079><c> approach</c><00:00:50.559><c> to</c><00:00:50.640><c> help</c><00:00:50.879><c> admissions</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
data-driven approach to help admissions
 

00:00:51.440 --> 00:00:52.389 align:start position:0%
data-driven approach to help admissions
identify

00:00:52.389 --> 00:00:52.399 align:start position:0%
identify
 

00:00:52.399 --> 00:00:54.310 align:start position:0%
identify
which<00:00:52.640><c> students</c><00:00:52.960><c> will</c><00:00:53.199><c> struggle</c><00:00:53.680><c> to</c><00:00:53.840><c> graduate</c>

00:00:54.310 --> 00:00:54.320 align:start position:0%
which students will struggle to graduate
 

00:00:54.320 --> 00:00:55.590 align:start position:0%
which students will struggle to graduate
and<00:00:54.399><c> which</c><00:00:54.559><c> will</c><00:00:54.800><c> succeed</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
and which will succeed
 

00:00:55.600 --> 00:00:58.389 align:start position:0%
and which will succeed
at<00:00:55.760><c> penn</c><00:00:56.879><c> we</c><00:00:57.120><c> also</c><00:00:57.360><c> aim</c><00:00:57.520><c> to</c><00:00:57.680><c> find</c><00:00:58.079><c> which</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
at penn we also aim to find which
 

00:00:58.399 --> 00:01:00.069 align:start position:0%
at penn we also aim to find which
specific<00:00:58.879><c> factors</c><00:00:59.359><c> in</c><00:00:59.440><c> the</c><00:00:59.600><c> application</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
specific factors in the application
 

00:01:00.079 --> 00:01:00.950 align:start position:0%
specific factors in the application
profile

00:01:00.950 --> 00:01:00.960 align:start position:0%
profile
 

00:01:00.960 --> 00:01:03.189 align:start position:0%
profile
are<00:01:01.120><c> most</c><00:01:01.440><c> indicative</c><00:01:02.079><c> of</c><00:01:02.320><c> future</c><00:01:02.719><c> academic</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
are most indicative of future academic
 

00:01:03.199 --> 00:01:05.270 align:start position:0%
are most indicative of future academic
performance

00:01:05.270 --> 00:01:05.280 align:start position:0%
performance
 

00:01:05.280 --> 00:01:07.350 align:start position:0%
performance
now<00:01:05.519><c> the</c><00:01:05.680><c> data</c><00:01:06.000><c> set</c><00:01:06.320><c> has</c><00:01:06.640><c> application</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
now the data set has application
 

00:01:07.360 --> 00:01:09.350 align:start position:0%
now the data set has application
information<00:01:07.840><c> of</c><00:01:08.000><c> students</c><00:01:08.400><c> who</c><00:01:08.720><c> enrolled</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
information of students who enrolled
 

00:01:09.360 --> 00:01:11.109 align:start position:0%
information of students who enrolled
in<00:01:09.439><c> the</c><00:01:09.600><c> pen</c><00:01:09.840><c> engineering</c><00:01:10.240><c> master's</c><00:01:10.640><c> program</c>

00:01:11.109 --> 00:01:11.119 align:start position:0%
in the pen engineering master's program
 

00:01:11.119 --> 00:01:13.750 align:start position:0%
in the pen engineering master's program
through<00:01:11.360><c> the</c><00:01:11.520><c> regular</c><00:01:12.159><c> admissions</c><00:01:12.960><c> process</c>

00:01:13.750 --> 00:01:13.760 align:start position:0%
through the regular admissions process
 

00:01:13.760 --> 00:01:15.830 align:start position:0%
through the regular admissions process
this<00:01:14.000><c> includes</c><00:01:14.400><c> their</c><00:01:14.560><c> past</c><00:01:14.880><c> education</c><00:01:15.520><c> data</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
this includes their past education data
 

00:01:15.840 --> 00:01:16.870 align:start position:0%
this includes their past education data
like<00:01:16.080><c> their</c><00:01:16.240><c> school</c>

00:01:16.870 --> 00:01:16.880 align:start position:0%
like their school
 

00:01:16.880 --> 00:01:19.910 align:start position:0%
like their school
major<00:01:17.360><c> gpa</c><00:01:18.080><c> degree</c><00:01:18.400><c> received</c><00:01:18.880><c> etc</c>

00:01:19.910 --> 00:01:19.920 align:start position:0%
major gpa degree received etc
 

00:01:19.920 --> 00:01:22.710 align:start position:0%
major gpa degree received etc
standardized<00:01:20.560><c> test</c><00:01:20.799><c> scores</c><00:01:21.200><c> such</c><00:01:21.439><c> as</c><00:01:21.600><c> the</c><00:01:21.680><c> gre</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
standardized test scores such as the gre
 

00:01:22.720 --> 00:01:25.109 align:start position:0%
standardized test scores such as the gre
english<00:01:23.119><c> tests</c><00:01:23.439><c> like</c><00:01:23.600><c> the</c><00:01:23.759><c> toefl</c><00:01:24.240><c> and</c><00:01:24.320><c> ielts</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
english tests like the toefl and ielts
 

00:01:25.119 --> 00:01:26.870 align:start position:0%
english tests like the toefl and ielts
and<00:01:25.280><c> other</c><00:01:25.520><c> demographic</c><00:01:26.159><c> information</c><00:01:26.640><c> like</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
and other demographic information like
 

00:01:26.880 --> 00:01:29.030 align:start position:0%
and other demographic information like
gender<00:01:27.280><c> and</c><00:01:27.439><c> international</c><00:01:28.000><c> status</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
gender and international status
 

00:01:29.040 --> 00:01:31.590 align:start position:0%
gender and international status
there<00:01:29.200><c> are</c><00:01:29.439><c> over</c><00:01:30.000><c> 10</c><00:01:30.400><c> 000</c><00:01:31.040><c> applicant</c>

00:01:31.590 --> 00:01:31.600 align:start position:0%
there are over 10 000 applicant
 

00:01:31.600 --> 00:01:32.950 align:start position:0%
there are over 10 000 applicant
institution<00:01:32.159><c> records</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
institution records
 

00:01:32.960 --> 00:01:34.950 align:start position:0%
institution records
so<00:01:33.119><c> to</c><00:01:33.280><c> elaborate</c><00:01:33.840><c> a</c><00:01:34.000><c> student</c><00:01:34.400><c> with</c><00:01:34.560><c> multiple</c>

00:01:34.950 --> 00:01:34.960 align:start position:0%
so to elaborate a student with multiple
 

00:01:34.960 --> 00:01:37.590 align:start position:0%
so to elaborate a student with multiple
past<00:01:35.280><c> degrees</c><00:01:35.840><c> would</c><00:01:36.000><c> have</c><00:01:36.320><c> a</c><00:01:36.479><c> separate</c><00:01:36.880><c> entry</c>

00:01:37.590 --> 00:01:37.600 align:start position:0%
past degrees would have a separate entry
 

00:01:37.600 --> 00:01:39.749 align:start position:0%
past degrees would have a separate entry
for<00:01:37.840><c> each</c><00:01:38.079><c> degree</c><00:01:38.960><c> and</c><00:01:39.040><c> then</c><00:01:39.200><c> there</c><00:01:39.360><c> are</c><00:01:39.439><c> over</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
for each degree and then there are over
 

00:01:39.759 --> 00:01:40.710 align:start position:0%
for each degree and then there are over
5<00:01:40.240><c> 000</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
5 000
 

00:01:40.720 --> 00:01:42.630 align:start position:0%
5 000
graduation<00:01:41.280><c> results</c><00:01:41.680><c> which</c><00:01:41.920><c> is</c><00:01:42.079><c> basically</c>

00:01:42.630 --> 00:01:42.640 align:start position:0%
graduation results which is basically
 

00:01:42.640 --> 00:01:44.310 align:start position:0%
graduation results which is basically
the<00:01:42.960><c> gpa</c><00:01:43.439><c> that</c><00:01:43.600><c> they</c><00:01:43.840><c> got</c>

00:01:44.310 --> 00:01:44.320 align:start position:0%
the gpa that they got
 

00:01:44.320 --> 00:01:47.350 align:start position:0%
the gpa that they got
when<00:01:44.560><c> graduating</c><00:01:45.840><c> and</c><00:01:46.240><c> on</c><00:01:46.399><c> the</c><00:01:46.479><c> left</c><00:01:47.040><c> we</c><00:01:47.200><c> see</c>

00:01:47.350 --> 00:01:47.360 align:start position:0%
when graduating and on the left we see
 

00:01:47.360 --> 00:01:49.270 align:start position:0%
when graduating and on the left we see
the<00:01:47.600><c> distribution</c><00:01:48.240><c> of</c><00:01:48.399><c> students</c><00:01:48.880><c> across</c>

00:01:49.270 --> 00:01:49.280 align:start position:0%
the distribution of students across
 

00:01:49.280 --> 00:01:51.190 align:start position:0%
the distribution of students across
enrollment<00:01:49.680><c> years</c><00:01:50.000><c> spanning</c><00:01:50.320><c> from</c><00:01:50.560><c> 2000</c><00:01:51.040><c> to</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
enrollment years spanning from 2000 to
 

00:01:51.200 --> 00:01:52.469 align:start position:0%
enrollment years spanning from 2000 to
2018<00:01:52.240><c> and</c>

00:01:52.469 --> 00:01:52.479 align:start position:0%
2018 and
 

00:01:52.479 --> 00:01:55.350 align:start position:0%
2018 and
below<00:01:52.799><c> that</c><00:01:53.200><c> we</c><00:01:53.439><c> see</c><00:01:53.680><c> the</c><00:01:53.840><c> gpa</c><00:01:54.399><c> distribution</c>

00:01:55.350 --> 00:01:55.360 align:start position:0%
below that we see the gpa distribution
 

00:01:55.360 --> 00:01:57.590 align:start position:0%
below that we see the gpa distribution
with<00:01:55.600><c> most</c><00:01:55.920><c> around</c><00:01:56.320><c> three</c><00:01:56.640><c> to</c><00:01:56.799><c> four</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
with most around three to four
 

00:01:57.600 --> 00:02:01.270 align:start position:0%
with most around three to four
and<00:01:57.680><c> then</c><00:01:58.079><c> a</c><00:01:58.240><c> spike</c><00:01:58.640><c> at</c><00:01:58.880><c> zero</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
and then a spike at zero
 

00:02:01.280 --> 00:02:03.510 align:start position:0%
and then a spike at zero
as<00:02:01.520><c> we</c><00:02:01.680><c> see</c><00:02:01.840><c> in</c><00:02:01.920><c> the</c><00:02:02.079><c> real</c><00:02:02.320><c> world</c><00:02:03.119><c> data</c><00:02:03.439><c> is</c>

00:02:03.510 --> 00:02:03.520 align:start position:0%
as we see in the real world data is
 

00:02:03.520 --> 00:02:05.429 align:start position:0%
as we see in the real world data is
dirty<00:02:04.000><c> and</c><00:02:04.240><c> often</c><00:02:04.560><c> incomplete</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
dirty and often incomplete
 

00:02:05.439 --> 00:02:07.670 align:start position:0%
dirty and often incomplete
so<00:02:05.680><c> a</c><00:02:05.759><c> big</c><00:02:06.000><c> chunk</c><00:02:06.240><c> of</c><00:02:06.320><c> my</c><00:02:06.479><c> work</c><00:02:06.799><c> was</c><00:02:07.040><c> compiling</c>

00:02:07.670 --> 00:02:07.680 align:start position:0%
so a big chunk of my work was compiling
 

00:02:07.680 --> 00:02:09.029 align:start position:0%
so a big chunk of my work was compiling
a<00:02:07.920><c> usable</c><00:02:08.319><c> data</c><00:02:08.640><c> set</c>

00:02:09.029 --> 00:02:09.039 align:start position:0%
a usable data set
 

00:02:09.039 --> 00:02:10.869 align:start position:0%
a usable data set
and<00:02:09.200><c> developing</c><00:02:09.759><c> an</c><00:02:09.920><c> approach</c><00:02:10.479><c> that</c><00:02:10.640><c> would</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
and developing an approach that would
 

00:02:10.879 --> 00:02:13.030 align:start position:0%
and developing an approach that would
give<00:02:11.120><c> us</c><00:02:11.280><c> useful</c><00:02:11.760><c> insights</c><00:02:12.160><c> in</c><00:02:12.319><c> spite</c><00:02:12.640><c> of</c><00:02:12.720><c> this</c>

00:02:13.030 --> 00:02:13.040 align:start position:0%
give us useful insights in spite of this
 

00:02:13.040 --> 00:02:16.470 align:start position:0%
give us useful insights in spite of this
imperfect<00:02:13.520><c> data</c><00:02:14.720><c> so</c><00:02:15.200><c> the</c><00:02:15.440><c> first</c><00:02:15.920><c> challenge</c><00:02:16.319><c> i</c>

00:02:16.470 --> 00:02:16.480 align:start position:0%
imperfect data so the first challenge i
 

00:02:16.480 --> 00:02:18.070 align:start position:0%
imperfect data so the first challenge i
encountered<00:02:16.959><c> was</c><00:02:17.200><c> unexpected</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
encountered was unexpected
 

00:02:18.080 --> 00:02:20.070 align:start position:0%
encountered was unexpected
missing<00:02:18.400><c> data</c><00:02:19.200><c> there</c><00:02:19.360><c> is</c><00:02:19.520><c> an</c><00:02:19.680><c> uneven</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
missing data there is an uneven
 

00:02:20.080 --> 00:02:22.309 align:start position:0%
missing data there is an uneven
distribution<00:02:20.959><c> of</c><00:02:21.200><c> student</c><00:02:21.599><c> enrollments</c><00:02:22.160><c> from</c>

00:02:22.309 --> 00:02:22.319 align:start position:0%
distribution of student enrollments from
 

00:02:22.319 --> 00:02:23.350 align:start position:0%
distribution of student enrollments from
year<00:02:22.560><c> to</c><00:02:22.720><c> year</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
year to year
 

00:02:23.360 --> 00:02:26.390 align:start position:0%
year to year
missing<00:02:23.760><c> ids</c><00:02:24.239><c> and</c><00:02:24.480><c> other</c><00:02:24.720><c> fields</c><00:02:25.760><c> so</c><00:02:26.080><c> seeing</c>

00:02:26.390 --> 00:02:26.400 align:start position:0%
missing ids and other fields so seeing
 

00:02:26.400 --> 00:02:27.030 align:start position:0%
missing ids and other fields so seeing
this

00:02:27.030 --> 00:02:27.040 align:start position:0%
this
 

00:02:27.040 --> 00:02:29.030 align:start position:0%
this
we<00:02:27.280><c> investigated</c><00:02:27.840><c> the</c><00:02:28.000><c> data</c><00:02:28.239><c> source</c><00:02:28.879><c> and</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
we investigated the data source and
 

00:02:29.040 --> 00:02:30.630 align:start position:0%
we investigated the data source and
discovered<00:02:29.520><c> that</c><00:02:29.599><c> the</c><00:02:29.840><c> admission</c><00:02:30.239><c> system</c>

00:02:30.630 --> 00:02:30.640 align:start position:0%
discovered that the admission system
 

00:02:30.640 --> 00:02:31.190 align:start position:0%
discovered that the admission system
went<00:02:30.800><c> through</c>

00:02:31.190 --> 00:02:31.200 align:start position:0%
went through
 

00:02:31.200 --> 00:02:33.589 align:start position:0%
went through
several<00:02:31.840><c> changes</c><00:02:32.239><c> over</c><00:02:32.400><c> the</c><00:02:32.480><c> past</c><00:02:32.800><c> decade</c><00:02:33.440><c> and</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
several changes over the past decade and
 

00:02:33.599 --> 00:02:35.430 align:start position:0%
several changes over the past decade and
the<00:02:33.760><c> information</c><00:02:34.319><c> was</c><00:02:34.480><c> not</c><00:02:34.720><c> all</c><00:02:34.959><c> properly</c>

00:02:35.430 --> 00:02:35.440 align:start position:0%
the information was not all properly
 

00:02:35.440 --> 00:02:36.630 align:start position:0%
the information was not all properly
transferred

00:02:36.630 --> 00:02:36.640 align:start position:0%
transferred
 

00:02:36.640 --> 00:02:38.150 align:start position:0%
transferred
understanding<00:02:37.200><c> the</c><00:02:37.280><c> root</c><00:02:37.519><c> of</c><00:02:37.599><c> the</c><00:02:37.760><c> issue</c><00:02:38.000><c> we</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
understanding the root of the issue we
 

00:02:38.160 --> 00:02:40.390 align:start position:0%
understanding the root of the issue we
were<00:02:38.319><c> then</c><00:02:38.560><c> able</c><00:02:38.800><c> to</c><00:02:38.879><c> retrieve</c><00:02:39.360><c> the</c><00:02:39.440><c> missing</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
were then able to retrieve the missing
 

00:02:40.400 --> 00:02:43.350 align:start position:0%
were then able to retrieve the missing
data<00:02:40.879><c> and</c><00:02:41.040><c> then</c><00:02:41.360><c> compile</c><00:02:41.840><c> a</c><00:02:42.000><c> much</c><00:02:42.239><c> more</c><00:02:42.560><c> useful</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
data and then compile a much more useful
 

00:02:43.360 --> 00:02:45.030 align:start position:0%
data and then compile a much more useful
data<00:02:43.599><c> set</c>

00:02:45.030 --> 00:02:45.040 align:start position:0%
data set
 

00:02:45.040 --> 00:02:46.710 align:start position:0%
data set
and<00:02:45.120><c> then</c><00:02:45.280><c> the</c><00:02:45.440><c> second</c><00:02:45.760><c> challenge</c><00:02:46.319><c> i</c><00:02:46.480><c> had</c><00:02:46.640><c> to</c>

00:02:46.710 --> 00:02:46.720 align:start position:0%
and then the second challenge i had to
 

00:02:46.720 --> 00:02:48.949 align:start position:0%
and then the second challenge i had to
deal<00:02:46.959><c> with</c><00:02:47.200><c> was</c><00:02:47.599><c> incomparable</c><00:02:48.319><c> data</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
deal with was incomparable data
 

00:02:48.959 --> 00:02:51.430 align:start position:0%
deal with was incomparable data
so<00:02:49.120><c> some</c><00:02:49.440><c> fields</c><00:02:49.920><c> were</c><00:02:50.239><c> user</c><00:02:50.640><c> inputs</c><00:02:51.040><c> so</c><00:02:51.360><c> we</c>

00:02:51.430 --> 00:02:51.440 align:start position:0%
so some fields were user inputs so we
 

00:02:51.440 --> 00:02:52.790 align:start position:0%
so some fields were user inputs so we
had<00:02:51.599><c> to</c><00:02:51.680><c> standardize</c><00:02:52.239><c> spellings</c>

00:02:52.790 --> 00:02:52.800 align:start position:0%
had to standardize spellings
 

00:02:52.800 --> 00:02:55.030 align:start position:0%
had to standardize spellings
abbreviations<00:02:53.599><c> typos</c><00:02:54.080><c> of</c><00:02:54.160><c> names</c>

00:02:55.030 --> 00:02:55.040 align:start position:0%
abbreviations typos of names
 

00:02:55.040 --> 00:02:58.070 align:start position:0%
abbreviations typos of names
uh<00:02:56.160><c> also</c><00:02:56.480><c> a</c><00:02:56.560><c> big</c><00:02:56.800><c> issue</c><00:02:57.040><c> was</c><00:02:57.200><c> that</c><00:02:57.440><c> schools</c><00:02:57.840><c> use</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
uh also a big issue was that schools use
 

00:02:58.080 --> 00:02:59.270 align:start position:0%
uh also a big issue was that schools use
different<00:02:58.319><c> grading</c><00:02:58.720><c> skills</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
different grading skills
 

00:02:59.280 --> 00:03:01.350 align:start position:0%
different grading skills
some<00:02:59.680><c> have</c><00:02:59.840><c> gpas</c><00:03:00.239><c> out</c><00:03:00.400><c> of</c><00:03:00.480><c> four</c><00:03:00.800><c> some</c><00:03:01.040><c> out</c><00:03:01.200><c> of</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
some have gpas out of four some out of
 

00:03:01.360 --> 00:03:03.430 align:start position:0%
some have gpas out of four some out of
five<00:03:01.920><c> some</c><00:03:02.239><c> percents</c><00:03:02.800><c> honors</c>

00:03:03.430 --> 00:03:03.440 align:start position:0%
five some percents honors
 

00:03:03.440 --> 00:03:05.990 align:start position:0%
five some percents honors
letter<00:03:03.680><c> grades</c><00:03:04.480><c> uh</c><00:03:04.720><c> so</c><00:03:04.879><c> i</c><00:03:05.040><c> had</c><00:03:05.200><c> to</c><00:03:05.519><c> create</c><00:03:05.840><c> a</c>

00:03:05.990 --> 00:03:06.000 align:start position:0%
letter grades uh so i had to create a
 

00:03:06.000 --> 00:03:07.270 align:start position:0%
letter grades uh so i had to create a
normalized<00:03:06.879><c> uh</c>

00:03:07.270 --> 00:03:07.280 align:start position:0%
normalized uh
 

00:03:07.280 --> 00:03:09.190 align:start position:0%
normalized uh
system<00:03:08.159><c> where</c><00:03:08.400><c> everything</c><00:03:08.720><c> would</c><00:03:08.879><c> be</c><00:03:09.040><c> on</c><00:03:09.120><c> the</c>

00:03:09.190 --> 00:03:09.200 align:start position:0%
system where everything would be on the
 

00:03:09.200 --> 00:03:10.790 align:start position:0%
system where everything would be on the
same<00:03:09.440><c> zero</c><00:03:09.760><c> to</c><00:03:09.840><c> one</c><00:03:10.080><c> scale</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
same zero to one scale
 

00:03:10.800 --> 00:03:13.589 align:start position:0%
same zero to one scale
to<00:03:10.879><c> allow</c><00:03:11.200><c> me</c><00:03:11.360><c> to</c><00:03:11.440><c> compare</c><00:03:11.840><c> them</c><00:03:12.000><c> directly</c><00:03:13.519><c> and</c>

00:03:13.589 --> 00:03:13.599 align:start position:0%
to allow me to compare them directly and
 

00:03:13.599 --> 00:03:15.190 align:start position:0%
to allow me to compare them directly and
then<00:03:13.840><c> third</c><00:03:14.159><c> challenge</c><00:03:14.640><c> was</c>

00:03:15.190 --> 00:03:15.200 align:start position:0%
then third challenge was
 

00:03:15.200 --> 00:03:18.390 align:start position:0%
then third challenge was
just<00:03:15.680><c> missing</c><00:03:16.080><c> information</c><00:03:16.560><c> altogether</c><00:03:17.599><c> so</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
just missing information altogether so
 

00:03:18.400 --> 00:03:20.229 align:start position:0%
just missing information altogether so
the<00:03:18.480><c> admissions</c><00:03:18.959><c> data</c><00:03:19.280><c> doesn't</c><00:03:19.599><c> contain</c>

00:03:20.229 --> 00:03:20.239 align:start position:0%
the admissions data doesn't contain
 

00:03:20.239 --> 00:03:21.670 align:start position:0%
the admissions data doesn't contain
school<00:03:20.560><c> rankings</c><00:03:21.200><c> which</c>

00:03:21.670 --> 00:03:21.680 align:start position:0%
school rankings which
 

00:03:21.680 --> 00:03:23.670 align:start position:0%
school rankings which
we<00:03:21.840><c> would</c><00:03:22.080><c> believe</c><00:03:22.560><c> is</c><00:03:22.720><c> an</c><00:03:22.879><c> important</c><00:03:23.280><c> signal</c>

00:03:23.670 --> 00:03:23.680 align:start position:0%
we would believe is an important signal
 

00:03:23.680 --> 00:03:25.190 align:start position:0%
we would believe is an important signal
of<00:03:23.840><c> a</c><00:03:23.920><c> student's</c><00:03:24.239><c> preparedness</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
of a student's preparedness
 

00:03:25.200 --> 00:03:27.270 align:start position:0%
of a student's preparedness
so<00:03:25.360><c> to</c><00:03:25.519><c> deal</c><00:03:25.760><c> with</c><00:03:25.920><c> this</c><00:03:26.159><c> i</c><00:03:26.400><c> augmented</c><00:03:27.200><c> a</c>

00:03:27.270 --> 00:03:27.280 align:start position:0%
so to deal with this i augmented a
 

00:03:27.280 --> 00:03:28.390 align:start position:0%
so to deal with this i augmented a
selectivity<00:03:27.920><c> feature</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
selectivity feature
 

00:03:28.400 --> 00:03:31.030 align:start position:0%
selectivity feature
based<00:03:28.799><c> on</c><00:03:29.440><c> a</c><00:03:29.599><c> compilation</c><00:03:30.080><c> of</c><00:03:30.239><c> ranking</c><00:03:30.640><c> lists</c>

00:03:31.030 --> 00:03:31.040 align:start position:0%
based on a compilation of ranking lists
 

00:03:31.040 --> 00:03:32.949 align:start position:0%
based on a compilation of ranking lists
like<00:03:31.200><c> the</c><00:03:31.280><c> qs</c><00:03:31.599><c> world</c><00:03:31.840><c> rankings</c><00:03:32.319><c> and</c><00:03:32.400><c> u.s</c><00:03:32.720><c> news</c>

00:03:32.949 --> 00:03:32.959 align:start position:0%
like the qs world rankings and u.s news
 

00:03:32.959 --> 00:03:33.990 align:start position:0%
like the qs world rankings and u.s news
reports

00:03:33.990 --> 00:03:34.000 align:start position:0%
reports
 

00:03:34.000 --> 00:03:37.270 align:start position:0%
reports
also<00:03:35.120><c> are</c><00:03:35.280><c> missing</c><00:03:35.760><c> english</c><00:03:36.159><c> test</c><00:03:36.400><c> scores</c><00:03:36.959><c> uh</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
also are missing english test scores uh
 

00:03:37.280 --> 00:03:37.750 align:start position:0%
also are missing english test scores uh
because

00:03:37.750 --> 00:03:37.760 align:start position:0%
because
 

00:03:37.760 --> 00:03:39.910 align:start position:0%
because
native<00:03:38.159><c> speakers</c><00:03:38.720><c> don't</c><00:03:39.280><c> have</c><00:03:39.440><c> to</c><00:03:39.599><c> take</c><00:03:39.840><c> a</c>

00:03:39.910 --> 00:03:39.920 align:start position:0%
native speakers don't have to take a
 

00:03:39.920 --> 00:03:41.830 align:start position:0%
native speakers don't have to take a
test<00:03:40.640><c> and</c><00:03:40.879><c> for</c><00:03:41.120><c> students</c><00:03:41.440><c> who</c>

00:03:41.830 --> 00:03:41.840 align:start position:0%
test and for students who
 

00:03:41.840 --> 00:03:43.509 align:start position:0%
test and for students who
do<00:03:42.159><c> need</c><00:03:42.319><c> to</c><00:03:42.400><c> take</c><00:03:42.560><c> the</c><00:03:42.640><c> english</c><00:03:42.959><c> test</c><00:03:43.360><c> some</c>

00:03:43.509 --> 00:03:43.519 align:start position:0%
do need to take the english test some
 

00:03:43.519 --> 00:03:45.830 align:start position:0%
do need to take the english test some
provide<00:03:43.840><c> toefl</c><00:03:44.239><c> while</c><00:03:44.480><c> others</c><00:03:44.720><c> provide</c><00:03:45.120><c> ielts</c>

00:03:45.830 --> 00:03:45.840 align:start position:0%
provide toefl while others provide ielts
 

00:03:45.840 --> 00:03:46.949 align:start position:0%
provide toefl while others provide ielts
so<00:03:46.000><c> i</c><00:03:46.159><c> created</c><00:03:46.640><c> a</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
so i created a
 

00:03:46.959 --> 00:03:49.750 align:start position:0%
so i created a
separate<00:03:47.440><c> english</c><00:03:47.840><c> ability</c><00:03:48.480><c> feature</c><00:03:49.200><c> where</c><00:03:49.599><c> i</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
separate english ability feature where i
 

00:03:49.760 --> 00:03:51.350 align:start position:0%
separate english ability feature where i
group<00:03:50.000><c> people</c><00:03:50.319><c> based</c><00:03:50.640><c> on</c><00:03:50.720><c> the</c><00:03:50.879><c> ranges</c><00:03:51.280><c> of</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
group people based on the ranges of
 

00:03:51.360 --> 00:03:52.309 align:start position:0%
group people based on the ranges of
their<00:03:51.519><c> test</c><00:03:51.760><c> scores</c>

00:03:52.309 --> 00:03:52.319 align:start position:0%
their test scores
 

00:03:52.319 --> 00:03:54.949 align:start position:0%
their test scores
and<00:03:52.400><c> the</c><00:03:52.560><c> native</c><00:03:52.879><c> speakers</c><00:03:54.400><c> are</c><00:03:54.560><c> grouped</c><00:03:54.799><c> with</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
and the native speakers are grouped with
 

00:03:54.959 --> 00:03:56.869 align:start position:0%
and the native speakers are grouped with
the<00:03:55.040><c> top</c><00:03:55.280><c> scoring</c><00:03:55.680><c> students</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
the top scoring students
 

00:03:56.879 --> 00:03:59.910 align:start position:0%
the top scoring students
and<00:03:56.959><c> then</c><00:03:57.519><c> finally</c><00:03:58.319><c> some</c><00:03:58.799><c> like</c><00:03:59.120><c> gre</c><00:03:59.680><c> test</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
and then finally some like gre test
 

00:03:59.920 --> 00:04:00.630 align:start position:0%
and then finally some like gre test
scores

00:04:00.630 --> 00:04:00.640 align:start position:0%
scores
 

00:04:00.640 --> 00:04:02.470 align:start position:0%
scores
were<00:04:00.879><c> missing</c><00:04:01.120><c> from</c><00:04:01.280><c> the</c><00:04:01.360><c> main</c><00:04:01.599><c> data</c><00:04:01.840><c> set</c><00:04:02.319><c> the</c>

00:04:02.470 --> 00:04:02.480 align:start position:0%
were missing from the main data set the
 

00:04:02.480 --> 00:04:04.470 align:start position:0%
were missing from the main data set the
main<00:04:02.720><c> data</c><00:04:03.040><c> set</c><00:04:03.280><c> had</c><00:04:03.519><c> only</c><00:04:03.760><c> one</c><00:04:04.000><c> test</c><00:04:04.239><c> score</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
main data set had only one test score
 

00:04:04.480 --> 00:04:05.350 align:start position:0%
main data set had only one test score
per<00:04:04.640><c> student</c>

00:04:05.350 --> 00:04:05.360 align:start position:0%
per student
 

00:04:05.360 --> 00:04:07.429 align:start position:0%
per student
while<00:04:05.599><c> there's</c><00:04:05.920><c> a</c><00:04:06.000><c> supplemental</c><00:04:06.640><c> data</c><00:04:06.959><c> set</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
while there's a supplemental data set
 

00:04:07.439 --> 00:04:09.350 align:start position:0%
while there's a supplemental data set
that<00:04:07.760><c> has</c><00:04:08.159><c> all</c><00:04:08.319><c> of</c><00:04:08.480><c> the</c><00:04:08.640><c> students</c><00:04:08.879><c> submitted</c>

00:04:09.350 --> 00:04:09.360 align:start position:0%
that has all of the students submitted
 

00:04:09.360 --> 00:04:10.470 align:start position:0%
that has all of the students submitted
scores

00:04:10.470 --> 00:04:10.480 align:start position:0%
scores
 

00:04:10.480 --> 00:04:12.869 align:start position:0%
scores
there's<00:04:10.720><c> no</c><00:04:10.959><c> documentation</c><00:04:11.760><c> on</c><00:04:12.239><c> how</c><00:04:12.640><c> these</c>

00:04:12.869 --> 00:04:12.879 align:start position:0%
there's no documentation on how these
 

00:04:12.879 --> 00:04:14.789 align:start position:0%
there's no documentation on how these
scores<00:04:13.200><c> were</c><00:04:13.360><c> selected</c><00:04:13.840><c> so</c><00:04:14.000><c> i</c><00:04:14.080><c> had</c><00:04:14.239><c> to</c><00:04:14.319><c> reverse</c>

00:04:14.789 --> 00:04:14.799 align:start position:0%
scores were selected so i had to reverse
 

00:04:14.799 --> 00:04:16.390 align:start position:0%
scores were selected so i had to reverse
engineer<00:04:15.439><c> the</c><00:04:15.599><c> process</c>

00:04:16.390 --> 00:04:16.400 align:start position:0%
engineer the process
 

00:04:16.400 --> 00:04:18.310 align:start position:0%
engineer the process
to<00:04:16.560><c> figure</c><00:04:16.799><c> out</c><00:04:16.959><c> if</c><00:04:17.040><c> they</c><00:04:17.199><c> were</c><00:04:17.359><c> being</c><00:04:18.000><c> super</c>

00:04:18.310 --> 00:04:18.320 align:start position:0%
to figure out if they were being super
 

00:04:18.320 --> 00:04:22.790 align:start position:0%
to figure out if they were being super
scored<00:04:18.880><c> by</c><00:04:19.040><c> section</c><00:04:19.519><c> or</c><00:04:19.680><c> by</c><00:04:20.000><c> total</c><00:04:20.400><c> score</c>

00:04:22.790 --> 00:04:22.800 align:start position:0%
 
 

00:04:22.800 --> 00:04:25.430 align:start position:0%
 
and<00:04:23.520><c> to</c><00:04:23.840><c> address</c><00:04:24.160><c> the</c><00:04:24.320><c> goals</c><00:04:24.639><c> of</c><00:04:24.880><c> identifying</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
and to address the goals of identifying
 

00:04:25.440 --> 00:04:27.110 align:start position:0%
and to address the goals of identifying
top<00:04:25.680><c> and</c><00:04:25.759><c> bottom</c><00:04:26.000><c> performing</c><00:04:26.479><c> students</c><00:04:27.040><c> and</c>

00:04:27.110 --> 00:04:27.120 align:start position:0%
top and bottom performing students and
 

00:04:27.120 --> 00:04:29.189 align:start position:0%
top and bottom performing students and
which<00:04:27.360><c> factors</c><00:04:27.759><c> are</c><00:04:27.919><c> most</c><00:04:28.160><c> important</c>

00:04:29.189 --> 00:04:29.199 align:start position:0%
which factors are most important
 

00:04:29.199 --> 00:04:30.870 align:start position:0%
which factors are most important
i<00:04:29.440><c> decide</c><00:04:29.759><c> to</c><00:04:29.840><c> use</c><00:04:30.080><c> a</c><00:04:30.240><c> machine</c><00:04:30.560><c> learning</c>

00:04:30.870 --> 00:04:30.880 align:start position:0%
i decide to use a machine learning
 

00:04:30.880 --> 00:04:32.790 align:start position:0%
i decide to use a machine learning
approach<00:04:31.759><c> first</c><00:04:32.160><c> i</c><00:04:32.320><c> start</c><00:04:32.560><c> with</c><00:04:32.720><c> a</c>

00:04:32.790 --> 00:04:32.800 align:start position:0%
approach first i start with a
 

00:04:32.800 --> 00:04:34.150 align:start position:0%
approach first i start with a
classification<00:04:33.520><c> problem</c>

00:04:34.150 --> 00:04:34.160 align:start position:0%
classification problem
 

00:04:34.160 --> 00:04:36.550 align:start position:0%
classification problem
to<00:04:34.320><c> develop</c><00:04:34.720><c> a</c><00:04:34.800><c> model</c><00:04:35.120><c> that</c><00:04:35.280><c> can</c><00:04:36.000><c> predict</c><00:04:36.400><c> if</c>

00:04:36.550 --> 00:04:36.560 align:start position:0%
to develop a model that can predict if
 

00:04:36.560 --> 00:04:38.710 align:start position:0%
to develop a model that can predict if
an<00:04:36.720><c> applicant</c><00:04:37.199><c> can</c><00:04:37.440><c> graduate</c><00:04:37.919><c> or</c><00:04:38.160><c> not</c>

00:04:38.710 --> 00:04:38.720 align:start position:0%
an applicant can graduate or not
 

00:04:38.720 --> 00:04:40.950 align:start position:0%
an applicant can graduate or not
and<00:04:38.880><c> then</c><00:04:39.040><c> we</c><00:04:39.199><c> add</c><00:04:39.600><c> different</c><00:04:39.919><c> gpa</c><00:04:40.400><c> thresholds</c>

00:04:40.950 --> 00:04:40.960 align:start position:0%
and then we add different gpa thresholds
 

00:04:40.960 --> 00:04:42.390 align:start position:0%
and then we add different gpa thresholds
and<00:04:41.120><c> create</c><00:04:41.360><c> models</c><00:04:41.680><c> that</c><00:04:41.840><c> predict</c><00:04:42.160><c> whether</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
and create models that predict whether
 

00:04:42.400 --> 00:04:43.590 align:start position:0%
and create models that predict whether
the<00:04:42.639><c> student</c><00:04:42.880><c> will</c><00:04:43.120><c> hit</c>

00:04:43.590 --> 00:04:43.600 align:start position:0%
the student will hit
 

00:04:43.600 --> 00:04:47.189 align:start position:0%
the student will hit
a<00:04:43.680><c> gpa</c><00:04:44.160><c> above</c><00:04:44.639><c> or</c><00:04:44.800><c> below</c><00:04:45.840><c> benchmarks</c><00:04:46.400><c> like</c><00:04:46.639><c> 3.0</c>

00:04:47.189 --> 00:04:47.199 align:start position:0%
a gpa above or below benchmarks like 3.0
 

00:04:47.199 --> 00:04:48.950 align:start position:0%
a gpa above or below benchmarks like 3.0
3.3<00:04:47.840><c> etc</c>

00:04:48.950 --> 00:04:48.960 align:start position:0%
3.3 etc
 

00:04:48.960 --> 00:04:51.350 align:start position:0%
3.3 etc
and<00:04:49.120><c> then</c><00:04:49.440><c> i</c><00:04:49.600><c> evaluate</c><00:04:50.080><c> the</c><00:04:50.160><c> models</c><00:04:50.639><c> by</c><00:04:51.120><c> test</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
and then i evaluate the models by test
 

00:04:51.360 --> 00:04:52.550 align:start position:0%
and then i evaluate the models by test
set<00:04:51.680><c> accuracy</c><00:04:52.320><c> and</c>

00:04:52.550 --> 00:04:52.560 align:start position:0%
set accuracy and
 

00:04:52.560 --> 00:04:55.749 align:start position:0%
set accuracy and
f1<00:04:52.960><c> score</c><00:04:54.160><c> i</c><00:04:54.320><c> also</c><00:04:54.560><c> create</c><00:04:54.880><c> regression</c><00:04:55.360><c> models</c>

00:04:55.749 --> 00:04:55.759 align:start position:0%
f1 score i also create regression models
 

00:04:55.759 --> 00:04:55.990 align:start position:0%
f1 score i also create regression models
to

00:04:55.990 --> 00:04:56.000 align:start position:0%
to
 

00:04:56.000 --> 00:04:58.710 align:start position:0%
to
predict<00:04:56.560><c> the</c><00:04:56.800><c> actual</c><00:04:57.199><c> gpa</c><00:04:57.919><c> and</c><00:04:58.080><c> use</c><00:04:58.320><c> multiple</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
predict the actual gpa and use multiple
 

00:04:58.720 --> 00:05:00.550 align:start position:0%
predict the actual gpa and use multiple
types<00:04:58.960><c> of</c><00:04:59.040><c> evaluation</c><00:04:59.919><c> criteria</c>

00:05:00.550 --> 00:05:00.560 align:start position:0%
types of evaluation criteria
 

00:05:00.560 --> 00:05:03.590 align:start position:0%
types of evaluation criteria
on<00:05:00.639><c> the</c><00:05:00.800><c> predicted</c><00:05:01.280><c> gpa</c><00:05:01.919><c> and</c><00:05:02.240><c> percentile</c><00:05:02.800><c> rank</c>

00:05:03.590 --> 00:05:03.600 align:start position:0%
on the predicted gpa and percentile rank
 

00:05:03.600 --> 00:05:06.390 align:start position:0%
on the predicted gpa and percentile rank
so<00:05:04.000><c> percentile</c><00:05:04.560><c> rank</c><00:05:04.960><c> gives</c><00:05:05.280><c> us</c><00:05:05.440><c> an</c><00:05:05.680><c> idea</c><00:05:06.160><c> of</c>

00:05:06.390 --> 00:05:06.400 align:start position:0%
so percentile rank gives us an idea of
 

00:05:06.400 --> 00:05:08.230 align:start position:0%
so percentile rank gives us an idea of
how<00:05:06.720><c> well</c><00:05:07.039><c> the</c><00:05:07.199><c> model</c><00:05:07.520><c> can</c><00:05:07.680><c> distinguish</c>

00:05:08.230 --> 00:05:08.240 align:start position:0%
how well the model can distinguish
 

00:05:08.240 --> 00:05:09.590 align:start position:0%
how well the model can distinguish
between<00:05:08.560><c> relatively</c><00:05:09.039><c> stronger</c><00:05:09.440><c> and</c>

00:05:09.590 --> 00:05:09.600 align:start position:0%
between relatively stronger and
 

00:05:09.600 --> 00:05:11.350 align:start position:0%
between relatively stronger and
relatively<00:05:10.160><c> weaker</c>

00:05:11.350 --> 00:05:11.360 align:start position:0%
relatively weaker
 

00:05:11.360 --> 00:05:13.909 align:start position:0%
relatively weaker
candidates<00:05:12.639><c> we</c><00:05:12.800><c> evaluate</c><00:05:13.280><c> these</c><00:05:13.520><c> numbers</c>

00:05:13.909 --> 00:05:13.919 align:start position:0%
candidates we evaluate these numbers
 

00:05:13.919 --> 00:05:14.629 align:start position:0%
candidates we evaluate these numbers
based<00:05:14.240><c> on</c>

00:05:14.629 --> 00:05:14.639 align:start position:0%
based on
 

00:05:14.639 --> 00:05:17.350 align:start position:0%
based on
mean<00:05:14.960><c> and</c><00:05:15.199><c> median</c><00:05:15.680><c> absolute</c><00:05:16.080><c> percent</c><00:05:16.479><c> error</c>

00:05:17.350 --> 00:05:17.360 align:start position:0%
mean and median absolute percent error
 

00:05:17.360 --> 00:05:19.110 align:start position:0%
mean and median absolute percent error
and<00:05:17.440><c> for</c><00:05:17.680><c> each</c><00:05:17.919><c> student</c><00:05:18.400><c> i</c><00:05:18.560><c> place</c><00:05:18.880><c> them</c>

00:05:19.110 --> 00:05:19.120 align:start position:0%
and for each student i place them
 

00:05:19.120 --> 00:05:22.790 align:start position:0%
and for each student i place them
in<00:05:19.199><c> both</c><00:05:19.919><c> predicted</c><00:05:21.280><c> and</c><00:05:21.600><c> actual</c><00:05:22.000><c> gpas</c><00:05:22.639><c> and</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
in both predicted and actual gpas and
 

00:05:22.800 --> 00:05:24.150 align:start position:0%
in both predicted and actual gpas and
percentile<00:05:23.360><c> ranks</c><00:05:23.759><c> in</c>

00:05:24.150 --> 00:05:24.160 align:start position:0%
percentile ranks in
 

00:05:24.160 --> 00:05:26.950 align:start position:0%
percentile ranks in
buckets<00:05:24.960><c> based</c><00:05:25.280><c> on</c><00:05:25.440><c> what</c><00:05:25.680><c> range</c><00:05:26.160><c> they</c><00:05:26.479><c> lied</c><00:05:26.800><c> in</c>

00:05:26.950 --> 00:05:26.960 align:start position:0%
buckets based on what range they lied in
 

00:05:26.960 --> 00:05:28.550 align:start position:0%
buckets based on what range they lied in
for<00:05:27.120><c> instance</c><00:05:27.440><c> there's</c><00:05:27.600><c> a</c><00:05:27.680><c> low</c><00:05:27.840><c> bucket</c><00:05:28.240><c> medium</c>

00:05:28.550 --> 00:05:28.560 align:start position:0%
for instance there's a low bucket medium
 

00:05:28.560 --> 00:05:30.390 align:start position:0%
for instance there's a low bucket medium
bucket<00:05:28.960><c> and</c><00:05:29.120><c> high</c><00:05:29.360><c> bucket</c>

00:05:30.390 --> 00:05:30.400 align:start position:0%
bucket and high bucket
 

00:05:30.400 --> 00:05:32.870 align:start position:0%
bucket and high bucket
uh<00:05:30.800><c> so</c><00:05:31.120><c> i</c><00:05:31.280><c> look</c><00:05:31.520><c> at</c><00:05:31.600><c> the</c><00:05:31.680><c> percent</c><00:05:32.160><c> of</c><00:05:32.240><c> records</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
uh so i look at the percent of records
 

00:05:32.880 --> 00:05:33.909 align:start position:0%
uh so i look at the percent of records
that<00:05:33.120><c> were</c><00:05:33.280><c> correctly</c>

00:05:33.909 --> 00:05:33.919 align:start position:0%
that were correctly
 

00:05:33.919 --> 00:05:37.029 align:start position:0%
that were correctly
put<00:05:34.320><c> in</c><00:05:34.479><c> the</c><00:05:34.720><c> same</c><00:05:35.120><c> bucket</c><00:05:35.680><c> as</c><00:05:35.919><c> the</c><00:05:36.320><c> actual</c>

00:05:37.029 --> 00:05:37.039 align:start position:0%
put in the same bucket as the actual
 

00:05:37.039 --> 00:05:40.390 align:start position:0%
put in the same bucket as the actual
records<00:05:39.600><c> and</c><00:05:39.840><c> before</c>

00:05:40.390 --> 00:05:40.400 align:start position:0%
records and before
 

00:05:40.400 --> 00:05:42.550 align:start position:0%
records and before
running<00:05:40.880><c> the</c><00:05:41.120><c> actual</c><00:05:41.440><c> models</c><00:05:42.160><c> we</c><00:05:42.320><c> have</c><00:05:42.400><c> to</c>

00:05:42.550 --> 00:05:42.560 align:start position:0%
running the actual models we have to
 

00:05:42.560 --> 00:05:44.390 align:start position:0%
running the actual models we have to
address<00:05:42.880><c> the</c><00:05:43.039><c> inherent</c><00:05:43.520><c> class</c><00:05:43.919><c> imbalance</c>

00:05:44.390 --> 00:05:44.400 align:start position:0%
address the inherent class imbalance
 

00:05:44.400 --> 00:05:44.870 align:start position:0%
address the inherent class imbalance
issue

00:05:44.870 --> 00:05:44.880 align:start position:0%
issue
 

00:05:44.880 --> 00:05:47.189 align:start position:0%
issue
of<00:05:45.039><c> the</c><00:05:45.120><c> data</c><00:05:45.440><c> set</c><00:05:46.160><c> most</c><00:05:46.479><c> students</c><00:05:46.880><c> end</c><00:05:47.120><c> up</c>

00:05:47.189 --> 00:05:47.199 align:start position:0%
of the data set most students end up
 

00:05:47.199 --> 00:05:48.469 align:start position:0%
of the data set most students end up
graduating

00:05:48.469 --> 00:05:48.479 align:start position:0%
graduating
 

00:05:48.479 --> 00:05:51.029 align:start position:0%
graduating
so<00:05:49.039><c> i</c><00:05:49.199><c> resolve</c><00:05:49.520><c> this</c><00:05:49.759><c> in</c><00:05:49.840><c> two</c><00:05:50.000><c> different</c><00:05:50.320><c> ways</c>

00:05:51.029 --> 00:05:51.039 align:start position:0%
so i resolve this in two different ways
 

00:05:51.039 --> 00:05:51.510 align:start position:0%
so i resolve this in two different ways
one

00:05:51.510 --> 00:05:51.520 align:start position:0%
one
 

00:05:51.520 --> 00:05:53.350 align:start position:0%
one
by<00:05:51.759><c> using</c><00:05:52.080><c> models</c><00:05:52.479><c> that</c><00:05:52.720><c> re-weight</c><00:05:53.120><c> the</c>

00:05:53.350 --> 00:05:53.360 align:start position:0%
by using models that re-weight the
 

00:05:53.360 --> 00:05:56.150 align:start position:0%
by using models that re-weight the
training<00:05:54.000><c> data</c><00:05:54.479><c> to</c><00:05:54.639><c> get</c><00:05:54.960><c> class</c><00:05:55.360><c> balance</c>

00:05:56.150 --> 00:05:56.160 align:start position:0%
training data to get class balance
 

00:05:56.160 --> 00:05:58.550 align:start position:0%
training data to get class balance
and<00:05:56.479><c> two</c><00:05:56.960><c> by</c><00:05:57.199><c> up</c><00:05:57.360><c> sampling</c><00:05:57.759><c> the</c><00:05:57.840><c> training</c><00:05:58.160><c> data</c>

00:05:58.550 --> 00:05:58.560 align:start position:0%
and two by up sampling the training data
 

00:05:58.560 --> 00:05:59.670 align:start position:0%
and two by up sampling the training data
to<00:05:58.639><c> get</c><00:05:58.880><c> a</c><00:05:58.960><c> positive</c>

00:05:59.670 --> 00:05:59.680 align:start position:0%
to get a positive
 

00:05:59.680 --> 00:06:03.029 align:start position:0%
to get a positive
negative<00:06:00.240><c> class</c><00:06:00.800><c> 50</c><00:06:01.199><c> 50</c><00:06:01.600><c> ratio</c>

00:06:03.029 --> 00:06:03.039 align:start position:0%
negative class 50 50 ratio
 

00:06:03.039 --> 00:06:04.629 align:start position:0%
negative class 50 50 ratio
so<00:06:03.199><c> i</c><00:06:03.280><c> try</c><00:06:03.680><c> several</c><00:06:04.000><c> different</c><00:06:04.240><c> models</c>

00:06:04.629 --> 00:06:04.639 align:start position:0%
so i try several different models
 

00:06:04.639 --> 00:06:06.070 align:start position:0%
so i try several different models
including<00:06:05.039><c> logistic</c><00:06:05.440><c> regression</c>

00:06:06.070 --> 00:06:06.080 align:start position:0%
including logistic regression
 

00:06:06.080 --> 00:06:08.230 align:start position:0%
including logistic regression
svm<00:06:06.639><c> decision</c><00:06:07.039><c> trees</c><00:06:07.360><c> k</c><00:06:07.520><c> nearest</c><00:06:07.840><c> neighbors</c>

00:06:08.230 --> 00:06:08.240 align:start position:0%
svm decision trees k nearest neighbors
 

00:06:08.240 --> 00:06:10.070 align:start position:0%
svm decision trees k nearest neighbors
and<00:06:08.319><c> linear</c><00:06:08.720><c> regression</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
and linear regression
 

00:06:10.080 --> 00:06:12.150 align:start position:0%
and linear regression
but<00:06:10.319><c> in</c><00:06:10.560><c> both</c><00:06:11.120><c> classification</c><00:06:12.000><c> and</c>

00:06:12.150 --> 00:06:12.160 align:start position:0%
but in both classification and
 

00:06:12.160 --> 00:06:13.270 align:start position:0%
but in both classification and
regression

00:06:13.270 --> 00:06:13.280 align:start position:0%
regression
 

00:06:13.280 --> 00:06:15.350 align:start position:0%
regression
uh<00:06:13.680><c> we</c><00:06:13.840><c> select</c><00:06:14.160><c> the</c><00:06:14.400><c> random</c><00:06:14.720><c> forest</c><00:06:15.120><c> as</c><00:06:15.280><c> our</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
uh we select the random forest as our
 

00:06:15.360 --> 00:06:16.309 align:start position:0%
uh we select the random forest as our
best<00:06:15.680><c> model</c>

00:06:16.309 --> 00:06:16.319 align:start position:0%
best model
 

00:06:16.319 --> 00:06:18.150 align:start position:0%
best model
for<00:06:16.479><c> its</c><00:06:16.639><c> relative</c><00:06:17.120><c> accuracy</c><00:06:17.759><c> and</c><00:06:17.919><c> its</c>

00:06:18.150 --> 00:06:18.160 align:start position:0%
for its relative accuracy and its
 

00:06:18.160 --> 00:06:20.390 align:start position:0%
for its relative accuracy and its
features<00:06:18.639><c> importances</c><00:06:19.199><c> vector</c><00:06:19.840><c> which</c><00:06:20.080><c> show</c>

00:06:20.390 --> 00:06:20.400 align:start position:0%
features importances vector which show
 

00:06:20.400 --> 00:06:21.909 align:start position:0%
features importances vector which show
us<00:06:20.560><c> which</c><00:06:20.880><c> features</c><00:06:21.199><c> were</c><00:06:21.440><c> the</c><00:06:21.600><c> most</c>

00:06:21.909 --> 00:06:21.919 align:start position:0%
us which features were the most
 

00:06:21.919 --> 00:06:23.510 align:start position:0%
us which features were the most
important<00:06:22.240><c> to</c><00:06:22.400><c> that</c><00:06:22.560><c> model</c>

00:06:23.510 --> 00:06:23.520 align:start position:0%
important to that model
 

00:06:23.520 --> 00:06:25.909 align:start position:0%
important to that model
and<00:06:24.000><c> then</c><00:06:24.240><c> we</c><00:06:24.400><c> can</c><00:06:24.639><c> infer</c><00:06:25.120><c> that</c><00:06:25.280><c> these</c><00:06:25.520><c> are</c><00:06:25.759><c> the</c>

00:06:25.909 --> 00:06:25.919 align:start position:0%
and then we can infer that these are the
 

00:06:25.919 --> 00:06:28.309 align:start position:0%
and then we can infer that these are the
most<00:06:26.160><c> influential</c><00:06:26.800><c> in</c><00:06:26.960><c> predicting</c>

00:06:28.309 --> 00:06:28.319 align:start position:0%
most influential in predicting
 

00:06:28.319 --> 00:06:32.790 align:start position:0%
most influential in predicting
graduation<00:06:29.039><c> and</c><00:06:29.199><c> other</c><00:06:29.840><c> gpa</c><00:06:30.240><c> benchmarks</c>

00:06:32.790 --> 00:06:32.800 align:start position:0%
graduation and other gpa benchmarks
 

00:06:32.800 --> 00:06:35.110 align:start position:0%
graduation and other gpa benchmarks
so<00:06:33.120><c> for</c><00:06:33.360><c> predicting</c><00:06:33.840><c> graduation</c><00:06:34.560><c> we</c><00:06:34.720><c> see</c><00:06:34.960><c> the</c>

00:06:35.110 --> 00:06:35.120 align:start position:0%
so for predicting graduation we see the
 

00:06:35.120 --> 00:06:36.870 align:start position:0%
so for predicting graduation we see the
features<00:06:35.520><c> with</c><00:06:35.680><c> the</c><00:06:35.840><c> highest</c><00:06:36.240><c> importances</c>

00:06:36.870 --> 00:06:36.880 align:start position:0%
features with the highest importances
 

00:06:36.880 --> 00:06:38.070 align:start position:0%
features with the highest importances
are<00:06:37.120><c> average</c>

00:06:38.070 --> 00:06:38.080 align:start position:0%
are average
 

00:06:38.080 --> 00:06:40.230 align:start position:0%
are average
10<00:06:38.400><c> gpa</c><00:06:38.880><c> of</c><00:06:39.039><c> students</c><00:06:39.440><c> from</c><00:06:39.600><c> the</c><00:06:39.680><c> same</c><00:06:39.919><c> prior</c>

00:06:40.230 --> 00:06:40.240 align:start position:0%
10 gpa of students from the same prior
 

00:06:40.240 --> 00:06:41.189 align:start position:0%
10 gpa of students from the same prior
institution

00:06:41.189 --> 00:06:41.199 align:start position:0%
institution
 

00:06:41.199 --> 00:06:43.830 align:start position:0%
institution
gre<00:06:41.840><c> writing</c><00:06:42.080><c> percentile</c><00:06:43.039><c> ratio</c><00:06:43.440><c> of</c><00:06:43.520><c> students</c>

00:06:43.830 --> 00:06:43.840 align:start position:0%
gre writing percentile ratio of students
 

00:06:43.840 --> 00:06:45.749 align:start position:0%
gre writing percentile ratio of students
gpa<00:06:44.319><c> at</c><00:06:44.400><c> the</c><00:06:44.560><c> prior</c><00:06:44.880><c> institution</c><00:06:45.440><c> to</c><00:06:45.600><c> the</c>

00:06:45.749 --> 00:06:45.759 align:start position:0%
gpa at the prior institution to the
 

00:06:45.759 --> 00:06:47.670 align:start position:0%
gpa at the prior institution to the
average<00:06:46.080><c> gpa</c><00:06:46.479><c> from</c><00:06:46.639><c> that</c><00:06:46.800><c> institution</c>

00:06:47.670 --> 00:06:47.680 align:start position:0%
average gpa from that institution
 

00:06:47.680 --> 00:06:50.550 align:start position:0%
average gpa from that institution
and<00:06:47.840><c> then</c><00:06:48.000><c> the</c><00:06:48.160><c> students</c><00:06:48.880><c> gpa</c><00:06:49.680><c> at</c><00:06:50.000><c> their</c><00:06:50.240><c> prior</c>

00:06:50.550 --> 00:06:50.560 align:start position:0%
and then the students gpa at their prior
 

00:06:50.560 --> 00:06:51.589 align:start position:0%
and then the students gpa at their prior
institution

00:06:51.589 --> 00:06:51.599 align:start position:0%
institution
 

00:06:51.599 --> 00:06:53.670 align:start position:0%
institution
most<00:06:51.840><c> surprising</c><00:06:52.720><c> is</c><00:06:52.880><c> the</c><00:06:52.960><c> fact</c><00:06:53.199><c> that</c><00:06:53.440><c> the</c>

00:06:53.670 --> 00:06:53.680 align:start position:0%
most surprising is the fact that the
 

00:06:53.680 --> 00:06:55.909 align:start position:0%
most surprising is the fact that the
prior<00:06:54.080><c> institution</c><00:06:54.800><c> selectivity</c>

00:06:55.909 --> 00:06:55.919 align:start position:0%
prior institution selectivity
 

00:06:55.919 --> 00:06:58.230 align:start position:0%
prior institution selectivity
has<00:06:56.160><c> relatively</c><00:06:56.639><c> low</c><00:06:56.880><c> importance</c><00:06:57.759><c> which</c><00:06:58.000><c> goes</c>

00:06:58.230 --> 00:06:58.240 align:start position:0%
has relatively low importance which goes
 

00:06:58.240 --> 00:06:59.510 align:start position:0%
has relatively low importance which goes
against<00:06:58.560><c> commonly</c><00:06:59.039><c> held</c>

00:06:59.510 --> 00:06:59.520 align:start position:0%
against commonly held
 

00:06:59.520 --> 00:07:02.150 align:start position:0%
against commonly held
intuition<00:07:00.560><c> we</c><00:07:00.800><c> see</c><00:07:01.360><c> all</c><00:07:01.440><c> these</c><00:07:01.680><c> trends</c>

00:07:02.150 --> 00:07:02.160 align:start position:0%
intuition we see all these trends
 

00:07:02.160 --> 00:07:03.189 align:start position:0%
intuition we see all these trends
generally<00:07:02.639><c> hold</c>

00:07:03.189 --> 00:07:03.199 align:start position:0%
generally hold
 

00:07:03.199 --> 00:07:05.430 align:start position:0%
generally hold
for<00:07:03.440><c> other</c><00:07:03.680><c> gpa</c><00:07:04.080><c> thresholds</c><00:07:04.800><c> and</c><00:07:05.199><c> in</c><00:07:05.280><c> the</c>

00:07:05.430 --> 00:07:05.440 align:start position:0%
for other gpa thresholds and in the
 

00:07:05.440 --> 00:07:06.550 align:start position:0%
for other gpa thresholds and in the
regression<00:07:05.919><c> model</c>

00:07:06.550 --> 00:07:06.560 align:start position:0%
regression model
 

00:07:06.560 --> 00:07:09.029 align:start position:0%
regression model
and<00:07:06.880><c> as</c><00:07:07.039><c> we</c><00:07:07.280><c> increase</c><00:07:07.599><c> the</c><00:07:07.759><c> gpa</c><00:07:08.240><c> benchmark</c><00:07:08.880><c> the</c>

00:07:09.029 --> 00:07:09.039 align:start position:0%
and as we increase the gpa benchmark the
 

00:07:09.039 --> 00:07:11.189 align:start position:0%
and as we increase the gpa benchmark the
ratio<00:07:09.360><c> of</c><00:07:09.440><c> the</c><00:07:09.599><c> student's</c><00:07:10.000><c> gpa</c><00:07:10.560><c> at</c><00:07:10.639><c> the</c><00:07:10.800><c> prior</c>

00:07:11.189 --> 00:07:11.199 align:start position:0%
ratio of the student's gpa at the prior
 

00:07:11.199 --> 00:07:11.990 align:start position:0%
ratio of the student's gpa at the prior
institution

00:07:11.990 --> 00:07:12.000 align:start position:0%
institution
 

00:07:12.000 --> 00:07:13.909 align:start position:0%
institution
to<00:07:12.160><c> the</c><00:07:12.319><c> average</c><00:07:12.560><c> gpa</c><00:07:12.960><c> from</c><00:07:13.120><c> that</c><00:07:13.360><c> institution</c>

00:07:13.909 --> 00:07:13.919 align:start position:0%
to the average gpa from that institution
 

00:07:13.919 --> 00:07:16.150 align:start position:0%
to the average gpa from that institution
becomes<00:07:14.639><c> more</c><00:07:15.039><c> important</c>

00:07:16.150 --> 00:07:16.160 align:start position:0%
becomes more important
 

00:07:16.160 --> 00:07:19.029 align:start position:0%
becomes more important
these<00:07:16.479><c> findings</c><00:07:16.960><c> suggest</c><00:07:17.360><c> to</c><00:07:17.599><c> us</c><00:07:17.840><c> that</c><00:07:18.080><c> gpa</c><00:07:18.720><c> is</c>

00:07:19.029 --> 00:07:19.039 align:start position:0%
these findings suggest to us that gpa is
 

00:07:19.039 --> 00:07:20.150 align:start position:0%
these findings suggest to us that gpa is
very<00:07:19.280><c> important</c>

00:07:20.150 --> 00:07:20.160 align:start position:0%
very important
 

00:07:20.160 --> 00:07:22.150 align:start position:0%
very important
and<00:07:20.479><c> when</c><00:07:20.800><c> choosing</c><00:07:21.120><c> between</c><00:07:21.520><c> a</c><00:07:21.599><c> candidate</c>

00:07:22.150 --> 00:07:22.160 align:start position:0%
and when choosing between a candidate
 

00:07:22.160 --> 00:07:23.670 align:start position:0%
and when choosing between a candidate
with<00:07:22.319><c> a</c><00:07:22.479><c> higher</c><00:07:22.880><c> gpa</c>

00:07:23.670 --> 00:07:23.680 align:start position:0%
with a higher gpa
 

00:07:23.680 --> 00:07:25.670 align:start position:0%
with a higher gpa
at<00:07:23.840><c> a</c><00:07:23.919><c> lesser</c><00:07:24.240><c> known</c><00:07:24.560><c> school</c><00:07:25.199><c> versus</c><00:07:25.599><c> a</c>

00:07:25.670 --> 00:07:25.680 align:start position:0%
at a lesser known school versus a
 

00:07:25.680 --> 00:07:26.870 align:start position:0%
at a lesser known school versus a
candidate<00:07:26.240><c> with</c><00:07:26.560><c> a</c>

00:07:26.870 --> 00:07:26.880 align:start position:0%
candidate with a
 

00:07:26.880 --> 00:07:29.589 align:start position:0%
candidate with a
lower<00:07:27.199><c> gpa</c><00:07:27.759><c> from</c><00:07:27.919><c> a</c><00:07:28.000><c> prestigious</c><00:07:28.720><c> school</c><00:07:29.440><c> we</c>

00:07:29.589 --> 00:07:29.599 align:start position:0%
lower gpa from a prestigious school we
 

00:07:29.599 --> 00:07:31.510 align:start position:0%
lower gpa from a prestigious school we
should<00:07:29.840><c> pick</c><00:07:30.000><c> the</c><00:07:30.160><c> student</c><00:07:30.560><c> with</c><00:07:30.720><c> the</c><00:07:30.960><c> higher</c>

00:07:31.510 --> 00:07:31.520 align:start position:0%
should pick the student with the higher
 

00:07:31.520 --> 00:07:34.710 align:start position:0%
should pick the student with the higher
gpa<00:07:32.800><c> now</c><00:07:33.199><c> we</c><00:07:33.520><c> also</c><00:07:33.759><c> note</c><00:07:34.000><c> that</c><00:07:34.080><c> the</c><00:07:34.240><c> accuracy</c>

00:07:34.710 --> 00:07:34.720 align:start position:0%
gpa now we also note that the accuracy
 

00:07:34.720 --> 00:07:35.909 align:start position:0%
gpa now we also note that the accuracy
in<00:07:34.800><c> the</c><00:07:34.880><c> regression</c><00:07:35.440><c> task</c>

00:07:35.909 --> 00:07:35.919 align:start position:0%
in the regression task
 

00:07:35.919 --> 00:07:38.230 align:start position:0%
in the regression task
for<00:07:36.080><c> both</c><00:07:36.400><c> bucketing</c><00:07:36.800><c> schemes</c><00:07:37.280><c> is</c><00:07:37.360><c> around</c><00:07:37.759><c> 50</c>

00:07:38.230 --> 00:07:38.240 align:start position:0%
for both bucketing schemes is around 50
 

00:07:38.240 --> 00:07:39.830 align:start position:0%
for both bucketing schemes is around 50
to<00:07:38.400><c> 55</c>

00:07:39.830 --> 00:07:39.840 align:start position:0%
to 55
 

00:07:39.840 --> 00:07:41.990 align:start position:0%
to 55
so<00:07:40.240><c> the</c><00:07:40.400><c> most</c><00:07:40.639><c> we</c><00:07:40.800><c> can</c><00:07:40.960><c> conclude</c><00:07:41.520><c> from</c><00:07:41.840><c> the</c>

00:07:41.990 --> 00:07:42.000 align:start position:0%
so the most we can conclude from the
 

00:07:42.000 --> 00:07:44.230 align:start position:0%
so the most we can conclude from the
regression<00:07:42.639><c> endeavor</c><00:07:43.120><c> is</c><00:07:43.280><c> that</c><00:07:43.840><c> while</c><00:07:44.080><c> the</c>

00:07:44.230 --> 00:07:44.240 align:start position:0%
regression endeavor is that while the
 

00:07:44.240 --> 00:07:45.990 align:start position:0%
regression endeavor is that while the
set<00:07:44.400><c> of</c><00:07:44.560><c> features</c><00:07:45.039><c> may</c><00:07:45.280><c> work</c><00:07:45.599><c> to</c>

00:07:45.990 --> 00:07:46.000 align:start position:0%
set of features may work to
 

00:07:46.000 --> 00:07:48.790 align:start position:0%
set of features may work to
predict<00:07:46.800><c> our</c><00:07:46.960><c> classification</c><00:07:47.919><c> tasks</c><00:07:48.560><c> it's</c>

00:07:48.790 --> 00:07:48.800 align:start position:0%
predict our classification tasks it's
 

00:07:48.800 --> 00:07:50.390 align:start position:0%
predict our classification tasks it's
not<00:07:48.960><c> sufficient</c><00:07:49.520><c> to</c><00:07:49.680><c> accurately</c>

00:07:50.390 --> 00:07:50.400 align:start position:0%
not sufficient to accurately
 

00:07:50.400 --> 00:07:54.150 align:start position:0%
not sufficient to accurately
predict<00:07:50.960><c> exact</c><00:07:51.199><c> student</c><00:07:51.520><c> gpas</c>

00:07:54.150 --> 00:07:54.160 align:start position:0%
 
 

00:07:54.160 --> 00:07:56.869 align:start position:0%
 
so<00:07:54.560><c> there's</c><00:07:54.879><c> a</c><00:07:54.960><c> couple</c><00:07:55.680><c> main</c><00:07:56.160><c> limitations</c><00:07:56.800><c> to</c>

00:07:56.869 --> 00:07:56.879 align:start position:0%
so there's a couple main limitations to
 

00:07:56.879 --> 00:07:58.070 align:start position:0%
so there's a couple main limitations to
this<00:07:57.199><c> study</c>

00:07:58.070 --> 00:07:58.080 align:start position:0%
this study
 

00:07:58.080 --> 00:08:00.550 align:start position:0%
this study
first<00:07:58.960><c> we</c><00:07:59.120><c> can't</c><00:07:59.440><c> distinguish</c><00:08:00.080><c> between</c>

00:08:00.550 --> 00:08:00.560 align:start position:0%
first we can't distinguish between
 

00:08:00.560 --> 00:08:01.990 align:start position:0%
first we can't distinguish between
students<00:08:00.960><c> who</c><00:08:01.120><c> couldn't</c><00:08:01.360><c> graduate</c><00:08:01.759><c> because</c>

00:08:01.990 --> 00:08:02.000 align:start position:0%
students who couldn't graduate because
 

00:08:02.000 --> 00:08:03.749 align:start position:0%
students who couldn't graduate because
of<00:08:02.160><c> extenuating</c><00:08:02.800><c> circumstances</c>

00:08:03.749 --> 00:08:03.759 align:start position:0%
of extenuating circumstances
 

00:08:03.759 --> 00:08:05.430 align:start position:0%
of extenuating circumstances
like<00:08:03.919><c> medical</c><00:08:04.319><c> reasons</c><00:08:04.720><c> family</c><00:08:05.039><c> leave</c><00:08:05.360><c> et</c>

00:08:05.430 --> 00:08:05.440 align:start position:0%
like medical reasons family leave et
 

00:08:05.440 --> 00:08:07.990 align:start position:0%
like medical reasons family leave et
cetera<00:08:06.479><c> versus</c><00:08:06.879><c> a</c><00:08:07.039><c> lack</c><00:08:07.360><c> of</c><00:08:07.520><c> academic</c>

00:08:07.990 --> 00:08:08.000 align:start position:0%
cetera versus a lack of academic
 

00:08:08.000 --> 00:08:09.589 align:start position:0%
cetera versus a lack of academic
preparedness

00:08:09.589 --> 00:08:09.599 align:start position:0%
preparedness
 

00:08:09.599 --> 00:08:12.950 align:start position:0%
preparedness
we<00:08:09.840><c> found</c><00:08:10.080><c> that</c><00:08:10.560><c> gpa</c><00:08:11.199><c> is</c><00:08:11.360><c> a</c><00:08:11.440><c> helpful</c><00:08:11.759><c> metric</c>

00:08:12.950 --> 00:08:12.960 align:start position:0%
we found that gpa is a helpful metric
 

00:08:12.960 --> 00:08:14.950 align:start position:0%
we found that gpa is a helpful metric
but<00:08:13.280><c> it</c><00:08:13.440><c> doesn't</c><00:08:13.919><c> reflect</c><00:08:14.319><c> the</c><00:08:14.400><c> full</c><00:08:14.639><c> picture</c>

00:08:14.950 --> 00:08:14.960 align:start position:0%
but it doesn't reflect the full picture
 

00:08:14.960 --> 00:08:15.990 align:start position:0%
but it doesn't reflect the full picture
of<00:08:15.120><c> a</c><00:08:15.199><c> student</c>

00:08:15.990 --> 00:08:16.000 align:start position:0%
of a student
 

00:08:16.000 --> 00:08:17.670 align:start position:0%
of a student
in<00:08:16.080><c> terms</c><00:08:16.400><c> of</c><00:08:16.560><c> curriculum</c><00:08:17.039><c> difficulty</c><00:08:17.599><c> and</c>

00:08:17.670 --> 00:08:17.680 align:start position:0%
in terms of curriculum difficulty and
 

00:08:17.680 --> 00:08:19.670 align:start position:0%
in terms of curriculum difficulty and
course<00:08:18.000><c> load</c><00:08:18.639><c> harder</c><00:08:19.039><c> classes</c>

00:08:19.670 --> 00:08:19.680 align:start position:0%
course load harder classes
 

00:08:19.680 --> 00:08:21.270 align:start position:0%
course load harder classes
and<00:08:19.759><c> heavier</c><00:08:20.080><c> workload</c><00:08:20.560><c> may</c><00:08:20.720><c> result</c><00:08:21.039><c> in</c><00:08:21.199><c> a</c>

00:08:21.270 --> 00:08:21.280 align:start position:0%
and heavier workload may result in a
 

00:08:21.280 --> 00:08:23.830 align:start position:0%
and heavier workload may result in a
lower<00:08:21.599><c> gpa</c><00:08:22.479><c> but</c><00:08:22.720><c> the</c><00:08:22.879><c> student</c><00:08:23.280><c> may</c>

00:08:23.830 --> 00:08:23.840 align:start position:0%
lower gpa but the student may
 

00:08:23.840 --> 00:08:26.710 align:start position:0%
lower gpa but the student may
be<00:08:24.000><c> more</c><00:08:24.240><c> prepared</c><00:08:24.840><c> academically</c><00:08:26.000><c> and</c><00:08:26.080><c> then</c>

00:08:26.710 --> 00:08:26.720 align:start position:0%
be more prepared academically and then
 

00:08:26.720 --> 00:08:28.550 align:start position:0%
be more prepared academically and then
school<00:08:27.039><c> ranking</c><00:08:27.360><c> data</c><00:08:27.680><c> was</c><00:08:27.919><c> very</c><00:08:28.319><c> very</c>

00:08:28.550 --> 00:08:28.560 align:start position:0%
school ranking data was very very
 

00:08:28.560 --> 00:08:31.110 align:start position:0%
school ranking data was very very
limited<00:08:29.120><c> and</c><00:08:29.440><c> had</c><00:08:29.599><c> to</c><00:08:29.759><c> be</c><00:08:29.919><c> manually</c><00:08:30.479><c> compiled</c>

00:08:31.110 --> 00:08:31.120 align:start position:0%
limited and had to be manually compiled
 

00:08:31.120 --> 00:08:32.630 align:start position:0%
limited and had to be manually compiled
from<00:08:31.360><c> several</c><00:08:31.680><c> different</c><00:08:32.000><c> sources</c>

00:08:32.630 --> 00:08:32.640 align:start position:0%
from several different sources
 

00:08:32.640 --> 00:08:35.190 align:start position:0%
from several different sources
and<00:08:32.800><c> there's</c><00:08:33.120><c> no</c><00:08:33.680><c> universal</c><00:08:34.399><c> ranking</c><00:08:34.800><c> system</c>

00:08:35.190 --> 00:08:35.200 align:start position:0%
and there's no universal ranking system
 

00:08:35.200 --> 00:08:36.310 align:start position:0%
and there's no universal ranking system
that<00:08:35.440><c> has</c><00:08:35.680><c> both</c>

00:08:36.310 --> 00:08:36.320 align:start position:0%
that has both
 

00:08:36.320 --> 00:08:39.670 align:start position:0%
that has both
international<00:08:37.120><c> schools</c><00:08:37.839><c> and</c><00:08:38.800><c> small</c><00:08:39.279><c> liberal</c>

00:08:39.670 --> 00:08:39.680 align:start position:0%
international schools and small liberal
 

00:08:39.680 --> 00:08:44.230 align:start position:0%
international schools and small liberal
arts<00:08:39.919><c> colleges</c><00:08:40.479><c> and</c><00:08:40.560><c> community</c><00:08:41.039><c> colleges</c>

00:08:44.230 --> 00:08:44.240 align:start position:0%
 
 

00:08:44.240 --> 00:08:47.030 align:start position:0%
 
now<00:08:44.720><c> based</c><00:08:45.120><c> on</c><00:08:45.519><c> our</c><00:08:45.680><c> key</c><00:08:45.920><c> findings</c><00:08:46.720><c> i</c>

00:08:47.030 --> 00:08:47.040 align:start position:0%
now based on our key findings i
 

00:08:47.040 --> 00:08:49.509 align:start position:0%
now based on our key findings i
recommended<00:08:47.760><c> that</c><00:08:47.920><c> we</c><00:08:48.160><c> prioritize</c><00:08:48.800><c> applicant</c>

00:08:49.509 --> 00:08:49.519 align:start position:0%
recommended that we prioritize applicant
 

00:08:49.519 --> 00:08:53.190 align:start position:0%
recommended that we prioritize applicant
gpa<00:08:50.080><c> over</c><00:08:50.320><c> school</c><00:08:50.560><c> ranking</c><00:08:51.760><c> and</c><00:08:52.240><c> applications</c>

00:08:53.190 --> 00:08:53.200 align:start position:0%
gpa over school ranking and applications
 

00:08:53.200 --> 00:08:55.509 align:start position:0%
gpa over school ranking and applications
should<00:08:53.440><c> use</c><00:08:53.600><c> as</c><00:08:53.760><c> many</c><00:08:54.160><c> predefined</c><00:08:54.880><c> options</c><00:08:55.279><c> as</c>

00:08:55.509 --> 00:08:55.519 align:start position:0%
should use as many predefined options as
 

00:08:55.519 --> 00:08:58.150 align:start position:0%
should use as many predefined options as
possible<00:08:56.000><c> to</c><00:08:56.160><c> minimize</c><00:08:56.800><c> any</c><00:08:57.040><c> user</c><00:08:57.519><c> freeform</c>

00:08:58.150 --> 00:08:58.160 align:start position:0%
possible to minimize any user freeform
 

00:08:58.160 --> 00:08:59.110 align:start position:0%
possible to minimize any user freeform
input

00:08:59.110 --> 00:08:59.120 align:start position:0%
input
 

00:08:59.120 --> 00:09:01.750 align:start position:0%
input
um<00:08:59.519><c> to</c><00:08:59.680><c> make</c><00:08:59.839><c> the</c><00:09:00.000><c> data</c><00:09:00.399><c> cleaning</c><00:09:00.800><c> much</c><00:09:01.120><c> easier</c>

00:09:01.750 --> 00:09:01.760 align:start position:0%
um to make the data cleaning much easier
 

00:09:01.760 --> 00:09:02.470 align:start position:0%
um to make the data cleaning much easier
and

00:09:02.470 --> 00:09:02.480 align:start position:0%
and
 

00:09:02.480 --> 00:09:05.030 align:start position:0%
and
matching<00:09:02.880><c> records</c><00:09:03.200><c> a</c><00:09:03.279><c> lot</c><00:09:03.440><c> easier</c><00:09:04.399><c> and</c><00:09:04.560><c> then</c>

00:09:05.030 --> 00:09:05.040 align:start position:0%
matching records a lot easier and then
 

00:09:05.040 --> 00:09:07.030 align:start position:0%
matching records a lot easier and then
the<00:09:05.440><c> initial</c><00:09:05.839><c> data</c><00:09:06.160><c> set</c>

00:09:07.030 --> 00:09:07.040 align:start position:0%
the initial data set
 

00:09:07.040 --> 00:09:09.030 align:start position:0%
the initial data set
we<00:09:07.279><c> had</c><00:09:07.600><c> actually</c><00:09:08.000><c> included</c><00:09:08.640><c> letters</c><00:09:08.959><c> of</c>

00:09:09.030 --> 00:09:09.040 align:start position:0%
we had actually included letters of
 

00:09:09.040 --> 00:09:10.389 align:start position:0%
we had actually included letters of
recommendation

00:09:10.389 --> 00:09:10.399 align:start position:0%
recommendation
 

00:09:10.399 --> 00:09:12.870 align:start position:0%
recommendation
but<00:09:10.800><c> it</c><00:09:10.880><c> was</c><00:09:11.120><c> very</c><00:09:11.440><c> very</c><00:09:11.680><c> minimal</c><00:09:12.399><c> none</c><00:09:12.640><c> were</c>

00:09:12.870 --> 00:09:12.880 align:start position:0%
but it was very very minimal none were
 

00:09:12.880 --> 00:09:15.430 align:start position:0%
but it was very very minimal none were
archived<00:09:13.360><c> before</c><00:09:13.680><c> the</c><00:09:13.839><c> year</c><00:09:14.000><c> of</c><00:09:14.080><c> 2015</c>

00:09:15.430 --> 00:09:15.440 align:start position:0%
archived before the year of 2015
 

00:09:15.440 --> 00:09:17.110 align:start position:0%
archived before the year of 2015
so<00:09:15.680><c> admissions</c><00:09:16.399><c> should</c><00:09:16.640><c> make</c><00:09:16.800><c> sure</c><00:09:16.959><c> that</c>

00:09:17.110 --> 00:09:17.120 align:start position:0%
so admissions should make sure that
 

00:09:17.120 --> 00:09:19.430 align:start position:0%
so admissions should make sure that
these<00:09:17.360><c> letters</c><00:09:17.680><c> of</c><00:09:17.839><c> wreck</c><00:09:18.320><c> are</c>

00:09:19.430 --> 00:09:19.440 align:start position:0%
these letters of wreck are
 

00:09:19.440 --> 00:09:21.269 align:start position:0%
these letters of wreck are
saved<00:09:19.920><c> and</c><00:09:20.080><c> that</c><00:09:20.320><c> the</c><00:09:20.640><c> ratings</c><00:09:21.040><c> of</c><00:09:21.120><c> these</c>

00:09:21.269 --> 00:09:21.279 align:start position:0%
saved and that the ratings of these
 

00:09:21.279 --> 00:09:23.509 align:start position:0%
saved and that the ratings of these
letters<00:09:21.600><c> of</c><00:09:21.680><c> rex</c><00:09:22.000><c> are</c><00:09:22.160><c> also</c><00:09:22.480><c> archived</c>

00:09:23.509 --> 00:09:23.519 align:start position:0%
letters of rex are also archived
 

00:09:23.519 --> 00:09:26.550 align:start position:0%
letters of rex are also archived
now<00:09:24.080><c> um</c><00:09:25.519><c> also</c><00:09:25.839><c> should</c><00:09:26.000><c> continue</c>

00:09:26.550 --> 00:09:26.560 align:start position:0%
now um also should continue
 

00:09:26.560 --> 00:09:29.110 align:start position:0%
now um also should continue
obtaining<00:09:27.120><c> more</c><00:09:27.360><c> institution</c><00:09:27.920><c> data</c><00:09:28.640><c> like</c><00:09:28.959><c> the</c>

00:09:29.110 --> 00:09:29.120 align:start position:0%
obtaining more institution data like the
 

00:09:29.120 --> 00:09:31.430 align:start position:0%
obtaining more institution data like the
rankings<00:09:29.760><c> and</c><00:09:30.000><c> average</c><00:09:30.399><c> gpas</c><00:09:31.120><c> from</c><00:09:31.279><c> these</c>

00:09:31.430 --> 00:09:31.440 align:start position:0%
rankings and average gpas from these
 

00:09:31.440 --> 00:09:32.870 align:start position:0%
rankings and average gpas from these
different<00:09:31.760><c> institutions</c>

00:09:32.870 --> 00:09:32.880 align:start position:0%
different institutions
 

00:09:32.880 --> 00:09:35.990 align:start position:0%
different institutions
since<00:09:33.360><c> the</c><00:09:33.760><c> average</c><00:09:34.240><c> that</c><00:09:34.480><c> i</c><00:09:34.640><c> had</c><00:09:34.800><c> to</c><00:09:34.959><c> use</c><00:09:35.279><c> was</c>

00:09:35.990 --> 00:09:36.000 align:start position:0%
since the average that i had to use was
 

00:09:36.000 --> 00:09:39.350 align:start position:0%
since the average that i had to use was
among<00:09:36.720><c> the</c><00:09:37.600><c> enrolled</c><00:09:38.160><c> pen</c><00:09:38.480><c> students</c><00:09:38.959><c> not</c><00:09:39.200><c> of</c>

00:09:39.350 --> 00:09:39.360 align:start position:0%
among the enrolled pen students not of
 

00:09:39.360 --> 00:09:39.750 align:start position:0%
among the enrolled pen students not of
all

00:09:39.750 --> 00:09:39.760 align:start position:0%
all
 

00:09:39.760 --> 00:09:42.790 align:start position:0%
all
people<00:09:40.000><c> who</c><00:09:40.399><c> applied</c><00:09:41.360><c> and</c><00:09:41.519><c> then</c><00:09:42.240><c> one</c><00:09:42.480><c> final</c>

00:09:42.790 --> 00:09:42.800 align:start position:0%
people who applied and then one final
 

00:09:42.800 --> 00:09:44.630 align:start position:0%
people who applied and then one final
thing<00:09:43.040><c> i'd</c><00:09:43.200><c> recommend</c><00:09:43.760><c> would</c><00:09:43.920><c> be</c><00:09:44.080><c> to</c><00:09:44.240><c> build</c><00:09:44.560><c> a</c>

00:09:44.630 --> 00:09:44.640 align:start position:0%
thing i'd recommend would be to build a
 

00:09:44.640 --> 00:09:46.470 align:start position:0%
thing i'd recommend would be to build a
tool<00:09:44.959><c> based</c><00:09:45.279><c> on</c><00:09:45.360><c> these</c><00:09:45.600><c> feature</c><00:09:46.000><c> importance</c>

00:09:46.470 --> 00:09:46.480 align:start position:0%
tool based on these feature importance
 

00:09:46.480 --> 00:09:47.350 align:start position:0%
tool based on these feature importance
weights

00:09:47.350 --> 00:09:47.360 align:start position:0%
weights
 

00:09:47.360 --> 00:09:49.990 align:start position:0%
weights
that<00:09:47.519><c> the</c><00:09:48.399><c> models</c><00:09:48.800><c> identified</c><00:09:49.440><c> so</c><00:09:49.600><c> that</c><00:09:49.839><c> it</c>

00:09:49.990 --> 00:09:50.000 align:start position:0%
that the models identified so that it
 

00:09:50.000 --> 00:09:51.030 align:start position:0%
that the models identified so that it
can<00:09:50.240><c> actually</c><00:09:50.640><c> make</c>

00:09:51.030 --> 00:09:51.040 align:start position:0%
can actually make
 

00:09:51.040 --> 00:09:54.389 align:start position:0%
can actually make
admissions<00:09:52.480><c> jobs</c><00:09:52.959><c> a</c><00:09:53.040><c> lot</c><00:09:53.279><c> easier</c><00:09:53.839><c> that's</c><00:09:54.240><c> like</c>

00:09:54.389 --> 00:09:54.399 align:start position:0%
admissions jobs a lot easier that's like
 

00:09:54.399 --> 00:09:57.110 align:start position:0%
admissions jobs a lot easier that's like
a<00:09:54.480><c> supplemental</c><00:09:55.120><c> tool</c>

00:09:57.110 --> 00:09:57.120 align:start position:0%
a supplemental tool
 

00:09:57.120 --> 00:09:59.750 align:start position:0%
a supplemental tool
so<00:09:58.080><c> thank</c><00:09:58.320><c> you</c><00:09:58.480><c> for</c><00:09:59.120><c> tuning</c><00:09:59.440><c> in</c><00:09:59.519><c> to</c><00:09:59.600><c> my</c>

00:09:59.750 --> 00:09:59.760 align:start position:0%
so thank you for tuning in to my
 

00:09:59.760 --> 00:10:00.630 align:start position:0%
so thank you for tuning in to my
presentation

00:10:00.630 --> 00:10:00.640 align:start position:0%
presentation
 

00:10:00.640 --> 00:10:02.550 align:start position:0%
presentation
i<00:10:00.800><c> hope</c><00:10:01.040><c> you</c><00:10:01.279><c> find</c><00:10:01.440><c> that</c><00:10:01.600><c> my</c><00:10:01.839><c> learnings</c><00:10:02.320><c> were</c>

00:10:02.550 --> 00:10:02.560 align:start position:0%
i hope you find that my learnings were
 

00:10:02.560 --> 00:10:07.949 align:start position:0%
i hope you find that my learnings were
helpful<00:10:03.040><c> on</c><00:10:03.120><c> your</c><00:10:03.360><c> data</c><00:10:03.600><c> science</c>

00:10:07.949 --> 00:10:07.959 align:start position:0%
 
 

00:10:07.959 --> 00:10:10.959 align:start position:0%
 
journey

