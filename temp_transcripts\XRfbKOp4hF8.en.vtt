WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.930 align:start position:0%
 
hey<00:00:00.960><c> there</c><00:00:01.140><c> it's</c><00:00:01.560><c> Matt</c><00:00:01.740><c> <PERSON></c><00:00:01.979><c> from</c><00:00:02.460><c> olama</c>

00:00:02.930 --> 00:00:02.940 align:start position:0%
hey there it's <PERSON> from olama
 

00:00:02.940 --> 00:00:05.570 align:start position:0%
hey there it's <PERSON> from olama
last<00:00:03.780><c> night</c><00:00:04.020><c> I</c><00:00:04.560><c> finally</c><00:00:04.860><c> posted</c><00:00:05.279><c> the</c><00:00:05.400><c> video</c>

00:00:05.570 --> 00:00:05.580 align:start position:0%
last night I finally posted the video
 

00:00:05.580 --> 00:00:08.870 align:start position:0%
last night I finally posted the video
about<00:00:06.200><c> 0.0.12</c><00:00:07.200><c> and</c><00:00:07.500><c> today</c><00:00:07.799><c> I</c><00:00:08.519><c> get</c><00:00:08.639><c> to</c><00:00:08.760><c> post</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
about 0.0.12 and today I get to post
 

00:00:08.880 --> 00:00:10.270 align:start position:0%
about 0.0.12 and today I get to post
another<00:00:09.059><c> one</c><00:00:09.300><c> about</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
another one about
 

00:00:10.280 --> 00:00:14.089 align:start position:0%
another one about
0.0.13<00:00:11.280><c> this</c><00:00:12.240><c> one</c><00:00:12.360><c> is</c><00:00:12.660><c> even</c><00:00:12.840><c> bigger</c><00:00:13.500><c> than</c><00:00:13.860><c> the</c>

00:00:14.089 --> 00:00:14.099 align:start position:0%
0.0.13 this one is even bigger than the
 

00:00:14.099 --> 00:00:15.049 align:start position:0%
0.0.13 this one is even bigger than the
last<00:00:14.219><c> one</c>

00:00:15.049 --> 00:00:15.059 align:start position:0%
last one
 

00:00:15.059 --> 00:00:17.330 align:start position:0%
last one
just<00:00:15.480><c> like</c><00:00:15.660><c> last</c><00:00:15.900><c> time</c><00:00:16.080><c> I</c><00:00:16.440><c> have</c><00:00:16.560><c> two</c><00:00:16.800><c> commands</c>

00:00:17.330 --> 00:00:17.340 align:start position:0%
just like last time I have two commands
 

00:00:17.340 --> 00:00:19.130 align:start position:0%
just like last time I have two commands
that<00:00:17.460><c> I'm</c><00:00:17.580><c> running</c><00:00:17.760><c> but</c><00:00:18.420><c> now</c><00:00:18.720><c> it's</c><00:00:18.900><c> the</c><00:00:19.020><c> other</c>

00:00:19.130 --> 00:00:19.140 align:start position:0%
that I'm running but now it's the other
 

00:00:19.140 --> 00:00:22.029 align:start position:0%
that I'm running but now it's the other
way<00:00:19.320><c> around</c><00:00:19.440><c> so</c><00:00:20.160><c> olama</c><00:00:20.820><c> is</c>

00:00:22.029 --> 00:00:22.039 align:start position:0%
way around so olama is
 

00:00:22.039 --> 00:00:27.290 align:start position:0%
way around so olama is
0.0.12<00:00:23.039><c> and</c><00:00:24.240><c> ulama</c><00:00:24.539><c> is</c><00:00:25.460><c> 0.0.13</c><00:00:26.460><c> and</c><00:00:27.000><c> to</c><00:00:27.180><c> make</c>

00:00:27.290 --> 00:00:27.300 align:start position:0%
0.0.12 and ulama is 0.0.13 and to make
 

00:00:27.300 --> 00:00:29.470 align:start position:0%
0.0.12 and ulama is 0.0.13 and to make
it<00:00:27.420><c> easier</c><00:00:27.720><c> to</c><00:00:27.960><c> say</c><00:00:28.080><c> in</c><00:00:28.260><c> the</c><00:00:28.439><c> video</c><00:00:28.500><c> I'll</c><00:00:28.920><c> call</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
it easier to say in the video I'll call
 

00:00:29.480 --> 00:00:34.150 align:start position:0%
it easier to say in the video I'll call
0.0.12<00:00:30.480><c> olama</c><00:00:31.380><c> 12</c><00:00:31.740><c> or</c><00:00:32.040><c> just</c><00:00:32.220><c> 12</c><00:00:32.520><c> and</c>

00:00:34.150 --> 00:00:34.160 align:start position:0%
0.0.12 olama 12 or just 12 and
 

00:00:34.160 --> 00:00:38.090 align:start position:0%
0.0.12 olama 12 or just 12 and
0.0.13<00:00:35.160><c> I'll</c><00:00:36.000><c> refer</c><00:00:36.360><c> to</c><00:00:36.420><c> as</c><00:00:36.660><c> olama</c><00:00:37.140><c> 13.</c><00:00:37.739><c> again</c>

00:00:38.090 --> 00:00:38.100 align:start position:0%
0.0.13 I'll refer to as olama 13. again
 

00:00:38.100 --> 00:00:40.729 align:start position:0%
0.0.13 I'll refer to as olama 13. again
that's<00:00:38.460><c> just</c><00:00:38.700><c> to</c><00:00:38.880><c> make</c><00:00:39.059><c> the</c><00:00:39.300><c> video</c><00:00:39.660><c> easier</c><00:00:40.379><c> to</c>

00:00:40.729 --> 00:00:40.739 align:start position:0%
that's just to make the video easier to
 

00:00:40.739 --> 00:00:44.510 align:start position:0%
that's just to make the video easier to
follow<00:00:40.920><c> okay</c><00:00:41.520><c> let's</c><00:00:42.180><c> get</c><00:00:42.420><c> into</c><00:00:42.660><c> it</c><00:00:43.160><c> models</c><00:00:44.160><c> now</c>

00:00:44.510 --> 00:00:44.520 align:start position:0%
follow okay let's get into it models now
 

00:00:44.520 --> 00:00:47.270 align:start position:0%
follow okay let's get into it models now
stay<00:00:44.820><c> in</c><00:00:45.239><c> memory</c><00:00:45.540><c> between</c><00:00:45.840><c> messages</c><00:00:46.200><c> this</c><00:00:47.100><c> is</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
stay in memory between messages this is
 

00:00:47.280 --> 00:00:50.090 align:start position:0%
stay in memory between messages this is
huge<00:00:47.879><c> before</c><00:00:48.660><c> if</c><00:00:49.320><c> you</c><00:00:49.440><c> had</c><00:00:49.559><c> a</c><00:00:49.680><c> chat</c><00:00:49.860><c> session</c>

00:00:50.090 --> 00:00:50.100 align:start position:0%
huge before if you had a chat session
 

00:00:50.100 --> 00:00:52.790 align:start position:0%
huge before if you had a chat session
with<00:00:50.340><c> llama</c><00:00:50.760><c> we</c><00:00:51.120><c> spent</c><00:00:51.360><c> some</c><00:00:51.539><c> time</c><00:00:51.840><c> loading</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
with llama we spent some time loading
 

00:00:52.800 --> 00:00:54.770 align:start position:0%
with llama we spent some time loading
the<00:00:52.920><c> model</c><00:00:53.219><c> into</c><00:00:53.579><c> memory</c>

00:00:54.770 --> 00:00:54.780 align:start position:0%
the model into memory
 

00:00:54.780 --> 00:00:58.910 align:start position:0%
the model into memory
with<00:00:56.059><c> every</c><00:00:57.059><c> single</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
with every single
 

00:00:58.920 --> 00:01:01.250 align:start position:0%
with every single
prompt

00:01:01.250 --> 00:01:01.260 align:start position:0%
prompt
 

00:01:01.260 --> 00:01:03.410 align:start position:0%
prompt
now<00:01:01.739><c> in</c><00:01:01.920><c> 13</c><00:01:02.100><c> we're</c><00:01:02.460><c> keeping</c><00:01:02.760><c> it</c><00:01:02.879><c> loaded</c><00:01:03.239><c> in</c>

00:01:03.410 --> 00:01:03.420 align:start position:0%
now in 13 we're keeping it loaded in
 

00:01:03.420 --> 00:01:05.149 align:start position:0%
now in 13 we're keeping it loaded in
memory<00:01:03.660><c> until</c><00:01:03.840><c> you</c><00:01:04.199><c> end</c><00:01:04.379><c> the</c><00:01:04.559><c> session</c><00:01:04.739><c> with</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
memory until you end the session with
 

00:01:05.159 --> 00:01:07.730 align:start position:0%
memory until you end the session with
olama<00:01:05.580><c> so</c><00:01:06.180><c> let's</c><00:01:06.420><c> ask</c><00:01:06.720><c> a</c><00:01:06.900><c> few</c><00:01:07.020><c> questions</c><00:01:07.260><c> in</c>

00:01:07.730 --> 00:01:07.740 align:start position:0%
olama so let's ask a few questions in
 

00:01:07.740 --> 00:01:08.750 align:start position:0%
olama so let's ask a few questions in
12.

00:01:08.750 --> 00:01:08.760 align:start position:0%
12.
 

00:01:08.760 --> 00:01:13.190 align:start position:0%
12.
uh<00:01:09.299><c> like</c><00:01:09.540><c> why</c><00:01:09.960><c> is</c><00:01:10.140><c> this</c><00:01:10.260><c> guy</c><00:01:10.380><c> blue</c><00:01:10.680><c> uh</c><00:01:11.900><c> is</c><00:01:12.900><c> there</c>

00:01:13.190 --> 00:01:13.200 align:start position:0%
uh like why is this guy blue uh is there
 

00:01:13.200 --> 00:01:16.010 align:start position:0%
uh like why is this guy blue uh is there
is<00:01:13.380><c> also</c><00:01:13.740><c> green</c><00:01:14.119><c> and</c><00:01:15.119><c> uh</c><00:01:15.360><c> what</c><00:01:15.540><c> what</c><00:01:15.840><c> happens</c>

00:01:16.010 --> 00:01:16.020 align:start position:0%
is also green and uh what what happens
 

00:01:16.020 --> 00:01:17.450 align:start position:0%
is also green and uh what what happens
at<00:01:16.260><c> sunset</c>

00:01:17.450 --> 00:01:17.460 align:start position:0%
at sunset
 

00:01:17.460 --> 00:01:19.850 align:start position:0%
at sunset
and<00:01:18.060><c> now</c><00:01:18.299><c> let's</c><00:01:18.659><c> ask</c><00:01:19.080><c> those</c><00:01:19.320><c> same</c><00:01:19.560><c> questions</c>

00:01:19.850 --> 00:01:19.860 align:start position:0%
and now let's ask those same questions
 

00:01:19.860 --> 00:01:23.810 align:start position:0%
and now let's ask those same questions
in<00:01:20.460><c> 13.</c><00:01:21.360><c> yeah</c><00:01:21.960><c> that's</c><00:01:22.439><c> pretty</c><00:01:22.680><c> sweet</c><00:01:22.860><c> oh</c><00:01:23.460><c> and</c>

00:01:23.810 --> 00:01:23.820 align:start position:0%
in 13. yeah that's pretty sweet oh and
 

00:01:23.820 --> 00:01:25.789 align:start position:0%
in 13. yeah that's pretty sweet oh and
did<00:01:24.060><c> you</c><00:01:24.240><c> notice</c><00:01:24.540><c> the</c><00:01:24.840><c> change</c><00:01:25.020><c> in</c><00:01:25.320><c> the</c><00:01:25.439><c> verbose</c>

00:01:25.789 --> 00:01:25.799 align:start position:0%
did you notice the change in the verbose
 

00:01:25.799 --> 00:01:27.710 align:start position:0%
did you notice the change in the verbose
output<00:01:26.280><c> let's</c><00:01:26.700><c> look</c><00:01:26.880><c> at</c><00:01:27.000><c> one</c><00:01:27.240><c> of</c><00:01:27.360><c> those</c><00:01:27.420><c> from</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
output let's look at one of those from
 

00:01:27.720 --> 00:01:31.490 align:start position:0%
output let's look at one of those from
12<00:01:27.960><c> and</c><00:01:28.920><c> here</c><00:01:29.340><c> we</c><00:01:29.520><c> are</c><00:01:29.640><c> in</c><00:01:29.759><c> 13.</c><00:01:30.420><c> you</c><00:01:31.080><c> can</c><00:01:31.200><c> see</c><00:01:31.320><c> a</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
12 and here we are in 13. you can see a
 

00:01:31.500 --> 00:01:33.950 align:start position:0%
12 and here we are in 13. you can see a
lot<00:01:31.560><c> more</c><00:01:31.680><c> detail</c><00:01:32.100><c> now</c><00:01:32.460><c> in</c><00:01:32.880><c> the</c><00:01:33.000><c> output</c>

00:01:33.950 --> 00:01:33.960 align:start position:0%
lot more detail now in the output
 

00:01:33.960 --> 00:01:36.830 align:start position:0%
lot more detail now in the output
next<00:01:34.500><c> we</c><00:01:34.920><c> expand</c><00:01:35.340><c> our</c><00:01:35.880><c> compatibility</c><00:01:36.540><c> story</c>

00:01:36.830 --> 00:01:36.840 align:start position:0%
next we expand our compatibility story
 

00:01:36.840 --> 00:01:39.590 align:start position:0%
next we expand our compatibility story
up<00:01:37.380><c> until</c><00:01:37.500><c> 12</c><00:01:37.860><c> we</c><00:01:38.220><c> only</c><00:01:38.400><c> supported</c><00:01:38.880><c> Max</c><00:01:39.180><c> with</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
up until 12 we only supported Max with
 

00:01:39.600 --> 00:01:42.469 align:start position:0%
up until 12 we only supported Max with
apple<00:01:39.780><c> silicon</c><00:01:40.380><c> so</c><00:01:40.920><c> that's</c><00:01:41.040><c> the</c><00:01:41.280><c> M1</c><00:01:41.700><c> and</c><00:01:41.939><c> M2</c>

00:01:42.469 --> 00:01:42.479 align:start position:0%
apple silicon so that's the M1 and M2
 

00:01:42.479 --> 00:01:46.010 align:start position:0%
apple silicon so that's the M1 and M2
chips<00:01:43.140><c> now</c><00:01:44.100><c> we've</c><00:01:44.460><c> expanded</c><00:01:44.820><c> that</c><00:01:45.000><c> to</c><00:01:45.299><c> intomax</c>

00:01:46.010 --> 00:01:46.020 align:start position:0%
chips now we've expanded that to intomax
 

00:01:46.020 --> 00:01:48.170 align:start position:0%
chips now we've expanded that to intomax
and<00:01:46.619><c> we're</c><00:01:46.799><c> Distributing</c><00:01:47.220><c> the</c><00:01:47.400><c> executable</c><00:01:47.939><c> as</c>

00:01:48.170 --> 00:01:48.180 align:start position:0%
and we're Distributing the executable as
 

00:01:48.180 --> 00:01:51.109 align:start position:0%
and we're Distributing the executable as
a<00:01:48.420><c> universal</c><00:01:48.840><c> binary</c><00:01:49.320><c> so</c><00:01:50.100><c> no</c><00:01:50.460><c> need</c><00:01:50.640><c> to</c><00:01:50.820><c> choose</c>

00:01:51.109 --> 00:01:51.119 align:start position:0%
a universal binary so no need to choose
 

00:01:51.119 --> 00:01:53.630 align:start position:0%
a universal binary so no need to choose
which<00:01:51.420><c> platform</c><00:01:51.840><c> you're</c><00:01:52.200><c> on</c><00:01:52.439><c> and</c><00:01:53.040><c> windows</c><00:01:53.520><c> and</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
which platform you're on and windows and
 

00:01:53.640 --> 00:01:55.550 align:start position:0%
which platform you're on and windows and
Linux<00:01:54.000><c> that</c><00:01:54.659><c> support</c><00:01:54.840><c> is</c><00:01:55.079><c> going</c><00:01:55.200><c> to</c><00:01:55.320><c> be</c><00:01:55.439><c> coming</c>

00:01:55.550 --> 00:01:55.560 align:start position:0%
Linux that support is going to be coming
 

00:01:55.560 --> 00:01:56.870 align:start position:0%
Linux that support is going to be coming
soon

00:01:56.870 --> 00:01:56.880 align:start position:0%
soon
 

00:01:56.880 --> 00:01:59.210 align:start position:0%
soon
when<00:01:57.540><c> you're</c><00:01:57.659><c> in</c><00:01:57.899><c> a</c><00:01:58.020><c> chat</c><00:01:58.140><c> with</c><00:01:58.320><c> olama</c><00:01:58.740><c> you</c><00:01:59.100><c> now</c>

00:01:59.210 --> 00:01:59.220 align:start position:0%
when you're in a chat with olama you now
 

00:01:59.220 --> 00:02:01.310 align:start position:0%
when you're in a chat with olama you now
have<00:01:59.399><c> a</c><00:01:59.579><c> set</c><00:01:59.700><c> of</c><00:01:59.939><c> Slash</c><00:02:00.299><c> commands</c><00:02:00.780><c> for</c><00:02:01.020><c> show</c>

00:02:01.310 --> 00:02:01.320 align:start position:0%
have a set of Slash commands for show
 

00:02:01.320 --> 00:02:03.350 align:start position:0%
have a set of Slash commands for show
this<00:02:02.159><c> will</c><00:02:02.280><c> let</c><00:02:02.399><c> you</c><00:02:02.520><c> inspect</c><00:02:02.939><c> the</c><00:02:03.180><c> current</c>

00:02:03.350 --> 00:02:03.360 align:start position:0%
this will let you inspect the current
 

00:02:03.360 --> 00:02:05.510 align:start position:0%
this will let you inspect the current
model<00:02:03.840><c> to</c><00:02:04.500><c> find</c><00:02:04.619><c> out</c><00:02:04.740><c> how</c><00:02:04.920><c> it's</c><00:02:05.040><c> configured</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
model to find out how it's configured
 

00:02:05.520 --> 00:02:08.029 align:start position:0%
model to find out how it's configured
you<00:02:06.119><c> can</c><00:02:06.180><c> show</c><00:02:06.360><c> the</c><00:02:06.659><c> license</c><00:02:06.899><c> or</c><00:02:07.560><c> the</c><00:02:07.799><c> system</c>

00:02:08.029 --> 00:02:08.039 align:start position:0%
you can show the license or the system
 

00:02:08.039 --> 00:02:10.370 align:start position:0%
you can show the license or the system
prompt<00:02:08.520><c> or</c><00:02:09.119><c> the</c><00:02:09.360><c> template</c><00:02:09.660><c> used</c><00:02:09.899><c> for</c><00:02:10.200><c> the</c>

00:02:10.370 --> 00:02:10.380 align:start position:0%
prompt or the template used for the
 

00:02:10.380 --> 00:02:11.029 align:start position:0%
prompt or the template used for the
model

00:02:11.029 --> 00:02:11.039 align:start position:0%
model
 

00:02:11.039 --> 00:02:12.710 align:start position:0%
model
did<00:02:11.340><c> you</c><00:02:11.520><c> know</c><00:02:11.640><c> about</c><00:02:11.760><c> the</c><00:02:12.000><c> slash</c><00:02:12.239><c> commands</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
did you know about the slash commands
 

00:02:12.720 --> 00:02:15.110 align:start position:0%
did you know about the slash commands
you<00:02:13.080><c> can</c><00:02:13.140><c> turn</c><00:02:13.260><c> on</c><00:02:13.440><c> verbose</c><00:02:13.920><c> to</c><00:02:14.400><c> see</c><00:02:14.640><c> stats</c><00:02:15.060><c> for</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
you can turn on verbose to see stats for
 

00:02:15.120 --> 00:02:18.470 align:start position:0%
you can turn on verbose to see stats for
each<00:02:15.300><c> prompt</c><00:02:15.599><c> or</c><00:02:16.560><c> just</c><00:02:16.980><c> use</c><00:02:17.280><c> slash</c><00:02:17.640><c> help</c><00:02:17.879><c> and</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
each prompt or just use slash help and
 

00:02:18.480 --> 00:02:20.210 align:start position:0%
each prompt or just use slash help and
that'll<00:02:18.720><c> show</c><00:02:18.900><c> you</c><00:02:19.080><c> all</c><00:02:19.379><c> the</c><00:02:19.500><c> commands</c><00:02:19.920><c> that</c>

00:02:20.210 --> 00:02:20.220 align:start position:0%
that'll show you all the commands that
 

00:02:20.220 --> 00:02:21.170 align:start position:0%
that'll show you all the commands that
are<00:02:20.340><c> available</c>

00:02:21.170 --> 00:02:21.180 align:start position:0%
are available
 

00:02:21.180 --> 00:02:23.750 align:start position:0%
are available
you<00:02:21.780><c> can</c><00:02:21.840><c> also</c><00:02:22.200><c> now</c><00:02:22.440><c> have</c><00:02:22.680><c> multi-line</c><00:02:23.220><c> strings</c>

00:02:23.750 --> 00:02:23.760 align:start position:0%
you can also now have multi-line strings
 

00:02:23.760 --> 00:02:26.630 align:start position:0%
you can also now have multi-line strings
when<00:02:24.300><c> you</c><00:02:24.420><c> run</c><00:02:24.599><c> olama</c><00:02:25.200><c> run</c><00:02:25.440><c> these</c><00:02:26.280><c> start</c><00:02:26.459><c> and</c>

00:02:26.630 --> 00:02:26.640 align:start position:0%
when you run olama run these start and
 

00:02:26.640 --> 00:02:28.910 align:start position:0%
when you run olama run these start and
end<00:02:26.819><c> with</c><00:02:27.060><c> three</c><00:02:27.300><c> double</c><00:02:27.540><c> quotes</c><00:02:27.800><c> this</c><00:02:28.800><c> was</c>

00:02:28.910 --> 00:02:28.920 align:start position:0%
end with three double quotes this was
 

00:02:28.920 --> 00:02:30.290 align:start position:0%
end with three double quotes this was
something<00:02:29.220><c> that</c><00:02:29.400><c> was</c><00:02:29.520><c> requested</c><00:02:29.940><c> in</c><00:02:30.120><c> the</c>

00:02:30.290 --> 00:02:30.300 align:start position:0%
something that was requested in the
 

00:02:30.300 --> 00:02:32.330 align:start position:0%
something that was requested in the
Discord<00:02:30.599><c> I</c><00:02:31.020><c> think</c><00:02:31.140><c> Did</c><00:02:31.800><c> You</c><00:02:31.920><c> Know</c><00:02:32.040><c> by</c><00:02:32.220><c> the</c>

00:02:32.330 --> 00:02:32.340 align:start position:0%
Discord I think Did You Know by the
 

00:02:32.340 --> 00:02:34.850 align:start position:0%
Discord I think Did You Know by the
Discord<00:02:32.640><c> you</c><00:02:33.480><c> can</c><00:02:33.599><c> find</c><00:02:33.780><c> out</c><00:02:34.020><c> about</c><00:02:34.379><c> it</c><00:02:34.620><c> when</c>

00:02:34.850 --> 00:02:34.860 align:start position:0%
Discord you can find out about it when
 

00:02:34.860 --> 00:02:36.890 align:start position:0%
Discord you can find out about it when
you<00:02:34.980><c> click</c><00:02:35.280><c> the</c><00:02:35.400><c> Discord</c><00:02:35.700><c> Link</c><00:02:35.940><c> at</c><00:02:36.360><c> the</c><00:02:36.480><c> top</c><00:02:36.599><c> of</c>

00:02:36.890 --> 00:02:36.900 align:start position:0%
you click the Discord Link at the top of
 

00:02:36.900 --> 00:02:39.229 align:start position:0%
you click the Discord Link at the top of
the<00:02:37.040><c> olama.ai</c><00:02:38.040><c> website</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
the olama.ai website
 

00:02:39.239 --> 00:02:41.690 align:start position:0%
the olama.ai website
now<00:02:40.020><c> up</c><00:02:40.200><c> until</c><00:02:40.379><c> this</c><00:02:40.860><c> version</c><00:02:41.040><c> Whenever</c><00:02:41.519><c> there</c>

00:02:41.690 --> 00:02:41.700 align:start position:0%
now up until this version Whenever there
 

00:02:41.700 --> 00:02:43.850 align:start position:0%
now up until this version Whenever there
was<00:02:41.879><c> a</c><00:02:42.060><c> new</c><00:02:42.180><c> version</c><00:02:42.360><c> we'd</c><00:02:43.080><c> put</c><00:02:43.260><c> a</c><00:02:43.500><c> dialog</c>

00:02:43.850 --> 00:02:43.860 align:start position:0%
was a new version we'd put a dialog
 

00:02:43.860 --> 00:02:45.350 align:start position:0%
was a new version we'd put a dialog
right<00:02:44.280><c> there</c><00:02:44.459><c> right</c><00:02:44.819><c> in</c><00:02:45.060><c> front</c><00:02:45.180><c> of</c><00:02:45.300><c> you</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
right there right in front of you
 

00:02:45.360 --> 00:02:47.630 align:start position:0%
right there right in front of you
announcing<00:02:45.840><c> the</c><00:02:46.080><c> new</c><00:02:46.200><c> version</c><00:02:46.379><c> if</c><00:02:47.280><c> you</c><00:02:47.400><c> chose</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
announcing the new version if you chose
 

00:02:47.640 --> 00:02:49.430 align:start position:0%
announcing the new version if you chose
to<00:02:47.819><c> not</c><00:02:48.060><c> install</c><00:02:48.300><c> it</c><00:02:48.660><c> which</c><00:02:49.140><c> still</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
to not install it which still
 

00:02:49.440 --> 00:02:50.809 align:start position:0%
to not install it which still
automatically<00:02:50.040><c> installed</c><00:02:50.459><c> the</c><00:02:50.580><c> next</c><00:02:50.700><c> time</c>

00:02:50.809 --> 00:02:50.819 align:start position:0%
automatically installed the next time
 

00:02:50.819 --> 00:02:53.449 align:start position:0%
automatically installed the next time
you<00:02:50.940><c> run</c><00:02:51.180><c> the</c><00:02:51.420><c> server</c><00:02:51.660><c> well</c><00:02:52.560><c> now</c><00:02:52.860><c> we'll</c><00:02:53.280><c> show</c>

00:02:53.449 --> 00:02:53.459 align:start position:0%
you run the server well now we'll show
 

00:02:53.459 --> 00:02:56.690 align:start position:0%
you run the server well now we'll show
you<00:02:53.640><c> a</c><00:02:54.000><c> more</c><00:02:54.120><c> subtle</c><00:02:54.480><c> hint</c><00:02:54.920><c> that</c><00:02:55.920><c> an</c><00:02:56.160><c> update</c><00:02:56.459><c> is</c>

00:02:56.690 --> 00:02:56.700 align:start position:0%
you a more subtle hint that an update is
 

00:02:56.700 --> 00:02:58.850 align:start position:0%
you a more subtle hint that an update is
ready<00:02:56.940><c> up</c><00:02:57.540><c> there</c><00:02:57.720><c> in</c><00:02:57.840><c> the</c><00:02:57.959><c> menu</c><00:02:58.080><c> bar</c>

00:02:58.850 --> 00:02:58.860 align:start position:0%
ready up there in the menu bar
 

00:02:58.860 --> 00:03:01.130 align:start position:0%
ready up there in the menu bar
there<00:02:59.580><c> isn't</c><00:02:59.819><c> really</c><00:03:00.060><c> a</c><00:03:00.420><c> difference</c><00:03:00.540><c> in</c><00:03:00.900><c> how</c>

00:03:01.130 --> 00:03:01.140 align:start position:0%
there isn't really a difference in how
 

00:03:01.140 --> 00:03:03.470 align:start position:0%
there isn't really a difference in how
we<00:03:01.260><c> do</c><00:03:01.440><c> the</c><00:03:01.620><c> update</c><00:03:01.920><c> just</c><00:03:02.459><c> how</c><00:03:02.700><c> we</c><00:03:02.940><c> alert</c><00:03:03.300><c> you</c>

00:03:03.470 --> 00:03:03.480 align:start position:0%
we do the update just how we alert you
 

00:03:03.480 --> 00:03:05.330 align:start position:0%
we do the update just how we alert you
this<00:03:03.900><c> should</c><00:03:04.080><c> result</c><00:03:04.260><c> in</c><00:03:04.680><c> a</c><00:03:04.920><c> much</c><00:03:05.099><c> better</c>

00:03:05.330 --> 00:03:05.340 align:start position:0%
this should result in a much better
 

00:03:05.340 --> 00:03:07.910 align:start position:0%
this should result in a much better
overall<00:03:06.180><c> user</c><00:03:06.780><c> experience</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
overall user experience
 

00:03:07.920 --> 00:03:10.250 align:start position:0%
overall user experience
when<00:03:08.400><c> we</c><00:03:08.580><c> first</c><00:03:08.760><c> put</c><00:03:09.000><c> out</c><00:03:09.180><c> olama</c><00:03:09.720><c> and</c><00:03:10.019><c> started</c>

00:03:10.250 --> 00:03:10.260 align:start position:0%
when we first put out olama and started
 

00:03:10.260 --> 00:03:12.229 align:start position:0%
when we first put out olama and started
watching<00:03:10.620><c> the</c><00:03:10.680><c> Discord</c><00:03:11.040><c> we</c><00:03:11.640><c> saw</c><00:03:11.760><c> a</c><00:03:12.000><c> number</c><00:03:12.120><c> of</c>

00:03:12.229 --> 00:03:12.239 align:start position:0%
watching the Discord we saw a number of
 

00:03:12.239 --> 00:03:14.390 align:start position:0%
watching the Discord we saw a number of
folks<00:03:12.540><c> experiencing</c><00:03:13.019><c> crashes</c><00:03:13.560><c> with</c><00:03:13.860><c> Max</c><00:03:14.040><c> with</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
folks experiencing crashes with Max with
 

00:03:14.400 --> 00:03:16.910 align:start position:0%
folks experiencing crashes with Max with
eight<00:03:14.640><c> gigs</c><00:03:15.060><c> of</c><00:03:15.120><c> memory</c><00:03:15.420><c> we</c><00:03:16.200><c> fixed</c><00:03:16.620><c> most</c><00:03:16.800><c> of</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
eight gigs of memory we fixed most of
 

00:03:16.920 --> 00:03:19.369 align:start position:0%
eight gigs of memory we fixed most of
those<00:03:17.040><c> issues</c><00:03:17.459><c> so</c><00:03:17.819><c> that</c><00:03:18.180><c> those</c><00:03:18.420><c> users</c><00:03:18.840><c> should</c>

00:03:19.369 --> 00:03:19.379 align:start position:0%
those issues so that those users should
 

00:03:19.379 --> 00:03:21.649 align:start position:0%
those issues so that those users should
have<00:03:19.560><c> a</c><00:03:19.739><c> better</c><00:03:19.860><c> experience</c><00:03:20.280><c> going</c><00:03:20.580><c> forward</c>

00:03:21.649 --> 00:03:21.659 align:start position:0%
have a better experience going forward
 

00:03:21.659 --> 00:03:23.570 align:start position:0%
have a better experience going forward
we<00:03:22.019><c> also</c><00:03:22.260><c> fixed</c><00:03:22.500><c> some</c><00:03:22.620><c> issues</c><00:03:22.980><c> when</c><00:03:23.159><c> you</c><00:03:23.340><c> had</c>

00:03:23.570 --> 00:03:23.580 align:start position:0%
we also fixed some issues when you had
 

00:03:23.580 --> 00:03:25.490 align:start position:0%
we also fixed some issues when you had
multi-line<00:03:24.180><c> strings</c><00:03:24.659><c> in</c><00:03:24.900><c> the</c><00:03:25.019><c> model</c><00:03:25.140><c> file</c>

00:03:25.490 --> 00:03:25.500 align:start position:0%
multi-line strings in the model file
 

00:03:25.500 --> 00:03:28.009 align:start position:0%
multi-line strings in the model file
such<00:03:25.800><c> as</c><00:03:25.920><c> with</c><00:03:26.400><c> the</c><00:03:26.580><c> system</c><00:03:26.700><c> prompt</c><00:03:27.180><c> so</c><00:03:27.780><c> that's</c>

00:03:28.009 --> 00:03:28.019 align:start position:0%
such as with the system prompt so that's
 

00:03:28.019 --> 00:03:30.410 align:start position:0%
such as with the system prompt so that's
great<00:03:28.260><c> to</c><00:03:28.440><c> see</c><00:03:28.620><c> and</c><00:03:29.580><c> you</c><00:03:29.819><c> know</c><00:03:29.940><c> that's</c><00:03:30.120><c> pretty</c>

00:03:30.410 --> 00:03:30.420 align:start position:0%
great to see and you know that's pretty
 

00:03:30.420 --> 00:03:33.670 align:start position:0%
great to see and you know that's pretty
much<00:03:30.540><c> everything</c><00:03:30.959><c> new</c><00:03:31.379><c> in</c><00:03:31.980><c> version</c>

00:03:33.670 --> 00:03:33.680 align:start position:0%
much everything new in version
 

00:03:33.680 --> 00:03:37.610 align:start position:0%
much everything new in version
0.0.13<00:03:34.680><c> of</c><00:03:35.220><c> olama</c><00:03:35.700><c> I</c><00:03:36.360><c> hope</c><00:03:36.540><c> to</c><00:03:36.659><c> see</c><00:03:36.900><c> you</c><00:03:37.080><c> all</c><00:03:37.319><c> in</c>

00:03:37.610 --> 00:03:37.620 align:start position:0%
0.0.13 of olama I hope to see you all in
 

00:03:37.620 --> 00:03:39.170 align:start position:0%
0.0.13 of olama I hope to see you all in
the<00:03:37.739><c> Discord</c><00:03:38.040><c> now</c><00:03:38.340><c> that</c><00:03:38.519><c> you</c><00:03:38.640><c> know</c><00:03:38.760><c> about</c><00:03:38.940><c> it</c>

00:03:39.170 --> 00:03:39.180 align:start position:0%
the Discord now that you know about it
 

00:03:39.180 --> 00:03:41.690 align:start position:0%
the Discord now that you know about it
and<00:03:39.599><c> I</c><00:03:39.780><c> really</c><00:03:39.959><c> do</c><00:03:40.200><c> want</c><00:03:40.500><c> to</c><00:03:40.680><c> learn</c><00:03:40.860><c> about</c><00:03:41.280><c> how</c>

00:03:41.690 --> 00:03:41.700 align:start position:0%
and I really do want to learn about how
 

00:03:41.700 --> 00:03:47.299 align:start position:0%
and I really do want to learn about how
you<00:03:42.000><c> are</c><00:03:42.239><c> using</c><00:03:42.599><c> olama</c><00:03:43.080><c> until</c><00:03:43.799><c> next</c><00:03:44.159><c> time</c><00:03:44.340><c> bye</c>

