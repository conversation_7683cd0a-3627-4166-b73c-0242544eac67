# Database Schema for YouTube Summaries App

This document outlines the required Supabase database schema for the YouTube Summaries NextJS application.

## Prerequisites

1. Enable the `pgvector` extension in your Supabase database
2. Create the following SQL functions for vector search

## Required Extensions

```sql
-- Enable pgvector extension for vector similarity search
CREATE EXTENSION IF NOT EXISTS vector;
```

## Table Structure

Each topic should have its own table with the following structure:

```sql
-- Example table for a topic (replace 'programming' with your topic name)
CREATE TABLE programming (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    video_id VARCHAR NOT NULL UNIQUE,
    title TEXT NOT NULL,
    channel_name VARCHAR NOT NULL,
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration VARCHAR NOT NULL,
    view_count BIGINT DEFAULT 0,
    transcript TEXT NOT NULL,
    llm_summary TEXT NOT NULL,
    embedding_summary vector(768), -- Adjust dimension based on your embedding model
    topic_category VARCHAR NOT NULL DEFAULT 'programming',
    thumbnail_url TEXT,
    video_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for vector similarity search
CREATE INDEX programming_embedding_idx ON programming 
USING ivfflat (embedding_summary vector_cosine_ops);

-- Create index for video_id lookups
CREATE INDEX programming_video_id_idx ON programming(video_id);

-- Create index for published_at ordering
CREATE INDEX programming_published_at_idx ON programming(published_at DESC);
```

## Required SQL Functions

### 1. Get Topic Tables Function

```sql
CREATE OR REPLACE FUNCTION get_topic_tables()
RETURNS TABLE(
    name TEXT,
    display_name TEXT,
    description TEXT,
    video_count BIGINT
) AS $$
DECLARE
    table_record RECORD;
    table_count BIGINT;
BEGIN
    -- Get all tables that contain video summaries
    FOR table_record IN 
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT IN ('schema_migrations', 'supabase_migrations', 'auth', 'storage')
        AND EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = tablename 
            AND column_name = 'video_id'
        )
    LOOP
        -- Get count for each table
        EXECUTE format('SELECT COUNT(*) FROM %I', table_record.tablename) INTO table_count;
        
        -- Return table info
        name := table_record.tablename;
        display_name := INITCAP(REPLACE(table_record.tablename, '_', ' '));
        description := format('Videos about %s', display_name);
        video_count := table_count;
        
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 2. Vector Search Function

```sql
CREATE OR REPLACE FUNCTION search_videos_by_embedding(
    table_name TEXT,
    query_embedding vector(768), -- Adjust dimension as needed
    match_threshold FLOAT DEFAULT 0.7,
    match_count INT DEFAULT 10
)
RETURNS TABLE(
    id UUID,
    video_id VARCHAR,
    title TEXT,
    channel_name VARCHAR,
    published_at TIMESTAMP WITH TIME ZONE,
    duration VARCHAR,
    view_count BIGINT,
    transcript TEXT,
    llm_summary TEXT,
    topic_category VARCHAR,
    thumbnail_url TEXT,
    video_url TEXT,
    similarity_score FLOAT,
    relevant_chunks TEXT[]
) AS $$
BEGIN
    RETURN QUERY EXECUTE format('
        SELECT 
            t.id,
            t.video_id,
            t.title,
            t.channel_name,
            t.published_at,
            t.duration,
            t.view_count,
            t.transcript,
            t.llm_summary,
            t.topic_category,
            t.thumbnail_url,
            t.video_url,
            1 - (t.embedding_summary <=> $1) as similarity_score,
            ARRAY[substring(t.transcript, 1, 500)] as relevant_chunks
        FROM %I t
        WHERE t.embedding_summary IS NOT NULL
        AND 1 - (t.embedding_summary <=> $1) > $2
        ORDER BY t.embedding_summary <=> $1
        LIMIT $3
    ', table_name)
    USING query_embedding, match_threshold, match_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Row Level Security (RLS)

```sql
-- Enable RLS on each topic table
ALTER TABLE programming ENABLE ROW LEVEL SECURITY;

-- Allow public read access to video summaries
CREATE POLICY "Allow public read access" ON programming
    FOR SELECT USING (true);

-- Restrict write access (adjust based on your authentication needs)
CREATE POLICY "Restrict write access" ON programming
    FOR ALL USING (auth.role() = 'service_role');
```

## Environment Variables Setup

Add these to your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Python AI Service Configuration
PYTHON_AI_SERVICE_URL=http://localhost:9000
PYTHON_AI_SERVICE_API_KEY=your_python_service_api_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:9696
```

## Python Service Integration

You'll need to create a Python FastAPI service that exposes endpoints for:

1. `/chat` - Chat with AI using your existing gemini_methods
2. `/embeddings` - Generate embeddings for search queries

Example Python service structure:

```python
# main.py
from fastapi import FastAPI
from your_existing_gemini_methods.genai_utils import YourAIClass

app = FastAPI()

@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    # Use your existing AI utilities
    response = await your_ai_instance.generate_response(
        message=request.message,
        context=request.context
    )
    return {"response": response}

@app.post("/embeddings")
async def embeddings_endpoint(request: EmbeddingRequest):
    # Generate embeddings using your existing methods
    embedding = await your_ai_instance.generate_embedding(request.text)
    return {"embedding": embedding}
```

## Next Steps

1. Set up your Supabase database with the above schema
2. Create your topic tables using the provided structure
3. Set up the Python AI service to work with your existing gemini_methods
4. Populate your environment variables
5. Run the NextJS application

The application will automatically detect your topic tables and provide search and chat functionality across all your YouTube video summaries.
