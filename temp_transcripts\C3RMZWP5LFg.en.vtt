WEBVTT
Kind: captions
Language: en

00:00:06.359 --> 00:00:08.830 align:start position:0%
 
you<00:00:06.520><c> can</c><00:00:06.720><c> use</c><00:00:06.960><c> the</c><00:00:07.120><c> zaps</c><00:00:07.879><c> website</c><00:00:08.360><c> to</c><00:00:08.559><c> find</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
you can use the zaps website to find
 

00:00:08.840 --> 00:00:11.150 align:start position:0%
you can use the zaps website to find
models<00:00:09.360><c> that</c><00:00:09.519><c> can</c><00:00:09.639><c> be</c><00:00:09.840><c> deployed</c><00:00:10.400><c> as</c><00:00:10.519><c> a</c><00:00:10.759><c> private</c>

00:00:11.150 --> 00:00:11.160 align:start position:0%
models that can be deployed as a private
 

00:00:11.160 --> 00:00:18.590 align:start position:0%
models that can be deployed as a private
API<00:00:11.559><c> endpoint</c><00:00:12.040><c> on</c><00:00:12.280><c> data</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
 
 

00:00:18.600 --> 00:00:22.509 align:start position:0%
 
brids<00:00:19.600><c> today</c><00:00:20.320><c> I'm</c><00:00:20.560><c> going</c><00:00:20.720><c> to</c><00:00:21.000><c> deploy</c><00:00:21.680><c> a</c><00:00:21.880><c> model</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
brids today I'm going to deploy a model
 

00:00:22.519 --> 00:00:24.670 align:start position:0%
brids today I'm going to deploy a model
that<00:00:22.720><c> detects</c><00:00:23.119><c> oncology</c><00:00:23.720><c> specific</c><00:00:24.199><c> entities</c>

00:00:24.670 --> 00:00:24.680 align:start position:0%
that detects oncology specific entities
 

00:00:24.680 --> 00:00:28.509 align:start position:0%
that detects oncology specific entities
on<00:00:24.880><c> data</c><00:00:25.160><c> bricks</c><00:00:25.599><c> as</c><00:00:25.720><c> a</c><00:00:25.920><c> private</c><00:00:26.359><c> API</c>

00:00:28.509 --> 00:00:28.519 align:start position:0%
on data bricks as a private API
 

00:00:28.519 --> 00:00:30.710 align:start position:0%
on data bricks as a private API
endpoint<00:00:29.519><c> this</c><00:00:29.720><c> small</c><00:00:30.000><c> model</c><00:00:30.279><c> extract</c>

00:00:30.710 --> 00:00:30.720 align:start position:0%
endpoint this small model extract
 

00:00:30.720 --> 00:00:33.350 align:start position:0%
endpoint this small model extract
oncological<00:00:31.480><c> entities</c><00:00:32.040><c> and</c><00:00:32.239><c> relations</c><00:00:33.040><c> was</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
oncological entities and relations was
 

00:00:33.360 --> 00:00:36.430 align:start position:0%
oncological entities and relations was
designed<00:00:33.920><c> to</c><00:00:34.239><c> detect</c><00:00:34.640><c> and</c><00:00:34.879><c> classify</c><00:00:35.800><c> a</c><00:00:36.079><c> wide</c>

00:00:36.430 --> 00:00:36.440 align:start position:0%
designed to detect and classify a wide
 

00:00:36.440 --> 00:00:38.869 align:start position:0%
designed to detect and classify a wide
spectrum<00:00:36.920><c> of</c><00:00:37.079><c> oncological</c><00:00:37.680><c> entities</c><00:00:38.520><c> ranging</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
spectrum of oncological entities ranging
 

00:00:38.879 --> 00:00:41.430 align:start position:0%
spectrum of oncological entities ranging
from<00:00:39.160><c> adenopathy</c><00:00:40.160><c> biomarkers</c><00:00:41.079><c> cancer</c>

00:00:41.430 --> 00:00:41.440 align:start position:0%
from adenopathy biomarkers cancer
 

00:00:41.440 --> 00:00:50.189 align:start position:0%
from adenopathy biomarkers cancer
diagnosis<00:00:42.399><c> hormonal</c><00:00:42.879><c> therapy</c>

00:00:50.189 --> 00:00:50.199 align:start position:0%
 
 

00:00:50.199 --> 00:00:52.910 align:start position:0%
 
Etc<00:00:51.199><c> to</c><00:00:51.399><c> start</c><00:00:51.760><c> the</c><00:00:51.879><c> deployment</c><00:00:52.440><c> process</c>

00:00:52.910 --> 00:00:52.920 align:start position:0%
Etc to start the deployment process
 

00:00:52.920 --> 00:00:54.950 align:start position:0%
Etc to start the deployment process
click<00:00:53.160><c> on</c><00:00:53.320><c> the</c><00:00:53.480><c> deploy</c><00:00:53.879><c> to</c><00:00:54.079><c> database</c><00:00:54.559><c> button</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
click on the deploy to database button
 

00:00:54.960 --> 00:01:01.270 align:start position:0%
click on the deploy to database button
on<00:00:55.120><c> the</c><00:00:55.239><c> model</c><00:00:55.680><c> card</c><00:00:56.120><c> page</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
 
 

00:01:01.280 --> 00:01:04.149 align:start position:0%
 
this<00:01:01.600><c> opens</c><00:01:02.079><c> up</c><00:01:02.399><c> the</c><00:01:02.680><c> datab</c><00:01:03.440><c> marketplace</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
this opens up the datab marketplace
 

00:01:04.159 --> 00:01:07.670 align:start position:0%
this opens up the datab marketplace
listing<00:01:04.640><c> page</c><00:01:05.000><c> of</c><00:01:05.199><c> the</c>

00:01:07.670 --> 00:01:07.680 align:start position:0%
 
 

00:01:07.680 --> 00:01:10.469 align:start position:0%
 
model<00:01:08.680><c> you</c><00:01:08.920><c> need</c><00:01:09.200><c> to</c><00:01:09.400><c> be</c><00:01:09.600><c> logged</c><00:01:10.000><c> into</c><00:01:10.320><c> a</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
model you need to be logged into a
 

00:01:10.479 --> 00:01:20.350 align:start position:0%
model you need to be logged into a
databas<00:01:11.200><c> account</c><00:01:11.840><c> to</c><00:01:12.080><c> get</c><00:01:12.400><c> access</c><00:01:12.720><c> to</c><00:01:12.920><c> the</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
 
 

00:01:20.360 --> 00:01:22.870 align:start position:0%
 
listing<00:01:21.360><c> on</c><00:01:21.600><c> the</c><00:01:21.759><c> listing</c><00:01:22.200><c> page</c><00:01:22.520><c> you</c><00:01:22.640><c> will</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
listing on the listing page you will
 

00:01:22.880 --> 00:01:25.109 align:start position:0%
listing on the listing page you will
find<00:01:23.159><c> detail</c><00:01:23.560><c> instruction</c><00:01:24.079><c> about</c><00:01:24.320><c> the</c><00:01:24.439><c> model</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
find detail instruction about the model
 

00:01:25.119 --> 00:01:32.950 align:start position:0%
find detail instruction about the model
and<00:01:25.320><c> also</c><00:01:25.680><c> how</c><00:01:25.880><c> to</c><00:01:26.079><c> use</c><00:01:26.320><c> the</c><00:01:26.439><c> model</c><00:01:26.759><c> endpoint</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
 
 

00:01:32.960 --> 00:01:35.350 align:start position:0%
 
next<00:01:33.520><c> click</c><00:01:33.759><c> on</c><00:01:33.960><c> the</c><00:01:34.119><c> get</c><00:01:34.439><c> instance</c><00:01:34.960><c> access</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
next click on the get instance access
 

00:01:35.360 --> 00:01:38.030 align:start position:0%
next click on the get instance access
button<00:01:36.200><c> located</c><00:01:36.720><c> at</c><00:01:36.880><c> the</c><00:01:37.079><c> top</c><00:01:37.360><c> right</c><00:01:37.560><c> corner</c>

00:01:38.030 --> 00:01:38.040 align:start position:0%
button located at the top right corner
 

00:01:38.040 --> 00:01:39.710 align:start position:0%
button located at the top right corner
of<00:01:38.280><c> the</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
of the
 

00:01:39.720 --> 00:01:43.710 align:start position:0%
of the
page<00:01:40.720><c> a</c><00:01:40.960><c> popup</c><00:01:41.560><c> will</c><00:01:41.799><c> show</c><00:01:42.200><c> up</c><00:01:43.040><c> that</c><00:01:43.240><c> displays</c>

00:01:43.710 --> 00:01:43.720 align:start position:0%
page a popup will show up that displays
 

00:01:43.720 --> 00:01:48.149 align:start position:0%
page a popup will show up that displays
the<00:01:43.880><c> model</c><00:01:44.320><c> available</c><00:01:44.880><c> within</c><00:01:45.200><c> the</c>

00:01:48.149 --> 00:01:48.159 align:start position:0%
 
 

00:01:48.159 --> 00:01:50.910 align:start position:0%
 
listing<00:01:49.159><c> after</c><00:01:49.560><c> accepting</c><00:01:50.040><c> the</c><00:01:50.200><c> terms</c><00:01:50.520><c> and</c>

00:01:50.910 --> 00:01:50.920 align:start position:0%
listing after accepting the terms and
 

00:01:50.920 --> 00:01:53.510 align:start position:0%
listing after accepting the terms and
condition<00:01:51.719><c> click</c><00:01:52.000><c> the</c><00:01:52.200><c> get</c><00:01:52.439><c> instant</c><00:01:52.920><c> access</c>

00:01:53.510 --> 00:01:53.520 align:start position:0%
condition click the get instant access
 

00:01:53.520 --> 00:02:00.270 align:start position:0%
condition click the get instant access
button<00:01:54.040><c> available</c><00:01:54.520><c> on</c><00:01:54.680><c> the</c><00:01:54.880><c> popup</c>

00:02:00.270 --> 00:02:00.280 align:start position:0%
 
 

00:02:00.280 --> 00:02:02.270 align:start position:0%
 
you<00:02:00.520><c> now</c><00:02:00.799><c> have</c><00:02:01.039><c> access</c><00:02:01.320><c> to</c><00:02:01.479><c> the</c><00:02:01.600><c> model</c><00:02:01.920><c> on</c><00:02:02.079><c> your</c>

00:02:02.270 --> 00:02:02.280 align:start position:0%
you now have access to the model on your
 

00:02:02.280 --> 00:02:04.830 align:start position:0%
you now have access to the model on your
databas<00:02:02.880><c> account</c><00:02:03.799><c> you</c><00:02:03.920><c> can</c><00:02:04.119><c> start</c><00:02:04.439><c> creating</c>

00:02:04.830 --> 00:02:04.840 align:start position:0%
databas account you can start creating
 

00:02:04.840 --> 00:02:10.390 align:start position:0%
databas account you can start creating
endpoint<00:02:05.320><c> from</c>

00:02:10.390 --> 00:02:10.400 align:start position:0%
 
 

00:02:10.400 --> 00:02:16.990 align:start position:0%
 
it<00:02:11.400><c> the</c><00:02:11.560><c> model</c><00:02:11.920><c> is</c><00:02:12.120><c> located</c><00:02:12.520><c> on</c><00:02:12.720><c> a</c><00:02:12.920><c> Delta</c><00:02:13.239><c> shell</c>

00:02:16.990 --> 00:02:17.000 align:start position:0%
 
 

00:02:17.000 --> 00:02:19.750 align:start position:0%
 
catalog<00:02:18.000><c> new</c><00:02:18.280><c> model</c><00:02:18.640><c> versions</c><00:02:19.120><c> publish</c><00:02:19.599><c> will</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
catalog new model versions publish will
 

00:02:19.760 --> 00:02:30.270 align:start position:0%
catalog new model versions publish will
be<00:02:19.959><c> immediately</c><00:02:20.560><c> available</c><00:02:21.040><c> to</c><00:02:21.200><c> you</c>

00:02:30.270 --> 00:02:30.280 align:start position:0%
 
 

00:02:30.280 --> 00:02:32.670 align:start position:0%
 
in<00:02:30.440><c> order</c><00:02:30.800><c> to</c><00:02:31.000><c> start</c><00:02:31.360><c> using</c><00:02:31.720><c> the</c><00:02:31.879><c> model</c><00:02:32.280><c> as</c><00:02:32.400><c> a</c>

00:02:32.670 --> 00:02:32.680 align:start position:0%
in order to start using the model as a
 

00:02:32.680 --> 00:02:36.470 align:start position:0%
in order to start using the model as a
API<00:02:33.560><c> endpoint</c><00:02:34.560><c> go</c><00:02:34.800><c> back</c><00:02:34.959><c> to</c><00:02:35.120><c> the</c><00:02:35.239><c> listing</c><00:02:35.720><c> page</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
API endpoint go back to the listing page
 

00:02:36.480 --> 00:02:39.750 align:start position:0%
API endpoint go back to the listing page
and<00:02:36.680><c> click</c><00:02:36.920><c> on</c><00:02:37.080><c> the</c><00:02:37.239><c> preview</c><00:02:37.599><c> notebook</c>

00:02:39.750 --> 00:02:39.760 align:start position:0%
and click on the preview notebook
 

00:02:39.760 --> 00:02:42.550 align:start position:0%
and click on the preview notebook
button<00:02:40.760><c> every</c><00:02:41.080><c> medical</c><00:02:41.519><c> language</c><00:02:42.000><c> model</c><00:02:42.319><c> on</c>

00:02:42.550 --> 00:02:42.560 align:start position:0%
button every medical language model on
 

00:02:42.560 --> 00:02:45.309 align:start position:0%
button every medical language model on
datab<00:02:42.840><c> breaks</c><00:02:43.280><c> comes</c><00:02:43.720><c> with</c><00:02:43.959><c> a</c><00:02:44.200><c> notebook</c><00:02:45.159><c> that</c>

00:02:45.309 --> 00:02:45.319 align:start position:0%
datab breaks comes with a notebook that
 

00:02:45.319 --> 00:02:48.309 align:start position:0%
datab breaks comes with a notebook that
you<00:02:45.440><c> can</c><00:02:45.720><c> run</c><00:02:46.720><c> to</c><00:02:46.959><c> deploy</c><00:02:47.360><c> The</c><00:02:47.519><c> Notebook</c><00:02:47.959><c> as</c><00:02:48.120><c> an</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
you can run to deploy The Notebook as an
 

00:02:48.319 --> 00:02:52.110 align:start position:0%
you can run to deploy The Notebook as an
serving<00:02:48.800><c> API</c><00:02:49.200><c> endpoint</c><00:02:50.120><c> and</c><00:02:50.319><c> also</c><00:02:50.640><c> query</c>

00:02:52.110 --> 00:02:52.120 align:start position:0%
serving API endpoint and also query
 

00:02:52.120 --> 00:02:55.350 align:start position:0%
serving API endpoint and also query
it<00:02:53.120><c> in</c><00:02:53.280><c> order</c><00:02:53.519><c> to</c><00:02:53.680><c> start</c><00:02:54.080><c> using</c><00:02:54.400><c> the</c><00:02:54.560><c> notebook</c>

00:02:55.350 --> 00:02:55.360 align:start position:0%
it in order to start using the notebook
 

00:02:55.360 --> 00:03:01.990 align:start position:0%
it in order to start using the notebook
click<00:02:55.599><c> on</c><00:02:55.800><c> the</c><00:02:55.959><c> import</c><00:02:56.319><c> notebook</c><00:02:56.800><c> button</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
 
 

00:03:02.000 --> 00:03:03.910 align:start position:0%
 
this<00:03:02.159><c> will</c><00:03:02.480><c> import</c><00:03:02.920><c> your</c><00:03:03.159><c> notebook</c><00:03:03.560><c> to</c><00:03:03.720><c> your</c>

00:03:03.910 --> 00:03:03.920 align:start position:0%
this will import your notebook to your
 

00:03:03.920 --> 00:03:06.869 align:start position:0%
this will import your notebook to your
databas<00:03:04.799><c> workspace</c><00:03:05.799><c> and</c><00:03:06.000><c> you</c><00:03:06.120><c> can</c><00:03:06.360><c> use</c><00:03:06.680><c> a</c>

00:03:06.869 --> 00:03:06.879 align:start position:0%
databas workspace and you can use a
 

00:03:06.879 --> 00:03:08.630 align:start position:0%
databas workspace and you can use a
cluster<00:03:07.280><c> to</c><00:03:07.440><c> run</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
cluster to run
 

00:03:08.640 --> 00:03:11.350 align:start position:0%
cluster to run
it<00:03:09.640><c> there</c><00:03:09.879><c> are</c><00:03:10.120><c> some</c><00:03:10.440><c> required</c><00:03:10.959><c> input</c>

00:03:11.350 --> 00:03:11.360 align:start position:0%
it there are some required input
 

00:03:11.360 --> 00:03:13.390 align:start position:0%
it there are some required input
parameters<00:03:12.000><c> for</c><00:03:12.239><c> running</c><00:03:12.599><c> the</c>

00:03:13.390 --> 00:03:13.400 align:start position:0%
parameters for running the
 

00:03:13.400 --> 00:03:15.869 align:start position:0%
parameters for running the
notebook<00:03:14.400><c> the</c><00:03:14.560><c> databas</c><00:03:15.159><c> access</c><00:03:15.440><c> token</c><00:03:15.680><c> and</c>

00:03:15.869 --> 00:03:15.879 align:start position:0%
notebook the databas access token and
 

00:03:15.879 --> 00:03:18.830 align:start position:0%
notebook the databas access token and
databas<00:03:16.360><c> host</c><00:03:16.760><c> are</c><00:03:17.000><c> required</c><00:03:17.560><c> to</c><00:03:18.280><c> create</c><00:03:18.680><c> the</c>

00:03:18.830 --> 00:03:18.840 align:start position:0%
databas host are required to create the
 

00:03:18.840 --> 00:03:20.789 align:start position:0%
databas host are required to create the
endpoint<00:03:19.400><c> programmatically</c><00:03:20.239><c> from</c><00:03:20.480><c> within</c>

00:03:20.789 --> 00:03:20.799 align:start position:0%
endpoint programmatically from within
 

00:03:20.799 --> 00:03:25.789 align:start position:0%
endpoint programmatically from within
the

00:03:25.789 --> 00:03:25.799 align:start position:0%
 
 

00:03:25.799 --> 00:03:28.470 align:start position:0%
 
notebook<00:03:26.799><c> in</c><00:03:26.959><c> order</c><00:03:27.280><c> to</c><00:03:27.480><c> get</c><00:03:27.640><c> a</c><00:03:27.840><c> database</c>

00:03:28.470 --> 00:03:28.480 align:start position:0%
notebook in order to get a database
 

00:03:28.480 --> 00:03:31.229 align:start position:0%
notebook in order to get a database
token<00:03:29.480><c> first</c><00:03:29.920><c> ly</c><00:03:30.080><c> go</c><00:03:30.200><c> to</c><00:03:30.360><c> your</c><00:03:30.560><c> settings</c><00:03:31.000><c> page</c>

00:03:31.229 --> 00:03:31.239 align:start position:0%
token first ly go to your settings page
 

00:03:31.239 --> 00:03:34.190 align:start position:0%
token first ly go to your settings page
on<00:03:31.439><c> data</c><00:03:32.120><c> brakes</c><00:03:33.120><c> from</c><00:03:33.439><c> there</c><00:03:33.640><c> click</c><00:03:33.840><c> on</c><00:03:34.000><c> the</c>

00:03:34.190 --> 00:03:34.200 align:start position:0%
on data brakes from there click on the
 

00:03:34.200 --> 00:03:36.070 align:start position:0%
on data brakes from there click on the
developer

00:03:36.070 --> 00:03:36.080 align:start position:0%
developer
 

00:03:36.080 --> 00:03:41.149 align:start position:0%
developer
menu<00:03:37.080><c> you</c><00:03:37.200><c> can</c><00:03:37.400><c> generate</c><00:03:37.879><c> a</c><00:03:38.080><c> token</c><00:03:38.560><c> from</c>

00:03:41.149 --> 00:03:41.159 align:start position:0%
 
 

00:03:41.159 --> 00:03:43.670 align:start position:0%
 
there<00:03:42.159><c> once</c><00:03:42.400><c> the</c><00:03:42.560><c> databas</c><00:03:43.120><c> token</c><00:03:43.439><c> is</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
there once the databas token is
 

00:03:43.680 --> 00:03:46.229 align:start position:0%
there once the databas token is
generated<00:03:44.680><c> copy</c><00:03:45.000><c> it</c><00:03:45.280><c> and</c><00:03:45.480><c> paste</c><00:03:45.799><c> it</c><00:03:45.959><c> in</c><00:03:46.080><c> the</c>

00:03:46.229 --> 00:03:46.239 align:start position:0%
generated copy it and paste it in the
 

00:03:46.239 --> 00:03:52.830 align:start position:0%
generated copy it and paste it in the
database<00:03:46.879><c> access</c><00:03:47.239><c> token</c><00:03:48.120><c> input</c><00:03:48.560><c> on</c><00:03:48.760><c> the</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
 
 

00:03:52.840 --> 00:03:55.550 align:start position:0%
 
notebook<00:03:53.840><c> the</c><00:03:53.959><c> datab</c><00:03:54.239><c> bricks</c><00:03:54.640><c> host</c><00:03:54.920><c> URL</c><00:03:55.400><c> can</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
notebook the datab bricks host URL can
 

00:03:55.560 --> 00:04:04.509 align:start position:0%
notebook the datab bricks host URL can
be<00:03:55.799><c> copied</c><00:03:56.519><c> from</c><00:03:56.799><c> the</c><00:03:57.000><c> URL</c><00:03:57.519><c> itself</c>

00:04:04.509 --> 00:04:04.519 align:start position:0%
 
 

00:04:04.519 --> 00:04:07.830 align:start position:0%
 
the<00:04:04.720><c> JSL</c><00:04:05.239><c> license</c><00:04:05.680><c> is</c><00:04:05.920><c> required</c><00:04:06.680><c> because</c><00:04:07.120><c> this</c>

00:04:07.830 --> 00:04:07.840 align:start position:0%
the JSL license is required because this
 

00:04:07.840 --> 00:04:10.390 align:start position:0%
the JSL license is required because this
model<00:04:08.319><c> on</c><00:04:08.680><c> data</c><00:04:09.000><c> bricks</c><00:04:09.360><c> runs</c><00:04:09.680><c> on</c><00:04:09.879><c> a</c><00:04:10.079><c> bring</c>

00:04:10.390 --> 00:04:10.400 align:start position:0%
model on data bricks runs on a bring
 

00:04:10.400 --> 00:04:15.030 align:start position:0%
model on data bricks runs on a bring
your<00:04:10.599><c> own</c><00:04:10.879><c> license</c><00:04:11.760><c> pricing</c>

00:04:15.030 --> 00:04:15.040 align:start position:0%
 
 

00:04:15.040 --> 00:04:18.830 align:start position:0%
 
policy<00:04:16.040><c> in</c><00:04:16.239><c> order</c><00:04:16.519><c> to</c><00:04:16.799><c> get</c><00:04:16.959><c> a</c><00:04:17.160><c> JSL</c>

00:04:18.830 --> 00:04:18.840 align:start position:0%
policy in order to get a JSL
 

00:04:18.840 --> 00:04:24.390 align:start position:0%
policy in order to get a JSL
license<00:04:19.840><c> firstly</c><00:04:20.680><c> go</c><00:04:20.880><c> to</c><00:04:21.160><c> my</c><00:04:21.479><c> joh.com</c>

00:04:24.390 --> 00:04:24.400 align:start position:0%
license firstly go to my joh.com
 

00:04:24.400 --> 00:04:27.550 align:start position:0%
license firstly go to my joh.com
if<00:04:25.400><c> you</c><00:04:25.600><c> don't</c><00:04:25.960><c> have</c><00:04:26.160><c> an</c><00:04:26.440><c> account</c><00:04:27.120><c> please</c>

00:04:27.550 --> 00:04:27.560 align:start position:0%
if you don't have an account please
 

00:04:27.560 --> 00:04:32.629 align:start position:0%
if you don't have an account please
create<00:04:27.960><c> it</c><00:04:28.320><c> with</c><00:04:28.520><c> the</c><00:04:28.759><c> create</c><00:04:29.199><c> account</c><00:04:29.479><c> button</c>

00:04:32.629 --> 00:04:32.639 align:start position:0%
 
 

00:04:32.639 --> 00:04:36.510 align:start position:0%
 
button<00:04:33.639><c> next</c><00:04:34.320><c> fill</c><00:04:34.600><c> up</c><00:04:34.800><c> the</c><00:04:34.960><c> sign</c><00:04:35.160><c> up</c><00:04:35.360><c> form</c><00:04:36.280><c> and</c>

00:04:36.510 --> 00:04:36.520 align:start position:0%
button next fill up the sign up form and
 

00:04:36.520 --> 00:04:44.870 align:start position:0%
button next fill up the sign up form and
click<00:04:36.720><c> on</c><00:04:36.919><c> the</c><00:04:37.120><c> create</c><00:04:37.520><c> account</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
 
 

00:04:44.880 --> 00:04:47.110 align:start position:0%
 
button<00:04:45.880><c> follow</c><00:04:46.320><c> the</c><00:04:46.520><c> rest</c><00:04:46.759><c> of</c><00:04:46.919><c> the</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
button follow the rest of the
 

00:04:47.120 --> 00:04:52.070 align:start position:0%
button follow the rest of the
instructions<00:04:47.720><c> to</c><00:04:48.000><c> get</c><00:04:48.240><c> your</c><00:04:48.520><c> account</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
 
 

00:04:52.080 --> 00:04:55.909 align:start position:0%
 
activated<00:04:53.080><c> once</c><00:04:53.440><c> your</c><00:04:53.919><c> my</c><00:04:54.160><c> J</c><00:04:54.600><c> Labs</c><00:04:55.000><c> account</c><00:04:55.360><c> is</c>

00:04:55.909 --> 00:04:55.919 align:start position:0%
activated once your my J Labs account is
 

00:04:55.919 --> 00:04:59.469 align:start position:0%
activated once your my J Labs account is
activated<00:04:56.919><c> you</c><00:04:57.120><c> can</c><00:04:57.400><c> now</c><00:04:57.880><c> buy</c><00:04:58.520><c> a</c><00:04:58.800><c> p</c><00:04:59.000><c> as</c><00:04:59.160><c> you</c><00:04:59.320><c> go</c>

00:04:59.469 --> 00:04:59.479 align:start position:0%
activated you can now buy a p as you go
 

00:04:59.479 --> 00:05:02.830 align:start position:0%
activated you can now buy a p as you go
live<00:04:59.720><c> license</c><00:05:00.199><c> from</c><00:05:00.479><c> your</c>

00:05:02.830 --> 00:05:02.840 align:start position:0%
 
 

00:05:02.840 --> 00:05:06.990 align:start position:0%
 
account<00:05:03.840><c> from</c><00:05:04.160><c> the</c><00:05:04.320><c> buyer</c><00:05:04.759><c> license</c><00:05:05.720><c> page</c><00:05:06.720><c> fill</c>

00:05:06.990 --> 00:05:07.000 align:start position:0%
account from the buyer license page fill
 

00:05:07.000 --> 00:05:08.950 align:start position:0%
account from the buyer license page fill
up<00:05:07.240><c> the</c><00:05:07.400><c> payment</c><00:05:07.880><c> method</c><00:05:08.240><c> with</c><00:05:08.400><c> your</c><00:05:08.639><c> credit</c>

00:05:08.950 --> 00:05:08.960 align:start position:0%
up the payment method with your credit
 

00:05:08.960 --> 00:05:11.430 align:start position:0%
up the payment method with your credit
card

00:05:11.430 --> 00:05:11.440 align:start position:0%
 
 

00:05:11.440 --> 00:05:15.310 align:start position:0%
 
information<00:05:12.440><c> opting</c><00:05:12.919><c> for</c><00:05:13.080><c> a</c><00:05:13.240><c> tri</c><00:05:13.639><c> license</c><00:05:14.160><c> if</c>

00:05:15.310 --> 00:05:15.320 align:start position:0%
information opting for a tri license if
 

00:05:15.320 --> 00:05:17.710 align:start position:0%
information opting for a tri license if
available<00:05:16.320><c> agree</c><00:05:16.639><c> to</c><00:05:16.759><c> the</c><00:05:16.919><c> terms</c><00:05:17.280><c> and</c>

00:05:17.710 --> 00:05:17.720 align:start position:0%
available agree to the terms and
 

00:05:17.720 --> 00:05:19.430 align:start position:0%
available agree to the terms and
condition<00:05:18.120><c> and</c><00:05:18.360><c> click</c><00:05:18.639><c> on</c><00:05:18.880><c> the</c><00:05:19.080><c> create</c>

00:05:19.430 --> 00:05:19.440 align:start position:0%
condition and click on the create
 

00:05:19.440 --> 00:05:21.590 align:start position:0%
condition and click on the create
license

00:05:21.590 --> 00:05:21.600 align:start position:0%
license
 

00:05:21.600 --> 00:05:24.430 align:start position:0%
license
button<00:05:22.600><c> following</c><00:05:23.199><c> this</c><00:05:23.479><c> a</c><00:05:23.720><c> license</c><00:05:24.120><c> will</c><00:05:24.280><c> be</c>

00:05:24.430 --> 00:05:24.440 align:start position:0%
button following this a license will be
 

00:05:24.440 --> 00:05:26.590 align:start position:0%
button following this a license will be
generated<00:05:24.960><c> for</c><00:05:25.280><c> you</c><00:05:25.800><c> and</c><00:05:25.960><c> it</c><00:05:26.080><c> will</c><00:05:26.280><c> be</c>

00:05:26.590 --> 00:05:26.600 align:start position:0%
generated for you and it will be
 

00:05:26.600 --> 00:05:33.469 align:start position:0%
generated for you and it will be
available<00:05:27.600><c> VI</c><00:05:27.919><c> the</c><00:05:28.080><c> my</c><00:05:28.360><c> licenses</c><00:05:29.039><c> page</c>

00:05:33.469 --> 00:05:33.479 align:start position:0%
 
 

00:05:33.479 --> 00:05:36.749 align:start position:0%
 
next<00:05:34.360><c> copy</c><00:05:34.720><c> the</c><00:05:34.880><c> license</c><00:05:35.360><c> key</c><00:05:35.680><c> from</c><00:05:36.479><c> my</c>

00:05:36.749 --> 00:05:36.759 align:start position:0%
next copy the license key from my
 

00:05:36.759 --> 00:05:47.469 align:start position:0%
next copy the license key from my
licenses<00:05:37.360><c> page</c><00:05:37.840><c> and</c><00:05:38.000><c> use</c><00:05:38.280><c> it</c><00:05:38.520><c> on</c><00:05:38.840><c> the</c><00:05:39.440><c> data</c>

00:05:47.469 --> 00:05:47.479 align:start position:0%
 
 

00:05:47.479 --> 00:05:50.230 align:start position:0%
 
notebook<00:05:48.479><c> the</c><00:05:48.680><c> hardware</c><00:05:49.120><c> Target</c><00:05:49.560><c> can</c><00:05:49.759><c> be</c><00:05:49.919><c> used</c>

00:05:50.230 --> 00:05:50.240 align:start position:0%
notebook the hardware Target can be used
 

00:05:50.240 --> 00:05:52.950 align:start position:0%
notebook the hardware Target can be used
to<00:05:50.400><c> deploy</c><00:05:50.800><c> the</c><00:05:50.919><c> endpoint</c><00:05:51.400><c> to</c><00:05:51.639><c> either</c><00:05:51.919><c> a</c><00:05:52.120><c> CPU</c>

00:05:52.950 --> 00:05:52.960 align:start position:0%
to deploy the endpoint to either a CPU
 

00:05:52.960 --> 00:05:56.469 align:start position:0%
to deploy the endpoint to either a CPU
or<00:05:53.199><c> GPU</c>

00:05:56.469 --> 00:05:56.479 align:start position:0%
 
 

00:05:56.479 --> 00:05:59.350 align:start position:0%
 
machine<00:05:57.479><c> next</c><00:05:58.000><c> click</c><00:05:58.240><c> on</c><00:05:58.440><c> the</c><00:05:58.600><c> run</c><00:05:58.800><c> all</c><00:05:59.080><c> button</c>

00:05:59.350 --> 00:05:59.360 align:start position:0%
machine next click on the run all button
 

00:05:59.360 --> 00:06:03.230 align:start position:0%
machine next click on the run all button
to<00:05:59.720><c> run</c><00:05:59.919><c> the</c>

00:06:03.230 --> 00:06:03.240 align:start position:0%
 
 

00:06:03.240 --> 00:06:06.710 align:start position:0%
 
notebook<00:06:04.240><c> The</c><00:06:04.440><c> Notebook</c><00:06:05.160><c> installs</c><00:06:05.720><c> some</c>

00:06:06.710 --> 00:06:06.720 align:start position:0%
notebook The Notebook installs some
 

00:06:06.720 --> 00:06:09.150 align:start position:0%
notebook The Notebook installs some
dependencies<00:06:07.720><c> creates</c><00:06:08.080><c> a</c><00:06:08.280><c> input</c><00:06:08.680><c> medical</c>

00:06:09.150 --> 00:06:09.160 align:start position:0%
dependencies creates a input medical
 

00:06:09.160 --> 00:06:10.950 align:start position:0%
dependencies creates a input medical
Text

00:06:10.950 --> 00:06:10.960 align:start position:0%
Text
 

00:06:10.960 --> 00:06:14.510 align:start position:0%
Text
data<00:06:11.960><c> and</c><00:06:12.160><c> deploys</c><00:06:12.680><c> the</c><00:06:12.800><c> model</c><00:06:13.280><c> to</c><00:06:13.520><c> an</c><00:06:14.080><c> private</c>

00:06:14.510 --> 00:06:14.520 align:start position:0%
data and deploys the model to an private
 

00:06:14.520 --> 00:06:18.870 align:start position:0%
data and deploys the model to an private
API<00:06:14.960><c> endpoint</c><00:06:15.800><c> via</c><00:06:16.120><c> the</c><00:06:16.240><c> deploy</c>

00:06:18.870 --> 00:06:18.880 align:start position:0%
 
 

00:06:18.880 --> 00:06:21.589 align:start position:0%
 
function<00:06:19.880><c> it</c><00:06:20.120><c> might</c><00:06:20.479><c> take</c><00:06:20.759><c> few</c><00:06:21.000><c> minutes</c><00:06:21.360><c> for</c>

00:06:21.589 --> 00:06:21.599 align:start position:0%
function it might take few minutes for
 

00:06:21.599 --> 00:06:27.909 align:start position:0%
function it might take few minutes for
the<00:06:21.800><c> API</c><00:06:22.280><c> endpoint</c><00:06:22.800><c> to</c>

00:06:27.909 --> 00:06:27.919 align:start position:0%
 
 

00:06:27.919 --> 00:06:31.790 align:start position:0%
 
start<00:06:28.919><c> once</c><00:06:29.240><c> the</c><00:06:29.800><c> Point</c><00:06:30.039><c> starts</c><00:06:30.800><c> successfully</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
start once the Point starts successfully
 

00:06:31.800 --> 00:06:38.029 align:start position:0%
start once the Point starts successfully
you<00:06:31.919><c> can</c><00:06:32.199><c> start</c><00:06:32.639><c> sending</c><00:06:33.120><c> request</c><00:06:33.560><c> to</c>

00:06:38.029 --> 00:06:38.039 align:start position:0%
 
 

00:06:38.039 --> 00:06:40.909 align:start position:0%
 
it<00:06:39.039><c> in</c><00:06:39.199><c> order</c><00:06:39.560><c> to</c><00:06:39.759><c> make</c><00:06:40.080><c> requests</c><00:06:40.520><c> to</c><00:06:40.680><c> the</c>

00:06:40.909 --> 00:06:40.919 align:start position:0%
it in order to make requests to the
 

00:06:40.919 --> 00:06:44.670 align:start position:0%
it in order to make requests to the
private<00:06:41.319><c> API</c><00:06:41.880><c> endpoints</c><00:06:42.880><c> you</c><00:06:43.039><c> can</c><00:06:43.240><c> use</c><00:06:43.880><c> NLP</c><00:06:44.479><c> do</c>

00:06:44.670 --> 00:06:44.680 align:start position:0%
private API endpoints you can use NLP do
 

00:06:44.680 --> 00:06:47.629 align:start position:0%
private API endpoints you can use NLP do
query<00:06:44.960><c> endpoint</c><00:06:45.840><c> function</c><00:06:46.840><c> this</c><00:06:47.080><c> function</c>

00:06:47.629 --> 00:06:47.639 align:start position:0%
query endpoint function this function
 

00:06:47.639 --> 00:06:49.430 align:start position:0%
query endpoint function this function
takes<00:06:48.000><c> endpoint</c><00:06:48.479><c> name</c><00:06:48.759><c> as</c><00:06:48.919><c> the</c><00:06:49.039><c> first</c>

00:06:49.430 --> 00:06:49.440 align:start position:0%
takes endpoint name as the first
 

00:06:49.440 --> 00:06:52.830 align:start position:0%
takes endpoint name as the first
parameter<00:06:50.440><c> followed</c><00:06:50.880><c> by</c><00:06:51.039><c> a</c><00:06:51.280><c> string</c><00:06:52.120><c> data</c>

00:06:52.830 --> 00:06:52.840 align:start position:0%
parameter followed by a string data
 

00:06:52.840 --> 00:06:56.350 align:start position:0%
parameter followed by a string data
containing<00:06:53.280><c> your</c><00:06:53.560><c> medical</c><00:06:53.960><c> text</c><00:06:54.479><c> as</c><00:06:54.639><c> a</c><00:06:54.880><c> second</c>

00:06:56.350 --> 00:06:56.360 align:start position:0%
containing your medical text as a second
 

00:06:56.360 --> 00:06:59.830 align:start position:0%
containing your medical text as a second
parameter<00:06:57.360><c> as</c><00:06:57.520><c> you</c><00:06:57.680><c> can</c><00:06:58.000><c> see</c><00:06:59.000><c> the</c><00:06:59.160><c> input</c><00:06:59.599><c> Point</c>

00:06:59.830 --> 00:06:59.840 align:start position:0%
parameter as you can see the input Point
 

00:06:59.840 --> 00:07:02.390 align:start position:0%
parameter as you can see the input Point
has<00:07:00.039><c> detected</c><00:07:00.599><c> some</c><00:07:00.840><c> unical</c><00:07:01.599><c> entities</c><00:07:02.240><c> and</c>

00:07:02.390 --> 00:07:02.400 align:start position:0%
has detected some unical entities and
 

00:07:02.400 --> 00:07:05.110 align:start position:0%
has detected some unical entities and
made<00:07:02.639><c> some</c><00:07:02.879><c> classification</c><00:07:03.599><c> on</c><00:07:03.800><c> the</c><00:07:03.919><c> input</c>

00:07:05.110 --> 00:07:05.120 align:start position:0%
made some classification on the input
 

00:07:05.120 --> 00:07:08.070 align:start position:0%
made some classification on the input
document<00:07:06.120><c> for</c><00:07:06.319><c> example</c><00:07:06.879><c> the</c><00:07:07.039><c> list</c><00:07:07.400><c> has</c><00:07:07.759><c> been</c>

00:07:08.070 --> 00:07:08.080 align:start position:0%
document for example the list has been
 

00:07:08.080 --> 00:07:10.909 align:start position:0%
document for example the list has been
identified<00:07:08.919><c> as</c><00:07:09.080><c> a</c><00:07:09.599><c> Direction</c><00:07:10.599><c> with</c><00:07:10.720><c> a</c>

00:07:10.909 --> 00:07:10.919 align:start position:0%
identified as a Direction with a
 

00:07:10.919 --> 00:07:14.710 align:start position:0%
identified as a Direction with a
confidence<00:07:11.800><c> score</c><00:07:12.800><c> similarly</c><00:07:13.400><c> the</c><00:07:13.599><c> momy</c><00:07:14.560><c> has</c>

00:07:14.710 --> 00:07:14.720 align:start position:0%
confidence score similarly the momy has
 

00:07:14.720 --> 00:07:17.390 align:start position:0%
confidence score similarly the momy has
been<00:07:14.960><c> identified</c><00:07:15.479><c> as</c><00:07:15.599><c> a</c><00:07:15.759><c> cancer</c><00:07:16.120><c> surgery</c><00:07:17.080><c> also</c>

00:07:17.390 --> 00:07:17.400 align:start position:0%
been identified as a cancer surgery also
 

00:07:17.400 --> 00:07:20.189 align:start position:0%
been identified as a cancer surgery also
with<00:07:17.520><c> a</c><00:07:17.680><c> confidence</c><00:07:18.639><c> score</c><00:07:19.639><c> and</c><00:07:19.840><c> there</c><00:07:19.960><c> are</c>

00:07:20.189 --> 00:07:20.199 align:start position:0%
with a confidence score and there are
 

00:07:20.199 --> 00:07:22.830 align:start position:0%
with a confidence score and there are
other<00:07:21.160><c> entities</c><00:07:21.720><c> as</c><00:07:21.879><c> well</c><00:07:22.199><c> each</c><00:07:22.520><c> with</c><00:07:22.680><c> their</c>

00:07:22.830 --> 00:07:22.840 align:start position:0%
other entities as well each with their
 

00:07:22.840 --> 00:07:24.670 align:start position:0%
other entities as well each with their
own<00:07:23.120><c> classification</c><00:07:23.919><c> and</c><00:07:24.120><c> their</c><00:07:24.319><c> own</c>

00:07:24.670 --> 00:07:24.680 align:start position:0%
own classification and their own
 

00:07:24.680 --> 00:07:27.350 align:start position:0%
own classification and their own
Associated<00:07:25.680><c> confidence</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
Associated confidence
 

00:07:27.360 --> 00:07:31.110 align:start position:0%
Associated confidence
score<00:07:28.360><c> the</c><00:07:28.599><c> confidence</c><00:07:29.120><c> score</c><00:07:29.560><c> indicate</c><00:07:30.120><c> how</c>

00:07:31.110 --> 00:07:31.120 align:start position:0%
score the confidence score indicate how
 

00:07:31.120 --> 00:07:32.909 align:start position:0%
score the confidence score indicate how
confident<00:07:31.599><c> the</c><00:07:31.720><c> model</c><00:07:32.080><c> is</c><00:07:32.319><c> regarding</c><00:07:32.720><c> the</c>

00:07:32.909 --> 00:07:32.919 align:start position:0%
confident the model is regarding the
 

00:07:32.919 --> 00:07:41.950 align:start position:0%
confident the model is regarding the
accuracy<00:07:33.400><c> of</c><00:07:33.560><c> his</c>

00:07:41.950 --> 00:07:41.960 align:start position:0%
 
 

00:07:41.960 --> 00:07:44.830 align:start position:0%
 
classification<00:07:42.960><c> you</c><00:07:43.120><c> can</c><00:07:43.400><c> also</c><00:07:43.680><c> send</c><00:07:44.039><c> an</c><00:07:44.240><c> a</c><00:07:44.639><c> of</c>

00:07:44.830 --> 00:07:44.840 align:start position:0%
classification you can also send an a of
 

00:07:44.840 --> 00:07:46.430 align:start position:0%
classification you can also send an a of
text<00:07:45.159><c> to</c><00:07:45.319><c> the</c>

00:07:46.430 --> 00:07:46.440 align:start position:0%
text to the
 

00:07:46.440 --> 00:07:49.589 align:start position:0%
text to the
endpoint<00:07:47.440><c> as</c><00:07:47.560><c> you</c><00:07:47.720><c> see</c><00:07:47.919><c> in</c><00:07:48.039><c> the</c><00:07:48.400><c> example</c><00:07:49.400><c> I</c><00:07:49.479><c> am</c>

00:07:49.589 --> 00:07:49.599 align:start position:0%
endpoint as you see in the example I am
 

00:07:49.599 --> 00:07:51.749 align:start position:0%
endpoint as you see in the example I am
sending<00:07:50.039><c> two</c><00:07:50.280><c> clinical</c><00:07:50.759><c> text</c><00:07:51.039><c> to</c><00:07:51.199><c> the</c>

00:07:51.749 --> 00:07:51.759 align:start position:0%
sending two clinical text to the
 

00:07:51.759 --> 00:07:53.990 align:start position:0%
sending two clinical text to the
endpoint<00:07:52.759><c> now</c><00:07:52.960><c> we</c><00:07:53.080><c> are</c><00:07:53.199><c> showing</c><00:07:53.599><c> the</c><00:07:53.720><c> result</c>

00:07:53.990 --> 00:07:54.000 align:start position:0%
endpoint now we are showing the result
 

00:07:54.000 --> 00:07:57.270 align:start position:0%
endpoint now we are showing the result
on<00:07:54.120><c> a</c><00:07:54.280><c> data</c><00:07:54.560><c> frame</c><00:07:55.560><c> so</c><00:07:55.759><c> you</c><00:07:55.879><c> can</c><00:07:56.039><c> see</c><00:07:56.360><c> we</c><00:07:56.599><c> have</c>

00:07:57.270 --> 00:07:57.280 align:start position:0%
on a data frame so you can see we have
 

00:07:57.280 --> 00:07:59.749 align:start position:0%
on a data frame so you can see we have
oncological<00:07:58.000><c> entity</c><00:07:58.360><c> recognition</c><00:07:58.919><c> for</c><00:07:59.120><c> both</c>

00:07:59.749 --> 00:07:59.759 align:start position:0%
oncological entity recognition for both
 

00:07:59.759 --> 00:08:01.869 align:start position:0%
oncological entity recognition for both
documents<00:08:00.759><c> and</c><00:08:01.039><c> confidence</c><00:08:01.520><c> score</c>

00:08:01.869 --> 00:08:01.879 align:start position:0%
documents and confidence score
 

00:08:01.879 --> 00:08:10.629 align:start position:0%
documents and confidence score
associated<00:08:02.479><c> with</c><00:08:02.639><c> each</c>

00:08:10.629 --> 00:08:10.639 align:start position:0%
 
 

00:08:10.639 --> 00:08:13.110 align:start position:0%
 
classification<00:08:11.639><c> the</c><00:08:11.800><c> JSL</c><00:08:12.319><c> license</c><00:08:12.680><c> used</c><00:08:12.960><c> by</c>

00:08:13.110 --> 00:08:13.120 align:start position:0%
classification the JSL license used by
 

00:08:13.120 --> 00:08:16.390 align:start position:0%
classification the JSL license used by
the<00:08:13.280><c> endpoint</c><00:08:14.240><c> constantly</c><00:08:14.800><c> tracks</c><00:08:15.120><c> your</c><00:08:15.400><c> uses</c>

00:08:16.390 --> 00:08:16.400 align:start position:0%
the endpoint constantly tracks your uses
 

00:08:16.400 --> 00:08:18.469 align:start position:0%
the endpoint constantly tracks your uses
and<00:08:16.560><c> you</c><00:08:16.680><c> will</c><00:08:16.840><c> be</c><00:08:17.039><c> build</c><00:08:17.440><c> according</c><00:08:17.840><c> to</c><00:08:18.319><c> the</c>

00:08:18.469 --> 00:08:18.479 align:start position:0%
and you will be build according to the
 

00:08:18.479 --> 00:08:21.350 align:start position:0%
and you will be build according to the
usage<00:08:18.960><c> at</c><00:08:19.080><c> the</c><00:08:19.159><c> end</c><00:08:19.360><c> of</c><00:08:19.479><c> the</c><00:08:19.879><c> month</c><00:08:20.879><c> so</c><00:08:21.159><c> it</c>

00:08:21.350 --> 00:08:21.360 align:start position:0%
usage at the end of the month so it
 

00:08:21.360 --> 00:08:23.270 align:start position:0%
usage at the end of the month so it
might<00:08:21.560><c> be</c><00:08:21.800><c> wise</c><00:08:22.039><c> to</c><00:08:22.199><c> stop</c><00:08:22.479><c> your</c><00:08:22.639><c> endpoint</c><00:08:23.120><c> when</c>

00:08:23.270 --> 00:08:23.280 align:start position:0%
might be wise to stop your endpoint when
 

00:08:23.280 --> 00:08:25.710 align:start position:0%
might be wise to stop your endpoint when
you<00:08:23.400><c> are</c><00:08:23.599><c> not</c><00:08:23.800><c> using</c><00:08:24.159><c> it</c><00:08:25.080><c> to</c><00:08:25.240><c> stop</c><00:08:25.599><c> the</c>

00:08:25.710 --> 00:08:25.720 align:start position:0%
you are not using it to stop the
 

00:08:25.720 --> 00:08:27.990 align:start position:0%
you are not using it to stop the
endpoint<00:08:26.720><c> locate</c><00:08:27.120><c> your</c><00:08:27.280><c> endpoint</c><00:08:27.720><c> from</c><00:08:27.879><c> the</c>

00:08:27.990 --> 00:08:28.000 align:start position:0%
endpoint locate your endpoint from the
 

00:08:28.000 --> 00:08:31.029 align:start position:0%
endpoint locate your endpoint from the
serving<00:08:28.720><c> page</c><00:08:29.800><c> then</c><00:08:30.000><c> to</c><00:08:30.159><c> the</c><00:08:30.360><c> top</c><00:08:30.599><c> right</c><00:08:30.840><c> there</c>

00:08:31.029 --> 00:08:31.039 align:start position:0%
serving page then to the top right there
 

00:08:31.039 --> 00:08:32.949 align:start position:0%
serving page then to the top right there
is<00:08:31.159><c> a</c><00:08:31.319><c> stop</c><00:08:31.639><c> button</c><00:08:32.080><c> that</c><00:08:32.200><c> you</c><00:08:32.320><c> can</c><00:08:32.519><c> use</c><00:08:32.760><c> to</c>

00:08:32.949 --> 00:08:32.959 align:start position:0%
is a stop button that you can use to
 

00:08:32.959 --> 00:08:36.870 align:start position:0%
is a stop button that you can use to
stop<00:08:33.240><c> the</c><00:08:33.360><c> end</c>

00:08:36.870 --> 00:08:36.880 align:start position:0%
 
 

00:08:36.880 --> 00:08:41.159 align:start position:0%
 
point<00:08:37.880><c> thank</c><00:08:38.159><c> you</c>

