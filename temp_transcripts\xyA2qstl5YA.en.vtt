WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.210 align:start position:0%
 
come<00:00:00.149><c> back</c><00:00:00.329><c> to</c><00:00:00.750><c> part</c><00:00:01.260><c> 2</c><00:00:01.469><c> of</c><00:00:01.500><c> building</c><00:00:02.159><c> the</c>

00:00:02.210 --> 00:00:02.220 align:start position:0%
come back to part 2 of building the
 

00:00:02.220 --> 00:00:05.660 align:start position:0%
come back to part 2 of building the
modern-day<00:00:02.580><c> CMS</c><00:00:03.330><c> so</c><00:00:04.110><c> he</c><00:00:05.040><c> started</c><00:00:05.370><c> talking</c>

00:00:05.660 --> 00:00:05.670 align:start position:0%
modern-day CMS so he started talking
 

00:00:05.670 --> 00:00:07.249 align:start position:0%
modern-day CMS so he started talking
about<00:00:05.759><c> the</c><00:00:05.970><c> technology</c><00:00:06.540><c> stack</c><00:00:06.870><c> of</c><00:00:07.109><c> this</c>

00:00:07.249 --> 00:00:07.259 align:start position:0%
about the technology stack of this
 

00:00:07.259 --> 00:00:09.790 align:start position:0%
about the technology stack of this
project<00:00:07.890><c> just</c><00:00:08.160><c> last</c><00:00:08.730><c> time</c><00:00:08.970><c> but</c><00:00:09.210><c> I</c><00:00:09.240><c> want</c><00:00:09.480><c> to</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
project just last time but I want to
 

00:00:09.800 --> 00:00:12.680 align:start position:0%
project just last time but I want to
just<00:00:10.800><c> get</c><00:00:10.920><c> a</c><00:00:10.980><c> little</c><00:00:11.070><c> more</c><00:00:11.429><c> a</c><00:00:11.940><c> little</c><00:00:12.420><c> more</c><00:00:12.540><c> in</c>

00:00:12.680 --> 00:00:12.690 align:start position:0%
just get a little more a little more in
 

00:00:12.690 --> 00:00:14.690 align:start position:0%
just get a little more a little more in
depth<00:00:12.960><c> here</c><00:00:13.320><c> first</c><00:00:14.280><c> of</c><00:00:14.340><c> all</c><00:00:14.429><c> we've</c><00:00:14.549><c> got</c>

00:00:14.690 --> 00:00:14.700 align:start position:0%
depth here first of all we've got
 

00:00:14.700 --> 00:00:17.269 align:start position:0%
depth here first of all we've got
angular<00:00:15.030><c> my</c><00:00:15.210><c> sequel</c><00:00:15.630><c> nodejs</c><00:00:16.049><c> server</c><00:00:16.470><c> Heroku</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
angular my sequel nodejs server Heroku
 

00:00:17.279 --> 00:00:20.150 align:start position:0%
angular my sequel nodejs server Heroku
AWS<00:00:17.970><c> s3</c><00:00:18.150><c> image</c><00:00:18.900><c> storage</c><00:00:19.320><c> compressed</c><00:00:19.980><c> before</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
AWS s3 image storage compressed before
 

00:00:20.160 --> 00:00:24.259 align:start position:0%
AWS s3 image storage compressed before
uploaded<00:00:21.050><c> after</c><00:00:22.050><c> uploaded</c><00:00:22.980><c> by</c><00:00:23.130><c> user</c><00:00:23.460><c> sorry</c>

00:00:24.259 --> 00:00:24.269 align:start position:0%
uploaded after uploaded by user sorry
 

00:00:24.269 --> 00:00:30.200 align:start position:0%
uploaded after uploaded by user sorry
about<00:00:24.480><c> that</c><00:00:24.710><c> ok</c><00:00:26.689><c> after</c><00:00:28.429><c> if</c><00:00:29.429><c> that</c><00:00:29.640><c> Google</c><00:00:29.970><c> Cloud</c>

00:00:30.200 --> 00:00:30.210 align:start position:0%
about that ok after if that Google Cloud
 

00:00:30.210 --> 00:00:31.999 align:start position:0%
about that ok after if that Google Cloud
console<00:00:30.630><c> for</c><00:00:30.810><c> the</c><00:00:30.869><c> my</c><00:00:30.990><c> sequel</c><00:00:31.380><c> hosting</c><00:00:31.830><c> we'll</c>

00:00:31.999 --> 00:00:32.009 align:start position:0%
console for the my sequel hosting we'll
 

00:00:32.009 --> 00:00:33.889 align:start position:0%
console for the my sequel hosting we'll
see<00:00:32.189><c> how</c><00:00:32.340><c> much</c><00:00:32.489><c> it's</c><00:00:32.880><c> gonna</c><00:00:33.059><c> cost</c><00:00:33.180><c> over</c><00:00:33.870><c> the</c>

00:00:33.889 --> 00:00:33.899 align:start position:0%
see how much it's gonna cost over the
 

00:00:33.899 --> 00:00:36.290 align:start position:0%
see how much it's gonna cost over the
course<00:00:34.200><c> of</c><00:00:34.320><c> time</c><00:00:34.469><c> might</c><00:00:34.770><c> change</c><00:00:35.120><c> angular</c><00:00:36.120><c> UI</c>

00:00:36.290 --> 00:00:36.300 align:start position:0%
course of time might change angular UI
 

00:00:36.300 --> 00:00:39.380 align:start position:0%
course of time might change angular UI
rather<00:00:36.750><c> and</c><00:00:37.140><c> each</c><00:00:37.590><c> is</c><00:00:38.040><c> that</c><00:00:38.850><c> handles</c><00:00:39.239><c> the</c>

00:00:39.380 --> 00:00:39.390 align:start position:0%
rather and each is that handles the
 

00:00:39.390 --> 00:00:42.560 align:start position:0%
rather and each is that handles the
routes<00:00:39.570><c> and</c><00:00:39.870><c> the</c><00:00:40.110><c> templates</c><00:00:40.910><c> socket</c><00:00:41.910><c> IO</c><00:00:42.000><c> which</c>

00:00:42.560 --> 00:00:42.570 align:start position:0%
routes and the templates socket IO which
 

00:00:42.570 --> 00:00:44.420 align:start position:0%
routes and the templates socket IO which
we're<00:00:42.809><c> gonna</c><00:00:42.899><c> turn</c><00:00:43.140><c> into</c><00:00:43.320><c> either</c><00:00:43.500><c> a</c><00:00:43.649><c> live</c><00:00:43.920><c> chat</c>

00:00:44.420 --> 00:00:44.430 align:start position:0%
we're gonna turn into either a live chat
 

00:00:44.430 --> 00:00:46.540 align:start position:0%
we're gonna turn into either a live chat
feature<00:00:44.730><c> or</c><00:00:45.180><c> a</c><00:00:45.210><c> bulletin</c><00:00:45.750><c> board</c><00:00:45.780><c> feature</c>

00:00:46.540 --> 00:00:46.550 align:start position:0%
feature or a bulletin board feature
 

00:00:46.550 --> 00:00:49.130 align:start position:0%
feature or a bulletin board feature
passport<00:00:47.550><c> authentication</c><00:00:48.300><c> and</c><00:00:48.510><c> a</c><00:00:48.870><c> node</c>

00:00:49.130 --> 00:00:49.140 align:start position:0%
passport authentication and a node
 

00:00:49.140 --> 00:00:51.560 align:start position:0%
passport authentication and a node
mailer<00:00:49.500><c> NPM</c><00:00:49.950><c> package</c><00:00:50.399><c> which</c><00:00:50.640><c> uses</c><00:00:51.030><c> Melvin</c>

00:00:51.560 --> 00:00:51.570 align:start position:0%
mailer NPM package which uses Melvin
 

00:00:51.570 --> 00:00:53.299 align:start position:0%
mailer NPM package which uses Melvin
that's<00:00:52.260><c> a</c><00:00:52.350><c> pretty</c><00:00:52.500><c> good</c><00:00:52.739><c> sum</c><00:00:52.980><c> above</c>

00:00:53.299 --> 00:00:53.309 align:start position:0%
that's a pretty good sum above
 

00:00:53.309 --> 00:00:55.850 align:start position:0%
that's a pretty good sum above
everything<00:00:53.670><c> that</c><00:00:54.059><c> the</c><00:00:55.050><c> technology</c><00:00:55.649><c> that's</c>

00:00:55.850 --> 00:00:55.860 align:start position:0%
everything that the technology that's
 

00:00:55.860 --> 00:00:58.189 align:start position:0%
everything that the technology that's
covered<00:00:56.280><c> within</c><00:00:56.520><c> with</c><00:00:57.449><c> it's</c><00:00:57.809><c> in</c><00:00:58.020><c> these</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
covered within with it's in these
 

00:00:58.199 --> 00:01:00.139 align:start position:0%
covered within with it's in these
tutorials<00:00:58.859><c> this</c><00:00:59.100><c> site</c><00:00:59.309><c> is</c><00:00:59.430><c> called</c><00:00:59.579><c> canva.com</c>

00:01:00.139 --> 00:01:00.149 align:start position:0%
tutorials this site is called canva.com
 

00:01:00.149 --> 00:01:02.240 align:start position:0%
tutorials this site is called canva.com
by<00:01:00.930><c> the</c><00:01:00.989><c> way</c><00:01:01.199><c> you</c><00:01:01.260><c> can</c><00:01:01.559><c> use</c><00:01:01.680><c> it</c><00:01:01.829><c> to</c><00:01:01.949><c> build</c><00:01:02.070><c> a</c>

00:01:02.240 --> 00:01:02.250 align:start position:0%
by the way you can use it to build a
 

00:01:02.250 --> 00:01:04.479 align:start position:0%
by the way you can use it to build a
resume<00:01:02.430><c> or</c><00:01:02.789><c> anything</c><00:01:03.239><c> you'd</c><00:01:03.539><c> like</c><00:01:03.570><c> it's</c><00:01:04.019><c> great</c>

00:01:04.479 --> 00:01:04.489 align:start position:0%
resume or anything you'd like it's great
 

00:01:04.489 --> 00:01:07.280 align:start position:0%
resume or anything you'd like it's great
so<00:01:05.489><c> we're</c><00:01:06.180><c> gonna</c><00:01:06.299><c> go</c><00:01:06.479><c> back</c><00:01:06.689><c> in</c><00:01:06.900><c> here</c><00:01:07.110><c> we're</c>

00:01:07.280 --> 00:01:07.290 align:start position:0%
so we're gonna go back in here we're
 

00:01:07.290 --> 00:01:10.370 align:start position:0%
so we're gonna go back in here we're
gonna<00:01:07.350><c> go</c><00:01:07.590><c> into</c><00:01:07.860><c> the</c><00:01:07.979><c> app</c><00:01:08.510><c> donations</c><00:01:09.510><c> as</c><00:01:09.720><c> you</c>

00:01:10.370 --> 00:01:10.380 align:start position:0%
gonna go into the app donations as you
 

00:01:10.380 --> 00:01:12.200 align:start position:0%
gonna go into the app donations as you
can<00:01:10.530><c> see</c><00:01:10.710><c> it</c><00:01:10.799><c> also</c><00:01:10.950><c> uses</c><00:01:11.250><c> cookies</c><00:01:11.880><c> and</c><00:01:12.119><c> it</c>

00:01:12.200 --> 00:01:12.210 align:start position:0%
can see it also uses cookies and it
 

00:01:12.210 --> 00:01:16.940 align:start position:0%
can see it also uses cookies and it
remembers<00:01:12.479><c> the</c><00:01:12.930><c> user</c><00:01:13.200><c> that's</c><00:01:13.680><c> good</c><00:01:14.479><c> and</c><00:01:15.950><c> this</c>

00:01:16.940 --> 00:01:16.950 align:start position:0%
remembers the user that's good and this
 

00:01:16.950 --> 00:01:19.310 align:start position:0%
remembers the user that's good and this
is<00:01:17.009><c> the</c><00:01:17.340><c> live</c><00:01:17.520><c> bulletin</c><00:01:18.090><c> feature</c><00:01:18.479><c> as</c><00:01:19.049><c> you'll</c>

00:01:19.310 --> 00:01:19.320 align:start position:0%
is the live bulletin feature as you'll
 

00:01:19.320 --> 00:01:22.609 align:start position:0%
is the live bulletin feature as you'll
see<00:01:19.500><c> currently</c><00:01:19.979><c> it</c><00:01:20.100><c> doesn't</c><00:01:20.130><c> work</c><00:01:20.670><c> Pizza</c><00:01:21.390><c> Hut</c>

00:01:22.609 --> 00:01:22.619 align:start position:0%
see currently it doesn't work Pizza Hut
 

00:01:22.619 --> 00:01:25.580 align:start position:0%
see currently it doesn't work Pizza Hut
let's<00:01:23.520><c> see</c><00:01:23.670><c> pizza</c><00:01:24.210><c> nah</c><00:01:24.600><c> it</c><00:01:24.930><c> doesn't</c><00:01:25.080><c> work</c><00:01:25.409><c> at</c>

00:01:25.580 --> 00:01:25.590 align:start position:0%
let's see pizza nah it doesn't work at
 

00:01:25.590 --> 00:01:25.940 align:start position:0%
let's see pizza nah it doesn't work at
all

00:01:25.940 --> 00:01:25.950 align:start position:0%
all
 

00:01:25.950 --> 00:01:29.270 align:start position:0%
all
so<00:01:26.400><c> we</c><00:01:26.670><c> have</c><00:01:26.790><c> to</c><00:01:26.939><c> fix</c><00:01:27.150><c> that</c><00:01:27.450><c> a</c><00:01:28.380><c> stat</c><00:01:28.950><c> section</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
so we have to fix that a stat section
 

00:01:29.280 --> 00:01:31.819 align:start position:0%
so we have to fix that a stat section
that's<00:01:29.939><c> gonna</c><00:01:30.119><c> be</c><00:01:30.299><c> used</c><00:01:30.570><c> for</c><00:01:30.990><c> managing</c><00:01:31.650><c> the</c>

00:01:31.819 --> 00:01:31.829 align:start position:0%
that's gonna be used for managing the
 

00:01:31.829 --> 00:01:35.719 align:start position:0%
that's gonna be used for managing the
pages<00:01:32.310><c> somehow</c><00:01:32.790><c> I</c><00:01:34.130><c> figured</c><00:01:35.130><c> that</c><00:01:35.280><c> out</c><00:01:35.310><c> the</c>

00:01:35.719 --> 00:01:35.729 align:start position:0%
pages somehow I figured that out the
 

00:01:35.729 --> 00:01:41.749 align:start position:0%
pages somehow I figured that out the
community<00:01:36.150><c> section</c><00:01:36.590><c> which</c><00:01:37.590><c> saves</c><00:01:40.759><c> saves</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
community section which saves saves
 

00:01:41.759 --> 00:01:44.569 align:start position:0%
community section which saves saves
posts<00:01:42.479><c> ok</c><00:01:42.960><c> it</c><00:01:43.079><c> saves</c><00:01:43.320><c> it</c><00:01:43.500><c> into</c><00:01:43.710><c> the</c><00:01:43.799><c> DB</c><00:01:44.159><c> once</c><00:01:44.430><c> we</c>

00:01:44.569 --> 00:01:44.579 align:start position:0%
posts ok it saves it into the DB once we
 

00:01:44.579 --> 00:01:46.940 align:start position:0%
posts ok it saves it into the DB once we
have<00:01:44.729><c> the</c><00:01:45.000><c> the</c><00:01:45.360><c> HTML</c><00:01:46.049><c> stored</c><00:01:46.409><c> in</c><00:01:46.530><c> the</c><00:01:46.649><c> database</c>

00:01:46.940 --> 00:01:46.950 align:start position:0%
have the the HTML stored in the database
 

00:01:46.950 --> 00:01:50.230 align:start position:0%
have the the HTML stored in the database
it's<00:01:47.280><c> gonna</c><00:01:47.430><c> be</c><00:01:47.610><c> relatively</c><00:01:48.030><c> simple</c><00:01:48.570><c> to</c><00:01:48.720><c> just</c>

00:01:50.230 --> 00:01:50.240 align:start position:0%
it's gonna be relatively simple to just
 

00:01:50.240 --> 00:01:55.480 align:start position:0%
it's gonna be relatively simple to just
just<00:01:51.240><c> pull</c><00:01:51.509><c> it</c><00:01:51.659><c> up</c><00:01:51.810><c> in</c><00:01:52.500><c> in</c><00:01:53.369><c> the</c><00:01:53.610><c> code</c><00:01:53.880><c> and</c><00:01:54.540><c> so</c><00:01:54.930><c> on</c>

00:01:55.480 --> 00:01:55.490 align:start position:0%
just pull it up in in the code and so on
 

00:01:55.490 --> 00:02:00.260 align:start position:0%
just pull it up in in the code and so on
let's<00:01:56.490><c> go</c><00:01:56.759><c> back</c><00:01:58.579><c> over</c><00:01:59.579><c> here</c><00:01:59.610><c> we</c><00:01:59.969><c> have</c><00:02:00.090><c> the</c>

00:02:00.260 --> 00:02:00.270 align:start position:0%
let's go back over here we have the
 

00:02:00.270 --> 00:02:02.569 align:start position:0%
let's go back over here we have the
search<00:02:00.570><c> ok</c><00:02:01.229><c> within</c><00:02:01.590><c> the</c><00:02:01.799><c> search</c><00:02:02.009><c> itself</c><00:02:02.399><c> we</c>

00:02:02.569 --> 00:02:02.579 align:start position:0%
search ok within the search itself we
 

00:02:02.579 --> 00:02:04.160 align:start position:0%
search ok within the search itself we
have<00:02:02.700><c> another</c><00:02:02.939><c> nice</c><00:02:03.180><c> feature</c><00:02:03.450><c> which</c><00:02:03.869><c> is</c><00:02:04.049><c> if</c>

00:02:04.160 --> 00:02:04.170 align:start position:0%
have another nice feature which is if
 

00:02:04.170 --> 00:02:06.170 align:start position:0%
have another nice feature which is if
you<00:02:04.350><c> click</c><00:02:04.619><c> on</c><00:02:04.680><c> this</c><00:02:05.460><c> one</c><00:02:05.700><c> as</c><00:02:05.820><c> you'll</c><00:02:06.000><c> see</c><00:02:06.119><c> this</c>

00:02:06.170 --> 00:02:06.180 align:start position:0%
you click on this one as you'll see this
 

00:02:06.180 --> 00:02:08.690 align:start position:0%
you click on this one as you'll see this
is<00:02:06.360><c> Cramer</c><00:02:06.630><c> 16</c><00:02:07.259><c> this</c><00:02:07.469><c> is</c><00:02:07.590><c> an</c><00:02:07.740><c> ng-repeat</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
is Cramer 16 this is an ng-repeat
 

00:02:08.700 --> 00:02:10.999 align:start position:0%
is Cramer 16 this is an ng-repeat
if<00:02:09.330><c> I</c><00:02:09.450><c> click</c><00:02:09.690><c> on</c><00:02:09.840><c> visit</c><00:02:10.170><c> profile</c><00:02:10.590><c> have</c><00:02:10.800><c> a</c><00:02:10.830><c> look</c>

00:02:10.999 --> 00:02:11.009 align:start position:0%
if I click on visit profile have a look
 

00:02:11.009 --> 00:02:12.980 align:start position:0%
if I click on visit profile have a look
at<00:02:11.099><c> what</c><00:02:11.190><c> happens</c><00:02:11.489><c> to</c><00:02:11.640><c> the</c><00:02:11.730><c> URL</c><00:02:12.000><c> slash</c><00:02:12.300><c> homes</c>

00:02:12.980 --> 00:02:12.990 align:start position:0%
at what happens to the URL slash homes
 

00:02:12.990 --> 00:02:17.510 align:start position:0%
at what happens to the URL slash homes
search<00:02:13.350><c> and</c><00:02:14.120><c> profile</c><00:02:15.120><c> ID</c><00:02:15.870><c> one</c><00:02:16.440><c> and</c><00:02:16.860><c> cream</c><00:02:17.400><c> with</c>

00:02:17.510 --> 00:02:17.520 align:start position:0%
search and profile ID one and cream with
 

00:02:17.520 --> 00:02:20.600 align:start position:0%
search and profile ID one and cream with
sixteen<00:02:18.300><c> it</c><00:02:18.570><c> just</c><00:02:18.780><c> multiplies</c><00:02:18.960><c> that</c><00:02:19.710><c> ID</c><00:02:19.950><c> and</c>

00:02:20.600 --> 00:02:20.610 align:start position:0%
sixteen it just multiplies that ID and
 

00:02:20.610 --> 00:02:23.120 align:start position:0%
sixteen it just multiplies that ID and
this<00:02:21.390><c> is</c><00:02:21.570><c> the</c><00:02:21.720><c> email</c><00:02:21.870><c> Cramer</c><00:02:22.290><c> sixteen</c><00:02:22.890><c> it's</c>

00:02:23.120 --> 00:02:23.130 align:start position:0%
this is the email Cramer sixteen it's
 

00:02:23.130 --> 00:02:25.250 align:start position:0%
this is the email Cramer sixteen it's
pretty<00:02:23.400><c> much</c><00:02:23.550><c> a</c><00:02:23.610><c> customized</c><00:02:24.270><c> profile</c><00:02:24.900><c> page</c>

00:02:25.250 --> 00:02:25.260 align:start position:0%
pretty much a customized profile page
 

00:02:25.260 --> 00:02:27.710 align:start position:0%
pretty much a customized profile page
for<00:02:25.290><c> that</c><00:02:25.560><c> specific</c><00:02:26.160><c> user</c><00:02:26.480><c> so</c><00:02:27.480><c> that's</c><00:02:27.630><c> a</c>

00:02:27.710 --> 00:02:27.720 align:start position:0%
for that specific user so that's a
 

00:02:27.720 --> 00:02:30.080 align:start position:0%
for that specific user so that's a
really<00:02:27.930><c> nice</c><00:02:28.110><c> feature</c><00:02:28.170><c> and</c><00:02:28.920><c> you</c><00:02:29.820><c> could</c><00:02:30.000><c> do</c>

00:02:30.080 --> 00:02:30.090 align:start position:0%
really nice feature and you could do
 

00:02:30.090 --> 00:02:32.360 align:start position:0%
really nice feature and you could do
that<00:02:30.240><c> pretty</c><00:02:30.630><c> much</c><00:02:30.840><c> with</c><00:02:31.140><c> that</c><00:02:32.010><c> could</c><00:02:32.130><c> have</c><00:02:32.310><c> a</c>

00:02:32.360 --> 00:02:32.370 align:start position:0%
that pretty much with that could have a
 

00:02:32.370 --> 00:02:34.400 align:start position:0%
that pretty much with that could have a
lot<00:02:32.400><c> of</c><00:02:32.580><c> different</c><00:02:33.120><c> use</c><00:02:33.330><c> cases</c><00:02:33.630><c> it's</c><00:02:34.110><c> just</c>

00:02:34.400 --> 00:02:34.410 align:start position:0%
lot of different use cases it's just
 

00:02:34.410 --> 00:02:36.910 align:start position:0%
lot of different use cases it's just
angular<00:02:35.160><c> UI</c><00:02:35.400><c> router</c><00:02:35.760><c> and</c><00:02:36.270><c> it's</c><00:02:36.420><c> using</c>

00:02:36.910 --> 00:02:36.920 align:start position:0%
angular UI router and it's using
 

00:02:36.920 --> 00:02:39.800 align:start position:0%
angular UI router and it's using
parameters<00:02:37.920><c> within</c><00:02:38.640><c> the</c><00:02:38.850><c> URL</c><00:02:39.030><c> which</c><00:02:39.540><c> are</c><00:02:39.690><c> then</c>

00:02:39.800 --> 00:02:39.810 align:start position:0%
parameters within the URL which are then
 

00:02:39.810 --> 00:02:41.660 align:start position:0%
parameters within the URL which are then
fetched<00:02:40.260><c> by</c><00:02:40.380><c> the</c><00:02:40.440><c> angular</c><00:02:40.980><c> code</c><00:02:41.280><c> and</c><00:02:41.580><c> then</c>

00:02:41.660 --> 00:02:41.670 align:start position:0%
fetched by the angular code and then
 

00:02:41.670 --> 00:02:46.040 align:start position:0%
fetched by the angular code and then
reused<00:02:42.180><c> somehow</c><00:02:42.870><c> in</c><00:02:43.050><c> the</c><00:02:43.170><c> logic</c><00:02:44.600><c> okay</c><00:02:45.600><c> this</c><00:02:45.900><c> is</c>

00:02:46.040 --> 00:02:46.050 align:start position:0%
reused somehow in the logic okay this is
 

00:02:46.050 --> 00:02:48.790 align:start position:0%
reused somehow in the logic okay this is
another<00:02:46.520><c> thing</c><00:02:47.520><c> that</c><00:02:47.580><c> I</c><00:02:47.820><c> stumbled</c><00:02:48.330><c> across</c>

00:02:48.790 --> 00:02:48.800 align:start position:0%
another thing that I stumbled across
 

00:02:48.800 --> 00:02:51.800 align:start position:0%
another thing that I stumbled across
Administration<00:02:50.180><c> it's</c><00:02:51.180><c> not</c><00:02:51.360><c> such</c><00:02:51.510><c> a</c><00:02:51.630><c> great</c>

00:02:51.800 --> 00:02:51.810 align:start position:0%
Administration it's not such a great
 

00:02:51.810 --> 00:02:53.240 align:start position:0%
Administration it's not such a great
feature<00:02:52.050><c> I</c><00:02:52.230><c> was</c><00:02:52.410><c> just</c><00:02:52.590><c> playing</c><00:02:52.830><c> around</c><00:02:52.980><c> with</c>

00:02:53.240 --> 00:02:53.250 align:start position:0%
feature I was just playing around with
 

00:02:53.250 --> 00:02:55.970 align:start position:0%
feature I was just playing around with
some<00:02:53.600><c> drop-down</c><00:02:54.600><c> features</c><00:02:55.140><c> and</c><00:02:55.470><c> we</c><00:02:55.740><c> have</c><00:02:55.860><c> the</c>

00:02:55.970 --> 00:02:55.980 align:start position:0%
some drop-down features and we have the
 

00:02:55.980 --> 00:02:57.740 align:start position:0%
some drop-down features and we have the
contact<00:02:56.460><c> feature</c><00:02:56.730><c> this</c><00:02:57.000><c> is</c><00:02:57.120><c> an</c><00:02:57.270><c> exciting</c><00:02:57.570><c> one</c>

00:02:57.740 --> 00:02:57.750 align:start position:0%
contact feature this is an exciting one
 

00:02:57.750 --> 00:03:00.530 align:start position:0%
contact feature this is an exciting one
let's<00:02:58.560><c> say</c><00:02:58.740><c> my</c><00:02:59.010><c> name</c><00:02:59.160><c> is</c><00:02:59.310><c> Leticia</c><00:02:59.730><c> I</c><00:03:00.120><c> haven't</c>

00:03:00.530 --> 00:03:00.540 align:start position:0%
let's say my name is Leticia I haven't
 

00:03:00.540 --> 00:03:07.780 align:start position:0%
let's say my name is Leticia I haven't
free<00:03:00.810><c> have</c><00:03:01.470><c> amp</c><00:03:02.280><c> it</c><00:03:02.580><c> up</c><00:03:02.700><c> I</c><00:03:03.170><c> use</c><00:03:04.170><c> a</c><00:03:04.200><c> couple</c><00:03:04.560><c> of</c>

00:03:07.780 --> 00:03:07.790 align:start position:0%
 
 

00:03:07.790 --> 00:03:12.080 align:start position:0%
 
email<00:03:08.790><c> accounts</c><00:03:09.420><c> this</c><00:03:10.020><c> is</c><00:03:10.200><c> an</c><00:03:10.350><c> example</c><00:03:11.090><c> let's</c>

00:03:12.080 --> 00:03:12.090 align:start position:0%
email accounts this is an example let's
 

00:03:12.090 --> 00:03:13.630 align:start position:0%
email accounts this is an example let's
see<00:03:12.450><c> here</c>

00:03:13.630 --> 00:03:13.640 align:start position:0%
see here
 

00:03:13.640 --> 00:03:23.840 align:start position:0%
see here
Leticia<00:03:17.810><c> here's</c><00:03:18.810><c> the</c><00:03:19.020><c> email</c><00:03:20.060><c> we'll</c><00:03:21.060><c> see</c><00:03:22.850><c> okay</c>

00:03:23.840 --> 00:03:23.850 align:start position:0%
Leticia here's the email we'll see okay
 

00:03:23.850 --> 00:03:26.690 align:start position:0%
Leticia here's the email we'll see okay
I'm<00:03:23.970><c> gonna</c><00:03:24.120><c> paste</c><00:03:24.420><c> that</c><00:03:24.570><c> in</c><00:03:24.720><c> there</c><00:03:25.340><c> okay</c><00:03:26.340><c> phone</c>

00:03:26.690 --> 00:03:26.700 align:start position:0%
I'm gonna paste that in there okay phone
 

00:03:26.700 --> 00:03:39.350 align:start position:0%
I'm gonna paste that in there okay phone
number<00:03:27.090><c> okay</c><00:03:31.550><c> baby</c><00:03:32.550><c> okay</c><00:03:34.670><c> and</c><00:03:35.670><c> submit</c><00:03:37.130><c> and</c><00:03:38.360><c> at</c>

00:03:39.350 --> 00:03:39.360 align:start position:0%
number okay baby okay and submit and at
 

00:03:39.360 --> 00:03:40.970 align:start position:0%
number okay baby okay and submit and at
a<00:03:39.420><c> Bing</c><00:03:39.630><c> badda</c><00:03:39.810><c> boom</c><00:03:40.020><c> takes</c><00:03:40.380><c> me</c><00:03:40.530><c> back</c><00:03:40.740><c> to</c><00:03:40.770><c> the</c>

00:03:40.970 --> 00:03:40.980 align:start position:0%
a Bing badda boom takes me back to the
 

00:03:40.980 --> 00:03:44.150 align:start position:0%
a Bing badda boom takes me back to the
home<00:03:41.190><c> page</c><00:03:41.870><c> not</c><00:03:42.870><c> sure</c><00:03:43.050><c> why</c><00:03:43.200><c> it</c><00:03:43.260><c> still</c><00:03:43.710><c> has</c><00:03:43.890><c> this</c>

00:03:44.150 --> 00:03:44.160 align:start position:0%
home page not sure why it still has this
 

00:03:44.160 --> 00:03:46.940 align:start position:0%
home page not sure why it still has this
contact<00:03:44.820><c> here</c><00:03:45.170><c> you</c><00:03:46.170><c> may</c><00:03:46.290><c> want</c><00:03:46.320><c> to</c><00:03:46.530><c> get</c><00:03:46.710><c> rid</c><00:03:46.890><c> of</c>

00:03:46.940 --> 00:03:46.950 align:start position:0%
contact here you may want to get rid of
 

00:03:46.950 --> 00:03:48.920 align:start position:0%
contact here you may want to get rid of
that<00:03:47.070><c> somehow</c><00:03:47.370><c> and</c><00:03:47.730><c> the</c><00:03:47.850><c> contact</c><00:03:48.420><c> stays</c><00:03:48.720><c> in</c>

00:03:48.920 --> 00:03:48.930 align:start position:0%
that somehow and the contact stays in
 

00:03:48.930 --> 00:03:51.230 align:start position:0%
that somehow and the contact stays in
the<00:03:49.050><c> URL</c><00:03:49.350><c> even</c><00:03:49.830><c> if</c><00:03:49.950><c> we</c><00:03:50.130><c> you</c><00:03:50.820><c> know</c><00:03:50.970><c> it</c><00:03:51.120><c> just</c>

00:03:51.230 --> 00:03:51.240 align:start position:0%
the URL even if we you know it just
 

00:03:51.240 --> 00:03:54.350 align:start position:0%
the URL even if we you know it just
doesn't<00:03:51.540><c> disrupt</c><00:03:52.110><c> her</c><00:03:52.410><c> and</c><00:03:52.590><c> leave</c><00:03:52.740><c> flow</c><00:03:53.460><c> but</c>

00:03:54.350 --> 00:03:54.360 align:start position:0%
doesn't disrupt her and leave flow but
 

00:03:54.360 --> 00:03:56.960 align:start position:0%
doesn't disrupt her and leave flow but
okay<00:03:54.660><c> we'll</c><00:03:55.440><c> go</c><00:03:55.560><c> back</c><00:03:55.740><c> into</c><00:03:55.890><c> the</c><00:03:56.070><c> inbox</c><00:03:56.190><c> we're</c>

00:03:56.960 --> 00:03:56.970 align:start position:0%
okay we'll go back into the inbox we're
 

00:03:56.970 --> 00:03:58.490 align:start position:0%
okay we'll go back into the inbox we're
gonna<00:03:57.150><c> go</c><00:03:57.360><c> into</c><00:03:57.510><c> more</c><00:03:57.840><c> we're</c><00:03:58.050><c> gonna</c><00:03:58.140><c> go</c><00:03:58.350><c> into</c>

00:03:58.490 --> 00:03:58.500 align:start position:0%
gonna go into more we're gonna go into
 

00:03:58.500 --> 00:04:03.200 align:start position:0%
gonna go into more we're gonna go into
the<00:03:58.710><c> spam</c><00:03:59.010><c> and</c><00:03:59.930><c> there</c><00:04:00.930><c> it</c><00:04:01.110><c> is</c><00:04:01.820><c> you</c><00:04:02.820><c> have</c><00:04:02.970><c> a</c><00:04:03.000><c> new</c>

00:04:03.200 --> 00:04:03.210 align:start position:0%
the spam and there it is you have a new
 

00:04:03.210 --> 00:04:06.920 align:start position:0%
the spam and there it is you have a new
contact<00:04:03.780><c> request</c><00:04:04.430><c> we</c><00:04:05.430><c> are</c><00:04:05.700><c> live</c><00:04:06.060><c> on</c><00:04:06.330><c> YouTube</c>

00:04:06.920 --> 00:04:06.930 align:start position:0%
contact request we are live on YouTube
 

00:04:06.930 --> 00:04:09.140 align:start position:0%
contact request we are live on YouTube
baby<00:04:07.230><c> so</c><00:04:07.560><c> male</c><00:04:07.860><c> done</c><00:04:08.100><c> works</c><00:04:08.400><c> already</c><00:04:08.790><c> that's</c>

00:04:09.140 --> 00:04:09.150 align:start position:0%
baby so male done works already that's
 

00:04:09.150 --> 00:04:13.210 align:start position:0%
baby so male done works already that's
good<00:04:09.450><c> and</c><00:04:09.720><c> it's</c><00:04:10.470><c> based</c><00:04:10.740><c> off</c><00:04:10.920><c> the</c><00:04:11.130><c> user</c><00:04:11.340><c> input</c>

00:04:13.210 --> 00:04:13.220 align:start position:0%
good and it's based off the user input
 

00:04:13.220 --> 00:04:17.170 align:start position:0%
good and it's based off the user input
we<00:04:14.220><c> can</c><00:04:14.370><c> have</c><00:04:14.520><c> a</c><00:04:14.550><c> look</c><00:04:14.760><c> at</c><00:04:14.880><c> that</c><00:04:15.030><c> soon</c><00:04:15.360><c> but</c>

00:04:17.170 --> 00:04:17.180 align:start position:0%
we can have a look at that soon but
 

00:04:17.180 --> 00:04:18.460 align:start position:0%
we can have a look at that soon but
[Music]

00:04:18.460 --> 00:04:18.470 align:start position:0%
[Music]
 

00:04:18.470 --> 00:04:20.960 align:start position:0%
[Music]
we'll<00:04:19.470><c> have</c><00:04:19.650><c> to</c><00:04:19.799><c> investigate</c><00:04:20.160><c> why</c><00:04:20.489><c> it's</c><00:04:20.670><c> going</c>

00:04:20.960 --> 00:04:20.970 align:start position:0%
we'll have to investigate why it's going
 

00:04:20.970 --> 00:04:23.840 align:start position:0%
we'll have to investigate why it's going
to<00:04:21.060><c> spam</c><00:04:21.390><c> in</c><00:04:21.780><c> order</c><00:04:21.989><c> to</c><00:04:22.109><c> fix</c><00:04:22.320><c> it</c><00:04:22.560><c> and</c><00:04:22.710><c> get</c><00:04:23.700><c> it</c>

00:04:23.840 --> 00:04:23.850 align:start position:0%
to spam in order to fix it and get it
 

00:04:23.850 --> 00:04:25.670 align:start position:0%
to spam in order to fix it and get it
sent<00:04:24.150><c> straight</c><00:04:24.419><c> to</c><00:04:24.540><c> the</c><00:04:24.780><c> inbox</c><00:04:25.140><c> which</c><00:04:25.410><c> it</c><00:04:25.560><c> is</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
sent straight to the inbox which it is
 

00:04:25.680 --> 00:04:26.140 align:start position:0%
sent straight to the inbox which it is
possible

00:04:26.140 --> 00:04:26.150 align:start position:0%
possible
 

00:04:26.150 --> 00:04:29.890 align:start position:0%
possible
according<00:04:26.600><c> to</c><00:04:26.630><c> my</c><00:04:26.810><c> research</c><00:04:27.639><c> let's</c><00:04:28.639><c> see</c><00:04:28.910><c> okay</c>

00:04:29.890 --> 00:04:29.900 align:start position:0%
according to my research let's see okay
 

00:04:29.900 --> 00:04:31.420 align:start position:0%
according to my research let's see okay
and<00:04:30.110><c> that's</c><00:04:30.259><c> pretty</c><00:04:30.500><c> much</c><00:04:30.620><c> all</c><00:04:30.800><c> there</c><00:04:30.979><c> is</c><00:04:31.190><c> to</c>

00:04:31.420 --> 00:04:31.430 align:start position:0%
and that's pretty much all there is to
 

00:04:31.430 --> 00:04:33.640 align:start position:0%
and that's pretty much all there is to
it<00:04:31.580><c> so</c><00:04:31.910><c> in</c><00:04:32.570><c> the</c><00:04:32.720><c> next</c><00:04:32.960><c> video</c><00:04:33.290><c> we're</c><00:04:33.530><c> gonna</c>

00:04:33.640 --> 00:04:33.650 align:start position:0%
it so in the next video we're gonna
 

00:04:33.650 --> 00:04:36.460 align:start position:0%
it so in the next video we're gonna
actually<00:04:33.889><c> get</c><00:04:34.400><c> started</c><00:04:35.080><c> understanding</c><00:04:36.080><c> the</c>

00:04:36.460 --> 00:04:36.470 align:start position:0%
actually get started understanding the
 

00:04:36.470 --> 00:04:39.640 align:start position:0%
actually get started understanding the
layout<00:04:36.800><c> of</c><00:04:37.039><c> the</c><00:04:37.340><c> code</c><00:04:37.639><c> and</c><00:04:38.229><c> trying</c><00:04:39.229><c> to</c><00:04:39.380><c> plan</c>

00:04:39.640 --> 00:04:39.650 align:start position:0%
layout of the code and trying to plan
 

00:04:39.650 --> 00:04:42.400 align:start position:0%
layout of the code and trying to plan
what<00:04:40.310><c> kind</c><00:04:40.340><c> of</c><00:04:40.850><c> features</c><00:04:41.240><c> users</c><00:04:41.750><c> may</c><00:04:41.990><c> want</c><00:04:42.020><c> to</c>

00:04:42.400 --> 00:04:42.410 align:start position:0%
what kind of features users may want to
 

00:04:42.410 --> 00:04:44.529 align:start position:0%
what kind of features users may want to
have<00:04:42.759><c> we're</c><00:04:43.759><c> gonna</c><00:04:43.880><c> go</c><00:04:44.060><c> with</c><00:04:44.180><c> a</c><00:04:44.240><c> lean</c>

00:04:44.529 --> 00:04:44.539 align:start position:0%
have we're gonna go with a lean
 

00:04:44.539 --> 00:04:48.760 align:start position:0%
have we're gonna go with a lean
methodology<00:04:45.289><c> only</c><00:04:46.479><c> prioritize</c><00:04:47.479><c> the</c><00:04:48.320><c> features</c>

00:04:48.760 --> 00:04:48.770 align:start position:0%
methodology only prioritize the features
 

00:04:48.770 --> 00:04:51.129 align:start position:0%
methodology only prioritize the features
that<00:04:49.130><c> users</c><00:04:49.550><c> are</c><00:04:49.910><c> going</c><00:04:50.060><c> to</c><00:04:50.120><c> actually</c><00:04:50.750><c> enjoy</c>

00:04:51.129 --> 00:04:51.139 align:start position:0%
that users are going to actually enjoy
 

00:04:51.139 --> 00:04:53.409 align:start position:0%
that users are going to actually enjoy
and<00:04:51.410><c> appreciate</c><00:04:51.500><c> so</c><00:04:52.460><c> we'll</c><00:04:52.940><c> be</c><00:04:53.060><c> right</c><00:04:53.210><c> back</c>

00:04:53.409 --> 00:04:53.419 align:start position:0%
and appreciate so we'll be right back
 

00:04:53.419 --> 00:04:55.960 align:start position:0%
and appreciate so we'll be right back
after<00:04:53.660><c> this</c>

