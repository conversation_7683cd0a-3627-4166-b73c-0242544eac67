WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:01.790 align:start position:0%
 
okay<00:00:00.480><c> in</c><00:00:00.640><c> this</c><00:00:00.799><c> video</c><00:00:01.120><c> I'm</c><00:00:01.199><c> going</c><00:00:01.319><c> to</c><00:00:01.439><c> do</c><00:00:01.599><c> an</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
okay in this video I'm going to do an
 

00:00:01.800 --> 00:00:06.030 align:start position:0%
okay in this video I'm going to do an
update<00:00:02.480><c> about</c><00:00:02.919><c> the</c><00:00:03.240><c> mix</c><00:00:03.719><c> TR</c><00:00:04.279><c> of</c><00:00:04.839><c> experts</c><00:00:05.839><c> so</c>

00:00:06.030 --> 00:00:06.040 align:start position:0%
update about the mix TR of experts so
 

00:00:06.040 --> 00:00:08.629 align:start position:0%
update about the mix TR of experts so
basically<00:00:06.399><c> today</c><00:00:06.680><c> we</c><00:00:06.839><c> have</c><00:00:07.000><c> a</c><00:00:07.160><c> blog</c><00:00:07.520><c> post</c><00:00:08.000><c> out</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
basically today we have a blog post out
 

00:00:08.639 --> 00:00:10.910 align:start position:0%
basically today we have a blog post out
uh<00:00:08.760><c> a</c><00:00:08.880><c> few</c><00:00:09.160><c> hours</c><00:00:09.519><c> ago</c><00:00:10.200><c> and</c><00:00:10.360><c> also</c><00:00:10.599><c> looking</c><00:00:10.800><c> at</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
uh a few hours ago and also looking at
 

00:00:10.920 --> 00:00:12.669 align:start position:0%
uh a few hours ago and also looking at
some<00:00:11.080><c> of</c><00:00:11.200><c> the</c><00:00:11.280><c> code</c><00:00:11.519><c> earlier</c><00:00:11.880><c> on</c><00:00:12.120><c> today</c><00:00:12.559><c> I</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
some of the code earlier on today I
 

00:00:12.679 --> 00:00:14.869 align:start position:0%
some of the code earlier on today I
realized<00:00:13.240><c> that</c><00:00:13.679><c> my</c><00:00:13.880><c> diagram</c><00:00:14.360><c> wasn't</c><00:00:14.679><c> that</c>

00:00:14.869 --> 00:00:14.879 align:start position:0%
realized that my diagram wasn't that
 

00:00:14.879 --> 00:00:17.150 align:start position:0%
realized that my diagram wasn't that
good<00:00:15.040><c> so</c><00:00:15.160><c> I</c><00:00:15.240><c> want</c><00:00:15.320><c> to</c><00:00:15.480><c> go</c><00:00:15.799><c> through</c><00:00:16.240><c> and</c><00:00:16.440><c> explain</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
good so I want to go through and explain
 

00:00:17.160 --> 00:00:18.670 align:start position:0%
good so I want to go through and explain
a<00:00:17.279><c> little</c><00:00:17.439><c> bit</c><00:00:17.560><c> more</c><00:00:17.720><c> of</c><00:00:17.920><c> how</c><00:00:18.080><c> they're</c><00:00:18.320><c> doing</c>

00:00:18.670 --> 00:00:18.680 align:start position:0%
a little bit more of how they're doing
 

00:00:18.680 --> 00:00:22.310 align:start position:0%
a little bit more of how they're doing
this<00:00:19.320><c> with</c><00:00:19.760><c> a</c><00:00:20.560><c> Transformer</c><00:00:21.560><c> version</c><00:00:22.039><c> of</c><00:00:22.199><c> the</c>

00:00:22.310 --> 00:00:22.320 align:start position:0%
this with a Transformer version of the
 

00:00:22.320 --> 00:00:25.310 align:start position:0%
this with a Transformer version of the
mix<00:00:22.760><c> of</c><00:00:23.320><c> experts</c><00:00:24.320><c> so</c><00:00:24.519><c> first</c><00:00:24.720><c> off</c><00:00:24.920><c> the</c><00:00:25.039><c> blog</c>

00:00:25.310 --> 00:00:25.320 align:start position:0%
mix of experts so first off the blog
 

00:00:25.320 --> 00:00:27.269 align:start position:0%
mix of experts so first off the blog
post<00:00:25.560><c> is</c><00:00:25.760><c> out</c><00:00:26.039><c> and</c><00:00:26.160><c> it</c><00:00:26.279><c> goes</c><00:00:26.480><c> into</c><00:00:26.800><c> detail</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
post is out and it goes into detail
 

00:00:27.279 --> 00:00:29.710 align:start position:0%
post is out and it goes into detail
about<00:00:27.599><c> what</c><00:00:27.800><c> they've</c><00:00:28.080><c> actually</c><00:00:29.000><c> uh</c><00:00:29.119><c> released</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
about what they've actually uh released
 

00:00:29.720 --> 00:00:31.910 align:start position:0%
about what they've actually uh released
now<00:00:30.119><c> so</c><00:00:30.279><c> we've</c><00:00:30.439><c> got</c><00:00:30.599><c> a</c><00:00:30.720><c> lot</c><00:00:30.880><c> more</c><00:00:31.119><c> than</c><00:00:31.400><c> just</c><00:00:31.720><c> a</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
now so we've got a lot more than just a
 

00:00:31.920 --> 00:00:33.830 align:start position:0%
now so we've got a lot more than just a
simple<00:00:32.279><c> torrent</c><00:00:32.680><c> link</c><00:00:33.320><c> we've</c><00:00:33.480><c> also</c><00:00:33.640><c> got</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
simple torrent link we've also got
 

00:00:33.840 --> 00:00:36.350 align:start position:0%
simple torrent link we've also got
another<00:00:34.120><c> model</c><00:00:34.559><c> out</c><00:00:35.160><c> just</c><00:00:35.320><c> in</c><00:00:35.520><c> the</c><00:00:35.680><c> past</c><00:00:36.079><c> few</c>

00:00:36.350 --> 00:00:36.360 align:start position:0%
another model out just in the past few
 

00:00:36.360 --> 00:00:38.229 align:start position:0%
another model out just in the past few
hours<00:00:36.760><c> as</c><00:00:36.960><c> well</c><00:00:37.360><c> so</c><00:00:37.520><c> what</c><00:00:37.600><c> they</c><00:00:37.719><c> talk</c><00:00:37.960><c> about</c><00:00:38.120><c> in</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
hours as well so what they talk about in
 

00:00:38.239 --> 00:00:40.229 align:start position:0%
hours as well so what they talk about in
the<00:00:38.360><c> blog</c><00:00:38.600><c> post</c><00:00:38.840><c> is</c><00:00:39.000><c> some</c><00:00:39.200><c> details</c><00:00:39.719><c> about</c><00:00:40.079><c> the</c>

00:00:40.229 --> 00:00:40.239 align:start position:0%
the blog post is some details about the
 

00:00:40.239 --> 00:00:43.190 align:start position:0%
the blog post is some details about the
model<00:00:41.120><c> obviously</c><00:00:41.559><c> one</c><00:00:41.680><c> of</c><00:00:41.840><c> the</c><00:00:42.120><c> sort</c><00:00:42.320><c> of</c><00:00:42.640><c> major</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
model obviously one of the sort of major
 

00:00:43.200 --> 00:00:45.389 align:start position:0%
model obviously one of the sort of major
details<00:00:43.960><c> is</c><00:00:44.200><c> that</c><00:00:44.360><c> it</c><00:00:44.680><c> matches</c><00:00:45.160><c> or</c>

00:00:45.389 --> 00:00:45.399 align:start position:0%
details is that it matches or
 

00:00:45.399 --> 00:00:48.590 align:start position:0%
details is that it matches or
outperforms<00:00:46.199><c> GPT</c><00:00:46.879><c> 3.5</c><00:00:47.879><c> on</c><00:00:48.120><c> most</c><00:00:48.320><c> of</c><00:00:48.480><c> the</c>

00:00:48.590 --> 00:00:48.600 align:start position:0%
outperforms GPT 3.5 on most of the
 

00:00:48.600 --> 00:00:50.790 align:start position:0%
outperforms GPT 3.5 on most of the
standard<00:00:49.039><c> benchmarks</c><00:00:50.039><c> as</c><00:00:50.120><c> we</c><00:00:50.239><c> can</c><00:00:50.360><c> look</c><00:00:50.480><c> at</c><00:00:50.680><c> in</c>

00:00:50.790 --> 00:00:50.800 align:start position:0%
standard benchmarks as we can look at in
 

00:00:50.800 --> 00:00:52.709 align:start position:0%
standard benchmarks as we can look at in
a<00:00:51.000><c> second</c><00:00:51.760><c> the</c><00:00:51.840><c> other</c><00:00:52.039><c> thing</c><00:00:52.160><c> too</c><00:00:52.440><c> is</c><00:00:52.559><c> that</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
a second the other thing too is that
 

00:00:52.719 --> 00:00:55.069 align:start position:0%
a second the other thing too is that
it's<00:00:52.879><c> got</c><00:00:53.079><c> details</c><00:00:53.640><c> a</c><00:00:53.719><c> little</c><00:00:53.960><c> bit</c><00:00:54.160><c> more</c><00:00:54.520><c> about</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
it's got details a little bit more about
 

00:00:55.079 --> 00:00:57.229 align:start position:0%
it's got details a little bit more about
the<00:00:55.239><c> languages</c><00:00:55.760><c> that</c><00:00:56.079><c> it</c><00:00:56.280><c> supports</c><00:00:56.879><c> being</c>

00:00:57.229 --> 00:00:57.239 align:start position:0%
the languages that it supports being
 

00:00:57.239 --> 00:00:59.349 align:start position:0%
the languages that it supports being
English<00:00:57.800><c> French</c><00:00:58.280><c> Italian</c><00:00:58.800><c> German</c><00:00:59.120><c> and</c>

00:00:59.349 --> 00:00:59.359 align:start position:0%
English French Italian German and
 

00:00:59.359 --> 00:01:02.349 align:start position:0%
English French Italian German and
Spanish<00:01:00.399><c> unfortunately</c><00:01:00.960><c> no</c><00:01:01.480><c> Asian</c><00:01:01.840><c> languages</c>

00:01:02.349 --> 00:01:02.359 align:start position:0%
Spanish unfortunately no Asian languages
 

00:01:02.359 --> 00:01:04.990 align:start position:0%
Spanish unfortunately no Asian languages
or<00:01:02.640><c> other</c><00:01:02.920><c> languages</c><00:01:03.800><c> apart</c><00:01:04.080><c> from</c><00:01:04.360><c> these</c><00:01:04.879><c> and</c>

00:01:04.990 --> 00:01:05.000 align:start position:0%
or other languages apart from these and
 

00:01:05.000 --> 00:01:07.630 align:start position:0%
or other languages apart from these and
it's<00:01:05.239><c> also</c><00:01:05.600><c> got</c><00:01:05.880><c> a</c><00:01:06.000><c> lot</c><00:01:06.119><c> of</c><00:01:06.240><c> the</c><00:01:06.479><c> details</c><00:01:07.320><c> about</c>

00:01:07.630 --> 00:01:07.640 align:start position:0%
it's also got a lot of the details about
 

00:01:07.640 --> 00:01:10.070 align:start position:0%
it's also got a lot of the details about
the<00:01:07.960><c> actual</c><00:01:08.600><c> model</c><00:01:09.000><c> itself</c><00:01:09.680><c> so</c><00:01:09.880><c> not</c>

00:01:10.070 --> 00:01:10.080 align:start position:0%
the actual model itself so not
 

00:01:10.080 --> 00:01:12.749 align:start position:0%
the actual model itself so not
surprising<00:01:10.600><c> this</c><00:01:10.680><c> is</c><00:01:10.799><c> a</c><00:01:10.960><c> decoder</c><00:01:11.520><c> only</c><00:01:11.960><c> model</c>

00:01:12.749 --> 00:01:12.759 align:start position:0%
surprising this is a decoder only model
 

00:01:12.759 --> 00:01:14.469 align:start position:0%
surprising this is a decoder only model
the<00:01:12.880><c> thing</c><00:01:13.040><c> that</c><00:01:13.159><c> is</c><00:01:13.479><c> different</c><00:01:14.000><c> than</c><00:01:14.240><c> what</c><00:01:14.360><c> I</c>

00:01:14.469 --> 00:01:14.479 align:start position:0%
the thing that is different than what I
 

00:01:14.479 --> 00:01:16.510 align:start position:0%
the thing that is different than what I
explained<00:01:14.840><c> in</c><00:01:14.920><c> the</c><00:01:15.159><c> last</c><00:01:15.600><c> video</c><00:01:16.119><c> with</c><00:01:16.240><c> some</c><00:01:16.400><c> of</c>

00:01:16.510 --> 00:01:16.520 align:start position:0%
explained in the last video with some of
 

00:01:16.520 --> 00:01:18.310 align:start position:0%
explained in the last video with some of
the<00:01:16.720><c> diagrams</c><00:01:17.680><c> is</c><00:01:17.840><c> that</c><00:01:18.000><c> what</c><00:01:18.119><c> they're</c>

00:01:18.310 --> 00:01:18.320 align:start position:0%
the diagrams is that what they're
 

00:01:18.320 --> 00:01:20.270 align:start position:0%
the diagrams is that what they're
actually<00:01:18.520><c> doing</c><00:01:18.920><c> here</c><00:01:19.240><c> is</c><00:01:19.479><c> they're</c><00:01:20.200><c> uh</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
actually doing here is they're uh
 

00:01:20.280 --> 00:01:23.510 align:start position:0%
actually doing here is they're uh
sharing<00:01:20.880><c> key</c><00:01:21.240><c> parts</c><00:01:21.680><c> of</c><00:01:21.840><c> the</c><00:01:21.960><c> decoder</c><00:01:22.600><c> Network</c>

00:01:23.510 --> 00:01:23.520 align:start position:0%
sharing key parts of the decoder Network
 

00:01:23.520 --> 00:01:25.710 align:start position:0%
sharing key parts of the decoder Network
and<00:01:23.680><c> they're</c><00:01:24.040><c> actually</c><00:01:24.520><c> having</c><00:01:25.000><c> the</c><00:01:25.119><c> sort</c><00:01:25.320><c> of</c>

00:01:25.710 --> 00:01:25.720 align:start position:0%
and they're actually having the sort of
 

00:01:25.720 --> 00:01:29.149 align:start position:0%
and they're actually having the sort of
experts<00:01:26.720><c> in</c><00:01:26.920><c> the</c><00:01:27.079><c> feed</c><00:01:27.479><c> forward</c><00:01:27.960><c> blocks</c><00:01:28.799><c> so</c><00:01:29.000><c> at</c>

00:01:29.149 --> 00:01:29.159 align:start position:0%
experts in the feed forward blocks so at
 

00:01:29.159 --> 00:01:31.230 align:start position:0%
experts in the feed forward blocks so at
every<00:01:29.360><c> layer</c><00:01:29.920><c> they</c><00:01:30.079><c> have</c><00:01:30.320><c> these</c><00:01:30.640><c> router</c>

00:01:31.230 --> 00:01:31.240 align:start position:0%
every layer they have these router
 

00:01:31.240 --> 00:01:34.990 align:start position:0%
every layer they have these router
networks<00:01:32.200><c> that</c><00:01:32.439><c> basically</c><00:01:33.040><c> choose</c><00:01:34.040><c> uh</c><00:01:34.200><c> two</c><00:01:34.720><c> of</c>

00:01:34.990 --> 00:01:35.000 align:start position:0%
networks that basically choose uh two of
 

00:01:35.000 --> 00:01:37.149 align:start position:0%
networks that basically choose uh two of
the<00:01:35.159><c> groups</c><00:01:35.479><c> of</c><00:01:35.840><c> experts</c><00:01:36.320><c> to</c><00:01:36.600><c> process</c><00:01:37.000><c> the</c>

00:01:37.149 --> 00:01:37.159 align:start position:0%
the groups of experts to process the
 

00:01:37.159 --> 00:01:40.230 align:start position:0%
the groups of experts to process the
token<00:01:37.960><c> and</c><00:01:38.079><c> then</c><00:01:38.280><c> combine</c><00:01:38.840><c> this</c><00:01:39.520><c> so</c><00:01:39.720><c> this</c><00:01:39.840><c> is</c><00:01:39.960><c> a</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
token and then combine this so this is a
 

00:01:40.240 --> 00:01:42.749 align:start position:0%
token and then combine this so this is a
diagram<00:01:40.720><c> showing</c><00:01:41.159><c> this</c><00:01:41.479><c> concept</c><00:01:42.079><c> from</c><00:01:42.520><c> the</c>

00:01:42.749 --> 00:01:42.759 align:start position:0%
diagram showing this concept from the
 

00:01:42.759 --> 00:01:44.830 align:start position:0%
diagram showing this concept from the
switch<00:01:43.200><c> Transformer</c><00:01:44.040><c> which</c><00:01:44.159><c> I</c><00:01:44.280><c> talked</c><00:01:44.560><c> about</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
switch Transformer which I talked about
 

00:01:44.840 --> 00:01:47.069 align:start position:0%
switch Transformer which I talked about
in<00:01:44.960><c> the</c><00:01:45.200><c> last</c><00:01:45.479><c> video</c><00:01:46.280><c> so</c><00:01:46.439><c> you</c><00:01:46.520><c> can</c><00:01:46.640><c> see</c><00:01:46.880><c> that</c>

00:01:47.069 --> 00:01:47.079 align:start position:0%
in the last video so you can see that
 

00:01:47.079 --> 00:01:48.630 align:start position:0%
in the last video so you can see that
you've<00:01:47.280><c> got</c><00:01:47.399><c> the</c><00:01:47.520><c> normal</c><00:01:47.840><c> self</c><00:01:48.159><c> attention</c>

00:01:48.630 --> 00:01:48.640 align:start position:0%
you've got the normal self attention
 

00:01:48.640 --> 00:01:51.270 align:start position:0%
you've got the normal self attention
going<00:01:48.960><c> through</c><00:01:49.600><c> of</c><00:01:49.759><c> a</c><00:01:49.960><c> transformer</c><00:01:50.960><c> so</c><00:01:51.119><c> we've</c>

00:01:51.270 --> 00:01:51.280 align:start position:0%
going through of a transformer so we've
 

00:01:51.280 --> 00:01:52.990 align:start position:0%
going through of a transformer so we've
got<00:01:51.399><c> the</c><00:01:51.520><c> normal</c><00:01:51.920><c> parts</c><00:01:52.320><c> of</c><00:01:52.520><c> the</c><00:01:52.680><c> self</c>

00:01:52.990 --> 00:01:53.000 align:start position:0%
got the normal parts of the self
 

00:01:53.000 --> 00:01:56.149 align:start position:0%
got the normal parts of the self
attention<00:01:53.680><c> and</c><00:01:53.880><c> the</c><00:01:54.479><c> Mast</c><00:01:54.880><c> of</c><00:01:55.000><c> a</c><00:01:55.159><c> decoder</c><00:01:56.000><c> part</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
attention and the Mast of a decoder part
 

00:01:56.159 --> 00:01:58.709 align:start position:0%
attention and the Mast of a decoder part
of<00:01:56.280><c> a</c><00:01:56.439><c> transformer</c><00:01:57.159><c> going</c><00:01:57.360><c> on</c><00:01:57.600><c> in</c><00:01:57.840><c> here</c><00:01:58.560><c> but</c>

00:01:58.709 --> 00:01:58.719 align:start position:0%
of a transformer going on in here but
 

00:01:58.719 --> 00:02:00.350 align:start position:0%
of a transformer going on in here but
then<00:01:58.880><c> when</c><00:01:59.000><c> we</c><00:01:59.159><c> get</c><00:01:59.280><c> to</c><00:01:59.439><c> the</c><00:01:59.520><c> feed</c><00:01:59.960><c> forward</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
then when we get to the feed forward
 

00:02:00.360 --> 00:02:03.149 align:start position:0%
then when we get to the feed forward
layers<00:02:01.240><c> you</c><00:02:01.479><c> get</c><00:02:01.680><c> this</c><00:02:01.880><c> router</c><00:02:02.439><c> system</c><00:02:02.880><c> going</c>

00:02:03.149 --> 00:02:03.159 align:start position:0%
layers you get this router system going
 

00:02:03.159 --> 00:02:06.230 align:start position:0%
layers you get this router system going
on<00:02:03.640><c> where</c><00:02:03.960><c> it's</c><00:02:04.200><c> going</c><00:02:04.399><c> to</c><00:02:04.799><c> basically</c><00:02:05.799><c> have</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
on where it's going to basically have
 

00:02:06.240 --> 00:02:08.869 align:start position:0%
on where it's going to basically have
the<00:02:06.600><c> experts</c><00:02:07.520><c> go</c><00:02:07.799><c> through</c><00:02:08.160><c> this</c><00:02:08.640><c> and</c><00:02:08.759><c> then</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
the experts go through this and then
 

00:02:08.879 --> 00:02:10.669 align:start position:0%
the experts go through this and then
they<00:02:09.039><c> join</c><00:02:09.319><c> back</c><00:02:09.640><c> together</c><00:02:09.920><c> and</c><00:02:10.119><c> go</c><00:02:10.280><c> to</c><00:02:10.479><c> the</c>

00:02:10.669 --> 00:02:10.679 align:start position:0%
they join back together and go to the
 

00:02:10.679 --> 00:02:12.869 align:start position:0%
they join back together and go to the
next<00:02:10.959><c> layer</c><00:02:11.400><c> where</c><00:02:11.720><c> again</c><00:02:12.040><c> they</c><00:02:12.280><c> share</c><00:02:12.760><c> the</c>

00:02:12.869 --> 00:02:12.879 align:start position:0%
next layer where again they share the
 

00:02:12.879 --> 00:02:14.990 align:start position:0%
next layer where again they share the
self<00:02:13.200><c> attention</c><00:02:14.120><c> and</c><00:02:14.239><c> split</c><00:02:14.560><c> up</c><00:02:14.720><c> for</c><00:02:14.879><c> the</c>

00:02:14.990 --> 00:02:15.000 align:start position:0%
self attention and split up for the
 

00:02:15.000 --> 00:02:17.070 align:start position:0%
self attention and split up for the
various<00:02:15.440><c> experts</c><00:02:16.160><c> as</c><00:02:16.280><c> it's</c><00:02:16.440><c> going</c><00:02:16.760><c> through</c>

00:02:17.070 --> 00:02:17.080 align:start position:0%
various experts as it's going through
 

00:02:17.080 --> 00:02:19.350 align:start position:0%
various experts as it's going through
this<00:02:17.840><c> so</c><00:02:18.000><c> the</c><00:02:18.120><c> concept</c><00:02:18.440><c> of</c><00:02:18.599><c> thinking</c><00:02:18.879><c> of</c><00:02:19.040><c> it</c><00:02:19.200><c> as</c>

00:02:19.350 --> 00:02:19.360 align:start position:0%
this so the concept of thinking of it as
 

00:02:19.360 --> 00:02:21.589 align:start position:0%
this so the concept of thinking of it as
being<00:02:19.599><c> 8</c><00:02:20.040><c> time</c><00:02:20.400><c> 7</c><00:02:20.720><c> billion</c><00:02:21.200><c> is</c><00:02:21.319><c> a</c><00:02:21.440><c> bit</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
being 8 time 7 billion is a bit
 

00:02:21.599 --> 00:02:24.630 align:start position:0%
being 8 time 7 billion is a bit
misleading<00:02:22.280><c> so</c><00:02:22.599><c> actually</c><00:02:23.000><c> the</c><00:02:23.120><c> mix</c><00:02:23.640><c> has</c><00:02:24.080><c> 45</c>

00:02:24.630 --> 00:02:24.640 align:start position:0%
misleading so actually the mix has 45
 

00:02:24.640 --> 00:02:26.949 align:start position:0%
misleading so actually the mix has 45
billion<00:02:25.040><c> parameters</c><00:02:25.599><c> in</c><00:02:25.840><c> total</c><00:02:26.599><c> but</c><00:02:26.720><c> it's</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
billion parameters in total but it's
 

00:02:26.959 --> 00:02:29.949 align:start position:0%
billion parameters in total but it's
only<00:02:27.319><c> using</c><00:02:27.800><c> 12</c><00:02:28.200><c> billion</c><00:02:28.680><c> parameters</c><00:02:29.560><c> per</c>

00:02:29.949 --> 00:02:29.959 align:start position:0%
only using 12 billion parameters per
 

00:02:29.959 --> 00:02:31.509 align:start position:0%
only using 12 billion parameters per
token<00:02:30.599><c> so</c><00:02:30.760><c> therefore</c><00:02:31.000><c> you're</c><00:02:31.120><c> getting</c><00:02:31.360><c> the</c>

00:02:31.509 --> 00:02:31.519 align:start position:0%
token so therefore you're getting the
 

00:02:31.519 --> 00:02:33.630 align:start position:0%
token so therefore you're getting the
advantages<00:02:32.080><c> of</c><00:02:32.239><c> having</c><00:02:32.440><c> the</c><00:02:32.599><c> big</c><00:02:32.920><c> model</c><00:02:33.480><c> but</c>

00:02:33.630 --> 00:02:33.640 align:start position:0%
advantages of having the big model but
 

00:02:33.640 --> 00:02:35.949 align:start position:0%
advantages of having the big model but
not<00:02:33.840><c> having</c><00:02:34.080><c> to</c><00:02:34.280><c> use</c><00:02:34.640><c> that</c><00:02:34.959><c> and</c><00:02:35.360><c> for</c><00:02:35.720><c> actually</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
not having to use that and for actually
 

00:02:35.959 --> 00:02:38.670 align:start position:0%
not having to use that and for actually
doing<00:02:36.360><c> the</c><00:02:36.920><c> compute</c><00:02:37.480><c> and</c><00:02:37.800><c> processing</c><00:02:38.239><c> it</c><00:02:38.400><c> for</c>

00:02:38.670 --> 00:02:38.680 align:start position:0%
doing the compute and processing it for
 

00:02:38.680 --> 00:02:41.470 align:start position:0%
doing the compute and processing it for
generating<00:02:39.280><c> out</c><00:02:40.040><c> here</c><00:02:40.440><c> so</c><00:02:40.640><c> they</c><00:02:40.760><c> mention</c><00:02:41.200><c> here</c>

00:02:41.470 --> 00:02:41.480 align:start position:0%
generating out here so they mention here
 

00:02:41.480 --> 00:02:43.430 align:start position:0%
generating out here so they mention here
that<00:02:41.640><c> the</c><00:02:41.760><c> model</c><00:02:42.400><c> processes</c><00:02:42.959><c> input</c><00:02:43.319><c> and</c>

00:02:43.430 --> 00:02:43.440 align:start position:0%
that the model processes input and
 

00:02:43.440 --> 00:02:45.790 align:start position:0%
that the model processes input and
generates<00:02:43.920><c> output</c><00:02:44.440><c> at</c><00:02:44.560><c> the</c><00:02:44.720><c> same</c><00:02:45.000><c> speed</c><00:02:45.519><c> and</c>

00:02:45.790 --> 00:02:45.800 align:start position:0%
generates output at the same speed and
 

00:02:45.800 --> 00:02:48.830 align:start position:0%
generates output at the same speed and
cost<00:02:46.080><c> as</c><00:02:46.200><c> a</c><00:02:46.360><c> 12</c><00:02:46.720><c> billion</c><00:02:47.120><c> model</c><00:02:47.920><c> so</c><00:02:48.400><c> for</c><00:02:48.640><c> less</c>

00:02:48.830 --> 00:02:48.840 align:start position:0%
cost as a 12 billion model so for less
 

00:02:48.840 --> 00:02:50.670 align:start position:0%
cost as a 12 billion model so for less
than<00:02:49.040><c> double</c><00:02:49.360><c> the</c><00:02:49.560><c> size</c><00:02:49.879><c> of</c><00:02:50.040><c> the</c><00:02:50.239><c> compute</c>

00:02:50.670 --> 00:02:50.680 align:start position:0%
than double the size of the compute
 

00:02:50.680 --> 00:02:53.070 align:start position:0%
than double the size of the compute
needed<00:02:51.040><c> for</c><00:02:51.280><c> doing</c><00:02:51.959><c> the</c><00:02:52.159><c> inference</c><00:02:52.800><c> we're</c>

00:02:53.070 --> 00:02:53.080 align:start position:0%
needed for doing the inference we're
 

00:02:53.080 --> 00:02:55.790 align:start position:0%
needed for doing the inference we're
able<00:02:53.360><c> to</c><00:02:53.800><c> get</c><00:02:53.959><c> a</c><00:02:54.080><c> model</c><00:02:54.400><c> that's</c><00:02:54.720><c> much</c><00:02:55.040><c> better</c>

00:02:55.790 --> 00:02:55.800 align:start position:0%
able to get a model that's much better
 

00:02:55.800 --> 00:02:57.630 align:start position:0%
able to get a model that's much better
performance<00:02:56.440><c> here</c><00:02:56.959><c> so</c><00:02:57.120><c> let's</c><00:02:57.280><c> have</c><00:02:57.360><c> a</c><00:02:57.440><c> look</c><00:02:57.560><c> at</c>

00:02:57.630 --> 00:02:57.640 align:start position:0%
performance here so let's have a look at
 

00:02:57.640 --> 00:02:59.990 align:start position:0%
performance here so let's have a look at
some<00:02:57.760><c> of</c><00:02:57.879><c> those</c><00:02:58.080><c> performance</c><00:02:58.599><c> stats</c><00:02:59.400><c> so</c><00:02:59.840><c> quick</c>

00:02:59.990 --> 00:03:00.000 align:start position:0%
some of those performance stats so quick
 

00:03:00.000 --> 00:03:03.149 align:start position:0%
some of those performance stats so quick
shout<00:03:00.200><c> out</c><00:03:00.400><c> to</c><00:03:00.720><c> vicat</c><00:03:01.720><c> and</c><00:03:01.879><c> this</c><00:03:02.080><c> G</c><00:03:02.360><c> repo</c><00:03:02.879><c> for</c>

00:03:03.149 --> 00:03:03.159 align:start position:0%
shout out to vicat and this G repo for
 

00:03:03.159 --> 00:03:05.470 align:start position:0%
shout out to vicat and this G repo for
mixtur<00:03:03.840><c> inference</c><00:03:04.480><c> in</c><00:03:04.680><c> here</c><00:03:05.200><c> you</c><00:03:05.280><c> can</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
mixtur inference in here you can
 

00:03:05.480 --> 00:03:07.949 align:start position:0%
mixtur inference in here you can
actually<00:03:05.799><c> look</c><00:03:05.959><c> at</c><00:03:06.159><c> the</c><00:03:06.360><c> code</c><00:03:06.799><c> of</c><00:03:07.040><c> how</c><00:03:07.280><c> this</c><00:03:07.400><c> is</c>

00:03:07.949 --> 00:03:07.959 align:start position:0%
actually look at the code of how this is
 

00:03:07.959 --> 00:03:10.750 align:start position:0%
actually look at the code of how this is
uh<00:03:08.120><c> basically</c><00:03:08.519><c> being</c><00:03:08.799><c> done</c><00:03:09.560><c> and</c><00:03:09.760><c> see</c><00:03:10.519><c> okay</c>

00:03:10.750 --> 00:03:10.760 align:start position:0%
uh basically being done and see okay
 

00:03:10.760 --> 00:03:12.830 align:start position:0%
uh basically being done and see okay
what<00:03:10.920><c> is</c><00:03:11.120><c> actually</c><00:03:11.400><c> going</c><00:03:11.680><c> on</c><00:03:12.360><c> with</c><00:03:12.640><c> these</c>

00:03:12.830 --> 00:03:12.840 align:start position:0%
what is actually going on with these
 

00:03:12.840 --> 00:03:15.030 align:start position:0%
what is actually going on with these
feed<00:03:13.239><c> forward</c><00:03:13.680><c> layers</c><00:03:14.560><c> and</c><00:03:14.720><c> how</c><00:03:14.840><c> it's</c>

00:03:15.030 --> 00:03:15.040 align:start position:0%
feed forward layers and how it's
 

00:03:15.040 --> 00:03:16.949 align:start position:0%
feed forward layers and how it's
actually<00:03:15.280><c> gating</c><00:03:15.799><c> things</c><00:03:16.159><c> out</c><00:03:16.640><c> and</c><00:03:16.760><c> then</c>

00:03:16.949 --> 00:03:16.959 align:start position:0%
actually gating things out and then
 

00:03:16.959 --> 00:03:20.390 align:start position:0%
actually gating things out and then
taking<00:03:17.599><c> the</c><00:03:17.799><c> top</c><00:03:18.040><c> two</c><00:03:18.560><c> experts</c><00:03:19.159><c> from</c><00:03:19.560><c> this</c><00:03:20.159><c> so</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
taking the top two experts from this so
 

00:03:20.400 --> 00:03:22.110 align:start position:0%
taking the top two experts from this so
this<00:03:20.519><c> is</c><00:03:20.720><c> a</c><00:03:20.840><c> good</c><00:03:21.000><c> repo</c><00:03:21.440><c> to</c><00:03:21.560><c> study</c><00:03:21.840><c> if</c><00:03:21.920><c> you</c><00:03:22.000><c> want</c>

00:03:22.110 --> 00:03:22.120 align:start position:0%
this is a good repo to study if you want
 

00:03:22.120 --> 00:03:24.990 align:start position:0%
this is a good repo to study if you want
to<00:03:22.239><c> see</c><00:03:22.560><c> how</c><00:03:22.799><c> this</c><00:03:22.959><c> is</c><00:03:23.200><c> done</c><00:03:23.959><c> as</c><00:03:24.120><c> well</c><00:03:24.760><c> so</c><00:03:24.920><c> you</c>

00:03:24.990 --> 00:03:25.000 align:start position:0%
to see how this is done as well so you
 

00:03:25.000 --> 00:03:27.750 align:start position:0%
to see how this is done as well so you
can<00:03:25.159><c> see</c><00:03:25.519><c> here</c><00:03:26.080><c> that</c><00:03:26.280><c> they</c><00:03:26.519><c> compare</c><00:03:27.159><c> to</c><00:03:27.599><c> the</c>

00:03:27.750 --> 00:03:27.760 align:start position:0%
can see here that they compare to the
 

00:03:27.760 --> 00:03:30.350 align:start position:0%
can see here that they compare to the
Llama<00:03:28.239><c> 70</c><00:03:28.760><c> billion</c><00:03:29.159><c> model</c><00:03:29.799><c> and</c><00:03:29.920><c> they're</c><00:03:30.080><c> far</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
Llama 70 billion model and they're far
 

00:03:30.360 --> 00:03:32.229 align:start position:0%
Llama 70 billion model and they're far
smaller<00:03:30.720><c> than</c><00:03:30.920><c> that</c><00:03:31.200><c> even</c><00:03:31.480><c> with</c><00:03:31.599><c> the</c><00:03:31.760><c> 45</c>

00:03:32.229 --> 00:03:32.239 align:start position:0%
smaller than that even with the 45
 

00:03:32.239 --> 00:03:35.750 align:start position:0%
smaller than that even with the 45
billion<00:03:32.720><c> parameters</c><00:03:33.360><c> here</c><00:03:33.799><c> you</c><00:03:34.000><c> see</c><00:03:34.159><c> on</c><00:03:34.360><c> MML</c><00:03:35.159><c> U</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
billion parameters here you see on MML U
 

00:03:35.760 --> 00:03:38.830 align:start position:0%
billion parameters here you see on MML U
it's<00:03:36.280><c> not</c><00:03:36.480><c> only</c><00:03:36.720><c> beating</c><00:03:37.319><c> llama</c><00:03:37.720><c> 2</c><00:03:38.040><c> 70</c><00:03:38.480><c> billion</c>

00:03:38.830 --> 00:03:38.840 align:start position:0%
it's not only beating llama 2 70 billion
 

00:03:38.840 --> 00:03:40.949 align:start position:0%
it's not only beating llama 2 70 billion
but<00:03:38.959><c> it's</c><00:03:39.239><c> also</c><00:03:40.080><c> and</c><00:03:40.200><c> on</c><00:03:40.360><c> most</c><00:03:40.519><c> of</c><00:03:40.640><c> the</c><00:03:40.720><c> other</c>

00:03:40.949 --> 00:03:40.959 align:start position:0%
but it's also and on most of the other
 

00:03:40.959 --> 00:03:43.070 align:start position:0%
but it's also and on most of the other
benchmarks<00:03:41.560><c> they're</c><00:03:41.799><c> also</c><00:03:42.519><c> you</c><00:03:42.799><c> either</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
benchmarks they're also you either
 

00:03:43.080 --> 00:03:46.670 align:start position:0%
benchmarks they're also you either
beating<00:03:43.720><c> GPT</c><00:03:44.360><c> 3.5</c><00:03:45.360><c> or</c><00:03:45.640><c> coming</c><00:03:46.000><c> very</c><00:03:46.400><c> you</c><00:03:46.480><c> know</c>

00:03:46.670 --> 00:03:46.680 align:start position:0%
beating GPT 3.5 or coming very you know
 

00:03:46.680 --> 00:03:49.110 align:start position:0%
beating GPT 3.5 or coming very you know
close<00:03:46.920><c> to</c><00:03:47.159><c> matching</c><00:03:47.599><c> it</c><00:03:48.000><c> in</c><00:03:48.239><c> here</c><00:03:48.720><c> so</c><00:03:48.879><c> one</c><00:03:49.000><c> of</c>

00:03:49.110 --> 00:03:49.120 align:start position:0%
close to matching it in here so one of
 

00:03:49.120 --> 00:03:50.470 align:start position:0%
close to matching it in here so one of
the<00:03:49.200><c> cool</c><00:03:49.400><c> things</c><00:03:49.599><c> that</c><00:03:49.760><c> they've</c><00:03:50.040><c> also</c><00:03:50.280><c> done</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
the cool things that they've also done
 

00:03:50.480 --> 00:03:52.830 align:start position:0%
the cool things that they've also done
today<00:03:50.959><c> is</c><00:03:51.159><c> just</c><00:03:51.400><c> released</c><00:03:52.159><c> literally</c><00:03:52.680><c> a</c>

00:03:52.830 --> 00:03:52.840 align:start position:0%
today is just released literally a
 

00:03:52.840 --> 00:03:56.110 align:start position:0%
today is just released literally a
couple<00:03:53.040><c> of</c><00:03:53.200><c> hours</c><00:03:53.480><c> ago</c><00:03:54.319><c> a</c><00:03:54.879><c> instruct</c><00:03:55.599><c> version</c>

00:03:56.110 --> 00:03:56.120 align:start position:0%
couple of hours ago a instruct version
 

00:03:56.120 --> 00:03:58.710 align:start position:0%
couple of hours ago a instruct version
of<00:03:56.360><c> this</c><00:03:56.599><c> model</c><00:03:57.400><c> so</c><00:03:57.920><c> previously</c><00:03:58.480><c> they</c><00:03:58.560><c> had</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
of this model so previously they had
 

00:03:58.720 --> 00:04:00.789 align:start position:0%
of this model so previously they had
released<00:03:59.120><c> the</c><00:03:59.239><c> base</c><00:03:59.720><c> model</c><00:04:00.280><c> the</c><00:04:00.360><c> one</c><00:04:00.480><c> I</c><00:04:00.560><c> showed</c>

00:04:00.789 --> 00:04:00.799 align:start position:0%
released the base model the one I showed
 

00:04:00.799 --> 00:04:02.710 align:start position:0%
released the base model the one I showed
you<00:04:01.000><c> yesterday</c><00:04:01.400><c> was</c><00:04:01.599><c> releasing</c><00:04:02.280><c> the</c><00:04:02.439><c> Bas</c>

00:04:02.710 --> 00:04:02.720 align:start position:0%
you yesterday was releasing the Bas
 

00:04:02.720 --> 00:04:05.069 align:start position:0%
you yesterday was releasing the Bas
model<00:04:03.280><c> this</c><00:04:03.400><c> is</c><00:04:03.519><c> an</c><00:04:03.680><c> instruc</c><00:04:04.200><c> fine</c><00:04:04.439><c> tuning</c>

00:04:05.069 --> 00:04:05.079 align:start position:0%
model this is an instruc fine tuning
 

00:04:05.079 --> 00:04:09.429 align:start position:0%
model this is an instruc fine tuning
model<00:04:05.879><c> it's</c><00:04:06.120><c> also</c><00:04:06.920><c> used</c><00:04:07.360><c> DPO</c><00:04:08.360><c> for</c><00:04:09.120><c> direct</c>

00:04:09.429 --> 00:04:09.439 align:start position:0%
model it's also used DPO for direct
 

00:04:09.439 --> 00:04:11.869 align:start position:0%
model it's also used DPO for direct
preference<00:04:09.879><c> optimization</c><00:04:10.760><c> here</c><00:04:11.319><c> with</c><00:04:11.519><c> this</c>

00:04:11.869 --> 00:04:11.879 align:start position:0%
preference optimization here with this
 

00:04:11.879 --> 00:04:13.390 align:start position:0%
preference optimization here with this
so<00:04:12.120><c> personally</c><00:04:12.480><c> I</c><00:04:12.560><c> think</c><00:04:12.720><c> this</c><00:04:12.799><c> is</c><00:04:13.000><c> a</c><00:04:13.159><c> really</c>

00:04:13.390 --> 00:04:13.400 align:start position:0%
so personally I think this is a really
 

00:04:13.400 --> 00:04:16.310 align:start position:0%
so personally I think this is a really
good<00:04:13.760><c> vote</c><00:04:14.200><c> for</c><00:04:14.840><c> DPO</c><00:04:15.760><c> the</c><00:04:15.879><c> fact</c><00:04:16.040><c> that</c><00:04:16.160><c> this</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
good vote for DPO the fact that this
 

00:04:16.320 --> 00:04:17.949 align:start position:0%
good vote for DPO the fact that this
team<00:04:16.759><c> really</c><00:04:17.000><c> seems</c><00:04:17.239><c> to</c><00:04:17.400><c> know</c><00:04:17.639><c> what</c><00:04:17.759><c> they're</c>

00:04:17.949 --> 00:04:17.959 align:start position:0%
team really seems to know what they're
 

00:04:17.959 --> 00:04:20.710 align:start position:0%
team really seems to know what they're
doing<00:04:18.440><c> they've</c><00:04:18.959><c> produced</c><00:04:19.400><c> a</c><00:04:19.600><c> really</c><00:04:19.919><c> good</c><00:04:20.359><c> 7</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
doing they've produced a really good 7
 

00:04:20.720 --> 00:04:22.710 align:start position:0%
doing they've produced a really good 7
billion<00:04:21.120><c> parameter</c><00:04:21.600><c> model</c><00:04:22.280><c> now</c><00:04:22.479><c> they've</c>

00:04:22.710 --> 00:04:22.720 align:start position:0%
billion parameter model now they've
 

00:04:22.720 --> 00:04:25.030 align:start position:0%
billion parameter model now they've
released<00:04:23.120><c> this</c><00:04:23.240><c> mixture</c><00:04:23.560><c> of</c><00:04:23.800><c> experts</c><00:04:24.479><c> model</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
released this mixture of experts model
 

00:04:25.040 --> 00:04:26.990 align:start position:0%
released this mixture of experts model
and<00:04:25.160><c> they're</c><00:04:25.360><c> also</c><00:04:25.639><c> using</c><00:04:25.919><c> the</c><00:04:26.080><c> DPO</c><00:04:26.600><c> on</c><00:04:26.800><c> here</c>

00:04:26.990 --> 00:04:27.000 align:start position:0%
and they're also using the DPO on here
 

00:04:27.000 --> 00:04:29.710 align:start position:0%
and they're also using the DPO on here
is<00:04:27.280><c> a</c><00:04:27.400><c> very</c><00:04:27.639><c> interesting</c><00:04:28.199><c> sign</c><00:04:28.880><c> for</c><00:04:29.160><c> this</c>

00:04:29.710 --> 00:04:29.720 align:start position:0%
is a very interesting sign for this
 

00:04:29.720 --> 00:04:31.230 align:start position:0%
is a very interesting sign for this
another<00:04:29.919><c> thing</c><00:04:30.039><c> that's</c><00:04:30.280><c> interesting</c><00:04:30.639><c> in</c><00:04:30.880><c> this</c>

00:04:31.230 --> 00:04:31.240 align:start position:0%
another thing that's interesting in this
 

00:04:31.240 --> 00:04:33.749 align:start position:0%
another thing that's interesting in this
is<00:04:31.360><c> the</c><00:04:31.479><c> Mt</c><00:04:31.960><c> bench</c><00:04:32.479><c> score</c><00:04:33.080><c> so</c><00:04:33.320><c> this</c><00:04:33.440><c> is</c><00:04:33.600><c> the</c>

00:04:33.749 --> 00:04:33.759 align:start position:0%
is the Mt bench score so this is the
 

00:04:33.759 --> 00:04:36.110 align:start position:0%
is the Mt bench score so this is the
multi-turn<00:04:34.520><c> bench</c><00:04:34.919><c> it's</c><00:04:35.160><c> basically</c><00:04:35.560><c> testing</c>

00:04:36.110 --> 00:04:36.120 align:start position:0%
multi-turn bench it's basically testing
 

00:04:36.120 --> 00:04:37.870 align:start position:0%
multi-turn bench it's basically testing
that<00:04:36.479><c> will</c><00:04:36.759><c> the</c><00:04:36.880><c> model</c><00:04:37.160><c> be</c><00:04:37.280><c> able</c><00:04:37.479><c> to</c><00:04:37.600><c> have</c><00:04:37.720><c> a</c>

00:04:37.870 --> 00:04:37.880 align:start position:0%
that will the model be able to have a
 

00:04:37.880 --> 00:04:40.230 align:start position:0%
that will the model be able to have a
conversation<00:04:38.479><c> going</c><00:04:38.840><c> back</c><00:04:39.000><c> and</c><00:04:39.280><c> forth</c><00:04:39.919><c> over</c>

00:04:40.230 --> 00:04:40.240 align:start position:0%
conversation going back and forth over
 

00:04:40.240 --> 00:04:42.430 align:start position:0%
conversation going back and forth over
multiple<00:04:40.720><c> turns</c><00:04:41.199><c> here</c><00:04:41.639><c> and</c><00:04:41.759><c> it</c><00:04:41.919><c> certainly</c>

00:04:42.430 --> 00:04:42.440 align:start position:0%
multiple turns here and it certainly
 

00:04:42.440 --> 00:04:45.110 align:start position:0%
multiple turns here and it certainly
does<00:04:43.199><c> which</c><00:04:43.320><c> is</c><00:04:43.479><c> really</c><00:04:43.720><c> good</c><00:04:43.880><c> to</c><00:04:44.039><c> see</c><00:04:44.479><c> here</c>

00:04:45.110 --> 00:04:45.120 align:start position:0%
does which is really good to see here
 

00:04:45.120 --> 00:04:46.469 align:start position:0%
does which is really good to see here
another<00:04:45.440><c> thing</c><00:04:45.639><c> that</c><00:04:45.720><c> I</c><00:04:45.840><c> find</c><00:04:46.120><c> really</c>

00:04:46.469 --> 00:04:46.479 align:start position:0%
another thing that I find really
 

00:04:46.479 --> 00:04:49.189 align:start position:0%
another thing that I find really
interesting<00:04:46.960><c> in</c><00:04:47.199><c> this</c><00:04:47.880><c> is</c><00:04:48.120><c> that</c><00:04:48.360><c> they</c><00:04:48.600><c> haven't</c>

00:04:49.189 --> 00:04:49.199 align:start position:0%
interesting in this is that they haven't
 

00:04:49.199 --> 00:04:52.070 align:start position:0%
interesting in this is that they haven't
tried<00:04:49.600><c> to</c><00:04:50.199><c> dumb</c><00:04:50.680><c> this</c><00:04:50.960><c> down</c><00:04:51.440><c> or</c><00:04:51.639><c> anything</c>

00:04:52.070 --> 00:04:52.080 align:start position:0%
tried to dumb this down or anything
 

00:04:52.080 --> 00:04:53.830 align:start position:0%
tried to dumb this down or anything
meaning<00:04:52.479><c> that</c><00:04:52.840><c> they</c><00:04:53.039><c> don't</c><00:04:53.240><c> seem</c><00:04:53.440><c> to</c><00:04:53.639><c> have</c>

00:04:53.830 --> 00:04:53.840 align:start position:0%
meaning that they don't seem to have
 

00:04:53.840 --> 00:04:55.909 align:start position:0%
meaning that they don't seem to have
censored<00:04:54.320><c> the</c><00:04:54.440><c> model</c><00:04:55.080><c> they</c><00:04:55.199><c> do</c><00:04:55.400><c> give</c><00:04:55.560><c> you</c><00:04:55.680><c> some</c>

00:04:55.909 --> 00:04:55.919 align:start position:0%
censored the model they do give you some
 

00:04:55.919 --> 00:04:57.950 align:start position:0%
censored the model they do give you some
things<00:04:56.199><c> that</c><00:04:56.320><c> you</c><00:04:56.440><c> can</c><00:04:56.720><c> do</c><00:04:57.199><c> if</c><00:04:57.320><c> you</c><00:04:57.479><c> want</c><00:04:57.680><c> to</c>

00:04:57.950 --> 00:04:57.960 align:start position:0%
things that you can do if you want to
 

00:04:57.960 --> 00:05:00.629 align:start position:0%
things that you can do if you want to
have<00:04:58.400><c> a</c><00:04:58.600><c> stronger</c><00:04:59.000><c> version</c><00:04:59.360><c> of</c><00:04:59.880><c> moderation</c>

00:05:00.629 --> 00:05:00.639 align:start position:0%
have a stronger version of moderation
 

00:05:00.639 --> 00:05:02.749 align:start position:0%
have a stronger version of moderation
Etc<00:05:01.160><c> here</c><00:05:01.720><c> and</c><00:05:01.880><c> they</c><00:05:02.000><c> also</c><00:05:02.240><c> mentioned</c><00:05:02.639><c> that</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
Etc here and they also mentioned that
 

00:05:02.759 --> 00:05:04.909 align:start position:0%
Etc here and they also mentioned that
you<00:05:02.880><c> can</c><00:05:03.080><c> actually</c><00:05:03.320><c> do</c><00:05:03.560><c> your</c><00:05:03.759><c> own</c><00:05:04.440><c> preference</c>

00:05:04.909 --> 00:05:04.919 align:start position:0%
you can actually do your own preference
 

00:05:04.919 --> 00:05:08.270 align:start position:0%
you can actually do your own preference
tuning<00:05:05.800><c> for</c><00:05:06.320><c> you</c><00:05:06.440><c> know</c><00:05:06.680><c> rebuttals</c><00:05:07.600><c> or</c><00:05:07.840><c> things</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
tuning for you know rebuttals or things
 

00:05:08.280 --> 00:05:10.629 align:start position:0%
tuning for you know rebuttals or things
like<00:05:08.600><c> that</c><00:05:09.080><c> going</c><00:05:09.440><c> through</c><00:05:09.919><c> and</c><00:05:10.080><c> finally</c><00:05:10.440><c> they</c>

00:05:10.629 --> 00:05:10.639 align:start position:0%
like that going through and finally they
 

00:05:10.639 --> 00:05:12.950 align:start position:0%
like that going through and finally they
finish<00:05:11.120><c> up</c><00:05:11.600><c> basically</c><00:05:12.120><c> talking</c><00:05:12.440><c> about</c><00:05:12.759><c> that</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
finish up basically talking about that
 

00:05:12.960 --> 00:05:14.909 align:start position:0%
finish up basically talking about that
they've<00:05:13.360><c> released</c><00:05:14.039><c> code</c><00:05:14.600><c> so</c><00:05:14.759><c> they've</c>

00:05:14.909 --> 00:05:14.919 align:start position:0%
they've released code so they've
 

00:05:14.919 --> 00:05:17.629 align:start position:0%
they've released code so they've
submitted<00:05:15.360><c> code</c><00:05:15.680><c> to</c><00:05:16.080><c> VM</c><00:05:17.080><c> which</c><00:05:17.280><c> actually</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
submitted code to VM which actually
 

00:05:17.639 --> 00:05:20.189 align:start position:0%
submitted code to VM which actually
allows<00:05:18.000><c> you</c><00:05:18.160><c> to</c><00:05:18.360><c> run</c><00:05:18.680><c> this</c><00:05:18.919><c> model</c><00:05:19.720><c> for</c><00:05:19.960><c> both</c>

00:05:20.189 --> 00:05:20.199 align:start position:0%
allows you to run this model for both
 

00:05:20.199 --> 00:05:23.110 align:start position:0%
allows you to run this model for both
the<00:05:20.360><c> base</c><00:05:20.680><c> model</c><00:05:21.080><c> and</c><00:05:21.280><c> the</c><00:05:21.440><c> instruct</c><00:05:21.919><c> model</c><00:05:22.720><c> in</c>

00:05:23.110 --> 00:05:23.120 align:start position:0%
the base model and the instruct model in
 

00:05:23.120 --> 00:05:26.629 align:start position:0%
the base model and the instruct model in
the<00:05:23.720><c> VM</c><00:05:24.720><c> in</c><00:05:24.960><c> here</c><00:05:25.560><c> so</c><00:05:25.720><c> my</c><00:05:25.919><c> guess</c><00:05:26.080><c> it's</c><00:05:26.280><c> probably</c>

00:05:26.629 --> 00:05:26.639 align:start position:0%
the VM in here so my guess it's probably
 

00:05:26.639 --> 00:05:28.909 align:start position:0%
the VM in here so my guess it's probably
just<00:05:26.840><c> a</c><00:05:26.960><c> matter</c><00:05:27.199><c> of</c><00:05:27.400><c> hours</c><00:05:27.919><c> or</c><00:05:28.319><c> possibly</c><00:05:28.639><c> a</c><00:05:28.800><c> day</c>

00:05:28.909 --> 00:05:28.919 align:start position:0%
just a matter of hours or possibly a day
 

00:05:28.919 --> 00:05:32.029 align:start position:0%
just a matter of hours or possibly a day
or<00:05:29.080><c> two</c><00:05:29.560><c> before</c><00:05:29.840><c> we</c><00:05:30.000><c> have</c><00:05:30.400><c> a</c><00:05:30.919><c> quantise</c><00:05:31.560><c> version</c>

00:05:32.029 --> 00:05:32.039 align:start position:0%
or two before we have a quantise version
 

00:05:32.039 --> 00:05:33.629 align:start position:0%
or two before we have a quantise version
that<00:05:32.199><c> people</c><00:05:32.400><c> will</c><00:05:32.520><c> be</c><00:05:32.639><c> able</c><00:05:32.800><c> to</c><00:05:33.000><c> run</c><00:05:33.319><c> on</c><00:05:33.479><c> a</c>

00:05:33.629 --> 00:05:33.639 align:start position:0%
that people will be able to run on a
 

00:05:33.639 --> 00:05:35.990 align:start position:0%
that people will be able to run on a
smaller<00:05:34.240><c> GPU</c><00:05:35.240><c> you</c><00:05:35.360><c> could</c><00:05:35.520><c> imagine</c><00:05:35.800><c> that</c><00:05:35.880><c> the</c>

00:05:35.990 --> 00:05:36.000 align:start position:0%
smaller GPU you could imagine that the
 

00:05:36.000 --> 00:05:37.670 align:start position:0%
smaller GPU you could imagine that the
quantise<00:05:36.440><c> version</c><00:05:36.759><c> should</c><00:05:37.000><c> perhaps</c><00:05:37.280><c> be</c><00:05:37.520><c> able</c>

00:05:37.670 --> 00:05:37.680 align:start position:0%
quantise version should perhaps be able
 

00:05:37.680 --> 00:05:42.350 align:start position:0%
quantise version should perhaps be able
to<00:05:37.840><c> run</c><00:05:38.280><c> even</c><00:05:38.759><c> on</c><00:05:39.199><c> a</c><00:05:39.520><c> T4</c><00:05:40.280><c> GPU</c><00:05:41.280><c> going</c><00:05:41.520><c> forward</c><00:05:42.199><c> so</c>

00:05:42.350 --> 00:05:42.360 align:start position:0%
to run even on a T4 GPU going forward so
 

00:05:42.360 --> 00:05:43.150 align:start position:0%
to run even on a T4 GPU going forward so
this<00:05:42.479><c> is</c><00:05:42.600><c> certainly</c><00:05:42.840><c> going</c><00:05:42.960><c> to</c><00:05:43.039><c> be</c>

00:05:43.150 --> 00:05:43.160 align:start position:0%
this is certainly going to be
 

00:05:43.160 --> 00:05:44.710 align:start position:0%
this is certainly going to be
interesting<00:05:43.479><c> to</c><00:05:43.600><c> see</c><00:05:43.880><c> how</c><00:05:44.080><c> this</c><00:05:44.240><c> instruct</c>

00:05:44.710 --> 00:05:44.720 align:start position:0%
interesting to see how this instruct
 

00:05:44.720 --> 00:05:47.430 align:start position:0%
interesting to see how this instruct
model<00:05:45.080><c> performs</c><00:05:46.000><c> over</c><00:05:46.240><c> the</c><00:05:46.400><c> next</c><00:05:46.680><c> few</c><00:05:47.000><c> days</c>

00:05:47.430 --> 00:05:47.440 align:start position:0%
model performs over the next few days
 

00:05:47.440 --> 00:05:49.550 align:start position:0%
model performs over the next few days
here<00:05:48.120><c> hopefully</c><00:05:48.479><c> this</c><00:05:48.639><c> clears</c><00:05:48.960><c> up</c><00:05:49.160><c> some</c><00:05:49.319><c> of</c>

00:05:49.550 --> 00:05:49.560 align:start position:0%
here hopefully this clears up some of
 

00:05:49.560 --> 00:05:51.309 align:start position:0%
here hopefully this clears up some of
the<00:05:49.759><c> issues</c><00:05:50.080><c> that</c><00:05:50.240><c> I</c><00:05:50.400><c> had</c><00:05:50.560><c> in</c><00:05:50.680><c> the</c><00:05:50.840><c> previous</c>

00:05:51.309 --> 00:05:51.319 align:start position:0%
the issues that I had in the previous
 

00:05:51.319 --> 00:05:53.710 align:start position:0%
the issues that I had in the previous
video<00:05:52.120><c> and</c><00:05:52.240><c> that</c><00:05:52.400><c> people</c><00:05:52.639><c> have</c><00:05:52.960><c> a</c><00:05:53.080><c> better</c>

00:05:53.710 --> 00:05:53.720 align:start position:0%
video and that people have a better
 

00:05:53.720 --> 00:05:55.710 align:start position:0%
video and that people have a better
understanding<00:05:54.360><c> of</c><00:05:54.800><c> exactly</c><00:05:55.160><c> what</c><00:05:55.280><c> the</c><00:05:55.400><c> model</c>

00:05:55.710 --> 00:05:55.720 align:start position:0%
understanding of exactly what the model
 

00:05:55.720 --> 00:05:57.909 align:start position:0%
understanding of exactly what the model
is<00:05:55.919><c> doing</c><00:05:56.440><c> here</c><00:05:57.120><c> and</c><00:05:57.280><c> hopefully</c><00:05:57.600><c> over</c><00:05:57.759><c> the</c>

00:05:57.909 --> 00:05:57.919 align:start position:0%
is doing here and hopefully over the
 

00:05:57.919 --> 00:06:00.710 align:start position:0%
is doing here and hopefully over the
next<00:05:58.319><c> day</c><00:05:58.880><c> or</c><00:05:59.080><c> two</c><00:05:59.560><c> can</c><00:05:59.680><c> all</c><00:05:59.880><c> play</c><00:06:00.199><c> with</c><00:06:00.479><c> the</c>

00:06:00.710 --> 00:06:00.720 align:start position:0%
next day or two can all play with the
 

00:06:00.720 --> 00:06:03.189 align:start position:0%
next day or two can all play with the
instruct<00:06:01.280><c> version</c><00:06:01.639><c> of</c><00:06:01.840><c> the</c><00:06:01.960><c> model</c><00:06:02.840><c> as</c><00:06:03.000><c> always</c>

00:06:03.189 --> 00:06:03.199 align:start position:0%
instruct version of the model as always
 

00:06:03.199 --> 00:06:04.990 align:start position:0%
instruct version of the model as always
if<00:06:03.319><c> you</c><00:06:03.400><c> found</c><00:06:03.600><c> the</c><00:06:03.720><c> video</c><00:06:03.960><c> useful</c><00:06:04.479><c> please</c>

00:06:04.990 --> 00:06:05.000 align:start position:0%
if you found the video useful please
 

00:06:05.000 --> 00:06:07.070 align:start position:0%
if you found the video useful please
click<00:06:05.280><c> like</c><00:06:05.440><c> And</c><00:06:05.639><c> subscribe</c><00:06:06.560><c> I</c><00:06:06.639><c> will</c><00:06:06.840><c> talk</c><00:06:07.000><c> to</c>

00:06:07.070 --> 00:06:07.080 align:start position:0%
click like And subscribe I will talk to
 

00:06:07.080 --> 00:06:12.240 align:start position:0%
click like And subscribe I will talk to
you<00:06:07.199><c> in</c><00:06:07.280><c> the</c><00:06:07.400><c> next</c><00:06:07.720><c> video</c><00:06:08.400><c> bye</c><00:06:08.599><c> for</c><00:06:09.240><c> now</c>

