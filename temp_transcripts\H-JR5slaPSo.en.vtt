WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:03.110 align:start position:0%
 
and<00:00:00.799><c> howdy</c><00:00:01.120><c> guys</c><00:00:01.520><c> today</c><00:00:01.839><c> we</c><00:00:02.080><c> are</c><00:00:02.240><c> looking</c><00:00:02.639><c> at</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
and howdy guys today we are looking at
 

00:00:03.120 --> 00:00:05.030 align:start position:0%
and howdy guys today we are looking at
a<00:00:03.360><c> de</c><00:00:03.600><c> tree</c><00:00:03.919><c> article</c><00:00:04.319><c> we're</c><00:00:04.480><c> looking</c><00:00:04.720><c> at</c><00:00:04.880><c> the</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
a de tree article we're looking at the
 

00:00:05.040 --> 00:00:06.869 align:start position:0%
a de tree article we're looking at the
first<00:00:05.279><c> command</c><00:00:05.680><c> from</c><00:00:05.920><c> a</c><00:00:06.000><c> very</c><00:00:06.319><c> awesome</c><00:00:06.720><c> the</c>

00:00:06.869 --> 00:00:06.879 align:start position:0%
first command from a very awesome the
 

00:00:06.879 --> 00:00:08.310 align:start position:0%
first command from a very awesome the
tree<00:00:07.200><c> article</c><00:00:07.680><c> called</c>

00:00:08.310 --> 00:00:08.320 align:start position:0%
tree article called
 

00:00:08.320 --> 00:00:10.709 align:start position:0%
tree article called
10<00:00:08.720><c> insanely</c><00:00:09.280><c> useful</c><00:00:09.840><c> git</c><00:00:10.080><c> commands</c><00:00:10.559><c> for</c>

00:00:10.709 --> 00:00:10.719 align:start position:0%
10 insanely useful git commands for
 

00:00:10.719 --> 00:00:11.430 align:start position:0%
10 insanely useful git commands for
common<00:00:11.120><c> git</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
common git
 

00:00:11.440 --> 00:00:13.830 align:start position:0%
common git
tasks<00:00:12.240><c> and</c><00:00:12.639><c> the</c><00:00:12.799><c> command</c><00:00:13.120><c> we're</c><00:00:13.280><c> looking</c><00:00:13.599><c> at</c>

00:00:13.830 --> 00:00:13.840 align:start position:0%
tasks and the command we're looking at
 

00:00:13.840 --> 00:00:14.549 align:start position:0%
tasks and the command we're looking at
is<00:00:14.000><c> going</c><00:00:14.080><c> to</c><00:00:14.240><c> be</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
is going to be
 

00:00:14.559 --> 00:00:17.590 align:start position:0%
is going to be
how<00:00:14.719><c> to</c><00:00:14.880><c> create</c><00:00:15.679><c> a</c><00:00:15.759><c> new</c><00:00:15.920><c> branch</c><00:00:16.640><c> based</c><00:00:16.960><c> off</c><00:00:17.279><c> of</c>

00:00:17.590 --> 00:00:17.600 align:start position:0%
how to create a new branch based off of
 

00:00:17.600 --> 00:00:19.029 align:start position:0%
how to create a new branch based off of
the<00:00:17.760><c> master</c><00:00:18.240><c> branch</c><00:00:18.640><c> all</c><00:00:18.720><c> right</c><00:00:18.880><c> the</c>

00:00:19.029 --> 00:00:19.039 align:start position:0%
the master branch all right the
 

00:00:19.039 --> 00:00:20.310 align:start position:0%
the master branch all right the
assumption<00:00:19.439><c> here</c><00:00:19.600><c> is</c><00:00:19.680><c> that</c><00:00:19.840><c> your</c><00:00:20.000><c> master</c>

00:00:20.310 --> 00:00:20.320 align:start position:0%
assumption here is that your master
 

00:00:20.320 --> 00:00:21.990 align:start position:0%
assumption here is that your master
branch<00:00:20.720><c> is</c><00:00:20.800><c> going</c><00:00:20.880><c> to</c><00:00:20.960><c> be</c><00:00:21.119><c> totally</c><00:00:21.520><c> totally</c>

00:00:21.990 --> 00:00:22.000 align:start position:0%
branch is going to be totally totally
 

00:00:22.000 --> 00:00:22.790 align:start position:0%
branch is going to be totally totally
updated

00:00:22.790 --> 00:00:22.800 align:start position:0%
updated
 

00:00:22.800 --> 00:00:25.349 align:start position:0%
updated
so<00:00:23.039><c> let's</c><00:00:23.199><c> go</c><00:00:23.359><c> ahead</c><00:00:23.680><c> and</c><00:00:24.000><c> and</c><00:00:24.720><c> start</c><00:00:24.960><c> playing</c>

00:00:25.349 --> 00:00:25.359 align:start position:0%
so let's go ahead and and start playing
 

00:00:25.359 --> 00:00:25.830 align:start position:0%
so let's go ahead and and start playing
around

00:00:25.830 --> 00:00:25.840 align:start position:0%
around
 

00:00:25.840 --> 00:00:28.870 align:start position:0%
around
with<00:00:26.560><c> the</c><00:00:26.800><c> actual</c><00:00:27.680><c> command</c><00:00:28.080><c> line</c><00:00:28.400><c> over</c><00:00:28.640><c> here</c>

00:00:28.870 --> 00:00:28.880 align:start position:0%
with the actual command line over here
 

00:00:28.880 --> 00:00:30.150 align:start position:0%
with the actual command line over here
all<00:00:29.039><c> right</c><00:00:29.279><c> in</c><00:00:29.359><c> this</c><00:00:29.599><c> example</c>

00:00:30.150 --> 00:00:30.160 align:start position:0%
all right in this example
 

00:00:30.160 --> 00:00:31.990 align:start position:0%
all right in this example
we're<00:00:30.400><c> already</c><00:00:30.800><c> in</c><00:00:30.960><c> a</c><00:00:31.279><c> in</c><00:00:31.439><c> a</c><00:00:31.519><c> branch</c><00:00:31.840><c> called</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
we're already in a in a branch called
 

00:00:32.000 --> 00:00:33.350 align:start position:0%
we're already in a in a branch called
the<00:00:32.160><c> tree</c><00:00:32.480><c> tutorial</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
the tree tutorial
 

00:00:33.360 --> 00:00:35.910 align:start position:0%
the tree tutorial
but<00:00:33.520><c> let's</c><00:00:33.680><c> say</c><00:00:33.840><c> we</c><00:00:34.000><c> go</c><00:00:34.239><c> into</c><00:00:35.040><c> our</c><00:00:35.520><c> master</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
but let's say we go into our master
 

00:00:35.920 --> 00:00:36.950 align:start position:0%
but let's say we go into our master
branch<00:00:36.320><c> git</c><00:00:36.559><c> check</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
branch git check
 

00:00:36.960 --> 00:00:40.549 align:start position:0%
branch git check
out<00:00:37.440><c> master</c><00:00:38.960><c> okay</c><00:00:39.280><c> we</c><00:00:39.440><c> switch</c><00:00:39.760><c> into</c><00:00:40.000><c> master</c><00:00:40.399><c> we</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
out master okay we switch into master we
 

00:00:40.559 --> 00:00:41.190 align:start position:0%
out master okay we switch into master we
say<00:00:40.800><c> get</c>

00:00:41.190 --> 00:00:41.200 align:start position:0%
say get
 

00:00:41.200 --> 00:00:44.310 align:start position:0%
say get
pull<00:00:42.640><c> origin</c><00:00:43.440><c> master</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
pull origin master
 

00:00:44.320 --> 00:00:45.910 align:start position:0%
pull origin master
make<00:00:44.480><c> sure</c><00:00:44.640><c> that</c><00:00:44.800><c> your</c><00:00:44.960><c> master</c><00:00:45.360><c> branch</c><00:00:45.600><c> is</c><00:00:45.840><c> up</c>

00:00:45.910 --> 00:00:45.920 align:start position:0%
make sure that your master branch is up
 

00:00:45.920 --> 00:00:47.430 align:start position:0%
make sure that your master branch is up
to<00:00:46.079><c> date</c><00:00:46.399><c> with</c><00:00:46.559><c> whatever's</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
to date with whatever's
 

00:00:47.440 --> 00:00:50.630 align:start position:0%
to date with whatever's
um<00:00:48.480><c> whatever</c><00:00:48.879><c> is</c><00:00:49.120><c> live</c><00:00:49.520><c> right</c><00:00:49.760><c> now</c><00:00:50.160><c> okay</c><00:00:50.399><c> so</c><00:00:50.559><c> we</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
um whatever is live right now okay so we
 

00:00:50.640 --> 00:00:52.549 align:start position:0%
um whatever is live right now okay so we
pull<00:00:50.879><c> all</c><00:00:51.039><c> those</c><00:00:51.199><c> changes</c><00:00:51.680><c> into</c><00:00:52.000><c> our</c><00:00:52.160><c> local</c>

00:00:52.549 --> 00:00:52.559 align:start position:0%
pull all those changes into our local
 

00:00:52.559 --> 00:00:53.350 align:start position:0%
pull all those changes into our local
machine

00:00:53.350 --> 00:00:53.360 align:start position:0%
machine
 

00:00:53.360 --> 00:00:55.990 align:start position:0%
machine
and<00:00:53.600><c> now</c><00:00:53.840><c> we</c><00:00:53.920><c> can</c><00:00:54.160><c> actually</c><00:00:54.480><c> do</c><00:00:55.199><c> git</c><00:00:55.520><c> check</c><00:00:55.840><c> we</c>

00:00:55.990 --> 00:00:56.000 align:start position:0%
and now we can actually do git check we
 

00:00:56.000 --> 00:00:57.670 align:start position:0%
and now we can actually do git check we
can<00:00:56.160><c> actually</c><00:00:56.640><c> use</c><00:00:56.879><c> this</c><00:00:57.120><c> command</c>

00:00:57.670 --> 00:00:57.680 align:start position:0%
can actually use this command
 

00:00:57.680 --> 00:01:00.069 align:start position:0%
can actually use this command
okay<00:00:58.000><c> git</c><00:00:58.239><c> checkout</c><00:00:58.719><c> dash</c><00:00:59.120><c> b</c><00:00:59.520><c> what</c><00:00:59.680><c> will</c><00:00:59.920><c> we</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
okay git checkout dash b what will we
 

00:01:00.079 --> 00:01:01.029 align:start position:0%
okay git checkout dash b what will we
call<00:01:00.320><c> this</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
call this
 

00:01:01.039 --> 00:01:04.149 align:start position:0%
call this
we'll<00:01:01.280><c> call</c><00:01:01.600><c> this</c><00:01:02.239><c> video</c><00:01:03.199><c> take</c><00:01:03.600><c> two</c>

00:01:04.149 --> 00:01:04.159 align:start position:0%
we'll call this video take two
 

00:01:04.159 --> 00:01:07.429 align:start position:0%
we'll call this video take two
okay<00:01:05.040><c> get</c><00:01:05.280><c> checkout</c><00:01:05.680><c> dash</c><00:01:06.080><c> b</c><00:01:06.560><c> video</c><00:01:06.960><c> take</c><00:01:07.200><c> two</c>

00:01:07.429 --> 00:01:07.439 align:start position:0%
okay get checkout dash b video take two
 

00:01:07.439 --> 00:01:08.550 align:start position:0%
okay get checkout dash b video take two
we're<00:01:07.600><c> creating</c><00:01:08.000><c> a</c><00:01:08.159><c> new</c>

00:01:08.550 --> 00:01:08.560 align:start position:0%
we're creating a new
 

00:01:08.560 --> 00:01:11.030 align:start position:0%
we're creating a new
branch<00:01:08.880><c> called</c><00:01:09.200><c> video</c><00:01:09.520><c> take</c><00:01:09.840><c> two</c><00:01:10.560><c> based</c><00:01:10.799><c> off</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
branch called video take two based off
 

00:01:11.040 --> 00:01:12.070 align:start position:0%
branch called video take two based off
of<00:01:11.119><c> the</c><00:01:11.280><c> master</c><00:01:11.680><c> branch</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
of the master branch
 

00:01:12.080 --> 00:01:13.990 align:start position:0%
of the master branch
let's<00:01:12.240><c> go</c><00:01:12.400><c> ahead</c><00:01:12.560><c> and</c><00:01:12.720><c> make</c><00:01:12.880><c> a</c><00:01:13.040><c> change</c><00:01:13.439><c> to</c><00:01:13.680><c> it</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
let's go ahead and make a change to it
 

00:01:14.000 --> 00:01:16.149 align:start position:0%
let's go ahead and make a change to it
okay<00:01:14.720><c> and</c><00:01:14.960><c> in</c><00:01:15.119><c> this</c><00:01:15.360><c> example</c><00:01:15.840><c> we're</c><00:01:16.000><c> just</c>

00:01:16.149 --> 00:01:16.159 align:start position:0%
okay and in this example we're just
 

00:01:16.159 --> 00:01:17.350 align:start position:0%
okay and in this example we're just
going<00:01:16.240><c> to</c><00:01:16.320><c> go</c><00:01:16.479><c> ahead</c><00:01:16.720><c> and</c><00:01:16.799><c> delete</c>

00:01:17.350 --> 00:01:17.360 align:start position:0%
going to go ahead and delete
 

00:01:17.360 --> 00:01:19.510 align:start position:0%
going to go ahead and delete
these<00:01:17.680><c> two</c><00:01:17.920><c> lines</c><00:01:18.320><c> at</c><00:01:18.400><c> the</c><00:01:18.720><c> at</c><00:01:18.880><c> the</c><00:01:19.200><c> front</c><00:01:19.360><c> of</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
these two lines at the at the front of
 

00:01:19.520 --> 00:01:20.870 align:start position:0%
these two lines at the at the front of
the<00:01:19.600><c> readme</c><00:01:20.159><c> file</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
the readme file
 

00:01:20.880 --> 00:01:22.630 align:start position:0%
the readme file
just<00:01:21.040><c> for</c><00:01:21.200><c> the</c><00:01:21.280><c> sake</c><00:01:21.520><c> of</c><00:01:21.600><c> the</c><00:01:21.759><c> example</c><00:01:22.479><c> we're</c>

00:01:22.630 --> 00:01:22.640 align:start position:0%
just for the sake of the example we're
 

00:01:22.640 --> 00:01:25.030 align:start position:0%
just for the sake of the example we're
going<00:01:22.720><c> to</c><00:01:22.960><c> add</c><00:01:23.200><c> those</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
going to add those
 

00:01:25.040 --> 00:01:27.109 align:start position:0%
going to add those
we changes we<00:01:26.240><c> i</c><00:01:26.400><c> just</c><00:01:26.640><c> as</c><00:01:26.720><c> we</c><00:01:26.880><c> would</c>

00:01:27.109 --> 00:01:27.119 align:start position:0%
we changes we i just as we would
 

00:01:27.119 --> 00:01:28.870 align:start position:0%
we changes we i just as we would
normally<00:01:27.759><c> get</c><00:01:28.000><c> commit</c><00:01:28.400><c> dash</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
normally get commit dash
 

00:01:28.880 --> 00:01:32.069 align:start position:0%
normally get commit dash
m<00:01:29.520><c> deleted</c><00:01:30.560><c> two</c><00:01:31.360><c> lines</c>

00:01:32.069 --> 00:01:32.079 align:start position:0%
m deleted two lines
 

00:01:32.079 --> 00:01:35.830 align:start position:0%
m deleted two lines
in<00:01:32.400><c> read</c><00:01:32.720><c> me</c><00:01:33.360><c> okay</c><00:01:34.960><c> and</c>

00:01:35.830 --> 00:01:35.840 align:start position:0%
in read me okay and
 

00:01:35.840 --> 00:01:38.630 align:start position:0%
in read me okay and
if<00:01:36.000><c> we</c><00:01:36.159><c> run</c><00:01:36.560><c> git</c><00:01:36.799><c> branch</c><00:01:37.280><c> before</c><00:01:37.600><c> we</c><00:01:37.759><c> push</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
if we run git branch before we push
 

00:01:38.640 --> 00:01:40.230 align:start position:0%
if we run git branch before we push
right<00:01:38.880><c> we</c><00:01:39.040><c> always</c><00:01:39.280><c> check</c><00:01:39.439><c> which</c><00:01:39.680><c> branch</c><00:01:40.000><c> we're</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
right we always check which branch we're
 

00:01:40.240 --> 00:01:40.870 align:start position:0%
right we always check which branch we're
on

00:01:40.870 --> 00:01:40.880 align:start position:0%
on
 

00:01:40.880 --> 00:01:42.389 align:start position:0%
on
okay<00:01:41.119><c> right</c><00:01:41.280><c> now</c><00:01:41.439><c> we're</c><00:01:41.600><c> on</c><00:01:41.759><c> the</c><00:01:41.840><c> video</c><00:01:42.159><c> take</c>

00:01:42.389 --> 00:01:42.399 align:start position:0%
okay right now we're on the video take
 

00:01:42.399 --> 00:01:44.230 align:start position:0%
okay right now we're on the video take
two<00:01:42.640><c> branch</c><00:01:42.960><c> we're</c><00:01:43.040><c> gonna</c><00:01:43.439><c> push</c><00:01:43.759><c> all</c><00:01:43.920><c> these</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
two branch we're gonna push all these
 

00:01:44.240 --> 00:01:44.870 align:start position:0%
two branch we're gonna push all these
changes

00:01:44.870 --> 00:01:44.880 align:start position:0%
changes
 

00:01:44.880 --> 00:01:47.990 align:start position:0%
changes
into<00:01:45.280><c> the</c><00:01:45.439><c> video</c><00:01:45.840><c> take</c><00:01:46.240><c> two</c><00:01:46.880><c> branch</c><00:01:47.360><c> git</c><00:01:47.600><c> push</c>

00:01:47.990 --> 00:01:48.000 align:start position:0%
into the video take two branch git push
 

00:01:48.000 --> 00:01:51.429 align:start position:0%
into the video take two branch git push
origin<00:01:49.040><c> video</c><00:01:49.680><c> take</c><00:01:50.079><c> two</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
origin video take two
 

00:01:51.439 --> 00:01:54.469 align:start position:0%
origin video take two
video<00:01:51.840><c> dash</c><00:01:52.159><c> take</c><00:01:52.840><c> two</c><00:01:54.000><c> okay</c>

00:01:54.469 --> 00:01:54.479 align:start position:0%
video dash take two okay
 

00:01:54.479 --> 00:01:57.510 align:start position:0%
video dash take two okay
and<00:01:55.119><c> that's</c><00:01:55.360><c> all</c><00:01:55.520><c> there</c><00:01:55.759><c> is</c><00:01:55.920><c> to</c><00:01:56.159><c> it</c><00:01:56.479><c> we've</c><00:01:56.799><c> now</c>

00:01:57.510 --> 00:01:57.520 align:start position:0%
and that's all there is to it we've now
 

00:01:57.520 --> 00:02:00.709 align:start position:0%
and that's all there is to it we've now
um<00:01:58.640><c> pushed</c><00:01:58.960><c> that</c><00:01:59.680><c> push</c><00:02:00.000><c> that</c><00:02:00.320><c> uh</c>

00:02:00.709 --> 00:02:00.719 align:start position:0%
um pushed that push that uh
 

00:02:00.719 --> 00:02:02.870 align:start position:0%
um pushed that push that uh
into<00:02:00.960><c> our</c><00:02:01.119><c> version</c><00:02:01.520><c> control</c><00:02:01.920><c> system</c><00:02:02.479><c> and</c><00:02:02.799><c> the</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
into our version control system and the
 

00:02:02.880 --> 00:02:04.870 align:start position:0%
into our version control system and the
command<00:02:03.280><c> line</c><00:02:03.520><c> even</c><00:02:03.680><c> tells</c><00:02:04.000><c> us</c>

00:02:04.870 --> 00:02:04.880 align:start position:0%
command line even tells us
 

00:02:04.880 --> 00:02:08.150 align:start position:0%
command line even tells us
create<00:02:05.439><c> a</c><00:02:06.079><c> pull</c><00:02:06.320><c> request</c><00:02:06.799><c> for</c><00:02:07.040><c> video</c><00:02:07.520><c> take</c><00:02:07.840><c> two</c>

00:02:08.150 --> 00:02:08.160 align:start position:0%
create a pull request for video take two
 

00:02:08.160 --> 00:02:09.430 align:start position:0%
create a pull request for video take two
what<00:02:08.319><c> that</c><00:02:08.479><c> really</c><00:02:08.800><c> means</c><00:02:09.039><c> is</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
what that really means is
 

00:02:09.440 --> 00:02:11.670 align:start position:0%
what that really means is
we<00:02:09.599><c> need</c><00:02:09.759><c> to</c><00:02:10.000><c> actually</c><00:02:10.479><c> go</c><00:02:10.720><c> into</c><00:02:11.039><c> our</c><00:02:11.200><c> version</c>

00:02:11.670 --> 00:02:11.680 align:start position:0%
we need to actually go into our version
 

00:02:11.680 --> 00:02:12.630 align:start position:0%
we need to actually go into our version
control

00:02:12.630 --> 00:02:12.640 align:start position:0%
control
 

00:02:12.640 --> 00:02:14.710 align:start position:0%
control
uh<00:02:13.120><c> system</c><00:02:13.520><c> over</c><00:02:13.760><c> here</c><00:02:14.000><c> and</c><00:02:14.160><c> of</c><00:02:14.239><c> course</c><00:02:14.480><c> this</c>

00:02:14.710 --> 00:02:14.720 align:start position:0%
uh system over here and of course this
 

00:02:14.720 --> 00:02:16.790 align:start position:0%
uh system over here and of course this
could<00:02:14.879><c> be</c><00:02:15.520><c> this</c><00:02:15.760><c> might</c><00:02:15.920><c> be</c><00:02:16.080><c> bitbucket</c><00:02:16.720><c> it</c>

00:02:16.790 --> 00:02:16.800 align:start position:0%
could be this might be bitbucket it
 

00:02:16.800 --> 00:02:17.589 align:start position:0%
could be this might be bitbucket it
might<00:02:17.040><c> be</c><00:02:17.280><c> git</c>

00:02:17.589 --> 00:02:17.599 align:start position:0%
might be git
 

00:02:17.599 --> 00:02:19.990 align:start position:0%
might be git
lab<00:02:18.080><c> whatever</c><00:02:18.800><c> whatever</c><00:02:19.200><c> you're</c><00:02:19.440><c> using</c><00:02:19.920><c> in</c>

00:02:19.990 --> 00:02:20.000 align:start position:0%
lab whatever whatever you're using in
 

00:02:20.000 --> 00:02:22.070 align:start position:0%
lab whatever whatever you're using in
this<00:02:20.239><c> example</c><00:02:20.640><c> it's</c><00:02:20.840><c> github</c>

00:02:22.070 --> 00:02:22.080 align:start position:0%
this example it's github
 

00:02:22.080 --> 00:02:24.949 align:start position:0%
this example it's github
and<00:02:22.480><c> it</c><00:02:23.040><c> even</c><00:02:23.280><c> tells</c><00:02:23.599><c> us</c><00:02:23.760><c> very</c><00:02:24.160><c> cleanly</c><00:02:24.720><c> hey</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
and it even tells us very cleanly hey
 

00:02:24.959 --> 00:02:27.510 align:start position:0%
and it even tells us very cleanly hey
videotake2<00:02:25.840><c> this</c><00:02:26.080><c> branch</c><00:02:26.480><c> had</c><00:02:26.720><c> recent</c><00:02:27.120><c> pushes</c>

00:02:27.510 --> 00:02:27.520 align:start position:0%
videotake2 this branch had recent pushes
 

00:02:27.520 --> 00:02:29.190 align:start position:0%
videotake2 this branch had recent pushes
less<00:02:27.760><c> than</c><00:02:28.000><c> a</c><00:02:28.080><c> minute</c><00:02:28.319><c> ago</c>

00:02:29.190 --> 00:02:29.200 align:start position:0%
less than a minute ago
 

00:02:29.200 --> 00:02:31.350 align:start position:0%
less than a minute ago
we<00:02:29.360><c> can</c><00:02:29.599><c> compare</c><00:02:30.319><c> and</c><00:02:30.640><c> pull</c><00:02:30.959><c> all</c><00:02:31.040><c> those</c>

00:02:31.350 --> 00:02:31.360 align:start position:0%
we can compare and pull all those
 

00:02:31.360 --> 00:02:32.390 align:start position:0%
we can compare and pull all those
changes<00:02:31.920><c> into</c>

00:02:32.390 --> 00:02:32.400 align:start position:0%
changes into
 

00:02:32.400 --> 00:02:34.229 align:start position:0%
changes into
our<00:02:32.720><c> master</c><00:02:33.200><c> branch</c><00:02:33.599><c> and</c><00:02:33.760><c> it's</c><00:02:33.920><c> going</c><00:02:34.000><c> to</c><00:02:34.080><c> be</c>

00:02:34.229 --> 00:02:34.239 align:start position:0%
our master branch and it's going to be
 

00:02:34.239 --> 00:02:36.470 align:start position:0%
our master branch and it's going to be
much<00:02:34.480><c> cleaner</c><00:02:35.280><c> when</c><00:02:35.440><c> we</c><00:02:35.599><c> do</c><00:02:35.760><c> it</c><00:02:35.920><c> in</c><00:02:36.080><c> here</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
much cleaner when we do it in here
 

00:02:36.480 --> 00:02:37.990 align:start position:0%
much cleaner when we do it in here
so<00:02:36.640><c> let's</c><00:02:36.879><c> have</c><00:02:37.040><c> a</c><00:02:37.120><c> look</c><00:02:37.280><c> at</c><00:02:37.360><c> what</c><00:02:37.519><c> that</c><00:02:37.760><c> looks</c>

00:02:37.990 --> 00:02:38.000 align:start position:0%
so let's have a look at what that looks
 

00:02:38.000 --> 00:02:39.990 align:start position:0%
so let's have a look at what that looks
like<00:02:38.400><c> okay</c><00:02:38.640><c> so</c><00:02:38.879><c> it</c><00:02:38.959><c> says</c><00:02:39.120><c> here</c><00:02:39.360><c> deleted</c><00:02:39.760><c> two</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
like okay so it says here deleted two
 

00:02:40.000 --> 00:02:41.110 align:start position:0%
like okay so it says here deleted two
lines<00:02:40.239><c> in</c><00:02:40.319><c> the</c><00:02:40.480><c> readme</c>

00:02:41.110 --> 00:02:41.120 align:start position:0%
lines in the readme
 

00:02:41.120 --> 00:02:43.190 align:start position:0%
lines in the readme
if<00:02:41.280><c> we</c><00:02:41.440><c> scroll</c><00:02:41.840><c> down</c><00:02:42.080><c> we</c><00:02:42.239><c> even</c><00:02:42.560><c> see</c><00:02:42.800><c> those</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
if we scroll down we even see those
 

00:02:43.200 --> 00:02:45.190 align:start position:0%
if we scroll down we even see those
changes<00:02:44.000><c> we</c><00:02:44.160><c> see</c><00:02:44.319><c> that</c><00:02:44.480><c> two</c><00:02:44.720><c> lines</c><00:02:44.959><c> were</c>

00:02:45.190 --> 00:02:45.200 align:start position:0%
changes we see that two lines were
 

00:02:45.200 --> 00:02:45.910 align:start position:0%
changes we see that two lines were
deleted

00:02:45.910 --> 00:02:45.920 align:start position:0%
deleted
 

00:02:45.920 --> 00:02:48.550 align:start position:0%
deleted
and<00:02:46.160><c> the</c><00:02:46.319><c> name</c><00:02:46.480><c> of</c><00:02:46.560><c> the</c><00:02:46.720><c> file</c><00:02:47.040><c> is</c><00:02:47.200><c> readme.md</c>

00:02:48.550 --> 00:02:48.560 align:start position:0%
and the name of the file is readme.md
 

00:02:48.560 --> 00:02:48.949 align:start position:0%
and the name of the file is readme.md
and

00:02:48.949 --> 00:02:48.959 align:start position:0%
and
 

00:02:48.959 --> 00:02:51.270 align:start position:0%
and
okay<00:02:49.519><c> we</c><00:02:49.760><c> are</c><00:02:49.920><c> willing</c><00:02:50.239><c> to</c><00:02:50.400><c> create</c><00:02:50.800><c> that</c><00:02:51.040><c> pull</c>

00:02:51.270 --> 00:02:51.280 align:start position:0%
okay we are willing to create that pull
 

00:02:51.280 --> 00:02:52.470 align:start position:0%
okay we are willing to create that pull
request

00:02:52.470 --> 00:02:52.480 align:start position:0%
request
 

00:02:52.480 --> 00:02:54.770 align:start position:0%
request
and<00:02:52.879><c> in</c><00:02:53.120><c> addition</c><00:02:54.319><c> um</c>

00:02:54.770 --> 00:02:54.780 align:start position:0%
and in addition um
 

00:02:54.780 --> 00:02:55.910 align:start position:0%
and in addition um
[Music]

00:02:55.910 --> 00:02:55.920 align:start position:0%
[Music]
 

00:02:55.920 --> 00:02:58.309 align:start position:0%
[Music]
we<00:02:56.239><c> are</c><00:02:56.400><c> willing</c><00:02:56.720><c> to</c><00:02:56.959><c> merge</c><00:02:57.280><c> the</c><00:02:57.440><c> pull</c><00:02:57.680><c> request</c>

00:02:58.309 --> 00:02:58.319 align:start position:0%
we are willing to merge the pull request
 

00:02:58.319 --> 00:02:59.350 align:start position:0%
we are willing to merge the pull request
and<00:02:58.400><c> we</c><00:02:58.560><c> are</c><00:02:58.640><c> willing</c><00:02:58.959><c> to</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
and we are willing to
 

00:02:59.360 --> 00:03:02.790 align:start position:0%
and we are willing to
confirm<00:02:59.840><c> the</c><00:03:00.000><c> merge</c><00:03:01.040><c> and</c><00:03:01.519><c> boom</c><00:03:02.159><c> everything</c>

00:03:02.790 --> 00:03:02.800 align:start position:0%
confirm the merge and boom everything
 

00:03:02.800 --> 00:03:05.190 align:start position:0%
confirm the merge and boom everything
has<00:03:03.040><c> been</c><00:03:03.440><c> pulled</c><00:03:03.920><c> into</c><00:03:04.319><c> the</c><00:03:04.480><c> master</c><00:03:04.879><c> branch</c>

00:03:05.190 --> 00:03:05.200 align:start position:0%
has been pulled into the master branch
 

00:03:05.200 --> 00:03:07.190 align:start position:0%
has been pulled into the master branch
pull<00:03:05.440><c> request</c><00:03:05.840><c> successfully</c><00:03:06.480><c> merged</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
pull request successfully merged
 

00:03:07.200 --> 00:03:09.350 align:start position:0%
pull request successfully merged
and<00:03:07.360><c> it's</c><00:03:07.599><c> been</c><00:03:07.840><c> closed</c><00:03:08.480><c> if</c><00:03:08.640><c> we</c><00:03:08.720><c> go</c><00:03:09.040><c> into</c>

00:03:09.350 --> 00:03:09.360 align:start position:0%
and it's been closed if we go into
 

00:03:09.360 --> 00:03:12.309 align:start position:0%
and it's been closed if we go into
react.js<00:03:10.080><c> book</c><00:03:10.400><c> library</c><00:03:10.959><c> now</c>

00:03:12.309 --> 00:03:12.319 align:start position:0%
react.js book library now
 

00:03:12.319 --> 00:03:15.270 align:start position:0%
react.js book library now
and<00:03:12.480><c> we</c><00:03:12.640><c> look</c><00:03:13.360><c> at</c><00:03:13.760><c> our</c><00:03:14.080><c> readme</c><00:03:14.640><c> it</c><00:03:14.879><c> even</c><00:03:15.040><c> gives</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
and we look at our readme it even gives
 

00:03:15.280 --> 00:03:16.949 align:start position:0%
and we look at our readme it even gives
us<00:03:15.440><c> that</c><00:03:15.760><c> commit</c><00:03:16.159><c> message</c><00:03:16.560><c> over</c><00:03:16.720><c> there</c>

00:03:16.949 --> 00:03:16.959 align:start position:0%
us that commit message over there
 

00:03:16.959 --> 00:03:18.869 align:start position:0%
us that commit message over there
deleted<00:03:17.599><c> two</c><00:03:17.840><c> lines</c><00:03:18.080><c> in</c><00:03:18.159><c> the</c><00:03:18.319><c> readme</c><00:03:18.720><c> and</c><00:03:18.800><c> you</c>

00:03:18.869 --> 00:03:18.879 align:start position:0%
deleted two lines in the readme and you
 

00:03:18.879 --> 00:03:20.309 align:start position:0%
deleted two lines in the readme and you
can<00:03:18.959><c> see</c><00:03:19.200><c> right</c><00:03:19.360><c> now</c><00:03:19.519><c> we're</c><00:03:19.680><c> in</c><00:03:19.840><c> the</c><00:03:19.920><c> master</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
can see right now we're in the master
 

00:03:20.319 --> 00:03:21.750 align:start position:0%
can see right now we're in the master
branch<00:03:20.640><c> so</c><00:03:20.800><c> everything</c><00:03:21.120><c> has</c><00:03:21.280><c> been</c><00:03:21.519><c> pulled</c>

00:03:21.750 --> 00:03:21.760 align:start position:0%
branch so everything has been pulled
 

00:03:21.760 --> 00:03:22.949 align:start position:0%
branch so everything has been pulled
into<00:03:22.000><c> master</c>

00:03:22.949 --> 00:03:22.959 align:start position:0%
into master
 

00:03:22.959 --> 00:03:24.470 align:start position:0%
into master
if<00:03:23.040><c> you</c><00:03:23.200><c> have</c><00:03:23.280><c> any</c><00:03:23.519><c> questions</c><00:03:23.920><c> feel</c><00:03:24.159><c> free</c><00:03:24.319><c> to</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
if you have any questions feel free to
 

00:03:24.480 --> 00:03:26.390 align:start position:0%
if you have any questions feel free to
reach<00:03:24.720><c> out</c><00:03:25.040><c> this</c><00:03:25.440><c> is</c>

00:03:26.390 --> 00:03:26.400 align:start position:0%
reach out this is
 

00:03:26.400 --> 00:03:29.110 align:start position:0%
reach out this is
the<00:03:26.560><c> tree</c><00:03:26.879><c> tutorial</c><00:03:27.840><c> about</c><00:03:28.159><c> how</c><00:03:28.319><c> to</c><00:03:28.480><c> create</c><00:03:28.959><c> a</c>

00:03:29.110 --> 00:03:29.120 align:start position:0%
the tree tutorial about how to create a
 

00:03:29.120 --> 00:03:30.070 align:start position:0%
the tree tutorial about how to create a
new<00:03:29.360><c> branch</c>

00:03:30.070 --> 00:03:30.080 align:start position:0%
new branch
 

00:03:30.080 --> 00:03:32.470 align:start position:0%
new branch
via<00:03:30.400><c> the</c><00:03:30.560><c> git</c><00:03:30.799><c> command</c><00:03:31.200><c> line</c><00:03:31.599><c> thanks</c><00:03:31.920><c> guys</c><00:03:32.319><c> and</c>

00:03:32.470 --> 00:03:32.480 align:start position:0%
via the git command line thanks guys and
 

00:03:32.480 --> 00:03:36.799 align:start position:0%
via the git command line thanks guys and
we'll<00:03:32.720><c> see</c><00:03:32.879><c> you</c><00:03:33.040><c> in</c><00:03:33.120><c> the</c><00:03:33.200><c> next</c><00:03:33.799><c> one</c>

