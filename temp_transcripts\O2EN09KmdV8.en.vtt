WEBVTT
Kind: captions
Language: en

00:00:17.680 --> 00:00:20.550 align:start position:0%
 
issue<00:00:18.039><c> closure</c><00:00:18.439><c> burnout</c><00:00:18.880><c> is</c><00:00:19.080><c> real</c><00:00:19.560><c> but</c><00:00:19.760><c> GitHub</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
issue closure burnout is real but GitHub
 

00:00:20.560 --> 00:00:22.509 align:start position:0%
issue closure burnout is real but GitHub
understands<00:00:21.160><c> which</c><00:00:21.279><c> is</c><00:00:21.560><c> why</c><00:00:21.800><c> we've</c><00:00:22.080><c> added</c><00:00:22.359><c> a</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
understands which is why we've added a
 

00:00:22.519 --> 00:00:24.950 align:start position:0%
understands which is why we've added a
new<00:00:22.720><c> workflow</c><00:00:23.160><c> to</c><00:00:23.279><c> GitHub</c><00:00:23.680><c> projects</c><00:00:24.439><c> autoc</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
new workflow to GitHub projects autoc
 

00:00:24.960 --> 00:00:27.630 align:start position:0%
new workflow to GitHub projects autoc
close<00:00:25.439><c> issues</c><00:00:26.400><c> for</c><00:00:26.599><c> each</c><00:00:26.840><c> project</c><00:00:27.160><c> board</c><00:00:27.519><c> you</c>

00:00:27.630 --> 00:00:27.640 align:start position:0%
close issues for each project board you
 

00:00:27.640 --> 00:00:29.589 align:start position:0%
close issues for each project board you
can<00:00:27.840><c> decide</c><00:00:28.359><c> which</c><00:00:28.599><c> item</c><00:00:29.039><c> status</c><00:00:29.400><c> should</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
can decide which item status should
 

00:00:29.599 --> 00:00:32.510 align:start position:0%
can decide which item status should
trigger<00:00:30.080><c> issue</c><00:00:30.400><c> closure</c><00:00:31.320><c> then</c><00:00:32.040><c> whenever</c><00:00:32.360><c> an</c>

00:00:32.510 --> 00:00:32.520 align:start position:0%
trigger issue closure then whenever an
 

00:00:32.520 --> 00:00:34.190 align:start position:0%
trigger issue closure then whenever an
item<00:00:32.719><c> is</c><00:00:32.840><c> moved</c><00:00:33.200><c> to</c><00:00:33.360><c> that</c><00:00:33.559><c> status</c><00:00:34.040><c> the</c>

00:00:34.190 --> 00:00:34.200 align:start position:0%
item is moved to that status the
 

00:00:34.200 --> 00:00:36.310 align:start position:0%
item is moved to that status the
associated<00:00:34.800><c> issue</c><00:00:35.120><c> will</c><00:00:35.480><c> automatically</c><00:00:36.120><c> be</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
associated issue will automatically be
 

00:00:36.320 --> 00:00:39.389 align:start position:0%
associated issue will automatically be
closed<00:00:37.280><c> this</c><00:00:37.480><c> even</c><00:00:37.719><c> works</c><00:00:38.040><c> for</c><00:00:38.280><c> Mass</c><00:00:38.680><c> edits</c>

00:00:39.389 --> 00:00:39.399 align:start position:0%
closed this even works for Mass edits
 

00:00:39.399 --> 00:00:41.750 align:start position:0%
closed this even works for Mass edits
allowing<00:00:39.760><c> you</c><00:00:39.840><c> to</c><00:00:40.039><c> drag</c><00:00:40.440><c> drop</c><00:00:41.079><c> your</c><00:00:41.360><c> problems</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
allowing you to drag drop your problems
 

00:00:41.760 --> 00:00:44.190 align:start position:0%
allowing you to drag drop your problems
away<00:00:42.640><c> oh</c><00:00:42.800><c> and</c><00:00:43.039><c> by</c><00:00:43.160><c> the</c><00:00:43.280><c> way</c><00:00:43.920><c> this</c><00:00:44.039><c> is</c>

00:00:44.190 --> 00:00:44.200 align:start position:0%
away oh and by the way this is
 

00:00:44.200 --> 00:00:46.069 align:start position:0%
away oh and by the way this is
automatically<00:00:44.760><c> enabled</c><00:00:45.239><c> for</c><00:00:45.480><c> all</c><00:00:45.719><c> new</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
automatically enabled for all new
 

00:00:46.079 --> 00:00:47.430 align:start position:0%
automatically enabled for all new
project

00:00:47.430 --> 00:00:47.440 align:start position:0%
project
 

00:00:47.440 --> 00:00:51.470 align:start position:0%
project
boards<00:00:48.440><c> find</c><00:00:48.640><c> out</c><00:00:48.840><c> more</c><00:00:49.320><c> at</c><00:00:49.680><c> GH</c><00:00:50.680><c> projects</c>

00:00:51.470 --> 00:00:51.480 align:start position:0%
boards find out more at GH projects
 

00:00:51.480 --> 00:00:55.199 align:start position:0%
boards find out more at GH projects
autoclose<00:00:52.199><c> issues</c>

