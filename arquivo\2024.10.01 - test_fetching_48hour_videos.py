import os
from dotenv import load_dotenv
import yt_dlp
from datetime import datetime, timedelta, timezone
from rich.console import Console
from rich.table import Table
import logging
from typing import Dict, List, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
#logging.getLogger('yt_dlp').setLevel(logging.ERROR)  # Suppress yt_dlp warnings

# Load environment variables
load_dotenv()

CHOSEN_WATCHLIST_FILE = "youtube_channels_Watchlist_download_entire_channels.md"


"""
This script tests fetching recent YouTube videos from specified channels.

Key functionality:
- Reads channel data from a markdown file containing channel names and IDs organized by topics
- For each channel, fetches videos uploaded in the last 48 hours using yt-dlp
- Provides logging and rich console output to track the fetching process
- <PERSON>les errors gracefully and provides detailed debug information
- Can be used to verify channel accessibility and recent upload activity
- Useful for testing before running the full video processing pipeline

The script uses:
- yt-dlp for fetching video metadata from YouTube channels
- Rich library for formatted console output
- Logging for debug and error tracking
- Environment variables for configuration
- Type hints for better code clarity and IDE support

Usage:
Run the script directly to test fetching recent videos from channels listed in the
specified watchlist file (CHOSEN_WATCHLIST_FILE).
"""


def read_channel_data(file_name: str) -> Dict[str, List[Tuple[str, str]]]:
    """
    Reads channel data from a markdown file and returns a dictionary with topics and channels.

    Args:
        file_name (str): The name of the markdown file containing channel data.

    Returns:
        Dict[str, List[Tuple[str, str]]]: A dictionary mapping topics to a list of channel name and ID tuples.
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(script_dir, file_name)

    logging.info(f"Reading channel data from: {file_path}")

    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_name} does not exist in the script directory.")

    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    channel_data = {}
    current_topic = None

    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            current_topic = line[3:]
            channel_data[current_topic] = []
        elif line.startswith('- '):
            parts = line[2:].split(' - ')
            if len(parts) == 2:
                channel_name, channel_id = parts
                channel_data[current_topic].append((channel_name.strip(), channel_id.strip()))
            elif len(parts) == 1:
                channel_id = parts[0].strip()
                channel_data[current_topic].append(("N/A", channel_id))

    logging.info(f"Found {len(channel_data)} topics with {sum(len(channels) for channels in channel_data.values())} channels")
    return channel_data

def fetch_recent_videos(channel_name: str, channel_id: str) -> int:
    """
    Fetches videos from the given channel ID and counts how many were uploaded in the last 48 hours.

    Args:
        channel_name (str): The name of the YouTube channel.
        channel_id (str): The YouTube channel ID.

    Returns:
        int: The number of recent videos uploaded in the last 48 hours.
    """
    try:
        # Define the time 48 hours ago in Unix timestamp
        timestamp_48_hours_ago = int((datetime.now(timezone.utc) - timedelta(hours=48)).timestamp())

        channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
        ydl_opts = {
            'ignoreerrors': False,
            'quiet': False,  # Suppress yt_dlp console output
            'sleep_interval_requests': 0.1,  # Wait 1 second between requests
            'playlistend': 30,  # Limit to the most recent 30 videos
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Added channel_name to the logging message for clarity
            logging.info(f"Fetching videos for channel '{channel_name}' (ID: {channel_id})")
            info = ydl.extract_info(channel_url, download=False)
            if not info or 'entries' not in info:
                logging.warning(f"No video information found for channel '{channel_name}' (ID: {channel_id})")
                return 0

            recent_videos_count = 0

            for entry in info['entries']:
                if entry and 'timestamp' in entry:
                    # Get the upload timestamp
                    upload_timestamp = entry['timestamp']

                    # Check if the video was uploaded in the last 48 hours
                    if upload_timestamp >= timestamp_48_hours_ago:
                        recent_videos_count += 1
                else:
                    logging.debug(f"Missing timestamp for a video in channel '{channel_name}' (ID: {channel_id})")

            # Added channel_name to the logging message
            logging.info(f"Found {recent_videos_count} videos in the last 48 hours for channel '{channel_name}' (ID: {channel_id})")
            return recent_videos_count
    except Exception as e:
        logging.error(f"Error fetching videos for channel '{channel_name}' (ID: {channel_id}): {str(e)}")
        return 0

def main():
    """
    Main function to read channel data, fetch recent videos, and display the results in a table.
    """
    console = Console()
    try:
        channel_data = read_channel_data(CHOSEN_WATCHLIST_FILE)

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Topic", style="dim")
        table.add_column("Channel Name")
        table.add_column("Channel ID")
        table.add_column("Videos in Last 48 Hours", justify="right")

        for topic, channels in channel_data.items():
            logging.info(f"Processing topic: {topic}")
            for channel_name, channel_id in channels:
                # Pass channel_name to fetch_recent_videos
                recent_video_count = fetch_recent_videos(channel_name, channel_id)
                table.add_row(
                    topic,
                    channel_name or "N/A",
                    channel_id,
                    str(recent_video_count)
                )

        console.print(table)
    except FileNotFoundError as e:
        logging.error(f"File not found: {str(e)}")
    except Exception as e:
        logging.error(f"An unexpected error occurred: {str(e)}")

if __name__ == "__main__":
    logging.info("Script started")
    main()
    logging.info("Script finished")