#!/usr/bin/env python3
"""
Test script for the generate_embedding function implementation
"""

import asyncio
import sys
import os

# Add the current directory to sys.path to import gemini_methods
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_embedding_generation():
    """Test the generate_embedding function with different scenarios"""
    try:
        from gemini_methods.genai_utils import generate_embedding, get_available_embedding_models, get_default_embedding_model
        
        print("✅ Successfully imported generate_embedding function")
        print(f"📋 Available models: {get_available_embedding_models()}")
        print(f"🎯 Default model: {get_default_embedding_model()}")
        print()
        
        # Test 1: Basic embedding generation
        test_text = "This is a test sentence for embedding generation."
        print(f"🧪 Test 1: Basic embedding generation")
        print(f"Input text: '{test_text}'")
        
        result = await generate_embedding(text=test_text)
        
        print(f"✅ Embedding generated successfully!")
        print(f"📏 Dimensions: {result['dimensions']}")
        print(f"🤖 Model used: {result['model']}")
        print(f"💰 Cost estimate: ${result.get('cost_estimate', 'N/A')}")
        print(f"🔢 First 5 values: {result['values'][:5]}")
        print()
        
        # Test 2: Different model
        print(f"🧪 Test 2: Using embedding-001 model")
        result2 = await generate_embedding(
            text=test_text,
            model_name="embedding-001"
        )
        
        print(f"✅ Embedding with embedding-001 generated!")
        print(f"📏 Dimensions: {result2['dimensions']}")
        print(f"🤖 Model used: {result2['model']}")
        print(f"💰 Cost estimate: ${result2.get('cost_estimate', 'N/A')}")
        print()
        
        # Test 3: With task type
        print(f"🧪 Test 3: Using SEMANTIC_SIMILARITY task type")
        result3 = await generate_embedding(
            text=test_text,
            task_type="SEMANTIC_SIMILARITY"
        )
        
        print(f"✅ Embedding with task type generated!")
        print(f"📏 Dimensions: {result3['dimensions']}")
        print(f"🎯 Task type: {result3.get('task_type', 'N/A')}")
        print()
        
        # Test 4: Error handling - empty text
        print(f"🧪 Test 4: Error handling (empty text)")
        try:
            await generate_embedding(text="")
            print("❌ Should have failed with empty text!")
        except ValueError as e:
            print(f"✅ Correctly caught error: {e}")
        print()
        
        print("🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_embedding_generation())
