WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.579 align:start position:0%
 
if<00:00:00.060><c> you're</c><00:00:00.179><c> just</c><00:00:00.390><c> tuning</c><00:00:00.570><c> in</c><00:00:00.840><c> now</c><00:00:00.900><c> today</c><00:00:01.380><c> we're</c>

00:00:01.579 --> 00:00:01.589 align:start position:0%
if you're just tuning in now today we're
 

00:00:01.589 --> 00:00:05.240 align:start position:0%
if you're just tuning in now today we're
gonna<00:00:01.709><c> be</c><00:00:02.040><c> looking</c><00:00:02.490><c> at</c><00:00:02.909><c> how</c><00:00:03.300><c> to</c><00:00:03.950><c> create</c><00:00:04.950><c> the</c>

00:00:05.240 --> 00:00:05.250 align:start position:0%
gonna be looking at how to create the
 

00:00:05.250 --> 00:00:08.120 align:start position:0%
gonna be looking at how to create the
database<00:00:05.549><c> which</c><00:00:06.240><c> has</c><00:00:06.270><c> the</c><00:00:07.080><c> following</c><00:00:07.350><c> create</c>

00:00:08.120 --> 00:00:08.130 align:start position:0%
database which has the following create
 

00:00:08.130 --> 00:00:10.280 align:start position:0%
database which has the following create
statement<00:00:08.700><c> it's</c><00:00:08.790><c> got</c><00:00:08.910><c> the</c><00:00:09.030><c> message</c><00:00:09.360><c> table</c><00:00:09.900><c> the</c>

00:00:10.280 --> 00:00:10.290 align:start position:0%
statement it's got the message table the
 

00:00:10.290 --> 00:00:13.580 align:start position:0%
statement it's got the message table the
client<00:00:10.889><c> table</c><00:00:11.340><c> the</c><00:00:11.550><c> black</c><00:00:11.759><c> spas</c><00:00:12.059><c> table</c><00:00:12.690><c> post</c>

00:00:13.580 --> 00:00:13.590 align:start position:0%
client table the black spas table post
 

00:00:13.590 --> 00:00:15.350 align:start position:0%
client table the black spas table post
table<00:00:14.099><c> and</c><00:00:14.250><c> a</c><00:00:14.280><c> couple</c><00:00:14.309><c> other</c><00:00:14.670><c> ones</c><00:00:14.910><c> and</c><00:00:15.179><c> this</c>

00:00:15.350 --> 00:00:15.360 align:start position:0%
table and a couple other ones and this
 

00:00:15.360 --> 00:00:16.790 align:start position:0%
table and a couple other ones and this
is<00:00:15.509><c> the</c><00:00:15.630><c> code</c><00:00:15.839><c> that's</c><00:00:16.020><c> actually</c><00:00:16.199><c> going</c><00:00:16.590><c> to</c><00:00:16.680><c> be</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
is the code that's actually going to be
 

00:00:16.800 --> 00:00:19.189 align:start position:0%
is the code that's actually going to be
used<00:00:17.039><c> in</c><00:00:17.310><c> order</c><00:00:17.940><c> to</c><00:00:18.090><c> create</c><00:00:18.330><c> it</c><00:00:18.510><c> that's</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
used in order to create it that's
 

00:00:19.199 --> 00:00:22.519 align:start position:0%
used in order to create it that's
actually<00:00:20.330><c> created</c><00:00:21.330><c> with</c><00:00:21.539><c> our</c><00:00:21.570><c> Sam</c><00:00:22.020><c> we</c>

00:00:22.519 --> 00:00:22.529 align:start position:0%
actually created with our Sam we
 

00:00:22.529 --> 00:00:26.570 align:start position:0%
actually created with our Sam we
currently<00:00:22.740><c> have</c><00:00:23.160><c> Sam</c><00:00:23.760><c> running</c><00:00:24.420><c> and</c><00:00:25.580><c> we're</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
currently have Sam running and we're
 

00:00:26.580 --> 00:00:28.880 align:start position:0%
currently have Sam running and we're
gonna<00:00:26.699><c> go</c><00:00:26.910><c> into</c><00:00:27.210><c> information</c><00:00:28.080><c> schema</c><00:00:28.349><c> I</c>

00:00:28.880 --> 00:00:28.890 align:start position:0%
gonna go into information schema I
 

00:00:28.890 --> 00:00:30.560 align:start position:0%
gonna go into information schema I
sequel<00:00:29.429><c> I</c><00:00:29.609><c> want</c><00:00:29.820><c> to</c><00:00:29.880><c> create</c><00:00:30.060><c> a</c><00:00:30.150><c> new</c><00:00:30.359><c> database</c>

00:00:30.560 --> 00:00:30.570 align:start position:0%
sequel I want to create a new database
 

00:00:30.570 --> 00:00:34.209 align:start position:0%
sequel I want to create a new database
just<00:00:31.260><c> to</c><00:00:31.410><c> give</c><00:00:31.590><c> you</c><00:00:31.710><c> an</c><00:00:31.830><c> example</c><00:00:32.160><c> database</c><00:00:32.700><c> is</c>

00:00:34.209 --> 00:00:34.219 align:start position:0%
just to give you an example database is
 

00:00:34.219 --> 00:00:38.119 align:start position:0%
just to give you an example database is
loads<00:00:35.219><c> my</c><00:00:35.340><c> database</c><00:00:35.820><c> section</c><00:00:36.559><c> we</c><00:00:37.559><c> have</c><00:00:37.680><c> create</c>

00:00:38.119 --> 00:00:38.129 align:start position:0%
loads my database section we have create
 

00:00:38.129 --> 00:00:39.500 align:start position:0%
loads my database section we have create
a<00:00:38.160><c> database</c><00:00:38.579><c> and</c><00:00:38.940><c> we're</c><00:00:39.030><c> gonna</c><00:00:39.149><c> call</c><00:00:39.480><c> this</c>

00:00:39.500 --> 00:00:39.510 align:start position:0%
a database and we're gonna call this
 

00:00:39.510 --> 00:00:48.010 align:start position:0%
a database and we're gonna call this
koala<00:00:41.329><c> ok</c><00:00:42.329><c> koala</c><00:00:42.899><c> CMS</c><00:00:44.300><c> secret</c><00:00:45.300><c> and</c><00:00:45.620><c> create</c>

00:00:48.010 --> 00:00:48.020 align:start position:0%
koala ok koala CMS secret and create
 

00:00:48.020 --> 00:00:51.170 align:start position:0%
koala ok koala CMS secret and create
well<00:00:49.020><c> database</c><00:00:49.500><c> well</c><00:00:49.739><c> CMS</c><00:00:50.430><c> has</c><00:00:50.579><c> been</c><00:00:50.760><c> created</c>

00:00:51.170 --> 00:00:51.180 align:start position:0%
well database well CMS has been created
 

00:00:51.180 --> 00:00:53.240 align:start position:0%
well database well CMS has been created
and<00:00:51.360><c> there</c><00:00:51.480><c> it</c><00:00:51.600><c> is</c><00:00:51.750><c> now</c><00:00:52.500><c> we're</c><00:00:52.680><c> gonna</c><00:00:52.829><c> add</c><00:00:53.190><c> some</c>

00:00:53.240 --> 00:00:53.250 align:start position:0%
and there it is now we're gonna add some
 

00:00:53.250 --> 00:00:57.319 align:start position:0%
and there it is now we're gonna add some
sequel<00:00:54.059><c> to</c><00:00:54.600><c> this</c><00:00:55.079><c> database</c><00:00:55.620><c> right</c><00:00:56.070><c> now</c><00:00:56.329><c> so</c>

00:00:57.319 --> 00:00:57.329 align:start position:0%
sequel to this database right now so
 

00:00:57.329 --> 00:00:58.639 align:start position:0%
sequel to this database right now so
we're<00:00:57.480><c> gonna</c><00:00:57.600><c> go</c><00:00:57.780><c> in</c><00:00:57.899><c> here</c><00:00:58.170><c> I'm</c><00:00:58.410><c> gonna</c><00:00:58.559><c> add</c>

00:00:58.639 --> 00:00:58.649 align:start position:0%
we're gonna go in here I'm gonna add
 

00:00:58.649 --> 00:01:00.950 align:start position:0%
we're gonna go in here I'm gonna add
everything<00:00:59.100><c> except</c><00:00:59.850><c> for</c><00:01:00.180><c> the</c><00:01:00.510><c> insert</c>

00:01:00.950 --> 00:01:00.960 align:start position:0%
everything except for the insert
 

00:01:00.960 --> 00:01:06.380 align:start position:0%
everything except for the insert
statement<00:01:03.680><c> what</c><00:01:04.710><c> also</c><00:01:05.150><c> everything</c><00:01:06.150><c> other</c>

00:01:06.380 --> 00:01:06.390 align:start position:0%
statement what also everything other
 

00:01:06.390 --> 00:01:10.280 align:start position:0%
statement what also everything other
than<00:01:06.479><c> that</c><00:01:06.810><c> insert</c><00:01:07.200><c> statement</c><00:01:07.680><c> so</c><00:01:09.170><c> every</c><00:01:10.170><c> new</c>

00:01:10.280 --> 00:01:10.290 align:start position:0%
than that insert statement so every new
 

00:01:10.290 --> 00:01:14.450 align:start position:0%
than that insert statement so every new
insert<00:01:10.619><c> statements</c><00:01:11.310><c> it</c><00:01:12.119><c> ctrl</c><00:01:12.750><c> C</c><00:01:13.220><c> and</c><00:01:14.220><c> let's</c>

00:01:14.450 --> 00:01:14.460 align:start position:0%
insert statements it ctrl C and let's
 

00:01:14.460 --> 00:01:16.609 align:start position:0%
insert statements it ctrl C and let's
see<00:01:14.610><c> what</c><00:01:14.729><c> happens</c><00:01:15.030><c> when</c><00:01:15.210><c> we</c><00:01:15.360><c> faced</c><00:01:15.750><c> this</c><00:01:16.049><c> make</c>

00:01:16.609 --> 00:01:16.619 align:start position:0%
see what happens when we faced this make
 

00:01:16.619 --> 00:01:18.830 align:start position:0%
see what happens when we faced this make
sure<00:01:16.770><c> that</c><00:01:16.890><c> everything</c><00:01:17.250><c> is</c><00:01:17.540><c> called</c><00:01:18.540><c> smells</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
sure that everything is called smells
 

00:01:18.840 --> 00:01:20.990 align:start position:0%
sure that everything is called smells
currently<00:01:19.229><c> were</c><00:01:19.350><c> in</c><00:01:19.500><c> koala</c><00:01:19.860><c> CMS</c><00:01:20.400><c> we</c><00:01:20.700><c> run</c><00:01:20.880><c> the</c>

00:01:20.990 --> 00:01:21.000 align:start position:0%
currently were in koala CMS we run the
 

00:01:21.000 --> 00:01:22.580 align:start position:0%
currently were in koala CMS we run the
following<00:01:21.390><c> query</c><00:01:21.630><c> and</c><00:01:21.840><c> this</c><00:01:22.020><c> query</c><00:01:22.380><c> actually</c>

00:01:22.580 --> 00:01:22.590 align:start position:0%
following query and this query actually
 

00:01:22.590 --> 00:01:24.499 align:start position:0%
following query and this query actually
creates<00:01:23.189><c> our</c><00:01:23.369><c> tables</c><00:01:23.820><c> which</c><00:01:23.970><c> weren't</c><00:01:24.180><c> there</c><00:01:24.479><c> a</c>

00:01:24.499 --> 00:01:24.509 align:start position:0%
creates our tables which weren't there a
 

00:01:24.509 --> 00:01:25.160 align:start position:0%
creates our tables which weren't there a
second<00:01:24.930><c> ago</c>

00:01:25.160 --> 00:01:25.170 align:start position:0%
second ago
 

00:01:25.170 --> 00:01:27.499 align:start position:0%
second ago
so<00:01:26.070><c> let's</c><00:01:26.250><c> have</c><00:01:26.340><c> a</c><00:01:26.400><c> look</c><00:01:26.640><c> here</c><00:01:26.820><c> and</c><00:01:27.119><c> boom</c>

00:01:27.499 --> 00:01:27.509 align:start position:0%
so let's have a look here and boom
 

00:01:27.509 --> 00:01:29.210 align:start position:0%
so let's have a look here and boom
there's<00:01:27.869><c> our</c><00:01:28.020><c> tables</c><00:01:28.500><c> that</c><00:01:28.710><c> are</c><00:01:28.770><c> newly</c>

00:01:29.210 --> 00:01:29.220 align:start position:0%
there's our tables that are newly
 

00:01:29.220 --> 00:01:30.980 align:start position:0%
there's our tables that are newly
created<00:01:29.700><c> it'll</c><00:01:30.270><c> tell</c><00:01:30.450><c> you</c><00:01:30.570><c> everything</c><00:01:30.600><c> on</c><00:01:30.960><c> the</c>

00:01:30.980 --> 00:01:30.990 align:start position:0%
created it'll tell you everything on the
 

00:01:30.990 --> 00:01:33.710 align:start position:0%
created it'll tell you everything on the
cloning<00:01:31.409><c> table</c><00:01:31.710><c> comment</c><00:01:32.189><c> message</c><00:01:32.610><c> and</c><00:01:33.090><c> post</c>

00:01:33.710 --> 00:01:33.720 align:start position:0%
cloning table comment message and post
 

00:01:33.720 --> 00:01:36.350 align:start position:0%
cloning table comment message and post
so<00:01:34.320><c> let's</c><00:01:35.130><c> actually</c><00:01:35.400><c> have</c><00:01:35.729><c> a</c><00:01:35.790><c> look</c><00:01:35.909><c> at</c><00:01:36.180><c> what</c><00:01:36.329><c> a</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
so let's actually have a look at what a
 

00:01:36.360 --> 00:01:42.590 align:start position:0%
so let's actually have a look at what a
post<00:01:36.780><c> is</c><00:01:37.140><c> ok</c><00:01:38.040><c> I</c><00:01:39.140><c> see</c><00:01:40.140><c> cool</c><00:01:40.970><c> we</c><00:01:41.970><c> got</c><00:01:42.090><c> a</c><00:01:42.119><c> post</c><00:01:42.390><c> ID</c>

00:01:42.590 --> 00:01:42.600 align:start position:0%
post is ok I see cool we got a post ID
 

00:01:42.600 --> 00:01:44.810 align:start position:0%
post is ok I see cool we got a post ID
text<00:01:43.290><c> client</c><00:01:43.680><c> ID</c><00:01:43.799><c> and</c><00:01:43.920><c> the</c><00:01:44.280><c> amount</c><00:01:44.520><c> of</c><00:01:44.579><c> likes</c>

00:01:44.810 --> 00:01:44.820 align:start position:0%
text client ID and the amount of likes
 

00:01:44.820 --> 00:01:47.179 align:start position:0%
text client ID and the amount of likes
of<00:01:45.000><c> a</c><00:01:45.090><c> specific</c><00:01:45.600><c> post</c><00:01:45.899><c> but</c><00:01:46.770><c> what</c><00:01:46.890><c> are</c><00:01:46.979><c> they</c>

00:01:47.179 --> 00:01:47.189 align:start position:0%
of a specific post but what are they
 

00:01:47.189 --> 00:01:50.920 align:start position:0%
of a specific post but what are they
interested<00:01:47.399><c> now</c><00:01:48.890><c> hidden</c><00:01:49.890><c> desk</c><00:01:50.250><c> create</c><00:01:50.579><c> huh</c>

00:01:50.920 --> 00:01:50.930 align:start position:0%
interested now hidden desk create huh
 

00:01:50.930 --> 00:01:52.910 align:start position:0%
interested now hidden desk create huh
inserted<00:01:51.930><c> the</c><00:01:52.020><c> client</c><00:01:52.409><c> what</c><00:01:52.619><c> was</c><00:01:52.710><c> the</c><00:01:52.799><c> last</c>

00:01:52.910 --> 00:01:52.920 align:start position:0%
inserted the client what was the last
 

00:01:52.920 --> 00:01:55.490 align:start position:0%
inserted the client what was the last
thing<00:01:53.130><c> we</c><00:01:53.280><c> created</c><00:01:53.430><c> a</c><00:01:54.090><c> message</c><00:01:55.020><c> and</c><00:01:55.259><c> post</c>

00:01:55.490 --> 00:01:55.500 align:start position:0%
thing we created a message and post
 

00:01:55.500 --> 00:01:59.030 align:start position:0%
thing we created a message and post
table<00:01:56.960><c> she</c><00:01:57.960><c> dragged</c><00:01:58.200><c> before</c><00:01:58.350><c> insert</c><00:01:58.890><c> into</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
table she dragged before insert into
 

00:01:59.040 --> 00:02:04.830 align:start position:0%
table she dragged before insert into
claim

00:02:04.830 --> 00:02:04.840 align:start position:0%
 
 

00:02:04.840 --> 00:02:07.990 align:start position:0%
 
incident<00:02:05.840><c> to</c><00:02:05.929><c> client</c><00:02:06.469><c> client</c><00:02:07.130><c> email</c><00:02:07.460><c> crema</c>

00:02:07.990 --> 00:02:08.000 align:start position:0%
incident to client client email crema
 

00:02:08.000 --> 00:02:12.400 align:start position:0%
incident to client client email crema
13:46<00:02:08.780><c> Ongina</c><00:02:09.410><c> and</c><00:02:09.590><c> pizza</c><00:02:10.099><c> okay</c><00:02:10.910><c> so</c><00:02:11.660><c> let's</c>

00:02:12.400 --> 00:02:12.410 align:start position:0%
13:46 Ongina and pizza okay so let's
 

00:02:12.410 --> 00:02:17.080 align:start position:0%
13:46 Ongina and pizza okay so let's
look<00:02:12.560><c> at</c><00:02:12.709><c> our</c><00:02:12.830><c> client</c><00:02:13.370><c> table</c><00:02:13.730><c> here</c><00:02:16.090><c> okay</c>

00:02:17.080 --> 00:02:17.090 align:start position:0%
look at our client table here okay
 

00:02:17.090 --> 00:02:18.460 align:start position:0%
look at our client table here okay
that's<00:02:17.300><c> everything</c><00:02:17.720><c> that</c><00:02:17.870><c> has</c><00:02:18.080><c> to</c><00:02:18.200><c> do</c><00:02:18.290><c> with</c><00:02:18.319><c> a</c>

00:02:18.460 --> 00:02:18.470 align:start position:0%
that's everything that has to do with a
 

00:02:18.470 --> 00:02:23.619 align:start position:0%
that's everything that has to do with a
client<00:02:18.980><c> sorry</c><00:02:21.940><c> you</c><00:02:22.940><c> can</c><00:02:23.060><c> see</c><00:02:23.180><c> we</c><00:02:23.299><c> have</c><00:02:23.390><c> a</c><00:02:23.420><c> lot</c>

00:02:23.619 --> 00:02:23.629 align:start position:0%
client sorry you can see we have a lot
 

00:02:23.629 --> 00:02:25.420 align:start position:0%
client sorry you can see we have a lot
of<00:02:23.720><c> fields</c><00:02:24.019><c> the</c><00:02:24.110><c> client</c><00:02:24.530><c> ID</c><00:02:24.650><c> the</c><00:02:24.890><c> client</c><00:02:25.250><c> name</c>

00:02:25.420 --> 00:02:25.430 align:start position:0%
of fields the client ID the client name
 

00:02:25.430 --> 00:02:27.670 align:start position:0%
of fields the client ID the client name
client<00:02:25.910><c> email</c><00:02:26.030><c> password</c><00:02:26.690><c> analytics</c><00:02:27.230><c> code</c><00:02:27.410><c> and</c>

00:02:27.670 --> 00:02:27.680 align:start position:0%
client email password analytics code and
 

00:02:27.680 --> 00:02:30.039 align:start position:0%
client email password analytics code and
a<00:02:27.709><c> client</c><00:02:28.040><c> creation</c><00:02:28.580><c> date</c><00:02:28.819><c> so</c><00:02:29.629><c> that's</c><00:02:29.750><c> a</c><00:02:29.900><c> lot</c>

00:02:30.039 --> 00:02:30.049 align:start position:0%
a client creation date so that's a lot
 

00:02:30.049 --> 00:02:32.500 align:start position:0%
a client creation date so that's a lot
of<00:02:30.080><c> stuff</c><00:02:30.440><c> let's</c><00:02:30.769><c> do</c><00:02:30.980><c> just</c><00:02:31.489><c> one</c><00:02:31.819><c> simple</c><00:02:32.239><c> query</c>

00:02:32.500 --> 00:02:32.510 align:start position:0%
of stuff let's do just one simple query
 

00:02:32.510 --> 00:02:34.360 align:start position:0%
of stuff let's do just one simple query
which<00:02:32.780><c> is</c><00:02:32.900><c> going</c><00:02:33.049><c> to</c><00:02:33.110><c> add</c><00:02:33.290><c> a</c><00:02:33.319><c> new</c><00:02:33.590><c> client</c><00:02:34.129><c> so</c>

00:02:34.360 --> 00:02:34.370 align:start position:0%
which is going to add a new client so
 

00:02:34.370 --> 00:02:37.089 align:start position:0%
which is going to add a new client so
we're<00:02:34.700><c> going</c><00:02:34.849><c> to</c><00:02:34.940><c> sequel</c><00:02:35.590><c> and</c><00:02:36.590><c> we're</c><00:02:36.890><c> going</c><00:02:37.040><c> to</c>

00:02:37.089 --> 00:02:37.099 align:start position:0%
we're going to sequel and we're going to
 

00:02:37.099 --> 00:02:42.670 align:start position:0%
we're going to sequel and we're going to
incident<00:02:37.610><c> to</c><00:02:37.730><c> client</c><00:02:38.150><c> this</c><00:02:38.630><c> is</c><00:02:40.840><c> and</c><00:02:41.840><c> how</c><00:02:42.500><c> do</c><00:02:42.560><c> I</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
incident to client this is and how do I
 

00:02:42.680 --> 00:02:49.600 align:start position:0%
incident to client this is and how do I
run<00:02:42.920><c> it</c><00:02:43.099><c> I</c><00:02:43.720><c> did</c><00:02:44.720><c> it</c><00:02:44.810><c> go</c><00:02:46.000><c> okay</c><00:02:47.410><c> Donna</c><00:02:48.519><c> insert</c><00:02:49.519><c> it</c>

00:02:49.600 --> 00:02:49.610 align:start position:0%
run it I did it go okay Donna insert it
 

00:02:49.610 --> 00:02:51.130 align:start position:0%
run it I did it go okay Donna insert it
into<00:02:49.760><c> the</c><00:02:49.940><c> client</c><00:02:50.299><c> now</c><00:02:50.420><c> if</c><00:02:50.569><c> I</c><00:02:50.660><c> go</c><00:02:50.810><c> back</c><00:02:50.989><c> into</c>

00:02:51.130 --> 00:02:51.140 align:start position:0%
into the client now if I go back into
 

00:02:51.140 --> 00:02:54.880 align:start position:0%
into the client now if I go back into
the<00:02:51.290><c> client</c><00:02:51.650><c> table</c><00:02:51.980><c> I</c><00:02:52.870><c> see</c><00:02:53.870><c> that</c><00:02:54.709><c> there's</c>

00:02:54.880 --> 00:02:54.890 align:start position:0%
the client table I see that there's
 

00:02:54.890 --> 00:02:56.589 align:start position:0%
the client table I see that there's
actually<00:02:55.040><c> going</c><00:02:55.310><c> to</c><00:02:55.370><c> be</c><00:02:55.430><c> one</c><00:02:55.760><c> column</c><00:02:56.239><c> and</c>

00:02:56.589 --> 00:02:56.599 align:start position:0%
actually going to be one column and
 

00:02:56.599 --> 00:02:59.050 align:start position:0%
actually going to be one column and
that's<00:02:56.690><c> Kramer</c><00:02:57.110><c> 16</c><00:02:57.590><c> and</c><00:02:57.829><c> pizza</c><00:02:58.250><c> so</c><00:02:58.910><c> that's</c>

00:02:59.050 --> 00:02:59.060 align:start position:0%
that's Kramer 16 and pizza so that's
 

00:02:59.060 --> 00:03:01.270 align:start position:0%
that's Kramer 16 and pizza so that's
also<00:02:59.269><c> in</c><00:02:59.660><c> the</c><00:02:59.780><c> actual</c><00:02:59.930><c> project</c><00:03:00.739><c> code</c><00:03:01.010><c> that's</c>

00:03:01.270 --> 00:03:01.280 align:start position:0%
also in the actual project code that's
 

00:03:01.280 --> 00:03:05.589 align:start position:0%
also in the actual project code that's
how<00:03:01.430><c> we</c><00:03:01.900><c> end</c><00:03:02.900><c> up</c><00:03:03.049><c> creating</c><00:03:03.879><c> new</c><00:03:04.879><c> entries</c><00:03:05.360><c> which</c>

00:03:05.589 --> 00:03:05.599 align:start position:0%
how we end up creating new entries which
 

00:03:05.599 --> 00:03:07.660 align:start position:0%
how we end up creating new entries which
is<00:03:05.780><c> there's</c><00:03:06.500><c> rows</c><00:03:06.739><c> and</c><00:03:07.040><c> columns</c><00:03:07.430><c> within</c>

00:03:07.660 --> 00:03:07.670 align:start position:0%
is there's rows and columns within
 

00:03:07.670 --> 00:03:10.780 align:start position:0%
is there's rows and columns within
sequel<00:03:08.299><c> so</c><00:03:08.540><c> just</c><00:03:09.200><c> remember</c><00:03:09.470><c> this</c><00:03:10.160><c> is</c><00:03:10.370><c> the</c><00:03:10.609><c> row</c>

00:03:10.780 --> 00:03:10.790 align:start position:0%
sequel so just remember this is the row
 

00:03:10.790 --> 00:03:14.259 align:start position:0%
sequel so just remember this is the row
and<00:03:11.450><c> this</c><00:03:12.170><c> is</c><00:03:12.380><c> the</c><00:03:12.590><c> column</c><00:03:12.799><c> names</c><00:03:13.519><c> over</c><00:03:13.910><c> here</c>

00:03:14.259 --> 00:03:14.269 align:start position:0%
and this is the column names over here
 

00:03:14.269 --> 00:03:17.170 align:start position:0%
and this is the column names over here
okay<00:03:15.230><c> so</c><00:03:15.290><c> that</c><00:03:15.590><c> that's</c><00:03:16.579><c> that</c><00:03:16.819><c> now</c><00:03:17.030><c> let's</c>

00:03:17.170 --> 00:03:17.180 align:start position:0%
okay so that that's that now let's
 

00:03:17.180 --> 00:03:19.569 align:start position:0%
okay so that that's that now let's
create<00:03:17.329><c> another</c><00:03:17.720><c> table</c><00:03:18.190><c> called</c><00:03:19.190><c> the</c><00:03:19.310><c> blog</c>

00:03:19.569 --> 00:03:19.579 align:start position:0%
create another table called the blog
 

00:03:19.579 --> 00:03:21.520 align:start position:0%
create another table called the blog
post<00:03:19.940><c> table</c><00:03:20.510><c> as</c><00:03:20.690><c> you</c><00:03:20.870><c> can</c><00:03:20.989><c> see</c><00:03:21.109><c> there's</c><00:03:21.290><c> a</c><00:03:21.349><c> blog</c>

00:03:21.520 --> 00:03:21.530 align:start position:0%
post table as you can see there's a blog
 

00:03:21.530 --> 00:03:24.309 align:start position:0%
post table as you can see there's a blog
post<00:03:21.799><c> idea</c><00:03:22.130><c> title</c><00:03:22.639><c> of</c><00:03:22.760><c> content</c><00:03:23.420><c> client</c><00:03:23.900><c> ID</c><00:03:24.019><c> and</c>

00:03:24.309 --> 00:03:24.319 align:start position:0%
post idea title of content client ID and
 

00:03:24.319 --> 00:03:27.190 align:start position:0%
post idea title of content client ID and
post<00:03:24.950><c> publish</c><00:03:25.370><c> date</c><00:03:25.639><c> and</c><00:03:26.359><c> the</c><00:03:26.510><c> primary</c><00:03:26.900><c> key</c><00:03:27.109><c> is</c>

00:03:27.190 --> 00:03:27.200 align:start position:0%
post publish date and the primary key is
 

00:03:27.200 --> 00:03:28.809 align:start position:0%
post publish date and the primary key is
going<00:03:27.349><c> to</c><00:03:27.410><c> be</c><00:03:27.470><c> defined</c><00:03:27.769><c> as</c><00:03:27.859><c> the</c><00:03:27.980><c> blog</c><00:03:28.220><c> post</c><00:03:28.549><c> ID</c>

00:03:28.809 --> 00:03:28.819 align:start position:0%
going to be defined as the blog post ID
 

00:03:28.819 --> 00:03:31.420 align:start position:0%
going to be defined as the blog post ID
defines<00:03:29.510><c> a</c><00:03:29.630><c> couple</c><00:03:29.900><c> of</c><00:03:30.099><c> foreign</c><00:03:31.099><c> keys</c><00:03:31.400><c> and</c>

00:03:31.420 --> 00:03:31.430 align:start position:0%
defines a couple of foreign keys and
 

00:03:31.430 --> 00:03:32.680 align:start position:0%
defines a couple of foreign keys and
we're<00:03:31.760><c> going</c><00:03:31.880><c> to</c><00:03:31.910><c> talk</c><00:03:32.090><c> about</c><00:03:32.329><c> that</c><00:03:32.480><c> in</c><00:03:32.599><c> a</c>

00:03:32.680 --> 00:03:32.690 align:start position:0%
we're going to talk about that in a
 

00:03:32.690 --> 00:03:34.690 align:start position:0%
we're going to talk about that in a
future<00:03:32.959><c> in</c><00:03:33.200><c> future</c><00:03:33.380><c> videos</c><00:03:33.919><c> but</c><00:03:34.190><c> now</c><00:03:34.489><c> we're</c>

00:03:34.690 --> 00:03:34.700 align:start position:0%
future in future videos but now we're
 

00:03:34.700 --> 00:03:37.300 align:start position:0%
future in future videos but now we're
just<00:03:34.940><c> getting</c><00:03:35.720><c> started</c><00:03:35.870><c> as</c><00:03:36.410><c> you</c><00:03:36.919><c> can</c><00:03:37.040><c> see</c><00:03:37.190><c> it's</c>

00:03:37.300 --> 00:03:37.310 align:start position:0%
just getting started as you can see it's
 

00:03:37.310 --> 00:03:39.819 align:start position:0%
just getting started as you can see it's
very<00:03:37.370><c> very</c><00:03:37.609><c> easy</c><00:03:37.910><c> with</c><00:03:38.510><c> this</c><00:03:38.720><c> user</c><00:03:39.530><c> interface</c>

00:03:39.819 --> 00:03:39.829 align:start position:0%
very very easy with this user interface
 

00:03:39.829 --> 00:03:42.280 align:start position:0%
very very easy with this user interface
to<00:03:40.669><c> create</c><00:03:40.849><c> new</c><00:03:41.239><c> things</c><00:03:41.480><c> and</c><00:03:41.780><c> run</c><00:03:41.870><c> different</c>

00:03:42.280 --> 00:03:42.290 align:start position:0%
to create new things and run different
 

00:03:42.290 --> 00:03:44.890 align:start position:0%
to create new things and run different
sequel<00:03:42.709><c> queries</c><00:03:43.099><c> we</c><00:03:43.970><c> just</c><00:03:44.180><c> paste</c><00:03:44.450><c> stuff</c><00:03:44.690><c> in</c>

00:03:44.890 --> 00:03:44.900 align:start position:0%
sequel queries we just paste stuff in
 

00:03:44.900 --> 00:03:46.659 align:start position:0%
sequel queries we just paste stuff in
there<00:03:45.079><c> create</c><00:03:45.560><c> a</c><00:03:45.620><c> table</c><00:03:45.829><c> class</c><00:03:46.099><c> blog</c><00:03:46.340><c> post</c>

00:03:46.659 --> 00:03:46.669 align:start position:0%
there create a table class blog post
 

00:03:46.669 --> 00:03:49.539 align:start position:0%
there create a table class blog post
something<00:03:47.030><c> didn't</c><00:03:47.329><c> work</c><00:03:47.419><c> for</c><00:03:48.250><c> years</c><00:03:49.250><c> were</c>

00:03:49.539 --> 00:03:49.549 align:start position:0%
something didn't work for years were
 

00:03:49.549 --> 00:03:51.309 align:start position:0%
something didn't work for years were
shown<00:03:49.790><c> the</c><00:03:49.970><c> symbol</c><00:03:50.299><c> name</c><00:03:50.419><c> was</c><00:03:50.569><c> expected</c><00:03:51.139><c> near</c>

00:03:51.309 --> 00:03:51.319 align:start position:0%
shown the symbol name was expected near
 

00:03:51.319 --> 00:03:57.009 align:start position:0%
shown the symbol name was expected near
title<00:03:51.950><c> at</c><00:03:52.489><c> position</c><00:03:52.940><c> 88</c><00:03:55.810><c> symbol</c><00:03:56.810><c> name</c><00:03:56.900><c> was</c>

00:03:57.009 --> 00:03:57.019 align:start position:0%
title at position 88 symbol name was
 

00:03:57.019 --> 00:03:59.559 align:start position:0%
title at position 88 symbol name was
expected<00:03:57.650><c> near</c><00:03:57.859><c> title</c><00:03:58.370><c> this</c><00:03:58.730><c> blog</c><00:03:59.030><c> post</c><00:03:59.060><c> was</c>

00:03:59.559 --> 00:03:59.569 align:start position:0%
expected near title this blog post was
 

00:03:59.569 --> 00:04:05.860 align:start position:0%
expected near title this blog post was
not<00:03:59.599><c> created</c><00:04:00.380><c> let's</c><00:04:01.250><c> go</c><00:04:01.519><c> back</c><00:04:04.450><c> did</c><00:04:05.450><c> you</c><00:04:05.480><c> forget</c>

00:04:05.860 --> 00:04:05.870 align:start position:0%
not created let's go back did you forget
 

00:04:05.870 --> 00:04:12.699 align:start position:0%
not created let's go back did you forget
the<00:04:07.840><c> great</c><00:04:08.840><c> table</c><00:04:09.200><c> blog</c><00:04:09.620><c> post</c><00:04:11.500><c> maybe</c><00:04:12.500><c> we</c>

00:04:12.699 --> 00:04:12.709 align:start position:0%
the great table blog post maybe we
 

00:04:12.709 --> 00:04:14.709 align:start position:0%
the great table blog post maybe we
didn't<00:04:12.950><c> create</c><00:04:13.219><c> this</c><00:04:13.519><c> table</c><00:04:13.760><c> the</c><00:04:14.360><c> message</c>

00:04:14.709 --> 00:04:14.719 align:start position:0%
didn't create this table the message
 

00:04:14.719 --> 00:04:20.490 align:start position:0%
didn't create this table the message
table<00:04:18.700><c> sequel</c><00:04:19.700><c> let's</c><00:04:19.849><c> try</c><00:04:20.030><c> that</c><00:04:20.150><c> again</c>

00:04:20.490 --> 00:04:20.500 align:start position:0%
table sequel let's try that again
 

00:04:20.500 --> 00:04:28.380 align:start position:0%
table sequel let's try that again
[Music]

00:04:28.380 --> 00:04:28.390 align:start position:0%
 
 

00:04:28.390 --> 00:04:37.620 align:start position:0%
 
okay<00:04:29.390><c> title</c><00:04:30.580><c> used</c><00:04:31.580><c> to</c><00:04:31.760><c> be</c><00:04:31.940><c> in</c><00:04:32.270><c> these</c><00:04:32.450><c> special</c>

00:04:37.620 --> 00:04:37.630 align:start position:0%
 
 

00:04:37.630 --> 00:04:49.390 align:start position:0%
 
special<00:04:38.630><c> quotes</c><00:04:42.430><c> and</c><00:04:43.430><c> let's</c><00:04:44.180><c> run</c><00:04:44.390><c> that</c><00:04:48.400><c> and</c>

00:04:49.390 --> 00:04:49.400 align:start position:0%
special quotes and let's run that and
 

00:04:49.400 --> 00:04:52.409 align:start position:0%
special quotes and let's run that and
there<00:04:49.729><c> we</c><00:04:49.820><c> go</c><00:04:50.000><c> this</c><00:04:50.210><c> time</c><00:04:50.240><c> we've</c><00:04:50.539><c> created</c><00:04:50.750><c> the</c>

00:04:52.409 --> 00:04:52.419 align:start position:0%
there we go this time we've created the
 

00:04:52.419 --> 00:04:54.820 align:start position:0%
there we go this time we've created the
blog<00:04:53.419><c> post</c><00:04:53.780><c> table</c><00:04:54.169><c> so</c><00:04:54.289><c> we're</c><00:04:54.409><c> gonna</c><00:04:54.500><c> talk</c>

00:04:54.820 --> 00:04:54.830 align:start position:0%
blog post table so we're gonna talk
 

00:04:54.830 --> 00:04:56.050 align:start position:0%
blog post table so we're gonna talk
about<00:04:54.860><c> in</c><00:04:55.159><c> the</c><00:04:55.219><c> next</c><00:04:55.340><c> video</c><00:04:55.520><c> how</c><00:04:55.729><c> to</c><00:04:55.760><c> start</c>

00:04:56.050 --> 00:04:56.060 align:start position:0%
about in the next video how to start
 

00:04:56.060 --> 00:04:57.969 align:start position:0%
about in the next video how to start
inserting<00:04:56.390><c> rows</c><00:04:56.570><c> into</c><00:04:57.020><c> the</c><00:04:57.110><c> blog</c><00:04:57.320><c> post</c><00:04:57.590><c> table</c>

00:04:57.969 --> 00:04:57.979 align:start position:0%
inserting rows into the blog post table
 

00:04:57.979 --> 00:05:01.120 align:start position:0%
inserting rows into the blog post table
so<00:04:58.130><c> I'll</c><00:04:58.219><c> see</c><00:04:58.370><c> you</c><00:04:58.430><c> in</c><00:04:58.550><c> the</c><00:04:58.610><c> next</c><00:04:58.820><c> video</c>

