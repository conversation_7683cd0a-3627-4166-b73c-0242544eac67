WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.110 align:start position:0%
 
all<00:00:00.640><c> righty</c><00:00:01.000><c> guys</c><00:00:01.160><c> so</c><00:00:01.319><c> in</c><00:00:01.480><c> this</c><00:00:01.640><c> section</c><00:00:01.959><c> we're</c>

00:00:02.110 --> 00:00:02.120 align:start position:0%
all righty guys so in this section we're
 

00:00:02.120 --> 00:00:03.709 align:start position:0%
all righty guys so in this section we're
going<00:00:02.240><c> to</c><00:00:02.440><c> continue</c><00:00:02.879><c> answering</c><00:00:03.399><c> these</c>

00:00:03.709 --> 00:00:03.719 align:start position:0%
going to continue answering these
 

00:00:03.719 --> 00:00:05.869 align:start position:0%
going to continue answering these
questions<00:00:04.359><c> the</c><00:00:04.520><c> data</c><00:00:04.799><c> is</c><00:00:04.920><c> all</c><00:00:05.080><c> in</c><00:00:05.279><c> metabase</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
questions the data is all in metabase
 

00:00:05.879 --> 00:00:10.790 align:start position:0%
questions the data is all in metabase
very<00:00:06.600><c> cleanly</c><00:00:07.639><c> um</c><00:00:08.639><c> uh</c><00:00:09.200><c> just</c><00:00:09.559><c> as</c><00:00:09.719><c> we</c><00:00:09.920><c> did</c><00:00:10.480><c> as</c><00:00:10.679><c> in</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
very cleanly um uh just as we did as in
 

00:00:10.800 --> 00:00:12.669 align:start position:0%
very cleanly um uh just as we did as in
the<00:00:11.000><c> previous</c><00:00:11.440><c> video</c><00:00:11.840><c> so</c><00:00:12.120><c> let's</c><00:00:12.320><c> really</c><00:00:12.559><c> look</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
the previous video so let's really look
 

00:00:12.679 --> 00:00:14.669 align:start position:0%
the previous video so let's really look
at<00:00:12.840><c> this</c><00:00:13.000><c> question</c><00:00:13.320><c> please</c><00:00:13.599><c> identify</c><00:00:14.360><c> five</c>

00:00:14.669 --> 00:00:14.679 align:start position:0%
at this question please identify five
 

00:00:14.679 --> 00:00:17.189 align:start position:0%
at this question please identify five
traffic<00:00:15.120><c> sources</c><00:00:15.679><c> that</c><00:00:15.920><c> exceeded</c><00:00:16.520><c> the</c><00:00:16.720><c> target</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
traffic sources that exceeded the target
 

00:00:17.199 --> 00:00:19.950 align:start position:0%
traffic sources that exceeded the target
quality<00:00:18.160><c> a</c><00:00:18.359><c> traffic</c><00:00:18.720><c> source</c><00:00:19.039><c> is</c><00:00:19.160><c> a</c><00:00:19.400><c> sub</c>

00:00:19.950 --> 00:00:19.960 align:start position:0%
quality a traffic source is a sub
 

00:00:19.960 --> 00:00:22.029 align:start position:0%
quality a traffic source is a sub
publisher<00:00:20.680><c> all</c><00:00:20.800><c> right</c><00:00:20.960><c> so</c><00:00:21.119><c> we</c><00:00:21.279><c> group</c><00:00:21.600><c> the</c><00:00:21.720><c> data</c>

00:00:22.029 --> 00:00:22.039 align:start position:0%
publisher all right so we group the data
 

00:00:22.039 --> 00:00:24.910 align:start position:0%
publisher all right so we group the data
according<00:00:22.359><c> to</c><00:00:22.640><c> subp</c><00:00:23.199><c> Publishers</c><00:00:24.199><c> we</c><00:00:24.439><c> counted</c>

00:00:24.910 --> 00:00:24.920 align:start position:0%
according to subp Publishers we counted
 

00:00:24.920 --> 00:00:27.750 align:start position:0%
according to subp Publishers we counted
the<00:00:25.080><c> amount</c><00:00:25.560><c> of</c><00:00:26.080><c> deposits</c><00:00:27.080><c> uh</c><00:00:27.199><c> there's</c><00:00:27.439><c> one</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
the amount of deposits uh there's one
 

00:00:27.760 --> 00:00:30.390 align:start position:0%
the amount of deposits uh there's one
other<00:00:28.199><c> important</c><00:00:28.640><c> data</c><00:00:29.039><c> point</c><00:00:29.800><c> uh</c><00:00:30.160><c> that</c><00:00:30.279><c> we</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
other important data point uh that we
 

00:00:30.400 --> 00:00:33.950 align:start position:0%
other important data point uh that we
have<00:00:30.519><c> to</c><00:00:30.720><c> look</c><00:00:30.920><c> at</c><00:00:31.359><c> which</c><00:00:31.560><c> is</c><00:00:32.360><c> um</c><00:00:33.360><c> which</c><00:00:33.559><c> is</c><00:00:33.879><c> I</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
have to look at which is um which is I
 

00:00:33.960 --> 00:00:35.590 align:start position:0%
have to look at which is um which is I
think<00:00:34.200><c> the</c><00:00:34.360><c> amount</c><00:00:34.600><c> of</c><00:00:34.760><c> people</c><00:00:34.960><c> that</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
think the amount of people that
 

00:00:35.600 --> 00:00:37.950 align:start position:0%
think the amount of people that
registered<00:00:36.600><c> and</c><00:00:36.960><c> uh</c><00:00:37.160><c> we'll</c><00:00:37.360><c> do</c><00:00:37.520><c> that</c><00:00:37.640><c> in</c><00:00:37.760><c> a</c>

00:00:37.950 --> 00:00:37.960 align:start position:0%
registered and uh we'll do that in a
 

00:00:37.960 --> 00:00:39.790 align:start position:0%
registered and uh we'll do that in a
moment<00:00:38.280><c> for</c><00:00:38.480><c> now</c><00:00:38.719><c> let's</c><00:00:38.960><c> just</c><00:00:39.280><c> just</c><00:00:39.559><c> just</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
moment for now let's just just just
 

00:00:39.800 --> 00:00:42.470 align:start position:0%
moment for now let's just just just
count<00:00:40.120><c> the</c><00:00:40.440><c> the</c><00:00:40.600><c> sum</c><00:00:40.840><c> of</c><00:00:41.079><c> deposits</c><00:00:41.800><c> per</c><00:00:42.079><c> subp</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
count the the sum of deposits per subp
 

00:00:42.480 --> 00:00:45.229 align:start position:0%
count the the sum of deposits per subp
Publishers<00:00:43.239><c> and</c><00:00:43.920><c> the</c><00:00:44.039><c> thing</c><00:00:44.280><c> is</c><00:00:44.559><c> now</c><00:00:44.840><c> the</c><00:00:44.960><c> data</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
Publishers and the thing is now the data
 

00:00:45.239 --> 00:00:47.590 align:start position:0%
Publishers and the thing is now the data
is<00:00:45.399><c> not</c><00:00:45.680><c> is</c><00:00:45.960><c> not</c><00:00:46.440><c> uh</c><00:00:46.559><c> ordered</c><00:00:47.039><c> correctly</c><00:00:47.520><c> we</c>

00:00:47.590 --> 00:00:47.600 align:start position:0%
is not is not uh ordered correctly we
 

00:00:47.600 --> 00:00:49.670 align:start position:0%
is not is not uh ordered correctly we
want<00:00:47.719><c> to</c><00:00:47.960><c> group</c><00:00:48.280><c> it</c><00:00:48.480><c> according</c><00:00:48.800><c> to</c><00:00:48.960><c> the</c><00:00:49.120><c> sum</c><00:00:49.520><c> so</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
want to group it according to the sum so
 

00:00:49.680 --> 00:00:53.150 align:start position:0%
want to group it according to the sum so
I'm<00:00:49.760><c> going</c><00:00:49.879><c> to</c><00:00:50.039><c> go</c><00:00:50.840><c> into</c><00:00:51.840><c> the</c><00:00:52.079><c> actual</c><00:00:52.640><c> question</c>

00:00:53.150 --> 00:00:53.160 align:start position:0%
I'm going to go into the actual question
 

00:00:53.160 --> 00:00:55.590 align:start position:0%
I'm going to go into the actual question
over<00:00:53.440><c> here</c><00:00:54.359><c> within</c><00:00:54.640><c> the</c><00:00:54.920><c> question</c><00:00:55.359><c> I'm</c><00:00:55.480><c> just</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
over here within the question I'm just
 

00:00:55.600 --> 00:00:58.110 align:start position:0%
over here within the question I'm just
going<00:00:55.719><c> to</c><00:00:56.039><c> order</c><00:00:56.480><c> the</c><00:00:56.680><c> data</c><00:00:57.280><c> according</c><00:00:57.719><c> to</c><00:00:57.920><c> the</c>

00:00:58.110 --> 00:00:58.120 align:start position:0%
going to order the data according to the
 

00:00:58.120 --> 00:01:01.549 align:start position:0%
going to order the data according to the
sum<00:00:58.640><c> of</c><00:00:59.039><c> deposits</c><00:00:59.640><c> so</c><00:00:59.760><c> let's</c><00:01:00.000><c> let's</c><00:01:00.160><c> do</c><00:01:00.359><c> that</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
sum of deposits so let's let's do that
 

00:01:01.559 --> 00:01:05.149 align:start position:0%
sum of deposits so let's let's do that
now<00:01:02.559><c> uh</c><00:01:02.719><c> one</c><00:01:03.159><c> moment</c><00:01:04.159><c> looks</c><00:01:04.400><c> like</c><00:01:04.640><c> we</c><00:01:04.799><c> need</c><00:01:04.920><c> to</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
now uh one moment looks like we need to
 

00:01:05.159 --> 00:01:07.109 align:start position:0%
now uh one moment looks like we need to
wait<00:01:05.360><c> for</c><00:01:05.560><c> the</c><00:01:05.720><c> data</c><00:01:06.000><c> to</c>

00:01:07.109 --> 00:01:07.119 align:start position:0%
wait for the data to
 

00:01:07.119 --> 00:01:09.429 align:start position:0%
wait for the data to
load

00:01:09.429 --> 00:01:09.439 align:start position:0%
load
 

00:01:09.439 --> 00:01:13.390 align:start position:0%
load
okay<00:01:10.439><c> uh</c><00:01:10.600><c> one</c><00:01:10.960><c> second</c><00:01:11.880><c> I</c><00:01:12.000><c> go</c><00:01:12.159><c> into</c><00:01:12.479><c> settings</c>

00:01:13.390 --> 00:01:13.400 align:start position:0%
okay uh one second I go into settings
 

00:01:13.400 --> 00:01:16.109 align:start position:0%
okay uh one second I go into settings
just<00:01:13.720><c> checking</c><00:01:14.360><c> whether</c><00:01:15.360><c> it's</c><00:01:15.600><c> possible</c><00:01:16.000><c> that</c>

00:01:16.109 --> 00:01:16.119 align:start position:0%
just checking whether it's possible that
 

00:01:16.119 --> 00:01:17.990 align:start position:0%
just checking whether it's possible that
metabase<00:01:16.600><c> went</c><00:01:16.799><c> to</c><00:01:17.000><c> sleep</c><00:01:17.360><c> while</c><00:01:17.520><c> I</c><00:01:17.640><c> was</c><00:01:17.799><c> away</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
metabase went to sleep while I was away
 

00:01:18.000 --> 00:01:20.069 align:start position:0%
metabase went to sleep while I was away
from<00:01:18.159><c> the</c><00:01:18.400><c> computer</c><00:01:18.920><c> so</c><00:01:19.119><c> we</c><00:01:19.240><c> do</c><00:01:19.520><c> have</c><00:01:19.640><c> to</c><00:01:19.840><c> wait</c>

00:01:20.069 --> 00:01:20.079 align:start position:0%
from the computer so we do have to wait
 

00:01:20.079 --> 00:01:21.789 align:start position:0%
from the computer so we do have to wait
a

00:01:21.789 --> 00:01:21.799 align:start position:0%
a
 

00:01:21.799 --> 00:01:32.550 align:start position:0%
a
moment<00:01:22.799><c> one</c><00:01:23.200><c> sec</c>

00:01:32.550 --> 00:01:32.560 align:start position:0%
 
 

00:01:32.560 --> 00:01:34.630 align:start position:0%
 
meanwhile<00:01:33.000><c> we'll</c><00:01:33.200><c> also</c><00:01:33.520><c> open</c><00:01:33.799><c> up</c><00:01:34.159><c> some</c><00:01:34.320><c> of</c><00:01:34.479><c> the</c>

00:01:34.630 --> 00:01:34.640 align:start position:0%
meanwhile we'll also open up some of the
 

00:01:34.640 --> 00:01:36.030 align:start position:0%
meanwhile we'll also open up some of the
data<00:01:34.920><c> and</c><00:01:35.040><c> start</c><00:01:35.320><c> thinking</c><00:01:35.600><c> through</c><00:01:35.799><c> the</c><00:01:35.920><c> rest</c>

00:01:36.030 --> 00:01:36.040 align:start position:0%
data and start thinking through the rest
 

00:01:36.040 --> 00:01:38.350 align:start position:0%
data and start thinking through the rest
of<00:01:36.200><c> the</c><00:01:36.360><c> questions</c><00:01:37.240><c> please</c><00:01:37.520><c> identify</c><00:01:38.079><c> five</c>

00:01:38.350 --> 00:01:38.360 align:start position:0%
of the questions please identify five
 

00:01:38.360 --> 00:01:40.270 align:start position:0%
of the questions please identify five
traffic<00:01:38.799><c> sources</c><00:01:39.320><c> that</c><00:01:39.439><c> did</c><00:01:39.680><c> not</c><00:01:39.920><c> hit</c><00:01:40.119><c> the</c>

00:01:40.270 --> 00:01:40.280 align:start position:0%
traffic sources that did not hit the
 

00:01:40.280 --> 00:01:43.950 align:start position:0%
traffic sources that did not hit the
target<00:01:40.799><c> quality</c><00:01:41.799><c> okay</c><00:01:42.720><c> I</c><00:01:42.840><c> think</c><00:01:43.040><c> ordering</c><00:01:43.680><c> the</c>

00:01:43.950 --> 00:01:43.960 align:start position:0%
target quality okay I think ordering the
 

00:01:43.960 --> 00:01:46.749 align:start position:0%
target quality okay I think ordering the
sum<00:01:44.200><c> of</c><00:01:44.439><c> deposits</c><00:01:45.000><c> and</c><00:01:45.200><c> the</c><00:01:45.360><c> sum</c><00:01:45.640><c> of</c><00:01:46.600><c> uh</c>

00:01:46.749 --> 00:01:46.759 align:start position:0%
sum of deposits and the sum of uh
 

00:01:46.759 --> 00:01:49.749 align:start position:0%
sum of deposits and the sum of uh
registrations<00:01:47.759><c> will</c><00:01:48.719><c> will</c><00:01:48.920><c> help</c><00:01:49.119><c> us</c><00:01:49.280><c> in</c><00:01:49.520><c> this</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
registrations will will help us in this
 

00:01:49.759 --> 00:01:52.030 align:start position:0%
registrations will will help us in this
in<00:01:49.960><c> these</c><00:01:50.119><c> two</c><00:01:50.479><c> questions</c><00:01:51.439><c> and</c><00:01:51.719><c> question</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
in these two questions and question
 

00:01:52.040 --> 00:01:53.990 align:start position:0%
in these two questions and question
three<00:01:52.360><c> did</c><00:01:52.520><c> you</c><00:01:52.680><c> see</c><00:01:53.000><c> anything</c><00:01:53.479><c> interesting</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
three did you see anything interesting
 

00:01:54.000 --> 00:01:55.830 align:start position:0%
three did you see anything interesting
in<00:01:54.159><c> the</c><00:01:54.320><c> data</c><00:01:54.680><c> that</c><00:01:54.759><c> can</c><00:01:54.920><c> help</c><00:01:55.119><c> you</c><00:01:55.600><c> understand</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
in the data that can help you understand
 

00:01:55.840 --> 00:01:58.950 align:start position:0%
in the data that can help you understand
user<00:01:56.479><c> behavior</c><00:01:57.479><c> and</c><00:01:57.680><c> focus</c><00:01:58.039><c> your</c><00:01:58.360><c> campaign</c>

00:01:58.950 --> 00:01:58.960 align:start position:0%
user behavior and focus your campaign
 

00:01:58.960 --> 00:02:00.350 align:start position:0%
user behavior and focus your campaign
targeting<00:01:59.439><c> for</c><00:01:59.600><c> better</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
targeting for better
 

00:02:00.360 --> 00:02:02.789 align:start position:0%
targeting for better
performance

00:02:02.789 --> 00:02:02.799 align:start position:0%
performance
 

00:02:02.799 --> 00:02:05.109 align:start position:0%
performance
okay<00:02:03.799><c> we're</c><00:02:03.960><c> in</c><00:02:04.119><c> the</c>

00:02:05.109 --> 00:02:05.119 align:start position:0%
okay we're in the
 

00:02:05.119 --> 00:02:07.550 align:start position:0%
okay we're in the
activity<00:02:06.119><c> uh</c><00:02:06.240><c> metabase</c><00:02:06.719><c> has</c><00:02:06.920><c> this</c><00:02:07.119><c> activity</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
activity uh metabase has this activity
 

00:02:07.560 --> 00:02:09.430 align:start position:0%
activity uh metabase has this activity
table<00:02:07.960><c> which</c><00:02:08.080><c> is</c><00:02:08.280><c> very</c><00:02:08.520><c> helpful</c><00:02:09.039><c> which</c><00:02:09.160><c> shows</c>

00:02:09.430 --> 00:02:09.440 align:start position:0%
table which is very helpful which shows
 

00:02:09.440 --> 00:02:10.990 align:start position:0%
table which is very helpful which shows
me<00:02:09.599><c> all</c><00:02:09.800><c> the</c><00:02:10.000><c> activity</c><00:02:10.520><c> that</c><00:02:10.640><c> all</c><00:02:10.800><c> the</c>

00:02:10.990 --> 00:02:11.000 align:start position:0%
me all the activity that all the
 

00:02:11.000 --> 00:02:13.949 align:start position:0%
me all the activity that all the
different<00:02:11.920><c> metabase</c><00:02:12.480><c> users</c><00:02:12.920><c> have</c><00:02:13.120><c> done</c><00:02:13.720><c> okay</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
different metabase users have done okay
 

00:02:13.959 --> 00:02:16.229 align:start position:0%
different metabase users have done okay
cool<00:02:14.440><c> so</c><00:02:14.959><c> now</c><00:02:15.440><c> I'm</c><00:02:15.560><c> just</c><00:02:15.680><c> going</c><00:02:15.760><c> to</c><00:02:15.920><c> make</c><00:02:16.120><c> this</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
cool so now I'm just going to make this
 

00:02:16.239 --> 00:02:18.550 align:start position:0%
cool so now I'm just going to make this
a<00:02:16.360><c> little</c><00:02:16.560><c> bit</c><00:02:16.920><c> bigger</c><00:02:17.920><c> I'm</c><00:02:18.040><c> going</c><00:02:18.160><c> to</c><00:02:18.280><c> press</c>

00:02:18.550 --> 00:02:18.560 align:start position:0%
a little bit bigger I'm going to press
 

00:02:18.560 --> 00:02:21.270 align:start position:0%
a little bit bigger I'm going to press
open<00:02:19.000><c> editor</c><00:02:19.599><c> here</c><00:02:20.120><c> where</c><00:02:20.360><c> sum</c><00:02:20.720><c> is</c><00:02:20.920><c> greater</c>

00:02:21.270 --> 00:02:21.280 align:start position:0%
open editor here where sum is greater
 

00:02:21.280 --> 00:02:25.150 align:start position:0%
open editor here where sum is greater
than<00:02:21.920><c> zero</c><00:02:22.920><c> uh</c><00:02:23.120><c> there's</c><00:02:24.120><c> I'm</c><00:02:24.239><c> going</c><00:02:24.360><c> to</c><00:02:24.480><c> do</c><00:02:25.040><c> the</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
than zero uh there's I'm going to do the
 

00:02:25.160 --> 00:02:28.030 align:start position:0%
than zero uh there's I'm going to do the
SQL<00:02:25.599><c> command</c><00:02:26.080><c> order</c><00:02:26.640><c> by</c>

00:02:28.030 --> 00:02:28.040 align:start position:0%
SQL command order by
 

00:02:28.040 --> 00:02:31.869 align:start position:0%
SQL command order by
Sum<00:02:29.040><c> descending</c>

00:02:31.869 --> 00:02:31.879 align:start position:0%
 
 

00:02:31.879 --> 00:02:35.350 align:start position:0%
 
okay<00:02:32.879><c> okay</c><00:02:33.640><c> and</c><00:02:33.840><c> that's</c><00:02:34.040><c> going</c><00:02:34.200><c> to</c><00:02:35.200><c> that's</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
okay okay and that's going to that's
 

00:02:35.360 --> 00:02:37.869 align:start position:0%
okay okay and that's going to that's
going<00:02:35.480><c> to</c><00:02:35.599><c> order</c><00:02:35.959><c> all</c><00:02:36.120><c> the</c><00:02:36.280><c> data</c><00:02:36.680><c> Okay</c><00:02:36.879><c> cool</c><00:02:37.200><c> so</c>

00:02:37.869 --> 00:02:37.879 align:start position:0%
going to order all the data Okay cool so
 

00:02:37.879 --> 00:02:40.790 align:start position:0%
going to order all the data Okay cool so
best<00:02:38.120><c> performance</c><00:02:38.800><c> was</c><00:02:39.040><c> by</c><00:02:39.280><c> this</c><00:02:39.800><c> publisher</c>

00:02:40.790 --> 00:02:40.800 align:start position:0%
best performance was by this publisher
 

00:02:40.800 --> 00:02:43.830 align:start position:0%
best performance was by this publisher
they<00:02:40.920><c> want</c><00:02:41.200><c> the</c><00:02:41.400><c> top</c><00:02:41.720><c> five</c><00:02:42.480><c> uh</c><00:02:42.680><c> subish</c><00:02:43.480><c> sub</c>

00:02:43.830 --> 00:02:43.840 align:start position:0%
they want the top five uh subish sub
 

00:02:43.840 --> 00:02:45.630 align:start position:0%
they want the top five uh subish sub
Publishers<00:02:44.400><c> with</c><00:02:44.560><c> the</c><00:02:44.760><c> best</c><00:02:45.000><c> performance</c><00:02:45.519><c> so</c>

00:02:45.630 --> 00:02:45.640 align:start position:0%
Publishers with the best performance so
 

00:02:45.640 --> 00:02:48.470 align:start position:0%
Publishers with the best performance so
I'm<00:02:45.760><c> going</c><00:02:45.840><c> to</c><00:02:46.080><c> go</c><00:02:47.080><c> I</c><00:02:47.400><c> I</c><00:02:47.480><c> want</c><00:02:47.640><c> to</c><00:02:47.840><c> just</c><00:02:48.080><c> copy</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
I'm going to go I I want to just copy
 

00:02:48.480 --> 00:02:50.630 align:start position:0%
I'm going to go I I want to just copy
paste<00:02:48.720><c> that</c><00:02:48.840><c> into</c><00:02:49.080><c> my</c><00:02:49.280><c> Google</c><00:02:49.640><c> doc</c><00:02:50.400><c> I'm</c><00:02:50.519><c> going</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
paste that into my Google doc I'm going
 

00:02:50.640 --> 00:02:55.149 align:start position:0%
paste that into my Google doc I'm going
to<00:02:50.800><c> go</c><00:02:50.959><c> ahead</c><00:02:51.159><c> and</c><00:02:51.519><c> press</c><00:02:52.519><c> um</c><00:02:53.200><c> let's</c><00:02:53.519><c> see</c><00:02:54.519><c> data</c>

00:02:55.149 --> 00:02:55.159 align:start position:0%
to go ahead and press um let's see data
 

00:02:55.159 --> 00:02:57.869 align:start position:0%
to go ahead and press um let's see data
how<00:02:55.239><c> do</c><00:02:55.400><c> I</c><00:02:55.480><c> want</c><00:02:55.599><c> to</c><00:02:55.879><c> display</c><00:02:56.400><c> it</c><00:02:57.400><c> um</c><00:02:57.680><c> I</c><00:02:57.760><c> don't</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
how do I want to display it um I don't
 

00:02:57.879 --> 00:02:59.670 align:start position:0%
how do I want to display it um I don't
want<00:02:58.000><c> to</c><00:02:58.120><c> display</c><00:02:58.440><c> it</c><00:02:58.560><c> as</c><00:02:58.640><c> a</c><00:02:58.760><c> bar</c><00:02:59.040><c> chart</c><00:02:59.400><c> I</c><00:02:59.599><c> I</c>

00:02:59.670 --> 00:02:59.680 align:start position:0%
want to display it as a bar chart I I
 

00:02:59.680 --> 00:03:02.270 align:start position:0%
want to display it as a bar chart I I
just<00:02:59.840><c> just</c><00:02:59.959><c> wanted</c><00:03:00.280><c> as</c><00:03:00.400><c> a</c><00:03:00.879><c> table</c><00:03:01.879><c> that</c><00:03:02.000><c> way</c><00:03:02.159><c> I</c>

00:03:02.270 --> 00:03:02.280 align:start position:0%
just just wanted as a table that way I
 

00:03:02.280 --> 00:03:05.390 align:start position:0%
just just wanted as a table that way I
can<00:03:02.480><c> copy</c><00:03:02.879><c> paste</c><00:03:03.400><c> all</c><00:03:03.560><c> right</c><00:03:03.720><c> so</c><00:03:04.040><c> this</c><00:03:04.239><c> table</c><00:03:05.239><c> I</c>

00:03:05.390 --> 00:03:05.400 align:start position:0%
can copy paste all right so this table I
 

00:03:05.400 --> 00:03:08.190 align:start position:0%
can copy paste all right so this table I
already<00:03:05.720><c> have</c><00:03:06.000><c> 71</c><00:03:06.680><c> deposits</c><00:03:07.319><c> this</c><00:03:07.519><c> sub</c>

00:03:08.190 --> 00:03:08.200 align:start position:0%
already have 71 deposits this sub
 

00:03:08.200 --> 00:03:10.949 align:start position:0%
already have 71 deposits this sub
publisher<00:03:09.200><c> 21</c>

00:03:10.949 --> 00:03:10.959 align:start position:0%
publisher 21
 

00:03:10.959 --> 00:03:15.229 align:start position:0%
publisher 21
deposits<00:03:12.040><c> okay</c><00:03:13.040><c> this</c>

00:03:15.229 --> 00:03:15.239 align:start position:0%
deposits okay this
 

00:03:15.239 --> 00:03:20.509 align:start position:0%
deposits okay this
guy<00:03:16.239><c> 21</c>

00:03:20.509 --> 00:03:20.519 align:start position:0%
 
 

00:03:20.519 --> 00:03:22.710 align:start position:0%
 
deposits<00:03:21.519><c> all</c><00:03:21.680><c> right</c><00:03:21.840><c> so</c><00:03:22.080><c> as</c><00:03:22.159><c> you</c><00:03:22.280><c> can</c><00:03:22.440><c> see</c>

00:03:22.710 --> 00:03:22.720 align:start position:0%
deposits all right so as you can see
 

00:03:22.720 --> 00:03:24.430 align:start position:0%
deposits all right so as you can see
once<00:03:22.920><c> the</c><00:03:23.080><c> data</c><00:03:23.319><c> is</c><00:03:23.400><c> in</c><00:03:23.560><c> metabase</c><00:03:24.120><c> it's</c><00:03:24.239><c> so</c>

00:03:24.430 --> 00:03:24.440 align:start position:0%
once the data is in metabase it's so
 

00:03:24.440 --> 00:03:27.110 align:start position:0%
once the data is in metabase it's so
much<00:03:24.599><c> easier</c><00:03:24.959><c> to</c><00:03:25.159><c> answer</c><00:03:25.519><c> these</c><00:03:26.120><c> questions</c>

00:03:27.110 --> 00:03:27.120 align:start position:0%
much easier to answer these questions
 

00:03:27.120 --> 00:03:29.509 align:start position:0%
much easier to answer these questions
all<00:03:27.239><c> right</c><00:03:27.440><c> let</c><00:03:27.560><c> me</c><00:03:27.680><c> bring</c><00:03:27.920><c> this</c><00:03:28.040><c> to</c><00:03:28.200><c> the</c><00:03:28.519><c> left</c>

00:03:29.509 --> 00:03:29.519 align:start position:0%
all right let me bring this to the left
 

00:03:29.519 --> 00:03:32.910 align:start position:0%
all right let me bring this to the left
this<00:03:29.799><c> sub</c><00:03:30.159><c> publisher</c><00:03:30.840><c> 14</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
this sub publisher 14
 

00:03:32.920 --> 00:03:37.229 align:start position:0%
this sub publisher 14
deposits

00:03:37.229 --> 00:03:37.239 align:start position:0%
 
 

00:03:37.239 --> 00:03:42.190 align:start position:0%
 
okay<00:03:38.239><c> okay</c><00:03:38.959><c> this</c><00:03:39.239><c> guy</c><00:03:39.680><c> 13</c><00:03:40.680><c> I</c><00:03:41.040><c> and</c>

00:03:42.190 --> 00:03:42.200 align:start position:0%
okay okay this guy 13 I and
 

00:03:42.200 --> 00:03:46.710 align:start position:0%
okay okay this guy 13 I and
just<00:03:43.680><c> okay</c><00:03:44.680><c> 13</c>

00:03:46.710 --> 00:03:46.720 align:start position:0%
just okay 13
 

00:03:46.720 --> 00:03:54.470 align:start position:0%
just okay 13
deposits<00:03:47.799><c> and</c><00:03:48.799><c> this</c><00:03:49.040><c> guy</c><00:03:49.280><c> over</c><00:03:49.879><c> here</c><00:03:50.879><c> with</c><00:03:51.120><c> 10</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
 
 

00:03:54.480 --> 00:03:58.390 align:start position:0%
 
deposits<00:03:55.840><c> okay</c><00:03:56.840><c> 10</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
deposits okay 10
 

00:03:58.400 --> 00:04:00.589 align:start position:0%
deposits okay 10
deposits<00:03:59.400><c> all</c><00:03:59.560><c> right</c><00:03:59.840><c> right</c><00:04:00.040><c> perfect</c><00:04:00.360><c> so</c>

00:04:00.589 --> 00:04:00.599 align:start position:0%
deposits all right right perfect so
 

00:04:00.599 --> 00:04:03.390 align:start position:0%
deposits all right right perfect so
everything<00:04:00.879><c> is</c><00:04:01.040><c> all</c><00:04:01.360><c> good</c><00:04:02.000><c> here</c><00:04:03.000><c> uh</c><00:04:03.120><c> let</c><00:04:03.239><c> me</c>

00:04:03.390 --> 00:04:03.400 align:start position:0%
everything is all good here uh let me
 

00:04:03.400 --> 00:04:07.270 align:start position:0%
everything is all good here uh let me
make<00:04:03.560><c> this</c><00:04:03.680><c> a</c><00:04:03.799><c> little</c><00:04:03.959><c> bit</c><00:04:04.120><c> bigger</c><00:04:04.519><c> remove</c><00:04:04.879><c> the</c>

00:04:07.270 --> 00:04:07.280 align:start position:0%
 
 

00:04:07.280 --> 00:04:08.830 align:start position:0%
 
underline

00:04:08.830 --> 00:04:08.840 align:start position:0%
underline
 

00:04:08.840 --> 00:04:11.990 align:start position:0%
underline
okay<00:04:09.840><c> uh</c><00:04:10.079><c> cool</c><00:04:10.599><c> so</c><00:04:10.879><c> that's</c><00:04:11.239><c> that</c><00:04:11.560><c> that</c><00:04:11.680><c> takes</c>

00:04:11.990 --> 00:04:12.000 align:start position:0%
okay uh cool so that's that that takes
 

00:04:12.000 --> 00:04:14.350 align:start position:0%
okay uh cool so that's that that takes
care<00:04:12.159><c> of</c><00:04:12.439><c> that</c><00:04:13.360><c> uh</c><00:04:13.519><c> question</c><00:04:13.799><c> two</c><00:04:14.079><c> please</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
care of that uh question two please
 

00:04:14.360 --> 00:04:16.509 align:start position:0%
care of that uh question two please
identify<00:04:14.959><c> five</c><00:04:15.239><c> traffic</c><00:04:15.760><c> sources</c><00:04:16.160><c> that</c><00:04:16.280><c> did</c>

00:04:16.509 --> 00:04:16.519 align:start position:0%
identify five traffic sources that did
 

00:04:16.519 --> 00:04:18.270 align:start position:0%
identify five traffic sources that did
not<00:04:16.840><c> hit</c><00:04:17.079><c> the</c><00:04:17.199><c> target</c>

00:04:18.270 --> 00:04:18.280 align:start position:0%
not hit the target
 

00:04:18.280 --> 00:04:20.710 align:start position:0%
not hit the target
quality<00:04:19.280><c> okay</c><00:04:19.600><c> just</c><00:04:19.720><c> to</c><00:04:19.959><c> clarify</c><00:04:20.400><c> that</c><00:04:20.600><c> this</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
quality okay just to clarify that this
 

00:04:20.720 --> 00:04:24.030 align:start position:0%
quality okay just to clarify that this
is<00:04:20.959><c> the</c><00:04:21.400><c> answer</c><00:04:22.400><c> going</c><00:04:22.520><c> to</c><00:04:22.680><c> turn</c><00:04:22.960><c> this</c><00:04:23.120><c> into</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
is the answer going to turn this into
 

00:04:24.040 --> 00:04:27.469 align:start position:0%
is the answer going to turn this into
red<00:04:25.040><c> okay</c><00:04:25.840><c> five</c><00:04:26.400><c> Publishers</c><00:04:26.919><c> that</c><00:04:27.040><c> did</c><00:04:27.240><c> not</c>

00:04:27.469 --> 00:04:27.479 align:start position:0%
red okay five Publishers that did not
 

00:04:27.479 --> 00:04:30.070 align:start position:0%
red okay five Publishers that did not
hit<00:04:27.639><c> the</c><00:04:27.800><c> target</c><00:04:28.320><c> quality</c><00:04:29.320><c> uh</c><00:04:29.440><c> that's</c><00:04:29.840><c> really</c>

00:04:30.070 --> 00:04:30.080 align:start position:0%
hit the target quality uh that's really
 

00:04:30.080 --> 00:04:32.909 align:start position:0%
hit the target quality uh that's really
easy<00:04:30.400><c> all</c><00:04:30.520><c> we</c><00:04:30.639><c> need</c><00:04:30.800><c> to</c><00:04:30.960><c> do</c><00:04:31.160><c> is</c><00:04:31.440><c> just</c><00:04:32.000><c> uh</c><00:04:32.120><c> do</c><00:04:32.400><c> sum</c>

00:04:32.909 --> 00:04:32.919 align:start position:0%
easy all we need to do is just uh do sum
 

00:04:32.919 --> 00:04:36.029 align:start position:0%
easy all we need to do is just uh do sum
equals

00:04:36.029 --> 00:04:36.039 align:start position:0%
 
 

00:04:36.039 --> 00:04:38.749 align:start position:0%
 
z<00:04:37.039><c> um</c><00:04:37.199><c> so</c><00:04:37.400><c> find</c><00:04:37.680><c> me</c><00:04:37.800><c> the</c><00:04:37.880><c> subp</c><00:04:38.199><c> Publishers</c>

00:04:38.749 --> 00:04:38.759 align:start position:0%
z um so find me the subp Publishers
 

00:04:38.759 --> 00:04:41.550 align:start position:0%
z um so find me the subp Publishers
where<00:04:38.960><c> the</c><00:04:39.080><c> sum</c><00:04:39.320><c> of</c><00:04:39.520><c> deposits</c><00:04:40.039><c> was</c><00:04:40.400><c> zero</c><00:04:41.400><c> and</c>

00:04:41.550 --> 00:04:41.560 align:start position:0%
where the sum of deposits was zero and
 

00:04:41.560 --> 00:04:43.830 align:start position:0%
where the sum of deposits was zero and
we<00:04:41.680><c> can</c><00:04:41.880><c> just</c><00:04:42.120><c> again</c><00:04:42.479><c> copy</c><00:04:42.880><c> paste</c><00:04:43.240><c> these</c><00:04:43.520><c> five</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
we can just again copy paste these five
 

00:04:43.840 --> 00:04:46.629 align:start position:0%
we can just again copy paste these five
over<00:04:44.440><c> here</c><00:04:45.440><c> I'll</c><00:04:45.680><c> paste</c><00:04:45.919><c> it</c><00:04:46.039><c> into</c><00:04:46.320><c> there</c><00:04:46.479><c> to</c>

00:04:46.629 --> 00:04:46.639 align:start position:0%
over here I'll paste it into there to
 

00:04:46.639 --> 00:04:49.230 align:start position:0%
over here I'll paste it into there to
remove<00:04:46.960><c> the</c>

00:04:49.230 --> 00:04:49.240 align:start position:0%
 
 

00:04:49.240 --> 00:04:51.430 align:start position:0%
 
formatting<00:04:50.240><c> uh</c><00:04:50.680><c> oh</c>

00:04:51.430 --> 00:04:51.440 align:start position:0%
formatting uh oh
 

00:04:51.440 --> 00:04:55.790 align:start position:0%
formatting uh oh
schnikes<00:04:52.440><c> so</c><00:04:52.759><c> guys</c><00:04:52.960><c> contrl</c><00:04:53.440><c> C</c><00:04:54.440><c> and</c><00:04:54.759><c> contrl</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
schnikes so guys contrl C and contrl
 

00:04:55.800 --> 00:05:05.629 align:start position:0%
schnikes so guys contrl C and contrl
v<00:04:56.800><c> h</c><00:04:57.720><c> okay</c>

00:05:05.629 --> 00:05:05.639 align:start position:0%
 
 

00:05:05.639 --> 00:05:08.590 align:start position:0%
 
okay<00:05:05.960><c> great</c><00:05:06.840><c> cool</c><00:05:07.840><c> I'm</c><00:05:07.919><c> going</c><00:05:08.039><c> to</c><00:05:08.199><c> make</c><00:05:08.440><c> that</c>

00:05:08.590 --> 00:05:08.600 align:start position:0%
okay great cool I'm going to make that
 

00:05:08.600 --> 00:05:11.270 align:start position:0%
okay great cool I'm going to make that
all<00:05:08.919><c> red</c><00:05:09.320><c> again</c><00:05:09.800><c> text</c><00:05:10.199><c> color</c><00:05:10.520><c> is</c><00:05:10.639><c> going</c><00:05:10.759><c> to</c><00:05:10.880><c> be</c>

00:05:11.270 --> 00:05:11.280 align:start position:0%
all red again text color is going to be
 

00:05:11.280 --> 00:05:14.310 align:start position:0%
all red again text color is going to be
red<00:05:12.280><c> I'm</c><00:05:12.520><c> going</c><00:05:12.639><c> to</c><00:05:12.840><c> bring</c><00:05:13.039><c> it</c>

00:05:14.310 --> 00:05:14.320 align:start position:0%
red I'm going to bring it
 

00:05:14.320 --> 00:05:17.870 align:start position:0%
red I'm going to bring it
there<00:05:15.320><c> okay</c><00:05:16.240><c> these</c><00:05:16.479><c> five</c><00:05:16.720><c> sub</c><00:05:17.080><c> Publishers</c><00:05:17.639><c> had</c>

00:05:17.870 --> 00:05:17.880 align:start position:0%
there okay these five sub Publishers had
 

00:05:17.880 --> 00:05:19.629 align:start position:0%
there okay these five sub Publishers had
zero

00:05:19.629 --> 00:05:19.639 align:start position:0%
zero
 

00:05:19.639 --> 00:05:22.469 align:start position:0%
zero
deposits<00:05:20.639><c> all</c>

00:05:22.469 --> 00:05:22.479 align:start position:0%
deposits all
 

00:05:22.479 --> 00:05:25.670 align:start position:0%
deposits all
right<00:05:23.600><c> ah</c><00:05:24.600><c> let's</c>

00:05:25.670 --> 00:05:25.680 align:start position:0%
right ah let's
 

00:05:25.680 --> 00:05:28.469 align:start position:0%
right ah let's
see<00:05:26.680><c> we</c><00:05:26.800><c> could</c><00:05:27.080><c> go</c><00:05:27.280><c> a</c><00:05:27.400><c> little</c><00:05:27.600><c> bit</c><00:05:27.800><c> deeper</c><00:05:28.160><c> into</c>

00:05:28.469 --> 00:05:28.479 align:start position:0%
see we could go a little bit deeper into
 

00:05:28.479 --> 00:05:30.390 align:start position:0%
see we could go a little bit deeper into
that<00:05:28.800><c> and</c><00:05:29.080><c> maybe</c><00:05:29.280><c> we'll</c><00:05:29.400><c> do</c><00:05:29.759><c> that</c><00:05:30.080><c> a</c><00:05:30.199><c> little</c>

00:05:30.390 --> 00:05:30.400 align:start position:0%
that and maybe we'll do that a little
 

00:05:30.400 --> 00:05:32.150 align:start position:0%
that and maybe we'll do that a little
bit<00:05:30.840><c> a</c><00:05:30.960><c> little</c><00:05:31.160><c> bit</c>

00:05:32.150 --> 00:05:32.160 align:start position:0%
bit a little bit
 

00:05:32.160 --> 00:05:38.070 align:start position:0%
bit a little bit
later<00:05:33.160><c> uh</c><00:05:33.319><c> zero</c><00:05:33.800><c> deposits</c><00:05:34.479><c> contrl</c><00:05:34.880><c> z</c><00:05:35.400><c> contrl</c>

00:05:38.070 --> 00:05:38.080 align:start position:0%
 
 

00:05:38.080 --> 00:05:40.909 align:start position:0%
 
c<00:05:39.080><c> we</c><00:05:39.199><c> could</c><00:05:39.440><c> count</c><00:05:39.800><c> up</c><00:05:40.039><c> up</c><00:05:40.240><c> the</c><00:05:40.400><c> amount</c><00:05:40.639><c> of</c>

00:05:40.909 --> 00:05:40.919 align:start position:0%
c we could count up up the amount of
 

00:05:40.919 --> 00:05:43.909 align:start position:0%
c we could count up up the amount of
clicks<00:05:41.479><c> also</c><00:05:41.919><c> per</c><00:05:42.160><c> sub</c><00:05:42.639><c> publisher</c><00:05:43.639><c> and</c><00:05:43.759><c> then</c>

00:05:43.909 --> 00:05:43.919 align:start position:0%
clicks also per sub publisher and then
 

00:05:43.919 --> 00:05:46.550 align:start position:0%
clicks also per sub publisher and then
count<00:05:44.160><c> the</c><00:05:44.280><c> amount</c><00:05:44.479><c> of</c><00:05:44.960><c> deposits</c><00:05:45.960><c> uh</c><00:05:46.199><c> let's</c>

00:05:46.550 --> 00:05:46.560 align:start position:0%
count the amount of deposits uh let's
 

00:05:46.560 --> 00:05:51.270 align:start position:0%
count the amount of deposits uh let's
see<00:05:47.560><c> can</c><00:05:47.680><c> we</c><00:05:47.840><c> do</c><00:05:48.000><c> that</c><00:05:48.400><c> here</c><00:05:49.400><c> where</c>

00:05:51.270 --> 00:05:51.280 align:start position:0%
see can we do that here where
 

00:05:51.280 --> 00:05:53.189 align:start position:0%
see can we do that here where
sum

00:05:53.189 --> 00:05:53.199 align:start position:0%
sum
 

00:05:53.199 --> 00:05:57.590 align:start position:0%
sum
count<00:05:54.600><c> uh</c><00:05:55.600><c> maybe</c><00:05:55.840><c> we</c><00:05:55.919><c> could</c><00:05:56.080><c> do</c><00:05:56.280><c> select</c>

00:05:57.590 --> 00:05:57.600 align:start position:0%
count uh maybe we could do select
 

00:05:57.600 --> 00:06:00.670 align:start position:0%
count uh maybe we could do select
all<00:05:58.600><c> select</c><00:05:59.080><c> count</c><00:05:59.360><c> of</c>

00:06:00.670 --> 00:06:00.680 align:start position:0%
all select count of
 

00:06:00.680 --> 00:06:03.950 align:start position:0%
all select count of
all<00:06:01.680><c> count</c><00:06:02.280><c> count</c><00:06:02.600><c> the</c><00:06:02.759><c> amount</c><00:06:02.960><c> of</c><00:06:03.240><c> click</c><00:06:03.840><c> the</c>

00:06:03.950 --> 00:06:03.960 align:start position:0%
all count count the amount of click the
 

00:06:03.960 --> 00:06:05.430 align:start position:0%
all count count the amount of click the
amount<00:06:04.160><c> of</c><00:06:04.319><c> records</c><00:06:04.759><c> from</c><00:06:04.960><c> that</c><00:06:05.120><c> sub</c>

00:06:05.430 --> 00:06:05.440 align:start position:0%
amount of records from that sub
 

00:06:05.440 --> 00:06:07.710 align:start position:0%
amount of records from that sub
publisher<00:06:05.840><c> and</c><00:06:06.039><c> also</c><00:06:06.319><c> count</c><00:06:06.560><c> the</c><00:06:06.680><c> amount</c><00:06:06.880><c> of</c>

00:06:07.710 --> 00:06:07.720 align:start position:0%
publisher and also count the amount of
 

00:06:07.720 --> 00:06:09.749 align:start position:0%
publisher and also count the amount of
deposits<00:06:08.720><c> uh</c><00:06:08.840><c> let's</c><00:06:09.000><c> see</c><00:06:09.160><c> if</c><00:06:09.280><c> that</c><00:06:09.400><c> gives</c><00:06:09.599><c> us</c>

00:06:09.749 --> 00:06:09.759 align:start position:0%
deposits uh let's see if that gives us
 

00:06:09.759 --> 00:06:11.909 align:start position:0%
deposits uh let's see if that gives us
any<00:06:09.960><c> interesting</c><00:06:10.599><c> information</c><00:06:11.319><c> there</c><00:06:11.720><c> the</c>

00:06:11.909 --> 00:06:11.919 align:start position:0%
any interesting information there the
 

00:06:11.919 --> 00:06:18.110 align:start position:0%
any interesting information there the
count<00:06:12.240><c> of</c><00:06:13.039><c> count</c><00:06:13.919><c> as</c><00:06:14.919><c> um</c><00:06:15.319><c> let's</c><00:06:15.759><c> see</c><00:06:16.759><c> as</c><00:06:17.440><c> number</c>

00:06:18.110 --> 00:06:18.120 align:start position:0%
count of count as um let's see as number
 

00:06:18.120 --> 00:06:21.230 align:start position:0%
count of count as um let's see as number
of<00:06:18.680><c> clicks</c><00:06:19.520><c> okay</c><00:06:19.759><c> cool</c><00:06:20.599><c> let's</c><00:06:20.840><c> count</c><00:06:21.080><c> the</c>

00:06:21.230 --> 00:06:21.240 align:start position:0%
of clicks okay cool let's count the
 

00:06:21.240 --> 00:06:23.749 align:start position:0%
of clicks okay cool let's count the
amount<00:06:21.560><c> the</c><00:06:21.759><c> number</c><00:06:22.039><c> of</c><00:06:22.280><c> clicks</c><00:06:22.639><c> per</c><00:06:22.840><c> subp</c>

00:06:23.749 --> 00:06:23.759 align:start position:0%
amount the number of clicks per subp
 

00:06:23.759 --> 00:06:25.950 align:start position:0%
amount the number of clicks per subp
publisher<00:06:24.759><c> and</c><00:06:24.960><c> then</c><00:06:25.240><c> I'm</c><00:06:25.599><c> instead</c><00:06:25.840><c> of</c>

00:06:25.950 --> 00:06:25.960 align:start position:0%
publisher and then I'm instead of
 

00:06:25.960 --> 00:06:27.909 align:start position:0%
publisher and then I'm instead of
ordering<00:06:26.360><c> it</c><00:06:26.599><c> by</c><00:06:26.800><c> the</c><00:06:27.000><c> sum</c><00:06:27.520><c> I'm</c><00:06:27.639><c> going</c><00:06:27.759><c> to</c>

00:06:27.909 --> 00:06:27.919 align:start position:0%
ordering it by the sum I'm going to
 

00:06:27.919 --> 00:06:30.110 align:start position:0%
ordering it by the sum I'm going to
order<00:06:28.360><c> this</c><00:06:28.840><c> by</c>

00:06:30.110 --> 00:06:30.120 align:start position:0%
order this by
 

00:06:30.120 --> 00:06:35.350 align:start position:0%
order this by
um<00:06:31.039><c> by</c><00:06:31.199><c> the</c><00:06:31.400><c> number</c><00:06:31.840><c> of</c><00:06:32.240><c> clicks</c><00:06:33.160><c> order</c><00:06:33.800><c> by</c><00:06:34.800><c> num</c>

00:06:35.350 --> 00:06:35.360 align:start position:0%
um by the number of clicks order by num
 

00:06:35.360 --> 00:06:38.390 align:start position:0%
um by the number of clicks order by num
of<00:06:35.599><c> clicks</c><00:06:35.960><c> metabase</c><00:06:36.520><c> already</c><00:06:36.840><c> gives</c><00:06:37.039><c> it</c><00:06:37.919><c> uh</c>

00:06:38.390 --> 00:06:38.400 align:start position:0%
of clicks metabase already gives it uh
 

00:06:38.400 --> 00:06:42.230 align:start position:0%
of clicks metabase already gives it uh
descending<00:06:39.360><c> okay</c><00:06:40.080><c> I</c><00:06:40.199><c> think</c><00:06:40.680><c> that</c><00:06:41.680><c> this</c><00:06:41.800><c> is</c><00:06:42.039><c> the</c>

00:06:42.230 --> 00:06:42.240 align:start position:0%
descending okay I think that this is the
 

00:06:42.240 --> 00:06:46.510 align:start position:0%
descending okay I think that this is the
easiest<00:06:42.840><c> way</c><00:06:43.520><c> uh</c><00:06:43.720><c> this</c><00:06:44.000><c> is</c><00:06:45.000><c> uh</c><00:06:45.240><c> right</c><00:06:45.560><c> because</c>

00:06:46.510 --> 00:06:46.520 align:start position:0%
easiest way uh this is uh right because
 

00:06:46.520 --> 00:06:48.749 align:start position:0%
easiest way uh this is uh right because
um<00:06:47.360><c> the</c><00:06:47.560><c> target</c>

00:06:48.749 --> 00:06:48.759 align:start position:0%
um the target
 

00:06:48.759 --> 00:06:51.070 align:start position:0%
um the target
quality<00:06:49.759><c> I</c><00:06:49.840><c> think</c><00:06:50.039><c> this</c><00:06:50.160><c> is</c><00:06:50.280><c> a</c><00:06:50.520><c> better</c><00:06:50.840><c> a</c>

00:06:51.070 --> 00:06:51.080 align:start position:0%
quality I think this is a better a
 

00:06:51.080 --> 00:06:53.469 align:start position:0%
quality I think this is a better a
better<00:06:51.919><c> five</c>

00:06:53.469 --> 00:06:53.479 align:start position:0%
better five
 

00:06:53.479 --> 00:06:56.629 align:start position:0%
better five
um<00:06:54.479><c> because</c><00:06:54.880><c> this</c><00:06:55.319><c> this</c><00:06:55.680><c> uh</c><00:06:55.840><c> this</c><00:06:56.000><c> query</c><00:06:56.440><c> over</c>

00:06:56.629 --> 00:06:56.639 align:start position:0%
um because this this uh this query over
 

00:06:56.639 --> 00:06:58.270 align:start position:0%
um because this this uh this query over
here<00:06:56.800><c> and</c><00:06:56.960><c> I'm</c><00:06:57.120><c> actually</c><00:06:57.319><c> going</c><00:06:57.440><c> to</c><00:06:57.599><c> save</c><00:06:58.000><c> this</c>

00:06:58.270 --> 00:06:58.280 align:start position:0%
here and I'm actually going to save this
 

00:06:58.280 --> 00:07:00.270 align:start position:0%
here and I'm actually going to save this
as<00:06:58.720><c> uh</c><00:06:59.080><c> question</c>

00:07:00.270 --> 00:07:00.280 align:start position:0%
as uh question
 

00:07:00.280 --> 00:07:03.629 align:start position:0%
as uh question
to<00:07:01.280><c> uh</c><00:07:01.440><c> and</c><00:07:01.639><c> now</c><00:07:02.240><c> right</c><00:07:02.440><c> I've</c><00:07:02.599><c> taken</c><00:07:02.840><c> this</c><00:07:03.039><c> SQL</c>

00:07:03.629 --> 00:07:03.639 align:start position:0%
to uh and now right I've taken this SQL
 

00:07:03.639 --> 00:07:05.510 align:start position:0%
to uh and now right I've taken this SQL
I've<00:07:03.800><c> edited</c><00:07:04.240><c> a</c><00:07:04.400><c> little</c><00:07:04.599><c> bit</c><00:07:04.919><c> and</c><00:07:05.160><c> it's</c><00:07:05.319><c> given</c>

00:07:05.510 --> 00:07:05.520 align:start position:0%
I've edited a little bit and it's given
 

00:07:05.520 --> 00:07:07.390 align:start position:0%
I've edited a little bit and it's given
me<00:07:05.680><c> an</c><00:07:05.840><c> answer</c><00:07:06.120><c> to</c><00:07:06.319><c> question</c><00:07:06.599><c> number</c><00:07:06.879><c> two</c><00:07:07.199><c> so</c><00:07:07.319><c> I</c>

00:07:07.390 --> 00:07:07.400 align:start position:0%
me an answer to question number two so I
 

00:07:07.400 --> 00:07:09.869 align:start position:0%
me an answer to question number two so I
want<00:07:07.560><c> to</c><00:07:07.800><c> save</c><00:07:08.680><c> this</c><00:07:08.879><c> format</c><00:07:09.240><c> of</c><00:07:09.360><c> the</c><00:07:09.479><c> SQL</c>

00:07:09.869 --> 00:07:09.879 align:start position:0%
want to save this format of the SQL
 

00:07:09.879 --> 00:07:11.990 align:start position:0%
want to save this format of the SQL
query<00:07:10.319><c> as</c><00:07:10.560><c> question</c><00:07:11.000><c> two</c><00:07:11.400><c> so</c><00:07:11.520><c> I'm</c><00:07:11.639><c> going</c><00:07:11.720><c> to</c><00:07:11.879><c> go</c>

00:07:11.990 --> 00:07:12.000 align:start position:0%
query as question two so I'm going to go
 

00:07:12.000 --> 00:07:14.309 align:start position:0%
query as question two so I'm going to go
ahead<00:07:12.199><c> and</c><00:07:12.440><c> press</c><00:07:12.759><c> save</c><00:07:13.240><c> here</c><00:07:13.960><c> and</c><00:07:14.080><c> I'm</c><00:07:14.240><c> going</c>

00:07:14.309 --> 00:07:14.319 align:start position:0%
ahead and press save here and I'm going
 

00:07:14.319 --> 00:07:16.309 align:start position:0%
ahead and press save here and I'm going
to<00:07:14.800><c> do</c><00:07:14.919><c> I</c><00:07:15.000><c> want</c><00:07:15.120><c> to</c><00:07:15.280><c> replace</c><00:07:15.720><c> the</c><00:07:15.840><c> original</c>

00:07:16.309 --> 00:07:16.319 align:start position:0%
to do I want to replace the original
 

00:07:16.319 --> 00:07:18.710 align:start position:0%
to do I want to replace the original
question<00:07:16.960><c> no</c><00:07:17.479><c> the</c><00:07:17.639><c> original</c><00:07:18.120><c> question</c><00:07:18.440><c> has</c>

00:07:18.710 --> 00:07:18.720 align:start position:0%
question no the original question has
 

00:07:18.720 --> 00:07:20.430 align:start position:0%
question no the original question has
the<00:07:18.879><c> answer</c><00:07:19.120><c> to</c><00:07:19.319><c> question</c><00:07:19.639><c> one</c><00:07:19.919><c> so</c><00:07:20.039><c> I</c><00:07:20.120><c> want</c><00:07:20.240><c> to</c>

00:07:20.430 --> 00:07:20.440 align:start position:0%
the answer to question one so I want to
 

00:07:20.440 --> 00:07:23.350 align:start position:0%
the answer to question one so I want to
keep<00:07:20.639><c> it</c><00:07:20.840><c> so</c><00:07:21.080><c> I</c><00:07:21.240><c> but</c><00:07:21.360><c> I</c><00:07:21.440><c> do</c><00:07:21.599><c> want</c><00:07:21.680><c> to</c><00:07:22.160><c> create</c><00:07:23.160><c> uh</c>

00:07:23.350 --> 00:07:23.360 align:start position:0%
keep it so I but I do want to create uh
 

00:07:23.360 --> 00:07:25.070 align:start position:0%
keep it so I but I do want to create uh
an<00:07:23.599><c> insight</c><00:07:23.960><c> for</c><00:07:24.160><c> question</c><00:07:24.479><c> two</c><00:07:24.800><c> what</c><00:07:24.879><c> am</c><00:07:25.000><c> I</c>

00:07:25.070 --> 00:07:25.080 align:start position:0%
an insight for question two what am I
 

00:07:25.080 --> 00:07:26.589 align:start position:0%
an insight for question two what am I
going<00:07:25.160><c> to</c><00:07:25.319><c> call</c><00:07:25.599><c> this</c><00:07:25.879><c> I'm</c><00:07:26.000><c> going</c><00:07:26.120><c> to</c><00:07:26.280><c> call</c>

00:07:26.589 --> 00:07:26.599 align:start position:0%
going to call this I'm going to call
 

00:07:26.599 --> 00:07:29.830 align:start position:0%
going to call this I'm going to call
this<00:07:27.319><c> uh</c><00:07:27.599><c> question</c><00:07:28.080><c> two</c>

00:07:29.830 --> 00:07:29.840 align:start position:0%
this uh question two
 

00:07:29.840 --> 00:07:32.790 align:start position:0%
this uh question two
um<00:07:30.800><c> number</c><00:07:31.360><c> of</c>

00:07:32.790 --> 00:07:32.800 align:start position:0%
um number of
 

00:07:32.800 --> 00:07:36.830 align:start position:0%
um number of
clicks<00:07:33.800><c> uh</c><00:07:34.280><c> per</c><00:07:34.720><c> sub</c>

00:07:36.830 --> 00:07:36.840 align:start position:0%
clicks uh per sub
 

00:07:36.840 --> 00:07:38.710 align:start position:0%
clicks uh per sub
publisher<00:07:37.840><c> uh</c><00:07:37.960><c> you</c><00:07:38.080><c> know</c><00:07:38.240><c> I'm</c><00:07:38.319><c> just</c><00:07:38.440><c> going</c><00:07:38.520><c> to</c>

00:07:38.710 --> 00:07:38.720 align:start position:0%
publisher uh you know I'm just going to
 

00:07:38.720 --> 00:07:41.309 align:start position:0%
publisher uh you know I'm just going to
click<00:07:39.039><c> I'm</c><00:07:39.160><c> just</c><00:07:39.280><c> going</c><00:07:39.400><c> to</c><00:07:39.520><c> save</c><00:07:39.800><c> it</c><00:07:40.000><c> as</c>

00:07:41.309 --> 00:07:41.319 align:start position:0%
click I'm just going to save it as
 

00:07:41.319 --> 00:07:44.990 align:start position:0%
click I'm just going to save it as
underperforming<00:07:42.319><c> subp</c><00:07:42.840><c> Publishers</c>

00:07:44.990 --> 00:07:45.000 align:start position:0%
underperforming subp Publishers
 

00:07:45.000 --> 00:07:47.710 align:start position:0%
underperforming subp Publishers
right<00:07:46.000><c> sub</c>

00:07:47.710 --> 00:07:47.720 align:start position:0%
right sub
 

00:07:47.720 --> 00:07:50.589 align:start position:0%
right sub
Publishers<00:07:48.720><c> okay</c><00:07:49.000><c> cool</c><00:07:49.479><c> and</c><00:07:49.800><c> again</c><00:07:50.280><c> this</c><00:07:50.440><c> time</c>

00:07:50.589 --> 00:07:50.599 align:start position:0%
Publishers okay cool and again this time
 

00:07:50.599 --> 00:07:52.270 align:start position:0%
Publishers okay cool and again this time
I<00:07:50.720><c> do</c><00:07:50.840><c> want</c><00:07:50.960><c> to</c><00:07:51.120><c> add</c><00:07:51.280><c> it</c><00:07:51.400><c> to</c><00:07:51.520><c> my</c>

00:07:52.270 --> 00:07:52.280 align:start position:0%
I do want to add it to my
 

00:07:52.280 --> 00:07:55.629 align:start position:0%
I do want to add it to my
dashboard<00:07:53.280><c> uh</c><00:07:53.479><c> the</c><00:07:53.639><c> startup</c><00:07:54.159><c> dashboard</c><00:07:55.039><c> start</c>

00:07:55.629 --> 00:07:55.639 align:start position:0%
dashboard uh the startup dashboard start
 

00:07:55.639 --> 00:07:57.869 align:start position:0%
dashboard uh the startup dashboard start
start<00:07:55.919><c> app</c>

00:07:57.869 --> 00:07:57.879 align:start position:0%
start app
 

00:07:57.879 --> 00:08:00.070 align:start position:0%
start app
assignment<00:07:58.879><c> all</c><00:07:59.039><c> right</c><00:07:59.520><c> make</c><00:07:59.680><c> sure</c><00:07:59.840><c> that</c><00:07:59.960><c> it</c>

00:08:00.070 --> 00:08:00.080 align:start position:0%
assignment all right make sure that it
 

00:08:00.080 --> 00:08:02.189 align:start position:0%
assignment all right make sure that it
spreads<00:08:00.520><c> the</c><00:08:00.680><c> entire</c><00:08:01.080><c> way</c><00:08:01.560><c> really</c><00:08:01.800><c> cool</c><00:08:02.080><c> the</c>

00:08:02.189 --> 00:08:02.199 align:start position:0%
spreads the entire way really cool the
 

00:08:02.199 --> 00:08:04.110 align:start position:0%
spreads the entire way really cool the
number<00:08:02.400><c> of</c><00:08:02.599><c> clicks</c><00:08:02.879><c> and</c><00:08:03.039><c> the</c><00:08:03.159><c> sub</c>

00:08:04.110 --> 00:08:04.120 align:start position:0%
number of clicks and the sub
 

00:08:04.120 --> 00:08:07.469 align:start position:0%
number of clicks and the sub
publisher<00:08:05.120><c> uh</c><00:08:05.280><c> that</c><00:08:05.440><c> had</c><00:08:05.720><c> zero</c><00:08:06.240><c> deposits</c><00:08:07.240><c> okay</c>

00:08:07.469 --> 00:08:07.479 align:start position:0%
publisher uh that had zero deposits okay
 

00:08:07.479 --> 00:08:09.029 align:start position:0%
publisher uh that had zero deposits okay
cool

00:08:09.029 --> 00:08:09.039 align:start position:0%
cool
 

00:08:09.039 --> 00:08:10.950 align:start position:0%
cool
save

00:08:10.950 --> 00:08:10.960 align:start position:0%
save
 

00:08:10.960 --> 00:08:13.990 align:start position:0%
save
nice<00:08:11.960><c> uh</c><00:08:12.159><c> let's</c><00:08:12.319><c> see</c><00:08:12.560><c> over</c><00:08:12.840><c> here</c><00:08:13.479><c> and</c><00:08:13.800><c> yeah</c>

00:08:13.990 --> 00:08:14.000 align:start position:0%
nice uh let's see over here and yeah
 

00:08:14.000 --> 00:08:15.950 align:start position:0%
nice uh let's see over here and yeah
that's<00:08:14.280><c> that's</c><00:08:14.520><c> pretty</c><00:08:14.720><c> much</c><00:08:15.039><c> that</c><00:08:15.400><c> and</c><00:08:15.759><c> let's</c>

00:08:15.950 --> 00:08:15.960 align:start position:0%
that's that's pretty much that and let's
 

00:08:15.960 --> 00:08:20.110 align:start position:0%
that's that's pretty much that and let's
go<00:08:16.159><c> back</c><00:08:16.360><c> into</c><00:08:16.720><c> my</c><00:08:17.240><c> my</c><00:08:17.840><c> question</c><00:08:18.240><c> over</c><00:08:18.960><c> here</c><00:08:19.960><c> uh</c>

00:08:20.110 --> 00:08:20.120 align:start position:0%
go back into my my question over here uh
 

00:08:20.120 --> 00:08:21.710 align:start position:0%
go back into my my question over here uh
you<00:08:20.199><c> know</c><00:08:20.360><c> what</c><00:08:20.520><c> let's</c><00:08:20.759><c> delete</c><00:08:21.199><c> this</c><00:08:21.479><c> over</c>

00:08:21.710 --> 00:08:21.720 align:start position:0%
you know what let's delete this over
 

00:08:21.720 --> 00:08:23.909 align:start position:0%
you know what let's delete this over
here<00:08:22.039><c> because</c><00:08:22.199><c> it's</c><00:08:22.400><c> not</c><00:08:22.720><c> as</c>

00:08:23.909 --> 00:08:23.919 align:start position:0%
here because it's not as
 

00:08:23.919 --> 00:08:27.629 align:start position:0%
here because it's not as
helpful<00:08:24.919><c> okay</c><00:08:25.159><c> remove</c><00:08:26.159><c> that</c><00:08:27.159><c> I'm</c><00:08:27.240><c> going</c><00:08:27.360><c> to</c><00:08:27.479><c> go</c>

00:08:27.629 --> 00:08:27.639 align:start position:0%
helpful okay remove that I'm going to go
 

00:08:27.639 --> 00:08:30.990 align:start position:0%
helpful okay remove that I'm going to go
into<00:08:27.960><c> this</c><00:08:28.199><c> question</c><00:08:28.840><c> over</c><00:08:29.159><c> here</c><00:08:29.840><c> here</c><00:08:30.840><c> uh</c>

00:08:30.990 --> 00:08:31.000 align:start position:0%
into this question over here here uh
 

00:08:31.000 --> 00:08:32.389 align:start position:0%
into this question over here here uh
let's<00:08:31.199><c> see</c><00:08:31.440><c> what</c><00:08:31.520><c> do</c><00:08:31.639><c> I</c>

00:08:32.389 --> 00:08:32.399 align:start position:0%
let's see what do I
 

00:08:32.399 --> 00:08:35.870 align:start position:0%
let's see what do I
want<00:08:33.399><c> I</c><00:08:33.519><c> want</c><00:08:33.880><c> these</c><00:08:34.560><c> these</c><00:08:34.919><c> top</c><00:08:35.279><c> five</c>

00:08:35.870 --> 00:08:35.880 align:start position:0%
want I want these these top five
 

00:08:35.880 --> 00:08:38.110 align:start position:0%
want I want these these top five
underperforming

00:08:38.110 --> 00:08:38.120 align:start position:0%
underperforming
 

00:08:38.120 --> 00:08:39.630 align:start position:0%
underperforming
sources

00:08:39.630 --> 00:08:39.640 align:start position:0%
sources
 

00:08:39.640 --> 00:08:42.750 align:start position:0%
sources
okay<00:08:40.640><c> see</c><00:08:41.000><c> go</c><00:08:41.200><c> in</c>

00:08:42.750 --> 00:08:42.760 align:start position:0%
okay see go in
 

00:08:42.760 --> 00:08:46.150 align:start position:0%
okay see go in
here<00:08:43.760><c> okay</c><00:08:44.000><c> cool</c><00:08:44.880><c> all</c><00:08:45.000><c> right</c><00:08:45.200><c> good</c><00:08:45.399><c> enough</c>

00:08:46.150 --> 00:08:46.160 align:start position:0%
here okay cool all right good enough
 

00:08:46.160 --> 00:08:47.509 align:start position:0%
here okay cool all right good enough
five<00:08:46.480><c> traffic</c>

00:08:47.509 --> 00:08:47.519 align:start position:0%
five traffic
 

00:08:47.519 --> 00:08:50.670 align:start position:0%
five traffic
sources<00:08:48.519><c> um</c><00:08:49.240><c> let's</c>

00:08:50.670 --> 00:08:50.680 align:start position:0%
sources um let's
 

00:08:50.680 --> 00:08:55.110 align:start position:0%
sources um let's
see<00:08:51.680><c> and</c><00:08:51.839><c> I</c><00:08:52.000><c> just</c><00:08:52.160><c> need</c><00:08:52.640><c> I</c><00:08:52.720><c> just</c><00:08:52.880><c> need</c><00:08:53.040><c> to</c><00:08:54.120><c> Fair</c>

00:08:55.110 --> 00:08:55.120 align:start position:0%
see and I just need I just need to Fair
 

00:08:55.120 --> 00:08:57.829 align:start position:0%
see and I just need I just need to Fair
uh<00:08:55.440><c> number</c><00:08:55.800><c> of</c><00:08:56.200><c> clicks</c>

00:08:57.829 --> 00:08:57.839 align:start position:0%
uh number of clicks
 

00:08:57.839 --> 00:09:00.269 align:start position:0%
uh number of clicks
per<00:08:58.839><c> sub</c>

00:09:00.269 --> 00:09:00.279 align:start position:0%
per sub
 

00:09:00.279 --> 00:09:02.069 align:start position:0%
per sub
P<00:09:01.079><c> sub</c>

00:09:02.069 --> 00:09:02.079 align:start position:0%
P sub
 

00:09:02.079 --> 00:09:05.750 align:start position:0%
P sub
publisher<00:09:03.079><c> with</c><00:09:03.680><c> zero</c>

00:09:05.750 --> 00:09:05.760 align:start position:0%
publisher with zero
 

00:09:05.760 --> 00:09:10.269 align:start position:0%
publisher with zero
deposits<00:09:06.760><c> okay</c><00:09:07.760><c> sounds</c><00:09:08.160><c> good</c><00:09:08.480><c> qu</c><00:09:09.120><c> um</c><00:09:10.040><c> I</c><00:09:10.120><c> think</c>

00:09:10.269 --> 00:09:10.279 align:start position:0%
deposits okay sounds good qu um I think
 

00:09:10.279 --> 00:09:11.829 align:start position:0%
deposits okay sounds good qu um I think
that<00:09:10.480><c> that</c><00:09:10.680><c> that's</c><00:09:10.880><c> pretty</c><00:09:11.160><c> good</c><00:09:11.360><c> in</c><00:09:11.480><c> the</c><00:09:11.600><c> next</c>

00:09:11.829 --> 00:09:11.839 align:start position:0%
that that that's pretty good in the next
 

00:09:11.839 --> 00:09:13.190 align:start position:0%
that that that's pretty good in the next
section<00:09:12.079><c> we're</c><00:09:12.240><c> going</c><00:09:12.320><c> to</c><00:09:12.480><c> continue</c><00:09:12.920><c> with</c><00:09:13.040><c> the</c>

00:09:13.190 --> 00:09:13.200 align:start position:0%
section we're going to continue with the
 

00:09:13.200 --> 00:09:15.069 align:start position:0%
section we're going to continue with the
next<00:09:13.360><c> one</c><00:09:13.560><c> let's</c><00:09:13.720><c> see</c><00:09:13.839><c> what</c><00:09:13.959><c> it</c><00:09:14.040><c> says</c><00:09:14.760><c> uh</c><00:09:14.959><c> in</c>

00:09:15.069 --> 00:09:15.079 align:start position:0%
next one let's see what it says uh in
 

00:09:15.079 --> 00:09:16.269 align:start position:0%
next one let's see what it says uh in
the<00:09:15.200><c> question</c><00:09:15.560><c> do</c><00:09:15.640><c> you</c><00:09:15.760><c> see</c><00:09:15.920><c> anything</c>

00:09:16.269 --> 00:09:16.279 align:start position:0%
the question do you see anything
 

00:09:16.279 --> 00:09:17.430 align:start position:0%
the question do you see anything
interesting<00:09:16.600><c> in</c><00:09:16.720><c> the</c><00:09:16.880><c> data</c><00:09:17.079><c> that</c><00:09:17.200><c> can</c><00:09:17.279><c> help</c>

00:09:17.430 --> 00:09:17.440 align:start position:0%
interesting in the data that can help
 

00:09:17.440 --> 00:09:20.150 align:start position:0%
interesting in the data that can help
you<00:09:17.920><c> understand</c><00:09:18.600><c> user</c><00:09:19.120><c> behavior</c><00:09:19.600><c> and</c><00:09:19.800><c> focus</c>

00:09:20.150 --> 00:09:20.160 align:start position:0%
you understand user behavior and focus
 

00:09:20.160 --> 00:09:21.829 align:start position:0%
you understand user behavior and focus
your<00:09:20.399><c> campaign</c><00:09:20.880><c> targeting</c><00:09:21.360><c> for</c><00:09:21.560><c> better</c>

00:09:21.829 --> 00:09:21.839 align:start position:0%
your campaign targeting for better
 

00:09:21.839 --> 00:09:23.829 align:start position:0%
your campaign targeting for better
performance<00:09:22.320><c> moving</c><00:09:22.600><c> forward</c><00:09:23.160><c> we</c><00:09:23.399><c> can</c><00:09:23.560><c> dig</c><00:09:23.760><c> a</c>

00:09:23.829 --> 00:09:23.839 align:start position:0%
performance moving forward we can dig a
 

00:09:23.839 --> 00:09:25.269 align:start position:0%
performance moving forward we can dig a
little<00:09:24.040><c> bit</c><00:09:24.200><c> deeper</c><00:09:24.560><c> over</c><00:09:24.720><c> here</c><00:09:24.920><c> I</c><00:09:25.000><c> think</c><00:09:25.160><c> what</c>

00:09:25.269 --> 00:09:25.279 align:start position:0%
little bit deeper over here I think what
 

00:09:25.279 --> 00:09:27.069 align:start position:0%
little bit deeper over here I think what
we<00:09:25.440><c> really</c><00:09:25.640><c> want</c><00:09:25.839><c> is</c><00:09:26.000><c> the</c><00:09:26.200><c> conversion</c><00:09:26.760><c> rate</c>

00:09:27.069 --> 00:09:27.079 align:start position:0%
we really want is the conversion rate
 

00:09:27.079 --> 00:09:31.030 align:start position:0%
we really want is the conversion rate
over<00:09:27.360><c> here</c><00:09:28.240><c> meaning</c><00:09:29.240><c> um</c><00:09:29.480><c> um</c><00:09:30.240><c> how</c><00:09:30.399><c> many</c><00:09:30.720><c> clicks</c>

00:09:31.030 --> 00:09:31.040 align:start position:0%
over here meaning um um how many clicks
 

00:09:31.040 --> 00:09:33.150 align:start position:0%
over here meaning um um how many clicks
did<00:09:31.200><c> we</c><00:09:31.320><c> get</c><00:09:31.480><c> per</c><00:09:31.680><c> subp</c><00:09:32.040><c> publisher</c><00:09:32.800><c> and</c><00:09:33.000><c> how</c>

00:09:33.150 --> 00:09:33.160 align:start position:0%
did we get per subp publisher and how
 

00:09:33.160 --> 00:09:35.269 align:start position:0%
did we get per subp publisher and how
many<00:09:33.519><c> deposits</c><00:09:34.120><c> how</c><00:09:34.200><c> many</c><00:09:34.440><c> registrations</c><00:09:35.120><c> did</c>

00:09:35.269 --> 00:09:35.279 align:start position:0%
many deposits how many registrations did
 

00:09:35.279 --> 00:09:37.150 align:start position:0%
many deposits how many registrations did
we<00:09:35.399><c> get</c><00:09:35.560><c> per</c><00:09:35.720><c> sub</c><00:09:36.000><c> publisher</c><00:09:36.720><c> and</c><00:09:36.880><c> what</c><00:09:37.000><c> was</c>

00:09:37.150 --> 00:09:37.160 align:start position:0%
we get per sub publisher and what was
 

00:09:37.160 --> 00:09:38.870 align:start position:0%
we get per sub publisher and what was
the<00:09:37.399><c> conversion</c><00:09:38.000><c> rate</c><00:09:38.360><c> because</c><00:09:38.560><c> that's</c><00:09:38.720><c> the</c>

00:09:38.870 --> 00:09:38.880 align:start position:0%
the conversion rate because that's the
 

00:09:38.880 --> 00:09:40.269 align:start position:0%
the conversion rate because that's the
most<00:09:39.120><c> important</c><00:09:39.519><c> so</c><00:09:39.640><c> in</c><00:09:39.720><c> the</c><00:09:39.839><c> next</c><00:09:40.079><c> question</c>

00:09:40.269 --> 00:09:40.279 align:start position:0%
most important so in the next question
 

00:09:40.279 --> 00:09:42.230 align:start position:0%
most important so in the next question
we're<00:09:40.440><c> going</c><00:09:40.519><c> to</c><00:09:40.640><c> dig</c><00:09:40.839><c> deeper</c><00:09:41.160><c> into</c><00:09:41.360><c> the</c><00:09:41.519><c> data</c>

00:09:42.230 --> 00:09:42.240 align:start position:0%
we're going to dig deeper into the data
 

00:09:42.240 --> 00:09:43.750 align:start position:0%
we're going to dig deeper into the data
try<00:09:42.399><c> to</c><00:09:42.600><c> answer</c><00:09:42.880><c> the</c><00:09:43.000><c> rest</c><00:09:43.160><c> of</c><00:09:43.279><c> the</c><00:09:43.440><c> questions</c>

00:09:43.750 --> 00:09:43.760 align:start position:0%
try to answer the rest of the questions
 

00:09:43.760 --> 00:09:45.069 align:start position:0%
try to answer the rest of the questions
if<00:09:43.839><c> you</c><00:09:43.959><c> have</c><00:09:44.079><c> any</c><00:09:44.279><c> questions</c><00:09:44.600><c> feel</c><00:09:44.760><c> free</c><00:09:44.959><c> to</c>

00:09:45.069 --> 00:09:45.079 align:start position:0%
if you have any questions feel free to
 

00:09:45.079 --> 00:09:47.190 align:start position:0%
if you have any questions feel free to
reach<00:09:45.360><c> out</c><00:09:45.640><c> and</c><00:09:45.760><c> I'll</c><00:09:45.920><c> see</c><00:09:46.120><c> you</c><00:09:46.360><c> guys</c><00:09:46.640><c> in</c><00:09:47.000><c> the</c>

00:09:47.190 --> 00:09:47.200 align:start position:0%
reach out and I'll see you guys in the
 

00:09:47.200 --> 00:09:50.480 align:start position:0%
reach out and I'll see you guys in the
next<00:09:47.480><c> one</c>

