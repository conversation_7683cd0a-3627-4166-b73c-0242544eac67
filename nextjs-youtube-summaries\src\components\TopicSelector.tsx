'use client'

import { useState, useEffect } from 'react'
import { TopicTable } from '@/types'
import { Filter, ChevronDown } from 'lucide-react'

interface TopicSelectorProps {
  selectedTopics: string[]
  onTopicsChange: (topics: string[]) => void
}

export function TopicSelector({ selectedTopics, onTopicsChange }: TopicSelectorProps) {
  const [topics, setTopics] = useState<TopicTable[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTopics()
  }, [])

  const fetchTopics = async () => {
    try {
      const response = await fetch('/api/topics')
      if (response.ok) {
        const data = await response.json()
        setTopics(data.topics)
      }
    } catch (error) {
      console.error('Error fetching topics:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTopicToggle = (topicName: string) => {
    const newTopics = selectedTopics.includes(topicName)
      ? selectedTopics.filter(t => t !== topicName)
      : [...selectedTopics, topicName]
    
    onTopicsChange(newTopics)
  }

  const clearSelection = () => {
    onTopicsChange([])
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded-lg"></div>
      </div>
    )
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-500" />
          <span className="text-gray-700">
            {selectedTopics.length === 0 
              ? 'All Topics' 
              : `${selectedTopics.length} topic${selectedTopics.length > 1 ? 's' : ''} selected`
            }
          </span>
        </div>
        <ChevronDown className={`w-5 h-5 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto">
          <div className="p-2">
            {selectedTopics.length > 0 && (
              <button
                onClick={clearSelection}
                className="w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded"
              >
                Clear selection
              </button>
            )}
            
            {topics.map((topic) => (
              <label
                key={topic.name}
                className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer rounded"
              >
                <input
                  type="checkbox"
                  checked={selectedTopics.includes(topic.name)}
                  onChange={() => handleTopicToggle(topic.name)}
                  className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    {topic.display_name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {topic.video_count} videos
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
