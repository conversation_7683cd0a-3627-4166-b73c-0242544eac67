WEBVTT
Kind: captions
Language: en

00:00:04.080 --> 00:00:07.110 align:start position:0%
 
hello<00:00:04.799><c> my</c><00:00:04.920><c> name</c><00:00:05.080><c> is</c><00:00:05.279><c> <PERSON><PERSON><PERSON></c><00:00:06.040><c> and</c><00:00:06.520><c> I</c><00:00:06.759><c> want</c><00:00:06.919><c> to</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
hello my name is <PERSON><PERSON><PERSON> and I want to
 

00:00:07.120 --> 00:00:10.270 align:start position:0%
hello my name is <PERSON><PERSON><PERSON> and I want to
introduce<00:00:07.560><c> you</c><00:00:07.720><c> to</c><00:00:08.160><c> quadrant</c><00:00:08.639><c> hybrid</c><00:00:09.280><c> Cloud</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
introduce you to quadrant hybrid Cloud
 

00:00:10.280 --> 00:00:12.030 align:start position:0%
introduce you to quadrant hybrid Cloud
quadrant<00:00:10.679><c> hybrid</c><00:00:10.960><c> cloud</c><00:00:11.200><c> is</c><00:00:11.320><c> an</c><00:00:11.480><c> exciting</c><00:00:11.840><c> new</c>

00:00:12.030 --> 00:00:12.040 align:start position:0%
quadrant hybrid cloud is an exciting new
 

00:00:12.040 --> 00:00:14.709 align:start position:0%
quadrant hybrid cloud is an exciting new
feature<00:00:12.719><c> that</c><00:00:13.040><c> lets</c><00:00:13.360><c> you</c><00:00:14.120><c> combine</c><00:00:14.559><c> the</c>

00:00:14.709 --> 00:00:14.719 align:start position:0%
feature that lets you combine the
 

00:00:14.719 --> 00:00:17.150 align:start position:0%
feature that lets you combine the
benefits<00:00:15.280><c> of</c><00:00:15.440><c> a</c><00:00:15.639><c> completely</c><00:00:16.320><c> managed</c><00:00:16.760><c> Cloud</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
benefits of a completely managed Cloud
 

00:00:17.160 --> 00:00:20.750 align:start position:0%
benefits of a completely managed Cloud
product<00:00:17.880><c> to</c><00:00:18.840><c> run</c><00:00:19.279><c> quadrant</c><00:00:19.840><c> databases</c><00:00:20.439><c> in</c><00:00:20.560><c> a</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
product to run quadrant databases in a
 

00:00:20.760 --> 00:00:24.790 align:start position:0%
product to run quadrant databases in a
highly<00:00:21.039><c> available</c><00:00:21.439><c> fall</c><00:00:21.760><c> tolerant</c><00:00:22.560><c> way</c><00:00:23.800><c> with</c>

00:00:24.790 --> 00:00:24.800 align:start position:0%
highly available fall tolerant way with
 

00:00:24.800 --> 00:00:27.550 align:start position:0%
highly available fall tolerant way with
the<00:00:24.960><c> benefits</c><00:00:25.599><c> of</c><00:00:25.840><c> data</c><00:00:26.240><c> locality</c><00:00:27.240><c> data</c>

00:00:27.550 --> 00:00:27.560 align:start position:0%
the benefits of data locality data
 

00:00:27.560 --> 00:00:29.470 align:start position:0%
the benefits of data locality data
sovereignity<00:00:28.560><c> and</c><00:00:28.720><c> running</c><00:00:29.119><c> actually</c><00:00:29.359><c> the</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
sovereignity and running actually the
 

00:00:29.480 --> 00:00:31.150 align:start position:0%
sovereignity and running actually the
databases<00:00:30.039><c> on</c><00:00:30.160><c> the</c><00:00:30.279><c> infrastructure</c><00:00:30.880><c> of</c><00:00:31.000><c> your</c>

00:00:31.150 --> 00:00:31.160 align:start position:0%
databases on the infrastructure of your
 

00:00:31.160 --> 00:00:33.869 align:start position:0%
databases on the infrastructure of your
choice<00:00:32.119><c> to</c><00:00:32.279><c> Showcase</c><00:00:32.719><c> this</c><00:00:32.840><c> to</c><00:00:33.000><c> you</c><00:00:33.520><c> I</c>

00:00:33.869 --> 00:00:33.879 align:start position:0%
choice to Showcase this to you I
 

00:00:33.879 --> 00:00:35.990 align:start position:0%
choice to Showcase this to you I
prepared<00:00:34.320><c> a</c><00:00:34.480><c> small</c><00:00:34.879><c> cluster</c><00:00:35.360><c> in</c><00:00:35.480><c> my</c><00:00:35.680><c> private</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
prepared a small cluster in my private
 

00:00:36.000 --> 00:00:38.990 align:start position:0%
prepared a small cluster in my private
digital<00:00:36.320><c> ocean</c><00:00:36.840><c> account</c><00:00:37.840><c> and</c><00:00:38.399><c> you</c><00:00:38.520><c> can</c><00:00:38.719><c> see</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
digital ocean account and you can see
 

00:00:39.000 --> 00:00:41.709 align:start position:0%
digital ocean account and you can see
here<00:00:39.840><c> that</c><00:00:40.559><c> it's</c><00:00:40.719><c> a</c><00:00:40.840><c> basic</c><00:00:41.120><c> kubernetes</c>

00:00:41.709 --> 00:00:41.719 align:start position:0%
here that it's a basic kubernetes
 

00:00:41.719 --> 00:00:44.270 align:start position:0%
here that it's a basic kubernetes
cluster<00:00:42.239><c> I</c><00:00:42.360><c> created</c><00:00:42.719><c> it</c><00:00:42.879><c> like</c><00:00:43.039><c> 7</c><00:00:43.320><c> minutes</c><00:00:43.680><c> ago</c>

00:00:44.270 --> 00:00:44.280 align:start position:0%
cluster I created it like 7 minutes ago
 

00:00:44.280 --> 00:00:47.470 align:start position:0%
cluster I created it like 7 minutes ago
it<00:00:44.399><c> is</c><00:00:44.559><c> not</c><00:00:44.760><c> tied</c><00:00:45.039><c> to</c><00:00:45.200><c> any</c><00:00:45.399><c> quadrant</c>

00:00:47.470 --> 00:00:47.480 align:start position:0%
it is not tied to any quadrant
 

00:00:47.480 --> 00:00:50.150 align:start position:0%
it is not tied to any quadrant
infrastructure<00:00:48.480><c> if</c><00:00:48.640><c> we</c>

00:00:50.150 --> 00:00:50.160 align:start position:0%
infrastructure if we
 

00:00:50.160 --> 00:00:53.630 align:start position:0%
infrastructure if we
look<00:00:51.160><c> at</c><00:00:52.079><c> the</c><00:00:52.280><c> cluster</c><00:00:52.719><c> itself</c><00:00:53.160><c> you</c><00:00:53.239><c> can</c><00:00:53.399><c> also</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
look at the cluster itself you can also
 

00:00:53.640 --> 00:00:55.510 align:start position:0%
look at the cluster itself you can also
see<00:00:53.879><c> it</c><00:00:54.039><c> has</c><00:00:54.160><c> a</c><00:00:54.239><c> single</c><00:00:54.520><c> note</c><00:00:54.920><c> for</c><00:00:55.160><c> demo</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
see it has a single note for demo
 

00:00:55.520 --> 00:00:58.750 align:start position:0%
see it has a single note for demo
purposes<00:00:56.520><c> and</c><00:00:57.440><c> all</c><00:00:57.640><c> the</c><00:00:57.800><c> standard</c><00:00:58.280><c> components</c>

00:00:58.750 --> 00:00:58.760 align:start position:0%
purposes and all the standard components
 

00:00:58.760 --> 00:01:00.430 align:start position:0%
purposes and all the standard components
are<00:00:58.960><c> installed</c><00:00:59.399><c> in</c><00:00:59.559><c> there</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
are installed in there
 

00:01:00.440 --> 00:01:03.069 align:start position:0%
are installed in there
if<00:01:00.559><c> I</c><00:01:00.680><c> want</c><00:01:00.840><c> to</c><00:01:00.960><c> Now</c><00:01:01.199><c> hook</c><00:01:01.480><c> up</c><00:01:02.000><c> this</c><00:01:02.480><c> kubernetes</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
if I want to Now hook up this kubernetes
 

00:01:03.079 --> 00:01:04.670 align:start position:0%
if I want to Now hook up this kubernetes
cluster<00:01:03.480><c> on</c><00:01:03.640><c> my</c><00:01:03.800><c> private</c><00:01:04.080><c> digital</c><00:01:04.360><c> ocean</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
cluster on my private digital ocean
 

00:01:04.680 --> 00:01:07.310 align:start position:0%
cluster on my private digital ocean
account<00:01:05.280><c> to</c><00:01:05.439><c> the</c><00:01:05.560><c> quadrant</c><00:01:05.960><c> hybrid</c><00:01:06.240><c> Cloud</c><00:01:07.240><c> I</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
account to the quadrant hybrid Cloud I
 

00:01:07.320 --> 00:01:09.030 align:start position:0%
account to the quadrant hybrid Cloud I
can<00:01:07.520><c> go</c><00:01:07.720><c> into</c><00:01:08.080><c> the</c><00:01:08.240><c> quadrant</c><00:01:08.680><c> Cloud</c>

00:01:09.030 --> 00:01:09.040 align:start position:0%
can go into the quadrant Cloud
 

00:01:09.040 --> 00:01:11.190 align:start position:0%
can go into the quadrant Cloud
Management<00:01:09.520><c> console</c><00:01:10.360><c> and</c><00:01:10.560><c> to</c><00:01:10.759><c> the</c><00:01:10.920><c> hybrid</c>

00:01:11.190 --> 00:01:11.200 align:start position:0%
Management console and to the hybrid
 

00:01:11.200 --> 00:01:14.710 align:start position:0%
Management console and to the hybrid
Cloud<00:01:11.960><c> section</c><00:01:12.960><c> there</c><00:01:13.799><c> I</c><00:01:14.080><c> need</c><00:01:14.280><c> to</c><00:01:14.439><c> pick</c><00:01:14.600><c> a</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
Cloud section there I need to pick a
 

00:01:14.720 --> 00:01:19.749 align:start position:0%
Cloud section there I need to pick a
name<00:01:15.320><c> for</c><00:01:15.840><c> example</c><00:01:16.840><c> digital</c><00:01:17.720><c> ocean</c><00:01:18.720><c> for</c><00:01:19.400><c> my</c>

00:01:19.749 --> 00:01:19.759 align:start position:0%
name for example digital ocean for my
 

00:01:19.759 --> 00:01:21.789 align:start position:0%
name for example digital ocean for my
first<00:01:20.079><c> hybrid</c><00:01:20.439><c> Cloud</c><00:01:20.720><c> environment</c><00:01:21.439><c> you</c><00:01:21.560><c> can</c>

00:01:21.789 --> 00:01:21.799 align:start position:0%
first hybrid Cloud environment you can
 

00:01:21.799 --> 00:01:23.550 align:start position:0%
first hybrid Cloud environment you can
create<00:01:22.119><c> as</c><00:01:22.280><c> many</c><00:01:22.520><c> hybrid</c><00:01:22.799><c> Cloud</c><00:01:23.079><c> environments</c>

00:01:23.550 --> 00:01:23.560 align:start position:0%
create as many hybrid Cloud environments
 

00:01:23.560 --> 00:01:25.469 align:start position:0%
create as many hybrid Cloud environments
as<00:01:23.680><c> you</c><00:01:23.759><c> want</c><00:01:24.560><c> every</c><00:01:24.799><c> hybrid</c><00:01:25.079><c> Cloud</c>

00:01:25.469 --> 00:01:25.479 align:start position:0%
as you want every hybrid Cloud
 

00:01:25.479 --> 00:01:28.069 align:start position:0%
as you want every hybrid Cloud
environment<00:01:26.479><c> um</c><00:01:26.720><c> is</c><00:01:26.920><c> basically</c><00:01:27.240><c> one</c><00:01:27.479><c> cuberes</c>

00:01:28.069 --> 00:01:28.079 align:start position:0%
environment um is basically one cuberes
 

00:01:28.079 --> 00:01:29.990 align:start position:0%
environment um is basically one cuberes
cluster<00:01:28.400><c> running</c><00:01:28.759><c> somewhere</c><00:01:29.400><c> and</c><00:01:29.479><c> it</c><00:01:29.600><c> doesn't</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
cluster running somewhere and it doesn't
 

00:01:30.000 --> 00:01:31.469 align:start position:0%
cluster running somewhere and it doesn't
matter<00:01:30.240><c> where</c><00:01:30.680><c> it's</c><00:01:30.840><c> running</c><00:01:31.159><c> it</c><00:01:31.240><c> can</c><00:01:31.360><c> be</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
matter where it's running it can be
 

00:01:31.479 --> 00:01:33.469 align:start position:0%
matter where it's running it can be
running<00:01:31.720><c> on</c><00:01:31.880><c> any</c><00:01:32.040><c> cloud</c><00:01:32.360><c> provider</c><00:01:33.000><c> it</c><00:01:33.119><c> can</c><00:01:33.280><c> run</c>

00:01:33.469 --> 00:01:33.479 align:start position:0%
running on any cloud provider it can run
 

00:01:33.479 --> 00:01:36.030 align:start position:0%
running on any cloud provider it can run
on<00:01:33.640><c> premise</c><00:01:34.040><c> it</c><00:01:34.159><c> can</c><00:01:34.320><c> even</c><00:01:34.479><c> run</c><00:01:34.680><c> on</c><00:01:34.799><c> your</c>

00:01:36.030 --> 00:01:36.040 align:start position:0%
on premise it can even run on your
 

00:01:36.040 --> 00:01:39.550 align:start position:0%
on premise it can even run on your
laptop<00:01:37.119><c> next</c><00:01:38.119><c> you</c><00:01:38.360><c> have</c><00:01:38.479><c> to</c><00:01:38.720><c> choose</c><00:01:39.040><c> the</c><00:01:39.200><c> Nam</c>

00:01:39.550 --> 00:01:39.560 align:start position:0%
laptop next you have to choose the Nam
 

00:01:39.560 --> 00:01:42.910 align:start position:0%
laptop next you have to choose the Nam
space<00:01:40.159><c> where</c><00:01:41.040><c> quadrant</c><00:01:42.040><c> should</c><00:01:42.399><c> and</c><00:01:42.560><c> quadrant</c>

00:01:42.910 --> 00:01:42.920 align:start position:0%
space where quadrant should and quadrant
 

00:01:42.920 --> 00:01:45.149 align:start position:0%
space where quadrant should and quadrant
Hy<00:01:43.200><c> Cloud</c><00:01:43.680><c> should</c><00:01:44.119><c> deploy</c><00:01:44.520><c> all</c><00:01:44.680><c> the</c><00:01:44.759><c> quadrant</c>

00:01:45.149 --> 00:01:45.159 align:start position:0%
Hy Cloud should deploy all the quadrant
 

00:01:45.159 --> 00:01:47.709 align:start position:0%
Hy Cloud should deploy all the quadrant
components<00:01:45.560><c> and</c><00:01:45.680><c> data</c><00:01:46.000><c> bases</c><00:01:46.320><c> in</c><00:01:47.280><c> for</c><00:01:47.439><c> this</c><00:01:47.600><c> I</c>

00:01:47.709 --> 00:01:47.719 align:start position:0%
components and data bases in for this I
 

00:01:47.719 --> 00:01:49.870 align:start position:0%
components and data bases in for this I
choose<00:01:47.960><c> quadrant</c><00:01:48.960><c> but</c><00:01:49.079><c> you</c><00:01:49.200><c> can</c><00:01:49.360><c> also</c><00:01:49.600><c> choose</c>

00:01:49.870 --> 00:01:49.880 align:start position:0%
choose quadrant but you can also choose
 

00:01:49.880 --> 00:01:52.510 align:start position:0%
choose quadrant but you can also choose
any<00:01:50.079><c> name</c><00:01:50.360><c> space</c><00:01:50.600><c> you</c><00:01:50.799><c> want</c><00:01:51.799><c> optionally</c><00:01:52.360><c> you</c>

00:01:52.510 --> 00:01:52.520 align:start position:0%
any name space you want optionally you
 

00:01:52.520 --> 00:01:55.109 align:start position:0%
any name space you want optionally you
can<00:01:52.799><c> configure</c><00:01:53.520><c> the</c><00:01:53.880><c> cubern</c><00:01:54.399><c> storage</c><00:01:54.759><c> classes</c>

00:01:55.109 --> 00:01:55.119 align:start position:0%
can configure the cubern storage classes
 

00:01:55.119 --> 00:01:57.870 align:start position:0%
can configure the cubern storage classes
and<00:01:55.280><c> volume</c><00:01:55.600><c> snapshot</c><00:01:56.360><c> classes</c><00:01:57.360><c> if</c><00:01:57.520><c> you</c><00:01:57.680><c> don't</c>

00:01:57.870 --> 00:01:57.880 align:start position:0%
and volume snapshot classes if you don't
 

00:01:57.880 --> 00:02:00.670 align:start position:0%
and volume snapshot classes if you don't
want<00:01:58.000><c> to</c><00:01:58.159><c> take</c><00:01:58.360><c> the</c><00:01:58.520><c> defaults</c><00:01:59.520><c> so</c><00:02:00.079><c> that</c><00:02:00.240><c> we</c><00:02:00.399><c> can</c>

00:02:00.670 --> 00:02:00.680 align:start position:0%
want to take the defaults so that we can
 

00:02:00.680 --> 00:02:02.749 align:start position:0%
want to take the defaults so that we can
provision<00:02:01.280><c> block</c><00:02:01.520><c> storage</c><00:02:01.920><c> for</c><00:02:02.119><c> the</c><00:02:02.240><c> Vol</c><00:02:02.600><c> for</c>

00:02:02.749 --> 00:02:02.759 align:start position:0%
provision block storage for the Vol for
 

00:02:02.759 --> 00:02:04.709 align:start position:0%
provision block storage for the Vol for
the<00:02:02.880><c> quadrant</c><00:02:03.240><c> database</c><00:02:03.680><c> volumes</c><00:02:04.360><c> and</c><00:02:04.520><c> also</c>

00:02:04.709 --> 00:02:04.719 align:start position:0%
the quadrant database volumes and also
 

00:02:04.719 --> 00:02:09.109 align:start position:0%
the quadrant database volumes and also
create<00:02:05.039><c> backups</c><00:02:05.399><c> of</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
 
 

00:02:09.119 --> 00:02:13.830 align:start position:0%
 
them<00:02:10.119><c> in</c><00:02:10.280><c> the</c><00:02:10.399><c> next</c><00:02:11.040><c> step</c><00:02:12.040><c> I</c><00:02:12.480><c> get</c><00:02:12.800><c> a</c><00:02:13.319><c> onetime</c>

00:02:13.830 --> 00:02:13.840 align:start position:0%
them in the next step I get a onetime
 

00:02:13.840 --> 00:02:15.750 align:start position:0%
them in the next step I get a onetime
single<00:02:14.200><c> use</c><00:02:14.440><c> installation</c><00:02:14.920><c> command</c><00:02:15.519><c> that</c><00:02:15.640><c> I</c>

00:02:15.750 --> 00:02:15.760 align:start position:0%
single use installation command that I
 

00:02:15.760 --> 00:02:16.710 align:start position:0%
single use installation command that I
have<00:02:15.920><c> to</c>

00:02:16.710 --> 00:02:16.720 align:start position:0%
have to
 

00:02:16.720 --> 00:02:19.630 align:start position:0%
have to
copy<00:02:17.720><c> um</c><00:02:18.000><c> this</c><00:02:18.280><c> installation</c><00:02:18.800><c> command</c>

00:02:19.630 --> 00:02:19.640 align:start position:0%
copy um this installation command
 

00:02:19.640 --> 00:02:22.190 align:start position:0%
copy um this installation command
creates<00:02:20.640><c> the</c><00:02:20.920><c> necessary</c><00:02:21.400><c> secrets</c><00:02:22.000><c> and</c>

00:02:22.190 --> 00:02:22.200 align:start position:0%
creates the necessary secrets and
 

00:02:22.200 --> 00:02:24.390 align:start position:0%
creates the necessary secrets and
installs<00:02:22.720><c> the</c><00:02:23.319><c> quadrant</c><00:02:23.800><c> kubernetes</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
installs the quadrant kubernetes
 

00:02:24.400 --> 00:02:27.509 align:start position:0%
installs the quadrant kubernetes
operator<00:02:25.319><c> and</c><00:02:25.680><c> Cloud</c><00:02:26.000><c> agent</c><00:02:26.400><c> to</c><00:02:26.560><c> connect</c><00:02:27.000><c> back</c>

00:02:27.509 --> 00:02:27.519 align:start position:0%
operator and Cloud agent to connect back
 

00:02:27.519 --> 00:02:29.470 align:start position:0%
operator and Cloud agent to connect back
to<00:02:27.959><c> quadrant</c><00:02:28.400><c> Cloud</c><00:02:28.920><c> inside</c><00:02:29.239><c> of</c><00:02:29.360><c> my</c>

00:02:29.470 --> 00:02:29.480 align:start position:0%
to quadrant Cloud inside of my
 

00:02:29.480 --> 00:02:32.470 align:start position:0%
to quadrant Cloud inside of my
kubernetes<00:02:30.280><c> tuster</c><00:02:31.280><c> so</c><00:02:31.560><c> this</c><00:02:31.680><c> is</c><00:02:31.920><c> just</c><00:02:32.200><c> one</c>

00:02:32.470 --> 00:02:32.480 align:start position:0%
kubernetes tuster so this is just one
 

00:02:32.480 --> 00:02:34.390 align:start position:0%
kubernetes tuster so this is just one
thing<00:02:32.800><c> I</c><00:02:32.959><c> initially</c><00:02:33.360><c> have</c><00:02:33.480><c> to</c>

00:02:34.390 --> 00:02:34.400 align:start position:0%
thing I initially have to
 

00:02:34.400 --> 00:02:37.430 align:start position:0%
thing I initially have to
do<00:02:35.400><c> and</c><00:02:35.840><c> then</c><00:02:36.040><c> the</c><00:02:36.200><c> whole</c><00:02:36.440><c> life</c><00:02:36.680><c> cycle</c><00:02:37.000><c> of</c><00:02:37.200><c> this</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
do and then the whole life cycle of this
 

00:02:37.440 --> 00:02:39.149 align:start position:0%
do and then the whole life cycle of this
agent<00:02:37.879><c> and</c><00:02:38.040><c> all</c><00:02:38.200><c> the</c><00:02:38.360><c> quadrant</c><00:02:38.800><c> components</c>

00:02:39.149 --> 00:02:39.159 align:start position:0%
agent and all the quadrant components
 

00:02:39.159 --> 00:02:40.949 align:start position:0%
agent and all the quadrant components
and<00:02:39.319><c> databases</c><00:02:39.920><c> can</c><00:02:40.040><c> be</c><00:02:40.200><c> controlled</c><00:02:40.680><c> from</c><00:02:40.840><c> the</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
and databases can be controlled from the
 

00:02:40.959 --> 00:02:44.710 align:start position:0%
and databases can be controlled from the
quadrant<00:02:41.319><c> Cloud</c>

00:02:44.710 --> 00:02:44.720 align:start position:0%
 
 

00:02:44.720 --> 00:02:47.390 align:start position:0%
 
console<00:02:45.720><c> while</c><00:02:46.480><c> um</c><00:02:46.599><c> the</c><00:02:46.720><c> container</c><00:02:47.080><c> images</c>

00:02:47.390 --> 00:02:47.400 align:start position:0%
console while um the container images
 

00:02:47.400 --> 00:02:50.910 align:start position:0%
console while um the container images
are<00:02:47.680><c> downloaded</c><00:02:48.680><c> and</c><00:02:49.239><c> are</c><00:02:49.440><c> starting</c><00:02:49.840><c> up</c><00:02:50.599><c> let's</c>

00:02:50.910 --> 00:02:50.920 align:start position:0%
are downloaded and are starting up let's
 

00:02:50.920 --> 00:02:56.229 align:start position:0%
are downloaded and are starting up let's
maybe<00:02:51.360><c> have</c><00:02:51.519><c> a</c><00:02:51.920><c> quick</c><00:02:52.200><c> look</c><00:02:52.560><c> at</c><00:02:52.720><c> our</c>

00:02:56.229 --> 00:02:56.239 align:start position:0%
 
 

00:02:56.239 --> 00:02:58.509 align:start position:0%
 
documentation<00:02:57.239><c> on</c><00:02:57.400><c> our</c><00:02:57.560><c> in</c><00:02:57.680><c> our</c><00:02:57.840><c> docs</c><00:02:58.159><c> you</c><00:02:58.280><c> can</c>

00:02:58.509 --> 00:02:58.519 align:start position:0%
documentation on our in our docs you can
 

00:02:58.519 --> 00:03:00.509 align:start position:0%
documentation on our in our docs you can
find<00:02:58.959><c> all</c><00:02:59.159><c> about</c><00:02:59.400><c> hybrid</c><00:02:59.680><c> CL</c><00:02:59.840><c> Cloud</c><00:03:00.159><c> you</c><00:03:00.280><c> can</c>

00:03:00.509 --> 00:03:00.519 align:start position:0%
find all about hybrid CL Cloud you can
 

00:03:00.519 --> 00:03:03.309 align:start position:0%
find all about hybrid CL Cloud you can
find<00:03:00.959><c> all</c><00:03:01.200><c> about</c><00:03:01.519><c> the</c><00:03:01.760><c> prerequisites</c><00:03:02.680><c> we</c><00:03:02.879><c> have</c>

00:03:03.309 --> 00:03:03.319 align:start position:0%
find all about the prerequisites we have
 

00:03:03.319 --> 00:03:05.190 align:start position:0%
find all about the prerequisites we have
what<00:03:03.480><c> we</c><00:03:03.599><c> need</c><00:03:03.840><c> from</c><00:03:04.000><c> a</c><00:03:04.159><c> kubernetes</c><00:03:04.760><c> cluster</c>

00:03:05.190 --> 00:03:05.200 align:start position:0%
what we need from a kubernetes cluster
 

00:03:05.200 --> 00:03:07.830 align:start position:0%
what we need from a kubernetes cluster
in<00:03:05.319><c> your</c><00:03:05.879><c> environment</c><00:03:06.879><c> basically</c><00:03:07.319><c> kubernetes</c>

00:03:07.830 --> 00:03:07.840 align:start position:0%
in your environment basically kubernetes
 

00:03:07.840 --> 00:03:10.110 align:start position:0%
in your environment basically kubernetes
cluster<00:03:08.480><c> with</c><00:03:08.720><c> persistent</c><00:03:09.159><c> storage</c><00:03:09.959><c> the</c>

00:03:10.110 --> 00:03:10.120 align:start position:0%
cluster with persistent storage the
 

00:03:10.120 --> 00:03:12.750 align:start position:0%
cluster with persistent storage the
connectivity<00:03:10.920><c> that</c><00:03:11.080><c> is</c><00:03:11.280><c> necessary</c><00:03:11.879><c> there</c><00:03:12.519><c> and</c>

00:03:12.750 --> 00:03:12.760 align:start position:0%
connectivity that is necessary there and
 

00:03:12.760 --> 00:03:14.830 align:start position:0%
connectivity that is necessary there and
we<00:03:12.959><c> also</c><00:03:13.319><c> have</c><00:03:13.560><c> dedicated</c><00:03:14.040><c> tutorials</c><00:03:14.560><c> for</c><00:03:14.720><c> all</c>

00:03:14.830 --> 00:03:14.840 align:start position:0%
we also have dedicated tutorials for all
 

00:03:14.840 --> 00:03:17.869 align:start position:0%
we also have dedicated tutorials for all
the<00:03:14.959><c> major</c><00:03:15.280><c> Cloud</c><00:03:15.760><c> providers</c><00:03:16.760><c> that</c><00:03:17.080><c> cover</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
the major Cloud providers that cover
 

00:03:17.879 --> 00:03:20.309 align:start position:0%
the major Cloud providers that cover
what<00:03:18.040><c> you</c><00:03:18.159><c> need</c><00:03:18.319><c> to</c><00:03:18.480><c> set</c><00:03:18.720><c> up</c><00:03:19.400><c> um</c><00:03:19.599><c> that</c><00:03:19.840><c> link</c><00:03:20.159><c> you</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
what you need to set up um that link you
 

00:03:20.319 --> 00:03:22.350 align:start position:0%
what you need to set up um that link you
to<00:03:20.640><c> the</c><00:03:21.000><c> manage</c><00:03:21.400><c> kubernetes</c><00:03:21.920><c> offering</c>

00:03:22.350 --> 00:03:22.360 align:start position:0%
to the manage kubernetes offering
 

00:03:22.360 --> 00:03:23.630 align:start position:0%
to the manage kubernetes offering
documentation<00:03:22.879><c> sites</c><00:03:23.159><c> on</c><00:03:23.280><c> the</c><00:03:23.400><c> cloud</c>

00:03:23.630 --> 00:03:23.640 align:start position:0%
documentation sites on the cloud
 

00:03:23.640 --> 00:03:26.030 align:start position:0%
documentation sites on the cloud
provider<00:03:24.080><c> site</c><00:03:24.640><c> and</c><00:03:24.840><c> also</c><00:03:25.120><c> cover</c><00:03:25.640><c> if</c><00:03:25.760><c> you</c><00:03:25.879><c> need</c>

00:03:26.030 --> 00:03:26.040 align:start position:0%
provider site and also cover if you need
 

00:03:26.040 --> 00:03:28.390 align:start position:0%
provider site and also cover if you need
to<00:03:26.200><c> activate</c><00:03:26.599><c> any</c><00:03:26.840><c> add-ons</c><00:03:27.640><c> or</c><00:03:27.879><c> provide</c><00:03:28.200><c> any</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
to activate any add-ons or provide any
 

00:03:28.400 --> 00:03:30.309 align:start position:0%
to activate any add-ons or provide any
additional<00:03:28.760><c> configuration</c><00:03:29.319><c> for</c><00:03:29.480><c> example</c><00:03:30.159><c> to</c>

00:03:30.309 --> 00:03:30.319 align:start position:0%
additional configuration for example to
 

00:03:30.319 --> 00:03:34.509 align:start position:0%
additional configuration for example to
consume<00:03:30.760><c> storage</c><00:03:31.599><c> or</c><00:03:32.080><c> to</c><00:03:32.280><c> be</c><00:03:32.439><c> able</c><00:03:32.640><c> to</c><00:03:32.840><c> back</c><00:03:33.040><c> it</c>

00:03:34.509 --> 00:03:34.519 align:start position:0%
consume storage or to be able to back it
 

00:03:34.519 --> 00:03:38.190 align:start position:0%
consume storage or to be able to back it
up<00:03:35.519><c> um</c><00:03:35.920><c> going</c><00:03:36.319><c> back</c><00:03:36.720><c> to</c><00:03:36.879><c> my</c><00:03:37.080><c> cluster</c><00:03:37.920><c> we</c><00:03:38.040><c> can</c>

00:03:38.190 --> 00:03:38.200 align:start position:0%
up um going back to my cluster we can
 

00:03:38.200 --> 00:03:40.750 align:start position:0%
up um going back to my cluster we can
see<00:03:38.519><c> now</c><00:03:38.879><c> that</c><00:03:39.040><c> we</c><00:03:39.120><c> are</c><00:03:39.280><c> nearly</c><00:03:39.680><c> there</c><00:03:40.080><c> so</c><00:03:40.560><c> we</c>

00:03:40.750 --> 00:03:40.760 align:start position:0%
see now that we are nearly there so we
 

00:03:40.760 --> 00:03:42.789 align:start position:0%
see now that we are nearly there so we
have<00:03:40.959><c> a</c><00:03:41.120><c> couple</c><00:03:41.400><c> components</c><00:03:41.840><c> up</c><00:03:42.040><c> and</c><00:03:42.159><c> running</c>

00:03:42.789 --> 00:03:42.799 align:start position:0%
have a couple components up and running
 

00:03:42.799 --> 00:03:44.869 align:start position:0%
have a couple components up and running
there<00:03:42.959><c> is</c><00:03:43.080><c> the</c><00:03:43.239><c> cordant</c><00:03:43.680><c> operator</c><00:03:44.680><c> that</c>

00:03:44.869 --> 00:03:44.879 align:start position:0%
there is the cordant operator that
 

00:03:44.879 --> 00:03:47.149 align:start position:0%
there is the cordant operator that
manages<00:03:45.400><c> all</c><00:03:45.640><c> the</c><00:03:45.920><c> quadrant</c><00:03:46.319><c> databases</c><00:03:47.000><c> that</c>

00:03:47.149 --> 00:03:47.159 align:start position:0%
manages all the quadrant databases that
 

00:03:47.159 --> 00:03:48.750 align:start position:0%
manages all the quadrant databases that
I'll<00:03:47.360><c> create</c><00:03:47.599><c> in</c><00:03:47.680><c> my</c><00:03:47.799><c> Cuates</c><00:03:48.239><c> cluster</c><00:03:48.519><c> later</c>

00:03:48.750 --> 00:03:48.760 align:start position:0%
I'll create in my Cuates cluster later
 

00:03:48.760 --> 00:03:51.670 align:start position:0%
I'll create in my Cuates cluster later
on<00:03:49.720><c> we</c><00:03:49.879><c> have</c><00:03:50.120><c> the</c><00:03:50.280><c> quadrant</c><00:03:50.680><c> Cloud</c><00:03:51.040><c> agent</c>

00:03:51.670 --> 00:03:51.680 align:start position:0%
on we have the quadrant Cloud agent
 

00:03:51.680 --> 00:03:53.830 align:start position:0%
on we have the quadrant Cloud agent
which<00:03:51.959><c> connects</c><00:03:52.720><c> your</c><00:03:53.000><c> environment</c><00:03:53.480><c> with</c><00:03:53.640><c> an</c>

00:03:53.830 --> 00:03:53.840 align:start position:0%
which connects your environment with an
 

00:03:53.840 --> 00:03:56.069 align:start position:0%
which connects your environment with an
outgoing<00:03:54.280><c> connection</c><00:03:54.640><c> to</c><00:03:54.799><c> our</c><00:03:55.079><c> Cloud</c>

00:03:56.069 --> 00:03:56.079 align:start position:0%
outgoing connection to our Cloud
 

00:03:56.079 --> 00:03:57.750 align:start position:0%
outgoing connection to our Cloud
important<00:03:56.519><c> there</c><00:03:56.720><c> is</c><00:03:56.920><c> we</c><00:03:57.040><c> don't</c><00:03:57.239><c> need</c><00:03:57.519><c> any</c>

00:03:57.750 --> 00:03:57.760 align:start position:0%
important there is we don't need any
 

00:03:57.760 --> 00:04:00.350 align:start position:0%
important there is we don't need any
access<00:03:58.000><c> to</c><00:03:58.120><c> your</c><00:03:58.319><c> kuus</c><00:03:58.840><c> API</c><00:03:59.840><c> and</c><00:04:00.000><c> also</c><00:04:00.239><c> we</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
access to your kuus API and also we
 

00:04:00.360 --> 00:04:04.589 align:start position:0%
access to your kuus API and also we
don't<00:04:00.560><c> need</c><00:04:00.879><c> any</c><00:04:01.519><c> access</c><00:04:02.200><c> to</c><00:04:03.200><c> um</c><00:04:03.640><c> the</c><00:04:03.879><c> apis</c><00:04:04.480><c> of</c>

00:04:04.589 --> 00:04:04.599 align:start position:0%
don't need any access to um the apis of
 

00:04:04.599 --> 00:04:06.390 align:start position:0%
don't need any access to um the apis of
your<00:04:04.760><c> cloud</c><00:04:05.079><c> provider</c><00:04:05.480><c> or</c><00:04:05.680><c> virtualization</c>

00:04:06.390 --> 00:04:06.400 align:start position:0%
your cloud provider or virtualization
 

00:04:06.400 --> 00:04:09.149 align:start position:0%
your cloud provider or virtualization
platform<00:04:06.920><c> also</c><00:04:07.879><c> so</c><00:04:08.040><c> it's</c><00:04:08.200><c> just</c><00:04:08.439><c> one</c><00:04:08.640><c> outgoing</c>

00:04:09.149 --> 00:04:09.159 align:start position:0%
platform also so it's just one outgoing
 

00:04:09.159 --> 00:04:11.350 align:start position:0%
platform also so it's just one outgoing
connection<00:04:09.480><c> to</c><00:04:09.640><c> transport</c><00:04:10.200><c> Telemetry</c><00:04:11.200><c> and</c>

00:04:11.350 --> 00:04:11.360 align:start position:0%
connection to transport Telemetry and
 

00:04:11.360 --> 00:04:13.149 align:start position:0%
connection to transport Telemetry and
also<00:04:11.599><c> your</c><00:04:11.760><c> databases</c><00:04:12.360><c> stay</c><00:04:12.640><c> completely</c>

00:04:13.149 --> 00:04:13.159 align:start position:0%
also your databases stay completely
 

00:04:13.159 --> 00:04:15.509 align:start position:0%
also your databases stay completely
isolated<00:04:13.680><c> using</c><00:04:13.959><c> your</c><00:04:14.159><c> network</c><00:04:14.560><c> your</c><00:04:14.799><c> storage</c>

00:04:15.509 --> 00:04:15.519 align:start position:0%
isolated using your network your storage
 

00:04:15.519 --> 00:04:17.469 align:start position:0%
isolated using your network your storage
your<00:04:15.840><c> compute</c><00:04:16.320><c> and</c><00:04:16.479><c> neither</c><00:04:16.799><c> we</c><00:04:17.000><c> or</c><00:04:17.199><c> anyone</c>

00:04:17.469 --> 00:04:17.479 align:start position:0%
your compute and neither we or anyone
 

00:04:17.479 --> 00:04:19.670 align:start position:0%
your compute and neither we or anyone
else<00:04:17.720><c> outside</c><00:04:18.079><c> of</c><00:04:18.160><c> your</c><00:04:18.320><c> organization</c><00:04:19.320><c> is</c>

00:04:19.670 --> 00:04:19.680 align:start position:0%
else outside of your organization is
 

00:04:19.680 --> 00:04:21.430 align:start position:0%
else outside of your organization is
physically<00:04:20.120><c> able</c><00:04:20.320><c> to</c><00:04:20.519><c> access</c>

00:04:21.430 --> 00:04:21.440 align:start position:0%
physically able to access
 

00:04:21.440 --> 00:04:24.110 align:start position:0%
physically able to access
this<00:04:22.440><c> there's</c><00:04:22.759><c> also</c><00:04:23.040><c> a</c><00:04:23.199><c> small</c><00:04:23.600><c> promus</c>

00:04:24.110 --> 00:04:24.120 align:start position:0%
this there's also a small promus
 

00:04:24.120 --> 00:04:26.189 align:start position:0%
this there's also a small promus
monitoring<00:04:24.680><c> component</c><00:04:25.240><c> which</c><00:04:25.520><c> just</c><00:04:25.759><c> runs</c><00:04:26.040><c> in</c>

00:04:26.189 --> 00:04:26.199 align:start position:0%
monitoring component which just runs in
 

00:04:26.199 --> 00:04:27.870 align:start position:0%
monitoring component which just runs in
agent<00:04:26.440><c> mode</c><00:04:26.680><c> to</c><00:04:26.840><c> transport</c><00:04:27.240><c> us</c><00:04:27.360><c> a</c><00:04:27.520><c> necessary</c>

00:04:27.870 --> 00:04:27.880 align:start position:0%
agent mode to transport us a necessary
 

00:04:27.880 --> 00:04:29.870 align:start position:0%
agent mode to transport us a necessary
Telemetry<00:04:28.639><c> so</c><00:04:28.759><c> that</c><00:04:28.880><c> we</c><00:04:29.000><c> can</c><00:04:29.120><c> also</c><00:04:29.360><c> proactive</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
Telemetry so that we can also proactive
 

00:04:29.880 --> 00:04:31.749 align:start position:0%
Telemetry so that we can also proactive
ly<00:04:30.000><c> help</c>

00:04:31.749 --> 00:04:31.759 align:start position:0%
ly help
 

00:04:31.759 --> 00:04:34.670 align:start position:0%
ly help
you<00:04:32.759><c> now</c><00:04:33.360><c> all</c><00:04:33.520><c> these</c><00:04:33.720><c> components</c><00:04:34.160><c> are</c><00:04:34.400><c> up</c><00:04:34.560><c> and</c>

00:04:34.670 --> 00:04:34.680 align:start position:0%
you now all these components are up and
 

00:04:34.680 --> 00:04:37.950 align:start position:0%
you now all these components are up and
running<00:04:35.400><c> and</c><00:04:35.759><c> I</c><00:04:35.880><c> can</c><00:04:36.080><c> go</c><00:04:36.320><c> back</c><00:04:36.720><c> to</c>

00:04:37.950 --> 00:04:37.960 align:start position:0%
running and I can go back to
 

00:04:37.960 --> 00:04:41.550 align:start position:0%
running and I can go back to
my<00:04:38.960><c> um</c><00:04:39.120><c> Quant</c><00:04:39.479><c> Cloud</c><00:04:39.960><c> interface</c><00:04:40.960><c> um</c><00:04:41.160><c> I</c><00:04:41.280><c> see</c>

00:04:41.550 --> 00:04:41.560 align:start position:0%
my um Quant Cloud interface um I see
 

00:04:41.560 --> 00:04:42.870 align:start position:0%
my um Quant Cloud interface um I see
that<00:04:41.720><c> everything</c><00:04:41.960><c> is</c><00:04:42.080><c> up</c><00:04:42.199><c> and</c><00:04:42.360><c> running</c><00:04:42.639><c> also</c>

00:04:42.870 --> 00:04:42.880 align:start position:0%
that everything is up and running also
 

00:04:42.880 --> 00:04:45.550 align:start position:0%
that everything is up and running also
from<00:04:43.160><c> here</c><00:04:43.639><c> and</c><00:04:43.840><c> I</c><00:04:43.919><c> can</c><00:04:44.080><c> start</c><00:04:44.320><c> creating</c>

00:04:45.550 --> 00:04:45.560 align:start position:0%
from here and I can start creating
 

00:04:45.560 --> 00:04:48.310 align:start position:0%
from here and I can start creating
databases<00:04:46.560><c> so</c><00:04:47.080><c> and</c><00:04:47.240><c> this</c><00:04:47.360><c> is</c><00:04:47.479><c> the</c><00:04:47.680><c> same</c><00:04:48.080><c> as</c>

00:04:48.310 --> 00:04:48.320 align:start position:0%
databases so and this is the same as
 

00:04:48.320 --> 00:04:51.189 align:start position:0%
databases so and this is the same as
with<00:04:48.479><c> manage</c><00:04:48.880><c> Cloud</c><00:04:49.600><c> you</c><00:04:49.759><c> can</c><00:04:50.479><c> choose</c><00:04:51.039><c> now</c>

00:04:51.189 --> 00:04:51.199 align:start position:0%
with manage Cloud you can choose now
 

00:04:51.199 --> 00:04:52.990 align:start position:0%
with manage Cloud you can choose now
your<00:04:51.400><c> hybrid</c><00:04:51.759><c> Cloud</c><00:04:52.039><c> environment</c><00:04:52.560><c> here</c><00:04:52.840><c> and</c>

00:04:52.990 --> 00:04:53.000 align:start position:0%
your hybrid Cloud environment here and
 

00:04:53.000 --> 00:04:54.670 align:start position:0%
your hybrid Cloud environment here and
you<00:04:53.080><c> can</c><00:04:53.360><c> have</c><00:04:53.680><c> have</c><00:04:53.880><c> as</c><00:04:54.000><c> many</c><00:04:54.199><c> environments</c>

00:04:54.670 --> 00:04:54.680 align:start position:0%
you can have have as many environments
 

00:04:54.680 --> 00:04:56.270 align:start position:0%
you can have have as many environments
as<00:04:54.800><c> you</c><00:04:54.919><c> want</c><00:04:55.240><c> I</c><00:04:55.360><c> choose</c><00:04:55.600><c> my</c><00:04:55.720><c> digital</c><00:04:56.000><c> ocean</c>

00:04:56.270 --> 00:04:56.280 align:start position:0%
as you want I choose my digital ocean
 

00:04:56.280 --> 00:05:00.230 align:start position:0%
as you want I choose my digital ocean
one<00:04:56.720><c> you</c><00:04:56.840><c> can</c><00:04:57.039><c> give</c><00:04:57.360><c> your</c><00:04:58.360><c> um</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
one you can give your um
 

00:05:00.240 --> 00:05:02.790 align:start position:0%
one you can give your um
cluster<00:05:00.639><c> and</c><00:05:00.960><c> name</c><00:05:01.960><c> you</c><00:05:02.080><c> can</c><00:05:02.240><c> choose</c><00:05:02.479><c> how</c><00:05:02.639><c> much</c>

00:05:02.790 --> 00:05:02.800 align:start position:0%
cluster and name you can choose how much
 

00:05:02.800 --> 00:05:04.670 align:start position:0%
cluster and name you can choose how much
CPU<00:05:03.320><c> and</c><00:05:03.479><c> memory</c><00:05:03.800><c> you</c><00:05:03.919><c> want</c><00:05:04.080><c> to</c><00:05:04.240><c> have</c><00:05:04.360><c> in</c><00:05:04.479><c> the</c>

00:05:04.670 --> 00:05:04.680 align:start position:0%
CPU and memory you want to have in the
 

00:05:04.680 --> 00:05:06.629 align:start position:0%
CPU and memory you want to have in the
cluster<00:05:05.600><c> you</c><00:05:05.720><c> can</c><00:05:05.880><c> choose</c><00:05:06.160><c> the</c><00:05:06.280><c> amount</c><00:05:06.520><c> of</c>

00:05:06.629 --> 00:05:06.639 align:start position:0%
cluster you can choose the amount of
 

00:05:06.639 --> 00:05:08.629 align:start position:0%
cluster you can choose the amount of
nodes<00:05:07.039><c> that</c><00:05:07.160><c> you</c><00:05:07.240><c> want</c><00:05:07.360><c> to</c><00:05:07.479><c> have</c><00:05:07.680><c> inide</c><00:05:08.440><c> of</c><00:05:08.520><c> the</c>

00:05:08.629 --> 00:05:08.639 align:start position:0%
nodes that you want to have inide of the
 

00:05:08.639 --> 00:05:10.430 align:start position:0%
nodes that you want to have inide of the
cluster<00:05:08.960><c> you</c><00:05:09.039><c> can</c><00:05:09.160><c> choose</c><00:05:09.360><c> the</c><00:05:09.479><c> dis</c><00:05:09.720><c> space</c><00:05:10.320><c> and</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
cluster you can choose the dis space and
 

00:05:10.440 --> 00:05:12.110 align:start position:0%
cluster you can choose the dis space and
of<00:05:10.600><c> course</c><00:05:10.800><c> you</c><00:05:11.080><c> at</c><00:05:11.199><c> any</c><00:05:11.360><c> point</c><00:05:11.520><c> in</c><00:05:11.680><c> time</c><00:05:11.880><c> later</c>

00:05:12.110 --> 00:05:12.120 align:start position:0%
of course you at any point in time later
 

00:05:12.120 --> 00:05:14.710 align:start position:0%
of course you at any point in time later
on<00:05:12.520><c> can</c><00:05:12.720><c> also</c><00:05:12.919><c> scale</c><00:05:13.240><c> this</c><00:05:13.400><c> up</c><00:05:14.199><c> I</c><00:05:14.320><c> only</c><00:05:14.479><c> have</c><00:05:14.600><c> a</c>

00:05:14.710 --> 00:05:14.720 align:start position:0%
on can also scale this up I only have a
 

00:05:14.720 --> 00:05:16.870 align:start position:0%
on can also scale this up I only have a
small<00:05:14.919><c> demo</c><00:05:15.199><c> cluster</c><00:05:15.520><c> so</c><00:05:15.960><c> that</c><00:05:16.320><c> let's</c><00:05:16.520><c> keep</c><00:05:16.680><c> it</c>

00:05:16.870 --> 00:05:16.880 align:start position:0%
small demo cluster so that let's keep it
 

00:05:16.880 --> 00:05:18.309 align:start position:0%
small demo cluster so that let's keep it
at<00:05:17.080><c> as</c><00:05:17.199><c> a</c>

00:05:18.309 --> 00:05:18.319 align:start position:0%
at as a
 

00:05:18.319 --> 00:05:20.430 align:start position:0%
at as a
minum<00:05:19.319><c> what</c><00:05:19.400><c> you</c><00:05:19.560><c> also</c><00:05:19.720><c> see</c><00:05:19.919><c> now</c><00:05:20.080><c> here</c><00:05:20.199><c> in</c><00:05:20.319><c> the</c>

00:05:20.430 --> 00:05:20.440 align:start position:0%
minum what you also see now here in the
 

00:05:20.440 --> 00:05:23.309 align:start position:0%
minum what you also see now here in the
cluster<00:05:20.800><c> list</c><00:05:21.440><c> that</c><00:05:21.919><c> um</c><00:05:22.080><c> it's</c><00:05:22.240><c> not</c><00:05:22.440><c> either</c><00:05:22.840><c> or</c>

00:05:23.309 --> 00:05:23.319 align:start position:0%
cluster list that um it's not either or
 

00:05:23.319 --> 00:05:25.350 align:start position:0%
cluster list that um it's not either or
with<00:05:23.479><c> manage</c><00:05:23.840><c> cloud</c><00:05:24.080><c> and</c><00:05:24.199><c> hybrid</c><00:05:24.520><c> Cloud</c>

00:05:25.350 --> 00:05:25.360 align:start position:0%
with manage cloud and hybrid Cloud
 

00:05:25.360 --> 00:05:27.510 align:start position:0%
with manage cloud and hybrid Cloud
manage<00:05:25.720><c> Cloud</c><00:05:26.199><c> where</c><00:05:26.520><c> the</c><00:05:26.680><c> databases</c><00:05:27.160><c> run</c><00:05:27.360><c> on</c>

00:05:27.510 --> 00:05:27.520 align:start position:0%
manage Cloud where the databases run on
 

00:05:27.520 --> 00:05:30.189 align:start position:0%
manage Cloud where the databases run on
our<00:05:28.000><c> infrastructure</c><00:05:29.000><c> and</c><00:05:29.280><c> the</c><00:05:29.400><c> CL</c><00:05:29.960><c> in</c><00:05:30.080><c> there</c>

00:05:30.189 --> 00:05:30.199 align:start position:0%
our infrastructure and the CL in there
 

00:05:30.199 --> 00:05:32.550 align:start position:0%
our infrastructure and the CL in there
and<00:05:30.360><c> hybrid</c><00:05:30.680><c> Cloud</c><00:05:31.000><c> can</c><00:05:31.160><c> lift</c><00:05:31.400><c> side</c><00:05:31.600><c> by</c><00:05:31.840><c> side</c>

00:05:32.550 --> 00:05:32.560 align:start position:0%
and hybrid Cloud can lift side by side
 

00:05:32.560 --> 00:05:34.469 align:start position:0%
and hybrid Cloud can lift side by side
for<00:05:32.759><c> example</c><00:05:33.160><c> here</c><00:05:33.400><c> I</c><00:05:33.520><c> have</c><00:05:33.720><c> one</c><00:05:33.960><c> cluster</c><00:05:34.319><c> on</c>

00:05:34.469 --> 00:05:34.479 align:start position:0%
for example here I have one cluster on
 

00:05:34.479 --> 00:05:36.830 align:start position:0%
for example here I have one cluster on
AWS<00:05:35.120><c> one</c><00:05:35.240><c> on</c><00:05:35.440><c> Azure</c><00:05:36.080><c> these</c><00:05:36.199><c> are</c><00:05:36.360><c> running</c><00:05:36.680><c> in</c>

00:05:36.830 --> 00:05:36.840 align:start position:0%
AWS one on Azure these are running in
 

00:05:36.840 --> 00:05:39.550 align:start position:0%
AWS one on Azure these are running in
the<00:05:36.960><c> quadrant</c><00:05:37.319><c> manage</c><00:05:37.680><c> Cloud</c><00:05:38.199><c> regions</c><00:05:39.199><c> and</c>

00:05:39.550 --> 00:05:39.560 align:start position:0%
the quadrant manage Cloud regions and
 

00:05:39.560 --> 00:05:41.629 align:start position:0%
the quadrant manage Cloud regions and
you<00:05:39.720><c> just</c><00:05:39.919><c> get</c><00:05:40.039><c> a</c><00:05:40.160><c> URL</c><00:05:40.639><c> there</c><00:05:40.960><c> but</c><00:05:41.120><c> I</c><00:05:41.240><c> also</c><00:05:41.479><c> have</c>

00:05:41.629 --> 00:05:41.639 align:start position:0%
you just get a URL there but I also have
 

00:05:41.639 --> 00:05:43.309 align:start position:0%
you just get a URL there but I also have
one<00:05:41.880><c> cluster</c><00:05:42.240><c> running</c><00:05:42.479><c> on</c><00:05:42.600><c> my</c><00:05:42.759><c> private</c><00:05:43.039><c> digit</c>

00:05:43.309 --> 00:05:43.319 align:start position:0%
one cluster running on my private digit
 

00:05:43.319 --> 00:05:45.390 align:start position:0%
one cluster running on my private digit
ocean<00:05:43.639><c> account</c><00:05:44.199><c> and</c><00:05:44.319><c> you</c><00:05:44.400><c> can</c><00:05:44.600><c> mix</c><00:05:44.800><c> and</c><00:05:44.960><c> match</c>

00:05:45.390 --> 00:05:45.400 align:start position:0%
ocean account and you can mix and match
 

00:05:45.400 --> 00:05:46.870 align:start position:0%
ocean account and you can mix and match
however<00:05:45.560><c> you</c><00:05:45.680><c> want</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
however you want
 

00:05:46.880 --> 00:05:49.629 align:start position:0%
however you want
this<00:05:47.880><c> the</c><00:05:48.000><c> database</c><00:05:48.560><c> by</c><00:05:48.680><c> the</c><00:05:48.800><c> way</c><00:05:49.039><c> is</c><00:05:49.280><c> now</c>

00:05:49.629 --> 00:05:49.639 align:start position:0%
this the database by the way is now
 

00:05:49.639 --> 00:05:52.230 align:start position:0%
this the database by the way is now
starting<00:05:50.080><c> up</c><00:05:50.440><c> and</c><00:05:50.600><c> we</c><00:05:50.759><c> can</c><00:05:51.240><c> go</c><00:05:51.520><c> back</c><00:05:51.800><c> to</c><00:05:52.000><c> our</c>

00:05:52.230 --> 00:05:52.240 align:start position:0%
starting up and we can go back to our
 

00:05:52.240 --> 00:05:53.950 align:start position:0%
starting up and we can go back to our
terminal<00:05:52.800><c> here</c><00:05:52.960><c> and</c><00:05:53.080><c> you</c><00:05:53.240><c> see</c><00:05:53.520><c> there</c><00:05:53.720><c> is</c><00:05:53.840><c> a</c>

00:05:53.950 --> 00:05:53.960 align:start position:0%
terminal here and you see there is a
 

00:05:53.960 --> 00:05:56.629 align:start position:0%
terminal here and you see there is a
quadrant<00:05:54.400><c> po</c><00:05:55.080><c> that</c><00:05:55.199><c> is</c><00:05:55.479><c> in</c><00:05:55.759><c> here</c><00:05:56.080><c> and</c><00:05:56.280><c> up</c><00:05:56.400><c> and</c>

00:05:56.629 --> 00:05:56.639 align:start position:0%
quadrant po that is in here and up and
 

00:05:56.639 --> 00:05:59.430 align:start position:0%
quadrant po that is in here and up and
running<00:05:57.639><c> and</c><00:05:58.000><c> it</c><00:05:58.160><c> also</c><00:05:58.400><c> now</c><00:05:58.639><c> became</c><00:05:58.960><c> healthy</c>

00:05:59.430 --> 00:05:59.440 align:start position:0%
running and it also now became healthy
 

00:05:59.440 --> 00:06:01.510 align:start position:0%
running and it also now became healthy
in<00:05:59.600><c> insert</c><00:05:59.880><c> of</c><00:06:00.000><c> the</c><00:06:00.080><c> quadrant</c><00:06:00.400><c> Cloud</c><00:06:00.680><c> console</c>

00:06:01.510 --> 00:06:01.520 align:start position:0%
in insert of the quadrant Cloud console
 

00:06:01.520 --> 00:06:04.350 align:start position:0%
in insert of the quadrant Cloud console
and<00:06:01.680><c> now</c><00:06:01.800><c> we</c><00:06:02.000><c> have</c><00:06:02.199><c> the</c><00:06:02.360><c> same</c><00:06:02.720><c> features</c><00:06:03.720><c> to</c>

00:06:04.350 --> 00:06:04.360 align:start position:0%
and now we have the same features to
 

00:06:04.360 --> 00:06:07.150 align:start position:0%
and now we have the same features to
look<00:06:04.560><c> at</c><00:06:04.720><c> the</c><00:06:04.840><c> metrics</c><00:06:05.440><c> of</c><00:06:05.600><c> the</c><00:06:06.039><c> database</c><00:06:07.039><c> um</c>

00:06:07.150 --> 00:06:07.160 align:start position:0%
look at the metrics of the database um
 

00:06:07.160 --> 00:06:09.710 align:start position:0%
look at the metrics of the database um
to<00:06:07.319><c> create</c><00:06:07.680><c> backups</c><00:06:08.160><c> to</c><00:06:08.319><c> restore</c><00:06:08.759><c> from</c><00:06:09.039><c> them</c>

00:06:09.710 --> 00:06:09.720 align:start position:0%
to create backups to restore from them
 

00:06:09.720 --> 00:06:11.990 align:start position:0%
to create backups to restore from them
as<00:06:09.840><c> you</c><00:06:09.960><c> would</c><00:06:10.639><c> imagine</c><00:06:11.440><c> inside</c><00:06:11.759><c> of</c><00:06:11.880><c> the</c>

00:06:11.990 --> 00:06:12.000 align:start position:0%
as you would imagine inside of the
 

00:06:12.000 --> 00:06:15.390 align:start position:0%
as you would imagine inside of the
manage<00:06:12.680><c> Cloud</c><00:06:13.680><c> you</c><00:06:13.800><c> can</c><00:06:14.039><c> also</c><00:06:14.360><c> easily</c><00:06:14.759><c> upgrade</c>

00:06:15.390 --> 00:06:15.400 align:start position:0%
manage Cloud you can also easily upgrade
 

00:06:15.400 --> 00:06:17.150 align:start position:0%
manage Cloud you can also easily upgrade
the<00:06:15.680><c> database</c><00:06:16.240><c> if</c><00:06:16.360><c> there</c><00:06:16.440><c> is</c><00:06:16.560><c> a</c><00:06:16.680><c> new</c><00:06:16.840><c> version</c>

00:06:17.150 --> 00:06:17.160 align:start position:0%
the database if there is a new version
 

00:06:17.160 --> 00:06:18.430 align:start position:0%
the database if there is a new version
there<00:06:17.280><c> you</c><00:06:17.360><c> can</c><00:06:17.520><c> restart</c><00:06:17.960><c> it</c><00:06:18.120><c> and</c><00:06:18.240><c> you</c><00:06:18.319><c> can</c>

00:06:18.430 --> 00:06:18.440 align:start position:0%
there you can restart it and you can
 

00:06:18.440 --> 00:06:20.790 align:start position:0%
there you can restart it and you can
scale<00:06:18.720><c> it</c><00:06:18.840><c> up</c><00:06:18.960><c> at</c><00:06:19.080><c> any</c><00:06:19.280><c> point</c><00:06:19.440><c> in</c>

00:06:20.790 --> 00:06:20.800 align:start position:0%
scale it up at any point in
 

00:06:20.800 --> 00:06:23.270 align:start position:0%
scale it up at any point in
time<00:06:21.800><c> thanks</c><00:06:22.000><c> for</c><00:06:22.160><c> listening</c><00:06:22.800><c> and</c><00:06:22.919><c> I</c><00:06:23.039><c> hope</c><00:06:23.160><c> I</c>

00:06:23.270 --> 00:06:23.280 align:start position:0%
time thanks for listening and I hope I
 

00:06:23.280 --> 00:06:24.990 align:start position:0%
time thanks for listening and I hope I
got<00:06:23.400><c> you</c><00:06:23.599><c> interested</c><00:06:23.919><c> in</c><00:06:24.080><c> hybrid</c><00:06:24.400><c> Cloud</c><00:06:24.919><c> if</c>

00:06:24.990 --> 00:06:25.000 align:start position:0%
got you interested in hybrid Cloud if
 

00:06:25.000 --> 00:06:26.790 align:start position:0%
got you interested in hybrid Cloud if
you<00:06:25.120><c> want</c><00:06:25.280><c> to</c><00:06:25.400><c> know</c><00:06:25.599><c> more</c><00:06:26.120><c> visit</c><00:06:26.360><c> other</c>

00:06:26.790 --> 00:06:26.800 align:start position:0%
you want to know more visit other
 

00:06:26.800 --> 00:06:29.350 align:start position:0%
you want to know more visit other
documentation<00:06:27.800><c> visit</c><00:06:28.080><c> our</c><00:06:28.560><c> landing</c><00:06:29.000><c> pages</c>

00:06:29.350 --> 00:06:29.360 align:start position:0%
documentation visit our landing pages
 

00:06:29.360 --> 00:06:31.670 align:start position:0%
documentation visit our landing pages
and<00:06:29.560><c> and</c><00:06:29.680><c> get</c><00:06:29.840><c> started</c><00:06:30.160><c> today</c><00:06:31.160><c> thank</c><00:06:31.319><c> you</c><00:06:31.479><c> very</c>

00:06:31.670 --> 00:06:31.680 align:start position:0%
and and get started today thank you very
 

00:06:31.680 --> 00:06:34.680 align:start position:0%
and and get started today thank you very
much

