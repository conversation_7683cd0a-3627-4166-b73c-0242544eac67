WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:03.530 align:start position:0%
 
guys<00:00:00.149><c> what's</c><00:00:00.450><c> up</c><00:00:00.599><c> so</c><00:00:00.960><c> in</c><00:00:02.210><c> since</c><00:00:03.210><c> the</c><00:00:03.360><c> last</c>

00:00:03.530 --> 00:00:03.540 align:start position:0%
guys what's up so in since the last
 

00:00:03.540 --> 00:00:05.809 align:start position:0%
guys what's up so in since the last
video<00:00:03.899><c> I</c><00:00:03.929><c> went</c><00:00:04.319><c> ahead</c><00:00:04.410><c> and</c><00:00:04.740><c> did</c><00:00:05.250><c> some</c><00:00:05.460><c> research</c>

00:00:05.809 --> 00:00:05.819 align:start position:0%
video I went ahead and did some research
 

00:00:05.819 --> 00:00:07.909 align:start position:0%
video I went ahead and did some research
on<00:00:06.060><c> what</c><00:00:06.720><c> was</c><00:00:06.870><c> happening</c><00:00:07.290><c> with</c><00:00:07.410><c> the</c><00:00:07.529><c> repo</c><00:00:07.890><c> and</c>

00:00:07.909 --> 00:00:07.919 align:start position:0%
on what was happening with the repo and
 

00:00:07.919 --> 00:00:09.680 align:start position:0%
on what was happening with the repo and
here's<00:00:08.280><c> this</c><00:00:08.370><c> quick</c><00:00:08.730><c> explanation</c><00:00:09.059><c> of</c><00:00:09.450><c> what's</c>

00:00:09.680 --> 00:00:09.690 align:start position:0%
here's this quick explanation of what's
 

00:00:09.690 --> 00:00:10.790 align:start position:0%
here's this quick explanation of what's
happening<00:00:10.139><c> when</c><00:00:10.260><c> there's</c><00:00:10.410><c> a</c><00:00:10.500><c> merge</c><00:00:10.769><c> conflict</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
happening when there's a merge conflict
 

00:00:10.800 --> 00:00:13.879 align:start position:0%
happening when there's a merge conflict
on<00:00:11.610><c> git</c><00:00:11.969><c> well</c><00:00:12.870><c> as</c><00:00:13.080><c> you</c><00:00:13.290><c> can</c><00:00:13.440><c> see</c><00:00:13.500><c> there's</c><00:00:13.799><c> a</c>

00:00:13.879 --> 00:00:13.889 align:start position:0%
on git well as you can see there's a
 

00:00:13.889 --> 00:00:15.499 align:start position:0%
on git well as you can see there's a
local<00:00:14.099><c> branch</c><00:00:14.490><c> and</c><00:00:14.700><c> there's</c><00:00:14.849><c> the</c><00:00:15.000><c> origin</c>

00:00:15.499 --> 00:00:15.509 align:start position:0%
local branch and there's the origin
 

00:00:15.509 --> 00:00:17.510 align:start position:0%
local branch and there's the origin
branch<00:00:15.780><c> which</c><00:00:15.960><c> is</c><00:00:16.109><c> on</c><00:00:16.260><c> the</c><00:00:16.410><c> website</c><00:00:16.830><c> over</c><00:00:17.039><c> here</c>

00:00:17.510 --> 00:00:17.520 align:start position:0%
branch which is on the website over here
 

00:00:17.520 --> 00:00:20.029 align:start position:0%
branch which is on the website over here
okay<00:00:18.119><c> that's</c><00:00:18.480><c> that's</c><00:00:19.320><c> what's</c><00:00:19.590><c> happening</c><00:00:19.949><c> in</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
okay that's that's what's happening in
 

00:00:20.039 --> 00:00:21.830 align:start position:0%
okay that's that's what's happening in
this<00:00:20.279><c> repo</c><00:00:20.670><c> over</c><00:00:20.970><c> here</c><00:00:21.180><c> but</c><00:00:21.480><c> I've</c><00:00:21.600><c> got</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
this repo over here but I've got
 

00:00:21.840 --> 00:00:23.840 align:start position:0%
this repo over here but I've got
different<00:00:22.320><c> changes</c><00:00:23.010><c> on</c><00:00:23.130><c> my</c><00:00:23.279><c> local</c><00:00:23.519><c> computer</c>

00:00:23.840 --> 00:00:23.850 align:start position:0%
different changes on my local computer
 

00:00:23.850 --> 00:00:27.259 align:start position:0%
different changes on my local computer
now<00:00:24.359><c> there's</c><00:00:25.050><c> commit</c><00:00:25.500><c> a</c><00:00:25.650><c> I'll</c><00:00:26.310><c> say</c><00:00:26.519><c> I'm</c><00:00:26.760><c> the</c>

00:00:27.259 --> 00:00:27.269 align:start position:0%
now there's commit a I'll say I'm the
 

00:00:27.269 --> 00:00:29.330 align:start position:0%
now there's commit a I'll say I'm the
repo<00:00:27.449><c> we</c><00:00:27.750><c> have</c><00:00:27.840><c> commit</c><00:00:28.109><c> a</c><00:00:28.260><c> commit</c><00:00:28.680><c> B</c><00:00:28.890><c> and</c><00:00:29.189><c> then</c>

00:00:29.330 --> 00:00:29.340 align:start position:0%
repo we have commit a commit B and then
 

00:00:29.340 --> 00:00:31.820 align:start position:0%
repo we have commit a commit B and then
commit<00:00:29.789><c> D</c><00:00:30.090><c> this</c><00:00:30.900><c> is</c><00:00:30.960><c> what</c><00:00:31.260><c> happened</c><00:00:31.619><c> there</c><00:00:31.710><c> was</c>

00:00:31.820 --> 00:00:31.830 align:start position:0%
commit D this is what happened there was
 

00:00:31.830 --> 00:00:34.220 align:start position:0%
commit D this is what happened there was
a<00:00:31.859><c> commit</c><00:00:32.309><c> D</c><00:00:32.640><c> over</c><00:00:33.030><c> here</c><00:00:33.239><c> from</c><00:00:33.510><c> another</c><00:00:33.750><c> branch</c>

00:00:34.220 --> 00:00:34.230 align:start position:0%
a commit D over here from another branch
 

00:00:34.230 --> 00:00:35.900 align:start position:0%
a commit D over here from another branch
this<00:00:34.590><c> usually</c><00:00:34.980><c> happens</c><00:00:35.340><c> when</c><00:00:35.489><c> there's</c><00:00:35.670><c> more</c>

00:00:35.900 --> 00:00:35.910 align:start position:0%
this usually happens when there's more
 

00:00:35.910 --> 00:00:38.420 align:start position:0%
this usually happens when there's more
than<00:00:36.059><c> one</c><00:00:36.239><c> developer</c><00:00:36.870><c> on</c><00:00:37.200><c> a</c><00:00:37.620><c> specific</c><00:00:38.219><c> feature</c>

00:00:38.420 --> 00:00:38.430 align:start position:0%
than one developer on a specific feature
 

00:00:38.430 --> 00:00:40.459 align:start position:0%
than one developer on a specific feature
or<00:00:38.640><c> if</c><00:00:38.969><c> it's</c><00:00:39.120><c> one</c><00:00:39.329><c> developer</c><00:00:39.960><c> that</c><00:00:40.140><c> just</c>

00:00:40.459 --> 00:00:40.469 align:start position:0%
or if it's one developer that just
 

00:00:40.469 --> 00:00:42.950 align:start position:0%
or if it's one developer that just
creates<00:00:40.800><c> more</c><00:00:41.100><c> than</c><00:00:41.309><c> one</c><00:00:41.460><c> branch</c><00:00:41.850><c> and</c><00:00:42.239><c> then</c><00:00:42.870><c> he</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
creates more than one branch and then he
 

00:00:42.960 --> 00:00:45.290 align:start position:0%
creates more than one branch and then he
pulls<00:00:43.290><c> stuff</c><00:00:43.649><c> into</c><00:00:43.980><c> the</c><00:00:44.070><c> master</c><00:00:44.520><c> branch</c><00:00:44.760><c> and</c>

00:00:45.290 --> 00:00:45.300 align:start position:0%
pulls stuff into the master branch and
 

00:00:45.300 --> 00:00:48.080 align:start position:0%
pulls stuff into the master branch and
there's<00:00:45.660><c> stuff</c><00:00:45.960><c> that's</c><00:00:46.260><c> in</c><00:00:46.500><c> I</c><00:00:46.980><c> don't</c><00:00:47.670><c> -</c><00:00:47.820><c> there</c>

00:00:48.080 --> 00:00:48.090 align:start position:0%
there's stuff that's in I don't - there
 

00:00:48.090 --> 00:00:51.139 align:start position:0%
there's stuff that's in I don't - there
were<00:00:48.210><c> changes</c><00:00:48.570><c> in</c><00:00:48.870><c> this</c><00:00:49.260><c> online</c><00:00:49.620><c> file</c><00:00:50.520><c> that</c>

00:00:51.139 --> 00:00:51.149 align:start position:0%
were changes in this online file that
 

00:00:51.149 --> 00:00:54.439 align:start position:0%
were changes in this online file that
were<00:00:51.270><c> not</c><00:00:51.480><c> located</c><00:00:52.050><c> in</c><00:00:52.590><c> our</c><00:00:53.129><c> local</c><00:00:53.699><c> repo</c><00:00:53.969><c> okay</c>

00:00:54.439 --> 00:00:54.449 align:start position:0%
were not located in our local repo okay
 

00:00:54.449 --> 00:00:56.270 align:start position:0%
were not located in our local repo okay
and<00:00:55.110><c> that's</c><00:00:55.350><c> what's</c><00:00:55.620><c> happening</c><00:00:55.980><c> over</c><00:00:56.250><c> here</c>

00:00:56.270 --> 00:00:56.280 align:start position:0%
and that's what's happening over here
 

00:00:56.280 --> 00:01:00.439 align:start position:0%
and that's what's happening over here
all<00:00:56.670><c> of</c><00:00:56.699><c> a</c><00:00:56.850><c> sudden</c><00:00:57.590><c> so</c><00:00:58.590><c> what</c><00:00:58.710><c> I</c><00:00:58.739><c> did</c><00:00:58.829><c> is</c><00:00:59.160><c> we</c><00:01:00.000><c> did</c>

00:01:00.439 --> 00:01:00.449 align:start position:0%
all of a sudden so what I did is we did
 

00:01:00.449 --> 00:01:03.260 align:start position:0%
all of a sudden so what I did is we did
a<00:01:00.510><c> git</c><00:01:01.020><c> check</c><00:01:01.350><c> what</c><00:01:02.129><c> essentially</c><00:01:02.730><c> what</c><00:01:03.000><c> we</c><00:01:03.090><c> did</c>

00:01:03.260 --> 00:01:03.270 align:start position:0%
a git check what essentially what we did
 

00:01:03.270 --> 00:01:04.640 align:start position:0%
a git check what essentially what we did
is<00:01:03.359><c> we</c><00:01:03.480><c> just</c><00:01:03.510><c> went</c><00:01:03.809><c> back</c><00:01:03.899><c> into</c><00:01:04.379><c> into</c>

00:01:04.640 --> 00:01:04.650 align:start position:0%
is we just went back into into
 

00:01:04.650 --> 00:01:05.740 align:start position:0%
is we just went back into into
origin/master

00:01:05.740 --> 00:01:05.750 align:start position:0%
origin/master
 

00:01:05.750 --> 00:01:14.530 align:start position:0%
origin/master
and<00:01:06.750><c> once</c><00:01:07.049><c> we</c><00:01:07.200><c> got</c><00:01:07.320><c> the</c><00:01:07.470><c> origin/master</c><00:01:09.500><c> we</c><00:01:10.500><c> did</c>

00:01:14.530 --> 00:01:14.540 align:start position:0%
 
 

00:01:14.540 --> 00:01:16.910 align:start position:0%
 
once<00:01:15.540><c> we</c><00:01:15.659><c> got</c><00:01:15.810><c> into</c><00:01:15.960><c> origin/master</c><00:01:16.350><c> we</c><00:01:16.770><c> did</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
once we got into origin/master we did
 

00:01:16.920 --> 00:01:17.270 align:start position:0%
once we got into origin/master we did
this

00:01:17.270 --> 00:01:17.280 align:start position:0%
this
 

00:01:17.280 --> 00:01:19.940 align:start position:0%
this
git<00:01:17.549><c> merge</c><00:01:17.580><c> adding</c><00:01:18.420><c> s3</c><00:01:18.780><c> image</c><00:01:19.110><c> uploading</c><00:01:19.740><c> so</c>

00:01:19.940 --> 00:01:19.950 align:start position:0%
git merge adding s3 image uploading so
 

00:01:19.950 --> 00:01:21.649 align:start position:0%
git merge adding s3 image uploading so
we<00:01:20.070><c> were</c><00:01:20.189><c> in</c><00:01:20.369><c> origin</c><00:01:20.790><c> master</c><00:01:21.000><c> switch</c><00:01:21.479><c> to</c>

00:01:21.649 --> 00:01:21.659 align:start position:0%
we were in origin master switch to
 

00:01:21.659 --> 00:01:23.690 align:start position:0%
we were in origin master switch to
branch<00:01:21.869><c> master</c><00:01:22.350><c> and</c><00:01:22.560><c> we</c><00:01:23.189><c> pushed</c><00:01:23.430><c> everything</c>

00:01:23.690 --> 00:01:23.700 align:start position:0%
branch master and we pushed everything
 

00:01:23.700 --> 00:01:25.820 align:start position:0%
branch master and we pushed everything
from<00:01:23.970><c> our</c><00:01:24.119><c> from</c><00:01:24.450><c> our</c><00:01:24.570><c> branch</c><00:01:24.900><c> which</c><00:01:25.350><c> was</c><00:01:25.380><c> all</c>

00:01:25.820 --> 00:01:25.830 align:start position:0%
from our from our branch which was all
 

00:01:25.830 --> 00:01:28.940 align:start position:0%
from our from our branch which was all
about<00:01:26.100><c> the</c><00:01:26.250><c> s3</c><00:01:26.909><c> image</c><00:01:27.150><c> uploading</c><00:01:27.750><c> and</c><00:01:27.930><c> once</c><00:01:28.770><c> we</c>

00:01:28.940 --> 00:01:28.950 align:start position:0%
about the s3 image uploading and once we
 

00:01:28.950 --> 00:01:35.600 align:start position:0%
about the s3 image uploading and once we
did<00:01:29.130><c> that</c><00:01:29.189><c> we</c><00:01:32.600><c> see</c><00:01:33.600><c> we</c><00:01:33.840><c> ran</c><00:01:34.049><c> the</c><00:01:34.200><c> server</c><00:01:34.470><c> oh</c><00:01:34.890><c> we</c>

00:01:35.600 --> 00:01:35.610 align:start position:0%
did that we see we ran the server oh we
 

00:01:35.610 --> 00:01:37.249 align:start position:0%
did that we see we ran the server oh we
tried<00:01:35.759><c> to</c><00:01:35.970><c> push</c><00:01:36.119><c> it</c><00:01:36.270><c> to</c><00:01:36.329><c> origin</c><00:01:36.600><c> master</c><00:01:36.930><c> it</c>

00:01:37.249 --> 00:01:37.259 align:start position:0%
tried to push it to origin master it
 

00:01:37.259 --> 00:01:39.830 align:start position:0%
tried to push it to origin master it
didn't<00:01:37.530><c> let</c><00:01:37.650><c> us</c><00:01:37.799><c> it</c><00:01:38.009><c> said</c><00:01:38.250><c> it's</c><00:01:39.090><c> a</c><00:01:39.270><c> do</c><00:01:39.479><c> a</c><00:01:39.509><c> git</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
didn't let us it said it's a do a git
 

00:01:39.840 --> 00:01:42.740 align:start position:0%
didn't let us it said it's a do a git
pull<00:01:40.229><c> before</c><00:01:41.040><c> pushing</c><00:01:41.340><c> again</c><00:01:41.909><c> what's</c><00:01:42.420><c> saying</c>

00:01:42.740 --> 00:01:42.750 align:start position:0%
pull before pushing again what's saying
 

00:01:42.750 --> 00:01:44.870 align:start position:0%
pull before pushing again what's saying
is<00:01:42.990><c> that</c><00:01:43.229><c> he</c><00:01:43.380><c> we're</c><00:01:43.770><c> trying</c><00:01:44.009><c> to</c><00:01:44.100><c> push</c><00:01:44.310><c> a</c><00:01:44.340><c> repo</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
is that he we're trying to push a repo
 

00:01:44.880 --> 00:01:48.469 align:start position:0%
is that he we're trying to push a repo
from<00:01:45.630><c> our</c><00:01:45.750><c> local</c><00:01:45.990><c> computer</c><00:01:46.350><c> to</c><00:01:47.310><c> over</c><00:01:47.820><c> here</c><00:01:48.180><c> but</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
from our local computer to over here but
 

00:01:48.479 --> 00:01:51.230 align:start position:0%
from our local computer to over here but
get<00:01:49.170><c> recognizes</c><00:01:50.159><c> that</c><00:01:50.310><c> there's</c><00:01:50.520><c> some</c><00:01:50.700><c> files</c>

00:01:51.230 --> 00:01:51.240 align:start position:0%
get recognizes that there's some files
 

00:01:51.240 --> 00:01:54.410 align:start position:0%
get recognizes that there's some files
in<00:01:51.659><c> in</c><00:01:52.020><c> this</c><00:01:52.259><c> repo</c><00:01:52.950><c> that</c><00:01:53.220><c> could</c><00:01:53.430><c> get</c><00:01:53.670><c> lost</c><00:01:53.970><c> if</c>

00:01:54.410 --> 00:01:54.420 align:start position:0%
in in this repo that could get lost if
 

00:01:54.420 --> 00:01:57.980 align:start position:0%
in in this repo that could get lost if
we<00:01:54.720><c> if</c><00:01:55.710><c> we</c><00:01:55.950><c> push</c><00:01:56.310><c> from</c><00:01:56.939><c> from</c><00:01:57.299><c> locally</c><00:01:57.780><c> and</c>

00:01:57.980 --> 00:01:57.990 align:start position:0%
we if we push from from locally and
 

00:01:57.990 --> 00:02:00.789 align:start position:0%
we if we push from from locally and
that's<00:01:58.200><c> why</c><00:01:58.409><c> it</c><00:01:58.469><c> says</c><00:01:58.799><c> just</c><00:01:59.610><c> pull</c><00:02:00.030><c> everything</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
that's why it says just pull everything
 

00:02:00.799 --> 00:02:07.520 align:start position:0%
that's why it says just pull everything
from<00:02:01.799><c> this</c><00:02:02.430><c> this</c><00:02:03.119><c> online</c><00:02:03.750><c> repo</c><00:02:04.649><c> and</c><00:02:06.380><c> once</c><00:02:07.380><c> you</c>

00:02:07.520 --> 00:02:07.530 align:start position:0%
from this this online repo and once you
 

00:02:07.530 --> 00:02:10.320 align:start position:0%
from this this online repo and once you
pull<00:02:07.799><c> it</c><00:02:07.979><c> look</c><00:02:08.700><c> at</c><00:02:08.819><c> the</c><00:02:08.940><c> differences</c><00:02:09.450><c> between</c>

00:02:10.320 --> 00:02:10.330 align:start position:0%
pull it look at the differences between
 

00:02:10.330 --> 00:02:13.110 align:start position:0%
pull it look at the differences between
this<00:02:11.170><c> online</c><00:02:11.440><c> repo</c><00:02:12.220><c> that</c><00:02:12.460><c> we</c><00:02:12.580><c> look</c><00:02:12.760><c> that</c><00:02:13.030><c> we</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
this online repo that we look that we
 

00:02:13.120 --> 00:02:16.230 align:start position:0%
this online repo that we look that we
see<00:02:13.150><c> here</c><00:02:13.660><c> and</c><00:02:13.840><c> your</c><00:02:14.050><c> local</c><00:02:14.470><c> repo</c><00:02:14.860><c> and</c><00:02:15.180><c> try</c><00:02:16.180><c> to</c>

00:02:16.230 --> 00:02:16.240 align:start position:0%
see here and your local repo and try to
 

00:02:16.240 --> 00:02:18.930 align:start position:0%
see here and your local repo and try to
resolve<00:02:16.810><c> the</c><00:02:17.050><c> differences</c><00:02:17.260><c> and</c><00:02:17.800><c> that's</c><00:02:17.920><c> why</c><00:02:18.190><c> I</c>

00:02:18.930 --> 00:02:18.940 align:start position:0%
resolve the differences and that's why I
 

00:02:18.940 --> 00:02:23.580 align:start position:0%
resolve the differences and that's why I
went<00:02:19.870><c> ahead</c><00:02:19.960><c> and</c><00:02:20.320><c> I</c><00:02:20.380><c> did</c><00:02:20.410><c> a</c><00:02:20.620><c> search</c><00:02:21.100><c> for</c><00:02:22.590><c> this</c>

00:02:23.580 --> 00:02:23.590 align:start position:0%
went ahead and I did a search for this
 

00:02:23.590 --> 00:02:26.550 align:start position:0%
went ahead and I did a search for this
word<00:02:23.910><c> /head</c><00:02:24.940><c> anywhere</c><00:02:25.510><c> within</c><00:02:25.720><c> my</c><00:02:26.020><c> repo</c><00:02:26.410><c> and</c>

00:02:26.550 --> 00:02:26.560 align:start position:0%
word /head anywhere within my repo and
 

00:02:26.560 --> 00:02:30.060 align:start position:0%
word /head anywhere within my repo and
the<00:02:27.100><c> moment</c><00:02:27.430><c> that</c><00:02:27.460><c> I</c><00:02:27.580><c> did</c><00:02:27.880><c> a</c><00:02:27.910><c> get</c><00:02:28.270><c> pull</c><00:02:28.750><c> as</c><00:02:29.110><c> you</c>

00:02:30.060 --> 00:02:30.070 align:start position:0%
the moment that I did a get pull as you
 

00:02:30.070 --> 00:02:33.320 align:start position:0%
the moment that I did a get pull as you
could<00:02:30.220><c> see</c><00:02:30.570><c> get</c><00:02:31.570><c> push</c><00:02:31.990><c> origin</c><00:02:32.260><c> master</c><00:02:32.530><c> no</c>

00:02:33.320 --> 00:02:33.330 align:start position:0%
could see get push origin master no
 

00:02:33.330 --> 00:02:35.550 align:start position:0%
could see get push origin master no
moment<00:02:34.330><c> that</c><00:02:34.420><c> I</c><00:02:34.450><c> did</c><00:02:34.660><c> to</c><00:02:34.750><c> get</c><00:02:34.870><c> pull</c><00:02:35.290><c> origin</c>

00:02:35.550 --> 00:02:35.560 align:start position:0%
moment that I did to get pull origin
 

00:02:35.560 --> 00:02:39.390 align:start position:0%
moment that I did to get pull origin
master<00:02:36.330><c> get</c><00:02:37.330><c> did</c><00:02:37.870><c> this</c><00:02:38.200><c> it</c><00:02:38.770><c> went</c><00:02:39.010><c> ahead</c><00:02:39.100><c> and</c>

00:02:39.390 --> 00:02:39.400 align:start position:0%
master get did this it went ahead and
 

00:02:39.400 --> 00:02:43.440 align:start position:0%
master get did this it went ahead and
added<00:02:39.580><c> these</c><00:02:40.270><c> sections</c><00:02:40.900><c> slash</c><00:02:41.890><c> head</c><00:02:42.280><c> right</c><00:02:43.270><c> in</c>

00:02:43.440 --> 00:02:43.450 align:start position:0%
added these sections slash head right in
 

00:02:43.450 --> 00:02:45.840 align:start position:0%
added these sections slash head right in
these<00:02:43.690><c> files</c><00:02:44.050><c> over</c><00:02:44.650><c> here</c><00:02:44.980><c> so</c><00:02:45.460><c> that's</c><00:02:45.640><c> a</c><00:02:45.700><c> good</c>

00:02:45.840 --> 00:02:45.850 align:start position:0%
these files over here so that's a good
 

00:02:45.850 --> 00:02:47.760 align:start position:0%
these files over here so that's a good
way<00:02:46.000><c> to</c><00:02:46.060><c> see</c><00:02:46.420><c> what</c><00:02:46.450><c> needs</c><00:02:46.870><c> to</c><00:02:46.960><c> be</c><00:02:47.170><c> resolved</c>

00:02:47.760 --> 00:02:47.770 align:start position:0%
way to see what needs to be resolved
 

00:02:47.770 --> 00:02:50.280 align:start position:0%
way to see what needs to be resolved
before<00:02:47.950><c> you</c><00:02:48.280><c> can</c><00:02:48.459><c> push</c><00:02:48.760><c> push</c><00:02:49.720><c> back</c><00:02:49.990><c> to</c><00:02:50.170><c> the</c>

00:02:50.280 --> 00:02:50.290 align:start position:0%
before you can push push back to the
 

00:02:50.290 --> 00:02:51.780 align:start position:0%
before you can push push back to the
main<00:02:50.470><c> repo</c><00:02:50.890><c> so</c><00:02:50.950><c> that's</c><00:02:51.160><c> what</c><00:02:51.310><c> we're</c><00:02:51.430><c> gonna</c><00:02:51.520><c> do</c>

00:02:51.780 --> 00:02:51.790 align:start position:0%
main repo so that's what we're gonna do
 

00:02:51.790 --> 00:02:54.600 align:start position:0%
main repo so that's what we're gonna do
now<00:02:52.410><c> and</c><00:02:53.410><c> once</c><00:02:53.709><c> we</c><00:02:53.830><c> finish</c><00:02:54.100><c> making</c><00:02:54.130><c> the</c>

00:02:54.600 --> 00:02:54.610 align:start position:0%
now and once we finish making the
 

00:02:54.610 --> 00:02:56.790 align:start position:0%
now and once we finish making the
differences<00:02:55.150><c> of</c><00:02:55.780><c> what</c><00:02:55.990><c> needs</c><00:02:56.200><c> to</c><00:02:56.290><c> be</c><00:02:56.440><c> result</c>

00:02:56.790 --> 00:02:56.800 align:start position:0%
differences of what needs to be result
 

00:02:56.800 --> 00:02:57.870 align:start position:0%
differences of what needs to be result
we're<00:02:56.950><c> gonna</c><00:02:57.070><c> go</c><00:02:57.280><c> ahead</c><00:02:57.400><c> and</c><00:02:57.520><c> we're</c><00:02:57.730><c> gonna</c>

00:02:57.870 --> 00:02:57.880 align:start position:0%
we're gonna go ahead and we're gonna
 

00:02:57.880 --> 00:03:00.540 align:start position:0%
we're gonna go ahead and we're gonna
push<00:02:58.209><c> again</c><00:02:58.540><c> so</c><00:02:58.959><c> I'll</c><00:02:59.680><c> see</c><00:02:59.980><c> you</c><00:03:00.070><c> in</c><00:03:00.130><c> the</c><00:03:00.310><c> next</c>

00:03:00.540 --> 00:03:00.550 align:start position:0%
push again so I'll see you in the next
 

00:03:00.550 --> 00:03:04.680 align:start position:0%
push again so I'll see you in the next
video<00:03:00.820><c> once</c><00:03:01.120><c> all</c><00:03:01.300><c> the</c><00:03:01.480><c> changes</c><00:03:02.020><c> are</c><00:03:02.140><c> resolved</c>

