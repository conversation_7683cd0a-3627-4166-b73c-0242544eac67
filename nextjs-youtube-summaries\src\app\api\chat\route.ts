import { DatabaseService } from '@/lib/database'
import { ChatRequest, ChatResponse } from '@/types/index'
import { NextRequest, NextResponse } from 'next/server'
import { optimizeContextForChat } from '@/lib/tokenUtils'

interface ContextualChatRequest {
  message: string
  context: {
    videos: Array<{
      title: string
      channel: string
      summary: string
      published: string
      transcript?: string  // Add transcript field
    }>
    topics: string[]
    channels: string[]
  }
  session_id?: string  // Add session ID for conversation memory
}

interface VideoContext {
  title: string
  channel?: string
  summary: string
  published?: string
  transcript?: string  // Add transcript field
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
      // Handle both old format and new contextual format
    let message: string
    let contextVideosFromRequest: VideoContext[] = []
    let topicFilters: string[] = []
    let searchQuery: string
    let sessionId: string | undefined
    
    if (body.context) {
      // New contextual format from ContextualChat component
      const { message: msg, context, session_id }: ContextualChatRequest = body
      message = msg
      contextVideosFromRequest = context.videos || []
      topicFilters = context.topics || []
      searchQuery = msg
      sessionId = session_id    } else {
      // Legacy format
      const { message: msg, topic_filters, search_query, session_id } = body as ChatRequest
      message = msg
      topicFilters = topic_filters || []
      searchQuery = search_query || msg
      sessionId = session_id    }

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    let contextText = ''

    if (contextVideosFromRequest.length > 0) {
      // Use smart context optimization for provided videos
      // Map to ensure all required properties are present
      const contextForOptimization = contextVideosFromRequest.map(video => ({
        title: video.title,
        channel: video.channel || 'Unknown Channel',
        summary: video.summary,
        transcript: video.transcript
      }))
      
      const optimized = optimizeContextForChat(
        contextForOptimization, 
        message,
        '' // TODO: Add conversation history here if available
      )
      
      contextText = optimized.contextText
      
      // Log token usage for monitoring
      console.log(`Context optimization: ${optimized.totalEstimatedTokens} tokens estimated, truncated: ${optimized.truncated}`)
      
      if (optimized.truncated) {
        console.warn('Context was truncated to fit within token limits')
      }
    } else {// Search for relevant videos using vector similarity
      const searchResults = await DatabaseService.searchVideos(
        searchQuery,
        topicFilters,
        5 // Limit context videos for chat
      )
      
      const searchContextVideos = searchResults.map(result => result.video)
      contextText = searchContextVideos.map(video => {
        // Extract summary from llm_response if it's an object with summary, or use summary_text
        let summary = 'No summary available'
        if (video.llm_response && typeof video.llm_response === 'object' && 'summary' in video.llm_response) {
          summary = String(video.llm_response.summary)
        } else if (video.summary_text) {
          summary = video.summary_text
        }
        
        return `Title: ${video.title}\nSummary: ${summary}\nTranscript excerpt: ${video.transcript?.substring(0, 500) || 'No transcript available'}...`
      }).join('\n\n---\n\n')
    }// Step 3: Call the unified Python service
    const pythonServiceUrl = process.env.PYTHON_AI_SERVICE_URL || 'http://localhost:9000'
    const aiResponse = await fetch(`${pythonServiceUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.PYTHON_AI_SERVICE_API_KEY || 'dev-key-123'}`
      },
      body: JSON.stringify({
        message,
        context: contextText,
        system_prompt: `You are an AI assistant that helps users understand YouTube video content. 
        Use the provided video summaries and transcripts to answer questions accurately. 
        If the question cannot be answered from the context, say so clearly.
        Always cite which videos you're referencing in your response.`,
        session_id: sessionId  // Pass session ID for conversation memory
      })
    })

    if (!aiResponse.ok) {
      throw new Error(`Python AI service responded with status: ${aiResponse.status}`)
    }

    const aiData = await aiResponse.json()    // Construct the response according to the ChatResponse interface
    const responsePayload: ChatResponse = {
      response: aiData.response || 'I apologize, but I encountered an issue generating a response.',
      context_videos: [], // Empty for now, as we have simplified context
      session_id: aiData.session_id // Return session ID for conversation continuity
    }

    return NextResponse.json(responsePayload)
  } catch (error) {
    console.error('Error in chat API:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
}
