WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.730 align:start position:0%
 
developers<00:00:00.719><c> Excel</c><00:00:01.140><c> when</c><00:00:01.319><c> they</c><00:00:01.500><c> have</c><00:00:01.560><c> the</c>

00:00:01.730 --> 00:00:01.740 align:start position:0%
developers Excel when they have the
 

00:00:01.740 --> 00:00:03.350 align:start position:0%
developers Excel when they have the
freedom<00:00:01.979><c> to</c><00:00:02.100><c> innovate</c><00:00:02.520><c> rather</c><00:00:02.940><c> than</c><00:00:03.120><c> being</c>

00:00:03.350 --> 00:00:03.360 align:start position:0%
freedom to innovate rather than being
 

00:00:03.360 --> 00:00:05.769 align:start position:0%
freedom to innovate rather than being
burdened<00:00:03.780><c> by</c><00:00:04.020><c> security</c><00:00:04.200><c> alerts</c><00:00:04.799><c> however</c>

00:00:05.769 --> 00:00:05.779 align:start position:0%
burdened by security alerts however
 

00:00:05.779 --> 00:00:08.150 align:start position:0%
burdened by security alerts however
addressing<00:00:06.779><c> security</c><00:00:06.960><c> issues</c><00:00:07.620><c> can</c><00:00:07.919><c> be</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
addressing security issues can be
 

00:00:08.160 --> 00:00:10.730 align:start position:0%
addressing security issues can be
challenging<00:00:08.639><c> while</c><00:00:09.300><c> traditional</c><00:00:09.840><c> SAS</c><00:00:10.200><c> tools</c>

00:00:10.730 --> 00:00:10.740 align:start position:0%
challenging while traditional SAS tools
 

00:00:10.740 --> 00:00:12.950 align:start position:0%
challenging while traditional SAS tools
aim<00:00:11.160><c> to</c><00:00:11.280><c> identify</c><00:00:11.700><c> vulnerabilities</c><00:00:12.360><c> early</c><00:00:12.660><c> on</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
aim to identify vulnerabilities early on
 

00:00:12.960 --> 00:00:16.070 align:start position:0%
aim to identify vulnerabilities early on
they<00:00:13.740><c> often</c><00:00:14.040><c> introduce</c><00:00:14.400><c> new</c><00:00:14.759><c> problems</c><00:00:15.299><c> like</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
they often introduce new problems like
 

00:00:16.080 --> 00:00:18.590 align:start position:0%
they often introduce new problems like
false<00:00:16.500><c> positives</c><00:00:16.980><c> and</c><00:00:17.580><c> struggles</c><00:00:18.060><c> to</c><00:00:18.300><c> keep</c><00:00:18.420><c> up</c>

00:00:18.590 --> 00:00:18.600 align:start position:0%
false positives and struggles to keep up
 

00:00:18.600 --> 00:00:21.050 align:start position:0%
false positives and struggles to keep up
with<00:00:18.779><c> evolving</c><00:00:19.260><c> Technologies</c><00:00:20.279><c> this</c><00:00:20.699><c> leads</c><00:00:20.939><c> to</c>

00:00:21.050 --> 00:00:21.060 align:start position:0%
with evolving Technologies this leads to
 

00:00:21.060 --> 00:00:22.849 align:start position:0%
with evolving Technologies this leads to
frustration<00:00:21.480><c> and</c><00:00:21.840><c> wasted</c><00:00:22.260><c> time</c><00:00:22.439><c> for</c><00:00:22.680><c> both</c>

00:00:22.849 --> 00:00:22.859 align:start position:0%
frustration and wasted time for both
 

00:00:22.859 --> 00:00:24.950 align:start position:0%
frustration and wasted time for both
security<00:00:23.160><c> teams</c><00:00:23.699><c> and</c><00:00:23.939><c> Developers</c>

00:00:24.950 --> 00:00:24.960 align:start position:0%
security teams and Developers
 

00:00:24.960 --> 00:00:27.109 align:start position:0%
security teams and Developers
GitHub<00:00:25.680><c> code</c><00:00:25.859><c> scanning</c><00:00:26.279><c> comes</c><00:00:26.580><c> to</c><00:00:26.699><c> the</c><00:00:26.820><c> Rescue</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
GitHub code scanning comes to the Rescue
 

00:00:27.119 --> 00:00:29.269 align:start position:0%
GitHub code scanning comes to the Rescue
It<00:00:27.779><c> enables</c><00:00:28.140><c> effortless</c><00:00:28.680><c> vulnerability</c>

00:00:29.269 --> 00:00:29.279 align:start position:0%
It enables effortless vulnerability
 

00:00:29.279 --> 00:00:31.630 align:start position:0%
It enables effortless vulnerability
detection<00:00:29.699><c> and</c><00:00:30.000><c> prevention</c><00:00:30.420><c> as</c><00:00:31.019><c> you</c><00:00:31.260><c> code</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
detection and prevention as you code
 

00:00:31.640 --> 00:00:33.650 align:start position:0%
detection and prevention as you code
seamlessly<00:00:32.640><c> integrating</c><00:00:33.059><c> into</c><00:00:33.360><c> your</c>

00:00:33.650 --> 00:00:33.660 align:start position:0%
seamlessly integrating into your
 

00:00:33.660 --> 00:00:36.049 align:start position:0%
seamlessly integrating into your
workflow<00:00:34.140><c> with</c><00:00:34.920><c> customizable</c><00:00:35.640><c> security</c>

00:00:36.049 --> 00:00:36.059 align:start position:0%
workflow with customizable security
 

00:00:36.059 --> 00:00:38.930 align:start position:0%
workflow with customizable security
policies<00:00:36.840><c> integration</c><00:00:37.620><c> with</c><00:00:37.980><c> popular</c><00:00:38.160><c> CI</c><00:00:38.700><c> CD</c>

00:00:38.930 --> 00:00:38.940 align:start position:0%
policies integration with popular CI CD
 

00:00:38.940 --> 00:00:41.869 align:start position:0%
policies integration with popular CI CD
pipelines<00:00:39.420><c> and</c><00:00:40.140><c> open</c><00:00:40.320><c> source</c><00:00:40.800><c> tools</c><00:00:41.160><c> and</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
pipelines and open source tools and
 

00:00:41.879 --> 00:00:43.970 align:start position:0%
pipelines and open source tools and
results<00:00:42.059><c> displayed</c><00:00:42.840><c> input</c><00:00:43.260><c> requests</c><00:00:43.680><c> for</c>

00:00:43.970 --> 00:00:43.980 align:start position:0%
results displayed input requests for
 

00:00:43.980 --> 00:00:46.670 align:start position:0%
results displayed input requests for
easy<00:00:44.219><c> collaboration</c><00:00:45.140><c> prevention</c><00:00:46.140><c> and</c>

00:00:46.670 --> 00:00:46.680 align:start position:0%
easy collaboration prevention and
 

00:00:46.680 --> 00:00:49.430 align:start position:0%
easy collaboration prevention and
Remediation<00:00:47.280><c> code</c><00:00:48.120><c> scanning</c><00:00:48.600><c> simplifies</c><00:00:49.200><c> the</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
Remediation code scanning simplifies the
 

00:00:49.440 --> 00:00:52.250 align:start position:0%
Remediation code scanning simplifies the
process<00:00:49.739><c> it</c><00:00:50.640><c> offers</c><00:00:50.940><c> clear</c><00:00:51.300><c> feedback</c><00:00:51.899><c> and</c>

00:00:52.250 --> 00:00:52.260 align:start position:0%
process it offers clear feedback and
 

00:00:52.260 --> 00:00:54.170 align:start position:0%
process it offers clear feedback and
actionable<00:00:52.739><c> guidance</c><00:00:53.219><c> on</c><00:00:53.520><c> vulnerabilities</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
actionable guidance on vulnerabilities
 

00:00:54.180 --> 00:00:56.510 align:start position:0%
actionable guidance on vulnerabilities
allowing<00:00:55.140><c> you</c><00:00:55.260><c> to</c><00:00:55.500><c> try</c><00:00:55.680><c> out</c><00:00:55.980><c> and</c><00:00:56.340><c> address</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
allowing you to try out and address
 

00:00:56.520 --> 00:00:59.090 align:start position:0%
allowing you to try out and address
issues<00:00:57.120><c> directly</c><00:00:57.660><c> within</c><00:00:58.140><c> your</c><00:00:58.320><c> code</c>

00:00:59.090 --> 00:00:59.100 align:start position:0%
issues directly within your code
 

00:00:59.100 --> 00:01:01.729 align:start position:0%
issues directly within your code
code<00:00:59.579><c> scanning</c><00:01:00.059><c> is</c><00:01:00.360><c> powered</c><00:01:00.780><c> by</c><00:01:00.960><c> the</c><00:01:01.140><c> code</c><00:01:01.260><c> ql</c>

00:01:01.729 --> 00:01:01.739 align:start position:0%
code scanning is powered by the code ql
 

00:01:01.739 --> 00:01:03.349 align:start position:0%
code scanning is powered by the code ql
static<00:01:02.160><c> analysis</c><00:01:02.579><c> engine</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
static analysis engine
 

00:01:03.359 --> 00:01:06.050 align:start position:0%
static analysis engine
code<00:01:03.780><c> URL</c><00:01:04.320><c> lets</c><00:01:04.619><c> you</c><00:01:04.680><c> query</c><00:01:04.979><c> code</c><00:01:05.159><c> AS</c><00:01:05.640><c> do</c><00:01:05.880><c> it</c>

00:01:06.050 --> 00:01:06.060 align:start position:0%
code URL lets you query code AS do it
 

00:01:06.060 --> 00:01:08.510 align:start position:0%
code URL lets you query code AS do it
with<00:01:06.240><c> data</c><00:01:06.720><c> instead</c><00:01:07.439><c> of</c><00:01:07.560><c> relying</c><00:01:07.920><c> on</c><00:01:08.159><c> pattern</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
with data instead of relying on pattern
 

00:01:08.520 --> 00:01:09.770 align:start position:0%
with data instead of relying on pattern
matching

00:01:09.770 --> 00:01:09.780 align:start position:0%
matching
 

00:01:09.780 --> 00:01:12.050 align:start position:0%
matching
GitHub<00:01:10.560><c> steam</c><00:01:10.860><c> of</c><00:01:10.979><c> security</c><00:01:11.220><c> researchers</c><00:01:11.880><c> and</c>

00:01:12.050 --> 00:01:12.060 align:start position:0%
GitHub steam of security researchers and
 

00:01:12.060 --> 00:01:15.109 align:start position:0%
GitHub steam of security researchers and
language<00:01:12.299><c> experts</c><00:01:12.900><c> have</c><00:01:13.500><c> developed</c><00:01:13.920><c> over</c><00:01:14.220><c> 2</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
language experts have developed over 2
 

00:01:15.119 --> 00:01:17.090 align:start position:0%
language experts have developed over 2
000<00:01:15.240><c> pre-built</c><00:01:15.900><c> queries</c><00:01:16.200><c> which</c><00:01:16.860><c> are</c>

00:01:17.090 --> 00:01:17.100 align:start position:0%
000 pre-built queries which are
 

00:01:17.100 --> 00:01:18.830 align:start position:0%
000 pre-built queries which are
continuously<00:01:17.640><c> updated</c><00:01:18.000><c> to</c><00:01:18.420><c> enhanced</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
continuously updated to enhanced
 

00:01:18.840 --> 00:01:21.230 align:start position:0%
continuously updated to enhanced
analysis<00:01:19.439><c> and</c><00:01:19.920><c> minimize</c><00:01:20.400><c> false</c><00:01:20.880><c> positive</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
analysis and minimize false positive
 

00:01:21.240 --> 00:01:22.370 align:start position:0%
analysis and minimize false positive
results

00:01:22.370 --> 00:01:22.380 align:start position:0%
results
 

00:01:22.380 --> 00:01:24.590 align:start position:0%
results
you<00:01:23.040><c> can</c><00:01:23.159><c> even</c><00:01:23.340><c> write</c><00:01:23.580><c> your</c><00:01:23.820><c> own</c><00:01:24.000><c> code</c><00:01:24.240><c> ql</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
you can even write your own code ql
 

00:01:24.600 --> 00:01:27.170 align:start position:0%
you can even write your own code ql
queries<00:01:25.080><c> and</c><00:01:25.680><c> your</c><00:01:25.860><c> own</c><00:01:26.040><c> query</c><00:01:26.400><c> pack</c><00:01:26.700><c> best</c>

00:01:27.170 --> 00:01:27.180 align:start position:0%
queries and your own query pack best
 

00:01:27.180 --> 00:01:28.910 align:start position:0%
queries and your own query pack best
suited<00:01:27.540><c> to</c><00:01:27.720><c> your</c><00:01:27.900><c> organization</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
suited to your organization
 

00:01:28.920 --> 00:01:31.910 align:start position:0%
suited to your organization
or<00:01:29.580><c> even</c><00:01:30.000><c> leverage</c><00:01:30.420><c> queries</c><00:01:30.960><c> created</c><00:01:31.500><c> by</c><00:01:31.799><c> a</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
or even leverage queries created by a
 

00:01:31.920 --> 00:01:33.710 align:start position:0%
or even leverage queries created by a
vast<00:01:32.220><c> community</c><00:01:32.520><c> of</c><00:01:33.000><c> leading</c><00:01:33.479><c> security</c>

00:01:33.710 --> 00:01:33.720 align:start position:0%
vast community of leading security
 

00:01:33.720 --> 00:01:36.170 align:start position:0%
vast community of leading security
researchers<00:01:34.500><c> at</c><00:01:34.860><c> companies</c><00:01:35.280><c> like</c><00:01:35.579><c> Microsoft</c>

00:01:36.170 --> 00:01:36.180 align:start position:0%
researchers at companies like Microsoft
 

00:01:36.180 --> 00:01:39.590 align:start position:0%
researchers at companies like Microsoft
Google<00:01:36.900><c> Uber</c><00:01:37.680><c> and</c><00:01:37.979><c> others</c><00:01:38.400><c> getting</c><00:01:39.119><c> started</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
Google Uber and others getting started
 

00:01:39.600 --> 00:01:42.289 align:start position:0%
Google Uber and others getting started
with<00:01:39.840><c> code</c><00:01:40.079><c> ql</c><00:01:40.560><c> is</c><00:01:40.799><c> simple</c><00:01:41.040><c> simply</c><00:01:42.000><c> navigate</c>

00:01:42.289 --> 00:01:42.299 align:start position:0%
with code ql is simple simply navigate
 

00:01:42.299 --> 00:01:44.690 align:start position:0%
with code ql is simple simply navigate
to<00:01:42.780><c> the</c><00:01:42.960><c> main</c><00:01:43.140><c> page</c><00:01:43.380><c> of</c><00:01:43.619><c> your</c><00:01:43.740><c> repository</c><00:01:44.340><c> on</c>

00:01:44.690 --> 00:01:44.700 align:start position:0%
to the main page of your repository on
 

00:01:44.700 --> 00:01:46.010 align:start position:0%
to the main page of your repository on
github.com

00:01:46.010 --> 00:01:46.020 align:start position:0%
github.com
 

00:01:46.020 --> 00:01:48.310 align:start position:0%
github.com
under<00:01:46.439><c> the</c><00:01:46.680><c> repository</c><00:01:47.220><c> name</c><00:01:47.460><c> click</c><00:01:48.060><c> settings</c>

00:01:48.310 --> 00:01:48.320 align:start position:0%
under the repository name click settings
 

00:01:48.320 --> 00:01:51.310 align:start position:0%
under the repository name click settings
in<00:01:49.320><c> the</c><00:01:49.439><c> security</c><00:01:49.619><c> section</c><00:01:50.220><c> of</c><00:01:50.640><c> the</c><00:01:50.759><c> sidebar</c>

00:01:51.310 --> 00:01:51.320 align:start position:0%
in the security section of the sidebar
 

00:01:51.320 --> 00:01:55.069 align:start position:0%
in the security section of the sidebar
click<00:01:52.320><c> code</c><00:01:52.560><c> security</c><00:01:52.860><c> and</c><00:01:53.399><c> Analysis</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
click code security and Analysis
 

00:01:55.079 --> 00:01:57.289 align:start position:0%
click code security and Analysis
in<00:01:55.560><c> the</c><00:01:55.740><c> course</c><00:01:55.799><c> scanning</c><00:01:56.340><c> section</c><00:01:56.700><c> select</c>

00:01:57.289 --> 00:01:57.299 align:start position:0%
in the course scanning section select
 

00:01:57.299 --> 00:01:59.990 align:start position:0%
in the course scanning section select
setup<00:01:57.720><c> then</c><00:01:58.020><c> click</c><00:01:58.380><c> default</c><00:01:58.799><c> review</c><00:01:59.700><c> the</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
setup then click default review the
 

00:02:00.000 --> 00:02:01.910 align:start position:0%
setup then click default review the
settings<00:02:00.240><c> for</c><00:02:00.659><c> the</c><00:02:00.899><c> default</c><00:02:01.140><c> setup</c><00:02:01.560><c> on</c><00:02:01.740><c> your</c>

00:02:01.910 --> 00:02:01.920 align:start position:0%
settings for the default setup on your
 

00:02:01.920 --> 00:02:04.910 align:start position:0%
settings for the default setup on your
repository<00:02:02.460><c> then</c><00:02:02.880><c> click</c><00:02:03.240><c> enable</c><00:02:03.540><c> code</c><00:02:03.840><c> ql</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
repository then click enable code ql
 

00:02:04.920 --> 00:02:06.770 align:start position:0%
repository then click enable code ql
check<00:02:05.460><c> out</c><00:02:05.579><c> our</c><00:02:05.759><c> documentation</c><00:02:06.299><c> to</c><00:02:06.600><c> learn</c>

00:02:06.770 --> 00:02:06.780 align:start position:0%
check out our documentation to learn
 

00:02:06.780 --> 00:02:09.079 align:start position:0%
check out our documentation to learn
more

