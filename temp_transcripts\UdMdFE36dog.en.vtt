WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.470 align:start position:0%
 
Claude<00:00:00.440><c> <PERSON><PERSON></c><00:00:01.120><c> is</c><00:00:01.319><c> one</c><00:00:01.480><c> of</c><00:00:01.599><c> the</c><00:00:01.839><c> fastest</c><00:00:02.280><c> and</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
<PERSON> is one of the fastest and
 

00:00:02.480 --> 00:00:04.510 align:start position:0%
<PERSON> is one of the fastest and
most<00:00:02.720><c> affordable</c><00:00:03.159><c> Vision</c><00:00:03.600><c> capable</c><00:00:03.959><c> models</c><00:00:04.400><c> in</c>

00:00:04.510 --> 00:00:04.520 align:start position:0%
most affordable Vision capable models in
 

00:00:04.520 --> 00:00:06.630 align:start position:0%
most affordable Vision capable models in
the<00:00:04.640><c> world</c><00:00:05.440><c> to</c><00:00:05.600><c> demonstrate</c><00:00:06.160><c> this</c><00:00:06.480><c> we're</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
the world to demonstrate this we're
 

00:00:06.640 --> 00:00:08.310 align:start position:0%
the world to demonstrate this we're
going<00:00:06.759><c> to</c><00:00:06.879><c> read</c><00:00:07.080><c> through</c><00:00:07.399><c> thousands</c><00:00:07.759><c> of</c><00:00:08.000><c> scan</c>

00:00:08.310 --> 00:00:08.320 align:start position:0%
going to read through thousands of scan
 

00:00:08.320 --> 00:00:10.749 align:start position:0%
going to read through thousands of scan
documents<00:00:08.760><c> in</c><00:00:08.880><c> a</c><00:00:09.000><c> matter</c><00:00:09.240><c> of</c><00:00:09.599><c> minutes</c><00:00:10.599><c> the</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
documents in a matter of minutes the
 

00:00:10.759 --> 00:00:12.629 align:start position:0%
documents in a matter of minutes the
Library<00:00:11.160><c> of</c><00:00:11.320><c> Congress</c><00:00:11.799><c> Federal</c><00:00:12.120><c> writers</c>

00:00:12.629 --> 00:00:12.639 align:start position:0%
Library of Congress Federal writers
 

00:00:12.639 --> 00:00:14.549 align:start position:0%
Library of Congress Federal writers
project<00:00:13.280><c> is</c><00:00:13.400><c> a</c><00:00:13.519><c> collection</c><00:00:13.880><c> of</c><00:00:14.080><c> thousands</c><00:00:14.440><c> of</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
project is a collection of thousands of
 

00:00:14.559 --> 00:00:16.189 align:start position:0%
project is a collection of thousands of
scanned<00:00:15.000><c> transcripts</c><00:00:15.559><c> from</c><00:00:15.719><c> interviews</c>

00:00:16.189 --> 00:00:16.199 align:start position:0%
scanned transcripts from interviews
 

00:00:16.199 --> 00:00:18.029 align:start position:0%
scanned transcripts from interviews
during<00:00:16.400><c> the</c><00:00:16.480><c> Great</c><00:00:16.680><c> Depression</c><00:00:17.640><c> this</c><00:00:17.800><c> is</c><00:00:17.920><c> a</c>

00:00:18.029 --> 00:00:18.039 align:start position:0%
during the Great Depression this is a
 

00:00:18.039 --> 00:00:19.830 align:start position:0%
during the Great Depression this is a
gold<00:00:18.279><c> mine</c><00:00:18.520><c> of</c><00:00:18.680><c> incredible</c><00:00:19.119><c> narratives</c><00:00:19.680><c> and</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
gold mine of incredible narratives and
 

00:00:19.840 --> 00:00:22.269 align:start position:0%
gold mine of incredible narratives and
real<00:00:20.080><c> life</c><00:00:20.320><c> Heroes</c><00:00:21.039><c> but</c><00:00:21.199><c> it's</c><00:00:21.359><c> locked</c><00:00:21.680><c> away</c><00:00:22.039><c> in</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
real life Heroes but it's locked away in
 

00:00:22.279 --> 00:00:24.429 align:start position:0%
real life Heroes but it's locked away in
hard<00:00:22.560><c> to</c><00:00:22.760><c> access</c><00:00:23.119><c> scans</c><00:00:23.480><c> of</c>

00:00:24.429 --> 00:00:24.439 align:start position:0%
hard to access scans of
 

00:00:24.439 --> 00:00:26.630 align:start position:0%
hard to access scans of
transcripts<00:00:25.439><c> imagine</c><00:00:25.760><c> you're</c><00:00:25.960><c> a</c><00:00:26.119><c> documentary</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
transcripts imagine you're a documentary
 

00:00:26.640 --> 00:00:28.830 align:start position:0%
transcripts imagine you're a documentary
filmmaker<00:00:27.119><c> or</c><00:00:27.279><c> journalist</c><00:00:28.240><c> how</c><00:00:28.359><c> can</c><00:00:28.480><c> you</c><00:00:28.640><c> dig</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
filmmaker or journalist how can you dig
 

00:00:28.840 --> 00:00:30.070 align:start position:0%
filmmaker or journalist how can you dig
through<00:00:29.039><c> these</c><00:00:29.240><c> thousands</c><00:00:29.519><c> of</c><00:00:29.640><c> messy</c>

00:00:30.070 --> 00:00:30.080 align:start position:0%
through these thousands of messy
 

00:00:30.080 --> 00:00:31.790 align:start position:0%
through these thousands of messy
documents<00:00:30.720><c> to</c><00:00:30.920><c> find</c><00:00:31.160><c> the</c><00:00:31.320><c> best</c><00:00:31.519><c> source</c>

00:00:31.790 --> 00:00:31.800 align:start position:0%
documents to find the best source
 

00:00:31.800 --> 00:00:33.310 align:start position:0%
documents to find the best source
material<00:00:32.160><c> for</c><00:00:32.279><c> your</c><00:00:32.439><c> research</c><00:00:33.079><c> without</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
material for your research without
 

00:00:33.320 --> 00:00:34.830 align:start position:0%
material for your research without
reading<00:00:33.640><c> them</c><00:00:33.840><c> all</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
reading them all
 

00:00:34.840 --> 00:00:36.670 align:start position:0%
reading them all
yourself<00:00:35.840><c> since</c><00:00:36.079><c> these</c><00:00:36.239><c> documents</c><00:00:36.559><c> are</c>

00:00:36.670 --> 00:00:36.680 align:start position:0%
yourself since these documents are
 

00:00:36.680 --> 00:00:38.549 align:start position:0%
yourself since these documents are
scanned<00:00:37.000><c> images</c><00:00:37.520><c> we</c><00:00:37.680><c> can't</c><00:00:37.960><c> feed</c><00:00:38.120><c> them</c><00:00:38.239><c> into</c><00:00:38.440><c> a</c>

00:00:38.549 --> 00:00:38.559 align:start position:0%
scanned images we can't feed them into a
 

00:00:38.559 --> 00:00:41.590 align:start position:0%
scanned images we can't feed them into a
Texton<00:00:39.239><c> llm</c><00:00:40.239><c> and</c><00:00:40.600><c> these</c><00:00:40.760><c> scans</c><00:00:41.079><c> are</c><00:00:41.239><c> messy</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
Texton llm and these scans are messy
 

00:00:41.600 --> 00:00:43.150 align:start position:0%
Texton llm and these scans are messy
enough<00:00:42.079><c> that</c><00:00:42.200><c> they'</c><00:00:42.399><c> be</c><00:00:42.520><c> a</c><00:00:42.600><c> challenge</c><00:00:42.960><c> for</c>

00:00:43.150 --> 00:00:43.160 align:start position:0%
enough that they' be a challenge for
 

00:00:43.160 --> 00:00:46.510 align:start position:0%
enough that they' be a challenge for
most<00:00:43.440><c> dedicated</c><00:00:44.000><c> OCR</c><00:00:44.800><c> software</c><00:00:45.800><c> but</c><00:00:46.039><c> luckily</c>

00:00:46.510 --> 00:00:46.520 align:start position:0%
most dedicated OCR software but luckily
 

00:00:46.520 --> 00:00:49.069 align:start position:0%
most dedicated OCR software but luckily
Haiku<00:00:47.039><c> is</c><00:00:47.239><c> natively</c><00:00:47.719><c> Vision</c><00:00:48.120><c> capable</c><00:00:48.800><c> and</c><00:00:48.920><c> can</c>

00:00:49.069 --> 00:00:49.079 align:start position:0%
Haiku is natively Vision capable and can
 

00:00:49.079 --> 00:00:51.470 align:start position:0%
Haiku is natively Vision capable and can
use<00:00:49.320><c> surrounding</c><00:00:49.840><c> text</c><00:00:50.120><c> to</c><00:00:50.640><c> transcribe</c><00:00:51.320><c> these</c>

00:00:51.470 --> 00:00:51.480 align:start position:0%
use surrounding text to transcribe these
 

00:00:51.480 --> 00:00:53.310 align:start position:0%
use surrounding text to transcribe these
images<00:00:52.160><c> and</c><00:00:52.359><c> really</c><00:00:52.920><c> understand</c><00:00:53.120><c> what's</c>

00:00:53.310 --> 00:00:53.320 align:start position:0%
images and really understand what's
 

00:00:53.320 --> 00:00:54.229 align:start position:0%
images and really understand what's
going

00:00:54.229 --> 00:00:54.239 align:start position:0%
going
 

00:00:54.239 --> 00:00:57.150 align:start position:0%
going
on<00:00:55.239><c> we</c><00:00:55.359><c> can</c><00:00:55.559><c> also</c><00:00:55.879><c> go</c><00:00:56.039><c> beyond</c><00:00:56.800><c> simple</c>

00:00:57.150 --> 00:00:57.160 align:start position:0%
on we can also go beyond simple
 

00:00:57.160 --> 00:00:59.189 align:start position:0%
on we can also go beyond simple
transcription<00:00:57.760><c> for</c><00:00:57.920><c> each</c><00:00:58.079><c> interview</c><00:00:58.719><c> and</c><00:00:58.960><c> ask</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
transcription for each interview and ask
 

00:00:59.199 --> 00:01:01.830 align:start position:0%
transcription for each interview and ask
Haiku<00:00:59.600><c> to</c><00:00:59.920><c> generate</c><00:01:00.280><c> structured</c><00:01:00.800><c> Json</c><00:01:01.199><c> output</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
Haiku to generate structured Json output
 

00:01:01.840 --> 00:01:05.310 align:start position:0%
Haiku to generate structured Json output
with<00:01:02.039><c> metadata</c><00:01:02.760><c> like</c><00:01:03.000><c> title</c><00:01:03.840><c> date</c><00:01:04.439><c> keywords</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
with metadata like title date keywords
 

00:01:05.320 --> 00:01:07.670 align:start position:0%
with metadata like title date keywords
but<00:01:05.560><c> also</c><00:01:05.840><c> use</c><00:01:06.080><c> some</c><00:01:06.320><c> creativity</c><00:01:06.920><c> in</c><00:01:07.080><c> judgment</c>

00:01:07.670 --> 00:01:07.680 align:start position:0%
but also use some creativity in judgment
 

00:01:07.680 --> 00:01:10.109 align:start position:0%
but also use some creativity in judgment
to<00:01:07.960><c> assess</c><00:01:08.320><c> how</c><00:01:08.520><c> compelling</c><00:01:09.000><c> a</c><00:01:09.240><c> documentary</c>

00:01:10.109 --> 00:01:10.119 align:start position:0%
to assess how compelling a documentary
 

00:01:10.119 --> 00:01:12.870 align:start position:0%
to assess how compelling a documentary
the<00:01:10.320><c> story</c><00:01:10.600><c> and</c><00:01:10.799><c> characters</c><00:01:11.200><c> would</c><00:01:11.520><c> be</c><00:01:12.520><c> we</c><00:01:12.640><c> can</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
the story and characters would be we can
 

00:01:12.880 --> 00:01:14.630 align:start position:0%
the story and characters would be we can
process<00:01:13.240><c> each</c><00:01:13.520><c> document</c><00:01:13.840><c> in</c><00:01:14.000><c> parallel</c><00:01:14.400><c> for</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
process each document in parallel for
 

00:01:14.640 --> 00:01:16.510 align:start position:0%
process each document in parallel for
performance<00:01:15.520><c> and</c><00:01:15.680><c> with</c><00:01:15.880><c> claud's</c><00:01:16.360><c> high</c>

00:01:16.510 --> 00:01:16.520 align:start position:0%
performance and with claud's high
 

00:01:16.520 --> 00:01:18.950 align:start position:0%
performance and with claud's high
availability<00:01:17.159><c> API</c><00:01:17.960><c> do</c><00:01:18.159><c> that</c><00:01:18.320><c> at</c><00:01:18.560><c> massive</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
availability API do that at massive
 

00:01:18.960 --> 00:01:20.910 align:start position:0%
availability API do that at massive
scale<00:01:19.280><c> for</c><00:01:19.479><c> hundreds</c><00:01:19.799><c> or</c><00:01:19.960><c> thousands</c><00:01:20.280><c> of</c>

00:01:20.910 --> 00:01:20.920 align:start position:0%
scale for hundreds or thousands of
 

00:01:20.920 --> 00:01:22.789 align:start position:0%
scale for hundreds or thousands of
documents<00:01:21.920><c> let's</c><00:01:22.119><c> take</c><00:01:22.240><c> a</c><00:01:22.320><c> look</c><00:01:22.479><c> at</c><00:01:22.560><c> some</c><00:01:22.680><c> of</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
documents let's take a look at some of
 

00:01:22.799 --> 00:01:25.429 align:start position:0%
documents let's take a look at some of
that<00:01:22.920><c> structured</c><00:01:23.560><c> output</c><00:01:24.560><c> Haiku</c><00:01:24.960><c> is</c><00:01:25.079><c> able</c><00:01:25.280><c> to</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
that structured output Haiku is able to
 

00:01:25.439 --> 00:01:27.390 align:start position:0%
that structured output Haiku is able to
not<00:01:25.600><c> just</c><00:01:25.759><c> transcribe</c><00:01:26.720><c> but</c><00:01:26.920><c> pull</c><00:01:27.119><c> out</c>

00:01:27.390 --> 00:01:27.400 align:start position:0%
not just transcribe but pull out
 

00:01:27.400 --> 00:01:30.109 align:start position:0%
not just transcribe but pull out
creative<00:01:28.000><c> things</c><00:01:28.360><c> like</c><00:01:28.840><c> keywords</c><00:01:29.920><c> we've</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
creative things like keywords we've
 

00:01:30.119 --> 00:01:32.469 align:start position:0%
creative things like keywords we've
transformed<00:01:30.720><c> this</c><00:01:30.920><c> collection</c><00:01:31.680><c> of</c><00:01:31.920><c> many</c><00:01:32.200><c> many</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
transformed this collection of many many
 

00:01:32.479 --> 00:01:35.429 align:start position:0%
transformed this collection of many many
scans<00:01:33.360><c> uh</c><00:01:33.479><c> into</c><00:01:33.880><c> Rich</c><00:01:34.399><c> keyword</c><00:01:34.920><c> structure</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
scans uh into Rich keyword structure
 

00:01:35.439 --> 00:01:38.190 align:start position:0%
scans uh into Rich keyword structure
data<00:01:36.399><c> imagine</c><00:01:36.920><c> what</c><00:01:37.119><c> any</c><00:01:37.320><c> organization</c><00:01:38.079><c> with</c>

00:01:38.190 --> 00:01:38.200 align:start position:0%
data imagine what any organization with
 

00:01:38.200 --> 00:01:40.190 align:start position:0%
data imagine what any organization with
a<00:01:38.320><c> knowledge</c><00:01:38.680><c> base</c><00:01:38.920><c> of</c><00:01:39.079><c> scanned</c><00:01:39.520><c> documents</c>

00:01:40.190 --> 00:01:40.200 align:start position:0%
a knowledge base of scanned documents
 

00:01:40.200 --> 00:01:42.350 align:start position:0%
a knowledge base of scanned documents
like<00:01:40.360><c> a</c><00:01:40.479><c> traditional</c><00:01:41.000><c> publisher</c><00:01:41.840><c> healthcare</c>

00:01:42.350 --> 00:01:42.360 align:start position:0%
like a traditional publisher healthcare
 

00:01:42.360 --> 00:01:45.109 align:start position:0%
like a traditional publisher healthcare
provider<00:01:42.720><c> or</c><00:01:42.920><c> Law</c><00:01:43.159><c> Firm</c><00:01:43.520><c> can</c><00:01:43.640><c> do</c><00:01:44.520><c> Haiku</c><00:01:44.960><c> can</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
provider or Law Firm can do Haiku can
 

00:01:45.119 --> 00:01:46.709 align:start position:0%
provider or Law Firm can do Haiku can
parse<00:01:45.439><c> their</c><00:01:45.600><c> extensive</c><00:01:46.200><c> archives</c><00:01:46.560><c> and</c>

00:01:46.709 --> 00:01:46.719 align:start position:0%
parse their extensive archives and
 

00:01:46.719 --> 00:01:48.590 align:start position:0%
parse their extensive archives and
bodies<00:01:47.000><c> of</c><00:01:47.159><c> work</c><00:01:47.799><c> we'd</c><00:01:48.000><c> love</c><00:01:48.119><c> for</c><00:01:48.240><c> you</c><00:01:48.320><c> to</c><00:01:48.439><c> try</c>

00:01:48.590 --> 00:01:48.600 align:start position:0%
bodies of work we'd love for you to try
 

00:01:48.600 --> 00:01:52.360 align:start position:0%
bodies of work we'd love for you to try
it<00:01:48.680><c> out</c><00:01:48.880><c> and</c><00:01:49.000><c> see</c><00:01:49.159><c> what</c><00:01:49.240><c> you</c><00:01:49.360><c> build</c>

