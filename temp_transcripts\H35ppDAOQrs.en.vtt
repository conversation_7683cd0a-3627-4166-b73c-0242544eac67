WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:03.020 align:start position:0%
 
all<00:00:00.329><c> right</c><00:00:00.599><c> welcome</c><00:00:00.900><c> to</c><00:00:01.020><c> another</c><00:00:01.860><c> video</c><00:00:02.639><c> what</c>

00:00:03.020 --> 00:00:03.030 align:start position:0%
all right welcome to another video what
 

00:00:03.030 --> 00:00:04.550 align:start position:0%
all right welcome to another video what
are<00:00:03.120><c> we</c><00:00:03.270><c> talking</c><00:00:03.510><c> about</c><00:00:03.689><c> big</c><00:00:03.899><c> annotation</c><00:00:04.440><c> and</c>

00:00:04.550 --> 00:00:04.560 align:start position:0%
are we talking about big annotation and
 

00:00:04.560 --> 00:00:06.349 align:start position:0%
are we talking about big annotation and
go<00:00:04.710><c> over</c><00:00:04.890><c> some</c><00:00:05.130><c> interview</c><00:00:05.490><c> questions</c><00:00:05.850><c> all</c>

00:00:06.349 --> 00:00:06.359 align:start position:0%
go over some interview questions all
 

00:00:06.359 --> 00:00:08.270 align:start position:0%
go over some interview questions all
right<00:00:06.450><c> so</c><00:00:07.049><c> let's</c><00:00:07.379><c> going</c><00:00:07.589><c> into</c><00:00:07.799><c> the</c><00:00:07.890><c> first</c><00:00:08.069><c> one</c>

00:00:08.270 --> 00:00:08.280 align:start position:0%
right so let's going into the first one
 

00:00:08.280 --> 00:00:11.990 align:start position:0%
right so let's going into the first one
so<00:00:09.030><c> here</c><00:00:09.599><c> as</c><00:00:10.440><c> usual</c><00:00:11.250><c> we're</c><00:00:11.610><c> going</c><00:00:11.639><c> to</c><00:00:11.820><c> go</c><00:00:11.910><c> to</c>

00:00:11.990 --> 00:00:12.000 align:start position:0%
so here as usual we're going to go to
 

00:00:12.000 --> 00:00:13.430 align:start position:0%
so here as usual we're going to go to
the<00:00:12.059><c> meeting</c><00:00:12.240><c> of</c><00:00:12.269><c> the</c><00:00:12.360><c> code</c><00:00:12.540><c> just</c><00:00:13.230><c> take</c><00:00:13.410><c> a</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
the meeting of the code just take a
 

00:00:13.440 --> 00:00:14.780 align:start position:0%
the meeting of the code just take a
briefly<00:00:13.799><c> just</c><00:00:14.040><c> make</c><00:00:14.190><c> sure</c><00:00:14.280><c> we</c><00:00:14.340><c> understand</c><00:00:14.639><c> it</c>

00:00:14.780 --> 00:00:14.790 align:start position:0%
briefly just make sure we understand it
 

00:00:14.790 --> 00:00:17.029 align:start position:0%
briefly just make sure we understand it
this<00:00:15.599><c> is</c><00:00:15.660><c> where</c><00:00:16.139><c> this</c><00:00:16.470><c> one</c><00:00:16.650><c> is</c><00:00:16.770><c> going</c><00:00:16.859><c> to</c><00:00:16.920><c> be</c>

00:00:17.029 --> 00:00:17.039 align:start position:0%
this is where this one is going to be
 

00:00:17.039 --> 00:00:19.010 align:start position:0%
this is where this one is going to be
taking<00:00:17.430><c> in</c><00:00:17.640><c> an</c><00:00:17.880><c> array</c><00:00:18.150><c> is</c><00:00:18.210><c> the</c><00:00:18.510><c> input</c><00:00:18.810><c> and</c>

00:00:19.010 --> 00:00:19.020 align:start position:0%
taking in an array is the input and
 

00:00:19.020 --> 00:00:21.260 align:start position:0%
taking in an array is the input and
we're<00:00:19.529><c> just</c><00:00:19.590><c> going</c><00:00:19.770><c> to</c><00:00:19.800><c> be</c><00:00:19.890><c> reversing</c><00:00:20.430><c> it</c><00:00:20.550><c> it's</c>

00:00:21.260 --> 00:00:21.270 align:start position:0%
we're just going to be reversing it it's
 

00:00:21.270 --> 00:00:22.849 align:start position:0%
we're just going to be reversing it it's
actually<00:00:21.539><c> happening</c><00:00:21.750><c> in</c><00:00:21.960><c> place</c><00:00:22.350><c> there's</c><00:00:22.740><c> no</c>

00:00:22.849 --> 00:00:22.859 align:start position:0%
actually happening in place there's no
 

00:00:22.859 --> 00:00:26.630 align:start position:0%
actually happening in place there's no
interest<00:00:23.070><c> or</c><00:00:23.279><c> grant</c><00:00:23.789><c> award</c><00:00:24.029><c> vote</c><00:00:24.289><c> so</c><00:00:25.400><c> the</c><00:00:26.400><c> for</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
interest or grant award vote so the for
 

00:00:26.640 --> 00:00:29.570 align:start position:0%
interest or grant award vote so the for
loop<00:00:26.789><c> is</c><00:00:27.029><c> going</c><00:00:27.060><c> from</c><00:00:27.630><c> I</c><00:00:27.840><c> equals</c><00:00:28.170><c> 0</c><00:00:28.380><c> to</c><00:00:29.099><c> the</c>

00:00:29.570 --> 00:00:29.580 align:start position:0%
loop is going from I equals 0 to the
 

00:00:29.580 --> 00:00:32.060 align:start position:0%
loop is going from I equals 0 to the
length<00:00:29.760><c> of</c><00:00:29.910><c> your</c><00:00:30.000><c> 8/2</c><00:00:30.900><c> so</c><00:00:31.410><c> we're</c><00:00:31.619><c> only</c><00:00:31.769><c> going</c>

00:00:32.060 --> 00:00:32.070 align:start position:0%
length of your 8/2 so we're only going
 

00:00:32.070 --> 00:00:35.569 align:start position:0%
length of your 8/2 so we're only going
around<00:00:32.640><c> half</c><00:00:32.969><c> of</c><00:00:33.149><c> the</c><00:00:33.270><c> array</c><00:00:33.450><c> and</c><00:00:34.399><c> then</c><00:00:35.399><c> it</c>

00:00:35.569 --> 00:00:35.579 align:start position:0%
around half of the array and then it
 

00:00:35.579 --> 00:00:39.049 align:start position:0%
around half of the array and then it
looks<00:00:35.760><c> like</c><00:00:36.000><c> in</c><00:00:36.870><c> other</c><00:00:37.260><c> equals</c><00:00:37.739><c> R</c><00:00:37.860><c> a</c><00:00:38.059><c> length</c>

00:00:39.049 --> 00:00:39.059 align:start position:0%
looks like in other equals R a length
 

00:00:39.059 --> 00:00:41.330 align:start position:0%
looks like in other equals R a length
minus<00:00:39.600><c> I</c><00:00:39.750><c> minus</c><00:00:39.960><c> 1</c><00:00:40.290><c> so</c><00:00:40.829><c> the</c><00:00:40.920><c> first</c><00:00:41.100><c> iteration</c>

00:00:41.330 --> 00:00:41.340 align:start position:0%
minus I minus 1 so the first iteration
 

00:00:41.340 --> 00:00:45.229 align:start position:0%
minus I minus 1 so the first iteration
other<00:00:42.090><c> is</c><00:00:42.450><c> going</c><00:00:42.719><c> to</c><00:00:42.870><c> be</c><00:00:43.140><c> the</c><00:00:43.920><c> last</c><00:00:44.090><c> element</c><00:00:45.090><c> in</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
other is going to be the last element in
 

00:00:45.239 --> 00:00:47.900 align:start position:0%
other is going to be the last element in
the<00:00:45.270><c> array</c><00:00:45.620><c> it's</c><00:00:46.620><c> just</c><00:00:46.770><c> like</c><00:00:46.920><c> minus</c><00:00:47.280><c> 0</c><00:00:47.579><c> minus</c><00:00:47.610><c> 1</c>

00:00:47.900 --> 00:00:47.910 align:start position:0%
the array it's just like minus 0 minus 1
 

00:00:47.910 --> 00:00:49.549 align:start position:0%
the array it's just like minus 0 minus 1
so<00:00:48.149><c> it's</c><00:00:48.300><c> like</c><00:00:48.539><c> the</c><00:00:48.690><c> size</c><00:00:48.870><c> of</c><00:00:48.899><c> your</c><00:00:49.020><c> a</c><00:00:49.110><c> minus</c><00:00:49.350><c> 1</c>

00:00:49.549 --> 00:00:49.559 align:start position:0%
so it's like the size of your a minus 1
 

00:00:49.559 --> 00:00:52.040 align:start position:0%
so it's like the size of your a minus 1
and<00:00:49.980><c> B</c><00:00:50.129><c> the</c><00:00:50.250><c> last</c><00:00:50.399><c> element</c><00:00:50.640><c> array</c><00:00:50.969><c> and</c><00:00:51.270><c> then</c>

00:00:52.040 --> 00:00:52.050 align:start position:0%
and B the last element array and then
 

00:00:52.050 --> 00:00:56.090 align:start position:0%
and B the last element array and then
the<00:00:52.320><c> next</c><00:00:52.559><c> 3</c><00:00:53.270><c> these</c><00:00:54.270><c> here</c><00:00:54.750><c> these</c><00:00:55.469><c> are</c><00:00:55.680><c> that's</c>

00:00:56.090 --> 00:00:56.100 align:start position:0%
the next 3 these here these are that's
 

00:00:56.100 --> 00:00:58.610 align:start position:0%
the next 3 these here these are that's
just<00:00:56.309><c> a</c><00:00:56.489><c> basic</c><00:00:56.879><c> swap</c><00:00:57.449><c> all</c><00:00:57.960><c> right</c><00:00:58.109><c> that's</c><00:00:58.350><c> all</c>

00:00:58.610 --> 00:00:58.620 align:start position:0%
just a basic swap all right that's all
 

00:00:58.620 --> 00:01:01.400 align:start position:0%
just a basic swap all right that's all
it's<00:00:58.859><c> doing</c><00:00:58.980><c> is</c><00:00:59.160><c> just</c><00:00:59.250><c> swapping</c><00:01:00.050><c> two</c><00:01:01.050><c> elements</c>

00:01:01.400 --> 00:01:01.410 align:start position:0%
it's doing is just swapping two elements
 

00:01:01.410 --> 00:01:06.710 align:start position:0%
it's doing is just swapping two elements
so<00:01:01.469><c> it's</c><00:01:01.620><c> swapping</c><00:01:03.140><c> the</c><00:01:04.140><c> first</c><00:01:04.379><c> index</c><00:01:04.799><c> so</c><00:01:05.729><c> the</c>

00:01:06.710 --> 00:01:06.720 align:start position:0%
so it's swapping the first index so the
 

00:01:06.720 --> 00:01:08.000 align:start position:0%
so it's swapping the first index so the
first<00:01:06.900><c> time</c><00:01:07.080><c> on</c><00:01:07.200><c> the</c><00:01:07.290><c> right</c><00:01:07.470><c> with</c><00:01:07.740><c> the</c><00:01:07.830><c> last</c>

00:01:08.000 --> 00:01:08.010 align:start position:0%
first time on the right with the last
 

00:01:08.010 --> 00:01:09.440 align:start position:0%
first time on the right with the last
that's<00:01:08.580><c> all</c><00:01:08.670><c> it's</c><00:01:08.790><c> doing</c><00:01:08.880><c> and</c><00:01:09.119><c> it's</c><00:01:09.240><c> going</c><00:01:09.390><c> to</c>

00:01:09.440 --> 00:01:09.450 align:start position:0%
that's all it's doing and it's going to
 

00:01:09.450 --> 00:01:11.060 align:start position:0%
that's all it's doing and it's going to
kind<00:01:09.570><c> of</c><00:01:09.659><c> keep</c><00:01:09.840><c> coming</c><00:01:10.170><c> in</c><00:01:10.290><c> and</c><00:01:10.320><c> doing</c><00:01:10.500><c> that</c><00:01:10.740><c> ok</c>

00:01:11.060 --> 00:01:11.070 align:start position:0%
kind of keep coming in and doing that ok
 

00:01:11.070 --> 00:01:12.350 align:start position:0%
kind of keep coming in and doing that ok
so<00:01:11.460><c> that's</c><00:01:11.580><c> how</c><00:01:11.640><c> that's</c><00:01:11.880><c> why</c><00:01:12.000><c> this</c><00:01:12.060><c> house</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
so that's how that's why this house
 

00:01:12.360 --> 00:01:16.310 align:start position:0%
so that's how that's why this house
doing<00:01:12.600><c> in</c><00:01:12.780><c> place</c><00:01:14.600><c> alright</c><00:01:15.600><c> so</c><00:01:15.810><c> now</c><00:01:15.900><c> let's</c><00:01:15.960><c> now</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
doing in place alright so now let's now
 

00:01:16.320 --> 00:01:17.950 align:start position:0%
doing in place alright so now let's now
understand<00:01:16.710><c> what's</c><00:01:16.860><c> actually</c><00:01:16.979><c> happening</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
understand what's actually happening
 

00:01:17.960 --> 00:01:20.060 align:start position:0%
understand what's actually happening
let's<00:01:18.960><c> let's</c><00:01:19.380><c> take</c><00:01:19.470><c> a</c><00:01:19.530><c> look</c><00:01:19.650><c> at</c><00:01:19.740><c> what's</c><00:01:19.890><c> going</c>

00:01:20.060 --> 00:01:20.070 align:start position:0%
let's let's take a look at what's going
 

00:01:20.070 --> 00:01:24.350 align:start position:0%
let's let's take a look at what's going
on<00:01:20.310><c> here</c><00:01:22.310><c> we</c><00:01:23.310><c> have</c><00:01:23.430><c> a</c><00:01:23.460><c> for</c><00:01:23.700><c> loop</c><00:01:23.820><c> and</c><00:01:24.030><c> we</c><00:01:24.090><c> have</c><00:01:24.330><c> a</c>

00:01:24.350 --> 00:01:24.360 align:start position:0%
on here we have a for loop and we have a
 

00:01:24.360 --> 00:01:25.789 align:start position:0%
on here we have a for loop and we have a
bunch<00:01:24.570><c> of</c><00:01:24.600><c> statements</c><00:01:24.840><c> there's</c><00:01:25.350><c> bunch</c><00:01:25.710><c> of</c>

00:01:25.789 --> 00:01:25.799 align:start position:0%
bunch of statements there's bunch of
 

00:01:25.799 --> 00:01:28.789 align:start position:0%
bunch of statements there's bunch of
assignment<00:01:26.159><c> operators</c><00:01:26.869><c> so</c><00:01:27.869><c> one</c><00:01:28.500><c> of</c><00:01:28.530><c> the</c><00:01:28.650><c> first</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
assignment operators so one of the first
 

00:01:28.799 --> 00:01:30.410 align:start position:0%
assignment operators so one of the first
thing<00:01:28.920><c> we</c><00:01:29.009><c> can</c><00:01:29.159><c> do</c><00:01:29.340><c> is</c><00:01:29.610><c> you</c><00:01:29.670><c> can</c><00:01:29.790><c> eliminate</c><00:01:30.119><c> a</c>

00:01:30.410 --> 00:01:30.420 align:start position:0%
thing we can do is you can eliminate a
 

00:01:30.420 --> 00:01:31.760 align:start position:0%
thing we can do is you can eliminate a
lot<00:01:30.659><c> of</c><00:01:30.689><c> things</c><00:01:30.990><c> here</c><00:01:31.259><c> that</c><00:01:31.320><c> we</c><00:01:31.470><c> don't</c><00:01:31.619><c> care</c>

00:01:31.760 --> 00:01:31.770 align:start position:0%
lot of things here that we don't care
 

00:01:31.770 --> 00:01:33.920 align:start position:0%
lot of things here that we don't care
about<00:01:31.829><c> these</c><00:01:32.759><c> assignment</c><00:01:33.180><c> operators</c><00:01:33.540><c> I</c><00:01:33.659><c> just</c>

00:01:33.920 --> 00:01:33.930 align:start position:0%
about these assignment operators I just
 

00:01:33.930 --> 00:01:37.210 align:start position:0%
about these assignment operators I just
again<00:01:34.470><c> or</c><00:01:34.740><c> just</c><00:01:35.220><c> Big</c><00:01:35.430><c> O</c><00:01:35.549><c> of</c><00:01:35.640><c> one</c><00:01:35.880><c> operation</c><00:01:36.450><c> we</c>

00:01:37.210 --> 00:01:37.220 align:start position:0%
again or just Big O of one operation we
 

00:01:37.220 --> 00:01:40.219 align:start position:0%
again or just Big O of one operation we
ain't<00:01:38.220><c> no</c><00:01:38.460><c> matter</c><00:01:38.670><c> what</c><00:01:39.030><c> the</c><00:01:39.509><c> this</c><00:01:39.990><c> input</c>

00:01:40.219 --> 00:01:40.229 align:start position:0%
ain't no matter what the this input
 

00:01:40.229 --> 00:01:43.039 align:start position:0%
ain't no matter what the this input
array<00:01:40.409><c> no</c><00:01:40.740><c> matter</c><00:01:40.890><c> what</c><00:01:40.979><c> the</c><00:01:41.100><c> size</c><00:01:41.280><c> is</c><00:01:41.899><c> this</c><00:01:42.899><c> is</c>

00:01:43.039 --> 00:01:43.049 align:start position:0%
array no matter what the size is this is
 

00:01:43.049 --> 00:01:46.090 align:start position:0%
array no matter what the size is this is
gonna<00:01:43.200><c> go</c><00:01:43.500><c> over</c><00:01:44.149><c> we're</c><00:01:45.149><c> gonna</c><00:01:45.240><c> perform</c><00:01:45.479><c> these</c>

00:01:46.090 --> 00:01:46.100 align:start position:0%
gonna go over we're gonna perform these
 

00:01:46.100 --> 00:01:49.249 align:start position:0%
gonna go over we're gonna perform these
n<00:01:47.100><c> divided</c><00:01:48.090><c> by</c><00:01:48.119><c> 2</c><00:01:48.180><c> or</c><00:01:48.540><c> the</c><00:01:48.810><c> array</c><00:01:48.990><c> length</c>

00:01:49.249 --> 00:01:49.259 align:start position:0%
n divided by 2 or the array length
 

00:01:49.259 --> 00:01:51.560 align:start position:0%
n divided by 2 or the array length
divided<00:01:49.530><c> by</c><00:01:49.560><c> 2</c><00:01:49.740><c> times</c><00:01:50.189><c> plus</c><00:01:51.000><c> the</c><00:01:51.270><c> actual</c>

00:01:51.560 --> 00:01:51.570 align:start position:0%
divided by 2 times plus the actual
 

00:01:51.570 --> 00:01:53.690 align:start position:0%
divided by 2 times plus the actual
operation<00:01:52.079><c> it's</c><00:01:52.710><c> not</c><00:01:52.950><c> gonna</c><00:01:53.100><c> at</c><00:01:53.220><c> the</c><00:01:53.340><c> time</c><00:01:53.549><c> to</c>

00:01:53.690 --> 00:01:53.700 align:start position:0%
operation it's not gonna at the time to
 

00:01:53.700 --> 00:01:56.149 align:start position:0%
operation it's not gonna at the time to
perform<00:01:54.060><c> each</c><00:01:54.600><c> assignment</c><00:01:55.070><c> isn't</c><00:01:56.070><c> gonna</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
perform each assignment isn't gonna
 

00:01:56.159 --> 00:01:57.859 align:start position:0%
perform each assignment isn't gonna
change<00:01:56.280><c> what</c><00:01:57.030><c> the</c><00:01:57.210><c> size</c><00:01:57.390><c> of</c><00:01:57.509><c> your</c><00:01:57.600><c> reading</c><00:01:57.810><c> is</c>

00:01:57.859 --> 00:01:57.869 align:start position:0%
change what the size of your reading is
 

00:01:57.869 --> 00:01:59.719 align:start position:0%
change what the size of your reading is
always<00:01:58.079><c> gonna</c><00:01:58.829><c> take</c><00:01:59.040><c> same</c><00:01:59.250><c> amount</c><00:01:59.430><c> of</c><00:01:59.490><c> times</c>

00:01:59.719 --> 00:01:59.729 align:start position:0%
always gonna take same amount of times
 

00:01:59.729 --> 00:02:00.770 align:start position:0%
always gonna take same amount of times
performance<00:02:00.299><c> ok</c>

00:02:00.770 --> 00:02:00.780 align:start position:0%
performance ok
 

00:02:00.780 --> 00:02:02.600 align:start position:0%
performance ok
that's<00:02:01.200><c> why</c><00:02:01.500><c> they're</c><00:02:01.770><c> Big</c><00:02:01.979><c> O</c><00:02:02.070><c> of</c><00:02:02.100><c> 1</c><00:02:02.399><c> their</c>

00:02:02.600 --> 00:02:02.610 align:start position:0%
that's why they're Big O of 1 their
 

00:02:02.610 --> 00:02:05.149 align:start position:0%
that's why they're Big O of 1 their
constant<00:02:03.030><c> time</c><00:02:03.380><c> so</c><00:02:04.380><c> we</c><00:02:04.439><c> got</c><00:02:04.530><c> rid</c><00:02:04.710><c> of</c><00:02:04.770><c> all</c><00:02:04.890><c> that</c>

00:02:05.149 --> 00:02:05.159 align:start position:0%
constant time so we got rid of all that
 

00:02:05.159 --> 00:02:06.590 align:start position:0%
constant time so we got rid of all that
rib<00:02:05.490><c> all</c><00:02:05.640><c> those</c><00:02:05.820><c> and</c><00:02:06.090><c> everything</c><00:02:06.329><c> inside</c><00:02:06.509><c> the</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
rib all those and everything inside the
 

00:02:06.600 --> 00:02:09.249 align:start position:0%
rib all those and everything inside the
for<00:02:06.780><c> loop</c><00:02:06.810><c> now</c><00:02:07.439><c> we</c><00:02:07.619><c> care</c><00:02:07.829><c> about</c><00:02:07.920><c> the</c><00:02:08.129><c> Portland</c>

00:02:09.249 --> 00:02:09.259 align:start position:0%
for loop now we care about the Portland
 

00:02:09.259 --> 00:02:11.750 align:start position:0%
for loop now we care about the Portland
so<00:02:10.259><c> we're</c><00:02:10.890><c> going</c><00:02:10.979><c> to</c><00:02:11.069><c> sub</c><00:02:11.220><c> to</c><00:02:11.340><c> your</c><00:02:11.489><c> rate</c><00:02:11.610><c> at</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
so we're going to sub to your rate at
 

00:02:11.760 --> 00:02:13.040 align:start position:0%
so we're going to sub to your rate at
length<00:02:11.940><c> for</c><00:02:12.330><c> n</c>

00:02:13.040 --> 00:02:13.050 align:start position:0%
length for n
 

00:02:13.050 --> 00:02:16.310 align:start position:0%
length for n
is<00:02:13.260><c> very</c><00:02:13.650><c> common</c><00:02:14.040><c> all</c><00:02:14.280><c> right</c><00:02:14.790><c> and</c><00:02:15.230><c> being</c><00:02:16.230><c> the</c>

00:02:16.310 --> 00:02:16.320 align:start position:0%
is very common all right and being the
 

00:02:16.320 --> 00:02:20.090 align:start position:0%
is very common all right and being the
size<00:02:16.500><c> of</c><00:02:17.190><c> the</c><00:02:17.460><c> array</c><00:02:17.750><c> or</c><00:02:18.750><c> the</c><00:02:18.870><c> length</c><00:02:19.050><c> of</c><00:02:19.230><c> it</c><00:02:19.350><c> so</c>

00:02:20.090 --> 00:02:20.100 align:start position:0%
size of the array or the length of it so
 

00:02:20.100 --> 00:02:28.240 align:start position:0%
size of the array or the length of it so
we<00:02:20.250><c> can</c><00:02:20.280><c> say</c><00:02:20.700><c> that</c><00:02:21.570><c> this</c><00:02:21.810><c> is</c><00:02:22.310><c> Big</c><00:02:23.310><c> O</c><00:02:23.580><c> of</c><00:02:24.000><c> and</c><00:02:25.520><c> /</c><00:02:26.520><c> -</c>

00:02:28.240 --> 00:02:28.250 align:start position:0%
we can say that this is Big O of and / -
 

00:02:28.250 --> 00:02:29.690 align:start position:0%
we can say that this is Big O of and / -
okay

00:02:29.690 --> 00:02:29.700 align:start position:0%
okay
 

00:02:29.700 --> 00:02:32.600 align:start position:0%
okay
so<00:02:30.570><c> what's</c><00:02:31.440><c> actually</c><00:02:31.620><c> going</c><00:02:31.860><c> to</c><00:02:31.920><c> happen</c><00:02:31.980><c> here</c>

00:02:32.600 --> 00:02:32.610 align:start position:0%
so what's actually going to happen here
 

00:02:32.610 --> 00:02:35.870 align:start position:0%
so what's actually going to happen here
is<00:02:33.050><c> we're</c><00:02:34.050><c> gonna</c><00:02:34.170><c> we're</c><00:02:34.890><c> gonna</c><00:02:35.100><c> say</c><00:02:35.280><c> he</c><00:02:35.340><c> goes</c>

00:02:35.870 --> 00:02:35.880 align:start position:0%
is we're gonna we're gonna say he goes
 

00:02:35.880 --> 00:02:39.770 align:start position:0%
is we're gonna we're gonna say he goes
and<00:02:36.150><c> instead</c><00:02:36.780><c> of</c><00:02:36.900><c> Big</c><00:02:37.110><c> O</c><00:02:37.260><c> of</c><00:02:37.290><c> n</c><00:02:37.500><c> /</c><00:02:38.250><c> -</c><00:02:39.030><c> all</c><00:02:39.600><c> right</c>

00:02:39.770 --> 00:02:39.780 align:start position:0%
and instead of Big O of n / - all right
 

00:02:39.780 --> 00:02:42.020 align:start position:0%
and instead of Big O of n / - all right
and<00:02:39.960><c> the</c><00:02:40.020><c> reason</c><00:02:40.560><c> we</c><00:02:41.250><c> think</c><00:02:41.400><c> about</c><00:02:41.670><c> this</c><00:02:41.850><c> the</c>

00:02:42.020 --> 00:02:42.030 align:start position:0%
and the reason we think about this the
 

00:02:42.030 --> 00:02:45.710 align:start position:0%
and the reason we think about this the
reason<00:02:42.180><c> is</c><00:02:43.310><c> we</c><00:02:44.310><c> can</c><00:02:44.460><c> just</c><00:02:44.700><c> get</c><00:02:44.850><c> rid</c><00:02:44.970><c> of</c><00:02:45.030><c> -</c><00:02:45.300><c> is</c>

00:02:45.710 --> 00:02:45.720 align:start position:0%
reason is we can just get rid of - is
 

00:02:45.720 --> 00:02:49.250 align:start position:0%
reason is we can just get rid of - is
because<00:02:46.580><c> let's</c><00:02:47.580><c> say</c><00:02:47.870><c> let's</c><00:02:48.870><c> say</c><00:02:48.900><c> the</c><00:02:49.050><c> real</c><00:02:49.170><c> ace</c>

00:02:49.250 --> 00:02:49.260 align:start position:0%
because let's say let's say the real ace
 

00:02:49.260 --> 00:02:51.410 align:start position:0%
because let's say let's say the real ace
is<00:02:49.410><c> 10</c><00:02:49.710><c> all</c><00:02:50.010><c> right</c><00:02:50.160><c> so</c><00:02:50.340><c> we're</c><00:02:50.490><c> 10</c><00:02:51.030><c> divided</c><00:02:51.270><c> by</c><00:02:51.300><c> 2</c>

00:02:51.410 --> 00:02:51.420 align:start position:0%
is 10 all right so we're 10 divided by 2
 

00:02:51.420 --> 00:02:54.620 align:start position:0%
is 10 all right so we're 10 divided by 2
is<00:02:51.600><c> 5</c><00:02:51.840><c> now</c><00:02:52.590><c> let's</c><00:02:52.770><c> say</c><00:02:52.920><c> it's</c><00:02:53.040><c> 100</c><00:02:53.220><c> 100</c><00:02:54.180><c> by</c><00:02:54.270><c> 2</c><00:02:54.330><c> is</c>

00:02:54.620 --> 00:02:54.630 align:start position:0%
is 5 now let's say it's 100 100 by 2 is
 

00:02:54.630 --> 00:02:57.320 align:start position:0%
is 5 now let's say it's 100 100 by 2 is
50<00:02:55.110><c> you</c><00:02:56.010><c> know</c><00:02:56.130><c> if</c><00:02:56.220><c> it's</c><00:02:56.310><c> a</c><00:02:56.340><c> thousand</c><00:02:56.820><c> thousand</c>

00:02:57.320 --> 00:02:57.330 align:start position:0%
50 you know if it's a thousand thousand
 

00:02:57.330 --> 00:03:01.690 align:start position:0%
50 you know if it's a thousand thousand
by<00:02:57.390><c> 2</c><00:02:57.450><c> is</c><00:02:57.750><c> 500</c><00:02:58.290><c> so</c><00:02:58.500><c> what's</c><00:02:59.310><c> happening</c><00:02:59.730><c> is</c><00:02:59.990><c> the</c>

00:03:01.690 --> 00:03:01.700 align:start position:0%
by 2 is 500 so what's happening is the
 

00:03:01.700 --> 00:03:04.190 align:start position:0%
by 2 is 500 so what's happening is the
the<00:03:02.700><c> time</c><00:03:02.910><c> for</c><00:03:03.150><c> the</c><00:03:03.510><c> amount</c><00:03:03.720><c> of</c><00:03:03.750><c> time</c><00:03:03.930><c> to</c><00:03:04.050><c> go</c><00:03:04.170><c> to</c>

00:03:04.190 --> 00:03:04.200 align:start position:0%
the time for the amount of time to go to
 

00:03:04.200 --> 00:03:06.590 align:start position:0%
the time for the amount of time to go to
the<00:03:04.350><c> loop</c><00:03:04.560><c> is</c><00:03:05.010><c> going</c><00:03:05.190><c> to</c><00:03:05.340><c> increase</c><00:03:05.880><c> with</c><00:03:06.390><c> the</c>

00:03:06.590 --> 00:03:06.600 align:start position:0%
the loop is going to increase with the
 

00:03:06.600 --> 00:03:09.770 align:start position:0%
the loop is going to increase with the
size<00:03:06.900><c> of</c><00:03:07.260><c> the</c><00:03:07.470><c> input</c><00:03:07.730><c> all</c><00:03:08.730><c> right</c><00:03:08.940><c> linearly</c><00:03:09.450><c> so</c>

00:03:09.770 --> 00:03:09.780 align:start position:0%
size of the input all right linearly so
 

00:03:09.780 --> 00:03:13.730 align:start position:0%
size of the input all right linearly so
it's<00:03:09.900><c> gonna</c><00:03:10.020><c> be</c><00:03:10.110><c> a</c><00:03:10.140><c> big</c><00:03:10.290><c> old</c><00:03:10.320><c> and</c><00:03:11.360><c> right</c><00:03:12.740><c> so</c>

00:03:13.730 --> 00:03:13.740 align:start position:0%
it's gonna be a big old and right so
 

00:03:13.740 --> 00:03:16.670 align:start position:0%
it's gonna be a big old and right so
anytime<00:03:13.950><c> we</c><00:03:14.550><c> increase</c><00:03:15.450><c> whatever</c><00:03:15.990><c> that</c><00:03:16.260><c> this</c>

00:03:16.670 --> 00:03:16.680 align:start position:0%
anytime we increase whatever that this
 

00:03:16.680 --> 00:03:19.070 align:start position:0%
anytime we increase whatever that this
how<00:03:16.800><c> the</c><00:03:16.860><c> input</c><00:03:17.040><c> is</c><00:03:17.280><c> is</c><00:03:17.880><c> still</c><00:03:18.270><c> gonna</c><00:03:18.510><c> perform</c>

00:03:19.070 --> 00:03:19.080 align:start position:0%
how the input is is still gonna perform
 

00:03:19.080 --> 00:03:22.250 align:start position:0%
how the input is is still gonna perform
and<00:03:19.860><c> divided</c><00:03:20.400><c> by</c><00:03:20.430><c> 2</c><00:03:20.520><c> times</c><00:03:20.940><c> no</c><00:03:21.540><c> matter</c><00:03:21.720><c> what</c><00:03:22.050><c> so</c>

00:03:22.250 --> 00:03:22.260 align:start position:0%
and divided by 2 times no matter what so
 

00:03:22.260 --> 00:03:23.780 align:start position:0%
and divided by 2 times no matter what so
it's<00:03:22.380><c> gonna</c><00:03:22.500><c> literally</c><00:03:22.980><c> they're</c><00:03:23.670><c> gonna</c>

00:03:23.780 --> 00:03:23.790 align:start position:0%
it's gonna literally they're gonna
 

00:03:23.790 --> 00:03:25.090 align:start position:0%
it's gonna literally they're gonna
really<00:03:24.180><c> go</c><00:03:24.450><c> together</c>

00:03:25.090 --> 00:03:25.100 align:start position:0%
really go together
 

00:03:25.100 --> 00:03:28.070 align:start position:0%
really go together
okay<00:03:26.100><c> and</c><00:03:26.400><c> a</c><00:03:27.060><c> lot</c><00:03:27.239><c> of</c><00:03:27.270><c> times</c><00:03:27.540><c> one</c><00:03:27.840><c> of</c><00:03:27.900><c> the</c><00:03:27.989><c> big</c>

00:03:28.070 --> 00:03:28.080 align:start position:0%
okay and a lot of times one of the big
 

00:03:28.080 --> 00:03:29.510 align:start position:0%
okay and a lot of times one of the big
thing<00:03:28.260><c> is</c><00:03:28.440><c> you</c><00:03:28.470><c> get</c><00:03:28.739><c> rid</c><00:03:28.860><c> of</c><00:03:28.920><c> the</c><00:03:29.070><c> constants</c>

00:03:29.510 --> 00:03:29.520 align:start position:0%
thing is you get rid of the constants
 

00:03:29.520 --> 00:03:33.140 align:start position:0%
thing is you get rid of the constants
anyways<00:03:30.770><c> one</c><00:03:31.770><c> of</c><00:03:31.800><c> two</c><00:03:32.400><c> things</c><00:03:32.580><c> here</c><00:03:32.970><c> are</c><00:03:33.060><c> the</c>

00:03:33.140 --> 00:03:33.150 align:start position:0%
anyways one of two things here are the
 

00:03:33.150 --> 00:03:35.270 align:start position:0%
anyways one of two things here are the
constants<00:03:33.690><c> and</c><00:03:33.810><c> take</c><00:03:34.680><c> the</c><00:03:34.830><c> highest</c><00:03:34.980><c> order</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
constants and take the highest order
 

00:03:35.280 --> 00:03:38.030 align:start position:0%
constants and take the highest order
highest<00:03:36.090><c> order</c><00:03:36.270><c> of</c><00:03:36.330><c> operation</c><00:03:36.870><c> all</c><00:03:37.290><c> right</c><00:03:37.470><c> so</c>

00:03:38.030 --> 00:03:38.040 align:start position:0%
highest order of operation all right so
 

00:03:38.040 --> 00:03:40.820 align:start position:0%
highest order of operation all right so
that's<00:03:38.550><c> those</c><00:03:39.450><c> are</c><00:03:39.510><c> two</c><00:03:39.780><c> key</c><00:03:40.020><c> things</c><00:03:40.260><c> to</c><00:03:40.440><c> think</c>

00:03:40.820 --> 00:03:40.830 align:start position:0%
that's those are two key things to think
 

00:03:40.830 --> 00:03:43.610 align:start position:0%
that's those are two key things to think
about<00:03:40.890><c> actually</c><00:03:41.370><c> whenever</c><00:03:41.580><c> you're</c><00:03:42.620><c> figuring</c>

00:03:43.610 --> 00:03:43.620 align:start position:0%
about actually whenever you're figuring
 

00:03:43.620 --> 00:03:45.920 align:start position:0%
about actually whenever you're figuring
out<00:03:43.680><c> the</c><00:03:43.770><c> bigger</c><00:03:43.920><c> notation</c><00:03:44.400><c> but</c><00:03:45.209><c> it's</c><00:03:45.690><c> also</c>

00:03:45.920 --> 00:03:45.930 align:start position:0%
out the bigger notation but it's also
 

00:03:45.930 --> 00:03:48.650 align:start position:0%
out the bigger notation but it's also
nice<00:03:46.320><c> to</c><00:03:46.380><c> know</c><00:03:46.709><c> to</c><00:03:47.400><c> kind</c><00:03:47.820><c> of</c><00:03:47.880><c> give</c><00:03:48.090><c> some</c><00:03:48.270><c> actual</c>

00:03:48.650 --> 00:03:48.660 align:start position:0%
nice to know to kind of give some actual
 

00:03:48.660 --> 00:03:50.540 align:start position:0%
nice to know to kind of give some actual
hard<00:03:48.870><c> examples</c><00:03:49.380><c> and</c><00:03:49.620><c> then</c><00:03:50.040><c> you</c><00:03:50.130><c> can</c><00:03:50.310><c> kind</c><00:03:50.520><c> of</c>

00:03:50.540 --> 00:03:50.550 align:start position:0%
hard examples and then you can kind of
 

00:03:50.550 --> 00:03:53.540 align:start position:0%
hard examples and then you can kind of
plot<00:03:50.790><c> them</c><00:03:51.000><c> and</c><00:03:51.239><c> see</c><00:03:51.540><c> that</c><00:03:52.280><c> clock</c><00:03:53.280><c> on</c><00:03:53.459><c> the</c>

00:03:53.540 --> 00:03:53.550 align:start position:0%
plot them and see that clock on the
 

00:03:53.550 --> 00:03:55.520 align:start position:0%
plot them and see that clock on the
graph<00:03:53.790><c> is</c><00:03:54.060><c> going</c><00:03:54.209><c> to</c><00:03:54.270><c> be</c><00:03:54.330><c> linear</c><00:03:54.510><c> time</c><00:03:54.750><c> anyways</c>

00:03:55.520 --> 00:03:55.530 align:start position:0%
graph is going to be linear time anyways
 

00:03:55.530 --> 00:03:59.780 align:start position:0%
graph is going to be linear time anyways
so<00:03:56.489><c> the</c><00:03:56.970><c> answer</c><00:03:57.239><c> to</c><00:03:57.390><c> this</c><00:03:57.510><c> one</c><00:03:57.570><c> is</c><00:03:58.350><c> Big</c><00:03:58.770><c> O</c><00:03:58.980><c> of</c><00:03:59.340><c> n</c>

00:03:59.780 --> 00:03:59.790 align:start position:0%
so the answer to this one is Big O of n
 

00:03:59.790 --> 00:04:02.270 align:start position:0%
so the answer to this one is Big O of n
all<00:04:00.690><c> right</c><00:04:00.780><c> I'll</c><00:04:01.020><c> just</c><00:04:01.200><c> drop</c><00:04:01.680><c> the</c><00:04:01.830><c> constant</c>

00:04:02.270 --> 00:04:02.280 align:start position:0%
all right I'll just drop the constant
 

00:04:02.280 --> 00:04:03.920 align:start position:0%
all right I'll just drop the constant
and<00:04:02.820><c> the</c><00:04:02.880><c> reason</c><00:04:03.180><c> is</c><00:04:03.300><c> if</c><00:04:03.420><c> you</c><00:04:03.450><c> substitute</c><00:04:03.870><c> the</c>

00:04:03.920 --> 00:04:03.930 align:start position:0%
and the reason is if you substitute the
 

00:04:03.930 --> 00:04:06.680 align:start position:0%
and the reason is if you substitute the
rate<00:04:04.080><c> at</c><00:04:04.230><c> length</c><00:04:04.410><c> or</c><00:04:04.709><c> some</c><00:04:05.040><c> number</c><00:04:05.420><c> and</c><00:04:06.420><c> plot</c>

00:04:06.680 --> 00:04:06.690 align:start position:0%
rate at length or some number and plot
 

00:04:06.690 --> 00:04:08.540 align:start position:0%
rate at length or some number and plot
it<00:04:06.900><c> it's</c><00:04:07.110><c> still</c><00:04:07.260><c> gonna</c><00:04:07.440><c> look</c><00:04:07.890><c> or</c><00:04:08.280><c> they</c><00:04:08.459><c> get</c>

00:04:08.540 --> 00:04:08.550 align:start position:0%
it it's still gonna look or they get
 

00:04:08.550 --> 00:04:11.930 align:start position:0%
it it's still gonna look or they get
linear<00:04:08.940><c> time</c><00:04:09.209><c> alright</c><00:04:10.370><c> okay</c><00:04:11.370><c> so</c><00:04:11.430><c> the</c><00:04:11.700><c> next</c><00:04:11.820><c> one</c>

00:04:11.930 --> 00:04:11.940 align:start position:0%
linear time alright okay so the next one
 

00:04:11.940 --> 00:04:13.280 align:start position:0%
linear time alright okay so the next one
here<00:04:12.239><c> has</c><00:04:12.480><c> some</c><00:04:12.630><c> a</c><00:04:12.660><c> little</c><00:04:12.750><c> bit</c><00:04:12.959><c> different</c><00:04:13.200><c> for</c>

00:04:13.280 --> 00:04:13.290 align:start position:0%
here has some a little bit different for
 

00:04:13.290 --> 00:04:17.000 align:start position:0%
here has some a little bit different for
you<00:04:13.410><c> I</c><00:04:14.209><c> want</c><00:04:15.209><c> you</c><00:04:15.360><c> to</c><00:04:15.690><c> have</c><00:04:16.290><c> four</c><00:04:16.620><c> different</c>

00:04:17.000 --> 00:04:17.010 align:start position:0%
you I want you to have four different
 

00:04:17.010 --> 00:04:19.909 align:start position:0%
you I want you to have four different
examples<00:04:17.070><c> here</c><00:04:17.430><c> and</c><00:04:17.760><c> I</c><00:04:18.180><c> want</c><00:04:18.419><c> you</c><00:04:18.540><c> to</c><00:04:18.919><c> see</c>

00:04:19.909 --> 00:04:19.919 align:start position:0%
examples here and I want you to see
 

00:04:19.919 --> 00:04:22.430 align:start position:0%
examples here and I want you to see
which<00:04:20.160><c> of</c><00:04:20.190><c> those</c><00:04:20.459><c> are</c><00:04:20.760><c> equivalent</c><00:04:21.180><c> to</c><00:04:21.810><c> Big</c><00:04:22.350><c> O</c>

00:04:22.430 --> 00:04:22.440 align:start position:0%
which of those are equivalent to Big O
 

00:04:22.440 --> 00:04:25.370 align:start position:0%
which of those are equivalent to Big O
of<00:04:22.470><c> n</c><00:04:22.740><c> so</c><00:04:23.490><c> go</c><00:04:23.610><c> ahead</c><00:04:23.669><c> and</c><00:04:23.820><c> pause</c><00:04:23.970><c> the</c><00:04:24.090><c> video</c><00:04:24.380><c> try</c>

00:04:25.370 --> 00:04:25.380 align:start position:0%
of n so go ahead and pause the video try
 

00:04:25.380 --> 00:04:26.120 align:start position:0%
of n so go ahead and pause the video try
and<00:04:25.590><c> work</c>

00:04:26.120 --> 00:04:26.130 align:start position:0%
and work
 

00:04:26.130 --> 00:04:28.880 align:start position:0%
and work
and<00:04:26.430><c> it's</c><00:04:26.910><c> basically</c><00:04:27.090><c> like</c><00:04:27.500><c> like</c><00:04:28.500><c> Chu</c><00:04:28.740><c> or</c>

00:04:28.880 --> 00:04:28.890 align:start position:0%
and it's basically like like Chu or
 

00:04:28.890 --> 00:04:31.220 align:start position:0%
and it's basically like like Chu or
false<00:04:29.130><c> for</c><00:04:29.400><c> these</c><00:04:29.520><c> if</c><00:04:29.760><c> it's</c><00:04:30.690><c> bigger</c><00:04:31.050><c> than</c><00:04:31.110><c> M</c>

00:04:31.220 --> 00:04:31.230 align:start position:0%
false for these if it's bigger than M
 

00:04:31.230 --> 00:04:34.100 align:start position:0%
false for these if it's bigger than M
plus<00:04:31.470><c> P</c><00:04:31.500><c> or</c><00:04:32.280><c> P</c><00:04:32.310><c> less</c><00:04:32.700><c> than</c><00:04:32.730><c> /</c><00:04:33.180><c> -</c><00:04:33.240><c> is</c><00:04:34.080><c> that</c>

00:04:34.100 --> 00:04:34.110 align:start position:0%
plus P or P less than / - is that
 

00:04:34.110 --> 00:04:36.020 align:start position:0%
plus P or P less than / - is that
equivalent<00:04:34.620><c> bigger</c><00:04:35.220><c> than</c><00:04:35.460><c> alright</c><00:04:35.820><c> and</c><00:04:35.970><c> do</c>

00:04:36.020 --> 00:04:36.030 align:start position:0%
equivalent bigger than alright and do
 

00:04:36.030 --> 00:04:37.940 align:start position:0%
equivalent bigger than alright and do
the<00:04:36.150><c> same</c><00:04:36.300><c> thing</c><00:04:36.540><c> for</c><00:04:36.630><c> all</c><00:04:36.750><c> of</c><00:04:36.780><c> them</c><00:04:36.990><c> and</c><00:04:37.800><c> when</c>

00:04:37.940 --> 00:04:37.950 align:start position:0%
the same thing for all of them and when
 

00:04:37.950 --> 00:04:39.590 align:start position:0%
the same thing for all of them and when
you're<00:04:38.040><c> ready</c><00:04:38.340><c> to</c><00:04:38.430><c> unpause</c><00:04:38.760><c> we'll</c><00:04:39.390><c> get</c><00:04:39.510><c> back</c>

00:04:39.590 --> 00:04:39.600 align:start position:0%
you're ready to unpause we'll get back
 

00:04:39.600 --> 00:04:41.480 align:start position:0%
you're ready to unpause we'll get back
to<00:04:39.750><c> it</c><00:04:39.900><c> and</c><00:04:39.990><c> figure</c><00:04:40.380><c> it</c><00:04:40.410><c> out</c><00:04:40.500><c> all</c><00:04:40.710><c> right</c><00:04:40.740><c> okay</c>

00:04:41.480 --> 00:04:41.490 align:start position:0%
to it and figure it out all right okay
 

00:04:41.490 --> 00:04:43.250 align:start position:0%
to it and figure it out all right okay
hope<00:04:41.730><c> you've</c><00:04:41.880><c> taken</c><00:04:42.120><c> the</c><00:04:42.420><c> time</c><00:04:42.570><c> to</c><00:04:43.110><c> try</c>

00:04:43.250 --> 00:04:43.260 align:start position:0%
hope you've taken the time to try
 

00:04:43.260 --> 00:04:45.350 align:start position:0%
hope you've taken the time to try
yourself<00:04:43.470><c> on</c><00:04:43.920><c> your</c><00:04:44.070><c> own</c><00:04:44.130><c> if</c><00:04:44.910><c> you're</c><00:04:45.060><c> not</c><00:04:45.180><c> quite</c>

00:04:45.350 --> 00:04:45.360 align:start position:0%
yourself on your own if you're not quite
 

00:04:45.360 --> 00:04:47.300 align:start position:0%
yourself on your own if you're not quite
sure<00:04:45.390><c> you're</c><00:04:45.810><c> stuck</c><00:04:46.080><c> we're</c><00:04:46.830><c> going</c><00:04:46.950><c> to</c><00:04:47.010><c> go</c><00:04:47.280><c> over</c>

00:04:47.300 --> 00:04:47.310 align:start position:0%
sure you're stuck we're going to go over
 

00:04:47.310 --> 00:04:50.510 align:start position:0%
sure you're stuck we're going to go over
now<00:04:47.460><c> so</c><00:04:48.620><c> let's</c><00:04:49.620><c> take</c><00:04:49.710><c> just</c><00:04:49.860><c> look</c><00:04:50.220><c> at</c><00:04:50.310><c> the</c><00:04:50.400><c> first</c>

00:04:50.510 --> 00:04:50.520 align:start position:0%
now so let's take just look at the first
 

00:04:50.520 --> 00:04:53.030 align:start position:0%
now so let's take just look at the first
one<00:04:50.730><c> we've</c><00:04:51.330><c> again</c><00:04:51.600><c> we</c><00:04:51.930><c> had</c><00:04:52.050><c> Big</c><00:04:52.260><c> O</c><00:04:52.380><c> n</c><00:04:52.590><c> plus</c><00:04:53.010><c> P</c>

00:04:53.030 --> 00:04:53.040 align:start position:0%
one we've again we had Big O n plus P
 

00:04:53.040 --> 00:04:55.460 align:start position:0%
one we've again we had Big O n plus P
remember<00:04:53.820><c> the</c><00:04:54.210><c> two</c><00:04:54.360><c> things</c><00:04:54.540><c> I</c><00:04:54.690><c> just</c><00:04:54.750><c> said</c><00:04:54.990><c> like</c>

00:04:55.460 --> 00:04:55.470 align:start position:0%
remember the two things I just said like
 

00:04:55.470 --> 00:04:58.070 align:start position:0%
remember the two things I just said like
a<00:04:55.650><c> minute</c><00:04:56.070><c> ago</c><00:04:56.160><c> or</c><00:04:56.820><c> drop</c><00:04:57.300><c> the</c><00:04:57.390><c> constants</c><00:04:57.870><c> and</c>

00:04:58.070 --> 00:04:58.080 align:start position:0%
a minute ago or drop the constants and
 

00:04:58.080 --> 00:05:00.710 align:start position:0%
a minute ago or drop the constants and
take<00:04:58.680><c> the</c><00:04:58.800><c> highest</c><00:04:59.160><c> order</c><00:04:59.340><c> of</c><00:04:59.430><c> operation</c><00:05:00.030><c> or</c>

00:05:00.710 --> 00:05:00.720 align:start position:0%
take the highest order of operation or
 

00:05:00.720 --> 00:05:03.260 align:start position:0%
take the highest order of operation or
highest<00:05:00.990><c> order</c><00:05:01.170><c> notation</c><00:05:01.620><c> okay</c><00:05:02.270><c> which</c>

00:05:03.260 --> 00:05:03.270 align:start position:0%
highest order notation okay which
 

00:05:03.270 --> 00:05:05.210 align:start position:0%
highest order notation okay which
basically<00:05:03.510><c> means</c><00:05:03.750><c> take</c><00:05:04.020><c> the</c><00:05:04.200><c> worst</c><00:05:04.530><c> case</c><00:05:04.950><c> of</c>

00:05:05.210 --> 00:05:05.220 align:start position:0%
basically means take the worst case of
 

00:05:05.220 --> 00:05:09.710 align:start position:0%
basically means take the worst case of
all<00:05:05.400><c> these</c><00:05:06.230><c> so</c><00:05:07.400><c> you</c><00:05:08.400><c> have</c><00:05:08.550><c> a</c><00:05:08.580><c> plus</c><00:05:08.760><c> B</c><00:05:08.820><c> is</c><00:05:09.330><c> the</c>

00:05:09.710 --> 00:05:09.720 align:start position:0%
all these so you have a plus B is the
 

00:05:09.720 --> 00:05:17.930 align:start position:0%
all these so you have a plus B is the
sink<00:05:10.020><c> we</c><00:05:10.230><c> can</c><00:05:10.380><c> say</c><00:05:10.820><c> Big</c><00:05:11.820><c> O</c><00:05:12.120><c> of</c><00:05:12.630><c> n</c><00:05:13.410><c> plus</c><00:05:15.110><c> or</c><00:05:16.110><c> P</c><00:05:16.940><c> is</c>

00:05:17.930 --> 00:05:17.940 align:start position:0%
sink we can say Big O of n plus or P is
 

00:05:17.940 --> 00:05:22.760 align:start position:0%
sink we can say Big O of n plus or P is
less<00:05:18.330><c> than</c><00:05:18.390><c> and</c><00:05:18.990><c> /</c><00:05:19.890><c> -</c><00:05:21.260><c> this</c><00:05:22.260><c> was</c><00:05:22.410><c> really</c><00:05:22.620><c> sloppy</c>

00:05:22.760 --> 00:05:22.770 align:start position:0%
less than and / - this was really sloppy
 

00:05:22.770 --> 00:05:24.800 align:start position:0%
less than and / - this was really sloppy
I<00:05:22.950><c> know</c><00:05:23.160><c> but</c><00:05:23.460><c> what</c><00:05:23.970><c> is</c><00:05:24.060><c> this</c><00:05:24.180><c> to</c><00:05:24.270><c> say</c><00:05:24.300><c> with</c><00:05:24.690><c> this</c>

00:05:24.800 --> 00:05:24.810 align:start position:0%
I know but what is this to say with this
 

00:05:24.810 --> 00:05:26.360 align:start position:0%
I know but what is this to say with this
thing<00:05:25.080><c> is</c><00:05:25.260><c> P</c><00:05:25.560><c> is</c><00:05:25.680><c> always</c><00:05:25.890><c> going</c><00:05:26.070><c> to</c><00:05:26.130><c> be</c><00:05:26.190><c> less</c>

00:05:26.360 --> 00:05:26.370 align:start position:0%
thing is P is always going to be less
 

00:05:26.370 --> 00:05:31.550 align:start position:0%
thing is P is always going to be less
than<00:05:26.610><c> half</c><00:05:27.000><c> of</c><00:05:27.300><c> n</c><00:05:27.800><c> all</c><00:05:28.800><c> right</c><00:05:29.090><c> so</c><00:05:30.090><c> we</c><00:05:31.020><c> have</c><00:05:31.050><c> we</c>

00:05:31.550 --> 00:05:31.560 align:start position:0%
than half of n all right so we have we
 

00:05:31.560 --> 00:05:37.400 align:start position:0%
than half of n all right so we have we
could<00:05:31.710><c> say</c><00:05:31.950><c> +</c><00:05:32.810><c> +</c><00:05:33.810><c> +</c><00:05:35.120><c> /</c><00:05:36.120><c> -</c><00:05:36.360><c> you</c><00:05:37.050><c> know</c><00:05:37.170><c> or</c>

00:05:37.400 --> 00:05:37.410 align:start position:0%
could say + + + / - you know or
 

00:05:37.410 --> 00:05:38.150 align:start position:0%
could say + + + / - you know or
something<00:05:37.620><c> like</c><00:05:37.710><c> that</c><00:05:37.830><c> doesn't</c><00:05:38.010><c> really</c>

00:05:38.150 --> 00:05:38.160 align:start position:0%
something like that doesn't really
 

00:05:38.160 --> 00:05:42.500 align:start position:0%
something like that doesn't really
matter<00:05:38.520><c> but</c><00:05:39.360><c> either</c><00:05:39.600><c> way</c><00:05:40.580><c> n</c><00:05:41.580><c> divided</c><00:05:41.970><c> by</c><00:05:42.000><c> 2</c><00:05:42.180><c> was</c>

00:05:42.500 --> 00:05:42.510 align:start position:0%
matter but either way n divided by 2 was
 

00:05:42.510 --> 00:05:45.410 align:start position:0%
matter but either way n divided by 2 was
less<00:05:42.930><c> than</c><00:05:43.200><c> him</c><00:05:43.440><c> so</c><00:05:44.160><c> we</c><00:05:44.460><c> don't</c><00:05:44.700><c> really</c><00:05:44.940><c> care</c>

00:05:45.410 --> 00:05:45.420 align:start position:0%
less than him so we don't really care
 

00:05:45.420 --> 00:05:47.180 align:start position:0%
less than him so we don't really care
we're<00:05:45.690><c> going</c><00:05:45.810><c> to</c><00:05:45.870><c> take</c><00:05:46.020><c> the</c><00:05:46.200><c> highest</c><00:05:46.410><c> order</c><00:05:46.740><c> of</c>

00:05:47.180 --> 00:05:47.190 align:start position:0%
we're going to take the highest order of
 

00:05:47.190 --> 00:05:50.150 align:start position:0%
we're going to take the highest order of
operation<00:05:47.820><c> right</c><00:05:48.390><c> ium</c><00:05:48.570><c> Sissel</c><00:05:48.870><c> is</c><00:05:48.960><c> just</c><00:05:49.230><c> n</c><00:05:49.380><c> +</c><00:05:49.920><c> n</c>

00:05:50.150 --> 00:05:50.160 align:start position:0%
operation right ium Sissel is just n + n
 

00:05:50.160 --> 00:05:53.510 align:start position:0%
operation right ium Sissel is just n + n
what's<00:05:51.030><c> actually</c><00:05:51.330><c> kind</c><00:05:51.510><c> of</c><00:05:51.540><c> nice</c><00:05:51.660><c> example</c><00:05:52.520><c> you</c>

00:05:53.510 --> 00:05:53.520 align:start position:0%
what's actually kind of nice example you
 

00:05:53.520 --> 00:05:55.100 align:start position:0%
what's actually kind of nice example you
know<00:05:53.640><c> that</c><00:05:53.850><c> be</c><00:05:53.940><c> 2</c><00:05:54.150><c> n</c><00:05:54.330><c> we'd</c><00:05:54.780><c> get</c><00:05:54.960><c> rid</c><00:05:55.080><c> of</c>

00:05:55.100 --> 00:05:55.110 align:start position:0%
know that be 2 n we'd get rid of
 

00:05:55.110 --> 00:05:59.570 align:start position:0%
know that be 2 n we'd get rid of
constant<00:05:55.680><c> anyways</c><00:05:56.100><c> but</c><00:05:56.870><c> for</c><00:05:57.870><c> this</c><00:05:57.930><c> example</c><00:05:58.580><c> P</c>

00:05:59.570 --> 00:05:59.580 align:start position:0%
constant anyways but for this example P
 

00:05:59.580 --> 00:06:01.220 align:start position:0%
constant anyways but for this example P
is<00:05:59.760><c> going</c><00:05:59.850><c> to</c><00:05:59.940><c> be</c><00:06:00.000><c> less</c><00:06:00.180><c> than</c><00:06:00.360><c> n</c><00:06:00.540><c> so</c><00:06:01.020><c> we're</c><00:06:01.200><c> just</c>

00:06:01.220 --> 00:06:01.230 align:start position:0%
is going to be less than n so we're just
 

00:06:01.230 --> 00:06:03.290 align:start position:0%
is going to be less than n so we're just
going<00:06:01.410><c> to</c><00:06:01.500><c> take</c><00:06:01.620><c> n</c><00:06:01.800><c> as</c><00:06:02.070><c> the</c><00:06:02.220><c> answer</c><00:06:02.550><c> all</c><00:06:03.240><c> right</c>

00:06:03.290 --> 00:06:03.300 align:start position:0%
going to take n as the answer all right
 

00:06:03.300 --> 00:06:05.120 align:start position:0%
going to take n as the answer all right
so<00:06:03.570><c> that</c><00:06:04.050><c> means</c><00:06:04.260><c> that</c><00:06:04.590><c> the</c><00:06:04.740><c> answer</c><00:06:04.830><c> for</c><00:06:04.950><c> the</c>

00:06:05.120 --> 00:06:05.130 align:start position:0%
so that means that the answer for the
 

00:06:05.130 --> 00:06:11.840 align:start position:0%
so that means that the answer for the
first<00:06:05.310><c> one</c><00:06:05.490><c> is</c><00:06:05.700><c> just</c><00:06:05.970><c> Big</c><00:06:06.270><c> O</c><00:06:06.510><c> of</c><00:06:06.920><c> M</c><00:06:09.170><c> okay</c><00:06:10.850><c> so</c>

00:06:11.840 --> 00:06:11.850 align:start position:0%
first one is just Big O of M okay so
 

00:06:11.850 --> 00:06:13.610 align:start position:0%
first one is just Big O of M okay so
let's<00:06:12.120><c> go</c><00:06:12.210><c> on</c><00:06:12.240><c> the</c><00:06:12.360><c> next</c><00:06:12.420><c> one</c><00:06:12.600><c> big</c><00:06:13.050><c> enough</c><00:06:13.200><c> to</c><00:06:13.410><c> 1</c>

00:06:13.610 --> 00:06:13.620 align:start position:0%
let's go on the next one big enough to 1
 

00:06:13.620 --> 00:06:16.910 align:start position:0%
let's go on the next one big enough to 1
so<00:06:14.340><c> ones</c><00:06:14.460><c> rather</c><00:06:14.640><c> simple</c><00:06:15.620><c> there's</c><00:06:16.620><c> nothing</c>

00:06:16.910 --> 00:06:16.920 align:start position:0%
so ones rather simple there's nothing
 

00:06:16.920 --> 00:06:18.140 align:start position:0%
so ones rather simple there's nothing
but<00:06:16.980><c> there's</c><00:06:17.130><c> much</c><00:06:17.370><c> difference</c><00:06:17.490><c> between</c><00:06:17.670><c> 2</c><00:06:18.000><c> n</c>

00:06:18.140 --> 00:06:18.150 align:start position:0%
but there's much difference between 2 n
 

00:06:18.150 --> 00:06:19.880 align:start position:0%
but there's much difference between 2 n
and<00:06:18.360><c> n</c><00:06:18.480><c> so</c><00:06:18.840><c> we</c><00:06:18.930><c> dropped</c><00:06:19.170><c> the</c><00:06:19.260><c> constants</c><00:06:19.710><c> and</c>

00:06:19.880 --> 00:06:19.890 align:start position:0%
and n so we dropped the constants and
 

00:06:19.890 --> 00:06:29.820 align:start position:0%
and n so we dropped the constants and
the<00:06:20.430><c> answer</c><00:06:20.700><c> is</c><00:06:21.350><c> Big</c><00:06:22.350><c> O</c><00:06:22.530><c> of</c><00:06:23.010><c> n</c><00:06:26.570><c> okay</c><00:06:27.710><c> so</c>

00:06:29.820 --> 00:06:29.830 align:start position:0%
the answer is Big O of n okay so
 

00:06:29.830 --> 00:06:32.340 align:start position:0%
the answer is Big O of n okay so
a<00:06:29.860><c> third</c><00:06:30.190><c> one</c><00:06:30.370><c> so</c><00:06:30.610><c> the</c><00:06:31.420><c> first</c><00:06:31.690><c> two</c><00:06:31.840><c> are</c><00:06:32.140><c> good</c>

00:06:32.340 --> 00:06:32.350 align:start position:0%
a third one so the first two are good
 

00:06:32.350 --> 00:06:35.220 align:start position:0%
a third one so the first two are good
the<00:06:33.040><c> first</c><00:06:33.250><c> two</c><00:06:33.630><c> they</c><00:06:34.630><c> are</c><00:06:34.750><c> equivalent</c><00:06:35.020><c> to</c>

00:06:35.220 --> 00:06:35.230 align:start position:0%
the first two they are equivalent to
 

00:06:35.230 --> 00:06:37.650 align:start position:0%
the first two they are equivalent to
bingo<00:06:35.560><c> them</c><00:06:35.770><c> so</c><00:06:36.490><c> the</c><00:06:36.580><c> third</c><00:06:36.760><c> one</c><00:06:36.880><c> is</c><00:06:37.090><c> a</c><00:06:37.330><c> kilo</c>

00:06:37.650 --> 00:06:37.660 align:start position:0%
bingo them so the third one is a kilo
 

00:06:37.660 --> 00:06:41.490 align:start position:0%
bingo them so the third one is a kilo
and<00:06:37.870><c> plus</c><00:06:38.350><c> log</c><00:06:38.590><c> of</c><00:06:38.710><c> n</c><00:06:38.830><c> now</c><00:06:39.820><c> log</c><00:06:40.360><c> of</c><00:06:40.510><c> n</c><00:06:40.630><c> is</c><00:06:40.780><c> good</c>

00:06:41.490 --> 00:06:41.500 align:start position:0%
and plus log of n now log of n is good
 

00:06:41.500 --> 00:06:44.280 align:start position:0%
and plus log of n now log of n is good
right<00:06:42.100><c> that's</c><00:06:42.250><c> better</c><00:06:42.520><c> than</c><00:06:42.820><c> n</c><00:06:43.030><c> and</c><00:06:43.930><c> so</c><00:06:44.140><c> one</c><00:06:44.260><c> of</c>

00:06:44.280 --> 00:06:44.290 align:start position:0%
right that's better than n and so one of
 

00:06:44.290 --> 00:06:46.470 align:start position:0%
right that's better than n and so one of
the<00:06:44.350><c> things</c><00:06:44.500><c> we</c><00:06:44.620><c> wanted</c><00:06:44.800><c> to</c><00:06:44.890><c> do</c><00:06:45.220><c> Big</c><00:06:46.060><c> O</c><00:06:46.180><c> takes</c>

00:06:46.470 --> 00:06:46.480 align:start position:0%
the things we wanted to do Big O takes
 

00:06:46.480 --> 00:06:48.600 align:start position:0%
the things we wanted to do Big O takes
the<00:06:46.600><c> worst</c><00:06:46.840><c> case</c><00:06:47.140><c> so</c><00:06:47.920><c> we</c><00:06:48.070><c> want</c><00:06:48.280><c> to</c><00:06:48.370><c> take</c><00:06:48.490><c> with</c>

00:06:48.600 --> 00:06:48.610 align:start position:0%
the worst case so we want to take with
 

00:06:48.610 --> 00:06:50.070 align:start position:0%
the worst case so we want to take with
the<00:06:48.730><c> highest</c><00:06:48.970><c> order</c>

00:06:50.070 --> 00:06:50.080 align:start position:0%
the highest order
 

00:06:50.080 --> 00:06:55.710 align:start position:0%
the highest order
what's<00:06:50.770><c> highest</c><00:06:51.070><c> order</c><00:06:51.220><c> operation</c><00:06:51.790><c> is</c><00:06:54.720><c> so</c>

00:06:55.710 --> 00:06:55.720 align:start position:0%
what's highest order operation is so
 

00:06:55.720 --> 00:06:59.010 align:start position:0%
what's highest order operation is so
that<00:06:55.810><c> means</c><00:06:56.020><c> Big</c><00:06:56.830><c> O</c><00:06:56.980><c> of</c><00:06:57.040><c> n</c><00:06:57.190><c> is</c><00:06:57.700><c> worse</c><00:06:58.510><c> than</c><00:06:58.840><c> the</c>

00:06:59.010 --> 00:06:59.020 align:start position:0%
that means Big O of n is worse than the
 

00:06:59.020 --> 00:07:01.980 align:start position:0%
that means Big O of n is worse than the
log<00:06:59.200><c> of</c><00:06:59.350><c> n</c><00:06:59.440><c> which</c><00:06:59.830><c> is</c><00:06:59.950><c> true</c><00:07:00.400><c> and</c><00:07:01.120><c> as</c><00:07:01.270><c> linear</c><00:07:01.480><c> log</c>

00:07:01.980 --> 00:07:01.990 align:start position:0%
log of n which is true and as linear log
 

00:07:01.990 --> 00:07:06.150 align:start position:0%
log of n which is true and as linear log
of<00:07:02.110><c> n</c><00:07:02.200><c> is</c><00:07:02.290><c> better</c><00:07:03.630><c> so</c><00:07:04.630><c> what</c><00:07:05.560><c> we</c><00:07:05.740><c> can</c><00:07:05.890><c> just</c>

00:07:06.150 --> 00:07:06.160 align:start position:0%
of n is better so what we can just
 

00:07:06.160 --> 00:07:09.000 align:start position:0%
of n is better so what we can just
eliminate<00:07:06.520><c> the</c><00:07:06.790><c> log</c><00:07:06.910><c> of</c><00:07:07.090><c> N</c><00:07:07.210><c> and</c><00:07:07.480><c> this</c><00:07:08.320><c> is</c><00:07:08.620><c> the</c>

00:07:09.000 --> 00:07:09.010 align:start position:0%
eliminate the log of N and this is the
 

00:07:09.010 --> 00:07:13.710 align:start position:0%
eliminate the log of N and this is the
same<00:07:09.250><c> as</c><00:07:09.580><c> saying</c><00:07:10.800><c> negative</c><00:07:11.800><c> n</c><00:07:12.660><c> all</c><00:07:13.660><c> right</c>

00:07:13.710 --> 00:07:13.720 align:start position:0%
same as saying negative n all right
 

00:07:13.720 --> 00:07:16.560 align:start position:0%
same as saying negative n all right
because<00:07:14.350><c> generally</c><00:07:14.680><c> speaking</c><00:07:15.450><c> when</c><00:07:16.450><c> we're</c>

00:07:16.560 --> 00:07:16.570 align:start position:0%
because generally speaking when we're
 

00:07:16.570 --> 00:07:19.770 align:start position:0%
because generally speaking when we're
trying<00:07:16.780><c> to</c><00:07:16.840><c> find</c><00:07:16.960><c> the</c><00:07:17.050><c> worst</c><00:07:17.230><c> case</c><00:07:17.500><c> we</c><00:07:18.780><c> we're</c>

00:07:19.770 --> 00:07:19.780 align:start position:0%
trying to find the worst case we we're
 

00:07:19.780 --> 00:07:22.590 align:start position:0%
trying to find the worst case we we're
not<00:07:19.900><c> as</c><00:07:20.020><c> worried</c><00:07:20.470><c> about</c><00:07:21.150><c> the</c><00:07:22.150><c> smaller</c>

00:07:22.590 --> 00:07:22.600 align:start position:0%
not as worried about the smaller
 

00:07:22.600 --> 00:07:26.220 align:start position:0%
not as worried about the smaller
notations<00:07:23.440><c> we</c><00:07:24.150><c> because</c><00:07:25.150><c> they</c><00:07:25.450><c> won't</c><00:07:25.600><c> affect</c>

00:07:26.220 --> 00:07:26.230 align:start position:0%
notations we because they won't affect
 

00:07:26.230 --> 00:07:29.760 align:start position:0%
notations we because they won't affect
the<00:07:26.530><c> time</c><00:07:26.740><c> flexin</c><00:07:27.100><c> e</c><00:07:27.190><c> as</c><00:07:27.340><c> much</c><00:07:28.110><c> as</c><00:07:29.110><c> just</c><00:07:29.440><c> taking</c>

00:07:29.760 --> 00:07:29.770 align:start position:0%
the time flexin e as much as just taking
 

00:07:29.770 --> 00:07:32.790 align:start position:0%
the time flexin e as much as just taking
the<00:07:29.860><c> worst</c><00:07:30.040><c> case</c><00:07:30.310><c> right</c><00:07:30.960><c> so</c><00:07:31.960><c> that's</c><00:07:32.170><c> why</c><00:07:32.320><c> you</c>

00:07:32.790 --> 00:07:32.800 align:start position:0%
the worst case right so that's why you
 

00:07:32.800 --> 00:07:35.690 align:start position:0%
the worst case right so that's why you
just<00:07:33.130><c> want</c><00:07:33.340><c> to</c><00:07:33.400><c> kind</c><00:07:33.670><c> of</c><00:07:33.700><c> drop</c><00:07:33.910><c> them</c><00:07:34.210><c> all</c><00:07:34.750><c> right</c>

00:07:35.690 --> 00:07:35.700 align:start position:0%
just want to kind of drop them all right
 

00:07:35.700 --> 00:07:39.240 align:start position:0%
just want to kind of drop them all right
so<00:07:36.700><c> that</c><00:07:36.820><c> one's</c><00:07:36.970><c> a</c><00:07:37.030><c> check</c><00:07:37.800><c> now</c><00:07:38.800><c> for</c><00:07:38.980><c> this</c><00:07:39.040><c> next</c>

00:07:39.240 --> 00:07:39.250 align:start position:0%
so that one's a check now for this next
 

00:07:39.250 --> 00:07:42.720 align:start position:0%
so that one's a check now for this next
one<00:07:39.400><c> we</c><00:07:39.640><c> had</c><00:07:39.730><c> Big</c><00:07:39.940><c> O</c><00:07:40.030><c> of</c><00:07:40.060><c> n</c><00:07:40.210><c> plus</c><00:07:40.750><c> M</c><00:07:41.580><c> what</c><00:07:42.580><c> does</c>

00:07:42.720 --> 00:07:42.730 align:start position:0%
one we had Big O of n plus M what does
 

00:07:42.730 --> 00:07:45.180 align:start position:0%
one we had Big O of n plus M what does
that<00:07:42.760><c> mean</c><00:07:43.120><c> here</c><00:07:43.450><c> that</c><00:07:43.810><c> this</c><00:07:44.530><c> is</c><00:07:44.680><c> something</c><00:07:44.890><c> we</c>

00:07:45.180 --> 00:07:45.190 align:start position:0%
that mean here that this is something we
 

00:07:45.190 --> 00:07:46.980 align:start position:0%
that mean here that this is something we
just<00:07:45.340><c> went</c><00:07:45.760><c> over</c><00:07:45.910><c> it</c><00:07:46.000><c> example</c><00:07:46.390><c> in</c><00:07:46.540><c> the</c><00:07:46.840><c> last</c>

00:07:46.980 --> 00:07:46.990 align:start position:0%
just went over it example in the last
 

00:07:46.990 --> 00:07:50.550 align:start position:0%
just went over it example in the last
video<00:07:47.290><c> about</c><00:07:47.560><c> this</c><00:07:49.020><c> this</c><00:07:50.020><c> means</c><00:07:50.290><c> that</c><00:07:50.380><c> there's</c>

00:07:50.550 --> 00:07:50.560 align:start position:0%
video about this this means that there's
 

00:07:50.560 --> 00:07:54.030 align:start position:0%
video about this this means that there's
two<00:07:50.860><c> different</c><00:07:51.280><c> inputs</c><00:07:51.670><c> here</c><00:07:52.260><c> one</c><00:07:53.260><c> it</c><00:07:53.890><c> looks</c>

00:07:54.030 --> 00:07:54.040 align:start position:0%
two different inputs here one it looks
 

00:07:54.040 --> 00:07:55.950 align:start position:0%
two different inputs here one it looks
like<00:07:54.160><c> whatever</c><00:07:54.550><c> the</c><00:07:54.730><c> end</c><00:07:54.880><c> input</c><00:07:55.180><c> is</c><00:07:55.300><c> in</c><00:07:55.480><c> the</c><00:07:55.660><c> M</c>

00:07:55.950 --> 00:07:55.960 align:start position:0%
like whatever the end input is in the M
 

00:07:55.960 --> 00:07:59.520 align:start position:0%
like whatever the end input is in the M
input<00:07:56.700><c> this</c><00:07:57.700><c> is</c><00:07:57.880><c> not</c><00:07:58.180><c> equivalent</c><00:07:58.750><c> to</c><00:07:59.050><c> Big</c><00:07:59.380><c> O</c><00:07:59.500><c> of</c>

00:07:59.520 --> 00:07:59.530 align:start position:0%
input this is not equivalent to Big O of
 

00:07:59.530 --> 00:08:02.370 align:start position:0%
input this is not equivalent to Big O of
n<00:07:59.680><c> because</c><00:08:00.610><c> we</c><00:08:00.640><c> can't</c><00:08:01.060><c> reduce</c><00:08:01.390><c> these</c><00:08:01.930><c> these</c>

00:08:02.370 --> 00:08:02.380 align:start position:0%
n because we can't reduce these these
 

00:08:02.380 --> 00:08:05.100 align:start position:0%
n because we can't reduce these these
inputs<00:08:02.800><c> are</c><00:08:02.890><c> distinct</c><00:08:03.610><c> from</c><00:08:04.450><c> each</c><00:08:04.510><c> other</c><00:08:04.840><c> and</c>

00:08:05.100 --> 00:08:05.110 align:start position:0%
inputs are distinct from each other and
 

00:08:05.110 --> 00:08:07.320 align:start position:0%
inputs are distinct from each other and
they're<00:08:05.440><c> both</c><00:08:05.590><c> need</c><00:08:05.830><c> to</c><00:08:05.920><c> be</c><00:08:06.010><c> included</c><00:08:06.550><c> so</c><00:08:07.000><c> we</c>

00:08:07.320 --> 00:08:07.330 align:start position:0%
they're both need to be included so we
 

00:08:07.330 --> 00:08:10.140 align:start position:0%
they're both need to be included so we
can't<00:08:08.170><c> reduce</c><00:08:08.410><c> this</c><00:08:08.770><c> anymore</c><00:08:09.280><c> we</c><00:08:09.610><c> don't</c><00:08:09.820><c> know</c>

00:08:10.140 --> 00:08:10.150 align:start position:0%
can't reduce this anymore we don't know
 

00:08:10.150 --> 00:08:11.430 align:start position:0%
can't reduce this anymore we don't know
the<00:08:10.300><c> relationship</c><00:08:10.750><c> between</c><00:08:10.780><c> them</c><00:08:11.170><c> and</c><00:08:11.350><c> we</c>

00:08:11.430 --> 00:08:11.440 align:start position:0%
the relationship between them and we
 

00:08:11.440 --> 00:08:12.660 align:start position:0%
the relationship between them and we
can't<00:08:11.650><c> assume</c><00:08:11.950><c> and</c><00:08:12.220><c> that</c><00:08:12.430><c> means</c><00:08:12.610><c> we</c>

00:08:12.660 --> 00:08:12.670 align:start position:0%
can't assume and that means we
 

00:08:12.670 --> 00:08:14.130 align:start position:0%
can't assume and that means we
definitely<00:08:13.090><c> can't</c><00:08:13.240><c> assume</c><00:08:13.570><c> the</c><00:08:13.750><c> relationship</c>

00:08:14.130 --> 00:08:14.140 align:start position:0%
definitely can't assume the relationship
 

00:08:14.140 --> 00:08:14.760 align:start position:0%
definitely can't assume the relationship
between<00:08:14.260><c> them</c>

00:08:14.760 --> 00:08:14.770 align:start position:0%
between them
 

00:08:14.770 --> 00:08:16.050 align:start position:0%
between them
all<00:08:14.980><c> we</c><00:08:15.340><c> know</c><00:08:15.490><c> is</c><00:08:15.610><c> they're</c><00:08:15.730><c> two</c><00:08:15.850><c> different</c>

00:08:16.050 --> 00:08:16.060 align:start position:0%
all we know is they're two different
 

00:08:16.060 --> 00:08:18.300 align:start position:0%
all we know is they're two different
inputs<00:08:16.150><c> and</c><00:08:16.570><c> we've</c><00:08:17.320><c> had</c><00:08:17.440><c> to</c><00:08:17.500><c> keep</c><00:08:17.650><c> it</c><00:08:17.860><c> big</c><00:08:18.100><c> U</c><00:08:18.190><c> of</c>

00:08:18.300 --> 00:08:18.310 align:start position:0%
inputs and we've had to keep it big U of
 

00:08:18.310 --> 00:08:22.740 align:start position:0%
inputs and we've had to keep it big U of
n<00:08:18.430><c> plus</c><00:08:18.760><c> M</c><00:08:20.370><c> so</c><00:08:21.370><c> that</c><00:08:21.790><c> is</c><00:08:21.850><c> non</c><00:08:22.240><c> equivalent</c>

00:08:22.740 --> 00:08:22.750 align:start position:0%
n plus M so that is non equivalent
 

00:08:22.750 --> 00:08:25.710 align:start position:0%
n plus M so that is non equivalent
alright<00:08:23.710><c> hope</c><00:08:24.370><c> you</c><00:08:24.490><c> learn</c><00:08:24.610><c> something</c><00:08:24.970><c> if</c><00:08:25.120><c> you</c>

00:08:25.710 --> 00:08:25.720 align:start position:0%
alright hope you learn something if you
 

00:08:25.720 --> 00:08:27.330 align:start position:0%
alright hope you learn something if you
have<00:08:25.900><c> any</c><00:08:26.140><c> other</c><00:08:26.380><c> questions</c><00:08:26.800><c> or</c><00:08:27.040><c> an</c><00:08:27.250><c> e</c>

00:08:27.330 --> 00:08:27.340 align:start position:0%
have any other questions or an e
 

00:08:27.340 --> 00:08:29.700 align:start position:0%
have any other questions or an e
clarification<00:08:28.030><c> or</c><00:08:28.270><c> whatever</c><00:08:28.690><c> put</c><00:08:29.560><c> them</c><00:08:29.650><c> in</c>

00:08:29.700 --> 00:08:29.710 align:start position:0%
clarification or whatever put them in
 

00:08:29.710 --> 00:08:31.200 align:start position:0%
clarification or whatever put them in
the<00:08:29.770><c> comments</c><00:08:30.100><c> below</c><00:08:30.160><c> and</c><00:08:30.520><c> I'll</c><00:08:30.940><c> be</c><00:08:31.030><c> more</c><00:08:31.150><c> than</c>

00:08:31.200 --> 00:08:31.210 align:start position:0%
the comments below and I'll be more than
 

00:08:31.210 --> 00:08:32.910 align:start position:0%
the comments below and I'll be more than
happy<00:08:31.450><c> to</c><00:08:31.600><c> get</c><00:08:31.960><c> to</c><00:08:32.050><c> you</c><00:08:32.140><c> as</c><00:08:32.260><c> soon</c><00:08:32.350><c> as</c><00:08:32.440><c> possible</c>

00:08:32.910 --> 00:08:32.920 align:start position:0%
happy to get to you as soon as possible
 

00:08:32.920 --> 00:08:37.170 align:start position:0%
happy to get to you as soon as possible
all<00:08:33.220><c> right</c><00:08:33.420><c> I'll</c><00:08:34.420><c> see</c><00:08:34.600><c> you</c><00:08:34.660><c> in</c><00:08:34.720><c> the</c><00:08:34.780><c> next</c><00:08:34.960><c> video</c>

