#!/usr/bin/env python3
"""
Test script for the ChatManager with conversation memory.

Demonstrates the Google GenAI SDK's built-in chat functionality 
that maintains conversation history automatically.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.text import Text
from dotenv import load_dotenv, find_dotenv

# Path setup for imports - ensure parent directory is in path for relative imports
script_dir = Path(__file__).resolve().parent
parent_dir = script_dir.parent
sys.path.insert(0, str(parent_dir))

# Import our chat manager - use absolute import to avoid relative import issues
try:
    from chat.chat_manager import Chat<PERSON>ana<PERSON>, create_simple_chat, quick_chat_exchange
except ImportError:
    # Fallback for direct execution - add current directory to path
    sys.path.insert(0, str(script_dir))
    from chat_manager import ChatManager, create_simple_chat, quick_chat_exchange

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

console = Console()

async def demonstrate_simple_chat():
    """Demonstrate basic chat functionality without persistence."""
    console.print("\n[bold cyan]🚀 Demo 1: Simple Chat Exchange[/bold cyan]")
    console.print("This demonstrates a quick chat exchange without session management.\n")
    
    # Get user input
    user_message = Prompt.ask("Enter your message")
    system_instruction = Prompt.ask("Enter system instruction (optional)", default="")
    
    try:
        # Use the quick chat function
        response = await quick_chat_exchange(
            message=user_message,
            system_instruction=system_instruction if system_instruction else None
        )
        
        console.print(Panel(response, title="🤖 Assistant Response", border_style="green"))
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

async def demonstrate_persistent_chat():
    """Demonstrate persistent chat with conversation memory."""
    console.print("\n[bold cyan]🧠 Demo 2: Persistent Chat with Memory[/bold cyan]")
    console.print("This demonstrates a chat session that remembers previous messages.\n")
    
    # Get initial setup
    system_instruction = Prompt.ask("Enter system instruction (optional)", default="")
    
    try:
        # Create a chat manager and session
        manager, session_id = await create_simple_chat(
            system_instruction=system_instruction if system_instruction else None
        )
        
        console.print(f"[green]✅ Created chat session: {session_id}[/green]\n")
        
        # Chat loop
        message_count = 0
        while True:
            # Get user input
            user_input = Prompt.ask(f"[bold blue]Message {message_count + 1}[/bold blue] (or 'quit' to exit)")
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            try:
                # Send message and get response
                response, input_tokens, output_tokens, cost = await manager.send_message(
                    session_id, user_input
                )
                
                # Display response with metadata
                response_panel = Panel(
                    response,
                    title=f"🤖 Assistant Response #{message_count + 1}",
                    border_style="green"
                )
                console.print(response_panel)
                
                # Display token and cost info
                token_info = f"Tokens: {input_tokens or 'N/A'} input, {output_tokens or 'N/A'} output"
                if cost:
                    token_info += f" | Cost: ${cost:.6f}"
                console.print(f"[dim]{token_info}[/dim]\n")
                
                message_count += 1
                
            except Exception as e:
                console.print(f"[red]Error sending message: {e}[/red]")
        
        # Show session summary
        await show_session_summary(manager, session_id)
        
        # Close the session
        await manager.close_session(session_id)
        console.print(f"[yellow]Session {session_id} closed.[/yellow]")
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

async def demonstrate_chat_with_context():
    """Demonstrate chat that builds context over multiple exchanges."""
    console.print("\n[bold cyan]📚 Demo 3: Context-Building Chat[/bold cyan]")
    console.print("This shows how the chat remembers context from previous messages.\n")
    
    try:
        manager, session_id = await create_simple_chat(
            system_instruction="You are a helpful math tutor. Remember the student's learning progress."
        )
        
        # Predefined conversation to show context building
        exchanges = [
            "Hi! I'm struggling with algebra. Can you help me understand what variables are?",
            "That's helpful! Now can you give me an example using the variable concept you just explained?",
            "Perfect! Now, based on what we've discussed about variables, how would I solve: 2x + 5 = 11?",
            "Great! Can you now create a similar problem for me to practice, using the same concepts?"
        ]
        
        for i, message in enumerate(exchanges, 1):
            console.print(f"\n[bold blue]👤 Student Message {i}:[/bold blue]")
            console.print(Panel(message, border_style="blue"))
            
            # Send message
            response, input_tokens, output_tokens, cost = await manager.send_message(
                session_id, message
            )
            
            console.print(f"\n[bold green]🤖 Tutor Response {i}:[/bold green]")
            console.print(Panel(response, border_style="green"))
            
            # Show token usage
            console.print(f"[dim]Tokens: {input_tokens}/{output_tokens} | Cost: ${cost or 0:.6f}[/dim]")
        
        # Show full conversation history
        await show_conversation_history(manager, session_id)
        
        # Close session
        await manager.close_session(session_id)
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

async def demonstrate_session_management():
    """Demonstrate session management features."""
    console.print("\n[bold cyan]⚙️ Demo 4: Session Management[/bold cyan]")
    console.print("This shows session creation, listing, and restoration.\n")
    
    try:
        manager = ChatManager()
        
        # Create multiple sessions
        sessions = []
        for i in range(3):
            session_id = await manager.create_chat_session(
                system_instruction=f"You are assistant #{i+1}. Always mention your number in responses."
            )
            sessions.append(session_id)
            console.print(f"[green]Created session {i+1}: {session_id[:8]}...[/green]")
        
        # Send a message to each session
        for i, session_id in enumerate(sessions):
            response, _, _, _ = await manager.send_message(
                session_id, f"Hello! This is my first message to assistant {i+1}."
            )
            console.print(f"[dim]Response from session {i+1}: {response[:50]}...[/dim]")
        
        # List active sessions
        console.print("\n[bold yellow]📋 Active Sessions:[/bold yellow]")
        active_sessions = await manager.list_active_sessions()
        for session in active_sessions:
            console.print(f"  • {session.session_id[:8]}... | Model: {session.model_name} | Messages: {session.total_messages}")
        
        # Demonstrate session restoration (simulation)
        test_session = sessions[0]
        console.print(f"\n[bold yellow]🔄 Simulating session restoration for: {test_session[:8]}...[/bold yellow]")
        
        # Get session info
        session_info = await manager.get_session_info(test_session)
        console.print(f"Session created: {session_info.created_at}")
        console.print(f"Total cost: ${session_info.total_cost:.6f}")
        console.print(f"Total tokens: {session_info.total_input_tokens + session_info.total_output_tokens}")
        
        # Close all sessions
        for session_id in sessions:
            await manager.close_session(session_id)
        
        console.print("\n[green]✅ All sessions closed successfully.[/green]")
        
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

async def show_session_summary(manager: ChatManager, session_id: str):
    """Show a summary of the chat session."""
    try:
        summary = manager.get_session_summary(session_id)
        
        console.print("\n[bold yellow]📊 Session Summary:[/bold yellow]")
        console.print(f"  • Session ID: {summary['session_id'][:8]}...")
        console.print(f"  • Model: {summary['model_name']}")
        console.print(f"  • Messages: {summary['total_messages']}")
        console.print(f"  • Total Tokens: {summary['total_input_tokens'] + summary['total_output_tokens']}")
        console.print(f"  • Total Cost: ${summary['total_cost']:.6f}")
        console.print(f"  • Duration: {summary['created_at']} to {summary['last_message_at']}")
        
    except Exception as e:
        console.print(f"[red]Error getting session summary: {e}[/red]")

async def show_conversation_history(manager: ChatManager, session_id: str):
    """Show the full conversation history."""
    try:
        history = await manager.get_chat_history(session_id)
        
        console.print("\n[bold yellow]💬 Conversation History:[/bold yellow]")
        for i, msg in enumerate(history, 1):
            role_emoji = "👤" if msg.role == "user" else "🤖"
            role_name = "User" if msg.role == "user" else "Assistant"
            
            console.print(f"\n[bold]{role_emoji} {role_name} (Message {i}):[/bold]")
            console.print(Panel(
                msg.content,
                border_style="blue" if msg.role == "user" else "green",
                title=f"{msg.timestamp.strftime('%H:%M:%S')}"
            ))
            
            if msg.token_count:
                console.print(f"[dim]Tokens: {msg.token_count}[/dim]")
        
    except Exception as e:
        console.print(f"[red]Error getting conversation history: {e}[/red]")

async def main():
    """Main demo runner."""
    console.print(Panel.fit(
        "[bold cyan]Google GenAI Chat Manager Demo[/bold cyan]\n\n"
        "This demo showcases the built-in conversation management capabilities\n"
        "of the Google GenAI SDK, which automatically maintains conversation\n"
        "history and context across multiple API calls.",
        border_style="cyan"
    ))
    
    # Load environment variables
    load_dotenv(find_dotenv(filename='.env.local', raise_error_if_not_found=False) or 
                find_dotenv(filename='.env', raise_error_if_not_found=False))
    
    # Check API key
    api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
    if not api_key:
        console.print("[red]❌ Error: No API key found. Set GOOGLE_API_KEY or GEMINI_API_KEY.[/red]")
        return
    
    # Menu loop
    while True:
        console.print("\n[bold yellow]Choose a demo:[/bold yellow]")
        console.print("  1. Simple Chat Exchange")
        console.print("  2. Persistent Chat with Memory")
        console.print("  3. Context-Building Chat")
        console.print("  4. Session Management")
        console.print("  5. Exit")
        
        choice = Prompt.ask("Enter your choice", choices=["1", "2", "3", "4", "5"])
        
        try:
            if choice == "1":
                await demonstrate_simple_chat()
            elif choice == "2":
                await demonstrate_persistent_chat()
            elif choice == "3":
                await demonstrate_chat_with_context()
            elif choice == "4":
                await demonstrate_session_management()
            elif choice == "5":
                console.print("[cyan]👋 Goodbye![/cyan]")
                break
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Demo interrupted by user.[/yellow]")
            break
        except Exception as e:
            console.print(f"[red]Unexpected error: {e}[/red]")
            logger.exception("Demo error")

if __name__ == "__main__":
    asyncio.run(main()) 