WEBVTT
Kind: captions
Language: en

00:00:04.799 --> 00:00:07.610 align:start position:0%
 
hi<00:00:05.339><c> guys</c><00:00:05.580><c> David</c><00:00:05.880><c> here</c><00:00:06.600><c> takes</c><00:00:07.140><c> about</c><00:00:07.319><c> five</c>

00:00:07.610 --> 00:00:07.620 align:start position:0%
hi guys David here takes about five
 

00:00:07.620 --> 00:00:09.169 align:start position:0%
hi guys David here takes about five
minutes<00:00:07.799><c> to</c><00:00:08.160><c> build</c><00:00:08.280><c> a</c><00:00:08.519><c> semantic</c><00:00:09.000><c> search</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
minutes to build a semantic search
 

00:00:09.179 --> 00:00:11.209 align:start position:0%
minutes to build a semantic search
engine<00:00:09.420><c> with</c><00:00:09.720><c> quadrant</c><00:00:10.139><c> if</c><00:00:10.920><c> you're</c><00:00:11.040><c> a</c>

00:00:11.209 --> 00:00:11.219 align:start position:0%
engine with quadrant if you're a
 

00:00:11.219 --> 00:00:12.530 align:start position:0%
engine with quadrant if you're a
complete<00:00:11.519><c> beginner</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
complete beginner
 

00:00:12.540 --> 00:00:14.990 align:start position:0%
complete beginner
stick<00:00:12.900><c> around</c><00:00:13.139><c> and</c><00:00:13.920><c> watch</c><00:00:14.219><c> me</c><00:00:14.400><c> do</c><00:00:14.580><c> it</c><00:00:14.700><c> in</c><00:00:14.820><c> six</c>

00:00:14.990 --> 00:00:15.000 align:start position:0%
stick around and watch me do it in six
 

00:00:15.000 --> 00:00:17.870 align:start position:0%
stick around and watch me do it in six
steps

00:00:17.870 --> 00:00:17.880 align:start position:0%
 
 

00:00:17.880 --> 00:00:19.430 align:start position:0%
 
to<00:00:18.240><c> begin</c><00:00:18.359><c> we're</c><00:00:18.660><c> going</c><00:00:18.840><c> to</c><00:00:18.960><c> have</c><00:00:19.020><c> to</c><00:00:19.260><c> install</c>

00:00:19.430 --> 00:00:19.440 align:start position:0%
to begin we're going to have to install
 

00:00:19.440 --> 00:00:22.250 align:start position:0%
to begin we're going to have to install
a<00:00:19.800><c> few</c><00:00:19.980><c> dependencies</c><00:00:20.699><c> so</c><00:00:21.240><c> whatever</c><00:00:21.600><c> data</c><00:00:22.140><c> you</c>

00:00:22.250 --> 00:00:22.260 align:start position:0%
a few dependencies so whatever data you
 

00:00:22.260 --> 00:00:24.470 align:start position:0%
a few dependencies so whatever data you
feed<00:00:22.500><c> to</c><00:00:22.740><c> the</c><00:00:22.920><c> search</c><00:00:23.039><c> engine</c><00:00:23.340><c> it</c><00:00:24.000><c> first</c><00:00:24.119><c> needs</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
feed to the search engine it first needs
 

00:00:24.480 --> 00:00:25.849 align:start position:0%
feed to the search engine it first needs
to<00:00:24.539><c> be</c><00:00:24.660><c> processed</c>

00:00:25.849 --> 00:00:25.859 align:start position:0%
to be processed
 

00:00:25.859 --> 00:00:28.670 align:start position:0%
to be processed
the<00:00:26.400><c> sentence</c><00:00:26.760><c> Transformers</c><00:00:27.539><c> framework</c><00:00:28.140><c> will</c>

00:00:28.670 --> 00:00:28.680 align:start position:0%
the sentence Transformers framework will
 

00:00:28.680 --> 00:00:30.830 align:start position:0%
the sentence Transformers framework will
give<00:00:28.920><c> you</c><00:00:29.039><c> access</c><00:00:29.279><c> to</c><00:00:29.820><c> Common</c><00:00:30.060><c> models</c><00:00:30.660><c> that</c>

00:00:30.830 --> 00:00:30.840 align:start position:0%
give you access to Common models that
 

00:00:30.840 --> 00:00:38.830 align:start position:0%
give you access to Common models that
turn<00:00:31.019><c> raw</c><00:00:31.439><c> data</c><00:00:31.800><c> into</c><00:00:31.920><c> embeddings</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
 
 

00:00:38.840 --> 00:00:41.270 align:start position:0%
 
all<00:00:39.840><c> right</c><00:00:39.960><c> good</c><00:00:40.200><c> now</c><00:00:40.440><c> that's</c><00:00:40.739><c> installed</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
all right good now that's installed
 

00:00:41.280 --> 00:00:44.389 align:start position:0%
all right good now that's installed
let's<00:00:41.940><c> move</c><00:00:42.180><c> on</c><00:00:42.360><c> once</c><00:00:43.020><c> encoded</c><00:00:43.559><c> your</c><00:00:44.100><c> data</c>

00:00:44.389 --> 00:00:44.399 align:start position:0%
let's move on once encoded your data
 

00:00:44.399 --> 00:00:46.130 align:start position:0%
let's move on once encoded your data
needs<00:00:44.579><c> to</c><00:00:44.640><c> be</c><00:00:44.760><c> kept</c><00:00:45.059><c> somewhere</c><00:00:45.360><c> and</c><00:00:46.020><c> we're</c>

00:00:46.130 --> 00:00:46.140 align:start position:0%
needs to be kept somewhere and we're
 

00:00:46.140 --> 00:00:48.049 align:start position:0%
needs to be kept somewhere and we're
going<00:00:46.260><c> to</c><00:00:46.379><c> be</c><00:00:46.440><c> using</c><00:00:46.739><c> quadrant</c><00:00:47.280><c> to</c><00:00:47.460><c> store</c><00:00:47.640><c> data</c>

00:00:48.049 --> 00:00:48.059 align:start position:0%
going to be using quadrant to store data
 

00:00:48.059 --> 00:00:49.430 align:start position:0%
going to be using quadrant to store data
as<00:00:48.239><c> embeddings</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
as embeddings
 

00:00:49.440 --> 00:00:51.830 align:start position:0%
as embeddings
you<00:00:50.160><c> can</c><00:00:50.280><c> also</c><00:00:50.460><c> use</c><00:00:50.640><c> quadrant</c><00:00:51.239><c> to</c><00:00:51.420><c> run</c><00:00:51.600><c> search</c>

00:00:51.830 --> 00:00:51.840 align:start position:0%
you can also use quadrant to run search
 

00:00:51.840 --> 00:00:54.170 align:start position:0%
you can also use quadrant to run search
queries<00:00:52.379><c> against</c><00:00:52.559><c> this</c><00:00:52.920><c> data</c><00:00:53.280><c> this</c><00:00:53.940><c> means</c>

00:00:54.170 --> 00:00:54.180 align:start position:0%
queries against this data this means
 

00:00:54.180 --> 00:00:55.970 align:start position:0%
queries against this data this means
that<00:00:54.300><c> you</c><00:00:54.480><c> can</c><00:00:54.600><c> ask</c><00:00:54.840><c> the</c><00:00:55.079><c> engine</c><00:00:55.260><c> to</c><00:00:55.680><c> give</c><00:00:55.800><c> you</c>

00:00:55.970 --> 00:00:55.980 align:start position:0%
that you can ask the engine to give you
 

00:00:55.980 --> 00:00:57.830 align:start position:0%
that you can ask the engine to give you
relevant<00:00:56.399><c> answers</c><00:00:56.760><c> that</c><00:00:57.059><c> go</c><00:00:57.239><c> Way</c><00:00:57.539><c> Beyond</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
relevant answers that go Way Beyond
 

00:00:57.840 --> 00:00:59.510 align:start position:0%
relevant answers that go Way Beyond
keyword<00:00:58.260><c> matching</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
keyword matching
 

00:00:59.520 --> 00:01:11.469 align:start position:0%
keyword matching
so<00:00:59.879><c> let's</c><00:01:00.120><c> install</c><00:01:00.420><c> the</c><00:01:00.780><c> quadrant</c><00:01:01.260><c> client</c><00:01:01.620><c> now</c>

00:01:11.469 --> 00:01:11.479 align:start position:0%
 
 

00:01:11.479 --> 00:01:14.330 align:start position:0%
 
okay<00:01:12.479><c> once</c><00:01:12.960><c> the</c><00:01:13.200><c> two</c><00:01:13.320><c> main</c><00:01:13.560><c> Frameworks</c><00:01:14.220><c> are</c>

00:01:14.330 --> 00:01:14.340 align:start position:0%
okay once the two main Frameworks are
 

00:01:14.340 --> 00:01:16.609 align:start position:0%
okay once the two main Frameworks are
defined<00:01:14.820><c> you</c><00:01:15.360><c> need</c><00:01:15.479><c> to</c><00:01:15.659><c> specify</c><00:01:16.080><c> the</c><00:01:16.320><c> exact</c>

00:01:16.609 --> 00:01:16.619 align:start position:0%
defined you need to specify the exact
 

00:01:16.619 --> 00:01:19.250 align:start position:0%
defined you need to specify the exact
models<00:01:17.100><c> that</c><00:01:17.400><c> this</c><00:01:17.520><c> engine</c><00:01:17.700><c> will</c><00:01:18.119><c> use</c>

00:01:19.250 --> 00:01:19.260 align:start position:0%
models that this engine will use
 

00:01:19.260 --> 00:01:38.350 align:start position:0%
models that this engine will use
so<00:01:19.860><c> let's</c><00:01:19.979><c> import</c><00:01:20.220><c> some</c><00:01:20.640><c> models</c>

00:01:38.350 --> 00:01:38.360 align:start position:0%
 
 

00:01:38.360 --> 00:01:42.170 align:start position:0%
 
okay<00:01:39.360><c> now</c><00:01:39.840><c> we</c><00:01:40.140><c> have</c><00:01:40.259><c> to</c><00:01:40.439><c> add</c><00:01:40.619><c> the</c><00:01:40.799><c> data</c><00:01:41.100><c> set</c>

00:01:42.170 --> 00:01:42.180 align:start position:0%
okay now we have to add the data set
 

00:01:42.180 --> 00:01:44.270 align:start position:0%
okay now we have to add the data set
our<00:01:42.540><c> large</c><00:01:42.780><c> language</c><00:01:42.960><c> model</c><00:01:43.380><c> will</c><00:01:43.740><c> encode</c><00:01:44.100><c> the</c>

00:01:44.270 --> 00:01:44.280 align:start position:0%
our large language model will encode the
 

00:01:44.280 --> 00:01:47.149 align:start position:0%
our large language model will encode the
data<00:01:44.579><c> you</c><00:01:44.759><c> provide</c><00:01:45.320><c> so</c><00:01:46.320><c> here</c><00:01:46.619><c> we're</c><00:01:46.860><c> going</c><00:01:47.040><c> to</c>

00:01:47.149 --> 00:01:47.159 align:start position:0%
data you provide so here we're going to
 

00:01:47.159 --> 00:01:48.950 align:start position:0%
data you provide so here we're going to
list<00:01:47.340><c> all</c><00:01:47.759><c> the</c><00:01:47.880><c> science</c><00:01:48.060><c> fiction</c><00:01:48.360><c> books</c><00:01:48.840><c> in</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
list all the science fiction books in
 

00:01:48.960 --> 00:01:50.090 align:start position:0%
list all the science fiction books in
your<00:01:49.140><c> library</c>

00:01:50.090 --> 00:01:50.100 align:start position:0%
your library
 

00:01:50.100 --> 00:01:52.670 align:start position:0%
your library
each<00:01:50.520><c> book</c><00:01:50.700><c> has</c><00:01:50.939><c> a</c><00:01:51.119><c> metadata</c><00:01:51.720><c> a</c><00:01:52.140><c> name</c><00:01:52.320><c> an</c>

00:01:52.670 --> 00:01:52.680 align:start position:0%
each book has a metadata a name an
 

00:01:52.680 --> 00:01:55.370 align:start position:0%
each book has a metadata a name an
author<00:01:53.040><c> and</c><00:01:53.340><c> a</c><00:01:53.520><c> publication</c><00:01:53.939><c> year</c><00:01:54.240><c> as</c><00:01:55.079><c> well</c><00:01:55.259><c> as</c>

00:01:55.370 --> 00:01:55.380 align:start position:0%
author and a publication year as well as
 

00:01:55.380 --> 00:02:01.249 align:start position:0%
author and a publication year as well as
a<00:01:55.500><c> short</c><00:01:55.680><c> description</c>

00:02:01.249 --> 00:02:01.259 align:start position:0%
 
 

00:02:01.259 --> 00:02:03.950 align:start position:0%
 
now<00:02:02.159><c> that</c><00:02:02.340><c> you've</c><00:02:02.520><c> added</c><00:02:02.820><c> your</c><00:02:02.939><c> data</c><00:02:03.299><c> you</c><00:02:03.840><c> need</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
now that you've added your data you need
 

00:02:03.960 --> 00:02:06.410 align:start position:0%
now that you've added your data you need
somewhere<00:02:04.439><c> to</c><00:02:04.740><c> store</c><00:02:04.860><c> the</c><00:02:05.100><c> embeddings</c>

00:02:06.410 --> 00:02:06.420 align:start position:0%
somewhere to store the embeddings
 

00:02:06.420 --> 00:02:08.330 align:start position:0%
somewhere to store the embeddings
we're<00:02:06.840><c> going</c><00:02:06.960><c> to</c><00:02:07.020><c> be</c><00:02:07.140><c> using</c><00:02:07.439><c> quadrant</c><00:02:07.920><c> as</c><00:02:08.160><c> a</c>

00:02:08.330 --> 00:02:08.340 align:start position:0%
we're going to be using quadrant as a
 

00:02:08.340 --> 00:02:11.229 align:start position:0%
we're going to be using quadrant as a
vector<00:02:08.640><c> database</c>

00:02:11.229 --> 00:02:11.239 align:start position:0%
 
 

00:02:11.239 --> 00:02:13.670 align:start position:0%
 
here<00:02:12.239><c> we're</c><00:02:12.420><c> going</c><00:02:12.540><c> to</c><00:02:12.660><c> configure</c><00:02:13.080><c> quadrant</c>

00:02:13.670 --> 00:02:13.680 align:start position:0%
here we're going to configure quadrant
 

00:02:13.680 --> 00:02:15.589 align:start position:0%
here we're going to configure quadrant
to<00:02:13.980><c> tell</c><00:02:14.220><c> the</c><00:02:14.459><c> database</c><00:02:15.000><c> where</c><00:02:15.239><c> to</c><00:02:15.420><c> store</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
to tell the database where to store
 

00:02:15.599 --> 00:02:16.729 align:start position:0%
to tell the database where to store
embeddings

00:02:16.729 --> 00:02:16.739 align:start position:0%
embeddings
 

00:02:16.739 --> 00:02:18.770 align:start position:0%
embeddings
this<00:02:17.040><c> is</c><00:02:17.220><c> a</c><00:02:17.340><c> very</c><00:02:17.459><c> basic</c><00:02:17.760><c> demo</c><00:02:18.120><c> so</c><00:02:18.239><c> your</c><00:02:18.540><c> local</c>

00:02:18.770 --> 00:02:18.780 align:start position:0%
this is a very basic demo so your local
 

00:02:18.780 --> 00:02:20.990 align:start position:0%
this is a very basic demo so your local
computer<00:02:19.080><c> is</c><00:02:19.500><c> good</c><00:02:19.680><c> enough</c><00:02:19.860><c> and</c><00:02:20.220><c> will</c><00:02:20.400><c> use</c><00:02:20.640><c> its</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
computer is good enough and will use its
 

00:02:21.000 --> 00:02:29.390 align:start position:0%
computer is good enough and will use its
memory<00:02:21.239><c> as</c><00:02:21.480><c> temporary</c><00:02:22.020><c> storage</c>

00:02:29.390 --> 00:02:29.400 align:start position:0%
 
 

00:02:29.400 --> 00:02:31.790 align:start position:0%
 
now<00:02:30.000><c> the</c><00:02:30.239><c> database</c><00:02:30.660><c> knows</c><00:02:31.260><c> where</c><00:02:31.440><c> to</c><00:02:31.620><c> store</c>

00:02:31.790 --> 00:02:31.800 align:start position:0%
now the database knows where to store
 

00:02:31.800 --> 00:02:33.890 align:start position:0%
now the database knows where to store
its<00:02:32.160><c> embeddings</c><00:02:32.520><c> we're</c><00:02:33.239><c> going</c><00:02:33.420><c> to</c><00:02:33.599><c> go</c><00:02:33.720><c> and</c>

00:02:33.890 --> 00:02:33.900 align:start position:0%
its embeddings we're going to go and
 

00:02:33.900 --> 00:02:36.170 align:start position:0%
its embeddings we're going to go and
create<00:02:34.140><c> a</c><00:02:34.500><c> collection</c>

00:02:36.170 --> 00:02:36.180 align:start position:0%
create a collection
 

00:02:36.180 --> 00:02:38.510 align:start position:0%
create a collection
all<00:02:36.780><c> data</c><00:02:37.140><c> in</c><00:02:37.260><c> quadrant</c><00:02:37.680><c> is</c><00:02:37.920><c> organized</c><00:02:38.340><c> by</c>

00:02:38.510 --> 00:02:38.520 align:start position:0%
all data in quadrant is organized by
 

00:02:38.520 --> 00:02:41.390 align:start position:0%
all data in quadrant is organized by
collections<00:02:39.540><c> in</c><00:02:40.080><c> this</c><00:02:40.200><c> case</c><00:02:40.379><c> you're</c><00:02:41.160><c> going</c><00:02:41.280><c> to</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
collections in this case you're going to
 

00:02:41.400 --> 00:02:43.369 align:start position:0%
collections in this case you're going to
be<00:02:41.459><c> storing</c><00:02:41.819><c> books</c><00:02:42.239><c> so</c><00:02:42.780><c> let's</c><00:02:42.959><c> call</c><00:02:43.200><c> this</c>

00:02:43.369 --> 00:02:43.379 align:start position:0%
be storing books so let's call this
 

00:02:43.379 --> 00:02:50.089 align:start position:0%
be storing books so let's call this
collection<00:02:43.739><c> my</c><00:02:43.980><c> books</c>

00:02:50.089 --> 00:02:50.099 align:start position:0%
 
 

00:02:50.099 --> 00:02:52.970 align:start position:0%
 
so<00:02:50.760><c> now</c><00:02:50.940><c> that</c><00:02:51.239><c> we</c><00:02:51.420><c> have</c><00:02:51.540><c> created</c><00:02:51.840><c> a</c><00:02:52.019><c> collection</c>

00:02:52.970 --> 00:02:52.980 align:start position:0%
so now that we have created a collection
 

00:02:52.980 --> 00:02:55.369 align:start position:0%
so now that we have created a collection
we<00:02:53.400><c> need</c><00:02:53.519><c> to</c><00:02:53.640><c> upload</c><00:02:53.940><c> our</c><00:02:54.120><c> data</c><00:02:54.420><c> to</c><00:02:54.599><c> it</c>

00:02:55.369 --> 00:02:55.379 align:start position:0%
we need to upload our data to it
 

00:02:55.379 --> 00:02:57.470 align:start position:0%
we need to upload our data to it
let's<00:02:55.800><c> tell</c><00:02:55.980><c> database</c><00:02:56.580><c> to</c><00:02:56.819><c> upload</c><00:02:57.180><c> the</c>

00:02:57.470 --> 00:02:57.480 align:start position:0%
let's tell database to upload the
 

00:02:57.480 --> 00:02:59.270 align:start position:0%
let's tell database to upload the
previously<00:02:57.840><c> added</c><00:02:58.200><c> documents</c><00:02:58.860><c> to</c><00:02:58.980><c> the</c><00:02:59.160><c> my</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
previously added documents to the my
 

00:02:59.280 --> 00:03:03.050 align:start position:0%
previously added documents to the my
books<00:02:59.700><c> collection</c>

00:03:03.050 --> 00:03:03.060 align:start position:0%
 
 

00:03:03.060 --> 00:03:06.530 align:start position:0%
 
here<00:03:03.599><c> is</c><00:03:03.720><c> the</c><00:03:03.900><c> reference</c><00:03:04.260><c> collection</c>

00:03:06.530 --> 00:03:06.540 align:start position:0%
here is the reference collection
 

00:03:06.540 --> 00:03:08.270 align:start position:0%
here is the reference collection
and<00:03:06.900><c> the</c><00:03:07.080><c> documents</c><00:03:07.620><c> that</c><00:03:07.739><c> we're</c><00:03:07.860><c> adding</c><00:03:08.160><c> to</c>

00:03:08.270 --> 00:03:08.280 align:start position:0%
and the documents that we're adding to
 

00:03:08.280 --> 00:03:13.970 align:start position:0%
and the documents that we're adding to
it

00:03:13.970 --> 00:03:13.980 align:start position:0%
 
 

00:03:13.980 --> 00:03:16.670 align:start position:0%
 
now<00:03:14.519><c> we</c><00:03:14.819><c> have</c><00:03:15.000><c> a</c><00:03:15.239><c> collection</c><00:03:15.540><c> we</c><00:03:16.500><c> have</c>

00:03:16.670 --> 00:03:16.680 align:start position:0%
now we have a collection we have
 

00:03:16.680 --> 00:03:18.890 align:start position:0%
now we have a collection we have
embedded<00:03:17.280><c> data</c><00:03:17.640><c> in</c><00:03:17.760><c> it</c>

00:03:18.890 --> 00:03:18.900 align:start position:0%
embedded data in it
 

00:03:18.900 --> 00:03:22.369 align:start position:0%
embedded data in it
the<00:03:19.440><c> next</c><00:03:19.500><c> thing</c><00:03:19.739><c> we</c><00:03:19.920><c> have</c><00:03:20.040><c> to</c><00:03:20.220><c> do</c><00:03:20.420><c> is</c><00:03:21.420><c> use</c><00:03:22.080><c> our</c>

00:03:22.369 --> 00:03:22.379 align:start position:0%
the next thing we have to do is use our
 

00:03:22.379 --> 00:03:24.830 align:start position:0%
the next thing we have to do is use our
semantic<00:03:22.980><c> search</c><00:03:23.159><c> engine</c><00:03:23.459><c> to</c><00:03:23.819><c> run</c><00:03:24.000><c> a</c><00:03:24.120><c> query</c><00:03:24.360><c> we</c>

00:03:24.830 --> 00:03:24.840 align:start position:0%
semantic search engine to run a query we
 

00:03:24.840 --> 00:03:29.149 align:start position:0%
semantic search engine to run a query we
have<00:03:25.019><c> to</c><00:03:25.140><c> ask</c><00:03:25.379><c> it</c><00:03:25.560><c> a</c><00:03:25.739><c> question</c>

00:03:29.149 --> 00:03:29.159 align:start position:0%
 
 

00:03:29.159 --> 00:03:31.610 align:start position:0%
 
against<00:03:29.640><c> quadrant</c><00:03:30.300><c> search</c><00:03:30.540><c> we're</c><00:03:31.319><c> going</c><00:03:31.500><c> to</c>

00:03:31.610 --> 00:03:31.620 align:start position:0%
against quadrant search we're going to
 

00:03:31.620 --> 00:03:34.729 align:start position:0%
against quadrant search we're going to
be<00:03:31.680><c> asking</c><00:03:32.159><c> from</c><00:03:32.819><c> the</c><00:03:32.940><c> my</c><00:03:33.120><c> books</c><00:03:33.480><c> collection</c>

00:03:34.729 --> 00:03:34.739 align:start position:0%
be asking from the my books collection
 

00:03:34.739 --> 00:03:38.210 align:start position:0%
be asking from the my books collection
about<00:03:35.159><c> a</c><00:03:35.519><c> potential</c><00:03:35.940><c> alien</c><00:03:36.480><c> invasion</c>

00:03:38.210 --> 00:03:38.220 align:start position:0%
about a potential alien invasion
 

00:03:38.220 --> 00:03:40.850 align:start position:0%
about a potential alien invasion
we're<00:03:39.000><c> going</c><00:03:39.180><c> to</c><00:03:39.300><c> be</c><00:03:39.360><c> asking</c><00:03:39.780><c> for</c><00:03:40.200><c> a</c><00:03:40.500><c> total</c><00:03:40.739><c> of</c>

00:03:40.850 --> 00:03:40.860 align:start position:0%
we're going to be asking for a total of
 

00:03:40.860 --> 00:03:43.430 align:start position:0%
we're going to be asking for a total of
three<00:03:41.099><c> results</c>

00:03:43.430 --> 00:03:43.440 align:start position:0%
three results
 

00:03:43.440 --> 00:03:45.050 align:start position:0%
three results
and<00:03:43.739><c> we're</c><00:03:43.920><c> going</c><00:03:44.040><c> to</c><00:03:44.159><c> have</c><00:03:44.220><c> the</c><00:03:44.459><c> search</c><00:03:44.640><c> show</c>

00:03:45.050 --> 00:03:45.060 align:start position:0%
and we're going to have the search show
 

00:03:45.060 --> 00:03:47.809 align:start position:0%
and we're going to have the search show
us<00:03:45.299><c> a</c><00:03:46.080><c> likelihood</c><00:03:46.500><c> score</c><00:03:46.980><c> that</c><00:03:47.580><c> something</c>

00:03:47.809 --> 00:03:47.819 align:start position:0%
us a likelihood score that something
 

00:03:47.819 --> 00:03:50.030 align:start position:0%
us a likelihood score that something
fits<00:03:48.420><c> our</c><00:03:48.780><c> search</c>

00:03:50.030 --> 00:03:50.040 align:start position:0%
fits our search
 

00:03:50.040 --> 00:03:52.149 align:start position:0%
fits our search
foreign

00:03:52.149 --> 00:03:52.159 align:start position:0%
foreign
 

00:03:52.159 --> 00:03:56.270 align:start position:0%
foreign
there<00:03:53.159><c> are</c><00:03:53.340><c> three</c><00:03:53.519><c> books</c><00:03:53.940><c> from</c><00:03:54.659><c> our</c><00:03:54.840><c> library</c>

00:03:56.270 --> 00:03:56.280 align:start position:0%
there are three books from our library
 

00:03:56.280 --> 00:03:59.449 align:start position:0%
there are three books from our library
that<00:03:56.819><c> talk</c><00:03:57.060><c> about</c><00:03:57.239><c> an</c><00:03:57.959><c> alien</c><00:03:58.319><c> invasion</c><00:03:58.799><c> The</c>

00:03:59.449 --> 00:03:59.459 align:start position:0%
that talk about an alien invasion The
 

00:03:59.459 --> 00:04:00.830 align:start position:0%
that talk about an alien invasion The
War<00:03:59.640><c> of</c><00:03:59.819><c> the</c><00:03:59.940><c> Worlds</c>

00:04:00.830 --> 00:04:00.840 align:start position:0%
War of the Worlds
 

00:04:00.840 --> 00:04:03.170 align:start position:0%
War of the Worlds
The<00:04:01.260><c> Hitchhiker's</c><00:04:01.920><c> Guide</c><00:04:02.099><c> to</c><00:04:02.280><c> the</c><00:04:02.459><c> Galaxy</c><00:04:02.640><c> and</c>

00:04:03.170 --> 00:04:03.180 align:start position:0%
The Hitchhiker's Guide to the Galaxy and
 

00:04:03.180 --> 00:04:04.789 align:start position:0%
The Hitchhiker's Guide to the Galaxy and
the<00:04:03.360><c> three</c><00:04:03.480><c> body</c><00:04:03.720><c> problem</c>

00:04:04.789 --> 00:04:04.799 align:start position:0%
the three body problem
 

00:04:04.799 --> 00:04:06.410 align:start position:0%
the three body problem
this<00:04:05.280><c> seems</c><00:04:05.580><c> to</c><00:04:05.700><c> make</c><00:04:05.819><c> a</c><00:04:06.000><c> lot</c><00:04:06.120><c> of</c><00:04:06.239><c> sense</c>

00:04:06.410 --> 00:04:06.420 align:start position:0%
this seems to make a lot of sense
 

00:04:06.420 --> 00:04:08.210 align:start position:0%
this seems to make a lot of sense
because<00:04:06.840><c> some</c><00:04:07.200><c> of</c><00:04:07.319><c> the</c><00:04:07.440><c> other</c><00:04:07.560><c> books</c><00:04:07.920><c> in</c><00:04:08.040><c> the</c>

00:04:08.210 --> 00:04:08.220 align:start position:0%
because some of the other books in the
 

00:04:08.220 --> 00:04:09.830 align:start position:0%
because some of the other books in the
library<00:04:08.400><c> had</c><00:04:08.819><c> absolutely</c><00:04:09.239><c> nothing</c><00:04:09.480><c> to</c><00:04:09.720><c> do</c>

00:04:09.830 --> 00:04:09.840 align:start position:0%
library had absolutely nothing to do
 

00:04:09.840 --> 00:04:11.449 align:start position:0%
library had absolutely nothing to do
with<00:04:10.140><c> aliens</c>

00:04:11.449 --> 00:04:11.459 align:start position:0%
with aliens
 

00:04:11.459 --> 00:04:13.190 align:start position:0%
with aliens
though<00:04:11.819><c> they</c><00:04:12.180><c> were</c><00:04:12.360><c> still</c><00:04:12.659><c> science</c><00:04:12.959><c> fiction</c>

00:04:13.190 --> 00:04:13.200 align:start position:0%
though they were still science fiction
 

00:04:13.200 --> 00:04:14.750 align:start position:0%
though they were still science fiction
books

00:04:14.750 --> 00:04:14.760 align:start position:0%
books
 

00:04:14.760 --> 00:04:16.370 align:start position:0%
books
what<00:04:15.180><c> if</c><00:04:15.299><c> we</c><00:04:15.420><c> want</c><00:04:15.540><c> to</c><00:04:15.659><c> take</c><00:04:15.840><c> this</c><00:04:16.079><c> a</c><00:04:16.260><c> step</c>

00:04:16.370 --> 00:04:16.380 align:start position:0%
what if we want to take this a step
 

00:04:16.380 --> 00:04:17.449 align:start position:0%
what if we want to take this a step
further

00:04:17.449 --> 00:04:17.459 align:start position:0%
further
 

00:04:17.459 --> 00:04:20.090 align:start position:0%
further
let's<00:04:18.120><c> look</c><00:04:18.359><c> for</c><00:04:18.540><c> the</c><00:04:18.780><c> most</c><00:04:18.959><c> recent</c><00:04:19.380><c> book</c><00:04:19.620><c> that</c>

00:04:20.090 --> 00:04:20.100 align:start position:0%
let's look for the most recent book that
 

00:04:20.100 --> 00:04:22.129 align:start position:0%
let's look for the most recent book that
talks<00:04:20.400><c> about</c><00:04:20.519><c> an</c><00:04:20.820><c> alien</c><00:04:21.120><c> invasion</c>

00:04:22.129 --> 00:04:22.139 align:start position:0%
talks about an alien invasion
 

00:04:22.139 --> 00:04:24.050 align:start position:0%
talks about an alien invasion
for<00:04:22.620><c> this</c><00:04:22.740><c> we</c><00:04:22.979><c> have</c><00:04:23.100><c> to</c><00:04:23.280><c> use</c><00:04:23.460><c> quadrants</c>

00:04:24.050 --> 00:04:24.060 align:start position:0%
for this we have to use quadrants
 

00:04:24.060 --> 00:04:26.450 align:start position:0%
for this we have to use quadrants
filtering<00:04:24.600><c> function</c><00:04:25.020><c> we</c><00:04:25.919><c> keep</c><00:04:26.100><c> the</c><00:04:26.280><c> same</c>

00:04:26.450 --> 00:04:26.460 align:start position:0%
filtering function we keep the same
 

00:04:26.460 --> 00:04:28.909 align:start position:0%
filtering function we keep the same
question<00:04:26.759><c> topic</c><00:04:27.360><c> but</c><00:04:28.139><c> we</c><00:04:28.320><c> will</c><00:04:28.500><c> add</c><00:04:28.740><c> our</c>

00:04:28.909 --> 00:04:28.919 align:start position:0%
question topic but we will add our
 

00:04:28.919 --> 00:04:35.090 align:start position:0%
question topic but we will add our
condition<00:04:29.160><c> filter</c><00:04:29.639><c> by</c><00:04:30.060><c> year</c><00:04:30.240><c> of</c><00:04:30.479><c> publication</c>

00:04:35.090 --> 00:04:35.100 align:start position:0%
 
 

00:04:35.100 --> 00:04:37.249 align:start position:0%
 
so<00:04:35.580><c> we</c><00:04:35.759><c> keep</c><00:04:35.880><c> the</c><00:04:36.120><c> question</c>

00:04:37.249 --> 00:04:37.259 align:start position:0%
so we keep the question
 

00:04:37.259 --> 00:04:42.290 align:start position:0%
so we keep the question
but<00:04:37.680><c> we</c><00:04:37.860><c> set</c><00:04:38.040><c> the</c><00:04:38.280><c> year</c><00:04:38.460><c> at</c><00:04:38.820><c> two</c><00:04:39.120><c> thousand</c>

00:04:42.290 --> 00:04:42.300 align:start position:0%
 
 

00:04:42.300 --> 00:04:44.150 align:start position:0%
 
because<00:04:42.660><c> we're</c><00:04:42.960><c> only</c><00:04:43.199><c> looking</c><00:04:43.500><c> for</c><00:04:43.800><c> the</c><00:04:44.040><c> most</c>

00:04:44.150 --> 00:04:44.160 align:start position:0%
because we're only looking for the most
 

00:04:44.160 --> 00:04:46.310 align:start position:0%
because we're only looking for the most
recent<00:04:44.580><c> book</c>

00:04:46.310 --> 00:04:46.320 align:start position:0%
recent book
 

00:04:46.320 --> 00:04:48.590 align:start position:0%
recent book
let's<00:04:46.800><c> only</c><00:04:46.979><c> look</c><00:04:47.220><c> for</c><00:04:47.460><c> the</c><00:04:47.639><c> top</c><00:04:47.759><c> result</c><00:04:48.120><c> and</c>

00:04:48.590 --> 00:04:48.600 align:start position:0%
let's only look for the top result and
 

00:04:48.600 --> 00:04:55.189 align:start position:0%
let's only look for the top result and
set<00:04:48.840><c> the</c><00:04:49.020><c> limit</c><00:04:49.139><c> at</c><00:04:49.560><c> one</c>

00:04:55.189 --> 00:04:55.199 align:start position:0%
 
 

00:04:55.199 --> 00:04:57.770 align:start position:0%
 
so<00:04:55.680><c> here</c><00:04:55.860><c> you</c><00:04:56.100><c> can</c><00:04:56.280><c> see</c><00:04:56.580><c> that</c><00:04:57.180><c> the</c><00:04:57.419><c> three</c><00:04:57.540><c> body</c>

00:04:57.770 --> 00:04:57.780 align:start position:0%
so here you can see that the three body
 

00:04:57.780 --> 00:05:00.430 align:start position:0%
so here you can see that the three body
problem<00:04:58.080><c> is</c><00:04:58.560><c> the</c><00:04:58.800><c> closest</c><00:04:58.919><c> result</c>

00:05:00.430 --> 00:05:00.440 align:start position:0%
problem is the closest result
 

00:05:00.440 --> 00:05:02.870 align:start position:0%
problem is the closest result
this<00:05:01.440><c> book</c><00:05:01.620><c> happens</c><00:05:01.860><c> to</c><00:05:02.160><c> have</c><00:05:02.280><c> been</c><00:05:02.460><c> published</c>

00:05:02.870 --> 00:05:02.880 align:start position:0%
this book happens to have been published
 

00:05:02.880 --> 00:05:05.409 align:start position:0%
this book happens to have been published
in<00:05:03.180><c> 2008.</c>

00:05:05.409 --> 00:05:05.419 align:start position:0%
in 2008.
 

00:05:05.419 --> 00:05:08.270 align:start position:0%
in 2008.
that's<00:05:06.419><c> all</c><00:05:06.660><c> it</c><00:05:06.780><c> takes</c><00:05:07.020><c> to</c><00:05:07.380><c> set</c><00:05:07.500><c> up</c><00:05:07.680><c> semantic</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
that's all it takes to set up semantic
 

00:05:08.280 --> 00:05:10.189 align:start position:0%
that's all it takes to set up semantic
search<00:05:08.460><c> with</c><00:05:08.940><c> quadrant</c>

00:05:10.189 --> 00:05:10.199 align:start position:0%
search with quadrant
 

00:05:10.199 --> 00:05:11.870 align:start position:0%
search with quadrant
in<00:05:10.620><c> the</c><00:05:10.800><c> next</c><00:05:10.860><c> tutorial</c>

00:05:11.870 --> 00:05:11.880 align:start position:0%
in the next tutorial
 

00:05:11.880 --> 00:05:13.790 align:start position:0%
in the next tutorial
I'm<00:05:12.419><c> going</c><00:05:12.540><c> to</c><00:05:12.660><c> show</c><00:05:12.780><c> you</c><00:05:12.840><c> how</c><00:05:13.080><c> to</c><00:05:13.199><c> upload</c><00:05:13.560><c> your</c>

00:05:13.790 --> 00:05:13.800 align:start position:0%
I'm going to show you how to upload your
 

00:05:13.800 --> 00:05:17.479 align:start position:0%
I'm going to show you how to upload your
own<00:05:13.919><c> data</c><00:05:14.280><c> and</c><00:05:14.820><c> search</c><00:05:15.000><c> through</c><00:05:15.300><c> it</c>

