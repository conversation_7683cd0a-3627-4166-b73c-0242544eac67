WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.730 align:start position:0%
 
you're<00:00:00.539><c> already</c><00:00:00.780><c> familiar</c><00:00:01.199><c> with</c><00:00:01.380><c> GitHub</c>

00:00:01.730 --> 00:00:01.740 align:start position:0%
you're already familiar with GitHub
 

00:00:01.740 --> 00:00:03.830 align:start position:0%
you're already familiar with GitHub
actions<00:00:02.280><c> the</c><00:00:02.520><c> CI</c><00:00:02.760><c> CD</c><00:00:03.060><c> tool</c><00:00:03.419><c> built</c><00:00:03.659><c> right</c><00:00:03.720><c> into</c>

00:00:03.830 --> 00:00:03.840 align:start position:0%
actions the CI CD tool built right into
 

00:00:03.840 --> 00:00:06.470 align:start position:0%
actions the CI CD tool built right into
the<00:00:04.020><c> GitHub</c><00:00:04.319><c> platform</c><00:00:04.740><c> yep</c><00:00:05.700><c> and</c><00:00:06.180><c> you</c><00:00:06.359><c> already</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
the GitHub platform yep and you already
 

00:00:06.480 --> 00:00:09.169 align:start position:0%
the GitHub platform yep and you already
know<00:00:06.779><c> that</c><00:00:07.020><c> we</c><00:00:07.259><c> can</c><00:00:07.440><c> migrate</c><00:00:08.040><c> from</c><00:00:08.880><c> other</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
know that we can migrate from other
 

00:00:09.179 --> 00:00:12.169 align:start position:0%
know that we can migrate from other
platforms<00:00:09.840><c> right</c><00:00:10.139><c> into</c><00:00:10.260><c> GitHub</c><00:00:10.679><c> actions</c><00:00:11.179><c> but</c>

00:00:12.169 --> 00:00:12.179 align:start position:0%
platforms right into GitHub actions but
 

00:00:12.179 --> 00:00:14.030 align:start position:0%
platforms right into GitHub actions but
did<00:00:12.900><c> you</c><00:00:13.080><c> know</c><00:00:13.200><c> that</c><00:00:13.320><c> we</c><00:00:13.559><c> just</c><00:00:13.679><c> recently</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
did you know that we just recently
 

00:00:14.040 --> 00:00:16.730 align:start position:0%
did you know that we just recently
expanded<00:00:14.639><c> the</c><00:00:14.759><c> set</c><00:00:14.940><c> of</c><00:00:15.059><c> tools</c><00:00:15.360><c> we</c><00:00:15.540><c> support</c>

00:00:16.730 --> 00:00:16.740 align:start position:0%
expanded the set of tools we support
 

00:00:16.740 --> 00:00:18.950 align:start position:0%
expanded the set of tools we support
using<00:00:17.160><c> GitHub</c><00:00:17.460><c> actions</c><00:00:17.880><c> importer</c><00:00:18.359><c> our</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
using GitHub actions importer our
 

00:00:18.960 --> 00:00:22.010 align:start position:0%
using GitHub actions importer our
customers<00:00:19.440><c> can</c><00:00:19.680><c> now</c><00:00:19.859><c> plan</c><00:00:20.220><c> test</c><00:00:20.699><c> and</c><00:00:21.539><c> automate</c>

00:00:22.010 --> 00:00:22.020 align:start position:0%
customers can now plan test and automate
 

00:00:22.020 --> 00:00:24.410 align:start position:0%
customers can now plan test and automate
migrations<00:00:22.439><c> of</c><00:00:22.800><c> existing</c><00:00:23.220><c> pipelines</c><00:00:23.760><c> from</c>

00:00:24.410 --> 00:00:24.420 align:start position:0%
migrations of existing pipelines from
 

00:00:24.420 --> 00:00:27.470 align:start position:0%
migrations of existing pipelines from
Jenkins<00:00:24.960><c> gitlab</c><00:00:25.800><c> Circle</c><00:00:26.160><c> CI</c><00:00:26.699><c> Travis</c><00:00:27.300><c> and</c>

00:00:27.470 --> 00:00:27.480 align:start position:0%
Jenkins gitlab Circle CI Travis and
 

00:00:27.480 --> 00:00:31.089 align:start position:0%
Jenkins gitlab Circle CI Travis and
azure<00:00:27.840><c> devops</c><00:00:28.740><c> yep</c><00:00:29.460><c> atlass</c><00:00:30.060><c> into</c><00:00:30.240><c> we</c><00:00:30.779><c> support</c>

00:00:31.089 --> 00:00:31.099 align:start position:0%
azure devops yep atlass into we support
 

00:00:31.099 --> 00:00:33.650 align:start position:0%
azure devops yep atlass into we support
bedbucket<00:00:32.099><c> pipelines</c><00:00:32.520><c> bamboo</c><00:00:33.239><c> data</c><00:00:33.540><c> center</c>

00:00:33.650 --> 00:00:33.660 align:start position:0%
bedbucket pipelines bamboo data center
 

00:00:33.660 --> 00:00:36.290 align:start position:0%
bedbucket pipelines bamboo data center
bamboo<00:00:34.440><c> server</c><00:00:34.860><c> the</c><00:00:35.700><c> last</c><00:00:35.820><c> one</c><00:00:36.000><c> is</c><00:00:36.120><c> really</c>

00:00:36.290 --> 00:00:36.300 align:start position:0%
bamboo server the last one is really
 

00:00:36.300 --> 00:00:37.910 align:start position:0%
bamboo server the last one is really
important<00:00:36.660><c> because</c><00:00:36.899><c> it</c><00:00:37.260><c> means</c><00:00:37.559><c> that</c><00:00:37.800><c> people</c>

00:00:37.910 --> 00:00:37.920 align:start position:0%
important because it means that people
 

00:00:37.920 --> 00:00:41.330 align:start position:0%
important because it means that people
can<00:00:38.219><c> migrate</c><00:00:38.640><c> to</c><00:00:38.940><c> actions</c><00:00:39.540><c> before</c><00:00:40.379><c> atlassian</c>

00:00:41.330 --> 00:00:41.340 align:start position:0%
can migrate to actions before atlassian
 

00:00:41.340 --> 00:00:43.130 align:start position:0%
can migrate to actions before atlassian
sun<00:00:41.700><c> sets</c><00:00:42.120><c> their</c><00:00:42.239><c> server</c><00:00:42.540><c> products</c><00:00:42.960><c> in</c>

00:00:43.130 --> 00:00:43.140 align:start position:0%
sun sets their server products in
 

00:00:43.140 --> 00:00:45.290 align:start position:0%
sun sets their server products in
February<00:00:43.500><c> of</c><00:00:43.739><c> 2024.</c>

00:00:45.290 --> 00:00:45.300 align:start position:0%
February of 2024.
 

00:00:45.300 --> 00:00:48.590 align:start position:0%
February of 2024.
who<00:00:45.899><c> well</c><00:00:46.739><c> individual</c><00:00:47.579><c> developers</c><00:00:48.180><c> who</c><00:00:48.480><c> want</c>

00:00:48.590 --> 00:00:48.600 align:start position:0%
who well individual developers who want
 

00:00:48.600 --> 00:00:50.330 align:start position:0%
who well individual developers who want
to<00:00:48.719><c> bring</c><00:00:48.780><c> GitHub</c><00:00:49.140><c> actions</c><00:00:49.860><c> into</c><00:00:50.100><c> their</c>

00:00:50.330 --> 00:00:50.340 align:start position:0%
to bring GitHub actions into their
 

00:00:50.340 --> 00:00:52.670 align:start position:0%
to bring GitHub actions into their
workplace<00:00:50.760><c> technology</c><00:00:51.660><c> managers</c><00:00:52.320><c> who</c><00:00:52.559><c> are</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
workplace technology managers who are
 

00:00:52.680 --> 00:00:54.350 align:start position:0%
workplace technology managers who are
looking<00:00:52.860><c> to</c><00:00:53.160><c> consolidate</c><00:00:53.760><c> and</c><00:00:54.059><c> simplify</c>

00:00:54.350 --> 00:00:54.360 align:start position:0%
looking to consolidate and simplify
 

00:00:54.360 --> 00:00:57.350 align:start position:0%
looking to consolidate and simplify
their<00:00:54.600><c> devops</c><00:00:54.960><c> tool</c><00:00:55.260><c> set</c><00:00:55.379><c> they</c><00:00:56.039><c> all</c><00:00:56.219><c> know</c><00:00:56.520><c> it's</c>

00:00:57.350 --> 00:00:57.360 align:start position:0%
their devops tool set they all know it's
 

00:00:57.360 --> 00:00:59.090 align:start position:0%
their devops tool set they all know it's
really<00:00:57.600><c> time</c><00:00:57.840><c> expensive</c><00:00:58.379><c> and</c><00:00:58.559><c> risky</c><00:00:58.920><c> to</c>

00:00:59.090 --> 00:00:59.100 align:start position:0%
really time expensive and risky to
 

00:00:59.100 --> 00:01:01.549 align:start position:0%
really time expensive and risky to
manually<00:00:59.460><c> migrate</c><00:00:59.940><c> so</c><00:01:00.360><c> it's</c><00:01:00.840><c> amazing</c><00:01:01.320><c> for</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
manually migrate so it's amazing for
 

00:01:01.559 --> 00:01:03.349 align:start position:0%
manually migrate so it's amazing for
them<00:01:01.620><c> to</c><00:01:01.800><c> be</c><00:01:01.860><c> able</c><00:01:02.039><c> to</c><00:01:02.280><c> automate</c><00:01:02.879><c> all</c><00:01:03.180><c> of</c><00:01:03.239><c> that</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
them to be able to automate all of that
 

00:01:03.359 --> 00:01:05.509 align:start position:0%
them to be able to automate all of that
with<00:01:03.600><c> GitHub</c><00:01:03.899><c> actions</c><00:01:04.320><c> importer</c><00:01:04.739><c> and</c><00:01:05.400><c> it's</c>

00:01:05.509 --> 00:01:05.519 align:start position:0%
with GitHub actions importer and it's
 

00:01:05.519 --> 00:01:07.310 align:start position:0%
with GitHub actions importer and it's
free<00:01:05.820><c> it's</c><00:01:06.360><c> available</c><00:01:06.600><c> right</c><00:01:07.020><c> inside</c><00:01:07.140><c> the</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
free it's available right inside the
 

00:01:07.320 --> 00:01:10.670 align:start position:0%
free it's available right inside the
GitHub<00:01:07.619><c> CLI</c><00:01:08.280><c> or</c><00:01:08.760><c> issue</c><00:01:09.060><c> Ops</c><00:01:09.900><c> where</c><00:01:10.380><c> can</c><00:01:10.560><c> they</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
GitHub CLI or issue Ops where can they
 

00:01:10.680 --> 00:01:12.190 align:start position:0%
GitHub CLI or issue Ops where can they
find<00:01:10.799><c> out</c><00:01:10.979><c> more</c>

00:01:12.190 --> 00:01:12.200 align:start position:0%
find out more
 

00:01:12.200 --> 00:01:17.060 align:start position:0%
find out more
gh.i<00:01:13.200><c> o</c><00:01:13.439><c> slash</c><00:01:13.979><c> actions</c><00:01:14.640><c> importer</c>

