// Token management utilities for Gemini API
export const GEMINI_LIMITS = {
  MAX_INPUT_TOKENS: 1_000_000,  // 1M context window for gemini-2.5-flash
  MAX_OUTPUT_TOKENS: 65_536,     // 64K output limit
  SAFETY_MARGIN: 10_000,         // Safety margin for system prompt + overhead
} as const

export const MAX_CONTEXT_TOKENS = GEMINI_LIMITS.MAX_INPUT_TOKENS - GEMINI_LIMITS.SAFETY_MARGIN

/**
 * Rough estimation of tokens (more accurate counting would require tiktoken)
 * English text is roughly 4 characters per token
 */
export function estimateTokens(text: string): number {
  return Math.ceil(text.length / 4)
}

/**
 * Truncate text to fit within token limit
 */
export function truncateToTokenLimit(text: string, maxTokens: number): string {
  const estimatedTokens = estimateTokens(text)
  if (estimatedTokens <= maxTokens) {
    return text
  }
  
  // Calculate approximate character limit
  const maxChars = maxTokens * 4
  return text.substring(0, maxChars) + '...[truncated]'
}

/**
 * Smart context management for chat API
 */
export function optimizeContextForChat(videos: Array<{
  title: string
  channel: string
  summary: string
  transcript?: string
}>, userMessage: string, conversationHistory: string = ''): {
  contextText: string
  totalEstimatedTokens: number
  truncated: boolean
} {
  const systemPromptTokens = 200
  const userMessageTokens = estimateTokens(userMessage)
  const conversationTokens = estimateTokens(conversationHistory)
  
  const availableTokensForContext = MAX_CONTEXT_TOKENS - systemPromptTokens - userMessageTokens - conversationTokens
  
  let contextText = ''
  let totalTokensUsed = 0
  let truncated = false
  
  for (const video of videos) {
    const summaryText = `Title: ${video.title}\nChannel: ${video.channel}\nSummary: ${video.summary}`
    const summaryTokens = estimateTokens(summaryText)
    
    // Always include at least the summary
    let videoContext = summaryText
    let videoTokens = summaryTokens
    
    // Try to include transcript if we have tokens available
    if (video.transcript && video.transcript.trim()) {
      const transcriptTokens = estimateTokens(video.transcript)
      const remainingTokens = availableTokensForContext - totalTokensUsed - videoTokens
      
      if (transcriptTokens <= remainingTokens - 1000) { // Leave 1000 tokens buffer
        // Include full transcript
        videoContext += `\n\nFull Transcript:\n${video.transcript}`
        videoTokens += transcriptTokens
      } else if (remainingTokens > 2000) {
        // Include truncated transcript
        const maxTranscriptTokens = remainingTokens - 1000
        const truncatedTranscript = truncateToTokenLimit(video.transcript, maxTranscriptTokens)
        videoContext += `\n\nTranscript (truncated):\n${truncatedTranscript}`
        videoTokens = summaryTokens + maxTranscriptTokens
        truncated = true
      }
    }
    
    // Check if adding this video would exceed our limit
    if (totalTokensUsed + videoTokens > availableTokensForContext) {
      truncated = true
      break
    }
    
    if (contextText) {
      contextText += '\n\n---\n\n'
    }
    contextText += videoContext
    totalTokensUsed += videoTokens
  }
  
  return {
    contextText,
    totalEstimatedTokens: systemPromptTokens + userMessageTokens + conversationTokens + totalTokensUsed,
    truncated
  }
}
