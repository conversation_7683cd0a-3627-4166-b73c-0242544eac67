WEBVTT
Kind: captions
Language: en

00:00:01.979 --> 00:00:04.249 align:start position:0%
 
hi<00:00:02.580><c> everyone</c><00:00:02.820><c> my</c><00:00:03.360><c> name</c><00:00:03.480><c> is</c><00:00:03.540><c> Adam</c><00:00:03.720><c> this</c><00:00:04.200><c> is</c>

00:00:04.249 --> 00:00:04.259 align:start position:0%
hi everyone my name is Adam this is
 

00:00:04.259 --> 00:00:05.930 align:start position:0%
hi everyone my name is Adam this is
going<00:00:04.380><c> to</c><00:00:04.440><c> be</c><00:00:04.560><c> an</c><00:00:04.680><c> intro</c><00:00:04.920><c> to</c><00:00:05.100><c> data</c><00:00:05.520><c> agents</c><00:00:05.819><c> for</c>

00:00:05.930 --> 00:00:05.940 align:start position:0%
going to be an intro to data agents for
 

00:00:05.940 --> 00:00:07.130 align:start position:0%
going to be an intro to data agents for
Developers

00:00:07.130 --> 00:00:07.140 align:start position:0%
Developers
 

00:00:07.140 --> 00:00:09.290 align:start position:0%
Developers
building<00:00:07.620><c> data</c><00:00:08.099><c> agents</c><00:00:08.400><c> with</c><00:00:08.580><c> llama</c><00:00:08.880><c> index</c>

00:00:09.290 --> 00:00:09.300 align:start position:0%
building data agents with llama index
 

00:00:09.300 --> 00:00:11.930 align:start position:0%
building data agents with llama index
and<00:00:09.540><c> lava</c><00:00:10.019><c> hub</c>

00:00:11.930 --> 00:00:11.940 align:start position:0%
and lava hub
 

00:00:11.940 --> 00:00:14.270 align:start position:0%
and lava hub
so<00:00:12.540><c> the</c><00:00:12.780><c> goals</c><00:00:13.080><c> for</c><00:00:13.200><c> this</c><00:00:13.380><c> presentation</c><00:00:13.860><c> are</c>

00:00:14.270 --> 00:00:14.280 align:start position:0%
so the goals for this presentation are
 

00:00:14.280 --> 00:00:16.250 align:start position:0%
so the goals for this presentation are
going<00:00:14.400><c> to</c><00:00:14.519><c> be</c><00:00:14.519><c> to</c><00:00:14.820><c> familiarize</c><00:00:15.480><c> developers</c><00:00:15.960><c> on</c>

00:00:16.250 --> 00:00:16.260 align:start position:0%
going to be to familiarize developers on
 

00:00:16.260 --> 00:00:18.349 align:start position:0%
going to be to familiarize developers on
data<00:00:16.619><c> agents</c><00:00:16.859><c> with</c><00:00:17.039><c> llama</c><00:00:17.279><c> index</c><00:00:17.699><c> showcase</c>

00:00:18.349 --> 00:00:18.359 align:start position:0%
data agents with llama index showcase
 

00:00:18.359 --> 00:00:20.570 align:start position:0%
data agents with llama index showcase
simple<00:00:18.720><c> and</c><00:00:19.080><c> more</c><00:00:19.320><c> complicated</c><00:00:19.740><c> examples</c><00:00:20.220><c> of</c>

00:00:20.570 --> 00:00:20.580 align:start position:0%
simple and more complicated examples of
 

00:00:20.580 --> 00:00:22.849 align:start position:0%
simple and more complicated examples of
data<00:00:20.939><c> agents</c><00:00:21.359><c> and</c><00:00:22.140><c> by</c><00:00:22.380><c> the</c><00:00:22.500><c> end</c><00:00:22.619><c> of</c><00:00:22.740><c> this</c>

00:00:22.849 --> 00:00:22.859 align:start position:0%
data agents and by the end of this
 

00:00:22.859 --> 00:00:24.230 align:start position:0%
data agents and by the end of this
presentation<00:00:23.220><c> you</c><00:00:23.580><c> should</c><00:00:23.760><c> understand</c><00:00:23.880><c> how</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
presentation you should understand how
 

00:00:24.240 --> 00:00:26.029 align:start position:0%
presentation you should understand how
to<00:00:24.359><c> create</c><00:00:24.420><c> your</c><00:00:24.660><c> own</c><00:00:24.779><c> data</c><00:00:25.140><c> agent</c><00:00:25.380><c> with</c><00:00:25.619><c> lava</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
to create your own data agent with lava
 

00:00:26.039 --> 00:00:28.130 align:start position:0%
to create your own data agent with lava
index<00:00:26.340><c> and</c><00:00:26.580><c> Obama</c><00:00:26.880><c> hub</c>

00:00:28.130 --> 00:00:28.140 align:start position:0%
index and Obama hub
 

00:00:28.140 --> 00:00:31.130 align:start position:0%
index and Obama hub
so<00:00:28.859><c> to</c><00:00:29.099><c> start</c><00:00:29.279><c> off</c><00:00:29.460><c> what</c><00:00:30.060><c> exactly</c><00:00:30.480><c> do</c><00:00:30.720><c> we</c><00:00:30.900><c> mean</c>

00:00:31.130 --> 00:00:31.140 align:start position:0%
so to start off what exactly do we mean
 

00:00:31.140 --> 00:00:34.069 align:start position:0%
so to start off what exactly do we mean
by<00:00:31.380><c> a</c><00:00:31.560><c> data</c><00:00:31.859><c> agent</c><00:00:32.520><c> so</c><00:00:32.940><c> data</c><00:00:33.239><c> agents</c><00:00:33.660><c> are</c><00:00:33.899><c> a</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
by a data agent so data agents are a
 

00:00:34.079 --> 00:00:36.290 align:start position:0%
by a data agent so data agents are a
large<00:00:34.200><c> language</c><00:00:34.500><c> model</c><00:00:34.920><c> powered</c><00:00:35.700><c> interfaces</c>

00:00:36.290 --> 00:00:36.300 align:start position:0%
large language model powered interfaces
 

00:00:36.300 --> 00:00:38.209 align:start position:0%
large language model powered interfaces
that<00:00:36.660><c> can</c><00:00:36.840><c> intelligently</c><00:00:37.500><c> interact</c><00:00:37.980><c> with</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
that can intelligently interact with
 

00:00:38.219 --> 00:00:40.610 align:start position:0%
that can intelligently interact with
apis<00:00:38.820><c> and</c><00:00:39.239><c> data</c><00:00:39.600><c> in</c><00:00:39.840><c> a</c><00:00:40.020><c> read</c><00:00:40.140><c> and</c><00:00:40.440><c> write</c>

00:00:40.610 --> 00:00:40.620 align:start position:0%
apis and data in a read and write
 

00:00:40.620 --> 00:00:43.549 align:start position:0%
apis and data in a read and write
fashion<00:00:41.160><c> so</c><00:00:42.000><c> essentially</c><00:00:42.600><c> allowing</c><00:00:43.200><c> our</c>

00:00:43.549 --> 00:00:43.559 align:start position:0%
fashion so essentially allowing our
 

00:00:43.559 --> 00:00:46.369 align:start position:0%
fashion so essentially allowing our
large<00:00:43.800><c> language</c><00:00:44.100><c> models</c><00:00:44.820><c> to</c><00:00:45.360><c> call</c><00:00:45.780><c> functions</c>

00:00:46.369 --> 00:00:46.379 align:start position:0%
large language models to call functions
 

00:00:46.379 --> 00:00:50.029 align:start position:0%
large language models to call functions
call<00:00:46.680><c> apis</c><00:00:47.340><c> and</c><00:00:48.180><c> interact</c><00:00:48.719><c> with</c><00:00:48.920><c> the</c><00:00:49.920><c> world</c>

00:00:50.029 --> 00:00:50.039 align:start position:0%
call apis and interact with the world
 

00:00:50.039 --> 00:00:51.130 align:start position:0%
call apis and interact with the world
around<00:00:50.280><c> it</c>

00:00:51.130 --> 00:00:51.140 align:start position:0%
around it
 

00:00:51.140 --> 00:00:54.049 align:start position:0%
around it
there's<00:00:52.140><c> a</c><00:00:52.320><c> number</c><00:00:52.440><c> of</c><00:00:52.620><c> limitations</c><00:00:53.340><c> around</c>

00:00:54.049 --> 00:00:54.059 align:start position:0%
there's a number of limitations around
 

00:00:54.059 --> 00:00:56.689 align:start position:0%
there's a number of limitations around
just<00:00:54.719><c> base</c><00:00:55.020><c> level</c><00:00:55.320><c> large</c><00:00:55.920><c> language</c><00:00:56.160><c> models</c>

00:00:56.689 --> 00:00:56.699 align:start position:0%
just base level large language models
 

00:00:56.699 --> 00:00:58.189 align:start position:0%
just base level large language models
that<00:00:56.820><c> currently</c><00:00:57.180><c> exist</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
that currently exist
 

00:00:58.199 --> 00:00:59.510 align:start position:0%
that currently exist
um<00:00:58.260><c> some</c><00:00:58.500><c> of</c><00:00:58.620><c> them</c><00:00:58.739><c> that</c><00:00:58.860><c> come</c><00:00:59.039><c> up</c><00:00:59.219><c> a</c><00:00:59.460><c> lot</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
um some of them that come up a lot
 

00:00:59.520 --> 00:01:02.150 align:start position:0%
um some of them that come up a lot
include<00:01:00.199><c> limited</c><00:01:01.199><c> context</c><00:01:01.559><c> Windows</c>

00:01:02.150 --> 00:01:02.160 align:start position:0%
include limited context Windows
 

00:01:02.160 --> 00:01:03.950 align:start position:0%
include limited context Windows
inability<00:01:02.760><c> to</c><00:01:03.000><c> complete</c><00:01:03.359><c> logical</c><00:01:03.780><c> or</c>

00:01:03.950 --> 00:01:03.960 align:start position:0%
inability to complete logical or
 

00:01:03.960 --> 00:01:06.230 align:start position:0%
inability to complete logical or
mathematical<00:01:04.500><c> reasoning</c><00:01:05.040><c> finding</c><00:01:05.939><c> knowledge</c>

00:01:06.230 --> 00:01:06.240 align:start position:0%
mathematical reasoning finding knowledge
 

00:01:06.240 --> 00:01:08.390 align:start position:0%
mathematical reasoning finding knowledge
password<00:01:06.540><c> training</c><00:01:06.960><c> cutoffs</c><00:01:07.680><c> Etc</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
password training cutoffs Etc
 

00:01:08.400 --> 00:01:10.969 align:start position:0%
password training cutoffs Etc
so<00:01:09.000><c> with</c><00:01:09.240><c> data</c><00:01:09.600><c> agents</c><00:01:09.840><c> we</c><00:01:10.140><c> can</c><00:01:10.260><c> combine</c><00:01:10.680><c> large</c>

00:01:10.969 --> 00:01:10.979 align:start position:0%
so with data agents we can combine large
 

00:01:10.979 --> 00:01:13.310 align:start position:0%
so with data agents we can combine large
language<00:01:11.220><c> models</c><00:01:11.820><c> and</c><00:01:12.180><c> modern</c><00:01:12.720><c> Technologies</c>

00:01:13.310 --> 00:01:13.320 align:start position:0%
language models and modern Technologies
 

00:01:13.320 --> 00:01:15.649 align:start position:0%
language models and modern Technologies
to<00:01:13.680><c> create</c><00:01:13.799><c> even</c><00:01:14.100><c> more</c><00:01:14.460><c> powerful</c><00:01:14.939><c> interfaces</c>

00:01:15.649 --> 00:01:15.659 align:start position:0%
to create even more powerful interfaces
 

00:01:15.659 --> 00:01:18.469 align:start position:0%
to create even more powerful interfaces
a<00:01:16.080><c> very</c><00:01:16.320><c> simple</c><00:01:16.500><c> example</c><00:01:16.920><c> here</c><00:01:17.280><c> is</c><00:01:18.000><c> asking</c>

00:01:18.469 --> 00:01:18.479 align:start position:0%
a very simple example here is asking
 

00:01:18.479 --> 00:01:20.810 align:start position:0%
a very simple example here is asking
chat<00:01:18.659><c> gbt</c><00:01:19.260><c> to</c><00:01:19.680><c> evaluate</c><00:01:20.159><c> something</c><00:01:20.460><c> like</c><00:01:20.640><c> a</c>

00:01:20.810 --> 00:01:20.820 align:start position:0%
chat gbt to evaluate something like a
 

00:01:20.820 --> 00:01:23.690 align:start position:0%
chat gbt to evaluate something like a
simple<00:01:20.939><c> quadratic</c><00:01:21.960><c> so</c><00:01:22.560><c> we</c><00:01:22.860><c> can</c><00:01:22.979><c> see</c><00:01:23.159><c> that</c><00:01:23.520><c> it</c>

00:01:23.690 --> 00:01:23.700 align:start position:0%
simple quadratic so we can see that it
 

00:01:23.700 --> 00:01:25.609 align:start position:0%
simple quadratic so we can see that it
goes<00:01:23.939><c> through</c><00:01:24.060><c> the</c><00:01:24.299><c> reasoning</c>

00:01:25.609 --> 00:01:25.619 align:start position:0%
goes through the reasoning
 

00:01:25.619 --> 00:01:26.870 align:start position:0%
goes through the reasoning
um it<00:01:25.920><c> tries</c><00:01:26.100><c> to</c><00:01:26.280><c> do</c><00:01:26.400><c> all</c><00:01:26.520><c> of</c><00:01:26.700><c> these</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
um it tries to do all of these
 

00:01:26.880 --> 00:01:29.210 align:start position:0%
um it tries to do all of these
calculations<00:01:27.439><c> and</c><00:01:28.439><c> it</c><00:01:28.619><c> makes</c><00:01:28.799><c> one</c><00:01:29.040><c> big</c>

00:01:29.210 --> 00:01:29.220 align:start position:0%
calculations and it makes one big
 

00:01:29.220 --> 00:01:31.130 align:start position:0%
calculations and it makes one big
mistake<00:01:29.520><c> here</c><00:01:29.759><c> which</c><00:01:30.119><c> is</c><00:01:30.299><c> calculating</c><00:01:30.900><c> the</c>

00:01:31.130 --> 00:01:31.140 align:start position:0%
mistake here which is calculating the
 

00:01:31.140 --> 00:01:33.410 align:start position:0%
mistake here which is calculating the
cube<00:01:31.500><c> of</c><00:01:31.680><c> 0.06</c>

00:01:33.410 --> 00:01:33.420 align:start position:0%
cube of 0.06
 

00:01:33.420 --> 00:01:36.890 align:start position:0%
cube of 0.06
um<00:01:33.479><c> way</c><00:01:34.140><c> off</c><00:01:34.380><c> and</c><00:01:35.360><c> subsequently</c><00:01:36.360><c> our</c><00:01:36.780><c> final</c>

00:01:36.890 --> 00:01:36.900 align:start position:0%
um way off and subsequently our final
 

00:01:36.900 --> 00:01:40.670 align:start position:0%
um way off and subsequently our final
answer<00:01:37.259><c> of</c><00:01:37.860><c> 33</c><00:01:38.420><c> is</c><00:01:39.420><c> way</c><00:01:39.720><c> off</c>

00:01:40.670 --> 00:01:40.680 align:start position:0%
answer of 33 is way off
 

00:01:40.680 --> 00:01:43.789 align:start position:0%
answer of 33 is way off
now<00:01:41.460><c> with</c><00:01:42.119><c> the</c><00:01:42.299><c> Llama</c><00:01:42.540><c> index</c><00:01:42.900><c> data</c><00:01:43.259><c> agent</c><00:01:43.560><c> we</c>

00:01:43.789 --> 00:01:43.799 align:start position:0%
now with the Llama index data agent we
 

00:01:43.799 --> 00:01:45.830 align:start position:0%
now with the Llama index data agent we
can<00:01:43.920><c> provide</c><00:01:44.280><c> it</c><00:01:44.460><c> the</c><00:01:44.700><c> Wolfram</c><00:01:45.119><c> Alpha</c><00:01:45.540><c> tool</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
can provide it the Wolfram Alpha tool
 

00:01:45.840 --> 00:01:47.569 align:start position:0%
can provide it the Wolfram Alpha tool
and<00:01:46.439><c> this</c><00:01:46.680><c> is</c><00:01:46.799><c> the</c><00:01:46.979><c> exact</c><00:01:47.220><c> same</c><00:01:47.340><c> large</c>

00:01:47.569 --> 00:01:47.579 align:start position:0%
and this is the exact same large
 

00:01:47.579 --> 00:01:51.130 align:start position:0%
and this is the exact same large
language<00:01:47.880><c> model</c><00:01:48.240><c> uh</c><00:01:48.720><c> chat</c><00:01:49.079><c> gbt</c><00:01:49.560><c> 3.5</c>

00:01:51.130 --> 00:01:51.140 align:start position:0%
language model uh chat gbt 3.5
 

00:01:51.140 --> 00:01:53.749 align:start position:0%
language model uh chat gbt 3.5
and<00:01:52.140><c> instead</c><00:01:52.680><c> of</c><00:01:52.799><c> trying</c><00:01:53.100><c> to</c><00:01:53.280><c> walk</c><00:01:53.579><c> through</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
and instead of trying to walk through
 

00:01:53.759 --> 00:01:55.789 align:start position:0%
and instead of trying to walk through
the<00:01:53.939><c> math</c><00:01:54.060><c> itself</c><00:01:54.420><c> we</c><00:01:54.780><c> can</c><00:01:54.899><c> see</c><00:01:55.079><c> that</c><00:01:55.439><c> the</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
the math itself we can see that the
 

00:01:55.799 --> 00:01:57.590 align:start position:0%
the math itself we can see that the
agent<00:01:56.100><c> is</c><00:01:56.280><c> just</c><00:01:56.460><c> going</c><00:01:56.579><c> to</c><00:01:56.700><c> go</c><00:01:56.820><c> ahead</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
agent is just going to go ahead
 

00:01:57.600 --> 00:02:00.710 align:start position:0%
agent is just going to go ahead
pass<00:01:58.259><c> the</c><00:01:58.860><c> equation</c><00:01:59.340><c> to</c><00:01:59.700><c> evaluate</c><00:02:00.060><c> wall</c><00:02:00.420><c> frame</c>

00:02:00.710 --> 00:02:00.720 align:start position:0%
pass the equation to evaluate wall frame
 

00:02:00.720 --> 00:02:03.410 align:start position:0%
pass the equation to evaluate wall frame
Alpha<00:02:01.079><c> and</c><00:02:01.740><c> we</c><00:02:02.159><c> get</c><00:02:02.340><c> the</c><00:02:02.759><c> answer</c><00:02:02.880><c> right</c><00:02:03.240><c> back</c>

00:02:03.410 --> 00:02:03.420 align:start position:0%
Alpha and we get the answer right back
 

00:02:03.420 --> 00:02:05.090 align:start position:0%
Alpha and we get the answer right back
without<00:02:03.659><c> having</c><00:02:04.020><c> to</c><00:02:04.200><c> do</c><00:02:04.320><c> math</c><00:02:04.560><c> we</c><00:02:04.860><c> end</c><00:02:04.979><c> up</c>

00:02:05.090 --> 00:02:05.100 align:start position:0%
without having to do math we end up
 

00:02:05.100 --> 00:02:07.730 align:start position:0%
without having to do math we end up
using<00:02:05.399><c> less</c><00:02:05.640><c> tokens</c><00:02:06.240><c> than</c><00:02:06.719><c> having</c><00:02:07.200><c> the</c><00:02:07.500><c> large</c>

00:02:07.730 --> 00:02:07.740 align:start position:0%
using less tokens than having the large
 

00:02:07.740 --> 00:02:10.190 align:start position:0%
using less tokens than having the large
language<00:02:08.039><c> model</c><00:02:08.399><c> try</c><00:02:08.640><c> to</c><00:02:08.759><c> reason</c><00:02:08.940><c> it</c><00:02:09.239><c> out</c>

00:02:10.190 --> 00:02:10.200 align:start position:0%
language model try to reason it out
 

00:02:10.200 --> 00:02:11.809 align:start position:0%
language model try to reason it out
and<00:02:10.619><c> so</c><00:02:10.800><c> you</c><00:02:10.920><c> can</c><00:02:11.039><c> see</c><00:02:11.160><c> here</c><00:02:11.340><c> that</c><00:02:11.520><c> we've</c>

00:02:11.809 --> 00:02:11.819 align:start position:0%
and so you can see here that we've
 

00:02:11.819 --> 00:02:13.970 align:start position:0%
and so you can see here that we've
really<00:02:12.000><c> extended</c><00:02:12.540><c> the</c><00:02:12.720><c> capabilities</c><00:02:13.260><c> of</c><00:02:13.739><c> what</c>

00:02:13.970 --> 00:02:13.980 align:start position:0%
really extended the capabilities of what
 

00:02:13.980 --> 00:02:16.130 align:start position:0%
really extended the capabilities of what
our<00:02:14.160><c> large</c><00:02:14.340><c> language</c><00:02:14.580><c> model</c><00:02:14.940><c> can</c><00:02:15.239><c> do</c>

00:02:16.130 --> 00:02:16.140 align:start position:0%
our large language model can do
 

00:02:16.140 --> 00:02:17.869 align:start position:0%
our large language model can do
um suddenly<00:02:16.560><c> we're</c><00:02:16.800><c> combining</c><00:02:17.340><c> all</c><00:02:17.580><c> of</c><00:02:17.760><c> the</c>

00:02:17.869 --> 00:02:17.879 align:start position:0%
um suddenly we're combining all of the
 

00:02:17.879 --> 00:02:20.150 align:start position:0%
um suddenly we're combining all of the
functionality<00:02:18.420><c> that</c><00:02:18.780><c> Wolfram</c><00:02:19.200><c> Alpha</c><00:02:19.680><c> has</c><00:02:19.860><c> to</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
functionality that Wolfram Alpha has to
 

00:02:20.160 --> 00:02:22.490 align:start position:0%
functionality that Wolfram Alpha has to
do<00:02:20.340><c> math</c><00:02:20.580><c> and</c><00:02:20.819><c> science</c><00:02:21.060><c> problems</c><00:02:21.599><c> and</c><00:02:22.319><c> we're</c>

00:02:22.490 --> 00:02:22.500 align:start position:0%
do math and science problems and we're
 

00:02:22.500 --> 00:02:23.809 align:start position:0%
do math and science problems and we're
adding<00:02:22.860><c> that</c><00:02:22.980><c> into</c><00:02:23.220><c> our</c><00:02:23.400><c> large</c><00:02:23.580><c> language</c>

00:02:23.809 --> 00:02:23.819 align:start position:0%
adding that into our large language
 

00:02:23.819 --> 00:02:27.530 align:start position:0%
adding that into our large language
model<00:02:24.379><c> so</c><00:02:25.379><c> the</c><00:02:25.980><c> idea</c><00:02:26.160><c> with</c><00:02:26.340><c> lava</c><00:02:26.760><c> index</c><00:02:27.060><c> is</c>

00:02:27.530 --> 00:02:27.540 align:start position:0%
model so the idea with lava index is
 

00:02:27.540 --> 00:02:29.210 align:start position:0%
model so the idea with lava index is
that<00:02:27.900><c> it's</c><00:02:28.260><c> going</c><00:02:28.440><c> to</c><00:02:28.560><c> be</c><00:02:28.620><c> as</c><00:02:28.800><c> easy</c><00:02:28.980><c> as</c>

00:02:29.210 --> 00:02:29.220 align:start position:0%
that it's going to be as easy as
 

00:02:29.220 --> 00:02:31.250 align:start position:0%
that it's going to be as easy as
possible<00:02:29.640><c> to</c><00:02:30.180><c> build</c><00:02:30.480><c> your</c><00:02:30.660><c> own</c><00:02:30.780><c> custom</c><00:02:30.959><c> bad</c>

00:02:31.250 --> 00:02:31.260 align:start position:0%
possible to build your own custom bad
 

00:02:31.260 --> 00:02:33.770 align:start position:0%
possible to build your own custom bad
engines<00:02:31.800><c> that</c><00:02:31.980><c> do</c><00:02:32.220><c> these</c><00:02:32.520><c> types</c><00:02:32.700><c> of</c><00:02:32.819><c> things</c>

00:02:33.770 --> 00:02:33.780 align:start position:0%
engines that do these types of things
 

00:02:33.780 --> 00:02:35.570 align:start position:0%
engines that do these types of things
um the<00:02:34.379><c> code</c><00:02:34.500><c> below</c><00:02:34.800><c> here</c><00:02:35.040><c> that's</c><00:02:35.280><c> the</c>

00:02:35.570 --> 00:02:35.580 align:start position:0%
um the code below here that's the
 

00:02:35.580 --> 00:02:37.430 align:start position:0%
um the code below here that's the
entirety<00:02:36.000><c> of</c><00:02:36.180><c> the</c><00:02:36.300><c> code</c><00:02:36.480><c> needed</c><00:02:36.840><c> to</c><00:02:37.140><c> make</c><00:02:37.260><c> a</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
entirety of the code needed to make a
 

00:02:37.440 --> 00:02:39.949 align:start position:0%
entirety of the code needed to make a
Wolfram<00:02:37.800><c> Alpha</c><00:02:38.220><c> data</c><00:02:38.580><c> agent</c><00:02:38.819><c> that</c><00:02:39.360><c> answered</c>

00:02:39.949 --> 00:02:39.959 align:start position:0%
Wolfram Alpha data agent that answered
 

00:02:39.959 --> 00:02:42.589 align:start position:0%
Wolfram Alpha data agent that answered
that<00:02:40.200><c> quadratic</c><00:02:40.860><c> equation</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
that quadratic equation
 

00:02:42.599 --> 00:02:44.030 align:start position:0%
that quadratic equation
um<00:02:42.660><c> and</c><00:02:42.840><c> we</c><00:02:43.019><c> can</c><00:02:43.140><c> walk</c><00:02:43.260><c> through</c><00:02:43.440><c> it</c><00:02:43.620><c> the</c><00:02:43.739><c> code's</c>

00:02:44.030 --> 00:02:44.040 align:start position:0%
um and we can walk through it the code's
 

00:02:44.040 --> 00:02:46.009 align:start position:0%
um and we can walk through it the code's
pretty<00:02:44.220><c> simple</c><00:02:44.400><c> we're</c><00:02:44.819><c> importing</c><00:02:45.360><c> a</c><00:02:45.599><c> Wolfram</c>

00:02:46.009 --> 00:02:46.019 align:start position:0%
pretty simple we're importing a Wolfram
 

00:02:46.019 --> 00:02:48.589 align:start position:0%
pretty simple we're importing a Wolfram
Alpha<00:02:46.379><c> tool</c><00:02:46.739><c> from</c><00:02:46.920><c> llama</c><00:02:47.340><c> Hub</c><00:02:47.580><c> we're</c><00:02:48.180><c> opening</c>

00:02:48.589 --> 00:02:48.599 align:start position:0%
Alpha tool from llama Hub we're opening
 

00:02:48.599 --> 00:02:50.930 align:start position:0%
Alpha tool from llama Hub we're opening
it<00:02:48.720><c> we're</c><00:02:48.959><c> importing</c><00:02:49.440><c> a</c><00:02:49.739><c> open</c><00:02:49.920><c> AI</c><00:02:50.459><c> agent</c>

00:02:50.930 --> 00:02:50.940 align:start position:0%
it we're importing a open AI agent
 

00:02:50.940 --> 00:02:54.410 align:start position:0%
it we're importing a open AI agent
abstraction<00:02:51.480><c> from</c><00:02:51.780><c> Lama</c><00:02:52.140><c> index</c><00:02:53.099><c> and</c>

00:02:54.410 --> 00:02:54.420 align:start position:0%
abstraction from Lama index and
 

00:02:54.420 --> 00:02:56.150 align:start position:0%
abstraction from Lama index and
all<00:02:54.900><c> we</c><00:02:55.019><c> really</c><00:02:55.140><c> have</c><00:02:55.319><c> to</c><00:02:55.440><c> do</c><00:02:55.560><c> is</c><00:02:55.620><c> initialize</c>

00:02:56.150 --> 00:02:56.160 align:start position:0%
all we really have to do is initialize
 

00:02:56.160 --> 00:02:58.550 align:start position:0%
all we really have to do is initialize
that<00:02:56.400><c> tool</c><00:02:56.640><c> with</c><00:02:56.940><c> our</c><00:02:57.060><c> API</c><00:02:57.540><c> key</c>

00:02:58.550 --> 00:02:58.560 align:start position:0%
that tool with our API key
 

00:02:58.560 --> 00:03:00.470 align:start position:0%
that tool with our API key
and<00:02:59.040><c> we</c><00:02:59.280><c> can</c><00:02:59.340><c> pass</c><00:02:59.519><c> that</c><00:02:59.760><c> tool</c><00:03:00.060><c> directly</c><00:03:00.300><c> to</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
and we can pass that tool directly to
 

00:03:00.480 --> 00:03:02.690 align:start position:0%
and we can pass that tool directly to
the<00:03:00.599><c> openai</c><00:03:01.019><c> agent</c><00:03:01.560><c> and</c><00:03:01.920><c> we're</c><00:03:02.099><c> ready</c><00:03:02.280><c> to</c><00:03:02.459><c> chat</c>

00:03:02.690 --> 00:03:02.700 align:start position:0%
the openai agent and we're ready to chat
 

00:03:02.700 --> 00:03:04.190 align:start position:0%
the openai agent and we're ready to chat
with<00:03:02.879><c> it</c><00:03:03.000><c> we're</c><00:03:03.239><c> ready</c><00:03:03.480><c> to</c><00:03:03.660><c> ask</c><00:03:03.780><c> it</c><00:03:03.959><c> math</c>

00:03:04.190 --> 00:03:04.200 align:start position:0%
with it we're ready to ask it math
 

00:03:04.200 --> 00:03:07.130 align:start position:0%
with it we're ready to ask it math
questions<00:03:04.940><c> and</c><00:03:05.940><c> we're</c><00:03:06.180><c> combining</c><00:03:06.660><c> that</c><00:03:06.840><c> with</c>

00:03:07.130 --> 00:03:07.140 align:start position:0%
questions and we're combining that with
 

00:03:07.140 --> 00:03:08.809 align:start position:0%
questions and we're combining that with
the<00:03:07.319><c> language</c><00:03:07.500><c> capabilities</c><00:03:08.160><c> of</c><00:03:08.580><c> the</c><00:03:08.700><c> large</c>

00:03:08.809 --> 00:03:08.819 align:start position:0%
the language capabilities of the large
 

00:03:08.819 --> 00:03:10.070 align:start position:0%
the language capabilities of the large
language<00:03:09.120><c> model</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
language model
 

00:03:10.080 --> 00:03:11.930 align:start position:0%
language model
um<00:03:10.140><c> so</c><00:03:10.319><c> you</c><00:03:10.440><c> can</c><00:03:10.560><c> see</c><00:03:10.680><c> here</c><00:03:11.040><c> I</c><00:03:11.459><c> have</c><00:03:11.580><c> like</c><00:03:11.760><c> the</c>

00:03:11.930 --> 00:03:11.940 align:start position:0%
um so you can see here I have like the
 

00:03:11.940 --> 00:03:14.509 align:start position:0%
um so you can see here I have like the
same<00:03:12.060><c> notebook</c><00:03:12.480><c> we</c><00:03:13.140><c> import</c><00:03:13.319><c> open</c><00:03:13.680><c> AI</c><00:03:14.099><c> we</c>

00:03:14.509 --> 00:03:14.519 align:start position:0%
same notebook we import open AI we
 

00:03:14.519 --> 00:03:16.670 align:start position:0%
same notebook we import open AI we
import<00:03:14.640><c> our</c><00:03:14.879><c> tools</c><00:03:15.239><c> from</c><00:03:15.360><c> Mama</c><00:03:15.540><c> index</c>

00:03:16.670 --> 00:03:16.680 align:start position:0%
import our tools from Mama index
 

00:03:16.680 --> 00:03:18.949 align:start position:0%
import our tools from Mama index
and<00:03:17.220><c> we</c><00:03:17.519><c> can</c><00:03:17.640><c> go</c><00:03:17.760><c> right</c><00:03:17.940><c> ahead</c><00:03:18.239><c> and</c><00:03:18.480><c> we</c><00:03:18.659><c> can</c><00:03:18.720><c> ask</c>

00:03:18.949 --> 00:03:18.959 align:start position:0%
and we can go right ahead and we can ask
 

00:03:18.959 --> 00:03:21.830 align:start position:0%
and we can go right ahead and we can ask
it<00:03:19.140><c> to</c><00:03:19.560><c> evaluate</c><00:03:20.099><c> our</c><00:03:20.280><c> math</c><00:03:20.459><c> problem</c><00:03:20.700><c> and</c><00:03:21.659><c> you</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
it to evaluate our math problem and you
 

00:03:21.840 --> 00:03:23.509 align:start position:0%
it to evaluate our math problem and you
can<00:03:21.959><c> see</c><00:03:22.080><c> here</c><00:03:22.379><c> this</c><00:03:22.680><c> is</c><00:03:22.800><c> the</c><00:03:23.040><c> large</c><00:03:23.220><c> language</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
can see here this is the large language
 

00:03:23.519 --> 00:03:24.830 align:start position:0%
can see here this is the large language
model

00:03:24.830 --> 00:03:24.840 align:start position:0%
model
 

00:03:24.840 --> 00:03:27.290 align:start position:0%
model
um<00:03:24.980><c> adding</c><00:03:25.980><c> in</c><00:03:26.099><c> the</c><00:03:26.220><c> text</c><00:03:26.340><c> capabilities</c><00:03:26.940><c> to</c>

00:03:27.290 --> 00:03:27.300 align:start position:0%
um adding in the text capabilities to
 

00:03:27.300 --> 00:03:30.790 align:start position:0%
um adding in the text capabilities to
like<00:03:27.420><c> the</c><00:03:27.659><c> simple</c><00:03:27.840><c> output</c><00:03:28.800><c> that</c><00:03:29.040><c> we</c><00:03:29.159><c> got</c>

00:03:30.790 --> 00:03:30.800 align:start position:0%
like the simple output that we got
 

00:03:30.800 --> 00:03:33.229 align:start position:0%
like the simple output that we got
now<00:03:32.159><c> um</c><00:03:32.159><c> for</c><00:03:32.340><c> a</c><00:03:32.519><c> bit</c><00:03:32.580><c> more</c><00:03:32.760><c> complicated</c>

00:03:33.229 --> 00:03:33.239 align:start position:0%
now um for a bit more complicated
 

00:03:33.239 --> 00:03:36.110 align:start position:0%
now um for a bit more complicated
example<00:03:33.720><c> if</c><00:03:33.959><c> it</c><00:03:34.140><c> weren't</c><00:03:34.379><c> going</c><00:03:34.560><c> on</c><00:03:34.739><c> uh</c><00:03:35.700><c> one</c><00:03:36.060><c> of</c>

00:03:36.110 --> 00:03:36.120 align:start position:0%
example if it weren't going on uh one of
 

00:03:36.120 --> 00:03:38.570 align:start position:0%
example if it weren't going on uh one of
the<00:03:36.180><c> limitations</c><00:03:36.540><c> we</c><00:03:36.840><c> talked</c><00:03:37.140><c> about</c><00:03:37.200><c> was</c><00:03:38.040><c> news</c>

00:03:38.570 --> 00:03:38.580 align:start position:0%
the limitations we talked about was news
 

00:03:38.580 --> 00:03:41.449 align:start position:0%
the limitations we talked about was news
retrieval<00:03:39.239><c> so</c><00:03:39.780><c> if</c><00:03:39.900><c> we</c><00:03:40.019><c> ask</c><00:03:40.140><c> Chachi</c><00:03:40.680><c> b2i</c><00:03:41.220><c> who</c>

00:03:41.449 --> 00:03:41.459 align:start position:0%
retrieval so if we ask Chachi b2i who
 

00:03:41.459 --> 00:03:44.750 align:start position:0%
retrieval so if we ask Chachi b2i who
won<00:03:41.640><c> the</c><00:03:41.819><c> NBA</c><00:03:42.060><c> playoffs</c><00:03:42.540><c> in</c><00:03:42.659><c> 2023</c><00:03:43.340><c> get</c><00:03:44.340><c> the</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
won the NBA playoffs in 2023 get the
 

00:03:44.760 --> 00:03:48.110 align:start position:0%
won the NBA playoffs in 2023 get the
generic<00:03:45.299><c> knowledge</c><00:03:45.720><c> cut</c><00:03:46.019><c> off</c><00:03:46.260><c> answer</c>

00:03:48.110 --> 00:03:48.120 align:start position:0%
generic knowledge cut off answer
 

00:03:48.120 --> 00:03:49.130 align:start position:0%
generic knowledge cut off answer
um

00:03:49.130 --> 00:03:49.140 align:start position:0%
um
 

00:03:49.140 --> 00:03:51.770 align:start position:0%
um
no<00:03:49.620><c> access</c><00:03:49.799><c> to</c><00:03:50.099><c> real-time</c><00:03:50.459><c> information</c><00:03:50.780><c> now</c>

00:03:51.770 --> 00:03:51.780 align:start position:0%
no access to real-time information now
 

00:03:51.780 --> 00:03:54.890 align:start position:0%
no access to real-time information now
luckily<00:03:52.319><c> with</c><00:03:52.560><c> llama</c><00:03:52.799><c> index</c><00:03:53.159><c> we</c><00:03:53.940><c> can</c><00:03:54.120><c> give</c><00:03:54.480><c> our</c>

00:03:54.890 --> 00:03:54.900 align:start position:0%
luckily with llama index we can give our
 

00:03:54.900 --> 00:03:57.530 align:start position:0%
luckily with llama index we can give our
agent<00:03:55.319><c> access</c><00:03:55.620><c> to</c><00:03:55.980><c> real-time</c><00:03:56.280><c> information</c>

00:03:57.530 --> 00:03:57.540 align:start position:0%
agent access to real-time information
 

00:03:57.540 --> 00:04:00.830 align:start position:0%
agent access to real-time information
so<00:03:58.200><c> when</c><00:03:58.860><c> we</c><00:03:59.040><c> ask</c><00:03:59.159><c> a</c><00:03:59.400><c> lot</c><00:03:59.580><c> of</c><00:03:59.760><c> index</c><00:04:00.299><c> agent</c><00:04:00.599><c> with</c>

00:04:00.830 --> 00:04:00.840 align:start position:0%
so when we ask a lot of index agent with
 

00:04:00.840 --> 00:04:02.869 align:start position:0%
so when we ask a lot of index agent with
access<00:04:01.080><c> to</c><00:04:01.379><c> Wikipedia</c><00:04:01.920><c> who</c><00:04:02.159><c> won</c><00:04:02.400><c> the</c><00:04:02.519><c> NBA</c>

00:04:02.869 --> 00:04:02.879 align:start position:0%
access to Wikipedia who won the NBA
 

00:04:02.879 --> 00:04:04.550 align:start position:0%
access to Wikipedia who won the NBA
playoffs<00:04:03.299><c> in</c><00:04:03.420><c> 2023</c>

00:04:04.550 --> 00:04:04.560 align:start position:0%
playoffs in 2023
 

00:04:04.560 --> 00:04:07.670 align:start position:0%
playoffs in 2023
you<00:04:04.980><c> can</c><00:04:05.099><c> see</c><00:04:05.280><c> we</c><00:04:05.640><c> load</c><00:04:05.940><c> some</c><00:04:06.120><c> content</c><00:04:06.480><c> and</c><00:04:07.379><c> we</c>

00:04:07.670 --> 00:04:07.680 align:start position:0%
you can see we load some content and we
 

00:04:07.680 --> 00:04:09.410 align:start position:0%
you can see we load some content and we
get<00:04:07.860><c> the</c><00:04:07.980><c> answers</c><00:04:08.220><c> at</c><00:04:08.640><c> the</c><00:04:08.760><c> Denver</c><00:04:08.879><c> Nuggets</c>

00:04:09.410 --> 00:04:09.420 align:start position:0%
get the answers at the Denver Nuggets
 

00:04:09.420 --> 00:04:12.470 align:start position:0%
get the answers at the Denver Nuggets
won<00:04:09.599><c> the</c><00:04:09.840><c> 22</c><00:04:10.200><c> 2023</c><00:04:11.099><c> NBA</c><00:04:11.640><c> finals</c><00:04:12.060><c> and</c><00:04:12.360><c> that's</c>

00:04:12.470 --> 00:04:12.480 align:start position:0%
won the 22 2023 NBA finals and that's
 

00:04:12.480 --> 00:04:13.850 align:start position:0%
won the 22 2023 NBA finals and that's
the<00:04:12.659><c> right</c><00:04:12.840><c> answer</c>

00:04:13.850 --> 00:04:13.860 align:start position:0%
the right answer
 

00:04:13.860 --> 00:04:16.610 align:start position:0%
the right answer
so<00:04:14.400><c> the</c><00:04:15.000><c> Wikipedia</c><00:04:15.540><c> tool</c><00:04:15.900><c> is</c><00:04:16.139><c> letting</c><00:04:16.500><c> the</c>

00:04:16.610 --> 00:04:16.620 align:start position:0%
so the Wikipedia tool is letting the
 

00:04:16.620 --> 00:04:18.349 align:start position:0%
so the Wikipedia tool is letting the
agent<00:04:16.919><c> search</c><00:04:17.160><c> and</c><00:04:17.400><c> retrieve</c><00:04:17.820><c> articles</c><00:04:18.180><c> from</c>

00:04:18.349 --> 00:04:18.359 align:start position:0%
agent search and retrieve articles from
 

00:04:18.359 --> 00:04:19.909 align:start position:0%
agent search and retrieve articles from
Wikipedia

00:04:19.909 --> 00:04:19.919 align:start position:0%
Wikipedia
 

00:04:19.919 --> 00:04:21.770 align:start position:0%
Wikipedia
and<00:04:20.400><c> one</c><00:04:20.639><c> problem</c><00:04:20.820><c> we</c><00:04:21.000><c> have</c><00:04:21.180><c> is</c><00:04:21.359><c> that</c><00:04:21.540><c> an</c>

00:04:21.770 --> 00:04:21.780 align:start position:0%
and one problem we have is that an
 

00:04:21.780 --> 00:04:24.050 align:start position:0%
and one problem we have is that an
entire<00:04:22.079><c> Wikipedia</c><00:04:22.740><c> page</c><00:04:23.040><c> is</c><00:04:23.160><c> often</c><00:04:23.580><c> too</c><00:04:23.820><c> large</c>

00:04:24.050 --> 00:04:24.060 align:start position:0%
entire Wikipedia page is often too large
 

00:04:24.060 --> 00:04:26.749 align:start position:0%
entire Wikipedia page is often too large
for<00:04:24.419><c> a</c><00:04:24.600><c> large</c><00:04:25.020><c> language</c><00:04:25.199><c> model</c><00:04:25.500><c> to</c><00:04:25.800><c> process</c>

00:04:26.749 --> 00:04:26.759 align:start position:0%
for a large language model to process
 

00:04:26.759 --> 00:04:28.730 align:start position:0%
for a large language model to process
and<00:04:27.240><c> so</c><00:04:27.419><c> that's</c><00:04:27.540><c> why</c><00:04:27.780><c> we</c><00:04:28.020><c> separated</c><00:04:28.440><c> the</c>

00:04:28.730 --> 00:04:28.740 align:start position:0%
and so that's why we separated the
 

00:04:28.740 --> 00:04:30.350 align:start position:0%
and so that's why we separated the
previous<00:04:28.919><c> example</c><00:04:29.580><c> into</c><00:04:29.940><c> two</c><00:04:30.240><c> different</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
previous example into two different
 

00:04:30.360 --> 00:04:32.270 align:start position:0%
previous example into two different
function<00:04:30.780><c> calls</c><00:04:31.199><c> so</c>

00:04:32.270 --> 00:04:32.280 align:start position:0%
function calls so
 

00:04:32.280 --> 00:04:33.710 align:start position:0%
function calls so
I'll<00:04:32.759><c> walk</c><00:04:32.880><c> through</c><00:04:33.060><c> this</c><00:04:33.180><c> a</c><00:04:33.360><c> bit</c><00:04:33.419><c> more</c><00:04:33.540><c> about</c>

00:04:33.710 --> 00:04:33.720 align:start position:0%
I'll walk through this a bit more about
 

00:04:33.720 --> 00:04:35.450 align:start position:0%
I'll walk through this a bit more about
the<00:04:34.020><c> first</c><00:04:34.139><c> function</c><00:04:34.440><c> call</c><00:04:34.620><c> was</c><00:04:34.979><c> to</c><00:04:35.160><c> load</c>

00:04:35.450 --> 00:04:35.460 align:start position:0%
the first function call was to load
 

00:04:35.460 --> 00:04:37.490 align:start position:0%
the first function call was to load
Wikipedia<00:04:36.060><c> output</c><00:04:36.419><c> and</c><00:04:37.020><c> then</c><00:04:37.139><c> the</c><00:04:37.320><c> second</c>

00:04:37.490 --> 00:04:37.500 align:start position:0%
Wikipedia output and then the second
 

00:04:37.500 --> 00:04:41.090 align:start position:0%
Wikipedia output and then the second
function<00:04:37.919><c> call</c><00:04:38.220><c> was</c><00:04:39.180><c> to</c><00:04:39.600><c> query</c><00:04:40.080><c> that</c><00:04:40.320><c> from</c><00:04:40.800><c> a</c>

00:04:41.090 --> 00:04:41.100 align:start position:0%
function call was to query that from a
 

00:04:41.100 --> 00:04:42.710 align:start position:0%
function call was to query that from a
vector<00:04:41.460><c> store</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
vector store
 

00:04:42.720 --> 00:04:44.870 align:start position:0%
vector store
so<00:04:43.560><c> here's</c><00:04:43.979><c> the</c><00:04:44.100><c> first</c><00:04:44.220><c> function</c><00:04:44.520><c> call</c><00:04:44.699><c> that</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
so here's the first function call that
 

00:04:44.880 --> 00:04:47.749 align:start position:0%
so here's the first function call that
we<00:04:45.060><c> made</c><00:04:45.180><c> we</c><00:04:45.479><c> search</c><00:04:45.720><c> data</c><00:04:46.259><c> for</c><00:04:46.979><c> NBA</c><00:04:47.340><c> playoffs</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
we made we search data for NBA playoffs
 

00:04:47.759 --> 00:04:51.350 align:start position:0%
we made we search data for NBA playoffs
2023<00:04:48.419><c> winner</c><00:04:48.860><c> we</c><00:04:49.860><c> loaded</c><00:04:50.340><c> the</c><00:04:50.460><c> content</c><00:04:50.820><c> and</c><00:04:51.180><c> we</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
2023 winner we loaded the content and we
 

00:04:51.360 --> 00:04:52.850 align:start position:0%
2023 winner we loaded the content and we
informed<00:04:51.780><c> the</c><00:04:51.840><c> agent</c><00:04:52.139><c> that</c><00:04:52.380><c> it</c><00:04:52.560><c> can</c><00:04:52.680><c> now</c>

00:04:52.850 --> 00:04:52.860 align:start position:0%
informed the agent that it can now
 

00:04:52.860 --> 00:04:54.590 align:start position:0%
informed the agent that it can now
search<00:04:53.160><c> that</c><00:04:53.400><c> information</c><00:04:53.639><c> using</c><00:04:54.419><c> read</c>

00:04:54.590 --> 00:04:54.600 align:start position:0%
search that information using read
 

00:04:54.600 --> 00:04:56.150 align:start position:0%
search that information using read
search<00:04:54.900><c> data</c>

00:04:56.150 --> 00:04:56.160 align:start position:0%
search data
 

00:04:56.160 --> 00:04:58.129 align:start position:0%
search data
and<00:04:56.759><c> essentially</c><00:04:57.120><c> what</c><00:04:57.360><c> we've</c><00:04:57.540><c> done</c><00:04:57.720><c> is</c><00:04:57.900><c> we've</c>

00:04:58.129 --> 00:04:58.139 align:start position:0%
and essentially what we've done is we've
 

00:04:58.139 --> 00:05:00.770 align:start position:0%
and essentially what we've done is we've
captured<00:04:58.620><c> the</c><00:04:58.800><c> output</c><00:04:59.160><c> from</c><00:04:59.880><c> searching</c>

00:05:00.770 --> 00:05:00.780 align:start position:0%
captured the output from searching
 

00:05:00.780 --> 00:05:02.689 align:start position:0%
captured the output from searching
Wikipedia<00:05:01.380><c> and</c><00:05:01.620><c> we've</c><00:05:01.860><c> stored</c><00:05:02.220><c> all</c><00:05:02.400><c> of</c><00:05:02.520><c> those</c>

00:05:02.689 --> 00:05:02.699 align:start position:0%
Wikipedia and we've stored all of those
 

00:05:02.699 --> 00:05:04.909 align:start position:0%
Wikipedia and we've stored all of those
pages<00:05:03.000><c> into</c><00:05:03.419><c> a</c><00:05:03.660><c> vector</c><00:05:04.020><c> store</c>

00:05:04.909 --> 00:05:04.919 align:start position:0%
pages into a vector store
 

00:05:04.919 --> 00:05:07.790 align:start position:0%
pages into a vector store
and<00:05:05.400><c> now</c><00:05:05.580><c> we</c><00:05:06.180><c> can</c><00:05:06.300><c> query</c><00:05:06.720><c> that</c><00:05:07.259><c> Vector</c><00:05:07.620><c> store</c>

00:05:07.790 --> 00:05:07.800 align:start position:0%
and now we can query that Vector store
 

00:05:07.800 --> 00:05:10.550 align:start position:0%
and now we can query that Vector store
essentially<00:05:08.400><c> as</c><00:05:08.580><c> a</c><00:05:08.759><c> query</c><00:05:08.940><c> engine</c><00:05:09.259><c> and</c><00:05:10.259><c> we</c><00:05:10.500><c> can</c>

00:05:10.550 --> 00:05:10.560 align:start position:0%
essentially as a query engine and we can
 

00:05:10.560 --> 00:05:12.950 align:start position:0%
essentially as a query engine and we can
ask<00:05:10.740><c> it</c><00:05:10.919><c> who</c><00:05:11.160><c> won</c><00:05:11.340><c> the</c><00:05:11.460><c> NBA</c><00:05:11.820><c> playoffs</c><00:05:12.240><c> in</c><00:05:12.360><c> 2023</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
ask it who won the NBA playoffs in 2023
 

00:05:12.960 --> 00:05:15.050 align:start position:0%
ask it who won the NBA playoffs in 2023
and<00:05:13.740><c> we'll</c><00:05:13.860><c> get</c><00:05:14.040><c> the</c><00:05:14.160><c> output</c><00:05:14.520><c> the</c><00:05:14.880><c> Denver</c>

00:05:15.050 --> 00:05:15.060 align:start position:0%
and we'll get the output the Denver
 

00:05:15.060 --> 00:05:19.730 align:start position:0%
and we'll get the output the Denver
Nuggets<00:05:15.600><c> won</c><00:05:15.720><c> the</c><00:05:15.900><c> 2023</c><00:05:16.500><c> MBA</c><00:05:17.100><c> playoffs</c>

00:05:19.730 --> 00:05:19.740 align:start position:0%
 
 

00:05:19.740 --> 00:05:21.710 align:start position:0%
 
um<00:05:20.280><c> since</c><00:05:20.820><c> Adventure</c><00:05:21.300><c> store</c><00:05:21.419><c> has</c><00:05:21.600><c> already</c>

00:05:21.710 --> 00:05:21.720 align:start position:0%
um since Adventure store has already
 

00:05:21.720 --> 00:05:23.870 align:start position:0%
um since Adventure store has already
been<00:05:21.960><c> populated</c><00:05:22.380><c> with</c><00:05:22.680><c> context</c><00:05:22.979><c> rather</c><00:05:23.340><c> 22</c><00:05:23.699><c> 3</c>

00:05:23.870 --> 00:05:23.880 align:start position:0%
been populated with context rather 22 3
 

00:05:23.880 --> 00:05:25.730 align:start position:0%
been populated with context rather 22 3
NBA<00:05:24.360><c> plots</c><00:05:24.660><c> we</c><00:05:24.900><c> can</c><00:05:25.020><c> even</c><00:05:25.139><c> ask</c><00:05:25.440><c> follow</c>

00:05:25.730 --> 00:05:25.740 align:start position:0%
NBA plots we can even ask follow
 

00:05:25.740 --> 00:05:28.490 align:start position:0%
NBA plots we can even ask follow
questions<00:05:26.160><c> that</c><00:05:26.580><c> the</c><00:05:26.699><c> agent</c><00:05:27.000><c> can</c><00:05:27.180><c> answer</c><00:05:27.500><c> and</c>

00:05:28.490 --> 00:05:28.500 align:start position:0%
questions that the agent can answer and
 

00:05:28.500 --> 00:05:31.430 align:start position:0%
questions that the agent can answer and
we<00:05:28.680><c> don't</c><00:05:28.800><c> need</c><00:05:29.039><c> to</c><00:05:29.660><c> load</c><00:05:30.660><c> the</c><00:05:30.720><c> pages</c><00:05:30.900><c> again</c><00:05:31.199><c> so</c>

00:05:31.430 --> 00:05:31.440 align:start position:0%
we don't need to load the pages again so
 

00:05:31.440 --> 00:05:33.050 align:start position:0%
we don't need to load the pages again so
you<00:05:31.560><c> can</c><00:05:31.680><c> see</c><00:05:31.860><c> when</c><00:05:32.160><c> we</c><00:05:32.280><c> ask</c><00:05:32.460><c> the</c><00:05:32.639><c> agent</c><00:05:32.880><c> who</c>

00:05:33.050 --> 00:05:33.060 align:start position:0%
you can see when we ask the agent who
 

00:05:33.060 --> 00:05:35.689 align:start position:0%
you can see when we ask the agent who
did<00:05:33.240><c> they</c><00:05:33.419><c> beat</c><00:05:33.800><c> uh</c><00:05:34.800><c> we</c><00:05:35.039><c> directly</c><00:05:35.400><c> get</c><00:05:35.580><c> the</c>

00:05:35.689 --> 00:05:35.699 align:start position:0%
did they beat uh we directly get the
 

00:05:35.699 --> 00:05:37.010 align:start position:0%
did they beat uh we directly get the
answer<00:05:35.820><c> without</c><00:05:36.120><c> having</c><00:05:36.479><c> to</c><00:05:36.600><c> load</c><00:05:36.840><c> those</c>

00:05:37.010 --> 00:05:37.020 align:start position:0%
answer without having to load those
 

00:05:37.020 --> 00:05:39.110 align:start position:0%
answer without having to load those
Wikipedia<00:05:37.560><c> Pages</c><00:05:37.800><c> again</c>

00:05:39.110 --> 00:05:39.120 align:start position:0%
Wikipedia Pages again
 

00:05:39.120 --> 00:05:40.850 align:start position:0%
Wikipedia Pages again
so<00:05:39.360><c> we</c><00:05:39.720><c> can</c><00:05:39.900><c> come</c><00:05:40.020><c> in</c><00:05:40.139><c> we</c><00:05:40.380><c> can</c><00:05:40.440><c> look</c><00:05:40.620><c> at</c><00:05:40.740><c> our</c>

00:05:40.850 --> 00:05:40.860 align:start position:0%
so we can come in we can look at our
 

00:05:40.860 --> 00:05:42.469 align:start position:0%
so we can come in we can look at our
example

00:05:42.469 --> 00:05:42.479 align:start position:0%
example
 

00:05:42.479 --> 00:05:44.629 align:start position:0%
example
um<00:05:42.600><c> as</c><00:05:43.020><c> I</c><00:05:43.199><c> talked</c><00:05:43.440><c> about</c><00:05:43.440><c> in</c><00:05:43.740><c> the</c><00:05:43.919><c> slides</c><00:05:44.220><c> we</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
um as I talked about in the slides we
 

00:05:44.639 --> 00:05:48.710 align:start position:0%
um as I talked about in the slides we
are<00:05:45.139><c> loading</c><00:05:46.139><c> those</c><00:05:47.240><c> Wikipedia</c><00:05:48.240><c> and</c><00:05:48.479><c> that</c>

00:05:48.710 --> 00:05:48.720 align:start position:0%
are loading those Wikipedia and that
 

00:05:48.720 --> 00:05:51.590 align:start position:0%
are loading those Wikipedia and that
load<00:05:48.960><c> and</c><00:05:49.080><c> search</c><00:05:49.259><c> tool</c>

00:05:51.590 --> 00:05:51.600 align:start position:0%
load and search tool
 

00:05:51.600 --> 00:05:52.909 align:start position:0%
load and search tool
um

00:05:52.909 --> 00:05:52.919 align:start position:0%
um
 

00:05:52.919 --> 00:05:55.370 align:start position:0%
um
we're<00:05:53.580><c> gonna</c><00:05:53.759><c> wrap</c><00:05:54.120><c> our</c><00:05:54.419><c> Wikipedia</c><00:05:55.080><c> tool</c>

00:05:55.370 --> 00:05:55.380 align:start position:0%
we're gonna wrap our Wikipedia tool
 

00:05:55.380 --> 00:05:57.469 align:start position:0%
we're gonna wrap our Wikipedia tool
that's<00:05:55.560><c> loading</c><00:05:55.979><c> the</c><00:05:56.160><c> data</c><00:05:56.520><c> in</c><00:05:57.000><c> the</c><00:05:57.120><c> load</c><00:05:57.360><c> and</c>

00:05:57.469 --> 00:05:57.479 align:start position:0%
that's loading the data in the load and
 

00:05:57.479 --> 00:05:59.210 align:start position:0%
that's loading the data in the load and
search<00:05:57.660><c> load</c><00:05:58.080><c> and</c><00:05:58.259><c> the</c><00:05:58.380><c> load</c><00:05:58.620><c> and</c><00:05:58.680><c> search</c><00:05:58.860><c> tool</c>

00:05:59.210 --> 00:05:59.220 align:start position:0%
search load and the load and search tool
 

00:05:59.220 --> 00:06:00.529 align:start position:0%
search load and the load and search tool
is<00:05:59.280><c> essentially</c><00:05:59.639><c> what</c><00:05:59.940><c> setting</c><00:06:00.240><c> up</c><00:06:00.360><c> our</c>

00:06:00.529 --> 00:06:00.539 align:start position:0%
is essentially what setting up our
 

00:06:00.539 --> 00:06:02.870 align:start position:0%
is essentially what setting up our
Vector<00:06:00.900><c> store</c><00:06:01.080><c> and</c><00:06:01.380><c> our</c><00:06:01.560><c> query</c><00:06:01.800><c> engine</c><00:06:02.039><c> and</c>

00:06:02.870 --> 00:06:02.880 align:start position:0%
Vector store and our query engine and
 

00:06:02.880 --> 00:06:04.790 align:start position:0%
Vector store and our query engine and
it's<00:06:03.000><c> separating</c><00:06:03.360><c> that</c><00:06:03.600><c> Wikipedia</c><00:06:04.259><c> tool</c><00:06:04.560><c> into</c>

00:06:04.790 --> 00:06:04.800 align:start position:0%
it's separating that Wikipedia tool into
 

00:06:04.800 --> 00:06:07.010 align:start position:0%
it's separating that Wikipedia tool into
two<00:06:05.100><c> interfaces</c><00:06:05.699><c> and</c><00:06:06.479><c> the</c><00:06:06.600><c> cool</c><00:06:06.720><c> thing</c><00:06:06.900><c> about</c>

00:06:07.010 --> 00:06:07.020 align:start position:0%
two interfaces and the cool thing about
 

00:06:07.020 --> 00:06:10.969 align:start position:0%
two interfaces and the cool thing about
this<00:06:07.320><c> is</c><00:06:07.740><c> that</c><00:06:08.100><c> any</c><00:06:09.000><c> llama</c><00:06:09.720><c> Hub</c><00:06:10.259><c> tool</c><00:06:10.680><c> can</c>

00:06:10.969 --> 00:06:10.979 align:start position:0%
this is that any llama Hub tool can
 

00:06:10.979 --> 00:06:12.590 align:start position:0%
this is that any llama Hub tool can
easily<00:06:11.280><c> be</c><00:06:11.460><c> wrapped</c><00:06:11.820><c> with</c><00:06:12.000><c> this</c><00:06:12.120><c> load</c><00:06:12.479><c> and</c>

00:06:12.590 --> 00:06:12.600 align:start position:0%
easily be wrapped with this load and
 

00:06:12.600 --> 00:06:14.930 align:start position:0%
easily be wrapped with this load and
search<00:06:12.720><c> tool</c><00:06:13.080><c> and</c><00:06:13.259><c> so</c><00:06:13.500><c> it's</c><00:06:14.220><c> super</c><00:06:14.400><c> easy</c><00:06:14.580><c> you</c>

00:06:14.930 --> 00:06:14.940 align:start position:0%
search tool and so it's super easy you
 

00:06:14.940 --> 00:06:16.430 align:start position:0%
search tool and so it's super easy you
don't<00:06:15.060><c> even</c><00:06:15.180><c> have</c><00:06:15.419><c> to</c><00:06:15.539><c> think</c><00:06:15.720><c> about</c><00:06:15.900><c> this</c><00:06:16.139><c> when</c>

00:06:16.430 --> 00:06:16.440 align:start position:0%
don't even have to think about this when
 

00:06:16.440 --> 00:06:17.990 align:start position:0%
don't even have to think about this when
you're<00:06:16.620><c> building</c><00:06:16.800><c> your</c><00:06:17.100><c> tool</c><00:06:17.400><c> and</c><00:06:17.580><c> it's</c><00:06:17.759><c> very</c>

00:06:17.990 --> 00:06:18.000 align:start position:0%
you're building your tool and it's very
 

00:06:18.000 --> 00:06:21.830 align:start position:0%
you're building your tool and it's very
easy<00:06:18.300><c> to</c><00:06:18.960><c> handle</c><00:06:19.800><c> tools</c><00:06:20.160><c> with</c><00:06:20.340><c> larger</c><00:06:20.639><c> output</c>

00:06:21.830 --> 00:06:21.840 align:start position:0%
easy to handle tools with larger output
 

00:06:21.840 --> 00:06:24.290 align:start position:0%
easy to handle tools with larger output
um so<00:06:22.319><c> we</c><00:06:22.500><c> can</c><00:06:22.620><c> call</c><00:06:22.740><c> it</c><00:06:22.979><c> and</c><00:06:23.639><c> I</c><00:06:23.880><c> have</c><00:06:24.000><c> a</c><00:06:24.060><c> bit</c><00:06:24.180><c> of</c>

00:06:24.290 --> 00:06:24.300 align:start position:0%
um so we can call it and I have a bit of
 

00:06:24.300 --> 00:06:26.870 align:start position:0%
um so we can call it and I have a bit of
extra<00:06:24.479><c> output</c><00:06:24.900><c> coming</c><00:06:25.139><c> here</c><00:06:25.440><c> so</c><00:06:25.979><c> first</c><00:06:26.639><c> we're</c>

00:06:26.870 --> 00:06:26.880 align:start position:0%
extra output coming here so first we're
 

00:06:26.880 --> 00:06:29.390 align:start position:0%
extra output coming here so first we're
doing<00:06:27.060><c> search</c><00:06:27.240><c> data</c><00:06:27.780><c> which</c><00:06:28.020><c> is</c><00:06:28.139><c> going</c><00:06:28.380><c> to</c><00:06:28.560><c> load</c>

00:06:29.390 --> 00:06:29.400 align:start position:0%
doing search data which is going to load
 

00:06:29.400 --> 00:06:31.790 align:start position:0%
doing search data which is going to load
the<00:06:29.880><c> Wikipedia</c><00:06:30.419><c> pages</c><00:06:30.720><c> that</c><00:06:31.080><c> are</c><00:06:31.199><c> relevant</c><00:06:31.560><c> to</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
the Wikipedia pages that are relevant to
 

00:06:31.800 --> 00:06:34.730 align:start position:0%
the Wikipedia pages that are relevant to
the<00:06:31.979><c> MBA</c><00:06:32.520><c> playoffs</c><00:06:32.940><c> 2023</c><00:06:33.539><c> winner</c><00:06:33.840><c> and</c><00:06:34.620><c> then</c>

00:06:34.730 --> 00:06:34.740 align:start position:0%
the MBA playoffs 2023 winner and then
 

00:06:34.740 --> 00:06:36.409 align:start position:0%
the MBA playoffs 2023 winner and then
you<00:06:34.919><c> can</c><00:06:35.039><c> see</c><00:06:35.220><c> these</c><00:06:35.639><c> are</c><00:06:35.759><c> the</c><00:06:36.120><c> entire</c>

00:06:36.409 --> 00:06:36.419 align:start position:0%
you can see these are the entire
 

00:06:36.419 --> 00:06:39.230 align:start position:0%
you can see these are the entire
documents<00:06:37.139><c> that</c><00:06:37.500><c> I'm</c><00:06:37.800><c> getting</c><00:06:38.240><c> from</c>

00:06:39.230 --> 00:06:39.240 align:start position:0%
documents that I'm getting from
 

00:06:39.240 --> 00:06:42.950 align:start position:0%
documents that I'm getting from
Wikipedia<00:06:39.960><c> and</c><00:06:40.620><c> this</c><00:06:41.580><c> is</c><00:06:41.759><c> clearly</c><00:06:42.240><c> way</c><00:06:42.780><c> too</c>

00:06:42.950 --> 00:06:42.960 align:start position:0%
Wikipedia and this is clearly way too
 

00:06:42.960 --> 00:06:46.189 align:start position:0%
Wikipedia and this is clearly way too
large<00:06:43.139><c> to</c><00:06:43.800><c> fit</c><00:06:44.160><c> into</c><00:06:44.400><c> a</c><00:06:44.639><c> large</c><00:06:44.880><c> language</c><00:06:45.120><c> model</c>

00:06:46.189 --> 00:06:46.199 align:start position:0%
large to fit into a large language model
 

00:06:46.199 --> 00:06:47.870 align:start position:0%
large to fit into a large language model
it<00:06:46.740><c> would</c><00:06:46.860><c> blow</c><00:06:47.100><c> up</c><00:06:47.160><c> the</c><00:06:47.340><c> context</c><00:06:47.639><c> window</c>

00:06:47.870 --> 00:06:47.880 align:start position:0%
it would blow up the context window
 

00:06:47.880 --> 00:06:49.249 align:start position:0%
it would blow up the context window
super<00:06:48.479><c> quick</c>

00:06:49.249 --> 00:06:49.259 align:start position:0%
super quick
 

00:06:49.259 --> 00:06:53.210 align:start position:0%
super quick
and<00:06:49.979><c> so</c><00:06:50.280><c> this</c><00:06:50.940><c> doesn't</c><00:06:51.180><c> get</c><00:06:51.419><c> passed</c><00:06:51.900><c> the</c><00:06:52.440><c> the</c>

00:06:53.210 --> 00:06:53.220 align:start position:0%
and so this doesn't get passed the the
 

00:06:53.220 --> 00:06:56.330 align:start position:0%
and so this doesn't get passed the the
large<00:06:54.120><c> language</c><00:06:54.360><c> model</c><00:06:54.720><c> instead</c><00:06:55.440><c> we</c><00:06:56.039><c> get</c><00:06:56.220><c> the</c>

00:06:56.330 --> 00:06:56.340 align:start position:0%
large language model instead we get the
 

00:06:56.340 --> 00:06:58.430 align:start position:0%
large language model instead we get the
content<00:06:56.699><c> loaded</c><00:06:57.120><c> let's</c><00:06:57.419><c> start</c><00:06:57.720><c> into</c><00:06:57.900><c> a</c><00:06:58.139><c> vector</c>

00:06:58.430 --> 00:06:58.440 align:start position:0%
content loaded let's start into a vector
 

00:06:58.440 --> 00:07:02.150 align:start position:0%
content loaded let's start into a vector
store<00:06:58.620><c> and</c><00:06:59.160><c> now</c><00:06:59.340><c> the</c><00:06:59.539><c> data</c><00:07:00.539><c> agent</c><00:07:00.780><c> can</c><00:07:00.900><c> again</c>

00:07:02.150 --> 00:07:02.160 align:start position:0%
store and now the data agent can again
 

00:07:02.160 --> 00:07:04.909 align:start position:0%
store and now the data agent can again
read<00:07:02.819><c> that</c><00:07:03.360><c> data</c><00:07:03.900><c> that</c><00:07:04.319><c> we've</c><00:07:04.500><c> searched</c><00:07:04.800><c> for</c>

00:07:04.909 --> 00:07:04.919 align:start position:0%
read that data that we've searched for
 

00:07:04.919 --> 00:07:08.150 align:start position:0%
read that data that we've searched for
and<00:07:05.880><c> ask</c><00:07:06.120><c> it</c><00:07:06.300><c> who</c><00:07:06.479><c> won</c><00:07:06.660><c> the</c><00:07:06.780><c> NBA</c><00:07:07.080><c> Playoffs</c><00:07:07.500><c> we</c>

00:07:08.150 --> 00:07:08.160 align:start position:0%
and ask it who won the NBA Playoffs we
 

00:07:08.160 --> 00:07:09.770 align:start position:0%
and ask it who won the NBA Playoffs we
get<00:07:08.340><c> the</c><00:07:08.460><c> dead</c><00:07:08.639><c> in</c><00:07:08.819><c> renouncements</c><00:07:09.419><c> and</c><00:07:09.660><c> then</c>

00:07:09.770 --> 00:07:09.780 align:start position:0%
get the dead in renouncements and then
 

00:07:09.780 --> 00:07:11.090 align:start position:0%
get the dead in renouncements and then
this<00:07:09.960><c> is</c><00:07:10.080><c> our</c><00:07:10.259><c> kind</c><00:07:10.440><c> of</c><00:07:10.560><c> follow-up</c><00:07:10.860><c> question</c>

00:07:11.090 --> 00:07:11.100 align:start position:0%
this is our kind of follow-up question
 

00:07:11.100 --> 00:07:14.270 align:start position:0%
this is our kind of follow-up question
example<00:07:11.660><c> we</c><00:07:12.660><c> don't</c><00:07:12.840><c> have</c><00:07:13.080><c> to</c><00:07:13.319><c> load</c><00:07:13.800><c> that</c><00:07:13.919><c> data</c>

00:07:14.270 --> 00:07:14.280 align:start position:0%
example we don't have to load that data
 

00:07:14.280 --> 00:07:16.309 align:start position:0%
example we don't have to load that data
again<00:07:14.520><c> we</c><00:07:14.880><c> can</c><00:07:15.060><c> simply</c><00:07:15.300><c> read</c><00:07:15.539><c> it</c><00:07:15.720><c> and</c><00:07:16.080><c> we</c><00:07:16.199><c> can</c>

00:07:16.309 --> 00:07:16.319 align:start position:0%
again we can simply read it and we can
 

00:07:16.319 --> 00:07:17.930 align:start position:0%
again we can simply read it and we can
realize<00:07:16.919><c> that</c>

00:07:17.930 --> 00:07:17.940 align:start position:0%
realize that
 

00:07:17.940 --> 00:07:19.909 align:start position:0%
realize that
the<00:07:18.419><c> Denver</c><00:07:18.600><c> Nuggets</c><00:07:19.199><c> beat</c><00:07:19.380><c> the</c><00:07:19.620><c> Los</c><00:07:19.740><c> Angeles</c>

00:07:19.909 --> 00:07:19.919 align:start position:0%
the Denver Nuggets beat the Los Angeles
 

00:07:19.919 --> 00:07:23.390 align:start position:0%
the Denver Nuggets beat the Los Angeles
Lakers

00:07:23.390 --> 00:07:23.400 align:start position:0%
 
 

00:07:23.400 --> 00:07:25.430 align:start position:0%
 
so<00:07:24.000><c> again</c><00:07:24.360><c> just</c><00:07:24.780><c> looking</c><00:07:24.900><c> at</c><00:07:25.080><c> this</c><00:07:25.259><c> code</c>

00:07:25.430 --> 00:07:25.440 align:start position:0%
so again just looking at this code
 

00:07:25.440 --> 00:07:27.290 align:start position:0%
so again just looking at this code
example<00:07:25.860><c> super</c><00:07:26.280><c> straightforward</c><00:07:26.940><c> it's</c>

00:07:27.290 --> 00:07:27.300 align:start position:0%
example super straightforward it's
 

00:07:27.300 --> 00:07:29.270 align:start position:0%
example super straightforward it's
really<00:07:27.479><c> just</c><00:07:27.720><c> five</c><00:07:28.020><c> lines</c><00:07:28.319><c> of</c><00:07:28.500><c> code</c><00:07:28.620><c> again</c><00:07:28.860><c> and</c>

00:07:29.270 --> 00:07:29.280 align:start position:0%
really just five lines of code again and
 

00:07:29.280 --> 00:07:30.770 align:start position:0%
really just five lines of code again and
we're<00:07:29.460><c> ready</c><00:07:29.639><c> to</c><00:07:29.819><c> chat</c>

00:07:30.770 --> 00:07:30.780 align:start position:0%
we're ready to chat
 

00:07:30.780 --> 00:07:32.689 align:start position:0%
we're ready to chat
um load<00:07:31.259><c> all</c><00:07:31.380><c> sorts</c><00:07:31.680><c> of</c><00:07:31.800><c> like</c><00:07:31.919><c> recent</c><00:07:32.280><c> events</c>

00:07:32.689 --> 00:07:32.699 align:start position:0%
um load all sorts of like recent events
 

00:07:32.699 --> 00:07:35.990 align:start position:0%
um load all sorts of like recent events
and<00:07:33.060><c> current</c><00:07:33.240><c> news</c><00:07:33.479><c> from</c><00:07:33.720><c> Wikipedia</c>

00:07:35.990 --> 00:07:36.000 align:start position:0%
and current news from Wikipedia
 

00:07:36.000 --> 00:07:38.290 align:start position:0%
and current news from Wikipedia
so<00:07:36.479><c> both</c><00:07:36.780><c> of</c><00:07:37.020><c> these</c><00:07:37.139><c> examples</c>

00:07:38.290 --> 00:07:38.300 align:start position:0%
so both of these examples
 

00:07:38.300 --> 00:07:40.969 align:start position:0%
so both of these examples
go<00:07:39.300><c> a</c><00:07:39.479><c> long</c><00:07:39.660><c> way</c><00:07:39.900><c> in</c><00:07:40.380><c> extending</c><00:07:40.860><c> the</c>

00:07:40.969 --> 00:07:40.979 align:start position:0%
go a long way in extending the
 

00:07:40.979 --> 00:07:42.770 align:start position:0%
go a long way in extending the
capability<00:07:41.460><c> of</c><00:07:41.880><c> modern</c><00:07:42.180><c> large</c><00:07:42.539><c> language</c>

00:07:42.770 --> 00:07:42.780 align:start position:0%
capability of modern large language
 

00:07:42.780 --> 00:07:44.150 align:start position:0%
capability of modern large language
models

00:07:44.150 --> 00:07:44.160 align:start position:0%
models
 

00:07:44.160 --> 00:07:46.129 align:start position:0%
models
um<00:07:44.220><c> you</c><00:07:44.460><c> know</c><00:07:44.580><c> all</c><00:07:44.819><c> those</c><00:07:45.120><c> limitations</c><00:07:45.780><c> that</c>

00:07:46.129 --> 00:07:46.139 align:start position:0%
um you know all those limitations that
 

00:07:46.139 --> 00:07:47.890 align:start position:0%
um you know all those limitations that
we<00:07:46.199><c> expressed</c><00:07:46.560><c> at</c><00:07:46.740><c> the</c><00:07:46.919><c> start</c><00:07:47.039><c> we</c><00:07:47.460><c> have</c>

00:07:47.890 --> 00:07:47.900 align:start position:0%
we expressed at the start we have
 

00:07:47.900 --> 00:07:50.990 align:start position:0%
we expressed at the start we have
solutions<00:07:48.900><c> for</c><00:07:49.319><c> them</c><00:07:49.560><c> now</c>

00:07:50.990 --> 00:07:51.000 align:start position:0%
solutions for them now
 

00:07:51.000 --> 00:07:52.490 align:start position:0%
solutions for them now
um<00:07:51.060><c> and</c><00:07:51.360><c> the</c><00:07:51.479><c> nice</c><00:07:51.599><c> thing</c><00:07:51.840><c> is</c><00:07:52.020><c> with</c><00:07:52.199><c> llama</c>

00:07:52.490 --> 00:07:52.500 align:start position:0%
um and the nice thing is with llama
 

00:07:52.500 --> 00:07:54.770 align:start position:0%
um and the nice thing is with llama
index<00:07:52.860><c> and</c><00:07:53.039><c> llama</c><00:07:53.280><c> Hub</c><00:07:53.520><c> it's</c><00:07:53.940><c> it's</c><00:07:54.180><c> five</c><00:07:54.479><c> lines</c>

00:07:54.770 --> 00:07:54.780 align:start position:0%
index and llama Hub it's it's five lines
 

00:07:54.780 --> 00:07:55.870 align:start position:0%
index and llama Hub it's it's five lines
of<00:07:54.960><c> code</c>

00:07:55.870 --> 00:07:55.880 align:start position:0%
of code
 

00:07:55.880 --> 00:07:58.249 align:start position:0%
of code
agents<00:07:56.880><c> can</c><00:07:57.000><c> additionally</c><00:07:57.360><c> use</c><00:07:57.780><c> multiple</c>

00:07:58.249 --> 00:07:58.259 align:start position:0%
agents can additionally use multiple
 

00:07:58.259 --> 00:08:00.110 align:start position:0%
agents can additionally use multiple
tools<00:07:58.680><c> at</c><00:07:58.919><c> the</c><00:07:59.039><c> same</c><00:07:59.160><c> time</c><00:07:59.340><c> so</c><00:07:59.699><c> we</c><00:07:59.819><c> could</c><00:07:59.940><c> have</c>

00:08:00.110 --> 00:08:00.120 align:start position:0%
tools at the same time so we could have
 

00:08:00.120 --> 00:08:03.230 align:start position:0%
tools at the same time so we could have
passed<00:08:00.539><c> an</c><00:08:00.780><c> array</c><00:08:01.139><c> of</c><00:08:01.979><c> larger</c><00:08:02.580><c> tools</c><00:08:03.000><c> we</c><00:08:03.180><c> could</c>

00:08:03.230 --> 00:08:03.240 align:start position:0%
passed an array of larger tools we could
 

00:08:03.240 --> 00:08:05.210 align:start position:0%
passed an array of larger tools we could
have<00:08:03.360><c> given</c><00:08:03.539><c> both</c><00:08:03.780><c> Wolfram</c><00:08:04.319><c> Alpha</c><00:08:04.800><c> and</c><00:08:05.099><c> the</c>

00:08:05.210 --> 00:08:05.220 align:start position:0%
have given both Wolfram Alpha and the
 

00:08:05.220 --> 00:08:07.189 align:start position:0%
have given both Wolfram Alpha and the
Wikipedia<00:08:05.699><c> tools</c><00:08:06.060><c> to</c><00:08:06.180><c> an</c><00:08:06.360><c> agent</c>

00:08:07.189 --> 00:08:07.199 align:start position:0%
Wikipedia tools to an agent
 

00:08:07.199 --> 00:08:09.409 align:start position:0%
Wikipedia tools to an agent
and<00:08:07.620><c> the</c><00:08:07.740><c> agent</c><00:08:08.099><c> can</c><00:08:08.280><c> decide</c><00:08:08.639><c> when</c><00:08:09.120><c> to</c><00:08:09.300><c> use</c>

00:08:09.409 --> 00:08:09.419 align:start position:0%
and the agent can decide when to use
 

00:08:09.419 --> 00:08:12.170 align:start position:0%
and the agent can decide when to use
which<00:08:09.720><c> tool</c><00:08:10.139><c> so</c>

00:08:12.170 --> 00:08:12.180 align:start position:0%
which tool so
 

00:08:12.180 --> 00:08:14.510 align:start position:0%
which tool so
long<00:08:12.660><c> Hub</c><00:08:13.020><c> currently</c><00:08:13.259><c> offers</c><00:08:13.560><c> over</c><00:08:13.740><c> 30</c><00:08:14.039><c> tools</c>

00:08:14.510 --> 00:08:14.520 align:start position:0%
long Hub currently offers over 30 tools
 

00:08:14.520 --> 00:08:16.850 align:start position:0%
long Hub currently offers over 30 tools
and<00:08:14.759><c> there's</c><00:08:14.940><c> hundreds</c><00:08:15.419><c> of</c><00:08:15.539><c> data</c><00:08:16.500><c> readers</c>

00:08:16.850 --> 00:08:16.860 align:start position:0%
and there's hundreds of data readers
 

00:08:16.860 --> 00:08:18.890 align:start position:0%
and there's hundreds of data readers
that<00:08:17.220><c> are</c><00:08:17.400><c> completely</c><00:08:17.940><c> ready</c><00:08:18.180><c> for</c><00:08:18.419><c> your</c><00:08:18.599><c> agent</c>

00:08:18.890 --> 00:08:18.900 align:start position:0%
that are completely ready for your agent
 

00:08:18.900 --> 00:08:21.469 align:start position:0%
that are completely ready for your agent
to<00:08:19.020><c> use</c><00:08:19.199><c> just</c><00:08:19.560><c> like</c><00:08:19.680><c> I</c><00:08:19.800><c> showed</c><00:08:20.099><c> above</c>

00:08:21.469 --> 00:08:21.479 align:start position:0%
to use just like I showed above
 

00:08:21.479 --> 00:08:22.850 align:start position:0%
to use just like I showed above
um<00:08:21.539><c> they're</c><00:08:21.720><c> all</c><00:08:21.900><c> open</c><00:08:22.080><c> source</c><00:08:22.500><c> so</c><00:08:22.680><c> you</c><00:08:22.800><c> can</c>

00:08:22.850 --> 00:08:22.860 align:start position:0%
um they're all open source so you can
 

00:08:22.860 --> 00:08:24.529 align:start position:0%
um they're all open source so you can
look<00:08:23.039><c> at</c><00:08:23.160><c> the</c><00:08:23.340><c> code</c><00:08:23.520><c> you</c><00:08:23.879><c> can</c><00:08:23.940><c> develop</c><00:08:24.300><c> your</c>

00:08:24.529 --> 00:08:24.539 align:start position:0%
look at the code you can develop your
 

00:08:24.539 --> 00:08:27.409 align:start position:0%
look at the code you can develop your
own<00:08:24.660><c> create</c><00:08:25.500><c> custom</c><00:08:25.800><c> tools</c><00:08:26.400><c> and</c><00:08:26.879><c> contribute</c>

00:08:27.409 --> 00:08:27.419 align:start position:0%
own create custom tools and contribute
 

00:08:27.419 --> 00:08:29.990 align:start position:0%
own create custom tools and contribute
them<00:08:27.599><c> back</c><00:08:27.780><c> to</c><00:08:27.960><c> llama</c><00:08:28.259><c> hub</c>

00:08:29.990 --> 00:08:30.000 align:start position:0%
them back to llama hub
 

00:08:30.000 --> 00:08:32.510 align:start position:0%
them back to llama hub
next<00:08:30.720><c> steps</c><00:08:31.020><c> after</c><00:08:31.199><c> this</c><00:08:31.500><c> presentation</c><00:08:31.919><c> I</c>

00:08:32.510 --> 00:08:32.520 align:start position:0%
next steps after this presentation I
 

00:08:32.520 --> 00:08:34.430 align:start position:0%
next steps after this presentation I
encourage<00:08:32.820><c> you</c><00:08:33.060><c> to</c><00:08:33.539><c> build</c><00:08:33.899><c> your</c><00:08:34.140><c> own</c><00:08:34.260><c> custom</c>

00:08:34.430 --> 00:08:34.440 align:start position:0%
encourage you to build your own custom
 

00:08:34.440 --> 00:08:37.070 align:start position:0%
encourage you to build your own custom
data<00:08:34.919><c> agents</c><00:08:35.219><c> using</c><00:08:35.520><c> llama</c><00:08:35.820><c> Hub</c><00:08:36.000><c> tools</c><00:08:36.419><c> you</c>

00:08:37.070 --> 00:08:37.080 align:start position:0%
data agents using llama Hub tools you
 

00:08:37.080 --> 00:08:39.050 align:start position:0%
data agents using llama Hub tools you
can<00:08:37.140><c> build</c><00:08:37.320><c> your</c><00:08:37.560><c> own</c><00:08:37.680><c> tool</c><00:08:38.099><c> and</c><00:08:38.459><c> open</c><00:08:38.640><c> source</c>

00:08:39.050 --> 00:08:39.060 align:start position:0%
can build your own tool and open source
 

00:08:39.060 --> 00:08:40.790 align:start position:0%
can build your own tool and open source
it<00:08:39.180><c> on</c><00:08:39.419><c> lava</c><00:08:39.779><c> Hub</c><00:08:39.959><c> I</c><00:08:40.260><c> love</c><00:08:40.440><c> to</c><00:08:40.560><c> see</c><00:08:40.680><c> your</c>

00:08:40.790 --> 00:08:40.800 align:start position:0%
it on lava Hub I love to see your
 

00:08:40.800 --> 00:08:43.550 align:start position:0%
it on lava Hub I love to see your
contributions<00:08:41.339><c> and</c><00:08:42.240><c> finally</c><00:08:42.719><c> there's</c><00:08:43.380><c> the</c>

00:08:43.550 --> 00:08:43.560 align:start position:0%
contributions and finally there's the
 

00:08:43.560 --> 00:08:45.889 align:start position:0%
contributions and finally there's the
Llama<00:08:43.919><c> Hub</c><00:08:44.099><c> notebooks</c><00:08:44.580><c> folder</c><00:08:44.880><c> that</c><00:08:45.300><c> has</c><00:08:45.600><c> a</c>

00:08:45.889 --> 00:08:45.899 align:start position:0%
Llama Hub notebooks folder that has a
 

00:08:45.899 --> 00:08:47.690 align:start position:0%
Llama Hub notebooks folder that has a
ton<00:08:46.020><c> more</c><00:08:46.260><c> examples</c><00:08:46.620><c> like</c><00:08:46.980><c> this</c><00:08:47.160><c> presentation</c>

00:08:47.690 --> 00:08:47.700 align:start position:0%
ton more examples like this presentation
 

00:08:47.700 --> 00:08:49.250 align:start position:0%
ton more examples like this presentation
that

00:08:49.250 --> 00:08:49.260 align:start position:0%
that
 

00:08:49.260 --> 00:08:52.130 align:start position:0%
that
serve<00:08:49.980><c> to</c><00:08:50.160><c> remedy</c><00:08:51.060><c> all</c><00:08:51.360><c> sorts</c><00:08:51.660><c> of</c><00:08:51.720><c> problems</c>

00:08:52.130 --> 00:08:52.140 align:start position:0%
serve to remedy all sorts of problems
 

00:08:52.140 --> 00:08:54.650 align:start position:0%
serve to remedy all sorts of problems
with<00:08:52.380><c> large</c><00:08:52.560><c> language</c><00:08:52.860><c> models</c><00:08:53.399><c> and</c><00:08:54.120><c> really</c>

00:08:54.650 --> 00:08:54.660 align:start position:0%
with large language models and really
 

00:08:54.660 --> 00:08:57.050 align:start position:0%
with large language models and really
bypass<00:08:55.200><c> current</c><00:08:55.860><c> limitations</c>

00:08:57.050 --> 00:08:57.060 align:start position:0%
bypass current limitations
 

00:08:57.060 --> 00:08:59.959 align:start position:0%
bypass current limitations
thanks<00:08:57.540><c> for</c><00:08:57.660><c> listening</c>

