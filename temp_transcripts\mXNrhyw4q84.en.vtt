WEBVTT
Kind: captions
Language: en

00:00:00.399 --> 00:00:02.310 align:start position:0%
 
what<00:00:00.560><c> makes</c><00:00:00.799><c> the</c><00:00:00.919><c> internet</c><00:00:01.439><c> so</c><00:00:01.719><c> addictive</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
what makes the internet so addictive
 

00:00:02.320 --> 00:00:04.430 align:start position:0%
what makes the internet so addictive
lately<00:00:03.320><c> have</c><00:00:03.439><c> you</c><00:00:03.560><c> ever</c><00:00:03.760><c> wondered</c><00:00:04.240><c> how</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
lately have you ever wondered how
 

00:00:04.440 --> 00:00:06.749 align:start position:0%
lately have you ever wondered how
platforms<00:00:04.960><c> like</c><00:00:05.120><c> YouTube</c><00:00:05.520><c> or</c><00:00:05.720><c> Tik</c><00:00:05.960><c> Tok</c><00:00:06.279><c> knows</c>

00:00:06.749 --> 00:00:06.759 align:start position:0%
platforms like YouTube or Tik Tok knows
 

00:00:06.759 --> 00:00:08.589 align:start position:0%
platforms like YouTube or Tik Tok knows
exactly<00:00:07.120><c> what</c><00:00:07.279><c> videos</c><00:00:07.600><c> to</c><00:00:07.720><c> show</c><00:00:08.000><c> you</c><00:00:08.200><c> so</c><00:00:08.440><c> that</c>

00:00:08.589 --> 00:00:08.599 align:start position:0%
exactly what videos to show you so that
 

00:00:08.599 --> 00:00:11.990 align:start position:0%
exactly what videos to show you so that
you<00:00:08.719><c> will</c><00:00:08.920><c> be</c><00:00:09.200><c> most</c><00:00:09.480><c> likely</c><00:00:09.840><c> to</c><00:00:10.320><c> watch</c><00:00:11.320><c> or</c><00:00:11.599><c> how</c>

00:00:11.990 --> 00:00:12.000 align:start position:0%
you will be most likely to watch or how
 

00:00:12.000 --> 00:00:16.150 align:start position:0%
you will be most likely to watch or how
online<00:00:12.480><c> stores</c><00:00:13.200><c> or</c><00:00:13.599><c> ads</c><00:00:14.440><c> knows</c><00:00:15.000><c> exactly</c><00:00:15.759><c> what</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
online stores or ads knows exactly what
 

00:00:16.160 --> 00:00:17.910 align:start position:0%
online stores or ads knows exactly what
products<00:00:16.520><c> to</c><00:00:16.680><c> show</c><00:00:16.920><c> you</c><00:00:17.080><c> so</c><00:00:17.279><c> that</c><00:00:17.439><c> you</c><00:00:17.520><c> will</c><00:00:17.680><c> be</c>

00:00:17.910 --> 00:00:17.920 align:start position:0%
products to show you so that you will be
 

00:00:17.920 --> 00:00:21.029 align:start position:0%
products to show you so that you will be
most<00:00:18.160><c> likely</c><00:00:18.520><c> to</c><00:00:19.199><c> buy</c><00:00:20.199><c> all</c><00:00:20.439><c> this</c><00:00:20.560><c> is</c><00:00:20.680><c> due</c><00:00:20.880><c> to</c>

00:00:21.029 --> 00:00:21.039 align:start position:0%
most likely to buy all this is due to
 

00:00:21.039 --> 00:00:22.950 align:start position:0%
most likely to buy all this is due to
something<00:00:21.320><c> called</c><00:00:21.600><c> Vector</c><00:00:21.920><c> search</c><00:00:22.640><c> Imagine</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
something called Vector search Imagine
 

00:00:22.960 --> 00:00:24.870 align:start position:0%
something called Vector search Imagine
you<00:00:23.199><c> just</c><00:00:23.359><c> watched</c><00:00:23.640><c> the</c><00:00:23.720><c> video</c><00:00:24.039><c> about</c><00:00:24.400><c> rocket</c>

00:00:24.870 --> 00:00:24.880 align:start position:0%
you just watched the video about rocket
 

00:00:24.880 --> 00:00:27.150 align:start position:0%
you just watched the video about rocket
science<00:00:25.880><c> a</c><00:00:26.000><c> simple</c><00:00:26.279><c> keyword-based</c><00:00:26.720><c> search</c>

00:00:27.150 --> 00:00:27.160 align:start position:0%
science a simple keyword-based search
 

00:00:27.160 --> 00:00:28.750 align:start position:0%
science a simple keyword-based search
would<00:00:27.359><c> only</c><00:00:27.519><c> be</c><00:00:27.679><c> able</c><00:00:27.880><c> to</c><00:00:28.080><c> recommend</c><00:00:28.519><c> you</c>

00:00:28.750 --> 00:00:28.760 align:start position:0%
would only be able to recommend you
 

00:00:28.760 --> 00:00:31.390 align:start position:0%
would only be able to recommend you
other<00:00:29.080><c> videos</c><00:00:29.480><c> with</c><00:00:29.599><c> a</c><00:00:29.720><c> word</c><00:00:30.000><c> words</c><00:00:30.519><c> rocket</c><00:00:31.000><c> or</c>

00:00:31.390 --> 00:00:31.400 align:start position:0%
other videos with a word words rocket or
 

00:00:31.400 --> 00:00:34.910 align:start position:0%
other videos with a word words rocket or
science<00:00:31.960><c> in</c><00:00:32.119><c> it</c><00:00:32.880><c> but</c><00:00:33.280><c> a</c><00:00:33.520><c> vector</c><00:00:33.840><c> search</c><00:00:34.320><c> engine</c>

00:00:34.910 --> 00:00:34.920 align:start position:0%
science in it but a vector search engine
 

00:00:34.920 --> 00:00:37.150 align:start position:0%
science in it but a vector search engine
works<00:00:35.200><c> in</c><00:00:35.320><c> a</c><00:00:35.520><c> much</c><00:00:35.680><c> more</c><00:00:35.960><c> sophisticated</c><00:00:36.760><c> way</c>

00:00:37.150 --> 00:00:37.160 align:start position:0%
works in a much more sophisticated way
 

00:00:37.160 --> 00:00:39.430 align:start position:0%
works in a much more sophisticated way
it<00:00:37.280><c> can</c><00:00:37.520><c> analyze</c><00:00:38.079><c> the</c><00:00:38.280><c> actual</c><00:00:38.719><c> context</c><00:00:39.200><c> and</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
it can analyze the actual context and
 

00:00:39.440 --> 00:00:42.709 align:start position:0%
it can analyze the actual context and
meaning<00:00:40.440><c> of</c><00:00:41.000><c> texts</c><00:00:41.559><c> like</c><00:00:41.800><c> the</c><00:00:42.000><c> title</c><00:00:42.559><c> the</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
meaning of texts like the title the
 

00:00:42.719 --> 00:00:44.470 align:start position:0%
meaning of texts like the title the
transcript<00:00:43.360><c> or</c><00:00:43.520><c> the</c><00:00:43.719><c> description</c><00:00:44.160><c> of</c><00:00:44.280><c> the</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
transcript or the description of the
 

00:00:44.480 --> 00:00:46.790 align:start position:0%
transcript or the description of the
video<00:00:45.200><c> as</c><00:00:45.320><c> well</c><00:00:45.480><c> as</c><00:00:45.640><c> user</c><00:00:46.000><c> interactions</c><00:00:46.640><c> such</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
video as well as user interactions such
 

00:00:46.800 --> 00:00:49.590 align:start position:0%
video as well as user interactions such
as<00:00:46.960><c> likes</c><00:00:47.360><c> views</c><00:00:47.719><c> or</c><00:00:48.000><c> watch</c><00:00:48.320><c> time</c><00:00:49.000><c> and</c><00:00:49.199><c> search</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
as likes views or watch time and search
 

00:00:49.600 --> 00:00:52.790 align:start position:0%
as likes views or watch time and search
them<00:00:50.079><c> using</c><00:00:50.640><c> High</c><00:00:50.879><c> dimensional</c><00:00:51.520><c> vectors</c><00:00:52.520><c> so</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
them using High dimensional vectors so
 

00:00:52.800 --> 00:00:54.830 align:start position:0%
them using High dimensional vectors so
the<00:00:52.920><c> YouTube</c><00:00:53.320><c> algorithm</c><00:00:53.960><c> takes</c><00:00:54.199><c> the</c><00:00:54.399><c> vectors</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
the YouTube algorithm takes the vectors
 

00:00:54.840 --> 00:00:56.950 align:start position:0%
the YouTube algorithm takes the vectors
from<00:00:55.000><c> this</c><00:00:55.199><c> rocket</c><00:00:55.559><c> science</c><00:00:56.000><c> video</c><00:00:56.760><c> and</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
from this rocket science video and
 

00:00:56.960 --> 00:00:58.910 align:start position:0%
from this rocket science video and
Compares<00:00:57.359><c> them</c><00:00:57.559><c> against</c><00:00:57.920><c> millions</c><00:00:58.440><c> of</c><00:00:58.680><c> other</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
Compares them against millions of other
 

00:00:58.920 --> 00:01:01.590 align:start position:0%
Compares them against millions of other
vectors<00:00:59.399><c> from</c><00:00:59.559><c> other</c><00:01:00.039><c> videos</c><00:01:00.640><c> in</c><00:01:00.960><c> a</c><00:01:01.160><c> fraction</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
vectors from other videos in a fraction
 

00:01:01.600 --> 00:01:02.990 align:start position:0%
vectors from other videos in a fraction
of<00:01:01.760><c> a</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
of a
 

00:01:03.000 --> 00:01:05.030 align:start position:0%
of a
second<00:01:04.000><c> the</c><00:01:04.119><c> ones</c><00:01:04.360><c> with</c><00:01:04.519><c> the</c><00:01:04.640><c> closest</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
second the ones with the closest
 

00:01:05.040 --> 00:01:07.350 align:start position:0%
second the ones with the closest
matching<00:01:05.479><c> vectors</c><00:01:06.320><c> means</c><00:01:06.680><c> that</c><00:01:06.840><c> they</c><00:01:07.000><c> cover</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
matching vectors means that they cover
 

00:01:07.360 --> 00:01:09.710 align:start position:0%
matching vectors means that they cover
similar<00:01:07.799><c> topics</c><00:01:08.400><c> but</c><00:01:08.600><c> potentially</c><00:01:09.320><c> from</c>

00:01:09.710 --> 00:01:09.720 align:start position:0%
similar topics but potentially from
 

00:01:09.720 --> 00:01:11.070 align:start position:0%
similar topics but potentially from
different

00:01:11.070 --> 00:01:11.080 align:start position:0%
different
 

00:01:11.080 --> 00:01:14.510 align:start position:0%
different
angles<00:01:12.080><c> so</c><00:01:12.520><c> they</c><00:01:12.680><c> get</c><00:01:12.920><c> resurfaced</c><00:01:13.840><c> as</c><00:01:14.119><c> a</c>

00:01:14.510 --> 00:01:14.520 align:start position:0%
angles so they get resurfaced as a
 

00:01:14.520 --> 00:01:16.469 align:start position:0%
angles so they get resurfaced as a
recommendation<00:01:15.200><c> for</c><00:01:15.439><c> you</c><00:01:16.200><c> as</c><00:01:16.320><c> you're</c>

00:01:16.469 --> 00:01:16.479 align:start position:0%
recommendation for you as you're
 

00:01:16.479 --> 00:01:18.710 align:start position:0%
recommendation for you as you're
scrolling<00:01:17.000><c> through</c><00:01:17.200><c> your</c><00:01:17.400><c> timeline</c><00:01:18.240><c> quadrant</c>

00:01:18.710 --> 00:01:18.720 align:start position:0%
scrolling through your timeline quadrant
 

00:01:18.720 --> 00:01:20.789 align:start position:0%
scrolling through your timeline quadrant
is<00:01:18.799><c> a</c><00:01:18.960><c> vector</c><00:01:19.240><c> search</c><00:01:19.640><c> engine</c><00:01:20.159><c> that</c><00:01:20.320><c> uses</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
is a vector search engine that uses
 

00:01:20.799 --> 00:01:24.270 align:start position:0%
is a vector search engine that uses
advanced<00:01:21.439><c> search</c><00:01:22.079><c> algorithms</c><00:01:23.079><c> to</c><00:01:23.320><c> go</c><00:01:23.560><c> through</c>

00:01:24.270 --> 00:01:24.280 align:start position:0%
advanced search algorithms to go through
 

00:01:24.280 --> 00:01:26.590 align:start position:0%
advanced search algorithms to go through
high<00:01:24.520><c> dimensional</c><00:01:25.079><c> Vector</c><00:01:25.600><c> representations</c>

00:01:26.590 --> 00:01:26.600 align:start position:0%
high dimensional Vector representations
 

00:01:26.600 --> 00:01:30.109 align:start position:0%
high dimensional Vector representations
of<00:01:26.960><c> any</c><00:01:27.240><c> type</c><00:01:27.439><c> of</c><00:01:27.680><c> data</c><00:01:28.680><c> but</c><00:01:29.280><c> what</c><00:01:29.400><c> is</c><00:01:29.560><c> exactly</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
of any type of data but what is exactly
 

00:01:30.119 --> 00:01:31.350 align:start position:0%
of any type of data but what is exactly
happening<00:01:30.479><c> behind</c><00:01:30.720><c> the</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
happening behind the
 

00:01:31.360 --> 00:01:33.870 align:start position:0%
happening behind the
scenes<00:01:32.360><c> each</c><00:01:32.600><c> piece</c><00:01:32.759><c> of</c><00:01:32.960><c> data</c><00:01:33.200><c> inserted</c><00:01:33.600><c> into</c>

00:01:33.870 --> 00:01:33.880 align:start position:0%
scenes each piece of data inserted into
 

00:01:33.880 --> 00:01:37.270 align:start position:0%
scenes each piece of data inserted into
quadrant<00:01:34.360><c> is</c><00:01:34.520><c> represented</c><00:01:35.240><c> as</c><00:01:35.399><c> a</c><00:01:35.759><c> vector</c><00:01:36.759><c> say</c>

00:01:37.270 --> 00:01:37.280 align:start position:0%
quadrant is represented as a vector say
 

00:01:37.280 --> 00:01:38.310 align:start position:0%
quadrant is represented as a vector say
a

00:01:38.310 --> 00:01:38.320 align:start position:0%
a
 

00:01:38.320 --> 00:01:41.510 align:start position:0%
a
1,536<00:01:39.320><c> dimensional</c><00:01:39.920><c> Vector</c><00:01:40.320><c> collection</c><00:01:41.320><c> of</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
1,536 dimensional Vector collection of
 

00:01:41.520 --> 00:01:45.630 align:start position:0%
1,536 dimensional Vector collection of
numbers<00:01:41.960><c> like</c><00:01:42.799><c> 1</c><00:01:43.840><c> 4</c>

00:01:45.630 --> 00:01:45.640 align:start position:0%
numbers like 1 4
 

00:01:45.640 --> 00:01:51.190 align:start position:0%
numbers like 1 4
3.2<00:01:46.640><c> minus</c><00:01:47.200><c> 0.8</c><00:01:48.200><c> etc</c><00:01:48.719><c> etc</c><00:01:49.719><c> your</c><00:01:50.000><c> query</c><00:01:50.719><c> is</c><00:01:50.920><c> also</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
3.2 minus 0.8 etc etc your query is also
 

00:01:51.200 --> 00:01:53.990 align:start position:0%
3.2 minus 0.8 etc etc your query is also
converted<00:01:51.759><c> into</c><00:01:52.159><c> a</c><00:01:52.479><c> vector</c><00:01:53.000><c> representation</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
converted into a vector representation
 

00:01:54.000 --> 00:01:56.550 align:start position:0%
converted into a vector representation
quadrant<00:01:54.479><c> then</c><00:01:54.759><c> calculates</c><00:01:55.360><c> how</c><00:01:55.600><c> similar</c><00:01:56.360><c> the</c>

00:01:56.550 --> 00:01:56.560 align:start position:0%
quadrant then calculates how similar the
 

00:01:56.560 --> 00:02:00.109 align:start position:0%
quadrant then calculates how similar the
query<00:01:57.000><c> Vector</c><00:01:57.479><c> is</c><00:01:57.680><c> to</c><00:01:58.159><c> every</c><00:01:58.479><c> data</c><00:01:58.799><c> Vector</c><00:01:59.520><c> so</c>

00:02:00.109 --> 00:02:00.119 align:start position:0%
query Vector is to every data Vector so
 

00:02:00.119 --> 00:02:01.990 align:start position:0%
query Vector is to every data Vector so
in<00:02:00.240><c> the</c><00:02:00.360><c> closest</c><00:02:00.719><c> matches</c><00:02:01.159><c> in</c><00:02:01.360><c> the</c><00:02:01.600><c> entire</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
in the closest matches in the entire
 

00:02:02.000 --> 00:02:04.190 align:start position:0%
in the closest matches in the entire
data<00:02:02.320><c> set</c><00:02:03.039><c> now</c><00:02:03.240><c> imagine</c><00:02:03.560><c> this</c><00:02:03.719><c> playing</c><00:02:04.000><c> out</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
data set now imagine this playing out
 

00:02:04.200 --> 00:02:06.149 align:start position:0%
data set now imagine this playing out
across<00:02:04.560><c> millions</c><00:02:04.960><c> or</c><00:02:05.119><c> even</c><00:02:05.399><c> billions</c><00:02:05.759><c> of</c><00:02:05.920><c> data</c>

00:02:06.149 --> 00:02:06.159 align:start position:0%
across millions or even billions of data
 

00:02:06.159 --> 00:02:08.790 align:start position:0%
across millions or even billions of data
points<00:02:06.479><c> globally</c><00:02:07.440><c> distributed</c><00:02:08.440><c> that</c><00:02:08.560><c> is</c><00:02:08.679><c> the</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
points globally distributed that is the
 

00:02:08.800 --> 00:02:11.110 align:start position:0%
points globally distributed that is the
crazy<00:02:09.239><c> scale</c><00:02:09.560><c> that</c><00:02:09.679><c> quadrant</c><00:02:10.200><c> operates</c><00:02:10.679><c> at</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
crazy scale that quadrant operates at
 

00:02:11.120 --> 00:02:13.070 align:start position:0%
crazy scale that quadrant operates at
and<00:02:11.280><c> yet</c><00:02:11.480><c> it</c><00:02:11.599><c> manages</c><00:02:11.959><c> to</c><00:02:12.160><c> do</c><00:02:12.360><c> so</c><00:02:12.840><c> with</c>

00:02:13.070 --> 00:02:13.080 align:start position:0%
and yet it manages to do so with
 

00:02:13.080 --> 00:02:15.550 align:start position:0%
and yet it manages to do so with
Incredible<00:02:13.599><c> efficiency</c><00:02:14.599><c> and</c><00:02:14.800><c> using</c><00:02:15.120><c> minimal</c>

00:02:15.550 --> 00:02:15.560 align:start position:0%
Incredible efficiency and using minimal
 

00:02:15.560 --> 00:02:17.990 align:start position:0%
Incredible efficiency and using minimal
Computer<00:02:16.280><c> Resources</c><00:02:17.280><c> quadrant</c><00:02:17.680><c> is</c><00:02:17.800><c> open</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
Computer Resources quadrant is open
 

00:02:18.000 --> 00:02:19.790 align:start position:0%
Computer Resources quadrant is open
source<00:02:18.239><c> and</c><00:02:18.440><c> free</c><00:02:18.760><c> and</c><00:02:18.920><c> can</c><00:02:19.080><c> be</c><00:02:19.280><c> readily</c>

00:02:19.790 --> 00:02:19.800 align:start position:0%
source and free and can be readily
 

00:02:19.800 --> 00:02:21.990 align:start position:0%
source and free and can be readily
modified<00:02:20.319><c> and</c><00:02:20.480><c> deployed</c><00:02:21.080><c> in</c><00:02:21.280><c> any</c><00:02:21.560><c> environment</c>

00:02:21.990 --> 00:02:22.000 align:start position:0%
modified and deployed in any environment
 

00:02:22.000 --> 00:02:24.589 align:start position:0%
modified and deployed in any environment
of<00:02:22.120><c> your</c><00:02:22.319><c> choice</c><00:02:23.280><c> from</c><00:02:23.560><c> personal</c><00:02:24.080><c> projects</c><00:02:24.440><c> on</c>

00:02:24.589 --> 00:02:24.599 align:start position:0%
of your choice from personal projects on
 

00:02:24.599 --> 00:02:27.910 align:start position:0%
of your choice from personal projects on
a<00:02:24.760><c> Raspberry</c><00:02:25.319><c> Pi</c><00:02:26.120><c> to</c><00:02:26.599><c> powering</c><00:02:27.040><c> search</c><00:02:27.560><c> on</c>

00:02:27.910 --> 00:02:27.920 align:start position:0%
a Raspberry Pi to powering search on
 

00:02:27.920 --> 00:02:30.470 align:start position:0%
a Raspberry Pi to powering search on
major<00:02:28.840><c> corporations</c><00:02:29.920><c> even</c><00:02:30.080><c> the</c><00:02:30.160><c> biggest</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
major corporations even the biggest
 

00:02:30.480 --> 00:02:32.710 align:start position:0%
major corporations even the biggest
global<00:02:30.840><c> companies</c><00:02:31.519><c> can</c><00:02:31.680><c> use</c><00:02:31.959><c> quadrant</c><00:02:32.480><c> at</c><00:02:32.599><c> the</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
global companies can use quadrant at the
 

00:02:32.720 --> 00:02:35.390 align:start position:0%
global companies can use quadrant at the
same<00:02:32.959><c> level</c><00:02:33.200><c> of</c><00:02:33.400><c> complexity</c><00:02:34.360><c> as</c><00:02:34.480><c> a</c><00:02:34.680><c> hobbist</c>

00:02:35.390 --> 00:02:35.400 align:start position:0%
same level of complexity as a hobbist
 

00:02:35.400 --> 00:02:37.350 align:start position:0%
same level of complexity as a hobbist
from<00:02:35.599><c> their</c><00:02:35.800><c> home</c><00:02:36.040><c> office</c><00:02:36.959><c> combining</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
from their home office combining
 

00:02:37.360 --> 00:02:39.110 align:start position:0%
from their home office combining
quadrant<00:02:37.720><c> with</c><00:02:37.920><c> large</c><00:02:38.160><c> language</c><00:02:38.519><c> models</c><00:02:38.920><c> can</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
quadrant with large language models can
 

00:02:39.120 --> 00:02:41.430 align:start position:0%
quadrant with large language models can
give<00:02:39.239><c> you</c><00:02:39.440><c> more</c><00:02:39.800><c> accurate</c><00:02:40.280><c> and</c><00:02:40.480><c> in-depth</c>

00:02:41.430 --> 00:02:41.440 align:start position:0%
give you more accurate and in-depth
 

00:02:41.440 --> 00:02:43.869 align:start position:0%
give you more accurate and in-depth
results<00:02:42.159><c> than</c><00:02:42.360><c> just</c><00:02:42.560><c> using</c><00:02:42.920><c> the</c><00:02:43.040><c> llm</c><00:02:43.640><c> by</c>

00:02:43.869 --> 00:02:43.879 align:start position:0%
results than just using the llm by
 

00:02:43.879 --> 00:02:47.229 align:start position:0%
results than just using the llm by
itself<00:02:44.840><c> this</c><00:02:45.040><c> create</c><00:02:45.440><c> powerful</c><00:02:45.959><c> systems</c><00:02:46.959><c> and</c>

00:02:47.229 --> 00:02:47.239 align:start position:0%
itself this create powerful systems and
 

00:02:47.239 --> 00:02:48.949 align:start position:0%
itself this create powerful systems and
we<00:02:47.400><c> call</c><00:02:47.599><c> it</c><00:02:47.840><c> retrieval</c><00:02:48.400><c> augmented</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
we call it retrieval augmented
 

00:02:48.959 --> 00:02:52.430 align:start position:0%
we call it retrieval augmented
generation<00:02:49.840><c> or</c><00:02:50.440><c> rag</c><00:02:51.400><c> the</c><00:02:51.519><c> retrieval</c><00:02:52.080><c> process</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
generation or rag the retrieval process
 

00:02:52.440 --> 00:02:54.270 align:start position:0%
generation or rag the retrieval process
boost<00:02:52.800><c> the</c><00:02:52.920><c> performance</c><00:02:53.360><c> of</c><00:02:53.519><c> AI</c><00:02:53.760><c> models</c><00:02:54.120><c> like</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
boost the performance of AI models like
 

00:02:54.280 --> 00:02:56.190 align:start position:0%
boost the performance of AI models like
chatbots<00:02:54.959><c> ensuring</c><00:02:55.360><c> that</c><00:02:55.560><c> generate</c><00:02:56.000><c> high</c>

00:02:56.190 --> 00:02:56.200 align:start position:0%
chatbots ensuring that generate high
 

00:02:56.200 --> 00:02:58.830 align:start position:0%
chatbots ensuring that generate high
quality<00:02:56.640><c> outputs</c><00:02:57.480><c> while</c><00:02:57.760><c> minimizing</c><00:02:58.599><c> the</c>

00:02:58.830 --> 00:02:58.840 align:start position:0%
quality outputs while minimizing the
 

00:02:58.840 --> 00:03:00.949 align:start position:0%
quality outputs while minimizing the
resource<00:02:59.400><c> usage</c><00:03:00.040><c> and</c><00:03:00.200><c> significantly</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
resource usage and significantly
 

00:03:00.959 --> 00:03:03.910 align:start position:0%
resource usage and significantly
reducing<00:03:01.599><c> the</c><00:03:01.760><c> operational</c><00:03:02.519><c> cost</c><00:03:03.519><c> there's</c><00:03:03.760><c> a</c>

00:03:03.910 --> 00:03:03.920 align:start position:0%
reducing the operational cost there's a
 

00:03:03.920 --> 00:03:06.149 align:start position:0%
reducing the operational cost there's a
lot<00:03:04.080><c> more</c><00:03:04.280><c> I</c><00:03:04.400><c> can</c><00:03:04.599><c> go</c><00:03:04.760><c> into</c><00:03:05.040><c> this</c><00:03:05.319><c> topic</c><00:03:05.799><c> so</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
lot more I can go into this topic so
 

00:03:06.159 --> 00:03:08.030 align:start position:0%
lot more I can go into this topic so
please<00:03:06.519><c> if</c><00:03:06.599><c> you</c><00:03:06.720><c> want</c><00:03:06.879><c> to</c><00:03:07.120><c> keep</c><00:03:07.360><c> going</c><00:03:07.840><c> check</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
please if you want to keep going check
 

00:03:08.040 --> 00:03:09.710 align:start position:0%
please if you want to keep going check
out<00:03:08.200><c> some</c><00:03:08.360><c> of</c><00:03:08.519><c> the</c><00:03:08.680><c> other</c><00:03:09.040><c> videos</c><00:03:09.319><c> in</c><00:03:09.519><c> this</c>

00:03:09.710 --> 00:03:09.720 align:start position:0%
out some of the other videos in this
 

00:03:09.720 --> 00:03:11.789 align:start position:0%
out some of the other videos in this
channel<00:03:10.480><c> like</c><00:03:10.879><c> Advanced</c><00:03:11.280><c> retrieval</c>

00:03:11.789 --> 00:03:11.799 align:start position:0%
channel like Advanced retrieval
 

00:03:11.799 --> 00:03:15.350 align:start position:0%
channel like Advanced retrieval
strategies<00:03:12.599><c> or</c><00:03:12.840><c> centic</c><00:03:13.680><c> cach</c><00:03:14.680><c> also</c><00:03:15.000><c> we</c><00:03:15.120><c> have</c><00:03:15.239><c> a</c>

00:03:15.350 --> 00:03:15.360 align:start position:0%
strategies or centic cach also we have a
 

00:03:15.360 --> 00:03:17.030 align:start position:0%
strategies or centic cach also we have a
growing<00:03:15.680><c> Discord</c><00:03:16.080><c> community</c><00:03:16.519><c> of</c><00:03:16.720><c> vector</c>

00:03:17.030 --> 00:03:17.040 align:start position:0%
growing Discord community of vector
 

00:03:17.040 --> 00:03:19.030 align:start position:0%
growing Discord community of vector
search<00:03:17.480><c> experts</c><00:03:18.000><c> so</c><00:03:18.120><c> if</c><00:03:18.200><c> you'd</c><00:03:18.400><c> like</c><00:03:18.519><c> to</c><00:03:18.680><c> join</c>

00:03:19.030 --> 00:03:19.040 align:start position:0%
search experts so if you'd like to join
 

00:03:19.040 --> 00:03:21.589 align:start position:0%
search experts so if you'd like to join
that<00:03:19.360><c> share</c><00:03:19.760><c> ideas</c><00:03:20.360><c> projects</c><00:03:21.000><c> and</c><00:03:21.159><c> contribute</c>

00:03:21.589 --> 00:03:21.599 align:start position:0%
that share ideas projects and contribute
 

00:03:21.599 --> 00:03:23.509 align:start position:0%
that share ideas projects and contribute
to<00:03:21.720><c> the</c><00:03:21.840><c> Future</c><00:03:22.280><c> Vector</c><00:03:22.599><c> search</c><00:03:22.920><c> with</c><00:03:23.080><c> us</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
to the Future Vector search with us
 

00:03:23.519 --> 00:03:27.480 align:start position:0%
to the Future Vector search with us
check<00:03:23.799><c> out</c><00:03:23.959><c> the</c><00:03:24.040><c> link</c><00:03:24.239><c> in</c><00:03:24.319><c> the</c><00:03:24.480><c> description</c>

