WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.790 align:start position:0%
 
This<00:00:00.320><c> is</c><00:00:00.480><c> the</c><00:00:00.640><c> perfect</c><00:00:00.960><c> example</c><00:00:01.280><c> of</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
This is the perfect example of
 

00:00:01.800 --> 00:00:03.669 align:start position:0%
This is the perfect example of
one-sizefits-all.<00:00:02.800><c> There</c><00:00:02.960><c> is</c><00:00:03.120><c> no</c><00:00:03.360><c> perfect</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
one-sizefits-all. There is no perfect
 

00:00:03.679 --> 00:00:05.190 align:start position:0%
one-sizefits-all. There is no perfect
answer<00:00:03.919><c> that</c><00:00:04.080><c> applies</c><00:00:04.400><c> to</c><00:00:04.560><c> everybody,</c><00:00:04.960><c> right?</c>

00:00:05.190 --> 00:00:05.200 align:start position:0%
answer that applies to everybody, right?
 

00:00:05.200 --> 00:00:07.190 align:start position:0%
answer that applies to everybody, right?
Because<00:00:05.839><c> we'd</c><00:00:06.240><c> start</c><00:00:06.400><c> from</c><00:00:06.640><c> the</c><00:00:06.799><c> other</c><00:00:06.879><c> end</c><00:00:07.040><c> of</c>

00:00:07.190 --> 00:00:07.200 align:start position:0%
Because we'd start from the other end of
 

00:00:07.200 --> 00:00:09.270 align:start position:0%
Because we'd start from the other end of
that<00:00:07.440><c> which</c><00:00:07.680><c> is</c><00:00:08.080><c> innovation</c><00:00:08.559><c> and</c><00:00:08.720><c> IP</c><00:00:09.040><c> is</c>

00:00:09.270 --> 00:00:09.280 align:start position:0%
that which is innovation and IP is
 

00:00:09.280 --> 00:00:10.950 align:start position:0%
that which is innovation and IP is
absolutely<00:00:09.679><c> key</c><00:00:10.000><c> because</c><00:00:10.240><c> otherwise</c><00:00:10.719><c> the</c>

00:00:10.950 --> 00:00:10.960 align:start position:0%
absolutely key because otherwise the
 

00:00:10.960 --> 00:00:12.390 align:start position:0%
absolutely key because otherwise the
conversation<00:00:11.440><c> with</c><00:00:11.599><c> investor</c><00:00:12.000><c> goes</c><00:00:12.240><c> like</c>

00:00:12.390 --> 00:00:12.400 align:start position:0%
conversation with investor goes like
 

00:00:12.400 --> 00:00:15.509 align:start position:0%
conversation with investor goes like
this.<00:00:12.639><c> What</c><00:00:12.719><c> are</c><00:00:12.880><c> you</c><00:00:13.040><c> doing?</c><00:00:13.440><c> Uh</c><00:00:14.200><c> PV,</c><00:00:15.200><c> what</c><00:00:15.440><c> do</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
this. What are you doing? Uh PV, what do
 

00:00:15.519 --> 00:00:16.670 align:start position:0%
this. What are you doing? Uh PV, what do
you<00:00:15.599><c> do?</c><00:00:15.839><c> What</c><00:00:15.920><c> do</c><00:00:16.080><c> you</c><00:00:16.160><c> mean?</c><00:00:16.320><c> Uh</c>

00:00:16.670 --> 00:00:16.680 align:start position:0%
you do? What do you mean? Uh
 

00:00:16.680 --> 00:00:20.550 align:start position:0%
you do? What do you mean? Uh
manufacturing<00:00:17.680><c> where</c><00:00:18.240><c> Europe</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
manufacturing where Europe
 

00:00:20.560 --> 00:00:24.230 align:start position:0%
manufacturing where Europe
or<00:00:20.960><c> if</c><00:00:21.119><c> it's</c><00:00:21.279><c> not</c><00:00:21.439><c> that</c><00:00:21.760><c> Yeah,</c><00:00:21.920><c> but</c><00:00:22.160><c> China.</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
or if it's not that Yeah, but China.
 

00:00:24.240 --> 00:00:26.230 align:start position:0%
or if it's not that Yeah, but China.
So<00:00:24.720><c> you</c><00:00:25.039><c> have</c><00:00:25.199><c> to</c><00:00:25.359><c> deal</c><00:00:25.519><c> with</c><00:00:25.600><c> that.</c><00:00:25.760><c> And</c><00:00:25.920><c> I</c><00:00:26.080><c> I</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
So you have to deal with that. And I I
 

00:00:26.240 --> 00:00:29.669 align:start position:0%
So you have to deal with that. And I I
think<00:00:26.400><c> our</c><00:00:26.640><c> view</c><00:00:26.800><c> is</c><00:00:27.439><c> for</c><00:00:27.680><c> what</c><00:00:27.920><c> we</c><00:00:28.240><c> do</c><00:00:29.199><c> and</c><00:00:29.519><c> for</c>

00:00:29.669 --> 00:00:29.679 align:start position:0%
think our view is for what we do and for
 

00:00:29.679 --> 00:00:31.429 align:start position:0%
think our view is for what we do and for
what<00:00:29.840><c> Bertrron</c><00:00:30.400><c> does,</c><00:00:30.720><c> you</c><00:00:30.880><c> know,</c><00:00:31.039><c> innovation</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
what Bertrron does, you know, innovation
 

00:00:31.439 --> 00:00:33.510 align:start position:0%
what Bertrron does, you know, innovation
is<00:00:31.679><c> absolutely</c><00:00:32.079><c> key.</c><00:00:32.399><c> You</c><00:00:32.480><c> you</c><00:00:32.880><c> cannot</c><00:00:33.200><c> make</c><00:00:33.360><c> a</c>

00:00:33.510 --> 00:00:33.520 align:start position:0%
is absolutely key. You you cannot make a
 

00:00:33.520 --> 00:00:36.790 align:start position:0%
is absolutely key. You you cannot make a
sane<00:00:33.920><c> argument</c><00:00:34.399><c> for</c><00:00:34.640><c> doing</c><00:00:35.600><c> traditional</c><00:00:36.480><c> old</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
sane argument for doing traditional old
 

00:00:36.800 --> 00:00:39.510 align:start position:0%
sane argument for doing traditional old
school<00:00:37.520><c> PV</c><00:00:38.239><c> in</c><00:00:38.480><c> Europe.</c><00:00:38.879><c> You</c><00:00:39.200><c> make</c><00:00:39.280><c> an</c>

00:00:39.510 --> 00:00:39.520 align:start position:0%
school PV in Europe. You make an
 

00:00:39.520 --> 00:00:41.030 align:start position:0%
school PV in Europe. You make an
argument<00:00:39.760><c> for</c><00:00:39.920><c> doing</c><00:00:40.160><c> innovative,</c><00:00:40.800><c> you</c><00:00:40.879><c> know,</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
argument for doing innovative, you know,
 

00:00:41.040 --> 00:00:42.709 align:start position:0%
argument for doing innovative, you know,
high-end<00:00:41.520><c> silicon</c><00:00:41.920><c> PV.</c><00:00:42.320><c> You</c><00:00:42.480><c> make</c><00:00:42.559><c> an</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
high-end silicon PV. You make an
 

00:00:42.719 --> 00:00:45.110 align:start position:0%
high-end silicon PV. You make an
argument<00:00:43.040><c> for</c><00:00:43.280><c> doing</c><00:00:43.920><c> tandem</c><00:00:44.559><c> standing</c><00:00:44.960><c> on</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
argument for doing tandem standing on
 

00:00:45.120 --> 00:00:47.069 align:start position:0%
argument for doing tandem standing on
the<00:00:45.200><c> shoulders</c><00:00:45.520><c> of</c><00:00:45.680><c> that</c><00:00:45.920><c> to</c><00:00:46.079><c> do</c><00:00:46.160><c> the</c><00:00:46.399><c> next</c>

00:00:47.069 --> 00:00:47.079 align:start position:0%
the shoulders of that to do the next
 

00:00:47.079 --> 00:00:49.430 align:start position:0%
the shoulders of that to do the next
generation.<00:00:48.079><c> Um,</c><00:00:48.399><c> but</c><00:00:48.640><c> you</c><00:00:48.879><c> still</c><00:00:49.120><c> have</c><00:00:49.280><c> to</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
generation. Um, but you still have to
 

00:00:49.440 --> 00:00:50.950 align:start position:0%
generation. Um, but you still have to
prove<00:00:49.680><c> your</c><00:00:49.920><c> way</c><00:00:50.079><c> through</c><00:00:50.320><c> it.</c><00:00:50.480><c> And</c><00:00:50.719><c> that's</c>

00:00:50.950 --> 00:00:50.960 align:start position:0%
prove your way through it. And that's
 

00:00:50.960 --> 00:00:53.430 align:start position:0%
prove your way through it. And that's
why<00:00:51.280><c> having</c><00:00:51.760><c> the</c><00:00:52.160><c> facility</c><00:00:52.559><c> that</c><00:00:52.800><c> we</c><00:00:53.039><c> have</c><00:00:53.120><c> in</c>

00:00:53.430 --> 00:00:53.440 align:start position:0%
why having the facility that we have in
 

00:00:53.440 --> 00:00:56.389 align:start position:0%
why having the facility that we have in
small<00:00:53.760><c> scale</c><00:00:54.719><c> in</c><00:00:54.960><c> Germany</c><00:00:55.360><c> has</c><00:00:55.600><c> been</c><00:00:55.760><c> so</c><00:00:56.079><c> vital</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
small scale in Germany has been so vital
 

00:00:56.399 --> 00:00:58.389 align:start position:0%
small scale in Germany has been so vital
to<00:00:56.719><c> be</c><00:00:56.800><c> able</c><00:00:56.960><c> to</c><00:00:57.199><c> prove</c><00:00:57.600><c> the</c><00:00:57.840><c> intellectual</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
to be able to prove the intellectual
 

00:00:58.399 --> 00:01:00.069 align:start position:0%
to be able to prove the intellectual
property,<00:00:58.719><c> to</c><00:00:58.879><c> prove</c><00:00:59.120><c> you</c><00:00:59.280><c> can</c><00:00:59.359><c> do</c><00:00:59.520><c> a</c><00:00:59.680><c> product,</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
property, to prove you can do a product,
 

00:01:00.079 --> 00:01:01.750 align:start position:0%
property, to prove you can do a product,
to<00:01:00.239><c> prove</c><00:01:00.399><c> that</c><00:01:00.640><c> customers</c><00:01:01.039><c> actually</c><00:01:01.280><c> do</c><00:01:01.600><c> want</c>

00:01:01.750 --> 00:01:01.760 align:start position:0%
to prove that customers actually do want
 

00:01:01.760 --> 00:01:03.510 align:start position:0%
to prove that customers actually do want
to<00:01:01.920><c> buy</c><00:01:02.079><c> it,</c><00:01:02.239><c> that</c><00:01:02.480><c> high</c><00:01:02.719><c> efficiency</c><00:01:03.199><c> is</c>

00:01:03.510 --> 00:01:03.520 align:start position:0%
to buy it, that high efficiency is
 

00:01:03.520 --> 00:01:06.870 align:start position:0%
to buy it, that high efficiency is
important,<00:01:04.080><c> that</c><00:01:04.239><c> there's</c><00:01:04.559><c> a</c><00:01:05.119><c> business</c><00:01:05.600><c> case</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
important, that there's a business case
 

00:01:06.880 --> 00:01:11.159 align:start position:0%
important, that there's a business case
um<00:01:07.680><c> for</c><00:01:07.920><c> doing</c><00:01:08.159><c> it.</c>

