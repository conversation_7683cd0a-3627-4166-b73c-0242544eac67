WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.880 align:start position:0%
 
welcome<00:00:00.390><c> back</c><00:00:00.570><c> to</c><00:00:00.719><c> our</c><00:00:00.810><c> gear</c><00:00:00.989><c> tutorial</c><00:00:01.620><c> series</c>

00:00:01.880 --> 00:00:01.890 align:start position:0%
welcome back to our gear tutorial series
 

00:00:01.890 --> 00:00:05.329 align:start position:0%
welcome back to our gear tutorial series
if<00:00:02.310><c> you're</c><00:00:02.460><c> just</c><00:00:02.669><c> getting</c><00:00:02.970><c> started</c><00:00:04.190><c> if</c><00:00:05.190><c> you're</c>

00:00:05.329 --> 00:00:05.339 align:start position:0%
if you're just getting started if you're
 

00:00:05.339 --> 00:00:07.130 align:start position:0%
if you're just getting started if you're
just<00:00:05.520><c> joining</c><00:00:05.580><c> us</c><00:00:05.910><c> now</c><00:00:06.120><c> please</c><00:00:06.330><c> visit</c><00:00:06.660><c> video</c>

00:00:07.130 --> 00:00:07.140 align:start position:0%
just joining us now please visit video
 

00:00:07.140 --> 00:00:08.810 align:start position:0%
just joining us now please visit video
number<00:00:07.500><c> one</c><00:00:07.680><c> because</c><00:00:08.069><c> what</c><00:00:08.250><c> we've</c><00:00:08.400><c> done</c><00:00:08.580><c> so</c>

00:00:08.810 --> 00:00:08.820 align:start position:0%
number one because what we've done so
 

00:00:08.820 --> 00:00:11.180 align:start position:0%
number one because what we've done so
far<00:00:08.849><c> in</c><00:00:09.210><c> this</c><00:00:09.360><c> series</c><00:00:09.420><c> is</c><00:00:10.050><c> we</c><00:00:10.679><c> realized</c><00:00:11.040><c> that</c>

00:00:11.180 --> 00:00:11.190 align:start position:0%
far in this series is we realized that
 

00:00:11.190 --> 00:00:14.570 align:start position:0%
far in this series is we realized that
we<00:00:11.370><c> had</c><00:00:11.400><c> a</c><00:00:11.580><c> problem</c><00:00:11.969><c> on</c><00:00:12.420><c> our</c><00:00:13.009><c> Heroku</c><00:00:14.009><c> site</c><00:00:14.280><c> and</c>

00:00:14.570 --> 00:00:14.580 align:start position:0%
we had a problem on our Heroku site and
 

00:00:14.580 --> 00:00:18.439 align:start position:0%
we had a problem on our Heroku site and
we<00:00:15.269><c> ended</c><00:00:15.509><c> up</c><00:00:15.630><c> just</c><00:00:15.690><c> deleting</c><00:00:16.289><c> two</c><00:00:16.529><c> lines</c><00:00:17.449><c> in</c>

00:00:18.439 --> 00:00:18.449 align:start position:0%
we ended up just deleting two lines in
 

00:00:18.449 --> 00:00:20.240 align:start position:0%
we ended up just deleting two lines in
order<00:00:18.630><c> to</c><00:00:18.869><c> do</c><00:00:18.990><c> that</c><00:00:19.170><c> we</c><00:00:19.350><c> created</c><00:00:19.740><c> a</c><00:00:19.830><c> brand</c><00:00:20.100><c> new</c>

00:00:20.240 --> 00:00:20.250 align:start position:0%
order to do that we created a brand new
 

00:00:20.250 --> 00:00:24.980 align:start position:0%
order to do that we created a brand new
branch<00:00:21.260><c> it</c><00:00:22.260><c> was</c><00:00:22.470><c> called</c><00:00:22.880><c> over</c><00:00:23.880><c> here</c><00:00:24.510><c> git</c>

00:00:24.980 --> 00:00:24.990 align:start position:0%
branch it was called over here git
 

00:00:24.990 --> 00:00:27.500 align:start position:0%
branch it was called over here git
branch<00:00:25.230><c> it</c><00:00:25.859><c> was</c><00:00:25.980><c> called</c><00:00:26.220><c> editing</c><00:00:26.640><c> admin</c><00:00:27.240><c> tab</c>

00:00:27.500 --> 00:00:27.510 align:start position:0%
branch it was called editing admin tab
 

00:00:27.510 --> 00:00:29.870 align:start position:0%
branch it was called editing admin tab
once<00:00:28.289><c> we</c><00:00:28.470><c> made</c><00:00:28.650><c> the</c><00:00:28.800><c> changes</c><00:00:29.250><c> we</c><00:00:29.429><c> pushed</c><00:00:29.699><c> that</c>

00:00:29.870 --> 00:00:29.880 align:start position:0%
once we made the changes we pushed that
 

00:00:29.880 --> 00:00:34.040 align:start position:0%
once we made the changes we pushed that
to<00:00:30.150><c> our</c><00:00:30.269><c> github</c><00:00:30.869><c> repo</c><00:00:31.410><c> and</c><00:00:32.570><c> as</c><00:00:33.570><c> you</c><00:00:33.840><c> can</c><00:00:33.989><c> see</c>

00:00:34.040 --> 00:00:34.050 align:start position:0%
to our github repo and as you can see
 

00:00:34.050 --> 00:00:37.700 align:start position:0%
to our github repo and as you can see
here<00:00:34.530><c> and</c><00:00:34.680><c> I</c><00:00:34.739><c> get</c><00:00:34.980><c> hub</c><00:00:35.130><c> repo</c><00:00:36.260><c> we</c><00:00:37.260><c> see</c><00:00:37.500><c> that</c>

00:00:37.700 --> 00:00:37.710 align:start position:0%
here and I get hub repo we see that
 

00:00:37.710 --> 00:00:42.280 align:start position:0%
here and I get hub repo we see that
there<00:00:37.920><c> was</c><00:00:38.809><c> 130</c><00:00:39.809><c> commits</c><00:00:40.290><c> seven</c><00:00:40.739><c> branches</c>

00:00:42.280 --> 00:00:42.290 align:start position:0%
there was 130 commits seven branches
 

00:00:42.290 --> 00:00:44.510 align:start position:0%
there was 130 commits seven branches
second<00:00:43.290><c> we</c><00:00:43.469><c> pushed</c><00:00:43.710><c> it</c><00:00:43.860><c> to</c><00:00:44.010><c> this</c><00:00:44.160><c> branch</c>

00:00:44.510 --> 00:00:44.520 align:start position:0%
second we pushed it to this branch
 

00:00:44.520 --> 00:00:47.060 align:start position:0%
second we pushed it to this branch
editing<00:00:45.059><c> admin</c><00:00:45.539><c> tab</c><00:00:45.750><c> which</c><00:00:45.930><c> was</c><00:00:46.079><c> updated</c><00:00:46.260><c> 31</c>

00:00:47.060 --> 00:00:47.070 align:start position:0%
editing admin tab which was updated 31
 

00:00:47.070 --> 00:00:48.400 align:start position:0%
editing admin tab which was updated 31
minutes<00:00:47.460><c> ago</c>

00:00:48.400 --> 00:00:48.410 align:start position:0%
minutes ago
 

00:00:48.410 --> 00:00:50.660 align:start position:0%
minutes ago
once<00:00:49.410><c> we</c><00:00:49.620><c> pushed</c><00:00:49.950><c> it</c><00:00:50.100><c> to</c><00:00:50.190><c> that</c><00:00:50.340><c> one</c><00:00:50.550><c> who</c>

00:00:50.660 --> 00:00:50.670 align:start position:0%
once we pushed it to that one who
 

00:00:50.670 --> 00:00:53.869 align:start position:0%
once we pushed it to that one who
created<00:00:50.969><c> a</c><00:00:51.149><c> pull</c><00:00:51.329><c> request</c><00:00:51.360><c> on</c><00:00:51.989><c> github</c><00:00:52.699><c> all</c><00:00:53.699><c> you</c>

00:00:53.869 --> 00:00:53.879 align:start position:0%
created a pull request on github all you
 

00:00:53.879 --> 00:00:55.639 align:start position:0%
created a pull request on github all you
do<00:00:54.180><c> to</c><00:00:54.329><c> create</c><00:00:54.449><c> a</c><00:00:54.629><c> pull</c><00:00:54.840><c> request</c><00:00:54.870><c> is</c><00:00:55.320><c> you</c><00:00:55.469><c> go</c><00:00:55.590><c> to</c>

00:00:55.639 --> 00:00:55.649 align:start position:0%
do to create a pull request is you go to
 

00:00:55.649 --> 00:00:57.380 align:start position:0%
do to create a pull request is you go to
pull<00:00:55.890><c> request</c><00:00:56.250><c> and</c><00:00:56.489><c> you</c><00:00:56.610><c> do</c><00:00:56.760><c> a</c><00:00:56.789><c> new</c><00:00:57.090><c> pull</c>

00:00:57.380 --> 00:00:57.390 align:start position:0%
pull request and you do a new pull
 

00:00:57.390 --> 00:01:01.069 align:start position:0%
pull request and you do a new pull
request<00:00:57.870><c> and</c><00:00:58.140><c> you</c><00:00:59.070><c> say</c><00:00:59.280><c> base</c><00:00:59.609><c> you</c><00:01:00.120><c> say</c><00:01:00.660><c> what</c>

00:01:01.069 --> 00:01:01.079 align:start position:0%
request and you say base you say what
 

00:01:01.079 --> 00:01:02.450 align:start position:0%
request and you say base you say what
you<00:01:01.199><c> want</c><00:01:01.410><c> to</c><00:01:01.469><c> compare</c><00:01:01.829><c> you</c><00:01:01.980><c> want</c><00:01:02.160><c> to</c><00:01:02.219><c> create</c>

00:01:02.450 --> 00:01:02.460 align:start position:0%
you want to compare you want to create
 

00:01:02.460 --> 00:01:04.759 align:start position:0%
you want to compare you want to create
compare<00:01:02.940><c> the</c><00:01:03.059><c> master</c><00:01:03.539><c> branch</c><00:01:03.719><c> to</c><00:01:04.140><c> the</c><00:01:04.170><c> editing</c>

00:01:04.759 --> 00:01:04.769 align:start position:0%
compare the master branch to the editing
 

00:01:04.769 --> 00:01:10.039 align:start position:0%
compare the master branch to the editing
admin<00:01:05.369><c> tab</c><00:01:05.640><c> okay</c><00:01:06.710><c> and</c><00:01:08.450><c> right</c><00:01:09.450><c> now</c><00:01:09.630><c> there</c><00:01:09.780><c> isn't</c>

00:01:10.039 --> 00:01:10.049 align:start position:0%
admin tab okay and right now there isn't
 

00:01:10.049 --> 00:01:11.450 align:start position:0%
admin tab okay and right now there isn't
anything<00:01:10.229><c> because</c><00:01:10.590><c> they're</c><00:01:10.799><c> both</c><00:01:10.979><c> in</c><00:01:11.220><c> sync</c>

00:01:11.450 --> 00:01:11.460 align:start position:0%
anything because they're both in sync
 

00:01:11.460 --> 00:01:13.840 align:start position:0%
anything because they're both in sync
but<00:01:11.790><c> if</c><00:01:12.000><c> you</c><00:01:12.150><c> wanted</c><00:01:12.450><c> to</c><00:01:12.600><c> you</c><00:01:12.840><c> could</c><00:01:12.990><c> do</c>

00:01:13.840 --> 00:01:13.850 align:start position:0%
but if you wanted to you could do
 

00:01:13.850 --> 00:01:17.090 align:start position:0%
but if you wanted to you could do
directives<00:01:14.850><c> for</c><00:01:15.119><c> example</c><00:01:15.740><c> and</c><00:01:16.740><c> you</c><00:01:16.860><c> would</c><00:01:16.979><c> see</c>

00:01:17.090 --> 00:01:17.100 align:start position:0%
directives for example and you would see
 

00:01:17.100 --> 00:01:18.410 align:start position:0%
directives for example and you would see
that<00:01:17.189><c> there's</c><00:01:17.369><c> a</c><00:01:17.430><c> lot</c><00:01:17.670><c> of</c><00:01:17.790><c> changes</c><00:01:18.119><c> but</c><00:01:18.180><c> we</c>

00:01:18.410 --> 00:01:18.420 align:start position:0%
that there's a lot of changes but we
 

00:01:18.420 --> 00:01:19.789 align:start position:0%
that there's a lot of changes but we
don't<00:01:18.570><c> want</c><00:01:18.720><c> to</c><00:01:18.780><c> do</c><00:01:18.900><c> that</c><00:01:19.020><c> because</c><00:01:19.290><c> this</c><00:01:19.470><c> is</c><00:01:19.530><c> an</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
don't want to do that because this is an
 

00:01:19.799 --> 00:01:22.070 align:start position:0%
don't want to do that because this is an
old<00:01:19.950><c> branch</c><00:01:20.310><c> so</c><00:01:20.939><c> anyways</c><00:01:21.299><c> we</c><00:01:21.540><c> create</c><00:01:21.840><c> we</c>

00:01:22.070 --> 00:01:22.080 align:start position:0%
old branch so anyways we create we
 

00:01:22.080 --> 00:01:24.770 align:start position:0%
old branch so anyways we create we
compared<00:01:22.500><c> those</c><00:01:22.770><c> two</c><00:01:23.009><c> branches</c><00:01:23.340><c> the</c><00:01:24.000><c> the</c>

00:01:24.770 --> 00:01:24.780 align:start position:0%
compared those two branches the the
 

00:01:24.780 --> 00:01:27.200 align:start position:0%
compared those two branches the the
master<00:01:25.110><c> branch</c><00:01:25.530><c> and</c><00:01:25.920><c> the</c><00:01:26.040><c> editing</c><00:01:26.490><c> admin</c><00:01:26.970><c> tab</c>

00:01:27.200 --> 00:01:27.210 align:start position:0%
master branch and the editing admin tab
 

00:01:27.210 --> 00:01:28.910 align:start position:0%
master branch and the editing admin tab
branch<00:01:27.630><c> and</c><00:01:27.930><c> we</c><00:01:28.140><c> saw</c><00:01:28.560><c> that</c><00:01:28.650><c> there</c><00:01:28.799><c> were</c><00:01:28.890><c> some</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
branch and we saw that there were some
 

00:01:28.920 --> 00:01:32.539 align:start position:0%
branch and we saw that there were some
changes<00:01:29.340><c> we</c><00:01:30.740><c> created</c><00:01:31.740><c> a</c><00:01:31.799><c> pull</c><00:01:32.009><c> request</c><00:01:32.040><c> which</c>

00:01:32.539 --> 00:01:32.549 align:start position:0%
changes we created a pull request which
 

00:01:32.549 --> 00:01:33.830 align:start position:0%
changes we created a pull request which
is<00:01:32.700><c> pretty</c><00:01:32.880><c> much</c><00:01:33.000><c> like</c><00:01:33.150><c> creating</c><00:01:33.540><c> a</c><00:01:33.600><c> push</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
is pretty much like creating a push
 

00:01:33.840 --> 00:01:37.850 align:start position:0%
is pretty much like creating a push
request<00:01:34.290><c> and</c><00:01:35.040><c> then</c><00:01:35.430><c> we</c><00:01:35.700><c> ended</c><00:01:36.090><c> up</c><00:01:36.270><c> merging</c><00:01:37.170><c> the</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
request and then we ended up merging the
 

00:01:37.860 --> 00:01:40.940 align:start position:0%
request and then we ended up merging the
two<00:01:38.159><c> and</c><00:01:38.520><c> then</c><00:01:38.820><c> when</c><00:01:39.570><c> we</c><00:01:39.689><c> saw</c><00:01:39.900><c> we</c><00:01:40.320><c> saw</c><00:01:40.350><c> that</c>

00:01:40.940 --> 00:01:40.950 align:start position:0%
two and then when we saw we saw that
 

00:01:40.950 --> 00:01:43.389 align:start position:0%
two and then when we saw we saw that
there<00:01:41.400><c> was</c><00:01:41.610><c> the</c><00:01:42.119><c> commit</c><00:01:42.509><c> message</c><00:01:42.930><c> of</c><00:01:43.110><c> that</c>

00:01:43.389 --> 00:01:43.399 align:start position:0%
there was the commit message of that
 

00:01:43.399 --> 00:01:46.760 align:start position:0%
there was the commit message of that
wasn't<00:01:44.399><c> was</c><00:01:44.700><c> was</c><00:01:45.360><c> merged</c><00:01:45.810><c> as</c><00:01:46.170><c> you</c><00:01:46.439><c> can</c><00:01:46.590><c> see</c>

00:01:46.760 --> 00:01:46.770 align:start position:0%
wasn't was was merged as you can see
 

00:01:46.770 --> 00:01:49.910 align:start position:0%
wasn't was was merged as you can see
here<00:01:47.070><c> fixed</c><00:01:47.759><c> admin</c><00:01:48.119><c> tab</c><00:01:48.329><c> 32</c><00:01:48.840><c> minutes</c><00:01:49.049><c> ago</c><00:01:49.380><c> once</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
here fixed admin tab 32 minutes ago once
 

00:01:49.920 --> 00:01:51.859 align:start position:0%
here fixed admin tab 32 minutes ago once
we<00:01:50.100><c> did</c><00:01:50.280><c> that</c><00:01:50.340><c> we</c><00:01:50.549><c> went</c><00:01:50.850><c> back</c><00:01:51.119><c> into</c><00:01:51.360><c> our</c>

00:01:51.859 --> 00:01:51.869 align:start position:0%
we did that we went back into our
 

00:01:51.869 --> 00:01:54.230 align:start position:0%
we did that we went back into our
command<00:01:52.409><c> line</c><00:01:52.530><c> we</c><00:01:53.250><c> went</c><00:01:53.460><c> into</c><00:01:53.700><c> the</c><00:01:54.030><c> master</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
command line we went into the master
 

00:01:54.240 --> 00:01:56.480 align:start position:0%
command line we went into the master
branch<00:01:54.840><c> and</c><00:01:55.020><c> we</c><00:01:55.200><c> pulled</c><00:01:55.500><c> all</c><00:01:55.770><c> the</c><00:01:55.950><c> changes</c>

00:01:56.480 --> 00:01:56.490 align:start position:0%
branch and we pulled all the changes
 

00:01:56.490 --> 00:02:00.920 align:start position:0%
branch and we pulled all the changes
from<00:01:56.700><c> the</c><00:01:56.969><c> editing</c><00:01:57.360><c> admin</c><00:01:57.869><c> tab</c><00:01:59.180><c> branch</c><00:02:00.180><c> we</c>

00:02:00.920 --> 00:02:00.930 align:start position:0%
from the editing admin tab branch we
 

00:02:00.930 --> 00:02:03.469 align:start position:0%
from the editing admin tab branch we
pulled<00:02:01.259><c> that</c><00:02:01.469><c> into</c><00:02:01.950><c> our</c><00:02:02.219><c> master</c><00:02:02.640><c> branch</c><00:02:03.210><c> you</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
pulled that into our master branch you
 

00:02:03.479 --> 00:02:06.859 align:start position:0%
pulled that into our master branch you
could<00:02:03.630><c> do</c><00:02:03.750><c> that</c><00:02:03.899><c> with</c><00:02:04.140><c> with</c><00:02:04.969><c> this</c><00:02:05.969><c> command</c><00:02:06.390><c> get</c>

00:02:06.859 --> 00:02:06.869 align:start position:0%
could do that with with this command get
 

00:02:06.869 --> 00:02:11.150 align:start position:0%
could do that with with this command get
pull<00:02:07.380><c> let's</c><00:02:07.860><c> like</c><00:02:08.750><c> get</c><00:02:09.750><c> pull</c><00:02:09.959><c> origin</c><00:02:10.229><c> editing</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
pull let's like get pull origin editing
 

00:02:11.160 --> 00:02:13.260 align:start position:0%
pull let's like get pull origin editing
admin<00:02:11.520><c> tab</c><00:02:11.730><c> we</c><00:02:11.879><c> pulled</c><00:02:12.180><c> all</c><00:02:12.450><c> the</c><00:02:12.690><c> changes</c>

00:02:13.260 --> 00:02:13.270 align:start position:0%
admin tab we pulled all the changes
 

00:02:13.270 --> 00:02:16.590 align:start position:0%
admin tab we pulled all the changes
from<00:02:13.540><c> the</c><00:02:13.690><c> editing</c><00:02:14.080><c> admin</c><00:02:14.680><c> tab</c><00:02:15.010><c> branch</c><00:02:15.640><c> into</c>

00:02:16.590 --> 00:02:16.600 align:start position:0%
from the editing admin tab branch into
 

00:02:16.600 --> 00:02:18.810 align:start position:0%
from the editing admin tab branch into
the<00:02:16.810><c> master</c><00:02:17.320><c> branch</c><00:02:17.530><c> okay</c><00:02:18.250><c> the</c><00:02:18.520><c> branch</c><00:02:18.730><c> that</c>

00:02:18.810 --> 00:02:18.820 align:start position:0%
the master branch okay the branch that
 

00:02:18.820 --> 00:02:20.970 align:start position:0%
the master branch okay the branch that
we<00:02:19.060><c> were</c><00:02:19.180><c> in</c><00:02:19.390><c> at</c><00:02:19.510><c> the</c><00:02:19.540><c> time</c><00:02:19.840><c> master</c><00:02:20.740><c> branch</c>

00:02:20.970 --> 00:02:20.980 align:start position:0%
we were in at the time master branch
 

00:02:20.980 --> 00:02:22.710 align:start position:0%
we were in at the time master branch
once<00:02:21.280><c> we</c><00:02:21.430><c> did</c><00:02:21.580><c> that</c><00:02:21.640><c> we</c><00:02:21.940><c> were</c><00:02:22.060><c> able</c><00:02:22.360><c> to</c><00:02:22.390><c> push</c>

00:02:22.710 --> 00:02:22.720 align:start position:0%
once we did that we were able to push
 

00:02:22.720 --> 00:02:23.940 align:start position:0%
once we did that we were able to push
everything<00:02:23.020><c> to</c><00:02:23.350><c> Heroku</c>

00:02:23.940 --> 00:02:23.950 align:start position:0%
everything to Heroku
 

00:02:23.950 --> 00:02:26.370 align:start position:0%
everything to Heroku
and<00:02:24.160><c> once</c><00:02:25.060><c> we</c><00:02:25.210><c> pushed</c><00:02:25.480><c> everything</c><00:02:25.780><c> to</c><00:02:26.110><c> Heroku</c>

00:02:26.370 --> 00:02:26.380 align:start position:0%
and once we pushed everything to Heroku
 

00:02:26.380 --> 00:02:30.990 align:start position:0%
and once we pushed everything to Heroku
we<00:02:26.860><c> were</c><00:02:26.980><c> finally</c><00:02:27.520><c> able</c><00:02:29.160><c> to</c><00:02:30.160><c> remove</c><00:02:30.730><c> those</c>

00:02:30.990 --> 00:02:31.000 align:start position:0%
we were finally able to remove those
 

00:02:31.000 --> 00:02:32.970 align:start position:0%
we were finally able to remove those
lines<00:02:31.510><c> that</c><00:02:31.660><c> we</c><00:02:31.900><c> didn't</c><00:02:32.200><c> want</c><00:02:32.410><c> so</c><00:02:32.590><c> as</c><00:02:32.740><c> you</c><00:02:32.860><c> can</c>

00:02:32.970 --> 00:02:32.980 align:start position:0%
lines that we didn't want so as you can
 

00:02:32.980 --> 00:02:35.040 align:start position:0%
lines that we didn't want so as you can
see<00:02:33.190><c> everything</c><00:02:33.640><c> worked</c><00:02:33.910><c> and</c><00:02:34.210><c> that's</c><00:02:34.900><c> pretty</c>

00:02:35.040 --> 00:02:35.050 align:start position:0%
see everything worked and that's pretty
 

00:02:35.050 --> 00:02:37.440 align:start position:0%
see everything worked and that's pretty
much<00:02:35.290><c> a</c><00:02:35.320><c> good</c><00:02:35.560><c> flow</c><00:02:35.920><c> for</c><00:02:36.160><c> you</c><00:02:36.250><c> to</c><00:02:36.400><c> follow</c><00:02:36.610><c> in</c>

00:02:37.440 --> 00:02:37.450 align:start position:0%
much a good flow for you to follow in
 

00:02:37.450 --> 00:02:42.180 align:start position:0%
much a good flow for you to follow in
summary<00:02:38.610><c> you</c><00:02:39.610><c> can</c><00:02:39.640><c> you</c><00:02:40.390><c> can</c><00:02:40.630><c> create</c><00:02:41.410><c> a</c><00:02:41.950><c> new</c>

00:02:42.180 --> 00:02:42.190 align:start position:0%
summary you can you can create a new
 

00:02:42.190 --> 00:02:43.920 align:start position:0%
summary you can you can create a new
branch<00:02:42.520><c> every</c><00:02:42.970><c> time</c><00:02:43.150><c> you</c><00:02:43.300><c> want</c><00:02:43.510><c> to</c><00:02:43.570><c> create</c><00:02:43.780><c> a</c>

00:02:43.920 --> 00:02:43.930 align:start position:0%
branch every time you want to create a
 

00:02:43.930 --> 00:02:48.090 align:start position:0%
branch every time you want to create a
new<00:02:44.200><c> feature</c><00:02:44.530><c> or</c><00:02:44.950><c> do</c><00:02:45.400><c> a</c><00:02:45.430><c> specific</c><00:02:45.850><c> fix</c><00:02:47.100><c> once</c>

00:02:48.090 --> 00:02:48.100 align:start position:0%
new feature or do a specific fix once
 

00:02:48.100 --> 00:02:50.220 align:start position:0%
new feature or do a specific fix once
you're<00:02:48.250><c> on</c><00:02:48.280><c> that</c><00:02:48.430><c> new</c><00:02:48.850><c> branch</c><00:02:49.180><c> you</c><00:02:49.570><c> do</c><00:02:50.050><c> your</c>

00:02:50.220 --> 00:02:50.230 align:start position:0%
you're on that new branch you do your
 

00:02:50.230 --> 00:02:51.840 align:start position:0%
you're on that new branch you do your
changes<00:02:50.440><c> on</c><00:02:50.800><c> the</c><00:02:50.920><c> code</c><00:02:51.130><c> everything</c><00:02:51.490><c> you</c><00:02:51.700><c> need</c>

00:02:51.840 --> 00:02:51.850 align:start position:0%
changes on the code everything you need
 

00:02:51.850 --> 00:02:54.750 align:start position:0%
changes on the code everything you need
to<00:02:51.970><c> do</c><00:02:52.120><c> push</c><00:02:52.660><c> that</c><00:02:52.960><c> branch</c><00:02:53.380><c> to</c><00:02:53.680><c> the</c><00:02:53.710><c> Roku</c><00:02:54.340><c> merge</c>

00:02:54.750 --> 00:02:54.760 align:start position:0%
to do push that branch to the Roku merge
 

00:02:54.760 --> 00:02:56.580 align:start position:0%
to do push that branch to the Roku merge
it<00:02:54.910><c> into</c><00:02:55.150><c> the</c><00:02:55.240><c> master</c><00:02:55.600><c> branch</c><00:02:55.750><c> merge</c>

00:02:56.580 --> 00:02:56.590 align:start position:0%
it into the master branch merge
 

00:02:56.590 --> 00:02:58.710 align:start position:0%
it into the master branch merge
everything<00:02:57.040><c> on</c><00:02:57.190><c> your</c><00:02:57.430><c> local</c><00:02:57.970><c> machine</c><00:02:58.150><c> to</c><00:02:58.540><c> the</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
everything on your local machine to the
 

00:02:58.720 --> 00:03:00.330 align:start position:0%
everything on your local machine to the
master<00:02:59.050><c> branch</c><00:02:59.290><c> push</c><00:02:59.620><c> that</c><00:02:59.830><c> out</c><00:02:59.890><c> to</c>

00:03:00.330 --> 00:03:00.340 align:start position:0%
master branch push that out to
 

00:03:00.340 --> 00:03:02.520 align:start position:0%
master branch push that out to
production<00:03:00.670><c> so</c><00:03:01.090><c> that's</c><00:03:01.690><c> pretty</c><00:03:01.930><c> much</c><00:03:02.020><c> the</c>

00:03:02.520 --> 00:03:02.530 align:start position:0%
production so that's pretty much the
 

00:03:02.530 --> 00:03:07.080 align:start position:0%
production so that's pretty much the
summary<00:03:03.010><c> of</c><00:03:03.390><c> these</c><00:03:04.390><c> these</c><00:03:05.290><c> github</c><00:03:05.740><c> videos</c><00:03:06.100><c> we</c>

00:03:07.080 --> 00:03:07.090 align:start position:0%
summary of these these github videos we
 

00:03:07.090 --> 00:03:09.830 align:start position:0%
summary of these these github videos we
may<00:03:07.240><c> continue</c><00:03:07.660><c> with</c><00:03:07.690><c> a</c><00:03:07.810><c> couple</c><00:03:08.110><c> more</c><00:03:08.260><c> complex</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
may continue with a couple more complex
 

00:03:09.840 --> 00:03:12.180 align:start position:0%
may continue with a couple more complex
subjects<00:03:10.840><c> in</c><00:03:10.930><c> the</c><00:03:11.050><c> past</c><00:03:11.260><c> in</c><00:03:11.470><c> terms</c><00:03:11.500><c> of</c><00:03:11.800><c> github</c>

00:03:12.180 --> 00:03:12.190 align:start position:0%
subjects in the past in terms of github
 

00:03:12.190 --> 00:03:17.010 align:start position:0%
subjects in the past in terms of github
but<00:03:12.400><c> pretty</c><00:03:12.820><c> much</c><00:03:12.880><c> I</c><00:03:13.180><c> recommend</c><00:03:15.540><c> just</c><00:03:16.540><c> google</c>

00:03:17.010 --> 00:03:17.020 align:start position:0%
but pretty much I recommend just google
 

00:03:17.020 --> 00:03:19.440 align:start position:0%
but pretty much I recommend just google
searching<00:03:17.530><c> it</c><00:03:17.940><c> everything</c><00:03:18.940><c> that</c><00:03:19.120><c> you</c><00:03:19.240><c> need</c>

00:03:19.440 --> 00:03:19.450 align:start position:0%
searching it everything that you need
 

00:03:19.450 --> 00:03:21.390 align:start position:0%
searching it everything that you need
that<00:03:19.750><c> you</c><00:03:20.080><c> need</c><00:03:20.260><c> sometimes</c><00:03:20.830><c> you</c><00:03:20.980><c> might</c><00:03:21.160><c> need</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
that you need sometimes you might need
 

00:03:21.400 --> 00:03:24.449 align:start position:0%
that you need sometimes you might need
to<00:03:21.610><c> pull</c><00:03:21.910><c> a</c><00:03:21.940><c> specific</c><00:03:22.780><c> commit</c><00:03:23.290><c> from</c><00:03:24.100><c> your</c>

00:03:24.449 --> 00:03:24.459 align:start position:0%
to pull a specific commit from your
 

00:03:24.459 --> 00:03:27.030 align:start position:0%
to pull a specific commit from your
github<00:03:25.150><c> repo</c><00:03:25.570><c> to</c><00:03:25.810><c> your</c><00:03:25.959><c> local</c><00:03:26.170><c> machine</c><00:03:26.440><c> and</c>

00:03:27.030 --> 00:03:27.040 align:start position:0%
github repo to your local machine and
 

00:03:27.040 --> 00:03:31.020 align:start position:0%
github repo to your local machine and
then<00:03:28.830><c> there's</c><00:03:29.830><c> all</c><00:03:29.980><c> sorts</c><00:03:30.310><c> of</c><00:03:30.400><c> cases</c>

00:03:31.020 --> 00:03:31.030 align:start position:0%
then there's all sorts of cases
 

00:03:31.030 --> 00:03:32.910 align:start position:0%
then there's all sorts of cases
sometimes<00:03:31.240><c> on</c><00:03:31.660><c> one</c><00:03:31.959><c> specific</c><00:03:32.200><c> branch</c><00:03:32.740><c> you're</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
sometimes on one specific branch you're
 

00:03:32.920 --> 00:03:36.180 align:start position:0%
sometimes on one specific branch you're
going<00:03:33.070><c> to</c><00:03:33.130><c> want</c><00:03:33.310><c> to</c><00:03:33.370><c> go</c><00:03:33.670><c> back</c><00:03:33.700><c> in</c><00:03:34.600><c> time</c><00:03:35.170><c> and</c><00:03:35.500><c> a</c>

00:03:36.180 --> 00:03:36.190 align:start position:0%
going to want to go back in time and a
 

00:03:36.190 --> 00:03:38.640 align:start position:0%
going to want to go back in time and a
really<00:03:36.490><c> helpful</c><00:03:36.790><c> trick</c><00:03:37.300><c> by</c><00:03:38.110><c> the</c><00:03:38.170><c> way</c><00:03:38.380><c> is</c>

00:03:38.640 --> 00:03:38.650 align:start position:0%
really helpful trick by the way is
 

00:03:38.650 --> 00:03:42.720 align:start position:0%
really helpful trick by the way is
something<00:03:39.100><c> called</c><00:03:39.810><c> get</c><00:03:40.810><c> love</c><00:03:41.110><c> and</c><00:03:41.730><c> that's</c>

00:03:42.720 --> 00:03:42.730 align:start position:0%
something called get love and that's
 

00:03:42.730 --> 00:03:44.310 align:start position:0%
something called get love and that's
gonna<00:03:42.940><c> also</c><00:03:43.300><c> help</c><00:03:43.480><c> us</c><00:03:43.600><c> sum</c><00:03:43.810><c> up</c><00:03:43.959><c> what</c><00:03:44.170><c> we've</c>

00:03:44.310 --> 00:03:44.320 align:start position:0%
gonna also help us sum up what we've
 

00:03:44.320 --> 00:03:47.340 align:start position:0%
gonna also help us sum up what we've
done<00:03:44.560><c> so</c><00:03:44.890><c> far</c><00:03:45.510><c> hmm</c><00:03:46.510><c> let's</c><00:03:46.870><c> see</c><00:03:46.930><c> what</c><00:03:47.110><c> it</c><00:03:47.200><c> says</c>

00:03:47.340 --> 00:03:47.350 align:start position:0%
done so far hmm let's see what it says
 

00:03:47.350 --> 00:03:50.010 align:start position:0%
done so far hmm let's see what it says
here<00:03:47.700><c> made</c><00:03:48.700><c> changes</c><00:03:49.090><c> to</c><00:03:49.150><c> the</c><00:03:49.360><c> front</c><00:03:49.630><c> end</c><00:03:49.930><c> I</c>

00:03:50.010 --> 00:03:50.020 align:start position:0%
here made changes to the front end I
 

00:03:50.020 --> 00:03:53.280 align:start position:0%
here made changes to the front end I
fixed<00:03:50.530><c> the</c><00:03:50.590><c> add</c><00:03:50.830><c> event</c><00:03:51.040><c> tab</c><00:03:51.570><c> okay</c><00:03:52.570><c> and</c><00:03:52.870><c> here</c>

00:03:53.280 --> 00:03:53.290 align:start position:0%
fixed the add event tab okay and here
 

00:03:53.290 --> 00:03:55.140 align:start position:0%
fixed the add event tab okay and here
you<00:03:53.590><c> see</c><00:03:53.800><c> that</c><00:03:53.980><c> we</c><00:03:54.130><c> have</c><00:03:54.280><c> commits</c><00:03:54.700><c> and</c><00:03:54.910><c> you</c><00:03:54.970><c> can</c>

00:03:55.140 --> 00:03:55.150 align:start position:0%
you see that we have commits and you can
 

00:03:55.150 --> 00:03:58.199 align:start position:0%
you see that we have commits and you can
revert<00:03:55.570><c> back</c><00:03:55.930><c> to</c><00:03:56.230><c> these</c><00:03:56.410><c> commits</c><00:03:57.000><c> you</c><00:03:58.000><c> could</c>

00:03:58.199 --> 00:03:58.209 align:start position:0%
revert back to these commits you could
 

00:03:58.209 --> 00:04:00.780 align:start position:0%
revert back to these commits you could
do<00:03:58.300><c> a</c><00:03:58.330><c> git</c><00:03:58.660><c> revert</c><00:03:59.110><c> and</c><00:03:59.440><c> so</c><00:03:59.709><c> on</c><00:03:59.950><c> we</c><00:04:00.430><c> may</c><00:04:00.580><c> cover</c>

00:04:00.780 --> 00:04:00.790 align:start position:0%
do a git revert and so on we may cover
 

00:04:00.790 --> 00:04:03.150 align:start position:0%
do a git revert and so on we may cover
that<00:04:00.970><c> in</c><00:04:01.120><c> a</c><00:04:01.630><c> future</c><00:04:01.900><c> video</c><00:04:02.170><c> so</c><00:04:02.650><c> hope</c><00:04:02.980><c> you</c><00:04:03.100><c> guys</c>

00:04:03.150 --> 00:04:03.160 align:start position:0%
that in a future video so hope you guys
 

00:04:03.160 --> 00:04:04.890 align:start position:0%
that in a future video so hope you guys
learn<00:04:04.060><c> something</c><00:04:04.450><c> if</c><00:04:04.570><c> you</c><00:04:04.660><c> have</c><00:04:04.780><c> any</c>

00:04:04.890 --> 00:04:04.900 align:start position:0%
learn something if you have any
 

00:04:04.900 --> 00:04:06.690 align:start position:0%
learn something if you have any
questions<00:04:05.560><c> about</c><00:04:05.770><c> what</c><00:04:06.040><c> we</c><00:04:06.160><c> covered</c><00:04:06.520><c> so</c><00:04:06.670><c> far</c>

00:04:06.690 --> 00:04:06.700 align:start position:0%
questions about what we covered so far
 

00:04:06.700 --> 00:04:10.110 align:start position:0%
questions about what we covered so far
on<00:04:07.150><c> get</c><00:04:07.390><c> feel</c><00:04:08.080><c> free</c><00:04:08.410><c> to</c><00:04:08.520><c> to</c><00:04:09.520><c> let</c><00:04:09.700><c> us</c><00:04:09.820><c> know</c><00:04:09.970><c> in</c>

00:04:10.110 --> 00:04:10.120 align:start position:0%
on get feel free to to let us know in
 

00:04:10.120 --> 00:04:12.630 align:start position:0%
on get feel free to to let us know in
the<00:04:10.150><c> comment</c>

