#!/usr/bin/env python3
"""
Simple example demonstrating the Google GenAI Chat Manager with conversation memory.

This example shows how to use the built-in chat functionality that automatically
maintains conversation history and context across multiple API calls.
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Path setup for imports - ensure parent directory is in path for relative imports
script_dir = Path(__file__).resolve().parent
parent_dir = script_dir.parent
sys.path.insert(0, str(parent_dir))

# Import our chat manager - use absolute import to avoid relative import issues
try:
    from chat.chat_manager import <PERSON>t<PERSON>ana<PERSON>, create_simple_chat, quick_chat_exchange
except ImportError:
    # Fallback for direct execution - add current directory to path
    sys.path.insert(0, str(script_dir))
    from chat_manager import ChatManager, create_simple_chat, quick_chat_exchange

async def basic_example():
    """Basic chat example demonstrating conversation memory."""
    print("=== Basic Chat Example ===")
    
    # Create a chat session
    manager, session_id = await create_simple_chat(
        system_instruction="You are a helpful assistant that remembers our conversation."
    )
    
    print(f"Created chat session: {session_id}")
    
    # First message
    response1, tokens1_in, tokens1_out, cost1 = await manager.send_message(
        session_id, 
        "Hi! My name is <PERSON> and I love programming in Python."
    )
    print(f"\nUser: Hi! My name is Alice and I love programming in Python.")
    print(f"Assistant: {response1}")
    print(f"Tokens: {tokens1_in}/{tokens1_out}, Cost: ${cost1 or 0:.6f}")
    
    # Second message - the AI should remember Alice's name and interest
    response2, tokens2_in, tokens2_out, cost2 = await manager.send_message(
        session_id,
        "What programming languages would you recommend for someone like me?"
    )
    print(f"\nUser: What programming languages would you recommend for someone like me?")
    print(f"Assistant: {response2}")
    print(f"Tokens: {tokens2_in}/{tokens2_out}, Cost: ${cost2 or 0:.6f}")
    
    # Third message - continuing the context
    response3, tokens3_in, tokens3_out, cost3 = await manager.send_message(
        session_id,
        "Thanks! Can you remind me what I told you about myself earlier?"
    )
    print(f"\nUser: Thanks! Can you remind me what I told you about myself earlier?")
    print(f"Assistant: {response3}")
    print(f"Tokens: {tokens3_in}/{tokens3_out}, Cost: ${cost3 or 0:.6f}")
    
    # Show session summary
    summary = manager.get_session_summary(session_id)
    print(f"\n=== Session Summary ===")
    print(f"Total messages: {summary['total_messages']}")
    print(f"Total tokens: {summary['total_input_tokens'] + summary['total_output_tokens']}")
    print(f"Total cost: ${summary['total_cost']:.6f}")
    
    # Close the session
    await manager.close_session(session_id)
    print(f"\nSession {session_id} closed.")

async def tic_tac_toe_example():
    """Example showing a tic-tac-toe game with conversation memory."""
    print("\n=== Tic-Tac-Toe Game Example ===")
    
    # Create a chat session for playing tic-tac-toe
    manager, session_id = await create_simple_chat(
        system_instruction="You are playing tic-tac-toe with the user. Keep track of the game state and make strategic moves. Always show the current board state."
    )
    
    print(f"Created tic-tac-toe session: {session_id}")
    
    # Game moves
    moves = [
        "I want to play tic-tac-toe. I'll be X and you'll be O. I choose the center position (5).",
        "I'll take position 1 (top-left corner).",
        "I'll take position 9 (bottom-right corner).",
        "Good game! Let's start a new one. I'll take position 3 (top-right) this time."
    ]
    
    for i, move in enumerate(moves, 1):
        response, _, _, cost = await manager.send_message(session_id, move)
        print(f"\nMove {i}")
        print(f"User: {move}")
        print(f"Assistant: {response}")
        if cost:
            print(f"Cost: ${cost:.6f}")
    
    # Close the session
    await manager.close_session(session_id)
    print(f"\nTic-tac-toe session closed.")

async def quick_exchange_example():
    """Example using the quick exchange function for one-off interactions."""
    print("\n=== Quick Exchange Example ===")
    
    # Quick one-off exchange without session management
    response = await quick_chat_exchange(
        "Explain quantum computing in simple terms.",
        system_instruction="You are a science teacher explaining complex topics simply."
    )
    
    print("User: Explain quantum computing in simple terms.")
    print(f"Assistant: {response}")

async def main():
    """Main function to run all examples."""
    # Load environment variables
    load_dotenv()
    
    # Check for API key
    api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: No API key found. Set GOOGLE_API_KEY or GEMINI_API_KEY environment variable.")
        return
    
    print("🤖 Google GenAI Chat Manager Examples")
    print("====================================")
    
    try:
        # Run the examples
        await basic_example()
        await tic_tac_toe_example()
        await quick_exchange_example()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 