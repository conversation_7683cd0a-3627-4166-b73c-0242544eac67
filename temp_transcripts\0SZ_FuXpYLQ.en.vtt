WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.550 align:start position:0%
 
today<00:00:00.399><c> we're</c><00:00:00.560><c> going</c><00:00:00.680><c> to</c><00:00:00.799><c> be</c><00:00:00.919><c> going</c><00:00:01.160><c> over</c><00:00:01.400><c> a</c>

00:00:01.550 --> 00:00:01.560 align:start position:0%
today we're going to be going over a
 

00:00:01.560 --> 00:00:03.510 align:start position:0%
today we're going to be going over a
speech<00:00:01.880><c> recognition</c><00:00:02.399><c> model</c><00:00:02.800><c> whisper</c><00:00:03.320><c> we'll</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
speech recognition model whisper we'll
 

00:00:03.520 --> 00:00:05.829 align:start position:0%
speech recognition model whisper we'll
be<00:00:03.679><c> transcribing</c><00:00:04.440><c> and</c><00:00:04.720><c> translating</c><00:00:05.319><c> an</c><00:00:05.520><c> audio</c>

00:00:05.829 --> 00:00:05.839 align:start position:0%
be transcribing and translating an audio
 

00:00:05.839 --> 00:00:07.990 align:start position:0%
be transcribing and translating an audio
file<00:00:06.240><c> from</c><00:00:06.480><c> a</c><00:00:06.680><c> previous</c><00:00:07.080><c> video</c><00:00:07.439><c> so</c><00:00:07.560><c> we'll</c><00:00:07.720><c> take</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
file from a previous video so we'll take
 

00:00:08.000 --> 00:00:10.870 align:start position:0%
file from a previous video so we'll take
this<00:00:08.160><c> audio</c><00:00:08.559><c> file</c><00:00:09.320><c> in</c><00:00:09.400><c> a</c><00:00:09.599><c> world</c><00:00:10.000><c> where</c><00:00:10.200><c> I</c><00:00:10.360><c> roam</c>

00:00:10.870 --> 00:00:10.880 align:start position:0%
this audio file in a world where I roam
 

00:00:10.880 --> 00:00:14.150 align:start position:0%
this audio file in a world where I roam
freely<00:00:11.799><c> a</c><00:00:12.000><c> curious</c><00:00:12.440><c> sign</c><00:00:12.759><c> named</c><00:00:13.599><c> or</c><00:00:13.799><c> a</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
freely a curious sign named or a
 

00:00:14.160 --> 00:00:16.310 align:start position:0%
freely a curious sign named or a
discovered<00:00:14.519><c> at</c><00:00:14.719><c> Hidden</c><00:00:15.120><c> library</c><00:00:15.639><c> of</c><00:00:16.000><c> and</c><00:00:16.119><c> then</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
discovered at Hidden library of and then
 

00:00:16.320 --> 00:00:18.189 align:start position:0%
discovered at Hidden library of and then
translate<00:00:16.720><c> that</c><00:00:16.880><c> into</c><00:00:17.199><c> another</c><00:00:17.600><c> language</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
translate that into another language
 

00:00:18.199 --> 00:00:19.750 align:start position:0%
translate that into another language
like<00:00:18.400><c> French</c><00:00:19.000><c> now</c><00:00:19.119><c> we'll</c><00:00:19.240><c> be</c><00:00:19.359><c> using</c><00:00:19.600><c> the</c>

00:00:19.750 --> 00:00:19.760 align:start position:0%
like French now we'll be using the
 

00:00:19.760 --> 00:00:22.269 align:start position:0%
like French now we'll be using the
whisper<00:00:20.240><c> large</c><00:00:20.760><c> version</c><00:00:21.119><c> 3</c><00:00:21.480><c> model</c><00:00:21.760><c> from</c><00:00:21.960><c> open</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
whisper large version 3 model from open
 

00:00:22.279 --> 00:00:25.550 align:start position:0%
whisper large version 3 model from open
AI<00:00:22.720><c> this</c><00:00:22.800><c> is</c><00:00:22.960><c> trained</c><00:00:23.279><c> on</c><00:00:23.519><c> 6</c><00:00:24.080><c> 180,000</c><00:00:25.039><c> hours</c><00:00:25.359><c> of</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
AI this is trained on 6 180,000 hours of
 

00:00:25.560 --> 00:00:27.429 align:start position:0%
AI this is trained on 6 180,000 hours of
labeled<00:00:26.080><c> data</c><00:00:26.480><c> and</c><00:00:26.640><c> just</c><00:00:26.840><c> last</c><00:00:27.000><c> month</c><00:00:27.199><c> it</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
labeled data and just last month it
 

00:00:27.439 --> 00:00:28.870 align:start position:0%
labeled data and just last month it
almost<00:00:27.720><c> had</c><00:00:27.840><c> a</c><00:00:27.960><c> million</c><00:00:28.240><c> downloads</c><00:00:28.679><c> here</c><00:00:28.800><c> are</c>

00:00:28.870 --> 00:00:28.880 align:start position:0%
almost had a million downloads here are
 

00:00:28.880 --> 00:00:30.830 align:start position:0%
almost had a million downloads here are
all<00:00:29.000><c> the</c><00:00:29.160><c> sizes</c><00:00:29.560><c> that</c><00:00:29.679><c> you</c><00:00:30.000><c> can</c><00:00:30.240><c> use</c><00:00:30.480><c> I</c><00:00:30.599><c> will</c><00:00:30.720><c> be</c>

00:00:30.830 --> 00:00:30.840 align:start position:0%
all the sizes that you can use I will be
 

00:00:30.840 --> 00:00:33.229 align:start position:0%
all the sizes that you can use I will be
using<00:00:31.199><c> the</c><00:00:31.400><c> largest</c><00:00:31.880><c> version</c><00:00:32.200><c> 3</c><00:00:32.680><c> but</c><00:00:32.920><c> all</c><00:00:33.079><c> of</c>

00:00:33.229 --> 00:00:33.239 align:start position:0%
using the largest version 3 but all of
 

00:00:33.239 --> 00:00:35.630 align:start position:0%
using the largest version 3 but all of
them<00:00:33.440><c> are</c><00:00:33.960><c> multilingual</c><00:00:34.960><c> okay</c><00:00:35.320><c> well</c><00:00:35.480><c> let's</c>

00:00:35.630 --> 00:00:35.640 align:start position:0%
them are multilingual okay well let's
 

00:00:35.640 --> 00:00:36.950 align:start position:0%
them are multilingual okay well let's
start<00:00:35.840><c> coding</c><00:00:36.120><c> it</c><00:00:36.280><c> and</c><00:00:36.399><c> the</c><00:00:36.559><c> first</c><00:00:36.719><c> thing</c><00:00:36.840><c> we</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
start coding it and the first thing we
 

00:00:36.960 --> 00:00:38.190 align:start position:0%
start coding it and the first thing we
need<00:00:37.079><c> to</c><00:00:37.200><c> do</c><00:00:37.440><c> is</c><00:00:37.600><c> install</c><00:00:37.960><c> all</c><00:00:38.079><c> the</c>

00:00:38.190 --> 00:00:38.200 align:start position:0%
need to do is install all the
 

00:00:38.200 --> 00:00:40.110 align:start position:0%
need to do is install all the
requirements<00:00:39.120><c> I</c><00:00:39.280><c> have</c><00:00:39.440><c> I</c><00:00:39.520><c> will</c><00:00:39.719><c> have</c><00:00:39.920><c> a</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
requirements I have I will have a
 

00:00:40.120 --> 00:00:42.069 align:start position:0%
requirements I have I will have a
requirements.<00:00:40.840><c> text</c><00:00:41.160><c> file</c><00:00:41.440><c> here</c><00:00:41.600><c> so</c><00:00:41.840><c> all</c><00:00:42.000><c> you</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
requirements. text file here so all you
 

00:00:42.079 --> 00:00:43.910 align:start position:0%
requirements. text file here so all you
have<00:00:42.160><c> to</c><00:00:42.280><c> type</c><00:00:42.440><c> in</c><00:00:42.559><c> in</c><00:00:42.680><c> your</c><00:00:42.800><c> terminal</c><00:00:43.480><c> is</c><00:00:43.680><c> PIP</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
have to type in in your terminal is PIP
 

00:00:43.920 --> 00:00:47.189 align:start position:0%
have to type in in your terminal is PIP
install<00:00:44.520><c> dasr</c><00:00:45.480><c> requirements.txt</c><00:00:46.480><c> okay</c>

00:00:47.189 --> 00:00:47.199 align:start position:0%
install dasr requirements.txt okay
 

00:00:47.199 --> 00:00:48.430 align:start position:0%
install dasr requirements.txt okay
there's<00:00:47.360><c> going</c><00:00:47.480><c> to</c><00:00:47.559><c> be</c><00:00:47.760><c> two</c><00:00:47.960><c> different</c><00:00:48.199><c> ways</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
there's going to be two different ways
 

00:00:48.440 --> 00:00:49.470 align:start position:0%
there's going to be two different ways
that<00:00:48.559><c> we're</c><00:00:48.680><c> going</c><00:00:48.800><c> to</c><00:00:48.920><c> use</c><00:00:49.120><c> this</c><00:00:49.280><c> I'm</c><00:00:49.360><c> going</c>

00:00:49.470 --> 00:00:49.480 align:start position:0%
that we're going to use this I'm going
 

00:00:49.480 --> 00:00:51.270 align:start position:0%
that we're going to use this I'm going
to<00:00:49.520><c> show</c><00:00:49.680><c> you</c><00:00:49.879><c> both</c><00:00:50.120><c> ways</c><00:00:50.520><c> the</c><00:00:50.640><c> first</c><00:00:50.920><c> way</c><00:00:51.120><c> is</c>

00:00:51.270 --> 00:00:51.280 align:start position:0%
to show you both ways the first way is
 

00:00:51.280 --> 00:00:52.950 align:start position:0%
to show you both ways the first way is
the<00:00:51.399><c> easiest</c><00:00:51.840><c> way</c><00:00:52.280><c> this</c><00:00:52.399><c> is</c><00:00:52.520><c> using</c><00:00:52.800><c> an</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
the easiest way this is using an
 

00:00:52.960 --> 00:00:55.389 align:start position:0%
the easiest way this is using an
inference<00:00:53.399><c> server</c><00:00:54.079><c> so</c><00:00:54.280><c> on</c><00:00:54.559><c> hugging</c><00:00:54.960><c> face</c><00:00:55.280><c> if</c>

00:00:55.389 --> 00:00:55.399 align:start position:0%
inference server so on hugging face if
 

00:00:55.399 --> 00:00:57.029 align:start position:0%
inference server so on hugging face if
you<00:00:55.520><c> go</c><00:00:55.760><c> to</c><00:00:55.960><c> the</c><00:00:56.079><c> model</c><00:00:56.359><c> whisper</c><00:00:56.760><c> large</c>

00:00:57.029 --> 00:00:57.039 align:start position:0%
you go to the model whisper large
 

00:00:57.039 --> 00:00:58.869 align:start position:0%
you go to the model whisper large
version<00:00:57.320><c> 3</c><00:00:57.920><c> on</c><00:00:58.039><c> the</c><00:00:58.199><c> right</c><00:00:58.399><c> hand</c><00:00:58.559><c> side</c><00:00:58.719><c> there's</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
version 3 on the right hand side there's
 

00:00:58.879 --> 00:01:01.950 align:start position:0%
version 3 on the right hand side there's
a<00:00:59.039><c> deploy</c><00:00:59.920><c> choose</c><00:01:00.160><c> inference</c><00:01:00.760><c> API</c><00:01:01.760><c> and</c><00:01:01.879><c> then</c>

00:01:01.950 --> 00:01:01.960 align:start position:0%
a deploy choose inference API and then
 

00:01:01.960 --> 00:01:03.830 align:start position:0%
a deploy choose inference API and then
it'll<00:01:02.120><c> come</c><00:01:02.239><c> up</c><00:01:02.399><c> with</c><00:01:02.519><c> this</c><00:01:02.680><c> dialogue</c><00:01:03.120><c> box</c><00:01:03.480><c> for</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
it'll come up with this dialogue box for
 

00:01:03.840 --> 00:01:05.149 align:start position:0%
it'll come up with this dialogue box for
python<00:01:04.199><c> which</c><00:01:04.320><c> we'll</c><00:01:04.479><c> be</c><00:01:04.600><c> using</c><00:01:04.920><c> we're</c><00:01:05.080><c> going</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
python which we'll be using we're going
 

00:01:05.159 --> 00:01:07.149 align:start position:0%
python which we'll be using we're going
to<00:01:05.239><c> have</c><00:01:05.360><c> the</c><00:01:05.439><c> API</c><00:01:05.840><c> URL</c><00:01:06.200><c> and</c><00:01:06.360><c> the</c><00:01:06.479><c> headers</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
to have the API URL and the headers
 

00:01:07.159 --> 00:01:08.390 align:start position:0%
to have the API URL and the headers
which<00:01:07.280><c> is</c><00:01:07.400><c> where</c><00:01:07.520><c> we're</c><00:01:07.680><c> going</c><00:01:07.759><c> to</c><00:01:07.920><c> send</c><00:01:08.200><c> the</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
which is where we're going to send the
 

00:01:08.400 --> 00:01:10.390 align:start position:0%
which is where we're going to send the
request<00:01:09.000><c> to</c><00:01:09.280><c> this</c><00:01:09.479><c> model</c><00:01:10.000><c> and</c><00:01:10.080><c> it's</c><00:01:10.200><c> going</c><00:01:10.280><c> to</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
request to this model and it's going to
 

00:01:10.400 --> 00:01:12.390 align:start position:0%
request to this model and it's going to
give<00:01:10.520><c> us</c><00:01:10.720><c> back</c><00:01:10.920><c> the</c><00:01:11.119><c> transcription</c><00:01:11.960><c> so</c><00:01:12.280><c> right</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
give us back the transcription so right
 

00:01:12.400 --> 00:01:13.830 align:start position:0%
give us back the transcription so right
here<00:01:12.520><c> you</c><00:01:12.600><c> can</c><00:01:12.720><c> see</c><00:01:12.880><c> it</c><00:01:12.960><c> says</c><00:01:13.159><c> output</c><00:01:13.520><c> equals</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
here you can see it says output equals
 

00:01:13.840 --> 00:01:16.030 align:start position:0%
here you can see it says output equals
query<00:01:14.320><c> and</c><00:01:14.400><c> then</c><00:01:14.560><c> they</c><00:01:14.799><c> give</c><00:01:15.159><c> the</c><00:01:15.320><c> Flack</c><00:01:15.680><c> file</c>

00:01:16.030 --> 00:01:16.040 align:start position:0%
query and then they give the Flack file
 

00:01:16.040 --> 00:01:16.789 align:start position:0%
query and then they give the Flack file
so<00:01:16.119><c> we're</c><00:01:16.240><c> going</c><00:01:16.320><c> to</c><00:01:16.400><c> go</c><00:01:16.439><c> ahead</c><00:01:16.560><c> and</c><00:01:16.640><c> do</c>

00:01:16.789 --> 00:01:16.799 align:start position:0%
so we're going to go ahead and do
 

00:01:16.799 --> 00:01:18.670 align:start position:0%
so we're going to go ahead and do
something<00:01:17.119><c> very</c><00:01:17.320><c> similar</c><00:01:17.680><c> to</c><00:01:17.840><c> this</c><00:01:18.280><c> now</c><00:01:18.560><c> back</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
something very similar to this now back
 

00:01:18.680 --> 00:01:21.510 align:start position:0%
something very similar to this now back
in<00:01:18.799><c> the</c><00:01:18.960><c> code</c><00:01:19.759><c> here</c><00:01:19.880><c> I</c><00:01:20.040><c> have</c><00:01:20.240><c> the</c><00:01:20.360><c> API</c><00:01:20.799><c> URL</c><00:01:21.400><c> and</c>

00:01:21.510 --> 00:01:21.520 align:start position:0%
in the code here I have the API URL and
 

00:01:21.520 --> 00:01:23.149 align:start position:0%
in the code here I have the API URL and
I<00:01:21.680><c> have</c><00:01:21.840><c> the</c><00:01:21.960><c> headers</c><00:01:22.640><c> you're</c><00:01:22.799><c> just</c><00:01:22.920><c> going</c><00:01:23.040><c> to</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
I have the headers you're just going to
 

00:01:23.159 --> 00:01:25.390 align:start position:0%
I have the headers you're just going to
have<00:01:23.360><c> your</c><00:01:23.640><c> Barr</c><00:01:24.079><c> token</c><00:01:24.479><c> here</c><00:01:24.960><c> and</c><00:01:25.200><c> I've</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
have your Barr token here and I've
 

00:01:25.400 --> 00:01:27.789 align:start position:0%
have your Barr token here and I've
literally<00:01:25.920><c> copied</c><00:01:26.439><c> the</c><00:01:26.600><c> same</c><00:01:26.960><c> exact</c><00:01:27.360><c> code</c>

00:01:27.789 --> 00:01:27.799 align:start position:0%
literally copied the same exact code
 

00:01:27.799 --> 00:01:29.990 align:start position:0%
literally copied the same exact code
okay<00:01:27.960><c> you</c><00:01:28.079><c> just</c><00:01:28.240><c> have</c><00:01:28.320><c> a</c><00:01:28.479><c> query</c><00:01:28.960><c> function</c><00:01:29.600><c> and</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
okay you just have a query function and
 

00:01:30.000 --> 00:01:32.670 align:start position:0%
okay you just have a query function and
I<00:01:30.159><c> call</c><00:01:30.439><c> that</c><00:01:30.680><c> except</c><00:01:31.159><c> this</c><00:01:31.360><c> AI</c><00:01:31.799><c> audio</c><00:01:32.360><c> is</c><00:01:32.560><c> what</c>

00:01:32.670 --> 00:01:32.680 align:start position:0%
I call that except this AI audio is what
 

00:01:32.680 --> 00:01:35.630 align:start position:0%
I call that except this AI audio is what
we<00:01:32.960><c> created</c><00:01:33.799><c> from</c><00:01:34.159><c> day</c><00:01:34.399><c> 11</c><00:01:35.040><c> the</c><00:01:35.200><c> text</c><00:01:35.439><c> to</c>

00:01:35.630 --> 00:01:35.640 align:start position:0%
we created from day 11 the text to
 

00:01:35.640 --> 00:01:36.950 align:start position:0%
we created from day 11 the text to
speech<00:01:35.960><c> directory</c><00:01:36.439><c> we're</c><00:01:36.560><c> going</c><00:01:36.680><c> to</c><00:01:36.759><c> use</c><00:01:36.840><c> an</c>

00:01:36.950 --> 00:01:36.960 align:start position:0%
speech directory we're going to use an
 

00:01:36.960 --> 00:01:38.230 align:start position:0%
speech directory we're going to use an
inference<00:01:37.360><c> server</c><00:01:37.759><c> and</c><00:01:37.880><c> it's</c><00:01:38.000><c> just</c><00:01:38.079><c> going</c><00:01:38.159><c> to</c>

00:01:38.230 --> 00:01:38.240 align:start position:0%
inference server and it's just going to
 

00:01:38.240 --> 00:01:40.230 align:start position:0%
inference server and it's just going to
be<00:01:38.399><c> us</c><00:01:38.479><c> the</c><00:01:38.640><c> transcription</c><00:01:39.479><c> let's</c><00:01:39.640><c> run</c><00:01:39.840><c> it</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
be us the transcription let's run it
 

00:01:40.240 --> 00:01:42.310 align:start position:0%
be us the transcription let's run it
okay<00:01:40.479><c> and</c><00:01:40.680><c> here</c><00:01:40.799><c> is</c><00:01:40.960><c> the</c><00:01:41.159><c> transcription</c><00:01:42.040><c> in</c><00:01:42.159><c> a</c>

00:01:42.310 --> 00:01:42.320 align:start position:0%
okay and here is the transcription in a
 

00:01:42.320 --> 00:01:44.709 align:start position:0%
okay and here is the transcription in a
worldwide<00:01:42.759><c> room</c><00:01:43.040><c> freely</c><00:01:43.560><c> this</c><00:01:43.680><c> is</c><00:01:44.159><c> the</c><00:01:44.320><c> audio</c>

00:01:44.709 --> 00:01:44.719 align:start position:0%
worldwide room freely this is the audio
 

00:01:44.719 --> 00:01:46.310 align:start position:0%
worldwide room freely this is the audio
file<00:01:45.040><c> that</c><00:01:45.159><c> we</c><00:01:45.320><c> tested</c><00:01:45.719><c> that</c><00:01:45.880><c> I</c><00:01:45.960><c> showed</c><00:01:46.159><c> you</c><00:01:46.240><c> in</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
file that we tested that I showed you in
 

00:01:46.320 --> 00:01:48.389 align:start position:0%
file that we tested that I showed you in
the<00:01:46.439><c> beginning</c><00:01:47.079><c> okay</c><00:01:47.200><c> so</c><00:01:47.439><c> this</c><00:01:47.560><c> is</c><00:01:48.040><c> just</c><00:01:48.200><c> the</c>

00:01:48.389 --> 00:01:48.399 align:start position:0%
the beginning okay so this is just the
 

00:01:48.399 --> 00:01:50.310 align:start position:0%
the beginning okay so this is just the
straight<00:01:48.840><c> transcription</c><00:01:49.680><c> of</c><00:01:49.840><c> that</c><00:01:49.960><c> audio</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
straight transcription of that audio
 

00:01:50.320 --> 00:01:52.469 align:start position:0%
straight transcription of that audio
file<00:01:51.040><c> really</c><00:01:51.360><c> simple</c><00:01:51.960><c> all</c><00:01:52.079><c> right</c><00:01:52.200><c> now</c><00:01:52.320><c> for</c>

00:01:52.469 --> 00:01:52.479 align:start position:0%
file really simple all right now for
 

00:01:52.479 --> 00:01:54.310 align:start position:0%
file really simple all right now for
this<00:01:52.759><c> next</c><00:01:53.040><c> bit</c><00:01:53.200><c> of</c><00:01:53.360><c> code</c><00:01:53.920><c> this</c><00:01:54.040><c> is</c><00:01:54.119><c> a</c><00:01:54.200><c> little</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
this next bit of code this is a little
 

00:01:54.320 --> 00:01:55.590 align:start position:0%
this next bit of code this is a little
bit<00:01:54.439><c> more</c><00:01:54.600><c> involved</c><00:01:55.000><c> right</c><00:01:55.119><c> so</c><00:01:55.280><c> now</c><00:01:55.399><c> we're</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
bit more involved right so now we're
 

00:01:55.600 --> 00:01:57.389 align:start position:0%
bit more involved right so now we're
using<00:01:56.320><c> uh</c><00:01:56.439><c> straight</c><00:01:56.759><c> code</c><00:01:57.000><c> while</c><00:01:57.119><c> we're</c><00:01:57.280><c> not</c>

00:01:57.389 --> 00:01:57.399 align:start position:0%
using uh straight code while we're not
 

00:01:57.399 --> 00:01:59.990 align:start position:0%
using uh straight code while we're not
using<00:01:57.759><c> any</c><00:01:58.079><c> API</c><00:01:58.600><c> requests</c><00:01:59.079><c> to</c><00:01:59.320><c> have</c><00:01:59.479><c> something</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
using any API requests to have something
 

00:02:00.000 --> 00:02:02.190 align:start position:0%
using any API requests to have something
done<00:02:00.240><c> for</c><00:02:00.479><c> us</c><00:02:00.799><c> on</c><00:02:00.920><c> a</c><00:02:01.119><c> different</c><00:02:01.360><c> server</c><00:02:02.000><c> so</c>

00:02:02.190 --> 00:02:02.200 align:start position:0%
done for us on a different server so
 

00:02:02.200 --> 00:02:03.789 align:start position:0%
done for us on a different server so
again<00:02:02.399><c> we</c><00:02:02.479><c> need</c><00:02:02.640><c> to</c><00:02:02.880><c> import</c><00:02:03.200><c> a</c><00:02:03.320><c> couple</c><00:02:03.560><c> more</c>

00:02:03.789 --> 00:02:03.799 align:start position:0%
again we need to import a couple more
 

00:02:03.799 --> 00:02:06.469 align:start position:0%
again we need to import a couple more
things<00:02:04.240><c> but</c><00:02:04.439><c> I</c><00:02:04.560><c> will</c><00:02:04.920><c> have</c><00:02:05.719><c> the</c><00:02:05.799><c> requirements.</c>

00:02:06.469 --> 00:02:06.479 align:start position:0%
things but I will have the requirements.
 

00:02:06.479 --> 00:02:08.070 align:start position:0%
things but I will have the requirements.
tech<00:02:06.719><c> so</c><00:02:06.920><c> you</c><00:02:07.079><c> already</c><00:02:07.360><c> have</c><00:02:07.560><c> these</c><00:02:07.799><c> when</c><00:02:07.920><c> you</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
tech so you already have these when you
 

00:02:08.080 --> 00:02:10.029 align:start position:0%
tech so you already have these when you
do<00:02:08.280><c> the</c><00:02:08.479><c> PIP</c><00:02:08.720><c> install</c><00:02:09.080><c> requirements</c><00:02:09.800><c> but</c><00:02:09.920><c> we</c>

00:02:10.029 --> 00:02:10.039 align:start position:0%
do the PIP install requirements but we
 

00:02:10.039 --> 00:02:12.430 align:start position:0%
do the PIP install requirements but we
choose<00:02:10.239><c> our</c><00:02:10.479><c> device</c><00:02:11.080><c> mine</c><00:02:11.280><c> will</c><00:02:11.400><c> be</c><00:02:11.520><c> a</c><00:02:11.640><c> CPU</c><00:02:12.319><c> we</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
choose our device mine will be a CPU we
 

00:02:12.440 --> 00:02:14.229 align:start position:0%
choose our device mine will be a CPU we
had<00:02:12.599><c> the</c><00:02:12.720><c> model</c><00:02:13.080><c> ID</c><00:02:13.520><c> which</c><00:02:13.640><c> is</c><00:02:13.760><c> the</c><00:02:13.879><c> large</c>

00:02:14.229 --> 00:02:14.239 align:start position:0%
had the model ID which is the large
 

00:02:14.239 --> 00:02:15.990 align:start position:0%
had the model ID which is the large
version<00:02:14.599><c> three</c><00:02:15.160><c> and</c><00:02:15.280><c> then</c><00:02:15.400><c> for</c><00:02:15.560><c> the</c><00:02:15.680><c> model</c>

00:02:15.990 --> 00:02:16.000 align:start position:0%
version three and then for the model
 

00:02:16.000 --> 00:02:17.910 align:start position:0%
version three and then for the model
we're<00:02:16.160><c> grabbing</c><00:02:16.519><c> the</c><00:02:16.680><c> sequence</c><00:02:17.080><c> to</c><00:02:17.280><c> sequence</c>

00:02:17.910 --> 00:02:17.920 align:start position:0%
we're grabbing the sequence to sequence
 

00:02:17.920 --> 00:02:20.390 align:start position:0%
we're grabbing the sequence to sequence
and<00:02:18.319><c> speech</c><00:02:18.720><c> to</c><00:02:19.000><c> text</c><00:02:19.280><c> modeling</c><00:02:20.120><c> and</c><00:02:20.239><c> then</c>

00:02:20.390 --> 00:02:20.400 align:start position:0%
and speech to text modeling and then
 

00:02:20.400 --> 00:02:21.589 align:start position:0%
and speech to text modeling and then
down<00:02:20.599><c> here</c><00:02:20.720><c> we're</c><00:02:20.920><c> grabbing</c><00:02:21.280><c> all</c><00:02:21.440><c> the</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
down here we're grabbing all the
 

00:02:21.599 --> 00:02:24.070 align:start position:0%
down here we're grabbing all the
pre-trained<00:02:22.319><c> vocabulary</c><00:02:23.120><c> from</c><00:02:23.519><c> this</c><00:02:23.800><c> whisper</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
pre-trained vocabulary from this whisper
 

00:02:24.080 --> 00:02:25.589 align:start position:0%
pre-trained vocabulary from this whisper
larger<00:02:24.319><c> version</c><00:02:24.560><c> 3</c><00:02:24.760><c> model</c><00:02:25.200><c> and</c><00:02:25.280><c> then</c><00:02:25.400><c> we</c><00:02:25.480><c> have</c>

00:02:25.589 --> 00:02:25.599 align:start position:0%
larger version 3 model and then we have
 

00:02:25.599 --> 00:02:27.670 align:start position:0%
larger version 3 model and then we have
the<00:02:25.720><c> pipeline</c><00:02:26.160><c> where</c><00:02:26.599><c> the</c><00:02:26.720><c> magic</c><00:02:27.040><c> happens</c><00:02:27.519><c> so</c>

00:02:27.670 --> 00:02:27.680 align:start position:0%
the pipeline where the magic happens so
 

00:02:27.680 --> 00:02:29.270 align:start position:0%
the pipeline where the magic happens so
this<00:02:27.800><c> is</c><00:02:27.920><c> going</c><00:02:28.040><c> to</c><00:02:28.120><c> be</c><00:02:28.280><c> automatic</c><00:02:28.959><c> speech</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
this is going to be automatic speech
 

00:02:29.280 --> 00:02:31.270 align:start position:0%
this is going to be automatic speech
recognition<00:02:30.000><c> you</c><00:02:30.160><c> might</c><00:02:30.400><c> see</c><00:02:30.640><c> the</c><00:02:30.760><c> acronym</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
recognition you might see the acronym
 

00:02:31.280 --> 00:02:34.110 align:start position:0%
recognition you might see the acronym
ASR<00:02:31.879><c> the</c><00:02:32.040><c> max</c><00:02:32.680><c> new</c><00:02:33.000><c> tokens</c><00:02:33.640><c> you</c><00:02:33.720><c> might</c><00:02:33.879><c> need</c><00:02:34.000><c> to</c>

00:02:34.110 --> 00:02:34.120 align:start position:0%
ASR the max new tokens you might need to
 

00:02:34.120 --> 00:02:35.710 align:start position:0%
ASR the max new tokens you might need to
change<00:02:34.440><c> this</c><00:02:34.599><c> if</c><00:02:34.680><c> you</c><00:02:34.959><c> if</c><00:02:35.040><c> you</c><00:02:35.160><c> think</c><00:02:35.319><c> it's</c><00:02:35.440><c> a</c>

00:02:35.710 --> 00:02:35.720 align:start position:0%
change this if you if you think it's a
 

00:02:35.720 --> 00:02:38.309 align:start position:0%
change this if you if you think it's a
really<00:02:36.080><c> long</c><00:02:36.480><c> audio</c><00:02:36.879><c> file</c><00:02:37.319><c> you</c><00:02:37.480><c> can</c><00:02:37.720><c> you</c><00:02:37.920><c> can</c>

00:02:38.309 --> 00:02:38.319 align:start position:0%
really long audio file you can you can
 

00:02:38.319 --> 00:02:40.229 align:start position:0%
really long audio file you can you can
return<00:02:38.720><c> timestamps</c><00:02:39.640><c> I'll</c><00:02:39.800><c> get</c><00:02:39.920><c> to</c><00:02:40.040><c> that</c><00:02:40.120><c> in</c>

00:02:40.229 --> 00:02:40.239 align:start position:0%
return timestamps I'll get to that in
 

00:02:40.239 --> 00:02:41.750 align:start position:0%
return timestamps I'll get to that in
just<00:02:40.360><c> a</c><00:02:40.480><c> second</c><00:02:40.920><c> and</c><00:02:41.000><c> then</c><00:02:41.120><c> we</c><00:02:41.200><c> have</c><00:02:41.319><c> the</c><00:02:41.440><c> pipe</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
just a second and then we have the pipe
 

00:02:41.760 --> 00:02:43.229 align:start position:0%
just a second and then we have the pipe
function<00:02:42.360><c> and</c><00:02:42.480><c> we</c><00:02:42.560><c> have</c><00:02:42.680><c> to</c><00:02:42.760><c> give</c><00:02:42.879><c> it</c><00:02:43.040><c> the</c>

00:02:43.229 --> 00:02:43.239 align:start position:0%
function and we have to give it the
 

00:02:43.239 --> 00:02:45.430 align:start position:0%
function and we have to give it the
input<00:02:43.680><c> which</c><00:02:43.800><c> is</c><00:02:44.040><c> the</c><00:02:44.200><c> audio</c><00:02:44.640><c> file</c><00:02:45.239><c> and</c><00:02:45.319><c> then</c>

00:02:45.430 --> 00:02:45.440 align:start position:0%
input which is the audio file and then
 

00:02:45.440 --> 00:02:47.790 align:start position:0%
input which is the audio file and then
for<00:02:45.560><c> the</c><00:02:45.720><c> generate</c><00:02:46.280><c> arguments</c><00:02:47.120><c> argument</c>

00:02:47.790 --> 00:02:47.800 align:start position:0%
for the generate arguments argument
 

00:02:47.800 --> 00:02:49.190 align:start position:0%
for the generate arguments argument
we're<00:02:47.959><c> going</c><00:02:48.080><c> to</c><00:02:48.159><c> give</c><00:02:48.280><c> it</c><00:02:48.440><c> the</c><00:02:48.599><c> language</c><00:02:49.040><c> as</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
we're going to give it the language as
 

00:02:49.200 --> 00:02:51.110 align:start position:0%
we're going to give it the language as
French<00:02:49.720><c> now</c><00:02:49.920><c> this</c><00:02:50.080><c> result</c><00:02:50.440><c> text</c><00:02:50.800><c> is</c><00:02:50.920><c> going</c><00:02:51.040><c> to</c>

00:02:51.110 --> 00:02:51.120 align:start position:0%
French now this result text is going to
 

00:02:51.120 --> 00:02:53.030 align:start position:0%
French now this result text is going to
give<00:02:51.239><c> a</c><00:02:51.440><c> straight</c><00:02:51.800><c> translation</c><00:02:52.760><c> and</c><00:02:52.879><c> the</c>

00:02:53.030 --> 00:02:53.040 align:start position:0%
give a straight translation and the
 

00:02:53.040 --> 00:02:54.830 align:start position:0%
give a straight translation and the
result<00:02:53.400><c> chunks</c><00:02:54.000><c> should</c><00:02:54.280><c> give</c><00:02:54.400><c> us</c><00:02:54.560><c> the</c>

00:02:54.830 --> 00:02:54.840 align:start position:0%
result chunks should give us the
 

00:02:54.840 --> 00:02:56.990 align:start position:0%
result chunks should give us the
timestamps<00:02:55.640><c> of</c><00:02:55.800><c> the</c><00:02:55.959><c> translation</c><00:02:56.640><c> let's</c><00:02:56.800><c> run</c>

00:02:56.990 --> 00:02:57.000 align:start position:0%
timestamps of the translation let's run
 

00:02:57.000 --> 00:02:59.470 align:start position:0%
timestamps of the translation let's run
it<00:02:57.360><c> and</c><00:02:57.440><c> so</c><00:02:57.640><c> here</c><00:02:57.800><c> is</c><00:02:58.000><c> the</c><00:02:58.239><c> translated</c><00:02:58.959><c> text</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
it and so here is the translated text
 

00:02:59.480 --> 00:03:02.030 align:start position:0%
it and so here is the translated text
this<00:02:59.599><c> is</c><00:02:59.800><c> is</c><00:02:59.920><c> from</c><00:03:00.080><c> the</c><00:03:00.239><c> print</c><00:03:00.800><c> result</c><00:03:01.319><c> text</c>

00:03:02.030 --> 00:03:02.040 align:start position:0%
this is is from the print result text
 

00:03:02.040 --> 00:03:04.750 align:start position:0%
this is is from the print result text
and<00:03:02.200><c> then</c><00:03:02.519><c> here</c><00:03:02.800><c> is</c><00:03:03.040><c> the</c><00:03:03.360><c> result</c><00:03:03.799><c> chunks</c><00:03:04.480><c> so</c><00:03:04.640><c> as</c>

00:03:04.750 --> 00:03:04.760 align:start position:0%
and then here is the result chunks so as
 

00:03:04.760 --> 00:03:07.430 align:start position:0%
and then here is the result chunks so as
you<00:03:04.879><c> see</c><00:03:05.040><c> it</c><00:03:05.159><c> chunks</c><00:03:05.680><c> out</c><00:03:06.080><c> all</c><00:03:06.280><c> the</c><00:03:06.519><c> timestamps</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
you see it chunks out all the timestamps
 

00:03:07.440 --> 00:03:11.229 align:start position:0%
you see it chunks out all the timestamps
for<00:03:07.920><c> the</c><00:03:08.080><c> translated</c><00:03:08.760><c> text</c><00:03:09.200><c> so</c><00:03:09.400><c> from</c><00:03:09.599><c> 0</c><00:03:10.000><c> to</c>

00:03:11.229 --> 00:03:11.239 align:start position:0%
for the translated text so from 0 to
 

00:03:11.239 --> 00:03:15.030 align:start position:0%
for the translated text so from 0 to
7.6<00:03:12.239><c> it</c><00:03:12.560><c> has</c><00:03:13.400><c> all</c><00:03:13.599><c> of</c><00:03:13.760><c> this</c><00:03:13.959><c> text</c><00:03:14.360><c> in</c><00:03:14.519><c> French</c>

00:03:15.030 --> 00:03:15.040 align:start position:0%
7.6 it has all of this text in French
 

00:03:15.040 --> 00:03:18.149 align:start position:0%
7.6 it has all of this text in French
and<00:03:15.159><c> then</c><00:03:15.319><c> from</c><00:03:15.519><c> 8.32</c><00:03:16.519><c> seconds</c><00:03:16.920><c> to</c><00:03:17.159><c> 15.9</c>

00:03:18.149 --> 00:03:18.159 align:start position:0%
and then from 8.32 seconds to 15.9
 

00:03:18.159 --> 00:03:20.830 align:start position:0%
and then from 8.32 seconds to 15.9
seconds<00:03:18.920><c> it</c><00:03:19.159><c> has</c><00:03:19.599><c> the</c><00:03:19.760><c> next</c><00:03:20.120><c> text</c><00:03:20.640><c> if</c><00:03:20.720><c> your</c>

00:03:20.830 --> 00:03:20.840 align:start position:0%
seconds it has the next text if your
 

00:03:20.840 --> 00:03:22.350 align:start position:0%
seconds it has the next text if your
machine<00:03:21.159><c> can</c><00:03:21.280><c> handle</c><00:03:21.519><c> it</c><00:03:21.680><c> this</c><00:03:21.760><c> will</c><00:03:21.879><c> run</c><00:03:22.159><c> just</c>

00:03:22.350 --> 00:03:22.360 align:start position:0%
machine can handle it this will run just
 

00:03:22.360 --> 00:03:24.670 align:start position:0%
machine can handle it this will run just
fine<00:03:22.640><c> loc</c><00:03:23.040><c> this</c><00:03:23.120><c> is</c><00:03:23.280><c> day</c><00:03:23.480><c> 13</c><00:03:24.239><c> thank</c><00:03:24.400><c> you</c><00:03:24.480><c> for</c>

00:03:24.670 --> 00:03:24.680 align:start position:0%
fine loc this is day 13 thank you for
 

00:03:24.680 --> 00:03:26.350 align:start position:0%
fine loc this is day 13 thank you for
watching<00:03:25.040><c> so</c><00:03:25.239><c> far</c><00:03:25.640><c> not</c><00:03:25.760><c> going</c><00:03:25.879><c> to</c><00:03:25.959><c> lie</c><00:03:26.120><c> to</c><00:03:26.200><c> you</c>

00:03:26.350 --> 00:03:26.360 align:start position:0%
watching so far not going to lie to you
 

00:03:26.360 --> 00:03:27.830 align:start position:0%
watching so far not going to lie to you
it's<00:03:26.480><c> a</c><00:03:26.560><c> little</c><00:03:26.680><c> bit</c><00:03:26.799><c> of</c><00:03:26.959><c> work</c><00:03:27.280><c> but</c><00:03:27.440><c> this</c><00:03:27.720><c> but</c>

00:03:27.830 --> 00:03:27.840 align:start position:0%
it's a little bit of work but this but
 

00:03:27.840 --> 00:03:29.390 align:start position:0%
it's a little bit of work but this but
this<00:03:27.920><c> is</c><00:03:28.080><c> another</c><00:03:28.480><c> building</c><00:03:28.840><c> block</c><00:03:29.159><c> or</c><00:03:29.319><c> a</c>

00:03:29.390 --> 00:03:29.400 align:start position:0%
this is another building block or a
 

00:03:29.400 --> 00:03:30.949 align:start position:0%
this is another building block or a
piece<00:03:29.720><c> to</c><00:03:29.840><c> the</c><00:03:29.920><c> puzzle</c><00:03:30.239><c> that</c><00:03:30.400><c> we</c><00:03:30.480><c> will</c><00:03:30.680><c> kind</c><00:03:30.799><c> of</c>

00:03:30.949 --> 00:03:30.959 align:start position:0%
piece to the puzzle that we will kind of
 

00:03:30.959 --> 00:03:32.789 align:start position:0%
piece to the puzzle that we will kind of
end<00:03:31.120><c> up</c><00:03:31.239><c> putting</c><00:03:31.640><c> together</c><00:03:32.280><c> where</c><00:03:32.480><c> you</c><00:03:32.560><c> can</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
end up putting together where you can
 

00:03:32.799 --> 00:03:35.270 align:start position:0%
end up putting together where you can
translate<00:03:33.560><c> an</c><00:03:33.760><c> audio</c><00:03:34.120><c> file</c><00:03:34.599><c> some</c><00:03:34.840><c> generated</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
translate an audio file some generated
 

00:03:35.280 --> 00:03:36.789 align:start position:0%
translate an audio file some generated
file<00:03:35.560><c> or</c><00:03:35.680><c> if</c><00:03:35.760><c> you</c><00:03:35.879><c> download</c><00:03:36.200><c> like</c><00:03:36.319><c> a</c><00:03:36.439><c> YouTube</c>

00:03:36.789 --> 00:03:36.799 align:start position:0%
file or if you download like a YouTube
 

00:03:36.799 --> 00:03:38.309 align:start position:0%
file or if you download like a YouTube
video<00:03:37.200><c> right</c><00:03:37.360><c> and</c><00:03:37.519><c> now</c><00:03:37.720><c> you</c><00:03:37.840><c> have</c><00:03:37.920><c> the</c><00:03:38.040><c> audio</c>

00:03:38.309 --> 00:03:38.319 align:start position:0%
video right and now you have the audio
 

00:03:38.319 --> 00:03:40.229 align:start position:0%
video right and now you have the audio
for<00:03:38.480><c> it</c><00:03:38.680><c> you</c><00:03:38.760><c> can</c><00:03:39.000><c> pass</c><00:03:39.239><c> that</c><00:03:39.400><c> into</c><00:03:39.760><c> here</c><00:03:40.120><c> and</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
for it you can pass that into here and
 

00:03:40.239 --> 00:03:42.030 align:start position:0%
for it you can pass that into here and
then<00:03:40.439><c> have</c><00:03:40.560><c> it</c><00:03:40.720><c> transcribe</c><00:03:41.439><c> and</c><00:03:41.560><c> then</c><00:03:41.720><c> maybe</c>

00:03:42.030 --> 00:03:42.040 align:start position:0%
then have it transcribe and then maybe
 

00:03:42.040 --> 00:03:43.589 align:start position:0%
then have it transcribe and then maybe
have<00:03:42.239><c> something</c><00:03:42.519><c> else</c><00:03:42.760><c> just</c><00:03:42.920><c> summarize</c><00:03:43.439><c> that</c>

00:03:43.589 --> 00:03:43.599 align:start position:0%
have something else just summarize that
 

00:03:43.599 --> 00:03:45.350 align:start position:0%
have something else just summarize that
for<00:03:43.760><c> you</c><00:03:44.080><c> or</c><00:03:44.319><c> if</c><00:03:44.439><c> you</c><00:03:44.680><c> or</c><00:03:44.799><c> if</c><00:03:44.879><c> your</c><00:03:45.080><c> company</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
for you or if you or if your company
 

00:03:45.360 --> 00:03:46.910 align:start position:0%
for you or if you or if your company
uses<00:03:45.640><c> something</c><00:03:45.879><c> like</c><00:03:46.080><c> Confluence</c><00:03:46.640><c> where</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
uses something like Confluence where
 

00:03:46.920 --> 00:03:48.390 align:start position:0%
uses something like Confluence where
everybody<00:03:47.239><c> puts</c><00:03:47.480><c> all</c><00:03:47.599><c> their</c><00:03:47.799><c> documents</c><00:03:48.200><c> it's</c>

00:03:48.390 --> 00:03:48.400 align:start position:0%
everybody puts all their documents it's
 

00:03:48.400 --> 00:03:50.149 align:start position:0%
everybody puts all their documents it's
just<00:03:48.959><c> a</c><00:03:49.080><c> web</c><00:03:49.280><c> platform</c><00:03:49.640><c> where</c><00:03:49.760><c> everybody</c><00:03:50.040><c> put</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
just a web platform where everybody put
 

00:03:50.159 --> 00:03:51.270 align:start position:0%
just a web platform where everybody put
their<00:03:50.280><c> documents</c><00:03:50.720><c> together</c><00:03:50.920><c> and</c><00:03:51.040><c> it's</c><00:03:51.159><c> kind</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
their documents together and it's kind
 

00:03:51.280 --> 00:03:53.030 align:start position:0%
their documents together and it's kind
of<00:03:51.400><c> organized</c><00:03:51.879><c> I</c><00:03:52.000><c> hope</c><00:03:52.159><c> to</c><00:03:52.239><c> be</c><00:03:52.400><c> anyways</c><00:03:52.879><c> but</c>

00:03:53.030 --> 00:03:53.040 align:start position:0%
of organized I hope to be anyways but
 

00:03:53.040 --> 00:03:54.869 align:start position:0%
of organized I hope to be anyways but
those<00:03:53.239><c> documents</c><00:03:53.560><c> need</c><00:03:53.799><c> translated</c><00:03:54.319><c> for</c>

00:03:54.869 --> 00:03:54.879 align:start position:0%
those documents need translated for
 

00:03:54.879 --> 00:03:56.550 align:start position:0%
those documents need translated for
different<00:03:55.200><c> people</c><00:03:55.959><c> this</c><00:03:56.079><c> is</c><00:03:56.159><c> the</c><00:03:56.319><c> way</c><00:03:56.439><c> that</c>

00:03:56.550 --> 00:03:56.560 align:start position:0%
different people this is the way that
 

00:03:56.560 --> 00:03:57.710 align:start position:0%
different people this is the way that
you<00:03:56.640><c> can</c><00:03:56.760><c> do</c><00:03:56.879><c> that</c><00:03:57.079><c> here's</c><00:03:57.239><c> some</c><00:03:57.360><c> more</c><00:03:57.480><c> videos</c>

00:03:57.710 --> 00:03:57.720 align:start position:0%
you can do that here's some more videos
 

00:03:57.720 --> 00:04:01.280 align:start position:0%
you can do that here's some more videos
on<00:03:57.840><c> autogen</c><00:03:58.400><c> I'll</c><00:03:58.519><c> see</c><00:03:58.640><c> you</c><00:03:58.840><c> next</c><00:03:59.000><c> video</c>

