WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.910 align:start position:0%
 
today's<00:00:00.359><c> day</c><00:00:00.520><c> 15</c><00:00:00.799><c> of</c><00:00:00.919><c> the</c><00:00:01.000><c> 31-day</c><00:00:01.560><c> challenge</c>

00:00:01.910 --> 00:00:01.920 align:start position:0%
today's day 15 of the 31-day challenge
 

00:00:01.920 --> 00:00:03.669 align:start position:0%
today's day 15 of the 31-day challenge
we're<00:00:02.159><c> about</c><00:00:02.600><c> halfway</c><00:00:03.240><c> and</c><00:00:03.360><c> today</c><00:00:03.560><c> we're</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
we're about halfway and today we're
 

00:00:03.679 --> 00:00:05.390 align:start position:0%
we're about halfway and today we're
going<00:00:03.800><c> to</c><00:00:03.879><c> go</c><00:00:04.000><c> over</c><00:00:04.160><c> our</c><00:00:04.400><c> last</c><00:00:04.680><c> hugging</c><00:00:05.120><c> face</c>

00:00:05.390 --> 00:00:05.400 align:start position:0%
going to go over our last hugging face
 

00:00:05.400 --> 00:00:07.070 align:start position:0%
going to go over our last hugging face
model<00:00:05.839><c> image</c><00:00:06.120><c> to</c><00:00:06.319><c> text</c><00:00:06.600><c> this</c><00:00:06.720><c> model</c><00:00:06.919><c> is</c><00:00:07.000><c> going</c>

00:00:07.070 --> 00:00:07.080 align:start position:0%
model image to text this model is going
 

00:00:07.080 --> 00:00:08.589 align:start position:0%
model image to text this model is going
to<00:00:07.200><c> take</c><00:00:07.319><c> an</c><00:00:07.439><c> image</c><00:00:07.720><c> that</c><00:00:07.839><c> we</c><00:00:08.000><c> had</c><00:00:08.160><c> previously</c>

00:00:08.589 --> 00:00:08.599 align:start position:0%
to take an image that we had previously
 

00:00:08.599 --> 00:00:10.589 align:start position:0%
to take an image that we had previously
created<00:00:09.000><c> from</c><00:00:09.160><c> the</c><00:00:09.360><c> text</c><00:00:09.599><c> to</c><00:00:09.800><c> image</c><00:00:10.080><c> model</c><00:00:10.480><c> and</c>

00:00:10.589 --> 00:00:10.599 align:start position:0%
created from the text to image model and
 

00:00:10.599 --> 00:00:12.150 align:start position:0%
created from the text to image model and
then<00:00:10.719><c> give</c><00:00:10.840><c> us</c><00:00:11.000><c> a</c><00:00:11.160><c> description</c><00:00:11.799><c> about</c><00:00:12.000><c> it</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
then give us a description about it
 

00:00:12.160 --> 00:00:13.749 align:start position:0%
then give us a description about it
here's<00:00:12.320><c> an</c><00:00:12.519><c> example</c><00:00:12.920><c> here's</c><00:00:13.080><c> a</c><00:00:13.200><c> Mario</c><00:00:13.519><c> image</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
here's an example here's a Mario image
 

00:00:13.759 --> 00:00:15.270 align:start position:0%
here's an example here's a Mario image
that<00:00:13.880><c> we</c><00:00:14.000><c> created</c><00:00:14.320><c> from</c><00:00:14.440><c> the</c><00:00:14.599><c> text</c><00:00:14.839><c> to</c><00:00:15.000><c> image</c>

00:00:15.270 --> 00:00:15.280 align:start position:0%
that we created from the text to image
 

00:00:15.280 --> 00:00:16.870 align:start position:0%
that we created from the text to image
model<00:00:15.719><c> and</c><00:00:15.839><c> then</c><00:00:16.039><c> we</c><00:00:16.160><c> run</c><00:00:16.320><c> that</c><00:00:16.480><c> through</c><00:00:16.680><c> the</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
model and then we run that through the
 

00:00:16.880 --> 00:00:18.870 align:start position:0%
model and then we run that through the
image<00:00:17.160><c> to</c><00:00:17.400><c> text</c><00:00:17.640><c> model</c><00:00:18.160><c> it</c><00:00:18.320><c> generates</c><00:00:18.720><c> the</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
image to text model it generates the
 

00:00:18.880 --> 00:00:20.509 align:start position:0%
image to text model it generates the
text<00:00:19.199><c> Mario</c><00:00:19.600><c> running</c><00:00:19.840><c> through</c><00:00:19.960><c> mushrooms</c><00:00:20.359><c> in</c>

00:00:20.509 --> 00:00:20.519 align:start position:0%
text Mario running through mushrooms in
 

00:00:20.519 --> 00:00:22.349 align:start position:0%
text Mario running through mushrooms in
a<00:00:20.600><c> forest</c><00:00:21.119><c> this</c><00:00:21.199><c> is</c><00:00:21.320><c> the</c><00:00:21.439><c> last</c><00:00:21.640><c> one</c><00:00:21.920><c> before</c><00:00:22.199><c> the</c>

00:00:22.349 --> 00:00:22.359 align:start position:0%
a forest this is the last one before the
 

00:00:22.359 --> 00:00:23.750 align:start position:0%
a forest this is the last one before the
next<00:00:22.560><c> video</c><00:00:22.840><c> where</c><00:00:22.960><c> we</c><00:00:23.119><c> put</c><00:00:23.279><c> some</c><00:00:23.400><c> of</c><00:00:23.519><c> these</c>

00:00:23.750 --> 00:00:23.760 align:start position:0%
next video where we put some of these
 

00:00:23.760 --> 00:00:25.189 align:start position:0%
next video where we put some of these
together<00:00:24.080><c> we've</c><00:00:24.240><c> already</c><00:00:24.400><c> gone</c><00:00:24.599><c> over</c><00:00:24.880><c> text</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
together we've already gone over text
 

00:00:25.199 --> 00:00:27.750 align:start position:0%
together we've already gone over text
image<00:00:25.760><c> a</c><00:00:25.920><c> text</c><00:00:26.160><c> to</c><00:00:26.400><c> speech</c><00:00:26.679><c> model</c><00:00:27.000><c> flow</c><00:00:27.519><c> text</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
image a text to speech model flow text
 

00:00:27.760 --> 00:00:29.589 align:start position:0%
image a text to speech model flow text
to<00:00:28.000><c> video</c><00:00:28.560><c> speech</c><00:00:28.920><c> recognition</c><00:00:29.400><c> with</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
to video speech recognition with
 

00:00:29.599 --> 00:00:31.029 align:start position:0%
to video speech recognition with
Whisperers<00:00:30.119><c> and</c><00:00:30.199><c> then</c><00:00:30.320><c> we</c><00:00:30.439><c> finally</c><00:00:30.720><c> created</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
Whisperers and then we finally created
 

00:00:31.039 --> 00:00:32.950 align:start position:0%
Whisperers and then we finally created
our<00:00:31.160><c> own</c><00:00:31.400><c> music</c><00:00:31.679><c> generation</c><00:00:32.360><c> and</c><00:00:32.559><c> now</c><00:00:32.840><c> the</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
our own music generation and now the
 

00:00:32.960 --> 00:00:34.549 align:start position:0%
our own music generation and now the
model<00:00:33.239><c> we're</c><00:00:33.360><c> going</c><00:00:33.440><c> to</c><00:00:33.520><c> go</c><00:00:33.640><c> over</c><00:00:33.879><c> today</c><00:00:34.440><c> we're</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
model we're going to go over today we're
 

00:00:34.559 --> 00:00:37.030 align:start position:0%
model we're going to go over today we're
going<00:00:34.800><c> over</c><00:00:35.040><c> a</c><00:00:35.239><c> Salesforce</c><00:00:35.960><c> model</c><00:00:36.600><c> just</c><00:00:36.879><c> last</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
going over a Salesforce model just last
 

00:00:37.040 --> 00:00:38.670 align:start position:0%
going over a Salesforce model just last
month<00:00:37.239><c> that</c><00:00:37.399><c> had</c><00:00:37.600><c> almost</c><00:00:37.960><c> three</c><00:00:38.160><c> qus</c><00:00:38.480><c> of</c><00:00:38.600><c> a</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
month that had almost three qus of a
 

00:00:38.680 --> 00:00:40.510 align:start position:0%
month that had almost three qus of a
million<00:00:39.040><c> downloads</c><00:00:39.680><c> and</c><00:00:39.840><c> we'll</c><00:00:40.000><c> be</c><00:00:40.120><c> using</c><00:00:40.320><c> an</c>

00:00:40.510 --> 00:00:40.520 align:start position:0%
million downloads and we'll be using an
 

00:00:40.520 --> 00:00:41.950 align:start position:0%
million downloads and we'll be using an
inference<00:00:40.920><c> server</c><00:00:41.280><c> instead</c><00:00:41.520><c> of</c><00:00:41.640><c> using</c>

00:00:41.950 --> 00:00:41.960 align:start position:0%
inference server instead of using
 

00:00:41.960 --> 00:00:44.350 align:start position:0%
inference server instead of using
Transformers<00:00:42.640><c> API</c><00:00:43.320><c> let's</c><00:00:43.559><c> get</c><00:00:43.719><c> started</c><00:00:44.160><c> as</c><00:00:44.280><c> I</c>

00:00:44.350 --> 00:00:44.360 align:start position:0%
Transformers API let's get started as I
 

00:00:44.360 --> 00:00:45.630 align:start position:0%
Transformers API let's get started as I
mentioned<00:00:44.680><c> we're</c><00:00:44.760><c> going</c><00:00:44.879><c> to</c><00:00:44.960><c> be</c><00:00:45.039><c> using</c><00:00:45.399><c> an</c>

00:00:45.630 --> 00:00:45.640 align:start position:0%
mentioned we're going to be using an
 

00:00:45.640 --> 00:00:47.350 align:start position:0%
mentioned we're going to be using an
image<00:00:45.960><c> to</c><00:00:46.120><c> text</c><00:00:46.320><c> model</c><00:00:46.600><c> from</c><00:00:46.760><c> Salesforce</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
image to text model from Salesforce
 

00:00:47.360 --> 00:00:49.270 align:start position:0%
image to text model from Salesforce
called<00:00:47.600><c> blip</c><00:00:47.960><c> image</c><00:00:48.399><c> captioning</c><00:00:48.800><c> large</c><00:00:49.160><c> and</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
called blip image captioning large and
 

00:00:49.280 --> 00:00:51.430 align:start position:0%
called blip image captioning large and
you<00:00:49.440><c> can</c><00:00:49.719><c> use</c><00:00:49.920><c> the</c><00:00:50.079><c> Transformers</c><00:00:50.680><c> API</c><00:00:51.280><c> just</c>

00:00:51.430 --> 00:00:51.440 align:start position:0%
you can use the Transformers API just
 

00:00:51.440 --> 00:00:53.349 align:start position:0%
you can use the Transformers API just
like<00:00:51.680><c> this</c><00:00:52.120><c> okay</c><00:00:52.320><c> you</c><00:00:52.399><c> can</c><00:00:52.520><c> certainly</c><00:00:52.840><c> do</c><00:00:53.039><c> this</c>

00:00:53.349 --> 00:00:53.359 align:start position:0%
like this okay you can certainly do this
 

00:00:53.359 --> 00:00:54.670 align:start position:0%
like this okay you can certainly do this
but<00:00:53.480><c> today</c><00:00:53.680><c> we're</c><00:00:53.800><c> going</c><00:00:53.920><c> to</c><00:00:54.000><c> be</c><00:00:54.120><c> using</c><00:00:54.480><c> the</c>

00:00:54.670 --> 00:00:54.680 align:start position:0%
but today we're going to be using the
 

00:00:54.680 --> 00:00:57.069 align:start position:0%
but today we're going to be using the
deploy<00:00:55.359><c> inference</c><00:00:55.800><c> server</c><00:00:56.160><c> API</c><00:00:56.840><c> so</c><00:00:56.960><c> you're</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
deploy inference server API so you're
 

00:00:57.079 --> 00:00:59.069 align:start position:0%
deploy inference server API so you're
going<00:00:57.199><c> to</c><00:00:57.359><c> choose</c><00:00:57.879><c> the</c><00:00:58.000><c> inference</c><00:00:58.559><c> API</c>

00:00:59.069 --> 00:00:59.079 align:start position:0%
going to choose the inference API
 

00:00:59.079 --> 00:01:01.150 align:start position:0%
going to choose the inference API
serverless<00:00:59.760><c> the</c><00:01:00.000><c> inference</c><00:01:00.480><c> endpoints</c>

00:01:01.150 --> 00:01:01.160 align:start position:0%
serverless the inference endpoints
 

00:01:01.160 --> 00:01:02.790 align:start position:0%
serverless the inference endpoints
dedicated<00:01:01.640><c> is</c><00:01:01.760><c> more</c><00:01:01.920><c> for</c><00:01:02.160><c> production</c><00:01:02.519><c> ready</c>

00:01:02.790 --> 00:01:02.800 align:start position:0%
dedicated is more for production ready
 

00:01:02.800 --> 00:01:04.270 align:start position:0%
dedicated is more for production ready
deployments<00:01:03.239><c> and</c><00:01:03.320><c> you</c><00:01:03.399><c> have</c><00:01:03.480><c> to</c><00:01:03.600><c> pay</c><00:01:03.719><c> for</c><00:01:03.879><c> that</c>

00:01:04.270 --> 00:01:04.280 align:start position:0%
deployments and you have to pay for that
 

00:01:04.280 --> 00:01:05.750 align:start position:0%
deployments and you have to pay for that
so<00:01:04.400><c> we're</c><00:01:04.519><c> going</c><00:01:04.600><c> to</c><00:01:04.720><c> use</c><00:01:04.839><c> the</c><00:01:05.000><c> serverless</c><00:01:05.640><c> so</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
so we're going to use the serverless so
 

00:01:05.760 --> 00:01:07.270 align:start position:0%
so we're going to use the serverless so
if<00:01:05.799><c> you</c><00:01:05.920><c> click</c><00:01:06.119><c> on</c><00:01:06.280><c> this</c><00:01:06.640><c> at</c><00:01:06.720><c> the</c><00:01:06.880><c> top</c><00:01:07.080><c> here</c><00:01:07.200><c> you</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
if you click on this at the top here you
 

00:01:07.280 --> 00:01:08.630 align:start position:0%
if you click on this at the top here you
want<00:01:07.400><c> to</c><00:01:07.520><c> create</c><00:01:07.720><c> your</c><00:01:07.799><c> own</c><00:01:08.040><c> token</c><00:01:08.400><c> and</c><00:01:08.560><c> you</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
want to create your own token and you
 

00:01:08.640 --> 00:01:09.670 align:start position:0%
want to create your own token and you
might<00:01:08.720><c> have</c><00:01:08.799><c> to</c><00:01:08.880><c> sign</c><00:01:09.040><c> up</c><00:01:09.119><c> and</c><00:01:09.200><c> log</c><00:01:09.400><c> in</c><00:01:09.520><c> if</c><00:01:09.600><c> you</c>

00:01:09.670 --> 00:01:09.680 align:start position:0%
might have to sign up and log in if you
 

00:01:09.680 --> 00:01:10.950 align:start position:0%
might have to sign up and log in if you
haven't<00:01:09.960><c> already</c><00:01:10.200><c> at</c><00:01:10.320><c> this</c><00:01:10.479><c> point</c><00:01:10.759><c> and</c><00:01:10.840><c> then</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
haven't already at this point and then
 

00:01:10.960 --> 00:01:12.990 align:start position:0%
haven't already at this point and then
we're<00:01:11.119><c> basically</c><00:01:11.479><c> going</c><00:01:11.600><c> to</c><00:01:11.799><c> be</c><00:01:12.040><c> copying</c><00:01:12.759><c> this</c>

00:01:12.990 --> 00:01:13.000 align:start position:0%
we're basically going to be copying this
 

00:01:13.000 --> 00:01:16.070 align:start position:0%
we're basically going to be copying this
code<00:01:13.759><c> and</c><00:01:13.880><c> using</c><00:01:14.280><c> this</c><00:01:14.640><c> get</c><00:01:14.799><c> your</c><00:01:14.960><c> API</c><00:01:15.400><c> token</c>

00:01:16.070 --> 00:01:16.080 align:start position:0%
code and using this get your API token
 

00:01:16.080 --> 00:01:18.390 align:start position:0%
code and using this get your API token
copy<00:01:16.439><c> this</c><00:01:16.759><c> and</c><00:01:16.840><c> then</c><00:01:17.080><c> paste</c><00:01:17.320><c> it</c><00:01:17.560><c> into</c><00:01:17.880><c> a</c><00:01:18.080><c> new</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
copy this and then paste it into a new
 

00:01:18.400 --> 00:01:20.510 align:start position:0%
copy this and then paste it into a new
python<00:01:18.840><c> file</c><00:01:19.280><c> copy</c><00:01:19.520><c> and</c><00:01:19.640><c> paste</c><00:01:19.920><c> the</c><00:01:20.040><c> code</c><00:01:20.360><c> I'll</c>

00:01:20.510 --> 00:01:20.520 align:start position:0%
python file copy and paste the code I'll
 

00:01:20.520 --> 00:01:22.230 align:start position:0%
python file copy and paste the code I'll
just<00:01:20.680><c> have</c><00:01:20.840><c> my</c><00:01:21.040><c> token</c><00:01:21.320><c> in</c><00:01:21.520><c> here</c><00:01:21.759><c> whenever</c><00:01:22.040><c> I</c><00:01:22.159><c> go</c>

00:01:22.230 --> 00:01:22.240 align:start position:0%
just have my token in here whenever I go
 

00:01:22.240 --> 00:01:24.310 align:start position:0%
just have my token in here whenever I go
to<00:01:22.360><c> run</c><00:01:22.600><c> this</c><00:01:22.880><c> we're</c><00:01:23.000><c> not</c><00:01:23.119><c> going</c><00:01:23.240><c> to</c><00:01:23.479><c> use</c><00:01:23.799><c> an</c><00:01:24.040><c> AI</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
to run this we're not going to use an AI
 

00:01:24.320 --> 00:01:25.710 align:start position:0%
to run this we're not going to use an AI
agent<00:01:24.600><c> here</c><00:01:24.799><c> because</c><00:01:25.280><c> we're</c><00:01:25.400><c> going</c><00:01:25.479><c> to</c><00:01:25.560><c> use</c>

00:01:25.710 --> 00:01:25.720 align:start position:0%
agent here because we're going to use
 

00:01:25.720 --> 00:01:27.870 align:start position:0%
agent here because we're going to use
that<00:01:25.840><c> in</c><00:01:25.920><c> the</c><00:01:26.079><c> next</c><00:01:26.400><c> video</c><00:01:27.119><c> for</c><00:01:27.360><c> the</c><00:01:27.560><c> build</c>

00:01:27.870 --> 00:01:27.880 align:start position:0%
that in the next video for the build
 

00:01:27.880 --> 00:01:29.429 align:start position:0%
that in the next video for the build
Workforce<00:01:28.439><c> number</c><00:01:28.680><c> two</c><00:01:29.000><c> which</c><00:01:29.119><c> is</c><00:01:29.240><c> where</c><00:01:29.360><c> we</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
Workforce number two which is where we
 

00:01:29.439 --> 00:01:31.429 align:start position:0%
Workforce number two which is where we
have<00:01:29.560><c> multiple</c><00:01:30.040><c> of</c><00:01:30.200><c> these</c><00:01:30.400><c> models</c><00:01:30.960><c> combined</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
have multiple of these models combined
 

00:01:31.439 --> 00:01:33.830 align:start position:0%
have multiple of these models combined
together<00:01:32.000><c> however</c><00:01:32.200><c> this</c><00:01:32.360><c> mario.png</c><00:01:33.079><c> this</c><00:01:33.720><c> is</c>

00:01:33.830 --> 00:01:33.840 align:start position:0%
together however this mario.png this is
 

00:01:33.840 --> 00:01:36.310 align:start position:0%
together however this mario.png this is
a<00:01:34.040><c> file</c><00:01:34.360><c> that</c><00:01:34.640><c> we</c><00:01:34.840><c> had</c><00:01:35.119><c> created</c><00:01:35.600><c> from</c><00:01:35.840><c> the</c><00:01:36.079><c> text</c>

00:01:36.310 --> 00:01:36.320 align:start position:0%
a file that we had created from the text
 

00:01:36.320 --> 00:01:38.429 align:start position:0%
a file that we had created from the text
to<00:01:36.560><c> image</c><00:01:36.880><c> from</c><00:01:37.119><c> day</c><00:01:37.280><c> 10</c><00:01:37.640><c> so</c><00:01:37.759><c> you</c><00:01:37.880><c> can</c><00:01:38.159><c> choose</c>

00:01:38.429 --> 00:01:38.439 align:start position:0%
to image from day 10 so you can choose
 

00:01:38.439 --> 00:01:40.469 align:start position:0%
to image from day 10 so you can choose
whatever<00:01:38.759><c> image</c><00:01:39.000><c> you</c><00:01:39.119><c> want</c><00:01:39.520><c> so</c><00:01:39.840><c> this</c><00:01:40.079><c> just</c>

00:01:40.469 --> 00:01:40.479 align:start position:0%
whatever image you want so this just
 

00:01:40.479 --> 00:01:42.710 align:start position:0%
whatever image you want so this just
opens<00:01:41.000><c> so</c><00:01:41.200><c> this</c><00:01:41.320><c> reads</c><00:01:41.640><c> the</c><00:01:41.840><c> bytes</c><00:01:42.320><c> from</c><00:01:42.520><c> a</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
opens so this reads the bytes from a
 

00:01:42.720 --> 00:01:45.190 align:start position:0%
opens so this reads the bytes from a
file<00:01:43.280><c> save</c><00:01:43.600><c> that</c><00:01:43.720><c> to</c><00:01:43.920><c> data</c><00:01:44.479><c> and</c><00:01:44.600><c> then</c><00:01:44.719><c> we</c><00:01:44.880><c> call</c>

00:01:45.190 --> 00:01:45.200 align:start position:0%
file save that to data and then we call
 

00:01:45.200 --> 00:01:48.310 align:start position:0%
file save that to data and then we call
request.<00:01:46.040><c> poost</c><00:01:46.360><c> and</c><00:01:46.479><c> we</c><00:01:46.680><c> take</c><00:01:46.920><c> this</c><00:01:47.439><c> API</c><00:01:47.880><c> URL</c>

00:01:48.310 --> 00:01:48.320 align:start position:0%
request. poost and we take this API URL
 

00:01:48.320 --> 00:01:50.310 align:start position:0%
request. poost and we take this API URL
here<00:01:48.600><c> from</c><00:01:48.840><c> hugging</c><00:01:49.240><c> face</c><00:01:49.960><c> give</c><00:01:50.079><c> it</c><00:01:50.240><c> the</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
here from hugging face give it the
 

00:01:50.320 --> 00:01:51.950 align:start position:0%
here from hugging face give it the
headers<00:01:50.759><c> which</c><00:01:50.880><c> is</c><00:01:51.040><c> essentially</c><00:01:51.399><c> your</c><00:01:51.600><c> Bearer</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
headers which is essentially your Bearer
 

00:01:51.960 --> 00:01:54.230 align:start position:0%
headers which is essentially your Bearer
token<00:01:52.600><c> and</c><00:01:52.680><c> then</c><00:01:52.840><c> the</c><00:01:53.000><c> data</c><00:01:53.360><c> which</c><00:01:53.560><c> is</c><00:01:54.040><c> the</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
token and then the data which is the
 

00:01:54.240 --> 00:01:56.389 align:start position:0%
token and then the data which is the
image<00:01:54.520><c> as</c><00:01:54.719><c> bytes</c><00:01:55.200><c> and</c><00:01:55.320><c> then</c><00:01:55.479><c> we</c><00:01:55.600><c> can</c><00:01:55.960><c> return</c>

00:01:56.389 --> 00:01:56.399 align:start position:0%
image as bytes and then we can return
 

00:01:56.399 --> 00:01:59.149 align:start position:0%
image as bytes and then we can return
the<00:01:56.600><c> Json</c><00:01:57.360><c> from</c><00:01:57.880><c> that</c><00:01:58.079><c> response</c><00:01:58.640><c> so</c><00:01:58.880><c> here</c><00:01:59.039><c> the</c>

00:01:59.149 --> 00:01:59.159 align:start position:0%
the Json from that response so here the
 

00:01:59.159 --> 00:02:00.749 align:start position:0%
the Json from that response so here the
output<00:01:59.439><c> is</c><00:01:59.560><c> equal</c><00:01:59.719><c> to</c><00:02:00.000><c> the</c><00:02:00.119><c> query</c><00:02:00.479><c> and</c><00:02:00.600><c> I</c>

00:02:00.749 --> 00:02:00.759 align:start position:0%
output is equal to the query and I
 

00:02:00.759 --> 00:02:02.910 align:start position:0%
output is equal to the query and I
hardcode<00:02:01.360><c> the</c><00:02:01.560><c> file</c><00:02:01.799><c> name</c><00:02:02.119><c> so</c><00:02:02.360><c> I</c><00:02:02.520><c> get</c><00:02:02.719><c> the</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
hardcode the file name so I get the
 

00:02:02.920 --> 00:02:04.149 align:start position:0%
hardcode the file name so I get the
first<00:02:03.159><c> item</c><00:02:03.399><c> in</c><00:02:03.479><c> the</c><00:02:03.600><c> array</c><00:02:03.880><c> which</c><00:02:03.960><c> is</c>

00:02:04.149 --> 00:02:04.159 align:start position:0%
first item in the array which is
 

00:02:04.159 --> 00:02:06.990 align:start position:0%
first item in the array which is
actually<00:02:04.399><c> only</c><00:02:04.640><c> one</c><00:02:05.399><c> and</c><00:02:05.560><c> then</c><00:02:06.159><c> the</c><00:02:06.479><c> property</c>

00:02:06.990 --> 00:02:07.000 align:start position:0%
actually only one and then the property
 

00:02:07.000 --> 00:02:09.109 align:start position:0%
actually only one and then the property
is<00:02:07.200><c> generated</c><00:02:07.719><c> text</c><00:02:08.080><c> so</c><00:02:08.280><c> this</c><00:02:08.399><c> will</c><00:02:08.759><c> just</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
is generated text so this will just
 

00:02:09.119 --> 00:02:10.949 align:start position:0%
is generated text so this will just
return<00:02:09.280><c> me</c><00:02:09.440><c> the</c><00:02:09.640><c> actual</c><00:02:10.119><c> text</c><00:02:10.479><c> instead</c><00:02:10.720><c> of</c><00:02:10.840><c> the</c>

00:02:10.949 --> 00:02:10.959 align:start position:0%
return me the actual text instead of the
 

00:02:10.959 --> 00:02:13.390 align:start position:0%
return me the actual text instead of the
whole<00:02:11.360><c> Json</c><00:02:11.879><c> and</c><00:02:11.959><c> when</c><00:02:12.080><c> we</c><00:02:12.200><c> run</c><00:02:12.480><c> this</c><00:02:13.160><c> because</c>

00:02:13.390 --> 00:02:13.400 align:start position:0%
whole Json and when we run this because
 

00:02:13.400 --> 00:02:15.270 align:start position:0%
whole Json and when we run this because
this<00:02:13.520><c> is</c><00:02:13.640><c> an</c><00:02:13.840><c> inference</c><00:02:14.360><c> server</c><00:02:14.959><c> it's</c><00:02:15.120><c> not</c>

00:02:15.270 --> 00:02:15.280 align:start position:0%
this is an inference server it's not
 

00:02:15.280 --> 00:02:17.190 align:start position:0%
this is an inference server it's not
actually<00:02:15.480><c> running</c><00:02:15.879><c> on</c><00:02:16.000><c> my</c><00:02:16.160><c> local</c><00:02:16.519><c> machine</c><00:02:17.120><c> it</c>

00:02:17.190 --> 00:02:17.200 align:start position:0%
actually running on my local machine it
 

00:02:17.200 --> 00:02:18.949 align:start position:0%
actually running on my local machine it
should<00:02:17.400><c> take</c><00:02:17.760><c> a</c><00:02:17.879><c> much</c><00:02:18.120><c> quicker</c><00:02:18.440><c> time</c><00:02:18.720><c> and</c><00:02:18.840><c> it</c>

00:02:18.949 --> 00:02:18.959 align:start position:0%
should take a much quicker time and it
 

00:02:18.959 --> 00:02:21.750 align:start position:0%
should take a much quicker time and it
does<00:02:19.480><c> so</c><00:02:19.879><c> here</c><00:02:20.160><c> is</c><00:02:20.360><c> the</c><00:02:20.599><c> array</c><00:02:20.879><c> of</c><00:02:21.000><c> Json</c><00:02:21.400><c> data</c>

00:02:21.750 --> 00:02:21.760 align:start position:0%
does so here is the array of Json data
 

00:02:21.760 --> 00:02:22.710 align:start position:0%
does so here is the array of Json data
and<00:02:21.840><c> then</c><00:02:22.000><c> this</c><00:02:22.080><c> is</c><00:02:22.200><c> just</c><00:02:22.319><c> the</c><00:02:22.440><c> actual</c>

00:02:22.710 --> 00:02:22.720 align:start position:0%
and then this is just the actual
 

00:02:22.720 --> 00:02:24.270 align:start position:0%
and then this is just the actual
generated<00:02:23.160><c> text</c><00:02:23.640><c> okay</c><00:02:23.800><c> and</c><00:02:23.920><c> I</c><00:02:24.040><c> have</c><00:02:24.160><c> a</c>

00:02:24.270 --> 00:02:24.280 align:start position:0%
generated text okay and I have a
 

00:02:24.280 --> 00:02:26.509 align:start position:0%
generated text okay and I have a
different<00:02:24.560><c> image</c><00:02:25.160><c> we</c><00:02:25.280><c> can</c><00:02:25.519><c> run</c><00:02:25.760><c> this</c><00:02:26.000><c> again</c><00:02:26.360><c> so</c>

00:02:26.509 --> 00:02:26.519 align:start position:0%
different image we can run this again so
 

00:02:26.519 --> 00:02:29.509 align:start position:0%
different image we can run this again so
I<00:02:26.599><c> just</c><00:02:26.720><c> change</c><00:02:27.040><c> the</c><00:02:27.200><c> name</c><00:02:27.400><c> to</c><00:02:27.680><c> sam.</c><00:02:28.440><c> PNG</c><00:02:29.239><c> okay</c>

00:02:29.509 --> 00:02:29.519 align:start position:0%
I just change the name to sam. PNG okay
 

00:02:29.519 --> 00:02:31.509 align:start position:0%
I just change the name to sam. PNG okay
this<00:02:29.959><c> is</c><00:02:30.200><c> actually</c><00:02:30.519><c> just</c><00:02:30.680><c> a</c><00:02:30.840><c> picture</c><00:02:31.080><c> of</c><00:02:31.239><c> Sam</c>

00:02:31.509 --> 00:02:31.519 align:start position:0%
this is actually just a picture of Sam
 

00:02:31.519 --> 00:02:34.070 align:start position:0%
this is actually just a picture of Sam
Alman<00:02:32.120><c> and</c><00:02:32.440><c> it</c><00:02:32.640><c> describes</c><00:02:33.080><c> it</c><00:02:33.319><c> as</c><00:02:33.720><c> there's</c><00:02:33.920><c> a</c>

00:02:34.070 --> 00:02:34.080 align:start position:0%
Alman and it describes it as there's a
 

00:02:34.080 --> 00:02:36.150 align:start position:0%
Alman and it describes it as there's a
man<00:02:34.319><c> in</c><00:02:34.440><c> a</c><00:02:34.640><c> suit</c><00:02:35.080><c> sitting</c><00:02:35.400><c> at</c><00:02:35.519><c> a</c><00:02:35.680><c> table</c><00:02:36.080><c> I</c>

00:02:36.150 --> 00:02:36.160 align:start position:0%
man in a suit sitting at a table I
 

00:02:36.160 --> 00:02:37.430 align:start position:0%
man in a suit sitting at a table I
thought<00:02:36.280><c> it</c><00:02:36.400><c> might</c><00:02:36.560><c> actually</c><00:02:36.760><c> give</c><00:02:36.920><c> the</c><00:02:37.160><c> name</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
thought it might actually give the name
 

00:02:37.440 --> 00:02:39.229 align:start position:0%
thought it might actually give the name
of<00:02:37.640><c> him</c><00:02:38.040><c> but</c><00:02:38.280><c> it</c><00:02:38.480><c> didn't</c><00:02:38.879><c> all</c><00:02:38.959><c> right</c><00:02:39.080><c> I'm</c><00:02:39.120><c> going</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
of him but it didn't all right I'm going
 

00:02:39.239 --> 00:02:41.110 align:start position:0%
of him but it didn't all right I'm going
to<00:02:39.319><c> try</c><00:02:39.480><c> one</c><00:02:39.640><c> more</c><00:02:39.959><c> this</c><00:02:40.080><c> is</c><00:02:40.239><c> called</c><00:02:40.480><c> george.</c>

00:02:41.110 --> 00:02:41.120 align:start position:0%
to try one more this is called george.
 

00:02:41.120 --> 00:02:43.350 align:start position:0%
to try one more this is called george.
PNG<00:02:41.800><c> I</c><00:02:41.920><c> see</c><00:02:42.120><c> what</c><00:02:42.200><c> it</c><00:02:42.400><c> describes</c><00:02:43.120><c> and</c><00:02:43.239><c> then</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
PNG I see what it describes and then
 

00:02:43.360 --> 00:02:44.910 align:start position:0%
PNG I see what it describes and then
I'll<00:02:43.480><c> show</c><00:02:43.640><c> you</c><00:02:43.800><c> the</c><00:02:43.920><c> image</c><00:02:44.440><c> okay</c><00:02:44.599><c> well</c><00:02:44.720><c> I</c><00:02:44.800><c> just</c>

00:02:44.910 --> 00:02:44.920 align:start position:0%
I'll show you the image okay well I just
 

00:02:44.920 --> 00:02:46.309 align:start position:0%
I'll show you the image okay well I just
took<00:02:45.120><c> this</c><00:02:45.400><c> I</c><00:02:45.480><c> just</c><00:02:45.599><c> took</c><00:02:45.800><c> this</c><00:02:45.959><c> picture</c><00:02:46.159><c> of</c>

00:02:46.309 --> 00:02:46.319 align:start position:0%
took this I just took this picture of
 

00:02:46.319 --> 00:02:48.070 align:start position:0%
took this I just took this picture of
George<00:02:46.640><c> Washington</c><00:02:47.280><c> the</c><00:02:47.400><c> portrait</c><00:02:47.720><c> of</c><00:02:47.800><c> a</c><00:02:47.879><c> man</c>

00:02:48.070 --> 00:02:48.080 align:start position:0%
George Washington the portrait of a man
 

00:02:48.080 --> 00:02:50.309 align:start position:0%
George Washington the portrait of a man
in<00:02:48.239><c> black</c><00:02:48.440><c> coat</c><00:02:48.640><c> and</c><00:02:48.760><c> white</c><00:02:49.080><c> collar</c><00:02:50.080><c> which</c><00:02:50.200><c> I</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
in black coat and white collar which I
 

00:02:50.319 --> 00:02:53.030 align:start position:0%
in black coat and white collar which I
mean<00:02:50.879><c> he</c><00:02:51.120><c> is</c><00:02:51.720><c> but</c><00:02:51.959><c> it</c><00:02:52.080><c> knew</c><00:02:52.319><c> Mario</c><00:02:52.800><c> but</c><00:02:52.920><c> it</c>

00:02:53.030 --> 00:02:53.040 align:start position:0%
mean he is but it knew Mario but it
 

00:02:53.040 --> 00:02:54.270 align:start position:0%
mean he is but it knew Mario but it
didn't<00:02:53.200><c> know</c><00:02:53.400><c> same</c><00:02:53.599><c> Alman</c><00:02:53.920><c> or</c><00:02:54.040><c> George</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
didn't know same Alman or George
 

00:02:54.280 --> 00:02:55.790 align:start position:0%
didn't know same Alman or George
Washington<00:02:54.920><c> that's</c><00:02:55.120><c> fine</c><00:02:55.319><c> I</c><00:02:55.400><c> just</c><00:02:55.519><c> picked</c><00:02:55.720><c> a</c>

00:02:55.790 --> 00:02:55.800 align:start position:0%
Washington that's fine I just picked a
 

00:02:55.800 --> 00:02:57.509 align:start position:0%
Washington that's fine I just picked a
model<00:02:56.080><c> there</c><00:02:56.200><c> might</c><00:02:56.360><c> be</c><00:02:56.760><c> better</c><00:02:57.000><c> models</c><00:02:57.360><c> out</c>

00:02:57.509 --> 00:02:57.519 align:start position:0%
model there might be better models out
 

00:02:57.519 --> 00:02:59.430 align:start position:0%
model there might be better models out
there<00:02:57.640><c> to</c><00:02:57.879><c> use</c><00:02:58.120><c> with</c><00:02:58.280><c> this</c><00:02:58.879><c> but</c><00:02:59.120><c> at</c><00:02:59.200><c> the</c><00:02:59.280><c> end</c><00:02:59.360><c> of</c>

00:02:59.430 --> 00:02:59.440 align:start position:0%
there to use with this but at the end of
 

00:02:59.440 --> 00:03:01.110 align:start position:0%
there to use with this but at the end of
the<00:02:59.519><c> day</c><00:02:59.640><c> it</c><00:02:59.920><c> did</c><00:03:00.080><c> describe</c><00:03:00.400><c> the</c><00:03:00.560><c> image</c><00:03:01.000><c> the</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
the day it did describe the image the
 

00:03:01.120 --> 00:03:03.190 align:start position:0%
the day it did describe the image the
way<00:03:01.239><c> it</c><00:03:01.400><c> looks</c><00:03:01.920><c> okay</c><00:03:02.040><c> this</c><00:03:02.159><c> was</c><00:03:02.280><c> a</c><00:03:02.440><c> quick</c><00:03:02.640><c> video</c>

00:03:03.190 --> 00:03:03.200 align:start position:0%
way it looks okay this was a quick video
 

00:03:03.200 --> 00:03:04.670 align:start position:0%
way it looks okay this was a quick video
and<00:03:03.360><c> this</c><00:03:03.480><c> is</c><00:03:03.640><c> probably</c><00:03:03.879><c> the</c><00:03:04.040><c> last</c><00:03:04.239><c> one</c><00:03:04.519><c> where</c>

00:03:04.670 --> 00:03:04.680 align:start position:0%
and this is probably the last one where
 

00:03:04.680 --> 00:03:06.149 align:start position:0%
and this is probably the last one where
we<00:03:04.799><c> use</c><00:03:04.959><c> a</c><00:03:05.080><c> model</c><00:03:05.319><c> from</c><00:03:05.519><c> hugging</c><00:03:05.840><c> face</c><00:03:06.000><c> and</c><00:03:06.080><c> I</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
we use a model from hugging face and I
 

00:03:06.159 --> 00:03:07.789 align:start position:0%
we use a model from hugging face and I
show<00:03:06.319><c> you</c><00:03:06.519><c> how</c><00:03:06.680><c> to</c><00:03:07.080><c> after</c><00:03:07.400><c> today</c><00:03:07.599><c> we're</c><00:03:07.720><c> going</c>

00:03:07.789 --> 00:03:07.799 align:start position:0%
show you how to after today we're going
 

00:03:07.799 --> 00:03:09.229 align:start position:0%
show you how to after today we're going
to<00:03:07.920><c> start</c><00:03:08.159><c> putting</c><00:03:08.519><c> things</c><00:03:08.879><c> together</c><00:03:09.120><c> and</c>

00:03:09.229 --> 00:03:09.239 align:start position:0%
to start putting things together and
 

00:03:09.239 --> 00:03:10.869 align:start position:0%
to start putting things together and
we'll<00:03:09.360><c> learn</c><00:03:09.799><c> other</c><00:03:10.080><c> Integrations</c><00:03:10.680><c> that</c><00:03:10.799><c> we</c>

00:03:10.869 --> 00:03:10.879 align:start position:0%
we'll learn other Integrations that we
 

00:03:10.879 --> 00:03:12.589 align:start position:0%
we'll learn other Integrations that we
can<00:03:11.040><c> use</c><00:03:11.239><c> with</c><00:03:11.360><c> autogen</c><00:03:12.080><c> I</c><00:03:12.159><c> know</c><00:03:12.280><c> we</c><00:03:12.400><c> didn't</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
can use with autogen I know we didn't
 

00:03:12.599 --> 00:03:14.550 align:start position:0%
can use with autogen I know we didn't
use<00:03:12.920><c> autogen</c><00:03:13.400><c> to</c><00:03:13.560><c> create</c><00:03:13.799><c> AI</c><00:03:14.040><c> agents</c><00:03:14.400><c> this</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
use autogen to create AI agents this
 

00:03:14.560 --> 00:03:16.229 align:start position:0%
use autogen to create AI agents this
time<00:03:14.920><c> but</c><00:03:15.080><c> the</c><00:03:15.200><c> next</c><00:03:15.440><c> video</c><00:03:15.879><c> we're</c><00:03:16.040><c> going</c><00:03:16.159><c> to</c>

00:03:16.229 --> 00:03:16.239 align:start position:0%
time but the next video we're going to
 

00:03:16.239 --> 00:03:18.190 align:start position:0%
time but the next video we're going to
have<00:03:16.360><c> a</c><00:03:16.480><c> few</c><00:03:16.680><c> of</c><00:03:16.840><c> them</c><00:03:17.239><c> with</c><00:03:17.480><c> function</c><00:03:17.840><c> calling</c>

00:03:18.190 --> 00:03:18.200 align:start position:0%
have a few of them with function calling
 

00:03:18.200 --> 00:03:19.430 align:start position:0%
have a few of them with function calling
if<00:03:18.280><c> you</c><00:03:18.360><c> have</c><00:03:18.480><c> any</c><00:03:18.599><c> questions</c><00:03:18.840><c> or</c><00:03:19.000><c> comments</c>

00:03:19.430 --> 00:03:19.440 align:start position:0%
if you have any questions or comments
 

00:03:19.440 --> 00:03:21.509 align:start position:0%
if you have any questions or comments
please<00:03:19.720><c> leave</c><00:03:19.920><c> them</c><00:03:20.120><c> down</c><00:03:20.319><c> below</c><00:03:20.959><c> remember</c><00:03:21.360><c> I</c>

00:03:21.509 --> 00:03:21.519 align:start position:0%
please leave them down below remember I
 

00:03:21.519 --> 00:03:23.070 align:start position:0%
please leave them down below remember I
have<00:03:21.720><c> a</c><00:03:21.840><c> free</c><00:03:22.080><c> newsletter</c><00:03:22.599><c> that</c><00:03:22.680><c> you</c><00:03:22.760><c> can</c><00:03:22.879><c> sign</c>

00:03:23.070 --> 00:03:23.080 align:start position:0%
have a free newsletter that you can sign
 

00:03:23.080 --> 00:03:24.789 align:start position:0%
have a free newsletter that you can sign
up<00:03:23.200><c> for</c><00:03:23.440><c> in</c><00:03:23.560><c> the</c><00:03:23.720><c> description</c><00:03:24.360><c> comes</c><00:03:24.560><c> out</c>

00:03:24.789 --> 00:03:24.799 align:start position:0%
up for in the description comes out
 

00:03:24.799 --> 00:03:26.670 align:start position:0%
up for in the description comes out
every<00:03:25.040><c> Sunday</c><00:03:25.360><c> at</c><00:03:25.480><c> noon</c><00:03:26.040><c> so</c><00:03:26.159><c> we</c><00:03:26.239><c> got</c><00:03:26.400><c> another</c>

00:03:26.670 --> 00:03:26.680 align:start position:0%
every Sunday at noon so we got another
 

00:03:26.680 --> 00:03:29.110 align:start position:0%
every Sunday at noon so we got another
halfway<00:03:27.040><c> to</c><00:03:27.280><c> go</c><00:03:28.120><c> it</c><00:03:28.360><c> it</c><00:03:28.439><c> won't</c><00:03:28.599><c> be</c><00:03:28.760><c> long</c><00:03:28.920><c> before</c>

00:03:29.110 --> 00:03:29.120 align:start position:0%
halfway to go it it won't be long before
 

00:03:29.120 --> 00:03:30.949 align:start position:0%
halfway to go it it won't be long before
I<00:03:29.200><c> start</c><00:03:29.360><c> opening</c><00:03:29.599><c> up</c><00:03:29.879><c> a</c><00:03:30.080><c> Discord</c><00:03:30.519><c> it</c><00:03:30.640><c> might</c><00:03:30.799><c> be</c>

00:03:30.949 --> 00:03:30.959 align:start position:0%
I start opening up a Discord it might be
 

00:03:30.959 --> 00:03:32.190 align:start position:0%
I start opening up a Discord it might be
easier<00:03:31.319><c> for</c><00:03:31.480><c> us</c><00:03:31.599><c> to</c><00:03:31.680><c> interact</c><00:03:32.000><c> with</c><00:03:32.120><c> each</c>

00:03:32.190 --> 00:03:32.200 align:start position:0%
easier for us to interact with each
 

00:03:32.200 --> 00:03:33.789 align:start position:0%
easier for us to interact with each
other<00:03:32.439><c> so</c><00:03:32.599><c> that</c><00:03:33.040><c> we</c><00:03:33.159><c> can</c><00:03:33.319><c> also</c><00:03:33.519><c> build</c><00:03:33.680><c> a</c>

00:03:33.789 --> 00:03:33.799 align:start position:0%
other so that we can also build a
 

00:03:33.799 --> 00:03:36.589 align:start position:0%
other so that we can also build a
community<00:03:34.319><c> that</c><00:03:34.560><c> has</c><00:03:35.319><c> uh</c><00:03:35.519><c> we</c><00:03:35.640><c> can</c><00:03:36.080><c> have</c><00:03:36.360><c> best</c>

00:03:36.589 --> 00:03:36.599 align:start position:0%
community that has uh we can have best
 

00:03:36.599 --> 00:03:38.350 align:start position:0%
community that has uh we can have best
practices<00:03:37.200><c> and</c><00:03:37.360><c> we</c><00:03:37.480><c> have</c><00:03:37.599><c> all</c><00:03:37.760><c> these</c><00:03:37.959><c> prompts</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
practices and we have all these prompts
 

00:03:38.360 --> 00:03:40.190 align:start position:0%
practices and we have all these prompts
that<00:03:38.519><c> other</c><00:03:38.680><c> people</c><00:03:38.879><c> can</c><00:03:39.120><c> use</c><00:03:39.680><c> and</c><00:03:39.840><c> other</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
that other people can use and other
 

00:03:40.200 --> 00:03:42.190 align:start position:0%
that other people can use and other
ideas<00:03:40.720><c> that</c><00:03:40.840><c> we</c><00:03:41.040><c> kind</c><00:03:41.120><c> of</c><00:03:41.280><c> put</c><00:03:41.599><c> together</c><00:03:41.879><c> and</c>

00:03:42.190 --> 00:03:42.200 align:start position:0%
ideas that we kind of put together and
 

00:03:42.200 --> 00:03:43.830 align:start position:0%
ideas that we kind of put together and
we<00:03:42.280><c> can</c><00:03:42.400><c> just</c><00:03:42.519><c> all</c><00:03:42.640><c> share</c><00:03:42.959><c> them</c><00:03:43.360><c> right</c><00:03:43.680><c> thank</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
we can just all share them right thank
 

00:03:43.840 --> 00:03:47.480 align:start position:0%
we can just all share them right thank
you<00:03:43.920><c> for</c><00:03:44.120><c> watching</c><00:03:44.480><c> I'll</c><00:03:44.640><c> see</c><00:03:44.799><c> you</c><00:03:44.959><c> next</c><00:03:45.159><c> video</c>

