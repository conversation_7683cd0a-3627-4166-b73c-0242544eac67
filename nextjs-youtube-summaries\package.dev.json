{"name": "youtube-summaries-dev-scripts", "version": "1.0.0", "description": "Development scripts for YouTube Summaries application", "scripts": {"dev": "concurrently \"npm run dev:next\" \"npm run dev:python\"", "dev:next": "next dev", "dev:python": "cd python-service && python main.py", "build": "next build", "start": "next start", "lint": "next lint", "python:install": "cd python-service && pip install -r requirements.txt", "db:setup": "echo 'Please run the database/schema.sql file in your Supabase SQL editor'", "test": "npm run test:next && npm run test:python", "test:next": "jest", "test:python": "cd python-service && python -m pytest", "docker:build": "docker build -t youtube-summaries .", "docker:run": "docker run -p 3000:3000 -p 8000:8000 youtube-summaries"}, "devDependencies": {"concurrently": "^8.2.2"}}