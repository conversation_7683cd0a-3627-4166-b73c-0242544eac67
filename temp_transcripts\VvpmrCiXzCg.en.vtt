WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.470 align:start position:0%
 
[Music]

00:00:05.470 --> 00:00:05.480 align:start position:0%
 
 

00:00:05.480 --> 00:00:07.510 align:start position:0%
 
in<00:00:05.600><c> the</c><00:00:05.759><c> previous</c><00:00:06.120><c> videos</c><00:00:06.680><c> we</c><00:00:06.839><c> build</c><00:00:07.120><c> a</c><00:00:07.279><c> very</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
in the previous videos we build a very
 

00:00:07.520 --> 00:00:10.910 align:start position:0%
in the previous videos we build a very
simple<00:00:08.280><c> application</c><00:00:09.120><c> using</c><00:00:09.480><c> L</c><00:00:09.800><c> chain</c><00:00:10.519><c> and</c><00:00:10.719><c> we</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
simple application using L chain and we
 

00:00:10.920 --> 00:00:13.509 align:start position:0%
simple application using L chain and we
expose<00:00:11.360><c> it</c><00:00:11.719><c> on</c><00:00:11.880><c> the</c><00:00:12.080><c> web</c><00:00:12.400><c> using</c>

00:00:13.509 --> 00:00:13.519 align:start position:0%
expose it on the web using
 

00:00:13.519 --> 00:00:16.870 align:start position:0%
expose it on the web using
radio<00:00:14.519><c> as</c><00:00:14.639><c> you</c><00:00:14.799><c> develop</c><00:00:15.240><c> your</c><00:00:15.760><c> application</c><00:00:16.760><c> uh</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
radio as you develop your application uh
 

00:00:16.880 --> 00:00:19.470 align:start position:0%
radio as you develop your application uh
you<00:00:17.000><c> will</c><00:00:17.320><c> need</c><00:00:17.520><c> to</c><00:00:17.680><c> move</c><00:00:18.160><c> Beyond</c><00:00:18.960><c> uh</c><00:00:19.199><c> the</c>

00:00:19.470 --> 00:00:19.480 align:start position:0%
you will need to move Beyond uh the
 

00:00:19.480 --> 00:00:21.790 align:start position:0%
you will need to move Beyond uh the
scale<00:00:19.920><c> of</c><00:00:20.039><c> the</c><00:00:20.199><c> example</c><00:00:20.640><c> that</c><00:00:20.760><c> we</c><00:00:20.960><c> presented</c>

00:00:21.790 --> 00:00:21.800 align:start position:0%
scale of the example that we presented
 

00:00:21.800 --> 00:00:24.230 align:start position:0%
scale of the example that we presented
in<00:00:22.000><c> this</c><00:00:22.199><c> lesson</c><00:00:23.119><c> and</c><00:00:23.359><c> to</c><00:00:23.640><c> give</c><00:00:23.800><c> you</c><00:00:24.000><c> some</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
in this lesson and to give you some
 

00:00:24.240 --> 00:00:26.669 align:start position:0%
in this lesson and to give you some
inspiration<00:00:25.119><c> we</c><00:00:25.240><c> are</c><00:00:25.480><c> also</c><00:00:25.720><c> sharing</c><00:00:26.439><c> and</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
inspiration we are also sharing and
 

00:00:26.679 --> 00:00:29.429 align:start position:0%
inspiration we are also sharing and
we've<00:00:26.920><c> open</c><00:00:27.279><c> sourced</c><00:00:27.880><c> the</c><00:00:28.240><c> onebot</c><00:00:29.160><c> that</c><00:00:29.320><c> we</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
we've open sourced the onebot that we
 

00:00:29.439 --> 00:00:32.670 align:start position:0%
we've open sourced the onebot that we
are<00:00:29.599><c> using</c><00:00:30.400><c> um</c><00:00:30.840><c> internally</c><00:00:31.759><c> with</c><00:00:32.040><c> we</c><00:00:32.239><c> Andes</c>

00:00:32.670 --> 00:00:32.680 align:start position:0%
are using um internally with we Andes
 

00:00:32.680 --> 00:00:35.229 align:start position:0%
are using um internally with we Andes
employees<00:00:33.480><c> and</c><00:00:33.640><c> that</c><00:00:33.800><c> we</c><00:00:34.040><c> also</c><00:00:34.680><c> exposed</c>

00:00:35.229 --> 00:00:35.239 align:start position:0%
employees and that we also exposed
 

00:00:35.239 --> 00:00:37.630 align:start position:0%
employees and that we also exposed
externally<00:00:36.040><c> on</c><00:00:36.239><c> our</c><00:00:36.480><c> Discord</c><00:00:36.879><c> server</c><00:00:37.480><c> and</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
externally on our Discord server and
 

00:00:37.640 --> 00:00:39.630 align:start position:0%
externally on our Discord server and
where<00:00:37.760><c> you</c><00:00:37.879><c> can</c><00:00:38.120><c> go</c><00:00:38.440><c> and</c><00:00:38.600><c> play</c><00:00:38.879><c> and</c><00:00:39.160><c> interact</c>

00:00:39.630 --> 00:00:39.640 align:start position:0%
where you can go and play and interact
 

00:00:39.640 --> 00:00:42.470 align:start position:0%
where you can go and play and interact
with<00:00:39.760><c> it</c><00:00:40.680><c> so</c><00:00:40.840><c> you</c><00:00:40.920><c> can</c><00:00:41.120><c> find</c><00:00:41.399><c> this</c><00:00:41.879><c> um</c>

00:00:42.470 --> 00:00:42.480 align:start position:0%
with it so you can find this um
 

00:00:42.480 --> 00:00:46.709 align:start position:0%
with it so you can find this um
application<00:00:43.120><c> under</c><00:00:43.840><c> 1db</c><00:00:45.320><c> onebot</c><00:00:46.320><c> and</c><00:00:46.559><c> here</c>

00:00:46.709 --> 00:00:46.719 align:start position:0%
application under 1db onebot and here
 

00:00:46.719 --> 00:00:49.110 align:start position:0%
application under 1db onebot and here
you<00:00:46.840><c> can</c><00:00:47.039><c> find</c><00:00:47.360><c> all</c><00:00:47.480><c> of</c><00:00:47.600><c> the</c><00:00:47.719><c> source</c><00:00:48.039><c> files</c><00:00:48.960><c> uh</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
you can find all of the source files uh
 

00:00:49.120 --> 00:00:51.630 align:start position:0%
you can find all of the source files uh
you<00:00:49.239><c> can</c><00:00:49.600><c> uh</c><00:00:49.680><c> see</c><00:00:49.960><c> documentation</c><00:00:50.719><c> on</c><00:00:50.920><c> how</c><00:00:51.199><c> this</c>

00:00:51.630 --> 00:00:51.640 align:start position:0%
you can uh see documentation on how this
 

00:00:51.640 --> 00:00:54.229 align:start position:0%
you can uh see documentation on how this
uh<00:00:52.000><c> bort</c><00:00:52.320><c> was</c><00:00:52.559><c> developed</c><00:00:53.359><c> you'll</c><00:00:53.600><c> be</c><00:00:53.719><c> able</c><00:00:54.039><c> to</c>

00:00:54.229 --> 00:00:54.239 align:start position:0%
uh bort was developed you'll be able to
 

00:00:54.239 --> 00:00:56.029 align:start position:0%
uh bort was developed you'll be able to
see<00:00:54.559><c> both</c><00:00:54.800><c> the</c><00:00:54.960><c> slack</c><00:00:55.359><c> and</c><00:00:55.520><c> the</c><00:00:55.680><c> Discord</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
see both the slack and the Discord
 

00:00:56.039 --> 00:00:59.229 align:start position:0%
see both the slack and the Discord
client<00:00:56.559><c> implementation</c><00:00:57.559><c> in</c><00:00:57.760><c> this</c><00:00:58.120><c> repo</c><00:00:59.120><c> you</c>

00:00:59.229 --> 00:00:59.239 align:start position:0%
client implementation in this repo you
 

00:00:59.239 --> 00:01:02.430 align:start position:0%
client implementation in this repo you
can<00:00:59.480><c> go</c><00:01:00.199><c> and</c><00:01:00.440><c> review</c><00:01:00.760><c> the</c><00:01:00.879><c> source</c><00:01:01.239><c> files</c><00:01:02.239><c> uh</c>

00:01:02.430 --> 00:01:02.440 align:start position:0%
can go and review the source files uh
 

00:01:02.440 --> 00:01:04.429 align:start position:0%
can go and review the source files uh
which<00:01:02.559><c> is</c><00:01:02.680><c> a</c><00:01:02.840><c> bit</c><00:01:03.000><c> more</c><00:01:03.320><c> comprehensive</c><00:01:04.280><c> than</c>

00:01:04.429 --> 00:01:04.439 align:start position:0%
which is a bit more comprehensive than
 

00:01:04.439 --> 00:01:07.350 align:start position:0%
which is a bit more comprehensive than
the<00:01:04.600><c> examples</c><00:01:05.320><c> we</c><00:01:05.479><c> covered</c><00:01:06.000><c> in</c><00:01:06.240><c> the</c><00:01:06.680><c> in</c><00:01:06.799><c> the</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
the examples we covered in the in the
 

00:01:07.360 --> 00:01:10.350 align:start position:0%
the examples we covered in the in the
lesson<00:01:08.360><c> and</c><00:01:08.840><c> I</c><00:01:09.000><c> encourage</c><00:01:09.400><c> you</c><00:01:09.560><c> to</c><00:01:09.960><c> to</c><00:01:10.119><c> play</c>

00:01:10.350 --> 00:01:10.360 align:start position:0%
lesson and I encourage you to to play
 

00:01:10.360 --> 00:01:12.390 align:start position:0%
lesson and I encourage you to to play
with<00:01:10.520><c> this</c><00:01:10.840><c> uh</c><00:01:10.960><c> onebot</c><00:01:11.720><c> if</c><00:01:11.840><c> you</c><00:01:11.960><c> have</c><00:01:12.119><c> any</c>

00:01:12.390 --> 00:01:12.400 align:start position:0%
with this uh onebot if you have any
 

00:01:12.400 --> 00:01:14.950 align:start position:0%
with this uh onebot if you have any
questions<00:01:12.960><c> about</c><00:01:13.240><c> it</c><00:01:13.720><c> its</c><00:01:13.960><c> implementation</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
questions about it its implementation
 

00:01:14.960 --> 00:01:17.590 align:start position:0%
questions about it its implementation
you<00:01:15.080><c> can</c><00:01:15.360><c> ask</c><00:01:15.600><c> it</c><00:01:15.880><c> on</c><00:01:16.040><c> our</c><00:01:16.360><c> Discord</c><00:01:16.759><c> server</c><00:01:17.479><c> you</c>

00:01:17.590 --> 00:01:17.600 align:start position:0%
you can ask it on our Discord server you
 

00:01:17.600 --> 00:01:19.830 align:start position:0%
you can ask it on our Discord server you
can<00:01:17.840><c> also</c><00:01:18.360><c> uh</c><00:01:18.479><c> create</c><00:01:18.840><c> issues</c><00:01:19.280><c> or</c><00:01:19.520><c> pull</c>

00:01:19.830 --> 00:01:19.840 align:start position:0%
can also uh create issues or pull
 

00:01:19.840 --> 00:01:21.870 align:start position:0%
can also uh create issues or pull
requests<00:01:20.479><c> here</c><00:01:20.640><c> in</c><00:01:20.799><c> this</c><00:01:20.960><c> repo</c><00:01:21.479><c> and</c><00:01:21.720><c> we</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
requests here in this repo and we
 

00:01:21.880 --> 00:01:23.510 align:start position:0%
requests here in this repo and we
definitely<00:01:22.200><c> want</c><00:01:22.360><c> to</c><00:01:22.520><c> make</c><00:01:22.640><c> it</c><00:01:22.799><c> better</c><00:01:23.400><c> we</c>

00:01:23.510 --> 00:01:23.520 align:start position:0%
definitely want to make it better we
 

00:01:23.520 --> 00:01:25.270 align:start position:0%
definitely want to make it better we
want<00:01:23.640><c> to</c><00:01:23.759><c> make</c><00:01:23.960><c> it</c><00:01:24.119><c> mature</c><00:01:24.479><c> application</c><00:01:25.119><c> and</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
want to make it mature application and
 

00:01:25.280 --> 00:01:27.030 align:start position:0%
want to make it mature application and
hopefully<00:01:26.079><c> this</c><00:01:26.240><c> gives</c><00:01:26.439><c> you</c><00:01:26.640><c> some</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
hopefully this gives you some
 

00:01:27.040 --> 00:01:29.469 align:start position:0%
hopefully this gives you some
inspiration<00:01:27.799><c> on</c><00:01:28.040><c> how</c><00:01:28.200><c> to</c><00:01:28.400><c> build</c><00:01:29.079><c> potentially</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
inspiration on how to build potentially
 

00:01:29.479 --> 00:01:32.590 align:start position:0%
inspiration on how to build potentially
a<00:01:29.600><c> simil</c><00:01:30.320><c> app</c><00:01:30.799><c> yourself</c><00:01:31.799><c> we</c><00:01:32.000><c> also</c><00:01:32.200><c> encourage</c>

00:01:32.590 --> 00:01:32.600 align:start position:0%
a simil app yourself we also encourage
 

00:01:32.600 --> 00:01:35.030 align:start position:0%
a simil app yourself we also encourage
you<00:01:32.720><c> to</c><00:01:32.880><c> open</c><00:01:33.159><c> source</c><00:01:33.680><c> your</c><00:01:33.920><c> applications</c><00:01:34.680><c> so</c>

00:01:35.030 --> 00:01:35.040 align:start position:0%
you to open source your applications so
 

00:01:35.040 --> 00:01:37.870 align:start position:0%
you to open source your applications so
as<00:01:35.119><c> a</c><00:01:35.320><c> community</c><00:01:36.079><c> we</c><00:01:36.200><c> can</c><00:01:36.360><c> learn</c><00:01:36.799><c> together</c><00:01:37.520><c> and</c>

00:01:37.870 --> 00:01:37.880 align:start position:0%
as a community we can learn together and
 

00:01:37.880 --> 00:01:51.190 align:start position:0%
as a community we can learn together and
develop<00:01:38.560><c> really</c><00:01:38.920><c> nice</c><00:01:39.360><c> and</c><00:01:39.600><c> useful</c><00:01:40.399><c> llm</c><00:01:40.840><c> based</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
 
 

00:01:51.200 --> 00:01:54.200 align:start position:0%
 
applications

