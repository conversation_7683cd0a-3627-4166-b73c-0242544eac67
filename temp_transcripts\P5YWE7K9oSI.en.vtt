WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:05.950 align:start position:0%
 
[Music]

00:00:05.950 --> 00:00:05.960 align:start position:0%
[Music]
 

00:00:05.960 --> 00:00:08.150 align:start position:0%
[Music]
GitHub<00:00:06.440><c> actions</c><00:00:07.080><c> over</c><00:00:07.439><c> these</c><00:00:07.640><c> last</c><00:00:07.839><c> three</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
GitHub actions over these last three
 

00:00:08.160 --> 00:00:10.950 align:start position:0%
GitHub actions over these last three
years<00:00:08.599><c> it</c><00:00:08.800><c> has</c><00:00:09.000><c> become</c><00:00:09.360><c> our</c><00:00:09.639><c> main</c><00:00:10.000><c> way</c><00:00:10.360><c> to</c><00:00:10.599><c> run</c>

00:00:10.950 --> 00:00:10.960 align:start position:0%
years it has become our main way to run
 

00:00:10.960 --> 00:00:13.749 align:start position:0%
years it has become our main way to run
automation<00:00:11.960><c> as</c><00:00:12.120><c> part</c><00:00:12.320><c> of</c><00:00:12.440><c> our</c><00:00:12.679><c> day-to-day</c><00:00:13.599><c> we</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
automation as part of our day-to-day we
 

00:00:13.759 --> 00:00:16.950 align:start position:0%
automation as part of our day-to-day we
run<00:00:14.400><c> like</c><00:00:14.879><c> tens</c><00:00:15.160><c> of</c><00:00:15.360><c> thousands</c><00:00:16.080><c> of</c><00:00:16.440><c> workflows</c>

00:00:16.950 --> 00:00:16.960 align:start position:0%
run like tens of thousands of workflows
 

00:00:16.960 --> 00:00:20.230 align:start position:0%
run like tens of thousands of workflows
a<00:00:17.160><c> day</c><00:00:17.760><c> and</c><00:00:17.960><c> that</c><00:00:18.439><c> automation</c><00:00:19.439><c> is</c><00:00:19.640><c> helping</c><00:00:20.039><c> not</c>

00:00:20.230 --> 00:00:20.240 align:start position:0%
a day and that automation is helping not
 

00:00:20.240 --> 00:00:22.470 align:start position:0%
a day and that automation is helping not
only<00:00:20.519><c> in</c><00:00:20.680><c> the</c><00:00:20.800><c> productivity</c><00:00:21.359><c> of</c><00:00:21.519><c> people</c><00:00:22.199><c> but</c>

00:00:22.470 --> 00:00:22.480 align:start position:0%
only in the productivity of people but
 

00:00:22.480 --> 00:00:25.310 align:start position:0%
only in the productivity of people but
also<00:00:23.199><c> uh</c><00:00:23.320><c> is</c><00:00:23.480><c> improving</c><00:00:24.000><c> the</c><00:00:24.199><c> quality</c><00:00:24.920><c> of</c><00:00:25.039><c> our</c>

00:00:25.310 --> 00:00:25.320 align:start position:0%
also uh is improving the quality of our
 

00:00:25.320 --> 00:00:27.750 align:start position:0%
also uh is improving the quality of our
resulting<00:00:25.880><c> products</c><00:00:26.599><c> we're</c><00:00:26.960><c> using</c><00:00:27.320><c> actions</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
resulting products we're using actions
 

00:00:27.760 --> 00:00:32.150 align:start position:0%
resulting products we're using actions
for<00:00:28.599><c> cicd</c><00:00:29.599><c> but</c><00:00:30.039><c> also</c><00:00:30.320><c> to</c><00:00:30.519><c> automate</c><00:00:31.080><c> the</c><00:00:31.840><c> very</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
for cicd but also to automate the very
 

00:00:32.160 --> 00:00:35.350 align:start position:0%
for cicd but also to automate the very
manual<00:00:32.759><c> task</c><00:00:33.120><c> of</c><00:00:33.440><c> publishing</c><00:00:34.120><c> Jam</c><00:00:34.640><c> to</c><00:00:35.160><c> the</c>

00:00:35.350 --> 00:00:35.360 align:start position:0%
manual task of publishing Jam to the
 

00:00:35.360 --> 00:00:37.630 align:start position:0%
manual task of publishing Jam to the
browser<00:00:35.920><c> web</c><00:00:36.200><c> stores</c><00:00:36.960><c> GitHub</c><00:00:37.280><c> actions</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
browser web stores GitHub actions
 

00:00:37.640 --> 00:00:39.389 align:start position:0%
browser web stores GitHub actions
actually<00:00:37.960><c> does</c><00:00:38.160><c> that</c><00:00:38.320><c> for</c><00:00:38.559><c> us</c><00:00:38.960><c> developers</c>

00:00:39.389 --> 00:00:39.399 align:start position:0%
actually does that for us developers
 

00:00:39.399 --> 00:00:41.590 align:start position:0%
actually does that for us developers
don't<00:00:39.600><c> have</c><00:00:39.719><c> to</c><00:00:39.879><c> switch</c><00:00:40.320><c> tabs</c><00:00:41.079><c> notice</c><00:00:41.440><c> when</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
don't have to switch tabs notice when
 

00:00:41.600 --> 00:00:44.190 align:start position:0%
don't have to switch tabs notice when
the<00:00:41.680><c> build</c><00:00:41.960><c> is</c><00:00:42.120><c> finished</c><00:00:42.920><c> it</c><00:00:43.039><c> just</c><00:00:43.239><c> happens</c>

00:00:44.190 --> 00:00:44.200 align:start position:0%
the build is finished it just happens
 

00:00:44.200 --> 00:00:46.590 align:start position:0%
the build is finished it just happens
automatically<00:00:45.200><c> in</c><00:00:45.320><c> the</c><00:00:45.520><c> past</c><00:00:45.719><c> in</c><00:00:45.840><c> the</c><00:00:46.000><c> bank</c><00:00:46.440><c> we</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
automatically in the past in the bank we
 

00:00:46.600 --> 00:00:49.270 align:start position:0%
automatically in the past in the bank we
created<00:00:47.120><c> lots</c><00:00:47.320><c> of</c><00:00:47.520><c> cicd</c><00:00:48.120><c> systems</c><00:00:49.000><c> mainly</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
created lots of cicd systems mainly
 

00:00:49.280 --> 00:00:51.310 align:start position:0%
created lots of cicd systems mainly
using<00:00:49.559><c> Jen</c><00:00:49.840><c> Kings</c><00:00:50.559><c> then</c><00:00:50.719><c> we</c><00:00:50.840><c> saw</c><00:00:51.039><c> in</c><00:00:51.120><c> the</c>

00:00:51.310 --> 00:00:51.320 align:start position:0%
using Jen Kings then we saw in the
 

00:00:51.320 --> 00:00:53.349 align:start position:0%
using Jen Kings then we saw in the
GitHub<00:00:51.879><c> opportunity</c><00:00:52.440><c> to</c><00:00:52.640><c> have</c><00:00:52.879><c> everything</c><00:00:53.239><c> in</c>

00:00:53.349 --> 00:00:53.359 align:start position:0%
GitHub opportunity to have everything in
 

00:00:53.359 --> 00:00:55.430 align:start position:0%
GitHub opportunity to have everything in
the<00:00:53.480><c> same</c><00:00:53.680><c> platform</c><00:00:54.559><c> and</c><00:00:54.719><c> we</c><00:00:54.840><c> start</c><00:00:55.199><c> that</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
the same platform and we start that
 

00:00:55.440 --> 00:00:57.069 align:start position:0%
the same platform and we start that
project<00:00:55.719><c> to</c><00:00:55.920><c> migrate</c><00:00:56.359><c> everything</c><00:00:56.719><c> from</c><00:00:56.920><c> the</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
project to migrate everything from the
 

00:00:57.079 --> 00:00:59.150 align:start position:0%
project to migrate everything from the
older<00:00:57.440><c> platforms</c><00:00:57.920><c> to</c><00:00:58.079><c> the</c><00:00:58.199><c> new</c><00:00:58.399><c> platform</c><00:00:59.000><c> now</c>

00:00:59.150 --> 00:00:59.160 align:start position:0%
older platforms to the new platform now
 

00:00:59.160 --> 00:01:01.029 align:start position:0%
older platforms to the new platform now
they<00:00:59.280><c> can</c><00:00:59.399><c> focus</c><00:01:00.000><c> most</c><00:01:00.199><c> on</c><00:01:00.359><c> the</c><00:01:00.519><c> business</c><00:01:00.920><c> and</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
they can focus most on the business and
 

00:01:01.039 --> 00:01:03.189 align:start position:0%
they can focus most on the business and
not<00:01:01.199><c> in</c><00:01:01.320><c> the</c><00:01:01.440><c> tech</c><00:01:01.760><c> part</c><00:01:02.160><c> the</c><00:01:02.320><c> security</c><00:01:02.760><c> QA</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
not in the tech part the security QA
 

00:01:03.199 --> 00:01:06.390 align:start position:0%
not in the tech part the security QA
process<00:01:03.559><c> can</c><00:01:03.719><c> go</c><00:01:03.960><c> from</c><00:01:04.879><c> weeks</c><00:01:05.799><c> maybe</c><00:01:06.080><c> even</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
process can go from weeks maybe even
 

00:01:06.400 --> 00:01:09.310 align:start position:0%
process can go from weeks maybe even
months<00:01:07.400><c> and</c><00:01:07.520><c> it</c><00:01:07.640><c> can</c><00:01:07.759><c> take</c><00:01:08.320><c> minutes</c><00:01:08.840><c> it</c><00:01:08.960><c> can</c><00:01:09.119><c> be</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
months and it can take minutes it can be
 

00:01:09.320 --> 00:01:10.990 align:start position:0%
months and it can take minutes it can be
literally<00:01:09.680><c> the</c><00:01:09.840><c> result</c><00:01:10.080><c> of</c><00:01:10.200><c> a</c><00:01:10.320><c> GI</c><00:01:10.479><c> H</c><00:01:10.680><c> actions</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
literally the result of a GI H actions
 

00:01:11.000 --> 00:01:13.109 align:start position:0%
literally the result of a GI H actions
workflow<00:01:11.759><c> developers</c><00:01:12.320><c> they</c><00:01:12.479><c> have</c><00:01:12.600><c> to</c><00:01:12.759><c> wait</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
workflow developers they have to wait
 

00:01:13.119 --> 00:01:15.550 align:start position:0%
workflow developers they have to wait
less<00:01:13.680><c> to</c><00:01:13.880><c> get</c><00:01:14.040><c> the</c><00:01:14.200><c> feedback</c><00:01:15.080><c> of</c><00:01:15.320><c> what</c><00:01:15.479><c> they</c>

00:01:15.550 --> 00:01:15.560 align:start position:0%
less to get the feedback of what they
 

00:01:15.560 --> 00:01:17.550 align:start position:0%
less to get the feedback of what they
are<00:01:15.720><c> working</c><00:01:16.119><c> on</c><00:01:16.680><c> and</c><00:01:16.840><c> therefore</c><00:01:17.240><c> they</c><00:01:17.360><c> are</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
are working on and therefore they are
 

00:01:17.560 --> 00:01:20.310 align:start position:0%
are working on and therefore they are
reducing<00:01:18.479><c> contact</c><00:01:18.920><c> switch</c><00:01:19.560><c> developer</c><00:01:20.040><c> time</c>

00:01:20.310 --> 00:01:20.320 align:start position:0%
reducing contact switch developer time
 

00:01:20.320 --> 00:01:22.749 align:start position:0%
reducing contact switch developer time
is<00:01:20.479><c> always</c><00:01:21.000><c> like</c><00:01:21.119><c> a</c><00:01:21.280><c> treasure</c><00:01:21.960><c> and</c><00:01:22.360><c> every</c>

00:01:22.749 --> 00:01:22.759 align:start position:0%
is always like a treasure and every
 

00:01:22.759 --> 00:01:25.310 align:start position:0%
is always like a treasure and every
little<00:01:23.040><c> bit</c><00:01:23.360><c> that</c><00:01:23.520><c> you</c><00:01:24.079><c> save</c><00:01:24.759><c> it</c><00:01:24.960><c> gets</c>

00:01:25.310 --> 00:01:25.320 align:start position:0%
little bit that you save it gets
 

00:01:25.320 --> 00:01:27.390 align:start position:0%
little bit that you save it gets
eventually<00:01:25.880><c> invested</c><00:01:26.360><c> in</c><00:01:26.560><c> something</c><00:01:26.880><c> else</c><00:01:27.320><c> in</c>

00:01:27.390 --> 00:01:27.400 align:start position:0%
eventually invested in something else in
 

00:01:27.400 --> 00:01:28.990 align:start position:0%
eventually invested in something else in
the<00:01:27.600><c> past</c><00:01:27.799><c> we</c><00:01:27.920><c> were</c><00:01:28.040><c> able</c><00:01:28.240><c> to</c><00:01:28.400><c> deploy</c><00:01:28.759><c> to</c>

00:01:28.990 --> 00:01:29.000 align:start position:0%
the past we were able to deploy to
 

00:01:29.000 --> 00:01:32.310 align:start position:0%
the past we were able to deploy to
production<00:01:30.280><c> uh</c><00:01:30.520><c> maybe</c><00:01:30.799><c> in</c><00:01:30.920><c> a</c><00:01:31.079><c> week</c><00:01:32.040><c> sometimes</c>

00:01:32.310 --> 00:01:32.320 align:start position:0%
production uh maybe in a week sometimes
 

00:01:32.320 --> 00:01:34.590 align:start position:0%
production uh maybe in a week sometimes
two<00:01:32.920><c> and</c><00:01:33.079><c> right</c><00:01:33.240><c> now</c><00:01:33.399><c> with</c><00:01:33.560><c> the</c><00:01:33.680><c> geub</c><00:01:34.119><c> actions</c>

00:01:34.590 --> 00:01:34.600 align:start position:0%
two and right now with the geub actions
 

00:01:34.600 --> 00:01:36.830 align:start position:0%
two and right now with the geub actions
platform<00:01:35.240><c> we</c><00:01:35.520><c> able</c><00:01:35.720><c> to</c><00:01:35.880><c> deploy</c><00:01:36.200><c> in</c><00:01:36.360><c> like</c><00:01:36.600><c> one</c>

00:01:36.830 --> 00:01:36.840 align:start position:0%
platform we able to deploy in like one
 

00:01:36.840 --> 00:01:39.230 align:start position:0%
platform we able to deploy in like one
hour<00:01:37.079><c> or</c><00:01:37.240><c> two</c><00:01:37.439><c> hours</c><00:01:37.680><c> to</c><00:01:37.880><c> our</c><00:01:38.240><c> customers</c>

00:01:39.230 --> 00:01:39.240 align:start position:0%
hour or two hours to our customers
 

00:01:39.240 --> 00:01:41.030 align:start position:0%
hour or two hours to our customers
having<00:01:39.680><c> a</c><00:01:39.880><c> great</c><00:01:40.079><c> source</c><00:01:40.439><c> code</c><00:01:40.640><c> management</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
having a great source code management
 

00:01:41.040 --> 00:01:43.910 align:start position:0%
having a great source code management
tool<00:01:41.320><c> is</c><00:01:42.240><c> obviously</c><00:01:42.799><c> a</c><00:01:42.960><c> foundational</c><00:01:43.759><c> thing</c>

00:01:43.910 --> 00:01:43.920 align:start position:0%
tool is obviously a foundational thing
 

00:01:43.920 --> 00:01:45.670 align:start position:0%
tool is obviously a foundational thing
to<00:01:44.159><c> have</c><00:01:44.560><c> but</c><00:01:44.719><c> beyond</c><00:01:45.040><c> that</c><00:01:45.280><c> like</c><00:01:45.399><c> we</c><00:01:45.520><c> really</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
to have but beyond that like we really
 

00:01:45.680 --> 00:01:47.389 align:start position:0%
to have but beyond that like we really
want<00:01:45.960><c> developers</c><00:01:46.600><c> to</c><00:01:46.759><c> spend</c><00:01:47.000><c> their</c><00:01:47.200><c> time</c>

00:01:47.389 --> 00:01:47.399 align:start position:0%
want developers to spend their time
 

00:01:47.399 --> 00:01:49.469 align:start position:0%
want developers to spend their time
innovating<00:01:48.040><c> and</c><00:01:48.159><c> being</c><00:01:48.360><c> in</c><00:01:48.520><c> flow</c><00:01:49.119><c> and</c><00:01:49.240><c> not</c>

00:01:49.469 --> 00:01:49.479 align:start position:0%
innovating and being in flow and not
 

00:01:49.479 --> 00:01:51.190 align:start position:0%
innovating and being in flow and not
thinking<00:01:49.799><c> about</c><00:01:50.119><c> like</c><00:01:50.320><c> going</c><00:01:50.520><c> to</c><00:01:50.719><c> 17</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
thinking about like going to 17
 

00:01:51.200 --> 00:01:54.380 align:start position:0%
thinking about like going to 17
different<00:01:51.520><c> places</c><00:01:51.840><c> to</c><00:01:52.000><c> do</c><00:01:52.560><c> two</c><00:01:52.880><c> things</c>

00:01:54.380 --> 00:01:54.390 align:start position:0%
different places to do two things
 

00:01:54.390 --> 00:02:02.140 align:start position:0%
different places to do two things
[Music]

