WEBVTT
Kind: captions
Language: en

00:00:05.279 --> 00:00:05.670 align:start position:0%
 
hi

00:00:05.670 --> 00:00:05.680 align:start position:0%
hi
 

00:00:05.680 --> 00:00:07.510 align:start position:0%
hi
i'm<00:00:05.839><c> catherine</c><00:00:06.240><c> lynn</c><00:00:06.560><c> and</c><00:00:06.720><c> my</c><00:00:06.960><c> talk</c><00:00:07.200><c> will</c><00:00:07.359><c> be</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
i'm catherine lynn and my talk will be
 

00:00:07.520 --> 00:00:09.509 align:start position:0%
i'm catherine lynn and my talk will be
about<00:00:07.839><c> coven</c><00:00:08.240><c> 19's</c><00:00:08.880><c> impact</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
about coven 19's impact
 

00:00:09.519 --> 00:00:11.310 align:start position:0%
about coven 19's impact
on<00:00:10.320><c> counties</c><00:00:10.719><c> with</c><00:00:10.880><c> different</c>

00:00:11.310 --> 00:00:11.320 align:start position:0%
on counties with different
 

00:00:11.320 --> 00:00:13.830 align:start position:0%
on counties with different
socio-economical<00:00:12.320><c> characteristics</c>

00:00:13.830 --> 00:00:13.840 align:start position:0%
socio-economical characteristics
 

00:00:13.840 --> 00:00:16.470 align:start position:0%
socio-economical characteristics
it's<00:00:14.080><c> based</c><00:00:14.480><c> on</c><00:00:14.719><c> a</c><00:00:14.880><c> research</c><00:00:15.360><c> project</c><00:00:15.920><c> that</c><00:00:16.160><c> i</c>

00:00:16.470 --> 00:00:16.480 align:start position:0%
it's based on a research project that i
 

00:00:16.480 --> 00:00:16.870 align:start position:0%
it's based on a research project that i
did

00:00:16.870 --> 00:00:16.880 align:start position:0%
did
 

00:00:16.880 --> 00:00:22.550 align:start position:0%
did
supervised<00:00:17.520><c> by</c><00:00:17.680><c> professor</c><00:00:18.160><c> linda</c><00:00:18.480><c> zao</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
 
 

00:00:22.560 --> 00:00:25.509 align:start position:0%
 
since<00:00:22.880><c> the</c><00:00:23.039><c> first</c><00:00:23.439><c> u.s</c><00:00:23.920><c> coven</c><00:00:24.240><c> 19</c><00:00:24.720><c> case</c><00:00:25.199><c> in</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
since the first u.s coven 19 case in
 

00:00:25.519 --> 00:00:27.269 align:start position:0%
since the first u.s coven 19 case in
january<00:00:26.080><c> 2020</c>

00:00:27.269 --> 00:00:27.279 align:start position:0%
january 2020
 

00:00:27.279 --> 00:00:30.390 align:start position:0%
january 2020
cases<00:00:27.680><c> have</c><00:00:27.920><c> shot</c><00:00:28.160><c> up</c><00:00:28.320><c> really</c><00:00:28.840><c> quickly</c><00:00:30.240><c> they</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
cases have shot up really quickly they
 

00:00:30.400 --> 00:00:31.509 align:start position:0%
cases have shot up really quickly they
did<00:00:30.640><c> so</c><00:00:30.880><c> much</c><00:00:31.119><c> so</c>

00:00:31.509 --> 00:00:31.519 align:start position:0%
did so much so
 

00:00:31.519 --> 00:00:33.990 align:start position:0%
did so much so
that<00:00:31.760><c> by</c><00:00:32.000><c> march</c><00:00:32.480><c> a</c><00:00:32.640><c> lot</c><00:00:32.800><c> of</c><00:00:32.880><c> states</c><00:00:33.360><c> instituted</c>

00:00:33.990 --> 00:00:34.000 align:start position:0%
that by march a lot of states instituted
 

00:00:34.000 --> 00:00:35.190 align:start position:0%
that by march a lot of states instituted
lockdowns

00:00:35.190 --> 00:00:35.200 align:start position:0%
lockdowns
 

00:00:35.200 --> 00:00:37.830 align:start position:0%
lockdowns
my<00:00:35.360><c> school</c><00:00:35.840><c> my</c><00:00:36.000><c> school</c><00:00:36.239><c> was</c><00:00:36.480><c> closed</c><00:00:37.040><c> and</c><00:00:37.440><c> i</c>

00:00:37.830 --> 00:00:37.840 align:start position:0%
my school my school was closed and i
 

00:00:37.840 --> 00:00:39.830 align:start position:0%
my school my school was closed and i
started<00:00:38.160><c> taking</c><00:00:38.480><c> online</c><00:00:38.879><c> classes</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
started taking online classes
 

00:00:39.840 --> 00:00:42.150 align:start position:0%
started taking online classes
all<00:00:40.079><c> around</c><00:00:40.399><c> me</c><00:00:40.640><c> i</c><00:00:40.960><c> saw</c><00:00:41.200><c> signs</c><00:00:41.520><c> of</c><00:00:41.600><c> how</c><00:00:41.840><c> covet</c>

00:00:42.150 --> 00:00:42.160 align:start position:0%
all around me i saw signs of how covet
 

00:00:42.160 --> 00:00:43.510 align:start position:0%
all around me i saw signs of how covet
19<00:00:42.559><c> had</c><00:00:42.719><c> affected</c>

00:00:43.510 --> 00:00:43.520 align:start position:0%
19 had affected
 

00:00:43.520 --> 00:00:46.150 align:start position:0%
19 had affected
not<00:00:43.760><c> only</c><00:00:44.000><c> my</c><00:00:44.239><c> life</c><00:00:44.559><c> which</c><00:00:45.039><c> it</c><00:00:45.200><c> did</c><00:00:45.440><c> so</c><00:00:45.680><c> in</c><00:00:45.920><c> a</c>

00:00:46.150 --> 00:00:46.160 align:start position:0%
not only my life which it did so in a
 

00:00:46.160 --> 00:00:46.630 align:start position:0%
not only my life which it did so in a
very

00:00:46.630 --> 00:00:46.640 align:start position:0%
very
 

00:00:46.640 --> 00:00:49.110 align:start position:0%
very
mild<00:00:47.039><c> manner</c><00:00:47.680><c> but</c><00:00:47.840><c> the</c><00:00:48.000><c> lives</c><00:00:48.320><c> of</c><00:00:48.480><c> my</c><00:00:48.719><c> friends</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
mild manner but the lives of my friends
 

00:00:49.120 --> 00:00:50.389 align:start position:0%
mild manner but the lives of my friends
and<00:00:49.200><c> my</c><00:00:49.360><c> community</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
and my community
 

00:00:50.399 --> 00:00:53.189 align:start position:0%
and my community
and<00:00:50.719><c> across</c><00:00:51.039><c> the</c><00:00:51.199><c> country</c><00:00:52.399><c> so</c><00:00:52.640><c> i</c><00:00:52.800><c> began</c><00:00:53.039><c> to</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
and across the country so i began to
 

00:00:53.199 --> 00:00:53.830 align:start position:0%
and across the country so i began to
wonder

00:00:53.830 --> 00:00:53.840 align:start position:0%
wonder
 

00:00:53.840 --> 00:00:59.590 align:start position:0%
wonder
if<00:00:54.000><c> there</c><00:00:54.160><c> was</c><00:00:54.320><c> anything</c><00:00:54.719><c> i</c><00:00:54.879><c> could</c><00:00:55.039><c> do</c><00:00:55.360><c> to</c><00:00:56.840><c> help</c>

00:00:59.590 --> 00:00:59.600 align:start position:0%
if there was anything i could do to help
 

00:00:59.600 --> 00:01:01.590 align:start position:0%
if there was anything i could do to help
at<00:00:59.760><c> this</c><00:01:00.000><c> time</c><00:01:00.399><c> i</c><00:01:00.719><c> also</c><00:01:01.039><c> found</c><00:01:01.280><c> out</c><00:01:01.440><c> that</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
at this time i also found out that
 

00:01:01.600 --> 00:01:03.910 align:start position:0%
at this time i also found out that
professor<00:01:02.079><c> zao</c><00:01:02.399><c> was</c><00:01:02.640><c> initiating</c><00:01:03.359><c> a</c><00:01:03.600><c> summer</c>

00:01:03.910 --> 00:01:03.920 align:start position:0%
professor zao was initiating a summer
 

00:01:03.920 --> 00:01:05.030 align:start position:0%
professor zao was initiating a summer
data<00:01:04.320><c> science</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
data science
 

00:01:05.040 --> 00:01:07.990 align:start position:0%
data science
program<00:01:06.080><c> for</c><00:01:06.240><c> high</c><00:01:06.400><c> school</c><00:01:06.720><c> students</c><00:01:07.520><c> and</c><00:01:07.760><c> so</c>

00:01:07.990 --> 00:01:08.000 align:start position:0%
program for high school students and so
 

00:01:08.000 --> 00:01:08.710 align:start position:0%
program for high school students and so
i<00:01:08.080><c> reached</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
i reached
 

00:01:08.720 --> 00:01:11.750 align:start position:0%
i reached
reached<00:01:09.119><c> out</c><00:01:09.280><c> to</c><00:01:09.439><c> her</c><00:01:10.080><c> unfortunately</c>

00:01:11.750 --> 00:01:11.760 align:start position:0%
reached out to her unfortunately
 

00:01:11.760 --> 00:01:14.390 align:start position:0%
reached out to her unfortunately
the<00:01:11.920><c> program</c><00:01:12.240><c> was</c><00:01:12.400><c> canceled</c><00:01:12.720><c> due</c><00:01:12.880><c> to</c><00:01:12.960><c> cover</c><00:01:13.280><c> 19</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
the program was canceled due to cover 19
 

00:01:14.400 --> 00:01:14.789 align:start position:0%
the program was canceled due to cover 19
but

00:01:14.789 --> 00:01:14.799 align:start position:0%
but
 

00:01:14.799 --> 00:01:17.990 align:start position:0%
but
she<00:01:15.040><c> graciously</c><00:01:15.840><c> agreed</c><00:01:16.400><c> to</c><00:01:16.560><c> mentor</c><00:01:16.960><c> me</c><00:01:17.280><c> on</c><00:01:17.680><c> a</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
she graciously agreed to mentor me on a
 

00:01:18.000 --> 00:01:22.390 align:start position:0%
she graciously agreed to mentor me on a
research<00:01:18.479><c> project</c><00:01:18.960><c> on</c><00:01:19.119><c> code</c><00:01:19.439><c> 19.</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
 
 

00:01:22.400 --> 00:01:24.630 align:start position:0%
 
we<00:01:22.560><c> started</c><00:01:22.960><c> by</c><00:01:23.119><c> going</c><00:01:23.439><c> over</c><00:01:23.680><c> the</c><00:01:23.759><c> material</c><00:01:24.479><c> in</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
we started by going over the material in
 

00:01:24.640 --> 00:01:26.870 align:start position:0%
we started by going over the material in
professor<00:01:25.119><c> zhao's</c><00:01:25.759><c> modern</c><00:01:26.159><c> data</c><00:01:26.479><c> mining</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
professor zhao's modern data mining
 

00:01:26.880 --> 00:01:28.070 align:start position:0%
professor zhao's modern data mining
course

00:01:28.070 --> 00:01:28.080 align:start position:0%
course
 

00:01:28.080 --> 00:01:29.749 align:start position:0%
course
which<00:01:28.400><c> covered</c><00:01:28.720><c> statistical</c><00:01:29.439><c> machine</c>

00:01:29.749 --> 00:01:29.759 align:start position:0%
which covered statistical machine
 

00:01:29.759 --> 00:01:32.069 align:start position:0%
which covered statistical machine
learning<00:01:30.079><c> methods</c>

00:01:32.069 --> 00:01:32.079 align:start position:0%
learning methods
 

00:01:32.079 --> 00:01:34.469 align:start position:0%
learning methods
ranging<00:01:32.560><c> from</c><00:01:32.880><c> the</c><00:01:33.040><c> standard</c><00:01:33.520><c> linear</c><00:01:33.840><c> model</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
ranging from the standard linear model
 

00:01:34.479 --> 00:01:36.069 align:start position:0%
ranging from the standard linear model
to<00:01:34.880><c> lasso</c>

00:01:36.069 --> 00:01:36.079 align:start position:0%
to lasso
 

00:01:36.079 --> 00:01:38.830 align:start position:0%
to lasso
to<00:01:36.240><c> random</c><00:01:36.640><c> forests</c><00:01:37.280><c> boosting</c><00:01:37.920><c> and</c><00:01:38.159><c> neural</c>

00:01:38.830 --> 00:01:38.840 align:start position:0%
to random forests boosting and neural
 

00:01:38.840 --> 00:01:43.190 align:start position:0%
to random forests boosting and neural
networks

00:01:43.190 --> 00:01:43.200 align:start position:0%
 
 

00:01:43.200 --> 00:01:50.550 align:start position:0%
 
then<00:01:43.680><c> we</c><00:01:43.840><c> started</c><00:01:44.479><c> the</c><00:01:44.640><c> project</c><00:01:45.119><c> on</c><00:01:45.560><c> coven19.3</c>

00:01:50.550 --> 00:01:50.560 align:start position:0%
 
 

00:01:50.560 --> 00:01:53.510 align:start position:0%
 
we<00:01:50.799><c> tried</c><00:01:51.040><c> to</c><00:01:51.200><c> find</c><00:01:51.759><c> the</c><00:01:52.320><c> important</c><00:01:53.040><c> factors</c>

00:01:53.510 --> 00:01:53.520 align:start position:0%
we tried to find the important factors
 

00:01:53.520 --> 00:01:55.030 align:start position:0%
we tried to find the important factors
affecting<00:01:53.920><c> the</c><00:01:54.079><c> death</c><00:01:54.320><c> rate</c>

00:01:55.030 --> 00:01:55.040 align:start position:0%
affecting the death rate
 

00:01:55.040 --> 00:01:57.910 align:start position:0%
affecting the death rate
so<00:01:55.280><c> for</c><00:01:55.439><c> example</c><00:01:56.640><c> is</c><00:01:57.119><c> one</c><00:01:57.360><c> racial</c><00:01:57.680><c> group</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
so for example is one racial group
 

00:01:57.920 --> 00:01:58.709 align:start position:0%
so for example is one racial group
affected<00:01:58.320><c> more</c>

00:01:58.709 --> 00:01:58.719 align:start position:0%
affected more
 

00:01:58.719 --> 00:02:02.069 align:start position:0%
affected more
are<00:01:59.040><c> the</c><00:01:59.280><c> elderly</c><00:01:59.759><c> affected</c><00:02:00.159><c> more</c><00:02:00.880><c> and</c><00:02:01.680><c> do</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
are the elderly affected more and do
 

00:02:02.079 --> 00:02:05.590 align:start position:0%
are the elderly affected more and do
income<00:02:02.399><c> level</c><00:02:02.880><c> and</c><00:02:03.200><c> education</c><00:02:03.759><c> level</c><00:02:04.640><c> play</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
income level and education level play
 

00:02:05.600 --> 00:02:09.270 align:start position:0%
income level and education level play
an<00:02:05.759><c> important</c><00:02:06.840><c> role</c>

00:02:09.270 --> 00:02:09.280 align:start position:0%
an important role
 

00:02:09.280 --> 00:02:11.190 align:start position:0%
an important role
we<00:02:09.520><c> further</c><00:02:09.840><c> used</c><00:02:10.160><c> machine</c><00:02:10.479><c> learning</c><00:02:10.800><c> methods</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
we further used machine learning methods
 

00:02:11.200 --> 00:02:13.350 align:start position:0%
we further used machine learning methods
to<00:02:11.440><c> predict</c><00:02:11.840><c> coven</c><00:02:12.239><c> 19</c><00:02:12.640><c> death</c><00:02:12.879><c> rate</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
to predict coven 19 death rate
 

00:02:13.360 --> 00:02:15.750 align:start position:0%
to predict coven 19 death rate
at<00:02:13.599><c> a</c><00:02:13.680><c> county</c><00:02:14.000><c> level</c><00:02:15.040><c> we</c><00:02:15.200><c> hope</c><00:02:15.440><c> that</c><00:02:15.680><c> the</c>

00:02:15.750 --> 00:02:15.760 align:start position:0%
at a county level we hope that the
 

00:02:15.760 --> 00:02:16.710 align:start position:0%
at a county level we hope that the
results

00:02:16.710 --> 00:02:16.720 align:start position:0%
results
 

00:02:16.720 --> 00:02:18.630 align:start position:0%
results
might<00:02:16.959><c> help</c><00:02:17.200><c> on</c><00:02:17.440><c> issues</c><00:02:17.840><c> like</c><00:02:18.160><c> resource</c>

00:02:18.630 --> 00:02:18.640 align:start position:0%
might help on issues like resource
 

00:02:18.640 --> 00:02:20.869 align:start position:0%
might help on issues like resource
allocation<00:02:19.360><c> and</c><00:02:19.599><c> policy</c><00:02:20.080><c> decisions</c><00:02:20.640><c> in</c><00:02:20.720><c> the</c>

00:02:20.869 --> 00:02:20.879 align:start position:0%
allocation and policy decisions in the
 

00:02:20.879 --> 00:02:24.309 align:start position:0%
allocation and policy decisions in the
future

00:02:24.309 --> 00:02:24.319 align:start position:0%
 
 

00:02:24.319 --> 00:02:27.110 align:start position:0%
 
this<00:02:24.480><c> is</c><00:02:24.640><c> the</c><00:02:24.720><c> roadmap</c><00:02:25.360><c> of</c><00:02:25.520><c> our</c><00:02:25.599><c> research</c><00:02:26.800><c> we</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
this is the roadmap of our research we
 

00:02:27.120 --> 00:02:29.030 align:start position:0%
this is the roadmap of our research we
first<00:02:27.440><c> gathered</c><00:02:28.000><c> and</c><00:02:28.239><c> processed</c><00:02:28.879><c> the</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
first gathered and processed the
 

00:02:29.040 --> 00:02:30.390 align:start position:0%
first gathered and processed the
relevant<00:02:29.520><c> data</c>

00:02:30.390 --> 00:02:30.400 align:start position:0%
relevant data
 

00:02:30.400 --> 00:02:32.710 align:start position:0%
relevant data
and<00:02:30.560><c> then</c><00:02:30.720><c> we</c><00:02:30.879><c> built</c><00:02:31.120><c> the</c><00:02:31.200><c> models</c><00:02:31.840><c> and</c><00:02:32.160><c> finally</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
and then we built the models and finally
 

00:02:32.720 --> 00:02:37.270 align:start position:0%
and then we built the models and finally
we<00:02:32.959><c> summarized</c><00:02:33.519><c> our</c><00:02:34.840><c> findings</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
we summarized our findings
 

00:02:37.280 --> 00:02:41.990 align:start position:0%
we summarized our findings
the<00:02:37.440><c> data</c><00:02:37.840><c> came</c><00:02:38.080><c> from</c><00:02:38.319><c> two</c><00:02:38.480><c> different</c><00:02:38.800><c> sources</c>

00:02:41.990 --> 00:02:42.000 align:start position:0%
 
 

00:02:42.000 --> 00:02:45.110 align:start position:0%
 
c<00:02:42.480><c> and</c><00:02:42.640><c> the</c><00:02:42.800><c> usda</c><00:02:44.000><c> economic</c><00:02:44.560><c> resource</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
c and the usda economic resource
 

00:02:45.120 --> 00:02:46.790 align:start position:0%
c and the usda economic resource
research<00:02:45.599><c> service</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
research service
 

00:02:46.800 --> 00:02:49.190 align:start position:0%
research service
the<00:02:46.959><c> cdc</c><00:02:47.599><c> data</c><00:02:48.000><c> contains</c><00:02:48.640><c> county</c><00:02:48.879><c> level</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
the cdc data contains county level
 

00:02:49.200 --> 00:02:51.509 align:start position:0%
the cdc data contains county level
infection<00:02:49.840><c> and</c><00:02:50.080><c> fatality</c><00:02:50.640><c> data</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
infection and fatality data
 

00:02:51.519 --> 00:02:53.910 align:start position:0%
infection and fatality data
and<00:02:51.760><c> we</c><00:02:51.920><c> concentrated</c><00:02:52.640><c> just</c><00:02:52.959><c> on</c><00:02:53.040><c> the</c><00:02:53.200><c> fatality</c>

00:02:53.910 --> 00:02:53.920 align:start position:0%
and we concentrated just on the fatality
 

00:02:53.920 --> 00:02:55.670 align:start position:0%
and we concentrated just on the fatality
fatality<00:02:54.560><c> data</c>

00:02:55.670 --> 00:02:55.680 align:start position:0%
fatality data
 

00:02:55.680 --> 00:02:57.430 align:start position:0%
fatality data
rather<00:02:56.000><c> than</c><00:02:56.160><c> the</c><00:02:56.400><c> infection</c><00:02:56.800><c> data</c><00:02:57.120><c> because</c>

00:02:57.430 --> 00:02:57.440 align:start position:0%
rather than the infection data because
 

00:02:57.440 --> 00:02:59.670 align:start position:0%
rather than the infection data because
fatality<00:02:58.159><c> is</c><00:02:58.319><c> more</c><00:02:58.560><c> well-defined</c>

00:02:59.670 --> 00:02:59.680 align:start position:0%
fatality is more well-defined
 

00:02:59.680 --> 00:03:02.390 align:start position:0%
fatality is more well-defined
and<00:02:59.920><c> doesn't</c><00:03:00.239><c> depend</c><00:03:00.720><c> on</c><00:03:00.879><c> factors</c><00:03:02.080><c> like</c>

00:03:02.390 --> 00:03:02.400 align:start position:0%
and doesn't depend on factors like
 

00:03:02.400 --> 00:03:04.070 align:start position:0%
and doesn't depend on factors like
whether<00:03:02.720><c> tests</c><00:03:03.040><c> are</c><00:03:03.120><c> widely</c><00:03:03.519><c> available</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
whether tests are widely available
 

00:03:04.080 --> 00:03:07.350 align:start position:0%
whether tests are widely available
in<00:03:04.159><c> the</c><00:03:04.319><c> region</c>

00:03:07.350 --> 00:03:07.360 align:start position:0%
 
 

00:03:07.360 --> 00:03:10.229 align:start position:0%
 
the<00:03:07.599><c> ers</c><00:03:08.159><c> website</c><00:03:08.879><c> had</c><00:03:09.599><c> county</c><00:03:09.920><c> level</c>

00:03:10.229 --> 00:03:10.239 align:start position:0%
the ers website had county level
 

00:03:10.239 --> 00:03:11.990 align:start position:0%
the ers website had county level
socioeconomic<00:03:11.200><c> data</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
socioeconomic data
 

00:03:12.000 --> 00:03:14.949 align:start position:0%
socioeconomic data
with<00:03:12.400><c> four</c><00:03:12.640><c> relevant</c><00:03:12.959><c> data</c><00:03:13.280><c> sets</c><00:03:13.680><c> on</c><00:03:14.000><c> income</c>

00:03:14.949 --> 00:03:14.959 align:start position:0%
with four relevant data sets on income
 

00:03:14.959 --> 00:03:15.589 align:start position:0%
with four relevant data sets on income
jobs

00:03:15.589 --> 00:03:15.599 align:start position:0%
jobs
 

00:03:15.599 --> 00:03:19.509 align:start position:0%
jobs
demographics<00:03:16.879><c> and</c><00:03:17.120><c> county</c><00:03:17.599><c> classifications</c>

00:03:19.509 --> 00:03:19.519 align:start position:0%
demographics and county classifications
 

00:03:19.519 --> 00:03:22.630 align:start position:0%
demographics and county classifications
the<00:03:19.680><c> combined</c><00:03:20.080><c> data</c><00:03:20.400><c> had</c><00:03:20.800><c> over</c><00:03:21.120><c> 200</c><00:03:21.680><c> variables</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
the combined data had over 200 variables
 

00:03:22.640 --> 00:03:24.470 align:start position:0%
the combined data had over 200 variables
but<00:03:23.040><c> some</c><00:03:23.200><c> of</c><00:03:23.280><c> them</c><00:03:23.440><c> were</c><00:03:23.680><c> the</c><00:03:23.760><c> same</c><00:03:24.000><c> variable</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
but some of them were the same variable
 

00:03:24.480 --> 00:03:26.550 align:start position:0%
but some of them were the same variable
measured<00:03:24.959><c> at</c><00:03:25.120><c> different</c><00:03:25.519><c> times</c>

00:03:26.550 --> 00:03:26.560 align:start position:0%
measured at different times
 

00:03:26.560 --> 00:03:29.030 align:start position:0%
measured at different times
and<00:03:26.799><c> so</c><00:03:27.200><c> we</c><00:03:27.440><c> only</c><00:03:27.760><c> took</c><00:03:28.000><c> the</c><00:03:28.080><c> most</c><00:03:28.400><c> recent</c><00:03:28.720><c> one</c>

00:03:29.030 --> 00:03:29.040 align:start position:0%
and so we only took the most recent one
 

00:03:29.040 --> 00:03:30.869 align:start position:0%
and so we only took the most recent one
for<00:03:29.200><c> example</c>

00:03:30.869 --> 00:03:30.879 align:start position:0%
for example
 

00:03:30.879 --> 00:03:32.869 align:start position:0%
for example
there<00:03:31.120><c> was</c><00:03:31.440><c> unemployment</c><00:03:32.000><c> rate</c><00:03:32.239><c> 2010</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
there was unemployment rate 2010
 

00:03:32.879 --> 00:03:34.710 align:start position:0%
there was unemployment rate 2010
unemployment<00:03:33.360><c> rate</c><00:03:33.599><c> 2011</c>

00:03:34.710 --> 00:03:34.720 align:start position:0%
unemployment rate 2011
 

00:03:34.720 --> 00:03:36.869 align:start position:0%
unemployment rate 2011
and<00:03:34.959><c> so</c><00:03:35.200><c> on</c><00:03:35.760><c> and</c><00:03:35.920><c> so</c><00:03:36.159><c> we</c><00:03:36.319><c> only</c><00:03:36.640><c> took</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
and so on and so we only took
 

00:03:36.879 --> 00:03:39.190 align:start position:0%
and so on and so we only took
unemployment<00:03:37.519><c> rate</c><00:03:37.840><c> 2019</c>

00:03:39.190 --> 00:03:39.200 align:start position:0%
unemployment rate 2019
 

00:03:39.200 --> 00:03:42.390 align:start position:0%
unemployment rate 2019
and<00:03:39.680><c> um</c><00:03:40.400><c> got</c><00:03:40.640><c> rid</c><00:03:40.799><c> of</c><00:03:40.879><c> the</c><00:03:40.959><c> rest</c>

00:03:42.390 --> 00:03:42.400 align:start position:0%
and um got rid of the rest
 

00:03:42.400 --> 00:03:45.589 align:start position:0%
and um got rid of the rest
we<00:03:42.640><c> also</c><00:03:44.400><c> removed</c><00:03:45.040><c> any</c>

00:03:45.589 --> 00:03:45.599 align:start position:0%
we also removed any
 

00:03:45.599 --> 00:03:47.670 align:start position:0%
we also removed any
highly<00:03:46.000><c> correlated</c><00:03:46.560><c> variables</c><00:03:47.360><c> so</c><00:03:47.519><c> for</c>

00:03:47.670 --> 00:03:47.680 align:start position:0%
highly correlated variables so for
 

00:03:47.680 --> 00:03:49.030 align:start position:0%
highly correlated variables so for
example<00:03:48.159><c> we</c><00:03:48.319><c> removed</c>

00:03:49.030 --> 00:03:49.040 align:start position:0%
example we removed
 

00:03:49.040 --> 00:03:51.350 align:start position:0%
example we removed
uh<00:03:49.440><c> the</c><00:03:49.599><c> total</c><00:03:49.920><c> how</c><00:03:50.560><c> the</c><00:03:50.720><c> total</c><00:03:51.040><c> number</c><00:03:51.280><c> of</c>

00:03:51.350 --> 00:03:51.360 align:start position:0%
uh the total how the total number of
 

00:03:51.360 --> 00:03:52.229 align:start position:0%
uh the total how the total number of
households<00:03:52.000><c> in</c>

00:03:52.229 --> 00:03:52.239 align:start position:0%
households in
 

00:03:52.239 --> 00:03:55.589 align:start position:0%
households in
a<00:03:52.400><c> county</c><00:03:53.840><c> in</c><00:03:54.080><c> favor</c><00:03:54.560><c> of</c><00:03:55.120><c> the</c><00:03:55.280><c> total</c>

00:03:55.589 --> 00:03:55.599 align:start position:0%
a county in favor of the total
 

00:03:55.599 --> 00:03:57.190 align:start position:0%
a county in favor of the total
population<00:03:56.159><c> size</c><00:03:56.400><c> of</c><00:03:56.560><c> the</c><00:03:56.640><c> county</c>

00:03:57.190 --> 00:03:57.200 align:start position:0%
population size of the county
 

00:03:57.200 --> 00:03:59.589 align:start position:0%
population size of the county
because<00:03:57.519><c> they</c><00:03:57.599><c> were</c><00:03:57.840><c> 0.996</c><00:03:58.959><c> correlated</c><00:03:59.439><c> with</c>

00:03:59.589 --> 00:03:59.599 align:start position:0%
because they were 0.996 correlated with
 

00:03:59.599 --> 00:04:01.589 align:start position:0%
because they were 0.996 correlated with
each<00:03:59.760><c> other</c>

00:04:01.589 --> 00:04:01.599 align:start position:0%
each other
 

00:04:01.599 --> 00:04:04.710 align:start position:0%
each other
finally<00:04:02.640><c> we</c><00:04:03.040><c> removed</c><00:04:03.519><c> any</c><00:04:03.760><c> other</c><00:04:04.159><c> redundant</c>

00:04:04.710 --> 00:04:04.720 align:start position:0%
finally we removed any other redundant
 

00:04:04.720 --> 00:04:05.350 align:start position:0%
finally we removed any other redundant
or

00:04:05.350 --> 00:04:05.360 align:start position:0%
or
 

00:04:05.360 --> 00:04:07.509 align:start position:0%
or
irrelevant<00:04:05.840><c> variables</c><00:04:06.480><c> and</c><00:04:06.799><c> ended</c><00:04:07.120><c> up</c><00:04:07.200><c> with</c>

00:04:07.509 --> 00:04:07.519 align:start position:0%
irrelevant variables and ended up with
 

00:04:07.519 --> 00:04:11.030 align:start position:0%
irrelevant variables and ended up with
40<00:04:07.840><c> socioeconomic</c><00:04:08.720><c> variables</c>

00:04:11.030 --> 00:04:11.040 align:start position:0%
40 socioeconomic variables
 

00:04:11.040 --> 00:04:12.869 align:start position:0%
40 socioeconomic variables
it's<00:04:11.280><c> also</c><00:04:11.599><c> clear</c><00:04:11.920><c> that</c><00:04:12.239><c> other</c><00:04:12.480><c> variables</c>

00:04:12.869 --> 00:04:12.879 align:start position:0%
it's also clear that other variables
 

00:04:12.879 --> 00:04:14.149 align:start position:0%
it's also clear that other variables
that<00:04:13.120><c> are</c><00:04:13.200><c> not</c><00:04:13.439><c> already</c><00:04:13.920><c> in</c><00:04:14.080><c> our</c>

00:04:14.149 --> 00:04:14.159 align:start position:0%
that are not already in our
 

00:04:14.159 --> 00:04:16.069 align:start position:0%
that are not already in our
socioeconomic<00:04:15.040><c> dataset</c>

00:04:16.069 --> 00:04:16.079 align:start position:0%
socioeconomic dataset
 

00:04:16.079 --> 00:04:18.469 align:start position:0%
socioeconomic dataset
like<00:04:16.799><c> the</c><00:04:17.040><c> phase</c><00:04:17.519><c> of</c><00:04:17.600><c> the</c><00:04:17.759><c> pandemic</c><00:04:18.239><c> that</c><00:04:18.400><c> a</c>

00:04:18.469 --> 00:04:18.479 align:start position:0%
like the phase of the pandemic that a
 

00:04:18.479 --> 00:04:19.909 align:start position:0%
like the phase of the pandemic that a
state<00:04:18.720><c> is</c><00:04:18.799><c> going</c><00:04:19.120><c> through</c>

00:04:19.909 --> 00:04:19.919 align:start position:0%
state is going through
 

00:04:19.919 --> 00:04:22.550 align:start position:0%
state is going through
or<00:04:20.639><c> the</c><00:04:21.040><c> lockdown</c><00:04:21.519><c> and</c><00:04:21.680><c> social</c><00:04:22.000><c> distancing</c>

00:04:22.550 --> 00:04:22.560 align:start position:0%
or the lockdown and social distancing
 

00:04:22.560 --> 00:04:24.150 align:start position:0%
or the lockdown and social distancing
policies<00:04:23.120><c> of</c><00:04:23.280><c> that</c><00:04:23.520><c> sea</c>

00:04:24.150 --> 00:04:24.160 align:start position:0%
policies of that sea
 

00:04:24.160 --> 00:04:26.790 align:start position:0%
policies of that sea
can<00:04:24.400><c> also</c><00:04:24.720><c> strongly</c><00:04:25.199><c> affect</c><00:04:25.759><c> the</c><00:04:26.400><c> county</c>

00:04:26.790 --> 00:04:26.800 align:start position:0%
can also strongly affect the county
 

00:04:26.800 --> 00:04:27.350 align:start position:0%
can also strongly affect the county
level

00:04:27.350 --> 00:04:27.360 align:start position:0%
level
 

00:04:27.360 --> 00:04:29.990 align:start position:0%
level
death<00:04:27.680><c> rate</c><00:04:28.400><c> and</c><00:04:28.560><c> so</c><00:04:28.800><c> in</c><00:04:28.960><c> order</c><00:04:29.280><c> to</c><00:04:29.680><c> account</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
death rate and so in order to account
 

00:04:30.000 --> 00:04:32.550 align:start position:0%
death rate and so in order to account
for<00:04:30.240><c> differences</c><00:04:30.880><c> between</c><00:04:31.280><c> states</c>

00:04:32.550 --> 00:04:32.560 align:start position:0%
for differences between states
 

00:04:32.560 --> 00:04:34.790 align:start position:0%
for differences between states
and<00:04:32.800><c> concentrate</c><00:04:33.440><c> just</c><00:04:33.759><c> on</c><00:04:33.919><c> the</c><00:04:34.080><c> effect</c><00:04:34.479><c> of</c>

00:04:34.790 --> 00:04:34.800 align:start position:0%
and concentrate just on the effect of
 

00:04:34.800 --> 00:04:37.270 align:start position:0%
and concentrate just on the effect of
county<00:04:35.120><c> level</c><00:04:35.440><c> socioeconomic</c><00:04:36.320><c> variables</c>

00:04:37.270 --> 00:04:37.280 align:start position:0%
county level socioeconomic variables
 

00:04:37.280 --> 00:04:39.350 align:start position:0%
county level socioeconomic variables
we<00:04:37.440><c> made</c><00:04:37.680><c> sure</c><00:04:38.000><c> to</c><00:04:38.240><c> always</c><00:04:38.479><c> include</c><00:04:38.880><c> state</c><00:04:39.199><c> as</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
we made sure to always include state as
 

00:04:39.360 --> 00:04:40.870 align:start position:0%
we made sure to always include state as
an<00:04:39.440><c> explanatory</c><00:04:40.160><c> variable</c>

00:04:40.870 --> 00:04:40.880 align:start position:0%
an explanatory variable
 

00:04:40.880 --> 00:04:43.909 align:start position:0%
an explanatory variable
in<00:04:41.040><c> all</c><00:04:41.199><c> of</c><00:04:41.280><c> our</c><00:04:41.440><c> analyses</c>

00:04:43.909 --> 00:04:43.919 align:start position:0%
 
 

00:04:43.919 --> 00:04:46.070 align:start position:0%
 
to<00:04:44.080><c> control</c><00:04:44.639><c> for</c><00:04:45.120><c> any</c><00:04:45.360><c> state-related</c>

00:04:46.070 --> 00:04:46.080 align:start position:0%
to control for any state-related
 

00:04:46.080 --> 00:04:49.270 align:start position:0%
to control for any state-related
variability

00:04:49.270 --> 00:04:49.280 align:start position:0%
 
 

00:04:49.280 --> 00:04:51.350 align:start position:0%
 
we<00:04:49.440><c> merged</c><00:04:49.840><c> the</c><00:04:49.919><c> two</c><00:04:50.160><c> data</c><00:04:50.479><c> sets</c><00:04:50.880><c> and</c><00:04:50.960><c> then</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
we merged the two data sets and then
 

00:04:51.360 --> 00:04:52.950 align:start position:0%
we merged the two data sets and then
moved<00:04:51.759><c> on</c><00:04:52.080><c> to</c>

00:04:52.950 --> 00:04:52.960 align:start position:0%
moved on to
 

00:04:52.960 --> 00:04:56.150 align:start position:0%
moved on to
modeling<00:04:54.320><c> we</c><00:04:54.639><c> started</c><00:04:55.040><c> with</c><00:04:55.360><c> the</c><00:04:55.520><c> exploratory</c>

00:04:56.150 --> 00:04:56.160 align:start position:0%
modeling we started with the exploratory
 

00:04:56.160 --> 00:04:57.189 align:start position:0%
modeling we started with the exploratory
data<00:04:56.479><c> analysis</c>

00:04:57.189 --> 00:04:57.199 align:start position:0%
data analysis
 

00:04:57.199 --> 00:04:59.189 align:start position:0%
data analysis
and<00:04:57.360><c> then</c><00:04:57.600><c> applied</c><00:04:58.000><c> statistical</c><00:04:58.880><c> data</c>

00:04:59.189 --> 00:04:59.199 align:start position:0%
and then applied statistical data
 

00:04:59.199 --> 00:05:04.230 align:start position:0%
and then applied statistical data
analysis<00:04:59.759><c> methods</c>

00:05:04.230 --> 00:05:04.240 align:start position:0%
 
 

00:05:04.240 --> 00:05:06.790 align:start position:0%
 
here<00:05:04.639><c> are</c><00:05:04.720><c> the</c><00:05:04.880><c> maps</c><00:05:05.360><c> of</c><00:05:05.759><c> state</c><00:05:06.160><c> death</c><00:05:06.400><c> count</c>

00:05:06.790 --> 00:05:06.800 align:start position:0%
here are the maps of state death count
 

00:05:06.800 --> 00:05:09.110 align:start position:0%
here are the maps of state death count
and<00:05:07.039><c> death</c><00:05:07.280><c> rate</c>

00:05:09.110 --> 00:05:09.120 align:start position:0%
and death rate
 

00:05:09.120 --> 00:05:12.469 align:start position:0%
and death rate
both<00:05:09.600><c> on</c><00:05:10.080><c> august</c><00:05:10.560><c> 19</c><00:05:11.199><c> 2020</c><00:05:11.759><c> which</c><00:05:12.000><c> is</c><00:05:12.080><c> the</c><00:05:12.240><c> day</c>

00:05:12.469 --> 00:05:12.479 align:start position:0%
both on august 19 2020 which is the day
 

00:05:12.479 --> 00:05:14.550 align:start position:0%
both on august 19 2020 which is the day
that<00:05:12.639><c> i</c><00:05:12.880><c> downloaded</c><00:05:13.360><c> the</c><00:05:13.520><c> data</c>

00:05:14.550 --> 00:05:14.560 align:start position:0%
that i downloaded the data
 

00:05:14.560 --> 00:05:16.710 align:start position:0%
that i downloaded the data
um<00:05:15.360><c> so</c><00:05:15.600><c> there's</c><00:05:15.759><c> a</c><00:05:15.840><c> clear</c><00:05:16.080><c> difference</c><00:05:16.400><c> between</c>

00:05:16.710 --> 00:05:16.720 align:start position:0%
um so there's a clear difference between
 

00:05:16.720 --> 00:05:18.790 align:start position:0%
um so there's a clear difference between
the<00:05:16.800><c> two</c><00:05:17.039><c> maps</c><00:05:17.440><c> but</c><00:05:17.680><c> in</c><00:05:17.759><c> this</c><00:05:18.080><c> case</c>

00:05:18.790 --> 00:05:18.800 align:start position:0%
the two maps but in this case
 

00:05:18.800 --> 00:05:20.150 align:start position:0%
the two maps but in this case
death<00:05:19.039><c> ray</c><00:05:19.360><c> is</c><00:05:19.440><c> the</c><00:05:19.520><c> more</c><00:05:19.680><c> appropriate</c>

00:05:20.150 --> 00:05:20.160 align:start position:0%
death ray is the more appropriate
 

00:05:20.160 --> 00:05:22.310 align:start position:0%
death ray is the more appropriate
variable<00:05:20.479><c> to</c><00:05:20.639><c> look</c><00:05:20.800><c> at</c><00:05:21.360><c> just</c><00:05:21.600><c> because</c><00:05:22.160><c> it</c>

00:05:22.310 --> 00:05:22.320 align:start position:0%
variable to look at just because it
 

00:05:22.320 --> 00:05:24.230 align:start position:0%
variable to look at just because it
takes<00:05:22.639><c> the</c><00:05:22.800><c> total</c><00:05:23.039><c> population</c><00:05:23.680><c> of</c><00:05:23.840><c> a</c><00:05:23.919><c> state</c>

00:05:24.230 --> 00:05:24.240 align:start position:0%
takes the total population of a state
 

00:05:24.240 --> 00:05:25.909 align:start position:0%
takes the total population of a state
into<00:05:24.479><c> account</c>

00:05:25.909 --> 00:05:25.919 align:start position:0%
into account
 

00:05:25.919 --> 00:05:28.310 align:start position:0%
into account
so<00:05:26.720><c> we</c><00:05:26.960><c> can</c><00:05:27.199><c> see</c><00:05:27.440><c> from</c><00:05:27.600><c> the</c><00:05:27.759><c> map</c><00:05:28.000><c> that</c><00:05:28.160><c> the</c>

00:05:28.310 --> 00:05:28.320 align:start position:0%
so we can see from the map that the
 

00:05:28.320 --> 00:05:33.510 align:start position:0%
so we can see from the map that the
northeast<00:05:28.880><c> was</c><00:05:29.120><c> very</c><00:05:29.360><c> heavily</c><00:05:29.680><c> affected</c>

00:05:33.510 --> 00:05:33.520 align:start position:0%
 
 

00:05:33.520 --> 00:05:36.310 align:start position:0%
 
and<00:05:33.759><c> here's</c><00:05:34.000><c> a</c><00:05:34.080><c> more</c><00:05:34.320><c> detailed</c><00:05:34.800><c> breakdown</c><00:05:35.919><c> of</c>

00:05:36.310 --> 00:05:36.320 align:start position:0%
and here's a more detailed breakdown of
 

00:05:36.320 --> 00:05:37.510 align:start position:0%
and here's a more detailed breakdown of
death<00:05:36.560><c> ray</c>

00:05:37.510 --> 00:05:37.520 align:start position:0%
death ray
 

00:05:37.520 --> 00:05:43.189 align:start position:0%
death ray
by<00:05:37.759><c> county</c>

00:05:43.189 --> 00:05:43.199 align:start position:0%
 
 

00:05:43.199 --> 00:05:45.270 align:start position:0%
 
this<00:05:43.360><c> histogram</c><00:05:44.000><c> of</c><00:05:44.240><c> death</c><00:05:44.479><c> rates</c><00:05:44.800><c> shows</c><00:05:45.120><c> that</c>

00:05:45.270 --> 00:05:45.280 align:start position:0%
this histogram of death rates shows that
 

00:05:45.280 --> 00:05:47.590 align:start position:0%
this histogram of death rates shows that
the<00:05:45.440><c> distribution</c><00:05:46.160><c> is</c><00:05:46.320><c> very</c><00:05:46.639><c> skewed</c>

00:05:47.590 --> 00:05:47.600 align:start position:0%
the distribution is very skewed
 

00:05:47.600 --> 00:05:49.590 align:start position:0%
the distribution is very skewed
so<00:05:47.759><c> we</c><00:05:47.919><c> needed</c><00:05:48.240><c> to</c><00:05:48.400><c> apply</c><00:05:48.720><c> a</c><00:05:48.800><c> transformation</c>

00:05:49.590 --> 00:05:49.600 align:start position:0%
so we needed to apply a transformation
 

00:05:49.600 --> 00:05:51.270 align:start position:0%
so we needed to apply a transformation
to<00:05:49.759><c> make</c><00:05:49.919><c> it</c><00:05:50.080><c> closer</c><00:05:50.400><c> to</c><00:05:50.560><c> normal</c>

00:05:51.270 --> 00:05:51.280 align:start position:0%
to make it closer to normal
 

00:05:51.280 --> 00:05:55.510 align:start position:0%
to make it closer to normal
in<00:05:51.440><c> order</c><00:05:51.759><c> to</c><00:05:52.000><c> get</c><00:05:52.240><c> better</c><00:05:52.560><c> modeling</c><00:05:52.880><c> results</c>

00:05:55.510 --> 00:05:55.520 align:start position:0%
 
 

00:05:55.520 --> 00:05:57.110 align:start position:0%
 
though<00:05:55.680><c> we</c><00:05:55.840><c> chose</c><00:05:56.160><c> to</c><00:05:56.400><c> apply</c><00:05:56.720><c> the</c><00:05:56.800><c> log</c>

00:05:57.110 --> 00:05:57.120 align:start position:0%
though we chose to apply the log
 

00:05:57.120 --> 00:06:01.749 align:start position:0%
though we chose to apply the log
transformation

00:06:01.749 --> 00:06:01.759 align:start position:0%
 
 

00:06:01.759 --> 00:06:03.909 align:start position:0%
 
the<00:06:01.919><c> resulting</c><00:06:02.400><c> distribution</c><00:06:03.440><c> looks</c>

00:06:03.909 --> 00:06:03.919 align:start position:0%
the resulting distribution looks
 

00:06:03.919 --> 00:06:05.350 align:start position:0%
the resulting distribution looks
reasonably<00:06:04.479><c> normal</c>

00:06:05.350 --> 00:06:05.360 align:start position:0%
reasonably normal
 

00:06:05.360 --> 00:06:08.309 align:start position:0%
reasonably normal
and<00:06:05.919><c> we</c><00:06:06.160><c> used</c><00:06:06.639><c> log</c><00:06:06.960><c> death</c><00:06:07.199><c> rate</c><00:06:07.600><c> as</c><00:06:08.000><c> our</c>

00:06:08.309 --> 00:06:08.319 align:start position:0%
and we used log death rate as our
 

00:06:08.319 --> 00:06:09.670 align:start position:0%
and we used log death rate as our
response<00:06:08.800><c> variable</c>

00:06:09.670 --> 00:06:09.680 align:start position:0%
response variable
 

00:06:09.680 --> 00:06:12.710 align:start position:0%
response variable
in<00:06:09.919><c> all</c><00:06:10.080><c> of</c><00:06:10.240><c> our</c><00:06:10.400><c> analyses</c><00:06:11.759><c> similarly</c>

00:06:12.710 --> 00:06:12.720 align:start position:0%
in all of our analyses similarly
 

00:06:12.720 --> 00:06:15.270 align:start position:0%
in all of our analyses similarly
we<00:06:12.960><c> also</c><00:06:13.360><c> transformed</c><00:06:14.160><c> any</c><00:06:14.479><c> highly</c><00:06:14.880><c> skewed</c>

00:06:15.270 --> 00:06:15.280 align:start position:0%
we also transformed any highly skewed
 

00:06:15.280 --> 00:06:16.870 align:start position:0%
we also transformed any highly skewed
explanatory<00:06:15.919><c> variables</c>

00:06:16.870 --> 00:06:16.880 align:start position:0%
explanatory variables
 

00:06:16.880 --> 00:06:24.150 align:start position:0%
explanatory variables
by<00:06:17.120><c> either</c><00:06:17.360><c> taking</c><00:06:17.680><c> the</c><00:06:17.840><c> log</c><00:06:18.160><c> or</c><00:06:18.400><c> capping</c><00:06:18.840><c> them</c>

00:06:24.150 --> 00:06:24.160 align:start position:0%
 
 

00:06:24.160 --> 00:06:26.790 align:start position:0%
 
to<00:06:24.319><c> gain</c><00:06:24.639><c> some</c><00:06:24.880><c> insight</c><00:06:25.360><c> on</c><00:06:25.600><c> the</c><00:06:25.919><c> importance</c>

00:06:26.790 --> 00:06:26.800 align:start position:0%
to gain some insight on the importance
 

00:06:26.800 --> 00:06:27.510 align:start position:0%
to gain some insight on the importance
of

00:06:27.510 --> 00:06:27.520 align:start position:0%
of
 

00:06:27.520 --> 00:06:30.150 align:start position:0%
of
certain<00:06:27.840><c> variables</c><00:06:28.720><c> we</c><00:06:29.039><c> did</c><00:06:29.280><c> lasso</c>

00:06:30.150 --> 00:06:30.160 align:start position:0%
certain variables we did lasso
 

00:06:30.160 --> 00:06:32.790 align:start position:0%
certain variables we did lasso
regression<00:06:30.639><c> on</c><00:06:30.720><c> them</c><00:06:31.039><c> to</c><00:06:31.360><c> pick</c><00:06:31.680><c> out</c><00:06:31.840><c> a</c><00:06:32.080><c> subset</c>

00:06:32.790 --> 00:06:32.800 align:start position:0%
regression on them to pick out a subset
 

00:06:32.800 --> 00:06:34.550 align:start position:0%
regression on them to pick out a subset
of<00:06:33.039><c> important</c><00:06:33.440><c> variables</c><00:06:33.840><c> for</c><00:06:34.000><c> linear</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
of important variables for linear
 

00:06:34.560 --> 00:06:36.710 align:start position:0%
of important variables for linear
regression<00:06:35.759><c> and</c><00:06:36.160><c> we</c><00:06:36.400><c> use</c>

00:06:36.710 --> 00:06:36.720 align:start position:0%
regression and we use
 

00:06:36.720 --> 00:06:39.189 align:start position:0%
regression and we use
machine<00:06:37.120><c> learning</c><00:06:37.360><c> methods</c><00:06:38.240><c> to</c><00:06:38.479><c> predict</c><00:06:38.880><c> code</c>

00:06:39.189 --> 00:06:39.199 align:start position:0%
machine learning methods to predict code
 

00:06:39.199 --> 00:06:42.469 align:start position:0%
machine learning methods to predict code
19<00:06:39.680><c> county</c><00:06:40.080><c> death</c><00:06:40.319><c> rate</c>

00:06:42.469 --> 00:06:42.479 align:start position:0%
19 county death rate
 

00:06:42.479 --> 00:06:45.670 align:start position:0%
19 county death rate
and<00:06:42.720><c> now</c><00:06:42.960><c> we</c><00:06:43.120><c> come</c><00:06:43.280><c> to</c><00:06:43.440><c> the</c><00:06:43.600><c> findings</c>

00:06:45.670 --> 00:06:45.680 align:start position:0%
and now we come to the findings
 

00:06:45.680 --> 00:06:48.469 align:start position:0%
and now we come to the findings
here<00:06:46.000><c> is</c><00:06:46.160><c> the</c><00:06:46.240><c> list</c><00:06:46.560><c> of</c><00:06:46.720><c> the</c><00:06:46.800><c> worst</c><00:06:47.120><c> states</c>

00:06:48.469 --> 00:06:48.479 align:start position:0%
here is the list of the worst states
 

00:06:48.479 --> 00:06:50.070 align:start position:0%
here is the list of the worst states
that<00:06:48.720><c> is</c><00:06:48.880><c> the</c><00:06:49.039><c> states</c><00:06:49.360><c> that</c><00:06:49.520><c> were</c><00:06:49.759><c> most</c>

00:06:50.070 --> 00:06:50.080 align:start position:0%
that is the states that were most
 

00:06:50.080 --> 00:06:53.350 align:start position:0%
that is the states that were most
heavily<00:06:50.560><c> impacted</c><00:06:51.039><c> by</c><00:06:51.199><c> covenant</c><00:06:51.520><c> 19</c>

00:06:53.350 --> 00:06:53.360 align:start position:0%
heavily impacted by covenant 19
 

00:06:53.360 --> 00:06:56.710 align:start position:0%
heavily impacted by covenant 19
and<00:06:53.680><c> here</c><00:06:53.919><c> is</c><00:06:54.080><c> the</c><00:06:54.240><c> list</c><00:06:54.479><c> of</c><00:06:54.639><c> the</c><00:06:54.720><c> best</c><00:06:54.960><c> states</c>

00:06:56.710 --> 00:06:56.720 align:start position:0%
and here is the list of the best states
 

00:06:56.720 --> 00:06:59.909 align:start position:0%
and here is the list of the best states
what<00:06:57.039><c> this</c><00:06:57.280><c> result</c><00:06:57.680><c> is</c><00:06:57.840><c> saying</c><00:06:58.160><c> that</c>

00:06:59.909 --> 00:06:59.919 align:start position:0%
what this result is saying that
 

00:06:59.919 --> 00:07:01.990 align:start position:0%
what this result is saying that
is<00:07:00.080><c> that</c><00:07:00.560><c> given</c><00:07:00.960><c> two</c><00:07:01.199><c> counties</c><00:07:01.680><c> with</c>

00:07:01.990 --> 00:07:02.000 align:start position:0%
is that given two counties with
 

00:07:02.000 --> 00:07:05.110 align:start position:0%
is that given two counties with
identical<00:07:02.639><c> socioeconomic</c><00:07:03.520><c> characteristics</c>

00:07:05.110 --> 00:07:05.120 align:start position:0%
identical socioeconomic characteristics
 

00:07:05.120 --> 00:07:07.670 align:start position:0%
identical socioeconomic characteristics
one<00:07:05.440><c> in</c><00:07:05.680><c> let's</c><00:07:06.000><c> say</c><00:07:06.479><c> new</c><00:07:06.720><c> jersey</c><00:07:07.280><c> which</c><00:07:07.520><c> was</c>

00:07:07.670 --> 00:07:07.680 align:start position:0%
one in let's say new jersey which was
 

00:07:07.680 --> 00:07:08.790 align:start position:0%
one in let's say new jersey which was
the<00:07:07.840><c> worst</c><00:07:08.160><c> state</c>

00:07:08.790 --> 00:07:08.800 align:start position:0%
the worst state
 

00:07:08.800 --> 00:07:10.629 align:start position:0%
the worst state
and<00:07:09.039><c> one</c><00:07:09.199><c> in</c><00:07:09.360><c> oregon</c><00:07:09.919><c> which</c><00:07:10.160><c> is</c><00:07:10.240><c> the</c><00:07:10.400><c> best</c>

00:07:10.629 --> 00:07:10.639 align:start position:0%
and one in oregon which is the best
 

00:07:10.639 --> 00:07:13.270 align:start position:0%
and one in oregon which is the best
state<00:07:11.520><c> uh</c><00:07:11.840><c> the</c><00:07:12.080><c> county</c><00:07:12.400><c> in</c><00:07:12.479><c> new</c><00:07:12.639><c> jersey</c><00:07:13.039><c> will</c>

00:07:13.270 --> 00:07:13.280 align:start position:0%
state uh the county in new jersey will
 

00:07:13.280 --> 00:07:15.029 align:start position:0%
state uh the county in new jersey will
still<00:07:13.520><c> have</c><00:07:13.680><c> a</c><00:07:13.759><c> higher</c><00:07:14.080><c> death</c><00:07:14.319><c> rate</c>

00:07:15.029 --> 00:07:15.039 align:start position:0%
still have a higher death rate
 

00:07:15.039 --> 00:07:17.430 align:start position:0%
still have a higher death rate
probably<00:07:15.520><c> due</c><00:07:15.759><c> to</c><00:07:16.080><c> state</c><00:07:16.319><c> variations</c><00:07:17.199><c> in</c>

00:07:17.430 --> 00:07:17.440 align:start position:0%
probably due to state variations in
 

00:07:17.440 --> 00:07:18.950 align:start position:0%
probably due to state variations in
pandemic<00:07:18.000><c> timeline</c>

00:07:18.950 --> 00:07:18.960 align:start position:0%
pandemic timeline
 

00:07:18.960 --> 00:07:20.790 align:start position:0%
pandemic timeline
social<00:07:19.280><c> distancing</c><00:07:19.840><c> policies</c><00:07:20.560><c> and</c>

00:07:20.790 --> 00:07:20.800 align:start position:0%
social distancing policies and
 

00:07:20.800 --> 00:07:24.550 align:start position:0%
social distancing policies and
geographical<00:07:21.520><c> location</c>

00:07:24.550 --> 00:07:24.560 align:start position:0%
 
 

00:07:24.560 --> 00:07:26.870 align:start position:0%
 
here<00:07:24.880><c> are</c><00:07:25.039><c> the</c><00:07:25.199><c> set</c><00:07:25.360><c> of</c><00:07:25.520><c> most</c><00:07:25.759><c> important</c>

00:07:26.870 --> 00:07:26.880 align:start position:0%
here are the set of most important
 

00:07:26.880 --> 00:07:29.029 align:start position:0%
here are the set of most important
variables<00:07:27.360><c> that</c><00:07:27.440><c> we</c><00:07:27.599><c> found</c><00:07:27.840><c> in</c><00:07:28.000><c> our</c><00:07:28.160><c> models</c>

00:07:29.029 --> 00:07:29.039 align:start position:0%
variables that we found in our models
 

00:07:29.039 --> 00:07:32.230 align:start position:0%
variables that we found in our models
there's<00:07:29.520><c> age</c><00:07:30.720><c> so</c><00:07:30.960><c> in</c><00:07:31.039><c> particular</c><00:07:31.759><c> the</c><00:07:32.000><c> higher</c>

00:07:32.230 --> 00:07:32.240 align:start position:0%
there's age so in particular the higher
 

00:07:32.240 --> 00:07:33.350 align:start position:0%
there's age so in particular the higher
the<00:07:32.319><c> percentage</c>

00:07:33.350 --> 00:07:33.360 align:start position:0%
the percentage
 

00:07:33.360 --> 00:07:36.550 align:start position:0%
the percentage
of<00:07:33.680><c> people</c><00:07:34.160><c> aged</c><00:07:34.720><c> 65</c><00:07:35.280><c> and</c><00:07:35.440><c> over</c><00:07:36.080><c> the</c><00:07:36.240><c> higher</c>

00:07:36.550 --> 00:07:36.560 align:start position:0%
of people aged 65 and over the higher
 

00:07:36.560 --> 00:07:37.830 align:start position:0%
of people aged 65 and over the higher
the<00:07:36.720><c> death</c><00:07:36.960><c> rate</c>

00:07:37.830 --> 00:07:37.840 align:start position:0%
the death rate
 

00:07:37.840 --> 00:07:41.110 align:start position:0%
the death rate
there<00:07:38.080><c> was</c><00:07:38.639><c> race</c><00:07:39.520><c> the</c><00:07:40.319><c> counties</c><00:07:40.720><c> with</c><00:07:40.960><c> high</c>

00:07:41.110 --> 00:07:41.120 align:start position:0%
there was race the counties with high
 

00:07:41.120 --> 00:07:43.110 align:start position:0%
there was race the counties with high
percentages<00:07:41.840><c> of</c><00:07:42.000><c> white</c><00:07:42.240><c> people</c>

00:07:43.110 --> 00:07:43.120 align:start position:0%
percentages of white people
 

00:07:43.120 --> 00:07:47.270 align:start position:0%
percentages of white people
had<00:07:44.160><c> lower</c><00:07:44.560><c> death</c><00:07:44.879><c> rates</c>

00:07:47.270 --> 00:07:47.280 align:start position:0%
 
 

00:07:47.280 --> 00:07:49.990 align:start position:0%
 
larger<00:07:47.759><c> population</c><00:07:48.400><c> size</c><00:07:48.720><c> and</c><00:07:48.879><c> density</c><00:07:49.759><c> were</c>

00:07:49.990 --> 00:07:50.000 align:start position:0%
larger population size and density were
 

00:07:50.000 --> 00:07:52.869 align:start position:0%
larger population size and density were
associated<00:07:50.720><c> with</c><00:07:50.960><c> higher</c><00:07:51.280><c> death</c><00:07:51.520><c> rate</c>

00:07:52.869 --> 00:07:52.879 align:start position:0%
associated with higher death rate
 

00:07:52.879 --> 00:07:54.550 align:start position:0%
associated with higher death rate
furthermore<00:07:53.599><c> counties</c><00:07:54.000><c> with</c><00:07:54.240><c> higher</c>

00:07:54.550 --> 00:07:54.560 align:start position:0%
furthermore counties with higher
 

00:07:54.560 --> 00:07:56.550 align:start position:0%
furthermore counties with higher
percentages<00:07:55.199><c> of</c><00:07:55.360><c> people</c><00:07:55.680><c> born</c><00:07:56.000><c> in</c><00:07:56.160><c> foreign</c>

00:07:56.550 --> 00:07:56.560 align:start position:0%
percentages of people born in foreign
 

00:07:56.560 --> 00:07:57.670 align:start position:0%
percentages of people born in foreign
countries

00:07:57.670 --> 00:07:57.680 align:start position:0%
countries
 

00:07:57.680 --> 00:08:01.749 align:start position:0%
countries
at<00:07:57.919><c> higher</c><00:07:58.240><c> death</c><00:07:58.560><c> rates</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
 
 

00:08:01.759 --> 00:08:03.990 align:start position:0%
 
the<00:08:01.919><c> percentage</c><00:08:02.560><c> of</c><00:08:02.800><c> the</c><00:08:02.960><c> county</c><00:08:03.280><c> population</c>

00:08:03.990 --> 00:08:04.000 align:start position:0%
the percentage of the county population
 

00:08:04.000 --> 00:08:04.950 align:start position:0%
the percentage of the county population
involved

00:08:04.950 --> 00:08:04.960 align:start position:0%
involved
 

00:08:04.960 --> 00:08:07.909 align:start position:0%
involved
in<00:08:05.440><c> different</c><00:08:06.479><c> job</c><00:08:06.800><c> types</c><00:08:07.199><c> also</c><00:08:07.520><c> played</c><00:08:07.759><c> an</c>

00:08:07.909 --> 00:08:07.919 align:start position:0%
in different job types also played an
 

00:08:07.919 --> 00:08:09.110 align:start position:0%
in different job types also played an
important<00:08:08.319><c> role</c>

00:08:09.110 --> 00:08:09.120 align:start position:0%
important role
 

00:08:09.120 --> 00:08:12.070 align:start position:0%
important role
and<00:08:09.360><c> similarly</c><00:08:10.000><c> the</c><00:08:10.160><c> high</c><00:08:10.400><c> creative</c><00:08:10.800><c> class</c><00:08:11.680><c> is</c>

00:08:12.070 --> 00:08:12.080 align:start position:0%
and similarly the high creative class is
 

00:08:12.080 --> 00:08:12.710 align:start position:0%
and similarly the high creative class is
a

00:08:12.710 --> 00:08:12.720 align:start position:0%
a
 

00:08:12.720 --> 00:08:14.629 align:start position:0%
a
county<00:08:13.039><c> classification</c><00:08:13.840><c> based</c><00:08:14.160><c> on</c><00:08:14.319><c> whether</c>

00:08:14.629 --> 00:08:14.639 align:start position:0%
county classification based on whether
 

00:08:14.639 --> 00:08:16.309 align:start position:0%
county classification based on whether
there's<00:08:14.960><c> a</c><00:08:15.039><c> high</c><00:08:15.280><c> percentage</c>

00:08:16.309 --> 00:08:16.319 align:start position:0%
there's a high percentage
 

00:08:16.319 --> 00:08:18.629 align:start position:0%
there's a high percentage
of<00:08:16.639><c> knowledge</c><00:08:17.039><c> workers</c><00:08:17.599><c> intellectuals</c><00:08:18.400><c> and</c>

00:08:18.629 --> 00:08:18.639 align:start position:0%
of knowledge workers intellectuals and
 

00:08:18.639 --> 00:08:19.670 align:start position:0%
of knowledge workers intellectuals and
various<00:08:19.039><c> type</c><00:08:19.199><c> of</c>

00:08:19.670 --> 00:08:19.680 align:start position:0%
various type of
 

00:08:19.680 --> 00:08:24.550 align:start position:0%
various type of
types<00:08:20.000><c> of</c><00:08:20.160><c> artists</c>

00:08:24.550 --> 00:08:24.560 align:start position:0%
 
 

00:08:24.560 --> 00:08:26.950 align:start position:0%
 
finally<00:08:25.120><c> we</c><00:08:25.360><c> applied</c><00:08:25.919><c> different</c><00:08:26.560><c> machine</c>

00:08:26.950 --> 00:08:26.960 align:start position:0%
finally we applied different machine
 

00:08:26.960 --> 00:08:28.710 align:start position:0%
finally we applied different machine
learning<00:08:27.199><c> methods</c><00:08:27.680><c> to</c><00:08:27.919><c> predict</c><00:08:28.319><c> the</c><00:08:28.400><c> log</c>

00:08:28.710 --> 00:08:28.720 align:start position:0%
learning methods to predict the log
 

00:08:28.720 --> 00:08:29.749 align:start position:0%
learning methods to predict the log
death<00:08:28.960><c> rate</c>

00:08:29.749 --> 00:08:29.759 align:start position:0%
death rate
 

00:08:29.759 --> 00:08:33.509 align:start position:0%
death rate
um<00:08:30.160><c> including</c><00:08:30.800><c> lasso</c><00:08:31.599><c> random</c><00:08:31.919><c> forest</c>

00:08:33.509 --> 00:08:33.519 align:start position:0%
um including lasso random forest
 

00:08:33.519 --> 00:08:36.949 align:start position:0%
um including lasso random forest
boosting<00:08:34.320><c> and</c><00:08:34.719><c> deep</c><00:08:34.959><c> learning</c>

00:08:36.949 --> 00:08:36.959 align:start position:0%
boosting and deep learning
 

00:08:36.959 --> 00:08:39.509 align:start position:0%
boosting and deep learning
on<00:08:37.360><c> our</c><00:08:37.599><c> data</c><00:08:37.919><c> the</c><00:08:38.080><c> random</c><00:08:38.479><c> forest</c><00:08:38.959><c> had</c><00:08:39.360><c> the</c>

00:08:39.509 --> 00:08:39.519 align:start position:0%
on our data the random forest had the
 

00:08:39.519 --> 00:08:40.870 align:start position:0%
on our data the random forest had the
best

00:08:40.870 --> 00:08:40.880 align:start position:0%
best
 

00:08:40.880 --> 00:08:47.269 align:start position:0%
best
test<00:08:41.200><c> set</c><00:08:41.440><c> mean</c><00:08:41.680><c> squared</c><00:08:42.080><c> error</c>

00:08:47.269 --> 00:08:47.279 align:start position:0%
 
 

00:08:47.279 --> 00:08:49.430 align:start position:0%
 
so<00:08:47.600><c> from</c><00:08:47.839><c> this</c><00:08:48.080><c> research</c><00:08:48.640><c> i've</c><00:08:48.959><c> gained</c><00:08:49.279><c> an</c>

00:08:49.430 --> 00:08:49.440 align:start position:0%
so from this research i've gained an
 

00:08:49.440 --> 00:08:50.310 align:start position:0%
so from this research i've gained an
appreciation

00:08:50.310 --> 00:08:50.320 align:start position:0%
appreciation
 

00:08:50.320 --> 00:08:52.949 align:start position:0%
appreciation
for<00:08:51.040><c> the</c><00:08:51.279><c> power</c><00:08:51.760><c> of</c><00:08:51.839><c> what</c><00:08:52.080><c> data</c><00:08:52.399><c> science</c><00:08:52.720><c> can</c>

00:08:52.949 --> 00:08:52.959 align:start position:0%
for the power of what data science can
 

00:08:52.959 --> 00:08:53.670 align:start position:0%
for the power of what data science can
do

00:08:53.670 --> 00:08:53.680 align:start position:0%
do
 

00:08:53.680 --> 00:08:55.750 align:start position:0%
do
and<00:08:54.000><c> i</c><00:08:54.160><c> look</c><00:08:54.320><c> forward</c><00:08:54.720><c> to</c><00:08:54.959><c> delving</c><00:08:55.360><c> deeper</c>

00:08:55.750 --> 00:08:55.760 align:start position:0%
and i look forward to delving deeper
 

00:08:55.760 --> 00:08:57.110 align:start position:0%
and i look forward to delving deeper
into<00:08:56.000><c> data</c><00:08:56.320><c> science</c>

00:08:57.110 --> 00:08:57.120 align:start position:0%
into data science
 

00:08:57.120 --> 00:09:00.150 align:start position:0%
into data science
and<00:08:57.360><c> continuing</c><00:08:58.000><c> to</c><00:08:58.240><c> work</c><00:08:58.640><c> to</c><00:08:58.800><c> use</c><00:08:59.200><c> data</c><00:08:59.920><c> to</c>

00:09:00.150 --> 00:09:00.160 align:start position:0%
and continuing to work to use data to
 

00:09:00.160 --> 00:09:02.150 align:start position:0%
and continuing to work to use data to
help<00:09:00.480><c> impact</c><00:09:00.800><c> society</c>

00:09:02.150 --> 00:09:02.160 align:start position:0%
help impact society
 

00:09:02.160 --> 00:09:08.870 align:start position:0%
help impact society
thank<00:09:02.320><c> you</c><00:09:02.560><c> for</c><00:09:02.800><c> listening</c><00:09:03.120><c> to</c><00:09:03.200><c> my</c><00:09:06.839><c> talk</c>

00:09:08.870 --> 00:09:08.880 align:start position:0%
thank you for listening to my talk
 

00:09:08.880 --> 00:09:10.959 align:start position:0%
thank you for listening to my talk
you

