# YouTube Fetcher Scripts Comparison: V28 vs V29

> **⚠️ IMPORTANT SCHEDULING NOTE**  
> **My PC runs V28 automatically every day at 19:30 (7:30 PM)**  
> This is the production script that handles daily 48-hour video fetching and processing.

## Executive Summary

The transition from V28 to V29 represents a **fundamental shift in operational scope and strategy**:

- **V28**: Production script focused on **recent content** (48-hour window)
- **V29**: Development/testing script designed for **historical data** (entire channel archives)

## 🎯 Primary Differences

### 1. **Scope of Video Fetching**

| Aspect | V28 (Production) | V29 (Development) |
|--------|------------------|-------------------|
| **Time Range** | Last 48 hours only | All videos from channel history |
| **Use Case** | Daily monitoring | Historical data collection / Testing |
| **Efficiency** | High (recent content) | Moderate (large datasets) |
| **Data Volume** | Low-Medium | High |

### 2. **Channel Lists Used**

| Script | Watchlist File | Channels | Purpose |
|--------|----------------|----------|----------|
| **V28** | `youtube_channels_Watchlist_last_48hours_videos.md` | **170+ channels** across 8 topics | Production monitoring |
| **V29** | `youtube_channels_Watchlist_download_test_all_channel.md` | **1 channel** (JustDario only) | Testing/Development |

**Key Insight**: V29 is configured for **testing with minimal data** - it only processes one channel!

#### V28 - 48 Hour Focus:
```python
def fetch_channel_videos(channel_id, max_videos=MAX_VIDEOS):
    """
    Fetches videos from the given channel ID that were uploaded in the last 48 hours.
    Uses an optimized approach to stop fetching once we find older videos.
    """
    date_48_hours_ago = datetime.now(timezone.utc) - timedelta(hours=48)
    date_after = date_48_hours_ago.strftime('%Y%m%d')
    
    ydl_opts = {
        'dateafter': date_after,  # Only fetch recent videos
        'playlistend': 30 if max_videos <= 0 else max_videos,
    }
```

#### V29 - Complete Archive:
```python
def fetch_channel_videos(channel_id, max_videos=MAX_VIDEOS):
    """
    Fetches all videos from the given channel ID.
    Uses an optimized approach to fetch in batches.
    """
    ydl_opts = {
        'playlistend': 300 if max_videos <= 0 else max_videos,  # No date restriction
    }
```

### 2. **Watchlist Configuration**

| Script | Watchlist File | Purpose |
|--------|----------------|---------|
| **V28** | `youtube_channels_Watchlist_last_48hours_videos.md` | Daily monitoring |
| **V29** | `youtube_channels_Watchlist_download_test_all_channel.md` | Testing/Archive |

### 3. **Processing Pipeline Architecture**

#### V28 Pipeline (Production):
```
Main Fetching → run_additional_scripts() → {
    ├── Gemini Processing (commented out)
    ├── Ollama Processing (active)
    └── Email Notifications
}
```

#### V29 Pipeline (Simplified):
```
Main Fetching → Direct Script Execution → {
    ├── Ollama Processing
    └── Email Notifications
}
```

### 4. **Script Structure Changes**

| Feature | V28 | V29 |
|---------|-----|-----|
| **Additional Scripts Function** | `run_additional_scripts()` | Inline execution |
| **Gemini Integration** | Commented out but present | Removed entirely |
| **Ollama Import** | Runtime execution | Import statement added |
| **Error Handling** | Centralized in function | Direct try/catch |

## 🔧 Technical Implementation Details

### Import Differences

**V29 New Imports:**
```python
import threading
import json
import tiktoken
# Import Ollama prompt templates
from llama31_promptsV3 import prompt_template
```

### Execution Flow Changes

#### V28 (Production Flow):
```python
async def run_additional_scripts():
    try:
        # Run Gemini script and wait for it to complete
        print("\n=== Running Gemini Processing Script ===")
        # await run_script("gemini_supabase_query_pages_V8B.py")
        
        # Run Ollama script instead
        print("\n=== Running Ollama Processing Script ===")
        await run_script("ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py")
        
    except Exception as e:
        logging.error(f"Error in Gemini/Ollama script: {e}")
    
    # Always continue to email script, even if Gemini/Ollama script had errors
    try:
        print("\n=== Running Email Processing Script ===")
        await run_script("Just_email_V2.py")
        
    except Exception as e:
        logging.error(f"Error in email script: {e}")
```

#### V29 (Simplified Flow):
```python
# Run Ollama script for processing
try:
    print("\n=== Running Ollama Processing Script ===")
    loop.run_until_complete(run_script("ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py"))
    
    # Add a small delay before email script
    loop.run_until_complete(asyncio.sleep(2))
    
    # Run email script
    print("\n=== Running Email Processing Script ===")
    loop.run_until_complete(run_script("Just_email_V2.py"))
except Exception as e:
    logging.error(f"Error in additional scripts: {e}")
    # Continue execution, don't exit
```

## 📊 Data Fetching & Database Saving Logic

### **✅ IDENTICAL DATA PROCESSING**

Both V28 and V29 use **exactly the same code** for:

#### **Database Saving Function:**
```python
async def save_to_supabase(table_name, video_data):
    """Async function to save data to Supabase with proper error handling"""
    @supabase_api_call
    async def insert_video_data():
        response = await supabase.table(table_name).insert(video_data).execute()
        return response
    # ... identical error handling
```

#### **Video Data Structure:**
```python
video_data = {
    'id': str(uuid.uuid4()),
    'video_id': video_id,
    'channel_name': channel_name,
    'title': info.get('title', ''),
    'duration': info.get('duration', 0),
    'published_at': published_at.isoformat(),
    'transcript': transcript_result['formatted_text'],
    'processed': 'pending',
    'created_at': current_time.isoformat(),
    'email_sent': False,
    'summary': None,
    'keywords': None,
    'visual_description': None,
    'llm_response': None,
    'llm_call_date': None
}
```

#### **Transcript Fetching:**
- ✅ Same `fetch_transcript_yt_dlp()` function
- ✅ Same VTT file processing
- ✅ Same error handling and cleanup logic

#### **Database Schema:**
- ✅ Same table creation logic
- ✅ Same column definitions
- ✅ Same validation and constraints

### **Bottom Line on Data Processing:**
**The code for fetching, processing, and saving video data is 100% identical between V28 and V29.**

The only differences are:
1. **Time filtering** (48 hours vs all time)  
2. **Channel list** (170+ channels vs 1 test channel)
3. **Watchlist file** used

## 📊 Performance & Resource Implications

### V28 (Production):
- **CPU Usage**: Low-Medium (recent videos only)
- **Network Requests**: Minimal (48-hour filter)
- **Storage Growth**: Incremental
- **Execution Time**: 15-30 minutes typically
- **Database Load**: Light (new videos only)
- **Channels Processed**: 170+ channels across 8 topics

### V29 (Development):
- **CPU Usage**: Low (only 1 test channel)
- **Network Requests**: Moderate (all history for 1 channel)
- **Storage Growth**: Minimal (test environment)
- **Execution Time**: 5-15 minutes (single channel)
- **Database Load**: Light (testing with JustDario channel only)
- **Channels Processed**: 1 channel (JustDario) for testing

**Note**: V29 is currently configured as a **lightweight testing environment**, not the heavy bulk operation initially assumed.

## 🗂️ Data Management Strategy

### V28 Approach:
- **Incremental Updates**: Only processes new videos from last 48 hours
- **Duplicate Prevention**: Efficient checks against recent data
- **Transcript Retry Logic**: For recent failed fetches
- **Real-time Monitoring**: Daily automated execution

### V29 Approach:
- **Bulk Collection**: Processes entire channel histories
- **Historical Gaps**: Fills in missing archive data
- **Testing Environment**: Safe for experimenting with new features
- **One-time Operations**: Designed for occasional large-scale data collection

## 🚀 Use Case Scenarios

### When to Use V28:
- ✅ Daily automated monitoring
- ✅ Production environment
- ✅ Real-time content tracking
- ✅ Minimal resource usage
- ✅ Regular email updates

### When to Use V29:
- ✅ Initial channel setup
- ✅ Historical data backfill
- ✅ Testing new features
- ✅ Archive research projects
- ✅ Development environment

## ⚠️ Important Considerations

### V28 (Production Script):
- **Scheduled Execution**: Runs automatically at 19:30 daily
- **Reliability**: Optimized for consistent daily operation
- **Resource Efficiency**: Designed for minimal system impact
- **Error Recovery**: Robust handling of temporary failures

### V29 (Development Script):
- **Manual Execution**: Run only when needed
- **Resource Intensive**: May require dedicated time and resources
- **Testing Purpose**: Not intended for production scheduling
- **Comprehensive Collection**: May take hours to complete for large channels

## 🔄 Migration & Compatibility

Both scripts share:
- Core database schema
- Supabase integration
- Error logging mechanisms
- Rich console output
- Async processing architecture

**Key Compatibility Note**: V29 can safely run alongside V28 as they target different use cases and don't conflict in their operations.

## 🤖 Ollama Processing & Prompts

### **✅ SAME OLLAMA SCRIPT - DIFFERENT PROMPT IMPORTS**

Both V28 and V29 run **exactly the same Ollama processing script**:
- **Script**: `ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py`
- **Processing Logic**: Identical AI analysis and database updates
- **Output Format**: Same JSON structure for keywords, questions, insights, recommendations

### **✅ CLEANED UP: Import Optimization**

| Script | Ollama Script Uses | V29 Script Imports | Status |
|--------|-------------------|-------------------|---------|
| **V28** | `llama31_promptsV2` | ❌ No import | ✅ Clean |
| **V29** | `llama31_promptsV2` | ❌ Removed unused import | ✅ **FIXED** |

### **Current Prompt Usage Status:**

#### **Active & Used:**
- ✅ **`llama31_promptsV2`**: Used by `ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py`
- ✅ **`llama31_promptsV3`**: Used by `gemini_supabase_query_page_v9_all_time_videos.py` (Gemini processing)

#### **Import Cleanup Completed:**
- ❌ **Removed**: Unused `llama31_promptsV3` import from V29 script
- ✅ **Added**: Explanatory comment about where prompts are actually used

### **Bottom Line**
- **Processing Results**: Identical between V28 and V29
- **AI Analysis Quality**: Same prompt template (V2) used for both via Ollama script
- **Code Cleanliness**: ✅ **Improved** - Removed confusing dead code import
- **Documentation**: Added clear comments explaining the prompt architecture

## 📝 Recommendations

1. **Keep V28 as Production**: Continue automated daily execution at 19:30
2. **Use V29 for Setup**: Run when adding new channels or backfilling data
3. **Monitor Resources**: V29 should be run during off-peak hours
4. **Data Validation**: Cross-check V29 results before integrating with production data
5. **Backup Strategy**: Ensure database backups before running V29 on large channels
6. **✅ Code Cleanup Completed**: Removed unused `llama31_promptsV3` import from V29

### **Prompt Template Status Summary:**
- **`llama31_promptsV2`**: ✅ **Actively used** by Ollama processing (both V28 & V29)
- **`llama31_promptsV3`**: ✅ **Used** by Gemini processing scripts (when enabled)
- **V29 Import**: ✅ **Fixed** - Removed unused import and added documentation

---

**Generated on**: July 13, 2025  
**Script Versions Analyzed**: V28 (Production) & V29 (Development)  
**Analysis Focus**: Operational differences and use case optimization
