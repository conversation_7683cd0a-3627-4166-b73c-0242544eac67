WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.670 align:start position:0%
 
so<00:00:00.480><c> here's</c><00:00:00.780><c> the</c><00:00:00.960><c> script</c><00:00:01.140><c> that</c><00:00:01.439><c> we've</c><00:00:01.560><c> been</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
so here's the script that we've been
 

00:00:01.680 --> 00:00:04.490 align:start position:0%
so here's the script that we've been
using<00:00:01.979><c> so</c><00:00:02.220><c> far</c><00:00:02.399><c> we</c><00:00:03.179><c> have</c><00:00:03.240><c> our</c><00:00:03.480><c> config</c><00:00:03.899><c> to</c><00:00:04.319><c> find</c>

00:00:04.490 --> 00:00:04.500 align:start position:0%
using so far we have our config to find
 

00:00:04.500 --> 00:00:06.349 align:start position:0%
using so far we have our config to find
in<00:00:04.680><c> a</c><00:00:04.860><c> dictionary</c><00:00:05.220><c> and</c><00:00:05.759><c> we're</c><00:00:05.940><c> creating</c><00:00:06.180><c> a</c>

00:00:06.349 --> 00:00:06.359 align:start position:0%
in a dictionary and we're creating a
 

00:00:06.359 --> 00:00:09.410 align:start position:0%
in a dictionary and we're creating a
weights<00:00:06.660><c> and</c><00:00:06.720><c> biases</c><00:00:07.080><c> run</c><00:00:07.379><c> using</c><00:00:08.160><c> wand</c><00:00:08.820><c> B</c><00:00:09.000><c> dot</c>

00:00:09.410 --> 00:00:09.420 align:start position:0%
weights and biases run using wand B dot
 

00:00:09.420 --> 00:00:12.049 align:start position:0%
weights and biases run using wand B dot
init<00:00:10.200><c> we're</c><00:00:10.679><c> passing</c><00:00:11.099><c> our</c><00:00:11.280><c> config</c><00:00:11.639><c> to</c><00:00:11.880><c> that</c>

00:00:12.049 --> 00:00:12.059 align:start position:0%
init we're passing our config to that
 

00:00:12.059 --> 00:00:13.910 align:start position:0%
init we're passing our config to that
and<00:00:12.540><c> we're</c><00:00:12.780><c> referencing</c><00:00:13.139><c> that</c><00:00:13.440><c> config</c>

00:00:13.910 --> 00:00:13.920 align:start position:0%
and we're referencing that config
 

00:00:13.920 --> 00:00:15.829 align:start position:0%
and we're referencing that config
throughout<00:00:14.460><c> the</c><00:00:14.580><c> script</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
throughout the script
 

00:00:15.839 --> 00:00:17.750 align:start position:0%
throughout the script
and<00:00:16.199><c> then</c><00:00:16.320><c> when</c><00:00:16.500><c> we</c><00:00:16.619><c> want</c><00:00:16.740><c> to</c><00:00:16.920><c> log</c><00:00:17.100><c> metrics</c>

00:00:17.750 --> 00:00:17.760 align:start position:0%
and then when we want to log metrics
 

00:00:17.760 --> 00:00:19.670 align:start position:0%
and then when we want to log metrics
we're<00:00:18.180><c> passing</c><00:00:18.600><c> in</c><00:00:18.720><c> a</c><00:00:18.960><c> dictionary</c><00:00:19.260><c> of</c><00:00:19.560><c> the</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
we're passing in a dictionary of the
 

00:00:19.680 --> 00:00:21.650 align:start position:0%
we're passing in a dictionary of the
metrics<00:00:20.039><c> that</c><00:00:20.160><c> we're</c><00:00:20.279><c> trying</c><00:00:20.520><c> to</c><00:00:20.699><c> improve</c><00:00:21.060><c> and</c>

00:00:21.650 --> 00:00:21.660 align:start position:0%
metrics that we're trying to improve and
 

00:00:21.660 --> 00:00:23.330 align:start position:0%
metrics that we're trying to improve and
any<00:00:21.840><c> other</c><00:00:22.020><c> details</c><00:00:22.619><c> that</c><00:00:22.920><c> might</c><00:00:23.100><c> change</c>

00:00:23.330 --> 00:00:23.340 align:start position:0%
any other details that might change
 

00:00:23.340 --> 00:00:25.490 align:start position:0%
any other details that might change
throughout<00:00:23.939><c> training</c>

00:00:25.490 --> 00:00:25.500 align:start position:0%
throughout training
 

00:00:25.500 --> 00:00:27.529 align:start position:0%
throughout training
so<00:00:25.980><c> I'm</c><00:00:26.100><c> going</c><00:00:26.279><c> to</c><00:00:26.340><c> run</c><00:00:26.519><c> our</c><00:00:26.760><c> cell</c><00:00:26.939><c> and</c><00:00:27.240><c> weights</c>

00:00:27.529 --> 00:00:27.539 align:start position:0%
so I'm going to run our cell and weights
 

00:00:27.539 --> 00:00:29.269 align:start position:0%
so I'm going to run our cell and weights
and<00:00:27.599><c> biases</c><00:00:28.019><c> will</c><00:00:28.199><c> output</c><00:00:28.500><c> a</c><00:00:28.740><c> link</c><00:00:28.920><c> to</c><00:00:29.160><c> that</c>

00:00:29.269 --> 00:00:29.279 align:start position:0%
and biases will output a link to that
 

00:00:29.279 --> 00:00:31.970 align:start position:0%
and biases will output a link to that
runs<00:00:29.699><c> page</c><00:00:30.480><c> we</c><00:00:30.900><c> can</c><00:00:31.019><c> click</c><00:00:31.320><c> to</c><00:00:31.500><c> navigate</c><00:00:31.679><c> to</c>

00:00:31.970 --> 00:00:31.980 align:start position:0%
runs page we can click to navigate to
 

00:00:31.980 --> 00:00:34.610 align:start position:0%
runs page we can click to navigate to
that<00:00:32.160><c> run</c><00:00:32.399><c> and</c><00:00:33.300><c> view</c><00:00:33.480><c> our</c><00:00:33.899><c> way</c><00:00:34.020><c> to</c><00:00:34.140><c> biases</c>

00:00:34.610 --> 00:00:34.620 align:start position:0%
that run and view our way to biases
 

00:00:34.620 --> 00:00:35.750 align:start position:0%
that run and view our way to biases
workspace

00:00:35.750 --> 00:00:35.760 align:start position:0%
workspace
 

00:00:35.760 --> 00:00:37.490 align:start position:0%
workspace
once<00:00:36.300><c> we</c><00:00:36.360><c> navigate</c><00:00:36.540><c> to</c><00:00:36.840><c> the</c><00:00:37.020><c> run</c><00:00:37.140><c> in</c><00:00:37.380><c> the</c>

00:00:37.490 --> 00:00:37.500 align:start position:0%
once we navigate to the run in the
 

00:00:37.500 --> 00:00:39.470 align:start position:0%
once we navigate to the run in the
weights<00:00:37.739><c> and</c><00:00:37.860><c> biases</c><00:00:38.160><c> workspace</c><00:00:38.640><c> we</c><00:00:39.239><c> can</c><00:00:39.300><c> see</c>

00:00:39.470 --> 00:00:39.480 align:start position:0%
weights and biases workspace we can see
 

00:00:39.480 --> 00:00:41.209 align:start position:0%
weights and biases workspace we can see
our<00:00:39.660><c> metrics</c><00:00:40.079><c> streaming</c><00:00:40.559><c> it</c><00:00:40.680><c> in</c><00:00:40.739><c> real</c><00:00:40.980><c> time</c>

00:00:41.209 --> 00:00:41.219 align:start position:0%
our metrics streaming it in real time
 

00:00:41.219 --> 00:00:43.250 align:start position:0%
our metrics streaming it in real time
there<00:00:42.000><c> are</c><00:00:42.120><c> some</c><00:00:42.300><c> plots</c><00:00:42.660><c> created</c>

00:00:43.250 --> 00:00:43.260 align:start position:0%
there are some plots created
 

00:00:43.260 --> 00:00:45.229 align:start position:0%
there are some plots created
automatically<00:00:43.800><c> for</c><00:00:44.040><c> us</c><00:00:44.219><c> to</c><00:00:44.820><c> display</c><00:00:44.940><c> our</c>

00:00:45.229 --> 00:00:45.239 align:start position:0%
automatically for us to display our
 

00:00:45.239 --> 00:00:47.930 align:start position:0%
automatically for us to display our
logged<00:00:45.600><c> data</c><00:00:45.960><c> unlike</c><00:00:46.920><c> static</c><00:00:47.280><c> plots</c><00:00:47.579><c> you</c>

00:00:47.930 --> 00:00:47.940 align:start position:0%
logged data unlike static plots you
 

00:00:47.940 --> 00:00:49.910 align:start position:0%
logged data unlike static plots you
might<00:00:48.120><c> make</c><00:00:48.300><c> in</c><00:00:48.600><c> matplotlib</c><00:00:49.440><c> or</c><00:00:49.680><c> something</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
might make in matplotlib or something
 

00:00:49.920 --> 00:00:52.670 align:start position:0%
might make in matplotlib or something
like<00:00:50.219><c> that</c><00:00:50.399><c> we</c><00:00:51.180><c> can</c><00:00:51.360><c> go</c><00:00:51.539><c> in</c><00:00:51.719><c> and</c><00:00:51.960><c> interactively</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
like that we can go in and interactively
 

00:00:52.680 --> 00:00:54.830 align:start position:0%
like that we can go in and interactively
customize<00:00:53.160><c> them</c><00:00:53.460><c> to</c><00:00:53.820><c> display</c><00:00:54.000><c> our</c><00:00:54.300><c> metrics</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
customize them to display our metrics
 

00:00:54.840 --> 00:00:56.750 align:start position:0%
customize them to display our metrics
how<00:00:55.140><c> we</c><00:00:55.320><c> would</c><00:00:55.500><c> like</c><00:00:55.680><c> by</c><00:00:56.280><c> adding</c><00:00:56.579><c> some</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
how we would like by adding some
 

00:00:56.760 --> 00:00:58.910 align:start position:0%
how we would like by adding some
smoothing<00:00:57.120><c> or</c><00:00:57.420><c> changing</c><00:00:57.899><c> our</c><00:00:57.960><c> axes</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
smoothing or changing our axes
 

00:00:58.920 --> 00:01:01.250 align:start position:0%
smoothing or changing our axes
we<00:00:59.399><c> can</c><00:00:59.579><c> also</c><00:00:59.820><c> add</c><00:01:00.000><c> new</c><00:01:00.239><c> panels</c><00:01:00.600><c> to</c><00:01:00.899><c> visualize</c>

00:01:01.250 --> 00:01:01.260 align:start position:0%
we can also add new panels to visualize
 

00:01:01.260 --> 00:01:03.709 align:start position:0%
we can also add new panels to visualize
our<00:01:01.440><c> logs</c><00:01:01.800><c> data</c><00:01:02.160><c> in</c><00:01:02.640><c> other</c><00:01:02.820><c> ways</c><00:01:03.180><c> to</c><00:01:03.539><c> dive</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
our logs data in other ways to dive
 

00:01:03.719 --> 00:01:06.109 align:start position:0%
our logs data in other ways to dive
deeper<00:01:04.080><c> into</c><00:01:04.199><c> our</c><00:01:04.379><c> runs</c><00:01:04.739><c> weights</c><00:01:05.640><c> and</c><00:01:05.700><c> biases</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
deeper into our runs weights and biases
 

00:01:06.119 --> 00:01:07.190 align:start position:0%
deeper into our runs weights and biases
supports<00:01:06.600><c> lots</c><00:01:06.960><c> of</c><00:01:07.020><c> different</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
supports lots of different
 

00:01:07.200 --> 00:01:08.810 align:start position:0%
supports lots of different
visualizations<00:01:07.799><c> that</c><00:01:08.220><c> are</c><00:01:08.340><c> helpful</c><00:01:08.580><c> when</c>

00:01:08.810 --> 00:01:08.820 align:start position:0%
visualizations that are helpful when
 

00:01:08.820 --> 00:01:11.390 align:start position:0%
visualizations that are helpful when
running<00:01:08.939><c> machine</c><00:01:09.240><c> learning</c><00:01:09.659><c> projects</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
running machine learning projects
 

00:01:11.400 --> 00:01:13.789 align:start position:0%
running machine learning projects
weights<00:01:12.119><c> and</c><00:01:12.180><c> biases</c><00:01:12.659><c> also</c><00:01:13.020><c> captures</c><00:01:13.380><c> system</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
weights and biases also captures system
 

00:01:13.799 --> 00:01:16.010 align:start position:0%
weights and biases also captures system
metrics<00:01:14.340><c> automatically</c><00:01:14.820><c> like</c><00:01:15.479><c> GPU</c>

00:01:16.010 --> 00:01:16.020 align:start position:0%
metrics automatically like GPU
 

00:01:16.020 --> 00:01:18.649 align:start position:0%
metrics automatically like GPU
utilization<00:01:16.560><c> and</c><00:01:16.920><c> CPU</c><00:01:17.340><c> utilization</c>

00:01:18.649 --> 00:01:18.659 align:start position:0%
utilization and CPU utilization
 

00:01:18.659 --> 00:01:20.690 align:start position:0%
utilization and CPU utilization
these<00:01:19.140><c> are</c><00:01:19.260><c> useful</c><00:01:19.500><c> to</c><00:01:19.680><c> have</c><00:01:19.920><c> to</c><00:01:20.460><c> make</c><00:01:20.580><c> sure</c>

00:01:20.690 --> 00:01:20.700 align:start position:0%
these are useful to have to make sure
 

00:01:20.700 --> 00:01:21.950 align:start position:0%
these are useful to have to make sure
you're<00:01:20.880><c> getting</c><00:01:21.119><c> the</c><00:01:21.360><c> most</c><00:01:21.420><c> out</c><00:01:21.780><c> of</c><00:01:21.900><c> your</c>

00:01:21.950 --> 00:01:21.960 align:start position:0%
you're getting the most out of your
 

00:01:21.960 --> 00:01:23.630 align:start position:0%
you're getting the most out of your
expensive<00:01:22.380><c> compute</c>

00:01:23.630 --> 00:01:23.640 align:start position:0%
expensive compute
 

00:01:23.640 --> 00:01:25.670 align:start position:0%
expensive compute
if<00:01:23.939><c> we</c><00:01:24.060><c> navigate</c><00:01:24.180><c> to</c><00:01:24.540><c> the</c><00:01:24.659><c> overview</c><00:01:24.960><c> tab</c><00:01:25.380><c> we</c>

00:01:25.670 --> 00:01:25.680 align:start position:0%
if we navigate to the overview tab we
 

00:01:25.680 --> 00:01:27.590 align:start position:0%
if we navigate to the overview tab we
can<00:01:25.860><c> see</c><00:01:26.040><c> useful</c><00:01:26.580><c> information</c><00:01:26.759><c> that</c><00:01:27.240><c> weights</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
can see useful information that weights
 

00:01:27.600 --> 00:01:29.390 align:start position:0%
can see useful information that weights
and<00:01:27.659><c> biases</c><00:01:28.080><c> has</c><00:01:28.439><c> automatically</c><00:01:28.979><c> captured</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
and biases has automatically captured
 

00:01:29.400 --> 00:01:31.550 align:start position:0%
and biases has automatically captured
that<00:01:29.700><c> helps</c><00:01:29.939><c> with</c><00:01:30.060><c> reproducibility</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
that helps with reproducibility
 

00:01:31.560 --> 00:01:34.249 align:start position:0%
that helps with reproducibility
I<00:01:31.979><c> can</c><00:01:32.220><c> see</c><00:01:32.400><c> which</c><00:01:32.939><c> environment</c><00:01:33.659><c> I'm</c><00:01:33.900><c> using</c>

00:01:34.249 --> 00:01:34.259 align:start position:0%
I can see which environment I'm using
 

00:01:34.259 --> 00:01:37.390 align:start position:0%
I can see which environment I'm using
who<00:01:34.619><c> ran</c><00:01:34.860><c> the</c><00:01:35.100><c> code</c><00:01:35.340><c> when</c><00:01:36.240><c> on</c><00:01:36.720><c> what</c><00:01:36.960><c> machine</c>

00:01:37.390 --> 00:01:37.400 align:start position:0%
who ran the code when on what machine
 

00:01:37.400 --> 00:01:41.929 align:start position:0%
who ran the code when on what machine
and<00:01:38.400><c> what</c><00:01:38.579><c> command</c><00:01:38.880><c> was</c><00:01:39.060><c> used</c><00:01:39.240><c> to</c><00:01:39.479><c> run</c><00:01:39.600><c> it</c>

00:01:41.929 --> 00:01:41.939 align:start position:0%
 
 

00:01:41.939 --> 00:01:44.929 align:start position:0%
 
is<00:01:42.299><c> also</c><00:01:42.600><c> where</c><00:01:42.780><c> we</c><00:01:43.020><c> can</c><00:01:43.140><c> see</c><00:01:43.439><c> the</c><00:01:44.340><c> config</c><00:01:44.759><c> that</c>

00:01:44.929 --> 00:01:44.939 align:start position:0%
is also where we can see the config that
 

00:01:44.939 --> 00:01:46.910 align:start position:0%
is also where we can see the config that
we<00:01:45.119><c> passed</c><00:01:45.479><c> in</c><00:01:45.600><c> and</c><00:01:46.079><c> verify</c><00:01:46.439><c> that</c><00:01:46.560><c> weights</c><00:01:46.860><c> and</c>

00:01:46.910 --> 00:01:46.920 align:start position:0%
we passed in and verify that weights and
 

00:01:46.920 --> 00:01:48.830 align:start position:0%
we passed in and verify that weights and
biased<00:01:47.220><c> is</c><00:01:47.340><c> keeping</c><00:01:47.700><c> track</c><00:01:47.880><c> of</c><00:01:48.360><c> all</c><00:01:48.540><c> of</c><00:01:48.659><c> those</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
biased is keeping track of all of those
 

00:01:48.840 --> 00:01:50.810 align:start position:0%
biased is keeping track of all of those
hyper<00:01:49.320><c> parameters</c>

00:01:50.810 --> 00:01:50.820 align:start position:0%
hyper parameters
 

00:01:50.820 --> 00:01:53.149 align:start position:0%
hyper parameters
so<00:01:51.180><c> this</c><00:01:51.299><c> is</c><00:01:51.420><c> a</c><00:01:51.600><c> runs</c><00:01:51.899><c> page</c><00:01:52.140><c> but</c><00:01:52.619><c> we</c><00:01:52.860><c> also</c><00:01:53.040><c> have</c>

00:01:53.149 --> 00:01:53.159 align:start position:0%
so this is a runs page but we also have
 

00:01:53.159 --> 00:01:54.770 align:start position:0%
so this is a runs page but we also have
the<00:01:53.280><c> option</c><00:01:53.460><c> to</c><00:01:53.759><c> visit</c><00:01:53.880><c> the</c><00:01:54.180><c> project</c><00:01:54.299><c> page</c>

00:01:54.770 --> 00:01:54.780 align:start position:0%
the option to visit the project page
 

00:01:54.780 --> 00:01:56.270 align:start position:0%
the option to visit the project page
which<00:01:55.259><c> you</c><00:01:55.380><c> can</c><00:01:55.500><c> find</c><00:01:55.680><c> by</c><00:01:55.920><c> using</c><00:01:56.159><c> the</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
which you can find by using the
 

00:01:56.280 --> 00:01:58.429 align:start position:0%
which you can find by using the
breadcrumbs<00:01:56.820><c> in</c><00:01:56.939><c> the</c><00:01:57.060><c> navigation</c><00:01:57.360><c> bar</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
breadcrumbs in the navigation bar
 

00:01:58.439 --> 00:02:00.350 align:start position:0%
breadcrumbs in the navigation bar
if<00:01:58.799><c> we</c><00:01:58.920><c> navigate</c><00:01:59.040><c> to</c><00:01:59.399><c> the</c><00:01:59.520><c> project</c><00:01:59.640><c> page</c><00:02:00.060><c> we'll</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
if we navigate to the project page we'll
 

00:02:00.360 --> 00:02:02.690 align:start position:0%
if we navigate to the project page we'll
be<00:02:00.540><c> able</c><00:02:00.659><c> to</c><00:02:00.840><c> see</c><00:02:01.140><c> multiple</c><00:02:01.619><c> runs</c><00:02:01.979><c> on</c><00:02:02.220><c> one</c><00:02:02.399><c> page</c>

00:02:02.690 --> 00:02:02.700 align:start position:0%
be able to see multiple runs on one page
 

00:02:02.700 --> 00:02:04.910 align:start position:0%
be able to see multiple runs on one page
and<00:02:03.299><c> compare</c><00:02:03.659><c> all</c><00:02:03.840><c> of</c><00:02:03.960><c> them</c><00:02:04.079><c> there</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
and compare all of them there
 

00:02:04.920 --> 00:02:07.010 align:start position:0%
and compare all of them there
in<00:02:05.399><c> the</c><00:02:05.460><c> next</c><00:02:05.640><c> video</c><00:02:05.820><c> we'll</c><00:02:06.299><c> log</c><00:02:06.540><c> a</c><00:02:06.780><c> few</c><00:02:06.899><c> more</c>

00:02:07.010 --> 00:02:07.020 align:start position:0%
in the next video we'll log a few more
 

00:02:07.020 --> 00:02:09.770 align:start position:0%
in the next video we'll log a few more
runs<00:02:07.380><c> and</c><00:02:07.860><c> we'll</c><00:02:08.160><c> see</c><00:02:08.880><c> lots</c><00:02:09.300><c> of</c><00:02:09.360><c> the</c><00:02:09.479><c> ways</c><00:02:09.720><c> that</c>

00:02:09.770 --> 00:02:09.780 align:start position:0%
runs and we'll see lots of the ways that
 

00:02:09.780 --> 00:02:11.270 align:start position:0%
runs and we'll see lots of the ways that
you<00:02:09.899><c> can</c><00:02:10.020><c> compare</c><00:02:10.380><c> and</c><00:02:10.619><c> analyze</c><00:02:10.979><c> them</c><00:02:11.099><c> in</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
you can compare and analyze them in
 

00:02:11.280 --> 00:02:14.060 align:start position:0%
you can compare and analyze them in
weight<00:02:11.459><c> and</c><00:02:11.640><c> biases</c>

