WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:05.070 align:start position:0%
 
[Music]

00:00:05.070 --> 00:00:05.080 align:start position:0%
 
 

00:00:05.080 --> 00:00:07.990 align:start position:0%
 
hello<00:00:05.400><c> all</c><00:00:05.920><c> good</c><00:00:06.080><c> morning</c><00:00:06.520><c> good</c><00:00:06.720><c> evening</c><00:00:07.720><c> I'm</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
hello all good morning good evening I'm
 

00:00:08.000 --> 00:00:11.310 align:start position:0%
hello all good morning good evening I'm
devananda<00:00:08.639><c> <PERSON></c><00:00:09.559><c> uh</c><00:00:09.880><c> and</c><00:00:10.040><c> I'm</c><00:00:10.280><c> glad</c><00:00:11.000><c> uh</c><00:00:11.160><c> that</c>

00:00:11.310 --> 00:00:11.320 align:start position:0%
devananda Kumar uh and I'm glad uh that
 

00:00:11.320 --> 00:00:13.749 align:start position:0%
devananda Kumar uh and I'm glad uh that
I<00:00:11.440><c> got</c><00:00:11.639><c> an</c><00:00:11.840><c> opportunity</c><00:00:12.480><c> today</c><00:00:13.160><c> to</c><00:00:13.360><c> speak</c><00:00:13.639><c> in</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
I got an opportunity today to speak in
 

00:00:13.759 --> 00:00:16.630 align:start position:0%
I got an opportunity today to speak in
the<00:00:13.880><c> NLP</c><00:00:14.639><c> Summit</c><00:00:15.639><c> uh</c><00:00:15.799><c> thanks</c><00:00:16.119><c> to</c><00:00:16.240><c> the</c><00:00:16.359><c> John</c>

00:00:16.630 --> 00:00:16.640 align:start position:0%
the NLP Summit uh thanks to the John
 

00:00:16.640 --> 00:00:20.109 align:start position:0%
the NLP Summit uh thanks to the John
Snow<00:00:16.960><c> Labs</c><00:00:17.279><c> as</c><00:00:17.640><c> well</c><00:00:18.640><c> today</c><00:00:19.320><c> uh</c><00:00:19.439><c> we</c><00:00:19.560><c> can</c><00:00:19.760><c> take</c><00:00:19.920><c> a</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
Snow Labs as well today uh we can take a
 

00:00:20.119 --> 00:00:22.230 align:start position:0%
Snow Labs as well today uh we can take a
quick<00:00:20.359><c> look</c><00:00:20.640><c> at</c><00:00:20.880><c> the</c><00:00:21.199><c> competitor</c><00:00:21.800><c> insights</c>

00:00:22.230 --> 00:00:22.240 align:start position:0%
quick look at the competitor insights
 

00:00:22.240 --> 00:00:24.109 align:start position:0%
quick look at the competitor insights
and<00:00:22.519><c> analytics</c><00:00:23.199><c> from</c><00:00:23.400><c> a</c><00:00:23.560><c> procurement</c>

00:00:24.109 --> 00:00:24.119 align:start position:0%
and analytics from a procurement
 

00:00:24.119 --> 00:00:27.470 align:start position:0%
and analytics from a procurement
landscape

00:00:27.470 --> 00:00:27.480 align:start position:0%
 
 

00:00:27.480 --> 00:00:30.790 align:start position:0%
 
perspective<00:00:28.480><c> So</c><00:00:28.760><c> within</c><00:00:29.080><c> the</c><00:00:29.320><c> comp</c><00:00:29.679><c> comp</c><00:00:30.000><c> t</c><00:00:30.640><c> uh</c>

00:00:30.790 --> 00:00:30.800 align:start position:0%
perspective So within the comp comp t uh
 

00:00:30.800 --> 00:00:32.950 align:start position:0%
perspective So within the comp comp t uh
landscape<00:00:31.400><c> analysis</c><00:00:32.079><c> you</c><00:00:32.200><c> can</c><00:00:32.439><c> broadly</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
landscape analysis you can broadly
 

00:00:32.960 --> 00:00:35.030 align:start position:0%
landscape analysis you can broadly
classify<00:00:33.520><c> them</c><00:00:33.680><c> into</c><00:00:33.960><c> three</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
classify them into three
 

00:00:35.040 --> 00:00:37.030 align:start position:0%
classify them into three
categories<00:00:36.040><c> uh</c><00:00:36.200><c> they</c><00:00:36.280><c> are</c><00:00:36.480><c> the</c><00:00:36.640><c> market</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
categories uh they are the market
 

00:00:37.040 --> 00:00:40.029 align:start position:0%
categories uh they are the market
analysis<00:00:38.000><c> reviews</c><00:00:38.399><c> and</c><00:00:38.600><c> ratings</c><00:00:39.480><c> and</c><00:00:39.840><c> the</c>

00:00:40.029 --> 00:00:40.039 align:start position:0%
analysis reviews and ratings and the
 

00:00:40.039 --> 00:00:41.029 align:start position:0%
analysis reviews and ratings and the
price

00:00:41.029 --> 00:00:41.039 align:start position:0%
price
 

00:00:41.039 --> 00:00:43.709 align:start position:0%
price
analysis<00:00:42.039><c> in</c><00:00:42.239><c> market</c><00:00:42.680><c> analysis</c><00:00:43.280><c> you</c><00:00:43.440><c> may</c><00:00:43.600><c> want</c>

00:00:43.709 --> 00:00:43.719 align:start position:0%
analysis in market analysis you may want
 

00:00:43.719 --> 00:00:45.790 align:start position:0%
analysis in market analysis you may want
to<00:00:43.920><c> get</c><00:00:44.200><c> answers</c><00:00:44.640><c> to</c><00:00:44.800><c> the</c><00:00:45.000><c> questions</c><00:00:45.520><c> like</c>

00:00:45.790 --> 00:00:45.800 align:start position:0%
to get answers to the questions like
 

00:00:45.800 --> 00:00:49.110 align:start position:0%
to get answers to the questions like
what<00:00:46.000><c> is</c><00:00:46.120><c> the</c><00:00:46.280><c> market</c><00:00:46.600><c> share</c><00:00:47.120><c> of</c><00:00:48.120><c> your</c><00:00:48.480><c> company</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
what is the market share of your company
 

00:00:49.120 --> 00:00:52.510 align:start position:0%
what is the market share of your company
for<00:00:49.680><c> an</c><00:00:49.920><c> analyzer</c><00:00:50.879><c> device</c><00:00:51.879><c> in</c><00:00:52.039><c> the</c><00:00:52.199><c> North</c>

00:00:52.510 --> 00:00:52.520 align:start position:0%
for an analyzer device in the North
 

00:00:52.520 --> 00:00:56.750 align:start position:0%
for an analyzer device in the North
America<00:00:53.160><c> Market</c><00:00:54.160><c> maybe</c><00:00:54.480><c> in</c><00:00:54.760><c> Q2</c><00:00:55.520><c> 2024</c><00:00:56.520><c> when</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
America Market maybe in Q2 2024 when
 

00:00:56.760 --> 00:00:59.549 align:start position:0%
America Market maybe in Q2 2024 when
compared<00:00:57.120><c> to</c><00:00:57.239><c> the</c><00:00:57.399><c> competitor</c><00:00:58.320><c> companies</c><00:00:59.320><c> for</c>

00:00:59.549 --> 00:00:59.559 align:start position:0%
compared to the competitor companies for
 

00:00:59.559 --> 00:01:02.270 align:start position:0%
compared to the competitor companies for
this<00:01:00.000><c> qu</c><00:01:00.239><c> to</c><00:01:00.359><c> be</c><00:01:00.559><c> answered</c><00:01:01.519><c> if</c><00:01:01.640><c> you</c><00:01:01.920><c> have</c><00:01:02.120><c> a</c>

00:01:02.270 --> 00:01:02.280 align:start position:0%
this qu to be answered if you have a
 

00:01:02.280 --> 00:01:05.550 align:start position:0%
this qu to be answered if you have a
geni<00:01:02.800><c> chatboard</c><00:01:03.440><c> application</c><00:01:04.040><c> in</c><00:01:04.439><c> place</c><00:01:05.439><c> it</c>

00:01:05.550 --> 00:01:05.560 align:start position:0%
geni chatboard application in place it
 

00:01:05.560 --> 00:01:07.270 align:start position:0%
geni chatboard application in place it
would<00:01:05.799><c> get</c><00:01:06.000><c> connected</c><00:01:06.439><c> to</c><00:01:06.600><c> your</c><00:01:06.880><c> internal</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
would get connected to your internal
 

00:01:07.280 --> 00:01:10.149 align:start position:0%
would get connected to your internal
sales<00:01:07.680><c> datab</c><00:01:08.080><c> piece</c><00:01:08.920><c> apply</c><00:01:09.400><c> all</c><00:01:09.600><c> the</c><00:01:09.720><c> relevant</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
sales datab piece apply all the relevant
 

00:01:10.159 --> 00:01:13.590 align:start position:0%
sales datab piece apply all the relevant
filters<00:01:10.840><c> for</c><00:01:11.119><c> the</c><00:01:11.720><c> device</c><00:01:12.720><c> uh</c><00:01:12.840><c> for</c><00:01:13.040><c> the</c><00:01:13.200><c> device</c>

00:01:13.590 --> 00:01:13.600 align:start position:0%
filters for the device uh for the device
 

00:01:13.600 --> 00:01:17.390 align:start position:0%
filters for the device uh for the device
name<00:01:14.439><c> time</c><00:01:14.759><c> period</c><00:01:15.799><c> region</c><00:01:16.799><c> and</c><00:01:17.040><c> get</c><00:01:17.240><c> the</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
name time period region and get the
 

00:01:17.400 --> 00:01:19.710 align:start position:0%
name time period region and get the
numbers<00:01:17.799><c> for</c><00:01:18.000><c> you</c><00:01:18.680><c> as</c><00:01:18.840><c> well</c><00:01:19.080><c> as</c><00:01:19.400><c> it</c><00:01:19.520><c> will</c>

00:01:19.710 --> 00:01:19.720 align:start position:0%
numbers for you as well as it will
 

00:01:19.720 --> 00:01:21.789 align:start position:0%
numbers for you as well as it will
connect<00:01:20.040><c> to</c><00:01:20.200><c> the</c><00:01:20.400><c> Internet</c><00:01:21.240><c> and</c><00:01:21.400><c> get</c><00:01:21.600><c> the</c>

00:01:21.789 --> 00:01:21.799 align:start position:0%
connect to the Internet and get the
 

00:01:21.799 --> 00:01:24.390 align:start position:0%
connect to the Internet and get the
competitor<00:01:22.400><c> market</c><00:01:22.720><c> share</c><00:01:23.560><c> based</c><00:01:24.000><c> on</c><00:01:24.200><c> the</c>

00:01:24.390 --> 00:01:24.400 align:start position:0%
competitor market share based on the
 

00:01:24.400 --> 00:01:26.030 align:start position:0%
competitor market share based on the
published<00:01:24.799><c> General</c><00:01:25.200><c> ledgers</c><00:01:25.720><c> that</c><00:01:25.840><c> are</c>

00:01:26.030 --> 00:01:26.040 align:start position:0%
published General ledgers that are
 

00:01:26.040 --> 00:01:27.749 align:start position:0%
published General ledgers that are
available<00:01:26.560><c> out</c><00:01:26.799><c> on</c><00:01:26.960><c> the</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
available out on the
 

00:01:27.759 --> 00:01:30.230 align:start position:0%
available out on the
internet<00:01:28.759><c> similarly</c><00:01:29.320><c> one</c><00:01:29.560><c> can</c><00:01:29.720><c> ALS</c><00:01:29.960><c> also</c>

00:01:30.230 --> 00:01:30.240 align:start position:0%
internet similarly one can ALS also
 

00:01:30.240 --> 00:01:32.230 align:start position:0%
internet similarly one can ALS also
analyze<00:01:30.720><c> the</c><00:01:30.840><c> market</c><00:01:31.200><c> trends</c><00:01:31.600><c> as</c><00:01:31.720><c> to</c><00:01:31.880><c> how</c><00:01:32.079><c> the</c>

00:01:32.230 --> 00:01:32.240 align:start position:0%
analyze the market trends as to how the
 

00:01:32.240 --> 00:01:33.910 align:start position:0%
analyze the market trends as to how the
product<00:01:32.520><c> is</c><00:01:32.720><c> performing</c><00:01:33.280><c> over</c><00:01:33.479><c> a</c><00:01:33.640><c> given</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
product is performing over a given
 

00:01:33.920 --> 00:01:36.550 align:start position:0%
product is performing over a given
period<00:01:34.240><c> of</c><00:01:34.560><c> time</c><00:01:35.560><c> and</c><00:01:35.720><c> when</c><00:01:35.880><c> it</c><00:01:36.040><c> comes</c><00:01:36.240><c> to</c><00:01:36.399><c> the</c>

00:01:36.550 --> 00:01:36.560 align:start position:0%
period of time and when it comes to the
 

00:01:36.560 --> 00:01:38.950 align:start position:0%
period of time and when it comes to the
reviews<00:01:36.920><c> and</c><00:01:37.159><c> ratings</c><00:01:37.799><c> it</c><00:01:37.960><c> is</c><00:01:38.240><c> about</c>

00:01:38.950 --> 00:01:38.960 align:start position:0%
reviews and ratings it is about
 

00:01:38.960 --> 00:01:40.590 align:start position:0%
reviews and ratings it is about
understanding<00:01:39.439><c> how</c><00:01:39.600><c> the</c><00:01:39.759><c> end</c><00:01:40.240><c> what</c><00:01:40.360><c> is</c><00:01:40.479><c> the</c>

00:01:40.590 --> 00:01:40.600 align:start position:0%
understanding how the end what is the
 

00:01:40.600 --> 00:01:43.190 align:start position:0%
understanding how the end what is the
end<00:01:40.840><c> users</c><00:01:41.320><c> perspective</c><00:01:41.920><c> of</c><00:01:42.079><c> the</c><00:01:42.200><c> features</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
end users perspective of the features
 

00:01:43.200 --> 00:01:45.789 align:start position:0%
end users perspective of the features
and<00:01:43.360><c> so</c>

00:01:45.789 --> 00:01:45.799 align:start position:0%
 
 

00:01:45.799 --> 00:01:48.510 align:start position:0%
 
on<00:01:46.799><c> when</c><00:01:46.960><c> you</c><00:01:47.119><c> take</c><00:01:47.280><c> a</c><00:01:47.479><c> look</c><00:01:47.640><c> at</c><00:01:47.840><c> the</c><00:01:48.040><c> product</c>

00:01:48.510 --> 00:01:48.520 align:start position:0%
on when you take a look at the product
 

00:01:48.520 --> 00:01:51.190 align:start position:0%
on when you take a look at the product
Journey<00:01:49.520><c> based</c><00:01:49.840><c> on</c><00:01:50.000><c> the</c><00:01:50.200><c> demand</c><00:01:50.799><c> we</c><00:01:50.960><c> would</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
Journey based on the demand we would
 

00:01:51.200 --> 00:01:53.910 align:start position:0%
Journey based on the demand we would
raise<00:01:51.719><c> purchase</c><00:01:52.159><c> orders</c><00:01:53.119><c> where</c><00:01:53.320><c> the</c><00:01:53.479><c> purchase</c>

00:01:53.910 --> 00:01:53.920 align:start position:0%
raise purchase orders where the purchase
 

00:01:53.920 --> 00:01:56.149 align:start position:0%
raise purchase orders where the purchase
orders<00:01:54.320><c> are</c><00:01:54.560><c> the</c><00:01:54.719><c> materials</c><00:01:55.280><c> required</c><00:01:55.840><c> to</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
orders are the materials required to
 

00:01:56.159 --> 00:01:59.550 align:start position:0%
orders are the materials required to
build<00:01:56.479><c> the</c><00:01:56.719><c> product</c><00:01:57.719><c> and</c><00:01:57.960><c> procurement</c><00:01:58.680><c> is</c>

00:01:59.550 --> 00:01:59.560 align:start position:0%
build the product and procurement is
 

00:01:59.560 --> 00:02:02.870 align:start position:0%
build the product and procurement is
about<00:01:59.960><c> out</c><00:02:00.520><c> from</c><00:02:01.520><c> which</c><00:02:01.799><c> suppliers</c><00:02:02.479><c> are</c><00:02:02.759><c> the</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
about out from which suppliers are the
 

00:02:02.880 --> 00:02:04.950 align:start position:0%
about out from which suppliers are the
materials<00:02:03.360><c> being</c><00:02:03.640><c> procured</c><00:02:04.280><c> and</c><00:02:04.439><c> at</c><00:02:04.680><c> what</c>

00:02:04.950 --> 00:02:04.960 align:start position:0%
materials being procured and at what
 

00:02:04.960 --> 00:02:08.070 align:start position:0%
materials being procured and at what
cost<00:02:05.960><c> here</c><00:02:06.200><c> the</c><00:02:06.399><c> procurement</c><00:02:07.039><c> teams</c><00:02:07.640><c> main</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
cost here the procurement teams main
 

00:02:08.080 --> 00:02:09.949 align:start position:0%
cost here the procurement teams main
goal<00:02:08.360><c> would</c><00:02:08.560><c> be</c><00:02:08.920><c> to</c><00:02:09.160><c> procure</c><00:02:09.599><c> the</c><00:02:09.720><c> raw</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
goal would be to procure the raw
 

00:02:09.959 --> 00:02:12.949 align:start position:0%
goal would be to procure the raw
materials<00:02:10.640><c> at</c><00:02:11.080><c> the</c><00:02:11.280><c> best</c><00:02:11.640><c> price</c><00:02:12.599><c> with</c><00:02:12.760><c> the</c>

00:02:12.949 --> 00:02:12.959 align:start position:0%
materials at the best price with the
 

00:02:12.959 --> 00:02:17.830 align:start position:0%
materials at the best price with the
competitor<00:02:13.879><c> analysis</c><00:02:14.879><c> uh</c><00:02:15.000><c> to</c><00:02:15.160><c> be</c><00:02:15.800><c> an</c><00:02:16.800><c> to</c><00:02:17.440><c> uh</c>

00:02:17.830 --> 00:02:17.840 align:start position:0%
competitor analysis uh to be an to uh
 

00:02:17.840 --> 00:02:19.309 align:start position:0%
competitor analysis uh to be an to uh
procure<00:02:18.200><c> the</c><00:02:18.280><c> raw</c><00:02:18.480><c> material</c><00:02:18.840><c> at</c><00:02:18.959><c> the</c><00:02:19.120><c> best</c>

00:02:19.309 --> 00:02:19.319 align:start position:0%
procure the raw material at the best
 

00:02:19.319 --> 00:02:21.589 align:start position:0%
procure the raw material at the best
price<00:02:19.720><c> right</c><00:02:20.360><c> so</c><00:02:20.680><c> with</c><00:02:20.840><c> the</c><00:02:21.000><c> competitor</c>

00:02:21.589 --> 00:02:21.599 align:start position:0%
price right so with the competitor
 

00:02:21.599 --> 00:02:24.150 align:start position:0%
price right so with the competitor
analysis<00:02:22.280><c> a</c><00:02:22.480><c> question</c><00:02:22.760><c> to</c><00:02:22.920><c> be</c><00:02:23.160><c> answered</c><00:02:23.599><c> is</c>

00:02:24.150 --> 00:02:24.160 align:start position:0%
analysis a question to be answered is
 

00:02:24.160 --> 00:02:26.869 align:start position:0%
analysis a question to be answered is
what<00:02:24.319><c> is</c><00:02:24.519><c> the</c><00:02:24.680><c> supplier</c><00:02:25.239><c> base</c><00:02:25.640><c> of</c><00:02:25.879><c> my</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
what is the supplier base of my
 

00:02:26.879 --> 00:02:29.910 align:start position:0%
what is the supplier base of my
competitor<00:02:27.879><c> and</c><00:02:28.160><c> can</c><00:02:28.400><c> I</c><00:02:28.640><c> get</c><00:02:28.920><c> acquainted</c><00:02:29.480><c> with</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
competitor and can I get acquainted with
 

00:02:29.920 --> 00:02:31.990 align:start position:0%
competitor and can I get acquainted with
more<00:02:30.160><c> suppliers</c><00:02:30.879><c> to</c><00:02:31.040><c> get</c><00:02:31.200><c> a</c><00:02:31.360><c> competitive</c>

00:02:31.990 --> 00:02:32.000 align:start position:0%
more suppliers to get a competitive
 

00:02:32.000 --> 00:02:35.509 align:start position:0%
more suppliers to get a competitive
price<00:02:32.400><c> at</c><00:02:32.560><c> the</c><00:02:32.760><c> best</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
 
 

00:02:35.519 --> 00:02:37.309 align:start position:0%
 
quality

00:02:37.309 --> 00:02:37.319 align:start position:0%
quality
 

00:02:37.319 --> 00:02:40.670 align:start position:0%
quality
so<00:02:38.319><c> competitor</c><00:02:39.040><c> analysis</c><00:02:39.760><c> for</c><00:02:40.000><c> a</c><00:02:40.159><c> procurement</c>

00:02:40.670 --> 00:02:40.680 align:start position:0%
so competitor analysis for a procurement
 

00:02:40.680 --> 00:02:44.630 align:start position:0%
so competitor analysis for a procurement
team<00:02:41.920><c> involves</c><00:02:42.920><c> evaluating</c><00:02:43.640><c> the</c><00:02:44.040><c> procurement</c>

00:02:44.630 --> 00:02:44.640 align:start position:0%
team involves evaluating the procurement
 

00:02:44.640 --> 00:02:48.229 align:start position:0%
team involves evaluating the procurement
strategies<00:02:45.599><c> practices</c><00:02:46.599><c> and</c><00:02:46.840><c> performance</c><00:02:47.519><c> of</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
strategies practices and performance of
 

00:02:48.239 --> 00:02:50.070 align:start position:0%
strategies practices and performance of
rival

00:02:50.070 --> 00:02:50.080 align:start position:0%
rival
 

00:02:50.080 --> 00:02:52.350 align:start position:0%
rival
organizations<00:02:51.080><c> the</c><00:02:51.239><c> goal</c><00:02:51.440><c> is</c><00:02:51.599><c> to</c><00:02:51.800><c> identify</c>

00:02:52.350 --> 00:02:52.360 align:start position:0%
organizations the goal is to identify
 

00:02:52.360 --> 00:02:54.350 align:start position:0%
organizations the goal is to identify
the<00:02:52.480><c> strengths</c><00:02:52.879><c> and</c><00:02:53.080><c> weaknesses</c><00:02:54.040><c> in</c><00:02:54.200><c> the</c>

00:02:54.350 --> 00:02:54.360 align:start position:0%
the strengths and weaknesses in the
 

00:02:54.360 --> 00:02:57.309 align:start position:0%
the strengths and weaknesses in the
competitor<00:02:54.920><c> supply</c><00:02:55.239><c> chain</c><00:02:55.959><c> operations</c><00:02:56.959><c> along</c>

00:02:57.309 --> 00:02:57.319 align:start position:0%
competitor supply chain operations along
 

00:02:57.319 --> 00:03:00.309 align:start position:0%
competitor supply chain operations along
with<00:02:57.480><c> an</c><00:02:57.920><c> organization</c><00:02:58.920><c> to</c><00:02:59.239><c> optimize</c><00:02:59.879><c> is</c><00:03:00.040><c> its</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
with an organization to optimize is its
 

00:03:00.319 --> 00:03:03.309 align:start position:0%
with an organization to optimize is its
procurement<00:03:01.400><c> process</c><00:03:02.400><c> so</c><00:03:02.599><c> when</c><00:03:02.760><c> it</c><00:03:02.920><c> comes</c><00:03:03.120><c> to</c>

00:03:03.309 --> 00:03:03.319 align:start position:0%
procurement process so when it comes to
 

00:03:03.319 --> 00:03:06.309 align:start position:0%
procurement process so when it comes to
the<00:03:03.519><c> cost</c><00:03:04.159><c> efficiency</c><00:03:05.159><c> the</c><00:03:05.319><c> competitor</c><00:03:05.959><c> Focus</c>

00:03:06.309 --> 00:03:06.319 align:start position:0%
the cost efficiency the competitor Focus
 

00:03:06.319 --> 00:03:09.630 align:start position:0%
the cost efficiency the competitor Focus
would<00:03:06.560><c> be</c><00:03:07.400><c> to</c><00:03:08.120><c> compare</c><00:03:08.560><c> the</c><00:03:08.720><c> procurement</c><00:03:09.280><c> cost</c>

00:03:09.630 --> 00:03:09.640 align:start position:0%
would be to compare the procurement cost
 

00:03:09.640 --> 00:03:12.309 align:start position:0%
would be to compare the procurement cost
such<00:03:09.879><c> as</c><00:03:10.480><c> material</c><00:03:11.040><c> prices</c><00:03:11.599><c> shipping</c><00:03:12.120><c> and</c>

00:03:12.309 --> 00:03:12.319 align:start position:0%
such as material prices shipping and
 

00:03:12.319 --> 00:03:14.949 align:start position:0%
such as material prices shipping and
logistic<00:03:13.000><c> expenses</c><00:03:14.000><c> and</c><00:03:14.159><c> the</c><00:03:14.319><c> goal</c><00:03:14.599><c> would</c><00:03:14.799><c> be</c>

00:03:14.949 --> 00:03:14.959 align:start position:0%
logistic expenses and the goal would be
 

00:03:14.959 --> 00:03:18.390 align:start position:0%
logistic expenses and the goal would be
to<00:03:15.159><c> Benchmark</c><00:03:16.000><c> the</c><00:03:16.840><c> procurement</c><00:03:17.599><c> costs</c>

00:03:18.390 --> 00:03:18.400 align:start position:0%
to Benchmark the procurement costs
 

00:03:18.400 --> 00:03:21.390 align:start position:0%
to Benchmark the procurement costs
against<00:03:18.799><c> the</c><00:03:19.000><c> competitors</c><00:03:20.000><c> to</c><00:03:20.280><c> identify</c><00:03:21.200><c> the</c>

00:03:21.390 --> 00:03:21.400 align:start position:0%
against the competitors to identify the
 

00:03:21.400 --> 00:03:22.830 align:start position:0%
against the competitors to identify the
areas<00:03:21.799><c> of</c><00:03:22.000><c> cost</c>

00:03:22.830 --> 00:03:22.840 align:start position:0%
areas of cost
 

00:03:22.840 --> 00:03:26.270 align:start position:0%
areas of cost
reduction<00:03:23.840><c> and</c><00:03:24.040><c> the</c><00:03:24.239><c> advantage</c><00:03:24.799><c> is</c><00:03:25.599><c> uh</c><00:03:25.840><c> we</c><00:03:25.959><c> can</c>

00:03:26.270 --> 00:03:26.280 align:start position:0%
reduction and the advantage is uh we can
 

00:03:26.280 --> 00:03:29.949 align:start position:0%
reduction and the advantage is uh we can
achieve<00:03:26.920><c> a</c><00:03:27.640><c> cost</c><00:03:27.959><c> leadership</c><00:03:28.799><c> When</c><00:03:29.040><c> comparing</c>

00:03:29.949 --> 00:03:29.959 align:start position:0%
achieve a cost leadership When comparing
 

00:03:29.959 --> 00:03:33.030 align:start position:0%
achieve a cost leadership When comparing
uh<00:03:30.439><c> without</c><00:03:30.720><c> compromising</c><00:03:31.319><c> on</c><00:03:31.519><c> the</c>

00:03:33.030 --> 00:03:33.040 align:start position:0%
uh without compromising on the
 

00:03:33.040 --> 00:03:35.309 align:start position:0%
uh without compromising on the
quality<00:03:34.040><c> when</c><00:03:34.159><c> it</c><00:03:34.319><c> comes</c><00:03:34.560><c> to</c><00:03:34.720><c> the</c><00:03:34.840><c> supplier</c>

00:03:35.309 --> 00:03:35.319 align:start position:0%
quality when it comes to the supplier
 

00:03:35.319 --> 00:03:37.270 align:start position:0%
quality when it comes to the supplier
base<00:03:35.680><c> the</c><00:03:35.840><c> competitor</c><00:03:36.400><c> focus</c><00:03:36.879><c> is</c><00:03:37.040><c> to</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
base the competitor focus is to
 

00:03:37.280 --> 00:03:39.670 align:start position:0%
base the competitor focus is to
investigate<00:03:38.120><c> the</c><00:03:38.280><c> competitor</c><00:03:38.920><c> supplier</c>

00:03:39.670 --> 00:03:39.680 align:start position:0%
investigate the competitor supplier
 

00:03:39.680 --> 00:03:42.149 align:start position:0%
investigate the competitor supplier
networks<00:03:40.680><c> what</c><00:03:40.799><c> are</c><00:03:41.000><c> the</c><00:03:41.159><c> kind</c><00:03:41.360><c> of</c><00:03:41.599><c> contracts</c>

00:03:42.149 --> 00:03:42.159 align:start position:0%
networks what are the kind of contracts
 

00:03:42.159 --> 00:03:44.589 align:start position:0%
networks what are the kind of contracts
that<00:03:42.319><c> are</c><00:03:42.560><c> there</c><00:03:42.720><c> with</c><00:03:42.920><c> those</c><00:03:43.200><c> suppliers</c><00:03:44.200><c> and</c>

00:03:44.589 --> 00:03:44.599 align:start position:0%
that are there with those suppliers and
 

00:03:44.599 --> 00:03:46.509 align:start position:0%
that are there with those suppliers and
what<00:03:44.720><c> is</c><00:03:44.840><c> the</c><00:03:45.080><c> kind</c><00:03:45.280><c> of</c><00:03:45.439><c> relationships</c><00:03:46.319><c> that</c>

00:03:46.509 --> 00:03:46.519 align:start position:0%
what is the kind of relationships that
 

00:03:46.519 --> 00:03:48.710 align:start position:0%
what is the kind of relationships that
the<00:03:46.720><c> competitor</c><00:03:47.319><c> has</c><00:03:47.680><c> with</c><00:03:47.879><c> his</c><00:03:48.040><c> own</c>

00:03:48.710 --> 00:03:48.720 align:start position:0%
the competitor has with his own
 

00:03:48.720 --> 00:03:51.589 align:start position:0%
the competitor has with his own
suppliers<00:03:49.720><c> and</c><00:03:49.879><c> the</c><00:03:50.040><c> goal</c><00:03:50.319><c> is</c><00:03:50.480><c> to</c><00:03:51.040><c> understand</c>

00:03:51.589 --> 00:03:51.599 align:start position:0%
suppliers and the goal is to understand
 

00:03:51.599 --> 00:03:54.710 align:start position:0%
suppliers and the goal is to understand
how<00:03:52.079><c> competitors</c><00:03:53.079><c> secure</c><00:03:53.799><c> their</c><00:03:54.040><c> favorable</c>

00:03:54.710 --> 00:03:54.720 align:start position:0%
how competitors secure their favorable
 

00:03:54.720 --> 00:03:58.390 align:start position:0%
how competitors secure their favorable
terms<00:03:55.720><c> diversify</c><00:03:56.400><c> their</c><00:03:56.599><c> supplier</c><00:03:57.079><c> base</c><00:03:57.959><c> and</c>

00:03:58.390 --> 00:03:58.400 align:start position:0%
terms diversify their supplier base and
 

00:03:58.400 --> 00:04:01.589 align:start position:0%
terms diversify their supplier base and
manage<00:03:58.840><c> their</c><00:03:59.079><c> risk</c><00:03:59.439><c> like</c><00:03:59.799><c> the</c><00:03:59.920><c> shortages</c><00:04:00.439><c> or</c>

00:04:01.589 --> 00:04:01.599 align:start position:0%
manage their risk like the shortages or
 

00:04:01.599 --> 00:04:04.589 align:start position:0%
manage their risk like the shortages or
delays<00:04:02.599><c> and</c><00:04:02.760><c> the</c><00:04:02.959><c> advantage</c><00:04:03.599><c> here</c><00:04:03.879><c> is</c><00:04:04.159><c> to</c>

00:04:04.589 --> 00:04:04.599 align:start position:0%
delays and the advantage here is to
 

00:04:04.599 --> 00:04:07.149 align:start position:0%
delays and the advantage here is to
strengthen<00:04:05.120><c> the</c><00:04:05.280><c> supplier</c><00:04:05.879><c> negotiations</c><00:04:06.879><c> and</c>

00:04:07.149 --> 00:04:07.159 align:start position:0%
strengthen the supplier negotiations and
 

00:04:07.159 --> 00:04:09.949 align:start position:0%
strengthen the supplier negotiations and
identify<00:04:08.159><c> potential</c><00:04:08.720><c> new</c><00:04:09.000><c> suppliers</c><00:04:09.680><c> or</c>

00:04:09.949 --> 00:04:09.959 align:start position:0%
identify potential new suppliers or
 

00:04:09.959 --> 00:04:13.550 align:start position:0%
identify potential new suppliers or
cost-saving

00:04:13.550 --> 00:04:13.560 align:start position:0%
 
 

00:04:13.560 --> 00:04:16.229 align:start position:0%
 
opportunities<00:04:14.560><c> so</c><00:04:15.079><c> the</c><00:04:15.280><c> analysis</c><00:04:15.920><c> could</c><00:04:16.079><c> be</c>

00:04:16.229 --> 00:04:16.239 align:start position:0%
opportunities so the analysis could be
 

00:04:16.239 --> 00:04:18.430 align:start position:0%
opportunities so the analysis could be
broadly<00:04:16.680><c> classified</c><00:04:17.320><c> into</c><00:04:17.720><c> quantitative</c>

00:04:18.430 --> 00:04:18.440 align:start position:0%
broadly classified into quantitative
 

00:04:18.440 --> 00:04:21.590 align:start position:0%
broadly classified into quantitative
analysis<00:04:19.199><c> and</c><00:04:19.400><c> qualitative</c><00:04:20.440><c> analysis</c><00:04:21.440><c> the</c>

00:04:21.590 --> 00:04:21.600 align:start position:0%
analysis and qualitative analysis the
 

00:04:21.600 --> 00:04:24.350 align:start position:0%
analysis and qualitative analysis the
quantitative<00:04:22.240><c> analysis</c><00:04:22.919><c> is</c><00:04:23.120><c> much</c><00:04:23.440><c> enabled</c><00:04:24.120><c> by</c>

00:04:24.350 --> 00:04:24.360 align:start position:0%
quantitative analysis is much enabled by
 

00:04:24.360 --> 00:04:26.830 align:start position:0%
quantitative analysis is much enabled by
the<00:04:24.520><c> procurement</c><00:04:25.080><c> data</c><00:04:25.520><c> aggregators</c><00:04:26.520><c> or</c><00:04:26.720><c> you</c>

00:04:26.830 --> 00:04:26.840 align:start position:0%
the procurement data aggregators or you
 

00:04:26.840 --> 00:04:29.830 align:start position:0%
the procurement data aggregators or you
could<00:04:27.000><c> say</c><00:04:27.240><c> the</c><00:04:27.360><c> third</c><00:04:27.639><c> party</c><00:04:27.960><c> data</c><00:04:28.280><c> providers</c>

00:04:29.830 --> 00:04:29.840 align:start position:0%
could say the third party data providers
 

00:04:29.840 --> 00:04:32.310 align:start position:0%
could say the third party data providers
and<00:04:30.479><c> uh</c><00:04:30.639><c> let's</c><00:04:30.919><c> take</c><00:04:31.080><c> a</c><00:04:31.280><c> look</c><00:04:31.600><c> at</c><00:04:31.840><c> a</c><00:04:31.960><c> few</c>

00:04:32.310 --> 00:04:32.320 align:start position:0%
and uh let's take a look at a few
 

00:04:32.320 --> 00:04:35.310 align:start position:0%
and uh let's take a look at a few
examples<00:04:32.919><c> as</c><00:04:33.080><c> to</c><00:04:33.880><c> uh</c><00:04:34.240><c> what</c><00:04:34.440><c> are</c><00:04:34.720><c> the</c><00:04:34.880><c> kind</c><00:04:35.120><c> of</c>

00:04:35.310 --> 00:04:35.320 align:start position:0%
examples as to uh what are the kind of
 

00:04:35.320 --> 00:04:39.070 align:start position:0%
examples as to uh what are the kind of
kpis<00:04:35.880><c> that</c><00:04:36.039><c> you</c><00:04:36.520><c> can</c><00:04:37.520><c> uh</c><00:04:37.800><c> evaluate</c><00:04:38.520><c> with</c><00:04:38.759><c> a</c>

00:04:39.070 --> 00:04:39.080 align:start position:0%
kpis that you can uh evaluate with a
 

00:04:39.080 --> 00:04:42.390 align:start position:0%
kpis that you can uh evaluate with a
quantitative

00:04:42.390 --> 00:04:42.400 align:start position:0%
 
 

00:04:42.400 --> 00:04:45.390 align:start position:0%
 
analysis<00:04:43.400><c> if</c><00:04:43.520><c> you</c><00:04:43.680><c> see</c><00:04:44.080><c> this</c><00:04:44.720><c> uh</c><00:04:44.919><c> the</c><00:04:45.080><c> first</c>

00:04:45.390 --> 00:04:45.400 align:start position:0%
analysis if you see this uh the first
 

00:04:45.400 --> 00:04:47.950 align:start position:0%
analysis if you see this uh the first
kpi<00:04:46.000><c> which</c><00:04:46.120><c> is</c><00:04:46.240><c> the</c><00:04:46.400><c> supplier</c><00:04:46.880><c> performance</c>

00:04:47.950 --> 00:04:47.960 align:start position:0%
kpi which is the supplier performance
 

00:04:47.960 --> 00:04:50.990 align:start position:0%
kpi which is the supplier performance
risk<00:04:48.960><c> here</c><00:04:49.199><c> you</c><00:04:49.360><c> see</c><00:04:49.840><c> that</c><00:04:50.039><c> your</c>

00:04:50.990 --> 00:04:51.000 align:start position:0%
risk here you see that your
 

00:04:51.000 --> 00:04:53.310 align:start position:0%
risk here you see that your
organization<00:04:52.000><c> the</c><00:04:52.320><c> performance</c><00:04:53.000><c> of</c><00:04:53.160><c> the</c>

00:04:53.310 --> 00:04:53.320 align:start position:0%
organization the performance of the
 

00:04:53.320 --> 00:04:56.430 align:start position:0%
organization the performance of the
supplier<00:04:54.039><c> and</c><00:04:54.280><c> the</c><00:04:54.440><c> risk</c><00:04:54.880><c> is</c><00:04:55.120><c> at</c><00:04:55.360><c> around</c>

00:04:56.430 --> 00:04:56.440 align:start position:0%
supplier and the risk is at around
 

00:04:56.440 --> 00:04:59.749 align:start position:0%
supplier and the risk is at around
87%<00:04:57.440><c> but</c><00:04:57.680><c> whereas</c><00:04:58.240><c> for</c><00:04:58.440><c> your</c><00:04:58.720><c> competitors</c><00:04:59.400><c> it</c>

00:04:59.749 --> 00:04:59.759 align:start position:0%
87% but whereas for your competitors it
 

00:04:59.759 --> 00:05:00.950 align:start position:0%
87% but whereas for your competitors it
is<00:04:59.919><c> around</c>

00:05:00.950 --> 00:05:00.960 align:start position:0%
is around
 

00:05:00.960 --> 00:05:03.749 align:start position:0%
is around
93%<00:05:01.960><c> and</c><00:05:02.280><c> if</c><00:05:02.440><c> you</c><00:05:02.560><c> are</c><00:05:02.960><c> trying</c><00:05:03.280><c> to</c><00:05:03.400><c> do</c><00:05:03.600><c> a</c>

00:05:03.749 --> 00:05:03.759 align:start position:0%
93% and if you are trying to do a
 

00:05:03.759 --> 00:05:06.029 align:start position:0%
93% and if you are trying to do a
weighted<00:05:04.160><c> average</c><00:05:04.560><c> of</c><00:05:04.720><c> your</c><00:05:04.919><c> supplier</c><00:05:05.440><c> base</c>

00:05:06.029 --> 00:05:06.039 align:start position:0%
weighted average of your supplier base
 

00:05:06.039 --> 00:05:07.590 align:start position:0%
weighted average of your supplier base
then<00:05:06.199><c> it</c><00:05:06.320><c> is</c><00:05:06.560><c> around</c>

00:05:07.590 --> 00:05:07.600 align:start position:0%
then it is around
 

00:05:07.600 --> 00:05:11.590 align:start position:0%
then it is around
92%<00:05:08.600><c> still</c><00:05:08.960><c> there</c><00:05:09.080><c> is</c><00:05:09.199><c> a</c><00:05:09.400><c> gap</c><00:05:09.759><c> of</c><00:05:10.400><c> 5%</c><00:05:11.360><c> as</c>

00:05:11.590 --> 00:05:11.600 align:start position:0%
92% still there is a gap of 5% as
 

00:05:11.600 --> 00:05:15.070 align:start position:0%
92% still there is a gap of 5% as
highlighted<00:05:12.080><c> in</c><00:05:12.240><c> red</c><00:05:12.680><c> here</c><00:05:13.680><c> so</c><00:05:13.960><c> that</c><00:05:14.080><c> means</c><00:05:14.919><c> it</c>

00:05:15.070 --> 00:05:15.080 align:start position:0%
highlighted in red here so that means it
 

00:05:15.080 --> 00:05:18.189 align:start position:0%
highlighted in red here so that means it
is<00:05:15.560><c> there</c><00:05:15.759><c> is</c><00:05:16.240><c> uh</c><00:05:16.479><c> room</c><00:05:16.800><c> for</c><00:05:17.120><c> improvement</c><00:05:18.039><c> when</c>

00:05:18.189 --> 00:05:18.199 align:start position:0%
is there is uh room for improvement when
 

00:05:18.199 --> 00:05:19.909 align:start position:0%
is there is uh room for improvement when
it<00:05:18.319><c> comes</c><00:05:18.520><c> to</c><00:05:18.680><c> the</c><00:05:18.800><c> supplier</c><00:05:19.240><c> performance</c><00:05:19.680><c> and</c>

00:05:19.909 --> 00:05:19.919 align:start position:0%
it comes to the supplier performance and
 

00:05:19.919 --> 00:05:21.870 align:start position:0%
it comes to the supplier performance and
risk<00:05:20.319><c> for</c><00:05:20.639><c> your</c>

00:05:21.870 --> 00:05:21.880 align:start position:0%
risk for your
 

00:05:21.880 --> 00:05:26.189 align:start position:0%
risk for your
organization<00:05:22.880><c> and</c><00:05:23.440><c> in</c><00:05:23.680><c> the</c><00:05:24.120><c> in</c><00:05:24.240><c> the</c><00:05:24.919><c> uh</c><00:05:25.759><c> next</c>

00:05:26.189 --> 00:05:26.199 align:start position:0%
organization and in the in the uh next
 

00:05:26.199 --> 00:05:29.150 align:start position:0%
organization and in the in the uh next
uh<00:05:26.360><c> below</c><00:05:26.759><c> the</c><00:05:27.759><c> supply</c><00:05:28.240><c> performance</c><00:05:28.680><c> and</c><00:05:28.880><c> risk</c>

00:05:29.150 --> 00:05:29.160 align:start position:0%
uh below the supply performance and risk
 

00:05:29.160 --> 00:05:31.150 align:start position:0%
uh below the supply performance and risk
you<00:05:29.240><c> can</c><00:05:29.400><c> see</c><00:05:29.880><c> a</c><00:05:30.039><c> kpi</c><00:05:30.560><c> which</c><00:05:30.680><c> is</c><00:05:30.840><c> called</c>

00:05:31.150 --> 00:05:31.160 align:start position:0%
you can see a kpi which is called
 

00:05:31.160 --> 00:05:32.309 align:start position:0%
you can see a kpi which is called
inventory

00:05:32.309 --> 00:05:32.319 align:start position:0%
inventory
 

00:05:32.319 --> 00:05:35.390 align:start position:0%
inventory
coverage<00:05:33.319><c> so</c><00:05:33.639><c> inventory</c><00:05:34.160><c> coverage</c><00:05:34.680><c> means</c><00:05:35.160><c> for</c>

00:05:35.390 --> 00:05:35.400 align:start position:0%
coverage so inventory coverage means for
 

00:05:35.400 --> 00:05:38.510 align:start position:0%
coverage so inventory coverage means for
a<00:05:35.600><c> manufacturing</c><00:05:36.440><c> plant</c><00:05:37.360><c> how</c><00:05:37.479><c> many</c><00:05:37.880><c> days</c><00:05:38.240><c> can</c>

00:05:38.510 --> 00:05:38.520 align:start position:0%
a manufacturing plant how many days can
 

00:05:38.520 --> 00:05:42.950 align:start position:0%
a manufacturing plant how many days can
the<00:05:38.720><c> plant</c><00:05:39.600><c> run</c><00:05:40.600><c> in</c><00:05:40.840><c> its</c><00:05:41.319><c> uh</c><00:05:41.639><c> manufacturing</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
the plant run in its uh manufacturing
 

00:05:42.960 --> 00:05:45.830 align:start position:0%
the plant run in its uh manufacturing
process<00:05:43.960><c> without</c><00:05:44.600><c> new</c><00:05:44.960><c> inventory</c><00:05:45.479><c> coming</c>

00:05:45.830 --> 00:05:45.840 align:start position:0%
process without new inventory coming
 

00:05:45.840 --> 00:05:48.390 align:start position:0%
process without new inventory coming
into<00:05:46.160><c> the</c><00:05:46.520><c> plant</c><00:05:47.520><c> that</c><00:05:47.639><c> is</c><00:05:47.759><c> the</c><00:05:47.880><c> meaning</c><00:05:48.199><c> of</c>

00:05:48.390 --> 00:05:48.400 align:start position:0%
into the plant that is the meaning of
 

00:05:48.400 --> 00:05:50.870 align:start position:0%
into the plant that is the meaning of
inventory<00:05:48.880><c> coverage</c><00:05:49.840><c> so</c><00:05:50.080><c> here</c><00:05:50.319><c> you</c><00:05:50.479><c> can</c><00:05:50.639><c> see</c>

00:05:50.870 --> 00:05:50.880 align:start position:0%
inventory coverage so here you can see
 

00:05:50.880 --> 00:05:54.309 align:start position:0%
inventory coverage so here you can see
the<00:05:51.039><c> current</c><00:05:51.400><c> organization</c><00:05:52.319><c> is</c><00:05:52.520><c> able</c><00:05:52.759><c> to</c><00:05:53.319><c> run</c>

00:05:54.309 --> 00:05:54.319 align:start position:0%
the current organization is able to run
 

00:05:54.319 --> 00:05:57.430 align:start position:0%
the current organization is able to run
uh<00:05:54.560><c> the</c><00:05:54.759><c> plant</c><00:05:55.319><c> without</c><00:05:56.080><c> new</c><00:05:56.440><c> inventory</c><00:05:57.039><c> for</c>

00:05:57.430 --> 00:05:57.440 align:start position:0%
uh the plant without new inventory for
 

00:05:57.440 --> 00:06:00.230 align:start position:0%
uh the plant without new inventory for
around<00:05:57.759><c> 60</c><00:05:58.199><c> days</c><00:05:59.199><c> whereas</c>

00:06:00.230 --> 00:06:00.240 align:start position:0%
around 60 days whereas
 

00:06:00.240 --> 00:06:03.350 align:start position:0%
around 60 days whereas
the<00:06:00.680><c> normal</c><00:06:01.280><c> weighted</c><00:06:01.759><c> average</c><00:06:02.400><c> shows</c><00:06:02.919><c> it</c><00:06:03.120><c> is</c>

00:06:03.350 --> 00:06:03.360 align:start position:0%
the normal weighted average shows it is
 

00:06:03.360 --> 00:06:06.550 align:start position:0%
the normal weighted average shows it is
56<00:06:03.960><c> days</c><00:06:04.319><c> that</c><00:06:04.440><c> means</c><00:06:05.400><c> your</c><00:06:05.759><c> company</c><00:06:06.280><c> can</c>

00:06:06.550 --> 00:06:06.560 align:start position:0%
56 days that means your company can
 

00:06:06.560 --> 00:06:09.589 align:start position:0%
56 days that means your company can
perfor<00:06:07.080><c> uh</c><00:06:07.360><c> is</c><00:06:07.759><c> 4</c><00:06:08.199><c> days</c><00:06:08.560><c> better</c><00:06:09.000><c> when</c><00:06:09.240><c> compared</c>

00:06:09.589 --> 00:06:09.599 align:start position:0%
perfor uh is 4 days better when compared
 

00:06:09.599 --> 00:06:11.950 align:start position:0%
perfor uh is 4 days better when compared
to<00:06:09.720><c> the</c><00:06:09.840><c> market</c><00:06:10.199><c> standards</c><00:06:10.800><c> which</c><00:06:10.919><c> is</c><00:06:11.039><c> a</c><00:06:11.199><c> good</c>

00:06:11.950 --> 00:06:11.960 align:start position:0%
to the market standards which is a good
 

00:06:11.960 --> 00:06:15.070 align:start position:0%
to the market standards which is a good
sign<00:06:12.960><c> so</c><00:06:13.240><c> these</c><00:06:13.400><c> are</c><00:06:13.639><c> some</c><00:06:13.840><c> of</c><00:06:14.039><c> the</c><00:06:14.440><c> examples</c>

00:06:15.070 --> 00:06:15.080 align:start position:0%
sign so these are some of the examples
 

00:06:15.080 --> 00:06:17.309 align:start position:0%
sign so these are some of the examples
where<00:06:15.240><c> you</c><00:06:15.360><c> can</c><00:06:15.599><c> see</c><00:06:15.919><c> how</c><00:06:16.360><c> the</c><00:06:16.560><c> quantitative</c>

00:06:17.309 --> 00:06:17.319 align:start position:0%
where you can see how the quantitative
 

00:06:17.319 --> 00:06:20.990 align:start position:0%
where you can see how the quantitative
analysis<00:06:17.919><c> of</c><00:06:18.039><c> our</c><00:06:18.639><c> competitor</c><00:06:19.840><c> um</c><00:06:20.840><c> uh</c>

00:06:20.990 --> 00:06:21.000 align:start position:0%
analysis of our competitor um uh
 

00:06:21.000 --> 00:06:23.110 align:start position:0%
analysis of our competitor um uh
insights<00:06:21.520><c> could</c><00:06:21.720><c> be</c><00:06:21.960><c> taken</c><00:06:22.639><c> with</c>

00:06:23.110 --> 00:06:23.120 align:start position:0%
insights could be taken with
 

00:06:23.120 --> 00:06:25.070 align:start position:0%
insights could be taken with
quantitative

00:06:25.070 --> 00:06:25.080 align:start position:0%
quantitative
 

00:06:25.080 --> 00:06:28.670 align:start position:0%
quantitative
data<00:06:26.080><c> so</c><00:06:26.319><c> there</c><00:06:26.440><c> are</c><00:06:26.720><c> some</c><00:06:27.560><c> data</c><00:06:27.919><c> limitations</c>

00:06:28.670 --> 00:06:28.680 align:start position:0%
data so there are some data limitations
 

00:06:28.680 --> 00:06:32.189 align:start position:0%
data so there are some data limitations
as<00:06:28.919><c> well</c><00:06:30.000><c> so</c><00:06:30.520><c> when</c><00:06:30.680><c> it</c><00:06:30.880><c> comes</c><00:06:31.120><c> to</c><00:06:31.560><c> cost</c><00:06:31.840><c> of</c><00:06:32.000><c> the</c>

00:06:32.189 --> 00:06:32.199 align:start position:0%
as well so when it comes to cost of the
 

00:06:32.199 --> 00:06:34.189 align:start position:0%
as well so when it comes to cost of the
components<00:06:33.000><c> that</c><00:06:33.160><c> are</c><00:06:33.360><c> offered</c><00:06:33.800><c> by</c><00:06:34.000><c> the</c>

00:06:34.189 --> 00:06:34.199 align:start position:0%
components that are offered by the
 

00:06:34.199 --> 00:06:37.550 align:start position:0%
components that are offered by the
suppliers<00:06:35.000><c> to</c><00:06:35.199><c> the</c><00:06:35.880><c> competitors</c><00:06:36.880><c> may</c><00:06:37.080><c> not</c><00:06:37.280><c> be</c>

00:06:37.550 --> 00:06:37.560 align:start position:0%
suppliers to the competitors may not be
 

00:06:37.560 --> 00:06:39.990 align:start position:0%
suppliers to the competitors may not be
very<00:06:37.840><c> easily</c><00:06:38.479><c> available</c><00:06:39.479><c> there</c><00:06:39.639><c> could</c><00:06:39.840><c> be</c>

00:06:39.990 --> 00:06:40.000 align:start position:0%
very easily available there could be
 

00:06:40.000 --> 00:06:41.950 align:start position:0%
very easily available there could be
some<00:06:40.319><c> special</c><00:06:40.720><c> discounts</c><00:06:41.280><c> offered</c><00:06:41.680><c> by</c><00:06:41.840><c> the</c>

00:06:41.950 --> 00:06:41.960 align:start position:0%
some special discounts offered by the
 

00:06:41.960 --> 00:06:43.870 align:start position:0%
some special discounts offered by the
suppliers<00:06:42.440><c> to</c><00:06:42.599><c> the</c><00:06:42.720><c> competitors</c><00:06:43.520><c> which</c><00:06:43.720><c> you</c>

00:06:43.870 --> 00:06:43.880 align:start position:0%
suppliers to the competitors which you
 

00:06:43.880 --> 00:06:47.870 align:start position:0%
suppliers to the competitors which you
might<00:06:44.080><c> not</c><00:06:44.319><c> be</c><00:06:44.479><c> aware</c><00:06:44.960><c> of</c><00:06:45.960><c> or</c><00:06:46.440><c> it</c><00:06:46.560><c> could</c><00:06:46.759><c> be</c><00:06:47.639><c> how</c>

00:06:47.870 --> 00:06:47.880 align:start position:0%
might not be aware of or it could be how
 

00:06:47.880 --> 00:06:49.749 align:start position:0%
might not be aware of or it could be how
well<00:06:48.120><c> the</c><00:06:48.280><c> suppliers</c><00:06:48.759><c> are</c><00:06:49.000><c> performing</c><00:06:49.599><c> in</c>

00:06:49.749 --> 00:06:49.759 align:start position:0%
well the suppliers are performing in
 

00:06:49.759 --> 00:06:51.870 align:start position:0%
well the suppliers are performing in
terms<00:06:50.039><c> of</c><00:06:50.199><c> the</c><00:06:50.360><c> quality</c><00:06:51.120><c> and</c><00:06:51.360><c> there</c><00:06:51.520><c> could</c><00:06:51.680><c> be</c>

00:06:51.870 --> 00:06:51.880 align:start position:0%
terms of the quality and there could be
 

00:06:51.880 --> 00:06:54.550 align:start position:0%
terms of the quality and there could be
no<00:06:52.080><c> measure</c><00:06:52.520><c> for</c><00:06:52.759><c> you</c><00:06:53.280><c> to</c><00:06:53.560><c> get</c><00:06:53.840><c> these</c><00:06:54.280><c> from</c>

00:06:54.550 --> 00:06:54.560 align:start position:0%
no measure for you to get these from
 

00:06:54.560 --> 00:06:56.629 align:start position:0%
no measure for you to get these from
your<00:06:54.840><c> competitor</c><00:06:55.440><c> companies</c><00:06:56.080><c> in</c><00:06:56.280><c> such</c>

00:06:56.629 --> 00:06:56.639 align:start position:0%
your competitor companies in such
 

00:06:56.639 --> 00:06:58.830 align:start position:0%
your competitor companies in such
scenarios<00:06:57.639><c> there</c><00:06:57.800><c> are</c><00:06:58.039><c> some</c><00:06:58.319><c> industry</c>

00:06:58.830 --> 00:06:58.840 align:start position:0%
scenarios there are some industry
 

00:06:58.840 --> 00:07:01.629 align:start position:0%
scenarios there are some industry
standards<00:06:59.360><c> which</c><00:06:59.599><c> which</c><00:06:59.720><c> you</c><00:07:00.000><c> can</c><00:07:01.000><c> uh</c><00:07:01.160><c> derive</c>

00:07:01.629 --> 00:07:01.639 align:start position:0%
standards which which you can uh derive
 

00:07:01.639 --> 00:07:04.909 align:start position:0%
standards which which you can uh derive
and<00:07:02.080><c> get</c><00:07:02.240><c> some</c><00:07:02.479><c> conclusions</c>

00:07:04.909 --> 00:07:04.919 align:start position:0%
and get some conclusions
 

00:07:04.919 --> 00:07:07.990 align:start position:0%
and get some conclusions
into<00:07:05.919><c> when</c><00:07:06.080><c> it</c><00:07:06.240><c> comes</c><00:07:06.479><c> to</c><00:07:06.639><c> the</c><00:07:06.879><c> qualitative</c>

00:07:07.990 --> 00:07:08.000 align:start position:0%
into when it comes to the qualitative
 

00:07:08.000 --> 00:07:10.469 align:start position:0%
into when it comes to the qualitative
insights<00:07:09.000><c> that</c><00:07:09.160><c> would</c><00:07:09.360><c> be</c><00:07:09.560><c> supported</c><00:07:10.160><c> by</c>

00:07:10.469 --> 00:07:10.479 align:start position:0%
insights that would be supported by
 

00:07:10.479 --> 00:07:13.589 align:start position:0%
insights that would be supported by
secondary<00:07:11.039><c> research</c><00:07:11.960><c> data</c><00:07:12.960><c> today</c><00:07:13.280><c> we'll</c>

00:07:13.589 --> 00:07:13.599 align:start position:0%
secondary research data today we'll
 

00:07:13.599 --> 00:07:15.270 align:start position:0%
secondary research data today we'll
focus<00:07:14.039><c> a</c><00:07:14.160><c> little</c><00:07:14.400><c> bit</c><00:07:14.599><c> more</c><00:07:14.919><c> on</c><00:07:15.120><c> the</c>

00:07:15.270 --> 00:07:15.280 align:start position:0%
focus a little bit more on the
 

00:07:15.280 --> 00:07:17.309 align:start position:0%
focus a little bit more on the
qualitative<00:07:15.919><c> analysis</c><00:07:16.560><c> where</c><00:07:16.720><c> we</c><00:07:16.879><c> try</c><00:07:17.080><c> to</c>

00:07:17.309 --> 00:07:17.319 align:start position:0%
qualitative analysis where we try to
 

00:07:17.319 --> 00:07:19.909 align:start position:0%
qualitative analysis where we try to
gather<00:07:17.680><c> the</c><00:07:17.879><c> information</c><00:07:18.759><c> from</c><00:07:19.000><c> the</c><00:07:19.160><c> internet</c>

00:07:19.909 --> 00:07:19.919 align:start position:0%
gather the information from the internet
 

00:07:19.919 --> 00:07:22.469 align:start position:0%
gather the information from the internet
it<00:07:20.039><c> could</c><00:07:20.199><c> be</c><00:07:20.479><c> the</c><00:07:20.599><c> news</c><00:07:21.039><c> or</c><00:07:21.280><c> blogs</c><00:07:21.759><c> or</c><00:07:22.080><c> company</c>

00:07:22.469 --> 00:07:22.479 align:start position:0%
it could be the news or blogs or company
 

00:07:22.479 --> 00:07:25.909 align:start position:0%
it could be the news or blogs or company
annual<00:07:23.240><c> reports</c><00:07:24.240><c> and</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
annual reports and
 

00:07:25.919 --> 00:07:28.589 align:start position:0%
annual reports and
more<00:07:26.919><c> so</c><00:07:27.199><c> there</c><00:07:27.360><c> could</c><00:07:27.520><c> be</c><00:07:27.720><c> some</c><00:07:27.960><c> dedicated</c>

00:07:28.589 --> 00:07:28.599 align:start position:0%
more so there could be some dedicated
 

00:07:28.599 --> 00:07:30.110 align:start position:0%
more so there could be some dedicated
websites<00:07:29.120><c> that</c><00:07:29.240><c> you</c><00:07:29.319><c> want</c><00:07:29.520><c> want</c><00:07:29.639><c> to</c><00:07:29.720><c> feed</c><00:07:29.960><c> in</c>

00:07:30.110 --> 00:07:30.120 align:start position:0%
websites that you want want to feed in
 

00:07:30.120 --> 00:07:33.629 align:start position:0%
websites that you want want to feed in
to<00:07:30.360><c> your</c><00:07:31.199><c> uh</c><00:07:31.560><c> gen</c><00:07:32.120><c> chatbot</c><00:07:32.800><c> application</c><00:07:33.479><c> which</c>

00:07:33.629 --> 00:07:33.639 align:start position:0%
to your uh gen chatbot application which
 

00:07:33.639 --> 00:07:36.070 align:start position:0%
to your uh gen chatbot application which
you<00:07:33.759><c> want</c><00:07:33.879><c> to</c><00:07:34.039><c> use</c><00:07:34.280><c> them</c><00:07:34.479><c> as</c><00:07:34.680><c> primary</c>

00:07:36.070 --> 00:07:36.080 align:start position:0%
you want to use them as primary
 

00:07:36.080 --> 00:07:38.189 align:start position:0%
you want to use them as primary
sources<00:07:37.080><c> it</c><00:07:37.199><c> could</c><00:07:37.479><c> then</c><00:07:37.680><c> collect</c><00:07:38.039><c> the</c>

00:07:38.189 --> 00:07:38.199 align:start position:0%
sources it could then collect the
 

00:07:38.199 --> 00:07:40.150 align:start position:0%
sources it could then collect the
information<00:07:38.680><c> in</c><00:07:38.800><c> a</c><00:07:39.000><c> specific</c><00:07:39.479><c> format</c><00:07:40.000><c> and</c>

00:07:40.150 --> 00:07:40.160 align:start position:0%
information in a specific format and
 

00:07:40.160 --> 00:07:42.670 align:start position:0%
information in a specific format and
showcase<00:07:40.639><c> the</c><00:07:40.800><c> results</c><00:07:41.199><c> to</c><00:07:41.360><c> you</c><00:07:41.759><c> in</c><00:07:41.960><c> the</c>

00:07:42.670 --> 00:07:42.680 align:start position:0%
showcase the results to you in the
 

00:07:42.680 --> 00:07:45.029 align:start position:0%
showcase the results to you in the
required

00:07:45.029 --> 00:07:45.039 align:start position:0%
required
 

00:07:45.039 --> 00:07:48.790 align:start position:0%
required
format<00:07:46.039><c> so</c><00:07:46.479><c> secondary</c><00:07:47.120><c> research</c><00:07:47.639><c> data</c><00:07:48.039><c> points</c>

00:07:48.790 --> 00:07:48.800 align:start position:0%
format so secondary research data points
 

00:07:48.800 --> 00:07:50.909 align:start position:0%
format so secondary research data points
when<00:07:48.960><c> you</c><00:07:49.280><c> talk</c><00:07:49.520><c> about</c><00:07:49.800><c> the</c><00:07:50.000><c> data</c><00:07:50.319><c> points</c><00:07:50.759><c> the</c>

00:07:50.909 --> 00:07:50.919 align:start position:0%
when you talk about the data points the
 

00:07:50.919 --> 00:07:53.830 align:start position:0%
when you talk about the data points the
data<00:07:51.199><c> sources</c><00:07:51.720><c> could</c><00:07:52.120><c> be</c><00:07:53.120><c> it</c><00:07:53.280><c> could</c><00:07:53.520><c> be</c><00:07:53.680><c> some</c>

00:07:53.830 --> 00:07:53.840 align:start position:0%
data sources could be it could be some
 

00:07:53.840 --> 00:07:56.749 align:start position:0%
data sources could be it could be some
of<00:07:54.000><c> the</c><00:07:54.159><c> subscriptions</c><00:07:55.039><c> of</c><00:07:55.560><c> journals</c><00:07:56.560><c> and</c>

00:07:56.749 --> 00:07:56.759 align:start position:0%
of the subscriptions of journals and
 

00:07:56.759 --> 00:08:00.149 align:start position:0%
of the subscriptions of journals and
magazines<00:07:57.599><c> it</c><00:07:57.720><c> could</c><00:07:57.919><c> be</c><00:07:58.199><c> annual</c><00:07:58.599><c> reports</c><00:07:59.680><c> or</c>

00:08:00.149 --> 00:08:00.159 align:start position:0%
magazines it could be annual reports or
 

00:08:00.159 --> 00:08:02.909 align:start position:0%
magazines it could be annual reports or
some<00:08:00.400><c> new</c><00:08:00.759><c> speed</c><00:08:01.159><c> and</c><00:08:01.400><c> daily</c><00:08:01.919><c> updates</c>

00:08:02.909 --> 00:08:02.919 align:start position:0%
some new speed and daily updates
 

00:08:02.919 --> 00:08:04.950 align:start position:0%
some new speed and daily updates
government<00:08:03.879><c> or</c>

00:08:04.950 --> 00:08:04.960 align:start position:0%
government or
 

00:08:04.960 --> 00:08:08.350 align:start position:0%
government or
regulatory<00:08:05.960><c> data</c><00:08:06.560><c> trade</c><00:08:06.960><c> associations</c><00:08:07.919><c> and</c>

00:08:08.350 --> 00:08:08.360 align:start position:0%
regulatory data trade associations and
 

00:08:08.360 --> 00:08:11.149 align:start position:0%
regulatory data trade associations and
financial<00:08:09.039><c> database</c><00:08:10.039><c> and</c><00:08:10.479><c> uh</c><00:08:10.680><c> academic</c>

00:08:11.149 --> 00:08:11.159 align:start position:0%
financial database and uh academic
 

00:08:11.159 --> 00:08:12.990 align:start position:0%
financial database and uh academic
research<00:08:11.759><c> so</c><00:08:12.000><c> these</c><00:08:12.199><c> could</c><00:08:12.360><c> be</c><00:08:12.560><c> some</c><00:08:12.720><c> of</c><00:08:12.840><c> the</c>

00:08:12.990 --> 00:08:13.000 align:start position:0%
research so these could be some of the
 

00:08:13.000 --> 00:08:15.550 align:start position:0%
research so these could be some of the
data<00:08:13.599><c> sources</c><00:08:14.599><c> and</c><00:08:14.759><c> when</c><00:08:14.919><c> it</c><00:08:15.080><c> comes</c><00:08:15.280><c> to</c>

00:08:15.550 --> 00:08:15.560 align:start position:0%
data sources and when it comes to
 

00:08:15.560 --> 00:08:17.029 align:start position:0%
data sources and when it comes to
parameters<00:08:16.159><c> like</c><00:08:16.319><c> you</c><00:08:16.400><c> know</c><00:08:16.639><c> design</c>

00:08:17.029 --> 00:08:17.039 align:start position:0%
parameters like you know design
 

00:08:17.039 --> 00:08:18.990 align:start position:0%
parameters like you know design
benchmarking<00:08:17.879><c> or</c><00:08:18.120><c> cost</c>

00:08:18.990 --> 00:08:19.000 align:start position:0%
benchmarking or cost
 

00:08:19.000 --> 00:08:21.189 align:start position:0%
benchmarking or cost
benchmarking<00:08:20.000><c> you</c><00:08:20.120><c> can</c><00:08:20.319><c> see</c><00:08:20.599><c> that</c><00:08:20.800><c> there</c><00:08:20.919><c> are</c>

00:08:21.189 --> 00:08:21.199 align:start position:0%
benchmarking you can see that there are
 

00:08:21.199 --> 00:08:24.430 align:start position:0%
benchmarking you can see that there are
numbers<00:08:21.639><c> written</c><00:08:22.080><c> here</c><00:08:22.479><c> 1</c><00:08:22.800><c> 2</c><00:08:23.120><c> 3</c><00:08:23.479><c> and</c><00:08:23.680><c> five</c><00:08:24.120><c> for</c>

00:08:24.430 --> 00:08:24.440 align:start position:0%
numbers written here 1 2 3 and five for
 

00:08:24.440 --> 00:08:27.350 align:start position:0%
numbers written here 1 2 3 and five for
design<00:08:24.800><c> benchmarking</c><00:08:25.560><c> that</c><00:08:26.039><c> means</c><00:08:27.039><c> for</c>

00:08:27.350 --> 00:08:27.360 align:start position:0%
design benchmarking that means for
 

00:08:27.360 --> 00:08:29.029 align:start position:0%
design benchmarking that means for
design<00:08:27.680><c> benchmarking</c><00:08:28.360><c> you</c><00:08:28.440><c> will</c><00:08:28.639><c> be</c><00:08:28.800><c> looking</c>

00:08:29.029 --> 00:08:29.039 align:start position:0%
design benchmarking you will be looking
 

00:08:29.039 --> 00:08:32.430 align:start position:0%
design benchmarking you will be looking
into<00:08:29.800><c> subscriptions</c><00:08:30.800><c> annual</c><00:08:31.199><c> reports</c><00:08:32.039><c> news</c>

00:08:32.430 --> 00:08:32.440 align:start position:0%
into subscriptions annual reports news
 

00:08:32.440 --> 00:08:34.990 align:start position:0%
into subscriptions annual reports news
feeds<00:08:33.159><c> and</c><00:08:33.440><c> trade</c><00:08:33.839><c> associations</c><00:08:34.640><c> these</c><00:08:34.800><c> could</c>

00:08:34.990 --> 00:08:35.000 align:start position:0%
feeds and trade associations these could
 

00:08:35.000 --> 00:08:36.949 align:start position:0%
feeds and trade associations these could
be<00:08:35.159><c> some</c><00:08:35.320><c> of</c><00:08:35.440><c> the</c><00:08:35.599><c> sources</c><00:08:36.399><c> when</c><00:08:36.560><c> you</c><00:08:36.640><c> want</c><00:08:36.800><c> to</c>

00:08:36.949 --> 00:08:36.959 align:start position:0%
be some of the sources when you want to
 

00:08:36.959 --> 00:08:39.949 align:start position:0%
be some of the sources when you want to
do<00:08:37.159><c> some</c><00:08:37.479><c> design</c><00:08:37.760><c> benchmarking</c><00:08:38.519><c> and</c><00:08:38.719><c> derive</c>

00:08:39.949 --> 00:08:39.959 align:start position:0%
do some design benchmarking and derive
 

00:08:39.959 --> 00:08:43.149 align:start position:0%
do some design benchmarking and derive
some<00:08:40.959><c> uh</c><00:08:41.080><c> key</c><00:08:41.360><c> insights</c><00:08:42.159><c> maybe</c><00:08:42.519><c> some</c><00:08:42.880><c> returns</c>

00:08:43.149 --> 00:08:43.159 align:start position:0%
some uh key insights maybe some returns
 

00:08:43.159 --> 00:08:48.150 align:start position:0%
some uh key insights maybe some returns
and<00:08:43.360><c> recalls</c><00:08:44.000><c> and</c><00:08:44.159><c> so</c>

00:08:48.150 --> 00:08:48.160 align:start position:0%
 
 

00:08:48.160 --> 00:08:52.070 align:start position:0%
 
on<00:08:49.279><c> so</c><00:08:50.279><c> when</c><00:08:50.440><c> it</c><00:08:50.600><c> comes</c><00:08:50.839><c> to</c><00:08:51.000><c> the</c><00:08:51.160><c> process</c><00:08:51.560><c> flow</c>

00:08:52.070 --> 00:08:52.080 align:start position:0%
on so when it comes to the process flow
 

00:08:52.080 --> 00:08:55.269 align:start position:0%
on so when it comes to the process flow
for<00:08:52.399><c> secondary</c><00:08:52.959><c> research</c><00:08:53.920><c> tool</c><00:08:54.920><c> there</c><00:08:55.080><c> could</c>

00:08:55.269 --> 00:08:55.279 align:start position:0%
for secondary research tool there could
 

00:08:55.279 --> 00:08:58.389 align:start position:0%
for secondary research tool there could
be<00:08:55.839><c> a</c><00:08:56.120><c> process</c><00:08:56.920><c> there</c><00:08:57.080><c> could</c><00:08:57.240><c> be</c><00:08:57.399><c> a</c><00:08:57.600><c> Persona</c><00:08:58.200><c> or</c>

00:08:58.389 --> 00:08:58.399 align:start position:0%
be a process there could be a Persona or
 

00:08:58.399 --> 00:09:02.030 align:start position:0%
be a process there could be a Persona or
an<00:08:58.600><c> enabler</c><00:08:59.680><c> and</c><00:09:00.560><c> uh</c><00:09:00.720><c> I</c><00:09:00.800><c> have</c><00:09:00.959><c> also</c><00:09:01.240><c> quoted</c><00:09:01.800><c> an</c>

00:09:02.030 --> 00:09:02.040 align:start position:0%
an enabler and uh I have also quoted an
 

00:09:02.040 --> 00:09:04.590 align:start position:0%
an enabler and uh I have also quoted an
example<00:09:02.640><c> here</c><00:09:03.640><c> let's</c><00:09:03.880><c> say</c><00:09:04.160><c> there</c><00:09:04.279><c> is</c><00:09:04.399><c> a</c>

00:09:04.590 --> 00:09:04.600 align:start position:0%
example here let's say there is a
 

00:09:04.600 --> 00:09:06.550 align:start position:0%
example here let's say there is a
business<00:09:05.000><c> query</c><00:09:05.519><c> that</c><00:09:05.760><c> a</c><00:09:06.000><c> procurement</c>

00:09:06.550 --> 00:09:06.560 align:start position:0%
business query that a procurement
 

00:09:06.560 --> 00:09:10.750 align:start position:0%
business query that a procurement
manager<00:09:07.279><c> or</c><00:09:07.760><c> a</c><00:09:08.640><c> supply</c><00:09:09.000><c> chain</c><00:09:09.320><c> planner</c><00:09:10.000><c> has</c><00:09:10.600><c> in</c>

00:09:10.750 --> 00:09:10.760 align:start position:0%
manager or a supply chain planner has in
 

00:09:10.760 --> 00:09:13.230 align:start position:0%
manager or a supply chain planner has in
terms<00:09:11.120><c> of</c><00:09:11.399><c> what</c><00:09:11.519><c> is</c><00:09:11.640><c> the</c><00:09:11.839><c> cost</c><00:09:12.079><c> reduction</c>

00:09:13.230 --> 00:09:13.240 align:start position:0%
terms of what is the cost reduction
 

00:09:13.240 --> 00:09:17.550 align:start position:0%
terms of what is the cost reduction
strategy<00:09:14.240><c> uh</c><00:09:14.640><c> that</c><00:09:14.839><c> my</c><00:09:15.079><c> competitor</c><00:09:15.720><c> is</c><00:09:16.320><c> uh</c>

00:09:17.550 --> 00:09:17.560 align:start position:0%
strategy uh that my competitor is uh
 

00:09:17.560 --> 00:09:19.790 align:start position:0%
strategy uh that my competitor is uh
obtaining<00:09:18.560><c> if</c><00:09:18.760><c> that</c><00:09:18.880><c> is</c><00:09:19.040><c> the</c><00:09:19.200><c> kind</c><00:09:19.440><c> of</c><00:09:19.640><c> a</c>

00:09:19.790 --> 00:09:19.800 align:start position:0%
obtaining if that is the kind of a
 

00:09:19.800 --> 00:09:22.670 align:start position:0%
obtaining if that is the kind of a
business<00:09:20.160><c> query</c><00:09:20.600><c> that</c><00:09:20.720><c> is</c><00:09:21.000><c> there</c><00:09:21.959><c> then</c><00:09:22.519><c> the</c>

00:09:22.670 --> 00:09:22.680 align:start position:0%
business query that is there then the
 

00:09:22.680 --> 00:09:24.750 align:start position:0%
business query that is there then the
secondary<00:09:23.200><c> data</c><00:09:23.600><c> source</c><00:09:24.079><c> could</c><00:09:24.279><c> be</c><00:09:24.519><c> some</c>

00:09:24.750 --> 00:09:24.760 align:start position:0%
secondary data source could be some
 

00:09:24.760 --> 00:09:27.829 align:start position:0%
secondary data source could be some
journals<00:09:25.519><c> or</c><00:09:25.760><c> some</c><00:09:26.000><c> news</c><00:09:26.320><c> reports</c><00:09:27.279><c> use</c><00:09:27.680><c> uh</c>

00:09:27.829 --> 00:09:27.839 align:start position:0%
journals or some news reports use uh
 

00:09:27.839 --> 00:09:30.310 align:start position:0%
journals or some news reports use uh
which<00:09:28.000><c> are</c><00:09:28.360><c> having</c><00:09:28.720><c> some</c><00:09:29.440><c> substantiating</c>

00:09:30.310 --> 00:09:30.320 align:start position:0%
which are having some substantiating
 

00:09:30.320 --> 00:09:33.030 align:start position:0%
which are having some substantiating
data<00:09:30.720><c> required</c><00:09:31.200><c> for</c><00:09:31.480><c> this</c><00:09:31.880><c> insights</c><00:09:32.880><c> and</c>

00:09:33.030 --> 00:09:33.040 align:start position:0%
data required for this insights and
 

00:09:33.040 --> 00:09:35.990 align:start position:0%
data required for this insights and
using<00:09:33.360><c> the</c><00:09:33.519><c> secondary</c><00:09:34.000><c> research</c><00:09:34.680><c> Tool</c><00:09:35.680><c> uh</c>

00:09:35.990 --> 00:09:36.000 align:start position:0%
using the secondary research Tool uh
 

00:09:36.000 --> 00:09:38.150 align:start position:0%
using the secondary research Tool uh
which<00:09:36.120><c> is</c><00:09:36.440><c> enabled</c><00:09:36.959><c> using</c><00:09:37.360><c> the</c><00:09:37.480><c> Gen</c>

00:09:38.150 --> 00:09:38.160 align:start position:0%
which is enabled using the Gen
 

00:09:38.160 --> 00:09:40.949 align:start position:0%
which is enabled using the Gen
automation<00:09:38.760><c> tool</c><00:09:39.720><c> it</c><00:09:39.839><c> will</c><00:09:40.079><c> read</c><00:09:40.440><c> some</c><00:09:40.640><c> of</c><00:09:40.800><c> the</c>

00:09:40.949 --> 00:09:40.959 align:start position:0%
automation tool it will read some of the
 

00:09:40.959 --> 00:09:43.630 align:start position:0%
automation tool it will read some of the
keywords<00:09:41.640><c> which</c><00:09:41.760><c> are</c><00:09:42.040><c> posed</c><00:09:42.880><c> uh</c><00:09:43.079><c> as</c><00:09:43.279><c> part</c><00:09:43.480><c> of</c>

00:09:43.630 --> 00:09:43.640 align:start position:0%
keywords which are posed uh as part of
 

00:09:43.640 --> 00:09:46.590 align:start position:0%
keywords which are posed uh as part of
the<00:09:44.000><c> query</c><00:09:45.000><c> and</c><00:09:45.399><c> based</c><00:09:45.680><c> on</c><00:09:45.839><c> the</c><00:09:45.959><c> pre-trained</c>

00:09:46.590 --> 00:09:46.600 align:start position:0%
the query and based on the pre-trained
 

00:09:46.600 --> 00:09:49.710 align:start position:0%
the query and based on the pre-trained
model<00:09:47.320><c> it'll</c><00:09:47.600><c> try</c><00:09:47.839><c> to</c><00:09:48.120><c> extract</c><00:09:48.640><c> all</c><00:09:48.839><c> the</c>

00:09:49.710 --> 00:09:49.720 align:start position:0%
model it'll try to extract all the
 

00:09:49.720 --> 00:09:52.310 align:start position:0%
model it'll try to extract all the
information<00:09:50.720><c> uh</c><00:09:50.920><c> from</c><00:09:51.279><c> the</c><00:09:51.560><c> secondary</c><00:09:52.040><c> data</c>

00:09:52.310 --> 00:09:52.320 align:start position:0%
information uh from the secondary data
 

00:09:52.320 --> 00:09:55.350 align:start position:0%
information uh from the secondary data
source<00:09:53.200><c> and</c><00:09:53.440><c> showcase</c><00:09:53.880><c> it</c><00:09:54.160><c> as</c><00:09:54.360><c> the</c><00:09:54.560><c> insights</c>

00:09:55.350 --> 00:09:55.360 align:start position:0%
source and showcase it as the insights
 

00:09:55.360 --> 00:09:56.590 align:start position:0%
source and showcase it as the insights
it<00:09:55.480><c> could</c><00:09:55.640><c> be</c><00:09:55.839><c> prescriptive</c><00:09:56.440><c> or</c>

00:09:56.590 --> 00:09:56.600 align:start position:0%
it could be prescriptive or
 

00:09:56.600 --> 00:09:58.829 align:start position:0%
it could be prescriptive or
forward-looking<00:09:57.160><c> but</c><00:09:58.120><c> the</c><00:09:58.240><c> insights</c><00:09:58.680><c> would</c>

00:09:58.829 --> 00:09:58.839 align:start position:0%
forward-looking but the insights would
 

00:09:58.839 --> 00:10:05.430 align:start position:0%
forward-looking but the insights would
be<00:09:59.000><c> in</c><00:09:59.160><c> a</c><00:09:59.760><c> uh</c><00:10:00.000><c> readable</c><00:10:00.519><c> format</c><00:10:00.959><c> it</c><00:10:01.079><c> would</c><00:10:01.279><c> be</c>

00:10:05.430 --> 00:10:05.440 align:start position:0%
 
 

00:10:05.440 --> 00:10:08.710 align:start position:0%
 
delivered<00:10:06.440><c> so</c><00:10:06.720><c> let's</c><00:10:07.000><c> say</c><00:10:07.720><c> there</c><00:10:07.839><c> are</c><00:10:08.480><c> uh</c>

00:10:08.710 --> 00:10:08.720 align:start position:0%
delivered so let's say there are uh
 

00:10:08.720 --> 00:10:10.269 align:start position:0%
delivered so let's say there are uh
there<00:10:08.839><c> is</c><00:10:09.000><c> a</c><00:10:09.160><c> medical</c><00:10:09.600><c> device</c><00:10:10.000><c> which</c><00:10:10.120><c> is</c>

00:10:10.269 --> 00:10:10.279 align:start position:0%
there is a medical device which is
 

00:10:10.279 --> 00:10:13.470 align:start position:0%
there is a medical device which is
called<00:10:10.519><c> as</c><00:10:10.680><c> an</c><00:10:11.320><c> analyzer</c><00:10:12.320><c> and</c><00:10:12.760><c> we</c><00:10:12.920><c> want</c><00:10:13.079><c> to</c><00:10:13.279><c> do</c>

00:10:13.470 --> 00:10:13.480 align:start position:0%
called as an analyzer and we want to do
 

00:10:13.480 --> 00:10:15.030 align:start position:0%
called as an analyzer and we want to do
a<00:10:13.680><c> comparative</c>

00:10:15.030 --> 00:10:15.040 align:start position:0%
a comparative
 

00:10:15.040 --> 00:10:18.389 align:start position:0%
a comparative
study<00:10:16.040><c> uh</c><00:10:16.399><c> from</c><00:10:17.200><c> uh</c><00:10:17.519><c> there</c><00:10:17.680><c> could</c><00:10:17.880><c> be</c><00:10:18.120><c> around</c>

00:10:18.389 --> 00:10:18.399 align:start position:0%
study uh from uh there could be around
 

00:10:18.399 --> 00:10:21.509 align:start position:0%
study uh from uh there could be around
some<00:10:18.680><c> 25</c><00:10:19.360><c> features</c><00:10:20.360><c> uh</c><00:10:20.519><c> in</c><00:10:20.640><c> an</c><00:10:20.880><c> analyzer</c>

00:10:21.509 --> 00:10:21.519 align:start position:0%
some 25 features uh in an analyzer
 

00:10:21.519 --> 00:10:24.470 align:start position:0%
some 25 features uh in an analyzer
device<00:10:21.920><c> and</c><00:10:22.040><c> you</c><00:10:22.160><c> want</c><00:10:22.320><c> to</c><00:10:22.880><c> compare</c><00:10:23.880><c> uh</c><00:10:24.040><c> your</c>

00:10:24.470 --> 00:10:24.480 align:start position:0%
device and you want to compare uh your
 

00:10:24.480 --> 00:10:27.069 align:start position:0%
device and you want to compare uh your
analyzer<00:10:25.200><c> device</c><00:10:25.640><c> your</c><00:10:25.920><c> company's</c><00:10:26.480><c> analyzer</c>

00:10:27.069 --> 00:10:27.079 align:start position:0%
analyzer device your company's analyzer
 

00:10:27.079 --> 00:10:29.870 align:start position:0%
analyzer device your company's analyzer
device<00:10:27.480><c> with</c><00:10:27.760><c> eight</c><00:10:28.160><c> competitors</c><00:10:29.480><c> so</c><00:10:29.760><c> that</c>

00:10:29.870 --> 00:10:29.880 align:start position:0%
device with eight competitors so that
 

00:10:29.880 --> 00:10:32.750 align:start position:0%
device with eight competitors so that
means<00:10:30.279><c> 25</c><00:10:30.800><c> features</c><00:10:31.320><c> into</c><00:10:31.640><c> eight</c><00:10:32.040><c> competitors</c>

00:10:32.750 --> 00:10:32.760 align:start position:0%
means 25 features into eight competitors
 

00:10:32.760 --> 00:10:34.990 align:start position:0%
means 25 features into eight competitors
that<00:10:32.880><c> is</c><00:10:33.120><c> around</c><00:10:33.640><c> 200</c><00:10:33.959><c> fields</c><00:10:34.440><c> that</c><00:10:34.600><c> are</c><00:10:34.760><c> need</c>

00:10:34.990 --> 00:10:35.000 align:start position:0%
that is around 200 fields that are need
 

00:10:35.000 --> 00:10:37.710 align:start position:0%
that is around 200 fields that are need
to<00:10:35.160><c> be</c><00:10:35.320><c> filled</c><00:10:36.040><c> filled</c><00:10:36.399><c> up</c><00:10:36.959><c> as</c><00:10:37.079><c> you</c><00:10:37.279><c> can</c><00:10:37.440><c> see</c>

00:10:37.710 --> 00:10:37.720 align:start position:0%
to be filled filled up as you can see
 

00:10:37.720 --> 00:10:39.069 align:start position:0%
to be filled filled up as you can see
here<00:10:38.000><c> in</c><00:10:38.200><c> this</c>

00:10:39.069 --> 00:10:39.079 align:start position:0%
here in this
 

00:10:39.079 --> 00:10:42.590 align:start position:0%
here in this
table<00:10:40.079><c> if</c><00:10:40.360><c> this</c><00:10:40.680><c> has</c><00:10:40.839><c> to</c><00:10:41.000><c> be</c><00:10:41.279><c> manually</c><00:10:41.880><c> done</c>

00:10:42.590 --> 00:10:42.600 align:start position:0%
table if this has to be manually done
 

00:10:42.600 --> 00:10:45.269 align:start position:0%
table if this has to be manually done
then<00:10:42.920><c> it</c><00:10:43.040><c> is</c><00:10:43.279><c> a</c><00:10:43.480><c> time</c><00:10:43.800><c> consuming</c><00:10:44.360><c> task</c><00:10:45.040><c> and</c>

00:10:45.269 --> 00:10:45.279 align:start position:0%
then it is a time consuming task and
 

00:10:45.279 --> 00:10:47.190 align:start position:0%
then it is a time consuming task and
there<00:10:45.440><c> might</c><00:10:45.639><c> be</c><00:10:45.839><c> scenarios</c>

00:10:47.190 --> 00:10:47.200 align:start position:0%
there might be scenarios
 

00:10:47.200 --> 00:10:50.910 align:start position:0%
there might be scenarios
wherein<00:10:48.200><c> uh</c><00:10:48.480><c> the</c><00:10:48.639><c> manual</c><00:10:49.519><c> uh</c><00:10:50.320><c> the</c><00:10:50.480><c> people</c><00:10:50.800><c> who</c>

00:10:50.910 --> 00:10:50.920 align:start position:0%
wherein uh the manual uh the people who
 

00:10:50.920 --> 00:10:53.030 align:start position:0%
wherein uh the manual uh the people who
are<00:10:51.079><c> trying</c><00:10:51.320><c> to</c><00:10:51.480><c> put</c><00:10:51.680><c> the</c><00:10:51.800><c> manual</c><00:10:52.200><c> effort</c><00:10:52.680><c> they</c>

00:10:53.030 --> 00:10:53.040 align:start position:0%
are trying to put the manual effort they
 

00:10:53.040 --> 00:10:54.990 align:start position:0%
are trying to put the manual effort they
they<00:10:53.160><c> are</c><00:10:53.360><c> unable</c><00:10:53.760><c> to</c><00:10:53.880><c> fill</c><00:10:54.120><c> in</c><00:10:54.480><c> some</c><00:10:54.680><c> of</c><00:10:54.839><c> the</c>

00:10:54.990 --> 00:10:55.000 align:start position:0%
they are unable to fill in some of the
 

00:10:55.000 --> 00:10:56.829 align:start position:0%
they are unable to fill in some of the
boxes<00:10:55.480><c> here</c><00:10:55.800><c> because</c><00:10:56.000><c> they</c><00:10:56.120><c> were</c><00:10:56.320><c> not</c><00:10:56.480><c> able</c><00:10:56.680><c> to</c>

00:10:56.829 --> 00:10:56.839 align:start position:0%
boxes here because they were not able to
 

00:10:56.839 --> 00:11:00.829 align:start position:0%
boxes here because they were not able to
find<00:10:57.120><c> that</c><00:10:57.480><c> information</c><00:10:58.480><c> on</c><00:10:58.680><c> the</c><00:10:58.839><c> internet</c>

00:11:00.829 --> 00:11:00.839 align:start position:0%
find that information on the internet
 

00:11:00.839 --> 00:11:02.949 align:start position:0%
find that information on the internet
so<00:11:01.160><c> if</c><00:11:01.399><c> this</c><00:11:01.600><c> has</c><00:11:01.760><c> been</c><00:11:02.040><c> enabled</c><00:11:02.560><c> with</c><00:11:02.680><c> the</c>

00:11:02.949 --> 00:11:02.959 align:start position:0%
so if this has been enabled with the
 

00:11:02.959 --> 00:11:06.269 align:start position:0%
so if this has been enabled with the
chatbot<00:11:03.959><c> uh</c><00:11:04.160><c> gen</c><00:11:04.680><c> chatbot</c><00:11:05.240><c> application</c><00:11:06.120><c> this</c>

00:11:06.269 --> 00:11:06.279 align:start position:0%
chatbot uh gen chatbot application this
 

00:11:06.279 --> 00:11:08.670 align:start position:0%
chatbot uh gen chatbot application this
could<00:11:06.480><c> be</c><00:11:06.680><c> filled</c><00:11:06.959><c> up</c><00:11:07.160><c> within</c><00:11:07.399><c> a</c><00:11:07.560><c> few</c><00:11:07.800><c> seconds</c>

00:11:08.670 --> 00:11:08.680 align:start position:0%
could be filled up within a few seconds
 

00:11:08.680 --> 00:11:14.550 align:start position:0%
could be filled up within a few seconds
which<00:11:08.800><c> is</c><00:11:08.959><c> a</c><00:11:09.880><c> uh</c><00:11:10.480><c> very</c><00:11:10.680><c> much</c><00:11:11.200><c> time-saving</c><00:11:12.200><c> uh</c>

00:11:14.550 --> 00:11:14.560 align:start position:0%
which is a uh very much time-saving uh
 

00:11:14.560 --> 00:11:17.470 align:start position:0%
which is a uh very much time-saving uh
effort<00:11:15.560><c> so</c><00:11:15.880><c> we</c><00:11:16.079><c> have</c><00:11:16.279><c> coined</c><00:11:16.760><c> this</c><00:11:16.959><c> secondary</c>

00:11:17.470 --> 00:11:17.480 align:start position:0%
effort so we have coined this secondary
 

00:11:17.480 --> 00:11:19.629 align:start position:0%
effort so we have coined this secondary
research<00:11:17.959><c> tool</c><00:11:18.440><c> as</c><00:11:18.680><c> the</c><00:11:18.839><c> smart</c><00:11:19.200><c> business</c>

00:11:19.629 --> 00:11:19.639 align:start position:0%
research tool as the smart business
 

00:11:19.639 --> 00:11:22.550 align:start position:0%
research tool as the smart business
analyst<00:11:20.639><c> so</c><00:11:21.000><c> if</c><00:11:21.160><c> it</c><00:11:21.279><c> is</c><00:11:21.399><c> a</c><00:11:21.600><c> traditional</c><00:11:22.160><c> method</c>

00:11:22.550 --> 00:11:22.560 align:start position:0%
analyst so if it is a traditional method
 

00:11:22.560 --> 00:11:25.590 align:start position:0%
analyst so if it is a traditional method
versus<00:11:22.959><c> a</c><00:11:23.120><c> gen</c><00:11:23.639><c> powered</c><00:11:24.440><c> application</c><00:11:25.440><c> uh</c>

00:11:25.590 --> 00:11:25.600 align:start position:0%
versus a gen powered application uh
 

00:11:25.600 --> 00:11:28.150 align:start position:0%
versus a gen powered application uh
let's<00:11:25.839><c> say</c><00:11:26.079><c> there</c><00:11:26.200><c> is</c><00:11:26.320><c> a</c><00:11:26.480><c> business</c><00:11:27.000><c> user</c><00:11:28.000><c> a</c>

00:11:28.150 --> 00:11:28.160 align:start position:0%
let's say there is a business user a
 

00:11:28.160 --> 00:11:29.829 align:start position:0%
let's say there is a business user a
business<00:11:28.600><c> analyst</c>

00:11:29.829 --> 00:11:29.839 align:start position:0%
business analyst
 

00:11:29.839 --> 00:11:33.389 align:start position:0%
business analyst
um<00:11:30.200><c> who</c><00:11:30.360><c> is</c><00:11:30.600><c> manually</c><00:11:31.320><c> trying</c><00:11:31.680><c> to</c><00:11:32.399><c> access</c>

00:11:33.389 --> 00:11:33.399 align:start position:0%
um who is manually trying to access
 

00:11:33.399 --> 00:11:36.590 align:start position:0%
um who is manually trying to access
multiple<00:11:33.880><c> data</c><00:11:34.200><c> sources</c><00:11:34.760><c> like</c><00:11:35.399><c> PDFs</c><00:11:36.000><c> Word</c>

00:11:36.590 --> 00:11:36.600 align:start position:0%
multiple data sources like PDFs Word
 

00:11:36.600 --> 00:11:39.949 align:start position:0%
multiple data sources like PDFs Word
documents<00:11:37.600><c> share</c><00:11:38.079><c> points</c><00:11:39.040><c> and</c><00:11:39.360><c> also</c><00:11:39.720><c> some</c>

00:11:39.949 --> 00:11:39.959 align:start position:0%
documents share points and also some
 

00:11:39.959 --> 00:11:42.710 align:start position:0%
documents share points and also some
internal<00:11:40.560><c> databases</c><00:11:41.560><c> and</c><00:11:41.760><c> trying</c><00:11:42.079><c> to</c><00:11:42.480><c> put</c>

00:11:42.710 --> 00:11:42.720 align:start position:0%
internal databases and trying to put
 

00:11:42.720 --> 00:11:44.829 align:start position:0%
internal databases and trying to put
across<00:11:43.200><c> some</c><00:11:43.440><c> textual</c><00:11:43.920><c> information</c><00:11:44.480><c> or</c><00:11:44.639><c> a</c>

00:11:44.829 --> 00:11:44.839 align:start position:0%
across some textual information or a
 

00:11:44.839 --> 00:11:46.910 align:start position:0%
across some textual information or a
visual<00:11:45.600><c> which</c><00:11:45.760><c> could</c><00:11:46.000><c> answer</c><00:11:46.480><c> all</c><00:11:46.680><c> the</c>

00:11:46.910 --> 00:11:46.920 align:start position:0%
visual which could answer all the
 

00:11:46.920 --> 00:11:49.949 align:start position:0%
visual which could answer all the
question<00:11:47.880><c> that</c><00:11:48.519><c> uh</c><00:11:48.639><c> the</c><00:11:48.800><c> business</c><00:11:49.160><c> is</c><00:11:49.320><c> looking</c>

00:11:49.949 --> 00:11:49.959 align:start position:0%
question that uh the business is looking
 

00:11:49.959 --> 00:11:52.949 align:start position:0%
question that uh the business is looking
for<00:11:50.959><c> that</c><00:11:51.120><c> is</c><00:11:51.279><c> the</c><00:11:51.440><c> traditional</c><00:11:52.000><c> method</c><00:11:52.760><c> but</c>

00:11:52.949 --> 00:11:52.959 align:start position:0%
for that is the traditional method but
 

00:11:52.959 --> 00:11:56.269 align:start position:0%
for that is the traditional method but
whereas<00:11:53.440><c> when</c><00:11:53.639><c> we</c><00:11:53.839><c> have</c><00:11:54.160><c> a</c><00:11:54.839><c> gen</c><00:11:55.480><c> powered</c><00:11:56.160><c> uh</c>

00:11:56.269 --> 00:11:56.279 align:start position:0%
whereas when we have a gen powered uh
 

00:11:56.279 --> 00:11:59.389 align:start position:0%
whereas when we have a gen powered uh
chatbot<00:11:56.839><c> application</c><00:11:57.399><c> in</c><00:11:57.880><c> place</c><00:11:58.880><c> you</c><00:11:59.040><c> can</c><00:11:59.240><c> can</c>

00:11:59.389 --> 00:11:59.399 align:start position:0%
chatbot application in place you can can
 

00:11:59.399 --> 00:12:02.470 align:start position:0%
chatbot application in place you can can
connect<00:12:00.000><c> as</c><00:12:00.120><c> many</c><00:12:00.399><c> data</c><00:12:00.680><c> sources</c><00:12:01.279><c> as</c><00:12:01.399><c> you</c><00:12:01.600><c> want</c>

00:12:02.470 --> 00:12:02.480 align:start position:0%
connect as many data sources as you want
 

00:12:02.480 --> 00:12:05.269 align:start position:0%
connect as many data sources as you want
and<00:12:02.920><c> um</c><00:12:03.720><c> the</c><00:12:03.880><c> business</c><00:12:04.240><c> user</c><00:12:04.639><c> can</c><00:12:04.880><c> directly</c>

00:12:05.269 --> 00:12:05.279 align:start position:0%
and um the business user can directly
 

00:12:05.279 --> 00:12:08.069 align:start position:0%
and um the business user can directly
pose<00:12:05.600><c> the</c><00:12:05.800><c> question</c><00:12:06.600><c> to</c><00:12:06.839><c> the</c><00:12:07.040><c> application</c><00:12:07.880><c> and</c>

00:12:08.069 --> 00:12:08.079 align:start position:0%
pose the question to the application and
 

00:12:08.079 --> 00:12:12.750 align:start position:0%
pose the question to the application and
he<00:12:08.240><c> can</c><00:12:08.480><c> just</c><00:12:08.720><c> get</c><00:12:08.920><c> the</c><00:12:09.079><c> answers</c><00:12:09.519><c> in</c><00:12:09.680><c> a</c><00:12:09.800><c> few</c>

00:12:12.750 --> 00:12:12.760 align:start position:0%
 
 

00:12:12.760 --> 00:12:15.350 align:start position:0%
 
seconds<00:12:13.760><c> so</c><00:12:14.160><c> this</c><00:12:14.279><c> is</c><00:12:14.440><c> an</c><00:12:14.600><c> illustrative</c>

00:12:15.350 --> 00:12:15.360 align:start position:0%
seconds so this is an illustrative
 

00:12:15.360 --> 00:12:18.550 align:start position:0%
seconds so this is an illustrative
example<00:12:16.279><c> uh</c><00:12:16.399><c> to</c><00:12:16.560><c> get</c><00:12:16.720><c> a</c><00:12:16.920><c> look</c><00:12:17.120><c> and</c><00:12:17.320><c> feel</c><00:12:17.880><c> of</c><00:12:18.079><c> the</c>

00:12:18.550 --> 00:12:18.560 align:start position:0%
example uh to get a look and feel of the
 

00:12:18.560 --> 00:12:20.550 align:start position:0%
example uh to get a look and feel of the
application<00:12:19.560><c> now</c><00:12:19.800><c> you</c><00:12:19.959><c> might</c><00:12:20.240><c> have</c><00:12:20.360><c> a</c>

00:12:20.550 --> 00:12:20.560 align:start position:0%
application now you might have a
 

00:12:20.560 --> 00:12:23.230 align:start position:0%
application now you might have a
question<00:12:21.199><c> how</c><00:12:21.480><c> this</c><00:12:21.760><c> application</c><00:12:22.560><c> is</c>

00:12:23.230 --> 00:12:23.240 align:start position:0%
question how this application is
 

00:12:23.240 --> 00:12:26.750 align:start position:0%
question how this application is
different<00:12:23.680><c> from</c><00:12:24.279><c> the</c><00:12:24.440><c> chat</c><00:12:25.160><c> chat</c>

00:12:26.750 --> 00:12:26.760 align:start position:0%
different from the chat chat
 

00:12:26.760 --> 00:12:29.430 align:start position:0%
different from the chat chat
GPT<00:12:27.760><c> so</c><00:12:28.120><c> I</c><00:12:28.199><c> can</c><00:12:28.399><c> State</c><00:12:28.720><c> a</c><00:12:28.839><c> couple</c><00:12:29.040><c> of</c>

00:12:29.430 --> 00:12:29.440 align:start position:0%
GPT so I can State a couple of
 

00:12:29.440 --> 00:12:31.829 align:start position:0%
GPT so I can State a couple of
highlights<00:12:30.440><c> when</c><00:12:30.560><c> you</c><00:12:30.680><c> want</c><00:12:30.839><c> to</c><00:12:31.000><c> do</c><00:12:31.199><c> a</c><00:12:31.360><c> feature</c>

00:12:31.829 --> 00:12:31.839 align:start position:0%
highlights when you want to do a feature
 

00:12:31.839 --> 00:12:36.069 align:start position:0%
highlights when you want to do a feature
evaluation<00:12:32.480><c> of</c><00:12:32.639><c> a</c><00:12:32.800><c> medical</c><00:12:33.399><c> device</c><00:12:34.519><c> um</c><00:12:35.519><c> GPT</c>

00:12:36.069 --> 00:12:36.079 align:start position:0%
evaluation of a medical device um GPT
 

00:12:36.079 --> 00:12:38.430 align:start position:0%
evaluation of a medical device um GPT
doesn't<00:12:36.440><c> give</c><00:12:36.600><c> you</c><00:12:37.079><c> exact</c><00:12:37.560><c> numbers</c><00:12:38.120><c> of</c><00:12:38.279><c> the</c>

00:12:38.430 --> 00:12:38.440 align:start position:0%
doesn't give you exact numbers of the
 

00:12:38.440 --> 00:12:41.910 align:start position:0%
doesn't give you exact numbers of the
feature<00:12:39.000><c> value</c><00:12:40.000><c> for</c><00:12:40.240><c> example</c><00:12:40.800><c> let's</c><00:12:41.079><c> say</c><00:12:41.720><c> the</c>

00:12:41.910 --> 00:12:41.920 align:start position:0%
feature value for example let's say the
 

00:12:41.920 --> 00:12:45.389 align:start position:0%
feature value for example let's say the
analyzer<00:12:42.760><c> device</c><00:12:43.760><c> uh</c><00:12:44.000><c> has</c><00:12:44.199><c> some</c><00:12:44.519><c> aperture</c><00:12:45.240><c> or</c>

00:12:45.389 --> 00:12:45.399 align:start position:0%
analyzer device uh has some aperture or
 

00:12:45.399 --> 00:12:46.870 align:start position:0%
analyzer device uh has some aperture or
a<00:12:45.560><c> rotation</c>

00:12:46.870 --> 00:12:46.880 align:start position:0%
a rotation
 

00:12:46.880 --> 00:12:51.670 align:start position:0%
a rotation
degree<00:12:47.920><c> and</c><00:12:48.920><c> it</c><00:12:49.399><c> uh</c><00:12:49.519><c> GPT</c><00:12:50.079><c> only</c><00:12:50.360><c> tells</c><00:12:50.680><c> you</c><00:12:51.000><c> that</c>

00:12:51.670 --> 00:12:51.680 align:start position:0%
degree and it uh GPT only tells you that
 

00:12:51.680 --> 00:12:53.990 align:start position:0%
degree and it uh GPT only tells you that
the<00:12:51.920><c> analyzer</c><00:12:52.560><c> device</c><00:12:52.920><c> of</c><00:12:53.160><c> your</c><00:12:53.440><c> company</c><00:12:53.800><c> is</c>

00:12:53.990 --> 00:12:54.000 align:start position:0%
the analyzer device of your company is
 

00:12:54.000 --> 00:12:56.269 align:start position:0%
the analyzer device of your company is
better<00:12:54.360><c> than</c><00:12:54.600><c> your</c><00:12:54.880><c> competitor</c><00:12:55.480><c> company</c><00:12:56.120><c> it</c>

00:12:56.269 --> 00:12:56.279 align:start position:0%
better than your competitor company it
 

00:12:56.279 --> 00:12:59.949 align:start position:0%
better than your competitor company it
does<00:12:56.480><c> not</c><00:12:56.720><c> give</c><00:12:56.880><c> out</c><00:12:57.120><c> to</c><00:12:57.279><c> you</c><00:12:57.560><c> the</c><00:12:57.839><c> exact</c><00:12:58.320><c> value</c>

00:12:59.949 --> 00:12:59.959 align:start position:0%
does not give out to you the exact value
 

00:12:59.959 --> 00:13:02.150 align:start position:0%
does not give out to you the exact value
but<00:13:00.240><c> whereas</c><00:13:01.240><c> uh</c><00:13:01.360><c> the</c><00:13:01.480><c> smart</c><00:13:01.839><c> business</c>

00:13:02.150 --> 00:13:02.160 align:start position:0%
but whereas uh the smart business
 

00:13:02.160 --> 00:13:04.790 align:start position:0%
but whereas uh the smart business
analyst<00:13:02.680><c> is</c><00:13:02.800><c> trained</c><00:13:03.480><c> in</c><00:13:03.639><c> such</c><00:13:03.839><c> a</c><00:13:04.000><c> way</c><00:13:04.279><c> that</c><00:13:04.560><c> it</c>

00:13:04.790 --> 00:13:04.800 align:start position:0%
analyst is trained in such a way that it
 

00:13:04.800 --> 00:13:07.310 align:start position:0%
analyst is trained in such a way that it
exactly<00:13:05.279><c> gives</c><00:13:05.560><c> out</c><00:13:06.120><c> the</c><00:13:06.279><c> rotation</c><00:13:06.760><c> degrees</c>

00:13:07.310 --> 00:13:07.320 align:start position:0%
exactly gives out the rotation degrees
 

00:13:07.320 --> 00:13:10.030 align:start position:0%
exactly gives out the rotation degrees
35°<00:13:08.320><c> for</c><00:13:08.560><c> your</c><00:13:08.880><c> company</c><00:13:09.360><c> versus</c><00:13:09.800><c> your</c>

00:13:10.030 --> 00:13:10.040 align:start position:0%
35° for your company versus your
 

00:13:10.040 --> 00:13:16.910 align:start position:0%
35° for your company versus your
competitor<00:13:10.560><c> company</c><00:13:10.920><c> is</c><00:13:11.079><c> just</c><00:13:11.320><c> 33°</c><00:13:12.240><c> and</c><00:13:12.440><c> so</c>

00:13:16.910 --> 00:13:16.920 align:start position:0%
 
 

00:13:16.920 --> 00:13:21.230 align:start position:0%
 
on<00:13:18.360><c> so</c><00:13:19.360><c> if</c><00:13:19.560><c> at</c><00:13:19.720><c> all</c><00:13:19.920><c> we</c><00:13:20.000><c> are</c><00:13:20.160><c> taking</c><00:13:20.399><c> a</c><00:13:20.519><c> look</c><00:13:20.720><c> at</c>

00:13:21.230 --> 00:13:21.240 align:start position:0%
on so if at all we are taking a look at
 

00:13:21.240 --> 00:13:22.310 align:start position:0%
on so if at all we are taking a look at
the<00:13:21.440><c> data</c>

00:13:22.310 --> 00:13:22.320 align:start position:0%
the data
 

00:13:22.320 --> 00:13:24.829 align:start position:0%
the data
flow<00:13:23.320><c> as</c><00:13:23.519><c> the</c><00:13:23.680><c> information</c><00:13:24.279><c> we</c><00:13:24.360><c> are</c><00:13:24.519><c> looking</c>

00:13:24.829 --> 00:13:24.839 align:start position:0%
flow as the information we are looking
 

00:13:24.839 --> 00:13:29.949 align:start position:0%
flow as the information we are looking
for<00:13:25.279><c> is</c><00:13:25.880><c> not</c><00:13:26.880><c> available</c><00:13:27.440><c> in</c><00:13:28.000><c> abundance</c><00:13:29.000><c> uh</c><00:13:29.320><c> uh</c>

00:13:29.949 --> 00:13:29.959 align:start position:0%
for is not available in abundance uh uh
 

00:13:29.959 --> 00:13:34.069 align:start position:0%
for is not available in abundance uh uh
and<00:13:30.279><c> then</c><00:13:30.839><c> this</c><00:13:31.000><c> is</c><00:13:31.199><c> Niche</c><00:13:31.800><c> information</c>

00:13:34.069 --> 00:13:34.079 align:start position:0%
and then this is Niche information
 

00:13:34.079 --> 00:13:37.990 align:start position:0%
and then this is Niche information
so<00:13:35.079><c> uh</c><00:13:35.560><c> there</c><00:13:35.720><c> are</c><00:13:36.000><c> very</c><00:13:36.240><c> few</c><00:13:36.519><c> data</c><00:13:36.800><c> sources</c><00:13:37.800><c> uh</c>

00:13:37.990 --> 00:13:38.000 align:start position:0%
so uh there are very few data sources uh
 

00:13:38.000 --> 00:13:40.590 align:start position:0%
so uh there are very few data sources uh
which<00:13:38.279><c> could</c><00:13:38.639><c> provide</c><00:13:39.160><c> answers</c><00:13:39.639><c> to</c><00:13:40.279><c> the</c>

00:13:40.590 --> 00:13:40.600 align:start position:0%
which could provide answers to the
 

00:13:40.600 --> 00:13:42.350 align:start position:0%
which could provide answers to the
question<00:13:41.120><c> the</c><00:13:41.279><c> kind</c><00:13:41.480><c> of</c><00:13:41.680><c> questions</c><00:13:42.120><c> that</c><00:13:42.240><c> we</c>

00:13:42.350 --> 00:13:42.360 align:start position:0%
question the kind of questions that we
 

00:13:42.360 --> 00:13:44.269 align:start position:0%
question the kind of questions that we
are<00:13:42.480><c> looking</c><00:13:42.760><c> for</c><00:13:43.000><c> in</c><00:13:43.160><c> terms</c><00:13:43.440><c> of</c><00:13:43.639><c> the</c>

00:13:44.269 --> 00:13:44.279 align:start position:0%
are looking for in terms of the
 

00:13:44.279 --> 00:13:47.069 align:start position:0%
are looking for in terms of the
competitor<00:13:45.360><c> analysis</c><00:13:46.360><c> we</c><00:13:46.519><c> don't</c><00:13:46.760><c> want</c><00:13:46.920><c> to</c>

00:13:47.069 --> 00:13:47.079 align:start position:0%
competitor analysis we don't want to
 

00:13:47.079 --> 00:13:49.430 align:start position:0%
competitor analysis we don't want to
miss<00:13:47.320><c> out</c><00:13:47.560><c> on</c><00:13:47.880><c> any</c><00:13:48.160><c> details</c><00:13:48.680><c> which</c><00:13:48.800><c> are</c><00:13:49.040><c> out</c><00:13:49.240><c> on</c>

00:13:49.430 --> 00:13:49.440 align:start position:0%
miss out on any details which are out on
 

00:13:49.440 --> 00:13:53.310 align:start position:0%
miss out on any details which are out on
the<00:13:49.920><c> internet</c><00:13:51.360><c> therefore</c><00:13:52.360><c> we</c><00:13:52.560><c> are</c><00:13:52.839><c> trying</c><00:13:53.199><c> to</c>

00:13:53.310 --> 00:13:53.320 align:start position:0%
the internet therefore we are trying to
 

00:13:53.320 --> 00:13:56.069 align:start position:0%
the internet therefore we are trying to
split<00:13:53.680><c> the</c><00:13:53.800><c> query</c><00:13:54.160><c> into</c><00:13:54.519><c> n</c><00:13:54.839><c> number</c><00:13:55.079><c> of</c><00:13:55.240><c> queries</c>

00:13:56.069 --> 00:13:56.079 align:start position:0%
split the query into n number of queries
 

00:13:56.079 --> 00:13:58.310 align:start position:0%
split the query into n number of queries
to<00:13:56.279><c> retrive</c><00:13:56.880><c> maximum</c><00:13:57.360><c> information</c><00:13:57.920><c> from</c><00:13:58.160><c> the</c>

00:13:58.310 --> 00:13:58.320 align:start position:0%
to retrive maximum information from the
 

00:13:58.320 --> 00:14:01.350 align:start position:0%
to retrive maximum information from the
internet<00:13:59.440><c> this</c><00:13:59.639><c> step</c><00:13:59.920><c> is</c><00:14:00.240><c> called</c><00:14:00.519><c> the</c><00:14:01.120><c> step</c>

00:14:01.350 --> 00:14:01.360 align:start position:0%
internet this step is called the step
 

00:14:01.360 --> 00:14:02.870 align:start position:0%
internet this step is called the step
number<00:14:01.680><c> one</c><00:14:02.040><c> which</c><00:14:02.160><c> is</c><00:14:02.320><c> called</c><00:14:02.560><c> as</c><00:14:02.720><c> the</c>

00:14:02.870 --> 00:14:02.880 align:start position:0%
number one which is called as the
 

00:14:02.880 --> 00:14:04.990 align:start position:0%
number one which is called as the
subquery<00:14:03.480><c> generation</c>

00:14:04.990 --> 00:14:05.000 align:start position:0%
subquery generation
 

00:14:05.000 --> 00:14:08.230 align:start position:0%
subquery generation
agent<00:14:06.000><c> and</c><00:14:06.160><c> the</c><00:14:06.320><c> second</c><00:14:06.639><c> one</c><00:14:06.920><c> is</c><00:14:07.519><c> the</c><00:14:07.720><c> search</c>

00:14:08.230 --> 00:14:08.240 align:start position:0%
agent and the second one is the search
 

00:14:08.240 --> 00:14:11.069 align:start position:0%
agent and the second one is the search
agents<00:14:08.920><c> which</c><00:14:09.320><c> scrape</c><00:14:10.000><c> and</c><00:14:10.199><c> store</c><00:14:10.639><c> the</c><00:14:10.800><c> top</c>

00:14:11.069 --> 00:14:11.079 align:start position:0%
agents which scrape and store the top
 

00:14:11.079 --> 00:14:14.870 align:start position:0%
agents which scrape and store the top
ranked<00:14:11.399><c> URLs</c><00:14:12.000><c> on</c><00:14:12.199><c> the</c><00:14:12.399><c> web</c><00:14:13.199><c> into</c><00:14:13.519><c> a</c>

00:14:14.870 --> 00:14:14.880 align:start position:0%
ranked URLs on the web into a
 

00:14:14.880 --> 00:14:17.389 align:start position:0%
ranked URLs on the web into a
vector<00:14:15.880><c> and</c><00:14:16.040><c> the</c><00:14:16.199><c> third</c><00:14:16.440><c> one</c><00:14:16.720><c> is</c><00:14:16.839><c> the</c><00:14:17.000><c> report</c>

00:14:17.389 --> 00:14:17.399 align:start position:0%
vector and the third one is the report
 

00:14:17.399 --> 00:14:19.790 align:start position:0%
vector and the third one is the report
generation<00:14:17.920><c> agent</c><00:14:18.480><c> which</c><00:14:18.680><c> crafts</c><00:14:19.160><c> the</c><00:14:19.320><c> output</c>

00:14:19.790 --> 00:14:19.800 align:start position:0%
generation agent which crafts the output
 

00:14:19.800 --> 00:14:23.550 align:start position:0%
generation agent which crafts the output
into<00:14:20.120><c> the</c><00:14:20.839><c> display</c><00:14:21.240><c> ready</c>

00:14:23.550 --> 00:14:23.560 align:start position:0%
into the display ready
 

00:14:23.560 --> 00:14:25.910 align:start position:0%
into the display ready
State<00:14:24.560><c> there</c><00:14:24.680><c> are</c><00:14:24.880><c> many</c><00:14:25.160><c> features</c><00:14:25.600><c> that</c><00:14:25.759><c> are</c>

00:14:25.910 --> 00:14:25.920 align:start position:0%
State there are many features that are
 

00:14:25.920 --> 00:14:30.350 align:start position:0%
State there are many features that are
added<00:14:26.880><c> um</c><00:14:27.720><c> to</c><00:14:28.040><c> this</c><00:14:28.800><c> uh</c><00:14:29.160><c> like</c><00:14:29.480><c> visualization</c>

00:14:30.350 --> 00:14:30.360 align:start position:0%
added um to this uh like visualization
 

00:14:30.360 --> 00:14:32.790 align:start position:0%
added um to this uh like visualization
charts<00:14:31.079><c> or</c><00:14:31.440><c> conversational</c><00:14:32.160><c> memory</c><00:14:32.600><c> or</c>

00:14:32.790 --> 00:14:32.800 align:start position:0%
charts or conversational memory or
 

00:14:32.800 --> 00:14:35.990 align:start position:0%
charts or conversational memory or
multilingual<00:14:33.560><c> support</c><00:14:34.000><c> and</c><00:14:34.519><c> more</c><00:14:35.519><c> uh</c><00:14:35.639><c> you</c><00:14:35.800><c> can</c>

00:14:35.990 --> 00:14:36.000 align:start position:0%
multilingual support and more uh you can
 

00:14:36.000 --> 00:14:38.310 align:start position:0%
multilingual support and more uh you can
plug<00:14:36.240><c> in</c><00:14:36.560><c> as</c><00:14:36.720><c> many</c><00:14:37.079><c> additional</c><00:14:37.519><c> data</c><00:14:37.800><c> sources</c>

00:14:38.310 --> 00:14:38.320 align:start position:0%
plug in as many additional data sources
 

00:14:38.320 --> 00:14:41.790 align:start position:0%
plug in as many additional data sources
as<00:14:38.440><c> you</c><00:14:38.639><c> want</c><00:14:39.399><c> to</c><00:14:39.639><c> the</c><00:14:39.759><c> smart</c><00:14:40.160><c> business</c><00:14:40.639><c> an</c>

00:14:41.790 --> 00:14:41.800 align:start position:0%
as you want to the smart business an
 

00:14:41.800 --> 00:14:46.150 align:start position:0%
as you want to the smart business an
analyst<00:14:42.800><c> and</c><00:14:43.040><c> also</c><00:14:44.040><c> uh</c><00:14:44.279><c> as</c><00:14:44.480><c> the</c><00:14:44.600><c> llm</c><00:14:45.440><c> uh</c><00:14:45.600><c> apis</c>

00:14:46.150 --> 00:14:46.160 align:start position:0%
analyst and also uh as the llm uh apis
 

00:14:46.160 --> 00:14:49.590 align:start position:0%
analyst and also uh as the llm uh apis
are<00:14:46.399><c> trained</c><00:14:46.880><c> for</c><00:14:47.240><c> a</c><00:14:47.440><c> while</c><00:14:47.800><c> ago</c><00:14:48.800><c> we</c><00:14:49.160><c> do</c><00:14:49.360><c> not</c>

00:14:49.590 --> 00:14:49.600 align:start position:0%
are trained for a while ago we do not
 

00:14:49.600 --> 00:14:52.509 align:start position:0%
are trained for a while ago we do not
want<00:14:50.399><c> uh</c><00:14:50.600><c> to</c><00:14:50.800><c> miss</c><00:14:51.040><c> out</c><00:14:51.240><c> on</c><00:14:51.480><c> any</c><00:14:51.759><c> latest</c>

00:14:52.509 --> 00:14:52.519 align:start position:0%
want uh to miss out on any latest
 

00:14:52.519 --> 00:14:54.910 align:start position:0%
want uh to miss out on any latest
information<00:14:53.519><c> uh</c><00:14:53.720><c> that</c><00:14:53.839><c> is</c><00:14:54.360><c> uh</c><00:14:54.519><c> on</c><00:14:54.720><c> the</c>

00:14:54.910 --> 00:14:54.920 align:start position:0%
information uh that is uh on the
 

00:14:54.920 --> 00:14:57.710 align:start position:0%
information uh that is uh on the
internet<00:14:55.800><c> after</c><00:14:56.199><c> the</c><00:14:56.360><c> llm</c><00:14:56.800><c> model</c><00:14:57.199><c> has</c><00:14:57.360><c> been</c>

00:14:57.710 --> 00:14:57.720 align:start position:0%
internet after the llm model has been
 

00:14:57.720 --> 00:15:01.150 align:start position:0%
internet after the llm model has been
trained<00:14:58.720><c> so</c><00:14:59.120><c> for</c><00:14:59.440><c> to</c><00:14:59.639><c> suffice</c><00:15:00.279><c> that</c><00:15:00.959><c> uh</c>

00:15:01.150 --> 00:15:01.160 align:start position:0%
trained so for to suffice that uh
 

00:15:01.160 --> 00:15:03.550 align:start position:0%
trained so for to suffice that uh
restriction<00:15:02.160><c> we</c><00:15:02.360><c> have</c><00:15:02.600><c> also</c><00:15:02.880><c> plugged</c><00:15:03.240><c> in</c><00:15:03.399><c> the</c>

00:15:03.550 --> 00:15:03.560 align:start position:0%
restriction we have also plugged in the
 

00:15:03.560 --> 00:15:07.030 align:start position:0%
restriction we have also plugged in the
Google<00:15:03.839><c> Ser</c><00:15:04.320><c> API</c><00:15:04.800><c> wrapper</c><00:15:05.800><c> uh</c><00:15:05.920><c> into</c><00:15:06.240><c> the</c><00:15:06.360><c> llm</c>

00:15:07.030 --> 00:15:07.040 align:start position:0%
Google Ser API wrapper uh into the llm
 

00:15:07.040 --> 00:15:09.230 align:start position:0%
Google Ser API wrapper uh into the llm
so<00:15:07.240><c> that</c><00:15:07.399><c> it</c><00:15:07.519><c> can</c><00:15:07.680><c> fetch</c><00:15:08.040><c> the</c><00:15:08.160><c> most</c><00:15:08.839><c> latest</c>

00:15:09.230 --> 00:15:09.240 align:start position:0%
so that it can fetch the most latest
 

00:15:09.240 --> 00:15:12.269 align:start position:0%
so that it can fetch the most latest
results<00:15:09.639><c> from</c><00:15:09.880><c> the</c>

00:15:12.269 --> 00:15:12.279 align:start position:0%
 
 

00:15:12.279 --> 00:15:15.110 align:start position:0%
 
internet<00:15:13.279><c> and</c><00:15:13.440><c> it</c><00:15:13.560><c> can</c><00:15:13.800><c> also</c><00:15:14.120><c> support</c><00:15:14.880><c> uh</c>

00:15:15.110 --> 00:15:15.120 align:start position:0%
internet and it can also support uh
 

00:15:15.120 --> 00:15:19.470 align:start position:0%
internet and it can also support uh
multil<00:15:15.880><c> language</c><00:15:16.880><c> um</c><00:15:17.320><c> so</c><00:15:18.240><c> the</c><00:15:18.600><c> end</c><00:15:18.880><c> user</c><00:15:19.240><c> can</c>

00:15:19.470 --> 00:15:19.480 align:start position:0%
multil language um so the end user can
 

00:15:19.480 --> 00:15:21.670 align:start position:0%
multil language um so the end user can
ask<00:15:19.880><c> the</c><00:15:20.120><c> question</c><00:15:20.560><c> in</c><00:15:20.800><c> any</c>

00:15:21.670 --> 00:15:21.680 align:start position:0%
ask the question in any
 

00:15:21.680 --> 00:15:24.509 align:start position:0%
ask the question in any
language<00:15:22.680><c> and</c><00:15:22.920><c> there</c><00:15:23.079><c> is</c><00:15:23.680><c> uh</c><00:15:24.320><c> this</c>

00:15:24.509 --> 00:15:24.519 align:start position:0%
language and there is uh this
 

00:15:24.519 --> 00:15:27.509 align:start position:0%
language and there is uh this
conversational<00:15:25.279><c> memory</c><00:15:25.839><c> that</c><00:15:25.959><c> means</c><00:15:26.720><c> um</c><00:15:27.360><c> you</c>

00:15:27.509 --> 00:15:27.519 align:start position:0%
conversational memory that means um you
 

00:15:27.519 --> 00:15:29.749 align:start position:0%
conversational memory that means um you
have<00:15:27.720><c> asked</c><00:15:27.959><c> a</c><00:15:28.120><c> question</c><00:15:28.399><c> number</c><00:15:28.680><c> one</c>

00:15:29.749 --> 00:15:29.759 align:start position:0%
have asked a question number one
 

00:15:29.759 --> 00:15:32.350 align:start position:0%
have asked a question number one
and<00:15:30.199><c> uh</c><00:15:30.360><c> you</c><00:15:30.639><c> have</c><00:15:30.839><c> a</c><00:15:31.000><c> follow-up</c><00:15:31.560><c> question</c><00:15:32.240><c> uh</c>

00:15:32.350 --> 00:15:32.360 align:start position:0%
and uh you have a follow-up question uh
 

00:15:32.360 --> 00:15:35.629 align:start position:0%
and uh you have a follow-up question uh
based<00:15:32.639><c> on</c><00:15:32.800><c> your</c><00:15:33.000><c> previous</c><00:15:33.440><c> question</c><00:15:34.319><c> so</c><00:15:34.920><c> the</c>

00:15:35.629 --> 00:15:35.639 align:start position:0%
based on your previous question so the
 

00:15:35.639 --> 00:15:37.509 align:start position:0%
based on your previous question so the
application<00:15:36.160><c> will</c><00:15:36.360><c> remember</c><00:15:36.800><c> your</c><00:15:37.079><c> previous</c>

00:15:37.509 --> 00:15:37.519 align:start position:0%
application will remember your previous
 

00:15:37.519 --> 00:15:40.110 align:start position:0%
application will remember your previous
question<00:15:38.319><c> uh</c><00:15:38.480><c> like</c><00:15:38.920><c> the</c><00:15:39.040><c> GPT</c>

00:15:40.110 --> 00:15:40.120 align:start position:0%
question uh like the GPT
 

00:15:40.120 --> 00:15:42.829 align:start position:0%
question uh like the GPT
itself<00:15:41.120><c> you</c><00:15:41.279><c> need</c><00:15:41.519><c> not</c><00:15:41.720><c> pose</c><00:15:42.079><c> the</c><00:15:42.519><c> first</c>

00:15:42.829 --> 00:15:42.839 align:start position:0%
itself you need not pose the first
 

00:15:42.839 --> 00:15:45.590 align:start position:0%
itself you need not pose the first
question

00:15:45.590 --> 00:15:45.600 align:start position:0%
 
 

00:15:45.600 --> 00:15:50.110 align:start position:0%
 
again<00:15:46.600><c> so</c><00:15:47.480><c> this</c><00:15:47.639><c> is</c><00:15:48.000><c> again</c><00:15:48.399><c> an</c><00:15:49.120><c> illustrative</c>

00:15:50.110 --> 00:15:50.120 align:start position:0%
again so this is again an illustrative
 

00:15:50.120 --> 00:15:54.189 align:start position:0%
again so this is again an illustrative
um<00:15:50.759><c> uh</c><00:15:50.959><c> visual</c><00:15:51.399><c> of</c><00:15:51.920><c> how</c><00:15:52.199><c> the</c><00:15:52.759><c> tool</c><00:15:53.160><c> looks</c><00:15:53.519><c> like</c>

00:15:54.189 --> 00:15:54.199 align:start position:0%
um uh visual of how the tool looks like
 

00:15:54.199 --> 00:15:56.350 align:start position:0%
um uh visual of how the tool looks like
and<00:15:54.560><c> uh</c><00:15:54.680><c> you</c><00:15:54.800><c> can</c><00:15:55.000><c> also</c><00:15:55.279><c> see</c><00:15:55.560><c> at</c><00:15:55.720><c> the</c><00:15:55.839><c> bottom</c>

00:15:56.350 --> 00:15:56.360 align:start position:0%
and uh you can also see at the bottom
 

00:15:56.360 --> 00:15:59.670 align:start position:0%
and uh you can also see at the bottom
that<00:15:56.639><c> there</c><00:15:56.759><c> are</c><00:15:57.360><c> multiple</c><00:15:57.920><c> websites</c>

00:15:59.670 --> 00:15:59.680 align:start position:0%
that there are multiple websites
 

00:15:59.680 --> 00:16:02.470 align:start position:0%
that there are multiple websites
uh<00:16:00.079><c> from</c><00:16:00.360><c> where</c><00:16:00.639><c> the</c><00:16:00.839><c> information</c><00:16:01.440><c> has</c><00:16:01.839><c> been</c>

00:16:02.470 --> 00:16:02.480 align:start position:0%
uh from where the information has been
 

00:16:02.480 --> 00:16:05.110 align:start position:0%
uh from where the information has been
extracted<00:16:03.480><c> based</c><00:16:03.800><c> on</c><00:16:03.959><c> the</c><00:16:04.120><c> question</c><00:16:04.639><c> query</c>

00:16:05.110 --> 00:16:05.120 align:start position:0%
extracted based on the question query
 

00:16:05.120 --> 00:16:09.069 align:start position:0%
extracted based on the question query
that<00:16:05.240><c> you</c><00:16:05.399><c> have</c><00:16:05.880><c> uh</c><00:16:06.079><c> asked</c><00:16:07.079><c> so</c><00:16:07.959><c> yeah</c><00:16:08.399><c> this</c><00:16:08.560><c> is</c>

00:16:09.069 --> 00:16:09.079 align:start position:0%
that you have uh asked so yeah this is
 

00:16:09.079 --> 00:16:11.350 align:start position:0%
that you have uh asked so yeah this is
uh<00:16:09.440><c> this</c><00:16:09.600><c> is</c><00:16:09.800><c> the</c><00:16:10.319><c> kind</c><00:16:10.480><c> of</c><00:16:10.600><c> an</c><00:16:10.759><c> illustrative</c>

00:16:11.350 --> 00:16:11.360 align:start position:0%
uh this is the kind of an illustrative
 

00:16:11.360 --> 00:16:13.030 align:start position:0%
uh this is the kind of an illustrative
visual<00:16:11.720><c> I</c><00:16:11.839><c> would</c>

00:16:13.030 --> 00:16:13.040 align:start position:0%
visual I would
 

00:16:13.040 --> 00:16:17.360 align:start position:0%
visual I would
say<00:16:14.040><c> yeah</c><00:16:14.480><c> thank</c><00:16:14.680><c> you</c>

