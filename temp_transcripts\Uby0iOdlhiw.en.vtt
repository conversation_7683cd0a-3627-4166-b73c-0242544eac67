WEBVTT
Kind: captions
Language: en

00:00:00.319 --> 00:00:03.670 align:start position:0%
 
this<00:00:01.319><c> is</c><00:00:01.480><c> autog</c><00:00:01.920><c> Gro</c><00:00:02.720><c> but</c><00:00:02.879><c> you</c><00:00:03.040><c> already</c><00:00:03.280><c> knew</c>

00:00:03.670 --> 00:00:03.680 align:start position:0%
this is autog Gro but you already knew
 

00:00:03.680 --> 00:00:07.270 align:start position:0%
this is autog Gro but you already knew
that<00:00:04.680><c> this</c><00:00:05.680><c> is</c><00:00:05.839><c> the</c><00:00:05.960><c> new</c><00:00:06.160><c> autog</c><00:00:06.520><c> gr</c><00:00:06.759><c> Discord</c>

00:00:07.270 --> 00:00:07.280 align:start position:0%
that this is the new autog gr Discord
 

00:00:07.280 --> 00:00:12.190 align:start position:0%
that this is the new autog gr Discord
server<00:00:08.280><c> and</c><00:00:08.519><c> that</c><00:00:09.360><c> as</c><00:00:09.519><c> the</c><00:00:09.679><c> kids</c><00:00:09.920><c> say</c><00:00:10.599><c> is</c><00:00:10.840><c> just</c>

00:00:12.190 --> 00:00:12.200 align:start position:0%
server and that as the kids say is just
 

00:00:12.200 --> 00:00:15.910 align:start position:0%
server and that as the kids say is just
[Music]

00:00:15.910 --> 00:00:15.920 align:start position:0%
[Music]
 

00:00:15.920 --> 00:00:19.470 align:start position:0%
[Music]
groovy<00:00:17.359><c> autut</c>

00:00:19.470 --> 00:00:19.480 align:start position:0%
groovy autut
 

00:00:19.480 --> 00:00:23.029 align:start position:0%
groovy autut
autut<00:00:20.480><c> Auto</c>

00:00:23.029 --> 00:00:23.039 align:start position:0%
autut Auto
 

00:00:23.039 --> 00:00:26.550 align:start position:0%
autut Auto
autut<00:00:24.480><c> aut</c>

00:00:26.550 --> 00:00:26.560 align:start position:0%
autut aut
 

00:00:26.560 --> 00:00:28.269 align:start position:0%
autut aut
aut

00:00:28.269 --> 00:00:28.279 align:start position:0%
aut
 

00:00:28.279 --> 00:00:31.070 align:start position:0%
aut
a<00:00:29.279><c> aut</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
a aut
 

00:00:31.080 --> 00:00:33.760 align:start position:0%
a aut
I

00:00:33.760 --> 00:00:33.770 align:start position:0%
I
 

00:00:33.770 --> 00:00:37.990 align:start position:0%
I
[Music]

