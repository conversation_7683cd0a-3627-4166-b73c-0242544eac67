WEBVTT
Kind: captions
Language: en

00:00:10.080 --> 00:00:12.150 align:start position:0%
 
hello<00:00:10.440><c> everybody</c><00:00:11.080><c> and</c><00:00:11.320><c> welcome</c><00:00:11.759><c> to</c><00:00:11.960><c> the</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
hello everybody and welcome to the
 

00:00:12.160 --> 00:00:14.669 align:start position:0%
hello everybody and welcome to the
overview<00:00:12.799><c> of</c><00:00:13.040><c> the</c><00:00:13.240><c> production</c><00:00:13.759><c> workflow</c><00:00:14.440><c> for</c>

00:00:14.669 --> 00:00:14.679 align:start position:0%
overview of the production workflow for
 

00:00:14.679 --> 00:00:18.830 align:start position:0%
overview of the production workflow for
the<00:00:14.879><c> paneuropean</c><00:00:15.400><c> climate</c><00:00:16.039><c> database</c><00:00:16.720><c> version</c>

00:00:18.830 --> 00:00:18.840 align:start position:0%
the paneuropean climate database version
 

00:00:18.840 --> 00:00:21.630 align:start position:0%
the paneuropean climate database version
4.2<00:00:19.840><c> in</c><00:00:20.080><c> this</c><00:00:20.320><c> presentation</c><00:00:21.119><c> we</c><00:00:21.199><c> will</c><00:00:21.439><c> go</c>

00:00:21.630 --> 00:00:21.640 align:start position:0%
4.2 in this presentation we will go
 

00:00:21.640 --> 00:00:24.070 align:start position:0%
4.2 in this presentation we will go
through<00:00:22.039><c> an</c><00:00:22.240><c> overview</c><00:00:22.760><c> of</c><00:00:22.920><c> the</c><00:00:23.080><c> citras</c><00:00:23.640><c> energy</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
through an overview of the citras energy
 

00:00:24.080 --> 00:00:26.990 align:start position:0%
through an overview of the citras energy
Lot<00:00:24.320><c> 2</c><00:00:24.760><c> contract</c><00:00:25.760><c> the</c><00:00:26.000><c> production</c><00:00:26.480><c> workflow</c>

00:00:26.990 --> 00:00:27.000 align:start position:0%
Lot 2 contract the production workflow
 

00:00:27.000 --> 00:00:30.950 align:start position:0%
Lot 2 contract the production workflow
for<00:00:27.240><c> the</c><00:00:27.439><c> PCD</c><00:00:28.240><c> version</c><00:00:28.960><c> 4.2</c><00:00:30.039><c> the</c><00:00:30.240><c> spatial</c><00:00:30.679><c> and</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
for the PCD version 4.2 the spatial and
 

00:00:30.960 --> 00:00:33.389 align:start position:0%
for the PCD version 4.2 the spatial and
temporal<00:00:31.439><c> interpolation</c><00:00:32.239><c> procedures</c><00:00:33.239><c> and</c>

00:00:33.389 --> 00:00:33.399 align:start position:0%
temporal interpolation procedures and
 

00:00:33.399 --> 00:00:35.670 align:start position:0%
temporal interpolation procedures and
the<00:00:33.520><c> wind</c><00:00:33.840><c> profile</c><00:00:34.239><c> power</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
the wind profile power
 

00:00:35.680 --> 00:00:39.110 align:start position:0%
the wind profile power
LW<00:00:36.680><c> the</c><00:00:36.879><c> main</c><00:00:37.200><c> objective</c><00:00:37.719><c> of</c><00:00:37.960><c> the</c><00:00:38.120><c> c3s</c><00:00:38.719><c> energy</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
LW the main objective of the c3s energy
 

00:00:39.120 --> 00:00:41.910 align:start position:0%
LW the main objective of the c3s energy
Lot<00:00:39.480><c> 2</c><00:00:40.160><c> is</c><00:00:40.320><c> to</c><00:00:40.520><c> support</c><00:00:40.960><c> enoe</c><00:00:41.600><c> in</c><00:00:41.719><c> the</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
Lot 2 is to support enoe in the
 

00:00:41.920 --> 00:00:44.549 align:start position:0%
Lot 2 is to support enoe in the
preparation<00:00:42.760><c> of</c><00:00:43.079><c> the</c><00:00:43.360><c> paneuropean</c><00:00:43.960><c> climate</c>

00:00:44.549 --> 00:00:44.559 align:start position:0%
preparation of the paneuropean climate
 

00:00:44.559 --> 00:00:47.150 align:start position:0%
preparation of the paneuropean climate
database<00:00:45.440><c> also</c><00:00:45.680><c> known</c><00:00:46.039><c> as</c><00:00:46.239><c> the</c>

00:00:47.150 --> 00:00:47.160 align:start position:0%
database also known as the
 

00:00:47.160 --> 00:00:50.830 align:start position:0%
database also known as the
PCD<00:00:48.160><c> to</c><00:00:48.440><c> achieve</c><00:00:48.960><c> this</c><00:00:49.559><c> the</c><00:00:49.800><c> contact</c><00:00:50.280><c> focuses</c>

00:00:50.830 --> 00:00:50.840 align:start position:0%
PCD to achieve this the contact focuses
 

00:00:50.840 --> 00:00:54.110 align:start position:0%
PCD to achieve this the contact focuses
on<00:00:51.120><c> key</c><00:00:51.320><c> climate</c><00:00:51.760><c> variables</c><00:00:52.399><c> including</c><00:00:53.320><c> 2</c><00:00:53.559><c> m</c>

00:00:54.110 --> 00:00:54.120 align:start position:0%
on key climate variables including 2 m
 

00:00:54.120 --> 00:00:58.869 align:start position:0%
on key climate variables including 2 m
temperature<00:00:55.120><c> wind</c><00:00:55.440><c> speed</c><00:00:55.840><c> at</c><00:00:56.120><c> 10</c><00:00:56.440><c> m</c><00:00:57.000><c> and</c><00:00:57.480><c> 100</c><00:00:57.879><c> m</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
temperature wind speed at 10 m and 100 m
 

00:00:58.879 --> 00:01:00.549 align:start position:0%
temperature wind speed at 10 m and 100 m
total<00:00:59.280><c> precipitation</c>

00:01:00.549 --> 00:01:00.559 align:start position:0%
total precipitation
 

00:01:00.559 --> 00:01:02.869 align:start position:0%
total precipitation
and<00:01:00.760><c> surface</c><00:01:01.239><c> solar</c><00:01:01.640><c> radiation</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
and surface solar radiation
 

00:01:02.879 --> 00:01:05.429 align:start position:0%
and surface solar radiation
downwards<00:01:03.879><c> these</c><00:01:04.119><c> climate</c><00:01:04.559><c> variables</c><00:01:05.119><c> are</c>

00:01:05.429 --> 00:01:05.439 align:start position:0%
downwards these climate variables are
 

00:01:05.439 --> 00:01:08.070 align:start position:0%
downwards these climate variables are
then<00:01:05.760><c> converted</c><00:01:06.560><c> into</c><00:01:06.960><c> energy</c><00:01:07.400><c> relevant</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
then converted into energy relevant
 

00:01:08.080 --> 00:01:11.270 align:start position:0%
then converted into energy relevant
indicators<00:01:09.080><c> which</c><00:01:09.320><c> include</c><00:01:09.920><c> wind</c><00:01:10.320><c> power</c><00:01:10.920><c> both</c>

00:01:11.270 --> 00:01:11.280 align:start position:0%
indicators which include wind power both
 

00:01:11.280 --> 00:01:14.429 align:start position:0%
indicators which include wind power both
onshore<00:01:11.920><c> and</c><00:01:12.200><c> offshore</c><00:01:13.200><c> solar</c><00:01:13.600><c> photovoltaic</c>

00:01:14.429 --> 00:01:14.439 align:start position:0%
onshore and offshore solar photovoltaic
 

00:01:14.439 --> 00:01:18.149 align:start position:0%
onshore and offshore solar photovoltaic
power<00:01:15.360><c> concentrated</c><00:01:16.159><c> solar</c><00:01:16.600><c> power</c><00:01:17.439><c> and</c><00:01:17.680><c> Hydro</c>

00:01:18.149 --> 00:01:18.159 align:start position:0%
power concentrated solar power and Hydro
 

00:01:18.159 --> 00:01:19.789 align:start position:0%
power concentrated solar power and Hydro
power<00:01:18.439><c> generation</c><00:01:19.040><c> and</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
power generation and
 

00:01:19.799 --> 00:01:22.310 align:start position:0%
power generation and
inflow<00:01:20.799><c> the</c><00:01:21.040><c> historical</c><00:01:21.600><c> climate</c><00:01:22.040><c> data</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
inflow the historical climate data
 

00:01:22.320 --> 00:01:26.429 align:start position:0%
inflow the historical climate data
stream<00:01:22.960><c> extends</c><00:01:23.520><c> from</c><00:01:24.119><c> 1950</c><00:01:25.119><c> to</c><00:01:25.320><c> the</c><00:01:25.520><c> present</c>

00:01:26.429 --> 00:01:26.439 align:start position:0%
stream extends from 1950 to the present
 

00:01:26.439 --> 00:01:28.590 align:start position:0%
stream extends from 1950 to the present
while<00:01:26.640><c> the</c><00:01:26.840><c> projection</c><00:01:27.280><c> stream</c><00:01:27.799><c> goes</c><00:01:28.119><c> from</c>

00:01:28.590 --> 00:01:28.600 align:start position:0%
while the projection stream goes from
 

00:01:28.600 --> 00:01:32.510 align:start position:0%
while the projection stream goes from
2015<00:01:29.600><c> up</c><00:01:29.759><c> to</c><00:01:29.920><c> to</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
 
 

00:01:32.520 --> 00:01:35.710 align:start position:0%
 
2,00<00:01:33.520><c> this</c><00:01:33.759><c> slide</c><00:01:34.280><c> illustrates</c><00:01:35.000><c> the</c><00:01:35.200><c> workflow</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
2,00 this slide illustrates the workflow
 

00:01:35.720 --> 00:01:39.749 align:start position:0%
2,00 this slide illustrates the workflow
for<00:01:36.000><c> producing</c><00:01:36.560><c> PD</c><00:01:37.240><c> version</c><00:01:38.000><c> 4.2</c><00:01:39.000><c> showing</c><00:01:39.520><c> the</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
for producing PD version 4.2 showing the
 

00:01:39.759 --> 00:01:42.310 align:start position:0%
for producing PD version 4.2 showing the
processing<00:01:40.280><c> steps</c><00:01:40.680><c> for</c><00:01:41.000><c> both</c><00:01:41.360><c> historical</c><00:01:42.079><c> and</c>

00:01:42.310 --> 00:01:42.320 align:start position:0%
processing steps for both historical and
 

00:01:42.320 --> 00:01:45.190 align:start position:0%
processing steps for both historical and
projection<00:01:43.240><c> streams</c><00:01:44.240><c> for</c><00:01:44.520><c> the</c><00:01:44.680><c> historical</c>

00:01:45.190 --> 00:01:45.200 align:start position:0%
projection streams for the historical
 

00:01:45.200 --> 00:01:48.190 align:start position:0%
projection streams for the historical
stream<00:01:45.880><c> we</c><00:01:46.079><c> start</c><00:01:46.439><c> with</c><00:01:46.680><c> era</c><00:01:47.079><c> five</c><00:01:47.399><c> reanalysis</c>

00:01:48.190 --> 00:01:48.200 align:start position:0%
stream we start with era five reanalysis
 

00:01:48.200 --> 00:01:51.389 align:start position:0%
stream we start with era five reanalysis
data<00:01:49.119><c> where</c><00:01:49.399><c> preprocessing</c><00:01:50.399><c> includes</c>

00:01:51.389 --> 00:01:51.399 align:start position:0%
data where preprocessing includes
 

00:01:51.399 --> 00:01:53.870 align:start position:0%
data where preprocessing includes
calculating<00:01:52.000><c> wind</c><00:01:52.280><c> speed</c><00:01:52.600><c> from</c><00:01:52.880><c> unv</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
calculating wind speed from unv
 

00:01:53.880 --> 00:01:57.830 align:start position:0%
calculating wind speed from unv
components<00:01:54.880><c> fixing</c><00:01:55.560><c> known</c><00:01:56.079><c> ER</c><00:01:56.439><c> 5</c><00:01:56.759><c> issues</c><00:01:57.600><c> and</c>

00:01:57.830 --> 00:01:57.840 align:start position:0%
components fixing known ER 5 issues and
 

00:01:57.840 --> 00:02:00.550 align:start position:0%
components fixing known ER 5 issues and
converting<00:01:58.479><c> ghi</c><00:01:59.159><c> units</c>

00:02:00.550 --> 00:02:00.560 align:start position:0%
converting ghi units
 

00:02:00.560 --> 00:02:03.069 align:start position:0%
converting ghi units
next<00:02:00.920><c> we</c><00:02:01.119><c> apply</c><00:02:01.520><c> bias</c><00:02:01.920><c> adjustment</c><00:02:02.479><c> to</c><00:02:02.719><c> wind</c>

00:02:03.069 --> 00:02:03.079 align:start position:0%
next we apply bias adjustment to wind
 

00:02:03.079 --> 00:02:07.469 align:start position:0%
next we apply bias adjustment to wind
speed<00:02:03.439><c> at</c><00:02:03.719><c> 10</c><00:02:04.039><c> m</c><00:02:04.600><c> and</c><00:02:05.159><c> 100</c><00:02:06.000><c> m</c><00:02:07.000><c> for</c><00:02:07.240><c> the</c>

00:02:07.469 --> 00:02:07.479 align:start position:0%
speed at 10 m and 100 m for the
 

00:02:07.479 --> 00:02:10.589 align:start position:0%
speed at 10 m and 100 m for the
projection<00:02:08.039><c> stream</c><00:02:08.640><c> we</c><00:02:08.800><c> begin</c><00:02:09.239><c> with</c><00:02:09.520><c> SIM</c><00:02:10.000><c> 6</c>

00:02:10.589 --> 00:02:10.599 align:start position:0%
projection stream we begin with SIM 6
 

00:02:10.599 --> 00:02:14.110 align:start position:0%
projection stream we begin with SIM 6
model<00:02:11.599><c> where</c><00:02:12.000><c> the</c><00:02:12.239><c> pre-processing</c><00:02:13.160><c> includes</c>

00:02:14.110 --> 00:02:14.120 align:start position:0%
model where the pre-processing includes
 

00:02:14.120 --> 00:02:16.869 align:start position:0%
model where the pre-processing includes
spatial<00:02:14.760><c> and</c><00:02:15.000><c> temporal</c><00:02:15.519><c> interpolation</c><00:02:16.519><c> since</c>

00:02:16.869 --> 00:02:16.879 align:start position:0%
spatial and temporal interpolation since
 

00:02:16.879 --> 00:02:19.990 align:start position:0%
spatial and temporal interpolation since
the<00:02:17.120><c> C6</c><00:02:17.920><c> models</c><00:02:18.760><c> do</c><00:02:18.959><c> not</c><00:02:19.280><c> have</c><00:02:19.519><c> the</c><00:02:19.720><c> same</c>

00:02:19.990 --> 00:02:20.000 align:start position:0%
the C6 models do not have the same
 

00:02:20.000 --> 00:02:23.350 align:start position:0%
the C6 models do not have the same
resolution<00:02:20.640><c> as</c><00:02:20.879><c> the</c><00:02:21.080><c> AA</c><00:02:21.480><c> 5</c><00:02:22.200><c> reanalysis</c><00:02:23.200><c> and</c>

00:02:23.350 --> 00:02:23.360 align:start position:0%
resolution as the AA 5 reanalysis and
 

00:02:23.360 --> 00:02:25.270 align:start position:0%
resolution as the AA 5 reanalysis and
then<00:02:23.519><c> also</c><00:02:23.760><c> in</c><00:02:23.959><c> this</c><00:02:24.160><c> case</c><00:02:24.480><c> Computing</c><00:02:24.959><c> wind</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
then also in this case Computing wind
 

00:02:25.280 --> 00:02:27.589 align:start position:0%
then also in this case Computing wind
speed<00:02:25.680><c> from</c><00:02:26.040><c> u</c><00:02:26.360><c> and</c><00:02:26.560><c> v</c>

00:02:27.589 --> 00:02:27.599 align:start position:0%
speed from u and v
 

00:02:27.599 --> 00:02:30.150 align:start position:0%
speed from u and v
components<00:02:28.599><c> bias</c><00:02:29.000><c> adjustment</c><00:02:29.599><c> is</c><00:02:29.959><c> then</c>

00:02:30.150 --> 00:02:30.160 align:start position:0%
components bias adjustment is then
 

00:02:30.160 --> 00:02:32.710 align:start position:0%
components bias adjustment is then
applied<00:02:30.599><c> to</c><00:02:30.840><c> all</c><00:02:31.080><c> the</c><00:02:31.319><c> variables</c><00:02:32.280><c> using</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
applied to all the variables using
 

00:02:32.720 --> 00:02:35.949 align:start position:0%
applied to all the variables using
different<00:02:33.720><c> methods</c><00:02:34.720><c> from</c><00:02:35.040><c> this</c><00:02:35.319><c> wind</c><00:02:35.640><c> speed</c>

00:02:35.949 --> 00:02:35.959 align:start position:0%
different methods from this wind speed
 

00:02:35.959 --> 00:02:39.070 align:start position:0%
different methods from this wind speed
at<00:02:36.440><c> 100</c><00:02:36.800><c> m</c><00:02:37.280><c> is</c><00:02:37.440><c> derived</c><00:02:38.200><c> using</c><00:02:38.560><c> the</c><00:02:38.760><c> wind</c>

00:02:39.070 --> 00:02:39.080 align:start position:0%
at 100 m is derived using the wind
 

00:02:39.080 --> 00:02:40.509 align:start position:0%
at 100 m is derived using the wind
profile<00:02:39.560><c> power</c>

00:02:40.509 --> 00:02:40.519 align:start position:0%
profile power
 

00:02:40.519 --> 00:02:43.390 align:start position:0%
profile power
low<00:02:41.519><c> at</c><00:02:41.720><c> this</c><00:02:41.920><c> point</c><00:02:42.319><c> the</c><00:02:42.480><c> graded</c><00:02:43.000><c> climate</c>

00:02:43.390 --> 00:02:43.400 align:start position:0%
low at this point the graded climate
 

00:02:43.400 --> 00:02:46.630 align:start position:0%
low at this point the graded climate
indicators<00:02:44.080><c> are</c><00:02:44.680><c> ready</c><00:02:45.680><c> then</c><00:02:45.959><c> the</c><00:02:46.080><c> workflow</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
indicators are ready then the workflow
 

00:02:46.640 --> 00:02:48.710 align:start position:0%
indicators are ready then the workflow
proceeds<00:02:47.159><c> with</c><00:02:47.280><c> the</c><00:02:47.480><c> computation</c><00:02:48.319><c> of</c><00:02:48.480><c> the</c>

00:02:48.710 --> 00:02:48.720 align:start position:0%
proceeds with the computation of the
 

00:02:48.720 --> 00:02:50.710 align:start position:0%
proceeds with the computation of the
population<00:02:49.280><c> weighted</c>

00:02:50.710 --> 00:02:50.720 align:start position:0%
population weighted
 

00:02:50.720 --> 00:02:53.630 align:start position:0%
population weighted
temperature<00:02:51.720><c> to</c><00:02:52.040><c> obtain</c><00:02:52.720><c> the</c><00:02:52.959><c> aggregated</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
temperature to obtain the aggregated
 

00:02:53.640 --> 00:02:56.509 align:start position:0%
temperature to obtain the aggregated
climate<00:02:54.120><c> indicators</c><00:02:54.959><c> a</c><00:02:55.200><c> special</c><00:02:55.920><c> aggregation</c>

00:02:56.509 --> 00:02:56.519 align:start position:0%
climate indicators a special aggregation
 

00:02:56.519 --> 00:02:59.270 align:start position:0%
climate indicators a special aggregation
procedure<00:02:57.080><c> is</c><00:02:57.480><c> performed</c><00:02:58.480><c> which</c><00:02:58.640><c> allows</c><00:02:59.000><c> to</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
procedure is performed which allows to
 

00:02:59.280 --> 00:03:02.110 align:start position:0%
procedure is performed which allows to
compute<00:03:00.040><c> average</c><00:03:00.560><c> values</c><00:03:00.959><c> at</c><00:03:01.280><c> country</c><00:03:01.840><c> and</c>

00:03:02.110 --> 00:03:02.120 align:start position:0%
compute average values at country and
 

00:03:02.120 --> 00:03:03.869 align:start position:0%
compute average values at country and
subcount

00:03:03.869 --> 00:03:03.879 align:start position:0%
subcount
 

00:03:03.879 --> 00:03:07.190 align:start position:0%
subcount
level<00:03:04.879><c> to</c><00:03:05.319><c> compute</c><00:03:05.720><c> the</c><00:03:05.879><c> energy</c><00:03:06.400><c> indicators</c>

00:03:07.190 --> 00:03:07.200 align:start position:0%
level to compute the energy indicators
 

00:03:07.200 --> 00:03:09.869 align:start position:0%
level to compute the energy indicators
some<00:03:07.560><c> installed</c><00:03:08.280><c> capacity</c><00:03:08.959><c> and</c><00:03:09.239><c> generation</c>

00:03:09.869 --> 00:03:09.879 align:start position:0%
some installed capacity and generation
 

00:03:09.879 --> 00:03:12.869 align:start position:0%
some installed capacity and generation
data<00:03:10.239><c> are</c><00:03:10.519><c> needed</c><00:03:11.519><c> to</c><00:03:11.720><c> train</c><00:03:12.200><c> and</c><00:03:12.400><c> validate</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
data are needed to train and validate
 

00:03:12.879 --> 00:03:16.030 align:start position:0%
data are needed to train and validate
the<00:03:13.040><c> conversion</c><00:03:14.080><c> models</c><00:03:15.080><c> there</c><00:03:15.200><c> are</c><00:03:15.440><c> four</c>

00:03:16.030 --> 00:03:16.040 align:start position:0%
the conversion models there are four
 

00:03:16.040 --> 00:03:19.670 align:start position:0%
the conversion models there are four
main<00:03:16.599><c> energy</c><00:03:17.080><c> conversion</c><00:03:17.840><c> models</c><00:03:18.840><c> wind</c><00:03:19.239><c> power</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
main energy conversion models wind power
 

00:03:19.680 --> 00:03:23.030 align:start position:0%
main energy conversion models wind power
solar<00:03:20.120><c> photovoltaic</c><00:03:21.239><c> power</c><00:03:22.239><c> concentrated</c>

00:03:23.030 --> 00:03:23.040 align:start position:0%
solar photovoltaic power concentrated
 

00:03:23.040 --> 00:03:25.270 align:start position:0%
solar photovoltaic power concentrated
solar<00:03:23.480><c> power</c><00:03:24.000><c> and</c><00:03:24.239><c> Hydro</c>

00:03:25.270 --> 00:03:25.280 align:start position:0%
solar power and Hydro
 

00:03:25.280 --> 00:03:28.509 align:start position:0%
solar power and Hydro
power<00:03:26.280><c> exclusion</c><00:03:26.879><c> layers</c><00:03:27.400><c> are</c><00:03:27.680><c> then</c><00:03:27.920><c> applied</c>

00:03:28.509 --> 00:03:28.519 align:start position:0%
power exclusion layers are then applied
 

00:03:28.519 --> 00:03:31.270 align:start position:0%
power exclusion layers are then applied
if<00:03:28.760><c> needed</c><00:03:29.799><c> and</c><00:03:30.040><c> special</c><00:03:30.480><c> aggregation</c><00:03:31.080><c> is</c>

00:03:31.270 --> 00:03:31.280 align:start position:0%
if needed and special aggregation is
 

00:03:31.280 --> 00:03:33.789 align:start position:0%
if needed and special aggregation is
performed<00:03:31.959><c> again</c><00:03:32.400><c> to</c><00:03:32.640><c> produce</c><00:03:33.120><c> aggregated</c>

00:03:33.789 --> 00:03:33.799 align:start position:0%
performed again to produce aggregated
 

00:03:33.799 --> 00:03:36.309 align:start position:0%
performed again to produce aggregated
energy<00:03:34.239><c> indicators</c><00:03:35.159><c> at</c><00:03:35.439><c> country</c><00:03:36.040><c> and</c>

00:03:36.309 --> 00:03:36.319 align:start position:0%
energy indicators at country and
 

00:03:36.319 --> 00:03:38.670 align:start position:0%
energy indicators at country and
subcount

00:03:38.670 --> 00:03:38.680 align:start position:0%
subcount
 

00:03:38.680 --> 00:03:41.509 align:start position:0%
subcount
level<00:03:39.680><c> we</c><00:03:39.799><c> are</c><00:03:40.040><c> going</c><00:03:40.360><c> now</c><00:03:40.599><c> to</c><00:03:40.879><c> expand</c><00:03:41.360><c> a</c>

00:03:41.509 --> 00:03:41.519 align:start position:0%
level we are going now to expand a
 

00:03:41.519 --> 00:03:43.710 align:start position:0%
level we are going now to expand a
little<00:03:41.799><c> more</c><00:03:42.120><c> on</c><00:03:42.239><c> the</c><00:03:42.480><c> spatial</c><00:03:43.080><c> and</c><00:03:43.280><c> temporal</c>

00:03:43.710 --> 00:03:43.720 align:start position:0%
little more on the spatial and temporal
 

00:03:43.720 --> 00:03:46.070 align:start position:0%
little more on the spatial and temporal
interpolation<00:03:44.439><c> procedures</c><00:03:45.319><c> and</c><00:03:45.480><c> on</c><00:03:45.640><c> the</c><00:03:45.840><c> Wind</c>

00:03:46.070 --> 00:03:46.080 align:start position:0%
interpolation procedures and on the Wind
 

00:03:46.080 --> 00:03:48.229 align:start position:0%
interpolation procedures and on the Wind
profile<00:03:46.480><c> power</c><00:03:46.760><c> law</c><00:03:47.120><c> that</c><00:03:47.239><c> are</c><00:03:47.480><c> used</c><00:03:47.959><c> in</c><00:03:48.080><c> the</c>

00:03:48.229 --> 00:03:48.239 align:start position:0%
profile power law that are used in the
 

00:03:48.239 --> 00:03:49.949 align:start position:0%
profile power law that are used in the
projection

00:03:49.949 --> 00:03:49.959 align:start position:0%
projection
 

00:03:49.959 --> 00:03:53.429 align:start position:0%
projection
stream<00:03:50.959><c> the</c><00:03:51.120><c> CIP</c><00:03:51.599><c> 6</c><00:03:51.920><c> models</c><00:03:52.480><c> have</c><00:03:52.680><c> a</c><00:03:52.879><c> core</c>

00:03:53.429 --> 00:03:53.439 align:start position:0%
stream the CIP 6 models have a core
 

00:03:53.439 --> 00:03:56.630 align:start position:0%
stream the CIP 6 models have a core
special<00:03:53.920><c> resolution</c><00:03:54.480><c> of</c><00:03:54.720><c> about</c><00:03:55.360><c> 100</c><00:03:55.720><c> kilm</c><00:03:56.480><c> and</c>

00:03:56.630 --> 00:03:56.640 align:start position:0%
special resolution of about 100 kilm and
 

00:03:56.640 --> 00:03:59.910 align:start position:0%
special resolution of about 100 kilm and
a<00:03:56.799><c> temporal</c><00:03:57.239><c> resolution</c><00:03:57.799><c> of</c><00:03:58.000><c> 3</c><00:03:58.319><c> hours</c><00:03:59.319><c> to</c><00:03:59.480><c> make</c>

00:03:59.910 --> 00:03:59.920 align:start position:0%
a temporal resolution of 3 hours to make
 

00:03:59.920 --> 00:04:02.509 align:start position:0%
a temporal resolution of 3 hours to make
the<00:04:00.079><c> data</c><00:04:00.439><c> compatible</c><00:04:01.079><c> with</c><00:04:01.319><c> era</c><00:04:01.720><c> five</c><00:04:02.280><c> and</c>

00:04:02.509 --> 00:04:02.519 align:start position:0%
the data compatible with era five and
 

00:04:02.519 --> 00:04:05.949 align:start position:0%
the data compatible with era five and
suitable<00:04:03.040><c> for</c><00:04:03.360><c> our</c><00:04:04.040><c> applications</c><00:04:05.040><c> we</c><00:04:05.319><c> perform</c>

00:04:05.949 --> 00:04:05.959 align:start position:0%
suitable for our applications we perform
 

00:04:05.959 --> 00:04:08.509 align:start position:0%
suitable for our applications we perform
both<00:04:06.360><c> spatial</c><00:04:07.040><c> and</c><00:04:07.280><c> temporal</c>

00:04:08.509 --> 00:04:08.519 align:start position:0%
both spatial and temporal
 

00:04:08.519 --> 00:04:11.270 align:start position:0%
both spatial and temporal
interpolation<00:04:09.519><c> for</c><00:04:09.879><c> spatial</c><00:04:10.439><c> interpolation</c>

00:04:11.270 --> 00:04:11.280 align:start position:0%
interpolation for spatial interpolation
 

00:04:11.280 --> 00:04:14.350 align:start position:0%
interpolation for spatial interpolation
we<00:04:11.439><c> use</c><00:04:11.760><c> bilinear</c><00:04:12.480><c> interpolation</c><00:04:13.360><c> to</c><00:04:13.680><c> regrid</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
we use bilinear interpolation to regrid
 

00:04:14.360 --> 00:04:17.349 align:start position:0%
we use bilinear interpolation to regrid
the<00:04:14.599><c> data</c><00:04:15.120><c> to</c><00:04:15.319><c> a</c><00:04:15.439><c> final</c><00:04:15.840><c> resolution</c><00:04:16.359><c> of</c>

00:04:17.349 --> 00:04:17.359 align:start position:0%
the data to a final resolution of
 

00:04:17.359 --> 00:04:21.749 align:start position:0%
the data to a final resolution of
0.25<00:04:18.600><c> de</c><00:04:19.600><c> as</c><00:04:19.759><c> shown</c><00:04:20.280><c> in</c><00:04:20.440><c> the</c><00:04:20.720><c> example</c><00:04:21.120><c> on</c><00:04:21.280><c> the</c>

00:04:21.749 --> 00:04:21.759 align:start position:0%
0.25 de as shown in the example on the
 

00:04:21.759 --> 00:04:24.670 align:start position:0%
0.25 de as shown in the example on the
left<00:04:22.759><c> for</c><00:04:23.080><c> temporal</c><00:04:23.560><c> interpolation</c><00:04:24.440><c> we</c>

00:04:24.670 --> 00:04:24.680 align:start position:0%
left for temporal interpolation we
 

00:04:24.680 --> 00:04:28.070 align:start position:0%
left for temporal interpolation we
refine<00:04:25.199><c> the</c><00:04:25.400><c> data</c><00:04:25.720><c> to</c><00:04:26.000><c> 1</c><00:04:26.520><c> hour</c><00:04:27.520><c> and</c><00:04:27.720><c> this</c><00:04:27.880><c> is</c>

00:04:28.070 --> 00:04:28.080 align:start position:0%
refine the data to 1 hour and this is
 

00:04:28.080 --> 00:04:32.270 align:start position:0%
refine the data to 1 hour and this is
done<00:04:28.520><c> using</c><00:04:29.440><c> um</c><00:04:29.960><c> several</c><00:04:30.520><c> different</c><00:04:31.280><c> methods</c>

00:04:32.270 --> 00:04:32.280 align:start position:0%
done using um several different methods
 

00:04:32.280 --> 00:04:34.909 align:start position:0%
done using um several different methods
we<00:04:32.440><c> use</c><00:04:32.800><c> spine</c><00:04:33.199><c> interpolation</c><00:04:34.000><c> for</c><00:04:34.280><c> variables</c>

00:04:34.909 --> 00:04:34.919 align:start position:0%
we use spine interpolation for variables
 

00:04:34.919 --> 00:04:37.950 align:start position:0%
we use spine interpolation for variables
like<00:04:35.280><c> temperature</c><00:04:35.960><c> and</c><00:04:36.199><c> wind</c><00:04:36.520><c> speed</c><00:04:37.520><c> and</c><00:04:37.800><c> the</c>

00:04:37.950 --> 00:04:37.960 align:start position:0%
like temperature and wind speed and the
 

00:04:37.960 --> 00:04:40.469 align:start position:0%
like temperature and wind speed and the
interpolation<00:04:38.720><c> of</c><00:04:38.880><c> the</c><00:04:39.039><c> clearness</c><00:04:39.680><c> index</c><00:04:40.120><c> for</c>

00:04:40.469 --> 00:04:40.479 align:start position:0%
interpolation of the clearness index for
 

00:04:40.479 --> 00:04:41.870 align:start position:0%
interpolation of the clearness index for
solar

00:04:41.870 --> 00:04:41.880 align:start position:0%
solar
 

00:04:41.880 --> 00:04:44.629 align:start position:0%
solar
radiation<00:04:42.880><c> the</c><00:04:43.160><c> graph</c><00:04:43.600><c> of</c><00:04:43.919><c> on</c><00:04:44.080><c> the</c><00:04:44.320><c> right</c>

00:04:44.629 --> 00:04:44.639 align:start position:0%
radiation the graph of on the right
 

00:04:44.639 --> 00:04:47.189 align:start position:0%
radiation the graph of on the right
illustrates<00:04:45.360><c> temporal</c><00:04:45.800><c> interpolation</c><00:04:46.639><c> for</c><00:04:47.080><c> a</c>

00:04:47.189 --> 00:04:47.199 align:start position:0%
illustrates temporal interpolation for a
 

00:04:47.199 --> 00:04:48.550 align:start position:0%
illustrates temporal interpolation for a
2<00:04:47.440><c> m</c>

00:04:48.550 --> 00:04:48.560 align:start position:0%
2 m
 

00:04:48.560 --> 00:04:51.350 align:start position:0%
2 m
temperature<00:04:49.560><c> the</c><00:04:49.919><c> original</c><00:04:50.479><c> Three</c><00:04:50.880><c> hourly</c>

00:04:51.350 --> 00:04:51.360 align:start position:0%
temperature the original Three hourly
 

00:04:51.360 --> 00:04:54.310 align:start position:0%
temperature the original Three hourly
data<00:04:51.880><c> is</c><00:04:52.039><c> shown</c><00:04:52.400><c> in</c><00:04:52.639><c> red</c><00:04:53.280><c> and</c><00:04:53.639><c> um</c><00:04:54.039><c> the</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
data is shown in red and um the
 

00:04:54.320 --> 00:04:56.790 align:start position:0%
data is shown in red and um the
interpolated<00:04:55.080><c> hourly</c><00:04:55.639><c> data</c><00:04:56.080><c> in</c>

00:04:56.790 --> 00:04:56.800 align:start position:0%
interpolated hourly data in
 

00:04:56.800 --> 00:04:59.870 align:start position:0%
interpolated hourly data in
blue<00:04:57.800><c> we</c><00:04:58.000><c> Will</c><00:04:58.280><c> further</c><00:04:58.919><c> explore</c><00:04:59.720><c> the</c>

00:04:59.870 --> 00:04:59.880 align:start position:0%
blue we Will further explore the
 

00:04:59.880 --> 00:05:01.950 align:start position:0%
blue we Will further explore the
different<00:05:00.280><c> temporal</c><00:05:01.000><c> interpolation</c>

00:05:01.950 --> 00:05:01.960 align:start position:0%
different temporal interpolation
 

00:05:01.960 --> 00:05:04.590 align:start position:0%
different temporal interpolation
techniques<00:05:02.720><c> in</c><00:05:02.880><c> the</c><00:05:03.080><c> next</c>

00:05:04.590 --> 00:05:04.600 align:start position:0%
techniques in the next
 

00:05:04.600 --> 00:05:07.790 align:start position:0%
techniques in the next
slide<00:05:05.600><c> for</c><00:05:06.000><c> temporal</c><00:05:06.520><c> interpolation</c><00:05:07.400><c> of</c>

00:05:07.790 --> 00:05:07.800 align:start position:0%
slide for temporal interpolation of
 

00:05:07.800 --> 00:05:10.670 align:start position:0%
slide for temporal interpolation of
temperature<00:05:08.600><c> and</c><00:05:09.039><c> wind</c><00:05:09.440><c> components</c><00:05:10.199><c> we</c><00:05:10.360><c> use</c>

00:05:10.670 --> 00:05:10.680 align:start position:0%
temperature and wind components we use
 

00:05:10.680 --> 00:05:13.870 align:start position:0%
temperature and wind components we use
this<00:05:11.080><c> planine</c><00:05:12.080><c> this</c><00:05:12.360><c> method</c><00:05:12.840><c> processes</c><00:05:13.440><c> data</c>

00:05:13.870 --> 00:05:13.880 align:start position:0%
this planine this method processes data
 

00:05:13.880 --> 00:05:17.110 align:start position:0%
this planine this method processes data
in<00:05:14.120><c> 3-day</c><00:05:14.840><c> Windows</c><00:05:15.759><c> keeping</c><00:05:16.199><c> only</c><00:05:16.479><c> the</c><00:05:16.720><c> day</c><00:05:16.960><c> in</c>

00:05:17.110 --> 00:05:17.120 align:start position:0%
in 3-day Windows keeping only the day in
 

00:05:17.120 --> 00:05:19.950 align:start position:0%
in 3-day Windows keeping only the day in
the<00:05:17.280><c> middle</c><00:05:18.000><c> and</c><00:05:18.199><c> it</c><00:05:18.400><c> erases</c><00:05:18.919><c> through</c><00:05:19.319><c> each</c>

00:05:19.950 --> 00:05:19.960 align:start position:0%
the middle and it erases through each
 

00:05:19.960 --> 00:05:23.150 align:start position:0%
the middle and it erases through each
day<00:05:20.960><c> secondly</c><00:05:21.440><c> for</c><00:05:21.759><c> ghi</c><00:05:22.560><c> a</c><00:05:22.759><c> different</c>

00:05:23.150 --> 00:05:23.160 align:start position:0%
day secondly for ghi a different
 

00:05:23.160 --> 00:05:26.270 align:start position:0%
day secondly for ghi a different
approach<00:05:23.479><c> is</c><00:05:23.720><c> used</c><00:05:24.080><c> due</c><00:05:24.280><c> to</c><00:05:24.520><c> its</c><00:05:25.039><c> strong</c><00:05:25.319><c> Dural</c>

00:05:26.270 --> 00:05:26.280 align:start position:0%
approach is used due to its strong Dural
 

00:05:26.280 --> 00:05:29.710 align:start position:0%
approach is used due to its strong Dural
cycle<00:05:27.280><c> instead</c><00:05:27.680><c> of</c><00:05:27.960><c> directly</c><00:05:28.639><c> interpolating</c>

00:05:29.710 --> 00:05:29.720 align:start position:0%
cycle instead of directly interpolating
 

00:05:29.720 --> 00:05:32.110 align:start position:0%
cycle instead of directly interpolating
radians<00:05:30.479><c> we</c><00:05:30.680><c> interplate</c><00:05:31.280><c> the</c><00:05:31.440><c> clearness</c>

00:05:32.110 --> 00:05:32.120 align:start position:0%
radians we interplate the clearness
 

00:05:32.120 --> 00:05:35.710 align:start position:0%
radians we interplate the clearness
index<00:05:32.960><c> which</c><00:05:33.199><c> reflects</c><00:05:33.800><c> atmospheric</c>

00:05:35.710 --> 00:05:35.720 align:start position:0%
index which reflects atmospheric
 

00:05:35.720 --> 00:05:37.990 align:start position:0%
index which reflects atmospheric
transmissivity<00:05:36.720><c> this</c><00:05:36.960><c> index</c><00:05:37.400><c> is</c><00:05:37.680><c> then</c>

00:05:37.990 --> 00:05:38.000 align:start position:0%
transmissivity this index is then
 

00:05:38.000 --> 00:05:40.309 align:start position:0%
transmissivity this index is then
converted<00:05:38.560><c> back</c><00:05:38.720><c> to</c><00:05:38.919><c> IR</c><00:05:39.080><c> Radiance</c><00:05:39.800><c> preserving</c>

00:05:40.309 --> 00:05:40.319 align:start position:0%
converted back to IR Radiance preserving
 

00:05:40.319 --> 00:05:41.550 align:start position:0%
converted back to IR Radiance preserving
the<00:05:40.520><c> daily</c>

00:05:41.550 --> 00:05:41.560 align:start position:0%
the daily
 

00:05:41.560 --> 00:05:44.150 align:start position:0%
the daily
profile<00:05:42.560><c> however</c><00:05:43.080><c> this</c><00:05:43.319><c> method</c><00:05:43.720><c> assumes</c>

00:05:44.150 --> 00:05:44.160 align:start position:0%
profile however this method assumes
 

00:05:44.160 --> 00:05:46.029 align:start position:0%
profile however this method assumes
stable<00:05:44.560><c> weather</c><00:05:45.120><c> conditions</c><00:05:45.560><c> within</c><00:05:45.919><c> the</c>

00:05:46.029 --> 00:05:46.039 align:start position:0%
stable weather conditions within the
 

00:05:46.039 --> 00:05:49.749 align:start position:0%
stable weather conditions within the
original<00:05:46.680><c> time</c><00:05:47.400><c> steps</c><00:05:48.400><c> finally</c><00:05:48.880><c> for</c><00:05:49.240><c> total</c>

00:05:49.749 --> 00:05:49.759 align:start position:0%
original time steps finally for total
 

00:05:49.759 --> 00:05:52.309 align:start position:0%
original time steps finally for total
precipitation<00:05:50.759><c> no</c><00:05:51.080><c> temporal</c><00:05:51.560><c> interpolation</c>

00:05:52.309 --> 00:05:52.319 align:start position:0%
precipitation no temporal interpolation
 

00:05:52.319 --> 00:05:55.150 align:start position:0%
precipitation no temporal interpolation
is<00:05:52.520><c> applied</c><00:05:53.199><c> because</c><00:05:53.479><c> it</c><00:05:53.680><c> is</c><00:05:53.960><c> aggregated</c>

00:05:55.150 --> 00:05:55.160 align:start position:0%
is applied because it is aggregated
 

00:05:55.160 --> 00:05:57.870 align:start position:0%
is applied because it is aggregated
daily<00:05:56.160><c> on</c><00:05:56.319><c> the</c><00:05:56.560><c> right</c><00:05:56.880><c> you</c><00:05:57.000><c> can</c><00:05:57.240><c> see</c><00:05:57.639><c> an</c>

00:05:57.870 --> 00:05:57.880 align:start position:0%
daily on the right you can see an
 

00:05:57.880 --> 00:06:00.629 align:start position:0%
daily on the right you can see an
example<00:05:58.520><c> of</c><00:05:58.759><c> temporal</c><00:05:59.160><c> interpolation</c><00:06:00.000><c> for</c>

00:06:00.629 --> 00:06:00.639 align:start position:0%
example of temporal interpolation for
 

00:06:00.639 --> 00:06:03.749 align:start position:0%
example of temporal interpolation for
ghi<00:06:01.639><c> where</c><00:06:01.919><c> the</c><00:06:02.080><c> original</c><00:06:02.680><c> data</c><00:06:03.039><c> points</c><00:06:03.479><c> are</c>

00:06:03.749 --> 00:06:03.759 align:start position:0%
ghi where the original data points are
 

00:06:03.759 --> 00:06:06.469 align:start position:0%
ghi where the original data points are
marked<00:06:04.360><c> in</c><00:06:04.600><c> blue</c><00:06:05.479><c> and</c><00:06:05.600><c> the</c><00:06:05.759><c> interpolated</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
marked in blue and the interpolated
 

00:06:06.479 --> 00:06:09.070 align:start position:0%
marked in blue and the interpolated
curve<00:06:06.840><c> is</c><00:06:07.000><c> shown</c><00:06:07.360><c> in</c>

00:06:09.070 --> 00:06:09.080 align:start position:0%
curve is shown in
 

00:06:09.080 --> 00:06:12.070 align:start position:0%
curve is shown in
red<00:06:10.080><c> wind</c><00:06:10.400><c> speed</c><00:06:10.800><c> measurements</c><00:06:11.400><c> are</c><00:06:11.639><c> usually</c>

00:06:12.070 --> 00:06:12.080 align:start position:0%
red wind speed measurements are usually
 

00:06:12.080 --> 00:06:15.150 align:start position:0%
red wind speed measurements are usually
taken<00:06:12.560><c> at</c><00:06:12.680><c> a</c><00:06:12.919><c> standard</c><00:06:13.440><c> height</c><00:06:13.800><c> of</c><00:06:14.080><c> 10</c><00:06:14.360><c> m</c><00:06:14.840><c> above</c>

00:06:15.150 --> 00:06:15.160 align:start position:0%
taken at a standard height of 10 m above
 

00:06:15.160 --> 00:06:17.870 align:start position:0%
taken at a standard height of 10 m above
ground<00:06:15.599><c> level</c><00:06:16.599><c> this</c><00:06:16.759><c> is</c><00:06:16.919><c> done</c><00:06:17.160><c> to</c><00:06:17.360><c> minimize</c>

00:06:17.870 --> 00:06:17.880 align:start position:0%
ground level this is done to minimize
 

00:06:17.880 --> 00:06:20.189 align:start position:0%
ground level this is done to minimize
the<00:06:18.080><c> influence</c><00:06:18.599><c> of</c><00:06:18.880><c> obstacles</c><00:06:19.639><c> such</c><00:06:19.919><c> as</c>

00:06:20.189 --> 00:06:20.199 align:start position:0%
the influence of obstacles such as
 

00:06:20.199 --> 00:06:22.749 align:start position:0%
the influence of obstacles such as
buildings<00:06:20.800><c> terrain</c><00:06:21.400><c> and</c><00:06:21.759><c> vegetation</c>

00:06:22.749 --> 00:06:22.759 align:start position:0%
buildings terrain and vegetation
 

00:06:22.759 --> 00:06:25.230 align:start position:0%
buildings terrain and vegetation
ensuring<00:06:23.280><c> the</c><00:06:23.479><c> data</c><00:06:23.800><c> is</c><00:06:24.160><c> as</c><00:06:24.440><c> accurate</c><00:06:24.960><c> as</c>

00:06:25.230 --> 00:06:25.240 align:start position:0%
ensuring the data is as accurate as
 

00:06:25.240 --> 00:06:27.230 align:start position:0%
ensuring the data is as accurate as
possible<00:06:25.599><c> for</c><00:06:25.840><c> open</c><00:06:26.160><c> ground</c>

00:06:27.230 --> 00:06:27.240 align:start position:0%
possible for open ground
 

00:06:27.240 --> 00:06:29.790 align:start position:0%
possible for open ground
conditions<00:06:28.240><c> however</c><00:06:28.560><c> for</c><00:06:28.840><c> energy</c><00:06:29.199><c> model</c>

00:06:29.790 --> 00:06:29.800 align:start position:0%
conditions however for energy model
 

00:06:29.800 --> 00:06:32.350 align:start position:0%
conditions however for energy model
purposes<00:06:30.400><c> we</c><00:06:30.599><c> often</c><00:06:30.919><c> need</c><00:06:31.400><c> wind</c><00:06:31.720><c> speed</c><00:06:32.039><c> data</c>

00:06:32.350 --> 00:06:32.360 align:start position:0%
purposes we often need wind speed data
 

00:06:32.360 --> 00:06:35.749 align:start position:0%
purposes we often need wind speed data
at<00:06:32.840><c> 100</c><00:06:33.160><c> m</c><00:06:33.680><c> above</c><00:06:33.960><c> ground</c><00:06:34.319><c> level</c><00:06:35.240><c> as</c><00:06:35.479><c> this</c>

00:06:35.749 --> 00:06:35.759 align:start position:0%
at 100 m above ground level as this
 

00:06:35.759 --> 00:06:37.749 align:start position:0%
at 100 m above ground level as this
corresponds<00:06:36.360><c> to</c><00:06:36.520><c> the</c><00:06:36.800><c> typical</c><00:06:37.240><c> height</c><00:06:37.560><c> of</c>

00:06:37.749 --> 00:06:37.759 align:start position:0%
corresponds to the typical height of
 

00:06:37.759 --> 00:06:39.110 align:start position:0%
corresponds to the typical height of
modern<00:06:38.120><c> wind</c>

00:06:39.110 --> 00:06:39.120 align:start position:0%
modern wind
 

00:06:39.120 --> 00:06:41.909 align:start position:0%
modern wind
turbines<00:06:40.120><c> to</c><00:06:40.440><c> estimate</c><00:06:40.960><c> wind</c><00:06:41.240><c> speed</c><00:06:41.599><c> at</c>

00:06:41.909 --> 00:06:41.919 align:start position:0%
turbines to estimate wind speed at
 

00:06:41.919 --> 00:06:44.510 align:start position:0%
turbines to estimate wind speed at
desired<00:06:42.720><c> we</c><00:06:42.919><c> use</c><00:06:43.240><c> the</c><00:06:43.440><c> wind</c><00:06:43.759><c> profile</c><00:06:44.240><c> power</c>

00:06:44.510 --> 00:06:44.520 align:start position:0%
desired we use the wind profile power
 

00:06:44.520 --> 00:06:48.230 align:start position:0%
desired we use the wind profile power
low<00:06:44.960><c> shown</c><00:06:45.880><c> here</c><00:06:46.880><c> the</c><00:06:47.039><c> formula</c><00:06:47.479><c> relates</c><00:06:47.960><c> wind</c>

00:06:48.230 --> 00:06:48.240 align:start position:0%
low shown here the formula relates wind
 

00:06:48.240 --> 00:06:52.830 align:start position:0%
low shown here the formula relates wind
speeds<00:06:48.680><c> at</c><00:06:48.919><c> two</c><00:06:49.280><c> heights</c><00:06:49.919><c> V1</c><00:06:50.560><c> and</c><00:06:51.240><c> V2</c><00:06:52.240><c> using</c><00:06:52.639><c> an</c>

00:06:52.830 --> 00:06:52.840 align:start position:0%
speeds at two heights V1 and V2 using an
 

00:06:52.840 --> 00:06:55.390 align:start position:0%
speeds at two heights V1 and V2 using an
empirically<00:06:53.400><c> derived</c><00:06:53.960><c> coefficient</c><00:06:54.720><c> Alpha</c>

00:06:55.390 --> 00:06:55.400 align:start position:0%
empirically derived coefficient Alpha
 

00:06:55.400 --> 00:06:58.550 align:start position:0%
empirically derived coefficient Alpha
and<00:06:55.599><c> the</c><00:06:55.800><c> ratio</c><00:06:56.240><c> of</c><00:06:56.400><c> the</c><00:06:56.560><c> two</c><00:06:56.800><c> heights</c><00:06:57.680><c> H1</c><00:06:58.319><c> and</c>

00:06:58.550 --> 00:06:58.560 align:start position:0%
and the ratio of the two heights H1 and
 

00:06:58.560 --> 00:07:00.350 align:start position:0%
and the ratio of the two heights H1 and
H2

00:07:00.350 --> 00:07:00.360 align:start position:0%
H2
 

00:07:00.360 --> 00:07:02.350 align:start position:0%
H2
the<00:07:00.560><c> value</c><00:07:00.919><c> of</c><00:07:01.199><c> alpha</c><00:07:01.680><c> depends</c><00:07:02.080><c> on</c>

00:07:02.350 --> 00:07:02.360 align:start position:0%
the value of alpha depends on
 

00:07:02.360 --> 00:07:05.550 align:start position:0%
the value of alpha depends on
atmospheric<00:07:03.120><c> stability</c><00:07:04.120><c> and</c><00:07:04.479><c> under</c><00:07:04.840><c> neutral</c>

00:07:05.550 --> 00:07:05.560 align:start position:0%
atmospheric stability and under neutral
 

00:07:05.560 --> 00:07:07.980 align:start position:0%
atmospheric stability and under neutral
conditions<00:07:06.080><c> it</c><00:07:06.319><c> is</c><00:07:06.800><c> approximately</c>

00:07:07.980 --> 00:07:07.990 align:start position:0%
conditions it is approximately
 

00:07:07.990 --> 00:07:10.070 align:start position:0%
conditions it is approximately
[Music]

00:07:10.070 --> 00:07:10.080 align:start position:0%
[Music]
 

00:07:10.080 --> 00:07:13.430 align:start position:0%
[Music]
0143<00:07:11.080><c> this</c><00:07:11.319><c> method</c><00:07:11.720><c> allows</c><00:07:12.120><c> us</c><00:07:12.360><c> to</c><00:07:12.960><c> scale</c>

00:07:13.430 --> 00:07:13.440 align:start position:0%
0143 this method allows us to scale
 

00:07:13.440 --> 00:07:15.830 align:start position:0%
0143 this method allows us to scale
winds<00:07:13.759><c> speed</c><00:07:14.080><c> data</c><00:07:14.400><c> from</c><00:07:14.680><c> 10</c><00:07:14.960><c> m</c><00:07:15.440><c> to</c><00:07:15.639><c> the</c>

00:07:15.830 --> 00:07:15.840 align:start position:0%
winds speed data from 10 m to the
 

00:07:15.840 --> 00:07:19.110 align:start position:0%
winds speed data from 10 m to the
required<00:07:16.400><c> height</c><00:07:16.680><c> of</c><00:07:17.199><c> 100</c><00:07:17.479><c> m</c><00:07:18.240><c> ensuring</c><00:07:18.840><c> its</c>

00:07:19.110 --> 00:07:19.120 align:start position:0%
required height of 100 m ensuring its
 

00:07:19.120 --> 00:07:22.070 align:start position:0%
required height of 100 m ensuring its
relevance<00:07:19.560><c> for</c><00:07:19.800><c> energy</c>

00:07:22.070 --> 00:07:22.080 align:start position:0%
relevance for energy
 

00:07:22.080 --> 00:07:24.189 align:start position:0%
relevance for energy
applications<00:07:23.080><c> the</c><00:07:23.240><c> alpha</c><00:07:23.599><c> coefficient</c>

00:07:24.189 --> 00:07:24.199 align:start position:0%
applications the alpha coefficient
 

00:07:24.199 --> 00:07:26.710 align:start position:0%
applications the alpha coefficient
determines<00:07:24.960><c> how</c><00:07:25.160><c> wind</c><00:07:25.479><c> speed</c><00:07:25.879><c> changes</c><00:07:26.440><c> with</c>

00:07:26.710 --> 00:07:26.720 align:start position:0%
determines how wind speed changes with
 

00:07:26.720 --> 00:07:29.589 align:start position:0%
determines how wind speed changes with
height<00:07:27.240><c> and</c><00:07:27.560><c> it's</c><00:07:27.800><c> not</c><00:07:28.280><c> fixed</c><00:07:29.160><c> in</c><00:07:29.319><c> fact</c><00:07:29.520><c> in</c>

00:07:29.589 --> 00:07:29.599 align:start position:0%
height and it's not fixed in fact in
 

00:07:29.599 --> 00:07:31.629 align:start position:0%
height and it's not fixed in fact in
fact<00:07:29.759><c> it</c><00:07:29.919><c> depends</c><00:07:30.319><c> on</c><00:07:30.520><c> the</c><00:07:30.680><c> local</c><00:07:31.240><c> conditions</c>

00:07:31.629 --> 00:07:31.639 align:start position:0%
fact it depends on the local conditions
 

00:07:31.639 --> 00:07:34.189 align:start position:0%
fact it depends on the local conditions
of<00:07:31.840><c> the</c><00:07:32.000><c> area</c><00:07:33.000><c> and</c><00:07:33.160><c> in</c><00:07:33.360><c> this</c><00:07:33.599><c> case</c><00:07:33.960><c> we</c>

00:07:34.189 --> 00:07:34.199 align:start position:0%
of the area and in this case we
 

00:07:34.199 --> 00:07:36.909 align:start position:0%
of the area and in this case we
calculate<00:07:34.720><c> Alpha</c><00:07:35.120><c> using</c><00:07:35.520><c> data</c><00:07:35.800><c> from</c><00:07:36.120><c> AI</c><00:07:36.680><c> wind</c>

00:07:36.909 --> 00:07:36.919 align:start position:0%
calculate Alpha using data from AI wind
 

00:07:36.919 --> 00:07:41.909 align:start position:0%
calculate Alpha using data from AI wind
speed<00:07:37.400><c> at</c><00:07:37.680><c> 10</c><00:07:38.240><c> and</c><00:07:38.759><c> 100</c><00:07:39.560><c> m</c><00:07:40.560><c> this</c><00:07:40.840><c> allows</c><00:07:41.199><c> us</c><00:07:41.440><c> to</c>

00:07:41.909 --> 00:07:41.919 align:start position:0%
speed at 10 and 100 m this allows us to
 

00:07:41.919 --> 00:07:44.189 align:start position:0%
speed at 10 and 100 m this allows us to
account<00:07:42.319><c> for</c><00:07:42.680><c> variations</c><00:07:43.319><c> in</c><00:07:43.560><c> surface</c>

00:07:44.189 --> 00:07:44.199 align:start position:0%
account for variations in surface
 

00:07:44.199 --> 00:07:46.710 align:start position:0%
account for variations in surface
roughness<00:07:44.919><c> and</c><00:07:45.280><c> other</c><00:07:45.639><c> local</c>

00:07:46.710 --> 00:07:46.720 align:start position:0%
roughness and other local
 

00:07:46.720 --> 00:07:49.430 align:start position:0%
roughness and other local
features<00:07:47.720><c> temporal</c><00:07:48.240><c> variations</c><00:07:48.800><c> in</c><00:07:49.000><c> Alpha</c>

00:07:49.430 --> 00:07:49.440 align:start position:0%
features temporal variations in Alpha
 

00:07:49.440 --> 00:07:53.070 align:start position:0%
features temporal variations in Alpha
are<00:07:49.720><c> also</c><00:07:50.240><c> considered</c><00:07:51.080><c> by</c><00:07:51.639><c> stratifying</c><00:07:52.639><c> uh</c>

00:07:53.070 --> 00:07:53.080 align:start position:0%
are also considered by stratifying uh
 

00:07:53.080 --> 00:07:55.629 align:start position:0%
are also considered by stratifying uh
the<00:07:53.479><c> efficient</c><00:07:54.000><c> by</c><00:07:54.240><c> the</c><00:07:54.400><c> months</c><00:07:54.879><c> and</c><00:07:55.080><c> the</c><00:07:55.280><c> hour</c>

00:07:55.629 --> 00:07:55.639 align:start position:0%
the efficient by the months and the hour
 

00:07:55.639 --> 00:07:59.550 align:start position:0%
the efficient by the months and the hour
of<00:07:55.800><c> the</c><00:07:56.400><c> day</c><00:07:57.400><c> this</c><00:07:57.800><c> ensures</c><00:07:58.440><c> that</c><00:07:58.759><c> we</c><00:07:59.039><c> can</c>

00:07:59.550 --> 00:07:59.560 align:start position:0%
of the day this ensures that we can
 

00:07:59.560 --> 00:08:02.149 align:start position:0%
of the day this ensures that we can
capture<00:08:00.080><c> changes</c><00:08:00.680><c> in</c><00:08:00.919><c> atmospheric</c><00:08:01.520><c> stability</c>

00:08:02.149 --> 00:08:02.159 align:start position:0%
capture changes in atmospheric stability
 

00:08:02.159 --> 00:08:05.430 align:start position:0%
capture changes in atmospheric stability
throughout<00:08:02.960><c> the</c><00:08:03.319><c> day</c><00:08:03.759><c> and</c><00:08:04.159><c> across</c><00:08:04.479><c> the</c>

00:08:05.430 --> 00:08:05.440 align:start position:0%
throughout the day and across the
 

00:08:05.440 --> 00:08:08.230 align:start position:0%
throughout the day and across the
seasons<00:08:06.440><c> as</c><00:08:06.639><c> shown</c><00:08:07.159><c> in</c><00:08:07.319><c> the</c><00:08:07.560><c> graph</c><00:08:07.840><c> on</c><00:08:08.039><c> the</c>

00:08:08.230 --> 00:08:08.240 align:start position:0%
seasons as shown in the graph on the
 

00:08:08.240 --> 00:08:11.350 align:start position:0%
seasons as shown in the graph on the
left<00:08:09.039><c> the</c><00:08:09.199><c> mean</c><00:08:09.479><c> value</c><00:08:09.960><c> of</c><00:08:10.280><c> alpha</c><00:08:10.759><c> varies</c>

00:08:11.350 --> 00:08:11.360 align:start position:0%
left the mean value of alpha varies
 

00:08:11.360 --> 00:08:14.110 align:start position:0%
left the mean value of alpha varies
significantly<00:08:12.360><c> depending</c><00:08:12.879><c> on</c><00:08:13.479><c> the</c><00:08:13.800><c> time</c><00:08:14.000><c> of</c>

00:08:14.110 --> 00:08:14.120 align:start position:0%
significantly depending on the time of
 

00:08:14.120 --> 00:08:17.230 align:start position:0%
significantly depending on the time of
the<00:08:14.319><c> day</c><00:08:14.599><c> and</c><00:08:14.759><c> the</c><00:08:15.120><c> month</c><00:08:16.120><c> on</c><00:08:16.319><c> the</c><00:08:16.560><c> right</c><00:08:17.000><c> the</c>

00:08:17.230 --> 00:08:17.240 align:start position:0%
the day and the month on the right the
 

00:08:17.240 --> 00:08:19.749 align:start position:0%
the day and the month on the right the
box<00:08:17.599><c> and</c><00:08:18.000><c> whisker</c><00:08:18.440><c> plot</c><00:08:18.879><c> shows</c><00:08:19.400><c> the</c>

00:08:19.749 --> 00:08:19.759 align:start position:0%
box and whisker plot shows the
 

00:08:19.759 --> 00:08:22.110 align:start position:0%
box and whisker plot shows the
distribution<00:08:20.360><c> of</c><00:08:20.560><c> alpha</c><00:08:20.919><c> values</c><00:08:21.599><c> for</c><00:08:21.840><c> each</c>

00:08:22.110 --> 00:08:22.120 align:start position:0%
distribution of alpha values for each
 

00:08:22.120 --> 00:08:25.270 align:start position:0%
distribution of alpha values for each
hour<00:08:22.599><c> across</c><00:08:22.919><c> the</c><00:08:23.039><c> entire</c><00:08:23.479><c> PCD</c>

00:08:25.270 --> 00:08:25.280 align:start position:0%
hour across the entire PCD
 

00:08:25.280 --> 00:08:28.230 align:start position:0%
hour across the entire PCD
domain<00:08:26.280><c> the</c><00:08:26.599><c> interquantile</c><00:08:27.280><c> range</c>

00:08:28.230 --> 00:08:28.240 align:start position:0%
domain the interquantile range
 

00:08:28.240 --> 00:08:30.909 align:start position:0%
domain the interquantile range
highlights<00:08:28.720><c> the</c><00:08:28.919><c> typical</c><00:08:29.560><c> viability</c><00:08:30.560><c> while</c>

00:08:30.909 --> 00:08:30.919 align:start position:0%
highlights the typical viability while
 

00:08:30.919 --> 00:08:34.070 align:start position:0%
highlights the typical viability while
the<00:08:31.080><c> whiskers</c><00:08:31.800><c> capture</c><00:08:32.360><c> extreme</c>

00:08:34.070 --> 00:08:34.080 align:start position:0%
the whiskers capture extreme
 

00:08:34.080 --> 00:08:37.029 align:start position:0%
the whiskers capture extreme
values<00:08:35.080><c> this</c><00:08:35.320><c> calculation</c><00:08:35.880><c> of</c><00:08:36.120><c> alpha</c><00:08:36.599><c> allows</c>

00:08:37.029 --> 00:08:37.039 align:start position:0%
values this calculation of alpha allows
 

00:08:37.039 --> 00:08:40.909 align:start position:0%
values this calculation of alpha allows
us<00:08:37.240><c> to</c><00:08:37.560><c> derive</c><00:08:38.039><c> wind</c><00:08:38.360><c> speeds</c><00:08:38.760><c> at</c><00:08:39.360><c> 100</c><00:08:39.680><c> m</c><00:08:40.599><c> even</c>

00:08:40.909 --> 00:08:40.919 align:start position:0%
us to derive wind speeds at 100 m even
 

00:08:40.919 --> 00:08:43.149 align:start position:0%
us to derive wind speeds at 100 m even
when<00:08:41.279><c> direct</c><00:08:41.760><c> measurements</c><00:08:42.360><c> are</c><00:08:42.839><c> not</c>

00:08:43.149 --> 00:08:43.159 align:start position:0%
when direct measurements are not
 

00:08:43.159 --> 00:08:46.630 align:start position:0%
when direct measurements are not
available<00:08:44.120><c> such</c><00:08:44.360><c> as</c><00:08:44.600><c> in</c><00:08:44.839><c> SIM</c><00:08:45.279><c> 6</c>

00:08:46.630 --> 00:08:46.640 align:start position:0%
available such as in SIM 6
 

00:08:46.640 --> 00:08:49.430 align:start position:0%
available such as in SIM 6
projections<00:08:47.640><c> this</c><00:08:48.000><c> makes</c><00:08:48.360><c> it</c><00:08:48.680><c> an</c><00:08:48.920><c> essential</c>

00:08:49.430 --> 00:08:49.440 align:start position:0%
projections this makes it an essential
 

00:08:49.440 --> 00:08:53.269 align:start position:0%
projections this makes it an essential
tool<00:08:49.720><c> for</c><00:08:50.040><c> climate</c><00:08:50.480><c> and</c><00:08:50.760><c> energy</c>

00:08:53.269 --> 00:08:53.279 align:start position:0%
tool for climate and energy
 

00:08:53.279 --> 00:08:55.670 align:start position:0%
tool for climate and energy
applications<00:08:54.279><c> thank</c><00:08:54.440><c> you</c><00:08:54.560><c> for</c><00:08:54.760><c> the</c><00:08:54.959><c> attention</c>

00:08:55.670 --> 00:08:55.680 align:start position:0%
applications thank you for the attention
 

00:08:55.680 --> 00:08:57.949 align:start position:0%
applications thank you for the attention
and<00:08:55.880><c> do</c><00:08:56.080><c> not</c><00:08:56.320><c> hesitate</c><00:08:56.839><c> to</c><00:08:57.040><c> reach</c><00:08:57.320><c> out</c><00:08:57.640><c> if</c><00:08:57.760><c> you</c>

00:08:57.949 --> 00:08:57.959 align:start position:0%
and do not hesitate to reach out if you
 

00:08:57.959 --> 00:09:02.440 align:start position:0%
and do not hesitate to reach out if you
have<00:08:58.160><c> any</c><00:08:58.440><c> questions</c><00:08:59.440><c> e</c>

