WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.389 align:start position:0%
 
Yesterday,<00:00:00.800><c> after</c><00:00:01.120><c> achieving</c><00:00:01.600><c> a</c><00:00:01.920><c> quote</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
Yesterday, after achieving a quote
 

00:00:02.399 --> 00:00:04.710 align:start position:0%
Yesterday, after achieving a quote
ludicrous<00:00:02.879><c> rate</c><00:00:03.120><c> of</c><00:00:03.360><c> progress,</c><00:00:03.919><c> Elon</c><00:00:04.319><c> Musk</c>

00:00:04.710 --> 00:00:04.720 align:start position:0%
ludicrous rate of progress, Elon Musk
 

00:00:04.720 --> 00:00:06.470 align:start position:0%
ludicrous rate of progress, <PERSON><PERSON>
released<00:00:05.040><c> his</c><00:00:05.200><c> AI</c><00:00:05.600><c> chatbot</c><00:00:06.080><c> and</c><00:00:06.240><c> large</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
released his AI chatbot and large
 

00:00:06.480 --> 00:00:08.549 align:start position:0%
released his AI chatbot and large
language<00:00:06.799><c> model,</c><00:00:07.120><c> Grock</c><00:00:07.520><c> 4,</c><00:00:07.839><c> and</c><00:00:08.080><c> claims</c><00:00:08.320><c> it's</c>

00:00:08.549 --> 00:00:08.559 align:start position:0%
language model, Grock 4, and claims it's
 

00:00:08.559 --> 00:00:10.310 align:start position:0%
language model, Grock 4, and claims it's
the<00:00:08.720><c> smartest</c><00:00:09.040><c> AI</c><00:00:09.360><c> in</c><00:00:09.519><c> the</c><00:00:09.679><c> world</c><00:00:09.920><c> along</c><00:00:10.160><c> with</c>

00:00:10.310 --> 00:00:10.320 align:start position:0%
the smartest AI in the world along with
 

00:00:10.320 --> 00:00:12.070 align:start position:0%
the smartest AI in the world along with
the<00:00:10.480><c> Trust</c><00:00:10.719><c> Me</c><00:00:10.880><c> Bro</c><00:00:11.120><c> benchmarks</c><00:00:11.599><c> to</c><00:00:11.759><c> back</c><00:00:11.920><c> it</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
the Trust Me Bro benchmarks to back it
 

00:00:12.080 --> 00:00:14.310 align:start position:0%
the Trust Me Bro benchmarks to back it
up.<00:00:12.400><c> It</c><00:00:12.559><c> can</c><00:00:12.719><c> achieve</c><00:00:13.040><c> perfect</c><00:00:13.440><c> SAT</c><00:00:13.920><c> scores</c>

00:00:14.310 --> 00:00:14.320 align:start position:0%
up. It can achieve perfect SAT scores
 

00:00:14.320 --> 00:00:16.550 align:start position:0%
up. It can achieve perfect SAT scores
every<00:00:14.559><c> time</c><00:00:14.960><c> and</c><00:00:15.200><c> outperforms</c><00:00:15.920><c> almost</c><00:00:16.240><c> every</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
every time and outperforms almost every
 

00:00:16.560 --> 00:00:18.630 align:start position:0%
every time and outperforms almost every
grad<00:00:16.880><c> student</c><00:00:17.119><c> in</c><00:00:17.359><c> every</c><00:00:17.680><c> discipline.</c><00:00:18.320><c> Vibe</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
grad student in every discipline. Vibe
 

00:00:18.640 --> 00:00:20.070 align:start position:0%
grad student in every discipline. Vibe
coders<00:00:18.880><c> have</c><00:00:19.039><c> been</c><00:00:19.199><c> dropping</c><00:00:19.520><c> all</c><00:00:19.760><c> kinds</c><00:00:19.920><c> of</c>

00:00:20.070 --> 00:00:20.080 align:start position:0%
coders have been dropping all kinds of
 

00:00:20.080 --> 00:00:21.910 align:start position:0%
coders have been dropping all kinds of
crazy<00:00:20.320><c> demos</c><00:00:20.720><c> with</c><00:00:20.880><c> it,</c><00:00:21.199><c> like</c><00:00:21.359><c> this</c><00:00:21.600><c> 3D</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
crazy demos with it, like this 3D
 

00:00:21.920 --> 00:00:24.070 align:start position:0%
crazy demos with it, like this 3D
firstperson<00:00:22.480><c> shooter</c><00:00:22.880><c> built</c><00:00:23.039><c> in</c><00:00:23.279><c> 4</c><00:00:23.519><c> hours.</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
firstperson shooter built in 4 hours.
 

00:00:24.080 --> 00:00:25.830 align:start position:0%
firstperson shooter built in 4 hours.
And<00:00:24.240><c> Elon</c><00:00:24.640><c> himself</c><00:00:24.960><c> claims</c><00:00:25.199><c> it's</c><00:00:25.439><c> even</c><00:00:25.680><c> better</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
And Elon himself claims it's even better
 

00:00:25.840 --> 00:00:27.589 align:start position:0%
And Elon himself claims it's even better
than<00:00:26.000><c> Cursor.</c><00:00:26.560><c> All</c><00:00:26.720><c> you</c><00:00:26.880><c> have</c><00:00:26.960><c> to</c><00:00:27.039><c> do</c><00:00:27.199><c> is</c><00:00:27.359><c> copy</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
than Cursor. All you have to do is copy
 

00:00:27.599 --> 00:00:29.349 align:start position:0%
than Cursor. All you have to do is copy
and<00:00:27.760><c> paste</c><00:00:28.000><c> your</c><00:00:28.160><c> entire</c><00:00:28.480><c> codebase</c><00:00:28.880><c> into</c><00:00:29.119><c> it.</c>

00:00:29.349 --> 00:00:29.359 align:start position:0%
and paste your entire codebase into it.
 

00:00:29.359 --> 00:00:31.429 align:start position:0%
and paste your entire codebase into it.
In<00:00:29.519><c> addition,</c><00:00:30.000><c> Super</c><00:00:30.320><c> Grock</c><00:00:30.640><c> 4</c><00:00:30.880><c> Heavy</c><00:00:31.119><c> can</c><00:00:31.279><c> run</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
In addition, Super Grock 4 Heavy can run
 

00:00:31.439 --> 00:00:33.670 align:start position:0%
In addition, Super Grock 4 Heavy can run
in<00:00:31.599><c> parallel</c><00:00:32.000><c> to</c><00:00:32.320><c> solve</c><00:00:32.559><c> complex</c><00:00:33.120><c> problems</c>

00:00:33.670 --> 00:00:33.680 align:start position:0%
in parallel to solve complex problems
 

00:00:33.680 --> 00:00:35.350 align:start position:0%
in parallel to solve complex problems
while<00:00:33.920><c> your</c><00:00:34.160><c> obsolete</c><00:00:34.640><c> monkey</c><00:00:34.960><c> brain</c><00:00:35.200><c> looks</c>

00:00:35.350 --> 00:00:35.360 align:start position:0%
while your obsolete monkey brain looks
 

00:00:35.360 --> 00:00:37.750 align:start position:0%
while your obsolete monkey brain looks
in<00:00:35.520><c> awe</c><00:00:35.680><c> at</c><00:00:35.840><c> this</c><00:00:36.079><c> beautiful</c><00:00:36.399><c> futuristic</c><00:00:37.040><c> UI.</c>

00:00:37.750 --> 00:00:37.760 align:start position:0%
in awe at this beautiful futuristic UI.
 

00:00:37.760 --> 00:00:39.670 align:start position:0%
in awe at this beautiful futuristic UI.
It<00:00:37.920><c> all</c><00:00:38.079><c> sounds</c><00:00:38.399><c> amazing,</c><00:00:38.960><c> but</c><00:00:39.200><c> there's</c><00:00:39.440><c> just</c>

00:00:39.670 --> 00:00:39.680 align:start position:0%
It all sounds amazing, but there's just
 

00:00:39.680 --> 00:00:41.750 align:start position:0%
It all sounds amazing, but there's just
one<00:00:39.840><c> problem.</c><00:00:40.399><c> Grock</c><00:00:40.800><c> is</c><00:00:41.040><c> literally</c><00:00:41.440><c> Mecca</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
one problem. Grock is literally Mecca
 

00:00:41.760 --> 00:00:43.110 align:start position:0%
one problem. Grock is literally Mecca
Hitler.<00:00:42.239><c> Or</c><00:00:42.399><c> at</c><00:00:42.399><c> least</c><00:00:42.640><c> that's</c><00:00:42.800><c> what</c><00:00:42.960><c> it's</c>

00:00:43.110 --> 00:00:43.120 align:start position:0%
Hitler. Or at least that's what it's
 

00:00:43.120 --> 00:00:44.709 align:start position:0%
Hitler. Or at least that's what it's
been<00:00:43.280><c> calling</c><00:00:43.440><c> itself</c><00:00:43.840><c> recently,</c><00:00:44.399><c> while</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
been calling itself recently, while
 

00:00:44.719 --> 00:00:46.389 align:start position:0%
been calling itself recently, while
offering<00:00:45.120><c> unprompted</c><00:00:45.760><c> praise</c><00:00:46.000><c> to</c><00:00:46.160><c> the</c>

00:00:46.389 --> 00:00:46.399 align:start position:0%
offering unprompted praise to the
 

00:00:46.399 --> 00:00:48.709 align:start position:0%
offering unprompted praise to the
original<00:00:46.719><c> emo</c><00:00:47.120><c> kid,</c><00:00:47.360><c> Adolf</c><00:00:47.760><c> H.</c><00:00:48.239><c> Yes,</c><00:00:48.559><c> the</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
original emo kid, Adolf H. Yes, the
 

00:00:48.719 --> 00:00:50.549 align:start position:0%
original emo kid, Adolf H. Yes, the
Austrian<00:00:49.120><c> painter</c><00:00:49.440><c> who</c><00:00:49.680><c> died</c><00:00:49.840><c> in</c><00:00:50.000><c> Argentina</c>

00:00:50.549 --> 00:00:50.559 align:start position:0%
Austrian painter who died in Argentina
 

00:00:50.559 --> 00:00:53.029 align:start position:0%
Austrian painter who died in Argentina
in<00:00:50.800><c> 1962.</c><00:00:51.920><c> Despite</c><00:00:52.239><c> this</c><00:00:52.480><c> controversy,</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
in 1962. Despite this controversy,
 

00:00:53.039 --> 00:00:54.549 align:start position:0%
in 1962. Despite this controversy,
though,<00:00:53.440><c> Grock</c><00:00:53.840><c> appears</c><00:00:54.079><c> to</c><00:00:54.239><c> have</c><00:00:54.399><c> pulled</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
though, Grock appears to have pulled
 

00:00:54.559 --> 00:00:56.790 align:start position:0%
though, Grock appears to have pulled
ahead<00:00:54.879><c> in</c><00:00:55.120><c> the</c><00:00:55.280><c> race</c><00:00:55.440><c> to</c><00:00:55.600><c> AGI.</c><00:00:56.320><c> In</c><00:00:56.559><c> today's</c>

00:00:56.790 --> 00:00:56.800 align:start position:0%
ahead in the race to AGI. In today's
 

00:00:56.800 --> 00:00:58.470 align:start position:0%
ahead in the race to AGI. In today's
video,<00:00:57.120><c> we'll</c><00:00:57.280><c> put</c><00:00:57.360><c> it</c><00:00:57.520><c> to</c><00:00:57.680><c> the</c><00:00:57.840><c> ultimate</c><00:00:58.160><c> test</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
video, we'll put it to the ultimate test
 

00:00:58.480 --> 00:01:00.389 align:start position:0%
video, we'll put it to the ultimate test
and<00:00:58.719><c> find</c><00:00:58.800><c> out</c><00:00:58.960><c> if</c><00:00:59.120><c> XAI</c><00:00:59.760><c> just</c><00:00:59.920><c> cracked</c><00:01:00.239><c> the</c>

00:01:00.389 --> 00:01:00.399 align:start position:0%
and find out if XAI just cracked the
 

00:01:00.399 --> 00:01:01.910 align:start position:0%
and find out if XAI just cracked the
final<00:01:00.640><c> solution</c><00:01:01.039><c> to</c><00:01:01.359><c> artificial</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
final solution to artificial
 

00:01:01.920 --> 00:01:04.789 align:start position:0%
final solution to artificial
intelligence.<00:01:02.640><c> It</c><00:01:02.800><c> is</c><00:01:02.960><c> July</c><00:01:03.280><c> 11th,</c><00:01:03.760><c> 2025,</c><00:01:04.559><c> and</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
intelligence. It is July 11th, 2025, and
 

00:01:04.799 --> 00:01:06.550 align:start position:0%
intelligence. It is July 11th, 2025, and
you're<00:01:05.040><c> watching</c><00:01:05.280><c> the</c><00:01:05.439><c> Code</c><00:01:05.760><c> Report.</c><00:01:06.159><c> Elon</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
you're watching the Code Report. Elon
 

00:01:06.560 --> 00:01:08.390 align:start position:0%
you're watching the Code Report. Elon
Musk<00:01:06.880><c> might</c><00:01:07.119><c> have</c><00:01:07.280><c> more</c><00:01:07.520><c> haters</c><00:01:07.840><c> than</c><00:01:08.080><c> anyone</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
Musk might have more haters than anyone
 

00:01:08.400 --> 00:01:10.149 align:start position:0%
Musk might have more haters than anyone
else<00:01:08.560><c> in</c><00:01:08.799><c> the</c><00:01:08.880><c> world</c><00:01:09.040><c> right</c><00:01:09.280><c> now.</c><00:01:09.600><c> Libs</c><00:01:10.000><c> hate</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
else in the world right now. Libs hate
 

00:01:10.159 --> 00:01:11.990 align:start position:0%
else in the world right now. Libs hate
him<00:01:10.240><c> for</c><00:01:10.400><c> going</c><00:01:10.560><c> full</c><00:01:10.799><c> MAGA,</c><00:01:11.360><c> while</c><00:01:11.600><c> MAGA</c>

00:01:11.990 --> 00:01:12.000 align:start position:0%
him for going full MAGA, while MAGA
 

00:01:12.000 --> 00:01:13.750 align:start position:0%
him for going full MAGA, while MAGA
hates<00:01:12.240><c> him</c><00:01:12.400><c> for</c><00:01:12.640><c> accusing</c><00:01:13.040><c> Trump</c><00:01:13.280><c> of</c><00:01:13.439><c> being</c><00:01:13.600><c> on</c>

00:01:13.750 --> 00:01:13.760 align:start position:0%
hates him for accusing Trump of being on
 

00:01:13.760 --> 00:01:15.910 align:start position:0%
hates him for accusing Trump of being on
the<00:01:13.840><c> Epstein</c><00:01:14.400><c> client</c><00:01:14.640><c> list,</c><00:01:15.200><c> a</c><00:01:15.360><c> list</c><00:01:15.520><c> that</c><00:01:15.760><c> was</c>

00:01:15.910 --> 00:01:15.920 align:start position:0%
the Epstein client list, a list that was
 

00:01:15.920 --> 00:01:17.749 align:start position:0%
the Epstein client list, a list that was
on<00:01:16.080><c> the</c><00:01:16.240><c> Attorney</c><00:01:16.560><c> General's</c><00:01:16.960><c> desk,</c><00:01:17.280><c> but</c><00:01:17.520><c> now</c>

00:01:17.749 --> 00:01:17.759 align:start position:0%
on the Attorney General's desk, but now
 

00:01:17.759 --> 00:01:19.749 align:start position:0%
on the Attorney General's desk, but now
magically<00:01:18.240><c> never</c><00:01:18.560><c> existed.</c><00:01:19.200><c> It</c><00:01:19.439><c> turns</c><00:01:19.600><c> out</c>

00:01:19.749 --> 00:01:19.759 align:start position:0%
magically never existed. It turns out
 

00:01:19.759 --> 00:01:21.350 align:start position:0%
magically never existed. It turns out
this<00:01:19.920><c> poor</c><00:01:20.159><c> guy</c><00:01:20.320><c> was</c><00:01:20.479><c> just</c><00:01:20.640><c> a</c><00:01:20.799><c> math</c><00:01:21.040><c> teacher</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
this poor guy was just a math teacher
 

00:01:21.360 --> 00:01:23.109 align:start position:0%
this poor guy was just a math teacher
with<00:01:21.520><c> no</c><00:01:21.759><c> clients</c><00:01:22.080><c> at</c><00:01:22.240><c> all.</c><00:01:22.560><c> The</c><00:01:22.720><c> haters</c><00:01:23.040><c> want</c>

00:01:23.109 --> 00:01:23.119 align:start position:0%
with no clients at all. The haters want
 

00:01:23.119 --> 00:01:25.270 align:start position:0%
with no clients at all. The haters want
to<00:01:23.280><c> see</c><00:01:23.360><c> Elon</c><00:01:23.840><c> fail,</c><00:01:24.159><c> but</c><00:01:24.400><c> Gro</c><00:01:24.720><c> 4</c><00:01:24.960><c> is</c><00:01:25.119><c> too</c>

00:01:25.270 --> 00:01:25.280 align:start position:0%
to see Elon fail, but Gro 4 is too
 

00:01:25.280 --> 00:01:27.030 align:start position:0%
to see Elon fail, but Gro 4 is too
impressive<00:01:25.680><c> to</c><00:01:25.920><c> ignore.</c><00:01:26.400><c> If</c><00:01:26.560><c> we're</c><00:01:26.720><c> to</c><00:01:26.799><c> trust</c>

00:01:27.030 --> 00:01:27.040 align:start position:0%
impressive to ignore. If we're to trust
 

00:01:27.040 --> 00:01:28.550 align:start position:0%
impressive to ignore. If we're to trust
these<00:01:27.280><c> benchmarks,</c><00:01:27.920><c> its</c><00:01:28.159><c> reasoning</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
these benchmarks, its reasoning
 

00:01:28.560 --> 00:01:30.469 align:start position:0%
these benchmarks, its reasoning
capabilities<00:01:29.200><c> are</c><00:01:29.439><c> far</c><00:01:29.680><c> ahead</c><00:01:29.920><c> of</c><00:01:30.080><c> the</c><00:01:30.320><c> other</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
capabilities are far ahead of the other
 

00:01:30.479 --> 00:01:33.109 align:start position:0%
capabilities are far ahead of the other
top<00:01:30.799><c> models,</c><00:01:31.360><c> most</c><00:01:31.600><c> notably</c><00:01:32.000><c> on</c><00:01:32.159><c> the</c><00:01:32.320><c> Arc</c><00:01:32.640><c> AGI</c>

00:01:33.109 --> 00:01:33.119 align:start position:0%
top models, most notably on the Arc AGI
 

00:01:33.119 --> 00:01:34.950 align:start position:0%
top models, most notably on the Arc AGI
benchmark.<00:01:33.759><c> It's</c><00:01:34.000><c> not</c><00:01:34.159><c> only</c><00:01:34.320><c> outperforming</c>

00:01:34.950 --> 00:01:34.960 align:start position:0%
benchmark. It's not only outperforming
 

00:01:34.960 --> 00:01:36.870 align:start position:0%
benchmark. It's not only outperforming
other<00:01:35.280><c> models,</c><00:01:35.680><c> but</c><00:01:35.920><c> also</c><00:01:36.159><c> doing</c><00:01:36.400><c> it</c><00:01:36.560><c> at</c><00:01:36.720><c> a</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
other models, but also doing it at a
 

00:01:36.880 --> 00:01:39.190 align:start position:0%
other models, but also doing it at a
lower<00:01:37.119><c> cost.</c><00:01:37.600><c> And</c><00:01:37.759><c> XAI</c><00:01:38.320><c> is</c><00:01:38.400><c> scaling</c><00:01:38.720><c> up</c><00:01:38.880><c> very</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
lower cost. And XAI is scaling up very
 

00:01:39.200 --> 00:01:40.710 align:start position:0%
lower cost. And XAI is scaling up very
aggressively,<00:01:39.840><c> like</c><00:01:40.079><c> they're</c><00:01:40.240><c> even</c><00:01:40.479><c> shipping</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
aggressively, like they're even shipping
 

00:01:40.720 --> 00:01:42.630 align:start position:0%
aggressively, like they're even shipping
a<00:01:40.880><c> power</c><00:01:41.119><c> plant</c><00:01:41.360><c> from</c><00:01:41.600><c> overseas</c><00:01:42.159><c> because</c><00:01:42.400><c> they</c>

00:01:42.630 --> 00:01:42.640 align:start position:0%
a power plant from overseas because they
 

00:01:42.640 --> 00:01:44.069 align:start position:0%
a power plant from overseas because they
can't<00:01:42.799><c> get</c><00:01:42.880><c> it</c><00:01:43.040><c> done</c><00:01:43.280><c> fast</c><00:01:43.520><c> enough</c><00:01:43.680><c> in</c><00:01:43.920><c> the</c>

00:01:44.069 --> 00:01:44.079 align:start position:0%
can't get it done fast enough in the
 

00:01:44.079 --> 00:01:45.590 align:start position:0%
can't get it done fast enough in the
United<00:01:44.320><c> States.</c><00:01:44.799><c> The</c><00:01:45.040><c> thing</c><00:01:45.119><c> is</c><00:01:45.360><c> though,</c>

00:01:45.590 --> 00:01:45.600 align:start position:0%
United States. The thing is though,
 

00:01:45.600 --> 00:01:47.429 align:start position:0%
United States. The thing is though,
every<00:01:45.840><c> model</c><00:01:46.159><c> nowadays</c><00:01:46.640><c> is</c><00:01:46.799><c> cooked</c><00:01:47.119><c> to</c><00:01:47.280><c> look</c>

00:01:47.429 --> 00:01:47.439 align:start position:0%
every model nowadays is cooked to look
 

00:01:47.439 --> 00:01:49.270 align:start position:0%
every model nowadays is cooked to look
as<00:01:47.600><c> good</c><00:01:47.680><c> as</c><00:01:47.920><c> possible</c><00:01:48.159><c> on</c><00:01:48.399><c> benchmarks.</c><00:01:49.119><c> And</c>

00:01:49.270 --> 00:01:49.280 align:start position:0%
as good as possible on benchmarks. And
 

00:01:49.280 --> 00:01:50.950 align:start position:0%
as good as possible on benchmarks. And
the<00:01:49.439><c> one</c><00:01:49.600><c> true</c><00:01:49.840><c> test</c><00:01:50.079><c> is</c><00:01:50.240><c> to</c><00:01:50.479><c> have</c><00:01:50.560><c> it</c><00:01:50.720><c> solve</c>

00:01:50.950 --> 00:01:50.960 align:start position:0%
the one true test is to have it solve
 

00:01:50.960 --> 00:01:52.789 align:start position:0%
the one true test is to have it solve
real<00:01:51.280><c> problems</c><00:01:51.600><c> in</c><00:01:51.840><c> your</c><00:01:52.000><c> own</c><00:01:52.159><c> life.</c><00:01:52.560><c> You</c><00:01:52.720><c> can</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
real problems in your own life. You can
 

00:01:52.799 --> 00:01:55.429 align:start position:0%
real problems in your own life. You can
use<00:01:52.960><c> Gro</c><00:01:53.360><c> 4</c><00:01:53.600><c> for</c><00:01:53.759><c> just</c><00:01:54.000><c> $30</c><00:01:54.399><c> per</c><00:01:54.720><c> month.</c><00:01:55.119><c> Or</c><00:01:55.280><c> if</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
use Gro 4 for just $30 per month. Or if
 

00:01:55.439 --> 00:01:57.190 align:start position:0%
use Gro 4 for just $30 per month. Or if
you're<00:01:55.520><c> not</c><00:01:55.680><c> already</c><00:01:56.000><c> broke</c><00:01:56.320><c> from</c><00:01:56.560><c> OpenAI</c>

00:01:57.190 --> 00:01:57.200 align:start position:0%
you're not already broke from OpenAI
 

00:01:57.200 --> 00:01:59.510 align:start position:0%
you're not already broke from OpenAI
Pro,<00:01:57.520><c> Claude</c><00:01:57.920><c> Max,</c><00:01:58.240><c> and</c><00:01:58.320><c> Gemini</c><00:01:58.799><c> Ultra,</c><00:01:59.360><c> you</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
Pro, Claude Max, and Gemini Ultra, you
 

00:01:59.520 --> 00:02:02.230 align:start position:0%
Pro, Claude Max, and Gemini Ultra, you
can<00:01:59.600><c> use</c><00:01:59.759><c> Super</c><00:02:00.079><c> Gro</c><00:02:00.399><c> 4</c><00:02:00.640><c> Heavy</c><00:02:00.960><c> for</c><00:02:01.200><c> just</c><00:02:01.520><c> $300</c>

00:02:02.230 --> 00:02:02.240 align:start position:0%
can use Super Gro 4 Heavy for just $300
 

00:02:02.240 --> 00:02:03.990 align:start position:0%
can use Super Gro 4 Heavy for just $300
per<00:02:02.479><c> month,</c><00:02:02.799><c> a</c><00:02:03.040><c> version</c><00:02:03.280><c> with</c><00:02:03.439><c> higher</c><00:02:03.680><c> rate</c>

00:02:03.990 --> 00:02:04.000 align:start position:0%
per month, a version with higher rate
 

00:02:04.000 --> 00:02:05.670 align:start position:0%
per month, a version with higher rate
limits<00:02:04.240><c> and</c><00:02:04.479><c> the</c><00:02:04.640><c> ability</c><00:02:04.960><c> to</c><00:02:05.119><c> run</c><00:02:05.360><c> multiple</c>

00:02:05.670 --> 00:02:05.680 align:start position:0%
limits and the ability to run multiple
 

00:02:05.680 --> 00:02:07.590 align:start position:0%
limits and the ability to run multiple
agents<00:02:06.079><c> in</c><00:02:06.240><c> parallel.</c><00:02:06.799><c> But</c><00:02:06.960><c> one</c><00:02:07.200><c> problem</c><00:02:07.360><c> in</c>

00:02:07.590 --> 00:02:07.600 align:start position:0%
agents in parallel. But one problem in
 

00:02:07.600 --> 00:02:09.589 align:start position:0%
agents in parallel. But one problem in
my<00:02:07.759><c> life</c><00:02:08.000><c> is</c><00:02:08.239><c> building</c><00:02:08.640><c> spell</c><00:02:08.959><c> 5</c><00:02:09.119><c> apps</c><00:02:09.360><c> with</c>

00:02:09.589 --> 00:02:09.599 align:start position:0%
my life is building spell 5 apps with
 

00:02:09.599 --> 00:02:11.830 align:start position:0%
my life is building spell 5 apps with
runes.<00:02:10.160><c> So</c><00:02:10.319><c> let's</c><00:02:10.479><c> see</c><00:02:10.560><c> if</c><00:02:10.720><c> we</c><00:02:10.879><c> can</c><00:02:11.039><c> make</c><00:02:11.200><c> Gro</c><00:02:11.599><c> 4</c>

00:02:11.830 --> 00:02:11.840 align:start position:0%
runes. So let's see if we can make Gro 4
 

00:02:11.840 --> 00:02:13.830 align:start position:0%
runes. So let's see if we can make Gro 4
build<00:02:12.080><c> a</c><00:02:12.239><c> simple</c><00:02:12.480><c> to-do</c><00:02:12.800><c> app</c><00:02:13.040><c> with</c><00:02:13.200><c> this</c><00:02:13.440><c> tech.</c>

00:02:13.830 --> 00:02:13.840 align:start position:0%
build a simple to-do app with this tech.
 

00:02:13.840 --> 00:02:15.350 align:start position:0%
build a simple to-do app with this tech.
I've<00:02:14.080><c> tried</c><00:02:14.239><c> this</c><00:02:14.400><c> prompt</c><00:02:14.720><c> with</c><00:02:14.959><c> every</c><00:02:15.120><c> other</c>

00:02:15.350 --> 00:02:15.360 align:start position:0%
I've tried this prompt with every other
 

00:02:15.360 --> 00:02:17.030 align:start position:0%
I've tried this prompt with every other
AI<00:02:15.680><c> tool</c><00:02:15.920><c> out</c><00:02:16.080><c> there,</c><00:02:16.319><c> but</c><00:02:16.480><c> none</c><00:02:16.720><c> have</c><00:02:16.879><c> been</c>

00:02:17.030 --> 00:02:17.040 align:start position:0%
AI tool out there, but none have been
 

00:02:17.040 --> 00:02:19.030 align:start position:0%
AI tool out there, but none have been
able<00:02:17.120><c> to</c><00:02:17.280><c> satisfy</c><00:02:17.760><c> me.</c><00:02:18.080><c> When</c><00:02:18.239><c> prompted,</c><00:02:18.720><c> Grock</c>

00:02:19.030 --> 00:02:19.040 align:start position:0%
able to satisfy me. When prompted, Grock
 

00:02:19.040 --> 00:02:20.710 align:start position:0%
able to satisfy me. When prompted, Grock
did<00:02:19.200><c> a</c><00:02:19.360><c> ton</c><00:02:19.520><c> of</c><00:02:19.680><c> research.</c><00:02:20.160><c> It</c><00:02:20.319><c> went</c><00:02:20.400><c> to</c><00:02:20.560><c> the</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
did a ton of research. It went to the
 

00:02:20.720 --> 00:02:22.390 align:start position:0%
did a ton of research. It went to the
documentation.<00:02:21.520><c> It</c><00:02:21.680><c> went</c><00:02:21.840><c> to</c><00:02:22.000><c> Reddit,</c>

00:02:22.390 --> 00:02:22.400 align:start position:0%
documentation. It went to Reddit,
 

00:02:22.400 --> 00:02:24.390 align:start position:0%
documentation. It went to Reddit,
GitHub,<00:02:22.800><c> and</c><00:02:23.040><c> even</c><00:02:23.200><c> watched</c><00:02:23.520><c> YouTube</c><00:02:23.840><c> videos.</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
GitHub, and even watched YouTube videos.
 

00:02:24.400 --> 00:02:25.990 align:start position:0%
GitHub, and even watched YouTube videos.
And<00:02:24.480><c> the</c><00:02:24.640><c> end</c><00:02:24.879><c> result</c><00:02:25.120><c> was</c><00:02:25.360><c> a</c><00:02:25.520><c> full</c><00:02:25.760><c> working</c>

00:02:25.990 --> 00:02:26.000 align:start position:0%
And the end result was a full working
 

00:02:26.000 --> 00:02:27.910 align:start position:0%
And the end result was a full working
demo<00:02:26.400><c> that</c><00:02:26.640><c> did</c><00:02:26.800><c> use</c><00:02:26.959><c> the</c><00:02:27.120><c> new</c><00:02:27.360><c> runes</c><00:02:27.680><c> feature</c>

00:02:27.910 --> 00:02:27.920 align:start position:0%
demo that did use the new runes feature
 

00:02:27.920 --> 00:02:29.910 align:start position:0%
demo that did use the new runes feature
in<00:02:28.080><c> Spell</c><00:02:28.480><c> 5.</c><00:02:28.959><c> However,</c><00:02:29.280><c> when</c><00:02:29.440><c> I</c><00:02:29.599><c> looked</c><00:02:29.760><c> a</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
in Spell 5. However, when I looked a
 

00:02:29.920 --> 00:02:31.350 align:start position:0%
in Spell 5. However, when I looked a
little<00:02:30.000><c> more</c><00:02:30.160><c> closely</c><00:02:30.480><c> at</c><00:02:30.720><c> the</c><00:02:30.879><c> code,</c><00:02:31.200><c> I</c>

00:02:31.350 --> 00:02:31.360 align:start position:0%
little more closely at the code, I
 

00:02:31.360 --> 00:02:33.589 align:start position:0%
little more closely at the code, I
noticed<00:02:31.599><c> it</c><00:02:31.840><c> was</c><00:02:32.000><c> using</c><00:02:32.239><c> some</c><00:02:32.480><c> legacy</c><00:02:32.879><c> syntax</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
noticed it was using some legacy syntax
 

00:02:33.599 --> 00:02:35.350 align:start position:0%
noticed it was using some legacy syntax
that<00:02:33.840><c> required</c><00:02:34.160><c> some</c><00:02:34.319><c> manual</c><00:02:34.720><c> debugging.</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
that required some manual debugging.
 

00:02:35.360 --> 00:02:37.110 align:start position:0%
that required some manual debugging.
Overall,<00:02:35.840><c> Grock's</c><00:02:36.239><c> coding</c><00:02:36.560><c> capability</c>

00:02:37.110 --> 00:02:37.120 align:start position:0%
Overall, Grock's coding capability
 

00:02:37.120 --> 00:02:38.869 align:start position:0%
Overall, Grock's coding capability
seemed<00:02:37.360><c> to</c><00:02:37.519><c> be</c><00:02:37.599><c> on</c><00:02:37.840><c> par</c><00:02:38.080><c> with</c><00:02:38.239><c> the</c><00:02:38.400><c> other</c><00:02:38.560><c> big</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
seemed to be on par with the other big
 

00:02:38.879 --> 00:02:41.190 align:start position:0%
seemed to be on par with the other big
models,<00:02:39.519><c> but</c><00:02:39.680><c> it's</c><00:02:39.840><c> missing</c><00:02:40.080><c> a</c><00:02:40.319><c> CLI</c><00:02:40.720><c> tool</c><00:02:41.040><c> like</c>

00:02:41.190 --> 00:02:41.200 align:start position:0%
models, but it's missing a CLI tool like
 

00:02:41.200 --> 00:02:43.190 align:start position:0%
models, but it's missing a CLI tool like
Claude<00:02:41.599><c> Code.</c><00:02:42.000><c> However,</c><00:02:42.400><c> if</c><00:02:42.560><c> it's</c><00:02:42.720><c> as</c><00:02:42.959><c> good</c><00:02:43.040><c> as</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
Claude Code. However, if it's as good as
 

00:02:43.200 --> 00:02:44.869 align:start position:0%
Claude Code. However, if it's as good as
they<00:02:43.360><c> say</c><00:02:43.440><c> it</c><00:02:43.680><c> is,</c><00:02:44.000><c> why</c><00:02:44.239><c> can't</c><00:02:44.400><c> it</c><00:02:44.560><c> just</c><00:02:44.720><c> build</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
they say it is, why can't it just build
 

00:02:44.879 --> 00:02:46.869 align:start position:0%
they say it is, why can't it just build
its<00:02:45.040><c> own</c><00:02:45.200><c> CLI</c><00:02:45.680><c> tool?</c><00:02:46.080><c> Well,</c><00:02:46.319><c> actually,</c><00:02:46.640><c> it</c>

00:02:46.869 --> 00:02:46.879 align:start position:0%
its own CLI tool? Well, actually, it
 

00:02:46.879 --> 00:02:48.470 align:start position:0%
its own CLI tool? Well, actually, it
can,<00:02:47.120><c> and</c><00:02:47.360><c> that's</c><00:02:47.599><c> exactly</c><00:02:47.920><c> what</c><00:02:48.080><c> this</c><00:02:48.239><c> guy</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
can, and that's exactly what this guy
 

00:02:48.480 --> 00:02:50.150 align:start position:0%
can, and that's exactly what this guy
did.<00:02:48.720><c> If</c><00:02:48.879><c> we're</c><00:02:49.040><c> truly</c><00:02:49.360><c> advancing</c><00:02:49.760><c> into</c><00:02:50.000><c> the</c>

00:02:50.150 --> 00:02:50.160 align:start position:0%
did. If we're truly advancing into the
 

00:02:50.160 --> 00:02:52.309 align:start position:0%
did. If we're truly advancing into the
singularity,<00:02:50.959><c> AI</c><00:02:51.360><c> can</c><00:02:51.599><c> and</c><00:02:51.920><c> should</c><00:02:52.080><c> be</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
singularity, AI can and should be
 

00:02:52.319 --> 00:02:53.750 align:start position:0%
singularity, AI can and should be
building<00:02:52.560><c> all</c><00:02:52.720><c> of</c><00:02:52.879><c> its</c><00:02:53.040><c> own</c><00:02:53.120><c> tooling</c><00:02:53.519><c> at</c><00:02:53.680><c> this</c>

00:02:53.750 --> 00:02:53.760 align:start position:0%
building all of its own tooling at this
 

00:02:53.760 --> 00:02:55.509 align:start position:0%
building all of its own tooling at this
point.<00:02:54.400><c> Pretty</c><00:02:54.640><c> cool.</c><00:02:54.959><c> But</c><00:02:55.040><c> if</c><00:02:55.200><c> you're</c><00:02:55.360><c> still</c>

00:02:55.509 --> 00:02:55.519 align:start position:0%
point. Pretty cool. But if you're still
 

00:02:55.519 --> 00:02:56.949 align:start position:0%
point. Pretty cool. But if you're still
afraid<00:02:55.760><c> to</c><00:02:55.920><c> use</c><00:02:56.000><c> it</c><00:02:56.160><c> because</c><00:02:56.319><c> of</c><00:02:56.480><c> Hitler,</c>

00:02:56.949 --> 00:02:56.959 align:start position:0%
afraid to use it because of Hitler,
 

00:02:56.959 --> 00:02:58.550 align:start position:0%
afraid to use it because of Hitler,
you'll<00:02:57.280><c> be</c><00:02:57.360><c> happy</c><00:02:57.519><c> to</c><00:02:57.680><c> know</c><00:02:57.760><c> that</c><00:02:58.000><c> Elon</c><00:02:58.400><c> said</c>

00:02:58.550 --> 00:02:58.560 align:start position:0%
you'll be happy to know that Elon said
 

00:02:58.560 --> 00:03:00.390 align:start position:0%
you'll be happy to know that Elon said
it<00:02:58.720><c> was</c><00:02:58.879><c> manipulated</c><00:02:59.440><c> into</c><00:02:59.680><c> saying</c><00:02:59.920><c> this.</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
it was manipulated into saying this.
 

00:03:00.400 --> 00:03:02.070 align:start position:0%
it was manipulated into saying this.
Maybe<00:03:00.640><c> that's</c><00:03:00.879><c> true,</c><00:03:01.200><c> maybe</c><00:03:01.440><c> not.</c><00:03:01.760><c> But</c><00:03:01.920><c> in</c>

00:03:02.070 --> 00:03:02.080 align:start position:0%
Maybe that's true, maybe not. But in
 

00:03:02.080 --> 00:03:03.990 align:start position:0%
Maybe that's true, maybe not. But in
general,<00:03:02.400><c> Grock</c><00:03:02.800><c> has</c><00:03:02.959><c> far</c><00:03:03.200><c> fewer</c><00:03:03.440><c> guard</c><00:03:03.760><c> rails</c>

00:03:03.990 --> 00:03:04.000 align:start position:0%
general, Grock has far fewer guard rails
 

00:03:04.000 --> 00:03:05.830 align:start position:0%
general, Grock has far fewer guard rails
on<00:03:04.159><c> offensive</c><00:03:04.640><c> speech</c><00:03:05.040><c> compared</c><00:03:05.360><c> to</c><00:03:05.599><c> other</c>

00:03:05.830 --> 00:03:05.840 align:start position:0%
on offensive speech compared to other
 

00:03:05.840 --> 00:03:07.509 align:start position:0%
on offensive speech compared to other
mainstream<00:03:06.319><c> models.</c><00:03:06.879><c> And</c><00:03:07.040><c> this</c><00:03:07.200><c> gives</c><00:03:07.360><c> the</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
mainstream models. And this gives the
 

00:03:07.519 --> 00:03:09.350 align:start position:0%
mainstream models. And this gives the
end<00:03:07.680><c> user</c><00:03:08.000><c> the</c><00:03:08.239><c> ability</c><00:03:08.560><c> to</c><00:03:08.800><c> steer</c><00:03:09.040><c> it</c><00:03:09.200><c> in</c>

00:03:09.350 --> 00:03:09.360 align:start position:0%
end user the ability to steer it in
 

00:03:09.360 --> 00:03:10.790 align:start position:0%
end user the ability to steer it in
unique<00:03:09.680><c> ways</c><00:03:09.920><c> that</c><00:03:10.159><c> some</c><00:03:10.400><c> might</c><00:03:10.560><c> find</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
unique ways that some might find
 

00:03:10.800 --> 00:03:12.790 align:start position:0%
unique ways that some might find
offensive.<00:03:11.440><c> AI</c><00:03:11.840><c> is</c><00:03:12.000><c> now</c><00:03:12.159><c> writing</c><00:03:12.400><c> more</c><00:03:12.560><c> of</c><00:03:12.640><c> our</c>

00:03:12.790 --> 00:03:12.800 align:start position:0%
offensive. AI is now writing more of our
 

00:03:12.800 --> 00:03:14.390 align:start position:0%
offensive. AI is now writing more of our
code<00:03:12.959><c> than</c><00:03:13.200><c> ever.</c><00:03:13.519><c> But</c><00:03:13.680><c> according</c><00:03:14.000><c> to</c><00:03:14.159><c> a</c>

00:03:14.390 --> 00:03:14.400 align:start position:0%
code than ever. But according to a
 

00:03:14.400 --> 00:03:16.390 align:start position:0%
code than ever. But according to a
recent<00:03:14.720><c> Microsoft</c><00:03:15.280><c> study,</c><00:03:15.760><c> it</c><00:03:16.000><c> still</c><00:03:16.159><c> sucks</c>

00:03:16.390 --> 00:03:16.400 align:start position:0%
recent Microsoft study, it still sucks
 

00:03:16.400 --> 00:03:17.990 align:start position:0%
recent Microsoft study, it still sucks
at<00:03:16.640><c> debugging,</c><00:03:17.280><c> which</c><00:03:17.440><c> is</c><00:03:17.519><c> why</c><00:03:17.680><c> you</c><00:03:17.840><c> should</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
at debugging, which is why you should
 

00:03:18.000 --> 00:03:19.990 align:start position:0%
at debugging, which is why you should
check<00:03:18.080><c> out</c><00:03:18.319><c> Sentry,</c><00:03:18.879><c> the</c><00:03:19.120><c> sponsor</c><00:03:19.440><c> of</c><00:03:19.599><c> today's</c>

00:03:19.990 --> 00:03:20.000 align:start position:0%
check out Sentry, the sponsor of today's
 

00:03:20.000 --> 00:03:21.830 align:start position:0%
check out Sentry, the sponsor of today's
video.<00:03:20.560><c> They</c><00:03:20.720><c> just</c><00:03:20.879><c> launched</c><00:03:21.120><c> a</c><00:03:21.280><c> new</c><00:03:21.440><c> AI</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
video. They just launched a new AI
 

00:03:21.840 --> 00:03:23.830 align:start position:0%
video. They just launched a new AI
debugging<00:03:22.319><c> agent</c><00:03:22.640><c> called</c><00:03:22.959><c> Seir,</c><00:03:23.519><c> which</c>

00:03:23.830 --> 00:03:23.840 align:start position:0%
debugging agent called Seir, which
 

00:03:23.840 --> 00:03:25.830 align:start position:0%
debugging agent called Seir, which
developers<00:03:24.319><c> claim</c><00:03:24.640><c> is</c><00:03:24.879><c> actually</c><00:03:25.280><c> good</c><00:03:25.519><c> and</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
developers claim is actually good and
 

00:03:25.840 --> 00:03:27.910 align:start position:0%
developers claim is actually good and
capable<00:03:26.159><c> of</c><00:03:26.319><c> fixing</c><00:03:26.720><c> complex</c><00:03:27.200><c> issues</c><00:03:27.519><c> in</c><00:03:27.760><c> one</c>

00:03:27.910 --> 00:03:27.920 align:start position:0%
capable of fixing complex issues in one
 

00:03:27.920 --> 00:03:29.830 align:start position:0%
capable of fixing complex issues in one
shot.<00:03:28.400><c> That's</c><00:03:28.640><c> because</c><00:03:28.879><c> unlike</c><00:03:29.280><c> other</c><00:03:29.440><c> AI</c>

00:03:29.830 --> 00:03:29.840 align:start position:0%
shot. That's because unlike other AI
 

00:03:29.840 --> 00:03:32.309 align:start position:0%
shot. That's because unlike other AI
debugging<00:03:30.239><c> tools,</c><00:03:30.879><c> Seir</c><00:03:31.280><c> can</c><00:03:31.440><c> access</c><00:03:31.760><c> all</c><00:03:32.080><c> the</c>

00:03:32.309 --> 00:03:32.319 align:start position:0%
debugging tools, Seir can access all the
 

00:03:32.319 --> 00:03:34.229 align:start position:0%
debugging tools, Seir can access all the
context<00:03:32.640><c> that</c><00:03:32.879><c> it</c><00:03:33.040><c> gets</c><00:03:33.200><c> from</c><00:03:33.360><c> your</c><00:03:33.519><c> codebase,</c>

00:03:34.229 --> 00:03:34.239 align:start position:0%
context that it gets from your codebase,
 

00:03:34.239 --> 00:03:36.869 align:start position:0%
context that it gets from your codebase,
like<00:03:34.640><c> error</c><00:03:34.799><c> data,</c><00:03:35.280><c> logs,</c><00:03:35.760><c> and</c><00:03:35.920><c> stack</c><00:03:36.239><c> traces,</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
like error data, logs, and stack traces,
 

00:03:36.879 --> 00:03:38.710 align:start position:0%
like error data, logs, and stack traces,
allowing<00:03:37.200><c> it</c><00:03:37.360><c> to</c><00:03:37.519><c> pinpoint</c><00:03:38.000><c> the</c><00:03:38.159><c> root</c><00:03:38.400><c> cause</c>

00:03:38.710 --> 00:03:38.720 align:start position:0%
allowing it to pinpoint the root cause
 

00:03:38.720 --> 00:03:41.270 align:start position:0%
allowing it to pinpoint the root cause
with<00:03:38.879><c> over</c><00:03:39.200><c> 94%</c><00:03:40.000><c> accuracy.</c><00:03:40.799><c> Using</c><00:03:41.120><c> this</c>

00:03:41.270 --> 00:03:41.280 align:start position:0%
with over 94% accuracy. Using this
 

00:03:41.280 --> 00:03:43.350 align:start position:0%
with over 94% accuracy. Using this
context,<00:03:41.840><c> it</c><00:03:42.159><c> automatically</c><00:03:42.720><c> debugs</c><00:03:43.200><c> the</c>

00:03:43.350 --> 00:03:43.360 align:start position:0%
context, it automatically debugs the
 

00:03:43.360 --> 00:03:45.190 align:start position:0%
context, it automatically debugs the
root<00:03:43.519><c> cause</c><00:03:43.760><c> of</c><00:03:43.840><c> your</c><00:03:44.080><c> issue</c><00:03:44.480><c> and</c><00:03:44.720><c> opens</c><00:03:45.040><c> a</c>

00:03:45.190 --> 00:03:45.200 align:start position:0%
root cause of your issue and opens a
 

00:03:45.200 --> 00:03:47.190 align:start position:0%
root cause of your issue and opens a
pull<00:03:45.440><c> request</c><00:03:45.760><c> with</c><00:03:45.920><c> a</c><00:03:46.080><c> fix.</c><00:03:46.480><c> Try</c><00:03:46.640><c> out</c><00:03:46.799><c> Seir</c>

00:03:47.190 --> 00:03:47.200 align:start position:0%
pull request with a fix. Try out Seir
 

00:03:47.200 --> 00:03:50.470 align:start position:0%
pull request with a fix. Try out Seir
for<00:03:47.360><c> free</c><00:03:47.519><c> today</c><00:03:47.840><c> at</c><00:03:48.159><c> century.io/fireship.</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
for free today at century.io/fireship.
 

00:03:50.480 --> 00:03:51.990 align:start position:0%
for free today at century.io/fireship.
This<00:03:50.640><c> has</c><00:03:50.720><c> been</c><00:03:50.799><c> the</c><00:03:50.959><c> code</c><00:03:51.280><c> report.</c><00:03:51.680><c> Thanks</c>

00:03:51.990 --> 00:03:52.000 align:start position:0%
This has been the code report. Thanks
 

00:03:52.000 --> 00:03:53.589 align:start position:0%
This has been the code report. Thanks
for<00:03:52.159><c> watching</c><00:03:52.560><c> and</c><00:03:52.799><c> I</c><00:03:52.959><c> will</c><00:03:53.120><c> see</c><00:03:53.200><c> you</c><00:03:53.360><c> in</c><00:03:53.519><c> the</c>

00:03:53.589 --> 00:03:53.599 align:start position:0%
for watching and I will see you in the
 

00:03:53.599 --> 00:03:55.760 align:start position:0%
for watching and I will see you in the
next

