WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.570 align:start position:0%
 
hi<00:00:00.420><c> there</c><00:00:00.659><c> my</c><00:00:01.020><c> name</c><00:00:01.140><c> is</c><00:00:01.260><c> Alex</c><00:00:01.500><c> De</c><00:00:01.800><c> micheli</c><00:00:02.399><c> I'm</c>

00:00:02.570 --> 00:00:02.580 align:start position:0%
hi there my name is <PERSON> I'm
 

00:00:02.580 --> 00:00:04.550 align:start position:0%
hi there my name is <PERSON> I'm
a<00:00:02.760><c> partner</c><00:00:03.000><c> engineer</c><00:00:03.480><c> at</c><00:00:03.659><c> GitHub</c><00:00:04.020><c> and</c><00:00:04.380><c> I'm</c>

00:00:04.550 --> 00:00:04.560 align:start position:0%
a partner engineer at GitHub and I'm
 

00:00:04.560 --> 00:00:06.110 align:start position:0%
a partner engineer at GitHub and I'm
excited<00:00:05.040><c> to</c><00:00:05.220><c> show</c><00:00:05.400><c> you</c><00:00:05.520><c> the</c><00:00:05.759><c> integration</c>

00:00:06.110 --> 00:00:06.120 align:start position:0%
excited to show you the integration
 

00:00:06.120 --> 00:00:08.150 align:start position:0%
excited to show you the integration
between<00:00:06.420><c> Atlas</c><00:00:07.020><c> and</c><00:00:07.140><c> jira</c><00:00:07.620><c> and</c><00:00:07.740><c> GitHub</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
between Atlas and jira and GitHub
 

00:00:08.160 --> 00:00:10.850 align:start position:0%
between Atlas and jira and GitHub
Advanced<00:00:08.639><c> security</c><00:00:09.019><c> for</c><00:00:10.019><c> this</c><00:00:10.260><c> demo</c><00:00:10.620><c> I'm</c>

00:00:10.850 --> 00:00:10.860 align:start position:0%
Advanced security for this demo I'm
 

00:00:10.860 --> 00:00:12.890 align:start position:0%
Advanced security for this demo I'm
going<00:00:11.040><c> to</c><00:00:11.219><c> show</c><00:00:11.400><c> you</c><00:00:11.519><c> how</c><00:00:11.940><c> to</c><00:00:12.120><c> set</c><00:00:12.420><c> up</c><00:00:12.599><c> the</c>

00:00:12.890 --> 00:00:12.900 align:start position:0%
going to show you how to set up the
 

00:00:12.900 --> 00:00:14.990 align:start position:0%
going to show you how to set up the
integration<00:00:13.440><c> and</c><00:00:13.860><c> how</c><00:00:14.099><c> to</c><00:00:14.219><c> ingest</c><00:00:14.639><c> code</c>

00:00:14.990 --> 00:00:15.000 align:start position:0%
integration and how to ingest code
 

00:00:15.000 --> 00:00:17.510 align:start position:0%
integration and how to ingest code
scanning<00:00:15.660><c> secret</c><00:00:16.260><c> scanning</c><00:00:16.920><c> and</c><00:00:17.220><c> depend</c>

00:00:17.510 --> 00:00:17.520 align:start position:0%
scanning secret scanning and depend
 

00:00:17.520 --> 00:00:21.410 align:start position:0%
scanning secret scanning and depend
about<00:00:17.699><c> alerts</c><00:00:18.359><c> from</c><00:00:18.900><c> GitHub</c><00:00:19.440><c> into</c><00:00:19.859><c> jira</c><00:00:20.880><c> I</c>

00:00:21.410 --> 00:00:21.420 align:start position:0%
about alerts from GitHub into jira I
 

00:00:21.420 --> 00:00:23.330 align:start position:0%
about alerts from GitHub into jira I
have<00:00:21.600><c> a</c><00:00:21.900><c> test</c><00:00:22.080><c> organization</c><00:00:22.740><c> with</c><00:00:23.220><c> an</c>

00:00:23.330 --> 00:00:23.340 align:start position:0%
have a test organization with an
 

00:00:23.340 --> 00:00:25.009 align:start position:0%
have a test organization with an
insecure<00:00:23.760><c> application</c><00:00:24.240><c> which</c><00:00:24.539><c> I'm</c><00:00:24.660><c> going</c><00:00:24.900><c> to</c>

00:00:25.009 --> 00:00:25.019 align:start position:0%
insecure application which I'm going to
 

00:00:25.019 --> 00:00:27.890 align:start position:0%
insecure application which I'm going to
scan<00:00:25.260><c> with</c><00:00:25.680><c> code</c><00:00:25.859><c> 2L</c><00:00:26.880><c> for</c><00:00:27.359><c> those</c><00:00:27.480><c> who</c><00:00:27.660><c> don't</c>

00:00:27.890 --> 00:00:27.900 align:start position:0%
scan with code 2L for those who don't
 

00:00:27.900 --> 00:00:30.669 align:start position:0%
scan with code 2L for those who don't
know<00:00:28.080><c> code</c><00:00:28.680><c> ql</c><00:00:29.160><c> is</c><00:00:29.340><c> a</c><00:00:29.460><c> static</c><00:00:29.820><c> analysis</c><00:00:30.359><c> engine</c>

00:00:30.669 --> 00:00:30.679 align:start position:0%
know code ql is a static analysis engine
 

00:00:30.679 --> 00:00:33.709 align:start position:0%
know code ql is a static analysis engine
developed<00:00:31.679><c> by</c><00:00:32.160><c> GitHub</c><00:00:32.640><c> to</c><00:00:32.940><c> automate</c><00:00:33.420><c> Security</c>

00:00:33.709 --> 00:00:33.719 align:start position:0%
developed by GitHub to automate Security
 

00:00:33.719 --> 00:00:35.630 align:start position:0%
developed by GitHub to automate Security
checks

00:00:35.630 --> 00:00:35.640 align:start position:0%
checks
 

00:00:35.640 --> 00:00:38.569 align:start position:0%
checks
the<00:00:36.239><c> first</c><00:00:36.360><c> step</c><00:00:36.660><c> is</c><00:00:37.079><c> to</c><00:00:37.260><c> go</c><00:00:37.500><c> to</c><00:00:37.680><c> the</c><00:00:37.980><c> atlassian</c>

00:00:38.569 --> 00:00:38.579 align:start position:0%
the first step is to go to the atlassian
 

00:00:38.579 --> 00:00:41.090 align:start position:0%
the first step is to go to the atlassian
jira<00:00:39.120><c> application</c><00:00:39.660><c> in</c><00:00:40.620><c> the</c><00:00:40.800><c> navigation</c>

00:00:41.090 --> 00:00:41.100 align:start position:0%
jira application in the navigation
 

00:00:41.100 --> 00:00:45.170 align:start position:0%
jira application in the navigation
header<00:00:41.700><c> click</c><00:00:42.120><c> on</c><00:00:42.300><c> apps</c><00:00:42.899><c> explore</c><00:00:43.860><c> more</c><00:00:44.100><c> apps</c>

00:00:45.170 --> 00:00:45.180 align:start position:0%
header click on apps explore more apps
 

00:00:45.180 --> 00:00:48.410 align:start position:0%
header click on apps explore more apps
and<00:00:45.600><c> then</c><00:00:45.719><c> search</c><00:00:46.079><c> for</c><00:00:46.379><c> GitHub</c><00:00:47.040><c> for</c><00:00:47.219><c> jira</c><00:00:48.000><c> the</c>

00:00:48.410 --> 00:00:48.420 align:start position:0%
and then search for GitHub for jira the
 

00:00:48.420 --> 00:00:50.869 align:start position:0%
and then search for GitHub for jira the
application<00:00:48.840><c> GitHub</c><00:00:49.379><c> 4G</c><00:00:49.739><c> has</c><00:00:49.980><c> been</c><00:00:50.160><c> around</c><00:00:50.340><c> 4</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
application GitHub 4G has been around 4
 

00:00:50.879 --> 00:00:53.090 align:start position:0%
application GitHub 4G has been around 4
Cent<00:00:51.059><c> I'm</c><00:00:51.300><c> already</c><00:00:51.420><c> it</c><00:00:52.079><c> allows</c><00:00:52.440><c> you</c><00:00:52.559><c> to</c><00:00:52.860><c> track</c>

00:00:53.090 --> 00:00:53.100 align:start position:0%
Cent I'm already it allows you to track
 

00:00:53.100 --> 00:00:56.869 align:start position:0%
Cent I'm already it allows you to track
branches<00:00:54.000><c> PRS</c><00:00:54.899><c> commits</c><00:00:55.680><c> and</c><00:00:55.920><c> more</c>

00:00:56.869 --> 00:00:56.879 align:start position:0%
branches PRS commits and more
 

00:00:56.879 --> 00:00:59.510 align:start position:0%
branches PRS commits and more
in<00:00:57.360><c> this</c><00:00:57.600><c> tutorial</c><00:00:57.960><c> however</c><00:00:58.379><c> we</c><00:00:59.039><c> are</c><00:00:59.219><c> going</c><00:00:59.340><c> to</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
in this tutorial however we are going to
 

00:00:59.520 --> 00:01:03.770 align:start position:0%
in this tutorial however we are going to
focus<00:00:59.820><c> on</c><00:01:00.300><c> its</c><00:01:00.780><c> new</c><00:01:00.899><c> security</c><00:01:01.260><c> feature</c>

00:01:03.770 --> 00:01:03.780 align:start position:0%
focus on its new security feature
 

00:01:03.780 --> 00:01:06.890 align:start position:0%
focus on its new security feature
we<00:01:04.320><c> are</c><00:01:04.500><c> going</c><00:01:04.680><c> to</c><00:01:04.920><c> click</c><00:01:05.159><c> on</c><00:01:05.339><c> get</c><00:01:05.760><c> started</c><00:01:06.240><c> and</c>

00:01:06.890 --> 00:01:06.900 align:start position:0%
we are going to click on get started and
 

00:01:06.900 --> 00:01:09.289 align:start position:0%
we are going to click on get started and
we<00:01:07.020><c> are</c><00:01:07.200><c> going</c><00:01:07.320><c> to</c><00:01:07.500><c> hit</c><00:01:07.740><c> the</c><00:01:07.979><c> button</c><00:01:08.340><c> connect</c><00:01:08.820><c> a</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
we are going to hit the button connect a
 

00:01:09.299 --> 00:01:11.870 align:start position:0%
we are going to hit the button connect a
GitHub<00:01:09.720><c> organization</c><00:01:10.320><c> we</c><00:01:11.100><c> have</c><00:01:11.280><c> the</c><00:01:11.520><c> option</c>

00:01:11.870 --> 00:01:11.880 align:start position:0%
GitHub organization we have the option
 

00:01:11.880 --> 00:01:14.030 align:start position:0%
GitHub organization we have the option
to<00:01:12.180><c> connect</c><00:01:12.360><c> a</c><00:01:12.659><c> GitHub</c><00:01:13.080><c> server</c><00:01:13.439><c> but</c><00:01:13.680><c> I'm</c><00:01:13.860><c> going</c>

00:01:14.030 --> 00:01:14.040 align:start position:0%
to connect a GitHub server but I'm going
 

00:01:14.040 --> 00:01:17.690 align:start position:0%
to connect a GitHub server but I'm going
to<00:01:14.220><c> choose</c><00:01:14.520><c> connect</c><00:01:14.939><c> GitHub</c><00:01:15.720><c> cloud</c>

00:01:17.690 --> 00:01:17.700 align:start position:0%
to choose connect GitHub cloud
 

00:01:17.700 --> 00:01:19.490 align:start position:0%
to choose connect GitHub cloud
follow<00:01:18.240><c> the</c><00:01:18.540><c> steps</c><00:01:18.840><c> to</c><00:01:19.020><c> connect</c><00:01:19.260><c> the</c>

00:01:19.490 --> 00:01:19.500 align:start position:0%
follow the steps to connect the
 

00:01:19.500 --> 00:01:21.050 align:start position:0%
follow the steps to connect the
organization

00:01:21.050 --> 00:01:21.060 align:start position:0%
organization
 

00:01:21.060 --> 00:01:23.330 align:start position:0%
organization
to<00:01:21.659><c> confirm</c><00:01:22.080><c> that</c><00:01:22.439><c> it's</c><00:01:22.619><c> been</c><00:01:22.860><c> connected</c>

00:01:23.330 --> 00:01:23.340 align:start position:0%
to confirm that it's been connected
 

00:01:23.340 --> 00:01:27.770 align:start position:0%
to confirm that it's been connected
correctly<00:01:23.939><c> head</c><00:01:24.780><c> over</c><00:01:24.960><c> to</c><00:01:25.320><c> configure</c>

00:01:27.770 --> 00:01:27.780 align:start position:0%
correctly head over to configure
 

00:01:27.780 --> 00:01:30.109 align:start position:0%
correctly head over to configure
as<00:01:28.320><c> the</c><00:01:28.560><c> next</c><00:01:28.740><c> step</c><00:01:28.979><c> we</c><00:01:29.520><c> are</c><00:01:29.640><c> going</c><00:01:29.759><c> to</c><00:01:29.880><c> create</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
as the next step we are going to create
 

00:01:30.119 --> 00:01:31.490 align:start position:0%
as the next step we are going to create
a<00:01:30.420><c> project</c>

00:01:31.490 --> 00:01:31.500 align:start position:0%
a project
 

00:01:31.500 --> 00:01:34.490 align:start position:0%
a project
in<00:01:31.979><c> the</c><00:01:32.100><c> navigation</c><00:01:32.460><c> header</c><00:01:33.060><c> select</c><00:01:33.840><c> projects</c>

00:01:34.490 --> 00:01:34.500 align:start position:0%
in the navigation header select projects
 

00:01:34.500 --> 00:01:37.609 align:start position:0%
in the navigation header select projects
then<00:01:35.280><c> create</c><00:01:35.700><c> a</c><00:01:36.119><c> project</c><00:01:36.360><c> I'm</c><00:01:37.320><c> going</c><00:01:37.500><c> to</c>

00:01:37.609 --> 00:01:37.619 align:start position:0%
then create a project I'm going to
 

00:01:37.619 --> 00:01:39.950 align:start position:0%
then create a project I'm going to
choose<00:01:37.799><c> backtracking</c><00:01:38.700><c> template</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
choose backtracking template
 

00:01:39.960 --> 00:01:43.130 align:start position:0%
choose backtracking template
once<00:01:40.619><c> the</c><00:01:40.740><c> project</c><00:01:40.979><c> has</c><00:01:41.520><c> been</c><00:01:41.759><c> created</c><00:01:42.180><c> go</c>

00:01:43.130 --> 00:01:43.140 align:start position:0%
once the project has been created go
 

00:01:43.140 --> 00:01:45.350 align:start position:0%
once the project has been created go
back<00:01:43.320><c> to</c><00:01:43.799><c> the</c><00:01:44.040><c> project</c><00:01:44.220><c> settings</c><00:01:44.579><c> and</c><00:01:45.000><c> select</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
back to the project settings and select
 

00:01:45.360 --> 00:01:47.810 align:start position:0%
back to the project settings and select
features<00:01:45.780><c> make</c><00:01:46.259><c> sure</c><00:01:46.439><c> to</c><00:01:46.680><c> activate</c><00:01:46.979><c> the</c>

00:01:47.810 --> 00:01:47.820 align:start position:0%
features make sure to activate the
 

00:01:47.820 --> 00:01:50.510 align:start position:0%
features make sure to activate the
security<00:01:48.000><c> feature</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
security feature
 

00:01:50.520 --> 00:01:53.090 align:start position:0%
security feature
once<00:01:51.240><c> the</c><00:01:51.360><c> security</c><00:01:51.600><c> feature</c><00:01:52.259><c> is</c><00:01:52.619><c> activated</c>

00:01:53.090 --> 00:01:53.100 align:start position:0%
once the security feature is activated
 

00:01:53.100 --> 00:01:56.389 align:start position:0%
once the security feature is activated
you<00:01:53.759><c> should</c><00:01:53.939><c> see</c><00:01:54.180><c> a</c><00:01:54.479><c> new</c><00:01:54.600><c> tab</c><00:01:54.899><c> Security</c><00:01:55.439><c> in</c><00:01:56.220><c> the</c>

00:01:56.389 --> 00:01:56.399 align:start position:0%
you should see a new tab Security in the
 

00:01:56.399 --> 00:01:57.649 align:start position:0%
you should see a new tab Security in the
site<00:01:56.520><c> navigation</c>

00:01:57.649 --> 00:01:57.659 align:start position:0%
site navigation
 

00:01:57.659 --> 00:02:00.889 align:start position:0%
site navigation
we're<00:01:58.200><c> going</c><00:01:58.439><c> to</c><00:01:58.860><c> connect</c><00:01:59.220><c> our</c><00:01:59.820><c> repository</c><00:02:00.600><c> to</c>

00:02:00.889 --> 00:02:00.899 align:start position:0%
we're going to connect our repository to
 

00:02:00.899 --> 00:02:01.910 align:start position:0%
we're going to connect our repository to
the<00:02:01.079><c> project</c>

00:02:01.910 --> 00:02:01.920 align:start position:0%
the project
 

00:02:01.920 --> 00:02:04.069 align:start position:0%
the project
follow<00:02:02.340><c> the</c><00:02:02.640><c> steps</c><00:02:02.880><c> to</c><00:02:03.180><c> connect</c><00:02:03.479><c> security</c>

00:02:04.069 --> 00:02:04.079 align:start position:0%
follow the steps to connect security
 

00:02:04.079 --> 00:02:06.649 align:start position:0%
follow the steps to connect security
containers<00:02:04.920><c> you</c><00:02:05.520><c> can</c><00:02:05.640><c> add</c><00:02:05.820><c> as</c><00:02:06.060><c> many</c><00:02:06.240><c> as</c><00:02:06.479><c> you</c>

00:02:06.649 --> 00:02:06.659 align:start position:0%
containers you can add as many as you
 

00:02:06.659 --> 00:02:09.050 align:start position:0%
containers you can add as many as you
need<00:02:06.920><c> containers</c><00:02:07.920><c> really</c><00:02:08.160><c> represent</c><00:02:08.819><c> your</c>

00:02:09.050 --> 00:02:09.060 align:start position:0%
need containers really represent your
 

00:02:09.060 --> 00:02:11.930 align:start position:0%
need containers really represent your
repositories

00:02:11.930 --> 00:02:11.940 align:start position:0%
 
 

00:02:11.940 --> 00:02:14.089 align:start position:0%
 
when<00:02:12.480><c> this</c><00:02:12.720><c> is</c><00:02:12.840><c> done</c><00:02:13.140><c> we're</c><00:02:13.500><c> going</c><00:02:13.739><c> to</c><00:02:13.860><c> create</c>

00:02:14.089 --> 00:02:14.099 align:start position:0%
when this is done we're going to create
 

00:02:14.099 --> 00:02:15.470 align:start position:0%
when this is done we're going to create
an<00:02:14.340><c> automation</c>

00:02:15.470 --> 00:02:15.480 align:start position:0%
an automation
 

00:02:15.480 --> 00:02:18.050 align:start position:0%
an automation
click<00:02:16.020><c> on</c><00:02:16.140><c> the</c><00:02:16.319><c> little</c><00:02:16.440><c> funder</c><00:02:17.040><c> icon</c><00:02:17.459><c> you</c><00:02:17.940><c> can</c>

00:02:18.050 --> 00:02:18.060 align:start position:0%
click on the little funder icon you can
 

00:02:18.060 --> 00:02:20.690 align:start position:0%
click on the little funder icon you can
create<00:02:18.300><c> your</c><00:02:18.780><c> own</c><00:02:18.959><c> automation</c><00:02:19.739><c> however</c><00:02:20.160><c> for</c>

00:02:20.690 --> 00:02:20.700 align:start position:0%
create your own automation however for
 

00:02:20.700 --> 00:02:23.210 align:start position:0%
create your own automation however for
this<00:02:20.879><c> tutorial</c><00:02:21.300><c> I'm</c><00:02:21.599><c> going</c><00:02:21.840><c> to</c><00:02:22.020><c> select</c><00:02:22.680><c> the</c>

00:02:23.210 --> 00:02:23.220 align:start position:0%
this tutorial I'm going to select the
 

00:02:23.220 --> 00:02:27.710 align:start position:0%
this tutorial I'm going to select the
first<00:02:23.520><c> recommended</c><00:02:24.239><c> option</c>

00:02:27.710 --> 00:02:27.720 align:start position:0%
 
 

00:02:27.720 --> 00:02:30.589 align:start position:0%
 
I<00:02:28.319><c> want</c><00:02:28.440><c> to</c><00:02:28.620><c> create</c><00:02:28.800><c> an</c><00:02:29.099><c> issue</c><00:02:29.459><c> in</c><00:02:29.580><c> jira</c><00:02:30.060><c> when</c><00:02:30.360><c> a</c>

00:02:30.589 --> 00:02:30.599 align:start position:0%
I want to create an issue in jira when a
 

00:02:30.599 --> 00:02:33.290 align:start position:0%
I want to create an issue in jira when a
critical<00:02:30.959><c> high</c><00:02:31.680><c> medium</c><00:02:32.220><c> or</c><00:02:32.760><c> low</c>

00:02:33.290 --> 00:02:33.300 align:start position:0%
critical high medium or low
 

00:02:33.300 --> 00:02:36.110 align:start position:0%
critical high medium or low
vulnerability<00:02:34.140><c> is</c><00:02:34.800><c> found</c>

00:02:36.110 --> 00:02:36.120 align:start position:0%
vulnerability is found
 

00:02:36.120 --> 00:02:38.869 align:start position:0%
vulnerability is found
to<00:02:36.720><c> add</c><00:02:36.900><c> more</c><00:02:37.140><c> options</c><00:02:37.560><c> I</c><00:02:38.099><c> can</c><00:02:38.220><c> simply</c><00:02:38.520><c> select</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
to add more options I can simply select
 

00:02:38.879 --> 00:02:40.130 align:start position:0%
to add more options I can simply select
them<00:02:39.060><c> from</c><00:02:39.300><c> the</c><00:02:39.420><c> menu</c>

00:02:40.130 --> 00:02:40.140 align:start position:0%
them from the menu
 

00:02:40.140 --> 00:02:42.650 align:start position:0%
them from the menu
to<00:02:40.739><c> finish</c><00:02:40.920><c> we</c><00:02:41.519><c> are</c><00:02:41.700><c> turning</c><00:02:42.060><c> the</c><00:02:42.180><c> automation</c>

00:02:42.650 --> 00:02:42.660 align:start position:0%
to finish we are turning the automation
 

00:02:42.660 --> 00:02:44.210 align:start position:0%
to finish we are turning the automation
on

00:02:44.210 --> 00:02:44.220 align:start position:0%
on
 

00:02:44.220 --> 00:02:47.150 align:start position:0%
on
back<00:02:44.700><c> to</c><00:02:44.940><c> GitHub</c><00:02:45.300><c> I'm</c><00:02:45.540><c> going</c><00:02:45.720><c> to</c><00:02:46.019><c> actions</c><00:02:46.860><c> and</c>

00:02:47.150 --> 00:02:47.160 align:start position:0%
back to GitHub I'm going to actions and
 

00:02:47.160 --> 00:02:49.670 align:start position:0%
back to GitHub I'm going to actions and
enable<00:02:47.519><c> code</c><00:02:47.940><c> ql</c><00:02:48.420><c> workflow</c>

00:02:49.670 --> 00:02:49.680 align:start position:0%
enable code ql workflow
 

00:02:49.680 --> 00:02:52.550 align:start position:0%
enable code ql workflow
once<00:02:50.400><c> the</c><00:02:50.519><c> workflow</c><00:02:51.000><c> finishes</c><00:02:51.360><c> to</c><00:02:51.599><c> run</c><00:02:51.780><c> we</c>

00:02:52.550 --> 00:02:52.560 align:start position:0%
once the workflow finishes to run we
 

00:02:52.560 --> 00:02:54.949 align:start position:0%
once the workflow finishes to run we
should<00:02:52.739><c> see</c><00:02:52.980><c> a</c><00:02:53.220><c> few</c><00:02:53.340><c> vulnerabilities</c><00:02:54.120><c> within</c>

00:02:54.949 --> 00:02:54.959 align:start position:0%
should see a few vulnerabilities within
 

00:02:54.959 --> 00:03:01.430 align:start position:0%
should see a few vulnerabilities within
the<00:02:55.140><c> security</c><00:02:55.319><c> panel</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
 
 

00:03:01.440 --> 00:03:04.009 align:start position:0%
 
if<00:03:01.920><c> we</c><00:03:02.040><c> go</c><00:03:02.220><c> back</c><00:03:02.400><c> to</c><00:03:02.580><c> jira</c><00:03:03.120><c> we</c><00:03:03.480><c> should</c><00:03:03.599><c> see</c><00:03:03.840><c> in</c>

00:03:04.009 --> 00:03:04.019 align:start position:0%
if we go back to jira we should see in
 

00:03:04.019 --> 00:03:05.809 align:start position:0%
if we go back to jira we should see in
our<00:03:04.200><c> list</c><00:03:04.440><c> of</c><00:03:04.620><c> vulnerabilities</c><00:03:05.220><c> that</c><00:03:05.640><c> have</c>

00:03:05.809 --> 00:03:05.819 align:start position:0%
our list of vulnerabilities that have
 

00:03:05.819 --> 00:03:08.509 align:start position:0%
our list of vulnerabilities that have
been<00:03:06.000><c> imported</c>

00:03:08.509 --> 00:03:08.519 align:start position:0%
been imported
 

00:03:08.519 --> 00:03:10.850 align:start position:0%
been imported
you<00:03:09.120><c> can</c><00:03:09.180><c> click</c><00:03:09.420><c> on</c><00:03:09.599><c> a</c><00:03:09.720><c> vulnerability</c><00:03:10.319><c> title</c>

00:03:10.850 --> 00:03:10.860 align:start position:0%
you can click on a vulnerability title
 

00:03:10.860 --> 00:03:13.309 align:start position:0%
you can click on a vulnerability title
to<00:03:11.099><c> get</c><00:03:11.280><c> routed</c><00:03:11.760><c> to</c><00:03:12.000><c> GitHub</c><00:03:12.420><c> and</c><00:03:12.840><c> see</c><00:03:13.019><c> more</c>

00:03:13.309 --> 00:03:13.319 align:start position:0%
to get routed to GitHub and see more
 

00:03:13.319 --> 00:03:16.070 align:start position:0%
to get routed to GitHub and see more
information<00:03:13.500><c> about</c><00:03:14.040><c> that</c><00:03:14.340><c> vulnerability</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
information about that vulnerability
 

00:03:16.080 --> 00:03:17.750 align:start position:0%
information about that vulnerability
you<00:03:16.560><c> can</c><00:03:16.620><c> also</c><00:03:16.860><c> see</c><00:03:16.980><c> additional</c><00:03:17.459><c> information</c>

00:03:17.750 --> 00:03:17.760 align:start position:0%
you can also see additional information
 

00:03:17.760 --> 00:03:20.270 align:start position:0%
you can also see additional information
about<00:03:18.180><c> the</c><00:03:18.420><c> identifiers</c>

00:03:20.270 --> 00:03:20.280 align:start position:0%
about the identifiers
 

00:03:20.280 --> 00:03:22.250 align:start position:0%
about the identifiers
and<00:03:20.700><c> you</c><00:03:20.879><c> can</c><00:03:21.000><c> take</c><00:03:21.120><c> additional</c><00:03:21.599><c> actions</c><00:03:22.080><c> on</c>

00:03:22.250 --> 00:03:22.260 align:start position:0%
and you can take additional actions on
 

00:03:22.260 --> 00:03:24.949 align:start position:0%
and you can take additional actions on
it<00:03:22.860><c> in</c><00:03:23.340><c> this</c><00:03:23.459><c> brief</c><00:03:23.760><c> demo</c><00:03:24.120><c> we</c><00:03:24.360><c> have</c><00:03:24.480><c> seen</c><00:03:24.659><c> how</c>

00:03:24.949 --> 00:03:24.959 align:start position:0%
it in this brief demo we have seen how
 

00:03:24.959 --> 00:03:27.710 align:start position:0%
it in this brief demo we have seen how
to<00:03:25.140><c> ingest</c><00:03:25.500><c> alerts</c><00:03:26.159><c> from</c><00:03:26.400><c> GitHub</c><00:03:26.879><c> to</c><00:03:27.120><c> Atlas</c><00:03:27.540><c> in</c>

00:03:27.710 --> 00:03:27.720 align:start position:0%
to ingest alerts from GitHub to Atlas in
 

00:03:27.720 --> 00:03:29.630 align:start position:0%
to ingest alerts from GitHub to Atlas in
jira<00:03:28.140><c> so</c><00:03:28.440><c> I</c><00:03:28.620><c> wanted</c><00:03:28.739><c> to</c><00:03:29.040><c> thank</c><00:03:29.159><c> you</c><00:03:29.340><c> for</c>

00:03:29.630 --> 00:03:29.640 align:start position:0%
jira so I wanted to thank you for
 

00:03:29.640 --> 00:03:31.430 align:start position:0%
jira so I wanted to thank you for
watching<00:03:30.120><c> the</c><00:03:30.300><c> video</c><00:03:30.480><c> and</c><00:03:30.900><c> I'll</c><00:03:31.019><c> see</c><00:03:31.200><c> you</c><00:03:31.260><c> the</c>

00:03:31.430 --> 00:03:31.440 align:start position:0%
watching the video and I'll see you the
 

00:03:31.440 --> 00:03:33.800 align:start position:0%
watching the video and I'll see you the
next<00:03:31.560><c> time</c>

