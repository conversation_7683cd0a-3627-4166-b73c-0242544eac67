import os
import subprocess
from pydub import AudioSegment
from pytube import YouTube
from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound
import yt_dlp
import openai

"""
This script performs the following steps:

1. Imports necessary libraries for YouTube video downloading, audio processing, and transcript retrieval.
2. Defines a list of YouTube video links to be processed.
3. Sets up configuration variables.
4. Initializes a list to track failed downloads.
5. Defines utility functions for filename sanitization and video/transcript downloading.
6. Implements a fallback mechanism using OpenAI's Whisper API for transcription.
7. Iterates through the list of video links, attempting to download using multiple methods.
8. Provides progress updates and error messages throughout the process.

This V3 version includes a fallback mechanism using OpenAI's Whisper API for transcription when YouTube's transcription fails.
"""

# Define all necessary variables here
video_links = [
    #"https://www.youtube.com/watch?v=wmIKTZ3q0bU",
    "https://www.youtube.com/watch?v=_Tb97tD3iCA",
    # Add more video links here
]

base_download_folder = "D:\\1 - Youtube_Vimeo_Videos\\Baringa\\"
download_video = "y"
download_transcript_flag = "n"
preferred_qualities = ["2160p", "1080p", "720p", "480p", "360p"]

failed_downloads = []

# Set your OpenAI API key here
#openai.api_key = "your_openai_api_key_here"

def sanitize_filename(filename):
    return "".join([c for c in filename if c.isalpha() or c.isdigit() or c in (' ', '.', '_', '-')]).rstrip()

def download_video_pytube(url, output_path):
    try:
        yt = YouTube(url)
        video_stream = yt.streams.get_highest_resolution()
        video_stream.download(output_path=output_path)
        return True
    except Exception as e:
        print(f"Pytube download failed: {str(e)}")
        return False

def download_video_ytdlp(url, output_path):
    ydl_opts = {
        'format': 'bestvideo[height<=2160][ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'merge_output_format': 'mp4',
        'postprocessors': [{
            'key': 'FFmpegVideoConvertor',
            'preferedformat': 'mp4',
        }]
    }
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])
        return True
    except Exception as e:
        print(f"yt-dlp download failed: {str(e)}")
        return False

def download_audio(url, output_path):
    ydl_opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s')
    }
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])
        return True
    except Exception as e:
        print(f"Audio download failed: {str(e)}")
        return False

def transcribe_with_whisper(audio_file_path):
    try:
        with open(audio_file_path, "rb") as audio_file:
            transcript = openai.Audio.transcribe("whisper-1", audio_file)
        return transcript["text"]
    except Exception as e:
        print(f"Whisper transcription failed: {str(e)}")
        return None

def download_transcript(video_id, output_path, title, video_url):
    try:
        transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])
        transcript = " ".join([item["text"] for item in transcript_data])
        with open(os.path.join(output_path, f"{title}.md"), "w", encoding="utf-8") as f:
            f.write(transcript)
        return True
    except NoTranscriptFound:
        print(f"Transcript not found for video: {title}. Trying Whisper API...")
        if download_audio(video_url, output_path):
            audio_file_path = os.path.join(output_path, f"{title}.mp3")
            whisper_transcript = transcribe_with_whisper(audio_file_path)
            if whisper_transcript:
                with open(os.path.join(output_path, f"{title}.md"), "w", encoding="utf-8") as f:
                    f.write(whisper_transcript)
                os.remove(audio_file_path)  # Remove the temporary audio file
                return True
        return False
    except Exception as e:
        print(f"Failed to download transcript: {str(e)}")
        return False

def download_and_transcribe(video_url):
    try:
        try:
            yt_obj = YouTube(video_url)
            title = sanitize_filename(yt_obj.title)
        except Exception as pytex:
            print(f"pytube error obtaining video title, fallback using yt_dlp: {pytex}")
            with yt_dlp.YoutubeDL({}) as ydl:
                info = ydl.extract_info(video_url, download=False)
            title = sanitize_filename(info.get("title", "unknown_video"))
        download_folder = os.path.join(base_download_folder, title)
        os.makedirs(download_folder, exist_ok=True)
        
        if isinstance(download_video, str) and download_video.lower() == "y":
            print(f"Attempting to download video: {title}")
            # Try yt-dlp first as it handles 4K better, then fallback to pytube
            success = download_video_ytdlp(video_url, download_folder) or download_video_pytube(video_url, download_folder)
            if success:
                print(f"Video download complete: {title}")
            else:
                print(f"Failed to download video: {title}")
                failed_downloads.append(video_url)
        
        if isinstance(download_transcript_flag, str) and download_transcript_flag.lower() == "y":
            print(f"Attempting to download transcript for: {title}")
            # Use yt_dlp info if available to get video id if possible
            video_id = ""  # default empty
            try:
                video_id = yt_obj.video_id
            except Exception:
                with yt_dlp.YoutubeDL({}) as ydl:
                    info = ydl.extract_info(video_url, download=False)
                    video_id = info.get("id", "")
            if download_transcript(video_id, download_folder, title, video_url):
                print("Transcript download complete!")
            else:
                print(f"Failed to download transcript for: {title}")
        
        print(f"Processing complete for: {title}")
    except Exception as e:
        print(f"Failed to process video from URL: {video_url}")
        print(f"Error: {str(e)}")
        failed_downloads.append(video_url)

for download_link in video_links:
    print(f"Processing video: {download_link}")
    download_and_transcribe(download_link)

if failed_downloads:
    print("Some downloads failed.")
    print("Failed URLs:", failed_downloads)
else:
    print("All downloads completed successfully!")