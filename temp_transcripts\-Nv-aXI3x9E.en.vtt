WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:02.149 align:start position:0%
 
and<00:00:00.399><c> howdy</c><00:00:00.719><c> guys</c><00:00:00.960><c> so</c><00:00:01.120><c> in</c><00:00:01.199><c> the</c><00:00:01.280><c> last</c><00:00:01.599><c> video</c><00:00:02.000><c> we</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
and howdy guys so in the last video we
 

00:00:02.159 --> 00:00:03.590 align:start position:0%
and howdy guys so in the last video we
started<00:00:02.480><c> to</c><00:00:02.639><c> discuss</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
started to discuss
 

00:00:03.600 --> 00:00:05.670 align:start position:0%
started to discuss
um<00:00:03.919><c> a</c><00:00:04.000><c> little</c><00:00:04.240><c> bit</c><00:00:04.319><c> about</c><00:00:04.560><c> the</c><00:00:04.720><c> specs</c><00:00:05.200><c> and</c><00:00:05.440><c> uh</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
um a little bit about the specs and uh
 

00:00:05.680 --> 00:00:07.349 align:start position:0%
um a little bit about the specs and uh
we<00:00:05.920><c> started</c><00:00:06.240><c> to</c><00:00:06.319><c> look</c><00:00:06.480><c> through</c><00:00:06.640><c> the</c><00:00:06.799><c> ux</c><00:00:07.200><c> in</c>

00:00:07.349 --> 00:00:07.359 align:start position:0%
we started to look through the ux in
 

00:00:07.359 --> 00:00:08.950 align:start position:0%
we started to look through the ux in
order<00:00:07.600><c> to</c><00:00:07.759><c> describe</c><00:00:08.240><c> exactly</c><00:00:08.639><c> what</c><00:00:08.800><c> we're</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
order to describe exactly what we're
 

00:00:08.960 --> 00:00:10.790 align:start position:0%
order to describe exactly what we're
going<00:00:09.040><c> to</c><00:00:09.200><c> be</c><00:00:09.280><c> coding</c><00:00:09.599><c> within</c><00:00:09.920><c> the</c><00:00:10.080><c> proxy</c><00:00:10.559><c> app</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
going to be coding within the proxy app
 

00:00:10.800 --> 00:00:12.310 align:start position:0%
going to be coding within the proxy app
everything<00:00:11.120><c> is</c><00:00:11.200><c> done</c><00:00:11.440><c> on</c><00:00:11.519><c> the</c><00:00:11.599><c> admin</c><00:00:12.000><c> app</c><00:00:12.160><c> now</c>

00:00:12.310 --> 00:00:12.320 align:start position:0%
everything is done on the admin app now
 

00:00:12.320 --> 00:00:14.070 align:start position:0%
everything is done on the admin app now
we<00:00:12.400><c> got</c><00:00:12.559><c> to</c><00:00:12.639><c> go</c><00:00:12.719><c> into</c><00:00:12.960><c> the</c><00:00:13.040><c> proxy</c><00:00:13.519><c> app</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
we got to go into the proxy app
 

00:00:14.080 --> 00:00:16.710 align:start position:0%
we got to go into the proxy app
so<00:00:14.240><c> like</c><00:00:14.480><c> we</c><00:00:14.639><c> said</c><00:00:15.360><c> proxy</c><00:00:15.839><c> app</c><00:00:16.080><c> very</c><00:00:16.320><c> similar</c>

00:00:16.710 --> 00:00:16.720 align:start position:0%
so like we said proxy app very similar
 

00:00:16.720 --> 00:00:18.150 align:start position:0%
so like we said proxy app very similar
to<00:00:16.880><c> the</c><00:00:16.960><c> admin</c><00:00:17.359><c> app</c><00:00:17.520><c> because</c><00:00:17.760><c> we</c><00:00:17.840><c> got</c><00:00:18.000><c> the</c>

00:00:18.150 --> 00:00:18.160 align:start position:0%
to the admin app because we got the
 

00:00:18.160 --> 00:00:20.710 align:start position:0%
to the admin app because we got the
proxy<00:00:18.640><c> controllers</c><00:00:19.199><c> and</c><00:00:19.279><c> the</c><00:00:19.439><c> proxy</c><00:00:19.920><c> routes</c>

00:00:20.710 --> 00:00:20.720 align:start position:0%
proxy controllers and the proxy routes
 

00:00:20.720 --> 00:00:24.230 align:start position:0%
proxy controllers and the proxy routes
files<00:00:21.359><c> the</c><00:00:21.600><c> routes</c><00:00:22.160><c> this</c><00:00:22.400><c> is</c><00:00:22.560><c> the</c><00:00:23.680><c> uh</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
files the routes this is the uh
 

00:00:24.240 --> 00:00:26.310 align:start position:0%
files the routes this is the uh
and<00:00:24.720><c> this</c><00:00:24.960><c> is</c><00:00:25.119><c> all</c><00:00:25.279><c> the</c><00:00:25.439><c> proxy</c><00:00:25.840><c> routes</c><00:00:26.160><c> that</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
and this is all the proxy routes that
 

00:00:26.320 --> 00:00:27.750 align:start position:0%
and this is all the proxy routes that
are<00:00:26.400><c> related</c><00:00:26.720><c> to</c><00:00:26.880><c> the</c><00:00:27.039><c> blog</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
are related to the blog
 

00:00:27.760 --> 00:00:30.710 align:start position:0%
are related to the blog
right<00:00:28.640><c> let's</c><00:00:28.880><c> just</c><00:00:29.039><c> open</c><00:00:29.359><c> this</c><00:00:29.599><c> up</c><00:00:30.080><c> over</c><00:00:30.400><c> here</c>

00:00:30.710 --> 00:00:30.720 align:start position:0%
right let's just open this up over here
 

00:00:30.720 --> 00:00:31.429 align:start position:0%
right let's just open this up over here
proxy

00:00:31.429 --> 00:00:31.439 align:start position:0%
proxy
 

00:00:31.439 --> 00:00:33.350 align:start position:0%
proxy
routes<00:00:31.840><c> related</c><00:00:32.239><c> to</c><00:00:32.320><c> the</c><00:00:32.480><c> blog</c><00:00:32.880><c> and</c><00:00:33.040><c> we</c><00:00:33.200><c> have</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
routes related to the blog and we have
 

00:00:33.360 --> 00:00:35.430 align:start position:0%
routes related to the blog and we have
proxy<00:00:33.840><c> controllers</c><00:00:34.559><c> also</c><00:00:34.800><c> related</c><00:00:35.200><c> to</c><00:00:35.360><c> the</c>

00:00:35.430 --> 00:00:35.440 align:start position:0%
proxy controllers also related to the
 

00:00:35.440 --> 00:00:36.310 align:start position:0%
proxy controllers also related to the
blog

00:00:36.310 --> 00:00:36.320 align:start position:0%
blog
 

00:00:36.320 --> 00:00:38.069 align:start position:0%
blog
and<00:00:36.559><c> on</c><00:00:36.640><c> the</c><00:00:36.719><c> left</c><00:00:36.880><c> side</c><00:00:37.120><c> we</c><00:00:37.280><c> have</c><00:00:37.360><c> the</c><00:00:37.520><c> actual</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
and on the left side we have the actual
 

00:00:38.079 --> 00:00:39.430 align:start position:0%
and on the left side we have the actual
features<00:00:38.559><c> so</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
features so
 

00:00:39.440 --> 00:00:41.510 align:start position:0%
features so
first<00:00:39.680><c> of</c><00:00:39.840><c> all</c><00:00:40.480><c> in</c><00:00:40.640><c> order</c><00:00:40.800><c> to</c><00:00:40.960><c> have</c><00:00:41.040><c> a</c><00:00:41.120><c> bunch</c><00:00:41.360><c> of</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
first of all in order to have a bunch of
 

00:00:41.520 --> 00:00:43.510 align:start position:0%
first of all in order to have a bunch of
small<00:00:41.840><c> wins</c><00:00:42.160><c> i</c><00:00:42.239><c> put</c><00:00:42.399><c> the</c><00:00:42.559><c> easier</c><00:00:42.960><c> features</c><00:00:43.360><c> on</c>

00:00:43.510 --> 00:00:43.520 align:start position:0%
small wins i put the easier features on
 

00:00:43.520 --> 00:00:45.670 align:start position:0%
small wins i put the easier features on
the<00:00:43.680><c> top</c><00:00:43.920><c> over</c><00:00:44.160><c> here</c><00:00:44.320><c> i</c><00:00:44.480><c> clean</c><00:00:44.719><c> it</c><00:00:44.879><c> up</c><00:00:44.960><c> a</c><00:00:45.039><c> bit</c>

00:00:45.670 --> 00:00:45.680 align:start position:0%
the top over here i clean it up a bit
 

00:00:45.680 --> 00:00:47.750 align:start position:0%
the top over here i clean it up a bit
on<00:00:45.840><c> the</c><00:00:46.000><c> post</c><00:00:46.399><c> record</c><00:00:46.719><c> in</c><00:00:46.879><c> the</c><00:00:47.200><c> okay</c><00:00:47.440><c> so</c><00:00:47.600><c> if</c>

00:00:47.750 --> 00:00:47.760 align:start position:0%
on the post record in the okay so if
 

00:00:47.760 --> 00:00:49.430 align:start position:0%
on the post record in the okay so if
moderation<00:00:48.399><c> is</c><00:00:48.559><c> required</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
moderation is required
 

00:00:49.440 --> 00:00:52.470 align:start position:0%
moderation is required
on<00:00:49.600><c> the</c><00:00:49.760><c> post</c><00:00:50.160><c> level</c><00:00:51.440><c> um</c>

00:00:52.470 --> 00:00:52.480 align:start position:0%
on the post level um
 

00:00:52.480 --> 00:00:55.510 align:start position:0%
on the post level um
then<00:00:53.440><c> uh</c><00:00:53.760><c> when</c><00:00:54.000><c> somebody</c><00:00:54.399><c> submits</c><00:00:54.800><c> a</c><00:00:54.879><c> new</c><00:00:55.120><c> post</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
then uh when somebody submits a new post
 

00:00:55.520 --> 00:00:57.670 align:start position:0%
then uh when somebody submits a new post
set<00:00:55.760><c> hidden</c><00:00:56.160><c> to</c><00:00:56.399><c> true</c><00:00:56.719><c> by</c><00:00:56.879><c> default</c><00:00:57.280><c> so</c><00:00:57.440><c> let's</c>

00:00:57.670 --> 00:00:57.680 align:start position:0%
set hidden to true by default so let's
 

00:00:57.680 --> 00:00:57.990 align:start position:0%
set hidden to true by default so let's
actually

00:00:57.990 --> 00:00:58.000 align:start position:0%
actually
 

00:00:58.000 --> 00:00:59.950 align:start position:0%
actually
go<00:00:58.160><c> into</c><00:00:58.399><c> the</c><00:00:58.480><c> create</c><00:00:58.879><c> function</c>

00:00:59.950 --> 00:00:59.960 align:start position:0%
go into the create function
 

00:00:59.960 --> 00:01:01.830 align:start position:0%
go into the create function
exports.create<00:01:00.960><c> over</c><00:01:01.199><c> here</c>

00:01:01.830 --> 00:01:01.840 align:start position:0%
exports.create over here
 

00:01:01.840 --> 00:01:03.590 align:start position:0%
exports.create over here
and<00:01:02.239><c> all</c><00:01:02.399><c> we're</c><00:01:02.559><c> going</c><00:01:02.640><c> to</c><00:01:02.800><c> do</c><00:01:03.039><c> is</c><00:01:03.280><c> we're</c><00:01:03.440><c> going</c>

00:01:03.590 --> 00:01:03.600 align:start position:0%
and all we're going to do is we're going
 

00:01:03.600 --> 00:01:05.109 align:start position:0%
and all we're going to do is we're going
to

00:01:05.109 --> 00:01:05.119 align:start position:0%
to
 

00:01:05.119 --> 00:01:07.750 align:start position:0%
to
have<00:01:05.360><c> a</c><00:01:05.519><c> look</c><00:01:05.920><c> over</c><00:01:06.159><c> here</c><00:01:06.400><c> okay</c><00:01:06.680><c> block.html</c>

00:01:07.750 --> 00:01:07.760 align:start position:0%
have a look over here okay block.html
 

00:01:07.760 --> 00:01:10.469 align:start position:0%
have a look over here okay block.html
block.save<00:01:08.720><c> okay</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
block.save okay
 

00:01:10.479 --> 00:01:14.550 align:start position:0%
block.save okay
let's<00:01:10.840><c> see</c><00:01:12.159><c> um</c><00:01:13.439><c> okay</c><00:01:13.680><c> we</c><00:01:13.840><c> need</c><00:01:14.000><c> to</c><00:01:14.080><c> actually</c>

00:01:14.550 --> 00:01:14.560 align:start position:0%
let's see um okay we need to actually
 

00:01:14.560 --> 00:01:17.990 align:start position:0%
let's see um okay we need to actually
grab<00:01:14.799><c> the</c><00:01:14.960><c> shop</c><00:01:15.280><c> record</c><00:01:15.759><c> somewhere</c><00:01:16.159><c> over</c><00:01:16.479><c> here</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
grab the shop record somewhere over here
 

00:01:18.000 --> 00:01:22.230 align:start position:0%
grab the shop record somewhere over here
okay<00:01:18.400><c> let's</c><00:01:18.560><c> see</c><00:01:18.920><c> blog.save</c><00:01:20.439><c> shop.find1</c>

00:01:22.230 --> 00:01:22.240 align:start position:0%
okay let's see blog.save shop.find1
 

00:01:22.240 --> 00:01:26.230 align:start position:0%
okay let's see blog.save shop.find1
ah<00:01:22.560><c> okay</c><00:01:23.759><c> and</c><00:01:24.159><c> shop.doc</c><00:01:25.040><c> what</c><00:01:25.200><c> does</c><00:01:25.360><c> this</c><00:01:25.520><c> do</c>

00:01:26.230 --> 00:01:26.240 align:start position:0%
ah okay and shop.doc what does this do
 

00:01:26.240 --> 00:01:28.710 align:start position:0%
ah okay and shop.doc what does this do
okay<00:01:26.720><c> post</c><00:01:27.040><c> save</c><00:01:27.360><c> successfully</c><00:01:28.080><c> block</c><00:01:28.400><c> that</c>

00:01:28.710 --> 00:01:28.720 align:start position:0%
okay post save successfully block that
 

00:01:28.720 --> 00:01:29.590 align:start position:0%
okay post save successfully block that
save

00:01:29.590 --> 00:01:29.600 align:start position:0%
save
 

00:01:29.600 --> 00:01:32.390 align:start position:0%
save
console.log<00:01:30.320><c> post</c><00:01:30.560><c> save</c><00:01:30.880><c> successfully</c><00:01:32.000><c> add</c>

00:01:32.390 --> 00:01:32.400 align:start position:0%
console.log post save successfully add
 

00:01:32.400 --> 00:01:33.030 align:start position:0%
console.log post save successfully add
shop

00:01:33.030 --> 00:01:33.040 align:start position:0%
shop
 

00:01:33.040 --> 00:01:35.910 align:start position:0%
shop
to<00:01:33.200><c> blog</c><00:01:33.759><c> record</c><00:01:34.880><c> okay</c><00:01:35.200><c> i'm</c><00:01:35.280><c> not</c><00:01:35.439><c> sure</c><00:01:35.600><c> exactly</c>

00:01:35.910 --> 00:01:35.920 align:start position:0%
to blog record okay i'm not sure exactly
 

00:01:35.920 --> 00:01:38.310 align:start position:0%
to blog record okay i'm not sure exactly
what<00:01:36.079><c> we're</c><00:01:36.240><c> doing</c><00:01:36.560><c> there</c><00:01:36.799><c> shopify</c><00:01:37.360><c> domain</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
what we're doing there shopify domain
 

00:01:38.320 --> 00:01:41.590 align:start position:0%
what we're doing there shopify domain
let's<00:01:38.640><c> store</c><00:01:39.119><c> admin</c><00:01:39.520><c> name</c><00:01:40.079><c> equals</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
let's store admin name equals
 

00:01:41.600 --> 00:01:44.710 align:start position:0%
let's store admin name equals
this<00:01:42.720><c> okay</c><00:01:43.119><c> and</c><00:01:43.280><c> app</c><00:01:43.600><c> slug</c>

00:01:44.710 --> 00:01:44.720 align:start position:0%
this okay and app slug
 

00:01:44.720 --> 00:01:47.590 align:start position:0%
this okay and app slug
if<00:01:45.040><c> shopping</c><00:01:45.600><c> and</c><00:01:45.840><c> shop</c><00:01:46.240><c> dot</c><00:01:46.560><c> underscore</c><00:01:47.119><c> doc</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
if shopping and shop dot underscore doc
 

00:01:47.600 --> 00:01:48.149 align:start position:0%
if shopping and shop dot underscore doc
okay

00:01:48.149 --> 00:01:48.159 align:start position:0%
okay
 

00:01:48.159 --> 00:01:51.350 align:start position:0%
okay
and<00:01:48.399><c> over</c><00:01:48.640><c> here</c><00:01:50.000><c> uh</c><00:01:50.560><c> i</c><00:01:50.640><c> think</c><00:01:50.799><c> this</c><00:01:51.040><c> is</c><00:01:51.200><c> the</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
and over here uh i think this is the
 

00:01:51.360 --> 00:01:52.069 align:start position:0%
and over here uh i think this is the
part

00:01:52.069 --> 00:01:52.079 align:start position:0%
part
 

00:01:52.079 --> 00:01:53.670 align:start position:0%
part
okay<00:01:52.320><c> over</c><00:01:52.479><c> here</c><00:01:52.640><c> we're</c><00:01:52.799><c> adding</c><00:01:53.119><c> the</c><00:01:53.280><c> shop</c><00:01:53.520><c> to</c>

00:01:53.670 --> 00:01:53.680 align:start position:0%
okay over here we're adding the shop to
 

00:01:53.680 --> 00:01:55.190 align:start position:0%
okay over here we're adding the shop to
the<00:01:53.840><c> shop</c><00:01:54.159><c> record</c><00:01:54.479><c> and</c><00:01:54.560><c> we're</c><00:01:54.720><c> sending</c><00:01:55.040><c> an</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
the shop record and we're sending an
 

00:01:55.200 --> 00:01:55.749 align:start position:0%
the shop record and we're sending an
email

00:01:55.749 --> 00:01:55.759 align:start position:0%
email
 

00:01:55.759 --> 00:01:59.030 align:start position:0%
email
okay<00:01:57.600><c> uh</c><00:01:57.840><c> so</c><00:01:58.079><c> within</c><00:01:58.479><c> the</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
okay uh so within the
 

00:01:59.040 --> 00:02:01.270 align:start position:0%
okay uh so within the
within<00:01:59.439><c> the</c><00:01:59.600><c> create</c><00:02:00.079><c> function</c><00:02:00.479><c> of</c><00:02:00.560><c> the</c><00:02:00.719><c> blog</c>

00:02:01.270 --> 00:02:01.280 align:start position:0%
within the create function of the blog
 

00:02:01.280 --> 00:02:02.550 align:start position:0%
within the create function of the blog
there's<00:02:01.520><c> a</c><00:02:01.600><c> couple</c><00:02:01.920><c> of</c><00:02:02.000><c> things</c><00:02:02.240><c> that</c><00:02:02.399><c> are</c>

00:02:02.550 --> 00:02:02.560 align:start position:0%
there's a couple of things that are
 

00:02:02.560 --> 00:02:03.990 align:start position:0%
there's a couple of things that are
happening<00:02:03.040><c> here</c><00:02:03.360><c> right</c>

00:02:03.990 --> 00:02:04.000 align:start position:0%
happening here right
 

00:02:04.000 --> 00:02:05.910 align:start position:0%
happening here right
the<00:02:04.159><c> first</c><00:02:04.320><c> thing</c><00:02:04.560><c> that's</c><00:02:04.719><c> happened</c><00:02:05.040><c> and</c><00:02:05.280><c> um</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
the first thing that's happened and um
 

00:02:05.920 --> 00:02:07.590 align:start position:0%
the first thing that's happened and um
this<00:02:06.079><c> is</c><00:02:06.240><c> also</c><00:02:06.479><c> a</c><00:02:06.560><c> good</c><00:02:06.799><c> review</c><00:02:07.200><c> of</c><00:02:07.360><c> what's</c>

00:02:07.590 --> 00:02:07.600 align:start position:0%
this is also a good review of what's
 

00:02:07.600 --> 00:02:09.790 align:start position:0%
this is also a good review of what's
happening<00:02:07.920><c> in</c><00:02:08.000><c> the</c><00:02:08.160><c> proxy</c><00:02:08.560><c> app</c><00:02:08.800><c> exactly</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
happening in the proxy app exactly
 

00:02:09.800 --> 00:02:11.990 align:start position:0%
happening in the proxy app exactly
exports.create<00:02:10.879><c> is</c><00:02:11.120><c> is</c><00:02:11.360><c> the</c><00:02:11.520><c> function</c><00:02:11.840><c> that</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
exports.create is is the function that
 

00:02:12.000 --> 00:02:13.910 align:start position:0%
exports.create is is the function that
runs<00:02:12.239><c> when</c><00:02:12.400><c> a</c><00:02:12.560><c> blog</c><00:02:12.879><c> is</c><00:02:12.959><c> created</c><00:02:13.599><c> okay</c>

00:02:13.910 --> 00:02:13.920 align:start position:0%
runs when a blog is created okay
 

00:02:13.920 --> 00:02:14.470 align:start position:0%
runs when a blog is created okay
whatever

00:02:14.470 --> 00:02:14.480 align:start position:0%
whatever
 

00:02:14.480 --> 00:02:16.309 align:start position:0%
whatever
it<00:02:14.560><c> does</c><00:02:14.800><c> all</c><00:02:14.959><c> this</c><00:02:15.200><c> stuff</c><00:02:15.440><c> with</c><00:02:15.599><c> the</c><00:02:15.920><c> with</c><00:02:16.160><c> the</c>

00:02:16.309 --> 00:02:16.319 align:start position:0%
it does all this stuff with the with the
 

00:02:16.319 --> 00:02:17.750 align:start position:0%
it does all this stuff with the with the
blog<00:02:16.800><c> record</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
blog record
 

00:02:17.760 --> 00:02:19.270 align:start position:0%
blog record
cleans<00:02:18.080><c> it</c><00:02:18.239><c> up</c><00:02:18.400><c> for</c><00:02:18.560><c> saving</c><00:02:18.879><c> it</c><00:02:19.040><c> in</c><00:02:19.120><c> the</c>

00:02:19.270 --> 00:02:19.280 align:start position:0%
cleans it up for saving it in the
 

00:02:19.280 --> 00:02:21.030 align:start position:0%
cleans it up for saving it in the
database<00:02:19.840><c> on</c><00:02:20.000><c> top</c><00:02:20.319><c> over</c><00:02:20.480><c> here</c><00:02:20.720><c> and</c><00:02:20.800><c> then</c><00:02:20.959><c> it</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
database on top over here and then it
 

00:02:21.040 --> 00:02:22.949 align:start position:0%
database on top over here and then it
says<00:02:21.200><c> blog.save</c>

00:02:22.949 --> 00:02:22.959 align:start position:0%
says blog.save
 

00:02:22.959 --> 00:02:25.430 align:start position:0%
says blog.save
and<00:02:23.040><c> then</c><00:02:23.280><c> after</c><00:02:23.599><c> it's</c><00:02:23.840><c> saved</c><00:02:24.560><c> it</c><00:02:24.879><c> uh</c><00:02:25.200><c> checks</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
and then after it's saved it uh checks
 

00:02:25.440 --> 00:02:26.150 align:start position:0%
and then after it's saved it uh checks
for<00:02:25.599><c> an</c><00:02:25.760><c> error</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
for an error
 

00:02:26.160 --> 00:02:28.229 align:start position:0%
for an error
if<00:02:26.319><c> there's</c><00:02:26.560><c> no</c><00:02:26.800><c> error</c><00:02:27.120><c> it</c><00:02:27.200><c> says</c><00:02:27.440><c> add</c><00:02:27.760><c> shop</c><00:02:28.080><c> to</c>

00:02:28.229 --> 00:02:28.239 align:start position:0%
if there's no error it says add shop to
 

00:02:28.239 --> 00:02:29.990 align:start position:0%
if there's no error it says add shop to
a<00:02:28.319><c> blog</c><00:02:28.720><c> record</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
a blog record
 

00:02:30.000 --> 00:02:33.270 align:start position:0%
a blog record
and<00:02:30.959><c> whatever</c><00:02:32.000><c> it's</c><00:02:32.239><c> a</c>

00:02:33.270 --> 00:02:33.280 align:start position:0%
and whatever it's a
 

00:02:33.280 --> 00:02:36.949 align:start position:0%
and whatever it's a
let's<00:02:33.519><c> see</c><00:02:34.840><c> console.log</c><00:02:36.000><c> let's</c><00:02:36.319><c> store</c><00:02:36.560><c> admin</c>

00:02:36.949 --> 00:02:36.959 align:start position:0%
let's see console.log let's store admin
 

00:02:36.959 --> 00:02:38.710 align:start position:0%
let's see console.log let's store admin
lab<00:02:37.200><c> app</c><00:02:37.519><c> slug</c>

00:02:38.710 --> 00:02:38.720 align:start position:0%
lab app slug
 

00:02:38.720 --> 00:02:41.910 align:start position:0%
lab app slug
shopping<00:02:39.280><c> blank</c><00:02:40.239><c> blog</c><00:02:40.560><c> dot</c><00:02:40.800><c> find</c><00:02:41.120><c> by</c><00:02:41.360><c> id</c><00:02:41.760><c> with</c>

00:02:41.910 --> 00:02:41.920 align:start position:0%
shopping blank blog dot find by id with
 

00:02:41.920 --> 00:02:42.790 align:start position:0%
shopping blank blog dot find by id with
the<00:02:42.239><c> shop</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
the shop
 

00:02:42.800 --> 00:02:45.509 align:start position:0%
the shop
okay<00:02:43.120><c> it</c><00:02:43.200><c> takes</c><00:02:43.599><c> the</c><00:02:43.840><c> shop</c><00:02:44.160><c> after</c><00:02:44.560><c> that</c><00:02:45.280><c> and</c><00:02:45.440><c> it</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
okay it takes the shop after that and it
 

00:02:45.519 --> 00:02:47.910 align:start position:0%
okay it takes the shop after that and it
says<00:02:45.840><c> tags</c><00:02:46.239><c> dot</c><00:02:46.560><c> for</c><00:02:46.879><c> each</c>

00:02:47.910 --> 00:02:47.920 align:start position:0%
says tags dot for each
 

00:02:47.920 --> 00:02:50.790 align:start position:0%
says tags dot for each
blog.find<00:02:48.800><c> by</c><00:02:49.040><c> id</c><00:02:49.280><c> and</c><00:02:49.599><c> update</c><00:02:50.160><c> dan</c><00:02:50.400><c> this</c><00:02:50.560><c> is</c><00:02:50.720><c> a</c>

00:02:50.790 --> 00:02:50.800 align:start position:0%
blog.find by id and update dan this is a
 

00:02:50.800 --> 00:02:52.550 align:start position:0%
blog.find by id and update dan this is a
pretty<00:02:51.040><c> big</c><00:02:51.360><c> function</c>

00:02:52.550 --> 00:02:52.560 align:start position:0%
pretty big function
 

00:02:52.560 --> 00:02:53.830 align:start position:0%
pretty big function
the<00:02:52.720><c> main</c><00:02:52.879><c> thing</c><00:02:53.040><c> that's</c><00:02:53.280><c> happening</c><00:02:53.599><c> over</c>

00:02:53.830 --> 00:02:53.840 align:start position:0%
the main thing that's happening over
 

00:02:53.840 --> 00:02:56.070 align:start position:0%
the main thing that's happening over
here<00:02:54.080><c> is</c><00:02:54.160><c> that</c><00:02:54.319><c> it's</c><00:02:54.400><c> also</c>

00:02:56.070 --> 00:02:56.080 align:start position:0%
here is that it's also
 

00:02:56.080 --> 00:02:57.990 align:start position:0%
here is that it's also
with<00:02:56.319><c> mongoose</c><00:02:56.959><c> it's</c><00:02:57.120><c> kind</c><00:02:57.280><c> of</c><00:02:57.440><c> a</c><00:02:57.519><c> pain</c><00:02:57.760><c> to</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
with mongoose it's kind of a pain to
 

00:02:58.000 --> 00:02:59.750 align:start position:0%
with mongoose it's kind of a pain to
like<00:02:58.959><c> to</c><00:02:59.200><c> save</c>

00:02:59.750 --> 00:02:59.760 align:start position:0%
like to save
 

00:02:59.760 --> 00:03:05.110 align:start position:0%
like to save
to<00:02:59.920><c> do</c><00:03:00.239><c> inner</c><00:03:00.560><c> joins</c><00:03:01.040><c> and</c><00:03:01.200><c> stuff</c><00:03:02.159><c> but</c><00:03:02.840><c> um</c><00:03:03.920><c> um</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
to do inner joins and stuff but um um
 

00:03:05.120 --> 00:03:06.710 align:start position:0%
to do inner joins and stuff but um um
what<00:03:05.280><c> it's</c><00:03:05.440><c> doing</c><00:03:05.680><c> is</c><00:03:05.840><c> it's</c><00:03:06.000><c> saving</c><00:03:06.560><c> it's</c>

00:03:06.710 --> 00:03:06.720 align:start position:0%
what it's doing is it's saving it's
 

00:03:06.720 --> 00:03:08.390 align:start position:0%
what it's doing is it's saving it's
pretty<00:03:06.959><c> much</c><00:03:07.120><c> saving</c><00:03:07.599><c> references</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
pretty much saving references
 

00:03:08.400 --> 00:03:11.750 align:start position:0%
pretty much saving references
for<00:03:09.040><c> uh</c><00:03:09.360><c> for</c><00:03:09.599><c> specific</c><00:03:10.480><c> uh</c><00:03:10.879><c> tags</c>

00:03:11.750 --> 00:03:11.760 align:start position:0%
for uh for specific uh tags
 

00:03:11.760 --> 00:03:14.630 align:start position:0%
for uh for specific uh tags
within<00:03:12.159><c> a</c><00:03:12.239><c> blog</c><00:03:13.040><c> object</c><00:03:13.440><c> so</c><00:03:13.680><c> blog</c><00:03:14.080><c> that</c><00:03:14.319><c> find</c>

00:03:14.630 --> 00:03:14.640 align:start position:0%
within a blog object so blog that find
 

00:03:14.640 --> 00:03:15.270 align:start position:0%
within a blog object so blog that find
by<00:03:14.879><c> id</c>

00:03:15.270 --> 00:03:15.280 align:start position:0%
by id
 

00:03:15.280 --> 00:03:18.470 align:start position:0%
by id
and<00:03:15.599><c> update</c><00:03:16.480><c> it</c><00:03:16.800><c> up</c><00:03:17.200><c> it</c><00:03:17.519><c> it</c><00:03:17.760><c> cycles</c><00:03:18.239><c> through</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
and update it up it it cycles through
 

00:03:18.480 --> 00:03:20.390 align:start position:0%
and update it up it it cycles through
all<00:03:18.640><c> the</c><00:03:18.800><c> different</c><00:03:19.120><c> tags</c><00:03:19.519><c> it</c><00:03:19.680><c> looks</c><00:03:19.920><c> like</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
all the different tags it looks like
 

00:03:20.400 --> 00:03:24.470 align:start position:0%
all the different tags it looks like
and<00:03:20.640><c> it</c><00:03:20.800><c> just</c><00:03:21.200><c> uh</c><00:03:21.599><c> keeps</c><00:03:22.000><c> on</c><00:03:22.879><c> uh</c><00:03:23.200><c> tags.for</c><00:03:24.000><c> each</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
and it just uh keeps on uh tags.for each
 

00:03:24.480 --> 00:03:27.190 align:start position:0%
and it just uh keeps on uh tags.for each
blog.find<00:03:25.360><c> by</c><00:03:25.599><c> id</c><00:03:25.920><c> and</c><00:03:26.159><c> update</c><00:03:26.480><c> it</c><00:03:26.720><c> saves</c><00:03:27.040><c> all</c>

00:03:27.190 --> 00:03:27.200 align:start position:0%
blog.find by id and update it saves all
 

00:03:27.200 --> 00:03:28.949 align:start position:0%
blog.find by id and update it saves all
the<00:03:27.280><c> references</c><00:03:27.840><c> to</c><00:03:28.000><c> the</c><00:03:28.159><c> tags</c><00:03:28.560><c> and</c><00:03:28.720><c> stuff</c>

00:03:28.949 --> 00:03:28.959 align:start position:0%
the references to the tags and stuff
 

00:03:28.959 --> 00:03:30.229 align:start position:0%
the references to the tags and stuff
over<00:03:29.200><c> there</c><00:03:29.519><c> alright</c><00:03:29.920><c> so</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
over there alright so
 

00:03:30.239 --> 00:03:31.750 align:start position:0%
over there alright so
but<00:03:30.400><c> we</c><00:03:30.560><c> don't</c><00:03:30.720><c> really</c><00:03:30.959><c> need</c><00:03:31.120><c> to</c><00:03:31.200><c> touch</c><00:03:31.519><c> that</c>

00:03:31.750 --> 00:03:31.760 align:start position:0%
but we don't really need to touch that
 

00:03:31.760 --> 00:03:33.589 align:start position:0%
but we don't really need to touch that
piece<00:03:32.000><c> and</c><00:03:32.080><c> it</c><00:03:32.159><c> also</c><00:03:32.400><c> sends</c><00:03:32.640><c> an</c><00:03:32.799><c> email</c>

00:03:33.589 --> 00:03:33.599 align:start position:0%
piece and it also sends an email
 

00:03:33.599 --> 00:03:35.030 align:start position:0%
piece and it also sends an email
this<00:03:33.760><c> stuff</c><00:03:34.080><c> probably</c><00:03:34.400><c> needs</c><00:03:34.640><c> to</c><00:03:34.799><c> start</c>

00:03:35.030 --> 00:03:35.040 align:start position:0%
this stuff probably needs to start
 

00:03:35.040 --> 00:03:37.270 align:start position:0%
this stuff probably needs to start
getting<00:03:35.360><c> broken</c><00:03:35.840><c> up</c><00:03:36.400><c> right</c><00:03:36.720><c> just</c><00:03:36.879><c> the</c><00:03:37.040><c> fact</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
getting broken up right just the fact
 

00:03:37.280 --> 00:03:37.910 align:start position:0%
getting broken up right just the fact
that<00:03:37.440><c> it's</c>

00:03:37.910 --> 00:03:37.920 align:start position:0%
that it's
 

00:03:37.920 --> 00:03:40.229 align:start position:0%
that it's
that<00:03:38.080><c> it's</c><00:03:38.319><c> uh</c><00:03:38.879><c> it's</c><00:03:39.040><c> incredibly</c><00:03:39.599><c> wordy</c><00:03:40.000><c> over</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
that it's uh it's incredibly wordy over
 

00:03:40.239 --> 00:03:41.589 align:start position:0%
that it's uh it's incredibly wordy over
here<00:03:40.480><c> and</c><00:03:40.560><c> that's</c><00:03:40.799><c> also</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
here and that's also
 

00:03:41.599 --> 00:03:44.229 align:start position:0%
here and that's also
a<00:03:41.760><c> good</c><00:03:42.080><c> uh</c><00:03:42.480><c> a</c><00:03:42.560><c> good</c><00:03:42.799><c> example</c><00:03:43.280><c> of</c><00:03:43.680><c> why</c><00:03:44.000><c> things</c>

00:03:44.229 --> 00:03:44.239 align:start position:0%
a good uh a good example of why things
 

00:03:44.239 --> 00:03:46.070 align:start position:0%
a good uh a good example of why things
should<00:03:44.480><c> really</c><00:03:44.720><c> be</c><00:03:44.879><c> modularized</c><00:03:45.760><c> in</c>

00:03:46.070 --> 00:03:46.080 align:start position:0%
should really be modularized in
 

00:03:46.080 --> 00:03:48.309 align:start position:0%
should really be modularized in
in<00:03:46.159><c> the</c><00:03:46.319><c> code</c><00:03:46.640><c> but</c><00:03:46.879><c> this</c><00:03:47.040><c> was</c><00:03:47.280><c> all</c><00:03:47.519><c> mvp</c><00:03:48.080><c> code</c>

00:03:48.309 --> 00:03:48.319 align:start position:0%
in the code but this was all mvp code
 

00:03:48.319 --> 00:03:49.750 align:start position:0%
in the code but this was all mvp code
you<00:03:48.400><c> got</c><00:03:48.560><c> to</c><00:03:48.640><c> understand</c><00:03:49.200><c> that</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
you got to understand that
 

00:03:49.760 --> 00:03:51.589 align:start position:0%
you got to understand that
it<00:03:49.920><c> was</c><00:03:50.239><c> i</c><00:03:50.400><c> just</c><00:03:50.560><c> needed</c><00:03:50.799><c> to</c><00:03:50.959><c> get</c><00:03:51.120><c> the</c><00:03:51.280><c> app</c><00:03:51.519><c> out</c>

00:03:51.589 --> 00:03:51.599 align:start position:0%
it was i just needed to get the app out
 

00:03:51.599 --> 00:03:53.030 align:start position:0%
it was i just needed to get the app out
there<00:03:51.760><c> in</c><00:03:51.920><c> order</c><00:03:52.080><c> to</c><00:03:52.239><c> validate</c><00:03:52.720><c> that</c><00:03:52.879><c> there</c>

00:03:53.030 --> 00:03:53.040 align:start position:0%
there in order to validate that there
 

00:03:53.040 --> 00:03:54.470 align:start position:0%
there in order to validate that there
was<00:03:53.360><c> that</c><00:03:53.519><c> there</c><00:03:53.680><c> was</c><00:03:53.920><c> a</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
was that there was a
 

00:03:54.480 --> 00:03:56.789 align:start position:0%
was that there was a
any<00:03:55.120><c> any</c><00:03:55.360><c> demand</c><00:03:55.760><c> or</c><00:03:55.920><c> any</c><00:03:56.159><c> need</c><00:03:56.400><c> for</c><00:03:56.560><c> this</c>

00:03:56.789 --> 00:03:56.799 align:start position:0%
any any demand or any need for this
 

00:03:56.799 --> 00:03:57.990 align:start position:0%
any any demand or any need for this
product<00:03:57.200><c> in</c><00:03:57.280><c> the</c><00:03:57.360><c> market</c>

00:03:57.990 --> 00:03:58.000 align:start position:0%
product in the market
 

00:03:58.000 --> 00:03:59.670 align:start position:0%
product in the market
before<00:03:58.319><c> i</c><00:03:58.480><c> went</c><00:03:58.640><c> ahead</c><00:03:58.879><c> and</c><00:03:59.040><c> modularized</c>

00:03:59.670 --> 00:03:59.680 align:start position:0%
before i went ahead and modularized
 

00:03:59.680 --> 00:04:01.350 align:start position:0%
before i went ahead and modularized
everything<00:04:00.000><c> and</c><00:04:00.159><c> cleaned</c><00:04:00.480><c> up</c><00:04:00.640><c> everything</c><00:04:01.120><c> but</c>

00:04:01.350 --> 00:04:01.360 align:start position:0%
everything and cleaned up everything but
 

00:04:01.360 --> 00:04:03.670 align:start position:0%
everything and cleaned up everything but
so<00:04:01.519><c> now</c><00:04:01.680><c> that</c><00:04:01.840><c> i'm</c><00:04:01.920><c> coming</c><00:04:02.239><c> back</c><00:04:02.400><c> to</c><00:04:02.560><c> it</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
so now that i'm coming back to it
 

00:04:03.680 --> 00:04:06.789 align:start position:0%
so now that i'm coming back to it
it's<00:04:03.920><c> kind</c><00:04:04.080><c> of</c><00:04:04.239><c> like</c><00:04:04.959><c> ah</c><00:04:05.280><c> schneids</c><00:04:05.680><c> this</c><00:04:05.920><c> is</c><00:04:06.159><c> uh</c>

00:04:06.789 --> 00:04:06.799 align:start position:0%
it's kind of like ah schneids this is uh
 

00:04:06.799 --> 00:04:08.949 align:start position:0%
it's kind of like ah schneids this is uh
this<00:04:07.040><c> is</c><00:04:07.200><c> very</c><00:04:07.439><c> wordy</c><00:04:07.840><c> it</c><00:04:07.920><c> makes</c><00:04:08.159><c> it</c><00:04:08.319><c> hard</c><00:04:08.640><c> to</c>

00:04:08.949 --> 00:04:08.959 align:start position:0%
this is very wordy it makes it hard to
 

00:04:08.959 --> 00:04:10.309 align:start position:0%
this is very wordy it makes it hard to
to<00:04:09.200><c> understand</c>

00:04:10.309 --> 00:04:10.319 align:start position:0%
to understand
 

00:04:10.319 --> 00:04:13.589 align:start position:0%
to understand
uh<00:04:10.640><c> what's</c><00:04:10.959><c> being</c><00:04:11.280><c> done</c><00:04:11.599><c> exactly</c><00:04:12.319><c> because</c><00:04:12.879><c> uh</c>

00:04:13.589 --> 00:04:13.599 align:start position:0%
uh what's being done exactly because uh
 

00:04:13.599 --> 00:04:16.469 align:start position:0%
uh what's being done exactly because uh
right<00:04:14.720><c> because</c><00:04:14.959><c> it's</c><00:04:15.120><c> just</c><00:04:15.280><c> so</c><00:04:15.519><c> wordy</c><00:04:16.079><c> within</c>

00:04:16.469 --> 00:04:16.479 align:start position:0%
right because it's just so wordy within
 

00:04:16.479 --> 00:04:17.670 align:start position:0%
right because it's just so wordy within
one<00:04:16.720><c> function</c>

00:04:17.670 --> 00:04:17.680 align:start position:0%
one function
 

00:04:17.680 --> 00:04:20.469 align:start position:0%
one function
all<00:04:17.840><c> right</c><00:04:18.079><c> but</c><00:04:18.320><c> um</c><00:04:18.799><c> but</c><00:04:19.280><c> either</c><00:04:19.600><c> way</c><00:04:20.079><c> i</c><00:04:20.239><c> think</c>

00:04:20.469 --> 00:04:20.479 align:start position:0%
all right but um but either way i think
 

00:04:20.479 --> 00:04:21.590 align:start position:0%
all right but um but either way i think
the<00:04:20.639><c> main</c><00:04:20.799><c> thing</c><00:04:20.959><c> that</c><00:04:21.120><c> we</c><00:04:21.199><c> need</c><00:04:21.359><c> to</c><00:04:21.440><c> be</c>

00:04:21.590 --> 00:04:21.600 align:start position:0%
the main thing that we need to be
 

00:04:21.600 --> 00:04:23.430 align:start position:0%
the main thing that we need to be
thinking<00:04:21.840><c> about</c><00:04:22.160><c> right</c><00:04:22.400><c> now</c><00:04:22.639><c> is</c><00:04:22.800><c> this</c>

00:04:23.430 --> 00:04:23.440 align:start position:0%
thinking about right now is this
 

00:04:23.440 --> 00:04:25.909 align:start position:0%
thinking about right now is this
is<00:04:23.600><c> this</c><00:04:23.759><c> feature</c><00:04:24.160><c> over</c><00:04:24.320><c> here</c><00:04:24.479><c> on</c><00:04:24.560><c> the</c><00:04:24.720><c> left</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
is this feature over here on the left
 

00:04:25.919 --> 00:04:27.350 align:start position:0%
is this feature over here on the left
and<00:04:26.160><c> what</c><00:04:26.320><c> does</c><00:04:26.560><c> the</c><00:04:26.639><c> feature</c><00:04:27.040><c> say</c>

00:04:27.350 --> 00:04:27.360 align:start position:0%
and what does the feature say
 

00:04:27.360 --> 00:04:30.070 align:start position:0%
and what does the feature say
it<00:04:27.440><c> says</c><00:04:28.479><c> when</c><00:04:28.720><c> the</c><00:04:28.800><c> shopper</c><00:04:29.199><c> submits</c><00:04:29.680><c> a</c><00:04:29.759><c> post</c>

00:04:30.070 --> 00:04:30.080 align:start position:0%
it says when the shopper submits a post
 

00:04:30.080 --> 00:04:32.310 align:start position:0%
it says when the shopper submits a post
check<00:04:30.320><c> the</c><00:04:30.400><c> given</c><00:04:30.720><c> shop's</c><00:04:31.120><c> post</c><00:04:31.440><c> moderation</c>

00:04:32.310 --> 00:04:32.320 align:start position:0%
check the given shop's post moderation
 

00:04:32.320 --> 00:04:33.510 align:start position:0%
check the given shop's post moderation
field

00:04:33.510 --> 00:04:33.520 align:start position:0%
field
 

00:04:33.520 --> 00:04:35.270 align:start position:0%
field
uh<00:04:33.759><c> okay</c><00:04:34.000><c> we</c><00:04:34.160><c> need</c><00:04:34.320><c> to</c><00:04:34.560><c> check</c><00:04:34.800><c> the</c><00:04:34.960><c> given</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
uh okay we need to check the given
 

00:04:35.280 --> 00:04:37.510 align:start position:0%
uh okay we need to check the given
shop's<00:04:35.680><c> post-moderation</c><00:04:36.639><c> field</c><00:04:36.880><c> so</c><00:04:37.120><c> and</c>

00:04:37.510 --> 00:04:37.520 align:start position:0%
shop's post-moderation field so and
 

00:04:37.520 --> 00:04:39.270 align:start position:0%
shop's post-moderation field so and
this<00:04:37.759><c> looks</c><00:04:38.000><c> like</c><00:04:38.160><c> a</c><00:04:38.240><c> good</c><00:04:38.400><c> opportunity</c><00:04:38.960><c> to</c><00:04:39.120><c> do</c>

00:04:39.270 --> 00:04:39.280 align:start position:0%
this looks like a good opportunity to do
 

00:04:39.280 --> 00:04:42.550 align:start position:0%
this looks like a good opportunity to do
it<00:04:39.520><c> somewhere</c><00:04:39.919><c> over</c><00:04:40.840><c> here</c><00:04:41.680><c> um</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
it somewhere over here um
 

00:04:42.560 --> 00:04:44.430 align:start position:0%
it somewhere over here um
okay<00:04:43.040><c> and</c><00:04:43.280><c> let's</c><00:04:43.440><c> go</c><00:04:43.600><c> ahead</c><00:04:43.840><c> and</c><00:04:43.919><c> do</c><00:04:44.080><c> that</c>

00:04:44.430 --> 00:04:44.440 align:start position:0%
okay and let's go ahead and do that
 

00:04:44.440 --> 00:04:45.590 align:start position:0%
okay and let's go ahead and do that
shop.find1

00:04:45.590 --> 00:04:45.600 align:start position:0%
shop.find1
 

00:04:45.600 --> 00:04:47.510 align:start position:0%
shop.find1
what<00:04:45.759><c> are</c><00:04:45.919><c> we</c><00:04:46.000><c> going</c><00:04:46.160><c> to</c><00:04:46.240><c> say</c><00:04:46.800><c> ah</c><00:04:47.040><c> and</c><00:04:47.120><c> there's</c>

00:04:47.510 --> 00:04:47.520 align:start position:0%
what are we going to say ah and there's
 

00:04:47.520 --> 00:04:48.790 align:start position:0%
what are we going to say ah and there's
there's<00:04:47.759><c> that</c><00:04:48.000><c> shop</c><00:04:48.320><c> over</c><00:04:48.479><c> there</c>

00:04:48.790 --> 00:04:48.800 align:start position:0%
there's that shop over there
 

00:04:48.800 --> 00:04:52.550 align:start position:0%
there's that shop over there
okay<00:04:49.759><c> oh</c><00:04:50.400><c> schneids</c><00:04:50.880><c> okay</c><00:04:51.120><c> the</c><00:04:52.080><c> also</c><00:04:52.320><c> has</c>

00:04:52.550 --> 00:04:52.560 align:start position:0%
okay oh schneids okay the also has
 

00:04:52.560 --> 00:04:53.710 align:start position:0%
okay oh schneids okay the also has
this<00:04:52.720><c> really</c><00:04:52.960><c> weird</c><00:04:53.199><c> thing</c><00:04:53.440><c> with</c>

00:04:53.710 --> 00:04:53.720 align:start position:0%
this really weird thing with
 

00:04:53.720 --> 00:04:55.990 align:start position:0%
this really weird thing with
shop.underscore<00:04:54.840><c> doc</c>

00:04:55.990 --> 00:04:56.000 align:start position:0%
shop.underscore doc
 

00:04:56.000 --> 00:04:58.790 align:start position:0%
shop.underscore doc
okay<00:04:56.720><c> and</c><00:04:57.440><c> so</c><00:04:57.600><c> we</c><00:04:57.759><c> actually</c><00:04:58.160><c> need</c><00:04:58.320><c> to</c><00:04:58.400><c> do</c><00:04:58.560><c> this</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
okay and so we actually need to do this
 

00:04:58.800 --> 00:05:00.790 align:start position:0%
okay and so we actually need to do this
test<00:04:59.199><c> too</c>

00:05:00.790 --> 00:05:00.800 align:start position:0%
test too
 

00:05:00.800 --> 00:05:02.950 align:start position:0%
test too
uh<00:05:01.120><c> so</c><00:05:01.280><c> let's</c><00:05:01.440><c> go</c><00:05:01.600><c> ahead</c><00:05:01.840><c> and</c><00:05:02.160><c> and</c><00:05:02.400><c> check</c><00:05:02.639><c> that</c>

00:05:02.950 --> 00:05:02.960 align:start position:0%
uh so let's go ahead and and check that
 

00:05:02.960 --> 00:05:04.550 align:start position:0%
uh so let's go ahead and and check that
shop<00:05:03.280><c> in</c><00:05:03.520><c> function</c><00:05:04.000><c> to</c><00:05:04.160><c> send</c>

00:05:04.550 --> 00:05:04.560 align:start position:0%
shop in function to send
 

00:05:04.560 --> 00:05:08.950 align:start position:0%
shop in function to send
email<00:05:05.039><c> alert</c><00:05:05.440><c> and</c><00:05:05.680><c> add</c><00:05:06.000><c> shop</c><00:05:06.560><c> reference</c>

00:05:08.950 --> 00:05:08.960 align:start position:0%
email alert and add shop reference
 

00:05:08.960 --> 00:05:11.110 align:start position:0%
email alert and add shop reference
uh<00:05:09.199><c> okay</c><00:05:09.600><c> this</c><00:05:09.919><c> is</c><00:05:10.000><c> kind</c><00:05:10.160><c> of</c><00:05:10.320><c> descriptive</c><00:05:10.960><c> we</c>

00:05:11.110 --> 00:05:11.120 align:start position:0%
uh okay this is kind of descriptive we
 

00:05:11.120 --> 00:05:12.469 align:start position:0%
uh okay this is kind of descriptive we
have<00:05:11.280><c> a</c><00:05:11.360><c> comment</c><00:05:11.840><c> in</c><00:05:12.000><c> here</c>

00:05:12.469 --> 00:05:12.479 align:start position:0%
have a comment in here
 

00:05:12.479 --> 00:05:17.590 align:start position:0%
have a comment in here
send<00:05:12.880><c> email</c><00:05:13.280><c> alert</c><00:05:14.720><c> um</c><00:05:15.440><c> okay</c>

00:05:17.590 --> 00:05:17.600 align:start position:0%
send email alert um okay
 

00:05:17.600 --> 00:05:21.029 align:start position:0%
send email alert um okay
well<00:05:17.840><c> we</c><00:05:18.080><c> we're</c><00:05:18.320><c> doing</c><00:05:18.639><c> something</c><00:05:19.039><c> else</c><00:05:20.000><c> um</c>

00:05:21.029 --> 00:05:21.039 align:start position:0%
well we we're doing something else um
 

00:05:21.039 --> 00:05:24.630 align:start position:0%
well we we're doing something else um
ah<00:05:21.440><c> okay</c><00:05:21.759><c> whatever</c><00:05:22.160><c> good</c><00:05:22.840><c> enough</c><00:05:24.000><c> let's</c><00:05:24.240><c> see</c>

00:05:24.630 --> 00:05:24.640 align:start position:0%
ah okay whatever good enough let's see
 

00:05:24.640 --> 00:05:26.469 align:start position:0%
ah okay whatever good enough let's see
[Music]

00:05:26.469 --> 00:05:26.479 align:start position:0%
[Music]
 

00:05:26.479 --> 00:05:31.670 align:start position:0%
[Music]
what<00:05:26.639><c> do</c><00:05:26.800><c> we</c><00:05:26.960><c> actually</c><00:05:27.360><c> want</c><00:05:27.520><c> to</c><00:05:27.680><c> do</c>

00:05:31.670 --> 00:05:31.680 align:start position:0%
 
 

00:05:31.680 --> 00:05:34.469 align:start position:0%
 
okay<00:05:32.240><c> well</c><00:05:32.400><c> we</c><00:05:32.560><c> have</c><00:05:32.639><c> the</c><00:05:32.880><c> shop</c><00:05:33.120><c> here</c><00:05:34.000><c> ideally</c>

00:05:34.469 --> 00:05:34.479 align:start position:0%
okay well we have the shop here ideally
 

00:05:34.479 --> 00:05:36.830 align:start position:0%
okay well we have the shop here ideally
the<00:05:34.639><c> shop</c><00:05:34.880><c> should</c><00:05:35.039><c> be</c><00:05:35.199><c> somewhere</c><00:05:35.600><c> else</c><00:05:35.840><c> but</c>

00:05:36.830 --> 00:05:36.840 align:start position:0%
the shop should be somewhere else but
 

00:05:36.840 --> 00:05:42.710 align:start position:0%
the shop should be somewhere else but
whatever<00:05:38.720><c> shop</c><00:05:39.199><c> and</c><00:05:39.440><c> shop</c>

00:05:42.710 --> 00:05:42.720 align:start position:0%
 
 

00:05:42.720 --> 00:05:50.830 align:start position:0%
 
okay<00:05:43.199><c> wait</c><00:05:43.520><c> i'm</c><00:05:43.600><c> gonna</c><00:05:43.759><c> do</c><00:05:43.919><c> an</c><00:05:44.080><c> if</c><00:05:44.320><c> block</c><00:05:44.639><c> over</c>

00:05:50.830 --> 00:05:50.840 align:start position:0%
 
 

00:05:50.840 --> 00:05:52.710 align:start position:0%
 
here<00:05:51.919><c> okay</c>

00:05:52.710 --> 00:05:52.720 align:start position:0%
here okay
 

00:05:52.720 --> 00:05:55.830 align:start position:0%
here okay
um<00:05:54.560><c> you</c><00:05:54.639><c> know</c><00:05:54.800><c> what</c><00:05:54.960><c> we</c><00:05:55.120><c> should</c><00:05:55.280><c> really</c><00:05:55.600><c> save</c>

00:05:55.830 --> 00:05:55.840 align:start position:0%
um you know what we should really save
 

00:05:55.840 --> 00:05:56.309 align:start position:0%
um you know what we should really save
this

00:05:56.309 --> 00:05:56.319 align:start position:0%
this
 

00:05:56.319 --> 00:05:58.390 align:start position:0%
this
this<00:05:56.639><c> under</c><00:05:57.039><c> shopped</c><00:05:57.360><c> underscore</c><00:05:58.160><c> dock</c>

00:05:58.390 --> 00:05:58.400 align:start position:0%
this under shopped underscore dock
 

00:05:58.400 --> 00:06:00.390 align:start position:0%
this under shopped underscore dock
somewhere<00:05:58.880><c> else</c><00:05:59.120><c> but</c>

00:06:00.390 --> 00:06:00.400 align:start position:0%
somewhere else but
 

00:06:00.400 --> 00:06:04.469 align:start position:0%
somewhere else but
okay

00:06:04.469 --> 00:06:04.479 align:start position:0%
 
 

00:06:04.479 --> 00:06:06.070 align:start position:0%
 
so<00:06:04.800><c> it's</c><00:06:04.880><c> so</c><00:06:05.120><c> important</c><00:06:05.440><c> to</c><00:06:05.600><c> keep</c><00:06:05.840><c> code</c>

00:06:06.070 --> 00:06:06.080 align:start position:0%
so it's so important to keep code
 

00:06:06.080 --> 00:06:07.670 align:start position:0%
so it's so important to keep code
modular<00:06:06.560><c> it</c><00:06:06.639><c> makes</c><00:06:06.880><c> it</c><00:06:06.960><c> so</c><00:06:07.120><c> hard</c><00:06:07.280><c> to</c><00:06:07.440><c> reason</c>

00:06:07.670 --> 00:06:07.680 align:start position:0%
modular it makes it so hard to reason
 

00:06:07.680 --> 00:06:09.670 align:start position:0%
modular it makes it so hard to reason
about<00:06:08.000><c> it</c><00:06:08.080><c> otherwise</c>

00:06:09.670 --> 00:06:09.680 align:start position:0%
about it otherwise
 

00:06:09.680 --> 00:06:11.830 align:start position:0%
about it otherwise
shop<00:06:10.000><c> and</c><00:06:10.160><c> shop</c><00:06:10.319><c> down</c><00:06:10.560><c> underscore</c><00:06:11.120><c> dock</c><00:06:11.680><c> and</c>

00:06:11.830 --> 00:06:11.840 align:start position:0%
shop and shop down underscore dock and
 

00:06:11.840 --> 00:06:12.790 align:start position:0%
shop and shop down underscore dock and
shop<00:06:12.000><c> that</c><00:06:12.240><c> underscore</c>

00:06:12.790 --> 00:06:12.800 align:start position:0%
shop that underscore
 

00:06:12.800 --> 00:06:15.350 align:start position:0%
shop that underscore
dock<00:06:14.000><c> see</c><00:06:14.240><c> while</c><00:06:14.479><c> i'm</c><00:06:14.560><c> trying</c><00:06:14.800><c> to</c><00:06:14.880><c> figure</c><00:06:15.199><c> out</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
dock see while i'm trying to figure out
 

00:06:15.360 --> 00:06:17.990 align:start position:0%
dock see while i'm trying to figure out
what<00:06:15.600><c> i'm</c><00:06:15.759><c> doing</c><00:06:16.000><c> here</c><00:06:16.319><c> i'm</c><00:06:16.560><c> already</c>

00:06:17.990 --> 00:06:18.000 align:start position:0%
what i'm doing here i'm already
 

00:06:18.000 --> 00:06:19.350 align:start position:0%
what i'm doing here i'm already
confused<00:06:18.400><c> about</c><00:06:18.560><c> what</c><00:06:18.720><c> needs</c><00:06:18.960><c> to</c><00:06:19.039><c> be</c><00:06:19.120><c> done</c>

00:06:19.350 --> 00:06:19.360 align:start position:0%
confused about what needs to be done
 

00:06:19.360 --> 00:06:21.510 align:start position:0%
confused about what needs to be done
next

00:06:21.510 --> 00:06:21.520 align:start position:0%
next
 

00:06:21.520 --> 00:06:23.510 align:start position:0%
next
let's<00:06:21.759><c> see</c><00:06:22.319><c> if</c><00:06:22.479><c> moderation</c><00:06:23.039><c> is</c><00:06:23.120><c> required</c><00:06:23.440><c> on</c>

00:06:23.510 --> 00:06:23.520 align:start position:0%
let's see if moderation is required on
 

00:06:23.520 --> 00:06:25.029 align:start position:0%
let's see if moderation is required on
the<00:06:23.680><c> post</c><00:06:23.919><c> level</c><00:06:24.160><c> on</c><00:06:24.240><c> the</c><00:06:24.319><c> post</c><00:06:24.560><c> record</c><00:06:24.880><c> in</c><00:06:24.960><c> the</c>

00:06:25.029 --> 00:06:25.039 align:start position:0%
the post level on the post record in the
 

00:06:25.039 --> 00:06:26.629 align:start position:0%
the post level on the post record in the
database<00:06:25.520><c> that</c><00:06:25.680><c> hit</c><00:06:25.919><c> into</c><00:06:26.240><c> true</c>

00:06:26.629 --> 00:06:26.639 align:start position:0%
database that hit into true
 

00:06:26.639 --> 00:06:29.749 align:start position:0%
database that hit into true
once<00:06:26.960><c> that</c><00:06:27.199><c> guys</c>

00:06:29.749 --> 00:06:29.759 align:start position:0%
 
 

00:06:29.759 --> 00:06:33.270 align:start position:0%
 
hey<00:06:30.000><c> guys</c><00:06:30.319><c> so</c><00:06:30.560><c> after</c><00:06:31.120><c> reviewing</c><00:06:32.240><c> reviewing</c><00:06:32.880><c> uh</c>

00:06:33.270 --> 00:06:33.280 align:start position:0%
hey guys so after reviewing reviewing uh
 

00:06:33.280 --> 00:06:35.189 align:start position:0%
hey guys so after reviewing reviewing uh
just<00:06:33.520><c> the</c><00:06:34.000><c> the</c><00:06:34.160><c> app</c><00:06:34.400><c> a</c><00:06:34.479><c> little</c><00:06:34.720><c> bit</c><00:06:34.880><c> in</c><00:06:35.039><c> the</c>

00:06:35.189 --> 00:06:35.199 align:start position:0%
just the the app a little bit in the
 

00:06:35.199 --> 00:06:37.110 align:start position:0%
just the the app a little bit in the
features<00:06:35.840><c> i</c><00:06:36.000><c> noticed</c><00:06:36.400><c> that</c>

00:06:37.110 --> 00:06:37.120 align:start position:0%
features i noticed that
 

00:06:37.120 --> 00:06:38.950 align:start position:0%
features i noticed that
there's<00:06:37.520><c> also</c><00:06:38.000><c> something</c><00:06:38.400><c> happening</c><00:06:38.880><c> the</c>

00:06:38.950 --> 00:06:38.960 align:start position:0%
there's also something happening the
 

00:06:38.960 --> 00:06:40.390 align:start position:0%
there's also something happening the
moment<00:06:39.280><c> that</c><00:06:39.440><c> a</c><00:06:39.600><c> post</c><00:06:40.080><c> is</c>

00:06:40.390 --> 00:06:40.400 align:start position:0%
moment that a post is
 

00:06:40.400 --> 00:06:43.990 align:start position:0%
moment that a post is
submitted<00:06:41.680><c> um</c><00:06:42.639><c> it</c><00:06:42.880><c> sends</c><00:06:43.120><c> a</c><00:06:43.280><c> message</c><00:06:43.680><c> to</c><00:06:43.840><c> the</c>

00:06:43.990 --> 00:06:44.000 align:start position:0%
submitted um it sends a message to the
 

00:06:44.000 --> 00:06:45.110 align:start position:0%
submitted um it sends a message to the
store<00:06:44.319><c> admin</c>

00:06:45.110 --> 00:06:45.120 align:start position:0%
store admin
 

00:06:45.120 --> 00:06:47.510 align:start position:0%
store admin
saying<00:06:45.440><c> your</c><00:06:45.680><c> post</c><00:06:46.000><c> is</c><00:06:46.160><c> pending</c><00:06:46.880><c> pending</c><00:06:47.280><c> your</c>

00:06:47.510 --> 00:06:47.520 align:start position:0%
saying your post is pending pending your
 

00:06:47.520 --> 00:06:48.950 align:start position:0%
saying your post is pending pending your
review<00:06:48.080><c> here</c>

00:06:48.950 --> 00:06:48.960 align:start position:0%
review here
 

00:06:48.960 --> 00:06:51.350 align:start position:0%
review here
and<00:06:49.440><c> with</c><00:06:49.599><c> that</c><00:06:50.000><c> and</c><00:06:50.240><c> uh</c><00:06:50.560><c> what</c><00:06:50.720><c> that</c><00:06:50.880><c> means</c><00:06:51.120><c> is</c>

00:06:51.350 --> 00:06:51.360 align:start position:0%
and with that and uh what that means is
 

00:06:51.360 --> 00:06:53.589 align:start position:0%
and with that and uh what that means is
um<00:06:51.919><c> in</c><00:06:52.080><c> order</c><00:06:52.240><c> to</c><00:06:52.400><c> get</c><00:06:52.560><c> this</c><00:06:52.800><c> auto</c><00:06:53.120><c> approve</c>

00:06:53.589 --> 00:06:53.599 align:start position:0%
um in order to get this auto approve
 

00:06:53.599 --> 00:06:55.510 align:start position:0%
um in order to get this auto approve
feature<00:06:53.919><c> to</c><00:06:54.080><c> work</c><00:06:54.319><c> we</c><00:06:54.479><c> need</c><00:06:54.560><c> to</c><00:06:55.039><c> think</c><00:06:55.280><c> about</c>

00:06:55.510 --> 00:06:55.520 align:start position:0%
feature to work we need to think about
 

00:06:55.520 --> 00:06:57.270 align:start position:0%
feature to work we need to think about
this<00:06:55.759><c> over</c><00:06:55.919><c> here</c><00:06:56.160><c> if</c><00:06:56.319><c> moderation</c><00:06:56.960><c> is</c><00:06:57.120><c> not</c>

00:06:57.270 --> 00:06:57.280 align:start position:0%
this over here if moderation is not
 

00:06:57.280 --> 00:06:58.790 align:start position:0%
this over here if moderation is not
required<00:06:57.680><c> on</c><00:06:57.759><c> the</c><00:06:57.840><c> post</c><00:06:58.160><c> level</c>

00:06:58.790 --> 00:06:58.800 align:start position:0%
required on the post level
 

00:06:58.800 --> 00:07:01.029 align:start position:0%
required on the post level
we<00:06:58.960><c> need</c><00:06:59.120><c> to</c><00:06:59.360><c> change</c><00:07:00.000><c> the</c><00:07:00.319><c> content</c><00:07:00.800><c> of</c><00:07:00.880><c> that</c>

00:07:01.029 --> 00:07:01.039 align:start position:0%
we need to change the content of that
 

00:07:01.039 --> 00:07:02.309 align:start position:0%
we need to change the content of that
email<00:07:01.360><c> that</c><00:07:01.440><c> we</c><00:07:01.599><c> send</c><00:07:01.840><c> the</c><00:07:01.919><c> sta</c>

00:07:02.309 --> 00:07:02.319 align:start position:0%
email that we send the sta
 

00:07:02.319 --> 00:07:03.830 align:start position:0%
email that we send the sta
that<00:07:02.479><c> we</c><00:07:02.639><c> send</c><00:07:02.880><c> the</c><00:07:02.960><c> store</c><00:07:03.199><c> admin</c><00:07:03.599><c> it</c><00:07:03.680><c> should</c>

00:07:03.830 --> 00:07:03.840 align:start position:0%
that we send the store admin it should
 

00:07:03.840 --> 00:07:05.830 align:start position:0%
that we send the store admin it should
say<00:07:04.080><c> send</c><00:07:04.400><c> message</c><00:07:04.720><c> to</c><00:07:04.800><c> the</c><00:07:04.960><c> store</c><00:07:05.199><c> admin</c>

00:07:05.830 --> 00:07:05.840 align:start position:0%
say send message to the store admin
 

00:07:05.840 --> 00:07:08.710 align:start position:0%
say send message to the store admin
which<00:07:06.080><c> is</c><00:07:06.319><c> post</c><00:07:06.639><c> is</c><00:07:06.800><c> live</c><00:07:07.280><c> here</c><00:07:07.680><c> all</c><00:07:07.840><c> right</c><00:07:08.479><c> uh</c>

00:07:08.710 --> 00:07:08.720 align:start position:0%
which is post is live here all right uh
 

00:07:08.720 --> 00:07:10.150 align:start position:0%
which is post is live here all right uh
so<00:07:08.880><c> these</c><00:07:09.120><c> are</c><00:07:09.280><c> kind</c><00:07:09.440><c> of</c><00:07:09.520><c> the</c><00:07:09.680><c> features</c><00:07:10.000><c> that</c>

00:07:10.150 --> 00:07:10.160 align:start position:0%
so these are kind of the features that
 

00:07:10.160 --> 00:07:10.790 align:start position:0%
so these are kind of the features that
need<00:07:10.319><c> to</c><00:07:10.400><c> be</c>

00:07:10.790 --> 00:07:10.800 align:start position:0%
need to be
 

00:07:10.800 --> 00:07:13.189 align:start position:0%
need to be
that<00:07:10.960><c> need</c><00:07:11.120><c> to</c><00:07:11.280><c> be</c><00:07:11.440><c> worked</c><00:07:11.840><c> out</c><00:07:12.479><c> um</c><00:07:12.960><c> but</c><00:07:13.120><c> you</c>

00:07:13.189 --> 00:07:13.199 align:start position:0%
that need to be worked out um but you
 

00:07:13.199 --> 00:07:14.629 align:start position:0%
that need to be worked out um but you
know<00:07:13.360><c> what</c><00:07:13.599><c> it's</c><00:07:13.759><c> already</c><00:07:14.160><c> starting</c><00:07:14.400><c> to</c><00:07:14.479><c> get</c>

00:07:14.629 --> 00:07:14.639 align:start position:0%
know what it's already starting to get
 

00:07:14.639 --> 00:07:16.150 align:start position:0%
know what it's already starting to get
incredibly<00:07:15.120><c> incredibly</c><00:07:15.520><c> messy</c>

00:07:16.150 --> 00:07:16.160 align:start position:0%
incredibly incredibly messy
 

00:07:16.160 --> 00:07:17.510 align:start position:0%
incredibly incredibly messy
i<00:07:16.240><c> think</c><00:07:16.479><c> in</c><00:07:16.560><c> the</c><00:07:16.639><c> next</c><00:07:16.880><c> video</c><00:07:17.120><c> we're</c><00:07:17.280><c> going</c><00:07:17.360><c> to</c>

00:07:17.510 --> 00:07:17.520 align:start position:0%
i think in the next video we're going to
 

00:07:17.520 --> 00:07:20.070 align:start position:0%
i think in the next video we're going to
start<00:07:17.919><c> to</c><00:07:18.240><c> uh</c><00:07:18.560><c> just</c><00:07:18.800><c> modularize</c><00:07:19.680><c> this</c><00:07:19.919><c> that</c>

00:07:20.070 --> 00:07:20.080 align:start position:0%
start to uh just modularize this that
 

00:07:20.080 --> 00:07:21.749 align:start position:0%
start to uh just modularize this that
way<00:07:20.240><c> it'll</c><00:07:20.479><c> be</c><00:07:20.639><c> much</c><00:07:20.800><c> easier</c><00:07:21.120><c> for</c><00:07:21.280><c> us</c><00:07:21.360><c> to</c><00:07:21.520><c> view</c>

00:07:21.749 --> 00:07:21.759 align:start position:0%
way it'll be much easier for us to view
 

00:07:21.759 --> 00:07:22.870 align:start position:0%
way it'll be much easier for us to view
the<00:07:21.919><c> code</c><00:07:22.160><c> alright</c><00:07:22.479><c> guys</c>

00:07:22.870 --> 00:07:22.880 align:start position:0%
the code alright guys
 

00:07:22.880 --> 00:07:24.309 align:start position:0%
the code alright guys
so<00:07:23.039><c> thanks</c><00:07:23.280><c> for</c><00:07:23.360><c> tuning</c><00:07:23.680><c> in</c><00:07:23.840><c> if</c><00:07:23.919><c> you</c><00:07:24.000><c> have</c><00:07:24.080><c> any</c>

00:07:24.309 --> 00:07:24.319 align:start position:0%
so thanks for tuning in if you have any
 

00:07:24.319 --> 00:07:26.230 align:start position:0%
so thanks for tuning in if you have any
questions<00:07:24.720><c> i'll</c><00:07:24.880><c> see</c><00:07:25.039><c> you</c><00:07:25.840><c> feel</c><00:07:26.000><c> free</c><00:07:26.160><c> to</c>

00:07:26.230 --> 00:07:26.240 align:start position:0%
questions i'll see you feel free to
 

00:07:26.240 --> 00:07:27.749 align:start position:0%
questions i'll see you feel free to
reach<00:07:26.479><c> out</c><00:07:26.560><c> and</c><00:07:26.720><c> i'll</c><00:07:26.800><c> see</c><00:07:26.960><c> you</c><00:07:27.199><c> in</c><00:07:27.360><c> the</c><00:07:27.440><c> next</c>

00:07:27.749 --> 00:07:27.759 align:start position:0%
reach out and i'll see you in the next
 

00:07:27.759 --> 00:07:30.240 align:start position:0%
reach out and i'll see you in the next
one

