WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.270 align:start position:0%
 
hey<00:00:00.719><c> there</c><00:00:00.900><c> it's</c><00:00:01.319><c> Matt</c><00:00:01.620><c> the</c><00:00:01.920><c> Techno</c>

00:00:02.270 --> 00:00:02.280 align:start position:0%
hey there it's Matt the Techno
 

00:00:02.280 --> 00:00:04.309 align:start position:0%
hey there it's Matt the Techno
evangelist<00:00:02.760><c> in</c><00:00:03.179><c> this</c><00:00:03.300><c> video</c><00:00:03.480><c> I</c><00:00:03.899><c> want</c><00:00:04.020><c> to</c><00:00:04.140><c> show</c>

00:00:04.309 --> 00:00:04.319 align:start position:0%
evangelist in this video I want to show
 

00:00:04.319 --> 00:00:07.249 align:start position:0%
evangelist in this video I want to show
you<00:00:04.440><c> a</c><00:00:04.799><c> component</c><00:00:05.279><c> of</c><00:00:05.819><c> a</c><00:00:06.000><c> workflow</c><00:00:06.420><c> that</c><00:00:06.899><c> I</c><00:00:07.080><c> use</c>

00:00:07.249 --> 00:00:07.259 align:start position:0%
you a component of a workflow that I use
 

00:00:07.259 --> 00:00:09.110 align:start position:0%
you a component of a workflow that I use
most<00:00:07.560><c> days</c><00:00:07.799><c> in</c><00:00:08.220><c> the</c><00:00:08.340><c> process</c><00:00:08.639><c> of</c><00:00:08.880><c> making</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
most days in the process of making
 

00:00:09.120 --> 00:00:10.129 align:start position:0%
most days in the process of making
videos

00:00:10.129 --> 00:00:10.139 align:start position:0%
videos
 

00:00:10.139 --> 00:00:12.350 align:start position:0%
videos
I'll<00:00:10.679><c> talk</c><00:00:10.860><c> about</c><00:00:10.980><c> the</c><00:00:11.340><c> whole</c><00:00:11.519><c> workflow</c><00:00:12.059><c> in</c>

00:00:12.350 --> 00:00:12.360 align:start position:0%
I'll talk about the whole workflow in
 

00:00:12.360 --> 00:00:14.209 align:start position:0%
I'll talk about the whole workflow in
another<00:00:12.480><c> video</c><00:00:12.780><c> but</c><00:00:13.139><c> part</c><00:00:13.500><c> of</c><00:00:13.620><c> it</c><00:00:13.740><c> requires</c>

00:00:14.209 --> 00:00:14.219 align:start position:0%
another video but part of it requires
 

00:00:14.219 --> 00:00:16.670 align:start position:0%
another video but part of it requires
that<00:00:14.400><c> I</c><00:00:14.519><c> mount</c><00:00:14.700><c> the</c><00:00:14.940><c> SSD</c><00:00:15.420><c> attached</c><00:00:16.020><c> to</c><00:00:16.139><c> my</c><00:00:16.320><c> A10</c>

00:00:16.670 --> 00:00:16.680 align:start position:0%
that I mount the SSD attached to my A10
 

00:00:16.680 --> 00:00:19.490 align:start position:0%
that I mount the SSD attached to my A10
mini<00:00:16.980><c> to</c><00:00:17.640><c> the</c><00:00:17.820><c> MacBook</c><00:00:18.180><c> Pro</c><00:00:18.480><c> where</c><00:00:18.779><c> I</c><00:00:19.020><c> edit</c><00:00:19.320><c> my</c>

00:00:19.490 --> 00:00:19.500 align:start position:0%
mini to the MacBook Pro where I edit my
 

00:00:19.500 --> 00:00:20.570 align:start position:0%
mini to the MacBook Pro where I edit my
videos

00:00:20.570 --> 00:00:20.580 align:start position:0%
videos
 

00:00:20.580 --> 00:00:23.029 align:start position:0%
videos
to<00:00:21.119><c> get</c><00:00:21.300><c> this</c><00:00:21.480><c> drive</c><00:00:21.720><c> mounted</c><00:00:22.380><c> automatically</c>

00:00:23.029 --> 00:00:23.039 align:start position:0%
to get this drive mounted automatically
 

00:00:23.039 --> 00:00:25.790 align:start position:0%
to get this drive mounted automatically
I'm<00:00:23.939><c> using</c><00:00:24.300><c> a</c><00:00:24.720><c> few</c><00:00:24.840><c> different</c><00:00:25.140><c> components</c>

00:00:25.790 --> 00:00:25.800 align:start position:0%
I'm using a few different components
 

00:00:25.800 --> 00:00:28.189 align:start position:0%
I'm using a few different components
first<00:00:26.580><c> there</c><00:00:26.880><c> is</c><00:00:27.060><c> cloud</c><00:00:27.240><c> monitor</c><00:00:27.779><c> Cloud</c>

00:00:28.189 --> 00:00:28.199 align:start position:0%
first there is cloud monitor Cloud
 

00:00:28.199 --> 00:00:30.470 align:start position:0%
first there is cloud monitor Cloud
monitor<00:00:28.680><c> is</c><00:00:28.859><c> a</c><00:00:29.039><c> really</c><00:00:29.160><c> cool</c><00:00:29.400><c> tool</c><00:00:29.820><c> that</c><00:00:30.180><c> comes</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
monitor is a really cool tool that comes
 

00:00:30.480 --> 00:00:32.930 align:start position:0%
monitor is a really cool tool that comes
in<00:00:30.539><c> the</c><00:00:30.720><c> setup</c><00:00:31.019><c> subscription</c><00:00:31.740><c> it</c><00:00:32.579><c> allows</c><00:00:32.880><c> you</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
in the setup subscription it allows you
 

00:00:32.940 --> 00:00:35.030 align:start position:0%
in the setup subscription it allows you
to<00:00:33.120><c> mount</c><00:00:33.300><c> drives</c><00:00:33.840><c> to</c><00:00:34.020><c> your</c><00:00:34.140><c> Mac</c><00:00:34.320><c> that</c><00:00:34.920><c> are</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
to mount drives to your Mac that are
 

00:00:35.040 --> 00:00:37.130 align:start position:0%
to mount drives to your Mac that are
normally<00:00:35.340><c> available</c><00:00:35.579><c> on</c><00:00:35.940><c> S3</c><00:00:36.360><c> or</c><00:00:36.600><c> Google</c><00:00:36.899><c> drive</c>

00:00:37.130 --> 00:00:37.140 align:start position:0%
normally available on S3 or Google drive
 

00:00:37.140 --> 00:00:40.610 align:start position:0%
normally available on S3 or Google drive
or<00:00:37.500><c> OneDrive</c><00:00:38.040><c> or</c><00:00:38.340><c> FTP</c><00:00:39.059><c> webdav</c><00:00:39.780><c> and</c><00:00:39.960><c> a</c><00:00:40.379><c> bunch</c><00:00:40.500><c> of</c>

00:00:40.610 --> 00:00:40.620 align:start position:0%
or OneDrive or FTP webdav and a bunch of
 

00:00:40.620 --> 00:00:43.490 align:start position:0%
or OneDrive or FTP webdav and a bunch of
others<00:00:41.399><c> the</c><00:00:42.059><c> next</c><00:00:42.239><c> key</c><00:00:42.480><c> component</c><00:00:42.899><c> is</c><00:00:43.140><c> Hammer</c>

00:00:43.490 --> 00:00:43.500 align:start position:0%
others the next key component is Hammer
 

00:00:43.500 --> 00:00:45.830 align:start position:0%
others the next key component is Hammer
spoon<00:00:43.739><c> I've</c><00:00:44.520><c> talked</c><00:00:44.760><c> about</c><00:00:44.879><c> Hammer</c><00:00:45.300><c> spoon</c><00:00:45.540><c> in</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
spoon I've talked about Hammer spoon in
 

00:00:45.840 --> 00:00:47.869 align:start position:0%
spoon I've talked about Hammer spoon in
a<00:00:45.960><c> previous</c><00:00:46.079><c> video</c><00:00:46.440><c> but</c><00:00:46.800><c> essentially</c><00:00:47.340><c> it's</c><00:00:47.579><c> a</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
a previous video but essentially it's a
 

00:00:47.879 --> 00:00:49.729 align:start position:0%
a previous video but essentially it's a
Mac<00:00:48.059><c> automation</c><00:00:48.660><c> platform</c><00:00:49.140><c> that</c><00:00:49.379><c> leverages</c>

00:00:49.729 --> 00:00:49.739 align:start position:0%
Mac automation platform that leverages
 

00:00:49.739 --> 00:00:52.850 align:start position:0%
Mac automation platform that leverages
Lua<00:00:50.420><c> Lua</c><00:00:51.420><c> is</c><00:00:51.660><c> a</c><00:00:51.840><c> language</c><00:00:51.960><c> that</c><00:00:52.260><c> was</c><00:00:52.440><c> made</c><00:00:52.620><c> to</c>

00:00:52.850 --> 00:00:52.860 align:start position:0%
Lua Lua is a language that was made to
 

00:00:52.860 --> 00:00:54.889 align:start position:0%
Lua Lua is a language that was made to
be<00:00:53.039><c> an</c><00:00:53.280><c> embedded</c><00:00:53.700><c> language</c><00:00:53.940><c> often</c><00:00:54.600><c> in</c><00:00:54.719><c> games</c>

00:00:54.889 --> 00:00:54.899 align:start position:0%
be an embedded language often in games
 

00:00:54.899 --> 00:00:57.650 align:start position:0%
be an embedded language often in games
and<00:00:55.320><c> it's</c><00:00:55.559><c> super</c><00:00:55.860><c> easy</c><00:00:56.160><c> to</c><00:00:56.520><c> learn</c><00:00:56.699><c> check</c><00:00:57.539><c> out</c>

00:00:57.650 --> 00:00:57.660 align:start position:0%
and it's super easy to learn check out
 

00:00:57.660 --> 00:01:00.229 align:start position:0%
and it's super easy to learn check out
this<00:00:57.899><c> page</c><00:00:58.199><c> on</c><00:00:58.559><c> the</c><00:00:58.860><c> learn</c><00:00:59.039><c> X</c><00:00:59.399><c> and</c><00:00:59.699><c> Y</c><00:00:59.879><c> minute</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
this page on the learn X and Y minute
 

00:01:00.239 --> 00:01:03.349 align:start position:0%
this page on the learn X and Y minute
site<00:01:00.719><c> to</c><00:01:01.260><c> see</c><00:01:01.440><c> how</c><00:01:01.739><c> easy</c><00:01:01.980><c> it</c><00:01:02.460><c> can</c><00:01:02.579><c> be</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
site to see how easy it can be
 

00:01:03.359 --> 00:01:05.690 align:start position:0%
site to see how easy it can be
this<00:01:03.840><c> script</c><00:01:04.080><c> started</c><00:01:04.739><c> with</c><00:01:05.159><c> a</c><00:01:05.280><c> prompt</c><00:01:05.580><c> to</c>

00:01:05.690 --> 00:01:05.700 align:start position:0%
this script started with a prompt to
 

00:01:05.700 --> 00:01:08.630 align:start position:0%
this script started with a prompt to
chat<00:01:05.880><c> gbt</c><00:01:06.540><c> chat</c><00:01:07.260><c> GPT</c><00:01:07.619><c> is</c><00:01:07.979><c> great</c><00:01:08.159><c> at</c><00:01:08.400><c> making</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
chat gbt chat GPT is great at making
 

00:01:08.640 --> 00:01:10.730 align:start position:0%
chat gbt chat GPT is great at making
yourself<00:01:08.880><c> you</c><00:01:09.240><c> know</c><00:01:09.420><c> 30</c><00:01:09.900><c> to</c><00:01:10.140><c> 50</c><00:01:10.380><c> percent</c>

00:01:10.730 --> 00:01:10.740 align:start position:0%
yourself you know 30 to 50 percent
 

00:01:10.740 --> 00:01:13.130 align:start position:0%
yourself you know 30 to 50 percent
better<00:01:11.100><c> and</c><00:01:11.580><c> got</c><00:01:11.760><c> me</c><00:01:12.000><c> started</c><00:01:12.479><c> in</c><00:01:12.900><c> the</c><00:01:13.020><c> right</c>

00:01:13.130 --> 00:01:13.140 align:start position:0%
better and got me started in the right
 

00:01:13.140 --> 00:01:15.530 align:start position:0%
better and got me started in the right
direction<00:01:13.520><c> and</c><00:01:14.520><c> then</c><00:01:14.640><c> I</c><00:01:14.880><c> was</c><00:01:15.000><c> able</c><00:01:15.240><c> to</c><00:01:15.360><c> take</c>

00:01:15.530 --> 00:01:15.540 align:start position:0%
direction and then I was able to take
 

00:01:15.540 --> 00:01:18.530 align:start position:0%
direction and then I was able to take
that<00:01:15.840><c> and</c><00:01:16.320><c> run</c><00:01:16.560><c> the</c><00:01:16.740><c> rest</c><00:01:16.920><c> away</c><00:01:17.159><c> with</c><00:01:17.460><c> it</c><00:01:18.060><c> so</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
that and run the rest away with it so
 

00:01:18.540 --> 00:01:21.109 align:start position:0%
that and run the rest away with it so
let's<00:01:18.780><c> get</c><00:01:19.020><c> into</c><00:01:19.320><c> the</c><00:01:19.560><c> script</c><00:01:19.920><c> at</c><00:01:20.820><c> the</c><00:01:21.000><c> heart</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
let's get into the script at the heart
 

00:01:21.119 --> 00:01:23.570 align:start position:0%
let's get into the script at the heart
of<00:01:21.360><c> the</c><00:01:21.479><c> script</c><00:01:21.659><c> is</c><00:01:22.080><c> a</c><00:01:22.259><c> timer</c><00:01:22.380><c> I</c><00:01:23.100><c> need</c><00:01:23.280><c> to</c><00:01:23.400><c> check</c>

00:01:23.570 --> 00:01:23.580 align:start position:0%
of the script is a timer I need to check
 

00:01:23.580 --> 00:01:26.270 align:start position:0%
of the script is a timer I need to check
every<00:01:24.060><c> 10</c><00:01:24.360><c> seconds</c><00:01:24.659><c> or</c><00:01:25.020><c> so</c><00:01:25.140><c> to</c><00:01:25.799><c> see</c><00:01:25.979><c> if</c><00:01:26.100><c> the</c>

00:01:26.270 --> 00:01:26.280 align:start position:0%
every 10 seconds or so to see if the
 

00:01:26.280 --> 00:01:29.030 align:start position:0%
every 10 seconds or so to see if the
atem<00:01:26.580><c> is</c><00:01:26.820><c> available</c><00:01:27.000><c> online</c><00:01:27.479><c> so</c><00:01:28.439><c> every</c><00:01:28.740><c> 10</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
atem is available online so every 10
 

00:01:29.040 --> 00:01:31.550 align:start position:0%
atem is available online so every 10
seconds<00:01:29.280><c> it</c><00:01:29.700><c> fires</c><00:01:30.000><c> the</c><00:01:30.180><c> Ping</c><00:01:30.360><c> command</c><00:01:31.140><c> it</c>

00:01:31.550 --> 00:01:31.560 align:start position:0%
seconds it fires the Ping command it
 

00:01:31.560 --> 00:01:33.530 align:start position:0%
seconds it fires the Ping command it
just<00:01:31.680><c> sends</c><00:01:31.979><c> a</c><00:01:32.040><c> single</c><00:01:32.340><c> packet</c><00:01:32.700><c> and</c><00:01:33.180><c> then</c><00:01:33.360><c> the</c>

00:01:33.530 --> 00:01:33.540 align:start position:0%
just sends a single packet and then the
 

00:01:33.540 --> 00:01:35.330 align:start position:0%
just sends a single packet and then the
result<00:01:33.659><c> gets</c><00:01:34.200><c> sent</c><00:01:34.320><c> to</c><00:01:34.500><c> this</c><00:01:34.680><c> text</c><00:01:34.920><c> pay</c>

00:01:35.330 --> 00:01:35.340 align:start position:0%
result gets sent to this text pay
 

00:01:35.340 --> 00:01:37.249 align:start position:0%
result gets sent to this text pay
function<00:01:35.759><c> that</c><00:01:36.540><c> looks</c><00:01:36.900><c> at</c><00:01:36.960><c> the</c><00:01:37.140><c> second</c>

00:01:37.249 --> 00:01:37.259 align:start position:0%
function that looks at the second
 

00:01:37.259 --> 00:01:39.230 align:start position:0%
function that looks at the second
parameter<00:01:37.799><c> to</c><00:01:38.040><c> see</c><00:01:38.220><c> if</c><00:01:38.340><c> the</c><00:01:38.579><c> status</c><00:01:38.700><c> is</c><00:01:39.060><c> either</c>

00:01:39.230 --> 00:01:39.240 align:start position:0%
parameter to see if the status is either
 

00:01:39.240 --> 00:01:42.890 align:start position:0%
parameter to see if the status is either
received<00:01:39.840><c> packet</c><00:01:40.259><c> or</c><00:01:40.799><c> send</c><00:01:41.040><c> packet</c><00:01:41.520><c> failed</c><00:01:42.000><c> if</c>

00:01:42.890 --> 00:01:42.900 align:start position:0%
received packet or send packet failed if
 

00:01:42.900 --> 00:01:45.230 align:start position:0%
received packet or send packet failed if
it<00:01:43.079><c> received</c><00:01:43.619><c> a</c><00:01:43.740><c> packet</c><00:01:44.100><c> and</c><00:01:44.579><c> this</c><00:01:44.759><c> is</c><00:01:44.880><c> a</c><00:01:45.119><c> new</c>

00:01:45.230 --> 00:01:45.240 align:start position:0%
it received a packet and this is a new
 

00:01:45.240 --> 00:01:47.870 align:start position:0%
it received a packet and this is a new
status<00:01:45.479><c> then</c><00:01:45.900><c> Mount</c><00:01:46.140><c> the</c><00:01:46.320><c> SSD</c><00:01:46.880><c> otherwise</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
status then Mount the SSD otherwise
 

00:01:47.880 --> 00:01:50.270 align:start position:0%
status then Mount the SSD otherwise
don't<00:01:48.360><c> mount</c><00:01:48.659><c> it</c><00:01:48.900><c> and</c><00:01:49.560><c> then</c><00:01:49.680><c> set</c><00:01:49.979><c> the</c><00:01:50.100><c> status</c>

00:01:50.270 --> 00:01:50.280 align:start position:0%
don't mount it and then set the status
 

00:01:50.280 --> 00:01:51.950 align:start position:0%
don't mount it and then set the status
to<00:01:50.640><c> the</c><00:01:50.820><c> new</c><00:01:50.939><c> status</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
to the new status
 

00:01:51.960 --> 00:01:54.889 align:start position:0%
to the new status
so<00:01:52.500><c> Mount</c><00:01:52.799><c> makes</c><00:01:53.399><c> a</c><00:01:53.579><c> call</c><00:01:53.759><c> to</c><00:01:54.119><c> Cloud</c><00:01:54.299><c> mounter</c>

00:01:54.889 --> 00:01:54.899 align:start position:0%
so Mount makes a call to Cloud mounter
 

00:01:54.899 --> 00:01:57.170 align:start position:0%
so Mount makes a call to Cloud mounter
this<00:01:55.560><c> is</c><00:01:55.680><c> one</c><00:01:55.860><c> of</c><00:01:55.979><c> the</c><00:01:56.040><c> areas</c><00:01:56.340><c> where</c><00:01:56.579><c> chat</c><00:01:56.820><c> GPT</c>

00:01:57.170 --> 00:01:57.180 align:start position:0%
this is one of the areas where chat GPT
 

00:01:57.180 --> 00:01:59.569 align:start position:0%
this is one of the areas where chat GPT
failed<00:01:57.780><c> me</c><00:01:57.899><c> it</c><00:01:58.380><c> kept</c><00:01:58.680><c> wanting</c><00:01:58.920><c> to</c><00:01:58.979><c> use</c><00:01:59.159><c> Curl</c>

00:01:59.569 --> 00:01:59.579 align:start position:0%
failed me it kept wanting to use Curl
 

00:01:59.579 --> 00:02:02.450 align:start position:0%
failed me it kept wanting to use Curl
FTP<00:02:00.119><c> FS</c><00:02:00.600><c> which</c><00:02:00.899><c> is</c><00:02:01.079><c> a</c><00:02:01.200><c> Linux</c><00:02:01.500><c> command</c><00:02:01.860><c> but</c>

00:02:02.450 --> 00:02:02.460 align:start position:0%
FTP FS which is a Linux command but
 

00:02:02.460 --> 00:02:04.190 align:start position:0%
FTP FS which is a Linux command but
Hammer<00:02:02.820><c> spoon</c><00:02:03.000><c> is</c><00:02:03.180><c> for</c><00:02:03.299><c> the</c><00:02:03.420><c> mac</c><00:02:03.540><c> and</c><00:02:04.020><c> that's</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
Hammer spoon is for the mac and that's
 

00:02:04.200 --> 00:02:06.590 align:start position:0%
Hammer spoon is for the mac and that's
where<00:02:04.439><c> I</c><00:02:04.560><c> am</c><00:02:04.740><c> so</c><00:02:05.579><c> I</c><00:02:05.880><c> started</c><00:02:06.180><c> looking</c><00:02:06.360><c> for</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
where I am so I started looking for
 

00:02:06.600 --> 00:02:09.410 align:start position:0%
where I am so I started looking for
Alternatives<00:02:07.140><c> the</c><00:02:07.920><c> mount</c><00:02:08.099><c> FTP</c><00:02:08.759><c> command</c><00:02:09.179><c> would</c>

00:02:09.410 --> 00:02:09.420 align:start position:0%
Alternatives the mount FTP command would
 

00:02:09.420 --> 00:02:11.690 align:start position:0%
Alternatives the mount FTP command would
work<00:02:09.539><c> but</c><00:02:09.959><c> it</c><00:02:10.259><c> will</c><00:02:10.440><c> only</c><00:02:10.500><c> amount</c><00:02:10.920><c> a</c><00:02:11.160><c> read-only</c>

00:02:11.690 --> 00:02:11.700 align:start position:0%
work but it will only amount a read-only
 

00:02:11.700 --> 00:02:14.089 align:start position:0%
work but it will only amount a read-only
volume<00:02:11.940><c> that</c><00:02:12.900><c> might</c><00:02:13.140><c> be</c><00:02:13.260><c> fine</c><00:02:13.440><c> but</c><00:02:13.739><c> I</c><00:02:13.980><c> was</c>

00:02:14.089 --> 00:02:14.099 align:start position:0%
volume that might be fine but I was
 

00:02:14.099 --> 00:02:15.650 align:start position:0%
volume that might be fine but I was
already<00:02:14.280><c> using</c><00:02:14.580><c> Cloud</c><00:02:14.700><c> Monitor</c><00:02:15.239><c> and</c><00:02:15.360><c> I</c><00:02:15.540><c> knew</c>

00:02:15.650 --> 00:02:15.660 align:start position:0%
already using Cloud Monitor and I knew
 

00:02:15.660 --> 00:02:18.050 align:start position:0%
already using Cloud Monitor and I knew
that<00:02:15.840><c> it</c><00:02:15.959><c> mounted</c><00:02:16.319><c> a</c><00:02:16.560><c> read</c><00:02:16.739><c> write</c><00:02:16.980><c> volume</c>

00:02:18.050 --> 00:02:18.060 align:start position:0%
that it mounted a read write volume
 

00:02:18.060 --> 00:02:20.030 align:start position:0%
that it mounted a read write volume
there's<00:02:18.660><c> almost</c><00:02:18.900><c> no</c><00:02:19.319><c> info</c><00:02:19.680><c> on</c><00:02:19.860><c> the</c><00:02:19.980><c> cloud</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
there's almost no info on the cloud
 

00:02:20.040 --> 00:02:22.190 align:start position:0%
there's almost no info on the cloud
monitor<00:02:20.580><c> website</c><00:02:20.940><c> about</c><00:02:21.239><c> its</c><00:02:21.660><c> ability</c><00:02:21.900><c> to</c>

00:02:22.190 --> 00:02:22.200 align:start position:0%
monitor website about its ability to
 

00:02:22.200 --> 00:02:24.710 align:start position:0%
monitor website about its ability to
script<00:02:22.500><c> other</c><00:02:23.160><c> than</c><00:02:23.459><c> one</c><00:02:23.879><c> mention</c><00:02:24.180><c> in</c><00:02:24.599><c> the</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
script other than one mention in the
 

00:02:24.720 --> 00:02:26.930 align:start position:0%
script other than one mention in the
release<00:02:24.840><c> notes</c><00:02:25.319><c> about</c><00:02:25.739><c> applescript</c><00:02:26.580><c> being</c>

00:02:26.930 --> 00:02:26.940 align:start position:0%
release notes about applescript being
 

00:02:26.940 --> 00:02:29.210 align:start position:0%
release notes about applescript being
added<00:02:27.300><c> so</c><00:02:27.900><c> I</c><00:02:28.020><c> loaded</c><00:02:28.319><c> up</c><00:02:28.440><c> the</c><00:02:28.620><c> script</c><00:02:28.800><c> editor</c>

00:02:29.210 --> 00:02:29.220 align:start position:0%
added so I loaded up the script editor
 

00:02:29.220 --> 00:02:31.369 align:start position:0%
added so I loaded up the script editor
utility<00:02:29.700><c> that</c><00:02:30.000><c> comes</c><00:02:30.180><c> with</c><00:02:30.300><c> a</c><00:02:30.480><c> Mac</c><00:02:30.599><c> then</c><00:02:31.200><c> open</c>

00:02:31.369 --> 00:02:31.379 align:start position:0%
utility that comes with a Mac then open
 

00:02:31.379 --> 00:02:33.770 align:start position:0%
utility that comes with a Mac then open
the<00:02:31.680><c> library</c><00:02:31.800><c> and</c><00:02:32.340><c> added</c><00:02:32.640><c> Cloud</c><00:02:32.879><c> bouncer</c><00:02:33.420><c> to</c>

00:02:33.770 --> 00:02:33.780 align:start position:0%
the library and added Cloud bouncer to
 

00:02:33.780 --> 00:02:35.930 align:start position:0%
the library and added Cloud bouncer to
the<00:02:33.900><c> library</c><00:02:34.160><c> quickly</c><00:02:35.160><c> I</c><00:02:35.340><c> could</c><00:02:35.459><c> see</c><00:02:35.640><c> that</c>

00:02:35.930 --> 00:02:35.940 align:start position:0%
the library quickly I could see that
 

00:02:35.940 --> 00:02:38.270 align:start position:0%
the library quickly I could see that
mount<00:02:36.300><c> is</c><00:02:36.840><c> an</c><00:02:37.020><c> available</c><00:02:37.200><c> verb</c><00:02:37.800><c> so</c><00:02:38.040><c> I</c><00:02:38.160><c> could</c>

00:02:38.270 --> 00:02:38.280 align:start position:0%
mount is an available verb so I could
 

00:02:38.280 --> 00:02:40.970 align:start position:0%
mount is an available verb so I could
use<00:02:38.400><c> that</c><00:02:39.000><c> now</c><00:02:39.480><c> I</c><00:02:39.720><c> hate</c><00:02:39.900><c> applescript</c><00:02:40.440><c> it</c><00:02:40.860><c> is</c>

00:02:40.970 --> 00:02:40.980 align:start position:0%
use that now I hate applescript it is
 

00:02:40.980 --> 00:02:42.589 align:start position:0%
use that now I hate applescript it is
easily<00:02:41.280><c> the</c><00:02:41.519><c> hardest</c><00:02:41.819><c> language</c><00:02:42.060><c> to</c><00:02:42.480><c> learn</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
easily the hardest language to learn
 

00:02:42.599 --> 00:02:45.170 align:start position:0%
easily the hardest language to learn
because<00:02:43.080><c> there</c><00:02:43.379><c> are</c><00:02:43.500><c> no</c><00:02:43.680><c> standards</c><00:02:44.099><c> every</c><00:02:44.940><c> app</c>

00:02:45.170 --> 00:02:45.180 align:start position:0%
because there are no standards every app
 

00:02:45.180 --> 00:02:47.030 align:start position:0%
because there are no standards every app
implements<00:02:45.959><c> it</c><00:02:46.080><c> differently</c><00:02:46.440><c> and</c><00:02:46.680><c> that</c><00:02:46.860><c> makes</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
implements it differently and that makes
 

00:02:47.040 --> 00:02:50.150 align:start position:0%
implements it differently and that makes
it<00:02:47.340><c> super</c><00:02:47.879><c> frustrating</c><00:02:48.560><c> but</c><00:02:49.560><c> with</c><00:02:49.800><c> a</c><00:02:49.980><c> bit</c><00:02:50.040><c> of</c>

00:02:50.150 --> 00:02:50.160 align:start position:0%
it super frustrating but with a bit of
 

00:02:50.160 --> 00:02:51.830 align:start position:0%
it super frustrating but with a bit of
trial<00:02:50.400><c> and</c><00:02:50.580><c> error</c><00:02:50.879><c> I</c><00:02:51.180><c> got</c><00:02:51.360><c> something</c><00:02:51.540><c> that</c>

00:02:51.830 --> 00:02:51.840 align:start position:0%
trial and error I got something that
 

00:02:51.840 --> 00:02:53.869 align:start position:0%
trial and error I got something that
would<00:02:52.019><c> call</c><00:02:52.260><c> my</c><00:02:52.500><c> existing</c><00:02:52.860><c> config</c><00:02:53.280><c> for</c><00:02:53.760><c> the</c>

00:02:53.869 --> 00:02:53.879 align:start position:0%
would call my existing config for the
 

00:02:53.879 --> 00:02:56.030 align:start position:0%
would call my existing config for the
atem<00:02:54.239><c> drive</c><00:02:54.480><c> that</c><00:02:54.840><c> would</c><00:02:55.019><c> melt</c><00:02:55.440><c> and</c><00:02:55.620><c> unmount</c>

00:02:56.030 --> 00:02:56.040 align:start position:0%
atem drive that would melt and unmount
 

00:02:56.040 --> 00:02:59.350 align:start position:0%
atem drive that would melt and unmount
it<00:02:56.580><c> so</c><00:02:57.000><c> I</c><00:02:57.300><c> was</c><00:02:57.420><c> able</c><00:02:57.599><c> to</c><00:02:57.720><c> call</c><00:02:58.220><c> hs.osa</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
it so I was able to call hs.osa
 

00:02:59.360 --> 00:03:01.850 align:start position:0%
it so I was able to call hs.osa
script.apple<00:03:00.360><c> script</c><00:03:00.599><c> with</c><00:03:01.140><c> the</c><00:03:01.379><c> string</c><00:03:01.620><c> tell</c>

00:03:01.850 --> 00:03:01.860 align:start position:0%
script.apple script with the string tell
 

00:03:01.860 --> 00:03:04.009 align:start position:0%
script.apple script with the string tell
application<00:03:02.580><c> Cloud</c><00:03:03.000><c> mounter</c><00:03:03.540><c> to</c><00:03:03.780><c> mount</c>

00:03:04.009 --> 00:03:04.019 align:start position:0%
application Cloud mounter to mount
 

00:03:04.019 --> 00:03:07.430 align:start position:0%
application Cloud mounter to mount
connection<00:03:04.560><c> atem</c><00:03:05.400><c> to</c><00:03:06.180><c> mount</c><00:03:06.360><c> the</c><00:03:06.540><c> drive</c><00:03:06.720><c> and</c>

00:03:07.430 --> 00:03:07.440 align:start position:0%
connection atem to mount the drive and
 

00:03:07.440 --> 00:03:09.350 align:start position:0%
connection atem to mount the drive and
then<00:03:07.560><c> use</c><00:03:07.739><c> unmount</c><00:03:08.280><c> to</c><00:03:08.700><c> unmount</c><00:03:09.000><c> the</c><00:03:09.180><c> drive</c>

00:03:09.350 --> 00:03:09.360 align:start position:0%
then use unmount to unmount the drive
 

00:03:09.360 --> 00:03:12.050 align:start position:0%
then use unmount to unmount the drive
note<00:03:10.080><c> that</c><00:03:10.379><c> Apple</c><00:03:10.739><c> scripts</c><00:03:11.159><c> wants</c><00:03:11.580><c> strings</c><00:03:11.819><c> to</c>

00:03:12.050 --> 00:03:12.060 align:start position:0%
note that Apple scripts wants strings to
 

00:03:12.060 --> 00:03:13.670 align:start position:0%
note that Apple scripts wants strings to
be<00:03:12.120><c> wrapped</c><00:03:12.480><c> in</c><00:03:12.720><c> double</c><00:03:12.900><c> quotes</c><00:03:13.140><c> So</c><00:03:13.500><c> the</c>

00:03:13.670 --> 00:03:13.680 align:start position:0%
be wrapped in double quotes So the
 

00:03:13.680 --> 00:03:15.649 align:start position:0%
be wrapped in double quotes So the
applescript<00:03:14.159><c> needs</c><00:03:14.879><c> to</c><00:03:14.940><c> be</c><00:03:15.060><c> in</c><00:03:15.180><c> single</c><00:03:15.480><c> quotes</c>

00:03:15.649 --> 00:03:15.659 align:start position:0%
applescript needs to be in single quotes
 

00:03:15.659 --> 00:03:18.170 align:start position:0%
applescript needs to be in single quotes
or<00:03:16.319><c> you</c><00:03:16.500><c> need</c><00:03:16.620><c> to</c><00:03:16.739><c> escape</c><00:03:16.860><c> the</c><00:03:17.159><c> strings</c>

00:03:18.170 --> 00:03:18.180 align:start position:0%
or you need to escape the strings
 

00:03:18.180 --> 00:03:21.110 align:start position:0%
or you need to escape the strings
finally<00:03:18.959><c> I</c><00:03:19.440><c> pop</c><00:03:19.620><c> up</c><00:03:19.800><c> an</c><00:03:20.040><c> alert</c><00:03:20.340><c> to</c><00:03:20.580><c> show</c><00:03:20.760><c> that</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
finally I pop up an alert to show that
 

00:03:21.120 --> 00:03:23.449 align:start position:0%
finally I pop up an alert to show that
the<00:03:21.300><c> drive</c><00:03:21.480><c> is</c><00:03:21.840><c> mounted</c><00:03:22.200><c> within</c><00:03:23.040><c> about</c><00:03:23.220><c> 10</c>

00:03:23.449 --> 00:03:23.459 align:start position:0%
the drive is mounted within about 10
 

00:03:23.459 --> 00:03:25.550 align:start position:0%
the drive is mounted within about 10
seconds<00:03:23.640><c> of</c><00:03:23.879><c> turning</c><00:03:24.180><c> the</c><00:03:24.300><c> ATM</c><00:03:24.659><c> on</c><00:03:24.840><c> the</c><00:03:25.440><c> drive</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
seconds of turning the ATM on the drive
 

00:03:25.560 --> 00:03:27.770 align:start position:0%
seconds of turning the ATM on the drive
will<00:03:25.800><c> appear</c><00:03:26.099><c> on</c><00:03:26.280><c> my</c><00:03:26.459><c> Mac</c><00:03:26.580><c> and</c><00:03:27.239><c> then</c><00:03:27.300><c> within</c>

00:03:27.770 --> 00:03:27.780 align:start position:0%
will appear on my Mac and then within
 

00:03:27.780 --> 00:03:30.170 align:start position:0%
will appear on my Mac and then within
about<00:03:27.959><c> 90</c><00:03:28.500><c> seconds</c><00:03:28.860><c> of</c><00:03:29.159><c> turning</c><00:03:29.519><c> the</c><00:03:29.580><c> atem</c><00:03:29.940><c> off</c>

00:03:30.170 --> 00:03:30.180 align:start position:0%
about 90 seconds of turning the atem off
 

00:03:30.180 --> 00:03:32.809 align:start position:0%
about 90 seconds of turning the atem off
it<00:03:30.720><c> disappears</c><00:03:31.739><c> I'll</c><00:03:32.099><c> talk</c><00:03:32.280><c> about</c><00:03:32.400><c> the</c><00:03:32.700><c> rest</c>

00:03:32.809 --> 00:03:32.819 align:start position:0%
it disappears I'll talk about the rest
 

00:03:32.819 --> 00:03:34.430 align:start position:0%
it disappears I'll talk about the rest
of<00:03:33.000><c> this</c><00:03:33.120><c> workflow</c><00:03:33.599><c> in</c><00:03:33.900><c> a</c><00:03:34.080><c> different</c><00:03:34.200><c> video</c>

00:03:34.430 --> 00:03:34.440 align:start position:0%
of this workflow in a different video
 

00:03:34.440 --> 00:03:36.949 align:start position:0%
of this workflow in a different video
coming<00:03:34.860><c> soon</c><00:03:35.159><c> thanks</c><00:03:36.000><c> so</c><00:03:36.180><c> much</c><00:03:36.300><c> for</c><00:03:36.540><c> watching</c>

00:03:36.949 --> 00:03:36.959 align:start position:0%
coming soon thanks so much for watching
 

00:03:36.959 --> 00:03:39.589 align:start position:0%
coming soon thanks so much for watching
this<00:03:37.440><c> video</c><00:03:37.680><c> and</c><00:03:38.400><c> I</c><00:03:38.519><c> look</c><00:03:38.700><c> forward</c><00:03:38.940><c> to</c><00:03:39.120><c> seeing</c>

00:03:39.589 --> 00:03:39.599 align:start position:0%
this video and I look forward to seeing
 

00:03:39.599 --> 00:03:42.920 align:start position:0%
this video and I look forward to seeing
you<00:03:39.659><c> in</c><00:03:39.720><c> the</c><00:03:39.900><c> next</c><00:03:40.019><c> one</c><00:03:40.140><c> bye</c>

