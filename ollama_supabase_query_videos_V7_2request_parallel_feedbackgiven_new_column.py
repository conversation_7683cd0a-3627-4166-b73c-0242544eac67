import os
import sys
import codecs
import platform
import subprocess
import time
import unicodedata

# Configure console encoding for Windows
if platform.system() == 'Windows':
    # Enable unicode output in Windows console
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr.encoding != 'utf-8':
        sys.stderr.reconfigure(encoding='utf-8')
    # Ensure Windows console is in UTF-8 mode
    os.system('chcp 65001')

from supabase import create_client, Client
from supabase.client import ClientOptions
import json
from datetime import datetime, timedelta
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
import time
import aiohttp
import logging
from llama31_promptsV2 import prompt_template, validation_prompt_template
from rich import print
from rich.console import Console
from rich.progress import Progress

load_dotenv()


def check_and_start_ollama(ollama_command_str):
    """
    Checks if ollama.exe is running. If not, attempts to start it.

    Args:
        ollama_command_str (str): The command string to start Ollama (e.g., "ollama serve" or "C:\\path\\to\\ollama.exe serve").

    Returns:
        bool: True if Ollama is running or was successfully started, False otherwise.
    """
    try:
        # Check if ollama.exe is running (Windows specific)
        output = subprocess.check_output('tasklist', shell=True, text=True, universal_newlines=True)
        if 'ollama.exe' in output.lower():
            print("Ollama is already running.")
            logging.info("Ollama is already running.")
            return True
        else:
            print("Ollama is not running. Attempting to start it...")
            logging.info("Ollama is not running. Attempting to start it...")
            
            # Manually split the command string to preserve backslashes in Windows paths.
            # ollama_command_str is expected to be like: r"C:\path\to\ollama.exe serve"
            if ' serve' in ollama_command_str: # Simple check for 'serve' argument
                parts = ollama_command_str.rsplit(' serve', 1)
                executable_path = parts[0]
                command_args = ['serve']
            else: # Assumes command is just the executable if ' serve' is not present
                executable_path = ollama_command_str
                command_args = []
            
            command_parts = [executable_path] + command_args
            print(f"DEBUG: executable_path: '{executable_path}', command_args: {command_args}") # DEBUG LINE

            # Explicitly check if the executable path (first part of the command) exists
            if not os.path.exists(executable_path):
                print(f"Error: The identified executable '{executable_path}' does not exist at that path. Please check the path and Ollama installation.")
                logging.error(f"Identified executable '{executable_path}' does not exist.")
                return False 
            
            executable_dir = os.path.dirname(executable_path)
            
            print(f"Attempting to start Ollama. Executable: '{executable_path}', Arguments: {command_parts[1:] if len(command_parts) > 1 else 'None'}, CWD: '{executable_dir}'")
            logging.info(f"Attempting to start Ollama. Executable: '{executable_path}', Arguments: {command_parts[1:] if len(command_parts) > 1 else 'None'}, CWD: '{executable_dir}'")
            
            # Try to start Ollama
            # DETACHED_PROCESS and CREATE_NO_WINDOW are for running in background without a new console on Windows
            process = subprocess.Popen(command_parts, 
                                     creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NO_WINDOW, 
                                     shell=False, # shell=False when command_parts is a list
                                     cwd=executable_dir) # Set Current Working Directory for ollama.exe
            print(f"Ollama process started with PID: {process.pid}. Waiting a few seconds for initialization...")
            logging.info(f"Ollama process started with PID: {process.pid}. Waiting for initialization.")
            time.sleep(10)  # Wait 10 seconds for Ollama to initialize. Adjust if needed.
            
            # Re-check if ollama.exe is running after attempting to start
            output_after_start = subprocess.check_output('tasklist', shell=True, text=True, universal_newlines=True)
            if 'ollama.exe' in output_after_start.lower():
                print("Ollama successfully started and is now running.")
                logging.info("Ollama successfully started and is now running.")
                return True
            else:
                print("Failed to confirm Ollama is running after attempting to start.")
                logging.error("Failed to confirm Ollama is running after attempting to start.")
                return False

    except FileNotFoundError:
        executable_path_at_error = "Unknown"
        if 'ollama_command_str' in locals() and ollama_command_str:
            try:
                temp_executable_path = ollama_command_str 
                if ' serve' in ollama_command_str:
                    temp_executable_path = ollama_command_str.rsplit(' serve', 1)[0]
                executable_path_at_error = temp_executable_path
            except Exception:
                if ollama_command_str: 
                     executable_path_at_error = ollama_command_str.split()[0] if ollama_command_str.split() else "ErrorParsingCommand"

        print(f"Error: Failed to start the Ollama process. The system could not execute '{executable_path_at_error}'.")
        print("This can happen if the executable exists but its dependencies (DLLs) are missing or not found from its CWD, or due to permissions.")
        executable_dir_at_error = "N/A"
        if executable_path_at_error != "Unknown" and os.path.isabs(executable_path_at_error):
            executable_dir_at_error = os.path.dirname(executable_path_at_error)
        command_to_run_manually_base = os.path.basename(executable_path_at_error) if executable_path_at_error not in ['Unknown', 'ErrorParsingCommand'] else 'ollama.exe'
        run_command_example = f"{command_to_run_manually_base} serve"
        print(f"Please ensure Ollama is installed correctly at '{executable_path_at_error}' and can be run manually, for example, by opening a command prompt in '{executable_dir_at_error}' and running '{run_command_example}'.")
        logging.error(f"Popen FileNotFoundError for Ollama executable '{executable_path_at_error}'. Check dependencies/permissions or if it requires admin rights.", exc_info=True)
        return False
    except subprocess.CalledProcessError as e:
        print(f"Error executing system command: {e}")
        logging.error(f"Error executing system command: {e}")
        return False
    except Exception as e:
        print(f"An unexpected error occurred while checking or starting Ollama: {e}")
        logging.error(f"Unexpected error with Ollama check/start: {e}", exc_info=True)
        return False


"""
This script is designed to process and analyze video transcripts stored in a Supabase database. It performs the following main functions:

1. Connects to a Supabase database using environment variables for authentication.
2. Retrieves video transcripts from specified tables in the database.
3. Uses the Ollama API to generate AI-powered analysis of the transcripts, including keywords, typical questions, and key conclusions.
4. Implements parallel processing to handle multiple requests simultaneously, improving efficiency.
5. Includes a validation step to ensure the AI-generated responses meet specific criteria.
6. Handles rate limiting and retries failed requests using exponential backoff.
7. Truncates long transcripts to fit within the AI model's token limit.
8. Updates the database with the processed results and any feedback on the AI's performance.
9. Implements logging for monitoring the script's progress and debugging.

The script is designed to be robust, handling various edge cases and potential errors, and is optimized for processing large amounts of data efficiently.
"""


url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key,
  options=ClientOptions(
    schema="public",  # Use the default public schema
    headers={},  # No additional headers needed for now
    auto_refresh_token=True,  # Automatically refresh the token when it expires
    persist_session=True,  # Persist the logged in session
    storage=None,  # Use the default storage provider
    realtime=None,  # No specific realtime options needed
    postgrest_client_timeout=999999,  # Set PostgreSQL timeout to 1 hour (3600 seconds)
    storage_client_timeout=999999,  # Set storage timeout to 1 hour
    flow_type=None  # Use the default authentication flow
  ))

# Constants
ENABLE_VALIDATION = False  # Set to True to enable second prompt validation check
ROWS_PER_TABLE = -1
MAX_PROCESSING_ATTEMPTS = 5  # Max number of times to try processing a row
PARALLEL_REQUESTS = 1  # Number of concurrent requests to process
tables_to_process = ["youtube_renewable_energy","youtube_gme","youtube_artificial_intelligence","youtube_sustainability", "youtube_startups","youtube_financial_markets", "youtube_general"]#, "youtube_artificial_intelligence","youtube_gme","youtube_sustainability", "youtube_startups","youtube_financial_markets", "youtube_general", "youtube_legal"]
chosen_model = "gemma3:4b"  #gemma3:4b ;;; gemma3:12b ;;; qwen
validator_model = "gemma3:4b"  #gemma3:4b ;;; gemma3:12b

# Constants for token limits
# IMPORTANT: Verify the actual context window for your chosen model (gemma3:4b).
# Gemma models typically have 8192 tokens. Using 8192 as a placeholder.
MODEL_TOTAL_CONTEXT_WINDOW = 128000 # Gemma 3 4B supports up to 128k. Ollama default is 2048 if not specified via API.
MAX_OUTPUT_TOKENS = 8192   # For generated response
MAX_VALIDATION_OUTPUT_TOKENS = 1024 # For the validation model's response
SAFETY_MARGIN_TOKENS = 500  # Adjust as needed
# Calculate max tokens for the input part of the prompt
MAX_INPUT_PROMPT_TOKENS = MODEL_TOTAL_CONTEXT_WINDOW - MAX_OUTPUT_TOKENS - SAFETY_MARGIN_TOKENS
MIN_TRANSCRIPT_TOKENS_FOR_PROCESSING = 200 # Minimum tokens a transcript must have after calculating available space

# Function to count tokens
def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))

def validate_llm_response_structure(response_dict):
    """Validates that the LLM response contains all required fields"""
    required_fields = ["keywords", "questions", "insights", "recommendations"]
    
    for field in required_fields:
        if field not in response_dict:
            return False, f"Missing required field: {field}"
        if not isinstance(response_dict[field], list):
            return False, f"Field '{field}' must be a list, got {type(response_dict[field])}"
        if len(response_dict[field]) == 0:
            return False, f"Field '{field}' cannot be empty"
    
    return True, "Valid structure"

def sanitize_text_for_prompt(text):
    """Sanitizes text to prevent encoding issues and characters problematic for JSON in prompts."""
    if not text:
        return ""
    
    # Normalize unicode characters to their closest ASCII representation
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('utf-8')
    
    # Escape characters that could break JSON string values or cause issues in the prompt
    # Order matters: escape backslash first, then others.
    text = text.replace('\\\\', '\\\\\\\\')  # Escape backslashes (e.g., \\ -> \\\\)
    text = text.replace('"', '\\"')    # Escape double quotes
    text = text.replace('\\n', '\\\\n')  # Escape actual newline characters if they are already escaped once
    text = text.replace('\\r', '\\\\r')  # Escape actual carriage return characters if they are already escaped once
    text = text.replace('\\t', '\\\\t')  # Escape actual tab characters if they are already escaped once
    text = text.replace('\n', '\\\\n')  # Escape raw newlines
    text = text.replace('\r', '\\\\r')  # Escape raw carriage returns
    text = text.replace('\t', '\\\\t')  # Escape raw tabs
    
    # Replace other problematic characters 
    # (some of these might be less critical after ASCII normalization and JSON escaping)
    replacements = {
        '…': '...', # Ellipsis
        '–': '-',   # En dash
        '—': '-',   # Em dash
        # Add any other specific character replacements here if needed
    }

    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text

# Modified function to truncate the transcript
def truncate_transcript(transcript, target_transcript_tokens):
    """Truncates the transcript to be as close as possible to target_transcript_tokens without exceeding it."""
    if not transcript:
        return ""
    
    current_tokens = count_tokens(transcript)
    
    if current_tokens <= target_transcript_tokens:
        return transcript
    else:
        # Estimate characters per token (can be rough, e.g., 3-4 for English)
        # This is a starting point for slicing; the while loop will refine it.
        # A simple approach: assume an average of 4 chars/token.
        # If target_transcript_tokens is 0 or negative, return empty string.
        if target_transcript_tokens <= 0:
            return ""
            
        estimated_chars = int(target_transcript_tokens * 3.5) # Adjusted for a slightly better initial guess
        truncated_transcript = transcript[:estimated_chars]
        
        # Iteratively adjust to meet the token limit
        # Shrink if over
        while count_tokens(truncated_transcript) > target_transcript_tokens:
            truncated_transcript = truncated_transcript[:-10] # Remove 10 chars at a time for speed
            if not truncated_transcript: # Safety break if it becomes empty
                break
        # If we undershot significantly (e.g. by removing 10 chars), try adding back char by char
        # This part is tricky and can be slow. For now, we'll accept being slightly under.
        # A more sophisticated approach might involve binary searching for the cut-off point.
        # Let's ensure we don't return an empty string if target_transcript_tokens > 0
        if not truncated_transcript and target_transcript_tokens > 0:
             # Fallback if aggressive trimming made it empty, try a very small slice
             # This case should be rare with positive target_transcript_tokens
             temp_slice = transcript[:target_transcript_tokens] # Very rough, likely too short
             while count_tokens(temp_slice) > target_transcript_tokens and temp_slice:
                 temp_slice = temp_slice[:-1]
             return temp_slice

        return truncated_transcript

# Silence the HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)


# Replace the synchronous query_ollama function with an asynchronous version
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=3))
async def query_ollama_async(prompt, session, max_output_tokens_override=None):
    try:
        effective_max_output_tokens = max_output_tokens_override if max_output_tokens_override is not None else MAX_OUTPUT_TOKENS
        # print(f"Querying Ollama with model: {chosen_model}. Effective max output tokens: {effective_max_output_tokens}") # Debug
        request_payload = {
            "model": chosen_model,
            "prompt": prompt,
            "format": "json",
            "stream": False,
            "options": {
                "num_predict": effective_max_output_tokens,
                "temperature": 0.2,
                "top_k": 10,
                "top_p": 0.9,
                "repeat_penalty": 1.1,
                "num_ctx": MODEL_TOTAL_CONTEXT_WINDOW  # Explicitly set context window for Ollama
            }
        }
        # print(f"Ollama request payload: {json.dumps(request_payload, indent=2)}") # Careful, prompt can be huge

        async with session.post("http://localhost:11434/api/generate", json=request_payload, timeout=600) as response:
            response.raise_for_status()  # Will raise an exception for HTTP error codes
            result = await response.json()
            
            # print(f"Full Ollama result object: {result}") # Debug: Print the full object
        
            ollama_response_str = result.get("response", "")
            # print(f"Type of ollama_response_str: {type(ollama_response_str)}") # Debug
            # print(f"Raw Ollama response string (repr()): {repr(ollama_response_str)}") # Debug
            # print(f"Length of Ollama response string: {len(ollama_response_str)}") # Debug
            # print(f"Raw Ollama response string (first 500 chars): {ollama_response_str[:500]}") # Debug
            # print(f"Raw Ollama response string (last 500 chars): {ollama_response_str[-500:]}") # Debug

            if not ollama_response_str or ollama_response_str.strip() == "{}":
                # This warning is more contextually placed in process_video_async or main loop
                # print(f"Warning: Ollama returned an empty or \\'{{}}\\' response. Raw response: {ollama_response_str}")
                pass # The calling function will handle the empty response
        
            return ollama_response_str
            
    except aiohttp.ClientResponseError as e:
        print(f"HTTP error occurred while querying Ollama: {e.status} {e.message}")
        print(f"Response headers: {e.headers}")
        print(f"Response text: {await e.text() if hasattr(e, 'text') else 'N/A'}") # Log response body if available
        raise
    except asyncio.TimeoutError: # Timeout for the individual request
        print("Ollama query timed out after 10 minutes")
        raise
    except Exception as e:
        print(f"An unexpected error occurred while querying Ollama: {str(e)} of type {type(e)}")
        # Log traceback for unexpected errors
        import traceback
        print(traceback.format_exc())
        raise


# Modify the validate_ollama_response_async function
@retry(stop=stop_after_attempt(10), wait=wait_exponential(multiplier=1, min=4, max=10))
async def validate_ollama_response_async(original_prompt: str, ollama_response_str: str, session, attempt: int):
    """
    Validates the Ollama response by sending it back to another LLM instance (or the same with a different prompt)
    to check for correctness, completeness, and adherence to the expected JSON structure.
    Logs the original prompt and response if validation fails critically.
    """
    validation_prompt = f"""Original prompt: {original_prompt}
Ollama's response: {ollama_response_str}

Please validate this response.
1. Is the JSON valid?
2. Does it contain all required keys: 'keywords', 'questions', 'insights', 'recommendations'?
3. Are the values for these keys non-empty lists of strings?
4. Are the keywords relevant to the content?
5. Are the questions pertinent and insightful?
6. Do the insights offer valuable observations?
7. Are the recommendations actionable and relevant?

Respond with a JSON object: {{"is_valid": boolean, "issues": ["description of issue 1", "description of issue 2", ...]}}.
If the response is valid and meets all criteria, "issues" should be an empty list.
"""
    try:
        # Use query_ollama_async for the validation call, potentially with different settings
        raw_validation_response = await query_ollama_async(
            validation_prompt, 
            session, 
            max_output_tokens_override=MAX_VALIDATION_OUTPUT_TOKENS # Use the new constant
        )

        if not raw_validation_response or raw_validation_response.isspace():
            logging.error(f"Validation LLM returned an empty or whitespace response. Original response: {ollama_response_str}")
            if attempt == MAX_PROCESSING_ATTEMPTS - 1: # Corrected variable name
                logging.critical(f"CRITICAL VALIDATION FAILURE (empty validation response) on attempt {attempt + 1}/{MAX_PROCESSING_ATTEMPTS}. Original prompt for main LLM: \\n{original_prompt}\\nOllama's response to validate: \\n{ollama_response_str}")
            return {"is_valid": False, "issues": ["Validation LLM returned empty response."]}

        validation_result = json.loads(raw_validation_response)
        
        if not validation_result.get("is_valid", False):
            logging.warning(f"Validation failed for response: {ollama_response_str}. Issues: {validation_result.get('issues', [])}")
            if attempt == MAX_PROCESSING_ATTEMPTS - 1: # Corrected variable name
                 logging.critical(f"CRITICAL VALIDATION FAILURE on attempt {attempt + 1}/{MAX_PROCESSING_ATTEMPTS}. Original prompt for main LLM: \\n{original_prompt}\\nOllama's response: \\n{ollama_response_str}\\nValidation issues: {validation_result.get('issues', [])}")
        return validation_result

    except json.JSONDecodeError as e:
        logging.error(f"Failed to decode JSON from validation LLM: {e}. Raw response: {raw_validation_response}")
        if attempt == MAX_PROCESSING_ATTEMPTS - 1: # Corrected variable name
            logging.critical(f"CRITICAL VALIDATION FAILURE (JSON decode error in validation response) on attempt {attempt + 1}/{MAX_PROCESSING_ATTEMPTS}. Original prompt for main LLM: \\n{original_prompt}\\nOllama's response to validate: \\n{ollama_response_str}\\nRaw validation response: \\n{raw_validation_response}")
        return {"is_valid": False, "issues": [f"Validation LLM response was not valid JSON: {e}"]}
    except Exception as e:
        logging.error(f"Error during validation call: {e}")
        if attempt == MAX_PROCESSING_ATTEMPTS - 1: # Corrected variable name
            logging.critical(f"CRITICAL VALIDATION FAILURE (exception in validation) on attempt {attempt + 1}/{MAX_PROCESSING_ATTEMPTS}. Original prompt for main LLM: \\n{original_prompt}\\nOllama's response to validate: \\n{ollama_response_str}\\nException: {e}")
        return {"is_valid": False, "issues": [f"Exception during validation: {e}"]}

# Modify the process_row_async function to pass the transcript
"""
This function processes a single row of video data asynchronously. It performs the following steps:

1. Checks if the video has already been processed, skipping if so.
2. If a transcript is available, it attempts to process the video up to MAX_PROCESSING_ATTEMPTS times.
3. For each attempt:
   a. Truncates the transcript to fit within token limits, including any previous feedback.
   b. Formats the prompt with video metadata and the truncated transcript.
   c. Queries the Ollama API with the formatted prompt.
   d. Validates the LLM response for JSON format and content quality.
   e. If approved, updates the database with the processed result.
   f. If not approved, collects feedback for the next attempt.
4. Handles JSON parsing errors and logging throughout the process.
5. Uses retry logic to handle potential API failures or rate limiting.

The function is designed to be robust, handling various edge cases and potential errors,
and is optimized for processing large amounts of video transcript data efficiently.
"""


async def process_row_async(row, table_name, session):
    if row['processed'] in ["completed", "impossible"]:
        print(f"Skipping already processed/impossible video: {row['title']}")
        return

    # Check if transcript is empty or just whitespace
    if not row['transcript'] or row['transcript'].strip() == "" or row['transcript'] == "Transcript not available":
        print(f"Empty transcript found for video: {row['title']} - Marking as impossible")
        current_datetime = datetime.now().isoformat()        
        supabase.table(table_name).update({
            "llm_response": "Error: Empty transcript",
            "llm_call_date": current_datetime,
            "processed": "impossible",
            "is_visible": False  # Set is_visible to False
        }).eq("id", row['id']).execute()
        return

    auditor_feedback = "" # Initialize auditor_feedback for the first attempt
    # Ensure auditor_feedback from the row is a string, even if None or empty
    # This will be used if ENABLE_VALIDATION leads to feedback for subsequent attempts.
    # For the first attempt, it's effectively empty unless pre-filled in DB.
    initial_auditor_feedback_from_row = "" # row.get('auditor_feedback', '') or "" # Removed direct DB read for auditor_feedback


    for attempt in range(MAX_PROCESSING_ATTEMPTS):
        # Sanitize non-transcript text fields first
        sanitized_title = sanitize_text_for_prompt(row['title'])
        sanitized_channel = sanitize_text_for_prompt(row['channel_name'])
        
        # Auditor feedback for the current attempt. Starts empty or with row data, then gets updated.
        current_attempt_auditor_feedback = auditor_feedback if attempt > 0 else initial_auditor_feedback_from_row
        sanitized_auditor_feedback = sanitize_text_for_prompt(current_attempt_auditor_feedback)

        # Create a temporary prompt with placeholders for the transcript to calculate base tokens
        # Use the actual prompt_template structure
        temp_prompt_for_size_calc = prompt_template.format(
            feedback_section=sanitized_auditor_feedback,
            channel_name=sanitized_channel,
            title=sanitized_title,
            published_at=row['published_at'],
            transcript="{transcript_placeholder}" # Placeholder
        )
        
        # Calculate tokens used by the prompt template and metadata, excluding the transcript itself
        # Subtract tokens of placeholder, add tokens of empty string if placeholder was empty
        base_prompt_tokens = count_tokens(temp_prompt_for_size_calc) - count_tokens("{transcript_placeholder}") + count_tokens("")

        available_tokens_for_transcript = MAX_INPUT_PROMPT_TOKENS - base_prompt_tokens
        
        print(f"\\nProcessing video: {row['title']} (ID: {row['video_id']}) - Attempt {attempt + 1}/{MAX_PROCESSING_ATTEMPTS}")
        # print(f"MAX_INPUT_PROMPT_TOKENS: {MAX_INPUT_PROMPT_TOKENS}") # Debug
        # print(f"Base prompt tokens (excluding transcript): {base_prompt_tokens}") # Debug
        # print(f"Available tokens for transcript: {available_tokens_for_transcript}") # Debug

        if available_tokens_for_transcript < MIN_TRANSCRIPT_TOKENS_FOR_PROCESSING:
            print(f"Warning: Not enough tokens available for a meaningful transcript ({available_tokens_for_transcript} < {MIN_TRANSCRIPT_TOKENS_FOR_PROCESSING}). Marking as impossible for this attempt.")
            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                current_datetime = datetime.now().isoformat()
                supabase.table(table_name).update({
                    "llm_response": f"Error: Insufficient token space for transcript after {MAX_PROCESSING_ATTEMPTS} attempts. Available: {available_tokens_for_transcript}, Min required: {MIN_TRANSCRIPT_TOKENS_FOR_PROCESSING}",
                    "llm_call_date": current_datetime,
                    "processed": "impossible",
                    "is_visible": False
                }).eq("id", row['id']).execute()
                print(f"Marked video_id: {row['video_id']} as impossible due to insufficient token space for transcript.")
            # No specific auditor feedback to generate here, just continue to next attempt or fail
            auditor_feedback = "Previous attempt failed due to insufficient token space for transcript." # Generic feedback
            continue # Try next attempt, maybe feedback changes things, or fail if last attempt

        # Sanitize and truncate the original transcript
        sanitized_original_transcript = sanitize_text_for_prompt(row['transcript'])
        truncated_transcript = truncate_transcript(sanitized_original_transcript, available_tokens_for_transcript)
        
        # Check if truncated_transcript is empty after truncation when it shouldn't be
        if not truncated_transcript and available_tokens_for_transcript > 0 and count_tokens(sanitized_original_transcript) > 0 :
            print(f"Warning: Transcript became empty after truncation for video {row['video_id']}, despite available tokens. Original transcript was not empty.")
            # This might indicate an issue in truncate_transcript or extremely short available_tokens_for_transcript
            # For now, we'll proceed, but it might lead to a poor quality prompt.
            # If this is the last attempt, it might be marked impossible by subsequent checks.

        final_prompt = prompt_template.format(
            feedback_section=sanitized_auditor_feedback,
            channel_name=sanitized_channel,
            title=sanitized_title,
            published_at=row['published_at'],
            transcript=truncated_transcript
        )
        final_prompt_tokens = count_tokens(final_prompt)
        # print(f"Final prompt tokens: {final_prompt_tokens} (Budget: {MAX_INPUT_PROMPT_TOKENS})") # Debug

        if final_prompt_tokens > MAX_INPUT_PROMPT_TOKENS:
            print(f"Error: Prompt token calculation error. Final tokens {final_prompt_tokens} > Budget {MAX_INPUT_PROMPT_TOKENS}")
            # print(f"Length of failing prompt: {len(final_prompt)} chars, Tokens: {final_prompt_tokens}") # Debug
            # Log more details for this specific error case
            logging.error(f"Token calculation error for video_id: {row['video_id']}. Final tokens {final_prompt_tokens} > Budget {MAX_INPUT_PROMPT_TOKENS}. "
                          f"Base prompt tokens: {base_prompt_tokens}, Available for transcript: {available_tokens_for_transcript}, "
                          f"Truncated transcript tokens: {count_tokens(truncated_transcript)}, Sanitized feedback tokens: {count_tokens(sanitized_auditor_feedback)}")

            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                current_datetime = datetime.now().isoformat()
                supabase.table(table_name).update({
                    "llm_response": f"Error: Prompt token calculation error after {MAX_PROCESSING_ATTEMPTS} attempts. Final tokens {final_prompt_tokens} > Budget {MAX_INPUT_PROMPT_TOKENS}",
                    "llm_call_date": current_datetime,
                    "processed": "impossible",
                    "is_visible": False
                }).eq("id", row['id']).execute()
                print(f"Marked video_id: {row['video_id']} as impossible due to token calculation error.")
            auditor_feedback = "Previous attempt failed due to prompt exceeding token limits after transcript truncation. This might indicate an issue with the feedback loop or base prompt size."
            continue

        # print(f"\\nProcessing video: {row['title']}")
        # print(f"Video ID: {row['video_id']}")
        # print(f"Attempt: {attempt + 1}")
        # print(f"Prompt tokens: {count_tokens(final_prompt)}") # Already printed as final_prompt_tokens
        
        try:
            llm_response = await query_ollama_async(final_prompt, session)
            current_datetime = datetime.now().isoformat()
            
            if not llm_response or llm_response.strip() == "":
                print(f"Warning: Empty response for video {row['video_id']}")
                auditor_feedback = "Previous attempt resulted in an empty response from the AI."
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    supabase.table(table_name).update({
                        "llm_response": "Error: AI returned empty response",
                        "llm_call_date": current_datetime,
                        "processed": "impossible",
                        "is_visible": False
                    }).eq("id", row['id']).execute()
                    print(f"Marked video_id: {row['video_id']} as impossible due to empty response after {MAX_PROCESSING_ATTEMPTS} attempts.")
                continue
            
            try:
                llm_response_dict = json.loads(llm_response)
                is_valid_json = True
            except json.JSONDecodeError:
                is_valid_json = False
                print(f"Warning: Response for video {row['video_id']} is not valid JSON: {llm_response[:500]}...") # Log part of the invalid response
                auditor_feedback = f"Previous attempt resulted in invalid JSON. The AI returned: {llm_response[:200]}..."
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    supabase.table(table_name).update({
                        "llm_response": f"Error: Failed to generate valid JSON after {MAX_PROCESSING_ATTEMPTS} attempts. Last response snippet: {llm_response[:500]}",
                        "llm_call_date": current_datetime,
                        "processed": "impossible",
                        "is_visible": False
                    }).eq("id", row['id']).execute()
                    print(f"Marked video_id: {row['video_id']} as impossible due to invalid JSON after {MAX_PROCESSING_ATTEMPTS} attempts.")
                continue

            if is_valid_json:
                is_valid_structure, validation_message = validate_llm_response_structure(llm_response_dict)
                
                if not is_valid_structure:
                    print(f"Warning: Response structure invalid for video {row['video_id']}: {validation_message}")
                    print(f"--- Failing Prompt for Video ID {row['video_id']} (Attempt {attempt + 1}) ---")
                    # print(final_prompt) # This can be very long, consider logging to file or conditionally
                    print(f"Length of failing prompt: {len(final_prompt)} chars, Tokens: {final_prompt_tokens}")
                    print("--- End of Failing Prompt ---")
                    auditor_feedback = f"Previous attempt resulted in an invalid response structure: {validation_message}. The AI returned: {json.dumps(llm_response_dict, indent=2)[:500]}..."

                    if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                        supabase.table(table_name).update({
                            "llm_response": f"Error: Invalid response structure after {MAX_PROCESSING_ATTEMPTS} attempts - {validation_message}. Last response: {json.dumps(llm_response_dict, indent=2)[:500]}",
                            "llm_call_date": current_datetime,
                            "processed": "impossible",
                            "is_visible": False
                        }).eq("id", row['id']).execute()                        
                        print(f"Marked video_id: {row['video_id']} as impossible due to invalid structure after {MAX_PROCESSING_ATTEMPTS} attempts.")
                    continue
                
                # If structure is valid, proceed with saving or further validation if enabled
                if ENABLE_VALIDATION:
                    try:
                        # Pass the original final_prompt that led to this llm_response_str
                        validation_result = await validate_ollama_response_async(
                            original_prompt=final_prompt, # Pass the actual prompt used
                            ollama_response_str=llm_response, # Pass the raw string response
                            session=session,
                            attempt=attempt # Pass current attempt number for logging in validation
                        )
                        
                        # Assuming validation_result is a dict like: {"is_valid": True/False, "issues": []}
                        # or your previous structure: {"Approved": "YES"/"NO", "Reason": "...", "Improvement_Suggestions": []}
                        # Let's adapt to the {"is_valid": ..., "issues": ...} structure from the example validation_prompt
                        
                        is_approved_by_validator = validation_result.get("is_valid", False)
                        validation_issues = validation_result.get("issues", ["No specific issues provided by validator."])

                        if is_approved_by_validator:
                            update_data = {
                                "llm_response": llm_response_dict, # Save the parsed dict
                                "llm_call_date": current_datetime,
                                "processed": "completed",
                                # "auditor_feedback": "Validation passed." # Do not save auditor_feedback to DB
                            }
                            supabase.table(table_name).update(update_data).eq("id", row['id']).execute()
                            print(f"Successfully processed and validated video_id: {row['video_id']}")
                            return # Exit after successful processing
                        else:
                            print(f"Validation failed for video_id: {row['video_id']}. Issues: {validation_issues}")
                            auditor_feedback = f"""Previous Analysis Feedback (from validator):
- Issues: {'; '.join(validation_issues)}
- AI's response that failed validation: {llm_response[:300]}...
Please address these issues."""
                            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                                supabase.table(table_name).update({
                                    "llm_response": f"Error: Failed validation after {MAX_PROCESSING_ATTEMPTS} attempts. Last issues: {validation_issues}. Last AI response: {llm_response[:500]}",
                                    "llm_call_date": current_datetime,
                                    "processed": "impossible",
                                    "is_visible": False,
                                    # "auditor_feedback": auditor_feedback # Do not save auditor_feedback to DB
                                }).eq("id", row['id']).execute()
                                print(f"Marked video_id: {row['video_id']} as impossible after failing validation {MAX_PROCESSING_ATTEMPTS} times.")
                            continue # To the next attempt with new auditor_feedback
                    except asyncio.TimeoutError:
                        print(f"Validation timed out for video ID: {row['video_id']}")
                        auditor_feedback = "Previous attempt's validation timed out."
                        if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                             # Mark as impossible if validation consistently times out
                            supabase.table(table_name).update({
                                "llm_response": "Error: Validation timed out repeatedly.",
                                "llm_call_date": current_datetime,
                                "processed": "impossible",
                                "is_visible": False
                            }).eq("id", row['id']).execute()
                        continue
                    except Exception as e:
                        print(f"Error during validation for video ID: {row['video_id']}. Error: {str(e)}")
                        auditor_feedback = f"Previous attempt's validation failed with an error: {str(e)}"
                        if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                            supabase.table(table_name).update({
                                "llm_response": f"Error: Validation process failed with exception: {str(e)}",
                                "llm_call_date": current_datetime,
                                "processed": "impossible",
                                "is_visible": False
                            }).eq("id", row['id']).execute()
                        continue
                else: # ENABLE_VALIDATION is False
                    update_data = {
                        "llm_response": llm_response_dict, # Save the parsed dict
                        "llm_call_date": current_datetime,
                        "processed": "completed"
                        # "auditor_feedback": "" # Do not save auditor_feedback to DB
                    }
                    supabase.table(table_name).update(update_data).eq("id", row['id']).execute()
                    print(f"Successfully processed video_id: {row['video_id']} (validation disabled)")
                    return # Exit after successful processing
        
        except asyncio.TimeoutError:
            print(f"Ollama query timed out for video ID: {row['video_id']} on attempt {attempt + 1}")
            auditor_feedback = "Previous Ollama query timed out."
            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                current_datetime = datetime.now().isoformat()
                supabase.table(table_name).update({
                    "llm_response": f"Error: Ollama query timed out after {MAX_PROCESSING_ATTEMPTS} attempts",
                    "llm_call_date": current_datetime,
                    "processed": "impossible",
                    "is_visible": False
                }).eq("id", row['id']).execute()
                print(f"Marked video_id: {row['video_id']} as Impossible due to repeated Ollama timeouts")
            continue # To the next attempt

        except Exception as e:
            print(f"Ollama query failed for video ID: {row['video_id']} on attempt {attempt + 1}. Error: {str(e)}")
            auditor_feedback = f"Previous Ollama query failed with error: {str(e)}"
            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                current_datetime = datetime.now().isoformat()
                supabase.table(table_name).update({
                    "llm_response": f"Error: Ollama query failed after {MAX_PROCESSING_ATTEMPTS} attempts. Last error: {str(e)}",
                    "llm_call_date": current_datetime,
                    "processed": "impossible",
                    "is_visible": False
                }).eq("id", row['id']).execute()
                print(f"Marked video_id: {row['video_id']} as Impossible due to repeated Ollama query failures")
            continue # To the next attempt
    
    # If loop finishes, all attempts were exhausted
    current_datetime = datetime.now().isoformat()
    final_error_message = f"Error: Failed to generate a valid response after {MAX_PROCESSING_ATTEMPTS} attempts. Last feedback for retry: {auditor_feedback}"
    supabase.table(table_name).update({
        "llm_response": final_error_message,
        "llm_call_date": current_datetime,
        "processed": "impossible",
        "is_visible": False,
        # "auditor_feedback": auditor_feedback # Do not save auditor_feedback to DB
    }).eq("id", row['id']).execute()
    print(f"Marked video_id: {row['video_id']} as impossible after {MAX_PROCESSING_ATTEMPTS} attempts. Last error leading to this: {auditor_feedback}")

# Add these new functions at an appropriate place in your script:

def get_unprocessed_count(table_name):
    # Only count "pending" rows as unprocessed
    response = supabase.table(table_name).select("id", count="exact").eq("processed", "pending").execute()
    return response.count

def get_total_count(table_name):
    response = supabase.table(table_name).select("id", count="exact").execute()
    return response.count

def calculate_progress_and_eta(start_time, total_unprocessed, current_unprocessed):
    elapsed_time = time.time() - start_time
    processed = total_unprocessed - current_unprocessed
    
    if processed == 0:
        return 0, "N/A"
    
    percentage = (processed / total_unprocessed) * 100
    
    time_per_item = elapsed_time / processed
    remaining_time = current_unprocessed * time_per_item
    
    eta = datetime.now() + timedelta(seconds=remaining_time)
    eta_str = eta.strftime("%Y-%m-%d %H:%M:%S")
    
    return percentage, eta_str

# Modify the process_table_async function:
async def process_table_async(table_name):
    print(f"Processing table: {table_name}")
    print(f"Processing {PARALLEL_REQUESTS} rows in parallel")  # Added logging
    page_size = 1000
    offset = 0
    processed_count = 0
    
    total_unprocessed = get_unprocessed_count(table_name)
    total_rows = get_total_count(table_name)
    start_time = time.time()
    
    print(f"Total unprocessed rows in {table_name}: {total_unprocessed}")
    print(f"Total rows in {table_name}: {total_rows}")
    
    timeout = aiohttp.ClientTimeout(total=0)  # 0 means no timeout
    async with aiohttp.ClientSession(timeout=timeout) as session:
        while True:
            print(f"Fetching rows {offset} to {offset + page_size} from {table_name}")
            response = supabase.table(table_name).select("*").eq("processed", "pending").range(offset, offset + page_size).execute()
            rows = response.data
            
            print(f"Fetched {len(rows)} rows from {table_name}")
            
            if not rows:
                print(f"No more rows to process in {table_name}")
                break
            
            # Process rows in parallel batches using PARALLEL_REQUESTS constant
            for i in range(0, len(rows), PARALLEL_REQUESTS):
                tasks = []
                for j in range(PARALLEL_REQUESTS):
                    if i + j < len(rows):
                        tasks.append(process_row_async(rows[i + j], table_name, session))
                
                print(f"Processing rows {i} to {i+len(tasks)} in {table_name}")
                await asyncio.gather(*tasks)
                processed_count += len(tasks)
                
                current_unprocessed = total_unprocessed - processed_count
                percentage, eta = calculate_progress_and_eta(start_time, total_unprocessed, current_unprocessed)
                
                print(f"Processed {processed_count}/{total_unprocessed} unprocessed rows ({percentage:.2f}% complete).")
                print(f"Estimated completion time: {eta}")
                print(f"Total progress: {((total_rows - current_unprocessed) / total_rows) * 100:.2f}% of all rows")
            
            offset += len(rows)
            
            if ROWS_PER_TABLE != -1 and offset >= ROWS_PER_TABLE:
                print(f"Reached ROWS_PER_TABLE limit for {table_name}")
                break
    
    print(f"Finished processing {processed_count} rows in table: {table_name}")

# Modify the main_async function:
async def main_async():
    start_time = time.time()
    
    total_unprocessed = sum(get_unprocessed_count(table) for table in tables_to_process)
    total_rows = sum(get_total_count(table) for table in tables_to_process)
    print(f"Total unprocessed rows across all tables: {total_unprocessed}")
    print(f"Total rows across all tables: {total_rows}")
    
    for table_name in tables_to_process:
        table_start_time = time.time()
        await process_table_async_with_timeout(table_name)
        table_end_time = time.time()
        table_time = table_end_time - table_start_time
        print(f"Time taken for table {table_name}: {table_time:.2f} seconds")
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\nTotal time taken: {total_time:.2f} seconds")

async def process_table_async_with_timeout(table_name):
    try:
        # No timeout specified - will run until completion
        await process_table_async(table_name)
    except Exception as e:
        print(f"ERROR: Failed to process table {table_name}: {str(e)}")
        # Log the error but continue with next table
        current_datetime = datetime.now().isoformat()
        supabase.table(table_name).update({
            "llm_response": f"Error: Processing failed - {str(e)}",
            "llm_call_date": current_datetime,
            "processed": "pending"  # Keep as pending so it will be retried next run
        }).eq("processed", "pending").execute()

def validate_and_format_llm_response(response_string_or_dict):
    """Ensures consistent JSONB structure regardless of input type"""
    try:
        # Convert to dict if it's a string
        if isinstance(response_string_or_dict, str):
            response_dict = json.loads(response_string_or_dict)
        else:
            response_dict = response_string_or_dict
            
        # Validate required fields
        required_fields = ["keywords", "questions", "insights", "recommendations"]
        for field in required_fields:
            if field not in response_dict:
                raise ValueError(f"Missing required field: {field}")
            if not isinstance(response_dict[field], list):
                raise ValueError(f"{field} must be a list")
            
        return response_dict
    except Exception as e:
        raise ValueError(f"Invalid LLM response structure: {str(e)}")

if __name__ == "__main__":
    # --- Ollama Check --- 
    # IMPORTANT: Update 'ollama_start_cmd' if your command to start Ollama is different.
    # Examples: "ollama serve", r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe serve"
    ollama_start_cmd = r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe serve"  # Path automatically detected

    print(f"INFO: Using Ollama start command: '{ollama_start_cmd}'. Ensure this is correct for your system.")
    # Use a logger if available, otherwise fallback to print for this initial check's logging
    if 'logging' in globals():
        logging.info(f"Using Ollama start command: '{ollama_start_cmd}'. Ensure this is correct.")
    else:
        print(f"LOG_INFO: Using Ollama start command: '{ollama_start_cmd}'. Ensure this is correct.")

    if not check_and_start_ollama(ollama_start_cmd):
        print("CRITICAL: Failed to ensure Ollama is running. The script heavily relies on Ollama and will now exit.")
        if 'logging' in globals():
            logging.critical("Failed to ensure Ollama is running. Script will exit.")
        else:
            print("LOG_CRITICAL: Failed to ensure Ollama is running. Script will exit.")
        sys.exit(1)  # Exit if Ollama check/start fails
    else:
        print("INFO: Ollama check passed. Proceeding with the script.")
        if 'logging' in globals():
            logging.info("Ollama check passed. Proceeding with script execution.")
        else:
            print("LOG_INFO: Ollama check passed. Proceeding with script execution.")
    # --- End Ollama Check ---

    asyncio.run(main_async())
