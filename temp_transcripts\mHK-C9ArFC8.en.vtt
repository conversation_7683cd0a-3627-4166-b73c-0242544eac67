WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.670 align:start position:0%
 
hey<00:00:00.280><c> and</c><00:00:00.399><c> welcome</c><00:00:00.640><c> back</c><00:00:00.760><c> to</c><00:00:01.000><c> another</c><00:00:01.360><c> video</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
hey and welcome back to another video
 

00:00:01.680 --> 00:00:03.750 align:start position:0%
hey and welcome back to another video
and<00:00:01.880><c> today</c><00:00:02.200><c> we're</c><00:00:02.360><c> going</c><00:00:02.480><c> to</c><00:00:02.600><c> be</c><00:00:02.760><c> going</c><00:00:03.080><c> over</c><00:00:03.560><c> a</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
and today we're going to be going over a
 

00:00:03.760 --> 00:00:06.150 align:start position:0%
and today we're going to be going over a
math<00:00:04.240><c> agent</c><00:00:04.799><c> this</c><00:00:05.000><c> agent</c><00:00:05.440><c> will</c><00:00:05.640><c> give</c><00:00:05.839><c> better</c>

00:00:06.150 --> 00:00:06.160 align:start position:0%
math agent this agent will give better
 

00:00:06.160 --> 00:00:08.870 align:start position:0%
math agent this agent will give better
responses<00:00:06.759><c> and</c><00:00:06.919><c> more</c><00:00:07.279><c> accurate</c><00:00:07.680><c> solutions</c><00:00:08.559><c> to</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
responses and more accurate solutions to
 

00:00:08.880 --> 00:00:11.230 align:start position:0%
responses and more accurate solutions to
more<00:00:09.240><c> complex</c><00:00:09.719><c> math</c><00:00:10.080><c> problems</c><00:00:10.759><c> the</c><00:00:10.960><c> math</c>

00:00:11.230 --> 00:00:11.240 align:start position:0%
more complex math problems the math
 

00:00:11.240 --> 00:00:13.950 align:start position:0%
more complex math problems the math
agent<00:00:11.599><c> is</c><00:00:11.759><c> just</c><00:00:11.920><c> a</c><00:00:12.160><c> custom</c><00:00:12.599><c> user</c><00:00:13.080><c> proxy</c><00:00:13.559><c> agent</c>

00:00:13.950 --> 00:00:13.960 align:start position:0%
agent is just a custom user proxy agent
 

00:00:13.960 --> 00:00:15.829 align:start position:0%
agent is just a custom user proxy agent
it<00:00:14.080><c> goes</c><00:00:14.280><c> through</c><00:00:14.440><c> a</c><00:00:14.599><c> series</c><00:00:14.960><c> of</c><00:00:15.200><c> steps</c><00:00:15.639><c> of</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
it goes through a series of steps of
 

00:00:15.839 --> 00:00:18.429 align:start position:0%
it goes through a series of steps of
better<00:00:16.359><c> prompting</c><00:00:17.320><c> which</c><00:00:17.480><c> is</c><00:00:17.640><c> what</c><00:00:17.840><c> allows</c><00:00:18.199><c> it</c>

00:00:18.429 --> 00:00:18.439 align:start position:0%
better prompting which is what allows it
 

00:00:18.439 --> 00:00:20.870 align:start position:0%
better prompting which is what allows it
to<00:00:18.840><c> solve</c><00:00:19.199><c> these</c><00:00:19.359><c> more</c><00:00:19.760><c> complex</c><00:00:20.359><c> problems</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
to solve these more complex problems
 

00:00:20.880 --> 00:00:22.670 align:start position:0%
to solve these more complex problems
break<00:00:21.160><c> them</c><00:00:21.439><c> down</c><00:00:21.880><c> and</c><00:00:22.000><c> then</c><00:00:22.160><c> give</c><00:00:22.359><c> better</c>

00:00:22.670 --> 00:00:22.680 align:start position:0%
break them down and then give better
 

00:00:22.680 --> 00:00:24.150 align:start position:0%
break them down and then give better
responses<00:00:23.400><c> there</c><00:00:23.480><c> are</c><00:00:23.599><c> essentially</c><00:00:24.000><c> two</c>

00:00:24.150 --> 00:00:24.160 align:start position:0%
responses there are essentially two
 

00:00:24.160 --> 00:00:25.550 align:start position:0%
responses there are essentially two
different<00:00:24.480><c> types</c><00:00:24.720><c> of</c><00:00:24.880><c> prompting</c><00:00:25.320><c> that</c><00:00:25.400><c> it</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
different types of prompting that it
 

00:00:25.560 --> 00:00:27.509 align:start position:0%
different types of prompting that it
uses<00:00:25.920><c> to</c><00:00:26.080><c> help</c><00:00:26.400><c> the</c><00:00:26.519><c> first</c><00:00:26.679><c> one</c><00:00:26.800><c> is</c><00:00:26.920><c> tool</c><00:00:27.160><c> using</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
uses to help the first one is tool using
 

00:00:27.519 --> 00:00:29.470 align:start position:0%
uses to help the first one is tool using
prompt<00:00:28.080><c> which</c><00:00:28.240><c> guides</c><00:00:28.519><c> the</c><00:00:28.599><c> LM</c><00:00:29.000><c> assistant</c><00:00:29.359><c> to</c>

00:00:29.470 --> 00:00:29.480 align:start position:0%
prompt which guides the LM assistant to
 

00:00:29.480 --> 00:00:31.630 align:start position:0%
prompt which guides the LM assistant to
use<00:00:29.679><c> python</c><00:00:30.119><c> code</c><00:00:30.320><c> in</c><00:00:30.400><c> the</c><00:00:30.519><c> correct</c><00:00:30.920><c> format</c><00:00:31.320><c> to</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
use python code in the correct format to
 

00:00:31.640 --> 00:00:33.310 align:start position:0%
use python code in the correct format to
address<00:00:32.000><c> the</c><00:00:32.200><c> problem</c><00:00:32.599><c> and</c><00:00:32.680><c> then</c><00:00:32.840><c> we</c><00:00:32.960><c> have</c><00:00:33.079><c> the</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
address the problem and then we have the
 

00:00:33.320 --> 00:00:35.190 align:start position:0%
address the problem and then we have the
problem<00:00:33.640><c> solving</c><00:00:34.120><c> strategy</c><00:00:34.640><c> selection</c>

00:00:35.190 --> 00:00:35.200 align:start position:0%
problem solving strategy selection
 

00:00:35.200 --> 00:00:36.790 align:start position:0%
problem solving strategy selection
prompt<00:00:35.559><c> and</c><00:00:35.680><c> there</c><00:00:35.760><c> are</c><00:00:35.920><c> three</c><00:00:36.160><c> steps</c><00:00:36.399><c> to</c><00:00:36.559><c> this</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
prompt and there are three steps to this
 

00:00:36.800 --> 00:00:39.150 align:start position:0%
prompt and there are three steps to this
and<00:00:36.920><c> it</c><00:00:37.079><c> goes</c><00:00:37.440><c> in</c><00:00:37.640><c> order</c><00:00:38.079><c> so</c><00:00:38.280><c> the</c><00:00:38.480><c> first</c><00:00:38.719><c> one</c><00:00:39.040><c> is</c>

00:00:39.150 --> 00:00:39.160 align:start position:0%
and it goes in order so the first one is
 

00:00:39.160 --> 00:00:41.190 align:start position:0%
and it goes in order so the first one is
it<00:00:39.280><c> tries</c><00:00:39.520><c> to</c><00:00:39.640><c> Rite</c><00:00:39.920><c> a</c><00:00:40.160><c> Python</c><00:00:40.600><c> program</c><00:00:41.000><c> to</c>

00:00:41.190 --> 00:00:41.200 align:start position:0%
it tries to Rite a Python program to
 

00:00:41.200 --> 00:00:42.549 align:start position:0%
it tries to Rite a Python program to
solve<00:00:41.480><c> the</c><00:00:41.640><c> problem</c><00:00:41.920><c> directly</c><00:00:42.320><c> and</c><00:00:42.399><c> if</c><00:00:42.480><c> it</c>

00:00:42.549 --> 00:00:42.559 align:start position:0%
solve the problem directly and if it
 

00:00:42.559 --> 00:00:43.830 align:start position:0%
solve the problem directly and if it
doesn't<00:00:42.760><c> think</c><00:00:42.920><c> it</c><00:00:43.000><c> can</c><00:00:43.200><c> do</c><00:00:43.399><c> that</c><00:00:43.640><c> then</c><00:00:43.719><c> it</c>

00:00:43.830 --> 00:00:43.840 align:start position:0%
doesn't think it can do that then it
 

00:00:43.840 --> 00:00:45.549 align:start position:0%
doesn't think it can do that then it
tries<00:00:44.039><c> to</c><00:00:44.200><c> solve</c><00:00:44.440><c> the</c><00:00:44.640><c> problem</c><00:00:45.120><c> with</c><00:00:45.280><c> just</c><00:00:45.440><c> the</c>

00:00:45.549 --> 00:00:45.559 align:start position:0%
tries to solve the problem with just the
 

00:00:45.559 --> 00:00:47.950 align:start position:0%
tries to solve the problem with just the
llms<00:00:46.239><c> inherent</c><00:00:46.719><c> reasoning</c><00:00:47.239><c> capability</c><00:00:47.879><c> and</c>

00:00:47.950 --> 00:00:47.960 align:start position:0%
llms inherent reasoning capability and
 

00:00:47.960 --> 00:00:49.189 align:start position:0%
llms inherent reasoning capability and
if<00:00:48.079><c> it</c><00:00:48.160><c> doesn't</c><00:00:48.360><c> think</c><00:00:48.480><c> it</c><00:00:48.559><c> can</c><00:00:48.719><c> do</c><00:00:48.879><c> that</c><00:00:49.079><c> then</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
if it doesn't think it can do that then
 

00:00:49.199 --> 00:00:51.029 align:start position:0%
if it doesn't think it can do that then
it<00:00:49.320><c> tries</c><00:00:49.559><c> to</c><00:00:49.719><c> solve</c><00:00:49.960><c> the</c><00:00:50.160><c> problem</c><00:00:50.520><c> step</c><00:00:50.800><c> by</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
it tries to solve the problem step by
 

00:00:51.039 --> 00:00:53.069 align:start position:0%
it tries to solve the problem step by
step<00:00:51.280><c> with</c><00:00:51.520><c> python</c><00:00:51.920><c> code</c><00:00:52.399><c> when</c><00:00:52.600><c> they</c><00:00:52.760><c> did</c>

00:00:53.069 --> 00:00:53.079 align:start position:0%
step with python code when they did
 

00:00:53.079 --> 00:00:55.229 align:start position:0%
step with python code when they did
experimental<00:00:54.000><c> results</c><00:00:54.640><c> they</c><00:00:54.760><c> came</c><00:00:54.920><c> up</c><00:00:55.079><c> with</c>

00:00:55.229 --> 00:00:55.239 align:start position:0%
experimental results they came up with
 

00:00:55.239 --> 00:00:56.830 align:start position:0%
experimental results they came up with
four<00:00:55.440><c> different</c><00:00:55.719><c> ways</c><00:00:55.920><c> to</c><00:00:56.039><c> solve</c><00:00:56.280><c> the</c><00:00:56.480><c> problem</c>

00:00:56.830 --> 00:00:56.840 align:start position:0%
four different ways to solve the problem
 

00:00:56.840 --> 00:00:58.910 align:start position:0%
four different ways to solve the problem
the<00:00:56.960><c> first</c><00:00:57.160><c> one</c><00:00:57.320><c> being</c><00:00:57.600><c> vanilla</c><00:00:58.120><c> just</c><00:00:58.480><c> simply</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
the first one being vanilla just simply
 

00:00:58.920 --> 00:01:01.110 align:start position:0%
the first one being vanilla just simply
asking<00:00:59.239><c> the</c><00:00:59.399><c> llm</c><00:01:00.399><c> with</c><00:01:00.559><c> its</c><00:01:00.719><c> reasoning</c>

00:01:01.110 --> 00:01:01.120 align:start position:0%
asking the llm with its reasoning
 

00:01:01.120 --> 00:01:02.869 align:start position:0%
asking the llm with its reasoning
capabilities<00:01:01.640><c> to</c><00:01:01.879><c> solve</c><00:01:02.160><c> the</c><00:01:02.320><c> problem</c><00:01:02.760><c> and</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
capabilities to solve the problem and
 

00:01:02.879 --> 00:01:04.670 align:start position:0%
capabilities to solve the problem and
the<00:01:03.000><c> matchat</c><00:01:03.440><c> agent</c><00:01:03.719><c> performed</c><00:01:04.320><c> better</c><00:01:04.559><c> than</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
the matchat agent performed better than
 

00:01:04.680 --> 00:01:06.990 align:start position:0%
the matchat agent performed better than
the<00:01:04.920><c> other</c><00:01:05.239><c> types</c><00:01:05.439><c> of</c><00:01:05.640><c> prompting</c><00:01:06.200><c> in</c><00:01:06.520><c> most</c>

00:01:06.990 --> 00:01:07.000 align:start position:0%
the other types of prompting in most
 

00:01:07.000 --> 00:01:09.149 align:start position:0%
the other types of prompting in most
categories<00:01:07.600><c> and</c><00:01:07.720><c> if</c><00:01:07.880><c> it</c><00:01:08.000><c> didn't</c><00:01:08.640><c> beat</c><00:01:08.880><c> out</c><00:01:09.040><c> the</c>

00:01:09.149 --> 00:01:09.159 align:start position:0%
categories and if it didn't beat out the
 

00:01:09.159 --> 00:01:10.710 align:start position:0%
categories and if it didn't beat out the
other<00:01:09.360><c> categories</c><00:01:09.799><c> of</c><00:01:09.920><c> prompting</c><00:01:10.360><c> it</c><00:01:10.520><c> came</c>

00:01:10.710 --> 00:01:10.720 align:start position:0%
other categories of prompting it came
 

00:01:10.720 --> 00:01:13.990 align:start position:0%
other categories of prompting it came
close<00:01:11.000><c> in</c><00:01:11.200><c> second</c><00:01:11.720><c> algebra</c><00:01:12.360><c> winning</c><00:01:12.759><c> at</c><00:01:13.000><c> 60%</c>

00:01:13.990 --> 00:01:14.000 align:start position:0%
close in second algebra winning at 60%
 

00:01:14.000 --> 00:01:15.950 align:start position:0%
close in second algebra winning at 60%
being<00:01:14.400><c> far</c><00:01:14.720><c> better</c><00:01:15.000><c> than</c><00:01:15.159><c> the</c><00:01:15.280><c> other</c><00:01:15.560><c> types</c><00:01:15.759><c> of</c>

00:01:15.950 --> 00:01:15.960 align:start position:0%
being far better than the other types of
 

00:01:15.960 --> 00:01:17.950 align:start position:0%
being far better than the other types of
prompting<00:01:16.400><c> you</c><00:01:16.520><c> can</c><00:01:16.759><c> also</c><00:01:17.000><c> use</c><00:01:17.400><c> wolf</c><00:01:17.720><c> from</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
prompting you can also use wolf from
 

00:01:17.960 --> 00:01:20.830 align:start position:0%
prompting you can also use wolf from
Alpha's<00:01:18.560><c> API</c><00:01:19.159><c> calls</c><00:01:19.600><c> to</c><00:01:19.840><c> help</c><00:01:20.040><c> you</c><00:01:20.280><c> solve</c><00:01:20.600><c> the</c>

00:01:20.830 --> 00:01:20.840 align:start position:0%
Alpha's API calls to help you solve the
 

00:01:20.840 --> 00:01:22.550 align:start position:0%
Alpha's API calls to help you solve the
problems<00:01:21.320><c> with</c><00:01:21.479><c> the</c><00:01:21.600><c> math</c><00:01:21.840><c> agent</c><00:01:22.240><c> now</c><00:01:22.360><c> let's</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
problems with the math agent now let's
 

00:01:22.560 --> 00:01:24.429 align:start position:0%
problems with the math agent now let's
see<00:01:22.720><c> how</c><00:01:22.840><c> we</c><00:01:22.920><c> can</c><00:01:23.119><c> use</c><00:01:23.320><c> the</c><00:01:23.439><c> math</c><00:01:23.680><c> agent</c><00:01:24.280><c> okay</c>

00:01:24.429 --> 00:01:24.439 align:start position:0%
see how we can use the math agent okay
 

00:01:24.439 --> 00:01:26.429 align:start position:0%
see how we can use the math agent okay
so<00:01:24.560><c> let's</c><00:01:24.759><c> get</c><00:01:24.880><c> started</c><00:01:25.159><c> we</c><00:01:25.280><c> need</c><00:01:25.640><c> two</c><00:01:25.920><c> files</c>

00:01:26.429 --> 00:01:26.439 align:start position:0%
so let's get started we need two files
 

00:01:26.439 --> 00:01:28.710 align:start position:0%
so let's get started we need two files
we<00:01:26.640><c> need</c><00:01:26.920><c> a</c><00:01:27.079><c> main</c><00:01:27.439><c> python</c><00:01:27.840><c> file</c><00:01:28.200><c> and</c><00:01:28.360><c> then</c><00:01:28.520><c> that</c>

00:01:28.710 --> 00:01:28.720 align:start position:0%
we need a main python file and then that
 

00:01:28.720 --> 00:01:30.990 align:start position:0%
we need a main python file and then that
open<00:01:29.040><c> AI</c><00:01:29.360><c> config</c><00:01:29.680><c> list</c><00:01:30.000><c> Json</c><00:01:30.400><c> file</c><00:01:30.680><c> if</c><00:01:30.759><c> we</c><00:01:30.880><c> look</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
open AI config list Json file if we look
 

00:01:31.000 --> 00:01:33.789 align:start position:0%
open AI config list Json file if we look
at<00:01:31.119><c> the</c><00:01:31.320><c> open</c><00:01:31.640><c> AI</c><00:01:31.920><c> config</c><00:01:32.280><c> list</c><00:01:32.479><c> Json</c><00:01:32.880><c> file</c><00:01:33.680><c> all</c>

00:01:33.789 --> 00:01:33.799 align:start position:0%
at the open AI config list Json file all
 

00:01:33.799 --> 00:01:37.030 align:start position:0%
at the open AI config list Json file all
you<00:01:33.920><c> need</c><00:01:34.200><c> here</c><00:01:34.560><c> is</c><00:01:34.840><c> a</c><00:01:35.079><c> model</c><00:01:35.560><c> and</c><00:01:35.720><c> an</c><00:01:35.960><c> API</c><00:01:36.560><c> key</c>

00:01:37.030 --> 00:01:37.040 align:start position:0%
you need here is a model and an API key
 

00:01:37.040 --> 00:01:38.950 align:start position:0%
you need here is a model and an API key
and<00:01:37.240><c> again</c><00:01:37.520><c> if</c><00:01:37.600><c> you</c><00:01:37.720><c> want</c><00:01:38.000><c> to</c><00:01:38.200><c> use</c><00:01:38.399><c> a</c><00:01:38.600><c> local</c>

00:01:38.950 --> 00:01:38.960 align:start position:0%
and again if you want to use a local
 

00:01:38.960 --> 00:01:41.550 align:start position:0%
and again if you want to use a local
open<00:01:39.240><c> source</c><00:01:39.479><c> llm</c><00:01:40.360><c> you</c><00:01:40.479><c> can</c><00:01:40.720><c> also</c><00:01:41.000><c> just</c><00:01:41.200><c> add</c><00:01:41.399><c> in</c>

00:01:41.550 --> 00:01:41.560 align:start position:0%
open source llm you can also just add in
 

00:01:41.560 --> 00:01:43.870 align:start position:0%
open source llm you can also just add in
here<00:01:41.759><c> the</c><00:01:42.000><c> base</c><00:01:42.439><c> URL</c><00:01:43.159><c> and</c><00:01:43.280><c> then</c><00:01:43.439><c> give</c><00:01:43.560><c> it</c><00:01:43.720><c> the</c>

00:01:43.870 --> 00:01:43.880 align:start position:0%
here the base URL and then give it the
 

00:01:43.880 --> 00:01:46.950 align:start position:0%
here the base URL and then give it the
HTTP<00:01:44.799><c> URL</c><00:01:45.479><c> from</c><00:01:45.880><c> whichever</c><00:01:46.320><c> software</c><00:01:46.759><c> or</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
HTTP URL from whichever software or
 

00:01:46.960 --> 00:01:48.429 align:start position:0%
HTTP URL from whichever software or
Library<00:01:47.360><c> you're</c><00:01:47.479><c> using</c><00:01:47.840><c> for</c><00:01:47.960><c> instance</c><00:01:48.320><c> with</c>

00:01:48.429 --> 00:01:48.439 align:start position:0%
Library you're using for instance with
 

00:01:48.439 --> 00:01:50.830 align:start position:0%
Library you're using for instance with
LM<00:01:48.759><c> Studio</c><00:01:49.479><c> you</c><00:01:49.600><c> can</c><00:01:49.719><c> use</c><00:01:49.960><c> this</c><00:01:50.079><c> URL</c><00:01:50.600><c> and</c><00:01:50.680><c> then</c>

00:01:50.830 --> 00:01:50.840 align:start position:0%
LM Studio you can use this URL and then
 

00:01:50.840 --> 00:01:52.590 align:start position:0%
LM Studio you can use this URL and then
back<00:01:50.960><c> at</c><00:01:51.079><c> the</c><00:01:51.200><c> main</c><00:01:51.439><c> python</c><00:01:51.799><c> file</c><00:01:52.280><c> we</c><00:01:52.399><c> don't</c>

00:01:52.590 --> 00:01:52.600 align:start position:0%
back at the main python file we don't
 

00:01:52.600 --> 00:01:54.510 align:start position:0%
back at the main python file we don't
need<00:01:52.799><c> a</c><00:01:53.000><c> lot</c><00:01:53.159><c> of</c><00:01:53.320><c> code</c><00:01:53.600><c> here</c><00:01:53.880><c> so</c><00:01:54.040><c> I'll</c><00:01:54.200><c> just</c><00:01:54.360><c> go</c>

00:01:54.510 --> 00:01:54.520 align:start position:0%
need a lot of code here so I'll just go
 

00:01:54.520 --> 00:01:55.950 align:start position:0%
need a lot of code here so I'll just go
through<00:01:54.680><c> this</c><00:01:54.880><c> quickly</c><00:01:55.320><c> first</c><00:01:55.520><c> we</c><00:01:55.640><c> need</c><00:01:55.799><c> the</c>

00:01:55.950 --> 00:01:55.960 align:start position:0%
through this quickly first we need the
 

00:01:55.960 --> 00:01:57.749 align:start position:0%
through this quickly first we need the
Imports<00:01:56.520><c> and</c><00:01:56.640><c> because</c><00:01:56.840><c> we're</c><00:01:56.960><c> using</c><00:01:57.159><c> autogen</c>

00:01:57.749 --> 00:01:57.759 align:start position:0%
Imports and because we're using autogen
 

00:01:57.759 --> 00:01:59.510 align:start position:0%
Imports and because we're using autogen
and<00:01:57.920><c> if</c><00:01:58.039><c> you</c><00:01:58.200><c> haven't</c><00:01:58.439><c> installed</c><00:01:58.759><c> it</c><00:01:58.920><c> already</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
and if you haven't installed it already
 

00:01:59.520 --> 00:02:01.190 align:start position:0%
and if you haven't installed it already
you'll<00:01:59.680><c> just</c><00:01:59.960><c> simply</c><00:02:00.200><c> open</c><00:02:00.360><c> up</c><00:02:00.479><c> your</c><00:02:00.600><c> terminal</c>

00:02:01.190 --> 00:02:01.200 align:start position:0%
you'll just simply open up your terminal
 

00:02:01.200 --> 00:02:04.109 align:start position:0%
you'll just simply open up your terminal
and<00:02:01.280><c> you'll</c><00:02:01.520><c> type</c><00:02:01.759><c> pip</c><00:02:02.360><c> install</c><00:02:03.119><c> Pi</c><00:02:03.520><c> autogen</c>

00:02:04.109 --> 00:02:04.119 align:start position:0%
and you'll type pip install Pi autogen
 

00:02:04.119 --> 00:02:05.590 align:start position:0%
and you'll type pip install Pi autogen
once<00:02:04.280><c> that's</c><00:02:04.439><c> done</c><00:02:04.600><c> installing</c><00:02:05.280><c> the</c><00:02:05.399><c> next</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
once that's done installing the next
 

00:02:05.600 --> 00:02:07.109 align:start position:0%
once that's done installing the next
thing<00:02:05.759><c> is</c><00:02:05.920><c> the</c><00:02:06.119><c> configuration</c><00:02:06.920><c> we're</c><00:02:07.039><c> going</c>

00:02:07.109 --> 00:02:07.119 align:start position:0%
thing is the configuration we're going
 

00:02:07.119 --> 00:02:09.389 align:start position:0%
thing is the configuration we're going
to<00:02:07.200><c> say</c><00:02:07.360><c> config</c><00:02:07.680><c> list</c><00:02:07.920><c> equals</c><00:02:08.360><c> autogen</c><00:02:09.160><c> doc</c>

00:02:09.389 --> 00:02:09.399 align:start position:0%
to say config list equals autogen doc
 

00:02:09.399 --> 00:02:12.430 align:start position:0%
to say config list equals autogen doc
config<00:02:09.759><c> list</c><00:02:10.080><c> from</c><00:02:10.480><c> Json</c><00:02:11.480><c> and</c><00:02:11.680><c> all</c><00:02:11.800><c> we</c><00:02:11.959><c> need</c><00:02:12.239><c> is</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
config list from Json and all we need is
 

00:02:12.440 --> 00:02:14.990 align:start position:0%
config list from Json and all we need is
this<00:02:12.760><c> open</c><00:02:13.080><c> AI</c><00:02:13.360><c> config</c><00:02:13.680><c> list.</c><00:02:14.000><c> Json</c><00:02:14.400><c> file</c><00:02:14.840><c> that</c>

00:02:14.990 --> 00:02:15.000 align:start position:0%
this open AI config list. Json file that
 

00:02:15.000 --> 00:02:17.949 align:start position:0%
this open AI config list. Json file that
we<00:02:15.239><c> just</c><00:02:15.480><c> created</c><00:02:16.000><c> you</c><00:02:16.080><c> can</c><00:02:16.239><c> also</c><00:02:16.480><c> say</c><00:02:17.040><c> EnV</c><00:02:17.680><c> or</c>

00:02:17.949 --> 00:02:17.959 align:start position:0%
we just created you can also say EnV or
 

00:02:17.959 --> 00:02:21.110 align:start position:0%
we just created you can also say EnV or
file<00:02:18.480><c> equals</c><00:02:19.120><c> this</c><00:02:19.280><c> Json</c><00:02:19.800><c> file</c><00:02:20.599><c> but</c><00:02:20.800><c> you</c><00:02:20.879><c> don't</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
file equals this Json file but you don't
 

00:02:21.120 --> 00:02:22.869 align:start position:0%
file equals this Json file but you don't
necessarily<00:02:21.519><c> need</c><00:02:21.680><c> to</c><00:02:21.879><c> explicitly</c><00:02:22.400><c> say</c><00:02:22.680><c> that</c>

00:02:22.869 --> 00:02:22.879 align:start position:0%
necessarily need to explicitly say that
 

00:02:22.879 --> 00:02:25.350 align:start position:0%
necessarily need to explicitly say that
and<00:02:22.959><c> then</c><00:02:23.080><c> we</c><00:02:23.160><c> need</c><00:02:23.319><c> the</c><00:02:23.440><c> llm</c><00:02:24.040><c> config</c><00:02:24.800><c> for</c><00:02:25.200><c> the</c>

00:02:25.350 --> 00:02:25.360 align:start position:0%
and then we need the llm config for the
 

00:02:25.360 --> 00:02:27.229 align:start position:0%
and then we need the llm config for the
assistant<00:02:25.800><c> agent</c><00:02:26.200><c> and</c><00:02:26.319><c> we</c><00:02:26.440><c> give</c><00:02:26.519><c> it</c><00:02:26.640><c> a</c><00:02:26.800><c> timeout</c>

00:02:27.229 --> 00:02:27.239 align:start position:0%
assistant agent and we give it a timeout
 

00:02:27.239 --> 00:02:30.030 align:start position:0%
assistant agent and we give it a timeout
of<00:02:27.480><c> 5</c><00:02:27.680><c> minutes</c><00:02:28.440><c> a</c><00:02:28.640><c> seed</c><00:02:29.160><c> and</c><00:02:29.280><c> then</c><00:02:29.519><c> we</c><00:02:29.840><c> give</c><00:02:29.959><c> it</c>

00:02:30.030 --> 00:02:30.040 align:start position:0%
of 5 minutes a seed and then we give it
 

00:02:30.040 --> 00:02:31.390 align:start position:0%
of 5 minutes a seed and then we give it
the<00:02:30.160><c> config</c><00:02:30.480><c> list</c><00:02:30.720><c> property</c><00:02:31.120><c> and</c><00:02:31.239><c> then</c><00:02:31.319><c> we</c>

00:02:31.390 --> 00:02:31.400 align:start position:0%
the config list property and then we
 

00:02:31.400 --> 00:02:32.949 align:start position:0%
the config list property and then we
need<00:02:31.519><c> to</c><00:02:31.640><c> create</c><00:02:31.879><c> the</c><00:02:32.040><c> agents</c><00:02:32.519><c> the</c><00:02:32.599><c> first</c><00:02:32.800><c> one</c>

00:02:32.949 --> 00:02:32.959 align:start position:0%
need to create the agents the first one
 

00:02:32.959 --> 00:02:35.509 align:start position:0%
need to create the agents the first one
is<00:02:33.120><c> the</c><00:02:33.280><c> assistant</c><00:02:33.720><c> agent</c><00:02:34.400><c> with</c><00:02:34.599><c> a</c><00:02:34.800><c> name</c><00:02:35.360><c> the</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
is the assistant agent with a name the
 

00:02:35.519 --> 00:02:36.990 align:start position:0%
is the assistant agent with a name the
assistant<00:02:35.840><c> message</c><00:02:36.120><c> that</c><00:02:36.239><c> you</c><00:02:36.319><c> are</c><00:02:36.480><c> helpful</c>

00:02:36.990 --> 00:02:37.000 align:start position:0%
assistant message that you are helpful
 

00:02:37.000 --> 00:02:39.229 align:start position:0%
assistant message that you are helpful
assistant<00:02:37.560><c> and</c><00:02:37.800><c> the</c><00:02:37.920><c> llm</c><00:02:38.440><c> config</c><00:02:39.000><c> that</c><00:02:39.120><c> we</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
assistant and the llm config that we
 

00:02:39.239 --> 00:02:41.350 align:start position:0%
assistant and the llm config that we
just<00:02:39.440><c> created</c><00:02:40.040><c> this</c><00:02:40.120><c> is</c><00:02:40.319><c> the</c><00:02:40.560><c> new</c><00:02:40.920><c> agent</c><00:02:41.280><c> this</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
just created this is the new agent this
 

00:02:41.360 --> 00:02:43.830 align:start position:0%
just created this is the new agent this
is<00:02:41.480><c> a</c><00:02:41.640><c> custom</c><00:02:42.000><c> agent</c><00:02:42.560><c> so</c><00:02:42.800><c> it's</c><00:02:43.000><c> called</c><00:02:43.360><c> math</c>

00:02:43.830 --> 00:02:43.840 align:start position:0%
is a custom agent so it's called math
 

00:02:43.840 --> 00:02:45.949 align:start position:0%
is a custom agent so it's called math
user<00:02:44.280><c> proxy</c><00:02:44.720><c> agent</c><00:02:45.159><c> all</c><00:02:45.239><c> we</c><00:02:45.360><c> need</c><00:02:45.519><c> here</c><00:02:45.640><c> is</c><00:02:45.800><c> a</c>

00:02:45.949 --> 00:02:45.959 align:start position:0%
user proxy agent all we need here is a
 

00:02:45.959 --> 00:02:47.750 align:start position:0%
user proxy agent all we need here is a
name<00:02:46.360><c> and</c><00:02:46.480><c> for</c><00:02:46.640><c> the</c><00:02:46.800><c> human</c><00:02:47.080><c> input</c><00:02:47.400><c> mode</c><00:02:47.680><c> I'm</c>

00:02:47.750 --> 00:02:47.760 align:start position:0%
name and for the human input mode I'm
 

00:02:47.760 --> 00:02:49.509 align:start position:0%
name and for the human input mode I'm
going<00:02:47.879><c> to</c><00:02:48.000><c> put</c><00:02:48.159><c> never</c><00:02:48.800><c> because</c><00:02:49.159><c> the</c><00:02:49.280><c> math</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
going to put never because the math
 

00:02:49.519 --> 00:02:50.869 align:start position:0%
going to put never because the math
problems<00:02:49.840><c> I'm</c><00:02:50.000><c> about</c><00:02:50.200><c> to</c><00:02:50.360><c> show</c><00:02:50.560><c> you</c><00:02:50.680><c> as</c>

00:02:50.869 --> 00:02:50.879 align:start position:0%
problems I'm about to show you as
 

00:02:50.879 --> 00:02:52.750 align:start position:0%
problems I'm about to show you as
examples<00:02:51.599><c> I</c><00:02:51.680><c> would</c><00:02:51.840><c> have</c><00:02:52.000><c> no</c><00:02:52.159><c> idea</c><00:02:52.440><c> how</c><00:02:52.519><c> to</c>

00:02:52.750 --> 00:02:52.760 align:start position:0%
examples I would have no idea how to
 

00:02:52.760 --> 00:02:56.070 align:start position:0%
examples I would have no idea how to
help<00:02:53.319><c> anyways</c><00:02:54.040><c> so</c><00:02:54.480><c> I</c><00:02:54.760><c> don't</c><00:02:55.080><c> need</c><00:02:55.480><c> to</c><00:02:55.760><c> talk</c>

00:02:56.070 --> 00:02:56.080 align:start position:0%
help anyways so I don't need to talk
 

00:02:56.080 --> 00:02:58.190 align:start position:0%
help anyways so I don't need to talk
with<00:02:56.200><c> the</c><00:02:56.360><c> llm</c><00:02:57.360><c> and</c><00:02:57.440><c> then</c><00:02:57.560><c> for</c><00:02:57.680><c> the</c><00:02:57.840><c> code</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
with the llm and then for the code
 

00:02:58.200 --> 00:03:00.309 align:start position:0%
with the llm and then for the code
execution<00:02:58.760><c> configuration</c><00:02:59.760><c> just</c><00:02:59.879><c> set</c><00:03:00.080><c> use</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
execution configuration just set use
 

00:03:00.319 --> 00:03:01.630 align:start position:0%
execution configuration just set use
Docker<00:03:00.599><c> to</c><00:03:00.720><c> false</c><00:03:01.040><c> unless</c><00:03:01.239><c> you're</c><00:03:01.360><c> using</c>

00:03:01.630 --> 00:03:01.640 align:start position:0%
Docker to false unless you're using
 

00:03:01.640 --> 00:03:03.270 align:start position:0%
Docker to false unless you're using
Docker<00:03:02.200><c> then</c><00:03:02.360><c> go</c><00:03:02.480><c> ahead</c><00:03:02.640><c> and</c><00:03:02.840><c> keep</c><00:03:03.000><c> this</c><00:03:03.120><c> as</c>

00:03:03.270 --> 00:03:03.280 align:start position:0%
Docker then go ahead and keep this as
 

00:03:03.280 --> 00:03:05.470 align:start position:0%
Docker then go ahead and keep this as
true<00:03:03.599><c> and</c><00:03:03.680><c> then</c><00:03:03.879><c> finally</c><00:03:04.280><c> for</c><00:03:04.519><c> the</c><00:03:04.760><c> first</c><00:03:05.159><c> math</c>

00:03:05.470 --> 00:03:05.480 align:start position:0%
true and then finally for the first math
 

00:03:05.480 --> 00:03:06.949 align:start position:0%
true and then finally for the first math
problem<00:03:05.840><c> that</c><00:03:05.959><c> we're</c><00:03:06.120><c> going</c><00:03:06.239><c> to</c><00:03:06.360><c> see</c><00:03:06.640><c> as</c><00:03:06.760><c> an</c>

00:03:06.949 --> 00:03:06.959 align:start position:0%
problem that we're going to see as an
 

00:03:06.959 --> 00:03:09.149 align:start position:0%
problem that we're going to see as an
example<00:03:07.760><c> something</c><00:03:08.040><c> about</c><00:03:08.239><c> an</c><00:03:08.400><c> inequality</c>

00:03:09.149 --> 00:03:09.159 align:start position:0%
example something about an inequality
 

00:03:09.159 --> 00:03:11.309 align:start position:0%
example something about an inequality
and<00:03:09.319><c> then</c><00:03:09.599><c> Express</c><00:03:10.159><c> the</c><00:03:10.319><c> answer</c><00:03:10.599><c> in</c><00:03:10.799><c> interval</c>

00:03:11.309 --> 00:03:11.319 align:start position:0%
and then Express the answer in interval
 

00:03:11.319 --> 00:03:13.270 align:start position:0%
and then Express the answer in interval
notation<00:03:11.879><c> all</c><00:03:12.000><c> we</c><00:03:12.120><c> need</c><00:03:12.239><c> to</c><00:03:12.400><c> say</c><00:03:12.599><c> is</c><00:03:12.840><c> math</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
notation all we need to say is math
 

00:03:13.280 --> 00:03:15.670 align:start position:0%
notation all we need to say is math
proxy<00:03:13.640><c> agent.</c><00:03:14.200><c> initiate</c><00:03:14.720><c> chat</c><00:03:15.360><c> with</c><00:03:15.480><c> the</c>

00:03:15.670 --> 00:03:15.680 align:start position:0%
proxy agent. initiate chat with the
 

00:03:15.680 --> 00:03:18.030 align:start position:0%
proxy agent. initiate chat with the
assistant<00:03:16.480><c> and</c><00:03:16.640><c> then</c><00:03:17.000><c> this</c><00:03:17.159><c> is</c><00:03:17.440><c> a</c><00:03:17.760><c> new</c>

00:03:18.030 --> 00:03:18.040 align:start position:0%
assistant and then this is a new
 

00:03:18.040 --> 00:03:21.229 align:start position:0%
assistant and then this is a new
parameter<00:03:18.720><c> just</c><00:03:19.080><c> for</c><00:03:19.560><c> the</c><00:03:19.840><c> math</c><00:03:20.159><c> proxy</c><00:03:20.519><c> agent</c>

00:03:21.229 --> 00:03:21.239 align:start position:0%
parameter just for the math proxy agent
 

00:03:21.239 --> 00:03:22.949 align:start position:0%
parameter just for the math proxy agent
called<00:03:21.560><c> problem</c><00:03:22.000><c> where</c><00:03:22.159><c> we</c><00:03:22.319><c> give</c><00:03:22.440><c> it</c><00:03:22.560><c> the</c><00:03:22.680><c> math</c>

00:03:22.949 --> 00:03:22.959 align:start position:0%
called problem where we give it the math
 

00:03:22.959 --> 00:03:24.509 align:start position:0%
called problem where we give it the math
problem<00:03:23.360><c> and</c><00:03:23.480><c> now</c><00:03:23.560><c> we</c><00:03:23.720><c> can</c><00:03:23.840><c> simply</c><00:03:24.080><c> run</c><00:03:24.239><c> it</c><00:03:24.400><c> to</c>

00:03:24.509 --> 00:03:24.519 align:start position:0%
problem and now we can simply run it to
 

00:03:24.519 --> 00:03:26.630 align:start position:0%
problem and now we can simply run it to
see<00:03:24.720><c> what</c><00:03:24.799><c> it</c><00:03:24.959><c> responds</c><00:03:25.400><c> with</c><00:03:25.920><c> okay</c><00:03:26.239><c> so</c><00:03:26.480><c> what</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
see what it responds with okay so what
 

00:03:26.640 --> 00:03:29.190 align:start position:0%
see what it responds with okay so what
we<00:03:26.799><c> see</c><00:03:27.200><c> here</c><00:03:27.959><c> is</c><00:03:28.200><c> that</c><00:03:28.480><c> it</c><00:03:28.680><c> thought</c><00:03:28.959><c> that</c><00:03:29.080><c> it</c>

00:03:29.190 --> 00:03:29.200 align:start position:0%
we see here is that it thought that it
 

00:03:29.200 --> 00:03:31.710 align:start position:0%
we see here is that it thought that it
could<00:03:29.319><c> use</c><00:03:29.799><c> python</c><00:03:30.200><c> to</c><00:03:30.360><c> solve</c><00:03:30.599><c> the</c><00:03:30.840><c> problem</c><00:03:31.519><c> so</c>

00:03:31.710 --> 00:03:31.720 align:start position:0%
could use python to solve the problem so
 

00:03:31.720 --> 00:03:33.350 align:start position:0%
could use python to solve the problem so
when<00:03:31.840><c> we</c><00:03:32.000><c> went</c><00:03:32.200><c> back</c><00:03:32.319><c> to</c><00:03:32.480><c> the</c><00:03:32.680><c> three-step</c>

00:03:33.350 --> 00:03:33.360 align:start position:0%
when we went back to the three-step
 

00:03:33.360 --> 00:03:35.390 align:start position:0%
when we went back to the three-step
solution<00:03:33.720><c> for</c><00:03:33.959><c> prompting</c><00:03:34.680><c> it</c><00:03:34.799><c> took</c><00:03:35.000><c> the</c><00:03:35.159><c> first</c>

00:03:35.390 --> 00:03:35.400 align:start position:0%
solution for prompting it took the first
 

00:03:35.400 --> 00:03:37.350 align:start position:0%
solution for prompting it took the first
one<00:03:35.640><c> where</c><00:03:35.720><c> it</c><00:03:35.840><c> tries</c><00:03:36.080><c> to</c><00:03:36.239><c> use</c><00:03:36.519><c> Python</c><00:03:37.159><c> then</c>

00:03:37.350 --> 00:03:37.360 align:start position:0%
one where it tries to use Python then
 

00:03:37.360 --> 00:03:39.149 align:start position:0%
one where it tries to use Python then
after<00:03:37.560><c> all</c><00:03:37.760><c> the</c><00:03:37.959><c> queries</c><00:03:38.439><c> are</c><00:03:38.599><c> run</c><00:03:38.959><c> and</c><00:03:39.040><c> you</c>

00:03:39.149 --> 00:03:39.159 align:start position:0%
after all the queries are run and you
 

00:03:39.159 --> 00:03:40.869 align:start position:0%
after all the queries are run and you
get<00:03:39.280><c> the</c><00:03:39.400><c> answer</c><00:03:39.760><c> put</c><00:03:39.920><c> the</c><00:03:40.040><c> answer</c><00:03:40.280><c> in</c><00:03:40.439><c> slash</c>

00:03:40.869 --> 00:03:40.879 align:start position:0%
get the answer put the answer in slash
 

00:03:40.879 --> 00:03:44.350 align:start position:0%
get the answer put the answer in slash
boox<00:03:41.400><c> so</c><00:03:41.519><c> whenever</c><00:03:41.799><c> you</c><00:03:41.959><c> see</c><00:03:42.519><c> this</c><00:03:43.439><c> that</c><00:03:43.599><c> means</c>

00:03:44.350 --> 00:03:44.360 align:start position:0%
boox so whenever you see this that means
 

00:03:44.360 --> 00:03:46.949 align:start position:0%
boox so whenever you see this that means
that<00:03:44.879><c> basically</c><00:03:45.200><c> we're</c><00:03:45.720><c> terminating</c><00:03:46.720><c> uh</c><00:03:46.840><c> they</c>

00:03:46.949 --> 00:03:46.959 align:start position:0%
that basically we're terminating uh they
 

00:03:46.959 --> 00:03:48.229 align:start position:0%
that basically we're terminating uh they
found<00:03:47.239><c> the</c><00:03:47.400><c> answer</c><00:03:47.760><c> and</c><00:03:47.879><c> they're</c><00:03:48.040><c> going</c><00:03:48.120><c> to</c>

00:03:48.229 --> 00:03:48.239 align:start position:0%
found the answer and they're going to
 

00:03:48.239 --> 00:03:50.589 align:start position:0%
found the answer and they're going to
send<00:03:48.439><c> it</c><00:03:48.680><c> back</c><00:03:48.840><c> to</c><00:03:49.040><c> us</c><00:03:49.640><c> the</c><00:03:49.760><c> assistant</c><00:03:50.120><c> gives</c><00:03:50.360><c> a</c>

00:03:50.589 --> 00:03:50.599 align:start position:0%
send it back to us the assistant gives a
 

00:03:50.599 --> 00:03:53.750 align:start position:0%
send it back to us the assistant gives a
key<00:03:51.000><c> idea</c><00:03:51.760><c> with</c><00:03:52.000><c> some</c><00:03:52.360><c> python</c><00:03:52.760><c> code</c><00:03:53.120><c> to</c><00:03:53.360><c> solve</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
key idea with some python code to solve
 

00:03:53.760 --> 00:03:55.589 align:start position:0%
key idea with some python code to solve
the<00:03:53.959><c> problem</c><00:03:54.480><c> and</c><00:03:54.599><c> then</c><00:03:54.720><c> you</c><00:03:54.840><c> can</c><00:03:55.040><c> see</c><00:03:55.400><c> the</c>

00:03:55.589 --> 00:03:55.599 align:start position:0%
the problem and then you can see the
 

00:03:55.599 --> 00:03:58.030 align:start position:0%
the problem and then you can see the
slash<00:03:56.120><c> booxed</c><00:03:56.519><c> here</c><00:03:56.879><c> which</c><00:03:57.079><c> means</c><00:03:57.519><c> that</c>

00:03:58.030 --> 00:03:58.040 align:start position:0%
slash booxed here which means that
 

00:03:58.040 --> 00:04:00.350 align:start position:0%
slash booxed here which means that
everything<00:03:58.400><c> in</c><00:03:58.640><c> here</c><00:03:59.079><c> is</c><00:03:59.480><c> I</c><00:03:59.680><c> I</c><00:03:59.799><c> believe</c><00:04:00.120><c> the</c>

00:04:00.350 --> 00:04:00.360 align:start position:0%
everything in here is I I believe the
 

00:04:00.360 --> 00:04:02.149 align:start position:0%
everything in here is I I believe the
answer<00:04:00.879><c> and</c><00:04:00.959><c> then</c><00:04:01.079><c> the</c><00:04:01.239><c> assistant</c><00:04:01.720><c> gives</c><00:04:02.000><c> the</c>

00:04:02.149 --> 00:04:02.159 align:start position:0%
answer and then the assistant gives the
 

00:04:02.159 --> 00:04:04.949 align:start position:0%
answer and then the assistant gives the
answer<00:04:02.599><c> something</c><00:04:02.879><c> about</c><00:04:03.200><c> Infinity</c><00:04:04.040><c> -14</c><00:04:04.599><c> and</c>

00:04:04.949 --> 00:04:04.959 align:start position:0%
answer something about Infinity -14 and
 

00:04:04.959 --> 00:04:06.949 align:start position:0%
answer something about Infinity -14 and
-3<00:04:05.680><c> positive</c><00:04:06.040><c> Infinity</c><00:04:06.519><c> this</c><00:04:06.640><c> may</c><00:04:06.760><c> be</c><00:04:06.879><c> the</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
-3 positive Infinity this may be the
 

00:04:06.959 --> 00:04:08.830 align:start position:0%
-3 positive Infinity this may be the
correct<00:04:07.239><c> answer</c><00:04:07.760><c> I</c><00:04:07.799><c> don't</c><00:04:08.000><c> really</c><00:04:08.200><c> know</c><00:04:08.720><c> I'm</c>

00:04:08.830 --> 00:04:08.840 align:start position:0%
correct answer I don't really know I'm
 

00:04:08.840 --> 00:04:10.149 align:start position:0%
correct answer I don't really know I'm
just<00:04:08.959><c> going</c><00:04:09.040><c> to</c><00:04:09.239><c> go</c><00:04:09.360><c> with</c><00:04:09.480><c> it</c><00:04:09.599><c> and</c><00:04:09.720><c> say</c><00:04:09.920><c> that</c><00:04:10.040><c> it</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
just going to go with it and say that it
 

00:04:10.159 --> 00:04:12.630 align:start position:0%
just going to go with it and say that it
is<00:04:10.680><c> let's</c><00:04:10.879><c> try</c><00:04:11.239><c> another</c><00:04:11.599><c> math</c><00:04:11.920><c> problem</c><00:04:12.319><c> so</c><00:04:12.480><c> in</c>

00:04:12.630 --> 00:04:12.640 align:start position:0%
is let's try another math problem so in
 

00:04:12.640 --> 00:04:14.190 align:start position:0%
is let's try another math problem so in
this<00:04:12.799><c> math</c><00:04:13.079><c> problem</c><00:04:13.439><c> I</c><00:04:13.519><c> think</c><00:04:13.680><c> this</c><00:04:13.879><c> looks</c>

00:04:14.190 --> 00:04:14.200 align:start position:0%
this math problem I think this looks
 

00:04:14.200 --> 00:04:16.469 align:start position:0%
this math problem I think this looks
like<00:04:14.519><c> a</c><00:04:14.879><c> system</c><00:04:15.159><c> of</c><00:04:15.360><c> equations</c><00:04:16.120><c> and</c><00:04:16.239><c> then</c><00:04:16.359><c> we</c>

00:04:16.469 --> 00:04:16.479 align:start position:0%
like a system of equations and then we
 

00:04:16.479 --> 00:04:19.509 align:start position:0%
like a system of equations and then we
want<00:04:16.560><c> to</c><00:04:16.680><c> know</c><00:04:16.840><c> what</c><00:04:17.000><c> the</c><00:04:17.239><c> value</c><00:04:17.720><c> of</c><00:04:18.040><c> x</c><00:04:18.320><c> -</c><00:04:18.799><c> Y</c><00:04:19.160><c> is</c>

00:04:19.509 --> 00:04:19.519 align:start position:0%
want to know what the value of x - Y is
 

00:04:19.519 --> 00:04:21.229 align:start position:0%
want to know what the value of x - Y is
so<00:04:19.680><c> let's</c><00:04:19.880><c> run</c><00:04:20.280><c> this</c><00:04:20.479><c> and</c><00:04:20.600><c> see</c><00:04:20.799><c> what</c><00:04:20.880><c> it</c><00:04:21.040><c> gives</c>

00:04:21.229 --> 00:04:21.239 align:start position:0%
so let's run this and see what it gives
 

00:04:21.239 --> 00:04:23.270 align:start position:0%
so let's run this and see what it gives
us<00:04:21.639><c> as</c><00:04:21.720><c> you</c><00:04:21.799><c> can</c><00:04:21.919><c> see</c><00:04:22.160><c> here</c><00:04:22.440><c> this</c><00:04:22.560><c> time</c><00:04:22.759><c> it</c><00:04:22.960><c> also</c>

00:04:23.270 --> 00:04:23.280 align:start position:0%
us as you can see here this time it also
 

00:04:23.280 --> 00:04:25.189 align:start position:0%
us as you can see here this time it also
thought<00:04:23.479><c> it</c><00:04:23.639><c> could</c><00:04:23.759><c> use</c><00:04:24.040><c> Python</c><00:04:24.440><c> to</c><00:04:24.680><c> solve</c><00:04:25.000><c> the</c>

00:04:25.189 --> 00:04:25.199 align:start position:0%
thought it could use Python to solve the
 

00:04:25.199 --> 00:04:26.830 align:start position:0%
thought it could use Python to solve the
problem<00:04:25.600><c> and</c><00:04:25.720><c> then</c><00:04:25.840><c> for</c><00:04:26.000><c> the</c><00:04:26.160><c> cases</c><00:04:26.520><c> here</c><00:04:26.720><c> you</c>

00:04:26.830 --> 00:04:26.840 align:start position:0%
problem and then for the cases here you
 

00:04:26.840 --> 00:04:28.430 align:start position:0%
problem and then for the cases here you
can<00:04:27.000><c> see</c><00:04:27.240><c> that</c><00:04:27.639><c> these</c><00:04:27.759><c> are</c><00:04:27.919><c> the</c><00:04:28.080><c> three</c><00:04:28.240><c> that</c><00:04:28.360><c> I</c>

00:04:28.430 --> 00:04:28.440 align:start position:0%
can see that these are the three that I
 

00:04:28.440 --> 00:04:30.189 align:start position:0%
can see that these are the three that I
was<00:04:28.600><c> talking</c><00:04:28.840><c> about</c><00:04:29.240><c> at</c><00:04:29.400><c> the</c><00:04:29.759><c> problem</c><00:04:29.960><c> can</c><00:04:30.080><c> be</c>

00:04:30.189 --> 00:04:30.199 align:start position:0%
was talking about at the problem can be
 

00:04:30.199 --> 00:04:31.990 align:start position:0%
was talking about at the problem can be
solved<00:04:30.479><c> with</c><00:04:30.680><c> python</c><00:04:30.960><c> code</c><00:04:31.240><c> directly</c><00:04:31.880><c> just</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
solved with python code directly just
 

00:04:32.000 --> 00:04:33.670 align:start position:0%
solved with python code directly just
write<00:04:32.199><c> the</c><00:04:32.360><c> program</c><00:04:32.960><c> if</c><00:04:33.080><c> it's</c><00:04:33.240><c> mostly</c>

00:04:33.670 --> 00:04:33.680 align:start position:0%
write the program if it's mostly
 

00:04:33.680 --> 00:04:36.150 align:start position:0%
write the program if it's mostly
reasoning<00:04:34.440><c> solve</c><00:04:34.680><c> it</c><00:04:34.880><c> yourself</c><00:04:35.600><c> if</c><00:04:35.759><c> the</c><00:04:35.919><c> first</c>

00:04:36.150 --> 00:04:36.160 align:start position:0%
reasoning solve it yourself if the first
 

00:04:36.160 --> 00:04:39.150 align:start position:0%
reasoning solve it yourself if the first
two<00:04:36.520><c> cannot</c><00:04:36.880><c> be</c><00:04:37.080><c> done</c><00:04:37.400><c> that</c><00:04:37.600><c> way</c><00:04:38.160><c> then</c><00:04:38.440><c> follow</c>

00:04:39.150 --> 00:04:39.160 align:start position:0%
two cannot be done that way then follow
 

00:04:39.160 --> 00:04:41.070 align:start position:0%
two cannot be done that way then follow
this<00:04:39.680><c> process</c><00:04:40.160><c> which</c><00:04:40.280><c> is</c><00:04:40.400><c> a</c><00:04:40.479><c> four-step</c>

00:04:41.070 --> 00:04:41.080 align:start position:0%
this process which is a four-step
 

00:04:41.080 --> 00:04:43.430 align:start position:0%
this process which is a four-step
process<00:04:41.520><c> so</c><00:04:41.680><c> now</c><00:04:41.800><c> we</c><00:04:41.919><c> have</c><00:04:42.080><c> the</c><00:04:42.320><c> problem</c><00:04:43.160><c> it</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
process so now we have the problem it
 

00:04:43.440 --> 00:04:46.790 align:start position:0%
process so now we have the problem it
writes<00:04:44.000><c> python</c><00:04:44.560><c> code</c><00:04:45.039><c> for</c><00:04:45.320><c> us</c><00:04:45.919><c> so</c><00:04:46.280><c> the</c><00:04:46.479><c> math</c>

00:04:46.790 --> 00:04:46.800 align:start position:0%
writes python code for us so the math
 

00:04:46.800 --> 00:04:49.310 align:start position:0%
writes python code for us so the math
user<00:04:47.120><c> proxy</c><00:04:47.520><c> agent</c><00:04:48.080><c> basically</c><00:04:48.479><c> just</c><00:04:48.800><c> executes</c>

00:04:49.310 --> 00:04:49.320 align:start position:0%
user proxy agent basically just executes
 

00:04:49.320 --> 00:04:54.390 align:start position:0%
user proxy agent basically just executes
that<00:04:49.479><c> code</c><00:04:50.080><c> gets</c><00:04:51.000><c> -48</c><00:04:52.000><c> and</c><00:04:52.199><c> I</c><00:04:52.360><c> guess</c><00:04:52.639><c> x</c><00:04:52.919><c> -</c><00:04:53.400><c> Y</c><00:04:54.199><c> is</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
that code gets -48 and I guess x - Y is
 

00:04:54.400 --> 00:04:57.070 align:start position:0%
that code gets -48 and I guess x - Y is
now<00:04:54.680><c> equal</c><00:04:54.960><c> to</c><00:04:55.639><c> -48</c><00:04:56.479><c> I'm</c><00:04:56.600><c> also</c><00:04:56.840><c> just</c><00:04:56.919><c> going</c><00:04:57.000><c> to</c>

00:04:57.070 --> 00:04:57.080 align:start position:0%
now equal to -48 I'm also just going to
 

00:04:57.080 --> 00:04:58.430 align:start position:0%
now equal to -48 I'm also just going to
assume<00:04:57.320><c> that's</c><00:04:57.479><c> correct</c><00:04:58.000><c> I</c><00:04:58.080><c> don't</c><00:04:58.280><c> really</c>

00:04:58.430 --> 00:04:58.440 align:start position:0%
assume that's correct I don't really
 

00:04:58.440 --> 00:04:59.830 align:start position:0%
assume that's correct I don't really
know<00:04:58.840><c> okay</c><00:04:59.000><c> please</c><00:04:59.160><c> let</c><00:04:59.240><c> me</c><00:04:59.320><c> know</c><00:04:59.440><c> if</c><00:04:59.720><c> those</c>

00:04:59.830 --> 00:04:59.840 align:start position:0%
know okay please let me know if those
 

00:04:59.840 --> 00:05:01.270 align:start position:0%
know okay please let me know if those
math<00:05:00.039><c> problems</c><00:05:00.280><c> are</c><00:05:00.440><c> correct</c><00:05:00.680><c> or</c><00:05:00.759><c> not</c><00:05:00.960><c> cuz</c><00:05:01.160><c> I</c>

00:05:01.270 --> 00:05:01.280 align:start position:0%
math problems are correct or not cuz I
 

00:05:01.280 --> 00:05:02.749 align:start position:0%
math problems are correct or not cuz I
really<00:05:01.440><c> don't</c><00:05:01.600><c> know</c><00:05:01.759><c> for</c><00:05:01.919><c> sure</c><00:05:02.360><c> I'm</c><00:05:02.520><c> planning</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
really don't know for sure I'm planning
 

00:05:02.759 --> 00:05:04.350 align:start position:0%
really don't know for sure I'm planning
on<00:05:02.919><c> making</c><00:05:03.199><c> video</c><00:05:03.560><c> every</c><00:05:03.759><c> day</c><00:05:03.919><c> this</c><00:05:04.039><c> month</c><00:05:04.280><c> I'm</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
on making video every day this month I'm
 

00:05:04.360 --> 00:05:05.950 align:start position:0%
on making video every day this month I'm
going<00:05:04.479><c> to</c><00:05:04.560><c> do</c><00:05:04.680><c> my</c><00:05:04.880><c> very</c><00:05:05.080><c> best</c><00:05:05.240><c> to</c><00:05:05.400><c> stick</c><00:05:05.639><c> to</c>

00:05:05.950 --> 00:05:05.960 align:start position:0%
going to do my very best to stick to
 

00:05:05.960 --> 00:05:08.070 align:start position:0%
going to do my very best to stick to
that<00:05:06.240><c> this</c><00:05:06.320><c> is</c><00:05:06.600><c> day</c><00:05:06.880><c> three</c><00:05:07.600><c> if</c><00:05:07.680><c> you</c><00:05:07.800><c> have</c><00:05:07.919><c> any</c>

00:05:08.070 --> 00:05:08.080 align:start position:0%
that this is day three if you have any
 

00:05:08.080 --> 00:05:09.629 align:start position:0%
that this is day three if you have any
questions<00:05:08.360><c> or</c><00:05:08.520><c> comments</c><00:05:08.960><c> please</c><00:05:09.240><c> leave</c><00:05:09.440><c> them</c>

00:05:09.629 --> 00:05:09.639 align:start position:0%
questions or comments please leave them
 

00:05:09.639 --> 00:05:11.430 align:start position:0%
questions or comments please leave them
down<00:05:09.919><c> in</c><00:05:10.039><c> the</c><00:05:10.160><c> section</c><00:05:10.479><c> below</c><00:05:11.080><c> here</c><00:05:11.199><c> are</c><00:05:11.320><c> some</c>

00:05:11.430 --> 00:05:11.440 align:start position:0%
down in the section below here are some
 

00:05:11.440 --> 00:05:12.950 align:start position:0%
down in the section below here are some
more<00:05:11.600><c> autogen</c><00:05:12.120><c> videos</c><00:05:12.320><c> for</c><00:05:12.479><c> to</c><00:05:12.600><c> look</c><00:05:12.720><c> at</c><00:05:12.880><c> in</c>

00:05:12.950 --> 00:05:12.960 align:start position:0%
more autogen videos for to look at in
 

00:05:12.960 --> 00:05:14.749 align:start position:0%
more autogen videos for to look at in
the<00:05:13.080><c> meantime</c><00:05:13.840><c> have</c><00:05:13.960><c> a</c><00:05:14.080><c> wonderful</c><00:05:14.479><c> day</c><00:05:14.680><c> and</c>

00:05:14.749 --> 00:05:14.759 align:start position:0%
the meantime have a wonderful day and
 

00:05:14.759 --> 00:05:17.840 align:start position:0%
the meantime have a wonderful day and
I'll<00:05:14.960><c> see</c><00:05:15.120><c> you</c><00:05:15.320><c> next</c><00:05:15.600><c> time</c>

