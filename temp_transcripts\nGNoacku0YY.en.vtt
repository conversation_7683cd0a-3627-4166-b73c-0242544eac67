WEBVTT
Kind: captions
Language: en

00:00:00.299 --> 00:00:02.510 align:start position:0%
 
hey<00:00:00.719><c> everyone</c><00:00:00.900><c> <PERSON></c><00:00:01.319><c> here</c><00:00:01.680><c> from</c><00:00:01.860><c> llama</c><00:00:02.159><c> index</c>

00:00:02.510 --> 00:00:02.520 align:start position:0%
hey everyone Logan here from llama index
 

00:00:02.520 --> 00:00:04.610 align:start position:0%
hey everyone Logan here from llama index
back<00:00:02.879><c> with</c><00:00:03.060><c> another</c><00:00:03.240><c> video</c><00:00:03.540><c> on</c><00:00:03.840><c> our</c><00:00:04.020><c> series</c><00:00:04.200><c> of</c>

00:00:04.610 --> 00:00:04.620 align:start position:0%
back with another video on our series of
 

00:00:04.620 --> 00:00:06.730 align:start position:0%
back with another video on our series of
Bottoms<00:00:05.040><c> Up</c><00:00:05.160><c> development</c><00:00:05.520><c> with</c><00:00:05.880><c> llama</c><00:00:06.240><c> index</c>

00:00:06.730 --> 00:00:06.740 align:start position:0%
Bottoms Up development with llama index
 

00:00:06.740 --> 00:00:10.009 align:start position:0%
Bottoms Up development with llama index
uh<00:00:07.740><c> we're</c><00:00:07.980><c> on</c><00:00:08.160><c> a</c><00:00:08.280><c> quest</c><00:00:08.460><c> to</c><00:00:08.940><c> build</c><00:00:09.120><c> a</c><00:00:09.480><c> chat</c><00:00:09.780><c> bot</c>

00:00:10.009 --> 00:00:10.019 align:start position:0%
uh we're on a quest to build a chat bot
 

00:00:10.019 --> 00:00:11.830 align:start position:0%
uh we're on a quest to build a chat bot
using<00:00:10.380><c> the</c><00:00:10.500><c> Walmart</c><00:00:10.740><c> index</c><00:00:11.160><c> documentation</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
using the Walmart index documentation
 

00:00:11.840 --> 00:00:15.049 align:start position:0%
using the Walmart index documentation
but<00:00:12.840><c> in</c><00:00:13.080><c> a</c><00:00:13.259><c> sort</c><00:00:13.380><c> of</c><00:00:13.440><c> like</c><00:00:13.559><c> Bottoms</c><00:00:14.040><c> Up</c><00:00:14.160><c> uh</c>

00:00:15.049 --> 00:00:15.059 align:start position:0%
but in a sort of like Bottoms Up uh
 

00:00:15.059 --> 00:00:17.330 align:start position:0%
but in a sort of like Bottoms Up uh
low-level<00:00:15.599><c> way</c><00:00:15.780><c> and</c><00:00:16.500><c> so</c><00:00:16.619><c> last</c><00:00:16.859><c> episode</c><00:00:17.039><c> we</c>

00:00:17.330 --> 00:00:17.340 align:start position:0%
low-level way and so last episode we
 

00:00:17.340 --> 00:00:19.849 align:start position:0%
low-level way and so last episode we
covered<00:00:17.580><c> how</c><00:00:17.760><c> to</c><00:00:17.880><c> use</c><00:00:18.000><c> loms</c><00:00:18.660><c> in</c><00:00:18.779><c> lawmend</c><00:00:19.140><c> X</c><00:00:19.320><c> and</c>

00:00:19.849 --> 00:00:19.859 align:start position:0%
covered how to use loms in lawmend X and
 

00:00:19.859 --> 00:00:21.769 align:start position:0%
covered how to use loms in lawmend X and
in<00:00:19.980><c> this</c><00:00:20.160><c> video</c><00:00:20.340><c> we're</c><00:00:20.699><c> going</c><00:00:20.880><c> to</c><00:00:21.000><c> cover</c><00:00:21.180><c> how</c>

00:00:21.769 --> 00:00:21.779 align:start position:0%
in this video we're going to cover how
 

00:00:21.779 --> 00:00:24.250 align:start position:0%
in this video we're going to cover how
to<00:00:21.900><c> load</c><00:00:22.439><c> data</c><00:00:22.680><c> and</c><00:00:22.920><c> create</c><00:00:23.100><c> document</c><00:00:23.580><c> objects</c>

00:00:24.250 --> 00:00:24.260 align:start position:0%
to load data and create document objects
 

00:00:24.260 --> 00:00:28.550 align:start position:0%
to load data and create document objects
completely<00:00:25.260><c> customized</c><00:00:25.859><c> in</c><00:00:26.160><c> lava</c><00:00:26.580><c> index</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
completely customized in lava index
 

00:00:28.560 --> 00:00:31.370 align:start position:0%
completely customized in lava index
so<00:00:29.580><c> in</c><00:00:29.939><c> loam</c><00:00:30.300><c> index</c><00:00:30.539><c> there</c><00:00:30.779><c> are</c><00:00:30.900><c> documents</c><00:00:31.320><c> and</c>

00:00:31.370 --> 00:00:31.380 align:start position:0%
so in loam index there are documents and
 

00:00:31.380 --> 00:00:33.590 align:start position:0%
so in loam index there are documents and
nodes<00:00:31.619><c> they're</c><00:00:32.520><c> essentially</c><00:00:32.880><c> the</c><00:00:33.059><c> same</c><00:00:33.239><c> but</c>

00:00:33.590 --> 00:00:33.600 align:start position:0%
nodes they're essentially the same but
 

00:00:33.600 --> 00:00:35.150 align:start position:0%
nodes they're essentially the same but
the<00:00:33.780><c> main</c><00:00:34.020><c> difference</c><00:00:34.260><c> is</c><00:00:34.620><c> is</c><00:00:34.860><c> that</c><00:00:35.040><c> a</c>

00:00:35.150 --> 00:00:35.160 align:start position:0%
the main difference is is that a
 

00:00:35.160 --> 00:00:37.370 align:start position:0%
the main difference is is that a
document<00:00:35.460><c> is</c><00:00:35.700><c> intended</c><00:00:36.120><c> to</c><00:00:36.239><c> represent</c><00:00:36.719><c> the</c>

00:00:37.370 --> 00:00:37.380 align:start position:0%
document is intended to represent the
 

00:00:37.380 --> 00:00:39.049 align:start position:0%
document is intended to represent the
entire<00:00:37.620><c> document</c><00:00:38.040><c> whether</c><00:00:38.399><c> that's</c><00:00:38.579><c> an</c><00:00:38.820><c> entire</c>

00:00:39.049 --> 00:00:39.059 align:start position:0%
entire document whether that's an entire
 

00:00:39.059 --> 00:00:42.110 align:start position:0%
entire document whether that's an entire
page<00:00:39.300><c> in</c><00:00:39.540><c> a</c><00:00:39.660><c> PDF</c><00:00:40.079><c> the</c><00:00:40.680><c> entire</c><00:00:40.920><c> PDF</c><00:00:41.340><c> entire</c><00:00:41.820><c> Word</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
page in a PDF the entire PDF entire Word
 

00:00:42.120 --> 00:00:43.970 align:start position:0%
page in a PDF the entire PDF entire Word
file<00:00:42.780><c> Etc</c>

00:00:43.970 --> 00:00:43.980 align:start position:0%
file Etc
 

00:00:43.980 --> 00:00:45.709 align:start position:0%
file Etc
um<00:00:44.040><c> and</c><00:00:44.579><c> when</c><00:00:44.700><c> you</c><00:00:44.820><c> insert</c><00:00:45.120><c> these</c><00:00:45.360><c> documents</c>

00:00:45.709 --> 00:00:45.719 align:start position:0%
um and when you insert these documents
 

00:00:45.719 --> 00:00:47.930 align:start position:0%
um and when you insert these documents
into<00:00:45.960><c> an</c><00:00:46.200><c> index</c><00:00:46.559><c> they</c><00:00:47.160><c> are</c><00:00:47.280><c> broken</c><00:00:47.460><c> down</c><00:00:47.700><c> into</c>

00:00:47.930 --> 00:00:47.940 align:start position:0%
into an index they are broken down into
 

00:00:47.940 --> 00:00:51.170 align:start position:0%
into an index they are broken down into
nodes<00:00:48.360><c> which</c><00:00:48.840><c> are</c><00:00:49.140><c> basically</c><00:00:50.039><c> smaller</c><00:00:50.640><c> chunks</c>

00:00:51.170 --> 00:00:51.180 align:start position:0%
nodes which are basically smaller chunks
 

00:00:51.180 --> 00:00:53.389 align:start position:0%
nodes which are basically smaller chunks
of<00:00:51.360><c> that</c><00:00:51.539><c> original</c><00:00:51.660><c> document</c><00:00:52.320><c> used</c><00:00:53.160><c> for</c>

00:00:53.389 --> 00:00:53.399 align:start position:0%
of that original document used for
 

00:00:53.399 --> 00:00:55.130 align:start position:0%
of that original document used for
retrieval<00:00:53.940><c> and</c><00:00:54.239><c> question</c><00:00:54.480><c> answering</c><00:00:55.020><c> and</c>

00:00:55.130 --> 00:00:55.140 align:start position:0%
retrieval and question answering and
 

00:00:55.140 --> 00:00:56.090 align:start position:0%
retrieval and question answering and
whatnot

00:00:56.090 --> 00:00:56.100 align:start position:0%
whatnot
 

00:00:56.100 --> 00:00:57.709 align:start position:0%
whatnot
and<00:00:56.460><c> so</c><00:00:56.640><c> these</c><00:00:56.820><c> documents</c><00:00:57.180><c> and</c><00:00:57.239><c> nodes</c><00:00:57.480><c> can</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
and so these documents and nodes can
 

00:00:57.719 --> 00:01:00.049 align:start position:0%
and so these documents and nodes can
have<00:00:57.899><c> a</c><00:00:58.140><c> few</c><00:00:58.260><c> different</c><00:00:58.440><c> attributes</c><00:00:59.100><c> such</c><00:00:59.879><c> as</c>

00:01:00.049 --> 00:01:00.059 align:start position:0%
have a few different attributes such as
 

00:01:00.059 --> 00:01:02.389 align:start position:0%
have a few different attributes such as
metadata<00:01:00.899><c> which</c><00:01:01.140><c> can</c><00:01:01.320><c> be</c><00:01:01.559><c> something</c><00:01:01.920><c> like</c><00:01:02.160><c> a</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
metadata which can be something like a
 

00:01:02.399 --> 00:01:04.670 align:start position:0%
metadata which can be something like a
category<00:01:02.760><c> or</c><00:01:03.000><c> a</c><00:01:03.180><c> file</c><00:01:03.359><c> name</c><00:01:03.539><c> as</c><00:01:04.379><c> well</c><00:01:04.559><c> as</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
category or a file name as well as
 

00:01:04.680 --> 00:01:07.370 align:start position:0%
category or a file name as well as
relationships<00:01:05.519><c> which</c><00:01:06.180><c> can</c><00:01:06.720><c> be</c><00:01:06.900><c> links</c><00:01:07.260><c> to</c>

00:01:07.370 --> 00:01:07.380 align:start position:0%
relationships which can be links to
 

00:01:07.380 --> 00:01:09.590 align:start position:0%
relationships which can be links to
other<00:01:07.560><c> nodes</c><00:01:07.860><c> or</c><00:01:08.040><c> documents</c><00:01:08.580><c> so</c><00:01:09.060><c> for</c><00:01:09.299><c> example</c>

00:01:09.590 --> 00:01:09.600 align:start position:0%
other nodes or documents so for example
 

00:01:09.600 --> 00:01:12.770 align:start position:0%
other nodes or documents so for example
when<00:01:10.200><c> you</c><00:01:10.320><c> put</c><00:01:10.619><c> a</c><00:01:10.740><c> document</c><00:01:11.119><c> inserted</c><00:01:12.119><c> into</c><00:01:12.360><c> an</c>

00:01:12.770 --> 00:01:12.780 align:start position:0%
when you put a document inserted into an
 

00:01:12.780 --> 00:01:13.730 align:start position:0%
when you put a document inserted into an
index

00:01:13.730 --> 00:01:13.740 align:start position:0%
index
 

00:01:13.740 --> 00:01:16.850 align:start position:0%
index
uh<00:01:14.280><c> the</c><00:01:14.520><c> nodes</c><00:01:14.700><c> contain</c><00:01:15.180><c> a</c><00:01:15.299><c> reference</c><00:01:15.659><c> to</c><00:01:16.140><c> the</c>

00:01:16.850 --> 00:01:16.860 align:start position:0%
uh the nodes contain a reference to the
 

00:01:16.860 --> 00:01:19.310 align:start position:0%
uh the nodes contain a reference to the
parent<00:01:17.040><c> document</c><00:01:17.640><c> ID</c><00:01:18.000><c> and</c><00:01:18.900><c> so</c><00:01:19.020><c> that's</c><00:01:19.140><c> an</c>

00:01:19.310 --> 00:01:19.320 align:start position:0%
parent document ID and so that's an
 

00:01:19.320 --> 00:01:20.870 align:start position:0%
parent document ID and so that's an
example<00:01:19.560><c> of</c><00:01:19.740><c> a</c><00:01:19.920><c> relationship</c>

00:01:20.870 --> 00:01:20.880 align:start position:0%
example of a relationship
 

00:01:20.880 --> 00:01:22.969 align:start position:0%
example of a relationship
and<00:01:21.240><c> we</c><00:01:21.360><c> would</c><00:01:21.479><c> see</c><00:01:21.659><c> the</c><00:01:21.900><c> actual</c><00:01:22.320><c> usage</c><00:01:22.740><c> for</c>

00:01:22.969 --> 00:01:22.979 align:start position:0%
and we would see the actual usage for
 

00:01:22.979 --> 00:01:25.550 align:start position:0%
and we would see the actual usage for
creating<00:01:23.280><c> a</c><00:01:23.460><c> document</c><00:01:23.759><c> is</c><00:01:24.119><c> super</c><00:01:24.299><c> simple</c><00:01:24.600><c> we</c>

00:01:25.550 --> 00:01:25.560 align:start position:0%
creating a document is super simple we
 

00:01:25.560 --> 00:01:27.590 align:start position:0%
creating a document is super simple we
can<00:01:25.680><c> import</c><00:01:25.860><c> it</c><00:01:26.220><c> create</c><00:01:26.880><c> it</c><00:01:27.119><c> give</c><00:01:27.299><c> it</c><00:01:27.420><c> some</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
can import it create it give it some
 

00:01:27.600 --> 00:01:29.870 align:start position:0%
can import it create it give it some
text<00:01:27.840><c> super</c><00:01:28.680><c> easy</c>

00:01:29.870 --> 00:01:29.880 align:start position:0%
text super easy
 

00:01:29.880 --> 00:01:32.510 align:start position:0%
text super easy
we<00:01:30.360><c> can</c><00:01:30.479><c> also</c><00:01:30.840><c> import</c><00:01:31.200><c> a</c><00:01:31.740><c> simple</c><00:01:31.920><c> directory</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
we can also import a simple directory
 

00:01:32.520 --> 00:01:35.450 align:start position:0%
we can also import a simple directory
reader<00:01:32.939><c> and</c><00:01:33.659><c> read</c><00:01:33.900><c> a</c><00:01:34.140><c> directory</c><00:01:34.560><c> of</c><00:01:34.680><c> data</c><00:01:35.040><c> and</c>

00:01:35.450 --> 00:01:35.460 align:start position:0%
reader and read a directory of data and
 

00:01:35.460 --> 00:01:37.370 align:start position:0%
reader and read a directory of data and
get<00:01:35.640><c> a</c><00:01:35.820><c> list</c><00:01:36.000><c> of</c><00:01:36.180><c> documents</c>

00:01:37.370 --> 00:01:37.380 align:start position:0%
get a list of documents
 

00:01:37.380 --> 00:01:39.789 align:start position:0%
get a list of documents
so<00:01:37.740><c> if</c><00:01:37.860><c> we</c><00:01:37.979><c> want</c><00:01:38.159><c> to</c><00:01:38.280><c> you</c><00:01:38.880><c> know</c><00:01:38.939><c> load</c><00:01:39.299><c> all</c><00:01:39.540><c> the</c>

00:01:39.789 --> 00:01:39.799 align:start position:0%
so if we want to you know load all the
 

00:01:39.799 --> 00:01:43.069 align:start position:0%
so if we want to you know load all the
data<00:01:40.799><c> from</c><00:01:40.979><c> our</c><00:01:41.100><c> documentation</c><00:01:41.820><c> to</c><00:01:42.479><c> create</c><00:01:42.780><c> a</c>

00:01:43.069 --> 00:01:43.079 align:start position:0%
data from our documentation to create a
 

00:01:43.079 --> 00:01:45.350 align:start position:0%
data from our documentation to create a
chat<00:01:43.500><c> bot</c><00:01:43.740><c> we</c><00:01:44.520><c> would</c><00:01:44.700><c> just</c><00:01:44.820><c> use</c><00:01:45.000><c> the</c><00:01:45.180><c> simple</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
chat bot we would just use the simple
 

00:01:45.360 --> 00:01:46.910 align:start position:0%
chat bot we would just use the simple
directory<00:01:45.780><c> reader</c><00:01:46.200><c> and</c><00:01:46.320><c> could</c><00:01:46.500><c> load</c>

00:01:46.910 --> 00:01:46.920 align:start position:0%
directory reader and could load
 

00:01:46.920 --> 00:01:49.609 align:start position:0%
directory reader and could load
everything<00:01:47.280><c> in</c><00:01:47.520><c> our</c><00:01:47.640><c> Docs</c>

00:01:49.609 --> 00:01:49.619 align:start position:0%
everything in our Docs
 

00:01:49.619 --> 00:01:52.030 align:start position:0%
everything in our Docs
we<00:01:50.159><c> can</c><00:01:50.280><c> also</c><00:01:50.579><c> customize</c><00:01:51.180><c> the</c><00:01:51.479><c> documents</c>

00:01:52.030 --> 00:01:52.040 align:start position:0%
we can also customize the documents
 

00:01:52.040 --> 00:01:54.410 align:start position:0%
we can also customize the documents
quite<00:01:53.040><c> heavily</c><00:01:53.399><c> actually</c><00:01:53.579><c> some</c><00:01:54.000><c> Advanced</c>

00:01:54.410 --> 00:01:54.420 align:start position:0%
quite heavily actually some Advanced
 

00:01:54.420 --> 00:01:56.749 align:start position:0%
quite heavily actually some Advanced
usage<00:01:54.720><c> here</c><00:01:54.960><c> like</c><00:01:55.920><c> I</c><00:01:56.040><c> mentioned</c><00:01:56.280><c> before</c><00:01:56.460><c> we</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
usage here like I mentioned before we
 

00:01:56.759 --> 00:01:58.789 align:start position:0%
usage here like I mentioned before we
can<00:01:56.939><c> add</c><00:01:57.180><c> metadata</c><00:01:58.020><c> so</c><00:01:58.200><c> giving</c><00:01:58.380><c> it</c><00:01:58.560><c> in</c><00:01:58.740><c> this</c>

00:01:58.789 --> 00:01:58.799 align:start position:0%
can add metadata so giving it in this
 

00:01:58.799 --> 00:02:00.050 align:start position:0%
can add metadata so giving it in this
case<00:01:58.920><c> a</c><00:01:59.100><c> category</c>

00:02:00.050 --> 00:02:00.060 align:start position:0%
case a category
 

00:02:00.060 --> 00:02:03.950 align:start position:0%
case a category
we<00:02:00.540><c> can</c><00:02:00.720><c> also</c><00:02:01.560><c> tell</c><00:02:02.340><c> llama</c><00:02:02.759><c> index</c><00:02:03.180><c> to</c><00:02:03.420><c> only</c><00:02:03.659><c> use</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
we can also tell llama index to only use
 

00:02:03.960 --> 00:02:06.410 align:start position:0%
we can also tell llama index to only use
certain<00:02:04.259><c> metadata</c><00:02:04.979><c> for</c><00:02:05.759><c> certain</c><00:02:06.060><c> parts</c><00:02:06.240><c> of</c>

00:02:06.410 --> 00:02:06.420 align:start position:0%
certain metadata for certain parts of
 

00:02:06.420 --> 00:02:09.410 align:start position:0%
certain metadata for certain parts of
LOM<00:02:06.780><c> index</c><00:02:07.140><c> so</c><00:02:07.920><c> we</c><00:02:08.099><c> can</c><00:02:08.220><c> say</c><00:02:08.459><c> you</c><00:02:08.940><c> know</c><00:02:09.000><c> we</c><00:02:09.239><c> only</c>

00:02:09.410 --> 00:02:09.420 align:start position:0%
LOM index so we can say you know we only
 

00:02:09.420 --> 00:02:11.029 align:start position:0%
LOM index so we can say you know we only
want<00:02:09.660><c> the</c><00:02:09.899><c> embeddings</c><00:02:10.500><c> to</c><00:02:10.619><c> look</c><00:02:10.739><c> at</c><00:02:10.920><c> this</c>

00:02:11.029 --> 00:02:11.039 align:start position:0%
want the embeddings to look at this
 

00:02:11.039 --> 00:02:13.309 align:start position:0%
want the embeddings to look at this
metadata<00:02:11.640><c> or</c><00:02:12.000><c> we</c><00:02:12.180><c> only</c><00:02:12.300><c> want</c><00:02:12.480><c> the</c><00:02:12.660><c> llm</c><00:02:13.080><c> to</c><00:02:13.200><c> look</c>

00:02:13.309 --> 00:02:13.319 align:start position:0%
metadata or we only want the llm to look
 

00:02:13.319 --> 00:02:15.770 align:start position:0%
metadata or we only want the llm to look
at<00:02:13.440><c> these</c><00:02:13.800><c> metadata</c><00:02:14.400><c> and</c><00:02:15.239><c> we</c><00:02:15.360><c> can</c><00:02:15.480><c> see</c><00:02:15.599><c> here</c>

00:02:15.770 --> 00:02:15.780 align:start position:0%
at these metadata and we can see here
 

00:02:15.780 --> 00:02:17.210 align:start position:0%
at these metadata and we can see here
that<00:02:15.959><c> we're</c><00:02:16.080><c> saying</c><00:02:16.379><c> we</c><00:02:16.500><c> don't</c><00:02:16.620><c> want</c><00:02:16.739><c> the</c><00:02:16.920><c> LOM</c>

00:02:17.210 --> 00:02:17.220 align:start position:0%
that we're saying we don't want the LOM
 

00:02:17.220 --> 00:02:20.630 align:start position:0%
that we're saying we don't want the LOM
to<00:02:17.700><c> read</c><00:02:17.879><c> the</c><00:02:18.360><c> category</c><00:02:19.200><c> metadata</c><00:02:19.860><c> so</c><00:02:20.459><c> when</c>

00:02:20.630 --> 00:02:20.640 align:start position:0%
to read the category metadata so when
 

00:02:20.640 --> 00:02:23.089 align:start position:0%
to read the category metadata so when
this<00:02:20.940><c> document</c><00:02:21.239><c> or</c><00:02:21.480><c> its</c><00:02:21.959><c> nodes</c><00:02:22.140><c> gets</c><00:02:22.620><c> sent</c><00:02:22.800><c> to</c>

00:02:23.089 --> 00:02:23.099 align:start position:0%
this document or its nodes gets sent to
 

00:02:23.099 --> 00:02:25.490 align:start position:0%
this document or its nodes gets sent to
the<00:02:23.640><c> llm</c><00:02:24.060><c> the</c><00:02:24.300><c> llm</c><00:02:24.720><c> doesn't</c><00:02:24.900><c> know</c><00:02:25.260><c> the</c>

00:02:25.490 --> 00:02:25.500 align:start position:0%
the llm the llm doesn't know the
 

00:02:25.500 --> 00:02:26.630 align:start position:0%
the llm the llm doesn't know the
category

00:02:26.630 --> 00:02:26.640 align:start position:0%
category
 

00:02:26.640 --> 00:02:28.970 align:start position:0%
category
and<00:02:27.060><c> on</c><00:02:27.239><c> top</c><00:02:27.360><c> of</c><00:02:27.480><c> that</c><00:02:27.660><c> we</c><00:02:28.319><c> can</c><00:02:28.440><c> customize</c><00:02:28.800><c> the</c>

00:02:28.970 --> 00:02:28.980 align:start position:0%
and on top of that we can customize the
 

00:02:28.980 --> 00:02:30.949 align:start position:0%
and on top of that we can customize the
representation<00:02:29.459><c> of</c><00:02:29.760><c> the</c><00:02:29.819><c> metadata</c><00:02:30.420><c> when</c><00:02:30.780><c> this</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
representation of the metadata when this
 

00:02:30.959 --> 00:02:33.110 align:start position:0%
representation of the metadata when this
document<00:02:31.260><c> gets</c><00:02:31.620><c> transformed</c><00:02:31.980><c> into</c><00:02:32.099><c> a</c><00:02:32.400><c> string</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
document gets transformed into a string
 

00:02:33.120 --> 00:02:35.589 align:start position:0%
document gets transformed into a string
so<00:02:33.480><c> we</c><00:02:33.599><c> can</c><00:02:33.720><c> set</c><00:02:33.900><c> the</c><00:02:34.080><c> separator</c><00:02:34.440><c> between</c><00:02:34.800><c> each</c>

00:02:35.589 --> 00:02:35.599 align:start position:0%
so we can set the separator between each
 

00:02:35.599 --> 00:02:38.210 align:start position:0%
so we can set the separator between each
metadata<00:02:36.599><c> field</c><00:02:36.840><c> in</c><00:02:37.140><c> the</c><00:02:37.319><c> dictionary</c>

00:02:38.210 --> 00:02:38.220 align:start position:0%
metadata field in the dictionary
 

00:02:38.220 --> 00:02:40.610 align:start position:0%
metadata field in the dictionary
we<00:02:38.640><c> could</c><00:02:38.819><c> see</c><00:02:39.120><c> what</c><00:02:39.660><c> each</c><00:02:39.900><c> key</c><00:02:40.080><c> value</c><00:02:40.319><c> is</c>

00:02:40.610 --> 00:02:40.620 align:start position:0%
we could see what each key value is
 

00:02:40.620 --> 00:02:42.710 align:start position:0%
we could see what each key value is
formatted<00:02:41.099><c> as</c><00:02:41.280><c> here</c><00:02:41.940><c> I've</c><00:02:42.180><c> customized</c><00:02:42.660><c> it</c>

00:02:42.710 --> 00:02:42.720 align:start position:0%
formatted as here I've customized it
 

00:02:42.720 --> 00:02:45.350 align:start position:0%
formatted as here I've customized it
with<00:02:42.840><c> a</c><00:02:43.019><c> little</c><00:02:43.080><c> arrow</c><00:02:43.440><c> and</c><00:02:44.280><c> then</c><00:02:44.459><c> on</c><00:02:45.120><c> top</c><00:02:45.239><c> of</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
with a little arrow and then on top of
 

00:02:45.360 --> 00:02:46.970 align:start position:0%
with a little arrow and then on top of
that<00:02:45.480><c> when</c><00:02:45.720><c> the</c><00:02:45.840><c> metadata</c><00:02:46.319><c> gets</c><00:02:46.620><c> inserted</c>

00:02:46.970 --> 00:02:46.980 align:start position:0%
that when the metadata gets inserted
 

00:02:46.980 --> 00:02:49.250 align:start position:0%
that when the metadata gets inserted
next<00:02:47.220><c> to</c><00:02:47.400><c> the</c><00:02:47.519><c> text</c><00:02:47.760><c> we</c><00:02:48.420><c> can</c><00:02:48.599><c> add</c><00:02:48.780><c> a</c><00:02:48.959><c> template</c>

00:02:49.250 --> 00:02:49.260 align:start position:0%
next to the text we can add a template
 

00:02:49.260 --> 00:02:50.930 align:start position:0%
next to the text we can add a template
for<00:02:49.440><c> what</c><00:02:49.620><c> that</c><00:02:49.739><c> looks</c><00:02:50.040><c> like</c><00:02:50.099><c> and</c><00:02:50.580><c> here</c><00:02:50.760><c> I've</c>

00:02:50.930 --> 00:02:50.940 align:start position:0%
for what that looks like and here I've
 

00:02:50.940 --> 00:02:53.150 align:start position:0%
for what that looks like and here I've
added<00:02:51.239><c> a</c><00:02:51.360><c> little</c><00:02:51.480><c> label</c><00:02:51.720><c> for</c><00:02:51.900><c> metadata</c><00:02:52.500><c> a</c>

00:02:53.150 --> 00:02:53.160 align:start position:0%
added a little label for metadata a
 

00:02:53.160 --> 00:02:55.009 align:start position:0%
added a little label for metadata a
little<00:02:53.340><c> line</c><00:02:53.580><c> divider</c><00:02:54.060><c> a</c><00:02:54.360><c> little</c><00:02:54.540><c> label</c><00:02:54.900><c> for</c>

00:02:55.009 --> 00:02:55.019 align:start position:0%
little line divider a little label for
 

00:02:55.019 --> 00:02:56.449 align:start position:0%
little line divider a little label for
the<00:02:55.140><c> content</c>

00:02:56.449 --> 00:02:56.459 align:start position:0%
the content
 

00:02:56.459 --> 00:02:58.910 align:start position:0%
the content
super<00:02:57.060><c> simple</c><00:02:57.300><c> but</c><00:02:58.019><c> this</c><00:02:58.140><c> allows</c><00:02:58.500><c> for</c><00:02:58.620><c> some</c>

00:02:58.910 --> 00:02:58.920 align:start position:0%
super simple but this allows for some
 

00:02:58.920 --> 00:03:00.410 align:start position:0%
super simple but this allows for some
pretty<00:02:59.099><c> I</c><00:02:59.760><c> would</c><00:02:59.819><c> say</c><00:03:00.000><c> complex</c>

00:03:00.410 --> 00:03:00.420 align:start position:0%
pretty I would say complex
 

00:03:00.420 --> 00:03:03.229 align:start position:0%
pretty I would say complex
representations<00:03:01.519><c> and</c><00:03:02.519><c> customization</c><00:03:03.060><c> in</c>

00:03:03.229 --> 00:03:03.239 align:start position:0%
representations and customization in
 

00:03:03.239 --> 00:03:05.030 align:start position:0%
representations and customization in
Lama<00:03:03.540><c> index</c>

00:03:05.030 --> 00:03:05.040 align:start position:0%
Lama index
 

00:03:05.040 --> 00:03:07.309 align:start position:0%
Lama index
so<00:03:05.400><c> that's</c><00:03:05.519><c> how</c><00:03:05.700><c> to</c><00:03:05.819><c> create</c><00:03:06.060><c> documents</c><00:03:06.660><c> and</c>

00:03:07.309 --> 00:03:07.319 align:start position:0%
so that's how to create documents and
 

00:03:07.319 --> 00:03:09.050 align:start position:0%
so that's how to create documents and
now<00:03:07.500><c> we're</c><00:03:07.680><c> actually</c><00:03:07.800><c> going</c><00:03:08.040><c> to</c><00:03:08.160><c> cover</c><00:03:08.340><c> how</c><00:03:08.879><c> we</c>

00:03:09.050 --> 00:03:09.060 align:start position:0%
now we're actually going to cover how we
 

00:03:09.060 --> 00:03:10.070 align:start position:0%
now we're actually going to cover how we
can

00:03:10.070 --> 00:03:10.080 align:start position:0%
can
 

00:03:10.080 --> 00:03:12.589 align:start position:0%
can
you<00:03:10.560><c> know</c><00:03:10.620><c> create</c><00:03:11.099><c> Dom</c><00:03:11.459><c> documents</c><00:03:12.120><c> for</c><00:03:12.420><c> the</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
you know create Dom documents for the
 

00:03:12.599 --> 00:03:14.809 align:start position:0%
you know create Dom documents for the
Llama<00:03:12.900><c> index</c><00:03:13.260><c> documentation</c>

00:03:14.809 --> 00:03:14.819 align:start position:0%
Llama index documentation
 

00:03:14.819 --> 00:03:17.830 align:start position:0%
Llama index documentation
so<00:03:15.300><c> I</c><00:03:15.659><c> have</c><00:03:15.780><c> a</c><00:03:16.080><c> notebook</c><00:03:16.440><c> here</c><00:03:16.739><c> to</c><00:03:17.040><c> cover</c><00:03:17.280><c> that</c>

00:03:17.830 --> 00:03:17.840 align:start position:0%
so I have a notebook here to cover that
 

00:03:17.840 --> 00:03:20.750 align:start position:0%
so I have a notebook here to cover that
what<00:03:18.840><c> I've</c><00:03:18.959><c> done</c><00:03:19.200><c> ahead</c><00:03:19.620><c> of</c><00:03:19.739><c> time</c><00:03:19.860><c> here</c><00:03:20.159><c> is</c>

00:03:20.750 --> 00:03:20.760 align:start position:0%
what I've done ahead of time here is
 

00:03:20.760 --> 00:03:22.850 align:start position:0%
what I've done ahead of time here is
I've<00:03:21.000><c> created</c><00:03:21.360><c> a</c><00:03:21.840><c> custom</c>

00:03:22.850 --> 00:03:22.860 align:start position:0%
I've created a custom
 

00:03:22.860 --> 00:03:26.630 align:start position:0%
I've created a custom
document<00:03:23.580><c> loader</c><00:03:24.379><c> for</c><00:03:25.379><c> the</c><00:03:25.620><c> markdown</c><00:03:25.980><c> docs</c><00:03:26.459><c> in</c>

00:03:26.630 --> 00:03:26.640 align:start position:0%
document loader for the markdown docs in
 

00:03:26.640 --> 00:03:28.790 align:start position:0%
document loader for the markdown docs in
lawmandex<00:03:27.540><c> so</c><00:03:28.200><c> we're</c><00:03:28.319><c> only</c><00:03:28.500><c> going</c><00:03:28.620><c> to</c><00:03:28.680><c> worry</c>

00:03:28.790 --> 00:03:28.800 align:start position:0%
lawmandex so we're only going to worry
 

00:03:28.800 --> 00:03:31.009 align:start position:0%
lawmandex so we're only going to worry
about<00:03:28.860><c> the</c><00:03:29.099><c> markdown</c><00:03:29.459><c> documents</c><00:03:30.000><c> because</c><00:03:30.180><c> we</c>

00:03:31.009 --> 00:03:31.019 align:start position:0%
about the markdown documents because we
 

00:03:31.019 --> 00:03:32.809 align:start position:0%
about the markdown documents because we
just<00:03:31.200><c> want</c><00:03:31.319><c> to</c><00:03:31.379><c> keep</c><00:03:31.500><c> the</c><00:03:31.680><c> scope</c><00:03:31.860><c> a</c><00:03:32.159><c> little</c><00:03:32.280><c> as</c>

00:03:32.809 --> 00:03:32.819 align:start position:0%
just want to keep the scope a little as
 

00:03:32.819 --> 00:03:34.790 align:start position:0%
just want to keep the scope a little as
a<00:03:33.300><c> little</c><00:03:33.420><c> narrow</c><00:03:33.780><c> and</c><00:03:34.080><c> we</c><00:03:34.319><c> should</c><00:03:34.500><c> still</c><00:03:34.680><c> be</c>

00:03:34.790 --> 00:03:34.800 align:start position:0%
a little narrow and we should still be
 

00:03:34.800 --> 00:03:36.470 align:start position:0%
a little narrow and we should still be
able<00:03:34.920><c> to</c><00:03:35.040><c> get</c><00:03:35.159><c> a</c><00:03:35.280><c> useful</c><00:03:35.519><c> chatbot</c><00:03:36.000><c> with</c><00:03:36.239><c> just</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
able to get a useful chatbot with just
 

00:03:36.480 --> 00:03:39.110 align:start position:0%
able to get a useful chatbot with just
our<00:03:36.780><c> markdown</c><00:03:37.260><c> documents</c><00:03:37.739><c> in</c><00:03:37.980><c> our</c><00:03:38.159><c> in</c><00:03:38.819><c> our</c>

00:03:39.110 --> 00:03:39.120 align:start position:0%
our markdown documents in our in our
 

00:03:39.120 --> 00:03:41.449 align:start position:0%
our markdown documents in our in our
documentation

00:03:41.449 --> 00:03:41.459 align:start position:0%
documentation
 

00:03:41.459 --> 00:03:43.009 align:start position:0%
documentation
um<00:03:41.519><c> and</c><00:03:41.700><c> I've</c><00:03:41.819><c> just</c><00:03:42.060><c> made</c><00:03:42.180><c> a</c><00:03:42.360><c> function</c><00:03:42.599><c> here</c><00:03:42.840><c> to</c>

00:03:43.009 --> 00:03:43.019 align:start position:0%
um and I've just made a function here to
 

00:03:43.019 --> 00:03:44.990 align:start position:0%
um and I've just made a function here to
basically<00:03:43.379><c> go</c><00:03:43.560><c> through</c><00:03:43.739><c> and</c><00:03:43.980><c> parse</c><00:03:44.400><c> the</c>

00:03:44.990 --> 00:03:45.000 align:start position:0%
basically go through and parse the
 

00:03:45.000 --> 00:03:46.369 align:start position:0%
basically go through and parse the
markdown<00:03:45.420><c> which</c><00:03:45.780><c> is</c><00:03:45.840><c> like</c><00:03:46.080><c> a</c><00:03:46.260><c> super</c>

00:03:46.369 --> 00:03:46.379 align:start position:0%
markdown which is like a super
 

00:03:46.379 --> 00:03:48.410 align:start position:0%
markdown which is like a super
structured<00:03:46.920><c> format</c><00:03:47.340><c> we</c><00:03:48.000><c> know</c><00:03:48.120><c> where</c><00:03:48.299><c> the</c>

00:03:48.410 --> 00:03:48.420 align:start position:0%
structured format we know where the
 

00:03:48.420 --> 00:03:49.610 align:start position:0%
structured format we know where the
headers<00:03:48.720><c> are</c><00:03:48.900><c> we</c><00:03:49.080><c> know</c><00:03:49.200><c> where</c><00:03:49.379><c> the</c><00:03:49.500><c> code</c>

00:03:49.610 --> 00:03:49.620 align:start position:0%
headers are we know where the code
 

00:03:49.620 --> 00:03:51.830 align:start position:0%
headers are we know where the code
blocks<00:03:50.099><c> are</c><00:03:50.220><c> uh</c><00:03:51.180><c> and</c><00:03:51.360><c> there's</c><00:03:51.480><c> just</c><00:03:51.659><c> a</c><00:03:51.780><c> bunch</c>

00:03:51.830 --> 00:03:51.840 align:start position:0%
blocks are uh and there's just a bunch
 

00:03:51.840 --> 00:03:53.690 align:start position:0%
blocks are uh and there's just a bunch
of<00:03:51.959><c> handling</c><00:03:52.260><c> that</c><00:03:52.500><c> for</c><00:03:52.620><c> that</c><00:03:52.799><c> in</c><00:03:52.920><c> here</c>

00:03:53.690 --> 00:03:53.700 align:start position:0%
of handling that for that in here
 

00:03:53.700 --> 00:03:55.729 align:start position:0%
of handling that for that in here
now<00:03:54.000><c> while</c><00:03:54.239><c> llama</c><00:03:54.540><c> index</c><00:03:54.900><c> does</c><00:03:55.080><c> have</c><00:03:55.379><c> a</c>

00:03:55.729 --> 00:03:55.739 align:start position:0%
now while llama index does have a
 

00:03:55.739 --> 00:03:57.949 align:start position:0%
now while llama index does have a
built-in<00:03:56.099><c> markdown</c><00:03:56.700><c> reader</c><00:03:57.180><c> uh</c><00:03:57.780><c> building</c>

00:03:57.949 --> 00:03:57.959 align:start position:0%
built-in markdown reader uh building
 

00:03:57.959 --> 00:04:00.710 align:start position:0%
built-in markdown reader uh building
this<00:03:58.140><c> on</c><00:03:58.319><c> your</c><00:03:58.560><c> own</c><00:03:58.739><c> gets</c><00:03:59.459><c> you</c><00:03:59.580><c> one</c><00:04:00.540><c> more</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
this on your own gets you one more
 

00:04:00.720 --> 00:04:02.809 align:start position:0%
this on your own gets you one more
familiar<00:04:01.140><c> with</c><00:04:01.440><c> how</c><00:04:01.680><c> to</c><00:04:01.799><c> create</c><00:04:02.159><c> documents</c>

00:04:02.809 --> 00:04:02.819 align:start position:0%
familiar with how to create documents
 

00:04:02.819 --> 00:04:04.970 align:start position:0%
familiar with how to create documents
and<00:04:03.420><c> two</c><00:04:03.599><c> if</c><00:04:03.840><c> we</c><00:04:04.019><c> ever</c><00:04:04.140><c> need</c><00:04:04.319><c> to</c><00:04:04.440><c> customize</c>

00:04:04.970 --> 00:04:04.980 align:start position:0%
and two if we ever need to customize
 

00:04:04.980 --> 00:04:06.949 align:start position:0%
and two if we ever need to customize
further<00:04:05.519><c> how</c><00:04:06.060><c> these</c><00:04:06.299><c> documents</c><00:04:06.720><c> are</c><00:04:06.780><c> being</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
further how these documents are being
 

00:04:06.959 --> 00:04:09.589 align:start position:0%
further how these documents are being
loaded<00:04:07.379><c> now</c><00:04:08.220><c> we</c><00:04:08.459><c> have</c><00:04:08.640><c> straight</c><00:04:09.060><c> access</c><00:04:09.299><c> to</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
loaded now we have straight access to
 

00:04:09.599 --> 00:04:11.690 align:start position:0%
loaded now we have straight access to
how<00:04:09.780><c> that's</c><00:04:09.900><c> being</c><00:04:10.140><c> loaded</c><00:04:10.620><c> for</c><00:04:11.459><c> instance</c>

00:04:11.690 --> 00:04:11.700 align:start position:0%
how that's being loaded for instance
 

00:04:11.700 --> 00:04:13.729 align:start position:0%
how that's being loaded for instance
here<00:04:11.939><c> one</c><00:04:12.420><c> kind</c><00:04:12.659><c> of</c><00:04:12.780><c> customization</c><00:04:13.319><c> that</c><00:04:13.620><c> I</c>

00:04:13.729 --> 00:04:13.739 align:start position:0%
here one kind of customization that I
 

00:04:13.739 --> 00:04:17.150 align:start position:0%
here one kind of customization that I
did<00:04:13.860><c> make</c><00:04:14.040><c> was</c><00:04:14.939><c> all</c><00:04:15.599><c> the</c><00:04:15.720><c> code</c><00:04:15.900><c> blocks</c>

00:04:17.150 --> 00:04:17.160 align:start position:0%
did make was all the code blocks
 

00:04:17.160 --> 00:04:19.909 align:start position:0%
did make was all the code blocks
keep<00:04:17.880><c> track</c><00:04:18.120><c> of</c><00:04:18.600><c> the</c><00:04:18.959><c> paragraph</c><00:04:19.380><c> above</c><00:04:19.799><c> it</c>

00:04:19.909 --> 00:04:19.919 align:start position:0%
keep track of the paragraph above it
 

00:04:19.919 --> 00:04:22.189 align:start position:0%
keep track of the paragraph above it
because<00:04:20.400><c> often</c><00:04:20.940><c> that</c><00:04:21.180><c> paragraph</c><00:04:21.540><c> above</c><00:04:21.840><c> it</c><00:04:22.019><c> is</c>

00:04:22.189 --> 00:04:22.199 align:start position:0%
because often that paragraph above it is
 

00:04:22.199 --> 00:04:24.890 align:start position:0%
because often that paragraph above it is
introducing<00:04:22.919><c> that</c><00:04:23.699><c> code</c><00:04:23.880><c> block</c><00:04:24.180><c> so</c><00:04:24.600><c> it's</c><00:04:24.720><c> kind</c>

00:04:24.890 --> 00:04:24.900 align:start position:0%
introducing that code block so it's kind
 

00:04:24.900 --> 00:04:27.050 align:start position:0%
introducing that code block so it's kind
of<00:04:24.960><c> like</c><00:04:25.080><c> an</c><00:04:25.320><c> extra</c><00:04:25.440><c> piece</c><00:04:25.919><c> of</c><00:04:25.979><c> reference</c><00:04:26.759><c> text</c>

00:04:27.050 --> 00:04:27.060 align:start position:0%
of like an extra piece of reference text
 

00:04:27.060 --> 00:04:29.689 align:start position:0%
of like an extra piece of reference text
that<00:04:27.479><c> we</c><00:04:27.660><c> can</c><00:04:27.780><c> use</c><00:04:28.380><c> later</c><00:04:28.620><c> on</c><00:04:28.860><c> maybe</c><00:04:29.160><c> we'll</c><00:04:29.460><c> see</c>

00:04:29.689 --> 00:04:29.699 align:start position:0%
that we can use later on maybe we'll see
 

00:04:29.699 --> 00:04:32.390 align:start position:0%
that we can use later on maybe we'll see
to<00:04:30.479><c> help</c><00:04:30.660><c> the</c><00:04:31.139><c> question</c><00:04:31.320><c> answer</c><00:04:31.680><c> process</c><00:04:32.160><c> work</c>

00:04:32.390 --> 00:04:32.400 align:start position:0%
to help the question answer process work
 

00:04:32.400 --> 00:04:34.189 align:start position:0%
to help the question answer process work
better

00:04:34.189 --> 00:04:34.199 align:start position:0%
better
 

00:04:34.199 --> 00:04:36.469 align:start position:0%
better
so<00:04:34.740><c> just</c><00:04:34.979><c> a</c><00:04:35.100><c> quick</c><00:04:35.280><c> demo</c><00:04:35.699><c> of</c><00:04:35.940><c> how</c><00:04:36.240><c> this</c>

00:04:36.469 --> 00:04:36.479 align:start position:0%
so just a quick demo of how this
 

00:04:36.479 --> 00:04:37.969 align:start position:0%
so just a quick demo of how this
actually<00:04:36.660><c> works</c>

00:04:37.969 --> 00:04:37.979 align:start position:0%
actually works
 

00:04:37.979 --> 00:04:40.070 align:start position:0%
actually works
I'm<00:04:38.220><c> going</c><00:04:38.400><c> to</c><00:04:38.520><c> append</c><00:04:38.940><c> to</c><00:04:39.120><c> my</c><00:04:39.360><c> path</c><00:04:39.479><c> here</c><00:04:39.720><c> so</c><00:04:39.960><c> I</c>

00:04:40.070 --> 00:04:40.080 align:start position:0%
I'm going to append to my path here so I
 

00:04:40.080 --> 00:04:42.830 align:start position:0%
I'm going to append to my path here so I
can<00:04:40.199><c> actually</c><00:04:40.320><c> load</c><00:04:40.860><c> from</c><00:04:41.699><c> my</c><00:04:42.120><c> little</c><00:04:42.419><c> folder</c>

00:04:42.830 --> 00:04:42.840 align:start position:0%
can actually load from my little folder
 

00:04:42.840 --> 00:04:45.290 align:start position:0%
can actually load from my little folder
here<00:04:43.020><c> with</c><00:04:43.560><c> my</c><00:04:43.740><c> script</c>

00:04:45.290 --> 00:04:45.300 align:start position:0%
here with my script
 

00:04:45.300 --> 00:04:46.909 align:start position:0%
here with my script
um<00:04:45.360><c> I</c><00:04:45.540><c> made</c><00:04:45.720><c> a</c><00:04:45.900><c> quick</c><00:04:46.020><c> helper</c><00:04:46.380><c> function</c><00:04:46.680><c> here</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
um I made a quick helper function here
 

00:04:46.919 --> 00:04:49.730 align:start position:0%
um I made a quick helper function here
to<00:04:47.280><c> load</c><00:04:47.699><c> the</c><00:04:48.060><c> markdown</c><00:04:48.419><c> documents</c><00:04:49.020><c> and</c><00:04:49.560><c> here</c>

00:04:49.730 --> 00:04:49.740 align:start position:0%
to load the markdown documents and here
 

00:04:49.740 --> 00:04:51.590 align:start position:0%
to load the markdown documents and here
I'm<00:04:49.979><c> just</c><00:04:50.160><c> saying</c><00:04:50.460><c> exclude</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
I'm just saying exclude
 

00:04:51.600 --> 00:04:54.770 align:start position:0%
I'm just saying exclude
everything<00:04:52.639><c> except</c><00:04:53.639><c> for</c><00:04:53.820><c> markdown</c><00:04:54.240><c> documents</c>

00:04:54.770 --> 00:04:54.780 align:start position:0%
everything except for markdown documents
 

00:04:54.780 --> 00:04:56.390 align:start position:0%
everything except for markdown documents
we<00:04:54.960><c> only</c><00:04:55.080><c> want</c><00:04:55.259><c> to</c><00:04:55.380><c> we</c><00:04:55.560><c> only</c><00:04:55.680><c> care</c><00:04:55.919><c> about</c><00:04:56.040><c> those</c>

00:04:56.390 --> 00:04:56.400 align:start position:0%
we only want to we only care about those
 

00:04:56.400 --> 00:05:00.230 align:start position:0%
we only want to we only care about those
and<00:04:57.120><c> then</c><00:04:57.240><c> here</c><00:04:57.600><c> I</c><00:04:58.199><c> set</c><00:04:58.500><c> a</c><00:04:59.100><c> custom</c><00:04:59.280><c> loader</c><00:05:00.000><c> for</c>

00:05:00.230 --> 00:05:00.240 align:start position:0%
and then here I set a custom loader for
 

00:05:00.240 --> 00:05:03.350 align:start position:0%
and then here I set a custom loader for
that<00:05:01.020><c> I</c><00:05:01.199><c> was</c><00:05:01.560><c> just</c><00:05:01.740><c> showing</c><00:05:02.100><c> uh</c><00:05:02.940><c> so</c><00:05:03.060><c> basically</c>

00:05:03.350 --> 00:05:03.360 align:start position:0%
that I was just showing uh so basically
 

00:05:03.360 --> 00:05:05.150 align:start position:0%
that I was just showing uh so basically
what<00:05:03.479><c> this</c><00:05:03.600><c> does</c><00:05:03.720><c> is</c><00:05:03.900><c> it</c><00:05:04.020><c> sets</c><00:05:04.320><c> it</c><00:05:04.380><c> up</c><00:05:04.500><c> for</c><00:05:04.680><c> DOT</c>

00:05:05.150 --> 00:05:05.160 align:start position:0%
what this does is it sets it up for DOT
 

00:05:05.160 --> 00:05:07.850 align:start position:0%
what this does is it sets it up for DOT
MD<00:05:05.400><c> files</c><00:05:05.820><c> we'll</c><00:05:06.000><c> use</c><00:05:06.240><c> my</c><00:05:06.540><c> loader</c><00:05:07.020><c> and</c><00:05:07.740><c> then</c>

00:05:07.850 --> 00:05:07.860 align:start position:0%
MD files we'll use my loader and then
 

00:05:07.860 --> 00:05:10.730 align:start position:0%
MD files we'll use my loader and then
lastly<00:05:08.160><c> I</c><00:05:08.340><c> set</c><00:05:08.460><c> recursive</c><00:05:08.940><c> equal</c><00:05:09.120><c> to</c><00:05:09.360><c> True</c><00:05:09.740><c> our</c>

00:05:10.730 --> 00:05:10.740 align:start position:0%
lastly I set recursive equal to True our
 

00:05:10.740 --> 00:05:12.770 align:start position:0%
lastly I set recursive equal to True our
documentation<00:05:11.220><c> is</c><00:05:11.520><c> a</c><00:05:11.699><c> little</c><00:05:11.759><c> nested</c><00:05:12.360><c> so</c><00:05:12.600><c> it's</c>

00:05:12.770 --> 00:05:12.780 align:start position:0%
documentation is a little nested so it's
 

00:05:12.780 --> 00:05:14.390 align:start position:0%
documentation is a little nested so it's
going<00:05:12.960><c> to</c><00:05:13.080><c> recursively</c><00:05:13.620><c> go</c><00:05:13.800><c> through</c><00:05:13.979><c> every</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
going to recursively go through every
 

00:05:14.400 --> 00:05:16.850 align:start position:0%
going to recursively go through every
folder<00:05:15.240><c> and</c><00:05:15.360><c> file</c><00:05:15.660><c> and</c><00:05:15.960><c> find</c><00:05:16.139><c> every</c><00:05:16.380><c> markdown</c>

00:05:16.850 --> 00:05:16.860 align:start position:0%
folder and file and find every markdown
 

00:05:16.860 --> 00:05:18.290 align:start position:0%
folder and file and find every markdown
file

00:05:18.290 --> 00:05:18.300 align:start position:0%
file
 

00:05:18.300 --> 00:05:20.390 align:start position:0%
file
that's<00:05:18.780><c> as</c><00:05:18.960><c> simple</c><00:05:19.139><c> as</c><00:05:19.259><c> that</c>

00:05:20.390 --> 00:05:20.400 align:start position:0%
that's as simple as that
 

00:05:20.400 --> 00:05:21.070 align:start position:0%
that's as simple as that
um

00:05:21.070 --> 00:05:21.080 align:start position:0%
um
 

00:05:21.080 --> 00:05:23.510 align:start position:0%
um
in<00:05:22.080><c> I</c><00:05:22.440><c> have</c><00:05:22.560><c> a</c><00:05:22.680><c> folder</c><00:05:22.979><c> of</c><00:05:23.220><c> all</c><00:05:23.400><c> the</c>

00:05:23.510 --> 00:05:23.520 align:start position:0%
in I have a folder of all the
 

00:05:23.520 --> 00:05:26.870 align:start position:0%
in I have a folder of all the
documentation<00:05:24.500><c> uh</c><00:05:25.500><c> every</c><00:05:25.919><c> folder</c><00:05:26.580><c> from</c><00:05:26.699><c> our</c>

00:05:26.870 --> 00:05:26.880 align:start position:0%
documentation uh every folder from our
 

00:05:26.880 --> 00:05:28.550 align:start position:0%
documentation uh every folder from our
documentation

00:05:28.550 --> 00:05:28.560 align:start position:0%
documentation
 

00:05:28.560 --> 00:05:30.950 align:start position:0%
documentation
um I'm<00:05:28.919><c> loading</c><00:05:29.280><c> it</c><00:05:29.400><c> into</c><00:05:29.520><c> separate</c><00:05:30.120><c> lists</c><00:05:30.780><c> of</c>

00:05:30.950 --> 00:05:30.960 align:start position:0%
um I'm loading it into separate lists of
 

00:05:30.960 --> 00:05:33.770 align:start position:0%
um I'm loading it into separate lists of
documents<00:05:31.560><c> because</c><00:05:32.520><c> each</c><00:05:33.000><c> folder</c><00:05:33.360><c> kind</c><00:05:33.660><c> of</c>

00:05:33.770 --> 00:05:33.780 align:start position:0%
documents because each folder kind of
 

00:05:33.780 --> 00:05:36.170 align:start position:0%
documents because each folder kind of
captures<00:05:34.500><c> a</c><00:05:34.979><c> very</c><00:05:35.160><c> specific</c><00:05:35.460><c> part</c><00:05:35.699><c> of</c><00:05:35.880><c> llama</c>

00:05:36.170 --> 00:05:36.180 align:start position:0%
captures a very specific part of llama
 

00:05:36.180 --> 00:05:38.749 align:start position:0%
captures a very specific part of llama
index<00:05:36.600><c> so</c><00:05:37.440><c> if</c><00:05:37.680><c> you're</c><00:05:37.800><c> building</c><00:05:38.039><c> you</c><00:05:38.520><c> know</c><00:05:38.580><c> a</c>

00:05:38.749 --> 00:05:38.759 align:start position:0%
index so if you're building you know a
 

00:05:38.759 --> 00:05:40.430 align:start position:0%
index so if you're building you know a
chat<00:05:38.940><c> bot</c><00:05:39.180><c> over</c><00:05:39.300><c> the</c><00:05:39.479><c> documentation</c><00:05:39.960><c> it</c><00:05:40.259><c> can</c>

00:05:40.430 --> 00:05:40.440 align:start position:0%
chat bot over the documentation it can
 

00:05:40.440 --> 00:05:44.090 align:start position:0%
chat bot over the documentation it can
be<00:05:40.500><c> helpful</c><00:05:40.800><c> to</c><00:05:41.160><c> sort</c><00:05:41.880><c> this</c><00:05:42.180><c> ahead</c><00:05:42.600><c> of</c><00:05:42.720><c> time</c><00:05:43.100><c> to</c>

00:05:44.090 --> 00:05:44.100 align:start position:0%
be helpful to sort this ahead of time to
 

00:05:44.100 --> 00:05:47.029 align:start position:0%
be helpful to sort this ahead of time to
make<00:05:44.280><c> question</c><00:05:44.699><c> answering</c><00:05:45.360><c> easier</c><00:05:45.840><c> later</c>

00:05:47.029 --> 00:05:47.039 align:start position:0%
make question answering easier later
 

00:05:47.039 --> 00:05:49.490 align:start position:0%
make question answering easier later
so<00:05:47.880><c> load</c><00:05:48.479><c> those</c><00:05:48.660><c> documents</c><00:05:49.080><c> it's</c><00:05:49.259><c> pretty</c>

00:05:49.490 --> 00:05:49.500 align:start position:0%
so load those documents it's pretty
 

00:05:49.500 --> 00:05:51.409 align:start position:0%
so load those documents it's pretty
quick<00:05:49.699><c> and</c><00:05:50.699><c> now</c><00:05:50.820><c> we</c><00:05:51.000><c> can</c><00:05:51.060><c> just</c><00:05:51.240><c> kind</c><00:05:51.360><c> of</c>

00:05:51.409 --> 00:05:51.419 align:start position:0%
quick and now we can just kind of
 

00:05:51.419 --> 00:05:52.790 align:start position:0%
quick and now we can just kind of
investigate<00:05:51.900><c> a</c><00:05:52.139><c> little</c><00:05:52.199><c> bit</c><00:05:52.320><c> what</c><00:05:52.620><c> this</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
investigate a little bit what this
 

00:05:52.800 --> 00:05:54.830 align:start position:0%
investigate a little bit what this
actually<00:05:52.979><c> looks</c><00:05:53.340><c> like</c><00:05:53.460><c> so</c><00:05:54.360><c> I'm</c><00:05:54.479><c> going</c><00:05:54.600><c> to</c><00:05:54.660><c> grab</c>

00:05:54.830 --> 00:05:54.840 align:start position:0%
actually looks like so I'm going to grab
 

00:05:54.840 --> 00:05:56.629 align:start position:0%
actually looks like so I'm going to grab
the<00:05:55.020><c> agent</c><00:05:55.380><c> docs</c><00:05:55.800><c> there</c>

00:05:56.629 --> 00:05:56.639 align:start position:0%
the agent docs there
 

00:05:56.639 --> 00:05:58.790 align:start position:0%
the agent docs there
uh<00:05:57.300><c> you</c><00:05:57.539><c> could</c><00:05:57.660><c> see</c><00:05:57.840><c> you</c><00:05:58.199><c> know</c><00:05:58.259><c> it's</c><00:05:58.440><c> printed</c>

00:05:58.790 --> 00:05:58.800 align:start position:0%
uh you could see you know it's printed
 

00:05:58.800 --> 00:05:59.689 align:start position:0%
uh you could see you know it's printed
out

00:05:59.689 --> 00:05:59.699 align:start position:0%
out
 

00:05:59.699 --> 00:06:00.890 align:start position:0%
out
uh

00:06:00.890 --> 00:06:00.900 align:start position:0%
uh
 

00:06:00.900 --> 00:06:03.409 align:start position:0%
uh
all<00:06:01.560><c> our</c><00:06:01.800><c> metadata</c><00:06:02.460><c> at</c><00:06:02.580><c> the</c><00:06:02.699><c> top</c><00:06:02.820><c> here</c><00:06:03.060><c> so</c><00:06:03.300><c> you</c>

00:06:03.409 --> 00:06:03.419 align:start position:0%
all our metadata at the top here so you
 

00:06:03.419 --> 00:06:05.110 align:start position:0%
all our metadata at the top here so you
can<00:06:03.479><c> see</c><00:06:03.600><c> we're</c><00:06:03.780><c> keeping</c><00:06:04.139><c> track</c><00:06:04.259><c> of</c><00:06:04.500><c> file</c><00:06:04.740><c> name</c>

00:06:05.110 --> 00:06:05.120 align:start position:0%
can see we're keeping track of file name
 

00:06:05.120 --> 00:06:07.490 align:start position:0%
can see we're keeping track of file name
content<00:06:06.120><c> type</c><00:06:06.300><c> is</c><00:06:06.660><c> going</c><00:06:06.840><c> to</c><00:06:06.900><c> be</c><00:06:06.960><c> either</c><00:06:07.139><c> text</c>

00:06:07.490 --> 00:06:07.500 align:start position:0%
content type is going to be either text
 

00:06:07.500 --> 00:06:11.150 align:start position:0%
content type is going to be either text
or<00:06:07.740><c> code</c><00:06:08.060><c> and</c><00:06:09.060><c> the</c><00:06:09.660><c> header</c><00:06:10.139><c> path</c><00:06:10.560><c> which</c><00:06:11.039><c> is</c>

00:06:11.150 --> 00:06:11.160 align:start position:0%
or code and the header path which is
 

00:06:11.160 --> 00:06:14.570 align:start position:0%
or code and the header path which is
like<00:06:11.400><c> how</c><00:06:11.759><c> nested</c><00:06:12.500><c> the</c><00:06:13.500><c> markdown</c><00:06:13.979><c> headers</c><00:06:14.400><c> are</c>

00:06:14.570 --> 00:06:14.580 align:start position:0%
like how nested the markdown headers are
 

00:06:14.580 --> 00:06:16.490 align:start position:0%
like how nested the markdown headers are
so<00:06:14.759><c> obviously</c><00:06:15.180><c> right</c><00:06:15.419><c> now</c><00:06:15.539><c> we're</c><00:06:16.020><c> at</c><00:06:16.259><c> the</c><00:06:16.380><c> top</c>

00:06:16.490 --> 00:06:16.500 align:start position:0%
so obviously right now we're at the top
 

00:06:16.500 --> 00:06:19.370 align:start position:0%
so obviously right now we're at the top
level<00:06:16.740><c> it's</c><00:06:17.460><c> just</c><00:06:17.699><c> module</c><00:06:18.120><c> guides</c><00:06:18.479><c> if</c><00:06:18.900><c> I</c><00:06:19.080><c> go</c><00:06:19.199><c> to</c>

00:06:19.370 --> 00:06:19.380 align:start position:0%
level it's just module guides if I go to
 

00:06:19.380 --> 00:06:22.610 align:start position:0%
level it's just module guides if I go to
maybe<00:06:19.699><c> five</c><00:06:20.699><c> here</c><00:06:21.080><c> now</c><00:06:22.080><c> you</c><00:06:22.199><c> can</c><00:06:22.319><c> see</c><00:06:22.440><c> the</c>

00:06:22.610 --> 00:06:22.620 align:start position:0%
maybe five here now you can see the
 

00:06:22.620 --> 00:06:25.430 align:start position:0%
maybe five here now you can see the
header<00:06:22.919><c> path</c><00:06:23.039><c> is</c><00:06:23.280><c> data</c><00:06:23.699><c> agents</c><00:06:24.180><c> concept</c><00:06:24.780><c> tool</c>

00:06:25.430 --> 00:06:25.440 align:start position:0%
header path is data agents concept tool
 

00:06:25.440 --> 00:06:28.070 align:start position:0%
header path is data agents concept tool
abstractions<00:06:26.039><c> so</c><00:06:26.340><c> this</c><00:06:26.880><c> is</c><00:06:27.000><c> just</c><00:06:27.120><c> a</c><00:06:27.300><c> way</c><00:06:27.479><c> to</c>

00:06:28.070 --> 00:06:28.080 align:start position:0%
abstractions so this is just a way to
 

00:06:28.080 --> 00:06:31.010 align:start position:0%
abstractions so this is just a way to
help<00:06:28.740><c> the</c><00:06:28.979><c> LOM</c><00:06:29.400><c> understand</c><00:06:30.020><c> what</c>

00:06:31.010 --> 00:06:31.020 align:start position:0%
help the LOM understand what
 

00:06:31.020 --> 00:06:32.689 align:start position:0%
help the LOM understand what
documentation<00:06:31.560><c> it's</c><00:06:31.800><c> even</c><00:06:31.979><c> looking</c><00:06:32.220><c> at</c><00:06:32.400><c> right</c>

00:06:32.689 --> 00:06:32.699 align:start position:0%
documentation it's even looking at right
 

00:06:32.699 --> 00:06:34.909 align:start position:0%
documentation it's even looking at right
now

00:06:34.909 --> 00:06:34.919 align:start position:0%
 
 

00:06:34.919 --> 00:06:37.370 align:start position:0%
 
and<00:06:35.340><c> again</c><00:06:35.880><c> you</c><00:06:36.360><c> can</c><00:06:36.419><c> access</c><00:06:36.600><c> the</c><00:06:36.840><c> metadata</c>

00:06:37.370 --> 00:06:37.380 align:start position:0%
and again you can access the metadata
 

00:06:37.380 --> 00:06:39.290 align:start position:0%
and again you can access the metadata
directly<00:06:37.740><c> just</c><00:06:38.100><c> by</c><00:06:38.340><c> you</c><00:06:38.639><c> know</c><00:06:38.699><c> including</c><00:06:39.060><c> that</c>

00:06:39.290 --> 00:06:39.300 align:start position:0%
directly just by you know including that
 

00:06:39.300 --> 00:06:42.110 align:start position:0%
directly just by you know including that
little<00:06:39.479><c> dot</c><00:06:40.380><c> metadata</c><00:06:40.919><c> attribute</c><00:06:41.400><c> and</c><00:06:41.880><c> we</c><00:06:42.000><c> can</c>

00:06:42.110 --> 00:06:42.120 align:start position:0%
little dot metadata attribute and we can
 

00:06:42.120 --> 00:06:43.909 align:start position:0%
little dot metadata attribute and we can
see<00:06:42.240><c> here</c><00:06:42.479><c> that</c><00:06:42.720><c> it's</c><00:06:43.080><c> formatted</c><00:06:43.620><c> as</c><00:06:43.740><c> a</c>

00:06:43.909 --> 00:06:43.919 align:start position:0%
see here that it's formatted as a
 

00:06:43.919 --> 00:06:44.990 align:start position:0%
see here that it's formatted as a
dictionary

00:06:44.990 --> 00:06:45.000 align:start position:0%
dictionary
 

00:06:45.000 --> 00:06:46.730 align:start position:0%
dictionary
and<00:06:45.300><c> that</c><00:06:45.419><c> looks</c><00:06:45.660><c> good</c><00:06:45.780><c> but</c><00:06:46.380><c> we</c><00:06:46.500><c> can</c><00:06:46.620><c> actually</c>

00:06:46.730 --> 00:06:46.740 align:start position:0%
and that looks good but we can actually
 

00:06:46.740 --> 00:06:49.490 align:start position:0%
and that looks good but we can actually
customize<00:06:47.220><c> this</c><00:06:47.460><c> even</c><00:06:47.699><c> further</c><00:06:48.199><c> we</c><00:06:49.199><c> can</c><00:06:49.319><c> set</c>

00:06:49.490 --> 00:06:49.500 align:start position:0%
customize this even further we can set
 

00:06:49.500 --> 00:06:52.730 align:start position:0%
customize this even further we can set
up<00:06:49.680><c> our</c><00:06:49.979><c> own</c><00:06:50.160><c> text</c><00:06:50.639><c> template</c><00:06:51.360><c> so</c><00:06:52.259><c> like</c><00:06:52.500><c> before</c>

00:06:52.730 --> 00:06:52.740 align:start position:0%
up our own text template so like before
 

00:06:52.740 --> 00:06:54.409 align:start position:0%
up our own text template so like before
we<00:06:53.039><c> saw</c><00:06:53.220><c> on</c><00:06:53.340><c> the</c><00:06:53.520><c> slides</c><00:06:53.759><c> we</c><00:06:54.000><c> could</c><00:06:54.060><c> add</c><00:06:54.300><c> a</c>

00:06:54.409 --> 00:06:54.419 align:start position:0%
we saw on the slides we could add a
 

00:06:54.419 --> 00:06:56.689 align:start position:0%
we saw on the slides we could add a
little<00:06:54.600><c> header</c><00:06:54.900><c> for</c><00:06:55.020><c> the</c><00:06:55.139><c> metadata</c><00:06:55.680><c> a</c><00:06:56.460><c> little</c>

00:06:56.689 --> 00:06:56.699 align:start position:0%
little header for the metadata a little
 

00:06:56.699 --> 00:06:59.150 align:start position:0%
little header for the metadata a little
separator<00:06:57.180><c> and</c><00:06:57.539><c> then</c><00:06:57.600><c> the</c><00:06:57.780><c> content</c><00:06:58.199><c> we</c><00:06:58.979><c> can</c>

00:06:59.150 --> 00:06:59.160 align:start position:0%
separator and then the content we can
 

00:06:59.160 --> 00:07:01.610 align:start position:0%
separator and then the content we can
customize<00:06:59.520><c> the</c><00:06:59.759><c> metadata</c><00:07:00.300><c> template</c><00:07:00.660><c> and</c><00:07:01.500><c> then</c>

00:07:01.610 --> 00:07:01.620 align:start position:0%
customize the metadata template and then
 

00:07:01.620 --> 00:07:04.010 align:start position:0%
customize the metadata template and then
we<00:07:01.860><c> can</c><00:07:01.979><c> separate</c><00:07:02.340><c> them</c><00:07:02.520><c> by</c><00:07:02.819><c> spaces</c><00:07:03.360><c> so</c><00:07:03.780><c> now</c>

00:07:04.010 --> 00:07:04.020 align:start position:0%
we can separate them by spaces so now
 

00:07:04.020 --> 00:07:06.350 align:start position:0%
we can separate them by spaces so now
basically<00:07:04.740><c> the</c><00:07:04.919><c> metadata</c><00:07:05.460><c> will</c><00:07:05.580><c> be</c><00:07:05.759><c> comma</c>

00:07:06.350 --> 00:07:06.360 align:start position:0%
basically the metadata will be comma
 

00:07:06.360 --> 00:07:09.650 align:start position:0%
basically the metadata will be comma
separated<00:07:06.660><c> list</c><00:07:06.960><c> rather</c><00:07:07.800><c> than</c><00:07:08.100><c> line</c><00:07:08.580><c> by</c><00:07:08.819><c> line</c>

00:07:09.650 --> 00:07:09.660 align:start position:0%
separated list rather than line by line
 

00:07:09.660 --> 00:07:12.230 align:start position:0%
separated list rather than line by line
so<00:07:10.259><c> we</c><00:07:10.440><c> can</c><00:07:10.560><c> go</c><00:07:10.680><c> through</c><00:07:10.800><c> and</c><00:07:11.160><c> apply</c><00:07:11.460><c> this</c><00:07:11.759><c> to</c>

00:07:12.230 --> 00:07:12.240 align:start position:0%
so we can go through and apply this to
 

00:07:12.240 --> 00:07:15.290 align:start position:0%
so we can go through and apply this to
all<00:07:12.539><c> the</c><00:07:12.720><c> docs</c><00:07:13.080><c> in</c><00:07:13.319><c> the</c><00:07:13.560><c> agent</c><00:07:13.919><c> docs</c><00:07:14.340><c> list</c>

00:07:15.290 --> 00:07:15.300 align:start position:0%
all the docs in the agent docs list
 

00:07:15.300 --> 00:07:18.110 align:start position:0%
all the docs in the agent docs list
and<00:07:15.840><c> then</c><00:07:15.960><c> we</c><00:07:16.139><c> can</c><00:07:16.259><c> go</c><00:07:16.380><c> through</c><00:07:16.560><c> and</c><00:07:16.800><c> print</c><00:07:17.120><c> uh</c>

00:07:18.110 --> 00:07:18.120 align:start position:0%
and then we can go through and print uh
 

00:07:18.120 --> 00:07:20.210 align:start position:0%
and then we can go through and print uh
the<00:07:18.360><c> content</c><00:07:18.780><c> from</c><00:07:19.080><c> that</c><00:07:19.319><c> dock</c>

00:07:20.210 --> 00:07:20.220 align:start position:0%
the content from that dock
 

00:07:20.220 --> 00:07:22.129 align:start position:0%
the content from that dock
you'll<00:07:20.759><c> notice</c><00:07:20.940><c> here</c><00:07:21.060><c> I've</c><00:07:21.180><c> added</c><00:07:21.479><c> a</c><00:07:21.599><c> metadata</c>

00:07:22.129 --> 00:07:22.139 align:start position:0%
you'll notice here I've added a metadata
 

00:07:22.139 --> 00:07:24.589 align:start position:0%
you'll notice here I've added a metadata
mode<00:07:22.340><c> and</c><00:07:23.340><c> this</c><00:07:23.460><c> is</c><00:07:23.520><c> what</c><00:07:23.699><c> llama</c><00:07:23.880><c> index</c><00:07:24.240><c> uses</c>

00:07:24.589 --> 00:07:24.599 align:start position:0%
mode and this is what llama index uses
 

00:07:24.599 --> 00:07:27.890 align:start position:0%
mode and this is what llama index uses
under<00:07:24.780><c> the</c><00:07:24.960><c> hood</c><00:07:25.139><c> to</c><00:07:25.680><c> get</c><00:07:26.280><c> the</c><00:07:26.580><c> text</c><00:07:26.759><c> for</c>

00:07:27.890 --> 00:07:27.900 align:start position:0%
under the hood to get the text for
 

00:07:27.900 --> 00:07:29.749 align:start position:0%
under the hood to get the text for
different<00:07:28.380><c> parts</c><00:07:28.680><c> of</c><00:07:28.860><c> LOM</c><00:07:29.160><c> index</c><00:07:29.460><c> so</c><00:07:29.639><c> there's</c>

00:07:29.749 --> 00:07:29.759 align:start position:0%
different parts of LOM index so there's
 

00:07:29.759 --> 00:07:32.029 align:start position:0%
different parts of LOM index so there's
a<00:07:29.880><c> metadata</c><00:07:30.300><c> mode</c><00:07:30.479><c> for</c><00:07:30.780><c> embeddings</c><00:07:31.680><c> and</c><00:07:31.919><c> for</c>

00:07:32.029 --> 00:07:32.039 align:start position:0%
a metadata mode for embeddings and for
 

00:07:32.039 --> 00:07:34.070 align:start position:0%
a metadata mode for embeddings and for
llms<00:07:32.639><c> and</c><00:07:32.880><c> then</c><00:07:32.940><c> also</c><00:07:33.300><c> all</c>

00:07:34.070 --> 00:07:34.080 align:start position:0%
llms and then also all
 

00:07:34.080 --> 00:07:36.950 align:start position:0%
llms and then also all
and<00:07:34.620><c> so</c><00:07:34.860><c> you</c><00:07:35.220><c> can</c><00:07:35.400><c> see</c><00:07:35.520><c> here</c><00:07:35.759><c> that</c><00:07:36.060><c> it</c><00:07:36.599><c> fetches</c>

00:07:36.950 --> 00:07:36.960 align:start position:0%
and so you can see here that it fetches
 

00:07:36.960 --> 00:07:38.809 align:start position:0%
and so you can see here that it fetches
all<00:07:37.139><c> the</c><00:07:37.259><c> metadata</c><00:07:37.740><c> in</c><00:07:37.979><c> a</c><00:07:38.160><c> single</c><00:07:38.400><c> comma</c>

00:07:38.809 --> 00:07:38.819 align:start position:0%
all the metadata in a single comma
 

00:07:38.819 --> 00:07:40.010 align:start position:0%
all the metadata in a single comma
separated<00:07:39.120><c> line</c>

00:07:40.010 --> 00:07:40.020 align:start position:0%
separated line
 

00:07:40.020 --> 00:07:41.809 align:start position:0%
separated line
and<00:07:40.500><c> then</c><00:07:40.620><c> we</c><00:07:40.800><c> have</c><00:07:40.860><c> our</c><00:07:40.979><c> content</c><00:07:41.340><c> following</c>

00:07:41.809 --> 00:07:41.819 align:start position:0%
and then we have our content following
 

00:07:41.819 --> 00:07:44.570 align:start position:0%
and then we have our content following
our<00:07:42.000><c> template</c><00:07:42.419><c> that</c><00:07:42.599><c> we</c><00:07:42.720><c> specified</c><00:07:43.080><c> up</c><00:07:43.500><c> here</c>

00:07:44.570 --> 00:07:44.580 align:start position:0%
our template that we specified up here
 

00:07:44.580 --> 00:07:45.890 align:start position:0%
our template that we specified up here
now<00:07:44.880><c> we</c><00:07:45.000><c> can</c><00:07:45.120><c> get</c><00:07:45.240><c> a</c><00:07:45.360><c> bit</c><00:07:45.419><c> more</c><00:07:45.479><c> advanced</c><00:07:45.780><c> with</c>

00:07:45.890 --> 00:07:45.900 align:start position:0%
now we can get a bit more advanced with
 

00:07:45.900 --> 00:07:50.029 align:start position:0%
now we can get a bit more advanced with
this<00:07:46.080><c> customization</c><00:07:46.680><c> we</c><00:07:47.400><c> could</c><00:07:47.520><c> say</c><00:07:47.759><c> that</c><00:07:48.120><c> for</c>

00:07:50.029 --> 00:07:50.039 align:start position:0%
this customization we could say that for
 

00:07:50.039 --> 00:07:52.309 align:start position:0%
this customization we could say that for
the<00:07:50.460><c> llm</c><00:07:50.819><c> metadata</c><00:07:51.419><c> so</c><00:07:51.599><c> the</c><00:07:51.720><c> metadata</c><00:07:52.199><c> that</c>

00:07:52.309 --> 00:07:52.319 align:start position:0%
the llm metadata so the metadata that
 

00:07:52.319 --> 00:07:54.770 align:start position:0%
the llm metadata so the metadata that
the<00:07:52.500><c> llm</c><00:07:52.860><c> reads</c><00:07:53.280><c> we</c><00:07:54.240><c> don't</c><00:07:54.300><c> want</c><00:07:54.479><c> it</c><00:07:54.599><c> to</c><00:07:54.660><c> see</c>

00:07:54.770 --> 00:07:54.780 align:start position:0%
the llm reads we don't want it to see
 

00:07:54.780 --> 00:07:57.050 align:start position:0%
the llm reads we don't want it to see
the<00:07:54.900><c> file</c><00:07:55.139><c> name</c><00:07:55.380><c> so</c><00:07:55.979><c> now</c><00:07:56.220><c> when</c><00:07:56.520><c> we</c><00:07:56.699><c> call</c><00:07:56.819><c> get</c>

00:07:57.050 --> 00:07:57.060 align:start position:0%
the file name so now when we call get
 

00:07:57.060 --> 00:08:00.529 align:start position:0%
the file name so now when we call get
content<00:07:57.599><c> with</c><00:07:58.199><c> the</c><00:07:58.380><c> metadata</c><00:07:58.860><c> mode</c><00:07:58.979><c> LOM</c><00:07:59.819><c> after</c>

00:08:00.529 --> 00:08:00.539 align:start position:0%
content with the metadata mode LOM after
 

00:08:00.539 --> 00:08:03.110 align:start position:0%
content with the metadata mode LOM after
applying<00:08:01.259><c> this</c><00:08:01.440><c> exclusion</c><00:08:01.979><c> we</c><00:08:02.699><c> can</c><00:08:02.819><c> see</c><00:08:02.940><c> that</c>

00:08:03.110 --> 00:08:03.120 align:start position:0%
applying this exclusion we can see that
 

00:08:03.120 --> 00:08:05.450 align:start position:0%
applying this exclusion we can see that
the<00:08:03.300><c> file</c><00:08:03.419><c> name</c><00:08:03.660><c> is</c><00:08:04.139><c> no</c><00:08:04.440><c> longer</c><00:08:04.740><c> present</c><00:08:05.220><c> in</c>

00:08:05.450 --> 00:08:05.460 align:start position:0%
the file name is no longer present in
 

00:08:05.460 --> 00:08:07.610 align:start position:0%
the file name is no longer present in
the<00:08:05.580><c> text</c><00:08:05.759><c> which</c><00:08:06.120><c> is</c><00:08:06.180><c> what</c><00:08:06.360><c> we</c><00:08:06.479><c> wanted</c>

00:08:07.610 --> 00:08:07.620 align:start position:0%
the text which is what we wanted
 

00:08:07.620 --> 00:08:09.409 align:start position:0%
the text which is what we wanted
and<00:08:08.039><c> we</c><00:08:08.160><c> can</c><00:08:08.280><c> apply</c><00:08:08.580><c> the</c><00:08:08.759><c> same</c><00:08:08.940><c> thing</c><00:08:09.120><c> to</c><00:08:09.300><c> the</c>

00:08:09.409 --> 00:08:09.419 align:start position:0%
and we can apply the same thing to the
 

00:08:09.419 --> 00:08:12.350 align:start position:0%
and we can apply the same thing to the
embeddings<00:08:09.840><c> and</c><00:08:10.500><c> we</c><00:08:10.680><c> see</c><00:08:10.860><c> that</c><00:08:11.639><c> also</c><00:08:12.240><c> the</c>

00:08:12.350 --> 00:08:12.360 align:start position:0%
embeddings and we see that also the
 

00:08:12.360 --> 00:08:15.290 align:start position:0%
embeddings and we see that also the
embeddings<00:08:12.780><c> no</c><00:08:13.139><c> longer</c><00:08:13.380><c> see</c><00:08:13.560><c> the</c><00:08:13.740><c> file</c><00:08:13.919><c> name</c>

00:08:15.290 --> 00:08:15.300 align:start position:0%
embeddings no longer see the file name
 

00:08:15.300 --> 00:08:17.089 align:start position:0%
embeddings no longer see the file name
and<00:08:15.660><c> so</c><00:08:15.840><c> that's</c><00:08:16.020><c> basically</c><00:08:16.319><c> it</c>

00:08:17.089 --> 00:08:17.099 align:start position:0%
and so that's basically it
 

00:08:17.099 --> 00:08:19.129 align:start position:0%
and so that's basically it
in<00:08:17.639><c> this</c><00:08:17.699><c> video</c><00:08:17.819><c> we've</c><00:08:18.120><c> covered</c><00:08:18.479><c> how</c><00:08:19.020><c> to</c>

00:08:19.129 --> 00:08:19.139 align:start position:0%
in this video we've covered how to
 

00:08:19.139 --> 00:08:21.230 align:start position:0%
in this video we've covered how to
create<00:08:19.259><c> documents</c><00:08:19.979><c> the</c><00:08:20.639><c> different</c><00:08:20.819><c> ways</c><00:08:21.120><c> you</c>

00:08:21.230 --> 00:08:21.240 align:start position:0%
create documents the different ways you
 

00:08:21.240 --> 00:08:24.050 align:start position:0%
create documents the different ways you
can<00:08:21.360><c> customize</c><00:08:21.780><c> them</c><00:08:22.080><c> and</c><00:08:23.400><c> I'll</c><00:08:23.759><c> provide</c>

00:08:24.050 --> 00:08:24.060 align:start position:0%
can customize them and I'll provide
 

00:08:24.060 --> 00:08:25.730 align:start position:0%
can customize them and I'll provide
links<00:08:24.479><c> down</c><00:08:24.780><c> below</c><00:08:24.960><c> so</c><00:08:25.139><c> you</c><00:08:25.259><c> can</c><00:08:25.379><c> read</c><00:08:25.500><c> my</c>

00:08:25.730 --> 00:08:25.740 align:start position:0%
links down below so you can read my
 

00:08:25.740 --> 00:08:28.790 align:start position:0%
links down below so you can read my
markdown<00:08:26.039><c> reader</c><00:08:26.520><c> it's</c><00:08:27.120><c> not</c><00:08:27.419><c> perfect</c><00:08:27.720><c> but</c><00:08:28.680><c> it</c>

00:08:28.790 --> 00:08:28.800 align:start position:0%
markdown reader it's not perfect but it
 

00:08:28.800 --> 00:08:30.770 align:start position:0%
markdown reader it's not perfect but it
works<00:08:29.099><c> and</c><00:08:29.699><c> it's</c><00:08:29.879><c> kind</c><00:08:30.060><c> of</c><00:08:30.120><c> an</c><00:08:30.180><c> example</c><00:08:30.479><c> of</c><00:08:30.660><c> how</c>

00:08:30.770 --> 00:08:30.780 align:start position:0%
works and it's kind of an example of how
 

00:08:30.780 --> 00:08:32.389 align:start position:0%
works and it's kind of an example of how
you<00:08:30.900><c> can</c><00:08:31.020><c> build</c><00:08:31.199><c> your</c><00:08:31.440><c> own</c><00:08:31.560><c> loaders</c><00:08:31.979><c> they're</c>

00:08:32.389 --> 00:08:32.399 align:start position:0%
you can build your own loaders they're
 

00:08:32.399 --> 00:08:34.250 align:start position:0%
you can build your own loaders they're
not<00:08:32.580><c> scary</c><00:08:32.880><c> it</c><00:08:33.599><c> just</c><00:08:33.779><c> takes</c><00:08:33.899><c> a</c><00:08:34.020><c> little</c><00:08:34.080><c> bit</c><00:08:34.140><c> of</c>

00:08:34.250 --> 00:08:34.260 align:start position:0%
not scary it just takes a little bit of
 

00:08:34.260 --> 00:08:35.690 align:start position:0%
not scary it just takes a little bit of
time<00:08:34.380><c> to</c><00:08:34.560><c> write</c><00:08:34.680><c> and</c><00:08:35.099><c> then</c><00:08:35.339><c> you</c><00:08:35.459><c> have</c><00:08:35.580><c> full</c>

00:08:35.690 --> 00:08:35.700 align:start position:0%
time to write and then you have full
 

00:08:35.700 --> 00:08:37.790 align:start position:0%
time to write and then you have full
control<00:08:35.940><c> over</c><00:08:36.240><c> how</c><00:08:36.539><c> your</c><00:08:36.659><c> data</c><00:08:36.899><c> is</c><00:08:36.959><c> loaded</c><00:08:37.320><c> and</c>

00:08:37.790 --> 00:08:37.800 align:start position:0%
control over how your data is loaded and
 

00:08:37.800 --> 00:08:39.889 align:start position:0%
control over how your data is loaded and
what<00:08:37.979><c> your</c><00:08:38.099><c> documents</c><00:08:38.520><c> look</c><00:08:38.640><c> like</c><00:08:39.240><c> hope</c><00:08:39.719><c> this</c>

00:08:39.889 --> 00:08:39.899 align:start position:0%
what your documents look like hope this
 

00:08:39.899 --> 00:08:40.909 align:start position:0%
what your documents look like hope this
video<00:08:40.020><c> was</c><00:08:40.200><c> helpful</c><00:08:40.440><c> and</c><00:08:40.620><c> see</c><00:08:40.740><c> you</c><00:08:40.800><c> in</c><00:08:40.860><c> the</c>

00:08:40.909 --> 00:08:40.919 align:start position:0%
video was helpful and see you in the
 

00:08:40.919 --> 00:08:42.919 align:start position:0%
video was helpful and see you in the
next

