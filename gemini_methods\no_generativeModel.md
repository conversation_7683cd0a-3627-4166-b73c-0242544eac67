In the new Google Gen AI SDK (often imported as `genai`), the direct `GenerativeModel` class definition from the older SDK (like `google-generativeai` or the Vertex AI specific SDK) is replaced by a client-based approach[2][3].

Instead of creating an instance of `GenerativeModel` directly, you now typically:
1.  **Initialize a `Client` object**:
    *   For the Gemini API:
        ```python
        from google import genai
        client = genai.Client() # API key can be set via environment variable GOOGLE_API_KEY
        # or explicitly: client = genai.Client(api_key="YOUR_API_KEY")
        ```

    *   For Vertex AI:
        ```python
        from google import genai
        # Ensure environment variables like GOOGLE_CLOUD_PROJECT and GOOGLE_GENAI_USE_VERTEXAI=True are set
        client = genai.Client()
        # Or for express mode:
        # client = genai.Client(vertexai=True, api_key="YOUR_API_KEY")
        ```


2.  **Use methods on the `client.models` object** to interact with the models, specifying the model name as a string parameter. For example, to generate content:
    ```python
    response = client.models.generate_content(
        model="gemini-2.0-flash-001", # Specify the model name here
        contents="How does AI work?"
    )
    print(response.text)
    ```


This contrasts with the older SDKs where you would first instantiate a `GenerativeModel` object:
*   Old Vertex AI SDK example:
    ```python
    from vertexai.generative_models import GenerativeModel
    model = GenerativeModel("gemini-pro")
    response = model.generate_content("Why is sky blue?")
    ```

*   Old `google-generativeai` SDK example:
    ```python
    import google.generativeai as genai
    genai.configure(api_key="YOUR_API_KEY")
    model = genai.GenerativeModel('gemini-1.5-flash')
    response = model.generate_content("Tell me a story")
    ```


Therefore, the new `genai` SDK shifts from a model-centric object (`GenerativeModel`) to a client-centric interaction (`client.models.method()`) where the specific model is passed as an argument to the method[2][3]. The `client.models` object serves as the interface to the available generative models.

Citations:
[1] https://cloud.google.com/vertex-ai/generative-ai/docs/reference/python/latest
[2] https://cloud.google.com/vertex-ai/generative-ai/docs/sdks/overview
[3] https://ai.google.dev/gemini-api/docs/migrate
[4] https://ai.google.dev/api/generate-content
[5] https://cloud.google.com/vertex-ai/generative-ai/docs/reference/java/latest/com.google.cloud.vertexai.generativeai.GenerativeModel
[6] https://pkg.go.dev/github.com/google/generative-ai-go/genai
[7] https://github.com/google-gemini/generative-ai-python
[8] https://ai.google.dev/api/generate-content
[9] https://en.wikipedia.org/wiki/Generative_artificial_intelligence
[10] https://firebase.google.com/docs/reference/js/vertexai.generativemodel
[11] https://googleapis.github.io/python-genai/
[12] https://github.com/google-gemini/deprecated-generative-ai-python/blob/main/docs/api/google/generativeai/GenerativeModel.md
[13] https://pypi.org/project/google-generativeai/
[14] https://www.googlecloudcommunity.com/gc/AI-ML/Error-using-Vertex-GenerativeModel-example/m-p/739403
[15] https://github.com/googleapis/python-genai
[16] https://googleapis.github.io/python-genai/genai.html
[17] https://ai.google.dev/gemini-api/docs/models/generative-models
[18] https://pypi.org/project/google-genai/
[19] https://stackoverflow.com/questions/78772795/what-is-the-python-code-to-check-all-the-generativeai-models-supported-by-google
[20] https://www.youtube.com/watch?v=toOACqRMn9Q
[21] https://python.langchain.com/docs/integrations/chat/google_generative_ai/
[22] https://www.kaggle.com/code/manavpatel571/gemini-pro-model-guide
[23] https://sdk.vercel.ai/providers/ai-sdk-providers/google-generative-ai
[24] https://github.com/GoogleCloudPlatform/generative-ai/blob/main/gemini/getting-started/intro_genai_sdk.ipynb
[25] https://cloud.google.com/vertex-ai/generative-ai/docs/migrate-to-v2
[26] https://ibm.github.io/ibm-generative-ai/main/rst_source/examples.model.model.html
[27] https://ibm.github.io/ibm-generative-ai/v3.0.0/v3_migration_guide.html
[28] https://developers.sap.com/tutorials/ai-core-genai-hana-vector..html
[29] https://cloud.google.com/vertex-ai/generative-ai/docs/sdks/overview
[30] https://github.com/google-gemini/generative-ai-python/blob/main/docs/api/google/generativeai.md
[31] https://cloud.google.com/vertex-ai/generative-ai/docs/reference/nodejs/latest/vertexai/generativemodel
[32] https://developer.android.com/ai/vertex-ai-firebase
[33] https://github.com/google-gemini/deprecated-generative-ai-python
[34] https://www.youtube.com/watch?v=vH2iMV2Y3dI
[35] https://cloud.google.com/vertex-ai/generative-ai/docs/samples
[36] https://ai.google.dev/gemini-api/docs/libraries
[37] https://ibm.github.io/ibm-generative-ai/main/v2_migration_guide.html
[38] https://github.com/google-gemini/deprecated-generative-ai-js

---
Answer from Perplexity: pplx.ai/share