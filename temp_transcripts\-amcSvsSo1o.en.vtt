WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:02.389 align:start position:0%
 
hey<00:00:00.280><c> and</c><00:00:00.399><c> welcome</c><00:00:00.640><c> back</c><00:00:00.799><c> today</c><00:00:01.160><c> is</c><00:00:01.360><c> day</c><00:00:01.599><c> 20</c><00:00:02.200><c> and</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
hey and welcome back today is day 20 and
 

00:00:02.399 --> 00:00:04.230 align:start position:0%
hey and welcome back today is day 20 and
today<00:00:02.800><c> we're</c><00:00:02.919><c> going</c><00:00:03.080><c> to</c><00:00:03.159><c> be</c><00:00:03.320><c> creating</c><00:00:03.679><c> our</c><00:00:03.840><c> own</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
today we're going to be creating our own
 

00:00:04.240 --> 00:00:05.630 align:start position:0%
today we're going to be creating our own
quiz<00:00:04.880><c> of</c><00:00:05.000><c> course</c><00:00:05.160><c> we're</c><00:00:05.279><c> not</c><00:00:05.400><c> going</c><00:00:05.480><c> to</c><00:00:05.520><c> be</c>

00:00:05.630 --> 00:00:05.640 align:start position:0%
quiz of course we're not going to be
 

00:00:05.640 --> 00:00:07.829 align:start position:0%
quiz of course we're not going to be
asking<00:00:05.879><c> chat</c><00:00:06.160><c> GPT</c><00:00:06.600><c> to</c><00:00:06.720><c> do</c><00:00:06.879><c> this</c><00:00:07.040><c> for</c><00:00:07.279><c> us</c><00:00:07.720><c> we</c>

00:00:07.829 --> 00:00:07.839 align:start position:0%
asking chat GPT to do this for us we
 

00:00:07.839 --> 00:00:09.270 align:start position:0%
asking chat GPT to do this for us we
will<00:00:08.000><c> be</c><00:00:08.120><c> generating</c><00:00:08.480><c> the</c><00:00:08.599><c> quizzes</c><00:00:08.960><c> and</c><00:00:09.080><c> the</c>

00:00:09.270 --> 00:00:09.280 align:start position:0%
will be generating the quizzes and the
 

00:00:09.280 --> 00:00:11.830 align:start position:0%
will be generating the quizzes and the
answers<00:00:09.840><c> from</c><00:00:10.160><c> a</c><00:00:10.400><c> PDF</c><00:00:11.040><c> once</c><00:00:11.240><c> we</c><00:00:11.480><c> run</c><00:00:11.719><c> the</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
answers from a PDF once we run the
 

00:00:11.840 --> 00:00:14.070 align:start position:0%
answers from a PDF once we run the
streamlit<00:00:12.480><c> application</c><00:00:13.120><c> on</c><00:00:13.240><c> the</c><00:00:13.440><c> sidebar</c><00:00:13.960><c> you</c>

00:00:14.070 --> 00:00:14.080 align:start position:0%
streamlit application on the sidebar you
 

00:00:14.080 --> 00:00:16.150 align:start position:0%
streamlit application on the sidebar you
can<00:00:14.240><c> browse</c><00:00:14.559><c> for</c><00:00:14.799><c> files</c><00:00:15.360><c> upload</c><00:00:15.719><c> them</c><00:00:16.000><c> once</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
can browse for files upload them once
 

00:00:16.160 --> 00:00:17.670 align:start position:0%
can browse for files upload them once
it's<00:00:16.320><c> uploaded</c><00:00:16.760><c> successfully</c><00:00:17.359><c> you</c><00:00:17.520><c> just</c>

00:00:17.670 --> 00:00:17.680 align:start position:0%
it's uploaded successfully you just
 

00:00:17.680 --> 00:00:19.310 align:start position:0%
it's uploaded successfully you just
simply<00:00:17.960><c> ask</c><00:00:18.119><c> it</c><00:00:18.240><c> to</c><00:00:18.400><c> create</c><00:00:18.600><c> a</c><00:00:18.760><c> question</c><00:00:19.160><c> then</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
simply ask it to create a question then
 

00:00:19.320 --> 00:00:21.509 align:start position:0%
simply ask it to create a question then
using<00:00:19.600><c> that</c><00:00:19.760><c> PDF</c><00:00:20.199><c> with</c><00:00:20.279><c> the</c><00:00:20.439><c> vector</c><00:00:20.840><c> database</c>

00:00:21.509 --> 00:00:21.519 align:start position:0%
using that PDF with the vector database
 

00:00:21.519 --> 00:00:23.710 align:start position:0%
using that PDF with the vector database
and<00:00:21.680><c> autogen</c><00:00:22.199><c> agents</c><00:00:22.720><c> and</c><00:00:22.960><c> function</c><00:00:23.279><c> calling</c>

00:00:23.710 --> 00:00:23.720 align:start position:0%
and autogen agents and function calling
 

00:00:23.720 --> 00:00:25.750 align:start position:0%
and autogen agents and function calling
it<00:00:23.880><c> produces</c><00:00:24.400><c> our</c><00:00:24.760><c> quiz</c><00:00:25.279><c> and</c><00:00:25.439><c> during</c><00:00:25.599><c> one</c><00:00:25.720><c> of</c>

00:00:25.750 --> 00:00:25.760 align:start position:0%
it produces our quiz and during one of
 

00:00:25.760 --> 00:00:27.669 align:start position:0%
it produces our quiz and during one of
the<00:00:25.840><c> function</c><00:00:26.119><c> calls</c><00:00:26.720><c> we</c><00:00:27.000><c> extract</c><00:00:27.519><c> the</c>

00:00:27.669 --> 00:00:27.679 align:start position:0%
the function calls we extract the
 

00:00:27.679 --> 00:00:30.349 align:start position:0%
the function calls we extract the
information<00:00:28.359><c> from</c><00:00:28.560><c> the</c><00:00:28.720><c> PDF</c><00:00:29.400><c> make</c><00:00:29.599><c> it</c><00:00:29.679><c> a</c><00:00:30.000><c> B</c><00:00:30.160><c> on</c>

00:00:30.349 --> 00:00:30.359 align:start position:0%
information from the PDF make it a B on
 

00:00:30.359 --> 00:00:31.870 align:start position:0%
information from the PDF make it a B on
and<00:00:30.439><c> then</c><00:00:30.599><c> save</c><00:00:30.759><c> it</c><00:00:30.880><c> to</c><00:00:31.039><c> a</c><00:00:31.240><c> file</c><00:00:31.519><c> so</c><00:00:31.640><c> you</c><00:00:31.720><c> can</c>

00:00:31.870 --> 00:00:31.880 align:start position:0%
and then save it to a file so you can
 

00:00:31.880 --> 00:00:33.510 align:start position:0%
and then save it to a file so you can
see<00:00:32.079><c> where</c><00:00:32.200><c> we</c><00:00:32.320><c> get</c><00:00:32.439><c> the</c><00:00:32.559><c> data</c><00:00:32.840><c> from</c><00:00:33.160><c> all</c><00:00:33.280><c> right</c>

00:00:33.510 --> 00:00:33.520 align:start position:0%
see where we get the data from all right
 

00:00:33.520 --> 00:00:34.709 align:start position:0%
see where we get the data from all right
well<00:00:33.680><c> let's</c><00:00:33.840><c> get</c><00:00:33.960><c> started</c><00:00:34.200><c> with</c><00:00:34.280><c> the</c><00:00:34.360><c> coding</c>

00:00:34.709 --> 00:00:34.719 align:start position:0%
well let's get started with the coding
 

00:00:34.719 --> 00:00:36.389 align:start position:0%
well let's get started with the coding
we<00:00:34.800><c> have</c><00:00:34.920><c> a</c><00:00:35.000><c> few</c><00:00:35.160><c> things</c><00:00:35.320><c> to</c><00:00:35.480><c> go</c><00:00:35.640><c> over</c><00:00:36.120><c> and</c><00:00:36.280><c> I</c>

00:00:36.389 --> 00:00:36.399 align:start position:0%
we have a few things to go over and I
 

00:00:36.399 --> 00:00:38.270 align:start position:0%
we have a few things to go over and I
have<00:00:36.600><c> a</c><00:00:36.760><c> few</c><00:00:36.960><c> files</c><00:00:37.239><c> here</c><00:00:37.399><c> have</c><00:00:37.520><c> an</c><00:00:37.680><c> agents</c><00:00:38.160><c> A</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
have a few files here have an agents A
 

00:00:38.280 --> 00:00:40.549 align:start position:0%
have a few files here have an agents A
config<00:00:38.719><c> and</c><00:00:38.840><c> a</c><00:00:39.000><c> main</c><00:00:39.399><c> python</c><00:00:39.760><c> file</c><00:00:40.280><c> let's</c><00:00:40.440><c> go</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
config and a main python file let's go
 

00:00:40.559 --> 00:00:41.869 align:start position:0%
config and a main python file let's go
over<00:00:40.719><c> the</c><00:00:40.879><c> agents</c><00:00:41.280><c> first</c><00:00:41.600><c> we're</c><00:00:41.719><c> going</c><00:00:41.840><c> to</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
over the agents first we're going to
 

00:00:41.879 --> 00:00:43.150 align:start position:0%
over the agents first we're going to
have<00:00:41.960><c> a</c><00:00:42.039><c> total</c><00:00:42.280><c> of</c><00:00:42.360><c> four</c><00:00:42.480><c> different</c><00:00:42.680><c> agents</c>

00:00:43.150 --> 00:00:43.160 align:start position:0%
have a total of four different agents
 

00:00:43.160 --> 00:00:44.350 align:start position:0%
have a total of four different agents
let's<00:00:43.320><c> start</c><00:00:43.440><c> out</c><00:00:43.559><c> with</c><00:00:43.640><c> the</c><00:00:43.760><c> assistant</c><00:00:44.079><c> agent</c>

00:00:44.350 --> 00:00:44.360 align:start position:0%
let's start out with the assistant agent
 

00:00:44.360 --> 00:00:46.630 align:start position:0%
let's start out with the assistant agent
and<00:00:44.559><c> this</c><00:00:45.000><c> agent</c><00:00:45.320><c> gets</c><00:00:45.680><c> the</c><00:00:45.960><c> question</c><00:00:46.280><c> multip</c>

00:00:46.630 --> 00:00:46.640 align:start position:0%
and this agent gets the question multip
 

00:00:46.640 --> 00:00:48.470 align:start position:0%
and this agent gets the question multip
choice<00:00:46.879><c> answers</c><00:00:47.600><c> the</c><00:00:47.840><c> answer</c><00:00:48.160><c> to</c><00:00:48.280><c> the</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
choice answers the answer to the
 

00:00:48.480 --> 00:00:50.430 align:start position:0%
choice answers the answer to the
question<00:00:48.879><c> and</c><00:00:49.039><c> the</c><00:00:49.239><c> reason</c><00:00:49.800><c> and</c><00:00:49.960><c> puts</c><00:00:50.160><c> them</c><00:00:50.280><c> in</c>

00:00:50.430 --> 00:00:50.440 align:start position:0%
question and the reason and puts them in
 

00:00:50.440 --> 00:00:52.069 align:start position:0%
question and the reason and puts them in
Json<00:00:50.840><c> format</c><00:00:51.280><c> and</c><00:00:51.399><c> then</c><00:00:51.480><c> I</c><00:00:51.559><c> have</c><00:00:51.640><c> a</c><00:00:51.760><c> Json</c>

00:00:52.069 --> 00:00:52.079 align:start position:0%
Json format and then I have a Json
 

00:00:52.079 --> 00:00:53.069 align:start position:0%
Json format and then I have a Json
assistant<00:00:52.399><c> that</c><00:00:52.480><c> is</c><00:00:52.559><c> basically</c><00:00:52.840><c> going</c><00:00:52.960><c> to</c>

00:00:53.069 --> 00:00:53.079 align:start position:0%
assistant that is basically going to
 

00:00:53.079 --> 00:00:54.349 align:start position:0%
assistant that is basically going to
take<00:00:53.280><c> those</c><00:00:53.440><c> things</c><00:00:53.680><c> and</c><00:00:53.800><c> then</c><00:00:53.960><c> save</c><00:00:54.120><c> them</c><00:00:54.239><c> to</c>

00:00:54.349 --> 00:00:54.359 align:start position:0%
take those things and then save them to
 

00:00:54.359 --> 00:00:55.830 align:start position:0%
take those things and then save them to
a<00:00:54.520><c> file</c><00:00:54.840><c> and</c><00:00:54.920><c> that's</c><00:00:55.079><c> pretty</c><00:00:55.280><c> much</c><00:00:55.440><c> it</c><00:00:55.719><c> and</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
a file and that's pretty much it and
 

00:00:55.840 --> 00:00:58.069 align:start position:0%
a file and that's pretty much it and
then<00:00:56.000><c> because</c><00:00:56.280><c> I</c><00:00:56.480><c> seem</c><00:00:56.719><c> to</c><00:00:56.920><c> have</c><00:00:57.199><c> some</c><00:00:57.480><c> issues</c>

00:00:58.069 --> 00:00:58.079 align:start position:0%
then because I seem to have some issues
 

00:00:58.079 --> 00:01:00.630 align:start position:0%
then because I seem to have some issues
with<00:00:58.239><c> the</c><00:00:58.399><c> Json</c><00:00:58.920><c> formatting</c><00:00:59.680><c> I</c><00:01:00.079><c> created</c><00:01:00.440><c> a</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
with the Json formatting I created a
 

00:01:00.640 --> 00:01:02.950 align:start position:0%
with the Json formatting I created a
specific<00:01:01.079><c> agent</c><00:01:01.559><c> to</c><00:01:01.920><c> convert</c><00:01:02.600><c> whatever</c>

00:01:02.950 --> 00:01:02.960 align:start position:0%
specific agent to convert whatever
 

00:01:02.960 --> 00:01:05.229 align:start position:0%
specific agent to convert whatever
format<00:01:03.320><c> we</c><00:01:03.519><c> get</c><00:01:03.920><c> from</c><00:01:04.400><c> the</c><00:01:04.559><c> first</c><00:01:04.839><c> assistant</c>

00:01:05.229 --> 00:01:05.239 align:start position:0%
format we get from the first assistant
 

00:01:05.239 --> 00:01:07.550 align:start position:0%
format we get from the first assistant
agent<00:01:05.720><c> to</c><00:01:06.040><c> put</c><00:01:06.200><c> that</c><00:01:06.400><c> in</c><00:01:06.600><c> a</c><00:01:06.799><c> correct</c><00:01:07.119><c> Json</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
agent to put that in a correct Json
 

00:01:07.560 --> 00:01:09.070 align:start position:0%
agent to put that in a correct Json
format<00:01:08.119><c> then</c><00:01:08.240><c> of</c><00:01:08.360><c> course</c><00:01:08.520><c> I</c><00:01:08.600><c> have</c><00:01:08.720><c> a</c><00:01:08.799><c> user</c>

00:01:09.070 --> 00:01:09.080 align:start position:0%
format then of course I have a user
 

00:01:09.080 --> 00:01:10.390 align:start position:0%
format then of course I have a user
proxy<00:01:09.400><c> agent</c><00:01:09.720><c> with</c><00:01:09.840><c> some</c><00:01:10.040><c> standard</c>

00:01:10.390 --> 00:01:10.400 align:start position:0%
proxy agent with some standard
 

00:01:10.400 --> 00:01:12.310 align:start position:0%
proxy agent with some standard
properties<00:01:10.960><c> and</c><00:01:11.080><c> then</c><00:01:11.320><c> I</c><00:01:11.520><c> created</c><00:01:11.880><c> a</c><00:01:12.000><c> function</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
properties and then I created a function
 

00:01:12.320 --> 00:01:14.749 align:start position:0%
properties and then I created a function
called<00:01:12.600><c> initiate</c><00:01:13.119><c> agent</c><00:01:13.479><c> chats</c><00:01:14.240><c> because</c><00:01:14.680><c> I'm</c>

00:01:14.749 --> 00:01:14.759 align:start position:0%
called initiate agent chats because I'm
 

00:01:14.759 --> 00:01:16.789 align:start position:0%
called initiate agent chats because I'm
going<00:01:14.880><c> to</c><00:01:15.360><c> call</c><00:01:15.720><c> this</c><00:01:15.960><c> function</c><00:01:16.360><c> from</c><00:01:16.600><c> the</c>

00:01:16.789 --> 00:01:16.799 align:start position:0%
going to call this function from the
 

00:01:16.799 --> 00:01:19.109 align:start position:0%
going to call this function from the
main<00:01:17.159><c> python</c><00:01:17.560><c> file</c><00:01:17.880><c> so</c><00:01:18.360><c> in</c><00:01:18.479><c> order</c><00:01:18.720><c> it</c><00:01:18.920><c> just</c>

00:01:19.109 --> 00:01:19.119 align:start position:0%
main python file so in order it just
 

00:01:19.119 --> 00:01:21.670 align:start position:0%
main python file so in order it just
easier<00:01:19.759><c> to</c><00:01:20.000><c> wrap</c><00:01:20.640><c> uh</c><00:01:20.840><c> the</c><00:01:20.960><c> user</c><00:01:21.280><c> proxy</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
easier to wrap uh the user proxy
 

00:01:21.680 --> 00:01:24.030 align:start position:0%
easier to wrap uh the user proxy
initiate<00:01:22.159><c> chat</c><00:01:22.880><c> inside</c><00:01:23.360><c> of</c><00:01:23.479><c> a</c><00:01:23.600><c> function</c><00:01:23.880><c> that</c>

00:01:24.030 --> 00:01:24.040 align:start position:0%
initiate chat inside of a function that
 

00:01:24.040 --> 00:01:25.429 align:start position:0%
initiate chat inside of a function that
I<00:01:24.119><c> can</c><00:01:24.200><c> just</c><00:01:24.400><c> call</c><00:01:24.840><c> and</c><00:01:25.040><c> if</c><00:01:25.119><c> you</c><00:01:25.240><c> haven't</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
I can just call and if you haven't
 

00:01:25.439 --> 00:01:27.590 align:start position:0%
I can just call and if you haven't
worked<00:01:25.720><c> with</c><00:01:25.920><c> initiate</c><00:01:26.320><c> chats</c><00:01:26.720><c> before</c><00:01:27.479><c> this</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
worked with initiate chats before this
 

00:01:27.600 --> 00:01:30.510 align:start position:0%
worked with initiate chats before this
is<00:01:27.920><c> essentially</c><00:01:28.560><c> auten's</c><00:01:29.360><c> way</c><00:01:29.560><c> of</c><00:01:30.119><c> having</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
is essentially auten's way of having
 

00:01:30.520 --> 00:01:32.749 align:start position:0%
is essentially auten's way of having
sequential<00:01:31.079><c> chats</c><00:01:31.600><c> so</c><00:01:31.759><c> when</c><00:01:31.880><c> the</c><00:01:32.000><c> user</c><00:01:32.479><c> proxy</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
sequential chats so when the user proxy
 

00:01:32.759 --> 00:01:34.069 align:start position:0%
sequential chats so when the user proxy
agent<00:01:33.040><c> starts</c><00:01:33.360><c> the</c><00:01:33.479><c> chat</c><00:01:33.720><c> we're</c><00:01:33.840><c> going</c><00:01:33.960><c> to</c>

00:01:34.069 --> 00:01:34.079 align:start position:0%
agent starts the chat we're going to
 

00:01:34.079 --> 00:01:35.670 align:start position:0%
agent starts the chat we're going to
first<00:01:34.280><c> talk</c><00:01:34.439><c> to</c><00:01:34.600><c> the</c><00:01:34.759><c> assistant</c><00:01:35.479><c> this</c><00:01:35.560><c> is</c>

00:01:35.670 --> 00:01:35.680 align:start position:0%
first talk to the assistant this is
 

00:01:35.680 --> 00:01:37.950 align:start position:0%
first talk to the assistant this is
basically<00:01:36.040><c> where</c><00:01:36.280><c> we</c><00:01:36.479><c> retrieve</c><00:01:37.200><c> all</c><00:01:37.479><c> of</c><00:01:37.680><c> the</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
basically where we retrieve all of the
 

00:01:37.960 --> 00:01:39.510 align:start position:0%
basically where we retrieve all of the
answers<00:01:38.360><c> questions</c><00:01:38.880><c> the</c><00:01:38.960><c> reason</c><00:01:39.240><c> and</c><00:01:39.360><c> the</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
answers questions the reason and the
 

00:01:39.520 --> 00:01:41.109 align:start position:0%
answers questions the reason and the
actual<00:01:39.880><c> answer</c><00:01:40.200><c> for</c><00:01:40.439><c> the</c><00:01:40.560><c> multip</c><00:01:40.840><c> choice</c>

00:01:41.109 --> 00:01:41.119 align:start position:0%
actual answer for the multip choice
 

00:01:41.119 --> 00:01:43.429 align:start position:0%
actual answer for the multip choice
question<00:01:41.840><c> from</c><00:01:42.280><c> the</c><00:01:42.479><c> file</c><00:01:42.920><c> which</c><00:01:43.040><c> will</c><00:01:43.159><c> be</c><00:01:43.320><c> the</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
question from the file which will be the
 

00:01:43.439 --> 00:01:44.910 align:start position:0%
question from the file which will be the
PDF<00:01:43.960><c> and</c><00:01:44.040><c> then</c><00:01:44.119><c> whenever</c><00:01:44.360><c> it's</c><00:01:44.479><c> done</c><00:01:44.640><c> talking</c>

00:01:44.910 --> 00:01:44.920 align:start position:0%
PDF and then whenever it's done talking
 

00:01:44.920 --> 00:01:46.389 align:start position:0%
PDF and then whenever it's done talking
to<00:01:45.159><c> that</c><00:01:45.439><c> assistant</c><00:01:45.960><c> it's</c><00:01:46.079><c> going</c><00:01:46.159><c> to</c><00:01:46.280><c> come</c>

00:01:46.389 --> 00:01:46.399 align:start position:0%
to that assistant it's going to come
 

00:01:46.399 --> 00:01:48.149 align:start position:0%
to that assistant it's going to come
over<00:01:46.520><c> to</c><00:01:46.640><c> the</c><00:01:46.799><c> converter</c><00:01:47.280><c> assistant</c><00:01:47.920><c> and</c><00:01:48.079><c> this</c>

00:01:48.149 --> 00:01:48.159 align:start position:0%
over to the converter assistant and this
 

00:01:48.159 --> 00:01:51.030 align:start position:0%
over to the converter assistant and this
is<00:01:48.399><c> just</c><00:01:48.600><c> to</c><00:01:49.040><c> ensure</c><00:01:49.920><c> that</c><00:01:50.119><c> if</c><00:01:50.479><c> the</c><00:01:50.680><c> assistant</c>

00:01:51.030 --> 00:01:51.040 align:start position:0%
is just to ensure that if the assistant
 

00:01:51.040 --> 00:01:53.190 align:start position:0%
is just to ensure that if the assistant
agent<00:01:51.360><c> didn't</c><00:01:51.840><c> create</c><00:01:52.240><c> the</c><00:01:52.399><c> Json</c><00:01:52.759><c> format</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
agent didn't create the Json format
 

00:01:53.200 --> 00:01:54.910 align:start position:0%
agent didn't create the Json format
properly<00:01:54.000><c> this</c><00:01:54.119><c> agent</c><00:01:54.399><c> will</c><00:01:54.680><c> and</c><00:01:54.759><c> then</c>

00:01:54.910 --> 00:01:54.920 align:start position:0%
properly this agent will and then
 

00:01:54.920 --> 00:01:56.789 align:start position:0%
properly this agent will and then
finally<00:01:55.360><c> we</c><00:01:55.479><c> had</c><00:01:55.600><c> the</c><00:01:55.719><c> Json</c><00:01:56.119><c> assistant</c><00:01:56.640><c> which</c>

00:01:56.789 --> 00:01:56.799 align:start position:0%
finally we had the Json assistant which
 

00:01:56.799 --> 00:01:59.310 align:start position:0%
finally we had the Json assistant which
ends<00:01:57.000><c> up</c><00:01:57.240><c> saving</c><00:01:58.119><c> the</c><00:01:58.280><c> actual</c><00:01:58.520><c> Json</c><00:01:58.960><c> into</c><00:01:59.200><c> a</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
ends up saving the actual Json into a
 

00:01:59.320 --> 00:02:00.990 align:start position:0%
ends up saving the actual Json into a
file<00:01:59.600><c> so</c><00:01:59.920><c> whenever</c><00:02:00.159><c> we</c><00:02:00.280><c> open</c><00:02:00.479><c> up</c><00:02:00.600><c> one</c><00:02:00.759><c> over</c>

00:02:00.990 --> 00:02:01.000 align:start position:0%
file so whenever we open up one over
 

00:02:01.000 --> 00:02:02.910 align:start position:0%
file so whenever we open up one over
here<00:02:01.240><c> like</c><00:02:01.360><c> we</c><00:02:01.479><c> showed</c><00:02:01.680><c> in</c><00:02:01.759><c> the</c><00:02:01.920><c> example</c><00:02:02.600><c> we'll</c>

00:02:02.910 --> 00:02:02.920 align:start position:0%
here like we showed in the example we'll
 

00:02:02.920 --> 00:02:04.910 align:start position:0%
here like we showed in the example we'll
have<00:02:03.320><c> something</c><00:02:03.759><c> like</c><00:02:04.039><c> this</c><00:02:04.520><c> and</c><00:02:04.640><c> then</c><00:02:04.759><c> you</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
have something like this and then you
 

00:02:04.920 --> 00:02:06.590 align:start position:0%
have something like this and then you
also<00:02:05.079><c> need</c><00:02:05.200><c> to</c><00:02:05.320><c> create</c><00:02:05.560><c> an</c><00:02:05.719><c> environment</c><00:02:06.280><c> file</c>

00:02:06.590 --> 00:02:06.600 align:start position:0%
also need to create an environment file
 

00:02:06.600 --> 00:02:07.630 align:start position:0%
also need to create an environment file
which<00:02:06.719><c> is</c><00:02:06.799><c> going</c><00:02:06.920><c> to</c><00:02:06.960><c> be</c><00:02:07.039><c> used</c><00:02:07.280><c> by</c><00:02:07.399><c> all</c><00:02:07.520><c> the</c>

00:02:07.630 --> 00:02:07.640 align:start position:0%
which is going to be used by all the
 

00:02:07.640 --> 00:02:08.790 align:start position:0%
which is going to be used by all the
other<00:02:07.880><c> python</c><00:02:08.200><c> files</c><00:02:08.440><c> so</c><00:02:08.560><c> some</c><00:02:08.640><c> of</c><00:02:08.720><c> the</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
other python files so some of the
 

00:02:08.800 --> 00:02:10.589 align:start position:0%
other python files so some of the
standard<00:02:09.119><c> stuff</c><00:02:09.280><c> are</c><00:02:09.399><c> in</c><00:02:09.520><c> here</c><00:02:09.879><c> the</c><00:02:10.000><c> open</c><00:02:10.280><c> AI</c>

00:02:10.589 --> 00:02:10.599 align:start position:0%
standard stuff are in here the open AI
 

00:02:10.599 --> 00:02:13.510 align:start position:0%
standard stuff are in here the open AI
API<00:02:11.000><c> key</c><00:02:11.319><c> if</c><00:02:11.400><c> you</c><00:02:11.920><c> choose</c><00:02:12.200><c> to</c><00:02:12.319><c> use</c><00:02:12.480><c> GPT</c><00:02:13.319><c> there's</c>

00:02:13.510 --> 00:02:13.520 align:start position:0%
API key if you choose to use GPT there's
 

00:02:13.520 --> 00:02:16.350 align:start position:0%
API key if you choose to use GPT there's
a<00:02:13.720><c> model</c><00:02:14.120><c> and</c><00:02:14.239><c> then</c><00:02:14.360><c> a</c><00:02:14.480><c> base</c><00:02:14.760><c> URL</c><00:02:15.640><c> uh</c><00:02:15.760><c> this</c><00:02:15.959><c> is</c>

00:02:16.350 --> 00:02:16.360 align:start position:0%
a model and then a base URL uh this is
 

00:02:16.360 --> 00:02:18.589 align:start position:0%
a model and then a base URL uh this is
ama's<00:02:17.000><c> base</c><00:02:17.280><c> URL</c><00:02:17.720><c> so</c><00:02:17.879><c> if</c><00:02:17.959><c> you</c><00:02:18.040><c> choose</c><00:02:18.239><c> to</c><00:02:18.360><c> use</c><00:02:18.480><c> a</c>

00:02:18.589 --> 00:02:18.599 align:start position:0%
ama's base URL so if you choose to use a
 

00:02:18.599 --> 00:02:20.830 align:start position:0%
ama's base URL so if you choose to use a
local<00:02:18.840><c> server</c><00:02:19.760><c> you</c><00:02:19.879><c> could</c><00:02:20.040><c> also</c><00:02:20.239><c> use</c><00:02:20.480><c> LM</c>

00:02:20.830 --> 00:02:20.840 align:start position:0%
local server you could also use LM
 

00:02:20.840 --> 00:02:23.110 align:start position:0%
local server you could also use LM
studio<00:02:21.200><c> and</c><00:02:21.360><c> just</c><00:02:21.480><c> change</c><00:02:21.879><c> this</c><00:02:22.040><c> URL</c><00:02:22.840><c> now</c><00:02:22.959><c> for</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
studio and just change this URL now for
 

00:02:23.120 --> 00:02:25.190 align:start position:0%
studio and just change this URL now for
the<00:02:23.239><c> main</c><00:02:23.519><c> python</c><00:02:23.920><c> file</c><00:02:24.480><c> anytime</c><00:02:24.800><c> we're</c><00:02:24.920><c> using</c>

00:02:25.190 --> 00:02:25.200 align:start position:0%
the main python file anytime we're using
 

00:02:25.200 --> 00:02:26.949 align:start position:0%
the main python file anytime we're using
something<00:02:25.400><c> with</c><00:02:25.519><c> Lane</c><00:02:25.760><c> chain</c><00:02:26.239><c> I</c><00:02:26.519><c> talked</c><00:02:26.800><c> about</c>

00:02:26.949 --> 00:02:26.959 align:start position:0%
something with Lane chain I talked about
 

00:02:26.959 --> 00:02:28.350 align:start position:0%
something with Lane chain I talked about
in<00:02:27.080><c> yesterday's</c><00:02:27.560><c> video</c><00:02:28.000><c> you're</c><00:02:28.120><c> going</c><00:02:28.239><c> to</c>

00:02:28.350 --> 00:02:28.360 align:start position:0%
in yesterday's video you're going to
 

00:02:28.360 --> 00:02:30.509 align:start position:0%
in yesterday's video you're going to
have<00:02:28.599><c> quite</c><00:02:28.760><c> a</c><00:02:28.879><c> few</c><00:02:29.080><c> Imports</c><00:02:30.040><c> it's</c><00:02:30.200><c> just</c><00:02:30.400><c> kind</c>

00:02:30.509 --> 00:02:30.519 align:start position:0%
have quite a few Imports it's just kind
 

00:02:30.519 --> 00:02:32.150 align:start position:0%
have quite a few Imports it's just kind
of<00:02:30.640><c> how</c><00:02:30.800><c> it</c><00:02:30.920><c> is</c><00:02:31.239><c> so</c><00:02:31.480><c> after</c><00:02:31.680><c> we</c><00:02:31.800><c> have</c><00:02:31.920><c> these</c>

00:02:32.150 --> 00:02:32.160 align:start position:0%
of how it is so after we have these
 

00:02:32.160 --> 00:02:34.110 align:start position:0%
of how it is so after we have these
Imports<00:02:32.920><c> again</c><00:02:33.239><c> remember</c><00:02:33.720><c> we're</c><00:02:33.840><c> using</c>

00:02:34.110 --> 00:02:34.120 align:start position:0%
Imports again remember we're using
 

00:02:34.120 --> 00:02:37.070 align:start position:0%
Imports again remember we're using
streamlet<00:02:34.720><c> as</c><00:02:34.840><c> the</c><00:02:35.040><c> UI</c><00:02:35.879><c> so</c><00:02:36.200><c> anytime</c><00:02:36.720><c> you</c><00:02:36.840><c> see</c>

00:02:37.070 --> 00:02:37.080 align:start position:0%
streamlet as the UI so anytime you see
 

00:02:37.080 --> 00:02:39.229 align:start position:0%
streamlet as the UI so anytime you see
St<00:02:38.040><c> this</c><00:02:38.280><c> means</c><00:02:38.560><c> that</c><00:02:38.680><c> we're</c><00:02:38.840><c> creating</c>

00:02:39.229 --> 00:02:39.239 align:start position:0%
St this means that we're creating
 

00:02:39.239 --> 00:02:40.710 align:start position:0%
St this means that we're creating
something<00:02:39.480><c> using</c><00:02:39.760><c> streamlet</c><00:02:40.319><c> so</c><00:02:40.400><c> let's</c><00:02:40.599><c> look</c>

00:02:40.710 --> 00:02:40.720 align:start position:0%
something using streamlet so let's look
 

00:02:40.720 --> 00:02:43.070 align:start position:0%
something using streamlet so let's look
at<00:02:41.000><c> with</c><00:02:41.480><c> st.</c><00:02:42.200><c> sidebar</c><00:02:42.640><c> this</c><00:02:42.720><c> is</c><00:02:42.840><c> going</c><00:02:42.959><c> to</c>

00:02:43.070 --> 00:02:43.080 align:start position:0%
at with st. sidebar this is going to
 

00:02:43.080 --> 00:02:45.430 align:start position:0%
at with st. sidebar this is going to
have<00:02:43.239><c> streamlet</c><00:02:43.800><c> create</c><00:02:44.080><c> a</c><00:02:44.280><c> sidebar</c><00:02:44.680><c> Force</c>

00:02:45.430 --> 00:02:45.440 align:start position:0%
have streamlet create a sidebar Force
 

00:02:45.440 --> 00:02:46.869 align:start position:0%
have streamlet create a sidebar Force
pretty<00:02:45.680><c> straightforward</c><00:02:46.360><c> and</c><00:02:46.519><c> that's</c><00:02:46.720><c> what</c>

00:02:46.869 --> 00:02:46.879 align:start position:0%
pretty straightforward and that's what
 

00:02:46.879 --> 00:02:48.190 align:start position:0%
pretty straightforward and that's what
this<00:02:47.040><c> is</c><00:02:47.239><c> over</c><00:02:47.440><c> here</c><00:02:47.599><c> so</c><00:02:47.720><c> when</c><00:02:47.800><c> you</c><00:02:47.920><c> open</c><00:02:48.080><c> up</c>

00:02:48.190 --> 00:02:48.200 align:start position:0%
this is over here so when you open up
 

00:02:48.200 --> 00:02:49.949 align:start position:0%
this is over here so when you open up
the<00:02:48.280><c> UI</c><00:02:48.680><c> you'll</c><00:02:48.920><c> have</c><00:02:49.080><c> this</c><00:02:49.280><c> sidebar</c><00:02:49.680><c> created</c>

00:02:49.949 --> 00:02:49.959 align:start position:0%
the UI you'll have this sidebar created
 

00:02:49.959 --> 00:02:51.509 align:start position:0%
the UI you'll have this sidebar created
for<00:02:50.159><c> you</c><00:02:50.480><c> just</c><00:02:50.680><c> like</c><00:02:50.879><c> that</c><00:02:51.120><c> and</c><00:02:51.239><c> then</c><00:02:51.360><c> I</c><00:02:51.400><c> want</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
for you just like that and then I want
 

00:02:51.519 --> 00:02:54.190 align:start position:0%
for you just like that and then I want
to<00:02:51.599><c> set</c><00:02:51.760><c> the</c><00:02:51.840><c> uploaded</c><00:02:52.239><c> file</c><00:02:52.519><c> to</c><00:02:52.920><c> st.</c><00:02:53.920><c> file</c>

00:02:54.190 --> 00:02:54.200 align:start position:0%
to set the uploaded file to st. file
 

00:02:54.200 --> 00:02:56.430 align:start position:0%
to set the uploaded file to st. file
uploader<00:02:55.000><c> you</c><00:02:55.080><c> can</c><00:02:55.239><c> see</c><00:02:55.519><c> we</c><00:02:55.640><c> have</c><00:02:55.760><c> a</c><00:02:55.879><c> hardcoded</c>

00:02:56.430 --> 00:02:56.440 align:start position:0%
uploader you can see we have a hardcoded
 

00:02:56.440 --> 00:02:58.670 align:start position:0%
uploader you can see we have a hardcoded
has<00:02:56.560><c> to</c><00:02:56.640><c> be</c><00:02:56.720><c> a</c><00:02:56.920><c> specific</c><00:02:57.480><c> type</c><00:02:58.200><c> and</c><00:02:58.360><c> then</c><00:02:58.519><c> we</c>

00:02:58.670 --> 00:02:58.680 align:start position:0%
has to be a specific type and then we
 

00:02:58.680 --> 00:03:00.110 align:start position:0%
has to be a specific type and then we
have<00:02:58.879><c> the</c><00:02:59.040><c> label</c><00:02:59.519><c> so</c><00:02:59.640><c> if</c><00:02:59.720><c> you</c><00:02:59.800><c> if</c><00:02:59.840><c> you</c><00:02:59.959><c> come</c>

00:03:00.110 --> 00:03:00.120 align:start position:0%
have the label so if you if you come
 

00:03:00.120 --> 00:03:02.910 align:start position:0%
have the label so if you if you come
back<00:03:00.319><c> here</c><00:03:00.599><c> say</c><00:03:00.959><c> upload</c><00:03:01.440><c> a</c><00:03:01.640><c> PDF</c><00:03:02.080><c> file</c><00:03:02.480><c> and</c><00:03:02.599><c> then</c>

00:03:02.910 --> 00:03:02.920 align:start position:0%
back here say upload a PDF file and then
 

00:03:02.920 --> 00:03:05.350 align:start position:0%
back here say upload a PDF file and then
this<00:03:03.120><c> is</c><00:03:03.400><c> the</c><00:03:03.680><c> area</c><00:03:04.239><c> it</c><00:03:04.680><c> gives</c><00:03:04.879><c> you</c><00:03:05.080><c> in</c><00:03:05.159><c> order</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
this is the area it gives you in order
 

00:03:05.360 --> 00:03:07.509 align:start position:0%
this is the area it gives you in order
to<00:03:05.480><c> upload</c><00:03:05.760><c> a</c><00:03:05.879><c> file</c><00:03:06.200><c> so</c><00:03:06.400><c> now</c><00:03:06.760><c> if</c><00:03:06.959><c> we</c><00:03:07.200><c> have</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
to upload a file so now if we have
 

00:03:07.519 --> 00:03:09.830 align:start position:0%
to upload a file so now if we have
uploaded<00:03:07.920><c> a</c><00:03:08.040><c> file</c><00:03:08.319><c> so</c><00:03:08.560><c> if</c><00:03:08.680><c> that</c><00:03:08.799><c> is</c><00:03:08.959><c> not</c><00:03:09.159><c> none</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
uploaded a file so if that is not none
 

00:03:09.840 --> 00:03:11.229 align:start position:0%
uploaded a file so if that is not none
we're<00:03:10.000><c> going</c><00:03:10.080><c> to</c><00:03:10.280><c> have</c><00:03:10.400><c> it</c><00:03:10.599><c> right</c><00:03:10.920><c> in</c><00:03:11.040><c> the</c>

00:03:11.229 --> 00:03:11.239 align:start position:0%
we're going to have it right in the
 

00:03:11.239 --> 00:03:14.430 align:start position:0%
we're going to have it right in the
sidebar<00:03:11.799><c> file</c><00:03:12.120><c> uploaded</c><00:03:12.920><c> successfully</c><00:03:13.920><c> which</c>

00:03:14.430 --> 00:03:14.440 align:start position:0%
sidebar file uploaded successfully which
 

00:03:14.440 --> 00:03:16.070 align:start position:0%
sidebar file uploaded successfully which
it<00:03:14.560><c> does</c><00:03:14.879><c> over</c><00:03:15.120><c> here</c><00:03:15.319><c> we</c><00:03:15.440><c> use</c><00:03:15.560><c> the</c><00:03:15.640><c> same</c><00:03:15.879><c> code</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
it does over here we use the same code
 

00:03:16.080 --> 00:03:17.550 align:start position:0%
it does over here we use the same code
if<00:03:16.200><c> uploaded</c><00:03:16.560><c> file</c><00:03:16.760><c> is</c><00:03:16.879><c> not</c><00:03:17.000><c> none</c><00:03:17.239><c> mean</c><00:03:17.440><c> we</c>

00:03:17.550 --> 00:03:17.560 align:start position:0%
if uploaded file is not none mean we
 

00:03:17.560 --> 00:03:20.309 align:start position:0%
if uploaded file is not none mean we
have<00:03:17.799><c> actually</c><00:03:18.040><c> uploaded</c><00:03:18.519><c> a</c><00:03:18.840><c> file</c><00:03:19.760><c> now</c><00:03:20.120><c> we're</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
have actually uploaded a file now we're
 

00:03:20.319 --> 00:03:21.990 align:start position:0%
have actually uploaded a file now we're
going<00:03:20.400><c> to</c><00:03:20.519><c> start</c><00:03:20.760><c> the</c><00:03:21.000><c> process</c><00:03:21.519><c> so</c><00:03:21.680><c> this</c><00:03:21.799><c> is</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
going to start the process so this is
 

00:03:22.000 --> 00:03:23.910 align:start position:0%
going to start the process so this is
just<00:03:22.239><c> basic</c><00:03:22.599><c> file</c><00:03:22.879><c> naming</c><00:03:23.280><c> then</c><00:03:23.440><c> we</c><00:03:23.560><c> have</c><00:03:23.680><c> to</c>

00:03:23.910 --> 00:03:23.920 align:start position:0%
just basic file naming then we have to
 

00:03:23.920 --> 00:03:26.589 align:start position:0%
just basic file naming then we have to
enable<00:03:24.280><c> a</c><00:03:24.480><c> button</c><00:03:24.760><c> so</c><00:03:24.959><c> this</c><00:03:25.120><c> button</c><00:03:25.680><c> the</c><00:03:25.840><c> st.</c>

00:03:26.589 --> 00:03:26.599 align:start position:0%
enable a button so this button the st.
 

00:03:26.599 --> 00:03:28.910 align:start position:0%
enable a button so this button the st.
sidebar.<00:03:27.200><c> button</c><00:03:27.519><c> create</c><00:03:27.799><c> a</c><00:03:27.959><c> question</c><00:03:28.680><c> if</c><00:03:28.760><c> we</c>

00:03:28.910 --> 00:03:28.920 align:start position:0%
sidebar. button create a question if we
 

00:03:28.920 --> 00:03:30.350 align:start position:0%
sidebar. button create a question if we
come<00:03:29.080><c> back</c><00:03:29.200><c> to</c><00:03:29.319><c> the</c><00:03:29.400><c> UI</c><00:03:29.920><c> that's</c><00:03:30.080><c> just</c><00:03:30.239><c> this</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
come back to the UI that's just this
 

00:03:30.360 --> 00:03:32.229 align:start position:0%
come back to the UI that's just this
button<00:03:30.640><c> right</c><00:03:30.799><c> here</c><00:03:31.080><c> that's</c><00:03:31.319><c> all</c><00:03:31.480><c> it</c><00:03:31.640><c> is</c><00:03:32.120><c> and</c>

00:03:32.229 --> 00:03:32.239 align:start position:0%
button right here that's all it is and
 

00:03:32.239 --> 00:03:35.190 align:start position:0%
button right here that's all it is and
then<00:03:32.400><c> we</c><00:03:32.519><c> start</c><00:03:32.879><c> loading</c><00:03:33.360><c> the</c><00:03:33.599><c> file</c><00:03:34.159><c> using</c><00:03:34.920><c> pi</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
then we start loading the file using pi
 

00:03:35.200 --> 00:03:37.750 align:start position:0%
then we start loading the file using pi
muu<00:03:35.560><c> PDF</c><00:03:35.959><c> loader</c><00:03:36.360><c> so</c><00:03:36.519><c> it's</c><00:03:36.640><c> going</c><00:03:36.760><c> to</c><00:03:36.920><c> take</c><00:03:37.400><c> the</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
muu PDF loader so it's going to take the
 

00:03:37.760 --> 00:03:40.229 align:start position:0%
muu PDF loader so it's going to take the
file.<00:03:38.720><c> name</c><00:03:39.000><c> and</c><00:03:39.360><c> actually</c><00:03:39.879><c> get</c><00:03:40.080><c> that</c>

00:03:40.229 --> 00:03:40.239 align:start position:0%
file. name and actually get that
 

00:03:40.239 --> 00:03:42.350 align:start position:0%
file. name and actually get that
information<00:03:40.640><c> from</c><00:03:40.799><c> the</c><00:03:40.920><c> PDF</c><00:03:41.360><c> so</c><00:03:41.560><c> if</c><00:03:41.680><c> you</c><00:03:41.920><c> click</c>

00:03:42.350 --> 00:03:42.360 align:start position:0%
information from the PDF so if you click
 

00:03:42.360 --> 00:03:44.070 align:start position:0%
information from the PDF so if you click
the<00:03:42.519><c> button</c><00:03:42.959><c> so</c><00:03:43.200><c> the</c><00:03:43.439><c> button</c><00:03:43.720><c> variable</c><00:03:44.000><c> is</c>

00:03:44.070 --> 00:03:44.080 align:start position:0%
the button so the button variable is
 

00:03:44.080 --> 00:03:45.830 align:start position:0%
the button so the button variable is
called<00:03:44.280><c> button</c><00:03:44.480><c> enabled</c><00:03:44.959><c> if</c><00:03:45.040><c> you</c><00:03:45.280><c> click</c><00:03:45.519><c> it</c>

00:03:45.830 --> 00:03:45.840 align:start position:0%
called button enabled if you click it
 

00:03:45.840 --> 00:03:46.750 align:start position:0%
called button enabled if you click it
we're<00:03:45.959><c> going</c><00:03:46.040><c> to</c><00:03:46.120><c> start</c><00:03:46.280><c> the</c><00:03:46.400><c> whole</c><00:03:46.560><c> process</c>

00:03:46.750 --> 00:03:46.760 align:start position:0%
we're going to start the whole process
 

00:03:46.760 --> 00:03:48.350 align:start position:0%
we're going to start the whole process
of<00:03:46.840><c> splitting</c><00:03:47.159><c> the</c><00:03:47.280><c> text</c><00:03:47.480><c> into</c><00:03:47.760><c> documents</c>

00:03:48.350 --> 00:03:48.360 align:start position:0%
of splitting the text into documents
 

00:03:48.360 --> 00:03:50.470 align:start position:0%
of splitting the text into documents
saving<00:03:48.799><c> into</c><00:03:49.000><c> a</c><00:03:49.239><c> vector</c><00:03:49.560><c> database</c><00:03:50.200><c> so</c><00:03:50.319><c> we</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
saving into a vector database so we
 

00:03:50.480 --> 00:03:53.309 align:start position:0%
saving into a vector database so we
basically<00:03:50.920><c> append</c><00:03:51.599><c> the</c><00:03:51.840><c> file</c><00:03:52.360><c> into</c><00:03:52.799><c> a</c><00:03:52.959><c> docs</c>

00:03:53.309 --> 00:03:53.319 align:start position:0%
basically append the file into a docs
 

00:03:53.319 --> 00:03:55.149 align:start position:0%
basically append the file into a docs
variable<00:03:53.840><c> we</c><00:03:53.959><c> are</c><00:03:54.040><c> going</c><00:03:54.200><c> to</c><00:03:54.439><c> split</c><00:03:54.840><c> all</c><00:03:54.959><c> the</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
variable we are going to split all the
 

00:03:55.159 --> 00:03:57.789 align:start position:0%
variable we are going to split all the
text<00:03:55.480><c> into</c><00:03:55.799><c> chunks</c><00:03:56.239><c> of</c><00:03:56.400><c> size</c><00:03:56.680><c> 1000</c><00:03:57.360><c> we</c><00:03:57.519><c> create</c>

00:03:57.789 --> 00:03:57.799 align:start position:0%
text into chunks of size 1000 we create
 

00:03:57.799 --> 00:04:00.190 align:start position:0%
text into chunks of size 1000 we create
a<00:03:57.959><c> vector</c><00:03:58.200><c> store</c><00:03:58.560><c> using</c><00:03:59.079><c> chroma</c><00:03:59.760><c> Vector</c><00:04:00.000><c> store</c>

00:04:00.190 --> 00:04:00.200 align:start position:0%
a vector store using chroma Vector store
 

00:04:00.200 --> 00:04:01.949 align:start position:0%
a vector store using chroma Vector store
database<00:04:00.680><c> we're</c><00:04:00.840><c> going</c><00:04:00.920><c> to</c><00:04:01.000><c> use</c><00:04:01.239><c> hugging</c><00:04:01.599><c> face</c>

00:04:01.949 --> 00:04:01.959 align:start position:0%
database we're going to use hugging face
 

00:04:01.959 --> 00:04:04.030 align:start position:0%
database we're going to use hugging face
embeddings<00:04:02.720><c> then</c><00:04:02.799><c> we</c><00:04:02.959><c> just</c><00:04:03.079><c> basically</c><00:04:03.519><c> add</c>

00:04:04.030 --> 00:04:04.040 align:start position:0%
embeddings then we just basically add
 

00:04:04.040 --> 00:04:06.069 align:start position:0%
embeddings then we just basically add
the<00:04:04.319><c> PDF</c><00:04:04.840><c> documents</c><00:04:05.319><c> after</c><00:04:05.519><c> they</c><00:04:05.640><c> were</c><00:04:05.799><c> split</c>

00:04:06.069 --> 00:04:06.079 align:start position:0%
the PDF documents after they were split
 

00:04:06.079 --> 00:04:07.589 align:start position:0%
the PDF documents after they were split
into<00:04:06.319><c> different</c><00:04:06.519><c> chunks</c><00:04:07.000><c> into</c><00:04:07.200><c> the</c><00:04:07.360><c> vector</c>

00:04:07.589 --> 00:04:07.599 align:start position:0%
into different chunks into the vector
 

00:04:07.599 --> 00:04:09.309 align:start position:0%
into different chunks into the vector
store<00:04:08.040><c> and</c><00:04:08.120><c> now</c><00:04:08.239><c> we</c><00:04:08.360><c> can</c><00:04:08.439><c> use</c><00:04:08.599><c> our</c><00:04:08.799><c> retrieval</c>

00:04:09.309 --> 00:04:09.319 align:start position:0%
store and now we can use our retrieval
 

00:04:09.319 --> 00:04:11.910 align:start position:0%
store and now we can use our retrieval
chain<00:04:09.920><c> to</c><00:04:10.120><c> basically</c><00:04:10.439><c> use</c><00:04:10.840><c> as</c><00:04:11.159><c> context</c><00:04:11.720><c> with</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
chain to basically use as context with
 

00:04:11.920 --> 00:04:13.750 align:start position:0%
chain to basically use as context with
the<00:04:12.040><c> model</c><00:04:12.400><c> to</c><00:04:12.680><c> ask</c><00:04:12.879><c> it</c><00:04:13.000><c> a</c><00:04:13.159><c> question</c><00:04:13.480><c> and</c><00:04:13.640><c> now</c>

00:04:13.750 --> 00:04:13.760 align:start position:0%
the model to ask it a question and now
 

00:04:13.760 --> 00:04:16.229 align:start position:0%
the model to ask it a question and now
we<00:04:13.920><c> have</c><00:04:14.280><c> two</c><00:04:14.720><c> functions</c><00:04:15.239><c> that</c><00:04:15.400><c> I</c><00:04:15.560><c> created</c><00:04:16.000><c> as</c>

00:04:16.229 --> 00:04:16.239 align:start position:0%
we have two functions that I created as
 

00:04:16.239 --> 00:04:17.749 align:start position:0%
we have two functions that I created as
function<00:04:16.600><c> calling</c><00:04:17.040><c> the</c><00:04:17.160><c> first</c><00:04:17.320><c> function</c><00:04:17.560><c> is</c>

00:04:17.749 --> 00:04:17.759 align:start position:0%
function calling the first function is
 

00:04:17.759 --> 00:04:20.830 align:start position:0%
function calling the first function is
ask<00:04:18.079><c> question</c><00:04:18.759><c> where</c><00:04:18.959><c> we</c><00:04:19.199><c> use</c><00:04:19.759><c> the</c><00:04:20.040><c> QA</c><00:04:20.519><c> for</c>

00:04:20.830 --> 00:04:20.840 align:start position:0%
ask question where we use the QA for
 

00:04:20.840 --> 00:04:22.749 align:start position:0%
ask question where we use the QA for
question<00:04:21.120><c> and</c><00:04:21.320><c> answer</c><00:04:21.919><c> from</c><00:04:22.079><c> the</c><00:04:22.240><c> retrieval</c>

00:04:22.749 --> 00:04:22.759 align:start position:0%
question and answer from the retrieval
 

00:04:22.759 --> 00:04:24.909 align:start position:0%
question and answer from the retrieval
chain<00:04:23.080><c> to</c><00:04:23.759><c> ask</c><00:04:23.919><c> a</c><00:04:24.080><c> question</c><00:04:24.440><c> and</c><00:04:24.560><c> then</c><00:04:24.680><c> once</c><00:04:24.800><c> we</c>

00:04:24.909 --> 00:04:24.919 align:start position:0%
chain to ask a question and then once we
 

00:04:24.919 --> 00:04:26.790 align:start position:0%
chain to ask a question and then once we
get<00:04:25.040><c> the</c><00:04:25.199><c> response</c><00:04:25.759><c> back</c><00:04:26.120><c> we'll</c><00:04:26.320><c> grab</c><00:04:26.600><c> the</c>

00:04:26.790 --> 00:04:26.800 align:start position:0%
get the response back we'll grab the
 

00:04:26.800 --> 00:04:28.870 align:start position:0%
get the response back we'll grab the
answer<00:04:27.160><c> from</c><00:04:27.320><c> the</c><00:04:27.520><c> response</c><00:04:28.280><c> we'll</c><00:04:28.520><c> kind</c><00:04:28.639><c> of</c>

00:04:28.870 --> 00:04:28.880 align:start position:0%
answer from the response we'll kind of
 

00:04:28.880 --> 00:04:30.350 align:start position:0%
answer from the response we'll kind of
clean<00:04:29.160><c> up</c><00:04:29.280><c> the</c><00:04:29.360><c> Json</c><00:04:29.720><c> on</c><00:04:29.840><c> a</c><00:04:29.919><c> little</c><00:04:30.080><c> bit</c><00:04:30.240><c> and</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
clean up the Json on a little bit and
 

00:04:30.360 --> 00:04:31.469 align:start position:0%
clean up the Json on a little bit and
then<00:04:30.520><c> dump</c><00:04:30.880><c> that</c><00:04:31.000><c> so</c><00:04:31.120><c> we're</c><00:04:31.240><c> going</c><00:04:31.320><c> to</c><00:04:31.360><c> be</c>

00:04:31.469 --> 00:04:31.479 align:start position:0%
then dump that so we're going to be
 

00:04:31.479 --> 00:04:33.710 align:start position:0%
then dump that so we're going to be
registering<00:04:31.880><c> for</c><00:04:32.080><c> execution</c><00:04:33.080><c> with</c><00:04:33.199><c> the</c><00:04:33.320><c> user</c>

00:04:33.710 --> 00:04:33.720 align:start position:0%
registering for execution with the user
 

00:04:33.720 --> 00:04:35.029 align:start position:0%
registering for execution with the user
proxy<00:04:34.080><c> agent</c><00:04:34.440><c> and</c><00:04:34.520><c> then</c><00:04:34.639><c> we're</c><00:04:34.720><c> going</c><00:04:34.840><c> to</c><00:04:34.919><c> be</c>

00:04:35.029 --> 00:04:35.039 align:start position:0%
proxy agent and then we're going to be
 

00:04:35.039 --> 00:04:37.430 align:start position:0%
proxy agent and then we're going to be
registering<00:04:35.479><c> for</c><00:04:35.680><c> llm</c><00:04:36.600><c> with</c><00:04:36.759><c> the</c><00:04:36.960><c> assistant</c>

00:04:37.430 --> 00:04:37.440 align:start position:0%
registering for llm with the assistant
 

00:04:37.440 --> 00:04:38.390 align:start position:0%
registering for llm with the assistant
if<00:04:37.600><c> still</c><00:04:37.759><c> need</c><00:04:37.880><c> a</c><00:04:37.960><c> little</c><00:04:38.080><c> help</c><00:04:38.240><c> with</c><00:04:38.320><c> the</c>

00:04:38.390 --> 00:04:38.400 align:start position:0%
if still need a little help with the
 

00:04:38.400 --> 00:04:41.270 align:start position:0%
if still need a little help with the
function<00:04:38.680><c> calling</c><00:04:39.360><c> on</c><00:04:39.720><c> day</c><00:04:40.120><c> 18</c><00:04:40.720><c> I</c><00:04:40.880><c> created</c><00:04:41.160><c> a</c>

00:04:41.270 --> 00:04:41.280 align:start position:0%
function calling on day 18 I created a
 

00:04:41.280 --> 00:04:42.590 align:start position:0%
function calling on day 18 I created a
whole<00:04:41.440><c> video</c><00:04:41.680><c> about</c><00:04:41.919><c> function</c><00:04:42.199><c> calling</c><00:04:42.479><c> to</c>

00:04:42.590 --> 00:04:42.600 align:start position:0%
whole video about function calling to
 

00:04:42.600 --> 00:04:44.110 align:start position:0%
whole video about function calling to
help<00:04:42.720><c> you</c><00:04:42.840><c> out</c><00:04:43.039><c> then</c><00:04:43.160><c> I</c><00:04:43.240><c> have</c><00:04:43.360><c> the</c><00:04:43.479><c> save</c><00:04:43.759><c> file</c>

00:04:44.110 --> 00:04:44.120 align:start position:0%
help you out then I have the save file
 

00:04:44.120 --> 00:04:45.950 align:start position:0%
help you out then I have the save file
function<00:04:44.720><c> which</c><00:04:44.960><c> just</c><00:04:45.120><c> saves</c><00:04:45.360><c> this</c><00:04:45.520><c> file</c><00:04:45.720><c> to</c><00:04:45.840><c> a</c>

00:04:45.950 --> 00:04:45.960 align:start position:0%
function which just saves this file to a
 

00:04:45.960 --> 00:04:47.950 align:start position:0%
function which just saves this file to a
directory<00:04:46.840><c> and</c><00:04:46.960><c> that's</c><00:04:47.160><c> what</c><00:04:47.240><c> you</c><00:04:47.360><c> see</c><00:04:47.720><c> over</c>

00:04:47.950 --> 00:04:47.960 align:start position:0%
directory and that's what you see over
 

00:04:47.960 --> 00:04:49.870 align:start position:0%
directory and that's what you see over
here<00:04:48.199><c> and</c><00:04:48.320><c> then</c><00:04:48.400><c> we</c><00:04:48.520><c> call</c><00:04:48.800><c> agents.</c><00:04:49.440><c> initiate</c>

00:04:49.870 --> 00:04:49.880 align:start position:0%
here and then we call agents. initiate
 

00:04:49.880 --> 00:04:52.110 align:start position:0%
here and then we call agents. initiate
agent<00:04:50.199><c> chats</c><00:04:50.600><c> and</c><00:04:50.800><c> after</c><00:04:51.080><c> that's</c><00:04:51.360><c> done</c><00:04:51.919><c> once</c>

00:04:52.110 --> 00:04:52.120 align:start position:0%
agent chats and after that's done once
 

00:04:52.120 --> 00:04:54.550 align:start position:0%
agent chats and after that's done once
it<00:04:52.360><c> creates</c><00:04:52.919><c> the</c><00:04:53.120><c> file</c><00:04:53.400><c> over</c><00:04:53.720><c> here</c><00:04:54.360><c> we</c>

00:04:54.550 --> 00:04:54.560 align:start position:0%
it creates the file over here we
 

00:04:54.560 --> 00:04:57.029 align:start position:0%
it creates the file over here we
basically<00:04:55.080><c> read</c><00:04:55.479><c> that</c><00:04:55.840><c> file</c><00:04:56.400><c> and</c><00:04:56.520><c> then</c><00:04:56.720><c> grab</c>

00:04:57.029 --> 00:04:57.039 align:start position:0%
basically read that file and then grab
 

00:04:57.039 --> 00:04:58.710 align:start position:0%
basically read that file and then grab
the<00:04:57.280><c> question</c><00:04:57.759><c> the</c><00:04:57.880><c> correct</c><00:04:58.199><c> answer</c><00:04:58.560><c> the</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
the question the correct answer the
 

00:04:58.720 --> 00:05:01.749 align:start position:0%
the question the correct answer the
reason<00:04:59.199><c> and</c><00:04:59.840><c> the</c><00:05:00.000><c> multiple</c><00:05:00.440><c> choice</c><00:05:01.320><c> now</c><00:05:01.600><c> how</c>

00:05:01.749 --> 00:05:01.759 align:start position:0%
reason and the multiple choice now how
 

00:05:01.759 --> 00:05:04.150 align:start position:0%
reason and the multiple choice now how
did<00:05:01.919><c> we</c><00:05:02.160><c> get</c><00:05:02.680><c> uh</c><00:05:02.960><c> all</c><00:05:03.199><c> this</c><00:05:03.360><c> the</c><00:05:03.560><c> question</c><00:05:04.039><c> the</c>

00:05:04.150 --> 00:05:04.160 align:start position:0%
did we get uh all this the question the
 

00:05:04.160 --> 00:05:05.469 align:start position:0%
did we get uh all this the question the
multiple<00:05:04.520><c> choice</c><00:05:04.880><c> the</c><00:05:04.960><c> correct</c><00:05:05.240><c> answer</c><00:05:05.400><c> and</c>

00:05:05.469 --> 00:05:05.479 align:start position:0%
multiple choice the correct answer and
 

00:05:05.479 --> 00:05:07.189 align:start position:0%
multiple choice the correct answer and
the<00:05:05.600><c> reason</c><00:05:05.880><c> here</c><00:05:06.160><c> how</c><00:05:06.240><c> do</c><00:05:06.360><c> we</c><00:05:06.520><c> get</c><00:05:06.639><c> that</c><00:05:06.840><c> there</c>

00:05:07.189 --> 00:05:07.199 align:start position:0%
the reason here how do we get that there
 

00:05:07.199 --> 00:05:09.270 align:start position:0%
the reason here how do we get that there
well<00:05:07.880><c> again</c><00:05:08.120><c> with</c><00:05:08.320><c> streamlet</c><00:05:08.880><c> now</c><00:05:09.000><c> if</c><00:05:09.080><c> we</c><00:05:09.199><c> just</c>

00:05:09.270 --> 00:05:09.280 align:start position:0%
well again with streamlet now if we just
 

00:05:09.280 --> 00:05:11.790 align:start position:0%
well again with streamlet now if we just
say<00:05:09.520><c> st.</c><00:05:10.199><c> header</c><00:05:10.800><c> this</c><00:05:10.880><c> is</c><00:05:11.039><c> the</c><00:05:11.240><c> question</c><00:05:11.680><c> and</c>

00:05:11.790 --> 00:05:11.800 align:start position:0%
say st. header this is the question and
 

00:05:11.800 --> 00:05:13.469 align:start position:0%
say st. header this is the question and
then<00:05:11.960><c> I</c><00:05:12.080><c> give</c><00:05:12.240><c> it</c><00:05:12.400><c> divider</c><00:05:12.880><c> with</c><00:05:13.000><c> the</c><00:05:13.120><c> color</c>

00:05:13.469 --> 00:05:13.479 align:start position:0%
then I give it divider with the color
 

00:05:13.479 --> 00:05:15.510 align:start position:0%
then I give it divider with the color
rainbow<00:05:14.039><c> uh</c><00:05:14.120><c> st.</c><00:05:14.680><c> subhe</c><00:05:14.919><c> header</c><00:05:15.160><c> is</c><00:05:15.320><c> the</c>

00:05:15.510 --> 00:05:15.520 align:start position:0%
rainbow uh st. subhe header is the
 

00:05:15.520 --> 00:05:17.110 align:start position:0%
rainbow uh st. subhe header is the
actual<00:05:16.000><c> question</c><00:05:16.400><c> you</c><00:05:16.479><c> know</c><00:05:16.600><c> so</c><00:05:16.759><c> this</c><00:05:16.840><c> is</c><00:05:17.000><c> kind</c>

00:05:17.110 --> 00:05:17.120 align:start position:0%
actual question you know so this is kind
 

00:05:17.120 --> 00:05:19.469 align:start position:0%
actual question you know so this is kind
of<00:05:17.240><c> like</c><00:05:17.360><c> a</c><00:05:17.560><c> label</c><00:05:18.560><c> so</c><00:05:18.759><c> that's</c><00:05:18.960><c> what</c><00:05:19.080><c> you</c><00:05:19.199><c> see</c>

00:05:19.469 --> 00:05:19.479 align:start position:0%
of like a label so that's what you see
 

00:05:19.479 --> 00:05:20.990 align:start position:0%
of like a label so that's what you see
here<00:05:19.759><c> right</c><00:05:19.880><c> this</c><00:05:19.960><c> is</c><00:05:20.120><c> the</c><00:05:20.280><c> question</c><00:05:20.720><c> with</c><00:05:20.840><c> the</c>

00:05:20.990 --> 00:05:21.000 align:start position:0%
here right this is the question with the
 

00:05:21.000 --> 00:05:22.950 align:start position:0%
here right this is the question with the
divider<00:05:21.680><c> and</c><00:05:21.800><c> then</c><00:05:21.960><c> the</c><00:05:22.160><c> actual</c><00:05:22.560><c> question</c>

00:05:22.950 --> 00:05:22.960 align:start position:0%
divider and then the actual question
 

00:05:22.960 --> 00:05:24.629 align:start position:0%
divider and then the actual question
from<00:05:23.120><c> the</c><00:05:23.240><c> Json</c><00:05:23.680><c> file</c><00:05:24.080><c> I</c><00:05:24.199><c> create</c><00:05:24.479><c> two</c>

00:05:24.629 --> 00:05:24.639 align:start position:0%
from the Json file I create two
 

00:05:24.639 --> 00:05:27.189 align:start position:0%
from the Json file I create two
different<00:05:24.919><c> columns</c><00:05:25.560><c> with</c><00:05:25.880><c> the</c><00:05:26.080><c> First</c><00:05:26.479><c> Column</c>

00:05:27.189 --> 00:05:27.199 align:start position:0%
different columns with the First Column
 

00:05:27.199 --> 00:05:29.189 align:start position:0%
different columns with the First Column
I<00:05:27.360><c> just</c><00:05:27.520><c> set</c><00:05:27.800><c> the</c><00:05:27.919><c> info</c><00:05:28.199><c> to</c><00:05:28.360><c> Choice</c><00:05:28.600><c> A</c><00:05:28.759><c> and</c><00:05:28.919><c> B</c>

00:05:29.189 --> 00:05:29.199 align:start position:0%
I just set the info to Choice A and B
 

00:05:29.199 --> 00:05:31.150 align:start position:0%
I just set the info to Choice A and B
and<00:05:29.280><c> then</c><00:05:29.400><c> with</c><00:05:29.479><c> the</c><00:05:29.720><c> second</c><00:05:29.960><c> column</c><00:05:30.600><c> I</c><00:05:30.800><c> create</c>

00:05:31.150 --> 00:05:31.160 align:start position:0%
and then with the second column I create
 

00:05:31.160 --> 00:05:33.749 align:start position:0%
and then with the second column I create
the<00:05:31.280><c> info</c><00:05:31.639><c> with</c><00:05:31.759><c> Choice</c><00:05:32.039><c> C</c><00:05:32.319><c> and</c><00:05:32.560><c> D</c><00:05:32.880><c> so</c><00:05:33.120><c> Choice</c><00:05:33.560><c> A</c>

00:05:33.749 --> 00:05:33.759 align:start position:0%
the info with Choice C and D so Choice A
 

00:05:33.759 --> 00:05:35.870 align:start position:0%
the info with Choice C and D so Choice A
and<00:05:33.960><c> B</c><00:05:34.240><c> and</c><00:05:34.360><c> then</c><00:05:34.520><c> Choice</c><00:05:34.800><c> c</c><00:05:35.160><c> and</c><00:05:35.360><c> d</c><00:05:35.720><c> and</c><00:05:35.800><c> then</c>

00:05:35.870 --> 00:05:35.880 align:start position:0%
and B and then Choice c and d and then
 

00:05:35.880 --> 00:05:37.990 align:start position:0%
and B and then Choice c and d and then
we<00:05:36.039><c> create</c><00:05:36.280><c> a</c><00:05:36.520><c> caption</c><00:05:36.840><c> and</c><00:05:36.960><c> a</c><00:05:37.080><c> subheader</c><00:05:37.520><c> so</c>

00:05:37.990 --> 00:05:38.000 align:start position:0%
we create a caption and a subheader so
 

00:05:38.000 --> 00:05:39.390 align:start position:0%
we create a caption and a subheader so
the<00:05:38.160><c> cap</c><00:05:38.400><c> the</c><00:05:38.440><c> first</c><00:05:38.639><c> caption</c><00:05:38.880><c> is</c><00:05:39.000><c> the</c><00:05:39.120><c> correct</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
the cap the first caption is the correct
 

00:05:39.400 --> 00:05:41.150 align:start position:0%
the cap the first caption is the correct
answer<00:05:39.720><c> with</c><00:05:39.880><c> the</c><00:05:40.080><c> actual</c><00:05:40.360><c> correct</c><00:05:40.680><c> answer</c><00:05:40.960><c> so</c>

00:05:41.150 --> 00:05:41.160 align:start position:0%
answer with the actual correct answer so
 

00:05:41.160 --> 00:05:42.950 align:start position:0%
answer with the actual correct answer so
the<00:05:41.280><c> caption</c><00:05:41.560><c> is</c><00:05:41.680><c> kind</c><00:05:41.759><c> of</c><00:05:41.880><c> like</c><00:05:42.000><c> a</c><00:05:42.120><c> label</c><00:05:42.759><c> then</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
the caption is kind of like a label then
 

00:05:42.960 --> 00:05:44.629 align:start position:0%
the caption is kind of like a label then
the<00:05:43.360><c> this</c><00:05:43.520><c> caption</c><00:05:43.800><c> is</c><00:05:43.919><c> for</c><00:05:44.080><c> the</c><00:05:44.199><c> reason</c><00:05:44.479><c> which</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
the this caption is for the reason which
 

00:05:44.639 --> 00:05:46.430 align:start position:0%
the this caption is for the reason which
again<00:05:44.759><c> is</c><00:05:44.880><c> a</c><00:05:45.000><c> label</c><00:05:45.400><c> and</c><00:05:45.520><c> then</c><00:05:45.680><c> we</c><00:05:45.880><c> give</c><00:05:46.280><c> the</c>

00:05:46.430 --> 00:05:46.440 align:start position:0%
again is a label and then we give the
 

00:05:46.440 --> 00:05:49.189 align:start position:0%
again is a label and then we give the
correct<00:05:46.800><c> answer</c><00:05:47.080><c> and</c><00:05:47.319><c> reason</c><00:05:48.240><c> which</c><00:05:48.639><c> gives</c><00:05:48.880><c> us</c>

00:05:49.189 --> 00:05:49.199 align:start position:0%
correct answer and reason which gives us
 

00:05:49.199 --> 00:05:50.909 align:start position:0%
correct answer and reason which gives us
this<00:05:49.440><c> right</c><00:05:49.600><c> here</c><00:05:49.759><c> on</c><00:05:49.880><c> the</c><00:05:50.000><c> screen</c><00:05:50.440><c> now</c>

00:05:50.909 --> 00:05:50.919 align:start position:0%
this right here on the screen now
 

00:05:50.919 --> 00:05:53.390 align:start position:0%
this right here on the screen now
finally<00:05:51.520><c> how</c><00:05:51.639><c> do</c><00:05:51.759><c> we</c><00:05:51.960><c> run</c><00:05:52.319><c> this</c><00:05:52.800><c> well</c><00:05:53.160><c> if</c><00:05:53.280><c> you</c>

00:05:53.390 --> 00:05:53.400 align:start position:0%
finally how do we run this well if you
 

00:05:53.400 --> 00:05:55.870 align:start position:0%
finally how do we run this well if you
open<00:05:53.720><c> up</c><00:05:53.840><c> your</c><00:05:54.120><c> terminal</c><00:05:55.120><c> and</c><00:05:55.240><c> you</c><00:05:55.360><c> just</c><00:05:55.520><c> type</c>

00:05:55.870 --> 00:05:55.880 align:start position:0%
open up your terminal and you just type
 

00:05:55.880 --> 00:05:58.790 align:start position:0%
open up your terminal and you just type
streamlit<00:05:56.880><c> run</c><00:05:57.720><c> and</c><00:05:57.840><c> then</c><00:05:58.039><c> the</c><00:05:58.199><c> name</c><00:05:58.479><c> of</c><00:05:58.639><c> the</c>

00:05:58.790 --> 00:05:58.800 align:start position:0%
streamlit run and then the name of the
 

00:05:58.800 --> 00:06:00.510 align:start position:0%
streamlit run and then the name of the
main<00:05:59.080><c> python</c><00:05:59.400><c> file</c><00:05:59.800><c> which</c><00:05:59.880><c> will</c><00:06:00.000><c> be</c><00:06:00.080><c> streamlet</c>

00:06:00.510 --> 00:06:00.520 align:start position:0%
main python file which will be streamlet
 

00:06:00.520 --> 00:06:03.150 align:start position:0%
main python file which will be streamlet
run<00:06:01.039><c> main.py</c><00:06:02.039><c> press</c><00:06:02.319><c> enter</c><00:06:02.759><c> and</c><00:06:02.840><c> you</c><00:06:02.919><c> can</c><00:06:03.039><c> see</c>

00:06:03.150 --> 00:06:03.160 align:start position:0%
run main.py press enter and you can see
 

00:06:03.160 --> 00:06:04.390 align:start position:0%
run main.py press enter and you can see
it's<00:06:03.240><c> going</c><00:06:03.360><c> to</c><00:06:03.479><c> start</c><00:06:03.680><c> opening</c><00:06:03.960><c> it</c><00:06:04.080><c> up</c><00:06:04.240><c> for</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
it's going to start opening it up for
 

00:06:04.400 --> 00:06:05.909 align:start position:0%
it's going to start opening it up for
you<00:06:04.639><c> it</c><00:06:04.759><c> might</c><00:06:04.960><c> take</c><00:06:05.160><c> a</c><00:06:05.440><c> might</c><00:06:05.600><c> take</c><00:06:05.720><c> a</c><00:06:05.800><c> few</c>

00:06:05.909 --> 00:06:05.919 align:start position:0%
you it might take a might take a few
 

00:06:05.919 --> 00:06:07.270 align:start position:0%
you it might take a might take a few
seconds<00:06:06.160><c> to</c><00:06:06.319><c> actually</c><00:06:06.560><c> start</c><00:06:06.919><c> but</c><00:06:07.039><c> when</c><00:06:07.160><c> it's</c>

00:06:07.270 --> 00:06:07.280 align:start position:0%
seconds to actually start but when it's
 

00:06:07.280 --> 00:06:09.110 align:start position:0%
seconds to actually start but when it's
working<00:06:07.759><c> you'll</c><00:06:07.960><c> see</c><00:06:08.280><c> this</c><00:06:08.520><c> and</c><00:06:08.639><c> then</c><00:06:08.800><c> you</c><00:06:08.919><c> can</c>

00:06:09.110 --> 00:06:09.120 align:start position:0%
working you'll see this and then you can
 

00:06:09.120 --> 00:06:11.110 align:start position:0%
working you'll see this and then you can
just<00:06:09.280><c> start</c><00:06:09.520><c> browsing</c><00:06:10.240><c> and</c><00:06:10.440><c> uploading</c><00:06:10.919><c> your</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
just start browsing and uploading your
 

00:06:11.120 --> 00:06:13.510 align:start position:0%
just start browsing and uploading your
PDF<00:06:11.560><c> file</c><00:06:11.919><c> now</c><00:06:12.080><c> I</c><00:06:12.199><c> had</c><00:06:12.360><c> the</c><00:06:12.520><c> PDFs</c><00:06:13.080><c> in</c><00:06:13.319><c> the</c>

00:06:13.510 --> 00:06:13.520 align:start position:0%
PDF file now I had the PDFs in the
 

00:06:13.520 --> 00:06:15.230 align:start position:0%
PDF file now I had the PDFs in the
directory<00:06:14.000><c> of</c><00:06:14.160><c> the</c><00:06:14.360><c> project</c><00:06:14.800><c> and</c><00:06:14.919><c> as</c><00:06:15.039><c> you</c><00:06:15.120><c> can</c>

00:06:15.230 --> 00:06:15.240 align:start position:0%
directory of the project and as you can
 

00:06:15.240 --> 00:06:17.270 align:start position:0%
directory of the project and as you can
see<00:06:15.440><c> it</c><00:06:15.680><c> only</c><00:06:16.039><c> highlights</c><00:06:16.840><c> the</c><00:06:16.960><c> ancient</c>

00:06:17.270 --> 00:06:17.280 align:start position:0%
see it only highlights the ancient
 

00:06:17.280 --> 00:06:19.350 align:start position:0%
see it only highlights the ancient
ram.pdf<00:06:17.919><c> file</c><00:06:18.599><c> because</c><00:06:18.800><c> we're</c><00:06:19.000><c> only</c>

00:06:19.350 --> 00:06:19.360 align:start position:0%
ram.pdf file because we're only
 

00:06:19.360 --> 00:06:21.629 align:start position:0%
ram.pdf file because we're only
expecting<00:06:19.919><c> a</c><00:06:20.120><c> PDF</c><00:06:20.520><c> file</c><00:06:20.880><c> and</c><00:06:21.000><c> then</c><00:06:21.360><c> file</c>

00:06:21.629 --> 00:06:21.639 align:start position:0%
expecting a PDF file and then file
 

00:06:21.639 --> 00:06:23.230 align:start position:0%
expecting a PDF file and then file
successfully<00:06:22.120><c> uploaded</c><00:06:22.560><c> it</c><00:06:22.680><c> gives</c><00:06:22.880><c> the</c><00:06:23.039><c> size</c>

00:06:23.230 --> 00:06:23.240 align:start position:0%
successfully uploaded it gives the size
 

00:06:23.240 --> 00:06:24.670 align:start position:0%
successfully uploaded it gives the size
of<00:06:23.360><c> it</c><00:06:23.520><c> and</c><00:06:23.680><c> then</c><00:06:23.800><c> you</c><00:06:23.880><c> can</c><00:06:24.039><c> click</c><00:06:24.319><c> create</c>

00:06:24.670 --> 00:06:24.680 align:start position:0%
of it and then you can click create
 

00:06:24.680 --> 00:06:26.150 align:start position:0%
of it and then you can click create
question<00:06:25.160><c> and</c><00:06:25.319><c> it</c><00:06:25.400><c> would</c><00:06:25.560><c> create</c><00:06:25.759><c> a</c><00:06:25.880><c> question</c>

00:06:26.150 --> 00:06:26.160 align:start position:0%
question and it would create a question
 

00:06:26.160 --> 00:06:27.469 align:start position:0%
question and it would create a question
for<00:06:26.319><c> you</c><00:06:26.520><c> with</c><00:06:26.639><c> all</c><00:06:26.759><c> the</c><00:06:26.880><c> answers</c><00:06:27.199><c> like</c><00:06:27.319><c> we've</c>

00:06:27.469 --> 00:06:27.479 align:start position:0%
for you with all the answers like we've
 

00:06:27.479 --> 00:06:29.909 align:start position:0%
for you with all the answers like we've
seen<00:06:27.800><c> all</c><00:06:27.960><c> right</c><00:06:28.280><c> awesome</c><00:06:28.800><c> on</c><00:06:29.039><c> day</c><00:06:29.160><c> 20</c><00:06:29.639><c> we</c><00:06:29.759><c> have</c>

00:06:29.909 --> 00:06:29.919 align:start position:0%
seen all right awesome on day 20 we have
 

00:06:29.919 --> 00:06:32.309 align:start position:0%
seen all right awesome on day 20 we have
now<00:06:30.160><c> created</c><00:06:30.479><c> our</c><00:06:30.639><c> own</c><00:06:30.960><c> quizzes</c><00:06:31.720><c> based</c><00:06:32.039><c> off</c><00:06:32.199><c> of</c>

00:06:32.309 --> 00:06:32.319 align:start position:0%
now created our own quizzes based off of
 

00:06:32.319 --> 00:06:34.629 align:start position:0%
now created our own quizzes based off of
a<00:06:32.520><c> PDF</c><00:06:33.039><c> that</c><00:06:33.160><c> we</c><00:06:33.360><c> uploaded</c><00:06:34.120><c> so</c><00:06:34.280><c> that</c><00:06:34.400><c> we</c><00:06:34.479><c> can</c>

00:06:34.629 --> 00:06:34.639 align:start position:0%
a PDF that we uploaded so that we can
 

00:06:34.639 --> 00:06:36.469 align:start position:0%
a PDF that we uploaded so that we can
give<00:06:34.960><c> context</c><00:06:35.440><c> to</c><00:06:35.599><c> the</c><00:06:35.720><c> model</c><00:06:36.039><c> so</c><00:06:36.280><c> we</c><00:06:36.360><c> could</c>

00:06:36.469 --> 00:06:36.479 align:start position:0%
give context to the model so we could
 

00:06:36.479 --> 00:06:37.870 align:start position:0%
give context to the model so we could
ask<00:06:36.639><c> it</c><00:06:36.759><c> a</c><00:06:36.919><c> question</c><00:06:37.400><c> this</c><00:06:37.560><c> is</c><00:06:37.680><c> kind</c><00:06:37.759><c> of</c>

00:06:37.870 --> 00:06:37.880 align:start position:0%
ask it a question this is kind of
 

00:06:37.880 --> 00:06:39.589 align:start position:0%
ask it a question this is kind of
putting<00:06:38.120><c> a</c><00:06:38.240><c> couple</c><00:06:38.599><c> pieces</c><00:06:39.039><c> together</c><00:06:39.400><c> where</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
putting a couple pieces together where
 

00:06:39.599 --> 00:06:42.150 align:start position:0%
putting a couple pieces together where
we<00:06:39.759><c> have</c><00:06:40.280><c> the</c><00:06:40.440><c> we</c><00:06:40.520><c> can</c><00:06:40.639><c> read</c><00:06:40.919><c> from</c><00:06:41.039><c> a</c><00:06:41.240><c> PDF</c><00:06:41.960><c> but</c>

00:06:42.150 --> 00:06:42.160 align:start position:0%
we have the we can read from a PDF but
 

00:06:42.160 --> 00:06:44.950 align:start position:0%
we have the we can read from a PDF but
now<00:06:42.319><c> we</c><00:06:42.440><c> can</c><00:06:42.639><c> also</c><00:06:43.039><c> have</c><00:06:43.199><c> a</c><00:06:43.400><c> UI</c><00:06:44.000><c> and</c><00:06:44.240><c> aogen</c><00:06:44.800><c> with</c>

00:06:44.950 --> 00:06:44.960 align:start position:0%
now we can also have a UI and aogen with
 

00:06:44.960 --> 00:06:46.670 align:start position:0%
now we can also have a UI and aogen with
function<00:06:45.319><c> calling</c><00:06:45.800><c> together</c><00:06:46.240><c> let</c><00:06:46.360><c> me</c><00:06:46.479><c> know</c><00:06:46.599><c> in</c>

00:06:46.670 --> 00:06:46.680 align:start position:0%
function calling together let me know in
 

00:06:46.680 --> 00:06:49.070 align:start position:0%
function calling together let me know in
the<00:06:46.840><c> comments</c><00:06:47.400><c> what</c><00:06:47.680><c> PDF</c><00:06:48.120><c> you</c><00:06:48.319><c> use</c><00:06:48.599><c> to</c><00:06:48.840><c> create</c>

00:06:49.070 --> 00:06:49.080 align:start position:0%
the comments what PDF you use to create
 

00:06:49.080 --> 00:06:50.430 align:start position:0%
the comments what PDF you use to create
a<00:06:49.199><c> question</c><00:06:49.560><c> with</c><00:06:49.759><c> I'd</c><00:06:49.880><c> love</c><00:06:50.000><c> to</c><00:06:50.080><c> hear</c><00:06:50.240><c> your</c>

00:06:50.430 --> 00:06:50.440 align:start position:0%
a question with I'd love to hear your
 

00:06:50.440 --> 00:06:51.950 align:start position:0%
a question with I'd love to hear your
thoughts<00:06:50.680><c> and</c><00:06:50.919><c> any</c><00:06:51.120><c> other</c><00:06:51.479><c> just</c><00:06:51.639><c> general</c>

00:06:51.950 --> 00:06:51.960 align:start position:0%
thoughts and any other just general
 

00:06:51.960 --> 00:06:53.710 align:start position:0%
thoughts and any other just general
comments<00:06:52.280><c> that</c><00:06:52.400><c> you</c><00:06:52.520><c> have</c><00:06:52.720><c> about</c><00:06:53.000><c> this</c><00:06:53.400><c> let</c><00:06:53.599><c> me</c>

00:06:53.710 --> 00:06:53.720 align:start position:0%
comments that you have about this let me
 

00:06:53.720 --> 00:06:55.110 align:start position:0%
comments that you have about this let me
know<00:06:53.919><c> down</c><00:06:54.080><c> below</c><00:06:54.440><c> here</c><00:06:54.520><c> is</c><00:06:54.680><c> some</c><00:06:54.840><c> more</c>

00:06:55.110 --> 00:06:55.120 align:start position:0%
know down below here is some more
 

00:06:55.120 --> 00:06:57.110 align:start position:0%
know down below here is some more
content<00:06:55.440><c> from</c><00:06:55.560><c> the</c><00:06:55.680><c> 31</c><00:06:56.160><c> day</c><00:06:56.319><c> challenge</c><00:06:57.000><c> thank</c>

00:06:57.110 --> 00:06:57.120 align:start position:0%
content from the 31 day challenge thank
 

00:06:57.120 --> 00:06:58.550 align:start position:0%
content from the 31 day challenge thank
you<00:06:57.240><c> for</c><00:06:57.400><c> watching</c><00:06:57.759><c> and</c><00:06:57.879><c> I'll</c><00:06:58.039><c> see</c><00:06:58.199><c> you</c><00:06:58.360><c> next</c>

00:06:58.550 --> 00:06:58.560 align:start position:0%
you for watching and I'll see you next
 

00:06:58.560 --> 00:07:00.960 align:start position:0%
you for watching and I'll see you next
video<00:06:58.840><c> by</c>

