"""
Utilities for managing files with the Google GenAI File API.

Includes functions for uploading, listing, deleting, and getting file metadata.
"""

import logging
import os
import asyncio
import mimetypes
from pathlib import Path
from typing import Optional, List, Dict, BinaryIO
import time

import google
from google import genai
from google.genai import types
from google.api_core import exceptions as google_exceptions
from dotenv import load_dotenv, find_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Inline normalize_filename function to avoid relative import issues
def normalize_filename(filename, max_length: int = 200) -> str:
    """
    Normalizes a filename to be safe for most file systems and APIs.
    Removes invalid characters, limits length, and handles unicode.
    """
    import re
    import unicodedata
    
    # If path object, extract just the name
    if isinstance(filename, Path):
        filename = filename.name
    elif os.path.sep in filename:
        filename = os.path.basename(filename)
    
    # Normalize unicode characters
    filename = unicodedata.normalize('NFKD', filename)
    
    # Replace unsafe chars with underscores
    filename = re.sub(r'[^a-zA-Z0-9_\-. ]', '_', filename)
    
    # Remove duplicate underscores, spaces, and periods
    filename = re.sub(r'__+', '_', filename)
    filename = re.sub(r'  +', ' ', filename)
    filename = re.sub(r'\.\.+', '.', filename)
    
    # Trim leading/trailing spaces and underscores
    filename = filename.strip(' _')
    
    # Truncate if too long, preserving extension
    if len(filename) > max_length:
        name, ext = os.path.splitext(filename)
        # Leave space for the extension
        max_name_length = max_length - len(ext)
        if max_name_length < 1:  # Extension itself is too long
            return filename[:max_length]
        filename = name[:max_name_length] + ext
    
    # If filename becomes empty, provide a default
    if not filename:
        filename = "unnamed_file"
        
    return filename
from .genai_utils import DEFAULT_MODEL_NAME

# Initialize logger for this module
logger = logging.getLogger(__name__)

# Define RETRYABLE_API_ERRORS at module level
RETRYABLE_API_ERRORS = (
    google_exceptions.InternalServerError,
    google_exceptions.ServiceUnavailable,
    # Potentially add google_exceptions.DeadlineExceeded if needed
)

class GenaiFileManager:
    """Manages file interactions (upload, list, get, delete) with the Google GenAI API."""

    def __init__(self):
        """Initializes the Gemini asynchronous client."""
        self._client: Optional[genai.client.AsyncClient] = None
        try:
            # Load environment variables from .env.local or .env file
            dotenv_path = find_dotenv(filename='.env.local', raise_error_if_not_found=False) \
                            or find_dotenv(filename='.env', raise_error_if_not_found=False)
            if dotenv_path:
                load_dotenv(dotenv_path=dotenv_path)
                logger.info(f"FileManager loaded environment variables from: {dotenv_path}")
            else:
                logger.warning("FileManager: No .env.local or .env file found. Relying on system environment variables.")

            # Explicitly get API key
            api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("API Key not found. Set GOOGLE_API_KEY or GEMINI_API_KEY in environment or .env file.")

            # Initialize the asynchronous client
            self._client = genai.Client(api_key=api_key).aio
            logger.info("GenaiFileManager initialized Google GenAI AsyncClient successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize Google GenAI Client for FileManager: {e}", exc_info=True)
            # Propagate the error to signal initialization failure
            raise RuntimeError("Failed to initialize GenAI client for FileManager") from e

    async def _get_mime_type(self, file_path: Path) -> str:
        """Guesses the MIME type of a file."""
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            # Default to a generic type if guessing fails
            mime_type = "application/octet-stream"
            logger.warning(f"Could not guess MIME type for {file_path}. Defaulting to {mime_type}.")
        return mime_type

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10),
           retry=retry_if_exception_type(RETRYABLE_API_ERRORS), reraise=True)
    async def upload_file(
        self,
        file_path: Path,
        display_name: Optional[str] = None,
        mime_type: Optional[str] = None,
        timeout_seconds: int = 300, # Reduced default timeout
        polling_interval_seconds: int = 5
    ) -> Optional[types.File]:
        """Uploads a file, attempts to set display name, and polls until ACTIVE."""
        if not self._client:
            logger.error("Cannot upload file: GenAI client not initialized.")
            return None

        if not isinstance(file_path, Path) or not file_path.is_file():
            logger.error(f"Invalid or non-existent file path: {file_path}")
            return None

        # Determine display name and mime type
        effective_display_name = normalize_filename(display_name or file_path.name)
        effective_mime_type = mime_type or await self._get_mime_type(file_path)

        try:
            logger.debug(f"Attempting upload: {file_path}, display_name: {effective_display_name}, mime: {effective_mime_type}")
            upload_start = time.monotonic()

            # Create a specific UploadFileConfig object for type safety
            upload_config = types.UploadFileConfig(
                mime_type=effective_mime_type or None, # Ensure None if empty
                display_name=effective_display_name or None # Ensure None if empty
            )

            logger.debug(f"Upload attempt for {effective_display_name} with config: mime='{upload_config.mime_type}', display_name='{upload_config.display_name}'")
            # Pass file path directly and metadata via config
            upload_response = await self._client.files.upload(
                file=file_path, # Pass Path object directly (SDK should handle it)
                config=upload_config
            )

            file_id = getattr(upload_response, 'name', None)
            if not file_id or not isinstance(file_id, str):
                logger.error(f"Upload for {effective_display_name} did not return a valid file ID. Response: {upload_response}")
                return None

            logger.info(f"File '{effective_display_name}' upload initiated in {upload_start - time.monotonic():.2f}s. File ID: {file_id}. Polling for ACTIVE state...")

            # --- Polling Logic Integrated --- 
            poll_start_time = time.monotonic()
            while True:
                elapsed_time = time.monotonic() - poll_start_time
                if elapsed_time > timeout_seconds:
                    logger.error(f"Timeout ({timeout_seconds}s) waiting for file {file_id} to become active.")
                    # Optionally attempt deletion
                    # await self.delete_file(file_id)
                    return None

                try:
                    # Use get_file (assuming it exists and works)
                    file_info: Optional[types.File] = await self.get_file(file_id)

                    if file_info:
                        state_enum = getattr(file_info, 'state', None)
                        current_state = state_enum.name if state_enum else 'STATE_UNSPECIFIED'
                        logger.debug(f"File {file_id} state: {current_state} (Elapsed: {elapsed_time:.1f}s)")

                        if current_state == "ACTIVE":
                            logger.info(f"File {file_id} ({getattr(file_info, 'display_name', 'N/A')}) is ACTIVE.")
                            return file_info # Success!
                        elif current_state in ["FAILED", "STATE_UNSPECIFIED"]:
                            logger.error(f"File {file_id} processing failed or entered bad state: {current_state}.")
                            return None # Failure
                    else:
                        logger.warning(f"Polling for {file_id}: get_file returned None. Retrying soon...")
                
                except google_exceptions.GoogleAPIError as poll_api_err:
                    logger.error(f"API Error during polling for {file_id}: {poll_api_err}", exc_info=False)
                    # Decide if specific API errors during polling should break the loop or just continue
                except Exception as poll_err:
                    logger.error(f"Unexpected error during polling check for {file_id}: {poll_err}", exc_info=False)
                    # Consider breaking if it's a persistent unexpected error

                await asyncio.sleep(polling_interval_seconds) # Wait before next poll
            # --- End of Polling Logic --- 

        except FileNotFoundError as fnf_error:
            logger.error(f"File not found during upload process: {fnf_error}")
            return None
        except google_exceptions.GoogleAPIError as api_error:
            logger.error(f"Google API Error during upload initiation for {effective_display_name}: {api_error}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Unexpected error during upload initiation for {effective_display_name}: {e}", exc_info=True)
            return None

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10),
           retry=retry_if_exception_type(RETRYABLE_API_ERRORS), reraise=True)
    async def upload_file_from_buffer(
        self,
        file_buffer: BinaryIO,
        display_name: str,
        mime_type: str = "application/octet-stream",
        timeout_seconds: int = 300,
        polling_interval_seconds: int = 5
    ) -> Optional[types.File]:
        """
        Uploads a file from a binary buffer, sets the display name, and polls until ACTIVE.
        
        Args:
            file_buffer: Binary IO object containing the file data
            display_name: Display name for the file
            mime_type: MIME type of the file
            timeout_seconds: Maximum time to wait for file to be processed
            polling_interval_seconds: How often to check file status
            
        Returns:
            File object if successful, None otherwise
        """
        if not self._client:
            logger.error("Cannot upload file: GenAI client not initialized.")
            return None

        # Normalize the display name for safety
        effective_display_name = normalize_filename(display_name)
        
        try:
            logger.info(f"Uploading file from buffer with name: {effective_display_name}")
            
            # Upload the file from buffer using the correct SDK method
            # Create a temporary file from the buffer
            import tempfile
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{effective_display_name.split('.')[-1] if '.' in effective_display_name else 'bin'}") as temp_file:
                temp_file_path = temp_file.name
                # Write buffer contents to temp file
                file_buffer.seek(0)  # Ensure we're at the start of the buffer
                temp_file.write(file_buffer.read())
            
            try:
                # Create upload config
                upload_config = types.UploadFileConfig(
                    mime_type=mime_type,
                    display_name=effective_display_name
                )
                
                # Upload using the standard upload method
                file_obj = await self._client.files.upload(
                    file=temp_file_path,
                    config=upload_config
                )
            finally:
                # Clean up the temporary file
                try:
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
                except Exception as cleanup_err:
                    logger.warning(f"Failed to clean up temporary file {temp_file_path}: {cleanup_err}")
            
            if not file_obj or not hasattr(file_obj, 'name'):
                logger.error(f"Failed to upload file from buffer: {effective_display_name}. Invalid response from API.")
                return None
                
            # Check if file_obj has a name attribute and it's not None
            if not hasattr(file_obj, 'name') or file_obj.name is None:
                logger.error(f"Upload response missing file ID for {effective_display_name}")
                return None
                
            file_id = file_obj.name  # Now we know file_id is not None
            logger.info(f"File uploaded with ID: {file_id}. Display name: {effective_display_name}. Waiting for processing...")
            
            # Monitor until file is ACTIVE or timeout
            start_time = time.time()
            while time.time() - start_time < timeout_seconds:
                # Since we've verified file_id is not None above, this is safe
                file_info = await self.get_file(file_id)
                
                if not file_info:
                    logger.error(f"File not found during status check: {file_id}")
                    return None
                    
                if hasattr(file_info, 'state') and file_info.state is not None:
                    # First check if state has name attribute
                    if hasattr(file_info.state, 'name') and file_info.state.name is not None:
                        status = file_info.state.name
                    else:
                        status = str(file_info.state)
                    logger.debug(f"File {file_id} status: {status}")
                    
                    if status == 'ACTIVE':
                        logger.info(f"File {file_id} is now ACTIVE and ready for use")
                        return file_info
                    elif status == 'FAILED':
                        logger.error(f"File {file_id} processing failed")
                        return None
                
                await asyncio.sleep(polling_interval_seconds)
            
            logger.error(f"Timed out waiting for file {file_id} to become ACTIVE after {timeout_seconds}s")
            return None
            
        except google_exceptions.GoogleAPIError as api_error:
            logger.error(f"API Error uploading file from buffer: {api_error}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Unexpected error uploading file from buffer: {e}", exc_info=True)
            return None

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10),
           retry=retry_if_exception_type(RETRYABLE_API_ERRORS + (google_exceptions.NotFound,)), # Include NotFound for potential eventual consistency
           reraise=True)
    async def get_file(self, file_id: str) -> Optional[types.File]:
        """Retrieves file metadata by file ID (name)."""
        if not self._client:
            logger.error(f"Cannot get file {file_id}: GenAI client not initialized.")
            return None
        try:
            logger.debug(f"Attempting to get metadata for file_id: {file_id}")
            # Correct the call to use self._client.files.get
            file_info = await self._client.files.get(name=file_id)
            logger.debug(f"Successfully retrieved metadata for {file_id}")
            return file_info
        except google_exceptions.NotFound:
            logger.error(f"File not found with ID: {file_id}")
            return None
        except google_exceptions.GoogleAPIError as api_error:
            logger.error(f"API Error getting file {file_id}: {api_error}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting file {file_id}: {e}", exc_info=True)
            return None

    async def list_files(self, page_size: int = 100) -> List[types.File]:
        """Lists all files stored in the Gemini API.

        Args:
            page_size: Number of files to retrieve per page.

        Returns:
            A list of google.genai.types.File objects.
        """
        if not self._client:
            logger.error("FileManager client not initialized. Cannot list files.")
            return []

        all_files = []
        try:
            logger.info(f"Listing files from Gemini API (page size: {page_size})...")
            pager = await self._client.files.list(config={"page_size": page_size})
            async for file_obj in pager:
                if file_obj:
                    all_files.append(file_obj)
            logger.info(f"Found {len(all_files)} files.")
            return all_files
        except google_exceptions.GoogleAPIError as e:
            logger.error(f"Google API Error listing files: {e}", exc_info=True)
            return []
        except Exception as e:
            logger.error(f"Unexpected error listing files: {e}", exc_info=True)
            return []

    async def delete_file(self, file_id: str) -> bool:
        """Deletes a specific file by its ID.

        Args:
            file_id: The ID of the file to delete (e.g., 'files/abc123xyz').

        Returns:
            True if deletion was successful or file not found, False otherwise.
        """
        if not self._client:
            logger.error("FileManager client not initialized. Cannot delete file.")
            return False
        try:
            logger.warning(f"Attempting to delete file with ID: {file_id}")
            await self._client.files.delete(name=file_id)
            logger.info(f"Successfully deleted file: {file_id}")
            return True
        except google_exceptions.NotFound:
            logger.warning(f"File not found for deletion (already deleted?): {file_id}")
            return True # Treat 'not found' as success in deletion context
        except google_exceptions.GoogleAPIError as e:
            logger.error(f"Google API Error deleting file {file_id}: {e}", exc_info=True)
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting file {file_id}: {e}", exc_info=True)
            return False

    async def get_file_token_count(
        self,
        file_id: str,
        model_name: str = DEFAULT_MODEL_NAME
    ) -> Optional[int]:
        """Gets the token count for a file ID using the Gemini API.

        Args:
            file_id: The ID of the file (e.g., 'files/abc123xyz').
            model_name: The model to use for token counting.

        Returns:
            The token count as an integer, or None if counting fails.
        """
        if not self._client:
            logger.error("FileManager client not initialized. Cannot count tokens.")
            return None

        if not file_id:
             logger.error("File ID is required for token counting.")
             return None

        # Ensure the file is in the ACTIVE state before proceeding
        file_info = await self.get_file(file_id)
        if not file_info or file_info.state != types.FileState.ACTIVE:
            logger.error(f"File {file_id} is not ACTIVE. Cannot count tokens.")
            return None

        file_uri = file_info.uri
        mime_type = file_info.mime_type
        if not file_uri or not mime_type:
            logger.error(f"File {file_id} is missing URI or mime_type. Cannot count tokens.")
            return None

        logger.debug(f"Counting tokens for file: {file_id} (URI: {file_uri}) using model {model_name}")
        try:
            # Construct the part using the actual URI and mime_type from file_info
            file_part = types.Part.from_uri(file_uri=file_uri, mime_type=mime_type)

            # Use client.aio.models.count_tokens as seen in uploader.py
            response = await self._client.models.count_tokens(
                model=model_name,
                contents=[file_part]
            )
            token_count = response.total_tokens
            logger.info(f"Token count for file {file_id}: {token_count}")
            return token_count
        except google_exceptions.GoogleAPIError as e:
            logger.error(f"Google API Error counting tokens for URI {file_id}: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Unexpected error counting tokens for URI {file_id}: {e}", exc_info=True)
            return None

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10),
           retry=retry_if_exception_type(RETRYABLE_API_ERRORS), reraise=True)
    async def upload_file_from_uri(
        self,
        file_uri: str,
        display_name: str,
        mime_type: Optional[str] = None, # User should ideally provide this if known for URIs
        timeout_seconds: int = 300,
        polling_interval_seconds: int = 5
    ) -> Optional[types.File]:
        """
        Registers a file with Gemini using a URI, sets the display name, and polls until ACTIVE.
        Note: The file at the URI must be accessible by Google's services.

        Args:
            file_uri: The URI of the file to be processed by Gemini.
            display_name: A user-friendly display name for the file.
            mime_type: The MIME type of the file. If None, Gemini will attempt to infer it.
            timeout_seconds: Maximum time to wait for file to be processed.
            polling_interval_seconds: How often to check file status.

        Returns:
            File object if successful, None otherwise.
        """
        if not self._client:
            logger.error("Cannot register file from URI: GenAI client not initialized.")
            return None

        if not file_uri or not isinstance(file_uri, str):
            logger.error(f"Invalid file URI provided: {file_uri}")
            return None
        
        if not display_name:
            logger.error("Display name is required when uploading from URI.")
            return None

        effective_display_name = normalize_filename(display_name)

        try:
            logger.info(f"Registering file from URI: {file_uri} with display name: {effective_display_name}")

            # The current Google GenAI SDK doesn't have a direct method to upload from URI
            # We need to implement a workaround - two options:
            # 1. Download the file and then upload using the standard upload method
            # 2. Create a custom API request to upload from URI
            
            logger.info(f"Attempting to handle URI upload for: {file_uri}")
            
            # Option 1: Use an HTTP client to download the file to memory, then upload
            import aiohttp
            import tempfile
            
            # Create a temporary file to store the downloaded content
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{effective_display_name.split('.')[-1] if '.' in effective_display_name else 'bin'}") as temp_file:
                temp_file_path = temp_file.name
            
            try:
                # Download the file from URI
                async with aiohttp.ClientSession() as session:
                    async with session.get(file_uri) as response:
                        if response.status != 200:
                            logger.error(f"Failed to download file from URI: {file_uri}. Status: {response.status}")
                            return None
                        
                        # Save content to temporary file
                        with open(temp_file_path, 'wb') as f:
                            f.write(await response.read())
                
                # Now upload the temporary file
                logger.info(f"Downloaded file from URI to temporary file: {temp_file_path}")
                
                # Create upload config
                upload_config = types.UploadFileConfig(
                    mime_type=mime_type,
                    display_name=effective_display_name
                )
                
                # Upload the temporary file
                upload_response = await self._client.files.upload(
                    file=temp_file_path,
                    config=upload_config
                )
                
                logger.info(f"Successfully uploaded file from URI {file_uri} to Gemini API with ID: {upload_response.name if hasattr(upload_response, 'name') else 'unknown'}")
                return upload_response
                
            except Exception as download_err:
                logger.error(f"Error during URI file download and upload: {download_err}", exc_info=True)
                return None
            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
                        logger.debug(f"Removed temporary file: {temp_file_path}")
                except Exception as cleanup_err:
                    logger.warning(f"Failed to clean up temporary file {temp_file_path}: {cleanup_err}")

            # The following code is unreachable because the try block either returns a response or None
            # Any code here would never execute
            # Removed all unreachable code that was causing lint errors

            # --- Polling Logic (copied and adapted from upload_file) ---
            poll_start_time = time.monotonic()
            while True:
                elapsed_time = time.monotonic() - poll_start_time
                if elapsed_time > timeout_seconds:
                    logger.error(f"Timeout ({timeout_seconds}s) waiting for file {file_id} (from URI: {file_uri}) to become active.")
                    return None

                try:
                    file_info: Optional[types.File] = await self.get_file(file_id)

                    if file_info:
                        state_enum = getattr(file_info, 'state', None)
                        current_state = state_enum.name if state_enum else 'STATE_UNSPECIFIED'
                        logger.debug(f"File {file_id} (from URI: {file_uri}) state: {current_state} (Elapsed: {elapsed_time:.1f}s)")

                        if current_state == "ACTIVE":
                            logger.info(f"File {file_id} ({getattr(file_info, 'display_name', 'N/A')}) from URI {file_uri} is ACTIVE.")
                            return file_info # Success!
                        elif current_state in ["FAILED", "STATE_UNSPECIFIED"]:
                            logger.error(f"File {file_id} (from URI: {file_uri}) processing failed or entered bad state: {current_state}.")
                            return None # Failure
                    else:
                        logger.warning(f"Polling for {file_id} (from URI: {file_uri}): get_file returned None. Retrying soon...")
                
                except google_exceptions.GoogleAPIError as poll_api_err:
                    logger.error(f"API Error during polling for {file_id} (from URI: {file_uri}): {poll_api_err}", exc_info=False)
                except Exception as poll_err:
                    logger.error(f"Unexpected error during polling check for {file_id} (from URI: {file_uri}): {poll_err}", exc_info=False)

                await asyncio.sleep(polling_interval_seconds)
            # --- End of Polling Logic ---

        except google_exceptions.GoogleAPIError as api_error:
            logger.error(f"Google API Error during file registration from URI {file_uri} (display name: {effective_display_name}): {api_error}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Unexpected error during file registration from URI {file_uri} (display name: {effective_display_name}): {e}", exc_info=True)
            return None
