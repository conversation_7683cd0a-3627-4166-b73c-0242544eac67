WEBVTT
Kind: captions
Language: en

00:00:00.360 --> 00:00:04.710 align:start position:0%
 
So<00:00:01.360><c> TAO</c><00:00:01.920><c> is</c><00:00:02.560><c> basically</c><00:00:03.120><c> at</c><00:00:03.360><c> a</c><00:00:03.520><c> high</c><00:00:03.760><c> level</c><00:00:04.240><c> a</c>

00:00:04.710 --> 00:00:04.720 align:start position:0%
So TAO is basically at a high level a
 

00:00:04.720 --> 00:00:09.830 align:start position:0%
So TAO is basically at a high level a
way<00:00:05.640><c> of</c><00:00:06.759><c> people</c><00:00:08.200><c> fine-tuning</c><00:00:09.200><c> their</c><00:00:09.519><c> own</c>

00:00:09.830 --> 00:00:09.840 align:start position:0%
way of people fine-tuning their own
 

00:00:09.840 --> 00:00:13.509 align:start position:0%
way of people fine-tuning their own
models<00:00:10.320><c> for</c><00:00:10.719><c> their</c><00:00:11.120><c> own</c><00:00:11.880><c> domains.</c><00:00:12.880><c> So</c><00:00:13.120><c> there's</c>

00:00:13.509 --> 00:00:13.519 align:start position:0%
models for their own domains. So there's
 

00:00:13.519 --> 00:00:15.669 align:start position:0%
models for their own domains. So there's
always<00:00:13.840><c> been</c><00:00:14.080><c> this</c><00:00:14.320><c> sort</c><00:00:14.559><c> of</c><00:00:14.799><c> stress</c><00:00:15.200><c> between</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
always been this sort of stress between
 

00:00:15.679 --> 00:00:18.070 align:start position:0%
always been this sort of stress between
do<00:00:15.920><c> you</c><00:00:16.080><c> have</c><00:00:16.240><c> one</c><00:00:16.480><c> model</c><00:00:16.720><c> to</c><00:00:16.960><c> rule</c><00:00:17.199><c> them</c><00:00:17.440><c> all</c>

00:00:18.070 --> 00:00:18.080 align:start position:0%
do you have one model to rule them all
 

00:00:18.080 --> 00:00:20.550 align:start position:0%
do you have one model to rule them all
or<00:00:18.480><c> do</c><00:00:18.720><c> you</c><00:00:18.960><c> have</c><00:00:19.439><c> a</c><00:00:19.760><c> sort</c><00:00:19.920><c> of</c><00:00:20.000><c> a</c><00:00:20.160><c> separate</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
or do you have a sort of a separate
 

00:00:20.560 --> 00:00:23.109 align:start position:0%
or do you have a sort of a separate
model<00:00:21.359><c> for</c><00:00:21.840><c> every</c><00:00:22.160><c> single</c><00:00:22.480><c> individual</c>

00:00:23.109 --> 00:00:23.119 align:start position:0%
model for every single individual
 

00:00:23.119 --> 00:00:26.630 align:start position:0%
model for every single individual
domain.<00:00:24.320><c> And</c><00:00:24.960><c> given</c><00:00:25.279><c> that</c><00:00:25.680><c> a</c><00:00:25.920><c> lot</c><00:00:26.080><c> of</c><00:00:26.240><c> people's</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
domain. And given that a lot of people's
 

00:00:26.640 --> 00:00:28.790 align:start position:0%
domain. And given that a lot of people's
data<00:00:27.039><c> is</c><00:00:27.439><c> private,</c><00:00:28.000><c> we</c><00:00:28.320><c> seem</c><00:00:28.560><c> to</c><00:00:28.640><c> be</c>

00:00:28.790 --> 00:00:28.800 align:start position:0%
data is private, we seem to be
 

00:00:28.800 --> 00:00:32.069 align:start position:0%
data is private, we seem to be
gravitating<00:00:29.279><c> towards</c><00:00:29.679><c> a</c><00:00:29.920><c> world</c><00:00:30.720><c> in</c><00:00:31.599><c> which</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
gravitating towards a world in which
 

00:00:32.079 --> 00:00:33.750 align:start position:0%
gravitating towards a world in which
these<00:00:32.399><c> aren't</c><00:00:32.719><c> actually</c><00:00:32.960><c> necessarily</c><00:00:33.520><c> like</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
these aren't actually necessarily like
 

00:00:33.760 --> 00:00:36.870 align:start position:0%
these aren't actually necessarily like
entirely<00:00:34.600><c> orthogonal,</c><00:00:35.600><c> but</c><00:00:36.320><c> they're</c><00:00:36.640><c> very</c>

00:00:36.870 --> 00:00:36.880 align:start position:0%
entirely orthogonal, but they're very
 

00:00:36.880 --> 00:00:40.950 align:start position:0%
entirely orthogonal, but they're very
much<00:00:37.520><c> in</c><00:00:37.920><c> a</c><00:00:38.399><c> world</c><00:00:39.000><c> where</c><00:00:40.000><c> a</c><00:00:40.239><c> lot</c><00:00:40.320><c> of</c><00:00:40.480><c> people</c><00:00:40.719><c> do</c>

00:00:40.950 --> 00:00:40.960 align:start position:0%
much in a world where a lot of people do
 

00:00:40.960 --> 00:00:43.270 align:start position:0%
much in a world where a lot of people do
need<00:00:41.200><c> customized</c><00:00:41.840><c> models</c><00:00:42.239><c> for</c><00:00:42.719><c> their</c><00:00:42.960><c> data</c>

00:00:43.270 --> 00:00:43.280 align:start position:0%
need customized models for their data
 

00:00:43.280 --> 00:00:45.830 align:start position:0%
need customized models for their data
and<00:00:43.520><c> their</c><00:00:43.840><c> use</c><00:00:44.120><c> cases.</c><00:00:45.120><c> And</c><00:00:45.280><c> so</c><00:00:45.440><c> that's</c><00:00:45.680><c> what</c>

00:00:45.830 --> 00:00:45.840 align:start position:0%
and their use cases. And so that's what
 

00:00:45.840 --> 00:00:48.229 align:start position:0%
and their use cases. And so that's what
it<00:00:46.000><c> is.</c><00:00:46.239><c> It's</c><00:00:46.399><c> a</c><00:00:46.559><c> way</c><00:00:46.719><c> of</c><00:00:46.879><c> doing</c><00:00:47.039><c> that.</c><00:00:47.840><c> And</c>

00:00:48.229 --> 00:00:48.239 align:start position:0%
it is. It's a way of doing that. And
 

00:00:48.239 --> 00:00:50.229 align:start position:0%
it is. It's a way of doing that. And
most<00:00:48.559><c> importantly,</c><00:00:49.200><c> it's</c><00:00:49.440><c> a</c><00:00:49.680><c> way</c><00:00:49.760><c> of</c><00:00:50.000><c> doing</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
most importantly, it's a way of doing
 

00:00:50.239 --> 00:00:54.709 align:start position:0%
most importantly, it's a way of doing
that<00:00:51.039><c> without</c><00:00:51.760><c> people</c><00:00:52.239><c> having</c><00:00:53.399><c> labels.</c><00:00:54.399><c> Like</c>

00:00:54.709 --> 00:00:54.719 align:start position:0%
that without people having labels. Like
 

00:00:54.719 --> 00:00:57.110 align:start position:0%
that without people having labels. Like
the<00:00:55.039><c> bane</c><00:00:55.360><c> of</c><00:00:55.760><c> every</c><00:00:56.079><c> single</c><00:00:56.719><c> machine</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
the bane of every single machine
 

00:00:57.120 --> 00:00:59.270 align:start position:0%
the bane of every single machine
learning<00:00:57.520><c> person</c><00:00:58.079><c> trying</c><00:00:58.399><c> to</c><00:00:58.559><c> build</c><00:00:58.719><c> a</c><00:00:58.879><c> custom</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
learning person trying to build a custom
 

00:00:59.280 --> 00:01:01.189 align:start position:0%
learning person trying to build a custom
model<00:00:59.520><c> is,</c><00:00:59.840><c> oh</c><00:01:00.079><c> no,</c><00:01:00.320><c> where</c><00:01:00.480><c> am</c><00:01:00.640><c> I</c><00:01:00.800><c> going</c><00:01:00.879><c> to</c><00:01:01.039><c> get</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
model is, oh no, where am I going to get
 

00:01:01.199 --> 00:01:04.710 align:start position:0%
model is, oh no, where am I going to get
the<00:01:01.359><c> labels</c><00:01:01.920><c> for</c><00:01:02.239><c> this</c><00:01:02.559><c> data?</c>

00:01:04.710 --> 00:01:04.720 align:start position:0%
the labels for this data?
 

00:01:04.720 --> 00:01:05.990 align:start position:0%
the labels for this data?
Yeah,<00:01:04.879><c> these</c><00:01:05.119><c> are</c><00:01:05.280><c> expensive.</c><00:01:05.840><c> The</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
Yeah, these are expensive. The
 

00:01:06.000 --> 00:01:07.789 align:start position:0%
Yeah, these are expensive. The
annotation<00:01:06.560><c> costs</c><00:01:06.960><c> are</c><00:01:07.119><c> going</c><00:01:07.200><c> to</c><00:01:07.280><c> be</c><00:01:07.439><c> super</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
annotation costs are going to be super
 

00:01:07.799 --> 00:01:09.990 align:start position:0%
annotation costs are going to be super
expensive.<00:01:08.799><c> And</c><00:01:08.960><c> we've</c><00:01:09.280><c> heard</c><00:01:09.520><c> this</c><00:01:09.760><c> like</c>

00:01:09.990 --> 00:01:10.000 align:start position:0%
expensive. And we've heard this like
 

00:01:10.000 --> 00:01:12.190 align:start position:0%
expensive. And we've heard this like
time<00:01:10.240><c> and</c><00:01:10.560><c> time</c><00:01:10.799><c> again</c><00:01:11.119><c> from</c><00:01:11.680><c> so</c><00:01:11.920><c> many</c>

00:01:12.190 --> 00:01:12.200 align:start position:0%
time and time again from so many
 

00:01:12.200 --> 00:01:14.950 align:start position:0%
time and time again from so many
customers,<00:01:13.200><c> right?</c><00:01:13.600><c> like</c><00:01:13.840><c> we</c><00:01:14.080><c> were</c><00:01:14.640><c> that's</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
customers, right? like we were that's
 

00:01:14.960 --> 00:01:16.310 align:start position:0%
customers, right? like we were that's
actually<00:01:15.200><c> where</c><00:01:15.520><c> some</c><00:01:15.680><c> of</c><00:01:15.840><c> the</c><00:01:16.000><c> initial</c>

00:01:16.310 --> 00:01:16.320 align:start position:0%
actually where some of the initial
 

00:01:16.320 --> 00:01:18.950 align:start position:0%
actually where some of the initial
motivation<00:01:16.960><c> came</c><00:01:17.200><c> from</c><00:01:18.080><c> right</c><00:01:18.400><c> so</c><00:01:18.560><c> we</c><00:01:18.799><c> were</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
motivation came from right so we were
 

00:01:18.960 --> 00:01:21.030 align:start position:0%
motivation came from right so we were
thinking<00:01:19.360><c> okay</c><00:01:19.600><c> the</c><00:01:19.840><c> initial</c><00:01:20.479><c> version</c><00:01:20.799><c> was</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
thinking okay the initial version was
 

00:01:21.040 --> 00:01:23.590 align:start position:0%
thinking okay the initial version was
like<00:01:21.280><c> okay</c><00:01:21.520><c> what</c><00:01:21.759><c> would</c><00:01:21.920><c> it</c><00:01:22.159><c> take</c><00:01:22.400><c> to</c><00:01:23.360><c> have</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
like okay what would it take to have
 

00:01:23.600 --> 00:01:26.550 align:start position:0%
like okay what would it take to have
some<00:01:23.920><c> kind</c><00:01:24.080><c> of</c><00:01:24.240><c> a</c><00:01:24.560><c> system</c><00:01:24.960><c> where</c><00:01:25.439><c> people</c><00:01:26.159><c> are</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
some kind of a system where people are
 

00:01:26.560 --> 00:01:28.429 align:start position:0%
some kind of a system where people are
deploying<00:01:26.960><c> their</c><00:01:27.200><c> models</c><00:01:27.759><c> collecting</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
deploying their models collecting
 

00:01:28.439 --> 00:01:31.590 align:start position:0%
deploying their models collecting
feedback<00:01:29.439><c> in</c><00:01:29.759><c> some</c><00:01:29.920><c> form</c><00:01:30.240><c> with</c><00:01:30.479><c> these</c><00:01:30.799><c> models</c>

00:01:31.590 --> 00:01:31.600 align:start position:0%
feedback in some form with these models
 

00:01:31.600 --> 00:01:33.429 align:start position:0%
feedback in some form with these models
and<00:01:31.759><c> then</c><00:01:32.000><c> using</c><00:01:32.320><c> those</c><00:01:32.560><c> to</c><00:01:32.880><c> continuously</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
and then using those to continuously
 

00:01:33.439 --> 00:01:37.789 align:start position:0%
and then using those to continuously
improve<00:01:33.840><c> them</c><00:01:34.079><c> through</c><00:01:34.400><c> time</c><00:01:35.280><c> right</c>

00:01:37.789 --> 00:01:37.799 align:start position:0%
improve them through time right
 

00:01:37.799 --> 00:01:40.870 align:start position:0%
improve them through time right
but<00:01:38.799><c> not</c><00:01:39.040><c> everyone</c><00:01:39.520><c> has</c><00:01:39.920><c> access</c><00:01:40.320><c> to</c><00:01:40.560><c> be</c><00:01:40.720><c> able</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
but not everyone has access to be able
 

00:01:40.880 --> 00:01:42.870 align:start position:0%
but not everyone has access to be able
to<00:01:41.040><c> collect</c><00:01:41.680><c> the</c><00:01:41.920><c> sort</c><00:01:42.159><c> of</c><00:01:42.240><c> like</c><00:01:42.479><c> feedback</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
to collect the sort of like feedback
 

00:01:42.880 --> 00:01:46.230 align:start position:0%
to collect the sort of like feedback
back<00:01:43.439><c> and</c><00:01:43.759><c> whatnot</c><00:01:44.240><c> in</c><00:01:44.799><c> real</c><00:01:45.119><c> time.</c><00:01:45.759><c> A</c><00:01:46.000><c> lot</c><00:01:46.079><c> of</c>

00:01:46.230 --> 00:01:46.240 align:start position:0%
back and whatnot in real time. A lot of
 

00:01:46.240 --> 00:01:48.230 align:start position:0%
back and whatnot in real time. A lot of
people<00:01:46.640><c> just</c><00:01:47.200><c> straight</c><00:01:47.439><c> up</c><00:01:47.680><c> they</c><00:01:47.840><c> have</c><00:01:48.000><c> an</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
people just straight up they have an
 

00:01:48.240 --> 00:01:49.990 align:start position:0%
people just straight up they have an
idea<00:01:48.479><c> of</c><00:01:48.720><c> the</c><00:01:48.960><c> tasks</c><00:01:49.280><c> they</c><00:01:49.520><c> want</c><00:01:49.600><c> to</c><00:01:49.759><c> do,</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
idea of the tasks they want to do,
 

00:01:50.000 --> 00:01:51.429 align:start position:0%
idea of the tasks they want to do,
right?<00:01:50.240><c> They</c><00:01:50.399><c> have</c><00:01:50.560><c> some</c><00:01:50.720><c> prompts</c><00:01:51.200><c> and</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
right? They have some prompts and
 

00:01:51.439 --> 00:01:53.270 align:start position:0%
right? They have some prompts and
whatnot,<00:01:52.159><c> but</c><00:01:52.399><c> they</c><00:01:52.640><c> don't</c><00:01:52.880><c> have</c><00:01:53.040><c> a</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
whatnot, but they don't have a
 

00:01:53.280 --> 00:01:55.590 align:start position:0%
whatnot, but they don't have a
particularly<00:01:53.920><c> like</c><00:01:54.240><c> large</c><00:01:54.560><c> data</c><00:01:54.880><c> set</c><00:01:55.119><c> on</c><00:01:55.439><c> how</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
particularly like large data set on how
 

00:01:55.600 --> 00:01:57.730 align:start position:0%
particularly like large data set on how
to<00:01:55.759><c> do</c><00:01:55.920><c> it.</c>

00:01:57.730 --> 00:01:57.740 align:start position:0%
to do it.
 

00:01:57.740 --> 00:02:10.819 align:start position:0%
to do it.
[Music]

