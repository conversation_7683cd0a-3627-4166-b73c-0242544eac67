WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:05.230 align:start position:0%
 
open<00:00:01.560><c> AI</c><00:00:02.560><c> heard</c><00:00:02.760><c> of</c><00:00:02.919><c> them</c><00:00:03.879><c> they</c><00:00:04.080><c> have</c><00:00:04.359><c> this</c><00:00:04.759><c> AI</c>

00:00:05.230 --> 00:00:05.240 align:start position:0%
open AI heard of them they have this AI
 

00:00:05.240 --> 00:00:07.230 align:start position:0%
open AI heard of them they have this AI
product<00:00:05.600><c> that's</c><00:00:05.960><c> kind</c><00:00:06.080><c> of</c><00:00:06.279><c> popular</c><00:00:06.919><c> maybe</c><00:00:07.120><c> you</c>

00:00:07.230 --> 00:00:07.240 align:start position:0%
product that's kind of popular maybe you
 

00:00:07.240 --> 00:00:09.350 align:start position:0%
product that's kind of popular maybe you
heard<00:00:07.399><c> of</c><00:00:07.560><c> that</c><00:00:08.120><c> chat</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
heard of that chat
 

00:00:09.360 --> 00:00:12.870 align:start position:0%
heard of that chat
GPT<00:00:10.360><c> well</c><00:00:10.880><c> they</c><00:00:11.080><c> have</c><00:00:11.400><c> an</c><00:00:11.639><c> API</c><00:00:12.120><c> for</c><00:00:12.320><c> others</c><00:00:12.639><c> to</c>

00:00:12.870 --> 00:00:12.880 align:start position:0%
GPT well they have an API for others to
 

00:00:12.880 --> 00:00:16.109 align:start position:0%
GPT well they have an API for others to
use<00:00:13.280><c> to</c><00:00:13.519><c> leverage</c><00:00:14.000><c> open</c><00:00:14.360><c> AI</c><00:00:14.960><c> products</c><00:00:15.920><c> it's</c>

00:00:16.109 --> 00:00:16.119 align:start position:0%
use to leverage open AI products it's
 

00:00:16.119 --> 00:00:18.950 align:start position:0%
use to leverage open AI products it's
not<00:00:16.400><c> the</c><00:00:17.160><c> best</c><00:00:17.520><c> API</c><00:00:17.960><c> to</c><00:00:18.080><c> use</c><00:00:18.320><c> but</c><00:00:18.480><c> it's</c><00:00:18.720><c> there</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
not the best API to use but it's there
 

00:00:18.960 --> 00:00:21.429 align:start position:0%
not the best API to use but it's there
and<00:00:19.119><c> it's</c><00:00:19.400><c> pretty</c><00:00:19.920><c> popular</c><00:00:20.920><c> if</c><00:00:21.039><c> you've</c><00:00:21.240><c> been</c>

00:00:21.429 --> 00:00:21.439 align:start position:0%
and it's pretty popular if you've been
 

00:00:21.439 --> 00:00:25.109 align:start position:0%
and it's pretty popular if you've been
using<00:00:21.840><c> olama</c><00:00:22.400><c> for</c><00:00:23.199><c> any</c><00:00:23.640><c> amount</c><00:00:24.080><c> a</c><00:00:24.359><c> time</c><00:00:24.960><c> you</c>

00:00:25.109 --> 00:00:25.119 align:start position:0%
using olama for any amount a time you
 

00:00:25.119 --> 00:00:28.029 align:start position:0%
using olama for any amount a time you
know<00:00:25.400><c> there's</c><00:00:25.599><c> an</c><00:00:25.760><c> AMA</c><00:00:26.480><c> Discord</c><00:00:27.480><c> and</c><00:00:27.679><c> if</c><00:00:27.880><c> there</c>

00:00:28.029 --> 00:00:28.039 align:start position:0%
know there's an AMA Discord and if there
 

00:00:28.039 --> 00:00:32.590 align:start position:0%
know there's an AMA Discord and if there
is<00:00:28.400><c> one</c><00:00:28.960><c> question</c><00:00:30.320><c> that</c><00:00:30.480><c> is</c><00:00:30.800><c> more</c><00:00:31.160><c> frequently</c>

00:00:32.590 --> 00:00:32.600 align:start position:0%
is one question that is more frequently
 

00:00:32.600 --> 00:00:35.790 align:start position:0%
is one question that is more frequently
Asked<00:00:33.600><c> than</c><00:00:34.360><c> any</c>

00:00:35.790 --> 00:00:35.800 align:start position:0%
Asked than any
 

00:00:35.800 --> 00:00:39.549 align:start position:0%
Asked than any
other<00:00:36.800><c> it</c><00:00:36.960><c> would</c><00:00:37.200><c> have</c><00:00:37.320><c> to</c><00:00:37.480><c> be</c><00:00:38.280><c> no</c><00:00:39.120><c> question</c>

00:00:39.549 --> 00:00:39.559 align:start position:0%
other it would have to be no question
 

00:00:39.559 --> 00:00:41.229 align:start position:0%
other it would have to be no question
not<00:00:39.920><c> even</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
not even
 

00:00:41.239 --> 00:00:42.950 align:start position:0%
not even
close

00:00:42.950 --> 00:00:42.960 align:start position:0%
close
 

00:00:42.960 --> 00:00:45.029 align:start position:0%
close
it's<00:00:43.960><c> why</c><00:00:44.079><c> is</c><00:00:44.200><c> there</c><00:00:44.360><c> only</c><00:00:44.559><c> one</c><00:00:44.719><c> freaking</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
it's why is there only one freaking
 

00:00:45.039 --> 00:00:47.950 align:start position:0%
it's why is there only one freaking
channel<00:00:45.360><c> on</c><00:00:45.520><c> this</c><00:00:45.760><c> server</c><00:00:46.760><c> but</c><00:00:47.520><c> this</c><00:00:47.640><c> isn't</c>

00:00:47.950 --> 00:00:47.960 align:start position:0%
channel on this server but this isn't
 

00:00:47.960 --> 00:00:50.430 align:start position:0%
channel on this server but this isn't
that<00:00:48.120><c> video</c><00:00:48.960><c> if</c><00:00:49.079><c> you</c><00:00:49.239><c> look</c><00:00:49.360><c> at</c><00:00:49.559><c> the</c><00:00:49.879><c> second</c>

00:00:50.430 --> 00:00:50.440 align:start position:0%
that video if you look at the second
 

00:00:50.440 --> 00:00:52.750 align:start position:0%
that video if you look at the second
most<00:00:50.760><c> popular</c><00:00:51.160><c> question</c><00:00:51.680><c> of</c><00:00:51.840><c> all</c><00:00:52.079><c> time</c><00:00:52.440><c> again</c>

00:00:52.750 --> 00:00:52.760 align:start position:0%
most popular question of all time again
 

00:00:52.760 --> 00:00:54.830 align:start position:0%
most popular question of all time again
it's<00:00:53.000><c> so</c><00:00:53.320><c> obvious</c><00:00:53.719><c> you</c><00:00:54.000><c> only</c><00:00:54.239><c> have</c><00:00:54.359><c> to</c><00:00:54.480><c> be</c><00:00:54.600><c> in</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
it's so obvious you only have to be in
 

00:00:54.840 --> 00:00:58.389 align:start position:0%
it's so obvious you only have to be in
Discord<00:00:55.239><c> for</c><00:00:56.000><c> 30</c><00:00:56.399><c> seconds</c><00:00:56.920><c> it's</c><00:00:57.160><c> about</c><00:00:58.160><c> why</c><00:00:58.239><c> is</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
Discord for 30 seconds it's about why is
 

00:00:58.399 --> 00:01:01.670 align:start position:0%
Discord for 30 seconds it's about why is
my<00:00:58.600><c> GPU</c><00:00:59.039><c> not</c><00:00:59.199><c> being</c><00:00:59.399><c> used</c><00:01:00.199><c> again</c><00:01:00.760><c> wrong</c><00:01:01.320><c> video</c>

00:01:01.670 --> 00:01:01.680 align:start position:0%
my GPU not being used again wrong video
 

00:01:01.680 --> 00:01:04.950 align:start position:0%
my GPU not being used again wrong video
for<00:01:02.039><c> that</c><00:01:02.320><c> question</c><00:01:02.719><c> but</c><00:01:03.239><c> the</c><00:01:03.680><c> third</c><00:01:04.600><c> most</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
for that question but the third most
 

00:01:04.960 --> 00:01:07.230 align:start position:0%
for that question but the third most
popular<00:01:05.360><c> question</c><00:01:05.640><c> is</c><00:01:05.880><c> absolutely</c>

00:01:07.230 --> 00:01:07.240 align:start position:0%
popular question is absolutely
 

00:01:07.240 --> 00:01:11.749 align:start position:0%
popular question is absolutely
unequivocally<00:01:08.240><c> where</c><00:01:08.600><c> is</c><00:01:09.000><c> open</c><00:01:09.439><c> AI</c><00:01:10.240><c> API</c>

00:01:11.749 --> 00:01:11.759 align:start position:0%
unequivocally where is open AI API
 

00:01:11.759 --> 00:01:13.870 align:start position:0%
unequivocally where is open AI API
compatibility<00:01:12.759><c> people</c><00:01:13.080><c> don't</c><00:01:13.320><c> even</c><00:01:13.600><c> know</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
compatibility people don't even know
 

00:01:13.880 --> 00:01:16.749 align:start position:0%
compatibility people don't even know
what<00:01:14.000><c> it</c><00:01:14.320><c> means</c><00:01:14.759><c> and</c><00:01:14.960><c> they</c><00:01:15.119><c> want</c><00:01:15.360><c> it</c><00:01:16.360><c> well</c><00:01:16.640><c> as</c>

00:01:16.749 --> 00:01:16.759 align:start position:0%
what it means and they want it well as
 

00:01:16.759 --> 00:01:19.789 align:start position:0%
what it means and they want it well as
of<00:01:17.040><c> this</c><00:01:17.200><c> release</c><00:01:17.840><c> of</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
of this release of
 

00:01:19.799 --> 00:01:23.510 align:start position:0%
of this release of
0.124<00:01:20.799><c> it</c><00:01:20.960><c> is</c><00:01:21.320><c> right</c><00:01:21.600><c> there</c><00:01:22.079><c> for</c><00:01:22.240><c> you</c><00:01:22.400><c> to</c><00:01:22.560><c> use</c>

00:01:23.510 --> 00:01:23.520 align:start position:0%
0.124 it is right there for you to use
 

00:01:23.520 --> 00:01:26.710 align:start position:0%
0.124 it is right there for you to use
nothing<00:01:24.079><c> special</c><00:01:24.439><c> for</c><00:01:24.680><c> you</c><00:01:24.880><c> to</c><00:01:25.079><c> turn</c><00:01:25.280><c> on</c><00:01:26.240><c> now</c>

00:01:26.710 --> 00:01:26.720 align:start position:0%
nothing special for you to turn on now
 

00:01:26.720 --> 00:01:28.670 align:start position:0%
nothing special for you to turn on now
there<00:01:26.880><c> are</c><00:01:27.159><c> some</c><00:01:27.439><c> features</c><00:01:28.159><c> that</c><00:01:28.320><c> aren't</c>

00:01:28.670 --> 00:01:28.680 align:start position:0%
there are some features that aren't
 

00:01:28.680 --> 00:01:31.590 align:start position:0%
there are some features that aren't
available<00:01:29.200><c> yet</c><00:01:29.640><c> but</c><00:01:30.079><c> for</c><00:01:30.400><c> most</c><00:01:30.720><c> folks</c><00:01:31.159><c> it'll</c>

00:01:31.590 --> 00:01:31.600 align:start position:0%
available yet but for most folks it'll
 

00:01:31.600 --> 00:01:34.230 align:start position:0%
available yet but for most folks it'll
just<00:01:31.920><c> work</c><00:01:32.920><c> so</c><00:01:33.159><c> does</c><00:01:33.320><c> that</c><00:01:33.479><c> mean</c><00:01:33.759><c> that</c><00:01:33.960><c> folks</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
just work so does that mean that folks
 

00:01:34.240 --> 00:01:37.350 align:start position:0%
just work so does that mean that folks
who<00:01:34.439><c> spent</c><00:01:35.159><c> the</c><00:01:35.439><c> time</c><00:01:36.079><c> adding</c><00:01:36.439><c> AMA</c><00:01:36.880><c> to</c><00:01:37.040><c> their</c>

00:01:37.350 --> 00:01:37.360 align:start position:0%
who spent the time adding AMA to their
 

00:01:37.360 --> 00:01:40.670 align:start position:0%
who spent the time adding AMA to their
product<00:01:38.360><c> just</c><00:01:38.680><c> wasted</c><00:01:39.439><c> the</c><00:01:39.600><c> equivalent</c><00:01:40.439><c> of</c><00:01:40.560><c> a</c>

00:01:40.670 --> 00:01:40.680 align:start position:0%
product just wasted the equivalent of a
 

00:01:40.680 --> 00:01:43.710 align:start position:0%
product just wasted the equivalent of a
few<00:01:41.000><c> blueys</c><00:01:42.000><c> no</c><00:01:42.399><c> but</c><00:01:42.640><c> Bluey</c><00:01:43.119><c> is</c><00:01:43.439><c> pretty</c>

00:01:43.710 --> 00:01:43.720 align:start position:0%
few blueys no but Bluey is pretty
 

00:01:43.720 --> 00:01:46.109 align:start position:0%
few blueys no but Bluey is pretty
awesome<00:01:44.119><c> even</c><00:01:44.320><c> if</c><00:01:44.520><c> us</c><00:01:44.840><c> Americans</c><00:01:45.680><c> don't</c><00:01:46.000><c> get</c>

00:01:46.109 --> 00:01:46.119 align:start position:0%
awesome even if us Americans don't get
 

00:01:46.119 --> 00:01:48.709 align:start position:0%
awesome even if us Americans don't get
to<00:01:46.280><c> watch</c><00:01:46.439><c> the</c><00:01:46.600><c> pregnant</c><00:01:46.920><c> Dad</c><00:01:47.560><c> episode</c><00:01:48.560><c> what</c>

00:01:48.709 --> 00:01:48.719 align:start position:0%
to watch the pregnant Dad episode what
 

00:01:48.719 --> 00:01:52.069 align:start position:0%
to watch the pregnant Dad episode what
else<00:01:48.960><c> is</c><00:01:49.119><c> in</c><00:01:49.320><c> this</c><00:01:49.840><c> release</c><00:01:50.840><c> not</c><00:01:51.040><c> much</c><00:01:51.799><c> it</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
else is in this release not much it
 

00:01:52.079 --> 00:01:54.469 align:start position:0%
else is in this release not much it
really<00:01:52.479><c> is</c><00:01:52.759><c> all</c><00:01:52.960><c> about</c><00:01:53.200><c> open</c><00:01:53.479><c> AI</c><00:01:53.880><c> not</c><00:01:54.159><c> that</c><00:01:54.360><c> I</c>

00:01:54.469 --> 00:01:54.479 align:start position:0%
really is all about open AI not that I
 

00:01:54.479 --> 00:01:58.469 align:start position:0%
really is all about open AI not that I
don't<00:01:54.840><c> appreciate</c><00:01:55.640><c> E's</c><00:01:56.640><c> pointer</c><00:01:57.039><c> to</c><00:01:57.240><c> LM</c><00:01:57.680><c> olama</c>

00:01:58.469 --> 00:01:58.479 align:start position:0%
don't appreciate E's pointer to LM olama
 

00:01:58.479 --> 00:01:59.910 align:start position:0%
don't appreciate E's pointer to LM olama
or<00:01:58.680><c> M</c><00:01:58.920><c> raiser's</c><00:01:59.360><c> Cuda</c><00:01:59.640><c> country</c><00:01:59.840><c> cont</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
or M raiser's Cuda country cont
 

00:01:59.920 --> 00:02:04.389 align:start position:0%
or M raiser's Cuda country cont
contribution<00:02:00.479><c> but</c><00:02:01.320><c> let's</c><00:02:01.600><c> talk</c><00:02:02.079><c> open</c><00:02:02.840><c> AI</c><00:02:03.840><c> so</c>

00:02:04.389 --> 00:02:04.399 align:start position:0%
contribution but let's talk open AI so
 

00:02:04.399 --> 00:02:06.830 align:start position:0%
contribution but let's talk open AI so
this<00:02:04.680><c> has</c><00:02:05.000><c> API</c><00:02:05.479><c> in</c><00:02:05.640><c> the</c><00:02:05.799><c> name</c><00:02:06.079><c> of</c><00:02:06.240><c> the</c><00:02:06.360><c> feature</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
this has API in the name of the feature
 

00:02:06.840 --> 00:02:09.229 align:start position:0%
this has API in the name of the feature
but<00:02:07.280><c> let's</c><00:02:07.479><c> start</c><00:02:07.920><c> the</c><00:02:08.319><c> opposite</c><00:02:08.879><c> point</c><00:02:09.080><c> of</c>

00:02:09.229 --> 00:02:09.239 align:start position:0%
but let's start the opposite point of
 

00:02:09.239 --> 00:02:12.030 align:start position:0%
but let's start the opposite point of
view<00:02:10.239><c> the</c><00:02:10.399><c> end</c><00:02:10.679><c> user</c><00:02:11.160><c> who</c><00:02:11.319><c> is</c><00:02:11.520><c> working</c><00:02:11.879><c> with</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
view the end user who is working with
 

00:02:12.040 --> 00:02:15.229 align:start position:0%
view the end user who is working with
chat<00:02:12.319><c> GPT</c><00:02:12.840><c> for</c><00:02:13.000><c> their</c><00:02:13.160><c> regular</c><00:02:13.680><c> jobby</c><00:02:14.120><c> job</c><00:02:15.040><c> I'm</c>

00:02:15.229 --> 00:02:15.239 align:start position:0%
chat GPT for their regular jobby job I'm
 

00:02:15.239 --> 00:02:17.750 align:start position:0%
chat GPT for their regular jobby job I'm
on<00:02:15.360><c> a</c><00:02:15.519><c> Mac</c><00:02:16.080><c> so</c><00:02:16.319><c> I</c><00:02:16.440><c> did</c><00:02:16.599><c> a</c><00:02:16.760><c> search</c><00:02:17.000><c> for</c><00:02:17.319><c> regular</c>

00:02:17.750 --> 00:02:17.760 align:start position:0%
on a Mac so I did a search for regular
 

00:02:17.760 --> 00:02:20.990 align:start position:0%
on a Mac so I did a search for regular
client<00:02:18.040><c> tools</c><00:02:18.760><c> that</c><00:02:18.879><c> use</c><00:02:19.200><c> open</c><00:02:19.519><c> AI</c><00:02:20.200><c> in</c><00:02:20.560><c> some</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
client tools that use open AI in some
 

00:02:21.000 --> 00:02:25.070 align:start position:0%
client tools that use open AI in some
form<00:02:21.400><c> or</c><00:02:21.599><c> another</c><00:02:22.280><c> and</c><00:02:22.560><c> do</c><00:02:22.840><c> not</c><00:02:23.239><c> support</c><00:02:24.080><c> AMA</c>

00:02:25.070 --> 00:02:25.080 align:start position:0%
form or another and do not support AMA
 

00:02:25.080 --> 00:02:27.390 align:start position:0%
form or another and do not support AMA
at<00:02:25.200><c> first</c><00:02:25.400><c> I</c><00:02:25.480><c> was</c><00:02:25.599><c> going</c><00:02:25.760><c> to</c><00:02:25.920><c> use</c><00:02:26.280><c> obsidian</c><00:02:27.280><c> but</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
at first I was going to use obsidian but
 

00:02:27.400 --> 00:02:29.309 align:start position:0%
at first I was going to use obsidian but
it<00:02:27.560><c> seems</c><00:02:27.840><c> that</c><00:02:28.080><c> most</c><00:02:28.239><c> of</c><00:02:28.400><c> the</c><00:02:28.560><c> tools</c><00:02:29.080><c> either</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
it seems that most of the tools either
 

00:02:29.319 --> 00:02:32.110 align:start position:0%
it seems that most of the tools either
support<00:02:29.879><c> AMA</c><00:02:30.319><c> now</c><00:02:30.519><c> or</c><00:02:30.800><c> don't</c><00:02:31.080><c> have</c><00:02:31.200><c> a</c><00:02:31.400><c> way</c><00:02:31.920><c> to</c>

00:02:32.110 --> 00:02:32.120 align:start position:0%
support AMA now or don't have a way to
 

00:02:32.120 --> 00:02:35.309 align:start position:0%
support AMA now or don't have a way to
add<00:02:32.280><c> a</c><00:02:32.440><c> custom</c><00:02:32.760><c> URL</c><00:02:33.599><c> now</c><00:02:33.879><c> why</c><00:02:34.120><c> is</c><00:02:34.400><c> adding</c><00:02:35.120><c> a</c>

00:02:35.309 --> 00:02:35.319 align:start position:0%
add a custom URL now why is adding a
 

00:02:35.319 --> 00:02:39.430 align:start position:0%
add a custom URL now why is adding a
custom<00:02:35.720><c> URL</c><00:02:36.599><c> important</c><00:02:37.239><c> for</c><00:02:37.440><c> chat</c><00:02:38.040><c> gbt</c><00:02:39.040><c> well</c>

00:02:39.430 --> 00:02:39.440 align:start position:0%
custom URL important for chat gbt well
 

00:02:39.440 --> 00:02:42.630 align:start position:0%
custom URL important for chat gbt well
if<00:02:39.640><c> you</c><00:02:39.840><c> are</c><00:02:40.040><c> a</c><00:02:40.239><c> company</c><00:02:40.879><c> and</c><00:02:41.200><c> the</c><00:02:41.560><c> very</c><00:02:42.000><c> real</c>

00:02:42.630 --> 00:02:42.640 align:start position:0%
if you are a company and the very real
 

00:02:42.640 --> 00:02:44.949 align:start position:0%
if you are a company and the very real
privacy<00:02:43.120><c> and</c><00:02:43.400><c> security</c><00:02:43.840><c> issues</c><00:02:44.480><c> with</c><00:02:44.680><c> chat</c>

00:02:44.949 --> 00:02:44.959 align:start position:0%
privacy and security issues with chat
 

00:02:44.959 --> 00:02:47.509 align:start position:0%
privacy and security issues with chat
gbt<00:02:45.640><c> scare</c><00:02:46.040><c> you</c><00:02:46.519><c> then</c><00:02:46.640><c> you</c><00:02:46.760><c> can</c><00:02:46.920><c> host</c><00:02:47.200><c> the</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
gbt scare you then you can host the
 

00:02:47.519 --> 00:02:49.190 align:start position:0%
gbt scare you then you can host the
service<00:02:47.920><c> yourself</c><00:02:48.319><c> on</c><00:02:48.480><c> your</c><00:02:48.640><c> Azure</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
service yourself on your Azure
 

00:02:49.200 --> 00:02:51.910 align:start position:0%
service yourself on your Azure
environment<00:02:50.200><c> and</c><00:02:50.480><c> then</c><00:02:51.239><c> you</c><00:02:51.480><c> probably</c><00:02:51.760><c> want</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
environment and then you probably want
 

00:02:51.920 --> 00:02:54.270 align:start position:0%
environment and then you probably want
your<00:02:52.120><c> users</c><00:02:52.480><c> to</c><00:02:52.720><c> use</c><00:02:53.040><c> that</c><00:02:53.239><c> service</c><00:02:53.959><c> so</c><00:02:54.159><c> that</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
your users to use that service so that
 

00:02:54.280 --> 00:02:55.750 align:start position:0%
your users to use that service so that
your<00:02:54.440><c> company</c><00:02:54.800><c> Secrets</c><00:02:55.200><c> don't</c><00:02:55.400><c> get</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
your company Secrets don't get
 

00:02:55.760 --> 00:02:57.869 align:start position:0%
your company Secrets don't get
discovered<00:02:56.080><c> by</c><00:02:56.200><c> a</c><00:02:56.440><c> reporter</c><00:02:57.440><c> like</c><00:02:57.760><c> what</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
discovered by a reporter like what
 

00:02:57.879 --> 00:03:01.070 align:start position:0%
discovered by a reporter like what
happened<00:02:58.159><c> to</c><00:02:58.760><c> Samsung</c><00:02:59.800><c> I</c><00:02:59.959><c> found</c><00:03:00.239><c> a</c><00:03:00.400><c> cool</c><00:03:00.680><c> tool</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
happened to Samsung I found a cool tool
 

00:03:01.080 --> 00:03:04.830 align:start position:0%
happened to Samsung I found a cool tool
called<00:03:01.760><c> mmac</c><00:03:02.760><c> This</c><00:03:02.920><c> is</c><00:03:03.360><c> a</c><00:03:03.680><c> super</c><00:03:04.080><c> slick</c><00:03:04.480><c> tool</c>

00:03:04.830 --> 00:03:04.840 align:start position:0%
called mmac This is a super slick tool
 

00:03:04.840 --> 00:03:07.949 align:start position:0%
called mmac This is a super slick tool
and<00:03:05.040><c> I</c><00:03:05.200><c> think</c><00:03:05.440><c> I</c><00:03:05.599><c> may</c><00:03:05.840><c> consider</c><00:03:06.319><c> buying</c><00:03:06.840><c> it</c><00:03:07.840><c> but</c>

00:03:07.949 --> 00:03:07.959 align:start position:0%
and I think I may consider buying it but
 

00:03:07.959 --> 00:03:09.990 align:start position:0%
and I think I may consider buying it but
when<00:03:08.080><c> I</c><00:03:08.200><c> first</c><00:03:08.440><c> tried</c><00:03:08.680><c> it</c><00:03:09.040><c> I</c><00:03:09.159><c> could</c><00:03:09.319><c> have</c><00:03:09.560><c> sworn</c>

00:03:09.990 --> 00:03:10.000 align:start position:0%
when I first tried it I could have sworn
 

00:03:10.000 --> 00:03:12.350 align:start position:0%
when I first tried it I could have sworn
that<00:03:10.159><c> ama</c><00:03:10.640><c> wasn't</c><00:03:10.959><c> in</c><00:03:11.080><c> the</c><00:03:11.239><c> supported</c><00:03:11.680><c> list</c><00:03:12.200><c> so</c>

00:03:12.350 --> 00:03:12.360 align:start position:0%
that ama wasn't in the supported list so
 

00:03:12.360 --> 00:03:15.830 align:start position:0%
that ama wasn't in the supported list so
I<00:03:12.480><c> set</c><00:03:12.680><c> up</c><00:03:12.840><c> AMA</c><00:03:13.400><c> as</c><00:03:13.519><c> if</c><00:03:13.640><c> it</c><00:03:13.760><c> were</c><00:03:14.200><c> open</c><00:03:14.640><c> Ai</c><00:03:15.640><c> and</c>

00:03:15.830 --> 00:03:15.840 align:start position:0%
I set up AMA as if it were open Ai and
 

00:03:15.840 --> 00:03:17.190 align:start position:0%
I set up AMA as if it were open Ai and
then<00:03:16.040><c> when</c><00:03:16.159><c> I</c><00:03:16.280><c> started</c><00:03:16.560><c> scripting</c><00:03:17.040><c> this</c>

00:03:17.190 --> 00:03:17.200 align:start position:0%
then when I started scripting this
 

00:03:17.200 --> 00:03:19.470 align:start position:0%
then when I started scripting this
script<00:03:17.840><c> I</c><00:03:17.959><c> saw</c><00:03:18.239><c> that</c><00:03:18.400><c> olama</c><00:03:19.000><c> was</c><00:03:19.120><c> in</c><00:03:19.280><c> the</c>

00:03:19.470 --> 00:03:19.480 align:start position:0%
script I saw that olama was in the
 

00:03:19.480 --> 00:03:23.270 align:start position:0%
script I saw that olama was in the
supported<00:03:20.000><c> list</c><00:03:20.760><c> so</c><00:03:21.720><c> less</c><00:03:22.040><c> useful</c><00:03:22.599><c> for</c><00:03:22.799><c> me</c><00:03:23.120><c> for</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
supported list so less useful for me for
 

00:03:23.280 --> 00:03:26.430 align:start position:0%
supported list so less useful for me for
for<00:03:23.519><c> this</c><00:03:23.959><c> video</c><00:03:24.959><c> well</c><00:03:25.360><c> then</c><00:03:25.720><c> I</c><00:03:25.840><c> found</c><00:03:26.120><c> chat</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
for this video well then I found chat
 

00:03:26.440 --> 00:03:28.789 align:start position:0%
for this video well then I found chat
wizard<00:03:26.799><c> on</c><00:03:27.040><c> GitHub</c><00:03:27.480><c> and</c><00:03:27.640><c> got</c><00:03:27.799><c> it</c><00:03:27.920><c> installed</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
wizard on GitHub and got it installed
 

00:03:28.799 --> 00:03:31.190 align:start position:0%
wizard on GitHub and got it installed
this<00:03:28.959><c> works</c><00:03:29.400><c> great</c><00:03:30.040><c> with</c><00:03:30.159><c> chat</c><00:03:30.400><c> gbt</c><00:03:30.879><c> but</c><00:03:31.000><c> it</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
this works great with chat gbt but it
 

00:03:31.200 --> 00:03:34.429 align:start position:0%
this works great with chat gbt but it
has<00:03:31.480><c> no</c><00:03:31.840><c> idea</c><00:03:32.439><c> what</c><00:03:32.599><c> oama</c><00:03:33.239><c> is</c><00:03:34.000><c> if</c><00:03:34.080><c> you</c><00:03:34.239><c> come</c>

00:03:34.429 --> 00:03:34.439 align:start position:0%
has no idea what oama is if you come
 

00:03:34.439 --> 00:03:36.429 align:start position:0%
has no idea what oama is if you come
into<00:03:34.680><c> settings</c><00:03:35.080><c> you</c><00:03:35.159><c> can</c><00:03:35.319><c> see</c><00:03:35.519><c> a</c><00:03:35.760><c> place</c><00:03:36.000><c> to</c><00:03:36.239><c> put</c>

00:03:36.429 --> 00:03:36.439 align:start position:0%
into settings you can see a place to put
 

00:03:36.439 --> 00:03:39.710 align:start position:0%
into settings you can see a place to put
in<00:03:36.640><c> a</c><00:03:36.799><c> URL</c><00:03:37.599><c> at</c><00:03:37.799><c> first</c><00:03:38.080><c> I</c><00:03:38.200><c> put</c><00:03:38.400><c> in</c>

00:03:39.710 --> 00:03:39.720 align:start position:0%
in a URL at first I put in
 

00:03:39.720 --> 00:03:42.710 align:start position:0%
in a URL at first I put in
HTTP<00:03:40.720><c> logo</c><00:03:41.120><c> host</c><00:03:41.760><c> Port</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
HTTP logo host Port
 

00:03:42.720 --> 00:03:44.470 align:start position:0%
HTTP logo host Port
11434

00:03:44.470 --> 00:03:44.480 align:start position:0%
11434
 

00:03:44.480 --> 00:03:47.110 align:start position:0%
11434
slv1<00:03:45.480><c> as</c><00:03:45.760><c> per</c><00:03:46.000><c> the</c><00:03:46.159><c> olama</c><00:03:46.599><c> release</c>

00:03:47.110 --> 00:03:47.120 align:start position:0%
slv1 as per the olama release
 

00:03:47.120 --> 00:03:50.030 align:start position:0%
slv1 as per the olama release
announcement<00:03:48.120><c> but</c><00:03:48.640><c> this</c><00:03:48.799><c> didn't</c><00:03:49.159><c> work</c>

00:03:50.030 --> 00:03:50.040 align:start position:0%
announcement but this didn't work
 

00:03:50.040 --> 00:03:52.149 align:start position:0%
announcement but this didn't work
thankfully<00:03:50.599><c> I</c><00:03:50.680><c> can</c><00:03:50.879><c> use</c><00:03:51.120><c> the</c><00:03:51.239><c> olama</c><00:03:51.720><c> debug</c>

00:03:52.149 --> 00:03:52.159 align:start position:0%
thankfully I can use the olama debug
 

00:03:52.159 --> 00:03:53.990 align:start position:0%
thankfully I can use the olama debug
environment<00:03:52.640><c> variable</c><00:03:53.400><c> to</c><00:03:53.599><c> figure</c><00:03:53.879><c> out</c>

00:03:53.990 --> 00:03:54.000 align:start position:0%
environment variable to figure out
 

00:03:54.000 --> 00:03:56.830 align:start position:0%
environment variable to figure out
what's<00:03:54.200><c> going</c><00:03:54.360><c> on</c><00:03:55.200><c> there</c><00:03:55.720><c> you</c><00:03:55.879><c> see</c><00:03:56.159><c> that</c><00:03:56.680><c> it</c>

00:03:56.830 --> 00:03:56.840 align:start position:0%
what's going on there you see that it
 

00:03:56.840 --> 00:04:00.470 align:start position:0%
what's going on there you see that it
says<00:03:57.319><c> slv1</c><00:03:58.439><c> slv1</c><00:03:59.439><c> okay</c><00:03:59.680><c> so</c><00:03:59.879><c> go</c><00:04:00.079><c> back</c><00:04:00.239><c> into</c>

00:04:00.470 --> 00:04:00.480 align:start position:0%
says slv1 slv1 okay so go back into
 

00:04:00.480 --> 00:04:03.990 align:start position:0%
says slv1 slv1 okay so go back into
settings<00:04:00.879><c> and</c><00:04:01.079><c> remove</c><00:04:01.480><c> the</c><00:04:01.640><c> V1</c><00:04:02.480><c> from</c><00:04:02.640><c> the</c><00:04:03.000><c> URL</c>

00:04:03.990 --> 00:04:04.000 align:start position:0%
settings and remove the V1 from the URL
 

00:04:04.000 --> 00:04:06.030 align:start position:0%
settings and remove the V1 from the URL
now<00:04:04.239><c> before</c><00:04:04.519><c> I</c><00:04:04.599><c> knew</c><00:04:04.840><c> you</c><00:04:04.959><c> could</c><00:04:05.120><c> add</c><00:04:05.319><c> a</c><00:04:05.480><c> model</c>

00:04:06.030 --> 00:04:06.040 align:start position:0%
now before I knew you could add a model
 

00:04:06.040 --> 00:04:09.429 align:start position:0%
now before I knew you could add a model
I<00:04:06.159><c> just</c><00:04:06.280><c> did</c><00:04:06.519><c> an</c><00:04:06.760><c> olama</c><00:04:07.280><c> CP</c><00:04:08.120><c> new</c><00:04:08.400><c> Hermes</c><00:04:09.120><c> mix</c>

00:04:09.429 --> 00:04:09.439 align:start position:0%
I just did an olama CP new Hermes mix
 

00:04:09.439 --> 00:04:12.710 align:start position:0%
I just did an olama CP new Hermes mix
roll<00:04:09.840><c> to</c><00:04:10.239><c> GPT</c><00:04:10.840><c> 3.5</c><00:04:11.560><c> turbo</c><00:04:12.159><c> tricking</c><00:04:12.560><c> the</c>

00:04:12.710 --> 00:04:12.720 align:start position:0%
roll to GPT 3.5 turbo tricking the
 

00:04:12.720 --> 00:04:15.149 align:start position:0%
roll to GPT 3.5 turbo tricking the
system<00:04:13.000><c> to</c><00:04:13.200><c> think</c><00:04:13.480><c> it</c><00:04:13.599><c> was</c><00:04:13.799><c> really</c><00:04:14.079><c> on</c><00:04:14.400><c> open</c><00:04:14.720><c> Ai</c>

00:04:15.149 --> 00:04:15.159 align:start position:0%
system to think it was really on open Ai
 

00:04:15.159 --> 00:04:17.189 align:start position:0%
system to think it was really on open Ai
and<00:04:15.280><c> that</c><00:04:15.439><c> works</c><00:04:16.320><c> but</c><00:04:16.440><c> it</c><00:04:16.519><c> turns</c><00:04:16.759><c> out</c><00:04:16.959><c> you</c><00:04:17.079><c> can</c>

00:04:17.189 --> 00:04:17.199 align:start position:0%
and that works but it turns out you can
 

00:04:17.199 --> 00:04:20.310 align:start position:0%
and that works but it turns out you can
add<00:04:17.359><c> a</c><00:04:17.519><c> model</c><00:04:17.840><c> in</c><00:04:17.959><c> the</c><00:04:18.160><c> um</c><00:04:18.919><c> uh</c><00:04:19.479><c> you</c><00:04:19.600><c> know</c><00:04:19.799><c> in</c><00:04:20.120><c> ice</c>

00:04:20.310 --> 00:04:20.320 align:start position:0%
add a model in the um uh you know in ice
 

00:04:20.320 --> 00:04:22.950 align:start position:0%
add a model in the um uh you know in ice
block<00:04:20.560><c> view</c><00:04:21.440><c> just</c><00:04:21.639><c> enter</c><00:04:21.919><c> a</c><00:04:22.040><c> model</c><00:04:22.360><c> name</c><00:04:22.840><c> and</c>

00:04:22.950 --> 00:04:22.960 align:start position:0%
block view just enter a model name and
 

00:04:22.960 --> 00:04:25.629 align:start position:0%
block view just enter a model name and
then<00:04:23.080><c> you</c><00:04:23.199><c> can</c><00:04:23.360><c> define</c><00:04:23.720><c> a</c><00:04:23.919><c> cost</c><00:04:24.240><c> if</c><00:04:24.320><c> you</c><00:04:24.639><c> like</c>

00:04:25.629 --> 00:04:25.639 align:start position:0%
then you can define a cost if you like
 

00:04:25.639 --> 00:04:27.350 align:start position:0%
then you can define a cost if you like
there's<00:04:25.960><c> no</c><00:04:26.240><c> checking</c><00:04:26.720><c> that</c><00:04:26.840><c> the</c><00:04:27.000><c> model</c>

00:04:27.350 --> 00:04:27.360 align:start position:0%
there's no checking that the model
 

00:04:27.360 --> 00:04:29.909 align:start position:0%
there's no checking that the model
exists<00:04:27.800><c> so</c><00:04:28.160><c> be</c><00:04:28.320><c> careful</c><00:04:28.759><c> here</c><00:04:29.240><c> then</c><00:04:29.360><c> the</c><00:04:29.639><c> model</c>

00:04:29.909 --> 00:04:29.919 align:start position:0%
exists so be careful here then the model
 

00:04:29.919 --> 00:04:32.270 align:start position:0%
exists so be careful here then the model
just<00:04:30.039><c> works</c><00:04:30.440><c> in</c><00:04:30.560><c> the</c><00:04:30.720><c> chat</c><00:04:31.280><c> check</c><00:04:31.479><c> this</c><00:04:31.639><c> out</c>

00:04:32.270 --> 00:04:32.280 align:start position:0%
just works in the chat check this out
 

00:04:32.280 --> 00:04:35.430 align:start position:0%
just works in the chat check this out
pretty<00:04:32.520><c> nice</c><00:04:33.080><c> huh</c><00:04:34.080><c> now</c><00:04:34.280><c> let's</c><00:04:34.479><c> change</c><00:04:34.840><c> gears</c><00:04:35.280><c> a</c>

00:04:35.430 --> 00:04:35.440 align:start position:0%
pretty nice huh now let's change gears a
 

00:04:35.440 --> 00:04:38.310 align:start position:0%
pretty nice huh now let's change gears a
bit<00:04:35.919><c> and</c><00:04:36.360><c> get</c><00:04:36.520><c> a</c><00:04:36.600><c> little</c><00:04:36.759><c> bit</c><00:04:36.919><c> more</c><00:04:37.320><c> technical</c>

00:04:38.310 --> 00:04:38.320 align:start position:0%
bit and get a little bit more technical
 

00:04:38.320 --> 00:04:41.870 align:start position:0%
bit and get a little bit more technical
a<00:04:38.479><c> little</c><00:04:38.840><c> closer</c><00:04:39.240><c> to</c><00:04:39.440><c> the</c><00:04:39.960><c> developer</c><00:04:40.880><c> Persona</c>

00:04:41.870 --> 00:04:41.880 align:start position:0%
a little closer to the developer Persona
 

00:04:41.880 --> 00:04:45.070 align:start position:0%
a little closer to the developer Persona
autogen<00:04:42.919><c> Studio</c><00:04:43.919><c> this</c><00:04:44.039><c> is</c><00:04:44.160><c> a</c><00:04:44.360><c> pretty</c><00:04:44.560><c> cool</c><00:04:44.880><c> app</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
autogen Studio this is a pretty cool app
 

00:04:45.080 --> 00:04:46.670 align:start position:0%
autogen Studio this is a pretty cool app
from<00:04:45.280><c> the</c><00:04:45.400><c> folks</c><00:04:45.680><c> at</c><00:04:45.840><c> Microsoft</c><00:04:46.320><c> that</c><00:04:46.479><c> brought</c>

00:04:46.670 --> 00:04:46.680 align:start position:0%
from the folks at Microsoft that brought
 

00:04:46.680 --> 00:04:49.110 align:start position:0%
from the folks at Microsoft that brought
us<00:04:46.880><c> autogen</c><00:04:47.840><c> the</c><00:04:47.960><c> idea</c><00:04:48.240><c> of</c><00:04:48.360><c> autogen</c><00:04:48.880><c> is</c><00:04:49.000><c> to</c>

00:04:49.110 --> 00:04:49.120 align:start position:0%
us autogen the idea of autogen is to
 

00:04:49.120 --> 00:04:51.189 align:start position:0%
us autogen the idea of autogen is to
make<00:04:49.240><c> it</c><00:04:49.479><c> super</c><00:04:49.759><c> easy</c><00:04:49.960><c> to</c><00:04:50.120><c> build</c><00:04:50.479><c> agents</c><00:04:51.080><c> that</c>

00:04:51.189 --> 00:04:51.199 align:start position:0%
make it super easy to build agents that
 

00:04:51.199 --> 00:04:53.150 align:start position:0%
make it super easy to build agents that
will<00:04:51.400><c> do</c><00:04:51.680><c> things</c><00:04:51.960><c> for</c><00:04:52.160><c> you</c><00:04:52.440><c> using</c><00:04:52.720><c> the</c><00:04:52.919><c> power</c>

00:04:53.150 --> 00:04:53.160 align:start position:0%
will do things for you using the power
 

00:04:53.160 --> 00:04:56.310 align:start position:0%
will do things for you using the power
of<00:04:53.320><c> AI</c><00:04:54.280><c> this</c><00:04:54.479><c> gets</c><00:04:54.800><c> more</c><00:04:55.120><c> powerful</c><00:04:55.960><c> when</c><00:04:56.120><c> you</c>

00:04:56.310 --> 00:04:56.320 align:start position:0%
of AI this gets more powerful when you
 

00:04:56.320 --> 00:04:58.469 align:start position:0%
of AI this gets more powerful when you
combine<00:04:56.759><c> the</c><00:04:56.919><c> agents</c><00:04:57.280><c> to</c><00:04:57.440><c> work</c><00:04:57.840><c> together</c><00:04:58.320><c> on</c>

00:04:58.469 --> 00:04:58.479 align:start position:0%
combine the agents to work together on
 

00:04:58.479 --> 00:05:01.790 align:start position:0%
combine the agents to work together on
your<00:04:58.759><c> tasks</c><00:04:59.840><c> autogen</c><00:05:00.680><c> and</c><00:05:00.840><c> autogen</c><00:05:01.360><c> Studio</c>

00:05:01.790 --> 00:05:01.800 align:start position:0%
your tasks autogen and autogen Studio
 

00:05:01.800 --> 00:05:05.350 align:start position:0%
your tasks autogen and autogen Studio
work<00:05:02.039><c> with</c><00:05:02.160><c> the</c><00:05:02.320><c> open</c><00:05:02.600><c> AI</c><00:05:03.280><c> API</c><00:05:04.280><c> autogen</c><00:05:04.919><c> is</c><00:05:05.080><c> a</c>

00:05:05.350 --> 00:05:05.360 align:start position:0%
work with the open AI API autogen is a
 

00:05:05.360 --> 00:05:07.870 align:start position:0%
work with the open AI API autogen is a
purely<00:05:05.840><c> developer</c><00:05:06.400><c> product</c><00:05:06.800><c> while</c><00:05:07.039><c> studio</c><00:05:07.759><c> is</c>

00:05:07.870 --> 00:05:07.880 align:start position:0%
purely developer product while studio is
 

00:05:07.880 --> 00:05:12.189 align:start position:0%
purely developer product while studio is
a<00:05:08.039><c> web</c><00:05:08.320><c> guey</c><00:05:09.120><c> that's</c><00:05:09.320><c> a</c><00:05:09.800><c> t</c><00:05:10.199><c> more</c><00:05:11.039><c> friendly</c><00:05:12.039><c> it</c>

00:05:12.189 --> 00:05:12.199 align:start position:0%
a web guey that's a t more friendly it
 

00:05:12.199 --> 00:05:14.990 align:start position:0%
a web guey that's a t more friendly it
has<00:05:12.320><c> been</c><00:05:12.680><c> popular</c><00:05:13.160><c> to</c><00:05:13.360><c> use</c><00:05:13.720><c> orama</c><00:05:14.440><c> with</c><00:05:14.639><c> these</c>

00:05:14.990 --> 00:05:15.000 align:start position:0%
has been popular to use orama with these
 

00:05:15.000 --> 00:05:16.950 align:start position:0%
has been popular to use orama with these
according<00:05:15.280><c> to</c><00:05:15.520><c> the</c><00:05:15.639><c> posts</c><00:05:16.080><c> in</c><00:05:16.240><c> the</c><00:05:16.400><c> Discord</c>

00:05:16.950 --> 00:05:16.960 align:start position:0%
according to the posts in the Discord
 

00:05:16.960 --> 00:05:19.830 align:start position:0%
according to the posts in the Discord
but<00:05:17.400><c> to</c><00:05:17.560><c> do</c><00:05:17.720><c> so</c><00:05:17.919><c> you</c><00:05:18.039><c> have</c><00:05:18.160><c> to</c><00:05:18.280><c> use</c><00:05:18.600><c> light</c><00:05:18.919><c> llm</c>

00:05:19.830 --> 00:05:19.840 align:start position:0%
but to do so you have to use light llm
 

00:05:19.840 --> 00:05:22.550 align:start position:0%
but to do so you have to use light llm
in<00:05:19.960><c> the</c><00:05:20.080><c> middle</c><00:05:21.080><c> and</c><00:05:21.400><c> I</c><00:05:21.560><c> think</c><00:05:21.880><c> you</c><00:05:22.080><c> might</c><00:05:22.319><c> even</c>

00:05:22.550 --> 00:05:22.560 align:start position:0%
in the middle and I think you might even
 

00:05:22.560 --> 00:05:25.070 align:start position:0%
in the middle and I think you might even
have<00:05:22.639><c> to</c><00:05:22.800><c> set</c><00:05:22.960><c> up</c><00:05:23.160><c> a</c><00:05:23.280><c> web</c><00:05:23.560><c> server</c><00:05:24.280><c> well</c><00:05:24.840><c> you</c>

00:05:25.070 --> 00:05:25.080 align:start position:0%
have to set up a web server well you
 

00:05:25.080 --> 00:05:27.270 align:start position:0%
have to set up a web server well you
used<00:05:25.360><c> to</c><00:05:25.520><c> have</c><00:05:25.639><c> to</c><00:05:25.800><c> do</c><00:05:26.000><c> that</c><00:05:26.520><c> now</c><00:05:26.800><c> you</c><00:05:26.919><c> can</c><00:05:27.120><c> just</c>

00:05:27.270 --> 00:05:27.280 align:start position:0%
used to have to do that now you can just
 

00:05:27.280 --> 00:05:30.710 align:start position:0%
used to have to do that now you can just
use<00:05:27.600><c> olama</c><00:05:28.479><c> directly</c><00:05:29.560><c> once</c><00:05:29.680><c> you</c><00:05:29.800><c> get</c><00:05:30.120><c> autogen</c>

00:05:30.710 --> 00:05:30.720 align:start position:0%
use olama directly once you get autogen
 

00:05:30.720 --> 00:05:32.870 align:start position:0%
use olama directly once you get autogen
Studio<00:05:31.160><c> installed</c><00:05:31.840><c> and</c><00:05:32.039><c> up</c><00:05:32.199><c> and</c><00:05:32.319><c> running</c>

00:05:32.870 --> 00:05:32.880 align:start position:0%
Studio installed and up and running
 

00:05:32.880 --> 00:05:33.870 align:start position:0%
Studio installed and up and running
which

00:05:33.870 --> 00:05:33.880 align:start position:0%
which
 

00:05:33.880 --> 00:05:36.510 align:start position:0%
which
is<00:05:34.880><c> easier</c><00:05:35.240><c> said</c><00:05:35.520><c> than</c><00:05:35.680><c> done</c><00:05:35.919><c> because</c><00:05:36.120><c> it's</c>

00:05:36.510 --> 00:05:36.520 align:start position:0%
is easier said than done because it's
 

00:05:36.520 --> 00:05:39.390 align:start position:0%
is easier said than done because it's
python<00:05:37.520><c> go</c><00:05:37.680><c> to</c><00:05:37.880><c> build</c><00:05:38.400><c> and</c><00:05:38.560><c> then</c><00:05:38.840><c> the</c><00:05:38.960><c> models</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
python go to build and then the models
 

00:05:39.400 --> 00:05:41.749 align:start position:0%
python go to build and then the models
Tab<00:05:39.759><c> and</c><00:05:39.880><c> then</c><00:05:40.080><c> click</c><00:05:40.360><c> the</c><00:05:40.560><c> green</c><00:05:41.080><c> new</c><00:05:41.360><c> model</c>

00:05:41.749 --> 00:05:41.759 align:start position:0%
Tab and then click the green new model
 

00:05:41.759 --> 00:05:44.309 align:start position:0%
Tab and then click the green new model
button<00:05:42.720><c> enter</c><00:05:43.000><c> a</c><00:05:43.120><c> model</c><00:05:43.440><c> name</c><00:05:43.840><c> I'll</c><00:05:44.039><c> use</c>

00:05:44.309 --> 00:05:44.319 align:start position:0%
button enter a model name I'll use
 

00:05:44.319 --> 00:05:47.309 align:start position:0%
button enter a model name I'll use
dolphin<00:05:44.720><c> mistol</c><00:05:45.600><c> and</c><00:05:45.720><c> then</c><00:05:45.880><c> the</c><00:05:46.039><c> API</c><00:05:46.479><c> key</c>

00:05:47.309 --> 00:05:47.319 align:start position:0%
dolphin mistol and then the API key
 

00:05:47.319 --> 00:05:49.309 align:start position:0%
dolphin mistol and then the API key
something<00:05:47.680><c> has</c><00:05:47.840><c> to</c><00:05:48.000><c> go</c><00:05:48.120><c> in</c><00:05:48.319><c> here</c><00:05:48.560><c> but</c><00:05:49.039><c> I</c><00:05:49.160><c> don't</c>

00:05:49.309 --> 00:05:49.319 align:start position:0%
something has to go in here but I don't
 

00:05:49.319 --> 00:05:51.510 align:start position:0%
something has to go in here but I don't
think<00:05:49.479><c> it</c><00:05:49.600><c> really</c><00:05:49.800><c> matters</c><00:05:50.240><c> what</c><00:05:51.240><c> then</c>

00:05:51.510 --> 00:05:51.520 align:start position:0%
think it really matters what then
 

00:05:51.520 --> 00:05:53.950 align:start position:0%
think it really matters what then
there's<00:05:51.759><c> the</c><00:05:51.960><c> base</c><00:05:52.240><c> URL</c><00:05:53.120><c> the</c><00:05:53.240><c> default</c><00:05:53.639><c> to</c><00:05:53.800><c> go</c>

00:05:53.950 --> 00:05:53.960 align:start position:0%
there's the base URL the default to go
 

00:05:53.960 --> 00:05:55.270 align:start position:0%
there's the base URL the default to go
here<00:05:54.240><c> is</c>

00:05:55.270 --> 00:05:55.280 align:start position:0%
here is
 

00:05:55.280 --> 00:05:59.909 align:start position:0%
here is
HTP<00:05:56.280><c> localhost</c><00:05:57.240><c> Port</c><00:05:58.080><c> 11434</c><00:05:59.080><c> and</c><00:05:59.199><c> that's</c><00:05:59.319><c> it</c>

00:05:59.909 --> 00:05:59.919 align:start position:0%
HTP localhost Port 11434 and that's it
 

00:05:59.919 --> 00:06:03.830 align:start position:0%
HTP localhost Port 11434 and that's it
try<00:06:00.120><c> out</c><00:06:00.280><c> the</c><00:06:00.400><c> Model</c><00:06:01.280><c> H</c><00:06:02.120><c> well</c><00:06:03.120><c> okay</c><00:06:03.360><c> well</c><00:06:03.680><c> it</c>

00:06:03.830 --> 00:06:03.840 align:start position:0%
try out the Model H well okay well it
 

00:06:03.840 --> 00:06:07.430 align:start position:0%
try out the Model H well okay well it
failed<00:06:04.840><c> okay</c><00:06:05.280><c> let's</c><00:06:05.720><c> add</c><00:06:05.919><c> the</c><00:06:06.160><c> slv1</c><00:06:07.160><c> to</c><00:06:07.280><c> the</c>

00:06:07.430 --> 00:06:07.440 align:start position:0%
failed okay let's add the slv1 to the
 

00:06:07.440 --> 00:06:10.990 align:start position:0%
failed okay let's add the slv1 to the
URL<00:06:07.960><c> try</c><00:06:08.280><c> again</c><00:06:08.599><c> and</c><00:06:09.039><c> H</c><00:06:09.319><c> there</c><00:06:09.440><c> we</c><00:06:09.759><c> go</c><00:06:10.759><c> from</c>

00:06:10.990 --> 00:06:11.000 align:start position:0%
URL try again and H there we go from
 

00:06:11.000 --> 00:06:13.150 align:start position:0%
URL try again and H there we go from
here<00:06:11.199><c> you</c><00:06:11.319><c> can</c><00:06:11.520><c> create</c><00:06:11.840><c> skills</c><00:06:12.599><c> which</c><00:06:12.720><c> are</c>

00:06:13.150 --> 00:06:13.160 align:start position:0%
here you can create skills which are
 

00:06:13.160 --> 00:06:15.550 align:start position:0%
here you can create skills which are
specific<00:06:13.720><c> activities</c><00:06:14.440><c> you</c><00:06:14.599><c> want</c><00:06:14.919><c> your</c><00:06:15.199><c> agent</c>

00:06:15.550 --> 00:06:15.560 align:start position:0%
specific activities you want your agent
 

00:06:15.560 --> 00:06:18.870 align:start position:0%
specific activities you want your agent
to<00:06:15.800><c> do</c><00:06:16.800><c> this</c><00:06:17.000><c> could</c><00:06:17.199><c> be</c><00:06:17.560><c> search</c><00:06:17.919><c> the</c><00:06:18.039><c> internet</c>

00:06:18.870 --> 00:06:18.880 align:start position:0%
to do this could be search the internet
 

00:06:18.880 --> 00:06:22.749 align:start position:0%
to do this could be search the internet
or<00:06:19.360><c> search</c><00:06:19.639><c> a</c><00:06:19.800><c> database</c><00:06:20.800><c> or</c><00:06:21.000><c> parse</c><00:06:21.319><c> a</c><00:06:21.479><c> file</c><00:06:22.319><c> or</c>

00:06:22.749 --> 00:06:22.759 align:start position:0%
or search a database or parse a file or
 

00:06:22.759 --> 00:06:25.270 align:start position:0%
or search a database or parse a file or
well<00:06:23.400><c> whatever</c><00:06:24.400><c> these</c><00:06:24.560><c> skills</c><00:06:24.880><c> are</c><00:06:25.039><c> written</c>

00:06:25.270 --> 00:06:25.280 align:start position:0%
well whatever these skills are written
 

00:06:25.280 --> 00:06:28.390 align:start position:0%
well whatever these skills are written
in<00:06:25.440><c> Python</c><00:06:25.880><c> so</c><00:06:26.240><c> there</c><00:06:26.360><c> is</c><00:06:26.599><c> almost</c><00:06:27.000><c> no</c><00:06:27.520><c> limit</c><00:06:28.199><c> to</c>

00:06:28.390 --> 00:06:28.400 align:start position:0%
in Python so there is almost no limit to
 

00:06:28.400 --> 00:06:30.870 align:start position:0%
in Python so there is almost no limit to
what<00:06:28.520><c> you</c><00:06:28.599><c> can</c><00:06:28.800><c> do</c><00:06:29.800><c> and</c><00:06:30.039><c> then</c><00:06:30.280><c> you</c><00:06:30.479><c> create</c>

00:06:30.870 --> 00:06:30.880 align:start position:0%
what you can do and then you create
 

00:06:30.880 --> 00:06:33.110 align:start position:0%
what you can do and then you create
agents<00:06:31.560><c> that</c><00:06:31.720><c> have</c><00:06:31.840><c> a</c><00:06:32.000><c> system</c><00:06:32.360><c> message</c><00:06:32.960><c> a</c>

00:06:33.110 --> 00:06:33.120 align:start position:0%
agents that have a system message a
 

00:06:33.120 --> 00:06:35.430 align:start position:0%
agents that have a system message a
model<00:06:33.560><c> and</c><00:06:33.919><c> possibly</c><00:06:34.280><c> some</c><00:06:34.520><c> of</c><00:06:34.680><c> the</c><00:06:34.840><c> skills</c>

00:06:35.430 --> 00:06:35.440 align:start position:0%
model and possibly some of the skills
 

00:06:35.440 --> 00:06:37.950 align:start position:0%
model and possibly some of the skills
defined<00:06:36.440><c> then</c><00:06:36.639><c> finally</c><00:06:36.960><c> a</c><00:06:37.080><c> workflow</c><00:06:37.639><c> that</c>

00:06:37.950 --> 00:06:37.960 align:start position:0%
defined then finally a workflow that
 

00:06:37.960 --> 00:06:40.070 align:start position:0%
defined then finally a workflow that
orchestrates<00:06:38.639><c> the</c><00:06:38.800><c> different</c><00:06:39.120><c> agents</c><00:06:39.639><c> to</c><00:06:39.800><c> do</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
orchestrates the different agents to do
 

00:06:40.080 --> 00:06:41.990 align:start position:0%
orchestrates the different agents to do
some<00:06:40.280><c> sort</c><00:06:40.479><c> of</c><00:06:40.880><c> complex</c>

00:06:41.990 --> 00:06:42.000 align:start position:0%
some sort of complex
 

00:06:42.000 --> 00:06:44.710 align:start position:0%
some sort of complex
task<00:06:43.000><c> let's</c><00:06:43.240><c> just</c><00:06:43.360><c> use</c><00:06:43.560><c> one</c><00:06:43.680><c> of</c><00:06:43.800><c> the</c><00:06:43.960><c> examples</c>

00:06:44.710 --> 00:06:44.720 align:start position:0%
task let's just use one of the examples
 

00:06:44.720 --> 00:06:46.950 align:start position:0%
task let's just use one of the examples
the<00:06:44.880><c> general</c><00:06:45.319><c> agent</c><00:06:45.680><c> workflow</c><00:06:46.560><c> and</c><00:06:46.680><c> have</c><00:06:46.800><c> it</c>

00:06:46.950 --> 00:06:46.960 align:start position:0%
the general agent workflow and have it
 

00:06:46.960 --> 00:06:49.550 align:start position:0%
the general agent workflow and have it
run<00:06:47.240><c> through</c><00:06:47.479><c> the</c><00:06:47.680><c> sinewave</c><00:06:48.360><c> example</c><00:06:49.360><c> now</c>

00:06:49.550 --> 00:06:49.560 align:start position:0%
run through the sinewave example now
 

00:06:49.560 --> 00:06:51.830 align:start position:0%
run through the sinewave example now
this<00:06:49.720><c> takes</c><00:06:49.919><c> a</c><00:06:50.120><c> minute</c><00:06:50.400><c> or</c><00:06:50.560><c> two</c><00:06:50.759><c> on</c><00:06:51.039><c> my</c><00:06:51.280><c> machine</c>

00:06:51.830 --> 00:06:51.840 align:start position:0%
this takes a minute or two on my machine
 

00:06:51.840 --> 00:06:53.990 align:start position:0%
this takes a minute or two on my machine
but<00:06:52.400><c> while</c><00:06:52.599><c> it's</c><00:06:52.800><c> working</c><00:06:53.120><c> we</c><00:06:53.240><c> can</c><00:06:53.520><c> verify</c><00:06:53.880><c> its</c>

00:06:53.990 --> 00:06:54.000 align:start position:0%
but while it's working we can verify its
 

00:06:54.000 --> 00:06:56.270 align:start position:0%
but while it's working we can verify its
running<00:06:54.520><c> by</c><00:06:54.680><c> checking</c><00:06:54.919><c> out</c><00:06:55.080><c> the</c><00:06:55.240><c> olama</c><00:06:55.759><c> logs</c>

00:06:56.270 --> 00:06:56.280 align:start position:0%
running by checking out the olama logs
 

00:06:56.280 --> 00:06:59.029 align:start position:0%
running by checking out the olama logs
as<00:06:56.400><c> well</c><00:06:56.560><c> as</c><00:06:56.680><c> the</c><00:06:56.759><c> autogen</c><00:06:57.520><c> logs</c><00:06:58.520><c> this</c><00:06:58.680><c> example</c>

00:06:59.029 --> 00:06:59.039 align:start position:0%
as well as the autogen logs this example
 

00:06:59.039 --> 00:07:01.189 align:start position:0%
as well as the autogen logs this example
is<00:06:59.160><c> right</c><00:06:59.360><c> wrting</c><00:06:59.560><c> a</c><00:06:59.680><c> python</c><00:07:00.000><c> script</c><00:07:00.960><c> it's</c>

00:07:01.189 --> 00:07:01.199 align:start position:0%
is right wrting a python script it's
 

00:07:01.199 --> 00:07:03.270 align:start position:0%
is right wrting a python script it's
simple<00:07:01.680><c> so</c><00:07:02.000><c> any</c><00:07:02.160><c> of</c><00:07:02.280><c> the</c><00:07:02.400><c> common</c><00:07:02.720><c> models</c>

00:07:03.270 --> 00:07:03.280 align:start position:0%
simple so any of the common models
 

00:07:03.280 --> 00:07:05.670 align:start position:0%
simple so any of the common models
should<00:07:03.639><c> handle</c><00:07:03.960><c> it</c><00:07:04.440><c> but</c><00:07:04.599><c> for</c><00:07:04.800><c> more</c><00:07:05.160><c> complex</c>

00:07:05.670 --> 00:07:05.680 align:start position:0%
should handle it but for more complex
 

00:07:05.680 --> 00:07:08.110 align:start position:0%
should handle it but for more complex
tasks<00:07:06.280><c> perhaps</c><00:07:06.560><c> a</c><00:07:06.720><c> larger</c><00:07:07.360><c> more</c><00:07:07.680><c> specific</c>

00:07:08.110 --> 00:07:08.120 align:start position:0%
tasks perhaps a larger more specific
 

00:07:08.120 --> 00:07:11.150 align:start position:0%
tasks perhaps a larger more specific
model<00:07:08.599><c> is</c><00:07:08.800><c> required</c><00:07:09.720><c> or</c><00:07:09.919><c> if</c><00:07:10.080><c> your</c><00:07:10.360><c> workflow</c><00:07:10.960><c> is</c>

00:07:11.150 --> 00:07:11.160 align:start position:0%
model is required or if your workflow is
 

00:07:11.160 --> 00:07:13.710 align:start position:0%
model is required or if your workflow is
quering<00:07:11.639><c> a</c><00:07:11.840><c> database</c><00:07:12.360><c> using</c><00:07:12.639><c> a</c><00:07:12.800><c> skill</c><00:07:13.599><c> and</c>

00:07:13.710 --> 00:07:13.720 align:start position:0%
quering a database using a skill and
 

00:07:13.720 --> 00:07:15.550 align:start position:0%
quering a database using a skill and
then<00:07:13.919><c> interpreting</c><00:07:14.440><c> that</c><00:07:14.560><c> to</c><00:07:14.759><c> English</c><00:07:15.120><c> or</c>

00:07:15.550 --> 00:07:15.560 align:start position:0%
then interpreting that to English or
 

00:07:15.560 --> 00:07:18.670 align:start position:0%
then interpreting that to English or
another<00:07:15.919><c> language</c><00:07:16.800><c> maybe</c><00:07:17.199><c> a</c><00:07:17.440><c> super</c><00:07:17.800><c> lean</c><00:07:18.319><c> and</c>

00:07:18.670 --> 00:07:18.680 align:start position:0%
another language maybe a super lean and
 

00:07:18.680 --> 00:07:21.550 align:start position:0%
another language maybe a super lean and
fast<00:07:19.000><c> model</c><00:07:19.520><c> is</c><00:07:19.639><c> the</c><00:07:19.840><c> approach</c><00:07:20.120><c> to</c><00:07:20.440><c> take</c><00:07:21.440><c> this</c>

00:07:21.550 --> 00:07:21.560 align:start position:0%
fast model is the approach to take this
 

00:07:21.560 --> 00:07:24.430 align:start position:0%
fast model is the approach to take this
is<00:07:21.800><c> super</c><00:07:22.160><c> cool</c><00:07:22.520><c> but</c><00:07:23.080><c> there's</c><00:07:23.759><c> probably</c><00:07:24.199><c> a</c>

00:07:24.430 --> 00:07:24.440 align:start position:0%
is super cool but there's probably a
 

00:07:24.440 --> 00:07:27.350 align:start position:0%
is super cool but there's probably a
warning<00:07:25.080><c> somewhere</c><00:07:26.039><c> not</c><00:07:26.319><c> to</c><00:07:26.520><c> do</c><00:07:26.800><c> this</c><00:07:27.039><c> on</c><00:07:27.199><c> your</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
warning somewhere not to do this on your
 

00:07:27.360 --> 00:07:29.869 align:start position:0%
warning somewhere not to do this on your
local<00:07:27.639><c> machine</c><00:07:28.440><c> it's</c><00:07:28.680><c> creating</c><00:07:29.080><c> code</c><00:07:29.680><c> and</c>

00:07:29.869 --> 00:07:29.879 align:start position:0%
local machine it's creating code and
 

00:07:29.879 --> 00:07:32.670 align:start position:0%
local machine it's creating code and
running<00:07:30.240><c> it</c><00:07:30.560><c> without</c><00:07:30.960><c> your</c><00:07:31.360><c> input</c><00:07:32.360><c> so</c><00:07:32.560><c> it</c>

00:07:32.670 --> 00:07:32.680 align:start position:0%
running it without your input so it
 

00:07:32.680 --> 00:07:35.670 align:start position:0%
running it without your input so it
could<00:07:32.840><c> do</c><00:07:33.000><c> a</c><00:07:33.199><c> lot</c><00:07:33.440><c> on</c><00:07:33.599><c> its</c><00:07:33.720><c> own</c><00:07:34.240><c> now</c><00:07:34.800><c> I</c><00:07:34.960><c> am</c><00:07:35.319><c> not</c>

00:07:35.670 --> 00:07:35.680 align:start position:0%
could do a lot on its own now I am not
 

00:07:35.680 --> 00:07:38.550 align:start position:0%
could do a lot on its own now I am not
worried<00:07:36.160><c> that</c><00:07:36.360><c> this</c><00:07:36.520><c> is</c><00:07:37.000><c> Skynet</c><00:07:37.919><c> or</c><00:07:38.160><c> bringing</c>

00:07:38.550 --> 00:07:38.560 align:start position:0%
worried that this is Skynet or bringing
 

00:07:38.560 --> 00:07:41.869 align:start position:0%
worried that this is Skynet or bringing
on<00:07:38.720><c> the</c><00:07:38.919><c> Doom</c><00:07:39.400><c> of</c><00:07:39.639><c> AGI</c><00:07:40.639><c> but</c><00:07:41.240><c> maybe</c><00:07:41.520><c> it</c><00:07:41.639><c> could</c>

00:07:41.869 --> 00:07:41.879 align:start position:0%
on the Doom of AGI but maybe it could
 

00:07:41.879 --> 00:07:43.909 align:start position:0%
on the Doom of AGI but maybe it could
wipe<00:07:42.080><c> out</c><00:07:42.240><c> your</c><00:07:42.400><c> entire</c><00:07:42.720><c> machine</c><00:07:43.680><c> that's</c>

00:07:43.909 --> 00:07:43.919 align:start position:0%
wipe out your entire machine that's
 

00:07:43.919 --> 00:07:45.749 align:start position:0%
wipe out your entire machine that's
probably<00:07:44.240><c> why</c><00:07:44.479><c> Docker</c><00:07:44.879><c> is</c><00:07:45.039><c> recommended</c><00:07:45.599><c> for</c>

00:07:45.749 --> 00:07:45.759 align:start position:0%
probably why Docker is recommended for
 

00:07:45.759 --> 00:07:48.189 align:start position:0%
probably why Docker is recommended for
the<00:07:45.879><c> python</c><00:07:46.560><c> environment</c><00:07:47.560><c> I</c><00:07:47.680><c> might</c><00:07:47.960><c> actually</c>

00:07:48.189 --> 00:07:48.199 align:start position:0%
the python environment I might actually
 

00:07:48.199 --> 00:07:50.710 align:start position:0%
the python environment I might actually
spin<00:07:48.520><c> up</c><00:07:48.639><c> a</c><00:07:48.759><c> new</c><00:07:48.960><c> machine</c><00:07:49.240><c> on</c><00:07:49.440><c> brev</c><00:07:49.960><c> to</c><00:07:50.280><c> secure</c>

00:07:50.710 --> 00:07:50.720 align:start position:0%
spin up a new machine on brev to secure
 

00:07:50.720 --> 00:07:53.309 align:start position:0%
spin up a new machine on brev to secure
it<00:07:51.720><c> I</c><00:07:51.840><c> think</c><00:07:52.000><c> it</c><00:07:52.080><c> would</c><00:07:52.240><c> be</c><00:07:52.440><c> great</c><00:07:52.879><c> to</c><00:07:53.039><c> be</c><00:07:53.159><c> able</c>

00:07:53.309 --> 00:07:53.319 align:start position:0%
it I think it would be great to be able
 

00:07:53.319 --> 00:07:55.670 align:start position:0%
it I think it would be great to be able
to<00:07:53.440><c> cover</c><00:07:53.680><c> autogen</c><00:07:54.440><c> in</c><00:07:54.599><c> more</c><00:07:54.879><c> detail</c><00:07:55.440><c> in</c><00:07:55.560><c> the</c>

00:07:55.670 --> 00:07:55.680 align:start position:0%
to cover autogen in more detail in the
 

00:07:55.680 --> 00:07:58.230 align:start position:0%
to cover autogen in more detail in the
future<00:07:56.520><c> let</c><00:07:56.680><c> me</c><00:07:56.800><c> know</c><00:07:56.960><c> in</c><00:07:57.080><c> the</c><00:07:57.280><c> comments</c><00:07:57.879><c> if</c>

00:07:58.230 --> 00:07:58.240 align:start position:0%
future let me know in the comments if
 

00:07:58.240 --> 00:08:01.830 align:start position:0%
future let me know in the comments if
that<00:07:58.560><c> is</c><00:07:58.800><c> interesting</c><00:07:59.520><c> to</c><00:07:59.879><c> you</c><00:08:00.879><c> finally</c><00:08:01.599><c> let's</c>

00:08:01.830 --> 00:08:01.840 align:start position:0%
that is interesting to you finally let's
 

00:08:01.840 --> 00:08:04.550 align:start position:0%
that is interesting to you finally let's
look<00:08:02.000><c> at</c><00:08:02.319><c> going</c><00:08:02.840><c> one</c><00:08:03.280><c> level</c><00:08:03.639><c> deeper</c><00:08:04.120><c> you</c><00:08:04.319><c> are</c>

00:08:04.550 --> 00:08:04.560 align:start position:0%
look at going one level deeper you are
 

00:08:04.560 --> 00:08:07.950 align:start position:0%
look at going one level deeper you are
now<00:08:04.800><c> a</c><00:08:05.039><c> full-on</c><00:08:05.720><c> developer</c><00:08:06.720><c> so</c><00:08:07.319><c> we</c><00:08:07.479><c> need</c><00:08:07.680><c> to</c>

00:08:07.950 --> 00:08:07.960 align:start position:0%
now a full-on developer so we need to
 

00:08:07.960 --> 00:08:12.749 align:start position:0%
now a full-on developer so we need to
open<00:08:08.479><c> vs</c><00:08:08.919><c> code</c><00:08:09.639><c> and</c><00:08:09.840><c> now</c><00:08:10.280><c> go</c><00:08:10.520><c> to</c><00:08:10.800><c> it</c><00:08:11.800><c> oh</c><00:08:12.199><c> well</c>

00:08:12.749 --> 00:08:12.759 align:start position:0%
open vs code and now go to it oh well
 

00:08:12.759 --> 00:08:15.070 align:start position:0%
open vs code and now go to it oh well
maybe<00:08:13.000><c> we</c><00:08:13.120><c> need</c><00:08:13.319><c> some</c><00:08:13.520><c> help</c><00:08:14.240><c> so</c><00:08:14.479><c> let's</c><00:08:14.680><c> look</c><00:08:14.879><c> at</c>

00:08:15.070 --> 00:08:15.080 align:start position:0%
maybe we need some help so let's look at
 

00:08:15.080 --> 00:08:18.270 align:start position:0%
maybe we need some help so let's look at
open<00:08:15.479><c> ai's</c><00:08:16.000><c> developer</c><00:08:16.520><c> site</c><00:08:17.199><c> I'll</c><00:08:17.360><c> go</c><00:08:17.479><c> to</c><00:08:17.680><c> chat</c>

00:08:18.270 --> 00:08:18.280 align:start position:0%
open ai's developer site I'll go to chat
 

00:08:18.280 --> 00:08:20.350 align:start position:0%
open ai's developer site I'll go to chat
and<00:08:18.520><c> here's</c><00:08:18.680><c> a</c><00:08:18.879><c> code</c><00:08:19.159><c> sample</c><00:08:19.599><c> ready</c><00:08:19.840><c> for</c><00:08:20.039><c> us</c><00:08:20.159><c> to</c>

00:08:20.350 --> 00:08:20.360 align:start position:0%
and here's a code sample ready for us to
 

00:08:20.360 --> 00:08:23.070 align:start position:0%
and here's a code sample ready for us to
try<00:08:21.240><c> back</c><00:08:21.400><c> to</c><00:08:21.599><c> vs</c><00:08:21.960><c> code</c><00:08:22.280><c> and</c><00:08:22.560><c> I</c><00:08:22.639><c> want</c><00:08:22.759><c> to</c><00:08:22.879><c> use</c>

00:08:23.070 --> 00:08:23.080 align:start position:0%
try back to vs code and I want to use
 

00:08:23.080 --> 00:08:25.589 align:start position:0%
try back to vs code and I want to use
Dino<00:08:23.560><c> for</c><00:08:23.759><c> this</c><00:08:23.879><c> one</c><00:08:24.560><c> so</c><00:08:24.680><c> I'll</c><00:08:24.840><c> do</c><00:08:25.000><c> a</c><00:08:25.159><c> quick</c>

00:08:25.589 --> 00:08:25.599 align:start position:0%
Dino for this one so I'll do a quick
 

00:08:25.599 --> 00:08:28.589 align:start position:0%
Dino for this one so I'll do a quick
Dino<00:08:26.159><c> in</c><00:08:26.400><c> it</c><00:08:27.080><c> which</c><00:08:27.240><c> creates</c><00:08:27.560><c> the</c><00:08:27.720><c> main.ts</c>

00:08:28.589 --> 00:08:28.599 align:start position:0%
Dino in it which creates the main.ts
 

00:08:28.599 --> 00:08:32.070 align:start position:0%
Dino in it which creates the main.ts
file<00:08:29.039><c> and</c><00:08:29.280><c> and</c><00:08:29.440><c> doo.</c><00:08:30.240><c> Json</c><00:08:30.759><c> files</c><00:08:31.759><c> and</c><00:08:31.960><c> then</c>

00:08:32.070 --> 00:08:32.080 align:start position:0%
file and and doo. Json files and then
 

00:08:32.080 --> 00:08:33.630 align:start position:0%
file and and doo. Json files and then
I'll<00:08:32.360><c> replace</c><00:08:32.719><c> the</c><00:08:32.839><c> existing</c><00:08:33.200><c> code</c><00:08:33.440><c> with</c><00:08:33.560><c> the</c>

00:08:33.630 --> 00:08:33.640 align:start position:0%
I'll replace the existing code with the
 

00:08:33.640 --> 00:08:37.029 align:start position:0%
I'll replace the existing code with the
code<00:08:33.880><c> Temple</c><00:08:34.479><c> from</c><00:08:34.760><c> open</c><00:08:35.080><c> AI</c><00:08:36.080><c> Dino</c><00:08:36.560><c> has</c><00:08:36.680><c> a</c>

00:08:37.029 --> 00:08:37.039 align:start position:0%
code Temple from open AI Dino has a
 

00:08:37.039 --> 00:08:39.430 align:start position:0%
code Temple from open AI Dino has a
different<00:08:37.519><c> way</c><00:08:37.719><c> of</c><00:08:37.959><c> dealing</c><00:08:38.640><c> with</c><00:08:38.919><c> packages</c>

00:08:39.430 --> 00:08:39.440 align:start position:0%
different way of dealing with packages
 

00:08:39.440 --> 00:08:41.790 align:start position:0%
different way of dealing with packages
so<00:08:39.919><c> to</c><00:08:40.080><c> use</c><00:08:40.240><c> a</c><00:08:40.440><c> regular</c><00:08:40.760><c> npm</c><00:08:41.279><c> package</c><00:08:41.640><c> just</c>

00:08:41.790 --> 00:08:41.800 align:start position:0%
so to use a regular npm package just
 

00:08:41.800 --> 00:08:44.949 align:start position:0%
so to use a regular npm package just
throw<00:08:42.279><c> npm</c><00:08:43.159><c> colon</c><00:08:43.959><c> at</c><00:08:44.080><c> the</c><00:08:44.200><c> beginning</c><00:08:44.480><c> of</c><00:08:44.600><c> the</c>

00:08:44.949 --> 00:08:44.959 align:start position:0%
throw npm colon at the beginning of the
 

00:08:44.959 --> 00:08:47.430 align:start position:0%
throw npm colon at the beginning of the
import<00:08:45.959><c> now</c><00:08:46.160><c> we</c><00:08:46.279><c> need</c><00:08:46.440><c> to</c><00:08:46.640><c> update</c><00:08:47.120><c> the</c>

00:08:47.430 --> 00:08:47.440 align:start position:0%
import now we need to update the
 

00:08:47.440 --> 00:08:50.190 align:start position:0%
import now we need to update the
Constructor<00:08:48.440><c> first</c><00:08:48.880><c> an</c><00:08:49.040><c> API</c><00:08:49.480><c> key</c><00:08:49.839><c> I'll</c><00:08:50.080><c> just</c>

00:08:50.190 --> 00:08:50.200 align:start position:0%
Constructor first an API key I'll just
 

00:08:50.200 --> 00:08:52.550 align:start position:0%
Constructor first an API key I'll just
set<00:08:50.440><c> this</c><00:08:50.560><c> to</c><00:08:50.720><c> a</c><00:08:50.880><c> llama</c><00:08:51.640><c> and</c><00:08:51.800><c> next</c><00:08:52.000><c> is</c><00:08:52.160><c> the</c><00:08:52.279><c> base</c>

00:08:52.550 --> 00:08:52.560 align:start position:0%
set this to a llama and next is the base
 

00:08:52.560 --> 00:08:55.990 align:start position:0%
set this to a llama and next is the base
URL<00:08:53.560><c> and</c><00:08:53.880><c> that's</c><00:08:54.240><c> going</c><00:08:54.399><c> to</c><00:08:54.560><c> be</c><00:08:54.880><c> logo</c><00:08:55.320><c> host</c><00:08:55.880><c> and</c>

00:08:55.990 --> 00:08:56.000 align:start position:0%
URL and that's going to be logo host and
 

00:08:56.000 --> 00:08:57.389 align:start position:0%
URL and that's going to be logo host and
the<00:08:56.200><c> port</c>

00:08:57.389 --> 00:08:57.399 align:start position:0%
the port
 

00:08:57.399 --> 00:09:00.150 align:start position:0%
the port
slv1<00:08:58.399><c> that</c><00:08:58.519><c> should</c><00:08:58.720><c> be</c><00:08:58.880><c> all</c><00:08:59.240><c> we</c><00:08:59.360><c> need</c><00:08:59.880><c> so</c><00:09:00.040><c> we</c>

00:09:00.150 --> 00:09:00.160 align:start position:0%
slv1 that should be all we need so we
 

00:09:00.160 --> 00:09:03.190 align:start position:0%
slv1 that should be all we need so we
can<00:09:00.320><c> run</c><00:09:00.560><c> it</c><00:09:01.560><c> when</c><00:09:01.720><c> you</c><00:09:01.880><c> use</c><00:09:02.120><c> Dino</c><00:09:02.720><c> you</c><00:09:02.839><c> need</c><00:09:03.040><c> to</c>

00:09:03.190 --> 00:09:03.200 align:start position:0%
can run it when you use Dino you need to
 

00:09:03.200 --> 00:09:06.350 align:start position:0%
can run it when you use Dino you need to
be<00:09:03.800><c> intentional</c><00:09:04.760><c> about</c><00:09:04.959><c> the</c><00:09:05.120><c> resources</c><00:09:05.720><c> used</c>

00:09:06.350 --> 00:09:06.360 align:start position:0%
be intentional about the resources used
 

00:09:06.360 --> 00:09:09.670 align:start position:0%
be intentional about the resources used
so<00:09:06.680><c> Dino</c><00:09:07.160><c> Run</c><00:09:07.920><c> allow</c><00:09:08.360><c> net</c>

00:09:09.670 --> 00:09:09.680 align:start position:0%
so Dino Run allow net
 

00:09:09.680 --> 00:09:12.150 align:start position:0%
so Dino Run allow net
main.ts<00:09:10.680><c> and</c><00:09:10.920><c> there</c><00:09:11.079><c> is</c><00:09:11.200><c> our</c><00:09:11.440><c> message</c><00:09:11.720><c> from</c>

00:09:12.150 --> 00:09:12.160 align:start position:0%
main.ts and there is our message from
 

00:09:12.160 --> 00:09:14.870 align:start position:0%
main.ts and there is our message from
AMA<00:09:13.160><c> and</c><00:09:13.320><c> we</c><00:09:13.440><c> can</c><00:09:13.600><c> watch</c><00:09:13.839><c> the</c><00:09:14.000><c> logs</c><00:09:14.399><c> to</c><00:09:14.560><c> ensure</c>

00:09:14.870 --> 00:09:14.880 align:start position:0%
AMA and we can watch the logs to ensure
 

00:09:14.880 --> 00:09:17.310 align:start position:0%
AMA and we can watch the logs to ensure
that<00:09:15.000><c> it</c><00:09:15.320><c> really</c><00:09:15.760><c> is</c><00:09:16.079><c> olama</c><00:09:16.560><c> running</c><00:09:17.040><c> this</c>

00:09:17.310 --> 00:09:17.320 align:start position:0%
that it really is olama running this
 

00:09:17.320 --> 00:09:20.110 align:start position:0%
that it really is olama running this
open<00:09:17.640><c> Ai</c><00:09:18.120><c> call</c><00:09:19.120><c> to</c><00:09:19.320><c> make</c><00:09:19.480><c> it</c><00:09:19.600><c> a</c><00:09:19.680><c> little</c><00:09:19.880><c> more</c>

00:09:20.110 --> 00:09:20.120 align:start position:0%
open Ai call to make it a little more
 

00:09:20.120 --> 00:09:22.710 align:start position:0%
open Ai call to make it a little more
pretty<00:09:20.440><c> we</c><00:09:20.560><c> can</c><00:09:20.720><c> just</c><00:09:20.959><c> print</c><00:09:21.160><c> out</c><00:09:21.440><c> the</c><00:09:21.720><c> message</c>

00:09:22.710 --> 00:09:22.720 align:start position:0%
pretty we can just print out the message
 

00:09:22.720 --> 00:09:26.030 align:start position:0%
pretty we can just print out the message
content<00:09:23.720><c> so</c><00:09:24.200><c> we</c><00:09:24.320><c> have</c><00:09:24.440><c> seen</c><00:09:24.720><c> the</c><00:09:24.839><c> new</c><00:09:25.079><c> open</c><00:09:25.360><c> AI</c>

00:09:26.030 --> 00:09:26.040 align:start position:0%
content so we have seen the new open AI
 

00:09:26.040 --> 00:09:28.509 align:start position:0%
content so we have seen the new open AI
API<00:09:26.519><c> compatibility</c><00:09:27.200><c> in</c><00:09:27.399><c> ama</c><00:09:27.959><c> and</c><00:09:28.079><c> we</c><00:09:28.200><c> saw</c><00:09:28.399><c> it</c>

00:09:28.509 --> 00:09:28.519 align:start position:0%
API compatibility in ama and we saw it
 

00:09:28.519 --> 00:09:31.470 align:start position:0%
API compatibility in ama and we saw it
from<00:09:28.720><c> three</c><00:09:29.240><c> different</c><00:09:29.480><c> perspectives</c><00:09:30.320><c> a</c><00:09:30.480><c> user</c>

00:09:31.470 --> 00:09:31.480 align:start position:0%
from three different perspectives a user
 

00:09:31.480 --> 00:09:34.870 align:start position:0%
from three different perspectives a user
a<00:09:31.680><c> power</c><00:09:31.920><c> user</c><00:09:32.640><c> and</c><00:09:32.760><c> a</c><00:09:33.279><c> Dev</c><00:09:34.279><c> I</c><00:09:34.399><c> think</c><00:09:34.600><c> this</c><00:09:34.720><c> is</c>

00:09:34.870 --> 00:09:34.880 align:start position:0%
a power user and a Dev I think this is
 

00:09:34.880 --> 00:09:37.750 align:start position:0%
a power user and a Dev I think this is
pretty<00:09:35.120><c> cool</c><00:09:35.839><c> using</c><00:09:36.200><c> the</c><00:09:36.320><c> olama</c><00:09:36.839><c> API</c><00:09:37.399><c> directly</c>

00:09:37.750 --> 00:09:37.760 align:start position:0%
pretty cool using the olama API directly
 

00:09:37.760 --> 00:09:39.590 align:start position:0%
pretty cool using the olama API directly
is<00:09:37.880><c> going</c><00:09:38.040><c> to</c><00:09:38.160><c> be</c><00:09:38.320><c> easier</c><00:09:38.839><c> more</c><00:09:39.079><c> performant</c>

00:09:39.590 --> 00:09:39.600 align:start position:0%
is going to be easier more performant
 

00:09:39.600 --> 00:09:41.550 align:start position:0%
is going to be easier more performant
and<00:09:39.760><c> generally</c><00:09:40.200><c> better</c><00:09:40.560><c> but</c><00:09:40.920><c> especially</c><00:09:41.399><c> with</c>

00:09:41.550 --> 00:09:41.560 align:start position:0%
and generally better but especially with
 

00:09:41.560 --> 00:09:44.230 align:start position:0%
and generally better but especially with
the<00:09:41.880><c> official</c><00:09:42.720><c> JS</c><00:09:43.160><c> and</c><00:09:43.360><c> python</c><00:09:43.680><c> libraries</c><00:09:44.120><c> as</c>

00:09:44.230 --> 00:09:44.240 align:start position:0%
the official JS and python libraries as
 

00:09:44.240 --> 00:09:45.630 align:start position:0%
the official JS and python libraries as
well<00:09:44.360><c> as</c><00:09:44.680><c> you</c><00:09:44.760><c> know</c><00:09:44.880><c> all</c><00:09:45.040><c> the</c><00:09:45.200><c> community</c>

00:09:45.630 --> 00:09:45.640 align:start position:0%
well as you know all the community
 

00:09:45.640 --> 00:09:48.389 align:start position:0%
well as you know all the community
created<00:09:46.000><c> libraries</c><00:09:46.399><c> for</c><00:09:46.800><c> for</c><00:09:47.040><c> rust</c><00:09:47.760><c> and</c><00:09:47.920><c> Ruby</c>

00:09:48.389 --> 00:09:48.399 align:start position:0%
created libraries for for rust and Ruby
 

00:09:48.399 --> 00:09:51.829 align:start position:0%
created libraries for for rust and Ruby
and<00:09:48.880><c> and</c><00:09:49.079><c> R</c><00:09:49.560><c> and</c><00:09:49.760><c> Swift</c><00:09:50.240><c> and</c><00:09:50.399><c> so</c><00:09:50.600><c> many</c><00:09:50.880><c> others</c>

00:09:51.829 --> 00:09:51.839 align:start position:0%
and and R and Swift and so many others
 

00:09:51.839 --> 00:09:53.630 align:start position:0%
and and R and Swift and so many others
but<00:09:51.959><c> if</c><00:09:52.120><c> there's</c><00:09:52.320><c> an</c><00:09:52.560><c> existing</c><00:09:52.920><c> tool</c><00:09:53.480><c> that</c>

00:09:53.630 --> 00:09:53.640 align:start position:0%
but if there's an existing tool that
 

00:09:53.640 --> 00:09:57.190 align:start position:0%
but if there's an existing tool that
uses<00:09:54.000><c> the</c><00:09:54.160><c> open</c><00:09:54.519><c> AI</c><00:09:55.000><c> API</c><00:09:55.560><c> today</c><00:09:56.560><c> and</c><00:09:56.720><c> lets</c><00:09:56.959><c> you</c>

00:09:57.190 --> 00:09:57.200 align:start position:0%
uses the open AI API today and lets you
 

00:09:57.200 --> 00:09:59.389 align:start position:0%
uses the open AI API today and lets you
set<00:09:57.480><c> the</c><00:09:57.600><c> base</c><00:09:57.880><c> URL</c><00:09:58.640><c> then</c><00:09:58.839><c> this</c><00:09:58.959><c> is</c><00:09:59.120><c> is</c><00:09:59.240><c> going</c>

00:09:59.389 --> 00:09:59.399 align:start position:0%
set the base URL then this is is going
 

00:09:59.399 --> 00:10:02.910 align:start position:0%
set the base URL then this is is going
to<00:09:59.519><c> be</c><00:09:59.839><c> super</c><00:10:00.240><c> powerful</c><00:10:00.959><c> for</c><00:10:01.440><c> you</c><00:10:02.440><c> let</c><00:10:02.600><c> me</c><00:10:02.720><c> know</c>

00:10:02.910 --> 00:10:02.920 align:start position:0%
to be super powerful for you let me know
 

00:10:02.920 --> 00:10:04.389 align:start position:0%
to be super powerful for you let me know
if<00:10:03.040><c> there's</c><00:10:03.440><c> anything</c><00:10:03.720><c> else</c><00:10:03.920><c> you'd</c><00:10:04.160><c> like</c><00:10:04.279><c> to</c>

00:10:04.389 --> 00:10:04.399 align:start position:0%
if there's anything else you'd like to
 

00:10:04.399 --> 00:10:07.190 align:start position:0%
if there's anything else you'd like to
see<00:10:04.760><c> on</c><00:10:04.920><c> this</c><00:10:05.120><c> channel</c><00:10:06.079><c> it</c><00:10:06.200><c> seems</c><00:10:06.560><c> that</c><00:10:06.760><c> a</c><00:10:06.959><c> lot</c>

00:10:07.190 --> 00:10:07.200 align:start position:0%
see on this channel it seems that a lot
 

00:10:07.200 --> 00:10:08.590 align:start position:0%
see on this channel it seems that a lot
of<00:10:07.360><c> you</c><00:10:07.519><c> have</c><00:10:07.640><c> been</c><00:10:07.839><c> subscribing</c><00:10:08.320><c> to</c><00:10:08.480><c> the</c>

00:10:08.590 --> 00:10:08.600 align:start position:0%
of you have been subscribing to the
 

00:10:08.600 --> 00:10:12.110 align:start position:0%
of you have been subscribing to the
channel<00:10:08.959><c> and</c><00:10:09.160><c> I</c><00:10:09.480><c> love</c><00:10:10.440><c> every</c><00:10:10.959><c> single</c><00:10:11.440><c> one</c><00:10:11.959><c> of</c>

00:10:12.110 --> 00:10:12.120 align:start position:0%
channel and I love every single one of
 

00:10:12.120 --> 00:10:14.870 align:start position:0%
channel and I love every single one of
those<00:10:12.320><c> subs</c><00:10:13.200><c> it</c><00:10:13.320><c> is</c><00:10:13.519><c> so</c><00:10:13.839><c> exciting</c><00:10:14.440><c> to</c><00:10:14.640><c> watch</c>

00:10:14.870 --> 00:10:14.880 align:start position:0%
those subs it is so exciting to watch
 

00:10:14.880 --> 00:10:17.110 align:start position:0%
those subs it is so exciting to watch
how<00:10:15.120><c> many</c><00:10:15.320><c> of</c><00:10:15.480><c> you</c><00:10:16.040><c> are</c><00:10:16.279><c> interested</c><00:10:16.720><c> in</c><00:10:16.880><c> me</c>

00:10:17.110 --> 00:10:17.120 align:start position:0%
how many of you are interested in me
 

00:10:17.120 --> 00:10:19.750 align:start position:0%
how many of you are interested in me
creating<00:10:17.519><c> more</c><00:10:17.760><c> videos</c><00:10:18.600><c> so</c><00:10:18.839><c> keep</c><00:10:19.000><c> it</c><00:10:19.160><c> up</c><00:10:19.519><c> and</c>

00:10:19.750 --> 00:10:19.760 align:start position:0%
creating more videos so keep it up and
 

00:10:19.760 --> 00:10:22.949 align:start position:0%
creating more videos so keep it up and
thank<00:10:19.959><c> you</c><00:10:20.839><c> so</c><00:10:21.079><c> much</c><00:10:21.600><c> for</c><00:10:21.800><c> doing</c><00:10:22.120><c> that</c><00:10:22.839><c> and</c>

00:10:22.949 --> 00:10:22.959 align:start position:0%
thank you so much for doing that and
 

00:10:22.959 --> 00:10:28.030 align:start position:0%
thank you so much for doing that and
thanks<00:10:23.200><c> so</c><00:10:23.320><c> much</c><00:10:23.440><c> for</c><00:10:23.600><c> watching</c><00:10:23.959><c> this</c><00:10:24.079><c> one</c>

00:10:28.030 --> 00:10:28.040 align:start position:0%
 
 

00:10:28.040 --> 00:10:44.949 align:start position:0%
 
goodbye

00:10:44.949 --> 00:10:44.959 align:start position:0%
 
 

00:10:44.959 --> 00:10:47.959 align:start position:0%
 
ah

