WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.270 align:start position:0%
 
well<00:00:00.160><c> if</c><00:00:00.199><c> you</c><00:00:00.320><c> like</c><00:00:00.480><c> me</c><00:00:00.719><c> and</c><00:00:00.840><c> you</c><00:00:00.919><c> started</c><00:00:01.160><c> out</c>

00:00:01.270 --> 00:00:01.280 align:start position:0%
well if you like me and you started out
 

00:00:01.280 --> 00:00:03.030 align:start position:0%
well if you like me and you started out
using<00:00:01.520><c> chat</c><00:00:01.800><c> apt</c><00:00:02.320><c> then</c><00:00:02.440><c> you're</c><00:00:02.560><c> used</c><00:00:02.760><c> to</c><00:00:02.879><c> a</c>

00:00:03.030 --> 00:00:03.040 align:start position:0%
using chat apt then you're used to a
 

00:00:03.040 --> 00:00:05.390 align:start position:0%
using chat apt then you're used to a
text<00:00:03.360><c> generation</c><00:00:03.919><c> AI</c><00:00:04.240><c> model</c><00:00:04.680><c> and</c><00:00:04.920><c> while</c><00:00:05.120><c> open</c>

00:00:05.390 --> 00:00:05.400 align:start position:0%
text generation AI model and while open
 

00:00:05.400 --> 00:00:06.590 align:start position:0%
text generation AI model and while open
AI<00:00:05.680><c> is</c><00:00:05.759><c> great</c><00:00:06.000><c> even</c><00:00:06.200><c> with</c><00:00:06.319><c> some</c><00:00:06.440><c> of</c><00:00:06.520><c> the</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
AI is great even with some of the
 

00:00:06.600 --> 00:00:07.990 align:start position:0%
AI is great even with some of the
updates<00:00:06.919><c> that</c><00:00:07.040><c> they</c><00:00:07.160><c> have</c><00:00:07.399><c> at</c><00:00:07.480><c> some</c><00:00:07.640><c> point</c><00:00:07.799><c> you</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
updates that they have at some point you
 

00:00:08.000 --> 00:00:09.230 align:start position:0%
updates that they have at some point you
might<00:00:08.080><c> want</c><00:00:08.200><c> to</c><00:00:08.280><c> pay</c><00:00:08.440><c> the</c><00:00:08.599><c> subscription</c><00:00:09.040><c> to</c>

00:00:09.230 --> 00:00:09.240 align:start position:0%
might want to pay the subscription to
 

00:00:09.240 --> 00:00:11.830 align:start position:0%
might want to pay the subscription to
get<00:00:09.480><c> custom</c><00:00:09.760><c> chat</c><00:00:10.040><c> gpts</c><00:00:10.639><c> or</c><00:00:10.800><c> use</c><00:00:11.040><c> their</c><00:00:11.280><c> API</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
get custom chat gpts or use their API
 

00:00:11.840 --> 00:00:13.270 align:start position:0%
get custom chat gpts or use their API
and<00:00:12.000><c> this</c><00:00:12.160><c> will</c><00:00:12.360><c> cost</c><00:00:12.599><c> money</c><00:00:12.840><c> so</c><00:00:12.960><c> how</c><00:00:13.080><c> can</c><00:00:13.160><c> we</c>

00:00:13.270 --> 00:00:13.280 align:start position:0%
and this will cost money so how can we
 

00:00:13.280 --> 00:00:15.350 align:start position:0%
and this will cost money so how can we
try<00:00:13.559><c> other</c><00:00:13.799><c> models</c><00:00:14.400><c> such</c><00:00:14.559><c> as</c><00:00:14.719><c> text</c><00:00:14.960><c> to</c><00:00:15.120><c> image</c>

00:00:15.350 --> 00:00:15.360 align:start position:0%
try other models such as text to image
 

00:00:15.360 --> 00:00:17.590 align:start position:0%
try other models such as text to image
to<00:00:15.519><c> generate</c><00:00:15.839><c> images</c><00:00:16.440><c> or</c><00:00:16.720><c> text</c><00:00:16.960><c> to</c><00:00:17.160><c> speech</c>

00:00:17.590 --> 00:00:17.600 align:start position:0%
to generate images or text to speech
 

00:00:17.600 --> 00:00:18.710 align:start position:0%
to generate images or text to speech
well<00:00:17.680><c> this</c><00:00:17.800><c> is</c><00:00:17.880><c> where</c><00:00:18.000><c> hugging</c><00:00:18.320><c> face</c><00:00:18.480><c> can</c><00:00:18.600><c> come</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
well this is where hugging face can come
 

00:00:18.720 --> 00:00:20.670 align:start position:0%
well this is where hugging face can come
in<00:00:18.800><c> to</c><00:00:18.960><c> help</c><00:00:19.119><c> you</c><00:00:19.439><c> let</c><00:00:19.600><c> me</c><00:00:19.800><c> explain</c><00:00:20.359><c> hugging</c>

00:00:20.670 --> 00:00:20.680 align:start position:0%
in to help you let me explain hugging
 

00:00:20.680 --> 00:00:23.189 align:start position:0%
in to help you let me explain hugging
face<00:00:20.920><c> can</c><00:00:21.039><c> introduce</c><00:00:21.480><c> you</c><00:00:21.720><c> to</c><00:00:22.000><c> so</c><00:00:22.199><c> many</c><00:00:22.480><c> models</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
face can introduce you to so many models
 

00:00:23.199 --> 00:00:26.230 align:start position:0%
face can introduce you to so many models
and<00:00:23.439><c> actually</c><00:00:23.880><c> it</c><00:00:24.080><c> has</c><00:00:24.240><c> over</c><00:00:24.840><c> 680,000</c><00:00:25.840><c> models</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
and actually it has over 680,000 models
 

00:00:26.240 --> 00:00:27.750 align:start position:0%
and actually it has over 680,000 models
right<00:00:26.359><c> now</c><00:00:26.560><c> and</c><00:00:26.679><c> we</c><00:00:26.760><c> can</c><00:00:26.840><c> use</c><00:00:27.039><c> model</c><00:00:27.320><c> types</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
right now and we can use model types
 

00:00:27.760 --> 00:00:30.189 align:start position:0%
right now and we can use model types
such<00:00:27.920><c> as</c><00:00:28.080><c> text</c><00:00:28.320><c> to</c><00:00:28.480><c> image</c><00:00:29.039><c> image</c><00:00:29.320><c> to</c><00:00:29.519><c> text</c><00:00:30.000><c> text</c>

00:00:30.189 --> 00:00:30.199 align:start position:0%
such as text to image image to text text
 

00:00:30.199 --> 00:00:32.589 align:start position:0%
such as text to image image to text text
to<00:00:30.400><c> speech</c><00:00:30.880><c> and</c><00:00:31.039><c> so</c><00:00:31.279><c> forth</c><00:00:31.519><c> just</c><00:00:31.679><c> from</c><00:00:31.840><c> 2022</c><00:00:32.439><c> to</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
to speech and so forth just from 2022 to
 

00:00:32.599 --> 00:00:35.709 align:start position:0%
to speech and so forth just from 2022 to
2023<00:00:33.320><c> hugg</c><00:00:33.520><c> and</c><00:00:33.680><c> face</c><00:00:34.000><c> valuation</c><00:00:34.719><c> in</c><00:00:35.079><c> billions</c>

00:00:35.709 --> 00:00:35.719 align:start position:0%
2023 hugg and face valuation in billions
 

00:00:35.719 --> 00:00:38.229 align:start position:0%
2023 hugg and face valuation in billions
more<00:00:35.920><c> than</c><00:00:36.120><c> doubled</c><00:00:36.600><c> from</c><00:00:36.800><c> 2</c><00:00:37.040><c> to</c><00:00:37.200><c> 4.5</c><00:00:37.960><c> billion</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
more than doubled from 2 to 4.5 billion
 

00:00:38.239 --> 00:00:39.790 align:start position:0%
more than doubled from 2 to 4.5 billion
and<00:00:38.360><c> they</c><00:00:38.480><c> almost</c><00:00:38.760><c> always</c><00:00:39.040><c> have</c><00:00:39.320><c> updated</c>

00:00:39.790 --> 00:00:39.800 align:start position:0%
and they almost always have updated
 

00:00:39.800 --> 00:00:42.110 align:start position:0%
and they almost always have updated
models<00:00:40.120><c> for</c><00:00:40.280><c> us</c><00:00:40.399><c> to</c><00:00:40.559><c> use</c><00:00:41.160><c> and</c><00:00:41.360><c> just</c><00:00:41.600><c> last</c><00:00:41.840><c> month</c>

00:00:42.110 --> 00:00:42.120 align:start position:0%
models for us to use and just last month
 

00:00:42.120 --> 00:00:44.350 align:start position:0%
models for us to use and just last month
the<00:00:42.239><c> Llama</c><00:00:42.600><c> 3</c><00:00:42.879><c> Model</c><00:00:43.440><c> was</c><00:00:43.600><c> downloaded</c><00:00:44.079><c> almost</c>

00:00:44.350 --> 00:00:44.360 align:start position:0%
the Llama 3 Model was downloaded almost
 

00:00:44.360 --> 00:00:46.950 align:start position:0%
the Llama 3 Model was downloaded almost
2.5<00:00:45.000><c> million</c><00:00:45.440><c> times</c><00:00:45.960><c> this</c><00:00:46.120><c> stable</c><00:00:46.480><c> diffusion</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
2.5 million times this stable diffusion
 

00:00:46.960 --> 00:00:49.430 align:start position:0%
2.5 million times this stable diffusion
model<00:00:47.320><c> was</c><00:00:47.520><c> downloaded</c><00:00:47.960><c> almost</c><00:00:48.239><c> 4.2</c><00:00:49.039><c> million</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
model was downloaded almost 4.2 million
 

00:00:49.440 --> 00:00:51.549 align:start position:0%
model was downloaded almost 4.2 million
times<00:00:49.840><c> and</c><00:00:49.960><c> we</c><00:00:50.079><c> could</c><00:00:50.239><c> simply</c><00:00:50.680><c> ask</c><00:00:51.000><c> a</c><00:00:51.160><c> text</c><00:00:51.399><c> to</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
times and we could simply ask a text to
 

00:00:51.559 --> 00:00:53.670 align:start position:0%
times and we could simply ask a text to
image<00:00:51.840><c> model</c><00:00:52.520><c> give</c><00:00:52.640><c> us</c><00:00:52.800><c> an</c><00:00:52.960><c> astronaut</c><00:00:53.399><c> riding</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
image model give us an astronaut riding
 

00:00:53.680 --> 00:00:55.510 align:start position:0%
image model give us an astronaut riding
a<00:00:53.800><c> horse</c><00:00:54.160><c> in</c><00:00:54.399><c> space</c><00:00:54.800><c> and</c><00:00:54.879><c> it</c><00:00:55.000><c> can</c><00:00:55.120><c> be</c><00:00:55.280><c> done</c>

00:00:55.510 --> 00:00:55.520 align:start position:0%
a horse in space and it can be done
 

00:00:55.520 --> 00:00:57.590 align:start position:0%
a horse in space and it can be done
within<00:00:56.039><c> seconds</c><00:00:56.680><c> and</c><00:00:56.840><c> here</c><00:00:57.039><c> is</c><00:00:57.239><c> actually</c><00:00:57.440><c> an</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
within seconds and here is actually an
 

00:00:57.600 --> 00:00:58.869 align:start position:0%
within seconds and here is actually an
image<00:00:57.879><c> that</c><00:00:57.960><c> I</c><00:00:58.079><c> took</c><00:00:58.239><c> from</c><00:00:58.399><c> that</c><00:00:58.680><c> here's</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
image that I took from that here's
 

00:00:58.879 --> 00:01:00.270 align:start position:0%
image that I took from that here's
another<00:00:59.160><c> prompt</c><00:00:59.399><c> where</c><00:00:59.480><c> I</c><00:00:59.519><c> have</c><00:00:59.600><c> a</c><00:00:59.719><c> cat</c><00:01:00.000><c> riding</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
another prompt where I have a cat riding
 

00:01:00.280 --> 00:01:03.189 align:start position:0%
another prompt where I have a cat riding
a<00:01:00.440><c> rocket</c><00:01:01.120><c> and</c><00:01:01.399><c> best</c><00:01:01.559><c> of</c><00:01:01.719><c> all</c><00:01:02.320><c> this</c><00:01:02.440><c> was</c><00:01:02.680><c> free</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
a rocket and best of all this was free
 

00:01:03.199 --> 00:01:05.429 align:start position:0%
a rocket and best of all this was free
and<00:01:03.440><c> yes</c><00:01:03.760><c> this</c><00:01:03.960><c> cat</c><00:01:04.199><c> has</c><00:01:04.600><c> three</c><00:01:04.839><c> arms</c><00:01:05.320><c> well</c>

00:01:05.429 --> 00:01:05.439 align:start position:0%
and yes this cat has three arms well
 

00:01:05.439 --> 00:01:06.789 align:start position:0%
and yes this cat has three arms well
let's<00:01:05.560><c> go</c><00:01:05.600><c> ahead</c><00:01:05.760><c> and</c><00:01:05.840><c> explore</c><00:01:06.080><c> hugging</c><00:01:06.439><c> face</c>

00:01:06.789 --> 00:01:06.799 align:start position:0%
let's go ahead and explore hugging face
 

00:01:06.799 --> 00:01:08.149 align:start position:0%
let's go ahead and explore hugging face
so<00:01:06.960><c> I</c><00:01:07.040><c> can</c><00:01:07.119><c> show</c><00:01:07.280><c> you</c><00:01:07.400><c> what</c><00:01:07.520><c> you</c><00:01:07.600><c> can</c><00:01:07.720><c> do</c><00:01:08.000><c> on</c>

00:01:08.149 --> 00:01:08.159 align:start position:0%
so I can show you what you can do on
 

00:01:08.159 --> 00:01:10.429 align:start position:0%
so I can show you what you can do on
their<00:01:08.360><c> site</c><00:01:08.840><c> and</c><00:01:09.000><c> then</c><00:01:09.119><c> we'll</c><00:01:09.320><c> get</c><00:01:09.439><c> into</c><00:01:09.840><c> code</c>

00:01:10.429 --> 00:01:10.439 align:start position:0%
their site and then we'll get into code
 

00:01:10.439 --> 00:01:12.749 align:start position:0%
their site and then we'll get into code
where<00:01:10.560><c> you</c><00:01:10.640><c> can</c><00:01:10.880><c> connect</c><00:01:11.560><c> python</c><00:01:12.040><c> to</c><00:01:12.360><c> hugging</c>

00:01:12.749 --> 00:01:12.759 align:start position:0%
where you can connect python to hugging
 

00:01:12.759 --> 00:01:14.630 align:start position:0%
where you can connect python to hugging
face<00:01:13.119><c> again</c><00:01:13.560><c> for</c><00:01:13.720><c> free</c><00:01:14.200><c> well</c><00:01:14.320><c> we're</c><00:01:14.439><c> going</c><00:01:14.560><c> to</c>

00:01:14.630 --> 00:01:14.640 align:start position:0%
face again for free well we're going to
 

00:01:14.640 --> 00:01:16.870 align:start position:0%
face again for free well we're going to
go<00:01:14.720><c> to</c><00:01:14.880><c> huggingface</c><00:01:15.720><c> doco</c><00:01:16.400><c> and</c><00:01:16.520><c> this</c><00:01:16.640><c> is</c><00:01:16.759><c> the</c>

00:01:16.870 --> 00:01:16.880 align:start position:0%
go to huggingface doco and this is the
 

00:01:16.880 --> 00:01:18.670 align:start position:0%
go to huggingface doco and this is the
screen<00:01:17.159><c> that</c><00:01:17.280><c> you'll</c><00:01:17.439><c> see</c><00:01:17.960><c> at</c><00:01:18.080><c> the</c><00:01:18.240><c> top</c><00:01:18.479><c> here</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
screen that you'll see at the top here
 

00:01:18.680 --> 00:01:20.390 align:start position:0%
screen that you'll see at the top here
click<00:01:18.920><c> this</c><00:01:19.119><c> models</c><00:01:19.720><c> and</c><00:01:19.799><c> on</c><00:01:19.920><c> the</c><00:01:20.000><c> left</c><00:01:20.240><c> hand</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
click this models and on the left hand
 

00:01:20.400 --> 00:01:21.670 align:start position:0%
click this models and on the left hand
side<00:01:20.640><c> here</c><00:01:20.880><c> these</c><00:01:21.000><c> are</c><00:01:21.119><c> all</c><00:01:21.240><c> the</c><00:01:21.360><c> different</c>

00:01:21.670 --> 00:01:21.680 align:start position:0%
side here these are all the different
 

00:01:21.680 --> 00:01:23.990 align:start position:0%
side here these are all the different
types<00:01:21.920><c> of</c><00:01:22.119><c> models</c><00:01:22.799><c> that</c><00:01:22.920><c> you</c><00:01:23.000><c> can</c><00:01:23.240><c> filter</c><00:01:23.759><c> this</c>

00:01:23.990 --> 00:01:24.000 align:start position:0%
types of models that you can filter this
 

00:01:24.000 --> 00:01:26.870 align:start position:0%
types of models that you can filter this
search<00:01:24.360><c> by</c><00:01:24.920><c> such</c><00:01:25.159><c> as</c><00:01:25.360><c> text</c><00:01:25.600><c> to</c><00:01:25.759><c> image</c><00:01:26.360><c> image</c><00:01:26.640><c> to</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
search by such as text to image image to
 

00:01:26.880 --> 00:01:28.990 align:start position:0%
search by such as text to image image to
text<00:01:27.280><c> text</c><00:01:27.520><c> to</c><00:01:27.720><c> video</c><00:01:28.079><c> and</c><00:01:28.280><c> so</c><00:01:28.520><c> forth</c><00:01:28.840><c> and</c>

00:01:28.990 --> 00:01:29.000 align:start position:0%
text text to video and so forth and
 

00:01:29.000 --> 00:01:30.190 align:start position:0%
text text to video and so forth and
typically<00:01:29.320><c> here</c><00:01:29.439><c> on</c><00:01:29.520><c> the</c><00:01:29.600><c> front</c><00:01:29.759><c> page</c><00:01:29.960><c> page</c>

00:01:30.190 --> 00:01:30.200 align:start position:0%
typically here on the front page page
 

00:01:30.200 --> 00:01:31.630 align:start position:0%
typically here on the front page page
they<00:01:30.320><c> have</c><00:01:30.520><c> the</c><00:01:30.720><c> trending</c><00:01:31.119><c> models</c><00:01:31.520><c> that</c>

00:01:31.630 --> 00:01:31.640 align:start position:0%
they have the trending models that
 

00:01:31.640 --> 00:01:32.670 align:start position:0%
they have the trending models that
everybody<00:01:31.920><c> wants</c><00:01:32.079><c> to</c><00:01:32.159><c> play</c><00:01:32.320><c> around</c><00:01:32.520><c> with</c>

00:01:32.670 --> 00:01:32.680 align:start position:0%
everybody wants to play around with
 

00:01:32.680 --> 00:01:33.990 align:start position:0%
everybody wants to play around with
right<00:01:32.799><c> now</c><00:01:33.000><c> so</c><00:01:33.119><c> let's</c><00:01:33.240><c> go</c><00:01:33.399><c> and</c><00:01:33.520><c> look</c><00:01:33.640><c> at</c><00:01:33.799><c> a</c>

00:01:33.990 --> 00:01:34.000 align:start position:0%
right now so let's go and look at a
 

00:01:34.000 --> 00:01:36.109 align:start position:0%
right now so let's go and look at a
first<00:01:34.360><c> example</c><00:01:34.960><c> on</c><00:01:35.159><c> the</c><00:01:35.320><c> website</c><00:01:35.799><c> so</c><00:01:35.920><c> on</c><00:01:36.000><c> the</c>

00:01:36.109 --> 00:01:36.119 align:start position:0%
first example on the website so on the
 

00:01:36.119 --> 00:01:37.590 align:start position:0%
first example on the website so on the
left<00:01:36.320><c> hand</c><00:01:36.439><c> side</c><00:01:36.640><c> here</c><00:01:36.799><c> under</c><00:01:37.040><c> the</c><00:01:37.240><c> audio</c>

00:01:37.590 --> 00:01:37.600 align:start position:0%
left hand side here under the audio
 

00:01:37.600 --> 00:01:39.950 align:start position:0%
left hand side here under the audio
section<00:01:38.159><c> there's</c><00:01:38.399><c> a</c><00:01:38.600><c> text</c><00:01:38.920><c> to</c><00:01:39.159><c> speech</c><00:01:39.439><c> filter</c>

00:01:39.950 --> 00:01:39.960 align:start position:0%
section there's a text to speech filter
 

00:01:39.960 --> 00:01:41.590 align:start position:0%
section there's a text to speech filter
click<00:01:40.280><c> that</c><00:01:40.759><c> and</c><00:01:40.920><c> now</c><00:01:41.079><c> you're</c><00:01:41.200><c> going</c><00:01:41.320><c> to</c><00:01:41.399><c> see</c>

00:01:41.590 --> 00:01:41.600 align:start position:0%
click that and now you're going to see
 

00:01:41.600 --> 00:01:43.710 align:start position:0%
click that and now you're going to see
all<00:01:41.799><c> the</c><00:01:41.960><c> text</c><00:01:42.240><c> to</c><00:01:42.479><c> speech</c><00:01:42.759><c> models</c><00:01:43.399><c> let's</c><00:01:43.600><c> go</c>

00:01:43.710 --> 00:01:43.720 align:start position:0%
all the text to speech models let's go
 

00:01:43.720 --> 00:01:45.109 align:start position:0%
all the text to speech models let's go
ahead<00:01:43.920><c> and</c><00:01:44.040><c> scroll</c><00:01:44.320><c> down</c><00:01:44.439><c> and</c><00:01:44.560><c> choose</c><00:01:44.759><c> the</c><00:01:44.920><c> my</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
ahead and scroll down and choose the my
 

00:01:45.119 --> 00:01:48.830 align:start position:0%
ahead and scroll down and choose the my
shell<00:01:45.399><c> AI</c><00:01:46.119><c> melow</c><00:01:46.600><c> TTS</c><00:01:47.439><c> Chinese</c><00:01:48.200><c> so</c><00:01:48.399><c> here</c><00:01:48.640><c> they</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
shell AI melow TTS Chinese so here they
 

00:01:48.840 --> 00:01:51.230 align:start position:0%
shell AI melow TTS Chinese so here they
have<00:01:49.079><c> English</c><00:01:49.439><c> Aman</c><00:01:49.920><c> British</c><00:01:50.240><c> Indian</c><00:01:51.119><c> uh</c>

00:01:51.230 --> 00:01:51.240 align:start position:0%
have English Aman British Indian uh
 

00:01:51.240 --> 00:01:53.990 align:start position:0%
have English Aman British Indian uh
Australian<00:01:51.840><c> and</c><00:01:52.040><c> a</c><00:01:52.280><c> default</c><00:01:52.759><c> sample</c><00:01:53.280><c> of</c><00:01:53.719><c> what</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
Australian and a default sample of what
 

00:01:54.000 --> 00:01:55.630 align:start position:0%
Australian and a default sample of what
this<00:01:54.159><c> model</c><00:01:54.479><c> can</c><00:01:54.640><c> output</c><00:01:55.000><c> for</c><00:01:55.119><c> you</c><00:01:55.399><c> let's</c><00:01:55.560><c> go</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
this model can output for you let's go
 

00:01:55.640 --> 00:01:57.310 align:start position:0%
this model can output for you let's go
ahead<00:01:55.799><c> and</c><00:01:55.920><c> listen</c><00:01:56.200><c> to</c><00:01:56.320><c> two</c><00:01:56.479><c> of</c><00:01:56.640><c> them</c><00:01:57.039><c> did</c><00:01:57.159><c> you</c>

00:01:57.310 --> 00:01:57.320 align:start position:0%
ahead and listen to two of them did you
 

00:01:57.320 --> 00:01:59.190 align:start position:0%
ahead and listen to two of them did you
ever<00:01:57.520><c> hear</c><00:01:57.680><c> a</c><00:01:57.799><c> folk</c><00:01:58.159><c> tale</c><00:01:58.479><c> about</c><00:01:58.680><c> a</c><00:01:58.840><c> giant</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
ever hear a folk tale about a giant
 

00:01:59.200 --> 00:02:00.749 align:start position:0%
ever hear a folk tale about a giant
turtle<00:01:59.920><c> or</c><00:02:00.039><c> we</c><00:02:00.119><c> can</c><00:02:00.240><c> have</c><00:02:00.399><c> this</c><00:02:00.479><c> in</c><00:02:00.600><c> an</c>

00:02:00.749 --> 00:02:00.759 align:start position:0%
turtle or we can have this in an
 

00:02:00.759 --> 00:02:02.670 align:start position:0%
turtle or we can have this in an
Australian<00:02:01.320><c> accent</c><00:02:01.880><c> did</c><00:02:02.000><c> you</c><00:02:02.200><c> ever</c><00:02:02.399><c> hear</c><00:02:02.560><c> a</c>

00:02:02.670 --> 00:02:02.680 align:start position:0%
Australian accent did you ever hear a
 

00:02:02.680 --> 00:02:04.910 align:start position:0%
Australian accent did you ever hear a
folk<00:02:03.039><c> tale</c><00:02:03.360><c> about</c><00:02:03.560><c> a</c><00:02:03.759><c> giant</c><00:02:04.079><c> turtle</c><00:02:04.759><c> now</c><00:02:04.840><c> if</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
folk tale about a giant turtle now if
 

00:02:04.920 --> 00:02:06.550 align:start position:0%
folk tale about a giant turtle now if
you<00:02:05.039><c> go</c><00:02:05.200><c> back</c><00:02:05.320><c> to</c><00:02:05.439><c> the</c><00:02:05.560><c> models</c><00:02:05.920><c> page</c><00:02:06.280><c> one</c><00:02:06.439><c> thing</c>

00:02:06.550 --> 00:02:06.560 align:start position:0%
you go back to the models page one thing
 

00:02:06.560 --> 00:02:08.270 align:start position:0%
you go back to the models page one thing
we<00:02:06.680><c> can</c><00:02:06.799><c> do</c><00:02:06.960><c> is</c><00:02:07.079><c> we</c><00:02:07.159><c> can</c><00:02:07.280><c> use</c><00:02:07.479><c> another</c><00:02:07.719><c> model</c>

00:02:08.270 --> 00:02:08.280 align:start position:0%
we can do is we can use another model
 

00:02:08.280 --> 00:02:10.469 align:start position:0%
we can do is we can use another model
just<00:02:08.599><c> on</c><00:02:08.879><c> the</c><00:02:09.119><c> hugging</c><00:02:09.520><c> face</c><00:02:09.800><c> website</c><00:02:10.319><c> so</c>

00:02:10.469 --> 00:02:10.479 align:start position:0%
just on the hugging face website so
 

00:02:10.479 --> 00:02:12.190 align:start position:0%
just on the hugging face website so
under<00:02:10.720><c> computer</c><00:02:11.039><c> vision</c><00:02:11.319><c> if</c><00:02:11.400><c> you</c><00:02:11.520><c> go</c><00:02:11.640><c> to</c><00:02:11.920><c> image</c>

00:02:12.190 --> 00:02:12.200 align:start position:0%
under computer vision if you go to image
 

00:02:12.200 --> 00:02:14.990 align:start position:0%
under computer vision if you go to image
to<00:02:12.480><c> text</c><00:02:13.239><c> then</c><00:02:13.400><c> you</c><00:02:13.560><c> go</c><00:02:13.720><c> to</c><00:02:13.959><c> Salesforce</c><00:02:14.640><c> blip</c>

00:02:14.990 --> 00:02:15.000 align:start position:0%
to text then you go to Salesforce blip
 

00:02:15.000 --> 00:02:16.470 align:start position:0%
to text then you go to Salesforce blip
image<00:02:15.239><c> captioning</c><00:02:15.599><c> model</c><00:02:15.879><c> just</c><00:02:16.040><c> click</c><00:02:16.200><c> on</c><00:02:16.319><c> the</c>

00:02:16.470 --> 00:02:16.480 align:start position:0%
image captioning model just click on the
 

00:02:16.480 --> 00:02:19.030 align:start position:0%
image captioning model just click on the
card<00:02:17.280><c> here</c><00:02:17.560><c> we</c><00:02:17.640><c> can</c><00:02:17.840><c> just</c><00:02:18.040><c> drag</c><00:02:18.400><c> and</c><00:02:18.599><c> drop</c><00:02:18.879><c> an</c>

00:02:19.030 --> 00:02:19.040 align:start position:0%
card here we can just drag and drop an
 

00:02:19.040 --> 00:02:21.350 align:start position:0%
card here we can just drag and drop an
image<00:02:19.560><c> here</c><00:02:19.840><c> and</c><00:02:19.959><c> it'll</c><00:02:20.200><c> tell</c><00:02:20.400><c> us</c><00:02:20.640><c> about</c><00:02:20.840><c> it</c><00:02:21.080><c> so</c>

00:02:21.350 --> 00:02:21.360 align:start position:0%
image here and it'll tell us about it so
 

00:02:21.360 --> 00:02:23.030 align:start position:0%
image here and it'll tell us about it so
after<00:02:21.560><c> I</c><00:02:21.720><c> put</c><00:02:21.920><c> this</c><00:02:22.239><c> Meme</c><00:02:22.560><c> here</c><00:02:22.680><c> it</c><00:02:22.760><c> says</c><00:02:22.920><c> a</c>

00:02:23.030 --> 00:02:23.040 align:start position:0%
after I put this Meme here it says a
 

00:02:23.040 --> 00:02:24.589 align:start position:0%
after I put this Meme here it says a
cartoon<00:02:23.319><c> of</c><00:02:23.440><c> a</c><00:02:23.560><c> guy</c><00:02:23.680><c> working</c><00:02:23.959><c> on</c><00:02:24.040><c> a</c><00:02:24.160><c> computer</c>

00:02:24.589 --> 00:02:24.599 align:start position:0%
cartoon of a guy working on a computer
 

00:02:24.599 --> 00:02:26.910 align:start position:0%
cartoon of a guy working on a computer
and<00:02:24.840><c> another</c><00:02:25.239><c> guy</c><00:02:25.720><c> sitting</c><00:02:26.080><c> at</c><00:02:26.239><c> a</c><00:02:26.400><c> desk</c><00:02:26.800><c> this</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
and another guy sitting at a desk this
 

00:02:26.920 --> 00:02:29.309 align:start position:0%
and another guy sitting at a desk this
is<00:02:27.120><c> cool</c><00:02:27.440><c> we</c><00:02:27.560><c> can</c><00:02:27.720><c> do</c><00:02:27.920><c> this</c><00:02:28.200><c> on</c><00:02:28.599><c> their</c><00:02:28.879><c> website</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
is cool we can do this on their website
 

00:02:29.319 --> 00:02:31.030 align:start position:0%
is cool we can do this on their website
itself<00:02:29.640><c> so</c><00:02:30.000><c> a</c><00:02:30.080><c> lot</c><00:02:30.200><c> of</c><00:02:30.280><c> these</c><00:02:30.400><c> models</c><00:02:30.840><c> have</c>

00:02:31.030 --> 00:02:31.040 align:start position:0%
itself so a lot of these models have
 

00:02:31.040 --> 00:02:32.190 align:start position:0%
itself so a lot of these models have
examples<00:02:31.400><c> for</c><00:02:31.560><c> you</c><00:02:31.640><c> that</c><00:02:31.760><c> you</c><00:02:31.840><c> can</c><00:02:31.959><c> choose</c>

00:02:32.190 --> 00:02:32.200 align:start position:0%
examples for you that you can choose
 

00:02:32.200 --> 00:02:34.350 align:start position:0%
examples for you that you can choose
from<00:02:32.879><c> but</c><00:02:33.239><c> the</c><00:02:33.400><c> main</c><00:02:33.640><c> thing</c><00:02:33.800><c> that</c><00:02:33.920><c> I</c><00:02:34.120><c> really</c>

00:02:34.350 --> 00:02:34.360 align:start position:0%
from but the main thing that I really
 

00:02:34.360 --> 00:02:35.910 align:start position:0%
from but the main thing that I really
want<00:02:34.519><c> to</c><00:02:34.680><c> talk</c><00:02:34.840><c> to</c><00:02:34.959><c> you</c><00:02:35.080><c> about</c><00:02:35.519><c> is</c><00:02:35.720><c> the</c>

00:02:35.910 --> 00:02:35.920 align:start position:0%
want to talk to you about is the
 

00:02:35.920 --> 00:02:38.150 align:start position:0%
want to talk to you about is the
inference<00:02:36.400><c> serverless</c><00:02:37.080><c> API</c><00:02:37.720><c> it's</c><00:02:37.879><c> one</c><00:02:38.040><c> thing</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
inference serverless API it's one thing
 

00:02:38.160 --> 00:02:39.430 align:start position:0%
inference serverless API it's one thing
that<00:02:38.280><c> a</c><00:02:38.360><c> lot</c><00:02:38.560><c> these</c><00:02:38.720><c> that</c><00:02:38.840><c> you</c><00:02:38.920><c> can</c><00:02:39.080><c> try</c><00:02:39.280><c> out</c>

00:02:39.430 --> 00:02:39.440 align:start position:0%
that a lot these that you can try out
 

00:02:39.440 --> 00:02:41.270 align:start position:0%
that a lot these that you can try out
for<00:02:39.599><c> free</c><00:02:40.000><c> or</c><00:02:40.200><c> you</c><00:02:40.319><c> can</c><00:02:40.480><c> use</c><00:02:40.720><c> through</c><00:02:40.920><c> python</c>

00:02:41.270 --> 00:02:41.280 align:start position:0%
for free or you can use through python
 

00:02:41.280 --> 00:02:43.750 align:start position:0%
for free or you can use through python
code<00:02:41.840><c> but</c><00:02:42.000><c> the</c><00:02:42.159><c> other</c><00:02:42.400><c> thing</c><00:02:42.640><c> with</c><00:02:42.879><c> AI</c><00:02:43.519><c> is</c><00:02:43.640><c> that</c>

00:02:43.750 --> 00:02:43.760 align:start position:0%
code but the other thing with AI is that
 

00:02:43.760 --> 00:02:45.070 align:start position:0%
code but the other thing with AI is that
you<00:02:43.879><c> have</c><00:02:44.000><c> to</c><00:02:44.080><c> have</c><00:02:44.200><c> the</c><00:02:44.400><c> hardware</c><00:02:44.840><c> to</c>

00:02:45.070 --> 00:02:45.080 align:start position:0%
you have to have the hardware to
 

00:02:45.080 --> 00:02:46.630 align:start position:0%
you have to have the hardware to
actually<00:02:45.400><c> run</c><00:02:45.640><c> it</c><00:02:45.920><c> and</c><00:02:46.080><c> this</c><00:02:46.200><c> is</c><00:02:46.319><c> where</c><00:02:46.519><c> the</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
actually run it and this is where the
 

00:02:46.640 --> 00:02:48.990 align:start position:0%
actually run it and this is where the
inference<00:02:47.040><c> serverless</c><00:02:47.519><c> API</c><00:02:48.040><c> comes</c><00:02:48.280><c> into</c><00:02:48.560><c> play</c>

00:02:48.990 --> 00:02:49.000 align:start position:0%
inference serverless API comes into play
 

00:02:49.000 --> 00:02:50.589 align:start position:0%
inference serverless API comes into play
okay<00:02:49.080><c> so</c><00:02:49.239><c> what</c><00:02:49.319><c> we're</c><00:02:49.400><c> going</c><00:02:49.519><c> to</c><00:02:49.640><c> do</c><00:02:49.840><c> is</c><00:02:50.040><c> go</c><00:02:50.159><c> to</c>

00:02:50.589 --> 00:02:50.599 align:start position:0%
okay so what we're going to do is go to
 

00:02:50.599 --> 00:02:52.910 align:start position:0%
okay so what we're going to do is go to
text<00:02:50.920><c> to</c><00:02:51.200><c> image</c><00:02:52.120><c> then</c><00:02:52.239><c> we're</c><00:02:52.360><c> going</c><00:02:52.480><c> to</c><00:02:52.599><c> choose</c>

00:02:52.910 --> 00:02:52.920 align:start position:0%
text to image then we're going to choose
 

00:02:52.920 --> 00:02:56.110 align:start position:0%
text to image then we're going to choose
stable<00:02:53.239><c> diffusion</c><00:02:53.640><c> XL</c><00:02:54.120><c> base</c><00:02:54.680><c> 1.0</c><00:02:55.680><c> now</c><00:02:55.879><c> not</c>

00:02:56.110 --> 00:02:56.120 align:start position:0%
stable diffusion XL base 1.0 now not
 

00:02:56.120 --> 00:02:58.110 align:start position:0%
stable diffusion XL base 1.0 now not
every<00:02:56.360><c> model</c><00:02:56.720><c> has</c><00:02:56.959><c> this</c><00:02:57.360><c> but</c><00:02:57.480><c> at</c><00:02:57.599><c> the</c><00:02:57.840><c> top</c>

00:02:58.110 --> 00:02:58.120 align:start position:0%
every model has this but at the top
 

00:02:58.120 --> 00:02:59.470 align:start position:0%
every model has this but at the top
right<00:02:58.319><c> here</c><00:02:58.480><c> where</c><00:02:58.599><c> it</c><00:02:58.680><c> says</c><00:02:58.879><c> deploy</c><00:02:59.280><c> if</c><00:02:59.360><c> you</c>

00:02:59.470 --> 00:02:59.480 align:start position:0%
right here where it says deploy if you
 

00:02:59.480 --> 00:03:01.949 align:start position:0%
right here where it says deploy if you
choose<00:02:59.720><c> this</c><00:02:59.920><c> this</c><00:03:00.239><c> go</c><00:03:00.440><c> to</c><00:03:00.680><c> inference</c><00:03:01.319><c> API</c>

00:03:01.949 --> 00:03:01.959 align:start position:0%
choose this this go to inference API
 

00:03:01.959 --> 00:03:03.949 align:start position:0%
choose this this go to inference API
serverless<00:03:02.760><c> dedicated</c><00:03:03.200><c> you</c><00:03:03.319><c> have</c><00:03:03.400><c> to</c><00:03:03.519><c> pay</c><00:03:03.680><c> for</c>

00:03:03.949 --> 00:03:03.959 align:start position:0%
serverless dedicated you have to pay for
 

00:03:03.959 --> 00:03:05.070 align:start position:0%
serverless dedicated you have to pay for
and<00:03:04.120><c> that's</c><00:03:04.280><c> fine</c><00:03:04.440><c> but</c><00:03:04.560><c> we</c><00:03:04.640><c> want</c><00:03:04.760><c> to</c><00:03:04.840><c> choose</c>

00:03:05.070 --> 00:03:05.080 align:start position:0%
and that's fine but we want to choose
 

00:03:05.080 --> 00:03:06.430 align:start position:0%
and that's fine but we want to choose
serverless<00:03:05.680><c> and</c><00:03:05.760><c> it</c><00:03:05.879><c> literally</c><00:03:06.159><c> gives</c><00:03:06.319><c> you</c>

00:03:06.430 --> 00:03:06.440 align:start position:0%
serverless and it literally gives you
 

00:03:06.440 --> 00:03:08.030 align:start position:0%
serverless and it literally gives you
the<00:03:06.560><c> python</c><00:03:06.879><c> code</c><00:03:07.080><c> that</c><00:03:07.159><c> you</c><00:03:07.280><c> need</c><00:03:07.599><c> to</c><00:03:07.799><c> run</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
the python code that you need to run
 

00:03:08.040 --> 00:03:09.229 align:start position:0%
the python code that you need to run
this<00:03:08.200><c> and</c><00:03:08.280><c> I'm</c><00:03:08.360><c> about</c><00:03:08.519><c> to</c><00:03:08.599><c> show</c><00:03:08.720><c> you</c><00:03:08.840><c> that</c><00:03:08.959><c> in</c><00:03:09.080><c> a</c>

00:03:09.229 --> 00:03:09.239 align:start position:0%
this and I'm about to show you that in a
 

00:03:09.239 --> 00:03:10.789 align:start position:0%
this and I'm about to show you that in a
minute<00:03:09.680><c> but</c><00:03:09.799><c> the</c><00:03:09.879><c> only</c><00:03:10.040><c> thing</c><00:03:10.120><c> you</c><00:03:10.200><c> need</c><00:03:10.319><c> to</c><00:03:10.480><c> do</c>

00:03:10.789 --> 00:03:10.799 align:start position:0%
minute but the only thing you need to do
 

00:03:10.799 --> 00:03:12.869 align:start position:0%
minute but the only thing you need to do
is<00:03:10.920><c> you</c><00:03:11.000><c> do</c><00:03:11.159><c> need</c><00:03:11.280><c> to</c><00:03:11.440><c> sign</c><00:03:11.640><c> up</c><00:03:11.920><c> which</c><00:03:12.040><c> is</c><00:03:12.200><c> free</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
is you do need to sign up which is free
 

00:03:12.879 --> 00:03:14.550 align:start position:0%
is you do need to sign up which is free
and<00:03:12.959><c> then</c><00:03:13.080><c> you</c><00:03:13.239><c> go</c><00:03:13.319><c> to</c><00:03:13.440><c> your</c><00:03:13.519><c> manage</c><00:03:13.959><c> tokens</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
and then you go to your manage tokens
 

00:03:14.560 --> 00:03:16.390 align:start position:0%
and then you go to your manage tokens
and<00:03:14.720><c> then</c><00:03:15.120><c> you</c><00:03:15.239><c> have</c><00:03:15.360><c> to</c><00:03:15.519><c> copy</c><00:03:15.879><c> that</c><00:03:16.040><c> token</c><00:03:16.319><c> and</c>

00:03:16.390 --> 00:03:16.400 align:start position:0%
and then you have to copy that token and
 

00:03:16.400 --> 00:03:18.270 align:start position:0%
and then you have to copy that token and
you<00:03:16.599><c> replace</c><00:03:16.959><c> that</c><00:03:17.159><c> here</c><00:03:17.400><c> in</c><00:03:17.519><c> the</c><00:03:17.680><c> bearer</c>

00:03:18.270 --> 00:03:18.280 align:start position:0%
you replace that here in the bearer
 

00:03:18.280 --> 00:03:20.949 align:start position:0%
you replace that here in the bearer
token<00:03:18.680><c> section</c><00:03:19.239><c> so</c><00:03:19.519><c> I'm</c><00:03:19.599><c> going</c><00:03:19.720><c> to</c><00:03:19.959><c> copy</c><00:03:20.319><c> this</c>

00:03:20.949 --> 00:03:20.959 align:start position:0%
token section so I'm going to copy this
 

00:03:20.959 --> 00:03:23.229 align:start position:0%
token section so I'm going to copy this
and<00:03:21.120><c> I</c><00:03:21.239><c> started</c><00:03:21.560><c> a</c><00:03:21.760><c> new</c><00:03:22.080><c> project</c><00:03:22.480><c> and</c><00:03:22.640><c> I</c><00:03:22.840><c> just</c>

00:03:23.229 --> 00:03:23.239 align:start position:0%
and I started a new project and I just
 

00:03:23.239 --> 00:03:25.149 align:start position:0%
and I started a new project and I just
pasted<00:03:24.040><c> exactly</c><00:03:24.480><c> here</c><00:03:24.760><c> the</c><00:03:24.840><c> only</c><00:03:25.040><c> thing</c>

00:03:25.149 --> 00:03:25.159 align:start position:0%
pasted exactly here the only thing
 

00:03:25.159 --> 00:03:27.830 align:start position:0%
pasted exactly here the only thing
you'll<00:03:25.400><c> do</c><00:03:25.879><c> is</c><00:03:26.200><c> you'll</c><00:03:26.720><c> take</c><00:03:27.000><c> this</c><00:03:27.200><c> part</c><00:03:27.640><c> and</c>

00:03:27.830 --> 00:03:27.840 align:start position:0%
you'll do is you'll take this part and
 

00:03:27.840 --> 00:03:29.350 align:start position:0%
you'll do is you'll take this part and
paste<00:03:28.040><c> in</c><00:03:28.200><c> your</c><00:03:28.360><c> actual</c><00:03:28.640><c> token</c><00:03:29.080><c> but</c><00:03:29.200><c> that's</c>

00:03:29.350 --> 00:03:29.360 align:start position:0%
paste in your actual token but that's
 

00:03:29.360 --> 00:03:31.229 align:start position:0%
paste in your actual token but that's
all<00:03:29.519><c> you</c><00:03:29.879><c> need</c><00:03:30.000><c> to</c><00:03:30.200><c> do</c><00:03:30.599><c> but</c><00:03:30.720><c> if</c><00:03:30.799><c> you</c><00:03:30.920><c> do</c><00:03:31.040><c> want</c><00:03:31.120><c> to</c>

00:03:31.229 --> 00:03:31.239 align:start position:0%
all you need to do but if you do want to
 

00:03:31.239 --> 00:03:32.910 align:start position:0%
all you need to do but if you do want to
save<00:03:31.439><c> it</c><00:03:31.519><c> to</c><00:03:31.640><c> your</c><00:03:31.760><c> local</c><00:03:32.360><c> all</c><00:03:32.480><c> you</c><00:03:32.560><c> have</c><00:03:32.640><c> to</c><00:03:32.760><c> do</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
save it to your local all you have to do
 

00:03:32.920 --> 00:03:35.229 align:start position:0%
save it to your local all you have to do
is<00:03:33.080><c> go</c><00:03:33.159><c> to</c><00:03:33.319><c> next</c><00:03:33.480><c> line</c><00:03:33.640><c> and</c><00:03:33.799><c> type</c><00:03:34.000><c> image</c><00:03:34.680><c> Dove</c>

00:03:35.229 --> 00:03:35.239 align:start position:0%
is go to next line and type image Dove
 

00:03:35.239 --> 00:03:37.030 align:start position:0%
is go to next line and type image Dove
and<00:03:35.360><c> then</c><00:03:35.519><c> give</c><00:03:35.640><c> it</c><00:03:35.840><c> some</c><00:03:36.120><c> file</c><00:03:36.439><c> name</c><00:03:36.879><c> so</c><00:03:36.959><c> you</c>

00:03:37.030 --> 00:03:37.040 align:start position:0%
and then give it some file name so you
 

00:03:37.040 --> 00:03:39.949 align:start position:0%
and then give it some file name so you
can<00:03:37.200><c> say</c><00:03:37.400><c> tester.</c><00:03:38.400><c> PNG</c><00:03:39.120><c> and</c><00:03:39.239><c> then</c><00:03:39.360><c> just</c><00:03:39.480><c> run</c><00:03:39.680><c> it</c>

00:03:39.949 --> 00:03:39.959 align:start position:0%
can say tester. PNG and then just run it
 

00:03:39.959 --> 00:03:41.470 align:start position:0%
can say tester. PNG and then just run it
and<00:03:40.080><c> it</c><00:03:40.319><c> and</c><00:03:40.439><c> it</c><00:03:40.560><c> may</c><00:03:40.720><c> take</c><00:03:40.840><c> you</c><00:03:40.959><c> a</c><00:03:41.080><c> few</c><00:03:41.239><c> seconds</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
and it and it may take you a few seconds
 

00:03:41.480 --> 00:03:43.990 align:start position:0%
and it and it may take you a few seconds
to<00:03:41.640><c> run</c><00:03:42.000><c> but</c><00:03:42.360><c> once</c><00:03:42.519><c> it's</c><00:03:42.720><c> done</c><00:03:43.319><c> it</c><00:03:43.480><c> creates</c><00:03:43.840><c> the</c>

00:03:43.990 --> 00:03:44.000 align:start position:0%
to run but once it's done it creates the
 

00:03:44.000 --> 00:03:45.869 align:start position:0%
to run but once it's done it creates the
file<00:03:44.319><c> here</c><00:03:44.599><c> under</c><00:03:44.879><c> your</c><00:03:45.080><c> directory</c><00:03:45.640><c> there</c><00:03:45.760><c> is</c>

00:03:45.869 --> 00:03:45.879 align:start position:0%
file here under your directory there is
 

00:03:45.879 --> 00:03:47.830 align:start position:0%
file here under your directory there is
our<00:03:46.120><c> beautiful</c><00:03:46.599><c> image</c><00:03:47.000><c> of</c><00:03:47.159><c> an</c><00:03:47.360><c> astronaut</c>

00:03:47.830 --> 00:03:47.840 align:start position:0%
our beautiful image of an astronaut
 

00:03:47.840 --> 00:03:50.309 align:start position:0%
our beautiful image of an astronaut
riding<00:03:48.159><c> a</c><00:03:48.319><c> horse</c><00:03:48.879><c> Okay</c><00:03:49.040><c> so</c><00:03:49.280><c> that's</c><00:03:49.599><c> great</c><00:03:50.080><c> next</c>

00:03:50.309 --> 00:03:50.319 align:start position:0%
riding a horse Okay so that's great next
 

00:03:50.319 --> 00:03:51.910 align:start position:0%
riding a horse Okay so that's great next
I<00:03:50.400><c> want</c><00:03:50.519><c> to</c><00:03:50.640><c> try</c><00:03:50.840><c> a</c><00:03:51.000><c> text</c><00:03:51.280><c> generation</c><00:03:51.640><c> model</c>

00:03:51.910 --> 00:03:51.920 align:start position:0%
I want to try a text generation model
 

00:03:51.920 --> 00:03:53.949 align:start position:0%
I want to try a text generation model
but<00:03:52.000><c> I</c><00:03:52.079><c> want</c><00:03:52.159><c> to</c><00:03:52.280><c> use</c><00:03:52.400><c> a</c><00:03:52.599><c> new</c><00:03:52.799><c> one</c><00:03:53.200><c> from</c><00:03:53.439><c> meta</c><00:03:53.840><c> so</c>

00:03:53.949 --> 00:03:53.959 align:start position:0%
but I want to use a new one from meta so
 

00:03:53.959 --> 00:03:56.229 align:start position:0%
but I want to use a new one from meta so
I<00:03:54.000><c> want</c><00:03:54.120><c> to</c><00:03:54.239><c> use</c><00:03:54.439><c> metal</c><00:03:54.760><c> Lama</c><00:03:55.079><c> 3's</c><00:03:55.680><c> 8</c><00:03:56.000><c> billion</c>

00:03:56.229 --> 00:03:56.239 align:start position:0%
I want to use metal Lama 3's 8 billion
 

00:03:56.239 --> 00:03:58.630 align:start position:0%
I want to use metal Lama 3's 8 billion
perimeter<00:03:56.760><c> instruct</c><00:03:57.239><c> model</c><00:03:57.680><c> all</c><00:03:57.799><c> you</c><00:03:57.959><c> do</c><00:03:58.519><c> go</c>

00:03:58.630 --> 00:03:58.640 align:start position:0%
perimeter instruct model all you do go
 

00:03:58.640 --> 00:04:00.710 align:start position:0%
perimeter instruct model all you do go
to<00:03:58.840><c> deploy</c><00:03:59.280><c> see</c><00:03:59.480><c> if</c><00:03:59.560><c> it's</c><00:03:59.879><c> has</c><00:04:00.040><c> an</c><00:04:00.239><c> inference</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
to deploy see if it's has an inference
 

00:04:00.720 --> 00:04:02.990 align:start position:0%
to deploy see if it's has an inference
API<00:04:01.120><c> server</c><00:04:01.519><c> list</c><00:04:01.799><c> type</c><00:04:02.360><c> it</c><00:04:02.519><c> does</c><00:04:02.720><c> so</c><00:04:02.840><c> you</c>

00:04:02.990 --> 00:04:03.000 align:start position:0%
API server list type it does so you
 

00:04:03.000 --> 00:04:04.830 align:start position:0%
API server list type it does so you
click<00:04:03.200><c> on</c><00:04:03.360><c> this</c><00:04:03.760><c> then</c><00:04:03.879><c> you</c><00:04:04.000><c> just</c><00:04:04.200><c> copy</c><00:04:04.560><c> this</c>

00:04:04.830 --> 00:04:04.840 align:start position:0%
click on this then you just copy this
 

00:04:04.840 --> 00:04:06.830 align:start position:0%
click on this then you just copy this
and<00:04:05.040><c> paste</c><00:04:05.239><c> it</c><00:04:05.360><c> into</c><00:04:05.519><c> your</c><00:04:05.720><c> project</c><00:04:06.400><c> okay</c><00:04:06.519><c> so</c><00:04:06.720><c> I</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
and paste it into your project okay so I
 

00:04:06.840 --> 00:04:08.710 align:start position:0%
and paste it into your project okay so I
just<00:04:07.000><c> literally</c><00:04:07.439><c> pasted</c><00:04:07.879><c> it</c><00:04:08.319><c> I'm</c><00:04:08.439><c> going</c><00:04:08.560><c> to</c>

00:04:08.710 --> 00:04:08.720 align:start position:0%
just literally pasted it I'm going to
 

00:04:08.720 --> 00:04:11.229 align:start position:0%
just literally pasted it I'm going to
change<00:04:09.120><c> the</c><00:04:09.280><c> bear</c><00:04:09.599><c> token</c><00:04:10.200><c> I</c><00:04:10.319><c> change</c><00:04:10.720><c> the</c><00:04:10.879><c> input</c>

00:04:11.229 --> 00:04:11.239 align:start position:0%
change the bear token I change the input
 

00:04:11.239 --> 00:04:12.949 align:start position:0%
change the bear token I change the input
to<00:04:11.480><c> give</c><00:04:11.640><c> me</c><00:04:11.840><c> a</c><00:04:11.959><c> function</c><00:04:12.239><c> for</c><00:04:12.400><c> a</c><00:04:12.599><c> binary</c>

00:04:12.949 --> 00:04:12.959 align:start position:0%
to give me a function for a binary
 

00:04:12.959 --> 00:04:15.390 align:start position:0%
to give me a function for a binary
search<00:04:13.560><c> and</c><00:04:13.680><c> then</c><00:04:13.799><c> I</c><00:04:13.959><c> add</c><00:04:14.239><c> the</c><00:04:14.640><c> pretty</c><00:04:15.079><c> print</c>

00:04:15.390 --> 00:04:15.400 align:start position:0%
search and then I add the pretty print
 

00:04:15.400 --> 00:04:17.110 align:start position:0%
search and then I add the pretty print
Library<00:04:16.000><c> just</c><00:04:16.120><c> so</c><00:04:16.320><c> the</c><00:04:16.440><c> output</c><00:04:16.799><c> looks</c><00:04:17.000><c> a</c>

00:04:17.110 --> 00:04:17.120 align:start position:0%
Library just so the output looks a
 

00:04:17.120 --> 00:04:19.150 align:start position:0%
Library just so the output looks a
little<00:04:17.320><c> bit</c><00:04:17.479><c> better</c><00:04:17.680><c> for</c><00:04:17.840><c> you</c><00:04:18.120><c> and</c><00:04:18.199><c> it</c><00:04:18.400><c> gave</c><00:04:18.560><c> me</c>

00:04:19.150 --> 00:04:19.160 align:start position:0%
little bit better for you and it gave me
 

00:04:19.160 --> 00:04:21.189 align:start position:0%
little bit better for you and it gave me
exactly<00:04:19.720><c> what</c><00:04:19.840><c> I</c><00:04:20.000><c> wanted</c><00:04:20.440><c> it</c><00:04:20.600><c> gave</c><00:04:20.759><c> me</c><00:04:21.000><c> a</c>

00:04:21.189 --> 00:04:21.199 align:start position:0%
exactly what I wanted it gave me a
 

00:04:21.199 --> 00:04:22.909 align:start position:0%
exactly what I wanted it gave me a
binary<00:04:21.639><c> search</c><00:04:22.120><c> algorithm</c><00:04:22.639><c> and</c><00:04:22.720><c> you</c><00:04:22.800><c> know</c>

00:04:22.909 --> 00:04:22.919 align:start position:0%
binary search algorithm and you know
 

00:04:22.919 --> 00:04:25.070 align:start position:0%
binary search algorithm and you know
what<00:04:23.360><c> have</c><00:04:23.520><c> fun</c><00:04:23.880><c> with</c><00:04:24.040><c> some</c><00:04:24.160><c> of</c><00:04:24.320><c> these</c><00:04:24.520><c> so</c><00:04:24.880><c> one</c>

00:04:25.070 --> 00:04:25.080 align:start position:0%
what have fun with some of these so one
 

00:04:25.080 --> 00:04:26.990 align:start position:0%
what have fun with some of these so one
I<00:04:25.160><c> want</c><00:04:25.280><c> to</c><00:04:25.360><c> show</c><00:04:25.560><c> you</c><00:04:25.720><c> is</c><00:04:25.880><c> text</c><00:04:26.120><c> to</c><00:04:26.280><c> 3D</c><00:04:26.880><c> so</c>

00:04:26.990 --> 00:04:27.000 align:start position:0%
I want to show you is text to 3D so
 

00:04:27.000 --> 00:04:28.590 align:start position:0%
I want to show you is text to 3D so
whenever<00:04:27.280><c> you</c><00:04:27.440><c> get</c><00:04:27.560><c> to</c><00:04:27.840><c> here</c><00:04:28.320><c> as</c><00:04:28.400><c> you'll</c>

00:04:28.590 --> 00:04:28.600 align:start position:0%
whenever you get to here as you'll
 

00:04:28.600 --> 00:04:30.590 align:start position:0%
whenever you get to here as you'll
notice<00:04:28.880><c> on</c><00:04:29.000><c> the</c><00:04:29.160><c> top</c><00:04:29.360><c> right</c><00:04:29.520><c> there</c><00:04:29.840><c> is</c><00:04:30.000><c> no</c><00:04:30.280><c> drop</c>

00:04:30.590 --> 00:04:30.600 align:start position:0%
notice on the top right there is no drop
 

00:04:30.600 --> 00:04:33.510 align:start position:0%
notice on the top right there is no drop
down<00:04:30.880><c> for</c><00:04:31.240><c> to</c><00:04:31.479><c> deploy</c><00:04:32.199><c> an</c><00:04:32.440><c> inference</c><00:04:33.000><c> API</c>

00:04:33.510 --> 00:04:33.520 align:start position:0%
down for to deploy an inference API
 

00:04:33.520 --> 00:04:36.350 align:start position:0%
down for to deploy an inference API
serverless<00:04:34.520><c> type</c><00:04:34.840><c> of</c><00:04:35.039><c> this</c><00:04:35.199><c> model</c><00:04:35.800><c> so</c><00:04:36.080><c> if</c><00:04:36.199><c> they</c>

00:04:36.350 --> 00:04:36.360 align:start position:0%
serverless type of this model so if they
 

00:04:36.360 --> 00:04:37.550 align:start position:0%
serverless type of this model so if they
don't<00:04:36.600><c> have</c><00:04:36.759><c> that</c><00:04:36.880><c> and</c><00:04:36.960><c> a</c><00:04:37.039><c> lot</c><00:04:37.160><c> of</c><00:04:37.240><c> them</c><00:04:37.360><c> aren't</c>

00:04:37.550 --> 00:04:37.560 align:start position:0%
don't have that and a lot of them aren't
 

00:04:37.560 --> 00:04:39.350 align:start position:0%
don't have that and a lot of them aren't
going<00:04:37.800><c> to</c><00:04:38.440><c> um</c><00:04:38.600><c> well</c><00:04:38.720><c> I</c><00:04:38.759><c> don't</c><00:04:38.919><c> say</c><00:04:39.039><c> a</c><00:04:39.120><c> lot</c><00:04:39.280><c> of</c>

00:04:39.350 --> 00:04:39.360 align:start position:0%
going to um well I don't say a lot of
 

00:04:39.360 --> 00:04:41.189 align:start position:0%
going to um well I don't say a lot of
them<00:04:39.680><c> but</c><00:04:39.800><c> a</c><00:04:39.880><c> lot</c><00:04:40.000><c> of</c><00:04:40.080><c> the</c><00:04:40.240><c> popular</c><00:04:40.639><c> ones</c><00:04:40.960><c> are</c>

00:04:41.189 --> 00:04:41.199 align:start position:0%
them but a lot of the popular ones are
 

00:04:41.199 --> 00:04:43.550 align:start position:0%
them but a lot of the popular ones are
going<00:04:41.400><c> to</c><00:04:41.600><c> have</c><00:04:41.720><c> it</c><00:04:41.919><c> right</c><00:04:42.080><c> this</c><00:04:42.199><c> isn't</c><00:04:42.960><c> um</c><00:04:43.280><c> as</c>

00:04:43.550 --> 00:04:43.560 align:start position:0%
going to have it right this isn't um as
 

00:04:43.560 --> 00:04:45.469 align:start position:0%
going to have it right this isn't um as
popular<00:04:43.880><c> as</c><00:04:44.000><c> like</c><00:04:44.120><c> llama</c><00:04:44.440><c> 3</c><00:04:44.800><c> so</c><00:04:44.960><c> if</c><00:04:45.039><c> you</c><00:04:45.160><c> scroll</c>

00:04:45.469 --> 00:04:45.479 align:start position:0%
popular as like llama 3 so if you scroll
 

00:04:45.479 --> 00:04:47.469 align:start position:0%
popular as like llama 3 so if you scroll
down<00:04:45.800><c> though</c><00:04:46.000><c> that's</c><00:04:46.280><c> okay</c><00:04:46.560><c> you</c><00:04:46.680><c> can</c><00:04:46.960><c> still</c>

00:04:47.469 --> 00:04:47.479 align:start position:0%
down though that's okay you can still
 

00:04:47.479 --> 00:04:49.830 align:start position:0%
down though that's okay you can still
use<00:04:47.960><c> this</c><00:04:48.360><c> you'll</c><00:04:48.600><c> just</c><00:04:48.759><c> have</c><00:04:48.880><c> to</c><00:04:49.520><c> here</c><00:04:49.720><c> you</c>

00:04:49.830 --> 00:04:49.840 align:start position:0%
use this you'll just have to here you
 

00:04:49.840 --> 00:04:51.990 align:start position:0%
use this you'll just have to here you
can<00:04:49.960><c> just</c><00:04:50.160><c> copy</c><00:04:50.520><c> this</c><00:04:50.759><c> python</c><00:04:51.120><c> code</c><00:04:51.680><c> make</c><00:04:51.840><c> sure</c>

00:04:51.990 --> 00:04:52.000 align:start position:0%
can just copy this python code make sure
 

00:04:52.000 --> 00:04:54.310 align:start position:0%
can just copy this python code make sure
you<00:04:52.199><c> install</c><00:04:52.600><c> the</c><00:04:52.759><c> necessary</c><00:04:53.199><c> libraries</c><00:04:54.160><c> put</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
you install the necessary libraries put
 

00:04:54.320 --> 00:04:56.350 align:start position:0%
you install the necessary libraries put
that<00:04:54.520><c> into</c><00:04:54.759><c> your</c><00:04:55.039><c> project</c><00:04:55.720><c> after</c><00:04:55.880><c> you</c><00:04:56.039><c> install</c>

00:04:56.350 --> 00:04:56.360 align:start position:0%
that into your project after you install
 

00:04:56.360 --> 00:04:58.070 align:start position:0%
that into your project after you install
libraries<00:04:56.840><c> and</c><00:04:56.919><c> then</c><00:04:57.120><c> run</c><00:04:57.360><c> it</c><00:04:57.680><c> and</c><00:04:57.800><c> you</c><00:04:57.880><c> can</c>

00:04:58.070 --> 00:04:58.080 align:start position:0%
libraries and then run it and you can
 

00:04:58.080 --> 00:05:00.029 align:start position:0%
libraries and then run it and you can
still<00:04:58.400><c> produce</c><00:04:58.840><c> the</c><00:04:58.960><c> same</c><00:04:59.160><c> results</c><00:04:59.800><c> just</c><00:04:59.880><c> be</c>

00:05:00.029 --> 00:05:00.039 align:start position:0%
still produce the same results just be
 

00:05:00.039 --> 00:05:02.550 align:start position:0%
still produce the same results just be
through<00:05:00.360><c> Hardware</c><00:05:01.000><c> not</c><00:05:01.280><c> serverless</c><00:05:02.280><c> again</c>

00:05:02.550 --> 00:05:02.560 align:start position:0%
through Hardware not serverless again
 

00:05:02.560 --> 00:05:05.430 align:start position:0%
through Hardware not serverless again
there<00:05:02.639><c> are</c><00:05:02.840><c> over</c><00:05:03.400><c> 680,000</c><00:05:04.400><c> models</c><00:05:04.960><c> as</c><00:05:05.120><c> of</c>

00:05:05.430 --> 00:05:05.440 align:start position:0%
there are over 680,000 models as of
 

00:05:05.440 --> 00:05:07.830 align:start position:0%
there are over 680,000 models as of
right<00:05:05.639><c> now</c><00:05:05.880><c> of</c><00:05:06.039><c> this</c><00:05:06.240><c> recording</c><00:05:07.080><c> on</c><00:05:07.440><c> hugging</c>

00:05:07.830 --> 00:05:07.840 align:start position:0%
right now of this recording on hugging
 

00:05:07.840 --> 00:05:10.110 align:start position:0%
right now of this recording on hugging
face<00:05:08.360><c> have</c><00:05:08.479><c> some</c><00:05:08.680><c> fun</c><00:05:08.880><c> with</c><00:05:09.000><c> it</c><00:05:09.320><c> try</c><00:05:09.639><c> all</c><00:05:09.919><c> these</c>

00:05:10.110 --> 00:05:10.120 align:start position:0%
face have some fun with it try all these
 

00:05:10.120 --> 00:05:11.710 align:start position:0%
face have some fun with it try all these
different<00:05:10.360><c> types</c><00:05:10.560><c> of</c><00:05:10.720><c> models</c><00:05:11.320><c> and</c><00:05:11.479><c> you</c><00:05:11.560><c> can</c>

00:05:11.710 --> 00:05:11.720 align:start position:0%
different types of models and you can
 

00:05:11.720 --> 00:05:14.029 align:start position:0%
different types of models and you can
use<00:05:11.960><c> these</c><00:05:12.199><c> as</c><00:05:12.400><c> tools</c><00:05:13.039><c> or</c><00:05:13.320><c> you</c><00:05:13.440><c> can</c><00:05:13.600><c> Implement</c>

00:05:14.029 --> 00:05:14.039 align:start position:0%
use these as tools or you can Implement
 

00:05:14.039 --> 00:05:16.270 align:start position:0%
use these as tools or you can Implement
these<00:05:14.320><c> as</c><00:05:14.520><c> part</c><00:05:14.680><c> of</c><00:05:14.880><c> AI</c><00:05:15.280><c> agents</c><00:05:15.880><c> and</c><00:05:16.000><c> if</c><00:05:16.080><c> you've</c>

00:05:16.270 --> 00:05:16.280 align:start position:0%
these as part of AI agents and if you've
 

00:05:16.280 --> 00:05:18.670 align:start position:0%
these as part of AI agents and if you've
never<00:05:16.520><c> implemented</c><00:05:17.120><c> or</c><00:05:17.479><c> created</c><00:05:17.840><c> an</c><00:05:18.000><c> AI</c><00:05:18.319><c> agent</c>

00:05:18.670 --> 00:05:18.680 align:start position:0%
never implemented or created an AI agent
 

00:05:18.680 --> 00:05:20.830 align:start position:0%
never implemented or created an AI agent
before<00:05:19.520><c> watch</c><00:05:19.840><c> this</c><00:05:20.039><c> video</c><00:05:20.440><c> and</c><00:05:20.520><c> I'll</c><00:05:20.680><c> show</c>

00:05:20.830 --> 00:05:20.840 align:start position:0%
before watch this video and I'll show
 

00:05:20.840 --> 00:05:22.749 align:start position:0%
before watch this video and I'll show
you<00:05:21.039><c> how</c><00:05:21.400><c> thanks</c><00:05:21.600><c> for</c><00:05:21.800><c> watching</c><00:05:22.280><c> I'll</c><00:05:22.400><c> see</c><00:05:22.560><c> you</c>

00:05:22.749 --> 00:05:22.759 align:start position:0%
you how thanks for watching I'll see you
 

00:05:22.759 --> 00:05:25.280 align:start position:0%
you how thanks for watching I'll see you
next<00:05:22.960><c> video</c>

