WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:01.670 align:start position:0%
 
in<00:00:00.780><c> today's</c><00:00:00.900><c> video</c><00:00:01.140><c> we're</c><00:00:01.380><c> going</c><00:00:01.560><c> to</c><00:00:01.620><c> be</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
in today's video we're going to be
 

00:00:01.680 --> 00:00:02.869 align:start position:0%
in today's video we're going to be
talking<00:00:01.800><c> about</c><00:00:01.920><c> the</c><00:00:02.220><c> first</c><00:00:02.340><c> letter</c><00:00:02.520><c> in</c><00:00:02.760><c> the</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
talking about the first letter in the
 

00:00:02.879 --> 00:00:05.090 align:start position:0%
talking about the first letter in the
acronym<00:00:03.240><c> for</c><00:00:03.360><c> solid</c><00:00:03.659><c> principles</c><00:00:04.100><c> single</c>

00:00:05.090 --> 00:00:05.100 align:start position:0%
acronym for solid principles single
 

00:00:05.100 --> 00:00:07.309 align:start position:0%
acronym for solid principles single
responsibility<00:00:05.759><c> principle</c><00:00:06.420><c> this</c><00:00:07.140><c> design</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
responsibility principle this design
 

00:00:07.319 --> 00:00:08.870 align:start position:0%
responsibility principle this design
principle<00:00:07.740><c> suggests</c><00:00:08.280><c> that</c><00:00:08.400><c> a</c><00:00:08.580><c> class</c><00:00:08.700><c> or</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
principle suggests that a class or
 

00:00:08.880 --> 00:00:11.509 align:start position:0%
principle suggests that a class or
module<00:00:09.300><c> should</c><00:00:09.720><c> only</c><00:00:09.840><c> have</c><00:00:10.080><c> one</c><00:00:10.679><c> and</c><00:00:10.980><c> only</c><00:00:11.219><c> one</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
module should only have one and only one
 

00:00:11.519 --> 00:00:13.249 align:start position:0%
module should only have one and only one
reason<00:00:11.820><c> to</c><00:00:12.120><c> change</c><00:00:12.300><c> another</c><00:00:12.719><c> way</c><00:00:12.960><c> to</c><00:00:13.080><c> put</c><00:00:13.200><c> it</c>

00:00:13.249 --> 00:00:13.259 align:start position:0%
reason to change another way to put it
 

00:00:13.259 --> 00:00:14.089 align:start position:0%
reason to change another way to put it
is<00:00:13.440><c> that</c><00:00:13.559><c> it</c><00:00:13.620><c> should</c><00:00:13.799><c> have</c><00:00:13.920><c> one</c>

00:00:14.089 --> 00:00:14.099 align:start position:0%
is that it should have one
 

00:00:14.099 --> 00:00:16.070 align:start position:0%
is that it should have one
responsibility<00:00:14.639><c> and</c><00:00:15.179><c> that's</c><00:00:15.360><c> it</c><00:00:15.540><c> let's</c><00:00:15.900><c> get</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
responsibility and that's it let's get
 

00:00:16.080 --> 00:00:17.570 align:start position:0%
responsibility and that's it let's get
into<00:00:16.199><c> and</c><00:00:16.440><c> take</c><00:00:16.619><c> an</c><00:00:16.680><c> example</c><00:00:16.920><c> of</c><00:00:17.100><c> what</c><00:00:17.279><c> in</c><00:00:17.460><c> the</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
into and take an example of what in the
 

00:00:17.580 --> 00:00:19.250 align:start position:0%
into and take an example of what in the
world<00:00:17.640><c> this</c><00:00:17.880><c> means</c><00:00:18.180><c> here</c><00:00:18.480><c> I</c><00:00:18.720><c> have</c><00:00:18.840><c> an</c><00:00:18.960><c> employee</c>

00:00:19.250 --> 00:00:19.260 align:start position:0%
world this means here I have an employee
 

00:00:19.260 --> 00:00:21.170 align:start position:0%
world this means here I have an employee
class<00:00:19.440><c> that</c><00:00:19.680><c> does</c><00:00:19.859><c> three</c><00:00:20.100><c> things</c><00:00:20.400><c> it</c><00:00:21.000><c> takes</c>

00:00:21.170 --> 00:00:21.180 align:start position:0%
class that does three things it takes
 

00:00:21.180 --> 00:00:23.210 align:start position:0%
class that does three things it takes
employees<00:00:21.600><c> information</c><00:00:21.900><c> it</c><00:00:22.800><c> takes</c><00:00:22.980><c> their</c>

00:00:23.210 --> 00:00:23.220 align:start position:0%
employees information it takes their
 

00:00:23.220 --> 00:00:25.609 align:start position:0%
employees information it takes their
salaries<00:00:23.760><c> it</c><00:00:24.539><c> calculates</c><00:00:24.960><c> their</c><00:00:25.199><c> salaries</c>

00:00:25.609 --> 00:00:25.619 align:start position:0%
salaries it calculates their salaries
 

00:00:25.619 --> 00:00:28.490 align:start position:0%
salaries it calculates their salaries
and<00:00:26.220><c> then</c><00:00:26.340><c> it</c><00:00:26.580><c> also</c><00:00:27.000><c> will</c><00:00:27.420><c> send</c><00:00:27.840><c> an</c><00:00:28.380><c> email</c>

00:00:28.490 --> 00:00:28.500 align:start position:0%
and then it also will send an email
 

00:00:28.500 --> 00:00:30.470 align:start position:0%
and then it also will send an email
based<00:00:29.039><c> on</c><00:00:29.160><c> their</c><00:00:29.340><c> salary</c><00:00:29.519><c> so</c><00:00:30.180><c> you</c><00:00:30.300><c> might</c><00:00:30.420><c> be</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
based on their salary so you might be
 

00:00:30.480 --> 00:00:31.669 align:start position:0%
based on their salary so you might be
wondering<00:00:30.660><c> what's</c><00:00:30.960><c> the</c><00:00:31.140><c> problem</c><00:00:31.260><c> with</c><00:00:31.500><c> this</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
wondering what's the problem with this
 

00:00:31.679 --> 00:00:32.930 align:start position:0%
wondering what's the problem with this
well<00:00:32.040><c> the</c><00:00:32.160><c> first</c><00:00:32.279><c> thing</c><00:00:32.460><c> is</c><00:00:32.579><c> it</c><00:00:32.820><c> doesn't</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
well the first thing is it doesn't
 

00:00:32.940 --> 00:00:35.030 align:start position:0%
well the first thing is it doesn't
adhere<00:00:33.480><c> to</c><00:00:33.960><c> the</c><00:00:34.079><c> principle</c><00:00:34.380><c> that</c><00:00:34.620><c> a</c><00:00:34.739><c> classroom</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
adhere to the principle that a classroom
 

00:00:35.040 --> 00:00:36.530 align:start position:0%
adhere to the principle that a classroom
module<00:00:35.399><c> should</c><00:00:35.640><c> have</c><00:00:35.820><c> one</c><00:00:36.059><c> responsibility</c>

00:00:36.530 --> 00:00:36.540 align:start position:0%
module should have one responsibility
 

00:00:36.540 --> 00:00:38.630 align:start position:0%
module should have one responsibility
this<00:00:37.320><c> class</c><00:00:37.500><c> does</c><00:00:37.800><c> three</c><00:00:38.100><c> different</c><00:00:38.340><c> things</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
this class does three different things
 

00:00:38.640 --> 00:00:40.490 align:start position:0%
this class does three different things
takes<00:00:39.059><c> the</c><00:00:39.239><c> information</c><00:00:39.360><c> calculates</c><00:00:40.320><c> a</c>

00:00:40.490 --> 00:00:40.500 align:start position:0%
takes the information calculates a
 

00:00:40.500 --> 00:00:42.229 align:start position:0%
takes the information calculates a
salary<00:00:40.620><c> and</c><00:00:40.920><c> can</c><00:00:41.040><c> send</c><00:00:41.219><c> emails</c><00:00:41.760><c> if</c><00:00:42.120><c> there</c>

00:00:42.229 --> 00:00:42.239 align:start position:0%
salary and can send emails if there
 

00:00:42.239 --> 00:00:43.250 align:start position:0%
salary and can send emails if there
happens<00:00:42.420><c> to</c><00:00:42.600><c> be</c><00:00:42.660><c> a</c><00:00:42.780><c> change</c><00:00:42.899><c> of</c><00:00:43.020><c> salary</c>

00:00:43.250 --> 00:00:43.260 align:start position:0%
happens to be a change of salary
 

00:00:43.260 --> 00:00:44.930 align:start position:0%
happens to be a change of salary
calculation<00:00:43.739><c> logic</c><00:00:44.160><c> or</c><00:00:44.340><c> email</c><00:00:44.460><c> sending</c>

00:00:44.930 --> 00:00:44.940 align:start position:0%
calculation logic or email sending
 

00:00:44.940 --> 00:00:46.790 align:start position:0%
calculation logic or email sending
functionality<00:00:45.420><c> it</c><00:00:46.140><c> would</c><00:00:46.260><c> impact</c><00:00:46.500><c> the</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
functionality it would impact the
 

00:00:46.800 --> 00:00:49.250 align:start position:0%
functionality it would impact the
employee<00:00:47.100><c> class</c><00:00:47.280><c> as</c><00:00:47.700><c> a</c><00:00:47.879><c> whole</c><00:00:48.059><c> which</c><00:00:48.899><c> really</c>

00:00:49.250 --> 00:00:49.260 align:start position:0%
employee class as a whole which really
 

00:00:49.260 --> 00:00:51.350 align:start position:0%
employee class as a whole which really
this<00:00:49.800><c> employee</c><00:00:50.219><c> class</c><00:00:50.579><c> should</c><00:00:50.940><c> only</c><00:00:51.120><c> be</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
this employee class should only be
 

00:00:51.360 --> 00:00:52.850 align:start position:0%
this employee class should only be
focused<00:00:51.780><c> and</c><00:00:51.960><c> worried</c><00:00:52.320><c> about</c><00:00:52.559><c> the</c>

00:00:52.850 --> 00:00:52.860 align:start position:0%
focused and worried about the
 

00:00:52.860 --> 00:00:55.729 align:start position:0%
focused and worried about the
information<00:00:53.100><c> of</c><00:00:53.940><c> that</c><00:00:54.180><c> employee</c><00:00:55.079><c> now</c><00:00:55.559><c> yes</c>

00:00:55.729 --> 00:00:55.739 align:start position:0%
information of that employee now yes
 

00:00:55.739 --> 00:00:57.590 align:start position:0%
information of that employee now yes
this<00:00:55.920><c> is</c><00:00:56.039><c> a</c><00:00:56.219><c> simple</c><00:00:56.280><c> example</c><00:00:56.760><c> but</c><00:00:57.300><c> this</c><00:00:57.480><c> has</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
this is a simple example but this has
 

00:00:57.600 --> 00:01:00.229 align:start position:0%
this is a simple example but this has
something<00:00:57.840><c> called</c><00:00:58.140><c> Low</c><00:00:58.680><c> cohesion</c><00:00:59.280><c> which</c><00:01:00.000><c> just</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
something called Low cohesion which just
 

00:01:00.239 --> 00:01:01.790 align:start position:0%
something called Low cohesion which just
means<00:01:00.480><c> that</c><00:01:00.660><c> the</c><00:01:00.780><c> class</c><00:01:00.960><c> has</c><00:01:01.199><c> unrelated</c>

00:01:01.790 --> 00:01:01.800 align:start position:0%
means that the class has unrelated
 

00:01:01.800 --> 00:01:03.889 align:start position:0%
means that the class has unrelated
responsibilities<00:01:02.579><c> and</c><00:01:03.359><c> it</c><00:01:03.539><c> could</c><00:01:03.600><c> become</c>

00:01:03.889 --> 00:01:03.899 align:start position:0%
responsibilities and it could become
 

00:01:03.899 --> 00:01:05.810 align:start position:0%
responsibilities and it could become
challenging<00:01:04.320><c> to</c><00:01:04.559><c> understand</c><00:01:04.739><c> the</c><00:01:05.400><c> reason</c><00:01:05.519><c> for</c>

00:01:05.810 --> 00:01:05.820 align:start position:0%
challenging to understand the reason for
 

00:01:05.820 --> 00:01:08.149 align:start position:0%
challenging to understand the reason for
the<00:01:05.939><c> class's</c><00:01:06.299><c> behavior</c><00:01:06.840><c> because</c><00:01:07.680><c> it's</c><00:01:07.979><c> just</c>

00:01:08.149 --> 00:01:08.159 align:start position:0%
the class's behavior because it's just
 

00:01:08.159 --> 00:01:10.910 align:start position:0%
the class's behavior because it's just
simply<00:01:08.460><c> doing</c><00:01:08.700><c> too</c><00:01:09.060><c> many</c><00:01:09.240><c> things</c>

00:01:10.910 --> 00:01:10.920 align:start position:0%
simply doing too many things
 

00:01:10.920 --> 00:01:13.609 align:start position:0%
simply doing too many things
it<00:01:11.400><c> also</c><00:01:11.640><c> has</c><00:01:11.760><c> poor</c><00:01:12.060><c> reusability</c>

00:01:13.609 --> 00:01:13.619 align:start position:0%
it also has poor reusability
 

00:01:13.619 --> 00:01:15.830 align:start position:0%
it also has poor reusability
the<00:01:14.100><c> salary</c><00:01:14.220><c> calculation</c><00:01:14.820><c> and</c><00:01:15.119><c> email</c><00:01:15.299><c> sending</c>

00:01:15.830 --> 00:01:15.840 align:start position:0%
the salary calculation and email sending
 

00:01:15.840 --> 00:01:18.170 align:start position:0%
the salary calculation and email sending
logic<00:01:16.200><c> is</c><00:01:16.439><c> tightly</c><00:01:16.799><c> coupled</c><00:01:17.280><c> which</c><00:01:18.060><c> would</c>

00:01:18.170 --> 00:01:18.180 align:start position:0%
logic is tightly coupled which would
 

00:01:18.180 --> 00:01:20.390 align:start position:0%
logic is tightly coupled which would
make<00:01:18.360><c> it</c><00:01:18.540><c> hard</c><00:01:18.780><c> to</c><00:01:19.020><c> reuse</c><00:01:19.320><c> them</c><00:01:19.500><c> independently</c>

00:01:20.390 --> 00:01:20.400 align:start position:0%
make it hard to reuse them independently
 

00:01:20.400 --> 00:01:22.370 align:start position:0%
make it hard to reuse them independently
if<00:01:20.820><c> you</c><00:01:20.880><c> needed</c><00:01:21.119><c> to</c><00:01:21.240><c> calculate</c><00:01:21.540><c> salary</c><00:01:21.780><c> for</c><00:01:22.259><c> a</c>

00:01:22.370 --> 00:01:22.380 align:start position:0%
if you needed to calculate salary for a
 

00:01:22.380 --> 00:01:24.649 align:start position:0%
if you needed to calculate salary for a
different<00:01:22.560><c> context</c><00:01:23.159><c> or</c><00:01:23.820><c> send</c><00:01:24.060><c> emails</c><00:01:24.479><c> for</c>

00:01:24.649 --> 00:01:24.659 align:start position:0%
different context or send emails for
 

00:01:24.659 --> 00:01:26.810 align:start position:0%
different context or send emails for
other<00:01:24.900><c> purposes</c><00:01:25.439><c> you</c><00:01:26.040><c> would</c><00:01:26.159><c> need</c><00:01:26.340><c> to</c><00:01:26.460><c> extract</c>

00:01:26.810 --> 00:01:26.820 align:start position:0%
other purposes you would need to extract
 

00:01:26.820 --> 00:01:29.050 align:start position:0%
other purposes you would need to extract
and<00:01:27.000><c> modify</c><00:01:27.420><c> the</c><00:01:27.659><c> code</c><00:01:27.900><c> from</c><00:01:28.439><c> this</c><00:01:28.740><c> class</c>

00:01:29.050 --> 00:01:29.060 align:start position:0%
and modify the code from this class
 

00:01:29.060 --> 00:01:31.070 align:start position:0%
and modify the code from this class
which<00:01:30.060><c> is</c><00:01:30.420><c> then</c><00:01:30.540><c> going</c><00:01:30.720><c> to</c><00:01:30.780><c> be</c><00:01:30.840><c> code</c>

00:01:31.070 --> 00:01:31.080 align:start position:0%
which is then going to be code
 

00:01:31.080 --> 00:01:33.050 align:start position:0%
which is then going to be code
duplication<00:01:31.740><c> and</c><00:01:32.340><c> it's</c><00:01:32.520><c> going</c><00:01:32.700><c> to</c><00:01:32.759><c> decrease</c>

00:01:33.050 --> 00:01:33.060 align:start position:0%
duplication and it's going to decrease
 

00:01:33.060 --> 00:01:35.749 align:start position:0%
duplication and it's going to decrease
the<00:01:33.780><c> maintainability</c><00:01:34.500><c> of</c><00:01:34.920><c> it</c><00:01:35.040><c> now</c><00:01:35.520><c> here's</c><00:01:35.700><c> a</c>

00:01:35.749 --> 00:01:35.759 align:start position:0%
the maintainability of it now here's a
 

00:01:35.759 --> 00:01:36.830 align:start position:0%
the maintainability of it now here's a
couple<00:01:35.880><c> things</c><00:01:36.060><c> I</c><00:01:36.240><c> think</c><00:01:36.360><c> you</c><00:01:36.479><c> should</c><00:01:36.659><c> know</c>

00:01:36.830 --> 00:01:36.840 align:start position:0%
couple things I think you should know
 

00:01:36.840 --> 00:01:39.350 align:start position:0%
couple things I think you should know
before<00:01:37.439><c> we</c><00:01:37.740><c> get</c><00:01:37.920><c> into</c><00:01:38.040><c> solving</c><00:01:38.640><c> or</c><00:01:38.939><c> one</c><00:01:39.180><c> of</c><00:01:39.299><c> the</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
before we get into solving or one of the
 

00:01:39.360 --> 00:01:41.390 align:start position:0%
before we get into solving or one of the
solutions<00:01:39.659><c> for</c><00:01:40.020><c> this</c><00:01:40.259><c> kind</c><00:01:40.380><c> of</c><00:01:40.500><c> problem</c><00:01:40.680><c> a</c>

00:01:41.390 --> 00:01:41.400 align:start position:0%
solutions for this kind of problem a
 

00:01:41.400 --> 00:01:43.969 align:start position:0%
solutions for this kind of problem a
good<00:01:41.579><c> note</c><00:01:41.759><c> is</c><00:01:42.299><c> don't</c><00:01:42.780><c> put</c><00:01:43.020><c> functions</c><00:01:43.619><c> that</c>

00:01:43.969 --> 00:01:43.979 align:start position:0%
good note is don't put functions that
 

00:01:43.979 --> 00:01:46.310 align:start position:0%
good note is don't put functions that
change<00:01:44.159><c> for</c><00:01:44.520><c> different</c><00:01:44.759><c> reasons</c><00:01:45.360><c> in</c><00:01:46.020><c> the</c><00:01:46.140><c> same</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
change for different reasons in the same
 

00:01:46.320 --> 00:01:49.850 align:start position:0%
change for different reasons in the same
class<00:01:46.619><c> now</c><00:01:47.460><c> to</c><00:01:47.939><c> be</c><00:01:48.119><c> clear</c><00:01:48.360><c> this</c><00:01:49.320><c> doesn't</c><00:01:49.500><c> mean</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
class now to be clear this doesn't mean
 

00:01:49.860 --> 00:01:52.670 align:start position:0%
class now to be clear this doesn't mean
you<00:01:50.340><c> only</c><00:01:50.520><c> need</c><00:01:50.700><c> to</c><00:01:50.820><c> have</c><00:01:51.000><c> one</c><00:01:51.420><c> method</c><00:01:51.899><c> per</c>

00:01:52.670 --> 00:01:52.680 align:start position:0%
you only need to have one method per
 

00:01:52.680 --> 00:01:54.649 align:start position:0%
you only need to have one method per
class<00:01:52.979><c> you</c><00:01:53.579><c> can</c><00:01:53.640><c> have</c><00:01:53.820><c> multiple</c><00:01:54.060><c> methods</c><00:01:54.540><c> per</c>

00:01:54.649 --> 00:01:54.659 align:start position:0%
class you can have multiple methods per
 

00:01:54.659 --> 00:01:57.710 align:start position:0%
class you can have multiple methods per
class<00:01:54.899><c> as</c><00:01:55.680><c> long</c><00:01:55.860><c> as</c><00:01:56.159><c> they</c><00:01:56.520><c> are</c><00:01:56.939><c> for</c><00:01:57.180><c> the</c><00:01:57.479><c> same</c>

00:01:57.710 --> 00:01:57.720 align:start position:0%
class as long as they are for the same
 

00:01:57.720 --> 00:01:59.990 align:start position:0%
class as long as they are for the same
thing<00:01:58.020><c> they</c><00:01:58.439><c> should</c><00:01:58.680><c> all</c><00:01:58.920><c> be</c><00:01:59.220><c> for</c><00:01:59.520><c> the</c><00:01:59.759><c> same</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
thing they should all be for the same
 

00:02:00.000 --> 00:02:02.330 align:start position:0%
thing they should all be for the same
responsibility<00:02:00.899><c> of</c><00:02:01.439><c> what</c><00:02:01.740><c> that</c><00:02:01.979><c> class</c><00:02:02.100><c> is</c>

00:02:02.330 --> 00:02:02.340 align:start position:0%
responsibility of what that class is
 

00:02:02.340 --> 00:02:03.649 align:start position:0%
responsibility of what that class is
doing<00:02:02.520><c> something</c><00:02:03.060><c> else</c><00:02:03.240><c> that</c><00:02:03.479><c> you</c><00:02:03.600><c> should</c>

00:02:03.649 --> 00:02:03.659 align:start position:0%
doing something else that you should
 

00:02:03.659 --> 00:02:04.609 align:start position:0%
doing something else that you should
know<00:02:03.780><c> that's</c><00:02:03.960><c> not</c><00:02:04.140><c> really</c><00:02:04.259><c> a</c><00:02:04.500><c> design</c>

00:02:04.609 --> 00:02:04.619 align:start position:0%
know that's not really a design
 

00:02:04.619 --> 00:02:06.289 align:start position:0%
know that's not really a design
principle<00:02:05.100><c> but</c><00:02:05.399><c> it's</c><00:02:05.579><c> more</c><00:02:05.700><c> like</c><00:02:05.880><c> a</c><00:02:06.119><c> best</c>

00:02:06.289 --> 00:02:06.299 align:start position:0%
principle but it's more like a best
 

00:02:06.299 --> 00:02:09.109 align:start position:0%
principle but it's more like a best
practice<00:02:06.600><c> is</c><00:02:07.259><c> called</c><00:02:07.439><c> dry</c><00:02:07.799><c> or</c><00:02:08.399><c> don't</c><00:02:08.819><c> repeat</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
practice is called dry or don't repeat
 

00:02:09.119 --> 00:02:10.370 align:start position:0%
practice is called dry or don't repeat
yourself

00:02:10.370 --> 00:02:10.380 align:start position:0%
yourself
 

00:02:10.380 --> 00:02:13.010 align:start position:0%
yourself
keeping<00:02:11.039><c> application</c><00:02:11.459><c> dry</c><00:02:11.879><c> will</c><00:02:12.660><c> typically</c>

00:02:13.010 --> 00:02:13.020 align:start position:0%
keeping application dry will typically
 

00:02:13.020 --> 00:02:15.710 align:start position:0%
keeping application dry will typically
not<00:02:13.379><c> contain</c><00:02:13.739><c> any</c><00:02:13.980><c> code</c><00:02:14.280><c> that</c><00:02:14.760><c> is</c><00:02:14.940><c> not</c><00:02:15.180><c> beyond</c>

00:02:15.710 --> 00:02:15.720 align:start position:0%
not contain any code that is not beyond
 

00:02:15.720 --> 00:02:18.290 align:start position:0%
not contain any code that is not beyond
the<00:02:15.959><c> behavior</c><00:02:16.319><c> it's</c><00:02:16.560><c> supposed</c><00:02:16.800><c> to</c><00:02:16.980><c> perform</c><00:02:17.300><c> if</c>

00:02:18.290 --> 00:02:18.300 align:start position:0%
the behavior it's supposed to perform if
 

00:02:18.300 --> 00:02:21.470 align:start position:0%
the behavior it's supposed to perform if
the<00:02:18.480><c> code</c><00:02:18.720><c> for</c><00:02:19.260><c> one</c><00:02:19.440><c> responsibility</c><00:02:20.160><c> is</c><00:02:20.819><c> in</c><00:02:21.180><c> a</c>

00:02:21.470 --> 00:02:21.480 align:start position:0%
the code for one responsibility is in a
 

00:02:21.480 --> 00:02:23.570 align:start position:0%
the code for one responsibility is in a
single<00:02:21.780><c> place</c><00:02:22.020><c> then</c><00:02:22.620><c> we</c><00:02:22.860><c> only</c><00:02:22.980><c> need</c><00:02:23.220><c> to</c><00:02:23.340><c> update</c>

00:02:23.570 --> 00:02:23.580 align:start position:0%
single place then we only need to update
 

00:02:23.580 --> 00:02:26.089 align:start position:0%
single place then we only need to update
any<00:02:23.760><c> logic</c><00:02:24.239><c> to</c><00:02:24.420><c> that</c><00:02:24.660><c> code</c><00:02:25.020><c> without</c><00:02:25.620><c> touching</c>

00:02:26.089 --> 00:02:26.099 align:start position:0%
any logic to that code without touching
 

00:02:26.099 --> 00:02:27.830 align:start position:0%
any logic to that code without touching
any<00:02:26.340><c> other</c><00:02:26.459><c> class</c><00:02:26.819><c> this</c><00:02:27.480><c> is</c><00:02:27.599><c> something</c><00:02:27.720><c> else</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
any other class this is something else
 

00:02:27.840 --> 00:02:29.390 align:start position:0%
any other class this is something else
to<00:02:28.020><c> think</c><00:02:28.140><c> about</c><00:02:28.260><c> so</c><00:02:28.680><c> let's</c><00:02:28.920><c> see</c><00:02:29.040><c> what</c><00:02:29.160><c> we</c><00:02:29.280><c> can</c>

00:02:29.390 --> 00:02:29.400 align:start position:0%
to think about so let's see what we can
 

00:02:29.400 --> 00:02:30.650 align:start position:0%
to think about so let's see what we can
do<00:02:29.459><c> about</c><00:02:29.580><c> it</c><00:02:29.819><c> what</c><00:02:30.180><c> we're</c><00:02:30.300><c> going</c><00:02:30.360><c> to</c><00:02:30.480><c> do</c><00:02:30.540><c> to</c>

00:02:30.650 --> 00:02:30.660 align:start position:0%
do about it what we're going to do to
 

00:02:30.660 --> 00:02:32.449 align:start position:0%
do about it what we're going to do to
fix<00:02:30.840><c> our</c><00:02:31.020><c> previous</c><00:02:31.200><c> employee</c><00:02:31.680><c> class</c><00:02:31.860><c> which</c>

00:02:32.449 --> 00:02:32.459 align:start position:0%
fix our previous employee class which
 

00:02:32.459 --> 00:02:34.369 align:start position:0%
fix our previous employee class which
had<00:02:32.700><c> three</c><00:02:33.239><c> different</c><00:02:33.540><c> operations</c><00:02:34.020><c> it</c><00:02:34.260><c> was</c>

00:02:34.369 --> 00:02:34.379 align:start position:0%
had three different operations it was
 

00:02:34.379 --> 00:02:37.010 align:start position:0%
had three different operations it was
trying<00:02:34.560><c> to</c><00:02:34.680><c> perform</c><00:02:34.860><c> in</c><00:02:35.400><c> a</c><00:02:35.520><c> single</c><00:02:35.760><c> class</c><00:02:36.020><c> is</c>

00:02:37.010 --> 00:02:37.020 align:start position:0%
trying to perform in a single class is
 

00:02:37.020 --> 00:02:39.050 align:start position:0%
trying to perform in a single class is
to<00:02:37.319><c> separate</c><00:02:37.680><c> them</c><00:02:37.860><c> out</c><00:02:38.040><c> so</c><00:02:38.700><c> I'm</c><00:02:38.819><c> going</c><00:02:38.940><c> to</c>

00:02:39.050 --> 00:02:39.060 align:start position:0%
to separate them out so I'm going to
 

00:02:39.060 --> 00:02:40.729 align:start position:0%
to separate them out so I'm going to
create<00:02:39.180><c> three</c><00:02:39.599><c> different</c><00:02:39.840><c> interfaces</c><00:02:40.440><c> one</c>

00:02:40.729 --> 00:02:40.739 align:start position:0%
create three different interfaces one
 

00:02:40.739 --> 00:02:42.290 align:start position:0%
create three different interfaces one
for<00:02:40.920><c> employee</c><00:02:41.280><c> that</c><00:02:41.459><c> just</c><00:02:41.580><c> gets</c><00:02:41.819><c> the</c><00:02:41.940><c> name</c><00:02:42.060><c> the</c>

00:02:42.290 --> 00:02:42.300 align:start position:0%
for employee that just gets the name the
 

00:02:42.300 --> 00:02:45.470 align:start position:0%
for employee that just gets the name the
ID<00:02:42.480><c> a</c><00:02:43.440><c> salary</c><00:02:43.620><c> calculator</c><00:02:44.099><c> which</c><00:02:44.940><c> just</c><00:02:45.180><c> has</c>

00:02:45.470 --> 00:02:45.480 align:start position:0%
ID a salary calculator which just has
 

00:02:45.480 --> 00:02:47.330 align:start position:0%
ID a salary calculator which just has
two<00:02:45.959><c> methods</c><00:02:46.379><c> that</c><00:02:46.560><c> create</c><00:02:46.680><c> the</c><00:02:46.920><c> basic</c><00:02:47.220><c> salary</c>

00:02:47.330 --> 00:02:47.340 align:start position:0%
two methods that create the basic salary
 

00:02:47.340 --> 00:02:49.369 align:start position:0%
two methods that create the basic salary
and<00:02:47.760><c> the</c><00:02:47.879><c> bonus</c><00:02:48.180><c> salary</c><00:02:48.420><c> and</c><00:02:48.840><c> only</c><00:02:49.019><c> takes</c><00:02:49.200><c> an</c>

00:02:49.369 --> 00:02:49.379 align:start position:0%
and the bonus salary and only takes an
 

00:02:49.379 --> 00:02:51.589 align:start position:0%
and the bonus salary and only takes an
employee<00:02:49.739><c> and</c><00:02:50.340><c> then</c><00:02:50.459><c> an</c><00:02:50.640><c> email</c><00:02:50.819><c> service</c><00:02:51.120><c> which</c>

00:02:51.589 --> 00:02:51.599 align:start position:0%
employee and then an email service which
 

00:02:51.599 --> 00:02:53.630 align:start position:0%
employee and then an email service which
just<00:02:52.319><c> has</c><00:02:52.440><c> one</c><00:02:52.680><c> method</c><00:02:52.920><c> to</c><00:02:53.160><c> send</c><00:02:53.280><c> a</c><00:02:53.459><c> salary</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
just has one method to send a salary
 

00:02:53.640 --> 00:02:54.650 align:start position:0%
just has one method to send a salary
email

00:02:54.650 --> 00:02:54.660 align:start position:0%
email
 

00:02:54.660 --> 00:02:55.729 align:start position:0%
email
now<00:02:55.260><c> we're</c><00:02:55.440><c> going</c><00:02:55.560><c> to</c><00:02:55.620><c> create</c>

00:02:55.729 --> 00:02:55.739 align:start position:0%
now we're going to create
 

00:02:55.739 --> 00:02:57.710 align:start position:0%
now we're going to create
implementations<00:02:56.340><c> of</c><00:02:56.640><c> those</c><00:02:56.879><c> the</c><00:02:57.480><c> first</c><00:02:57.599><c> one</c>

00:02:57.710 --> 00:02:57.720 align:start position:0%
implementations of those the first one
 

00:02:57.720 --> 00:02:59.089 align:start position:0%
implementations of those the first one
is<00:02:57.840><c> going</c><00:02:57.959><c> to</c><00:02:58.019><c> be</c><00:02:58.080><c> a</c><00:02:58.319><c> permanent</c><00:02:58.739><c> employee</c>

00:02:59.089 --> 00:02:59.099 align:start position:0%
is going to be a permanent employee
 

00:02:59.099 --> 00:03:01.009 align:start position:0%
is going to be a permanent employee
which<00:02:59.400><c> just</c><00:02:59.580><c> gets</c><00:02:59.879><c> the</c><00:02:59.940><c> name</c><00:03:00.060><c> and</c><00:03:00.239><c> ID</c><00:03:00.420><c> and</c><00:03:00.900><c> this</c>

00:03:01.009 --> 00:03:01.019 align:start position:0%
which just gets the name and ID and this
 

00:03:01.019 --> 00:03:03.170 align:start position:0%
which just gets the name and ID and this
returns<00:03:01.440><c> that</c><00:03:01.980><c> the</c><00:03:02.519><c> second</c><00:03:02.640><c> implementation</c>

00:03:03.170 --> 00:03:03.180 align:start position:0%
returns that the second implementation
 

00:03:03.180 --> 00:03:05.210 align:start position:0%
returns that the second implementation
is<00:03:03.300><c> the</c><00:03:03.480><c> basic</c><00:03:03.780><c> salary</c><00:03:04.019><c> calculator</c><00:03:04.440><c> for</c><00:03:04.980><c> now</c>

00:03:05.210 --> 00:03:05.220 align:start position:0%
is the basic salary calculator for now
 

00:03:05.220 --> 00:03:07.610 align:start position:0%
is the basic salary calculator for now
I'm<00:03:05.340><c> just</c><00:03:05.459><c> having</c><00:03:05.580><c> a</c><00:03:05.760><c> return</c><00:03:06.019><c> sample</c><00:03:07.019><c> decimals</c>

00:03:07.610 --> 00:03:07.620 align:start position:0%
I'm just having a return sample decimals
 

00:03:07.620 --> 00:03:09.110 align:start position:0%
I'm just having a return sample decimals
and<00:03:08.280><c> then</c><00:03:08.400><c> finally</c><00:03:08.640><c> we</c><00:03:08.879><c> need</c><00:03:08.940><c> an</c>

00:03:09.110 --> 00:03:09.120 align:start position:0%
and then finally we need an
 

00:03:09.120 --> 00:03:10.550 align:start position:0%
and then finally we need an
implementation<00:03:09.660><c> for</c><00:03:09.959><c> the</c><00:03:10.140><c> basic</c><00:03:10.379><c> email</c>

00:03:10.550 --> 00:03:10.560 align:start position:0%
implementation for the basic email
 

00:03:10.560 --> 00:03:12.470 align:start position:0%
implementation for the basic email
service<00:03:10.920><c> and</c><00:03:11.459><c> this</c><00:03:11.700><c> just</c><00:03:11.940><c> prints</c><00:03:12.360><c> out</c>

00:03:12.470 --> 00:03:12.480 align:start position:0%
service and this just prints out
 

00:03:12.480 --> 00:03:14.930 align:start position:0%
service and this just prints out
employee<00:03:13.260><c> information</c><00:03:13.440><c> and</c><00:03:13.920><c> salary</c><00:03:14.159><c> and</c><00:03:14.760><c> what</c>

00:03:14.930 --> 00:03:14.940 align:start position:0%
employee information and salary and what
 

00:03:14.940 --> 00:03:16.670 align:start position:0%
employee information and salary and what
I'm<00:03:15.060><c> going</c><00:03:15.180><c> to</c><00:03:15.300><c> create</c><00:03:15.420><c> now</c><00:03:15.720><c> is</c><00:03:16.140><c> a</c><00:03:16.319><c> facade</c>

00:03:16.670 --> 00:03:16.680 align:start position:0%
I'm going to create now is a facade
 

00:03:16.680 --> 00:03:18.470 align:start position:0%
I'm going to create now is a facade
class<00:03:16.980><c> so</c><00:03:17.459><c> I'm</c><00:03:17.640><c> going</c><00:03:17.700><c> to</c><00:03:17.819><c> call</c><00:03:17.940><c> so</c><00:03:18.239><c> I'm</c><00:03:18.360><c> going</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
class so I'm going to call so I'm going
 

00:03:18.480 --> 00:03:20.690 align:start position:0%
class so I'm going to call so I'm going
to<00:03:18.599><c> call</c><00:03:18.659><c> it</c><00:03:18.840><c> payroll</c><00:03:19.260><c> facade</c><00:03:19.860><c> all</c><00:03:20.400><c> this</c><00:03:20.519><c> is</c>

00:03:20.690 --> 00:03:20.700 align:start position:0%
to call it payroll facade all this is
 

00:03:20.700 --> 00:03:23.149 align:start position:0%
to call it payroll facade all this is
doing<00:03:20.879><c> is</c><00:03:21.300><c> delegating</c><00:03:21.959><c> all</c><00:03:22.860><c> the</c><00:03:22.980><c> information</c>

00:03:23.149 --> 00:03:23.159 align:start position:0%
doing is delegating all the information
 

00:03:23.159 --> 00:03:27.530 align:start position:0%
doing is delegating all the information
from<00:03:23.819><c> the</c><00:03:23.940><c> interfaces</c><00:03:24.659><c> to</c><00:03:25.620><c> do</c><00:03:26.340><c> some</c><00:03:26.640><c> task</c><00:03:26.940><c> well</c>

00:03:27.530 --> 00:03:27.540 align:start position:0%
from the interfaces to do some task well
 

00:03:27.540 --> 00:03:29.570 align:start position:0%
from the interfaces to do some task well
I<00:03:27.780><c> wanted</c><00:03:27.959><c> to</c><00:03:28.200><c> calculate</c><00:03:28.620><c> and</c><00:03:28.980><c> then</c><00:03:29.099><c> send</c><00:03:29.400><c> an</c>

00:03:29.570 --> 00:03:29.580 align:start position:0%
I wanted to calculate and then send an
 

00:03:29.580 --> 00:03:31.250 align:start position:0%
I wanted to calculate and then send an
email<00:03:29.760><c> based</c><00:03:30.360><c> on</c><00:03:30.420><c> the</c><00:03:30.540><c> salaries</c><00:03:30.900><c> for</c><00:03:31.140><c> an</c>

00:03:31.250 --> 00:03:31.260 align:start position:0%
email based on the salaries for an
 

00:03:31.260 --> 00:03:34.009 align:start position:0%
email based on the salaries for an
employee<00:03:31.620><c> so</c><00:03:32.459><c> it's</c><00:03:32.760><c> going</c><00:03:32.940><c> to</c><00:03:33.120><c> point</c><00:03:33.360><c> to</c><00:03:33.659><c> all</c>

00:03:34.009 --> 00:03:34.019 align:start position:0%
employee so it's going to point to all
 

00:03:34.019 --> 00:03:36.710 align:start position:0%
employee so it's going to point to all
of<00:03:34.140><c> the</c><00:03:34.319><c> interfaces</c><00:03:34.920><c> and</c><00:03:35.760><c> take</c><00:03:35.940><c> in</c><00:03:36.180><c> all</c><00:03:36.480><c> of</c><00:03:36.540><c> the</c>

00:03:36.710 --> 00:03:36.720 align:start position:0%
of the interfaces and take in all of the
 

00:03:36.720 --> 00:03:39.530 align:start position:0%
of the interfaces and take in all of the
implementations<00:03:37.200><c> and</c><00:03:37.980><c> now</c><00:03:38.220><c> in</c><00:03:38.700><c> the</c><00:03:39.000><c> main</c>

00:03:39.530 --> 00:03:39.540 align:start position:0%
implementations and now in the main
 

00:03:39.540 --> 00:03:41.449 align:start position:0%
implementations and now in the main
class<00:03:39.840><c> I'm</c><00:03:40.620><c> just</c><00:03:40.739><c> going</c><00:03:40.860><c> to</c><00:03:41.040><c> create</c><00:03:41.220><c> an</c>

00:03:41.449 --> 00:03:41.459 align:start position:0%
class I'm just going to create an
 

00:03:41.459 --> 00:03:43.910 align:start position:0%
class I'm just going to create an
employee<00:03:41.819><c> I'm</c><00:03:42.720><c> going</c><00:03:42.900><c> to</c><00:03:43.019><c> create</c><00:03:43.319><c> a</c><00:03:43.620><c> payroll</c>

00:03:43.910 --> 00:03:43.920 align:start position:0%
employee I'm going to create a payroll
 

00:03:43.920 --> 00:03:45.890 align:start position:0%
employee I'm going to create a payroll
facade<00:03:44.400><c> class</c><00:03:44.580><c> and</c><00:03:45.000><c> then</c><00:03:45.180><c> just</c><00:03:45.480><c> call</c><00:03:45.659><c> the</c>

00:03:45.890 --> 00:03:45.900 align:start position:0%
facade class and then just call the
 

00:03:45.900 --> 00:03:47.990 align:start position:0%
facade class and then just call the
calculate<00:03:46.379><c> and</c><00:03:46.500><c> send</c><00:03:46.739><c> salary</c><00:03:47.040><c> now</c><00:03:47.819><c> let's</c>

00:03:47.990 --> 00:03:48.000 align:start position:0%
calculate and send salary now let's
 

00:03:48.000 --> 00:03:50.690 align:start position:0%
calculate and send salary now let's
compare<00:03:48.360><c> both</c><00:03:48.959><c> uml</c><00:03:49.260><c> diagrams</c><00:03:49.860><c> for</c><00:03:50.280><c> each</c><00:03:50.459><c> and</c>

00:03:50.690 --> 00:03:50.700 align:start position:0%
compare both uml diagrams for each and
 

00:03:50.700 --> 00:03:54.649 align:start position:0%
compare both uml diagrams for each and
we<00:03:50.940><c> can</c><00:03:51.060><c> see</c><00:03:51.360><c> what</c><00:03:51.720><c> the</c><00:03:51.840><c> differences</c><00:03:52.200><c> are</c>

00:03:54.649 --> 00:03:54.659 align:start position:0%
 
 

00:03:54.659 --> 00:03:57.830 align:start position:0%
 
okay<00:03:55.200><c> so</c><00:03:55.440><c> we</c><00:03:55.620><c> just</c><00:03:55.860><c> had</c><00:03:56.099><c> our</c><00:03:56.280><c> email</c>

00:03:57.830 --> 00:03:57.840 align:start position:0%
okay so we just had our email
 

00:03:57.840 --> 00:04:00.410 align:start position:0%
okay so we just had our email
when<00:03:58.260><c> we</c><00:03:58.440><c> just</c><00:03:58.560><c> had</c><00:03:58.680><c> our</c><00:03:58.860><c> employee</c><00:03:59.340><c> class</c><00:03:59.640><c> we</c>

00:04:00.410 --> 00:04:00.420 align:start position:0%
when we just had our employee class we
 

00:04:00.420 --> 00:04:02.869 align:start position:0%
when we just had our employee class we
had<00:04:00.540><c> four</c><00:04:00.840><c> fields</c><00:04:01.379><c> and</c><00:04:02.220><c> then</c><00:04:02.340><c> we</c><00:04:02.519><c> had</c><00:04:02.640><c> two</c>

00:04:02.869 --> 00:04:02.879 align:start position:0%
had four fields and then we had two
 

00:04:02.879 --> 00:04:05.509 align:start position:0%
had four fields and then we had two
methods<00:04:03.360><c> okay</c><00:04:04.019><c> as</c><00:04:04.620><c> stated</c><00:04:04.920><c> before</c><00:04:05.099><c> we're</c>

00:04:05.509 --> 00:04:05.519 align:start position:0%
methods okay as stated before we're
 

00:04:05.519 --> 00:04:06.830 align:start position:0%
methods okay as stated before we're
getting<00:04:05.760><c> the</c><00:04:05.940><c> name</c><00:04:06.060><c> and</c><00:04:06.299><c> ID</c><00:04:06.420><c> which</c><00:04:06.720><c> was</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
getting the name and ID which was
 

00:04:06.840 --> 00:04:08.449 align:start position:0%
getting the name and ID which was
Employee<00:04:07.260><c> information</c><00:04:07.379><c> where</c><00:04:08.280><c> you're</c>

00:04:08.449 --> 00:04:08.459 align:start position:0%
Employee information where you're
 

00:04:08.459 --> 00:04:10.369 align:start position:0%
Employee information where you're
getting<00:04:08.640><c> two</c><00:04:09.060><c> different</c><00:04:09.360><c> kind</c><00:04:09.720><c> of</c><00:04:09.840><c> salaries</c>

00:04:10.369 --> 00:04:10.379 align:start position:0%
getting two different kind of salaries
 

00:04:10.379 --> 00:04:12.530 align:start position:0%
getting two different kind of salaries
we<00:04:11.040><c> we</c><00:04:11.280><c> had</c><00:04:11.459><c> a</c><00:04:11.580><c> method</c><00:04:11.819><c> for</c><00:04:11.939><c> calculating</c><00:04:12.299><c> the</c>

00:04:12.530 --> 00:04:12.540 align:start position:0%
we we had a method for calculating the
 

00:04:12.540 --> 00:04:13.970 align:start position:0%
we we had a method for calculating the
salary<00:04:12.720><c> and</c><00:04:13.080><c> then</c><00:04:13.200><c> we</c><00:04:13.260><c> had</c><00:04:13.379><c> a</c><00:04:13.560><c> method</c><00:04:13.799><c> for</c>

00:04:13.970 --> 00:04:13.980 align:start position:0%
salary and then we had a method for
 

00:04:13.980 --> 00:04:16.490 align:start position:0%
salary and then we had a method for
sending<00:04:14.400><c> an</c><00:04:14.640><c> email</c><00:04:14.959><c> that</c><00:04:15.959><c> gave</c><00:04:16.199><c> information</c>

00:04:16.490 --> 00:04:16.500 align:start position:0%
sending an email that gave information
 

00:04:16.500 --> 00:04:19.550 align:start position:0%
sending an email that gave information
on<00:04:16.919><c> that</c><00:04:17.040><c> salary</c><00:04:17.280><c> so</c><00:04:17.820><c> then</c><00:04:18.120><c> what</c><00:04:18.359><c> we</c><00:04:18.479><c> did</c><00:04:18.660><c> to</c>

00:04:19.550 --> 00:04:19.560 align:start position:0%
on that salary so then what we did to
 

00:04:19.560 --> 00:04:21.770 align:start position:0%
on that salary so then what we did to
solve<00:04:20.220><c> this</c><00:04:20.400><c> is</c><00:04:20.699><c> we</c><00:04:20.880><c> created</c><00:04:21.120><c> we</c><00:04:21.540><c> created</c>

00:04:21.770 --> 00:04:21.780 align:start position:0%
solve this is we created we created
 

00:04:21.780 --> 00:04:23.090 align:start position:0%
solve this is we created we created
three<00:04:21.959><c> interfaces</c>

00:04:23.090 --> 00:04:23.100 align:start position:0%
three interfaces
 

00:04:23.100 --> 00:04:25.129 align:start position:0%
three interfaces
three<00:04:23.639><c> implementations</c><00:04:24.180><c> of</c><00:04:24.479><c> those</c><00:04:24.660><c> and</c><00:04:25.020><c> then</c>

00:04:25.129 --> 00:04:25.139 align:start position:0%
three implementations of those and then
 

00:04:25.139 --> 00:04:27.650 align:start position:0%
three implementations of those and then
a<00:04:25.320><c> facade</c><00:04:25.680><c> class</c><00:04:25.919><c> which</c><00:04:26.340><c> again</c><00:04:26.580><c> all</c><00:04:27.180><c> it</c><00:04:27.300><c> is</c><00:04:27.419><c> is</c>

00:04:27.650 --> 00:04:27.660 align:start position:0%
a facade class which again all it is is
 

00:04:27.660 --> 00:04:29.689 align:start position:0%
a facade class which again all it is is
a<00:04:27.780><c> delegator</c><00:04:28.199><c> okay</c><00:04:28.560><c> that's</c><00:04:28.919><c> another</c><00:04:29.100><c> video</c><00:04:29.400><c> I</c>

00:04:29.689 --> 00:04:29.699 align:start position:0%
a delegator okay that's another video I
 

00:04:29.699 --> 00:04:31.790 align:start position:0%
a delegator okay that's another video I
can<00:04:29.820><c> explain</c><00:04:30.000><c> the</c><00:04:30.360><c> facade</c><00:04:30.840><c> design</c><00:04:31.259><c> pattern</c>

00:04:31.790 --> 00:04:31.800 align:start position:0%
can explain the facade design pattern
 

00:04:31.800 --> 00:04:34.010 align:start position:0%
can explain the facade design pattern
but<00:04:32.400><c> for</c><00:04:32.639><c> now</c><00:04:32.820><c> just</c><00:04:33.240><c> think</c><00:04:33.419><c> of</c><00:04:33.540><c> it</c><00:04:33.660><c> as</c><00:04:33.840><c> a</c>

00:04:34.010 --> 00:04:34.020 align:start position:0%
but for now just think of it as a
 

00:04:34.020 --> 00:04:36.350 align:start position:0%
but for now just think of it as a
delegator<00:04:34.440><c> so</c><00:04:34.919><c> now</c><00:04:35.280><c> you</c><00:04:35.580><c> can</c><00:04:35.699><c> see</c><00:04:35.820><c> as</c><00:04:36.000><c> we</c><00:04:36.240><c> have</c>

00:04:36.350 --> 00:04:36.360 align:start position:0%
delegator so now you can see as we have
 

00:04:36.360 --> 00:04:39.050 align:start position:0%
delegator so now you can see as we have
our<00:04:36.600><c> facade</c><00:04:37.020><c> class</c><00:04:37.320><c> which</c><00:04:37.919><c> takes</c><00:04:38.220><c> in</c><00:04:38.520><c> three</c>

00:04:39.050 --> 00:04:39.060 align:start position:0%
our facade class which takes in three
 

00:04:39.060 --> 00:04:40.850 align:start position:0%
our facade class which takes in three
different<00:04:39.300><c> interfaces</c><00:04:40.020><c> takes</c><00:04:40.380><c> in</c><00:04:40.560><c> a</c><00:04:40.740><c> salary</c>

00:04:40.850 --> 00:04:40.860 align:start position:0%
different interfaces takes in a salary
 

00:04:40.860 --> 00:04:42.950 align:start position:0%
different interfaces takes in a salary
calculator<00:04:41.220><c> an</c><00:04:41.880><c> email</c><00:04:42.060><c> service</c><00:04:42.419><c> and</c><00:04:42.840><c> an</c>

00:04:42.950 --> 00:04:42.960 align:start position:0%
calculator an email service and an
 

00:04:42.960 --> 00:04:44.930 align:start position:0%
calculator an email service and an
employee<00:04:43.380><c> now</c><00:04:43.860><c> the</c><00:04:44.280><c> thing</c><00:04:44.400><c> is</c><00:04:44.580><c> with</c><00:04:44.759><c> the</c>

00:04:44.930 --> 00:04:44.940 align:start position:0%
employee now the thing is with the
 

00:04:44.940 --> 00:04:46.790 align:start position:0%
employee now the thing is with the
payroll<00:04:45.479><c> facade</c><00:04:45.900><c> is</c><00:04:46.020><c> it</c><00:04:46.199><c> doesn't</c><00:04:46.380><c> care</c><00:04:46.620><c> about</c>

00:04:46.790 --> 00:04:46.800 align:start position:0%
payroll facade is it doesn't care about
 

00:04:46.800 --> 00:04:49.490 align:start position:0%
payroll facade is it doesn't care about
the<00:04:47.100><c> implementations</c><00:04:47.639><c> of</c><00:04:48.000><c> those</c><00:04:48.180><c> okay</c><00:04:48.660><c> so</c><00:04:49.199><c> we</c>

00:04:49.490 --> 00:04:49.500 align:start position:0%
the implementations of those okay so we
 

00:04:49.500 --> 00:04:51.650 align:start position:0%
the implementations of those okay so we
don't<00:04:49.620><c> so</c><00:04:50.040><c> if</c><00:04:50.160><c> we</c><00:04:50.340><c> were</c><00:04:50.460><c> to</c><00:04:50.580><c> change</c><00:04:50.940><c> for</c>

00:04:51.650 --> 00:04:51.660 align:start position:0%
don't so if we were to change for
 

00:04:51.660 --> 00:04:53.629 align:start position:0%
don't so if we were to change for
instance<00:04:51.900><c> the</c><00:04:52.259><c> logic</c><00:04:52.620><c> when</c><00:04:52.919><c> we</c><00:04:53.100><c> go</c><00:04:53.280><c> to</c><00:04:53.460><c> send</c>

00:04:53.629 --> 00:04:53.639 align:start position:0%
instance the logic when we go to send
 

00:04:53.639 --> 00:04:56.390 align:start position:0%
instance the logic when we go to send
salary<00:04:54.060><c> email</c><00:04:54.600><c> we're</c><00:04:55.380><c> only</c><00:04:55.560><c> going</c><00:04:55.860><c> to</c><00:04:56.040><c> change</c>

00:04:56.390 --> 00:04:56.400 align:start position:0%
salary email we're only going to change
 

00:04:56.400 --> 00:04:59.749 align:start position:0%
salary email we're only going to change
information<00:04:57.259><c> based</c><00:04:58.259><c> on</c><00:04:58.380><c> that</c><00:04:58.620><c> basic</c><00:04:59.520><c> email</c>

00:04:59.749 --> 00:04:59.759 align:start position:0%
information based on that basic email
 

00:04:59.759 --> 00:05:02.270 align:start position:0%
information based on that basic email
service<00:05:00.180><c> implementation</c><00:05:00.900><c> We're</c><00:05:01.860><c> not</c><00:05:02.100><c> gonna</c>

00:05:02.270 --> 00:05:02.280 align:start position:0%
service implementation We're not gonna
 

00:05:02.280 --> 00:05:04.490 align:start position:0%
service implementation We're not gonna
change<00:05:02.699><c> anything</c><00:05:03.300><c> else</c><00:05:03.600><c> the</c><00:05:03.840><c> payroll</c><00:05:04.139><c> facade</c>

00:05:04.490 --> 00:05:04.500 align:start position:0%
change anything else the payroll facade
 

00:05:04.500 --> 00:05:06.170 align:start position:0%
change anything else the payroll facade
is<00:05:04.620><c> going</c><00:05:04.740><c> to</c><00:05:04.800><c> stay</c><00:05:04.919><c> the</c><00:05:05.160><c> exact</c><00:05:05.460><c> same</c><00:05:05.820><c> the</c>

00:05:06.170 --> 00:05:06.180 align:start position:0%
is going to stay the exact same the
 

00:05:06.180 --> 00:05:08.990 align:start position:0%
is going to stay the exact same the
interface<00:05:06.540><c> the</c><00:05:06.900><c> exact</c><00:05:07.199><c> same</c><00:05:07.500><c> no</c><00:05:08.340><c> other</c><00:05:08.580><c> class</c>

00:05:08.990 --> 00:05:09.000 align:start position:0%
interface the exact same no other class
 

00:05:09.000 --> 00:05:11.030 align:start position:0%
interface the exact same no other class
or<00:05:09.240><c> interface</c><00:05:09.720><c> is</c><00:05:10.080><c> going</c><00:05:10.199><c> to</c><00:05:10.380><c> be</c><00:05:10.500><c> affected</c>

00:05:11.030 --> 00:05:11.040 align:start position:0%
or interface is going to be affected
 

00:05:11.040 --> 00:05:13.129 align:start position:0%
or interface is going to be affected
because<00:05:11.340><c> we</c><00:05:11.639><c> did</c><00:05:11.759><c> what's</c><00:05:12.000><c> called</c><00:05:12.300><c> separation</c>

00:05:13.129 --> 00:05:13.139 align:start position:0%
because we did what's called separation
 

00:05:13.139 --> 00:05:15.110 align:start position:0%
because we did what's called separation
of<00:05:13.500><c> concerns</c><00:05:14.100><c> but</c><00:05:14.460><c> the</c><00:05:14.639><c> point</c><00:05:14.699><c> I'm</c><00:05:14.820><c> trying</c><00:05:15.000><c> to</c>

00:05:15.110 --> 00:05:15.120 align:start position:0%
of concerns but the point I'm trying to
 

00:05:15.120 --> 00:05:16.909 align:start position:0%
of concerns but the point I'm trying to
make<00:05:15.240><c> is</c><00:05:15.419><c> we</c><00:05:15.780><c> don't</c><00:05:15.900><c> want</c><00:05:16.139><c> a</c><00:05:16.380><c> class</c><00:05:16.560><c> having</c>

00:05:16.909 --> 00:05:16.919 align:start position:0%
make is we don't want a class having
 

00:05:16.919 --> 00:05:19.370 align:start position:0%
make is we don't want a class having
multiple<00:05:17.759><c> responsibilities</c><00:05:18.720><c> I'll</c><00:05:19.139><c> have</c><00:05:19.259><c> a</c>

00:05:19.370 --> 00:05:19.380 align:start position:0%
multiple responsibilities I'll have a
 

00:05:19.380 --> 00:05:20.450 align:start position:0%
multiple responsibilities I'll have a
link<00:05:19.500><c> in</c><00:05:19.560><c> the</c><00:05:19.680><c> description</c><00:05:19.860><c> for</c><00:05:20.100><c> the</c><00:05:20.160><c> GitHub</c>

00:05:20.450 --> 00:05:20.460 align:start position:0%
link in the description for the GitHub
 

00:05:20.460 --> 00:05:22.370 align:start position:0%
link in the description for the GitHub
for<00:05:20.699><c> this</c><00:05:20.820><c> so</c><00:05:21.000><c> that</c><00:05:21.120><c> you</c><00:05:21.419><c> can</c><00:05:21.540><c> go</c><00:05:21.960><c> and</c><00:05:22.139><c> look</c><00:05:22.259><c> at</c>

00:05:22.370 --> 00:05:22.380 align:start position:0%
for this so that you can go and look at
 

00:05:22.380 --> 00:05:23.990 align:start position:0%
for this so that you can go and look at
this<00:05:22.500><c> yourself</c><00:05:22.680><c> and</c><00:05:22.860><c> now</c><00:05:23.160><c> look</c><00:05:23.639><c> at</c><00:05:23.759><c> these</c>

00:05:23.990 --> 00:05:24.000 align:start position:0%
this yourself and now look at these
 

00:05:24.000 --> 00:05:25.610 align:start position:0%
this yourself and now look at these
videos<00:05:24.180><c> for</c><00:05:24.840><c> more</c><00:05:24.960><c> information</c><00:05:25.139><c> about</c>

00:05:25.610 --> 00:05:25.620 align:start position:0%
videos for more information about
 

00:05:25.620 --> 00:05:26.990 align:start position:0%
videos for more information about
computer<00:05:25.800><c> science</c><00:05:26.160><c> and</c><00:05:26.460><c> you</c><00:05:26.699><c> can</c><00:05:26.820><c> learn</c>

00:05:26.990 --> 00:05:27.000 align:start position:0%
computer science and you can learn
 

00:05:27.000 --> 00:05:30.440 align:start position:0%
computer science and you can learn
something<00:05:27.360><c> else</c><00:05:27.660><c> I'll</c><00:05:28.020><c> see</c><00:05:28.139><c> you</c><00:05:28.259><c> next</c><00:05:28.380><c> time</c>

