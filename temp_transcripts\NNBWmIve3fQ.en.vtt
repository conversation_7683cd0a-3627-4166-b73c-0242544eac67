WEBVTT
Kind: captions
Language: en

00:00:00.880 --> 00:00:03.230 align:start position:0%
 
hi<00:00:01.520><c> check</c><00:00:01.760><c> this</c><00:00:01.880><c> out</c><00:00:02.320><c> recently</c><00:00:02.720><c> the</c><00:00:02.840><c> olama</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
hi check this out recently the olama
 

00:00:03.240 --> 00:00:05.349 align:start position:0%
hi check this out recently the olama
team<00:00:03.439><c> released</c><00:00:03.879><c> an</c><00:00:04.240><c> official</c><00:00:04.839><c> node.js</c>

00:00:05.349 --> 00:00:05.359 align:start position:0%
team released an official node.js
 

00:00:05.359 --> 00:00:08.030 align:start position:0%
team released an official node.js
library<00:00:06.200><c> for</c><00:00:06.399><c> olama</c><00:00:07.399><c> you</c><00:00:07.480><c> can</c><00:00:07.600><c> find</c><00:00:07.759><c> it</c><00:00:07.879><c> here</c>

00:00:08.030 --> 00:00:08.040 align:start position:0%
library for olama you can find it here
 

00:00:08.040 --> 00:00:09.950 align:start position:0%
library for olama you can find it here
on<00:00:08.200><c> GitHub</c><00:00:09.000><c> I'll</c><00:00:09.160><c> probably</c><00:00:09.400><c> start</c><00:00:09.599><c> building</c>

00:00:09.950 --> 00:00:09.960 align:start position:0%
on GitHub I'll probably start building
 

00:00:09.960 --> 00:00:11.950 align:start position:0%
on GitHub I'll probably start building
with<00:00:10.080><c> it</c><00:00:10.400><c> and</c><00:00:10.559><c> the</c><00:00:10.759><c> python</c><00:00:11.120><c> equivalent</c><00:00:11.559><c> soon</c>

00:00:11.950 --> 00:00:11.960 align:start position:0%
with it and the python equivalent soon
 

00:00:11.960 --> 00:00:13.910 align:start position:0%
with it and the python equivalent soon
so<00:00:12.400><c> I</c><00:00:12.519><c> thought</c><00:00:12.719><c> I'd</c><00:00:13.000><c> start</c><00:00:13.320><c> playing</c><00:00:13.599><c> around</c>

00:00:13.910 --> 00:00:13.920 align:start position:0%
so I thought I'd start playing around
 

00:00:13.920 --> 00:00:17.670 align:start position:0%
so I thought I'd start playing around
with<00:00:14.040><c> it</c><00:00:14.320><c> now</c><00:00:15.320><c> here</c><00:00:15.719><c> I've</c><00:00:16.000><c> imported</c><00:00:16.680><c> AMA</c><00:00:17.400><c> and</c>

00:00:17.670 --> 00:00:17.680 align:start position:0%
with it now here I've imported AMA and
 

00:00:17.680 --> 00:00:19.990 align:start position:0%
with it now here I've imported AMA and
instantiated<00:00:18.439><c> it</c><00:00:19.080><c> then</c><00:00:19.199><c> I</c><00:00:19.320><c> call</c><00:00:19.600><c> the</c><00:00:19.720><c> chat</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
instantiated it then I call the chat
 

00:00:20.000 --> 00:00:22.429 align:start position:0%
instantiated it then I call the chat
endpoint<00:00:20.560><c> with</c><00:00:20.680><c> a</c><00:00:20.840><c> simple</c><00:00:21.320><c> system</c><00:00:21.680><c> prompt</c><00:00:22.160><c> and</c>

00:00:22.429 --> 00:00:22.439 align:start position:0%
endpoint with a simple system prompt and
 

00:00:22.439 --> 00:00:25.189 align:start position:0%
endpoint with a simple system prompt and
initial<00:00:22.880><c> question</c><00:00:23.720><c> so</c><00:00:24.000><c> now</c><00:00:24.400><c> I</c><00:00:24.480><c> want</c><00:00:24.720><c> to</c><00:00:24.960><c> print</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
initial question so now I want to print
 

00:00:25.199 --> 00:00:27.750 align:start position:0%
initial question so now I want to print
the<00:00:25.320><c> output</c><00:00:26.000><c> this</c><00:00:26.160><c> endpoint</c><00:00:26.599><c> defaults</c><00:00:27.119><c> to</c><00:00:27.480><c> not</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
the output this endpoint defaults to not
 

00:00:27.760 --> 00:00:30.109 align:start position:0%
the output this endpoint defaults to not
streaming<00:00:28.640><c> so</c><00:00:28.840><c> the</c><00:00:29.000><c> response</c><00:00:29.359><c> will</c><00:00:29.560><c> just</c><00:00:29.720><c> be</c>

00:00:30.109 --> 00:00:30.119 align:start position:0%
streaming so the response will just be
 

00:00:30.119 --> 00:00:32.950 align:start position:0%
streaming so the response will just be
at<00:00:30.320><c> Json</c><00:00:30.720><c> blob</c><00:00:31.599><c> with</c><00:00:31.759><c> the</c><00:00:31.880><c> output</c><00:00:32.480><c> and</c><00:00:32.680><c> some</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
at Json blob with the output and some
 

00:00:32.960 --> 00:00:35.430 align:start position:0%
at Json blob with the output and some
metrics<00:00:33.399><c> about</c><00:00:33.680><c> how</c><00:00:33.800><c> it</c><00:00:34.160><c> performed</c><00:00:35.160><c> this</c><00:00:35.239><c> is</c>

00:00:35.430 --> 00:00:35.440 align:start position:0%
metrics about how it performed this is
 

00:00:35.440 --> 00:00:37.590 align:start position:0%
metrics about how it performed this is
nice<00:00:35.640><c> and</c><00:00:35.879><c> easy</c><00:00:36.440><c> but</c><00:00:36.879><c> we</c><00:00:37.000><c> have</c><00:00:37.120><c> to</c><00:00:37.280><c> wait</c><00:00:37.440><c> for</c>

00:00:37.590 --> 00:00:37.600 align:start position:0%
nice and easy but we have to wait for
 

00:00:37.600 --> 00:00:39.830 align:start position:0%
nice and easy but we have to wait for
the<00:00:37.719><c> full</c><00:00:38.120><c> response</c><00:00:38.480><c> to</c><00:00:38.640><c> be</c><00:00:38.800><c> done</c><00:00:39.440><c> before</c><00:00:39.719><c> we</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
the full response to be done before we
 

00:00:39.840 --> 00:00:42.350 align:start position:0%
the full response to be done before we
see<00:00:40.120><c> anything</c><00:00:40.960><c> although</c><00:00:41.239><c> it's</c><00:00:41.399><c> not</c><00:00:41.719><c> faster</c><00:00:42.200><c> or</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
see anything although it's not faster or
 

00:00:42.360 --> 00:00:44.670 align:start position:0%
see anything although it's not faster or
slower<00:00:42.879><c> the</c><00:00:43.079><c> response</c><00:00:43.640><c> does</c><00:00:43.879><c> feel</c><00:00:44.280><c> faster</c>

00:00:44.670 --> 00:00:44.680 align:start position:0%
slower the response does feel faster
 

00:00:44.680 --> 00:00:46.590 align:start position:0%
slower the response does feel faster
when<00:00:44.840><c> streaming</c><00:00:45.719><c> because</c><00:00:46.000><c> we</c><00:00:46.120><c> start</c><00:00:46.320><c> to</c><00:00:46.399><c> see</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
when streaming because we start to see
 

00:00:46.600 --> 00:00:49.590 align:start position:0%
when streaming because we start to see
the<00:00:46.760><c> content</c><00:00:47.559><c> sooner</c><00:00:48.559><c> so</c><00:00:48.879><c> I</c><00:00:48.960><c> can</c><00:00:49.079><c> set</c><00:00:49.280><c> stream</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
the content sooner so I can set stream
 

00:00:49.600 --> 00:00:52.029 align:start position:0%
the content sooner so I can set stream
to<00:00:49.760><c> true</c><00:00:50.440><c> and</c><00:00:50.559><c> then</c><00:00:50.719><c> try</c><00:00:50.960><c> running</c><00:00:51.239><c> it</c><00:00:51.800><c> and</c><00:00:51.920><c> I</c>

00:00:52.029 --> 00:00:52.039 align:start position:0%
to true and then try running it and I
 

00:00:52.039 --> 00:00:54.110 align:start position:0%
to true and then try running it and I
get<00:00:52.199><c> an</c><00:00:52.320><c> error</c><00:00:53.120><c> well</c><00:00:53.320><c> that's</c><00:00:53.520><c> because</c><00:00:53.760><c> setting</c>

00:00:54.110 --> 00:00:54.120 align:start position:0%
get an error well that's because setting
 

00:00:54.120 --> 00:00:56.630 align:start position:0%
get an error well that's because setting
stream<00:00:54.520><c> to</c><00:00:54.719><c> True</c><00:00:55.440><c> sets</c><00:00:55.800><c> the</c><00:00:55.960><c> function</c><00:00:56.280><c> to</c>

00:00:56.630 --> 00:00:56.640 align:start position:0%
stream to True sets the function to
 

00:00:56.640 --> 00:00:59.110 align:start position:0%
stream to True sets the function to
return<00:00:57.000><c> an</c><00:00:57.199><c> async</c><00:00:57.719><c> generator</c><00:00:58.600><c> instead</c><00:00:58.879><c> of</c><00:00:59.000><c> a</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
return an async generator instead of a
 

00:00:59.120 --> 00:01:01.709 align:start position:0%
return an async generator instead of a
Json<00:00:59.519><c> blob</c><00:01:00.399><c> and</c><00:01:00.519><c> so</c><00:01:00.640><c> we</c><00:01:00.760><c> need</c><00:01:00.920><c> to</c><00:01:01.079><c> deal</c><00:01:01.320><c> with</c><00:01:01.440><c> it</c>

00:01:01.709 --> 00:01:01.719 align:start position:0%
Json blob and so we need to deal with it
 

00:01:01.719 --> 00:01:03.630 align:start position:0%
Json blob and so we need to deal with it
differently<00:01:02.719><c> but</c><00:01:02.879><c> did</c><00:01:03.000><c> you</c><00:01:03.160><c> notice</c><00:01:03.440><c> that</c>

00:01:03.630 --> 00:01:03.640 align:start position:0%
differently but did you notice that
 

00:01:03.640 --> 00:01:06.109 align:start position:0%
differently but did you notice that
console<00:01:04.080><c> log</c><00:01:04.559><c> and</c><00:01:04.760><c> stream</c><00:01:05.159><c> is</c><00:01:05.400><c> true</c><00:01:05.880><c> were</c>

00:01:06.109 --> 00:01:06.119 align:start position:0%
console log and stream is true were
 

00:01:06.119 --> 00:01:09.190 align:start position:0%
console log and stream is true were
typed<00:01:06.680><c> for</c><00:01:07.000><c> me</c><00:01:07.720><c> I</c><00:01:07.880><c> just</c><00:01:08.119><c> tab</c><00:01:08.520><c> to</c><00:01:08.720><c> accept</c><00:01:09.000><c> the</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
typed for me I just tab to accept the
 

00:01:09.200 --> 00:01:11.910 align:start position:0%
typed for me I just tab to accept the
result<00:01:09.799><c> which</c><00:01:09.960><c> is</c><00:01:10.280><c> pretty</c><00:01:10.600><c> cool</c><00:01:11.400><c> you</c><00:01:11.479><c> can</c><00:01:11.640><c> see</c>

00:01:11.910 --> 00:01:11.920 align:start position:0%
result which is pretty cool you can see
 

00:01:11.920 --> 00:01:13.789 align:start position:0%
result which is pretty cool you can see
down<00:01:12.119><c> here</c><00:01:12.280><c> in</c><00:01:12.400><c> the</c><00:01:12.520><c> corner</c><00:01:13.200><c> I'm</c><00:01:13.320><c> not</c><00:01:13.479><c> using</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
down here in the corner I'm not using
 

00:01:13.799 --> 00:01:16.710 align:start position:0%
down here in the corner I'm not using
co-pilot<00:01:14.560><c> but</c><00:01:14.720><c> rather</c><00:01:15.000><c> llama</c><00:01:15.400><c> coder</c><00:01:16.400><c> I</c><00:01:16.479><c> can</c>

00:01:16.710 --> 00:01:16.720 align:start position:0%
co-pilot but rather llama coder I can
 

00:01:16.720 --> 00:01:19.190 align:start position:0%
co-pilot but rather llama coder I can
even<00:01:17.080><c> add</c><00:01:17.320><c> a</c><00:01:17.520><c> comment</c><00:01:17.960><c> here</c><00:01:18.320><c> describing</c><00:01:19.080><c> what</c>

00:01:19.190 --> 00:01:19.200 align:start position:0%
even add a comment here describing what
 

00:01:19.200 --> 00:01:21.830 align:start position:0%
even add a comment here describing what
I<00:01:19.320><c> want</c><00:01:19.720><c> and</c><00:01:19.880><c> llama</c><00:01:20.280><c> coder</c><00:01:20.880><c> writes</c><00:01:21.240><c> the</c><00:01:21.400><c> code</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
I want and llama coder writes the code
 

00:01:21.840 --> 00:01:24.950 align:start position:0%
I want and llama coder writes the code
for<00:01:22.079><c> me</c><00:01:22.920><c> it's</c><00:01:23.119><c> not</c><00:01:23.320><c> always</c><00:01:24.000><c> perfect</c><00:01:24.720><c> but</c><00:01:24.840><c> it's</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
for me it's not always perfect but it's
 

00:01:24.960 --> 00:01:27.390 align:start position:0%
for me it's not always perfect but it's
a<00:01:25.159><c> good</c><00:01:25.360><c> start</c><00:01:26.360><c> now</c><00:01:26.520><c> it's</c><00:01:26.680><c> printing</c><00:01:27.119><c> each</c><00:01:27.280><c> of</c>

00:01:27.390 --> 00:01:27.400 align:start position:0%
a good start now it's printing each of
 

00:01:27.400 --> 00:01:28.990 align:start position:0%
a good start now it's printing each of
the<00:01:27.520><c> Json</c><00:01:27.960><c> blobs</c><00:01:28.360><c> and</c><00:01:28.479><c> I</c><00:01:28.600><c> just</c><00:01:28.720><c> want</c><00:01:28.880><c> the</c>

00:01:28.990 --> 00:01:29.000 align:start position:0%
the Json blobs and I just want the
 

00:01:29.000 --> 00:01:31.469 align:start position:0%
the Json blobs and I just want the
tokens<00:01:29.560><c> so</c><00:01:29.920><c> I'll</c><00:01:30.119><c> add</c><00:01:30.360><c> message.</c><00:01:31.079><c> content</c><00:01:31.360><c> to</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
tokens so I'll add message. content to
 

00:01:31.479 --> 00:01:34.069 align:start position:0%
tokens so I'll add message. content to
the<00:01:31.600><c> console</c><00:01:32.000><c> log</c><00:01:32.240><c> statement</c><00:01:33.240><c> and</c><00:01:33.399><c> now</c><00:01:33.600><c> I</c><00:01:33.759><c> have</c>

00:01:34.069 --> 00:01:34.079 align:start position:0%
the console log statement and now I have
 

00:01:34.079 --> 00:01:35.710 align:start position:0%
the console log statement and now I have
each<00:01:34.240><c> of</c><00:01:34.360><c> the</c><00:01:34.479><c> words</c><00:01:34.880><c> but</c><00:01:35.200><c> they're</c><00:01:35.399><c> on</c><00:01:35.560><c> their</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
each of the words but they're on their
 

00:01:35.720 --> 00:01:38.870 align:start position:0%
each of the words but they're on their
own<00:01:36.360><c> lines</c><00:01:37.360><c> at</c><00:01:37.479><c> this</c><00:01:37.640><c> point</c><00:01:38.200><c> I</c><00:01:38.280><c> need</c><00:01:38.439><c> to</c><00:01:38.600><c> have</c><00:01:38.720><c> a</c>

00:01:38.870 --> 00:01:38.880 align:start position:0%
own lines at this point I need to have a
 

00:01:38.880 --> 00:01:41.190 align:start position:0%
own lines at this point I need to have a
conversation<00:01:39.520><c> with</c><00:01:39.759><c> my</c><00:01:40.000><c> code</c><00:01:40.640><c> and</c><00:01:40.840><c> with</c><00:01:41.000><c> an</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
conversation with my code and with an
 

00:01:41.200 --> 00:01:43.389 align:start position:0%
conversation with my code and with an
expert<00:01:41.920><c> to</c><00:01:42.079><c> figure</c><00:01:42.360><c> out</c><00:01:42.640><c> how</c><00:01:42.799><c> to</c><00:01:43.000><c> do</c><00:01:43.200><c> this</c>

00:01:43.389 --> 00:01:43.399 align:start position:0%
expert to figure out how to do this
 

00:01:43.399 --> 00:01:45.190 align:start position:0%
expert to figure out how to do this
without<00:01:43.680><c> new</c><00:01:43.960><c> lines</c><00:01:44.520><c> and</c><00:01:44.640><c> so</c><00:01:44.799><c> that's</c><00:01:44.960><c> where</c>

00:01:45.190 --> 00:01:45.200 align:start position:0%
without new lines and so that's where
 

00:01:45.200 --> 00:01:47.789 align:start position:0%
without new lines and so that's where
continue.<00:01:45.960><c> Dev</c><00:01:46.240><c> comes</c><00:01:46.439><c> in</c><00:01:47.159><c> I</c><00:01:47.240><c> can</c><00:01:47.399><c> select</c><00:01:47.680><c> the</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
continue. Dev comes in I can select the
 

00:01:47.799 --> 00:01:50.630 align:start position:0%
continue. Dev comes in I can select the
code<00:01:48.119><c> then</c><00:01:48.280><c> press</c><00:01:48.719><c> command</c><00:01:49.240><c> shift</c><00:01:49.600><c> M</c><00:01:50.280><c> and</c><00:01:50.439><c> ask</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
code then press command shift M and ask
 

00:01:50.640 --> 00:01:52.789 align:start position:0%
code then press command shift M and ask
a<00:01:50.840><c> question</c><00:01:51.680><c> is</c><00:01:51.799><c> there</c><00:01:51.920><c> an</c><00:01:52.119><c> alternative</c><00:01:52.600><c> to</c>

00:01:52.789 --> 00:01:52.799 align:start position:0%
a question is there an alternative to
 

00:01:52.799 --> 00:01:55.789 align:start position:0%
a question is there an alternative to
console<00:01:53.159><c> log</c><00:01:53.560><c> that</c><00:01:54.000><c> doesn't</c><00:01:54.399><c> add</c><00:01:54.680><c> a</c><00:01:54.840><c> new</c><00:01:55.040><c> line</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
console log that doesn't add a new line
 

00:01:55.799 --> 00:01:57.830 align:start position:0%
console log that doesn't add a new line
and<00:01:55.920><c> it</c><00:01:56.079><c> reminds</c><00:01:56.479><c> me</c><00:01:56.680><c> about</c><00:01:57.079><c> process.st</c>

00:01:57.830 --> 00:01:57.840 align:start position:0%
and it reminds me about process.st
 

00:01:57.840 --> 00:01:59.830 align:start position:0%
and it reminds me about process.st
standard<00:01:58.119><c> out.</c><00:01:58.719><c> write</c><00:01:59.360><c> which</c><00:01:59.439><c> is</c><00:01:59.600><c> perfect</c>

00:01:59.830 --> 00:01:59.840 align:start position:0%
standard out. write which is perfect
 

00:01:59.840 --> 00:02:02.230 align:start position:0%
standard out. write which is perfect
perfect<00:02:00.520><c> so</c><00:02:00.719><c> I</c><00:02:00.840><c> start</c><00:02:01.079><c> to</c><00:02:01.280><c> type</c><00:02:01.479><c> it</c><00:02:01.600><c> in</c><00:02:02.039><c> and</c>

00:02:02.230 --> 00:02:02.240 align:start position:0%
perfect so I start to type it in and
 

00:02:02.240 --> 00:02:04.510 align:start position:0%
perfect so I start to type it in and
llama<00:02:02.600><c> coder</c><00:02:03.119><c> knows</c><00:02:03.399><c> what</c><00:02:03.520><c> I</c><00:02:03.640><c> want</c><00:02:04.079><c> and</c><00:02:04.200><c> allows</c>

00:02:04.510 --> 00:02:04.520 align:start position:0%
llama coder knows what I want and allows
 

00:02:04.520 --> 00:02:06.830 align:start position:0%
llama coder knows what I want and allows
me<00:02:04.640><c> to</c><00:02:04.840><c> tap</c><00:02:05.119><c> to</c><00:02:05.399><c> complete</c><00:02:06.399><c> these</c><00:02:06.600><c> two</c>

00:02:06.830 --> 00:02:06.840 align:start position:0%
me to tap to complete these two
 

00:02:06.840 --> 00:02:08.949 align:start position:0%
me to tap to complete these two
extensions<00:02:07.320><c> for</c><00:02:07.520><c> vs</c><00:02:07.880><c> code</c><00:02:08.200><c> provide</c><00:02:08.479><c> a</c><00:02:08.720><c> pretty</c>

00:02:08.949 --> 00:02:08.959 align:start position:0%
extensions for vs code provide a pretty
 

00:02:08.959 --> 00:02:11.309 align:start position:0%
extensions for vs code provide a pretty
good<00:02:09.160><c> alternative</c><00:02:09.679><c> to</c><00:02:09.920><c> co-pilot</c><00:02:10.920><c> they</c><00:02:11.039><c> aren't</c>

00:02:11.309 --> 00:02:11.319 align:start position:0%
good alternative to co-pilot they aren't
 

00:02:11.319 --> 00:02:13.830 align:start position:0%
good alternative to co-pilot they aren't
perfect<00:02:12.040><c> but</c><00:02:12.160><c> they're</c><00:02:12.440><c> completely</c><00:02:12.920><c> free</c><00:02:13.640><c> and</c>

00:02:13.830 --> 00:02:13.840 align:start position:0%
perfect but they're completely free and
 

00:02:13.840 --> 00:02:16.430 align:start position:0%
perfect but they're completely free and
even<00:02:14.080><c> better</c><00:02:14.640><c> they</c><00:02:14.800><c> work</c><00:02:15.080><c> when</c><00:02:15.239><c> I</c><00:02:15.440><c> have</c><00:02:16.120><c> no</c>

00:02:16.430 --> 00:02:16.440 align:start position:0%
even better they work when I have no
 

00:02:16.440 --> 00:02:18.509 align:start position:0%
even better they work when I have no
connection<00:02:16.879><c> to</c><00:02:17.080><c> the</c><00:02:17.200><c> internet</c><00:02:18.120><c> you</c><00:02:18.319><c> might</c>

00:02:18.509 --> 00:02:18.519 align:start position:0%
connection to the internet you might
 

00:02:18.519 --> 00:02:20.550 align:start position:0%
connection to the internet you might
think<00:02:18.800><c> that</c><00:02:18.959><c> rarely</c><00:02:19.360><c> happens</c><00:02:19.760><c> but</c><00:02:20.040><c> I</c><00:02:20.200><c> live</c><00:02:20.400><c> on</c>

00:02:20.550 --> 00:02:20.560 align:start position:0%
think that rarely happens but I live on
 

00:02:20.560 --> 00:02:22.750 align:start position:0%
think that rarely happens but I live on
an<00:02:20.760><c> island</c><00:02:21.200><c> near</c><00:02:21.400><c> Seattle</c><00:02:22.239><c> and</c><00:02:22.319><c> to</c><00:02:22.440><c> get</c><00:02:22.560><c> to</c><00:02:22.680><c> the</c>

00:02:22.750 --> 00:02:22.760 align:start position:0%
an island near Seattle and to get to the
 

00:02:22.760 --> 00:02:25.309 align:start position:0%
an island near Seattle and to get to the
mainland<00:02:23.160><c> I</c><00:02:23.239><c> have</c><00:02:23.319><c> to</c><00:02:23.440><c> get</c><00:02:23.560><c> on</c><00:02:23.800><c> a</c><00:02:24.000><c> ferry</c><00:02:25.000><c> and</c>

00:02:25.309 --> 00:02:25.319 align:start position:0%
mainland I have to get on a ferry and
 

00:02:25.319 --> 00:02:26.910 align:start position:0%
mainland I have to get on a ferry and
there<00:02:25.440><c> are</c><00:02:25.599><c> no</c><00:02:25.720><c> cell</c><00:02:26.040><c> towers</c><00:02:26.360><c> in</c><00:02:26.440><c> the</c><00:02:26.560><c> middle</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
there are no cell towers in the middle
 

00:02:26.920 --> 00:02:29.270 align:start position:0%
there are no cell towers in the middle
of<00:02:27.080><c> Puget</c><00:02:27.440><c> Sound</c><00:02:28.319><c> and</c><00:02:28.440><c> the</c><00:02:28.640><c> idea</c><00:02:28.959><c> of</c><00:02:29.120><c> just</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
of Puget Sound and the idea of just
 

00:02:29.280 --> 00:02:30.910 align:start position:0%
of Puget Sound and the idea of just
sitting<00:02:29.599><c> there</c><00:02:29.920><c> there</c><00:02:30.080><c> and</c><00:02:30.280><c> enjoying</c><00:02:30.720><c> the</c>

00:02:30.910 --> 00:02:30.920 align:start position:0%
sitting there there and enjoying the
 

00:02:30.920 --> 00:02:33.430 align:start position:0%
sitting there there and enjoying the
tremendous<00:02:31.440><c> view</c><00:02:32.400><c> when</c><00:02:32.599><c> I</c><00:02:32.760><c> could</c><00:02:32.879><c> be</c><00:02:33.040><c> buried</c>

00:02:33.430 --> 00:02:33.440 align:start position:0%
tremendous view when I could be buried
 

00:02:33.440 --> 00:02:36.990 align:start position:0%
tremendous view when I could be buried
in<00:02:33.760><c> code</c><00:02:34.640><c> it's</c><00:02:34.879><c> just</c><00:02:35.360><c> Insanity</c><00:02:36.360><c> so</c><00:02:36.640><c> let's</c><00:02:36.879><c> take</c>

00:02:36.990 --> 00:02:37.000 align:start position:0%
in code it's just Insanity so let's take
 

00:02:37.000 --> 00:02:39.309 align:start position:0%
in code it's just Insanity so let's take
a<00:02:37.319><c> brief</c><00:02:37.640><c> look</c><00:02:38.120><c> at</c><00:02:38.280><c> what's</c><00:02:38.519><c> involved</c><00:02:39.159><c> in</c>

00:02:39.309 --> 00:02:39.319 align:start position:0%
a brief look at what's involved in
 

00:02:39.319 --> 00:02:41.710 align:start position:0%
a brief look at what's involved in
setting<00:02:39.640><c> this</c><00:02:39.840><c> up</c><00:02:40.519><c> of</c><00:02:40.640><c> course</c><00:02:40.800><c> you</c><00:02:40.920><c> need</c><00:02:41.159><c> AMA</c>

00:02:41.710 --> 00:02:41.720 align:start position:0%
setting this up of course you need AMA
 

00:02:41.720 --> 00:02:45.670 align:start position:0%
setting this up of course you need AMA
installed<00:02:42.239><c> and</c><00:02:42.440><c> up</c><00:02:42.599><c> and</c><00:02:42.760><c> running</c><00:02:43.640><c> go</c><00:02:43.800><c> to</c><00:02:44.480><c> .i</c><00:02:45.480><c> to</c>

00:02:45.670 --> 00:02:45.680 align:start position:0%
installed and up and running go to .i to
 

00:02:45.680 --> 00:02:49.030 align:start position:0%
installed and up and running go to .i to
learn<00:02:46.040><c> more</c><00:02:46.480><c> about</c><00:02:46.720><c> doing</c><00:02:47.080><c> that</c><00:02:48.080><c> next</c><00:02:48.640><c> install</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
learn more about doing that next install
 

00:02:49.040 --> 00:02:51.070 align:start position:0%
learn more about doing that next install
the<00:02:49.159><c> Llama</c><00:02:49.560><c> coder</c><00:02:50.000><c> extension</c><00:02:50.599><c> and</c><00:02:50.760><c> take</c><00:02:50.920><c> a</c>

00:02:51.070 --> 00:02:51.080 align:start position:0%
the Llama coder extension and take a
 

00:02:51.080 --> 00:02:53.790 align:start position:0%
the Llama coder extension and take a
look<00:02:51.360><c> at</c><00:02:51.519><c> the</c><00:02:51.720><c> settings</c><00:02:52.720><c> I'm</c><00:02:52.840><c> using</c><00:02:53.159><c> the</c><00:02:53.280><c> model</c>

00:02:53.790 --> 00:02:53.800 align:start position:0%
look at the settings I'm using the model
 

00:02:53.800 --> 00:02:58.070 align:start position:0%
look at the settings I'm using the model
deep<00:02:54.080><c> seek</c><00:02:54.360><c> coder</c><00:02:55.120><c> 1.3b</c><00:02:56.360><c> Q4</c><00:02:57.360><c> it's</c><00:02:57.599><c> nice</c><00:02:57.760><c> and</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
deep seek coder 1.3b Q4 it's nice and
 

00:02:58.080 --> 00:02:59.949 align:start position:0%
deep seek coder 1.3b Q4 it's nice and
fast<00:02:58.360><c> to</c><00:02:58.519><c> work</c><00:02:58.760><c> with</c><00:02:59.080><c> and</c><00:02:59.200><c> seems</c><00:02:59.440><c> to</c><00:02:59.560><c> do</c><00:02:59.760><c> do</c><00:02:59.879><c> a</c>

00:02:59.949 --> 00:02:59.959 align:start position:0%
fast to work with and seems to do do a
 

00:02:59.959 --> 00:03:01.990 align:start position:0%
fast to work with and seems to do do a
good<00:03:00.159><c> job</c><00:03:00.879><c> you</c><00:03:01.000><c> should</c><00:03:01.239><c> play</c><00:03:01.440><c> around</c><00:03:01.800><c> with</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
good job you should play around with
 

00:03:02.000 --> 00:03:04.470 align:start position:0%
good job you should play around with
which<00:03:02.239><c> model</c><00:03:02.640><c> works</c><00:03:02.959><c> best</c><00:03:03.159><c> for</c><00:03:03.319><c> you</c><00:03:04.200><c> if</c><00:03:04.319><c> a</c>

00:03:04.470 --> 00:03:04.480 align:start position:0%
which model works best for you if a
 

00:03:04.480 --> 00:03:06.309 align:start position:0%
which model works best for you if a
model<00:03:04.799><c> has</c><00:03:04.959><c> better</c><00:03:05.239><c> answers</c><00:03:05.599><c> but</c><00:03:05.720><c> takes</c><00:03:06.000><c> 5</c><00:03:06.200><c> to</c>

00:03:06.309 --> 00:03:06.319 align:start position:0%
model has better answers but takes 5 to
 

00:03:06.319 --> 00:03:08.710 align:start position:0%
model has better answers but takes 5 to
10<00:03:06.680><c> seconds</c><00:03:07.120><c> to</c><00:03:07.280><c> generate</c><00:03:08.200><c> then</c><00:03:08.319><c> I'll</c><00:03:08.519><c> never</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
10 seconds to generate then I'll never
 

00:03:08.720 --> 00:03:11.470 align:start position:0%
10 seconds to generate then I'll never
see<00:03:08.959><c> those</c><00:03:09.200><c> answers</c><00:03:10.159><c> setting</c><00:03:10.760><c> that</c><00:03:10.959><c> model</c><00:03:11.280><c> to</c>

00:03:11.470 --> 00:03:11.480 align:start position:0%
see those answers setting that model to
 

00:03:11.480 --> 00:03:14.110 align:start position:0%
see those answers setting that model to
use<00:03:12.000><c> is</c><00:03:12.280><c> really</c><00:03:12.680><c> all</c><00:03:12.920><c> you</c><00:03:13.040><c> need</c><00:03:13.319><c> to</c>

00:03:14.110 --> 00:03:14.120 align:start position:0%
use is really all you need to
 

00:03:14.120 --> 00:03:17.350 align:start position:0%
use is really all you need to
configure<00:03:15.120><c> next</c><00:03:15.519><c> install</c><00:03:16.159><c> continue</c><00:03:17.159><c> there</c>

00:03:17.350 --> 00:03:17.360 align:start position:0%
configure next install continue there
 

00:03:17.360 --> 00:03:19.229 align:start position:0%
configure next install continue there
aren't<00:03:17.760><c> really</c><00:03:18.120><c> any</c><00:03:18.319><c> settings</c><00:03:18.720><c> up</c><00:03:18.920><c> front</c><00:03:19.159><c> that</c>

00:03:19.229 --> 00:03:19.239 align:start position:0%
aren't really any settings up front that
 

00:03:19.239 --> 00:03:21.670 align:start position:0%
aren't really any settings up front that
you<00:03:19.319><c> need</c><00:03:19.440><c> to</c><00:03:19.599><c> set</c><00:03:20.080><c> but</c><00:03:20.640><c> press</c><00:03:20.920><c> command</c><00:03:21.319><c> shift</c>

00:03:21.670 --> 00:03:21.680 align:start position:0%
you need to set but press command shift
 

00:03:21.680 --> 00:03:25.110 align:start position:0%
you need to set but press command shift
M<00:03:22.200><c> on</c><00:03:22.440><c> Mac</c><00:03:22.760><c> or</c><00:03:23.000><c> control</c><00:03:23.400><c> shift</c><00:03:23.799><c> M</c><00:03:24.080><c> on</c><00:03:24.280><c> Windows</c>

00:03:25.110 --> 00:03:25.120 align:start position:0%
M on Mac or control shift M on Windows
 

00:03:25.120 --> 00:03:26.470 align:start position:0%
M on Mac or control shift M on Windows
and<00:03:25.239><c> then</c><00:03:25.440><c> down</c><00:03:25.640><c> at</c><00:03:25.799><c> the</c><00:03:25.959><c> bottom</c><00:03:26.280><c> you</c><00:03:26.360><c> can</c>

00:03:26.470 --> 00:03:26.480 align:start position:0%
and then down at the bottom you can
 

00:03:26.480 --> 00:03:28.789 align:start position:0%
and then down at the bottom you can
select<00:03:26.760><c> the</c><00:03:26.920><c> model</c><00:03:27.239><c> to</c><00:03:27.400><c> use</c><00:03:28.239><c> I've</c><00:03:28.360><c> stuck</c><00:03:28.640><c> with</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
select the model to use I've stuck with
 

00:03:28.799 --> 00:03:31.110 align:start position:0%
select the model to use I've stuck with
code<00:03:29.080><c> llama</c><00:03:29.480><c> but</c><00:03:29.799><c> you</c><00:03:29.879><c> can</c><00:03:30.080><c> add</c><00:03:30.400><c> any</c><00:03:30.680><c> model</c>

00:03:31.110 --> 00:03:31.120 align:start position:0%
code llama but you can add any model
 

00:03:31.120 --> 00:03:33.670 align:start position:0%
code llama but you can add any model
available<00:03:31.519><c> with</c><00:03:31.720><c> AMA</c><00:03:32.560><c> by</c><00:03:32.760><c> just</c><00:03:33.040><c> updating</c><00:03:33.519><c> this</c>

00:03:33.670 --> 00:03:33.680 align:start position:0%
available with AMA by just updating this
 

00:03:33.680 --> 00:03:36.149 align:start position:0%
available with AMA by just updating this
Json<00:03:34.200><c> config</c><00:03:34.599><c> file</c><00:03:35.519><c> take</c><00:03:35.680><c> a</c><00:03:35.799><c> look</c><00:03:35.920><c> at</c><00:03:36.040><c> the</c>

00:03:36.149 --> 00:03:36.159 align:start position:0%
Json config file take a look at the
 

00:03:36.159 --> 00:03:38.190 align:start position:0%
Json config file take a look at the
continue<00:03:36.560><c> docks</c><00:03:37.200><c> there's</c><00:03:37.400><c> another</c><00:03:37.879><c> setting</c>

00:03:38.190 --> 00:03:38.200 align:start position:0%
continue docks there's another setting
 

00:03:38.200 --> 00:03:40.550 align:start position:0%
continue docks there's another setting
to<00:03:38.439><c> disable</c><00:03:38.879><c> Telemetry</c><00:03:39.599><c> so</c><00:03:39.920><c> that</c><00:03:40.040><c> it</c><00:03:40.200><c> doesn't</c>

00:03:40.550 --> 00:03:40.560 align:start position:0%
to disable Telemetry so that it doesn't
 

00:03:40.560 --> 00:03:42.750 align:start position:0%
to disable Telemetry so that it doesn't
try<00:03:40.760><c> to</c><00:03:40.879><c> use</c><00:03:41.120><c> the</c><00:03:41.239><c> internet</c><00:03:41.519><c> for</c><00:03:41.720><c> anything</c><00:03:42.599><c> and</c>

00:03:42.750 --> 00:03:42.760 align:start position:0%
try to use the internet for anything and
 

00:03:42.760 --> 00:03:44.830 align:start position:0%
try to use the internet for anything and
there's<00:03:42.959><c> a</c><00:03:43.120><c> lot</c><00:03:43.360><c> more</c><00:03:43.760><c> that</c><00:03:43.959><c> continue</c><00:03:44.360><c> can</c><00:03:44.519><c> do</c>

00:03:44.830 --> 00:03:44.840 align:start position:0%
there's a lot more that continue can do
 

00:03:44.840 --> 00:03:47.910 align:start position:0%
there's a lot more that continue can do
so<00:03:45.239><c> be</c><00:03:45.360><c> sure</c><00:03:45.560><c> to</c><00:03:45.760><c> review</c><00:03:46.080><c> the</c><00:03:46.200><c> docs</c><00:03:46.720><c> as</c><00:03:46.920><c> well</c>

00:03:47.910 --> 00:03:47.920 align:start position:0%
so be sure to review the docs as well
 

00:03:47.920 --> 00:03:49.350 align:start position:0%
so be sure to review the docs as well
there<00:03:48.040><c> are</c><00:03:48.200><c> a</c><00:03:48.280><c> few</c><00:03:48.519><c> other</c><00:03:48.799><c> VSS</c><00:03:49.120><c> code</c>

00:03:49.350 --> 00:03:49.360 align:start position:0%
there are a few other VSS code
 

00:03:49.360 --> 00:03:52.589 align:start position:0%
there are a few other VSS code
extensions<00:03:49.840><c> available</c><00:03:50.319><c> like</c><00:03:50.760><c> CBT</c><00:03:51.760><c> and</c><00:03:52.000><c> olama</c>

00:03:52.589 --> 00:03:52.599 align:start position:0%
extensions available like CBT and olama
 

00:03:52.599 --> 00:03:55.190 align:start position:0%
extensions available like CBT and olama
auto<00:03:52.959><c> coder</c><00:03:53.640><c> but</c><00:03:53.840><c> llama</c><00:03:54.239><c> coder</c><00:03:54.519><c> and</c><00:03:54.720><c> continue</c>

00:03:55.190 --> 00:03:55.200 align:start position:0%
auto coder but llama coder and continue
 

00:03:55.200 --> 00:03:57.670 align:start position:0%
auto coder but llama coder and continue
seem<00:03:55.400><c> to</c><00:03:55.519><c> work</c><00:03:55.840><c> better</c><00:03:56.159><c> best</c><00:03:56.319><c> for</c><00:03:56.519><c> me</c><00:03:57.319><c> I'd</c><00:03:57.480><c> also</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
seem to work better best for me I'd also
 

00:03:57.680 --> 00:03:59.229 align:start position:0%
seem to work better best for me I'd also
like<00:03:57.840><c> to</c><00:03:57.959><c> spend</c><00:03:58.200><c> some</c><00:03:58.360><c> time</c><00:03:58.519><c> with</c><00:03:58.720><c> Cody</c><00:03:59.079><c> from</c>

00:03:59.229 --> 00:03:59.239 align:start position:0%
like to spend some time with Cody from
 

00:03:59.239 --> 00:04:00.390 align:start position:0%
like to spend some time with Cody from
Source<00:03:59.480><c> Gra</c>

00:04:00.390 --> 00:04:00.400 align:start position:0%
Source Gra
 

00:04:00.400 --> 00:04:03.270 align:start position:0%
Source Gra
the<00:04:00.560><c> CEO</c><00:04:01.079><c> was</c><00:04:01.400><c> pretty</c><00:04:01.760><c> excited</c><00:04:02.280><c> when</c><00:04:02.519><c> AMA</c><00:04:03.079><c> came</c>

00:04:03.270 --> 00:04:03.280 align:start position:0%
the CEO was pretty excited when AMA came
 

00:04:03.280 --> 00:04:06.470 align:start position:0%
the CEO was pretty excited when AMA came
out<00:04:03.720><c> and</c><00:04:03.920><c> built</c><00:04:04.239><c> functionality</c><00:04:04.920><c> for</c><00:04:05.560><c> AMA</c><00:04:06.120><c> into</c>

00:04:06.470 --> 00:04:06.480 align:start position:0%
out and built functionality for AMA into
 

00:04:06.480 --> 00:04:09.710 align:start position:0%
out and built functionality for AMA into
Cody<00:04:07.200><c> so</c><00:04:07.680><c> I'd</c><00:04:07.879><c> really</c><00:04:08.040><c> like</c><00:04:08.159><c> to</c><00:04:08.319><c> try</c><00:04:08.640><c> that</c><00:04:09.640><c> what</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
Cody so I'd really like to try that what
 

00:04:09.720 --> 00:04:11.750 align:start position:0%
Cody so I'd really like to try that what
do<00:04:09.840><c> you</c><00:04:10.000><c> think</c><00:04:10.519><c> have</c><00:04:10.640><c> you</c><00:04:10.799><c> replaced</c><00:04:11.239><c> co-pilot</c>

00:04:11.750 --> 00:04:11.760 align:start position:0%
do you think have you replaced co-pilot
 

00:04:11.760 --> 00:04:14.069 align:start position:0%
do you think have you replaced co-pilot
with<00:04:11.920><c> something</c><00:04:12.200><c> local</c><00:04:13.079><c> I'd</c><00:04:13.280><c> love</c><00:04:13.480><c> to</c><00:04:13.640><c> learn</c>

00:04:14.069 --> 00:04:14.079 align:start position:0%
with something local I'd love to learn
 

00:04:14.079 --> 00:04:16.710 align:start position:0%
with something local I'd love to learn
about<00:04:14.400><c> your</c><00:04:14.680><c> setup</c><00:04:15.280><c> or</c><00:04:15.799><c> if</c><00:04:15.920><c> there's</c><00:04:16.239><c> another</c>

00:04:16.710 --> 00:04:16.720 align:start position:0%
about your setup or if there's another
 

00:04:16.720 --> 00:04:18.870 align:start position:0%
about your setup or if there's another
config<00:04:17.400><c> that</c><00:04:17.519><c> you'd</c><00:04:17.720><c> like</c><00:04:17.840><c> to</c><00:04:17.959><c> see</c><00:04:18.519><c> please</c><00:04:18.759><c> let</c>

00:04:18.870 --> 00:04:18.880 align:start position:0%
config that you'd like to see please let
 

00:04:18.880 --> 00:04:20.710 align:start position:0%
config that you'd like to see please let
me<00:04:19.000><c> know</c><00:04:19.160><c> in</c><00:04:19.239><c> the</c><00:04:19.400><c> comments</c><00:04:20.199><c> thanks</c><00:04:20.440><c> so</c><00:04:20.560><c> much</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
me know in the comments thanks so much
 

00:04:20.720 --> 00:04:27.060 align:start position:0%
me know in the comments thanks so much
for<00:04:20.919><c> watching</c>

00:04:27.060 --> 00:04:27.070 align:start position:0%
 
 

00:04:27.070 --> 00:04:28.590 align:start position:0%
 
[Music]

00:04:28.590 --> 00:04:28.600 align:start position:0%
[Music]
 

00:04:28.600 --> 00:04:38.270 align:start position:0%
[Music]
goodbye

00:04:38.270 --> 00:04:38.280 align:start position:0%
 
 

00:04:38.280 --> 00:04:43.839 align:start position:0%
 
[Music]

