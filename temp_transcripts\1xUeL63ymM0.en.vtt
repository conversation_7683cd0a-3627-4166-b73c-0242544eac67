WEBVTT
Kind: captions
Language: en

00:00:00.070 --> 00:00:02.389 align:start position:0%
 
[Music]

00:00:02.389 --> 00:00:02.399 align:start position:0%
[Music]
 

00:00:02.399 --> 00:00:04.230 align:start position:0%
[Music]
working<00:00:02.720><c> with</c><00:00:02.960><c> dictionaries</c><00:00:03.439><c> in</c><00:00:03.639><c> <PERSON>son</c><00:00:04.040><c> and</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
working with dictionaries in Json and
 

00:00:04.240 --> 00:00:07.230 align:start position:0%
working with dictionaries in Json and
python<00:00:04.600><c> kind</c><00:00:04.720><c> of</c>

00:00:07.230 --> 00:00:07.240 align:start position:0%
 
 

00:00:07.240 --> 00:00:08.750 align:start position:0%
 
[Music]

00:00:08.750 --> 00:00:08.760 align:start position:0%
[Music]
 

00:00:08.760 --> 00:00:11.470 align:start position:0%
[Music]
suck<00:00:09.760><c> detecting</c><00:00:10.320><c> small</c><00:00:10.639><c> issues</c><00:00:11.000><c> like</c><00:00:11.200><c> this</c><00:00:11.360><c> in</c>

00:00:11.470 --> 00:00:11.480 align:start position:0%
suck detecting small issues like this in
 

00:00:11.480 --> 00:00:13.549 align:start position:0%
suck detecting small issues like this in
your<00:00:11.639><c> code</c><00:00:11.880><c> base</c><00:00:12.240><c> at</c><00:00:12.400><c> runtime</c><00:00:12.880><c> or</c><00:00:13.080><c> even</c><00:00:13.360><c> as</c><00:00:13.440><c> you</c>

00:00:13.549 --> 00:00:13.559 align:start position:0%
your code base at runtime or even as you
 

00:00:13.559 --> 00:00:15.669 align:start position:0%
your code base at runtime or even as you
do<00:00:13.759><c> type</c><00:00:13.960><c> checking</c><00:00:14.639><c> is</c><00:00:14.799><c> going</c><00:00:14.920><c> to</c><00:00:15.120><c> make</c><00:00:15.480><c> your</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
do type checking is going to make your
 

00:00:15.679 --> 00:00:18.310 align:start position:0%
do type checking is going to make your
code<00:00:16.000><c> a</c><00:00:16.119><c> lot</c><00:00:16.279><c> easier</c><00:00:16.600><c> to</c><00:00:16.800><c> read</c><00:00:17.560><c> a</c><00:00:17.680><c> lot</c><00:00:17.880><c> safer</c><00:00:18.160><c> to</c>

00:00:18.310 --> 00:00:18.320 align:start position:0%
code a lot easier to read a lot safer to
 

00:00:18.320 --> 00:00:21.870 align:start position:0%
code a lot easier to read a lot safer to
work<00:00:18.600><c> with</c><00:00:19.439><c> and</c><00:00:19.560><c> just</c><00:00:19.720><c> less</c><00:00:19.880><c> of</c><00:00:20.000><c> a</c>

00:00:21.870 --> 00:00:21.880 align:start position:0%
work with and just less of a
 

00:00:21.880 --> 00:00:24.750 align:start position:0%
work with and just less of a
headache<00:00:22.880><c> hentic</c><00:00:23.519><c> itself</c><00:00:24.039><c> is</c><00:00:24.199><c> really</c><00:00:24.400><c> good</c><00:00:24.560><c> at</c>

00:00:24.750 --> 00:00:24.760 align:start position:0%
headache hentic itself is really good at
 

00:00:24.760 --> 00:00:26.669 align:start position:0%
headache hentic itself is really good at
automatically<00:00:25.400><c> generating</c><00:00:25.840><c> that</c><00:00:26.000><c> schema</c><00:00:26.359><c> for</c>

00:00:26.669 --> 00:00:26.679 align:start position:0%
automatically generating that schema for
 

00:00:26.679 --> 00:00:29.429 align:start position:0%
automatically generating that schema for
you<00:00:27.679><c> we</c><00:00:27.800><c> do</c><00:00:27.960><c> not</c><00:00:28.240><c> have</c><00:00:28.400><c> to</c><00:00:28.679><c> type</c><00:00:28.960><c> up</c><00:00:29.240><c> this</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
you we do not have to type up this
 

00:00:29.439 --> 00:00:31.830 align:start position:0%
you we do not have to type up this
complex<00:00:29.759><c> op</c><00:00:30.160><c> object</c><00:00:31.160><c> instead</c><00:00:31.560><c> just</c><00:00:31.679><c> by</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
complex op object instead just by
 

00:00:31.840 --> 00:00:34.869 align:start position:0%
complex op object instead just by
running<00:00:32.200><c> model</c><00:00:33.200><c> Json</c><00:00:33.640><c> Json</c><00:00:33.960><c> schema</c><00:00:34.399><c> we</c><00:00:34.640><c> get</c>

00:00:34.869 --> 00:00:34.879 align:start position:0%
running model Json Json schema we get
 

00:00:34.879 --> 00:00:37.430 align:start position:0%
running model Json Json schema we get
that<00:00:35.120><c> object</c><00:00:35.480><c> back</c>

00:00:37.430 --> 00:00:37.440 align:start position:0%
that object back
 

00:00:37.440 --> 00:00:40.510 align:start position:0%
that object back
out<00:00:38.440><c> our</c><00:00:38.600><c> prompt</c><00:00:38.920><c> is</c><00:00:39.040><c> very</c><00:00:39.280><c> simple</c><00:00:40.079><c> it's</c><00:00:40.360><c> just</c>

00:00:40.510 --> 00:00:40.520 align:start position:0%
out our prompt is very simple it's just
 

00:00:40.520 --> 00:00:43.190 align:start position:0%
out our prompt is very simple it's just
the<00:00:40.719><c> name</c><00:00:41.039><c> Harry</c><00:00:41.520><c> Potter</c><00:00:42.520><c> And</c><00:00:42.719><c> now</c><00:00:42.960><c> when</c><00:00:43.079><c> we</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
the name Harry Potter And now when we
 

00:00:43.200 --> 00:00:46.110 align:start position:0%
the name Harry Potter And now when we
run<00:00:43.559><c> this</c><00:00:43.719><c> code</c><00:00:44.360><c> we</c><00:00:44.640><c> extract</c><00:00:45.360><c> an</c><00:00:45.559><c> object</c><00:00:45.960><c> that</c>

00:00:46.110 --> 00:00:46.120 align:start position:0%
run this code we extract an object that
 

00:00:46.120 --> 00:00:49.350 align:start position:0%
run this code we extract an object that
has<00:00:46.280><c> the</c><00:00:46.520><c> age</c><00:00:47.520><c> name</c><00:00:48.360><c> and</c><00:00:48.480><c> in</c><00:00:48.640><c> this</c><00:00:48.840><c> instance</c>

00:00:49.350 --> 00:00:49.360 align:start position:0%
has the age name and in this instance
 

00:00:49.360 --> 00:00:51.590 align:start position:0%
has the age name and in this instance
the<00:00:49.520><c> house</c><00:00:49.879><c> attribute</c><00:00:50.680><c> is</c><00:00:50.800><c> one</c><00:00:50.920><c> of</c><00:00:51.039><c> the</c><00:00:51.199><c> values</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
the house attribute is one of the values
 

00:00:51.600 --> 00:00:55.150 align:start position:0%
the house attribute is one of the values
of<00:00:51.800><c> the</c><00:00:51.960><c> house</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
 
 

00:00:55.160 --> 00:00:57.389 align:start position:0%
 
enum<00:00:56.160><c> we</c><00:00:56.280><c> can</c><00:00:56.440><c> actually</c><00:00:56.640><c> see</c><00:00:57.000><c> of</c><00:00:57.120><c> all</c><00:00:57.280><c> the</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
enum we can actually see of all the
 

00:00:57.399 --> 00:00:59.910 align:start position:0%
enum we can actually see of all the
examples<00:00:57.800><c> we</c><00:00:57.960><c> have</c><00:00:58.199><c> here</c><00:00:59.199><c> we</c><00:00:59.320><c> see</c><00:00:59.480><c> that</c><00:00:59.600><c> the</c>

00:00:59.910 --> 00:00:59.920 align:start position:0%
examples we have here we see that the
 

00:00:59.920 --> 00:01:01.630 align:start position:0%
examples we have here we see that the
input<00:01:00.600><c> was</c><00:01:00.840><c> latest</c><00:01:01.120><c> development</c><00:01:01.519><c> and</c>

00:01:01.630 --> 00:01:01.640 align:start position:0%
input was latest development and
 

00:01:01.640 --> 00:01:05.550 align:start position:0%
input was latest development and
artificial<00:01:02.120><c> itelligence</c><00:01:02.640><c> that</c><00:01:02.800><c> the</c><00:01:02.920><c> last</c><00:01:03.160><c> 3</c>

00:01:05.550 --> 00:01:05.560 align:start position:0%
 
 

00:01:05.560 --> 00:01:07.510 align:start position:0%
 
weeks<00:01:06.560><c> and</c><00:01:06.720><c> now</c><00:01:06.840><c> you</c><00:01:06.960><c> can</c><00:01:07.080><c> see</c><00:01:07.240><c> that</c><00:01:07.360><c> the</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
weeks and now you can see that the
 

00:01:07.520 --> 00:01:09.469 align:start position:0%
weeks and now you can see that the
preview<00:01:07.920><c> query</c><00:01:08.360><c> has</c><00:01:08.520><c> been</c><00:01:08.720><c> written</c><00:01:09.200><c> a</c><00:01:09.320><c> little</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
preview query has been written a little
 

00:01:09.479 --> 00:01:12.070 align:start position:0%
preview query has been written a little
bit<00:01:09.960><c> simpler</c><00:01:10.960><c> we</c><00:01:11.119><c> use</c><00:01:11.360><c> Chain</c><00:01:11.560><c> of</c><00:01:11.759><c> Thought</c><00:01:11.960><c> to</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
bit simpler we use Chain of Thought to
 

00:01:12.080 --> 00:01:14.149 align:start position:0%
bit simpler we use Chain of Thought to
figure<00:01:12.320><c> out</c><00:01:12.479><c> what</c><00:01:12.640><c> time</c><00:01:12.840><c> it</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
figure out what time it
 

00:01:14.159 --> 00:01:23.110 align:start position:0%
figure out what time it
was<00:01:15.159><c> and</c><00:01:15.400><c> we</c><00:01:15.520><c> can</c><00:01:15.640><c> also</c><00:01:15.920><c> track</c><00:01:16.200><c> all</c><00:01:16.320><c> the</c><00:01:16.439><c> usage</c>

00:01:23.110 --> 00:01:23.120 align:start position:0%
 
 

00:01:23.120 --> 00:01:28.830 align:start position:0%
 
[Music]

00:01:28.830 --> 00:01:28.840 align:start position:0%
 
 

00:01:28.840 --> 00:01:31.840 align:start position:0%
 
tokens

