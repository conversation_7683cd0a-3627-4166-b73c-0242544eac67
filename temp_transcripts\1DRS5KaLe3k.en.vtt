WEBVTT
Kind: captions
Language: en

00:00:01.480 --> 00:00:03.949 align:start position:0%
 
okay<00:00:01.719><c> hey</c><00:00:01.920><c> there</c><00:00:02.080><c> this</c><00:00:02.200><c> is</c><00:00:02.440><c> Matt</c><00:00:02.720><c> <PERSON></c><00:00:03.560><c> uh</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
okay hey there this is <PERSON> uh
 

00:00:03.959 --> 00:00:06.190 align:start position:0%
okay hey there this is <PERSON> uh
sorry<00:00:04.279><c> for</c><00:00:04.640><c> the</c><00:00:05.319><c> you</c><00:00:05.440><c> might</c><00:00:05.640><c> hear</c><00:00:05.879><c> some</c>

00:00:06.190 --> 00:00:06.200 align:start position:0%
sorry for the you might hear some
 

00:00:06.200 --> 00:00:09.110 align:start position:0%
sorry for the you might hear some
background<00:00:06.879><c> noise</c><00:00:07.879><c> um</c><00:00:08.160><c> there's</c><00:00:08.360><c> a</c><00:00:08.599><c> generator</c>

00:00:09.110 --> 00:00:09.120 align:start position:0%
background noise um there's a generator
 

00:00:09.120 --> 00:00:11.430 align:start position:0%
background noise um there's a generator
on<00:00:09.480><c> that's</c><00:00:09.880><c> right</c><00:00:10.080><c> outside</c><00:00:10.559><c> my</c><00:00:10.719><c> window</c><00:00:11.200><c> the</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
on that's right outside my window the
 

00:00:11.440 --> 00:00:14.990 align:start position:0%
on that's right outside my window the
power<00:00:11.759><c> went</c><00:00:11.960><c> out</c><00:00:12.320><c> about</c><00:00:12.719><c> 3</c><00:00:12.920><c> or</c><00:00:13.080><c> 4</c><00:00:13.320><c> hours</c><00:00:13.759><c> ago</c><00:00:14.759><c> uh</c>

00:00:14.990 --> 00:00:15.000 align:start position:0%
power went out about 3 or 4 hours ago uh
 

00:00:15.000 --> 00:00:17.150 align:start position:0%
power went out about 3 or 4 hours ago uh
pretty<00:00:15.200><c> much</c><00:00:15.400><c> the</c><00:00:15.519><c> entire</c><00:00:15.799><c> Island</c><00:00:16.520><c> and</c><00:00:16.680><c> so</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
pretty much the entire Island and so
 

00:00:17.160 --> 00:00:20.710 align:start position:0%
pretty much the entire Island and so
I've<00:00:17.520><c> had</c><00:00:17.680><c> to</c><00:00:17.800><c> deal</c><00:00:18.000><c> with</c><00:00:18.199><c> this</c><00:00:18.960><c> for</c><00:00:19.960><c> hours</c>

00:00:20.710 --> 00:00:20.720 align:start position:0%
I've had to deal with this for hours
 

00:00:20.720 --> 00:00:22.870 align:start position:0%
I've had to deal with this for hours
anyway<00:00:21.320><c> so</c><00:00:21.560><c> let's</c><00:00:21.760><c> take</c><00:00:21.880><c> a</c><00:00:22.000><c> look</c><00:00:22.279><c> at</c><00:00:22.519><c> just</c><00:00:22.680><c> a</c>

00:00:22.870 --> 00:00:22.880 align:start position:0%
anyway so let's take a look at just a
 

00:00:22.880 --> 00:00:25.029 align:start position:0%
anyway so let's take a look at just a
simple<00:00:23.240><c> example</c><00:00:24.119><c> uh</c><00:00:24.240><c> you</c><00:00:24.359><c> said</c><00:00:24.680><c> that</c><00:00:24.800><c> you're</c>

00:00:25.029 --> 00:00:25.039 align:start position:0%
simple example uh you said that you're
 

00:00:25.039 --> 00:00:28.109 align:start position:0%
simple example uh you said that you're
having<00:00:25.279><c> problem</c><00:00:25.599><c> with</c><00:00:25.960><c> uh</c><00:00:26.080><c> dolphin</c><00:00:26.400><c> mistol</c><00:00:26.840><c> 7B</c>

00:00:28.109 --> 00:00:28.119 align:start position:0%
having problem with uh dolphin mistol 7B
 

00:00:28.119 --> 00:00:31.349 align:start position:0%
having problem with uh dolphin mistol 7B
v2.8<00:00:29.119><c> so</c><00:00:29.480><c> I've</c><00:00:29.640><c> got</c><00:00:29.800><c> a</c><00:00:30.240><c> simple</c><00:00:30.720><c> bit</c><00:00:30.880><c> of</c><00:00:31.039><c> code</c>

00:00:31.349 --> 00:00:31.359 align:start position:0%
v2.8 so I've got a simple bit of code
 

00:00:31.359 --> 00:00:34.709 align:start position:0%
v2.8 so I've got a simple bit of code
here<00:00:32.119><c> uh</c><00:00:32.279><c> let's</c><00:00:32.480><c> take</c><00:00:32.599><c> a</c><00:00:32.719><c> look</c><00:00:33.079><c> at</c><00:00:33.719><c> index.ts</c>

00:00:34.709 --> 00:00:34.719 align:start position:0%
here uh let's take a look at index.ts
 

00:00:34.719 --> 00:00:36.510 align:start position:0%
here uh let's take a look at index.ts
again<00:00:34.920><c> I'm</c><00:00:35.040><c> using</c><00:00:35.480><c> uh</c><00:00:35.640><c> typescript</c><00:00:36.160><c> because</c><00:00:36.399><c> I</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
again I'm using uh typescript because I
 

00:00:36.520 --> 00:00:38.830 align:start position:0%
again I'm using uh typescript because I
tend<00:00:36.680><c> to</c><00:00:36.760><c> use</c><00:00:36.920><c> typescript</c><00:00:37.800><c> but</c><00:00:38.079><c> here's</c><00:00:38.360><c> my</c><00:00:38.719><c> uh</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
tend to use typescript but here's my uh
 

00:00:38.840 --> 00:00:42.310 align:start position:0%
tend to use typescript but here's my uh
simple<00:00:39.320><c> code</c><00:00:40.000><c> um</c><00:00:41.000><c> I'm</c><00:00:41.160><c> importing</c><00:00:41.520><c> a</c><00:00:41.719><c> llama</c><00:00:42.200><c> and</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
simple code um I'm importing a llama and
 

00:00:42.320 --> 00:00:44.709 align:start position:0%
simple code um I'm importing a llama and
I'm<00:00:42.480><c> importing</c><00:00:43.079><c> a</c><00:00:43.320><c> a</c><00:00:43.840><c> a</c><00:00:44.000><c> nice</c><00:00:44.200><c> little</c><00:00:44.440><c> package</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
I'm importing a a a nice little package
 

00:00:44.719 --> 00:00:46.549 align:start position:0%
I'm importing a a a nice little package
called<00:00:44.920><c> Dirty</c><00:00:45.200><c> Json</c><00:00:45.640><c> if</c><00:00:45.760><c> you're</c><00:00:46.120><c> embedding</c>

00:00:46.549 --> 00:00:46.559 align:start position:0%
called Dirty Json if you're embedding
 

00:00:46.559 --> 00:00:49.189 align:start position:0%
called Dirty Json if you're embedding
Json<00:00:47.239><c> in</c><00:00:47.440><c> Json</c><00:00:47.960><c> it's</c><00:00:48.280><c> sometimes</c><00:00:48.480><c> really</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
Json in Json it's sometimes really
 

00:00:49.199 --> 00:00:53.389 align:start position:0%
Json in Json it's sometimes really
useful<00:00:50.199><c> and</c><00:00:50.760><c> um</c><00:00:51.640><c> uh</c><00:00:52.039><c> sometimes</c>

00:00:53.389 --> 00:00:53.399 align:start position:0%
useful and um uh sometimes
 

00:00:53.399 --> 00:00:56.310 align:start position:0%
useful and um uh sometimes
olama<00:00:54.399><c> in</c><00:00:54.520><c> the</c><00:00:54.640><c> last</c><00:00:54.840><c> few</c><00:00:55.079><c> versions</c><00:00:55.800><c> sometimes</c>

00:00:56.310 --> 00:00:56.320 align:start position:0%
olama in the last few versions sometimes
 

00:00:56.320 --> 00:00:59.509 align:start position:0%
olama in the last few versions sometimes
returns<00:00:56.800><c> without</c><00:00:57.120><c> being</c><00:00:57.640><c> done</c><00:00:58.480><c> and</c><00:00:58.680><c> that</c><00:00:59.359><c> uh</c>

00:00:59.509 --> 00:00:59.519 align:start position:0%
returns without being done and that uh
 

00:00:59.519 --> 00:01:01.270 align:start position:0%
returns without being done and that uh
causes<00:01:00.199><c> some</c><00:01:00.320><c> sort</c><00:01:00.480><c> of</c><00:01:00.559><c> error</c><00:01:00.840><c> so</c><00:01:00.920><c> I've</c><00:01:01.039><c> got</c><00:01:01.160><c> a</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
causes some sort of error so I've got a
 

00:01:01.280 --> 00:01:03.069 align:start position:0%
causes some sort of error so I've got a
little<00:01:01.440><c> Tri</c><00:01:01.719><c> catch</c><00:01:01.960><c> block</c><00:01:02.320><c> and</c><00:01:02.879><c> that's</c>

00:01:03.069 --> 00:01:03.079 align:start position:0%
little Tri catch block and that's
 

00:01:03.079 --> 00:01:06.310 align:start position:0%
little Tri catch block and that's
unrelated<00:01:03.600><c> to</c><00:01:03.879><c> Json</c><00:01:04.479><c> it's</c><00:01:04.720><c> just</c><00:01:05.119><c> something</c><00:01:05.560><c> in</c>

00:01:06.310 --> 00:01:06.320 align:start position:0%
unrelated to Json it's just something in
 

00:01:06.320 --> 00:01:08.550 align:start position:0%
unrelated to Json it's just something in
ama<00:01:07.159><c> recently</c><00:01:07.840><c> I'm</c><00:01:07.960><c> not</c><00:01:08.080><c> really</c><00:01:08.240><c> sure</c><00:01:08.400><c> what's</c>

00:01:08.550 --> 00:01:08.560 align:start position:0%
ama recently I'm not really sure what's
 

00:01:08.560 --> 00:01:12.550 align:start position:0%
ama recently I'm not really sure what's
going<00:01:08.720><c> on</c><00:01:08.920><c> there</c><00:01:09.759><c> but</c><00:01:10.159><c> um</c><00:01:10.880><c> so</c><00:01:11.439><c> kind</c><00:01:11.600><c> of</c><00:01:11.960><c> go</c><00:01:12.240><c> past</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
going on there but um so kind of go past
 

00:01:12.560 --> 00:01:15.270 align:start position:0%
going on there but um so kind of go past
that<00:01:13.040><c> and</c><00:01:13.240><c> you</c><00:01:13.360><c> see</c><00:01:13.600><c> there</c><00:01:13.759><c> is</c><00:01:14.000><c> the</c><00:01:14.600><c> in</c><00:01:14.840><c> line</c>

00:01:15.270 --> 00:01:15.280 align:start position:0%
that and you see there is the in line
 

00:01:15.280 --> 00:01:18.390 align:start position:0%
that and you see there is the in line
seven<00:01:15.759><c> here</c><00:01:16.640><c> um</c><00:01:16.799><c> I'm</c><00:01:16.920><c> using</c><00:01:17.360><c> AMA</c><00:01:17.840><c> generate</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
seven here um I'm using AMA generate
 

00:01:18.400 --> 00:01:20.789 align:start position:0%
seven here um I'm using AMA generate
because<00:01:18.600><c> I'm</c><00:01:18.799><c> not</c><00:01:19.799><c> I'm</c><00:01:19.920><c> not</c><00:01:20.079><c> doing</c><00:01:20.400><c> anything</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
because I'm not I'm not doing anything
 

00:01:20.799 --> 00:01:22.710 align:start position:0%
because I'm not I'm not doing anything
with<00:01:21.240><c> uh</c><00:01:21.360><c> a</c><00:01:21.520><c> conversation</c><00:01:22.159><c> so</c><00:01:22.320><c> I</c><00:01:22.400><c> don't</c><00:01:22.520><c> really</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
with uh a conversation so I don't really
 

00:01:22.720 --> 00:01:26.109 align:start position:0%
with uh a conversation so I don't really
need<00:01:22.960><c> the</c><00:01:23.119><c> chat</c><00:01:24.320><c> endpoint</c><00:01:25.320><c> give</c><00:01:25.439><c> me</c><00:01:25.560><c> a</c><00:01:25.720><c> list</c><00:01:25.960><c> of</c>

00:01:26.109 --> 00:01:26.119 align:start position:0%
need the chat endpoint give me a list of
 

00:01:26.119 --> 00:01:28.190 align:start position:0%
need the chat endpoint give me a list of
three<00:01:26.400><c> random</c><00:01:26.720><c> color</c><00:01:27.000><c> names</c><00:01:27.360><c> only</c><00:01:27.680><c> output</c>

00:01:28.190 --> 00:01:28.200 align:start position:0%
three random color names only output
 

00:01:28.200 --> 00:01:31.270 align:start position:0%
three random color names only output
names<00:01:28.640><c> in</c><00:01:28.920><c> lowercase</c><00:01:29.960><c> output</c><00:01:30.360><c> in</c><00:01:30.479><c> Json</c><00:01:30.880><c> format</c>

00:01:31.270 --> 00:01:31.280 align:start position:0%
names in lowercase output in Json format
 

00:01:31.280 --> 00:01:33.389 align:start position:0%
names in lowercase output in Json format
using<00:01:31.600><c> the</c><00:01:31.720><c> following</c><00:01:32.200><c> template</c><00:01:33.119><c> and</c><00:01:33.240><c> then</c>

00:01:33.389 --> 00:01:33.399 align:start position:0%
using the following template and then
 

00:01:33.399 --> 00:01:36.389 align:start position:0%
using the following template and then
I've<00:01:33.600><c> got</c><00:01:34.240><c> um</c><00:01:34.479><c> basically</c><00:01:34.759><c> a</c><00:01:34.880><c> Json</c><00:01:35.759><c> uh</c><00:01:35.920><c> blob</c>

00:01:36.389 --> 00:01:36.399 align:start position:0%
I've got um basically a Json uh blob
 

00:01:36.399 --> 00:01:38.910 align:start position:0%
I've got um basically a Json uh blob
with<00:01:36.799><c> colors</c><00:01:37.799><c> uh</c><00:01:38.079><c> and</c><00:01:38.240><c> that's</c><00:01:38.399><c> set</c><00:01:38.600><c> to</c><00:01:38.759><c> an</c>

00:01:38.910 --> 00:01:38.920 align:start position:0%
with colors uh and that's set to an
 

00:01:38.920 --> 00:01:42.069 align:start position:0%
with colors uh and that's set to an
array<00:01:39.520><c> of</c><00:01:40.360><c> colors</c><00:01:41.000><c> and</c><00:01:41.159><c> I</c><00:01:41.280><c> have</c><00:01:41.439><c> three</c><00:01:41.759><c> random</c>

00:01:42.069 --> 00:01:42.079 align:start position:0%
array of colors and I have three random
 

00:01:42.079 --> 00:01:44.429 align:start position:0%
array of colors and I have three random
colors<00:01:42.360><c> color</c><00:01:42.600><c> one</c><00:01:42.799><c> color</c><00:01:43.000><c> two</c><00:01:43.280><c> color</c><00:01:43.520><c> three</c>

00:01:44.429 --> 00:01:44.439 align:start position:0%
colors color one color two color three
 

00:01:44.439 --> 00:01:47.469 align:start position:0%
colors color one color two color three
uh<00:01:44.600><c> very</c><00:01:44.840><c> important</c><00:01:45.560><c> is</c><00:01:46.040><c> output</c><00:01:46.439><c> in</c><00:01:46.600><c> Json</c><00:01:47.240><c> or</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
uh very important is output in Json or
 

00:01:47.479 --> 00:01:49.870 align:start position:0%
uh very important is output in Json or
output<00:01:48.040><c> as</c><00:01:48.280><c> Json</c><00:01:48.880><c> or</c><00:01:49.079><c> you</c><00:01:49.200><c> know</c><00:01:49.360><c> something</c>

00:01:49.870 --> 00:01:49.880 align:start position:0%
output as Json or you know something
 

00:01:49.880 --> 00:01:53.230 align:start position:0%
output as Json or you know something
about<00:01:50.159><c> saying</c><00:01:50.719><c> output</c><00:01:51.159><c> it</c><00:01:51.360><c> as</c><00:01:51.479><c> a</c><00:01:51.640><c> Json</c><00:01:52.320><c> thing</c>

00:01:53.230 --> 00:01:53.240 align:start position:0%
about saying output it as a Json thing
 

00:01:53.240 --> 00:01:56.350 align:start position:0%
about saying output it as a Json thing
um<00:01:53.479><c> but</c><00:01:53.640><c> then</c><00:01:54.000><c> also</c><00:01:54.640><c> in</c><00:01:54.880><c> the</c><00:01:55.280><c> parameters</c><00:01:56.000><c> for</c>

00:01:56.350 --> 00:01:56.360 align:start position:0%
um but then also in the parameters for
 

00:01:56.360 --> 00:01:59.429 align:start position:0%
um but then also in the parameters for
the<00:01:56.560><c> or</c><00:01:56.759><c> the</c><00:01:56.920><c> options</c><00:01:57.439><c> for</c><00:01:57.840><c> the</c><00:01:58.200><c> call</c><00:01:59.039><c> saying</c>

00:01:59.429 --> 00:01:59.439 align:start position:0%
the or the options for the call saying
 

00:01:59.439 --> 00:02:00.389 align:start position:0%
the or the options for the call saying
format

00:02:00.389 --> 00:02:00.399 align:start position:0%
format
 

00:02:00.399 --> 00:02:03.149 align:start position:0%
format
is<00:02:00.600><c> Json</c><00:02:01.360><c> that's</c><00:02:01.600><c> super</c><00:02:01.880><c> important</c><00:02:02.320><c> otherwise</c>

00:02:03.149 --> 00:02:03.159 align:start position:0%
is Json that's super important otherwise
 

00:02:03.159 --> 00:02:05.870 align:start position:0%
is Json that's super important otherwise
yeah<00:02:03.320><c> you're</c><00:02:03.439><c> going</c><00:02:03.520><c> to</c><00:02:03.840><c> get</c><00:02:04.840><c> um</c><00:02:05.320><c> some</c><00:02:05.600><c> text</c>

00:02:05.870 --> 00:02:05.880 align:start position:0%
yeah you're going to get um some text
 

00:02:05.880 --> 00:02:09.109 align:start position:0%
yeah you're going to get um some text
that<00:02:06.039><c> says</c><00:02:06.399><c> here's</c><00:02:06.640><c> the</c><00:02:06.799><c> Json</c><00:02:07.280><c> or</c><00:02:08.280><c> uh</c><00:02:08.520><c> this</c><00:02:08.679><c> is</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
that says here's the Json or uh this is
 

00:02:09.119 --> 00:02:11.270 align:start position:0%
that says here's the Json or uh this is
I<00:02:09.239><c> think</c><00:02:09.599><c> or</c><00:02:09.879><c> this</c><00:02:09.959><c> is</c><00:02:10.200><c> the</c><00:02:10.520><c> answer</c><00:02:10.800><c> to</c><00:02:11.000><c> your</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
I think or this is the answer to your
 

00:02:11.280 --> 00:02:12.869 align:start position:0%
I think or this is the answer to your
request<00:02:11.640><c> or</c><00:02:11.920><c> you</c><00:02:12.000><c> know</c><00:02:12.120><c> something</c><00:02:12.360><c> goofy</c><00:02:12.720><c> like</c>

00:02:12.869 --> 00:02:12.879 align:start position:0%
request or you know something goofy like
 

00:02:12.879 --> 00:02:15.630 align:start position:0%
request or you know something goofy like
that<00:02:13.319><c> so</c><00:02:13.520><c> always</c><00:02:13.760><c> got</c><00:02:13.879><c> to</c><00:02:14.040><c> have</c><00:02:14.200><c> format</c><00:02:14.680><c> Json</c>

00:02:15.630 --> 00:02:15.640 align:start position:0%
that so always got to have format Json
 

00:02:15.640 --> 00:02:18.350 align:start position:0%
that so always got to have format Json
and<00:02:15.920><c> the</c><00:02:16.040><c> output</c><00:02:16.360><c> in</c><00:02:16.519><c> Json</c><00:02:17.400><c> ideally</c><00:02:18.120><c> you</c><00:02:18.239><c> know</c>

00:02:18.350 --> 00:02:18.360 align:start position:0%
and the output in Json ideally you know
 

00:02:18.360 --> 00:02:21.830 align:start position:0%
and the output in Json ideally you know
you'd<00:02:18.640><c> also</c><00:02:19.040><c> have</c><00:02:19.920><c> uh</c><00:02:20.080><c> maybe</c><00:02:20.599><c> a</c><00:02:20.959><c> an</c><00:02:21.200><c> example</c>

00:02:21.830 --> 00:02:21.840 align:start position:0%
you'd also have uh maybe a an example
 

00:02:21.840 --> 00:02:24.110 align:start position:0%
you'd also have uh maybe a an example
like<00:02:22.000><c> a</c><00:02:22.160><c> a</c><00:02:22.239><c> few</c><00:02:22.560><c> shot</c><00:02:22.840><c> prompt</c><00:02:23.599><c> um</c><00:02:23.760><c> where</c><00:02:23.920><c> you</c>

00:02:24.110 --> 00:02:24.120 align:start position:0%
like a a few shot prompt um where you
 

00:02:24.120 --> 00:02:26.670 align:start position:0%
like a a few shot prompt um where you
provide<00:02:24.480><c> an</c><00:02:24.680><c> example</c><00:02:25.319><c> of</c><00:02:25.760><c> what</c><00:02:25.920><c> the</c><00:02:26.040><c> output</c><00:02:26.480><c> is</c>

00:02:26.670 --> 00:02:26.680 align:start position:0%
provide an example of what the output is
 

00:02:26.680 --> 00:02:28.229 align:start position:0%
provide an example of what the output is
that<00:02:26.800><c> you</c><00:02:26.920><c> want</c><00:02:27.239><c> but</c><00:02:27.519><c> this</c><00:02:27.640><c> seems</c><00:02:27.840><c> to</c><00:02:27.959><c> work</c><00:02:28.120><c> in</c>

00:02:28.229 --> 00:02:28.239 align:start position:0%
that you want but this seems to work in
 

00:02:28.239 --> 00:02:30.589 align:start position:0%
that you want but this seems to work in
this<00:02:28.400><c> case</c><00:02:28.680><c> again</c><00:02:28.879><c> it's</c><00:02:29.040><c> dolphin</c><00:02:29.360><c> mistro</c><00:02:29.879><c> 7B</c>

00:02:30.589 --> 00:02:30.599 align:start position:0%
this case again it's dolphin mistro 7B
 

00:02:30.599 --> 00:02:35.070 align:start position:0%
this case again it's dolphin mistro 7B
v2.8<00:02:31.599><c> and</c><00:02:31.760><c> then</c><00:02:32.319><c> um</c><00:02:32.720><c> when</c><00:02:33.120><c> everything's</c><00:02:34.080><c> done</c>

00:02:35.070 --> 00:02:35.080 align:start position:0%
v2.8 and then um when everything's done
 

00:02:35.080 --> 00:02:39.390 align:start position:0%
v2.8 and then um when everything's done
uh<00:02:35.239><c> at</c><00:02:35.360><c> the</c><00:02:35.519><c> end</c><00:02:36.280><c> I'm</c><00:02:37.200><c> uh</c><00:02:37.959><c> in</c><00:02:38.160><c> here</c><00:02:38.400><c> I'm</c><00:02:38.640><c> in</c><00:02:38.840><c> this</c>

00:02:39.390 --> 00:02:39.400 align:start position:0%
uh at the end I'm uh in here I'm in this
 

00:02:39.400 --> 00:02:44.110 align:start position:0%
uh at the end I'm uh in here I'm in this
uh<00:02:39.599><c> while</c><00:02:39.920><c> loop</c><00:02:40.560><c> while</c><00:02:40.959><c> done</c><00:02:41.519><c> is</c><00:02:42.239><c> false</c><00:02:43.239><c> um</c><00:02:44.000><c> I</c>

00:02:44.110 --> 00:02:44.120 align:start position:0%
uh while loop while done is false um I
 

00:02:44.120 --> 00:02:46.949 align:start position:0%
uh while loop while done is false um I
say<00:02:44.519><c> colors</c><00:02:45.519><c> uh</c><00:02:45.640><c> push</c><00:02:45.920><c> the</c><00:02:46.040><c> colors</c><00:02:46.360><c> to</c><00:02:46.720><c> the</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
say colors uh push the colors to the
 

00:02:46.959 --> 00:02:48.270 align:start position:0%
say colors uh push the colors to the
colors

00:02:48.270 --> 00:02:48.280 align:start position:0%
colors
 

00:02:48.280 --> 00:02:51.030 align:start position:0%
colors
array<00:02:49.519><c> and</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
array and
 

00:02:51.040 --> 00:02:55.149 align:start position:0%
array and
um<00:02:52.040><c> and</c><00:02:52.120><c> so</c><00:02:52.400><c> that</c><00:02:52.760><c> is</c><00:02:53.120><c> the</c><00:02:53.360><c> list</c><00:02:53.640><c> of</c><00:02:53.920><c> colors</c><00:02:54.920><c> and</c>

00:02:55.149 --> 00:02:55.159 align:start position:0%
um and so that is the list of colors and
 

00:02:55.159 --> 00:02:57.270 align:start position:0%
um and so that is the list of colors and
I'm<00:02:55.400><c> just</c><00:02:56.080><c> outputting</c><00:02:56.519><c> the</c><00:02:56.640><c> log</c><00:02:57.080><c> or</c>

00:02:57.270 --> 00:02:57.280 align:start position:0%
I'm just outputting the log or
 

00:02:57.280 --> 00:03:00.350 align:start position:0%
I'm just outputting the log or
outputting<00:02:58.200><c> the</c><00:02:58.680><c> array</c><00:02:59.120><c> of</c><00:02:59.360><c> colors</c>

00:03:00.350 --> 00:03:00.360 align:start position:0%
outputting the array of colors
 

00:03:00.360 --> 00:03:04.309 align:start position:0%
outputting the array of colors
so<00:03:00.840><c> let's</c><00:03:01.159><c> try</c><00:03:01.519><c> this</c><00:03:01.800><c> oh</c><00:03:02.040><c> and</c><00:03:02.239><c> then</c><00:03:03.239><c> I</c><00:03:03.879><c> I</c><00:03:04.159><c> at</c>

00:03:04.309 --> 00:03:04.319 align:start position:0%
so let's try this oh and then I I at
 

00:03:04.319 --> 00:03:06.830 align:start position:0%
so let's try this oh and then I I at
first<00:03:04.840><c> was</c><00:03:05.040><c> doing</c><00:03:05.879><c> um</c><00:03:06.080><c> just</c><00:03:06.239><c> running</c><00:03:06.519><c> it</c><00:03:06.680><c> over</c>

00:03:06.830 --> 00:03:06.840 align:start position:0%
first was doing um just running it over
 

00:03:06.840 --> 00:03:08.430 align:start position:0%
first was doing um just running it over
and<00:03:07.000><c> over</c><00:03:07.239><c> again</c><00:03:07.519><c> which</c><00:03:07.840><c> turned</c><00:03:08.080><c> out</c><00:03:08.159><c> to</c><00:03:08.239><c> be</c><00:03:08.319><c> a</c>

00:03:08.430 --> 00:03:08.440 align:start position:0%
and over again which turned out to be a
 

00:03:08.440 --> 00:03:11.509 align:start position:0%
and over again which turned out to be a
pain<00:03:08.599><c> in</c><00:03:08.680><c> the</c><00:03:08.799><c> ass</c><00:03:09.720><c> so</c><00:03:10.720><c> I</c><00:03:10.920><c> just</c><00:03:11.080><c> created</c><00:03:11.400><c> a</c>

00:03:11.509 --> 00:03:11.519 align:start position:0%
pain in the ass so I just created a
 

00:03:11.519 --> 00:03:13.910 align:start position:0%
pain in the ass so I just created a
simple<00:03:11.959><c> bash</c><00:03:12.959><c> uh</c>

00:03:13.910 --> 00:03:13.920 align:start position:0%
simple bash uh
 

00:03:13.920 --> 00:03:17.910 align:start position:0%
simple bash uh
script<00:03:14.920><c> that</c><00:03:15.200><c> just</c><00:03:15.560><c> says</c><00:03:16.560><c> uh</c><00:03:16.680><c> for</c><00:03:16.920><c> one</c><00:03:17.360><c> for</c><00:03:17.640><c> I</c>

00:03:17.910 --> 00:03:17.920 align:start position:0%
script that just says uh for one for I
 

00:03:17.920 --> 00:03:21.910 align:start position:0%
script that just says uh for one for I
in<00:03:18.159><c> 1</c><00:03:18.400><c> to</c><00:03:18.720><c> 100</c><00:03:19.319><c> do</c><00:03:19.879><c> Bun</c><00:03:20.200><c> Run</c><00:03:20.480><c> index</c><00:03:20.920><c> TS</c><00:03:21.680><c> okay</c><00:03:21.799><c> so</c>

00:03:21.910 --> 00:03:21.920 align:start position:0%
in 1 to 100 do Bun Run index TS okay so
 

00:03:21.920 --> 00:03:23.589 align:start position:0%
in 1 to 100 do Bun Run index TS okay so
it's<00:03:22.040><c> just</c><00:03:22.159><c> going</c><00:03:22.239><c> to</c><00:03:22.440><c> run</c><00:03:22.680><c> that</c><00:03:22.879><c> whole</c><00:03:23.120><c> thing</c>

00:03:23.589 --> 00:03:23.599 align:start position:0%
it's just going to run that whole thing
 

00:03:23.599 --> 00:03:25.149 align:start position:0%
it's just going to run that whole thing
over<00:03:23.879><c> and</c><00:03:24.000><c> over</c><00:03:24.159><c> and</c><00:03:24.280><c> over</c><00:03:24.440><c> again</c><00:03:24.599><c> for</c><00:03:24.760><c> a</c><00:03:24.879><c> 100</c>

00:03:25.149 --> 00:03:25.159 align:start position:0%
over and over and over again for a 100
 

00:03:25.159 --> 00:03:27.190 align:start position:0%
over and over and over again for a 100
times<00:03:25.760><c> and</c><00:03:25.920><c> let's</c><00:03:26.080><c> see</c><00:03:26.319><c> what</c><00:03:26.440><c> it</c>

00:03:27.190 --> 00:03:27.200 align:start position:0%
times and let's see what it
 

00:03:27.200 --> 00:03:32.110 align:start position:0%
times and let's see what it
does<00:03:28.200><c> uh</c><00:03:28.360><c> I</c><00:03:28.680><c> need</c><00:03:28.799><c> to</c><00:03:28.959><c> run</c><00:03:29.280><c> test</c>

00:03:32.110 --> 00:03:32.120 align:start position:0%
 
 

00:03:32.120 --> 00:03:36.550 align:start position:0%
 
100sh<00:03:33.120><c> and</c><00:03:33.640><c> hopefully</c><00:03:34.640><c> if</c><00:03:34.879><c> everything</c><00:03:35.560><c> works</c>

00:03:36.550 --> 00:03:36.560 align:start position:0%
100sh and hopefully if everything works
 

00:03:36.560 --> 00:03:40.149 align:start position:0%
100sh and hopefully if everything works
uh<00:03:36.760><c> now</c><00:03:37.760><c> the</c><00:03:37.959><c> fact</c><00:03:38.159><c> that</c><00:03:38.360><c> it's</c><00:03:38.959><c> I</c><00:03:39.159><c> I'm</c><00:03:39.280><c> saying</c>

00:03:40.149 --> 00:03:40.159 align:start position:0%
uh now the fact that it's I I'm saying
 

00:03:40.159 --> 00:03:43.350 align:start position:0%
uh now the fact that it's I I'm saying
just<00:03:40.480><c> output</c><00:03:41.480><c> colors</c><00:03:42.200><c> which</c><00:03:42.360><c> is</c><00:03:42.519><c> in</c><00:03:42.799><c> the</c>

00:03:43.350 --> 00:03:43.360 align:start position:0%
just output colors which is in the
 

00:03:43.360 --> 00:03:46.110 align:start position:0%
just output colors which is in the
response<00:03:44.360><c> so</c><00:03:45.159><c> if</c>

00:03:46.110 --> 00:03:46.120 align:start position:0%
response so if
 

00:03:46.120 --> 00:03:48.509 align:start position:0%
response so if
the<00:03:47.120><c> um</c><00:03:47.439><c> if</c><00:03:47.519><c> it</c><00:03:47.680><c> was</c><00:03:47.879><c> responding</c><00:03:48.360><c> with</c>

00:03:48.509 --> 00:03:48.519 align:start position:0%
the um if it was responding with
 

00:03:48.519 --> 00:03:51.830 align:start position:0%
the um if it was responding with
something<00:03:49.000><c> other</c><00:03:49.360><c> than</c><00:03:50.080><c> Json</c><00:03:50.799><c> using</c><00:03:51.319><c> the</c><00:03:51.640><c> the</c>

00:03:51.830 --> 00:03:51.840 align:start position:0%
something other than Json using the the
 

00:03:51.840 --> 00:03:55.550 align:start position:0%
something other than Json using the the
value<00:03:52.640><c> the</c><00:03:52.720><c> field</c><00:03:53.400><c> of</c><00:03:53.959><c> colors</c><00:03:54.760><c> this</c><00:03:55.040><c> would</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
value the field of colors this would
 

00:03:55.560 --> 00:03:58.270 align:start position:0%
value the field of colors this would
fail<00:03:56.079><c> it</c><00:03:56.200><c> would</c><00:03:56.599><c> uh</c><00:03:56.680><c> it</c><00:03:56.760><c> would</c><00:03:56.959><c> break</c><00:03:57.959><c> but</c><00:03:58.159><c> it</c>

00:03:58.270 --> 00:03:58.280 align:start position:0%
fail it would uh it would break but it
 

00:03:58.280 --> 00:04:00.470 align:start position:0%
fail it would uh it would break but it
seems<00:03:58.519><c> to</c><00:03:58.599><c> be</c><00:03:58.760><c> doing</c><00:03:59.239><c> great</c>

00:04:00.470 --> 00:04:00.480 align:start position:0%
seems to be doing great
 

00:04:00.480 --> 00:04:02.949 align:start position:0%
seems to be doing great
uh<00:04:00.760><c> 100</c><00:04:01.040><c> times</c><00:04:02.000><c> I</c><00:04:02.079><c> don't</c><00:04:02.200><c> know</c><00:04:02.360><c> how</c><00:04:02.560><c> far</c><00:04:02.879><c> I</c>

00:04:02.949 --> 00:04:02.959 align:start position:0%
uh 100 times I don't know how far I
 

00:04:02.959 --> 00:04:04.750 align:start position:0%
uh 100 times I don't know how far I
should<00:04:03.079><c> have</c><00:04:03.200><c> put</c><00:04:03.319><c> an</c><00:04:03.480><c> index</c><00:04:03.799><c> in</c><00:04:03.959><c> there</c><00:04:04.159><c> to</c><00:04:04.560><c> to</c>

00:04:04.750 --> 00:04:04.760 align:start position:0%
should have put an index in there to to
 

00:04:04.760 --> 00:04:07.429 align:start position:0%
should have put an index in there to to
say<00:04:05.040><c> oh</c><00:04:05.200><c> you're</c><00:04:05.360><c> at</c><00:04:05.879><c> try</c><00:04:06.200><c> number</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
say oh you're at try number
 

00:04:07.439 --> 00:04:10.589 align:start position:0%
say oh you're at try number
50<00:04:08.439><c> but</c><00:04:09.319><c> software</c><00:04:09.720><c> update</c><00:04:10.120><c> I</c><00:04:10.239><c> I</c><00:04:10.319><c> don't</c><00:04:10.439><c> care</c>

00:04:10.589 --> 00:04:10.599 align:start position:0%
50 but software update I I don't care
 

00:04:10.599 --> 00:04:11.910 align:start position:0%
50 but software update I I don't care
about<00:04:10.720><c> a</c><00:04:10.879><c> software</c>

00:04:11.910 --> 00:04:11.920 align:start position:0%
about a software
 

00:04:11.920 --> 00:04:13.789 align:start position:0%
about a software
update

00:04:13.789 --> 00:04:13.799 align:start position:0%
update
 

00:04:13.799 --> 00:04:20.629 align:start position:0%
update
um<00:04:14.799><c> so</c><00:04:14.959><c> it's</c><00:04:15.120><c> still</c>

00:04:20.629 --> 00:04:20.639 align:start position:0%
 
 

00:04:20.639 --> 00:04:22.909 align:start position:0%
 
going<00:04:21.639><c> lots</c><00:04:21.840><c> of</c><00:04:21.919><c> Blues</c><00:04:22.160><c> and</c><00:04:22.360><c> reds</c><00:04:22.639><c> and</c>

00:04:22.909 --> 00:04:22.919 align:start position:0%
going lots of Blues and reds and
 

00:04:22.919 --> 00:04:25.030 align:start position:0%
going lots of Blues and reds and
occasionally<00:04:23.560><c> aquamarine</c><00:04:24.199><c> and</c><00:04:24.320><c> fuchsia</c><00:04:24.759><c> and</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
occasionally aquamarine and fuchsia and
 

00:04:25.040 --> 00:04:29.430 align:start position:0%
occasionally aquamarine and fuchsia and
Olive<00:04:25.520><c> and</c><00:04:26.520><c> magenta</c><00:04:27.120><c> and</c><00:04:27.280><c> teal</c><00:04:28.000><c> fun</c><00:04:28.320><c> names</c><00:04:29.160><c> but</c>

00:04:29.430 --> 00:04:29.440 align:start position:0%
Olive and magenta and teal fun names but
 

00:04:29.440 --> 00:04:31.830 align:start position:0%
Olive and magenta and teal fun names but
looks<00:04:29.600><c> like</c><00:04:29.800><c> like</c><00:04:29.919><c> it</c><00:04:30.080><c> went</c><00:04:30.280><c> through</c><00:04:30.520><c> all</c><00:04:31.120><c> 100</c>

00:04:31.830 --> 00:04:31.840 align:start position:0%
looks like like it went through all 100
 

00:04:31.840 --> 00:04:35.550 align:start position:0%
looks like like it went through all 100
and<00:04:32.160><c> I</c><00:04:32.280><c> didn't</c><00:04:32.720><c> have</c><00:04:33.080><c> any</c><00:04:33.440><c> problem</c><00:04:34.280><c> so</c><00:04:35.280><c> yeah</c>

00:04:35.550 --> 00:04:35.560 align:start position:0%
and I didn't have any problem so yeah
 

00:04:35.560 --> 00:04:39.590 align:start position:0%
and I didn't have any problem so yeah
that<00:04:35.880><c> that</c><00:04:36.479><c> uh</c><00:04:37.320><c> definitely</c><00:04:38.320><c> points</c><00:04:38.680><c> to</c><00:04:38.880><c> it</c>

00:04:39.590 --> 00:04:39.600 align:start position:0%
that that uh definitely points to it
 

00:04:39.600 --> 00:04:42.550 align:start position:0%
that that uh definitely points to it
always<00:04:40.160><c> reliably</c><00:04:40.919><c> spitting</c><00:04:41.400><c> out</c><00:04:41.919><c> only</c><00:04:42.400><c> the</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
always reliably spitting out only the
 

00:04:42.560 --> 00:04:47.150 align:start position:0%
always reliably spitting out only the
Json<00:04:43.240><c> nothing</c><00:04:43.880><c> else</c><00:04:44.880><c> and</c><00:04:45.400><c> it's</c><00:04:46.160><c> um</c><00:04:46.560><c> yeah</c><00:04:47.000><c> it</c>

00:04:47.150 --> 00:04:47.160 align:start position:0%
Json nothing else and it's um yeah it
 

00:04:47.160 --> 00:04:50.110 align:start position:0%
Json nothing else and it's um yeah it
works<00:04:47.840><c> great</c><00:04:48.720><c> so</c><00:04:49.039><c> I</c><00:04:49.120><c> would</c><00:04:49.320><c> love</c><00:04:49.479><c> to</c><00:04:49.600><c> see</c><00:04:49.880><c> any</c>

00:04:50.110 --> 00:04:50.120 align:start position:0%
works great so I would love to see any
 

00:04:50.120 --> 00:04:52.670 align:start position:0%
works great so I would love to see any
sample<00:04:50.440><c> code</c><00:04:50.680><c> from</c><00:04:50.840><c> you</c><00:04:51.080><c> about</c><00:04:51.720><c> that</c><00:04:51.880><c> shows</c>

00:04:52.670 --> 00:04:52.680 align:start position:0%
sample code from you about that shows
 

00:04:52.680 --> 00:04:55.469 align:start position:0%
sample code from you about that shows
how<00:04:52.880><c> it's</c><00:04:53.199><c> not</c><00:04:53.440><c> working</c><00:04:54.440><c> but</c><00:04:54.960><c> this</c><00:04:55.080><c> seems</c><00:04:55.320><c> to</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
how it's not working but this seems to
 

00:04:55.479 --> 00:04:59.790 align:start position:0%
how it's not working but this seems to
be<00:04:55.919><c> pretty</c><00:04:56.759><c> uh</c><00:04:57.600><c> this</c><00:04:57.720><c> is</c><00:04:57.880><c> pretty</c><00:04:58.360><c> perfect</c><00:04:59.360><c> and</c>

00:04:59.790 --> 00:04:59.800 align:start position:0%
be pretty uh this is pretty perfect and
 

00:04:59.800 --> 00:05:02.189 align:start position:0%
be pretty uh this is pretty perfect and
really<00:04:59.960><c> easy</c><00:05:00.199><c> to</c><00:05:00.360><c> do</c><00:05:01.080><c> okay</c><00:05:01.600><c> I'm</c><00:05:01.680><c> going</c><00:05:01.759><c> to</c><00:05:01.919><c> stop</c>

00:05:02.189 --> 00:05:02.199 align:start position:0%
really easy to do okay I'm going to stop
 

00:05:02.199 --> 00:05:04.919 align:start position:0%
really easy to do okay I'm going to stop
now

