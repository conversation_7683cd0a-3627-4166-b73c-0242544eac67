WEBVTT
Kind: captions
Language: en

00:00:00.760 --> 00:00:03.149 align:start position:0%
 
when<00:00:00.880><c> you're</c><00:00:01.040><c> using</c><00:00:01.520><c> AI</c><00:00:01.880><c> coding</c><00:00:02.240><c> tools</c><00:00:02.919><c> be</c>

00:00:03.149 --> 00:00:03.159 align:start position:0%
when you're using AI coding tools be
 

00:00:03.159 --> 00:00:04.990 align:start position:0%
when you're using AI coding tools be
intentional<00:00:03.840><c> and</c><00:00:03.959><c> then</c><00:00:04.160><c> review</c><00:00:04.520><c> the</c><00:00:04.680><c> code</c>

00:00:04.990 --> 00:00:05.000 align:start position:0%
intentional and then review the code
 

00:00:05.000 --> 00:00:07.510 align:start position:0%
intentional and then review the code
that<00:00:05.120><c> it</c><00:00:05.279><c> generates</c><00:00:06.120><c> don't</c><00:00:06.480><c> just</c><00:00:06.759><c> be</c><00:00:06.960><c> like</c><00:00:07.200><c> oh</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
that it generates don't just be like oh
 

00:00:07.520 --> 00:00:09.070 align:start position:0%
that it generates don't just be like oh
it<00:00:07.640><c> generated</c><00:00:08.160><c> this</c><00:00:08.320><c> code</c><00:00:08.559><c> I'm</c><00:00:08.679><c> going</c><00:00:08.760><c> to</c><00:00:08.920><c> put</c>

00:00:09.070 --> 00:00:09.080 align:start position:0%
it generated this code I'm going to put
 

00:00:09.080 --> 00:00:11.230 align:start position:0%
it generated this code I'm going to put
it<00:00:09.200><c> in</c><00:00:09.400><c> my</c><00:00:09.639><c> code</c><00:00:09.920><c> base</c><00:00:10.280><c> reread</c><00:00:10.679><c> it</c><00:00:10.920><c> just</c><00:00:11.080><c> like</c>

00:00:11.230 --> 00:00:11.240 align:start position:0%
it in my code base reread it just like
 

00:00:11.240 --> 00:00:13.709 align:start position:0%
it in my code base reread it just like
you<00:00:11.360><c> would</c><00:00:11.840><c> review</c><00:00:12.320><c> a</c><00:00:12.480><c> co-worker's</c><00:00:13.040><c> work</c><00:00:13.519><c> do</c>

00:00:13.709 --> 00:00:13.719 align:start position:0%
you would review a co-worker's work do
 

00:00:13.719 --> 00:00:16.590 align:start position:0%
you would review a co-worker's work do
that<00:00:13.880><c> with</c><00:00:14.080><c> AI</c><00:00:14.480><c> as</c><00:00:14.679><c> well</c><00:00:15.679><c> we're</c><00:00:16.000><c> seeing</c><00:00:16.359><c> a</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
that with AI as well we're seeing a
 

00:00:16.600 --> 00:00:19.910 align:start position:0%
that with AI as well we're seeing a
convergence<00:00:17.560><c> of</c><00:00:17.840><c> what</c><00:00:18.359><c> a</c><00:00:18.640><c> developer</c><00:00:19.279><c> is</c><00:00:19.560><c> and</c><00:00:19.720><c> a</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
convergence of what a developer is and a
 

00:00:19.920 --> 00:00:23.349 align:start position:0%
convergence of what a developer is and a
security<00:00:20.400><c> engineer</c><00:00:21.080><c> is</c><00:00:21.439><c> because</c><00:00:22.000><c> as</c><00:00:22.240><c> we</c><00:00:22.560><c> push</c>

00:00:23.349 --> 00:00:23.359 align:start position:0%
security engineer is because as we push
 

00:00:23.359 --> 00:00:26.390 align:start position:0%
security engineer is because as we push
more<00:00:23.640><c> and</c><00:00:23.800><c> more</c><00:00:24.199><c> of</c><00:00:24.400><c> our</c><00:00:24.760><c> tooling</c><00:00:25.240><c> to</c><00:00:25.400><c> the</c><00:00:25.640><c> left</c>

00:00:26.390 --> 00:00:26.400 align:start position:0%
more and more of our tooling to the left
 

00:00:26.400 --> 00:00:28.390 align:start position:0%
more and more of our tooling to the left
development<00:00:26.920><c> and</c><00:00:27.160><c> security</c><00:00:27.599><c> are</c><00:00:27.800><c> starting</c><00:00:28.199><c> to</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
development and security are starting to
 

00:00:28.400 --> 00:00:31.070 align:start position:0%
development and security are starting to
be<00:00:28.640><c> the</c><00:00:28.800><c> same</c><00:00:29.199><c> activity</c><00:00:30.199><c> the</c><00:00:30.320><c> software</c><00:00:30.720><c> supply</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
be the same activity the software supply
 

00:00:31.080 --> 00:00:32.830 align:start position:0%
be the same activity the software supply
chain<00:00:31.359><c> starts</c><00:00:31.759><c> with</c><00:00:31.880><c> the</c><00:00:32.000><c> developer</c><00:00:32.680><c> pay</c>

00:00:32.830 --> 00:00:32.840 align:start position:0%
chain starts with the developer pay
 

00:00:32.840 --> 00:00:34.790 align:start position:0%
chain starts with the developer pay
attention<00:00:33.120><c> to</c><00:00:33.320><c> your</c><00:00:33.680><c> dependency</c><00:00:34.160><c> updates</c><00:00:34.640><c> and</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
attention to your dependency updates and
 

00:00:34.800 --> 00:00:36.389 align:start position:0%
attention to your dependency updates and
just<00:00:34.920><c> keep</c><00:00:35.120><c> on</c><00:00:35.320><c> top</c><00:00:35.480><c> of</c><00:00:35.640><c> those</c><00:00:35.960><c> that's</c><00:00:36.160><c> a</c><00:00:36.320><c> a</c>

00:00:36.389 --> 00:00:36.399 align:start position:0%
just keep on top of those that's a a
 

00:00:36.399 --> 00:00:37.990 align:start position:0%
just keep on top of those that's a a
sneaky<00:00:36.800><c> thing</c><00:00:36.920><c> that</c><00:00:37.040><c> can</c><00:00:37.239><c> really</c><00:00:37.640><c> cause</c><00:00:37.840><c> you</c>

00:00:37.990 --> 00:00:38.000 align:start position:0%
sneaky thing that can really cause you
 

00:00:38.000 --> 00:00:40.150 align:start position:0%
sneaky thing that can really cause you
some<00:00:38.200><c> pain</c><00:00:38.440><c> in</c><00:00:38.559><c> the</c><00:00:38.719><c> long</c><00:00:39.000><c> run</c><00:00:39.600><c> the</c><00:00:39.760><c> recent</c>

00:00:40.150 --> 00:00:40.160 align:start position:0%
some pain in the long run the recent
 

00:00:40.160 --> 00:00:41.790 align:start position:0%
some pain in the long run the recent
things<00:00:40.399><c> that</c><00:00:40.520><c> we've</c><00:00:40.719><c> started</c><00:00:41.000><c> to</c><00:00:41.160><c> do</c><00:00:41.600><c> with</c>

00:00:41.790 --> 00:00:41.800 align:start position:0%
things that we've started to do with
 

00:00:41.800 --> 00:00:44.110 align:start position:0%
things that we've started to do with
co-pilot<00:00:42.559><c> are</c><00:00:42.760><c> really</c><00:00:43.039><c> fascinating</c><00:00:43.680><c> so</c><00:00:43.960><c> the</c>

00:00:44.110 --> 00:00:44.120 align:start position:0%
co-pilot are really fascinating so the
 

00:00:44.120 --> 00:00:47.670 align:start position:0%
co-pilot are really fascinating so the
AI<00:00:44.480><c> assisted</c><00:00:45.000><c> autofix</c><00:00:45.960><c> for</c><00:00:46.680><c> vulnerable</c><00:00:47.199><c> code</c>

00:00:47.670 --> 00:00:47.680 align:start position:0%
AI assisted autofix for vulnerable code
 

00:00:47.680 --> 00:00:49.389 align:start position:0%
AI assisted autofix for vulnerable code
that<00:00:47.800><c> happens</c><00:00:48.280><c> potentially</c><00:00:48.719><c> before</c><00:00:48.920><c> you</c><00:00:49.079><c> even</c>

00:00:49.389 --> 00:00:49.399 align:start position:0%
that happens potentially before you even
 

00:00:49.399 --> 00:00:51.150 align:start position:0%
that happens potentially before you even
get<00:00:49.520><c> to</c><00:00:49.719><c> a</c><00:00:49.879><c> review</c><00:00:50.239><c> with</c><00:00:50.399><c> another</c><00:00:50.680><c> human</c><00:00:50.920><c> being</c>

00:00:51.150 --> 00:00:51.160 align:start position:0%
get to a review with another human being
 

00:00:51.160 --> 00:00:53.270 align:start position:0%
get to a review with another human being
there's<00:00:51.360><c> a</c><00:00:51.760><c> a</c><00:00:51.920><c> fix</c><00:00:52.239><c> suggested</c><00:00:52.760><c> for</c><00:00:52.920><c> you</c><00:00:53.160><c> right</c>

00:00:53.270 --> 00:00:53.280 align:start position:0%
there's a a fix suggested for you right
 

00:00:53.280 --> 00:00:55.349 align:start position:0%
there's a a fix suggested for you right
out<00:00:53.440><c> of</c><00:00:53.559><c> the</c><00:00:53.719><c> gate</c><00:00:54.239><c> copilot</c><00:00:54.760><c> is</c><00:00:54.879><c> actually</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
out of the gate copilot is actually
 

00:00:55.359 --> 00:00:57.790 align:start position:0%
out of the gate copilot is actually
helping<00:00:55.640><c> me</c><00:00:55.920><c> explain</c><00:00:56.520><c> code</c><00:00:57.199><c> help</c><00:00:57.440><c> me</c>

00:00:57.790 --> 00:00:57.800 align:start position:0%
helping me explain code help me
 

00:00:57.800 --> 00:01:00.150 align:start position:0%
helping me explain code help me
summarize<00:00:58.800><c> the</c><00:00:58.960><c> change</c><00:00:59.359><c> to</c><00:00:59.519><c> the</c><00:00:59.600><c> code</c><00:01:00.039><c> done</c>

00:01:00.150 --> 00:01:00.160 align:start position:0%
summarize the change to the code done
 

00:01:00.160 --> 00:01:02.549 align:start position:0%
summarize the change to the code done
and<00:01:00.320><c> put</c><00:01:00.480><c> that</c><00:01:00.600><c> change</c><00:01:00.840><c> in</c><00:01:00.960><c> a</c><00:01:01.160><c> PR</c><00:01:01.840><c> so</c><00:01:02.000><c> it's</c><00:01:02.199><c> gone</c>

00:01:02.549 --> 00:01:02.559 align:start position:0%
and put that change in a PR so it's gone
 

00:01:02.559 --> 00:01:05.750 align:start position:0%
and put that change in a PR so it's gone
really<00:01:02.920><c> beyond</c><00:01:03.440><c> the</c><00:01:03.600><c> editor</c><00:01:04.439><c> and</c><00:01:04.720><c> Beyond</c><00:01:05.560><c> just</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
really beyond the editor and Beyond just
 

00:01:05.760 --> 00:01:08.429 align:start position:0%
really beyond the editor and Beyond just
code<00:01:06.040><c> generation</c><00:01:07.000><c> into</c><00:01:07.560><c> the</c><00:01:07.880><c> github.com</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
code generation into the github.com
 

00:01:08.439 --> 00:01:10.749 align:start position:0%
code generation into the github.com
experience<00:01:09.159><c> across</c><00:01:09.439><c> the</c><00:01:09.560><c> whole</c><00:01:09.840><c> platform</c><00:01:10.640><c> one</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
experience across the whole platform one
 

00:01:10.759 --> 00:01:12.830 align:start position:0%
experience across the whole platform one
of<00:01:10.920><c> the</c><00:01:11.280><c> problems</c><00:01:12.119><c> with</c><00:01:12.320><c> these</c><00:01:12.520><c> large</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
of the problems with these large
 

00:01:12.840 --> 00:01:14.350 align:start position:0%
of the problems with these large
foundational<00:01:13.320><c> models</c><00:01:13.640><c> is</c><00:01:13.799><c> they</c><00:01:13.920><c> can</c><00:01:14.040><c> do</c>

00:01:14.350 --> 00:01:14.360 align:start position:0%
foundational models is they can do
 

00:01:14.360 --> 00:01:16.590 align:start position:0%
foundational models is they can do
almost<00:01:14.960><c> anything</c><00:01:15.720><c> there's</c><00:01:15.880><c> a</c><00:01:16.000><c> developer</c>

00:01:16.590 --> 00:01:16.600 align:start position:0%
almost anything there's a developer
 

00:01:16.600 --> 00:01:18.429 align:start position:0%
almost anything there's a developer
you're<00:01:16.720><c> often</c><00:01:17.040><c> very</c><00:01:17.280><c> tempted</c><00:01:17.720><c> to</c><00:01:17.920><c> kind</c><00:01:18.080><c> of</c>

00:01:18.429 --> 00:01:18.439 align:start position:0%
you're often very tempted to kind of
 

00:01:18.439 --> 00:01:21.149 align:start position:0%
you're often very tempted to kind of
over<00:01:18.759><c> engineer</c><00:01:19.320><c> and</c><00:01:19.640><c> over</c><00:01:19.960><c> optimize</c><00:01:20.600><c> things</c>

00:01:21.149 --> 00:01:21.159 align:start position:0%
over engineer and over optimize things
 

00:01:21.159 --> 00:01:22.950 align:start position:0%
over engineer and over optimize things
so<00:01:21.400><c> you</c><00:01:21.680><c> pick</c><00:01:21.880><c> the</c><00:01:22.040><c> right</c><00:01:22.240><c> size</c><00:01:22.479><c> one</c><00:01:22.640><c> for</c><00:01:22.799><c> your</c>

00:01:22.950 --> 00:01:22.960 align:start position:0%
so you pick the right size one for your
 

00:01:22.960 --> 00:01:24.789 align:start position:0%
so you pick the right size one for your
task<00:01:23.280><c> based</c><00:01:23.520><c> on</c><00:01:23.640><c> its</c><00:01:23.920><c> characteristics</c><00:01:24.640><c> and</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
task based on its characteristics and
 

00:01:24.799 --> 00:01:26.390 align:start position:0%
task based on its characteristics and
what<00:01:24.880><c> you're</c><00:01:25.040><c> trying</c><00:01:25.200><c> to</c><00:01:25.360><c> do</c><00:01:25.960><c> if</c><00:01:26.079><c> there's</c><00:01:26.240><c> a</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
what you're trying to do if there's a
 

00:01:26.400 --> 00:01:28.469 align:start position:0%
what you're trying to do if there's a
security<00:01:26.920><c> issue</c><00:01:27.680><c> you</c><00:01:27.759><c> should</c><00:01:27.960><c> treat</c><00:01:28.159><c> it</c><00:01:28.320><c> like</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
security issue you should treat it like
 

00:01:28.479 --> 00:01:30.789 align:start position:0%
security issue you should treat it like
any<00:01:28.720><c> other</c><00:01:29.000><c> bug</c><00:01:29.479><c> priority</c><00:01:29.880><c> I</c><00:01:30.119><c> ize</c><00:01:30.320><c> it</c><00:01:30.400><c> and</c><00:01:30.600><c> fix</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
any other bug priority I ize it and fix
 

00:01:30.799 --> 00:01:32.630 align:start position:0%
any other bug priority I ize it and fix
it<00:01:31.240><c> the</c><00:01:31.360><c> best</c><00:01:31.520><c> way</c><00:01:31.640><c> to</c><00:01:31.799><c> do</c><00:01:31.960><c> that</c><00:01:32.119><c> of</c><00:01:32.240><c> course</c><00:01:32.520><c> is</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
it the best way to do that of course is
 

00:01:32.640 --> 00:01:34.469 align:start position:0%
it the best way to do that of course is
to<00:01:32.799><c> use</c><00:01:33.040><c> different</c><00:01:33.320><c> tools</c><00:01:33.759><c> that</c><00:01:34.040><c> every</c><00:01:34.280><c> time</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
to use different tools that every time
 

00:01:34.479 --> 00:01:36.910 align:start position:0%
to use different tools that every time
you<00:01:34.680><c> commit</c><00:01:34.960><c> a</c><00:01:35.119><c> change</c><00:01:35.920><c> it's</c><00:01:36.119><c> checking</c><00:01:36.600><c> to</c><00:01:36.680><c> see</c>

00:01:36.910 --> 00:01:36.920 align:start position:0%
you commit a change it's checking to see
 

00:01:36.920 --> 00:01:37.950 align:start position:0%
you commit a change it's checking to see
whether<00:01:37.079><c> or</c><00:01:37.240><c> not</c><00:01:37.360><c> there's</c><00:01:37.520><c> some</c><00:01:37.640><c> known</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
whether or not there's some known
 

00:01:37.960 --> 00:01:40.030 align:start position:0%
whether or not there's some known
security<00:01:38.399><c> issue</c><00:01:39.000><c> otherwise</c><00:01:39.520><c> there's</c><00:01:39.720><c> no</c><00:01:39.880><c> way</c>

00:01:40.030 --> 00:01:40.040 align:start position:0%
security issue otherwise there's no way
 

00:01:40.040 --> 00:01:42.030 align:start position:0%
security issue otherwise there's no way
you<00:01:40.119><c> could</c><00:01:40.320><c> keep</c><00:01:40.520><c> up</c><00:01:40.720><c> with</c><00:01:40.840><c> the</c><00:01:41.079><c> velocity</c><00:01:41.960><c> I</c>

00:01:42.030 --> 00:01:42.040 align:start position:0%
you could keep up with the velocity I
 

00:01:42.040 --> 00:01:44.230 align:start position:0%
you could keep up with the velocity I
think<00:01:42.159><c> we'll</c><00:01:42.320><c> see</c><00:01:42.640><c> more</c><00:01:43.399><c> natural</c><00:01:43.799><c> language</c>

00:01:44.230 --> 00:01:44.240 align:start position:0%
think we'll see more natural language
 

00:01:44.240 --> 00:01:46.670 align:start position:0%
think we'll see more natural language
coding<00:01:45.040><c> where</c><00:01:45.439><c> someone's</c><00:01:45.960><c> ability</c><00:01:46.320><c> to</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
coding where someone's ability to
 

00:01:46.680 --> 00:01:49.069 align:start position:0%
coding where someone's ability to
express<00:01:47.399><c> what</c><00:01:47.600><c> they</c><00:01:47.759><c> want</c><00:01:48.320><c> becomes</c><00:01:48.799><c> one</c><00:01:48.960><c> of</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
express what they want becomes one of
 

00:01:49.079 --> 00:01:52.709 align:start position:0%
express what they want becomes one of
the<00:01:49.280><c> most</c><00:01:49.759><c> uh</c><00:01:49.920><c> important</c><00:01:50.759><c> factors</c><00:01:51.759><c> and</c><00:01:52.399><c> the</c>

00:01:52.709 --> 00:01:52.719 align:start position:0%
the most uh important factors and the
 

00:01:52.719 --> 00:01:55.630 align:start position:0%
the most uh important factors and the
all<00:01:52.960><c> the</c><00:01:53.079><c> nitty-gritty</c><00:01:54.040><c> details</c><00:01:55.040><c> about</c><00:01:55.439><c> the</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
all the nitty-gritty details about the
 

00:01:55.640 --> 00:01:57.469 align:start position:0%
all the nitty-gritty details about the
specific<00:01:56.000><c> coding</c><00:01:56.360><c> language</c><00:01:56.680><c> or</c><00:01:56.920><c> the</c><00:01:57.079><c> specific</c>

00:01:57.469 --> 00:01:57.479 align:start position:0%
specific coding language or the specific
 

00:01:57.479 --> 00:01:59.950 align:start position:0%
specific coding language or the specific
Technologies<00:01:58.039><c> in</c><00:01:58.280><c> use</c><00:01:58.680><c> Fade</c><00:01:59.000><c> Away</c><00:01:59.520><c> into</c><00:01:59.680><c> the</c>

00:01:59.950 --> 00:01:59.960 align:start position:0%
Technologies in use Fade Away into the
 

00:01:59.960 --> 00:02:01.590 align:start position:0%
Technologies in use Fade Away into the
background<00:02:00.399><c> because</c><00:02:00.719><c> AI</c><00:02:01.000><c> handles</c><00:02:01.280><c> a</c><00:02:01.360><c> lot</c><00:02:01.479><c> of</c>

00:02:01.590 --> 00:02:01.600 align:start position:0%
background because AI handles a lot of
 

00:02:01.600 --> 00:02:05.310 align:start position:0%
background because AI handles a lot of
that<00:02:01.719><c> for</c>

00:02:05.310 --> 00:02:05.320 align:start position:0%
 
 

00:02:05.320 --> 00:02:08.320 align:start position:0%
 
you

