export interface VideoSummary {
  id: string;
  video_id: string;
  title: string;
  summary_text: string | null;
  created_at: string;
  updated_at: string;
  channel_id: string | null;
  channel_title: string | null;
  channel_name: string | null; // Added missing property
  video_published_at: string | null;
  published_at: string | null; // Added missing property  
  thumbnail_url: string | null;
  duration: string | null;
  views: number | null;
  view_count: number | null; // Added missing property
  likes: number | null;
  raw_transcript_id: string | null;
  llm_prompt_template_id: string | null;
  llm_model_name: string | null;
  llm_response_format_id: string | null;
  llm_response: Record<string, unknown> | null; // Changed from llm_summary to llm_response and type to Record<string, unknown>
  transcript: string | null; // Added missing property
  video_url: string | null; // Added missing property
  processing_status: string | null;
  processing_error_message: string | null;
  topic_table: string | null;
  topic_category: string | null; // Added missing property
}

export interface TopicTable {
  id: string;
  name: string;
  display_name: string; // Added missing property
  description: string | null;
  table_name: string; // e.g., 'technology_videos', 'science_videos'
  video_count: number; // Added missing property
  created_at: string;
  updated_at: string;
}

export interface SearchResult {
  video: VideoSummary;
  similarity_score: number;
  relevant_transcript_chunks: string[];
}

// Chat-related types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date | string;
  context_videos?: VideoSummary[];
}

export interface ChatRequest {
  message: string;
  topic_filters?: string[]; // Added optional topic_filters
  search_query?: string; // Added optional search_query
  context: {
    videos: Array<{
      title: string;
      channel: string | null;
      summary: string | null;
      published: string | null;
    }>;
    topics: string[];
    channels: string[];
  };
}

export interface ChatResponse {
  response: string;
  context_videos?: VideoSummary[];
  // search_results: [] // Removed as it's not defined and context_videos seems to cover this
}

// You can add other shared types here as your application grows.
