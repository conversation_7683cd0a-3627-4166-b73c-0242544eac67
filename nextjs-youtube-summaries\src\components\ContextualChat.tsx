'use client'

import { useState } from 'react'
import { Send, MessageCircle, Bo<PERSON>, User, AlertTriangle } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { VideoSummary } from '@/types' // Use same VideoSummary type as DatabaseService
import { MAX_CONTEXT_TOKENS, optimizeContextForChat } from '@/lib/tokenUtils'

interface ContextualChatProps {
  selectedTopics: string[]
  selectedChannels: string[]
  videos: VideoSummary[]
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  contextVideos?: VideoSummary[]
}

export function ContextualChat({ selectedTopics, selectedChannels, videos }: ContextualChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [sessionId, setSessionId] = useState<string | undefined>(undefined)

  const getContextDescription = () => {
    const parts = []
    
    if (selectedTopics.length > 0) {
      parts.push(`topics: ${selectedTopics.map(t => t.replace('_videos', '').replace('_', ' ')).join(', ')}`)
    }
    
    if (selectedChannels.length > 0) {
      parts.push(`channels: ${selectedChannels.join(', ')}`)
    }
    
    if (parts.length === 0) {
      return 'all available videos'
    }
    
    return parts.join(' and ')
  }
  const getTokenEstimate = () => {
    if (!input.trim()) return 0
    
    // Prepare context for optimization
    const context = videos.map(video => {
      // Extract summary from llm_response if it's an object with summary, or use summary_text
      let summary = 'No summary available'
      if (video.llm_response && typeof video.llm_response === 'object' && 'summary' in video.llm_response) {
        summary = String(video.llm_response.summary)
      } else if (video.summary_text) {
        summary = video.summary_text
      }
      
      return {
        title: video.title,
        channel: video.channel_name || '',
        summary: summary,
        transcript: video.transcript || undefined
      }
    })
    
    const { totalEstimatedTokens } = optimizeContextForChat(context, input)
    return totalEstimatedTokens
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    try {      // Prepare context from ALL current videos with full transcripts
      const context = videos.map(video => {
        // Extract summary from llm_response if it's an object with summary, or use summary_text
        let summary = 'No summary available'
        if (video.llm_response && typeof video.llm_response === 'object' && 'summary' in video.llm_response) {
          summary = String(video.llm_response.summary)
        } else if (video.summary_text) {
          summary = video.summary_text
        }
        
        return {
          title: video.title,
          channel: video.channel_name,
          summary: summary,
          transcript: video.transcript, // Include full transcript instead of just summary
          published: video.published_at
        }
      })

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: input.trim(),
          session_id: sessionId, // Include session_id for conversation memory
          context: {
            videos: context,
            topics: selectedTopics,
            channels: selectedChannels
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()
      
      // Store session_id from response for conversation continuity
      if (data.session_id && !sessionId) {
        setSessionId(data.session_id)
      }
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response || 'Sorry, I could not generate a response.',
        timestamp: new Date(),
        contextVideos: data.contextVideos || []
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error in chat:', error)
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-sm h-[1600px] flex flex-col">
      <div className="border-b border-gray-700 p-4">
        <div className="flex items-center gap-2 mb-2">
          <MessageCircle className="w-5 h-5 text-blue-400" />
          <h3 className="font-semibold text-gray-100">AI Chat Assistant</h3>
        </div>
        <p className="text-sm text-gray-300">
          Ask questions about {getContextDescription()} ({videos.length} videos available)
        </p>
      </div>      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-800 min-h-[600px] max-h-[1200px]">
        {messages.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            <Bot className="w-12 h-12 mx-auto mb-3 text-gray-500" />
            <p className="text-sm">Start a conversation about the videos you&apos;ve selected!</p>
            <p className="text-xs mt-1">Try asking: &ldquo;What are the main topics covered?&rdquo;</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex gap-2 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.role === 'user' ? 'bg-blue-600' : 'bg-gray-600'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-4 h-4 text-white" />
                  ) : (
                    <Bot className="w-4 h-4 text-white" />
                  )}
                </div>                <div className={`rounded-lg px-3 py-2 ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-100'
                }`}>
                  {message.role === 'assistant' ? (
                    <div className="text-sm prose prose-invert prose-sm max-w-none">
                      <ReactMarkdown 
                        remarkPlugins={[remarkGfm]}
                        components={{
                          // Style headers
                          h1: ({ children }) => <h1 className="text-lg font-bold mb-2 text-gray-100">{children}</h1>,
                          h2: ({ children }) => <h2 className="text-base font-semibold mb-2 text-gray-100">{children}</h2>,
                          h3: ({ children }) => <h3 className="text-sm font-medium mb-1 text-gray-100">{children}</h3>,
                          // Style lists
                          ul: ({ children }) => <ul className="list-disc list-inside space-y-1 mb-2">{children}</ul>,
                          ol: ({ children }) => <ol className="list-decimal list-inside space-y-1 mb-2">{children}</ol>,
                          li: ({ children }) => <li className="text-gray-100">{children}</li>,
                          // Style links
                          a: ({ children, href }) => <a href={href} className="text-blue-400 hover:text-blue-300 underline" target="_blank" rel="noopener noreferrer">{children}</a>,
                          // Style code
                          code: ({ children }) => <code className="bg-gray-600 px-1 py-0.5 rounded text-xs text-gray-200">{children}</code>,
                          pre: ({ children }) => <pre className="bg-gray-600 p-2 rounded mt-2 mb-2 overflow-x-auto text-xs text-gray-200">{children}</pre>,
                          // Style paragraphs
                          p: ({ children }) => <p className="mb-2 text-gray-100 leading-relaxed">{children}</p>,
                          // Style blockquotes
                          blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-500 pl-3 ml-2 my-2 text-gray-300 italic">{children}</blockquote>,
                          // Style strong/bold
                          strong: ({ children }) => <strong className="font-semibold text-gray-100">{children}</strong>,
                          // Style emphasis/italic
                          em: ({ children }) => <em className="italic text-gray-200">{children}</em>,
                        }}
                      >
                        {message.content}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  )}
                  <p className={`text-xs mt-1 ${
                    message.role === 'user' ? 'text-blue-100' : 'text-gray-400'
                  }`}>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
          {isLoading && (
          <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="bg-gray-700 rounded-lg px-3 py-2">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              </div>
            </div>
          </div>        )}
      </div>

      {/* Template Prompts */}
      <div className="mt-4 p-4 bg-gray-800 border-t border-gray-600">
        <h3 className="text-lg font-semibold text-gray-100 mb-3">Template Prompts</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">          <button
            onClick={() => {
              setInput("Summarize in bullet points the most important points made by these videos")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            📝 Summarize in bullet points the most important points made by these videos
          </button>
          <button
            onClick={() => {
              setInput("What are the main themes and topics covered across these videos?")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            🎯 What are the main themes and topics covered across these videos?
          </button>
          <button
            onClick={() => {
              setInput("Compare and contrast the different perspectives presented in these videos")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            ⚖️ Compare and contrast the different perspectives presented in these videos
          </button>
          <button
            onClick={() => {
              setInput("What actionable insights or recommendations can be drawn from these videos?")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            💡 What actionable insights or recommendations can be drawn from these videos?
          </button>
          <button
            onClick={() => {
              setInput("Identify any contradictions or disagreements between the videos")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            🔍 Identify any contradictions or disagreements between the videos
          </button>
          <button
            onClick={() => {
              setInput("What questions do these videos raise that weren't fully answered?")
              setTimeout(() => {
                const form = document.querySelector('form')
                if (form) form.requestSubmit()
              }, 100)
            }}
            className="p-3 text-left bg-gray-700 hover:bg-gray-600 text-gray-200 rounded-lg transition-colors duration-200"
          >
            ❓ What questions do these videos raise that weren&apos;t fully answered?
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="border-t border-gray-700 p-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={`Ask about ${getContextDescription()}...`}
            className="flex-1 px-3 py-2 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-800 text-gray-100 placeholder-gray-400"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Send message"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </form>
      <div className="p-4 text-sm text-gray-400">        <p>
          {`Token estimate: ${getTokenEstimate()} / ${MAX_CONTEXT_TOKENS}`}
        </p>
        {getTokenEstimate() > MAX_CONTEXT_TOKENS && (
          <p className="flex items-center gap-1 text-red-400">
            <AlertTriangle className="w-4 h-4" />
            {`Your message is too long. Please shorten it to under ${MAX_CONTEXT_TOKENS} tokens.`}
          </p>
        )}
      </div>
    </div>
  )
}
