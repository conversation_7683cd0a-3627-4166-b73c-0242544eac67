WEBVTT
Kind: captions
Language: en

00:00:00.399 --> 00:00:02.230 align:start position:0%
 
one<00:00:00.520><c> of</c><00:00:00.640><c> the</c><00:00:00.799><c> newest</c><00:00:01.160><c> exciting</c><00:00:01.599><c> features</c><00:00:02.120><c> of</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
one of the newest exciting features of
 

00:00:02.240 --> 00:00:04.990 align:start position:0%
one of the newest exciting features of
the<00:00:02.360><c> cloud</c><00:00:02.639><c> 3</c><00:00:02.840><c> Model</c><00:00:03.159><c> family</c><00:00:03.879><c> is</c><00:00:04.040><c> tool</c><00:00:04.319><c> use</c>

00:00:04.990 --> 00:00:05.000 align:start position:0%
the cloud 3 Model family is tool use
 

00:00:05.000 --> 00:00:07.630 align:start position:0%
the cloud 3 Model family is tool use
also<00:00:05.240><c> known</c><00:00:05.440><c> as</c><00:00:05.600><c> function</c><00:00:06.319><c> calling</c><00:00:07.319><c> tools</c>

00:00:07.630 --> 00:00:07.640 align:start position:0%
also known as function calling tools
 

00:00:07.640 --> 00:00:09.549 align:start position:0%
also known as function calling tools
that<00:00:07.799><c> cloud</c><00:00:08.120><c> can</c><00:00:08.280><c> use</c><00:00:08.559><c> are</c><00:00:08.800><c> represented</c><00:00:09.320><c> by</c><00:00:09.440><c> a</c>

00:00:09.549 --> 00:00:09.559 align:start position:0%
that cloud can use are represented by a
 

00:00:09.559 --> 00:00:11.870 align:start position:0%
that cloud can use are represented by a
Json<00:00:10.080><c> schema</c><00:00:10.880><c> that</c><00:00:11.040><c> tells</c><00:00:11.280><c> the</c><00:00:11.440><c> model</c><00:00:11.719><c> about</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
Json schema that tells the model about
 

00:00:11.880 --> 00:00:13.509 align:start position:0%
Json schema that tells the model about
the<00:00:12.000><c> capabilities</c><00:00:12.519><c> of</c><00:00:12.679><c> the</c><00:00:12.759><c> tool</c><00:00:13.280><c> and</c><00:00:13.400><c> the</c>

00:00:13.509 --> 00:00:13.519 align:start position:0%
the capabilities of the tool and the
 

00:00:13.519 --> 00:00:16.510 align:start position:0%
the capabilities of the tool and the
arguments<00:00:13.960><c> it</c><00:00:14.480><c> accepts</c><00:00:15.480><c> during</c><00:00:15.759><c> generation</c>

00:00:16.510 --> 00:00:16.520 align:start position:0%
arguments it accepts during generation
 

00:00:16.520 --> 00:00:18.189 align:start position:0%
arguments it accepts during generation
the<00:00:16.640><c> model</c><00:00:16.920><c> can</c><00:00:17.039><c> make</c><00:00:17.199><c> a</c><00:00:17.320><c> call</c><00:00:17.560><c> to</c><00:00:17.760><c> any</c><00:00:17.920><c> of</c><00:00:18.039><c> its</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
the model can make a call to any of its
 

00:00:18.199 --> 00:00:20.429 align:start position:0%
the model can make a call to any of its
tools<00:00:18.880><c> which</c><00:00:19.039><c> the</c><00:00:19.160><c> client</c><00:00:19.480><c> can</c><00:00:19.640><c> then</c><00:00:19.840><c> dispatch</c>

00:00:20.429 --> 00:00:20.439 align:start position:0%
tools which the client can then dispatch
 

00:00:20.439 --> 00:00:21.590 align:start position:0%
tools which the client can then dispatch
and<00:00:20.720><c> return</c><00:00:20.880><c> the</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
and return the
 

00:00:21.600 --> 00:00:24.630 align:start position:0%
and return the
results<00:00:22.600><c> for</c><00:00:22.800><c> example</c><00:00:23.359><c> this</c><00:00:23.560><c> Hau</c><00:00:24.039><c> model</c><00:00:24.519><c> which</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
results for example this Hau model which
 

00:00:24.640 --> 00:00:27.109 align:start position:0%
results for example this Hau model which
is<00:00:24.760><c> our</c><00:00:25.000><c> fastest</c><00:00:25.400><c> and</c><00:00:25.599><c> most</c><00:00:25.800><c> affordable</c><00:00:26.279><c> model</c>

00:00:27.109 --> 00:00:27.119 align:start position:0%
is our fastest and most affordable model
 

00:00:27.119 --> 00:00:29.710 align:start position:0%
is our fastest and most affordable model
has<00:00:27.359><c> access</c><00:00:27.599><c> to</c><00:00:27.720><c> a</c><00:00:27.840><c> fetch</c><00:00:28.119><c> webpage</c><00:00:28.560><c> tool</c><00:00:29.359><c> and</c><00:00:29.599><c> a</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
has access to a fetch webpage tool and a
 

00:00:29.720 --> 00:00:31.870 align:start position:0%
has access to a fetch webpage tool and a
stand<00:00:30.119><c> boxed</c><00:00:30.400><c> python</c><00:00:30.679><c> reppel</c><00:00:31.039><c> tool</c><00:00:31.480><c> so</c><00:00:31.640><c> it</c><00:00:31.720><c> can</c>

00:00:31.870 --> 00:00:31.880 align:start position:0%
stand boxed python reppel tool so it can
 

00:00:31.880 --> 00:00:33.549 align:start position:0%
stand boxed python reppel tool so it can
retrieve<00:00:32.279><c> information</c><00:00:32.719><c> from</c><00:00:32.840><c> the</c><00:00:32.960><c> internet</c>

00:00:33.549 --> 00:00:33.559 align:start position:0%
retrieve information from the internet
 

00:00:33.559 --> 00:00:35.670 align:start position:0%
retrieve information from the internet
and<00:00:33.760><c> run</c><00:00:34.079><c> code</c><00:00:34.760><c> we're</c><00:00:34.960><c> going</c><00:00:35.040><c> to</c><00:00:35.200><c> use</c><00:00:35.360><c> it</c><00:00:35.559><c> to</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
and run code we're going to use it to
 

00:00:35.680 --> 00:00:38.069 align:start position:0%
and run code we're going to use it to
retrieve<00:00:36.000><c> an</c><00:00:36.160><c> implementation</c><00:00:36.840><c> of</c><00:00:37.000><c> quick</c><00:00:37.280><c> sort</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
retrieve an implementation of quick sort
 

00:00:38.079 --> 00:00:39.590 align:start position:0%
retrieve an implementation of quick sort
one<00:00:38.200><c> of</c><00:00:38.320><c> the</c><00:00:38.440><c> most</c><00:00:38.719><c> popular</c><00:00:39.120><c> sorting</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
one of the most popular sorting
 

00:00:39.600 --> 00:00:42.029 align:start position:0%
one of the most popular sorting
algorithms<00:00:40.600><c> and</c><00:00:40.760><c> check</c><00:00:41.039><c> how</c><00:00:41.239><c> fast</c><00:00:41.520><c> it</c><00:00:41.640><c> runs</c><00:00:41.920><c> on</c>

00:00:42.029 --> 00:00:42.039 align:start position:0%
algorithms and check how fast it runs on
 

00:00:42.039 --> 00:00:43.310 align:start position:0%
algorithms and check how fast it runs on
a<00:00:42.160><c> sample</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
a sample
 

00:00:43.320 --> 00:00:46.470 align:start position:0%
a sample
input<00:00:44.320><c> now</c><00:00:44.680><c> because</c><00:00:44.960><c> Haiku</c><00:00:45.320><c> is</c><00:00:45.440><c> pretty</c><00:00:45.719><c> fast</c>

00:00:46.470 --> 00:00:46.480 align:start position:0%
input now because Haiku is pretty fast
 

00:00:46.480 --> 00:00:47.990 align:start position:0%
input now because Haiku is pretty fast
I've<00:00:46.680><c> actually</c><00:00:46.879><c> slowed</c><00:00:47.199><c> down</c><00:00:47.360><c> this</c><00:00:47.480><c> demo</c><00:00:47.800><c> by</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
I've actually slowed down this demo by
 

00:00:48.000 --> 00:00:50.750 align:start position:0%
I've actually slowed down this demo by
5x<00:00:48.719><c> so</c><00:00:48.840><c> that</c><00:00:48.960><c> we</c><00:00:49.079><c> can</c><00:00:49.160><c> see</c><00:00:49.320><c> the</c><00:00:49.440><c> tokens</c><00:00:49.840><c> being</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
5x so that we can see the tokens being
 

00:00:50.760 --> 00:00:53.470 align:start position:0%
5x so that we can see the tokens being
generated<00:00:51.760><c> you</c><00:00:51.879><c> can</c><00:00:52.039><c> see</c><00:00:52.320><c> that</c><00:00:52.559><c> Haiku</c><00:00:53.160><c> is</c><00:00:53.280><c> able</c>

00:00:53.470 --> 00:00:53.480 align:start position:0%
generated you can see that Haiku is able
 

00:00:53.480 --> 00:00:54.990 align:start position:0%
generated you can see that Haiku is able
to<00:00:53.600><c> link</c><00:00:53.920><c> together</c><00:00:54.160><c> several</c><00:00:54.480><c> different</c><00:00:54.719><c> tools</c>

00:00:54.990 --> 00:00:55.000 align:start position:0%
to link together several different tools
 

00:00:55.000 --> 00:00:57.430 align:start position:0%
to link together several different tools
to<00:00:55.160><c> accomplish</c><00:00:55.520><c> a</c>

00:00:57.430 --> 00:00:57.440 align:start position:0%
to accomplish a
 

00:00:57.440 --> 00:00:59.990 align:start position:0%
to accomplish a
task<00:00:58.440><c> now</c><00:00:58.719><c> things</c><00:00:59.000><c> get</c><00:00:59.199><c> even</c><00:00:59.440><c> more</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
task now things get even more
 

00:01:00.000 --> 00:01:01.869 align:start position:0%
task now things get even more
interesting<00:01:00.559><c> when</c><00:01:00.760><c> models</c><00:01:01.079><c> can</c><00:01:01.280><c> call</c><00:01:01.640><c> other</c>

00:01:01.869 --> 00:01:01.879 align:start position:0%
interesting when models can call other
 

00:01:01.879 --> 00:01:03.189 align:start position:0%
interesting when models can call other
models<00:01:02.320><c> as</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
models as
 

00:01:03.199 --> 00:01:05.630 align:start position:0%
models as
tools<00:01:04.199><c> for</c><00:01:04.400><c> example</c><00:01:04.960><c> let's</c><00:01:05.159><c> say</c><00:01:05.320><c> I</c><00:01:05.400><c> want</c><00:01:05.519><c> to</c>

00:01:05.630 --> 00:01:05.640 align:start position:0%
tools for example let's say I want to
 

00:01:05.640 --> 00:01:07.149 align:start position:0%
tools for example let's say I want to
find<00:01:05.880><c> the</c><00:01:06.040><c> fastest</c><00:01:06.439><c> implementation</c><00:01:07.040><c> of</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
find the fastest implementation of
 

00:01:07.159 --> 00:01:08.630 align:start position:0%
find the fastest implementation of
quicksort

00:01:08.630 --> 00:01:08.640 align:start position:0%
quicksort
 

00:01:08.640 --> 00:01:11.510 align:start position:0%
quicksort
online<00:01:09.640><c> here</c><00:01:09.920><c> I'm</c><00:01:10.119><c> asking</c><00:01:10.479><c> Opus</c><00:01:11.080><c> our</c><00:01:11.280><c> most</c>

00:01:11.510 --> 00:01:11.520 align:start position:0%
online here I'm asking Opus our most
 

00:01:11.520 --> 00:01:14.190 align:start position:0%
online here I'm asking Opus our most
advanced<00:01:11.960><c> model</c><00:01:12.640><c> to</c><00:01:12.840><c> find</c><00:01:13.320><c> 100</c><00:01:13.680><c> permissively</c>

00:01:14.190 --> 00:01:14.200 align:start position:0%
advanced model to find 100 permissively
 

00:01:14.200 --> 00:01:16.630 align:start position:0%
advanced model to find 100 permissively
licensed<00:01:14.640><c> quick</c><00:01:14.840><c> sort</c><00:01:15.080><c> implementations</c><00:01:15.759><c> on</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
licensed quick sort implementations on
 

00:01:16.640 --> 00:01:20.230 align:start position:0%
licensed quick sort implementations on
GitHub<00:01:17.640><c> then</c><00:01:18.320><c> 100</c><00:01:18.600><c> houp</c><00:01:19.000><c> models</c><00:01:19.360><c> write</c><00:01:19.640><c> tests</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
GitHub then 100 houp models write tests
 

00:01:20.240 --> 00:01:21.749 align:start position:0%
GitHub then 100 houp models write tests
to<00:01:20.400><c> determine</c><00:01:21.000><c> how</c><00:01:21.240><c> fast</c><00:01:21.560><c> each</c>

00:01:21.749 --> 00:01:21.759 align:start position:0%
to determine how fast each
 

00:01:21.759 --> 00:01:24.270 align:start position:0%
to determine how fast each
implementation<00:01:22.520><c> is</c><00:01:23.479><c> and</c><00:01:23.640><c> then</c><00:01:23.759><c> we'll</c><00:01:23.960><c> be</c><00:01:24.119><c> able</c>

00:01:24.270 --> 00:01:24.280 align:start position:0%
implementation is and then we'll be able
 

00:01:24.280 --> 00:01:26.390 align:start position:0%
implementation is and then we'll be able
to<00:01:24.479><c> determine</c><00:01:25.159><c> which</c><00:01:25.320><c> is</c><00:01:25.479><c> the</c><00:01:25.600><c> quickest</c><00:01:25.920><c> quick</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
to determine which is the quickest quick
 

00:01:26.400 --> 00:01:29.190 align:start position:0%
to determine which is the quickest quick
sort<00:01:27.400><c> while</c><00:01:27.600><c> we</c><00:01:27.759><c> let</c><00:01:27.960><c> this</c><00:01:28.119><c> run</c><00:01:28.680><c> here's</c><00:01:28.960><c> how</c><00:01:29.079><c> it</c>

00:01:29.190 --> 00:01:29.200 align:start position:0%
sort while we let this run here's how it
 

00:01:29.200 --> 00:01:30.789 align:start position:0%
sort while we let this run here's how it
works<00:01:29.400><c> under</c><00:01:29.600><c> the</c><00:01:29.720><c> hood</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
works under the hood
 

00:01:30.799 --> 00:01:33.109 align:start position:0%
works under the hood
we've<00:01:31.000><c> given</c><00:01:31.360><c> Opus</c><00:01:31.799><c> A</c><00:01:31.960><c> dispatch</c><00:01:32.479><c> sub</c><00:01:32.720><c> agents</c>

00:01:33.109 --> 00:01:33.119 align:start position:0%
we've given Opus A dispatch sub agents
 

00:01:33.119 --> 00:01:35.910 align:start position:0%
we've given Opus A dispatch sub agents
tool<00:01:33.680><c> to</c><00:01:33.840><c> parallelize</c><00:01:34.520><c> this</c><00:01:34.680><c> work</c><00:01:35.680><c> where</c><00:01:35.840><c> it</c>

00:01:35.910 --> 00:01:35.920 align:start position:0%
tool to parallelize this work where it
 

00:01:35.920 --> 00:01:37.630 align:start position:0%
tool to parallelize this work where it
can<00:01:36.079><c> write</c><00:01:36.240><c> a</c><00:01:36.360><c> prompt</c><00:01:36.720><c> template</c><00:01:37.280><c> and</c><00:01:37.399><c> provide</c>

00:01:37.630 --> 00:01:37.640 align:start position:0%
can write a prompt template and provide
 

00:01:37.640 --> 00:01:38.670 align:start position:0%
can write a prompt template and provide
a<00:01:37.720><c> list</c><00:01:37.960><c> of</c>

00:01:38.670 --> 00:01:38.680 align:start position:0%
a list of
 

00:01:38.680 --> 00:01:41.429 align:start position:0%
a list of
arguments<00:01:39.680><c> the</c><00:01:39.840><c> Haiku</c><00:01:40.240><c> sub</c><00:01:40.479><c> agents</c><00:01:41.000><c> each</c><00:01:41.280><c> get</c>

00:01:41.429 --> 00:01:41.439 align:start position:0%
arguments the Haiku sub agents each get
 

00:01:41.439 --> 00:01:42.950 align:start position:0%
arguments the Haiku sub agents each get
the<00:01:41.560><c> template</c><00:01:42.079><c> filled</c><00:01:42.439><c> in</c><00:01:42.640><c> with</c><00:01:42.759><c> their</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
the template filled in with their
 

00:01:42.960 --> 00:01:45.350 align:start position:0%
the template filled in with their
respective<00:01:43.799><c> argument</c><00:01:44.799><c> then</c><00:01:44.960><c> all</c><00:01:45.119><c> of</c><00:01:45.240><c> the</c>

00:01:45.350 --> 00:01:45.360 align:start position:0%
respective argument then all of the
 

00:01:45.360 --> 00:01:47.590 align:start position:0%
respective argument then all of the
answers<00:01:45.680><c> get</c><00:01:45.960><c> return</c><00:01:46.159><c> to</c><00:01:46.280><c> Opus</c><00:01:47.079><c> which</c><00:01:47.360><c> Returns</c>

00:01:47.590 --> 00:01:47.600 align:start position:0%
answers get return to Opus which Returns
 

00:01:47.600 --> 00:01:52.190 align:start position:0%
answers get return to Opus which Returns
the<00:01:47.759><c> fastest</c>

00:01:52.190 --> 00:01:52.200 align:start position:0%
 
 

00:01:52.200 --> 00:01:54.149 align:start position:0%
 
implementation<00:01:53.200><c> and</c><00:01:53.360><c> here</c><00:01:53.479><c> we</c><00:01:53.600><c> see</c><00:01:53.880><c> that</c><00:01:54.000><c> the</c>

00:01:54.149 --> 00:01:54.159 align:start position:0%
implementation and here we see that the
 

00:01:54.159 --> 00:01:57.149 align:start position:0%
implementation and here we see that the
fastest<00:01:54.560><c> result</c><00:01:54.840><c> is</c><00:01:55.000><c> available</c><00:01:55.840><c> here</c><00:01:56.840><c> and</c><00:01:57.000><c> it</c>

00:01:57.149 --> 00:01:57.159 align:start position:0%
fastest result is available here and it
 

00:01:57.159 --> 00:01:58.870 align:start position:0%
fastest result is available here and it
has<00:01:57.320><c> some</c><00:01:57.520><c> additional</c><00:01:57.920><c> optimizations</c><00:01:58.759><c> that</c>

00:01:58.870 --> 00:01:58.880 align:start position:0%
has some additional optimizations that
 

00:01:58.880 --> 00:02:01.830 align:start position:0%
has some additional optimizations that
some<00:01:59.000><c> of</c><00:01:59.119><c> the</c><00:01:59.200><c> other</c><00:01:59.360><c> implement</c><00:02:00.200><c> don't</c><00:02:00.840><c> have</c>

00:02:01.830 --> 00:02:01.840 align:start position:0%
some of the other implement don't have
 

00:02:01.840 --> 00:02:03.910 align:start position:0%
some of the other implement don't have
tool<00:02:02.119><c> use</c><00:02:02.360><c> with</c><00:02:02.520><c> sub</c><00:02:02.719><c> agents</c><00:02:03.119><c> is</c><00:02:03.240><c> a</c><00:02:03.439><c> great</c><00:02:03.719><c> way</c>

00:02:03.910 --> 00:02:03.920 align:start position:0%
tool use with sub agents is a great way
 

00:02:03.920 --> 00:02:05.870 align:start position:0%
tool use with sub agents is a great way
to<00:02:04.079><c> combine</c><00:02:04.399><c> the</c><00:02:04.560><c> intelligence</c><00:02:05.039><c> of</c><00:02:05.200><c> Opus</c><00:02:05.759><c> and</c>

00:02:05.870 --> 00:02:05.880 align:start position:0%
to combine the intelligence of Opus and
 

00:02:05.880 --> 00:02:08.109 align:start position:0%
to combine the intelligence of Opus and
the<00:02:06.000><c> speed</c><00:02:06.200><c> and</c><00:02:06.399><c> affordability</c><00:02:06.960><c> of</c><00:02:07.119><c> haiku</c><00:02:07.960><c> to</c>

00:02:08.109 --> 00:02:08.119 align:start position:0%
the speed and affordability of haiku to
 

00:02:08.119 --> 00:02:09.630 align:start position:0%
the speed and affordability of haiku to
take<00:02:08.399><c> action</c><00:02:08.679><c> on</c><00:02:08.920><c> large</c><00:02:09.200><c> amounts</c><00:02:09.440><c> of</c>

00:02:09.630 --> 00:02:09.640 align:start position:0%
take action on large amounts of
 

00:02:09.640 --> 00:02:12.350 align:start position:0%
take action on large amounts of
information<00:02:10.119><c> at</c><00:02:10.640><c> scale</c><00:02:11.640><c> hope</c><00:02:11.800><c> you</c><00:02:11.920><c> try</c><00:02:12.080><c> it</c><00:02:12.200><c> out</c>

00:02:12.350 --> 00:02:12.360 align:start position:0%
information at scale hope you try it out
 

00:02:12.360 --> 00:02:14.800 align:start position:0%
information at scale hope you try it out
soon

