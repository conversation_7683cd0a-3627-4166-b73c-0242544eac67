WEBVTT
Kind: captions
Language: en

00:00:00.730 --> 00:00:05.829 align:start position:0%
 
[Music]

00:00:05.829 --> 00:00:05.839 align:start position:0%
 
 

00:00:05.839 --> 00:00:08.950 align:start position:0%
 
welcome<00:00:06.200><c> to</c><00:00:06.359><c> module</c><00:00:06.720><c> 4</c><00:00:07.200><c> of</c><00:00:07.359><c> our</c><00:00:07.760><c> course</c><00:00:08.760><c> in</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
welcome to module 4 of our course in
 

00:00:08.960 --> 00:00:11.509 align:start position:0%
welcome to module 4 of our course in
this<00:00:09.080><c> module</c><00:00:09.719><c> we'll</c><00:00:10.040><c> discuss</c><00:00:10.519><c> enhancing</c><00:00:11.240><c> and</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
this module we'll discuss enhancing and
 

00:00:11.519 --> 00:00:14.110 align:start position:0%
this module we'll discuss enhancing and
optimizing<00:00:12.400><c> llm</c>

00:00:14.110 --> 00:00:14.120 align:start position:0%
optimizing llm
 

00:00:14.120 --> 00:00:16.590 align:start position:0%
optimizing llm
applications<00:00:15.120><c> before</c><00:00:15.400><c> we</c><00:00:15.559><c> start</c><00:00:16.119><c> improving</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
applications before we start improving
 

00:00:16.600 --> 00:00:19.429 align:start position:0%
applications before we start improving
our<00:00:16.880><c> application</c><00:00:17.840><c> it's</c><00:00:18.080><c> essential</c><00:00:18.520><c> to</c><00:00:18.720><c> decide</c>

00:00:19.429 --> 00:00:19.439 align:start position:0%
our application it's essential to decide
 

00:00:19.439 --> 00:00:22.109 align:start position:0%
our application it's essential to decide
how<00:00:19.600><c> to</c><00:00:19.960><c> evaluate</c><00:00:20.920><c> the</c><00:00:21.160><c> effectiveness</c><00:00:21.920><c> of</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
how to evaluate the effectiveness of
 

00:00:22.119 --> 00:00:24.990 align:start position:0%
how to evaluate the effectiveness of
these<00:00:22.800><c> changes</c><00:00:23.800><c> evaluating</c><00:00:24.439><c> llm</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
these changes evaluating llm
 

00:00:25.000 --> 00:00:27.509 align:start position:0%
these changes evaluating llm
applications<00:00:25.640><c> is</c><00:00:25.760><c> a</c><00:00:25.920><c> challenging</c><00:00:26.480><c> task</c><00:00:27.279><c> due</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
applications is a challenging task due
 

00:00:27.519 --> 00:00:30.950 align:start position:0%
applications is a challenging task due
to<00:00:28.000><c> stochastic</c><00:00:28.880><c> and</c><00:00:29.160><c> unstructured</c><00:00:30.080><c> nature</c><00:00:30.800><c> of</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
to stochastic and unstructured nature of
 

00:00:30.960 --> 00:00:33.229 align:start position:0%
to stochastic and unstructured nature of
their

00:00:33.229 --> 00:00:33.239 align:start position:0%
 
 

00:00:33.239 --> 00:00:36.110 align:start position:0%
 
outputs<00:00:34.239><c> Resa</c><00:00:34.600><c> Shabani</c><00:00:35.040><c> from</c><00:00:35.280><c> replit</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
outputs Resa Shabani from replit
 

00:00:36.120 --> 00:00:38.270 align:start position:0%
outputs Resa Shabani from replit
mentioned<00:00:36.600><c> in</c><00:00:36.719><c> a</c><00:00:36.920><c> podcast</c><00:00:37.320><c> interview</c><00:00:38.079><c> that</c>

00:00:38.270 --> 00:00:38.280 align:start position:0%
mentioned in a podcast interview that
 

00:00:38.280 --> 00:00:40.910 align:start position:0%
mentioned in a podcast interview that
when<00:00:38.440><c> training</c><00:00:38.920><c> that</c><00:00:39.200><c> code</c><00:00:39.920><c> language</c><00:00:40.360><c> model</c>

00:00:40.910 --> 00:00:40.920 align:start position:0%
when training that code language model
 

00:00:40.920 --> 00:00:43.229 align:start position:0%
when training that code language model
they<00:00:41.039><c> did</c><00:00:41.200><c> a</c><00:00:41.360><c> Vibes</c><00:00:41.719><c> check</c><00:00:42.200><c> by</c><00:00:42.440><c> manually</c>

00:00:43.229 --> 00:00:43.239 align:start position:0%
they did a Vibes check by manually
 

00:00:43.239 --> 00:00:47.069 align:start position:0%
they did a Vibes check by manually
prompting<00:00:44.239><c> and</c><00:00:44.559><c> assessing</c><00:00:45.239><c> its</c>

00:00:47.069 --> 00:00:47.079 align:start position:0%
prompting and assessing its
 

00:00:47.079 --> 00:00:50.470 align:start position:0%
prompting and assessing its
outputs<00:00:48.079><c> Vibes</c><00:00:48.440><c> check</c><00:00:48.760><c> might</c><00:00:48.960><c> be</c><00:00:49.120><c> a</c><00:00:49.719><c> quick</c><00:00:50.079><c> and</c>

00:00:50.470 --> 00:00:50.480 align:start position:0%
outputs Vibes check might be a quick and
 

00:00:50.480 --> 00:00:52.670 align:start position:0%
outputs Vibes check might be a quick and
subjective<00:00:51.120><c> way</c><00:00:51.480><c> of</c><00:00:51.680><c> assessing</c><00:00:52.079><c> model</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
subjective way of assessing model
 

00:00:52.680 --> 00:00:55.069 align:start position:0%
subjective way of assessing model
improvements<00:00:53.680><c> but</c><00:00:54.000><c> it's</c><00:00:54.160><c> not</c><00:00:54.359><c> ideal</c><00:00:54.840><c> if</c><00:00:54.960><c> you</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
improvements but it's not ideal if you
 

00:00:55.079 --> 00:00:58.389 align:start position:0%
improvements but it's not ideal if you
want<00:00:55.280><c> to</c><00:00:55.840><c> objectively</c><00:00:56.840><c> compare</c><00:00:57.359><c> tens</c><00:00:58.039><c> or</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
want to objectively compare tens or
 

00:00:58.399 --> 00:01:02.389 align:start position:0%
want to objectively compare tens or
hundreds<00:00:59.079><c> of</c><00:00:59.320><c> experiments</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
 
 

00:01:02.399 --> 00:01:05.030 align:start position:0%
 
to<00:01:02.640><c> automate</c><00:01:03.280><c> the</c><00:01:03.440><c> evaluation</c><00:01:04.080><c> process</c><00:01:04.920><c> we</c>

00:01:05.030 --> 00:01:05.040 align:start position:0%
to automate the evaluation process we
 

00:01:05.040 --> 00:01:08.469 align:start position:0%
to automate the evaluation process we
can<00:01:05.239><c> use</c><00:01:05.680><c> another</c><00:01:06.280><c> llm</c><00:01:07.000><c> in</c><00:01:07.119><c> a</c><00:01:07.280><c> modelbased</c>

00:01:08.469 --> 00:01:08.479 align:start position:0%
can use another llm in a modelbased
 

00:01:08.479 --> 00:01:11.390 align:start position:0%
can use another llm in a modelbased
evaluation<00:01:09.479><c> in</c><00:01:09.680><c> this</c><00:01:09.920><c> approach</c><00:01:10.640><c> We</c><00:01:10.840><c> Gather</c><00:01:11.159><c> a</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
evaluation in this approach We Gather a
 

00:01:11.400 --> 00:01:13.830 align:start position:0%
evaluation in this approach We Gather a
data<00:01:11.720><c> set</c><00:01:12.119><c> of</c><00:01:12.400><c> questions</c><00:01:12.920><c> and</c><00:01:13.159><c> corresponding</c>

00:01:13.830 --> 00:01:13.840 align:start position:0%
data set of questions and corresponding
 

00:01:13.840 --> 00:01:15.270 align:start position:0%
data set of questions and corresponding
ideal

00:01:15.270 --> 00:01:15.280 align:start position:0%
ideal
 

00:01:15.280 --> 00:01:17.550 align:start position:0%
ideal
answers<00:01:16.280><c> ideally</c><00:01:16.759><c> these</c><00:01:17.000><c> questions</c><00:01:17.320><c> are</c>

00:01:17.550 --> 00:01:17.560 align:start position:0%
answers ideally these questions are
 

00:01:17.560 --> 00:01:20.429 align:start position:0%
answers ideally these questions are
coming<00:01:18.000><c> from</c><00:01:18.320><c> a</c><00:01:18.520><c> production</c><00:01:19.119><c> service</c><00:01:20.119><c> and</c>

00:01:20.429 --> 00:01:20.439 align:start position:0%
coming from a production service and
 

00:01:20.439 --> 00:01:24.670 align:start position:0%
coming from a production service and
ideal<00:01:20.880><c> answers</c><00:01:21.280><c> are</c><00:01:21.600><c> manually</c><00:01:22.119><c> annotated</c><00:01:22.799><c> by</c>

00:01:24.670 --> 00:01:24.680 align:start position:0%
ideal answers are manually annotated by
 

00:01:24.680 --> 00:01:27.870 align:start position:0%
ideal answers are manually annotated by
experts<00:01:25.680><c> we</c><00:01:25.799><c> can</c><00:01:26.000><c> also</c><00:01:26.280><c> use</c><00:01:26.880><c> a</c><00:01:27.079><c> synthetic</c><00:01:27.560><c> data</c>

00:01:27.870 --> 00:01:27.880 align:start position:0%
experts we can also use a synthetic data
 

00:01:27.880 --> 00:01:29.950 align:start position:0%
experts we can also use a synthetic data
set<00:01:28.439><c> like</c><00:01:28.640><c> the</c><00:01:28.759><c> one</c><00:01:28.920><c> we</c><00:01:29.119><c> created</c><00:01:29.560><c> in</c><00:01:29.680><c> a</c>

00:01:29.950 --> 00:01:29.960 align:start position:0%
set like the one we created in a
 

00:01:29.960 --> 00:01:31.550 align:start position:0%
set like the one we created in a
previous

00:01:31.550 --> 00:01:31.560 align:start position:0%
previous
 

00:01:31.560 --> 00:01:34.590 align:start position:0%
previous
module<00:01:32.560><c> we</c><00:01:32.720><c> fit</c><00:01:33.040><c> this</c><00:01:33.280><c> data</c><00:01:33.600><c> set</c><00:01:34.040><c> to</c><00:01:34.280><c> our</c>

00:01:34.590 --> 00:01:34.600 align:start position:0%
module we fit this data set to our
 

00:01:34.600 --> 00:01:37.550 align:start position:0%
module we fit this data set to our
application<00:01:35.600><c> which</c><00:01:36.040><c> generates</c><00:01:36.759><c> an</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
application which generates an
 

00:01:37.560 --> 00:01:42.190 align:start position:0%
application which generates an
answer<00:01:38.560><c> the</c><00:01:38.920><c> uh</c><00:01:39.119><c> evaluation</c><00:01:39.880><c> llm</c><00:01:40.880><c> then</c><00:01:41.320><c> judges</c>

00:01:42.190 --> 00:01:42.200 align:start position:0%
answer the uh evaluation llm then judges
 

00:01:42.200 --> 00:01:44.670 align:start position:0%
answer the uh evaluation llm then judges
if<00:01:42.360><c> a</c><00:01:42.520><c> generated</c><00:01:43.119><c> answer</c><00:01:43.439><c> is</c><00:01:43.600><c> correct</c><00:01:44.560><c> which</c>

00:01:44.670 --> 00:01:44.680 align:start position:0%
if a generated answer is correct which
 

00:01:44.680 --> 00:01:48.550 align:start position:0%
if a generated answer is correct which
we<00:01:44.840><c> can</c><00:01:45.119><c> summarize</c><00:01:45.880><c> as</c><00:01:46.000><c> an</c><00:01:46.200><c> overall</c><00:01:46.960><c> accuracy</c>

00:01:48.550 --> 00:01:48.560 align:start position:0%
we can summarize as an overall accuracy
 

00:01:48.560 --> 00:01:51.389 align:start position:0%
we can summarize as an overall accuracy
metric<00:01:49.560><c> another</c><00:01:49.960><c> promising</c><00:01:50.560><c> method</c><00:01:51.040><c> involves</c>

00:01:51.389 --> 00:01:51.399 align:start position:0%
metric another promising method involves
 

00:01:51.399 --> 00:01:54.590 align:start position:0%
metric another promising method involves
a<00:01:51.560><c> more</c><00:01:51.920><c> granular</c><00:01:53.119><c> evaluation</c><00:01:54.119><c> here</c><00:01:54.320><c> we</c><00:01:54.439><c> need</c>

00:01:54.590 --> 00:01:54.600 align:start position:0%
a more granular evaluation here we need
 

00:01:54.600 --> 00:01:57.749 align:start position:0%
a more granular evaluation here we need
to<00:01:54.880><c> extract</c><00:01:55.520><c> a</c><00:01:55.680><c> set</c><00:01:55.840><c> of</c><00:01:56.119><c> specific</c><00:01:56.640><c> items</c><00:01:57.520><c> to</c>

00:01:57.749 --> 00:01:57.759 align:start position:0%
to extract a set of specific items to
 

00:01:57.759 --> 00:02:00.389 align:start position:0%
to extract a set of specific items to
validate<00:01:58.399><c> for</c><00:01:58.600><c> each</c><00:01:58.840><c> query</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
validate for each query
 

00:02:00.399 --> 00:02:02.350 align:start position:0%
validate for each query
each<00:02:00.680><c> example</c><00:02:01.039><c> may</c><00:02:01.240><c> have</c><00:02:01.399><c> a</c><00:02:01.640><c> different</c><00:02:02.000><c> set</c><00:02:02.200><c> of</c>

00:02:02.350 --> 00:02:02.360 align:start position:0%
each example may have a different set of
 

00:02:02.360 --> 00:02:05.149 align:start position:0%
each example may have a different set of
tests<00:02:03.119><c> for</c><00:02:03.320><c> example</c><00:02:03.680><c> when</c><00:02:04.000><c> evaluating</c><00:02:04.600><c> one</c>

00:02:05.149 --> 00:02:05.159 align:start position:0%
tests for example when evaluating one
 

00:02:05.159 --> 00:02:08.589 align:start position:0%
tests for example when evaluating one
bot<00:02:06.159><c> we</c><00:02:06.280><c> can</c><00:02:06.479><c> check</c><00:02:06.680><c> for</c><00:02:07.000><c> specific</c><00:02:07.439><c> API</c><00:02:07.920><c> calls</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
bot we can check for specific API calls
 

00:02:08.599 --> 00:02:11.430 align:start position:0%
bot we can check for specific API calls
and<00:02:08.759><c> see</c><00:02:09.000><c> if</c><00:02:09.160><c> they</c><00:02:09.280><c> are</c><00:02:09.679><c> included</c><00:02:10.319><c> in</c><00:02:10.440><c> a</c><00:02:10.560><c> model</c>

00:02:11.430 --> 00:02:11.440 align:start position:0%
and see if they are included in a model
 

00:02:11.440 --> 00:02:13.869 align:start position:0%
and see if they are included in a model
response<00:02:12.440><c> this</c><00:02:12.680><c> approach</c><00:02:13.200><c> requires</c><00:02:13.640><c> more</c>

00:02:13.869 --> 00:02:13.879 align:start position:0%
response this approach requires more
 

00:02:13.879 --> 00:02:17.350 align:start position:0%
response this approach requires more
effort<00:02:14.720><c> but</c><00:02:15.000><c> can</c><00:02:15.280><c> yield</c><00:02:15.599><c> more</c><00:02:16.000><c> robust</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
effort but can yield more robust
 

00:02:17.360 --> 00:02:20.229 align:start position:0%
effort but can yield more robust
results<00:02:18.360><c> finally</c><00:02:18.920><c> collecting</c><00:02:19.400><c> user</c><00:02:19.720><c> feedback</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
results finally collecting user feedback
 

00:02:20.239 --> 00:02:22.470 align:start position:0%
results finally collecting user feedback
and<00:02:20.400><c> tracking</c><00:02:20.800><c> performance</c><00:02:21.319><c> in</c><00:02:21.519><c> production</c>

00:02:22.470 --> 00:02:22.480 align:start position:0%
and tracking performance in production
 

00:02:22.480 --> 00:02:23.670 align:start position:0%
and tracking performance in production
is<00:02:22.760><c> also</c>

00:02:23.670 --> 00:02:23.680 align:start position:0%
is also
 

00:02:23.680 --> 00:02:26.309 align:start position:0%
is also
fundamental<00:02:24.680><c> in</c><00:02:24.840><c> the</c><00:02:25.000><c> case</c><00:02:25.160><c> of</c><00:02:25.319><c> our</c><00:02:25.560><c> bot</c><00:02:26.160><c> we</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
fundamental in the case of our bot we
 

00:02:26.319 --> 00:02:28.630 align:start position:0%
fundamental in the case of our bot we
allow<00:02:26.640><c> users</c><00:02:27.120><c> to</c><00:02:27.360><c> provide</c><00:02:27.680><c> feedback</c><00:02:28.280><c> via</c>

00:02:28.630 --> 00:02:28.640 align:start position:0%
allow users to provide feedback via
 

00:02:28.640 --> 00:02:30.350 align:start position:0%
allow users to provide feedback via
thumbs<00:02:28.959><c> up</c><00:02:29.360><c> thumbs</c><00:02:29.640><c> down</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
thumbs up thumbs down
 

00:02:30.360 --> 00:02:32.869 align:start position:0%
thumbs up thumbs down
reaction<00:02:31.360><c> we</c><00:02:31.480><c> can</c><00:02:31.720><c> then</c><00:02:32.000><c> monitor</c><00:02:32.640><c> the</c>

00:02:32.869 --> 00:02:32.879 align:start position:0%
reaction we can then monitor the
 

00:02:32.879 --> 00:02:34.910 align:start position:0%
reaction we can then monitor the
percentage<00:02:33.400><c> of</c><00:02:33.680><c> positive</c><00:02:34.160><c> or</c><00:02:34.440><c> negative</c>

00:02:34.910 --> 00:02:34.920 align:start position:0%
percentage of positive or negative
 

00:02:34.920 --> 00:02:38.150 align:start position:0%
percentage of positive or negative
reactions<00:02:35.400><c> over</c><00:02:35.920><c> time</c><00:02:36.920><c> in</c><00:02:37.120><c> this</c><00:02:37.319><c> case</c><00:02:37.920><c> it's</c>

00:02:38.150 --> 00:02:38.160 align:start position:0%
reactions over time in this case it's
 

00:02:38.160 --> 00:02:40.390 align:start position:0%
reactions over time in this case it's
critical<00:02:38.519><c> to</c><00:02:38.680><c> be</c><00:02:38.800><c> able</c><00:02:38.959><c> to</c><00:02:39.120><c> track</c><00:02:39.480><c> performance</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
critical to be able to track performance
 

00:02:40.400 --> 00:02:43.229 align:start position:0%
critical to be able to track performance
to<00:02:40.560><c> a</c><00:02:40.760><c> specific</c><00:02:41.280><c> version</c><00:02:41.640><c> of</c><00:02:41.800><c> our</c><00:02:42.239><c> application</c>

00:02:43.229 --> 00:02:43.239 align:start position:0%
to a specific version of our application
 

00:02:43.239 --> 00:02:45.270 align:start position:0%
to a specific version of our application
and<00:02:43.400><c> in</c><00:02:43.560><c> our</c><00:02:43.879><c> case</c><00:02:44.080><c> we</c><00:02:44.239><c> use</c><00:02:44.440><c> weights</c><00:02:44.640><c> and</c><00:02:44.800><c> bies</c>

00:02:45.270 --> 00:02:45.280 align:start position:0%
and in our case we use weights and bies
 

00:02:45.280 --> 00:02:48.470 align:start position:0%
and in our case we use weights and bies
artifacts<00:02:46.159><c> to</c><00:02:46.360><c> Version</c><00:02:46.800><c> Control</c><00:02:47.319><c> our</c>

00:02:48.470 --> 00:02:48.480 align:start position:0%
artifacts to Version Control our
 

00:02:48.480 --> 00:02:50.589 align:start position:0%
artifacts to Version Control our
application<00:02:49.480><c> when</c><00:02:49.640><c> we</c><00:02:49.840><c> decide</c><00:02:50.159><c> on</c><00:02:50.319><c> a</c>

00:02:50.589 --> 00:02:50.599 align:start position:0%
application when we decide on a
 

00:02:50.599 --> 00:02:53.509 align:start position:0%
application when we decide on a
consistent<00:02:51.599><c> and</c><00:02:51.959><c> ideally</c><00:02:52.879><c> automated</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
consistent and ideally automated
 

00:02:53.519 --> 00:02:55.750 align:start position:0%
consistent and ideally automated
evaluation<00:02:54.159><c> approach</c><00:02:54.840><c> we</c><00:02:55.000><c> can</c><00:02:55.239><c> explore</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
evaluation approach we can explore
 

00:02:55.760 --> 00:02:58.750 align:start position:0%
evaluation approach we can explore
techniques<00:02:56.200><c> for</c><00:02:56.599><c> enhancing</c><00:02:57.599><c> and</c><00:02:57.879><c> optimizing</c>

00:02:58.750 --> 00:02:58.760 align:start position:0%
techniques for enhancing and optimizing
 

00:02:58.760 --> 00:03:01.350 align:start position:0%
techniques for enhancing and optimizing
our<00:02:59.159><c> llm</c><00:02:59.840><c> applications</c><00:03:00.800><c> and</c><00:03:00.959><c> this</c><00:03:01.080><c> will</c><00:03:01.200><c> be</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
our llm applications and this will be
 

00:03:01.360 --> 00:03:12.430 align:start position:0%
our llm applications and this will be
the<00:03:01.519><c> topic</c><00:03:01.959><c> of</c><00:03:02.120><c> our</c><00:03:02.360><c> next</c>

00:03:12.430 --> 00:03:12.440 align:start position:0%
 
 

00:03:12.440 --> 00:03:15.440 align:start position:0%
 
chapter

