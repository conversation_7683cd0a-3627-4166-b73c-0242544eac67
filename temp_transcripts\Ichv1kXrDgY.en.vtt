WEBVTT
Kind: captions
Language: en

00:00:08.599 --> 00:00:11.150 align:start position:0%
 
in<00:00:08.800><c> this</c><00:00:09.040><c> presentation</c><00:00:10.040><c> we</c><00:00:10.280><c> will</c><00:00:10.519><c> describe</c>

00:00:11.150 --> 00:00:11.160 align:start position:0%
in this presentation we will describe
 

00:00:11.160 --> 00:00:13.190 align:start position:0%
in this presentation we will describe
the<00:00:11.320><c> main</c><00:00:11.679><c> differences</c><00:00:12.320><c> between</c><00:00:12.880><c> two</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
the main differences between two
 

00:00:13.200 --> 00:00:16.590 align:start position:0%
the main differences between two
versions<00:00:14.000><c> of</c><00:00:14.240><c> the</c><00:00:14.559><c> pan</c><00:00:14.920><c> European</c><00:00:15.559><c> climate</c>

00:00:16.590 --> 00:00:16.600 align:start position:0%
versions of the pan European climate
 

00:00:16.600 --> 00:00:20.870 align:start position:0%
versions of the pan European climate
database<00:00:17.640><c> specifically</c><00:00:18.640><c> the</c><00:00:19.080><c> recent</c><00:00:20.000><c> PCD</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
database specifically the recent PCD
 

00:00:20.880 --> 00:00:24.710 align:start position:0%
database specifically the recent PCD
version<00:00:21.240><c> 4.2</c><00:00:22.199><c> compared</c><00:00:22.640><c> to</c><00:00:22.800><c> the</c><00:00:22.960><c> previous</c><00:00:23.359><c> PCD</c>

00:00:24.710 --> 00:00:24.720 align:start position:0%
version 4.2 compared to the previous PCD
 

00:00:24.720 --> 00:00:27.950 align:start position:0%
version 4.2 compared to the previous PCD
4.1<00:00:25.720><c> we</c><00:00:25.920><c> start</c><00:00:26.320><c> by</c><00:00:26.519><c> looking</c><00:00:26.960><c> at</c><00:00:27.480><c> some</c><00:00:27.679><c> of</c><00:00:27.800><c> the</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
4.1 we start by looking at some of the
 

00:00:27.960 --> 00:00:31.550 align:start position:0%
4.1 we start by looking at some of the
recent<00:00:28.320><c> development</c><00:00:29.199><c> in</c><00:00:29.439><c> the</c><00:00:29.759><c> p</c><00:00:30.240><c> PCD</c><00:00:31.240><c> so</c><00:00:31.400><c> in</c>

00:00:31.550 --> 00:00:31.560 align:start position:0%
recent development in the p PCD so in
 

00:00:31.560 --> 00:00:35.630 align:start position:0%
recent development in the p PCD so in
January<00:00:31.960><c> 2021</c><00:00:32.800><c> the</c><00:00:33.000><c> PCD</c><00:00:33.600><c> 3.4</c><00:00:34.600><c> 3.1</c><00:00:35.399><c> was</c>

00:00:35.630 --> 00:00:35.640 align:start position:0%
January 2021 the PCD 3.4 3.1 was
 

00:00:35.640 --> 00:00:38.869 align:start position:0%
January 2021 the PCD 3.4 3.1 was
released<00:00:36.640><c> uh</c><00:00:36.840><c> which</c><00:00:37.280><c> had</c><00:00:37.640><c> a</c><00:00:37.800><c> historical</c><00:00:38.399><c> data</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
released uh which had a historical data
 

00:00:38.879 --> 00:00:43.150 align:start position:0%
released uh which had a historical data
only<00:00:39.760><c> and</c><00:00:39.960><c> this</c><00:00:40.160><c> were</c><00:00:40.440><c> taken</c><00:00:41.360><c> from</c><00:00:42.200><c> the</c>

00:00:43.150 --> 00:00:43.160 align:start position:0%
only and this were taken from the
 

00:00:43.160 --> 00:00:45.670 align:start position:0%
only and this were taken from the
climate<00:00:43.960><c> reanalysis</c><00:00:44.719><c> called</c><00:00:45.000><c> era</c><00:00:45.360><c> five</c>

00:00:45.670 --> 00:00:45.680 align:start position:0%
climate reanalysis called era five
 

00:00:45.680 --> 00:00:48.270 align:start position:0%
climate reanalysis called era five
covering<00:00:46.079><c> the</c><00:00:46.239><c> period</c><00:00:46.520><c> of</c><00:00:46.640><c> 1980</c><00:00:47.399><c> to</c>

00:00:48.270 --> 00:00:48.280 align:start position:0%
covering the period of 1980 to
 

00:00:48.280 --> 00:00:51.549 align:start position:0%
covering the period of 1980 to
2019<00:00:49.280><c> specifically</c><00:00:49.879><c> no</c><00:00:50.199><c> projection</c><00:00:51.039><c> data</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
2019 specifically no projection data
 

00:00:51.559 --> 00:00:54.189 align:start position:0%
2019 specifically no projection data
were<00:00:51.879><c> available</c><00:00:52.359><c> for</c><00:00:52.680><c> this</c><00:00:53.000><c> version</c><00:00:53.719><c> and</c><00:00:54.000><c> the</c>

00:00:54.189 --> 00:00:54.199 align:start position:0%
were available for this version and the
 

00:00:54.199 --> 00:00:56.630 align:start position:0%
were available for this version and the
data<00:00:54.440><c> were</c><00:00:54.640><c> available</c><00:00:55.039><c> through</c><00:00:55.280><c> HTO</c><00:00:56.280><c> then</c>

00:00:56.630 --> 00:00:56.640 align:start position:0%
data were available through HTO then
 

00:00:56.640 --> 00:00:58.750 align:start position:0%
data were available through HTO then
after<00:00:57.039><c> the</c><00:00:57.440><c> Copernicus</c><00:00:58.039><c> climate</c><00:00:58.359><c> change</c>

00:00:58.750 --> 00:00:58.760 align:start position:0%
after the Copernicus climate change
 

00:00:58.760 --> 00:01:02.310 align:start position:0%
after the Copernicus climate change
service<00:00:59.600><c> cont</c><00:01:00.000><c> truck</c><00:01:00.280><c> started</c><00:01:00.800><c> in</c><00:01:01.000><c> September</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
service cont truck started in September
 

00:01:02.320 --> 00:01:07.149 align:start position:0%
service cont truck started in September
2022<00:01:03.320><c> we</c><00:01:03.480><c> work</c><00:01:03.800><c> towards</c><00:01:04.239><c> the</c><00:01:04.640><c> pcd4</c><00:01:05.840><c> Z</c><00:01:06.840><c> which</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
2022 we work towards the pcd4 Z which
 

00:01:07.159 --> 00:01:10.830 align:start position:0%
2022 we work towards the pcd4 Z which
included<00:01:08.080><c> an</c><00:01:08.280><c> extended</c><00:01:08.920><c> era</c><00:01:09.400><c> five</c><00:01:09.960><c> data</c><00:01:10.320><c> set</c>

00:01:10.830 --> 00:01:10.840 align:start position:0%
included an extended era five data set
 

00:01:10.840 --> 00:01:14.350 align:start position:0%
included an extended era five data set
from<00:01:11.040><c> 1980</c><00:01:11.600><c> to</c><00:01:12.000><c> 2021</c><00:01:13.000><c> but</c><00:01:13.320><c> especially</c><00:01:14.119><c> we</c>

00:01:14.350 --> 00:01:14.360 align:start position:0%
from 1980 to 2021 but especially we
 

00:01:14.360 --> 00:01:17.109 align:start position:0%
from 1980 to 2021 but especially we
included<00:01:15.280><c> projection</c><00:01:15.759><c> data</c><00:01:16.240><c> from</c><00:01:16.640><c> Regional</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
included projection data from Regional
 

00:01:17.119 --> 00:01:19.950 align:start position:0%
included projection data from Regional
climate<00:01:17.520><c> models</c><00:01:18.200><c> called</c><00:01:18.560><c> Euro</c><00:01:18.960><c> cordex</c><00:01:19.600><c> and</c>

00:01:19.950 --> 00:01:19.960 align:start position:0%
climate models called Euro cordex and
 

00:01:19.960 --> 00:01:23.310 align:start position:0%
climate models called Euro cordex and
two<00:01:20.960><c> scenarios</c><00:01:21.799><c> emission</c><00:01:22.200><c> scenarios</c><00:01:22.720><c> so</c>

00:01:23.310 --> 00:01:23.320 align:start position:0%
two scenarios emission scenarios so
 

00:01:23.320 --> 00:01:25.950 align:start position:0%
two scenarios emission scenarios so
rcp45<00:01:24.320><c> which</c><00:01:24.439><c> is</c><00:01:24.560><c> a</c><00:01:24.720><c> mid-range</c><00:01:25.360><c> and</c><00:01:25.520><c> the</c><00:01:25.720><c> high</c>

00:01:25.950 --> 00:01:25.960 align:start position:0%
rcp45 which is a mid-range and the high
 

00:01:25.960 --> 00:01:27.590 align:start position:0%
rcp45 which is a mid-range and the high
range

00:01:27.590 --> 00:01:27.600 align:start position:0%
range
 

00:01:27.600 --> 00:01:30.109 align:start position:0%
range
8.5<00:01:28.600><c> and</c><00:01:28.880><c> the</c><00:01:29.000><c> three</c><00:01:29.280><c> climate</c><00:01:29.600><c> model</c><00:01:29.920><c> those</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
8.5 and the three climate model those
 

00:01:30.119 --> 00:01:32.630 align:start position:0%
8.5 and the three climate model those
were<00:01:30.439><c> selected</c><00:01:31.320><c> data</c><00:01:31.640><c> were</c><00:01:31.840><c> available</c><00:01:32.320><c> still</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
were selected data were available still
 

00:01:32.640 --> 00:01:36.870 align:start position:0%
were selected data were available still
through<00:01:32.880><c> the</c><00:01:33.360><c> stoe</c><00:01:34.360><c> in</c><00:01:34.640><c> September</c><00:01:35.159><c> 2024</c><00:01:36.159><c> we</c><00:01:36.600><c> um</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
through the stoe in September 2024 we um
 

00:01:36.880 --> 00:01:39.710 align:start position:0%
through the stoe in September 2024 we um
published<00:01:37.600><c> the</c><00:01:38.200><c> new</c><00:01:38.960><c> uh</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
published the new uh
 

00:01:39.720 --> 00:01:45.550 align:start position:0%
published the new uh
4.1<00:01:40.759><c> version</c><00:01:41.759><c> which</c><00:01:42.320><c> now</c><00:01:43.320><c> moved</c><00:01:44.280><c> to</c><00:01:45.119><c> climate</c>

00:01:45.550 --> 00:01:45.560 align:start position:0%
4.1 version which now moved to climate
 

00:01:45.560 --> 00:01:49.670 align:start position:0%
4.1 version which now moved to climate
data<00:01:46.000><c> from</c><00:01:46.360><c> the</c><00:01:46.680><c> global</c><00:01:47.360><c> models</c><00:01:47.960><c> CIP</c><00:01:48.520><c> 6</c><00:01:49.000><c> still</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
data from the global models CIP 6 still
 

00:01:49.680 --> 00:01:52.190 align:start position:0%
data from the global models CIP 6 still
covering<00:01:50.079><c> the</c><00:01:50.240><c> European</c><00:01:50.880><c> domain</c><00:01:51.560><c> but</c><00:01:52.079><c> the</c>

00:01:52.190 --> 00:01:52.200 align:start position:0%
covering the European domain but the
 

00:01:52.200 --> 00:01:56.109 align:start position:0%
covering the European domain but the
models<00:01:52.799><c> are</c><00:01:53.079><c> Global</c><00:01:53.719><c> and</c><00:01:54.399><c> new</c><00:01:54.719><c> scenarios</c><00:01:55.600><c> also</c>

00:01:56.109 --> 00:01:56.119 align:start position:0%
models are Global and new scenarios also
 

00:01:56.119 --> 00:01:59.870 align:start position:0%
models are Global and new scenarios also
called<00:01:56.840><c> SSP</c><00:01:57.840><c> in</c><00:01:58.000><c> this</c><00:01:58.200><c> case</c><00:01:58.399><c> we</c><00:01:58.520><c> use</c><00:01:58.719><c> 245</c><00:01:59.680><c> which</c>

00:01:59.870 --> 00:01:59.880 align:start position:0%
called SSP in this case we use 245 which
 

00:01:59.880 --> 00:02:02.069 align:start position:0%
called SSP in this case we use 245 which
which<00:01:59.960><c> is</c><00:02:00.079><c> the</c><00:02:00.200><c> mid-range</c><00:02:01.000><c> and</c><00:02:01.360><c> three</c><00:02:01.640><c> climate</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
which is the mid-range and three climate
 

00:02:02.079 --> 00:02:04.630 align:start position:0%
which is the mid-range and three climate
models<00:02:02.479><c> covering</c><00:02:02.840><c> the</c><00:02:02.960><c> period</c><00:02:03.280><c> 2015</c><00:02:03.920><c> to</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
models covering the period 2015 to
 

00:02:04.640 --> 00:02:09.109 align:start position:0%
models covering the period 2015 to
2100<00:02:05.640><c> availability</c><00:02:06.880><c> now</c><00:02:07.880><c> started</c><00:02:08.399><c> to</c><00:02:08.599><c> be</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
2100 availability now started to be
 

00:02:09.119 --> 00:02:12.990 align:start position:0%
2100 availability now started to be
through<00:02:09.599><c> the</c><00:02:10.440><c> public</c><00:02:11.039><c> climate</c><00:02:11.440><c> data</c><00:02:11.760><c> store</c><00:02:12.360><c> of</c>

00:02:12.990 --> 00:02:13.000 align:start position:0%
through the public climate data store of
 

00:02:13.000 --> 00:02:15.869 align:start position:0%
through the public climate data store of
c3s<00:02:14.000><c> and</c><00:02:14.400><c> uh</c><00:02:14.599><c> currently</c><00:02:15.120><c> we</c><00:02:15.239><c> are</c><00:02:15.400><c> working</c>

00:02:15.869 --> 00:02:15.879 align:start position:0%
c3s and uh currently we are working
 

00:02:15.879 --> 00:02:18.309 align:start position:0%
c3s and uh currently we are working
towards<00:02:16.160><c> the</c><00:02:16.360><c> publication</c><00:02:17.080><c> of</c><00:02:17.319><c> the</c><00:02:17.519><c> version</c>

00:02:18.309 --> 00:02:18.319 align:start position:0%
towards the publication of the version
 

00:02:18.319 --> 00:02:21.430 align:start position:0%
towards the publication of the version
4.2<00:02:19.319><c> which</c><00:02:19.599><c> has</c><00:02:19.879><c> some</c><00:02:20.160><c> improvements</c><00:02:20.920><c> to</c><00:02:21.239><c> the</c>

00:02:21.430 --> 00:02:21.440 align:start position:0%
4.2 which has some improvements to the
 

00:02:21.440 --> 00:02:25.309 align:start position:0%
4.2 which has some improvements to the
modeling<00:02:22.200><c> and</c><00:02:22.440><c> especially</c><00:02:23.319><c> expansion</c><00:02:24.319><c> of</c><00:02:25.040><c> the</c>

00:02:25.309 --> 00:02:25.319 align:start position:0%
modeling and especially expansion of the
 

00:02:25.319 --> 00:02:27.350 align:start position:0%
modeling and especially expansion of the
number<00:02:25.599><c> of</c><00:02:25.840><c> climate</c><00:02:26.200><c> models</c><00:02:26.680><c> and</c><00:02:26.879><c> number</c><00:02:27.200><c> of</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
number of climate models and number of
 

00:02:27.360 --> 00:02:30.110 align:start position:0%
number of climate models and number of
scenarios<00:02:27.959><c> for</c><00:02:28.280><c> the</c><00:02:28.519><c> projections</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
scenarios for the projections
 

00:02:30.120 --> 00:02:32.270 align:start position:0%
scenarios for the projections
and<00:02:30.360><c> the</c><00:02:30.560><c> data</c><00:02:30.840><c> will</c><00:02:31.280><c> also</c><00:02:31.560><c> be</c><00:02:31.760><c> available</c>

00:02:32.270 --> 00:02:32.280 align:start position:0%
and the data will also be available
 

00:02:32.280 --> 00:02:36.350 align:start position:0%
and the data will also be available
through<00:02:32.680><c> the</c><00:02:32.959><c> climate</c><00:02:33.360><c> data</c><00:02:33.640><c> store</c><00:02:34.599><c> in</c><00:02:35.480><c> Q2</c>

00:02:36.350 --> 00:02:36.360 align:start position:0%
through the climate data store in Q2
 

00:02:36.360 --> 00:02:39.350 align:start position:0%
through the climate data store in Q2
2025<00:02:37.360><c> there</c><00:02:37.599><c> also</c><00:02:38.280><c> ideas</c><00:02:38.760><c> for</c><00:02:39.000><c> future</c>

00:02:39.350 --> 00:02:39.360 align:start position:0%
2025 there also ideas for future
 

00:02:39.360 --> 00:02:42.070 align:start position:0%
2025 there also ideas for future
developments<00:02:40.000><c> but</c><00:02:40.200><c> this</c><00:02:40.560><c> are</c><00:02:40.879><c> not</c><00:02:41.400><c> confirmed</c>

00:02:42.070 --> 00:02:42.080 align:start position:0%
developments but this are not confirmed
 

00:02:42.080 --> 00:02:45.830 align:start position:0%
developments but this are not confirmed
yet<00:02:42.680><c> and</c><00:02:42.959><c> they</c><00:02:43.159><c> are</c><00:02:43.480><c> listed</c><00:02:44.120><c> here</c><00:02:44.480><c> in</c><00:02:44.640><c> the</c><00:02:44.840><c> last</c>

00:02:45.830 --> 00:02:45.840 align:start position:0%
yet and they are listed here in the last
 

00:02:45.840 --> 00:02:49.149 align:start position:0%
yet and they are listed here in the last
box<00:02:46.840><c> this</c><00:02:47.159><c> is</c><00:02:47.440><c> just</c><00:02:47.599><c> a</c><00:02:47.920><c> reference</c><00:02:48.519><c> slide</c><00:02:49.040><c> I</c>

00:02:49.149 --> 00:02:49.159 align:start position:0%
box this is just a reference slide I
 

00:02:49.159 --> 00:02:52.390 align:start position:0%
box this is just a reference slide I
won't<00:02:49.400><c> go</c><00:02:49.599><c> through</c><00:02:49.959><c> all</c><00:02:50.120><c> of</c><00:02:50.319><c> it</c><00:02:51.080><c> uh</c><00:02:51.319><c> just</c><00:02:51.519><c> to</c>

00:02:52.390 --> 00:02:52.400 align:start position:0%
won't go through all of it uh just to
 

00:02:52.400 --> 00:02:54.550 align:start position:0%
won't go through all of it uh just to
highlight<00:02:53.040><c> what</c><00:02:53.200><c> the</c><00:02:53.360><c> main</c><00:02:53.640><c> differences</c><00:02:54.200><c> are</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
highlight what the main differences are
 

00:02:54.560 --> 00:02:57.990 align:start position:0%
highlight what the main differences are
so<00:02:54.760><c> there</c><00:02:54.879><c> is</c><00:02:55.120><c> improvements</c><00:02:56.120><c> in</c><00:02:56.840><c> onshore</c><00:02:57.519><c> Wind</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
so there is improvements in onshore Wind
 

00:02:58.000 --> 00:03:00.190 align:start position:0%
so there is improvements in onshore Wind
offshore<00:02:58.480><c> wind</c><00:02:58.879><c> and</c><00:02:59.120><c> PV</c><00:02:59.440><c> modeling</c><00:03:00.040><c> which</c>

00:03:00.190 --> 00:03:00.200 align:start position:0%
offshore wind and PV modeling which
 

00:03:00.200 --> 00:03:04.229 align:start position:0%
offshore wind and PV modeling which
will'll<00:03:00.480><c> cover</c><00:03:01.159><c> more</c><00:03:01.599><c> in</c><00:03:01.879><c> detail</c><00:03:02.560><c> in</c><00:03:02.800><c> the</c><00:03:03.200><c> next</c>

00:03:04.229 --> 00:03:04.239 align:start position:0%
will'll cover more in detail in the next
 

00:03:04.239 --> 00:03:08.710 align:start position:0%
will'll cover more in detail in the next
slides<00:03:05.239><c> so</c><00:03:06.159><c> just</c><00:03:06.640><c> uh</c><00:03:07.040><c> summarizing</c><00:03:08.040><c> the</c><00:03:08.400><c> main</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
slides so just uh summarizing the main
 

00:03:08.720 --> 00:03:13.789 align:start position:0%
slides so just uh summarizing the main
differences<00:03:09.319><c> again</c><00:03:10.239><c> in</c><00:03:10.799><c> terms</c><00:03:11.680><c> of</c><00:03:12.680><c> projection</c>

00:03:13.789 --> 00:03:13.799 align:start position:0%
differences again in terms of projection
 

00:03:13.799 --> 00:03:17.990 align:start position:0%
differences again in terms of projection
data<00:03:14.799><c> 4.1</c><00:03:15.799><c> has</c><00:03:16.480><c> had</c><00:03:16.680><c> three</c><00:03:16.959><c> projection</c><00:03:17.480><c> models</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
data 4.1 has had three projection models
 

00:03:18.000 --> 00:03:20.309 align:start position:0%
data 4.1 has had three projection models
only<00:03:18.239><c> one</c><00:03:18.480><c> scenario</c><00:03:19.120><c> and</c><00:03:19.360><c> limited</c><00:03:19.840><c> cover</c>

00:03:20.309 --> 00:03:20.319 align:start position:0%
only one scenario and limited cover
 

00:03:20.319 --> 00:03:23.390 align:start position:0%
only one scenario and limited cover
period<00:03:21.000><c> with</c><00:03:21.159><c> 4.2</c><00:03:22.159><c> we</c><00:03:22.400><c> extended</c><00:03:22.840><c> to</c><00:03:23.040><c> six</c>

00:03:23.390 --> 00:03:23.400 align:start position:0%
period with 4.2 we extended to six
 

00:03:23.400 --> 00:03:26.430 align:start position:0%
period with 4.2 we extended to six
projection<00:03:23.920><c> models</c><00:03:24.360><c> for</c><00:03:25.239><c> scenarios</c><00:03:26.080><c> and</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
projection models for scenarios and
 

00:03:26.440 --> 00:03:29.149 align:start position:0%
projection models for scenarios and
extension<00:03:27.200><c> of</c><00:03:27.360><c> the</c><00:03:27.519><c> period</c><00:03:28.080><c> both</c><00:03:28.519><c> for</c><00:03:29.000><c> the</c>

00:03:29.149 --> 00:03:29.159 align:start position:0%
extension of the period both for the
 

00:03:29.159 --> 00:03:31.030 align:start position:0%
extension of the period both for the
historical<00:03:30.040><c> and</c><00:03:30.239><c> the</c>

00:03:31.030 --> 00:03:31.040 align:start position:0%
historical and the
 

00:03:31.040 --> 00:03:33.190 align:start position:0%
historical and the
projection<00:03:32.040><c> data</c>

00:03:33.190 --> 00:03:33.200 align:start position:0%
projection data
 

00:03:33.200 --> 00:03:36.190 align:start position:0%
projection data
sets<00:03:34.200><c> the</c><00:03:34.560><c> specifics</c><00:03:35.239><c> of</c><00:03:35.439><c> the</c><00:03:35.560><c> models</c><00:03:35.959><c> are</c>

00:03:36.190 --> 00:03:36.200 align:start position:0%
sets the specifics of the models are
 

00:03:36.200 --> 00:03:38.270 align:start position:0%
sets the specifics of the models are
given<00:03:36.480><c> in</c><00:03:36.640><c> the</c><00:03:36.879><c> slides</c><00:03:37.480><c> again</c><00:03:37.760><c> I</c><00:03:37.840><c> won't</c><00:03:38.080><c> go</c>

00:03:38.270 --> 00:03:38.280 align:start position:0%
given in the slides again I won't go
 

00:03:38.280 --> 00:03:41.270 align:start position:0%
given in the slides again I won't go
through<00:03:38.480><c> the</c><00:03:38.640><c> details</c><00:03:39.200><c> but</c><00:03:39.400><c> you</c><00:03:39.519><c> can</c><00:03:39.879><c> have</c><00:03:40.599><c> the</c>

00:03:41.270 --> 00:03:41.280 align:start position:0%
through the details but you can have the
 

00:03:41.280 --> 00:03:44.030 align:start position:0%
through the details but you can have the
the<00:03:41.519><c> names</c><00:03:42.040><c> here</c><00:03:42.439><c> of</c><00:03:42.599><c> the</c><00:03:42.760><c> models</c><00:03:43.319><c> as</c><00:03:43.480><c> well</c><00:03:43.760><c> the</c>

00:03:44.030 --> 00:03:44.040 align:start position:0%
the names here of the models as well the
 

00:03:44.040 --> 00:03:48.229 align:start position:0%
the names here of the models as well the
specific<00:03:44.840><c> ssps</c><00:03:45.840><c> you</c><00:03:45.959><c> can</c><00:03:46.159><c> see</c><00:03:46.680><c> that</c><00:03:47.120><c> the</c><00:03:47.519><c> uh</c>

00:03:48.229 --> 00:03:48.239 align:start position:0%
specific ssps you can see that the uh
 

00:03:48.239 --> 00:03:51.229 align:start position:0%
specific ssps you can see that the uh
scenarios<00:03:49.080><c> are</c>

00:03:51.229 --> 00:03:51.239 align:start position:0%
scenarios are
 

00:03:51.239 --> 00:03:54.030 align:start position:0%
scenarios are
41126<00:03:52.239><c> which</c><00:03:52.360><c> is</c><00:03:52.480><c> the</c><00:03:52.680><c> lowest</c><00:03:53.599><c> emission</c>

00:03:54.030 --> 00:03:54.040 align:start position:0%
41126 which is the lowest emission
 

00:03:54.040 --> 00:03:57.069 align:start position:0%
41126 which is the lowest emission
scenario<00:03:54.840><c> so</c><00:03:55.720><c> high</c>

00:03:57.069 --> 00:03:57.079 align:start position:0%
scenario so high
 

00:03:57.079 --> 00:04:00.949 align:start position:0%
scenario so high
mitigation<00:03:58.079><c> uh</c><00:03:58.200><c> up</c><00:03:58.439><c> to</c><00:03:58.879><c> 5</c><00:03:59.079><c> 85</c><00:03:59.959><c> which</c><00:04:00.079><c> is</c><00:04:00.280><c> a</c>

00:04:00.949 --> 00:04:00.959 align:start position:0%
mitigation uh up to 5 85 which is a
 

00:04:00.959 --> 00:04:03.309 align:start position:0%
mitigation uh up to 5 85 which is a
pretty<00:04:01.319><c> high</c><00:04:01.680><c> emission</c>

00:04:03.309 --> 00:04:03.319 align:start position:0%
pretty high emission
 

00:04:03.319 --> 00:04:06.949 align:start position:0%
pretty high emission
scenarios<00:04:04.319><c> and</c><00:04:05.280><c> the</c><00:04:05.680><c> historical</c><00:04:06.280><c> period</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
scenarios and the historical period
 

00:04:06.959 --> 00:04:10.350 align:start position:0%
scenarios and the historical period
covers<00:04:07.239><c> the</c><00:04:07.360><c> period</c><00:04:07.680><c> 1950</c><00:04:08.480><c> to</c><00:04:08.920><c> 2024</c><00:04:09.920><c> and</c><00:04:10.159><c> will</c>

00:04:10.350 --> 00:04:10.360 align:start position:0%
covers the period 1950 to 2024 and will
 

00:04:10.360 --> 00:04:13.830 align:start position:0%
covers the period 1950 to 2024 and will
be<00:04:10.840><c> updated</c><00:04:11.840><c> regularly</c><00:04:12.799><c> and</c><00:04:12.959><c> the</c><00:04:13.159><c> projections</c>

00:04:13.830 --> 00:04:13.840 align:start position:0%
be updated regularly and the projections
 

00:04:13.840 --> 00:04:17.110 align:start position:0%
be updated regularly and the projections
cover<00:04:14.159><c> the</c><00:04:14.319><c> period</c><00:04:14.720><c> 2015</c><00:04:15.480><c> to</c>

00:04:17.110 --> 00:04:17.120 align:start position:0%
cover the period 2015 to
 

00:04:17.120 --> 00:04:20.830 align:start position:0%
cover the period 2015 to
2100<00:04:18.120><c> now</c><00:04:18.479><c> we</c><00:04:18.720><c> cover</c><00:04:19.239><c> some</c><00:04:19.600><c> specifics</c><00:04:20.479><c> in</c><00:04:20.639><c> the</c>

00:04:20.830 --> 00:04:20.840 align:start position:0%
2100 now we cover some specifics in the
 

00:04:20.840 --> 00:04:24.030 align:start position:0%
2100 now we cover some specifics in the
differences<00:04:21.639><c> particularly</c><00:04:22.479><c> here</c><00:04:23.040><c> in</c><00:04:23.280><c> the</c><00:04:23.479><c> way</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
differences particularly here in the way
 

00:04:24.040 --> 00:04:27.670 align:start position:0%
differences particularly here in the way
we<00:04:24.440><c> have</c><00:04:25.120><c> uh</c><00:04:25.720><c> handled</c><00:04:26.240><c> the</c><00:04:26.360><c> wind</c><00:04:26.759><c> speed</c>

00:04:27.670 --> 00:04:27.680 align:start position:0%
we have uh handled the wind speed
 

00:04:27.680 --> 00:04:29.990 align:start position:0%
we have uh handled the wind speed
there's<00:04:28.000><c> some</c><00:04:28.320><c> processing</c><00:04:29.280><c> that</c><00:04:29.560><c> that</c>

00:04:29.990 --> 00:04:30.000 align:start position:0%
there's some processing that that
 

00:04:30.000 --> 00:04:32.830 align:start position:0%
there's some processing that that
required<00:04:30.720><c> to</c><00:04:31.320><c> improve</c><00:04:31.840><c> the</c><00:04:32.039><c> way</c><00:04:32.360><c> the</c><00:04:32.520><c> wind</c>

00:04:32.830 --> 00:04:32.840 align:start position:0%
required to improve the way the wind
 

00:04:32.840 --> 00:04:36.590 align:start position:0%
required to improve the way the wind
speeds<00:04:33.600><c> are</c><00:04:34.280><c> produced</c><00:04:35.160><c> starting</c><00:04:35.720><c> from</c><00:04:36.320><c> the</c>

00:04:36.590 --> 00:04:36.600 align:start position:0%
speeds are produced starting from the
 

00:04:36.600 --> 00:04:39.430 align:start position:0%
speeds are produced starting from the
er5<00:04:37.600><c> reanalysis</c><00:04:38.520><c> so</c><00:04:38.800><c> this</c><00:04:38.919><c> is</c><00:04:39.039><c> for</c><00:04:39.280><c> the</c>

00:04:39.430 --> 00:04:39.440 align:start position:0%
er5 reanalysis so this is for the
 

00:04:39.440 --> 00:04:44.070 align:start position:0%
er5 reanalysis so this is for the
historical<00:04:40.479><c> period</c><00:04:41.479><c> and</c><00:04:42.000><c> uh</c><00:04:42.240><c> in</c><00:04:42.479><c> the</c><00:04:42.880><c> 4.1</c><00:04:43.880><c> at</c>

00:04:44.070 --> 00:04:44.080 align:start position:0%
historical period and uh in the 4.1 at
 

00:04:44.080 --> 00:04:47.590 align:start position:0%
historical period and uh in the 4.1 at
the<00:04:44.360><c> top</c><00:04:45.039><c> we</c><00:04:45.199><c> see</c><00:04:45.960><c> the</c><00:04:46.440><c> method</c><00:04:46.880><c> that</c><00:04:47.039><c> was</c><00:04:47.240><c> used</c>

00:04:47.590 --> 00:04:47.600 align:start position:0%
the top we see the method that was used
 

00:04:47.600 --> 00:04:50.469 align:start position:0%
the top we see the method that was used
is<00:04:48.120><c> the</c><00:04:48.759><c> cumulative</c><00:04:49.360><c> distribution</c><00:04:50.000><c> function</c>

00:04:50.469 --> 00:04:50.479 align:start position:0%
is the cumulative distribution function
 

00:04:50.479 --> 00:04:52.710 align:start position:0%
is the cumulative distribution function
for<00:04:50.680><c> the</c><00:04:50.840><c> bias</c><00:04:51.280><c> adjustment</c><00:04:52.280><c> and</c><00:04:52.520><c> the</c>

00:04:52.710 --> 00:04:52.720 align:start position:0%
for the bias adjustment and the
 

00:04:52.720 --> 00:04:54.870 align:start position:0%
for the bias adjustment and the
reference<00:04:53.240><c> data</c><00:04:53.600><c> set</c><00:04:54.000><c> was</c><00:04:54.199><c> a</c><00:04:54.400><c> regional</c>

00:04:54.870 --> 00:04:54.880 align:start position:0%
reference data set was a regional
 

00:04:54.880 --> 00:04:58.670 align:start position:0%
reference data set was a regional
reanalysis<00:04:55.639><c> called</c><00:04:56.160><c> Cosmo</c><00:04:56.880><c> Ria</c><00:04:57.400><c> 6</c><00:04:58.400><c> and</c><00:04:58.600><c> we</c>

00:04:58.670 --> 00:04:58.680 align:start position:0%
reanalysis called Cosmo Ria 6 and we
 

00:04:58.680 --> 00:05:02.310 align:start position:0%
reanalysis called Cosmo Ria 6 and we
were<00:04:58.880><c> using</c><00:04:59.160><c> daily</c><00:05:00.000><c> AES</c><00:05:01.000><c> and</c><00:05:01.400><c> and</c><00:05:01.639><c> then</c><00:05:02.160><c> we</c>

00:05:02.310 --> 00:05:02.320 align:start position:0%
were using daily AES and and then we
 

00:05:02.320 --> 00:05:06.150 align:start position:0%
were using daily AES and and then we
were<00:05:02.800><c> Computing</c><00:05:03.560><c> the</c><00:05:04.440><c> wind</c><00:05:04.880><c> at</c><00:05:05.280><c> 100</c><00:05:05.639><c> met</c>

00:05:06.150 --> 00:05:06.160 align:start position:0%
were Computing the wind at 100 met
 

00:05:06.160 --> 00:05:08.469 align:start position:0%
were Computing the wind at 100 met
through<00:05:06.520><c> Power</c><00:05:06.880><c> low</c><00:05:07.280><c> and</c><00:05:07.479><c> AA</c><00:05:07.880><c> coefficient</c>

00:05:08.469 --> 00:05:08.479 align:start position:0%
through Power low and AA coefficient
 

00:05:08.479 --> 00:05:11.590 align:start position:0%
through Power low and AA coefficient
computed<00:05:09.000><c> from</c><00:05:09.240><c> the</c><00:05:09.360><c> original</c><00:05:09.840><c> wind</c><00:05:10.600><c> data</c>

00:05:11.590 --> 00:05:11.600 align:start position:0%
computed from the original wind data
 

00:05:11.600 --> 00:05:13.110 align:start position:0%
computed from the original wind data
however<00:05:11.880><c> we</c><00:05:12.039><c> found</c><00:05:12.440><c> that</c><00:05:12.600><c> there</c><00:05:12.720><c> were</c><00:05:12.880><c> some</c>

00:05:13.110 --> 00:05:13.120 align:start position:0%
however we found that there were some
 

00:05:13.120 --> 00:05:16.909 align:start position:0%
however we found that there were some
issues<00:05:13.560><c> with</c><00:05:13.759><c> the</c><00:05:14.240><c> dial</c><00:05:14.880><c> cycle</c><00:05:15.520><c> and</c><00:05:16.199><c> also</c><00:05:16.680><c> some</c>

00:05:16.909 --> 00:05:16.919 align:start position:0%
issues with the dial cycle and also some
 

00:05:16.919 --> 00:05:20.870 align:start position:0%
issues with the dial cycle and also some
of<00:05:17.160><c> the</c><00:05:17.720><c> wind</c><00:05:18.120><c> speed</c><00:05:18.840><c> uh</c><00:05:19.560><c> data</c><00:05:20.240><c> so</c><00:05:20.479><c> the</c><00:05:20.639><c> wind</c>

00:05:20.870 --> 00:05:20.880 align:start position:0%
of the wind speed uh data so the wind
 

00:05:20.880 --> 00:05:23.990 align:start position:0%
of the wind speed uh data so the wind
speed<00:05:21.319><c> was</c><00:05:22.000><c> underestimated</c><00:05:23.000><c> so</c><00:05:23.199><c> we</c><00:05:23.360><c> moved</c><00:05:23.720><c> to</c>

00:05:23.990 --> 00:05:24.000 align:start position:0%
speed was underestimated so we moved to
 

00:05:24.000 --> 00:05:27.590 align:start position:0%
speed was underestimated so we moved to
the<00:05:24.240><c> a</c><00:05:24.440><c> different</c><00:05:25.080><c> approach</c><00:05:25.720><c> with</c><00:05:26.240><c> 4.2</c><00:05:27.240><c> at</c><00:05:27.400><c> the</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
the a different approach with 4.2 at the
 

00:05:27.600 --> 00:05:30.230 align:start position:0%
the a different approach with 4.2 at the
bottom<00:05:28.160><c> here</c><00:05:28.520><c> and</c><00:05:28.759><c> we</c><00:05:29.160><c> used</c><00:05:29.479><c> the</c><00:05:29.759><c> simpler</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
bottom here and we used the simpler
 

00:05:30.240 --> 00:05:33.670 align:start position:0%
bottom here and we used the simpler
method<00:05:30.720><c> called</c><00:05:30.960><c> the</c><00:05:31.199><c> Delta</c><00:05:32.400><c> method</c><00:05:33.400><c> the</c>

00:05:33.670 --> 00:05:33.680 align:start position:0%
method called the Delta method the
 

00:05:33.680 --> 00:05:36.029 align:start position:0%
method called the Delta method the
reference<00:05:34.199><c> data</c><00:05:34.600><c> set</c><00:05:35.000><c> now</c><00:05:35.319><c> is</c><00:05:35.479><c> the</c><00:05:35.680><c> global</c>

00:05:36.029 --> 00:05:36.039 align:start position:0%
reference data set now is the global
 

00:05:36.039 --> 00:05:40.990 align:start position:0%
reference data set now is the global
wind<00:05:36.400><c> Atlas</c><00:05:36.960><c> version</c><00:05:37.400><c> two</c><00:05:38.360><c> and</c><00:05:38.560><c> then</c><00:05:39.000><c> the</c><00:05:40.000><c> wind</c>

00:05:40.990 --> 00:05:41.000 align:start position:0%
wind Atlas version two and then the wind
 

00:05:41.000 --> 00:05:44.390 align:start position:0%
wind Atlas version two and then the wind
speed<00:05:41.479><c> at</c><00:05:42.080><c> 100</c><00:05:42.400><c> meter</c><00:05:42.800><c> was</c><00:05:43.000><c> by</c><00:05:43.360><c> adjusted</c><00:05:44.000><c> using</c>

00:05:44.390 --> 00:05:44.400 align:start position:0%
speed at 100 meter was by adjusted using
 

00:05:44.400 --> 00:05:48.029 align:start position:0%
speed at 100 meter was by adjusted using
the<00:05:44.560><c> ER</c><00:05:45.039><c> 51</c><00:05:45.880><c> wind</c><00:05:46.160><c> speed</c><00:05:46.520><c> at</c><00:05:47.039><c> 100</c><00:05:47.520><c> so</c><00:05:47.759><c> there's</c>

00:05:48.029 --> 00:05:48.039 align:start position:0%
the ER 51 wind speed at 100 so there's
 

00:05:48.039 --> 00:05:51.469 align:start position:0%
the ER 51 wind speed at 100 so there's
no<00:05:48.280><c> power</c><00:05:48.560><c> low</c><00:05:48.919><c> applied</c><00:05:49.360><c> in</c><00:05:49.600><c> this</c><00:05:50.039><c> case</c><00:05:51.039><c> and</c>

00:05:51.469 --> 00:05:51.479 align:start position:0%
no power low applied in this case and
 

00:05:51.479 --> 00:05:54.430 align:start position:0%
no power low applied in this case and
the<00:05:51.639><c> adjustment</c><00:05:52.199><c> of</c><00:05:52.360><c> the</c><00:05:52.880><c> five</c><00:05:53.120><c> wind</c><00:05:53.400><c> speed</c><00:05:53.919><c> at</c>

00:05:54.430 --> 00:05:54.440 align:start position:0%
the adjustment of the five wind speed at
 

00:05:54.440 --> 00:05:56.990 align:start position:0%
the adjustment of the five wind speed at
100<00:05:54.680><c> m</c><00:05:55.000><c> was</c><00:05:55.160><c> done</c><00:05:55.360><c> in</c><00:05:55.479><c> the</c><00:05:55.680><c> same</c><00:05:55.960><c> way</c><00:05:56.360><c> as</c><00:05:56.639><c> the</c>

00:05:56.990 --> 00:05:57.000 align:start position:0%
100 m was done in the same way as the
 

00:05:57.000 --> 00:05:59.870 align:start position:0%
100 m was done in the same way as the
wind<00:05:57.280><c> speed</c><00:05:57.520><c> at</c><00:05:57.680><c> 10</c><00:05:57.919><c> m</c><00:05:58.440><c> using</c><00:05:58.960><c> the</c><00:05:59.199><c> global</c><00:05:59.639><c> with</c>

00:05:59.870 --> 00:05:59.880 align:start position:0%
wind speed at 10 m using the global with
 

00:05:59.880 --> 00:06:02.870 align:start position:0%
wind speed at 10 m using the global with
Atlas<00:06:00.400><c> which</c><00:06:00.680><c> provides</c><00:06:01.240><c> the</c><00:06:01.440><c> two</c><00:06:01.720><c> levels</c><00:06:02.600><c> the</c>

00:06:02.870 --> 00:06:02.880 align:start position:0%
Atlas which provides the two levels the
 

00:06:02.880 --> 00:06:08.510 align:start position:0%
Atlas which provides the two levels the
10<00:06:03.160><c> m</c><00:06:03.600><c> and</c><00:06:04.080><c> 100</c><00:06:04.880><c> m</c><00:06:05.880><c> following</c><00:06:06.639><c> the</c><00:06:07.639><c> changes</c><00:06:08.160><c> in</c>

00:06:08.510 --> 00:06:08.520 align:start position:0%
10 m and 100 m following the changes in
 

00:06:08.520 --> 00:06:11.629 align:start position:0%
10 m and 100 m following the changes in
historical<00:06:09.360><c> data</c><00:06:09.759><c> set</c><00:06:10.360><c> we</c><00:06:10.639><c> also</c><00:06:11.039><c> had</c><00:06:11.280><c> to</c>

00:06:11.629 --> 00:06:11.639 align:start position:0%
historical data set we also had to
 

00:06:11.639 --> 00:06:15.430 align:start position:0%
historical data set we also had to
update<00:06:12.440><c> the</c><00:06:12.960><c> projection</c><00:06:13.800><c> data</c><00:06:14.440><c> set</c>

00:06:15.430 --> 00:06:15.440 align:start position:0%
update the projection data set
 

00:06:15.440 --> 00:06:18.749 align:start position:0%
update the projection data set
specifically<00:06:16.360><c> we</c><00:06:16.599><c> had</c><00:06:16.880><c> to</c><00:06:17.680><c> rerun</c><00:06:18.360><c> the</c>

00:06:18.749 --> 00:06:18.759 align:start position:0%
specifically we had to rerun the
 

00:06:18.759 --> 00:06:20.430 align:start position:0%
specifically we had to rerun the
communative<00:06:19.400><c> distribution</c><00:06:20.039><c> function</c>

00:06:20.430 --> 00:06:20.440 align:start position:0%
communative distribution function
 

00:06:20.440 --> 00:06:22.670 align:start position:0%
communative distribution function
transfer<00:06:21.160><c> method</c><00:06:21.479><c> for</c><00:06:21.680><c> bias</c><00:06:22.039><c> adjustment</c>

00:06:22.670 --> 00:06:22.680 align:start position:0%
transfer method for bias adjustment
 

00:06:22.680 --> 00:06:25.670 align:start position:0%
transfer method for bias adjustment
using<00:06:23.120><c> now</c><00:06:23.400><c> the</c><00:06:23.639><c> new</c><00:06:24.240><c> reference</c><00:06:24.800><c> data</c><00:06:25.160><c> set</c>

00:06:25.670 --> 00:06:25.680 align:start position:0%
using now the new reference data set
 

00:06:25.680 --> 00:06:28.110 align:start position:0%
using now the new reference data set
adjusted<00:06:26.479><c> as</c><00:06:26.599><c> we</c><00:06:26.759><c> said</c><00:06:27.160><c> using</c><00:06:27.599><c> the</c><00:06:27.800><c> global</c>

00:06:28.110 --> 00:06:28.120 align:start position:0%
adjusted as we said using the global
 

00:06:28.120 --> 00:06:30.029 align:start position:0%
adjusted as we said using the global
wind<00:06:28.400><c> Atlas</c>

00:06:30.029 --> 00:06:30.039 align:start position:0%
wind Atlas
 

00:06:30.039 --> 00:06:33.270 align:start position:0%
wind Atlas
and<00:06:30.479><c> uh</c><00:06:30.680><c> also</c><00:06:31.360><c> we</c><00:06:31.720><c> changed</c><00:06:32.479><c> the</c><00:06:32.680><c> way</c><00:06:32.960><c> we</c>

00:06:33.270 --> 00:06:33.280 align:start position:0%
and uh also we changed the way we
 

00:06:33.280 --> 00:06:36.550 align:start position:0%
and uh also we changed the way we
calculate<00:06:33.919><c> the</c><00:06:34.120><c> wind</c><00:06:34.520><c> speed</c><00:06:34.880><c> at</c><00:06:35.319><c> 100</c><00:06:35.560><c> m</c><00:06:35.960><c> so</c><00:06:36.160><c> in</c>

00:06:36.550 --> 00:06:36.560 align:start position:0%
calculate the wind speed at 100 m so in
 

00:06:36.560 --> 00:06:39.790 align:start position:0%
calculate the wind speed at 100 m so in
4.1<00:06:37.560><c> we</c><00:06:37.720><c> use</c><00:06:38.120><c> Alpha</c><00:06:38.479><c> coefficient</c><00:06:39.360><c> computed</c>

00:06:39.790 --> 00:06:39.800 align:start position:0%
4.1 we use Alpha coefficient computed
 

00:06:39.800 --> 00:06:42.749 align:start position:0%
4.1 we use Alpha coefficient computed
from<00:06:40.000><c> the</c><00:06:40.160><c> original</c><00:06:40.680><c> wind</c><00:06:41.240><c> data</c><00:06:42.039><c> whereas</c><00:06:42.479><c> with</c>

00:06:42.749 --> 00:06:42.759 align:start position:0%
from the original wind data whereas with
 

00:06:42.759 --> 00:06:45.790 align:start position:0%
from the original wind data whereas with
4.2<00:06:43.759><c> we</c><00:06:43.880><c> use</c><00:06:44.400><c> Alpha</c><00:06:44.720><c> coefficient</c><00:06:45.319><c> computed</c>

00:06:45.790 --> 00:06:45.800 align:start position:0%
4.2 we use Alpha coefficient computed
 

00:06:45.800 --> 00:06:50.150 align:start position:0%
4.2 we use Alpha coefficient computed
from<00:06:46.000><c> the</c><00:06:46.199><c> bias</c><00:06:46.680><c> adjusted</c><00:06:47.479><c> wind</c><00:06:48.039><c> data</c><00:06:48.919><c> set</c><00:06:49.919><c> now</c>

00:06:50.150 --> 00:06:50.160 align:start position:0%
from the bias adjusted wind data set now
 

00:06:50.160 --> 00:06:53.309 align:start position:0%
from the bias adjusted wind data set now
we<00:06:50.360><c> look</c><00:06:50.599><c> at</c><00:06:50.919><c> some</c><00:06:51.759><c> of</c><00:06:52.080><c> the</c><00:06:52.319><c> key</c><00:06:52.560><c> differences</c>

00:06:53.309 --> 00:06:53.319 align:start position:0%
we look at some of the key differences
 

00:06:53.319 --> 00:06:55.230 align:start position:0%
we look at some of the key differences
in<00:06:53.599><c> the</c><00:06:53.759><c> wind</c><00:06:54.199><c> power</c>

00:06:55.230 --> 00:06:55.240 align:start position:0%
in the wind power
 

00:06:55.240 --> 00:06:58.510 align:start position:0%
in the wind power
modeling<00:06:56.240><c> between</c><00:06:56.800><c> again</c><00:06:57.360><c> version</c><00:06:57.680><c> 4.1</c><00:06:58.360><c> and</c>

00:06:58.510 --> 00:06:58.520 align:start position:0%
modeling between again version 4.1 and
 

00:06:58.520 --> 00:07:01.469 align:start position:0%
modeling between again version 4.1 and
version<00:06:58.800><c> 4.2</c><00:06:59.919><c> so</c><00:07:00.199><c> there</c><00:07:00.319><c> are</c><00:07:00.520><c> some</c><00:07:00.919><c> improved</c>

00:07:01.469 --> 00:07:01.479 align:start position:0%
version 4.2 so there are some improved
 

00:07:01.479 --> 00:07:05.550 align:start position:0%
version 4.2 so there are some improved
validation<00:07:02.680><c> metrics</c><00:07:03.680><c> in</c><00:07:04.280><c> 4.2</c><00:07:05.280><c> which</c>

00:07:05.550 --> 00:07:05.560 align:start position:0%
validation metrics in 4.2 which
 

00:07:05.560 --> 00:07:08.070 align:start position:0%
validation metrics in 4.2 which
demonstrate<00:07:06.440><c> improved</c><00:07:07.000><c> capacity</c><00:07:07.560><c> Factor</c>

00:07:08.070 --> 00:07:08.080 align:start position:0%
demonstrate improved capacity Factor
 

00:07:08.080 --> 00:07:10.670 align:start position:0%
demonstrate improved capacity Factor
through<00:07:08.479><c> this</c><00:07:08.960><c> metrics</c><00:07:09.759><c> like</c><00:07:10.199><c> the</c><00:07:10.319><c> earth</c>

00:07:10.670 --> 00:07:10.680 align:start position:0%
through this metrics like the earth
 

00:07:10.680 --> 00:07:12.830 align:start position:0%
through this metrics like the earth
mover<00:07:11.199><c> distance</c><00:07:11.800><c> and</c>

00:07:12.830 --> 00:07:12.840 align:start position:0%
mover distance and
 

00:07:12.840 --> 00:07:16.550 align:start position:0%
mover distance and
correlation<00:07:13.840><c> there</c><00:07:13.960><c> is</c><00:07:14.680><c> enhanced</c><00:07:15.560><c> accuracy</c>

00:07:16.550 --> 00:07:16.560 align:start position:0%
correlation there is enhanced accuracy
 

00:07:16.560 --> 00:07:18.790 align:start position:0%
correlation there is enhanced accuracy
which<00:07:16.960><c> results</c><00:07:17.360><c> in</c><00:07:17.479><c> a</c><00:07:17.639><c> better</c><00:07:17.960><c> alignment</c><00:07:18.599><c> of</c>

00:07:18.790 --> 00:07:18.800 align:start position:0%
which results in a better alignment of
 

00:07:18.800 --> 00:07:21.270 align:start position:0%
which results in a better alignment of
simulated<00:07:19.400><c> generation</c><00:07:20.000><c> distributions</c><00:07:20.919><c> with</c>

00:07:21.270 --> 00:07:21.280 align:start position:0%
simulated generation distributions with
 

00:07:21.280 --> 00:07:23.390 align:start position:0%
simulated generation distributions with
measure<00:07:21.759><c> data</c><00:07:22.160><c> for</c><00:07:22.440><c> most</c><00:07:22.800><c> countries</c>

00:07:23.390 --> 00:07:23.400 align:start position:0%
measure data for most countries
 

00:07:23.400 --> 00:07:26.510 align:start position:0%
measure data for most countries
especially<00:07:23.960><c> in</c><00:07:24.199><c> onshore</c><00:07:24.680><c> Wind</c><00:07:25.319><c> regions</c><00:07:26.319><c> and</c>

00:07:26.510 --> 00:07:26.520 align:start position:0%
especially in onshore Wind regions and
 

00:07:26.520 --> 00:07:29.830 align:start position:0%
especially in onshore Wind regions and
the<00:07:26.759><c> data</c><00:07:27.120><c> handling</c><00:07:27.840><c> was</c><00:07:28.360><c> refined</c><00:07:29.240><c> and</c><00:07:29.639><c> we</c>

00:07:29.830 --> 00:07:29.840 align:start position:0%
the data handling was refined and we
 

00:07:29.840 --> 00:07:33.029 align:start position:0%
the data handling was refined and we
included<00:07:30.720><c> also</c><00:07:31.120><c> cment</c><00:07:31.919><c> effects</c><00:07:32.360><c> in</c><00:07:32.560><c> measure</c>

00:07:33.029 --> 00:07:33.039 align:start position:0%
included also cment effects in measure
 

00:07:33.039 --> 00:07:36.390 align:start position:0%
included also cment effects in measure
data<00:07:33.759><c> for</c><00:07:34.039><c> several</c><00:07:34.440><c> regions</c><00:07:35.280><c> like</c><00:07:35.479><c> in</c><00:07:35.680><c> France</c>

00:07:36.390 --> 00:07:36.400 align:start position:0%
data for several regions like in France
 

00:07:36.400 --> 00:07:39.589 align:start position:0%
data for several regions like in France
Hungary<00:07:37.080><c> Spain</c><00:07:37.599><c> and</c><00:07:37.960><c> Italy</c><00:07:38.960><c> especially</c>

00:07:39.589 --> 00:07:39.599 align:start position:0%
Hungary Spain and Italy especially
 

00:07:39.599 --> 00:07:43.629 align:start position:0%
Hungary Spain and Italy especially
because<00:07:40.120><c> for</c><00:07:40.360><c> this</c><00:07:40.520><c> region</c><00:07:41.199><c> we</c><00:07:41.520><c> had</c><00:07:42.000><c> access</c><00:07:42.639><c> to</c>

00:07:43.629 --> 00:07:43.639 align:start position:0%
because for this region we had access to
 

00:07:43.639 --> 00:07:46.189 align:start position:0%
because for this region we had access to
additional<00:07:44.240><c> data</c><00:07:44.639><c> sets</c><00:07:45.159><c> other</c><00:07:45.520><c> then</c><00:07:45.879><c> the</c>

00:07:46.189 --> 00:07:46.199 align:start position:0%
additional data sets other then the
 

00:07:46.199 --> 00:07:49.670 align:start position:0%
additional data sets other then the
transparency<00:07:47.400><c> portal</c><00:07:48.400><c> and</c><00:07:48.800><c> also</c><00:07:49.080><c> we</c><00:07:49.240><c> use</c><00:07:49.520><c> the</c>

00:07:49.670 --> 00:07:49.680 align:start position:0%
transparency portal and also we use the
 

00:07:49.680 --> 00:07:51.550 align:start position:0%
transparency portal and also we use the
standardized<00:07:50.319><c> methods</c><00:07:50.759><c> for</c><00:07:51.039><c> row</c><00:07:51.360><c> and</c>

00:07:51.550 --> 00:07:51.560 align:start position:0%
standardized methods for row and
 

00:07:51.560 --> 00:07:54.390 align:start position:0%
standardized methods for row and
corrected<00:07:52.080><c> generation</c><00:07:52.599><c> data</c><00:07:53.400><c> comparison</c>

00:07:54.390 --> 00:07:54.400 align:start position:0%
corrected generation data comparison
 

00:07:54.400 --> 00:07:55.950 align:start position:0%
corrected generation data comparison
there<00:07:54.560><c> were</c><00:07:54.800><c> some</c><00:07:55.000><c> region</c><00:07:55.440><c> specific</c>

00:07:55.950 --> 00:07:55.960 align:start position:0%
there were some region specific
 

00:07:55.960 --> 00:07:59.149 align:start position:0%
there were some region specific
improvements<00:07:56.919><c> uh</c><00:07:57.240><c> again</c><00:07:57.599><c> based</c><00:07:58.039><c> on</c><00:07:58.479><c> the</c><00:07:59.039><c> uh</c>

00:07:59.149 --> 00:07:59.159 align:start position:0%
improvements uh again based on the uh
 

00:07:59.159 --> 00:08:02.550 align:start position:0%
improvements uh again based on the uh
data<00:07:59.800><c> set</c><00:08:00.039><c> that</c><00:08:00.240><c> we</c><00:08:00.400><c> were</c><00:08:00.720><c> able</c><00:08:01.120><c> to</c><00:08:01.879><c> uh</c><00:08:02.000><c> receive</c>

00:08:02.550 --> 00:08:02.560 align:start position:0%
data set that we were able to uh receive
 

00:08:02.560 --> 00:08:05.869 align:start position:0%
data set that we were able to uh receive
from<00:08:02.840><c> Individual</c><00:08:03.560><c> countries</c><00:08:04.560><c> in</c><00:08:05.319><c> onshore</c>

00:08:05.869 --> 00:08:05.879 align:start position:0%
from Individual countries in onshore
 

00:08:05.879 --> 00:08:08.710 align:start position:0%
from Individual countries in onshore
wind<00:08:06.360><c> for</c><00:08:06.599><c> example</c><00:08:07.120><c> we</c><00:08:07.280><c> had</c><00:08:07.599><c> announcements</c><00:08:08.319><c> in</c>

00:08:08.710 --> 00:08:08.720 align:start position:0%
wind for example we had announcements in
 

00:08:08.720 --> 00:08:10.950 align:start position:0%
wind for example we had announcements in
validation<00:08:09.360><c> from</c><00:08:09.840><c> Austria</c><00:08:10.319><c> Spain</c><00:08:10.680><c> and</c>

00:08:10.950 --> 00:08:10.960 align:start position:0%
validation from Austria Spain and
 

00:08:10.960 --> 00:08:13.950 align:start position:0%
validation from Austria Spain and
Hungary<00:08:11.960><c> and</c><00:08:12.360><c> also</c><00:08:12.919><c> slight</c><00:08:13.400><c> correlation</c>

00:08:13.950 --> 00:08:13.960 align:start position:0%
Hungary and also slight correlation
 

00:08:13.960 --> 00:08:16.270 align:start position:0%
Hungary and also slight correlation
improvements<00:08:14.520><c> in</c><00:08:14.720><c> Denmark</c><00:08:15.199><c> Finland</c><00:08:15.720><c> and</c>

00:08:16.270 --> 00:08:16.280 align:start position:0%
improvements in Denmark Finland and
 

00:08:16.280 --> 00:08:19.430 align:start position:0%
improvements in Denmark Finland and
Norway<00:08:17.280><c> despite</c><00:08:17.680><c> the</c><00:08:17.800><c> measure</c><00:08:18.199><c> data</c>

00:08:19.430 --> 00:08:19.440 align:start position:0%
Norway despite the measure data
 

00:08:19.440 --> 00:08:21.710 align:start position:0%
Norway despite the measure data
uncertainties<00:08:20.440><c> also</c><00:08:20.720><c> offshore</c><00:08:21.159><c> wind</c><00:08:21.520><c> was</c>

00:08:21.710 --> 00:08:21.720 align:start position:0%
uncertainties also offshore wind was
 

00:08:21.720 --> 00:08:25.110 align:start position:0%
uncertainties also offshore wind was
marginally<00:08:22.280><c> improved</c><00:08:22.759><c> for</c><00:08:23.199><c> Germany</c><00:08:23.879><c> and</c><00:08:24.400><c> uh</c>

00:08:25.110 --> 00:08:25.120 align:start position:0%
marginally improved for Germany and uh
 

00:08:25.120 --> 00:08:27.990 align:start position:0%
marginally improved for Germany and uh
Belgium<00:08:25.919><c> with</c><00:08:26.360><c> uh</c><00:08:27.039><c> however</c><00:08:27.280><c> mixed</c><00:08:27.639><c> results</c>

00:08:27.990 --> 00:08:28.000 align:start position:0%
Belgium with uh however mixed results
 

00:08:28.000 --> 00:08:31.029 align:start position:0%
Belgium with uh however mixed results
for<00:08:28.280><c> Denmark</c><00:08:28.800><c> due</c><00:08:29.000><c> to</c><00:08:29.199><c> dat</c>

00:08:31.029 --> 00:08:31.039 align:start position:0%
for Denmark due to dat
 

00:08:31.039 --> 00:08:33.550 align:start position:0%
for Denmark due to dat
inconsistencies<00:08:32.039><c> and</c><00:08:32.240><c> then</c><00:08:32.719><c> uh</c><00:08:32.959><c> finally</c><00:08:33.399><c> we</c>

00:08:33.550 --> 00:08:33.560 align:start position:0%
inconsistencies and then uh finally we
 

00:08:33.560 --> 00:08:38.149 align:start position:0%
inconsistencies and then uh finally we
show<00:08:33.959><c> some</c><00:08:34.279><c> of</c><00:08:34.599><c> the</c><00:08:35.039><c> updates</c><00:08:35.919><c> to</c><00:08:36.240><c> the</c><00:08:36.519><c> solar</c><00:08:37.519><c> PV</c>

00:08:38.149 --> 00:08:38.159 align:start position:0%
show some of the updates to the solar PV
 

00:08:38.159 --> 00:08:41.949 align:start position:0%
show some of the updates to the solar PV
modeling<00:08:39.159><c> one</c><00:08:39.519><c> major</c><00:08:39.959><c> update</c><00:08:40.760><c> was</c><00:08:41.240><c> that</c><00:08:41.680><c> we</c>

00:08:41.949 --> 00:08:41.959 align:start position:0%
modeling one major update was that we
 

00:08:41.959 --> 00:08:46.870 align:start position:0%
modeling one major update was that we
started<00:08:42.959><c> to</c><00:08:43.959><c> model</c><00:08:44.720><c> different</c><00:08:45.240><c> types</c><00:08:45.800><c> of</c><00:08:46.040><c> PV</c>

00:08:46.870 --> 00:08:46.880 align:start position:0%
started to model different types of PV
 

00:08:46.880 --> 00:08:50.750 align:start position:0%
started to model different types of PV
so<00:08:47.279><c> from</c><00:08:48.080><c> a</c><00:08:48.279><c> generic</c><00:08:48.760><c> PV</c><00:08:49.240><c> in</c><00:08:49.480><c> version</c><00:08:49.839><c> 4.1</c>

00:08:50.750 --> 00:08:50.760 align:start position:0%
so from a generic PV in version 4.1
 

00:08:50.760 --> 00:08:53.670 align:start position:0%
so from a generic PV in version 4.1
there<00:08:50.920><c> was</c><00:08:51.120><c> just</c><00:08:51.320><c> one</c><00:08:51.800><c> typology</c><00:08:52.760><c> now</c><00:08:52.959><c> we</c><00:08:53.120><c> have</c>

00:08:53.670 --> 00:08:53.680 align:start position:0%
there was just one typology now we have
 

00:08:53.680 --> 00:08:57.110 align:start position:0%
there was just one typology now we have
four<00:08:54.680><c> the</c><00:08:54.920><c> four</c><00:08:55.279><c> typologies</c><00:08:55.959><c> are</c><00:08:56.480><c> rooftop</c>

00:08:57.110 --> 00:08:57.120 align:start position:0%
four the four typologies are rooftop
 

00:08:57.120 --> 00:09:00.030 align:start position:0%
four the four typologies are rooftop
residential<00:08:57.839><c> rooftop</c><00:08:58.480><c> Industrial</c>

00:09:00.030 --> 00:09:00.040 align:start position:0%
residential rooftop Industrial
 

00:09:00.040 --> 00:09:03.670 align:start position:0%
residential rooftop Industrial
utility<00:09:00.600><c> scale</c><00:09:01.079><c> fixed</c><00:09:02.040><c> axis</c><00:09:02.800><c> and</c><00:09:03.079><c> utility</c>

00:09:03.670 --> 00:09:03.680 align:start position:0%
utility scale fixed axis and utility
 

00:09:03.680 --> 00:09:05.910 align:start position:0%
utility scale fixed axis and utility
scale<00:09:04.240><c> with</c><00:09:04.519><c> one</c><00:09:04.839><c> AIS</c>

00:09:05.910 --> 00:09:05.920 align:start position:0%
scale with one AIS
 

00:09:05.920 --> 00:09:08.710 align:start position:0%
scale with one AIS
tracking<00:09:06.920><c> each</c><00:09:07.320><c> uh</c><00:09:07.959><c> typology</c><00:09:08.519><c> is</c>

00:09:08.710 --> 00:09:08.720 align:start position:0%
tracking each uh typology is
 

00:09:08.720 --> 00:09:11.670 align:start position:0%
tracking each uh typology is
characterized<00:09:09.600><c> by</c><00:09:10.040><c> particular</c><00:09:10.600><c> daily</c><00:09:11.480><c> and</c>

00:09:11.670 --> 00:09:11.680 align:start position:0%
characterized by particular daily and
 

00:09:11.680 --> 00:09:14.470 align:start position:0%
characterized by particular daily and
annual<00:09:12.240><c> seasonality</c><00:09:13.240><c> and</c><00:09:13.560><c> over</c><00:09:13.920><c> planting</c>

00:09:14.470 --> 00:09:14.480 align:start position:0%
annual seasonality and over planting
 

00:09:14.480 --> 00:09:17.310 align:start position:0%
annual seasonality and over planting
factor<00:09:15.160><c> which</c><00:09:15.600><c> can</c><00:09:15.800><c> be</c><00:09:16.200><c> user</c>

00:09:17.310 --> 00:09:17.320 align:start position:0%
factor which can be user
 

00:09:17.320 --> 00:09:21.550 align:start position:0%
factor which can be user
defined<00:09:18.320><c> this</c><00:09:18.920><c> uh</c><00:09:19.160><c> updates</c><00:09:20.079><c> have</c><00:09:20.320><c> led</c><00:09:20.760><c> to</c>

00:09:21.550 --> 00:09:21.560 align:start position:0%
defined this uh updates have led to
 

00:09:21.560 --> 00:09:24.710 align:start position:0%
defined this uh updates have led to
overall<00:09:22.079><c> more</c><00:09:22.440><c> accurate</c><00:09:22.959><c> results</c><00:09:23.720><c> as</c><00:09:24.120><c> tested</c>

00:09:24.710 --> 00:09:24.720 align:start position:0%
overall more accurate results as tested
 

00:09:24.720 --> 00:09:27.750 align:start position:0%
overall more accurate results as tested
over<00:09:25.079><c> the</c><00:09:25.399><c> historical</c><00:09:26.320><c> period</c><00:09:27.160><c> this</c><00:09:27.399><c> are</c>

00:09:27.750 --> 00:09:27.760 align:start position:0%
over the historical period this are
 

00:09:27.760 --> 00:09:31.550 align:start position:0%
over the historical period this are
based<00:09:28.120><c> on</c><00:09:28.519><c> averages</c><00:09:29.440><c> over</c><00:09:30.079><c> countries</c><00:09:30.519><c> or</c><00:09:30.800><c> PCD</c>

00:09:31.550 --> 00:09:31.560 align:start position:0%
based on averages over countries or PCD
 

00:09:31.560 --> 00:09:36.949 align:start position:0%
based on averages over countries or PCD
zones<00:09:32.560><c> not</c><00:09:32.880><c> at</c><00:09:33.079><c> the</c><00:09:33.480><c> individual</c><00:09:34.480><c> uh</c><00:09:35.320><c> PV</c><00:09:36.320><c> Farm</c>

00:09:36.949 --> 00:09:36.959 align:start position:0%
zones not at the individual uh PV Farm
 

00:09:36.959 --> 00:09:41.630 align:start position:0%
zones not at the individual uh PV Farm
or<00:09:37.920><c> um</c><00:09:38.200><c> system</c><00:09:39.160><c> and</c><00:09:39.720><c> uh</c><00:09:40.000><c> there</c><00:09:40.320><c> also</c><00:09:41.160><c> with</c><00:09:41.360><c> this</c>

00:09:41.630 --> 00:09:41.640 align:start position:0%
or um system and uh there also with this
 

00:09:41.640 --> 00:09:44.150 align:start position:0%
or um system and uh there also with this
approach<00:09:42.079><c> more</c><00:09:42.360><c> flexible</c><00:09:43.040><c> options</c><00:09:43.600><c> to</c><00:09:43.800><c> test</c>

00:09:44.150 --> 00:09:44.160 align:start position:0%
approach more flexible options to test
 

00:09:44.160 --> 00:09:47.030 align:start position:0%
approach more flexible options to test
different<00:09:44.519><c> PV</c><00:09:45.000><c> scenarios</c><00:09:46.000><c> because</c><00:09:46.440><c> we</c><00:09:46.560><c> can</c>

00:09:47.030 --> 00:09:47.040 align:start position:0%
different PV scenarios because we can
 

00:09:47.040 --> 00:09:50.670 align:start position:0%
different PV scenarios because we can
combine<00:09:48.040><c> the</c><00:09:48.440><c> typologies</c><00:09:49.360><c> in</c><00:09:49.560><c> a</c><00:09:50.360><c> very</c>

00:09:50.670 --> 00:09:50.680 align:start position:0%
combine the typologies in a very
 

00:09:50.680 --> 00:09:54.550 align:start position:0%
combine the typologies in a very
flexible<00:09:51.360><c> way</c><00:09:52.000><c> so</c><00:09:52.240><c> this</c><00:09:52.440><c> concludes</c><00:09:53.200><c> the</c><00:09:54.079><c> short</c>

00:09:54.550 --> 00:09:54.560 align:start position:0%
flexible way so this concludes the short
 

00:09:54.560 --> 00:09:56.630 align:start position:0%
flexible way so this concludes the short
description<00:09:55.200><c> of</c><00:09:55.360><c> the</c><00:09:55.519><c> differences</c><00:09:56.079><c> between</c>

00:09:56.630 --> 00:09:56.640 align:start position:0%
description of the differences between
 

00:09:56.640 --> 00:10:00.350 align:start position:0%
description of the differences between
4.1<00:09:57.640><c> and</c><00:09:57.839><c> 4.2</c><00:09:58.600><c> of</c><00:09:58.760><c> the</c><00:09:58.959><c> PC</c>

00:10:00.350 --> 00:10:00.360 align:start position:0%
4.1 and 4.2 of the PC
 

00:10:00.360 --> 00:10:04.160 align:start position:0%
4.1 and 4.2 of the PC
thank<00:10:00.519><c> you</c><00:10:00.680><c> for</c><00:10:00.920><c> your</c><00:10:01.160><c> attention</c>

