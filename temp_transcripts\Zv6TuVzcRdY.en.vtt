WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:02.030 align:start position:0%
 
ideas<00:00:00.680><c> can</c><00:00:00.880><c> happen</c><00:00:01.160><c> anywhere</c><00:00:01.719><c> whether</c><00:00:01.880><c> you're</c>

00:00:02.030 --> 00:00:02.040 align:start position:0%
ideas can happen anywhere whether you're
 

00:00:02.040 --> 00:00:04.230 align:start position:0%
ideas can happen anywhere whether you're
at<00:00:02.200><c> your</c><00:00:02.399><c> desk</c><00:00:02.679><c> or</c><00:00:02.840><c> on</c><00:00:02.960><c> the</c><00:00:03.159><c> go</c><00:00:03.719><c> and</c><00:00:03.879><c> that's</c><00:00:04.080><c> why</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
at your desk or on the go and that's why
 

00:00:04.240 --> 00:00:06.110 align:start position:0%
at your desk or on the go and that's why
co-pilot<00:00:04.759><c> workspace</c><00:00:05.240><c> is</c><00:00:05.480><c> accessible</c><00:00:05.960><c> from</c>

00:00:06.110 --> 00:00:06.120 align:start position:0%
co-pilot workspace is accessible from
 

00:00:06.120 --> 00:00:08.910 align:start position:0%
co-pilot workspace is accessible from
the<00:00:06.279><c> GitHub</c><00:00:06.720><c> mobile</c><00:00:07.160><c> app</c><00:00:08.160><c> it</c><00:00:08.360><c> starts</c><00:00:08.760><c> by</c>

00:00:08.910 --> 00:00:08.920 align:start position:0%
the GitHub mobile app it starts by
 

00:00:08.920 --> 00:00:11.470 align:start position:0%
the GitHub mobile app it starts by
defining<00:00:09.320><c> your</c><00:00:09.559><c> task</c><00:00:10.120><c> which</c><00:00:10.480><c> you</c><00:00:10.599><c> could</c><00:00:10.840><c> type</c>

00:00:11.470 --> 00:00:11.480 align:start position:0%
defining your task which you could type
 

00:00:11.480 --> 00:00:14.470 align:start position:0%
defining your task which you could type
or<00:00:11.719><c> use</c><00:00:12.000><c> your</c><00:00:12.240><c> devic's</c><00:00:12.799><c> built-in</c><00:00:13.440><c> dictation</c>

00:00:14.470 --> 00:00:14.480 align:start position:0%
or use your devic's built-in dictation
 

00:00:14.480 --> 00:00:16.470 align:start position:0%
or use your devic's built-in dictation
capabilities<00:00:15.480><c> you</c><00:00:15.599><c> can</c><00:00:15.799><c> then</c><00:00:16.000><c> review</c><00:00:16.320><c> the</c>

00:00:16.470 --> 00:00:16.480 align:start position:0%
capabilities you can then review the
 

00:00:16.480 --> 00:00:19.830 align:start position:0%
capabilities you can then review the
generated<00:00:17.080><c> specification</c><00:00:18.080><c> and</c><00:00:18.400><c> add</c><00:00:19.000><c> edit</c><00:00:19.600><c> or</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
generated specification and add edit or
 

00:00:19.840 --> 00:00:22.269 align:start position:0%
generated specification and add edit or
remove<00:00:20.199><c> items</c><00:00:20.600><c> as</c><00:00:20.840><c> needed</c><00:00:21.840><c> once</c><00:00:22.039><c> you're</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
remove items as needed once you're
 

00:00:22.279 --> 00:00:24.429 align:start position:0%
remove items as needed once you're
satisfied<00:00:23.160><c> continue</c><00:00:23.560><c> on</c><00:00:23.720><c> to</c><00:00:24.000><c> review</c><00:00:24.279><c> the</c>

00:00:24.429 --> 00:00:24.439 align:start position:0%
satisfied continue on to review the
 

00:00:24.439 --> 00:00:26.990 align:start position:0%
satisfied continue on to review the
proposed<00:00:25.199><c> plan</c><00:00:26.199><c> and</c><00:00:26.400><c> once</c><00:00:26.560><c> you've</c><00:00:26.760><c> had</c><00:00:26.840><c> a</c>

00:00:26.990 --> 00:00:27.000 align:start position:0%
proposed plan and once you've had a
 

00:00:27.000 --> 00:00:29.390 align:start position:0%
proposed plan and once you've had a
chance<00:00:27.199><c> to</c><00:00:27.359><c> review</c><00:00:27.640><c> the</c><00:00:27.760><c> plan</c><00:00:28.519><c> move</c><00:00:28.760><c> on</c><00:00:29.119><c> to</c><00:00:29.279><c> the</c>

00:00:29.390 --> 00:00:29.400 align:start position:0%
chance to review the plan move on to the
 

00:00:29.400 --> 00:00:30.790 align:start position:0%
chance to review the plan move on to the
implementation

00:00:30.790 --> 00:00:30.800 align:start position:0%
implementation
 

00:00:30.800 --> 00:00:32.630 align:start position:0%
implementation
you<00:00:30.920><c> can</c><00:00:31.119><c> manually</c><00:00:31.599><c> review</c><00:00:31.920><c> the</c><00:00:32.040><c> code</c><00:00:32.279><c> changes</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
you can manually review the code changes
 

00:00:32.640 --> 00:00:35.110 align:start position:0%
you can manually review the code changes
using<00:00:32.880><c> the</c><00:00:33.040><c> diff</c><00:00:33.280><c> View</c><00:00:33.879><c> and</c><00:00:34.079><c> make</c><00:00:34.280><c> edits</c><00:00:34.760><c> as</c>

00:00:35.110 --> 00:00:35.120 align:start position:0%
using the diff View and make edits as
 

00:00:35.120 --> 00:00:37.630 align:start position:0%
using the diff View and make edits as
needed<00:00:36.120><c> and</c><00:00:36.600><c> you</c><00:00:36.719><c> can</c><00:00:36.920><c> leverage</c><00:00:37.280><c> the</c><00:00:37.440><c> buil-in</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
needed and you can leverage the buil-in
 

00:00:37.640 --> 00:00:40.549 align:start position:0%
needed and you can leverage the buil-in
terminal<00:00:38.640><c> to</c><00:00:38.840><c> run</c><00:00:39.040><c> your</c><00:00:39.200><c> unit</c><00:00:39.520><c> tests</c><00:00:40.280><c> so</c><00:00:40.440><c> you</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
terminal to run your unit tests so you
 

00:00:40.559 --> 00:00:43.190 align:start position:0%
terminal to run your unit tests so you
can<00:00:40.719><c> check</c><00:00:41.079><c> that</c><00:00:41.239><c> the</c><00:00:41.360><c> code</c><00:00:41.559><c> is</c><00:00:41.760><c> performing</c><00:00:42.680><c> as</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
can check that the code is performing as
 

00:00:43.200 --> 00:00:45.110 align:start position:0%
can check that the code is performing as
expected<00:00:44.200><c> once</c><00:00:44.360><c> you're</c><00:00:44.600><c> happy</c><00:00:44.879><c> with</c><00:00:45.000><c> the</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
expected once you're happy with the
 

00:00:45.120 --> 00:00:47.350 align:start position:0%
expected once you're happy with the
proposed<00:00:45.480><c> changes</c><00:00:46.239><c> you</c><00:00:46.360><c> can</c><00:00:46.559><c> choose</c><00:00:47.079><c> how</c><00:00:47.239><c> you</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
proposed changes you can choose how you
 

00:00:47.360 --> 00:00:50.069 align:start position:0%
proposed changes you can choose how you
want<00:00:47.480><c> to</c><00:00:47.680><c> integrate</c><00:00:48.120><c> them</c><00:00:48.960><c> for</c><00:00:49.199><c> now</c><00:00:49.760><c> we'll</c>

00:00:50.069 --> 00:00:50.079 align:start position:0%
want to integrate them for now we'll
 

00:00:50.079 --> 00:00:52.069 align:start position:0%
want to integrate them for now we'll
create<00:00:50.320><c> a</c><00:00:50.399><c> pull</c><00:00:50.680><c> request</c><00:00:51.399><c> and</c><00:00:51.600><c> co-pilot</c>

00:00:52.069 --> 00:00:52.079 align:start position:0%
create a pull request and co-pilot
 

00:00:52.079 --> 00:00:53.790 align:start position:0%
create a pull request and co-pilot
workspace<00:00:52.640><c> will</c><00:00:52.879><c> generate</c><00:00:53.199><c> a</c><00:00:53.399><c> description</c>

00:00:53.790 --> 00:00:53.800 align:start position:0%
workspace will generate a description
 

00:00:53.800 --> 00:00:54.630 align:start position:0%
workspace will generate a description
for

00:00:54.630 --> 00:00:54.640 align:start position:0%
for
 

00:00:54.640 --> 00:00:57.349 align:start position:0%
for
us<00:00:55.640><c> let's</c><00:00:55.879><c> get</c><00:00:56.039><c> that</c>

00:00:57.349 --> 00:00:57.359 align:start position:0%
us let's get that
 

00:00:57.359 --> 00:00:59.349 align:start position:0%
us let's get that
created<00:00:58.359><c> after</c><00:00:58.559><c> switching</c><00:00:58.960><c> back</c><00:00:59.079><c> to</c><00:00:59.199><c> the</c>

00:00:59.349 --> 00:00:59.359 align:start position:0%
created after switching back to the
 

00:00:59.359 --> 00:01:01.670 align:start position:0%
created after switching back to the
mobile<00:00:59.680><c> app</c><00:01:00.239><c> you'll</c><00:01:00.559><c> be</c><00:01:00.719><c> able</c><00:01:00.920><c> to</c><00:01:01.120><c> find</c><00:01:01.480><c> your</c>

00:01:01.670 --> 00:01:01.680 align:start position:0%
mobile app you'll be able to find your
 

00:01:01.680 --> 00:01:03.310 align:start position:0%
mobile app you'll be able to find your
new<00:01:01.920><c> pole</c><00:01:02.160><c> request</c><00:01:02.480><c> is</c>

00:01:03.310 --> 00:01:03.320 align:start position:0%
new pole request is
 

00:01:03.320 --> 00:01:06.030 align:start position:0%
new pole request is
available<00:01:04.320><c> exploring</c><00:01:04.799><c> ideas</c><00:01:05.239><c> on</c><00:01:05.400><c> the</c><00:01:05.600><c> go</c><00:01:05.840><c> has</c>

00:01:06.030 --> 00:01:06.040 align:start position:0%
available exploring ideas on the go has
 

00:01:06.040 --> 00:01:08.630 align:start position:0%
available exploring ideas on the go has
never<00:01:06.320><c> been</c><00:01:06.520><c> easier</c><00:01:07.400><c> thanks</c><00:01:07.960><c> to</c><00:01:08.119><c> co-pilot</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
never been easier thanks to co-pilot
 

00:01:08.640 --> 00:01:11.220 align:start position:0%
never been easier thanks to co-pilot
workspace

00:01:11.220 --> 00:01:11.230 align:start position:0%
workspace
 

00:01:11.230 --> 00:01:14.439 align:start position:0%
workspace
[Music]

