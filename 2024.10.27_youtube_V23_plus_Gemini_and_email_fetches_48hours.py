import os
import logging
import csv
from dotenv import load_dotenv
from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import yt_dlp
from tqdm import tqdm
from datetime import datetime, timedelta, timezone
import uuid
from youtube_transcript_api import YouTubeTranscriptApi
import urllib3
import pytz
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import sessionmaker
from rich import print as rprint
from rich.console import Console
from rich.table import Table as RichTable
import warnings
import webvtt
import re
from io import StringIO
import nltk
from nltk.tokenize import sent_tokenize
import time
import sys
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential
import ssl
import certifi
from httpx import Timeout
import asyncio
from flask import Flask, jsonify, request
import subprocess
import asyncio
import sys
from functools import wraps

CHOSEN_WATCHLIST_FILE = "youtube_channels_Watchlist_download_test.md"   #"youtube_channels_Watchlist_download_entire_channels.md"  #youtube_channels_Watchlist_last_48hours_videos.md

# Create Flask application instance
app = Flask(__name__)

# Add a basic route to test if the server is running
@app.route('/')
def home():
    return "YouTube Video Processor is running!"

@app.route('/status')
def status():
    return jsonify({
        "status": "running",
        "last_run": datetime.now().isoformat()
    })

@app.route('/run-update', methods=['POST'])
def run_update():
    asyncio.run(main())
    asyncio.run(run_additional_scripts())
    return jsonify({"status": "update completed"})

# Your existing code starts here
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Ensure NLTK sentence tokenizer is downloaded
nltk.download('punkt', quiet=True)

#option here to run without API
USE_YOUTUBE_API = os.getenv('USE_YOUTUBE_API', 'true').lower() == 'false'

"""
This script is designed to fetch youtube videos THAT ARE NOT YET IN THE DATABASE, and save their transcripts and metadata to a Supabase database.
It reads the markdown file "youtube_channels_Watchlist.md" that defines the channels to fetch and a Supabase database to store the metadata.
# Metadata collection explanation

This script collects metadata for YouTube videos using the following process:

1. Channel data is read from a markdown file ("youtube_channels_Watchlist.md") using the `read_channel_data` function.
   - The file contains topics and associated YouTube channel names and IDs.

2. The script uses yt-dlp (a fork of youtube-dl) to fetch video information from each channel.
   - This is likely done in a function not shown in the current file snippet.

3. For each video, the script collects the following metadata:
   - Video ID
   - Channel name
   - Video title
   - Published date
   - Duration
   - Transcript (using YouTubeTranscriptApi)

4. The collected metadata is then saved to a Supabase database.
   - The database has separate tables for different topics (e.g., "youtube_renewable_energy", "youtube_artificial_intelligence", etc.)

5. The script checks for existing videos in the database to avoid duplicates.
   - This is done by comparing the video IDs of newly fetched videos with those already in the database.

6. New videos (those not yet in the database) have their metadata and transcripts saved to the appropriate table in the Supabase database.

The script uses environment variables for Supabase credentials and employs logging for tracking the process and any errors that occur during execution.


"""

# Load environment variables and set up logging
load_dotenv()

# Configure logging to output only to the log file, preventing duplicate messages in the console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler("script.log")]
)

# Suppress HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

# Constants
MAX_VIDEOS = -1  # Set to -1 to fetch all videos, or any positive number to limit

# Comment out these lines to use all tables from the .md file
# TABLES_TO_CHECK = [
#     "youtube_gme",
# ]

# New retry decorator for Supabase API calls

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def supabase_api_call(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Supabase API Error: {str(e)}")
            raise
    return wrapper

# Move the Supabase client creation into an async function
async def create_supabase_client():
    url: str = os.getenv('SUPABASE_URL')
    key: str = os.getenv('SERVICE_ROLE_KEY')

    ssl_context = ssl.create_default_context(cafile=certifi.where())
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    conn = aiohttp.TCPConnector(limit=100, force_close=True, enable_cleanup_closed=True, ssl=ssl_context)
    session = aiohttp.ClientSession(connector=conn)

    options = ClientOptions(
        schema="public",
        headers={},
        postgrest_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
        storage_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
    )

    return create_client(url, key, options=options), session

# Global variable for Supabase client
supabase = None

def read_channel_data(file_name):
    file_path = os.path.join(os.getcwd(), file_name)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_name} does not exist in the current directory.")

    with open(file_path, 'r') as file:
        lines = file.readlines()
    
    channel_data = {}
    current_topic = None
    
    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            current_topic = line[3:]
            channel_data[current_topic] = []
        elif line.startswith('- '):
            parts = line[2:].split(' - ')
            if len(parts) == 2:
                channel_name, channel_id = parts
                channel_data[current_topic].append((channel_name.strip(), channel_id.strip()))
            elif len(parts) == 1:
                channel_id = parts[0].strip()
                channel_data[current_topic].append((None, channel_id))
    
    return channel_data

def check_for_duplicate_ids(channel_data):
    seen_ids = set()
    for topic, channels in channel_data.items():
        for _, channel_id in channels:
            if channel_id in seen_ids:
                logging.error(f"Duplicate channel ID found: {channel_id} in topic {topic}")
                return False
            seen_ids.add(channel_id)
    return True

async def load_existing_video_data(table_name, channel_name):
    try:
        response = supabase.table(table_name) \
            .select("video_id") \
            .eq("channel_name", channel_name) \
            .in_("processed", ["pending", "completed"]) \
            .execute()
        
        return {row['video_id'] for row in response.data}
    except Exception as e:
        logging.error(f"Error loading existing videos: {str(e)}")
        return set()
    
    
def check_channel_name_consistency(channel_data):
    for topic, channels in channel_data.items():
        table_name = f"youtube_{topic.lower().replace(' ', '_')}"
        db_channels = supabase.table(table_name).select("channel_name").execute().data
        db_channel_names = {c['channel_name'] for c in db_channels}
        for channel_name, _ in channels:
            if channel_name and channel_name not in db_channel_names:
                logging.warning(f"Channel name mismatch: '{channel_name}' in .md file not found in database for topic '{topic}'")

def fetch_channel_videos(channel_id, max_videos=MAX_VIDEOS):
    """
    Fetches videos from the given channel ID that were uploaded in the last 48 hours.
    Uses an optimized approach to stop fetching once we find older videos.
    """
    date_48_hours_ago = datetime.now(timezone.utc) - timedelta(hours=48)
    date_after = date_48_hours_ago.strftime('%Y%m%d')

    logging.info(f"Fetching videos after date: {date_after}")
    
    # First yt-dlp call to get video list
    ydl_opts = {
        'extract_flat': 'in_playlist',
        'force_generic_extractor': False,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'dateafter': date_after,
        'playlistend': 30 if max_videos <= 0 else max_videos,
    }

    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            if not info or 'entries' not in info:
                logging.error(f"No video information found for channel ID: {channel_id}")
                return [], {}

            video_ids = []
            video_info = {}
            
            # Debug: Print all entries
            logging.info(f"Total entries found: {len(info['entries'])}")
            
            # Second yt-dlp call with different options to get detailed metadata
            detailed_opts = {
                'quiet': True,
                'no_warnings': True,
                'ignoreerrors': True,
                'skip_download': True,
            }
            
            with yt_dlp.YoutubeDL(detailed_opts) as detailed_ydl:
                for entry in info['entries']:
                    if entry:
                        video_id = entry['id']
                        
                        try:
                            video_info_entry = detailed_ydl.extract_info(
                                f"https://www.youtube.com/watch?v={video_id}", 
                                download=False
                            )
                            
                            # Get upload date using multiple timestamp fields
                            if video_info_entry.get('release_timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['release_timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('timestamp'):
                                published_at = datetime.fromtimestamp(video_info_entry['timestamp'], tz=timezone.utc)
                            elif video_info_entry.get('upload_date'):
                                published_at = datetime.strptime(video_info_entry['upload_date'], '%Y%m%d').replace(tzinfo=timezone.utc)
                            else:
                                logging.warning(f"No publication date found for video {video_id}")
                                continue

                            # Break the loop if we find a video older than 48 hours
                            if published_at < date_48_hours_ago:
                                logging.info(f"Found older video, stopping search: {video_id} from {published_at}")
                                break

                            video_ids.append(video_id)
                            video_info[video_id] = {
                                'id': video_id,
                                'title': video_info_entry.get('title'),
                                'duration': video_info_entry.get('duration'),
                                'upload_date': published_at.strftime('%Y-%m-%d %H:%M:%S'),
                                'timestamp': video_info_entry.get('timestamp'),
                                'release_timestamp': video_info_entry.get('release_timestamp')
                            }
                            logging.info(f"Added video {video_id} to results (uploaded at {published_at})")
                            
                        except Exception as e:
                            logging.error(f"Error fetching detailed info for video {video_id}: {str(e)}")
                            continue

            logging.info(f"Fetched {len(video_ids)} videos from the last 48 hours for channel ID: {channel_id}")
            return video_ids, video_info

        except Exception as e:
            logging.error(f"Error fetching videos for channel ID {channel_id}: {str(e)}")
            return [], {}

async def compare_videos(table_name, channel_name, channel_id):
    existing_videos = await load_existing_video_data(table_name, channel_name)
    channel_video_ids, channel_video_info = fetch_channel_videos(channel_id)
    
    # Debug logging
    logging.info(f"Raw fetch results for {channel_name}:")
    logging.info(f"Number of videos fetched: {len(channel_video_ids)}")
    for vid_id, info in channel_video_info.items():
        logging.info(f"Video {vid_id}: Upload date = {info.get('upload_date')}")
    
    existing_video_set = set(existing_videos)
    channel_video_set = set(channel_video_ids)
    
    new_videos = list(channel_video_set - existing_video_set)
    missing_videos = list(existing_video_set - channel_video_set)
    
    # More detailed summary
    summary = f"""Comparison for channel: {channel_name} (ID: {channel_id})
Total videos in database: {len(existing_videos)}
Total videos on YouTube channel (last 48 hours): {len(channel_video_ids)}
New videos (on YouTube but not in database): {len(new_videos)}
Missing videos (in database but not on YouTube): {len(missing_videos)}
Video IDs found: {', '.join(channel_video_ids) if channel_video_ids else 'None'}"""
    
    logging.info(summary)
    
    # Only return the info for new videos
    new_video_info = {vid: channel_video_info[vid] for vid in new_videos}
    
    return new_videos, new_video_info, summary

def format_transcript(transcript):
    formatted_text = ""
    for segment in transcript:
        start_time = f"{int(segment['start'] // 60):02d}:{int(segment['start'] % 60):02d}"
        formatted_text += f"[{start_time}] {segment['text']}\n"
    
    return {"formatted_text": formatted_text.strip()}

import sys
import logging

# Suppress HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)

def clean_text(text):
    # Remove HTML-like tags and extra spaces
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def find_overlap(text1, text2):
    # Find the longest overlap between the end of text1 and the start of text2
    max_overlap = min(len(text1), len(text2))
    for i in range(max_overlap, 0, -1):
        if text1[-i:] == text2[:i]:
            return i
    return 0

def merge_captions(captions):
    # Merge captions while removing overlaps
    full_text = ""
    for caption in captions:
        clean_caption = clean_text(caption)
        if not full_text:
            full_text = clean_caption
        else:
            overlap_len = find_overlap(full_text, clean_caption)
            non_overlapping_part = clean_caption[overlap_len:]
            full_text += ' ' + non_overlapping_part
    return full_text

def remove_repeated_sentences(text):
    # Tokenize the text into sentences and remove duplicates
    sentences = sent_tokenize(text)
    unique_sentences = []
    seen_sentences = set()
    for sentence in sentences:
        cleaned_sentence = sentence.strip()
        lower_sentence = cleaned_sentence.lower()
        if lower_sentence not in seen_sentences:
            unique_sentences.append(cleaned_sentence)
            seen_sentences.add(lower_sentence)
    return ' '.join(unique_sentences)

def fetch_transcript_yt_dlp(video_id):
    ydl_opts = {
        'skip_download': True,
        'writesubtitles': False,
        'writeautomaticsub': True,
        'subtitlesformat': 'vtt',
        'subtitleslangs': ['en'],
        'outtmpl': f'{video_id}',
        'quiet': True,
        'no_warnings': True,
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
            
            # Check if automatic captions are available
            subtitles_available = False
            if 'automatic_captions' in info and info['automatic_captions']:
                for lang in info['automatic_captions']:
                    if lang == 'en':
                        subtitles_available = True
                        break

            if not subtitles_available:
                logging.warning(f"No automatic English subtitles available for video {video_id}")
                return None
            
            # Download the automatic subtitles
            ydl.download([f'https://www.youtube.com/watch?v={video_id}'])
            
            vtt_filename = f'{video_id}.en.vtt'
            if os.path.exists(vtt_filename):
                with open(vtt_filename, 'r', encoding='utf-8') as vtt_file:
                    vtt_content = vtt_file.read()
                
                vtt_file = webvtt.read_buffer(StringIO(vtt_content))
                captions = [caption.text for caption in vtt_file]
                full_text = merge_captions(captions)
                
                # Clean the transcript
                cleaned_transcript = remove_repeated_sentences(full_text)
                
                os.remove(vtt_filename)  # Clean up the temporary file
                return {"formatted_text": cleaned_transcript}
            else:
                logging.error(f"VTT file not created for video {video_id}")
                return None
        except Exception as e:
            logging.error(f"Error fetching transcript with yt-dlp for video {video_id}: {str(e)}")
            return None

async def fetch_and_save_metadata(table_name, channel_name, channel_id, new_videos, new_video_info):
    logging.info(f"Processing channel: '{channel_name}'")
    print(f"Found {len(new_videos)} new videos for channel: {channel_name}")
    
    # Double-check existing videos
    existing_videos = await load_existing_video_data(table_name, channel_name)
    truly_new_videos = [vid for vid in new_videos if vid not in existing_videos]
    
    print(f"After double-check, found {len(truly_new_videos)} truly new videos for channel: {channel_name}")
    
    successful_saves = 0
    last_error_message = ""
    
    ydl_opts = {
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'skip_download': True,
    }
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        for index, video_id in enumerate(truly_new_videos, start=1):
            video = new_video_info[video_id]
            
            try:
                duration = int(float(video['duration'])) if video['duration'] is not None else None
            except (ValueError, TypeError):
                logging.warning(f"Invalid duration for video {video_id}: {video['duration']}")
                duration = None
            
            # Fetch detailed metadata for each video
            try:
                video_info_entry = ydl.extract_info(f"https://www.youtube.com/watch?v={video_id}", download=False)
                if video_info_entry.get('release_timestamp'):
                    published_at = datetime.fromtimestamp(video_info_entry['release_timestamp'], tz=pytz.UTC).isoformat()
                elif video_info_entry.get('timestamp'):
                    published_at = datetime.fromtimestamp(video_info_entry['timestamp'], tz=pytz.UTC).isoformat()
                elif video_info_entry.get('upload_date'):
                    published_at = datetime.strptime(video_info_entry['upload_date'], '%Y%m%d').replace(tzinfo=pytz.UTC).isoformat()
                else:
                    published_at = None
                    logging.warning(f"No publication date found for video {video_id}")
            except Exception as e:
                logging.error(f"Error fetching detailed metadata for video {video_id}: {str(e)}")
                published_at = None
            
            video_data = {
                "id": str(uuid.uuid4()),
                "video_id": video_id,
                "channel_name": channel_name,
                "title": video.get('title', ''),
                "duration": duration,
                "published_at": published_at,
                "processed": "pending",  # Changed from False to 'pending'
                "transcript": None,
                "llm_response": None,
                "llm_call_date": None
            }
            
            try:
                # Add debug logging
                logging.info(f"Starting transcript fetch for video {video_id}")
                
                transcript = None
                # First try yt-dlp
                transcript = fetch_transcript_yt_dlp(video_id)
                logging.info(f"yt-dlp transcript result for {video_id}: {'Success' if transcript else 'Failed'}")
                
                # If yt-dlp fails and USE_YOUTUBE_API is True, try YouTube API
                if transcript is None and USE_YOUTUBE_API:
                    try:
                        logging.info(f"Attempting YouTube API transcript fetch for {video_id}")
                        yt_transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['en', 'pt', 'es', 'fr', 'sv', 'zh-Hans', 'it', 'pl', 'ru', 'ar'])
                        transcript = format_transcript(yt_transcript)
                        # Clean the transcript obtained from YouTubeTranscriptApi
                        transcript['formatted_text'] = remove_repeated_sentences(transcript['formatted_text'])
                        logging.info(f"YouTube API transcript fetch successful for {video_id}")
                    except Exception as e:
                        logging.error(f"Error fetching transcript with YouTube API for video {video_id}: {str(e)}")
                
                # Ensure transcript is properly set in video_data
                if transcript and transcript.get('formatted_text'):
                    logging.info(f"Setting transcript for {video_id} (length: {len(transcript['formatted_text'])})")
                    video_data["transcript"] = transcript['formatted_text']
                else:
                    logging.warning(f"No transcript available for {video_id}")
                    video_data["transcript"] = "Transcript not available"
                
                # Add debug logging for video_data
                logging.info(f"Video data for {video_id} prepared with transcript: {bool(video_data.get('transcript'))}")
                
            except Exception as e:
                error_message = f"Error fetching transcript for video {video_id}: {str(e)}"
                logging.error(error_message)
                video_data["transcript"] = "Transcript not available"
                last_error_message = error_message
            
            try:
                @supabase_api_call
                async def insert_video_data():
                    # Remove the await here since execute() returns the response directly
                    return supabase.table(table_name).insert(video_data).execute()

                # Still await the decorated function
                response = await insert_video_data()
                
                if response.data:
                    successful_saves += 1
                    progress = (index / len(truly_new_videos)) * 100
                    print(f"\r[Channel: {channel_name}] Progress: {progress:.2f}% | New Video {index}/{len(truly_new_videos)} | ID: {video_id}", end='', flush=True)
                else:
                    raise Exception("No data returned from insert operation")
                    
            except Exception as e:
                error_message = f"Error saving metadata for video {video_id}: {str(e)}"
                logging.error(error_message)
                last_error_message = error_message
                progress = (index / len(truly_new_videos)) * 100
                print(f"\r[Channel: {channel_name}] Progress: {progress:.2f}% | New Video {index}/{len(truly_new_videos)} | ID: {video_id}", end='', flush=True)
                print(f"\n[Channel: {channel_name}] {last_error_message}", end='', flush=True)
    
    print(f"\nCompleted processing for {channel_name}. Successfully saved {successful_saves} out of {len(truly_new_videos)} new videos.")

def create_table_if_not_exists(table_name):
    engine = create_engine(os.getenv('DIL_POSTGRES_CONNECTION_STRING'))
    metadata = MetaData()

    try:
        # Check if the table exists
        with engine.connect() as connection:
            if not engine.dialect.has_table(connection, table_name):
                # Table doesn't exist, so create it
                table = Table(table_name, metadata,
                    Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
                    Column('channel_name', String),
                    Column('video_id', String, unique=True, nullable=False),
                    Column('title', String),
                    Column('published_at', DateTime(timezone=True)),
                    Column('duration', Integer),
                    Column('transcript', String),
                    Column('summary', String),
                    Column('keywords', JSON),
                    Column('visual_description', String),
                    Column('llm_response', JSONB),
                    Column('llm_call_date', DateTime(timezone=True)),
                    Column('processed', String, check="processed IN ('pending', 'completed', 'error', 'skipped')"),
                    Column('created_at', DateTime(timezone=True), server_default=text('CURRENT_TIMESTAMP'))
                )
                
                metadata.create_all(engine)
                print(f"Table {table_name} created successfully.")
            else:
                print(f"Table {table_name} already exists.")

        # Enable RLS for the table (whether it's new or existing)
        Session = sessionmaker(bind=engine)
        with Session() as session:
            enable_rls_for_table(session, table_name)
    except Exception as e:
        print(f"Error creating table {table_name}: {str(e)}")
    finally:
        engine.dispose()

def enable_rls_for_table(session, table_name):
    try:
        session.execute(text(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;"))
        session.commit()
        print(f"RLS enabled for table {table_name}")
    except Exception as e:
        print(f"Error enabling RLS for table {table_name}: {str(e)}")
        session.rollback()

def print_table(title, data, columns):
    table = RichTable(title=title)
    for column in columns:
        table.add_column(column, style="cyan")
    for row in data:
        table.add_row(*[str(item) for item in row])
    console = Console()
    console.print(table)

def generate_table_names(channel_data):
    return [f"youtube_{topic.lower().replace(' ', '_')}" for topic in channel_data.keys()]

def get_tables_to_check(channel_data):
    if 'TABLES_TO_CHECK' in globals():
        return globals()['TABLES_TO_CHECK']
    else:
        return generate_table_names(channel_data)

def validate_processed_status(status):
    """Validate that a processed status is one of the allowed values."""
    valid_statuses = {'pending', 'completed', 'error', 'skipped'}
    if status not in valid_statuses:
        raise ValueError(f"Invalid processed status: {status}. Must be one of {valid_statuses}")
    return status




async def main():
    global supabase
    session = None
    print("Starting the script...")
    sys.stdout.flush()
    
    try:
        supabase, session = await create_supabase_client()
        
        print("Reading channel data...")
        sys.stdout.flush()
        channel_data = read_channel_data(CHOSEN_WATCHLIST_FILE) #youtube_channels_Watchlist_last_48hours_videos.md
        
        if not check_for_duplicate_ids(channel_data):
            print("Duplicate channel IDs found in the .md file. Exiting.")
            sys.stdout.flush()
            return

        # Create all tables before processing
        print("Creating tables for all topics...")
        sys.stdout.flush()
        for topic in channel_data.keys():
            table_name = f"youtube_{topic.lower().replace(' ', '_')}"
            create_table_if_not_exists(table_name)
        
        # Now check channel name consistency
        print("Checking channel name consistency...")
        sys.stdout.flush()
        check_channel_name_consistency(channel_data)
        
        initial_video_counts = []
        final_video_counts = []

        print("Getting tables to check...")
        sys.stdout.flush()
        tables_to_check = get_tables_to_check(channel_data)

        for topic, channels in channel_data.items():
            print(f"\nProcessing topic: {topic}")
            sys.stdout.flush()
            
            table_name = f"youtube_{topic.lower().replace(' ', '_')}"
            
            if table_name not in tables_to_check:
                print(f"Skipping table: {table_name}")
                sys.stdout.flush()
                continue
            
            channels_processed = 0
            total_new_videos = 0
            
            topic_initial_counts = []
            topic_final_counts = []
            
            for channel_name, channel_id in tqdm(channels, desc="Channels", disable=None):
                print(f"Processing channel: {channel_name}")
                sys.stdout.flush()
                try:
                    new_videos, new_video_info, summary = await compare_videos(table_name, channel_name, channel_id)
                    print(summary)
                    sys.stdout.flush()
                    
                    initial_count = len(new_videos)
                    topic_initial_counts.append((channel_name, initial_count))
                    
                    if new_videos:
                        await fetch_and_save_metadata(table_name, channel_name, channel_id, new_videos, new_video_info)
                        total_new_videos += len(new_videos)
                    
                    channels_processed += 1
                    
                    final_count = len(new_videos)
                    topic_final_counts.append((channel_name, final_count))
                    
                    print(f"Waiting 0.5 second before next channel...")  # Updated message
                    sys.stdout.flush()
                    time.sleep(0.5)  # Changed from 5 to 0.5 seconds
                except Exception as e:
                    print(f"Error processing channel {channel_name}: {str(e)}")
                    sys.stdout.flush()
                    continue
            
            initial_video_counts.extend([(topic, *count) for count in topic_initial_counts])
            final_video_counts.extend([(topic, *count) for count in topic_final_counts])
            
            print(f"\nCompleted processing for topic: {topic}")
            print(f"Channels processed: {channels_processed}")
            print(f"Total new videos saved: {total_new_videos}")
            sys.stdout.flush()
        
        print("\nPrinting summary tables...")
        sys.stdout.flush()
        print_table("Initial Video Counts", initial_video_counts, ["Topic", "Channel", "New Videos"])
        print_table("Final Video Counts", final_video_counts, ["Topic", "Channel", "Saved Videos"])
    
    except FileNotFoundError as e:
        print(f"Error: {str(e)}")
        sys.stdout.flush()
    except Exception as e:
        print(f"An unexpected error occurred: {str(e)}")
        sys.stdout.flush()
    finally:
        if session:
            await session.close()

    print("Script execution completed.")
    sys.stdout.flush()

async def run_script(script_name):
    print(f"Starting {script_name}...")
    sys.stdout.flush()
    
    # Use '-u' flag for unbuffered output and set PYTHONUNBUFFERED to '1'
    process = await asyncio.create_subprocess_exec(
        sys.executable, '-u', script_name,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
        env=dict(os.environ, PYTHONUNBUFFERED='1')
    )

    async def read_stream(stream, prefix):
        while True:
            data = await stream.read(1024)  # Read in chunks of 1024 bytes
            if not data:
                break
            print(f"{prefix}: {data.decode()}", end='', flush=True)

    # Run both stdout and stderr readers concurrently
    await asyncio.gather(
        read_stream(process.stdout, f"{script_name} [OUT]"),
        read_stream(process.stderr, f"{script_name} [ERR]")
    )

    return_code = await process.wait()
    print(f"{script_name} completed with return code {return_code}", flush=True)

async def run_additional_scripts():
    await run_script("gemini_supabase_query_pages_V8.py")
    await run_script("Just_email_V2.py")

# Modify the script execution
if __name__ == "__main__":
    import threading

    def run_flask():
        app.run(host='0.0.0.0', port=8080, debug=False, use_reloader=False)

    def run_main():
        asyncio.run(main())
        asyncio.run(run_additional_scripts())

    # Start Flask in a separate thread
    flask_thread = threading.Thread(target=run_flask)
    flask_thread.start()

    # Run the main function in the main thread
    run_main()
    



