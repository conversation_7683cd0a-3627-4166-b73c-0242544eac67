WEBVTT
Kind: captions
Language: en

00:00:00.799 --> 00:00:04.910 align:start position:0%
 
this<00:00:01.520><c> is</c><00:00:01.719><c> autog</c><00:00:02.480><c> beta</c><00:00:02.800><c> version</c><00:00:03.520><c> three</c><00:00:04.240><c> is</c><00:00:04.359><c> it</c>

00:00:04.910 --> 00:00:04.920 align:start position:0%
this is autog beta version three is it
 

00:00:04.920 --> 00:00:08.549 align:start position:0%
this is autog beta version three is it
or<00:00:05.080><c> four</c><00:00:05.480><c> maybe</c><00:00:06.120><c> whatever</c><00:00:06.960><c> here's</c><00:00:07.240><c> what's</c><00:00:07.559><c> new</c>

00:00:08.549 --> 00:00:08.559 align:start position:0%
or four maybe whatever here's what's new
 

00:00:08.559 --> 00:00:10.230 align:start position:0%
or four maybe whatever here's what's new
our<00:00:08.719><c> friends</c><00:00:08.960><c> at</c><00:00:09.120><c> grock</c><00:00:09.440><c> have</c><00:00:09.599><c> added</c><00:00:09.920><c> support</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
our friends at grock have added support
 

00:00:10.240 --> 00:00:12.950 align:start position:0%
our friends at grock have added support
for<00:00:10.480><c> more</c><00:00:11.120><c> llms</c><00:00:12.120><c> and</c><00:00:12.240><c> we've</c><00:00:12.400><c> included</c><00:00:12.759><c> them</c><00:00:12.880><c> in</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
for more llms and we've included them in
 

00:00:12.960 --> 00:00:15.549 align:start position:0%
for more llms and we've included them in
the<00:00:13.040><c> autog</c><00:00:13.360><c> gr</c><00:00:13.719><c> interface</c><00:00:14.719><c> autog</c><00:00:15.080><c> gr</c><00:00:15.280><c> allows</c>

00:00:15.549 --> 00:00:15.559 align:start position:0%
the autog gr interface autog gr allows
 

00:00:15.559 --> 00:00:17.349 align:start position:0%
the autog gr interface autog gr allows
you<00:00:15.679><c> to</c><00:00:15.839><c> switch</c><00:00:16.320><c> back</c><00:00:16.480><c> and</c><00:00:16.680><c> forth</c><00:00:17.039><c> between</c>

00:00:17.349 --> 00:00:17.359 align:start position:0%
you to switch back and forth between
 

00:00:17.359 --> 00:00:20.390 align:start position:0%
you to switch back and forth between
models<00:00:18.240><c> and</c><00:00:18.359><c> not</c><00:00:18.520><c> lose</c><00:00:18.760><c> the</c><00:00:19.000><c> context</c><00:00:19.480><c> of</c><00:00:19.600><c> your</c>

00:00:20.390 --> 00:00:20.400 align:start position:0%
models and not lose the context of your
 

00:00:20.400 --> 00:00:22.750 align:start position:0%
models and not lose the context of your
discussion<00:00:21.400><c> as</c><00:00:21.560><c> always</c><00:00:21.960><c> autogr</c><00:00:22.600><c> has</c>

00:00:22.750 --> 00:00:22.760 align:start position:0%
discussion as always autogr has
 

00:00:22.760 --> 00:00:25.390 align:start position:0%
discussion as always autogr has
refactored<00:00:23.400><c> our</c><00:00:23.599><c> prompt</c><00:00:24.480><c> and</c><00:00:24.640><c> created</c><00:00:24.960><c> a</c><00:00:25.080><c> full</c>

00:00:25.390 --> 00:00:25.400 align:start position:0%
refactored our prompt and created a full
 

00:00:25.400 --> 00:00:28.070 align:start position:0%
refactored our prompt and created a full
team<00:00:25.640><c> of</c><00:00:25.840><c> Agents</c><00:00:26.599><c> ready</c><00:00:26.840><c> to</c><00:00:27.000><c> help</c><00:00:27.279><c> tackle</c><00:00:27.599><c> our</c>

00:00:28.070 --> 00:00:28.080 align:start position:0%
team of Agents ready to help tackle our
 

00:00:28.080 --> 00:00:30.470 align:start position:0%
team of Agents ready to help tackle our
project<00:00:29.080><c> we'll</c><00:00:29.279><c> grab</c><00:00:29.480><c> the</c><00:00:29.560><c> mix</c><00:00:29.759><c> dra</c><00:00:30.080><c> model</c><00:00:30.320><c> to</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
project we'll grab the mix dra model to
 

00:00:30.480 --> 00:00:32.549 align:start position:0%
project we'll grab the mix dra model to
act<00:00:30.640><c> as</c><00:00:30.720><c> our</c><00:00:30.960><c> project</c><00:00:31.279><c> manager</c><00:00:32.200><c> let's</c><00:00:32.399><c> see</c>

00:00:32.549 --> 00:00:32.559 align:start position:0%
act as our project manager let's see
 

00:00:32.559 --> 00:00:35.229 align:start position:0%
act as our project manager let's see
what<00:00:32.680><c> he</c><00:00:32.800><c> has</c><00:00:32.920><c> to</c><00:00:33.040><c> say</c><00:00:33.200><c> about</c><00:00:33.360><c> our</c><00:00:33.520><c> new</c>

00:00:35.229 --> 00:00:35.239 align:start position:0%
what he has to say about our new
 

00:00:35.239 --> 00:00:37.750 align:start position:0%
what he has to say about our new
CRM<00:00:36.239><c> as</c><00:00:36.399><c> usual</c><00:00:36.760><c> it's</c><00:00:36.960><c> another</c><00:00:37.280><c> cohesive</c>

00:00:37.750 --> 00:00:37.760 align:start position:0%
CRM as usual it's another cohesive
 

00:00:37.760 --> 00:00:39.950 align:start position:0%
CRM as usual it's another cohesive
project<00:00:38.160><c> plan</c><00:00:38.600><c> for</c><00:00:38.800><c> our</c><00:00:39.000><c> team</c><00:00:39.200><c> of</c><00:00:39.360><c> agents</c><00:00:39.719><c> to</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
project plan for our team of agents to
 

00:00:39.960 --> 00:00:42.389 align:start position:0%
project plan for our team of agents to
follow<00:00:40.960><c> the</c><00:00:41.120><c> discussion</c><00:00:41.640><c> history</c><00:00:42.079><c> dropped</c>

00:00:42.389 --> 00:00:42.399 align:start position:0%
follow the discussion history dropped
 

00:00:42.399 --> 00:00:43.950 align:start position:0%
follow the discussion history dropped
down<00:00:42.600><c> below</c><00:00:42.840><c> our</c><00:00:43.039><c> comment</c><00:00:43.320><c> and</c><00:00:43.520><c> whiteboard</c>

00:00:43.950 --> 00:00:43.960 align:start position:0%
down below our comment and whiteboard
 

00:00:43.960 --> 00:00:46.630 align:start position:0%
down below our comment and whiteboard
Windows<00:00:44.640><c> is</c><00:00:44.840><c> new</c><00:00:45.640><c> and</c><00:00:45.760><c> we'll</c><00:00:45.960><c> look</c><00:00:46.079><c> at</c><00:00:46.199><c> it</c><00:00:46.360><c> in</c>

00:00:46.630 --> 00:00:46.640 align:start position:0%
Windows is new and we'll look at it in
 

00:00:46.640 --> 00:00:49.189 align:start position:0%
Windows is new and we'll look at it in
just<00:00:46.760><c> a</c><00:00:46.960><c> bit</c><00:00:47.800><c> for</c><00:00:48.000><c> now</c><00:00:48.520><c> let's</c><00:00:48.760><c> take</c><00:00:48.879><c> a</c><00:00:49.000><c> look</c><00:00:49.120><c> at</c>

00:00:49.189 --> 00:00:49.199 align:start position:0%
just a bit for now let's take a look at
 

00:00:49.199 --> 00:00:51.389 align:start position:0%
just a bit for now let's take a look at
our<00:00:49.360><c> new</c><00:00:49.520><c> rag</c><00:00:49.920><c> support</c><00:00:50.920><c> with</c><00:00:51.079><c> this</c><00:00:51.199><c> new</c>

00:00:51.389 --> 00:00:51.399 align:start position:0%
our new rag support with this new
 

00:00:51.399 --> 00:00:53.630 align:start position:0%
our new rag support with this new
version<00:00:51.760><c> you</c><00:00:51.840><c> can</c><00:00:52.000><c> upload</c><00:00:52.440><c> data</c><00:00:52.760><c> as</c><00:00:52.879><c> a</c><00:00:53.039><c> CSV</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
version you can upload data as a CSV
 

00:00:53.640 --> 00:00:56.150 align:start position:0%
version you can upload data as a CSV
file<00:00:54.520><c> and</c><00:00:54.680><c> include</c><00:00:55.000><c> it</c><00:00:55.160><c> in</c><00:00:55.280><c> your</c><00:00:55.440><c> autog</c><00:00:55.800><c> gr</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
file and include it in your autog gr
 

00:00:56.160 --> 00:00:58.509 align:start position:0%
file and include it in your autog gr
sessions<00:00:57.160><c> like</c><00:00:57.359><c> the</c><00:00:57.480><c> discussion</c><00:00:57.920><c> history</c>

00:00:58.509 --> 00:00:58.519 align:start position:0%
sessions like the discussion history
 

00:00:58.519 --> 00:01:00.869 align:start position:0%
sessions like the discussion history
your<00:00:58.719><c> rag</c><00:00:59.039><c> data</c><00:00:59.239><c> should</c><00:00:59.480><c> persist</c><00:01:00.399><c> even</c><00:01:00.600><c> if</c><00:01:00.719><c> you</c>

00:01:00.869 --> 00:01:00.879 align:start position:0%
your rag data should persist even if you
 

00:01:00.879 --> 00:01:03.310 align:start position:0%
your rag data should persist even if you
switch<00:01:01.280><c> back</c><00:01:01.440><c> and</c><00:01:01.640><c> forth</c><00:01:01.960><c> between</c><00:01:02.320><c> llms</c>

00:01:03.310 --> 00:01:03.320 align:start position:0%
switch back and forth between llms
 

00:01:03.320 --> 00:01:06.230 align:start position:0%
switch back and forth between llms
during<00:01:03.559><c> your</c><00:01:03.760><c> autogr</c><00:01:04.360><c> testing</c><00:01:05.040><c> sessions</c><00:01:06.040><c> our</c>

00:01:06.230 --> 00:01:06.240 align:start position:0%
during your autogr testing sessions our
 

00:01:06.240 --> 00:01:08.030 align:start position:0%
during your autogr testing sessions our
backend<00:01:06.600><c> development</c><00:01:07.040><c> agent</c><00:01:07.360><c> isn't</c><00:01:07.640><c> strictly</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
backend development agent isn't strictly
 

00:01:08.040 --> 00:01:10.990 align:start position:0%
backend development agent isn't strictly
a<00:01:08.159><c> DBA</c><00:01:09.080><c> but</c><00:01:09.400><c> that's</c><00:01:09.759><c> okay</c><00:01:10.400><c> they're</c><00:01:10.600><c> happy</c><00:01:10.840><c> to</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
a DBA but that's okay they're happy to
 

00:01:11.000 --> 00:01:13.230 align:start position:0%
a DBA but that's okay they're happy to
act<00:01:11.159><c> like</c><00:01:11.320><c> one</c><00:01:11.600><c> and</c><00:01:11.759><c> help</c><00:01:11.920><c> us</c><00:01:12.119><c> convert</c><00:01:12.439><c> our</c><00:01:12.640><c> CSV</c>

00:01:13.230 --> 00:01:13.240 align:start position:0%
act like one and help us convert our CSV
 

00:01:13.240 --> 00:01:15.310 align:start position:0%
act like one and help us convert our CSV
file<00:01:13.560><c> to</c><00:01:13.720><c> a</c><00:01:13.880><c> database</c><00:01:14.799><c> by</c><00:01:14.920><c> writing</c><00:01:15.200><c> the</c>

00:01:15.310 --> 00:01:15.320 align:start position:0%
file to a database by writing the
 

00:01:15.320 --> 00:01:17.230 align:start position:0%
file to a database by writing the
queries<00:01:15.799><c> that</c><00:01:15.960><c> will</c><00:01:16.119><c> build</c><00:01:16.479><c> whatever</c><00:01:16.799><c> schema</c>

00:01:17.230 --> 00:01:17.240 align:start position:0%
queries that will build whatever schema
 

00:01:17.240 --> 00:01:21.149 align:start position:0%
queries that will build whatever schema
we<00:01:17.360><c> need</c><00:01:17.640><c> to</c><00:01:17.799><c> get</c><00:01:17.960><c> the</c><00:01:18.119><c> job</c>

00:01:21.149 --> 00:01:21.159 align:start position:0%
 
 

00:01:21.159 --> 00:01:23.749 align:start position:0%
 
done<00:01:22.159><c> likewise</c><00:01:22.520><c> for</c><00:01:22.640><c> our</c><00:01:22.799><c> software</c><00:01:23.200><c> architect</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
done likewise for our software architect
 

00:01:23.759 --> 00:01:25.550 align:start position:0%
done likewise for our software architect
who<00:01:23.960><c> is</c><00:01:24.159><c> happy</c><00:01:24.360><c> to</c><00:01:24.560><c> put</c><00:01:24.720><c> on</c><00:01:24.840><c> their</c><00:01:25.079><c> developer</c>

00:01:25.550 --> 00:01:25.560 align:start position:0%
who is happy to put on their developer
 

00:01:25.560 --> 00:01:28.350 align:start position:0%
who is happy to put on their developer
hat<00:01:26.400><c> and</c><00:01:26.640><c> quickly</c><00:01:27.000><c> produce</c><00:01:27.400><c> the</c><00:01:27.600><c> base</c><00:01:27.960><c> classes</c>

00:01:28.350 --> 00:01:28.360 align:start position:0%
hat and quickly produce the base classes
 

00:01:28.360 --> 00:01:30.670 align:start position:0%
hat and quickly produce the base classes
our<00:01:28.560><c> solution</c><00:01:29.000><c> demands</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
our solution demands
 

00:01:30.680 --> 00:01:32.870 align:start position:0%
our solution demands
if<00:01:30.799><c> you</c><00:01:30.920><c> saw</c><00:01:31.159><c> our</c><00:01:31.360><c> earlier</c><00:01:31.759><c> autogr</c><00:01:32.320><c> videos</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
if you saw our earlier autogr videos
 

00:01:32.880 --> 00:01:34.710 align:start position:0%
if you saw our earlier autogr videos
you've<00:01:33.079><c> seen</c><00:01:33.280><c> us</c><00:01:33.479><c> validate</c><00:01:33.880><c> and</c><00:01:34.000><c> use</c><00:01:34.240><c> the</c><00:01:34.399><c> code</c>

00:01:34.710 --> 00:01:34.720 align:start position:0%
you've seen us validate and use the code
 

00:01:34.720 --> 00:01:37.069 align:start position:0%
you've seen us validate and use the code
our<00:01:34.880><c> agents</c><00:01:35.320><c> produce</c><00:01:36.280><c> I</c><00:01:36.360><c> won't</c><00:01:36.600><c> Bore</c><00:01:36.799><c> You</c><00:01:36.920><c> by</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
our agents produce I won't Bore You by
 

00:01:37.079 --> 00:01:39.749 align:start position:0%
our agents produce I won't Bore You by
going<00:01:37.240><c> over</c><00:01:37.439><c> old</c><00:01:37.920><c> material</c><00:01:38.920><c> instead</c><00:01:39.560><c> let's</c>

00:01:39.749 --> 00:01:39.759 align:start position:0%
going over old material instead let's
 

00:01:39.759 --> 00:01:42.149 align:start position:0%
going over old material instead let's
look<00:01:39.920><c> at</c><00:01:40.119><c> another</c><00:01:40.439><c> exciting</c><00:01:40.840><c> new</c><00:01:41.040><c> feature</c><00:01:42.000><c> the</c>

00:01:42.149 --> 00:01:42.159 align:start position:0%
look at another exciting new feature the
 

00:01:42.159 --> 00:01:44.310 align:start position:0%
look at another exciting new feature the
discussion<00:01:42.600><c> history</c><00:01:42.920><c> I</c><00:01:43.000><c> mentioned</c><00:01:43.360><c> earlier</c>

00:01:44.310 --> 00:01:44.320 align:start position:0%
discussion history I mentioned earlier
 

00:01:44.320 --> 00:01:46.310 align:start position:0%
discussion history I mentioned earlier
this<00:01:44.439><c> helps</c><00:01:44.759><c> clean</c><00:01:45.000><c> up</c><00:01:45.159><c> our</c><00:01:45.320><c> autog</c><00:01:45.640><c> gr</c><00:01:46.000><c> comment</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
this helps clean up our autog gr comment
 

00:01:46.320 --> 00:01:48.910 align:start position:0%
this helps clean up our autog gr comment
window<00:01:47.320><c> and</c><00:01:47.520><c> thanks</c><00:01:47.680><c> to</c><00:01:47.840><c> streamlet</c><00:01:48.719><c> the</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
window and thanks to streamlet the
 

00:01:48.920 --> 00:01:51.230 align:start position:0%
window and thanks to streamlet the
display<00:01:49.280><c> is</c><00:01:49.479><c> wonderful</c><00:01:50.360><c> with</c><00:01:50.640><c> beautifully</c>

00:01:51.230 --> 00:01:51.240 align:start position:0%
display is wonderful with beautifully
 

00:01:51.240 --> 00:01:53.590 align:start position:0%
display is wonderful with beautifully
formatted<00:01:51.840><c> color</c><00:01:52.119><c> enhanced</c><00:01:52.640><c> code</c><00:01:52.920><c> blocks</c><00:01:53.240><c> and</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
formatted color enhanced code blocks and
 

00:01:53.600 --> 00:01:56.109 align:start position:0%
formatted color enhanced code blocks and
everything<00:01:54.600><c> session</c><00:01:55.040><c> variables</c><00:01:55.560><c> hold</c><00:01:55.759><c> on</c><00:01:55.920><c> to</c>

00:01:56.109 --> 00:01:56.119 align:start position:0%
everything session variables hold on to
 

00:01:56.119 --> 00:01:58.670 align:start position:0%
everything session variables hold on to
as<00:01:56.280><c> much</c><00:01:56.439><c> of</c><00:01:56.600><c> this</c><00:01:56.799><c> material</c><00:01:57.159><c> as</c><00:01:57.320><c> they</c><00:01:57.520><c> can</c><00:01:58.479><c> to</c>

00:01:58.670 --> 00:01:58.680 align:start position:0%
as much of this material as they can to
 

00:01:58.680 --> 00:02:00.870 align:start position:0%
as much of this material as they can to
ensure<00:01:59.000><c> the</c><00:01:59.119><c> llms</c><00:01:59.680><c> have</c><00:02:00.039><c> as</c><00:02:00.200><c> much</c><00:02:00.439><c> relevant</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
ensure the llms have as much relevant
 

00:02:00.880 --> 00:02:03.109 align:start position:0%
ensure the llms have as much relevant
context<00:02:01.360><c> as</c><00:02:01.640><c> possible</c><00:02:02.360><c> when</c><00:02:02.560><c> responding</c><00:02:03.000><c> to</c>

00:02:03.109 --> 00:02:03.119 align:start position:0%
context as possible when responding to
 

00:02:03.119 --> 00:02:05.429 align:start position:0%
context as possible when responding to
your<00:02:03.560><c> requests</c><00:02:04.560><c> the</c><00:02:04.719><c> agent</c><00:02:05.000><c> files</c><00:02:05.280><c> for</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
your requests the agent files for
 

00:02:05.439 --> 00:02:07.630 align:start position:0%
your requests the agent files for
autogen<00:02:05.880><c> and</c><00:02:06.039><c> crew</c><00:02:06.320><c> aai</c><00:02:06.880><c> are</c><00:02:07.079><c> available</c><00:02:07.439><c> for</c>

00:02:07.630 --> 00:02:07.640 align:start position:0%
autogen and crew aai are available for
 

00:02:07.640 --> 00:02:10.430 align:start position:0%
autogen and crew aai are available for
download<00:02:08.119><c> as</c><00:02:08.319><c> before</c><00:02:09.319><c> if</c><00:02:09.440><c> you</c><00:02:09.560><c> host</c><00:02:09.840><c> autogr</c>

00:02:10.430 --> 00:02:10.440 align:start position:0%
download as before if you host autogr
 

00:02:10.440 --> 00:02:12.070 align:start position:0%
download as before if you host autogr
locally<00:02:11.039><c> you'll</c><00:02:11.280><c> appreciate</c><00:02:11.720><c> that</c><00:02:11.879><c> these</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
locally you'll appreciate that these
 

00:02:12.080 --> 00:02:13.710 align:start position:0%
locally you'll appreciate that these
files<00:02:12.319><c> are</c><00:02:12.520><c> never</c><00:02:12.840><c> actually</c><00:02:13.120><c> written</c><00:02:13.440><c> to</c><00:02:13.599><c> the</c>

00:02:13.710 --> 00:02:13.720 align:start position:0%
files are never actually written to the
 

00:02:13.720 --> 00:02:16.270 align:start position:0%
files are never actually written to the
file<00:02:14.080><c> system</c><00:02:15.000><c> so</c><00:02:15.200><c> your</c><00:02:15.400><c> local</c><00:02:15.640><c> streamlit</c>

00:02:16.270 --> 00:02:16.280 align:start position:0%
file system so your local streamlit
 

00:02:16.280 --> 00:02:18.790 align:start position:0%
file system so your local streamlit
server<00:02:16.800><c> won't</c><00:02:17.080><c> eat</c><00:02:17.280><c> up</c><00:02:17.440><c> your</c><00:02:17.599><c> hard</c><00:02:17.879><c> drive</c>

00:02:18.790 --> 00:02:18.800 align:start position:0%
server won't eat up your hard drive
 

00:02:18.800 --> 00:02:20.830 align:start position:0%
server won't eat up your hard drive
physical<00:02:19.200><c> zip</c><00:02:19.480><c> files</c><00:02:19.800><c> don't</c><00:02:20.080><c> exist</c><00:02:20.360><c> until</c><00:02:20.640><c> you</c>

00:02:20.830 --> 00:02:20.840 align:start position:0%
physical zip files don't exist until you
 

00:02:20.840 --> 00:02:22.790 align:start position:0%
physical zip files don't exist until you
download<00:02:21.280><c> them</c><00:02:21.440><c> from</c><00:02:21.599><c> the</c><00:02:21.760><c> server</c><00:02:22.680><c> the</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
download them from the server the
 

00:02:22.800 --> 00:02:25.910 align:start position:0%
download them from the server the
autogen<00:02:23.319><c> workflow</c><00:02:23.879><c> file</c><00:02:24.160><c> is</c><00:02:24.319><c> included</c><00:02:24.920><c> too</c>

00:02:25.910 --> 00:02:25.920 align:start position:0%
autogen workflow file is included too
 

00:02:25.920 --> 00:02:28.830 align:start position:0%
autogen workflow file is included too
autog<00:02:26.720><c> saves</c><00:02:27.000><c> you</c><00:02:27.120><c> from</c><00:02:27.319><c> typing</c><00:02:27.720><c> hundreds</c><00:02:28.640><c> and</c>

00:02:28.830 --> 00:02:28.840 align:start position:0%
autog saves you from typing hundreds and
 

00:02:28.840 --> 00:02:30.550 align:start position:0%
autog saves you from typing hundreds and
maybe<00:02:29.239><c> thousands</c><00:02:29.640><c> of</c><00:02:30.000><c> lines</c><00:02:30.280><c> of</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
maybe thousands of lines of
 

00:02:30.560 --> 00:02:34.390 align:start position:0%
maybe thousands of lines of
automatically<00:02:31.200><c> generated</c><00:02:31.800><c> code</c><00:02:33.280><c> SQL</c>

00:02:34.390 --> 00:02:34.400 align:start position:0%
automatically generated code SQL
 

00:02:34.400 --> 00:02:37.350 align:start position:0%
automatically generated code SQL
JavaScript<00:02:35.400><c> and</c><00:02:35.680><c> more</c><00:02:36.680><c> you're</c><00:02:36.879><c> going</c><00:02:37.040><c> to</c><00:02:37.200><c> have</c>

00:02:37.350 --> 00:02:37.360 align:start position:0%
JavaScript and more you're going to have
 

00:02:37.360 --> 00:02:40.110 align:start position:0%
JavaScript and more you're going to have
a<00:02:37.519><c> lot</c><00:02:37.640><c> of</c><00:02:37.879><c> free</c><00:02:38.120><c> time</c><00:02:38.879><c> and</c><00:02:39.040><c> no</c><00:02:39.280><c> crew</c><00:02:39.519><c> AI</c><00:02:39.840><c> fans</c>

00:02:40.110 --> 00:02:40.120 align:start position:0%
a lot of free time and no crew AI fans
 

00:02:40.120 --> 00:02:41.949 align:start position:0%
a lot of free time and no crew AI fans
we<00:02:40.239><c> didn't</c><00:02:40.440><c> forget</c><00:02:40.680><c> about</c><00:02:40.879><c> you</c><00:02:41.120><c> either</c><00:02:41.840><c> the</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
we didn't forget about you either the
 

00:02:41.959 --> 00:02:43.509 align:start position:0%
we didn't forget about you either the
entire<00:02:42.239><c> team</c><00:02:42.400><c> of</c><00:02:42.519><c> Agents</c><00:02:42.879><c> is</c><00:02:43.040><c> available</c><00:02:43.360><c> for</c>

00:02:43.509 --> 00:02:43.519 align:start position:0%
entire team of Agents is available for
 

00:02:43.519 --> 00:02:46.270 align:start position:0%
entire team of Agents is available for
you<00:02:43.599><c> to</c><00:02:43.800><c> download</c><00:02:44.200><c> too</c><00:02:45.120><c> see</c><00:02:45.720><c> it's</c><00:02:45.920><c> just</c><00:02:46.120><c> that</c>

00:02:46.270 --> 00:02:46.280 align:start position:0%
you to download too see it's just that
 

00:02:46.280 --> 00:02:48.190 align:start position:0%
you to download too see it's just that
easy<00:02:47.000><c> right</c><00:02:47.159><c> about</c><00:02:47.400><c> now</c><00:02:47.640><c> you're</c><00:02:47.920><c> probably</c>

00:02:48.190 --> 00:02:48.200 align:start position:0%
easy right about now you're probably
 

00:02:48.200 --> 00:02:50.670 align:start position:0%
easy right about now you're probably
thinking<00:02:49.000><c> gosh</c><00:02:49.239><c> how</c><00:02:49.360><c> can</c><00:02:49.440><c> I</c><00:02:49.599><c> ever</c><00:02:49.800><c> thank</c>

00:02:50.670 --> 00:02:50.680 align:start position:0%
thinking gosh how can I ever thank
 

00:02:50.680 --> 00:02:54.390 align:start position:0%
thinking gosh how can I ever thank
you<00:02:51.680><c> no</c><00:02:51.879><c> need</c><00:02:52.200><c> I'm</c><00:02:52.280><c> a</c><00:02:52.400><c> swell</c><00:02:53.000><c> guy</c><00:02:54.000><c> but</c><00:02:54.159><c> if</c><00:02:54.239><c> you</c>

00:02:54.390 --> 00:02:54.400 align:start position:0%
you no need I'm a swell guy but if you
 

00:02:54.400 --> 00:02:55.990 align:start position:0%
you no need I'm a swell guy but if you
insist<00:02:54.800><c> you</c><00:02:54.879><c> could</c><00:02:55.040><c> always</c><00:02:55.280><c> click</c><00:02:55.560><c> like</c><00:02:55.840><c> and</c>

00:02:55.990 --> 00:02:56.000 align:start position:0%
insist you could always click like and
 

00:02:56.000 --> 00:02:58.190 align:start position:0%
insist you could always click like and
then<00:02:56.239><c> subscribe</c><00:02:57.200><c> you</c><00:02:57.319><c> could</c><00:02:57.480><c> also</c><00:02:57.720><c> visit</c><00:02:58.000><c> this</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
then subscribe you could also visit this
 

00:02:58.200 --> 00:03:00.390 align:start position:0%
then subscribe you could also visit this
project<00:02:58.440><c> on</c><00:02:58.680><c> GitHub</c><00:02:59.040><c> and</c><00:02:59.120><c> lend</c><00:02:59.280><c> a</c><00:02:59.440><c> hand</c><00:03:00.239><c> I'll</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
project on GitHub and lend a hand I'll
 

00:03:00.400 --> 00:03:02.070 align:start position:0%
project on GitHub and lend a hand I'll
leave<00:03:00.519><c> a</c><00:03:00.599><c> link</c><00:03:00.760><c> to</c><00:03:00.840><c> the</c><00:03:01.159><c> repository</c><00:03:01.720><c> and</c><00:03:01.920><c> the</c>

00:03:02.070 --> 00:03:02.080 align:start position:0%
leave a link to the repository and the
 

00:03:02.080 --> 00:03:04.030 align:start position:0%
leave a link to the repository and the
live<00:03:02.319><c> demo</c><00:03:02.560><c> in</c><00:03:02.680><c> the</c><00:03:02.800><c> description</c><00:03:03.239><c> below</c>

00:03:04.030 --> 00:03:04.040 align:start position:0%
live demo in the description below
 

00:03:04.040 --> 00:03:07.519 align:start position:0%
live demo in the description below
thanks<00:03:04.280><c> for</c><00:03:04.519><c> watching</c>

