WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:05.390 align:start position:0%
 
[Music]

00:00:05.390 --> 00:00:05.400 align:start position:0%
 
 

00:00:05.400 --> 00:00:08.509 align:start position:0%
 
TTL<00:00:06.160><c> or</c><00:00:06.359><c> what</c><00:00:06.480><c> stands</c><00:00:06.680><c> for</c><00:00:06.960><c> time</c><00:00:07.200><c> to</c><00:00:07.439><c> live</c><00:00:08.400><c> uh</c>

00:00:08.509 --> 00:00:08.519 align:start position:0%
TTL or what stands for time to live uh
 

00:00:08.519 --> 00:00:10.110 align:start position:0%
TTL or what stands for time to live uh
is<00:00:08.719><c> essentially</c><00:00:09.120><c> like</c><00:00:09.280><c> being</c><00:00:09.440><c> able</c><00:00:09.639><c> to</c><00:00:09.760><c> set</c><00:00:09.960><c> a</c>

00:00:10.110 --> 00:00:10.120 align:start position:0%
is essentially like being able to set a
 

00:00:10.120 --> 00:00:12.470 align:start position:0%
is essentially like being able to set a
data<00:00:10.360><c> retention</c><00:00:10.880><c> policy</c><00:00:11.400><c> or</c><00:00:11.679><c> an</c><00:00:11.840><c> exploration</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
data retention policy or an exploration
 

00:00:12.480 --> 00:00:15.150 align:start position:0%
data retention policy or an exploration
policy<00:00:12.880><c> on</c><00:00:13.200><c> an</c><00:00:13.400><c> artifact</c><00:00:14.280><c> and</c><00:00:14.480><c> it</c><00:00:14.679><c> effectively</c>

00:00:15.150 --> 00:00:15.160 align:start position:0%
policy on an artifact and it effectively
 

00:00:15.160 --> 00:00:17.990 align:start position:0%
policy on an artifact and it effectively
allows<00:00:15.639><c> our</c><00:00:15.920><c> Enterprise</c><00:00:16.480><c> teams</c><00:00:17.000><c> and</c><00:00:17.199><c> users</c><00:00:17.840><c> to</c>

00:00:17.990 --> 00:00:18.000 align:start position:0%
allows our Enterprise teams and users to
 

00:00:18.000 --> 00:00:20.349 align:start position:0%
allows our Enterprise teams and users to
better<00:00:18.240><c> manage</c><00:00:18.640><c> retention</c><00:00:19.160><c> of</c><00:00:19.320><c> artifacts</c><00:00:20.080><c> and</c>

00:00:20.349 --> 00:00:20.359 align:start position:0%
better manage retention of artifacts and
 

00:00:20.359 --> 00:00:23.269 align:start position:0%
better manage retention of artifacts and
automatically<00:00:21.000><c> schedule</c><00:00:21.480><c> them</c><00:00:21.640><c> for</c><00:00:22.279><c> deletion</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
automatically schedule them for deletion
 

00:00:23.279 --> 00:00:25.470 align:start position:0%
automatically schedule them for deletion
now<00:00:23.480><c> this</c><00:00:23.640><c> becomes</c><00:00:24.320><c> particularly</c><00:00:24.960><c> important</c>

00:00:25.470 --> 00:00:25.480 align:start position:0%
now this becomes particularly important
 

00:00:25.480 --> 00:00:27.390 align:start position:0%
now this becomes particularly important
for<00:00:25.680><c> any</c><00:00:25.920><c> customers</c><00:00:26.400><c> that</c><00:00:26.519><c> we</c><00:00:26.679><c> work</c><00:00:26.960><c> with</c><00:00:27.279><c> that</c>

00:00:27.390 --> 00:00:27.400 align:start position:0%
for any customers that we work with that
 

00:00:27.400 --> 00:00:29.310 align:start position:0%
for any customers that we work with that
are<00:00:27.560><c> using</c><00:00:27.880><c> weights</c><00:00:28.119><c> and</c><00:00:28.240><c> biases</c><00:00:28.679><c> to</c><00:00:28.880><c> track</c>

00:00:29.310 --> 00:00:29.320 align:start position:0%
are using weights and biases to track
 

00:00:29.320 --> 00:00:32.030 align:start position:0%
are using weights and biases to track
and<00:00:29.480><c> version</c><00:00:30.039><c> their</c><00:00:30.199><c> data</c><00:00:30.480><c> sets</c><00:00:31.439><c> and</c><00:00:31.840><c> they</c>

00:00:32.030 --> 00:00:32.040 align:start position:0%
and version their data sets and they
 

00:00:32.040 --> 00:00:34.350 align:start position:0%
and version their data sets and they
have<00:00:32.200><c> their</c><00:00:32.360><c> own</c><00:00:33.120><c> uh</c><00:00:33.320><c> you</c><00:00:33.399><c> know</c><00:00:33.600><c> compliance</c>

00:00:34.350 --> 00:00:34.360 align:start position:0%
have their own uh you know compliance
 

00:00:34.360 --> 00:00:36.549 align:start position:0%
have their own uh you know compliance
stakeholders<00:00:35.360><c> um</c><00:00:35.559><c> maybe</c><00:00:35.800><c> those</c><00:00:35.920><c> are</c><00:00:36.120><c> internal</c>

00:00:36.549 --> 00:00:36.559 align:start position:0%
stakeholders um maybe those are internal
 

00:00:36.559 --> 00:00:38.549 align:start position:0%
stakeholders um maybe those are internal
or<00:00:36.800><c> external</c><00:00:37.280><c> bodies</c><00:00:37.840><c> that</c><00:00:38.079><c> want</c><00:00:38.239><c> to</c><00:00:38.399><c> make</c>

00:00:38.549 --> 00:00:38.559 align:start position:0%
or external bodies that want to make
 

00:00:38.559 --> 00:00:40.549 align:start position:0%
or external bodies that want to make
sure<00:00:39.360><c> um</c><00:00:39.480><c> that</c><00:00:39.640><c> they're</c><00:00:40.039><c> they're</c><00:00:40.239><c> keeping</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
sure um that they're they're keeping
 

00:00:40.559 --> 00:00:42.670 align:start position:0%
sure um that they're they're keeping
track<00:00:40.879><c> of</c><00:00:41.280><c> which</c><00:00:41.480><c> data</c><00:00:41.760><c> sets</c><00:00:41.960><c> were</c><00:00:42.160><c> used</c>

00:00:42.670 --> 00:00:42.680 align:start position:0%
track of which data sets were used
 

00:00:42.680 --> 00:00:44.310 align:start position:0%
track of which data sets were used
particularly<00:00:43.440><c> when</c><00:00:43.640><c> data</c><00:00:43.879><c> sets</c><00:00:44.120><c> might</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
particularly when data sets might
 

00:00:44.320 --> 00:00:47.510 align:start position:0%
particularly when data sets might
include<00:00:44.840><c> any</c><00:00:45.039><c> kind</c><00:00:45.200><c> of</c><00:00:45.480><c> pii</c><00:00:46.480><c> um</c><00:00:46.640><c> or</c><00:00:46.960><c> personally</c>

00:00:47.510 --> 00:00:47.520 align:start position:0%
include any kind of pii um or personally
 

00:00:47.520 --> 00:00:50.229 align:start position:0%
include any kind of pii um or personally
identifiable<00:00:48.480><c> information</c><00:00:49.480><c> anything</c><00:00:49.960><c> from</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
identifiable information anything from
 

00:00:50.239 --> 00:00:52.709 align:start position:0%
identifiable information anything from
credit<00:00:50.680><c> card</c><00:00:51.680><c> uh</c><00:00:51.800><c> you</c><00:00:51.920><c> know</c><00:00:52.079><c> details</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
credit card uh you know details
 

00:00:52.719 --> 00:00:55.310 align:start position:0%
credit card uh you know details
addresses<00:00:53.320><c> names</c><00:00:53.760><c> images</c><00:00:54.399><c> all</c><00:00:54.520><c> of</c><00:00:54.719><c> those</c><00:00:54.960><c> fall</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
addresses names images all of those fall
 

00:00:55.320 --> 00:00:58.670 align:start position:0%
addresses names images all of those fall
into<00:00:55.559><c> the</c><00:00:55.719><c> category</c><00:00:56.280><c> of</c><00:00:56.920><c> pii</c><00:00:57.920><c> um</c><00:00:58.239><c> the</c><00:00:58.399><c> other</c>

00:00:58.670 --> 00:00:58.680 align:start position:0%
into the category of pii um the other
 

00:00:58.680 --> 00:01:01.990 align:start position:0%
into the category of pii um the other
kind<00:00:58.800><c> of</c><00:00:58.960><c> big</c><00:00:59.120><c> use</c><00:00:59.399><c> case</c><00:00:59.559><c> we</c><00:00:59.640><c> see</c><00:01:00.239><c> for</c><00:01:00.760><c> TTL</c><00:01:01.760><c> uh</c>

00:01:01.990 --> 00:01:02.000 align:start position:0%
kind of big use case we see for TTL uh
 

00:01:02.000 --> 00:01:04.950 align:start position:0%
kind of big use case we see for TTL uh
is<00:01:02.320><c> better</c><00:01:02.800><c> management</c><00:01:03.320><c> of</c><00:01:03.519><c> of</c><00:01:03.640><c> storage</c><00:01:04.320><c> so</c><00:01:04.839><c> if</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
is better management of of storage so if
 

00:01:04.960 --> 00:01:06.030 align:start position:0%
is better management of of storage so if
you<00:01:05.119><c> kind</c><00:01:05.239><c> of</c><00:01:05.360><c> want</c><00:01:05.439><c> to</c><00:01:05.600><c> use</c><00:01:05.840><c> these</c>

00:01:06.030 --> 00:01:06.040 align:start position:0%
you kind of want to use these
 

00:01:06.040 --> 00:01:08.190 align:start position:0%
you kind of want to use these
exploration<00:01:06.640><c> policies</c><00:01:07.000><c> to</c><00:01:07.200><c> Archive</c><00:01:07.680><c> older</c>

00:01:08.190 --> 00:01:08.200 align:start position:0%
exploration policies to Archive older
 

00:01:08.200 --> 00:01:10.749 align:start position:0%
exploration policies to Archive older
artifacts<00:01:09.200><c> that's</c><00:01:09.400><c> another</c><00:01:09.759><c> option</c><00:01:10.400><c> uh</c><00:01:10.520><c> for</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
artifacts that's another option uh for
 

00:01:10.759 --> 00:01:13.670 align:start position:0%
artifacts that's another option uh for
you<00:01:11.040><c> as</c><00:01:11.240><c> well</c><00:01:12.240><c> now</c><00:01:12.759><c> the</c><00:01:12.920><c> two</c><00:01:13.159><c> big</c><00:01:13.320><c> features</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
you as well now the two big features
 

00:01:13.680 --> 00:01:16.149 align:start position:0%
you as well now the two big features
that<00:01:13.799><c> I'm</c><00:01:13.880><c> going</c><00:01:14.000><c> to</c><00:01:14.159><c> cover</c><00:01:14.479><c> today</c><00:01:15.040><c> around</c><00:01:15.759><c> um</c>

00:01:16.149 --> 00:01:16.159 align:start position:0%
that I'm going to cover today around um
 

00:01:16.159 --> 00:01:19.230 align:start position:0%
that I'm going to cover today around um
TTL<00:01:17.159><c> are</c><00:01:17.320><c> going</c><00:01:17.439><c> to</c><00:01:17.560><c> be</c><00:01:17.680><c> centered</c><00:01:18.200><c> around</c><00:01:19.000><c> the</c>

00:01:19.230 --> 00:01:19.240 align:start position:0%
TTL are going to be centered around the
 

00:01:19.240 --> 00:01:21.310 align:start position:0%
TTL are going to be centered around the
the<00:01:19.360><c> team</c><00:01:19.640><c> settings</c><00:01:20.159><c> page</c><00:01:20.640><c> which</c><00:01:20.799><c> you'll</c><00:01:21.079><c> only</c>

00:01:21.310 --> 00:01:21.320 align:start position:0%
the team settings page which you'll only
 

00:01:21.320 --> 00:01:24.590 align:start position:0%
the team settings page which you'll only
be<00:01:21.520><c> familiar</c><00:01:22.079><c> with</c><00:01:22.479><c> if</c><00:01:22.720><c> you're</c><00:01:23.040><c> a</c><00:01:23.240><c> team</c><00:01:23.600><c> admin</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
be familiar with if you're a team admin
 

00:01:24.600 --> 00:01:26.190 align:start position:0%
be familiar with if you're a team admin
um<00:01:24.720><c> so</c><00:01:24.960><c> this</c><00:01:25.040><c> is</c><00:01:25.280><c> the</c><00:01:25.600><c> the</c><00:01:25.759><c> page</c><00:01:26.000><c> that</c><00:01:26.119><c> I'm</c>

00:01:26.190 --> 00:01:26.200 align:start position:0%
um so this is the the page that I'm
 

00:01:26.200 --> 00:01:28.550 align:start position:0%
um so this is the the page that I'm
sharing<00:01:26.640><c> right</c><00:01:26.759><c> now</c><00:01:27.040><c> it</c><00:01:27.159><c> says</c><00:01:27.400><c> team</c><00:01:27.640><c> settings</c>

00:01:28.550 --> 00:01:28.560 align:start position:0%
sharing right now it says team settings
 

00:01:28.560 --> 00:01:31.270 align:start position:0%
sharing right now it says team settings
um<00:01:28.720><c> that</c><00:01:28.840><c> only</c><00:01:29.040><c> team</c><00:01:29.320><c> admins</c><00:01:29.640><c> have</c><00:01:30.040><c> access</c><00:01:30.320><c> to</c>

00:01:31.270 --> 00:01:31.280 align:start position:0%
um that only team admins have access to
 

00:01:31.280 --> 00:01:33.590 align:start position:0%
um that only team admins have access to
and<00:01:31.759><c> in</c><00:01:31.960><c> this</c><00:01:32.159><c> first</c><00:01:32.600><c> box</c><00:01:32.920><c> you'll</c><00:01:33.079><c> see</c><00:01:33.320><c> kind</c><00:01:33.399><c> of</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
and in this first box you'll see kind of
 

00:01:33.600 --> 00:01:36.870 align:start position:0%
and in this first box you'll see kind of
this<00:01:33.880><c> control</c><00:01:34.479><c> plane</c><00:01:35.240><c> for</c><00:01:35.640><c> being</c><00:01:35.960><c> able</c><00:01:36.320><c> to</c><00:01:36.520><c> set</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
this control plane for being able to set
 

00:01:36.880 --> 00:01:39.429 align:start position:0%
this control plane for being able to set
any<00:01:37.240><c> properties</c><00:01:37.880><c> for</c><00:01:38.200><c> the</c><00:01:38.439><c> the</c><00:01:38.560><c> ttls</c><00:01:39.240><c> that</c><00:01:39.320><c> you</c>

00:01:39.429 --> 00:01:39.439 align:start position:0%
any properties for the the ttls that you
 

00:01:39.439 --> 00:01:42.069 align:start position:0%
any properties for the the ttls that you
want<00:01:39.600><c> to</c><00:01:39.840><c> add</c><00:01:40.040><c> for</c><00:01:40.240><c> your</c><00:01:40.399><c> team</c><00:01:41.399><c> now</c><00:01:41.600><c> the</c><00:01:41.759><c> first</c>

00:01:42.069 --> 00:01:42.079 align:start position:0%
want to add for your team now the first
 

00:01:42.079 --> 00:01:44.870 align:start position:0%
want to add for your team now the first
option<00:01:42.680><c> you</c><00:01:42.960><c> have</c><00:01:43.560><c> um</c><00:01:43.720><c> as</c><00:01:43.799><c> a</c><00:01:43.960><c> team</c><00:01:44.200><c> admin</c><00:01:44.520><c> is</c><00:01:44.680><c> to</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
option you have um as a team admin is to
 

00:01:44.880 --> 00:01:47.149 align:start position:0%
option you have um as a team admin is to
decide<00:01:45.560><c> who</c><00:01:45.880><c> is</c><00:01:46.000><c> allowed</c><00:01:46.280><c> to</c><00:01:46.439><c> set</c><00:01:46.640><c> and</c><00:01:46.840><c> edit</c>

00:01:47.149 --> 00:01:47.159 align:start position:0%
decide who is allowed to set and edit
 

00:01:47.159 --> 00:01:49.190 align:start position:0%
decide who is allowed to set and edit
TTL<00:01:47.759><c> values</c><00:01:48.240><c> for</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
TTL values for
 

00:01:49.200 --> 00:01:51.709 align:start position:0%
TTL values for
artifacts<00:01:50.200><c> so</c><00:01:50.560><c> is</c><00:01:50.680><c> it</c><00:01:50.799><c> going</c><00:01:50.920><c> to</c><00:01:51.040><c> be</c><00:01:51.200><c> just</c><00:01:51.399><c> team</c>

00:01:51.709 --> 00:01:51.719 align:start position:0%
artifacts so is it going to be just team
 

00:01:51.719 --> 00:01:53.870 align:start position:0%
artifacts so is it going to be just team
admins<00:01:52.119><c> who</c><00:01:52.280><c> have</c><00:01:52.479><c> that</c><00:01:52.600><c> kind</c><00:01:52.759><c> of</c><00:01:52.920><c> control</c><00:01:53.479><c> or</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
admins who have that kind of control or
 

00:01:53.880 --> 00:01:55.709 align:start position:0%
admins who have that kind of control or
do<00:01:53.960><c> you</c><00:01:54.040><c> want</c><00:01:54.200><c> to</c><00:01:54.320><c> roll</c><00:01:54.560><c> it</c><00:01:54.680><c> out</c><00:01:55.040><c> to</c><00:01:55.240><c> be</c>

00:01:55.709 --> 00:01:55.719 align:start position:0%
do you want to roll it out to be
 

00:01:55.719 --> 00:01:58.310 align:start position:0%
do you want to roll it out to be
expanded<00:01:56.280><c> to</c><00:01:56.479><c> team</c><00:01:56.759><c> admins</c><00:01:57.240><c> and</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
expanded to team admins and
 

00:01:58.320 --> 00:02:00.990 align:start position:0%
expanded to team admins and
members<00:01:59.320><c> this</c><00:01:59.479><c> becomes</c><00:01:59.840><c> comes</c><00:02:00.280><c> particularly</c>

00:02:00.990 --> 00:02:01.000 align:start position:0%
members this becomes comes particularly
 

00:02:01.000 --> 00:02:03.230 align:start position:0%
members this becomes comes particularly
important<00:02:01.640><c> when</c><00:02:01.960><c> we</c><00:02:02.159><c> go</c><00:02:02.320><c> to</c><00:02:02.479><c> the</c><00:02:02.640><c> next</c><00:02:02.960><c> phase</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
important when we go to the next phase
 

00:02:03.240 --> 00:02:05.310 align:start position:0%
important when we go to the next phase
of<00:02:03.799><c> the</c><00:02:04.079><c> the</c><00:02:04.240><c> decision</c><00:02:04.600><c> you</c><00:02:04.759><c> have</c><00:02:04.920><c> to</c><00:02:05.039><c> make</c>

00:02:05.310 --> 00:02:05.320 align:start position:0%
of the the decision you have to make
 

00:02:05.320 --> 00:02:08.589 align:start position:0%
of the the decision you have to make
around<00:02:06.039><c> setting</c><00:02:06.600><c> a</c><00:02:06.799><c> default</c><00:02:07.360><c> policy</c><00:02:08.319><c> um</c><00:02:08.479><c> that</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
around setting a default policy um that
 

00:02:08.599 --> 00:02:11.550 align:start position:0%
around setting a default policy um that
will<00:02:08.840><c> apply</c><00:02:09.360><c> to</c><00:02:09.640><c> all</c><00:02:09.920><c> artifacts</c><00:02:10.520><c> under</c><00:02:10.840><c> your</c>

00:02:11.550 --> 00:02:11.560 align:start position:0%
will apply to all artifacts under your
 

00:02:11.560 --> 00:02:14.270 align:start position:0%
will apply to all artifacts under your
team<00:02:12.560><c> so</c><00:02:12.680><c> I'm</c><00:02:12.760><c> going</c><00:02:12.879><c> to</c><00:02:13.080><c> go</c><00:02:13.280><c> ahead</c><00:02:13.760><c> and</c><00:02:14.000><c> set</c>

00:02:14.270 --> 00:02:14.280 align:start position:0%
team so I'm going to go ahead and set
 

00:02:14.280 --> 00:02:18.470 align:start position:0%
team so I'm going to go ahead and set
one<00:02:14.440><c> of</c><00:02:14.680><c> those</c><00:02:15.200><c> to</c><00:02:16.200><c> a</c><00:02:16.400><c> 100</c><00:02:16.800><c> days</c><00:02:17.599><c> um</c><00:02:18.200><c> let's</c><00:02:18.400><c> give</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
one of those to a 100 days um let's give
 

00:02:18.480 --> 00:02:19.869 align:start position:0%
one of those to a 100 days um let's give
it<00:02:18.640><c> let's</c><00:02:18.760><c> give</c><00:02:18.879><c> it</c><00:02:19.000><c> a</c>

00:02:19.869 --> 00:02:19.879 align:start position:0%
it let's give it a
 

00:02:19.879 --> 00:02:23.470 align:start position:0%
it let's give it a
year<00:02:20.879><c> um</c><00:02:21.239><c> and</c><00:02:21.680><c> note</c><00:02:22.200><c> that</c><00:02:22.720><c> because</c><00:02:22.959><c> I</c><00:02:23.120><c> expanded</c>

00:02:23.470 --> 00:02:23.480 align:start position:0%
year um and note that because I expanded
 

00:02:23.480 --> 00:02:25.589 align:start position:0%
year um and note that because I expanded
the<00:02:23.599><c> permissions</c><00:02:24.080><c> to</c><00:02:24.239><c> include</c><00:02:24.599><c> members</c><00:02:25.319><c> I'm</c>

00:02:25.589 --> 00:02:25.599 align:start position:0%
the permissions to include members I'm
 

00:02:25.599 --> 00:02:27.630 align:start position:0%
the permissions to include members I'm
also<00:02:26.239><c> giving</c><00:02:26.560><c> permissions</c><00:02:27.000><c> for</c><00:02:27.160><c> members</c><00:02:27.480><c> to</c>

00:02:27.630 --> 00:02:27.640 align:start position:0%
also giving permissions for members to
 

00:02:27.640 --> 00:02:30.470 align:start position:0%
also giving permissions for members to
override<00:02:28.160><c> these</c><00:02:28.360><c> default</c><00:02:29.319><c> now</c><00:02:29.560><c> if</c><00:02:29.920><c> you</c><00:02:30.080><c> want</c>

00:02:30.470 --> 00:02:30.480 align:start position:0%
override these default now if you want
 

00:02:30.480 --> 00:02:33.910 align:start position:0%
override these default now if you want
this<00:02:30.680><c> default</c><00:02:31.319><c> to</c><00:02:31.519><c> kind</c><00:02:31.640><c> of</c><00:02:31.840><c> override</c><00:02:32.920><c> or</c>

00:02:33.910 --> 00:02:33.920 align:start position:0%
this default to kind of override or
 

00:02:33.920 --> 00:02:36.150 align:start position:0%
this default to kind of override or
exist<00:02:34.720><c> uh</c><00:02:34.840><c> and</c><00:02:35.000><c> and</c><00:02:35.200><c> prevent</c><00:02:35.560><c> any</c><00:02:35.760><c> members</c>

00:02:36.150 --> 00:02:36.160 align:start position:0%
exist uh and and prevent any members
 

00:02:36.160 --> 00:02:38.390 align:start position:0%
exist uh and and prevent any members
from<00:02:36.360><c> editing</c><00:02:37.319><c> uh</c><00:02:37.440><c> you</c><00:02:37.519><c> know</c><00:02:37.800><c> the</c><00:02:38.040><c> the</c><00:02:38.160><c> better</c>

00:02:38.390 --> 00:02:38.400 align:start position:0%
from editing uh you know the the better
 

00:02:38.400 --> 00:02:41.550 align:start position:0%
from editing uh you know the the better
option<00:02:38.640><c> for</c><00:02:38.879><c> you</c><00:02:39.200><c> might</c><00:02:39.360><c> be</c><00:02:39.599><c> to</c><00:02:39.760><c> select</c><00:02:40.080><c> team</c>

00:02:41.550 --> 00:02:41.560 align:start position:0%
option for you might be to select team
 

00:02:41.560 --> 00:02:43.790 align:start position:0%
option for you might be to select team
admins<00:02:42.560><c> going</c><00:02:42.680><c> to</c><00:02:42.840><c> go</c><00:02:42.959><c> ahead</c><00:02:43.159><c> and</c><00:02:43.319><c> and</c><00:02:43.480><c> save</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
admins going to go ahead and and save
 

00:02:43.800 --> 00:02:45.190 align:start position:0%
admins going to go ahead and and save
that<00:02:43.959><c> and</c><00:02:44.120><c> one</c><00:02:44.280><c> thing</c><00:02:44.400><c> to</c><00:02:44.519><c> know</c><00:02:44.760><c> is</c><00:02:44.879><c> you'll</c><00:02:45.000><c> see</c>

00:02:45.190 --> 00:02:45.200 align:start position:0%
that and one thing to know is you'll see
 

00:02:45.200 --> 00:02:47.710 align:start position:0%
that and one thing to know is you'll see
kind<00:02:45.319><c> of</c><00:02:45.440><c> the</c><00:02:45.560><c> summary</c><00:02:46.159><c> page</c><00:02:47.040><c> uh</c><00:02:47.239><c> which</c><00:02:47.440><c> will</c>

00:02:47.710 --> 00:02:47.720 align:start position:0%
kind of the summary page uh which will
 

00:02:47.720 --> 00:02:50.509 align:start position:0%
kind of the summary page uh which will
share<00:02:48.599><c> any</c><00:02:48.800><c> changes</c><00:02:49.159><c> that</c><00:02:49.280><c> you</c><00:02:49.440><c> tried</c><00:02:49.760><c> to</c><00:02:49.959><c> make</c>

00:02:50.509 --> 00:02:50.519 align:start position:0%
share any changes that you tried to make
 

00:02:50.519 --> 00:02:52.509 align:start position:0%
share any changes that you tried to make
and<00:02:51.120><c> the</c><00:02:51.280><c> the</c><00:02:51.360><c> big</c><00:02:51.599><c> reason</c><00:02:51.800><c> for</c><00:02:52.000><c> calling</c><00:02:52.360><c> this</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
and the the big reason for calling this
 

00:02:52.519 --> 00:02:54.949 align:start position:0%
and the the big reason for calling this
out<00:02:52.800><c> and</c><00:02:53.120><c> adding</c><00:02:53.640><c> a</c><00:02:53.720><c> bunch</c><00:02:53.920><c> of</c><00:02:54.040><c> writing</c><00:02:54.400><c> in</c><00:02:54.800><c> in</c>

00:02:54.949 --> 00:02:54.959 align:start position:0%
out and adding a bunch of writing in in
 

00:02:54.959 --> 00:02:58.430 align:start position:0%
out and adding a bunch of writing in in
Red<00:02:55.360><c> is</c><00:02:55.560><c> because</c><00:02:56.239><c> we</c><00:02:56.400><c> do</c><00:02:56.760><c> consider</c><00:02:57.280><c> TTL</c><00:02:58.280><c> uh</c>

00:02:58.430 --> 00:02:58.440 align:start position:0%
Red is because we do consider TTL uh
 

00:02:58.440 --> 00:03:01.070 align:start position:0%
Red is because we do consider TTL uh
destructive<00:02:59.360><c> action</c><00:03:00.000><c> because</c><00:03:00.239><c> it</c><00:03:00.560><c> eventually</c>

00:03:01.070 --> 00:03:01.080 align:start position:0%
destructive action because it eventually
 

00:03:01.080 --> 00:03:03.949 align:start position:0%
destructive action because it eventually
will<00:03:01.400><c> cause</c><00:03:02.319><c> um</c><00:03:02.599><c> the</c><00:03:02.760><c> the</c><00:03:02.920><c> deletion</c><00:03:03.599><c> of</c><00:03:03.840><c> of</c>

00:03:03.949 --> 00:03:03.959 align:start position:0%
will cause um the the deletion of of
 

00:03:03.959 --> 00:03:05.509 align:start position:0%
will cause um the the deletion of of
artifacts<00:03:04.519><c> so</c><00:03:04.640><c> you</c><00:03:04.720><c> want</c><00:03:04.799><c> to</c><00:03:04.959><c> make</c><00:03:05.080><c> sure</c><00:03:05.280><c> as</c><00:03:05.360><c> an</c>

00:03:05.509 --> 00:03:05.519 align:start position:0%
artifacts so you want to make sure as an
 

00:03:05.519 --> 00:03:06.910 align:start position:0%
artifacts so you want to make sure as an
admin<00:03:05.840><c> you're</c><00:03:05.959><c> reading</c><00:03:06.319><c> these</c><00:03:06.599><c> very</c>

00:03:06.910 --> 00:03:06.920 align:start position:0%
admin you're reading these very
 

00:03:06.920 --> 00:03:09.470 align:start position:0%
admin you're reading these very
carefully<00:03:07.920><c> and</c><00:03:08.120><c> once</c><00:03:08.319><c> you</c><00:03:08.440><c> feel</c><00:03:08.720><c> like</c><00:03:09.319><c> you</c>

00:03:09.470 --> 00:03:09.480 align:start position:0%
carefully and once you feel like you
 

00:03:09.480 --> 00:03:11.229 align:start position:0%
carefully and once you feel like you
know<00:03:09.799><c> you</c><00:03:10.200><c> you</c><00:03:10.360><c> set</c><00:03:10.560><c> the</c><00:03:10.720><c> right</c><00:03:10.840><c> things</c><00:03:11.040><c> in</c>

00:03:11.229 --> 00:03:11.239 align:start position:0%
know you you set the right things in
 

00:03:11.239 --> 00:03:14.190 align:start position:0%
know you you set the right things in
place<00:03:11.760><c> you</c><00:03:11.879><c> can</c><00:03:12.080><c> go</c><00:03:12.280><c> ahead</c><00:03:12.879><c> and</c><00:03:13.280><c> save</c><00:03:13.720><c> the</c>

00:03:14.190 --> 00:03:14.200 align:start position:0%
place you can go ahead and save the
 

00:03:14.200 --> 00:03:17.509 align:start position:0%
place you can go ahead and save the
settings<00:03:15.200><c> cool</c><00:03:16.040><c> now</c><00:03:16.239><c> let's</c><00:03:16.440><c> see</c><00:03:17.080><c> what</c><00:03:17.319><c> this</c>

00:03:17.509 --> 00:03:17.519 align:start position:0%
settings cool now let's see what this
 

00:03:17.519 --> 00:03:20.390 align:start position:0%
settings cool now let's see what this
might<00:03:17.720><c> look</c><00:03:17.959><c> like</c><00:03:18.599><c> um</c><00:03:18.760><c> in</c><00:03:18.959><c> the</c><00:03:19.159><c> actual</c><00:03:19.599><c> project</c>

00:03:20.390 --> 00:03:20.400 align:start position:0%
might look like um in the actual project
 

00:03:20.400 --> 00:03:22.030 align:start position:0%
might look like um in the actual project
now<00:03:20.640><c> again</c><00:03:20.879><c> because</c><00:03:21.200><c> team</c><00:03:21.519><c> admins</c><00:03:21.920><c> and</c>

00:03:22.030 --> 00:03:22.040 align:start position:0%
now again because team admins and
 

00:03:22.040 --> 00:03:24.550 align:start position:0%
now again because team admins and
members<00:03:22.440><c> can</c><00:03:22.599><c> set</c><00:03:22.760><c> and</c><00:03:22.920><c> edit</c><00:03:23.159><c> TTL</c><00:03:24.159><c> The</c><00:03:24.319><c> View</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
members can set and edit TTL The View
 

00:03:24.560 --> 00:03:27.149 align:start position:0%
members can set and edit TTL The View
that<00:03:24.680><c> I'm</c><00:03:24.760><c> going</c><00:03:24.840><c> to</c><00:03:25.000><c> pull</c><00:03:25.200><c> up</c><00:03:25.480><c> now</c><00:03:26.440><c> in</c><00:03:26.680><c> this</c>

00:03:27.149 --> 00:03:27.159 align:start position:0%
that I'm going to pull up now in this
 

00:03:27.159 --> 00:03:30.789 align:start position:0%
that I'm going to pull up now in this
conveniently<00:03:28.159><c> located</c><00:03:28.720><c> TTL</c><00:03:29.319><c> project</c>

00:03:30.789 --> 00:03:30.799 align:start position:0%
conveniently located TTL project
 

00:03:30.799 --> 00:03:32.390 align:start position:0%
conveniently located TTL project
is<00:03:30.959><c> going</c><00:03:31.040><c> to</c><00:03:31.120><c> be</c><00:03:31.239><c> a</c><00:03:31.360><c> view</c><00:03:31.560><c> that</c><00:03:31.720><c> all</c><00:03:31.879><c> members</c>

00:03:32.390 --> 00:03:32.400 align:start position:0%
is going to be a view that all members
 

00:03:32.400 --> 00:03:36.149 align:start position:0%
is going to be a view that all members
and<00:03:32.599><c> admins</c><00:03:33.000><c> will</c><00:03:33.159><c> be</c><00:03:33.280><c> able</c><00:03:33.480><c> to</c><00:03:33.920><c> see</c><00:03:34.920><c> now</c><00:03:35.720><c> um</c>

00:03:36.149 --> 00:03:36.159 align:start position:0%
and admins will be able to see now um
 

00:03:36.159 --> 00:03:37.750 align:start position:0%
and admins will be able to see now um
you'll<00:03:36.400><c> also</c><00:03:36.599><c> see</c><00:03:36.879><c> here</c><00:03:37.080><c> that</c><00:03:37.239><c> I</c><00:03:37.360><c> already</c><00:03:37.599><c> have</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
you'll also see here that I already have
 

00:03:37.760 --> 00:03:40.390 align:start position:0%
you'll also see here that I already have
a<00:03:37.920><c> data</c><00:03:38.159><c> set</c><00:03:38.720><c> uh</c><00:03:38.879><c> set</c><00:03:39.120><c> up</c><00:03:39.640><c> that</c><00:03:39.879><c> contains</c><00:03:40.280><c> a</c>

00:03:40.390 --> 00:03:40.400 align:start position:0%
a data set uh set up that contains a
 

00:03:40.400 --> 00:03:41.990 align:start position:0%
a data set uh set up that contains a
bunch<00:03:40.599><c> of</c><00:03:40.720><c> different</c><00:03:40.959><c> versions</c><00:03:41.519><c> with</c><00:03:41.680><c> a</c><00:03:41.760><c> few</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
bunch of different versions with a few
 

00:03:42.000 --> 00:03:45.350 align:start position:0%
bunch of different versions with a few
different<00:03:42.640><c> aliases</c><00:03:43.640><c> um</c><00:03:43.799><c> so</c><00:03:44.159><c> in</c><00:03:44.360><c> this</c><00:03:44.599><c> case</c><00:03:45.239><c> um</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
different aliases um so in this case um
 

00:03:45.360 --> 00:03:46.789 align:start position:0%
different aliases um so in this case um
we're<00:03:45.480><c> going</c><00:03:45.599><c> to</c><00:03:45.760><c> be</c><00:03:45.879><c> working</c><00:03:46.200><c> with</c><00:03:46.319><c> a</c><00:03:46.519><c> data</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
we're going to be working with a data
 

00:03:46.799 --> 00:03:48.949 align:start position:0%
we're going to be working with a data
set<00:03:47.239><c> uh</c><00:03:47.360><c> that</c><00:03:47.599><c> contains</c><00:03:48.599><c> um</c><00:03:48.720><c> you</c><00:03:48.799><c> know</c>

00:03:48.949 --> 00:03:48.959 align:start position:0%
set uh that contains um you know
 

00:03:48.959 --> 00:03:51.229 align:start position:0%
set uh that contains um you know
clinical<00:03:49.439><c> data</c><00:03:50.000><c> and</c><00:03:50.200><c> is</c><00:03:50.360><c> it</c><00:03:50.599><c> a</c><00:03:50.720><c> good</c><00:03:50.879><c> example</c>

00:03:51.229 --> 00:03:51.239 align:start position:0%
clinical data and is it a good example
 

00:03:51.239 --> 00:03:53.630 align:start position:0%
clinical data and is it a good example
of<00:03:51.360><c> a</c><00:03:51.480><c> data</c><00:03:51.720><c> set</c><00:03:51.879><c> that</c><00:03:52.000><c> might</c><00:03:52.239><c> contain</c><00:03:52.920><c> um</c><00:03:53.079><c> pii</c>

00:03:53.630 --> 00:03:53.640 align:start position:0%
of a data set that might contain um pii
 

00:03:53.640 --> 00:03:56.789 align:start position:0%
of a data set that might contain um pii
this<00:03:53.760><c> is</c><00:03:53.879><c> actually</c><00:03:54.239><c> a</c><00:03:54.400><c> public</c><00:03:55.159><c> um</c><00:03:55.840><c> public</c><00:03:56.120><c> data</c>

00:03:56.789 --> 00:03:56.799 align:start position:0%
this is actually a public um public data
 

00:03:56.799 --> 00:03:59.030 align:start position:0%
this is actually a public um public data
uh<00:03:56.959><c> data</c><00:03:57.239><c> set</c><00:03:57.920><c> that</c><00:03:58.079><c> we</c><00:03:58.200><c> have</c><00:03:58.360><c> some</c><00:03:58.560><c> customers</c>

00:03:59.030 --> 00:03:59.040 align:start position:0%
uh data set that we have some customers
 

00:03:59.040 --> 00:04:00.309 align:start position:0%
uh data set that we have some customers
working<00:03:59.319><c> with</c><00:03:59.480><c> to</c><00:03:59.640><c> to</c><00:03:59.760><c> measure</c><00:04:00.040><c> kind</c><00:04:00.159><c> of</c>

00:04:00.309 --> 00:04:00.319 align:start position:0%
working with to to measure kind of
 

00:04:00.319 --> 00:04:02.470 align:start position:0%
working with to to measure kind of
quality<00:04:00.680><c> of</c><00:04:00.840><c> of</c><00:04:01.000><c> healthare</c><00:04:01.920><c> um</c><00:04:02.079><c> across</c><00:04:02.360><c> the</c>

00:04:02.470 --> 00:04:02.480 align:start position:0%
quality of of healthare um across the
 

00:04:02.480 --> 00:04:04.630 align:start position:0%
quality of of healthare um across the
United<00:04:02.799><c> States</c><00:04:03.280><c> and</c><00:04:03.400><c> you</c><00:04:03.519><c> can</c><00:04:03.680><c> imagine</c><00:04:04.200><c> that</c>

00:04:04.630 --> 00:04:04.640 align:start position:0%
United States and you can imagine that
 

00:04:04.640 --> 00:04:06.630 align:start position:0%
United States and you can imagine that
this<00:04:04.799><c> might</c><00:04:04.959><c> be</c><00:04:05.239><c> a</c><00:04:05.360><c> good</c><00:04:05.560><c> example</c><00:04:06.000><c> of</c><00:04:06.200><c> a</c><00:04:06.360><c> data</c>

00:04:06.630 --> 00:04:06.640 align:start position:0%
this might be a good example of a data
 

00:04:06.640 --> 00:04:08.589 align:start position:0%
this might be a good example of a data
set<00:04:06.840><c> that</c><00:04:07.079><c> has</c><00:04:07.560><c> uh</c><00:04:07.799><c> things</c><00:04:08.000><c> like</c><00:04:08.200><c> patient</c>

00:04:08.589 --> 00:04:08.599 align:start position:0%
set that has uh things like patient
 

00:04:08.599 --> 00:04:11.429 align:start position:0%
set that has uh things like patient
information<00:04:09.360><c> Etc</c><00:04:10.360><c> um</c><00:04:10.720><c> that</c><00:04:10.959><c> need</c><00:04:11.159><c> to</c><00:04:11.280><c> be</c>

00:04:11.429 --> 00:04:11.439 align:start position:0%
information Etc um that need to be
 

00:04:11.439 --> 00:04:13.710 align:start position:0%
information Etc um that need to be
deleted<00:04:12.280><c> um</c><00:04:12.480><c> according</c><00:04:12.760><c> to</c><00:04:13.120><c> regulations</c>

00:04:13.710 --> 00:04:13.720 align:start position:0%
deleted um according to regulations
 

00:04:13.720 --> 00:04:16.469 align:start position:0%
deleted um according to regulations
after<00:04:13.959><c> a</c><00:04:14.079><c> certain</c><00:04:14.400><c> amount</c><00:04:14.599><c> of</c><00:04:15.040><c> time</c><00:04:16.040><c> so</c><00:04:16.320><c> the</c>

00:04:16.469 --> 00:04:16.479 align:start position:0%
after a certain amount of time so the
 

00:04:16.479 --> 00:04:18.509 align:start position:0%
after a certain amount of time so the
first<00:04:16.680><c> thing</c><00:04:16.840><c> I'm</c><00:04:16.919><c> going</c><00:04:17.040><c> to</c><00:04:17.199><c> do</c><00:04:17.959><c> um</c><00:04:18.320><c> is</c>

00:04:18.509 --> 00:04:18.519 align:start position:0%
first thing I'm going to do um is
 

00:04:18.519 --> 00:04:21.030 align:start position:0%
first thing I'm going to do um is
actually<00:04:18.799><c> walk</c><00:04:19.400><c> through</c><00:04:20.400><c> you</c><00:04:20.560><c> know</c><00:04:20.680><c> you</c><00:04:20.880><c> can</c>

00:04:21.030 --> 00:04:21.040 align:start position:0%
actually walk through you know you can
 

00:04:21.040 --> 00:04:23.870 align:start position:0%
actually walk through you know you can
see<00:04:21.400><c> here</c><00:04:22.160><c> um</c><00:04:22.800><c> because</c><00:04:23.080><c> we</c><00:04:23.199><c> have</c><00:04:23.320><c> this</c><00:04:23.440><c> default</c>

00:04:23.870 --> 00:04:23.880 align:start position:0%
see here um because we have this default
 

00:04:23.880 --> 00:04:27.230 align:start position:0%
see here um because we have this default
policy<00:04:24.280><c> set</c><00:04:24.680><c> there'll</c><00:04:24.960><c> already</c><00:04:25.240><c> be</c><00:04:25.360><c> a</c><00:04:25.479><c> TTL</c>

00:04:27.230 --> 00:04:27.240 align:start position:0%
policy set there'll already be a TTL
 

00:04:27.240 --> 00:04:29.670 align:start position:0%
policy set there'll already be a TTL
remaining<00:04:28.240><c> and</c><00:04:28.440><c> just</c><00:04:28.560><c> to</c><00:04:28.759><c> click</c><00:04:29.000><c> through</c><00:04:29.280><c> and</c>

00:04:29.670 --> 00:04:29.680 align:start position:0%
remaining and just to click through and
 

00:04:29.680 --> 00:04:30.830 align:start position:0%
remaining and just to click through and
prove<00:04:29.919><c> that</c><00:04:30.039><c> kind</c><00:04:30.160><c> of</c><00:04:30.280><c> all</c><00:04:30.479><c> all</c><00:04:30.600><c> of</c><00:04:30.720><c> the</c>

00:04:30.830 --> 00:04:30.840 align:start position:0%
prove that kind of all all of the
 

00:04:30.840 --> 00:04:33.430 align:start position:0%
prove that kind of all all of the
versions<00:04:31.360><c> will</c><00:04:31.560><c> we'll</c><00:04:31.759><c> get</c><00:04:31.960><c> this</c><00:04:32.600><c> um</c><00:04:33.280><c> one</c>

00:04:33.430 --> 00:04:33.440 align:start position:0%
versions will we'll get this um one
 

00:04:33.440 --> 00:04:36.150 align:start position:0%
versions will we'll get this um one
thing<00:04:33.600><c> to</c><00:04:33.759><c> know</c><00:04:34.039><c> is</c><00:04:34.120><c> you'll</c><00:04:34.320><c> see</c><00:04:35.320><c> hey</c><00:04:35.960><c> um</c><00:04:36.039><c> you</c>

00:04:36.150 --> 00:04:36.160 align:start position:0%
thing to know is you'll see hey um you
 

00:04:36.160 --> 00:04:39.670 align:start position:0%
thing to know is you'll see hey um you
know<00:04:36.479><c> I</c><00:04:36.680><c> I</c><00:04:36.800><c> I</c><00:04:36.880><c> saw</c><00:04:37.120><c> you</c><00:04:37.320><c> said</c><00:04:37.680><c> it</c><00:04:37.880><c> for</c><00:04:38.639><c> um</c><00:04:39.000><c> around</c>

00:04:39.670 --> 00:04:39.680 align:start position:0%
know I I I saw you said it for um around
 

00:04:39.680 --> 00:04:43.350 align:start position:0%
know I I I saw you said it for um around
you<00:04:39.800><c> know</c><00:04:39.960><c> 365</c><00:04:40.919><c> days</c><00:04:41.600><c> why</c><00:04:41.759><c> is</c><00:04:41.960><c> this</c><00:04:42.120><c> not</c><00:04:42.360><c> a</c><00:04:42.520><c> year</c>

00:04:43.350 --> 00:04:43.360 align:start position:0%
you know 365 days why is this not a year
 

00:04:43.360 --> 00:04:45.670 align:start position:0%
you know 365 days why is this not a year
well<00:04:43.680><c> one</c><00:04:43.919><c> thing</c><00:04:44.199><c> to</c><00:04:44.440><c> remember</c><00:04:45.000><c> is</c><00:04:45.160><c> that</c><00:04:45.320><c> we</c>

00:04:45.670 --> 00:04:45.680 align:start position:0%
well one thing to remember is that we
 

00:04:45.680 --> 00:04:47.710 align:start position:0%
well one thing to remember is that we
calculate<00:04:46.080><c> TTL</c><00:04:46.639><c> based</c><00:04:46.919><c> off</c><00:04:47.080><c> of</c><00:04:47.280><c> when</c><00:04:47.560><c> the</c>

00:04:47.710 --> 00:04:47.720 align:start position:0%
calculate TTL based off of when the
 

00:04:47.720 --> 00:04:49.670 align:start position:0%
calculate TTL based off of when the
artifact<00:04:48.240><c> was</c><00:04:48.440><c> created</c><00:04:48.960><c> so</c><00:04:49.160><c> since</c><00:04:49.360><c> it</c><00:04:49.479><c> was</c>

00:04:49.670 --> 00:04:49.680 align:start position:0%
artifact was created so since it was
 

00:04:49.680 --> 00:04:53.710 align:start position:0%
artifact was created so since it was
created<00:04:50.680><c> um</c><00:04:51.160><c> about</c><00:04:51.600><c> uh</c><00:04:51.759><c> 12</c><00:04:52.120><c> days</c><00:04:52.320><c> ago</c><00:04:53.120><c> then</c><00:04:53.400><c> it</c>

00:04:53.710 --> 00:04:53.720 align:start position:0%
created um about uh 12 days ago then it
 

00:04:53.720 --> 00:04:57.310 align:start position:0%
created um about uh 12 days ago then it
has<00:04:54.120><c> essentially</c><00:04:55.120><c> um</c><00:04:55.240><c> you</c><00:04:55.360><c> know</c><00:04:55.639><c> 365</c><00:04:56.639><c> minus</c><00:04:57.000><c> 12</c>

00:04:57.310 --> 00:04:57.320 align:start position:0%
has essentially um you know 365 minus 12
 

00:04:57.320 --> 00:05:00.950 align:start position:0%
has essentially um you know 365 minus 12
days<00:04:57.919><c> remaining</c><00:04:58.320><c> until</c><00:04:58.720><c> expiration</c><00:04:59.240><c> happen</c>

00:05:00.950 --> 00:05:00.960 align:start position:0%
days remaining until expiration happen
 

00:05:00.960 --> 00:05:03.790 align:start position:0%
days remaining until expiration happen
now<00:05:01.120><c> I'm</c><00:05:01.240><c> going</c><00:05:01.320><c> to</c><00:05:01.600><c> go</c><00:05:01.960><c> to</c><00:05:02.639><c> uh</c><00:05:02.880><c> this</c><00:05:03.240><c> version</c>

00:05:03.790 --> 00:05:03.800 align:start position:0%
now I'm going to go to uh this version
 

00:05:03.800 --> 00:05:06.790 align:start position:0%
now I'm going to go to uh this version
here<00:05:04.400><c> um</c><00:05:04.560><c> you'll</c><00:05:04.759><c> notice</c><00:05:05.080><c> it</c><00:05:05.360><c> has</c><00:05:05.960><c> this</c><00:05:06.560><c> uh</c>

00:05:06.790 --> 00:05:06.800 align:start position:0%
here um you'll notice it has this uh
 

00:05:06.800 --> 00:05:09.350 align:start position:0%
here um you'll notice it has this uh
Alias<00:05:07.240><c> that</c><00:05:07.360><c> says</c><00:05:07.639><c> pii</c><00:05:08.120><c> removed</c><00:05:08.720><c> so</c><00:05:09.199><c> we're</c>

00:05:09.350 --> 00:05:09.360 align:start position:0%
Alias that says pii removed so we're
 

00:05:09.360 --> 00:05:11.070 align:start position:0%
Alias that says pii removed so we're
going<00:05:09.479><c> to</c><00:05:09.680><c> consider</c><00:05:10.120><c> this</c><00:05:10.320><c> some</c><00:05:10.520><c> kind</c><00:05:10.639><c> of</c><00:05:10.800><c> data</c>

00:05:11.070 --> 00:05:11.080 align:start position:0%
going to consider this some kind of data
 

00:05:11.080 --> 00:05:13.749 align:start position:0%
going to consider this some kind of data
set<00:05:11.199><c> that</c><00:05:11.360><c> went</c><00:05:11.560><c> through</c><00:05:12.160><c> processing</c><00:05:13.160><c> and</c><00:05:13.680><c> uh</c>

00:05:13.749 --> 00:05:13.759 align:start position:0%
set that went through processing and uh
 

00:05:13.759 --> 00:05:17.189 align:start position:0%
set that went through processing and uh
it<00:05:13.880><c> was</c><00:05:14.039><c> de</c><00:05:14.240><c> anonymized</c><00:05:15.160><c> and</c><00:05:15.680><c> the</c><00:05:16.639><c> yeah</c><00:05:17.000><c> all</c>

00:05:17.189 --> 00:05:17.199 align:start position:0%
it was de anonymized and the yeah all
 

00:05:17.199 --> 00:05:20.029 align:start position:0%
it was de anonymized and the yeah all
forms<00:05:17.479><c> of</c><00:05:17.680><c> identification</c><00:05:18.319><c> were</c><00:05:18.600><c> removed</c><00:05:19.479><c> so</c>

00:05:20.029 --> 00:05:20.039 align:start position:0%
forms of identification were removed so
 

00:05:20.039 --> 00:05:22.110 align:start position:0%
forms of identification were removed so
this<00:05:20.160><c> is</c><00:05:20.360><c> actually</c><00:05:20.560><c> a</c><00:05:20.680><c> data</c><00:05:20.960><c> set</c><00:05:21.160><c> that</c><00:05:21.360><c> we</c><00:05:21.919><c> can</c>

00:05:22.110 --> 00:05:22.120 align:start position:0%
this is actually a data set that we can
 

00:05:22.120 --> 00:05:23.710 align:start position:0%
this is actually a data set that we can
decide<00:05:22.800><c> we</c><00:05:22.919><c> want</c><00:05:23.039><c> to</c><00:05:23.120><c> leave</c><00:05:23.319><c> this</c><00:05:23.520><c> for</c><00:05:23.639><c> a</c>

00:05:23.710 --> 00:05:23.720 align:start position:0%
decide we want to leave this for a
 

00:05:23.720 --> 00:05:26.870 align:start position:0%
decide we want to leave this for a
little<00:05:23.880><c> bit</c><00:05:24.039><c> longer</c><00:05:24.600><c> and</c><00:05:25.199><c> um</c><00:05:25.560><c> either</c><00:05:26.080><c> revise</c>

00:05:26.870 --> 00:05:26.880 align:start position:0%
little bit longer and um either revise
 

00:05:26.880 --> 00:05:28.710 align:start position:0%
little bit longer and um either revise
the<00:05:27.080><c> TTL</c><00:05:27.639><c> policy</c><00:05:27.919><c> to</c><00:05:28.080><c> make</c><00:05:28.199><c> it</c><00:05:28.319><c> a</c><00:05:28.400><c> little</c><00:05:28.560><c> bit</c>

00:05:28.710 --> 00:05:28.720 align:start position:0%
the TTL policy to make it a little bit
 

00:05:28.720 --> 00:05:31.710 align:start position:0%
the TTL policy to make it a little bit
longer<00:05:29.759><c> um</c><00:05:29.960><c> or</c><00:05:30.120><c> just</c><00:05:30.319><c> get</c><00:05:30.479><c> rid</c><00:05:30.639><c> of</c><00:05:30.720><c> it</c><00:05:31.039><c> together</c>

00:05:31.710 --> 00:05:31.720 align:start position:0%
longer um or just get rid of it together
 

00:05:31.720 --> 00:05:34.469 align:start position:0%
longer um or just get rid of it together
so<00:05:31.880><c> what</c><00:05:32.000><c> we're</c><00:05:32.120><c> going</c><00:05:32.240><c> to</c><00:05:32.360><c> do</c><00:05:32.560><c> is</c><00:05:32.720><c> say</c><00:05:33.560><c> hey</c><00:05:34.400><c> I</c>

00:05:34.469 --> 00:05:34.479 align:start position:0%
so what we're going to do is say hey I
 

00:05:34.479 --> 00:05:36.189 align:start position:0%
so what we're going to do is say hey I
want<00:05:34.639><c> you</c><00:05:34.759><c> to</c><00:05:34.880><c> no</c><00:05:35.080><c> longer</c><00:05:35.360><c> inherit</c><00:05:35.880><c> directly</c>

00:05:36.189 --> 00:05:36.199 align:start position:0%
want you to no longer inherit directly
 

00:05:36.199 --> 00:05:38.270 align:start position:0%
want you to no longer inherit directly
from<00:05:36.319><c> the</c><00:05:36.440><c> team's</c><00:05:36.800><c> default</c><00:05:37.319><c> instead</c><00:05:38.160><c> I'm</c>

00:05:38.270 --> 00:05:38.280 align:start position:0%
from the team's default instead I'm
 

00:05:38.280 --> 00:05:40.830 align:start position:0%
from the team's default instead I'm
going<00:05:38.400><c> to</c><00:05:38.560><c> go</c><00:05:38.680><c> in</c><00:05:38.840><c> and</c><00:05:39.039><c> say</c><00:05:39.919><c> I</c><00:05:40.039><c> actually</c><00:05:40.280><c> want</c><00:05:40.639><c> a</c>

00:05:40.830 --> 00:05:40.840 align:start position:0%
going to go in and say I actually want a
 

00:05:40.840 --> 00:05:43.790 align:start position:0%
going to go in and say I actually want a
a<00:05:40.960><c> longer</c><00:05:41.319><c> one</c><00:05:41.680><c> of</c><00:05:42.160><c> a</c><00:05:42.319><c> thousand</c><00:05:42.720><c> days</c>

00:05:43.790 --> 00:05:43.800 align:start position:0%
a longer one of a thousand days
 

00:05:43.800 --> 00:05:48.350 align:start position:0%
a longer one of a thousand days
potentially<00:05:44.800><c> so</c><00:05:45.000><c> I</c><00:05:45.080><c> can</c><00:05:45.280><c> go</c><00:05:45.440><c> ahead</c><00:05:45.960><c> and</c><00:05:46.440><c> update</c>

00:05:48.350 --> 00:05:48.360 align:start position:0%
potentially so I can go ahead and update
 

00:05:48.360 --> 00:05:51.430 align:start position:0%
potentially so I can go ahead and update
this<00:05:49.360><c> and</c><00:05:49.600><c> it'll</c><00:05:49.960><c> go</c><00:05:50.199><c> ahead</c><00:05:50.639><c> and</c><00:05:51.080><c> as</c><00:05:51.199><c> you</c><00:05:51.319><c> can</c>

00:05:51.430 --> 00:05:51.440 align:start position:0%
this and it'll go ahead and as you can
 

00:05:51.440 --> 00:05:54.110 align:start position:0%
this and it'll go ahead and as you can
see<00:05:51.759><c> here</c><00:05:52.280><c> update</c><00:05:52.639><c> it</c><00:05:52.759><c> to</c><00:05:52.960><c> be</c><00:05:53.240><c> slightly</c><00:05:53.759><c> longer</c>

00:05:54.110 --> 00:05:54.120 align:start position:0%
see here update it to be slightly longer
 

00:05:54.120 --> 00:05:56.270 align:start position:0%
see here update it to be slightly longer
duration<00:05:54.840><c> you</c><00:05:54.960><c> can</c><00:05:55.080><c> see</c><00:05:55.360><c> the</c><00:05:55.680><c> adjustment</c><00:05:56.080><c> to</c>

00:05:56.270 --> 00:05:56.280 align:start position:0%
duration you can see the adjustment to
 

00:05:56.280 --> 00:05:59.790 align:start position:0%
duration you can see the adjustment to
2.7<00:05:56.960><c> years</c><00:05:57.560><c> remaining</c><00:05:58.560><c> um</c><00:05:58.840><c> cool</c><00:05:59.240><c> and</c><00:05:59.319><c> then</c>

00:05:59.790 --> 00:05:59.800 align:start position:0%
2.7 years remaining um cool and then
 

00:05:59.800 --> 00:06:01.629 align:start position:0%
2.7 years remaining um cool and then
let's<00:06:00.039><c> take</c><00:06:00.160><c> a</c><00:06:00.319><c> look</c><00:06:00.479><c> at</c><00:06:00.840><c> another</c><00:06:01.280><c> version</c>

00:06:01.629 --> 00:06:01.639 align:start position:0%
let's take a look at another version
 

00:06:01.639 --> 00:06:03.950 align:start position:0%
let's take a look at another version
here<00:06:01.880><c> just</c><00:06:02.000><c> so</c><00:06:02.160><c> I</c><00:06:02.240><c> can</c><00:06:02.400><c> walk</c><00:06:02.680><c> through</c><00:06:03.560><c> if</c>

00:06:03.950 --> 00:06:03.960 align:start position:0%
here just so I can walk through if
 

00:06:03.960 --> 00:06:06.309 align:start position:0%
here just so I can walk through if
rather<00:06:04.240><c> than</c><00:06:04.520><c> updating</c><00:06:05.000><c> it</c><00:06:05.400><c> um</c><00:06:05.479><c> to</c><00:06:05.639><c> be</c><00:06:05.800><c> longer</c>

00:06:06.309 --> 00:06:06.319 align:start position:0%
rather than updating it um to be longer
 

00:06:06.319 --> 00:06:08.029 align:start position:0%
rather than updating it um to be longer
you<00:06:06.520><c> also</c><00:06:06.759><c> just</c><00:06:06.919><c> have</c><00:06:07.080><c> the</c><00:06:07.240><c> option</c><00:06:07.759><c> of</c>

00:06:08.029 --> 00:06:08.039 align:start position:0%
you also just have the option of
 

00:06:08.039 --> 00:06:10.550 align:start position:0%
you also just have the option of
deactivating<00:06:08.919><c> the</c><00:06:09.039><c> TTL</c><00:06:09.599><c> policy</c><00:06:09.960><c> completely</c>

00:06:10.550 --> 00:06:10.560 align:start position:0%
deactivating the TTL policy completely
 

00:06:10.560 --> 00:06:12.710 align:start position:0%
deactivating the TTL policy completely
so<00:06:10.720><c> you</c><00:06:10.800><c> would</c><00:06:10.960><c> also</c><00:06:11.199><c> jump</c><00:06:11.440><c> to</c><00:06:11.639><c> this</c><00:06:11.800><c> edit</c>

00:06:12.710 --> 00:06:12.720 align:start position:0%
so you would also jump to this edit
 

00:06:12.720 --> 00:06:15.309 align:start position:0%
so you would also jump to this edit
view<00:06:13.720><c> again</c><00:06:14.199><c> instead</c><00:06:14.520><c> of</c><00:06:14.680><c> inheriting</c><00:06:15.160><c> from</c>

00:06:15.309 --> 00:06:15.319 align:start position:0%
view again instead of inheriting from
 

00:06:15.319 --> 00:06:18.749 align:start position:0%
view again instead of inheriting from
the<00:06:15.479><c> team's</c><00:06:15.800><c> default</c><00:06:16.520><c> which</c><00:06:17.000><c> is</c><00:06:17.280><c> 365</c><00:06:18.280><c> days</c><00:06:18.599><c> as</c>

00:06:18.749 --> 00:06:18.759 align:start position:0%
the team's default which is 365 days as
 

00:06:18.759 --> 00:06:20.710 align:start position:0%
the team's default which is 365 days as
we<00:06:18.919><c> remember</c><00:06:19.280><c> setting</c><00:06:19.960><c> together</c><00:06:20.199><c> in</c><00:06:20.319><c> the</c><00:06:20.440><c> team</c>

00:06:20.710 --> 00:06:20.720 align:start position:0%
we remember setting together in the team
 

00:06:20.720 --> 00:06:22.790 align:start position:0%
we remember setting together in the team
settings<00:06:21.039><c> earlier</c><00:06:21.800><c> we're</c><00:06:22.000><c> going</c><00:06:22.120><c> to</c>

00:06:22.790 --> 00:06:22.800 align:start position:0%
settings earlier we're going to
 

00:06:22.800 --> 00:06:24.230 align:start position:0%
settings earlier we're going to
deactivate<00:06:23.360><c> it</c><00:06:23.479><c> completely</c><00:06:23.919><c> we're</c><00:06:24.039><c> not</c><00:06:24.120><c> going</c>

00:06:24.230 --> 00:06:24.240 align:start position:0%
deactivate it completely we're not going
 

00:06:24.240 --> 00:06:25.950 align:start position:0%
deactivate it completely we're not going
to<00:06:24.319><c> set</c><00:06:24.479><c> a</c><00:06:24.599><c> custom</c><00:06:24.919><c> one</c><00:06:25.160><c> we're</c><00:06:25.319><c> going</c><00:06:25.400><c> to</c><00:06:25.560><c> say</c>

00:06:25.950 --> 00:06:25.960 align:start position:0%
to set a custom one we're going to say
 

00:06:25.960 --> 00:06:28.110 align:start position:0%
to set a custom one we're going to say
hey<00:06:26.319><c> we're</c><00:06:26.520><c> just</c><00:06:26.639><c> going</c><00:06:26.759><c> to</c><00:06:27.280><c> go</c><00:06:27.440><c> ahead</c><00:06:27.759><c> exclude</c>

00:06:28.110 --> 00:06:28.120 align:start position:0%
hey we're just going to go ahead exclude
 

00:06:28.120 --> 00:06:34.550 align:start position:0%
hey we're just going to go ahead exclude
it<00:06:28.319><c> from</c><00:06:28.680><c> any</c><00:06:29.280><c> uh</c><00:06:29.520><c> automated</c><00:06:30.479><c> automated</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
 
 

00:06:34.560 --> 00:06:36.469 align:start position:0%
 
deletion<00:06:35.560><c> nice</c><00:06:35.880><c> and</c><00:06:35.960><c> you</c><00:06:36.039><c> can</c><00:06:36.160><c> see</c><00:06:36.319><c> that</c>

00:06:36.469 --> 00:06:36.479 align:start position:0%
deletion nice and you can see that
 

00:06:36.479 --> 00:06:38.510 align:start position:0%
deletion nice and you can see that
that's<00:06:36.639><c> been</c><00:06:36.800><c> updated</c><00:06:37.240><c> here</c><00:06:37.680><c> and</c><00:06:37.800><c> the</c><00:06:37.919><c> TTL</c>

00:06:38.510 --> 00:06:38.520 align:start position:0%
that's been updated here and the TTL
 

00:06:38.520 --> 00:06:41.629 align:start position:0%
that's been updated here and the TTL
remaining<00:06:38.960><c> has</c><00:06:39.160><c> been</c><00:06:39.639><c> uh</c><00:06:39.960><c> returned</c><00:06:40.680><c> to</c>

00:06:41.629 --> 00:06:41.639 align:start position:0%
remaining has been uh returned to
 

00:06:41.639 --> 00:06:44.390 align:start position:0%
remaining has been uh returned to
inactive<00:06:42.639><c> um</c><00:06:42.800><c> note</c><00:06:43.160><c> that</c><00:06:43.479><c> I</c><00:06:43.919><c> went</c><00:06:44.160><c> through</c>

00:06:44.390 --> 00:06:44.400 align:start position:0%
inactive um note that I went through
 

00:06:44.400 --> 00:06:46.350 align:start position:0%
inactive um note that I went through
this<00:06:44.599><c> process</c><00:06:44.880><c> for</c><00:06:45.120><c> data</c><00:06:45.560><c> sets</c><00:06:45.919><c> it</c><00:06:46.039><c> can</c><00:06:46.199><c> be</c>

00:06:46.350 --> 00:06:46.360 align:start position:0%
this process for data sets it can be
 

00:06:46.360 --> 00:06:48.790 align:start position:0%
this process for data sets it can be
applied<00:06:46.720><c> to</c><00:06:46.880><c> any</c><00:06:47.120><c> artifacts</c><00:06:48.080><c> um</c><00:06:48.280><c> as</c><00:06:48.400><c> you</c><00:06:48.560><c> see</c>

00:06:48.790 --> 00:06:48.800 align:start position:0%
applied to any artifacts um as you see
 

00:06:48.800 --> 00:06:50.830 align:start position:0%
applied to any artifacts um as you see
fit<00:06:49.120><c> depending</c><00:06:49.479><c> on</c><00:06:50.000><c> you</c><00:06:50.120><c> know</c><00:06:50.280><c> the</c><00:06:50.400><c> use</c><00:06:50.680><c> case</c>

00:06:50.830 --> 00:06:50.840 align:start position:0%
fit depending on you know the use case
 

00:06:50.840 --> 00:06:52.790 align:start position:0%
fit depending on you know the use case
your<00:06:51.000><c> team</c><00:06:51.319><c> has</c><00:06:51.960><c> and</c><00:06:52.120><c> thank</c><00:06:52.280><c> you</c><00:06:52.400><c> so</c><00:06:52.560><c> much</c>

00:06:52.790 --> 00:06:52.800 align:start position:0%
your team has and thank you so much
 

00:06:52.800 --> 00:06:55.070 align:start position:0%
your team has and thank you so much
that's<00:06:53.000><c> all</c><00:06:53.160><c> I</c><00:06:53.280><c> wanted</c><00:06:53.599><c> to</c><00:06:53.800><c> talk</c><00:06:54.000><c> today</c><00:06:54.520><c> about</c>

00:06:55.070 --> 00:06:55.080 align:start position:0%
that's all I wanted to talk today about
 

00:06:55.080 --> 00:06:57.749 align:start position:0%
that's all I wanted to talk today about
using<00:06:55.879><c> uh</c><00:06:56.000><c> TTL</c><00:06:56.560><c> and</c><00:06:56.680><c> weights</c><00:06:56.919><c> and</c><00:06:57.039><c> biases</c><00:06:57.440><c> for</c>

00:06:57.749 --> 00:06:57.759 align:start position:0%
using uh TTL and weights and biases for
 

00:06:57.759 --> 00:07:00.510 align:start position:0%
using uh TTL and weights and biases for
artifacts<00:06:58.759><c> um</c><00:06:58.879><c> for</c><00:06:59.039><c> enter</c><00:06:59.479><c> priz</c><00:06:59.759><c> teams</c><00:07:00.080><c> to</c><00:07:00.319><c> to</c>

00:07:00.510 --> 00:07:00.520 align:start position:0%
artifacts um for enter priz teams to to
 

00:07:00.520 --> 00:07:03.670 align:start position:0%
artifacts um for enter priz teams to to
better<00:07:01.240><c> manage</c><00:07:02.000><c> uh</c><00:07:02.120><c> the</c><00:07:02.319><c> content</c><00:07:02.919><c> that</c><00:07:03.039><c> you're</c>

00:07:03.670 --> 00:07:03.680 align:start position:0%
better manage uh the content that you're
 

00:07:03.680 --> 00:07:05.830 align:start position:0%
better manage uh the content that you're
um<00:07:03.800><c> using</c><00:07:04.080><c> in</c><00:07:04.199><c> weights</c><00:07:04.440><c> and</c><00:07:04.560><c> vises</c><00:07:05.160><c> and</c><00:07:05.560><c> give</c>

00:07:05.830 --> 00:07:05.840 align:start position:0%
um using in weights and vises and give
 

00:07:05.840 --> 00:07:06.950 align:start position:0%
um using in weights and vises and give
give<00:07:05.960><c> you</c><00:07:06.080><c> the</c><00:07:06.199><c> opportunity</c><00:07:06.639><c> to</c><00:07:06.720><c> make</c><00:07:06.840><c> sure</c>

00:07:06.950 --> 00:07:06.960 align:start position:0%
give you the opportunity to make sure
 

00:07:06.960 --> 00:07:08.430 align:start position:0%
give you the opportunity to make sure
you're<00:07:07.120><c> staying</c><00:07:07.360><c> in</c><00:07:07.560><c> compliance</c><00:07:08.120><c> or</c><00:07:08.280><c> or</c>

00:07:08.430 --> 00:07:08.440 align:start position:0%
you're staying in compliance or or
 

00:07:08.440 --> 00:07:10.100 align:start position:0%
you're staying in compliance or or
managing<00:07:08.879><c> your</c><00:07:09.120><c> your</c>

00:07:10.100 --> 00:07:10.110 align:start position:0%
managing your your
 

00:07:10.110 --> 00:07:26.469 align:start position:0%
managing your your
[Music]

00:07:26.469 --> 00:07:26.479 align:start position:0%
 
 

00:07:26.479 --> 00:07:29.479 align:start position:0%
 
storage

