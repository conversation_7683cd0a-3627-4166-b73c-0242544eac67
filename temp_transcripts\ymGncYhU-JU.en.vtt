WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.709 align:start position:0%
 
the<00:00:00.199><c> next</c><00:00:00.359><c> thing</c><00:00:00.520><c> we're</c><00:00:00.640><c> going</c><00:00:00.760><c> to</c><00:00:00.919><c> do</c><00:00:01.280><c> is</c><00:00:01.480><c> set</c>

00:00:01.709 --> 00:00:01.719 align:start position:0%
the next thing we're going to do is set
 

00:00:01.719 --> 00:00:04.789 align:start position:0%
the next thing we're going to do is set
up<00:00:02.320><c> web</c><00:00:02.600><c> Hooks</c><00:00:02.840><c> and</c><00:00:02.960><c> weights</c><00:00:03.199><c> and</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
up web Hooks and weights and
 

00:00:04.799 --> 00:00:07.230 align:start position:0%
up web Hooks and weights and
biases<00:00:05.799><c> now</c><00:00:05.960><c> just</c><00:00:06.080><c> to</c><00:00:06.279><c> remind</c><00:00:06.600><c> you</c><00:00:07.000><c> there's</c>

00:00:07.230 --> 00:00:07.240 align:start position:0%
biases now just to remind you there's
 

00:00:07.240 --> 00:00:09.669 align:start position:0%
biases now just to remind you there's
this<00:00:07.439><c> read</c><00:00:07.720><c> me</c><00:00:08.679><c> um</c><00:00:08.960><c> with</c><00:00:09.120><c> this</c><00:00:09.480><c> with</c><00:00:09.559><c> the</c>

00:00:09.669 --> 00:00:09.679 align:start position:0%
this read me um with this with the
 

00:00:09.679 --> 00:00:11.629 align:start position:0%
this read me um with this with the
GitHub<00:00:10.080><c> project</c><00:00:10.320><c> that</c><00:00:10.440><c> I</c><00:00:10.519><c> shared</c><00:00:10.960><c> the</c><00:00:11.120><c> W</c><00:00:11.360><c> DB</c>

00:00:11.629 --> 00:00:11.639 align:start position:0%
GitHub project that I shared the W DB
 

00:00:11.639 --> 00:00:15.629 align:start position:0%
GitHub project that I shared the W DB
modal<00:00:12.000><c> web</c><00:00:12.240><c> Hook</c><00:00:13.160><c> and</c><00:00:13.280><c> the</c><00:00:13.440><c> read</c><00:00:13.920><c> me</c><00:00:14.920><c> right</c>

00:00:15.629 --> 00:00:15.639 align:start position:0%
modal web Hook and the read me right
 

00:00:15.639 --> 00:00:17.349 align:start position:0%
modal web Hook and the read me right
describes<00:00:16.160><c> all</c><00:00:16.279><c> of</c><00:00:16.400><c> the</c><00:00:16.520><c> steps</c><00:00:16.840><c> I'm</c><00:00:17.119><c> going</c>

00:00:17.349 --> 00:00:17.359 align:start position:0%
describes all of the steps I'm going
 

00:00:17.359 --> 00:00:18.910 align:start position:0%
describes all of the steps I'm going
through<00:00:17.640><c> right</c><00:00:17.800><c> now</c><00:00:18.080><c> so</c><00:00:18.240><c> if</c><00:00:18.279><c> you</c><00:00:18.400><c> get</c><00:00:18.520><c> lost</c><00:00:18.760><c> at</c>

00:00:18.910 --> 00:00:18.920 align:start position:0%
through right now so if you get lost at
 

00:00:18.920 --> 00:00:21.390 align:start position:0%
through right now so if you get lost at
any<00:00:19.119><c> point</c><00:00:19.320><c> or</c><00:00:19.520><c> want</c><00:00:19.600><c> to</c><00:00:19.840><c> refer</c><00:00:20.760><c> to</c><00:00:20.920><c> a</c><00:00:21.039><c> written</c>

00:00:21.390 --> 00:00:21.400 align:start position:0%
any point or want to refer to a written
 

00:00:21.400 --> 00:00:24.349 align:start position:0%
any point or want to refer to a written
version<00:00:21.920><c> of</c><00:00:22.160><c> this</c><00:00:22.800><c> tutorial</c><00:00:23.800><c> uh</c><00:00:24.000><c> you</c><00:00:24.119><c> can</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
version of this tutorial uh you can
 

00:00:24.359 --> 00:00:25.830 align:start position:0%
version of this tutorial uh you can
reference<00:00:24.720><c> the</c><00:00:24.840><c> read</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
reference the read
 

00:00:25.840 --> 00:00:29.109 align:start position:0%
reference the read
me<00:00:26.840><c> so</c><00:00:27.080><c> to</c><00:00:27.240><c> set</c><00:00:27.400><c> up</c><00:00:27.560><c> web</c><00:00:27.800><c> hooks</c><00:00:28.560><c> you</c><00:00:28.679><c> want</c><00:00:28.800><c> to</c><00:00:29.000><c> go</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
me so to set up web hooks you want to go
 

00:00:29.119 --> 00:00:31.910 align:start position:0%
me so to set up web hooks you want to go
to<00:00:29.240><c> your</c><00:00:29.439><c> teams</c><00:00:30.039><c> page</c><00:00:30.679><c> so</c><00:00:31.119><c> my</c><00:00:31.359><c> the</c><00:00:31.439><c> name</c><00:00:31.599><c> of</c><00:00:31.759><c> my</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
to your teams page so my the name of my
 

00:00:31.920 --> 00:00:33.549 align:start position:0%
to your teams page so my the name of my
team<00:00:32.239><c> is</c><00:00:32.399><c> review</c>

00:00:33.549 --> 00:00:33.559 align:start position:0%
team is review
 

00:00:33.559 --> 00:00:35.869 align:start position:0%
team is review
Co<00:00:34.559><c> and</c><00:00:34.719><c> you're</c><00:00:34.879><c> going</c><00:00:35.000><c> to</c><00:00:35.120><c> want</c><00:00:35.239><c> to</c><00:00:35.399><c> set</c><00:00:35.600><c> up</c><00:00:35.760><c> a</c>

00:00:35.869 --> 00:00:35.879 align:start position:0%
Co and you're going to want to set up a
 

00:00:35.879 --> 00:00:38.030 align:start position:0%
Co and you're going to want to set up a
couple<00:00:36.079><c> of</c><00:00:36.280><c> things</c><00:00:37.239><c> so</c><00:00:37.480><c> first</c><00:00:37.719><c> you're</c><00:00:37.960><c> going</c>

00:00:38.030 --> 00:00:38.040 align:start position:0%
couple of things so first you're going
 

00:00:38.040 --> 00:00:41.270 align:start position:0%
couple of things so first you're going
to<00:00:38.120><c> want</c><00:00:38.239><c> to</c><00:00:38.399><c> send</c><00:00:38.640><c> up</c><00:00:39.160><c> a</c><00:00:39.879><c> secret</c><00:00:40.879><c> I</c><00:00:41.000><c> already</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
to want to send up a secret I already
 

00:00:41.280 --> 00:00:43.430 align:start position:0%
to want to send up a secret I already
have<00:00:41.440><c> the</c><00:00:41.559><c> secret</c><00:00:42.039><c> here</c><00:00:42.600><c> but</c><00:00:42.800><c> basically</c><00:00:43.360><c> what</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
have the secret here but basically what
 

00:00:43.440 --> 00:00:46.470 align:start position:0%
have the secret here but basically what
you<00:00:43.600><c> do</c><00:00:43.879><c> is</c><00:00:44.719><c> you</c><00:00:44.920><c> click</c><00:00:45.160><c> on</c><00:00:45.399><c> this</c><00:00:46.079><c> It'll</c><00:00:46.320><c> ask</c>

00:00:46.470 --> 00:00:46.480 align:start position:0%
you do is you click on this It'll ask
 

00:00:46.480 --> 00:00:48.709 align:start position:0%
you do is you click on this It'll ask
you<00:00:46.600><c> to</c><00:00:46.719><c> create</c><00:00:46.920><c> a</c><00:00:47.039><c> new</c><00:00:47.320><c> secret</c><00:00:48.320><c> the</c><00:00:48.440><c> name</c><00:00:48.600><c> of</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
you to create a new secret the name of
 

00:00:48.719 --> 00:00:55.189 align:start position:0%
you to create a new secret the name of
the<00:00:48.879><c> secret</c><00:00:49.360><c> is</c><00:00:49.719><c> O</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
 
 

00:00:55.199 --> 00:00:59.549 align:start position:0%
 
token<00:00:56.199><c> and</c><00:00:56.359><c> then</c><00:00:56.680><c> the</c><00:00:56.840><c> secret</c><00:00:57.719><c> itself</c><00:00:58.719><c> is</c><00:00:59.440><c> uh</c>

00:00:59.549 --> 00:00:59.559 align:start position:0%
token and then the secret itself is uh
 

00:00:59.559 --> 00:01:02.189 align:start position:0%
token and then the secret itself is uh
describe<00:01:00.359><c> here</c><00:01:00.800><c> as</c><00:01:01.000><c> secret</c><00:01:01.359><c> random</c><00:01:01.719><c> token</c><00:01:02.079><c> so</c>

00:01:02.189 --> 00:01:02.199 align:start position:0%
describe here as secret random token so
 

00:01:02.199 --> 00:01:04.429 align:start position:0%
describe here as secret random token so
you<00:01:02.320><c> would</c><00:01:02.480><c> just</c><00:01:02.640><c> type</c><00:01:02.920><c> that</c><00:01:03.119><c> in</c><00:01:04.119><c> you</c><00:01:04.239><c> would</c>

00:01:04.429 --> 00:01:04.439 align:start position:0%
you would just type that in you would
 

00:01:04.439 --> 00:01:06.749 align:start position:0%
you would just type that in you would
type<00:01:04.680><c> in</c><00:01:05.199><c> secret</c>

00:01:06.749 --> 00:01:06.759 align:start position:0%
type in secret
 

00:01:06.759 --> 00:01:09.310 align:start position:0%
type in secret
random<00:01:07.759><c> token</c><00:01:08.280><c> you</c><00:01:08.360><c> can</c><00:01:08.520><c> reveal</c><00:01:08.840><c> the</c><00:01:08.960><c> secret</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
random token you can reveal the secret
 

00:01:09.320 --> 00:01:11.830 align:start position:0%
random token you can reveal the secret
to<00:01:09.439><c> make</c><00:01:09.560><c> sure</c><00:01:09.759><c> that</c><00:01:09.880><c> you</c><00:01:09.960><c> spelled</c><00:01:10.280><c> it</c><00:01:10.840><c> right</c>

00:01:11.830 --> 00:01:11.840 align:start position:0%
to make sure that you spelled it right
 

00:01:11.840 --> 00:01:13.310 align:start position:0%
to make sure that you spelled it right
look<00:01:12.000><c> I</c><00:01:12.159><c> even</c><00:01:12.320><c> spelled</c><00:01:12.640><c> it</c><00:01:12.799><c> wrong</c><00:01:13.000><c> so</c><00:01:13.119><c> it's</c><00:01:13.240><c> a</c>

00:01:13.310 --> 00:01:13.320 align:start position:0%
look I even spelled it wrong so it's a
 

00:01:13.320 --> 00:01:15.390 align:start position:0%
look I even spelled it wrong so it's a
good<00:01:13.439><c> idea</c><00:01:13.680><c> to</c><00:01:13.840><c> check</c><00:01:14.840><c> but</c><00:01:14.920><c> and</c><00:01:15.040><c> then</c><00:01:15.159><c> you</c><00:01:15.240><c> can</c>

00:01:15.390 --> 00:01:15.400 align:start position:0%
good idea to check but and then you can
 

00:01:15.400 --> 00:01:17.350 align:start position:0%
good idea to check but and then you can
add<00:01:15.560><c> the</c><00:01:15.640><c> secret</c><00:01:16.119><c> I've</c><00:01:16.280><c> already</c><00:01:16.520><c> added</c><00:01:16.840><c> it</c><00:01:17.240><c> we</c>

00:01:17.350 --> 00:01:17.360 align:start position:0%
add the secret I've already added it we
 

00:01:17.360 --> 00:01:18.870 align:start position:0%
add the secret I've already added it we
can<00:01:17.479><c> see</c><00:01:17.720><c> that</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
can see that
 

00:01:18.880 --> 00:01:22.910 align:start position:0%
can see that
here<00:01:19.880><c> um</c><00:01:20.880><c> and</c><00:01:21.040><c> you</c><00:01:21.119><c> know</c><00:01:21.320><c> I</c><00:01:21.400><c> can</c><00:01:21.759><c> just</c><00:01:22.759><c> make</c>

00:01:22.910 --> 00:01:22.920 align:start position:0%
here um and you know I can just make
 

00:01:22.920 --> 00:01:24.310 align:start position:0%
here um and you know I can just make
sure<00:01:23.280><c> Again</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
sure Again
 

00:01:24.320 --> 00:01:27.429 align:start position:0%
sure Again
by<00:01:25.320><c> uh</c><00:01:25.479><c> going</c><00:01:25.840><c> back</c>

00:01:27.429 --> 00:01:27.439 align:start position:0%
by uh going back
 

00:01:27.439 --> 00:01:29.990 align:start position:0%
by uh going back
to<00:01:28.439><c> going</c><00:01:28.720><c> back</c><00:01:28.880><c> to</c><00:01:29.079><c> the</c><00:01:29.200><c> read</c><00:01:29.439><c> me</c><00:01:29.720><c> and</c><00:01:29.880><c> and</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
to going back to the read me and and
 

00:01:30.000 --> 00:01:35.109 align:start position:0%
to going back to the read me and and
just<00:01:30.159><c> copy</c><00:01:30.520><c> pasting</c><00:01:30.840><c> it</c><00:01:31.119><c> just</c><00:01:31.280><c> because</c><00:01:31.520><c> I'm</c>

00:01:35.109 --> 00:01:35.119 align:start position:0%
 
 

00:01:35.119 --> 00:01:39.830 align:start position:0%
 
paranoid<00:01:36.119><c> so</c><00:01:36.280><c> I'm</c><00:01:36.399><c> just</c><00:01:36.560><c> going</c><00:01:36.720><c> to</c><00:01:36.960><c> go</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
 
 

00:01:39.840 --> 00:01:44.190 align:start position:0%
 
back<00:01:40.840><c> and</c><00:01:41.159><c> just</c><00:01:41.360><c> going</c><00:01:41.560><c> to</c><00:01:42.399><c> update</c>

00:01:44.190 --> 00:01:44.200 align:start position:0%
back and just going to update
 

00:01:44.200 --> 00:01:46.469 align:start position:0%
back and just going to update
that<00:01:45.200><c> and</c><00:01:45.320><c> then</c><00:01:45.439><c> what</c><00:01:45.600><c> we</c><00:01:45.680><c> want</c><00:01:45.799><c> to</c><00:01:46.000><c> do</c><00:01:46.280><c> is</c>

00:01:46.469 --> 00:01:46.479 align:start position:0%
that and then what we want to do is
 

00:01:46.479 --> 00:01:47.749 align:start position:0%
that and then what we want to do is
create<00:01:46.680><c> a</c><00:01:46.840><c> web</c><00:01:47.079><c> hook</c><00:01:47.280><c> now</c><00:01:47.399><c> I've</c><00:01:47.520><c> already</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
create a web hook now I've already
 

00:01:47.759 --> 00:01:49.310 align:start position:0%
create a web hook now I've already
created<00:01:48.240><c> the</c><00:01:48.479><c> the</c><00:01:48.600><c> web</c><00:01:48.840><c> hook</c><00:01:49.040><c> but</c><00:01:49.159><c> I'm</c><00:01:49.200><c> going</c>

00:01:49.310 --> 00:01:49.320 align:start position:0%
created the the web hook but I'm going
 

00:01:49.320 --> 00:01:50.870 align:start position:0%
created the the web hook but I'm going
to<00:01:49.439><c> go</c><00:01:49.520><c> ahead</c><00:01:49.680><c> and</c><00:01:49.840><c> delete</c><00:01:50.119><c> it</c><00:01:50.240><c> and</c><00:01:50.399><c> recreate</c>

00:01:50.870 --> 00:01:50.880 align:start position:0%
to go ahead and delete it and recreate
 

00:01:50.880 --> 00:01:52.950 align:start position:0%
to go ahead and delete it and recreate
it<00:01:51.000><c> so</c><00:01:51.119><c> you</c><00:01:51.240><c> can</c><00:01:51.360><c> see</c><00:01:51.560><c> the</c><00:01:51.680><c> whole</c><00:01:52.000><c> process</c><00:01:52.479><c> from</c>

00:01:52.950 --> 00:01:52.960 align:start position:0%
it so you can see the whole process from
 

00:01:52.960 --> 00:01:55.069 align:start position:0%
it so you can see the whole process from
scratch<00:01:53.960><c> so</c><00:01:54.079><c> what</c><00:01:54.159><c> you're</c><00:01:54.280><c> going</c><00:01:54.360><c> to</c><00:01:54.520><c> do</c><00:01:54.799><c> is</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
scratch so what you're going to do is
 

00:01:55.079 --> 00:01:56.590 align:start position:0%
scratch so what you're going to do is
you<00:01:55.159><c> want</c><00:01:55.280><c> to</c><00:01:55.439><c> create</c><00:01:55.680><c> a</c><00:01:55.840><c> new</c><00:01:56.000><c> web</c><00:01:56.240><c> hook</c><00:01:56.439><c> now</c>

00:01:56.590 --> 00:01:56.600 align:start position:0%
you want to create a new web hook now
 

00:01:56.600 --> 00:01:58.350 align:start position:0%
you want to create a new web hook now
you<00:01:56.680><c> will</c><00:01:56.920><c> probably</c><00:01:57.200><c> not</c><00:01:57.399><c> have</c><00:01:57.560><c> any</c><00:01:57.759><c> web</c><00:01:58.000><c> hooks</c>

00:01:58.350 --> 00:01:58.360 align:start position:0%
you will probably not have any web hooks
 

00:01:58.360 --> 00:02:00.910 align:start position:0%
you will probably not have any web hooks
so<00:01:58.479><c> you're</c><00:01:58.600><c> going</c><00:01:58.680><c> to</c><00:01:58.799><c> hit</c><00:01:58.960><c> new</c><00:01:59.159><c> web</c><00:01:59.399><c> hook</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
so you're going to hit new web hook
 

00:02:00.920 --> 00:02:02.789 align:start position:0%
so you're going to hit new web hook
and<00:02:01.119><c> you</c><00:02:01.240><c> can</c><00:02:01.520><c> name</c><00:02:01.719><c> it</c><00:02:01.960><c> whatever</c><00:02:02.280><c> you</c><00:02:02.439><c> want</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
and you can name it whatever you want
 

00:02:02.799 --> 00:02:06.670 align:start position:0%
and you can name it whatever you want
I'm<00:02:03.399><c> I'm</c><00:02:03.479><c> going</c><00:02:03.600><c> to</c><00:02:03.759><c> call</c><00:02:03.920><c> it</c><00:02:04.079><c> modal</c><00:02:04.560><c> web</c><00:02:04.840><c> hook</c>

00:02:06.670 --> 00:02:06.680 align:start position:0%
I'm I'm going to call it modal web hook
 

00:02:06.680 --> 00:02:09.309 align:start position:0%
I'm I'm going to call it modal web hook
test<00:02:07.680><c> and</c><00:02:07.880><c> then</c><00:02:08.039><c> the</c><00:02:08.200><c> URL</c><00:02:08.759><c> is</c><00:02:08.879><c> you</c><00:02:08.959><c> want</c><00:02:09.080><c> to</c>

00:02:09.309 --> 00:02:09.319 align:start position:0%
test and then the URL is you want to
 

00:02:09.319 --> 00:02:11.949 align:start position:0%
test and then the URL is you want to
enter<00:02:09.679><c> the</c><00:02:09.840><c> URL</c><00:02:10.679><c> endpoint</c><00:02:11.200><c> for</c><00:02:11.360><c> the</c><00:02:11.480><c> web</c><00:02:11.720><c> hook</c>

00:02:11.949 --> 00:02:11.959 align:start position:0%
enter the URL endpoint for the web hook
 

00:02:11.959 --> 00:02:14.350 align:start position:0%
enter the URL endpoint for the web hook
now<00:02:12.080><c> if</c><00:02:12.160><c> you</c><00:02:12.319><c> recall</c><00:02:13.080><c> going</c><00:02:13.319><c> back</c><00:02:13.440><c> to</c><00:02:13.599><c> my</c>

00:02:14.350 --> 00:02:14.360 align:start position:0%
now if you recall going back to my
 

00:02:14.360 --> 00:02:16.830 align:start position:0%
now if you recall going back to my
terminal<00:02:15.360><c> you</c><00:02:15.560><c> got</c><00:02:15.879><c> when</c><00:02:16.000><c> you</c><00:02:16.200><c> launch</c><00:02:16.720><c> the</c>

00:02:16.830 --> 00:02:16.840 align:start position:0%
terminal you got when you launch the
 

00:02:16.840 --> 00:02:20.030 align:start position:0%
terminal you got when you launch the
modal<00:02:17.280><c> app</c><00:02:17.480><c> you</c><00:02:17.640><c> got</c><00:02:17.879><c> these</c><00:02:18.120><c> two</c><00:02:18.760><c> different</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
modal app you got these two different
 

00:02:20.040 --> 00:02:24.470 align:start position:0%
modal app you got these two different
URLs<00:02:21.040><c> the</c><00:02:21.160><c> one</c><00:02:21.319><c> that</c><00:02:21.480><c> ends</c><00:02:21.720><c> in</c><00:02:22.400><c> run</c><00:02:23.239><c> is</c><00:02:23.440><c> the</c>

00:02:24.470 --> 00:02:24.480 align:start position:0%
URLs the one that ends in run is the
 

00:02:24.480 --> 00:02:27.309 align:start position:0%
URLs the one that ends in run is the
endpoint<00:02:25.480><c> so</c><00:02:25.599><c> you</c><00:02:25.720><c> want</c><00:02:25.800><c> to</c><00:02:26.000><c> copy</c><00:02:26.360><c> that</c><00:02:27.080><c> and</c>

00:02:27.309 --> 00:02:27.319 align:start position:0%
endpoint so you want to copy that and
 

00:02:27.319 --> 00:02:29.470 align:start position:0%
endpoint so you want to copy that and
paste<00:02:27.560><c> it</c><00:02:27.800><c> here</c><00:02:28.480><c> now</c><00:02:28.640><c> you</c><00:02:28.720><c> can</c><00:02:28.920><c> also</c><00:02:29.160><c> get</c><00:02:29.280><c> this</c>

00:02:29.470 --> 00:02:29.480 align:start position:0%
paste it here now you can also get this
 

00:02:29.480 --> 00:02:32.710 align:start position:0%
paste it here now you can also get this
URL<00:02:30.239><c> from</c><00:02:30.400><c> your</c><00:02:30.560><c> modal</c><00:02:31.319><c> dashboard</c><00:02:32.319><c> um</c><00:02:32.440><c> you</c><00:02:32.560><c> can</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
URL from your modal dashboard um you can
 

00:02:32.720 --> 00:02:34.790 align:start position:0%
URL from your modal dashboard um you can
go<00:02:32.879><c> in</c><00:02:33.040><c> here</c><00:02:33.319><c> you</c><00:02:33.400><c> can</c><00:02:33.680><c> get</c><00:02:33.840><c> go</c><00:02:33.959><c> into</c><00:02:34.280><c> Apps</c><00:02:34.680><c> and</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
go in here you can get go into Apps and
 

00:02:34.800 --> 00:02:37.229 align:start position:0%
go in here you can get go into Apps and
you<00:02:34.920><c> can</c><00:02:35.120><c> see</c><00:02:35.840><c> the</c><00:02:36.239><c> the</c><00:02:36.319><c> endpoint</c><00:02:36.800><c> right</c><00:02:36.959><c> here</c>

00:02:37.229 --> 00:02:37.239 align:start position:0%
you can see the the endpoint right here
 

00:02:37.239 --> 00:02:39.110 align:start position:0%
you can see the the endpoint right here
if<00:02:37.360><c> you</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
if you
 

00:02:39.120 --> 00:02:43.589 align:start position:0%
if you
want<00:02:40.120><c> so</c><00:02:40.760><c> uh</c><00:02:40.879><c> going</c><00:02:41.239><c> back</c><00:02:41.400><c> to</c><00:02:42.280><c> weights</c><00:02:42.560><c> and</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
want so uh going back to weights and
 

00:02:43.599 --> 00:02:47.110 align:start position:0%
want so uh going back to weights and
biases<00:02:44.599><c> um</c><00:02:45.159><c> you</c><00:02:45.319><c> got</c><00:02:45.440><c> the</c><00:02:45.560><c> URL</c><00:02:46.120><c> here</c><00:02:46.760><c> now</c><00:02:47.000><c> what</c>

00:02:47.110 --> 00:02:47.120 align:start position:0%
biases um you got the URL here now what
 

00:02:47.120 --> 00:02:49.790 align:start position:0%
biases um you got the URL here now what
we<00:02:47.239><c> want</c><00:02:47.360><c> to</c><00:02:47.519><c> do</c><00:02:48.040><c> is</c><00:02:48.159><c> you</c><00:02:48.319><c> have</c><00:02:48.680><c> access</c><00:02:49.040><c> token</c>

00:02:49.790 --> 00:02:49.800 align:start position:0%
we want to do is you have access token
 

00:02:49.800 --> 00:02:51.550 align:start position:0%
we want to do is you have access token
in<00:02:49.959><c> a</c><00:02:50.159><c> secret</c><00:02:50.640><c> now</c><00:02:50.800><c> this</c><00:02:50.879><c> is</c><00:02:51.159><c> a</c><00:02:51.239><c> little</c><00:02:51.400><c> bit</c>

00:02:51.550 --> 00:02:51.560 align:start position:0%
in a secret now this is a little bit
 

00:02:51.560 --> 00:02:54.509 align:start position:0%
in a secret now this is a little bit
tricky<00:02:52.560><c> I</c><00:02:52.640><c> get</c><00:02:52.840><c> mixed</c><00:02:53.120><c> up</c><00:02:53.239><c> on</c><00:02:53.400><c> this</c><00:02:53.560><c> too</c><00:02:54.319><c> and</c>

00:02:54.509 --> 00:02:54.519 align:start position:0%
tricky I get mixed up on this too and
 

00:02:54.519 --> 00:02:56.589 align:start position:0%
tricky I get mixed up on this too and
for<00:02:54.760><c> this</c>

00:02:56.589 --> 00:02:56.599 align:start position:0%
for this
 

00:02:56.599 --> 00:02:58.830 align:start position:0%
for this
exercise<00:02:57.599><c> uh</c><00:02:57.760><c> just</c><00:02:57.920><c> go</c><00:02:58.080><c> down</c><00:02:58.280><c> here</c><00:02:58.480><c> it'll</c><00:02:58.680><c> tell</c>

00:02:58.830 --> 00:02:58.840 align:start position:0%
exercise uh just go down here it'll tell
 

00:02:58.840 --> 00:03:00.710 align:start position:0%
exercise uh just go down here it'll tell
you<00:02:59.000><c> what</c><00:02:59.120><c> to</c><00:02:59.280><c> do</c><00:03:00.200><c> uh</c><00:03:00.280><c> you're</c><00:03:00.400><c> going</c><00:03:00.519><c> to</c><00:03:00.599><c> want</c>

00:03:00.710 --> 00:03:00.720 align:start position:0%
you what to do uh you're going to want
 

00:03:00.720 --> 00:03:02.990 align:start position:0%
you what to do uh you're going to want
to<00:03:00.840><c> set</c><00:03:01.080><c> the</c><00:03:01.280><c> access</c><00:03:01.680><c> token</c><00:03:02.680><c> and</c><00:03:02.760><c> you're</c><00:03:02.879><c> going</c>

00:03:02.990 --> 00:03:03.000 align:start position:0%
to set the access token and you're going
 

00:03:03.000 --> 00:03:04.990 align:start position:0%
to set the access token and you're going
to<00:03:03.120><c> want</c><00:03:03.239><c> to</c><00:03:03.599><c> select</c><00:03:03.920><c> off</c><00:03:04.280><c> token</c><00:03:04.680><c> so</c><00:03:04.840><c> that's</c>

00:03:04.990 --> 00:03:05.000 align:start position:0%
to want to select off token so that's
 

00:03:05.000 --> 00:03:07.509 align:start position:0%
to want to select off token so that's
what<00:03:05.120><c> we'll</c><00:03:05.280><c> do</c><00:03:05.480><c> we'll</c><00:03:05.680><c> go</c><00:03:06.080><c> back</c><00:03:07.080><c> and</c><00:03:07.319><c> we'll</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
what we'll do we'll go back and we'll
 

00:03:07.519 --> 00:03:10.110 align:start position:0%
what we'll do we'll go back and we'll
select<00:03:08.040><c> we'll</c><00:03:08.200><c> make</c><00:03:08.400><c> this</c><00:03:08.560><c> off</c>

00:03:10.110 --> 00:03:10.120 align:start position:0%
select we'll make this off
 

00:03:10.120 --> 00:03:12.630 align:start position:0%
select we'll make this off
token<00:03:11.120><c> and</c><00:03:11.280><c> then</c><00:03:11.440><c> we'll</c><00:03:11.640><c> say</c><00:03:11.920><c> great</c><00:03:12.319><c> we'll</c><00:03:12.480><c> say</c>

00:03:12.630 --> 00:03:12.640 align:start position:0%
token and then we'll say great we'll say
 

00:03:12.640 --> 00:03:13.789 align:start position:0%
token and then we'll say great we'll say
create<00:03:12.920><c> web</c>

00:03:13.789 --> 00:03:13.799 align:start position:0%
create web
 

00:03:13.799 --> 00:03:16.509 align:start position:0%
create web
hook<00:03:14.799><c> and</c><00:03:15.040><c> now</c><00:03:15.319><c> we</c><00:03:15.440><c> have</c>

00:03:16.509 --> 00:03:16.519 align:start position:0%
hook and now we have
 

00:03:16.519 --> 00:03:19.630 align:start position:0%
hook and now we have
created<00:03:17.519><c> this</c><00:03:17.799><c> web</c><00:03:18.080><c> hook</c><00:03:18.879><c> and</c><00:03:19.080><c> now</c><00:03:19.280><c> we</c><00:03:19.400><c> are</c>

00:03:19.630 --> 00:03:19.640 align:start position:0%
created this web hook and now we are
 

00:03:19.640 --> 00:03:24.760 align:start position:0%
created this web hook and now we are
ready<00:03:20.200><c> to</c><00:03:21.000><c> wire</c><00:03:21.360><c> up</c><00:03:21.599><c> the</c><00:03:21.760><c> automation</c>

