'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { VideoSummary } from '@/types'
import { VideoCard } from '@/components/VideoCard'
import { TopicSelector } from '@/components/TopicSelector'
import { ChannelSelector } from '@/components/ChannelSelector'
import { ContextualChat } from '@/components/ContextualChat'
import { SearchComponent } from '@/components/SearchComponent'
import { DateFilter } from '@/components/DateFilter'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Play, TrendingUp, Clock, MessageCircle } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  const [recentVideos, setRecentVideos] = useState<VideoSummary[]>([])
  const [selectedTopics, setSelectedTopics] = useState<string[]>([])
  const [selectedChannels, setSelectedChannels] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [hasMoreVideos, setHasMoreVideos] = useState(true)
  
  // Use refs to avoid dependency issues in useCallback
  const currentOffsetRef = useRef(0)
  const loadedVideoIdsRef = useRef<Set<string>>(new Set())
  
  // Use refs for filter values to avoid infinite loops
  const selectedTopicsRef = useRef<string[]>([])
  const selectedChannelsRef = useRef<string[]>([])
  const startDateRef = useRef('')
  const endDateRef = useRef('')
  const isInitialMount = useRef(true)
  
  // Update refs when state changes
  selectedTopicsRef.current = selectedTopics
  selectedChannelsRef.current = selectedChannels
  startDateRef.current = startDate
  endDateRef.current = endDate

  const fetchRecentVideos = useCallback(async (isLoadMore = false) => {
    try {
      console.log('🔍 fetchRecentVideos called with isLoadMore:', isLoadMore);
      if (!isLoadMore) {
        setLoading(true);
        currentOffsetRef.current = 0;
        loadedVideoIdsRef.current = new Set(); // Clear loaded video IDs on fresh fetch
      } else {
        setLoadingMore(true);
      }
      
      const params = new URLSearchParams();
      
      // Check if any filters are applied using refs for stable callback
      const hasTopicFilters = selectedTopicsRef.current.length > 0;
      const hasChannelFilters = selectedChannelsRef.current.length > 0;
      const hasDateFilters = startDateRef.current || endDateRef.current;
      const hasAnyFilters = hasTopicFilters || hasChannelFilters || hasDateFilters;
      
      let endpoint = '/api/videos/recent'; // Default for no filters
      
      if (hasAnyFilters) {
        // Use filtered endpoint when filters are applied
        endpoint = '/api/videos/filtered';
        
        if (hasTopicFilters) {
          params.append('topics', selectedTopicsRef.current.join(','));
        }
        
        if (hasChannelFilters) {
          params.append('channels', selectedChannelsRef.current.join(','));
        }

        if (startDateRef.current) {
          params.append('startDate', startDateRef.current);
        }

        if (endDateRef.current) {
          params.append('endDate', endDateRef.current);
        }
        
        params.append('offset', isLoadMore ? currentOffsetRef.current.toString() : '0');
      }
      
      params.append('limit', '20'); // Always fetch 20 videos
      
      console.log('🌐 Making request to:', `${endpoint}?${params}`);
      const response = await fetch(`${endpoint}?${params}`);
      console.log('📨 Response status:', response.status, response.ok);
      
      if (response.ok) {
        const data = await response.json();
        console.log('📊 Response data:', data);
        const newVideos = data.videos || [];
        console.log('🎥 New videos count:', newVideos.length);
        
        // Filter out any videos we\'ve already loaded
        const uniqueNewVideos = newVideos.filter((video: VideoSummary) => 
          !loadedVideoIdsRef.current.has(video.id)
        );
        
        // Sort videos by published_at in descending order (most recent first)
        // Handle data quality issue: ignore fake published_at dates that match created_at
        const sortedNewVideos = uniqueNewVideos.sort((a: VideoSummary, b: VideoSummary) => {
          const isRealDateA = a.published_at && a.created_at && a.published_at !== a.created_at;
          const isRealDateB = b.published_at && b.created_at && b.published_at !== b.created_at;

          const dateA = isRealDateA ? new Date(a.published_at).getTime() : 0;
          const dateB = isRealDateB ? new Date(b.published_at).getTime() : 0;
          return dateB - dateA;
        });
        
        if (isLoadMore) {
          setRecentVideos(prev => {
            const combined = [...prev, ...sortedNewVideos];
            // Additional deduplication at React level
            const seen = new Set<string>();
            return combined.filter(video => {
              if (seen.has(video.id)) {
                return false;
              }
              seen.add(video.id);
              return true;
            });
          });
          currentOffsetRef.current = currentOffsetRef.current + 20;
        } else {
          console.log('🔄 Setting recentVideos to:', sortedNewVideos.length, 'videos');
          setRecentVideos(sortedNewVideos);
          console.log('🔄 setRecentVideos called successfully');
          currentOffsetRef.current = 20;
        }
        
        // Update loaded video IDs set
        sortedNewVideos.forEach((video: VideoSummary) => {
          loadedVideoIdsRef.current.add(video.id);
        });
        
        // Determine if there are more videos to load
        if (hasAnyFilters) {
          setHasMoreVideos(sortedNewVideos.length >= 20);
        } else {
          // For recent videos (no filters), pagination is not supported by this logic
          setHasMoreVideos(false);
        }
      } else {
        console.error('❌ Response not OK:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('❌ Error response body:', errorText);
      }
    } catch (error) {
      console.error('❌ Error fetching recent videos:', error);
      console.error('❌ Error details:', {
        message: (error as Error).message,
        stack: (error as Error).stack,
        name: (error as Error).name
      });
    } finally {
      setLoading(false);
      setLoadingMore(false);
      console.log('✅ fetchRecentVideos completed');
    }
  }, []); // Empty dependency array is correct - we use refs to avoid stale closures

  const handleLoadMore = () => {
    // Only allow load more when filters are applied (filtered endpoint supports pagination)
    const hasAnyFilters = selectedTopicsRef.current.length > 0 || 
                         selectedChannelsRef.current.length > 0 || 
                         startDateRef.current || 
                         endDateRef.current
    
    if (!loadingMore && hasMoreVideos && hasAnyFilters) {
      fetchRecentVideos(true)
    }
  }

  const handleDateChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate)
    setEndDate(newEndDate)
  }

  const handleDateClear = () => {
    setStartDate('')
    setEndDate('')
  }

  useEffect(() => {
    // Initial fetch on component mount
    console.log('🚀 Component mounted, calling fetchRecentVideos')
    fetchRecentVideos()
    isInitialMount.current = false // Mark that initial mount is complete
  }, [fetchRecentVideos]) // Include fetchRecentVideos dependency

  // Trigger refresh when filters change
  useEffect(() => {
    // Skip the initial render (which is handled by the mount effect above)
    if (isInitialMount.current) {
      console.log('⏭️ Skipping filter effect on initial mount')
      return
    }
    console.log('🔄 Filters changed, calling fetchRecentVideos')
    fetchRecentVideos()
  }, [selectedTopics, selectedChannels, startDate, endDate, fetchRecentVideos])

  // Debug effect to track recentVideos state changes
  useEffect(() => {
    console.log('🎬 recentVideos state changed:', {
      length: recentVideos.length,
      firstVideo: recentVideos[0]?.title,
      loading,
      loadingMore
    })
  }, [recentVideos, loading, loadingMore])

  return (
    <div className="w-full">
      {/* Hero Section */}
      <div className="text-center py-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg mb-8">
        <h1 className="text-4xl font-bold mb-4">
          YouTube Summaries AI Explorer
        </h1>
        <p className="text-xl mb-8 max-w-3xl mx-auto">
          Discover, search, and chat with AI about YouTube video content. 
          Get instant insights from thousands of video summaries powered by Google Gemini.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/search"
            className="inline-flex items-center gap-2 bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            <Play className="w-5 h-5" />
            Explore Videos
          </Link>
          <Link
            href="/chat"
            className="inline-flex items-center gap-2 bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-400 transition-colors"
          >
            <MessageCircle className="w-5 h-5" />
            Chat with AI
          </Link>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="recent" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="recent" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Recent Videos
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Search & Explore
          </TabsTrigger>
        </TabsList>

        <TabsContent value="recent" className="space-y-6">
          {/* Date Filter */}
          <div className="flex justify-center">
            <DateFilter
              startDate={startDate}
              endDate={endDate}
              onDateChange={handleDateChange}
              onClear={handleDateClear}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TopicSelector
              selectedTopics={selectedTopics}
              onTopicsChange={setSelectedTopics}
            />
            <ChannelSelector
              selectedChannels={selectedChannels}
              onChannelsChange={setSelectedChannels}
              selectedTopics={selectedTopics}
            />
          </div>

          {loading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                  <div className="bg-gray-200 h-4 rounded mb-2"></div>
                  <div className="bg-gray-200 h-4 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">
                  {selectedTopics.length > 0 || selectedChannels.length > 0 ? 'Filtered Videos' : 'Recent Videos'}
                </h2>
                <span className="text-gray-600">
                  {recentVideos.length} videos
                </span>
              </div>

              {(() => {
                console.log('🎯 Current recentVideos.length:', recentVideos.length)
                console.log('🎯 Current recentVideos:', recentVideos)
                return recentVideos.length === 0
              })() ? (
                <div className="text-center py-12 text-gray-500">
                  <Play className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">No videos found</h3>
                  <p>Try adjusting your topic or channel filters or check back later for new content.</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {recentVideos.map((video) => (
                      <VideoCard key={video.id} video={video} showTopic />
                    ))}
                  </div>
                  
                  {/* Load More Button */}
                  {hasMoreVideos && (
                    <div className="flex justify-center pt-6">
                      <button
                        onClick={handleLoadMore}
                        disabled={loadingMore}
                        className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                      >
                        {loadingMore ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                            Loading more...
                          </>
                        ) : (
                          'Load More Videos'
                        )}
                      </button>
                    </div>
                  )}
                  
                  {/* Contextual Chat Section */}
                  <div className="border-t pt-6">
                    <ContextualChat
                      selectedTopics={selectedTopics}
                      selectedChannels={selectedChannels}
                      videos={recentVideos}
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          <SearchComponent selectedTopics={selectedTopics} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
