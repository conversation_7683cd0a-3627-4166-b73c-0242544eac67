WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:05.390 align:start position:0%
 
[Music]

00:00:05.390 --> 00:00:05.400 align:start position:0%
 
 

00:00:05.400 --> 00:00:07.550 align:start position:0%
 
hello<00:00:05.640><c> everyone</c><00:00:06.359><c> I'm</c><00:00:06.560><c> Ry</c><00:00:07.160><c> and</c><00:00:07.319><c> I'm</c><00:00:07.439><c> an</c>

00:00:07.550 --> 00:00:07.560 align:start position:0%
hello everyone I'm <PERSON>y and I'm an
 

00:00:07.560 --> 00:00:10.190 align:start position:0%
hello everyone I'm Ry and I'm an
independent<00:00:08.040><c> data</c><00:00:08.519><c> scientist</c><00:00:09.519><c> it's</c><00:00:09.679><c> an</c><00:00:09.880><c> honor</c>

00:00:10.190 --> 00:00:10.200 align:start position:0%
independent data scientist it's an honor
 

00:00:10.200 --> 00:00:13.350 align:start position:0%
independent data scientist it's an honor
to<00:00:10.360><c> be</c><00:00:10.599><c> here</c><00:00:11.080><c> at</c><00:00:11.240><c> the</c><00:00:11.360><c> NLB</c><00:00:11.799><c> Summit</c><00:00:12.200><c> 2024</c><00:00:13.200><c> to</c>

00:00:13.350 --> 00:00:13.360 align:start position:0%
to be here at the NLB Summit 2024 to
 

00:00:13.360 --> 00:00:15.669 align:start position:0%
to be here at the NLB Summit 2024 to
share<00:00:13.719><c> with</c><00:00:13.880><c> you</c><00:00:14.160><c> an</c><00:00:14.400><c> AI</c><00:00:14.759><c> powered</c><00:00:15.080><c> solution</c>

00:00:15.669 --> 00:00:15.679 align:start position:0%
share with you an AI powered solution
 

00:00:15.679 --> 00:00:18.150 align:start position:0%
share with you an AI powered solution
for<00:00:15.879><c> a</c><00:00:16.039><c> critical</c><00:00:16.520><c> problem</c><00:00:17.320><c> adverse</c><00:00:17.720><c> drug</c>

00:00:18.150 --> 00:00:18.160 align:start position:0%
for a critical problem adverse drug
 

00:00:18.160 --> 00:00:20.070 align:start position:0%
for a critical problem adverse drug
reactions<00:00:19.160><c> or</c>

00:00:20.070 --> 00:00:20.080 align:start position:0%
reactions or
 

00:00:20.080 --> 00:00:23.070 align:start position:0%
reactions or
adrs<00:00:21.080><c> as</c><00:00:21.199><c> a</c><00:00:21.359><c> data</c><00:00:21.680><c> scientist</c><00:00:22.320><c> and</c><00:00:22.560><c> Healthcare</c>

00:00:23.070 --> 00:00:23.080 align:start position:0%
adrs as a data scientist and Healthcare
 

00:00:23.080 --> 00:00:25.870 align:start position:0%
adrs as a data scientist and Healthcare
Advocate<00:00:24.000><c> I'm</c><00:00:24.199><c> excited</c><00:00:24.599><c> to</c><00:00:24.760><c> introduce</c><00:00:25.279><c> you</c><00:00:25.680><c> to</c>

00:00:25.870 --> 00:00:25.880 align:start position:0%
Advocate I'm excited to introduce you to
 

00:00:25.880 --> 00:00:28.950 align:start position:0%
Advocate I'm excited to introduce you to
the<00:00:26.160><c> ABR</c><00:00:26.640><c> Shield</c><00:00:27.439><c> a</c><00:00:27.640><c> patient</c><00:00:27.960><c> safety</c><00:00:28.279><c> tool</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
the ABR Shield a patient safety tool
 

00:00:28.960 --> 00:00:31.429 align:start position:0%
the ABR Shield a patient safety tool
that's<00:00:29.240><c> designed</c><00:00:29.599><c> to</c><00:00:30.080><c> evolutionize</c><00:00:31.080><c> how</c><00:00:31.240><c> we</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
that's designed to evolutionize how we
 

00:00:31.439 --> 00:00:36.709 align:start position:0%
that's designed to evolutionize how we
manage<00:00:31.840><c> drug</c><00:00:32.440><c> interactions</c><00:00:33.440><c> let's</c><00:00:33.719><c> dive</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
 
 

00:00:36.719 --> 00:00:39.510 align:start position:0%
 
in<00:00:37.719><c> so</c><00:00:38.079><c> let's</c><00:00:38.280><c> begin</c><00:00:38.520><c> by</c><00:00:38.960><c> understanding</c><00:00:39.360><c> the</c>

00:00:39.510 --> 00:00:39.520 align:start position:0%
in so let's begin by understanding the
 

00:00:39.520 --> 00:00:42.310 align:start position:0%
in so let's begin by understanding the
scale<00:00:39.879><c> of</c><00:00:40.000><c> the</c><00:00:40.160><c> area</c><00:00:40.680><c> problem</c><00:00:41.680><c> adverse</c><00:00:42.079><c> drug</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
scale of the area problem adverse drug
 

00:00:42.320 --> 00:00:44.709 align:start position:0%
scale of the area problem adverse drug
reactions<00:00:42.960><c> are</c><00:00:43.160><c> a</c><00:00:43.360><c> serious</c><00:00:43.800><c> concern</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
reactions are a serious concern
 

00:00:44.719 --> 00:00:47.430 align:start position:0%
reactions are a serious concern
particularly<00:00:45.200><c> for</c><00:00:45.399><c> older</c><00:00:45.760><c> adults</c><00:00:46.760><c> in</c><00:00:46.960><c> fact</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
particularly for older adults in fact
 

00:00:47.440 --> 00:00:49.869 align:start position:0%
particularly for older adults in fact
30%<00:00:48.239><c> of</c><00:00:48.480><c> Hospital</c><00:00:48.960><c> admissions</c><00:00:49.399><c> for</c><00:00:49.600><c> older</c>

00:00:49.869 --> 00:00:49.879 align:start position:0%
30% of Hospital admissions for older
 

00:00:49.879 --> 00:00:53.029 align:start position:0%
30% of Hospital admissions for older
adults<00:00:50.559><c> are</c><00:00:50.760><c> linked</c><00:00:51.079><c> to</c><00:00:51.239><c> adrs</c><00:00:52.199><c> with</c><00:00:52.440><c> drug</c><00:00:52.719><c> drug</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
adults are linked to adrs with drug drug
 

00:00:53.039 --> 00:00:55.830 align:start position:0%
adults are linked to adrs with drug drug
interactions<00:00:53.640><c> playing</c><00:00:54.000><c> a</c><00:00:54.160><c> major</c><00:00:54.600><c> role</c><00:00:55.600><c> when</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
interactions playing a major role when
 

00:00:55.840 --> 00:00:58.470 align:start position:0%
interactions playing a major role when
patients<00:00:56.239><c> take</c><00:00:56.480><c> five</c><00:00:56.760><c> or</c><00:00:56.879><c> more</c><00:00:57.280><c> medications</c><00:00:58.280><c> a</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
patients take five or more medications a
 

00:00:58.480 --> 00:01:00.430 align:start position:0%
patients take five or more medications a
practice<00:00:58.800><c> known</c><00:00:59.000><c> as</c><00:00:59.199><c> poly</c><00:00:59.480><c> Pharmacy</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
practice known as poly Pharmacy
 

00:01:00.440 --> 00:01:02.709 align:start position:0%
practice known as poly Pharmacy
they<00:01:00.600><c> are</c><00:01:00.840><c> at</c><00:01:00.920><c> an</c><00:01:01.079><c> even</c><00:01:01.359><c> greater</c><00:01:01.719><c> risk</c>

00:01:02.709 --> 00:01:02.719 align:start position:0%
they are at an even greater risk
 

00:01:02.719 --> 00:01:04.670 align:start position:0%
they are at an even greater risk
especially<00:01:03.280><c> in</c><00:01:03.559><c> in</c><00:01:03.800><c> countries</c><00:01:04.400><c> that</c><00:01:04.519><c> are</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
especially in in countries that are
 

00:01:04.680 --> 00:01:06.270 align:start position:0%
especially in in countries that are
underdeveloped<00:01:05.400><c> or</c>

00:01:06.270 --> 00:01:06.280 align:start position:0%
underdeveloped or
 

00:01:06.280 --> 00:01:08.310 align:start position:0%
underdeveloped or
developing<00:01:07.280><c> where</c><00:01:07.400><c> there</c><00:01:07.479><c> is</c><00:01:07.600><c> a</c><00:01:07.720><c> risk</c><00:01:08.040><c> of</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
developing where there is a risk of
 

00:01:08.320 --> 00:01:11.149 align:start position:0%
developing where there is a risk of
overcounted<00:01:09.000><c> drugs</c><00:01:09.320><c> being</c><00:01:10.040><c> oversold</c><00:01:11.040><c> there</c>

00:01:11.149 --> 00:01:11.159 align:start position:0%
overcounted drugs being oversold there
 

00:01:11.159 --> 00:01:14.030 align:start position:0%
overcounted drugs being oversold there
are<00:01:11.439><c> at</c><00:01:11.600><c> least</c><00:01:11.880><c> 42%</c><00:01:12.799><c> of</c><00:01:12.960><c> patients</c><00:01:13.320><c> on</c><00:01:13.439><c> multiple</c>

00:01:14.030 --> 00:01:14.040 align:start position:0%
are at least 42% of patients on multiple
 

00:01:14.040 --> 00:01:17.310 align:start position:0%
are at least 42% of patients on multiple
medications<00:01:15.040><c> and</c><00:01:15.240><c> also</c><00:01:15.520><c> the</c><00:01:15.720><c> experience</c><00:01:16.320><c> adrs</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
medications and also the experience adrs
 

00:01:17.320 --> 00:01:20.149 align:start position:0%
medications and also the experience adrs
and<00:01:17.560><c> sadly</c><00:01:18.360><c> adrs</c><00:01:18.960><c> have</c><00:01:19.159><c> become</c><00:01:19.520><c> the</c><00:01:19.720><c> fourth</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
and sadly adrs have become the fourth
 

00:01:20.159 --> 00:01:22.550 align:start position:0%
and sadly adrs have become the fourth
leading<00:01:20.600><c> cause</c><00:01:20.840><c> of</c><00:01:21.000><c> death</c><00:01:21.240><c> in</c><00:01:21.400><c> the</c><00:01:21.520><c> US</c><00:01:22.360><c> as</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
leading cause of death in the US as
 

00:01:22.560 --> 00:01:25.510 align:start position:0%
leading cause of death in the US as
reported<00:01:23.119><c> by</c><00:01:23.280><c> the</c><00:01:23.759><c> FDA</c><00:01:24.759><c> these</c><00:01:25.000><c> figures</c>

00:01:25.510 --> 00:01:25.520 align:start position:0%
reported by the FDA these figures
 

00:01:25.520 --> 00:01:27.950 align:start position:0%
reported by the FDA these figures
underscore<00:01:26.320><c> how</c><00:01:26.600><c> urgent</c><00:01:27.040><c> it</c><00:01:27.159><c> is</c><00:01:27.400><c> to</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
underscore how urgent it is to
 

00:01:27.960 --> 00:01:29.670 align:start position:0%
underscore how urgent it is to
understand<00:01:28.240><c> this</c><00:01:28.479><c> issue</c><00:01:28.880><c> efficiently</c><00:01:29.560><c> and</c>

00:01:29.670 --> 00:01:29.680 align:start position:0%
understand this issue efficiently and
 

00:01:29.680 --> 00:01:33.230 align:start position:0%
understand this issue efficiently and
it's

00:01:33.230 --> 00:01:33.240 align:start position:0%
 
 

00:01:33.240 --> 00:01:36.870 align:start position:0%
 
scale<00:01:34.240><c> now</c><00:01:34.439><c> let's</c><00:01:34.680><c> take</c><00:01:34.799><c> a</c><00:01:35.000><c> look</c><00:01:35.200><c> at</c>

00:01:36.870 --> 00:01:36.880 align:start position:0%
scale now let's take a look at
 

00:01:36.880 --> 00:01:41.510 align:start position:0%
scale now let's take a look at
um<00:01:37.880><c> like</c><00:01:38.399><c> where</c><00:01:38.680><c> the</c><00:01:39.479><c> uh</c><00:01:39.960><c> how</c><00:01:40.119><c> the</c><00:01:40.439><c> current</c><00:01:41.439><c> I</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
um like where the uh how the current I
 

00:01:41.520 --> 00:01:43.590 align:start position:0%
um like where the uh how the current I
mean<00:01:41.920><c> how</c><00:01:42.079><c> the</c><00:01:42.240><c> ADR</c><00:01:42.680><c> is</c><00:01:42.759><c> being</c><00:01:43.000><c> addressed</c><00:01:43.439><c> in</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
mean how the ADR is being addressed in
 

00:01:43.600 --> 00:01:46.749 align:start position:0%
mean how the ADR is being addressed in
in<00:01:43.680><c> the</c><00:01:43.799><c> current</c><00:01:44.159><c> trends</c><00:01:45.159><c> so</c><00:01:45.640><c> um</c><00:01:46.159><c> pharmacists</c>

00:01:46.749 --> 00:01:46.759 align:start position:0%
in the current trends so um pharmacists
 

00:01:46.759 --> 00:01:48.910 align:start position:0%
in the current trends so um pharmacists
play<00:01:47.000><c> a</c><00:01:47.200><c> key</c><00:01:47.399><c> role</c><00:01:47.920><c> manually</c><00:01:48.399><c> reviewing</c>

00:01:48.910 --> 00:01:48.920 align:start position:0%
play a key role manually reviewing
 

00:01:48.920 --> 00:01:51.149 align:start position:0%
play a key role manually reviewing
prescriptions<00:01:49.920><c> to</c><00:01:50.119><c> detect</c><00:01:50.479><c> potential</c><00:01:50.840><c> drug</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
prescriptions to detect potential drug
 

00:01:51.159 --> 00:01:53.670 align:start position:0%
prescriptions to detect potential drug
interactions<00:01:52.159><c> but</c><00:01:52.439><c> manual</c><00:01:52.880><c> work</c><00:01:53.159><c> is</c><00:01:53.360><c> time</c>

00:01:53.670 --> 00:01:53.680 align:start position:0%
interactions but manual work is time
 

00:01:53.680 --> 00:01:56.709 align:start position:0%
interactions but manual work is time
consuming<00:01:54.479><c> and</c><00:01:54.680><c> prone</c><00:01:54.960><c> to</c><00:01:55.159><c> errors</c><00:01:56.159><c> especially</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
consuming and prone to errors especially
 

00:01:56.719 --> 00:01:58.670 align:start position:0%
consuming and prone to errors especially
when<00:01:57.000><c> incomplete</c><00:01:57.680><c> patient</c><00:01:58.039><c> information</c><00:01:58.479><c> is</c>

00:01:58.670 --> 00:01:58.680 align:start position:0%
when incomplete patient information is
 

00:01:58.680 --> 00:02:02.469 align:start position:0%
when incomplete patient information is
provided<00:01:59.960><c> then</c><00:02:00.479><c> we</c><00:02:00.680><c> have</c><00:02:00.880><c> EHR</c><00:02:01.479><c> systems</c><00:02:02.280><c> that</c>

00:02:02.469 --> 00:02:02.479 align:start position:0%
provided then we have EHR systems that
 

00:02:02.479 --> 00:02:05.230 align:start position:0%
provided then we have EHR systems that
automate<00:02:02.920><c> drug</c><00:02:03.240><c> interaction</c><00:02:03.759><c> checking</c><00:02:04.759><c> while</c>

00:02:05.230 --> 00:02:05.240 align:start position:0%
automate drug interaction checking while
 

00:02:05.240 --> 00:02:07.590 align:start position:0%
automate drug interaction checking while
this<00:02:05.360><c> is</c><00:02:05.640><c> a</c><00:02:05.880><c> great</c><00:02:06.200><c> step</c><00:02:06.840><c> the</c><00:02:07.000><c> challenge</c><00:02:07.399><c> is</c>

00:02:07.590 --> 00:02:07.600 align:start position:0%
this is a great step the challenge is
 

00:02:07.600 --> 00:02:10.270 align:start position:0%
this is a great step the challenge is
allert<00:02:08.080><c> fatigue</c><00:02:09.080><c> providers</c><00:02:09.599><c> receive</c><00:02:10.039><c> too</c>

00:02:10.270 --> 00:02:10.280 align:start position:0%
allert fatigue providers receive too
 

00:02:10.280 --> 00:02:12.430 align:start position:0%
allert fatigue providers receive too
many<00:02:10.599><c> warnings</c><00:02:11.480><c> many</c><00:02:11.760><c> of</c><00:02:11.959><c> which</c><00:02:12.160><c> are</c>

00:02:12.430 --> 00:02:12.440 align:start position:0%
many warnings many of which are
 

00:02:12.440 --> 00:02:14.869 align:start position:0%
many warnings many of which are
irrelevant<00:02:13.440><c> leading</c><00:02:13.760><c> to</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
irrelevant leading to
 

00:02:14.879 --> 00:02:17.229 align:start position:0%
irrelevant leading to
desensitization<00:02:15.879><c> ehrs</c><00:02:16.519><c> often</c><00:02:16.800><c> lack</c><00:02:17.120><c> the</c>

00:02:17.229 --> 00:02:17.239 align:start position:0%
desensitization ehrs often lack the
 

00:02:17.239 --> 00:02:19.630 align:start position:0%
desensitization ehrs often lack the
realtime<00:02:17.800><c> data</c><00:02:18.319><c> that</c><00:02:18.440><c> is</c><00:02:18.599><c> needed</c><00:02:19.040><c> to</c><00:02:19.280><c> give</c>

00:02:19.630 --> 00:02:19.640 align:start position:0%
realtime data that is needed to give
 

00:02:19.640 --> 00:02:23.150 align:start position:0%
realtime data that is needed to give
accurate<00:02:20.319><c> and</c><00:02:20.560><c> personalized</c><00:02:21.360><c> alerts</c><00:02:22.360><c> lastly</c>

00:02:23.150 --> 00:02:23.160 align:start position:0%
accurate and personalized alerts lastly
 

00:02:23.160 --> 00:02:26.030 align:start position:0%
accurate and personalized alerts lastly
mobile<00:02:23.560><c> apps</c><00:02:23.920><c> like</c><00:02:24.120><c> Medscape</c><00:02:24.760><c> or</c><00:02:25.040><c> Epocrates</c>

00:02:26.030 --> 00:02:26.040 align:start position:0%
mobile apps like Medscape or Epocrates
 

00:02:26.040 --> 00:02:28.390 align:start position:0%
mobile apps like Medscape or Epocrates
offer<00:02:26.360><c> drug</c><00:02:26.680><c> interaction</c><00:02:27.200><c> information</c><00:02:28.160><c> but</c>

00:02:28.390 --> 00:02:28.400 align:start position:0%
offer drug interaction information but
 

00:02:28.400 --> 00:02:31.270 align:start position:0%
offer drug interaction information but
suffer<00:02:28.760><c> from</c><00:02:29.120><c> similar</c><00:02:29.519><c> issues</c><00:02:30.239><c> like</c><00:02:30.760><c> overly</c>

00:02:31.270 --> 00:02:31.280 align:start position:0%
suffer from similar issues like overly
 

00:02:31.280 --> 00:02:33.509 align:start position:0%
suffer from similar issues like overly
generic<00:02:31.680><c> warnings</c><00:02:32.480><c> and</c><00:02:32.800><c> lack</c><00:02:33.000><c> of</c><00:02:33.200><c> patient</c>

00:02:33.509 --> 00:02:33.519 align:start position:0%
generic warnings and lack of patient
 

00:02:33.519 --> 00:02:36.070 align:start position:0%
generic warnings and lack of patient
specific<00:02:34.200><c> insights</c><00:02:35.200><c> these</c><00:02:35.400><c> Solutions</c><00:02:35.879><c> are</c>

00:02:36.070 --> 00:02:36.080 align:start position:0%
specific insights these Solutions are
 

00:02:36.080 --> 00:02:39.589 align:start position:0%
specific insights these Solutions are
helpful<00:02:36.760><c> but</c><00:02:36.920><c> not</c><00:02:37.599><c> foolproof</c><00:02:38.599><c> so</c><00:02:39.120><c> what</c><00:02:39.280><c> can</c><00:02:39.440><c> we</c>

00:02:39.589 --> 00:02:39.599 align:start position:0%
helpful but not foolproof so what can we
 

00:02:39.599 --> 00:02:44.630 align:start position:0%
helpful but not foolproof so what can we
do<00:02:39.800><c> better</c>

00:02:44.630 --> 00:02:44.640 align:start position:0%
 
 

00:02:44.640 --> 00:02:47.509 align:start position:0%
 
here<00:02:45.640><c> so</c><00:02:45.959><c> this</c><00:02:46.080><c> is</c><00:02:46.280><c> where</c><00:02:46.599><c> the</c><00:02:46.760><c> ADR</c><00:02:47.159><c> Shield</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
here so this is where the ADR Shield
 

00:02:47.519 --> 00:02:50.430 align:start position:0%
here so this is where the ADR Shield
comes<00:02:47.760><c> in</c><00:02:48.480><c> it's</c><00:02:48.640><c> an</c><00:02:48.879><c> AI</c><00:02:49.239><c> powered</c><00:02:49.599><c> Solution</c>

00:02:50.430 --> 00:02:50.440 align:start position:0%
comes in it's an AI powered Solution
 

00:02:50.440 --> 00:02:53.190 align:start position:0%
comes in it's an AI powered Solution
that's<00:02:50.720><c> designed</c><00:02:51.159><c> to</c><00:02:51.360><c> simplify</c><00:02:52.080><c> drug</c><00:02:52.400><c> safety</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
that's designed to simplify drug safety
 

00:02:53.200 --> 00:02:55.550 align:start position:0%
that's designed to simplify drug safety
and<00:02:53.440><c> provide</c><00:02:53.879><c> more</c><00:02:54.159><c> personalized</c><00:02:54.959><c> actionable</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
and provide more personalized actionable
 

00:02:55.560 --> 00:02:58.670 align:start position:0%
and provide more personalized actionable
insights<00:02:56.360><c> for</c><00:02:56.599><c> both</c><00:02:56.800><c> clinicians</c><00:02:57.680><c> and</c>

00:02:58.670 --> 00:02:58.680 align:start position:0%
insights for both clinicians and
 

00:02:58.680 --> 00:03:01.350 align:start position:0%
insights for both clinicians and
patients<00:02:59.680><c> now</c><00:02:59.879><c> now</c><00:03:00.200><c> let's</c><00:03:00.720><c> quickly</c><00:03:01.080><c> take</c><00:03:01.200><c> a</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
patients now now let's quickly take a
 

00:03:01.360 --> 00:03:04.670 align:start position:0%
patients now now let's quickly take a
look<00:03:01.560><c> at</c><00:03:01.879><c> how</c><00:03:02.200><c> it</c><00:03:02.560><c> actually</c><00:03:03.000><c> works</c><00:03:04.000><c> so</c><00:03:04.360><c> for</c>

00:03:04.670 --> 00:03:04.680 align:start position:0%
look at how it actually works so for
 

00:03:04.680 --> 00:03:07.630 align:start position:0%
look at how it actually works so for
input<00:03:05.680><c> uh</c><00:03:05.799><c> a</c><00:03:05.959><c> patient</c><00:03:06.280><c> can</c><00:03:06.519><c> upload</c><00:03:07.120><c> medication</c>

00:03:07.630 --> 00:03:07.640 align:start position:0%
input uh a patient can upload medication
 

00:03:07.640 --> 00:03:10.430 align:start position:0%
input uh a patient can upload medication
images<00:03:08.519><c> the</c><00:03:08.760><c> prescription</c><00:03:09.319><c> images</c><00:03:09.959><c> or</c>

00:03:10.430 --> 00:03:10.440 align:start position:0%
images the prescription images or
 

00:03:10.440 --> 00:03:13.110 align:start position:0%
images the prescription images or
directly<00:03:10.840><c> enter</c><00:03:11.159><c> prescription</c><00:03:11.680><c> details</c><00:03:12.480><c> like</c>

00:03:13.110 --> 00:03:13.120 align:start position:0%
directly enter prescription details like
 

00:03:13.120 --> 00:03:15.789 align:start position:0%
directly enter prescription details like
100<00:03:13.319><c> mg</c><00:03:13.680><c> of</c><00:03:13.840><c> aspirin</c><00:03:14.360><c> or</c><00:03:14.560><c> 5</c><00:03:14.760><c> mg</c><00:03:15.040><c> of</c><00:03:15.159><c> warin</c>

00:03:15.789 --> 00:03:15.799 align:start position:0%
100 mg of aspirin or 5 mg of warin
 

00:03:15.799 --> 00:03:17.430 align:start position:0%
100 mg of aspirin or 5 mg of warin
whatever<00:03:16.200><c> the</c><00:03:16.440><c> The</c><00:03:16.720><c> Physician</c><00:03:17.120><c> has</c>

00:03:17.430 --> 00:03:17.440 align:start position:0%
whatever the The Physician has
 

00:03:17.440 --> 00:03:20.789 align:start position:0%
whatever the The Physician has
prescribed<00:03:18.440><c> and</c><00:03:18.680><c> it</c><00:03:18.840><c> uses</c><00:03:19.400><c> the</c><00:03:19.680><c> Gemini</c><00:03:20.200><c> 1.5</c>

00:03:20.789 --> 00:03:20.799 align:start position:0%
prescribed and it uses the Gemini 1.5
 

00:03:20.799 --> 00:03:23.910 align:start position:0%
prescribed and it uses the Gemini 1.5
flash<00:03:21.120><c> pro</c><00:03:21.400><c> model</c><00:03:22.040><c> to</c><00:03:22.280><c> process</c><00:03:22.599><c> the</c><00:03:22.799><c> input</c><00:03:23.519><c> and</c>

00:03:23.910 --> 00:03:23.920 align:start position:0%
flash pro model to process the input and
 

00:03:23.920 --> 00:03:26.670 align:start position:0%
flash pro model to process the input and
it<00:03:24.159><c> it</c><00:03:24.280><c> uses</c><00:03:24.720><c> AI</c><00:03:25.080><c> to</c><00:03:25.319><c> analyze</c><00:03:26.159><c> potential</c>

00:03:26.670 --> 00:03:26.680 align:start position:0%
it it uses AI to analyze potential
 

00:03:26.680 --> 00:03:29.869 align:start position:0%
it it uses AI to analyze potential
adverse<00:03:27.120><c> drug</c><00:03:27.640><c> reactions</c><00:03:28.640><c> for</c><00:03:28.959><c> output</c><00:03:29.519><c> you</c>

00:03:29.869 --> 00:03:29.879 align:start position:0%
adverse drug reactions for output you
 

00:03:29.879 --> 00:03:32.630 align:start position:0%
adverse drug reactions for output you
get<00:03:30.120><c> simple</c><00:03:30.680><c> actionable</c><00:03:31.200><c> warnings</c><00:03:32.120><c> about</c>

00:03:32.630 --> 00:03:32.640 align:start position:0%
get simple actionable warnings about
 

00:03:32.640 --> 00:03:35.149 align:start position:0%
get simple actionable warnings about
possible<00:03:33.080><c> drug</c><00:03:33.519><c> interactions</c><00:03:34.519><c> and</c><00:03:34.760><c> more</c>

00:03:35.149 --> 00:03:35.159 align:start position:0%
possible drug interactions and more
 

00:03:35.159 --> 00:03:38.670 align:start position:0%
possible drug interactions and more
importantly<00:03:36.159><c> personalized</c><00:03:37.080><c> insights</c><00:03:38.080><c> the</c><00:03:38.280><c> AI</c>

00:03:38.670 --> 00:03:38.680 align:start position:0%
importantly personalized insights the AI
 

00:03:38.680 --> 00:03:41.030 align:start position:0%
importantly personalized insights the AI
alerts<00:03:39.319><c> patients</c><00:03:39.799><c> on</c><00:03:40.040><c> symptoms</c><00:03:40.680><c> they</c><00:03:40.799><c> should</c>

00:03:41.030 --> 00:03:41.040 align:start position:0%
alerts patients on symptoms they should
 

00:03:41.040 --> 00:03:43.670 align:start position:0%
alerts patients on symptoms they should
monitor<00:03:42.040><c> all</c><00:03:42.360><c> in</c><00:03:42.480><c> a</c><00:03:42.720><c> way</c><00:03:43.040><c> that's</c><00:03:43.239><c> easy</c><00:03:43.519><c> for</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
monitor all in a way that's easy for
 

00:03:43.680 --> 00:03:46.910 align:start position:0%
monitor all in a way that's easy for
them<00:03:43.840><c> to</c><00:03:44.480><c> understand</c><00:03:45.480><c> of</c><00:03:45.680><c> course</c><00:03:46.480><c> it's</c><00:03:46.720><c> too</c>

00:03:46.910 --> 00:03:46.920 align:start position:0%
them to understand of course it's too
 

00:03:46.920 --> 00:03:49.589 align:start position:0%
them to understand of course it's too
much<00:03:47.080><c> of</c><00:03:47.200><c> information</c><00:03:47.560><c> to</c><00:03:47.840><c> consume</c><00:03:48.840><c> but</c><00:03:49.360><c> let's</c>

00:03:49.589 --> 00:03:49.599 align:start position:0%
much of information to consume but let's
 

00:03:49.599 --> 00:03:51.990 align:start position:0%
much of information to consume but let's
take<00:03:49.760><c> a</c><00:03:49.959><c> quick</c><00:03:50.200><c> look</c><00:03:50.599><c> at</c><00:03:50.840><c> how</c><00:03:51.040><c> the</c><00:03:51.599><c> tool</c>

00:03:51.990 --> 00:03:52.000 align:start position:0%
take a quick look at how the tool
 

00:03:52.000 --> 00:03:55.990 align:start position:0%
take a quick look at how the tool
actually<00:03:52.400><c> works</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
 
 

00:03:56.000 --> 00:03:59.470 align:start position:0%
 
Shing<00:03:57.000><c> so</c><00:03:57.280><c> with</c><00:03:57.519><c> that</c><00:03:58.079><c> um</c><00:03:58.400><c> let's</c><00:03:58.720><c> take</c><00:03:58.879><c> a</c><00:03:59.079><c> quick</c>

00:03:59.470 --> 00:03:59.480 align:start position:0%
Shing so with that um let's take a quick
 

00:03:59.480 --> 00:04:01.630 align:start position:0%
Shing so with that um let's take a quick
look<00:03:59.799><c> look</c><00:03:59.959><c> at</c><00:04:00.079><c> the</c>

00:04:01.630 --> 00:04:01.640 align:start position:0%
look look at the
 

00:04:01.640 --> 00:04:04.789 align:start position:0%
look look at the
demo<00:04:02.640><c> so</c><00:04:02.840><c> looking</c><00:04:03.159><c> at</c><00:04:03.360><c> the</c><00:04:03.680><c> demo</c><00:04:04.040><c> we</c><00:04:04.280><c> do</c><00:04:04.560><c> have</c>

00:04:04.789 --> 00:04:04.799 align:start position:0%
demo so looking at the demo we do have
 

00:04:04.799 --> 00:04:07.190 align:start position:0%
demo so looking at the demo we do have
three<00:04:05.079><c> options</c><00:04:05.599><c> the</c><00:04:05.799><c> first</c><00:04:06.200><c> being</c><00:04:06.680><c> uploading</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
three options the first being uploading
 

00:04:07.200 --> 00:04:10.190 align:start position:0%
three options the first being uploading
the<00:04:07.319><c> medical</c><00:04:07.920><c> medicine</c><00:04:08.400><c> images</c><00:04:09.400><c> so</c><00:04:09.879><c> uh</c><00:04:10.040><c> the</c>

00:04:10.190 --> 00:04:10.200 align:start position:0%
the medical medicine images so uh the
 

00:04:10.200 --> 00:04:11.670 align:start position:0%
the medical medicine images so uh the
images<00:04:10.560><c> that</c><00:04:10.680><c> the</c><00:04:10.799><c> patients</c><00:04:11.200><c> have</c><00:04:11.360><c> of</c><00:04:11.519><c> their</c>

00:04:11.670 --> 00:04:11.680 align:start position:0%
images that the patients have of their
 

00:04:11.680 --> 00:04:13.949 align:start position:0%
images that the patients have of their
medication<00:04:12.159><c> can</c><00:04:12.319><c> be</c><00:04:12.480><c> uploaded</c><00:04:13.079><c> here</c><00:04:13.560><c> in</c><00:04:13.720><c> this</c>

00:04:13.949 --> 00:04:13.959 align:start position:0%
medication can be uploaded here in this
 

00:04:13.959 --> 00:04:17.189 align:start position:0%
medication can be uploaded here in this
case<00:04:14.280><c> aspirin</c><00:04:14.720><c> and</c><00:04:15.199><c> OIC</c><00:04:16.199><c> so</c><00:04:16.400><c> when</c><00:04:16.639><c> clicked</c><00:04:16.959><c> on</c>

00:04:17.189 --> 00:04:17.199 align:start position:0%
case aspirin and OIC so when clicked on
 

00:04:17.199 --> 00:04:19.670 align:start position:0%
case aspirin and OIC so when clicked on
submit<00:04:17.919><c> it</c><00:04:18.079><c> takes</c><00:04:18.560><c> less</c><00:04:18.799><c> than</c><00:04:18.919><c> a</c><00:04:19.079><c> minute</c><00:04:19.359><c> to</c>

00:04:19.670 --> 00:04:19.680 align:start position:0%
submit it takes less than a minute to
 

00:04:19.680 --> 00:04:22.230 align:start position:0%
submit it takes less than a minute to
analyze<00:04:20.479><c> the</c><00:04:20.639><c> interactions</c><00:04:21.199><c> of</c><00:04:21.400><c> these</c><00:04:21.959><c> uh</c>

00:04:22.230 --> 00:04:22.240 align:start position:0%
analyze the interactions of these uh
 

00:04:22.240 --> 00:04:24.710 align:start position:0%
analyze the interactions of these uh
medications<00:04:22.919><c> with</c><00:04:23.120><c> one</c><00:04:23.280><c> another</c><00:04:24.080><c> and</c><00:04:24.280><c> come</c><00:04:24.479><c> up</c>

00:04:24.710 --> 00:04:24.720 align:start position:0%
medications with one another and come up
 

00:04:24.720 --> 00:04:27.590 align:start position:0%
medications with one another and come up
with<00:04:25.199><c> the</c><00:04:25.440><c> detailed</c><00:04:26.000><c> description</c><00:04:26.479><c> as</c><00:04:26.600><c> to</c><00:04:26.800><c> how</c>

00:04:27.590 --> 00:04:27.600 align:start position:0%
with the detailed description as to how
 

00:04:27.600 --> 00:04:29.310 align:start position:0%
with the detailed description as to how
um<00:04:27.800><c> this</c><00:04:28.000><c> specific</c><00:04:28.400><c> medication</c><00:04:28.880><c> interact</c>

00:04:29.310 --> 00:04:29.320 align:start position:0%
um this specific medication interact
 

00:04:29.320 --> 00:04:31.990 align:start position:0%
um this specific medication interact
with<00:04:29.440><c> each</c><00:04:29.560><c> other</c><00:04:29.759><c> other</c><00:04:30.600><c> so</c><00:04:31.000><c> as</c><00:04:31.120><c> we</c><00:04:31.280><c> can</c><00:04:31.440><c> see</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
with each other other so as we can see
 

00:04:32.000 --> 00:04:35.189 align:start position:0%
with each other other so as we can see
it<00:04:32.280><c> gives</c><00:04:32.479><c> you</c><00:04:32.720><c> a</c><00:04:32.960><c> detailed</c><00:04:33.960><c> um</c><00:04:34.759><c> description</c>

00:04:35.189 --> 00:04:35.199 align:start position:0%
it gives you a detailed um description
 

00:04:35.199 --> 00:04:37.670 align:start position:0%
it gives you a detailed um description
of<00:04:35.360><c> how</c><00:04:35.520><c> it</c><00:04:35.639><c> is</c><00:04:35.880><c> interacted</c><00:04:36.880><c> with</c><00:04:37.199><c> an</c>

00:04:37.670 --> 00:04:37.680 align:start position:0%
of how it is interacted with an
 

00:04:37.680 --> 00:04:39.790 align:start position:0%
of how it is interacted with an
indication<00:04:38.320><c> mentioning</c><00:04:38.800><c> that</c><00:04:39.080><c> it</c><00:04:39.199><c> is</c>

00:04:39.790 --> 00:04:39.800 align:start position:0%
indication mentioning that it is
 

00:04:39.800 --> 00:04:42.990 align:start position:0%
indication mentioning that it is
generated<00:04:40.280><c> by</c><00:04:40.400><c> an</c><00:04:40.600><c> AI</c><00:04:41.440><c> model</c><00:04:42.400><c> and</c><00:04:42.600><c> it</c><00:04:42.759><c> gives</c>

00:04:42.990 --> 00:04:43.000 align:start position:0%
generated by an AI model and it gives
 

00:04:43.000 --> 00:04:45.390 align:start position:0%
generated by an AI model and it gives
you<00:04:43.280><c> what</c><00:04:43.520><c> aspirin</c><00:04:44.000><c> is</c><00:04:44.199><c> and</c><00:04:44.400><c> what</c><00:04:44.919><c> waterin</c>

00:04:45.390 --> 00:04:45.400 align:start position:0%
you what aspirin is and what waterin
 

00:04:45.400 --> 00:04:48.310 align:start position:0%
you what aspirin is and what waterin
does<00:04:46.080><c> and</c><00:04:46.199><c> the</c><00:04:46.360><c> potential</c><00:04:46.880><c> interaction</c><00:04:47.880><c> so</c><00:04:48.160><c> in</c>

00:04:48.310 --> 00:04:48.320 align:start position:0%
does and the potential interaction so in
 

00:04:48.320 --> 00:04:50.830 align:start position:0%
does and the potential interaction so in
this<00:04:48.560><c> case</c><00:04:48.960><c> both</c><00:04:49.160><c> of</c><00:04:49.360><c> them</c><00:04:49.759><c> cause</c><00:04:50.120><c> low</c><00:04:50.360><c> blood</c>

00:04:50.830 --> 00:04:50.840 align:start position:0%
this case both of them cause low blood
 

00:04:50.840 --> 00:04:53.950 align:start position:0%
this case both of them cause low blood
sugar<00:04:51.560><c> so</c><00:04:51.800><c> when</c><00:04:52.320><c> uh</c><00:04:52.479><c> consumed</c><00:04:53.039><c> together</c><00:04:53.720><c> might</c>

00:04:53.950 --> 00:04:53.960 align:start position:0%
sugar so when uh consumed together might
 

00:04:53.960 --> 00:04:55.469 align:start position:0%
sugar so when uh consumed together might
impact<00:04:54.280><c> blood</c><00:04:54.520><c> sugar</c><00:04:54.800><c> levels</c><00:04:55.120><c> is</c><00:04:55.280><c> an</c>

00:04:55.469 --> 00:04:55.479 align:start position:0%
impact blood sugar levels is an
 

00:04:55.479 --> 00:04:57.550 align:start position:0%
impact blood sugar levels is an
indication<00:04:56.000><c> that's</c><00:04:56.400><c> been</c><00:04:56.680><c> provided</c><00:04:57.080><c> by</c><00:04:57.240><c> the</c>

00:04:57.550 --> 00:04:57.560 align:start position:0%
indication that's been provided by the
 

00:04:57.560 --> 00:05:00.390 align:start position:0%
indication that's been provided by the
model<00:04:58.560><c> and</c><00:04:58.880><c> also</c><00:04:59.120><c> it</c><00:04:59.320><c> gives</c><00:04:59.479><c> you</c><00:04:59.800><c> a</c><00:04:59.919><c> list</c><00:05:00.080><c> of</c><00:05:00.320><c> uh</c>

00:05:00.390 --> 00:05:00.400 align:start position:0%
model and also it gives you a list of uh
 

00:05:00.400 --> 00:05:01.909 align:start position:0%
model and also it gives you a list of uh
recommendations<00:05:00.960><c> and</c><00:05:01.120><c> warnings</c><00:05:01.680><c> with</c><00:05:01.800><c> the</c>

00:05:01.909 --> 00:05:01.919 align:start position:0%
recommendations and warnings with the
 

00:05:01.919 --> 00:05:04.270 align:start position:0%
recommendations and warnings with the
main<00:05:02.160><c> intent</c><00:05:02.759><c> being</c><00:05:03.240><c> to</c><00:05:03.639><c> contact</c><00:05:04.080><c> your</c>

00:05:04.270 --> 00:05:04.280 align:start position:0%
main intent being to contact your
 

00:05:04.280 --> 00:05:07.390 align:start position:0%
main intent being to contact your
healthcare<00:05:04.919><c> provider</c><00:05:05.919><c> um</c><00:05:06.199><c> to</c><00:05:06.680><c> and</c><00:05:06.960><c> not</c><00:05:07.160><c> make</c>

00:05:07.390 --> 00:05:07.400 align:start position:0%
healthcare provider um to and not make
 

00:05:07.400 --> 00:05:09.510 align:start position:0%
healthcare provider um to and not make
any<00:05:07.680><c> decisions</c><00:05:08.039><c> on</c><00:05:08.199><c> your</c><00:05:08.360><c> own</c><00:05:08.960><c> and</c><00:05:09.160><c> also</c><00:05:09.400><c> it</c>

00:05:09.510 --> 00:05:09.520 align:start position:0%
any decisions on your own and also it
 

00:05:09.520 --> 00:05:11.350 align:start position:0%
any decisions on your own and also it
gives<00:05:09.759><c> out</c><00:05:10.039><c> references</c><00:05:10.600><c> as</c><00:05:10.759><c> well</c><00:05:10.919><c> from</c><00:05:11.120><c> where</c>

00:05:11.350 --> 00:05:11.360 align:start position:0%
gives out references as well from where
 

00:05:11.360 --> 00:05:12.749 align:start position:0%
gives out references as well from where
these<00:05:11.560><c> informations</c><00:05:12.000><c> are</c>

00:05:12.749 --> 00:05:12.759 align:start position:0%
these informations are
 

00:05:12.759 --> 00:05:15.430 align:start position:0%
these informations are
fetched<00:05:13.759><c> the</c><00:05:13.960><c> second</c><00:05:14.280><c> option</c><00:05:14.600><c> is</c><00:05:14.840><c> to</c><00:05:15.039><c> upload</c>

00:05:15.430 --> 00:05:15.440 align:start position:0%
fetched the second option is to upload
 

00:05:15.440 --> 00:05:17.629 align:start position:0%
fetched the second option is to upload
the<00:05:15.680><c> prescription</c><00:05:16.120><c> images</c><00:05:16.560><c> directly</c><00:05:17.280><c> so</c><00:05:17.479><c> when</c>

00:05:17.629 --> 00:05:17.639 align:start position:0%
the prescription images directly so when
 

00:05:17.639 --> 00:05:19.309 align:start position:0%
the prescription images directly so when
a<00:05:17.800><c> patient</c><00:05:18.160><c> has</c><00:05:18.400><c> like</c><00:05:18.800><c> one</c><00:05:18.960><c> or</c><00:05:19.120><c> more</c>

00:05:19.309 --> 00:05:19.319 align:start position:0%
a patient has like one or more
 

00:05:19.319 --> 00:05:21.309 align:start position:0%
a patient has like one or more
prescriptions<00:05:19.880><c> from</c><00:05:20.080><c> different</c><00:05:20.400><c> Physicians</c>

00:05:21.309 --> 00:05:21.319 align:start position:0%
prescriptions from different Physicians
 

00:05:21.319 --> 00:05:24.230 align:start position:0%
prescriptions from different Physicians
they<00:05:21.479><c> can</c><00:05:21.680><c> simply</c><00:05:22.000><c> upload</c><00:05:22.440><c> them</c><00:05:23.400><c> um</c><00:05:23.680><c> and</c><00:05:23.840><c> then</c>

00:05:24.230 --> 00:05:24.240 align:start position:0%
they can simply upload them um and then
 

00:05:24.240 --> 00:05:26.550 align:start position:0%
they can simply upload them um and then
we<00:05:24.400><c> all</c><00:05:24.600><c> know</c><00:05:25.479><c> uh</c><00:05:25.639><c> how</c><00:05:25.800><c> to</c><00:05:26.280><c> understand</c><00:05:26.400><c> a</c>

00:05:26.550 --> 00:05:26.560 align:start position:0%
we all know uh how to understand a
 

00:05:26.560 --> 00:05:28.950 align:start position:0%
we all know uh how to understand a
prescription<00:05:27.440><c> right</c><00:05:27.680><c> it's</c><00:05:28.039><c> it's</c><00:05:28.479><c> really</c>

00:05:28.950 --> 00:05:28.960 align:start position:0%
prescription right it's it's really
 

00:05:28.960 --> 00:05:30.230 align:start position:0%
prescription right it's it's really
difficult<00:05:29.120><c> at</c><00:05:29.199><c> least</c><00:05:29.360><c> for</c><00:05:29.479><c> me</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
difficult at least for me
 

00:05:30.240 --> 00:05:33.550 align:start position:0%
difficult at least for me
so<00:05:30.759><c> um</c><00:05:30.960><c> when</c><00:05:31.160><c> uploaded</c><00:05:31.800><c> onto</c><00:05:32.120><c> this</c><00:05:32.280><c> toool</c><00:05:33.080><c> the</c>

00:05:33.550 --> 00:05:33.560 align:start position:0%
so um when uploaded onto this toool the
 

00:05:33.560 --> 00:05:35.710 align:start position:0%
so um when uploaded onto this toool the
model<00:05:34.280><c> understands</c><00:05:34.560><c> what's</c><00:05:34.759><c> written</c><00:05:35.039><c> in</c>

00:05:35.710 --> 00:05:35.720 align:start position:0%
model understands what's written in
 

00:05:35.720 --> 00:05:38.309 align:start position:0%
model understands what's written in
these<00:05:36.240><c> prescriptions</c><00:05:37.240><c> uh</c><00:05:37.360><c> and</c><00:05:37.520><c> then</c><00:05:37.800><c> fetches</c>

00:05:38.309 --> 00:05:38.319 align:start position:0%
these prescriptions uh and then fetches
 

00:05:38.319 --> 00:05:41.029 align:start position:0%
these prescriptions uh and then fetches
all<00:05:38.479><c> the</c><00:05:38.639><c> medicines</c><00:05:39.479><c> that</c><00:05:39.759><c> that</c><00:05:39.840><c> is</c><00:05:40.039><c> available</c>

00:05:41.029 --> 00:05:41.039 align:start position:0%
all the medicines that that is available
 

00:05:41.039 --> 00:05:43.950 align:start position:0%
all the medicines that that is available
and<00:05:41.600><c> uh</c><00:05:42.080><c> as</c><00:05:42.199><c> you</c><00:05:42.319><c> can</c><00:05:42.440><c> see</c><00:05:42.840><c> it</c><00:05:43.360><c> lists</c><00:05:43.720><c> all</c><00:05:43.880><c> the</c>

00:05:43.950 --> 00:05:43.960 align:start position:0%
and uh as you can see it lists all the
 

00:05:43.960 --> 00:05:45.670 align:start position:0%
and uh as you can see it lists all the
medicines<00:05:44.360><c> that</c><00:05:44.479><c> are</c><00:05:44.600><c> available</c><00:05:44.960><c> in</c><00:05:45.160><c> both</c><00:05:45.440><c> the</c>

00:05:45.670 --> 00:05:45.680 align:start position:0%
medicines that are available in both the
 

00:05:45.680 --> 00:05:48.029 align:start position:0%
medicines that are available in both the
both<00:05:45.880><c> of</c><00:05:46.000><c> these</c><00:05:46.479><c> prescriptions</c><00:05:47.479><c> and</c><00:05:47.759><c> also</c>

00:05:48.029 --> 00:05:48.039 align:start position:0%
both of these prescriptions and also
 

00:05:48.039 --> 00:05:50.390 align:start position:0%
both of these prescriptions and also
lists<00:05:48.360><c> out</c><00:05:48.560><c> the</c><00:05:48.680><c> potential</c><00:05:49.120><c> interactions</c><00:05:50.120><c> so</c>

00:05:50.390 --> 00:05:50.400 align:start position:0%
lists out the potential interactions so
 

00:05:50.400 --> 00:05:53.950 align:start position:0%
lists out the potential interactions so
amoxicilin<00:05:51.120><c> and</c><00:05:51.280><c> syin</c><00:05:52.039><c> do</c><00:05:52.199><c> not</c><00:05:52.680><c> go</c><00:05:53.039><c> together</c><00:05:53.759><c> I</c>

00:05:53.950 --> 00:05:53.960 align:start position:0%
amoxicilin and syin do not go together I
 

00:05:53.960 --> 00:05:56.110 align:start position:0%
amoxicilin and syin do not go together I
guess<00:05:54.680><c> U</c><00:05:54.800><c> and</c><00:05:54.960><c> then</c><00:05:55.199><c> three</c><00:05:55.440><c> other</c><00:05:55.639><c> drugs</c>

00:05:56.110 --> 00:05:56.120 align:start position:0%
guess U and then three other drugs
 

00:05:56.120 --> 00:05:57.710 align:start position:0%
guess U and then three other drugs
listed<00:05:56.440><c> in</c><00:05:56.520><c> the</c><00:05:56.639><c> prescription</c><00:05:57.199><c> do</c><00:05:57.400><c> not</c>

00:05:57.710 --> 00:05:57.720 align:start position:0%
listed in the prescription do not
 

00:05:57.720 --> 00:05:59.950 align:start position:0%
listed in the prescription do not
interact<00:05:58.199><c> really</c><00:05:58.520><c> well</c><00:05:59.240><c> and</c><00:05:59.440><c> again</c><00:05:59.639><c> again</c><00:05:59.800><c> it</c>

00:05:59.950 --> 00:05:59.960 align:start position:0%
interact really well and again again it
 

00:05:59.960 --> 00:06:01.590 align:start position:0%
interact really well and again again it
gives<00:06:00.160><c> you</c><00:06:00.360><c> a</c><00:06:00.479><c> recommendation</c><00:06:01.039><c> to</c><00:06:01.240><c> consult</c>

00:06:01.590 --> 00:06:01.600 align:start position:0%
gives you a recommendation to consult
 

00:06:01.600 --> 00:06:04.029 align:start position:0%
gives you a recommendation to consult
your<00:06:01.840><c> physician</c><00:06:02.360><c> before</c><00:06:03.120><c> uh</c><00:06:03.240><c> taking</c><00:06:03.600><c> any</c>

00:06:04.029 --> 00:06:04.039 align:start position:0%
your physician before uh taking any
 

00:06:04.039 --> 00:06:08.189 align:start position:0%
your physician before uh taking any
action<00:06:05.039><c> and</c><00:06:05.440><c> finally</c><00:06:06.120><c> we</c><00:06:06.280><c> have</c><00:06:06.560><c> the</c><00:06:07.199><c> third</c><00:06:07.960><c> U</c>

00:06:08.189 --> 00:06:08.199 align:start position:0%
action and finally we have the third U
 

00:06:08.199 --> 00:06:09.830 align:start position:0%
action and finally we have the third U
option<00:06:08.560><c> where</c><00:06:08.720><c> the</c><00:06:08.840><c> patients</c><00:06:09.240><c> can</c><00:06:09.560><c> simply</c>

00:06:09.830 --> 00:06:09.840 align:start position:0%
option where the patients can simply
 

00:06:09.840 --> 00:06:12.589 align:start position:0%
option where the patients can simply
enter<00:06:10.160><c> the</c><00:06:10.360><c> text</c><00:06:11.080><c> of</c><00:06:11.280><c> their</c><00:06:11.479><c> medications</c><00:06:12.280><c> so</c>

00:06:12.589 --> 00:06:12.599 align:start position:0%
enter the text of their medications so
 

00:06:12.599 --> 00:06:15.390 align:start position:0%
enter the text of their medications so
aspirin<00:06:13.199><c> warpin</c><00:06:14.199><c> and</c><00:06:14.479><c> to</c><00:06:14.680><c> test</c><00:06:14.880><c> the</c><00:06:15.000><c> model</c><00:06:15.319><c> if</c>

00:06:15.390 --> 00:06:15.400 align:start position:0%
aspirin warpin and to test the model if
 

00:06:15.400 --> 00:06:18.070 align:start position:0%
aspirin warpin and to test the model if
it<00:06:15.599><c> works</c><00:06:15.800><c> on</c><00:06:16.080><c> other</c><00:06:16.639><c> random</c><00:06:17.199><c> texts</c><00:06:17.800><c> like</c><00:06:17.960><c> you</c>

00:06:18.070 --> 00:06:18.080 align:start position:0%
it works on other random texts like you
 

00:06:18.080 --> 00:06:20.589 align:start position:0%
it works on other random texts like you
know<00:06:18.360><c> can</c><00:06:18.599><c> mention</c><00:06:18.960><c> squid</c><00:06:19.319><c> fry</c><00:06:19.759><c> or</c><00:06:19.960><c> chicken</c>

00:06:20.589 --> 00:06:20.599 align:start position:0%
know can mention squid fry or chicken
 

00:06:20.599 --> 00:06:23.990 align:start position:0%
know can mention squid fry or chicken
stew<00:06:21.599><c> right</c><00:06:22.039><c> so</c><00:06:22.520><c> um</c><00:06:22.840><c> when</c><00:06:23.160><c> when</c><00:06:23.319><c> given</c><00:06:23.680><c> this</c><00:06:23.840><c> as</c>

00:06:23.990 --> 00:06:24.000 align:start position:0%
stew right so um when when given this as
 

00:06:24.000 --> 00:06:27.350 align:start position:0%
stew right so um when when given this as
an<00:06:24.360><c> input</c><00:06:25.360><c> the</c><00:06:25.479><c> model</c><00:06:25.880><c> again</c><00:06:26.680><c> analyzes</c><00:06:27.199><c> the</c>

00:06:27.350 --> 00:06:27.360 align:start position:0%
an input the model again analyzes the
 

00:06:27.360 --> 00:06:29.870 align:start position:0%
an input the model again analyzes the
interactions<00:06:28.240><c> of</c><00:06:28.479><c> these</c><00:06:29.000><c> and</c><00:06:29.120><c> then</c><00:06:29.319><c> gives</c><00:06:29.720><c> a</c>

00:06:29.870 --> 00:06:29.880 align:start position:0%
interactions of these and then gives a
 

00:06:29.880 --> 00:06:32.029 align:start position:0%
interactions of these and then gives a
list<00:06:30.120><c> of</c><00:06:30.639><c> possible</c><00:06:31.080><c> medical</c><00:06:31.479><c> interactions</c>

00:06:32.029 --> 00:06:32.039 align:start position:0%
list of possible medical interactions
 

00:06:32.039 --> 00:06:33.790 align:start position:0%
list of possible medical interactions
that<00:06:32.199><c> might</c><00:06:32.440><c> happen</c><00:06:33.120><c> with</c><00:06:33.280><c> a</c><00:06:33.479><c> clear</c>

00:06:33.790 --> 00:06:33.800 align:start position:0%
that might happen with a clear
 

00:06:33.800 --> 00:06:36.870 align:start position:0%
that might happen with a clear
indication<00:06:34.560><c> mentioning</c><00:06:35.199><c> that</c><00:06:36.000><c> uh</c><00:06:36.120><c> squid</c><00:06:36.479><c> fry</c>

00:06:36.870 --> 00:06:36.880 align:start position:0%
indication mentioning that uh squid fry
 

00:06:36.880 --> 00:06:39.589 align:start position:0%
indication mentioning that uh squid fry
and<00:06:37.280><c> chickens</c><00:06:37.759><c> to</c><00:06:38.039><c> as</c><00:06:38.160><c> you</c><00:06:38.280><c> can</c><00:06:38.440><c> see</c><00:06:39.000><c> are</c><00:06:39.319><c> food</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
and chickens to as you can see are food
 

00:06:39.599 --> 00:06:41.909 align:start position:0%
and chickens to as you can see are food
items<00:06:39.919><c> and</c><00:06:40.080><c> not</c><00:06:40.319><c> medications</c><00:06:41.000><c> so</c><00:06:41.199><c> the</c><00:06:41.840><c> uh</c>

00:06:41.909 --> 00:06:41.919 align:start position:0%
items and not medications so the uh
 

00:06:41.919 --> 00:06:44.350 align:start position:0%
items and not medications so the uh
model<00:06:42.280><c> works</c><00:06:42.560><c> with</c><00:06:42.800><c> medications</c><00:06:43.440><c> and</c><00:06:43.639><c> then</c><00:06:44.240><c> uh</c>

00:06:44.350 --> 00:06:44.360 align:start position:0%
model works with medications and then uh
 

00:06:44.360 --> 00:06:47.110 align:start position:0%
model works with medications and then uh
ignores<00:06:44.880><c> all</c><00:06:45.080><c> the</c><00:06:45.319><c> irelevant</c><00:06:46.120><c> non-med</c>

00:06:47.110 --> 00:06:47.120 align:start position:0%
ignores all the irelevant non-med
 

00:06:47.120 --> 00:06:49.629 align:start position:0%
ignores all the irelevant non-med
medical<00:06:47.440><c> terms</c><00:06:48.039><c> entered</c><00:06:48.400><c> into</c><00:06:48.599><c> the</c><00:06:48.720><c> tool</c><00:06:49.479><c> and</c>

00:06:49.629 --> 00:06:49.639 align:start position:0%
medical terms entered into the tool and
 

00:06:49.639 --> 00:06:51.469 align:start position:0%
medical terms entered into the tool and
again<00:06:49.880><c> it</c><00:06:50.000><c> gives</c><00:06:50.199><c> you</c><00:06:50.319><c> a</c><00:06:50.440><c> disclaimer</c><00:06:50.960><c> as</c><00:06:51.120><c> to</c>

00:06:51.469 --> 00:06:51.479 align:start position:0%
again it gives you a disclaimer as to
 

00:06:51.479 --> 00:06:53.670 align:start position:0%
again it gives you a disclaimer as to
consult<00:06:51.880><c> your</c><00:06:52.120><c> phys</c><00:06:53.120><c> along</c><00:06:53.400><c> with</c><00:06:53.560><c> the</c>

00:06:53.670 --> 00:06:53.680 align:start position:0%
consult your phys along with the
 

00:06:53.680 --> 00:06:55.990 align:start position:0%
consult your phys along with the
references<00:06:54.160><c> from</c><00:06:54.400><c> where</c><00:06:55.039><c> these</c><00:06:55.240><c> are</c><00:06:55.759><c> these</c>

00:06:55.990 --> 00:06:56.000 align:start position:0%
references from where these are these
 

00:06:56.000 --> 00:06:57.110 align:start position:0%
references from where these are these
information<00:06:56.400><c> is</c>

00:06:57.110 --> 00:06:57.120 align:start position:0%
information is
 

00:06:57.120 --> 00:07:01.150 align:start position:0%
information is
fetched<00:06:58.120><c> so</c><00:06:58.639><c> um</c><00:06:59.080><c> moving</c><00:06:59.360><c> on</c><00:06:59.639><c> one</c><00:07:00.319><c> from</c><00:07:00.520><c> the</c>

00:07:01.150 --> 00:07:01.160 align:start position:0%
fetched so um moving on one from the
 

00:07:01.160 --> 00:07:06.150 align:start position:0%
fetched so um moving on one from the
demo

00:07:06.150 --> 00:07:06.160 align:start position:0%
 
 

00:07:06.160 --> 00:07:10.070 align:start position:0%
 
oops<00:07:07.160><c> okay</c><00:07:07.560><c> so</c><00:07:08.039><c> um</c><00:07:08.840><c> speaking</c><00:07:09.599><c> speaking</c><00:07:09.919><c> of</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
oops okay so um speaking speaking of
 

00:07:10.080 --> 00:07:11.950 align:start position:0%
oops okay so um speaking speaking of
this<00:07:10.240><c> ADR</c><00:07:10.639><c> Shield</c><00:07:11.319><c> this</c><00:07:11.520><c> kind</c><00:07:11.720><c> of</c>

00:07:11.950 --> 00:07:11.960 align:start position:0%
this ADR Shield this kind of
 

00:07:11.960 --> 00:07:14.670 align:start position:0%
this ADR Shield this kind of
personalized<00:07:12.560><c> analysis</c><00:07:13.319><c> is</c><00:07:13.520><c> what</c><00:07:13.720><c> makes</c><00:07:14.319><c> ADR</c>

00:07:14.670 --> 00:07:14.680 align:start position:0%
personalized analysis is what makes ADR
 

00:07:14.680 --> 00:07:17.390 align:start position:0%
personalized analysis is what makes ADR
Shield<00:07:15.080><c> like</c><00:07:15.280><c> tools</c><00:07:15.680><c> so</c><00:07:15.879><c> powerful</c><00:07:16.759><c> it</c><00:07:17.000><c> saves</c>

00:07:17.390 --> 00:07:17.400 align:start position:0%
Shield like tools so powerful it saves
 

00:07:17.400 --> 00:07:19.350 align:start position:0%
Shield like tools so powerful it saves
time<00:07:17.599><c> for</c><00:07:17.800><c> clinicians</c><00:07:18.560><c> reduces</c><00:07:19.080><c> their</c>

00:07:19.350 --> 00:07:19.360 align:start position:0%
time for clinicians reduces their
 

00:07:19.360 --> 00:07:22.309 align:start position:0%
time for clinicians reduces their
workload<00:07:20.240><c> and</c><00:07:20.720><c> ultimately</c><00:07:21.360><c> ensures</c><00:07:21.919><c> patient</c>

00:07:22.309 --> 00:07:22.319 align:start position:0%
workload and ultimately ensures patient
 

00:07:22.319 --> 00:07:25.150 align:start position:0%
workload and ultimately ensures patient
better<00:07:23.080><c> I</c><00:07:23.160><c> mean</c><00:07:24.039><c> ensures</c><00:07:24.440><c> better</c><00:07:24.680><c> outcomes</c>

00:07:25.150 --> 00:07:25.160 align:start position:0%
better I mean ensures better outcomes
 

00:07:25.160 --> 00:07:28.230 align:start position:0%
better I mean ensures better outcomes
for<00:07:25.400><c> for</c><00:07:25.520><c> these</c><00:07:25.800><c> patients</c><00:07:26.800><c> for</c><00:07:27.440><c> um</c><00:07:27.720><c> patients</c>

00:07:28.230 --> 00:07:28.240 align:start position:0%
for for these patients for um patients
 

00:07:28.240 --> 00:07:30.670 align:start position:0%
for for these patients for um patients
it's<00:07:28.560><c> an</c><00:07:28.759><c> empowerment</c><00:07:29.280><c> through</c><00:07:29.680><c> knowledge</c>

00:07:30.670 --> 00:07:30.680 align:start position:0%
it's an empowerment through knowledge
 

00:07:30.680 --> 00:07:32.309 align:start position:0%
it's an empowerment through knowledge
understanding<00:07:30.960><c> their</c><00:07:31.160><c> medications</c><00:07:32.039><c> and</c><00:07:32.199><c> the</c>

00:07:32.309 --> 00:07:32.319 align:start position:0%
understanding their medications and the
 

00:07:32.319 --> 00:07:34.350 align:start position:0%
understanding their medications and the
risks<00:07:32.720><c> that</c><00:07:32.879><c> they</c>

00:07:34.350 --> 00:07:34.360 align:start position:0%
risks that they
 

00:07:34.360 --> 00:07:37.790 align:start position:0%
risks that they
carry<00:07:35.360><c> and</c><00:07:35.879><c> uh</c><00:07:36.199><c> looking</c><00:07:36.520><c> at</c><00:07:36.759><c> the</c><00:07:37.479><c> future</c>

00:07:37.790 --> 00:07:37.800 align:start position:0%
carry and uh looking at the future
 

00:07:37.800 --> 00:07:40.390 align:start position:0%
carry and uh looking at the future
enhancements<00:07:38.680><c> or</c><00:07:39.120><c> what</c><00:07:39.280><c> is</c><00:07:39.919><c> what</c><00:07:40.160><c> what</c><00:07:40.280><c> can</c>

00:07:40.390 --> 00:07:40.400 align:start position:0%
enhancements or what is what what can
 

00:07:40.400 --> 00:07:43.909 align:start position:0%
enhancements or what is what what can
you<00:07:40.639><c> expect</c><00:07:41.080><c> for</c><00:07:41.720><c> um</c><00:07:41.879><c> in</c><00:07:42.000><c> terms</c><00:07:42.280><c> of</c><00:07:43.120><c> uh</c><00:07:43.520><c> ADR</c>

00:07:43.909 --> 00:07:43.919 align:start position:0%
you expect for um in terms of uh ADR
 

00:07:43.919 --> 00:07:46.629 align:start position:0%
you expect for um in terms of uh ADR
Shield<00:07:44.599><c> so</c><00:07:44.919><c> we</c><00:07:45.120><c> we</c><00:07:45.400><c> we</c><00:07:45.599><c> first</c><00:07:45.919><c> aim</c><00:07:46.199><c> at</c><00:07:46.400><c> for</c>

00:07:46.629 --> 00:07:46.639 align:start position:0%
Shield so we we we first aim at for
 

00:07:46.639 --> 00:07:48.670 align:start position:0%
Shield so we we we first aim at for
seamless<00:07:47.120><c> integration</c><00:07:47.800><c> with</c><00:07:48.000><c> tele</c><00:07:48.280><c> Health</c>

00:07:48.670 --> 00:07:48.680 align:start position:0%
seamless integration with tele Health
 

00:07:48.680 --> 00:07:51.070 align:start position:0%
seamless integration with tele Health
platforms<00:07:49.680><c> this</c><00:07:49.800><c> will</c><00:07:50.080><c> ensure</c><00:07:50.479><c> realtime</c>

00:07:51.070 --> 00:07:51.080 align:start position:0%
platforms this will ensure realtime
 

00:07:51.080 --> 00:07:53.110 align:start position:0%
platforms this will ensure realtime
access<00:07:51.360><c> to</c><00:07:51.560><c> drug</c><00:07:51.879><c> interaction</c><00:07:52.360><c> warnings</c>

00:07:53.110 --> 00:07:53.120 align:start position:0%
access to drug interaction warnings
 

00:07:53.120 --> 00:07:55.749 align:start position:0%
access to drug interaction warnings
during<00:07:53.720><c> virtual</c><00:07:54.159><c> care</c><00:07:54.800><c> enhancing</c><00:07:55.400><c> patient</c>

00:07:55.749 --> 00:07:55.759 align:start position:0%
during virtual care enhancing patient
 

00:07:55.759 --> 00:07:58.670 align:start position:0%
during virtual care enhancing patient
safety<00:07:56.720><c> second</c><00:07:57.360><c> we're</c><00:07:57.599><c> working</c><00:07:57.919><c> on</c><00:07:58.199><c> improved</c>

00:07:58.670 --> 00:07:58.680 align:start position:0%
safety second we're working on improved
 

00:07:58.680 --> 00:08:01.749 align:start position:0%
safety second we're working on improved
usability<00:07:59.800><c> features</c><00:08:00.280><c> like</c><00:08:00.479><c> severity</c><00:08:01.039><c> ratings</c>

00:08:01.749 --> 00:08:01.759 align:start position:0%
usability features like severity ratings
 

00:08:01.759 --> 00:08:04.070 align:start position:0%
usability features like severity ratings
and<00:08:02.080><c> multilingual</c><00:08:02.800><c> support</c><00:08:03.560><c> will</c><00:08:03.759><c> make</c><00:08:03.919><c> the</c>

00:08:04.070 --> 00:08:04.080 align:start position:0%
and multilingual support will make the
 

00:08:04.080 --> 00:08:06.469 align:start position:0%
and multilingual support will make the
tool<00:08:04.520><c> even</c><00:08:04.759><c> more</c><00:08:05.000><c> user</c><00:08:05.360><c> friendly</c><00:08:05.879><c> for</c><00:08:06.159><c> both</c>

00:08:06.469 --> 00:08:06.479 align:start position:0%
tool even more user friendly for both
 

00:08:06.479 --> 00:08:09.749 align:start position:0%
tool even more user friendly for both
patients<00:08:07.080><c> and</c><00:08:07.599><c> clins</c><00:08:08.599><c> and</c><00:08:08.960><c> finally</c><00:08:09.479><c> we</c>

00:08:09.749 --> 00:08:09.759 align:start position:0%
patients and clins and finally we
 

00:08:09.759 --> 00:08:12.110 align:start position:0%
patients and clins and finally we
committed<00:08:10.120><c> to</c><00:08:10.280><c> Holistic</c><00:08:10.759><c> Health</c><00:08:11.120><c> Management</c>

00:08:12.110 --> 00:08:12.120 align:start position:0%
committed to Holistic Health Management
 

00:08:12.120 --> 00:08:14.510 align:start position:0%
committed to Holistic Health Management
by<00:08:12.400><c> integrating</c><00:08:13.039><c> preventive</c><00:08:13.599><c> health</c><00:08:13.919><c> tips</c>

00:08:14.510 --> 00:08:14.520 align:start position:0%
by integrating preventive health tips
 

00:08:14.520 --> 00:08:17.110 align:start position:0%
by integrating preventive health tips
and<00:08:14.720><c> lifestyle</c><00:08:15.280><c> recommendations</c><00:08:16.280><c> ADR</c><00:08:16.759><c> Shield</c>

00:08:17.110 --> 00:08:17.120 align:start position:0%
and lifestyle recommendations ADR Shield
 

00:08:17.120 --> 00:08:19.790 align:start position:0%
and lifestyle recommendations ADR Shield
will<00:08:17.319><c> go</c><00:08:17.599><c> beyond</c><00:08:18.080><c> just</c><00:08:18.280><c> identifying</c><00:08:18.960><c> risks</c><00:08:19.680><c> it</c>

00:08:19.790 --> 00:08:19.800 align:start position:0%
will go beyond just identifying risks it
 

00:08:19.800 --> 00:08:21.790 align:start position:0%
will go beyond just identifying risks it
will<00:08:20.000><c> help</c><00:08:20.319><c> patients</c><00:08:20.919><c> make</c><00:08:21.120><c> a</c><00:08:21.280><c> healthier</c>

00:08:21.790 --> 00:08:21.800 align:start position:0%
will help patients make a healthier
 

00:08:21.800 --> 00:08:24.550 align:start position:0%
will help patients make a healthier
choice<00:08:22.400><c> for</c><00:08:22.560><c> the</c><00:08:22.720><c> long</c><00:08:22.960><c> term</c><00:08:23.840><c> the</c><00:08:24.000><c> future</c><00:08:24.360><c> of</c>

00:08:24.550 --> 00:08:24.560 align:start position:0%
choice for the long term the future of
 

00:08:24.560 --> 00:08:26.790 align:start position:0%
choice for the long term the future of
digital<00:08:25.000><c> health</c><00:08:25.479><c> is</c><00:08:25.720><c> personalized</c><00:08:26.520><c> that</c><00:08:26.639><c> is</c>

00:08:26.790 --> 00:08:26.800 align:start position:0%
digital health is personalized that is
 

00:08:26.800 --> 00:08:29.749 align:start position:0%
digital health is personalized that is
for<00:08:27.000><c> sure</c><00:08:27.840><c> and</c><00:08:28.159><c> ADR</c><00:08:28.680><c> is</c><00:08:28.800><c> at</c><00:08:28.960><c> the</c><00:08:29.039><c> Forefront</c><00:08:29.560><c> end</c>

00:08:29.749 --> 00:08:29.759 align:start position:0%
for sure and ADR is at the Forefront end
 

00:08:29.759 --> 00:08:32.670 align:start position:0%
for sure and ADR is at the Forefront end
of<00:08:29.960><c> this</c>

00:08:32.670 --> 00:08:32.680 align:start position:0%
 
 

00:08:32.680 --> 00:08:37.829 align:start position:0%
 
change<00:08:33.680><c> and</c><00:08:34.360><c> in</c><00:08:34.479><c> order</c><00:08:34.719><c> to</c><00:08:35.240><c> conclude</c><00:08:35.839><c> this</c><00:08:36.839><c> um</c>

00:08:37.829 --> 00:08:37.839 align:start position:0%
change and in order to conclude this um
 

00:08:37.839 --> 00:08:40.070 align:start position:0%
change and in order to conclude this um
this<00:08:38.000><c> whole</c><00:08:38.279><c> talk</c><00:08:38.719><c> I'd</c><00:08:38.839><c> like</c><00:08:39.000><c> to</c><00:08:39.120><c> mention</c><00:08:39.560><c> that</c>

00:08:40.070 --> 00:08:40.080 align:start position:0%
this whole talk I'd like to mention that
 

00:08:40.080 --> 00:08:42.790 align:start position:0%
this whole talk I'd like to mention that
ADR<00:08:40.519><c> Shield</c><00:08:40.959><c> is</c><00:08:41.200><c> easy</c><00:08:41.479><c> to</c><00:08:41.719><c> adopt</c><00:08:42.560><c> it</c>

00:08:42.790 --> 00:08:42.800 align:start position:0%
ADR Shield is easy to adopt it
 

00:08:42.800 --> 00:08:44.670 align:start position:0%
ADR Shield is easy to adopt it
integrates<00:08:43.320><c> seamlessly</c><00:08:44.039><c> with</c><00:08:44.279><c> existing</c>

00:08:44.670 --> 00:08:44.680 align:start position:0%
integrates seamlessly with existing
 

00:08:44.680 --> 00:08:47.110 align:start position:0%
integrates seamlessly with existing
systems<00:08:45.399><c> the</c><00:08:45.560><c> model</c><00:08:45.839><c> used</c><00:08:46.160><c> here</c><00:08:46.320><c> is</c><00:08:46.519><c> Gemini</c>

00:08:47.110 --> 00:08:47.120 align:start position:0%
systems the model used here is Gemini
 

00:08:47.120 --> 00:08:49.790 align:start position:0%
systems the model used here is Gemini
but<00:08:47.320><c> then</c><00:08:47.959><c> it</c><00:08:48.080><c> can</c><00:08:48.240><c> be</c><00:08:48.399><c> used</c><00:08:48.839><c> with</c><00:08:49.000><c> a</c><00:08:49.120><c> non-</c><00:08:49.399><c> API</c>

00:08:49.790 --> 00:08:49.800 align:start position:0%
but then it can be used with a non- API
 

00:08:49.800 --> 00:08:52.910 align:start position:0%
but then it can be used with a non- API
in-house<00:08:50.240><c> model</c><00:08:50.600><c> as</c><00:08:50.760><c> well</c><00:08:51.000><c> it's</c><00:08:51.240><c> easy</c><00:08:51.480><c> to</c><00:08:52.320><c> um</c>

00:08:52.910 --> 00:08:52.920 align:start position:0%
in-house model as well it's easy to um
 

00:08:52.920 --> 00:08:54.870 align:start position:0%
in-house model as well it's easy to um
integrate<00:08:53.320><c> with</c><00:08:53.480><c> your</c><00:08:53.760><c> existing</c><00:08:54.320><c> workflow</c>

00:08:54.870 --> 00:08:54.880 align:start position:0%
integrate with your existing workflow
 

00:08:54.880 --> 00:08:57.509 align:start position:0%
integrate with your existing workflow
that<00:08:55.399><c> uh</c><00:08:55.600><c> that</c><00:08:55.800><c> you</c><00:08:55.920><c> might</c><00:08:56.200><c> have</c><00:08:56.920><c> and</c><00:08:57.120><c> it</c><00:08:57.279><c> also</c>

00:08:57.509 --> 00:08:57.519 align:start position:0%
that uh that you might have and it also
 

00:08:57.519 --> 00:08:59.670 align:start position:0%
that uh that you might have and it also
offers<00:08:57.839><c> a</c><00:08:58.040><c> big</c><00:08:58.279><c> impact</c><00:08:58.640><c> by</c><00:08:58.839><c> preventing</c>

00:08:59.670 --> 00:08:59.680 align:start position:0%
offers a big impact by preventing
 

00:08:59.680 --> 00:09:01.630 align:start position:0%
offers a big impact by preventing
hospitalizations<00:09:00.680><c> and</c><00:09:00.839><c> saving</c><00:09:01.240><c> time</c><00:09:01.480><c> for</c>

00:09:01.630 --> 00:09:01.640 align:start position:0%
hospitalizations and saving time for
 

00:09:01.640 --> 00:09:03.670 align:start position:0%
hospitalizations and saving time for
healthcare<00:09:02.200><c> providers</c><00:09:03.200><c> with</c><00:09:03.399><c> its</c>

00:09:03.670 --> 00:09:03.680 align:start position:0%
healthcare providers with its
 

00:09:03.680 --> 00:09:06.310 align:start position:0%
healthcare providers with its
personalized<00:09:04.320><c> safety</c><00:09:04.720><c> alerts</c><00:09:05.680><c> this</c><00:09:05.839><c> tool</c><00:09:06.200><c> is</c>

00:09:06.310 --> 00:09:06.320 align:start position:0%
personalized safety alerts this tool is
 

00:09:06.320 --> 00:09:08.430 align:start position:0%
personalized safety alerts this tool is
a<00:09:06.519><c> GameChanger</c><00:09:07.200><c> for</c><00:09:07.399><c> patient</c><00:09:07.720><c> safety</c>

00:09:08.430 --> 00:09:08.440 align:start position:0%
a GameChanger for patient safety
 

00:09:08.440 --> 00:09:10.430 align:start position:0%
a GameChanger for patient safety
tailored<00:09:08.959><c> to</c><00:09:09.200><c> each</c><00:09:09.480><c> individual's</c><00:09:10.120><c> Health</c>

00:09:10.430 --> 00:09:10.440 align:start position:0%
tailored to each individual's Health
 

00:09:10.440 --> 00:09:13.949 align:start position:0%
tailored to each individual's Health
profile<00:09:11.320><c> so</c><00:09:11.680><c> by</c><00:09:11.920><c> creating</c><00:09:12.760><c> uh</c><00:09:12.920><c> ADR</c><00:09:13.399><c> like</c><00:09:13.600><c> tools</c>

00:09:13.949 --> 00:09:13.959 align:start position:0%
profile so by creating uh ADR like tools
 

00:09:13.959 --> 00:09:17.949 align:start position:0%
profile so by creating uh ADR like tools
using<00:09:14.839><c> the</c><00:09:15.200><c> upcoming</c><00:09:15.720><c> and</c><00:09:15.920><c> blooming</c><00:09:16.519><c> AI</c><00:09:16.959><c> tools</c>

00:09:17.949 --> 00:09:17.959 align:start position:0%
using the upcoming and blooming AI tools
 

00:09:17.959 --> 00:09:20.190 align:start position:0%
using the upcoming and blooming AI tools
let's<00:09:18.519><c> take</c><00:09:18.680><c> an</c><00:09:18.959><c> take</c><00:09:19.120><c> a</c><00:09:19.240><c> oath</c><00:09:19.720><c> together</c><00:09:20.040><c> to</c>

00:09:20.190 --> 00:09:20.200 align:start position:0%
let's take an take a oath together to
 

00:09:20.200 --> 00:09:22.829 align:start position:0%
let's take an take a oath together to
make<00:09:20.399><c> the</c><00:09:20.720><c> lives</c><00:09:21.000><c> of</c><00:09:21.160><c> patients</c><00:09:21.640><c> better</c><00:09:22.640><c> so</c>

00:09:22.829 --> 00:09:22.839 align:start position:0%
make the lives of patients better so
 

00:09:22.839 --> 00:09:25.190 align:start position:0%
make the lives of patients better so
with<00:09:23.000><c> that</c><00:09:23.160><c> I</c><00:09:23.279><c> thank</c><00:09:23.519><c> you</c><00:09:23.640><c> all</c><00:09:23.839><c> for</c><00:09:24.600><c> um</c>

00:09:25.190 --> 00:09:25.200 align:start position:0%
with that I thank you all for um
 

00:09:25.200 --> 00:09:27.150 align:start position:0%
with that I thank you all for um
listening<00:09:25.560><c> to</c><00:09:25.680><c> my</c><00:09:25.880><c> talk</c><00:09:26.440><c> and</c><00:09:26.560><c> I</c><00:09:26.680><c> look</c><00:09:26.880><c> forward</c>

00:09:27.150 --> 00:09:27.160 align:start position:0%
listening to my talk and I look forward
 

00:09:27.160 --> 00:09:31.279 align:start position:0%
listening to my talk and I look forward
to<00:09:27.279><c> connecting</c><00:09:27.720><c> with</c><00:09:27.839><c> you</c><00:09:28.000><c> all</c><00:09:28.640><c> thank</c><00:09:28.839><c> you</c>

