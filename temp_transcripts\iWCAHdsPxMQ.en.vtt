WEBVTT
Kind: captions
Language: en

00:00:00.760 --> 00:00:03.149 align:start position:0%
 
this<00:00:00.880><c> is</c><00:00:01.000><c> a</c><00:00:01.160><c> course</c><00:00:01.480><c> about</c><00:00:01.760><c> Model</c><00:00:02.159><c> Management</c>

00:00:03.149 --> 00:00:03.159 align:start position:0%
this is a course about Model Management
 

00:00:03.159 --> 00:00:04.870 align:start position:0%
this is a course about Model Management
but<00:00:03.280><c> to</c><00:00:03.520><c> manage</c><00:00:03.840><c> models</c><00:00:04.200><c> we</c><00:00:04.359><c> first</c><00:00:04.560><c> need</c><00:00:04.720><c> to</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
but to manage models we first need to
 

00:00:04.880 --> 00:00:06.950 align:start position:0%
but to manage models we first need to
train<00:00:05.160><c> some</c><00:00:05.400><c> models</c><00:00:06.240><c> and</c><00:00:06.399><c> this</c><00:00:06.520><c> is</c><00:00:06.680><c> what</c><00:00:06.799><c> we're</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
train some models and this is what we're
 

00:00:06.960 --> 00:00:09.430 align:start position:0%
train some models and this is what we're
going<00:00:07.040><c> to</c><00:00:07.200><c> do</c><00:00:07.319><c> in</c><00:00:07.480><c> this</c><00:00:07.800><c> lesson</c><00:00:08.800><c> this</c><00:00:08.920><c> is</c><00:00:09.320><c> uh</c>

00:00:09.430 --> 00:00:09.440 align:start position:0%
going to do in this lesson this is uh
 

00:00:09.440 --> 00:00:11.950 align:start position:0%
going to do in this lesson this is uh
the<00:00:09.559><c> train.py</c><00:00:10.120><c> script</c><00:00:10.960><c> that</c><00:00:11.080><c> you</c><00:00:11.240><c> can</c><00:00:11.559><c> access</c>

00:00:11.950 --> 00:00:11.960 align:start position:0%
the train.py script that you can access
 

00:00:11.960 --> 00:00:14.589 align:start position:0%
the train.py script that you can access
in<00:00:12.120><c> our</c><00:00:12.360><c> course</c><00:00:12.639><c> repo</c><00:00:13.599><c> and</c><00:00:13.799><c> we</c><00:00:13.920><c> will</c><00:00:14.080><c> share</c><00:00:14.440><c> the</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
in our course repo and we will share the
 

00:00:14.599 --> 00:00:17.109 align:start position:0%
in our course repo and we will share the
link<00:00:15.000><c> uh</c><00:00:15.080><c> to</c><00:00:15.200><c> the</c><00:00:15.320><c> course</c><00:00:15.599><c> repo</c><00:00:16.000><c> below</c><00:00:16.840><c> this</c>

00:00:17.109 --> 00:00:17.119 align:start position:0%
link uh to the course repo below this
 

00:00:17.119 --> 00:00:18.950 align:start position:0%
link uh to the course repo below this
video<00:00:18.119><c> and</c><00:00:18.240><c> let's</c><00:00:18.400><c> take</c><00:00:18.520><c> a</c><00:00:18.640><c> look</c><00:00:18.800><c> what's</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
video and let's take a look what's
 

00:00:18.960 --> 00:00:21.189 align:start position:0%
video and let's take a look what's
happening<00:00:19.400><c> here</c><00:00:20.160><c> in</c><00:00:20.359><c> this</c><00:00:20.760><c> uh</c><00:00:20.920><c> in</c><00:00:21.039><c> this</c>

00:00:21.189 --> 00:00:21.199 align:start position:0%
happening here in this uh in this
 

00:00:21.199 --> 00:00:23.509 align:start position:0%
happening here in this uh in this
training<00:00:21.600><c> we</c><00:00:21.720><c> will</c><00:00:21.880><c> be</c><00:00:22.039><c> using</c><00:00:22.640><c> a</c><00:00:22.880><c> data</c><00:00:23.160><c> set</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
training we will be using a data set
 

00:00:23.519 --> 00:00:26.429 align:start position:0%
training we will be using a data set
called<00:00:24.000><c> alpaka</c><00:00:24.800><c> and</c><00:00:24.960><c> this</c><00:00:25.039><c> is</c><00:00:25.160><c> a</c><00:00:25.359><c> data</c><00:00:25.640><c> set</c><00:00:25.880><c> for</c>

00:00:26.429 --> 00:00:26.439 align:start position:0%
called alpaka and this is a data set for
 

00:00:26.439 --> 00:00:28.429 align:start position:0%
called alpaka and this is a data set for
find<00:00:26.760><c> unique</c><00:00:27.080><c> language</c><00:00:27.439><c> models</c><00:00:27.880><c> on</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
find unique language models on
 

00:00:28.439 --> 00:00:30.950 align:start position:0%
find unique language models on
instruction<00:00:29.000><c> following</c><00:00:30.359><c> so</c><00:00:30.560><c> let's</c><00:00:30.720><c> take</c><00:00:30.840><c> a</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
instruction following so let's take a
 

00:00:30.960 --> 00:00:32.830 align:start position:0%
instruction following so let's take a
look<00:00:31.080><c> at</c><00:00:31.199><c> this</c><00:00:31.359><c> data</c><00:00:31.599><c> set</c><00:00:31.800><c> I</c><00:00:31.920><c> like</c><00:00:32.040><c> to</c><00:00:32.559><c> I</c><00:00:32.680><c> like</c>

00:00:32.830 --> 00:00:32.840 align:start position:0%
look at this data set I like to I like
 

00:00:32.840 --> 00:00:34.830 align:start position:0%
look at this data set I like to I like
to<00:00:33.000><c> see</c><00:00:33.239><c> the</c><00:00:33.440><c> data</c><00:00:33.760><c> before</c><00:00:33.960><c> I</c><00:00:34.079><c> trade</c><00:00:34.360><c> my</c><00:00:34.520><c> model</c>

00:00:34.830 --> 00:00:34.840 align:start position:0%
to see the data before I trade my model
 

00:00:34.840 --> 00:00:37.389 align:start position:0%
to see the data before I trade my model
so<00:00:34.960><c> let's</c><00:00:35.120><c> take</c><00:00:35.200><c> a</c><00:00:35.320><c> look</c><00:00:35.440><c> at</c><00:00:35.559><c> this</c><00:00:35.719><c> data</c><00:00:36.399><c> set</c>

00:00:37.389 --> 00:00:37.399 align:start position:0%
so let's take a look at this data set
 

00:00:37.399 --> 00:00:39.470 align:start position:0%
so let's take a look at this data set
and<00:00:37.640><c> you've</c><00:00:37.960><c> already</c><00:00:38.200><c> seen</c><00:00:38.520><c> this</c><00:00:38.960><c> artifact</c>

00:00:39.470 --> 00:00:39.480 align:start position:0%
and you've already seen this artifact
 

00:00:39.480 --> 00:00:41.910 align:start position:0%
and you've already seen this artifact
view<00:00:40.039><c> we</c><00:00:40.120><c> are</c><00:00:40.280><c> using</c><00:00:40.680><c> artifact</c><00:00:41.480><c> in</c><00:00:41.680><c> weights</c>

00:00:41.910 --> 00:00:41.920 align:start position:0%
view we are using artifact in weights
 

00:00:41.920 --> 00:00:44.310 align:start position:0%
view we are using artifact in weights
and<00:00:42.079><c> biases</c><00:00:42.520><c> to</c><00:00:43.000><c> manage</c><00:00:43.360><c> and</c><00:00:43.600><c> version</c><00:00:44.039><c> our</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
and biases to manage and version our
 

00:00:44.320 --> 00:00:46.110 align:start position:0%
and biases to manage and version our
data<00:00:44.559><c> sets</c><00:00:45.360><c> and</c>

00:00:46.110 --> 00:00:46.120 align:start position:0%
data sets and
 

00:00:46.120 --> 00:00:48.990 align:start position:0%
data sets and
models<00:00:47.120><c> this</c><00:00:47.280><c> case</c><00:00:47.480><c> this</c><00:00:47.600><c> is</c><00:00:47.680><c> a</c><00:00:47.879><c> data</c><00:00:48.160><c> set</c>

00:00:48.990 --> 00:00:49.000 align:start position:0%
models this case this is a data set
 

00:00:49.000 --> 00:00:50.990 align:start position:0%
models this case this is a data set
let's<00:00:49.199><c> take</c><00:00:49.320><c> a</c><00:00:49.440><c> look</c><00:00:49.559><c> at</c><00:00:49.640><c> the</c><00:00:49.840><c> files</c><00:00:50.280><c> here</c><00:00:50.840><c> and</c>

00:00:50.990 --> 00:00:51.000 align:start position:0%
let's take a look at the files here and
 

00:00:51.000 --> 00:00:54.150 align:start position:0%
let's take a look at the files here and
we<00:00:51.120><c> can</c><00:00:51.280><c> see</c><00:00:51.719><c> there</c><00:00:51.840><c> are</c><00:00:52.039><c> two</c><00:00:52.239><c> Json</c><00:00:53.239><c> Json</c><00:00:53.680><c> files</c>

00:00:54.150 --> 00:00:54.160 align:start position:0%
we can see there are two Json Json files
 

00:00:54.160 --> 00:00:57.069 align:start position:0%
we can see there are two Json Json files
for<00:00:54.800><c> the</c><00:00:55.120><c> eval</c><00:00:55.559><c> and</c><00:00:55.680><c> the</c><00:00:55.800><c> training</c><00:00:56.160><c> data</c><00:00:56.960><c> but</c>

00:00:57.069 --> 00:00:57.079 align:start position:0%
for the eval and the training data but
 

00:00:57.079 --> 00:00:59.270 align:start position:0%
for the eval and the training data but
it's<00:00:57.559><c> difficult</c><00:00:57.800><c> to</c><00:00:58.320><c> to</c><00:00:58.559><c> visualize</c><00:00:59.039><c> and</c><00:00:59.160><c> to</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
it's difficult to to visualize and to
 

00:00:59.280 --> 00:01:02.990 align:start position:0%
it's difficult to to visualize and to
navigate<00:00:59.640><c> J</c><00:01:00.160><c> to</c><00:01:00.399><c> explore</c><00:01:00.760><c> the</c><00:01:00.920><c> data</c><00:01:01.640><c> so</c><00:01:02.640><c> I</c><00:01:02.760><c> will</c>

00:01:02.990 --> 00:01:03.000 align:start position:0%
navigate J to explore the data so I will
 

00:01:03.000 --> 00:01:04.549 align:start position:0%
navigate J to explore the data so I will
take<00:01:03.160><c> a</c><00:01:03.280><c> look</c><00:01:03.440><c> at</c><00:01:03.600><c> the</c><00:01:03.680><c> lineage</c><00:01:04.360><c> you've</c>

00:01:04.549 --> 00:01:04.559 align:start position:0%
take a look at the lineage you've
 

00:01:04.559 --> 00:01:06.550 align:start position:0%
take a look at the lineage you've
already<00:01:04.799><c> seen</c><00:01:05.040><c> the</c><00:01:05.119><c> lineage</c><00:01:05.519><c> View</c><00:01:06.320><c> and</c><00:01:06.439><c> you</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
already seen the lineage View and you
 

00:01:06.560 --> 00:01:09.149 align:start position:0%
already seen the lineage View and you
can<00:01:06.760><c> see</c><00:01:07.320><c> our</c><00:01:07.600><c> data</c><00:01:07.880><c> set</c><00:01:08.400><c> and</c><00:01:08.640><c> specifically</c>

00:01:09.149 --> 00:01:09.159 align:start position:0%
can see our data set and specifically
 

00:01:09.159 --> 00:01:10.749 align:start position:0%
can see our data set and specifically
this<00:01:09.360><c> version</c><00:01:09.759><c> version</c><00:01:10.000><c> four</c><00:01:10.240><c> of</c><00:01:10.360><c> the</c><00:01:10.479><c> data</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
this version version four of the data
 

00:01:10.759 --> 00:01:12.350 align:start position:0%
this version version four of the data
set<00:01:10.960><c> it</c><00:01:11.080><c> was</c><00:01:11.200><c> used</c><00:01:11.400><c> in</c><00:01:11.520><c> a</c><00:01:11.640><c> bunch</c><00:01:11.799><c> of</c><00:01:11.920><c> training</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
set it was used in a bunch of training
 

00:01:12.360 --> 00:01:14.590 align:start position:0%
set it was used in a bunch of training
runs<00:01:13.360><c> but</c><00:01:13.479><c> I</c><00:01:13.560><c> want</c><00:01:13.680><c> to</c><00:01:13.840><c> look</c><00:01:13.960><c> at</c><00:01:14.080><c> the</c><00:01:14.200><c> run</c><00:01:14.479><c> that</c>

00:01:14.590 --> 00:01:14.600 align:start position:0%
runs but I want to look at the run that
 

00:01:14.600 --> 00:01:16.910 align:start position:0%
runs but I want to look at the run that
was<00:01:14.720><c> used</c><00:01:14.920><c> to</c><00:01:15.080><c> produce</c><00:01:15.400><c> this</c><00:01:15.560><c> data</c><00:01:15.799><c> set</c><00:01:16.600><c> and</c><00:01:16.759><c> my</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
was used to produce this data set and my
 

00:01:16.920 --> 00:01:18.630 align:start position:0%
was used to produce this data set and my
hope<00:01:17.119><c> is</c><00:01:17.280><c> that</c><00:01:17.560><c> the</c><00:01:17.799><c> the</c><00:01:17.920><c> person</c><00:01:18.200><c> that</c><00:01:18.320><c> loged</c>

00:01:18.630 --> 00:01:18.640 align:start position:0%
hope is that the the person that loged
 

00:01:18.640 --> 00:01:20.789 align:start position:0%
hope is that the the person that loged
this<00:01:18.799><c> data</c><00:01:19.280><c> can</c><00:01:19.479><c> take</c><00:01:19.600><c> a</c><00:01:19.720><c> look</c><00:01:19.960><c> who</c><00:01:20.479><c> Okay</c><00:01:20.600><c> so</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
this data can take a look who Okay so
 

00:01:20.799 --> 00:01:22.830 align:start position:0%
this data can take a look who Okay so
this<00:01:20.920><c> was</c><00:01:21.079><c> Thomas</c><00:01:21.880><c> Thomas</c><00:01:22.159><c> loged</c><00:01:22.439><c> this</c><00:01:22.560><c> data</c>

00:01:22.830 --> 00:01:22.840 align:start position:0%
this was Thomas Thomas loged this data
 

00:01:22.840 --> 00:01:24.590 align:start position:0%
this was Thomas Thomas loged this data
set<00:01:23.439><c> uh</c><00:01:23.520><c> let's</c><00:01:23.680><c> take</c><00:01:23.799><c> a</c><00:01:23.880><c> look</c><00:01:24.079><c> if</c><00:01:24.280><c> he</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
set uh let's take a look if he
 

00:01:24.600 --> 00:01:28.030 align:start position:0%
set uh let's take a look if he
visualized<00:01:25.200><c> the</c><00:01:25.360><c> data</c><00:01:26.159><c> as</c><00:01:26.320><c> he</c><00:01:26.439><c> was</c><00:01:26.560><c> logging</c>

00:01:28.030 --> 00:01:28.040 align:start position:0%
visualized the data as he was logging
 

00:01:28.040 --> 00:01:31.069 align:start position:0%
visualized the data as he was logging
at<00:01:29.040><c> and</c><00:01:29.720><c> as</c><00:01:29.920><c> as</c><00:01:30.079><c> expected</c><00:01:30.520><c> you</c><00:01:30.600><c> can</c><00:01:30.720><c> see</c><00:01:30.880><c> a</c>

00:01:31.069 --> 00:01:31.079 align:start position:0%
at and as as expected you can see a
 

00:01:31.079 --> 00:01:33.270 align:start position:0%
at and as as expected you can see a
table<00:01:31.479><c> a</c><00:01:31.600><c> weit</c><00:01:31.799><c> syis</c><00:01:32.320><c> table</c><00:01:32.759><c> with</c><00:01:32.880><c> a</c><00:01:33.000><c> trade</c>

00:01:33.270 --> 00:01:33.280 align:start position:0%
table a weit syis table with a trade
 

00:01:33.280 --> 00:01:36.789 align:start position:0%
table a weit syis table with a trade
data<00:01:33.759><c> set</c><00:01:34.759><c> and</c><00:01:35.240><c> and</c><00:01:35.399><c> we</c><00:01:35.520><c> can</c><00:01:35.840><c> use</c><00:01:36.079><c> this</c><00:01:36.240><c> table</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
data set and and we can use this table
 

00:01:36.799 --> 00:01:39.870 align:start position:0%
data set and and we can use this table
to<00:01:37.799><c> inspect</c><00:01:38.200><c> this</c><00:01:38.399><c> data</c><00:01:39.240><c> a</c><00:01:39.360><c> little</c><00:01:39.560><c> bit</c><00:01:39.720><c> and</c>

00:01:39.870 --> 00:01:39.880 align:start position:0%
to inspect this data a little bit and
 

00:01:39.880 --> 00:01:41.670 align:start position:0%
to inspect this data a little bit and
get<00:01:40.000><c> a</c><00:01:40.079><c> feel</c><00:01:40.320><c> for</c><00:01:40.560><c> what</c><00:01:40.680><c> we</c><00:01:40.799><c> are</c><00:01:41.399><c> actually</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
get a feel for what we are actually
 

00:01:41.680 --> 00:01:43.749 align:start position:0%
get a feel for what we are actually
using<00:01:42.200><c> and</c><00:01:42.360><c> you</c><00:01:42.479><c> can</c><00:01:42.560><c> see</c><00:01:42.840><c> there</c><00:01:42.960><c> are</c><00:01:43.520><c> three</c>

00:01:43.749 --> 00:01:43.759 align:start position:0%
using and you can see there are three
 

00:01:43.759 --> 00:01:45.670 align:start position:0%
using and you can see there are three
columns<00:01:44.159><c> in</c><00:01:44.280><c> this</c><00:01:44.479><c> data</c><00:01:44.719><c> set</c><00:01:44.960><c> there</c><00:01:45.079><c> is</c><00:01:45.200><c> an</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
columns in this data set there is an
 

00:01:45.680 --> 00:01:48.550 align:start position:0%
columns in this data set there is an
instruction<00:01:46.680><c> there</c><00:01:46.840><c> is</c><00:01:46.960><c> an</c><00:01:47.280><c> input</c><00:01:47.960><c> column</c>

00:01:48.550 --> 00:01:48.560 align:start position:0%
instruction there is an input column
 

00:01:48.560 --> 00:01:50.510 align:start position:0%
instruction there is an input column
which<00:01:48.880><c> sometimes</c><00:01:49.159><c> it's</c><00:01:49.439><c> empty</c><00:01:50.200><c> sometimes</c><00:01:50.399><c> it</c>

00:01:50.510 --> 00:01:50.520 align:start position:0%
which sometimes it's empty sometimes it
 

00:01:50.520 --> 00:01:52.950 align:start position:0%
which sometimes it's empty sometimes it
contains<00:01:50.880><c> some</c><00:01:51.079><c> text</c><00:01:52.079><c> and</c><00:01:52.240><c> that</c><00:01:52.360><c> is</c><00:01:52.520><c> the</c>

00:01:52.950 --> 00:01:52.960 align:start position:0%
contains some text and that is the
 

00:01:52.960 --> 00:01:55.630 align:start position:0%
contains some text and that is the
output<00:01:53.960><c> so</c><00:01:54.079><c> if</c><00:01:54.159><c> we</c><00:01:54.360><c> look</c><00:01:54.560><c> at</c><00:01:54.960><c> uh</c><00:01:55.200><c> at</c><00:01:55.360><c> one</c><00:01:55.479><c> of</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
output so if we look at uh at one of
 

00:01:55.640 --> 00:01:57.389 align:start position:0%
output so if we look at uh at one of
these<00:01:55.799><c> instructions</c><00:01:56.360><c> develop</c><00:01:56.719><c> a</c><00:01:56.880><c> script</c><00:01:57.240><c> that</c>

00:01:57.389 --> 00:01:57.399 align:start position:0%
these instructions develop a script that
 

00:01:57.399 --> 00:01:59.350 align:start position:0%
these instructions develop a script that
prints<00:01:57.640><c> out</c><00:01:57.799><c> the</c><00:01:57.920><c> Fibonacci</c><00:01:58.439><c> sequence</c><00:01:59.240><c> there</c>

00:01:59.350 --> 00:01:59.360 align:start position:0%
prints out the Fibonacci sequence there
 

00:01:59.360 --> 00:02:02.590 align:start position:0%
prints out the Fibonacci sequence there
is<00:01:59.479><c> no</c><00:01:59.600><c> input</c><00:02:00.159><c> here</c><00:02:00.520><c> and</c><00:02:00.640><c> then</c><00:02:00.799><c> the</c><00:02:00.960><c> output</c><00:02:01.600><c> is</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
is no input here and then the output is
 

00:02:02.600 --> 00:02:04.429 align:start position:0%
is no input here and then the output is
let's<00:02:02.840><c> take</c><00:02:02.960><c> a</c><00:02:03.159><c> look</c><00:02:03.360><c> at</c><00:02:03.840><c> uh</c><00:02:04.000><c> let's</c><00:02:04.200><c> take</c><00:02:04.320><c> a</c>

00:02:04.429 --> 00:02:04.439 align:start position:0%
let's take a look at uh let's take a
 

00:02:04.439 --> 00:02:06.270 align:start position:0%
let's take a look at uh let's take a
look<00:02:04.600><c> at</c><00:02:04.799><c> this</c><00:02:05.159><c> the</c><00:02:05.280><c> output</c><00:02:05.600><c> is</c><00:02:05.799><c> actually</c><00:02:06.119><c> the</c>

00:02:06.270 --> 00:02:06.280 align:start position:0%
look at this the output is actually the
 

00:02:06.280 --> 00:02:08.469 align:start position:0%
look at this the output is actually the
python<00:02:06.640><c> script</c><00:02:07.520><c> that</c><00:02:07.680><c> prints</c><00:02:08.000><c> out</c><00:02:08.280><c> a</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
python script that prints out a
 

00:02:08.479 --> 00:02:10.150 align:start position:0%
python script that prints out a
Fibonacci<00:02:09.039><c> Sequence</c><00:02:09.479><c> you</c><00:02:09.560><c> can</c><00:02:09.720><c> look</c><00:02:09.840><c> at</c><00:02:10.000><c> this</c>

00:02:10.150 --> 00:02:10.160 align:start position:0%
Fibonacci Sequence you can look at this
 

00:02:10.160 --> 00:02:12.190 align:start position:0%
Fibonacci Sequence you can look at this
in<00:02:10.319><c> markdown</c><00:02:10.879><c> hopefully</c><00:02:11.200><c> it</c><00:02:11.280><c> will</c><00:02:11.440><c> render</c>

00:02:12.190 --> 00:02:12.200 align:start position:0%
in markdown hopefully it will render
 

00:02:12.200 --> 00:02:16.190 align:start position:0%
in markdown hopefully it will render
better<00:02:13.200><c> yeah</c><00:02:13.560><c> so</c><00:02:14.280><c> it</c><00:02:14.400><c> seems</c><00:02:14.840><c> like</c><00:02:15.239><c> I</c><00:02:15.319><c> will</c><00:02:15.519><c> not</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
better yeah so it seems like I will not
 

00:02:16.200 --> 00:02:18.150 align:start position:0%
better yeah so it seems like I will not
analyze<00:02:16.680><c> this</c><00:02:16.879><c> I</c><00:02:17.080><c> I</c><00:02:17.160><c> would</c><00:02:17.360><c> expect</c><00:02:17.680><c> this</c><00:02:17.800><c> to</c><00:02:17.959><c> be</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
analyze this I I would expect this to be
 

00:02:18.160 --> 00:02:20.390 align:start position:0%
analyze this I I would expect this to be
to<00:02:18.280><c> be</c><00:02:18.519><c> correct</c><00:02:19.519><c> uh</c><00:02:19.599><c> let's</c><00:02:19.760><c> take</c><00:02:19.879><c> a</c><00:02:20.000><c> look</c><00:02:20.160><c> maybe</c>

00:02:20.390 --> 00:02:20.400 align:start position:0%
to be correct uh let's take a look maybe
 

00:02:20.400 --> 00:02:23.190 align:start position:0%
to be correct uh let's take a look maybe
at<00:02:20.560><c> one</c><00:02:20.680><c> of</c><00:02:21.040><c> the</c><00:02:22.040><c> examples</c><00:02:22.480><c> that</c><00:02:22.599><c> contains</c><00:02:22.959><c> an</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
at one of the examples that contains an
 

00:02:23.200 --> 00:02:26.229 align:start position:0%
at one of the examples that contains an
input<00:02:24.120><c> so</c><00:02:25.120><c> the</c><00:02:25.360><c> instruction</c><00:02:25.920><c> here</c><00:02:26.040><c> is</c>

00:02:26.229 --> 00:02:26.239 align:start position:0%
input so the instruction here is
 

00:02:26.239 --> 00:02:27.949 align:start position:0%
input so the instruction here is
categorize<00:02:26.760><c> the</c><00:02:26.840><c> following</c><00:02:27.200><c> post</c><00:02:27.519><c> as</c><00:02:27.720><c> either</c>

00:02:27.949 --> 00:02:27.959 align:start position:0%
categorize the following post as either
 

00:02:27.959 --> 00:02:31.630 align:start position:0%
categorize the following post as either
as<00:02:28.200><c> a</c><00:02:28.400><c> personal</c><00:02:28.840><c> or</c><00:02:29.080><c> professional</c><00:02:29.519><c> post</c>

00:02:31.630 --> 00:02:31.640 align:start position:0%
as a personal or professional post
 

00:02:31.640 --> 00:02:33.710 align:start position:0%
as a personal or professional post
and<00:02:31.920><c> the</c><00:02:32.080><c> input</c><00:02:32.440><c> is</c><00:02:32.920><c> just</c><00:02:33.200><c> had</c><00:02:33.400><c> the</c><00:02:33.560><c> best</c>

00:02:33.710 --> 00:02:33.720 align:start position:0%
and the input is just had the best
 

00:02:33.720 --> 00:02:37.430 align:start position:0%
and the input is just had the best
dinner<00:02:34.080><c> with</c><00:02:34.239><c> my</c><00:02:34.360><c> team</c><00:02:34.560><c> from</c><00:02:34.920><c> work</c><00:02:35.920><c> and</c><00:02:36.319><c> um</c><00:02:37.160><c> the</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
dinner with my team from work and um the
 

00:02:37.440 --> 00:02:39.750 align:start position:0%
dinner with my team from work and um the
output<00:02:37.959><c> is</c><00:02:38.280><c> it</c><00:02:38.400><c> is</c><00:02:38.519><c> a</c><00:02:38.760><c> professional</c><00:02:39.239><c> post</c><00:02:39.640><c> as</c>

00:02:39.750 --> 00:02:39.760 align:start position:0%
output is it is a professional post as
 

00:02:39.760 --> 00:02:41.509 align:start position:0%
output is it is a professional post as
it<00:02:39.959><c> references</c><00:02:40.480><c> a</c><00:02:40.599><c> work</c>

00:02:41.509 --> 00:02:41.519 align:start position:0%
it references a work
 

00:02:41.519 --> 00:02:44.070 align:start position:0%
it references a work
team<00:02:42.519><c> not</c><00:02:42.640><c> sure</c><00:02:42.800><c> if</c><00:02:42.879><c> I</c><00:02:43.040><c> agree</c><00:02:43.280><c> with</c><00:02:43.440><c> this</c><00:02:43.800><c> but</c>

00:02:44.070 --> 00:02:44.080 align:start position:0%
team not sure if I agree with this but
 

00:02:44.080 --> 00:02:45.509 align:start position:0%
team not sure if I agree with this but
this<00:02:44.200><c> is</c><00:02:44.319><c> the</c><00:02:44.480><c> data</c><00:02:44.760><c> set</c><00:02:44.920><c> that</c><00:02:45.040><c> we'll</c><00:02:45.280><c> work</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
this is the data set that we'll work
 

00:02:45.519 --> 00:02:48.030 align:start position:0%
this is the data set that we'll work
with<00:02:46.400><c> and</c><00:02:46.760><c> uh</c><00:02:46.840><c> it's</c><00:02:47.040><c> good</c><00:02:47.159><c> to</c><00:02:47.360><c> have</c><00:02:47.480><c> a</c><00:02:47.800><c> have</c><00:02:47.920><c> a</c>

00:02:48.030 --> 00:02:48.040 align:start position:0%
with and uh it's good to have a have a
 

00:02:48.040 --> 00:02:49.910 align:start position:0%
with and uh it's good to have a have a
good<00:02:48.200><c> feeling</c><00:02:48.519><c> for</c><00:02:48.800><c> what</c><00:02:49.280><c> this</c><00:02:49.440><c> data</c><00:02:49.720><c> set</c>

00:02:49.910 --> 00:02:49.920 align:start position:0%
good feeling for what this data set
 

00:02:49.920 --> 00:02:52.190 align:start position:0%
good feeling for what this data set
contains<00:02:50.319><c> we</c><00:02:50.440><c> can</c><00:02:50.560><c> take</c><00:02:50.680><c> a</c><00:02:50.840><c> look</c><00:02:51.000><c> at</c><00:02:51.480><c> eval</c><00:02:52.040><c> and</c>

00:02:52.190 --> 00:02:52.200 align:start position:0%
contains we can take a look at eval and
 

00:02:52.200 --> 00:02:54.470 align:start position:0%
contains we can take a look at eval and
it's<00:02:52.680><c> structured</c><00:02:53.120><c> in</c><00:02:53.200><c> a</c><00:02:53.319><c> similar</c><00:02:53.680><c> way</c><00:02:54.319><c> there</c>

00:02:54.470 --> 00:02:54.480 align:start position:0%
it's structured in a similar way there
 

00:02:54.480 --> 00:02:56.270 align:start position:0%
it's structured in a similar way there
is<00:02:54.599><c> for</c><00:02:54.800><c> example</c><00:02:55.120><c> translate</c><00:02:55.560><c> this</c><00:02:55.680><c> text</c><00:02:55.920><c> into</c>

00:02:56.270 --> 00:02:56.280 align:start position:0%
is for example translate this text into
 

00:02:56.280 --> 00:02:59.070 align:start position:0%
is for example translate this text into
Spanish<00:02:57.280><c> the</c><00:02:57.480><c> text</c><00:02:57.959><c> the</c><00:02:58.080><c> input</c><00:02:58.440><c> is</c><00:02:58.800><c> we</c><00:02:58.920><c> are</c>

00:02:59.070 --> 00:02:59.080 align:start position:0%
Spanish the text the input is we are
 

00:02:59.080 --> 00:03:00.390 align:start position:0%
Spanish the text the input is we are
excited<00:02:59.400><c> to</c><00:02:59.519><c> work</c><00:02:59.680><c> with</c><00:02:59.840><c> with</c><00:02:59.959><c> you</c><00:03:00.040><c> on</c><00:03:00.159><c> this</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
excited to work with with you on this
 

00:03:00.400 --> 00:03:03.309 align:start position:0%
excited to work with with you on this
project<00:03:01.239><c> and</c><00:03:01.360><c> the</c><00:03:01.480><c> output</c><00:03:01.920><c> is</c><00:03:02.720><c> uh</c><00:03:02.840><c> the</c><00:03:02.959><c> Spanish</c>

00:03:03.309 --> 00:03:03.319 align:start position:0%
project and the output is uh the Spanish
 

00:03:03.319 --> 00:03:05.229 align:start position:0%
project and the output is uh the Spanish
translation<00:03:03.840><c> of</c><00:03:04.040><c> this</c>

00:03:05.229 --> 00:03:05.239 align:start position:0%
translation of this
 

00:03:05.239 --> 00:03:08.509 align:start position:0%
translation of this
text<00:03:06.239><c> all</c><00:03:06.400><c> right</c><00:03:06.840><c> so</c><00:03:07.200><c> let's</c><00:03:07.400><c> go</c><00:03:07.560><c> back</c><00:03:07.799><c> to</c><00:03:08.000><c> the</c>

00:03:08.509 --> 00:03:08.519 align:start position:0%
text all right so let's go back to the
 

00:03:08.519 --> 00:03:11.149 align:start position:0%
text all right so let's go back to the
to<00:03:08.640><c> the</c><00:03:08.760><c> training</c><00:03:09.120><c> script</c><00:03:10.120><c> and</c><00:03:10.480><c> uh</c><00:03:10.640><c> I</c><00:03:10.760><c> want</c><00:03:10.920><c> to</c>

00:03:11.149 --> 00:03:11.159 align:start position:0%
to the training script and uh I want to
 

00:03:11.159 --> 00:03:13.710 align:start position:0%
to the training script and uh I want to
also<00:03:11.480><c> give</c><00:03:11.640><c> you</c><00:03:12.280><c> a</c><00:03:12.480><c> hint</c><00:03:12.760><c> on</c><00:03:13.000><c> how</c><00:03:13.239><c> this</c><00:03:13.400><c> data</c><00:03:13.640><c> is</c>

00:03:13.710 --> 00:03:13.720 align:start position:0%
also give you a hint on how this data is
 

00:03:13.720 --> 00:03:15.589 align:start position:0%
also give you a hint on how this data is
being<00:03:13.920><c> transformed</c><00:03:14.480><c> as</c><00:03:14.599><c> an</c><00:03:14.799><c> input</c><00:03:15.200><c> to</c><00:03:15.360><c> the</c>

00:03:15.589 --> 00:03:15.599 align:start position:0%
being transformed as an input to the
 

00:03:15.599 --> 00:03:19.630 align:start position:0%
being transformed as an input to the
model<00:03:16.599><c> and</c><00:03:16.879><c> this</c><00:03:17.000><c> is</c><00:03:17.319><c> the</c><00:03:17.760><c> the</c><00:03:17.920><c> data</c><00:03:18.280><c> dop</c><00:03:19.200><c> file</c>

00:03:19.630 --> 00:03:19.640 align:start position:0%
model and this is the the data dop file
 

00:03:19.640 --> 00:03:21.070 align:start position:0%
model and this is the the data dop file
that<00:03:19.760><c> is</c><00:03:19.959><c> in</c><00:03:20.080><c> the</c><00:03:20.200><c> mini</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
that is in the mini
 

00:03:21.080 --> 00:03:23.550 align:start position:0%
that is in the mini
llm<00:03:22.080><c> uh</c><00:03:22.239><c> Library</c><00:03:22.720><c> within</c><00:03:23.040><c> the</c><00:03:23.200><c> the</c><00:03:23.319><c> course</c>

00:03:23.550 --> 00:03:23.560 align:start position:0%
llm uh Library within the the course
 

00:03:23.560 --> 00:03:25.390 align:start position:0%
llm uh Library within the the course
repo<00:03:24.400><c> and</c><00:03:24.519><c> you</c><00:03:24.640><c> can</c><00:03:24.760><c> see</c><00:03:25.000><c> that</c><00:03:25.120><c> we</c><00:03:25.239><c> are</c>

00:03:25.390 --> 00:03:25.400 align:start position:0%
repo and you can see that we are
 

00:03:25.400 --> 00:03:27.550 align:start position:0%
repo and you can see that we are
transforming<00:03:26.280><c> the</c><00:03:26.440><c> inputs</c><00:03:26.879><c> the</c><00:03:27.000><c> rows</c>

00:03:27.550 --> 00:03:27.560 align:start position:0%
transforming the inputs the rows
 

00:03:27.560 --> 00:03:29.429 align:start position:0%
transforming the inputs the rows
depending<00:03:28.000><c> if</c><00:03:28.159><c> there</c><00:03:28.280><c> is</c><00:03:28.720><c> an</c><00:03:28.920><c> in</c><00:03:29.200><c> if</c><00:03:29.280><c> there's</c>

00:03:29.429 --> 00:03:29.439 align:start position:0%
depending if there is an in if there's
 

00:03:29.439 --> 00:03:32.830 align:start position:0%
depending if there is an in if there's
no<00:03:29.560><c> in</c><00:03:29.959><c> put</c><00:03:30.680><c> then</c><00:03:31.040><c> we're</c><00:03:31.480><c> putting</c><00:03:31.799><c> this</c><00:03:32.000><c> prefix</c>

00:03:32.830 --> 00:03:32.840 align:start position:0%
no in put then we're putting this prefix
 

00:03:32.840 --> 00:03:34.110 align:start position:0%
no in put then we're putting this prefix
uh<00:03:32.959><c> below</c><00:03:33.239><c> is</c><00:03:33.360><c> an</c><00:03:33.519><c> instruction</c><00:03:33.959><c> that</c>

00:03:34.110 --> 00:03:34.120 align:start position:0%
uh below is an instruction that
 

00:03:34.120 --> 00:03:36.350 align:start position:0%
uh below is an instruction that
describes<00:03:34.480><c> the</c><00:03:34.640><c> task</c><00:03:35.360><c> wrer</c><00:03:35.760><c> response</c><00:03:36.200><c> that</c>

00:03:36.350 --> 00:03:36.360 align:start position:0%
describes the task wrer response that
 

00:03:36.360 --> 00:03:38.190 align:start position:0%
describes the task wrer response that
appropriately<00:03:36.959><c> completes</c><00:03:37.360><c> the</c><00:03:37.480><c> request</c><00:03:38.040><c> and</c>

00:03:38.190 --> 00:03:38.200 align:start position:0%
appropriately completes the request and
 

00:03:38.200 --> 00:03:40.429 align:start position:0%
appropriately completes the request and
we're<00:03:38.319><c> feeding</c><00:03:38.680><c> both</c><00:03:38.879><c> the</c><00:03:39.319><c> instruction</c><00:03:40.319><c> we're</c>

00:03:40.429 --> 00:03:40.439 align:start position:0%
we're feeding both the instruction we're
 

00:03:40.439 --> 00:03:43.309 align:start position:0%
we're feeding both the instruction we're
feeding<00:03:40.720><c> actually</c><00:03:41.040><c> the</c><00:03:41.159><c> instruction</c><00:03:41.920><c> here</c><00:03:42.920><c> um</c>

00:03:43.309 --> 00:03:43.319 align:start position:0%
feeding actually the instruction here um
 

00:03:43.319 --> 00:03:44.910 align:start position:0%
feeding actually the instruction here um
if<00:03:43.519><c> there</c><00:03:43.640><c> is</c><00:03:43.760><c> an</c><00:03:44.000><c> input</c><00:03:44.599><c> then</c><00:03:44.720><c> we're</c>

00:03:44.910 --> 00:03:44.920 align:start position:0%
if there is an input then we're
 

00:03:44.920 --> 00:03:46.630 align:start position:0%
if there is an input then we're
formatting<00:03:45.400><c> this</c><00:03:45.519><c> a</c><00:03:45.640><c> bit</c><00:03:45.959><c> differently</c><00:03:46.400><c> we're</c>

00:03:46.630 --> 00:03:46.640 align:start position:0%
formatting this a bit differently we're
 

00:03:46.640 --> 00:03:49.750 align:start position:0%
formatting this a bit differently we're
also<00:03:46.879><c> adding</c><00:03:47.200><c> this</c><00:03:47.400><c> input</c><00:03:48.319><c> into</c><00:03:48.640><c> the</c><00:03:48.879><c> into</c><00:03:49.120><c> the</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
also adding this input into the into the
 

00:03:49.760 --> 00:03:53.429 align:start position:0%
also adding this input into the into the
prompt<00:03:50.760><c> and</c><00:03:50.959><c> then</c><00:03:51.879><c> uh</c><00:03:52.040><c> in</c><00:03:52.319><c> the</c><00:03:53.000><c> uh</c><00:03:53.200><c> in</c><00:03:53.280><c> the</c>

00:03:53.429 --> 00:03:53.439 align:start position:0%
prompt and then uh in the uh in the
 

00:03:53.439 --> 00:03:56.270 align:start position:0%
prompt and then uh in the uh in the
final<00:03:53.760><c> step</c><00:03:54.040><c> we're</c><00:03:54.319><c> combining</c><00:03:55.200><c> both</c><00:03:55.599><c> the</c><00:03:56.040><c> the</c>

00:03:56.270 --> 00:03:56.280 align:start position:0%
final step we're combining both the the
 

00:03:56.280 --> 00:03:59.030 align:start position:0%
final step we're combining both the the
instruction<00:03:57.280><c> and</c><00:03:57.519><c> the</c><00:03:57.680><c> output</c><00:03:58.360><c> which</c><00:03:58.480><c> is</c><00:03:58.680><c> the</c>

00:03:59.030 --> 00:03:59.040 align:start position:0%
instruction and the output which is the
 

00:03:59.040 --> 00:04:00.229 align:start position:0%
instruction and the output which is the
the<00:03:59.239><c> the</c><00:03:59.400><c> the</c><00:03:59.599><c> the</c>

00:04:00.229 --> 00:04:00.239 align:start position:0%
the the the the
 

00:04:00.239 --> 00:04:02.789 align:start position:0%
the the the the
the<00:04:00.400><c> completion</c><00:04:00.920><c> from</c><00:04:01.079><c> the</c><00:04:01.200><c> llm</c><00:04:02.200><c> and</c><00:04:02.400><c> that's</c>

00:04:02.789 --> 00:04:02.799 align:start position:0%
the completion from the llm and that's
 

00:04:02.799 --> 00:04:04.670 align:start position:0%
the completion from the llm and that's
something<00:04:03.120><c> that</c><00:04:03.239><c> we</c><00:04:03.360><c> will</c><00:04:03.560><c> use</c><00:04:03.760><c> to</c><00:04:04.159><c> to</c><00:04:04.360><c> train</c>

00:04:04.670 --> 00:04:04.680 align:start position:0%
something that we will use to to train
 

00:04:04.680 --> 00:04:06.589 align:start position:0%
something that we will use to to train
our

00:04:06.589 --> 00:04:06.599 align:start position:0%
our
 

00:04:06.599 --> 00:04:09.869 align:start position:0%
our
llm<00:04:07.599><c> um</c><00:04:08.120><c> this</c><00:04:08.280><c> script</c><00:04:08.959><c> um</c><00:04:09.280><c> starts</c><00:04:09.640><c> with</c><00:04:09.760><c> a</c>

00:04:09.869 --> 00:04:09.879 align:start position:0%
llm um this script um starts with a
 

00:04:09.879 --> 00:04:12.149 align:start position:0%
llm um this script um starts with a
config<00:04:10.480><c> that</c><00:04:10.599><c> we</c><00:04:10.760><c> can</c><00:04:11.120><c> we</c><00:04:11.200><c> can</c><00:04:11.360><c> modify</c><00:04:11.840><c> as</c><00:04:11.959><c> we</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
config that we can we can modify as we
 

00:04:12.159 --> 00:04:14.229 align:start position:0%
config that we can we can modify as we
as<00:04:12.239><c> we</c><00:04:12.360><c> launch</c><00:04:12.640><c> the</c><00:04:12.760><c> script</c><00:04:13.519><c> we</c><00:04:13.640><c> can</c><00:04:13.920><c> adjust</c>

00:04:14.229 --> 00:04:14.239 align:start position:0%
as we launch the script we can adjust
 

00:04:14.239 --> 00:04:16.990 align:start position:0%
as we launch the script we can adjust
the<00:04:14.319><c> learning</c><00:04:14.720><c> rate</c><00:04:15.400><c> we</c><00:04:15.519><c> can</c><00:04:16.040><c> adjust</c><00:04:16.680><c> we</c><00:04:16.799><c> can</c>

00:04:16.990 --> 00:04:17.000 align:start position:0%
the learning rate we can adjust we can
 

00:04:17.000 --> 00:04:18.590 align:start position:0%
the learning rate we can adjust we can
we<00:04:17.079><c> can</c><00:04:17.199><c> train</c><00:04:17.440><c> this</c><00:04:17.600><c> model</c><00:04:17.880><c> by</c><00:04:18.000><c> freezing</c><00:04:18.400><c> some</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
we can train this model by freezing some
 

00:04:18.600 --> 00:04:21.670 align:start position:0%
we can train this model by freezing some
layers<00:04:19.440><c> in</c><00:04:19.560><c> order</c><00:04:19.840><c> to</c><00:04:19.959><c> fit</c><00:04:20.160><c> it</c><00:04:20.280><c> on</c><00:04:20.479><c> single</c><00:04:20.759><c> GPU</c>

00:04:21.670 --> 00:04:21.680 align:start position:0%
layers in order to fit it on single GPU
 

00:04:21.680 --> 00:04:23.390 align:start position:0%
layers in order to fit it on single GPU
we<00:04:21.799><c> can</c><00:04:21.959><c> potentially</c><00:04:22.360><c> use</c><00:04:22.560><c> Laura</c><00:04:23.120><c> as</c><00:04:23.199><c> an</c>

00:04:23.390 --> 00:04:23.400 align:start position:0%
we can potentially use Laura as an
 

00:04:23.400 --> 00:04:25.030 align:start position:0%
we can potentially use Laura as an
alternative<00:04:24.080><c> way</c><00:04:24.199><c> of</c><00:04:24.320><c> fitting</c><00:04:24.680><c> this</c><00:04:24.919><c> this</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
alternative way of fitting this this
 

00:04:25.040 --> 00:04:28.430 align:start position:0%
alternative way of fitting this this
model<00:04:25.320><c> on</c><00:04:25.440><c> a</c><00:04:25.560><c> single</c><00:04:26.320><c> GPU</c><00:04:27.320><c> we</c><00:04:27.440><c> can</c><00:04:27.919><c> uh</c><00:04:28.199><c> maybe</c>

00:04:28.430 --> 00:04:28.440 align:start position:0%
model on a single GPU we can uh maybe
 

00:04:28.440 --> 00:04:29.870 align:start position:0%
model on a single GPU we can uh maybe
like<00:04:28.600><c> limit</c><00:04:28.840><c> the</c><00:04:28.960><c> number</c><00:04:29.160><c> of</c><00:04:29.280><c> steps</c><00:04:29.520><c> if</c><00:04:29.759><c> we</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
like limit the number of steps if we
 

00:04:29.880 --> 00:04:32.510 align:start position:0%
like limit the number of steps if we
just<00:04:30.000><c> want</c><00:04:30.120><c> to</c><00:04:30.240><c> debug</c><00:04:30.639><c> it</c><00:04:31.560><c> um</c><00:04:31.919><c> we</c><00:04:32.039><c> can</c><00:04:32.199><c> adjust</c>

00:04:32.510 --> 00:04:32.520 align:start position:0%
just want to debug it um we can adjust
 

00:04:32.520 --> 00:04:34.110 align:start position:0%
just want to debug it um we can adjust
the<00:04:32.639><c> number</c><00:04:32.840><c> of</c><00:04:33.039><c> evaluation</c>

00:04:34.110 --> 00:04:34.120 align:start position:0%
the number of evaluation
 

00:04:34.120 --> 00:04:36.629 align:start position:0%
the number of evaluation
samples<00:04:35.120><c> this</c><00:04:35.440><c> uh</c><00:04:35.560><c> script</c><00:04:35.880><c> is</c><00:04:36.000><c> using</c><00:04:36.280><c> hugging</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
samples this uh script is using hugging
 

00:04:36.639 --> 00:04:38.790 align:start position:0%
samples this uh script is using hugging
facee<00:04:36.960><c> trainer</c><00:04:37.960><c> and</c><00:04:38.160><c> because</c><00:04:38.360><c> it's</c><00:04:38.520><c> using</c>

00:04:38.790 --> 00:04:38.800 align:start position:0%
facee trainer and because it's using
 

00:04:38.800 --> 00:04:40.629 align:start position:0%
facee trainer and because it's using
huging<00:04:39.160><c> Fest</c><00:04:39.360><c> trainer</c><00:04:40.000><c> like</c><00:04:40.120><c> we</c><00:04:40.240><c> need</c><00:04:40.360><c> to</c><00:04:40.479><c> add</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
huging Fest trainer like we need to add
 

00:04:40.639 --> 00:04:43.350 align:start position:0%
huging Fest trainer like we need to add
this<00:04:40.800><c> line</c><00:04:41.199><c> report</c><00:04:41.560><c> to</c><00:04:41.720><c> 1B</c><00:04:42.560><c> and</c><00:04:42.720><c> this</c><00:04:42.919><c> will</c>

00:04:43.350 --> 00:04:43.360 align:start position:0%
this line report to 1B and this will
 

00:04:43.360 --> 00:04:44.909 align:start position:0%
this line report to 1B and this will
this<00:04:43.440><c> will</c><00:04:43.639><c> make</c><00:04:43.759><c> sure</c><00:04:43.960><c> like</c><00:04:44.160><c> we</c><00:04:44.240><c> can</c><00:04:44.400><c> see</c><00:04:44.680><c> how</c>

00:04:44.909 --> 00:04:44.919 align:start position:0%
this will make sure like we can see how
 

00:04:44.919 --> 00:04:46.909 align:start position:0%
this will make sure like we can see how
our<00:04:45.160><c> training</c><00:04:45.479><c> is</c><00:04:45.759><c> progresses</c><00:04:46.759><c> is</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
our training is progresses is
 

00:04:46.919 --> 00:04:48.550 align:start position:0%
our training is progresses is
progressing<00:04:47.479><c> in</c><00:04:47.639><c> weights</c><00:04:47.880><c> and</c>

00:04:48.550 --> 00:04:48.560 align:start position:0%
progressing in weights and
 

00:04:48.560 --> 00:04:51.749 align:start position:0%
progressing in weights and
biases<00:04:49.560><c> and</c><00:04:50.000><c> uh</c><00:04:50.600><c> yeah</c><00:04:50.720><c> we</c><00:04:50.800><c> will</c><00:04:50.960><c> not</c><00:04:51.199><c> go</c><00:04:51.520><c> in</c>

00:04:51.749 --> 00:04:51.759 align:start position:0%
biases and uh yeah we will not go in
 

00:04:51.759 --> 00:04:53.749 align:start position:0%
biases and uh yeah we will not go in
detail<00:04:52.160><c> through</c><00:04:52.360><c> the</c><00:04:52.560><c> script</c><00:04:52.960><c> if</c><00:04:53.120><c> you</c><00:04:53.280><c> are</c>

00:04:53.749 --> 00:04:53.759 align:start position:0%
detail through the script if you are
 

00:04:53.759 --> 00:04:55.710 align:start position:0%
detail through the script if you are
interested<00:04:54.120><c> in</c><00:04:54.240><c> learning</c><00:04:54.680><c> more</c><00:04:55.479><c> about</c>

00:04:55.710 --> 00:04:55.720 align:start position:0%
interested in learning more about
 

00:04:55.720 --> 00:04:58.230 align:start position:0%
interested in learning more about
training<00:04:56.039><c> and</c><00:04:56.199><c> find</c><00:04:56.440><c> Uni</c><00:04:56.759><c> llms</c><00:04:57.440><c> we</c><00:04:57.600><c> have</c><00:04:58.039><c> a</c>

00:04:58.230 --> 00:04:58.240 align:start position:0%
training and find Uni llms we have a
 

00:04:58.240 --> 00:04:59.909 align:start position:0%
training and find Uni llms we have a
fantastic<00:04:58.720><c> course</c><00:04:59.000><c> about</c><00:04:59.199><c> training</c><00:04:59.479><c> and</c><00:04:59.720><c> find</c>

00:04:59.909 --> 00:04:59.919 align:start position:0%
fantastic course about training and find
 

00:04:59.919 --> 00:05:01.830 align:start position:0%
fantastic course about training and find
unique<00:05:00.160><c> LMS</c><00:05:00.680><c> that</c><00:05:00.800><c> you</c><00:05:00.880><c> can</c><00:05:01.160><c> access</c><00:05:01.400><c> under</c><00:05:01.680><c> 1</c>

00:05:01.830 --> 00:05:01.840 align:start position:0%
unique LMS that you can access under 1
 

00:05:01.840 --> 00:05:04.950 align:start position:0%
unique LMS that you can access under 1
db.<00:05:02.560><c> courses</c><00:05:03.560><c> and</c><00:05:03.759><c> I</c><00:05:04.000><c> I</c><00:05:04.160><c> I</c><00:05:04.240><c> fully</c><00:05:04.520><c> recommend</c>

00:05:04.950 --> 00:05:04.960 align:start position:0%
db. courses and I I I fully recommend
 

00:05:04.960 --> 00:05:08.390 align:start position:0%
db. courses and I I I fully recommend
that<00:05:05.639><c> so</c><00:05:06.039><c> in</c><00:05:06.199><c> this</c><00:05:06.400><c> case</c><00:05:06.960><c> um</c><00:05:07.440><c> we'll</c><00:05:08.000><c> uh</c><00:05:08.199><c> just</c>

00:05:08.390 --> 00:05:08.400 align:start position:0%
that so in this case um we'll uh just
 

00:05:08.400 --> 00:05:10.270 align:start position:0%
that so in this case um we'll uh just
assume<00:05:08.800><c> that</c><00:05:09.199><c> um</c><00:05:09.400><c> you</c><00:05:09.520><c> know</c><00:05:09.639><c> we</c><00:05:09.759><c> want</c><00:05:09.919><c> to</c><00:05:10.039><c> trade</c>

00:05:10.270 --> 00:05:10.280 align:start position:0%
assume that um you know we want to trade
 

00:05:10.280 --> 00:05:11.830 align:start position:0%
assume that um you know we want to trade
some<00:05:10.400><c> models</c><00:05:10.759><c> we</c><00:05:10.840><c> will</c><00:05:11.000><c> not</c><00:05:11.160><c> go</c><00:05:11.280><c> in</c><00:05:11.440><c> details</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
some models we will not go in details
 

00:05:11.840 --> 00:05:14.270 align:start position:0%
some models we will not go in details
through<00:05:12.199><c> like</c><00:05:12.360><c> every</c><00:05:12.600><c> element</c><00:05:12.880><c> of</c><00:05:13.000><c> the</c><00:05:13.280><c> script</c>

00:05:14.270 --> 00:05:14.280 align:start position:0%
through like every element of the script
 

00:05:14.280 --> 00:05:16.629 align:start position:0%
through like every element of the script
and<00:05:14.840><c> we</c><00:05:15.240><c> um</c><00:05:15.440><c> just</c><00:05:15.680><c> just</c><00:05:15.840><c> one</c><00:05:16.039><c> final</c><00:05:16.320><c> thing</c><00:05:16.520><c> I</c>

00:05:16.629 --> 00:05:16.639 align:start position:0%
and we um just just one final thing I
 

00:05:16.639 --> 00:05:18.590 align:start position:0%
and we um just just one final thing I
want<00:05:16.759><c> to</c><00:05:17.000><c> highlight</c><00:05:17.360><c> is</c><00:05:17.520><c> we</c><00:05:17.639><c> are</c><00:05:17.759><c> adding</c><00:05:18.160><c> this</c>

00:05:18.590 --> 00:05:18.600 align:start position:0%
want to highlight is we are adding this
 

00:05:18.600 --> 00:05:21.070 align:start position:0%
want to highlight is we are adding this
sampling<00:05:19.000><c> callback</c><00:05:19.440><c> for</c><00:05:19.759><c> evaluation</c><00:05:20.759><c> because</c>

00:05:21.070 --> 00:05:21.080 align:start position:0%
sampling callback for evaluation because
 

00:05:21.080 --> 00:05:23.070 align:start position:0%
sampling callback for evaluation because
normally<00:05:21.600><c> as</c><00:05:21.720><c> we</c><00:05:21.880><c> train</c><00:05:22.120><c> the</c><00:05:22.280><c> model</c><00:05:22.720><c> we</c><00:05:22.880><c> get</c>

00:05:23.070 --> 00:05:23.080 align:start position:0%
normally as we train the model we get
 

00:05:23.080 --> 00:05:24.790 align:start position:0%
normally as we train the model we get
access<00:05:23.280><c> to</c><00:05:23.440><c> the</c><00:05:23.680><c> loss</c><00:05:24.319><c> you</c><00:05:24.440><c> can</c><00:05:24.560><c> see</c><00:05:24.720><c> the</c>

00:05:24.790 --> 00:05:24.800 align:start position:0%
access to the loss you can see the
 

00:05:24.800 --> 00:05:27.390 align:start position:0%
access to the loss you can see the
training<00:05:25.160><c> loss</c><00:05:25.400><c> and</c><00:05:25.600><c> evaluation</c><00:05:26.160><c> loss</c><00:05:27.160><c> but</c>

00:05:27.390 --> 00:05:27.400 align:start position:0%
training loss and evaluation loss but
 

00:05:27.400 --> 00:05:29.230 align:start position:0%
training loss and evaluation loss but
very<00:05:27.600><c> often</c><00:05:28.080><c> evaluation</c><00:05:28.600><c> loss</c><00:05:28.840><c> is</c><00:05:29.000><c> not</c>

00:05:29.230 --> 00:05:29.240 align:start position:0%
very often evaluation loss is not
 

00:05:29.240 --> 00:05:30.230 align:start position:0%
very often evaluation loss is not
sufficient

00:05:30.230 --> 00:05:30.240 align:start position:0%
sufficient
 

00:05:30.240 --> 00:05:31.909 align:start position:0%
sufficient
to<00:05:30.440><c> assess</c><00:05:30.720><c> the</c><00:05:30.840><c> quality</c><00:05:31.319><c> of</c><00:05:31.400><c> a</c><00:05:31.560><c> language</c>

00:05:31.909 --> 00:05:31.919 align:start position:0%
to assess the quality of a language
 

00:05:31.919 --> 00:05:34.629 align:start position:0%
to assess the quality of a language
model<00:05:32.440><c> and</c><00:05:32.600><c> for</c><00:05:32.800><c> that</c><00:05:33.000><c> reason</c><00:05:33.960><c> at</c><00:05:34.160><c> a</c><00:05:34.319><c> certain</c>

00:05:34.629 --> 00:05:34.639 align:start position:0%
model and for that reason at a certain
 

00:05:34.639 --> 00:05:36.469 align:start position:0%
model and for that reason at a certain
point<00:05:34.919><c> we</c><00:05:35.039><c> may</c><00:05:35.199><c> want</c><00:05:35.360><c> to</c><00:05:35.759><c> sample</c><00:05:36.199><c> from</c><00:05:36.360><c> the</c>

00:05:36.469 --> 00:05:36.479 align:start position:0%
point we may want to sample from the
 

00:05:36.479 --> 00:05:39.110 align:start position:0%
point we may want to sample from the
model<00:05:36.880><c> so</c><00:05:37.120><c> we</c><00:05:37.360><c> we</c><00:05:37.600><c> we</c><00:05:37.880><c> we</c><00:05:38.120><c> want</c><00:05:38.280><c> to</c><00:05:38.479><c> generate</c>

00:05:39.110 --> 00:05:39.120 align:start position:0%
model so we we we we want to generate
 

00:05:39.120 --> 00:05:42.110 align:start position:0%
model so we we we we want to generate
outputs<00:05:39.600><c> for</c><00:05:39.759><c> a</c><00:05:39.880><c> given</c><00:05:40.199><c> input</c><00:05:41.199><c> and</c><00:05:41.840><c> either</c>

00:05:42.110 --> 00:05:42.120 align:start position:0%
outputs for a given input and either
 

00:05:42.120 --> 00:05:44.670 align:start position:0%
outputs for a given input and either
visualize<00:05:42.639><c> it</c><00:05:43.000><c> or</c><00:05:43.319><c> automatically</c><00:05:44.000><c> evaluate</c>

00:05:44.670 --> 00:05:44.680 align:start position:0%
visualize it or automatically evaluate
 

00:05:44.680 --> 00:05:46.189 align:start position:0%
visualize it or automatically evaluate
this<00:05:44.960><c> this</c><00:05:45.160><c> output</c><00:05:45.520><c> and</c><00:05:45.680><c> this</c><00:05:45.800><c> is</c><00:05:45.919><c> what</c><00:05:46.039><c> we're</c>

00:05:46.189 --> 00:05:46.199 align:start position:0%
this this output and this is what we're
 

00:05:46.199 --> 00:05:48.590 align:start position:0%
this this output and this is what we're
going<00:05:46.319><c> to</c><00:05:46.479><c> do</c><00:05:46.759><c> here</c><00:05:47.600><c> uh</c><00:05:47.720><c> to</c><00:05:48.039><c> control</c><00:05:48.440><c> the</c>

00:05:48.590 --> 00:05:48.600 align:start position:0%
going to do here uh to control the
 

00:05:48.600 --> 00:05:50.749 align:start position:0%
going to do here uh to control the
quality<00:05:49.080><c> of</c><00:05:49.240><c> our</c><00:05:49.479><c> model</c><00:05:49.919><c> so</c><00:05:50.080><c> we're</c><00:05:50.319><c> adding</c><00:05:50.600><c> the</c>

00:05:50.749 --> 00:05:50.759 align:start position:0%
quality of our model so we're adding the
 

00:05:50.759 --> 00:05:52.590 align:start position:0%
quality of our model so we're adding the
sampling<00:05:51.080><c> call</c><00:05:51.360><c> back</c><00:05:51.639><c> that</c><00:05:52.120><c> at</c><00:05:52.240><c> the</c><00:05:52.319><c> end</c><00:05:52.479><c> of</c>

00:05:52.590 --> 00:05:52.600 align:start position:0%
sampling call back that at the end of
 

00:05:52.600 --> 00:05:54.830 align:start position:0%
sampling call back that at the end of
the<00:05:52.720><c> training</c><00:05:53.080><c> we'll</c><00:05:53.319><c> use</c><00:05:54.240><c> uh</c><00:05:54.319><c> we'll</c><00:05:54.520><c> use</c><00:05:54.720><c> the</c>

00:05:54.830 --> 00:05:54.840 align:start position:0%
the training we'll use uh we'll use the
 

00:05:54.840 --> 00:05:56.749 align:start position:0%
the training we'll use uh we'll use the
model<00:05:55.120><c> checkpoint</c><00:05:55.600><c> that</c><00:05:55.680><c> we</c><00:05:55.840><c> saved</c><00:05:56.560><c> and</c>

00:05:56.749 --> 00:05:56.759 align:start position:0%
model checkpoint that we saved and
 

00:05:56.759 --> 00:05:59.270 align:start position:0%
model checkpoint that we saved and
generate<00:05:57.400><c> some</c><00:05:57.680><c> outputs</c><00:05:58.080><c> for</c><00:05:58.319><c> our</c><00:05:58.800><c> evaluation</c>

00:05:59.270 --> 00:05:59.280 align:start position:0%
generate some outputs for our evaluation
 

00:05:59.280 --> 00:06:04.309 align:start position:0%
generate some outputs for our evaluation
data<00:05:59.479><c> set</c><00:05:59.919><c> set</c><00:06:00.919><c> okay</c><00:06:01.680><c> so</c><00:06:02.400><c> let's</c><00:06:03.080><c> uh</c><00:06:03.440><c> start</c><00:06:03.880><c> this</c>

00:06:04.309 --> 00:06:04.319 align:start position:0%
data set set okay so let's uh start this
 

00:06:04.319 --> 00:06:06.830 align:start position:0%
data set set okay so let's uh start this
uh<00:06:04.600><c> this</c><00:06:04.720><c> training</c><00:06:05.039><c> script</c><00:06:05.479><c> now</c><00:06:06.240><c> I'm</c><00:06:06.440><c> going</c><00:06:06.560><c> to</c>

00:06:06.830 --> 00:06:06.840 align:start position:0%
uh this training script now I'm going to
 

00:06:06.840 --> 00:06:08.990 align:start position:0%
uh this training script now I'm going to
go<00:06:07.039><c> into</c><00:06:07.599><c> my</c><00:06:07.800><c> console</c>

00:06:08.990 --> 00:06:09.000 align:start position:0%
go into my console
 

00:06:09.000 --> 00:06:14.990 align:start position:0%
go into my console
and<00:06:10.000><c> do</c><00:06:10.479><c> train.</c><00:06:11.120><c> PI</c><00:06:11.840><c> and</c><00:06:12.599><c> maybe</c><00:06:13.080><c> let's</c><00:06:14.000><c> limit</c>

00:06:14.990 --> 00:06:15.000 align:start position:0%
and do train. PI and maybe let's limit
 

00:06:15.000 --> 00:06:17.710 align:start position:0%
and do train. PI and maybe let's limit
uh<00:06:15.240><c> this</c><00:06:15.520><c> now</c><00:06:16.120><c> to</c><00:06:16.520><c> let's</c><00:06:16.720><c> say</c><00:06:16.880><c> 20</c><00:06:17.199><c> steps</c><00:06:17.599><c> just</c>

00:06:17.710 --> 00:06:17.720 align:start position:0%
uh this now to let's say 20 steps just
 

00:06:17.720 --> 00:06:19.430 align:start position:0%
uh this now to let's say 20 steps just
for<00:06:18.000><c> for</c><00:06:18.160><c> debugging</c><00:06:18.639><c> purposes</c><00:06:19.039><c> and</c><00:06:19.199><c> see</c><00:06:19.319><c> if</c>

00:06:19.430 --> 00:06:19.440 align:start position:0%
for for debugging purposes and see if
 

00:06:19.440 --> 00:06:30.189 align:start position:0%
for for debugging purposes and see if
the<00:06:19.560><c> script</c><00:06:19.800><c> is</c><00:06:19.919><c> running</c>

00:06:30.189 --> 00:06:30.199 align:start position:0%
 
 

00:06:30.199 --> 00:06:32.309 align:start position:0%
 
can<00:06:30.360><c> see</c><00:06:30.720><c> the</c><00:06:31.039><c> script</c><00:06:31.360><c> is</c><00:06:31.520><c> calling</c><00:06:31.919><c> weight</c><00:06:32.160><c> and</c>

00:06:32.309 --> 00:06:32.319 align:start position:0%
can see the script is calling weight and
 

00:06:32.319 --> 00:06:33.870 align:start position:0%
can see the script is calling weight and
bies<00:06:32.680><c> is</c><00:06:32.800><c> starting</c>

00:06:33.870 --> 00:06:33.880 align:start position:0%
bies is starting
 

00:06:33.880 --> 00:06:38.390 align:start position:0%
bies is starting
run<00:06:34.880><c> in</c><00:06:35.120><c> my</c><00:06:35.440><c> review</c><00:06:36.039><c> entity</c><00:06:37.039><c> uh</c><00:06:37.280><c> one</c><00:06:37.560><c> tip</c><00:06:38.039><c> is</c>

00:06:38.390 --> 00:06:38.400 align:start position:0%
run in my review entity uh one tip is
 

00:06:38.400 --> 00:06:40.110 align:start position:0%
run in my review entity uh one tip is
and<00:06:38.720><c> yeah</c><00:06:38.840><c> the</c><00:06:38.919><c> training</c><00:06:39.240><c> is</c><00:06:39.400><c> starting</c><00:06:39.840><c> and</c><00:06:40.000><c> it</c>

00:06:40.110 --> 00:06:40.120 align:start position:0%
and yeah the training is starting and it
 

00:06:40.120 --> 00:06:42.110 align:start position:0%
and yeah the training is starting and it
will<00:06:40.520><c> finish</c><00:06:40.880><c> quickly</c><00:06:41.319><c> because</c><00:06:41.639><c> this</c><00:06:41.759><c> is</c><00:06:41.960><c> just</c>

00:06:42.110 --> 00:06:42.120 align:start position:0%
will finish quickly because this is just
 

00:06:42.120 --> 00:06:45.270 align:start position:0%
will finish quickly because this is just
20<00:06:42.560><c> steps</c><00:06:43.120><c> uh</c><00:06:43.319><c> debugging</c><00:06:43.800><c> steps</c><00:06:44.720><c> uh</c><00:06:44.880><c> one</c><00:06:45.080><c> thing</c>

00:06:45.270 --> 00:06:45.280 align:start position:0%
20 steps uh debugging steps uh one thing
 

00:06:45.280 --> 00:06:46.629 align:start position:0%
20 steps uh debugging steps uh one thing
I<00:06:45.360><c> want</c><00:06:45.479><c> to</c><00:06:45.680><c> highlight</c><00:06:46.039><c> is</c><00:06:46.240><c> as</c><00:06:46.360><c> you</c><00:06:46.479><c> are</c>

00:06:46.629 --> 00:06:46.639 align:start position:0%
I want to highlight is as you are
 

00:06:46.639 --> 00:06:48.909 align:start position:0%
I want to highlight is as you are
running<00:06:46.919><c> the</c><00:06:47.120><c> script</c><00:06:47.919><c> uh</c><00:06:48.039><c> please</c><00:06:48.319><c> adjust</c><00:06:48.759><c> the</c>

00:06:48.909 --> 00:06:48.919 align:start position:0%
running the script uh please adjust the
 

00:06:48.919 --> 00:06:50.710 align:start position:0%
running the script uh please adjust the
project<00:06:49.240><c> name</c><00:06:49.479><c> and</c><00:06:49.680><c> specifically</c><00:06:50.280><c> the</c><00:06:50.560><c> the</c>

00:06:50.710 --> 00:06:50.720 align:start position:0%
project name and specifically the the
 

00:06:50.720 --> 00:06:52.749 align:start position:0%
project name and specifically the the
one<00:06:50.880><c> weit</c><00:06:51.080><c> Andes</c><00:06:51.520><c> entity</c><00:06:51.919><c> you</c><00:06:52.000><c> will</c><00:06:52.199><c> not</c><00:06:52.440><c> have</c>

00:06:52.749 --> 00:06:52.759 align:start position:0%
one weit Andes entity you will not have
 

00:06:52.759 --> 00:06:54.909 align:start position:0%
one weit Andes entity you will not have
access<00:06:53.039><c> to</c><00:06:53.240><c> revie</c><00:06:53.599><c> co</c><00:06:53.840><c> entity</c><00:06:54.479><c> so</c><00:06:54.639><c> you</c><00:06:54.759><c> might</c>

00:06:54.909 --> 00:06:54.919 align:start position:0%
access to revie co entity so you might
 

00:06:54.919 --> 00:06:56.150 align:start position:0%
access to revie co entity so you might
want<00:06:55.039><c> to</c><00:06:55.160><c> change</c><00:06:55.400><c> it</c><00:06:55.599><c> either</c><00:06:55.800><c> to</c><00:06:55.919><c> your</c>

00:06:56.150 --> 00:06:56.160 align:start position:0%
want to change it either to your
 

00:06:56.160 --> 00:06:59.150 align:start position:0%
want to change it either to your
personal<00:06:56.759><c> entity</c><00:06:57.440><c> or</c><00:06:57.560><c> to</c><00:06:57.759><c> the</c><00:06:57.879><c> team</c><00:06:58.879><c> um</c><00:06:59.080><c> that</c>

00:06:59.150 --> 00:06:59.160 align:start position:0%
personal entity or to the team um that
 

00:06:59.160 --> 00:07:02.270 align:start position:0%
personal entity or to the team um that
you<00:06:59.680><c> we</c><00:06:59.879><c> and</c><00:07:00.039><c> bies</c><00:07:00.440><c> and</c><00:07:01.440><c> you</c><00:07:01.560><c> can</c><00:07:01.720><c> adjust</c><00:07:02.080><c> also</c>

00:07:02.270 --> 00:07:02.280 align:start position:0%
you we and bies and you can adjust also
 

00:07:02.280 --> 00:07:04.510 align:start position:0%
you we and bies and you can adjust also
the<00:07:02.400><c> we</c><00:07:02.560><c> and</c><00:07:02.680><c> bies</c><00:07:02.960><c> taxs</c><00:07:03.319><c> you</c><00:07:03.400><c> can</c><00:07:03.560><c> change</c><00:07:04.039><c> the</c>

00:07:04.510 --> 00:07:04.520 align:start position:0%
the we and bies taxs you can change the
 

00:07:04.520 --> 00:07:06.710 align:start position:0%
the we and bies taxs you can change the
the<00:07:04.639><c> input</c><00:07:04.960><c> model</c><00:07:05.319><c> we</c><00:07:05.400><c> are</c><00:07:05.560><c> starting</c><00:07:06.039><c> here</c><00:07:06.360><c> off</c>

00:07:06.710 --> 00:07:06.720 align:start position:0%
the input model we are starting here off
 

00:07:06.720 --> 00:07:09.589 align:start position:0%
the input model we are starting here off
with<00:07:07.280><c> u</c><00:07:07.800><c> one</c><00:07:08.120><c> one</c><00:07:08.360><c> bill</c><00:07:08.680><c> 1.1</c><00:07:09.240><c> billion</c>

00:07:09.589 --> 00:07:09.599 align:start position:0%
with u one one bill 1.1 billion
 

00:07:09.599 --> 00:07:12.830 align:start position:0%
with u one one bill 1.1 billion
parameter<00:07:10.120><c> tiny</c><00:07:10.520><c> lava</c><00:07:11.520><c> uh</c><00:07:11.720><c> that</c><00:07:11.919><c> is</c><00:07:12.160><c> our</c><00:07:12.599><c> our</c>

00:07:12.830 --> 00:07:12.840 align:start position:0%
parameter tiny lava uh that is our our
 

00:07:12.840 --> 00:07:14.990 align:start position:0%
parameter tiny lava uh that is our our
pre-train<00:07:13.479><c> backbone</c><00:07:14.479><c> and</c><00:07:14.599><c> these</c><00:07:14.720><c> are</c><00:07:14.879><c> the</c>

00:07:14.990 --> 00:07:15.000 align:start position:0%
pre-train backbone and these are the
 

00:07:15.000 --> 00:07:16.909 align:start position:0%
pre-train backbone and these are the
things<00:07:15.240><c> that</c><00:07:15.319><c> you</c><00:07:15.440><c> can</c><00:07:16.039><c> you</c><00:07:16.160><c> can</c><00:07:16.360><c> adjust</c><00:07:16.759><c> as</c>

00:07:16.909 --> 00:07:16.919 align:start position:0%
things that you can you can adjust as
 

00:07:16.919 --> 00:07:18.790 align:start position:0%
things that you can you can adjust as
you<00:07:17.400><c> as</c><00:07:17.520><c> you're</c><00:07:17.680><c> playing</c><00:07:17.960><c> and</c><00:07:18.120><c> trying</c><00:07:18.360><c> to</c><00:07:18.560><c> get</c>

00:07:18.790 --> 00:07:18.800 align:start position:0%
you as you're playing and trying to get
 

00:07:18.800 --> 00:07:20.309 align:start position:0%
you as you're playing and trying to get
a<00:07:19.080><c> a</c><00:07:19.240><c> better</c><00:07:19.479><c> and</c><00:07:19.639><c> better</c><00:07:19.919><c> instruction</c>

00:07:20.309 --> 00:07:20.319 align:start position:0%
a a better and better instruction
 

00:07:20.319 --> 00:07:21.710 align:start position:0%
a a better and better instruction
following

00:07:21.710 --> 00:07:21.720 align:start position:0%
following
 

00:07:21.720 --> 00:07:24.830 align:start position:0%
following
model<00:07:22.720><c> okay</c><00:07:23.160><c> so</c><00:07:23.639><c> the</c><00:07:23.800><c> script</c><00:07:24.080><c> finished</c>

00:07:24.830 --> 00:07:24.840 align:start position:0%
model okay so the script finished
 

00:07:24.840 --> 00:07:28.110 align:start position:0%
model okay so the script finished
training<00:07:25.840><c> and</c><00:07:26.000><c> it's</c><00:07:26.280><c> now</c><00:07:27.080><c> generating</c><00:07:27.639><c> a</c><00:07:27.800><c> data</c>

00:07:28.110 --> 00:07:28.120 align:start position:0%
training and it's now generating a data
 

00:07:28.120 --> 00:07:32.390 align:start position:0%
training and it's now generating a data
set<00:07:28.639><c> um</c><00:07:29.080><c> that</c><00:07:29.639><c> generating</c><00:07:30.240><c> an</c><00:07:30.680><c> um</c><00:07:31.400><c> evaluation</c>

00:07:32.390 --> 00:07:32.400 align:start position:0%
set um that generating an um evaluation
 

00:07:32.400 --> 00:07:35.309 align:start position:0%
set um that generating an um evaluation
set<00:07:32.599><c> of</c><00:07:32.800><c> data</c><00:07:33.680><c> uh</c><00:07:33.840><c> so</c><00:07:34.400><c> uh</c><00:07:34.599><c> let's</c><00:07:34.919><c> uh</c><00:07:35.039><c> let's</c><00:07:35.199><c> give</c>

00:07:35.309 --> 00:07:35.319 align:start position:0%
set of data uh so uh let's uh let's give
 

00:07:35.319 --> 00:07:36.869 align:start position:0%
set of data uh so uh let's uh let's give
it<00:07:35.440><c> a</c><00:07:35.560><c> while</c><00:07:35.720><c> to</c><00:07:35.879><c> finish</c><00:07:36.319><c> and</c><00:07:36.440><c> in</c><00:07:36.560><c> the</c><00:07:36.680><c> next</c>

00:07:36.869 --> 00:07:36.879 align:start position:0%
it a while to finish and in the next
 

00:07:36.879 --> 00:07:38.909 align:start position:0%
it a while to finish and in the next
video<00:07:37.080><c> we'll</c><00:07:37.280><c> pick</c><00:07:37.440><c> it</c><00:07:37.599><c> up</c><00:07:38.240><c> and</c><00:07:38.400><c> we'll</c><00:07:38.599><c> try</c><00:07:38.800><c> to</c>

00:07:38.909 --> 00:07:38.919 align:start position:0%
video we'll pick it up and we'll try to
 

00:07:38.919 --> 00:07:41.270 align:start position:0%
video we'll pick it up and we'll try to
see<00:07:39.160><c> how</c><00:07:39.319><c> we</c><00:07:39.479><c> might</c><00:07:39.680><c> want</c><00:07:39.840><c> to</c><00:07:40.319><c> evaluate</c><00:07:41.120><c> this</c>

00:07:41.270 --> 00:07:41.280 align:start position:0%
see how we might want to evaluate this
 

00:07:41.280 --> 00:07:44.280 align:start position:0%
see how we might want to evaluate this
model

