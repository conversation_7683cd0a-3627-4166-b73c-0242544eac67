WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.149 align:start position:0%
 
one<00:00:00.599><c> of</c><00:00:00.719><c> the</c><00:00:00.840><c> reasons</c><00:00:01.199><c> people</c><00:00:01.500><c> migrate</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
one of the reasons people migrate
 

00:00:02.159 --> 00:00:04.850 align:start position:0%
one of the reasons people migrate
JavaScript<00:00:02.700><c> applications</c><00:00:03.360><c> to</c><00:00:03.840><c> typescript</c><00:00:04.319><c> is</c>

00:00:04.850 --> 00:00:04.860 align:start position:0%
JavaScript applications to typescript is
 

00:00:04.860 --> 00:00:06.410 align:start position:0%
JavaScript applications to typescript is
because<00:00:05.040><c> they</c><00:00:05.339><c> want</c><00:00:05.460><c> to</c><00:00:05.580><c> help</c><00:00:05.700><c> developers</c>

00:00:06.410 --> 00:00:06.420 align:start position:0%
because they want to help developers
 

00:00:06.420 --> 00:00:08.690 align:start position:0%
because they want to help developers
catch<00:00:07.140><c> errors</c><00:00:07.500><c> quickly</c><00:00:07.980><c> during</c><00:00:08.460><c> the</c>

00:00:08.690 --> 00:00:08.700 align:start position:0%
catch errors quickly during the
 

00:00:08.700 --> 00:00:11.330 align:start position:0%
catch errors quickly during the
development<00:00:09.120><c> phase</c><00:00:09.900><c> but</c><00:00:10.500><c> converting</c><00:00:11.099><c> a</c>

00:00:11.330 --> 00:00:11.340 align:start position:0%
development phase but converting a
 

00:00:11.340 --> 00:00:13.629 align:start position:0%
development phase but converting a
project<00:00:11.519><c> from</c><00:00:12.059><c> JavaScript</c><00:00:12.599><c> to</c><00:00:12.900><c> typescript</c>

00:00:13.629 --> 00:00:13.639 align:start position:0%
project from JavaScript to typescript
 

00:00:13.639 --> 00:00:17.029 align:start position:0%
project from JavaScript to typescript
isn't<00:00:14.639><c> always</c><00:00:15.059><c> straightforward</c><00:00:15.839><c> in</c><00:00:16.320><c> fact</c><00:00:16.500><c> I</c>

00:00:17.029 --> 00:00:17.039 align:start position:0%
isn't always straightforward in fact I
 

00:00:17.039 --> 00:00:19.310 align:start position:0%
isn't always straightforward in fact I
have<00:00:17.220><c> an</c><00:00:17.400><c> AI</c><00:00:17.760><c> powered</c><00:00:18.300><c> task</c><00:00:18.660><c> management</c><00:00:18.840><c> app</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
have an AI powered task management app
 

00:00:19.320 --> 00:00:21.710 align:start position:0%
have an AI powered task management app
that<00:00:19.619><c> I</c><00:00:19.740><c> built</c><00:00:19.980><c> in</c><00:00:20.100><c> xjs</c><00:00:20.640><c> and</c><00:00:21.240><c> I</c><00:00:21.420><c> want</c><00:00:21.600><c> to</c>

00:00:21.710 --> 00:00:21.720 align:start position:0%
that I built in xjs and I want to
 

00:00:21.720 --> 00:00:24.830 align:start position:0%
that I built in xjs and I want to
convert<00:00:22.080><c> that</c><00:00:22.320><c> to</c><00:00:22.680><c> use</c><00:00:22.800><c> typescript</c><00:00:23.340><c> but</c><00:00:24.180><c> I</c>

00:00:24.830 --> 00:00:24.840 align:start position:0%
convert that to use typescript but I
 

00:00:24.840 --> 00:00:26.830 align:start position:0%
convert that to use typescript but I
might<00:00:24.960><c> need</c><00:00:25.199><c> a</c><00:00:25.380><c> little</c><00:00:25.500><c> bit</c><00:00:25.619><c> of</c><00:00:25.740><c> help</c><00:00:25.980><c> from</c>

00:00:26.830 --> 00:00:26.840 align:start position:0%
might need a little bit of help from
 

00:00:26.840 --> 00:00:30.950 align:start position:0%
might need a little bit of help from
copilot<00:00:27.840><c> chat</c><00:00:28.340><c> co-pilot</c><00:00:29.340><c> chat</c><00:00:29.699><c> is</c><00:00:30.359><c> created</c><00:00:30.720><c> by</c>

00:00:30.950 --> 00:00:30.960 align:start position:0%
copilot chat co-pilot chat is created by
 

00:00:30.960 --> 00:00:33.709 align:start position:0%
copilot chat co-pilot chat is created by
GitHub<00:00:31.439><c> and</c><00:00:31.800><c> it's</c><00:00:31.980><c> a</c><00:00:32.160><c> chat</c><00:00:32.460><c> interface</c><00:00:33.000><c> that</c>

00:00:33.709 --> 00:00:33.719 align:start position:0%
GitHub and it's a chat interface that
 

00:00:33.719 --> 00:00:36.290 align:start position:0%
GitHub and it's a chat interface that
allows<00:00:34.140><c> you</c><00:00:34.320><c> to</c><00:00:34.680><c> ask</c><00:00:34.980><c> questions</c><00:00:35.399><c> and</c><00:00:36.000><c> receive</c>

00:00:36.290 --> 00:00:36.300 align:start position:0%
allows you to ask questions and receive
 

00:00:36.300 --> 00:00:39.950 align:start position:0%
allows you to ask questions and receive
answers<00:00:36.840><c> directly</c><00:00:37.739><c> in</c><00:00:38.100><c> a</c><00:00:38.280><c> supported</c><00:00:38.700><c> IDE</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
answers directly in a supported IDE
 

00:00:39.960 --> 00:00:42.590 align:start position:0%
answers directly in a supported IDE
when<00:00:40.620><c> I</c><00:00:40.860><c> asked</c><00:00:41.219><c> co-pilot</c><00:00:41.820><c> chat</c><00:00:42.120><c> to</c><00:00:42.360><c> help</c><00:00:42.480><c> me</c>

00:00:42.590 --> 00:00:42.600 align:start position:0%
when I asked co-pilot chat to help me
 

00:00:42.600 --> 00:00:44.869 align:start position:0%
when I asked co-pilot chat to help me
convert<00:00:43.079><c> my</c><00:00:43.440><c> next</c><00:00:43.559><c> JS</c><00:00:43.920><c> project</c><00:00:44.280><c> to</c><00:00:44.760><c> use</c>

00:00:44.869 --> 00:00:44.879 align:start position:0%
convert my next JS project to use
 

00:00:44.879 --> 00:00:47.810 align:start position:0%
convert my next JS project to use
typescript<00:00:45.420><c> it</c><00:00:46.200><c> gave</c><00:00:46.440><c> me</c><00:00:46.620><c> a</c><00:00:47.160><c> step-by-step</c>

00:00:47.810 --> 00:00:47.820 align:start position:0%
typescript it gave me a step-by-step
 

00:00:47.820 --> 00:00:50.869 align:start position:0%
typescript it gave me a step-by-step
guide<00:00:48.120><c> starting</c><00:00:48.719><c> with</c><00:00:49.140><c> a</c><00:00:50.039><c> command</c><00:00:50.460><c> to</c><00:00:50.700><c> install</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
guide starting with a command to install
 

00:00:50.879 --> 00:00:53.750 align:start position:0%
guide starting with a command to install
required<00:00:51.719><c> dependencies</c><00:00:52.379><c> I</c><00:00:53.160><c> can</c><00:00:53.280><c> actually</c><00:00:53.460><c> use</c>

00:00:53.750 --> 00:00:53.760 align:start position:0%
required dependencies I can actually use
 

00:00:53.760 --> 00:00:55.910 align:start position:0%
required dependencies I can actually use
this<00:00:54.059><c> button</c><00:00:54.420><c> to</c><00:00:54.840><c> run</c><00:00:54.960><c> the</c><00:00:55.140><c> command</c><00:00:55.440><c> in</c><00:00:55.680><c> my</c>

00:00:55.910 --> 00:00:55.920 align:start position:0%
this button to run the command in my
 

00:00:55.920 --> 00:00:57.110 align:start position:0%
this button to run the command in my
terminal

00:00:57.110 --> 00:00:57.120 align:start position:0%
terminal
 

00:00:57.120 --> 00:01:00.910 align:start position:0%
terminal
it<00:00:57.719><c> also</c><00:00:58.079><c> suggests</c><00:00:58.620><c> that</c><00:00:59.100><c> I</c><00:00:59.340><c> create</c><00:00:59.520><c> a</c><00:01:00.120><c> TS</c>

00:01:00.910 --> 00:01:00.920 align:start position:0%
it also suggests that I create a TS
 

00:01:00.920 --> 00:01:03.650 align:start position:0%
it also suggests that I create a TS
config.json<00:01:01.920><c> file</c><00:01:02.340><c> with</c><00:01:02.760><c> the</c><00:01:02.940><c> right</c><00:01:03.120><c> compiler</c>

00:01:03.650 --> 00:01:03.660 align:start position:0%
config.json file with the right compiler
 

00:01:03.660 --> 00:01:06.590 align:start position:0%
config.json file with the right compiler
options<00:01:04.080><c> thanks</c><00:01:04.920><c> to</c><00:01:05.159><c> copilot</c><00:01:05.760><c> chat</c><00:01:06.060><c> I</c><00:01:06.479><c> can</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
options thanks to copilot chat I can
 

00:01:06.600 --> 00:01:09.289 align:start position:0%
options thanks to copilot chat I can
just<00:01:06.780><c> insert</c><00:01:07.200><c> the</c><00:01:07.500><c> code</c><00:01:07.619><c> using</c><00:01:08.400><c> the</c><00:01:08.939><c> insert</c>

00:01:09.289 --> 00:01:09.299 align:start position:0%
just insert the code using the insert
 

00:01:09.299 --> 00:01:13.010 align:start position:0%
just insert the code using the insert
cursor<00:01:10.140><c> button</c><00:01:10.979><c> I</c><00:01:11.760><c> did</c><00:01:11.880><c> all</c><00:01:12.240><c> of</c><00:01:12.360><c> those</c><00:01:12.600><c> steps</c>

00:01:13.010 --> 00:01:13.020 align:start position:0%
cursor button I did all of those steps
 

00:01:13.020 --> 00:01:15.649 align:start position:0%
cursor button I did all of those steps
already<00:01:13.280><c> now</c><00:01:14.280><c> I</c><00:01:14.640><c> need</c><00:01:14.760><c> to</c><00:01:14.880><c> convert</c><00:01:15.299><c> the</c><00:01:15.479><c> code</c>

00:01:15.649 --> 00:01:15.659 align:start position:0%
already now I need to convert the code
 

00:01:15.659 --> 00:01:18.830 align:start position:0%
already now I need to convert the code
in<00:01:16.200><c> my</c><00:01:16.439><c> index</c><00:01:16.799><c> file</c><00:01:17.159><c> to</c><00:01:17.400><c> typescript</c><00:01:17.880><c> let</c><00:01:18.659><c> me</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
in my index file to typescript let me
 

00:01:18.840 --> 00:01:21.710 align:start position:0%
in my index file to typescript let me
ask<00:01:19.020><c> copilot</c><00:01:19.799><c> chat</c><00:01:20.159><c> for</c><00:01:20.460><c> help</c><00:01:20.640><c> with</c><00:01:20.880><c> that</c>

00:01:21.710 --> 00:01:21.720 align:start position:0%
ask copilot chat for help with that
 

00:01:21.720 --> 00:01:25.730 align:start position:0%
ask copilot chat for help with that
okay<00:01:22.200><c> wow</c><00:01:22.860><c> it</c><00:01:23.400><c> is</c><00:01:23.820><c> literally</c><00:01:24.540><c> generating</c><00:01:25.320><c> all</c>

00:01:25.730 --> 00:01:25.740 align:start position:0%
okay wow it is literally generating all
 

00:01:25.740 --> 00:01:27.410 align:start position:0%
okay wow it is literally generating all
the<00:01:25.860><c> typescript</c><00:01:26.280><c> needed</c>

00:01:27.410 --> 00:01:27.420 align:start position:0%
the typescript needed
 

00:01:27.420 --> 00:01:29.450 align:start position:0%
the typescript needed
I'm<00:01:27.780><c> just</c><00:01:27.960><c> going</c><00:01:28.080><c> to</c><00:01:28.259><c> go</c><00:01:28.439><c> ahead</c><00:01:28.680><c> and</c><00:01:29.220><c> press</c>

00:01:29.450 --> 00:01:29.460 align:start position:0%
I'm just going to go ahead and press
 

00:01:29.460 --> 00:01:30.950 align:start position:0%
I'm just going to go ahead and press
insert

00:01:30.950 --> 00:01:30.960 align:start position:0%
insert
 

00:01:30.960 --> 00:01:33.350 align:start position:0%
insert
there<00:01:31.680><c> were</c><00:01:31.860><c> a</c><00:01:32.100><c> lot</c><00:01:32.220><c> of</c><00:01:32.400><c> steps</c><00:01:32.759><c> and</c><00:01:33.060><c> it</c><00:01:33.180><c> would</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
there were a lot of steps and it would
 

00:01:33.360 --> 00:01:35.510 align:start position:0%
there were a lot of steps and it would
have<00:01:33.479><c> taken</c><00:01:33.780><c> me</c><00:01:33.900><c> much</c><00:01:34.200><c> longer</c><00:01:34.680><c> if</c><00:01:35.159><c> I</c><00:01:35.340><c> was</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
have taken me much longer if I was
 

00:01:35.520 --> 00:01:38.870 align:start position:0%
have taken me much longer if I was
searching<00:01:35.939><c> on</c><00:01:36.240><c> the</c><00:01:36.420><c> internet</c><00:01:36.600><c> for</c><00:01:37.200><c> answers</c>

00:01:38.870 --> 00:01:38.880 align:start position:0%
searching on the internet for answers
 

00:01:38.880 --> 00:01:42.710 align:start position:0%
searching on the internet for answers
here's<00:01:39.659><c> what</c><00:01:39.780><c> I</c><00:01:40.020><c> love</c><00:01:40.579><c> copilot</c><00:01:41.579><c> chat</c><00:01:41.939><c> now</c><00:01:42.119><c> only</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
here's what I love copilot chat now only
 

00:01:42.720 --> 00:01:45.710 align:start position:0%
here's what I love copilot chat now only
added<00:01:43.200><c> interfaces</c><00:01:43.979><c> to</c><00:01:44.520><c> ensure</c><00:01:44.880><c> type</c><00:01:45.180><c> safety</c>

00:01:45.710 --> 00:01:45.720 align:start position:0%
added interfaces to ensure type safety
 

00:01:45.720 --> 00:01:49.190 align:start position:0%
added interfaces to ensure type safety
it<00:01:46.560><c> also</c><00:01:46.920><c> provided</c><00:01:47.460><c> clear</c><00:01:47.820><c> explanations</c><00:01:48.600><c> as</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
it also provided clear explanations as
 

00:01:49.200 --> 00:01:51.050 align:start position:0%
it also provided clear explanations as
to<00:01:49.500><c> why</c><00:01:49.680><c> certain</c><00:01:49.920><c> changes</c><00:01:50.460><c> were</c><00:01:50.640><c> being</c><00:01:50.820><c> made</c>

00:01:51.050 --> 00:01:51.060 align:start position:0%
to why certain changes were being made
 

00:01:51.060 --> 00:01:54.109 align:start position:0%
to why certain changes were being made
this<00:01:51.780><c> way</c><00:01:52.020><c> I</c><00:01:52.560><c> can</c><00:01:52.619><c> make</c><00:01:52.799><c> informed</c><00:01:53.579><c> decisions</c>

00:01:54.109 --> 00:01:54.119 align:start position:0%
this way I can make informed decisions
 

00:01:54.119 --> 00:01:57.170 align:start position:0%
this way I can make informed decisions
as<00:01:54.600><c> I</c><00:01:54.840><c> add</c><00:01:55.140><c> in</c><00:01:55.320><c> code</c><00:01:55.619><c> suggested</c><00:01:56.340><c> by</c><00:01:56.579><c> copilot</c>

00:01:57.170 --> 00:01:57.180 align:start position:0%
as I add in code suggested by copilot
 

00:01:57.180 --> 00:01:58.969 align:start position:0%
as I add in code suggested by copilot
chat

00:01:58.969 --> 00:01:58.979 align:start position:0%
chat
 

00:01:58.979 --> 00:02:00.889 align:start position:0%
chat
you<00:01:59.520><c> know</c><00:01:59.640><c> this</c><00:01:59.939><c> could</c><00:02:00.119><c> have</c><00:02:00.240><c> been</c><00:02:00.420><c> a</c><00:02:00.720><c> really</c>

00:02:00.889 --> 00:02:00.899 align:start position:0%
you know this could have been a really
 

00:02:00.899 --> 00:02:03.469 align:start position:0%
you know this could have been a really
daunting<00:02:01.439><c> migration</c><00:02:01.979><c> process</c><00:02:02.460><c> but</c><00:02:03.060><c> using</c>

00:02:03.469 --> 00:02:03.479 align:start position:0%
daunting migration process but using
 

00:02:03.479 --> 00:02:06.170 align:start position:0%
daunting migration process but using
copilot<00:02:04.079><c> chat</c><00:02:04.439><c> really</c><00:02:04.799><c> streamlined</c><00:02:05.460><c> it</c><00:02:05.640><c> and</c>

00:02:06.170 --> 00:02:06.180 align:start position:0%
copilot chat really streamlined it and
 

00:02:06.180 --> 00:02:10.640 align:start position:0%
copilot chat really streamlined it and
to<00:02:06.780><c> be</c><00:02:06.899><c> honest</c><00:02:07.200><c> it</c><00:02:07.920><c> kind</c><00:02:08.039><c> of</c><00:02:08.160><c> made</c><00:02:08.280><c> it</c><00:02:08.459><c> fun</c>

