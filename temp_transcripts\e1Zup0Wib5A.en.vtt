WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:03.270 align:start position:0%
 
hello<00:00:01.079><c> Community</c><00:00:01.680><c> today</c><00:00:02.000><c> the</c><00:00:02.240><c> solution</c><00:00:02.879><c> of</c><00:00:03.040><c> my</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
hello Community today the solution of my
 

00:00:03.280 --> 00:00:05.070 align:start position:0%
hello Community today the solution of my
extreme<00:00:03.760><c> test</c><00:00:04.040><c> for</c><00:00:04.200><c> all</c><00:00:04.400><c> the</c><00:00:04.560><c> things</c><00:00:04.799><c> that</c><00:00:04.920><c> we</c>

00:00:05.070 --> 00:00:05.080 align:start position:0%
extreme test for all the things that we
 

00:00:05.080 --> 00:00:07.510 align:start position:0%
extreme test for all the things that we
looked<00:00:05.319><c> at</c><00:00:05.839><c> now</c><00:00:06.000><c> you</c><00:00:06.120><c> know</c><00:00:06.640><c> I</c><00:00:06.879><c> performed</c><00:00:07.319><c> a</c>

00:00:07.510 --> 00:00:07.520 align:start position:0%
looked at now you know I performed a
 

00:00:07.520 --> 00:00:10.270 align:start position:0%
looked at now you know I performed a
simple<00:00:08.040><c> extreme</c><00:00:08.719><c> logic</c><00:00:09.120><c> test</c><00:00:09.400><c> to</c><00:00:09.639><c> test</c><00:00:10.000><c> here</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
simple extreme logic test to test here
 

00:00:10.280 --> 00:00:13.789 align:start position:0%
simple extreme logic test to test here
the<00:00:10.400><c> new</c><00:00:10.599><c> open</c><00:00:11.360><c> i1</c><00:00:12.360><c> and</c><00:00:12.639><c> the</c><00:00:12.840><c> task</c><00:00:13.160><c> was</c><00:00:13.400><c> simple</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
the new open i1 and the task was simple
 

00:00:13.799 --> 00:00:16.230 align:start position:0%
the new open i1 and the task was simple
I<00:00:13.920><c> left</c><00:00:14.200><c> some</c><00:00:14.480><c> cloes</c><00:00:15.000><c> I</c><00:00:15.200><c> have</c><00:00:15.440><c> here</c><00:00:15.679><c> artifact</c>

00:00:16.230 --> 00:00:16.240 align:start position:0%
I left some cloes I have here artifact
 

00:00:16.240 --> 00:00:18.630 align:start position:0%
I left some cloes I have here artifact
field<00:00:16.480><c> of</c><00:00:16.680><c> magic</c><00:00:16.960><c> and</c><00:00:17.160><c> familiars</c><00:00:18.080><c> and</c><00:00:18.240><c> some</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
field of magic and familiars and some
 

00:00:18.640 --> 00:00:20.429 align:start position:0%
field of magic and familiars and some
very<00:00:18.880><c> simple</c><00:00:19.240><c> instruction</c><00:00:19.760><c> and</c><00:00:19.920><c> this</c><00:00:20.039><c> is</c><00:00:20.160><c> all</c>

00:00:20.429 --> 00:00:20.439 align:start position:0%
very simple instruction and this is all
 

00:00:20.439 --> 00:00:22.630 align:start position:0%
very simple instruction and this is all
there<00:00:20.600><c> is</c><00:00:20.800><c> and</c><00:00:21.279><c> you</c><00:00:21.480><c> found</c><00:00:21.800><c> a</c><00:00:22.000><c> prompt</c><00:00:22.320><c> in</c><00:00:22.439><c> my</c>

00:00:22.630 --> 00:00:22.640 align:start position:0%
there is and you found a prompt in my
 

00:00:22.640 --> 00:00:24.070 align:start position:0%
there is and you found a prompt in my
last<00:00:22.880><c> video</c><00:00:23.119><c> in</c><00:00:23.240><c> the</c><00:00:23.359><c> description</c><00:00:23.760><c> of</c><00:00:23.920><c> the</c>

00:00:24.070 --> 00:00:24.080 align:start position:0%
last video in the description of the
 

00:00:24.080 --> 00:00:27.189 align:start position:0%
last video in the description of the
video<00:00:24.920><c> now</c><00:00:25.800><c> you</c><00:00:26.000><c> remember</c><00:00:26.400><c> the</c><00:00:26.519><c> final</c><00:00:26.800><c> answer</c>

00:00:27.189 --> 00:00:27.199 align:start position:0%
video now you remember the final answer
 

00:00:27.199 --> 00:00:30.470 align:start position:0%
video now you remember the final answer
by<00:00:27.359><c> J</c><00:00:27.599><c> gb01</c><00:00:28.519><c> preview</c><00:00:29.039><c> was</c><00:00:29.519><c> this</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
by J gb01 preview was this
 

00:00:30.480 --> 00:00:32.910 align:start position:0%
by J gb01 preview was this
and<00:00:30.679><c> now</c><00:00:30.920><c> I</c><00:00:31.039><c> had</c><00:00:31.199><c> the</c><00:00:31.400><c> time</c><00:00:31.640><c> to</c><00:00:31.880><c> have</c><00:00:32.040><c> a</c><00:00:32.200><c> look</c><00:00:32.399><c> at</c>

00:00:32.910 --> 00:00:32.920 align:start position:0%
and now I had the time to have a look at
 

00:00:32.920 --> 00:00:36.270 align:start position:0%
and now I had the time to have a look at
this<00:00:33.920><c> and</c><00:00:34.399><c> because</c><00:00:34.800><c> I</c><00:00:34.920><c> saw</c><00:00:35.480><c> that</c><00:00:35.719><c> some</c><00:00:35.920><c> of</c><00:00:36.079><c> the</c>

00:00:36.270 --> 00:00:36.280 align:start position:0%
this and because I saw that some of the
 

00:00:36.280 --> 00:00:38.830 align:start position:0%
this and because I saw that some of the
comments<00:00:36.680><c> said</c><00:00:37.040><c> hey</c><00:00:37.399><c> neither</c><00:00:37.960><c> are</c><00:00:38.120><c> correct</c>

00:00:38.830 --> 00:00:38.840 align:start position:0%
comments said hey neither are correct
 

00:00:38.840 --> 00:00:40.470 align:start position:0%
comments said hey neither are correct
and<00:00:39.000><c> some</c><00:00:39.160><c> mother</c><00:00:39.360><c> wrote</c><00:00:39.600><c> me</c><00:00:39.760><c> here</c><00:00:40.039><c> none</c><00:00:40.280><c> of</c>

00:00:40.470 --> 00:00:40.480 align:start position:0%
and some mother wrote me here none of
 

00:00:40.480 --> 00:00:42.790 align:start position:0%
and some mother wrote me here none of
them<00:00:40.640><c> are</c><00:00:40.840><c> correct</c><00:00:41.559><c> so</c><00:00:41.760><c> I</c><00:00:41.879><c> was</c><00:00:42.039><c> a</c><00:00:42.200><c> little</c><00:00:42.480><c> bit</c>

00:00:42.790 --> 00:00:42.800 align:start position:0%
them are correct so I was a little bit
 

00:00:42.800 --> 00:00:44.990 align:start position:0%
them are correct so I was a little bit
preocupa<00:00:43.520><c> said</c><00:00:43.760><c> hey</c><00:00:44.000><c> okay</c><00:00:44.160><c> I</c><00:00:44.280><c> have</c><00:00:44.399><c> to</c><00:00:44.600><c> check</c>

00:00:44.990 --> 00:00:45.000 align:start position:0%
preocupa said hey okay I have to check
 

00:00:45.000 --> 00:00:46.950 align:start position:0%
preocupa said hey okay I have to check
myself<00:00:45.520><c> because</c><00:00:46.039><c> I</c><00:00:46.120><c> don't</c><00:00:46.280><c> think</c><00:00:46.520><c> that</c><00:00:46.719><c> those</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
myself because I don't think that those
 

00:00:46.960 --> 00:00:50.950 align:start position:0%
myself because I don't think that those
people<00:00:47.559><c> want</c><00:00:48.160><c> to</c><00:00:49.160><c> I</c><00:00:49.239><c> don't</c><00:00:49.399><c> know</c><00:00:49.760><c> lie</c><00:00:50.120><c> to</c><00:00:50.320><c> you</c>

00:00:50.950 --> 00:00:50.960 align:start position:0%
people want to I don't know lie to you
 

00:00:50.960 --> 00:00:53.029 align:start position:0%
people want to I don't know lie to you
but<00:00:51.280><c> maybe</c><00:00:51.600><c> they</c><00:00:51.760><c> need</c><00:00:52.000><c> a</c><00:00:52.160><c> little</c><00:00:52.359><c> bit</c><00:00:52.559><c> of</c><00:00:52.760><c> help</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
but maybe they need a little bit of help
 

00:00:53.039 --> 00:00:55.069 align:start position:0%
but maybe they need a little bit of help
if<00:00:53.160><c> they</c><00:00:53.359><c> least</c><00:00:53.960><c> those</c><00:00:54.280><c> comments</c><00:00:54.640><c> for</c><00:00:54.879><c> the</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
if they least those comments for the
 

00:00:55.079 --> 00:00:58.189 align:start position:0%
if they least those comments for the
community<00:00:56.079><c> so</c><00:00:56.559><c> here</c><00:00:56.760><c> you</c><00:00:56.920><c> see</c><00:00:57.239><c> now</c><00:00:57.480><c> I</c><00:00:57.719><c> checked</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
community so here you see now I checked
 

00:00:58.199 --> 00:01:00.869 align:start position:0%
community so here you see now I checked
here<00:00:58.559><c> and</c><00:00:59.000><c> everything</c><00:00:59.399><c> is</c><00:00:59.600><c> green</c><00:01:00.160><c> beautiful</c>

00:01:00.869 --> 00:01:00.879 align:start position:0%
here and everything is green beautiful
 

00:01:00.879 --> 00:01:03.830 align:start position:0%
here and everything is green beautiful
so<00:01:01.079><c> the</c><00:01:01.239><c> answer</c><00:01:01.600><c> by</c><00:01:01.760><c> 01</c><00:01:02.239><c> preview</c><00:01:02.680><c> was</c><00:01:02.920><c> correct</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
so the answer by 01 preview was correct
 

00:01:03.840 --> 00:01:08.030 align:start position:0%
so the answer by 01 preview was correct
there<00:01:04.040><c> is</c><00:01:04.320><c> only</c><00:01:05.080><c> a</c><00:01:05.720><c> certain</c><00:01:06.759><c> probability</c><00:01:07.759><c> that</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
there is only a certain probability that
 

00:01:08.040 --> 00:01:10.310 align:start position:0%
there is only a certain probability that
you<00:01:08.240><c> can</c><00:01:08.520><c> exchange</c><00:01:09.280><c> those</c><00:01:09.560><c> two</c><00:01:09.960><c> because</c><00:01:10.200><c> this</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
you can exchange those two because this
 

00:01:10.320 --> 00:01:13.630 align:start position:0%
you can exchange those two because this
is<00:01:10.520><c> not</c><00:01:10.799><c> really</c><00:01:11.240><c> defined</c><00:01:12.080><c> here</c><00:01:12.759><c> and</c><00:01:12.920><c> so</c><00:01:13.400><c> if</c><00:01:13.479><c> you</c>

00:01:13.630 --> 00:01:13.640 align:start position:0%
is not really defined here and so if you
 

00:01:13.640 --> 00:01:15.870 align:start position:0%
is not really defined here and so if you
want<00:01:13.840><c> a</c><00:01:14.000><c> single</c><00:01:14.320><c> degree</c><00:01:14.600><c> of</c><00:01:14.720><c> freedom</c><00:01:15.200><c> is</c><00:01:15.680><c> you</c>

00:01:15.870 --> 00:01:15.880 align:start position:0%
want a single degree of freedom is you
 

00:01:15.880 --> 00:01:19.270 align:start position:0%
want a single degree of freedom is you
can<00:01:16.240><c> exchange</c><00:01:16.920><c> these</c><00:01:17.159><c> two</c><00:01:17.600><c> Fields</c><00:01:18.600><c> both</c><00:01:19.040><c> of</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
can exchange these two Fields both of
 

00:01:19.280 --> 00:01:22.030 align:start position:0%
can exchange these two Fields both of
them<00:01:19.600><c> both</c><00:01:19.880><c> configuration</c><00:01:20.680><c> are</c>

00:01:22.030 --> 00:01:22.040 align:start position:0%
them both configuration are
 

00:01:22.040 --> 00:01:24.310 align:start position:0%
them both configuration are
correct<00:01:23.040><c> beautiful</c><00:01:23.560><c> you</c><00:01:23.759><c> remember</c><00:01:24.200><c> I</c>

00:01:24.310 --> 00:01:24.320 align:start position:0%
correct beautiful you remember I
 

00:01:24.320 --> 00:01:26.830 align:start position:0%
correct beautiful you remember I
designed<00:01:24.799><c> this</c><00:01:25.040><c> test</c><00:01:25.439><c> here</c><00:01:25.880><c> to</c><00:01:26.159><c> check</c><00:01:26.520><c> if</c><00:01:26.680><c> we</c>

00:01:26.830 --> 00:01:26.840 align:start position:0%
designed this test here to check if we
 

00:01:26.840 --> 00:01:29.910 align:start position:0%
designed this test here to check if we
can<00:01:27.159><c> find</c><00:01:27.439><c> here</c><00:01:27.799><c> configuration</c><00:01:28.600><c> of</c><00:01:28.759><c> four</c><00:01:29.200><c> Omni</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
can find here configuration of four Omni
 

00:01:29.920 --> 00:01:31.910 align:start position:0%
can find here configuration of four Omni
that<00:01:30.119><c> comes</c><00:01:30.479><c> close</c><00:01:30.880><c> here</c><00:01:31.040><c> to</c><00:01:31.240><c> this</c><00:01:31.439><c> beautiful</c>

00:01:31.910 --> 00:01:31.920 align:start position:0%
that comes close here to this beautiful
 

00:01:31.920 --> 00:01:35.710 align:start position:0%
that comes close here to this beautiful
new<00:01:32.200><c> openi</c><00:01:33.320><c> o1</c><00:01:34.320><c> if</c><00:01:34.520><c> we</c><00:01:34.759><c> have</c><00:01:35.000><c> a</c><00:01:35.280><c> kind</c><00:01:35.479><c> of</c>

00:01:35.710 --> 00:01:35.720 align:start position:0%
new openi o1 if we have a kind of
 

00:01:35.720 --> 00:01:38.469 align:start position:0%
new openi o1 if we have a kind of
cascading<00:01:36.360><c> prompt</c><00:01:37.240><c> so</c><00:01:37.640><c> the</c><00:01:37.880><c> test</c><00:01:38.200><c> was</c>

00:01:38.469 --> 00:01:38.479 align:start position:0%
cascading prompt so the test was
 

00:01:38.479 --> 00:01:40.910 align:start position:0%
cascading prompt so the test was
designed<00:01:39.000><c> to</c><00:01:39.119><c> be</c><00:01:39.320><c> able</c><00:01:39.720><c> exactly</c><00:01:40.200><c> to</c><00:01:40.360><c> be</c><00:01:40.479><c> solved</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
designed to be able exactly to be solved
 

00:01:40.920 --> 00:01:44.630 align:start position:0%
designed to be able exactly to be solved
holistically<00:01:41.720><c> by</c><00:01:41.880><c> 01</c><00:01:42.680><c> or</c><00:01:43.040><c> piece</c><00:01:43.320><c> by</c><00:01:43.600><c> piece</c><00:01:44.119><c> by</c>

00:01:44.630 --> 00:01:44.640 align:start position:0%
holistically by 01 or piece by piece by
 

00:01:44.640 --> 00:01:47.789 align:start position:0%
holistically by 01 or piece by piece by
four<00:01:45.280><c> Omni</c><00:01:46.280><c> now</c><00:01:46.640><c> let's</c><00:01:46.880><c> look</c><00:01:47.040><c> at</c><00:01:47.159><c> the</c><00:01:47.360><c> result</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
four Omni now let's look at the result
 

00:01:47.799 --> 00:01:51.950 align:start position:0%
four Omni now let's look at the result
you<00:01:48.079><c> know</c><00:01:48.799><c> 401</c><00:01:49.799><c> we</c><00:01:50.000><c> do</c><00:01:50.360><c> have</c><00:01:50.640><c> now</c><00:01:51.040><c> a</c><00:01:51.240><c> correct</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
you know 401 we do have now a correct
 

00:01:51.960 --> 00:01:54.749 align:start position:0%
you know 401 we do have now a correct
solution<00:01:52.960><c> beautiful</c><00:01:53.560><c> and</c><00:01:53.759><c> now</c><00:01:54.159><c> let's</c><00:01:54.439><c> have</c><00:01:54.560><c> a</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
solution beautiful and now let's have a
 

00:01:54.759 --> 00:01:58.270 align:start position:0%
solution beautiful and now let's have a
look<00:01:55.000><c> at</c><00:01:55.399><c> four</c><00:01:56.159><c> Omni</c><00:01:57.159><c> is</c><00:01:57.280><c> it</c><00:01:57.560><c> possible</c><00:01:57.920><c> and</c><00:01:58.079><c> you</c>

00:01:58.270 --> 00:01:58.280 align:start position:0%
look at four Omni is it possible and you
 

00:01:58.280 --> 00:02:01.630 align:start position:0%
look at four Omni is it possible and you
remember<00:01:58.719><c> in</c><00:01:58.960><c> step</c><00:01:59.240><c> four</c><00:01:59.439><c> in</c><00:02:00.119><c> video</c><00:02:00.880><c> this</c><00:02:01.240><c> was</c>

00:02:01.630 --> 00:02:01.640 align:start position:0%
remember in step four in video this was
 

00:02:01.640 --> 00:02:06.109 align:start position:0%
remember in step four in video this was
here<00:02:02.600><c> what</c><00:02:03.600><c> GPT</c><00:02:04.280><c> 4</c><00:02:04.759><c> Omni</c><00:02:05.200><c> could</c><00:02:05.360><c> do</c><00:02:05.799><c> and</c><00:02:05.960><c> you</c>

00:02:06.109 --> 00:02:06.119 align:start position:0%
here what GPT 4 Omni could do and you
 

00:02:06.119 --> 00:02:08.469 align:start position:0%
here what GPT 4 Omni could do and you
see<00:02:06.399><c> to</c><00:02:06.640><c> be</c><00:02:06.880><c> decided</c><00:02:07.360><c> there</c><00:02:07.479><c> were</c><00:02:07.680><c> fields</c><00:02:08.319><c> at</c>

00:02:08.469 --> 00:02:08.479 align:start position:0%
see to be decided there were fields at
 

00:02:08.479 --> 00:02:10.510 align:start position:0%
see to be decided there were fields at
the<00:02:08.679><c> step</c><00:02:08.879><c> four</c><00:02:09.200><c> it</c><00:02:09.360><c> was</c><00:02:09.599><c> not</c><00:02:09.879><c> able</c><00:02:10.119><c> to</c>

00:02:10.510 --> 00:02:10.520 align:start position:0%
the step four it was not able to
 

00:02:10.520 --> 00:02:13.630 align:start position:0%
the step four it was not able to
identify<00:02:11.520><c> and</c><00:02:11.800><c> this</c><00:02:12.000><c> is</c><00:02:12.319><c> kind</c><00:02:12.480><c> of</c><00:02:12.680><c> beautiful</c>

00:02:13.630 --> 00:02:13.640 align:start position:0%
identify and this is kind of beautiful
 

00:02:13.640 --> 00:02:17.030 align:start position:0%
identify and this is kind of beautiful
because<00:02:14.040><c> if</c><00:02:14.319><c> I</c><00:02:14.599><c> now</c><00:02:14.920><c> do</c><00:02:15.319><c> this</c><00:02:16.000><c> and</c><00:02:16.120><c> I</c><00:02:16.360><c> just</c><00:02:16.640><c> add</c>

00:02:17.030 --> 00:02:17.040 align:start position:0%
because if I now do this and I just add
 

00:02:17.040 --> 00:02:18.470 align:start position:0%
because if I now do this and I just add
two<00:02:17.319><c> more</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
two more
 

00:02:18.480 --> 00:02:21.670 align:start position:0%
two more
prompts<00:02:19.480><c> I</c><00:02:19.599><c> can</c><00:02:19.800><c> show</c><00:02:20.040><c> you</c><00:02:20.519><c> I</c><00:02:20.720><c> come</c><00:02:20.959><c> up</c><00:02:21.200><c> now</c>

00:02:21.670 --> 00:02:21.680 align:start position:0%
prompts I can show you I come up now
 

00:02:21.680 --> 00:02:26.190 align:start position:0%
prompts I can show you I come up now
with<00:02:22.120><c> four</c><00:02:22.760><c> Omni</c><00:02:23.720><c> with</c><00:02:24.000><c> the</c><00:02:24.239><c> solution</c><00:02:25.040><c> that</c><00:02:25.319><c> o1</c>

00:02:26.190 --> 00:02:26.200 align:start position:0%
with four Omni with the solution that o1
 

00:02:26.200 --> 00:02:30.430 align:start position:0%
with four Omni with the solution that o1
came<00:02:26.440><c> up</c><00:02:26.720><c> with</c><00:02:27.519><c> so</c><00:02:28.519><c> gbd4</c><00:02:29.440><c> Omni</c><00:02:29.959><c> plus</c><00:02:30.239><c> two</c>

00:02:30.430 --> 00:02:30.440 align:start position:0%
came up with so gbd4 Omni plus two
 

00:02:30.440 --> 00:02:33.509 align:start position:0%
came up with so gbd4 Omni plus two
intelligence<00:02:31.080><c> prompt</c><00:02:31.760><c> has</c><00:02:32.040><c> now</c><00:02:32.400><c> the</c><00:02:32.800><c> same</c><00:02:33.400><c> if</c>

00:02:33.509 --> 00:02:33.519 align:start position:0%
intelligence prompt has now the same if
 

00:02:33.519 --> 00:02:36.550 align:start position:0%
intelligence prompt has now the same if
you<00:02:33.640><c> want</c><00:02:34.000><c> causal</c><00:02:34.560><c> reasoning</c><00:02:35.280><c> capacity</c><00:02:36.280><c> like</c>

00:02:36.550 --> 00:02:36.560 align:start position:0%
you want causal reasoning capacity like
 

00:02:36.560 --> 00:02:39.830 align:start position:0%
you want causal reasoning capacity like
o1<00:02:37.319><c> at</c><00:02:37.480><c> least</c><00:02:37.920><c> in</c><00:02:38.200><c> my</c><00:02:38.519><c> test</c><00:02:39.080><c> and</c><00:02:39.239><c> of</c><00:02:39.360><c> course</c><00:02:39.599><c> my</c>

00:02:39.830 --> 00:02:39.840 align:start position:0%
o1 at least in my test and of course my
 

00:02:39.840 --> 00:02:42.990 align:start position:0%
o1 at least in my test and of course my
test<00:02:40.080><c> was</c><00:02:40.319><c> specifically</c><00:02:41.400><c> designed</c><00:02:42.400><c> to</c><00:02:42.800><c> be</c>

00:02:42.990 --> 00:02:43.000 align:start position:0%
test was specifically designed to be
 

00:02:43.000 --> 00:02:46.190 align:start position:0%
test was specifically designed to be
able<00:02:43.239><c> to</c><00:02:43.440><c> perform</c><00:02:44.040><c> this</c><00:02:44.440><c> test</c><00:02:45.080><c> great</c><00:02:45.640><c> so</c><00:02:45.959><c> let's</c>

00:02:46.190 --> 00:02:46.200 align:start position:0%
able to perform this test great so let's
 

00:02:46.200 --> 00:02:50.070 align:start position:0%
able to perform this test great so let's
go<00:02:46.440><c> here</c><00:02:46.599><c> to</c><00:02:46.840><c> my</c><00:02:47.120><c> account</c><00:02:47.480><c> at</c><00:02:47.720><c> GPD</c><00:02:48.239><c> 4</c><00:02:48.760><c> Omni</c><00:02:49.760><c> and</c>

00:02:50.070 --> 00:02:50.080 align:start position:0%
go here to my account at GPD 4 Omni and
 

00:02:50.080 --> 00:02:52.750 align:start position:0%
go here to my account at GPD 4 Omni and
I<00:02:50.280><c> just</c><00:02:50.599><c> put</c><00:02:50.800><c> in</c><00:02:51.120><c> here</c><00:02:51.440><c> my</c><00:02:51.760><c> extreme</c><00:02:52.319><c> test</c><00:02:52.640><c> this</c>

00:02:52.750 --> 00:02:52.760 align:start position:0%
I just put in here my extreme test this
 

00:02:52.760 --> 00:02:55.670 align:start position:0%
I just put in here my extreme test this
is<00:02:52.879><c> a</c><00:02:53.040><c> new</c><00:02:53.280><c> prom</c><00:02:53.680><c> there's</c><00:02:54.000><c> nothing</c><00:02:54.400><c> left</c><00:02:55.239><c> so</c><00:02:55.560><c> as</c>

00:02:55.670 --> 00:02:55.680 align:start position:0%
is a new prom there's nothing left so as
 

00:02:55.680 --> 00:02:58.589 align:start position:0%
is a new prom there's nothing left so as
you<00:02:55.840><c> see</c><00:02:56.080><c> just</c><00:02:56.280><c> is</c><00:02:56.760><c> simple</c><00:02:57.200><c> here</c><00:02:57.519><c> my</c><00:02:57.800><c> test</c><00:02:58.360><c> and</c>

00:02:58.589 --> 00:02:58.599 align:start position:0%
you see just is simple here my test and
 

00:02:58.599 --> 00:03:02.750 align:start position:0%
you see just is simple here my test and
then<00:02:59.000><c> for</c><00:02:59.400><c> Omni</c><00:02:59.920><c> Starts</c><00:03:00.440><c> Here</c><00:03:01.000><c> step</c><00:03:01.360><c> one</c><00:03:02.120><c> aru</c>

00:03:02.750 --> 00:03:02.760 align:start position:0%
then for Omni Starts Here step one aru
 

00:03:02.760 --> 00:03:06.229 align:start position:0%
then for Omni Starts Here step one aru
step<00:03:03.000><c> two</c><00:03:03.959><c> step</c><00:03:04.280><c> three</c><00:03:05.280><c> and</c><00:03:05.480><c> here</c><00:03:05.720><c> this</c><00:03:05.879><c> is</c><00:03:06.120><c> as</c>

00:03:06.229 --> 00:03:06.239 align:start position:0%
step two step three and here this is as
 

00:03:06.239 --> 00:03:08.190 align:start position:0%
step two step three and here this is as
I<00:03:06.360><c> showed</c><00:03:06.599><c> you</c><00:03:06.799><c> step</c><00:03:07.040><c> four</c><00:03:07.560><c> and</c><00:03:07.799><c> you</c><00:03:07.879><c> know</c><00:03:08.120><c> if</c>

00:03:08.190 --> 00:03:08.200 align:start position:0%
I showed you step four and you know if
 

00:03:08.200 --> 00:03:10.470 align:start position:0%
I showed you step four and you know if
you<00:03:08.440><c> compare</c><00:03:08.920><c> this</c><00:03:09.159><c> now</c><00:03:09.360><c> to</c><00:03:09.599><c> Sonet</c><00:03:10.280><c> who</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
you compare this now to Sonet who
 

00:03:10.480 --> 00:03:13.750 align:start position:0%
you compare this now to Sonet who
already<00:03:10.840><c> filled</c><00:03:11.239><c> out</c><00:03:11.560><c> completely</c><00:03:12.159><c> this</c><00:03:13.120><c> here</c>

00:03:13.750 --> 00:03:13.760 align:start position:0%
already filled out completely this here
 

00:03:13.760 --> 00:03:16.430 align:start position:0%
already filled out completely this here
for<00:03:14.200><c> Omni</c><00:03:14.680><c> says</c><00:03:15.040><c> Hey</c><00:03:15.200><c> to</c><00:03:15.360><c> be</c><00:03:15.519><c> decided</c><00:03:16.120><c> I</c><00:03:16.200><c> do</c>

00:03:16.430 --> 00:03:16.440 align:start position:0%
for Omni says Hey to be decided I do
 

00:03:16.440 --> 00:03:19.309 align:start position:0%
for Omni says Hey to be decided I do
know<00:03:16.680><c> about</c><00:03:17.000><c> this</c><00:03:17.280><c> particular</c><00:03:17.799><c> field</c><00:03:18.519><c> and</c><00:03:18.799><c> I</c>

00:03:19.309 --> 00:03:19.319 align:start position:0%
know about this particular field and I
 

00:03:19.319 --> 00:03:22.390 align:start position:0%
know about this particular field and I
like<00:03:19.840><c> this</c><00:03:20.120><c> approach</c><00:03:20.959><c> if</c><00:03:21.159><c> an</c><00:03:21.319><c> llm</c><00:03:21.840><c> tells</c><00:03:22.120><c> me</c>

00:03:22.390 --> 00:03:22.400 align:start position:0%
like this approach if an llm tells me
 

00:03:22.400 --> 00:03:25.830 align:start position:0%
like this approach if an llm tells me
hey<00:03:22.959><c> I'm</c><00:03:23.159><c> not</c><00:03:23.360><c> sure</c><00:03:23.799><c> about</c><00:03:24.440><c> this</c><00:03:25.440><c> what</c><00:03:25.560><c> I</c><00:03:25.680><c> would</c>

00:03:25.830 --> 00:03:25.840 align:start position:0%
hey I'm not sure about this what I would
 

00:03:25.840 --> 00:03:27.990 align:start position:0%
hey I'm not sure about this what I would
like<00:03:26.040><c> to</c><00:03:26.239><c> see</c><00:03:26.879><c> if</c><00:03:27.080><c> this</c><00:03:27.239><c> field</c><00:03:27.640><c> here</c><00:03:27.760><c> in</c><00:03:27.840><c> the</c>

00:03:27.990 --> 00:03:28.000 align:start position:0%
like to see if this field here in the
 

00:03:28.000 --> 00:03:29.869 align:start position:0%
like to see if this field here in the
background<00:03:28.400><c> is</c><00:03:28.519><c> a</c><00:03:28.680><c> little</c><00:03:28.879><c> bit</c><00:03:29.080><c> green</c><00:03:29.439><c> or</c><00:03:29.599><c> if</c>

00:03:29.869 --> 00:03:29.879 align:start position:0%
background is a little bit green or if
 

00:03:29.879 --> 00:03:32.030 align:start position:0%
background is a little bit green or if
this<00:03:30.000><c> field</c><00:03:30.400><c> is</c><00:03:30.560><c> a</c><00:03:30.680><c> little</c><00:03:30.840><c> bit</c><00:03:31.000><c> more</c><00:03:31.280><c> red</c>

00:03:32.030 --> 00:03:32.040 align:start position:0%
this field is a little bit more red
 

00:03:32.040 --> 00:03:35.229 align:start position:0%
this field is a little bit more red
indicating<00:03:32.799><c> the</c><00:03:33.360><c> uncertainty</c><00:03:34.360><c> level</c><00:03:35.080><c> is</c>

00:03:35.229 --> 00:03:35.239 align:start position:0%
indicating the uncertainty level is
 

00:03:35.239 --> 00:03:37.550 align:start position:0%
indicating the uncertainty level is
there<00:03:35.400><c> a</c><00:03:35.560><c> threshold</c><00:03:36.040><c> at</c><00:03:36.239><c> 70%</c><00:03:37.040><c> when</c><00:03:37.159><c> the</c><00:03:37.280><c> field</c>

00:03:37.550 --> 00:03:37.560 align:start position:0%
there a threshold at 70% when the field
 

00:03:37.560 --> 00:03:39.869 align:start position:0%
there a threshold at 70% when the field
finally<00:03:38.000><c> becomes</c><00:03:38.360><c> green</c><00:03:39.200><c> I</c><00:03:39.280><c> think</c><00:03:39.519><c> this</c><00:03:39.640><c> would</c>

00:03:39.869 --> 00:03:39.879 align:start position:0%
finally becomes green I think this would
 

00:03:39.879 --> 00:03:42.990 align:start position:0%
finally becomes green I think this would
help<00:03:40.239><c> me</c><00:03:40.519><c> a</c><00:03:40.760><c> lot</c><00:03:41.080><c> of</c><00:03:41.840><c> so</c><00:03:42.080><c> I</c><00:03:42.159><c> will</c><00:03:42.400><c> try</c><00:03:42.720><c> here</c>

00:03:42.990 --> 00:03:43.000 align:start position:0%
help me a lot of so I will try here
 

00:03:43.000 --> 00:03:45.350 align:start position:0%
help me a lot of so I will try here
maybe<00:03:43.200><c> in</c><00:03:43.319><c> my</c><00:03:43.480><c> next</c><00:03:43.760><c> video</c><00:03:44.400><c> to</c><00:03:44.799><c> have</c><00:03:45.040><c> here</c><00:03:45.200><c> a</c>

00:03:45.350 --> 00:03:45.360 align:start position:0%
maybe in my next video to have here a
 

00:03:45.360 --> 00:03:48.309 align:start position:0%
maybe in my next video to have here a
color<00:03:45.760><c> encoded</c><00:03:46.640><c> about</c><00:03:47.040><c> here</c><00:03:47.280><c> the</c><00:03:47.680><c> degree</c><00:03:48.040><c> of</c>

00:03:48.309 --> 00:03:48.319 align:start position:0%
color encoded about here the degree of
 

00:03:48.319 --> 00:03:51.110 align:start position:0%
color encoded about here the degree of
certainty<00:03:48.959><c> of</c><00:03:49.200><c> our</c><00:03:49.519><c> EI</c><00:03:50.000><c> system</c><00:03:50.680><c> about</c><00:03:50.959><c> the</c>

00:03:51.110 --> 00:03:51.120 align:start position:0%
certainty of our EI system about the
 

00:03:51.120 --> 00:03:53.149 align:start position:0%
certainty of our EI system about the
result<00:03:51.519><c> this</c><00:03:51.680><c> would</c><00:03:51.840><c> help</c><00:03:52.159><c> me</c><00:03:52.840><c> understand</c>

00:03:53.149 --> 00:03:53.159 align:start position:0%
result this would help me understand
 

00:03:53.159 --> 00:03:56.149 align:start position:0%
result this would help me understand
here<00:03:53.599><c> the</c><00:03:53.840><c> performance</c><00:03:54.239><c> of</c><00:03:54.360><c> this</c><00:03:54.599><c> Ai</c><00:03:55.400><c> and</c><00:03:55.599><c> then</c>

00:03:56.149 --> 00:03:56.159 align:start position:0%
here the performance of this Ai and then
 

00:03:56.159 --> 00:03:58.710 align:start position:0%
here the performance of this Ai and then
here<00:03:56.400><c> at</c><00:03:56.480><c> the</c><00:03:56.599><c> end</c><00:03:56.799><c> the</c><00:03:56.920><c> first</c><00:03:57.239><c> end</c><00:03:57.840><c> says</c><00:03:58.480><c> this</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
here at the end the first end says this
 

00:03:58.720 --> 00:04:01.030 align:start position:0%
here at the end the first end says this
analysis<00:03:59.200><c> framework</c><00:03:59.799><c> needs</c><00:04:00.200><c> continued</c>

00:04:01.030 --> 00:04:01.040 align:start position:0%
analysis framework needs continued
 

00:04:01.040 --> 00:04:02.750 align:start position:0%
analysis framework needs continued
refinement<00:04:02.040><c> and</c><00:04:02.159><c> you're</c><00:04:02.319><c> not</c><00:04:02.480><c> going</c><00:04:02.599><c> to</c>

00:04:02.750 --> 00:04:02.760 align:start position:0%
refinement and you're not going to
 

00:04:02.760 --> 00:04:05.589 align:start position:0%
refinement and you're not going to
believe<00:04:03.040><c> it</c><00:04:03.480><c> my</c><00:04:03.920><c> first</c><00:04:04.319><c> cascading</c><00:04:04.879><c> prompt</c><00:04:05.239><c> was</c>

00:04:05.589 --> 00:04:05.599 align:start position:0%
believe it my first cascading prompt was
 

00:04:05.599 --> 00:04:08.750 align:start position:0%
believe it my first cascading prompt was
simply<00:04:05.959><c> to</c><00:04:06.159><c> say</c><00:04:07.159><c> continue</c><00:04:08.159><c> with</c><00:04:08.360><c> further</c>

00:04:08.750 --> 00:04:08.760 align:start position:0%
simply to say continue with further
 

00:04:08.760 --> 00:04:10.869 align:start position:0%
simply to say continue with further
refinement<00:04:09.280><c> so</c><00:04:09.560><c> exactly</c><00:04:10.159><c> what</c><00:04:10.280><c> it</c><00:04:10.439><c> gave</c><00:04:10.680><c> me</c>

00:04:10.869 --> 00:04:10.879 align:start position:0%
refinement so exactly what it gave me
 

00:04:10.879 --> 00:04:13.350 align:start position:0%
refinement so exactly what it gave me
here<00:04:11.799><c> and</c><00:04:11.920><c> then</c><00:04:12.079><c> it</c><00:04:12.319><c> goes</c><00:04:12.519><c> on</c><00:04:12.840><c> we</c><00:04:12.959><c> have</c><00:04:13.120><c> now</c>

00:04:13.350 --> 00:04:13.360 align:start position:0%
here and then it goes on we have now
 

00:04:13.360 --> 00:04:16.310 align:start position:0%
here and then it goes on we have now
step<00:04:13.720><c> five</c><00:04:14.560><c> goes</c><00:04:14.840><c> all</c><00:04:15.079><c> the</c><00:04:15.239><c> clues</c><00:04:15.760><c> beautiful</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
step five goes all the clues beautiful
 

00:04:16.320 --> 00:04:20.030 align:start position:0%
step five goes all the clues beautiful
step<00:04:16.759><c> six</c><00:04:17.759><c> step</c><00:04:18.160><c> seven</c><00:04:19.079><c> and</c><00:04:19.239><c> then</c><00:04:19.400><c> we</c><00:04:19.560><c> got</c><00:04:19.720><c> now</c>

00:04:20.030 --> 00:04:20.040 align:start position:0%
step six step seven and then we got now
 

00:04:20.040 --> 00:04:22.270 align:start position:0%
step six step seven and then we got now
this<00:04:20.359><c> table</c><00:04:20.759><c> and</c><00:04:20.880><c> you</c><00:04:21.040><c> see</c><00:04:21.479><c> we</c><00:04:21.600><c> have</c><00:04:21.759><c> still</c><00:04:22.040><c> to</c>

00:04:22.270 --> 00:04:22.280 align:start position:0%
this table and you see we have still to
 

00:04:22.280 --> 00:04:25.870 align:start position:0%
this table and you see we have still to
be<00:04:22.560><c> decided</c><00:04:23.080><c> the</c><00:04:23.240><c> complexity</c><00:04:23.880><c> is</c><00:04:24.080><c> still</c><00:04:24.880><c> there</c>

00:04:25.870 --> 00:04:25.880 align:start position:0%
be decided the complexity is still there
 

00:04:25.880 --> 00:04:28.150 align:start position:0%
be decided the complexity is still there
and<00:04:26.199><c> I</c><00:04:26.320><c> said</c><00:04:26.600><c> no</c><00:04:26.880><c> problem</c><00:04:27.280><c> I</c><00:04:27.400><c> said</c><00:04:27.800><c> hey</c>

00:04:28.150 --> 00:04:28.160 align:start position:0%
and I said no problem I said hey
 

00:04:28.160 --> 00:04:31.990 align:start position:0%
and I said no problem I said hey
continue<00:04:28.720><c> to</c><00:04:28.960><c> solve</c><00:04:29.400><c> this</c><00:04:29.759><c> logic</c><00:04:30.440><c> test</c><00:04:31.440><c> so</c><00:04:31.800><c> you</c>

00:04:31.990 --> 00:04:32.000 align:start position:0%
continue to solve this logic test so you
 

00:04:32.000 --> 00:04:36.029 align:start position:0%
continue to solve this logic test so you
ask<00:04:32.240><c> me</c><00:04:32.520><c> hey</c><00:04:32.840><c> what</c><00:04:33.199><c> secret</c><00:04:33.880><c> prompt</c><00:04:34.479><c> is</c><00:04:35.039><c> this</c>

00:04:36.029 --> 00:04:36.039 align:start position:0%
ask me hey what secret prompt is this
 

00:04:36.039 --> 00:04:39.150 align:start position:0%
ask me hey what secret prompt is this
I'm<00:04:36.280><c> a</c><00:04:36.479><c> simple</c><00:04:36.880><c> person</c><00:04:37.400><c> I</c><00:04:37.560><c> just</c><00:04:37.840><c> say</c><00:04:38.400><c> continue</c>

00:04:39.150 --> 00:04:39.160 align:start position:0%
I'm a simple person I just say continue
 

00:04:39.160 --> 00:04:41.950 align:start position:0%
I'm a simple person I just say continue
continue<00:04:39.680><c> to</c><00:04:39.880><c> solve</c><00:04:40.320><c> this</c><00:04:40.560><c> test</c><00:04:41.320><c> and</c><00:04:41.520><c> here</c><00:04:41.800><c> you</c>

00:04:41.950 --> 00:04:41.960 align:start position:0%
continue to solve this test and here you
 

00:04:41.960 --> 00:04:43.110 align:start position:0%
continue to solve this test and here you
see<00:04:42.320><c> the</c>

00:04:43.110 --> 00:04:43.120 align:start position:0%
see the
 

00:04:43.120 --> 00:04:45.710 align:start position:0%
see the
result<00:04:44.120><c> recap</c><00:04:44.600><c> of</c><00:04:44.759><c> known</c><00:04:45.080><c> information</c>

00:04:45.710 --> 00:04:45.720 align:start position:0%
result recap of known information
 

00:04:45.720 --> 00:04:49.150 align:start position:0%
result recap of known information
beautiful<00:04:46.400><c> step</c><00:04:46.800><c> nine</c><00:04:47.280><c> so</c><00:04:47.440><c> it</c><00:04:47.680><c> continues</c><00:04:48.400><c> now</c>

00:04:49.150 --> 00:04:49.160 align:start position:0%
beautiful step nine so it continues now
 

00:04:49.160 --> 00:04:51.909 align:start position:0%
beautiful step nine so it continues now
narrowing<00:04:49.720><c> down</c><00:04:49.960><c> the</c><00:04:50.199><c> assignment</c><00:04:50.960><c> yes</c>

00:04:51.909 --> 00:04:51.919 align:start position:0%
narrowing down the assignment yes
 

00:04:51.919 --> 00:04:54.390 align:start position:0%
narrowing down the assignment yes
beautiful<00:04:52.919><c> yes</c><00:04:53.199><c> yes</c><00:04:53.479><c> yes</c><00:04:53.759><c> and</c><00:04:53.919><c> here</c><00:04:54.080><c> we</c><00:04:54.240><c> have</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
beautiful yes yes yes and here we have
 

00:04:54.400 --> 00:04:57.230 align:start position:0%
beautiful yes yes yes and here we have
now<00:04:54.560><c> the</c><00:04:54.759><c> final</c><00:04:55.120><c> solution</c><00:04:56.120><c> after</c><00:04:56.560><c> two</c>

00:04:57.230 --> 00:04:57.240 align:start position:0%
now the final solution after two
 

00:04:57.240 --> 00:05:00.270 align:start position:0%
now the final solution after two
additional<00:04:58.120><c> prompts</c><00:04:58.639><c> from</c><00:04:58.880><c> my</c><00:04:59.160><c> side</c>

00:05:00.270 --> 00:05:00.280 align:start position:0%
additional prompts from my side
 

00:05:00.280 --> 00:05:03.110 align:start position:0%
additional prompts from my side
and<00:05:00.800><c> I</c><00:05:01.000><c> showed</c><00:05:01.320><c> you</c><00:05:01.600><c> if</c><00:05:01.720><c> you</c><00:05:02.000><c> compare</c><00:05:02.520><c> this</c><00:05:02.840><c> now</c>

00:05:03.110 --> 00:05:03.120 align:start position:0%
and I showed you if you compare this now
 

00:05:03.120 --> 00:05:06.950 align:start position:0%
and I showed you if you compare this now
to<00:05:03.479><c> the</c><00:05:03.680><c> correct</c><00:05:04.039><c> Solution</c><00:05:04.720><c> by</c><00:05:05.280><c> openi</c>

00:05:06.950 --> 00:05:06.960 align:start position:0%
to the correct Solution by openi
 

00:05:06.960 --> 00:05:09.790 align:start position:0%
to the correct Solution by openi
o1<00:05:07.960><c> with</c><00:05:08.280><c> the</c><00:05:08.520><c> exception</c><00:05:09.000><c> that</c><00:05:09.240><c> these</c><00:05:09.479><c> two</c>

00:05:09.790 --> 00:05:09.800 align:start position:0%
o1 with the exception that these two
 

00:05:09.800 --> 00:05:12.749 align:start position:0%
o1 with the exception that these two
fields<00:05:10.240><c> are</c><00:05:10.400><c> not</c><00:05:10.680><c> determined</c><00:05:11.440><c> exactly</c><00:05:12.440><c> so</c>

00:05:12.749 --> 00:05:12.759 align:start position:0%
fields are not determined exactly so
 

00:05:12.759 --> 00:05:14.430 align:start position:0%
fields are not determined exactly so
there's<00:05:13.120><c> yet</c><00:05:13.360><c> this</c><00:05:13.560><c> very</c><00:05:13.800><c> little</c><00:05:14.039><c> degree</c><00:05:14.320><c> of</c>

00:05:14.430 --> 00:05:14.440 align:start position:0%
there's yet this very little degree of
 

00:05:14.440 --> 00:05:16.430 align:start position:0%
there's yet this very little degree of
Freedom<00:05:14.840><c> that</c><00:05:14.960><c> you</c><00:05:15.080><c> can</c><00:05:15.240><c> exchange</c><00:05:15.840><c> those</c><00:05:16.120><c> two</c>

00:05:16.430 --> 00:05:16.440 align:start position:0%
Freedom that you can exchange those two
 

00:05:16.440 --> 00:05:19.430 align:start position:0%
Freedom that you can exchange those two
Fields<00:05:17.440><c> this</c><00:05:17.600><c> is</c><00:05:17.919><c> the</c><00:05:18.160><c> correct</c>

00:05:19.430 --> 00:05:19.440 align:start position:0%
Fields this is the correct
 

00:05:19.440 --> 00:05:22.230 align:start position:0%
Fields this is the correct
solution<00:05:20.440><c> Step</c><00:05:20.720><c> 11</c><00:05:21.080><c> was</c><00:05:21.360><c> explanation</c><00:05:21.919><c> of</c><00:05:22.080><c> the</c>

00:05:22.230 --> 00:05:22.240 align:start position:0%
solution Step 11 was explanation of the
 

00:05:22.240 --> 00:05:25.070 align:start position:0%
solution Step 11 was explanation of the
logic<00:05:23.199><c> just</c><00:05:23.440><c> makes</c><00:05:23.800><c> a</c><00:05:23.960><c> check</c><00:05:24.520><c> and</c><00:05:24.680><c> says</c><00:05:24.960><c> this</c>

00:05:25.070 --> 00:05:25.080 align:start position:0%
logic just makes a check and says this
 

00:05:25.080 --> 00:05:27.909 align:start position:0%
logic just makes a check and says this
solution<00:05:25.520><c> satisfies</c><00:05:26.199><c> all</c><00:05:26.479><c> Clues</c>

00:05:27.909 --> 00:05:27.919 align:start position:0%
solution satisfies all Clues
 

00:05:27.919 --> 00:05:29.909 align:start position:0%
solution satisfies all Clues
logically<00:05:28.919><c> and</c><00:05:29.039><c> then</c><00:05:29.199><c> I</c><00:05:29.280><c> said</c><00:05:29.479><c> okay</c><00:05:29.759><c> okay</c>

00:05:29.909 --> 00:05:29.919 align:start position:0%
logically and then I said okay okay
 

00:05:29.919 --> 00:05:31.590 align:start position:0%
logically and then I said okay okay
great<00:05:30.199><c> but</c><00:05:30.360><c> now</c><00:05:30.520><c> if</c><00:05:30.600><c> you</c><00:05:30.759><c> exchange</c><00:05:31.240><c> here</c>

00:05:31.590 --> 00:05:31.600 align:start position:0%
great but now if you exchange here
 

00:05:31.600 --> 00:05:33.110 align:start position:0%
great but now if you exchange here
exactly<00:05:32.039><c> what</c><00:05:32.160><c> I</c><00:05:32.319><c> just</c><00:05:32.440><c> showed</c><00:05:32.720><c> you</c><00:05:32.840><c> here</c><00:05:33.000><c> this</c>

00:05:33.110 --> 00:05:33.120 align:start position:0%
exactly what I just showed you here this
 

00:05:33.120 --> 00:05:34.230 align:start position:0%
exactly what I just showed you here this
little<00:05:33.319><c> degree</c><00:05:33.600><c> of</c>

00:05:34.230 --> 00:05:34.240 align:start position:0%
little degree of
 

00:05:34.240 --> 00:05:38.029 align:start position:0%
little degree of
freedom<00:05:35.240><c> and</c><00:05:35.520><c> here</c><00:05:35.880><c> this</c><00:05:36.800><c> Omni</c><00:05:37.319><c> says</c><00:05:37.800><c> okay</c>

00:05:38.029 --> 00:05:38.039 align:start position:0%
freedom and here this Omni says okay
 

00:05:38.039 --> 00:05:41.189 align:start position:0%
freedom and here this Omni says okay
let's<00:05:38.319><c> investigate</c><00:05:39.160><c> this</c><00:05:40.160><c> beautiful</c><00:05:40.960><c> if</c><00:05:41.080><c> you</c>

00:05:41.189 --> 00:05:41.199 align:start position:0%
let's investigate this beautiful if you
 

00:05:41.199 --> 00:05:43.510 align:start position:0%
let's investigate this beautiful if you
want<00:05:41.360><c> to</c><00:05:41.520><c> see</c><00:05:41.759><c> we</c><00:05:41.919><c> can</c><00:05:42.240><c> swap</c><00:05:42.759><c> here</c><00:05:43.039><c> exactly</c>

00:05:43.510 --> 00:05:43.520 align:start position:0%
want to see we can swap here exactly
 

00:05:43.520 --> 00:05:46.790 align:start position:0%
want to see we can swap here exactly
this<00:05:43.680><c> little</c><00:05:44.240><c> Freedom</c><00:05:45.240><c> impact</c><00:05:45.720><c> of</c><00:05:45.880><c> the</c><00:05:46.039><c> swap</c>

00:05:46.790 --> 00:05:46.800 align:start position:0%
this little Freedom impact of the swap
 

00:05:46.800 --> 00:05:49.749 align:start position:0%
this little Freedom impact of the swap
analyzes<00:05:47.520><c> this</c><00:05:47.960><c> has</c><00:05:48.199><c> its</c><00:05:48.520><c> argumentation</c><00:05:49.520><c> has</c>

00:05:49.749 --> 00:05:49.759 align:start position:0%
analyzes this has its argumentation has
 

00:05:49.759 --> 00:05:53.510 align:start position:0%
analyzes this has its argumentation has
15<00:05:50.319><c> Steps</c><00:05:51.199><c> goes</c><00:05:51.520><c> further</c><00:05:51.919><c> on</c><00:05:52.360><c> conclusion</c><00:05:53.199><c> yes</c>

00:05:53.510 --> 00:05:53.520 align:start position:0%
15 Steps goes further on conclusion yes
 

00:05:53.520 --> 00:05:56.430 align:start position:0%
15 Steps goes further on conclusion yes
a<00:05:53.759><c> solution</c><00:05:54.199><c> where</c><00:05:54.400><c> the</c><00:05:54.560><c> swap</c><00:05:54.919><c> is</c><00:05:55.039><c> done</c><00:05:55.919><c> is</c><00:05:56.199><c> a</c>

00:05:56.430 --> 00:05:56.440 align:start position:0%
a solution where the swap is done is a
 

00:05:56.440 --> 00:05:58.909 align:start position:0%
a solution where the swap is done is a
valid<00:05:56.800><c> solution</c><00:05:57.160><c> to</c><00:05:57.319><c> our</c><00:05:57.520><c> logical</c><00:05:57.880><c> test</c><00:05:58.639><c> both</c>

00:05:58.909 --> 00:05:58.919 align:start position:0%
valid solution to our logical test both
 

00:05:58.919 --> 00:06:00.430 align:start position:0%
valid solution to our logical test both
configurations<00:05:59.479><c> have</c><00:05:59.639><c> satisfy</c><00:06:00.000><c> all</c><00:06:00.199><c> given</c>

00:06:00.430 --> 00:06:00.440 align:start position:0%
configurations have satisfy all given
 

00:06:00.440 --> 00:06:03.430 align:start position:0%
configurations have satisfy all given
Clues<00:06:00.880><c> without</c><00:06:01.560><c> contradiction</c><00:06:02.560><c> so</c><00:06:02.800><c> it</c>

00:06:03.430 --> 00:06:03.440 align:start position:0%
Clues without contradiction so it
 

00:06:03.440 --> 00:06:05.950 align:start position:0%
Clues without contradiction so it
understands<00:06:04.199><c> there</c><00:06:04.400><c> is</c><00:06:04.680><c> another</c><00:06:05.080><c> solution</c>

00:06:05.950 --> 00:06:05.960 align:start position:0%
understands there is another solution
 

00:06:05.960 --> 00:06:08.469 align:start position:0%
understands there is another solution
beautiful<00:06:06.960><c> and</c><00:06:07.160><c> now</c><00:06:07.400><c> I</c><00:06:07.599><c> tried</c><00:06:08.039><c> something</c><00:06:08.360><c> that</c>

00:06:08.469 --> 00:06:08.479 align:start position:0%
beautiful and now I tried something that
 

00:06:08.479 --> 00:06:09.830 align:start position:0%
beautiful and now I tried something that
is<00:06:08.599><c> out</c><00:06:08.800><c> of</c><00:06:08.919><c> scope</c><00:06:09.199><c> here</c><00:06:09.319><c> after</c><00:06:09.479><c> this</c><00:06:09.639><c> video</c>

00:06:09.830 --> 00:06:09.840 align:start position:0%
is out of scope here after this video
 

00:06:09.840 --> 00:06:11.870 align:start position:0%
is out of scope here after this video
and<00:06:09.919><c> I</c><00:06:10.039><c> said</c><00:06:10.440><c> hey</c><00:06:10.639><c> are</c><00:06:10.800><c> there</c><00:06:11.039><c> any</c><00:06:11.280><c> other</c>

00:06:11.870 --> 00:06:11.880 align:start position:0%
and I said hey are there any other
 

00:06:11.880 --> 00:06:13.469 align:start position:0%
and I said hey are there any other
alternation<00:06:12.479><c> of</c><00:06:12.639><c> the</c><00:06:12.759><c> element</c><00:06:13.160><c> that</c><00:06:13.280><c> would</c>

00:06:13.469 --> 00:06:13.479 align:start position:0%
alternation of the element that would
 

00:06:13.479 --> 00:06:16.270 align:start position:0%
alternation of the element that would
also<00:06:13.720><c> generate</c><00:06:14.319><c> a</c><00:06:14.599><c> correct</c>

00:06:16.270 --> 00:06:16.280 align:start position:0%
also generate a correct
 

00:06:16.280 --> 00:06:19.589 align:start position:0%
also generate a correct
solution<00:06:17.280><c> and</c><00:06:17.400><c> I</c><00:06:17.599><c> give</c><00:06:17.720><c> him</c><00:06:18.280><c> a</c><00:06:18.520><c> hint</c><00:06:18.880><c> how</c><00:06:19.039><c> to</c><00:06:19.280><c> do</c>

00:06:19.589 --> 00:06:19.599 align:start position:0%
solution and I give him a hint how to do
 

00:06:19.599 --> 00:06:21.189 align:start position:0%
solution and I give him a hint how to do
this<00:06:19.759><c> I</c><00:06:19.919><c> say</c><00:06:20.160><c> start</c><00:06:20.400><c> from</c><00:06:20.599><c> your</c><00:06:20.840><c> correct</c>

00:06:21.189 --> 00:06:21.199 align:start position:0%
this I say start from your correct
 

00:06:21.199 --> 00:06:24.430 align:start position:0%
this I say start from your correct
solution<00:06:22.160><c> and</c><00:06:22.360><c> then</c><00:06:22.680><c> exchange</c><00:06:23.360><c> pce</c><00:06:23.880><c> by</c><00:06:24.199><c> piece</c>

00:06:24.430 --> 00:06:24.440 align:start position:0%
solution and then exchange pce by piece
 

00:06:24.440 --> 00:06:26.629 align:start position:0%
solution and then exchange pce by piece
of<00:06:24.599><c> your</c><00:06:24.759><c> Matrix</c><00:06:25.240><c> to</c><00:06:25.479><c> investigate</c><00:06:26.199><c> the</c>

00:06:26.629 --> 00:06:26.639 align:start position:0%
of your Matrix to investigate the
 

00:06:26.639 --> 00:06:29.629 align:start position:0%
of your Matrix to investigate the
effects<00:06:27.639><c> so</c><00:06:27.880><c> you</c><00:06:28.000><c> see</c><00:06:28.520><c> you</c><00:06:28.759><c> can</c><00:06:29.000><c> go</c><00:06:29.160><c> on</c><00:06:29.360><c> and</c>

00:06:29.629 --> 00:06:29.639 align:start position:0%
effects so you see you can go on and
 

00:06:29.639 --> 00:06:31.790 align:start position:0%
effects so you see you can go on and
then<00:06:29.960><c> analyze</c><00:06:30.560><c> further</c><00:06:31.039><c> but</c><00:06:31.199><c> I</c><00:06:31.360><c> just</c><00:06:31.520><c> wanted</c>

00:06:31.790 --> 00:06:31.800 align:start position:0%
then analyze further but I just wanted
 

00:06:31.800 --> 00:06:34.670 align:start position:0%
then analyze further but I just wanted
to<00:06:32.000><c> show</c><00:06:32.199><c> you</c><00:06:32.800><c> with</c><00:06:33.080><c> two</c><00:06:33.599><c> additional</c><00:06:34.280><c> more</c><00:06:34.479><c> or</c>

00:06:34.670 --> 00:06:34.680 align:start position:0%
to show you with two additional more or
 

00:06:34.680 --> 00:06:38.670 align:start position:0%
to show you with two additional more or
less<00:06:34.960><c> just</c><00:06:35.319><c> continue</c><00:06:35.960><c> your</c><00:06:36.280><c> task</c><00:06:37.360><c> prompts</c><00:06:38.360><c> J</c>

00:06:38.670 --> 00:06:38.680 align:start position:0%
less just continue your task prompts J
 

00:06:38.680 --> 00:06:43.070 align:start position:0%
less just continue your task prompts J
GPT<00:06:39.240><c> F</c><00:06:39.599><c> Omni</c><00:06:40.199><c> on</c><00:06:40.639><c> my</c><00:06:41.240><c> personal</c><00:06:42.120><c> extreme</c><00:06:42.639><c> logic</c>

00:06:43.070 --> 00:06:43.080 align:start position:0%
GPT F Omni on my personal extreme logic
 

00:06:43.080 --> 00:06:45.309 align:start position:0%
GPT F Omni on my personal extreme logic
test<00:06:43.800><c> was</c><00:06:44.160><c> able</c><00:06:44.440><c> to</c><00:06:44.680><c> come</c><00:06:44.880><c> up</c><00:06:45.039><c> with</c><00:06:45.199><c> the</c>

00:06:45.309 --> 00:06:45.319 align:start position:0%
test was able to come up with the
 

00:06:45.319 --> 00:06:47.550 align:start position:0%
test was able to come up with the
correct<00:06:45.680><c> solution</c><00:06:46.120><c> that</c><00:06:46.240><c> is</c><00:06:46.680><c> almost</c>

00:06:47.550 --> 00:06:47.560 align:start position:0%
correct solution that is almost
 

00:06:47.560 --> 00:06:49.990 align:start position:0%
correct solution that is almost
identical<00:06:48.560><c> depending</c><00:06:48.919><c> on</c><00:06:49.199><c> one</c><00:06:49.440><c> little</c><00:06:49.720><c> degree</c>

00:06:49.990 --> 00:06:50.000 align:start position:0%
identical depending on one little degree
 

00:06:50.000 --> 00:06:54.629 align:start position:0%
identical depending on one little degree
of<00:06:50.120><c> Freedom</c><00:06:51.080><c> with</c><00:06:51.400><c> the</c><00:06:51.639><c> solution</c><00:06:52.240><c> by</c><00:06:52.680><c> openi</c>

00:06:54.629 --> 00:06:54.639 align:start position:0%
of Freedom with the solution by openi
 

00:06:54.639 --> 00:06:57.629 align:start position:0%
of Freedom with the solution by openi
o1<00:06:55.639><c> now</c><00:06:55.800><c> you</c><00:06:55.960><c> say</c><00:06:56.280><c> hey</c><00:06:56.479><c> what</c><00:06:56.599><c> about</c><00:06:56.840><c> Gro</c><00:06:57.160><c> 2</c><00:06:57.479><c> what</c>

00:06:57.629 --> 00:06:57.639 align:start position:0%
o1 now you say hey what about Gro 2 what
 

00:06:57.639 --> 00:06:59.749 align:start position:0%
o1 now you say hey what about Gro 2 what
about<00:06:57.919><c> Sonet</c><00:06:58.440><c> what</c><00:06:58.560><c> about</c><00:06:58.840><c> Gemini</c><00:06:59.240><c> what</c><00:06:59.360><c> about</c>

00:06:59.749 --> 00:06:59.759 align:start position:0%
about Sonet what about Gemini what about
 

00:06:59.759 --> 00:07:03.510 align:start position:0%
about Sonet what about Gemini what about
llama<00:07:00.280><c> 3.1</c><00:07:01.080><c> the</c><00:07:01.240><c> 405</c><00:07:02.160><c> billion</c><00:07:02.560><c> version</c><00:07:03.199><c> how</c>

00:07:03.510 --> 00:07:03.520 align:start position:0%
llama 3.1 the 405 billion version how
 

00:07:03.520 --> 00:07:07.150 align:start position:0%
llama 3.1 the 405 billion version how
good<00:07:03.720><c> are</c><00:07:04.120><c> there</c><00:07:04.520><c> in</c><00:07:05.400><c> this</c><00:07:05.879><c> test</c><00:07:06.879><c> what</c><00:07:07.000><c> is</c>

00:07:07.150 --> 00:07:07.160 align:start position:0%
good are there in this test what is
 

00:07:07.160 --> 00:07:09.469 align:start position:0%
good are there in this test what is
their<00:07:07.720><c> performance</c><00:07:08.720><c> so</c><00:07:08.879><c> let's</c><00:07:09.080><c> have</c><00:07:09.199><c> a</c><00:07:09.360><c> look</c>

00:07:09.469 --> 00:07:09.479 align:start position:0%
their performance so let's have a look
 

00:07:09.479 --> 00:07:11.629 align:start position:0%
their performance so let's have a look
at<00:07:09.680><c> Sonet</c><00:07:10.240><c> you</c><00:07:10.440><c> remember</c><00:07:10.759><c> in</c><00:07:10.879><c> my</c><00:07:11.080><c> last</c><00:07:11.319><c> video</c><00:07:11.560><c> I</c>

00:07:11.629 --> 00:07:11.639 align:start position:0%
at Sonet you remember in my last video I
 

00:07:11.639 --> 00:07:14.230 align:start position:0%
at Sonet you remember in my last video I
took<00:07:11.800><c> a</c><00:07:12.000><c> screenshot</c><00:07:12.680><c> live</c><00:07:12.960><c> from</c><00:07:13.120><c> a</c><00:07:13.319><c> recording</c>

00:07:14.230 --> 00:07:14.240 align:start position:0%
took a screenshot live from a recording
 

00:07:14.240 --> 00:07:17.550 align:start position:0%
took a screenshot live from a recording
here<00:07:14.479><c> you</c><00:07:14.680><c> have</c><00:07:14.879><c> youit</c><00:07:15.479><c> performance</c><00:07:16.000><c> of</c><00:07:16.560><c> Sonet</c>

00:07:17.550 --> 00:07:17.560 align:start position:0%
here you have youit performance of Sonet
 

00:07:17.560 --> 00:07:19.550 align:start position:0%
here you have youit performance of Sonet
and<00:07:17.680><c> you</c><00:07:17.840><c> say</c><00:07:18.160><c> great</c><00:07:18.520><c> now</c><00:07:18.759><c> that</c><00:07:18.960><c> we</c><00:07:19.160><c> have</c><00:07:19.360><c> here</c>

00:07:19.550 --> 00:07:19.560 align:start position:0%
and you say great now that we have here
 

00:07:19.560 --> 00:07:21.950 align:start position:0%
and you say great now that we have here
the<00:07:19.720><c> correct</c><00:07:20.039><c> Solution</c><00:07:20.520><c> by</c><00:07:20.639><c> four</c><00:07:20.960><c> Omni</c><00:07:21.360><c> or</c><00:07:21.759><c> by</c>

00:07:21.950 --> 00:07:21.960 align:start position:0%
the correct Solution by four Omni or by
 

00:07:21.960 --> 00:07:24.990 align:start position:0%
the correct Solution by four Omni or by
01<00:07:22.560><c> preview</c><00:07:23.560><c> this</c><00:07:23.680><c> is</c><00:07:23.840><c> it</c><00:07:24.280><c> you</c><00:07:24.440><c> know</c><00:07:24.639><c> we</c><00:07:24.800><c> have</c>

00:07:24.990 --> 00:07:25.000 align:start position:0%
01 preview this is it you know we have
 

00:07:25.000 --> 00:07:26.710 align:start position:0%
01 preview this is it you know we have
this<00:07:25.160><c> permutation</c><00:07:25.720><c> freedom</c><00:07:26.120><c> of</c><00:07:26.319><c> those</c><00:07:26.520><c> two</c>

00:07:26.710 --> 00:07:26.720 align:start position:0%
this permutation freedom of those two
 

00:07:26.720 --> 00:07:29.950 align:start position:0%
this permutation freedom of those two
Fields<00:07:27.560><c> all</c><00:07:27.960><c> those</c><00:07:28.199><c> are</c><00:07:28.400><c> the</c><00:07:28.599><c> correct</c><00:07:29.000><c> answer</c>

00:07:29.950 --> 00:07:29.960 align:start position:0%
Fields all those are the correct answer
 

00:07:29.960 --> 00:07:32.830 align:start position:0%
Fields all those are the correct answer
now<00:07:30.720><c> I</c><00:07:30.879><c> just</c><00:07:31.039><c> wanted</c><00:07:31.280><c> to</c><00:07:31.440><c> make</c><00:07:31.599><c> it</c><00:07:31.840><c> easy</c><00:07:32.639><c> you're</c>

00:07:32.830 --> 00:07:32.840 align:start position:0%
now I just wanted to make it easy you're
 

00:07:32.840 --> 00:07:35.350 align:start position:0%
now I just wanted to make it easy you're
not<00:07:33.000><c> going</c><00:07:33.120><c> to</c><00:07:33.319><c> believe</c><00:07:33.599><c> it</c><00:07:34.199><c> this</c><00:07:34.520><c> is</c><00:07:34.720><c> the</c>

00:07:35.350 --> 00:07:35.360 align:start position:0%
not going to believe it this is the
 

00:07:35.360 --> 00:07:38.950 align:start position:0%
not going to believe it this is the
final<00:07:36.000><c> performance</c><00:07:36.680><c> here</c><00:07:37.120><c> by</c><00:07:37.280><c> claw</c><00:07:37.639><c> 3.5</c><00:07:38.400><c> Sonet</c>

00:07:38.950 --> 00:07:38.960 align:start position:0%
final performance here by claw 3.5 Sonet
 

00:07:38.960 --> 00:07:41.350 align:start position:0%
final performance here by claw 3.5 Sonet
on<00:07:39.319><c> my</c><00:07:39.759><c> extreme</c><00:07:40.360><c> logic</c>

00:07:41.350 --> 00:07:41.360 align:start position:0%
on my extreme logic
 

00:07:41.360 --> 00:07:43.990 align:start position:0%
on my extreme logic
test<00:07:42.360><c> and</c><00:07:42.520><c> some</c><00:07:42.720><c> user</c><00:07:43.000><c> ask</c><00:07:43.319><c> hey</c><00:07:43.599><c> what</c><00:07:43.720><c> does</c><00:07:43.879><c> it</c>

00:07:43.990 --> 00:07:44.000 align:start position:0%
test and some user ask hey what does it
 

00:07:44.000 --> 00:07:46.749 align:start position:0%
test and some user ask hey what does it
mean<00:07:44.240><c> a</c><00:07:44.400><c> red</c><00:07:44.720><c> box</c><00:07:45.199><c> a</c><00:07:45.400><c> Red</c><00:07:45.639><c> Box</c><00:07:45.919><c> means</c><00:07:46.400><c> that</c><00:07:46.560><c> the</c>

00:07:46.749 --> 00:07:46.759 align:start position:0%
mean a red box a Red Box means that the
 

00:07:46.759 --> 00:07:50.230 align:start position:0%
mean a red box a Red Box means that the
result<00:07:47.120><c> is</c><00:07:47.840><c> incorrect</c><00:07:48.840><c> now</c><00:07:49.840><c> people</c><00:07:50.039><c> were</c>

00:07:50.230 --> 00:07:50.240 align:start position:0%
result is incorrect now people were
 

00:07:50.240 --> 00:07:53.270 align:start position:0%
result is incorrect now people were
interested<00:07:50.680><c> here</c><00:07:50.840><c> in</c><00:07:51.039><c> Gro</c><00:07:51.479><c> 2</c><00:07:52.080><c> how</c><00:07:52.360><c> good</c><00:07:52.639><c> is</c><00:07:52.759><c> it</c>

00:07:53.270 --> 00:07:53.280 align:start position:0%
interested here in Gro 2 how good is it
 

00:07:53.280 --> 00:07:55.350 align:start position:0%
interested here in Gro 2 how good is it
now<00:07:53.560><c> you</c><00:07:53.680><c> not</c><00:07:53.879><c> that</c><00:07:54.039><c> gr</c><00:07:54.319><c> 2</c><00:07:54.560><c> failed</c><00:07:55.000><c> here</c><00:07:55.159><c> to</c>

00:07:55.350 --> 00:07:55.360 align:start position:0%
now you not that gr 2 failed here to
 

00:07:55.360 --> 00:07:57.909 align:start position:0%
now you not that gr 2 failed here to
give<00:07:55.520><c> us</c><00:07:55.800><c> here</c><00:07:56.120><c> a</c><00:07:56.440><c> table</c><00:07:56.879><c> no</c><00:07:57.120><c> problem</c><00:07:57.479><c> we</c><00:07:57.680><c> have</c>

00:07:57.909 --> 00:07:57.919 align:start position:0%
give us here a table no problem we have
 

00:07:57.919 --> 00:08:00.430 align:start position:0%
give us here a table no problem we have
here<00:07:58.080><c> a</c><00:07:58.199><c> list</c><00:07:58.840><c> and</c><00:07:59.000><c> I</c><00:07:59.120><c> took</c><00:07:59.560><c> the</c><00:07:59.720><c> time</c><00:07:59.960><c> I</c><00:08:00.080><c> wrote</c>

00:08:00.430 --> 00:08:00.440 align:start position:0%
here a list and I took the time I wrote
 

00:08:00.440 --> 00:08:03.629 align:start position:0%
here a list and I took the time I wrote
here<00:08:00.720><c> down</c><00:08:01.039><c> so</c><00:08:01.400><c> you</c><00:08:01.560><c> see</c><00:08:02.479><c> this</c><00:08:02.680><c> is</c><00:08:02.919><c> here</c><00:08:03.280><c> all</c>

00:08:03.629 --> 00:08:03.639 align:start position:0%
here down so you see this is here all
 

00:08:03.639 --> 00:08:05.790 align:start position:0%
here down so you see this is here all
the<00:08:03.840><c> red</c><00:08:04.159><c> boxes</c><00:08:04.599><c> that</c><00:08:04.759><c> we</c><00:08:04.960><c> incorrect</c><00:08:05.560><c> with</c>

00:08:05.790 --> 00:08:05.800 align:start position:0%
the red boxes that we incorrect with
 

00:08:05.800 --> 00:08:12.149 align:start position:0%
the red boxes that we incorrect with
grock<00:08:06.639><c> 2</c><00:08:07.800><c> okay</c><00:08:08.800><c> next</c><00:08:09.080><c> one</c><00:08:09.440><c> was</c><00:08:09.800><c> llama</c><00:08:10.639><c> 3.1</c><00:08:11.639><c> the</c>

00:08:12.149 --> 00:08:12.159 align:start position:0%
grock 2 okay next one was llama 3.1 the
 

00:08:12.159 --> 00:08:16.309 align:start position:0%
grock 2 okay next one was llama 3.1 the
405b<00:08:13.159><c> instruction</c><00:08:14.199><c> model</c><00:08:15.199><c> and</c><00:08:15.520><c> as</c><00:08:15.680><c> I</c><00:08:15.840><c> told</c><00:08:16.080><c> you</c>

00:08:16.309 --> 00:08:16.319 align:start position:0%
405b instruction model and as I told you
 

00:08:16.319 --> 00:08:19.149 align:start position:0%
405b instruction model and as I told you
yeah<00:08:16.479><c> I</c><00:08:16.639><c> tried</c><00:08:17.159><c> several</c><00:08:17.720><c> times</c><00:08:18.080><c> to</c><00:08:18.639><c> find</c><00:08:18.960><c> here</c>

00:08:19.149 --> 00:08:19.159 align:start position:0%
yeah I tried several times to find here
 

00:08:19.159 --> 00:08:22.710 align:start position:0%
yeah I tried several times to find here
a<00:08:19.319><c> solution</c><00:08:19.879><c> but</c><00:08:20.720><c> at</c><00:08:20.840><c> least</c><00:08:21.080><c> for</c><00:08:21.680><c> my</c><00:08:22.199><c> personal</c>

00:08:22.710 --> 00:08:22.720 align:start position:0%
a solution but at least for my personal
 

00:08:22.720 --> 00:08:25.469 align:start position:0%
a solution but at least for my personal
extreme<00:08:23.120><c> logic</c><00:08:23.599><c> test</c><00:08:23.919><c> I</c><00:08:24.000><c> can</c><00:08:24.199><c> tell</c><00:08:24.400><c> you</c><00:08:25.319><c> there</c>

00:08:25.469 --> 00:08:25.479 align:start position:0%
extreme logic test I can tell you there
 

00:08:25.479 --> 00:08:29.110 align:start position:0%
extreme logic test I can tell you there
are<00:08:25.680><c> some</c><00:08:26.400><c> real</c><00:08:26.840><c> critical</c><00:08:28.120><c> incompatibilities</c>

00:08:29.110 --> 00:08:29.120 align:start position:0%
are some real critical incompatibilities
 

00:08:29.120 --> 00:08:31.749 align:start position:0%
are some real critical incompatibilities
no<00:08:29.520><c> Lam</c><00:08:29.800><c> is</c><00:08:29.919><c> not</c><00:08:30.080><c> able</c><00:08:30.319><c> to</c><00:08:30.440><c> solve</c><00:08:30.879><c> this</c><00:08:31.240><c> extreme</c>

00:08:31.749 --> 00:08:31.759 align:start position:0%
no Lam is not able to solve this extreme
 

00:08:31.759 --> 00:08:32.990 align:start position:0%
no Lam is not able to solve this extreme
logic

00:08:32.990 --> 00:08:33.000 align:start position:0%
logic
 

00:08:33.000 --> 00:08:35.949 align:start position:0%
logic
test<00:08:34.000><c> and</c><00:08:34.159><c> then</c><00:08:34.320><c> let's</c><00:08:34.519><c> come</c><00:08:34.640><c> to</c><00:08:34.800><c> gerini</c><00:08:35.279><c> 1.5</c>

00:08:35.949 --> 00:08:35.959 align:start position:0%
test and then let's come to gerini 1.5
 

00:08:35.959 --> 00:08:39.550 align:start position:0%
test and then let's come to gerini 1.5
Pro<00:08:36.800><c> here</c><00:08:37.159><c> the</c><00:08:37.399><c> experimental</c><00:08:38.279><c> version</c><00:08:39.279><c> and</c><00:08:39.440><c> if</c>

00:08:39.550 --> 00:08:39.560 align:start position:0%
Pro here the experimental version and if
 

00:08:39.560 --> 00:08:42.469 align:start position:0%
Pro here the experimental version and if
I<00:08:39.760><c> checked</c><00:08:40.360><c> manually</c><00:08:41.039><c> now</c><00:08:41.680><c> how</c><00:08:41.880><c> good</c><00:08:42.080><c> is</c><00:08:42.240><c> here</c>

00:08:42.469 --> 00:08:42.479 align:start position:0%
I checked manually now how good is here
 

00:08:42.479 --> 00:08:45.829 align:start position:0%
I checked manually now how good is here
the<00:08:42.680><c> result</c><00:08:43.039><c> of</c><00:08:43.600><c> gini</c><00:08:44.600><c> it</c><00:08:44.760><c> was</c><00:08:45.000><c> surprisingly</c>

00:08:45.829 --> 00:08:45.839 align:start position:0%
the result of gini it was surprisingly
 

00:08:45.839 --> 00:08:48.750 align:start position:0%
the result of gini it was surprisingly
good<00:08:46.560><c> look</c><00:08:47.160><c> if</c><00:08:47.399><c> it</c><00:08:47.519><c> would</c><00:08:47.760><c> have</c><00:08:48.000><c> exchanged</c>

00:08:48.750 --> 00:08:48.760 align:start position:0%
good look if it would have exchanged
 

00:08:48.760 --> 00:08:50.990 align:start position:0%
good look if it would have exchanged
these<00:08:49.000><c> two</c><00:08:49.240><c> fields</c><00:08:49.839><c> and</c><00:08:50.000><c> it</c><00:08:50.160><c> would</c><00:08:50.480><c> have</c>

00:08:50.990 --> 00:08:51.000 align:start position:0%
these two fields and it would have
 

00:08:51.000 --> 00:08:53.870 align:start position:0%
these two fields and it would have
exchanged<00:08:51.600><c> these</c><00:08:51.880><c> two</c><00:08:52.200><c> Fields</c><00:08:53.200><c> it</c><00:08:53.320><c> would</c><00:08:53.640><c> have</c>

00:08:53.870 --> 00:08:53.880 align:start position:0%
exchanged these two Fields it would have
 

00:08:53.880 --> 00:08:55.790 align:start position:0%
exchanged these two Fields it would have
come<00:08:54.120><c> up</c><00:08:54.320><c> with</c><00:08:54.480><c> the</c><00:08:54.720><c> correct</c><00:08:55.120><c> solution</c><00:08:55.600><c> in</c>

00:08:55.790 --> 00:08:55.800 align:start position:0%
come up with the correct solution in
 

00:08:55.800 --> 00:08:57.389 align:start position:0%
come up with the correct solution in
Step<00:08:56.120><c> number</c>

00:08:57.389 --> 00:08:57.399 align:start position:0%
Step number
 

00:08:57.399 --> 00:09:00.150 align:start position:0%
Step number
four<00:08:58.399><c> within</c><00:08:58.839><c> one</c><00:08:59.120><c> PR</c>

00:09:00.150 --> 00:09:00.160 align:start position:0%
four within one PR
 

00:09:00.160 --> 00:09:02.509 align:start position:0%
four within one PR
and<00:09:00.440><c> this</c><00:09:00.600><c> is</c><00:09:01.120><c> really</c>

00:09:02.509 --> 00:09:02.519 align:start position:0%
and this is really
 

00:09:02.519 --> 00:09:05.670 align:start position:0%
and this is really
amazing<00:09:03.519><c> so</c><00:09:03.920><c> from</c><00:09:04.240><c> all</c><00:09:04.480><c> the</c><00:09:04.720><c> models</c><00:09:05.160><c> that</c><00:09:05.320><c> we</c>

00:09:05.670 --> 00:09:05.680 align:start position:0%
amazing so from all the models that we
 

00:09:05.680 --> 00:09:09.190 align:start position:0%
amazing so from all the models that we
tested<00:09:06.680><c> apart</c><00:09:07.040><c> from</c><00:09:07.440><c> o1</c><00:09:07.920><c> preview</c><00:09:08.360><c> and</c><00:09:08.600><c> for</c>

00:09:09.190 --> 00:09:09.200 align:start position:0%
tested apart from o1 preview and for
 

00:09:09.200 --> 00:09:13.670 align:start position:0%
tested apart from o1 preview and for
Omni<00:09:10.200><c> I</c><00:09:10.320><c> have</c><00:09:10.480><c> to</c><00:09:11.000><c> say</c><00:09:11.519><c> gini</c><00:09:12.079><c> 1.5</c><00:09:12.800><c> Pro</c><00:09:13.320><c> really</c>

00:09:13.670 --> 00:09:13.680 align:start position:0%
Omni I have to say gini 1.5 Pro really
 

00:09:13.680 --> 00:09:16.750 align:start position:0%
Omni I have to say gini 1.5 Pro really
surprised<00:09:14.279><c> me</c><00:09:15.160><c> real</c><00:09:15.600><c> nice</c>

00:09:16.750 --> 00:09:16.760 align:start position:0%
surprised me real nice
 

00:09:16.760 --> 00:09:19.509 align:start position:0%
surprised me real nice
performance<00:09:17.760><c> and</c><00:09:17.920><c> I</c><00:09:18.040><c> think</c><00:09:18.560><c> if</c><00:09:18.680><c> I</c><00:09:18.839><c> go</c><00:09:19.040><c> in</c><00:09:19.279><c> here</c>

00:09:19.509 --> 00:09:19.519 align:start position:0%
performance and I think if I go in here
 

00:09:19.519 --> 00:09:22.710 align:start position:0%
performance and I think if I go in here
with<00:09:19.760><c> an</c><00:09:20.160><c> additional</c><00:09:20.959><c> prompt</c><00:09:21.959><c> and</c><00:09:22.160><c> I</c><00:09:22.440><c> have</c>

00:09:22.710 --> 00:09:22.720 align:start position:0%
with an additional prompt and I have
 

00:09:22.720 --> 00:09:26.069 align:start position:0%
with an additional prompt and I have
here<00:09:23.160><c> a</c><00:09:23.720><c> targeted</c><00:09:24.440><c> question</c><00:09:25.279><c> to</c><00:09:25.680><c> ask</c><00:09:25.920><c> the</c>

00:09:26.069 --> 00:09:26.079 align:start position:0%
here a targeted question to ask the
 

00:09:26.079 --> 00:09:28.150 align:start position:0%
here a targeted question to ask the
system<00:09:26.519><c> hey</c><00:09:26.720><c> could</c><00:09:26.880><c> you</c><00:09:27.079><c> validate</c><00:09:27.640><c> exactly</c>

00:09:28.150 --> 00:09:28.160 align:start position:0%
system hey could you validate exactly
 

00:09:28.160 --> 00:09:29.750 align:start position:0%
system hey could you validate exactly
these<00:09:28.360><c> two</c><00:09:28.519><c> fields</c><00:09:28.880><c> or</c><00:09:29.000><c> maybe</c><00:09:29.240><c> this</c><00:09:29.519><c> two</c>

00:09:29.750 --> 00:09:29.760 align:start position:0%
these two fields or maybe this two
 

00:09:29.760 --> 00:09:33.230 align:start position:0%
these two fields or maybe this two
Fields<00:09:30.760><c> I'm</c><00:09:31.160><c> quite</c><00:09:31.519><c> confident</c><00:09:32.040><c> the</c><00:09:32.200><c> ger</c><00:09:32.600><c> 1.5</c>

00:09:33.230 --> 00:09:33.240 align:start position:0%
Fields I'm quite confident the ger 1.5
 

00:09:33.240 --> 00:09:35.389 align:start position:0%
Fields I'm quite confident the ger 1.5
Pro<00:09:33.560><c> will</c><00:09:33.800><c> come</c><00:09:34.000><c> up</c><00:09:34.399><c> with</c><00:09:34.600><c> the</c><00:09:34.760><c> correct</c>

00:09:35.389 --> 00:09:35.399 align:start position:0%
Pro will come up with the correct
 

00:09:35.399 --> 00:09:37.829 align:start position:0%
Pro will come up with the correct
solution<00:09:36.399><c> so</c><00:09:36.680><c> there</c><00:09:36.800><c> you</c><00:09:37.000><c> have</c><00:09:37.160><c> it</c><00:09:37.480><c> if</c><00:09:37.640><c> you</c>

00:09:37.829 --> 00:09:37.839 align:start position:0%
solution so there you have it if you
 

00:09:37.839 --> 00:09:40.470 align:start position:0%
solution so there you have it if you
know<00:09:38.160><c> ask</c><00:09:38.480><c> hey</c><00:09:38.800><c> is</c><00:09:38.920><c> it</c><00:09:39.320><c> possible</c><00:09:39.800><c> here</c><00:09:40.000><c> for</c>

00:09:40.470 --> 00:09:40.480 align:start position:0%
know ask hey is it possible here for
 

00:09:40.480 --> 00:09:43.750 align:start position:0%
know ask hey is it possible here for
this<00:09:40.760><c> extreme</c><00:09:41.160><c> logic</c><00:09:41.560><c> test</c><00:09:41.880><c> of</c><00:09:42.040><c> mine</c><00:09:42.920><c> to</c><00:09:43.399><c> have</c>

00:09:43.750 --> 00:09:43.760 align:start position:0%
this extreme logic test of mine to have
 

00:09:43.760 --> 00:09:46.910 align:start position:0%
this extreme logic test of mine to have
with<00:09:44.279><c> Omni</c><00:09:44.959><c> to</c><00:09:45.360><c> come</c><00:09:45.560><c> to</c><00:09:45.760><c> the</c><00:09:46.040><c> performance</c><00:09:46.720><c> in</c>

00:09:46.910 --> 00:09:46.920 align:start position:0%
with Omni to come to the performance in
 

00:09:46.920 --> 00:09:49.470 align:start position:0%
with Omni to come to the performance in
causal<00:09:47.360><c> reasoning</c><00:09:47.880><c> for</c><00:09:48.120><c> 01</c><00:09:48.519><c> preview</c><00:09:49.320><c> the</c>

00:09:49.470 --> 00:09:49.480 align:start position:0%
causal reasoning for 01 preview the
 

00:09:49.480 --> 00:09:53.030 align:start position:0%
causal reasoning for 01 preview the
simple<00:09:49.839><c> answer</c><00:09:50.120><c> of</c><00:09:50.320><c> this</c><00:09:50.519><c> video</c><00:09:51.040><c> is</c><00:09:51.880><c> yes</c><00:09:52.880><c> it</c>

00:09:53.030 --> 00:09:53.040 align:start position:0%
simple answer of this video is yes it
 

00:09:53.040 --> 00:09:54.949 align:start position:0%
simple answer of this video is yes it
would<00:09:53.240><c> be</c><00:09:53.399><c> great</c><00:09:53.640><c> to</c><00:09:53.800><c> see</c><00:09:54.000><c> you</c><00:09:54.200><c> in</c><00:09:54.399><c> my</c><00:09:54.640><c> next</c>

00:09:54.949 --> 00:09:54.959 align:start position:0%
would be great to see you in my next
 

00:09:54.959 --> 00:09:57.959 align:start position:0%
would be great to see you in my next
video

