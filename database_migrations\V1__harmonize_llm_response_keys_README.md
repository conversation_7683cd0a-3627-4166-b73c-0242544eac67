# Explanation for `V1__harmonize_llm_response_keys.sql`

This document details the purpose, logic, and usage of the `V1__harmonize_llm_response_keys.sql` migration script.

## 1. Purpose of the Migration

The primary purpose of this migration is to **standardize the structure of the `llm_response` JSONB column** across all relevant `youtube_*` tables within the Supabase project (ID: `nppxvpupvhszcdkspsiq`). This ensures consistency in how Large Language Model (LLM) outputs are stored and accessed.

## 2. Problem Addressed

The `llm_response` column, prior to this migration, suffered from several inconsistencies:

*   **Inconsistent Key Casing:** Keys like "Insights" and "insights" were used interchangeably.
*   **Varied Key Names:** Different key names were used for similar concepts (e.g., "Key Takeaways", "Summary" for insights; "Key Words", "Tags" for keywords).
*   **Lack of Uniform Schema:** This made it difficult to reliably query and process the `llm_response` data programmatically across different tables or even within the same table over time.

## 3. The Standardized `llm_response` Structure

The migration transforms the `llm_response` column to consistently use the following four top-level lowercase keys. The value for each key is a JSON array of strings (`TEXT[]`):

*   `"insights": []`
*   `"keywords": []`
*   `"questions": []`
*   `"recommendations": []`

## 4. Key Mapping and Data Consolidation Logic

The core of the migration is the PL/pgSQL function `harmonize_llm_response_jsonb_v2`. This function implements the following logic:

*   **Case-Insensitive Mapping:** It identifies various existing key names (case-insensitively) and maps them to one of the four standard keys. Examples include:
    *   **To `insights`**: "Insights", "Key Insights", "Main Insights", "Summary", "Key Takeaways", "Conclusions", "Key Conclusions", "Resumo", "Conclusões Chave", "Principais Ideias"
    *   **To `keywords`**: "Keywords", "Key Words", "Tags", "Palavras-chave"
    *   **To `questions`**: "Questions", "Typical Questions", "Key Questions", "FAQs", "Perguntas", "Perguntas Típicas"
    *   **To `recommendations`**: "Recommendations", "Suggestions", "Actionable Advice", "Next Steps", "Recomendações", "Aconselhamento Prático"
*   **Value Aggregation:**
    *   If the original value associated with a mapped key is a single string, it's added as an element to the corresponding standard key's list.
    *   If the original value is already a JSON array of strings, its elements are merged into the standard key's list.
    *   Duplicate entries resulting from this aggregation are handled (the function aims to build a distinct list where appropriate, though the primary goal is consolidation).

## 5. Data Preservation for Unmapped Keys

To prevent data loss from keys in the original `llm_response` that do not directly map to the four standard categories, the following strategy is employed:

1.  Any key-value pair from the input JSONB that is not part of the explicit mapping rules is identified.
2.  The original key and its value (converted to a string if it's not already) are formatted into a single string: `"original_key_name: value_as_string"`.
3.  This formatted string is then appended to the `insights` list in the new, standardized JSONB object. This ensures that no information is silently discarded.

## 6. How the `harmonize_llm_response_jsonb_v2` Function Works

1.  **Input:** Takes an existing `llm_response` JSONB object.
2.  **Initialization:** Creates a new JSONB object (`harmonized_json`) initialized with the four standard keys, each holding an empty JSON array (`[]`).
3.  **Iteration:** Loops through each key-value pair of the input `llm_response`.
4.  **Mapping & Consolidation:**
    *   It checks if the current key (case-insensitively) matches any of the predefined variations for `insights`, `keywords`, `questions`, or `recommendations`.
    *   If a match is found, it processes the value (string or array of strings) and appends it/its elements to the corresponding list in `harmonized_json`.
5.  **Handling Unmapped Keys:** If a key does not match any of the predefined mappings, it's considered an "other" key. The function formats this key and its value as a string (`original_key: original_value_as_text`) and adds it to the `insights` array in `harmonized_json`.
6.  **Output:** Returns the `harmonized_json` object, now conforming to the standard structure.

## 7. How to Use the Migration Script (`V1__harmonize_llm_response_keys.sql`)

1.  **Function Definition:** The script first defines the `harmonize_llm_response_jsonb_v2` function. This function needs to be created in the database before it can be used.
2.  **Applying the Migration:**
    *   The script includes a **commented-out `DO` block** at the end. This block contains the logic to apply the `harmonize_llm_response_jsonb_v2` function to the `llm_response` column of specified tables.
    *   **To run the migration:**
        1.  **Uncomment** the `DO` block.
        2.  **Verify the list of table names** in the `FOREACH tbl_name IN ARRAY [...]` section. Ensure it includes all `youtube_*` tables that have the `llm_response` column and need to be updated.
        3.  **Execute the entire SQL script** against your Supabase database (Project ID: `nppxvpupvhszcdkspsiq`). This can be done via the Supabase SQL Editor or any compatible PostgreSQL client.
    *   This process will iterate through each specified table and update the `llm_response` column for every row, applying the harmonization logic.

## 8. Important Considerations for New Data

*   This migration script is designed to standardize **existing historical data**.
*   The Python script (e.g., `ollama_supabase_query_videos_V7_2request_parallel_feedbackgiven_new_column.py`) responsible for generating **new** `llm_response` data from the Ollama LLM should be modified separately.
*   For **new data**, the Python script should be updated to *strictly* parse the Ollama response and construct a JSON object containing *only* the four standard lowercase keys (`insights`, `keywords`, `questions`, `recommendations`). Any other keys returned by Ollama for new entries should generally be ignored, rather than being appended to the `insights` list as is done by this historical data migration script.
