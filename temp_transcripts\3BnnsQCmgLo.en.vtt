WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:02.310 align:start position:0%
 
okay<00:00:00.359><c> so</c><00:00:00.520><c> in</c><00:00:00.640><c> the</c><00:00:00.799><c> previous</c><00:00:01.160><c> AMA</c><00:00:01.719><c> videos</c><00:00:02.120><c> we've</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
okay so in the previous AMA videos we've
 

00:00:02.320 --> 00:00:04.030 align:start position:0%
okay so in the previous AMA videos we've
looked<00:00:02.520><c> at</c><00:00:02.720><c> how</c><00:00:02.840><c> to</c><00:00:03.040><c> set</c><00:00:03.240><c> it</c><00:00:03.399><c> up</c><00:00:03.639><c> and</c><00:00:03.800><c> stuff</c>

00:00:04.030 --> 00:00:04.040 align:start position:0%
looked at how to set it up and stuff
 

00:00:04.040 --> 00:00:06.269 align:start position:0%
looked at how to set it up and stuff
like<00:00:04.240><c> that</c><00:00:04.480><c> and</c><00:00:04.600><c> we've</c><00:00:04.799><c> also</c><00:00:05.160><c> come</c><00:00:05.359><c> in</c><00:00:05.600><c> here</c>

00:00:06.269 --> 00:00:06.279 align:start position:0%
like that and we've also come in here
 

00:00:06.279 --> 00:00:08.950 align:start position:0%
like that and we've also come in here
and<00:00:06.480><c> looked</c><00:00:06.759><c> at</c><00:00:07.160><c> how</c><00:00:07.399><c> to</c><00:00:08.120><c> install</c><00:00:08.599><c> different</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
and looked at how to install different
 

00:00:08.960 --> 00:00:11.830 align:start position:0%
and looked at how to install different
models<00:00:09.360><c> in</c><00:00:09.639><c> here</c><00:00:10.480><c> the</c><00:00:10.639><c> challenge</c><00:00:11.120><c> is</c><00:00:11.519><c> what</c><00:00:11.679><c> if</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
models in here the challenge is what if
 

00:00:11.840 --> 00:00:13.829 align:start position:0%
models in here the challenge is what if
you<00:00:12.120><c> actually</c><00:00:12.599><c> don't</c><00:00:12.880><c> see</c><00:00:13.200><c> the</c><00:00:13.360><c> model</c><00:00:13.719><c> that</c>

00:00:13.829 --> 00:00:13.839 align:start position:0%
you actually don't see the model that
 

00:00:13.839 --> 00:00:16.630 align:start position:0%
you actually don't see the model that
you<00:00:14.040><c> want</c><00:00:14.599><c> in</c><00:00:14.920><c> here</c><00:00:15.599><c> and</c><00:00:15.839><c> for</c><00:00:16.039><c> example</c><00:00:16.400><c> if</c><00:00:16.520><c> it's</c>

00:00:16.630 --> 00:00:16.640 align:start position:0%
you want in here and for example if it's
 

00:00:16.640 --> 00:00:19.310 align:start position:0%
you want in here and for example if it's
a<00:00:16.800><c> fine</c><00:00:17.199><c> tune</c><00:00:17.840><c> of</c><00:00:18.000><c> one</c><00:00:18.119><c> of</c><00:00:18.279><c> the</c><00:00:18.400><c> models</c><00:00:18.880><c> that</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
a fine tune of one of the models that
 

00:00:19.320 --> 00:00:21.830 align:start position:0%
a fine tune of one of the models that
are<00:00:19.600><c> in</c><00:00:19.960><c> here</c><00:00:20.960><c> but</c><00:00:21.080><c> you</c><00:00:21.199><c> don't</c><00:00:21.439><c> actually</c><00:00:21.600><c> see</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
are in here but you don't actually see
 

00:00:21.840 --> 00:00:23.990 align:start position:0%
are in here but you don't actually see
the<00:00:21.960><c> model</c><00:00:22.240><c> that</c><00:00:22.359><c> you</c><00:00:22.480><c> want</c><00:00:22.760><c> in</c><00:00:22.960><c> here</c><00:00:23.560><c> so</c><00:00:23.720><c> don't</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
the model that you want in here so don't
 

00:00:24.000 --> 00:00:25.670 align:start position:0%
the model that you want in here so don't
despair<00:00:24.359><c> it's</c><00:00:24.519><c> actually</c><00:00:24.800><c> quite</c><00:00:25.080><c> easy</c><00:00:25.439><c> to</c>

00:00:25.670 --> 00:00:25.680 align:start position:0%
despair it's actually quite easy to
 

00:00:25.680 --> 00:00:28.550 align:start position:0%
despair it's actually quite easy to
install<00:00:26.240><c> custom</c><00:00:26.679><c> models</c><00:00:27.400><c> into</c><00:00:27.679><c> a</c><00:00:27.880><c> llama</c><00:00:28.359><c> as</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
install custom models into a llama as
 

00:00:28.560 --> 00:00:30.310 align:start position:0%
install custom models into a llama as
well<00:00:29.000><c> so</c><00:00:29.160><c> in</c><00:00:29.279><c> this</c><00:00:29.400><c> video</c><00:00:29.640><c> Let's</c><00:00:29.759><c> look</c><00:00:30.080><c> at</c><00:00:30.199><c> how</c>

00:00:30.310 --> 00:00:30.320 align:start position:0%
well so in this video Let's look at how
 

00:00:30.320 --> 00:00:32.429 align:start position:0%
well so in this video Let's look at how
to<00:00:30.480><c> do</c><00:00:30.800><c> that</c><00:00:31.279><c> all</c><00:00:31.400><c> right</c><00:00:31.560><c> so</c><00:00:31.759><c> the</c><00:00:31.880><c> custom</c><00:00:32.200><c> model</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
to do that all right so the custom model
 

00:00:32.439 --> 00:00:34.549 align:start position:0%
to do that all right so the custom model
that<00:00:32.559><c> I'm</c><00:00:32.680><c> going</c><00:00:32.759><c> to</c><00:00:33.000><c> install</c><00:00:33.559><c> here</c><00:00:34.200><c> is</c><00:00:34.360><c> a</c>

00:00:34.549 --> 00:00:34.559 align:start position:0%
that I'm going to install here is a
 

00:00:34.559 --> 00:00:36.549 align:start position:0%
that I'm going to install here is a
model<00:00:34.960><c> called</c><00:00:35.399><c> Jackal</c><00:00:35.840><c> Loop</c><00:00:36.120><c> so</c><00:00:36.239><c> I've</c><00:00:36.399><c> been</c>

00:00:36.549 --> 00:00:36.559 align:start position:0%
model called Jackal Loop so I've been
 

00:00:36.559 --> 00:00:38.790 align:start position:0%
model called Jackal Loop so I've been
playing<00:00:36.840><c> with</c><00:00:37.000><c> this</c><00:00:37.280><c> in</c><00:00:37.559><c> collab</c><00:00:38.480><c> I</c><00:00:38.600><c> might</c>

00:00:38.790 --> 00:00:38.800 align:start position:0%
playing with this in collab I might
 

00:00:38.800 --> 00:00:40.190 align:start position:0%
playing with this in collab I might
actually<00:00:38.960><c> make</c><00:00:39.120><c> a</c><00:00:39.280><c> full</c><00:00:39.520><c> video</c><00:00:39.800><c> just</c><00:00:39.960><c> about</c>

00:00:40.190 --> 00:00:40.200 align:start position:0%
actually make a full video just about
 

00:00:40.200 --> 00:00:42.310 align:start position:0%
actually make a full video just about
this<00:00:40.399><c> model</c><00:00:41.039><c> but</c><00:00:41.239><c> for</c><00:00:41.399><c> the</c><00:00:41.640><c> sake</c><00:00:41.800><c> of</c><00:00:41.960><c> this</c>

00:00:42.310 --> 00:00:42.320 align:start position:0%
this model but for the sake of this
 

00:00:42.320 --> 00:00:44.630 align:start position:0%
this model but for the sake of this
video<00:00:42.559><c> we're</c><00:00:42.719><c> going</c><00:00:42.800><c> to</c><00:00:43.000><c> install</c><00:00:43.640><c> this</c><00:00:44.200><c> into</c>

00:00:44.630 --> 00:00:44.640 align:start position:0%
video we're going to install this into
 

00:00:44.640 --> 00:00:46.790 align:start position:0%
video we're going to install this into
olama<00:00:45.640><c> so</c><00:00:45.800><c> you</c><00:00:45.960><c> can</c><00:00:46.039><c> see</c><00:00:46.239><c> that</c><00:00:46.399><c> this</c><00:00:46.520><c> is</c>

00:00:46.790 --> 00:00:46.800 align:start position:0%
olama so you can see that this is
 

00:00:46.800 --> 00:00:49.670 align:start position:0%
olama so you can see that this is
basically<00:00:47.120><c> a</c><00:00:47.320><c> 7B</c><00:00:47.960><c> model</c><00:00:48.399><c> it's</c><00:00:48.520><c> a</c><00:00:48.680><c> fine-tuning</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
basically a 7B model it's a fine-tuning
 

00:00:49.680 --> 00:00:54.430 align:start position:0%
basically a 7B model it's a fine-tuning
of<00:00:49.920><c> the</c><00:00:50.079><c> mistal</c><00:00:51.039><c> 7B</c><00:00:51.719><c> model</c><00:00:52.719><c> in</c><00:00:53.000><c> here</c><00:00:53.800><c> and</c><00:00:54.320><c> what</c>

00:00:54.430 --> 00:00:54.440 align:start position:0%
of the mistal 7B model in here and what
 

00:00:54.440 --> 00:00:56.229 align:start position:0%
of the mistal 7B model in here and what
we<00:00:54.559><c> want</c><00:00:54.680><c> to</c><00:00:54.879><c> do</c><00:00:55.199><c> is</c><00:00:55.320><c> we</c><00:00:55.520><c> actually</c><00:00:55.719><c> want</c><00:00:55.840><c> to</c><00:00:56.039><c> get</c>

00:00:56.229 --> 00:00:56.239 align:start position:0%
we want to do is we actually want to get
 

00:00:56.239 --> 00:00:58.750 align:start position:0%
we want to do is we actually want to get
the<00:00:56.440><c> quantized</c><00:00:57.199><c> version</c><00:00:57.520><c> of</c><00:00:57.760><c> this</c><00:00:58.359><c> so</c><00:00:58.519><c> if</c><00:00:58.600><c> we</c>

00:00:58.750 --> 00:00:58.760 align:start position:0%
the quantized version of this so if we
 

00:00:58.760 --> 00:01:00.830 align:start position:0%
the quantized version of this so if we
come<00:00:59.079><c> down</c><00:00:59.399><c> the</c><00:00:59.519><c> one</c><00:00:59.680><c> that</c><00:00:59.760><c> we</c><00:00:59.960><c> we</c><00:01:00.120><c> want</c><00:01:00.600><c> is</c>

00:01:00.830 --> 00:01:00.840 align:start position:0%
come down the one that we we want is
 

00:01:00.840 --> 00:01:05.149 align:start position:0%
come down the one that we we want is
this<00:01:01.559><c> ggf</c><00:01:02.559><c> here</c><00:01:03.199><c> so</c><00:01:03.359><c> if</c><00:01:03.480><c> I</c><00:01:03.640><c> click</c><00:01:03.879><c> into</c><00:01:04.280><c> this</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
this ggf here so if I click into this
 

00:01:05.159 --> 00:01:06.910 align:start position:0%
this ggf here so if I click into this
you<00:01:05.280><c> can</c><00:01:05.400><c> see</c><00:01:05.600><c> that</c><00:01:05.720><c> the</c><00:01:05.840><c> bloke</c><00:01:06.240><c> has</c><00:01:06.439><c> converted</c>

00:01:06.910 --> 00:01:06.920 align:start position:0%
you can see that the bloke has converted
 

00:01:06.920 --> 00:01:09.350 align:start position:0%
you can see that the bloke has converted
the<00:01:07.080><c> weights</c><00:01:07.360><c> from</c><00:01:07.600><c> this</c><00:01:07.880><c> model</c><00:01:08.280><c> into</c><00:01:08.640><c> this</c><00:01:08.880><c> GG</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
the weights from this model into this GG
 

00:01:09.360 --> 00:01:14.469 align:start position:0%
the weights from this model into this GG
UF<00:01:09.840><c> format</c><00:01:10.720><c> so</c><00:01:10.960><c> ggf</c><00:01:11.960><c> is</c><00:01:12.400><c> used</c><00:01:12.600><c> to</c><00:01:12.680><c> be</c><00:01:13.320><c> gml</c><00:01:14.320><c> and</c>

00:01:14.469 --> 00:01:14.479 align:start position:0%
UF format so ggf is used to be gml and
 

00:01:14.479 --> 00:01:18.749 align:start position:0%
UF format so ggf is used to be gml and
this<00:01:14.600><c> is</c><00:01:15.479><c> quantized</c><00:01:16.600><c> version</c><00:01:17.600><c> of</c><00:01:17.960><c> models</c><00:01:18.520><c> so</c>

00:01:18.749 --> 00:01:18.759 align:start position:0%
this is quantized version of models so
 

00:01:18.759 --> 00:01:22.390 align:start position:0%
this is quantized version of models so
this<00:01:18.880><c> is</c><00:01:19.119><c> the</c><00:01:19.560><c> project</c><00:01:19.920><c> called</c><00:01:20.200><c> llama</c><00:01:21.240><c> CPP</c><00:01:22.240><c> it</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
this is the project called llama CPP it
 

00:01:22.400 --> 00:01:24.429 align:start position:0%
this is the project called llama CPP it
allows<00:01:22.680><c> us</c><00:01:22.840><c> to</c><00:01:23.159><c> basically</c><00:01:23.479><c> run</c><00:01:23.759><c> models</c><00:01:24.280><c> it's</c>

00:01:24.429 --> 00:01:24.439 align:start position:0%
allows us to basically run models it's
 

00:01:24.439 --> 00:01:26.950 align:start position:0%
allows us to basically run models it's
what<00:01:25.159><c> is</c><00:01:25.320><c> using</c><00:01:25.680><c> for</c><00:01:25.880><c> models</c><00:01:26.320><c> underneath</c><00:01:26.799><c> the</c>

00:01:26.950 --> 00:01:26.960 align:start position:0%
what is using for models underneath the
 

00:01:26.960 --> 00:01:29.069 align:start position:0%
what is using for models underneath the
hood<00:01:27.640><c> next</c><00:01:27.880><c> up</c><00:01:28.040><c> we're</c><00:01:28.159><c> going</c><00:01:28.240><c> to</c><00:01:28.400><c> go</c><00:01:28.600><c> into</c><00:01:28.920><c> and</c>

00:01:29.069 --> 00:01:29.079 align:start position:0%
hood next up we're going to go into and
 

00:01:29.079 --> 00:01:32.190 align:start position:0%
hood next up we're going to go into and
find<00:01:29.320><c> the</c><00:01:29.479><c> files</c><00:01:30.159><c> versions</c><00:01:30.560><c> of</c><00:01:30.759><c> this</c><00:01:31.200><c> model</c>

00:01:32.190 --> 00:01:32.200 align:start position:0%
find the files versions of this model
 

00:01:32.200 --> 00:01:34.109 align:start position:0%
find the files versions of this model
and<00:01:32.799><c> here</c><00:01:32.960><c> I've</c><00:01:33.119><c> got</c><00:01:33.280><c> a</c><00:01:33.399><c> bunch</c><00:01:33.600><c> of</c><00:01:33.759><c> different</c>

00:01:34.109 --> 00:01:34.119 align:start position:0%
and here I've got a bunch of different
 

00:01:34.119 --> 00:01:37.190 align:start position:0%
and here I've got a bunch of different
choices<00:01:35.119><c> so</c><00:01:35.479><c> basically</c><00:01:36.119><c> these</c><00:01:36.439><c> represent</c><00:01:37.000><c> the</c>

00:01:37.190 --> 00:01:37.200 align:start position:0%
choices so basically these represent the
 

00:01:37.200 --> 00:01:39.429 align:start position:0%
choices so basically these represent the
quality<00:01:37.880><c> of</c><00:01:38.159><c> the</c><00:01:38.280><c> model</c><00:01:38.680><c> there</c><00:01:38.960><c> different</c>

00:01:39.429 --> 00:01:39.439 align:start position:0%
quality of the model there different
 

00:01:39.439 --> 00:01:41.749 align:start position:0%
quality of the model there different
quantizations<00:01:40.439><c> in</c><00:01:40.680><c> here</c><00:01:41.320><c> I'm</c><00:01:41.439><c> going</c><00:01:41.560><c> to</c><00:01:41.680><c> go</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
quantizations in here I'm going to go
 

00:01:41.759 --> 00:01:43.630 align:start position:0%
quantizations in here I'm going to go
for<00:01:41.920><c> a</c><00:01:42.079><c> reasonably</c><00:01:42.600><c> big</c><00:01:42.840><c> one</c><00:01:43.200><c> I'm</c><00:01:43.280><c> going</c><00:01:43.399><c> to</c><00:01:43.520><c> go</c>

00:01:43.630 --> 00:01:43.640 align:start position:0%
for a reasonably big one I'm going to go
 

00:01:43.640 --> 00:01:44.990 align:start position:0%
for a reasonably big one I'm going to go
for<00:01:43.880><c> this</c>

00:01:44.990 --> 00:01:45.000 align:start position:0%
for this
 

00:01:45.000 --> 00:01:48.230 align:start position:0%
for this
q6k<00:01:46.000><c> in</c><00:01:46.200><c> here</c><00:01:46.759><c> but</c><00:01:46.880><c> you</c><00:01:47.040><c> might</c><00:01:47.200><c> want</c><00:01:47.320><c> to</c><00:01:47.520><c> pick</c><00:01:48.079><c> a</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
q6k in here but you might want to pick a
 

00:01:48.240 --> 00:01:49.630 align:start position:0%
q6k in here but you might want to pick a
different<00:01:48.520><c> one</c><00:01:49.119><c> and</c><00:01:49.200><c> I'm</c><00:01:49.320><c> just</c><00:01:49.399><c> going</c><00:01:49.520><c> to</c>

00:01:49.630 --> 00:01:49.640 align:start position:0%
different one and I'm just going to
 

00:01:49.640 --> 00:01:51.749 align:start position:0%
different one and I'm just going to
click<00:01:49.920><c> download</c><00:01:50.560><c> here</c><00:01:50.799><c> now</c><00:01:51.079><c> if</c><00:01:51.159><c> you're</c><00:01:51.360><c> using</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
click download here now if you're using
 

00:01:51.759 --> 00:01:53.069 align:start position:0%
click download here now if you're using
git<00:01:52.079><c> and</c><00:01:52.200><c> you're</c><00:01:52.360><c> quite</c><00:01:52.560><c> comfortable</c><00:01:52.920><c> with</c>

00:01:53.069 --> 00:01:53.079 align:start position:0%
git and you're quite comfortable with
 

00:01:53.079 --> 00:01:54.870 align:start position:0%
git and you're quite comfortable with
Git<00:01:53.320><c> of</c><00:01:53.439><c> course</c><00:01:53.600><c> you</c><00:01:53.680><c> can</c><00:01:53.920><c> just</c><00:01:54.079><c> do</c><00:01:54.680><c> you</c><00:01:54.759><c> can</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
Git of course you can just do you can
 

00:01:54.880 --> 00:01:57.310 align:start position:0%
Git of course you can just do you can
just<00:01:55.000><c> pull</c><00:01:55.280><c> this</c><00:01:55.439><c> down</c><00:01:55.719><c> via</c><00:01:56.079><c> git</c><00:01:56.439><c> as</c><00:01:56.600><c> well</c><00:01:57.200><c> but</c>

00:01:57.310 --> 00:01:57.320 align:start position:0%
just pull this down via git as well but
 

00:01:57.320 --> 00:01:58.870 align:start position:0%
just pull this down via git as well but
here<00:01:57.479><c> we</c><00:01:57.560><c> can</c><00:01:57.640><c> just</c><00:01:57.799><c> come</c><00:01:57.920><c> in</c><00:01:58.119><c> here</c><00:01:58.439><c> we</c><00:01:58.520><c> can</c>

00:01:58.870 --> 00:01:58.880 align:start position:0%
here we can just come in here we can
 

00:01:58.880 --> 00:02:00.550 align:start position:0%
here we can just come in here we can
download<00:01:59.320><c> it</c><00:01:59.520><c> so</c><00:01:59.920><c> that's</c><00:02:00.079><c> going</c><00:02:00.200><c> to</c><00:02:00.320><c> take</c><00:02:00.439><c> a</c>

00:02:00.550 --> 00:02:00.560 align:start position:0%
download it so that's going to take a
 

00:02:00.560 --> 00:02:02.789 align:start position:0%
download it so that's going to take a
little<00:02:00.759><c> bit</c><00:02:00.880><c> of</c><00:02:01.159><c> time</c><00:02:01.360><c> to</c><00:02:01.640><c> download</c><00:02:02.600><c> but</c><00:02:02.719><c> what</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
little bit of time to download but what
 

00:02:02.799 --> 00:02:04.429 align:start position:0%
little bit of time to download but what
I'm<00:02:02.920><c> going</c><00:02:03.000><c> to</c><00:02:03.119><c> do</c><00:02:03.280><c> is</c><00:02:03.479><c> download</c><00:02:04.000><c> that</c><00:02:04.240><c> and</c><00:02:04.360><c> I'm</c>

00:02:04.429 --> 00:02:04.439 align:start position:0%
I'm going to do is download that and I'm
 

00:02:04.439 --> 00:02:07.550 align:start position:0%
I'm going to do is download that and I'm
going<00:02:04.560><c> to</c><00:02:04.719><c> put</c><00:02:04.960><c> that</c><00:02:05.159><c> in</c><00:02:05.399><c> my</c><00:02:05.719><c> models</c><00:02:06.360><c> folder</c><00:02:07.360><c> so</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
going to put that in my models folder so
 

00:02:07.560 --> 00:02:10.109 align:start position:0%
going to put that in my models folder so
that<00:02:07.719><c> I</c><00:02:07.840><c> can</c><00:02:08.039><c> then</c><00:02:08.200><c> start</c><00:02:08.599><c> processing</c><00:02:09.119><c> it</c><00:02:09.920><c> okay</c>

00:02:10.109 --> 00:02:10.119 align:start position:0%
that I can then start processing it okay
 

00:02:10.119 --> 00:02:11.670 align:start position:0%
that I can then start processing it okay
now<00:02:10.280><c> the</c><00:02:10.360><c> model</c><00:02:10.599><c> is</c><00:02:10.759><c> actually</c><00:02:11.039><c> downloaded</c><00:02:11.599><c> we</c>

00:02:11.670 --> 00:02:11.680 align:start position:0%
now the model is actually downloaded we
 

00:02:11.680 --> 00:02:14.710 align:start position:0%
now the model is actually downloaded we
can<00:02:11.879><c> actually</c><00:02:12.160><c> see</c><00:02:12.400><c> it</c><00:02:12.720><c> here</c><00:02:13.440><c> in</c><00:02:14.160><c> my</c><00:02:14.319><c> models</c>

00:02:14.710 --> 00:02:14.720 align:start position:0%
can actually see it here in my models
 

00:02:14.720 --> 00:02:16.990 align:start position:0%
can actually see it here in my models
folder<00:02:15.160><c> here</c><00:02:15.440><c> which</c><00:02:15.640><c> I've</c><00:02:15.959><c> made</c><00:02:16.599><c> so</c><00:02:16.760><c> you</c><00:02:16.879><c> can</c>

00:02:16.990 --> 00:02:17.000 align:start position:0%
folder here which I've made so you can
 

00:02:17.000 --> 00:02:18.509 align:start position:0%
folder here which I've made so you can
see<00:02:17.360><c> I've</c><00:02:17.480><c> got</c><00:02:17.560><c> some</c><00:02:17.720><c> other</c><00:02:17.879><c> models</c><00:02:18.200><c> that</c><00:02:18.319><c> I've</c>

00:02:18.509 --> 00:02:18.519 align:start position:0%
see I've got some other models that I've
 

00:02:18.519 --> 00:02:20.430 align:start position:0%
see I've got some other models that I've
downloaded<00:02:19.120><c> last</c><00:02:19.319><c> week</c><00:02:19.519><c> and</c><00:02:19.720><c> I</c><00:02:19.840><c> made</c><00:02:20.120><c> custom</c>

00:02:20.430 --> 00:02:20.440 align:start position:0%
downloaded last week and I made custom
 

00:02:20.440 --> 00:02:23.509 align:start position:0%
downloaded last week and I made custom
versions<00:02:20.800><c> of</c><00:02:21.080><c> last</c><00:02:21.319><c> week</c><00:02:21.959><c> so</c><00:02:22.480><c> the</c><00:02:22.680><c> next</c><00:02:23.000><c> step</c>

00:02:23.509 --> 00:02:23.519 align:start position:0%
versions of last week so the next step
 

00:02:23.519 --> 00:02:26.190 align:start position:0%
versions of last week so the next step
is<00:02:23.879><c> that</c><00:02:24.080><c> we</c><00:02:24.160><c> want</c><00:02:24.280><c> to</c><00:02:24.480><c> make</c><00:02:24.640><c> a</c><00:02:24.879><c> model</c><00:02:25.319><c> file</c><00:02:26.040><c> so</c>

00:02:26.190 --> 00:02:26.200 align:start position:0%
is that we want to make a model file so
 

00:02:26.200 --> 00:02:27.390 align:start position:0%
is that we want to make a model file so
if<00:02:26.280><c> we</c><00:02:26.400><c> come</c><00:02:26.519><c> in</c><00:02:26.640><c> and</c><00:02:26.800><c> have</c><00:02:26.920><c> a</c><00:02:27.000><c> look</c><00:02:27.160><c> at</c><00:02:27.280><c> the</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
if we come in and have a look at the
 

00:02:27.400 --> 00:02:29.350 align:start position:0%
if we come in and have a look at the
Alama<00:02:27.920><c> model</c><00:02:28.280><c> file</c><00:02:28.519><c> we</c><00:02:28.640><c> can</c><00:02:28.800><c> see</c><00:02:29.080><c> you</c><00:02:29.200><c> know</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
Alama model file we can see you know
 

00:02:29.360 --> 00:02:31.869 align:start position:0%
Alama model file we can see you know
what<00:02:29.480><c> the</c><00:02:29.879><c> parameters</c><00:02:30.480><c> are</c><00:02:31.280><c> and</c><00:02:31.400><c> the</c><00:02:31.560><c> general</c>

00:02:31.869 --> 00:02:31.879 align:start position:0%
what the parameters are and the general
 

00:02:31.879 --> 00:02:34.750 align:start position:0%
what the parameters are and the general
thing<00:02:32.040><c> is</c><00:02:32.200><c> going</c><00:02:32.280><c> to</c><00:02:32.440><c> be</c><00:02:32.920><c> from</c><00:02:33.920><c> and</c><00:02:34.440><c> rather</c>

00:02:34.750 --> 00:02:34.760 align:start position:0%
thing is going to be from and rather
 

00:02:34.760 --> 00:02:36.790 align:start position:0%
thing is going to be from and rather
than<00:02:34.920><c> us</c><00:02:35.200><c> put</c><00:02:35.440><c> in</c><00:02:35.640><c> sort</c><00:02:35.800><c> of</c><00:02:35.959><c> llama</c><00:02:36.360><c> 2</c><00:02:36.680><c> or</c>

00:02:36.790 --> 00:02:36.800 align:start position:0%
than us put in sort of llama 2 or
 

00:02:36.800 --> 00:02:38.190 align:start position:0%
than us put in sort of llama 2 or
something<00:02:37.160><c> here</c><00:02:37.360><c> we're</c><00:02:37.560><c> actually</c><00:02:37.840><c> going</c><00:02:37.959><c> to</c>

00:02:38.190 --> 00:02:38.200 align:start position:0%
something here we're actually going to
 

00:02:38.200 --> 00:02:41.110 align:start position:0%
something here we're actually going to
point<00:02:38.879><c> to</c><00:02:39.120><c> the</c><00:02:39.360><c> file</c><00:02:39.680><c> that</c><00:02:39.840><c> we</c><00:02:40.120><c> downloaded</c>

00:02:41.110 --> 00:02:41.120 align:start position:0%
point to the file that we downloaded
 

00:02:41.120 --> 00:02:42.390 align:start position:0%
point to the file that we downloaded
where<00:02:41.280><c> they're</c><00:02:41.400><c> going</c><00:02:41.519><c> to</c><00:02:41.680><c> set</c><00:02:41.920><c> up</c><00:02:42.159><c> our</c>

00:02:42.390 --> 00:02:42.400 align:start position:0%
where they're going to set up our
 

00:02:42.400 --> 00:02:44.589 align:start position:0%
where they're going to set up our
template<00:02:43.000><c> Etc</c><00:02:43.920><c> and</c><00:02:44.000><c> then</c><00:02:44.080><c> we're</c><00:02:44.200><c> going</c><00:02:44.280><c> to</c><00:02:44.400><c> use</c>

00:02:44.589 --> 00:02:44.599 align:start position:0%
template Etc and then we're going to use
 

00:02:44.599 --> 00:02:47.190 align:start position:0%
template Etc and then we're going to use
the<00:02:44.720><c> model</c><00:02:45.040><c> file</c><00:02:45.360><c> to</c><00:02:45.599><c> actually</c><00:02:46.040><c> create</c><00:02:46.920><c> our</c>

00:02:47.190 --> 00:02:47.200 align:start position:0%
the model file to actually create our
 

00:02:47.200 --> 00:02:49.990 align:start position:0%
the model file to actually create our
model<00:02:48.000><c> so</c><00:02:48.280><c> let's</c><00:02:48.560><c> do</c><00:02:48.760><c> that</c><00:02:49.440><c> so</c><00:02:49.680><c> I'm</c><00:02:49.800><c> just</c><00:02:49.920><c> going</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
model so let's do that so I'm just going
 

00:02:50.000 --> 00:02:52.509 align:start position:0%
model so let's do that so I'm just going
to<00:02:50.159><c> make</c><00:02:50.480><c> a</c><00:02:50.680><c> text</c><00:02:51.000><c> file</c><00:02:51.200><c> for</c><00:02:51.360><c> a</c><00:02:51.480><c> model</c><00:02:51.840><c> file</c><00:02:52.280><c> in</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
to make a text file for a model file in
 

00:02:52.519 --> 00:02:54.670 align:start position:0%
to make a text file for a model file in
here<00:02:53.280><c> okay</c><00:02:53.400><c> so</c><00:02:53.560><c> you</c><00:02:53.640><c> can</c><00:02:53.840><c> see</c><00:02:54.159><c> here</c><00:02:54.400><c> I've</c><00:02:54.560><c> just</c>

00:02:54.670 --> 00:02:54.680 align:start position:0%
here okay so you can see here I've just
 

00:02:54.680 --> 00:02:56.509 align:start position:0%
here okay so you can see here I've just
sort<00:02:54.800><c> of</c><00:02:54.959><c> pre-filled</c><00:02:55.519><c> the</c><00:02:55.640><c> model</c><00:02:56.000><c> file</c><00:02:56.280><c> in</c>

00:02:56.509 --> 00:02:56.519 align:start position:0%
sort of pre-filled the model file in
 

00:02:56.519 --> 00:02:59.030 align:start position:0%
sort of pre-filled the model file in
here<00:02:57.120><c> so</c><00:02:57.400><c> we're</c><00:02:57.599><c> basically</c><00:02:57.959><c> saying</c><00:02:58.360><c> from</c><00:02:58.959><c> and</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
here so we're basically saying from and
 

00:02:59.040 --> 00:03:01.190 align:start position:0%
here so we're basically saying from and
we're<00:02:59.280><c> pointing</c><00:02:59.920><c> to</c><00:03:00.159><c> the</c><00:03:00.319><c> checkpoint</c><00:03:00.879><c> that</c><00:03:01.000><c> we</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
we're pointing to the checkpoint that we
 

00:03:01.200 --> 00:03:02.869 align:start position:0%
we're pointing to the checkpoint that we
just<00:03:01.440><c> downloaded</c><00:03:01.959><c> the</c><00:03:02.080><c> model</c><00:03:02.400><c> weights</c><00:03:02.760><c> that</c>

00:03:02.869 --> 00:03:02.879 align:start position:0%
just downloaded the model weights that
 

00:03:02.879 --> 00:03:05.830 align:start position:0%
just downloaded the model weights that
we<00:03:03.040><c> just</c><00:03:03.239><c> downloaded</c><00:03:04.080><c> so</c><00:03:04.360><c> remember</c><00:03:05.319><c> these</c><00:03:05.599><c> are</c>

00:03:05.830 --> 00:03:05.840 align:start position:0%
we just downloaded so remember these are
 

00:03:05.840 --> 00:03:09.190 align:start position:0%
we just downloaded so remember these are
the<00:03:06.080><c> quantized</c><00:03:07.200><c> version</c><00:03:08.200><c> of</c><00:03:08.360><c> the</c><00:03:08.519><c> Jackalope</c>

00:03:09.190 --> 00:03:09.200 align:start position:0%
the quantized version of the Jackalope
 

00:03:09.200 --> 00:03:12.070 align:start position:0%
the quantized version of the Jackalope
7B<00:03:09.760><c> model</c><00:03:10.200><c> in</c><00:03:10.400><c> here</c><00:03:11.080><c> we</c><00:03:11.200><c> need</c><00:03:11.319><c> to</c><00:03:11.480><c> put</c><00:03:11.599><c> in</c><00:03:11.879><c> a</c>

00:03:12.070 --> 00:03:12.080 align:start position:0%
7B model in here we need to put in a
 

00:03:12.080 --> 00:03:14.509 align:start position:0%
7B model in here we need to put in a
template<00:03:12.640><c> for</c><00:03:13.000><c> a</c><00:03:13.200><c> system</c><00:03:13.560><c> prompt</c><00:03:14.000><c> so</c><00:03:14.239><c> this</c><00:03:14.319><c> is</c>

00:03:14.509 --> 00:03:14.519 align:start position:0%
template for a system prompt so this is
 

00:03:14.519 --> 00:03:17.229 align:start position:0%
template for a system prompt so this is
the<00:03:14.720><c> template</c><00:03:15.519><c> that</c><00:03:15.720><c> they're</c><00:03:15.959><c> using</c><00:03:16.440><c> here</c><00:03:17.120><c> and</c>

00:03:17.229 --> 00:03:17.239 align:start position:0%
the template that they're using here and
 

00:03:17.239 --> 00:03:18.789 align:start position:0%
the template that they're using here and
then<00:03:17.400><c> the</c><00:03:17.519><c> system</c><00:03:17.920><c> prompts</c><00:03:18.400><c> people</c><00:03:18.599><c> can</c>

00:03:18.789 --> 00:03:18.799 align:start position:0%
then the system prompts people can
 

00:03:18.799 --> 00:03:20.630 align:start position:0%
then the system prompts people can
actually<00:03:19.000><c> fill</c><00:03:19.400><c> that</c><00:03:19.599><c> out</c><00:03:20.239><c> when</c><00:03:20.400><c> they're</c>

00:03:20.630 --> 00:03:20.640 align:start position:0%
actually fill that out when they're
 

00:03:20.640 --> 00:03:22.990 align:start position:0%
actually fill that out when they're
actually<00:03:20.840><c> running</c><00:03:21.200><c> the</c><00:03:21.360><c> model</c><00:03:21.760><c> in</c><00:03:22.000><c> here</c><00:03:22.680><c> so</c>

00:03:22.990 --> 00:03:23.000 align:start position:0%
actually running the model in here so
 

00:03:23.000 --> 00:03:25.589 align:start position:0%
actually running the model in here so
okay<00:03:23.239><c> I'm</c><00:03:23.400><c> going</c><00:03:23.560><c> to</c><00:03:23.840><c> save</c><00:03:24.319><c> this</c><00:03:25.159><c> okay</c><00:03:25.319><c> so</c><00:03:25.480><c> I</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
okay I'm going to save this okay so I
 

00:03:25.599 --> 00:03:27.589 align:start position:0%
okay I'm going to save this okay so I
saved<00:03:26.000><c> that</c><00:03:26.159><c> out</c><00:03:26.640><c> and</c><00:03:26.760><c> we</c><00:03:26.840><c> can</c><00:03:27.000><c> see</c><00:03:27.239><c> now</c><00:03:27.440><c> I've</c>

00:03:27.589 --> 00:03:27.599 align:start position:0%
saved that out and we can see now I've
 

00:03:27.599 --> 00:03:30.270 align:start position:0%
saved that out and we can see now I've
got<00:03:27.879><c> my</c><00:03:28.400><c> model</c><00:03:28.799><c> file</c><00:03:29.200><c> there</c><00:03:29.799><c> now</c><00:03:29.879><c> you</c><00:03:29.959><c> can</c><00:03:30.080><c> see</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
got my model file there now you can see
 

00:03:30.280 --> 00:03:32.789 align:start position:0%
got my model file there now you can see
that<00:03:30.400><c> I'm</c><00:03:30.599><c> going</c><00:03:30.799><c> to</c><00:03:31.400><c> be</c><00:03:31.680><c> making</c><00:03:32.080><c> a</c><00:03:32.239><c> model</c>

00:03:32.789 --> 00:03:32.799 align:start position:0%
that I'm going to be making a model
 

00:03:32.799 --> 00:03:35.589 align:start position:0%
that I'm going to be making a model
called<00:03:33.080><c> Jackalope</c><00:03:34.080><c> using</c><00:03:34.720><c> the</c><00:03:34.879><c> model</c><00:03:35.239><c> file</c>

00:03:35.589 --> 00:03:35.599 align:start position:0%
called Jackalope using the model file
 

00:03:35.599 --> 00:03:37.710 align:start position:0%
called Jackalope using the model file
Jackalope<00:03:36.200><c> that</c><00:03:36.319><c> we've</c><00:03:36.519><c> got</c><00:03:36.799><c> here</c><00:03:37.239><c> so</c><00:03:37.400><c> if</c><00:03:37.519><c> I</c>

00:03:37.710 --> 00:03:37.720 align:start position:0%
Jackalope that we've got here so if I
 

00:03:37.720 --> 00:03:39.949 align:start position:0%
Jackalope that we've got here so if I
run<00:03:38.000><c> that</c><00:03:38.480><c> you'll</c><00:03:38.640><c> see</c><00:03:38.840><c> it</c><00:03:39.000><c> passes</c><00:03:39.360><c> the</c><00:03:39.560><c> model</c>

00:03:39.949 --> 00:03:39.959 align:start position:0%
run that you'll see it passes the model
 

00:03:39.959 --> 00:03:41.990 align:start position:0%
run that you'll see it passes the model
file<00:03:40.720><c> it's</c><00:03:40.840><c> looking</c><00:03:41.080><c> for</c><00:03:41.239><c> a</c><00:03:41.360><c> model</c><00:03:41.760><c> it's</c><00:03:41.879><c> going</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
file it's looking for a model it's going
 

00:03:42.000 --> 00:03:43.949 align:start position:0%
file it's looking for a model it's going
to<00:03:42.239><c> create</c><00:03:42.680><c> the</c><00:03:42.840><c> various</c><00:03:43.159><c> layers</c><00:03:43.720><c> it's</c><00:03:43.879><c> going</c>

00:03:43.949 --> 00:03:43.959 align:start position:0%
to create the various layers it's going
 

00:03:43.959 --> 00:03:46.030 align:start position:0%
to create the various layers it's going
to<00:03:44.080><c> create</c><00:03:44.360><c> system</c><00:03:44.680><c> layer</c><00:03:45.000><c> Etc</c><00:03:45.760><c> and</c><00:03:45.840><c> then</c><00:03:45.959><c> it's</c>

00:03:46.030 --> 00:03:46.040 align:start position:0%
to create system layer Etc and then it's
 

00:03:46.040 --> 00:03:47.789 align:start position:0%
to create system layer Etc and then it's
going<00:03:46.159><c> to</c><00:03:46.319><c> write</c><00:03:46.599><c> the</c><00:03:46.760><c> weights</c><00:03:47.519><c> and</c><00:03:47.640><c> then</c>

00:03:47.789 --> 00:03:47.799 align:start position:0%
going to write the weights and then
 

00:03:47.799 --> 00:03:49.470 align:start position:0%
going to write the weights and then
finally<00:03:48.080><c> at</c><00:03:48.200><c> the</c><00:03:48.319><c> end</c><00:03:48.519><c> we'll</c><00:03:48.720><c> see</c><00:03:49.040><c> this</c>

00:03:49.470 --> 00:03:49.480 align:start position:0%
finally at the end we'll see this
 

00:03:49.480 --> 00:03:52.149 align:start position:0%
finally at the end we'll see this
success<00:03:50.439><c> here</c><00:03:51.080><c> now</c><00:03:51.239><c> if</c><00:03:51.360><c> I</c><00:03:51.480><c> come</c><00:03:51.640><c> in</c><00:03:51.760><c> and</c><00:03:51.920><c> have</c><00:03:52.040><c> a</c>

00:03:52.149 --> 00:03:52.159 align:start position:0%
success here now if I come in and have a
 

00:03:52.159 --> 00:03:54.910 align:start position:0%
success here now if I come in and have a
look<00:03:52.280><c> at</c><00:03:52.519><c> my</c><00:03:52.720><c> models</c><00:03:53.280><c> under</c><00:03:53.799><c> model</c><00:03:54.159><c> list</c>

00:03:54.910 --> 00:03:54.920 align:start position:0%
look at my models under model list
 

00:03:54.920 --> 00:03:56.710 align:start position:0%
look at my models under model list
you'll<00:03:55.159><c> see</c><00:03:55.439><c> that</c><00:03:55.799><c> I've</c><00:03:55.920><c> got</c><00:03:56.079><c> the</c><00:03:56.200><c> Hogwarts</c>

00:03:56.710 --> 00:03:56.720 align:start position:0%
you'll see that I've got the Hogwarts
 

00:03:56.720 --> 00:03:58.869 align:start position:0%
you'll see that I've got the Hogwarts
model<00:03:57.200><c> which</c><00:03:57.400><c> remember</c><00:03:57.760><c> is</c><00:03:58.040><c> just</c><00:03:58.239><c> a</c><00:03:58.439><c> different</c>

00:03:58.869 --> 00:03:58.879 align:start position:0%
model which remember is just a different
 

00:03:58.879 --> 00:04:01.789 align:start position:0%
model which remember is just a different
prompt<00:03:59.239><c> for</c><00:03:59.400><c> l</c><00:03:59.920><c> to</c><00:04:00.640><c> and</c><00:04:00.799><c> I've</c><00:04:01.000><c> now</c><00:04:01.239><c> got</c><00:04:01.519><c> this</c>

00:04:01.789 --> 00:04:01.799 align:start position:0%
prompt for l to and I've now got this
 

00:04:01.799 --> 00:04:04.390 align:start position:0%
prompt for l to and I've now got this
Jackalope<00:04:02.439><c> model</c><00:04:02.799><c> in</c><00:04:03.079><c> here</c><00:04:03.640><c> so</c><00:04:03.840><c> if</c><00:04:03.959><c> I</c><00:04:04.040><c> want</c><00:04:04.159><c> to</c>

00:04:04.390 --> 00:04:04.400 align:start position:0%
Jackalope model in here so if I want to
 

00:04:04.400 --> 00:04:06.589 align:start position:0%
Jackalope model in here so if I want to
use<00:04:04.720><c> this</c><00:04:04.920><c> model</c><00:04:05.439><c> I</c><00:04:05.519><c> can</c><00:04:05.720><c> just</c><00:04:05.879><c> come</c><00:04:06.079><c> in</c><00:04:06.400><c> and</c>

00:04:06.589 --> 00:04:06.599 align:start position:0%
use this model I can just come in and
 

00:04:06.599 --> 00:04:09.710 align:start position:0%
use this model I can just come in and
say<00:04:07.200><c> okay</c><00:04:07.400><c> Alama</c><00:04:08.200><c> run</c><00:04:08.760><c> Jackalope</c><00:04:09.400><c> and</c><00:04:09.480><c> it</c><00:04:09.599><c> will</c>

00:04:09.710 --> 00:04:09.720 align:start position:0%
say okay Alama run Jackalope and it will
 

00:04:09.720 --> 00:04:11.789 align:start position:0%
say okay Alama run Jackalope and it will
start<00:04:09.959><c> up</c><00:04:10.120><c> the</c><00:04:10.280><c> model</c><00:04:11.120><c> you</c><00:04:11.239><c> can</c><00:04:11.400><c> see</c><00:04:11.680><c> it's</c>

00:04:11.789 --> 00:04:11.799 align:start position:0%
start up the model you can see it's
 

00:04:11.799 --> 00:04:13.710 align:start position:0%
start up the model you can see it's
running<00:04:12.239><c> and</c><00:04:12.360><c> now</c><00:04:12.480><c> I</c><00:04:12.560><c> can</c><00:04:12.720><c> use</c><00:04:12.920><c> the</c><00:04:13.079><c> model</c><00:04:13.480><c> just</c>

00:04:13.710 --> 00:04:13.720 align:start position:0%
running and now I can use the model just
 

00:04:13.720 --> 00:04:16.509 align:start position:0%
running and now I can use the model just
like<00:04:13.879><c> I</c><00:04:14.000><c> would</c><00:04:14.400><c> any</c><00:04:14.640><c> other</c><00:04:14.879><c> model</c><00:04:15.280><c> in</c><00:04:15.480><c> here</c><00:04:16.199><c> so</c>

00:04:16.509 --> 00:04:16.519 align:start position:0%
like I would any other model in here so
 

00:04:16.519 --> 00:04:19.229 align:start position:0%
like I would any other model in here so
if<00:04:16.639><c> I</c><00:04:16.720><c> want</c><00:04:17.000><c> to</c><00:04:17.199><c> ask</c><00:04:17.400><c> it</c><00:04:17.560><c> a</c><00:04:17.799><c> question</c><00:04:18.759><c> I</c><00:04:18.840><c> can</c><00:04:19.000><c> do</c>

00:04:19.229 --> 00:04:19.239 align:start position:0%
if I want to ask it a question I can do
 

00:04:19.239 --> 00:04:21.550 align:start position:0%
if I want to ask it a question I can do
that<00:04:19.440><c> I</c><00:04:19.519><c> can</c><00:04:19.759><c> converse</c><00:04:20.239><c> with</c><00:04:20.400><c> it</c><00:04:20.639><c> just</c><00:04:20.919><c> like</c>

00:04:21.550 --> 00:04:21.560 align:start position:0%
that I can converse with it just like
 

00:04:21.560 --> 00:04:23.830 align:start position:0%
that I can converse with it just like
normal<00:04:22.400><c> if</c><00:04:22.520><c> I</c><00:04:22.639><c> want</c><00:04:22.759><c> to</c><00:04:22.840><c> see</c><00:04:23.040><c> the</c><00:04:23.160><c> commands</c><00:04:23.680><c> of</c>

00:04:23.830 --> 00:04:23.840 align:start position:0%
normal if I want to see the commands of
 

00:04:23.840 --> 00:04:25.430 align:start position:0%
normal if I want to see the commands of
what<00:04:23.919><c> I</c><00:04:24.040><c> can</c><00:04:24.199><c> do</c><00:04:24.479><c> I</c><00:04:24.560><c> can</c><00:04:24.680><c> basically</c><00:04:25.080><c> come</c><00:04:25.280><c> down</c>

00:04:25.430 --> 00:04:25.440 align:start position:0%
what I can do I can basically come down
 

00:04:25.440 --> 00:04:26.790 align:start position:0%
what I can do I can basically come down
and<00:04:25.560><c> see</c><00:04:25.840><c> that</c><00:04:26.040><c> and</c><00:04:26.160><c> you'll</c><00:04:26.280><c> see</c><00:04:26.479><c> that</c><00:04:26.600><c> it's</c>

00:04:26.790 --> 00:04:26.800 align:start position:0%
and see that and you'll see that it's
 

00:04:26.800 --> 00:04:28.990 align:start position:0%
and see that and you'll see that it's
just<00:04:27.040><c> like</c><00:04:27.280><c> normal</c><00:04:27.960><c> for</c><00:04:28.280><c> these</c><00:04:28.520><c> particular</c>

00:04:28.990 --> 00:04:29.000 align:start position:0%
just like normal for these particular
 

00:04:29.000 --> 00:04:32.189 align:start position:0%
just like normal for these particular
things<00:04:29.880><c> so</c><00:04:30.639><c> this</c><00:04:30.759><c> is</c><00:04:31.000><c> basically</c><00:04:31.479><c> how</c><00:04:31.639><c> to</c><00:04:31.880><c> set</c>

00:04:32.189 --> 00:04:32.199 align:start position:0%
things so this is basically how to set
 

00:04:32.199 --> 00:04:35.670 align:start position:0%
things so this is basically how to set
up<00:04:32.560><c> a</c><00:04:32.800><c> custom</c><00:04:33.240><c> model</c><00:04:34.039><c> how</c><00:04:34.199><c> to</c><00:04:34.680><c> use</c><00:04:34.919><c> it</c><00:04:35.479><c> there</c>

00:04:35.670 --> 00:04:35.680 align:start position:0%
up a custom model how to use it there
 

00:04:35.680 --> 00:04:37.110 align:start position:0%
up a custom model how to use it there
probably<00:04:35.919><c> are</c><00:04:36.120><c> some</c><00:04:36.320><c> models</c><00:04:36.680><c> that</c><00:04:36.759><c> are</c><00:04:36.919><c> not</c>

00:04:37.110 --> 00:04:37.120 align:start position:0%
probably are some models that are not
 

00:04:37.120 --> 00:04:39.469 align:start position:0%
probably are some models that are not
going<00:04:37.240><c> to</c><00:04:37.400><c> work</c><00:04:38.360><c> but</c><00:04:38.520><c> certainly</c><00:04:38.880><c> all</c><00:04:39.039><c> the</c><00:04:39.160><c> fine</c>

00:04:39.469 --> 00:04:39.479 align:start position:0%
going to work but certainly all the fine
 

00:04:39.479 --> 00:04:42.390 align:start position:0%
going to work but certainly all the fine
tunes<00:04:39.960><c> of</c><00:04:40.160><c> like</c><00:04:40.360><c> llama</c><00:04:40.800><c> 2</c><00:04:41.560><c> the</c><00:04:41.680><c> fine</c><00:04:41.919><c> tunes</c><00:04:42.199><c> of</c>

00:04:42.390 --> 00:04:42.400 align:start position:0%
tunes of like llama 2 the fine tunes of
 

00:04:42.400 --> 00:04:45.790 align:start position:0%
tunes of like llama 2 the fine tunes of
mistel<00:04:43.080><c> even</c><00:04:43.360><c> the</c><00:04:43.520><c> Falcon</c><00:04:43.960><c> models</c><00:04:44.600><c> Etc</c><00:04:45.600><c> will</c>

00:04:45.790 --> 00:04:45.800 align:start position:0%
mistel even the Falcon models Etc will
 

00:04:45.800 --> 00:04:48.029 align:start position:0%
mistel even the Falcon models Etc will
work<00:04:46.039><c> for</c><00:04:46.280><c> this</c><00:04:47.080><c> so</c><00:04:47.280><c> this</c><00:04:47.400><c> is</c><00:04:47.560><c> something</c><00:04:47.919><c> that</c>

00:04:48.029 --> 00:04:48.039 align:start position:0%
work for this so this is something that
 

00:04:48.039 --> 00:04:50.270 align:start position:0%
work for this so this is something that
you<00:04:48.160><c> can</c><00:04:48.440><c> definitely</c><00:04:49.280><c> use</c><00:04:49.560><c> to</c><00:04:49.800><c> basically</c>

00:04:50.270 --> 00:04:50.280 align:start position:0%
you can definitely use to basically
 

00:04:50.280 --> 00:04:52.670 align:start position:0%
you can definitely use to basically
start<00:04:50.560><c> trying</c><00:04:51.000><c> out</c><00:04:51.320><c> different</c><00:04:51.639><c> models</c><00:04:52.560><c> that</c>

00:04:52.670 --> 00:04:52.680 align:start position:0%
start trying out different models that
 

00:04:52.680 --> 00:04:55.230 align:start position:0%
start trying out different models that
you<00:04:52.800><c> see</c><00:04:53.080><c> on</c><00:04:53.199><c> the</c><00:04:53.320><c> hugging</c><00:04:53.720><c> face</c><00:04:54.039><c> Hub</c><00:04:54.400><c> there</c>

00:04:55.230 --> 00:04:55.240 align:start position:0%
you see on the hugging face Hub there
 

00:04:55.240 --> 00:04:56.870 align:start position:0%
you see on the hugging face Hub there
anyway<00:04:55.680><c> as</c><00:04:55.840><c> always</c><00:04:56.199><c> if</c><00:04:56.280><c> You'</c><00:04:56.440><c> got</c><00:04:56.600><c> any</c>

00:04:56.870 --> 00:04:56.880 align:start position:0%
anyway as always if You' got any
 

00:04:56.880 --> 00:04:58.870 align:start position:0%
anyway as always if You' got any
questions<00:04:57.360><c> or</c><00:04:57.560><c> any</c><00:04:57.720><c> comments</c><00:04:58.240><c> put</c><00:04:58.400><c> them</c><00:04:58.800><c> in</c>

00:04:58.870 --> 00:04:58.880 align:start position:0%
questions or any comments put them in
 

00:04:58.880 --> 00:05:00.990 align:start position:0%
questions or any comments put them in
the<00:04:59.000><c> comment</c><00:04:59.240><c> section</c><00:04:59.720><c> below</c><00:05:00.560><c> please</c><00:05:00.800><c> click</c>

00:05:00.990 --> 00:05:01.000 align:start position:0%
the comment section below please click
 

00:05:01.000 --> 00:05:02.749 align:start position:0%
the comment section below please click
like<00:05:01.160><c> And</c><00:05:01.360><c> subscribe</c><00:05:02.080><c> it</c><00:05:02.160><c> will</c><00:05:02.320><c> help</c><00:05:02.520><c> people</c>

00:05:02.749 --> 00:05:02.759 align:start position:0%
like And subscribe it will help people
 

00:05:02.759 --> 00:05:04.749 align:start position:0%
like And subscribe it will help people
see<00:05:03.039><c> the</c><00:05:03.199><c> video</c><00:05:04.039><c> I</c><00:05:04.120><c> will</c><00:05:04.240><c> talk</c><00:05:04.400><c> to</c><00:05:04.479><c> you</c><00:05:04.560><c> in</c><00:05:04.639><c> the</c>

00:05:04.749 --> 00:05:04.759 align:start position:0%
see the video I will talk to you in the
 

00:05:04.759 --> 00:05:08.240 align:start position:0%
see the video I will talk to you in the
next<00:05:04.960><c> video</c><00:05:05.520><c> bye</c><00:05:05.680><c> for</c><00:05:05.880><c> now</c>

