WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:02.030 align:start position:0%
 
hello<00:00:00.480><c> and</c><00:00:00.750><c> welcome</c><00:00:01.079><c> to</c><00:00:01.260><c> an</c><00:00:01.620><c> HTML</c>

00:00:02.030 --> 00:00:02.040 align:start position:0%
hello and welcome to an HTML
 

00:00:02.040 --> 00:00:04.490 align:start position:0%
hello and welcome to an HTML
introduction<00:00:02.610><c> so</c><00:00:03.570><c> we're</c><00:00:04.080><c> gonna</c><00:00:04.200><c> get</c><00:00:04.290><c> right</c>

00:00:04.490 --> 00:00:04.500 align:start position:0%
introduction so we're gonna get right
 

00:00:04.500 --> 00:00:05.869 align:start position:0%
introduction so we're gonna get right
into<00:00:04.620><c> it</c><00:00:04.770><c> but</c><00:00:05.160><c> the</c><00:00:05.279><c> first</c><00:00:05.460><c> thing</c><00:00:05.670><c> I</c><00:00:05.730><c> want</c><00:00:05.850><c> to</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
into it but the first thing I want to
 

00:00:05.879 --> 00:00:09.230 align:start position:0%
into it but the first thing I want to
talk<00:00:06.000><c> about</c><00:00:06.150><c> is</c><00:00:07.370><c> understanding</c><00:00:08.370><c> the</c><00:00:08.639><c> context</c>

00:00:09.230 --> 00:00:09.240 align:start position:0%
talk about is understanding the context
 

00:00:09.240 --> 00:00:12.530 align:start position:0%
talk about is understanding the context
of<00:00:09.389><c> how</c><00:00:09.780><c> a</c><00:00:09.809><c> smell</c><00:00:10.200><c> is</c><00:00:10.320><c> used</c><00:00:10.590><c> okay</c><00:00:11.040><c> so</c><00:00:11.820><c> if</c><00:00:12.330><c> you</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
of how a smell is used okay so if you
 

00:00:12.540 --> 00:00:16.189 align:start position:0%
of how a smell is used okay so if you
think<00:00:12.809><c> of</c><00:00:13.019><c> a</c><00:00:13.650><c> news</c><00:00:14.009><c> article</c><00:00:14.690><c> it</c><00:00:15.690><c> has</c><00:00:15.839><c> a</c><00:00:15.870><c> title</c>

00:00:16.189 --> 00:00:16.199 align:start position:0%
think of a news article it has a title
 

00:00:16.199 --> 00:00:20.120 align:start position:0%
think of a news article it has a title
of<00:00:17.130><c> subtitle</c><00:00:17.960><c> maybe</c><00:00:18.960><c> an</c><00:00:19.109><c> author</c><00:00:19.289><c> and</c><00:00:19.800><c> a</c><00:00:19.890><c> date</c>

00:00:20.120 --> 00:00:20.130 align:start position:0%
of subtitle maybe an author and a date
 

00:00:20.130 --> 00:00:22.910 align:start position:0%
of subtitle maybe an author and a date
and<00:00:20.550><c> it</c><00:00:21.300><c> has</c><00:00:21.390><c> a</c><00:00:21.420><c> bunch</c><00:00:21.689><c> of</c><00:00:21.750><c> text</c><00:00:22.080><c> describing</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
and it has a bunch of text describing
 

00:00:22.920 --> 00:00:26.630 align:start position:0%
and it has a bunch of text describing
what<00:00:23.460><c> article</c><00:00:23.970><c> is</c><00:00:24.060><c> about</c><00:00:24.800><c> so</c><00:00:25.800><c> now</c><00:00:26.010><c> if</c><00:00:26.310><c> you</c><00:00:26.550><c> were</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
what article is about so now if you were
 

00:00:26.640 --> 00:00:29.359 align:start position:0%
what article is about so now if you were
to<00:00:26.670><c> open</c><00:00:26.820><c> up</c><00:00:27.029><c> a</c><00:00:27.119><c> web</c><00:00:27.269><c> page</c><00:00:27.590><c> then</c><00:00:28.590><c> it's</c><00:00:28.980><c> kind</c><00:00:29.279><c> of</c>

00:00:29.359 --> 00:00:29.369 align:start position:0%
to open up a web page then it's kind of
 

00:00:29.369 --> 00:00:31.550 align:start position:0%
to open up a web page then it's kind of
the<00:00:29.519><c> same</c><00:00:29.699><c> deal</c><00:00:29.760><c> if</c><00:00:30.240><c> you</c><00:00:30.480><c> went</c><00:00:30.840><c> to</c><00:00:30.900><c> a</c><00:00:30.990><c> new</c><00:00:31.289><c> site</c>

00:00:31.550 --> 00:00:31.560 align:start position:0%
the same deal if you went to a new site
 

00:00:31.560 --> 00:00:34.750 align:start position:0%
the same deal if you went to a new site
they<00:00:32.460><c> have</c><00:00:32.489><c> a</c><00:00:32.610><c> title</c><00:00:33.000><c> they</c><00:00:33.750><c> have</c><00:00:33.870><c> a</c><00:00:33.899><c> subtitle</c>

00:00:34.750 --> 00:00:34.760 align:start position:0%
they have a title they have a subtitle
 

00:00:34.760 --> 00:00:38.930 align:start position:0%
they have a title they have a subtitle
author<00:00:35.760><c> and</c><00:00:36.300><c> a</c><00:00:36.329><c> date</c><00:00:36.540><c> and</c><00:00:37.190><c> then</c><00:00:38.190><c> there's</c><00:00:38.700><c> the</c>

00:00:38.930 --> 00:00:38.940 align:start position:0%
author and a date and then there's the
 

00:00:38.940 --> 00:00:41.360 align:start position:0%
author and a date and then there's the
whole<00:00:39.090><c> text</c><00:00:39.710><c> describing</c><00:00:40.710><c> what</c><00:00:41.129><c> they're</c><00:00:41.250><c> all</c>

00:00:41.360 --> 00:00:41.370 align:start position:0%
whole text describing what they're all
 

00:00:41.370 --> 00:00:42.740 align:start position:0%
whole text describing what they're all
about<00:00:41.399><c> and</c><00:00:41.940><c> then</c><00:00:42.420><c> there</c><00:00:42.510><c> might</c><00:00:42.600><c> be</c><00:00:42.660><c> some</c>

00:00:42.740 --> 00:00:42.750 align:start position:0%
about and then there might be some
 

00:00:42.750 --> 00:00:45.200 align:start position:0%
about and then there might be some
comments<00:00:43.290><c> underneath</c><00:00:43.500><c> of</c><00:00:43.739><c> that</c><00:00:44.030><c> so</c><00:00:45.030><c> that's</c>

00:00:45.200 --> 00:00:45.210 align:start position:0%
comments underneath of that so that's
 

00:00:45.210 --> 00:00:48.440 align:start position:0%
comments underneath of that so that's
the<00:00:45.390><c> structure</c><00:00:45.899><c> and</c><00:00:46.140><c> so</c><00:00:46.649><c> that's</c><00:00:46.920><c> what</c><00:00:47.450><c> that's</c>

00:00:48.440 --> 00:00:48.450 align:start position:0%
the structure and so that's what that's
 

00:00:48.450 --> 00:00:51.290 align:start position:0%
the structure and so that's what that's
what<00:00:48.690><c> HTML</c><00:00:49.260><c> does</c><00:00:49.500><c> it</c><00:00:49.800><c> presents</c><00:00:49.980><c> the</c><00:00:50.789><c> structure</c>

00:00:51.290 --> 00:00:51.300 align:start position:0%
what HTML does it presents the structure
 

00:00:51.300 --> 00:00:55.580 align:start position:0%
what HTML does it presents the structure
of<00:00:51.480><c> the</c><00:00:51.719><c> web</c><00:00:51.870><c> page</c><00:00:52.079><c> okay</c><00:00:53.539><c> so</c><00:00:54.539><c> what</c><00:00:54.899><c> does</c><00:00:55.050><c> the</c>

00:00:55.580 --> 00:00:55.590 align:start position:0%
of the web page okay so what does the
 

00:00:55.590 --> 00:01:01.790 align:start position:0%
of the web page okay so what does the
HTML<00:00:56.430><c> acronym</c><00:00:57.000><c> even</c><00:00:57.570><c> mean</c><00:00:57.780><c> all</c><00:00:58.320><c> right</c><00:00:58.500><c> so</c><00:01:00.800><c> HTML</c>

00:01:01.790 --> 00:01:01.800 align:start position:0%
HTML acronym even mean all right so HTML
 

00:01:01.800 --> 00:01:04.539 align:start position:0%
HTML acronym even mean all right so HTML
stands<00:01:02.100><c> for</c><00:01:02.160><c> hypertext</c><00:01:03.080><c> markup</c><00:01:04.080><c> language</c>

00:01:04.539 --> 00:01:04.549 align:start position:0%
stands for hypertext markup language
 

00:01:04.549 --> 00:01:07.160 align:start position:0%
stands for hypertext markup language
hypertext<00:01:05.549><c> is</c><00:01:05.640><c> what</c><00:01:05.850><c> allows</c><00:01:06.119><c> you</c><00:01:06.390><c> to</c><00:01:06.600><c> click</c><00:01:07.110><c> on</c>

00:01:07.160 --> 00:01:07.170 align:start position:0%
hypertext is what allows you to click on
 

00:01:07.170 --> 00:01:08.899 align:start position:0%
hypertext is what allows you to click on
a<00:01:07.320><c> link</c><00:01:07.380><c> within</c><00:01:08.010><c> a</c><00:01:08.159><c> web</c><00:01:08.189><c> page</c><00:01:08.490><c> and</c><00:01:08.760><c> it</c>

00:01:08.899 --> 00:01:08.909 align:start position:0%
a link within a web page and it
 

00:01:08.909 --> 00:01:11.690 align:start position:0%
a link within a web page and it
redirects<00:01:09.299><c> you</c><00:01:09.390><c> somewhere</c><00:01:09.780><c> else</c><00:01:10.700><c> markup</c>

00:01:11.690 --> 00:01:11.700 align:start position:0%
redirects you somewhere else markup
 

00:01:11.700 --> 00:01:15.530 align:start position:0%
redirects you somewhere else markup
language<00:01:12.060><c> is</c><00:01:12.290><c> what</c><00:01:13.290><c> allows</c><00:01:13.530><c> us</c><00:01:13.560><c> to</c><00:01:14.540><c> describe</c>

00:01:15.530 --> 00:01:15.540 align:start position:0%
language is what allows us to describe
 

00:01:15.540 --> 00:01:19.880 align:start position:0%
language is what allows us to describe
text<00:01:16.140><c> so</c><00:01:16.729><c> typically</c><00:01:17.729><c> you</c><00:01:17.909><c> use</c><00:01:17.970><c> tags</c><00:01:18.680><c> and</c><00:01:19.680><c> that</c>

00:01:19.880 --> 00:01:19.890 align:start position:0%
text so typically you use tags and that
 

00:01:19.890 --> 00:01:21.830 align:start position:0%
text so typically you use tags and that
and<00:01:20.130><c> those</c><00:01:20.490><c> tags</c><00:01:20.759><c> will</c><00:01:20.939><c> surround</c><00:01:21.270><c> some</c><00:01:21.540><c> text</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
and those tags will surround some text
 

00:01:21.840 --> 00:01:25.179 align:start position:0%
and those tags will surround some text
and<00:01:21.990><c> then</c><00:01:22.590><c> that's</c><00:01:23.159><c> how</c><00:01:23.400><c> we</c><00:01:23.880><c> want</c><00:01:24.090><c> to</c><00:01:24.119><c> describe</c>

00:01:25.179 --> 00:01:25.189 align:start position:0%
and then that's how we want to describe
 

00:01:25.189 --> 00:01:28.490 align:start position:0%
and then that's how we want to describe
that<00:01:26.189><c> text</c><00:01:26.549><c> to</c><00:01:26.640><c> the</c><00:01:26.729><c> browser</c><00:01:26.970><c> so</c><00:01:27.500><c> it'll</c>

00:01:28.490 --> 00:01:28.500 align:start position:0%
that text to the browser so it'll
 

00:01:28.500 --> 00:01:30.260 align:start position:0%
that text to the browser so it'll
display<00:01:28.680><c> in</c><00:01:28.890><c> a</c><00:01:28.979><c> certain</c><00:01:29.280><c> way</c><00:01:29.430><c> okay</c><00:01:29.970><c> and</c><00:01:30.180><c> those</c>

00:01:30.260 --> 00:01:30.270 align:start position:0%
display in a certain way okay and those
 

00:01:30.270 --> 00:01:34.100 align:start position:0%
display in a certain way okay and those
tags<00:01:30.630><c> or</c><00:01:30.990><c> what</c><00:01:31.590><c> we</c><00:01:31.710><c> call</c><00:01:31.950><c> the</c><00:01:32.280><c> markup</c><00:01:33.049><c> okay</c><00:01:34.049><c> so</c>

00:01:34.100 --> 00:01:34.110 align:start position:0%
tags or what we call the markup okay so
 

00:01:34.110 --> 00:01:38.120 align:start position:0%
tags or what we call the markup okay so
here<00:01:34.650><c> we</c><00:01:34.740><c> have</c><00:01:34.890><c> an</c><00:01:35.250><c> example</c><00:01:36.150><c> of</c><00:01:36.659><c> a</c><00:01:37.079><c> simple</c><00:01:37.439><c> HTML</c>

00:01:38.120 --> 00:01:38.130 align:start position:0%
here we have an example of a simple HTML
 

00:01:38.130 --> 00:01:43.280 align:start position:0%
here we have an example of a simple HTML
page<00:01:38.840><c> all</c><00:01:39.840><c> anything</c><00:01:40.380><c> in</c><00:01:40.799><c> blue</c><00:01:41.159><c> is</c><00:01:41.750><c> a</c><00:01:42.750><c> tag</c><00:01:43.049><c> and</c>

00:01:43.280 --> 00:01:43.290 align:start position:0%
page all anything in blue is a tag and
 

00:01:43.290 --> 00:01:45.889 align:start position:0%
page all anything in blue is a tag and
what<00:01:43.950><c> I</c><00:01:43.979><c> have</c><00:01:44.130><c> in</c><00:01:44.250><c> green</c><00:01:44.490><c> is</c><00:01:44.670><c> also</c><00:01:44.850><c> a</c><00:01:45.240><c> tag</c><00:01:45.689><c> but</c>

00:01:45.889 --> 00:01:45.899 align:start position:0%
what I have in green is also a tag but
 

00:01:45.899 --> 00:01:48.410 align:start position:0%
what I have in green is also a tag but
that's<00:01:46.110><c> just</c><00:01:46.259><c> that's</c><00:01:47.159><c> like</c><00:01:47.310><c> default</c><00:01:48.060><c> for</c>

00:01:48.410 --> 00:01:48.420 align:start position:0%
that's just that's like default for
 

00:01:48.420 --> 00:01:50.410 align:start position:0%
that's just that's like default for
every<00:01:48.600><c> HTML</c><00:01:48.930><c> page</c><00:01:49.110><c> it</c><00:01:49.740><c> needs</c><00:01:49.950><c> one</c><00:01:50.070><c> of</c><00:01:50.100><c> those</c>

00:01:50.410 --> 00:01:50.420 align:start position:0%
every HTML page it needs one of those
 

00:01:50.420 --> 00:01:53.810 align:start position:0%
every HTML page it needs one of those
but<00:01:51.560><c> then</c><00:01:52.560><c> anything</c><00:01:52.829><c> in</c><00:01:53.100><c> white</c><00:01:53.280><c> is</c><00:01:53.520><c> just</c><00:01:53.729><c> the</c>

00:01:53.810 --> 00:01:53.820 align:start position:0%
but then anything in white is just the
 

00:01:53.820 --> 00:01:57.770 align:start position:0%
but then anything in white is just the
text<00:01:54.240><c> just</c><00:01:54.390><c> simple</c><00:01:54.960><c> text</c><00:01:54.990><c> and</c><00:01:55.470><c> as</c><00:01:56.479><c> you</c><00:01:57.479><c> notice</c>

00:01:57.770 --> 00:01:57.780 align:start position:0%
text just simple text and as you notice
 

00:01:57.780 --> 00:02:00.469 align:start position:0%
text just simple text and as you notice
the<00:01:57.960><c> tags</c><00:01:58.200><c> are</c><00:01:59.070><c> surrounding</c><00:01:59.700><c> the</c><00:01:59.969><c> text</c><00:02:00.329><c> and</c>

00:02:00.469 --> 00:02:00.479 align:start position:0%
the tags are surrounding the text and
 

00:02:00.479 --> 00:02:02.840 align:start position:0%
the tags are surrounding the text and
they're<00:02:01.409><c> there</c><00:02:01.710><c> so</c><00:02:02.100><c> that</c><00:02:02.280><c> the</c><00:02:02.430><c> browser</c><00:02:02.670><c> and</c>

00:02:02.840 --> 00:02:02.850 align:start position:0%
they're there so that the browser and
 

00:02:02.850 --> 00:02:05.300 align:start position:0%
they're there so that the browser and
there's<00:02:03.000><c> how</c><00:02:03.210><c> we</c><00:02:03.270><c> want</c><00:02:03.630><c> to</c><00:02:04.259><c> display</c><00:02:04.680><c> that</c><00:02:04.710><c> text</c>

00:02:05.300 --> 00:02:05.310 align:start position:0%
there's how we want to display that text
 

00:02:05.310 --> 00:02:09.320 align:start position:0%
there's how we want to display that text
on<00:02:05.430><c> the</c><00:02:05.759><c> screen</c><00:02:06.710><c> so</c><00:02:07.710><c> let's</c><00:02:08.099><c> go</c><00:02:08.280><c> let's</c><00:02:09.119><c> take</c><00:02:09.270><c> a</c>

00:02:09.320 --> 00:02:09.330 align:start position:0%
on the screen so let's go let's take a
 

00:02:09.330 --> 00:02:12.670 align:start position:0%
on the screen so let's go let's take a
look<00:02:09.509><c> at</c><00:02:09.629><c> the</c><00:02:09.750><c> title</c>

00:02:12.670 --> 00:02:12.680 align:start position:0%
 
 

00:02:12.680 --> 00:02:16.869 align:start position:0%
 
the<00:02:12.799><c> title</c><00:02:13.159><c> as</c><00:02:13.909><c> you</c><00:02:14.569><c> can</c><00:02:14.720><c> see</c><00:02:14.930><c> here</c><00:02:15.879><c> actually</c>

00:02:16.869 --> 00:02:16.879 align:start position:0%
the title as you can see here actually
 

00:02:16.879 --> 00:02:20.559 align:start position:0%
the title as you can see here actually
there's<00:02:17.060><c> two</c><00:02:17.390><c> tags</c><00:02:17.689><c> so</c><00:02:18.760><c> one</c><00:02:19.760><c> on</c><00:02:19.819><c> the</c><00:02:19.909><c> left</c><00:02:20.120><c> with</c>

00:02:20.559 --> 00:02:20.569 align:start position:0%
there's two tags so one on the left with
 

00:02:20.569 --> 00:02:22.420 align:start position:0%
there's two tags so one on the left with
the<00:02:20.599><c> left</c><00:02:20.810><c> carrot</c><00:02:21.110><c> and</c><00:02:21.200><c> right</c><00:02:21.349><c> carrot</c><00:02:21.709><c> this</c><00:02:22.250><c> is</c>

00:02:22.420 --> 00:02:22.430 align:start position:0%
the left carrot and right carrot this is
 

00:02:22.430 --> 00:02:25.420 align:start position:0%
the left carrot and right carrot this is
called<00:02:22.730><c> an</c><00:02:23.030><c> opening</c><00:02:23.390><c> tag</c><00:02:23.890><c> yellow</c><00:02:24.890><c> you</c><00:02:25.189><c> always</c>

00:02:25.420 --> 00:02:25.430 align:start position:0%
called an opening tag yellow you always
 

00:02:25.430 --> 00:02:28.449 align:start position:0%
called an opening tag yellow you always
need<00:02:25.579><c> an</c><00:02:25.730><c> opening</c><00:02:26.000><c> tag</c><00:02:26.209><c> okay</c><00:02:26.720><c> and</c><00:02:26.930><c> then</c><00:02:27.459><c> the</c>

00:02:28.449 --> 00:02:28.459 align:start position:0%
need an opening tag okay and then the
 

00:02:28.459 --> 00:02:29.949 align:start position:0%
need an opening tag okay and then the
white<00:02:28.610><c> text</c><00:02:28.909><c> in</c><00:02:28.939><c> the</c><00:02:29.060><c> middle</c><00:02:29.150><c> then</c><00:02:29.689><c> the</c><00:02:29.720><c> web</c>

00:02:29.949 --> 00:02:29.959 align:start position:0%
white text in the middle then the web
 

00:02:29.959 --> 00:02:33.280 align:start position:0%
white text in the middle then the web
page<00:02:30.200><c> is</c><00:02:30.530><c> what</c><00:02:31.370><c> we</c><00:02:31.489><c> want</c><00:02:31.760><c> displayed</c><00:02:32.090><c> and</c><00:02:32.629><c> then</c>

00:02:33.280 --> 00:02:33.290 align:start position:0%
page is what we want displayed and then
 

00:02:33.290 --> 00:02:37.030 align:start position:0%
page is what we want displayed and then
in<00:02:33.709><c> order</c><00:02:34.159><c> to</c><00:02:34.420><c> and</c><00:02:35.420><c> where</c><00:02:36.170><c> for</c><00:02:36.319><c> the</c><00:02:36.379><c> browser</c><00:02:36.590><c> to</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
in order to and where for the browser to
 

00:02:37.040 --> 00:02:40.119 align:start position:0%
in order to and where for the browser to
know<00:02:37.220><c> we</c><00:02:38.000><c> only</c><00:02:38.209><c> want</c><00:02:38.569><c> this</c><00:02:39.260><c> text</c><00:02:39.650><c> meets</c><00:02:40.040><c> a</c>

00:02:40.119 --> 00:02:40.129 align:start position:0%
know we only want this text meets a
 

00:02:40.129 --> 00:02:43.240 align:start position:0%
know we only want this text meets a
title<00:02:40.579><c> you</c><00:02:41.209><c> have</c><00:02:41.390><c> to</c><00:02:41.629><c> have</c><00:02:42.230><c> a</c><00:02:42.260><c> closing</c><00:02:42.650><c> tag</c><00:02:43.040><c> and</c>

00:02:43.240 --> 00:02:43.250 align:start position:0%
title you have to have a closing tag and
 

00:02:43.250 --> 00:02:45.670 align:start position:0%
title you have to have a closing tag and
that's<00:02:44.209><c> what</c><00:02:44.540><c> is</c><00:02:44.750><c> displayed</c><00:02:45.170><c> on</c><00:02:45.200><c> the</c><00:02:45.379><c> right</c>

00:02:45.670 --> 00:02:45.680 align:start position:0%
that's what is displayed on the right
 

00:02:45.680 --> 00:02:48.970 align:start position:0%
that's what is displayed on the right
with<00:02:46.220><c> a</c><00:02:46.519><c> backslash</c><00:02:47.500><c> all</c><00:02:48.500><c> right</c><00:02:48.739><c> so</c><00:02:48.859><c> we're</c>

00:02:48.970 --> 00:02:48.980 align:start position:0%
with a backslash all right so we're
 

00:02:48.980 --> 00:02:50.530 align:start position:0%
with a backslash all right so we're
going<00:02:49.069><c> to</c><00:02:49.190><c> get</c><00:02:49.250><c> right</c><00:02:49.459><c> into</c><00:02:49.519><c> it</c><00:02:49.790><c> and</c><00:02:50.329><c> we're</c>

00:02:50.530 --> 00:02:50.540 align:start position:0%
going to get right into it and we're
 

00:02:50.540 --> 00:02:52.690 align:start position:0%
going to get right into it and we're
going<00:02:50.659><c> to</c><00:02:50.720><c> start</c><00:02:50.959><c> coding</c><00:02:51.920><c> our</c><00:02:52.099><c> first</c><00:02:52.489><c> HTML</c>

00:02:52.690 --> 00:02:52.700 align:start position:0%
going to start coding our first HTML
 

00:02:52.700 --> 00:02:57.160 align:start position:0%
going to start coding our first HTML
page<00:02:53.470><c> okay</c><00:02:54.470><c> so</c><00:02:54.670><c> just</c><00:02:55.670><c> before</c><00:02:56.329><c> we</c><00:02:56.840><c> make</c><00:02:56.989><c> one</c>

00:02:57.160 --> 00:02:57.170 align:start position:0%
page okay so just before we make one
 

00:02:57.170 --> 00:02:59.979 align:start position:0%
page okay so just before we make one
thing<00:02:57.379><c> kind</c><00:02:57.799><c> of</c><00:02:57.920><c> clear</c><00:02:58.599><c> hTML</c><00:02:59.599><c> is</c><00:02:59.629><c> not</c><00:02:59.959><c> a</c>

00:02:59.979 --> 00:02:59.989 align:start position:0%
thing kind of clear hTML is not a
 

00:02:59.989 --> 00:03:01.839 align:start position:0%
thing kind of clear hTML is not a
programming<00:03:00.530><c> language</c><00:03:00.560><c> it's</c><00:03:01.280><c> a</c><00:03:01.489><c> markup</c>

00:03:01.839 --> 00:03:01.849 align:start position:0%
programming language it's a markup
 

00:03:01.849 --> 00:03:04.929 align:start position:0%
programming language it's a markup
language<00:03:02.239><c> it's</c><00:03:02.510><c> in</c><00:03:02.870><c> the</c><00:03:03.079><c> name</c><00:03:03.819><c> typically</c><00:03:04.819><c> a</c>

00:03:04.929 --> 00:03:04.939 align:start position:0%
language it's in the name typically a
 

00:03:04.939 --> 00:03:07.089 align:start position:0%
language it's in the name typically a
programming<00:03:05.569><c> language</c><00:03:05.930><c> you</c><00:03:06.319><c> can</c><00:03:06.530><c> invoke</c><00:03:06.799><c> some</c>

00:03:07.089 --> 00:03:07.099 align:start position:0%
programming language you can invoke some
 

00:03:07.099 --> 00:03:11.199 align:start position:0%
programming language you can invoke some
logic<00:03:07.629><c> with</c><00:03:08.629><c> your</c><00:03:08.810><c> code</c><00:03:09.049><c> and</c><00:03:09.790><c> we</c><00:03:10.790><c> can't</c><00:03:11.120><c> do</c>

00:03:11.199 --> 00:03:11.209 align:start position:0%
logic with your code and we can't do
 

00:03:11.209 --> 00:03:15.580 align:start position:0%
logic with your code and we can't do
that<00:03:11.329><c> with</c><00:03:11.480><c> HTML</c><00:03:12.639><c> it's</c><00:03:13.639><c> only</c><00:03:13.970><c> there</c><00:03:14.209><c> again</c><00:03:14.840><c> to</c>

00:03:15.580 --> 00:03:15.590 align:start position:0%
that with HTML it's only there again to
 

00:03:15.590 --> 00:03:18.369 align:start position:0%
that with HTML it's only there again to
structure<00:03:16.189><c> the</c><00:03:16.849><c> web</c><00:03:17.090><c> page</c><00:03:17.299><c> it's</c><00:03:18.199><c> very</c>

00:03:18.369 --> 00:03:18.379 align:start position:0%
structure the web page it's very
 

00:03:18.379 --> 00:03:21.640 align:start position:0%
structure the web page it's very
important<00:03:18.769><c> but</c><00:03:19.400><c> that's</c><00:03:20.169><c> that's</c><00:03:21.169><c> why</c><00:03:21.470><c> we're</c>

00:03:21.640 --> 00:03:21.650 align:start position:0%
important but that's that's why we're
 

00:03:21.650 --> 00:03:23.800 align:start position:0%
important but that's that's why we're
using<00:03:21.739><c> HTML</c><00:03:22.430><c> it's</c><00:03:22.790><c> just</c><00:03:22.940><c> a</c><00:03:23.030><c> structure</c><00:03:23.359><c> how</c><00:03:23.750><c> we</c>

00:03:23.800 --> 00:03:23.810 align:start position:0%
using HTML it's just a structure how we
 

00:03:23.810 --> 00:03:26.199 align:start position:0%
using HTML it's just a structure how we
want<00:03:24.169><c> everything</c><00:03:24.500><c> to</c><00:03:24.530><c> be</c><00:03:24.949><c> displayed</c><00:03:25.370><c> on</c><00:03:25.819><c> the</c>

00:03:26.199 --> 00:03:26.209 align:start position:0%
want everything to be displayed on the
 

00:03:26.209 --> 00:03:29.710 align:start position:0%
want everything to be displayed on the
web<00:03:26.329><c> page</c><00:03:26.599><c> okay</c><00:03:27.159><c> so</c><00:03:28.159><c> let's</c><00:03:29.090><c> get</c><00:03:29.209><c> started</c><00:03:29.540><c> and</c>

00:03:29.710 --> 00:03:29.720 align:start position:0%
web page okay so let's get started and
 

00:03:29.720 --> 00:03:31.360 align:start position:0%
web page okay so let's get started and
you<00:03:29.930><c> don't</c><00:03:30.109><c> need</c><00:03:30.260><c> any</c><00:03:30.409><c> special</c><00:03:30.650><c> text</c><00:03:31.069><c> editor</c>

00:03:31.360 --> 00:03:31.370 align:start position:0%
you don't need any special text editor
 

00:03:31.370 --> 00:03:32.259 align:start position:0%
you don't need any special text editor
no<00:03:31.760><c> IDE</c>

00:03:32.259 --> 00:03:32.269 align:start position:0%
no IDE
 

00:03:32.269 --> 00:03:35.199 align:start position:0%
no IDE
if<00:03:32.750><c> you</c><00:03:32.870><c> don't</c><00:03:33.019><c> even</c><00:03:33.109><c> know</c><00:03:33.260><c> what</c><00:03:33.530><c> an</c><00:03:33.739><c> IDE</c><00:03:34.340><c> is</c><00:03:34.699><c> it</c>

00:03:35.199 --> 00:03:35.209 align:start position:0%
if you don't even know what an IDE is it
 

00:03:35.209 --> 00:03:36.309 align:start position:0%
if you don't even know what an IDE is it
doesn't<00:03:35.389><c> matter</c><00:03:35.540><c> because</c><00:03:35.900><c> we're</c><00:03:36.139><c> not</c><00:03:36.230><c> gonna</c>

00:03:36.309 --> 00:03:36.319 align:start position:0%
doesn't matter because we're not gonna
 

00:03:36.319 --> 00:03:39.699 align:start position:0%
doesn't matter because we're not gonna
be<00:03:36.440><c> using</c><00:03:36.560><c> one</c><00:03:36.970><c> so</c><00:03:37.970><c> all</c><00:03:38.750><c> we</c><00:03:38.989><c> need</c><00:03:39.079><c> to</c><00:03:39.139><c> do</c><00:03:39.379><c> is</c>

00:03:39.699 --> 00:03:39.709 align:start position:0%
be using one so all we need to do is
 

00:03:39.709 --> 00:03:42.099 align:start position:0%
be using one so all we need to do is
right-click<00:03:40.359><c> somewhere</c><00:03:41.359><c> on</c><00:03:41.750><c> your</c><00:03:41.810><c> Windows</c>

00:03:42.099 --> 00:03:42.109 align:start position:0%
right-click somewhere on your Windows
 

00:03:42.109 --> 00:03:44.530 align:start position:0%
right-click somewhere on your Windows
desktop<00:03:42.139><c> screen</c><00:03:42.709><c> or</c><00:03:43.400><c> if</c><00:03:44.090><c> you</c><00:03:44.180><c> have</c><00:03:44.299><c> a</c><00:03:44.329><c> Mac</c>

00:03:44.530 --> 00:03:44.540 align:start position:0%
desktop screen or if you have a Mac
 

00:03:44.540 --> 00:03:46.899 align:start position:0%
desktop screen or if you have a Mac
whatever<00:03:45.049><c> all</c><00:03:45.379><c> we're</c><00:03:45.560><c> doing</c><00:03:45.829><c> is</c><00:03:46.040><c> opening</c><00:03:46.519><c> up</c><00:03:46.699><c> a</c>

00:03:46.899 --> 00:03:46.909 align:start position:0%
whatever all we're doing is opening up a
 

00:03:46.909 --> 00:03:48.909 align:start position:0%
whatever all we're doing is opening up a
simple<00:03:47.389><c> text</c><00:03:47.569><c> document</c><00:03:47.629><c> okay</c><00:03:48.139><c> so</c><00:03:48.799><c> we'll</c><00:03:48.889><c> just</c>

00:03:48.909 --> 00:03:48.919 align:start position:0%
simple text document okay so we'll just
 

00:03:48.919 --> 00:03:51.460 align:start position:0%
simple text document okay so we'll just
create<00:03:49.519><c> text</c><00:03:49.849><c> document</c><00:03:50.269><c> we're</c><00:03:50.900><c> gonna</c><00:03:51.139><c> just</c>

00:03:51.460 --> 00:03:51.470 align:start position:0%
create text document we're gonna just
 

00:03:51.470 --> 00:03:53.650 align:start position:0%
create text document we're gonna just
call<00:03:51.889><c> it</c><00:03:52.010><c> demo</c>

00:03:53.650 --> 00:03:53.660 align:start position:0%
call it demo
 

00:03:53.660 --> 00:03:57.160 align:start position:0%
call it demo
okay<00:03:54.290><c> so</c><00:03:54.350><c> go</c><00:03:54.530><c> ahead</c><00:03:54.680><c> and</c><00:03:54.770><c> open</c><00:03:54.800><c> it</c><00:03:55.060><c> and</c><00:03:56.170><c> so</c>

00:03:57.160 --> 00:03:57.170 align:start position:0%
okay so go ahead and open it and so
 

00:03:57.170 --> 00:04:00.940 align:start position:0%
okay so go ahead and open it and so
we're<00:03:57.290><c> gonna</c><00:03:57.350><c> be</c><00:03:57.500><c> encoding</c><00:03:57.920><c> all</c><00:03:58.130><c> right</c><00:03:59.770><c> so</c><00:04:00.770><c> the</c>

00:04:00.940 --> 00:04:00.950 align:start position:0%
we're gonna be encoding all right so the
 

00:04:00.950 --> 00:04:04.840 align:start position:0%
we're gonna be encoding all right so the
first<00:04:01.070><c> thing</c><00:04:01.160><c> we</c><00:04:01.310><c> need</c><00:04:01.490><c> is</c><00:04:02.590><c> the</c><00:04:03.590><c> doctype</c><00:04:04.010><c> tag</c>

00:04:04.840 --> 00:04:04.850 align:start position:0%
first thing we need is the doctype tag
 

00:04:04.850 --> 00:04:06.310 align:start position:0%
first thing we need is the doctype tag
this<00:04:05.360><c> needs</c><00:04:05.540><c> to</c><00:04:05.570><c> be</c><00:04:05.630><c> at</c><00:04:05.720><c> the</c><00:04:05.780><c> top</c><00:04:05.930><c> of</c><00:04:06.110><c> every</c>

00:04:06.310 --> 00:04:06.320 align:start position:0%
this needs to be at the top of every
 

00:04:06.320 --> 00:04:10.240 align:start position:0%
this needs to be at the top of every
HTML<00:04:07.160><c> HTML</c><00:04:08.000><c> file</c><00:04:08.450><c> just</c><00:04:09.320><c> so</c><00:04:09.590><c> that</c><00:04:10.040><c> the</c><00:04:10.100><c> browser</c>

00:04:10.240 --> 00:04:10.250 align:start position:0%
HTML HTML file just so that the browser
 

00:04:10.250 --> 00:04:13.870 align:start position:0%
HTML HTML file just so that the browser
knows<00:04:11.770><c> that</c><00:04:12.770><c> every</c><00:04:12.980><c> browser</c><00:04:13.130><c> knows</c><00:04:13.580><c> that</c><00:04:13.730><c> this</c>

00:04:13.870 --> 00:04:13.880 align:start position:0%
knows that every browser knows that this
 

00:04:13.880 --> 00:04:16.630 align:start position:0%
knows that every browser knows that this
is<00:04:14.000><c> an</c><00:04:14.120><c> HTML</c><00:04:14.510><c> page</c><00:04:14.540><c> okay</c><00:04:15.280><c> so</c><00:04:16.280><c> the</c><00:04:16.370><c> next</c><00:04:16.520><c> thing</c>

00:04:16.630 --> 00:04:16.640 align:start position:0%
is an HTML page okay so the next thing
 

00:04:16.640 --> 00:04:24.340 align:start position:0%
is an HTML page okay so the next thing
we<00:04:16.730><c> need</c><00:04:16.970><c> is</c><00:04:17.290><c> the</c><00:04:18.290><c> HTML</c><00:04:18.650><c> opening</c><00:04:19.160><c> tag</c><00:04:19.390><c> and</c><00:04:23.350><c> the</c>

00:04:24.340 --> 00:04:24.350 align:start position:0%
we need is the HTML opening tag and the
 

00:04:24.350 --> 00:04:26.260 align:start position:0%
we need is the HTML opening tag and the
next<00:04:24.860><c> what</c><00:04:25.220><c> we're</c><00:04:25.340><c> going</c><00:04:25.460><c> to</c><00:04:25.490><c> use</c><00:04:25.580><c> is</c><00:04:25.850><c> the</c><00:04:26.090><c> head</c>

00:04:26.260 --> 00:04:26.270 align:start position:0%
next what we're going to use is the head
 

00:04:26.270 --> 00:04:29.830 align:start position:0%
next what we're going to use is the head
header<00:04:26.600><c> tag</c><00:04:26.810><c> a</c><00:04:27.080><c> head</c><00:04:27.290><c> head</c><00:04:27.830><c> tag</c><00:04:28.100><c> and</c><00:04:28.840><c> anything</c>

00:04:29.830 --> 00:04:29.840 align:start position:0%
header tag a head head tag and anything
 

00:04:29.840 --> 00:04:32.290 align:start position:0%
header tag a head head tag and anything
enclosed<00:04:30.400><c> anything</c><00:04:31.400><c> with</c><00:04:31.580><c> an</c><00:04:31.760><c> opening</c><00:04:32.180><c> and</c>

00:04:32.290 --> 00:04:32.300 align:start position:0%
enclosed anything with an opening and
 

00:04:32.300 --> 00:04:34.270 align:start position:0%
enclosed anything with an opening and
closing<00:04:32.630><c> tag</c><00:04:32.780><c> of</c><00:04:32.930><c> the</c><00:04:33.020><c> head</c><00:04:33.200><c> is</c><00:04:33.770><c> not</c><00:04:33.950><c> actually</c>

00:04:34.270 --> 00:04:34.280 align:start position:0%
closing tag of the head is not actually
 

00:04:34.280 --> 00:04:37.390 align:start position:0%
closing tag of the head is not actually
displayed<00:04:34.760><c> on</c><00:04:35.060><c> the</c><00:04:35.630><c> browser</c><00:04:35.920><c> this</c><00:04:36.920><c> is</c><00:04:36.980><c> for</c>

00:04:37.390 --> 00:04:37.400 align:start position:0%
displayed on the browser this is for
 

00:04:37.400 --> 00:04:39.310 align:start position:0%
displayed on the browser this is for
metadata<00:04:37.880><c> which</c><00:04:38.270><c> we'll</c><00:04:38.720><c> get</c><00:04:38.840><c> to</c><00:04:38.900><c> in</c><00:04:39.140><c> another</c>

00:04:39.310 --> 00:04:39.320 align:start position:0%
metadata which we'll get to in another
 

00:04:39.320 --> 00:04:41.980 align:start position:0%
metadata which we'll get to in another
video<00:04:39.880><c> so</c><00:04:40.880><c> it's</c><00:04:41.030><c> not</c><00:04:41.120><c> super</c><00:04:41.450><c> important</c><00:04:41.810><c> but</c>

00:04:41.980 --> 00:04:41.990 align:start position:0%
video so it's not super important but
 

00:04:41.990 --> 00:04:45.700 align:start position:0%
video so it's not super important but
the<00:04:42.740><c> more</c><00:04:42.920><c> important</c><00:04:44.230><c> thing</c><00:04:45.230><c> we're</c><00:04:45.350><c> be</c><00:04:45.470><c> doing</c>

00:04:45.700 --> 00:04:45.710 align:start position:0%
the more important thing we're be doing
 

00:04:45.710 --> 00:04:48.130 align:start position:0%
the more important thing we're be doing
in<00:04:45.800><c> here</c><00:04:45.950><c> is</c><00:04:46.160><c> using</c><00:04:46.370><c> studying</c><00:04:47.360><c> the</c><00:04:47.420><c> title</c><00:04:47.780><c> okay</c>

00:04:48.130 --> 00:04:48.140 align:start position:0%
in here is using studying the title okay
 

00:04:48.140 --> 00:04:53.050 align:start position:0%
in here is using studying the title okay
so<00:04:49.100><c> going</c><00:04:50.090><c> to</c><00:04:50.180><c> get</c><00:04:50.300><c> next</c><00:04:50.450><c> line</c><00:04:51.940><c> create</c><00:04:52.940><c> your</c>

00:04:53.050 --> 00:04:53.060 align:start position:0%
so going to get next line create your
 

00:04:53.060 --> 00:04:54.940 align:start position:0%
so going to get next line create your
title<00:04:53.330><c> tag</c><00:04:53.510><c> that</c><00:04:54.290><c> we're</c><00:04:54.410><c> going</c><00:04:54.500><c> to</c><00:04:54.560><c> call</c><00:04:54.710><c> this</c>

00:04:54.940 --> 00:04:54.950 align:start position:0%
title tag that we're going to call this
 

00:04:54.950 --> 00:05:00.520 align:start position:0%
title tag that we're going to call this
demo<00:04:55.720><c> webpage</c><00:04:56.720><c> okay</c><00:04:57.560><c> and</c><00:04:58.090><c> then</c><00:04:59.380><c> after</c><00:05:00.380><c> this</c>

00:05:00.520 --> 00:05:00.530 align:start position:0%
demo webpage okay and then after this
 

00:05:00.530 --> 00:05:03.850 align:start position:0%
demo webpage okay and then after this
that's<00:05:01.220><c> all</c><00:05:01.520><c> we</c><00:05:01.700><c> want</c><00:05:02.410><c> we</c><00:05:03.410><c> only</c><00:05:03.440><c> want</c><00:05:03.710><c> to</c><00:05:03.740><c> call</c>

00:05:03.850 --> 00:05:03.860 align:start position:0%
that's all we want we only want to call
 

00:05:03.860 --> 00:05:07.990 align:start position:0%
that's all we want we only want to call
it<00:05:03.950><c> demo</c><00:05:04.100><c> webpage</c><00:05:04.550><c> so</c><00:05:05.090><c> we</c><00:05:05.180><c> need</c><00:05:05.330><c> to</c><00:05:05.450><c> enclose</c><00:05:07.000><c> or</c>

00:05:07.990 --> 00:05:08.000 align:start position:0%
it demo webpage so we need to enclose or
 

00:05:08.000 --> 00:05:10.990 align:start position:0%
it demo webpage so we need to enclose or
we<00:05:08.300><c> need</c><00:05:08.420><c> to</c><00:05:08.540><c> end</c><00:05:08.780><c> that</c><00:05:09.020><c> with</c><00:05:09.680><c> a</c><00:05:10.040><c> closing</c><00:05:10.640><c> title</c>

00:05:10.990 --> 00:05:11.000 align:start position:0%
we need to end that with a closing title
 

00:05:11.000 --> 00:05:13.090 align:start position:0%
we need to end that with a closing title
tag<00:05:11.180><c> so</c><00:05:11.690><c> as</c><00:05:11.810><c> opening</c><00:05:12.260><c> in</c><00:05:12.380><c> the</c><00:05:12.470><c> closing</c><00:05:12.830><c> title</c>

00:05:13.090 --> 00:05:13.100 align:start position:0%
tag so as opening in the closing title
 

00:05:13.100 --> 00:05:15.130 align:start position:0%
tag so as opening in the closing title
tag<00:05:13.280><c> and</c><00:05:13.490><c> inside</c><00:05:14.210><c> there</c><00:05:14.510><c> the</c><00:05:14.810><c> browser</c><00:05:15.020><c> is</c>

00:05:15.130 --> 00:05:15.140 align:start position:0%
tag and inside there the browser is
 

00:05:15.140 --> 00:05:16.480 align:start position:0%
tag and inside there the browser is
going<00:05:15.170><c> to</c><00:05:15.350><c> know</c><00:05:15.470><c> that</c><00:05:15.800><c> we</c><00:05:15.890><c> want</c><00:05:16.040><c> to</c><00:05:16.100><c> call</c><00:05:16.310><c> this</c>

00:05:16.480 --> 00:05:16.490 align:start position:0%
going to know that we want to call this
 

00:05:16.490 --> 00:05:18.970 align:start position:0%
going to know that we want to call this
the<00:05:16.910><c> demo</c><00:05:17.210><c> webpage</c><00:05:17.570><c> and</c><00:05:17.780><c> we'll</c><00:05:17.990><c> see</c><00:05:18.230><c> we'll</c><00:05:18.950><c> see</c>

00:05:18.970 --> 00:05:18.980 align:start position:0%
the demo webpage and we'll see we'll see
 

00:05:18.980 --> 00:05:19.960 align:start position:0%
the demo webpage and we'll see we'll see
what<00:05:19.190><c> that</c><00:05:19.220><c> looks</c><00:05:19.310><c> like</c><00:05:19.490><c> when</c><00:05:19.730><c> we're</c><00:05:19.880><c> done</c>

00:05:19.960 --> 00:05:19.970 align:start position:0%
what that looks like when we're done
 

00:05:19.970 --> 00:05:22.020 align:start position:0%
what that looks like when we're done
okay

00:05:22.020 --> 00:05:22.030 align:start position:0%
okay
 

00:05:22.030 --> 00:05:25.270 align:start position:0%
okay
so<00:05:23.030><c> we're</c><00:05:23.180><c> actually</c><00:05:23.270><c> done</c><00:05:23.660><c> with</c><00:05:24.380><c> anything</c>

00:05:25.270 --> 00:05:25.280 align:start position:0%
so we're actually done with anything
 

00:05:25.280 --> 00:05:28.000 align:start position:0%
so we're actually done with anything
involved<00:05:25.790><c> with</c><00:05:26.270><c> the</c><00:05:26.420><c> head</c><00:05:26.570><c> tag</c><00:05:26.810><c> so</c><00:05:27.500><c> we</c><00:05:27.770><c> can</c><00:05:27.920><c> go</c>

00:05:28.000 --> 00:05:28.010 align:start position:0%
involved with the head tag so we can go
 

00:05:28.010 --> 00:05:29.980 align:start position:0%
involved with the head tag so we can go
ahead<00:05:28.100><c> and</c><00:05:28.250><c> close</c><00:05:28.370><c> that</c><00:05:28.430><c> as</c><00:05:28.670><c> well</c><00:05:28.820><c> all</c><00:05:29.780><c> right</c>

00:05:29.980 --> 00:05:29.990 align:start position:0%
ahead and close that as well all right
 

00:05:29.990 --> 00:05:34.230 align:start position:0%
ahead and close that as well all right
and<00:05:30.230><c> if</c><00:05:31.160><c> it's</c><00:05:31.310><c> easier</c><00:05:31.640><c> you</c><00:05:31.670><c> can</c><00:05:31.970><c> like</c><00:05:32.420><c> you</c><00:05:33.050><c> can</c>

00:05:34.230 --> 00:05:34.240 align:start position:0%
and if it's easier you can like you can
 

00:05:34.240 --> 00:05:38.410 align:start position:0%
and if it's easier you can like you can
indent<00:05:35.240><c> some</c><00:05:35.540><c> it</c><00:05:36.050><c> doesn't</c><00:05:36.500><c> matter</c><00:05:37.420><c> actually</c>

00:05:38.410 --> 00:05:38.420 align:start position:0%
indent some it doesn't matter actually
 

00:05:38.420 --> 00:05:39.490 align:start position:0%
indent some it doesn't matter actually
that's<00:05:38.570><c> kind</c><00:05:38.840><c> of</c><00:05:38.930><c> ugly</c>

00:05:39.490 --> 00:05:39.500 align:start position:0%
that's kind of ugly
 

00:05:39.500 --> 00:05:45.980 align:start position:0%
that's kind of ugly
let's<00:05:39.980><c> just</c><00:05:40.130><c> do</c><00:05:40.250><c> two</c><00:05:40.280><c> spaces</c><00:05:43.060><c> okay</c>

00:05:45.980 --> 00:05:45.990 align:start position:0%
let's just do two spaces okay
 

00:05:45.990 --> 00:05:50.610 align:start position:0%
let's just do two spaces okay
okay<00:05:46.990><c> so</c><00:05:47.430><c> now</c><00:05:48.430><c> this</c><00:05:49.000><c> is</c><00:05:49.230><c> what</c><00:05:50.230><c> we're</c><00:05:50.410><c> going</c><00:05:50.590><c> to</c>

00:05:50.610 --> 00:05:50.620 align:start position:0%
okay so now this is what we're going to
 

00:05:50.620 --> 00:05:54.000 align:start position:0%
okay so now this is what we're going to
see<00:05:50.830><c> okay</c><00:05:51.130><c> well</c><00:05:52.200><c> you</c><00:05:53.200><c> can</c><00:05:53.290><c> go</c><00:05:53.380><c> over</c><00:05:53.470><c> to</c><00:05:53.500><c> spaces</c>

00:05:54.000 --> 00:05:54.010 align:start position:0%
see okay well you can go over to spaces
 

00:05:54.010 --> 00:05:56.910 align:start position:0%
see okay well you can go over to spaces
and<00:05:54.190><c> the</c><00:05:54.490><c> body</c><00:05:54.670><c> tag</c><00:05:55.320><c> this</c><00:05:56.320><c> is</c><00:05:56.380><c> important</c>

00:05:56.910 --> 00:05:56.920 align:start position:0%
and the body tag this is important
 

00:05:56.920 --> 00:05:58.260 align:start position:0%
and the body tag this is important
because<00:05:56.950><c> everything</c><00:05:57.730><c> would</c><00:05:57.820><c> displayed</c>

00:05:58.260 --> 00:05:58.270 align:start position:0%
because everything would displayed
 

00:05:58.270 --> 00:06:00.120 align:start position:0%
because everything would displayed
within<00:05:58.480><c> the</c><00:05:58.690><c> body</c><00:05:58.900><c> tag</c><00:05:59.140><c> is</c><00:05:59.410><c> what</c><00:05:59.530><c> you're</c><00:05:59.950><c> gonna</c>

00:06:00.120 --> 00:06:00.130 align:start position:0%
within the body tag is what you're gonna
 

00:06:00.130 --> 00:06:04.710 align:start position:0%
within the body tag is what you're gonna
see<00:06:00.490><c> on</c><00:06:00.820><c> the</c><00:06:01.630><c> screen</c><00:06:01.930><c> in</c><00:06:02.140><c> the</c><00:06:02.200><c> browser</c><00:06:02.500><c> okay</c><00:06:03.720><c> so</c>

00:06:04.710 --> 00:06:04.720 align:start position:0%
see on the screen in the browser okay so
 

00:06:04.720 --> 00:06:07.350 align:start position:0%
see on the screen in the browser okay so
have<00:06:05.050><c> an</c><00:06:05.140><c> opening</c><00:06:05.470><c> body</c><00:06:05.590><c> tag</c><00:06:05.710><c> and</c><00:06:06.130><c> then</c><00:06:07.120><c> we're</c>

00:06:07.350 --> 00:06:07.360 align:start position:0%
have an opening body tag and then we're
 

00:06:07.360 --> 00:06:10.560 align:start position:0%
have an opening body tag and then we're
gonna<00:06:07.450><c> have</c><00:06:07.810><c> what's</c><00:06:08.320><c> called</c><00:06:08.560><c> a</c><00:06:09.000><c> heading</c><00:06:10.000><c> 1</c><00:06:10.300><c> or</c>

00:06:10.560 --> 00:06:10.570 align:start position:0%
gonna have what's called a heading 1 or
 

00:06:10.570 --> 00:06:13.680 align:start position:0%
gonna have what's called a heading 1 or
just<00:06:10.960><c> h1</c><00:06:11.440><c> for</c><00:06:11.650><c> short</c><00:06:11.860><c> okay</c><00:06:12.370><c> this</c><00:06:13.360><c> gives</c><00:06:13.510><c> you</c><00:06:13.630><c> a</c>

00:06:13.680 --> 00:06:13.690 align:start position:0%
just h1 for short okay this gives you a
 

00:06:13.690 --> 00:06:16.260 align:start position:0%
just h1 for short okay this gives you a
default<00:06:14.080><c> text</c><00:06:14.410><c> size</c><00:06:14.440><c> we'll</c><00:06:14.950><c> get</c><00:06:15.100><c> to</c><00:06:15.370><c> what</c><00:06:15.880><c> what</c>

00:06:16.260 --> 00:06:16.270 align:start position:0%
default text size we'll get to what what
 

00:06:16.270 --> 00:06:18.330 align:start position:0%
default text size we'll get to what what
these<00:06:16.390><c> really</c><00:06:16.750><c> mean</c><00:06:17.050><c> in</c><00:06:17.320><c> another</c><00:06:17.830><c> video</c><00:06:18.070><c> we're</c>

00:06:18.330 --> 00:06:18.340 align:start position:0%
these really mean in another video we're
 

00:06:18.340 --> 00:06:22.830 align:start position:0%
these really mean in another video we're
just<00:06:18.370><c> here</c><00:06:18.730><c> to</c><00:06:19.650><c> code</c><00:06:20.650><c> and</c><00:06:20.890><c> show</c><00:06:21.790><c> off</c><00:06:22.270><c> our</c><00:06:22.660><c> first</c>

00:06:22.830 --> 00:06:22.840 align:start position:0%
just here to code and show off our first
 

00:06:22.840 --> 00:06:25.320 align:start position:0%
just here to code and show off our first
HTML<00:06:23.020><c> page</c><00:06:23.380><c> all</c><00:06:23.620><c> right</c><00:06:23.800><c> so</c><00:06:23.980><c> we</c><00:06:24.880><c> just</c><00:06:25.030><c> say</c>

00:06:25.320 --> 00:06:25.330 align:start position:0%
HTML page all right so we just say
 

00:06:25.330 --> 00:06:31.290 align:start position:0%
HTML page all right so we just say
welcome<00:06:25.630><c> to</c><00:06:26.080><c> our</c><00:06:26.440><c> first</c><00:06:27.430><c> HTML</c><00:06:28.650><c> demo</c><00:06:29.650><c> okay</c><00:06:30.300><c> okay</c>

00:06:31.290 --> 00:06:31.300 align:start position:0%
welcome to our first HTML demo okay okay
 

00:06:31.300 --> 00:06:33.300 align:start position:0%
welcome to our first HTML demo okay okay
and<00:06:31.480><c> then</c><00:06:32.050><c> we</c><00:06:32.140><c> need</c><00:06:32.260><c> it</c><00:06:32.380><c> we</c><00:06:32.860><c> need</c><00:06:32.890><c> to</c><00:06:33.010><c> close</c><00:06:33.190><c> it</c>

00:06:33.300 --> 00:06:33.310 align:start position:0%
and then we need it we need to close it
 

00:06:33.310 --> 00:06:38.040 align:start position:0%
and then we need it we need to close it
off<00:06:33.430><c> and</c><00:06:35.790><c> come</c><00:06:36.790><c> over</c><00:06:36.880><c> for</c><00:06:37.090><c> spaces</c><00:06:37.480><c> again</c><00:06:37.660><c> we're</c>

00:06:38.040 --> 00:06:38.050 align:start position:0%
off and come over for spaces again we're
 

00:06:38.050 --> 00:06:41.910 align:start position:0%
off and come over for spaces again we're
gonna<00:06:38.110><c> write</c><00:06:38.850><c> we're</c><00:06:39.850><c> gonna</c><00:06:39.970><c> use</c><00:06:40.750><c> a</c><00:06:40.780><c> P</c><00:06:41.050><c> tag</c><00:06:41.290><c> or</c>

00:06:41.910 --> 00:06:41.920 align:start position:0%
gonna write we're gonna use a P tag or
 

00:06:41.920 --> 00:06:43.890 align:start position:0%
gonna write we're gonna use a P tag or
paragraph<00:06:42.430><c> P</c><00:06:42.790><c> is</c><00:06:42.850><c> for</c><00:06:43.180><c> for</c><00:06:43.330><c> paragraph</c><00:06:43.660><c> and</c>

00:06:43.890 --> 00:06:43.900 align:start position:0%
paragraph P is for for paragraph and
 

00:06:43.900 --> 00:06:46.110 align:start position:0%
paragraph P is for for paragraph and
then<00:06:44.740><c> here</c><00:06:44.950><c> we're</c><00:06:45.040><c> just</c><00:06:45.070><c> going</c><00:06:45.280><c> to</c><00:06:45.340><c> say</c><00:06:45.790><c> this</c>

00:06:46.110 --> 00:06:46.120 align:start position:0%
then here we're just going to say this
 

00:06:46.120 --> 00:06:53.340 align:start position:0%
then here we're just going to say this
is<00:06:46.870><c> an</c><00:06:47.110><c> example</c><00:06:47.790><c> paragraph</c><00:06:48.790><c> statement</c><00:06:52.350><c> okay</c>

00:06:53.340 --> 00:06:53.350 align:start position:0%
is an example paragraph statement okay
 

00:06:53.350 --> 00:06:58.050 align:start position:0%
is an example paragraph statement okay
so<00:06:54.250><c> again</c><00:06:55.210><c> we</c><00:06:55.270><c> opened</c><00:06:55.990><c> up</c><00:06:56.740><c> an</c><00:06:57.370><c> opening</c><00:06:57.610><c> tag</c><00:06:57.760><c> and</c>

00:06:58.050 --> 00:06:58.060 align:start position:0%
so again we opened up an opening tag and
 

00:06:58.060 --> 00:06:59.280 align:start position:0%
so again we opened up an opening tag and
then<00:06:58.210><c> we</c><00:06:58.270><c> have</c><00:06:58.390><c> you</c><00:06:58.450><c> a</c><00:06:58.480><c> closing</c><00:06:58.810><c> tag</c><00:06:58.840><c> all</c><00:06:59.200><c> right</c>

00:06:59.280 --> 00:06:59.290 align:start position:0%
then we have you a closing tag all right
 

00:06:59.290 --> 00:07:01.980 align:start position:0%
then we have you a closing tag all right
so<00:07:00.100><c> now</c><00:07:00.340><c> we're</c><00:07:00.700><c> done</c><00:07:00.820><c> with</c><00:07:00.880><c> everything</c><00:07:01.750><c> we</c>

00:07:01.980 --> 00:07:01.990 align:start position:0%
so now we're done with everything we
 

00:07:01.990 --> 00:07:03.150 align:start position:0%
so now we're done with everything we
want<00:07:02.170><c> actually</c><00:07:02.320><c> displayed</c><00:07:02.710><c> on</c><00:07:02.830><c> the</c><00:07:02.920><c> screen</c>

00:07:03.150 --> 00:07:03.160 align:start position:0%
want actually displayed on the screen
 

00:07:03.160 --> 00:07:05.460 align:start position:0%
want actually displayed on the screen
okay<00:07:03.640><c> this</c><00:07:03.760><c> is</c><00:07:03.850><c> very</c><00:07:04.000><c> simple</c><00:07:04.270><c> so</c><00:07:04.870><c> we</c><00:07:05.230><c> need</c><00:07:05.350><c> to</c>

00:07:05.460 --> 00:07:05.470 align:start position:0%
okay this is very simple so we need to
 

00:07:05.470 --> 00:07:08.790 align:start position:0%
okay this is very simple so we need to
close<00:07:05.710><c> off</c><00:07:05.890><c> the</c><00:07:06.340><c> light</c><00:07:06.580><c> AG</c><00:07:06.760><c> and</c><00:07:07.260><c> then</c><00:07:08.260><c> finally</c>

00:07:08.790 --> 00:07:08.800 align:start position:0%
close off the light AG and then finally
 

00:07:08.800 --> 00:07:10.680 align:start position:0%
close off the light AG and then finally
we<00:07:09.220><c> have</c><00:07:09.430><c> one</c><00:07:09.730><c> more</c><00:07:09.880><c> tag</c><00:07:10.150><c> that</c><00:07:10.420><c> we</c><00:07:10.510><c> need</c><00:07:10.630><c> to</c>

00:07:10.680 --> 00:07:10.690 align:start position:0%
we have one more tag that we need to
 

00:07:10.690 --> 00:07:16.820 align:start position:0%
we have one more tag that we need to
close<00:07:10.900><c> and</c><00:07:11.320><c> that's</c><00:07:12.010><c> the</c><00:07:12.100><c> HTML</c><00:07:12.460><c> tag</c><00:07:14.550><c> okay</c><00:07:15.550><c> so</c>

00:07:16.820 --> 00:07:16.830 align:start position:0%
close and that's the HTML tag okay so
 

00:07:16.830 --> 00:07:22.280 align:start position:0%
close and that's the HTML tag okay so
you<00:07:17.830><c> n</c><00:07:17.950><c> save</c><00:07:18.130><c> this</c><00:07:19.170><c> so</c><00:07:20.170><c> just</c><00:07:20.260><c> file</c><00:07:20.500><c> save</c><00:07:21.130><c> and</c>

00:07:22.280 --> 00:07:22.290 align:start position:0%
you n save this so just file save and
 

00:07:22.290 --> 00:07:24.780 align:start position:0%
you n save this so just file save and
right<00:07:23.290><c> now</c><00:07:23.410><c> this</c><00:07:23.530><c> is</c><00:07:23.680><c> still</c><00:07:23.770><c> a</c><00:07:23.860><c> text</c><00:07:24.250><c> file</c><00:07:24.430><c> so</c>

00:07:24.780 --> 00:07:24.790 align:start position:0%
right now this is still a text file so
 

00:07:24.790 --> 00:07:31.940 align:start position:0%
right now this is still a text file so
if<00:07:25.120><c> we</c><00:07:25.330><c> go</c><00:07:25.720><c> to</c><00:07:25.750><c> save</c><00:07:26.410><c> as</c><00:07:27.600><c> C</c><00:07:28.600><c> just</c><00:07:29.080><c> say</c><00:07:29.260><c> demo</c><00:07:30.330><c> HTML</c>

00:07:31.940 --> 00:07:31.950 align:start position:0%
if we go to save as C just say demo HTML
 

00:07:31.950 --> 00:07:34.950 align:start position:0%
if we go to save as C just say demo HTML
okay<00:07:32.950><c> and</c><00:07:33.220><c> as</c><00:07:34.030><c> you</c><00:07:34.120><c> can</c><00:07:34.240><c> see</c><00:07:34.450><c> in</c><00:07:34.600><c> the</c><00:07:34.720><c> top</c><00:07:34.930><c> left</c>

00:07:34.950 --> 00:07:34.960 align:start position:0%
okay and as you can see in the top left
 

00:07:34.960 --> 00:07:38.910 align:start position:0%
okay and as you can see in the top left
corner<00:07:35.230><c> a</c><00:07:35.880><c> new</c><00:07:36.880><c> a</c><00:07:37.300><c> new</c><00:07:38.170><c> file</c><00:07:38.410><c> appear</c><00:07:38.740><c> to</c><00:07:38.830><c> my</c>

00:07:38.910 --> 00:07:38.920 align:start position:0%
corner a new a new file appear to my
 

00:07:38.920 --> 00:07:40.700 align:start position:0%
corner a new a new file appear to my
desktop

00:07:40.700 --> 00:07:40.710 align:start position:0%
desktop
 

00:07:40.710 --> 00:07:45.270 align:start position:0%
desktop
alright<00:07:41.710><c> so</c><00:07:42.750><c> my</c><00:07:43.750><c> default</c><00:07:43.900><c> I</c><00:07:44.230><c> use</c><00:07:44.380><c> Chrome</c><00:07:44.680><c> so</c>

00:07:45.270 --> 00:07:45.280 align:start position:0%
alright so my default I use Chrome so
 

00:07:45.280 --> 00:07:47.070 align:start position:0%
alright so my default I use Chrome so
that's<00:07:45.430><c> why</c><00:07:45.580><c> is</c><00:07:45.640><c> the</c><00:07:45.820><c> permissible</c><00:07:46.120><c> so</c><00:07:46.840><c> now</c><00:07:46.960><c> if</c>

00:07:47.070 --> 00:07:47.080 align:start position:0%
that's why is the permissible so now if
 

00:07:47.080 --> 00:07:50.750 align:start position:0%
that's why is the permissible so now if
we<00:07:47.200><c> open</c><00:07:47.350><c> this</c><00:07:49.290><c> okay</c>

00:07:50.750 --> 00:07:50.760 align:start position:0%
we open this okay
 

00:07:50.760 --> 00:07:52.320 align:start position:0%
we open this okay
it<00:07:51.760><c> worked</c>

00:07:52.320 --> 00:07:52.330 align:start position:0%
it worked
 

00:07:52.330 --> 00:07:54.300 align:start position:0%
it worked
all<00:07:52.419><c> right</c><00:07:52.599><c> this</c><00:07:53.050><c> is</c><00:07:53.139><c> what</c><00:07:53.259><c> she</c><00:07:53.349><c> says</c><00:07:53.530><c> should</c>

00:07:54.300 --> 00:07:54.310 align:start position:0%
all right this is what she says should
 

00:07:54.310 --> 00:07:56.670 align:start position:0%
all right this is what she says should
see<00:07:54.520><c> if</c><00:07:54.789><c> not</c><00:07:55.060><c> just</c><00:07:55.870><c> kind</c><00:07:55.990><c> of</c><00:07:56.080><c> go</c><00:07:56.229><c> back</c><00:07:56.439><c> and</c>

00:07:56.670 --> 00:07:56.680 align:start position:0%
see if not just kind of go back and
 

00:07:56.680 --> 00:07:59.869 align:start position:0%
see if not just kind of go back and
follow<00:07:57.400><c> along</c><00:07:57.580><c> again</c><00:07:58.000><c> just</c><00:07:58.599><c> to</c><00:07:58.659><c> make</c><00:07:58.720><c> sure</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
follow along again just to make sure
 

00:07:59.879 --> 00:08:02.010 align:start position:0%
follow along again just to make sure
they<00:08:00.879><c> have</c><00:08:01.060><c> your</c><00:08:01.090><c> opening</c><00:08:01.479><c> closing</c><00:08:01.719><c> tags</c>

00:08:02.010 --> 00:08:02.020 align:start position:0%
they have your opening closing tags
 

00:08:02.020 --> 00:08:04.170 align:start position:0%
they have your opening closing tags
correctly<00:08:02.639><c> so</c><00:08:03.639><c> that's</c><00:08:03.819><c> probably</c><00:08:04.000><c> the</c><00:08:04.120><c> only</c>

00:08:04.170 --> 00:08:04.180 align:start position:0%
correctly so that's probably the only
 

00:08:04.180 --> 00:08:05.550 align:start position:0%
correctly so that's probably the only
thing<00:08:04.330><c> that</c><00:08:04.479><c> would</c><00:08:04.719><c> be</c><00:08:04.900><c> interrupt</c><00:08:05.349><c> at</c><00:08:05.439><c> this</c>

00:08:05.550 --> 00:08:05.560 align:start position:0%
thing that would be interrupt at this
 

00:08:05.560 --> 00:08:09.570 align:start position:0%
thing that would be interrupt at this
point<00:08:06.060><c> so</c><00:08:07.680><c> if</c><00:08:08.680><c> you</c><00:08:08.800><c> remember</c><00:08:08.979><c> everything</c>

00:08:09.570 --> 00:08:09.580 align:start position:0%
point so if you remember everything
 

00:08:09.580 --> 00:08:11.760 align:start position:0%
point so if you remember everything
they've<00:08:09.699><c> had</c><00:08:09.849><c> in</c><00:08:09.969><c> the</c><00:08:10.060><c> title</c><00:08:10.479><c> tag</c><00:08:10.770><c> demo</c>

00:08:11.760 --> 00:08:11.770 align:start position:0%
they've had in the title tag demo
 

00:08:11.770 --> 00:08:13.950 align:start position:0%
they've had in the title tag demo
webpage<00:08:12.159><c> that</c><00:08:12.460><c> showed</c><00:08:13.270><c> up</c><00:08:13.419><c> here</c><00:08:13.689><c> and</c><00:08:13.870><c> the</c>

00:08:13.950 --> 00:08:13.960 align:start position:0%
webpage that showed up here and the
 

00:08:13.960 --> 00:08:15.809 align:start position:0%
webpage that showed up here and the
browser<00:08:14.169><c> tab</c><00:08:14.500><c> okay</c><00:08:14.800><c> that's</c><00:08:15.250><c> where</c><00:08:15.400><c> the</c><00:08:15.490><c> titles</c>

00:08:15.809 --> 00:08:15.819 align:start position:0%
browser tab okay that's where the titles
 

00:08:15.819 --> 00:08:19.050 align:start position:0%
browser tab okay that's where the titles
gonna<00:08:15.969><c> be</c><00:08:16.500><c> welcome</c><00:08:17.500><c> to</c><00:08:17.650><c> our</c><00:08:17.830><c> first</c><00:08:17.949><c> HTML</c><00:08:18.610><c> web</c>

00:08:19.050 --> 00:08:19.060 align:start position:0%
gonna be welcome to our first HTML web
 

00:08:19.060 --> 00:08:22.670 align:start position:0%
gonna be welcome to our first HTML web
page<00:08:19.090><c> that</c><00:08:19.960><c> was</c><00:08:20.110><c> in</c><00:08:20.289><c> the</c><00:08:20.409><c> h1</c><00:08:20.949><c> tag</c><00:08:21.219><c> and</c><00:08:21.550><c> that's</c>

00:08:22.670 --> 00:08:22.680 align:start position:0%
page that was in the h1 tag and that's
 

00:08:22.680 --> 00:08:26.459 align:start position:0%
page that was in the h1 tag and that's
it<00:08:23.680><c> gives</c><00:08:23.919><c> you</c><00:08:24.419><c> it</c><00:08:25.419><c> builds</c><00:08:25.750><c> it</c><00:08:25.870><c> and</c><00:08:25.900><c> gives</c><00:08:26.289><c> you</c>

00:08:26.459 --> 00:08:26.469 align:start position:0%
it gives you it builds it and gives you
 

00:08:26.469 --> 00:08:30.330 align:start position:0%
it gives you it builds it and gives you
a<00:08:26.889><c> certain</c><00:08:27.280><c> text</c><00:08:28.240><c> size</c><00:08:28.389><c> and</c><00:08:28.750><c> then</c><00:08:29.530><c> when</c><00:08:30.069><c> we</c><00:08:30.159><c> say</c>

00:08:30.330 --> 00:08:30.340 align:start position:0%
a certain text size and then when we say
 

00:08:30.340 --> 00:08:32.759 align:start position:0%
a certain text size and then when we say
this<00:08:30.490><c> isn't</c><00:08:30.639><c> a</c><00:08:30.759><c> paragraph</c><00:08:31.300><c> statement</c><00:08:31.690><c> that</c><00:08:32.620><c> is</c>

00:08:32.759 --> 00:08:32.769 align:start position:0%
this isn't a paragraph statement that is
 

00:08:32.769 --> 00:08:35.329 align:start position:0%
this isn't a paragraph statement that is
what<00:08:32.919><c> was</c><00:08:33.070><c> in</c><00:08:33.250><c> that</c><00:08:33.640><c> was</c><00:08:33.909><c> what</c><00:08:34.120><c> was</c><00:08:34.390><c> within</c><00:08:34.599><c> our</c>

00:08:35.329 --> 00:08:35.339 align:start position:0%
what was in that was what was within our
 

00:08:35.339 --> 00:08:39.269 align:start position:0%
what was in that was what was within our
p<00:08:36.339><c> tag</c><00:08:36.550><c> paragraph</c><00:08:37.209><c> and</c><00:08:37.769><c> we'll</c><00:08:38.769><c> go</c><00:08:38.919><c> over</c><00:08:38.950><c> those</c>

00:08:39.269 --> 00:08:39.279 align:start position:0%
p tag paragraph and we'll go over those
 

00:08:39.279 --> 00:08:41.490 align:start position:0%
p tag paragraph and we'll go over those
tags<00:08:39.969><c> and</c><00:08:40.209><c> what</c><00:08:40.329><c> they</c><00:08:40.450><c> do</c><00:08:40.510><c> in</c><00:08:41.019><c> other</c><00:08:41.140><c> videos</c>

00:08:41.490 --> 00:08:41.500 align:start position:0%
tags and what they do in other videos
 

00:08:41.500 --> 00:08:44.750 align:start position:0%
tags and what they do in other videos
but<00:08:42.269><c> we</c><00:08:43.269><c> did</c><00:08:43.419><c> it</c><00:08:43.539><c> we</c><00:08:43.690><c> create</c><00:08:44.019><c> our</c><00:08:44.140><c> first</c><00:08:44.290><c> HTML</c>

00:08:44.750 --> 00:08:44.760 align:start position:0%
but we did it we create our first HTML
 

00:08:44.760 --> 00:08:48.030 align:start position:0%
but we did it we create our first HTML
page<00:08:45.760><c> it's</c><00:08:46.720><c> not</c><00:08:46.839><c> too</c><00:08:46.959><c> bad</c><00:08:47.170><c> if</c><00:08:47.649><c> you</c><00:08:47.800><c> if</c><00:08:47.920><c> you</c>

00:08:48.030 --> 00:08:48.040 align:start position:0%
page it's not too bad if you if you
 

00:08:48.040 --> 00:08:50.160 align:start position:0%
page it's not too bad if you if you
never<00:08:48.160><c> turn</c><00:08:48.399><c> it</c><00:08:48.490><c> before</c><00:08:48.810><c> this</c><00:08:49.810><c> is</c><00:08:49.959><c> a</c><00:08:49.990><c> good</c>

00:08:50.160 --> 00:08:50.170 align:start position:0%
never turn it before this is a good
 

00:08:50.170 --> 00:08:54.960 align:start position:0%
never turn it before this is a good
start<00:08:50.470><c> and</c><00:08:52.230><c> from</c><00:08:53.230><c> here</c><00:08:53.380><c> on</c><00:08:53.410><c> out</c><00:08:53.680><c> we'll</c><00:08:54.040><c> I'll</c><00:08:54.790><c> go</c>

00:08:54.960 --> 00:08:54.970 align:start position:0%
start and from here on out we'll I'll go
 

00:08:54.970 --> 00:08:58.769 align:start position:0%
start and from here on out we'll I'll go
more<00:08:55.240><c> in</c><00:08:55.329><c> depth</c><00:08:55.600><c> about</c><00:08:56.440><c> other</c><00:08:57.430><c> tags</c><00:08:58.329><c> and</c><00:08:58.690><c> their</c>

00:08:58.769 --> 00:08:58.779 align:start position:0%
more in depth about other tags and their
 

00:08:58.779 --> 00:09:00.420 align:start position:0%
more in depth about other tags and their
usefulness<00:08:59.140><c> and</c><00:08:59.500><c> one</c><00:08:59.769><c> creating</c><00:09:00.100><c> a</c><00:09:00.130><c> web</c><00:09:00.279><c> page</c>

00:09:00.420 --> 00:09:00.430 align:start position:0%
usefulness and one creating a web page
 

00:09:00.430 --> 00:09:02.910 align:start position:0%
usefulness and one creating a web page
okay<00:09:01.230><c> so</c><00:09:02.230><c> hope</c><00:09:02.350><c> you</c><00:09:02.440><c> guys</c><00:09:02.529><c> would</c><00:09:02.620><c> be</c><00:09:02.769><c> able</c><00:09:02.829><c> to</c>

00:09:02.910 --> 00:09:02.920 align:start position:0%
okay so hope you guys would be able to
 

00:09:02.920 --> 00:09:07.589 align:start position:0%
okay so hope you guys would be able to
follow<00:09:03.130><c> along</c><00:09:03.339><c> and</c><00:09:04.050><c> I'll</c><00:09:05.050><c> see</c><00:09:05.230><c> you</c><00:09:05.290><c> next</c><00:09:05.470><c> time</c>

