WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:02.070 align:start position:0%
 
our<00:00:00.560><c> open</c><00:00:00.799><c> source</c><00:00:01.120><c> program</c><00:00:01.480><c> office</c><00:00:01.760><c> has</c><00:00:01.880><c> been</c>

00:00:02.070 --> 00:00:02.080 align:start position:0%
our open source program office has been
 

00:00:02.080 --> 00:00:04.190 align:start position:0%
our open source program office has been
working<00:00:02.399><c> with</c><00:00:02.600><c> github's</c><00:00:03.080><c> graphql</c><00:00:03.679><c> API</c><00:00:04.080><c> to</c>

00:00:04.190 --> 00:00:04.200 align:start position:0%
working with github's graphql API to
 

00:00:04.200 --> 00:00:06.590 align:start position:0%
working with github's graphql API to
ease<00:00:04.480><c> tracking</c><00:00:04.839><c> the</c><00:00:04.960><c> health</c><00:00:05.279><c> of</c><00:00:05.560><c> projects</c><00:00:06.200><c> and</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
ease tracking the health of projects and
 

00:00:06.600 --> 00:00:08.629 align:start position:0%
ease tracking the health of projects and
communities<00:00:07.600><c> now</c><00:00:07.759><c> while</c><00:00:08.000><c> they</c><00:00:08.160><c> began</c><00:00:08.480><c> this</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
communities now while they began this
 

00:00:08.639 --> 00:00:11.190 align:start position:0%
communities now while they began this
work<00:00:08.880><c> to</c><00:00:09.040><c> help</c><00:00:09.320><c> other</c><00:00:09.559><c> OS</c><00:00:10.040><c> using</c><00:00:10.400><c> GitHub</c><00:00:11.040><c> the</c>

00:00:11.190 --> 00:00:11.200 align:start position:0%
work to help other OS using GitHub the
 

00:00:11.200 --> 00:00:13.549 align:start position:0%
work to help other OS using GitHub the
API<00:00:11.559><c> is</c><00:00:11.679><c> useful</c><00:00:12.000><c> for</c><00:00:12.320><c> anyone</c><00:00:12.960><c> who</c><00:00:13.080><c> is</c><00:00:13.240><c> using</c>

00:00:13.549 --> 00:00:13.559 align:start position:0%
API is useful for anyone who is using
 

00:00:13.559 --> 00:00:15.910 align:start position:0%
API is useful for anyone who is using
time<00:00:13.799><c> series</c><00:00:14.160><c> graphs</c><00:00:14.440><c> or</c><00:00:14.679><c> automating</c><00:00:15.200><c> checks</c>

00:00:15.910 --> 00:00:15.920 align:start position:0%
time series graphs or automating checks
 

00:00:15.920 --> 00:00:18.109 align:start position:0%
time series graphs or automating checks
of<00:00:16.160><c> their</c><00:00:16.359><c> repository</c><00:00:16.960><c> statistics</c><00:00:17.600><c> and</c>

00:00:18.109 --> 00:00:18.119 align:start position:0%
of their repository statistics and
 

00:00:18.119 --> 00:00:20.870 align:start position:0%
of their repository statistics and
status<00:00:19.119><c> with</c><00:00:19.240><c> a</c><00:00:19.400><c> recent</c><00:00:19.680><c> Improvement</c><00:00:20.519><c> you</c><00:00:20.720><c> can</c>

00:00:20.870 --> 00:00:20.880 align:start position:0%
status with a recent Improvement you can
 

00:00:20.880 --> 00:00:23.390 align:start position:0%
status with a recent Improvement you can
now<00:00:21.039><c> use</c><00:00:21.240><c> the</c><00:00:21.400><c> API</c><00:00:21.960><c> to</c><00:00:22.119><c> retrieve</c><00:00:22.600><c> information</c>

00:00:23.390 --> 00:00:23.400 align:start position:0%
now use the API to retrieve information
 

00:00:23.400 --> 00:00:26.470 align:start position:0%
now use the API to retrieve information
about<00:00:23.640><c> the</c><00:00:23.800><c> contributing</c><00:00:24.560><c> guidelines</c><00:00:25.279><c> for</c><00:00:25.720><c> a</c>

00:00:26.470 --> 00:00:26.480 align:start position:0%
about the contributing guidelines for a
 

00:00:26.480 --> 00:00:28.750 align:start position:0%
about the contributing guidelines for a
repository<00:00:27.480><c> this</c><00:00:27.679><c> completes</c><00:00:28.080><c> the</c><00:00:28.240><c> set</c><00:00:28.519><c> of</c>

00:00:28.750 --> 00:00:28.760 align:start position:0%
repository this completes the set of
 

00:00:28.760 --> 00:00:30.630 align:start position:0%
repository this completes the set of
community<00:00:29.160><c> standards</c><00:00:29.599><c> documents</c><00:00:30.199><c> that</c><00:00:30.359><c> are</c>

00:00:30.630 --> 00:00:30.640 align:start position:0%
community standards documents that are
 

00:00:30.640 --> 00:00:33.630 align:start position:0%
community standards documents that are
accessible<00:00:31.320><c> via</c><00:00:31.800><c> API</c><00:00:32.800><c> so</c><00:00:32.920><c> you</c><00:00:33.040><c> can</c><00:00:33.200><c> use</c><00:00:33.480><c> this</c>

00:00:33.630 --> 00:00:33.640 align:start position:0%
accessible via API so you can use this
 

00:00:33.640 --> 00:00:35.590 align:start position:0%
accessible via API so you can use this
API<00:00:34.079><c> to</c><00:00:34.239><c> find</c><00:00:34.559><c> repositories</c><00:00:35.120><c> in</c><00:00:35.280><c> your</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
API to find repositories in your
 

00:00:35.600 --> 00:00:37.709 align:start position:0%
API to find repositories in your
organization<00:00:36.600><c> which</c><00:00:36.800><c> do</c><00:00:36.960><c> not</c><00:00:37.200><c> have</c><00:00:37.480><c> a</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
organization which do not have a
 

00:00:37.719 --> 00:00:40.869 align:start position:0%
organization which do not have a
contributing<00:00:38.440><c> file</c><00:00:39.040><c> code</c><00:00:39.239><c> of</c><00:00:39.440><c> conduct</c><00:00:40.200><c> or</c>

00:00:40.869 --> 00:00:40.879 align:start position:0%
contributing file code of conduct or
 

00:00:40.879 --> 00:00:42.389 align:start position:0%
contributing file code of conduct or
which<00:00:41.079><c> have</c><00:00:41.200><c> a</c><00:00:41.360><c> license</c><00:00:41.760><c> that</c><00:00:41.879><c> is</c><00:00:42.120><c> not</c>

00:00:42.389 --> 00:00:42.399 align:start position:0%
which have a license that is not
 

00:00:42.399 --> 00:00:46.069 align:start position:0%
which have a license that is not
approved<00:00:43.280><c> by</c><00:00:43.559><c> your</c><00:00:43.920><c> organization's</c><00:00:45.079><c> policy</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
approved by your organization's policy
 

00:00:46.079 --> 00:00:47.830 align:start position:0%
approved by your organization's policy
in<00:00:46.239><c> addition</c><00:00:46.520><c> to</c><00:00:46.640><c> the</c><00:00:46.800><c> text</c><00:00:47.039><c> based</c><00:00:47.320><c> fields</c><00:00:47.680><c> we</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
in addition to the text based fields we
 

00:00:47.840 --> 00:00:50.029 align:start position:0%
in addition to the text based fields we
pull<00:00:48.199><c> together</c><00:00:48.399><c> some</c><00:00:48.600><c> graphql</c><00:00:49.199><c> queries</c><00:00:49.840><c> to</c>

00:00:50.029 --> 00:00:50.039 align:start position:0%
pull together some graphql queries to
 

00:00:50.039 --> 00:00:52.150 align:start position:0%
pull together some graphql queries to
aggregate<00:00:50.600><c> several</c><00:00:50.960><c> useful</c><00:00:51.360><c> metrics</c><00:00:51.840><c> that</c>

00:00:52.150 --> 00:00:52.160 align:start position:0%
aggregate several useful metrics that
 

00:00:52.160 --> 00:00:54.270 align:start position:0%
aggregate several useful metrics that
together<00:00:52.840><c> can</c><00:00:53.000><c> be</c><00:00:53.199><c> good</c><00:00:53.399><c> indicators</c><00:00:54.079><c> of</c>

00:00:54.270 --> 00:00:54.280 align:start position:0%
together can be good indicators of
 

00:00:54.280 --> 00:00:56.349 align:start position:0%
together can be good indicators of
project<00:00:54.640><c> Health</c><00:00:55.320><c> tracking</c><00:00:55.680><c> open</c><00:00:55.879><c> and</c><00:00:56.039><c> closed</c>

00:00:56.349 --> 00:00:56.359 align:start position:0%
project Health tracking open and closed
 

00:00:56.359 --> 00:00:58.389 align:start position:0%
project Health tracking open and closed
issues<00:00:56.840><c> and</c><00:00:57.120><c> PRS</c><00:00:57.760><c> and</c><00:00:57.960><c> looking</c><00:00:58.160><c> for</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
issues and PRS and looking for
 

00:00:58.399 --> 00:01:00.310 align:start position:0%
issues and PRS and looking for
repositories<00:00:59.000><c> which</c><00:00:59.120><c> have</c><00:00:59.239><c> gone</c><00:00:59.399><c> inactive</c><00:01:00.199><c> is</c>

00:01:00.310 --> 00:01:00.320 align:start position:0%
repositories which have gone inactive is
 

00:01:00.320 --> 00:01:02.590 align:start position:0%
repositories which have gone inactive is
a<00:01:00.519><c> good</c><00:01:00.760><c> practice</c><00:01:01.640><c> and</c><00:01:01.760><c> there's</c><00:01:01.960><c> now</c><00:01:02.120><c> an</c><00:01:02.280><c> easy</c>

00:01:02.590 --> 00:01:02.600 align:start position:0%
a good practice and there's now an easy
 

00:01:02.600 --> 00:01:05.750 align:start position:0%
a good practice and there's now an easy
way<00:01:02.800><c> to</c><00:01:02.960><c> get</c><00:01:03.239><c> all</c><00:01:03.399><c> of</c><00:01:03.559><c> those</c><00:01:03.760><c> metrics</c><00:01:04.400><c> at</c><00:01:04.760><c> once</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
way to get all of those metrics at once
 

00:01:05.760 --> 00:01:07.510 align:start position:0%
way to get all of those metrics at once
plus<00:01:06.000><c> we've</c><00:01:06.240><c> documented</c><00:01:06.720><c> Integrations</c><00:01:07.320><c> with</c>

00:01:07.510 --> 00:01:07.520 align:start position:0%
plus we've documented Integrations with
 

00:01:07.520 --> 00:01:09.749 align:start position:0%
plus we've documented Integrations with
two<00:01:08.000><c> open</c><00:01:08.240><c> source</c><00:01:08.479><c> graphing</c><00:01:08.880><c> packages</c><00:01:09.600><c> that</c>

00:01:09.749 --> 00:01:09.759 align:start position:0%
two open source graphing packages that
 

00:01:09.759 --> 00:01:12.230 align:start position:0%
two open source graphing packages that
have<00:01:10.000><c> easy</c><00:01:10.320><c> to</c><00:01:10.520><c> configure</c><00:01:11.280><c> SAS</c><00:01:11.720><c> options</c><00:01:12.040><c> to</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
have easy to configure SAS options to
 

00:01:12.240 --> 00:01:15.870 align:start position:0%
have easy to configure SAS options to
get<00:01:12.400><c> started</c><00:01:12.840><c> quickly</c><00:01:13.840><c> cauldron</c><00:01:14.280><c> IO</c><00:01:15.000><c> and</c>

00:01:15.870 --> 00:01:15.880 align:start position:0%
get started quickly cauldron IO and
 

00:01:15.880 --> 00:01:18.070 align:start position:0%
get started quickly cauldron IO and
grafana<00:01:16.880><c> so</c><00:01:17.040><c> if</c><00:01:17.119><c> you're</c><00:01:17.320><c> already</c><00:01:17.640><c> tracking</c>

00:01:18.070 --> 00:01:18.080 align:start position:0%
grafana so if you're already tracking
 

00:01:18.080 --> 00:01:20.069 align:start position:0%
grafana so if you're already tracking
metrics<00:01:18.479><c> for</c><00:01:18.600><c> your</c><00:01:18.799><c> repositories</c><00:01:19.640><c> mix</c><00:01:19.880><c> in</c>

00:01:20.069 --> 00:01:20.079 align:start position:0%
metrics for your repositories mix in
 

00:01:20.079 --> 00:01:22.270 align:start position:0%
metrics for your repositories mix in
these<00:01:20.280><c> queries</c><00:01:20.680><c> to</c><00:01:20.840><c> get</c><00:01:21.000><c> additional</c><00:01:21.520><c> insight</c>

00:01:22.270 --> 00:01:22.280 align:start position:0%
these queries to get additional insight
 

00:01:22.280 --> 00:01:24.069 align:start position:0%
these queries to get additional insight
and<00:01:22.400><c> if</c><00:01:22.520><c> you're</c><00:01:22.680><c> just</c><00:01:22.840><c> starting</c><00:01:23.520><c> check</c><00:01:23.720><c> out</c>

00:01:24.069 --> 00:01:24.079 align:start position:0%
and if you're just starting check out
 

00:01:24.079 --> 00:01:26.469 align:start position:0%
and if you're just starting check out
this<00:01:24.360><c> guide</c><00:01:24.840><c> to</c><00:01:25.000><c> see</c><00:01:25.240><c> how</c><00:01:25.360><c> to</c><00:01:25.520><c> set</c><00:01:25.680><c> up</c><00:01:25.840><c> grafana</c>

00:01:26.469 --> 00:01:26.479 align:start position:0%
this guide to see how to set up grafana
 

00:01:26.479 --> 00:01:28.749 align:start position:0%
this guide to see how to set up grafana
or<00:01:26.720><c> cauldron</c><00:01:27.400><c> to</c><00:01:27.560><c> make</c><00:01:27.720><c> use</c><00:01:27.880><c> of</c><00:01:28.159><c> these</c><00:01:28.520><c> and</c>

00:01:28.749 --> 00:01:28.759 align:start position:0%
or cauldron to make use of these and
 

00:01:28.759 --> 00:01:30.990 align:start position:0%
or cauldron to make use of these and
other<00:01:29.000><c> API</c><00:01:29.360><c> endpoints</c><00:01:30.079><c> to</c><00:01:30.240><c> get</c><00:01:30.360><c> a</c><00:01:30.520><c> holistic</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
other API endpoints to get a holistic
 

00:01:31.000 --> 00:01:35.550 align:start position:0%
other API endpoints to get a holistic
picture<00:01:31.759><c> of</c><00:01:32.000><c> your</c><00:01:32.280><c> Project's</c>

00:01:35.550 --> 00:01:35.560 align:start position:0%
 
 

00:01:35.560 --> 00:01:38.560 align:start position:0%
 
health

