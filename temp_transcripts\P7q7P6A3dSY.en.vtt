WEBVTT
Kind: captions
Language: en

00:00:05.120 --> 00:00:07.110 align:start position:0%
 
i<00:00:05.279><c> am</c><00:00:05.359><c> ludwigi</c><00:00:05.759><c> saxon</c><00:00:06.080><c> i'm</c><00:00:06.240><c> smhi</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
i am ludwigi saxon i'm smhi
 

00:00:07.120 --> 00:00:09.910 align:start position:0%
i am ludwigi saxon i'm smhi
and<00:00:07.279><c> i'm</c><00:00:07.440><c> part</c><00:00:07.759><c> of</c><00:00:07.919><c> the</c><00:00:08.800><c> copernicus</c><00:00:09.440><c> regional</c>

00:00:09.910 --> 00:00:09.920 align:start position:0%
and i'm part of the copernicus regional
 

00:00:09.920 --> 00:00:11.509 align:start position:0%
and i'm part of the copernicus regional
real<00:00:10.160><c> analysis</c><00:00:10.480><c> for</c><00:00:10.639><c> europe</c>

00:00:11.509 --> 00:00:11.519 align:start position:0%
real analysis for europe
 

00:00:11.519 --> 00:00:14.470 align:start position:0%
real analysis for europe
and<00:00:12.160><c> today</c><00:00:12.639><c> i</c><00:00:12.799><c> will</c><00:00:13.200><c> talk</c><00:00:13.440><c> about</c><00:00:13.679><c> the</c><00:00:14.080><c> data</c>

00:00:14.470 --> 00:00:14.480 align:start position:0%
and today i will talk about the data
 

00:00:14.480 --> 00:00:16.550 align:start position:0%
and today i will talk about the data
access<00:00:14.920><c> part</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
access part
 

00:00:16.560 --> 00:00:19.750 align:start position:0%
access part
for<00:00:16.800><c> this</c><00:00:17.119><c> data</c><00:00:17.520><c> sets</c>

00:00:19.750 --> 00:00:19.760 align:start position:0%
for this data sets
 

00:00:19.760 --> 00:00:21.830 align:start position:0%
for this data sets
this<00:00:20.000><c> is</c><00:00:20.080><c> the</c><00:00:20.240><c> outline</c><00:00:20.720><c> of</c><00:00:20.880><c> my</c><00:00:21.279><c> presentation</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
this is the outline of my presentation
 

00:00:21.840 --> 00:00:22.950 align:start position:0%
this is the outline of my presentation
today

00:00:22.950 --> 00:00:22.960 align:start position:0%
today
 

00:00:22.960 --> 00:00:24.470 align:start position:0%
today
the<00:00:23.119><c> main</c><00:00:23.359><c> sections</c><00:00:23.840><c> first</c><00:00:24.080><c> a</c><00:00:24.160><c> short</c>

00:00:24.470 --> 00:00:24.480 align:start position:0%
the main sections first a short
 

00:00:24.480 --> 00:00:26.470 align:start position:0%
the main sections first a short
introduction

00:00:26.470 --> 00:00:26.480 align:start position:0%
introduction
 

00:00:26.480 --> 00:00:28.310 align:start position:0%
introduction
then<00:00:26.640><c> i</c><00:00:26.720><c> will</c><00:00:26.880><c> mention</c><00:00:27.519><c> samuel</c><00:00:27.840><c> did</c><00:00:28.000><c> that</c><00:00:28.240><c> as</c>

00:00:28.310 --> 00:00:28.320 align:start position:0%
then i will mention samuel did that as
 

00:00:28.320 --> 00:00:29.990 align:start position:0%
then i will mention samuel did that as
well<00:00:28.480><c> the</c><00:00:28.640><c> user</c><00:00:28.960><c> learning</c><00:00:29.279><c> services</c>

00:00:29.990 --> 00:00:30.000 align:start position:0%
well the user learning services
 

00:00:30.000 --> 00:00:34.069 align:start position:0%
well the user learning services
or<00:00:30.160><c> uls</c><00:00:32.000><c> and</c><00:00:32.239><c> i</c><00:00:32.320><c> will</c><00:00:32.880><c> dive</c><00:00:33.200><c> into</c><00:00:33.440><c> the</c><00:00:33.680><c> climate</c>

00:00:34.069 --> 00:00:34.079 align:start position:0%
or uls and i will dive into the climate
 

00:00:34.079 --> 00:00:34.950 align:start position:0%
or uls and i will dive into the climate
data<00:00:34.399><c> store</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
data store
 

00:00:34.960 --> 00:00:37.990 align:start position:0%
data store
the<00:00:35.120><c> cds</c><00:00:35.840><c> and</c><00:00:36.960><c> that's</c><00:00:37.200><c> the</c><00:00:37.280><c> main</c><00:00:37.520><c> part</c><00:00:37.760><c> of</c><00:00:37.840><c> this</c>

00:00:37.990 --> 00:00:38.000 align:start position:0%
the cds and that's the main part of this
 

00:00:38.000 --> 00:00:39.110 align:start position:0%
the cds and that's the main part of this
presentation

00:00:39.110 --> 00:00:39.120 align:start position:0%
presentation
 

00:00:39.120 --> 00:00:41.190 align:start position:0%
presentation
and<00:00:39.200><c> the</c><00:00:39.360><c> different</c><00:00:39.840><c> ways</c><00:00:40.160><c> of</c><00:00:40.320><c> accessing</c><00:00:40.879><c> data</c>

00:00:41.190 --> 00:00:41.200 align:start position:0%
and the different ways of accessing data
 

00:00:41.200 --> 00:00:44.950 align:start position:0%
and the different ways of accessing data
in<00:00:41.280><c> the</c><00:00:41.360><c> climate</c><00:00:41.680><c> data</c><00:00:42.840><c> store</c>

00:00:44.950 --> 00:00:44.960 align:start position:0%
in the climate data store
 

00:00:44.960 --> 00:00:47.350 align:start position:0%
in the climate data store
and<00:00:45.280><c> finally</c><00:00:45.760><c> i'll</c><00:00:46.320><c> say</c><00:00:46.559><c> a</c><00:00:46.640><c> few</c><00:00:46.800><c> words</c><00:00:47.120><c> about</c>

00:00:47.350 --> 00:00:47.360 align:start position:0%
and finally i'll say a few words about
 

00:00:47.360 --> 00:00:49.110 align:start position:0%
and finally i'll say a few words about
the<00:00:47.520><c> upcoming</c><00:00:48.079><c> test</c><00:00:48.320><c> release</c><00:00:48.719><c> of</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
the upcoming test release of
 

00:00:49.120 --> 00:00:51.510 align:start position:0%
the upcoming test release of
the<00:00:49.280><c> arc</c><00:00:49.520><c> degree</c><00:00:49.920><c> analysis</c><00:00:50.640><c> in</c><00:00:50.800><c> the</c><00:00:51.039><c> climate</c>

00:00:51.510 --> 00:00:51.520 align:start position:0%
the arc degree analysis in the climate
 

00:00:51.520 --> 00:00:53.189 align:start position:0%
the arc degree analysis in the climate
data<00:00:51.840><c> store</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
data store
 

00:00:53.199 --> 00:00:56.389 align:start position:0%
data store
so<00:00:53.840><c> first</c><00:00:54.559><c> off</c><00:00:55.760><c> uh</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
so first off uh
 

00:00:56.399 --> 00:00:58.950 align:start position:0%
so first off uh
since<00:00:56.800><c> there</c><00:00:57.120><c> are</c><00:00:57.280><c> no</c><00:00:57.920><c> car</c><00:00:58.160><c> or</c><00:00:58.320><c> sera</c><00:00:58.640><c> data</c>

00:00:58.950 --> 00:00:58.960 align:start position:0%
since there are no car or sera data
 

00:00:58.960 --> 00:01:00.150 align:start position:0%
since there are no car or sera data
currently<00:00:59.440><c> publicly</c>

00:01:00.150 --> 00:01:00.160 align:start position:0%
currently publicly
 

00:01:00.160 --> 00:01:03.189 align:start position:0%
currently publicly
available<00:01:01.280><c> in</c><00:01:01.440><c> the</c><00:01:01.760><c> cds</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
available in the cds
 

00:01:03.199 --> 00:01:06.149 align:start position:0%
available in the cds
uh<00:01:04.000><c> we'll</c><00:01:04.720><c> in</c><00:01:04.879><c> this</c><00:01:05.040><c> presentation</c><00:01:05.680><c> i</c><00:01:05.760><c> will</c>

00:01:06.149 --> 00:01:06.159 align:start position:0%
uh we'll in this presentation i will
 

00:01:06.159 --> 00:01:06.870 align:start position:0%
uh we'll in this presentation i will
look<00:01:06.400><c> at</c><00:01:06.560><c> the</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
look at the
 

00:01:06.880 --> 00:01:10.469 align:start position:0%
look at the
ura<00:01:07.280><c> data</c><00:01:07.600><c> sets</c><00:01:08.560><c> which</c><00:01:08.880><c> are</c><00:01:09.040><c> available</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
ura data sets which are available
 

00:01:10.479 --> 00:01:13.670 align:start position:0%
ura data sets which are available
so<00:01:10.640><c> that's</c><00:01:10.880><c> a</c><00:01:12.240><c> thing</c><00:01:12.479><c> to</c><00:01:12.960><c> know</c><00:01:13.280><c> here</c>

00:01:13.670 --> 00:01:13.680 align:start position:0%
so that's a thing to know here
 

00:01:13.680 --> 00:01:18.070 align:start position:0%
so that's a thing to know here
but<00:01:15.520><c> yeah</c><00:01:16.159><c> the</c><00:01:16.400><c> the</c><00:01:16.720><c> plan</c><00:01:17.040><c> is</c><00:01:17.280><c> to</c><00:01:17.520><c> pre-release</c>

00:01:18.070 --> 00:01:18.080 align:start position:0%
but yeah the the plan is to pre-release
 

00:01:18.080 --> 00:01:19.429 align:start position:0%
but yeah the the plan is to pre-release
these<00:01:18.320><c> test</c><00:01:18.640><c> data</c>

00:01:19.429 --> 00:01:19.439 align:start position:0%
these test data
 

00:01:19.439 --> 00:01:22.149 align:start position:0%
these test data
uh<00:01:19.920><c> for</c><00:01:20.080><c> a</c><00:01:20.159><c> shorter</c><00:01:20.560><c> period</c><00:01:20.960><c> of</c><00:01:21.119><c> time</c><00:01:21.680><c> for</c><00:01:21.840><c> both</c>

00:01:22.149 --> 00:01:22.159 align:start position:0%
uh for a shorter period of time for both
 

00:01:22.159 --> 00:01:23.270 align:start position:0%
uh for a shorter period of time for both
cara<00:01:22.479><c> and</c><00:01:22.720><c> sarah</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
cara and sarah
 

00:01:23.280 --> 00:01:27.270 align:start position:0%
cara and sarah
and<00:01:23.439><c> i'll</c><00:01:23.520><c> come</c><00:01:23.680><c> back</c><00:01:23.840><c> to</c><00:01:24.840><c> that</c>

00:01:27.270 --> 00:01:27.280 align:start position:0%
and i'll come back to that
 

00:01:27.280 --> 00:01:29.990 align:start position:0%
and i'll come back to that
and<00:01:27.439><c> the</c><00:01:27.600><c> data</c><00:01:28.000><c> access</c><00:01:28.640><c> from</c><00:01:28.880><c> the</c><00:01:28.960><c> cds</c><00:01:29.520><c> from</c>

00:01:29.990 --> 00:01:30.000 align:start position:0%
and the data access from the cds from
 

00:01:30.000 --> 00:01:31.830 align:start position:0%
and the data access from the cds from
before<00:01:30.400><c> cara</c><00:01:30.799><c> and</c><00:01:30.960><c> sarah</c><00:01:31.280><c> will</c>

00:01:31.830 --> 00:01:31.840 align:start position:0%
before cara and sarah will
 

00:01:31.840 --> 00:01:34.390 align:start position:0%
before cara and sarah will
be<00:01:32.000><c> very</c><00:01:32.240><c> similar</c><00:01:32.960><c> to</c><00:01:33.119><c> the</c><00:01:33.280><c> current</c><00:01:33.680><c> ura</c><00:01:34.079><c> data</c>

00:01:34.390 --> 00:01:34.400 align:start position:0%
be very similar to the current ura data
 

00:01:34.400 --> 00:01:35.510 align:start position:0%
be very similar to the current ura data
so<00:01:34.560><c> this</c>

00:01:35.510 --> 00:01:35.520 align:start position:0%
so this
 

00:01:35.520 --> 00:01:37.270 align:start position:0%
so this
presentation<00:01:36.079><c> will</c><00:01:36.320><c> probably</c><00:01:36.640><c> hold</c><00:01:36.960><c> up</c>

00:01:37.270 --> 00:01:37.280 align:start position:0%
presentation will probably hold up
 

00:01:37.280 --> 00:01:38.870 align:start position:0%
presentation will probably hold up
pretty<00:01:37.520><c> well</c><00:01:37.840><c> for</c><00:01:38.159><c> for</c><00:01:38.400><c> that</c>

00:01:38.870 --> 00:01:38.880 align:start position:0%
pretty well for for that
 

00:01:38.880 --> 00:01:42.149 align:start position:0%
pretty well for for that
data<00:01:39.200><c> as</c><00:01:39.360><c> well</c>

00:01:42.149 --> 00:01:42.159 align:start position:0%
 
 

00:01:42.159 --> 00:01:44.469 align:start position:0%
 
yeah<00:01:42.399><c> the</c><00:01:42.479><c> user</c><00:01:42.799><c> learning</c><00:01:43.119><c> services</c><00:01:43.840><c> i</c><00:01:44.240><c> also</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
yeah the user learning services i also
 

00:01:44.479 --> 00:01:46.310 align:start position:0%
yeah the user learning services i also
recommend<00:01:45.119><c> because</c><00:01:45.360><c> a</c><00:01:45.439><c> lot</c><00:01:45.680><c> of</c><00:01:45.759><c> these</c><00:01:46.000><c> things</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
recommend because a lot of these things
 

00:01:46.320 --> 00:01:47.749 align:start position:0%
recommend because a lot of these things
that<00:01:46.399><c> i'm</c><00:01:46.560><c> going</c><00:01:46.640><c> to</c><00:01:46.799><c> mention</c><00:01:47.200><c> are</c><00:01:47.280><c> covered</c><00:01:47.680><c> in</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
that i'm going to mention are covered in
 

00:01:47.759 --> 00:01:48.870 align:start position:0%
that i'm going to mention are covered in
these<00:01:48.000><c> lessons</c>

00:01:48.870 --> 00:01:48.880 align:start position:0%
these lessons
 

00:01:48.880 --> 00:01:51.990 align:start position:0%
these lessons
the<00:01:49.040><c> url</c><00:01:49.439><c> lessons</c><00:01:50.000><c> for</c><00:01:50.399><c> for</c><00:01:50.640><c> uh</c><00:01:51.600><c> in</c><00:01:51.680><c> the</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
the url lessons for for uh in the
 

00:01:52.000 --> 00:01:55.190 align:start position:0%
the url lessons for for uh in the
in<00:01:52.159><c> the</c><00:01:52.240><c> user</c><00:01:52.720><c> learning</c><00:01:53.040><c> services</c><00:01:53.920><c> so</c><00:01:54.720><c> log</c><00:01:55.040><c> in</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
in the user learning services so log in
 

00:01:55.200 --> 00:01:56.709 align:start position:0%
in the user learning services so log in
there<00:01:55.520><c> and</c><00:01:55.759><c> search</c><00:01:56.079><c> for</c><00:01:56.240><c> you</c><00:01:56.399><c> error</c><00:01:56.560><c> and</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
there and search for you error and
 

00:01:56.719 --> 00:01:57.590 align:start position:0%
there and search for you error and
you'll<00:01:56.880><c> see</c><00:01:57.040><c> these</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
you'll see these
 

00:01:57.600 --> 00:02:00.789 align:start position:0%
you'll see these
three<00:01:57.920><c> lessons</c><00:01:59.280><c> there's</c><00:01:59.520><c> a</c><00:01:59.600><c> lot</c><00:01:59.840><c> about</c><00:02:00.479><c> data</c>

00:02:00.789 --> 00:02:00.799 align:start position:0%
three lessons there's a lot about data
 

00:02:00.799 --> 00:02:04.310 align:start position:0%
three lessons there's a lot about data
processing<00:02:01.360><c> and</c><00:02:01.439><c> retrieval</c><00:02:02.000><c> there</c>

00:02:04.310 --> 00:02:04.320 align:start position:0%
 
 

00:02:04.320 --> 00:02:08.070 align:start position:0%
 
just<00:02:04.640><c> a</c><00:02:04.880><c> tip</c><00:02:07.119><c> and</c><00:02:07.280><c> then</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
just a tip and then
 

00:02:08.080 --> 00:02:10.869 align:start position:0%
just a tip and then
the<00:02:08.399><c> climate</c><00:02:08.879><c> data</c><00:02:09.200><c> store</c><00:02:09.920><c> the</c><00:02:10.160><c> cornerstone</c>

00:02:10.869 --> 00:02:10.879 align:start position:0%
the climate data store the cornerstone
 

00:02:10.879 --> 00:02:11.910 align:start position:0%
the climate data store the cornerstone
of<00:02:10.959><c> the</c><00:02:11.120><c> c3s</c>

00:02:11.910 --> 00:02:11.920 align:start position:0%
of the c3s
 

00:02:11.920 --> 00:02:15.270 align:start position:0%
of the c3s
infrastructure<00:02:14.319><c> it</c><00:02:14.480><c> is</c><00:02:14.560><c> hosting</c><00:02:14.879><c> a</c><00:02:14.959><c> lot</c><00:02:15.120><c> of</c>

00:02:15.270 --> 00:02:15.280 align:start position:0%
infrastructure it is hosting a lot of
 

00:02:15.280 --> 00:02:16.390 align:start position:0%
infrastructure it is hosting a lot of
data<00:02:15.599><c> for</c><00:02:15.760><c> example</c>

00:02:16.390 --> 00:02:16.400 align:start position:0%
data for example
 

00:02:16.400 --> 00:02:19.430 align:start position:0%
data for example
all<00:02:16.640><c> the</c><00:02:16.800><c> uera</c><00:02:17.280><c> nata</c><00:02:19.040><c> and</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
all the uera nata and
 

00:02:19.440 --> 00:02:22.150 align:start position:0%
all the uera nata and
the<00:02:19.599><c> main</c><00:02:19.920><c> options</c><00:02:20.400><c> for</c><00:02:20.800><c> data</c><00:02:21.200><c> access</c><00:02:21.760><c> in</c><00:02:21.840><c> the</c>

00:02:22.150 --> 00:02:22.160 align:start position:0%
the main options for data access in the
 

00:02:22.160 --> 00:02:23.910 align:start position:0%
the main options for data access in the
cds

00:02:23.910 --> 00:02:23.920 align:start position:0%
cds
 

00:02:23.920 --> 00:02:26.869 align:start position:0%
cds
is<00:02:24.000><c> to</c><00:02:24.160><c> download</c><00:02:24.640><c> the</c><00:02:25.040><c> data</c><00:02:25.360><c> directly</c><00:02:26.720><c> through</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
is to download the data directly through
 

00:02:26.879 --> 00:02:29.910 align:start position:0%
is to download the data directly through
a<00:02:27.040><c> form</c><00:02:27.360><c> in</c><00:02:27.520><c> your</c><00:02:27.680><c> web</c><00:02:27.920><c> browser</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
a form in your web browser
 

00:02:29.920 --> 00:02:31.830 align:start position:0%
a form in your web browser
or<00:02:30.080><c> you</c><00:02:30.160><c> can</c><00:02:30.400><c> install</c><00:02:30.800><c> a</c><00:02:30.959><c> python</c><00:02:31.440><c> package</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
or you can install a python package
 

00:02:31.840 --> 00:02:33.430 align:start position:0%
or you can install a python package
called<00:02:32.160><c> cbs</c><00:02:32.720><c> api</c>

00:02:33.430 --> 00:02:33.440 align:start position:0%
called cbs api
 

00:02:33.440 --> 00:02:35.110 align:start position:0%
called cbs api
and<00:02:33.599><c> write</c><00:02:33.920><c> scripts</c><00:02:34.319><c> and</c><00:02:34.480><c> automate</c><00:02:34.959><c> the</c>

00:02:35.110 --> 00:02:35.120 align:start position:0%
and write scripts and automate the
 

00:02:35.120 --> 00:02:37.910 align:start position:0%
and write scripts and automate the
downloading<00:02:35.760><c> process</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
downloading process
 

00:02:37.920 --> 00:02:41.110 align:start position:0%
downloading process
and<00:02:38.080><c> you</c><00:02:38.160><c> can</c><00:02:38.319><c> also</c><00:02:38.480><c> use</c><00:02:38.720><c> the</c><00:02:38.800><c> cds</c><00:02:39.440><c> toolbox</c><00:02:40.879><c> to</c>

00:02:41.110 --> 00:02:41.120 align:start position:0%
and you can also use the cds toolbox to
 

00:02:41.120 --> 00:02:42.949 align:start position:0%
and you can also use the cds toolbox to
write<00:02:41.440><c> and</c><00:02:41.599><c> execute</c>

00:02:42.949 --> 00:02:42.959 align:start position:0%
write and execute
 

00:02:42.959 --> 00:02:44.710 align:start position:0%
write and execute
scripts<00:02:43.360><c> for</c><00:02:43.519><c> extraction</c><00:02:44.080><c> and</c><00:02:44.160><c> blocking</c><00:02:44.560><c> and</c>

00:02:44.710 --> 00:02:44.720 align:start position:0%
scripts for extraction and blocking and
 

00:02:44.720 --> 00:02:46.710 align:start position:0%
scripts for extraction and blocking and
processing<00:02:45.280><c> i'll</c><00:02:45.519><c> show</c><00:02:45.760><c> a</c><00:02:46.000><c> slide</c><00:02:46.319><c> of</c><00:02:46.400><c> that</c><00:02:46.640><c> in</c>

00:02:46.710 --> 00:02:46.720 align:start position:0%
processing i'll show a slide of that in
 

00:02:46.720 --> 00:02:48.309 align:start position:0%
processing i'll show a slide of that in
the<00:02:46.879><c> end</c>

00:02:48.309 --> 00:02:48.319 align:start position:0%
the end
 

00:02:48.319 --> 00:02:51.190 align:start position:0%
the end
so<00:02:48.480><c> this</c><00:02:48.720><c> is</c><00:02:48.879><c> the</c><00:02:49.519><c> url</c><00:02:50.080><c> to</c><00:02:50.239><c> the</c><00:02:50.480><c> home</c><00:02:50.720><c> page</c><00:02:51.120><c> of</c>

00:02:51.190 --> 00:02:51.200 align:start position:0%
so this is the url to the home page of
 

00:02:51.200 --> 00:02:53.750 align:start position:0%
so this is the url to the home page of
the<00:02:51.360><c> cds</c>

00:02:53.750 --> 00:02:53.760 align:start position:0%
the cds
 

00:02:53.760 --> 00:02:56.869 align:start position:0%
the cds
and<00:02:53.920><c> it</c><00:02:54.160><c> looks</c><00:02:54.560><c> something</c><00:02:55.040><c> like</c><00:02:55.280><c> this</c><00:02:56.720><c> there</c>

00:02:56.869 --> 00:02:56.879 align:start position:0%
and it looks something like this there
 

00:02:56.879 --> 00:02:58.710 align:start position:0%
and it looks something like this there
is<00:02:57.040><c> a</c><00:02:57.200><c> search</c><00:02:57.519><c> box</c>

00:02:58.710 --> 00:02:58.720 align:start position:0%
is a search box
 

00:02:58.720 --> 00:03:01.910 align:start position:0%
is a search box
on<00:02:58.800><c> the</c><00:02:58.959><c> start</c><00:02:59.200><c> page</c><00:03:00.080><c> if</c><00:03:00.239><c> you</c><00:03:00.800><c> would</c><00:03:01.120><c> enter</c><00:03:01.599><c> new</c>

00:03:01.910 --> 00:03:01.920 align:start position:0%
on the start page if you would enter new
 

00:03:01.920 --> 00:03:05.670 align:start position:0%
on the start page if you would enter new
era<00:03:02.239><c> there</c><00:03:02.480><c> for</c><00:03:02.640><c> example</c><00:03:03.280><c> and</c><00:03:03.519><c> click</c><00:03:03.920><c> search</c>

00:03:05.670 --> 00:03:05.680 align:start position:0%
era there for example and click search
 

00:03:05.680 --> 00:03:08.550 align:start position:0%
era there for example and click search
you'll<00:03:05.840><c> get</c><00:03:06.080><c> to</c><00:03:06.159><c> a</c><00:03:06.319><c> list</c><00:03:06.640><c> of</c><00:03:06.720><c> the</c><00:03:07.280><c> data</c><00:03:07.680><c> sets</c><00:03:08.400><c> in</c>

00:03:08.550 --> 00:03:08.560 align:start position:0%
you'll get to a list of the data sets in
 

00:03:08.560 --> 00:03:09.190 align:start position:0%
you'll get to a list of the data sets in
the

00:03:09.190 --> 00:03:09.200 align:start position:0%
the
 

00:03:09.200 --> 00:03:12.390 align:start position:0%
the
your<00:03:09.440><c> era</c><00:03:11.680><c> so</c>

00:03:12.390 --> 00:03:12.400 align:start position:0%
your era so
 

00:03:12.400 --> 00:03:14.070 align:start position:0%
your era so
this<00:03:12.640><c> is</c><00:03:12.720><c> the</c><00:03:12.879><c> starting</c><00:03:13.280><c> point</c><00:03:13.599><c> for</c>

00:03:14.070 --> 00:03:14.080 align:start position:0%
this is the starting point for
 

00:03:14.080 --> 00:03:16.390 align:start position:0%
this is the starting point for
downloading<00:03:14.720><c> data</c>

00:03:16.390 --> 00:03:16.400 align:start position:0%
downloading data
 

00:03:16.400 --> 00:03:19.670 align:start position:0%
downloading data
there<00:03:16.560><c> are</c><00:03:16.879><c> different</c><00:03:17.680><c> levels</c>

00:03:19.670 --> 00:03:19.680 align:start position:0%
there are different levels
 

00:03:19.680 --> 00:03:21.830 align:start position:0%
there are different levels
of<00:03:19.840><c> data</c><00:03:20.480><c> it</c><00:03:20.640><c> is</c><00:03:20.720><c> divided</c><00:03:21.280><c> into</c><00:03:21.440><c> different</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
of data it is divided into different
 

00:03:21.840 --> 00:03:23.270 align:start position:0%
of data it is divided into different
parts<00:03:22.159><c> so</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
parts so
 

00:03:23.280 --> 00:03:26.149 align:start position:0%
parts so
um<00:03:24.159><c> worth</c><00:03:24.400><c> mentioning</c><00:03:24.799><c> here</c><00:03:25.040><c> is</c><00:03:25.200><c> that</c><00:03:25.760><c> this</c><00:03:26.000><c> is</c>

00:03:26.149 --> 00:03:26.159 align:start position:0%
um worth mentioning here is that this is
 

00:03:26.159 --> 00:03:28.390 align:start position:0%
um worth mentioning here is that this is
the<00:03:26.319><c> analysis</c><00:03:27.040><c> data</c><00:03:27.360><c> from</c><00:03:27.519><c> the</c><00:03:27.680><c> uera</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
the analysis data from the uera
 

00:03:28.400 --> 00:03:31.270 align:start position:0%
the analysis data from the uera
project<00:03:29.200><c> that</c><00:03:29.440><c> is</c><00:03:30.000><c> available</c><00:03:30.720><c> here</c><00:03:31.120><c> from</c>

00:03:31.270 --> 00:03:31.280 align:start position:0%
project that is available here from
 

00:03:31.280 --> 00:03:32.470 align:start position:0%
project that is available here from
these

00:03:32.470 --> 00:03:32.480 align:start position:0%
these
 

00:03:32.480 --> 00:03:35.110 align:start position:0%
these
data<00:03:32.799><c> sets</c><00:03:33.599><c> and</c><00:03:33.760><c> they</c><00:03:33.920><c> have</c><00:03:34.480><c> access</c><00:03:34.879><c> both</c>

00:03:35.110 --> 00:03:35.120 align:start position:0%
data sets and they have access both
 

00:03:35.120 --> 00:03:35.750 align:start position:0%
data sets and they have access both
through<00:03:35.280><c> the</c><00:03:35.360><c> web</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
through the web
 

00:03:35.760 --> 00:03:39.509 align:start position:0%
through the web
form<00:03:36.159><c> and</c><00:03:36.480><c> through</c><00:03:36.720><c> the</c><00:03:37.200><c> cds</c><00:03:37.680><c> toolbox</c>

00:03:39.509 --> 00:03:39.519 align:start position:0%
form and through the cds toolbox
 

00:03:39.519 --> 00:03:43.830 align:start position:0%
form and through the cds toolbox
so<00:03:39.920><c> you</c><00:03:40.080><c> can</c><00:03:40.959><c> access</c><00:03:41.360><c> those</c><00:03:41.680><c> in</c><00:03:42.319><c> all</c><00:03:42.480><c> the</c><00:03:42.560><c> ways</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
so you can access those in all the ways
 

00:03:43.840 --> 00:03:46.149 align:start position:0%
so you can access those in all the ways
but<00:03:44.000><c> there</c><00:03:44.159><c> is</c><00:03:44.239><c> also</c><00:03:44.560><c> the</c><00:03:44.720><c> complete</c><00:03:45.280><c> ura</c><00:03:45.840><c> data</c>

00:03:46.149 --> 00:03:46.159 align:start position:0%
but there is also the complete ura data
 

00:03:46.159 --> 00:03:47.110 align:start position:0%
but there is also the complete ura data
set

00:03:47.110 --> 00:03:47.120 align:start position:0%
set
 

00:03:47.120 --> 00:03:50.390 align:start position:0%
set
which<00:03:47.599><c> contains</c><00:03:48.480><c> all</c><00:03:48.640><c> the</c><00:03:48.799><c> data</c><00:03:49.519><c> you</c><00:03:50.000><c> also</c><00:03:50.239><c> the</c>

00:03:50.390 --> 00:03:50.400 align:start position:0%
which contains all the data you also the
 

00:03:50.400 --> 00:03:53.110 align:start position:0%
which contains all the data you also the
modern<00:03:50.720><c> level</c><00:03:51.040><c> data</c><00:03:51.360><c> and</c><00:03:51.440><c> the</c><00:03:51.680><c> forecast</c><00:03:52.239><c> data</c>

00:03:53.110 --> 00:03:53.120 align:start position:0%
modern level data and the forecast data
 

00:03:53.120 --> 00:03:55.509 align:start position:0%
modern level data and the forecast data
and<00:03:53.280><c> this</c><00:03:53.439><c> can</c><00:03:53.680><c> only</c><00:03:53.920><c> be</c><00:03:54.080><c> accessed</c><00:03:54.560><c> by</c><00:03:54.720><c> the</c><00:03:54.879><c> cds</c>

00:03:55.509 --> 00:03:55.519 align:start position:0%
and this can only be accessed by the cds
 

00:03:55.519 --> 00:03:56.949 align:start position:0%
and this can only be accessed by the cds
api

00:03:56.949 --> 00:03:56.959 align:start position:0%
api
 

00:03:56.959 --> 00:04:02.390 align:start position:0%
api
so<00:03:57.120><c> that's</c><00:03:57.920><c> good</c><00:03:58.159><c> to</c><00:03:58.840><c> know</c>

00:04:02.390 --> 00:04:02.400 align:start position:0%
 
 

00:04:02.400 --> 00:04:05.190 align:start position:0%
 
and<00:04:02.560><c> if</c><00:04:02.799><c> you</c><00:04:04.080><c> click</c><00:04:04.480><c> for</c><00:04:04.640><c> example</c><00:04:04.959><c> on</c><00:04:05.040><c> the</c>

00:04:05.190 --> 00:04:05.200 align:start position:0%
and if you click for example on the
 

00:04:05.200 --> 00:04:05.830 align:start position:0%
and if you click for example on the
single

00:04:05.830 --> 00:04:05.840 align:start position:0%
single
 

00:04:05.840 --> 00:04:09.030 align:start position:0%
single
levels<00:04:06.319><c> data</c><00:04:06.640><c> set</c><00:04:06.879><c> here</c><00:04:07.920><c> you</c><00:04:08.080><c> will</c>

00:04:09.030 --> 00:04:09.040 align:start position:0%
levels data set here you will
 

00:04:09.040 --> 00:04:12.470 align:start position:0%
levels data set here you will
get<00:04:09.439><c> to</c><00:04:09.760><c> a</c><00:04:10.319><c> page</c><00:04:10.799><c> with</c><00:04:11.120><c> three</c><00:04:11.519><c> tabs</c>

00:04:12.470 --> 00:04:12.480 align:start position:0%
get to a page with three tabs
 

00:04:12.480 --> 00:04:15.190 align:start position:0%
get to a page with three tabs
the<00:04:12.720><c> overview</c><00:04:13.280><c> the</c><00:04:13.439><c> download</c><00:04:14.080><c> data</c><00:04:14.879><c> and</c><00:04:15.040><c> the</c>

00:04:15.190 --> 00:04:15.200 align:start position:0%
the overview the download data and the
 

00:04:15.200 --> 00:04:18.150 align:start position:0%
the overview the download data and the
documentation

00:04:18.150 --> 00:04:18.160 align:start position:0%
 
 

00:04:18.160 --> 00:04:21.110 align:start position:0%
 
and<00:04:19.040><c> please</c><00:04:19.440><c> read</c><00:04:19.919><c> the</c><00:04:20.079><c> user</c><00:04:20.479><c> guide</c><00:04:20.799><c> under</c>

00:04:21.110 --> 00:04:21.120 align:start position:0%
and please read the user guide under
 

00:04:21.120 --> 00:04:22.310 align:start position:0%
and please read the user guide under
documentation

00:04:22.310 --> 00:04:22.320 align:start position:0%
documentation
 

00:04:22.320 --> 00:04:25.670 align:start position:0%
documentation
and<00:04:22.880><c> there</c><00:04:23.040><c> are</c><00:04:23.199><c> listed</c><00:04:23.600><c> issues</c><00:04:24.800><c> known</c><00:04:25.120><c> issues</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
and there are listed issues known issues
 

00:04:25.680 --> 00:04:27.830 align:start position:0%
and there are listed issues known issues
with<00:04:25.919><c> the</c><00:04:26.000><c> datasets</c><00:04:26.960><c> and</c><00:04:27.199><c> a</c><00:04:27.280><c> lot</c><00:04:27.440><c> of</c><00:04:27.600><c> other</c>

00:04:27.830 --> 00:04:27.840 align:start position:0%
with the datasets and a lot of other
 

00:04:27.840 --> 00:04:29.510 align:start position:0%
with the datasets and a lot of other
things<00:04:28.240><c> and</c><00:04:28.320><c> then</c><00:04:28.560><c> the</c><00:04:28.720><c> overview</c><00:04:29.199><c> as</c><00:04:29.360><c> well</c>

00:04:29.510 --> 00:04:29.520 align:start position:0%
things and then the overview as well
 

00:04:29.520 --> 00:04:31.749 align:start position:0%
things and then the overview as well
we'll<00:04:29.759><c> read</c><00:04:30.000><c> those</c><00:04:30.240><c> before</c><00:04:30.639><c> starting</c>

00:04:31.749 --> 00:04:31.759 align:start position:0%
we'll read those before starting
 

00:04:31.759 --> 00:04:33.270 align:start position:0%
we'll read those before starting
but<00:04:31.919><c> today</c><00:04:32.240><c> we're</c><00:04:32.400><c> going</c><00:04:32.479><c> to</c><00:04:32.720><c> focus</c><00:04:33.120><c> on</c><00:04:33.199><c> the</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
but today we're going to focus on the
 

00:04:33.280 --> 00:04:35.270 align:start position:0%
but today we're going to focus on the
download<00:04:33.680><c> data</c><00:04:34.080><c> tab</c><00:04:34.560><c> because</c><00:04:34.800><c> that's</c><00:04:35.040><c> the</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
download data tab because that's the
 

00:04:35.280 --> 00:04:36.950 align:start position:0%
download data tab because that's the
access

00:04:36.950 --> 00:04:36.960 align:start position:0%
access
 

00:04:36.960 --> 00:04:40.310 align:start position:0%
access
tab<00:04:38.000><c> here</c><00:04:38.160><c> you</c><00:04:38.320><c> can</c><00:04:38.479><c> choose</c>

00:04:40.310 --> 00:04:40.320 align:start position:0%
tab here you can choose
 

00:04:40.320 --> 00:04:42.870 align:start position:0%
tab here you can choose
which<00:04:41.199><c> model</c><00:04:41.520><c> system</c><00:04:42.000><c> you</c><00:04:42.080><c> want</c><00:04:42.320><c> to</c><00:04:42.479><c> download</c>

00:04:42.870 --> 00:04:42.880 align:start position:0%
which model system you want to download
 

00:04:42.880 --> 00:04:44.230 align:start position:0%
which model system you want to download
data<00:04:43.199><c> from</c>

00:04:44.230 --> 00:04:44.240 align:start position:0%
data from
 

00:04:44.240 --> 00:04:47.110 align:start position:0%
data from
and<00:04:44.639><c> which</c><00:04:45.040><c> variable</c><00:04:46.160><c> and</c><00:04:46.320><c> then</c><00:04:46.479><c> you</c><00:04:46.639><c> fill</c><00:04:46.880><c> in</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
and which variable and then you fill in
 

00:04:47.120 --> 00:04:47.670 align:start position:0%
and which variable and then you fill in
all<00:04:47.199><c> the</c>

00:04:47.670 --> 00:04:47.680 align:start position:0%
all the
 

00:04:47.680 --> 00:04:51.030 align:start position:0%
all the
years<00:04:48.160><c> and</c><00:04:48.400><c> the</c><00:04:48.560><c> months</c><00:04:49.120><c> and</c><00:04:50.000><c> times</c><00:04:50.400><c> texts</c><00:04:50.880><c> you</c>

00:04:51.030 --> 00:04:51.040 align:start position:0%
years and the months and times texts you
 

00:04:51.040 --> 00:04:54.150 align:start position:0%
years and the months and times texts you
are<00:04:51.199><c> interested</c><00:04:51.680><c> in</c>

00:04:54.150 --> 00:04:54.160 align:start position:0%
 
 

00:04:54.160 --> 00:04:56.790 align:start position:0%
 
and<00:04:54.400><c> the</c><00:04:54.639><c> format</c><00:04:55.600><c> of</c><00:04:55.680><c> the</c><00:04:55.919><c> data</c><00:04:56.479><c> here</c><00:04:56.639><c> it's</c>

00:04:56.790 --> 00:04:56.800 align:start position:0%
and the format of the data here it's
 

00:04:56.800 --> 00:04:58.950 align:start position:0%
and the format of the data here it's
worth<00:04:57.120><c> noting</c><00:04:57.440><c> that</c><00:04:57.520><c> the</c><00:04:57.680><c> net</c><00:04:57.919><c> cdf</c>

00:04:58.950 --> 00:04:58.960 align:start position:0%
worth noting that the net cdf
 

00:04:58.960 --> 00:05:01.510 align:start position:0%
worth noting that the net cdf
format<00:04:59.360><c> is</c><00:04:59.440><c> still</c><00:04:59.680><c> experimental</c><00:05:00.639><c> the</c><00:05:00.960><c> grid</c><00:05:01.360><c> is</c>

00:05:01.510 --> 00:05:01.520 align:start position:0%
format is still experimental the grid is
 

00:05:01.520 --> 00:05:03.830 align:start position:0%
format is still experimental the grid is
more

00:05:03.830 --> 00:05:03.840 align:start position:0%
 
 

00:05:03.840 --> 00:05:07.909 align:start position:0%
 
tested<00:05:04.240><c> out</c>

00:05:07.909 --> 00:05:07.919 align:start position:0%
 
 

00:05:07.919 --> 00:05:09.749 align:start position:0%
 
so<00:05:08.160><c> when</c><00:05:08.320><c> you're</c><00:05:08.479><c> done</c><00:05:08.720><c> with</c><00:05:08.880><c> this</c><00:05:09.120><c> you</c><00:05:09.280><c> can</c>

00:05:09.749 --> 00:05:09.759 align:start position:0%
so when you're done with this you can
 

00:05:09.759 --> 00:05:11.189 align:start position:0%
so when you're done with this you can
click<00:05:10.160><c> the</c>

00:05:11.189 --> 00:05:11.199 align:start position:0%
click the
 

00:05:11.199 --> 00:05:14.390 align:start position:0%
click the
submit<00:05:11.840><c> form</c><00:05:12.479><c> button</c><00:05:13.280><c> down</c><00:05:13.520><c> here</c><00:05:14.000><c> to</c>

00:05:14.390 --> 00:05:14.400 align:start position:0%
submit form button down here to
 

00:05:14.400 --> 00:05:18.950 align:start position:0%
submit form button down here to
retrieve<00:05:15.039><c> the</c><00:05:15.280><c> chosen</c><00:05:15.680><c> data</c><00:05:16.479><c> in</c><00:05:16.720><c> a</c><00:05:17.039><c> grid</c><00:05:17.360><c> file</c>

00:05:18.950 --> 00:05:18.960 align:start position:0%
retrieve the chosen data in a grid file
 

00:05:18.960 --> 00:05:20.710 align:start position:0%
retrieve the chosen data in a grid file
so<00:05:19.120><c> that's</c><00:05:19.440><c> an</c><00:05:19.600><c> easy</c><00:05:19.840><c> way</c><00:05:20.000><c> to</c><00:05:20.160><c> get</c><00:05:20.400><c> started</c>

00:05:20.710 --> 00:05:20.720 align:start position:0%
so that's an easy way to get started
 

00:05:20.720 --> 00:05:22.070 align:start position:0%
so that's an easy way to get started
knowing<00:05:21.039><c> these</c><00:05:21.280><c> data</c><00:05:21.600><c> sets</c>

00:05:22.070 --> 00:05:22.080 align:start position:0%
knowing these data sets
 

00:05:22.080 --> 00:05:25.270 align:start position:0%
knowing these data sets
and<00:05:23.039><c> downloading</c><00:05:24.800><c> if</c><00:05:24.960><c> you're</c>

00:05:25.270 --> 00:05:25.280 align:start position:0%
and downloading if you're
 

00:05:25.280 --> 00:05:27.430 align:start position:0%
and downloading if you're
only<00:05:25.520><c> interested</c><00:05:25.840><c> in</c><00:05:26.000><c> small</c><00:05:26.160><c> amounts</c><00:05:26.560><c> this</c>

00:05:27.430 --> 00:05:27.440 align:start position:0%
only interested in small amounts this
 

00:05:27.440 --> 00:05:30.550 align:start position:0%
only interested in small amounts this
could<00:05:27.680><c> cover</c><00:05:28.000><c> your</c><00:05:28.160><c> needs</c>

00:05:30.550 --> 00:05:30.560 align:start position:0%
 
 

00:05:30.560 --> 00:05:33.670 align:start position:0%
 
but<00:05:30.880><c> if</c><00:05:31.039><c> you</c><00:05:31.440><c> want</c><00:05:31.600><c> to</c><00:05:31.759><c> do</c><00:05:32.240><c> uh</c><00:05:32.639><c> processing</c><00:05:33.520><c> for</c>

00:05:33.670 --> 00:05:33.680 align:start position:0%
but if you want to do uh processing for
 

00:05:33.680 --> 00:05:34.310 align:start position:0%
but if you want to do uh processing for
example

00:05:34.310 --> 00:05:34.320 align:start position:0%
example
 

00:05:34.320 --> 00:05:36.870 align:start position:0%
example
and<00:05:35.199><c> then</c><00:05:35.360><c> the</c><00:05:35.520><c> toolbox</c><00:05:36.080><c> can</c><00:05:36.240><c> be</c><00:05:36.400><c> an</c>

00:05:36.870 --> 00:05:36.880 align:start position:0%
and then the toolbox can be an
 

00:05:36.880 --> 00:05:38.710 align:start position:0%
and then the toolbox can be an
alternative<00:05:37.520><c> and</c><00:05:37.840><c> if</c><00:05:38.000><c> you</c><00:05:38.080><c> click</c><00:05:38.240><c> the</c><00:05:38.400><c> show</c>

00:05:38.710 --> 00:05:38.720 align:start position:0%
alternative and if you click the show
 

00:05:38.720 --> 00:05:40.629 align:start position:0%
alternative and if you click the show
toolbox<00:05:39.280><c> request</c><00:05:39.680><c> button</c><00:05:40.080><c> you'll</c><00:05:40.240><c> get</c><00:05:40.479><c> some</c>

00:05:40.629 --> 00:05:40.639 align:start position:0%
toolbox request button you'll get some
 

00:05:40.639 --> 00:05:41.909 align:start position:0%
toolbox request button you'll get some
help<00:05:41.039><c> to</c>

00:05:41.909 --> 00:05:41.919 align:start position:0%
help to
 

00:05:41.919 --> 00:05:43.510 align:start position:0%
help to
get<00:05:42.160><c> started</c><00:05:42.560><c> using</c><00:05:42.800><c> the</c><00:05:42.960><c> toolbox</c><00:05:43.440><c> to</c>

00:05:43.510 --> 00:05:43.520 align:start position:0%
get started using the toolbox to
 

00:05:43.520 --> 00:05:45.270 align:start position:0%
get started using the toolbox to
download<00:05:43.840><c> the</c><00:05:44.000><c> same</c><00:05:44.240><c> data</c><00:05:44.479><c> that's</c><00:05:44.800><c> chosen</c>

00:05:45.270 --> 00:05:45.280 align:start position:0%
download the same data that's chosen
 

00:05:45.280 --> 00:05:46.469 align:start position:0%
download the same data that's chosen
here

00:05:46.469 --> 00:05:46.479 align:start position:0%
here
 

00:05:46.479 --> 00:05:48.710 align:start position:0%
here
and<00:05:46.720><c> similar</c><00:05:47.120><c> with</c><00:05:47.280><c> the</c><00:05:47.440><c> api</c><00:05:48.000><c> request</c><00:05:48.400><c> button</c>

00:05:48.710 --> 00:05:48.720 align:start position:0%
and similar with the api request button
 

00:05:48.720 --> 00:05:50.950 align:start position:0%
and similar with the api request button
this<00:05:48.960><c> will</c><00:05:49.120><c> generate</c><00:05:49.440><c> some</c><00:05:49.680><c> code</c>

00:05:50.950 --> 00:05:50.960 align:start position:0%
this will generate some code
 

00:05:50.960 --> 00:05:54.710 align:start position:0%
this will generate some code
for<00:05:52.479><c> the</c><00:05:52.639><c> cds</c><00:05:53.120><c> api</c><00:05:53.680><c> that</c><00:05:53.840><c> can</c><00:05:54.000><c> get</c><00:05:54.160><c> you</c><00:05:54.320><c> started</c>

00:05:54.710 --> 00:05:54.720 align:start position:0%
for the cds api that can get you started
 

00:05:54.720 --> 00:05:56.150 align:start position:0%
for the cds api that can get you started
download<00:05:55.199><c> and</c><00:05:55.280><c> select</c><00:05:55.600><c> the</c><00:05:55.680><c> data</c>

00:05:56.150 --> 00:05:56.160 align:start position:0%
download and select the data
 

00:05:56.160 --> 00:05:57.350 align:start position:0%
download and select the data
automatically

00:05:57.350 --> 00:05:57.360 align:start position:0%
automatically
 

00:05:57.360 --> 00:06:00.390 align:start position:0%
automatically
instead<00:05:59.039><c> and</c><00:05:59.280><c> let's</c><00:05:59.759><c> jump</c>

00:06:00.390 --> 00:06:00.400 align:start position:0%
instead and let's jump
 

00:06:00.400 --> 00:06:03.990 align:start position:0%
instead and let's jump
further<00:06:00.800><c> into</c><00:06:01.039><c> the</c><00:06:01.199><c> cds</c><00:06:01.680><c> api</c><00:06:02.479><c> the</c><00:06:03.280><c> application</c>

00:06:03.990 --> 00:06:04.000 align:start position:0%
further into the cds api the application
 

00:06:04.000 --> 00:06:07.029 align:start position:0%
further into the cds api the application
program<00:06:04.840><c> interface</c>

00:06:07.029 --> 00:06:07.039 align:start position:0%
program interface
 

00:06:07.039 --> 00:06:09.029 align:start position:0%
program interface
as<00:06:07.280><c> mentioned</c><00:06:07.759><c> it's</c><00:06:08.000><c> good</c><00:06:08.160><c> for</c><00:06:08.479><c> downloading</c>

00:06:09.029 --> 00:06:09.039 align:start position:0%
as mentioned it's good for downloading
 

00:06:09.039 --> 00:06:11.029 align:start position:0%
as mentioned it's good for downloading
larger<00:06:09.440><c> amounts</c><00:06:09.919><c> of</c><00:06:10.080><c> data</c>

00:06:11.029 --> 00:06:11.039 align:start position:0%
larger amounts of data
 

00:06:11.039 --> 00:06:13.510 align:start position:0%
larger amounts of data
there<00:06:11.280><c> is</c><00:06:11.440><c> some</c><00:06:11.680><c> installation</c><00:06:12.400><c> needed</c><00:06:13.360><c> but</c>

00:06:13.510 --> 00:06:13.520 align:start position:0%
there is some installation needed but
 

00:06:13.520 --> 00:06:15.270 align:start position:0%
there is some installation needed but
it's<00:06:13.840><c> all</c><00:06:14.000><c> well</c><00:06:14.240><c> described</c><00:06:14.880><c> and</c>

00:06:15.270 --> 00:06:15.280 align:start position:0%
it's all well described and
 

00:06:15.280 --> 00:06:19.110 align:start position:0%
it's all well described and
not<00:06:15.520><c> too</c><00:06:16.160><c> difficult</c><00:06:17.039><c> i</c><00:06:17.360><c> guess</c>

00:06:19.110 --> 00:06:19.120 align:start position:0%
not too difficult i guess
 

00:06:19.120 --> 00:06:21.590 align:start position:0%
not too difficult i guess
and<00:06:19.360><c> you</c><00:06:19.440><c> can</c><00:06:19.840><c> then</c><00:06:20.160><c> write</c><00:06:20.560><c> python</c><00:06:21.039><c> scripts</c>

00:06:21.590 --> 00:06:21.600 align:start position:0%
and you can then write python scripts
 

00:06:21.600 --> 00:06:22.710 align:start position:0%
and you can then write python scripts
for<00:06:21.759><c> example</c><00:06:22.240><c> to</c>

00:06:22.710 --> 00:06:22.720 align:start position:0%
for example to
 

00:06:22.720 --> 00:06:25.510 align:start position:0%
for example to
retrieve<00:06:23.840><c> the</c><00:06:24.000><c> data</c><00:06:24.319><c> you</c><00:06:24.479><c> need</c><00:06:25.199><c> uh</c>

00:06:25.510 --> 00:06:25.520 align:start position:0%
retrieve the data you need uh
 

00:06:25.520 --> 00:06:27.990 align:start position:0%
retrieve the data you need uh
automatically

00:06:27.990 --> 00:06:28.000 align:start position:0%
automatically
 

00:06:28.000 --> 00:06:30.469 align:start position:0%
automatically
and<00:06:28.560><c> samuel</c><00:06:28.960><c> mentioned</c><00:06:29.440><c> this</c><00:06:29.680><c> as</c><00:06:29.840><c> well</c><00:06:30.319><c> the</c>

00:06:30.469 --> 00:06:30.479 align:start position:0%
and samuel mentioned this as well the
 

00:06:30.479 --> 00:06:31.909 align:start position:0%
and samuel mentioned this as well the
uls

00:06:31.909 --> 00:06:31.919 align:start position:0%
uls
 

00:06:31.919 --> 00:06:34.469 align:start position:0%
uls
github<00:06:32.720><c> where</c><00:06:32.960><c> we</c><00:06:33.120><c> have</c><00:06:33.280><c> some</c><00:06:33.520><c> code</c><00:06:33.840><c> examples</c>

00:06:34.469 --> 00:06:34.479 align:start position:0%
github where we have some code examples
 

00:06:34.479 --> 00:06:38.790 align:start position:0%
github where we have some code examples
for<00:06:34.639><c> downloading</c><00:06:35.120><c> new</c><00:06:35.280><c> error</c><00:06:35.520><c> data</c>

00:06:38.790 --> 00:06:38.800 align:start position:0%
 
 

00:06:38.800 --> 00:06:43.189 align:start position:0%
 
the<00:06:38.960><c> toolbox</c><00:06:39.440><c> editor</c>

00:06:43.189 --> 00:06:43.199 align:start position:0%
 
 

00:06:43.199 --> 00:06:45.110 align:start position:0%
 
yeah<00:06:43.759><c> you</c><00:06:44.000><c> remember</c><00:06:44.319><c> the</c><00:06:44.400><c> button</c><00:06:44.880><c> where</c><00:06:45.039><c> you</c>

00:06:45.110 --> 00:06:45.120 align:start position:0%
yeah you remember the button where you
 

00:06:45.120 --> 00:06:48.230 align:start position:0%
yeah you remember the button where you
can<00:06:45.680><c> generate</c><00:06:46.160><c> code</c><00:06:46.479><c> for</c><00:06:46.639><c> the</c><00:06:46.880><c> cds</c><00:06:47.280><c> toolbox</c>

00:06:48.230 --> 00:06:48.240 align:start position:0%
can generate code for the cds toolbox
 

00:06:48.240 --> 00:06:51.830 align:start position:0%
can generate code for the cds toolbox
and<00:06:48.400><c> that's</c><00:06:50.319><c> this</c><00:06:50.560><c> part</c><00:06:51.120><c> you</c><00:06:51.199><c> can</c><00:06:51.440><c> paste</c><00:06:51.680><c> it</c>

00:06:51.830 --> 00:06:51.840 align:start position:0%
and that's this part you can paste it
 

00:06:51.840 --> 00:06:53.909 align:start position:0%
and that's this part you can paste it
into<00:06:52.080><c> the</c><00:06:52.160><c> toolbox</c><00:06:52.639><c> editor</c><00:06:52.960><c> to</c><00:06:53.199><c> to</c><00:06:53.440><c> download</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
into the toolbox editor to to download
 

00:06:53.919 --> 00:06:55.589 align:start position:0%
into the toolbox editor to to download
data

00:06:55.589 --> 00:06:55.599 align:start position:0%
data
 

00:06:55.599 --> 00:06:58.070 align:start position:0%
data
that<00:06:55.759><c> you</c><00:06:55.919><c> need</c><00:06:56.240><c> so</c><00:06:56.400><c> this</c><00:06:56.639><c> is</c><00:06:56.720><c> a</c><00:06:56.880><c> tool</c><00:06:57.120><c> that</c><00:06:57.919><c> can</c>

00:06:58.070 --> 00:06:58.080 align:start position:0%
that you need so this is a tool that can
 

00:06:58.080 --> 00:07:00.710 align:start position:0%
that you need so this is a tool that can
both<00:06:58.479><c> download</c><00:06:58.960><c> process</c><00:06:59.440><c> and</c><00:06:59.599><c> plot</c>

00:07:00.710 --> 00:07:00.720 align:start position:0%
both download process and plot
 

00:07:00.720 --> 00:07:03.189 align:start position:0%
both download process and plot
the<00:07:00.880><c> data</c><00:07:01.440><c> you're</c><00:07:01.680><c> interested</c><00:07:02.160><c> in</c><00:07:02.720><c> so</c><00:07:02.880><c> this</c><00:07:03.120><c> is</c>

00:07:03.189 --> 00:07:03.199 align:start position:0%
the data you're interested in so this is
 

00:07:03.199 --> 00:07:04.629 align:start position:0%
the data you're interested in so this is
very<00:07:03.440><c> interesting</c><00:07:03.840><c> in</c><00:07:03.919><c> many</c><00:07:04.240><c> ways</c>

00:07:04.629 --> 00:07:04.639 align:start position:0%
very interesting in many ways
 

00:07:04.639 --> 00:07:07.589 align:start position:0%
very interesting in many ways
also<00:07:05.680><c> you</c><00:07:05.759><c> can</c><00:07:05.919><c> retrieve</c><00:07:06.400><c> data</c><00:07:07.199><c> and</c><00:07:07.280><c> then</c><00:07:07.440><c> you</c>

00:07:07.589 --> 00:07:07.599 align:start position:0%
also you can retrieve data and then you
 

00:07:07.599 --> 00:07:08.469 align:start position:0%
also you can retrieve data and then you
have<00:07:07.680><c> to</c><00:07:07.840><c> write</c>

00:07:08.469 --> 00:07:08.479 align:start position:0%
have to write
 

00:07:08.479 --> 00:07:11.990 align:start position:0%
have to write
some<00:07:08.720><c> code</c><00:07:10.240><c> well</c><00:07:10.479><c> described</c><00:07:11.039><c> also</c><00:07:11.440><c> in</c><00:07:11.599><c> the</c>

00:07:11.990 --> 00:07:12.000 align:start position:0%
some code well described also in the
 

00:07:12.000 --> 00:07:14.870 align:start position:0%
some code well described also in the
toolbox<00:07:13.120><c> that</c><00:07:13.280><c> you</c><00:07:13.440><c> plot</c><00:07:14.000><c> for</c><00:07:14.160><c> example</c><00:07:14.720><c> the</c>

00:07:14.870 --> 00:07:14.880 align:start position:0%
toolbox that you plot for example the
 

00:07:14.880 --> 00:07:15.990 align:start position:0%
toolbox that you plot for example the
data<00:07:15.199><c> you</c><00:07:15.360><c> downloaded</c>

00:07:15.990 --> 00:07:16.000 align:start position:0%
data you downloaded
 

00:07:16.000 --> 00:07:18.629 align:start position:0%
data you downloaded
so<00:07:17.199><c> unless</c><00:07:17.520><c> you</c><00:07:17.599><c> want</c><00:07:18.000><c> if</c><00:07:18.080><c> you</c><00:07:18.160><c> don't</c><00:07:18.319><c> want</c><00:07:18.479><c> to</c>

00:07:18.629 --> 00:07:18.639 align:start position:0%
so unless you want if you don't want to
 

00:07:18.639 --> 00:07:20.309 align:start position:0%
so unless you want if you don't want to
handle<00:07:18.960><c> a</c><00:07:19.039><c> lot</c><00:07:19.199><c> of</c><00:07:19.280><c> grid</c><00:07:19.599><c> files</c><00:07:19.919><c> this</c><00:07:20.080><c> can</c><00:07:20.240><c> be</c>

00:07:20.309 --> 00:07:20.319 align:start position:0%
handle a lot of grid files this can be
 

00:07:20.319 --> 00:07:20.550 align:start position:0%
handle a lot of grid files this can be
an

00:07:20.550 --> 00:07:20.560 align:start position:0%
an
 

00:07:20.560 --> 00:07:23.749 align:start position:0%
an
option

00:07:23.749 --> 00:07:23.759 align:start position:0%
 
 

00:07:23.759 --> 00:07:25.430 align:start position:0%
 
yeah<00:07:24.080><c> and</c><00:07:24.160><c> finally</c><00:07:24.560><c> a</c><00:07:24.639><c> few</c><00:07:24.800><c> words</c><00:07:25.120><c> about</c><00:07:25.280><c> the</c>

00:07:25.430 --> 00:07:25.440 align:start position:0%
yeah and finally a few words about the
 

00:07:25.440 --> 00:07:27.350 align:start position:0%
yeah and finally a few words about the
arc<00:07:25.680><c> degree</c><00:07:26.080><c> analysis</c><00:07:26.639><c> and</c><00:07:26.800><c> the</c><00:07:26.880><c> upcoming</c>

00:07:27.350 --> 00:07:27.360 align:start position:0%
arc degree analysis and the upcoming
 

00:07:27.360 --> 00:07:29.990 align:start position:0%
arc degree analysis and the upcoming
test<00:07:27.680><c> release</c><00:07:28.160><c> of</c><00:07:28.240><c> the</c><00:07:28.400><c> 2018</c>

00:07:29.990 --> 00:07:30.000 align:start position:0%
test release of the 2018
 

00:07:30.000 --> 00:07:33.990 align:start position:0%
test release of the 2018
c3s<00:07:30.560><c> arctic</c><00:07:31.440><c> re-analysis</c><00:07:32.240><c> data</c>

00:07:33.990 --> 00:07:34.000 align:start position:0%
c3s arctic re-analysis data
 

00:07:34.000 --> 00:07:37.670 align:start position:0%
c3s arctic re-analysis data
this<00:07:34.240><c> is</c><00:07:34.400><c> new</c><00:07:34.720><c> november</c><00:07:35.599><c> 2020</c><00:07:36.960><c> and</c>

00:07:37.670 --> 00:07:37.680 align:start position:0%
this is new november 2020 and
 

00:07:37.680 --> 00:07:40.870 align:start position:0%
this is new november 2020 and
it<00:07:37.759><c> will</c><00:07:37.919><c> be</c><00:07:38.080><c> similar</c><00:07:39.120><c> in</c><00:07:39.199><c> the</c><00:07:39.360><c> cbs</c><00:07:40.400><c> to</c><00:07:40.639><c> what</c>

00:07:40.870 --> 00:07:40.880 align:start position:0%
it will be similar in the cbs to what
 

00:07:40.880 --> 00:07:41.990 align:start position:0%
it will be similar in the cbs to what
i've<00:07:41.039><c> shown</c>

00:07:41.990 --> 00:07:42.000 align:start position:0%
i've shown
 

00:07:42.000 --> 00:07:43.990 align:start position:0%
i've shown
previously<00:07:42.479><c> here</c><00:07:43.120><c> the</c><00:07:43.280><c> difference</c><00:07:43.680><c> is</c><00:07:43.759><c> that</c>

00:07:43.990 --> 00:07:44.000 align:start position:0%
previously here the difference is that
 

00:07:44.000 --> 00:07:45.110 align:start position:0%
previously here the difference is that
the<00:07:44.319><c> the</c><00:07:44.560><c> cara</c>

00:07:45.110 --> 00:07:45.120 align:start position:0%
the the cara
 

00:07:45.120 --> 00:07:48.309 align:start position:0%
the the cara
is<00:07:46.479><c> divided</c><00:07:47.039><c> in</c><00:07:47.120><c> two</c><00:07:47.360><c> regions</c><00:07:47.759><c> the</c><00:07:47.919><c> east</c><00:07:48.160><c> and</c>

00:07:48.309 --> 00:07:48.319 align:start position:0%
is divided in two regions the east and
 

00:07:48.319 --> 00:07:48.710 align:start position:0%
is divided in two regions the east and
west

00:07:48.710 --> 00:07:48.720 align:start position:0%
west
 

00:07:48.720 --> 00:07:50.550 align:start position:0%
west
and<00:07:48.800><c> this</c><00:07:49.039><c> will</c><00:07:49.199><c> be</c><00:07:49.360><c> reflected</c><00:07:50.000><c> in</c><00:07:50.080><c> the</c><00:07:50.240><c> data</c>

00:07:50.550 --> 00:07:50.560 align:start position:0%
and this will be reflected in the data
 

00:07:50.560 --> 00:07:52.550 align:start position:0%
and this will be reflected in the data
set<00:07:50.800><c> names</c>

00:07:52.550 --> 00:07:52.560 align:start position:0%
set names
 

00:07:52.560 --> 00:07:55.830 align:start position:0%
set names
otherwise<00:07:52.960><c> it's</c><00:07:53.120><c> also</c><00:07:54.720><c> planned</c><00:07:55.039><c> to</c><00:07:55.199><c> be</c>

00:07:55.830 --> 00:07:55.840 align:start position:0%
otherwise it's also planned to be
 

00:07:55.840 --> 00:07:59.350 align:start position:0%
otherwise it's also planned to be
divided<00:07:56.319><c> into</c><00:07:57.759><c> levels</c><00:07:58.400><c> similar</c><00:07:58.879><c> to</c>

00:07:59.350 --> 00:07:59.360 align:start position:0%
divided into levels similar to
 

00:07:59.360 --> 00:08:02.629 align:start position:0%
divided into levels similar to
what<00:07:59.520><c> we</c><00:07:59.680><c> saw</c><00:07:59.919><c> before</c>

00:08:02.629 --> 00:08:02.639 align:start position:0%
 
 

00:08:02.639 --> 00:08:05.830 align:start position:0%
 
and<00:08:03.199><c> finally</c><00:08:03.680><c> the</c><00:08:04.000><c> the</c><00:08:04.319><c> variables</c>

00:08:05.830 --> 00:08:05.840 align:start position:0%
and finally the the variables
 

00:08:05.840 --> 00:08:08.869 align:start position:0%
and finally the the variables
that<00:08:06.080><c> are</c><00:08:07.919><c> planned</c><00:08:08.400><c> to</c><00:08:08.560><c> be</c>

00:08:08.869 --> 00:08:08.879 align:start position:0%
that are planned to be
 

00:08:08.879 --> 00:08:11.189 align:start position:0%
that are planned to be
included<00:08:09.599><c> in</c><00:08:09.759><c> the</c><00:08:10.160><c> in</c><00:08:10.319><c> the</c><00:08:10.560><c> first</c><00:08:10.879><c> data</c>

00:08:11.189 --> 00:08:11.199 align:start position:0%
included in the in the first data
 

00:08:11.199 --> 00:08:12.550 align:start position:0%
included in the in the first data
release

00:08:12.550 --> 00:08:12.560 align:start position:0%
release
 

00:08:12.560 --> 00:08:15.029 align:start position:0%
release
this<00:08:12.720><c> test</c><00:08:13.039><c> release</c><00:08:13.360><c> in</c><00:08:13.440><c> the</c><00:08:13.520><c> cds</c><00:08:14.639><c> there</c><00:08:14.800><c> might</c>

00:08:15.029 --> 00:08:15.039 align:start position:0%
this test release in the cds there might
 

00:08:15.039 --> 00:08:15.990 align:start position:0%
this test release in the cds there might
be<00:08:15.199><c> added</c><00:08:15.440><c> some</c><00:08:15.680><c> other</c>

00:08:15.990 --> 00:08:16.000 align:start position:0%
be added some other
 

00:08:16.000 --> 00:08:19.510 align:start position:0%
be added some other
arrivals<00:08:17.840><c> later</c><00:08:18.240><c> but</c><00:08:18.400><c> they're</c><00:08:18.639><c> not</c>

00:08:19.510 --> 00:08:19.520 align:start position:0%
arrivals later but they're not
 

00:08:19.520 --> 00:08:22.150 align:start position:0%
arrivals later but they're not
yet<00:08:19.840><c> named</c><00:08:20.080><c> by</c><00:08:20.240><c> the</c><00:08:20.400><c> wmo</c><00:08:21.199><c> so</c><00:08:21.759><c> they</c><00:08:21.919><c> will</c><00:08:22.080><c> be</c>

00:08:22.150 --> 00:08:22.160 align:start position:0%
yet named by the wmo so they will be
 

00:08:22.160 --> 00:08:23.589 align:start position:0%
yet named by the wmo so they will be
published<00:08:22.560><c> later</c>

00:08:23.589 --> 00:08:23.599 align:start position:0%
published later
 

00:08:23.599 --> 00:08:26.950 align:start position:0%
published later
here<00:08:23.759><c> you</c><00:08:23.840><c> can</c><00:08:24.000><c> see</c><00:08:24.840><c> them</c><00:08:25.520><c> uh</c><00:08:25.840><c> the</c>

00:08:26.950 --> 00:08:26.960 align:start position:0%
here you can see them uh the
 

00:08:26.960 --> 00:08:29.430 align:start position:0%
here you can see them uh the
the<00:08:27.120><c> variables</c><00:08:27.680><c> that</c><00:08:27.840><c> will</c><00:08:27.919><c> be</c><00:08:28.080><c> in</c><00:08:28.240><c> the</c><00:08:29.199><c> first</c>

00:08:29.430 --> 00:08:29.440 align:start position:0%
the variables that will be in the first
 

00:08:29.440 --> 00:08:30.070 align:start position:0%
the variables that will be in the first
release

00:08:30.070 --> 00:08:30.080 align:start position:0%
release
 

00:08:30.080 --> 00:08:33.190 align:start position:0%
release
and<00:08:31.120><c> the</c><00:08:31.280><c> full</c><00:08:31.919><c> uh</c><00:08:32.399><c> the</c><00:08:32.560><c> serra</c>

00:08:33.190 --> 00:08:33.200 align:start position:0%
and the full uh the serra
 

00:08:33.200 --> 00:08:35.909 align:start position:0%
and the full uh the serra
serra<00:08:33.440><c> data</c><00:08:33.760><c> will</c><00:08:33.919><c> also</c><00:08:34.800><c> uh</c><00:08:35.360><c> there</c><00:08:35.519><c> are</c><00:08:35.680><c> plans</c>

00:08:35.909 --> 00:08:35.919 align:start position:0%
serra data will also uh there are plans
 

00:08:35.919 --> 00:08:37.430 align:start position:0%
serra data will also uh there are plans
for<00:08:36.080><c> test</c><00:08:36.320><c> release</c><00:08:36.719><c> as</c><00:08:36.880><c> mentioned</c>

00:08:37.430 --> 00:08:37.440 align:start position:0%
for test release as mentioned
 

00:08:37.440 --> 00:08:39.909 align:start position:0%
for test release as mentioned
and<00:08:37.519><c> the</c><00:08:37.680><c> full</c><00:08:37.919><c> data</c><00:08:38.320><c> set</c><00:08:38.640><c> for</c><00:08:38.800><c> sarah</c><00:08:39.440><c> is</c>

00:08:39.909 --> 00:08:39.919 align:start position:0%
and the full data set for sarah is
 

00:08:39.919 --> 00:08:41.029 align:start position:0%
and the full data set for sarah is
planned<00:08:40.159><c> to</c><00:08:40.240><c> be</c><00:08:40.399><c> released</c>

00:08:41.029 --> 00:08:41.039 align:start position:0%
planned to be released
 

00:08:41.039 --> 00:08:45.030 align:start position:0%
planned to be released
in<00:08:41.440><c> summer</c><00:08:42.560><c> 2021</c>

00:08:45.030 --> 00:08:45.040 align:start position:0%
in summer 2021
 

00:08:45.040 --> 00:08:48.630 align:start position:0%
in summer 2021
so<00:08:45.360><c> um</c><00:08:46.080><c> that's</c><00:08:47.519><c> my</c><00:08:47.760><c> presentation</c>

00:08:48.630 --> 00:08:48.640 align:start position:0%
so um that's my presentation
 

00:08:48.640 --> 00:08:52.240 align:start position:0%
so um that's my presentation
thank<00:08:49.240><c> you</c>

