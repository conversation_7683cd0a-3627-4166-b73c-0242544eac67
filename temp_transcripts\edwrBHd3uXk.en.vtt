WEBVTT
Kind: captions
Language: en

00:00:00.140 --> 00:00:09.310 align:start position:0%
 
[Music]

00:00:09.310 --> 00:00:09.320 align:start position:0%
[Music]
 

00:00:09.320 --> 00:00:12.890 align:start position:0%
[Music]
thank<00:00:10.320><c> you</c>

00:00:12.890 --> 00:00:12.900 align:start position:0%
 
 

00:00:12.900 --> 00:00:14.030 align:start position:0%
 
I<00:00:13.200><c> mean</c><00:00:13.320><c> that</c><00:00:13.500><c> there's</c><00:00:13.679><c> always</c><00:00:13.799><c> a</c>

00:00:14.030 --> 00:00:14.040 align:start position:0%
I mean that there's always a
 

00:00:14.040 --> 00:00:17.710 align:start position:0%
I mean that there's always a
straight-off<00:00:14.400><c> between</c><00:00:14.759><c> parameter</c><00:00:15.660><c> size</c><00:00:15.900><c> and</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
straight-off between parameter size and
 

00:00:17.720 --> 00:00:21.410 align:start position:0%
straight-off between parameter size and
latency<00:00:18.920><c> and</c><00:00:19.920><c> it</c><00:00:20.039><c> feels</c><00:00:20.580><c> like</c><00:00:20.640><c> pretty</c><00:00:20.880><c> linear</c>

00:00:21.410 --> 00:00:21.420 align:start position:0%
latency and it feels like pretty linear
 

00:00:21.420 --> 00:00:24.650 align:start position:0%
latency and it feels like pretty linear
I<00:00:21.660><c> don't</c><00:00:21.840><c> know</c><00:00:21.900><c> Lucas</c><00:00:22.380><c> agrees</c><00:00:23.039><c> with</c><00:00:23.220><c> that</c><00:00:23.400><c> but</c>

00:00:24.650 --> 00:00:24.660 align:start position:0%
I don't know Lucas agrees with that but
 

00:00:24.660 --> 00:00:28.730 align:start position:0%
I don't know Lucas agrees with that but
um<00:00:24.779><c> like</c><00:00:25.260><c> so</c><00:00:25.560><c> so</c><00:00:25.800><c> just</c><00:00:26.699><c> a</c><00:00:26.880><c> bigger</c><00:00:27.240><c> the</c><00:00:27.720><c> smarter</c>

00:00:28.730 --> 00:00:28.740 align:start position:0%
um like so so just a bigger the smarter
 

00:00:28.740 --> 00:00:31.669 align:start position:0%
um like so so just a bigger the smarter
it's<00:00:29.160><c> almost</c><00:00:29.279><c> like</c><00:00:29.519><c> IQ</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
it's almost like IQ
 

00:00:31.679 --> 00:00:34.310 align:start position:0%
it's almost like IQ
um<00:00:31.740><c> that's</c><00:00:32.220><c> like</c><00:00:32.460><c> pretty</c><00:00:32.700><c> straightforward</c><00:00:33.320><c> so</c>

00:00:34.310 --> 00:00:34.320 align:start position:0%
um that's like pretty straightforward so
 

00:00:34.320 --> 00:00:37.069 align:start position:0%
um that's like pretty straightforward so
the<00:00:34.920><c> one</c><00:00:35.040><c> we</c><00:00:35.219><c> deployed</c><00:00:35.579><c> today</c><00:00:35.700><c> is</c><00:00:35.880><c> 2.7</c><00:00:36.540><c> billion</c>

00:00:37.069 --> 00:00:37.079 align:start position:0%
the one we deployed today is 2.7 billion
 

00:00:37.079 --> 00:00:38.090 align:start position:0%
the one we deployed today is 2.7 billion
parameters

00:00:38.090 --> 00:00:38.100 align:start position:0%
parameters
 

00:00:38.100 --> 00:00:40.250 align:start position:0%
parameters
and<00:00:38.579><c> surprisingly</c><00:00:39.180><c> it's</c><00:00:39.600><c> really</c><00:00:39.780><c> this</c><00:00:40.020><c> Rich</c>

00:00:40.250 --> 00:00:40.260 align:start position:0%
and surprisingly it's really this Rich
 

00:00:40.260 --> 00:00:43.430 align:start position:0%
and surprisingly it's really this Rich
spot<00:00:40.559><c> like</c><00:00:40.920><c> where</c><00:00:41.700><c> less</c><00:00:42.239><c> than</c><00:00:42.420><c> 2B</c><00:00:42.899><c> is</c><00:00:43.260><c> actually</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
spot like where less than 2B is actually
 

00:00:43.440 --> 00:00:45.049 align:start position:0%
spot like where less than 2B is actually
like<00:00:43.739><c> pretty</c><00:00:44.040><c> dumb</c>

00:00:45.049 --> 00:00:45.059 align:start position:0%
like pretty dumb
 

00:00:45.059 --> 00:00:46.810 align:start position:0%
like pretty dumb
uh

00:00:46.810 --> 00:00:46.820 align:start position:0%
uh
 

00:00:46.820 --> 00:00:49.549 align:start position:0%
uh
and<00:00:47.820><c> more</c><00:00:48.000><c> than</c><00:00:48.180><c> like</c><00:00:48.360><c> three</c><00:00:48.600><c> billion</c><00:00:49.079><c> it</c>

00:00:49.549 --> 00:00:49.559 align:start position:0%
and more than like three billion it
 

00:00:49.559 --> 00:00:52.790 align:start position:0%
and more than like three billion it
becomes<00:00:49.860><c> kind</c><00:00:50.100><c> of</c><00:00:50.280><c> slow</c><00:00:50.600><c> visibly</c><00:00:51.600><c> slow</c><00:00:51.840><c> so</c><00:00:52.680><c> we</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
becomes kind of slow visibly slow so we
 

00:00:52.800 --> 00:00:55.670 align:start position:0%
becomes kind of slow visibly slow so we
found<00:00:52.980><c> the</c><00:00:53.340><c> sort</c><00:00:53.520><c> of</c><00:00:53.579><c> the</c><00:00:53.700><c> Goldilocks</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
found the sort of the Goldilocks
 

00:00:55.680 --> 00:00:58.729 align:start position:0%
found the sort of the Goldilocks
um<00:00:55.800><c> of</c><00:00:56.399><c> language</c><00:00:56.879><c> models</c><00:00:57.480><c> 2.7</c><00:00:58.140><c> billion</c><00:00:58.559><c> per</c>

00:00:58.729 --> 00:00:58.739 align:start position:0%
um of language models 2.7 billion per
 

00:00:58.739 --> 00:01:00.290 align:start position:0%
um of language models 2.7 billion per
hours

00:01:00.290 --> 00:01:00.300 align:start position:0%
hours
 

00:01:00.300 --> 00:01:00.950 align:start position:0%
hours
um

00:01:00.950 --> 00:01:00.960 align:start position:0%
um
 

00:01:00.960 --> 00:01:02.869 align:start position:0%
um
in<00:01:01.800><c> terms</c><00:01:01.980><c> of</c><00:01:02.039><c> fine</c><00:01:02.219><c> tuning</c><00:01:02.640><c> it</c><00:01:02.760><c> doesn't</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
in terms of fine tuning it doesn't
 

00:01:02.879 --> 00:01:05.630 align:start position:0%
in terms of fine tuning it doesn't
really<00:01:03.059><c> have</c><00:01:03.359><c> any</c><00:01:03.660><c> bearing</c><00:01:04.199><c> or</c><00:01:04.320><c> latency</c>

00:01:05.630 --> 00:01:05.640 align:start position:0%
really have any bearing or latency
 

00:01:05.640 --> 00:01:08.270 align:start position:0%
really have any bearing or latency
um<00:01:06.119><c> the</c><00:01:06.780><c> the</c><00:01:06.960><c> tricky</c><00:01:07.380><c> thing</c><00:01:07.560><c> there</c><00:01:07.799><c> is</c><00:01:08.100><c> like</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
um the the tricky thing there is like
 

00:01:08.280 --> 00:01:09.350 align:start position:0%
um the the tricky thing there is like
again<00:01:08.400><c> what</c><00:01:08.700><c> we</c><00:01:08.820><c> talked</c><00:01:09.060><c> about</c><00:01:09.060><c> the</c>

00:01:09.350 --> 00:01:09.360 align:start position:0%
again what we talked about the
 

00:01:09.360 --> 00:01:11.570 align:start position:0%
again what we talked about the
benchmarking<00:01:09.960><c> is</c><00:01:10.560><c> that</c><00:01:10.799><c> you</c><00:01:10.920><c> could</c><00:01:11.100><c> do</c><00:01:11.280><c> well</c>

00:01:11.570 --> 00:01:11.580 align:start position:0%
benchmarking is that you could do well
 

00:01:11.580 --> 00:01:13.730 align:start position:0%
benchmarking is that you could do well
on<00:01:11.760><c> benchmark</c>

00:01:13.730 --> 00:01:13.740 align:start position:0%
on benchmark
 

00:01:13.740 --> 00:01:15.170 align:start position:0%
on benchmark
and<00:01:13.979><c> then</c><00:01:14.159><c> go</c><00:01:14.340><c> try</c><00:01:14.520><c> in</c><00:01:14.700><c> the</c><00:01:14.820><c> real</c><00:01:15.000><c> world</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
and then go try in the real world
 

00:01:15.180 --> 00:01:17.810 align:start position:0%
and then go try in the real world
there's<00:01:15.479><c> actually</c><00:01:15.780><c> made</c><00:01:16.380><c> the</c><00:01:16.619><c> model</c><00:01:17.159><c> performs</c>

00:01:17.810 --> 00:01:17.820 align:start position:0%
there's actually made the model performs
 

00:01:17.820 --> 00:01:19.250 align:start position:0%
there's actually made the model performs
worse

00:01:19.250 --> 00:01:19.260 align:start position:0%
worse
 

00:01:19.260 --> 00:01:21.710 align:start position:0%
worse
we<00:01:19.619><c> had</c><00:01:19.740><c> one</c><00:01:19.920><c> case</c><00:01:20.100><c> for</c><00:01:20.400><c> example</c><00:01:20.640><c> where</c>

00:01:21.710 --> 00:01:21.720 align:start position:0%
we had one case for example where
 

00:01:21.720 --> 00:01:24.410 align:start position:0%
we had one case for example where
it<00:01:22.020><c> was</c><00:01:22.200><c> doing</c><00:01:22.380><c> better</c><00:01:22.619><c> on</c><00:01:23.100><c> python</c><00:01:23.939><c> after</c><00:01:24.119><c> some</c>

00:01:24.410 --> 00:01:24.420 align:start position:0%
it was doing better on python after some
 

00:01:24.420 --> 00:01:26.090 align:start position:0%
it was doing better on python after some
fine<00:01:24.600><c> tuning</c><00:01:24.960><c> but</c><00:01:25.140><c> then</c><00:01:25.200><c> it</c><00:01:25.380><c> forgot</c><00:01:25.680><c> how</c><00:01:25.979><c> to</c>

00:01:26.090 --> 00:01:26.100 align:start position:0%
fine tuning but then it forgot how to
 

00:01:26.100 --> 00:01:29.330 align:start position:0%
fine tuning but then it forgot how to
write<00:01:26.600><c> jsx</c><00:01:27.600><c> which</c><00:01:28.439><c> is</c><00:01:28.500><c> javascript's</c><00:01:29.159><c> like</c>

00:01:29.330 --> 00:01:29.340 align:start position:0%
write jsx which is javascript's like
 

00:01:29.340 --> 00:01:31.130 align:start position:0%
write jsx which is javascript's like
react<00:01:29.700><c> syntax</c>

00:01:31.130 --> 00:01:31.140 align:start position:0%
react syntax
 

00:01:31.140 --> 00:01:34.190 align:start position:0%
react syntax
yeah<00:01:31.619><c> so</c><00:01:31.860><c> we</c><00:01:32.040><c> call</c><00:01:32.220><c> testing</c><00:01:32.640><c> by</c><00:01:32.820><c> Vibes</c><00:01:33.299><c> so</c><00:01:34.020><c> in</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
yeah so we call testing by Vibes so in
 

00:01:34.200 --> 00:01:37.190 align:start position:0%
yeah so we call testing by Vibes so in
addition<00:01:34.439><c> to</c><00:01:34.619><c> the</c><00:01:34.740><c> benchmark</c>

00:01:37.190 --> 00:01:37.200 align:start position:0%
 
 

00:01:37.200 --> 00:01:42.310 align:start position:0%
 
sure<00:01:38.040><c> I</c><00:01:38.759><c> like</c><00:01:38.939><c> best</c><00:01:39.180><c> practice</c>

00:01:42.310 --> 00:01:42.320 align:start position:0%
 
 

00:01:42.320 --> 00:01:46.609 align:start position:0%
 
after<00:01:43.320><c> we</c><00:01:43.680><c> uh</c><00:01:44.159><c> after</c><00:01:44.579><c> we</c><00:01:45.000><c> uh</c><00:01:45.360><c> that's</c><00:01:46.200><c> one</c><00:01:46.380><c> stage</c>

00:01:46.609 --> 00:01:46.619 align:start position:0%
after we uh after we uh that's one stage
 

00:01:46.619 --> 00:01:48.649 align:start position:0%
after we uh after we uh that's one stage
that's<00:01:46.920><c> not</c><00:01:47.159><c> the</c><00:01:47.340><c> whole</c><00:01:47.520><c> thing</c>

00:01:48.649 --> 00:01:48.659 align:start position:0%
that's not the whole thing
 

00:01:48.659 --> 00:01:50.569 align:start position:0%
that's not the whole thing
um<00:01:48.780><c> we</c><00:01:49.200><c> kind</c><00:01:49.439><c> of</c><00:01:49.619><c> check</c><00:01:49.799><c> the</c><00:01:49.979><c> Vibes</c><00:01:50.340><c> of</c><00:01:50.460><c> the</c>

00:01:50.569 --> 00:01:50.579 align:start position:0%
um we kind of check the Vibes of the
 

00:01:50.579 --> 00:01:53.929 align:start position:0%
um we kind of check the Vibes of the
model<00:01:51.079><c> uh</c><00:01:52.079><c> and</c><00:01:52.320><c> then</c><00:01:52.439><c> if</c><00:01:52.619><c> if</c><00:01:53.100><c> it</c><00:01:53.340><c> passes</c><00:01:53.640><c> live</c>

00:01:53.929 --> 00:01:53.939 align:start position:0%
model uh and then if if it passes live
 

00:01:53.939 --> 00:01:56.050 align:start position:0%
model uh and then if if it passes live
chat

00:01:56.050 --> 00:01:56.060 align:start position:0%
chat
 

00:01:56.060 --> 00:01:59.870 align:start position:0%
chat
it<00:01:57.060><c> goes</c><00:01:57.360><c> he</c><00:01:57.720><c> goes</c><00:01:58.020><c> he</c><00:01:58.320><c> goes</c><00:01:58.619><c> into</c><00:01:58.799><c> a</c><00:01:59.280><c> Navy</c><00:01:59.520><c> test</c>

00:01:59.870 --> 00:01:59.880 align:start position:0%
it goes he goes he goes into a Navy test
 

00:01:59.880 --> 00:02:02.929 align:start position:0%
it goes he goes he goes into a Navy test
Let's<00:02:00.479><c> do</c><00:02:00.659><c> an</c><00:02:00.780><c> A</c><00:02:00.899><c> B</c><00:02:01.020><c> test</c><00:02:01.140><c> we</c><00:02:01.619><c> check</c><00:02:01.860><c> the</c>

00:02:02.929 --> 00:02:02.939 align:start position:0%
Let's do an A B test we check the
 

00:02:02.939 --> 00:02:05.030 align:start position:0%
Let's do an A B test we check the
um<00:02:03.000><c> acceptance</c><00:02:03.780><c> rate</c><00:02:03.960><c> so</c><00:02:04.619><c> typically</c><00:02:04.860><c> our</c>

00:02:05.030 --> 00:02:05.040 align:start position:0%
um acceptance rate so typically our
 

00:02:05.040 --> 00:02:06.730 align:start position:0%
um acceptance rate so typically our
acceptance<00:02:05.399><c> rate</c><00:02:05.579><c> I</c><00:02:05.820><c> think</c><00:02:05.880><c> hovers</c><00:02:06.299><c> are</c><00:02:06.360><c> at</c><00:02:06.479><c> 25</c>

00:02:06.730 --> 00:02:06.740 align:start position:0%
acceptance rate I think hovers are at 25
 

00:02:06.740 --> 00:02:09.589 align:start position:0%
acceptance rate I think hovers are at 25
of<00:02:07.740><c> all</c><00:02:07.920><c> suggestions</c>

00:02:09.589 --> 00:02:09.599 align:start position:0%
of all suggestions
 

00:02:09.599 --> 00:02:11.930 align:start position:0%
of all suggestions
um<00:02:09.660><c> and</c><00:02:09.899><c> if</c><00:02:10.080><c> it</c><00:02:10.200><c> enters</c><00:02:10.619><c> Outboards</c><00:02:11.220><c> then</c><00:02:11.580><c> we're</c>

00:02:11.930 --> 00:02:11.940 align:start position:0%
um and if it enters Outboards then we're
 

00:02:11.940 --> 00:02:14.150 align:start position:0%
um and if it enters Outboards then we're
doing<00:02:12.420><c> something</c><00:02:12.599><c> well</c><00:02:12.959><c> if</c><00:02:13.440><c> it's</c><00:02:13.739><c> neutral</c>

00:02:14.150 --> 00:02:14.160 align:start position:0%
doing something well if it's neutral
 

00:02:14.160 --> 00:02:17.210 align:start position:0%
doing something well if it's neutral
then<00:02:14.520><c> maybe</c><00:02:14.940><c> whatever</c><00:02:15.360><c> we</c><00:02:15.660><c> did</c><00:02:15.840><c> wasn't</c><00:02:16.020><c> useful</c>

00:02:17.210 --> 00:02:17.220 align:start position:0%
then maybe whatever we did wasn't useful
 

00:02:17.220 --> 00:02:18.410 align:start position:0%
then maybe whatever we did wasn't useful
um<00:02:17.280><c> then</c><00:02:17.459><c> if</c><00:02:17.640><c> it</c><00:02:17.700><c> goes</c><00:02:17.940><c> down</c><00:02:18.060><c> that's</c>

00:02:18.410 --> 00:02:18.420 align:start position:0%
um then if it goes down that's
 

00:02:18.420 --> 00:02:21.770 align:start position:0%
um then if it goes down that's
definitely<00:02:18.780><c> uh</c><00:02:19.560><c> the</c><00:02:19.800><c> 70</c><00:02:19.980><c> bad</c>

00:02:21.770 --> 00:02:21.780 align:start position:0%
definitely uh the 70 bad
 

00:02:21.780 --> 00:02:23.930 align:start position:0%
definitely uh the 70 bad
um<00:02:21.900><c> and</c><00:02:22.440><c> so</c><00:02:22.620><c> that's</c><00:02:22.920><c> that's</c><00:02:23.220><c> sort</c><00:02:23.459><c> of</c><00:02:23.580><c> the</c><00:02:23.700><c> last</c>

00:02:23.930 --> 00:02:23.940 align:start position:0%
um and so that's that's sort of the last
 

00:02:23.940 --> 00:02:25.850 align:start position:0%
um and so that's that's sort of the last
test<00:02:24.360><c> we'd</c><00:02:24.840><c> love</c><00:02:24.959><c> to</c><00:02:25.080><c> get</c><00:02:25.200><c> more</c><00:02:25.379><c> objective</c>

00:02:25.850 --> 00:02:25.860 align:start position:0%
test we'd love to get more objective
 

00:02:25.860 --> 00:02:28.070 align:start position:0%
test we'd love to get more objective
about<00:02:26.040><c> it</c><00:02:26.220><c> but</c><00:02:26.459><c> we</c><00:02:27.000><c> haven't</c><00:02:27.180><c> found</c><00:02:27.599><c> a</c><00:02:27.840><c> way</c>

00:02:28.070 --> 00:02:28.080 align:start position:0%
about it but we haven't found a way
 

00:02:28.080 --> 00:02:30.290 align:start position:0%
about it but we haven't found a way
other<00:02:28.620><c> than</c><00:02:28.860><c> like</c><00:02:29.160><c> just</c><00:02:29.640><c> building</c><00:02:29.819><c> up</c><00:02:30.060><c> more</c>

00:02:30.290 --> 00:02:30.300 align:start position:0%
other than like just building up more
 

00:02:30.300 --> 00:02:34.729 align:start position:0%
other than like just building up more
and<00:02:30.420><c> more</c><00:02:30.599><c> Benchmark</c><00:02:31.020><c> over</c><00:02:31.379><c> time</c>

00:02:34.729 --> 00:02:34.739 align:start position:0%
 
 

00:02:34.739 --> 00:02:38.229 align:start position:0%
 
the<00:02:35.580><c> vibe</c><00:02:36.000><c> Vibe</c><00:02:36.540><c> driven</c><00:02:36.780><c> development</c>

00:02:38.229 --> 00:02:38.239 align:start position:0%
the vibe Vibe driven development
 

00:02:38.239 --> 00:02:41.030 align:start position:0%
the vibe Vibe driven development
the<00:02:39.239><c> strong</c><00:02:39.599><c> fire</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
the strong fire
 

00:02:41.040 --> 00:02:47.070 align:start position:0%
the strong fire
[Music]

