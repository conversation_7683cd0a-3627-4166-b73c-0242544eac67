WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.709 align:start position:0%
 
hey<00:00:00.240><c> and</c><00:00:00.359><c> welcome</c><00:00:00.599><c> back</c><00:00:00.719><c> to</c><00:00:00.960><c> another</c><00:00:01.280><c> video</c>

00:00:01.709 --> 00:00:01.719 align:start position:0%
hey and welcome back to another video
 

00:00:01.719 --> 00:00:03.630 align:start position:0%
hey and welcome back to another video
and<00:00:01.920><c> today</c><00:00:02.280><c> we're</c><00:00:02.480><c> going</c><00:00:02.600><c> to</c><00:00:02.760><c> be</c><00:00:02.919><c> integrating</c>

00:00:03.630 --> 00:00:03.640 align:start position:0%
and today we're going to be integrating
 

00:00:03.640 --> 00:00:06.110 align:start position:0%
and today we're going to be integrating
mgpt<00:00:04.640><c> with</c><00:00:04.839><c> autogen</c><00:00:05.520><c> I'll</c><00:00:05.640><c> do</c><00:00:05.759><c> a</c><00:00:05.920><c> quick</c>

00:00:06.110 --> 00:00:06.120 align:start position:0%
mgpt with autogen I'll do a quick
 

00:00:06.120 --> 00:00:08.110 align:start position:0%
mgpt with autogen I'll do a quick
example<00:00:06.759><c> of</c><00:00:07.040><c> what</c><00:00:07.160><c> the</c><00:00:07.319><c> context</c><00:00:07.680><c> window</c><00:00:07.919><c> is</c>

00:00:08.110 --> 00:00:08.120 align:start position:0%
example of what the context window is
 

00:00:08.120 --> 00:00:09.589 align:start position:0%
example of what the context window is
I'm<00:00:08.240><c> not</c><00:00:08.320><c> going</c><00:00:08.400><c> to</c><00:00:08.480><c> go</c><00:00:08.599><c> in</c><00:00:08.760><c> in-deep</c><00:00:09.160><c> detail</c><00:00:09.480><c> in</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
I'm not going to go in in-deep detail in
 

00:00:09.599 --> 00:00:12.030 align:start position:0%
I'm not going to go in in-deep detail in
this<00:00:09.760><c> video</c><00:00:09.960><c> of</c><00:00:10.280><c> mgpt</c><00:00:11.280><c> and</c><00:00:11.440><c> then</c><00:00:11.599><c> we'll</c><00:00:11.799><c> create</c>

00:00:12.030 --> 00:00:12.040 align:start position:0%
this video of mgpt and then we'll create
 

00:00:12.040 --> 00:00:15.629 align:start position:0%
this video of mgpt and then we'll create
a<00:00:12.120><c> new</c><00:00:12.400><c> project</c><00:00:13.240><c> install</c><00:00:13.679><c> autogen</c><00:00:14.320><c> and</c><00:00:14.639><c> mgpt</c>

00:00:15.629 --> 00:00:15.639 align:start position:0%
a new project install autogen and mgpt
 

00:00:15.639 --> 00:00:18.189 align:start position:0%
a new project install autogen and mgpt
go<00:00:15.799><c> through</c><00:00:16.080><c> the</c><00:00:16.240><c> code</c><00:00:16.760><c> and</c><00:00:17.039><c> run</c><00:00:17.320><c> it</c><00:00:17.720><c> and</c><00:00:17.960><c> see</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
go through the code and run it and see
 

00:00:18.199 --> 00:00:20.509 align:start position:0%
go through the code and run it and see
how<00:00:18.320><c> it</c><00:00:18.480><c> works</c><00:00:19.119><c> let's</c><00:00:19.359><c> get</c><00:00:19.520><c> started</c><00:00:20.279><c> all</c><00:00:20.400><c> right</c>

00:00:20.509 --> 00:00:20.519 align:start position:0%
how it works let's get started all right
 

00:00:20.519 --> 00:00:22.150 align:start position:0%
how it works let's get started all right
so<00:00:20.640><c> I'm</c><00:00:20.720><c> not</c><00:00:20.840><c> going</c><00:00:20.920><c> to</c><00:00:21.039><c> go</c><00:00:21.160><c> into</c><00:00:21.439><c> detail</c><00:00:21.880><c> of</c>

00:00:22.150 --> 00:00:22.160 align:start position:0%
so I'm not going to go into detail of
 

00:00:22.160 --> 00:00:24.830 align:start position:0%
so I'm not going to go into detail of
what<00:00:22.400><c> mgpt</c><00:00:23.279><c> is</c><00:00:23.760><c> it's</c><00:00:24.000><c> essentially</c><00:00:24.320><c> a</c><00:00:24.519><c> memory</c>

00:00:24.830 --> 00:00:24.840 align:start position:0%
what mgpt is it's essentially a memory
 

00:00:24.840 --> 00:00:26.790 align:start position:0%
what mgpt is it's essentially a memory
management<00:00:25.359><c> system</c><00:00:25.599><c> for</c><00:00:25.760><c> language</c><00:00:26.119><c> models</c>

00:00:26.790 --> 00:00:26.800 align:start position:0%
management system for language models
 

00:00:26.800 --> 00:00:29.950 align:start position:0%
management system for language models
but<00:00:27.000><c> let's</c><00:00:27.240><c> ask</c><00:00:27.800><c> why</c><00:00:28.279><c> mgpt</c><00:00:29.279><c> one</c><00:00:29.439><c> of</c><00:00:29.560><c> the</c><00:00:29.720><c> ISS</c>

00:00:29.950 --> 00:00:29.960 align:start position:0%
but let's ask why mgpt one of the ISS
 

00:00:29.960 --> 00:00:31.630 align:start position:0%
but let's ask why mgpt one of the ISS
isues<00:00:30.240><c> with</c><00:00:30.359><c> language</c><00:00:30.679><c> models</c><00:00:31.199><c> is</c><00:00:31.359><c> something</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
isues with language models is something
 

00:00:31.640 --> 00:00:33.750 align:start position:0%
isues with language models is something
called<00:00:31.800><c> the</c><00:00:32.079><c> context</c><00:00:32.559><c> window</c><00:00:33.280><c> and</c><00:00:33.520><c> I'm</c><00:00:33.640><c> going</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
called the context window and I'm going
 

00:00:33.760 --> 00:00:35.670 align:start position:0%
called the context window and I'm going
to<00:00:33.879><c> put</c><00:00:34.040><c> this</c><00:00:34.160><c> simply</c><00:00:34.840><c> but</c><00:00:35.120><c> the</c><00:00:35.280><c> context</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
to put this simply but the context
 

00:00:35.680 --> 00:00:38.389 align:start position:0%
to put this simply but the context
window<00:00:36.280><c> is</c><00:00:36.520><c> the</c><00:00:36.680><c> number</c><00:00:36.920><c> of</c><00:00:37.239><c> tokens</c><00:00:38.160><c> that</c><00:00:38.280><c> the</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
window is the number of tokens that the
 

00:00:38.399 --> 00:00:40.150 align:start position:0%
window is the number of tokens that the
language<00:00:38.760><c> model</c><00:00:39.160><c> can</c><00:00:39.440><c> remember</c><00:00:39.920><c> like</c><00:00:40.039><c> the</c>

00:00:40.150 --> 00:00:40.160 align:start position:0%
language model can remember like the
 

00:00:40.160 --> 00:00:41.270 align:start position:0%
language model can remember like the
last<00:00:40.360><c> number</c><00:00:40.559><c> of</c><00:00:40.680><c> tokens</c><00:00:40.920><c> that</c><00:00:41.039><c> I</c><00:00:41.120><c> can</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
last number of tokens that I can
 

00:00:41.280 --> 00:00:44.229 align:start position:0%
last number of tokens that I can
remember<00:00:42.000><c> so</c><00:00:42.160><c> a</c><00:00:42.320><c> token</c><00:00:43.239><c> is</c><00:00:43.399><c> more</c><00:00:43.680><c> commonly</c><00:00:44.079><c> a</c>

00:00:44.229 --> 00:00:44.239 align:start position:0%
remember so a token is more commonly a
 

00:00:44.239 --> 00:00:45.470 align:start position:0%
remember so a token is more commonly a
word<00:00:44.520><c> but</c><00:00:44.600><c> it</c><00:00:44.680><c> can</c><00:00:44.840><c> also</c><00:00:45.000><c> be</c><00:00:45.120><c> a</c><00:00:45.200><c> special</c>

00:00:45.470 --> 00:00:45.480 align:start position:0%
word but it can also be a special
 

00:00:45.480 --> 00:00:47.630 align:start position:0%
word but it can also be a special
character<00:00:45.960><c> like</c><00:00:46.280><c> semicolon</c><00:00:47.160><c> or</c><00:00:47.360><c> an</c>

00:00:47.630 --> 00:00:47.640 align:start position:0%
character like semicolon or an
 

00:00:47.640 --> 00:00:49.549 align:start position:0%
character like semicolon or an
apostrophe<00:00:48.640><c> I</c><00:00:48.719><c> think</c><00:00:48.920><c> this</c><00:00:49.039><c> might</c><00:00:49.199><c> make</c><00:00:49.360><c> more</c>

00:00:49.549 --> 00:00:49.559 align:start position:0%
apostrophe I think this might make more
 

00:00:49.559 --> 00:00:51.549 align:start position:0%
apostrophe I think this might make more
sense<00:00:49.840><c> if</c><00:00:50.000><c> I</c><00:00:50.160><c> just</c><00:00:50.280><c> go</c><00:00:50.480><c> through</c><00:00:50.600><c> an</c><00:00:50.760><c> example</c><00:00:51.399><c> so</c>

00:00:51.549 --> 00:00:51.559 align:start position:0%
sense if I just go through an example so
 

00:00:51.559 --> 00:00:54.229 align:start position:0%
sense if I just go through an example so
what<00:00:51.680><c> I'm</c><00:00:51.760><c> going</c><00:00:51.879><c> to</c><00:00:52.000><c> say</c><00:00:52.239><c> is</c><00:00:52.680><c> hello</c><00:00:53.359><c> my</c><00:00:53.879><c> name</c>

00:00:54.229 --> 00:00:54.239 align:start position:0%
what I'm going to say is hello my name
 

00:00:54.239 --> 00:00:55.110 align:start position:0%
what I'm going to say is hello my name
is

00:00:55.110 --> 00:00:55.120 align:start position:0%
is
 

00:00:55.120 --> 00:00:57.869 align:start position:0%
is
Tyler<00:00:56.120><c> it's</c><00:00:56.239><c> going</c><00:00:56.359><c> to</c><00:00:56.520><c> respond</c><00:00:56.960><c> back</c><00:00:57.640><c> and</c><00:00:57.760><c> now</c>

00:00:57.869 --> 00:00:57.879 align:start position:0%
Tyler it's going to respond back and now
 

00:00:57.879 --> 00:01:00.590 align:start position:0%
Tyler it's going to respond back and now
what<00:00:58.000><c> I'm</c><00:00:58.079><c> going</c><00:00:58.199><c> to</c><00:00:58.320><c> do</c><00:00:58.760><c> is</c><00:00:59.280><c> let</c><00:00:59.359><c> me</c><00:00:59.519><c> just</c><00:00:59.640><c> say</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
what I'm going to do is let me just say
 

00:01:00.600 --> 00:01:05.429 align:start position:0%
what I'm going to do is let me just say
all<00:01:00.760><c> I</c><00:01:00.879><c> want</c><00:01:01.120><c> to</c><00:01:02.120><c> ask</c><00:01:02.559><c> is</c><00:01:03.519><c> what</c><00:01:04.280><c> was</c><00:01:04.559><c> the</c><00:01:04.920><c> first</c>

00:01:05.429 --> 00:01:05.439 align:start position:0%
all I want to ask is what was the first
 

00:01:05.439 --> 00:01:08.749 align:start position:0%
all I want to ask is what was the first
sentence<00:01:06.400><c> I</c><00:01:06.560><c> said</c><00:01:07.040><c> in</c><00:01:07.640><c> this</c>

00:01:08.749 --> 00:01:08.759 align:start position:0%
sentence I said in this
 

00:01:08.759 --> 00:01:10.469 align:start position:0%
sentence I said in this
conversation<00:01:09.759><c> I</c><00:01:09.920><c> kind</c><00:01:10.000><c> of</c><00:01:10.080><c> butchered</c><00:01:10.400><c> the</c>

00:01:10.469 --> 00:01:10.479 align:start position:0%
conversation I kind of butchered the
 

00:01:10.479 --> 00:01:12.350 align:start position:0%
conversation I kind of butchered the
sentence<00:01:10.840><c> but</c><00:01:11.200><c> it's</c><00:01:11.320><c> going</c><00:01:11.439><c> to</c><00:01:11.680><c> respond</c><00:01:12.080><c> back</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
sentence but it's going to respond back
 

00:01:12.360 --> 00:01:13.870 align:start position:0%
sentence but it's going to respond back
the<00:01:12.479><c> first</c><00:01:12.680><c> sentence</c><00:01:13.000><c> I</c><00:01:13.080><c> said</c><00:01:13.320><c> was</c><00:01:13.479><c> hello</c><00:01:13.759><c> my</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
the first sentence I said was hello my
 

00:01:13.880 --> 00:01:15.310 align:start position:0%
the first sentence I said was hello my
name<00:01:14.000><c> is</c><00:01:14.119><c> Tyler</c><00:01:14.560><c> and</c><00:01:14.680><c> that's</c><00:01:14.880><c> obviously</c>

00:01:15.310 --> 00:01:15.320 align:start position:0%
name is Tyler and that's obviously
 

00:01:15.320 --> 00:01:17.789 align:start position:0%
name is Tyler and that's obviously
correct<00:01:16.000><c> because</c><00:01:16.240><c> we're</c><00:01:16.439><c> still</c><00:01:16.720><c> within</c><00:01:17.000><c> that</c>

00:01:17.789 --> 00:01:17.799 align:start position:0%
correct because we're still within that
 

00:01:17.799 --> 00:01:21.390 align:start position:0%
correct because we're still within that
4096<00:01:18.799><c> context</c><00:01:19.320><c> window</c><00:01:19.840><c> of</c><00:01:20.119><c> tokens</c><00:01:21.119><c> now</c><00:01:21.280><c> what</c>

00:01:21.390 --> 00:01:21.400 align:start position:0%
4096 context window of tokens now what
 

00:01:21.400 --> 00:01:23.310 align:start position:0%
4096 context window of tokens now what
I'm<00:01:21.479><c> going</c><00:01:21.600><c> to</c><00:01:21.720><c> do</c><00:01:22.320><c> is</c><00:01:22.520><c> I'm</c><00:01:22.600><c> going</c><00:01:22.720><c> to</c><00:01:22.880><c> give</c><00:01:23.000><c> it</c>

00:01:23.310 --> 00:01:23.320 align:start position:0%
I'm going to do is I'm going to give it
 

00:01:23.320 --> 00:01:26.749 align:start position:0%
I'm going to do is I'm going to give it
more<00:01:23.600><c> than</c><00:01:24.400><c> 4,096</c><00:01:25.400><c> tokens</c><00:01:26.159><c> and</c><00:01:26.320><c> I'm</c><00:01:26.439><c> going</c><00:01:26.560><c> to</c>

00:01:26.749 --> 00:01:26.759 align:start position:0%
more than 4,096 tokens and I'm going to
 

00:01:26.759 --> 00:01:28.830 align:start position:0%
more than 4,096 tokens and I'm going to
ask<00:01:26.960><c> it</c><00:01:27.439><c> the</c><00:01:27.600><c> same</c><00:01:27.880><c> question</c><00:01:28.280><c> again</c><00:01:28.600><c> and</c><00:01:28.720><c> we'll</c>

00:01:28.830 --> 00:01:28.840 align:start position:0%
ask it the same question again and we'll
 

00:01:28.840 --> 00:01:30.469 align:start position:0%
ask it the same question again and we'll
see<00:01:29.000><c> what</c><00:01:29.119><c> the</c><00:01:29.280><c> response</c><00:01:29.640><c> is</c><00:01:30.159><c> all</c><00:01:30.240><c> right</c><00:01:30.360><c> so</c>

00:01:30.469 --> 00:01:30.479 align:start position:0%
see what the response is all right so
 

00:01:30.479 --> 00:01:31.950 align:start position:0%
see what the response is all right so
essentially<00:01:30.840><c> what</c><00:01:30.920><c> I</c><00:01:31.040><c> did</c><00:01:31.280><c> is</c><00:01:31.479><c> I</c><00:01:31.600><c> just</c><00:01:31.720><c> copy</c>

00:01:31.950 --> 00:01:31.960 align:start position:0%
essentially what I did is I just copy
 

00:01:31.960 --> 00:01:34.350 align:start position:0%
essentially what I did is I just copy
and<00:01:32.159><c> pasted</c><00:01:32.840><c> as</c><00:01:33.119><c> to</c><00:01:33.360><c> make</c><00:01:33.520><c> a</c><00:01:33.600><c> short</c><00:01:33.840><c> story</c><00:01:34.200><c> I</c>

00:01:34.350 --> 00:01:34.360 align:start position:0%
and pasted as to make a short story I
 

00:01:34.360 --> 00:01:37.429 align:start position:0%
and pasted as to make a short story I
copy<00:01:34.600><c> and</c><00:01:34.720><c> pasted</c><00:01:35.040><c> it</c><00:01:35.479><c> several</c><00:01:35.880><c> times</c><00:01:36.880><c> um</c><00:01:37.240><c> and</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
copy and pasted it several times um and
 

00:01:37.439 --> 00:01:39.550 align:start position:0%
copy and pasted it several times um and
hoping<00:01:37.759><c> that</c><00:01:37.920><c> it's</c><00:01:38.079><c> more</c><00:01:38.320><c> than</c><00:01:38.640><c> 4,000</c><00:01:39.119><c> tokens</c>

00:01:39.550 --> 00:01:39.560 align:start position:0%
hoping that it's more than 4,000 tokens
 

00:01:39.560 --> 00:01:41.350 align:start position:0%
hoping that it's more than 4,000 tokens
I<00:01:39.680><c> said</c><00:01:40.040><c> do</c><00:01:40.240><c> something</c><00:01:40.479><c> with</c><00:01:40.600><c> the</c><00:01:40.759><c> story</c><00:01:41.079><c> so</c><00:01:41.240><c> it</c>

00:01:41.350 --> 00:01:41.360 align:start position:0%
I said do something with the story so it
 

00:01:41.360 --> 00:01:43.069 align:start position:0%
I said do something with the story so it
responded<00:01:41.799><c> back</c><00:01:42.320><c> okay</c><00:01:42.439><c> so</c><00:01:42.560><c> I</c><00:01:42.640><c> have</c><00:01:42.759><c> no</c><00:01:42.840><c> idea</c>

00:01:43.069 --> 00:01:43.079 align:start position:0%
responded back okay so I have no idea
 

00:01:43.079 --> 00:01:45.389 align:start position:0%
responded back okay so I have no idea
what<00:01:43.159><c> the</c><00:01:43.280><c> story</c><00:01:43.560><c> even</c><00:01:43.759><c> says</c><00:01:44.600><c> but</c><00:01:45.159><c> now</c><00:01:45.280><c> what</c>

00:01:45.389 --> 00:01:45.399 align:start position:0%
what the story even says but now what
 

00:01:45.399 --> 00:01:47.469 align:start position:0%
what the story even says but now what
I'm<00:01:45.479><c> going</c><00:01:45.600><c> to</c><00:01:45.680><c> do</c><00:01:45.880><c> is</c><00:01:46.079><c> ask</c><00:01:46.280><c> the</c><00:01:46.399><c> same</c><00:01:46.640><c> question</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
I'm going to do is ask the same question
 

00:01:47.479 --> 00:01:51.149 align:start position:0%
I'm going to do is ask the same question
all<00:01:47.719><c> I</c><00:01:47.920><c> want</c><00:01:48.520><c> all</c><00:01:48.719><c> I</c><00:01:48.880><c> want</c><00:01:49.159><c> to</c><00:01:49.520><c> ask</c><00:01:50.119><c> is</c><00:01:50.439><c> what</c><00:01:50.880><c> was</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
all I want all I want to ask is what was
 

00:01:51.159 --> 00:01:56.350 align:start position:0%
all I want all I want to ask is what was
the<00:01:51.479><c> first</c><00:01:52.439><c> sentence</c><00:01:53.439><c> that</c><00:01:53.640><c> I</c>

00:01:56.350 --> 00:01:56.360 align:start position:0%
 
 

00:01:56.360 --> 00:01:58.429 align:start position:0%
 
said<00:01:57.360><c> the</c><00:01:57.479><c> first</c><00:01:57.719><c> sentence</c><00:01:58.000><c> you</c><00:01:58.079><c> said</c><00:01:58.240><c> in</c><00:01:58.320><c> this</c>

00:01:58.429 --> 00:01:58.439 align:start position:0%
said the first sentence you said in this
 

00:01:58.439 --> 00:02:00.910 align:start position:0%
said the first sentence you said in this
conversation<00:01:58.960><c> was</c><00:01:59.200><c> all</c><00:01:59.360><c> I</c><00:01:59.439><c> want</c><00:01:59.640><c> to</c><00:01:59.960><c> ask</c><00:02:00.680><c> was</c>

00:02:00.910 --> 00:02:00.920 align:start position:0%
conversation was all I want to ask was
 

00:02:00.920 --> 00:02:02.789 align:start position:0%
conversation was all I want to ask was
the<00:02:01.039><c> first</c><00:02:01.320><c> sentence</c><00:02:01.640><c> that</c><00:02:01.759><c> I</c><00:02:01.920><c> said</c><00:02:02.600><c> right</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
the first sentence that I said right
 

00:02:02.799 --> 00:02:07.029 align:start position:0%
the first sentence that I said right
because<00:02:03.520><c> I'm</c><00:02:03.840><c> just</c><00:02:04.079><c> over</c><00:02:04.399><c> the</c><00:02:04.520><c> 496</c><00:02:05.520><c> token</c><00:02:06.039><c> mark</c>

00:02:07.029 --> 00:02:07.039 align:start position:0%
because I'm just over the 496 token mark
 

00:02:07.039 --> 00:02:09.109 align:start position:0%
because I'm just over the 496 token mark
and<00:02:07.159><c> it</c><00:02:07.320><c> doesn't</c><00:02:07.759><c> actually</c><00:02:08.200><c> know</c><00:02:08.879><c> that</c><00:02:09.000><c> the</c>

00:02:09.109 --> 00:02:09.119 align:start position:0%
and it doesn't actually know that the
 

00:02:09.119 --> 00:02:10.630 align:start position:0%
and it doesn't actually know that the
first<00:02:09.360><c> sentence</c><00:02:09.599><c> I</c><00:02:09.679><c> said</c><00:02:09.879><c> was</c><00:02:10.080><c> hello</c><00:02:10.360><c> my</c><00:02:10.479><c> name</c>

00:02:10.630 --> 00:02:10.640 align:start position:0%
first sentence I said was hello my name
 

00:02:10.640 --> 00:02:13.470 align:start position:0%
first sentence I said was hello my name
is<00:02:10.800><c> Tyler</c><00:02:11.720><c> because</c><00:02:12.200><c> the</c><00:02:12.400><c> context</c><00:02:12.879><c> window</c>

00:02:13.470 --> 00:02:13.480 align:start position:0%
is Tyler because the context window
 

00:02:13.480 --> 00:02:16.070 align:start position:0%
is Tyler because the context window
shifted<00:02:14.480><c> it's</c><00:02:14.680><c> not</c><00:02:15.040><c> starting</c><00:02:15.440><c> from</c><00:02:15.680><c> the</c><00:02:15.879><c> very</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
shifted it's not starting from the very
 

00:02:16.080 --> 00:02:18.190 align:start position:0%
shifted it's not starting from the very
beginning<00:02:16.440><c> thing</c><00:02:16.640><c> I</c><00:02:16.760><c> said</c><00:02:17.000><c> it</c><00:02:17.160><c> shifted</c><00:02:17.720><c> up</c>

00:02:18.190 --> 00:02:18.200 align:start position:0%
beginning thing I said it shifted up
 

00:02:18.200 --> 00:02:20.949 align:start position:0%
beginning thing I said it shifted up
because<00:02:18.519><c> we</c><00:02:18.680><c> reached</c><00:02:19.040><c> the</c><00:02:19.239><c> limit</c><00:02:20.080><c> of</c><00:02:20.840><c> the</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
because we reached the limit of the
 

00:02:20.959 --> 00:02:23.070 align:start position:0%
because we reached the limit of the
number<00:02:21.200><c> of</c><00:02:21.360><c> tokens</c><00:02:21.800><c> that</c><00:02:21.920><c> it</c><00:02:22.080><c> can</c><00:02:22.280><c> remember</c><00:02:22.959><c> at</c>

00:02:23.070 --> 00:02:23.080 align:start position:0%
number of tokens that it can remember at
 

00:02:23.080 --> 00:02:26.670 align:start position:0%
number of tokens that it can remember at
a<00:02:23.319><c> time</c><00:02:23.879><c> and</c><00:02:24.080><c> what</c><00:02:24.280><c> mgpt</c><00:02:25.280><c> is</c><00:02:26.080><c> helping</c><00:02:26.440><c> that</c>

00:02:26.670 --> 00:02:26.680 align:start position:0%
a time and what mgpt is helping that
 

00:02:26.680 --> 00:02:30.030 align:start position:0%
a time and what mgpt is helping that
with<00:02:26.920><c> is</c><00:02:27.080><c> basically</c><00:02:27.519><c> giving</c><00:02:27.800><c> us</c><00:02:28.760><c> an</c><00:02:29.000><c> unlimited</c>

00:02:30.030 --> 00:02:30.040 align:start position:0%
with is basically giving us an unlimited
 

00:02:30.040 --> 00:02:33.390 align:start position:0%
with is basically giving us an unlimited
or<00:02:31.040><c> like</c><00:02:31.160><c> a</c><00:02:31.319><c> virtual</c><00:02:31.840><c> unlimited</c><00:02:32.760><c> context</c>

00:02:33.390 --> 00:02:33.400 align:start position:0%
or like a virtual unlimited context
 

00:02:33.400 --> 00:02:35.430 align:start position:0%
or like a virtual unlimited context
window<00:02:34.280><c> okay</c><00:02:34.680><c> all</c><00:02:34.800><c> right</c><00:02:34.920><c> so</c><00:02:35.120><c> that</c><00:02:35.200><c> was</c><00:02:35.319><c> a</c>

00:02:35.430 --> 00:02:35.440 align:start position:0%
window okay all right so that was a
 

00:02:35.440 --> 00:02:37.309 align:start position:0%
window okay all right so that was a
quick<00:02:35.640><c> example</c><00:02:36.040><c> I</c><00:02:36.160><c> hope</c><00:02:36.319><c> that</c><00:02:36.440><c> made</c><00:02:36.640><c> sense</c><00:02:37.120><c> but</c>

00:02:37.309 --> 00:02:37.319 align:start position:0%
quick example I hope that made sense but
 

00:02:37.319 --> 00:02:38.710 align:start position:0%
quick example I hope that made sense but
let's<00:02:37.480><c> go</c><00:02:37.640><c> ahead</c><00:02:37.920><c> now</c><00:02:38.080><c> and</c><00:02:38.200><c> create</c><00:02:38.400><c> a</c><00:02:38.480><c> new</c>

00:02:38.710 --> 00:02:38.720 align:start position:0%
let's go ahead now and create a new
 

00:02:38.720 --> 00:02:42.309 align:start position:0%
let's go ahead now and create a new
project<00:02:39.400><c> install</c><00:02:40.040><c> autogen</c><00:02:41.040><c> and</c><00:02:41.319><c> mgpt</c><00:02:42.200><c> and</c>

00:02:42.309 --> 00:02:42.319 align:start position:0%
project install autogen and mgpt and
 

00:02:42.319 --> 00:02:43.869 align:start position:0%
project install autogen and mgpt and
then<00:02:42.519><c> go</c><00:02:42.680><c> through</c><00:02:42.879><c> the</c><00:02:43.000><c> code</c><00:02:43.239><c> I</c><00:02:43.400><c> have</c><00:02:43.599><c> and</c><00:02:43.720><c> then</c>

00:02:43.869 --> 00:02:43.879 align:start position:0%
then go through the code I have and then
 

00:02:43.879 --> 00:02:46.589 align:start position:0%
then go through the code I have and then
run<00:02:44.080><c> it</c><00:02:44.440><c> and</c><00:02:44.800><c> see</c><00:02:45.000><c> how</c><00:02:45.120><c> it</c><00:02:45.239><c> works</c><00:02:46.080><c> all</c><00:02:46.200><c> right</c><00:02:46.400><c> so</c>

00:02:46.589 --> 00:02:46.599 align:start position:0%
run it and see how it works all right so
 

00:02:46.599 --> 00:02:48.509 align:start position:0%
run it and see how it works all right so
let's<00:02:46.760><c> go</c><00:02:46.840><c> ahead</c><00:02:47.000><c> and</c><00:02:47.080><c> create</c><00:02:47.280><c> a</c><00:02:47.360><c> new</c><00:02:47.519><c> project</c>

00:02:48.509 --> 00:02:48.519 align:start position:0%
let's go ahead and create a new project
 

00:02:48.519 --> 00:02:50.869 align:start position:0%
let's go ahead and create a new project
let's<00:02:48.920><c> just</c><00:02:49.319><c> call</c><00:02:49.560><c> it</c>

00:02:50.869 --> 00:02:50.879 align:start position:0%
let's just call it
 

00:02:50.879 --> 00:02:52.390 align:start position:0%
let's just call it
autogen

00:02:52.390 --> 00:02:52.400 align:start position:0%
autogen
 

00:02:52.400 --> 00:02:55.030 align:start position:0%
autogen
mgpt<00:02:53.400><c> okay</c><00:02:53.560><c> now</c><00:02:53.800><c> the</c><00:02:53.959><c> project</c><00:02:54.200><c> is</c><00:02:54.400><c> created</c>

00:02:55.030 --> 00:02:55.040 align:start position:0%
mgpt okay now the project is created
 

00:02:55.040 --> 00:02:56.430 align:start position:0%
mgpt okay now the project is created
let's<00:02:55.239><c> go</c><00:02:55.440><c> and</c><00:02:55.519><c> just</c><00:02:55.680><c> install</c><00:02:56.000><c> a</c><00:02:56.080><c> few</c>

00:02:56.430 --> 00:02:56.440 align:start position:0%
let's go and just install a few
 

00:02:56.440 --> 00:02:58.190 align:start position:0%
let's go and just install a few
libraries<00:02:57.440><c> so</c><00:02:57.519><c> we</c><00:02:57.640><c> have</c><00:02:57.760><c> three</c><00:02:57.879><c> things</c><00:02:58.040><c> to</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
libraries so we have three things to
 

00:02:58.200 --> 00:03:00.470 align:start position:0%
libraries so we have three things to
install<00:02:58.560><c> here</c><00:02:59.040><c> uh</c><00:02:59.200><c> Pi</c><00:02:59.400><c> Auto</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
install here uh Pi Auto
 

00:03:00.480 --> 00:03:03.670 align:start position:0%
install here uh Pi Auto
P<00:03:00.800><c> men</c><00:03:01.000><c> GPT</c><00:03:01.840><c> and</c><00:03:01.959><c> then</c><00:03:02.360><c> python</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
P men GPT and then python
 

00:03:03.680 --> 00:03:06.589 align:start position:0%
P men GPT and then python
d.v<00:03:04.680><c> you</c><00:03:04.799><c> don't</c><00:03:05.120><c> have</c><00:03:05.239><c> to</c><00:03:05.400><c> do</c><00:03:05.640><c> this</c><00:03:06.040><c> but</c><00:03:06.519><c> I'm</c>

00:03:06.589 --> 00:03:06.599 align:start position:0%
d.v you don't have to do this but I'm
 

00:03:06.599 --> 00:03:08.990 align:start position:0%
d.v you don't have to do this but I'm
going<00:03:06.720><c> to</c><00:03:06.840><c> create</c><00:03:07.120><c> a</c><00:03:07.480><c> separate</c><00:03:08.239><c> EnV</c><00:03:08.680><c> file</c>

00:03:08.990 --> 00:03:09.000 align:start position:0%
going to create a separate EnV file
 

00:03:09.000 --> 00:03:11.190 align:start position:0%
going to create a separate EnV file
which<00:03:09.120><c> holds</c><00:03:09.599><c> our</c><00:03:09.840><c> environment</c><00:03:10.360><c> variables</c>

00:03:11.190 --> 00:03:11.200 align:start position:0%
which holds our environment variables
 

00:03:11.200 --> 00:03:12.750 align:start position:0%
which holds our environment variables
and<00:03:11.319><c> then</c><00:03:11.480><c> we</c><00:03:11.599><c> can</c><00:03:11.720><c> just</c><00:03:11.879><c> retrieve</c><00:03:12.319><c> them</c><00:03:12.480><c> into</c>

00:03:12.750 --> 00:03:12.760 align:start position:0%
and then we can just retrieve them into
 

00:03:12.760 --> 00:03:14.869 align:start position:0%
and then we can just retrieve them into
our<00:03:13.040><c> python</c><00:03:13.480><c> file</c><00:03:13.959><c> where</c><00:03:14.080><c> we'll</c><00:03:14.360><c> have</c><00:03:14.599><c> all</c><00:03:14.760><c> of</c>

00:03:14.869 --> 00:03:14.879 align:start position:0%
our python file where we'll have all of
 

00:03:14.879 --> 00:03:18.190 align:start position:0%
our python file where we'll have all of
the<00:03:15.080><c> code</c><00:03:15.840><c> uh</c><00:03:15.920><c> for</c><00:03:16.080><c> autogen</c><00:03:16.519><c> and</c><00:03:16.959><c> mgpt</c><00:03:17.959><c> and</c><00:03:18.120><c> I</c>

00:03:18.190 --> 00:03:18.200 align:start position:0%
the code uh for autogen and mgpt and I
 

00:03:18.200 --> 00:03:19.350 align:start position:0%
the code uh for autogen and mgpt and I
just<00:03:18.319><c> want</c><00:03:18.440><c> to</c><00:03:18.560><c> do</c><00:03:18.720><c> this</c><00:03:18.879><c> because</c><00:03:19.000><c> it</c><00:03:19.159><c> can</c>

00:03:19.350 --> 00:03:19.360 align:start position:0%
just want to do this because it can
 

00:03:19.360 --> 00:03:21.190 align:start position:0%
just want to do this because it can
separate<00:03:19.720><c> it</c><00:03:19.879><c> out</c><00:03:20.280><c> so</c><00:03:20.400><c> if</c><00:03:20.480><c> you</c><00:03:20.560><c> need</c><00:03:20.680><c> to</c><00:03:20.879><c> change</c>

00:03:21.190 --> 00:03:21.200 align:start position:0%
separate it out so if you need to change
 

00:03:21.200 --> 00:03:22.630 align:start position:0%
separate it out so if you need to change
anything<00:03:21.599><c> you</c><00:03:21.680><c> can</c><00:03:21.840><c> just</c><00:03:22.080><c> go</c><00:03:22.280><c> to</c><00:03:22.480><c> the</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
anything you can just go to the
 

00:03:22.640 --> 00:03:24.670 align:start position:0%
anything you can just go to the
environment<00:03:23.239><c> variable</c><00:03:23.920><c> change</c><00:03:24.200><c> it</c><00:03:24.440><c> there</c>

00:03:24.670 --> 00:03:24.680 align:start position:0%
environment variable change it there
 

00:03:24.680 --> 00:03:26.430 align:start position:0%
environment variable change it there
without<00:03:25.000><c> having</c><00:03:25.200><c> to</c><00:03:25.720><c> you</c><00:03:25.799><c> know</c><00:03:26.000><c> scroll</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
without having to you know scroll
 

00:03:26.440 --> 00:03:28.949 align:start position:0%
without having to you know scroll
through<00:03:26.680><c> our</c><00:03:27.000><c> python</c><00:03:27.440><c> code</c><00:03:27.799><c> to</c><00:03:28.200><c> see</c><00:03:28.519><c> where</c><00:03:28.760><c> see</c>

00:03:28.949 --> 00:03:28.959 align:start position:0%
through our python code to see where see
 

00:03:28.959 --> 00:03:30.550 align:start position:0%
through our python code to see where see
where<00:03:29.120><c> it's</c><00:03:29.280><c> at</c><00:03:29.760><c> so</c><00:03:29.920><c> just</c><00:03:30.040><c> go</c><00:03:30.120><c> ahead</c><00:03:30.280><c> and</c><00:03:30.360><c> run</c>

00:03:30.550 --> 00:03:30.560 align:start position:0%
where it's at so just go ahead and run
 

00:03:30.560 --> 00:03:32.910 align:start position:0%
where it's at so just go ahead and run
this<00:03:31.239><c> okay</c><00:03:31.480><c> now</c><00:03:31.680><c> it's</c><00:03:31.879><c> finished</c><00:03:32.640><c> I'm</c><00:03:32.720><c> going</c><00:03:32.840><c> to</c>

00:03:32.910 --> 00:03:32.920 align:start position:0%
this okay now it's finished I'm going to
 

00:03:32.920 --> 00:03:34.110 align:start position:0%
this okay now it's finished I'm going to
go<00:03:33.000><c> ahead</c><00:03:33.159><c> and</c><00:03:33.239><c> create</c><00:03:33.439><c> the</c><00:03:33.560><c> file</c><00:03:33.879><c> and</c><00:03:33.959><c> then</c>

00:03:34.110 --> 00:03:34.120 align:start position:0%
go ahead and create the file and then
 

00:03:34.120 --> 00:03:35.550 align:start position:0%
go ahead and create the file and then
paste<00:03:34.319><c> the</c><00:03:34.439><c> code</c><00:03:34.680><c> and</c><00:03:34.799><c> then</c><00:03:34.879><c> we'll</c><00:03:35.080><c> just</c><00:03:35.280><c> walk</c>

00:03:35.550 --> 00:03:35.560 align:start position:0%
paste the code and then we'll just walk
 

00:03:35.560 --> 00:03:37.229 align:start position:0%
paste the code and then we'll just walk
through<00:03:35.879><c> all</c><00:03:36.000><c> of</c><00:03:36.120><c> it</c><00:03:36.560><c> all</c><00:03:36.680><c> right</c><00:03:36.760><c> so</c><00:03:36.879><c> I</c><00:03:37.000><c> create</c>

00:03:37.229 --> 00:03:37.239 align:start position:0%
through all of it all right so I create
 

00:03:37.239 --> 00:03:40.910 align:start position:0%
through all of it all right so I create
two<00:03:37.400><c> files</c><00:03:37.840><c> app.py</c><00:03:39.000><c> andv</c><00:03:40.000><c> uh</c><00:03:40.120><c> the</c><00:03:40.560><c> EnV</c><00:03:40.799><c> is</c>

00:03:40.910 --> 00:03:40.920 align:start position:0%
two files app.py andv uh the EnV is
 

00:03:40.920 --> 00:03:42.710 align:start position:0%
two files app.py andv uh the EnV is
pretty<00:03:41.120><c> simple</c><00:03:41.760><c> this</c><00:03:41.920><c> just</c><00:03:42.120><c> holds</c><00:03:42.480><c> some</c><00:03:42.599><c> of</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
pretty simple this just holds some of
 

00:03:42.720 --> 00:03:43.830 align:start position:0%
pretty simple this just holds some of
the<00:03:42.840><c> configuration</c><00:03:43.319><c> that</c><00:03:43.439><c> we're</c><00:03:43.519><c> going</c><00:03:43.640><c> to</c><00:03:43.720><c> be</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
the configuration that we're going to be
 

00:03:43.840 --> 00:03:46.390 align:start position:0%
the configuration that we're going to be
using<00:03:44.599><c> uh</c><00:03:44.760><c> but</c><00:03:44.920><c> whenever</c><00:03:45.360><c> it's</c><00:03:45.560><c> here</c><00:03:46.120><c> all</c><00:03:46.280><c> we</c>

00:03:46.390 --> 00:03:46.400 align:start position:0%
using uh but whenever it's here all we
 

00:03:46.400 --> 00:03:49.550 align:start position:0%
using uh but whenever it's here all we
have<00:03:46.519><c> to</c><00:03:46.680><c> do</c><00:03:47.159><c> is</c><00:03:47.720><c> change</c><00:03:48.200><c> the</c><00:03:48.439><c> property</c><00:03:49.120><c> here</c>

00:03:49.550 --> 00:03:49.560 align:start position:0%
have to do is change the property here
 

00:03:49.560 --> 00:03:51.470 align:start position:0%
have to do is change the property here
instead<00:03:49.799><c> of</c><00:03:49.959><c> going</c><00:03:50.200><c> through</c><00:03:50.400><c> the</c><00:03:50.560><c> python</c><00:03:50.959><c> code</c>

00:03:51.470 --> 00:03:51.480 align:start position:0%
instead of going through the python code
 

00:03:51.480 --> 00:03:52.869 align:start position:0%
instead of going through the python code
looking<00:03:51.799><c> where</c><00:03:51.920><c> it's</c><00:03:52.079><c> at</c><00:03:52.360><c> especially</c><00:03:52.640><c> if</c><00:03:52.760><c> it's</c>

00:03:52.869 --> 00:03:52.879 align:start position:0%
looking where it's at especially if it's
 

00:03:52.879 --> 00:03:54.949 align:start position:0%
looking where it's at especially if it's
used<00:03:53.159><c> multiple</c><00:03:53.519><c> times</c><00:03:54.360><c> uh</c><00:03:54.480><c> that</c><00:03:54.599><c> we</c><00:03:54.680><c> can</c><00:03:54.799><c> just</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
used multiple times uh that we can just
 

00:03:54.959 --> 00:03:58.190 align:start position:0%
used multiple times uh that we can just
do<00:03:55.159><c> this</c><00:03:55.439><c> once</c><00:03:56.120><c> here</c><00:03:56.879><c> so</c><00:03:57.200><c> just</c><00:03:57.400><c> change</c><00:03:57.959><c> your</c>

00:03:58.190 --> 00:03:58.200 align:start position:0%
do this once here so just change your
 

00:03:58.200 --> 00:04:01.030 align:start position:0%
do this once here so just change your
API<00:03:58.599><c> key</c><00:03:58.799><c> to</c><00:03:59.079><c> whatever</c><00:03:59.319><c> yours</c><00:03:59.799><c> is</c><00:04:00.480><c> and</c><00:04:00.680><c> now</c><00:04:00.879><c> in</c>

00:04:01.030 --> 00:04:01.040 align:start position:0%
API key to whatever yours is and now in
 

00:04:01.040 --> 00:04:03.830 align:start position:0%
API key to whatever yours is and now in
the<00:04:01.239><c> app.py</c><00:04:02.239><c> there's</c><00:04:02.920><c> just</c><00:04:03.079><c> a</c><00:04:03.200><c> few</c><00:04:03.439><c> things</c><00:04:03.640><c> to</c>

00:04:03.830 --> 00:04:03.840 align:start position:0%
the app.py there's just a few things to
 

00:04:03.840 --> 00:04:06.550 align:start position:0%
the app.py there's just a few things to
import<00:04:04.400><c> um</c><00:04:04.680><c> it's</c><00:04:04.840><c> not</c><00:04:05.120><c> actually</c><00:04:05.480><c> that</c><00:04:05.680><c> much</c><00:04:06.159><c> so</c>

00:04:06.550 --> 00:04:06.560 align:start position:0%
import um it's not actually that much so
 

00:04:06.560 --> 00:04:09.149 align:start position:0%
import um it's not actually that much so
we<00:04:06.720><c> first</c><00:04:06.920><c> need</c><00:04:07.079><c> a</c><00:04:07.280><c> load</c><00:04:08.200><c> EnV</c><00:04:08.640><c> function</c><00:04:09.040><c> so</c>

00:04:09.149 --> 00:04:09.159 align:start position:0%
we first need a load EnV function so
 

00:04:09.159 --> 00:04:11.470 align:start position:0%
we first need a load EnV function so
it's<00:04:09.319><c> going</c><00:04:09.439><c> to</c><00:04:09.920><c> allow</c><00:04:10.239><c> us</c><00:04:10.439><c> to</c><00:04:10.840><c> get</c><00:04:11.120><c> the</c>

00:04:11.470 --> 00:04:11.480 align:start position:0%
it's going to allow us to get the
 

00:04:11.480 --> 00:04:14.429 align:start position:0%
it's going to allow us to get the
environment<00:04:12.000><c> variables</c><00:04:12.480><c> from</c><00:04:12.640><c> the</c><00:04:13.040><c> EnV</c><00:04:13.480><c> file</c>

00:04:14.429 --> 00:04:14.439 align:start position:0%
environment variables from the EnV file
 

00:04:14.439 --> 00:04:17.430 align:start position:0%
environment variables from the EnV file
we<00:04:14.640><c> have</c><00:04:15.000><c> the</c><00:04:15.280><c> config</c><00:04:15.760><c> list</c><00:04:16.639><c> so</c><00:04:17.040><c> I</c><00:04:17.160><c> get</c><00:04:17.320><c> the</c>

00:04:17.430 --> 00:04:17.440 align:start position:0%
we have the config list so I get the
 

00:04:17.440 --> 00:04:20.469 align:start position:0%
we have the config list so I get the
model<00:04:17.840><c> and</c><00:04:18.000><c> the</c><00:04:18.120><c> API</c><00:04:18.600><c> key</c><00:04:19.199><c> then</c><00:04:19.600><c> we</c><00:04:19.799><c> also</c><00:04:20.160><c> need</c>

00:04:20.469 --> 00:04:20.479 align:start position:0%
model and the API key then we also need
 

00:04:20.479 --> 00:04:25.030 align:start position:0%
model and the API key then we also need
to<00:04:20.759><c> set</c><00:04:21.040><c> the</c><00:04:21.239><c> open</c><00:04:21.800><c> ai.</c><00:04:22.800><c> API</c><00:04:23.520><c> key</c><00:04:24.000><c> and</c><00:04:24.199><c> then</c><00:04:24.800><c> the</c>

00:04:25.030 --> 00:04:25.040 align:start position:0%
to set the open ai. API key and then the
 

00:04:25.040 --> 00:04:28.510 align:start position:0%
to set the open ai. API key and then the
llm<00:04:25.600><c> config</c><00:04:26.440><c> uh</c><00:04:26.680><c> here</c><00:04:27.080><c> I</c><00:04:27.479><c> create</c><00:04:27.840><c> the</c><00:04:27.960><c> C</c><00:04:28.360><c> the</c>

00:04:28.510 --> 00:04:28.520 align:start position:0%
llm config uh here I create the C the
 

00:04:28.520 --> 00:04:30.189 align:start position:0%
llm config uh here I create the C the
request<00:04:28.840><c> timeout</c><00:04:29.240><c> and</c><00:04:29.360><c> just</c><00:04:29.639><c> set</c><00:04:29.759><c> the</c><00:04:29.880><c> config</c>

00:04:30.189 --> 00:04:30.199 align:start position:0%
request timeout and just set the config
 

00:04:30.199 --> 00:04:32.749 align:start position:0%
request timeout and just set the config
list<00:04:30.680><c> okay</c><00:04:31.320><c> we</c><00:04:31.520><c> we</c><00:04:31.639><c> did</c><00:04:31.800><c> this</c><00:04:32.000><c> before</c><00:04:32.320><c> so</c><00:04:32.639><c> this</c>

00:04:32.749 --> 00:04:32.759 align:start position:0%
list okay we we did this before so this
 

00:04:32.759 --> 00:04:35.469 align:start position:0%
list okay we we did this before so this
isn't<00:04:33.440><c> this</c><00:04:33.560><c> isn't</c><00:04:34.000><c> too</c><00:04:34.320><c> new</c><00:04:35.000><c> this</c><00:04:35.160><c> is</c><00:04:35.360><c> the</c>

00:04:35.469 --> 00:04:35.479 align:start position:0%
isn't this isn't too new this is the
 

00:04:35.479 --> 00:04:37.189 align:start position:0%
isn't this isn't too new this is the
first<00:04:35.720><c> video</c><00:04:36.160><c> you</c><00:04:36.320><c> watched</c><00:04:36.560><c> of</c>

00:04:37.189 --> 00:04:37.199 align:start position:0%
first video you watched of
 

00:04:37.199 --> 00:04:40.110 align:start position:0%
first video you watched of
autogen<00:04:38.199><c> and</c><00:04:38.360><c> then</c><00:04:38.639><c> we</c><00:04:38.840><c> create</c><00:04:39.120><c> the</c><00:04:39.280><c> agents</c><00:04:39.960><c> so</c>

00:04:40.110 --> 00:04:40.120 align:start position:0%
autogen and then we create the agents so
 

00:04:40.120 --> 00:04:42.469 align:start position:0%
autogen and then we create the agents so
we<00:04:40.240><c> have</c><00:04:40.440><c> the</c><00:04:40.639><c> the</c><00:04:40.720><c> user</c><00:04:41.039><c> agent</c><00:04:41.440><c> so</c><00:04:41.919><c> this</c><00:04:42.080><c> is</c>

00:04:42.469 --> 00:04:42.479 align:start position:0%
we have the the user agent so this is
 

00:04:42.479 --> 00:04:44.909 align:start position:0%
we have the the user agent so this is
again<00:04:43.440><c> this</c><00:04:43.560><c> isn't</c><00:04:43.919><c> much</c><00:04:44.160><c> different</c><00:04:44.600><c> than</c>

00:04:44.909 --> 00:04:44.919 align:start position:0%
again this isn't much different than
 

00:04:44.919 --> 00:04:47.550 align:start position:0%
again this isn't much different than
normal<00:04:45.240><c> autogen</c><00:04:45.840><c> so</c><00:04:46.039><c> far</c><00:04:46.759><c> so</c><00:04:47.039><c> we</c><00:04:47.160><c> create</c><00:04:47.440><c> the</c>

00:04:47.550 --> 00:04:47.560 align:start position:0%
normal autogen so far so we create the
 

00:04:47.560 --> 00:04:50.350 align:start position:0%
normal autogen so far so we create the
user<00:04:47.919><c> proxy</c><00:04:48.280><c> agent</c><00:04:48.880><c> uh</c><00:04:49.039><c> give</c><00:04:49.160><c> it</c><00:04:49.240><c> a</c><00:04:49.400><c> name</c><00:04:50.199><c> just</c>

00:04:50.350 --> 00:04:50.360 align:start position:0%
user proxy agent uh give it a name just
 

00:04:50.360 --> 00:04:52.270 align:start position:0%
user proxy agent uh give it a name just
saying<00:04:50.639><c> this</c><00:04:50.720><c> is</c><00:04:50.840><c> a</c><00:04:51.000><c> system</c><00:04:51.360><c> admin</c><00:04:51.759><c> or</c><00:04:51.960><c> human</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
saying this is a system admin or human
 

00:04:52.280 --> 00:04:54.870 align:start position:0%
saying this is a system admin or human
admin<00:04:53.160><c> uh</c><00:04:53.280><c> I'm</c><00:04:53.400><c> not</c><00:04:53.639><c> worried</c><00:04:53.960><c> about</c><00:04:54.479><c> giving</c>

00:04:54.870 --> 00:04:54.880 align:start position:0%
admin uh I'm not worried about giving
 

00:04:54.880 --> 00:04:56.830 align:start position:0%
admin uh I'm not worried about giving
feedback<00:04:55.360><c> so</c><00:04:55.520><c> I</c><00:04:55.600><c> just</c><00:04:55.720><c> put</c><00:04:55.919><c> the</c><00:04:56.080><c> human</c><00:04:56.400><c> input</c>

00:04:56.830 --> 00:04:56.840 align:start position:0%
feedback so I just put the human input
 

00:04:56.840 --> 00:05:01.670 align:start position:0%
feedback so I just put the human input
mode<00:04:57.120><c> to</c><00:04:57.479><c> never</c><00:04:58.479><c> um</c><00:04:58.639><c> the</c><00:04:59.080><c> max</c><00:04:59.680><c> auto</c><00:05:00.400><c> replies</c><00:05:01.400><c> um</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
mode to never um the max auto replies um
 

00:05:01.680 --> 00:05:04.310 align:start position:0%
mode to never um the max auto replies um
there's<00:05:01.840><c> a</c><00:05:02.000><c> termin</c><00:05:02.560><c> termination</c><00:05:03.160><c> message</c><00:05:03.960><c> but</c>

00:05:04.310 --> 00:05:04.320 align:start position:0%
there's a termin termination message but
 

00:05:04.320 --> 00:05:06.510 align:start position:0%
there's a termin termination message but
this<00:05:04.440><c> isn't</c><00:05:04.759><c> really</c><00:05:05.560><c> going</c><00:05:05.680><c> to</c><00:05:05.880><c> matter</c><00:05:06.240><c> since</c>

00:05:06.510 --> 00:05:06.520 align:start position:0%
this isn't really going to matter since
 

00:05:06.520 --> 00:05:08.990 align:start position:0%
this isn't really going to matter since
I'm<00:05:06.720><c> not</c><00:05:07.039><c> replying</c><00:05:07.680><c> anyways</c><00:05:08.560><c> and</c><00:05:08.680><c> then</c><00:05:08.880><c> just</c>

00:05:08.990 --> 00:05:09.000 align:start position:0%
I'm not replying anyways and then just
 

00:05:09.000 --> 00:05:10.950 align:start position:0%
I'm not replying anyways and then just
for<00:05:09.199><c> any</c><00:05:09.440><c> code</c><00:05:09.880><c> it's</c><00:05:10.039><c> going</c><00:05:10.120><c> to</c><00:05:10.240><c> be</c><00:05:10.440><c> put</c><00:05:10.639><c> into</c><00:05:10.840><c> a</c>

00:05:10.950 --> 00:05:10.960 align:start position:0%
for any code it's going to be put into a
 

00:05:10.960 --> 00:05:13.150 align:start position:0%
for any code it's going to be put into a
working<00:05:11.240><c> directory</c><00:05:11.680><c> called</c><00:05:12.000><c> agents</c><00:05:12.720><c> D</c>

00:05:13.150 --> 00:05:13.160 align:start position:0%
working directory called agents D
 

00:05:13.160 --> 00:05:15.670 align:start position:0%
working directory called agents D
workspace<00:05:14.160><c> uh</c><00:05:14.360><c> like</c><00:05:14.479><c> it</c><00:05:14.560><c> is</c><00:05:14.720><c> over</c><00:05:14.919><c> here</c><00:05:15.520><c> and</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
workspace uh like it is over here and
 

00:05:15.680 --> 00:05:18.150 align:start position:0%
workspace uh like it is over here and
then<00:05:15.919><c> we</c><00:05:16.160><c> have</c><00:05:16.520><c> the</c><00:05:16.759><c> product</c><00:05:17.039><c> manager</c><00:05:17.800><c> so</c>

00:05:18.150 --> 00:05:18.160 align:start position:0%
then we have the product manager so
 

00:05:18.160 --> 00:05:20.790 align:start position:0%
then we have the product manager so
again<00:05:18.360><c> this</c><00:05:18.479><c> is</c><00:05:18.639><c> just</c><00:05:18.800><c> another</c><00:05:19.600><c> autogen</c><00:05:20.440><c> agent</c>

00:05:20.790 --> 00:05:20.800 align:start position:0%
again this is just another autogen agent
 

00:05:20.800 --> 00:05:23.029 align:start position:0%
again this is just another autogen agent
so<00:05:20.960><c> we</c><00:05:21.039><c> have</c><00:05:21.199><c> the</c><00:05:21.319><c> assistant</c><00:05:21.800><c> agent</c><00:05:22.720><c> uh</c><00:05:22.840><c> it</c>

00:05:23.029 --> 00:05:23.039 align:start position:0%
so we have the assistant agent uh it
 

00:05:23.039 --> 00:05:25.110 align:start position:0%
so we have the assistant agent uh it
doesn't<00:05:23.360><c> write</c><00:05:23.639><c> any</c><00:05:23.880><c> code</c><00:05:24.240><c> and</c><00:05:24.360><c> just</c><00:05:24.520><c> lays</c><00:05:24.800><c> out</c>

00:05:25.110 --> 00:05:25.120 align:start position:0%
doesn't write any code and just lays out
 

00:05:25.120 --> 00:05:27.110 align:start position:0%
doesn't write any code and just lays out
the<00:05:25.280><c> plan</c><00:05:25.560><c> for</c><00:05:25.759><c> what</c><00:05:25.919><c> the</c><00:05:26.080><c> coder</c><00:05:26.680><c> needs</c><00:05:26.919><c> to</c>

00:05:27.110 --> 00:05:27.120 align:start position:0%
the plan for what the coder needs to
 

00:05:27.120 --> 00:05:28.950 align:start position:0%
the plan for what the coder needs to
code<00:05:27.759><c> okay</c><00:05:27.960><c> and</c><00:05:28.039><c> then</c><00:05:28.160><c> we</c><00:05:28.280><c> give</c><00:05:28.400><c> it</c><00:05:28.560><c> the</c><00:05:28.639><c> model</c>

00:05:28.950 --> 00:05:28.960 align:start position:0%
code okay and then we give it the model
 

00:05:28.960 --> 00:05:30.029 align:start position:0%
code okay and then we give it the model
configuration

00:05:30.029 --> 00:05:30.039 align:start position:0%
configuration
 

00:05:30.039 --> 00:05:32.830 align:start position:0%
configuration
so<00:05:30.199><c> all</c><00:05:30.319><c> I</c><00:05:30.479><c> have</c><00:05:30.680><c> here</c><00:05:30.960><c> this</c><00:05:31.080><c> is</c><00:05:31.280><c> kind</c><00:05:31.440><c> of</c><00:05:31.960><c> the</c>

00:05:32.830 --> 00:05:32.840 align:start position:0%
so all I have here this is kind of the
 

00:05:32.840 --> 00:05:36.230 align:start position:0%
so all I have here this is kind of the
new<00:05:33.160><c> code</c><00:05:33.919><c> um</c><00:05:34.319><c> that</c><00:05:34.680><c> we</c><00:05:34.840><c> need</c><00:05:35.039><c> to</c><00:05:35.319><c> talk</c><00:05:35.560><c> about</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
new code um that we need to talk about
 

00:05:36.240 --> 00:05:39.309 align:start position:0%
new code um that we need to talk about
so<00:05:36.720><c> if</c><00:05:37.520><c> this</c><00:05:37.639><c> is</c><00:05:37.759><c> false</c><00:05:38.199><c> so</c><00:05:38.720><c> if</c><00:05:38.840><c> we're</c><00:05:39.080><c> not</c>

00:05:39.309 --> 00:05:39.319 align:start position:0%
so if this is false so if we're not
 

00:05:39.319 --> 00:05:41.350 align:start position:0%
so if this is false so if we're not
going<00:05:39.440><c> to</c><00:05:39.639><c> use</c><00:05:40.160><c> a</c><00:05:40.360><c> mem</c>

00:05:41.350 --> 00:05:41.360 align:start position:0%
going to use a mem
 

00:05:41.360 --> 00:05:44.189 align:start position:0%
going to use a mem
GPT<00:05:42.360><c> agent</c><00:05:43.319><c> then</c><00:05:43.479><c> I</c><00:05:43.600><c> just</c><00:05:43.720><c> have</c><00:05:43.840><c> a</c><00:05:43.960><c> simple</c>

00:05:44.189 --> 00:05:44.199 align:start position:0%
GPT agent then I just have a simple
 

00:05:44.199 --> 00:05:46.830 align:start position:0%
GPT agent then I just have a simple
print<00:05:44.479><c> statement</c><00:05:45.160><c> and</c><00:05:45.560><c> then</c><00:05:45.759><c> we</c><00:05:45.880><c> just</c><00:05:46.120><c> create</c>

00:05:46.830 --> 00:05:46.840 align:start position:0%
print statement and then we just create
 

00:05:46.840 --> 00:05:50.189 align:start position:0%
print statement and then we just create
a<00:05:47.080><c> regular</c><00:05:47.919><c> autogen</c><00:05:48.919><c> uh</c><00:05:49.080><c> coder</c><00:05:49.600><c> agent</c><00:05:49.919><c> so</c><00:05:50.080><c> it's</c>

00:05:50.189 --> 00:05:50.199 align:start position:0%
a regular autogen uh coder agent so it's
 

00:05:50.199 --> 00:05:52.390 align:start position:0%
a regular autogen uh coder agent so it's
going<00:05:50.280><c> to</c><00:05:50.319><c> be</c><00:05:50.440><c> an</c><00:05:50.520><c> assistant</c><00:05:51.120><c> agent</c><00:05:52.120><c> uh</c><00:05:52.280><c> we're</c>

00:05:52.390 --> 00:05:52.400 align:start position:0%
going to be an assistant agent uh we're
 

00:05:52.400 --> 00:05:54.430 align:start position:0%
going to be an assistant agent uh we're
going<00:05:52.520><c> to</c><00:05:52.639><c> name</c><00:05:52.800><c> a</c><00:05:53.000><c> coder</c><00:05:53.560><c> they're</c><00:05:53.759><c> a</c><00:05:53.919><c> 10</c><00:05:54.160><c> times</c>

00:05:54.430 --> 00:05:54.440 align:start position:0%
going to name a coder they're a 10 times
 

00:05:54.440 --> 00:05:56.270 align:start position:0%
going to name a coder they're a 10 times
engineer<00:05:54.919><c> and</c><00:05:55.039><c> they're</c><00:05:55.160><c> going</c><00:05:55.280><c> to</c><00:05:55.479><c> make</c><00:05:55.639><c> sure</c>

00:05:56.270 --> 00:05:56.280 align:start position:0%
engineer and they're going to make sure
 

00:05:56.280 --> 00:05:58.830 align:start position:0%
engineer and they're going to make sure
everybody<00:05:56.639><c> knows</c><00:05:56.880><c> it</c><00:05:57.479><c> uh</c><00:05:57.759><c> and</c><00:05:57.919><c> then</c><00:05:58.160><c> it's</c><00:05:58.360><c> just</c>

00:05:58.830 --> 00:05:58.840 align:start position:0%
everybody knows it uh and then it's just
 

00:05:58.840 --> 00:06:00.990 align:start position:0%
everybody knows it uh and then it's just
we're<00:05:59.000><c> having</c><00:05:59.120><c> a</c><00:05:59.199><c> group</c><00:05:59.560><c> chat</c><00:05:59.960><c> with</c><00:06:00.639><c> myself</c>

00:06:00.990 --> 00:06:01.000 align:start position:0%
we're having a group chat with myself
 

00:06:01.000 --> 00:06:02.510 align:start position:0%
we're having a group chat with myself
and<00:06:01.120><c> the</c><00:06:01.280><c> product</c><00:06:01.560><c> manager</c><00:06:02.120><c> okay</c><00:06:02.240><c> and</c><00:06:02.319><c> then</c><00:06:02.400><c> we</c>

00:06:02.510 --> 00:06:02.520 align:start position:0%
and the product manager okay and then we
 

00:06:02.520 --> 00:06:04.870 align:start position:0%
and the product manager okay and then we
give<00:06:02.639><c> it</c><00:06:02.759><c> the</c><00:06:02.919><c> configuration</c><00:06:03.840><c> otherwise</c><00:06:04.600><c> if</c>

00:06:04.870 --> 00:06:04.880 align:start position:0%
give it the configuration otherwise if
 

00:06:04.880 --> 00:06:06.189 align:start position:0%
give it the configuration otherwise if
this<00:06:05.039><c> is</c><00:06:05.199><c> set</c><00:06:05.400><c> to</c>

00:06:06.189 --> 00:06:06.199 align:start position:0%
this is set to
 

00:06:06.199 --> 00:06:08.230 align:start position:0%
this is set to
true<00:06:07.199><c> then</c><00:06:07.440><c> we</c><00:06:07.560><c> have</c><00:06:07.639><c> a</c><00:06:07.759><c> simple</c><00:06:08.000><c> print</c>

00:06:08.230 --> 00:06:08.240 align:start position:0%
true then we have a simple print
 

00:06:08.240 --> 00:06:10.469 align:start position:0%
true then we have a simple print
statement<00:06:08.919><c> we</c><00:06:09.080><c> create</c><00:06:09.360><c> the</c><00:06:09.520><c> coder</c><00:06:09.960><c> but</c><00:06:10.120><c> we</c><00:06:10.240><c> use</c>

00:06:10.469 --> 00:06:10.479 align:start position:0%
statement we create the coder but we use
 

00:06:10.479 --> 00:06:13.870 align:start position:0%
statement we create the coder but we use
this<00:06:10.759><c> function</c><00:06:11.599><c> to</c><00:06:11.800><c> create</c><00:06:12.039><c> an</c><00:06:12.240><c> autogen</c><00:06:12.960><c> mgpt</c>

00:06:13.870 --> 00:06:13.880 align:start position:0%
this function to create an autogen mgpt
 

00:06:13.880 --> 00:06:17.390 align:start position:0%
this function to create an autogen mgpt
agent<00:06:14.520><c> and</c><00:06:14.680><c> we</c><00:06:14.840><c> just</c><00:06:15.000><c> give</c><00:06:15.120><c> it</c><00:06:15.240><c> a</c><00:06:15.720><c> name</c><00:06:16.720><c> um</c><00:06:17.240><c> this</c>

00:06:17.390 --> 00:06:17.400 align:start position:0%
agent and we just give it a name um this
 

00:06:17.400 --> 00:06:18.790 align:start position:0%
agent and we just give it a name um this
is<00:06:17.520><c> a</c><00:06:17.599><c> little</c><00:06:17.759><c> different</c><00:06:18.039><c> it's</c><00:06:18.160><c> not</c><00:06:18.319><c> a</c><00:06:18.479><c> system</c>

00:06:18.790 --> 00:06:18.800 align:start position:0%
is a little different it's not a system
 

00:06:18.800 --> 00:06:21.430 align:start position:0%
is a little different it's not a system
message<00:06:19.720><c> there's</c><00:06:20.120><c> a</c><00:06:20.360><c> Persona</c><00:06:20.800><c> description</c>

00:06:21.430 --> 00:06:21.440 align:start position:0%
message there's a Persona description
 

00:06:21.440 --> 00:06:23.950 align:start position:0%
message there's a Persona description
and<00:06:21.639><c> then</c><00:06:22.280><c> a</c><00:06:22.479><c> user</c><00:06:23.000><c> description</c><00:06:23.599><c> so</c><00:06:23.800><c> the</c>

00:06:23.950 --> 00:06:23.960 align:start position:0%
and then a user description so the
 

00:06:23.960 --> 00:06:26.990 align:start position:0%
and then a user description so the
Persona<00:06:24.680><c> is</c><00:06:25.440><c> you</c><00:06:25.599><c> know</c><00:06:26.160><c> they're</c><00:06:26.400><c> the</c><00:06:26.560><c> ones</c>

00:06:26.990 --> 00:06:27.000 align:start position:0%
Persona is you know they're the ones
 

00:06:27.000 --> 00:06:29.629 align:start position:0%
Persona is you know they're the ones
that<00:06:27.520><c> write</c><00:06:27.800><c> the</c><00:06:28.000><c> code</c><00:06:28.440><c> for</c><00:06:28.680><c> all</c><00:06:28.840><c> of</c><00:06:29.039><c> this</c><00:06:29.520><c> um</c>

00:06:29.629 --> 00:06:29.639 align:start position:0%
that write the code for all of this um
 

00:06:29.639 --> 00:06:30.990 align:start position:0%
that write the code for all of this um
they're<00:06:29.720><c> trained</c><00:06:29.960><c> in</c><00:06:30.199><c> Python</c><00:06:30.759><c> they're</c><00:06:30.919><c> going</c>

00:06:30.990 --> 00:06:31.000 align:start position:0%
they're trained in Python they're going
 

00:06:31.000 --> 00:06:33.150 align:start position:0%
they're trained in Python they're going
to<00:06:31.080><c> let</c><00:06:31.199><c> everybody</c><00:06:31.479><c> know</c><00:06:31.680><c> how</c><00:06:31.840><c> good</c><00:06:32.039><c> they</c><00:06:32.160><c> are</c>

00:06:33.150 --> 00:06:33.160 align:start position:0%
to let everybody know how good they are
 

00:06:33.160 --> 00:06:35.870 align:start position:0%
to let everybody know how good they are
um<00:06:33.360><c> and</c><00:06:33.560><c> then</c><00:06:33.960><c> we</c><00:06:34.160><c> give</c><00:06:34.319><c> it</c><00:06:34.479><c> the</c><00:06:34.720><c> model</c><00:06:35.720><c> and</c>

00:06:35.870 --> 00:06:35.880 align:start position:0%
um and then we give it the model and
 

00:06:35.880 --> 00:06:38.189 align:start position:0%
um and then we give it the model and
then<00:06:36.160><c> this</c><00:06:36.280><c> is</c><00:06:36.479><c> kind</c><00:06:36.599><c> of</c><00:06:36.720><c> for</c><00:06:36.960><c> the</c><00:06:37.240><c> debugging</c>

00:06:38.189 --> 00:06:38.199 align:start position:0%
then this is kind of for the debugging
 

00:06:38.199 --> 00:06:41.150 align:start position:0%
then this is kind of for the debugging
and<00:06:38.400><c> then</c><00:06:39.199><c> we</c><00:06:39.400><c> have</c><00:06:39.680><c> the</c><00:06:39.840><c> group</c><00:06:40.080><c> chat</c><00:06:40.639><c> so</c><00:06:40.960><c> we</c>

00:06:41.150 --> 00:06:41.160 align:start position:0%
and then we have the group chat so we
 

00:06:41.160 --> 00:06:44.469 align:start position:0%
and then we have the group chat so we
create<00:06:41.440><c> an</c><00:06:41.680><c> autogen</c><00:06:42.680><c> group</c><00:06:43.080><c> chat</c><00:06:43.759><c> object</c><00:06:44.319><c> we</c>

00:06:44.469 --> 00:06:44.479 align:start position:0%
create an autogen group chat object we
 

00:06:44.479 --> 00:06:46.350 align:start position:0%
create an autogen group chat object we
instantiate<00:06:45.080><c> it</c><00:06:45.319><c> give</c><00:06:45.520><c> it</c><00:06:45.639><c> the</c><00:06:45.720><c> agents</c><00:06:46.160><c> the</c>

00:06:46.350 --> 00:06:46.360 align:start position:0%
instantiate it give it the agents the
 

00:06:46.360 --> 00:06:50.790 align:start position:0%
instantiate it give it the agents the
user<00:06:46.880><c> proxy</c><00:06:47.880><c> uh</c><00:06:48.199><c> the</c><00:06:48.639><c> PM</c><00:06:49.160><c> and</c><00:06:49.360><c> the</c><00:06:49.680><c> coder</c><00:06:50.680><c> right</c>

00:06:50.790 --> 00:06:50.800 align:start position:0%
user proxy uh the PM and the coder right
 

00:06:50.800 --> 00:06:52.150 align:start position:0%
user proxy uh the PM and the coder right
and<00:06:50.919><c> then</c><00:06:51.000><c> we</c><00:06:51.120><c> have</c><00:06:51.240><c> a</c><00:06:51.360><c> manager</c><00:06:51.800><c> so</c><00:06:52.000><c> we</c>

00:06:52.150 --> 00:06:52.160 align:start position:0%
and then we have a manager so we
 

00:06:52.160 --> 00:06:55.710 align:start position:0%
and then we have a manager so we
instantiate<00:06:52.759><c> a</c><00:06:53.039><c> group</c><00:06:53.639><c> chat</c><00:06:54.039><c> manager</c><00:06:54.720><c> object</c>

00:06:55.710 --> 00:06:55.720 align:start position:0%
instantiate a group chat manager object
 

00:06:55.720 --> 00:06:57.869 align:start position:0%
instantiate a group chat manager object
uh<00:06:55.840><c> we</c><00:06:56.039><c> give</c><00:06:56.199><c> it</c><00:06:56.639><c> the</c><00:06:56.840><c> group</c><00:06:57.080><c> chat</c><00:06:57.599><c> and</c><00:06:57.720><c> then</c>

00:06:57.869 --> 00:06:57.879 align:start position:0%
uh we give it the group chat and then
 

00:06:57.879 --> 00:07:00.909 align:start position:0%
uh we give it the group chat and then
the<00:06:58.039><c> model</c><00:06:58.800><c> the</c><00:06:59.000><c> model</c><00:06:59.800><c> configuration</c><00:07:00.800><c> and</c>

00:07:00.909 --> 00:07:00.919 align:start position:0%
the model the model configuration and
 

00:07:00.919 --> 00:07:04.070 align:start position:0%
the model the model configuration and
then<00:07:01.400><c> finally</c><00:07:02.400><c> we</c><00:07:02.560><c> want</c><00:07:02.800><c> the</c><00:07:02.919><c> user</c><00:07:03.319><c> proxy</c><00:07:03.759><c> so</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
then finally we want the user proxy so
 

00:07:04.080 --> 00:07:05.909 align:start position:0%
then finally we want the user proxy so
us<00:07:04.400><c> we</c><00:07:04.479><c> want</c><00:07:04.599><c> to</c><00:07:04.800><c> initiate</c><00:07:05.240><c> the</c><00:07:05.360><c> chat</c><00:07:05.680><c> with</c><00:07:05.759><c> the</c>

00:07:05.909 --> 00:07:05.919 align:start position:0%
us we want to initiate the chat with the
 

00:07:05.919 --> 00:07:09.029 align:start position:0%
us we want to initiate the chat with the
manager<00:07:06.840><c> which</c><00:07:07.120><c> is</c><00:07:07.599><c> the</c><00:07:07.800><c> whole</c><00:07:08.120><c> group</c><00:07:08.599><c> okay</c><00:07:08.759><c> so</c>

00:07:09.029 --> 00:07:09.039 align:start position:0%
manager which is the whole group okay so
 

00:07:09.039 --> 00:07:12.390 align:start position:0%
manager which is the whole group okay so
the<00:07:09.160><c> manager</c><00:07:10.120><c> consists</c><00:07:10.800><c> of</c><00:07:11.360><c> the</c><00:07:11.520><c> group</c><00:07:11.759><c> chat</c>

00:07:12.390 --> 00:07:12.400 align:start position:0%
the manager consists of the group chat
 

00:07:12.400 --> 00:07:14.830 align:start position:0%
the manager consists of the group chat
which<00:07:12.599><c> consists</c><00:07:13.080><c> of</c><00:07:13.280><c> the</c><00:07:13.400><c> user</c><00:07:13.759><c> proxy</c><00:07:14.639><c> the</c>

00:07:14.830 --> 00:07:14.840 align:start position:0%
which consists of the user proxy the
 

00:07:14.840 --> 00:07:17.029 align:start position:0%
which consists of the user proxy the
product<00:07:15.120><c> manager</c><00:07:15.599><c> and</c><00:07:15.720><c> the</c><00:07:15.879><c> coder</c><00:07:16.759><c> so</c><00:07:16.919><c> we're</c>

00:07:17.029 --> 00:07:17.039 align:start position:0%
product manager and the coder so we're
 

00:07:17.039 --> 00:07:19.230 align:start position:0%
product manager and the coder so we're
going<00:07:17.160><c> to</c><00:07:17.440><c> initiate</c><00:07:17.919><c> the</c><00:07:18.080><c> chat</c><00:07:18.759><c> with</c><00:07:19.039><c> the</c>

00:07:19.230 --> 00:07:19.240 align:start position:0%
going to initiate the chat with the
 

00:07:19.240 --> 00:07:21.510 align:start position:0%
going to initiate the chat with the
whole<00:07:19.560><c> group</c><00:07:20.520><c> and</c><00:07:20.840><c> we're</c><00:07:21.000><c> just</c><00:07:21.120><c> going</c><00:07:21.199><c> to</c><00:07:21.319><c> say</c>

00:07:21.510 --> 00:07:21.520 align:start position:0%
whole group and we're just going to say
 

00:07:21.520 --> 00:07:22.749 align:start position:0%
whole group and we're just going to say
can<00:07:21.639><c> you</c><00:07:21.800><c> create</c><00:07:22.120><c> python</c><00:07:22.440><c> code</c><00:07:22.680><c> that</c>

00:07:22.749 --> 00:07:22.759 align:start position:0%
can you create python code that
 

00:07:22.759 --> 00:07:24.350 align:start position:0%
can you create python code that
generates<00:07:23.080><c> a</c><00:07:23.199><c> random</c><00:07:23.520><c> number</c><00:07:23.960><c> just</c><00:07:24.080><c> want</c><00:07:24.280><c> this</c>

00:07:24.350 --> 00:07:24.360 align:start position:0%
generates a random number just want this
 

00:07:24.360 --> 00:07:26.390 align:start position:0%
generates a random number just want this
to<00:07:24.479><c> be</c><00:07:24.639><c> simple</c><00:07:25.599><c> not</c><00:07:25.759><c> even</c><00:07:25.919><c> really</c><00:07:26.120><c> worried</c>

00:07:26.390 --> 00:07:26.400 align:start position:0%
to be simple not even really worried
 

00:07:26.400 --> 00:07:28.230 align:start position:0%
to be simple not even really worried
about<00:07:26.840><c> what</c><00:07:27.000><c> it</c><00:07:27.240><c> gives</c><00:07:27.479><c> us</c><00:07:27.800><c> but</c><00:07:27.960><c> I'm</c><00:07:28.080><c> just</c>

00:07:28.230 --> 00:07:28.240 align:start position:0%
about what it gives us but I'm just
 

00:07:28.240 --> 00:07:29.950 align:start position:0%
about what it gives us but I'm just
going<00:07:28.319><c> to</c><00:07:28.960><c> now</c><00:07:29.360><c> I'm</c><00:07:29.440><c> going</c><00:07:29.479><c> to</c><00:07:29.560><c> show</c><00:07:29.680><c> you</c><00:07:29.800><c> the</c>

00:07:29.950 --> 00:07:29.960 align:start position:0%
going to now I'm going to show you the
 

00:07:29.960 --> 00:07:31.950 align:start position:0%
going to now I'm going to show you the
difference<00:07:30.520><c> of</c><00:07:30.680><c> whenever</c><00:07:31.039><c> we</c><00:07:31.240><c> run</c><00:07:31.440><c> it</c><00:07:31.680><c> in</c>

00:07:31.950 --> 00:07:31.960 align:start position:0%
difference of whenever we run it in
 

00:07:31.960 --> 00:07:35.350 align:start position:0%
difference of whenever we run it in
autogen<00:07:32.960><c> and</c><00:07:33.120><c> then</c><00:07:33.400><c> run</c><00:07:33.599><c> in</c><00:07:34.120><c> mgpt</c><00:07:35.120><c> all</c><00:07:35.240><c> right</c>

00:07:35.350 --> 00:07:35.360 align:start position:0%
autogen and then run in mgpt all right
 

00:07:35.360 --> 00:07:37.909 align:start position:0%
autogen and then run in mgpt all right
so<00:07:35.599><c> first</c><00:07:35.840><c> we're</c><00:07:36.080><c> not</c><00:07:36.319><c> going</c><00:07:36.440><c> to</c><00:07:36.639><c> use</c><00:07:37.039><c> mgpt</c>

00:07:37.909 --> 00:07:37.919 align:start position:0%
so first we're not going to use mgpt
 

00:07:37.919 --> 00:07:39.070 align:start position:0%
so first we're not going to use mgpt
agent<00:07:38.280><c> we're</c><00:07:38.400><c> just</c><00:07:38.479><c> going</c><00:07:38.599><c> to</c><00:07:38.720><c> use</c><00:07:38.919><c> the</c>

00:07:39.070 --> 00:07:39.080 align:start position:0%
agent we're just going to use the
 

00:07:39.080 --> 00:07:41.230 align:start position:0%
agent we're just going to use the
regular<00:07:39.560><c> autogen</c><00:07:40.199><c> agent</c><00:07:40.759><c> so</c><00:07:40.919><c> I'm</c><00:07:41.000><c> going</c><00:07:41.120><c> to</c>

00:07:41.230 --> 00:07:41.240 align:start position:0%
regular autogen agent so I'm going to
 

00:07:41.240 --> 00:07:43.149 align:start position:0%
regular autogen agent so I'm going to
run<00:07:41.800><c> python</c>

00:07:43.149 --> 00:07:43.159 align:start position:0%
run python
 

00:07:43.159 --> 00:07:45.909 align:start position:0%
run python
app.py<00:07:44.159><c> okay</c><00:07:44.319><c> so</c><00:07:44.479><c> after</c><00:07:44.680><c> I</c><00:07:44.759><c> ran</c><00:07:45.000><c> it</c><00:07:45.319><c> um</c><00:07:45.599><c> again</c>

00:07:45.909 --> 00:07:45.919 align:start position:0%
app.py okay so after I ran it um again
 

00:07:45.919 --> 00:07:47.110 align:start position:0%
app.py okay so after I ran it um again
this<00:07:46.000><c> is</c><00:07:46.159><c> just</c><00:07:46.280><c> letting</c><00:07:46.520><c> us</c><00:07:46.599><c> know</c><00:07:46.759><c> we're</c><00:07:46.919><c> using</c>

00:07:47.110 --> 00:07:47.120 align:start position:0%
this is just letting us know we're using
 

00:07:47.120 --> 00:07:49.629 align:start position:0%
this is just letting us know we're using
the<00:07:47.240><c> autogen</c><00:07:47.800><c> coder</c><00:07:48.759><c> uh</c><00:07:48.919><c> asking</c><00:07:49.280><c> can</c><00:07:49.440><c> we</c>

00:07:49.629 --> 00:07:49.639 align:start position:0%
the autogen coder uh asking can we
 

00:07:49.639 --> 00:07:52.309 align:start position:0%
the autogen coder uh asking can we
create<00:07:49.960><c> the</c><00:07:50.159><c> python</c><00:07:50.560><c> code</c><00:07:51.479><c> the</c><00:07:51.680><c> coder</c><00:07:52.000><c> says</c>

00:07:52.309 --> 00:07:52.319 align:start position:0%
create the python code the coder says
 

00:07:52.319 --> 00:07:54.670 align:start position:0%
create the python code the coder says
yes<00:07:52.520><c> here's</c><00:07:52.720><c> a</c><00:07:52.879><c> simple</c><00:07:53.199><c> script</c><00:07:53.879><c> create</c><00:07:54.199><c> the</c>

00:07:54.670 --> 00:07:54.680 align:start position:0%
yes here's a simple script create the
 

00:07:54.680 --> 00:07:58.749 align:start position:0%
yes here's a simple script create the
script<00:07:55.680><c> uh</c><00:07:56.039><c> the</c><00:07:56.639><c> admin</c><00:07:57.199><c> so</c><00:07:57.599><c> me</c><00:07:58.400><c> cuz</c><00:07:58.520><c> I'm</c><00:07:58.639><c> not</c>

00:07:58.749 --> 00:07:58.759 align:start position:0%
script uh the admin so me cuz I'm not
 

00:07:58.759 --> 00:08:00.629 align:start position:0%
script uh the admin so me cuz I'm not
replying<00:07:59.159><c> right</c><00:07:59.319><c> it</c><00:07:59.440><c> set</c><00:07:59.599><c> the</c><00:07:59.720><c> never</c><00:08:00.120><c> so</c><00:08:00.520><c> I'm</c>

00:08:00.629 --> 00:08:00.639 align:start position:0%
replying right it set the never so I'm
 

00:08:00.639 --> 00:08:02.510 align:start position:0%
replying right it set the never so I'm
not<00:08:00.919><c> really</c><00:08:01.120><c> going</c><00:08:01.240><c> to</c><00:08:01.319><c> be</c><00:08:01.520><c> interjecting</c><00:08:02.159><c> in</c>

00:08:02.510 --> 00:08:02.520 align:start position:0%
not really going to be interjecting in
 

00:08:02.520 --> 00:08:04.869 align:start position:0%
not really going to be interjecting in
this<00:08:02.680><c> whole</c><00:08:02.879><c> conversation</c><00:08:03.879><c> uh</c><00:08:03.960><c> it</c><00:08:04.080><c> looks</c><00:08:04.319><c> like</c>

00:08:04.869 --> 00:08:04.879 align:start position:0%
this whole conversation uh it looks like
 

00:08:04.879 --> 00:08:07.629 align:start position:0%
this whole conversation uh it looks like
I<00:08:05.000><c> tried</c><00:08:05.319><c> it</c><00:08:05.800><c> the</c><00:08:05.919><c> code</c><00:08:06.120><c> output</c><00:08:06.440><c> was</c><00:08:06.599><c> six</c><00:08:07.520><c> which</c>

00:08:07.629 --> 00:08:07.639 align:start position:0%
I tried it the code output was six which
 

00:08:07.639 --> 00:08:09.830 align:start position:0%
I tried it the code output was six which
is<00:08:07.800><c> fine</c><00:08:08.000><c> it</c><00:08:08.080><c> says</c><00:08:08.280><c> it's</c><00:08:08.800><c> generating</c><00:08:09.560><c> between</c>

00:08:09.830 --> 00:08:09.840 align:start position:0%
is fine it says it's generating between
 

00:08:09.840 --> 00:08:13.830 align:start position:0%
is fine it says it's generating between
1<00:08:10.039><c> and</c><00:08:10.639><c> 100</c><00:08:11.639><c> and</c><00:08:12.039><c> good</c><00:08:12.639><c> so</c><00:08:13.240><c> now</c><00:08:13.400><c> the</c><00:08:13.560><c> product</c>

00:08:13.830 --> 00:08:13.840 align:start position:0%
1 and 100 and good so now the product
 

00:08:13.840 --> 00:08:15.749 align:start position:0%
1 and 100 and good so now the product
manager<00:08:14.159><c> says</c><00:08:14.479><c> come</c><00:08:14.639><c> up</c><00:08:14.879><c> with</c><00:08:15.479><c> without</c>

00:08:15.749 --> 00:08:15.759 align:start position:0%
manager says come up with without
 

00:08:15.759 --> 00:08:18.189 align:start position:0%
manager says come up with without
requiring<00:08:16.159><c> specific</c><00:08:16.479><c> code</c><00:08:16.759><c> right</c><00:08:16.879><c> here</c><00:08:17.080><c> a</c><00:08:17.159><c> few</c>

00:08:18.189 --> 00:08:18.199 align:start position:0%
requiring specific code right here a few
 

00:08:18.199 --> 00:08:21.550 align:start position:0%
requiring specific code right here a few
ideas<00:08:19.520><c> um</c><00:08:20.520><c> I</c><00:08:20.599><c> don't</c><00:08:20.720><c> know</c><00:08:20.919><c> what's</c><00:08:21.159><c> going</c><00:08:21.479><c> this</c>

00:08:21.550 --> 00:08:21.560 align:start position:0%
ideas um I don't know what's going this
 

00:08:21.560 --> 00:08:23.430 align:start position:0%
ideas um I don't know what's going this
is<00:08:21.680><c> kind</c><00:08:21.800><c> of</c><00:08:21.879><c> going</c><00:08:22.120><c> off</c><00:08:22.319><c> the</c><00:08:22.440><c> rails</c><00:08:23.120><c> I'm</c><00:08:23.319><c> not</c>

00:08:23.430 --> 00:08:23.440 align:start position:0%
is kind of going off the rails I'm not
 

00:08:23.440 --> 00:08:24.589 align:start position:0%
is kind of going off the rails I'm not
sure<00:08:23.599><c> where</c><00:08:23.720><c> we're</c><00:08:23.879><c> going</c><00:08:24.080><c> with</c><00:08:24.199><c> this</c><00:08:24.440><c> right</c>

00:08:24.589 --> 00:08:24.599 align:start position:0%
sure where we're going with this right
 

00:08:24.599 --> 00:08:27.550 align:start position:0%
sure where we're going with this right
now<00:08:25.199><c> but</c><00:08:25.520><c> we</c><00:08:25.639><c> got</c><00:08:25.800><c> the</c><00:08:26.000><c> idea</c><00:08:26.599><c> okay</c><00:08:26.759><c> so</c><00:08:27.120><c> that's</c>

00:08:27.550 --> 00:08:27.560 align:start position:0%
now but we got the idea okay so that's
 

00:08:27.560 --> 00:08:32.230 align:start position:0%
now but we got the idea okay so that's
regular<00:08:28.520><c> Auto</c><00:08:28.919><c> gem</c><00:08:29.720><c> now</c><00:08:30.159><c> I</c><00:08:30.319><c> set</c><00:08:30.639><c> use</c><00:08:31.120><c> mgpt</c><00:08:31.960><c> to</c>

00:08:32.230 --> 00:08:32.240 align:start position:0%
regular Auto gem now I set use mgpt to
 

00:08:32.240 --> 00:08:35.430 align:start position:0%
regular Auto gem now I set use mgpt to
true<00:08:33.039><c> and</c><00:08:33.200><c> now</c><00:08:33.279><c> I</c><00:08:33.360><c> want</c><00:08:33.479><c> to</c><00:08:33.680><c> rerun</c><00:08:34.279><c> this</c><00:08:35.080><c> so</c>

00:08:35.430 --> 00:08:35.440 align:start position:0%
true and now I want to rerun this so
 

00:08:35.440 --> 00:08:36.589 align:start position:0%
true and now I want to rerun this so
python

00:08:36.589 --> 00:08:36.599 align:start position:0%
python
 

00:08:36.599 --> 00:08:39.509 align:start position:0%
python
app.py<00:08:37.599><c> we're</c><00:08:37.760><c> going</c><00:08:37.839><c> to</c><00:08:38.000><c> be</c><00:08:38.159><c> using</c><00:08:38.800><c> the</c><00:08:39.000><c> mem</c>

00:08:39.509 --> 00:08:39.519 align:start position:0%
app.py we're going to be using the mem
 

00:08:39.519 --> 00:08:42.829 align:start position:0%
app.py we're going to be using the mem
GPT<00:08:40.120><c> coder</c><00:08:40.919><c> okay</c><00:08:41.200><c> and</c><00:08:41.440><c> as</c><00:08:41.560><c> you</c><00:08:41.680><c> can</c><00:08:41.880><c> see</c><00:08:42.560><c> it's</c>

00:08:42.829 --> 00:08:42.839 align:start position:0%
GPT coder okay and as you can see it's
 

00:08:42.839 --> 00:08:44.710 align:start position:0%
GPT coder okay and as you can see it's
already<00:08:43.200><c> going</c><00:08:43.320><c> to</c><00:08:43.479><c> be</c><00:08:43.680><c> a</c><00:08:43.839><c> little</c><00:08:44.279><c> different</c>

00:08:44.710 --> 00:08:44.720 align:start position:0%
already going to be a little different
 

00:08:44.720 --> 00:08:46.829 align:start position:0%
already going to be a little different
right<00:08:44.839><c> this</c><00:08:44.959><c> is</c><00:08:45.040><c> just</c><00:08:45.160><c> the</c><00:08:45.279><c> way</c><00:08:45.440><c> M</c><00:08:45.600><c> GPT</c><00:08:46.040><c> works</c>

00:08:46.829 --> 00:08:46.839 align:start position:0%
right this is just the way M GPT works
 

00:08:46.839 --> 00:08:47.949 align:start position:0%
right this is just the way M GPT works
as<00:08:46.959><c> you</c><00:08:47.080><c> can</c><00:08:47.200><c> see</c><00:08:47.399><c> this</c><00:08:47.480><c> is</c><00:08:47.600><c> going</c><00:08:47.680><c> to</c><00:08:47.760><c> be</c><00:08:47.839><c> a</c>

00:08:47.949 --> 00:08:47.959 align:start position:0%
as you can see this is going to be a
 

00:08:47.959 --> 00:08:49.350 align:start position:0%
as you can see this is going to be a
little<00:08:48.200><c> different</c><00:08:48.600><c> right</c><00:08:48.720><c> so</c><00:08:48.959><c> we</c><00:08:49.080><c> have</c><00:08:49.240><c> the</c>

00:08:49.350 --> 00:08:49.360 align:start position:0%
little different right so we have the
 

00:08:49.360 --> 00:08:51.590 align:start position:0%
little different right so we have the
debugger<00:08:49.760><c> mode</c><00:08:50.000><c> on</c><00:08:50.640><c> um</c><00:08:50.800><c> we</c><00:08:50.920><c> have</c><00:08:51.080><c> the</c><00:08:51.200><c> inner</c>

00:08:51.590 --> 00:08:51.600 align:start position:0%
debugger mode on um we have the inner
 

00:08:51.600 --> 00:08:54.630 align:start position:0%
debugger mode on um we have the inner
thoughts<00:08:52.120><c> the</c><00:08:52.600><c> function</c><00:08:53.600><c> uh</c><00:08:53.839><c> the</c><00:08:54.000><c> assistant</c>

00:08:54.630 --> 00:08:54.640 align:start position:0%
thoughts the function uh the assistant
 

00:08:54.640 --> 00:08:56.470 align:start position:0%
thoughts the function uh the assistant
here<00:08:55.360><c> um</c><00:08:55.480><c> we</c><00:08:55.640><c> kind</c><00:08:55.720><c> of</c><00:08:55.839><c> have</c><00:08:56.000><c> these</c><00:08:56.200><c> things</c>

00:08:56.470 --> 00:08:56.480 align:start position:0%
here um we kind of have these things
 

00:08:56.480 --> 00:09:00.630 align:start position:0%
here um we kind of have these things
like<00:08:57.240><c> giving</c><00:08:58.240><c> um</c><00:08:58.880><c> giving</c><00:08:59.519><c> more</c><00:08:59.880><c> information</c>

00:09:00.630 --> 00:09:00.640 align:start position:0%
like giving um giving more information
 

00:09:00.640 --> 00:09:02.630 align:start position:0%
like giving um giving more information
and<00:09:00.760><c> so</c><00:09:01.000><c> what's</c><00:09:01.200><c> happening</c><00:09:01.560><c> is</c><00:09:01.720><c> the</c><00:09:01.839><c> mgpt</c><00:09:02.480><c> is</c>

00:09:02.630 --> 00:09:02.640 align:start position:0%
and so what's happening is the mgpt is
 

00:09:02.640 --> 00:09:05.470 align:start position:0%
and so what's happening is the mgpt is
taking<00:09:02.959><c> all</c><00:09:03.320><c> this</c><00:09:04.279><c> and</c><00:09:04.640><c> it's</c><00:09:04.800><c> using</c><00:09:05.120><c> it</c><00:09:05.240><c> like</c>

00:09:05.470 --> 00:09:05.480 align:start position:0%
taking all this and it's using it like
 

00:09:05.480 --> 00:09:06.590 align:start position:0%
taking all this and it's using it like
it's<00:09:05.600><c> supposed</c><00:09:05.839><c> to</c><00:09:05.920><c> replicate</c><00:09:06.320><c> like</c><00:09:06.440><c> an</c>

00:09:06.590 --> 00:09:06.600 align:start position:0%
it's supposed to replicate like an
 

00:09:06.600 --> 00:09:09.069 align:start position:0%
it's supposed to replicate like an
operating<00:09:07.000><c> system</c><00:09:07.519><c> so</c><00:09:08.279><c> all</c><00:09:08.480><c> the</c><00:09:08.680><c> memory</c>

00:09:09.069 --> 00:09:09.079 align:start position:0%
operating system so all the memory
 

00:09:09.079 --> 00:09:11.550 align:start position:0%
operating system so all the memory
management<00:09:09.600><c> it's</c><00:09:09.800><c> doing</c><00:09:10.240><c> in</c><00:09:10.360><c> the</c><00:09:10.600><c> background</c>

00:09:11.550 --> 00:09:11.560 align:start position:0%
management it's doing in the background
 

00:09:11.560 --> 00:09:13.430 align:start position:0%
management it's doing in the background
is<00:09:11.760><c> so</c><00:09:12.000><c> that</c><00:09:12.200><c> we</c><00:09:12.320><c> don't</c><00:09:12.600><c> have</c><00:09:12.800><c> that</c><00:09:12.959><c> limited</c>

00:09:13.430 --> 00:09:13.440 align:start position:0%
is so that we don't have that limited
 

00:09:13.440 --> 00:09:15.230 align:start position:0%
is so that we don't have that limited
context<00:09:13.880><c> window</c><00:09:14.560><c> we</c><00:09:14.720><c> have</c><00:09:14.839><c> something</c><00:09:15.040><c> called</c>

00:09:15.230 --> 00:09:15.240 align:start position:0%
context window we have something called
 

00:09:15.240 --> 00:09:17.069 align:start position:0%
context window we have something called
the<00:09:15.360><c> virtual</c><00:09:15.800><c> context</c><00:09:16.200><c> that</c><00:09:16.279><c> it</c><00:09:16.399><c> uses</c><00:09:16.839><c> that</c>

00:09:17.069 --> 00:09:17.079 align:start position:0%
the virtual context that it uses that
 

00:09:17.079 --> 00:09:20.069 align:start position:0%
the virtual context that it uses that
has<00:09:17.839><c> um</c><00:09:18.200><c> you</c><00:09:18.320><c> know</c><00:09:18.800><c> replicating</c><00:09:19.480><c> infinite</c>

00:09:20.069 --> 00:09:20.079 align:start position:0%
has um you know replicating infinite
 

00:09:20.079 --> 00:09:22.110 align:start position:0%
has um you know replicating infinite
context<00:09:20.880><c> okay</c><00:09:21.200><c> so</c><00:09:21.360><c> it's</c><00:09:21.519><c> handling</c><00:09:21.880><c> all</c><00:09:22.000><c> that</c>

00:09:22.110 --> 00:09:22.120 align:start position:0%
context okay so it's handling all that
 

00:09:22.120 --> 00:09:23.949 align:start position:0%
context okay so it's handling all that
in<00:09:22.200><c> the</c><00:09:22.360><c> background</c><00:09:23.040><c> and</c><00:09:23.200><c> you</c><00:09:23.320><c> can</c><00:09:23.519><c> kind</c><00:09:23.640><c> of</c>

00:09:23.949 --> 00:09:23.959 align:start position:0%
in the background and you can kind of
 

00:09:23.959 --> 00:09:26.710 align:start position:0%
in the background and you can kind of
see<00:09:24.959><c> um</c><00:09:25.079><c> you</c><00:09:25.160><c> can</c><00:09:25.320><c> kind</c><00:09:25.399><c> of</c><00:09:25.519><c> see</c><00:09:25.720><c> it</c><00:09:25.880><c> doing</c><00:09:26.200><c> that</c>

00:09:26.710 --> 00:09:26.720 align:start position:0%
see um you can kind of see it doing that
 

00:09:26.720 --> 00:09:27.949 align:start position:0%
see um you can kind of see it doing that
I<00:09:26.839><c> thought</c><00:09:27.000><c> that's</c><00:09:27.200><c> funny</c><00:09:27.440><c> it's</c><00:09:27.600><c> inner</c>

00:09:27.949 --> 00:09:27.959 align:start position:0%
I thought that's funny it's inner
 

00:09:27.959 --> 00:09:30.110 align:start position:0%
I thought that's funny it's inner
thoughts<00:09:28.399><c> one</c><00:09:28.519><c> of</c><00:09:28.640><c> its</c><00:09:28.760><c> inner</c><00:09:29.240><c> thoughts</c><00:09:29.760><c> was</c>

00:09:30.110 --> 00:09:30.120 align:start position:0%
thoughts one of its inner thoughts was
 

00:09:30.120 --> 00:09:31.910 align:start position:0%
thoughts one of its inner thoughts was
he<00:09:30.440><c> classic</c><00:09:30.800><c> product</c><00:09:31.079><c> manager</c><00:09:31.480><c> explaining</c>

00:09:31.910 --> 00:09:31.920 align:start position:0%
he classic product manager explaining
 

00:09:31.920 --> 00:09:34.389 align:start position:0%
he classic product manager explaining
everything<00:09:32.240><c> in</c><00:09:32.399><c> unnecessary</c><00:09:33.200><c> detail</c><00:09:34.000><c> as</c><00:09:34.079><c> if</c><00:09:34.279><c> I</c>

00:09:34.389 --> 00:09:34.399 align:start position:0%
everything in unnecessary detail as if I
 

00:09:34.399 --> 00:09:35.630 align:start position:0%
everything in unnecessary detail as if I
don't<00:09:34.560><c> know</c><00:09:34.720><c> how</c><00:09:34.800><c> to</c><00:09:34.920><c> generate</c><00:09:35.240><c> a</c><00:09:35.360><c> random</c>

00:09:35.630 --> 00:09:35.640 align:start position:0%
don't know how to generate a random
 

00:09:35.640 --> 00:09:38.230 align:start position:0%
don't know how to generate a random
number<00:09:35.839><c> in</c><00:09:36.079><c> Python</c><00:09:37.079><c> well</c><00:09:37.240><c> you</c><00:09:37.360><c> know</c><00:09:37.519><c> what</c><00:09:38.000><c> that</c>

00:09:38.230 --> 00:09:38.240 align:start position:0%
number in Python well you know what that
 

00:09:38.240 --> 00:09:41.470 align:start position:0%
number in Python well you know what that
is<00:09:38.800><c> the</c><00:09:38.959><c> true</c><00:09:39.240><c> sign</c><00:09:39.560><c> of</c><00:09:39.680><c> a</c><00:09:39.880><c> 10x</c><00:09:40.519><c> engineer</c>

00:09:41.470 --> 00:09:41.480 align:start position:0%
is the true sign of a 10x engineer
 

00:09:41.480 --> 00:09:42.910 align:start position:0%
is the true sign of a 10x engineer
thinking<00:09:41.800><c> they</c><00:09:41.920><c> know</c><00:09:42.200><c> everything</c><00:09:42.519><c> no</c><00:09:42.680><c> need</c><00:09:42.839><c> to</c>

00:09:42.910 --> 00:09:42.920 align:start position:0%
thinking they know everything no need to
 

00:09:42.920 --> 00:09:44.710 align:start position:0%
thinking they know everything no need to
over<00:09:43.200><c> complicate</c>

00:09:44.710 --> 00:09:44.720 align:start position:0%
over complicate
 

00:09:44.720 --> 00:09:47.630 align:start position:0%
over complicate
things<00:09:45.720><c> okay</c><00:09:46.079><c> awesome</c><00:09:46.720><c> okay</c><00:09:46.920><c> great</c><00:09:47.320><c> and</c><00:09:47.519><c> it</c>

00:09:47.630 --> 00:09:47.640 align:start position:0%
things okay awesome okay great and it
 

00:09:47.640 --> 00:09:49.190 align:start position:0%
things okay awesome okay great and it
worked<00:09:48.040><c> and</c><00:09:48.160><c> it</c><00:09:48.279><c> finished</c><00:09:48.720><c> we</c><00:09:48.839><c> able</c><00:09:49.040><c> to</c>

00:09:49.190 --> 00:09:49.200 align:start position:0%
worked and it finished we able to
 

00:09:49.200 --> 00:09:51.990 align:start position:0%
worked and it finished we able to
integrate<00:09:49.640><c> mgpt</c><00:09:50.560><c> and</c><00:09:50.800><c> autogen</c><00:09:51.560><c> together</c><00:09:51.920><c> if</c>

00:09:51.990 --> 00:09:52.000 align:start position:0%
integrate mgpt and autogen together if
 

00:09:52.000 --> 00:09:53.350 align:start position:0%
integrate mgpt and autogen together if
you<00:09:52.079><c> have</c><00:09:52.200><c> any</c><00:09:52.399><c> questions</c><00:09:52.720><c> or</c><00:09:52.920><c> comments</c>

00:09:53.350 --> 00:09:53.360 align:start position:0%
you have any questions or comments
 

00:09:53.360 --> 00:09:55.110 align:start position:0%
you have any questions or comments
please<00:09:53.560><c> leave</c><00:09:53.760><c> them</c><00:09:53.959><c> down</c><00:09:54.160><c> below</c><00:09:54.680><c> and</c><00:09:54.800><c> I'll</c><00:09:54.959><c> be</c>

00:09:55.110 --> 00:09:55.120 align:start position:0%
please leave them down below and I'll be
 

00:09:55.120 --> 00:09:56.829 align:start position:0%
please leave them down below and I'll be
more<00:09:55.279><c> than</c><00:09:55.440><c> happy</c><00:09:55.680><c> to</c><00:09:55.839><c> get</c><00:09:56.120><c> with</c><00:09:56.279><c> you</c><00:09:56.560><c> if</c><00:09:56.680><c> you</c>

00:09:56.829 --> 00:09:56.839 align:start position:0%
more than happy to get with you if you
 

00:09:56.839 --> 00:09:58.670 align:start position:0%
more than happy to get with you if you
have<00:09:57.000><c> any</c><00:09:57.240><c> help</c><00:09:57.560><c> or</c><00:09:57.880><c> need</c><00:09:58.160><c> anything</c><00:09:58.480><c> thanks</c>

00:09:58.670 --> 00:09:58.680 align:start position:0%
have any help or need anything thanks
 

00:09:58.680 --> 00:10:02.360 align:start position:0%
have any help or need anything thanks
for<00:09:58.839><c> watching</c><00:09:59.240><c> and</c><00:09:59.320><c> I'll</c><00:09:59.440><c> see</c><00:09:59.600><c> you</c><00:09:59.760><c> next</c><00:09:59.920><c> time</c>

