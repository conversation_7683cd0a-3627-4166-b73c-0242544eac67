WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.540 align:start position:0%
 
all<00:00:00.359><c> right</c><00:00:00.719><c> we're</c><00:00:00.930><c> here</c><00:00:01.079><c> for</c><00:00:01.290><c> another</c><00:00:01.550><c> another</c>

00:00:02.540 --> 00:00:02.550 align:start position:0%
all right we're here for another another
 

00:00:02.550 --> 00:00:04.280 align:start position:0%
all right we're here for another another
coding<00:00:02.909><c> interview</c><00:00:03.060><c> question</c><00:00:03.570><c> and</c><00:00:03.810><c> this</c><00:00:04.080><c> one's</c>

00:00:04.280 --> 00:00:04.290 align:start position:0%
coding interview question and this one's
 

00:00:04.290 --> 00:00:06.289 align:start position:0%
coding interview question and this one's
so<00:00:04.470><c> me</c><00:00:04.650><c> on</c><00:00:04.710><c> Big</c><00:00:04.859><c> O</c><00:00:04.920><c> notation</c><00:00:05.100><c> I've</c><00:00:05.730><c> I'm</c><00:00:06.210><c> only</c>

00:00:06.289 --> 00:00:06.299 align:start position:0%
so me on Big O notation I've I'm only
 

00:00:06.299 --> 00:00:08.030 align:start position:0%
so me on Big O notation I've I'm only
doing<00:00:06.509><c> one</c><00:00:06.720><c> example</c><00:00:07.109><c> this</c><00:00:07.200><c> time</c><00:00:07.410><c> because</c><00:00:07.890><c> it's</c>

00:00:08.030 --> 00:00:08.040 align:start position:0%
doing one example this time because it's
 

00:00:08.040 --> 00:00:11.299 align:start position:0%
doing one example this time because it's
based<00:00:08.760><c> on</c><00:00:08.820><c> recursion</c><00:00:09.389><c> and</c><00:00:09.540><c> this</c><00:00:10.309><c> algorithm</c>

00:00:11.299 --> 00:00:11.309 align:start position:0%
based on recursion and this algorithm
 

00:00:11.309 --> 00:00:12.680 align:start position:0%
based on recursion and this algorithm
the<00:00:11.490><c> fact</c><00:00:11.670><c> finding</c><00:00:11.910><c> the</c><00:00:12.000><c> factorial</c><00:00:12.570><c> of</c><00:00:12.599><c> a</c>

00:00:12.680 --> 00:00:12.690 align:start position:0%
the fact finding the factorial of a
 

00:00:12.690 --> 00:00:15.530 align:start position:0%
the fact finding the factorial of a
number<00:00:12.719><c> is</c><00:00:13.530><c> probably</c><00:00:14.070><c> 1</c><00:00:14.370><c> if</c><00:00:14.700><c> you're</c><00:00:15.210><c> just</c>

00:00:15.530 --> 00:00:15.540 align:start position:0%
number is probably 1 if you're just
 

00:00:15.540 --> 00:00:17.300 align:start position:0%
number is probably 1 if you're just
getting<00:00:15.660><c> the</c><00:00:15.839><c> recursion</c><00:00:16.109><c> or</c><00:00:16.410><c> you</c><00:00:17.039><c> seen</c><00:00:17.220><c> it</c>

00:00:17.300 --> 00:00:17.310 align:start position:0%
getting the recursion or you seen it
 

00:00:17.310 --> 00:00:19.510 align:start position:0%
getting the recursion or you seen it
before<00:00:17.400><c> or</c><00:00:18.000><c> maybe</c><00:00:18.119><c> you</c><00:00:18.270><c> haven't</c><00:00:18.630><c> you</c><00:00:19.199><c> will</c>

00:00:19.510 --> 00:00:19.520 align:start position:0%
before or maybe you haven't you will
 

00:00:19.520 --> 00:00:21.920 align:start position:0%
before or maybe you haven't you will
because<00:00:20.520><c> this</c><00:00:20.609><c> is</c><00:00:20.760><c> this</c><00:00:21.420><c> is</c><00:00:21.480><c> one</c><00:00:21.750><c> you're</c>

00:00:21.920 --> 00:00:21.930 align:start position:0%
because this is this is one you're
 

00:00:21.930 --> 00:00:24.320 align:start position:0%
because this is this is one you're
always<00:00:22.500><c> introduced</c><00:00:23.039><c> to</c><00:00:23.220><c> in</c><00:00:23.519><c> beginning</c><00:00:23.939><c> we're</c>

00:00:24.320 --> 00:00:24.330 align:start position:0%
always introduced to in beginning we're
 

00:00:24.330 --> 00:00:27.170 align:start position:0%
always introduced to in beginning we're
starting<00:00:24.570><c> learn</c><00:00:24.660><c> recursion</c><00:00:25.050><c> okay</c><00:00:25.859><c> so</c><00:00:26.760><c> quick</c>

00:00:27.170 --> 00:00:27.180 align:start position:0%
starting learn recursion okay so quick
 

00:00:27.180 --> 00:00:29.450 align:start position:0%
starting learn recursion okay so quick
intro<00:00:27.539><c> here</c><00:00:27.980><c> first</c><00:00:28.980><c> we're</c><00:00:29.160><c> gonna</c><00:00:29.250><c> go</c><00:00:29.340><c> to</c><00:00:29.369><c> the</c>

00:00:29.450 --> 00:00:29.460 align:start position:0%
intro here first we're gonna go to the
 

00:00:29.460 --> 00:00:31.999 align:start position:0%
intro here first we're gonna go to the
tiny<00:00:29.699><c> complexity</c><00:00:30.269><c> of</c><00:00:30.449><c> this</c><00:00:31.019><c> algorithm</c><00:00:31.349><c> and</c>

00:00:31.999 --> 00:00:32.009 align:start position:0%
tiny complexity of this algorithm and
 

00:00:32.009 --> 00:00:34.400 align:start position:0%
tiny complexity of this algorithm and
then<00:00:32.340><c> what</c><00:00:33.210><c> I'm</c><00:00:33.270><c> gonna</c><00:00:33.390><c> do</c><00:00:33.540><c> is</c><00:00:33.750><c> briefly</c><00:00:34.050><c> go</c>

00:00:34.400 --> 00:00:34.410 align:start position:0%
then what I'm gonna do is briefly go
 

00:00:34.410 --> 00:00:37.160 align:start position:0%
then what I'm gonna do is briefly go
over<00:00:34.440><c> recursion</c><00:00:35.399><c> so</c><00:00:36.360><c> that</c><00:00:36.510><c> you</c><00:00:36.570><c> understand</c>

00:00:37.160 --> 00:00:37.170 align:start position:0%
over recursion so that you understand
 

00:00:37.170 --> 00:00:39.290 align:start position:0%
over recursion so that you understand
why<00:00:37.380><c> it</c><00:00:38.219><c> is</c><00:00:38.340><c> a</c><00:00:38.370><c> time</c><00:00:38.579><c> fussy</c><00:00:38.910><c> that</c><00:00:38.940><c> it</c><00:00:39.149><c> is</c>

00:00:39.290 --> 00:00:39.300 align:start position:0%
why it is a time fussy that it is
 

00:00:39.300 --> 00:00:41.479 align:start position:0%
why it is a time fussy that it is
instead<00:00:39.809><c> of</c><00:00:39.870><c> me</c><00:00:40.050><c> just</c><00:00:40.260><c> saying</c><00:00:40.500><c> oh</c><00:00:40.770><c> it's</c>

00:00:41.479 --> 00:00:41.489 align:start position:0%
instead of me just saying oh it's
 

00:00:41.489 --> 00:00:44.000 align:start position:0%
instead of me just saying oh it's
recursion<00:00:42.329><c> that's</c><00:00:42.570><c> why</c><00:00:42.930><c> it's</c><00:00:43.230><c> this</c><00:00:43.860><c> high</c>

00:00:44.000 --> 00:00:44.010 align:start position:0%
recursion that's why it's this high
 

00:00:44.010 --> 00:00:47.450 align:start position:0%
recursion that's why it's this high
capacity<00:00:44.520><c> okay</c><00:00:45.059><c> and</c><00:00:45.860><c> even</c><00:00:46.860><c> if</c><00:00:46.920><c> you</c><00:00:47.010><c> kind</c><00:00:47.340><c> of</c>

00:00:47.450 --> 00:00:47.460 align:start position:0%
capacity okay and even if you kind of
 

00:00:47.460 --> 00:00:49.130 align:start position:0%
capacity okay and even if you kind of
understand<00:00:47.850><c> it</c><00:00:47.940><c> or</c><00:00:48.149><c> you</c><00:00:48.480><c> don't</c><00:00:48.660><c> understand</c><00:00:48.989><c> it</c>

00:00:49.130 --> 00:00:49.140 align:start position:0%
understand it or you don't understand it
 

00:00:49.140 --> 00:00:51.619 align:start position:0%
understand it or you don't understand it
hopefully<00:00:49.620><c> this</c><00:00:49.710><c> will</c><00:00:49.770><c> help</c><00:00:49.829><c> you</c><00:00:49.980><c> out</c><00:00:50.629><c> because</c>

00:00:51.619 --> 00:00:51.629 align:start position:0%
hopefully this will help you out because
 

00:00:51.629 --> 00:00:54.290 align:start position:0%
hopefully this will help you out because
I<00:00:52.260><c> know</c><00:00:52.680><c> that</c><00:00:52.949><c> when</c><00:00:53.039><c> I</c><00:00:53.070><c> was</c><00:00:53.129><c> in</c><00:00:53.250><c> college</c><00:00:53.460><c> when</c><00:00:54.239><c> I</c>

00:00:54.290 --> 00:00:54.300 align:start position:0%
I know that when I was in college when I
 

00:00:54.300 --> 00:00:55.549 align:start position:0%
I know that when I was in college when I
first<00:00:54.360><c> learned</c><00:00:54.780><c> that</c><00:00:54.840><c> I</c><00:00:55.050><c> didn't</c><00:00:55.440><c> really</c>

00:00:55.549 --> 00:00:55.559 align:start position:0%
first learned that I didn't really
 

00:00:55.559 --> 00:00:57.529 align:start position:0%
first learned that I didn't really
understand<00:00:55.949><c> it</c><00:00:56.070><c> and</c><00:00:56.910><c> people</c><00:00:57.210><c> around</c><00:00:57.360><c> me</c>

00:00:57.529 --> 00:00:57.539 align:start position:0%
understand it and people around me
 

00:00:57.539 --> 00:00:58.819 align:start position:0%
understand it and people around me
didn't<00:00:57.719><c> really</c><00:00:57.899><c> help</c><00:00:58.079><c> because</c><00:00:58.469><c> they</c><00:00:58.590><c> didn't</c>

00:00:58.819 --> 00:00:58.829 align:start position:0%
didn't really help because they didn't
 

00:00:58.829 --> 00:01:01.069 align:start position:0%
didn't really help because they didn't
know<00:00:58.949><c> and</c><00:00:59.190><c> I</c><00:00:59.879><c> was</c><00:01:00.030><c> told</c><00:01:00.239><c> you</c><00:01:00.510><c> know</c><00:01:00.629><c> you</c><00:01:00.960><c> might</c>

00:01:01.069 --> 00:01:01.079 align:start position:0%
know and I was told you know you might
 

00:01:01.079 --> 00:01:03.770 align:start position:0%
know and I was told you know you might
have<00:01:01.109><c> heard</c><00:01:01.350><c> the</c><00:01:01.649><c> BS</c><00:01:01.980><c> like</c><00:01:02.370><c> oh</c><00:01:02.789><c> I</c><00:01:03.510><c> don't</c>

00:01:03.770 --> 00:01:03.780 align:start position:0%
have heard the BS like oh I don't
 

00:01:03.780 --> 00:01:05.929 align:start position:0%
have heard the BS like oh I don't
understand<00:01:04.439><c> recursion</c><00:01:04.739><c> you</c><00:01:05.189><c> got</c><00:01:05.760><c> a</c><00:01:05.790><c> nut</c>

00:01:05.929 --> 00:01:05.939 align:start position:0%
understand recursion you got a nut
 

00:01:05.939 --> 00:01:08.960 align:start position:0%
understand recursion you got a nut
recursion<00:01:06.420><c> or</c><00:01:06.540><c> it</c><00:01:07.409><c> works</c><00:01:08.189><c> because</c><00:01:08.549><c> it</c><00:01:08.790><c> works</c>

00:01:08.960 --> 00:01:08.970 align:start position:0%
recursion or it works because it works
 

00:01:08.970 --> 00:01:12.560 align:start position:0%
recursion or it works because it works
there's<00:01:09.600><c> some</c><00:01:10.040><c> BS</c><00:01:11.040><c> like</c><00:01:11.310><c> that</c><00:01:11.520><c> I've</c><00:01:12.210><c> heard</c>

00:01:12.560 --> 00:01:12.570 align:start position:0%
there's some BS like that I've heard
 

00:01:12.570 --> 00:01:15.289 align:start position:0%
there's some BS like that I've heard
I've<00:01:13.080><c> heard</c><00:01:13.350><c> that</c><00:01:13.610><c> and</c><00:01:14.610><c> it</c><00:01:14.700><c> wasn't</c><00:01:14.850><c> till</c><00:01:15.030><c> later</c>

00:01:15.289 --> 00:01:15.299 align:start position:0%
I've heard that and it wasn't till later
 

00:01:15.299 --> 00:01:16.490 align:start position:0%
I've heard that and it wasn't till later
on<00:01:15.390><c> I</c><00:01:15.509><c> was</c><00:01:15.750><c> introduced</c><00:01:16.049><c> to</c><00:01:16.080><c> activation</c>

00:01:16.490 --> 00:01:16.500 align:start position:0%
on I was introduced to activation
 

00:01:16.500 --> 00:01:19.070 align:start position:0%
on I was introduced to activation
records<00:01:17.100><c> for</c><00:01:17.340><c> the</c><00:01:17.430><c> activation</c><00:01:17.640><c> stack</c><00:01:18.060><c> and</c><00:01:18.390><c> I</c>

00:01:19.070 --> 00:01:19.080 align:start position:0%
records for the activation stack and I
 

00:01:19.080 --> 00:01:21.800 align:start position:0%
records for the activation stack and I
was<00:01:19.170><c> like</c><00:01:19.259><c> oh</c><00:01:19.409><c> that's</c><00:01:20.369><c> why</c><00:01:20.759><c> it</c><00:01:20.790><c> works</c><00:01:21.210><c> the</c><00:01:21.630><c> one</c>

00:01:21.800 --> 00:01:21.810 align:start position:0%
was like oh that's why it works the one
 

00:01:21.810 --> 00:01:23.420 align:start position:0%
was like oh that's why it works the one
it's<00:01:21.960><c> the</c><00:01:22.020><c> actual</c><00:01:22.320><c> logic</c><00:01:22.470><c> behind</c><00:01:22.799><c> recursion</c>

00:01:23.420 --> 00:01:23.430 align:start position:0%
it's the actual logic behind recursion
 

00:01:23.430 --> 00:01:25.910 align:start position:0%
it's the actual logic behind recursion
okay<00:01:23.909><c> so</c><00:01:23.970><c> way</c><00:01:24.960><c> to</c><00:01:25.020><c> that</c><00:01:25.200><c> oh</c><00:01:25.350><c> my</c><00:01:25.470><c> stop</c><00:01:25.619><c> talking</c>

00:01:25.910 --> 00:01:25.920 align:start position:0%
okay so way to that oh my stop talking
 

00:01:25.920 --> 00:01:28.010 align:start position:0%
okay so way to that oh my stop talking
and<00:01:26.310><c> we're</c><00:01:26.490><c> getting</c><00:01:26.610><c> to</c><00:01:26.790><c> the</c><00:01:26.850><c> example</c><00:01:27.210><c> okay</c><00:01:27.780><c> so</c>

00:01:28.010 --> 00:01:28.020 align:start position:0%
and we're getting to the example okay so
 

00:01:28.020 --> 00:01:32.630 align:start position:0%
and we're getting to the example okay so
let's<00:01:28.590><c> get</c><00:01:28.680><c> into</c><00:01:28.740><c> the</c><00:01:28.920><c> example</c><00:01:30.380><c> alright</c><00:01:31.640><c> so</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
let's get into the example alright so
 

00:01:32.640 --> 00:01:34.399 align:start position:0%
let's get into the example alright so
the<00:01:32.759><c> first</c><00:01:32.790><c> thing</c><00:01:33.030><c> here</c><00:01:33.329><c> is</c><00:01:33.600><c> let's</c><00:01:34.079><c> just</c><00:01:34.140><c> go</c>

00:01:34.399 --> 00:01:34.409 align:start position:0%
the first thing here is let's just go
 

00:01:34.409 --> 00:01:38.749 align:start position:0%
the first thing here is let's just go
over<00:01:35.030><c> let's</c><00:01:36.030><c> give</c><00:01:36.600><c> an</c><00:01:36.750><c> input</c><00:01:36.930><c> number</c><00:01:37.439><c> and</c><00:01:37.759><c> go</c>

00:01:38.749 --> 00:01:38.759 align:start position:0%
over let's give an input number and go
 

00:01:38.759 --> 00:01:41.450 align:start position:0%
over let's give an input number and go
through<00:01:39.170><c> kind</c><00:01:40.170><c> of</c><00:01:40.350><c> the</c><00:01:40.619><c> whole</c><00:01:40.799><c> algorithm</c>

00:01:41.450 --> 00:01:41.460 align:start position:0%
through kind of the whole algorithm
 

00:01:41.460 --> 00:01:43.219 align:start position:0%
through kind of the whole algorithm
we'll<00:01:41.820><c> do</c><00:01:41.880><c> some</c><00:01:42.030><c> small</c><00:01:42.329><c> we're</c><00:01:42.540><c> gonna</c><00:01:42.689><c> do</c><00:01:42.840><c> three</c>

00:01:43.219 --> 00:01:43.229 align:start position:0%
we'll do some small we're gonna do three
 

00:01:43.229 --> 00:01:46.370 align:start position:0%
we'll do some small we're gonna do three
okay<00:01:44.149><c> so</c><00:01:45.149><c> the</c><00:01:45.210><c> first</c><00:01:45.360><c> thing</c><00:01:45.600><c> is</c><00:01:45.720><c> we're</c><00:01:46.259><c> gonna</c>

00:01:46.370 --> 00:01:46.380 align:start position:0%
okay so the first thing is we're gonna
 

00:01:46.380 --> 00:01:48.950 align:start position:0%
okay so the first thing is we're gonna
say<00:01:46.680><c> I'm</c><00:01:47.399><c> gonna</c><00:01:47.640><c> say</c><00:01:47.790><c> F</c><00:01:48.060><c> for</c><00:01:48.270><c> factorial</c><00:01:48.630><c> over</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
say I'm gonna say F for factorial over
 

00:01:48.960 --> 00:01:53.260 align:start position:0%
say I'm gonna say F for factorial over
to<00:01:49.409><c> say</c><00:01:49.530><c> F</c><00:01:49.710><c> of</c><00:01:49.860><c> 3</c><00:01:50.159><c> so</c><00:01:50.820><c> we're</c><00:01:51.090><c> on</c><00:01:51.180><c> so</c><00:01:51.750><c> n</c><00:01:51.869><c> is</c><00:01:51.899><c> 3</c>

00:01:53.260 --> 00:01:53.270 align:start position:0%
to say F of 3 so we're on so n is 3
 

00:01:53.270 --> 00:01:56.510 align:start position:0%
to say F of 3 so we're on so n is 3
we're<00:01:54.270><c> gonna</c><00:01:54.360><c> say</c><00:01:54.689><c> at</c><00:01:55.229><c> 3</c><00:01:55.710><c> is</c><00:01:55.799><c> not</c><00:01:55.950><c> 3</c><00:01:56.340><c> is</c><00:01:56.430><c> not</c>

00:01:56.510 --> 00:01:56.520 align:start position:0%
we're gonna say at 3 is not 3 is not
 

00:01:56.520 --> 00:01:58.910 align:start position:0%
we're gonna say at 3 is not 3 is not
less<00:01:56.579><c> than</c><00:01:56.759><c> 0</c><00:01:56.820><c> 3</c><00:01:57.659><c> is</c><00:01:57.719><c> not</c><00:01:57.810><c> equal</c><00:01:58.140><c> 0</c><00:01:58.380><c> so</c><00:01:58.680><c> to</c><00:01:58.770><c> come</c>

00:01:58.910 --> 00:01:58.920 align:start position:0%
less than 0 3 is not equal 0 so to come
 

00:01:58.920 --> 00:02:00.530 align:start position:0%
less than 0 3 is not equal 0 so to come
down<00:01:59.040><c> here</c><00:01:59.250><c> we're</c><00:01:59.610><c> gonna</c><00:01:59.700><c> say</c><00:01:59.880><c> you</c><00:01:59.939><c> returned</c><00:02:00.270><c> n</c>

00:02:00.530 --> 00:02:00.540 align:start position:0%
down here we're gonna say you returned n
 

00:02:00.540 --> 00:02:04.850 align:start position:0%
down here we're gonna say you returned n
times<00:02:01.110><c> factorial</c><00:02:01.560><c> of</c><00:02:02.340><c> n</c><00:02:03.329><c> times</c><00:02:03.719><c> 1</c><00:02:04.079><c> so</c><00:02:04.710><c> we're</c>

00:02:04.850 --> 00:02:04.860 align:start position:0%
times factorial of n times 1 so we're
 

00:02:04.860 --> 00:02:06.530 align:start position:0%
times factorial of n times 1 so we're
gonna<00:02:04.950><c> call</c><00:02:05.130><c> a</c><00:02:05.189><c> factorial</c><00:02:05.579><c> again</c><00:02:06.000><c> here</c><00:02:06.360><c> and</c>

00:02:06.530 --> 00:02:06.540 align:start position:0%
gonna call a factorial again here and
 

00:02:06.540 --> 00:02:10.369 align:start position:0%
gonna call a factorial again here and
this<00:02:07.340><c> factor</c><00:02:08.340><c> of</c><00:02:08.369><c> n</c><00:02:08.610><c> minus</c><00:02:08.849><c> 1</c><00:02:09.060><c> factorial</c><00:02:09.869><c> 3</c>

00:02:10.369 --> 00:02:10.379 align:start position:0%
this factor of n minus 1 factorial 3
 

00:02:10.379 --> 00:02:11.839 align:start position:0%
this factor of n minus 1 factorial 3
minus<00:02:10.679><c> 1</c><00:02:10.709><c> is</c><00:02:11.129><c> 2</c>

00:02:11.839 --> 00:02:11.849 align:start position:0%
minus 1 is 2
 

00:02:11.849 --> 00:02:13.580 align:start position:0%
minus 1 is 2
so<00:02:12.840><c> now</c><00:02:13.020><c> we're</c><00:02:13.319><c> going</c><00:02:13.440><c> to</c>

00:02:13.580 --> 00:02:13.590 align:start position:0%
so now we're going to
 

00:02:13.590 --> 00:02:17.360 align:start position:0%
so now we're going to
end<00:02:13.680><c> up</c><00:02:13.800><c> calling</c><00:02:15.080><c> factorial</c><00:02:16.080><c> of</c><00:02:16.260><c> 2</c><00:02:16.319><c> all</c><00:02:17.310><c> right</c>

00:02:17.360 --> 00:02:17.370 align:start position:0%
end up calling factorial of 2 all right
 

00:02:17.370 --> 00:02:19.430 align:start position:0%
end up calling factorial of 2 all right
let's<00:02:18.209><c> go</c><00:02:18.300><c> through</c><00:02:18.330><c> the</c><00:02:18.480><c> algorithm</c><00:02:18.780><c> again</c><00:02:18.810><c> 2</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
let's go through the algorithm again 2
 

00:02:19.440 --> 00:02:22.100 align:start position:0%
let's go through the algorithm again 2
is<00:02:19.470><c> not</c><00:02:19.680><c> less</c><00:02:19.830><c> than</c><00:02:19.920><c> 0</c><00:02:19.980><c> 2</c><00:02:20.280><c> is</c><00:02:20.670><c> not</c><00:02:20.760><c> equal</c><00:02:21.030><c> 0</c><00:02:21.330><c> so</c>

00:02:22.100 --> 00:02:22.110 align:start position:0%
is not less than 0 2 is not equal 0 so
 

00:02:22.110 --> 00:02:23.600 align:start position:0%
is not less than 0 2 is not equal 0 so
we<00:02:22.200><c> get</c><00:02:22.379><c> down</c><00:02:22.500><c> to</c><00:02:22.680><c> the</c><00:02:22.769><c> return</c><00:02:23.010><c> statement</c><00:02:23.190><c> at</c>

00:02:23.600 --> 00:02:23.610 align:start position:0%
we get down to the return statement at
 

00:02:23.610 --> 00:02:27.860 align:start position:0%
we get down to the return statement at
the<00:02:24.239><c> bottom</c><00:02:24.440><c> return</c><00:02:25.610><c> n</c><00:02:26.610><c> times</c><00:02:27.060><c> factorial</c><00:02:27.330><c> of</c><00:02:27.690><c> n</c>

00:02:27.860 --> 00:02:27.870 align:start position:0%
the bottom return n times factorial of n
 

00:02:27.870 --> 00:02:30.860 align:start position:0%
the bottom return n times factorial of n
minus<00:02:27.930><c> 1</c><00:02:28.470><c> so</c><00:02:28.800><c> 2</c><00:02:28.950><c> times</c><00:02:29.370><c> the</c><00:02:30.030><c> factorial</c><00:02:30.450><c> of</c><00:02:30.810><c> 2</c>

00:02:30.860 --> 00:02:30.870 align:start position:0%
minus 1 so 2 times the factorial of 2
 

00:02:30.870 --> 00:02:32.839 align:start position:0%
minus 1 so 2 times the factorial of 2
minus<00:02:31.410><c> 1</c><00:02:31.560><c> so</c><00:02:32.220><c> that</c><00:02:32.370><c> means</c><00:02:32.430><c> we're</c><00:02:32.700><c> going</c><00:02:32.790><c> to</c>

00:02:32.839 --> 00:02:32.849 align:start position:0%
minus 1 so that means we're going to
 

00:02:32.849 --> 00:02:37.430 align:start position:0%
minus 1 so that means we're going to
call<00:02:33.319><c> factorial</c><00:02:34.319><c> of</c><00:02:34.530><c> 1</c><00:02:34.590><c> 2</c><00:02:35.400><c> minus</c><00:02:35.610><c> 1</c><00:02:35.640><c> is</c><00:02:35.730><c> 1</c><00:02:36.440><c> ok</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
call factorial of 1 2 minus 1 is 1 ok
 

00:02:37.440 --> 00:02:39.020 align:start position:0%
call factorial of 1 2 minus 1 is 1 ok
we're<00:02:37.769><c> going</c><00:02:37.890><c> to</c><00:02:37.980><c> go</c><00:02:38.040><c> through</c><00:02:38.190><c> it</c><00:02:38.250><c> again</c><00:02:38.340><c> a</c>

00:02:39.020 --> 00:02:39.030 align:start position:0%
we're going to go through it again a
 

00:02:39.030 --> 00:02:42.410 align:start position:0%
we're going to go through it again a
factorial<00:02:39.959><c> of</c><00:02:40.140><c> 1</c><00:02:40.260><c> 1</c><00:02:41.190><c> is</c><00:02:41.310><c> now</c><00:02:41.430><c> less</c><00:02:41.549><c> than</c><00:02:41.670><c> 0</c><00:02:41.910><c> 1</c>

00:02:42.410 --> 00:02:42.420 align:start position:0%
factorial of 1 1 is now less than 0 1
 

00:02:42.420 --> 00:02:45.500 align:start position:0%
factorial of 1 1 is now less than 0 1
does<00:02:42.510><c> not</c><00:02:42.540><c> equal</c><00:02:42.810><c> 0</c><00:02:43.700><c> return</c><00:02:44.700><c> 1</c><00:02:44.910><c> times</c>

00:02:45.500 --> 00:02:45.510 align:start position:0%
does not equal 0 return 1 times
 

00:02:45.510 --> 00:02:49.190 align:start position:0%
does not equal 0 return 1 times
factorial<00:02:45.810><c> of</c><00:02:46.260><c> 1</c><00:02:46.799><c> minus</c><00:02:47.400><c> 1</c><00:02:47.670><c> well</c><00:02:48.540><c> one</c><00:02:48.900><c> must</c><00:02:49.019><c> 1</c>

00:02:49.190 --> 00:02:49.200 align:start position:0%
factorial of 1 minus 1 well one must 1
 

00:02:49.200 --> 00:02:52.520 align:start position:0%
factorial of 1 minus 1 well one must 1
is<00:02:49.290><c> 0</c><00:02:49.620><c> so</c><00:02:50.250><c> that</c><00:02:50.519><c> means</c><00:02:50.700><c> we</c><00:02:50.880><c> call</c><00:02:51.120><c> f</c><00:02:51.360><c> of</c><00:02:51.870><c> or</c>

00:02:52.520 --> 00:02:52.530 align:start position:0%
is 0 so that means we call f of or
 

00:02:52.530 --> 00:02:56.900 align:start position:0%
is 0 so that means we call f of or
factorial<00:02:53.130><c> of</c><00:02:53.280><c> zero</c><00:02:54.950><c> all</c><00:02:55.950><c> right</c><00:02:56.099><c> so</c><00:02:56.310><c> now</c><00:02:56.849><c> we</c>

00:02:56.900 --> 00:02:56.910 align:start position:0%
factorial of zero all right so now we
 

00:02:56.910 --> 00:03:00.080 align:start position:0%
factorial of zero all right so now we
know<00:02:57.239><c> n</c><00:02:57.450><c> is</c><00:02:57.599><c> 0</c><00:02:57.920><c> well</c><00:02:58.920><c> and</c><00:02:59.220><c> is</c><00:02:59.340><c> not</c><00:02:59.549><c> less</c><00:02:59.760><c> than</c><00:02:59.790><c> or</c>

00:03:00.080 --> 00:03:00.090 align:start position:0%
know n is 0 well and is not less than or
 

00:03:00.090 --> 00:03:03.559 align:start position:0%
know n is 0 well and is not less than or
0<00:03:00.120><c> it's</c><00:03:00.510><c> not</c><00:03:00.540><c> less</c><00:03:00.780><c> than</c><00:03:00.900><c> 0</c><00:03:01.170><c> but</c><00:03:01.799><c> 0</c><00:03:02.239><c> does</c><00:03:03.239><c> equal</c>

00:03:03.559 --> 00:03:03.569 align:start position:0%
0 it's not less than 0 but 0 does equal
 

00:03:03.569 --> 00:03:04.309 align:start position:0%
0 it's not less than 0 but 0 does equal
0

00:03:04.309 --> 00:03:04.319 align:start position:0%
0
 

00:03:04.319 --> 00:03:10.009 align:start position:0%
0
so<00:03:04.920><c> that</c><00:03:05.069><c> means</c><00:03:05.280><c> we</c><00:03:06.030><c> return</c><00:03:06.480><c> 1</c><00:03:08.030><c> ok</c><00:03:09.030><c> so</c><00:03:09.810><c> there</c>

00:03:10.009 --> 00:03:10.019 align:start position:0%
so that means we return 1 ok so there
 

00:03:10.019 --> 00:03:12.259 align:start position:0%
so that means we return 1 ok so there
hasn't<00:03:10.170><c> really</c><00:03:10.379><c> been</c><00:03:10.560><c> any</c><00:03:11.030><c> computation</c><00:03:12.030><c> yet</c>

00:03:12.259 --> 00:03:12.269 align:start position:0%
hasn't really been any computation yet
 

00:03:12.269 --> 00:03:14.780 align:start position:0%
hasn't really been any computation yet
by<00:03:12.720><c> the</c><00:03:12.750><c> way</c><00:03:12.870><c> in</c><00:03:13.079><c> recursion</c><00:03:13.650><c> we're</c><00:03:14.610><c> going</c>

00:03:14.780 --> 00:03:14.790 align:start position:0%
by the way in recursion we're going
 

00:03:14.790 --> 00:03:15.680 align:start position:0%
by the way in recursion we're going
through<00:03:14.910><c> the</c><00:03:15.000><c> aggregate</c><00:03:15.150><c> where's</c><00:03:15.510><c> going</c>

00:03:15.680 --> 00:03:15.690 align:start position:0%
through the aggregate where's going
 

00:03:15.690 --> 00:03:16.850 align:start position:0%
through the aggregate where's going
through<00:03:15.780><c> an</c><00:03:15.840><c> algorithm</c><00:03:16.110><c> so</c><00:03:16.470><c> I'm</c><00:03:16.680><c> going</c><00:03:16.799><c> to</c>

00:03:16.850 --> 00:03:16.860 align:start position:0%
through an algorithm so I'm going to
 

00:03:16.860 --> 00:03:18.379 align:start position:0%
through an algorithm so I'm going to
show<00:03:16.950><c> you</c><00:03:17.010><c> at</c><00:03:17.489><c> the</c><00:03:17.639><c> time</c><00:03:17.819><c> plus</c><00:03:17.940><c> he</c><00:03:18.090><c> is</c><00:03:18.120><c> first</c>

00:03:18.379 --> 00:03:18.389 align:start position:0%
show you at the time plus he is first
 

00:03:18.389 --> 00:03:20.809 align:start position:0%
show you at the time plus he is first
but<00:03:19.319><c> we</c><00:03:19.829><c> haven't</c><00:03:19.950><c> done</c><00:03:20.040><c> actually</c><00:03:20.220><c> computation</c>

00:03:20.809 --> 00:03:20.819 align:start position:0%
but we haven't done actually computation
 

00:03:20.819 --> 00:03:23.180 align:start position:0%
but we haven't done actually computation
yet<00:03:20.940><c> but</c><00:03:21.209><c> now</c><00:03:21.389><c> we've</c><00:03:21.630><c> hit</c><00:03:21.750><c> the</c><00:03:21.870><c> base</c><00:03:22.230><c> case</c><00:03:22.560><c> you</c>

00:03:23.180 --> 00:03:23.190 align:start position:0%
yet but now we've hit the base case you
 

00:03:23.190 --> 00:03:25.640 align:start position:0%
yet but now we've hit the base case you
know<00:03:23.250><c> if</c><00:03:23.430><c> n</c><00:03:23.819><c> is</c><00:03:24.090><c> less</c><00:03:24.180><c> than</c><00:03:24.239><c> 0</c><00:03:24.540><c> or</c><00:03:24.630><c> if</c><00:03:24.959><c> N</c><00:03:25.139><c> equals</c>

00:03:25.640 --> 00:03:25.650 align:start position:0%
know if n is less than 0 or if N equals
 

00:03:25.650 --> 00:03:28.699 align:start position:0%
know if n is less than 0 or if N equals
0<00:03:25.829><c> there's</c><00:03:26.340><c> a</c><00:03:26.430><c> base</c><00:03:26.880><c> cases</c><00:03:27.180><c> we</c><00:03:28.049><c> hit</c><00:03:28.200><c> the</c><00:03:28.380><c> one</c><00:03:28.530><c> of</c>

00:03:28.699 --> 00:03:28.709 align:start position:0%
0 there's a base cases we hit the one of
 

00:03:28.709 --> 00:03:32.599 align:start position:0%
0 there's a base cases we hit the one of
them<00:03:28.859><c> N</c><00:03:29.340><c> equals</c><00:03:29.730><c> 0</c><00:03:29.940><c> so</c><00:03:30.359><c> we're</c><00:03:30.540><c> turning</c><00:03:30.660><c> 1</c><00:03:31.609><c> right</c>

00:03:32.599 --> 00:03:32.609 align:start position:0%
them N equals 0 so we're turning 1 right
 

00:03:32.609 --> 00:03:36.830 align:start position:0%
them N equals 0 so we're turning 1 right
so<00:03:32.880><c> we</c><00:03:33.690><c> turn</c><00:03:33.959><c> 1</c><00:03:34.349><c> over</c><00:03:34.859><c> here</c><00:03:35.220><c> and</c><00:03:35.459><c> now</c><00:03:36.450><c> we're</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
so we turn 1 over here and now we're
 

00:03:36.840 --> 00:03:40.190 align:start position:0%
so we turn 1 over here and now we're
gonna<00:03:36.930><c> do</c><00:03:37.139><c> factorial</c><00:03:37.950><c> of</c><00:03:38.220><c> 1</c><00:03:38.280><c> well</c><00:03:39.180><c> or</c><00:03:39.959><c> what</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
gonna do factorial of 1 well or what
 

00:03:40.200 --> 00:03:45.680 align:start position:0%
gonna do factorial of 1 well or what
factorial<00:03:41.099><c> of</c><00:03:41.310><c> N</c><00:03:41.790><c> equals</c><00:03:42.030><c> 1</c><00:03:42.480><c> well</c><00:03:44.329><c> we'd</c><00:03:45.329><c> have</c><00:03:45.419><c> n</c>

00:03:45.680 --> 00:03:45.690 align:start position:0%
factorial of N equals 1 well we'd have n
 

00:03:45.690 --> 00:03:48.920 align:start position:0%
factorial of N equals 1 well we'd have n
down<00:03:46.200><c> here</c><00:03:46.620><c> so</c><00:03:47.519><c> we're</c><00:03:47.639><c> gonna</c><00:03:47.730><c> do</c><00:03:47.940><c> it</c><00:03:48.660><c> says</c>

00:03:48.920 --> 00:03:48.930 align:start position:0%
down here so we're gonna do it says
 

00:03:48.930 --> 00:03:52.129 align:start position:0%
down here so we're gonna do it says
return<00:03:49.169><c> n</c><00:03:49.590><c> times</c><00:03:49.980><c> factorial</c><00:03:50.220><c> n</c><00:03:50.760><c> minus</c><00:03:50.790><c> 1</c><00:03:51.299><c> well</c>

00:03:52.129 --> 00:03:52.139 align:start position:0%
return n times factorial n minus 1 well
 

00:03:52.139 --> 00:03:57.500 align:start position:0%
return n times factorial n minus 1 well
the<00:03:52.470><c> factor</c><00:03:52.889><c> n</c><00:03:53.639><c> first</c><00:03:54.389><c> off</c><00:03:54.650><c> here</c><00:03:55.650><c> is</c><00:03:55.829><c> 1</c><00:03:56.730><c> but</c>

00:03:57.500 --> 00:03:57.510 align:start position:0%
the factor n first off here is 1 but
 

00:03:57.510 --> 00:04:00.620 align:start position:0%
the factor n first off here is 1 but
we're<00:03:57.660><c> multiplying</c><00:03:57.959><c> by</c><00:03:58.940><c> down</c><00:03:59.940><c> here</c><00:04:00.419><c> because</c>

00:04:00.620 --> 00:04:00.630 align:start position:0%
we're multiplying by down here because
 

00:04:00.630 --> 00:04:04.879 align:start position:0%
we're multiplying by down here because
the<00:04:01.109><c> factorial</c><00:04:01.769><c> of</c><00:04:01.950><c> 1</c><00:04:02.400><c> minus</c><00:04:03.120><c> 1</c><00:04:03.329><c> is</c><00:04:03.359><c> 0</c><00:04:03.560><c> well</c><00:04:04.560><c> f</c>

00:04:04.879 --> 00:04:04.889 align:start position:0%
the factorial of 1 minus 1 is 0 well f
 

00:04:04.889 --> 00:04:09.650 align:start position:0%
the factorial of 1 minus 1 is 0 well f
of<00:04:05.190><c> 0</c><00:04:05.609><c> is</c><00:04:06.329><c> 1</c><00:04:06.389><c> ok</c><00:04:08.090><c> so</c><00:04:09.090><c> now</c><00:04:09.180><c> we're</c><00:04:09.299><c> gonna</c><00:04:09.389><c> go</c><00:04:09.510><c> back</c>

00:04:09.650 --> 00:04:09.660 align:start position:0%
of 0 is 1 ok so now we're gonna go back
 

00:04:09.660 --> 00:04:15.050 align:start position:0%
of 0 is 1 ok so now we're gonna go back
up<00:04:09.900><c> where</c><00:04:10.349><c> a</c><00:04:10.919><c> factorial</c><00:04:11.669><c> of</c><00:04:11.970><c> 2</c><00:04:13.280><c> well</c><00:04:14.280><c> we're</c>

00:04:15.050 --> 00:04:15.060 align:start position:0%
up where a factorial of 2 well we're
 

00:04:15.060 --> 00:04:17.569 align:start position:0%
up where a factorial of 2 well we're
going<00:04:15.180><c> to</c><00:04:15.239><c> return</c><00:04:15.530><c> where</c><00:04:16.530><c> N</c><00:04:16.709><c> equals</c><00:04:16.799><c> 2</c><00:04:17.160><c> here</c><00:04:17.519><c> in</c>

00:04:17.569 --> 00:04:17.579 align:start position:0%
going to return where N equals 2 here in
 

00:04:17.579 --> 00:04:21.039 align:start position:0%
going to return where N equals 2 here in
this<00:04:17.669><c> factorial</c><00:04:18.650><c> so</c><00:04:19.650><c> we're</c><00:04:19.799><c> going</c><00:04:19.919><c> to</c><00:04:20.010><c> turn</c><00:04:20.160><c> 2</c>

00:04:21.039 --> 00:04:21.049 align:start position:0%
this factorial so we're going to turn 2
 

00:04:21.049 --> 00:04:24.830 align:start position:0%
this factorial so we're going to turn 2
times<00:04:22.049><c> the</c><00:04:22.560><c> factorial</c><00:04:23.010><c> of</c><00:04:23.310><c> 2</c><00:04:23.370><c> minus</c><00:04:23.849><c> 1</c><00:04:24.030><c> well</c><00:04:24.719><c> we</c>

00:04:24.830 --> 00:04:24.840 align:start position:0%
times the factorial of 2 minus 1 well we
 

00:04:24.840 --> 00:04:25.490 align:start position:0%
times the factorial of 2 minus 1 well we
know<00:04:25.020><c> what</c><00:04:25.140><c> that</c><00:04:25.169><c> is</c>

00:04:25.490 --> 00:04:25.500 align:start position:0%
know what that is
 

00:04:25.500 --> 00:04:31.719 align:start position:0%
know what that is
it's<00:04:26.070><c> 1</c><00:04:26.310><c> times</c><00:04:26.490><c> 1</c>

00:04:31.719 --> 00:04:31.729 align:start position:0%
 
 

00:04:31.729 --> 00:04:36.800 align:start position:0%
 
okay<00:04:32.729><c> so</c><00:04:33.270><c> we</c><00:04:33.750><c> did</c><00:04:33.960><c> here</c><00:04:34.800><c> uh</c><00:04:34.979><c> F</c><00:04:35.759><c> of</c><00:04:36.360><c> 2</c><00:04:36.569><c> or</c>

00:04:36.800 --> 00:04:36.810 align:start position:0%
okay so we did here uh F of 2 or
 

00:04:36.810 --> 00:04:39.649 align:start position:0%
okay so we did here uh F of 2 or
factorial<00:04:37.229><c> 2</c><00:04:37.409><c> that</c><00:04:37.740><c> means</c><00:04:37.860><c> N</c><00:04:38.099><c> equals</c><00:04:38.340><c> 2</c><00:04:38.460><c> we</c><00:04:39.360><c> did</c>

00:04:39.649 --> 00:04:39.659 align:start position:0%
factorial 2 that means N equals 2 we did
 

00:04:39.659 --> 00:04:43.879 align:start position:0%
factorial 2 that means N equals 2 we did
two<00:04:40.669><c> we</c><00:04:41.669><c> returned</c><00:04:42.030><c> two</c><00:04:42.389><c> times</c><00:04:42.840><c> the</c><00:04:43.530><c> factorial</c>

00:04:43.879 --> 00:04:43.889 align:start position:0%
two we returned two times the factorial
 

00:04:43.889 --> 00:04:47.089 align:start position:0%
two we returned two times the factorial
2<00:04:44.219><c> minus</c><00:04:44.460><c> 1</c><00:04:44.639><c> well</c><00:04:45.000><c> n</c><00:04:45.449><c> is</c><00:04:45.930><c> 2</c><00:04:46.259><c> so</c><00:04:46.469><c> we</c><00:04:46.500><c> do</c><00:04:46.650><c> 2</c><00:04:46.830><c> times</c>

00:04:47.089 --> 00:04:47.099 align:start position:0%
2 minus 1 well n is 2 so we do 2 times
 

00:04:47.099 --> 00:04:49.459 align:start position:0%
2 minus 1 well n is 2 so we do 2 times
and<00:04:47.789><c> we</c><00:04:47.849><c> know</c><00:04:47.939><c> what</c><00:04:48.060><c> factorial</c><00:04:48.659><c> of</c><00:04:48.780><c> 2</c><00:04:48.840><c> minus</c><00:04:49.319><c> 1</c>

00:04:49.459 --> 00:04:49.469 align:start position:0%
and we know what factorial of 2 minus 1
 

00:04:49.469 --> 00:04:51.589 align:start position:0%
and we know what factorial of 2 minus 1
is<00:04:49.530><c> because</c><00:04:50.520><c> it's</c><00:04:50.669><c> right</c><00:04:50.819><c> below</c><00:04:51.000><c> it</c><00:04:51.150><c> factorial</c>

00:04:51.589 --> 00:04:51.599 align:start position:0%
is because it's right below it factorial
 

00:04:51.599 --> 00:04:55.189 align:start position:0%
is because it's right below it factorial
of<00:04:51.629><c> 1</c><00:04:51.719><c> is</c><00:04:52.189><c> 1</c><00:04:53.189><c> times</c><00:04:53.610><c> 1</c><00:04:53.669><c> equals</c><00:04:54.000><c> 1</c><00:04:54.120><c> so</c><00:04:54.900><c> we</c><00:04:54.960><c> need</c><00:04:55.080><c> 2</c>

00:04:55.189 --> 00:04:55.199 align:start position:0%
of 1 is 1 times 1 equals 1 so we need 2
 

00:04:55.199 --> 00:04:58.550 align:start position:0%
of 1 is 1 times 1 equals 1 so we need 2
times<00:04:55.439><c> 1</c><00:04:55.590><c> equals</c><00:04:55.620><c> 2</c><00:04:56.689><c> and</c><00:04:57.689><c> now</c><00:04:57.840><c> we</c><00:04:57.870><c> come</c><00:04:58.229><c> back</c><00:04:58.259><c> up</c>

00:04:58.550 --> 00:04:58.560 align:start position:0%
times 1 equals 2 and now we come back up
 

00:04:58.560 --> 00:05:02.480 align:start position:0%
times 1 equals 2 and now we come back up
here<00:04:58.590><c> to</c><00:04:59.490><c> say</c><00:04:59.729><c> factorial</c><00:05:00.479><c> 3</c><00:05:00.960><c> well</c><00:05:01.770><c> that</c><00:05:02.280><c> means</c>

00:05:02.480 --> 00:05:02.490 align:start position:0%
here to say factorial 3 well that means
 

00:05:02.490 --> 00:05:05.540 align:start position:0%
here to say factorial 3 well that means
we<00:05:02.669><c> return</c><00:05:03.000><c> 3</c><00:05:03.509><c> times</c><00:05:03.870><c> factorial</c><00:05:04.379><c> 3</c><00:05:04.979><c> minus</c><00:05:05.310><c> 1</c>

00:05:05.540 --> 00:05:05.550 align:start position:0%
we return 3 times factorial 3 minus 1
 

00:05:05.550 --> 00:05:09.860 align:start position:0%
we return 3 times factorial 3 minus 1
well<00:05:06.300><c> okay</c><00:05:06.599><c> and</c><00:05:06.780><c> this</c><00:05:07.259><c> 3</c><00:05:07.500><c> so</c><00:05:07.830><c> we</c><00:05:08.129><c> do</c><00:05:08.569><c> 3</c><00:05:09.569><c> times</c>

00:05:09.860 --> 00:05:09.870 align:start position:0%
well okay and this 3 so we do 3 times
 

00:05:09.870 --> 00:05:12.170 align:start position:0%
well okay and this 3 so we do 3 times
the<00:05:10.080><c> factorial</c><00:05:10.500><c> of</c><00:05:10.800><c> 3</c><00:05:11.400><c> minus</c><00:05:11.669><c> 1</c><00:05:11.699><c> which</c><00:05:12.000><c> is</c><00:05:12.030><c> 2</c>

00:05:12.170 --> 00:05:12.180 align:start position:0%
the factorial of 3 minus 1 which is 2
 

00:05:12.180 --> 00:05:15.020 align:start position:0%
the factorial of 3 minus 1 which is 2
well<00:05:13.169><c> guess</c><00:05:13.349><c> what</c><00:05:13.530><c> we're</c><00:05:13.770><c> you</c><00:05:13.949><c> did</c><00:05:14.159><c> that</c><00:05:14.340><c> right</c>

00:05:15.020 --> 00:05:15.030 align:start position:0%
well guess what we're you did that right
 

00:05:15.030 --> 00:05:18.649 align:start position:0%
well guess what we're you did that right
here<00:05:15.680><c> all</c><00:05:16.680><c> right</c><00:05:16.860><c> and</c><00:05:17.009><c> that</c><00:05:17.280><c> means</c><00:05:17.479><c> that</c><00:05:18.479><c> the</c>

00:05:18.649 --> 00:05:18.659 align:start position:0%
here all right and that means that the
 

00:05:18.659 --> 00:05:21.680 align:start position:0%
here all right and that means that the
answer<00:05:18.810><c> for</c><00:05:19.319><c> factorial</c><00:05:20.039><c> of</c><00:05:20.400><c> 3</c><00:05:20.849><c> is</c><00:05:21.060><c> 6</c><00:05:21.360><c> which</c><00:05:21.659><c> is</c>

00:05:21.680 --> 00:05:21.690 align:start position:0%
answer for factorial of 3 is 6 which is
 

00:05:21.690 --> 00:05:23.839 align:start position:0%
answer for factorial of 3 is 6 which is
correct<00:05:22.080><c> this</c><00:05:22.680><c> is</c><00:05:22.800><c> 3</c><00:05:22.979><c> times</c><00:05:23.009><c> 2</c><00:05:23.219><c> times</c><00:05:23.279><c> 1</c>

00:05:23.839 --> 00:05:23.849 align:start position:0%
correct this is 3 times 2 times 1
 

00:05:23.849 --> 00:05:26.390 align:start position:0%
correct this is 3 times 2 times 1
okay<00:05:24.870><c> and</c><00:05:24.960><c> that's</c><00:05:25.229><c> actually</c><00:05:25.349><c> yeah</c><00:05:26.219><c> that's</c>

00:05:26.390 --> 00:05:26.400 align:start position:0%
okay and that's actually yeah that's
 

00:05:26.400 --> 00:05:27.890 align:start position:0%
okay and that's actually yeah that's
actually<00:05:26.729><c> kind</c><00:05:26.939><c> of</c><00:05:26.969><c> how</c><00:05:27.090><c> recursion</c><00:05:27.599><c> works</c><00:05:27.689><c> but</c>

00:05:27.890 --> 00:05:27.900 align:start position:0%
actually kind of how recursion works but
 

00:05:27.900 --> 00:05:30.320 align:start position:0%
actually kind of how recursion works but
I'll<00:05:28.080><c> go</c><00:05:28.259><c> over</c><00:05:28.289><c> a</c><00:05:28.800><c> little</c><00:05:29.400><c> bit</c><00:05:29.520><c> more</c><00:05:29.699><c> after</c>

00:05:30.320 --> 00:05:30.330 align:start position:0%
I'll go over a little bit more after
 

00:05:30.330 --> 00:05:32.659 align:start position:0%
I'll go over a little bit more after
this<00:05:30.599><c> so</c><00:05:31.590><c> where's</c><00:05:31.770><c> this</c><00:05:31.889><c> mean</c><00:05:32.159><c> for</c><00:05:32.310><c> the</c><00:05:32.370><c> time</c>

00:05:32.659 --> 00:05:32.669 align:start position:0%
this so where's this mean for the time
 

00:05:32.669 --> 00:05:35.140 align:start position:0%
this so where's this mean for the time
complexity<00:05:33.210><c> assess</c><00:05:33.539><c> us</c><00:05:33.690><c> what</c><00:05:33.960><c> we</c><00:05:34.080><c> care</c><00:05:34.379><c> about</c>

00:05:35.140 --> 00:05:35.150 align:start position:0%
complexity assess us what we care about
 

00:05:35.150 --> 00:05:37.519 align:start position:0%
complexity assess us what we care about
well<00:05:36.150><c> looks</c><00:05:36.870><c> like</c><00:05:36.900><c> how</c><00:05:37.050><c> many</c><00:05:37.169><c> times</c><00:05:37.379><c> we</c>

00:05:37.519 --> 00:05:37.529 align:start position:0%
well looks like how many times we
 

00:05:37.529 --> 00:05:40.519 align:start position:0%
well looks like how many times we
actually<00:05:37.650><c> called</c><00:05:38.159><c> factorial</c><00:05:39.080><c> well</c><00:05:40.080><c> we</c><00:05:40.139><c> did</c><00:05:40.289><c> it</c>

00:05:40.519 --> 00:05:40.529 align:start position:0%
actually called factorial well we did it
 

00:05:40.529 --> 00:05:43.700 align:start position:0%
actually called factorial well we did it
factorial<00:05:41.099><c> 3</c><00:05:41.659><c> really</c><00:05:42.659><c> went</c><00:05:43.080><c> through</c><00:05:43.169><c> it</c><00:05:43.440><c> three</c>

00:05:43.700 --> 00:05:43.710 align:start position:0%
factorial 3 really went through it three
 

00:05:43.710 --> 00:05:48.680 align:start position:0%
factorial 3 really went through it three
times<00:05:44.009><c> this</c><00:05:44.879><c> f</c><00:05:45.089><c> of</c><00:05:45.389><c> 0</c><00:05:46.879><c> we</c><00:05:47.879><c> so</c><00:05:48.270><c> the</c><00:05:48.389><c> cutoff</c><00:05:48.599><c> is</c>

00:05:48.680 --> 00:05:48.690 align:start position:0%
times this f of 0 we so the cutoff is
 

00:05:48.690 --> 00:05:51.170 align:start position:0%
times this f of 0 we so the cutoff is
right<00:05:48.930><c> there</c><00:05:49.110><c> f</c><00:05:49.259><c> of</c><00:05:49.409><c> 0</c><00:05:49.440><c> we</c><00:05:50.190><c> didn't</c><00:05:50.520><c> call</c>

00:05:51.170 --> 00:05:51.180 align:start position:0%
right there f of 0 we didn't call
 

00:05:51.180 --> 00:05:55.010 align:start position:0%
right there f of 0 we didn't call
factorial<00:05:51.750><c> again</c><00:05:52.020><c> whenever</c><00:05:52.979><c> N</c><00:05:53.370><c> equals</c><00:05:53.849><c> 0</c><00:05:54.150><c> we</c>

00:05:55.010 --> 00:05:55.020 align:start position:0%
factorial again whenever N equals 0 we
 

00:05:55.020 --> 00:05:57.890 align:start position:0%
factorial again whenever N equals 0 we
got<00:05:55.259><c> to</c><00:05:55.409><c> the</c><00:05:55.529><c> base</c><00:05:55.770><c> case</c><00:05:56.039><c> of</c><00:05:56.279><c> if</c><00:05:56.789><c> N</c><00:05:57.120><c> equals</c><00:05:57.690><c> 0</c>

00:05:57.890 --> 00:05:57.900 align:start position:0%
got to the base case of if N equals 0
 

00:05:57.900 --> 00:06:00.439 align:start position:0%
got to the base case of if N equals 0
which<00:05:58.379><c> it</c><00:05:58.589><c> did</c><00:05:58.830><c> we</c><00:05:59.009><c> just</c><00:05:59.159><c> returned</c><00:05:59.550><c> 1</c><00:05:59.819><c> we</c>

00:06:00.439 --> 00:06:00.449 align:start position:0%
which it did we just returned 1 we
 

00:06:00.449 --> 00:06:03.379 align:start position:0%
which it did we just returned 1 we
didn't<00:06:00.690><c> end</c><00:06:01.050><c> up</c><00:06:01.560><c> doing</c><00:06:01.849><c> return</c><00:06:02.849><c> n</c><00:06:03.089><c> times</c>

00:06:03.379 --> 00:06:03.389 align:start position:0%
didn't end up doing return n times
 

00:06:03.389 --> 00:06:05.749 align:start position:0%
didn't end up doing return n times
factorial<00:06:03.629><c> of</c><00:06:04.050><c> zero</c><00:06:04.229><c> minus</c><00:06:04.710><c> 1</c><00:06:04.889><c> for</c><00:06:05.580><c> stuff</c>

00:06:05.749 --> 00:06:05.759 align:start position:0%
factorial of zero minus 1 for stuff
 

00:06:05.759 --> 00:06:07.670 align:start position:0%
factorial of zero minus 1 for stuff
that'll<00:06:05.940><c> be</c><00:06:06.000><c> negative</c><00:06:06.240><c> anyways</c><00:06:06.779><c> and</c><00:06:06.990><c> so</c><00:06:07.589><c> we</c>

00:06:07.670 --> 00:06:07.680 align:start position:0%
that'll be negative anyways and so we
 

00:06:07.680 --> 00:06:10.869 align:start position:0%
that'll be negative anyways and so we
wouldn't<00:06:07.860><c> want</c><00:06:08.069><c> that</c><00:06:08.659><c> so</c><00:06:09.659><c> we</c><00:06:09.960><c> only</c><00:06:10.199><c> call</c><00:06:10.440><c> this</c>

00:06:10.869 --> 00:06:10.879 align:start position:0%
wouldn't want that so we only call this
 

00:06:10.879 --> 00:06:15.290 align:start position:0%
wouldn't want that so we only call this
3<00:06:11.879><c> times</c><00:06:12.150><c> which</c><00:06:12.509><c> was</c><00:06:12.779><c> the</c><00:06:13.620><c> input</c><00:06:13.919><c> size</c><00:06:14.520><c> right</c>

00:06:15.290 --> 00:06:15.300 align:start position:0%
3 times which was the input size right
 

00:06:15.300 --> 00:06:17.749 align:start position:0%
3 times which was the input size right
so<00:06:15.539><c> if</c><00:06:15.659><c> we</c><00:06:15.779><c> did</c><00:06:15.930><c> the</c><00:06:16.199><c> same</c><00:06:16.409><c> thing</c><00:06:16.710><c> for</c><00:06:16.740><c> you</c><00:06:17.669><c> know</c>

00:06:17.749 --> 00:06:17.759 align:start position:0%
so if we did the same thing for you know
 

00:06:17.759 --> 00:06:21.260 align:start position:0%
so if we did the same thing for you know
f<00:06:17.940><c> of</c><00:06:18.210><c> 5</c><00:06:18.479><c> and</c><00:06:18.930><c> then</c><00:06:19.830><c> you</c><00:06:20.219><c> know</c><00:06:20.310><c> we</c><00:06:20.430><c> call</c><00:06:20.610><c> F</c><00:06:20.789><c> of</c><00:06:20.940><c> 4</c>

00:06:21.260 --> 00:06:21.270 align:start position:0%
f of 5 and then you know we call F of 4
 

00:06:21.270 --> 00:06:23.149 align:start position:0%
f of 5 and then you know we call F of 4
factorial<00:06:21.900><c> of</c><00:06:22.050><c> 5</c><00:06:22.199><c> in</c><00:06:22.379><c> fact</c><00:06:22.529><c> Williford</c>

00:06:23.149 --> 00:06:23.159 align:start position:0%
factorial of 5 in fact Williford
 

00:06:23.159 --> 00:06:25.640 align:start position:0%
factorial of 5 in fact Williford
we<00:06:24.120><c> would</c><00:06:24.270><c> still</c><00:06:24.509><c> the</c><00:06:24.930><c> cut</c><00:06:25.139><c> off</c><00:06:25.229><c> would</c><00:06:25.409><c> still</c>

00:06:25.640 --> 00:06:25.650 align:start position:0%
we would still the cut off would still
 

00:06:25.650 --> 00:06:28.730 align:start position:0%
we would still the cut off would still
be<00:06:25.800><c> when</c><00:06:26.219><c> we</c><00:06:26.310><c> get</c><00:06:26.430><c> to</c><00:06:26.550><c> 0</c><00:06:27.199><c> so</c><00:06:28.199><c> can</c><00:06:28.529><c> you</c><00:06:28.589><c> guess</c>

00:06:28.730 --> 00:06:28.740 align:start position:0%
be when we get to 0 so can you guess
 

00:06:28.740 --> 00:06:30.980 align:start position:0%
be when we get to 0 so can you guess
with<00:06:28.919><c> the</c><00:06:29.069><c> tank</c><00:06:29.849><c> foxy</c><00:06:30.120><c> with</c><00:06:30.240><c> this</c><00:06:30.360><c> this</c><00:06:30.839><c> would</c>

00:06:30.980 --> 00:06:30.990 align:start position:0%
with the tank foxy with this this would
 

00:06:30.990 --> 00:06:33.239 align:start position:0%
with the tank foxy with this this would
be<00:06:31.729><c> well</c>

00:06:33.239 --> 00:06:33.249 align:start position:0%
be well
 

00:06:33.249 --> 00:06:35.699 align:start position:0%
be well
since<00:06:33.659><c> the</c><00:06:34.659><c> amount</c><00:06:34.809><c> of</c><00:06:34.899><c> times</c><00:06:35.079><c> we</c><00:06:35.199><c> go</c><00:06:35.409><c> through</c>

00:06:35.699 --> 00:06:35.709 align:start position:0%
since the amount of times we go through
 

00:06:35.709 --> 00:06:38.519 align:start position:0%
since the amount of times we go through
it<00:06:35.889><c> is</c><00:06:36.099><c> based</c><00:06:36.369><c> on</c><00:06:36.669><c> n</c><00:06:37.169><c> that</c><00:06:38.169><c> means</c><00:06:38.319><c> there's</c><00:06:38.469><c> a</c>

00:06:38.519 --> 00:06:38.529 align:start position:0%
it is based on n that means there's a
 

00:06:38.529 --> 00:06:41.969 align:start position:0%
it is based on n that means there's a
one-to-one<00:06:39.299><c> proportion</c><00:06:40.299><c> to</c><00:06:40.779><c> the</c><00:06:41.619><c> time</c><00:06:41.829><c> it</c>

00:06:41.969 --> 00:06:41.979 align:start position:0%
one-to-one proportion to the time it
 

00:06:41.979 --> 00:06:43.649 align:start position:0%
one-to-one proportion to the time it
takes<00:06:42.159><c> meaning</c><00:06:42.729><c> how</c><00:06:42.849><c> many</c><00:06:42.909><c> times</c><00:06:43.209><c> we</c><00:06:43.419><c> go</c>

00:06:43.649 --> 00:06:43.659 align:start position:0%
takes meaning how many times we go
 

00:06:43.659 --> 00:06:47.999 align:start position:0%
takes meaning how many times we go
through<00:06:43.689><c> it</c><00:06:44.879><c> to</c><00:06:45.879><c> the</c><00:06:46.299><c> size</c><00:06:46.539><c> of</c><00:06:46.629><c> the</c><00:06:46.749><c> input</c><00:06:47.009><c> all</c>

00:06:47.999 --> 00:06:48.009 align:start position:0%
through it to the size of the input all
 

00:06:48.009 --> 00:06:51.269 align:start position:0%
through it to the size of the input all
right<00:06:48.099><c> so</c><00:06:48.279><c> that</c><00:06:48.819><c> means</c><00:06:49.029><c> that</c><00:06:49.509><c> this</c><00:06:50.279><c> algorithm</c>

00:06:51.269 --> 00:06:51.279 align:start position:0%
right so that means that this algorithm
 

00:06:51.279 --> 00:06:57.079 align:start position:0%
right so that means that this algorithm
is<00:06:51.669><c> Big</c><00:06:52.449><c> O</c><00:06:52.569><c> of</c><00:06:52.599><c> n</c><00:06:52.979><c> okay</c><00:06:54.869><c> so</c><00:06:55.869><c> now</c><00:06:56.529><c> let's</c><00:06:56.709><c> go</c><00:06:56.829><c> over</c>

00:06:57.079 --> 00:06:57.089 align:start position:0%
is Big O of n okay so now let's go over
 

00:06:57.089 --> 00:07:01.439 align:start position:0%
is Big O of n okay so now let's go over
I<00:06:58.089><c> kind</c><00:06:58.539><c> of</c><00:06:58.569><c> have</c><00:06:58.719><c> a</c><00:06:58.749><c> decent</c><00:06:59.289><c> drawing</c><00:06:59.619><c> of</c><00:07:00.449><c> how</c>

00:07:01.439 --> 00:07:01.449 align:start position:0%
I kind of have a decent drawing of how
 

00:07:01.449 --> 00:07:03.809 align:start position:0%
I kind of have a decent drawing of how
like<00:07:01.719><c> you</c><00:07:01.869><c> go</c><00:07:02.319><c> down</c><00:07:02.529><c> and</c><00:07:03.279><c> then</c><00:07:03.399><c> come</c><00:07:03.429><c> back</c><00:07:03.669><c> up</c>

00:07:03.809 --> 00:07:03.819 align:start position:0%
like you go down and then come back up
 

00:07:03.819 --> 00:07:09.479 align:start position:0%
like you go down and then come back up
but<00:07:05.249><c> let's</c><00:07:06.249><c> go</c><00:07:06.369><c> over</c><00:07:07.379><c> recursion</c><00:07:08.379><c> so</c><00:07:09.219><c> you</c><00:07:09.309><c> kind</c>

00:07:09.479 --> 00:07:09.489 align:start position:0%
but let's go over recursion so you kind
 

00:07:09.489 --> 00:07:12.239 align:start position:0%
but let's go over recursion so you kind
of<00:07:09.549><c> get</c><00:07:10.089><c> a</c><00:07:10.119><c> little</c><00:07:10.269><c> bit</c><00:07:10.509><c> more</c><00:07:10.629><c> idea</c><00:07:11.049><c> of</c><00:07:11.289><c> how</c>

00:07:12.239 --> 00:07:12.249 align:start position:0%
of get a little bit more idea of how
 

00:07:12.249 --> 00:07:14.939 align:start position:0%
of get a little bit more idea of how
this<00:07:12.639><c> is</c><00:07:12.699><c> exactly</c><00:07:13.089><c> working</c><00:07:13.479><c> at</c><00:07:13.599><c> why</c><00:07:13.839><c> it's</c><00:07:14.769><c> Big</c>

00:07:14.939 --> 00:07:14.949 align:start position:0%
this is exactly working at why it's Big
 

00:07:14.949 --> 00:07:16.499 align:start position:0%
this is exactly working at why it's Big
O<00:07:15.039><c> of</c><00:07:15.069><c> n</c><00:07:15.249><c> understanding</c><00:07:15.999><c> what</c><00:07:16.089><c> workers</c><00:07:16.299><c> will</c>

00:07:16.499 --> 00:07:16.509 align:start position:0%
O of n understanding what workers will
 

00:07:16.509 --> 00:07:18.869 align:start position:0%
O of n understanding what workers will
help<00:07:16.629><c> a</c><00:07:16.659><c> little</c><00:07:16.719><c> bit</c><00:07:16.929><c> alright</c><00:07:17.199><c> okay</c><00:07:17.979><c> so</c><00:07:18.189><c> I</c><00:07:18.610><c> kind</c>

00:07:18.869 --> 00:07:18.879 align:start position:0%
help a little bit alright okay so I kind
 

00:07:18.879 --> 00:07:21.600 align:start position:0%
help a little bit alright okay so I kind
of<00:07:18.999><c> already</c><00:07:19.269><c> have</c><00:07:19.749><c> this</c><00:07:20.110><c> picture</c><00:07:20.409><c> here</c><00:07:20.889><c> that</c><00:07:21.579><c> I</c>

00:07:21.600 --> 00:07:21.610 align:start position:0%
of already have this picture here that I
 

00:07:21.610 --> 00:07:23.639 align:start position:0%
of already have this picture here that I
want<00:07:22.029><c> to</c><00:07:22.119><c> that</c><00:07:22.569><c> want</c><00:07:22.719><c> to</c><00:07:22.779><c> keep</c><00:07:22.959><c> here</c><00:07:23.199><c> to</c><00:07:23.499><c> help</c>

00:07:23.639 --> 00:07:23.649 align:start position:0%
want to that want to keep here to help
 

00:07:23.649 --> 00:07:25.009 align:start position:0%
want to that want to keep here to help
you<00:07:23.709><c> understand</c><00:07:24.099><c> all</c><00:07:24.309><c> right</c>

00:07:25.009 --> 00:07:25.019 align:start position:0%
you understand all right
 

00:07:25.019 --> 00:07:27.329 align:start position:0%
you understand all right
so<00:07:26.019><c> something</c><00:07:26.349><c> called</c><00:07:26.529><c> the</c><00:07:26.619><c> activation</c><00:07:26.889><c> stack</c>

00:07:27.329 --> 00:07:27.339 align:start position:0%
so something called the activation stack
 

00:07:27.339 --> 00:07:29.219 align:start position:0%
so something called the activation stack
which<00:07:28.059><c> has</c><00:07:28.299><c> eight</c><00:07:28.539><c> bunch</c><00:07:28.749><c> of</c><00:07:28.869><c> activate</c>

00:07:29.219 --> 00:07:29.229 align:start position:0%
which has eight bunch of activate
 

00:07:29.229 --> 00:07:33.119 align:start position:0%
which has eight bunch of activate
activation<00:07:29.829><c> records</c><00:07:30.279><c> if</c><00:07:30.519><c> you</c><00:07:30.969><c> understand</c><00:07:32.129><c> the</c>

00:07:33.119 --> 00:07:33.129 align:start position:0%
activation records if you understand the
 

00:07:33.129 --> 00:07:35.459 align:start position:0%
activation records if you understand the
stack<00:07:33.429><c> data</c><00:07:33.699><c> structure</c><00:07:34.199><c> basically</c><00:07:35.199><c> you</c><00:07:35.319><c> can</c>

00:07:35.459 --> 00:07:35.469 align:start position:0%
stack data structure basically you can
 

00:07:35.469 --> 00:07:38.519 align:start position:0%
stack data structure basically you can
push<00:07:35.709><c> things</c><00:07:35.859><c> on</c><00:07:36.099><c> to</c><00:07:36.249><c> it</c><00:07:36.399><c> and</c><00:07:36.549><c> pop</c><00:07:37.329><c> them</c><00:07:37.509><c> off</c><00:07:37.659><c> so</c>

00:07:38.519 --> 00:07:38.529 align:start position:0%
push things on to it and pop them off so
 

00:07:38.529 --> 00:07:41.009 align:start position:0%
push things on to it and pop them off so
when<00:07:39.489><c> you</c><00:07:39.579><c> push</c><00:07:39.849><c> them</c><00:07:39.999><c> onto</c><00:07:40.179><c> it</c><00:07:40.479><c> and</c><00:07:40.629><c> pop</c><00:07:40.869><c> them</c>

00:07:41.009 --> 00:07:41.019 align:start position:0%
when you push them onto it and pop them
 

00:07:41.019 --> 00:07:45.209 align:start position:0%
when you push them onto it and pop them
off<00:07:41.169><c> this</c><00:07:41.829><c> is</c><00:07:42.069><c> the</c><00:07:43.229><c> last</c><00:07:44.229><c> in</c><00:07:44.559><c> first</c><00:07:44.619><c> out</c>

00:07:45.209 --> 00:07:45.219 align:start position:0%
off this is the last in first out
 

00:07:45.219 --> 00:07:47.459 align:start position:0%
off this is the last in first out
meaning<00:07:45.909><c> you</c><00:07:46.479><c> know</c><00:07:46.569><c> LIFO</c><00:07:47.019><c> if</c><00:07:47.169><c> you've</c><00:07:47.289><c> heard</c><00:07:47.409><c> of</c>

00:07:47.459 --> 00:07:47.469 align:start position:0%
meaning you know LIFO if you've heard of
 

00:07:47.469 --> 00:07:49.259 align:start position:0%
meaning you know LIFO if you've heard of
that<00:07:47.529><c> so</c><00:07:48.279><c> let</c><00:07:48.549><c> me</c><00:07:48.610><c> just</c><00:07:48.729><c> draw</c><00:07:48.879><c> it</c><00:07:48.909><c> real</c><00:07:49.119><c> quick</c>

00:07:49.259 --> 00:07:49.269 align:start position:0%
that so let me just draw it real quick
 

00:07:49.269 --> 00:07:52.889 align:start position:0%
that so let me just draw it real quick
so<00:07:49.899><c> we</c><00:07:49.929><c> say</c><00:07:50.199><c> we</c><00:07:50.289><c> have</c><00:07:50.319><c> a</c><00:07:50.439><c> basic</c><00:07:50.860><c> stack</c><00:07:51.899><c> like</c>

00:07:52.889 --> 00:07:52.899 align:start position:0%
so we say we have a basic stack like
 

00:07:52.899 --> 00:07:54.509 align:start position:0%
so we say we have a basic stack like
this<00:07:53.369><c> okay</c>

00:07:54.509 --> 00:07:54.519 align:start position:0%
this okay
 

00:07:54.519 --> 00:07:56.309 align:start position:0%
this okay
well<00:07:54.699><c> we've</c><00:07:55.149><c> heard</c><00:07:55.299><c> what</c><00:07:55.539><c> we</c><00:07:55.689><c> call</c><00:07:55.899><c> first</c><00:07:56.169><c> we</c>

00:07:56.309 --> 00:07:56.319 align:start position:0%
well we've heard what we call first we
 

00:07:56.319 --> 00:07:58.859 align:start position:0%
well we've heard what we call first we
called<00:07:56.499><c> factorial</c><00:07:57.129><c> three</c><00:07:57.639><c> because</c><00:07:58.059><c> N</c><00:07:58.329><c> equals</c>

00:07:58.859 --> 00:07:58.869 align:start position:0%
called factorial three because N equals
 

00:07:58.869 --> 00:08:00.869 align:start position:0%
called factorial three because N equals
three<00:07:59.049><c> that's</c><00:07:59.169><c> we're</c><00:07:59.349><c> starting</c><00:07:59.679><c> with</c><00:07:59.799><c> so</c><00:08:00.579><c> we</c>

00:08:00.869 --> 00:08:00.879 align:start position:0%
three that's we're starting with so we
 

00:08:00.879 --> 00:08:03.179 align:start position:0%
three that's we're starting with so we
have<00:08:01.119><c> F</c><00:08:01.809><c> of</c><00:08:02.079><c> three</c><00:08:02.289><c> so</c><00:08:02.439><c> we're</c><00:08:02.589><c> gonna</c><00:08:02.979><c> do</c><00:08:03.069><c> is</c>

00:08:03.179 --> 00:08:03.189 align:start position:0%
have F of three so we're gonna do is
 

00:08:03.189 --> 00:08:05.549 align:start position:0%
have F of three so we're gonna do is
going<00:08:03.309><c> to</c><00:08:03.369><c> push</c><00:08:03.610><c> that</c><00:08:03.789><c> on</c><00:08:04.029><c> the</c><00:08:04.239><c> stack</c><00:08:04.559><c> that's</c>

00:08:05.549 --> 00:08:05.559 align:start position:0%
going to push that on the stack that's
 

00:08:05.559 --> 00:08:07.169 align:start position:0%
going to push that on the stack that's
an<00:08:05.709><c> activation</c><00:08:05.979><c> record</c><00:08:06.219><c> there's</c><00:08:06.939><c> a</c><00:08:07.029><c> lot</c>

00:08:07.169 --> 00:08:07.179 align:start position:0%
an activation record there's a lot
 

00:08:07.179 --> 00:08:08.519 align:start position:0%
an activation record there's a lot
there's<00:08:07.389><c> more</c><00:08:07.599><c> to</c><00:08:07.779><c> activation</c><00:08:08.169><c> records</c><00:08:08.439><c> I'm</c>

00:08:08.519 --> 00:08:08.529 align:start position:0%
there's more to activation records I'm
 

00:08:08.529 --> 00:08:10.049 align:start position:0%
there's more to activation records I'm
not<00:08:08.649><c> gonna</c><00:08:08.769><c> get</c><00:08:08.949><c> into</c><00:08:09.039><c> you</c><00:08:09.129><c> right</c><00:08:09.249><c> now</c><00:08:09.399><c> but</c>

00:08:10.049 --> 00:08:10.059 align:start position:0%
not gonna get into you right now but
 

00:08:10.059 --> 00:08:11.339 align:start position:0%
not gonna get into you right now but
that's<00:08:10.389><c> just</c><00:08:10.479><c> what</c><00:08:10.629><c> this</c><00:08:10.719><c> is</c><00:08:10.869><c> called</c><00:08:11.110><c> okay</c>

00:08:11.339 --> 00:08:11.349 align:start position:0%
that's just what this is called okay
 

00:08:11.349 --> 00:08:15.209 align:start position:0%
that's just what this is called okay
there's<00:08:11.529><c> more</c><00:08:11.860><c> to</c><00:08:12.039><c> it</c><00:08:12.069><c> but</c><00:08:13.379><c> okay</c><00:08:14.379><c> so</c><00:08:14.499><c> we</c><00:08:15.039><c> call</c>

00:08:15.209 --> 00:08:15.219 align:start position:0%
there's more to it but okay so we call
 

00:08:15.219 --> 00:08:17.850 align:start position:0%
there's more to it but okay so we call
this<00:08:15.309><c> a</c><00:08:15.429><c> three</c><00:08:15.669><c> and</c><00:08:15.999><c> then</c><00:08:16.869><c> when</c><00:08:17.589><c> we</c><00:08:17.679><c> get</c><00:08:17.799><c> down</c>

00:08:17.850 --> 00:08:17.860 align:start position:0%
this a three and then when we get down
 

00:08:17.860 --> 00:08:20.189 align:start position:0%
this a three and then when we get down
to<00:08:18.129><c> the</c><00:08:18.219><c> return</c><00:08:18.459><c> statement</c><00:08:18.669><c> we</c><00:08:19.389><c> return</c><00:08:20.079><c> it</c>

00:08:20.189 --> 00:08:20.199 align:start position:0%
to the return statement we return it
 

00:08:20.199 --> 00:08:21.869 align:start position:0%
to the return statement we return it
three<00:08:20.409><c> times</c><00:08:20.739><c> the</c><00:08:21.039><c> factorial</c><00:08:21.369><c> three</c><00:08:21.669><c> minus</c>

00:08:21.869 --> 00:08:21.879 align:start position:0%
three times the factorial three minus
 

00:08:21.879 --> 00:08:23.999 align:start position:0%
three times the factorial three minus
one<00:08:22.229><c> okay</c><00:08:23.229><c> then</c><00:08:23.439><c> we're</c><00:08:23.589><c> going</c><00:08:23.709><c> to</c><00:08:23.799><c> call</c><00:08:23.979><c> a</c>

00:08:23.999 --> 00:08:24.009 align:start position:0%
one okay then we're going to call a
 

00:08:24.009 --> 00:08:28.439 align:start position:0%
one okay then we're going to call a
factorial<00:08:24.489><c> of</c><00:08:24.669><c> 2</c><00:08:26.999><c> okay</c><00:08:27.999><c> and</c><00:08:28.179><c> we're</c><00:08:28.299><c> going</c><00:08:28.419><c> to</c>

00:08:28.439 --> 00:08:28.449 align:start position:0%
factorial of 2 okay and we're going to
 

00:08:28.449 --> 00:08:29.279 align:start position:0%
factorial of 2 okay and we're going to
push<00:08:28.689><c> that</c><00:08:28.719><c> on</c>

00:08:29.279 --> 00:08:29.289 align:start position:0%
push that on
 

00:08:29.289 --> 00:08:30.869 align:start position:0%
push that on
the<00:08:29.409><c> stack</c><00:08:29.680><c> or</c><00:08:29.710><c> the</c><00:08:29.949><c> activation</c><00:08:30.430><c> record</c><00:08:30.699><c> or</c>

00:08:30.869 --> 00:08:30.879 align:start position:0%
the stack or the activation record or
 

00:08:30.879 --> 00:08:32.879 align:start position:0%
the stack or the activation record or
activation<00:08:31.330><c> stack</c><00:08:31.629><c> and</c><00:08:31.899><c> so</c><00:08:32.440><c> this</c><00:08:32.560><c> is</c><00:08:32.649><c> another</c>

00:08:32.879 --> 00:08:32.889 align:start position:0%
activation stack and so this is another
 

00:08:32.889 --> 00:08:35.040 align:start position:0%
activation stack and so this is another
activation<00:08:33.339><c> record</c><00:08:33.639><c> okay</c><00:08:34.360><c> so</c><00:08:34.419><c> then</c><00:08:34.659><c> we</c><00:08:34.870><c> do</c><00:08:34.959><c> the</c>

00:08:35.040 --> 00:08:35.050 align:start position:0%
activation record okay so then we do the
 

00:08:35.050 --> 00:08:38.399 align:start position:0%
activation record okay so then we do the
same<00:08:35.229><c> thing</c><00:08:35.469><c> we</c><00:08:36.130><c> call</c><00:08:36.310><c> return</c><00:08:36.880><c> two</c><00:08:37.209><c> times</c><00:08:38.050><c> the</c>

00:08:38.399 --> 00:08:38.409 align:start position:0%
same thing we call return two times the
 

00:08:38.409 --> 00:08:41.069 align:start position:0%
same thing we call return two times the
factorial<00:08:38.680><c> 2</c><00:08:38.979><c> minus</c><00:08:39.190><c> 1</c><00:08:39.570><c> that's</c><00:08:40.570><c> the</c><00:08:40.719><c> factorial</c>

00:08:41.069 --> 00:08:41.079 align:start position:0%
factorial 2 minus 1 that's the factorial
 

00:08:41.079 --> 00:08:46.680 align:start position:0%
factorial 2 minus 1 that's the factorial
of<00:08:41.110><c> 1</c><00:08:41.199><c> so</c><00:08:42.039><c> we're</c><00:08:42.159><c> going</c><00:08:42.250><c> to</c><00:08:42.310><c> say</c><00:08:42.459><c> F</c><00:08:42.669><c> of</c><00:08:43.060><c> 1</c><00:08:45.690><c> okay</c>

00:08:46.680 --> 00:08:46.690 align:start position:0%
of 1 so we're going to say F of 1 okay
 

00:08:46.690 --> 00:08:51.420 align:start position:0%
of 1 so we're going to say F of 1 okay
and<00:08:46.959><c> then</c><00:08:47.889><c> here</c><00:08:48.670><c> we</c><00:08:48.940><c> have</c><00:08:48.970><c> factorial</c><00:08:49.810><c> 0</c><00:08:50.430><c> which</c>

00:08:51.420 --> 00:08:51.430 align:start position:0%
and then here we have factorial 0 which
 

00:08:51.430 --> 00:08:52.769 align:start position:0%
and then here we have factorial 0 which
remember<00:08:51.819><c> we</c><00:08:51.940><c> are</c><00:08:51.970><c> going</c><00:08:52.120><c> to</c><00:08:52.240><c> push</c><00:08:52.389><c> on</c><00:08:52.540><c> here</c>

00:08:52.769 --> 00:08:52.779 align:start position:0%
remember we are going to push on here
 

00:08:52.779 --> 00:08:54.660 align:start position:0%
remember we are going to push on here
even<00:08:53.199><c> though</c><00:08:53.350><c> we're</c><00:08:53.860><c> not</c><00:08:53.980><c> actually</c><00:08:54.190><c> calling</c>

00:08:54.660 --> 00:08:54.670 align:start position:0%
even though we're not actually calling
 

00:08:54.670 --> 00:08:57.990 align:start position:0%
even though we're not actually calling
factorial<00:08:55.000><c> again</c><00:08:55.389><c> but</c><00:08:56.110><c> but</c><00:08:56.860><c> we</c><00:08:57.220><c> still</c><00:08:57.430><c> call</c><00:08:57.970><c> it</c>

00:08:57.990 --> 00:08:58.000 align:start position:0%
factorial again but but we still call it
 

00:08:58.000 --> 00:09:01.230 align:start position:0%
factorial again but but we still call it
so<00:08:58.360><c> an</c><00:08:58.449><c> activation</c><00:08:58.750><c> record</c><00:08:58.990><c> and</c><00:08:59.699><c> so</c><00:09:00.699><c> and</c><00:09:00.910><c> what</c>

00:09:01.230 --> 00:09:01.240 align:start position:0%
so an activation record and so and what
 

00:09:01.240 --> 00:09:03.509 align:start position:0%
so an activation record and so and what
happens<00:09:01.569><c> is</c><00:09:01.750><c> when</c><00:09:02.079><c> we</c><00:09:02.259><c> push</c><00:09:03.130><c> this</c><00:09:03.250><c> on</c><00:09:03.430><c> the</c>

00:09:03.509 --> 00:09:03.519 align:start position:0%
happens is when we push this on the
 

00:09:03.519 --> 00:09:06.240 align:start position:0%
happens is when we push this on the
stack<00:09:03.839><c> we</c><00:09:04.839><c> go</c><00:09:05.110><c> through</c><00:09:05.470><c> it</c><00:09:05.589><c> and</c><00:09:05.829><c> we</c><00:09:05.889><c> say</c><00:09:06.069><c> oh</c>

00:09:06.240 --> 00:09:06.250 align:start position:0%
stack we go through it and we say oh
 

00:09:06.250 --> 00:09:09.660 align:start position:0%
stack we go through it and we say oh
well<00:09:06.850><c> it</c><00:09:07.300><c> hit</c><00:09:07.420><c> a</c><00:09:07.449><c> base</c><00:09:07.660><c> case</c><00:09:07.870><c> it</c><00:09:08.440><c> hit</c><00:09:08.709><c> a</c><00:09:08.740><c> if</c><00:09:09.220><c> N</c>

00:09:09.660 --> 00:09:09.670 align:start position:0%
well it hit a base case it hit a if N
 

00:09:09.670 --> 00:09:14.490 align:start position:0%
well it hit a base case it hit a if N
equals<00:09:09.730><c> 0</c><00:09:10.329><c> return</c><00:09:10.660><c> 1</c><00:09:11.009><c> which</c><00:09:13.079><c> which</c><00:09:14.079><c> is</c><00:09:14.259><c> means</c>

00:09:14.490 --> 00:09:14.500 align:start position:0%
equals 0 return 1 which which is means
 

00:09:14.500 --> 00:09:16.290 align:start position:0%
equals 0 return 1 which which is means
that<00:09:14.649><c> we're</c><00:09:14.769><c> done</c><00:09:14.949><c> pushing</c><00:09:15.910><c> anything</c><00:09:16.089><c> when</c>

00:09:16.290 --> 00:09:16.300 align:start position:0%
that we're done pushing anything when
 

00:09:16.300 --> 00:09:18.240 align:start position:0%
that we're done pushing anything when
he's<00:09:16.389><c> activation</c><00:09:16.959><c> stack</c><00:09:17.259><c> and</c><00:09:17.560><c> immediately</c>

00:09:18.240 --> 00:09:18.250 align:start position:0%
he's activation stack and immediately
 

00:09:18.250 --> 00:09:22.100 align:start position:0%
he's activation stack and immediately
we're<00:09:18.790><c> gonna</c><00:09:18.910><c> start</c><00:09:19.089><c> popping</c><00:09:19.329><c> things</c><00:09:19.690><c> off</c><00:09:20.819><c> so</c>

00:09:22.100 --> 00:09:22.110 align:start position:0%
we're gonna start popping things off so
 

00:09:22.110 --> 00:09:26.850 align:start position:0%
we're gonna start popping things off so
when<00:09:23.110><c> we</c><00:09:23.199><c> pop</c><00:09:24.160><c> off</c><00:09:24.690><c> s</c><00:09:25.690><c> of</c><00:09:25.930><c> 0</c><00:09:26.199><c> or</c><00:09:26.380><c> factorial</c><00:09:26.829><c> of</c>

00:09:26.850 --> 00:09:26.860 align:start position:0%
when we pop off s of 0 or factorial of
 

00:09:26.860 --> 00:09:30.509 align:start position:0%
when we pop off s of 0 or factorial of
zero<00:09:27.660><c> well</c><00:09:28.829><c> we</c><00:09:29.829><c> could</c><00:09:30.009><c> we</c><00:09:30.100><c> go</c><00:09:30.220><c> through</c><00:09:30.430><c> the</c>

00:09:30.509 --> 00:09:30.519 align:start position:0%
zero well we could we go through the
 

00:09:30.519 --> 00:09:32.280 align:start position:0%
zero well we could we go through the
algorithm<00:09:30.910><c> here</c><00:09:31.000><c> and</c><00:09:31.300><c> 0</c><00:09:31.600><c> equals</c><00:09:31.870><c> 0</c><00:09:31.899><c> so</c><00:09:32.199><c> we</c>

00:09:32.280 --> 00:09:32.290 align:start position:0%
algorithm here and 0 equals 0 so we
 

00:09:32.290 --> 00:09:36.840 align:start position:0%
algorithm here and 0 equals 0 so we
return<00:09:32.620><c> 1</c><00:09:32.949><c> which</c><00:09:33.579><c> is</c><00:09:33.760><c> what</c><00:09:34.000><c> we</c><00:09:34.120><c> did</c><00:09:35.819><c> which</c><00:09:36.819><c> is</c>

00:09:36.840 --> 00:09:36.850 align:start position:0%
return 1 which is what we did which is
 

00:09:36.850 --> 00:09:37.920 align:start position:0%
return 1 which is what we did which is
what<00:09:37.060><c> we</c><00:09:37.149><c> did</c><00:09:37.269><c> right</c><00:09:37.480><c> here</c>

00:09:37.920 --> 00:09:37.930 align:start position:0%
what we did right here
 

00:09:37.930 --> 00:09:42.660 align:start position:0%
what we did right here
we<00:09:38.019><c> returned</c><00:09:38.380><c> 1</c><00:09:39.060><c> ok</c><00:09:40.060><c> so</c><00:09:40.779><c> then</c><00:09:40.959><c> so</c><00:09:41.500><c> then</c><00:09:41.670><c> that's</c>

00:09:42.660 --> 00:09:42.670 align:start position:0%
we returned 1 ok so then so then that's
 

00:09:42.670 --> 00:09:47.009 align:start position:0%
we returned 1 ok so then so then that's
gone<00:09:42.970><c> let</c><00:09:43.870><c> me</c><00:09:43.930><c> get</c><00:09:44.050><c> another</c><00:09:44.110><c> color</c><00:09:44.910><c> quick</c><00:09:46.019><c> get</c>

00:09:47.009 --> 00:09:47.019 align:start position:0%
gone let me get another color quick get
 

00:09:47.019 --> 00:09:50.550 align:start position:0%
gone let me get another color quick get
black<00:09:47.290><c> so</c><00:09:48.130><c> now</c><00:09:48.160><c> that's</c><00:09:48.459><c> gone</c><00:09:49.079><c> so</c><00:09:50.079><c> then</c><00:09:50.290><c> we</c><00:09:50.380><c> have</c>

00:09:50.550 --> 00:09:50.560 align:start position:0%
black so now that's gone so then we have
 

00:09:50.560 --> 00:09:54.780 align:start position:0%
black so now that's gone so then we have
F<00:09:50.829><c> then</c><00:09:51.190><c> we're</c><00:09:51.310><c> a</c><00:09:51.370><c> pop</c><00:09:51.610><c> off</c><00:09:51.819><c> F</c><00:09:52.120><c> of</c><00:09:52.420><c> 1</c><00:09:53.250><c> which</c><00:09:54.250><c> we</c>

00:09:54.780 --> 00:09:54.790 align:start position:0%
F then we're a pop off F of 1 which we
 

00:09:54.790 --> 00:09:57.030 align:start position:0%
F then we're a pop off F of 1 which we
already<00:09:55.149><c> did</c><00:09:55.480><c> up</c><00:09:55.600><c> here</c><00:09:55.630><c> so</c><00:09:55.990><c> we</c><00:09:56.170><c> had</c><00:09:56.260><c> F</c><00:09:56.440><c> of</c><00:09:56.560><c> 1</c><00:09:56.769><c> is</c>

00:09:57.030 --> 00:09:57.040 align:start position:0%
already did up here so we had F of 1 is
 

00:09:57.040 --> 00:10:01.879 align:start position:0%
already did up here so we had F of 1 is
1<00:09:57.430><c> times</c><00:09:58.029><c> 1</c><00:09:58.120><c> equals</c><00:09:58.510><c> 1</c><00:09:58.690><c> okay</c><00:09:59.350><c> why</c><00:09:59.680><c> is</c><00:09:59.740><c> that</c><00:10:00.779><c> well</c>

00:10:01.879 --> 00:10:01.889 align:start position:0%
1 times 1 equals 1 okay why is that well
 

00:10:01.889 --> 00:10:05.730 align:start position:0%
1 times 1 equals 1 okay why is that well
we're<00:10:02.889><c> essentially</c><00:10:03.490><c> saying</c><00:10:04.529><c> we're</c><00:10:05.529><c> saying</c>

00:10:05.730 --> 00:10:05.740 align:start position:0%
we're essentially saying we're saying
 

00:10:05.740 --> 00:10:15.329 align:start position:0%
we're essentially saying we're saying
return<00:10:06.720><c> 1</c><00:10:08.010><c> times</c><00:10:10.019><c> F</c><00:10:11.019><c> of</c><00:10:11.860><c> 0</c><00:10:13.470><c> which</c><00:10:14.470><c> is</c><00:10:14.500><c> which</c><00:10:15.160><c> we</c>

00:10:15.329 --> 00:10:15.339 align:start position:0%
return 1 times F of 0 which is which we
 

00:10:15.339 --> 00:10:17.280 align:start position:0%
return 1 times F of 0 which is which we
know<00:10:15.519><c> if</c><00:10:15.699><c> a</c><00:10:15.790><c> zero</c><00:10:16.029><c> we</c><00:10:16.600><c> just</c><00:10:16.750><c> did</c><00:10:16.930><c> that</c>

00:10:17.280 --> 00:10:17.290 align:start position:0%
know if a zero we just did that
 

00:10:17.290 --> 00:10:22.530 align:start position:0%
know if a zero we just did that
it's<00:10:17.649><c> 1</c><00:10:17.920><c> so</c><00:10:18.310><c> we</c><00:10:18.399><c> do</c><00:10:18.519><c> 1</c><00:10:18.760><c> times</c><00:10:19.000><c> 1</c><00:10:19.769><c> equals</c><00:10:20.769><c> 1</c><00:10:21.540><c> and</c>

00:10:22.530 --> 00:10:22.540 align:start position:0%
it's 1 so we do 1 times 1 equals 1 and
 

00:10:22.540 --> 00:10:26.910 align:start position:0%
it's 1 so we do 1 times 1 equals 1 and
then<00:10:23.579><c> so</c><00:10:24.579><c> that</c><00:10:24.939><c> means</c><00:10:25.120><c> we're</c><00:10:26.019><c> done</c><00:10:26.259><c> here</c>

00:10:26.910 --> 00:10:26.920 align:start position:0%
then so that means we're done here
 

00:10:26.920 --> 00:10:29.699 align:start position:0%
then so that means we're done here
and<00:10:27.009><c> I</c><00:10:27.069><c> kind</c><00:10:27.279><c> of</c><00:10:27.310><c> already</c><00:10:27.490><c> did</c><00:10:27.790><c> this</c><00:10:28.259><c> F</c><00:10:29.259><c> of</c><00:10:29.500><c> one</c>

00:10:29.699 --> 00:10:29.709 align:start position:0%
and I kind of already did this F of one
 

00:10:29.709 --> 00:10:32.519 align:start position:0%
and I kind of already did this F of one
with<00:10:29.980><c> the</c><00:10:30.069><c> arrow</c><00:10:30.250><c> to</c><00:10:30.670><c> 1</c><00:10:30.850><c> times</c><00:10:31.029><c> 1</c><00:10:31.209><c> equals</c><00:10:31.420><c> 1</c><00:10:31.569><c> so</c>

00:10:32.519 --> 00:10:32.529 align:start position:0%
with the arrow to 1 times 1 equals 1 so
 

00:10:32.529 --> 00:10:34.679 align:start position:0%
with the arrow to 1 times 1 equals 1 so
that's<00:10:32.769><c> what</c><00:10:32.889><c> we're</c><00:10:33.009><c> doing</c><00:10:33.250><c> okay</c><00:10:33.399><c> and</c><00:10:33.790><c> then</c><00:10:34.120><c> we</c>

00:10:34.679 --> 00:10:34.689 align:start position:0%
that's what we're doing okay and then we
 

00:10:34.689 --> 00:10:40.020 align:start position:0%
that's what we're doing okay and then we
pop<00:10:34.959><c> off</c><00:10:35.459><c> factorial</c><00:10:36.459><c> 2</c><00:10:36.819><c> and</c><00:10:38.430><c> we</c><00:10:39.430><c> return</c>

00:10:40.020 --> 00:10:40.030 align:start position:0%
pop off factorial 2 and we return
 

00:10:40.030 --> 00:10:43.710 align:start position:0%
pop off factorial 2 and we return
two<00:10:40.270><c> times</c><00:10:40.810><c> the</c><00:10:41.560><c> factorial</c><00:10:41.950><c> 2</c><00:10:42.250><c> minus</c><00:10:42.490><c> 1</c><00:10:42.720><c> which</c>

00:10:43.710 --> 00:10:43.720 align:start position:0%
two times the factorial 2 minus 1 which
 

00:10:43.720 --> 00:10:45.990 align:start position:0%
two times the factorial 2 minus 1 which
is<00:10:44.380><c> already</c><00:10:44.980><c> done</c><00:10:45.070><c> up</c><00:10:45.340><c> here</c><00:10:45.370><c> so</c><00:10:45.700><c> we</c><00:10:45.760><c> say</c><00:10:45.940><c> 2</c>

00:10:45.990 --> 00:10:46.000 align:start position:0%
is already done up here so we say 2
 

00:10:46.000 --> 00:10:48.960 align:start position:0%
is already done up here so we say 2
times<00:10:46.750><c> the</c><00:10:47.440><c> factorial</c><00:10:47.710><c> 2</c><00:10:47.980><c> must</c><00:10:48.160><c> 1</c><00:10:48.370><c> which</c><00:10:48.790><c> is</c>

00:10:48.960 --> 00:10:48.970 align:start position:0%
times the factorial 2 must 1 which is
 

00:10:48.970 --> 00:10:51.900 align:start position:0%
times the factorial 2 must 1 which is
the<00:10:49.090><c> factorial</c><00:10:49.330><c> of</c><00:10:49.540><c> 1</c><00:10:50.490><c> which</c><00:10:51.490><c> we</c><00:10:51.640><c> already</c><00:10:51.760><c> know</c>

00:10:51.900 --> 00:10:51.910 align:start position:0%
the factorial of 1 which we already know
 

00:10:51.910 --> 00:10:54.510 align:start position:0%
the factorial of 1 which we already know
the<00:10:52.000><c> answer</c><00:10:52.650><c> we</c><00:10:53.650><c> just</c><00:10:53.680><c> did</c><00:10:53.980><c> it</c><00:10:54.100><c> and</c><00:10:54.250><c> it's</c>

00:10:54.510 --> 00:10:54.520 align:start position:0%
the answer we just did it and it's
 

00:10:54.520 --> 00:10:57.710 align:start position:0%
the answer we just did it and it's
equals<00:10:54.940><c> 1</c><00:10:55.150><c> so</c><00:10:55.600><c> we</c><00:10:55.690><c> say</c><00:10:55.840><c> 2</c><00:10:55.900><c> times</c><00:10:56.260><c> 1</c><00:10:56.620><c> equals</c><00:10:56.920><c> 2</c>

00:10:57.710 --> 00:10:57.720 align:start position:0%
equals 1 so we say 2 times 1 equals 2
 

00:10:57.720 --> 00:11:00.240 align:start position:0%
equals 1 so we say 2 times 1 equals 2
all<00:10:58.720><c> right</c><00:10:58.870><c> then</c><00:10:59.080><c> we</c><00:10:59.170><c> got</c><00:10:59.320><c> one</c><00:10:59.650><c> more</c><00:10:59.920><c> to</c><00:11:00.040><c> pop</c>

00:11:00.240 --> 00:11:00.250 align:start position:0%
all right then we got one more to pop
 

00:11:00.250 --> 00:11:02.790 align:start position:0%
all right then we got one more to pop
off<00:11:00.460><c> the</c><00:11:00.640><c> activation</c><00:11:01.180><c> stack</c><00:11:01.420><c> and</c><00:11:01.720><c> this</c><00:11:02.710><c> is</c><00:11:02.770><c> a</c>

00:11:02.790 --> 00:11:02.800 align:start position:0%
off the activation stack and this is a
 

00:11:02.800 --> 00:11:04.560 align:start position:0%
off the activation stack and this is a
last<00:11:02.980><c> activation</c><00:11:03.580><c> record</c><00:11:03.850><c> its</c><00:11:04.120><c> factorial</c><00:11:04.540><c> of</c>

00:11:04.560 --> 00:11:04.570 align:start position:0%
last activation record its factorial of
 

00:11:04.570 --> 00:11:10.650 align:start position:0%
last activation record its factorial of
3<00:11:04.930><c> so</c><00:11:07.620><c> factorial</c><00:11:08.620><c> of</c><00:11:08.650><c> 3</c><00:11:08.920><c> is</c><00:11:09.280><c> it</c><00:11:10.270><c> says</c><00:11:10.450><c> right</c>

00:11:10.650 --> 00:11:10.660 align:start position:0%
3 so factorial of 3 is it says right
 

00:11:10.660 --> 00:11:13.140 align:start position:0%
3 so factorial of 3 is it says right
down<00:11:10.810><c> there</c><00:11:11.020><c> in</c><00:11:11.500><c> the</c><00:11:11.620><c> algorithm</c><00:11:11.950><c> we</c><00:11:12.310><c> return</c><00:11:12.640><c> 3</c>

00:11:13.140 --> 00:11:13.150 align:start position:0%
down there in the algorithm we return 3
 

00:11:13.150 --> 00:11:16.200 align:start position:0%
down there in the algorithm we return 3
times<00:11:13.660><c> the</c><00:11:14.200><c> factorial</c><00:11:14.560><c> 3</c><00:11:14.950><c> minus</c><00:11:15.190><c> 1</c><00:11:15.460><c> which</c><00:11:16.180><c> is</c>

00:11:16.200 --> 00:11:16.210 align:start position:0%
times the factorial 3 minus 1 which is
 

00:11:16.210 --> 00:11:19.980 align:start position:0%
times the factorial 3 minus 1 which is
factorial<00:11:16.720><c> 2</c><00:11:16.900><c> so</c><00:11:17.970><c> just</c><00:11:18.970><c> like</c><00:11:19.210><c> up</c><00:11:19.360><c> here</c><00:11:19.570><c> a</c><00:11:19.600><c> final</c>

00:11:19.980 --> 00:11:19.990 align:start position:0%
factorial 2 so just like up here a final
 

00:11:19.990 --> 00:11:23.700 align:start position:0%
factorial 2 so just like up here a final
answer<00:11:20.310><c> factorial</c><00:11:21.310><c> 3</c><00:11:21.610><c> the</c><00:11:22.030><c> arrow</c><00:11:22.240><c> is</c><00:11:22.630><c> 3</c><00:11:23.290><c> times</c>

00:11:23.700 --> 00:11:23.710 align:start position:0%
answer factorial 3 the arrow is 3 times
 

00:11:23.710 --> 00:11:27.960 align:start position:0%
answer factorial 3 the arrow is 3 times
the<00:11:24.330><c> factorial</c><00:11:25.330><c> 2</c><00:11:25.720><c> which</c><00:11:26.590><c> is</c><00:11:26.980><c> 2</c><00:11:27.460><c> because</c><00:11:27.880><c> we</c>

00:11:27.960 --> 00:11:27.970 align:start position:0%
the factorial 2 which is 2 because we
 

00:11:27.970 --> 00:11:30.270 align:start position:0%
the factorial 2 which is 2 because we
already<00:11:28.090><c> did</c><00:11:28.240><c> the</c><00:11:28.360><c> computation</c><00:11:28.930><c> just</c><00:11:29.860><c> in</c><00:11:30.190><c> the</c>

00:11:30.270 --> 00:11:30.280 align:start position:0%
already did the computation just in the
 

00:11:30.280 --> 00:11:32.730 align:start position:0%
already did the computation just in the
last<00:11:30.430><c> Popoff</c><00:11:30.940><c> and</c><00:11:31.240><c> so</c><00:11:32.110><c> that</c><00:11:32.230><c> means</c><00:11:32.350><c> our</c><00:11:32.590><c> answer</c>

00:11:32.730 --> 00:11:32.740 align:start position:0%
last Popoff and so that means our answer
 

00:11:32.740 --> 00:11:37.170 align:start position:0%
last Popoff and so that means our answer
is<00:11:33.010><c> 6</c><00:11:33.280><c> and</c><00:11:34.000><c> that</c><00:11:34.120><c> means</c><00:11:34.300><c> we</c><00:11:34.510><c> are</c><00:11:34.540><c> done</c><00:11:35.940><c> so</c><00:11:36.940><c> there</c>

00:11:37.170 --> 00:11:37.180 align:start position:0%
is 6 and that means we are done so there
 

00:11:37.180 --> 00:11:40.230 align:start position:0%
is 6 and that means we are done so there
are<00:11:37.330><c> 4</c><00:11:37.630><c> items</c><00:11:37.870><c> we</c><00:11:38.590><c> pushed</c><00:11:38.980><c> a</c><00:11:39.130><c> total</c><00:11:39.370><c> of</c><00:11:39.460><c> 4</c><00:11:39.820><c> items</c>

00:11:40.230 --> 00:11:40.240 align:start position:0%
are 4 items we pushed a total of 4 items
 

00:11:40.240 --> 00:11:43.950 align:start position:0%
are 4 items we pushed a total of 4 items
on<00:11:40.630><c> to</c><00:11:40.960><c> the</c><00:11:41.050><c> activation</c><00:11:41.290><c> stack</c><00:11:42.570><c> however</c><00:11:43.570><c> we</c>

00:11:43.950 --> 00:11:43.960 align:start position:0%
on to the activation stack however we
 

00:11:43.960 --> 00:11:49.230 align:start position:0%
on to the activation stack however we
only<00:11:43.990><c> called</c><00:11:46.260><c> we</c><00:11:47.260><c> called</c><00:11:47.560><c> factorial</c><00:11:48.040><c> 4</c><00:11:48.970><c> times</c>

00:11:49.230 --> 00:11:49.240 align:start position:0%
only called we called factorial 4 times
 

00:11:49.240 --> 00:11:51.300 align:start position:0%
only called we called factorial 4 times
right<00:11:49.870><c> we</c><00:11:50.050><c> actually</c><00:11:50.140><c> went</c><00:11:50.740><c> through</c><00:11:50.950><c> the</c>

00:11:51.300 --> 00:11:51.310 align:start position:0%
right we actually went through the
 

00:11:51.310 --> 00:11:53.910 align:start position:0%
right we actually went through the
algorithm<00:11:51.520><c> four</c><00:11:51.850><c> times</c><00:11:52.060><c> but</c><00:11:52.540><c> recursively</c><00:11:53.350><c> we</c>

00:11:53.910 --> 00:11:53.920 align:start position:0%
algorithm four times but recursively we
 

00:11:53.920 --> 00:11:56.430 align:start position:0%
algorithm four times but recursively we
only<00:11:54.070><c> call</c><00:11:54.310><c> it</c><00:11:54.460><c> three</c><00:11:54.670><c> times</c><00:11:55.170><c> because</c><00:11:56.170><c> we</c><00:11:56.290><c> hit</c>

00:11:56.430 --> 00:11:56.440 align:start position:0%
only call it three times because we hit
 

00:11:56.440 --> 00:12:00.900 align:start position:0%
only call it three times because we hit
the<00:11:56.530><c> base</c><00:11:56.740><c> case</c><00:11:57.070><c> on</c><00:11:57.370><c> factorial</c><00:11:58.240><c> 0</c><00:11:58.380><c> so</c><00:11:59.380><c> the</c><00:11:59.950><c> so</c>

00:12:00.900 --> 00:12:00.910 align:start position:0%
the base case on factorial 0 so the so
 

00:12:00.910 --> 00:12:02.970 align:start position:0%
the base case on factorial 0 so the so
this<00:12:01.089><c> is</c><00:12:01.180><c> why</c><00:12:01.620><c> hopefully</c><00:12:02.620><c> that</c><00:12:02.740><c> helped</c><00:12:02.890><c> you</c>

00:12:02.970 --> 00:12:02.980 align:start position:0%
this is why hopefully that helped you
 

00:12:02.980 --> 00:12:04.350 align:start position:0%
this is why hopefully that helped you
kind<00:12:03.190><c> of</c><00:12:03.250><c> first</c><00:12:03.430><c> off</c><00:12:03.580><c> kind</c><00:12:03.850><c> of</c><00:12:03.910><c> understood</c><00:12:04.180><c> the</c>

00:12:04.350 --> 00:12:04.360 align:start position:0%
kind of first off kind of understood the
 

00:12:04.360 --> 00:12:05.790 align:start position:0%
kind of first off kind of understood the
recursion<00:12:04.600><c> a</c><00:12:04.839><c> little</c><00:12:05.080><c> bit</c><00:12:05.170><c> more</c><00:12:05.350><c> because</c>

00:12:05.790 --> 00:12:05.800 align:start position:0%
recursion a little bit more because
 

00:12:05.800 --> 00:12:08.190 align:start position:0%
recursion a little bit more because
that's<00:12:06.100><c> that's</c><00:12:06.760><c> how</c><00:12:07.000><c> it</c><00:12:07.089><c> works</c><00:12:07.300><c> there's</c><00:12:08.080><c> an</c>

00:12:08.190 --> 00:12:08.200 align:start position:0%
that's that's how it works there's an
 

00:12:08.200 --> 00:12:11.310 align:start position:0%
that's that's how it works there's an
activation<00:12:08.530><c> stack</c><00:12:09.510><c> you</c><00:12:10.510><c> put</c><00:12:10.810><c> in</c><00:12:10.900><c> the</c><00:12:10.990><c> first</c>

00:12:11.310 --> 00:12:11.320 align:start position:0%
activation stack you put in the first
 

00:12:11.320 --> 00:12:13.860 align:start position:0%
activation stack you put in the first
and<00:12:12.010><c> whatever</c><00:12:12.310><c> n</c><00:12:12.700><c> is</c><00:12:13.300><c> or</c><00:12:13.510><c> whatever</c><00:12:13.780><c> you're</c>

00:12:13.860 --> 00:12:13.870 align:start position:0%
and whatever n is or whatever you're
 

00:12:13.870 --> 00:12:15.870 align:start position:0%
and whatever n is or whatever you're
doing<00:12:14.050><c> recursively</c><00:12:14.530><c> you</c><00:12:15.160><c> push</c><00:12:15.400><c> that</c><00:12:15.520><c> onto</c><00:12:15.670><c> the</c>

00:12:15.870 --> 00:12:15.880 align:start position:0%
doing recursively you push that onto the
 

00:12:15.880 --> 00:12:18.060 align:start position:0%
doing recursively you push that onto the
stack<00:12:16.210><c> then</c><00:12:16.870><c> you</c><00:12:16.960><c> keep</c><00:12:17.230><c> pushing</c><00:12:17.470><c> until</c><00:12:17.860><c> you</c>

00:12:18.060 --> 00:12:18.070 align:start position:0%
stack then you keep pushing until you
 

00:12:18.070 --> 00:12:19.470 align:start position:0%
stack then you keep pushing until you
hit<00:12:18.190><c> a</c><00:12:18.250><c> base</c><00:12:18.490><c> case</c><00:12:18.730><c> and</c><00:12:19.000><c> then</c><00:12:19.180><c> you</c><00:12:19.240><c> just</c><00:12:19.360><c> start</c>

00:12:19.470 --> 00:12:19.480 align:start position:0%
hit a base case and then you just start
 

00:12:19.480 --> 00:12:21.810 align:start position:0%
hit a base case and then you just start
popping<00:12:19.690><c> everything</c><00:12:20.020><c> off</c><00:12:20.290><c> ok</c><00:12:20.710><c> that's</c><00:12:20.980><c> that's</c>

00:12:21.810 --> 00:12:21.820 align:start position:0%
popping everything off ok that's that's
 

00:12:21.820 --> 00:12:23.310 align:start position:0%
popping everything off ok that's that's
how<00:12:22.120><c> that's</c><00:12:22.540><c> what</c><00:12:22.690><c> recursion</c><00:12:22.990><c> works</c><00:12:23.080><c> the</c><00:12:23.200><c> way</c>

00:12:23.310 --> 00:12:23.320 align:start position:0%
how that's what recursion works the way
 

00:12:23.320 --> 00:12:25.020 align:start position:0%
how that's what recursion works the way
it<00:12:23.380><c> does</c><00:12:23.410><c> all</c><00:12:24.190><c> right</c><00:12:24.280><c> in</c><00:12:24.400><c> there</c><00:12:24.550><c> the</c><00:12:24.670><c> math</c><00:12:24.850><c> I</c>

00:12:25.020 --> 00:12:25.030 align:start position:0%
it does all right in there the math I
 

00:12:25.030 --> 00:12:28.020 align:start position:0%
it does all right in there the math I
kinds<00:12:25.240><c> up</c><00:12:25.330><c> here</c><00:12:25.510><c> in</c><00:12:25.630><c> the</c><00:12:25.750><c> red</c><00:12:25.960><c> ink</c><00:12:26.290><c> that's</c><00:12:27.160><c> just</c>

00:12:28.020 --> 00:12:28.030 align:start position:0%
kinds up here in the red ink that's just
 

00:12:28.030 --> 00:12:29.820 align:start position:0%
kinds up here in the red ink that's just
popping<00:12:28.480><c> things</c><00:12:28.630><c> off</c><00:12:28.870><c> and</c><00:12:29.320><c> that's</c><00:12:29.620><c> what</c><00:12:29.740><c> it</c>

00:12:29.820 --> 00:12:29.830 align:start position:0%
popping things off and that's what it
 

00:12:29.830 --> 00:12:30.120 align:start position:0%
popping things off and that's what it
means

00:12:30.120 --> 00:12:30.130 align:start position:0%
means
 

00:12:30.130 --> 00:12:36.930 align:start position:0%
means
all<00:12:30.190><c> right</c><00:12:31.800><c> so</c><00:12:33.300><c> so</c><00:12:34.300><c> that's</c><00:12:34.480><c> why</c><00:12:34.600><c> the</c><00:12:35.290><c> the</c><00:12:35.940><c> time</c>

00:12:36.930 --> 00:12:36.940 align:start position:0%
all right so so that's why the the time
 

00:12:36.940 --> 00:12:38.790 align:start position:0%
all right so so that's why the the time
complexity<00:12:37.390><c> is</c><00:12:37.510><c> big</c><00:12:37.810><c> of</c><00:12:37.960><c> n</c><00:12:38.110><c> because</c><00:12:38.530><c> just</c>

00:12:38.790 --> 00:12:38.800 align:start position:0%
complexity is big of n because just
 

00:12:38.800 --> 00:12:41.490 align:start position:0%
complexity is big of n because just
we're<00:12:39.400><c> gonna</c><00:12:39.780><c> go</c><00:12:40.780><c> through</c><00:12:41.170><c> we're</c><00:12:41.440><c> gonna</c>

00:12:41.490 --> 00:12:41.500 align:start position:0%
we're gonna go through we're gonna
 

00:12:41.500 --> 00:12:43.560 align:start position:0%
we're gonna go through we're gonna
personally<00:12:41.920><c> call</c><00:12:42.100><c> a</c><00:12:42.130><c> factorial</c><00:12:42.700><c> that</c><00:12:43.300><c> many</c>

00:12:43.560 --> 00:12:43.570 align:start position:0%
personally call a factorial that many
 

00:12:43.570 --> 00:12:46.150 align:start position:0%
personally call a factorial that many
times<00:12:43.920><c> okay</c>

00:12:46.150 --> 00:12:46.160 align:start position:0%
times okay
 

00:12:46.160 --> 00:12:49.000 align:start position:0%
times okay
so<00:12:46.430><c> I</c><00:12:46.460><c> hope</c><00:12:46.940><c> that</c><00:12:47.120><c> helped</c><00:12:47.480><c> if</c><00:12:48.320><c> you</c><00:12:48.590><c> need</c><00:12:48.860><c> any</c>

00:12:49.000 --> 00:12:49.010 align:start position:0%
so I hope that helped if you need any
 

00:12:49.010 --> 00:12:51.460 align:start position:0%
so I hope that helped if you need any
more<00:12:49.220><c> clarification</c><00:12:49.610><c> I</c><00:12:50.150><c> can</c><00:12:50.660><c> actually</c><00:12:50.960><c> do</c><00:12:51.440><c> a</c>

00:12:51.460 --> 00:12:51.470 align:start position:0%
more clarification I can actually do a
 

00:12:51.470 --> 00:12:52.720 align:start position:0%
more clarification I can actually do a
video<00:12:51.710><c> that</c><00:12:51.830><c> goes</c><00:12:52.190><c> a</c><00:12:52.340><c> little</c><00:12:52.430><c> bit</c><00:12:52.580><c> more</c>

00:12:52.720 --> 00:12:52.730 align:start position:0%
video that goes a little bit more
 

00:12:52.730 --> 00:12:55.660 align:start position:0%
video that goes a little bit more
in-depth<00:12:53.470><c> one</c><00:12:54.470><c> recursion</c><00:12:54.980><c> and</c><00:12:55.220><c> how</c><00:12:55.490><c> it</c>

00:12:55.660 --> 00:12:55.670 align:start position:0%
in-depth one recursion and how it
 

00:12:55.670 --> 00:12:57.700 align:start position:0%
in-depth one recursion and how it
actually<00:12:55.790><c> works</c><00:12:56.540><c> person</c><00:12:57.200><c> doesn't</c><00:12:57.530><c> work</c>

00:12:57.700 --> 00:12:57.710 align:start position:0%
actually works person doesn't work
 

00:12:57.710 --> 00:13:00.010 align:start position:0%
actually works person doesn't work
because<00:12:57.920><c> it's</c><00:12:58.070><c> magical</c><00:12:58.610><c> there's</c><00:12:59.420><c> a</c><00:12:59.540><c> reason</c>

00:13:00.010 --> 00:13:00.020 align:start position:0%
because it's magical there's a reason
 

00:13:00.020 --> 00:13:02.320 align:start position:0%
because it's magical there's a reason
why<00:13:00.140><c> it</c><00:13:00.200><c> works</c><00:13:00.470><c> it's</c><00:13:00.950><c> all</c><00:13:01.100><c> logic</c><00:13:01.580><c> okay</c><00:13:02.090><c> there</c>

00:13:02.320 --> 00:13:02.330 align:start position:0%
why it works it's all logic okay there
 

00:13:02.330 --> 00:13:04.420 align:start position:0%
why it works it's all logic okay there
it<00:13:02.750><c> works</c><00:13:02.990><c> because</c><00:13:03.380><c> the</c><00:13:03.470><c> data</c><00:13:03.650><c> structure</c><00:13:03.980><c> all</c>

00:13:04.420 --> 00:13:04.430 align:start position:0%
it works because the data structure all
 

00:13:04.430 --> 00:13:08.320 align:start position:0%
it works because the data structure all
right<00:13:05.920><c> so</c><00:13:06.920><c> if</c><00:13:07.070><c> you</c><00:13:07.100><c> need</c><00:13:07.280><c> more</c><00:13:07.430><c> help</c><00:13:07.490><c> that</c><00:13:07.850><c> let</c>

00:13:08.320 --> 00:13:08.330 align:start position:0%
right so if you need more help that let
 

00:13:08.330 --> 00:13:11.230 align:start position:0%
right so if you need more help that let
me<00:13:08.420><c> know</c><00:13:08.480><c> in</c><00:13:08.540><c> the</c><00:13:08.570><c> comments</c><00:13:08.990><c> if</c><00:13:10.030><c> we</c><00:13:11.030><c> have</c><00:13:11.150><c> any</c>

00:13:11.230 --> 00:13:11.240 align:start position:0%
me know in the comments if we have any
 

00:13:11.240 --> 00:13:12.820 align:start position:0%
me know in the comments if we have any
questions<00:13:11.630><c> or</c><00:13:11.750><c> anything</c><00:13:11.930><c> leave</c><00:13:12.590><c> them</c><00:13:12.680><c> down</c>

00:13:12.820 --> 00:13:12.830 align:start position:0%
questions or anything leave them down
 

00:13:12.830 --> 00:13:14.860 align:start position:0%
questions or anything leave them down
below<00:13:12.860><c> and</c><00:13:13.220><c> I</c><00:13:13.640><c> will</c><00:13:13.700><c> get</c><00:13:14.240><c> to</c><00:13:14.270><c> as</c><00:13:14.450><c> soon</c><00:13:14.570><c> as</c><00:13:14.660><c> I</c><00:13:14.690><c> can</c>

00:13:14.860 --> 00:13:14.870 align:start position:0%
below and I will get to as soon as I can
 

00:13:14.870 --> 00:13:18.760 align:start position:0%
below and I will get to as soon as I can
all<00:13:15.080><c> right</c><00:13:15.590><c> I'll</c><00:13:16.160><c> see</c><00:13:16.280><c> you</c><00:13:16.340><c> the</c><00:13:16.400><c> next</c><00:13:16.490><c> video</c>

