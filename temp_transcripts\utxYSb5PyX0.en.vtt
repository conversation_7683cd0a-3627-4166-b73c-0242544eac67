WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:02.310 align:start position:0%
 
and<00:00:00.799><c> howdy</c><00:00:01.199><c> guys</c><00:00:01.520><c> and</c><00:00:01.680><c> thanks</c><00:00:01.839><c> so</c><00:00:02.000><c> much</c><00:00:02.159><c> for</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
and howdy guys and thanks so much for
 

00:00:02.320 --> 00:00:04.390 align:start position:0%
and howdy guys and thanks so much for
checking<00:00:02.639><c> out</c><00:00:02.800><c> this</c><00:00:03.280><c> tutorial</c><00:00:03.840><c> series</c><00:00:04.240><c> all</c>

00:00:04.390 --> 00:00:04.400 align:start position:0%
checking out this tutorial series all
 

00:00:04.400 --> 00:00:07.110 align:start position:0%
checking out this tutorial series all
about<00:00:04.720><c> shopify</c><00:00:05.440><c> app</c><00:00:05.680><c> development</c><00:00:06.640><c> this</c><00:00:06.879><c> is</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
about shopify app development this is
 

00:00:07.120 --> 00:00:09.669 align:start position:0%
about shopify app development this is
the<00:00:07.279><c> app</c><00:00:07.520><c> that</c><00:00:07.680><c> we</c><00:00:07.839><c> will</c><00:00:08.000><c> be</c><00:00:08.160><c> building</c><00:00:09.120><c> uh</c><00:00:09.440><c> this</c>

00:00:09.669 --> 00:00:09.679 align:start position:0%
the app that we will be building uh this
 

00:00:09.679 --> 00:00:10.870 align:start position:0%
the app that we will be building uh this
is<00:00:09.920><c> a</c>

00:00:10.870 --> 00:00:10.880 align:start position:0%
is a
 

00:00:10.880 --> 00:00:12.549 align:start position:0%
is a
shopper-facing

00:00:12.549 --> 00:00:12.559 align:start position:0%
shopper-facing
 

00:00:12.559 --> 00:00:15.110 align:start position:0%
shopper-facing
news<00:00:12.880><c> feed</c><00:00:13.200><c> for</c><00:00:13.440><c> shoppers</c><00:00:14.000><c> to</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
news feed for shoppers to
 

00:00:15.120 --> 00:00:18.470 align:start position:0%
news feed for shoppers to
view<00:00:15.519><c> content</c><00:00:16.400><c> react</c><00:00:16.800><c> with</c><00:00:17.039><c> emojis</c><00:00:18.160><c> and</c>

00:00:18.470 --> 00:00:18.480 align:start position:0%
view content react with emojis and
 

00:00:18.480 --> 00:00:20.470 align:start position:0%
view content react with emojis and
create<00:00:18.880><c> new</c><00:00:19.119><c> content</c><00:00:19.600><c> and</c><00:00:19.680><c> this</c><00:00:19.920><c> is</c><00:00:20.080><c> the</c>

00:00:20.470 --> 00:00:20.480 align:start position:0%
create new content and this is the
 

00:00:20.480 --> 00:00:23.429 align:start position:0%
create new content and this is the
embedded<00:00:20.960><c> shopify</c><00:00:21.600><c> app</c><00:00:21.760><c> for</c><00:00:21.920><c> store</c><00:00:22.320><c> admins</c><00:00:23.199><c> to</c>

00:00:23.429 --> 00:00:23.439 align:start position:0%
embedded shopify app for store admins to
 

00:00:23.439 --> 00:00:25.830 align:start position:0%
embedded shopify app for store admins to
moderate<00:00:23.920><c> the</c><00:00:24.080><c> content</c><00:00:24.560><c> as</c><00:00:24.800><c> it</c><00:00:24.960><c> comes</c><00:00:25.439><c> comes</c>

00:00:25.830 --> 00:00:25.840 align:start position:0%
moderate the content as it comes comes
 

00:00:25.840 --> 00:00:28.950 align:start position:0%
moderate the content as it comes comes
in<00:00:26.480><c> um</c><00:00:27.119><c> create</c><00:00:27.599><c> topics</c><00:00:28.080><c> and</c><00:00:28.160><c> a</c><00:00:28.240><c> bunch</c><00:00:28.480><c> of</c><00:00:28.640><c> other</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
in um create topics and a bunch of other
 

00:00:28.960 --> 00:00:32.069 align:start position:0%
in um create topics and a bunch of other
cool<00:00:29.199><c> stuff</c><00:00:29.920><c> set</c><00:00:30.160><c> the</c><00:00:30.320><c> logo</c><00:00:30.880><c> customize</c><00:00:31.519><c> stuff</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
cool stuff set the logo customize stuff
 

00:00:32.079 --> 00:00:34.470 align:start position:0%
cool stuff set the logo customize stuff
and<00:00:32.239><c> it's</c><00:00:32.399><c> really</c><00:00:32.719><c> really</c><00:00:32.960><c> a</c><00:00:33.040><c> cool</c><00:00:33.360><c> app</c><00:00:34.079><c> so</c><00:00:34.399><c> the</c>

00:00:34.470 --> 00:00:34.480 align:start position:0%
and it's really really a cool app so the
 

00:00:34.480 --> 00:00:35.510 align:start position:0%
and it's really really a cool app so the
first<00:00:34.719><c> thing</c><00:00:34.880><c> you're</c><00:00:34.960><c> going</c><00:00:35.040><c> to</c><00:00:35.120><c> need</c><00:00:35.280><c> to</c><00:00:35.360><c> do</c>

00:00:35.510 --> 00:00:35.520 align:start position:0%
first thing you're going to need to do
 

00:00:35.520 --> 00:00:38.229 align:start position:0%
first thing you're going to need to do
in<00:00:35.680><c> order</c><00:00:35.840><c> to</c><00:00:35.920><c> get</c><00:00:36.160><c> started</c><00:00:36.719><c> is</c><00:00:37.120><c> install</c><00:00:37.760><c> ngrok</c>

00:00:38.229 --> 00:00:38.239 align:start position:0%
in order to get started is install ngrok
 

00:00:38.239 --> 00:00:39.590 align:start position:0%
in order to get started is install ngrok
so<00:00:38.399><c> we're</c><00:00:38.559><c> going</c><00:00:38.640><c> to</c><00:00:38.800><c> do</c>

00:00:39.590 --> 00:00:39.600 align:start position:0%
so we're going to do
 

00:00:39.600 --> 00:00:42.069 align:start position:0%
so we're going to do
we're<00:00:39.760><c> going</c><00:00:39.840><c> to</c><00:00:39.920><c> go</c><00:00:40.000><c> into</c><00:00:40.239><c> ngrok.com</c><00:00:41.600><c> and</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
we're going to go into ngrok.com and
 

00:00:42.079 --> 00:00:44.950 align:start position:0%
we're going to go into ngrok.com and
we're<00:00:42.239><c> going</c><00:00:42.399><c> to</c><00:00:42.960><c> download</c><00:00:43.520><c> it</c><00:00:43.920><c> for</c>

00:00:44.950 --> 00:00:44.960 align:start position:0%
we're going to download it for
 

00:00:44.960 --> 00:00:47.430 align:start position:0%
we're going to download it for
windows<00:00:45.920><c> all</c><00:00:46.079><c> right</c><00:00:46.320><c> so</c><00:00:46.480><c> let's</c><00:00:46.719><c> see</c><00:00:46.960><c> download</c>

00:00:47.430 --> 00:00:47.440 align:start position:0%
windows all right so let's see download
 

00:00:47.440 --> 00:00:49.190 align:start position:0%
windows all right so let's see download
for<00:00:47.680><c> windows</c>

00:00:49.190 --> 00:00:49.200 align:start position:0%
for windows
 

00:00:49.200 --> 00:00:52.229 align:start position:0%
for windows
all<00:00:49.360><c> right</c><00:00:50.000><c> because</c><00:00:50.879><c> and</c><00:00:51.199><c> uh</c><00:00:51.680><c> while</c><00:00:51.840><c> we</c><00:00:52.000><c> wait</c>

00:00:52.229 --> 00:00:52.239 align:start position:0%
all right because and uh while we wait
 

00:00:52.239 --> 00:00:53.910 align:start position:0%
all right because and uh while we wait
for<00:00:52.399><c> it</c><00:00:52.559><c> to</c><00:00:52.719><c> load</c>

00:00:53.910 --> 00:00:53.920 align:start position:0%
for it to load
 

00:00:53.920 --> 00:00:55.830 align:start position:0%
for it to load
i'll<00:00:54.160><c> just</c><00:00:54.480><c> uh</c>

00:00:55.830 --> 00:00:55.840 align:start position:0%
i'll just uh
 

00:00:55.840 --> 00:00:57.590 align:start position:0%
i'll just uh
i'll<00:00:56.079><c> explain</c><00:00:56.480><c> why</c><00:00:56.640><c> we</c><00:00:56.719><c> need</c><00:00:56.960><c> ngrok</c><00:00:57.440><c> is</c>

00:00:57.590 --> 00:00:57.600 align:start position:0%
i'll explain why we need ngrok is
 

00:00:57.600 --> 00:00:58.389 align:start position:0%
i'll explain why we need ngrok is
because

00:00:58.389 --> 00:00:58.399 align:start position:0%
because
 

00:00:58.399 --> 00:01:02.310 align:start position:0%
because
uh<00:00:58.719><c> shopify's</c><00:00:59.359><c> api</c><00:00:59.840><c> needs</c><00:01:00.160><c> to</c><00:01:00.320><c> talk</c><00:01:00.800><c> with</c><00:01:01.199><c> um</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
uh shopify's api needs to talk with um
 

00:01:02.320 --> 00:01:04.950 align:start position:0%
uh shopify's api needs to talk with um
is<00:01:02.559><c> sitting</c><00:01:02.879><c> in</c><00:01:02.960><c> the</c><00:01:03.120><c> cloud</c><00:01:03.520><c> so</c><00:01:04.000><c> you</c><00:01:04.239><c> can't</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
is sitting in the cloud so you can't
 

00:01:04.960 --> 00:01:06.550 align:start position:0%
is sitting in the cloud so you can't
everything<00:01:05.280><c> that's</c><00:01:05.519><c> happening</c><00:01:05.920><c> locally</c><00:01:06.479><c> all</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
everything that's happening locally all
 

00:01:06.560 --> 00:01:08.710 align:start position:0%
everything that's happening locally all
the<00:01:06.720><c> http</c><00:01:07.439><c> post</c><00:01:07.760><c> requests</c><00:01:08.159><c> that</c><00:01:08.320><c> happen</c><00:01:08.560><c> on</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
the http post requests that happen on
 

00:01:08.720 --> 00:01:11.109 align:start position:0%
the http post requests that happen on
your<00:01:08.799><c> local</c><00:01:09.119><c> machine</c><00:01:09.840><c> gets</c><00:01:10.159><c> forwarded</c><00:01:10.799><c> via</c>

00:01:11.109 --> 00:01:11.119 align:start position:0%
your local machine gets forwarded via
 

00:01:11.119 --> 00:01:15.510 align:start position:0%
your local machine gets forwarded via
ngrok<00:01:12.240><c> um</c><00:01:12.880><c> to</c><00:01:13.360><c> all</c><00:01:13.680><c> these</c><00:01:14.080><c> uh</c><00:01:14.400><c> to</c><00:01:14.560><c> the</c><00:01:14.960><c> the</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
ngrok um to all these uh to the the
 

00:01:15.520 --> 00:01:17.510 align:start position:0%
ngrok um to all these uh to the the
shopify<00:01:16.159><c> cloud</c><00:01:16.560><c> so</c><00:01:16.720><c> that's</c><00:01:16.880><c> kind</c><00:01:17.040><c> of</c><00:01:17.200><c> what</c>

00:01:17.510 --> 00:01:17.520 align:start position:0%
shopify cloud so that's kind of what
 

00:01:17.520 --> 00:01:19.270 align:start position:0%
shopify cloud so that's kind of what
what<00:01:17.680><c> we</c><00:01:17.840><c> need</c><00:01:18.000><c> to</c><00:01:18.159><c> do</c><00:01:18.479><c> we'll</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
what we need to do we'll
 

00:01:19.280 --> 00:01:21.830 align:start position:0%
what we need to do we'll
run<00:01:19.520><c> an</c><00:01:19.759><c> ngra</c><00:01:20.240><c> command</c><00:01:20.720><c> over</c><00:01:20.960><c> here</c><00:01:21.200><c> once</c><00:01:21.600><c> once</c>

00:01:21.830 --> 00:01:21.840 align:start position:0%
run an ngra command over here once once
 

00:01:21.840 --> 00:01:24.550 align:start position:0%
run an ngra command over here once once
we<00:01:22.000><c> get</c><00:01:22.240><c> it</c><00:01:22.320><c> running</c><00:01:23.119><c> okay</c><00:01:23.600><c> one</c><00:01:23.840><c> second</c><00:01:24.320><c> let's</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
we get it running okay one second let's
 

00:01:24.560 --> 00:01:28.789 align:start position:0%
we get it running okay one second let's
open<00:01:24.880><c> it</c><00:01:25.040><c> up</c><00:01:26.159><c> okay</c><00:01:26.840><c> ngrok.exe</c><00:01:28.000><c> that's</c><00:01:28.320><c> cool</c>

00:01:28.789 --> 00:01:28.799 align:start position:0%
open it up okay ngrok.exe that's cool
 

00:01:28.799 --> 00:01:31.109 align:start position:0%
open it up okay ngrok.exe that's cool
let's<00:01:29.119><c> let's</c><00:01:29.360><c> just</c><00:01:29.680><c> extract</c><00:01:30.320><c> all</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
let's let's just extract all
 

00:01:31.119 --> 00:01:31.990 align:start position:0%
let's let's just extract all
into

00:01:31.990 --> 00:01:32.000 align:start position:0%
into
 

00:01:32.000 --> 00:01:34.469 align:start position:0%
into
our<00:01:32.400><c> downloads</c><00:01:33.200><c> okay</c><00:01:33.520><c> and</c><00:01:33.680><c> see</c><00:01:33.840><c> if</c><00:01:34.159><c> see</c><00:01:34.320><c> if</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
our downloads okay and see if see if
 

00:01:34.479 --> 00:01:36.550 align:start position:0%
our downloads okay and see if see if
everything<00:01:34.880><c> works</c>

00:01:36.550 --> 00:01:36.560 align:start position:0%
everything works
 

00:01:36.560 --> 00:01:40.789 align:start position:0%
everything works
all<00:01:36.799><c> right</c><00:01:37.520><c> so</c><00:01:38.159><c> ngrok</c><00:01:38.720><c> is</c><00:01:38.880><c> now</c><00:01:39.280><c> extracted</c>

00:01:40.789 --> 00:01:40.799 align:start position:0%
all right so ngrok is now extracted
 

00:01:40.799 --> 00:01:42.710 align:start position:0%
all right so ngrok is now extracted
uh<00:01:41.119><c> from</c><00:01:41.280><c> the</c><00:01:41.439><c> zip</c><00:01:41.759><c> file</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
uh from the zip file
 

00:01:42.720 --> 00:01:45.270 align:start position:0%
uh from the zip file
and<00:01:43.360><c> uh</c><00:01:43.600><c> once</c><00:01:43.920><c> we</c><00:01:44.159><c> click</c><00:01:44.399><c> that</c><00:01:44.640><c> button</c><00:01:44.960><c> then</c><00:01:45.119><c> we</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
and uh once we click that button then we
 

00:01:45.280 --> 00:01:47.749 align:start position:0%
and uh once we click that button then we
see<00:01:45.439><c> this</c><00:01:45.759><c> this</c><00:01:46.000><c> terminal</c><00:01:46.479><c> over</c><00:01:46.720><c> here</c><00:01:47.520><c> which</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
see this this terminal over here which
 

00:01:47.759 --> 00:01:49.910 align:start position:0%
see this this terminal over here which
has<00:01:48.000><c> something</c><00:01:48.399><c> about</c><00:01:48.720><c> ngrok</c><00:01:49.360><c> let's</c><00:01:49.600><c> try</c><00:01:49.840><c> to</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
has something about ngrok let's try to
 

00:01:49.920 --> 00:01:52.870 align:start position:0%
has something about ngrok let's try to
see<00:01:50.079><c> if</c><00:01:50.240><c> it</c><00:01:50.399><c> works</c><00:01:51.040><c> yes</c><00:01:51.280><c> that's</c><00:01:51.600><c> positive</c><00:01:52.320><c> okay</c>

00:01:52.870 --> 00:01:52.880 align:start position:0%
see if it works yes that's positive okay
 

00:01:52.880 --> 00:01:55.709 align:start position:0%
see if it works yes that's positive okay
so<00:01:53.040><c> we're</c><00:01:53.200><c> gonna</c><00:01:53.360><c> do</c><00:01:53.600><c> ngrok</c><00:01:54.320><c> http</c>

00:01:55.709 --> 00:01:55.719 align:start position:0%
so we're gonna do ngrok http
 

00:01:55.719 --> 00:01:58.310 align:start position:0%
so we're gonna do ngrok http
3000.<00:01:56.799><c> oh</c><00:01:57.200><c> you</c><00:01:57.360><c> know</c><00:01:57.439><c> what</c><00:01:57.520><c> let's</c><00:01:57.759><c> do</c><00:01:58.000><c> seven</c>

00:01:58.310 --> 00:01:58.320 align:start position:0%
3000. oh you know what let's do seven
 

00:01:58.320 --> 00:01:59.910 align:start position:0%
3000. oh you know what let's do seven
seven<00:01:58.640><c> seven</c><00:01:59.040><c> okay</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
seven seven okay
 

00:01:59.920 --> 00:02:02.870 align:start position:0%
seven seven okay
quadruple<00:02:00.479><c> seven</c><00:02:00.880><c> cool</c><00:02:01.600><c> now</c><00:02:01.840><c> once</c><00:02:02.079><c> we</c><00:02:02.240><c> do</c><00:02:02.479><c> that</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
quadruple seven cool now once we do that
 

00:02:02.880 --> 00:02:05.590 align:start position:0%
quadruple seven cool now once we do that
then<00:02:03.200><c> ngrok</c><00:02:03.680><c> says</c><00:02:03.920><c> it's</c><00:02:04.159><c> running</c><00:02:04.560><c> on</c><00:02:04.880><c> port</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
then ngrok says it's running on port
 

00:02:05.600 --> 00:02:07.510 align:start position:0%
then ngrok says it's running on port
quadruple<00:02:06.320><c> seven</c><00:02:06.719><c> and</c><00:02:06.880><c> it's</c><00:02:07.040><c> going</c><00:02:07.119><c> to</c><00:02:07.280><c> give</c>

00:02:07.510 --> 00:02:07.520 align:start position:0%
quadruple seven and it's going to give
 

00:02:07.520 --> 00:02:10.150 align:start position:0%
quadruple seven and it's going to give
us<00:02:07.680><c> this</c><00:02:07.920><c> url</c><00:02:08.560><c> over</c><00:02:08.879><c> here</c><00:02:09.599><c> all</c><00:02:09.759><c> right</c><00:02:09.840><c> so</c><00:02:10.000><c> let's</c>

00:02:10.150 --> 00:02:10.160 align:start position:0%
us this url over here all right so let's
 

00:02:10.160 --> 00:02:12.150 align:start position:0%
us this url over here all right so let's
go<00:02:10.319><c> ahead</c><00:02:10.479><c> and</c><00:02:10.560><c> just</c><00:02:10.800><c> take</c><00:02:11.039><c> that</c><00:02:11.280><c> url</c><00:02:11.760><c> from</c><00:02:11.920><c> the</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
go ahead and just take that url from the
 

00:02:12.160 --> 00:02:16.630 align:start position:0%
go ahead and just take that url from the
https<00:02:13.599><c> i'm</c><00:02:13.760><c> going</c><00:02:13.920><c> to</c><00:02:14.080><c> go</c><00:02:14.239><c> ahead</c><00:02:14.640><c> and</c>

00:02:16.630 --> 00:02:16.640 align:start position:0%
https i'm going to go ahead and
 

00:02:16.640 --> 00:02:19.670 align:start position:0%
https i'm going to go ahead and
do<00:02:17.040><c> control</c><00:02:17.599><c> c</c><00:02:18.000><c> see</c><00:02:18.160><c> if</c><00:02:18.319><c> that</c><00:02:18.560><c> works</c>

00:02:19.670 --> 00:02:19.680 align:start position:0%
do control c see if that works
 

00:02:19.680 --> 00:02:22.790 align:start position:0%
do control c see if that works
okay<00:02:20.239><c> yes</c><00:02:20.480><c> i</c><00:02:20.640><c> only</c><00:02:20.800><c> press</c><00:02:21.040><c> control</c><00:02:21.440><c> c</c><00:02:21.760><c> once</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
okay yes i only press control c once
 

00:02:22.800 --> 00:02:25.350 align:start position:0%
okay yes i only press control c once
all<00:02:22.959><c> right</c><00:02:23.200><c> so</c><00:02:23.520><c> once</c><00:02:23.760><c> i</c><00:02:23.920><c> go</c><00:02:24.080><c> in</c><00:02:24.239><c> here</c><00:02:24.720><c> then</c><00:02:25.040><c> if</c><00:02:25.200><c> i</c>

00:02:25.350 --> 00:02:25.360 align:start position:0%
all right so once i go in here then if i
 

00:02:25.360 --> 00:02:27.030 align:start position:0%
all right so once i go in here then if i
go<00:02:25.520><c> into</c><00:02:25.760><c> the</c><00:02:25.920><c> actual</c><00:02:26.319><c> app</c><00:02:26.560><c> by</c><00:02:26.720><c> the</c><00:02:26.720><c> way</c><00:02:26.879><c> you're</c>

00:02:27.030 --> 00:02:27.040 align:start position:0%
go into the actual app by the way you're
 

00:02:27.040 --> 00:02:28.869 align:start position:0%
go into the actual app by the way you're
gonna<00:02:27.200><c> need</c><00:02:27.360><c> to</c><00:02:27.440><c> set</c><00:02:27.599><c> up</c><00:02:27.680><c> a</c><00:02:27.760><c> shopify</c><00:02:28.400><c> partners</c>

00:02:28.869 --> 00:02:28.879 align:start position:0%
gonna need to set up a shopify partners
 

00:02:28.879 --> 00:02:31.030 align:start position:0%
gonna need to set up a shopify partners
account<00:02:29.200><c> as</c><00:02:29.360><c> well</c><00:02:30.000><c> in</c><00:02:30.239><c> order</c><00:02:30.480><c> to</c><00:02:30.560><c> generate</c>

00:02:31.030 --> 00:02:31.040 align:start position:0%
account as well in order to generate
 

00:02:31.040 --> 00:02:33.990 align:start position:0%
account as well in order to generate
your<00:02:31.280><c> shopify</c><00:02:31.840><c> api</c><00:02:32.400><c> key</c><00:02:32.640><c> and</c><00:02:32.800><c> your</c><00:02:32.959><c> api</c><00:02:33.519><c> secret</c>

00:02:33.990 --> 00:02:34.000 align:start position:0%
your shopify api key and your api secret
 

00:02:34.000 --> 00:02:36.390 align:start position:0%
your shopify api key and your api secret
key<00:02:34.720><c> and</c><00:02:35.040><c> so</c><00:02:35.360><c> go</c><00:02:35.519><c> ahead</c><00:02:35.760><c> and</c><00:02:35.920><c> create</c><00:02:36.239><c> an</c>

00:02:36.390 --> 00:02:36.400 align:start position:0%
key and so go ahead and create an
 

00:02:36.400 --> 00:02:39.830 align:start position:0%
key and so go ahead and create an
account<00:02:36.959><c> on</c><00:02:37.280><c> partners.shopify.com</c>

00:02:39.830 --> 00:02:39.840 align:start position:0%
account on partners.shopify.com
 

00:02:39.840 --> 00:02:41.670 align:start position:0%
account on partners.shopify.com
as<00:02:40.080><c> well</c><00:02:40.560><c> once</c><00:02:40.800><c> you</c><00:02:41.040><c> do</c><00:02:41.200><c> that</c><00:02:41.360><c> then</c><00:02:41.519><c> you're</c>

00:02:41.670 --> 00:02:41.680 align:start position:0%
as well once you do that then you're
 

00:02:41.680 --> 00:02:43.030 align:start position:0%
as well once you do that then you're
going<00:02:41.760><c> to</c><00:02:41.840><c> create</c><00:02:42.160><c> a</c><00:02:42.239><c> new</c><00:02:42.400><c> app</c><00:02:42.640><c> it's</c><00:02:42.800><c> pretty</c>

00:02:43.030 --> 00:02:43.040 align:start position:0%
going to create a new app it's pretty
 

00:02:43.040 --> 00:02:44.869 align:start position:0%
going to create a new app it's pretty
simple<00:02:43.840><c> and</c><00:02:44.000><c> you're</c><00:02:44.160><c> going</c><00:02:44.239><c> to</c><00:02:44.400><c> go</c><00:02:44.480><c> into</c><00:02:44.720><c> the</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
simple and you're going to go into the
 

00:02:44.879 --> 00:02:46.869 align:start position:0%
simple and you're going to go into the
app<00:02:45.200><c> setup</c><00:02:45.599><c> over</c><00:02:45.760><c> here</c><00:02:45.920><c> that's</c><00:02:46.160><c> the</c><00:02:46.239><c> api</c><00:02:46.720><c> key</c>

00:02:46.869 --> 00:02:46.879 align:start position:0%
app setup over here that's the api key
 

00:02:46.879 --> 00:02:48.630 align:start position:0%
app setup over here that's the api key
and<00:02:46.959><c> api</c><00:02:47.360><c> secret</c><00:02:47.680><c> key</c><00:02:47.920><c> you</c><00:02:48.000><c> will</c><00:02:48.239><c> need</c><00:02:48.400><c> that</c>

00:02:48.630 --> 00:02:48.640 align:start position:0%
and api secret key you will need that
 

00:02:48.640 --> 00:02:49.509 align:start position:0%
and api secret key you will need that
too

00:02:49.509 --> 00:02:49.519 align:start position:0%
too
 

00:02:49.519 --> 00:02:51.990 align:start position:0%
too
this<00:02:49.760><c> is</c><00:02:49.920><c> the</c><00:02:50.080><c> app</c><00:02:50.319><c> url</c><00:02:50.800><c> for</c><00:02:50.959><c> the</c><00:02:51.200><c> embedded</c>

00:02:51.990 --> 00:02:52.000 align:start position:0%
this is the app url for the embedded
 

00:02:52.000 --> 00:02:53.910 align:start position:0%
this is the app url for the embedded
embedded<00:02:52.879><c> um</c>

00:02:53.910 --> 00:02:53.920 align:start position:0%
embedded um
 

00:02:53.920 --> 00:02:55.830 align:start position:0%
embedded um
embedded<00:02:54.319><c> shopify</c><00:02:54.959><c> app</c><00:02:55.200><c> and</c><00:02:55.360><c> that</c><00:02:55.680><c> is</c>

00:02:55.830 --> 00:02:55.840 align:start position:0%
embedded shopify app and that is
 

00:02:55.840 --> 00:02:57.670 align:start position:0%
embedded shopify app and that is
specifically<00:02:56.480><c> for</c><00:02:56.720><c> this</c>

00:02:57.670 --> 00:02:57.680 align:start position:0%
specifically for this
 

00:02:57.680 --> 00:02:59.350 align:start position:0%
specifically for this
right<00:02:57.920><c> so</c><00:02:58.080><c> whatever</c><00:02:58.400><c> is</c><00:02:58.560><c> happening</c><00:02:58.879><c> on</c><00:02:59.040><c> port</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
right so whatever is happening on port
 

00:02:59.360 --> 00:03:02.070 align:start position:0%
right so whatever is happening on port
3000<00:03:00.640><c> uh</c><00:03:00.879><c> we'll</c><00:03:01.040><c> get</c><00:03:01.280><c> forwarded</c><00:03:01.760><c> and</c><00:03:01.840><c> that's</c>

00:03:02.070 --> 00:03:02.080 align:start position:0%
3000 uh we'll get forwarded and that's
 

00:03:02.080 --> 00:03:04.710 align:start position:0%
3000 uh we'll get forwarded and that's
how<00:03:02.480><c> that</c><00:03:02.720><c> that's</c><00:03:03.040><c> that</c><00:03:03.280><c> specific</c><00:03:03.920><c> app</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
how that that's that specific app
 

00:03:04.720 --> 00:03:06.070 align:start position:0%
how that that's that specific app
but<00:03:04.959><c> you</c><00:03:05.120><c> also</c><00:03:05.360><c> have</c><00:03:05.440><c> something</c><00:03:05.760><c> called</c><00:03:06.000><c> the</c>

00:03:06.070 --> 00:03:06.080 align:start position:0%
but you also have something called the
 

00:03:06.080 --> 00:03:08.630 align:start position:0%
but you also have something called the
proxy<00:03:06.879><c> app</c><00:03:07.280><c> and</c><00:03:07.440><c> that's</c><00:03:07.840><c> pretty</c><00:03:08.080><c> much</c><00:03:08.400><c> what's</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
proxy app and that's pretty much what's
 

00:03:08.640 --> 00:03:10.309 align:start position:0%
proxy app and that's pretty much what's
sitting<00:03:09.040><c> over</c><00:03:09.360><c> here</c>

00:03:10.309 --> 00:03:10.319 align:start position:0%
sitting over here
 

00:03:10.319 --> 00:03:12.550 align:start position:0%
sitting over here
it's<00:03:10.640><c> kinda</c><00:03:10.959><c> it's</c><00:03:11.120><c> sitting</c><00:03:11.519><c> on</c><00:03:11.680><c> your</c><00:03:11.840><c> shopify</c>

00:03:12.550 --> 00:03:12.560 align:start position:0%
it's kinda it's sitting on your shopify
 

00:03:12.560 --> 00:03:15.030 align:start position:0%
it's kinda it's sitting on your shopify
store<00:03:13.360><c> um</c>

00:03:15.030 --> 00:03:15.040 align:start position:0%
store um
 

00:03:15.040 --> 00:03:16.309 align:start position:0%
store um
right<00:03:15.360><c> just</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
right just
 

00:03:16.319 --> 00:03:18.550 align:start position:0%
right just
uh<00:03:16.720><c> on</c><00:03:16.959><c> community</c><00:03:17.519><c> slash</c><00:03:17.920><c> connect</c><00:03:18.319><c> so</c>

00:03:18.550 --> 00:03:18.560 align:start position:0%
uh on community slash connect so
 

00:03:18.560 --> 00:03:21.190 align:start position:0%
uh on community slash connect so
socialking.app<00:03:20.000><c> right</c><00:03:20.239><c> whatever</c><00:03:20.560><c> the</c><00:03:20.720><c> domain</c>

00:03:21.190 --> 00:03:21.200 align:start position:0%
socialking.app right whatever the domain
 

00:03:21.200 --> 00:03:24.309 align:start position:0%
socialking.app right whatever the domain
is<00:03:21.519><c> slash</c><00:03:21.920><c> the</c><00:03:22.080><c> prefix</c><00:03:22.720><c> community</c><00:03:23.360><c> connect</c><00:03:24.159><c> is</c>

00:03:24.309 --> 00:03:24.319 align:start position:0%
is slash the prefix community connect is
 

00:03:24.319 --> 00:03:26.149 align:start position:0%
is slash the prefix community connect is
going<00:03:24.400><c> to</c><00:03:24.640><c> serve</c><00:03:25.040><c> this</c><00:03:25.280><c> specific</c><00:03:25.760><c> app</c><00:03:26.000><c> so</c>

00:03:26.149 --> 00:03:26.159 align:start position:0%
going to serve this specific app so
 

00:03:26.159 --> 00:03:28.070 align:start position:0%
going to serve this specific app so
let's<00:03:26.319><c> go</c><00:03:26.480><c> ahead</c><00:03:26.720><c> and</c><00:03:27.040><c> and</c><00:03:27.360><c> search</c><00:03:27.599><c> within</c><00:03:27.920><c> our</c>

00:03:28.070 --> 00:03:28.080 align:start position:0%
let's go ahead and and search within our
 

00:03:28.080 --> 00:03:29.830 align:start position:0%
let's go ahead and and search within our
partners<00:03:28.640><c> dashboard</c>

00:03:29.830 --> 00:03:29.840 align:start position:0%
partners dashboard
 

00:03:29.840 --> 00:03:32.309 align:start position:0%
partners dashboard
for<00:03:30.159><c> the</c><00:03:30.239><c> word</c><00:03:30.560><c> proxy</c>

00:03:32.309 --> 00:03:32.319 align:start position:0%
for the word proxy
 

00:03:32.319 --> 00:03:33.910 align:start position:0%
for the word proxy
okay<00:03:32.720><c> perfect</c>

00:03:33.910 --> 00:03:33.920 align:start position:0%
okay perfect
 

00:03:33.920 --> 00:03:35.750 align:start position:0%
okay perfect
and<00:03:34.000><c> that's</c><00:03:34.319><c> exactly</c><00:03:34.720><c> how</c><00:03:34.879><c> that's</c><00:03:35.200><c> configured</c>

00:03:35.750 --> 00:03:35.760 align:start position:0%
and that's exactly how that's configured
 

00:03:35.760 --> 00:03:38.229 align:start position:0%
and that's exactly how that's configured
we<00:03:36.000><c> we</c><00:03:36.159><c> have</c><00:03:36.400><c> our</c><00:03:36.480><c> proxy</c><00:03:36.959><c> url</c><00:03:37.440><c> over</c><00:03:37.680><c> here</c><00:03:38.000><c> and</c>

00:03:38.229 --> 00:03:38.239 align:start position:0%
we we have our proxy url over here and
 

00:03:38.239 --> 00:03:41.589 align:start position:0%
we we have our proxy url over here and
grog.io<00:03:39.360><c> proxy</c><00:03:40.400><c> okay</c><00:03:40.799><c> so</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
grog.io proxy okay so
 

00:03:41.599 --> 00:03:43.990 align:start position:0%
grog.io proxy okay so
we're<00:03:41.840><c> gonna</c><00:03:42.080><c> take</c><00:03:42.239><c> that</c><00:03:42.560><c> ngrok</c><00:03:43.040><c> url</c><00:03:43.680><c> that</c><00:03:43.840><c> we</c>

00:03:43.990 --> 00:03:44.000 align:start position:0%
we're gonna take that ngrok url that we
 

00:03:44.000 --> 00:03:46.149 align:start position:0%
we're gonna take that ngrok url that we
just<00:03:44.239><c> got</c><00:03:44.640><c> we</c><00:03:44.799><c> just</c><00:03:45.040><c> paste</c><00:03:45.360><c> it</c><00:03:45.440><c> right</c><00:03:45.680><c> in</c><00:03:45.840><c> there</c>

00:03:46.149 --> 00:03:46.159 align:start position:0%
just got we just paste it right in there
 

00:03:46.159 --> 00:03:47.990 align:start position:0%
just got we just paste it right in there
and<00:03:46.239><c> then</c><00:03:46.400><c> we</c><00:03:46.560><c> press</c><00:03:46.959><c> save</c>

00:03:47.990 --> 00:03:48.000 align:start position:0%
and then we press save
 

00:03:48.000 --> 00:03:50.309 align:start position:0%
and then we press save
and<00:03:48.080><c> the</c><00:03:48.239><c> nice</c><00:03:48.480><c> thing</c><00:03:48.640><c> about</c><00:03:48.959><c> that</c><00:03:49.280><c> is</c><00:03:49.599><c> now</c>

00:03:50.309 --> 00:03:50.319 align:start position:0%
and the nice thing about that is now
 

00:03:50.319 --> 00:03:52.550 align:start position:0%
and the nice thing about that is now
everything<00:03:50.640><c> that's</c><00:03:50.959><c> running</c><00:03:51.280><c> on</c><00:03:51.440><c> port</c><00:03:52.000><c> uh</c>

00:03:52.550 --> 00:03:52.560 align:start position:0%
everything that's running on port uh
 

00:03:52.560 --> 00:03:54.309 align:start position:0%
everything that's running on port uh
quadruple<00:03:53.280><c> seven</c><00:03:53.599><c> is</c><00:03:53.680><c> now</c><00:03:53.840><c> going</c><00:03:54.000><c> to</c><00:03:54.080><c> get</c>

00:03:54.309 --> 00:03:54.319 align:start position:0%
quadruple seven is now going to get
 

00:03:54.319 --> 00:03:55.509 align:start position:0%
quadruple seven is now going to get
forwarded

00:03:55.509 --> 00:03:55.519 align:start position:0%
forwarded
 

00:03:55.519 --> 00:03:59.110 align:start position:0%
forwarded
uh<00:03:56.000><c> to</c><00:03:56.400><c> to</c><00:03:56.640><c> this</c><00:03:57.040><c> all</c><00:03:57.200><c> right</c><00:03:57.439><c> to</c><00:03:57.599><c> this</c><00:03:57.920><c> app</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
uh to to this all right to this app
 

00:03:59.120 --> 00:04:01.350 align:start position:0%
uh to to this all right to this app
and<00:03:59.200><c> this</c><00:03:59.439><c> is</c><00:03:59.519><c> called</c><00:03:59.840><c> social</c><00:04:00.159><c> king</c><00:04:00.480><c> dev</c><00:04:01.120><c> let's</c>

00:04:01.350 --> 00:04:01.360 align:start position:0%
and this is called social king dev let's
 

00:04:01.360 --> 00:04:04.070 align:start position:0%
and this is called social king dev let's
go<00:04:01.599><c> ahead</c><00:04:01.920><c> and</c><00:04:02.480><c> and</c><00:04:02.799><c> open</c><00:04:03.040><c> up</c><00:04:03.200><c> a</c><00:04:03.280><c> new</c><00:04:03.519><c> terminal</c>

00:04:04.070 --> 00:04:04.080 align:start position:0%
go ahead and and open up a new terminal
 

00:04:04.080 --> 00:04:07.509 align:start position:0%
go ahead and and open up a new terminal
over<00:04:04.319><c> here</c><00:04:05.120><c> and</c><00:04:05.360><c> we're</c><00:04:05.519><c> gonna</c><00:04:05.760><c> go</c><00:04:06.159><c> into</c>

00:04:07.509 --> 00:04:07.519 align:start position:0%
over here and we're gonna go into
 

00:04:07.519 --> 00:04:10.149 align:start position:0%
over here and we're gonna go into
the<00:04:07.920><c> social</c><00:04:08.319><c> king</c><00:04:08.560><c> repo</c><00:04:09.360><c> and</c><00:04:09.599><c> by</c><00:04:09.760><c> the</c><00:04:09.840><c> way</c><00:04:10.000><c> if</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
the social king repo and by the way if
 

00:04:10.159 --> 00:04:12.070 align:start position:0%
the social king repo and by the way if
you<00:04:10.239><c> need</c><00:04:10.560><c> access</c><00:04:10.959><c> to</c><00:04:11.120><c> the</c><00:04:11.280><c> repo</c><00:04:11.599><c> and</c><00:04:11.760><c> it's</c><00:04:11.840><c> not</c>

00:04:12.070 --> 00:04:12.080 align:start position:0%
you need access to the repo and it's not
 

00:04:12.080 --> 00:04:14.949 align:start position:0%
you need access to the repo and it's not
public<00:04:13.040><c> um</c><00:04:13.599><c> feel</c><00:04:13.760><c> free</c><00:04:14.000><c> to</c><00:04:14.239><c> just</c>

00:04:14.949 --> 00:04:14.959 align:start position:0%
public um feel free to just
 

00:04:14.959 --> 00:04:17.030 align:start position:0%
public um feel free to just
put<00:04:15.200><c> your</c><00:04:15.360><c> github</c><00:04:15.760><c> username</c><00:04:16.320><c> in</c><00:04:16.479><c> the</c><00:04:16.639><c> youtube</c>

00:04:17.030 --> 00:04:17.040 align:start position:0%
put your github username in the youtube
 

00:04:17.040 --> 00:04:19.189 align:start position:0%
put your github username in the youtube
comments<00:04:17.440><c> below</c><00:04:18.079><c> and</c><00:04:18.239><c> i'm</c><00:04:18.320><c> happy</c><00:04:18.639><c> to</c><00:04:18.799><c> send</c><00:04:19.040><c> you</c>

00:04:19.189 --> 00:04:19.199 align:start position:0%
comments below and i'm happy to send you
 

00:04:19.199 --> 00:04:21.670 align:start position:0%
comments below and i'm happy to send you
over<00:04:19.680><c> i</c><00:04:19.840><c> invite</c><00:04:20.160><c> you</c><00:04:20.400><c> to</c><00:04:20.720><c> access</c><00:04:21.040><c> the</c><00:04:21.199><c> repo</c><00:04:21.600><c> and</c>

00:04:21.670 --> 00:04:21.680 align:start position:0%
over i invite you to access the repo and
 

00:04:21.680 --> 00:04:24.070 align:start position:0%
over i invite you to access the repo and
play<00:04:21.919><c> along</c><00:04:22.560><c> uh</c><00:04:22.800><c> throughout</c><00:04:23.199><c> the</c><00:04:23.360><c> tutorial</c>

00:04:24.070 --> 00:04:24.080 align:start position:0%
play along uh throughout the tutorial
 

00:04:24.080 --> 00:04:26.070 align:start position:0%
play along uh throughout the tutorial
all<00:04:24.240><c> right</c><00:04:24.400><c> so</c><00:04:24.479><c> there</c><00:04:24.639><c> you</c><00:04:24.720><c> go</c><00:04:24.880><c> cd</c><00:04:25.199><c> social</c><00:04:25.600><c> king</c>

00:04:26.070 --> 00:04:26.080 align:start position:0%
all right so there you go cd social king
 

00:04:26.080 --> 00:04:28.310 align:start position:0%
all right so there you go cd social king
i'm<00:04:26.240><c> going</c><00:04:26.320><c> to</c><00:04:26.400><c> go</c><00:04:26.639><c> in</c><00:04:26.880><c> and</c><00:04:27.280><c> let's</c><00:04:27.520><c> just</c><00:04:27.680><c> do</c><00:04:27.919><c> a</c>

00:04:28.310 --> 00:04:28.320 align:start position:0%
i'm going to go in and let's just do a
 

00:04:28.320 --> 00:04:32.230 align:start position:0%
i'm going to go in and let's just do a
dir<00:04:29.120><c> to</c><00:04:29.360><c> show</c><00:04:29.600><c> the</c><00:04:30.080><c> the</c><00:04:30.720><c> repo</c><00:04:31.199><c> structure</c><00:04:32.080><c> we</c>

00:04:32.230 --> 00:04:32.240 align:start position:0%
dir to show the the repo structure we
 

00:04:32.240 --> 00:04:34.150 align:start position:0%
dir to show the the repo structure we
have<00:04:32.479><c> admin</c><00:04:32.800><c> back</c><00:04:33.040><c> end</c><00:04:33.280><c> admin</c><00:04:33.680><c> front</c><00:04:33.840><c> end</c><00:04:34.080><c> and</c>

00:04:34.150 --> 00:04:34.160 align:start position:0%
have admin back end admin front end and
 

00:04:34.160 --> 00:04:35.990 align:start position:0%
have admin back end admin front end and
we<00:04:34.240><c> have</c><00:04:34.400><c> the</c><00:04:34.560><c> proxy</c><00:04:35.120><c> for</c><00:04:35.280><c> this</c><00:04:35.520><c> example</c><00:04:35.840><c> we</c>

00:04:35.990 --> 00:04:36.000 align:start position:0%
we have the proxy for this example we
 

00:04:36.000 --> 00:04:37.909 align:start position:0%
we have the proxy for this example we
just<00:04:36.160><c> want</c><00:04:36.320><c> to</c><00:04:36.479><c> get</c><00:04:36.639><c> the</c><00:04:36.800><c> proxy</c><00:04:37.199><c> running</c><00:04:37.759><c> so</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
just want to get the proxy running so
 

00:04:37.919 --> 00:04:39.749 align:start position:0%
just want to get the proxy running so
we're<00:04:38.080><c> going</c><00:04:38.160><c> to</c><00:04:38.240><c> go</c><00:04:38.479><c> into</c><00:04:38.639><c> the</c><00:04:38.800><c> proxy</c><00:04:39.440><c> and</c>

00:04:39.749 --> 00:04:39.759 align:start position:0%
we're going to go into the proxy and
 

00:04:39.759 --> 00:04:43.030 align:start position:0%
we're going to go into the proxy and
we're<00:04:39.919><c> going</c><00:04:40.080><c> to</c><00:04:40.160><c> do</c><00:04:40.800><c> i</c><00:04:40.960><c> believe</c><00:04:41.360><c> npm</c><00:04:41.919><c> start</c>

00:04:43.030 --> 00:04:43.040 align:start position:0%
we're going to do i believe npm start
 

00:04:43.040 --> 00:04:46.390 align:start position:0%
we're going to do i believe npm start
okay<00:04:43.600><c> so</c><00:04:43.759><c> that's</c><00:04:44.080><c> perfect</c>

00:04:46.390 --> 00:04:46.400 align:start position:0%
okay so that's perfect
 

00:04:46.400 --> 00:04:48.150 align:start position:0%
okay so that's perfect
and<00:04:47.040><c> um</c>

00:04:48.150 --> 00:04:48.160 align:start position:0%
and um
 

00:04:48.160 --> 00:04:50.870 align:start position:0%
and um
i<00:04:48.400><c> think</c><00:04:48.800><c> it</c><00:04:48.960><c> should</c><00:04:49.199><c> be</c><00:04:49.520><c> running</c><00:04:49.919><c> now</c><00:04:50.320><c> so</c><00:04:50.720><c> by</c>

00:04:50.870 --> 00:04:50.880 align:start position:0%
i think it should be running now so by
 

00:04:50.880 --> 00:04:52.310 align:start position:0%
i think it should be running now so by
the<00:04:50.960><c> way</c><00:04:51.280><c> you</c><00:04:51.440><c> also</c><00:04:51.680><c> have</c><00:04:51.840><c> something</c><00:04:52.080><c> within</c>

00:04:52.310 --> 00:04:52.320 align:start position:0%
the way you also have something within
 

00:04:52.320 --> 00:04:54.870 align:start position:0%
the way you also have something within
the<00:04:52.479><c> shopify</c><00:04:53.040><c> partners</c><00:04:53.520><c> called</c><00:04:54.000><c> an</c><00:04:54.240><c> example</c>

00:04:54.870 --> 00:04:54.880 align:start position:0%
the shopify partners called an example
 

00:04:54.880 --> 00:04:57.350 align:start position:0%
the shopify partners called an example
store<00:04:55.600><c> or</c><00:04:55.759><c> a</c><00:04:55.919><c> development</c><00:04:56.479><c> store</c><00:04:56.880><c> and</c><00:04:57.120><c> and</c>

00:04:57.350 --> 00:04:57.360 align:start position:0%
store or a development store and and
 

00:04:57.360 --> 00:05:00.310 align:start position:0%
store or a development store and and
then<00:04:57.520><c> you</c><00:04:57.680><c> can</c><00:04:58.000><c> uh</c><00:04:58.400><c> install</c><00:04:58.960><c> this</c><00:04:59.680><c> social</c><00:05:00.080><c> king</c>

00:05:00.310 --> 00:05:00.320 align:start position:0%
then you can uh install this social king
 

00:05:00.320 --> 00:05:03.110 align:start position:0%
then you can uh install this social king
dev<00:05:00.639><c> app</c><00:05:01.039><c> on</c><00:05:01.280><c> your</c><00:05:01.520><c> example</c><00:05:02.000><c> store</c><00:05:02.639><c> all</c><00:05:02.800><c> right</c>

00:05:03.110 --> 00:05:03.120 align:start position:0%
dev app on your example store all right
 

00:05:03.120 --> 00:05:05.909 align:start position:0%
dev app on your example store all right
uh<00:05:03.440><c> so</c><00:05:03.600><c> let's</c><00:05:03.840><c> go</c><00:05:04.000><c> ahead</c><00:05:04.320><c> now</c><00:05:04.960><c> and</c><00:05:05.199><c> go</c><00:05:05.520><c> into</c>

00:05:05.909 --> 00:05:05.919 align:start position:0%
uh so let's go ahead now and go into
 

00:05:05.919 --> 00:05:07.990 align:start position:0%
uh so let's go ahead now and go into
jungle<00:05:06.320><c> dash</c><00:05:06.720><c> navigator</c>

00:05:07.990 --> 00:05:08.000 align:start position:0%
jungle dash navigator
 

00:05:08.000 --> 00:05:11.350 align:start position:0%
jungle dash navigator
um<00:05:08.639><c> slash</c><00:05:09.120><c> community</c><00:05:09.759><c> slash</c><00:05:10.160><c> connect</c>

00:05:11.350 --> 00:05:11.360 align:start position:0%
um slash community slash connect
 

00:05:11.360 --> 00:05:13.430 align:start position:0%
um slash community slash connect
we'll<00:05:11.520><c> go</c><00:05:11.759><c> in</c><00:05:12.000><c> this</c><00:05:12.240><c> is</c><00:05:12.320><c> an</c><00:05:12.479><c> example</c><00:05:12.880><c> of</c><00:05:12.960><c> a</c><00:05:13.039><c> demo</c>

00:05:13.430 --> 00:05:13.440 align:start position:0%
we'll go in this is an example of a demo
 

00:05:13.440 --> 00:05:15.909 align:start position:0%
we'll go in this is an example of a demo
store<00:05:14.000><c> right</c><00:05:14.240><c> i'm</c><00:05:14.479><c> linking</c><00:05:14.880><c> to</c><00:05:15.039><c> my</c><00:05:15.280><c> tribe</c><00:05:15.680><c> over</c>

00:05:15.909 --> 00:05:15.919 align:start position:0%
store right i'm linking to my tribe over
 

00:05:15.919 --> 00:05:16.710 align:start position:0%
store right i'm linking to my tribe over
here

00:05:16.710 --> 00:05:16.720 align:start position:0%
here
 

00:05:16.720 --> 00:05:19.510 align:start position:0%
here
and<00:05:17.120><c> while</c><00:05:17.360><c> it's</c><00:05:17.600><c> trying</c><00:05:17.840><c> to</c><00:05:17.919><c> hit</c><00:05:18.160><c> my</c><00:05:18.400><c> tribe</c><00:05:19.120><c> on</c>

00:05:19.510 --> 00:05:19.520 align:start position:0%
and while it's trying to hit my tribe on
 

00:05:19.520 --> 00:05:21.590 align:start position:0%
and while it's trying to hit my tribe on
slash<00:05:19.919><c> community</c><00:05:20.400><c> slash</c><00:05:20.720><c> connect</c><00:05:21.440><c> it</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
slash community slash connect it
 

00:05:21.600 --> 00:05:23.830 align:start position:0%
slash community slash connect it
successfully<00:05:22.400><c> runs</c><00:05:22.800><c> in</c><00:05:23.039><c> development</c><00:05:23.520><c> mode</c><00:05:23.680><c> so</c>

00:05:23.830 --> 00:05:23.840 align:start position:0%
successfully runs in development mode so
 

00:05:23.840 --> 00:05:25.590 align:start position:0%
successfully runs in development mode so
that's<00:05:24.080><c> pretty</c><00:05:24.240><c> much</c><00:05:24.560><c> the</c><00:05:24.800><c> basics</c><00:05:25.199><c> of</c><00:05:25.280><c> how</c><00:05:25.440><c> you</c>

00:05:25.590 --> 00:05:25.600 align:start position:0%
that's pretty much the basics of how you
 

00:05:25.600 --> 00:05:27.830 align:start position:0%
that's pretty much the basics of how you
do<00:05:25.840><c> it</c><00:05:26.320><c> if</c><00:05:26.479><c> you</c><00:05:26.560><c> need</c><00:05:27.039><c> access</c><00:05:27.280><c> to</c><00:05:27.440><c> the</c><00:05:27.520><c> repo</c>

00:05:27.830 --> 00:05:27.840 align:start position:0%
do it if you need access to the repo
 

00:05:27.840 --> 00:05:29.749 align:start position:0%
do it if you need access to the repo
again<00:05:28.160><c> just</c><00:05:28.639><c> put</c><00:05:28.800><c> your</c><00:05:28.960><c> github</c><00:05:29.360><c> username</c>

00:05:29.749 --> 00:05:29.759 align:start position:0%
again just put your github username
 

00:05:29.759 --> 00:05:31.430 align:start position:0%
again just put your github username
below<00:05:30.160><c> and</c><00:05:30.320><c> i</c><00:05:30.479><c> really</c><00:05:30.639><c> hope</c><00:05:30.800><c> you</c><00:05:31.039><c> enjoy</c><00:05:31.280><c> this</c>

00:05:31.430 --> 00:05:31.440 align:start position:0%
below and i really hope you enjoy this
 

00:05:31.440 --> 00:05:33.830 align:start position:0%
below and i really hope you enjoy this
series<00:05:31.840><c> feel</c><00:05:32.000><c> free</c><00:05:32.240><c> to</c><00:05:32.639><c> ask</c><00:05:32.960><c> questions</c><00:05:33.440><c> and</c><00:05:33.680><c> i</c>

00:05:33.830 --> 00:05:33.840 align:start position:0%
series feel free to ask questions and i
 

00:05:33.840 --> 00:05:38.120 align:start position:0%
series feel free to ask questions and i
really<00:05:34.080><c> hope</c><00:05:34.240><c> you</c><00:05:34.400><c> get</c><00:05:34.560><c> a</c><00:05:34.639><c> lot</c><00:05:34.880><c> out</c><00:05:34.960><c> of</c><00:05:35.120><c> it</c>

