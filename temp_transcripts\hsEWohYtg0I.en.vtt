WEBVTT
Kind: captions
Language: en

00:00:00.780 --> 00:00:03.110 align:start position:0%
 
hello<00:00:01.319><c> everyone</c><00:00:01.700><c> welcome</c><00:00:02.700><c> to</c><00:00:02.940><c> another</c>

00:00:03.110 --> 00:00:03.120 align:start position:0%
hello everyone welcome to another
 

00:00:03.120 --> 00:00:05.030 align:start position:0%
hello everyone welcome to another
tutorial<00:00:03.659><c> with</c><00:00:03.840><c> llama</c><00:00:04.140><c> index</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
tutorial with llama index
 

00:00:05.040 --> 00:00:07.490 align:start position:0%
tutorial with llama index
today<00:00:05.580><c> we</c><00:00:06.000><c> will</c><00:00:06.120><c> explore</c><00:00:06.480><c> a</c><00:00:06.899><c> unique</c><00:00:07.200><c> type</c><00:00:07.319><c> of</c>

00:00:07.490 --> 00:00:07.500 align:start position:0%
today we will explore a unique type of
 

00:00:07.500 --> 00:00:09.470 align:start position:0%
today we will explore a unique type of
search<00:00:07.680><c> system</c><00:00:07.980><c> in</c><00:00:08.220><c> lava</c><00:00:08.580><c> index</c><00:00:08.820><c> known</c><00:00:09.300><c> as</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
search system in lava index known as
 

00:00:09.480 --> 00:00:13.370 align:start position:0%
search system in lava index known as
custom<00:00:09.840><c> retrieval</c><00:00:10.620><c> or</c><00:00:11.460><c> hybrid</c><00:00:11.940><c> search</c><00:00:12.179><c> engine</c>

00:00:13.370 --> 00:00:13.380 align:start position:0%
custom retrieval or hybrid search engine
 

00:00:13.380 --> 00:00:15.650 align:start position:0%
custom retrieval or hybrid search engine
in<00:00:13.799><c> our</c><00:00:13.920><c> previous</c><00:00:14.099><c> tutorials</c><00:00:14.759><c> we</c><00:00:15.240><c> primarily</c>

00:00:15.650 --> 00:00:15.660 align:start position:0%
in our previous tutorials we primarily
 

00:00:15.660 --> 00:00:17.930 align:start position:0%
in our previous tutorials we primarily
use<00:00:15.839><c> vectors</c><00:00:16.320><c> such</c><00:00:16.560><c> based</c><00:00:17.340><c> on</c><00:00:17.400><c> embedding</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
use vectors such based on embedding
 

00:00:17.940 --> 00:00:20.570 align:start position:0%
use vectors such based on embedding
similarity<00:00:18.480><c> however</c><00:00:19.320><c> relies</c><00:00:19.800><c> solely</c><00:00:20.160><c> on</c><00:00:20.279><c> its</c>

00:00:20.570 --> 00:00:20.580 align:start position:0%
similarity however relies solely on its
 

00:00:20.580 --> 00:00:24.890 align:start position:0%
similarity however relies solely on its
method<00:00:20.880><c> may</c><00:00:21.359><c> not</c><00:00:21.539><c> always</c><00:00:21.779><c> be</c><00:00:22.680><c> correct</c>

00:00:24.890 --> 00:00:24.900 align:start position:0%
method may not always be correct
 

00:00:24.900 --> 00:00:27.650 align:start position:0%
method may not always be correct
so<00:00:25.439><c> let's</c><00:00:25.800><c> say</c><00:00:26.039><c> you</c><00:00:26.699><c> are</c><00:00:26.880><c> looking</c><00:00:27.000><c> for</c><00:00:27.300><c> some</c>

00:00:27.650 --> 00:00:27.660 align:start position:0%
so let's say you are looking for some
 

00:00:27.660 --> 00:00:30.769 align:start position:0%
so let's say you are looking for some
information<00:00:27.840><c> from</c><00:00:28.260><c> an</c><00:00:28.439><c> essay</c><00:00:28.740><c> by</c><00:00:29.220><c> Ball</c><00:00:29.580><c> Ground</c>

00:00:30.769 --> 00:00:30.779 align:start position:0%
information from an essay by Ball Ground
 

00:00:30.779 --> 00:00:33.190 align:start position:0%
information from an essay by Ball Ground
regarding<00:00:31.560><c> these</c><00:00:31.679><c> activities</c><00:00:32.040><c> afterwards</c>

00:00:33.190 --> 00:00:33.200 align:start position:0%
regarding these activities afterwards
 

00:00:33.200 --> 00:00:35.569 align:start position:0%
regarding these activities afterwards
time<00:00:34.200><c> at</c><00:00:34.380><c> Yale</c>

00:00:35.569 --> 00:00:35.579 align:start position:0%
time at Yale
 

00:00:35.579 --> 00:00:37.610 align:start position:0%
time at Yale
our<00:00:36.120><c> different</c><00:00:36.420><c> search</c><00:00:36.719><c> methods</c><00:00:37.440><c> would</c>

00:00:37.610 --> 00:00:37.620 align:start position:0%
our different search methods would
 

00:00:37.620 --> 00:00:39.950 align:start position:0%
our different search methods would
approach<00:00:38.040><c> this</c><00:00:38.219><c> time</c><00:00:38.399><c> differently</c><00:00:39.000><c> let's</c><00:00:39.780><c> say</c>

00:00:39.950 --> 00:00:39.960 align:start position:0%
approach this time differently let's say
 

00:00:39.960 --> 00:00:43.310 align:start position:0%
approach this time differently let's say
if<00:00:40.140><c> we</c><00:00:40.320><c> employ</c><00:00:40.620><c> a</c><00:00:41.460><c> keyword</c><00:00:42.059><c> search</c><00:00:42.300><c> the</c><00:00:43.079><c> system</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
if we employ a keyword search the system
 

00:00:43.320 --> 00:00:45.410 align:start position:0%
if we employ a keyword search the system
will<00:00:43.620><c> search</c><00:00:43.860><c> for</c><00:00:44.160><c> exact</c><00:00:44.579><c> matching</c><00:00:45.000><c> expecting</c>

00:00:45.410 --> 00:00:45.420 align:start position:0%
will search for exact matching expecting
 

00:00:45.420 --> 00:00:47.990 align:start position:0%
will search for exact matching expecting
results<00:00:45.540><c> that</c><00:00:46.020><c> is</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
results that is
 

00:00:48.000 --> 00:00:50.270 align:start position:0%
results that is
however<00:00:48.539><c> if</c><00:00:48.899><c> programs</c><00:00:49.440><c> say</c><00:00:49.739><c> it</c><00:00:49.920><c> does</c><00:00:50.100><c> not</c>

00:00:50.270 --> 00:00:50.280 align:start position:0%
however if programs say it does not
 

00:00:50.280 --> 00:00:53.029 align:start position:0%
however if programs say it does not
directly<00:00:50.879><c> reference</c><00:00:51.780><c> real</c><00:00:52.020><c> a</c><00:00:52.500><c> keyword</c><00:00:52.800><c> search</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
directly reference real a keyword search
 

00:00:53.039 --> 00:00:55.130 align:start position:0%
directly reference real a keyword search
might<00:00:53.399><c> not</c><00:00:53.579><c> return</c><00:00:53.820><c> any</c><00:00:54.180><c> relevant</c><00:00:54.960><c> results</c>

00:00:55.130 --> 00:00:55.140 align:start position:0%
might not return any relevant results
 

00:00:55.140 --> 00:00:57.830 align:start position:0%
might not return any relevant results
living<00:00:55.739><c> with</c><00:00:56.039><c> an</c><00:00:56.280><c> empty</c><00:00:56.520><c> set</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
living with an empty set
 

00:00:57.840 --> 00:01:00.770 align:start position:0%
living with an empty set
and<00:00:58.320><c> contracts</c>

00:01:00.770 --> 00:01:00.780 align:start position:0%
and contracts
 

00:01:00.780 --> 00:01:02.930 align:start position:0%
and contracts
um<00:01:00.899><c> let's</c><00:01:01.320><c> say</c><00:01:01.500><c> a</c><00:01:01.860><c> vector</c><00:01:02.219><c> searching</c>

00:01:02.930 --> 00:01:02.940 align:start position:0%
um let's say a vector searching
 

00:01:02.940 --> 00:01:05.210 align:start position:0%
um let's say a vector searching
understands<00:01:03.840><c> the</c><00:01:04.019><c> context</c><00:01:04.500><c> or</c><00:01:04.739><c> meaning</c><00:01:04.979><c> of</c>

00:01:05.210 --> 00:01:05.220 align:start position:0%
understands the context or meaning of
 

00:01:05.220 --> 00:01:07.010 align:start position:0%
understands the context or meaning of
your<00:01:05.339><c> file</c><00:01:05.640><c> it</c><00:01:06.060><c> can</c><00:01:06.299><c> provide</c><00:01:06.479><c> your</c><00:01:06.659><c> advantages</c>

00:01:07.010 --> 00:01:07.020 align:start position:0%
your file it can provide your advantages
 

00:01:07.020 --> 00:01:09.830 align:start position:0%
your file it can provide your advantages
even<00:01:07.500><c> without</c><00:01:07.680><c> a</c><00:01:07.979><c> direct</c><00:01:08.159><c> mention</c><00:01:08.400><c> of</c><00:01:08.700><c> deal</c><00:01:09.360><c> it</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
even without a direct mention of deal it
 

00:01:09.840 --> 00:01:12.170 align:start position:0%
even without a direct mention of deal it
achieves<00:01:10.200><c> this</c><00:01:10.380><c> by</c><00:01:10.799><c> utilizing</c><00:01:11.520><c> embeddings</c><00:01:11.939><c> to</c>

00:01:12.170 --> 00:01:12.180 align:start position:0%
achieves this by utilizing embeddings to
 

00:01:12.180 --> 00:01:14.510 align:start position:0%
achieves this by utilizing embeddings to
manager<00:01:12.600><c> exact</c><00:01:13.140><c> context</c><00:01:13.560><c> of</c><00:01:13.740><c> websites</c><00:01:14.280><c> it's</c>

00:01:14.510 --> 00:01:14.520 align:start position:0%
manager exact context of websites it's
 

00:01:14.520 --> 00:01:17.330 align:start position:0%
manager exact context of websites it's
similar<00:01:14.880><c> context</c><00:01:15.240><c> in</c><00:01:15.420><c> available</c><00:01:15.659><c> data</c><00:01:16.680><c> this</c>

00:01:17.330 --> 00:01:17.340 align:start position:0%
similar context in available data this
 

00:01:17.340 --> 00:01:19.490 align:start position:0%
similar context in available data this
way<00:01:17.580><c> it</c><00:01:17.760><c> may</c><00:01:18.060><c> permission</c><00:01:18.960><c> meaningful</c>

00:01:19.490 --> 00:01:19.500 align:start position:0%
way it may permission meaningful
 

00:01:19.500 --> 00:01:21.050 align:start position:0%
way it may permission meaningful
response<00:01:19.920><c> regardless</c><00:01:20.400><c> of</c><00:01:20.580><c> whether</c><00:01:20.759><c> the</c>

00:01:21.050 --> 00:01:21.060 align:start position:0%
response regardless of whether the
 

00:01:21.060 --> 00:01:24.289 align:start position:0%
response regardless of whether the
keyword<00:01:21.420><c> ale</c><00:01:21.840><c> is</c><00:01:21.960><c> present</c><00:01:22.200><c> in</c><00:01:22.380><c> the</c><00:01:22.500><c> text</c>

00:01:24.289 --> 00:01:24.299 align:start position:0%
keyword ale is present in the text
 

00:01:24.299 --> 00:01:26.270 align:start position:0%
keyword ale is present in the text
so<00:01:24.780><c> how</c><00:01:25.020><c> does</c><00:01:25.200><c> our</c><00:01:25.380><c> hybrid</c><00:01:25.680><c> search</c><00:01:25.920><c> handle</c>

00:01:26.270 --> 00:01:26.280 align:start position:0%
so how does our hybrid search handle
 

00:01:26.280 --> 00:01:29.030 align:start position:0%
so how does our hybrid search handle
this<00:01:26.460><c> situation</c><00:01:26.820><c> if</c><00:01:27.360><c> we</c><00:01:27.540><c> set</c><00:01:27.720><c> it</c><00:01:27.900><c> and</c><00:01:28.439><c> it</c><00:01:28.920><c> will</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
this situation if we set it and it will
 

00:01:29.040 --> 00:01:31.969 align:start position:0%
this situation if we set it and it will
search<00:01:29.280><c> for</c><00:01:30.000><c> results</c><00:01:30.420><c> that</c><00:01:30.960><c> match</c><00:01:31.500><c> both</c><00:01:31.799><c> the</c>

00:01:31.969 --> 00:01:31.979 align:start position:0%
search for results that match both the
 

00:01:31.979 --> 00:01:34.429 align:start position:0%
search for results that match both the
keyboard<00:01:32.280><c> and</c><00:01:32.640><c> vectors</c><00:01:33.600><c> are</c><00:01:33.780><c> expected</c><00:01:34.200><c> them</c>

00:01:34.429 --> 00:01:34.439 align:start position:0%
keyboard and vectors are expected them
 

00:01:34.439 --> 00:01:36.050 align:start position:0%
keyboard and vectors are expected them
since<00:01:34.740><c> there</c><00:01:34.920><c> is</c><00:01:35.040><c> no</c><00:01:35.220><c> direct</c><00:01:35.400><c> mention</c><00:01:35.759><c> of</c><00:01:35.880><c> real</c>

00:01:36.050 --> 00:01:36.060 align:start position:0%
since there is no direct mention of real
 

00:01:36.060 --> 00:01:37.910 align:start position:0%
since there is no direct mention of real
in<00:01:36.299><c> the</c><00:01:36.420><c> essay</c><00:01:36.720><c> this</c><00:01:37.020><c> convolution</c><00:01:37.439><c> would</c><00:01:37.740><c> not</c>

00:01:37.910 --> 00:01:37.920 align:start position:0%
in the essay this convolution would not
 

00:01:37.920 --> 00:01:38.749 align:start position:0%
in the essay this convolution would not
result

00:01:38.749 --> 00:01:38.759 align:start position:0%
result
 

00:01:38.759 --> 00:01:41.810 align:start position:0%
result
any<00:01:39.420><c> results</c>

00:01:41.810 --> 00:01:41.820 align:start position:0%
 
 

00:01:41.820 --> 00:01:44.810 align:start position:0%
 
so<00:01:42.479><c> through</c><00:01:42.960><c> this</c><00:01:43.200><c> example</c>

00:01:44.810 --> 00:01:44.820 align:start position:0%
so through this example
 

00:01:44.820 --> 00:01:46.710 align:start position:0%
so through this example
system

00:01:46.710 --> 00:01:46.720 align:start position:0%
system
 

00:01:46.720 --> 00:01:53.749 align:start position:0%
system
[Music]

00:01:53.749 --> 00:01:53.759 align:start position:0%
 
 

00:01:53.759 --> 00:01:55.730 align:start position:0%
 
depending<00:01:54.420><c> on</c><00:01:54.540><c> circumstances</c><00:01:55.140><c> you</c><00:01:55.680><c> can</c>

00:01:55.730 --> 00:01:55.740 align:start position:0%
depending on circumstances you can
 

00:01:55.740 --> 00:01:59.450 align:start position:0%
depending on circumstances you can
select<00:01:56.700><c> to</c><00:01:56.880><c> use</c><00:01:57.479><c> either</c><00:01:57.960><c> both</c><00:01:58.680><c> keyword</c><00:01:59.220><c> and</c>

00:01:59.450 --> 00:01:59.460 align:start position:0%
select to use either both keyword and
 

00:01:59.460 --> 00:02:02.510 align:start position:0%
select to use either both keyword and
Vector<00:01:59.820><c> sets</c><00:02:00.240><c> or</c><00:02:00.540><c> just</c><00:02:00.720><c> one</c><00:02:00.899><c> of</c><00:02:01.079><c> them</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
Vector sets or just one of them
 

00:02:02.520 --> 00:02:04.969 align:start position:0%
Vector sets or just one of them
in<00:02:02.939><c> this</c><00:02:03.119><c> tutorial</c><00:02:03.479><c> we'll</c><00:02:03.899><c> guide</c><00:02:04.200><c> you</c><00:02:04.380><c> on</c><00:02:04.680><c> how</c>

00:02:04.969 --> 00:02:04.979 align:start position:0%
in this tutorial we'll guide you on how
 

00:02:04.979 --> 00:02:07.130 align:start position:0%
in this tutorial we'll guide you on how
to<00:02:05.159><c> implement</c><00:02:05.759><c> this</c>

00:02:07.130 --> 00:02:07.140 align:start position:0%
to implement this
 

00:02:07.140 --> 00:02:09.589 align:start position:0%
to implement this
flexibility<00:02:07.799><c> so</c><00:02:08.580><c> you</c><00:02:08.759><c> can</c><00:02:08.880><c> get</c><00:02:09.060><c> the</c><00:02:09.239><c> most</c><00:02:09.420><c> out</c>

00:02:09.589 --> 00:02:09.599 align:start position:0%
flexibility so you can get the most out
 

00:02:09.599 --> 00:02:11.029 align:start position:0%
flexibility so you can get the most out
of<00:02:09.780><c> yourself</c><00:02:09.959><c> system</c>

00:02:11.029 --> 00:02:11.039 align:start position:0%
of yourself system
 

00:02:11.039 --> 00:02:13.850 align:start position:0%
of yourself system
so<00:02:11.580><c> let's</c><00:02:11.760><c> get</c><00:02:12.060><c> started</c><00:02:12.360><c> with</c><00:02:12.540><c> it</c><00:02:13.080><c> let's</c><00:02:13.620><c> start</c>

00:02:13.850 --> 00:02:13.860 align:start position:0%
so let's get started with it let's start
 

00:02:13.860 --> 00:02:16.850 align:start position:0%
so let's get started with it let's start
with<00:02:14.040><c> the</c><00:02:14.400><c> installing</c><00:02:15.000><c> gamma</c><00:02:15.300><c> index</c><00:02:15.599><c> and</c><00:02:16.560><c> then</c>

00:02:16.850 --> 00:02:16.860 align:start position:0%
with the installing gamma index and then
 

00:02:16.860 --> 00:02:20.050 align:start position:0%
with the installing gamma index and then
we<00:02:17.760><c> even</c><00:02:18.000><c> import</c>

00:02:20.050 --> 00:02:20.060 align:start position:0%
we even import
 

00:02:20.060 --> 00:02:24.830 align:start position:0%
we even import
login<00:02:21.560><c> and</c><00:02:22.560><c> set</c><00:02:22.920><c> the</c><00:02:23.099><c> logging</c><00:02:23.580><c> things</c>

00:02:24.830 --> 00:02:24.840 align:start position:0%
login and set the logging things
 

00:02:24.840 --> 00:02:28.250 align:start position:0%
login and set the logging things
and<00:02:25.620><c> then</c><00:02:25.980><c> we</c><00:02:26.420><c> got</c><00:02:27.420><c> all</c><00:02:27.660><c> the</c><00:02:27.840><c> things</c><00:02:27.900><c> related</c>

00:02:28.250 --> 00:02:28.260 align:start position:0%
and then we got all the things related
 

00:02:28.260 --> 00:02:30.830 align:start position:0%
and then we got all the things related
to<00:02:28.500><c> our</c><00:02:29.340><c> customer</c><00:02:29.760><c> and</c><00:02:30.120><c> I</c><00:02:30.300><c> understand</c><00:02:30.420><c> then</c>

00:02:30.830 --> 00:02:30.840 align:start position:0%
to our customer and I understand then
 

00:02:30.840 --> 00:02:32.630 align:start position:0%
to our customer and I understand then
simple<00:02:31.200><c> keyword</c><00:02:31.560><c> table</c><00:02:31.800><c> next</c><00:02:32.099><c> to</c><00:02:32.280><c> build</c><00:02:32.459><c> their</c>

00:02:32.630 --> 00:02:32.640 align:start position:0%
simple keyword table next to build their
 

00:02:32.640 --> 00:02:35.750 align:start position:0%
simple keyword table next to build their
indexes<00:02:33.379><c> response</c><00:02:34.379><c> insurance</c><00:02:34.520><c> and</c><00:02:35.520><c> service</c>

00:02:35.750 --> 00:02:35.760 align:start position:0%
indexes response insurance and service
 

00:02:35.760 --> 00:02:37.910 align:start position:0%
indexes response insurance and service
context<00:02:36.239><c> and</c><00:02:36.420><c> storage</c><00:02:36.660><c> context</c><00:02:37.200><c> for</c><00:02:37.379><c> setting</c>

00:02:37.910 --> 00:02:37.920 align:start position:0%
context and storage context for setting
 

00:02:37.920 --> 00:02:43.130 align:start position:0%
context and storage context for setting
our<00:02:38.220><c> different</c><00:02:39.560><c> service</c><00:02:40.700><c> or</c><00:02:41.700><c> storage</c><00:02:42.660><c> index</c>

00:02:43.130 --> 00:02:43.140 align:start position:0%
our different service or storage index
 

00:02:43.140 --> 00:02:45.650 align:start position:0%
our different service or storage index
as<00:02:43.379><c> well</c><00:02:43.560><c> and</c><00:02:44.519><c> you</c><00:02:44.940><c> can</c><00:02:45.120><c> check</c><00:02:45.239><c> my</c><00:02:45.480><c> previous</c>

00:02:45.650 --> 00:02:45.660 align:start position:0%
as well and you can check my previous
 

00:02:45.660 --> 00:02:47.630 align:start position:0%
as well and you can check my previous
videos<00:02:46.019><c> to</c><00:02:46.319><c> understand</c><00:02:46.440><c> all</c><00:02:46.860><c> these</c><00:02:47.220><c> different</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
videos to understand all these different
 

00:02:47.640 --> 00:02:49.850 align:start position:0%
videos to understand all these different
things<00:02:47.940><c> uh</c><00:02:48.660><c> positivities</c><00:02:49.260><c> explain</c><00:02:49.560><c> different</c>

00:02:49.850 --> 00:02:49.860 align:start position:0%
things uh positivities explain different
 

00:02:49.860 --> 00:02:52.729 align:start position:0%
things uh positivities explain different
indexes<00:02:50.519><c> and</c><00:02:50.700><c> storage</c><00:02:51.660><c> context</c><00:02:52.260><c> service</c>

00:02:52.729 --> 00:02:52.739 align:start position:0%
indexes and storage context service
 

00:02:52.739 --> 00:02:57.630 align:start position:0%
indexes and storage context service
context<00:02:53.360><c> and</c><00:02:54.360><c> or</c><00:02:54.720><c> other</c><00:02:55.019><c> key</c><00:02:55.319><c> components</c>

00:02:57.630 --> 00:02:57.640 align:start position:0%
context and or other key components
 

00:02:57.640 --> 00:03:04.550 align:start position:0%
context and or other key components
[Music]

00:03:04.550 --> 00:03:04.560 align:start position:0%
 
 

00:03:04.560 --> 00:03:07.670 align:start position:0%
 
[Music]

00:03:07.670 --> 00:03:07.680 align:start position:0%
[Music]
 

00:03:07.680 --> 00:03:24.110 align:start position:0%
[Music]
so<00:03:08.220><c> yeah</c><00:03:08.459><c> we</c><00:03:08.700><c> need</c>

00:03:24.110 --> 00:03:24.120 align:start position:0%
 
 

00:03:24.120 --> 00:03:28.430 align:start position:0%
 
this<00:03:24.980><c> and</c><00:03:25.980><c> we</c><00:03:26.340><c> also</c><00:03:26.700><c> need</c><00:03:26.940><c> the</c><00:03:27.300><c> data</c><00:03:27.840><c> set</c><00:03:28.140><c> for</c>

00:03:28.430 --> 00:03:28.440 align:start position:0%
this and we also need the data set for
 

00:03:28.440 --> 00:03:30.350 align:start position:0%
this and we also need the data set for
this<00:03:28.800><c> program</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
this program
 

00:03:30.360 --> 00:03:36.670 align:start position:0%
this program
index

00:03:36.670 --> 00:03:36.680 align:start position:0%
 
 

00:03:36.680 --> 00:03:38.390 align:start position:0%
 
products

00:03:38.390 --> 00:03:38.400 align:start position:0%
products
 

00:03:38.400 --> 00:03:39.410 align:start position:0%
products
um

00:03:39.410 --> 00:03:39.420 align:start position:0%
um
 

00:03:39.420 --> 00:03:49.009 align:start position:0%
um
it's<00:03:40.819><c> a</c><00:03:41.819><c> data</c><00:03:42.239><c> folder</c><00:03:42.540><c> of</c>

00:03:49.009 --> 00:03:49.019 align:start position:0%
 
 

00:03:49.019 --> 00:04:01.610 align:start position:0%
 
scheme<00:03:49.980><c> that</c><00:03:50.099><c> in</c><00:03:50.340><c> data</c><00:03:51.060><c> folder</c>

00:04:01.610 --> 00:04:01.620 align:start position:0%
 
 

00:04:01.620 --> 00:04:07.930 align:start position:0%
 
and

00:04:07.930 --> 00:04:07.940 align:start position:0%
 
 

00:04:07.940 --> 00:04:11.560 align:start position:0%
 
Dot<00:04:08.940><c> txt</c>

00:04:11.560 --> 00:04:11.570 align:start position:0%
 
 

00:04:11.570 --> 00:04:13.550 align:start position:0%
 
[Music]

00:04:13.550 --> 00:04:13.560 align:start position:0%
[Music]
 

00:04:13.560 --> 00:04:17.170 align:start position:0%
[Music]
so

00:04:17.170 --> 00:04:17.180 align:start position:0%
 
 

00:04:17.180 --> 00:04:20.810 align:start position:0%
 
yeah<00:04:18.180><c> so</c><00:04:18.540><c> that</c><00:04:18.959><c> is</c><00:04:19.139><c> a</c><00:04:19.260><c> delivery</c><00:04:19.620><c> and</c><00:04:20.340><c> then</c><00:04:20.579><c> we</c>

00:04:20.810 --> 00:04:20.820 align:start position:0%
yeah so that is a delivery and then we
 

00:04:20.820 --> 00:04:24.170 align:start position:0%
yeah so that is a delivery and then we
make<00:04:21.239><c> the</c><00:04:21.680><c> documents</c><00:04:22.680><c> and</c><00:04:23.340><c> set</c><00:04:23.639><c> the</c><00:04:23.820><c> default</c>

00:04:24.170 --> 00:04:24.180 align:start position:0%
make the documents and set the default
 

00:04:24.180 --> 00:04:27.650 align:start position:0%
make the documents and set the default
sample<00:04:24.660><c> size</c><00:04:24.780><c> you</c><00:04:25.560><c> can</c><00:04:25.680><c> read</c><00:04:25.919><c> the</c><00:04:26.100><c> boxer</c><00:04:26.699><c> make</c>

00:04:27.650 --> 00:04:27.660 align:start position:0%
sample size you can read the boxer make
 

00:04:27.660 --> 00:04:29.330 align:start position:0%
sample size you can read the boxer make
notes<00:04:28.259><c> accordingly</c>

00:04:29.330 --> 00:04:29.340 align:start position:0%
notes accordingly
 

00:04:29.340 --> 00:04:35.629 align:start position:0%
notes accordingly
so

00:04:35.629 --> 00:04:35.639 align:start position:0%
 
 

00:04:35.639 --> 00:04:37.909 align:start position:0%
 
and<00:04:36.180><c> create</c><00:04:36.419><c> a</c><00:04:36.660><c> vectors</c><00:04:37.020><c> for</c><00:04:37.199><c> index</c><00:04:37.500><c> as</c><00:04:37.680><c> well</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
and create a vectors for index as well
 

00:04:37.919 --> 00:04:41.170 align:start position:0%
and create a vectors for index as well
as<00:04:38.040><c> a</c><00:04:38.460><c> simple</c><00:04:38.699><c> keyword</c><00:04:39.120><c> tablet</c><00:04:40.080><c> X</c>

00:04:41.170 --> 00:04:41.180 align:start position:0%
as a simple keyword tablet X
 

00:04:41.180 --> 00:04:43.969 align:start position:0%
as a simple keyword tablet X
let's<00:04:42.180><c> create</c><00:04:42.419><c> them</c>

00:04:43.969 --> 00:04:43.979 align:start position:0%
let's create them
 

00:04:43.979 --> 00:04:45.110 align:start position:0%
let's create them
um

00:04:45.110 --> 00:04:45.120 align:start position:0%
um
 

00:04:45.120 --> 00:04:49.030 align:start position:0%
um
and<00:04:45.720><c> then</c><00:04:46.440><c> one</c><00:04:46.919><c> is</c><00:04:47.100><c> all</c><00:04:47.400><c> this</c><00:04:47.580><c> index</c><00:04:47.880><c> separated</c>

00:04:49.030 --> 00:04:49.040 align:start position:0%
and then one is all this index separated
 

00:04:49.040 --> 00:04:52.189 align:start position:0%
and then one is all this index separated
and<00:04:50.040><c> so</c><00:04:50.639><c> uh</c><00:04:51.060><c> to</c><00:04:51.360><c> create</c><00:04:51.720><c> the</c><00:04:52.020><c> customer</c>

00:04:52.189 --> 00:04:52.199 align:start position:0%
and so uh to create the customer
 

00:04:52.199 --> 00:04:54.290 align:start position:0%
and so uh to create the customer
retriever<00:04:52.740><c> or</c><00:04:52.979><c> the</c><00:04:53.160><c> hybrid</c><00:04:53.580><c> search</c><00:04:53.820><c> that</c>

00:04:54.290 --> 00:04:54.300 align:start position:0%
retriever or the hybrid search that
 

00:04:54.300 --> 00:04:57.170 align:start position:0%
retriever or the hybrid search that
involves<00:04:54.660><c> both</c><00:04:54.960><c> both</c><00:04:55.440><c> the</c><00:04:55.979><c> usage</c><00:04:56.820><c> of</c><00:04:57.000><c> the</c>

00:04:57.170 --> 00:04:57.180 align:start position:0%
involves both both the usage of the
 

00:04:57.180 --> 00:04:59.210 align:start position:0%
involves both both the usage of the
trendex<00:04:57.600><c> and</c><00:04:57.780><c> period</c><00:04:58.080><c> index</c><00:04:58.380><c> basically</c><00:04:59.040><c> we</c>

00:04:59.210 --> 00:04:59.220 align:start position:0%
trendex and period index basically we
 

00:04:59.220 --> 00:05:01.670 align:start position:0%
trendex and period index basically we
will<00:04:59.400><c> retrieve</c><00:04:59.940><c> uh</c><00:05:00.360><c> the</c><00:05:00.600><c> nodes</c><00:05:00.840><c> using</c><00:05:01.500><c> the</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
will retrieve uh the nodes using the
 

00:05:01.680 --> 00:05:03.950 align:start position:0%
will retrieve uh the nodes using the
animating<00:05:02.160><c> similarity</c><00:05:02.759><c> as</c><00:05:03.060><c> well</c><00:05:03.240><c> as</c><00:05:03.419><c> uh</c>

00:05:03.950 --> 00:05:03.960 align:start position:0%
animating similarity as well as uh
 

00:05:03.960 --> 00:05:06.790 align:start position:0%
animating similarity as well as uh
specific<00:05:04.800><c> keywords</c><00:05:05.280><c> from</c><00:05:05.520><c> the</c>

00:05:06.790 --> 00:05:06.800 align:start position:0%
specific keywords from the
 

00:05:06.800 --> 00:05:09.770 align:start position:0%
specific keywords from the
Keyword<00:05:07.800><c> Index</c><00:05:08.280><c> answer</c><00:05:08.520><c> so</c><00:05:08.940><c> we</c><00:05:09.479><c> need</c><00:05:09.600><c> to</c>

00:05:09.770 --> 00:05:09.780 align:start position:0%
Keyword Index answer so we need to
 

00:05:09.780 --> 00:05:12.129 align:start position:0%
Keyword Index answer so we need to
create<00:05:09.960><c> uh</c><00:05:10.800><c> two</c><00:05:11.100><c> reverse</c><00:05:11.580><c> Financial</c>

00:05:12.129 --> 00:05:12.139 align:start position:0%
create uh two reverse Financial
 

00:05:12.139 --> 00:05:15.290 align:start position:0%
create uh two reverse Financial
territory<00:05:13.139><c> one</c><00:05:13.380><c> and</c><00:05:13.560><c> keyword</c><00:05:14.040><c> retrieval</c><00:05:14.639><c> and</c>

00:05:15.290 --> 00:05:15.300 align:start position:0%
territory one and keyword retrieval and
 

00:05:15.300 --> 00:05:17.570 align:start position:0%
territory one and keyword retrieval and
then<00:05:15.600><c> pass</c><00:05:15.840><c> it</c><00:05:16.020><c> to</c><00:05:16.139><c> the</c><00:05:16.259><c> class</c><00:05:16.440><c> uh</c>

00:05:17.570 --> 00:05:17.580 align:start position:0%
then pass it to the class uh
 

00:05:17.580 --> 00:05:20.990 align:start position:0%
then pass it to the class uh
and<00:05:18.120><c> then</c><00:05:18.240><c> the</c><00:05:18.419><c> mode</c><00:05:18.600><c> is</c><00:05:18.840><c> and</c><00:05:19.080><c> or</c><00:05:19.259><c> are</c><00:05:19.500><c> so</c><00:05:20.100><c> and</c>

00:05:20.990 --> 00:05:21.000 align:start position:0%
and then the mode is and or are so and
 

00:05:21.000 --> 00:05:24.650 align:start position:0%
and then the mode is and or are so and
um<00:05:21.120><c> and</c><00:05:21.660><c> means</c><00:05:22.320><c> you</c><00:05:22.860><c> return</c><00:05:23.100><c> the</c><00:05:23.400><c> nodes</c><00:05:23.699><c> uh</c>

00:05:24.650 --> 00:05:24.660 align:start position:0%
um and means you return the nodes uh
 

00:05:24.660 --> 00:05:27.050 align:start position:0%
um and means you return the nodes uh
both<00:05:24.960><c> from</c><00:05:25.259><c> the</c><00:05:25.440><c> internal</c><00:05:25.800><c> Retriever</c><00:05:26.160><c> and</c>

00:05:27.050 --> 00:05:27.060 align:start position:0%
both from the internal Retriever and
 

00:05:27.060 --> 00:05:29.749 align:start position:0%
both from the internal Retriever and
keyword<00:05:27.479><c> retrieval</c><00:05:28.080><c> uh</c><00:05:28.620><c> using</c><00:05:29.160><c> those</c><00:05:29.580><c> two</c>

00:05:29.749 --> 00:05:29.759 align:start position:0%
keyword retrieval uh using those two
 

00:05:29.759 --> 00:05:33.529 align:start position:0%
keyword retrieval uh using those two
levels<00:05:30.300><c> and</c><00:05:30.600><c> then</c><00:05:30.780><c> if</c><00:05:31.740><c> it</c><00:05:31.919><c> is</c><00:05:32.039><c> and</c><00:05:32.280><c> you</c><00:05:33.240><c> just</c>

00:05:33.529 --> 00:05:33.539 align:start position:0%
levels and then if it is and you just
 

00:05:33.539 --> 00:05:37.010 align:start position:0%
levels and then if it is and you just
combine<00:05:34.020><c> only</c><00:05:34.919><c> common</c><00:05:35.280><c> nodes</c><00:05:35.639><c> uh</c><00:05:36.539><c> if</c><00:05:36.780><c> it</c><00:05:36.900><c> is</c>

00:05:37.010 --> 00:05:37.020 align:start position:0%
combine only common nodes uh if it is
 

00:05:37.020 --> 00:05:39.650 align:start position:0%
combine only common nodes uh if it is
odd<00:05:37.380><c> you</c><00:05:37.680><c> combine</c><00:05:38.039><c> notes</c><00:05:38.940><c> on</c><00:05:39.180><c> both</c><00:05:39.360><c> of</c><00:05:39.539><c> them</c>

00:05:39.650 --> 00:05:39.660 align:start position:0%
odd you combine notes on both of them
 

00:05:39.660 --> 00:05:42.110 align:start position:0%
odd you combine notes on both of them
based<00:05:40.139><c> on</c><00:05:40.199><c> the</c><00:05:40.380><c> user</c><00:05:40.740><c> that</c><00:05:41.100><c> you</c><00:05:41.699><c> would</c><00:05:41.820><c> like</c><00:05:41.940><c> to</c>

00:05:42.110 --> 00:05:42.120 align:start position:0%
based on the user that you would like to
 

00:05:42.120 --> 00:05:43.670 align:start position:0%
based on the user that you would like to
have

00:05:43.670 --> 00:05:43.680 align:start position:0%
have
 

00:05:43.680 --> 00:05:46.060 align:start position:0%
have
earlier

00:05:46.060 --> 00:05:46.070 align:start position:0%
earlier
 

00:05:46.070 --> 00:05:53.150 align:start position:0%
earlier
[Music]

00:05:53.150 --> 00:05:53.160 align:start position:0%
 
 

00:05:53.160 --> 00:06:00.770 align:start position:0%
 
[Music]

00:06:00.770 --> 00:06:00.780 align:start position:0%
 
 

00:06:00.780 --> 00:06:03.189 align:start position:0%
 
and

00:06:03.189 --> 00:06:03.199 align:start position:0%
and
 

00:06:03.199 --> 00:06:06.050 align:start position:0%
and
initialize<00:06:04.199><c> the</c><00:06:04.440><c> default</c><00:06:05.100><c> uh</c><00:06:05.520><c> response</c>

00:06:06.050 --> 00:06:06.060 align:start position:0%
initialize the default uh response
 

00:06:06.060 --> 00:06:07.370 align:start position:0%
initialize the default uh response
synthesizer

00:06:07.370 --> 00:06:07.380 align:start position:0%
synthesizer
 

00:06:07.380 --> 00:06:10.969 align:start position:0%
synthesizer
and<00:06:08.039><c> create</c><00:06:08.340><c> a</c><00:06:08.759><c> retriever</c><00:06:09.300><c> query</c><00:06:09.660><c> engine</c><00:06:09.979><c> so</c>

00:06:10.969 --> 00:06:10.979 align:start position:0%
and create a retriever query engine so
 

00:06:10.979 --> 00:06:14.450 align:start position:0%
and create a retriever query engine so
the<00:06:11.940><c> query</c><00:06:12.240><c> engine</c><00:06:12.419><c> will</c><00:06:12.780><c> be</c><00:06:13.280><c> with</c><00:06:14.280><c> custom</c>

00:06:14.450 --> 00:06:14.460 align:start position:0%
the query engine will be with custom
 

00:06:14.460 --> 00:06:16.249 align:start position:0%
the query engine will be with custom
Retriever<00:06:15.060><c> and</c><00:06:15.240><c> then</c><00:06:15.419><c> you</c><00:06:15.539><c> see</c><00:06:15.720><c> the</c><00:06:15.840><c> response</c>

00:06:16.249 --> 00:06:16.259 align:start position:0%
Retriever and then you see the response
 

00:06:16.259 --> 00:06:17.930 align:start position:0%
Retriever and then you see the response
inside<00:06:16.440><c> that</c><00:06:16.740><c> we</c><00:06:16.919><c> have</c><00:06:17.039><c> to</c><00:06:17.220><c> Define</c>

00:06:17.930 --> 00:06:17.940 align:start position:0%
inside that we have to Define
 

00:06:17.940 --> 00:06:19.790 align:start position:0%
inside that we have to Define
and

00:06:19.790 --> 00:06:19.800 align:start position:0%
and
 

00:06:19.800 --> 00:06:23.270 align:start position:0%
and
we<00:06:20.340><c> even</c><00:06:20.580><c> have</c><00:06:21.180><c> a</c><00:06:21.660><c> temporary</c><00:06:22.440><c> interval</c><00:06:23.160><c> you</c>

00:06:23.270 --> 00:06:23.280 align:start position:0%
we even have a temporary interval you
 

00:06:23.280 --> 00:06:26.510 align:start position:0%
we even have a temporary interval you
can<00:06:23.460><c> also</c><00:06:23.639><c> to</c><00:06:23.759><c> compare</c><00:06:24.120><c> further</c><00:06:24.960><c> quality</c><00:06:25.520><c> and</c>

00:06:26.510 --> 00:06:26.520 align:start position:0%
can also to compare further quality and
 

00:06:26.520 --> 00:06:29.689 align:start position:0%
can also to compare further quality and
you<00:06:27.360><c> have</c><00:06:27.780><c> a</c><00:06:28.020><c> keyword</c><00:06:28.319><c> code</c><00:06:28.560><c> in</c><00:06:28.800><c> there</c><00:06:28.979><c> as</c><00:06:29.160><c> well</c>

00:06:29.689 --> 00:06:29.699 align:start position:0%
you have a keyword code in there as well
 

00:06:29.699 --> 00:06:34.730 align:start position:0%
you have a keyword code in there as well
YC

00:06:34.730 --> 00:06:34.740 align:start position:0%
 
 

00:06:34.740 --> 00:06:38.710 align:start position:0%
 
and<00:06:35.699><c> so</c><00:06:36.240><c> we</c><00:06:36.479><c> gave</c><00:06:36.660><c> an</c><00:06:36.840><c> answer</c><00:06:37.020><c> I</c><00:06:37.680><c> would</c><00:06:37.860><c> order</c>

00:06:38.710 --> 00:06:38.720 align:start position:0%
and so we gave an answer I would order
 

00:06:38.720 --> 00:06:42.650 align:start position:0%
and so we gave an answer I would order
done<00:06:39.720><c> now</c><00:06:40.380><c> let's</c><00:06:40.740><c> do</c><00:06:41.699><c> what</c><00:06:41.940><c> the</c><00:06:42.180><c> author</c><00:06:42.539><c> did</c>

00:06:42.650 --> 00:06:42.660 align:start position:0%
done now let's do what the author did
 

00:06:42.660 --> 00:06:45.590 align:start position:0%
done now let's do what the author did
what<00:06:43.139><c> this</c><00:06:43.319><c> time</c><00:06:43.560><c> until</c><00:06:43.800><c> uh</c><00:06:44.699><c> sincere</c><00:06:45.180><c> has</c>

00:06:45.590 --> 00:06:45.600 align:start position:0%
what this time until uh sincere has
 

00:06:45.600 --> 00:06:48.830 align:start position:0%
what this time until uh sincere has
never<00:06:45.840><c> mentioned</c><00:06:46.340><c> uh</c><00:06:47.340><c> about</c><00:06:47.660><c> checking</c><00:06:48.660><c> the</c>

00:06:48.830 --> 00:06:48.840 align:start position:0%
never mentioned uh about checking the
 

00:06:48.840 --> 00:06:51.590 align:start position:0%
never mentioned uh about checking the
essay<00:06:49.080><c> so</c><00:06:49.800><c> the</c><00:06:49.979><c> response</c><00:06:50.340><c> would</c><00:06:50.520><c> be</c><00:06:50.639><c> even</c><00:06:50.759><c> and</c>

00:06:51.590 --> 00:06:51.600 align:start position:0%
essay so the response would be even and
 

00:06:51.600 --> 00:06:54.590 align:start position:0%
essay so the response would be even and
the<00:06:51.900><c> retrieve</c><00:06:52.800><c> nodes</c><00:06:52.979><c> will</c><00:06:53.340><c> also</c><00:06:53.699><c> be</c><00:06:53.880><c> zero</c><00:06:54.479><c> for</c>

00:06:54.590 --> 00:06:54.600 align:start position:0%
the retrieve nodes will also be zero for
 

00:06:54.600 --> 00:06:57.770 align:start position:0%
the retrieve nodes will also be zero for
the<00:06:54.780><c> same</c><00:06:54.900><c> but</c><00:06:55.800><c> if</c><00:06:55.979><c> you</c><00:06:56.100><c> do</c><00:06:56.280><c> uh</c><00:06:56.759><c> something</c><00:06:57.060><c> uh</c>

00:06:57.770 --> 00:06:57.780 align:start position:0%
the same but if you do uh something uh
 

00:06:57.780 --> 00:07:00.710 align:start position:0%
the same but if you do uh something uh
with<00:06:58.020><c> the</c><00:06:58.139><c> letters</c><00:06:58.740><c> research</c>

00:07:00.710 --> 00:07:00.720 align:start position:0%
with the letters research
 

00:07:00.720 --> 00:07:02.749 align:start position:0%
with the letters research
um if<00:07:01.080><c> you</c><00:07:01.199><c> have</c><00:07:01.380><c> an</c><00:07:01.560><c> answer</c><00:07:01.680><c> but</c><00:07:02.220><c> but</c><00:07:02.460><c> again</c>

00:07:02.749 --> 00:07:02.759 align:start position:0%
um if you have an answer but but again
 

00:07:02.759 --> 00:07:04.490 align:start position:0%
um if you have an answer but but again
if<00:07:03.060><c> you</c><00:07:03.240><c> see</c><00:07:03.479><c> uh</c>

00:07:04.490 --> 00:07:04.500 align:start position:0%
if you see uh
 

00:07:04.500 --> 00:07:08.629 align:start position:0%
if you see uh
the<00:07:05.100><c> number</c><00:07:05.280><c> of</c><00:07:05.460><c> nodes</c><00:07:05.840><c> are</c><00:07:06.840><c> present</c><00:07:07.199><c> in</c><00:07:07.380><c> it</c>

00:07:08.629 --> 00:07:08.639 align:start position:0%
the number of nodes are present in it
 

00:07:08.639 --> 00:07:11.330 align:start position:0%
the number of nodes are present in it
it<00:07:09.120><c> will</c><00:07:09.360><c> still</c><00:07:09.600><c> retrieve</c><00:07:10.139><c> some</c><00:07:11.039><c> nodes</c>

00:07:11.330 --> 00:07:11.340 align:start position:0%
it will still retrieve some nodes
 

00:07:11.340 --> 00:07:14.689 align:start position:0%
it will still retrieve some nodes
because<00:07:11.759><c> of</c><00:07:12.060><c> the</c><00:07:12.560><c> embedding</c><00:07:13.560><c> similarity</c><00:07:13.979><c> but</c>

00:07:14.689 --> 00:07:14.699 align:start position:0%
because of the embedding similarity but
 

00:07:14.699 --> 00:07:17.210 align:start position:0%
because of the embedding similarity but
here<00:07:15.000><c> uh</c><00:07:15.539><c> the</c><00:07:15.960><c> custom</c><00:07:16.319><c> retriever</c><00:07:16.860><c> we</c><00:07:17.100><c> don't</c>

00:07:17.210 --> 00:07:17.220 align:start position:0%
here uh the custom retriever we don't
 

00:07:17.220 --> 00:07:22.129 align:start position:0%
here uh the custom retriever we don't
even<00:07:18.199><c> retrieve</c><00:07:19.199><c> all</c><00:07:19.380><c> the</c><00:07:19.620><c> names</c><00:07:19.860><c> because</c><00:07:20.580><c> uh</c>

00:07:22.129 --> 00:07:22.139 align:start position:0%
even retrieve all the names because uh
 

00:07:22.139 --> 00:07:24.469 align:start position:0%
even retrieve all the names because uh
combining<00:07:23.039><c> both</c><00:07:23.340><c> websites</c><00:07:24.060><c> and</c><00:07:24.180><c> keyboard</c>

00:07:24.469 --> 00:07:24.479 align:start position:0%
combining both websites and keyboard
 

00:07:24.479 --> 00:07:27.730 align:start position:0%
combining both websites and keyboard
sets<00:07:24.900><c> so</c><00:07:25.800><c> in</c><00:07:25.919><c> that</c><00:07:26.099><c> way</c><00:07:26.280><c> we</c><00:07:26.759><c> will</c><00:07:26.940><c> be</c><00:07:27.060><c> able</c><00:07:27.360><c> to</c>

00:07:27.730 --> 00:07:27.740 align:start position:0%
sets so in that way we will be able to
 

00:07:27.740 --> 00:07:32.210 align:start position:0%
sets so in that way we will be able to
make<00:07:28.740><c> it</c><00:07:28.979><c> more</c><00:07:29.460><c> accurate</c><00:07:29.880><c> and</c><00:07:30.660><c> also</c><00:07:31.139><c> the</c><00:07:31.620><c> cost</c>

00:07:32.210 --> 00:07:32.220 align:start position:0%
make it more accurate and also the cost
 

00:07:32.220 --> 00:07:35.749 align:start position:0%
make it more accurate and also the cost
because<00:07:33.060><c> the</c><00:07:33.720><c> amounts</c><00:07:34.620><c> are</c><00:07:34.740><c> zero</c><00:07:35.039><c> the</c><00:07:35.280><c> costs</c>

00:07:35.749 --> 00:07:35.759 align:start position:0%
because the amounts are zero the costs
 

00:07:35.759 --> 00:07:37.909 align:start position:0%
because the amounts are zero the costs
are<00:07:36.060><c> taken</c><00:07:36.360><c> for</c><00:07:36.599><c> generating</c><00:07:36.960><c> a</c><00:07:37.139><c> RSM</c><00:07:37.560><c> will</c><00:07:37.680><c> also</c>

00:07:37.909 --> 00:07:37.919 align:start position:0%
are taken for generating a RSM will also
 

00:07:37.919 --> 00:07:42.610 align:start position:0%
are taken for generating a RSM will also
be<00:07:38.120><c> low</c><00:07:39.139><c> so</c><00:07:40.139><c> this</c><00:07:40.680><c> is</c><00:07:40.860><c> how</c><00:07:40.979><c> you</c><00:07:41.280><c> can</c>

00:07:42.610 --> 00:07:42.620 align:start position:0%
be low so this is how you can
 

00:07:42.620 --> 00:07:46.370 align:start position:0%
be low so this is how you can
have<00:07:43.620><c> a</c><00:07:43.740><c> customer</c><00:07:43.919><c> table</c><00:07:44.400><c> and</c><00:07:45.120><c> then</c>

00:07:46.370 --> 00:07:46.380 align:start position:0%
have a customer table and then
 

00:07:46.380 --> 00:07:49.370 align:start position:0%
have a customer table and then
work<00:07:46.919><c> on</c><00:07:47.220><c> work</c><00:07:48.000><c> towards</c><00:07:48.419><c> it</c><00:07:48.660><c> with</c><00:07:49.139><c> the</c>

00:07:49.370 --> 00:07:49.380 align:start position:0%
work on work towards it with the
 

00:07:49.380 --> 00:07:50.930 align:start position:0%
work on work towards it with the
interesting<00:07:49.740><c> resources</c><00:07:50.280><c> and</c><00:07:50.520><c> you</c><00:07:50.699><c> can</c><00:07:50.819><c> even</c>

00:07:50.930 --> 00:07:50.940 align:start position:0%
interesting resources and you can even
 

00:07:50.940 --> 00:07:54.050 align:start position:0%
interesting resources and you can even
check<00:07:51.300><c> the</c><00:07:51.720><c> same</c><00:07:51.960><c> in</c><00:07:52.139><c> the</c><00:07:52.259><c> top</c><00:07:52.319><c> 2007</c><00:07:52.979><c> about</c><00:07:53.759><c> the</c>

00:07:54.050 --> 00:07:54.060 align:start position:0%
check the same in the top 2007 about the
 

00:07:54.060 --> 00:07:56.390 align:start position:0%
check the same in the top 2007 about the
customer<00:07:54.180><c> table</c><00:07:54.660><c> and</c><00:07:55.440><c> that's</c><00:07:55.800><c> all</c><00:07:56.039><c> for</c><00:07:56.220><c> this</c>

00:07:56.390 --> 00:07:56.400 align:start position:0%
customer table and that's all for this
 

00:07:56.400 --> 00:07:57.529 align:start position:0%
customer table and that's all for this
video

00:07:57.529 --> 00:07:57.539 align:start position:0%
video
 

00:07:57.539 --> 00:08:00.589 align:start position:0%
video
um<00:07:57.599><c> I</c><00:07:57.900><c> will</c><00:07:58.139><c> read</c><00:07:58.800><c> more</c><00:07:59.280><c> interesting</c><00:07:59.639><c> content</c>

00:08:00.589 --> 00:08:00.599 align:start position:0%
um I will read more interesting content
 

00:08:00.599 --> 00:08:04.880 align:start position:0%
um I will read more interesting content
in<00:08:00.840><c> the</c><00:08:00.960><c> next</c><00:08:01.139><c> video</c><00:08:01.759><c> thank</c><00:08:02.759><c> you</c>

