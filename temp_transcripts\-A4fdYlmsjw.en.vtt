WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.760 align:start position:0%
 
we're<00:00:00.149><c> continuing</c><00:00:00.359><c> here</c><00:00:01.020><c> with</c><00:00:01.290><c> our</c><00:00:01.469><c> cold</c>

00:00:01.760 --> 00:00:01.770 align:start position:0%
we're continuing here with our cold
 

00:00:01.770 --> 00:00:04.610 align:start position:0%
we're continuing here with our cold
introduction<00:00:03.020><c> so</c><00:00:04.020><c> what</c><00:00:04.170><c> I</c><00:00:04.200><c> want</c><00:00:04.410><c> to</c><00:00:04.500><c> talk</c>

00:00:04.610 --> 00:00:04.620 align:start position:0%
introduction so what I want to talk
 

00:00:04.620 --> 00:00:10.250 align:start position:0%
introduction so what I want to talk
about<00:00:04.950><c> is</c><00:00:05.220><c> something</c><00:00:05.970><c> called</c><00:00:08.690><c> as</c><00:00:09.690><c> I</c><00:00:09.929><c> was</c><00:00:10.230><c> just</c>

00:00:10.250 --> 00:00:10.260 align:start position:0%
about is something called as I was just
 

00:00:10.260 --> 00:00:13.190 align:start position:0%
about is something called as I was just
talking<00:00:10.769><c> here</c><00:00:10.950><c> about</c><00:00:11.040><c> the</c><00:00:11.250><c> header</c><00:00:12.110><c> so</c><00:00:13.110><c> the</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
talking here about the header so the
 

00:00:13.200 --> 00:00:16.160 align:start position:0%
talking here about the header so the
header<00:00:13.380><c> is</c><00:00:13.590><c> our</c><00:00:13.769><c> navbar</c><00:00:14.480><c> this</c><00:00:15.480><c> over</c><00:00:15.839><c> here</c><00:00:16.020><c> we</c>

00:00:16.160 --> 00:00:16.170 align:start position:0%
header is our navbar this over here we
 

00:00:16.170 --> 00:00:20.359 align:start position:0%
header is our navbar this over here we
don't<00:00:16.350><c> really</c><00:00:16.590><c> need</c><00:00:18.020><c> we</c><00:00:19.020><c> have</c><00:00:19.050><c> a</c><00:00:19.170><c> home</c><00:00:19.500><c> and</c><00:00:19.830><c> we</c>

00:00:20.359 --> 00:00:20.369 align:start position:0%
don't really need we have a home and we
 

00:00:20.369 --> 00:00:21.679 align:start position:0%
don't really need we have a home and we
have<00:00:20.460><c> a</c><00:00:20.490><c> client</c><00:00:20.789><c> email</c><00:00:20.970><c> and</c><00:00:21.330><c> we</c><00:00:21.449><c> have</c><00:00:21.570><c> the</c>

00:00:21.679 --> 00:00:21.689 align:start position:0%
have a client email and we have the
 

00:00:21.689 --> 00:00:24.019 align:start position:0%
have a client email and we have the
logout<00:00:22.080><c> button</c><00:00:22.140><c> and</c><00:00:22.920><c> let's</c><00:00:23.279><c> verify</c><00:00:23.519><c> that</c><00:00:23.789><c> the</c>

00:00:24.019 --> 00:00:24.029 align:start position:0%
logout button and let's verify that the
 

00:00:24.029 --> 00:00:26.810 align:start position:0%
logout button and let's verify that the
logout<00:00:24.359><c> button</c><00:00:24.600><c> works</c><00:00:25.140><c> and</c><00:00:25.560><c> yes</c><00:00:26.460><c> the</c><00:00:26.670><c> blog</c>

00:00:26.810 --> 00:00:26.820 align:start position:0%
logout button works and yes the blog
 

00:00:26.820 --> 00:00:29.060 align:start position:0%
logout button works and yes the blog
that<00:00:27.029><c> lemon</c><00:00:27.359><c> works</c><00:00:27.570><c> we</c><00:00:28.019><c> can</c><00:00:28.199><c> log</c><00:00:28.380><c> back</c><00:00:28.650><c> in</c><00:00:28.859><c> and</c>

00:00:29.060 --> 00:00:29.070 align:start position:0%
that lemon works we can log back in and
 

00:00:29.070 --> 00:00:32.330 align:start position:0%
that lemon works we can log back in and
we<00:00:29.460><c> have</c><00:00:29.640><c> cookies</c><00:00:30.179><c> as</c><00:00:30.390><c> you</c><00:00:30.689><c> can</c><00:00:30.840><c> see</c><00:00:31.340><c> CIF</c>

00:00:32.330 --> 00:00:32.340 align:start position:0%
we have cookies as you can see CIF
 

00:00:32.340 --> 00:00:35.780 align:start position:0%
we have cookies as you can see CIF
giving<00:00:33.500><c> now</c><00:00:34.500><c> we</c><00:00:34.649><c> have</c><00:00:34.770><c> a</c><00:00:34.800><c> new</c><00:00:34.980><c> email</c><00:00:35.219><c> here</c>

00:00:35.780 --> 00:00:35.790 align:start position:0%
giving now we have a new email here
 

00:00:35.790 --> 00:00:40.880 align:start position:0%
giving now we have a new email here
okay<00:00:36.750><c> so</c><00:00:39.110><c> we</c><00:00:40.110><c> also</c><00:00:40.230><c> want</c><00:00:40.469><c> to</c><00:00:40.530><c> talk</c><00:00:40.710><c> a</c><00:00:40.739><c> little</c>

00:00:40.880 --> 00:00:40.890 align:start position:0%
okay so we also want to talk a little
 

00:00:40.890 --> 00:00:42.680 align:start position:0%
okay so we also want to talk a little
bit<00:00:41.100><c> about</c><00:00:41.129><c> the</c><00:00:41.399><c> routes</c><00:00:41.610><c> now</c><00:00:41.879><c> so</c><00:00:42.149><c> if</c><00:00:42.300><c> I</c><00:00:42.420><c> go</c><00:00:42.570><c> to</c>

00:00:42.680 --> 00:00:42.690 align:start position:0%
bit about the routes now so if I go to
 

00:00:42.690 --> 00:00:49.490 align:start position:0%
bit about the routes now so if I go to
the<00:00:42.809><c> app</c><00:00:43.020><c> and</c><00:00:43.379><c> routes</c><00:00:44.250><c> I</c><00:00:45.140><c> have</c><00:00:46.140><c> here</c><00:00:48.289><c> I'm</c><00:00:49.289><c> sorry</c>

00:00:49.490 --> 00:00:49.500 align:start position:0%
the app and routes I have here I'm sorry
 

00:00:49.500 --> 00:00:51.250 align:start position:0%
the app and routes I have here I'm sorry
this<00:00:49.620><c> is</c><00:00:49.800><c> all</c><00:00:49.950><c> pulled</c><00:00:50.250><c> in</c><00:00:50.399><c> from</c><00:00:50.550><c> the</c><00:00:50.640><c> server</c>

00:00:51.250 --> 00:00:51.260 align:start position:0%
this is all pulled in from the server
 

00:00:51.260 --> 00:00:54.650 align:start position:0%
this is all pulled in from the server
j/s<00:00:52.289><c> so</c><00:00:53.160><c> within</c><00:00:53.430><c> the</c><00:00:53.520><c> server</c><00:00:53.760><c> yes</c><00:00:54.360><c> we</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
j/s so within the server yes we
 

00:00:54.660 --> 00:00:56.569 align:start position:0%
j/s so within the server yes we
declaring<00:00:55.260><c> our</c><00:00:55.410><c> port</c><00:00:55.739><c> we're</c><00:00:56.070><c> declaring</c><00:00:56.280><c> our</c>

00:00:56.569 --> 00:00:56.579 align:start position:0%
declaring our port we're declaring our
 

00:00:56.579 --> 00:00:59.330 align:start position:0%
declaring our port we're declaring our
views<00:00:56.850><c> folder</c><00:00:57.180><c> the</c><00:00:58.109><c> public</c><00:00:58.500><c> folder</c><00:00:58.739><c> is</c><00:00:59.100><c> where</c>

00:00:59.330 --> 00:00:59.340 align:start position:0%
views folder the public folder is where
 

00:00:59.340 --> 00:01:02.660 align:start position:0%
views folder the public folder is where
where<00:00:59.910><c> all</c><00:01:00.120><c> the</c><00:01:00.329><c> apps</c><00:01:01.010><c> main</c><00:01:02.010><c> files</c><00:01:02.370><c> are</c>

00:01:02.660 --> 00:01:02.670 align:start position:0%
where all the apps main files are
 

00:01:02.670 --> 00:01:05.119 align:start position:0%
where all the apps main files are
located<00:01:03.270><c> the</c><00:01:03.480><c> socket</c><00:01:03.989><c> is</c><00:01:04.290><c> an</c><00:01:04.530><c> array</c><00:01:04.830><c> that</c>

00:01:05.119 --> 00:01:05.129 align:start position:0%
located the socket is an array that
 

00:01:05.129 --> 00:01:07.190 align:start position:0%
located the socket is an array that
holds<00:01:05.460><c> all</c><00:01:05.549><c> the</c><00:01:05.729><c> logged</c><00:01:05.970><c> in</c><00:01:06.210><c> users</c><00:01:06.780><c> we're</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
holds all the logged in users we're
 

00:01:07.200 --> 00:01:14.020 align:start position:0%
holds all the logged in users we're
getting<00:01:07.439><c> to</c><00:01:07.560><c> that</c><00:01:07.650><c> in</c><00:01:07.710><c> another</c><00:01:07.950><c> video</c><00:01:10.909><c> we</c><00:01:11.909><c> have</c>

00:01:14.020 --> 00:01:14.030 align:start position:0%
getting to that in another video we have
 

00:01:14.030 --> 00:01:17.390 align:start position:0%
getting to that in another video we have
scheduled<00:01:15.030><c> a</c><00:01:15.090><c> little</c><00:01:15.299><c> job</c><00:01:16.130><c> okay</c><00:01:17.130><c> we</c><00:01:17.280><c> have</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
scheduled a little job okay we have
 

00:01:17.400 --> 00:01:19.789 align:start position:0%
scheduled a little job okay we have
passport<00:01:18.119><c> and</c><00:01:18.360><c> we</c><00:01:18.570><c> have</c><00:01:18.689><c> configure</c><00:01:19.200><c> passport</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
passport and we have configure passport
 

00:01:19.799 --> 00:01:23.210 align:start position:0%
passport and we have configure passport
jeaious<00:01:20.490><c> that's</c><00:01:20.729><c> in</c><00:01:20.909><c> config</c><00:01:21.710><c> passport</c><00:01:22.710><c> yes</c>

00:01:23.210 --> 00:01:23.220 align:start position:0%
jeaious that's in config passport yes
 

00:01:23.220 --> 00:01:25.640 align:start position:0%
jeaious that's in config passport yes
this<00:01:23.400><c> is</c><00:01:23.580><c> where</c><00:01:23.790><c> all</c><00:01:23.939><c> the</c><00:01:24.210><c> logic</c><00:01:25.170><c> goes</c><00:01:25.320><c> for</c>

00:01:25.640 --> 00:01:25.650 align:start position:0%
this is where all the logic goes for
 

00:01:25.650 --> 00:01:29.450 align:start position:0%
this is where all the logic goes for
actually<00:01:26.250><c> signing</c><00:01:26.640><c> up</c><00:01:26.939><c> so</c><00:01:27.020><c> that</c><00:01:28.020><c> means</c><00:01:28.460><c> when</c>

00:01:29.450 --> 00:01:29.460 align:start position:0%
actually signing up so that means when
 

00:01:29.460 --> 00:01:31.460 align:start position:0%
actually signing up so that means when
we<00:01:29.610><c> create</c><00:01:30.000><c> a</c><00:01:30.030><c> new</c><00:01:30.180><c> user</c><00:01:30.540><c> we're</c><00:01:30.990><c> gonna</c><00:01:31.110><c> find</c>

00:01:31.460 --> 00:01:31.470 align:start position:0%
we create a new user we're gonna find
 

00:01:31.470 --> 00:01:33.410 align:start position:0%
we create a new user we're gonna find
we're<00:01:31.710><c> gonna</c><00:01:31.920><c> run</c><00:01:32.159><c> this</c><00:01:32.400><c> sequel</c><00:01:32.909><c> query</c><00:01:33.210><c> over</c>

00:01:33.410 --> 00:01:33.420 align:start position:0%
we're gonna run this sequel query over
 

00:01:33.420 --> 00:01:35.390 align:start position:0%
we're gonna run this sequel query over
here<00:01:33.810><c> insert</c><00:01:34.290><c> into</c><00:01:34.439><c> client</c><00:01:35.009><c> the</c><00:01:35.100><c> client</c>

00:01:35.390 --> 00:01:35.400 align:start position:0%
here insert into client the client
 

00:01:35.400 --> 00:01:38.230 align:start position:0%
here insert into client the client
created<00:01:35.820><c> a</c><00:01:35.970><c> client</c><00:01:36.360><c> email</c><00:01:36.570><c> client</c><00:01:36.960><c> password</c>

00:01:38.230 --> 00:01:38.240 align:start position:0%
created a client email client password
 

00:01:38.240 --> 00:01:40.850 align:start position:0%
created a client email client password
and<00:01:39.240><c> so</c><00:01:39.540><c> on</c><00:01:39.780><c> and</c><00:01:40.020><c> then</c><00:01:40.170><c> we're</c><00:01:40.530><c> gonna</c><00:01:40.619><c> do</c>

00:01:40.850 --> 00:01:40.860 align:start position:0%
and so on and then we're gonna do
 

00:01:40.860 --> 00:01:47.300 align:start position:0%
and so on and then we're gonna do
something<00:01:42.170><c> with</c><00:01:43.170><c> it</c><00:01:43.200><c> and</c><00:01:43.770><c> so</c><00:01:44.490><c> on</c><00:01:46.070><c> we</c><00:01:47.070><c> pass</c><00:01:47.280><c> a</c>

00:01:47.300 --> 00:01:47.310 align:start position:0%
something with it and so on we pass a
 

00:01:47.310 --> 00:01:51.080 align:start position:0%
something with it and so on we pass a
new<00:01:47.729><c> user</c><00:01:48.060><c> my</c><00:01:48.390><c> sequel</c><00:01:48.869><c> and</c><00:01:49.520><c> over</c><00:01:50.520><c> here</c><00:01:50.729><c> we</c><00:01:50.909><c> have</c>

00:01:51.080 --> 00:01:51.090 align:start position:0%
new user my sequel and over here we have
 

00:01:51.090 --> 00:01:53.749 align:start position:0%
new user my sequel and over here we have
select<00:01:51.899><c> all</c><00:01:52.079><c> from</c><00:01:52.380><c> client</c><00:01:52.950><c> where</c><00:01:53.250><c> client</c>

00:01:53.749 --> 00:01:53.759 align:start position:0%
select all from client where client
 

00:01:53.759 --> 00:01:57.440 align:start position:0%
select all from client where client
email<00:01:54.000><c> equals</c><00:01:54.750><c> the</c><00:01:54.930><c> username</c><00:01:56.000><c> okay</c><00:01:57.000><c> and</c><00:01:57.240><c> if</c>

00:01:57.440 --> 00:01:57.450 align:start position:0%
email equals the username okay and if
 

00:01:57.450 --> 00:02:00.020 align:start position:0%
email equals the username okay and if
the<00:01:57.600><c> password</c><00:01:58.079><c> equals</c><00:01:58.530><c> rows</c><00:01:58.829><c> that</c><00:01:59.130><c> if</c><00:01:59.399><c> the</c>

00:02:00.020 --> 00:02:00.030 align:start position:0%
the password equals rows that if the
 

00:02:00.030 --> 00:02:03.410 align:start position:0%
the password equals rows that if the
password<00:02:00.600><c> were</c><00:02:01.320><c> passing</c><00:02:01.770><c> in</c><00:02:01.920><c> this</c><00:02:02.420><c> the</c>

00:02:03.410 --> 00:02:03.420 align:start position:0%
password were passing in this the
 

00:02:03.420 --> 00:02:06.319 align:start position:0%
password were passing in this the
username<00:02:03.960><c> which</c><00:02:04.530><c> is</c><00:02:04.740><c> the</c><00:02:05.130><c> email</c><00:02:05.490><c> the</c><00:02:05.939><c> client</c>

00:02:06.319 --> 00:02:06.329 align:start position:0%
username which is the email the client
 

00:02:06.329 --> 00:02:08.960 align:start position:0%
username which is the email the client
email<00:02:06.570><c> and</c><00:02:06.810><c> we</c><00:02:07.020><c> have</c><00:02:07.140><c> the</c><00:02:07.259><c> password</c><00:02:07.829><c> which</c><00:02:08.759><c> is</c>

00:02:08.960 --> 00:02:08.970 align:start position:0%
email and we have the password which is
 

00:02:08.970 --> 00:02:10.669 align:start position:0%
email and we have the password which is
taken<00:02:09.720><c> pretty</c><00:02:09.869><c> much</c><00:02:10.080><c> straight</c><00:02:10.379><c> from</c><00:02:10.560><c> the</c>

00:02:10.669 --> 00:02:10.679 align:start position:0%
taken pretty much straight from the
 

00:02:10.679 --> 00:02:12.030 align:start position:0%
taken pretty much straight from the
input<00:02:11.129><c> and</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
input and
 

00:02:12.040 --> 00:02:13.619 align:start position:0%
input and
we<00:02:12.189><c> pass</c><00:02:12.400><c> it</c><00:02:12.640><c> in</c><00:02:12.760><c> to</c><00:02:12.909><c> someone</c><00:02:13.209><c> we</c><00:02:13.299><c> do</c><00:02:13.420><c> some</c>

00:02:13.619 --> 00:02:13.629 align:start position:0%
we pass it in to someone we do some
 

00:02:13.629 --> 00:02:16.229 align:start position:0%
we pass it in to someone we do some
logic<00:02:14.019><c> with</c><00:02:14.200><c> it</c><00:02:14.409><c> if</c><00:02:14.650><c> it's</c><00:02:14.890><c> the</c><00:02:15.640><c> password</c><00:02:16.090><c> is</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
logic with it if it's the password is
 

00:02:16.239 --> 00:02:18.000 align:start position:0%
logic with it if it's the password is
not<00:02:16.359><c> equal</c><00:02:16.719><c> the</c><00:02:16.989><c> user</c><00:02:17.409><c> will</c><00:02:17.620><c> get</c><00:02:17.739><c> a</c><00:02:17.799><c> message</c>

00:02:18.000 --> 00:02:18.010 align:start position:0%
not equal the user will get a message
 

00:02:18.010 --> 00:02:22.890 align:start position:0%
not equal the user will get a message
saying<00:02:18.459><c> wrong</c><00:02:18.969><c> password</c><00:02:19.480><c> and</c><00:02:19.900><c> Chacho</c><00:02:20.260><c> if</c><00:02:21.900><c> they</c>

00:02:22.890 --> 00:02:22.900 align:start position:0%
saying wrong password and Chacho if they
 

00:02:22.900 --> 00:02:24.509 align:start position:0%
saying wrong password and Chacho if they
usually<00:02:23.349><c> it</c><00:02:23.409><c> does</c><00:02:23.560><c> not</c><00:02:23.709><c> exist</c><00:02:24.040><c> if</c><00:02:24.400><c> the</c>

00:02:24.509 --> 00:02:24.519 align:start position:0%
usually it does not exist if the
 

00:02:24.519 --> 00:02:27.539 align:start position:0%
usually it does not exist if the
username<00:02:24.790><c> doesn't</c><00:02:25.120><c> exist</c><00:02:25.299><c> in</c><00:02:25.689><c> database</c><00:02:26.549><c> then</c>

00:02:27.539 --> 00:02:27.549 align:start position:0%
username doesn't exist in database then
 

00:02:27.549 --> 00:02:39.240 align:start position:0%
username doesn't exist in database then
you're<00:02:28.390><c> gonna</c><00:02:28.599><c> get</c><00:02:28.780><c> that</c><00:02:29.049><c> message</c><00:02:37.500><c> okay</c><00:02:38.500><c> let's</c>

00:02:39.240 --> 00:02:39.250 align:start position:0%
you're gonna get that message okay let's
 

00:02:39.250 --> 00:02:40.559 align:start position:0%
you're gonna get that message okay let's
see<00:02:39.430><c> what</c><00:02:39.639><c> else</c><00:02:39.760><c> okay</c>

00:02:40.559 --> 00:02:40.569 align:start position:0%
see what else okay
 

00:02:40.569 --> 00:02:42.629 align:start position:0%
see what else okay
and<00:02:40.900><c> we</c><00:02:41.230><c> have</c><00:02:41.349><c> the</c><00:02:41.469><c> database</c><00:02:41.709><c> file</c><00:02:42.250><c> I</c><00:02:42.489><c> actually</c>

00:02:42.629 --> 00:02:42.639 align:start position:0%
and we have the database file I actually
 

00:02:42.639 --> 00:02:45.119 align:start position:0%
and we have the database file I actually
included<00:02:42.969><c> an</c><00:02:43.420><c> option</c><00:02:43.569><c> to</c><00:02:43.959><c> to</c><00:02:44.500><c> add</c><00:02:44.680><c> another</c>

00:02:45.119 --> 00:02:45.129 align:start position:0%
included an option to to add another
 

00:02:45.129 --> 00:02:48.420 align:start position:0%
included an option to to add another
database<00:02:45.639><c> in</c><00:02:46.209><c> K</c><00:02:46.920><c> because</c><00:02:47.920><c> one</c><00:02:48.189><c> of</c><00:02:48.220><c> the</c>

00:02:48.420 --> 00:02:48.430 align:start position:0%
database in K because one of the
 

00:02:48.430 --> 00:02:50.220 align:start position:0%
database in K because one of the
possible<00:02:48.939><c> features</c><00:02:49.480><c> I</c><00:02:49.629><c> was</c><00:02:49.750><c> thinking</c><00:02:50.109><c> about</c>

00:02:50.220 --> 00:02:50.230 align:start position:0%
possible features I was thinking about
 

00:02:50.230 --> 00:02:56.550 align:start position:0%
possible features I was thinking about
was<00:02:51.239><c> being</c><00:02:52.239><c> able</c><00:02:52.540><c> to</c><00:02:52.750><c> segment</c><00:02:55.049><c> segment</c><00:02:56.049><c> your</c>

00:02:56.550 --> 00:02:56.560 align:start position:0%
was being able to segment segment your
 

00:02:56.560 --> 00:03:00.750 align:start position:0%
was being able to segment segment your
leads<00:02:56.799><c> and</c><00:02:57.129><c> we're</c><00:02:57.280><c> gonna</c><00:02:57.459><c> go</c><00:02:59.370><c> I'm</c><00:03:00.370><c> gonna</c><00:03:00.579><c> get</c>

00:03:00.750 --> 00:03:00.760 align:start position:0%
leads and we're gonna go I'm gonna get
 

00:03:00.760 --> 00:03:08.520 align:start position:0%
leads and we're gonna go I'm gonna get
into<00:03:00.970><c> that</c><00:03:01.090><c> in</c><00:03:01.269><c> a</c><00:03:01.359><c> second</c><00:03:06.659><c> okay</c><00:03:07.689><c> this</c><00:03:08.139><c> is</c><00:03:08.349><c> the</c>

00:03:08.520 --> 00:03:08.530 align:start position:0%
into that in a second okay this is the
 

00:03:08.530 --> 00:03:11.960 align:start position:0%
into that in a second okay this is the
repo<00:03:08.950><c> here</c><00:03:09.540><c> feel</c><00:03:10.540><c> free</c><00:03:10.780><c> to</c><00:03:10.810><c> clone</c><00:03:11.230><c> it</c><00:03:11.260><c> make</c><00:03:11.919><c> a</c>

00:03:11.960 --> 00:03:11.970 align:start position:0%
repo here feel free to clone it make a
 

00:03:11.970 --> 00:03:15.569 align:start position:0%
repo here feel free to clone it make a
pull<00:03:12.970><c> request</c><00:03:13.000><c> if</c><00:03:13.629><c> you</c><00:03:13.780><c> want</c><00:03:14.430><c> segment</c><00:03:15.430><c> your</c>

00:03:15.569 --> 00:03:15.579 align:start position:0%
pull request if you want segment your
 

00:03:15.579 --> 00:03:18.030 align:start position:0%
pull request if you want segment your
customers<00:03:15.609><c> and</c><00:03:16.299><c> segment</c><00:03:16.720><c> send</c><00:03:16.930><c> them</c><00:03:17.109><c> highly</c>

00:03:18.030 --> 00:03:18.040 align:start position:0%
customers and segment send them highly
 

00:03:18.040 --> 00:03:20.789 align:start position:0%
customers and segment send them highly
targeted<00:03:18.280><c> emails</c><00:03:19.000><c> okay</c><00:03:19.479><c> and</c><00:03:19.870><c> there's</c><00:03:20.680><c> a</c>

00:03:20.789 --> 00:03:20.799 align:start position:0%
targeted emails okay and there's a
 

00:03:20.799 --> 00:03:23.099 align:start position:0%
targeted emails okay and there's a
little<00:03:20.949><c> bit</c><00:03:21.129><c> more</c><00:03:21.370><c> about</c><00:03:21.639><c> the</c><00:03:21.909><c> app</c><00:03:22.090><c> here</c><00:03:22.690><c> which</c>

00:03:23.099 --> 00:03:23.109 align:start position:0%
little bit more about the app here which
 

00:03:23.109 --> 00:03:25.680 align:start position:0%
little bit more about the app here which
is<00:03:23.290><c> that</c><00:03:23.650><c> it's</c><00:03:23.799><c> using</c><00:03:24.159><c> mail</c><00:03:24.430><c> done</c><00:03:24.729><c> how</c><00:03:25.659><c> to</c>

00:03:25.680 --> 00:03:25.690 align:start position:0%
is that it's using mail done how to
 

00:03:25.690 --> 00:03:28.110 align:start position:0%
is that it's using mail done how to
create<00:03:25.900><c> a</c><00:03:26.079><c> database</c><00:03:26.549><c> how</c><00:03:27.549><c> to</c><00:03:27.609><c> structure</c><00:03:27.970><c> your</c>

00:03:28.110 --> 00:03:28.120 align:start position:0%
create a database how to structure your
 

00:03:28.120 --> 00:03:30.869 align:start position:0%
create a database how to structure your
dot<00:03:28.479><c> emfe</c><00:03:29.139><c> file</c><00:03:29.470><c> that's</c><00:03:29.859><c> important</c><00:03:30.459><c> if</c><00:03:30.639><c> you're</c>

00:03:30.869 --> 00:03:30.879 align:start position:0%
dot emfe file that's important if you're
 

00:03:30.879 --> 00:03:33.479 align:start position:0%
dot emfe file that's important if you're
using<00:03:30.909><c> mail</c><00:03:31.449><c> done</c><00:03:31.720><c> this</c><00:03:32.680><c> is</c><00:03:32.919><c> specifically</c><00:03:33.400><c> for</c>

00:03:33.479 --> 00:03:33.489 align:start position:0%
using mail done this is specifically for
 

00:03:33.489 --> 00:03:36.689 align:start position:0%
using mail done this is specifically for
a<00:03:33.699><c> boom</c><00:03:34.000><c> -</c><00:03:34.209><c> but</c><00:03:34.479><c> if</c><00:03:35.229><c> you're</c><00:03:35.680><c> just</c><00:03:36.489><c> using</c>

00:03:36.689 --> 00:03:36.699 align:start position:0%
a boom - but if you're just using
 

00:03:36.699 --> 00:03:38.939 align:start position:0%
a boom - but if you're just using
Windows<00:03:37.209><c> they</c><00:03:37.449><c> race</c><00:03:37.659><c> the</c><00:03:37.870><c> word</c><00:03:38.019><c> export</c><00:03:38.620><c> here</c>

00:03:38.939 --> 00:03:38.949 align:start position:0%
Windows they race the word export here
 

00:03:38.949 --> 00:03:42.719 align:start position:0%
Windows they race the word export here
and<00:03:39.959><c> how</c><00:03:40.959><c> to</c><00:03:40.989><c> deploy</c><00:03:41.260><c> to</c><00:03:41.650><c> Heroku</c><00:03:41.739><c> because</c><00:03:42.370><c> you</c>

00:03:42.719 --> 00:03:42.729 align:start position:0%
and how to deploy to Heroku because you
 

00:03:42.729 --> 00:03:44.610 align:start position:0%
and how to deploy to Heroku because you
can<00:03:42.970><c> deploy</c><00:03:43.299><c> this</c><00:03:43.479><c> app</c><00:03:43.720><c> to</c><00:03:43.900><c> Heroku</c><00:03:44.229><c> once</c>

00:03:44.610 --> 00:03:44.620 align:start position:0%
can deploy this app to Heroku once
 

00:03:44.620 --> 00:03:49.199 align:start position:0%
can deploy this app to Heroku once
you're<00:03:44.799><c> finished</c><00:03:46.949><c> or</c><00:03:47.949><c> you</c><00:03:48.010><c> could</c><00:03:48.430><c> just</c><00:03:48.459><c> clone</c>

00:03:49.199 --> 00:03:49.209 align:start position:0%
you're finished or you could just clone
 

00:03:49.209 --> 00:03:50.819 align:start position:0%
you're finished or you could just clone
this<00:03:49.449><c> copy</c><00:03:49.720><c> here</c><00:03:50.079><c> and</c><00:03:50.199><c> deploy</c><00:03:50.470><c> to</c><00:03:50.500><c> Heroku</c>

00:03:50.819 --> 00:03:50.829 align:start position:0%
this copy here and deploy to Heroku
 

00:03:50.829 --> 00:03:53.189 align:start position:0%
this copy here and deploy to Heroku
notice<00:03:51.609><c> deploy</c><00:03:51.970><c> to</c><00:03:52.180><c> Heroku</c><00:03:52.299><c> you'll</c><00:03:52.840><c> just</c><00:03:53.019><c> have</c>

00:03:53.189 --> 00:03:53.199 align:start position:0%
notice deploy to Heroku you'll just have
 

00:03:53.199 --> 00:03:57.449 align:start position:0%
notice deploy to Heroku you'll just have
to<00:03:53.409><c> change</c><00:03:53.709><c> the</c><00:03:54.510><c> point</c><00:03:55.510><c> to</c><00:03:55.840><c> a</c><00:03:56.109><c> tee</c><00:03:56.229><c> and</c><00:03:56.680><c> then</c>

00:03:57.449 --> 00:03:57.459 align:start position:0%
to change the point to a tee and then
 

00:03:57.459 --> 00:04:00.119 align:start position:0%
to change the point to a tee and then
it'll<00:03:57.729><c> work</c><00:03:58.169><c> okay</c><00:03:59.169><c> so</c><00:03:59.409><c> that</c><00:03:59.769><c> pretty</c><00:03:59.949><c> much</c>

00:04:00.119 --> 00:04:00.129 align:start position:0%
it'll work okay so that pretty much
 

00:04:00.129 --> 00:04:04.199 align:start position:0%
it'll work okay so that pretty much
covers<00:04:00.400><c> that</c><00:04:00.870><c> I</c><00:04:02.489><c> want</c><00:04:03.489><c> to</c><00:04:03.549><c> talk</c><00:04:03.729><c> a</c><00:04:03.760><c> little</c><00:04:03.879><c> bit</c>

00:04:04.199 --> 00:04:04.209 align:start position:0%
covers that I want to talk a little bit
 

00:04:04.209 --> 00:04:09.409 align:start position:0%
covers that I want to talk a little bit
about<00:04:04.239><c> the</c><00:04:05.010><c> the</c><00:04:06.010><c> email</c><00:04:06.459><c> template</c><00:04:08.159><c> mmm</c>

00:04:09.409 --> 00:04:09.419 align:start position:0%
about the the email template mmm
 

00:04:09.419 --> 00:04:13.339 align:start position:0%
about the the email template mmm
now<00:04:10.419><c> that</c><00:04:10.720><c> we'll</c><00:04:10.900><c> talk</c><00:04:11.079><c> about</c><00:04:11.139><c> another</c><00:04:11.590><c> video</c>

00:04:13.339 --> 00:04:13.349 align:start position:0%
now that we'll talk about another video
 

00:04:13.349 --> 00:04:16.170 align:start position:0%
now that we'll talk about another video
see<00:04:14.349><c> we</c><00:04:14.530><c> got</c><00:04:14.709><c> we</c><00:04:14.949><c> covered</c><00:04:15.280><c> the</c><00:04:15.400><c> database</c><00:04:15.879><c> we've</c>

00:04:16.170 --> 00:04:16.180 align:start position:0%
see we got we covered the database we've
 

00:04:16.180 --> 00:04:20.240 align:start position:0%
see we got we covered the database we've
covered<00:04:16.419><c> passport</c><00:04:17.489><c> the</c><00:04:18.489><c> blog</c><00:04:18.759><c> itself</c><00:04:19.269><c> has</c>

00:04:20.240 --> 00:04:20.250 align:start position:0%
covered passport the blog itself has
 

00:04:20.250 --> 00:04:23.370 align:start position:0%
covered passport the blog itself has
index<00:04:21.250><c> dot</c><00:04:21.459><c> HTML</c><00:04:21.669><c> files</c><00:04:22.479><c> in</c><00:04:22.750><c> here</c><00:04:22.990><c> this</c><00:04:23.169><c> all</c>

00:04:23.370 --> 00:04:23.380 align:start position:0%
index dot HTML files in here this all
 

00:04:23.380 --> 00:04:25.340 align:start position:0%
index dot HTML files in here this all
needs<00:04:23.590><c> to</c><00:04:23.710><c> be</c><00:04:23.860><c> cleaned</c><00:04:24.219><c> up</c><00:04:24.460><c> by</c><00:04:24.639><c> the</c><00:04:24.669><c> way</c>

00:04:25.340 --> 00:04:25.350 align:start position:0%
needs to be cleaned up by the way
 

00:04:25.350 --> 00:04:29.300 align:start position:0%
needs to be cleaned up by the way
because<00:04:26.010><c> you'll</c><00:04:26.610><c> see</c><00:04:26.850><c> that</c><00:04:27.140><c> it</c><00:04:28.140><c> doesn't</c><00:04:28.590><c> in</c>

00:04:29.300 --> 00:04:29.310 align:start position:0%
because you'll see that it doesn't in
 

00:04:29.310 --> 00:04:31.400 align:start position:0%
because you'll see that it doesn't in
each<00:04:29.580><c> one</c><00:04:29.790><c> of</c><00:04:29.850><c> these</c><00:04:30.030><c> index</c><00:04:30.390><c> dot</c><00:04:30.540><c> HTML</c><00:04:30.570><c> files</c>

00:04:31.400 --> 00:04:31.410 align:start position:0%
each one of these index dot HTML files
 

00:04:31.410 --> 00:04:34.430 align:start position:0%
each one of these index dot HTML files
were<00:04:31.800><c> we're</c><00:04:32.190><c> adding</c><00:04:32.640><c> the</c><00:04:32.790><c> header</c><00:04:33.230><c> the</c><00:04:34.230><c> head</c>

00:04:34.430 --> 00:04:34.440 align:start position:0%
were we're adding the header the head
 

00:04:34.440 --> 00:04:36.800 align:start position:0%
were we're adding the header the head
and<00:04:34.710><c> the</c><00:04:34.950><c> header</c><00:04:35.160><c> here</c><00:04:35.520><c> and</c><00:04:35.970><c> we</c><00:04:36.690><c> don't</c>

00:04:36.800 --> 00:04:36.810 align:start position:0%
and the header here and we don't
 

00:04:36.810 --> 00:04:39.110 align:start position:0%
and the header here and we don't
actually<00:04:37.080><c> need</c><00:04:37.560><c> all</c><00:04:37.620><c> these</c><00:04:38.010><c> these</c><00:04:38.640><c> head</c><00:04:38.910><c> and</c>

00:04:39.110 --> 00:04:39.120 align:start position:0%
actually need all these these head and
 

00:04:39.120 --> 00:04:42.200 align:start position:0%
actually need all these these head and
headers<00:04:39.450><c> there's</c><00:04:39.750><c> a</c><00:04:39.840><c> concept</c><00:04:40.520><c> dr</c><00:04:41.520><c> why</c><00:04:41.760><c> don't</c>

00:04:42.200 --> 00:04:42.210 align:start position:0%
headers there's a concept dr why don't
 

00:04:42.210 --> 00:04:44.180 align:start position:0%
headers there's a concept dr why don't
repeat<00:04:42.570><c> yourself</c><00:04:42.720><c> in</c><00:04:43.170><c> coding</c><00:04:43.680><c> and</c><00:04:43.860><c> that's</c><00:04:44.040><c> why</c>

00:04:44.180 --> 00:04:44.190 align:start position:0%
repeat yourself in coding and that's why
 

00:04:44.190 --> 00:04:46.490 align:start position:0%
repeat yourself in coding and that's why
we<00:04:44.340><c> want</c><00:04:44.520><c> to</c><00:04:44.550><c> try</c><00:04:44.730><c> to</c><00:04:44.760><c> remove</c><00:04:45.690><c> all</c><00:04:46.110><c> of</c><00:04:46.410><c> these</c>

00:04:46.490 --> 00:04:46.500 align:start position:0%
we want to try to remove all of these
 

00:04:46.500 --> 00:04:48.800 align:start position:0%
we want to try to remove all of these
headers<00:04:46.830><c> as</c><00:04:47.160><c> you</c><00:04:47.340><c> can</c><00:04:47.520><c> see</c><00:04:47.730><c> even</c><00:04:48.330><c> if</c><00:04:48.420><c> I</c><00:04:48.540><c> go</c><00:04:48.750><c> to</c>

00:04:48.800 --> 00:04:48.810 align:start position:0%
headers as you can see even if I go to
 

00:04:48.810 --> 00:04:51.620 align:start position:0%
headers as you can see even if I go to
the<00:04:49.170><c> LinkedIn</c><00:04:49.560><c> Gerlach</c><00:04:50.130><c> article</c><00:04:50.730><c> as</c><00:04:51.450><c> your</c>

00:04:51.620 --> 00:04:51.630 align:start position:0%
the LinkedIn Gerlach article as your
 

00:04:51.630 --> 00:04:53.770 align:start position:0%
the LinkedIn Gerlach article as your
your<00:04:51.750><c> marketing</c><00:04:52.200><c> and</c><00:04:52.410><c> every</c><00:04:52.560><c> single</c><00:04:52.740><c> time</c><00:04:53.040><c> we</c>

00:04:53.770 --> 00:04:53.780 align:start position:0%
your marketing and every single time we
 

00:04:53.780 --> 00:04:56.420 align:start position:0%
your marketing and every single time we
get<00:04:54.780><c> a</c><00:04:55.020><c> header</c><00:04:55.290><c> typed</c><00:04:55.740><c> in</c><00:04:55.950><c> again</c><00:04:56.190><c> in</c><00:04:56.340><c> the</c>

00:04:56.420 --> 00:04:56.430 align:start position:0%
get a header typed in again in the
 

00:04:56.430 --> 00:04:58.790 align:start position:0%
get a header typed in again in the
Google<00:04:56.730><c> Analytics</c><00:04:56.880><c> after</c><00:04:57.540><c> so</c><00:04:57.750><c> we'll</c><00:04:57.960><c> cover</c><00:04:58.650><c> a</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
Google Analytics after so we'll cover a
 

00:04:58.800 --> 00:05:02.060 align:start position:0%
Google Analytics after so we'll cover a
solution<00:04:59.520><c> in</c><00:04:59.730><c> future</c>

