{"version": "0.2.0", "configurations": [{"name": "Next.js Dev Server", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/nextjs-youtube-summaries", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "FastAPI Server (Python)", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"], "cwd": "${workspaceFolder}/nextjs-youtube-summaries/python-service", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}}], "compounds": [{"name": "Start Both Servers", "configurations": ["Next.js Dev Server", "FastAPI Server (Python)"], "stopAll": true, "preLaunchTask": null}]}