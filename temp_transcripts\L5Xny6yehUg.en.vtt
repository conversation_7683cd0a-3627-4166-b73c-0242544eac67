WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.709 align:start position:0%
 
getting<00:00:00.359><c> started</c><00:00:00.719><c> with</c><00:00:00.880><c> your</c><00:00:01.040><c> work</c><00:00:01.360><c> usually</c>

00:00:01.709 --> 00:00:01.719 align:start position:0%
getting started with your work usually
 

00:00:01.719 --> 00:00:03.590 align:start position:0%
getting started with your work usually
takes<00:00:02.040><c> place</c><00:00:02.320><c> looking</c><00:00:02.560><c> at</c><00:00:02.679><c> a</c><00:00:02.800><c> project</c><00:00:03.080><c> board</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
takes place looking at a project board
 

00:00:03.600 --> 00:00:06.269 align:start position:0%
takes place looking at a project board
and<00:00:03.760><c> navigating</c><00:00:04.279><c> to</c><00:00:04.480><c> GitHub</c><00:00:04.880><c> issues</c><00:00:05.759><c> co-pilot</c>

00:00:06.269 --> 00:00:06.279 align:start position:0%
and navigating to GitHub issues co-pilot
 

00:00:06.279 --> 00:00:07.909 align:start position:0%
and navigating to GitHub issues co-pilot
workspace<00:00:06.720><c> brings</c><00:00:07.000><c> your</c><00:00:07.200><c> favorite</c><00:00:07.560><c> AI</c>

00:00:07.909 --> 00:00:07.919 align:start position:0%
workspace brings your favorite AI
 

00:00:07.919 --> 00:00:10.270 align:start position:0%
workspace brings your favorite AI
assistant<00:00:08.440><c> into</c><00:00:08.639><c> a</c><00:00:08.800><c> native</c><00:00:09.160><c> Dev</c><00:00:09.480><c> environment</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
assistant into a native Dev environment
 

00:00:10.280 --> 00:00:13.150 align:start position:0%
assistant into a native Dev environment
designed<00:00:10.639><c> for</c><00:00:10.840><c> everyday</c><00:00:11.320><c> tasks</c><00:00:12.200><c> for</c><00:00:12.360><c> example</c>

00:00:13.150 --> 00:00:13.160 align:start position:0%
designed for everyday tasks for example
 

00:00:13.160 --> 00:00:14.589 align:start position:0%
designed for everyday tasks for example
it<00:00:13.280><c> can</c><00:00:13.440><c> use</c><00:00:13.639><c> the</c><00:00:13.799><c> information</c><00:00:14.280><c> in</c><00:00:14.400><c> your</c>

00:00:14.589 --> 00:00:14.599 align:start position:0%
it can use the information in your
 

00:00:14.599 --> 00:00:16.870 align:start position:0%
it can use the information in your
GitHub<00:00:15.000><c> issue</c><00:00:15.599><c> along</c><00:00:16.000><c> with</c><00:00:16.240><c> references</c><00:00:16.720><c> from</c>

00:00:16.870 --> 00:00:16.880 align:start position:0%
GitHub issue along with references from
 

00:00:16.880 --> 00:00:18.710 align:start position:0%
GitHub issue along with references from
your<00:00:17.080><c> repository</c><00:00:17.880><c> to</c><00:00:18.080><c> build</c><00:00:18.320><c> out</c><00:00:18.520><c> a</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
your repository to build out a
 

00:00:18.720 --> 00:00:21.070 align:start position:0%
your repository to build out a
specification<00:00:19.680><c> based</c><00:00:19.880><c> on</c><00:00:20.000><c> the</c><00:00:20.160><c> current</c><00:00:20.480><c> state</c>

00:00:21.070 --> 00:00:21.080 align:start position:0%
specification based on the current state
 

00:00:21.080 --> 00:00:24.150 align:start position:0%
specification based on the current state
and<00:00:21.320><c> proposed</c><00:00:22.119><c> state</c><00:00:23.119><c> and</c><00:00:23.400><c> you</c><00:00:23.519><c> can</c><00:00:23.680><c> tailor</c>

00:00:24.150 --> 00:00:24.160 align:start position:0%
and proposed state and you can tailor
 

00:00:24.160 --> 00:00:25.990 align:start position:0%
and proposed state and you can tailor
the<00:00:24.320><c> spec</c><00:00:24.760><c> as</c><00:00:24.920><c> you</c><00:00:25.080><c> need</c><00:00:25.519><c> whether</c><00:00:25.760><c> that's</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
the spec as you need whether that's
 

00:00:26.000 --> 00:00:29.669 align:start position:0%
the spec as you need whether that's
adding<00:00:26.679><c> editing</c><00:00:27.400><c> or</c><00:00:27.720><c> removing</c><00:00:28.439><c> items</c><00:00:29.439><c> once</c>

00:00:29.669 --> 00:00:29.679 align:start position:0%
adding editing or removing items once
 

00:00:29.679 --> 00:00:32.429 align:start position:0%
adding editing or removing items once
ready<00:00:30.400><c> you</c><00:00:30.519><c> can</c><00:00:30.759><c> progress</c><00:00:31.199><c> from</c><00:00:31.400><c> spec</c><00:00:31.720><c> to</c><00:00:31.920><c> plan</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
ready you can progress from spec to plan
 

00:00:32.439 --> 00:00:35.069 align:start position:0%
ready you can progress from spec to plan
and<00:00:32.599><c> the</c><00:00:32.800><c> process</c><00:00:33.160><c> feels</c><00:00:33.800><c> familiar</c><00:00:34.800><c> with</c><00:00:34.920><c> the</c>

00:00:35.069 --> 00:00:35.079 align:start position:0%
and the process feels familiar with the
 

00:00:35.079 --> 00:00:36.990 align:start position:0%
and the process feels familiar with the
ability<00:00:35.480><c> to</c><00:00:35.719><c> adjust</c><00:00:36.000><c> the</c><00:00:36.120><c> planners</c><00:00:36.440><c> you</c><00:00:36.600><c> need</c>

00:00:36.990 --> 00:00:37.000 align:start position:0%
ability to adjust the planners you need
 

00:00:37.000 --> 00:00:39.709 align:start position:0%
ability to adjust the planners you need
by<00:00:37.239><c> creating</c><00:00:37.800><c> modifying</c><00:00:38.360><c> or</c><00:00:38.600><c> removing</c><00:00:39.040><c> files</c>

00:00:39.709 --> 00:00:39.719 align:start position:0%
by creating modifying or removing files
 

00:00:39.719 --> 00:00:41.590 align:start position:0%
by creating modifying or removing files
and<00:00:39.960><c> adjusting</c><00:00:40.440><c> the</c><00:00:40.640><c> expected</c><00:00:41.079><c> tasks</c><00:00:41.399><c> for</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
and adjusting the expected tasks for
 

00:00:41.600 --> 00:00:44.549 align:start position:0%
and adjusting the expected tasks for
each<00:00:41.960><c> step</c><00:00:42.960><c> this</c><00:00:43.120><c> leaves</c><00:00:43.360><c> you</c><00:00:43.559><c> in</c><00:00:43.760><c> control</c>

00:00:44.549 --> 00:00:44.559 align:start position:0%
each step this leaves you in control
 

00:00:44.559 --> 00:00:46.310 align:start position:0%
each step this leaves you in control
free<00:00:44.840><c> to</c><00:00:44.960><c> solve</c><00:00:45.280><c> the</c><00:00:45.399><c> higher</c><00:00:45.640><c> level</c><00:00:45.960><c> problems</c>

00:00:46.310 --> 00:00:46.320 align:start position:0%
free to solve the higher level problems
 

00:00:46.320 --> 00:00:48.430 align:start position:0%
free to solve the higher level problems
and<00:00:46.480><c> build</c><00:00:46.760><c> out</c><00:00:46.960><c> your</c><00:00:47.160><c> plan</c><00:00:47.800><c> before</c><00:00:48.160><c> getting</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
and build out your plan before getting
 

00:00:48.440 --> 00:00:50.790 align:start position:0%
and build out your plan before getting
into<00:00:48.640><c> the</c><00:00:48.800><c> finer</c><00:00:49.199><c> details</c><00:00:49.600><c> of</c><00:00:49.760><c> writing</c>

00:00:50.790 --> 00:00:50.800 align:start position:0%
into the finer details of writing
 

00:00:50.800 --> 00:00:53.709 align:start position:0%
into the finer details of writing
code<00:00:51.800><c> co-pilot</c><00:00:52.359><c> workspace</c><00:00:52.960><c> then</c><00:00:53.120><c> streams</c><00:00:53.520><c> the</c>

00:00:53.709 --> 00:00:53.719 align:start position:0%
code co-pilot workspace then streams the
 

00:00:53.719 --> 00:00:56.270 align:start position:0%
code co-pilot workspace then streams the
suggested<00:00:54.239><c> changes</c><00:00:54.640><c> to</c><00:00:54.800><c> our</c><00:00:55.280><c> environment</c>

00:00:56.270 --> 00:00:56.280 align:start position:0%
suggested changes to our environment
 

00:00:56.280 --> 00:00:58.029 align:start position:0%
suggested changes to our environment
notice<00:00:56.840><c> that</c><00:00:56.960><c> we</c><00:00:57.079><c> have</c><00:00:57.199><c> a</c><00:00:57.320><c> diff</c><00:00:57.600><c> view</c><00:00:57.840><c> to</c>

00:00:58.029 --> 00:00:58.039 align:start position:0%
notice that we have a diff view to
 

00:00:58.039 --> 00:01:00.349 align:start position:0%
notice that we have a diff view to
easily<00:00:58.480><c> digest</c><00:00:58.840><c> the</c><00:00:58.960><c> changes</c><00:00:59.480><c> and</c><00:00:59.680><c> can</c><00:01:00.039><c> easily</c>

00:01:00.349 --> 00:01:00.359 align:start position:0%
easily digest the changes and can easily
 

00:01:00.359 --> 00:01:01.950 align:start position:0%
easily digest the changes and can easily
make<00:01:00.600><c> updates</c><00:01:01.079><c> within</c><00:01:01.320><c> the</c>

00:01:01.950 --> 00:01:01.960 align:start position:0%
make updates within the
 

00:01:01.960 --> 00:01:04.590 align:start position:0%
make updates within the
editor<00:01:02.960><c> but</c><00:01:03.120><c> that's</c><00:01:03.320><c> not</c><00:01:03.480><c> all</c><00:01:04.239><c> at</c><00:01:04.400><c> our</c>

00:01:04.590 --> 00:01:04.600 align:start position:0%
editor but that's not all at our
 

00:01:04.600 --> 00:01:06.590 align:start position:0%
editor but that's not all at our
fingertips<00:01:05.560><c> we</c><00:01:05.760><c> have</c><00:01:05.960><c> access</c><00:01:06.240><c> to</c><00:01:06.400><c> an</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
fingertips we have access to an
 

00:01:06.600 --> 00:01:08.710 align:start position:0%
fingertips we have access to an
integrated<00:01:07.119><c> terminal</c><00:01:07.880><c> so</c><00:01:08.080><c> we</c><00:01:08.200><c> can</c><00:01:08.360><c> run</c><00:01:08.600><c> the</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
integrated terminal so we can run the
 

00:01:08.720 --> 00:01:10.910 align:start position:0%
integrated terminal so we can run the
test<00:01:09.000><c> in</c><00:01:09.159><c> our</c><00:01:09.360><c> workspace</c><00:01:10.159><c> before</c><00:01:10.520><c> committing</c>

00:01:10.910 --> 00:01:10.920 align:start position:0%
test in our workspace before committing
 

00:01:10.920 --> 00:01:13.950 align:start position:0%
test in our workspace before committing
our<00:01:11.119><c> changes</c><00:01:11.640><c> and</c><00:01:11.840><c> creating</c><00:01:12.240><c> a</c><00:01:12.400><c> poll</c><00:01:12.960><c> request</c>

00:01:13.950 --> 00:01:13.960 align:start position:0%
our changes and creating a poll request
 

00:01:13.960 --> 00:01:15.429 align:start position:0%
our changes and creating a poll request
and<00:01:14.159><c> what</c><00:01:14.320><c> if</c><00:01:14.439><c> you</c><00:01:14.560><c> want</c><00:01:14.720><c> to</c><00:01:14.880><c> make</c><00:01:15.040><c> use</c><00:01:15.240><c> of</c>

00:01:15.429 --> 00:01:15.439 align:start position:0%
and what if you want to make use of
 

00:01:15.439 --> 00:01:17.230 align:start position:0%
and what if you want to make use of
advanced<00:01:15.960><c> features</c><00:01:16.400><c> like</c><00:01:16.520><c> step</c><00:01:16.840><c> through</c>

00:01:17.230 --> 00:01:17.240 align:start position:0%
advanced features like step through
 

00:01:17.240 --> 00:01:20.230 align:start position:0%
advanced features like step through
debugging<00:01:18.240><c> no</c><00:01:18.520><c> problem</c><00:01:19.119><c> create</c><00:01:19.360><c> a</c><00:01:19.479><c> code</c><00:01:19.759><c> space</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
debugging no problem create a code space
 

00:01:20.240 --> 00:01:23.350 align:start position:0%
debugging no problem create a code space
and<00:01:20.479><c> pick</c><00:01:20.680><c> up</c><00:01:21.000><c> where</c><00:01:21.159><c> you</c><00:01:21.360><c> left</c><00:01:22.040><c> off</c><00:01:23.040><c> when</c><00:01:23.200><c> you</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
and pick up where you left off when you
 

00:01:23.360 --> 00:01:25.030 align:start position:0%
and pick up where you left off when you
raise<00:01:23.600><c> your</c><00:01:23.759><c> pull</c><00:01:24.040><c> request</c><00:01:24.360><c> with</c><00:01:24.520><c> co-pilot</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
raise your pull request with co-pilot
 

00:01:25.040 --> 00:01:27.230 align:start position:0%
raise your pull request with co-pilot
workspace<00:01:25.880><c> it</c><00:01:26.079><c> generates</c><00:01:26.479><c> a</c><00:01:26.640><c> description</c><00:01:27.040><c> for</c>

00:01:27.230 --> 00:01:27.240 align:start position:0%
workspace it generates a description for
 

00:01:27.240 --> 00:01:29.069 align:start position:0%
workspace it generates a description for
you<00:01:27.560><c> and</c><00:01:27.840><c> automatically</c><00:01:28.400><c> adds</c><00:01:28.600><c> a</c><00:01:28.720><c> link</c><00:01:28.960><c> to</c>

00:01:29.069 --> 00:01:29.079 align:start position:0%
you and automatically adds a link to
 

00:01:29.079 --> 00:01:31.270 align:start position:0%
you and automatically adds a link to
your<00:01:29.280><c> workspace</c><00:01:30.159><c> adding</c><00:01:30.439><c> a</c><00:01:30.600><c> little</c><00:01:30.840><c> bit</c><00:01:31.040><c> more</c>

00:01:31.270 --> 00:01:31.280 align:start position:0%
your workspace adding a little bit more
 

00:01:31.280 --> 00:01:33.510 align:start position:0%
your workspace adding a little bit more
context<00:01:31.680><c> for</c><00:01:31.880><c> the</c><00:01:32.000><c> reviewer</c><00:01:32.799><c> and</c><00:01:33.040><c> supporting</c>

00:01:33.510 --> 00:01:33.520 align:start position:0%
context for the reviewer and supporting
 

00:01:33.520 --> 00:01:36.230 align:start position:0%
context for the reviewer and supporting
their<00:01:33.680><c> code</c><00:01:33.960><c> review</c><00:01:34.720><c> workflow</c><00:01:35.720><c> and</c><00:01:35.880><c> as</c><00:01:36.000><c> it's</c><00:01:36.119><c> a</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
their code review workflow and as it's a
 

00:01:36.240 --> 00:01:38.990 align:start position:0%
their code review workflow and as it's a
pull<00:01:36.479><c> request</c><00:01:37.119><c> our</c><00:01:37.439><c> usual</c><00:01:37.880><c> checks</c><00:01:38.240><c> trigger</c>

00:01:38.990 --> 00:01:39.000 align:start position:0%
pull request our usual checks trigger
 

00:01:39.000 --> 00:01:41.789 align:start position:0%
pull request our usual checks trigger
including<00:01:39.479><c> GitHub</c><00:01:39.880><c> action</c><00:01:40.200><c> workflows</c><00:01:41.200><c> and</c>

00:01:41.789 --> 00:01:41.799 align:start position:0%
including GitHub action workflows and
 

00:01:41.799 --> 00:01:44.749 align:start position:0%
including GitHub action workflows and
code<00:01:42.439><c> scanning</c><00:01:43.439><c> co-pilot</c><00:01:43.960><c> workspace</c><00:01:44.439><c> leaves</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
code scanning co-pilot workspace leaves
 

00:01:44.759 --> 00:01:46.990 align:start position:0%
code scanning co-pilot workspace leaves
you<00:01:44.960><c> in</c><00:01:45.159><c> control</c><00:01:45.960><c> solving</c><00:01:46.280><c> the</c><00:01:46.439><c> higher</c><00:01:46.680><c> level</c>

00:01:46.990 --> 00:01:47.000 align:start position:0%
you in control solving the higher level
 

00:01:47.000 --> 00:01:49.469 align:start position:0%
you in control solving the higher level
problems<00:01:47.479><c> and</c><00:01:47.719><c> iterating</c><00:01:48.240><c> quickly</c><00:01:49.000><c> over</c><00:01:49.280><c> the</c>

00:01:49.469 --> 00:01:49.479 align:start position:0%
problems and iterating quickly over the
 

00:01:49.479 --> 00:01:51.780 align:start position:0%
problems and iterating quickly over the
solution

00:01:51.780 --> 00:01:51.790 align:start position:0%
solution
 

00:01:51.790 --> 00:01:55.009 align:start position:0%
solution
[Music]

