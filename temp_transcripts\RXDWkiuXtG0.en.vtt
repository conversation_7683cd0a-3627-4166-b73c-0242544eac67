WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:03.230 align:start position:0%
 
function<00:00:01.520><c> calling</c><00:00:02.399><c> this</c><00:00:02.639><c> has</c><00:00:02.800><c> to</c><00:00:02.919><c> be</c><00:00:03.120><c> the</c>

00:00:03.230 --> 00:00:03.240 align:start position:0%
function calling this has to be the
 

00:00:03.240 --> 00:00:06.070 align:start position:0%
function calling this has to be the
worst<00:00:03.560><c> name</c><00:00:03.760><c> for</c><00:00:03.919><c> a</c><00:00:04.040><c> feature</c><00:00:05.000><c> every</c><00:00:05.359><c> company</c>

00:00:06.070 --> 00:00:06.080 align:start position:0%
worst name for a feature every company
 

00:00:06.080 --> 00:00:08.509 align:start position:0%
worst name for a feature every company
I've<00:00:06.359><c> ever</c><00:00:06.600><c> worked</c><00:00:06.919><c> for</c><00:00:07.560><c> had</c><00:00:07.720><c> marketing</c><00:00:08.200><c> folks</c>

00:00:08.509 --> 00:00:08.519 align:start position:0%
I've ever worked for had marketing folks
 

00:00:08.519 --> 00:00:10.910 align:start position:0%
I've ever worked for had marketing folks
who<00:00:08.800><c> had</c><00:00:09.080><c> no</c><00:00:09.320><c> idea</c><00:00:09.599><c> what</c><00:00:09.679><c> the</c><00:00:09.800><c> feature</c><00:00:10.200><c> was</c><00:00:10.759><c> and</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
who had no idea what the feature was and
 

00:00:10.920 --> 00:00:12.070 align:start position:0%
who had no idea what the feature was and
called<00:00:11.200><c> that</c><00:00:11.360><c> feature</c><00:00:11.679><c> something</c><00:00:11.920><c> that</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
called that feature something that
 

00:00:12.080 --> 00:00:14.190 align:start position:0%
called that feature something that
sounded<00:00:12.440><c> cool</c><00:00:12.719><c> but</c><00:00:13.240><c> has</c><00:00:13.440><c> nothing</c><00:00:13.679><c> to</c><00:00:13.839><c> do</c><00:00:14.000><c> with</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
sounded cool but has nothing to do with
 

00:00:14.200 --> 00:00:16.630 align:start position:0%
sounded cool but has nothing to do with
the<00:00:14.360><c> actual</c><00:00:14.639><c> feature</c><00:00:15.639><c> this</c><00:00:15.920><c> is</c><00:00:16.160><c> sort</c><00:00:16.400><c> of</c>

00:00:16.630 --> 00:00:16.640 align:start position:0%
the actual feature this is sort of
 

00:00:16.640 --> 00:00:19.590 align:start position:0%
the actual feature this is sort of
related<00:00:17.640><c> but</c><00:00:17.800><c> it's</c><00:00:18.000><c> not</c><00:00:18.240><c> what</c><00:00:18.359><c> the</c><00:00:18.480><c> feature</c><00:00:18.840><c> is</c>

00:00:19.590 --> 00:00:19.600 align:start position:0%
related but it's not what the feature is
 

00:00:19.600 --> 00:00:21.550 align:start position:0%
related but it's not what the feature is
so<00:00:19.800><c> if</c><00:00:19.920><c> you</c><00:00:20.080><c> know</c><00:00:20.279><c> what</c><00:00:20.400><c> the</c><00:00:20.560><c> feature</c><00:00:20.960><c> is</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
so if you know what the feature is
 

00:00:21.560 --> 00:00:23.509 align:start position:0%
so if you know what the feature is
pretend<00:00:21.880><c> you</c><00:00:22.039><c> don't</c><00:00:22.480><c> and</c><00:00:22.680><c> think</c><00:00:23.080><c> of</c><00:00:23.279><c> what</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
pretend you don't and think of what
 

00:00:23.519 --> 00:00:26.630 align:start position:0%
pretend you don't and think of what
function<00:00:23.920><c> calling</c><00:00:24.400><c> should</c><00:00:25.199><c> mean</c><00:00:26.199><c> I</c><00:00:26.400><c> would</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
function calling should mean I would
 

00:00:26.640 --> 00:00:29.029 align:start position:0%
function calling should mean I would
think<00:00:27.119><c> that</c><00:00:27.279><c> function</c><00:00:27.640><c> calling</c><00:00:28.080><c> in</c><00:00:28.199><c> a</c><00:00:28.400><c> model</c>

00:00:29.029 --> 00:00:29.039 align:start position:0%
think that function calling in a model
 

00:00:29.039 --> 00:00:30.470 align:start position:0%
think that function calling in a model
means<00:00:29.320><c> that</c><00:00:29.480><c> the</c><00:00:29.599><c> model</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
means that the model
 

00:00:30.480 --> 00:00:33.389 align:start position:0%
means that the model
calls<00:00:31.599><c> functions</c><00:00:32.599><c> and</c><00:00:32.759><c> that</c><00:00:32.920><c> would</c><00:00:33.040><c> be</c><00:00:33.200><c> a</c>

00:00:33.389 --> 00:00:33.399 align:start position:0%
calls functions and that would be a
 

00:00:33.399 --> 00:00:35.670 align:start position:0%
calls functions and that would be a
great<00:00:33.640><c> feature</c><00:00:33.920><c> for</c><00:00:34.079><c> a</c><00:00:34.239><c> model</c><00:00:34.520><c> to</c><00:00:34.719><c> have</c><00:00:35.520><c> but</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
great feature for a model to have but
 

00:00:35.680 --> 00:00:37.630 align:start position:0%
great feature for a model to have but
the<00:00:35.879><c> model</c><00:00:36.280><c> has</c><00:00:36.600><c> no</c><00:00:36.879><c> ability</c><00:00:37.239><c> to</c><00:00:37.399><c> call</c>

00:00:37.630 --> 00:00:37.640 align:start position:0%
the model has no ability to call
 

00:00:37.640 --> 00:00:40.709 align:start position:0%
the model has no ability to call
functions<00:00:38.600><c> when</c><00:00:38.719><c> you</c><00:00:38.879><c> use</c><00:00:39.160><c> function</c><00:00:39.719><c> calling</c>

00:00:40.709 --> 00:00:40.719 align:start position:0%
functions when you use function calling
 

00:00:40.719 --> 00:00:42.270 align:start position:0%
functions when you use function calling
function<00:00:41.079><c> calling</c><00:00:41.600><c> is</c><00:00:41.800><c> all</c><00:00:42.000><c> about</c>

00:00:42.270 --> 00:00:42.280 align:start position:0%
function calling is all about
 

00:00:42.280 --> 00:00:44.709 align:start position:0%
function calling is all about
controlling<00:00:42.760><c> the</c><00:00:42.960><c> format</c><00:00:43.719><c> of</c><00:00:43.879><c> the</c><00:00:44.079><c> output</c>

00:00:44.709 --> 00:00:44.719 align:start position:0%
controlling the format of the output
 

00:00:44.719 --> 00:00:45.590 align:start position:0%
controlling the format of the output
from<00:00:44.920><c> the</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
from the
 

00:00:45.600 --> 00:00:47.830 align:start position:0%
from the
model<00:00:46.600><c> when</c><00:00:46.719><c> you</c><00:00:46.840><c> get</c><00:00:47.000><c> the</c><00:00:47.199><c> response</c><00:00:47.559><c> in</c><00:00:47.680><c> a</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
model when you get the response in a
 

00:00:47.840 --> 00:00:50.470 align:start position:0%
model when you get the response in a
format<00:00:48.199><c> you</c><00:00:48.440><c> expect</c><00:00:49.280><c> then</c><00:00:49.440><c> you</c><00:00:49.559><c> can</c><00:00:49.840><c> reliably</c>

00:00:50.470 --> 00:00:50.480 align:start position:0%
format you expect then you can reliably
 

00:00:50.480 --> 00:00:54.430 align:start position:0%
format you expect then you can reliably
call<00:00:51.239><c> a</c><00:00:51.600><c> function</c><00:00:52.600><c> but</c><00:00:53.239><c> you</c><00:00:53.680><c> are</c><00:00:53.960><c> calling</c><00:00:54.320><c> the</c>

00:00:54.430 --> 00:00:54.440 align:start position:0%
call a function but you are calling the
 

00:00:54.440 --> 00:00:56.830 align:start position:0%
call a function but you are calling the
function<00:00:54.879><c> the</c><00:00:55.039><c> model</c><00:00:55.399><c> is</c><00:00:55.600><c> not</c><00:00:56.039><c> or</c><00:00:56.320><c> even</c><00:00:56.480><c> if</c><00:00:56.640><c> we</c>

00:00:56.830 --> 00:00:56.840 align:start position:0%
function the model is not or even if we
 

00:00:56.840 --> 00:00:58.750 align:start position:0%
function the model is not or even if we
expand<00:00:57.199><c> it</c><00:00:57.359><c> out</c><00:00:57.520><c> to</c><00:00:57.680><c> the</c><00:00:57.800><c> company</c><00:00:58.559><c> that</c>

00:00:58.750 --> 00:00:58.760 align:start position:0%
expand it out to the company that
 

00:00:58.760 --> 00:01:01.709 align:start position:0%
expand it out to the company that
created<00:00:59.199><c> the</c><00:00:59.320><c> feature</c><00:01:00.160><c> open</c><00:01:00.480><c> AI</c><00:01:01.280><c> does</c><00:01:01.519><c> not</c>

00:01:01.709 --> 00:01:01.719 align:start position:0%
created the feature open AI does not
 

00:01:01.719 --> 00:01:04.030 align:start position:0%
created the feature open AI does not
call<00:01:02.039><c> the</c><00:01:02.199><c> function</c><00:01:03.039><c> your</c><00:01:03.440><c> program</c><00:01:03.879><c> that</c>

00:01:04.030 --> 00:01:04.040 align:start position:0%
call the function your program that
 

00:01:04.040 --> 00:01:07.190 align:start position:0%
call the function your program that
calls<00:01:04.360><c> the</c><00:01:04.519><c> open</c><00:01:04.799><c> a</c><00:01:05.080><c> API</c><00:01:05.960><c> calls</c><00:01:06.320><c> the</c><00:01:06.439><c> function</c>

00:01:07.190 --> 00:01:07.200 align:start position:0%
calls the open a API calls the function
 

00:01:07.200 --> 00:01:09.310 align:start position:0%
calls the open a API calls the function
don't<00:01:07.439><c> believe</c><00:01:07.680><c> me</c><00:01:08.439><c> let's</c><00:01:08.640><c> go</c><00:01:08.759><c> to</c><00:01:08.920><c> the</c><00:01:09.040><c> open</c>

00:01:09.310 --> 00:01:09.320 align:start position:0%
don't believe me let's go to the open
 

00:01:09.320 --> 00:01:11.950 align:start position:0%
don't believe me let's go to the open
aai<00:01:09.720><c> docs</c><00:01:10.280><c> and</c><00:01:10.439><c> go</c><00:01:10.560><c> to</c><00:01:10.720><c> function</c><00:01:11.080><c> calling</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
aai docs and go to function calling
 

00:01:11.960 --> 00:01:15.070 align:start position:0%
aai docs and go to function calling
scroll<00:01:12.520><c> down</c><00:01:12.960><c> to</c><00:01:13.159><c> this</c><00:01:13.360><c> four-step</c><00:01:14.119><c> process</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
scroll down to this four-step process
 

00:01:15.080 --> 00:01:17.630 align:start position:0%
scroll down to this four-step process
step<00:01:15.360><c> three</c><00:01:16.119><c> you</c><00:01:16.439><c> parse</c><00:01:16.759><c> the</c><00:01:16.880><c> string</c><00:01:17.200><c> output</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
step three you parse the string output
 

00:01:17.640 --> 00:01:20.109 align:start position:0%
step three you parse the string output
of<00:01:17.799><c> the</c><00:01:17.880><c> model</c><00:01:18.200><c> into</c><00:01:18.439><c> Json</c><00:01:19.280><c> then</c><00:01:19.479><c> you</c><00:01:19.720><c> pull</c><00:01:19.920><c> out</c>

00:01:20.109 --> 00:01:20.119 align:start position:0%
of the model into Json then you pull out
 

00:01:20.119 --> 00:01:23.550 align:start position:0%
of the model into Json then you pull out
the<00:01:20.280><c> values</c><00:01:21.159><c> then</c><00:01:21.400><c> you</c><00:01:21.799><c> call</c><00:01:22.119><c> the</c><00:01:22.400><c> function</c><00:01:23.400><c> I</c>

00:01:23.550 --> 00:01:23.560 align:start position:0%
the values then you call the function I
 

00:01:23.560 --> 00:01:26.789 align:start position:0%
the values then you call the function I
am<00:01:24.000><c> great</c><00:01:24.360><c> with</c><00:01:24.560><c> that</c><00:01:25.119><c> it's</c><00:01:25.360><c> an</c><00:01:25.720><c> awesome</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
am great with that it's an awesome
 

00:01:26.799 --> 00:01:29.550 align:start position:0%
am great with that it's an awesome
feature<00:01:27.799><c> it</c><00:01:27.920><c> would</c><00:01:28.119><c> just</c><00:01:28.240><c> be</c><00:01:28.439><c> better</c><00:01:29.280><c> if</c><00:01:29.439><c> it</c>

00:01:29.550 --> 00:01:29.560 align:start position:0%
feature it would just be better if it
 

00:01:29.560 --> 00:01:34.310 align:start position:0%
feature it would just be better if it
was<00:01:29.920><c> the</c><00:01:30.079><c> name</c><00:01:30.799><c> was</c><00:01:31.840><c> accurate</c><00:01:32.840><c> so</c><00:01:33.320><c> okay</c><00:01:33.799><c> so</c>

00:01:34.310 --> 00:01:34.320 align:start position:0%
was the name was accurate so okay so
 

00:01:34.320 --> 00:01:36.350 align:start position:0%
was the name was accurate so okay so
with<00:01:34.560><c> that</c><00:01:34.759><c> settled</c><00:01:35.240><c> let's</c><00:01:35.479><c> look</c><00:01:35.600><c> at</c><00:01:35.799><c> how</c><00:01:36.040><c> open</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
with that settled let's look at how open
 

00:01:36.360 --> 00:01:40.389 align:start position:0%
with that settled let's look at how open
AI<00:01:37.000><c> API</c><00:01:38.000><c> achieves</c><00:01:38.520><c> function</c><00:01:39.119><c> calling</c><00:01:40.119><c> one</c><00:01:40.280><c> of</c>

00:01:40.389 --> 00:01:40.399 align:start position:0%
AI API achieves function calling one of
 

00:01:40.399 --> 00:01:42.230 align:start position:0%
AI API achieves function calling one of
the<00:01:40.520><c> important</c><00:01:40.920><c> steps</c><00:01:41.280><c> is</c><00:01:41.399><c> to</c><00:01:41.600><c> inform</c><00:01:41.960><c> open</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
the important steps is to inform open
 

00:01:42.240 --> 00:01:45.069 align:start position:0%
the important steps is to inform open
aai<00:01:42.880><c> about</c><00:01:43.119><c> your</c><00:01:43.640><c> functions</c><00:01:44.640><c> what</c><00:01:44.840><c> the</c>

00:01:45.069 --> 00:01:45.079 align:start position:0%
aai about your functions what the
 

00:01:45.079 --> 00:01:46.749 align:start position:0%
aai about your functions what the
function<00:01:45.560><c> actually</c>

00:01:46.749 --> 00:01:46.759 align:start position:0%
function actually
 

00:01:46.759 --> 00:01:50.389 align:start position:0%
function actually
does<00:01:47.759><c> isn't</c><00:01:48.320><c> relevant</c><00:01:49.320><c> this</c><00:01:49.439><c> is</c><00:01:49.719><c> just</c><00:01:49.960><c> how</c><00:01:50.159><c> you</c>

00:01:50.389 --> 00:01:50.399 align:start position:0%
does isn't relevant this is just how you
 

00:01:50.399 --> 00:01:53.270 align:start position:0%
does isn't relevant this is just how you
let<00:01:50.600><c> the</c><00:01:50.719><c> model</c><00:01:51.119><c> know</c><00:01:51.840><c> about</c><00:01:52.079><c> the</c><00:01:52.280><c> schema</c><00:01:53.119><c> the</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
let the model know about the schema the
 

00:01:53.280 --> 00:01:56.230 align:start position:0%
let the model know about the schema the
output<00:01:53.680><c> should</c><00:01:53.920><c> stick</c><00:01:54.320><c> to</c><00:01:55.320><c> the</c><00:01:55.560><c> rest</c><00:01:55.799><c> of</c><00:01:56.039><c> this</c>

00:01:56.230 --> 00:01:56.240 align:start position:0%
output should stick to the rest of this
 

00:01:56.240 --> 00:01:59.550 align:start position:0%
output should stick to the rest of this
example<00:01:57.159><c> is</c><00:01:57.439><c> using</c><00:01:57.799><c> the</c><00:01:57.960><c> chat</c><00:01:58.360><c> API</c><00:01:59.079><c> to</c><00:01:59.280><c> ask</c><00:01:59.439><c> the</c>

00:01:59.550 --> 00:01:59.560 align:start position:0%
example is using the chat API to ask the
 

00:01:59.560 --> 00:02:02.590 align:start position:0%
example is using the chat API to ask the
model<00:02:00.240><c> a</c><00:02:00.479><c> question</c><00:02:01.479><c> and</c><00:02:01.719><c> then</c><00:02:02.159><c> the</c><00:02:02.280><c> chat</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
model a question and then the chat
 

00:02:02.600 --> 00:02:05.389 align:start position:0%
model a question and then the chat
completion<00:02:03.320><c> message</c><00:02:04.119><c> shows</c><00:02:04.479><c> us</c><00:02:04.759><c> the</c><00:02:04.920><c> output</c>

00:02:05.389 --> 00:02:05.399 align:start position:0%
completion message shows us the output
 

00:02:05.399 --> 00:02:07.550 align:start position:0%
completion message shows us the output
string<00:02:06.079><c> that</c><00:02:06.240><c> we</c><00:02:06.439><c> format</c><00:02:06.880><c> to</c>

00:02:07.550 --> 00:02:07.560 align:start position:0%
string that we format to
 

00:02:07.560 --> 00:02:10.510 align:start position:0%
string that we format to
Json<00:02:08.560><c> now</c><00:02:08.959><c> let's</c><00:02:09.160><c> move</c><00:02:09.399><c> on</c><00:02:09.759><c> and</c><00:02:09.920><c> look</c><00:02:10.080><c> at</c><00:02:10.319><c> how</c>

00:02:10.510 --> 00:02:10.520 align:start position:0%
Json now let's move on and look at how
 

00:02:10.520 --> 00:02:13.550 align:start position:0%
Json now let's move on and look at how
to<00:02:10.759><c> achieve</c><00:02:11.160><c> this</c><00:02:11.400><c> in</c><00:02:12.040><c> ama</c><00:02:13.040><c> the</c><00:02:13.200><c> feature</c>

00:02:13.550 --> 00:02:13.560 align:start position:0%
to achieve this in ama the feature
 

00:02:13.560 --> 00:02:15.509 align:start position:0%
to achieve this in ama the feature
you'll<00:02:13.840><c> find</c><00:02:14.120><c> in</c><00:02:14.239><c> the</c><00:02:14.400><c> docs</c><00:02:14.760><c> is</c><00:02:14.959><c> referred</c><00:02:15.280><c> to</c>

00:02:15.509 --> 00:02:15.519 align:start position:0%
you'll find in the docs is referred to
 

00:02:15.519 --> 00:02:18.470 align:start position:0%
you'll find in the docs is referred to
as<00:02:15.800><c> format</c><00:02:16.319><c> Json</c><00:02:17.319><c> which</c><00:02:17.480><c> is</c><00:02:17.680><c> a</c><00:02:17.920><c> bit</c><00:02:18.120><c> more</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
as format Json which is a bit more
 

00:02:18.480 --> 00:02:20.589 align:start position:0%
as format Json which is a bit more
descriptive<00:02:19.239><c> about</c><00:02:19.519><c> what</c><00:02:19.680><c> is</c><00:02:20.000><c> really</c><00:02:20.360><c> going</c>

00:02:20.589 --> 00:02:20.599 align:start position:0%
descriptive about what is really going
 

00:02:20.599 --> 00:02:23.150 align:start position:0%
descriptive about what is really going
on<00:02:21.560><c> there</c><00:02:21.680><c> are</c><00:02:22.000><c> two</c><00:02:22.319><c> requirements</c><00:02:22.840><c> to</c><00:02:23.000><c> get</c>

00:02:23.150 --> 00:02:23.160 align:start position:0%
on there are two requirements to get
 

00:02:23.160 --> 00:02:26.550 align:start position:0%
on there are two requirements to get
this<00:02:23.720><c> working</c><00:02:24.720><c> and</c><00:02:25.120><c> one</c><00:02:25.480><c> bonus</c><00:02:25.920><c> one</c><00:02:26.400><c> that</c>

00:02:26.550 --> 00:02:26.560 align:start position:0%
this working and one bonus one that
 

00:02:26.560 --> 00:02:29.070 align:start position:0%
this working and one bonus one that
makes<00:02:26.760><c> it</c><00:02:27.040><c> even</c><00:02:27.760><c> better</c><00:02:28.480><c> you</c><00:02:28.599><c> can</c><00:02:28.760><c> do</c><00:02:28.879><c> it</c><00:02:28.959><c> in</c>

00:02:29.070 --> 00:02:29.080 align:start position:0%
makes it even better you can do it in
 

00:02:29.080 --> 00:02:32.229 align:start position:0%
makes it even better you can do it in
the<00:02:29.200><c> API</c><00:02:29.840><c> or</c><00:02:30.239><c> even</c><00:02:30.480><c> in</c><00:02:30.640><c> the</c><00:02:30.959><c> CLI</c><00:02:31.959><c> if</c><00:02:32.080><c> you're</c>

00:02:32.229 --> 00:02:32.239 align:start position:0%
the API or even in the CLI if you're
 

00:02:32.239 --> 00:02:34.869 align:start position:0%
the API or even in the CLI if you're
scripting<00:02:32.720><c> something</c><00:02:32.959><c> in</c><00:02:33.319><c> bash</c><00:02:34.319><c> let's</c><00:02:34.560><c> use</c>

00:02:34.869 --> 00:02:34.879 align:start position:0%
scripting something in bash let's use
 

00:02:34.879 --> 00:02:37.030 align:start position:0%
scripting something in bash let's use
Python<00:02:35.239><c> for</c><00:02:35.440><c> this</c><00:02:35.599><c> one</c><00:02:35.879><c> and</c><00:02:36.239><c> I'm</c><00:02:36.480><c> not</c><00:02:36.720><c> going</c><00:02:36.879><c> to</c>

00:02:37.030 --> 00:02:37.040 align:start position:0%
Python for this one and I'm not going to
 

00:02:37.040 --> 00:02:40.270 align:start position:0%
Python for this one and I'm not going to
use<00:02:37.239><c> the</c><00:02:37.360><c> SDK</c><00:02:38.000><c> just</c><00:02:38.120><c> to</c><00:02:38.319><c> keep</c><00:02:38.560><c> things</c><00:02:39.440><c> more</c>

00:02:40.270 --> 00:02:40.280 align:start position:0%
use the SDK just to keep things more
 

00:02:40.280 --> 00:02:43.309 align:start position:0%
use the SDK just to keep things more
translatable<00:02:41.280><c> to</c><00:02:41.480><c> other</c><00:02:42.000><c> languages</c><00:02:43.000><c> we</c><00:02:43.080><c> can</c>

00:02:43.309 --> 00:02:43.319 align:start position:0%
translatable to other languages we can
 

00:02:43.319 --> 00:02:45.949 align:start position:0%
translatable to other languages we can
start<00:02:43.599><c> with</c><00:02:43.840><c> importing</c><00:02:44.400><c> requests</c><00:02:45.159><c> now</c><00:02:45.519><c> we</c><00:02:45.640><c> can</c>

00:02:45.949 --> 00:02:45.959 align:start position:0%
start with importing requests now we can
 

00:02:45.959 --> 00:02:48.070 align:start position:0%
start with importing requests now we can
define<00:02:46.280><c> a</c><00:02:46.519><c> payload</c><00:02:47.440><c> that</c><00:02:47.560><c> we're</c><00:02:47.800><c> going</c><00:02:47.959><c> to</c>

00:02:48.070 --> 00:02:48.080 align:start position:0%
define a payload that we're going to
 

00:02:48.080 --> 00:02:50.830 align:start position:0%
define a payload that we're going to
send<00:02:48.319><c> to</c><00:02:48.440><c> the</c><00:02:48.599><c> chat</c><00:02:49.080><c> endpoint</c><00:02:50.080><c> this</c><00:02:50.280><c> includes</c>

00:02:50.830 --> 00:02:50.840 align:start position:0%
send to the chat endpoint this includes
 

00:02:50.840 --> 00:02:54.509 align:start position:0%
send to the chat endpoint this includes
a<00:02:51.080><c> model</c><00:02:52.080><c> and</c><00:02:52.400><c> an</c><00:02:52.560><c> array</c><00:02:52.920><c> of</c><00:02:53.400><c> messages</c><00:02:54.400><c> we</c>

00:02:54.509 --> 00:02:54.519 align:start position:0%
a model and an array of messages we
 

00:02:54.519 --> 00:02:57.110 align:start position:0%
a model and an array of messages we
don't<00:02:54.760><c> need</c><00:02:54.959><c> to</c><00:02:55.159><c> include</c><00:02:55.560><c> a</c><00:02:55.800><c> system</c><00:02:56.200><c> prompt</c>

00:02:57.110 --> 00:02:57.120 align:start position:0%
don't need to include a system prompt
 

00:02:57.120 --> 00:02:59.790 align:start position:0%
don't need to include a system prompt
since<00:02:57.640><c> there's</c><00:02:58.000><c> already</c><00:02:58.400><c> one</c><00:02:58.800><c> in</c><00:02:58.959><c> the</c><00:02:59.120><c> model</c>

00:02:59.790 --> 00:02:59.800 align:start position:0%
since there's already one in the model
 

00:02:59.800 --> 00:03:05.110 align:start position:0%
since there's already one in the model
in<00:03:00.120><c> ama</c><00:03:01.120><c> so</c><00:03:01.519><c> roll</c><00:03:02.159><c> user</c><00:03:03.159><c> and</c><00:03:03.680><c> content</c><00:03:04.680><c> uh</c><00:03:04.920><c> what</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
in ama so roll user and content uh what
 

00:03:05.120 --> 00:03:08.190 align:start position:0%
in ama so roll user and content uh what
is<00:03:05.400><c> the</c><00:03:05.640><c> capital</c><00:03:06.040><c> of</c><00:03:06.440><c> Germany</c><00:03:07.440><c> now</c><00:03:07.799><c> set</c>

00:03:08.190 --> 00:03:08.200 align:start position:0%
is the capital of Germany now set
 

00:03:08.200 --> 00:03:11.430 align:start position:0%
is the capital of Germany now set
response<00:03:08.640><c> to</c><00:03:09.080><c> a</c><00:03:09.400><c> post</c><00:03:09.720><c> request</c><00:03:10.440><c> to</c>

00:03:11.430 --> 00:03:11.440 align:start position:0%
response to a post request to
 

00:03:11.440 --> 00:03:14.350 align:start position:0%
response to a post request to
http<00:03:12.440><c> localhost</c><00:03:13.080><c> Port</c>

00:03:14.350 --> 00:03:14.360 align:start position:0%
http localhost Port
 

00:03:14.360 --> 00:03:18.430 align:start position:0%
http localhost Port
11434<00:03:15.360><c> API</c><00:03:16.159><c> chat</c><00:03:16.959><c> and</c><00:03:17.080><c> setting</c><00:03:17.360><c> our</c><00:03:17.599><c> payload</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
11434 API chat and setting our payload
 

00:03:18.440 --> 00:03:21.270 align:start position:0%
11434 API chat and setting our payload
to<00:03:18.599><c> the</c><00:03:18.720><c> Json</c><00:03:19.720><c> now</c><00:03:20.040><c> print</c><00:03:20.280><c> out</c><00:03:20.440><c> the</c><00:03:20.560><c> Json</c><00:03:21.040><c> from</c>

00:03:21.270 --> 00:03:21.280 align:start position:0%
to the Json now print out the Json from
 

00:03:21.280 --> 00:03:23.470 align:start position:0%
to the Json now print out the Json from
the<00:03:21.519><c> response</c><00:03:22.239><c> to</c><00:03:22.400><c> get</c><00:03:22.519><c> a</c><00:03:22.640><c> streaming</c><00:03:23.159><c> response</c>

00:03:23.470 --> 00:03:23.480 align:start position:0%
the response to get a streaming response
 

00:03:23.480 --> 00:03:25.190 align:start position:0%
the response to get a streaming response
from<00:03:23.760><c> python</c><00:03:24.159><c> we</c><00:03:24.280><c> need</c><00:03:24.400><c> to</c><00:03:24.560><c> iterate</c><00:03:25.000><c> through</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
from python we need to iterate through
 

00:03:25.200 --> 00:03:28.589 align:start position:0%
from python we need to iterate through
the<00:03:25.440><c> response</c><00:03:26.440><c> so</c><00:03:26.920><c> for</c><00:03:27.159><c> message</c><00:03:27.560><c> in</c><00:03:27.799><c> response.</c>

00:03:28.589 --> 00:03:28.599 align:start position:0%
the response so for message in response.
 

00:03:28.599 --> 00:03:31.750 align:start position:0%
the response so for message in response.
iter<00:03:29.000><c> lines</c><00:03:29.920><c> then</c><00:03:30.159><c> parse</c><00:03:30.480><c> the</c><00:03:30.640><c> Json</c><00:03:31.200><c> and</c><00:03:31.480><c> print</c>

00:03:31.750 --> 00:03:31.760 align:start position:0%
iter lines then parse the Json and print
 

00:03:31.760 --> 00:03:35.110 align:start position:0%
iter lines then parse the Json and print
message.<00:03:32.480><c> content</c><00:03:33.480><c> let's</c><00:03:33.720><c> add</c><00:03:34.000><c> and</c><00:03:34.640><c> set</c><00:03:34.879><c> to</c>

00:03:35.110 --> 00:03:35.120 align:start position:0%
message. content let's add and set to
 

00:03:35.120 --> 00:03:37.990 align:start position:0%
message. content let's add and set to
nothing<00:03:36.080><c> to</c><00:03:36.319><c> put</c><00:03:36.599><c> everything</c><00:03:36.879><c> on</c><00:03:37.120><c> one</c><00:03:37.319><c> line</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
nothing to put everything on one line
 

00:03:38.000 --> 00:03:40.550 align:start position:0%
nothing to put everything on one line
unless<00:03:38.360><c> the</c><00:03:38.519><c> response</c><00:03:38.920><c> includes</c><00:03:39.239><c> a</c><00:03:39.360><c> new</c><00:03:39.560><c> line</c>

00:03:40.550 --> 00:03:40.560 align:start position:0%
unless the response includes a new line
 

00:03:40.560 --> 00:03:43.350 align:start position:0%
unless the response includes a new line
Python<00:03:41.159><c> fc.</c><00:03:41.879><c> piy</c><00:03:42.319><c> and</c><00:03:42.480><c> we</c><00:03:42.640><c> get</c><00:03:42.799><c> something</c>

00:03:43.350 --> 00:03:43.360 align:start position:0%
Python fc. piy and we get something
 

00:03:43.360 --> 00:03:45.390 align:start position:0%
Python fc. piy and we get something
saying<00:03:43.760><c> that</c><00:03:43.959><c> the</c><00:03:44.120><c> capital</c><00:03:44.400><c> of</c><00:03:44.519><c> Germany</c><00:03:44.920><c> is</c>

00:03:45.390 --> 00:03:45.400 align:start position:0%
saying that the capital of Germany is
 

00:03:45.400 --> 00:03:48.350 align:start position:0%
saying that the capital of Germany is
Berlin<00:03:46.439><c> awesome</c><00:03:47.439><c> now</c><00:03:47.680><c> let's</c><00:03:47.879><c> make</c><00:03:48.080><c> this</c><00:03:48.200><c> a</c>

00:03:48.350 --> 00:03:48.360 align:start position:0%
Berlin awesome now let's make this a
 

00:03:48.360 --> 00:03:50.869 align:start position:0%
Berlin awesome now let's make this a
touch<00:03:48.560><c> more</c><00:03:48.840><c> Dynamic</c><00:03:49.640><c> by</c><00:03:49.799><c> taking</c><00:03:50.080><c> the</c><00:03:50.239><c> country</c>

00:03:50.869 --> 00:03:50.879 align:start position:0%
touch more Dynamic by taking the country
 

00:03:50.879 --> 00:03:53.350 align:start position:0%
touch more Dynamic by taking the country
as<00:03:51.000><c> a</c><00:03:51.120><c> command</c><00:03:51.480><c> line</c><00:03:51.720><c> argument</c><00:03:52.720><c> I'll</c><00:03:53.000><c> import</c>

00:03:53.350 --> 00:03:53.360 align:start position:0%
as a command line argument I'll import
 

00:03:53.360 --> 00:03:56.789 align:start position:0%
as a command line argument I'll import
CIS<00:03:53.920><c> and</c><00:03:54.040><c> then</c><00:03:54.200><c> set</c><00:03:54.519><c> country</c><00:03:54.959><c> to</c><00:03:55.360><c> cy.</c>

00:03:56.789 --> 00:03:56.799 align:start position:0%
CIS and then set country to cy.
 

00:03:56.799 --> 00:03:59.069 align:start position:0%
CIS and then set country to cy.
arv1<00:03:57.799><c> then</c><00:03:57.959><c> change</c><00:03:58.319><c> the</c><00:03:58.519><c> content</c><00:03:58.840><c> in</c><00:03:58.959><c> the</c>

00:03:59.069 --> 00:03:59.079 align:start position:0%
arv1 then change the content in the
 

00:03:59.079 --> 00:04:01.470 align:start position:0%
arv1 then change the content in the
payload<00:03:59.760><c> to</c><00:03:59.920><c> be</c><00:04:00.040><c> an</c><00:04:00.159><c> FST</c><00:04:00.360><c> string</c><00:04:00.760><c> and</c><00:04:00.920><c> include</c>

00:04:01.470 --> 00:04:01.480 align:start position:0%
payload to be an FST string and include
 

00:04:01.480 --> 00:04:05.110 align:start position:0%
payload to be an FST string and include
country<00:04:02.200><c> in</c><00:04:02.400><c> curly</c><00:04:02.720><c> braces</c><00:04:03.640><c> python</c><00:04:04.120><c> fc.</c>

00:04:05.110 --> 00:04:05.120 align:start position:0%
country in curly braces python fc.
 

00:04:05.120 --> 00:04:08.270 align:start position:0%
country in curly braces python fc.
France<00:04:05.799><c> gets</c><00:04:06.040><c> us</c><00:04:06.480><c> Paris</c><00:04:07.480><c> now</c><00:04:07.760><c> let's</c><00:04:07.959><c> say</c><00:04:08.159><c> we</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
France gets us Paris now let's say we
 

00:04:08.280 --> 00:04:10.149 align:start position:0%
France gets us Paris now let's say we
want<00:04:08.439><c> to</c><00:04:08.640><c> find</c><00:04:08.920><c> the</c><00:04:09.159><c> capital</c><00:04:09.519><c> of</c><00:04:09.680><c> that</c><00:04:09.840><c> country</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
want to find the capital of that country
 

00:04:10.159 --> 00:04:12.509 align:start position:0%
want to find the capital of that country
and<00:04:10.280><c> then</c><00:04:10.519><c> calculate</c><00:04:11.000><c> the</c><00:04:11.200><c> distance</c><00:04:12.200><c> between</c>

00:04:12.509 --> 00:04:12.519 align:start position:0%
and then calculate the distance between
 

00:04:12.519 --> 00:04:15.750 align:start position:0%
and then calculate the distance between
Bainbridge<00:04:13.079><c> Island</c><00:04:13.799><c> where</c><00:04:14.159><c> I</c><00:04:14.360><c> live</c><00:04:15.200><c> and</c><00:04:15.480><c> that</c>

00:04:15.750 --> 00:04:15.760 align:start position:0%
Bainbridge Island where I live and that
 

00:04:15.760 --> 00:04:18.550 align:start position:0%
Bainbridge Island where I live and that
City<00:04:16.720><c> so</c><00:04:16.880><c> for</c><00:04:17.120><c> that</c><00:04:17.320><c> we</c><00:04:17.400><c> can</c><00:04:17.560><c> use</c><00:04:17.799><c> the</c><00:04:17.959><c> havene</c>

00:04:18.550 --> 00:04:18.560 align:start position:0%
City so for that we can use the havene
 

00:04:18.560 --> 00:04:20.870 align:start position:0%
City so for that we can use the havene
package<00:04:19.239><c> which</c><00:04:19.400><c> takes</c><00:04:19.639><c> the</c><00:04:19.880><c> decimal</c><00:04:20.320><c> latitude</c>

00:04:20.870 --> 00:04:20.880 align:start position:0%
package which takes the decimal latitude
 

00:04:20.880 --> 00:04:23.270 align:start position:0%
package which takes the decimal latitude
and<00:04:21.040><c> longitude</c><00:04:21.639><c> of</c><00:04:21.799><c> two</c><00:04:22.120><c> places</c><00:04:23.080><c> and</c>

00:04:23.270 --> 00:04:23.280 align:start position:0%
and longitude of two places and
 

00:04:23.280 --> 00:04:25.909 align:start position:0%
and longitude of two places and
calculates<00:04:23.800><c> the</c><00:04:23.960><c> distance</c><00:04:24.360><c> between</c><00:04:24.680><c> them</c><00:04:25.600><c> so</c>

00:04:25.909 --> 00:04:25.919 align:start position:0%
calculates the distance between them so
 

00:04:25.919 --> 00:04:27.830 align:start position:0%
calculates the distance between them so
let's<00:04:26.120><c> change</c><00:04:26.360><c> our</c><00:04:26.560><c> question</c><00:04:26.919><c> to</c><00:04:27.320><c> what</c><00:04:27.440><c> is</c><00:04:27.600><c> a</c>

00:04:27.830 --> 00:04:27.840 align:start position:0%
let's change our question to what is a
 

00:04:27.840 --> 00:04:30.710 align:start position:0%
let's change our question to what is a
decimal<00:04:28.320><c> latitude</c><00:04:29.160><c> and</c><00:04:29.320><c> de</c><00:04:29.639><c> small</c><00:04:29.840><c> longitude</c>

00:04:30.710 --> 00:04:30.720 align:start position:0%
decimal latitude and de small longitude
 

00:04:30.720 --> 00:04:32.909 align:start position:0%
decimal latitude and de small longitude
of<00:04:30.880><c> the</c><00:04:31.120><c> capital</c><00:04:31.600><c> of</c><00:04:32.120><c> an</c><00:04:32.280><c> in</c><00:04:32.479><c> then</c><00:04:32.639><c> curly</c>

00:04:32.909 --> 00:04:32.919 align:start position:0%
of the capital of an in then curly
 

00:04:32.919 --> 00:04:33.909 align:start position:0%
of the capital of an in then curly
braces

00:04:33.909 --> 00:04:33.919 align:start position:0%
braces
 

00:04:33.919 --> 00:04:37.950 align:start position:0%
braces
country<00:04:34.919><c> and</c><00:04:35.080><c> run</c><00:04:35.440><c> python</c><00:04:35.880><c> fc.</c><00:04:36.520><c> piy</c><00:04:37.000><c> Portugal</c>

00:04:37.950 --> 00:04:37.960 align:start position:0%
country and run python fc. piy Portugal
 

00:04:37.960 --> 00:04:40.390 align:start position:0%
country and run python fc. piy Portugal
and<00:04:38.080><c> we</c><00:04:38.199><c> get</c><00:04:38.320><c> some</c><00:04:38.880><c> coordinates</c><00:04:39.880><c> but</c><00:04:40.039><c> the</c>

00:04:40.390 --> 00:04:40.400 align:start position:0%
and we get some coordinates but the
 

00:04:40.400 --> 00:04:43.189 align:start position:0%
and we get some coordinates but the
current<00:04:40.840><c> format</c><00:04:41.280><c> of</c><00:04:41.400><c> the</c><00:04:41.600><c> response</c><00:04:42.199><c> changes</c>

00:04:43.189 --> 00:04:43.199 align:start position:0%
current format of the response changes
 

00:04:43.199 --> 00:04:45.350 align:start position:0%
current format of the response changes
as<00:04:43.400><c> we</c><00:04:43.560><c> use</c><00:04:43.759><c> it</c><00:04:44.520><c> plus</c><00:04:44.759><c> having</c><00:04:44.919><c> to</c><00:04:45.080><c> parse</c>

00:04:45.350 --> 00:04:45.360 align:start position:0%
as we use it plus having to parse
 

00:04:45.360 --> 00:04:46.909 align:start position:0%
as we use it plus having to parse
through<00:04:45.520><c> the</c><00:04:45.720><c> string</c><00:04:46.360><c> looking</c><00:04:46.639><c> for</c>

00:04:46.909 --> 00:04:46.919 align:start position:0%
through the string looking for
 

00:04:46.919 --> 00:04:48.390 align:start position:0%
through the string looking for
coordinates<00:04:47.720><c> well</c><00:04:47.880><c> that's</c><00:04:48.000><c> going</c><00:04:48.120><c> to</c><00:04:48.199><c> get</c>

00:04:48.390 --> 00:04:48.400 align:start position:0%
coordinates well that's going to get
 

00:04:48.400 --> 00:04:51.230 align:start position:0%
coordinates well that's going to get
annoying<00:04:49.400><c> the</c><00:04:49.639><c> first</c><00:04:49.919><c> thing</c><00:04:50.080><c> we</c><00:04:50.199><c> can</c><00:04:50.360><c> do</c><00:04:50.960><c> is</c>

00:04:51.230 --> 00:04:51.240 align:start position:0%
annoying the first thing we can do is
 

00:04:51.240 --> 00:04:53.950 align:start position:0%
annoying the first thing we can do is
ask<00:04:51.440><c> the</c><00:04:51.560><c> model</c><00:04:51.840><c> to</c><00:04:52.080><c> respond</c><00:04:52.479><c> as</c><00:04:52.680><c> Json</c><00:04:53.680><c> let's</c>

00:04:53.950 --> 00:04:53.960 align:start position:0%
ask the model to respond as Json let's
 

00:04:53.960 --> 00:04:56.310 align:start position:0%
ask the model to respond as Json let's
add<00:04:54.120><c> it</c><00:04:54.320><c> as</c><00:04:54.440><c> a</c><00:04:54.560><c> new</c><00:04:54.800><c> system</c><00:04:55.120><c> prompt</c><00:04:55.800><c> we</c><00:04:55.919><c> can</c>

00:04:56.310 --> 00:04:56.320 align:start position:0%
add it as a new system prompt we can
 

00:04:56.320 --> 00:04:58.749 align:start position:0%
add it as a new system prompt we can
just<00:04:56.639><c> add</c><00:04:56.880><c> this</c><00:04:57.039><c> to</c><00:04:57.199><c> the</c><00:04:57.400><c> top</c><00:04:57.560><c> of</c><00:04:57.680><c> our</c><00:04:57.840><c> messages</c>

00:04:58.749 --> 00:04:58.759 align:start position:0%
just add this to the top of our messages
 

00:04:58.759 --> 00:05:01.150 align:start position:0%
just add this to the top of our messages
roll<00:04:59.160><c> is</c><00:04:59.320><c> system</c><00:04:59.560><c> system</c><00:04:59.800><c> and</c><00:05:00.039><c> content</c><00:05:00.479><c> is</c><00:05:01.039><c> you</c>

00:05:01.150 --> 00:05:01.160 align:start position:0%
roll is system system and content is you
 

00:05:01.160 --> 00:05:04.110 align:start position:0%
roll is system system and content is you
are<00:05:01.360><c> a</c><00:05:01.520><c> helpful</c><00:05:02.039><c> AI</c><00:05:02.479><c> assistant</c><00:05:03.400><c> the</c><00:05:03.560><c> user</c><00:05:03.919><c> will</c>

00:05:04.110 --> 00:05:04.120 align:start position:0%
are a helpful AI assistant the user will
 

00:05:04.120 --> 00:05:05.990 align:start position:0%
are a helpful AI assistant the user will
enter<00:05:04.360><c> a</c><00:05:04.560><c> country</c><00:05:04.880><c> name</c><00:05:05.360><c> and</c><00:05:05.479><c> the</c><00:05:05.639><c> assistant</c>

00:05:05.990 --> 00:05:06.000 align:start position:0%
enter a country name and the assistant
 

00:05:06.000 --> 00:05:08.110 align:start position:0%
enter a country name and the assistant
will<00:05:06.320><c> return</c><00:05:06.520><c> the</c><00:05:06.680><c> decimal</c><00:05:07.080><c> latitude</c><00:05:07.919><c> and</c>

00:05:08.110 --> 00:05:08.120 align:start position:0%
will return the decimal latitude and
 

00:05:08.120 --> 00:05:10.270 align:start position:0%
will return the decimal latitude and
decimal<00:05:08.520><c> longitude</c><00:05:09.120><c> of</c><00:05:09.280><c> the</c><00:05:09.479><c> capital</c><00:05:10.000><c> of</c><00:05:10.120><c> the</c>

00:05:10.270 --> 00:05:10.280 align:start position:0%
decimal longitude of the capital of the
 

00:05:10.280 --> 00:05:13.870 align:start position:0%
decimal longitude of the capital of the
country<00:05:11.160><c> output</c><00:05:11.680><c> in</c><00:05:12.120><c> Json</c><00:05:13.120><c> and</c><00:05:13.240><c> we</c><00:05:13.320><c> can</c><00:05:13.520><c> change</c>

00:05:13.870 --> 00:05:13.880 align:start position:0%
country output in Json and we can change
 

00:05:13.880 --> 00:05:17.070 align:start position:0%
country output in Json and we can change
the<00:05:14.039><c> user</c><00:05:14.440><c> content</c><00:05:14.800><c> to</c><00:05:15.000><c> just</c><00:05:15.680><c> country</c><00:05:16.680><c> so</c><00:05:17.000><c> this</c>

00:05:17.070 --> 00:05:17.080 align:start position:0%
the user content to just country so this
 

00:05:17.080 --> 00:05:19.189 align:start position:0%
the user content to just country so this
is<00:05:17.240><c> a</c><00:05:17.400><c> good</c><00:05:17.600><c> start</c><00:05:18.039><c> but</c><00:05:18.319><c> notice</c><00:05:18.720><c> that</c><00:05:18.880><c> we</c><00:05:19.039><c> have</c>

00:05:19.189 --> 00:05:19.199 align:start position:0%
is a good start but notice that we have
 

00:05:19.199 --> 00:05:22.350 align:start position:0%
is a good start but notice that we have
an<00:05:19.400><c> explanation</c><00:05:20.000><c> of</c><00:05:20.120><c> the</c><00:05:20.240><c> Json</c><00:05:21.039><c> format</c><00:05:22.039><c> and</c>

00:05:22.350 --> 00:05:22.360 align:start position:0%
an explanation of the Json format and
 

00:05:22.360 --> 00:05:24.670 align:start position:0%
an explanation of the Json format and
sometimes<00:05:22.520><c> the</c><00:05:22.639><c> key</c><00:05:22.840><c> names</c><00:05:23.039><c> are</c><00:05:23.440><c> different</c><00:05:24.440><c> so</c>

00:05:24.670 --> 00:05:24.680 align:start position:0%
sometimes the key names are different so
 

00:05:24.680 --> 00:05:27.590 align:start position:0%
sometimes the key names are different so
add<00:05:24.880><c> format</c><00:05:25.319><c> Json</c><00:05:25.759><c> to</c><00:05:25.919><c> the</c><00:05:26.400><c> payload</c><00:05:27.400><c> and</c>

00:05:27.590 --> 00:05:27.600 align:start position:0%
add format Json to the payload and
 

00:05:27.600 --> 00:05:30.230 align:start position:0%
add format Json to the payload and
things<00:05:27.759><c> are</c><00:05:28.160><c> a</c><00:05:28.400><c> lot</c><00:05:28.720><c> better</c><00:05:29.639><c> but</c><00:05:29.759><c> if</c><00:05:29.880><c> you</c><00:05:30.000><c> try</c>

00:05:30.230 --> 00:05:30.240 align:start position:0%
things are a lot better but if you try
 

00:05:30.240 --> 00:05:32.110 align:start position:0%
things are a lot better but if you try
it<00:05:30.400><c> enough</c><00:05:30.759><c> you</c><00:05:30.919><c> may</c><00:05:31.199><c> still</c><00:05:31.440><c> see</c><00:05:31.720><c> that</c><00:05:31.919><c> key</c>

00:05:32.110 --> 00:05:32.120 align:start position:0%
it enough you may still see that key
 

00:05:32.120 --> 00:05:34.550 align:start position:0%
it enough you may still see that key
names<00:05:32.479><c> change</c><00:05:33.479><c> so</c><00:05:33.639><c> we</c><00:05:33.759><c> need</c><00:05:33.919><c> to</c><00:05:34.080><c> provide</c><00:05:34.319><c> a</c>

00:05:34.550 --> 00:05:34.560 align:start position:0%
names change so we need to provide a
 

00:05:34.560 --> 00:05:36.990 align:start position:0%
names change so we need to provide a
schema<00:05:34.960><c> to</c><00:05:35.120><c> the</c><00:05:35.280><c> model</c><00:05:36.280><c> this</c><00:05:36.400><c> is</c><00:05:36.560><c> the</c><00:05:36.720><c> purpose</c>

00:05:36.990 --> 00:05:37.000 align:start position:0%
schema to the model this is the purpose
 

00:05:37.000 --> 00:05:39.390 align:start position:0%
schema to the model this is the purpose
of<00:05:37.120><c> the</c><00:05:37.240><c> function</c><00:05:37.600><c> Block</c><00:05:38.160><c> in</c><00:05:38.280><c> the</c><00:05:38.440><c> open</c><00:05:38.720><c> aai</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
of the function Block in the open aai
 

00:05:39.400 --> 00:05:42.950 align:start position:0%
of the function Block in the open aai
API<00:05:40.360><c> for</c><00:05:40.520><c> the</c><00:05:40.680><c> schema</c><00:05:41.240><c> I'm</c><00:05:41.400><c> giving</c><00:05:41.639><c> a</c><00:05:41.880><c> key</c><00:05:42.680><c> then</c>

00:05:42.950 --> 00:05:42.960 align:start position:0%
API for the schema I'm giving a key then
 

00:05:42.960 --> 00:05:45.510 align:start position:0%
API for the schema I'm giving a key then
type<00:05:43.479><c> and</c><00:05:43.840><c> description</c><00:05:44.840><c> since</c><00:05:45.039><c> I</c><00:05:45.120><c> need</c><00:05:45.319><c> the</c>

00:05:45.510 --> 00:05:45.520 align:start position:0%
type and description since I need the
 

00:05:45.520 --> 00:05:47.790 align:start position:0%
type and description since I need the
city<00:05:46.039><c> and</c><00:05:46.199><c> coordinates</c><00:05:46.800><c> I</c><00:05:46.960><c> specify</c><00:05:47.400><c> one</c><00:05:47.600><c> for</c>

00:05:47.790 --> 00:05:47.800 align:start position:0%
city and coordinates I specify one for
 

00:05:47.800 --> 00:05:50.430 align:start position:0%
city and coordinates I specify one for
each<00:05:48.680><c> and</c><00:05:48.840><c> I'm</c><00:05:48.960><c> using</c><00:05:49.280><c> the</c><00:05:49.440><c> words</c><00:05:49.759><c> lat</c><00:05:50.240><c> and</c>

00:05:50.430 --> 00:05:50.440 align:start position:0%
each and I'm using the words lat and
 

00:05:50.440 --> 00:05:53.230 align:start position:0%
each and I'm using the words lat and
Lawn<00:05:51.319><c> to</c><00:05:51.479><c> show</c><00:05:51.759><c> that</c><00:05:52.039><c> my</c><00:05:52.319><c> schema</c><00:05:52.720><c> is</c><00:05:52.840><c> being</c>

00:05:53.230 --> 00:05:53.240 align:start position:0%
Lawn to show that my schema is being
 

00:05:53.240 --> 00:05:55.350 align:start position:0%
Lawn to show that my schema is being
respected<00:05:54.240><c> if</c><00:05:54.319><c> you</c><00:05:54.440><c> run</c><00:05:54.600><c> the</c><00:05:54.759><c> program</c><00:05:55.039><c> a</c><00:05:55.160><c> few</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
respected if you run the program a few
 

00:05:55.360 --> 00:05:56.990 align:start position:0%
respected if you run the program a few
times<00:05:55.639><c> you'll</c><00:05:55.880><c> notice</c><00:05:56.160><c> that</c><00:05:56.319><c> it's</c><00:05:56.600><c> not</c><00:05:56.800><c> the</c>

00:05:56.990 --> 00:05:57.000 align:start position:0%
times you'll notice that it's not the
 

00:05:57.000 --> 00:05:59.469 align:start position:0%
times you'll notice that it's not the
most<00:05:57.400><c> precise</c><00:05:57.840><c> way</c><00:05:57.960><c> of</c><00:05:58.120><c> getting</c><00:05:58.400><c> the</c><00:05:58.560><c> latitude</c>

00:05:59.469 --> 00:05:59.479 align:start position:0%
most precise way of getting the latitude
 

00:05:59.479 --> 00:06:00.150 align:start position:0%
most precise way of getting the latitude
and

00:06:00.150 --> 00:06:00.160 align:start position:0%
and
 

00:06:00.160 --> 00:06:03.629 align:start position:0%
and
longitude<00:06:01.160><c> but</c><00:06:01.360><c> it's</c><00:06:01.720><c> a</c><00:06:02.039><c> demo</c><00:06:03.039><c> now</c><00:06:03.360><c> if</c><00:06:03.479><c> the</c>

00:06:03.629 --> 00:06:03.639 align:start position:0%
longitude but it's a demo now if the
 

00:06:03.639 --> 00:06:05.870 align:start position:0%
longitude but it's a demo now if the
model<00:06:04.319><c> sometimes</c><00:06:04.639><c> responded</c><00:06:05.280><c> with</c><00:06:05.560><c> something</c>

00:06:05.870 --> 00:06:05.880 align:start position:0%
model sometimes responded with something
 

00:06:05.880 --> 00:06:07.950 align:start position:0%
model sometimes responded with something
that<00:06:06.080><c> wasn't</c><00:06:06.360><c> in</c><00:06:06.520><c> the</c><00:06:06.639><c> schema</c><00:06:07.440><c> I</c><00:06:07.560><c> would</c><00:06:07.720><c> do</c><00:06:07.840><c> a</c>

00:06:07.950 --> 00:06:07.960 align:start position:0%
that wasn't in the schema I would do a
 

00:06:07.960 --> 00:06:11.110 align:start position:0%
that wasn't in the schema I would do a
few<00:06:08.280><c> shot</c><00:06:08.520><c> prompt</c><00:06:09.520><c> let</c><00:06:09.639><c> me</c><00:06:09.880><c> show</c><00:06:10.360><c> that</c><00:06:10.759><c> just</c><00:06:10.960><c> so</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
few shot prompt let me show that just so
 

00:06:11.120 --> 00:06:13.589 align:start position:0%
few shot prompt let me show that just so
you<00:06:11.240><c> can</c><00:06:11.360><c> see</c><00:06:11.560><c> an</c><00:06:11.720><c> example</c><00:06:12.720><c> there's</c><00:06:12.919><c> a</c><00:06:13.080><c> user</c>

00:06:13.589 --> 00:06:13.599 align:start position:0%
you can see an example there's a user
 

00:06:13.599 --> 00:06:16.350 align:start position:0%
you can see an example there's a user
and<00:06:13.800><c> I</c><00:06:13.960><c> also</c><00:06:14.199><c> give</c><00:06:14.440><c> the</c><00:06:14.639><c> assistant</c><00:06:15.360><c> response</c>

00:06:16.350 --> 00:06:16.360 align:start position:0%
and I also give the assistant response
 

00:06:16.360 --> 00:06:17.830 align:start position:0%
and I also give the assistant response
that<00:06:16.919><c> I</c>

00:06:17.830 --> 00:06:17.840 align:start position:0%
that I
 

00:06:17.840 --> 00:06:20.670 align:start position:0%
that I
specify<00:06:18.840><c> for</c><00:06:19.000><c> a</c><00:06:19.160><c> Json</c><00:06:19.639><c> response</c><00:06:20.319><c> since</c><00:06:20.560><c> we</c>

00:06:20.670 --> 00:06:20.680 align:start position:0%
specify for a Json response since we
 

00:06:20.680 --> 00:06:23.070 align:start position:0%
specify for a Json response since we
need<00:06:20.840><c> to</c><00:06:21.039><c> get</c><00:06:21.319><c> content</c><00:06:21.960><c> out</c><00:06:22.120><c> of</c><00:06:22.280><c> it</c><00:06:22.840><c> and</c><00:06:22.960><c> we</c>

00:06:23.070 --> 00:06:23.080 align:start position:0%
need to get content out of it and we
 

00:06:23.080 --> 00:06:25.469 align:start position:0%
need to get content out of it and we
don't<00:06:23.240><c> do</c><00:06:23.599><c> anything</c><00:06:24.120><c> until</c><00:06:24.479><c> it's</c><00:06:24.720><c> complete</c><00:06:25.400><c> we</c>

00:06:25.469 --> 00:06:25.479 align:start position:0%
don't do anything until it's complete we
 

00:06:25.479 --> 00:06:27.469 align:start position:0%
don't do anything until it's complete we
should<00:06:25.720><c> disable</c><00:06:26.160><c> streaming</c><00:06:26.919><c> then</c><00:06:27.039><c> we</c><00:06:27.160><c> can</c><00:06:27.240><c> set</c>

00:06:27.469 --> 00:06:27.479 align:start position:0%
should disable streaming then we can set
 

00:06:27.479 --> 00:06:29.430 align:start position:0%
should disable streaming then we can set
City<00:06:27.800><c> info</c><00:06:28.120><c> to</c><00:06:28.240><c> the</c><00:06:28.360><c> Json</c><00:06:28.759><c> string</c><00:06:29.039><c> in</c><00:06:29.160><c> theage</c>

00:06:29.430 --> 00:06:29.440 align:start position:0%
City info to the Json string in theage
 

00:06:29.440 --> 00:06:31.230 align:start position:0%
City info to the Json string in theage
message<00:06:29.720><c> content</c><00:06:30.520><c> and</c><00:06:30.639><c> now</c><00:06:30.800><c> we</c><00:06:30.880><c> can</c><00:06:31.000><c> call</c>

00:06:31.230 --> 00:06:31.240 align:start position:0%
message content and now we can call
 

00:06:31.240 --> 00:06:33.550 align:start position:0%
message content and now we can call
havene<00:06:31.840><c> with</c><00:06:32.000><c> my</c><00:06:32.199><c> latitude</c><00:06:32.599><c> and</c><00:06:32.720><c> longitude</c>

00:06:33.550 --> 00:06:33.560 align:start position:0%
havene with my latitude and longitude
 

00:06:33.560 --> 00:06:35.189 align:start position:0%
havene with my latitude and longitude
and<00:06:33.680><c> the</c><00:06:33.759><c> coordinates</c><00:06:34.160><c> of</c><00:06:34.319><c> the</c><00:06:34.440><c> capital</c><00:06:34.759><c> city</c>

00:06:35.189 --> 00:06:35.199 align:start position:0%
and the coordinates of the capital city
 

00:06:35.199 --> 00:06:37.469 align:start position:0%
and the coordinates of the capital city
and<00:06:35.360><c> get</c><00:06:35.479><c> a</c><00:06:35.680><c> distance</c><00:06:36.599><c> now</c><00:06:36.720><c> if</c><00:06:36.800><c> you</c><00:06:36.960><c> run</c><00:06:37.160><c> this</c><00:06:37.280><c> a</c>

00:06:37.469 --> 00:06:37.479 align:start position:0%
and get a distance now if you run this a
 

00:06:37.479 --> 00:06:39.189 align:start position:0%
and get a distance now if you run this a
few<00:06:37.720><c> times</c><00:06:37.960><c> you</c><00:06:38.080><c> may</c><00:06:38.280><c> notice</c><00:06:38.639><c> that</c><00:06:38.800><c> the</c>

00:06:39.189 --> 00:06:39.199 align:start position:0%
few times you may notice that the
 

00:06:39.199 --> 00:06:42.670 align:start position:0%
few times you may notice that the
distance<00:06:40.199><c> isn't</c><00:06:40.599><c> always</c><00:06:41.360><c> consistent</c><00:06:42.360><c> asking</c>

00:06:42.670 --> 00:06:42.680 align:start position:0%
distance isn't always consistent asking
 

00:06:42.680 --> 00:06:44.909 align:start position:0%
distance isn't always consistent asking
a<00:06:42.840><c> model</c><00:06:43.120><c> for</c><00:06:43.319><c> latitude</c><00:06:43.800><c> and</c><00:06:43.960><c> longitude</c><00:06:44.639><c> isn't</c>

00:06:44.909 --> 00:06:44.919 align:start position:0%
a model for latitude and longitude isn't
 

00:06:44.919 --> 00:06:47.909 align:start position:0%
a model for latitude and longitude isn't
a<00:06:45.160><c> great</c><00:06:45.479><c> use</c><00:06:45.880><c> of</c><00:06:46.039><c> the</c><00:06:46.319><c> system</c><00:06:47.319><c> we</c><00:06:47.440><c> can</c><00:06:47.560><c> set</c><00:06:47.759><c> the</c>

00:06:47.909 --> 00:06:47.919 align:start position:0%
a great use of the system we can set the
 

00:06:47.919 --> 00:06:49.710 align:start position:0%
a great use of the system we can set the
temperature<00:06:48.319><c> to</c><00:06:48.520><c> zero</c><00:06:48.840><c> to</c><00:06:49.000><c> try</c><00:06:49.160><c> to</c><00:06:49.319><c> get</c><00:06:49.440><c> it</c><00:06:49.560><c> to</c>

00:06:49.710 --> 00:06:49.720 align:start position:0%
temperature to zero to try to get it to
 

00:06:49.720 --> 00:06:50.990 align:start position:0%
temperature to zero to try to get it to
be<00:06:49.919><c> more</c>

00:06:50.990 --> 00:06:51.000 align:start position:0%
be more
 

00:06:51.000 --> 00:06:53.909 align:start position:0%
be more
consistent<00:06:52.000><c> but</c><00:06:52.160><c> it</c><00:06:52.720><c> may</c><00:06:52.960><c> still</c><00:06:53.240><c> be</c><00:06:53.599><c> different</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
consistent but it may still be different
 

00:06:53.919 --> 00:06:56.390 align:start position:0%
consistent but it may still be different
from<00:06:54.120><c> what</c><00:06:54.240><c> you</c><00:06:54.400><c> get</c><00:06:54.599><c> with</c><00:06:54.720><c> a</c><00:06:54.840><c> Google</c><00:06:55.400><c> Search</c>

00:06:56.390 --> 00:06:56.400 align:start position:0%
from what you get with a Google Search
 

00:06:56.400 --> 00:06:58.670 align:start position:0%
from what you get with a Google Search
and<00:06:56.720><c> that</c><00:06:57.000><c> depends</c><00:06:57.319><c> on</c><00:06:57.599><c> where</c><00:06:57.960><c> exactly</c><00:06:58.400><c> both</c>

00:06:58.670 --> 00:06:58.680 align:start position:0%
and that depends on where exactly both
 

00:06:58.680 --> 00:07:01.309 align:start position:0%
and that depends on where exactly both
systems<00:06:59.039><c> put</c><00:06:59.199><c> the</c><00:06:59.560><c> dot</c><00:07:00.319><c> that</c><00:07:00.520><c> says</c><00:07:01.039><c> this</c><00:07:01.160><c> is</c>

00:07:01.309 --> 00:07:01.319 align:start position:0%
systems put the dot that says this is
 

00:07:01.319 --> 00:07:03.909 align:start position:0%
systems put the dot that says this is
the<00:07:01.560><c> spot</c><00:07:02.199><c> that</c><00:07:02.360><c> represents</c><00:07:02.840><c> the</c><00:07:03.039><c> capital</c><00:07:03.720><c> of</c>

00:07:03.909 --> 00:07:03.919 align:start position:0%
the spot that represents the capital of
 

00:07:03.919 --> 00:07:06.790 align:start position:0%
the spot that represents the capital of
whichever<00:07:04.720><c> country</c><00:07:05.720><c> hopefully</c><00:07:06.080><c> you</c><00:07:06.319><c> now</c><00:07:06.520><c> see</c>

00:07:06.790 --> 00:07:06.800 align:start position:0%
whichever country hopefully you now see
 

00:07:06.800 --> 00:07:08.749 align:start position:0%
whichever country hopefully you now see
how<00:07:06.919><c> you</c><00:07:07.000><c> can</c><00:07:07.120><c> use</c><00:07:07.440><c> format</c><00:07:07.840><c> Json</c><00:07:08.280><c> to</c><00:07:08.440><c> get</c><00:07:08.599><c> a</c>

00:07:08.749 --> 00:07:08.759 align:start position:0%
how you can use format Json to get a
 

00:07:08.759 --> 00:07:11.230 align:start position:0%
how you can use format Json to get a
consistent<00:07:09.280><c> output</c><00:07:09.639><c> from</c><00:07:09.800><c> the</c><00:07:09.919><c> model</c><00:07:10.919><c> because</c>

00:07:11.230 --> 00:07:11.240 align:start position:0%
consistent output from the model because
 

00:07:11.240 --> 00:07:13.230 align:start position:0%
consistent output from the model because
that's<00:07:11.639><c> all</c><00:07:11.960><c> the</c><00:07:12.120><c> terribly</c><00:07:12.520><c> named</c><00:07:12.800><c> feature</c><00:07:13.080><c> of</c>

00:07:13.230 --> 00:07:13.240 align:start position:0%
that's all the terribly named feature of
 

00:07:13.240 --> 00:07:15.670 align:start position:0%
that's all the terribly named feature of
function<00:07:13.560><c> calling</c><00:07:14.000><c> does</c><00:07:15.000><c> it</c><00:07:15.120><c> gives</c><00:07:15.360><c> you</c><00:07:15.520><c> the</c>

00:07:15.670 --> 00:07:15.680 align:start position:0%
function calling does it gives you the
 

00:07:15.680 --> 00:07:17.909 align:start position:0%
function calling does it gives you the
consistent<00:07:16.240><c> output</c><00:07:16.759><c> from</c><00:07:16.960><c> the</c><00:07:17.120><c> model</c><00:07:17.479><c> so</c><00:07:17.639><c> that</c>

00:07:17.909 --> 00:07:17.919 align:start position:0%
consistent output from the model so that
 

00:07:17.919 --> 00:07:20.350 align:start position:0%
consistent output from the model so that
you<00:07:18.199><c> can</c><00:07:18.400><c> call</c><00:07:18.680><c> a</c><00:07:18.840><c> function</c><00:07:19.199><c> with</c><00:07:19.319><c> the</c><00:07:19.479><c> data</c>

00:07:20.350 --> 00:07:20.360 align:start position:0%
you can call a function with the data
 

00:07:20.360 --> 00:07:23.029 align:start position:0%
you can call a function with the data
which<00:07:20.680><c> is</c><00:07:20.960><c> a</c><00:07:21.400><c> very</c><00:07:21.800><c> cool</c><00:07:22.120><c> thing</c><00:07:22.280><c> to</c><00:07:22.440><c> be</c><00:07:22.560><c> able</c><00:07:22.720><c> to</c>

00:07:23.029 --> 00:07:23.039 align:start position:0%
which is a very cool thing to be able to
 

00:07:23.039 --> 00:07:25.909 align:start position:0%
which is a very cool thing to be able to
do<00:07:24.039><c> function</c><00:07:24.400><c> calling</c><00:07:24.759><c> using</c><00:07:25.039><c> the</c><00:07:25.199><c> new</c><00:07:25.599><c> open</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
do function calling using the new open
 

00:07:25.919 --> 00:07:30.029 align:start position:0%
do function calling using the new open
aai<00:07:26.599><c> compatible</c><00:07:27.360><c> API</c><00:07:28.319><c> isn't</c><00:07:28.879><c> available</c><00:07:29.680><c> yet</c>

00:07:30.029 --> 00:07:30.039 align:start position:0%
aai compatible API isn't available yet
 

00:07:30.039 --> 00:07:32.710 align:start position:0%
aai compatible API isn't available yet
inama<00:07:31.039><c> at</c><00:07:31.160><c> the</c><00:07:31.319><c> time</c><00:07:31.520><c> of</c><00:07:31.680><c> this</c><00:07:31.879><c> recording</c><00:07:32.319><c> so</c>

00:07:32.710 --> 00:07:32.720 align:start position:0%
inama at the time of this recording so
 

00:07:32.720 --> 00:07:35.029 align:start position:0%
inama at the time of this recording so
you<00:07:32.840><c> need</c><00:07:33.000><c> to</c><00:07:33.160><c> use</c><00:07:33.360><c> the</c><00:07:33.520><c> native</c><00:07:33.879><c> olama</c><00:07:34.400><c> API</c><00:07:34.879><c> for</c>

00:07:35.029 --> 00:07:35.039 align:start position:0%
you need to use the native olama API for
 

00:07:35.039 --> 00:07:37.710 align:start position:0%
you need to use the native olama API for
this<00:07:35.759><c> now</c><00:07:35.919><c> I</c><00:07:36.039><c> don't</c><00:07:36.280><c> think</c><00:07:36.680><c> anyone</c><00:07:37.400><c> is</c><00:07:37.520><c> going</c>

00:07:37.710 --> 00:07:37.720 align:start position:0%
this now I don't think anyone is going
 

00:07:37.720 --> 00:07:39.830 align:start position:0%
this now I don't think anyone is going
to<00:07:37.840><c> want</c><00:07:38.000><c> to</c><00:07:38.120><c> do</c><00:07:38.280><c> it</c><00:07:38.440><c> the</c><00:07:38.560><c> open</c><00:07:38.800><c> AI</c><00:07:39.199><c> way</c><00:07:39.599><c> because</c>

00:07:39.830 --> 00:07:39.840 align:start position:0%
to want to do it the open AI way because
 

00:07:39.840 --> 00:07:41.869 align:start position:0%
to want to do it the open AI way because
it's<00:07:40.000><c> just</c><00:07:40.120><c> a</c><00:07:40.240><c> lot</c><00:07:40.440><c> more</c><00:07:40.800><c> complicated</c><00:07:41.720><c> and</c>

00:07:41.869 --> 00:07:41.879 align:start position:0%
it's just a lot more complicated and
 

00:07:41.879 --> 00:07:44.510 align:start position:0%
it's just a lot more complicated and
doesn't<00:07:42.360><c> offer</c><00:07:42.720><c> any</c><00:07:43.000><c> benefit</c><00:07:43.960><c> but</c><00:07:44.120><c> it</c><00:07:44.319><c> has</c>

00:07:44.510 --> 00:07:44.520 align:start position:0%
doesn't offer any benefit but it has
 

00:07:44.520 --> 00:07:47.149 align:start position:0%
doesn't offer any benefit but it has
open<00:07:44.840><c> AI</c><00:07:45.120><c> in</c><00:07:45.240><c> the</c><00:07:45.360><c> name</c><00:07:46.080><c> so</c><00:07:46.360><c> maybe</c><00:07:46.680><c> that's</c><00:07:46.960><c> good</c>

00:07:47.149 --> 00:07:47.159 align:start position:0%
open AI in the name so maybe that's good
 

00:07:47.159 --> 00:07:50.070 align:start position:0%
open AI in the name so maybe that's good
enough<00:07:47.599><c> for</c><00:07:47.919><c> some</c><00:07:48.599><c> to</c><00:07:48.840><c> justify</c><00:07:49.400><c> the</c><00:07:49.680><c> suffering</c>

00:07:50.070 --> 00:07:50.080 align:start position:0%
enough for some to justify the suffering
 

00:07:50.080 --> 00:07:52.469 align:start position:0%
enough for some to justify the suffering
through<00:07:50.280><c> the</c><00:07:50.479><c> pain</c><00:07:51.479><c> what</c><00:07:51.560><c> do</c><00:07:51.680><c> you</c><00:07:51.879><c> think</c><00:07:52.280><c> is</c>

00:07:52.469 --> 00:07:52.479 align:start position:0%
through the pain what do you think is
 

00:07:52.479 --> 00:07:55.469 align:start position:0%
through the pain what do you think is
function<00:07:52.840><c> calling</c><00:07:53.479><c> I</c><00:07:53.560><c> mean</c><00:07:54.080><c> format</c><00:07:54.680><c> Json</c><00:07:55.280><c> an</c>

00:07:55.469 --> 00:07:55.479 align:start position:0%
function calling I mean format Json an
 

00:07:55.479 --> 00:07:57.950 align:start position:0%
function calling I mean format Json an
important<00:07:55.879><c> feature</c><00:07:56.280><c> for</c><00:07:56.520><c> you</c><00:07:57.400><c> are</c><00:07:57.560><c> you</c><00:07:57.720><c> using</c>

00:07:57.950 --> 00:07:57.960 align:start position:0%
important feature for you are you using
 

00:07:57.960 --> 00:08:00.149 align:start position:0%
important feature for you are you using
it<00:07:58.159><c> today</c><00:07:59.080><c> let</c><00:07:59.199><c> me</c><00:07:59.319><c> let</c><00:07:59.400><c> me</c><00:07:59.520><c> know</c><00:07:59.720><c> down</c><00:07:59.879><c> in</c><00:08:00.000><c> the</c>

00:08:00.149 --> 00:08:00.159 align:start position:0%
it today let me let me know down in the
 

00:08:00.159 --> 00:08:02.790 align:start position:0%
it today let me let me know down in the
comments<00:08:00.560><c> below</c><00:08:01.560><c> and</c><00:08:01.680><c> if</c><00:08:01.800><c> there's</c><00:08:02.360><c> another</c>

00:08:02.790 --> 00:08:02.800 align:start position:0%
comments below and if there's another
 

00:08:02.800 --> 00:08:04.869 align:start position:0%
comments below and if there's another
feature<00:08:03.159><c> you'd</c><00:08:03.360><c> like</c><00:08:03.520><c> to</c><00:08:03.759><c> see</c><00:08:04.440><c> let</c><00:08:04.560><c> me</c><00:08:04.720><c> know</c>

00:08:04.869 --> 00:08:04.879 align:start position:0%
feature you'd like to see let me know
 

00:08:04.879 --> 00:08:07.070 align:start position:0%
feature you'd like to see let me know
about<00:08:05.120><c> that</c><00:08:05.240><c> in</c><00:08:05.360><c> the</c><00:08:05.800><c> comments</c><00:08:06.800><c> when</c><00:08:06.919><c> I</c>

00:08:07.070 --> 00:08:07.080 align:start position:0%
about that in the comments when I
 

00:08:07.080 --> 00:08:09.390 align:start position:0%
about that in the comments when I
started<00:08:07.520><c> this</c><00:08:07.840><c> latest</c><00:08:08.240><c> focus</c><00:08:08.599><c> on</c><00:08:08.720><c> the</c><00:08:08.840><c> channel</c>

00:08:09.390 --> 00:08:09.400 align:start position:0%
started this latest focus on the channel
 

00:08:09.400 --> 00:08:12.149 align:start position:0%
started this latest focus on the channel
about<00:08:09.680><c> a</c><00:08:09.840><c> month</c><00:08:10.120><c> ago</c><00:08:10.840><c> I</c><00:08:10.919><c> had</c><00:08:11.039><c> a</c><00:08:11.199><c> simple</c><00:08:11.560><c> list</c><00:08:11.919><c> of</c>

00:08:12.149 --> 00:08:12.159 align:start position:0%
about a month ago I had a simple list of
 

00:08:12.159 --> 00:08:14.950 align:start position:0%
about a month ago I had a simple list of
about<00:08:12.440><c> 50</c><00:08:12.879><c> ideas</c><00:08:13.240><c> for</c><00:08:13.400><c> future</c><00:08:13.840><c> videos</c><00:08:14.840><c> but</c>

00:08:14.950 --> 00:08:14.960 align:start position:0%
about 50 ideas for future videos but
 

00:08:14.960 --> 00:08:16.869 align:start position:0%
about 50 ideas for future videos but
your<00:08:15.319><c> comments</c><00:08:15.919><c> over</c><00:08:16.080><c> the</c><00:08:16.240><c> last</c><00:08:16.440><c> few</c><00:08:16.599><c> weeks</c>

00:08:16.869 --> 00:08:16.879 align:start position:0%
your comments over the last few weeks
 

00:08:16.879 --> 00:08:20.469 align:start position:0%
your comments over the last few weeks
have<00:08:17.080><c> upped</c><00:08:17.400><c> that</c><00:08:17.560><c> to</c><00:08:18.400><c> well</c><00:08:18.639><c> over</c><00:08:19.280><c> 150</c><00:08:20.280><c> keep</c>

00:08:20.469 --> 00:08:20.479 align:start position:0%
have upped that to well over 150 keep
 

00:08:20.479 --> 00:08:23.070 align:start position:0%
have upped that to well over 150 keep
them<00:08:20.759><c> coming</c><00:08:21.759><c> because</c><00:08:22.240><c> there</c><00:08:22.360><c> are</c><00:08:22.599><c> so</c><00:08:22.800><c> many</c>

00:08:23.070 --> 00:08:23.080 align:start position:0%
them coming because there are so many
 

00:08:23.080 --> 00:08:26.469 align:start position:0%
them coming because there are so many
good<00:08:23.280><c> ones</c><00:08:23.879><c> that</c><00:08:24.199><c> I</c><00:08:24.479><c> never</c><00:08:24.840><c> thought</c><00:08:25.199><c> of</c><00:08:26.199><c> thanks</c>

00:08:26.469 --> 00:08:26.479 align:start position:0%
good ones that I never thought of thanks
 

00:08:26.479 --> 00:08:47.910 align:start position:0%
good ones that I never thought of thanks
so<00:08:26.680><c> much</c><00:08:26.840><c> for</c><00:08:27.319><c> watching</c><00:08:28.319><c> goodbye</c>

00:08:47.910 --> 00:08:47.920 align:start position:0%
 
 

00:08:47.920 --> 00:08:50.920 align:start position:0%
 
yeah

