WEBVTT
Kind: captions
Language: en

00:00:00.290 --> 00:00:03.560 align:start position:0%
 
hi<00:00:01.290><c> Matt</c><00:00:01.979><c> <PERSON></c><00:00:02.340><c> techno</c><00:00:02.850><c> evangelist</c><00:00:03.330><c> here</c>

00:00:03.560 --> 00:00:03.570 align:start position:0%
hi <PERSON> techno evangelist here
 

00:00:03.570 --> 00:00:06.349 align:start position:0%
hi <PERSON> techno evangelist here
last<00:00:04.230><c> night</c><00:00:04.529><c> a</c><00:00:04.710><c> question</c><00:00:05.160><c> came</c><00:00:05.190><c> in</c><00:00:05.670><c> about</c><00:00:06.060><c> my</c>

00:00:06.349 --> 00:00:06.359 align:start position:0%
last night a question came in about my
 

00:00:06.359 --> 00:00:09.470 align:start position:0%
last night a question came in about my
file<00:00:06.870><c> hub</c><00:00:07.140><c> video</c><00:00:07.470><c> YouTube</c><00:00:08.309><c> user</c><00:00:08.550><c> why</c><00:00:08.970><c> lamort</c>

00:00:09.470 --> 00:00:09.480 align:start position:0%
file hub video YouTube user why lamort
 

00:00:09.480 --> 00:00:11.570 align:start position:0%
file hub video YouTube user why lamort
asked<00:00:09.900><c> if</c><00:00:09.990><c> it</c><00:00:10.110><c> was</c><00:00:10.170><c> possible</c><00:00:10.679><c> to</c><00:00:10.740><c> open</c><00:00:10.950><c> a</c><00:00:11.309><c> raw</c>

00:00:11.570 --> 00:00:11.580 align:start position:0%
asked if it was possible to open a raw
 

00:00:11.580 --> 00:00:14.419 align:start position:0%
asked if it was possible to open a raw
file<00:00:11.940><c> from</c><00:00:12.210><c> your</c><00:00:12.269><c> SD</c><00:00:12.719><c> card</c><00:00:12.990><c> into</c><00:00:13.590><c> Lightroom</c>

00:00:14.419 --> 00:00:14.429 align:start position:0%
file from your SD card into Lightroom
 

00:00:14.429 --> 00:00:17.689 align:start position:0%
file from your SD card into Lightroom
using<00:00:15.420><c> the</c><00:00:15.540><c> file</c><00:00:15.690><c> hub</c><00:00:15.900><c> and</c><00:00:16.160><c> closer</c><00:00:17.160><c> to</c><00:00:17.400><c> a</c><00:00:17.430><c> week</c>

00:00:17.689 --> 00:00:17.699 align:start position:0%
using the file hub and closer to a week
 

00:00:17.699 --> 00:00:20.570 align:start position:0%
using the file hub and closer to a week
ago<00:00:17.970><c> martin</c><00:00:18.539><c> silva</c><00:00:19.130><c> mentioned</c><00:00:20.130><c> that</c><00:00:20.340><c> an</c>

00:00:20.570 --> 00:00:20.580 align:start position:0%
ago martin silva mentioned that an
 

00:00:20.580 --> 00:00:22.990 align:start position:0%
ago martin silva mentioned that an
amazon<00:00:21.330><c> review</c><00:00:21.779><c> said</c><00:00:22.170><c> that</c><00:00:22.199><c> raw</c><00:00:22.590><c> files</c>

00:00:22.990 --> 00:00:23.000 align:start position:0%
amazon review said that raw files
 

00:00:23.000 --> 00:00:25.970 align:start position:0%
amazon review said that raw files
weren't<00:00:24.000><c> even</c><00:00:24.210><c> supported</c><00:00:24.750><c> I</c><00:00:24.930><c> thought</c><00:00:25.740><c> I</c><00:00:25.769><c> knew</c>

00:00:25.970 --> 00:00:25.980 align:start position:0%
weren't even supported I thought I knew
 

00:00:25.980 --> 00:00:28.640 align:start position:0%
weren't even supported I thought I knew
the<00:00:26.130><c> answer</c><00:00:26.400><c> and</c><00:00:26.699><c> it</c><00:00:26.880><c> was</c><00:00:27.000><c> a</c><00:00:27.029><c> simple</c><00:00:27.420><c> no</c><00:00:27.599><c> but</c><00:00:28.349><c> I</c>

00:00:28.640 --> 00:00:28.650 align:start position:0%
the answer and it was a simple no but I
 

00:00:28.650 --> 00:00:30.800 align:start position:0%
the answer and it was a simple no but I
was<00:00:28.980><c> wrong</c><00:00:29.189><c> I</c><00:00:29.429><c> played</c><00:00:30.150><c> around</c><00:00:30.269><c> with</c><00:00:30.480><c> it</c><00:00:30.570><c> and</c>

00:00:30.800 --> 00:00:30.810 align:start position:0%
was wrong I played around with it and
 

00:00:30.810 --> 00:00:33.110 align:start position:0%
was wrong I played around with it and
sold<00:00:31.289><c> that</c><00:00:31.470><c> support</c><00:00:32.160><c> for</c><00:00:32.399><c> workflows</c><00:00:32.820><c> like</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
sold that support for workflows like
 

00:00:33.120 --> 00:00:36.560 align:start position:0%
sold that support for workflows like
this<00:00:33.390><c> are</c><00:00:34.040><c> completely</c><00:00:35.040><c> supported</c><00:00:35.610><c> and</c><00:00:35.790><c> it</c>

00:00:36.560 --> 00:00:36.570 align:start position:0%
this are completely supported and it
 

00:00:36.570 --> 00:00:39.830 align:start position:0%
this are completely supported and it
works<00:00:36.809><c> really</c><00:00:37.620><c> well</c><00:00:37.800><c> if</c><00:00:38.550><c> you</c><00:00:39.239><c> like</c><00:00:39.540><c> videos</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
works really well if you like videos
 

00:00:39.840 --> 00:00:41.000 align:start position:0%
works really well if you like videos
like<00:00:39.899><c> this</c><00:00:40.140><c> consider</c><00:00:40.649><c> hitting</c><00:00:40.860><c> that</c>

00:00:41.000 --> 00:00:41.010 align:start position:0%
like this consider hitting that
 

00:00:41.010 --> 00:00:44.030 align:start position:0%
like this consider hitting that
subscribe<00:00:41.309><c> button</c><00:00:42.090><c> below</c><00:00:42.600><c> and</c><00:00:43.170><c> if</c><00:00:43.710><c> you</c><00:00:43.829><c> have</c>

00:00:44.030 --> 00:00:44.040 align:start position:0%
subscribe button below and if you have
 

00:00:44.040 --> 00:00:46.520 align:start position:0%
subscribe button below and if you have
any<00:00:44.219><c> experience</c><00:00:44.879><c> with</c><00:00:45.090><c> the</c><00:00:45.480><c> device</c><00:00:45.750><c> share</c>

00:00:46.520 --> 00:00:46.530 align:start position:0%
any experience with the device share
 

00:00:46.530 --> 00:00:48.170 align:start position:0%
any experience with the device share
them<00:00:46.739><c> with</c><00:00:46.770><c> everyone</c><00:00:47.160><c> in</c><00:00:47.489><c> the</c><00:00:47.550><c> comment</c>

00:00:48.170 --> 00:00:48.180 align:start position:0%
them with everyone in the comment
 

00:00:48.180 --> 00:00:50.779 align:start position:0%
them with everyone in the comment
section<00:00:48.510><c> so</c><00:00:49.410><c> let's</c><00:00:49.649><c> get</c><00:00:49.890><c> to</c><00:00:50.070><c> the</c><00:00:50.190><c> iPad</c><00:00:50.579><c> and</c>

00:00:50.779 --> 00:00:50.789 align:start position:0%
section so let's get to the iPad and
 

00:00:50.789 --> 00:00:56.680 align:start position:0%
section so let's get to the iPad and
check<00:00:51.149><c> it</c><00:00:51.270><c> out</c><00:00:51.420><c> now</c><00:00:52.289><c> I</c><00:00:52.469><c> have</c><00:00:52.770><c> a</c><00:00:53.570><c> 2017</c><00:00:54.570><c> iPad</c><00:00:55.170><c> pro</c>

00:00:56.680 --> 00:00:56.690 align:start position:0%
check it out now I have a 2017 iPad pro
 

00:00:56.690 --> 00:01:01.340 align:start position:0%
check it out now I have a 2017 iPad pro
but<00:00:57.800><c> this</c><00:00:58.800><c> is</c><00:00:58.980><c> going</c><00:00:59.219><c> to</c><00:00:59.309><c> work</c><00:00:59.550><c> on</c><00:00:59.850><c> any</c><00:01:00.480><c> iOS</c>

00:01:01.340 --> 00:01:01.350 align:start position:0%
but this is going to work on any iOS
 

00:01:01.350 --> 00:01:04.189 align:start position:0%
but this is going to work on any iOS
device<00:01:02.000><c> I'm</c><00:01:03.000><c> going</c><00:01:03.059><c> to</c><00:01:03.510><c> assume</c><00:01:03.780><c> that</c><00:01:03.840><c> there's</c>

00:01:04.189 --> 00:01:04.199 align:start position:0%
device I'm going to assume that there's
 

00:01:04.199 --> 00:01:06.200 align:start position:0%
device I'm going to assume that there's
a<00:01:04.260><c> similar</c><00:01:04.619><c> workflow</c><00:01:04.830><c> for</c><00:01:05.460><c> Android</c><00:01:05.939><c> devices</c>

00:01:06.200 --> 00:01:06.210 align:start position:0%
a similar workflow for Android devices
 

00:01:06.210 --> 00:01:09.170 align:start position:0%
a similar workflow for Android devices
as<00:01:06.540><c> well</c><00:01:06.930><c> but</c><00:01:07.290><c> I</c><00:01:07.530><c> have</c><00:01:08.189><c> no</c><00:01:08.369><c> experience</c><00:01:08.640><c> there</c>

00:01:09.170 --> 00:01:09.180 align:start position:0%
as well but I have no experience there
 

00:01:09.180 --> 00:01:12.350 align:start position:0%
as well but I have no experience there
now<00:01:10.049><c> I've</c><00:01:10.229><c> connected</c><00:01:10.500><c> my</c><00:01:10.799><c> iPads</c><00:01:11.369><c> Wi-Fi</c><00:01:11.909><c> to</c><00:01:11.970><c> the</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
now I've connected my iPads Wi-Fi to the
 

00:01:12.360 --> 00:01:16.399 align:start position:0%
now I've connected my iPads Wi-Fi to the
ravpower<00:01:12.720><c> file</c><00:01:13.650><c> hub</c><00:01:13.950><c> and</c><00:01:14.189><c> plugged</c><00:01:14.939><c> in</c><00:01:15.270><c> an</c><00:01:15.600><c> SD</c>

00:01:16.399 --> 00:01:16.409 align:start position:0%
ravpower file hub and plugged in an SD
 

00:01:16.409 --> 00:01:19.460 align:start position:0%
ravpower file hub and plugged in an SD
card<00:01:16.710><c> to</c><00:01:17.340><c> the</c><00:01:17.460><c> file</c><00:01:17.610><c> hub</c><00:01:17.820><c> now</c><00:01:18.630><c> I'll</c><00:01:19.049><c> open</c><00:01:19.350><c> up</c>

00:01:19.460 --> 00:01:19.470 align:start position:0%
card to the file hub now I'll open up
 

00:01:19.470 --> 00:01:25.700 align:start position:0%
card to the file hub now I'll open up
the<00:01:19.650><c> file</c><00:01:19.860><c> hub</c><00:01:20.100><c> app</c><00:01:20.280><c> on</c><00:01:20.670><c> my</c><00:01:20.970><c> iPad</c><00:01:24.380><c> so</c><00:01:25.380><c> my</c><00:01:25.530><c> goal</c>

00:01:25.700 --> 00:01:25.710 align:start position:0%
the file hub app on my iPad so my goal
 

00:01:25.710 --> 00:01:28.490 align:start position:0%
the file hub app on my iPad so my goal
here<00:01:26.009><c> is</c><00:01:26.159><c> to</c><00:01:26.189><c> take</c><00:01:26.460><c> a</c><00:01:26.610><c> raw</c><00:01:26.970><c> file</c><00:01:27.330><c> on</c><00:01:27.780><c> the</c><00:01:28.140><c> SD</c>

00:01:28.490 --> 00:01:28.500 align:start position:0%
here is to take a raw file on the SD
 

00:01:28.500 --> 00:01:30.770 align:start position:0%
here is to take a raw file on the SD
card<00:01:28.770><c> and</c><00:01:28.950><c> open</c><00:01:29.340><c> it</c><00:01:29.460><c> in</c><00:01:29.579><c> Lightroom</c><00:01:30.329><c> on</c><00:01:30.509><c> the</c>

00:01:30.770 --> 00:01:30.780 align:start position:0%
card and open it in Lightroom on the
 

00:01:30.780 --> 00:01:33.080 align:start position:0%
card and open it in Lightroom on the
iPad<00:01:31.079><c> first</c><00:01:31.950><c> I'm</c><00:01:32.189><c> going</c><00:01:32.430><c> to</c><00:01:32.520><c> open</c><00:01:32.640><c> up</c><00:01:32.850><c> the</c>

00:01:33.080 --> 00:01:33.090 align:start position:0%
iPad first I'm going to open up the
 

00:01:33.090 --> 00:01:36.230 align:start position:0%
iPad first I'm going to open up the
photos<00:01:33.689><c> section</c><00:01:34.170><c> now</c><00:01:34.770><c> I've</c><00:01:35.280><c> already</c><00:01:35.759><c> set</c><00:01:36.030><c> the</c>

00:01:36.230 --> 00:01:36.240 align:start position:0%
photos section now I've already set the
 

00:01:36.240 --> 00:01:39.590 align:start position:0%
photos section now I've already set the
DLNA<00:01:37.020><c> option</c><00:01:37.680><c> to</c><00:01:37.860><c> look</c><00:01:38.009><c> at</c><00:01:38.130><c> the</c><00:01:38.250><c> root</c><00:01:38.490><c> of</c><00:01:38.729><c> my</c><00:01:39.240><c> SD</c>

00:01:39.590 --> 00:01:39.600 align:start position:0%
DLNA option to look at the root of my SD
 

00:01:39.600 --> 00:01:41.539 align:start position:0%
DLNA option to look at the root of my SD
card<00:01:39.810><c> so</c><00:01:40.259><c> that's</c><00:01:40.470><c> where</c><00:01:40.650><c> the</c><00:01:40.799><c> photos</c><00:01:41.340><c> of</c>

00:01:41.539 --> 00:01:41.549 align:start position:0%
card so that's where the photos of
 

00:01:41.549 --> 00:01:43.700 align:start position:0%
card so that's where the photos of
videos<00:01:41.970><c> and</c><00:01:42.270><c> music</c><00:01:42.540><c> options</c><00:01:43.140><c> are</c><00:01:43.290><c> looking</c>

00:01:43.700 --> 00:01:43.710 align:start position:0%
videos and music options are looking
 

00:01:43.710 --> 00:01:46.999 align:start position:0%
videos and music options are looking
well<00:01:44.399><c> that</c><00:01:44.850><c> and</c><00:01:45.240><c> all</c><00:01:45.509><c> sub</c><00:01:45.840><c> directories</c><00:01:46.259><c> for</c>

00:01:46.999 --> 00:01:47.009 align:start position:0%
well that and all sub directories for
 

00:01:47.009 --> 00:01:49.460 align:start position:0%
well that and all sub directories for
more<00:01:47.189><c> about</c><00:01:47.399><c> setting</c><00:01:47.640><c> up</c><00:01:47.880><c> that</c><00:01:48.060><c> DLNA</c><00:01:48.869><c> option</c>

00:01:49.460 --> 00:01:49.470 align:start position:0%
more about setting up that DLNA option
 

00:01:49.470 --> 00:01:52.480 align:start position:0%
more about setting up that DLNA option
see<00:01:50.040><c> the</c><00:01:50.189><c> video</c><00:01:50.430><c> link</c><00:01:50.850><c> to</c><00:01:51.090><c> above</c>

00:01:52.480 --> 00:01:52.490 align:start position:0%
see the video link to above
 

00:01:52.490 --> 00:01:54.440 align:start position:0%
see the video link to above
unfortunately<00:01:53.490><c> my</c><00:01:53.610><c> RAW</c><00:01:53.820><c> files</c><00:01:54.060><c> aren't</c>

00:01:54.440 --> 00:01:54.450 align:start position:0%
unfortunately my RAW files aren't
 

00:01:54.450 --> 00:01:56.929 align:start position:0%
unfortunately my RAW files aren't
showing<00:01:54.780><c> up</c><00:01:54.899><c> if</c><00:01:55.259><c> I</c><00:01:55.680><c> go</c><00:01:55.860><c> back</c><00:01:56.070><c> and</c><00:01:56.250><c> open</c><00:01:56.399><c> videos</c>

00:01:56.929 --> 00:01:56.939 align:start position:0%
showing up if I go back and open videos
 

00:01:56.939 --> 00:01:59.149 align:start position:0%
showing up if I go back and open videos
I<00:01:57.240><c> can</c><00:01:57.390><c> list</c><00:01:57.659><c> the</c><00:01:57.840><c> video</c><00:01:58.079><c> files</c><00:01:58.439><c> but</c><00:01:58.770><c> when</c><00:01:59.130><c> I</c>

00:01:59.149 --> 00:01:59.159 align:start position:0%
I can list the video files but when I
 

00:01:59.159 --> 00:02:01.670 align:start position:0%
I can list the video files but when I
play<00:01:59.430><c> one</c><00:01:59.640><c> it's</c><00:01:59.909><c> pretty</c><00:02:00.240><c> choppy</c><00:02:00.479><c> now</c><00:02:01.350><c> that's</c><00:02:01.560><c> a</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
play one it's pretty choppy now that's a
 

00:02:01.680 --> 00:02:04.190 align:start position:0%
play one it's pretty choppy now that's a
video<00:02:01.890><c> I</c><00:02:01.920><c> took</c><00:02:02.040><c> at</c><00:02:02.399><c> dev</c><00:02:02.790><c> ops</c><00:02:02.969><c> days</c><00:02:03.240><c> Toronto</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
video I took at dev ops days Toronto
 

00:02:04.200 --> 00:02:05.450 align:start position:0%
video I took at dev ops days Toronto
earlier<00:02:04.350><c> in</c><00:02:04.680><c> the</c><00:02:04.770><c> year</c><00:02:04.890><c> to</c><00:02:05.009><c> compare</c><00:02:05.280><c> it</c><00:02:05.340><c> to</c><00:02:05.430><c> a</c>

00:02:05.450 --> 00:02:05.460 align:start position:0%
earlier in the year to compare it to a
 

00:02:05.460 --> 00:02:08.240 align:start position:0%
earlier in the year to compare it to a
show<00:02:05.759><c> I</c><00:02:05.909><c> helped</c><00:02:06.210><c> run</c><00:02:06.420><c> at</c><00:02:06.659><c> dev</c><00:02:07.170><c> ops</c><00:02:07.350><c> days</c><00:02:07.560><c> Boston</c>

00:02:08.240 --> 00:02:08.250 align:start position:0%
show I helped run at dev ops days Boston
 

00:02:08.250 --> 00:02:10.609 align:start position:0%
show I helped run at dev ops days Boston
the<00:02:09.239><c> reason</c><00:02:09.569><c> why</c><00:02:09.690><c> it's</c><00:02:09.840><c> choppy</c><00:02:10.200><c> is</c><00:02:10.440><c> that</c><00:02:10.590><c> I</c>

00:02:10.609 --> 00:02:10.619 align:start position:0%
the reason why it's choppy is that I
 

00:02:10.619 --> 00:02:13.040 align:start position:0%
the reason why it's choppy is that I
tend<00:02:10.890><c> to</c><00:02:10.979><c> do</c><00:02:11.190><c> a</c><00:02:11.220><c> pretty</c><00:02:11.879><c> high</c><00:02:12.150><c> bandwidth</c><00:02:12.690><c> on</c><00:02:12.870><c> my</c>

00:02:13.040 --> 00:02:13.050 align:start position:0%
tend to do a pretty high bandwidth on my
 

00:02:13.050 --> 00:02:13.370 align:start position:0%
tend to do a pretty high bandwidth on my
video

00:02:13.370 --> 00:02:13.380 align:start position:0%
video
 

00:02:13.380 --> 00:02:15.920 align:start position:0%
video
and<00:02:13.590><c> they</c><00:02:14.040><c> aren't</c><00:02:14.400><c> intended</c><00:02:14.880><c> to</c><00:02:15.180><c> stream</c><00:02:15.570><c> yet</c>

00:02:15.920 --> 00:02:15.930 align:start position:0%
and they aren't intended to stream yet
 

00:02:15.930 --> 00:02:17.960 align:start position:0%
and they aren't intended to stream yet
most<00:02:16.920><c> videos</c><00:02:17.220><c> that</c><00:02:17.370><c> you</c><00:02:17.460><c> actually</c><00:02:17.610><c> want</c><00:02:17.880><c> to</c>

00:02:17.960 --> 00:02:17.970 align:start position:0%
most videos that you actually want to
 

00:02:17.970 --> 00:02:20.990 align:start position:0%
most videos that you actually want to
watch<00:02:18.120><c> will</c><00:02:18.750><c> perform</c><00:02:19.110><c> better</c><00:02:19.640><c> but</c><00:02:20.640><c> I</c><00:02:20.700><c> want</c><00:02:20.940><c> to</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
watch will perform better but I want to
 

00:02:21.000 --> 00:02:23.750 align:start position:0%
watch will perform better but I want to
open<00:02:21.090><c> a</c><00:02:21.420><c> raw</c><00:02:21.690><c> file</c><00:02:22.050><c> in</c><00:02:22.350><c> Lightroom</c><00:02:22.920><c> so</c><00:02:23.370><c> let's</c><00:02:23.610><c> go</c>

00:02:23.750 --> 00:02:23.760 align:start position:0%
open a raw file in Lightroom so let's go
 

00:02:23.760 --> 00:02:25.910 align:start position:0%
open a raw file in Lightroom so let's go
to<00:02:23.850><c> file</c><00:02:24.270><c> management</c><00:02:24.720><c> navigate</c><00:02:25.710><c> to</c><00:02:25.830><c> the</c>

00:02:25.910 --> 00:02:25.920 align:start position:0%
to file management navigate to the
 

00:02:25.920 --> 00:02:27.410 align:start position:0%
to file management navigate to the
folder<00:02:26.130><c> with</c><00:02:26.400><c> your</c><00:02:26.430><c> role</c><00:02:26.730><c> files</c><00:02:26.970><c> and</c><00:02:27.240><c> click</c>

00:02:27.410 --> 00:02:27.420 align:start position:0%
folder with your role files and click
 

00:02:27.420 --> 00:02:30.200 align:start position:0%
folder with your role files and click
one<00:02:27.600><c> you</c><00:02:28.410><c> get</c><00:02:28.590><c> an</c><00:02:28.710><c> error</c><00:02:28.920><c> and</c><00:02:29.220><c> I</c><00:02:29.670><c> haven't</c><00:02:29.940><c> seen</c>

00:02:30.200 --> 00:02:30.210 align:start position:0%
one you get an error and I haven't seen
 

00:02:30.210 --> 00:02:33.290 align:start position:0%
one you get an error and I haven't seen
a<00:02:30.420><c> way</c><00:02:30.690><c> to</c><00:02:30.830><c> set</c><00:02:31.830><c> up</c><00:02:31.860><c> an</c><00:02:32.070><c> application</c><00:02:32.610><c> to</c><00:02:33.030><c> open</c>

00:02:33.290 --> 00:02:33.300 align:start position:0%
a way to set up an application to open
 

00:02:33.300 --> 00:02:36.320 align:start position:0%
a way to set up an application to open
these<00:02:34.020><c> but</c><00:02:34.770><c> if</c><00:02:34.950><c> you</c><00:02:35.130><c> click</c><00:02:35.370><c> and</c><00:02:35.520><c> hold</c><00:02:35.580><c> you'll</c>

00:02:36.320 --> 00:02:36.330 align:start position:0%
these but if you click and hold you'll
 

00:02:36.330 --> 00:02:38.930 align:start position:0%
these but if you click and hold you'll
select<00:02:36.570><c> the</c><00:02:36.780><c> image</c><00:02:37.080><c> now</c><00:02:37.680><c> click</c><00:02:38.220><c> on</c><00:02:38.310><c> share</c><00:02:38.700><c> and</c>

00:02:38.930 --> 00:02:38.940 align:start position:0%
select the image now click on share and
 

00:02:38.940 --> 00:02:40.580 align:start position:0%
select the image now click on share and
you<00:02:39.030><c> get</c><00:02:39.180><c> the</c><00:02:39.330><c> standard</c><00:02:39.780><c> share</c><00:02:40.110><c> sheet</c>

00:02:40.580 --> 00:02:40.590 align:start position:0%
you get the standard share sheet
 

00:02:40.590 --> 00:02:42.530 align:start position:0%
you get the standard share sheet
assuming<00:02:41.580><c> you</c><00:02:41.670><c> have</c><00:02:41.700><c> Lightroom</c><00:02:42.150><c> and</c><00:02:42.180><c> sold</c>

00:02:42.530 --> 00:02:42.540 align:start position:0%
assuming you have Lightroom and sold
 

00:02:42.540 --> 00:02:44.090 align:start position:0%
assuming you have Lightroom and sold
you'll<00:02:42.810><c> be</c><00:02:42.960><c> able</c><00:02:43.050><c> to</c><00:02:43.200><c> click</c><00:02:43.380><c> the</c><00:02:43.530><c> icon</c><00:02:43.890><c> to</c><00:02:44.010><c> open</c>

00:02:44.090 --> 00:02:44.100 align:start position:0%
you'll be able to click the icon to open
 

00:02:44.100 --> 00:02:46.460 align:start position:0%
you'll be able to click the icon to open
in<00:02:44.520><c> late</c><00:02:44.760><c> room</c><00:02:45.030><c> but</c><00:02:45.750><c> notice</c><00:02:46.050><c> that</c><00:02:46.080><c> I</c><00:02:46.200><c> can</c><00:02:46.380><c> also</c>

00:02:46.460 --> 00:02:46.470 align:start position:0%
in late room but notice that I can also
 

00:02:46.470 --> 00:02:49.250 align:start position:0%
in late room but notice that I can also
open<00:02:46.680><c> it</c><00:02:47.070><c> in</c><00:02:47.310><c> affinity</c><00:02:47.880><c> photo</c><00:02:48.260><c> Pixelmator</c>

00:02:49.250 --> 00:02:49.260 align:start position:0%
open it in affinity photo Pixelmator
 

00:02:49.260 --> 00:02:52.340 align:start position:0%
open it in affinity photo Pixelmator
concepts<00:02:50.100><c> and</c><00:02:50.310><c> lots</c><00:02:50.940><c> of</c><00:02:51.090><c> other</c><00:02:51.270><c> places</c><00:02:51.510><c> but</c>

00:02:52.340 --> 00:02:52.350 align:start position:0%
concepts and lots of other places but
 

00:02:52.350 --> 00:02:54.710 align:start position:0%
concepts and lots of other places but
why<00:02:52.860><c> lamort</c><00:02:53.340><c> asked</c><00:02:53.850><c> about</c><00:02:54.060><c> Lightroom</c><00:02:54.300><c> so</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
why lamort asked about Lightroom so
 

00:02:54.720 --> 00:02:59.890 align:start position:0%
why lamort asked about Lightroom so
let's<00:02:55.050><c> go</c><00:02:55.200><c> there</c>

00:02:59.890 --> 00:02:59.900 align:start position:0%
 
 

00:02:59.900 --> 00:03:02.080 align:start position:0%
 
now<00:03:00.590><c> I</c><00:03:00.620><c> can</c><00:03:00.920><c> make</c><00:03:01.010><c> all</c><00:03:01.220><c> the</c><00:03:01.400><c> changes</c><00:03:01.580><c> I</c><00:03:01.879><c> want</c>

00:03:02.080 --> 00:03:02.090 align:start position:0%
now I can make all the changes I want
 

00:03:02.090 --> 00:03:04.360 align:start position:0%
now I can make all the changes I want
when<00:03:02.690><c> it</c><00:03:02.810><c> comes</c><00:03:02.989><c> to</c><00:03:03.170><c> save</c><00:03:03.500><c> the</c><00:03:03.709><c> file</c><00:03:03.920><c> I</c><00:03:04.159><c> can</c>

00:03:04.360 --> 00:03:04.370 align:start position:0%
when it comes to save the file I can
 

00:03:04.370 --> 00:03:06.250 align:start position:0%
when it comes to save the file I can
choose<00:03:04.580><c> to</c><00:03:04.610><c> export</c><00:03:05.120><c> this</c><00:03:05.360><c> to</c><00:03:05.420><c> the</c><00:03:05.750><c> camera</c><00:03:06.110><c> roll</c>

00:03:06.250 --> 00:03:06.260 align:start position:0%
choose to export this to the camera roll
 

00:03:06.260 --> 00:03:09.070 align:start position:0%
choose to export this to the camera roll
on<00:03:06.500><c> my</c><00:03:06.650><c> iPad</c><00:03:07.069><c> or</c><00:03:07.340><c> if</c><00:03:07.879><c> I</c><00:03:07.970><c> decide</c><00:03:08.299><c> to</c><00:03:08.330><c> open</c><00:03:08.780><c> it</c><00:03:08.900><c> I</c>

00:03:09.070 --> 00:03:09.080 align:start position:0%
on my iPad or if I decide to open it I
 

00:03:09.080 --> 00:03:11.800 align:start position:0%
on my iPad or if I decide to open it I
can<00:03:09.500><c> save</c><00:03:09.769><c> it</c><00:03:09.920><c> back</c><00:03:10.040><c> to</c><00:03:10.250><c> the</c><00:03:10.370><c> SD</c><00:03:10.760><c> card</c><00:03:10.790><c> now</c><00:03:11.750><c> the</c>

00:03:11.800 --> 00:03:11.810 align:start position:0%
can save it back to the SD card now the
 

00:03:11.810 --> 00:03:13.960 align:start position:0%
can save it back to the SD card now the
language<00:03:12.080><c> here</c><00:03:12.680><c> is</c><00:03:12.830><c> a</c><00:03:12.860><c> bit</c><00:03:13.129><c> confusing</c><00:03:13.610><c> but</c>

00:03:13.960 --> 00:03:13.970 align:start position:0%
language here is a bit confusing but
 

00:03:13.970 --> 00:03:16.960 align:start position:0%
language here is a bit confusing but
open<00:03:14.810><c> basically</c><00:03:15.590><c> means</c><00:03:15.830><c> open</c><00:03:16.370><c> the</c><00:03:16.459><c> file</c><00:03:16.730><c> in</c>

00:03:16.960 --> 00:03:16.970 align:start position:0%
open basically means open the file in
 

00:03:16.970 --> 00:03:18.970 align:start position:0%
open basically means open the file in
another<00:03:17.299><c> app</c><00:03:17.480><c> and</c><00:03:17.690><c> do</c><00:03:17.900><c> whatever</c><00:03:18.110><c> you</c><00:03:18.440><c> do</c><00:03:18.680><c> in</c>

00:03:18.970 --> 00:03:18.980 align:start position:0%
another app and do whatever you do in
 

00:03:18.980 --> 00:03:21.729 align:start position:0%
another app and do whatever you do in
that<00:03:19.040><c> app</c><00:03:19.549><c> with</c><00:03:20.239><c> a</c><00:03:20.269><c> new</c><00:03:20.510><c> file</c><00:03:20.720><c> and</c><00:03:20.989><c> that's</c><00:03:21.560><c> how</c>

00:03:21.729 --> 00:03:21.739 align:start position:0%
that app with a new file and that's how
 

00:03:21.739 --> 00:03:23.860 align:start position:0%
that app with a new file and that's how
to<00:03:21.799><c> open</c><00:03:22.010><c> RAW</c><00:03:22.340><c> files</c><00:03:22.579><c> stored</c><00:03:23.030><c> on</c><00:03:23.090><c> your</c><00:03:23.329><c> SD</c><00:03:23.629><c> card</c>

00:03:23.860 --> 00:03:23.870 align:start position:0%
to open RAW files stored on your SD card
 

00:03:23.870 --> 00:03:26.830 align:start position:0%
to open RAW files stored on your SD card
and<00:03:24.079><c> work</c><00:03:24.590><c> with</c><00:03:24.799><c> them</c><00:03:24.950><c> on</c><00:03:25.159><c> your</c><00:03:25.250><c> iPad</c><00:03:25.670><c> if</c><00:03:26.120><c> you</c>

00:03:26.830 --> 00:03:26.840 align:start position:0%
and work with them on your iPad if you
 

00:03:26.840 --> 00:03:28.390 align:start position:0%
and work with them on your iPad if you
find<00:03:27.140><c> this</c><00:03:27.260><c> video</c><00:03:27.470><c> helpful</c><00:03:27.620><c> consider</c>

00:03:28.390 --> 00:03:28.400 align:start position:0%
find this video helpful consider
 

00:03:28.400 --> 00:03:30.729 align:start position:0%
find this video helpful consider
subscribing<00:03:28.700><c> to</c><00:03:29.239><c> the</c><00:03:29.480><c> channel</c><00:03:29.510><c> right</c><00:03:30.140><c> here</c>

00:03:30.729 --> 00:03:30.739 align:start position:0%
subscribing to the channel right here
 

00:03:30.739 --> 00:03:32.979 align:start position:0%
subscribing to the channel right here
and<00:03:30.980><c> if</c><00:03:31.819><c> you</c><00:03:31.940><c> have</c><00:03:32.090><c> any</c><00:03:32.239><c> other</c><00:03:32.359><c> questions</c><00:03:32.840><c> let</c>

00:03:32.979 --> 00:03:32.989 align:start position:0%
and if you have any other questions let
 

00:03:32.989 --> 00:03:35.140 align:start position:0%
and if you have any other questions let
me<00:03:33.200><c> know</c><00:03:33.290><c> in</c><00:03:33.440><c> the</c><00:03:33.620><c> comments</c><00:03:34.040><c> down</c><00:03:34.430><c> there</c><00:03:34.489><c> as</c>

00:03:35.140 --> 00:03:35.150 align:start position:0%
me know in the comments down there as
 

00:03:35.150 --> 00:03:37.839 align:start position:0%
me know in the comments down there as
you<00:03:36.079><c> can</c><00:03:36.410><c> see</c><00:03:36.680><c> I've</c><00:03:37.010><c> been</c><00:03:37.160><c> making</c><00:03:37.340><c> some</c><00:03:37.819><c> of</c>

00:03:37.839 --> 00:03:37.849 align:start position:0%
you can see I've been making some of
 

00:03:37.849 --> 00:03:40.030 align:start position:0%
you can see I've been making some of
those<00:03:38.000><c> questions</c><00:03:38.269><c> into</c><00:03:38.660><c> videos</c><00:03:39.049><c> and</c><00:03:39.379><c> check</c>

00:03:40.030 --> 00:03:40.040 align:start position:0%
those questions into videos and check
 

00:03:40.040 --> 00:03:41.800 align:start position:0%
those questions into videos and check
out<00:03:40.160><c> some</c><00:03:40.220><c> of</c><00:03:40.430><c> these</c><00:03:40.519><c> other</c><00:03:40.790><c> videos</c><00:03:41.120><c> that</c><00:03:41.780><c> I</c>

00:03:41.800 --> 00:03:41.810 align:start position:0%
out some of these other videos that I
 

00:03:41.810 --> 00:03:43.589 align:start position:0%
out some of these other videos that I
think<00:03:41.900><c> you</c><00:03:42.230><c> might</c><00:03:42.440><c> also</c><00:03:42.680><c> find</c><00:03:42.950><c> interesting</c>

00:03:43.589 --> 00:03:43.599 align:start position:0%
think you might also find interesting
 

00:03:43.599 --> 00:03:47.650 align:start position:0%
think you might also find interesting
thanks<00:03:44.599><c> for</c><00:03:44.720><c> watching</c><00:03:45.049><c> goodbye</c>

