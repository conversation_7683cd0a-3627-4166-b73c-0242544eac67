WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.690 align:start position:0%
 
Eisen<00:00:00.299><c> what's</c><00:00:00.539><c> up</c><00:00:00.780><c> and</c><00:00:01.050><c> in</c><00:00:01.770><c> today's</c><00:00:02.070><c> video</c><00:00:02.310><c> is</c>

00:00:02.690 --> 00:00:02.700 align:start position:0%
Eisen what's up and in today's video is
 

00:00:02.700 --> 00:00:05.210 align:start position:0%
Eisen what's up and in today's video is
gonna<00:00:02.879><c> be</c><00:00:02.970><c> episode</c><00:00:03.570><c> number</c><00:00:03.990><c> one</c><00:00:04.230><c> episode</c>

00:00:05.210 --> 00:00:05.220 align:start position:0%
gonna be episode number one episode
 

00:00:05.220 --> 00:00:07.460 align:start position:0%
gonna be episode number one episode
number<00:00:05.370><c> one</c><00:00:05.759><c> of</c><00:00:06.000><c> actually</c><00:00:06.839><c> tying</c><00:00:07.230><c> everything</c>

00:00:07.460 --> 00:00:07.470 align:start position:0%
number one of actually tying everything
 

00:00:07.470 --> 00:00:10.160 align:start position:0%
number one of actually tying everything
together<00:00:07.890><c> so</c><00:00:08.340><c> if</c><00:00:08.429><c> you</c><00:00:08.580><c> remember</c><00:00:09.050><c> what</c><00:00:10.050><c> we</c><00:00:10.139><c> did</c>

00:00:10.160 --> 00:00:10.170 align:start position:0%
together so if you remember what we did
 

00:00:10.170 --> 00:00:12.320 align:start position:0%
together so if you remember what we did
in<00:00:10.349><c> the</c><00:00:10.500><c> previous</c><00:00:10.650><c> videos</c><00:00:11.340><c> was</c><00:00:11.550><c> all</c><00:00:11.759><c> sorts</c><00:00:12.240><c> of</c>

00:00:12.320 --> 00:00:12.330 align:start position:0%
in the previous videos was all sorts of
 

00:00:12.330 --> 00:00:14.330 align:start position:0%
in the previous videos was all sorts of
different<00:00:12.540><c> technologies</c><00:00:13.440><c> we've</c><00:00:13.710><c> covered</c><00:00:13.980><c> how</c>

00:00:14.330 --> 00:00:14.340 align:start position:0%
different technologies we've covered how
 

00:00:14.340 --> 00:00:18.410 align:start position:0%
different technologies we've covered how
to<00:00:14.400><c> deploy</c><00:00:15.179><c> to</c><00:00:15.509><c> Heroku</c><00:00:16.369><c> I</c><00:00:17.369><c> think</c><00:00:18.150><c> that</c><00:00:18.300><c> the</c>

00:00:18.410 --> 00:00:18.420 align:start position:0%
to deploy to Heroku I think that the
 

00:00:18.420 --> 00:00:21.200 align:start position:0%
to deploy to Heroku I think that the
best<00:00:18.630><c> the</c><00:00:19.080><c> best</c><00:00:19.289><c> illustration</c><00:00:20.130><c> of</c><00:00:20.340><c> what</c><00:00:21.029><c> we've</c>

00:00:21.200 --> 00:00:21.210 align:start position:0%
best the best illustration of what we've
 

00:00:21.210 --> 00:00:24.259 align:start position:0%
best the best illustration of what we've
done<00:00:21.449><c> so</c><00:00:21.720><c> far</c><00:00:21.750><c> is</c><00:00:22.430><c> essentially</c><00:00:23.430><c> over</c><00:00:23.699><c> here</c>

00:00:24.259 --> 00:00:24.269 align:start position:0%
done so far is essentially over here
 

00:00:24.269 --> 00:00:26.720 align:start position:0%
done so far is essentially over here
which<00:00:24.600><c> is</c><00:00:24.750><c> in</c><00:00:24.990><c> the</c><00:00:25.199><c> github</c><00:00:25.439><c> repo</c><00:00:25.920><c> and</c><00:00:26.369><c> you</c><00:00:26.580><c> can</c>

00:00:26.720 --> 00:00:26.730 align:start position:0%
which is in the github repo and you can
 

00:00:26.730 --> 00:00:29.419 align:start position:0%
which is in the github repo and you can
also<00:00:26.849><c> have</c><00:00:27.269><c> a</c><00:00:27.300><c> look</c><00:00:27.480><c> at</c><00:00:27.630><c> that</c><00:00:27.779><c> we've</c><00:00:28.590><c> we've</c>

00:00:29.419 --> 00:00:29.429 align:start position:0%
also have a look at that we've we've
 

00:00:29.429 --> 00:00:32.600 align:start position:0%
also have a look at that we've we've
done<00:00:29.460><c> meta</c><00:00:29.939><c> based</c><00:00:30.179><c> data</c><00:00:30.590><c> visualization</c><00:00:31.610><c> we've</c>

00:00:32.600 --> 00:00:32.610 align:start position:0%
done meta based data visualization we've
 

00:00:32.610 --> 00:00:34.160 align:start position:0%
done meta based data visualization we've
we've<00:00:33.360><c> used</c><00:00:33.570><c> nodejs</c>

00:00:34.160 --> 00:00:34.170 align:start position:0%
we've used nodejs
 

00:00:34.170 --> 00:00:36.830 align:start position:0%
we've used nodejs
angularjs<00:00:34.829><c> and</c><00:00:34.980><c> my</c><00:00:35.160><c> sequel</c><00:00:35.670><c> we've</c><00:00:36.630><c> used</c>

00:00:36.830 --> 00:00:36.840 align:start position:0%
angularjs and my sequel we've used
 

00:00:36.840 --> 00:00:39.350 align:start position:0%
angularjs and my sequel we've used
intercom<00:00:37.500><c> and</c><00:00:37.920><c> we've</c><00:00:38.190><c> done</c><00:00:38.610><c> deploying</c><00:00:39.090><c> to</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
intercom and we've done deploying to
 

00:00:39.360 --> 00:00:41.900 align:start position:0%
intercom and we've done deploying to
Heroku<00:00:39.690><c> and</c><00:00:40.110><c> if</c><00:00:40.379><c> you</c><00:00:40.739><c> look</c><00:00:40.890><c> at</c><00:00:41.010><c> the</c><00:00:41.190><c> blog</c><00:00:41.610><c> the</c>

00:00:41.900 --> 00:00:41.910 align:start position:0%
Heroku and if you look at the blog the
 

00:00:41.910 --> 00:00:44.119 align:start position:0%
Heroku and if you look at the blog the
way<00:00:42.000><c> that</c><00:00:42.149><c> it</c><00:00:42.270><c> looks</c><00:00:42.450><c> currently</c><00:00:43.020><c> it's</c><00:00:43.440><c> this</c>

00:00:44.119 --> 00:00:44.129 align:start position:0%
way that it looks currently it's this
 

00:00:44.129 --> 00:00:46.700 align:start position:0%
way that it looks currently it's this
which<00:00:44.910><c> is</c><00:00:45.180><c> the</c><00:00:45.600><c> theme</c><00:00:45.930><c> this</c><00:00:46.260><c> is</c><00:00:46.320><c> the</c><00:00:46.559><c> new</c>

00:00:46.700 --> 00:00:46.710 align:start position:0%
which is the theme this is the new
 

00:00:46.710 --> 00:00:48.709 align:start position:0%
which is the theme this is the new
feature<00:00:46.980><c> which</c><00:00:47.250><c> is</c><00:00:47.399><c> the</c><00:00:47.610><c> themes</c><00:00:47.910><c> feature</c><00:00:48.329><c> so</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
feature which is the themes feature so
 

00:00:48.719 --> 00:00:51.500 align:start position:0%
feature which is the themes feature so
if<00:00:49.289><c> I</c><00:00:49.649><c> what</c><00:00:50.520><c> happened</c><00:00:50.640><c> here</c><00:00:51.090><c> okay</c>

00:00:51.500 --> 00:00:51.510 align:start position:0%
if I what happened here okay
 

00:00:51.510 --> 00:00:54.650 align:start position:0%
if I what happened here okay
I<00:00:51.690><c> was</c><00:00:52.559><c> I</c><00:00:52.800><c> need</c><00:00:53.070><c> to</c><00:00:53.190><c> do</c><00:00:53.309><c> no</c><00:00:53.550><c> daman</c><00:00:53.850><c> server</c><00:00:54.210><c> and</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
I was I need to do no daman server and
 

00:00:54.660 --> 00:00:57.110 align:start position:0%
I was I need to do no daman server and
I'm<00:00:54.960><c> also</c><00:00:55.260><c> let's</c><00:00:56.070><c> first</c><00:00:56.370><c> talk</c><00:00:56.579><c> about</c><00:00:56.640><c> how</c><00:00:57.059><c> to</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
I'm also let's first talk about how to
 

00:00:57.120 --> 00:01:02.479 align:start position:0%
I'm also let's first talk about how to
actually<00:00:59.780><c> add</c><00:01:00.780><c> a</c><00:01:00.809><c> clone</c><00:01:01.289><c> and</c><00:01:01.680><c> install</c><00:01:02.250><c> the</c>

00:01:02.479 --> 00:01:02.489 align:start position:0%
actually add a clone and install the
 

00:01:02.489 --> 00:01:04.880 align:start position:0%
actually add a clone and install the
project<00:01:03.090><c> I</c><00:01:03.300><c> feel</c><00:01:03.480><c> like</c><00:01:03.629><c> that</c><00:01:03.840><c> would</c><00:01:03.989><c> be</c><00:01:04.199><c> the</c>

00:01:04.880 --> 00:01:04.890 align:start position:0%
project I feel like that would be the
 

00:01:04.890 --> 00:01:07.490 align:start position:0%
project I feel like that would be the
best<00:01:05.129><c> possible</c><00:01:05.570><c> as</c><00:01:06.570><c> possible</c><00:01:07.049><c> use</c><00:01:07.229><c> of</c><00:01:07.260><c> our</c>

00:01:07.490 --> 00:01:07.500 align:start position:0%
best possible as possible use of our
 

00:01:07.500 --> 00:01:09.740 align:start position:0%
best possible as possible use of our
time<00:01:07.710><c> this</c><00:01:07.890><c> is</c><00:01:08.040><c> episode</c><00:01:08.549><c> number</c><00:01:08.790><c> one</c><00:01:09.240><c> if</c><00:01:09.510><c> you</c>

00:01:09.740 --> 00:01:09.750 align:start position:0%
time this is episode number one if you
 

00:01:09.750 --> 00:01:12.740 align:start position:0%
time this is episode number one if you
want<00:01:09.990><c> to</c><00:01:10.080><c> go</c><00:01:10.229><c> back</c><00:01:10.530><c> and</c><00:01:10.860><c> check</c><00:01:11.600><c> delve</c><00:01:12.600><c> a</c><00:01:12.720><c> little</c>

00:01:12.740 --> 00:01:12.750 align:start position:0%
want to go back and check delve a little
 

00:01:12.750 --> 00:01:15.140 align:start position:0%
want to go back and check delve a little
deeper<00:01:13.020><c> into</c><00:01:13.350><c> other</c><00:01:13.770><c> aspects</c><00:01:14.430><c> of</c><00:01:14.640><c> the</c><00:01:14.790><c> repo</c>

00:01:15.140 --> 00:01:15.150 align:start position:0%
deeper into other aspects of the repo
 

00:01:15.150 --> 00:01:16.910 align:start position:0%
deeper into other aspects of the repo
you<00:01:15.330><c> can</c><00:01:15.479><c> definitely</c><00:01:15.659><c> check</c><00:01:16.229><c> out</c><00:01:16.350><c> the</c><00:01:16.530><c> channel</c>

00:01:16.910 --> 00:01:16.920 align:start position:0%
you can definitely check out the channel
 

00:01:16.920 --> 00:01:18.910 align:start position:0%
you can definitely check out the channel
and<00:01:17.009><c> that's</c><00:01:17.130><c> going</c><00:01:17.310><c> to</c><00:01:17.369><c> show</c><00:01:17.430><c> you</c><00:01:17.640><c> pretty</c><00:01:18.540><c> much</c>

00:01:18.910 --> 00:01:18.920 align:start position:0%
and that's going to show you pretty much
 

00:01:18.920 --> 00:01:23.210 align:start position:0%
and that's going to show you pretty much
tutorials<00:01:19.920><c> on</c><00:01:20.130><c> every</c><00:01:20.549><c> single</c><00:01:21.740><c> piece</c><00:01:22.740><c> of</c><00:01:23.040><c> the</c>

00:01:23.210 --> 00:01:23.220 align:start position:0%
tutorials on every single piece of the
 

00:01:23.220 --> 00:01:26.539 align:start position:0%
tutorials on every single piece of the
puzzle<00:01:23.670><c> which</c><00:01:24.000><c> is</c><00:01:24.270><c> these</c><00:01:24.540><c> for</c><00:01:25.369><c> deploying</c><00:01:26.369><c> to</c>

00:01:26.539 --> 00:01:26.549 align:start position:0%
puzzle which is these for deploying to
 

00:01:26.549 --> 00:01:29.960 align:start position:0%
puzzle which is these for deploying to
Heroku<00:01:26.880><c> and</c><00:01:27.360><c> the</c><00:01:27.689><c> project</c><00:01:28.200><c> structure</c><00:01:28.530><c> and</c><00:01:28.979><c> the</c>

00:01:29.960 --> 00:01:29.970 align:start position:0%
Heroku and the project structure and the
 

00:01:29.970 --> 00:01:31.429 align:start position:0%
Heroku and the project structure and the
first<00:01:30.180><c> thing</c><00:01:30.299><c> we</c><00:01:30.420><c> want</c><00:01:30.570><c> to</c><00:01:30.600><c> do</c><00:01:30.720><c> is</c><00:01:30.840><c> actually</c>

00:01:31.429 --> 00:01:31.439 align:start position:0%
first thing we want to do is actually
 

00:01:31.439 --> 00:01:34.340 align:start position:0%
first thing we want to do is actually
clone<00:01:31.799><c> this</c><00:01:32.100><c> project</c><00:01:32.790><c> okay</c><00:01:33.180><c> and</c><00:01:33.540><c> then</c><00:01:34.229><c> you're</c>

00:01:34.340 --> 00:01:34.350 align:start position:0%
clone this project okay and then you're
 

00:01:34.350 --> 00:01:36.469 align:start position:0%
clone this project okay and then you're
gonna<00:01:34.530><c> go</c><00:01:34.740><c> into</c><00:01:35.009><c> clone</c><00:01:35.490><c> or</c><00:01:35.729><c> downloads</c><00:01:36.210><c> you're</c>

00:01:36.469 --> 00:01:36.479 align:start position:0%
gonna go into clone or downloads you're
 

00:01:36.479 --> 00:01:39.760 align:start position:0%
gonna go into clone or downloads you're
gonna<00:01:36.600><c> click</c><00:01:37.229><c> that</c><00:01:37.470><c> copy</c><00:01:37.860><c> it</c><00:01:38.130><c> you're</c><00:01:38.430><c> gonna</c><00:01:38.549><c> do</c>

00:01:39.760 --> 00:01:39.770 align:start position:0%
gonna click that copy it you're gonna do
 

00:01:39.770 --> 00:01:49.940 align:start position:0%
gonna click that copy it you're gonna do
git<00:01:40.770><c> clone</c><00:01:41.220><c> and</c><00:01:41.280><c> sec</c><00:01:42.320><c> C</c><00:01:43.320><c> CMD</c><00:01:47.240><c> CD</c><00:01:48.240><c> desktop</c><00:01:48.950><c> git</c>

00:01:49.940 --> 00:01:49.950 align:start position:0%
git clone and sec C CMD CD desktop git
 

00:01:49.950 --> 00:01:52.910 align:start position:0%
git clone and sec C CMD CD desktop git
clone<00:01:50.399><c> and</c><00:01:50.659><c> like</c><00:01:51.659><c> this</c><00:01:51.899><c> okay</c><00:01:52.590><c> you're</c><00:01:52.799><c> gonna</c>

00:01:52.910 --> 00:01:52.920 align:start position:0%
clone and like this okay you're gonna
 

00:01:52.920 --> 00:01:55.190 align:start position:0%
clone and like this okay you're gonna
press<00:01:53.270><c> enter.the</c><00:01:54.270><c> once</c><00:01:54.450><c> you're</c><00:01:54.630><c> done</c><00:01:54.810><c> with</c>

00:01:55.190 --> 00:01:55.200 align:start position:0%
press enter.the once you're done with
 

00:01:55.200 --> 00:01:59.179 align:start position:0%
press enter.the once you're done with
this<00:01:56.869><c> you're</c><00:01:57.869><c> gonna</c><00:01:58.170><c> do</c><00:01:58.439><c> okay</c><00:01:58.590><c> let</c><00:01:58.860><c> me</c><00:01:58.920><c> see</c><00:01:59.100><c> if</c>

00:01:59.179 --> 00:01:59.189 align:start position:0%
this you're gonna do okay let me see if
 

00:01:59.189 --> 00:02:01.069 align:start position:0%
this you're gonna do okay let me see if
I<00:01:59.310><c> actually</c><00:01:59.460><c> have</c><00:01:59.939><c> the</c><00:02:00.240><c> tutorial</c><00:02:00.840><c> on</c><00:02:00.930><c> the</c>

00:02:01.069 --> 00:02:01.079 align:start position:0%
I actually have the tutorial on the
 

00:02:01.079 --> 00:02:10.219 align:start position:0%
I actually have the tutorial on the
actual<00:02:01.350><c> website</c><00:02:01.469><c> one</c><00:02:02.189><c> sec</c><00:02:02.460><c> guys</c><00:02:08.629><c> I</c><00:02:09.629><c> do</c><00:02:09.780><c> have</c>

00:02:10.219 --> 00:02:10.229 align:start position:0%
actual website one sec guys I do have
 

00:02:10.229 --> 00:02:11.900 align:start position:0%
actual website one sec guys I do have
everything<00:02:10.470><c> here</c><00:02:10.830><c> on</c><00:02:10.920><c> the</c><00:02:11.009><c> website</c><00:02:11.370><c> get</c><00:02:11.580><c> clone</c>

00:02:11.900 --> 00:02:11.910 align:start position:0%
everything here on the website get clone
 

00:02:11.910 --> 00:02:13.770 align:start position:0%
everything here on the website get clone
CD<00:02:12.450><c> into</c><00:02:12.660><c> my</c><00:02:12.780><c> sequel</c><00:02:13.110><c> mode</c><00:02:13.260><c> angular</c>

00:02:13.770 --> 00:02:13.780 align:start position:0%
CD into my sequel mode angular
 

00:02:13.780 --> 00:02:17.699 align:start position:0%
CD into my sequel mode angular
npm<00:02:14.230><c> install</c><00:02:14.350><c> and</c><00:02:15.069><c> then</c><00:02:15.400><c> finally</c><00:02:16.030><c> node</c><00:02:16.390><c> man</c>

00:02:17.699 --> 00:02:17.709 align:start position:0%
npm install and then finally node man
 

00:02:17.709 --> 00:02:21.150 align:start position:0%
npm install and then finally node man
and<00:02:18.610><c> then</c><00:02:18.910><c> finally</c><00:02:19.330><c> node</c><00:02:19.569><c> man</c><00:02:19.840><c> server</c><00:02:20.170><c> j</c><00:02:20.709><c> s</c><00:02:20.890><c> and</c>

00:02:21.150 --> 00:02:21.160 align:start position:0%
and then finally node man server j s and
 

00:02:21.160 --> 00:02:22.559 align:start position:0%
and then finally node man server j s and
that's<00:02:21.370><c> pretty</c><00:02:21.580><c> much</c><00:02:21.700><c> what</c><00:02:21.970><c> we've</c><00:02:22.180><c> done</c><00:02:22.390><c> over</c>

00:02:22.559 --> 00:02:22.569 align:start position:0%
that's pretty much what we've done over
 

00:02:22.569 --> 00:02:25.830 align:start position:0%
that's pretty much what we've done over
here<00:02:22.930><c> as</c><00:02:23.080><c> well</c><00:02:23.350><c> okay</c><00:02:23.950><c> and</c><00:02:24.330><c> node</c><00:02:25.330><c> one</c><00:02:25.510><c> server</c>

00:02:25.830 --> 00:02:25.840 align:start position:0%
here as well okay and node one server
 

00:02:25.840 --> 00:02:28.199 align:start position:0%
here as well okay and node one server
okay<00:02:26.410><c> you</c><00:02:26.470><c> don't</c><00:02:26.709><c> need</c><00:02:26.890><c> the</c><00:02:27.010><c> J</c><00:02:27.190><c> s</c><00:02:27.370><c> and</c><00:02:27.640><c> the</c>

00:02:28.199 --> 00:02:28.209 align:start position:0%
okay you don't need the J s and the
 

00:02:28.209 --> 00:02:30.000 align:start position:0%
okay you don't need the J s and the
future<00:02:28.480><c> that</c><00:02:28.630><c> we're</c><00:02:28.780><c> building</c><00:02:28.900><c> currently</c><00:02:29.380><c> is</c>

00:02:30.000 --> 00:02:30.010 align:start position:0%
future that we're building currently is
 

00:02:30.010 --> 00:02:32.850 align:start position:0%
future that we're building currently is
this<00:02:31.000><c> one</c><00:02:31.239><c> over</c><00:02:31.630><c> here</c><00:02:31.989><c> which</c><00:02:32.140><c> is</c><00:02:32.200><c> the</c><00:02:32.380><c> theme</c>

00:02:32.850 --> 00:02:32.860 align:start position:0%
this one over here which is the theme
 

00:02:32.860 --> 00:02:34.830 align:start position:0%
this one over here which is the theme
section<00:02:33.310><c> okay</c><00:02:33.940><c> so</c><00:02:34.000><c> let's</c><00:02:34.270><c> say</c><00:02:34.360><c> I</c><00:02:34.420><c> click</c><00:02:34.510><c> on</c>

00:02:34.830 --> 00:02:34.840 align:start position:0%
section okay so let's say I click on
 

00:02:34.840 --> 00:02:38.130 align:start position:0%
section okay so let's say I click on
themes<00:02:35.260><c> here</c><00:02:35.500><c> I</c><00:02:36.000><c> noticed</c><00:02:37.000><c> that</c><00:02:37.209><c> and</c><00:02:37.420><c> there's</c>

00:02:38.130 --> 00:02:38.140 align:start position:0%
themes here I noticed that and there's
 

00:02:38.140 --> 00:02:40.380 align:start position:0%
themes here I noticed that and there's
something<00:02:38.530><c> like</c><00:02:38.770><c> twelve</c><00:02:39.580><c> different</c><00:02:39.940><c> boots</c>

00:02:40.380 --> 00:02:40.390 align:start position:0%
something like twelve different boots
 

00:02:40.390 --> 00:02:42.240 align:start position:0%
something like twelve different boots
watch<00:02:40.660><c> themes</c><00:02:41.200><c> I'm</c><00:02:41.620><c> not</c><00:02:41.770><c> sure</c><00:02:41.890><c> how</c><00:02:42.010><c> many</c><00:02:42.069><c> there</c>

00:02:42.240 --> 00:02:42.250 align:start position:0%
watch themes I'm not sure how many there
 

00:02:42.250 --> 00:02:46.830 align:start position:0%
watch themes I'm not sure how many there
are<00:02:42.400><c> here</c><00:02:42.850><c> one</c><00:02:43.330><c> two</c><00:02:43.900><c> three</c><00:02:44.590><c> four</c><00:02:45.280><c> five</c><00:02:45.840><c> five</c>

00:02:46.830 --> 00:02:46.840 align:start position:0%
are here one two three four five five
 

00:02:46.840 --> 00:02:48.630 align:start position:0%
are here one two three four five five
times<00:02:47.110><c> three</c><00:02:47.350><c> fifteen</c><00:02:47.709><c> sixteen</c><00:02:48.280><c> different</c>

00:02:48.630 --> 00:02:48.640 align:start position:0%
times three fifteen sixteen different
 

00:02:48.640 --> 00:02:50.610 align:start position:0%
times three fifteen sixteen different
themes<00:02:48.850><c> and</c><00:02:49.150><c> all</c><00:02:49.840><c> you</c><00:02:49.989><c> need</c><00:02:50.110><c> to</c><00:02:50.200><c> do</c><00:02:50.320><c> is</c><00:02:50.440><c> just</c>

00:02:50.610 --> 00:02:50.620 align:start position:0%
themes and all you need to do is just
 

00:02:50.620 --> 00:02:51.390 align:start position:0%
themes and all you need to do is just
click<00:02:50.890><c> on</c><00:02:51.070><c> it</c>

00:02:51.390 --> 00:02:51.400 align:start position:0%
click on it
 

00:02:51.400 --> 00:02:55.949 align:start position:0%
click on it
ah<00:02:52.680><c> what</c><00:02:53.680><c> happened</c><00:02:53.830><c> there</c><00:02:54.269><c> that's</c><00:02:55.269><c> not</c><00:02:55.540><c> good</c>

00:02:55.949 --> 00:02:55.959 align:start position:0%
ah what happened there that's not good
 

00:02:55.959 --> 00:02:59.100 align:start position:0%
ah what happened there that's not good
oh<00:02:56.880><c> no</c><00:02:57.880><c> no</c><00:02:58.030><c> it</c><00:02:58.150><c> changed</c><00:02:58.540><c> everything</c><00:02:58.840><c> I</c><00:02:59.050><c> think</c>

00:02:59.100 --> 00:02:59.110 align:start position:0%
oh no no it changed everything I think
 

00:02:59.110 --> 00:03:00.930 align:start position:0%
oh no no it changed everything I think
it's<00:02:59.380><c> good</c><00:02:59.650><c> because</c><00:02:59.860><c> it</c><00:03:00.130><c> changed</c><00:03:00.519><c> the</c><00:03:00.670><c> color</c>

00:03:00.930 --> 00:03:00.940 align:start position:0%
it's good because it changed the color
 

00:03:00.940 --> 00:03:03.000 align:start position:0%
it's good because it changed the color
as<00:03:01.180><c> you</c><00:03:01.360><c> can</c><00:03:01.540><c> notice</c><00:03:01.720><c> okay</c><00:03:02.319><c> I</c><00:03:02.350><c> changed</c><00:03:02.590><c> it</c><00:03:02.890><c> to</c>

00:03:03.000 --> 00:03:03.010 align:start position:0%
as you can notice okay I changed it to
 

00:03:03.010 --> 00:03:05.160 align:start position:0%
as you can notice okay I changed it to
white<00:03:03.220><c> I</c><00:03:03.489><c> go</c><00:03:04.150><c> back</c><00:03:04.330><c> to</c><00:03:04.510><c> the</c><00:03:04.600><c> home</c><00:03:04.780><c> and</c><00:03:04.989><c> it's</c>

00:03:05.160 --> 00:03:05.170 align:start position:0%
white I go back to the home and it's
 

00:03:05.170 --> 00:03:06.630 align:start position:0%
white I go back to the home and it's
still<00:03:05.440><c> white</c><00:03:05.709><c> so</c><00:03:05.980><c> i'm</c><00:03:06.190><c> gonna</c><00:03:06.370><c> click</c><00:03:06.580><c> a</c>

00:03:06.630 --> 00:03:06.640 align:start position:0%
still white so i'm gonna click a
 

00:03:06.640 --> 00:03:08.729 align:start position:0%
still white so i'm gonna click a
different<00:03:07.090><c> theme</c><00:03:07.269><c> cyborg</c><00:03:07.930><c> it's</c><00:03:08.350><c> gonna</c><00:03:08.590><c> make</c>

00:03:08.729 --> 00:03:08.739 align:start position:0%
different theme cyborg it's gonna make
 

00:03:08.739 --> 00:03:10.350 align:start position:0%
different theme cyborg it's gonna make
everything<00:03:08.950><c> black</c><00:03:09.250><c> there</c><00:03:09.880><c> we</c><00:03:10.000><c> go</c>

00:03:10.350 --> 00:03:10.360 align:start position:0%
everything black there we go
 

00:03:10.360 --> 00:03:12.240 align:start position:0%
everything black there we go
everything<00:03:11.019><c> is</c><00:03:11.140><c> black</c><00:03:11.350><c> i</c><00:03:11.560><c> click</c><00:03:11.830><c> to</c><00:03:11.980><c> the</c><00:03:12.069><c> home</c>

00:03:12.240 --> 00:03:12.250 align:start position:0%
everything is black i click to the home
 

00:03:12.250 --> 00:03:14.340 align:start position:0%
everything is black i click to the home
page<00:03:12.459><c> and</c><00:03:12.790><c> everything</c><00:03:13.120><c> is</c><00:03:13.330><c> still</c><00:03:13.540><c> black</c><00:03:13.840><c> black</c>

00:03:14.340 --> 00:03:14.350 align:start position:0%
page and everything is still black black
 

00:03:14.350 --> 00:03:16.199 align:start position:0%
page and everything is still black black
on<00:03:14.380><c> the</c><00:03:14.620><c> sideboard</c><00:03:15.069><c> theme</c><00:03:15.430><c> let's</c><00:03:16.030><c> say</c><00:03:16.180><c> i</c>

00:03:16.199 --> 00:03:16.209 align:start position:0%
on the sideboard theme let's say i
 

00:03:16.209 --> 00:03:22.020 align:start position:0%
on the sideboard theme let's say i
refresh<00:03:17.019><c> this</c><00:03:17.290><c> i</c><00:03:20.610><c> refresh</c><00:03:21.610><c> this</c><00:03:21.760><c> it's</c><00:03:21.940><c> gonna</c>

00:03:22.020 --> 00:03:22.030 align:start position:0%
refresh this i refresh this it's gonna
 

00:03:22.030 --> 00:03:23.819 align:start position:0%
refresh this i refresh this it's gonna
go<00:03:22.239><c> back</c><00:03:22.450><c> to</c><00:03:22.660><c> the</c><00:03:22.750><c> previous</c><00:03:23.019><c> theme</c><00:03:23.590><c> which</c><00:03:23.799><c> is</c>

00:03:23.819 --> 00:03:23.829 align:start position:0%
go back to the previous theme which is
 

00:03:23.829 --> 00:03:24.720 align:start position:0%
go back to the previous theme which is
darkly

00:03:24.720 --> 00:03:24.730 align:start position:0%
darkly
 

00:03:24.730 --> 00:03:28.080 align:start position:0%
darkly
okay<00:03:25.000><c> so</c><00:03:25.090><c> we're</c><00:03:25.329><c> away</c><00:03:25.600><c> from</c><00:03:26.700><c> away</c><00:03:27.700><c> from</c><00:03:27.940><c> that</c>

00:03:28.080 --> 00:03:28.090 align:start position:0%
okay so we're away from away from that
 

00:03:28.090 --> 00:03:30.000 align:start position:0%
okay so we're away from away from that
one<00:03:28.209><c> but</c><00:03:28.360><c> that's</c><00:03:28.480><c> of</c><00:03:28.840><c> course</c><00:03:29.140><c> this</c><00:03:29.739><c> is</c><00:03:29.890><c> gonna</c>

00:03:30.000 --> 00:03:30.010 align:start position:0%
one but that's of course this is gonna
 

00:03:30.010 --> 00:03:31.710 align:start position:0%
one but that's of course this is gonna
be<00:03:30.190><c> a</c><00:03:30.250><c> feature</c><00:03:30.489><c> both</c><00:03:30.760><c> for</c><00:03:31.000><c> the</c><00:03:31.090><c> logged</c><00:03:31.329><c> in</c><00:03:31.570><c> user</c>

00:03:31.710 --> 00:03:31.720 align:start position:0%
be a feature both for the logged in user
 

00:03:31.720 --> 00:03:36.870 align:start position:0%
be a feature both for the logged in user
and<00:03:32.709><c> also</c><00:03:33.310><c> before</c><00:03:34.620><c> for</c><00:03:35.620><c> visitors</c><00:03:36.100><c> but</c><00:03:36.370><c> mainly</c>

00:03:36.870 --> 00:03:36.880 align:start position:0%
and also before for visitors but mainly
 

00:03:36.880 --> 00:03:39.449 align:start position:0%
and also before for visitors but mainly
it's<00:03:37.180><c> for</c><00:03:37.299><c> the</c><00:03:37.630><c> logged</c><00:03:37.870><c> in</c><00:03:38.079><c> user</c><00:03:38.140><c> because</c><00:03:38.890><c> we</c>

00:03:39.449 --> 00:03:39.459 align:start position:0%
it's for the logged in user because we
 

00:03:39.459 --> 00:03:41.910 align:start position:0%
it's for the logged in user because we
have<00:03:39.640><c> a</c><00:03:39.670><c> database</c><00:03:40.299><c> and</c><00:03:40.720><c> this</c><00:03:41.440><c> is</c><00:03:41.590><c> how</c><00:03:41.680><c> my</c>

00:03:41.910 --> 00:03:41.920 align:start position:0%
have a database and this is how my
 

00:03:41.920 --> 00:03:44.430 align:start position:0%
have a database and this is how my
sequel<00:03:42.310><c> fits</c><00:03:42.549><c> into</c><00:03:42.880><c> this</c><00:03:43.030><c> new</c><00:03:43.299><c> feature</c><00:03:43.600><c> we</c>

00:03:44.430 --> 00:03:44.440 align:start position:0%
sequel fits into this new feature we
 

00:03:44.440 --> 00:03:46.410 align:start position:0%
sequel fits into this new feature we
have<00:03:44.470><c> a</c><00:03:44.650><c> database</c><00:03:45.070><c> that</c><00:03:45.250><c> is</c><00:03:45.910><c> gonna</c><00:03:46.090><c> add</c>

00:03:46.410 --> 00:03:46.420 align:start position:0%
have a database that is gonna add
 

00:03:46.420 --> 00:03:48.990 align:start position:0%
have a database that is gonna add
another<00:03:46.989><c> column</c><00:03:47.530><c> to</c><00:03:48.160><c> the</c><00:03:48.280><c> database</c><00:03:48.760><c> and</c>

00:03:48.990 --> 00:03:49.000 align:start position:0%
another column to the database and
 

00:03:49.000 --> 00:03:51.420 align:start position:0%
another column to the database and
that's<00:03:49.150><c> the</c><00:03:49.359><c> client</c><00:03:49.900><c> theme</c><00:03:50.290><c> okay</c><00:03:50.980><c> we</c><00:03:51.190><c> need</c><00:03:51.340><c> to</c>

00:03:51.420 --> 00:03:51.430 align:start position:0%
that's the client theme okay we need to
 

00:03:51.430 --> 00:03:54.120 align:start position:0%
that's the client theme okay we need to
add<00:03:51.549><c> that</c><00:03:51.820><c> to</c><00:03:51.880><c> the</c><00:03:52.150><c> database</c><00:03:52.600><c> and</c><00:03:52.840><c> we</c><00:03:53.799><c> can</c><00:03:53.950><c> do</c>

00:03:54.120 --> 00:03:54.130 align:start position:0%
add that to the database and we can do
 

00:03:54.130 --> 00:03:58.670 align:start position:0%
add that to the database and we can do
that<00:03:54.280><c> actually</c><00:03:55.060><c> through</c><00:03:56.130><c> this</c><00:03:57.130><c> specific</c>

00:03:58.670 --> 00:03:58.680 align:start position:0%
that actually through this specific
 

00:03:58.680 --> 00:04:01.979 align:start position:0%
that actually through this specific
query<00:03:59.680><c> over</c><00:03:59.890><c> here</c><00:04:00.250><c> sequel</c><00:04:00.670><c> alter</c><00:04:01.150><c> okay</c>

00:04:01.979 --> 00:04:01.989 align:start position:0%
query over here sequel alter okay
 

00:04:01.989 --> 00:04:04.710 align:start position:0%
query over here sequel alter okay
control<00:04:02.950><c> c</c><00:04:03.280><c> just</c><00:04:03.670><c> want</c><00:04:03.910><c> to</c><00:04:04.030><c> make</c><00:04:04.180><c> sure</c><00:04:04.209><c> i</c><00:04:04.420><c> have</c>

00:04:04.710 --> 00:04:04.720 align:start position:0%
control c just want to make sure i have
 

00:04:04.720 --> 00:04:08.490 align:start position:0%
control c just want to make sure i have
over<00:04:05.079><c> here</c><00:04:05.410><c> the</c><00:04:06.220><c> actual</c><00:04:06.670><c> table</c><00:04:07.510><c> what's</c><00:04:08.350><c> the</c>

00:04:08.490 --> 00:04:08.500 align:start position:0%
over here the actual table what's the
 

00:04:08.500 --> 00:04:10.259 align:start position:0%
over here the actual table what's the
name<00:04:08.680><c> of</c><00:04:08.709><c> it</c><00:04:08.890><c> it's</c><00:04:09.040><c> called</c><00:04:09.160><c> the</c><00:04:09.310><c> blog</c><00:04:09.519><c> post</c><00:04:09.880><c> of</c>

00:04:10.259 --> 00:04:10.269 align:start position:0%
name of it it's called the blog post of
 

00:04:10.269 --> 00:04:13.259 align:start position:0%
name of it it's called the blog post of
the<00:04:10.510><c> client</c><00:04:11.049><c> table</c><00:04:11.500><c> okay</c><00:04:11.980><c> i</c><00:04:12.160><c> click</c><00:04:13.000><c> on</c><00:04:13.150><c> the</c>

00:04:13.259 --> 00:04:13.269 align:start position:0%
the client table okay i click on the
 

00:04:13.269 --> 00:04:15.940 align:start position:0%
the client table okay i click on the
client<00:04:13.630><c> a</c><00:04:13.720><c> oh</c><00:04:13.750><c> this</c><00:04:13.959><c> is</c><00:04:14.109><c> a</c><00:04:14.400><c> app</c><00:04:15.400><c> called</c>

00:04:15.940 --> 00:04:15.950 align:start position:0%
client a oh this is a app called
 

00:04:15.950 --> 00:04:17.449 align:start position:0%
client a oh this is a app called
[Music]

00:04:17.449 --> 00:04:17.459 align:start position:0%
[Music]
 

00:04:17.459 --> 00:04:22.110 align:start position:0%
[Music]
create<00:04:18.459><c> code</c><00:04:18.850><c> database</c><00:04:20.400><c> there's</c><00:04:21.400><c> my</c><00:04:21.579><c> client</c><00:04:22.000><c> -</c>

00:04:22.110 --> 00:04:22.120 align:start position:0%
create code database there's my client -
 

00:04:22.120 --> 00:04:24.540 align:start position:0%
create code database there's my client -
here's<00:04:22.690><c> my</c><00:04:22.840><c> client</c><00:04:23.229><c> table</c><00:04:23.530><c> client</c><00:04:24.130><c> name</c><00:04:24.310><c> it</c>

00:04:24.540 --> 00:04:24.550 align:start position:0%
here's my client table client name it
 

00:04:24.550 --> 00:04:26.130 align:start position:0%
here's my client table client name it
doesn't<00:04:24.669><c> have</c><00:04:24.940><c> the</c><00:04:25.060><c> client</c><00:04:25.419><c> theme</c><00:04:25.630><c> as</c><00:04:25.840><c> you</c><00:04:25.870><c> can</c>

00:04:26.130 --> 00:04:26.140 align:start position:0%
doesn't have the client theme as you can
 

00:04:26.140 --> 00:04:27.960 align:start position:0%
doesn't have the client theme as you can
see<00:04:26.320><c> over</c><00:04:26.409><c> here</c><00:04:26.530><c> these</c><00:04:26.710><c> are</c><00:04:26.770><c> the</c><00:04:26.890><c> six</c>

00:04:27.960 --> 00:04:27.970 align:start position:0%
see over here these are the six
 

00:04:27.970 --> 00:04:30.330 align:start position:0%
see over here these are the six
six<00:04:28.720><c> current</c><00:04:29.050><c> columns</c><00:04:29.590><c> and</c><00:04:29.800><c> now</c><00:04:29.919><c> we're</c><00:04:30.190><c> gonna</c>

00:04:30.330 --> 00:04:30.340 align:start position:0%
six current columns and now we're gonna
 

00:04:30.340 --> 00:04:34.020 align:start position:0%
six current columns and now we're gonna
go<00:04:30.610><c> into</c><00:04:30.970><c> here</c><00:04:31.449><c> we're</c><00:04:32.410><c> gonna</c><00:04:32.530><c> get</c><00:04:32.740><c> into</c><00:04:33.030><c> this</c>

00:04:34.020 --> 00:04:34.030 align:start position:0%
go into here we're gonna get into this
 

00:04:34.030 --> 00:04:39.379 align:start position:0%
go into here we're gonna get into this
is<00:04:34.830><c> shortly</c><00:04:35.830><c> going</c><00:04:37.410><c> to</c><00:04:38.410><c> get</c><00:04:38.530><c> the</c><00:04:38.680><c> sequel</c><00:04:39.039><c> outer</c>

00:04:39.379 --> 00:04:39.389 align:start position:0%
is shortly going to get the sequel outer
 

00:04:39.389 --> 00:04:43.430 align:start position:0%
is shortly going to get the sequel outer
outer<00:04:40.410><c> column</c><00:04:41.410><c> table</c><00:04:41.620><c> and</c><00:04:42.490><c> we</c><00:04:42.759><c> want</c><00:04:42.940><c> to</c><00:04:43.000><c> do</c><00:04:43.150><c> ad</c>

00:04:43.430 --> 00:04:43.440 align:start position:0%
outer column table and we want to do ad
 

00:04:43.440 --> 00:04:47.969 align:start position:0%
outer column table and we want to do ad
alter<00:04:44.440><c> table</c><00:04:45.840><c> controls</c><00:04:46.840><c> T</c><00:04:47.139><c> so</c><00:04:47.470><c> let's</c><00:04:47.830><c> go</c><00:04:47.949><c> back</c>

00:04:47.969 --> 00:04:47.979 align:start position:0%
alter table controls T so let's go back
 

00:04:47.979 --> 00:04:50.309 align:start position:0%
alter table controls T so let's go back
in<00:04:48.370><c> here</c><00:04:48.580><c> we</c><00:04:49.180><c> got</c><00:04:49.330><c> ten</c><00:04:49.569><c> seconds</c>

00:04:50.309 --> 00:04:50.319 align:start position:0%
in here we got ten seconds
 

00:04:50.319 --> 00:04:52.140 align:start position:0%
in here we got ten seconds
we're<00:04:50.919><c> gonna</c><00:04:51.009><c> continue</c><00:04:51.280><c> in</c><00:04:51.580><c> the</c><00:04:51.639><c> next</c><00:04:51.880><c> video</c>

00:04:52.140 --> 00:04:52.150 align:start position:0%
we're gonna continue in the next video
 

00:04:52.150 --> 00:04:54.930 align:start position:0%
we're gonna continue in the next video
how<00:04:52.300><c> to</c><00:04:52.330><c> continue</c><00:04:52.900><c> but</c><00:04:53.110><c> I</c><00:04:53.259><c> had</c><00:04:53.470><c> to</c><00:04:53.620><c> out</c><00:04:54.220><c> at</c><00:04:54.610><c> this</c>

00:04:54.930 --> 00:04:54.940 align:start position:0%
how to continue but I had to out at this
 

00:04:54.940 --> 00:04:57.839 align:start position:0%
how to continue but I had to out at this
next<00:04:55.389><c> table</c><00:04:55.840><c> to</c><00:04:56.560><c> this</c><00:04:57.069><c> next</c><00:04:57.340><c> column</c><00:04:57.490><c> to</c><00:04:57.759><c> the</c>

00:04:57.839 --> 00:04:57.849 align:start position:0%
next table to this next column to the
 

00:04:57.849 --> 00:05:00.030 align:start position:0%
next table to this next column to the
table

