WEBVTT
Kind: captions
Language: en

00:00:00.960 --> 00:00:03.270 align:start position:0%
 
and<00:00:01.199><c> welcome</c><00:00:01.520><c> back</c><00:00:01.760><c> guys</c><00:00:02.320><c> uh</c><00:00:02.639><c> now</c><00:00:02.800><c> a</c><00:00:02.960><c> small</c>

00:00:03.270 --> 00:00:03.280 align:start position:0%
and welcome back guys uh now a small
 

00:00:03.280 --> 00:00:06.470 align:start position:0%
and welcome back guys uh now a small
little<00:00:03.600><c> amendment</c><00:00:04.080><c> to</c><00:00:04.240><c> the</c><00:00:04.319><c> last</c><00:00:04.880><c> uh</c><00:00:05.279><c> video</c><00:00:06.160><c> uh</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
little amendment to the last uh video uh
 

00:00:06.480 --> 00:00:09.589 align:start position:0%
little amendment to the last uh video uh
you<00:00:06.640><c> might</c><00:00:06.879><c> have</c><00:00:07.359><c> noticed</c><00:00:08.240><c> uh</c><00:00:08.639><c> that</c><00:00:08.960><c> uh</c><00:00:09.280><c> if</c><00:00:09.440><c> you</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
you might have noticed uh that uh if you
 

00:00:09.599 --> 00:00:11.669 align:start position:0%
you might have noticed uh that uh if you
open<00:00:09.840><c> up</c><00:00:09.920><c> your</c><00:00:10.160><c> developer</c><00:00:10.719><c> console</c><00:00:11.360><c> then</c><00:00:11.599><c> you</c>

00:00:11.669 --> 00:00:11.679 align:start position:0%
open up your developer console then you
 

00:00:11.679 --> 00:00:15.030 align:start position:0%
open up your developer console then you
would<00:00:11.840><c> have</c><00:00:12.080><c> an</c><00:00:12.320><c> error</c><00:00:12.719><c> there</c><00:00:13.519><c> um</c><00:00:14.240><c> regarding</c>

00:00:15.030 --> 00:00:15.040 align:start position:0%
would have an error there um regarding
 

00:00:15.040 --> 00:00:17.590 align:start position:0%
would have an error there um regarding
our<00:00:15.360><c> mixed</c><00:00:15.679><c> content</c><00:00:16.240><c> so</c><00:00:16.720><c> the</c><00:00:16.880><c> mixed</c><00:00:17.199><c> content</c>

00:00:17.590 --> 00:00:17.600 align:start position:0%
our mixed content so the mixed content
 

00:00:17.600 --> 00:00:20.310 align:start position:0%
our mixed content so the mixed content
era<00:00:17.920><c> did</c><00:00:18.080><c> not</c><00:00:18.320><c> fully</c><00:00:18.640><c> go</c><00:00:18.800><c> away</c><00:00:19.680><c> and</c><00:00:19.840><c> i</c><00:00:20.000><c> believe</c>

00:00:20.310 --> 00:00:20.320 align:start position:0%
era did not fully go away and i believe
 

00:00:20.320 --> 00:00:23.029 align:start position:0%
era did not fully go away and i believe
i<00:00:20.480><c> found</c><00:00:20.720><c> a</c><00:00:20.880><c> pretty</c><00:00:21.119><c> good</c><00:00:21.359><c> uh</c><00:00:21.920><c> fix</c><00:00:22.240><c> for</c><00:00:22.480><c> it</c><00:00:22.800><c> what</c>

00:00:23.029 --> 00:00:23.039 align:start position:0%
i found a pretty good uh fix for it what
 

00:00:23.039 --> 00:00:24.710 align:start position:0%
i found a pretty good uh fix for it what
i<00:00:23.199><c> did</c><00:00:23.519><c> was</c><00:00:23.840><c> um</c>

00:00:24.710 --> 00:00:24.720 align:start position:0%
i did was um
 

00:00:24.720 --> 00:00:27.429 align:start position:0%
i did was um
i<00:00:24.960><c> googled</c><00:00:25.599><c> the</c><00:00:25.760><c> mixed</c><00:00:26.080><c> content</c><00:00:26.640><c> error</c><00:00:27.119><c> and</c><00:00:27.359><c> i</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
i googled the mixed content error and i
 

00:00:27.439 --> 00:00:30.390 align:start position:0%
i googled the mixed content error and i
came<00:00:27.680><c> across</c><00:00:28.320><c> this</c><00:00:28.720><c> um</c><00:00:29.279><c> this</c><00:00:29.519><c> stack</c><00:00:29.840><c> overflow</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
came across this um this stack overflow
 

00:00:30.400 --> 00:00:31.349 align:start position:0%
came across this um this stack overflow
ticket

00:00:31.349 --> 00:00:31.359 align:start position:0%
ticket
 

00:00:31.359 --> 00:00:33.110 align:start position:0%
ticket
which<00:00:31.599><c> says</c><00:00:31.840><c> that</c><00:00:32.079><c> in</c><00:00:32.239><c> order</c><00:00:32.480><c> to</c><00:00:32.559><c> get</c><00:00:32.719><c> around</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
which says that in order to get around
 

00:00:33.120 --> 00:00:35.670 align:start position:0%
which says that in order to get around
that<00:00:33.440><c> that</c><00:00:33.680><c> uh</c><00:00:33.920><c> too</c><00:00:34.160><c> many</c><00:00:34.480><c> redirects</c><00:00:35.360><c> error</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
that that uh too many redirects error
 

00:00:35.680 --> 00:00:37.510 align:start position:0%
that that uh too many redirects error
that<00:00:35.840><c> we</c><00:00:36.239><c> that</c><00:00:36.559><c> uh</c>

00:00:37.510 --> 00:00:37.520 align:start position:0%
that we that uh
 

00:00:37.520 --> 00:00:38.310 align:start position:0%
that we that uh
that

00:00:38.310 --> 00:00:38.320 align:start position:0%
that
 

00:00:38.320 --> 00:00:41.110 align:start position:0%
that
setting<00:00:38.640><c> https</c><00:00:40.079><c> might</c><00:00:40.399><c> cause</c><00:00:40.719><c> if</c><00:00:40.879><c> you</c><00:00:40.960><c> set</c>

00:00:41.110 --> 00:00:41.120 align:start position:0%
setting https might cause if you set
 

00:00:41.120 --> 00:00:43.430 align:start position:0%
setting https might cause if you set
https<00:00:41.840><c> in</c><00:00:42.000><c> both</c><00:00:42.239><c> these</c><00:00:42.480><c> places</c><00:00:42.960><c> you</c><00:00:43.120><c> might</c><00:00:43.280><c> get</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
https in both these places you might get
 

00:00:43.440 --> 00:00:45.430 align:start position:0%
https in both these places you might get
an<00:00:43.680><c> error</c><00:00:43.920><c> that</c><00:00:44.079><c> says</c><00:00:44.399><c> redirect</c><00:00:44.960><c> it</c><00:00:45.039><c> too</c><00:00:45.200><c> many</c>

00:00:45.430 --> 00:00:45.440 align:start position:0%
an error that says redirect it too many
 

00:00:45.440 --> 00:00:47.590 align:start position:0%
an error that says redirect it too many
times<00:00:46.160><c> i</c><00:00:46.320><c> believe</c><00:00:46.559><c> this</c><00:00:46.800><c> is</c><00:00:46.960><c> the</c><00:00:47.039><c> best</c><00:00:47.280><c> way</c><00:00:47.440><c> to</c>

00:00:47.590 --> 00:00:47.600 align:start position:0%
times i believe this is the best way to
 

00:00:47.600 --> 00:00:50.470 align:start position:0%
times i believe this is the best way to
do<00:00:47.760><c> it</c><00:00:47.840><c> which</c><00:00:48.079><c> is</c><00:00:48.239><c> just</c><00:00:48.800><c> uh</c><00:00:49.120><c> go</c><00:00:49.280><c> ahead</c><00:00:49.600><c> and</c>

00:00:50.470 --> 00:00:50.480 align:start position:0%
do it which is just uh go ahead and
 

00:00:50.480 --> 00:00:51.670 align:start position:0%
do it which is just uh go ahead and
set<00:00:50.879><c> this</c>

00:00:51.670 --> 00:00:51.680 align:start position:0%
set this
 

00:00:51.680 --> 00:00:53.910 align:start position:0%
set this
add<00:00:51.920><c> these</c><00:00:52.160><c> lines</c><00:00:52.480><c> to</c><00:00:52.640><c> your</c><00:00:52.800><c> wp</c>

00:00:53.910 --> 00:00:53.920 align:start position:0%
add these lines to your wp
 

00:00:53.920 --> 00:00:57.029 align:start position:0%
add these lines to your wp
config<00:00:54.480><c> file</c><00:00:54.879><c> so</c><00:00:55.039><c> within</c><00:00:55.440><c> our</c><00:00:55.760><c> server</c><00:00:56.160><c> itself</c>

00:00:57.029 --> 00:00:57.039 align:start position:0%
config file so within our server itself
 

00:00:57.039 --> 00:00:59.510 align:start position:0%
config file so within our server itself
you're<00:00:57.280><c> going</c><00:00:57.360><c> to</c><00:00:57.520><c> go</c><00:00:58.000><c> into</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
you're going to go into
 

00:00:59.520 --> 00:01:02.389 align:start position:0%
you're going to go into
var<00:00:59.840><c> slash</c><00:01:00.239><c> www.html</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
var slash www.html
 

00:01:02.399 --> 00:01:05.910 align:start position:0%
var slash www.html
and<00:01:02.480><c> then</c><00:01:02.879><c> sudo</c><00:01:03.320><c> nanowp</c><00:01:04.799><c> config</c>

00:01:05.910 --> 00:01:05.920 align:start position:0%
and then sudo nanowp config
 

00:01:05.920 --> 00:01:08.390 align:start position:0%
and then sudo nanowp config
dot<00:01:06.240><c> php</c>

00:01:08.390 --> 00:01:08.400 align:start position:0%
dot php
 

00:01:08.400 --> 00:01:10.630 align:start position:0%
dot php
all<00:01:08.560><c> right</c><00:01:08.880><c> and</c><00:01:08.960><c> then</c><00:01:09.600><c> you</c><00:01:09.840><c> add</c><00:01:10.080><c> these</c><00:01:10.320><c> lines</c>

00:01:10.630 --> 00:01:10.640 align:start position:0%
all right and then you add these lines
 

00:01:10.640 --> 00:01:14.149 align:start position:0%
all right and then you add these lines
over<00:01:10.960><c> here</c><00:01:11.360><c> in</c><00:01:11.520><c> my</c><00:01:11.760><c> case</c><00:01:12.240><c> um</c><00:01:12.880><c> i</c><00:01:13.119><c> added</c><00:01:13.840><c> right</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
over here in my case um i added right
 

00:01:14.159 --> 00:01:16.550 align:start position:0%
over here in my case um i added right
king<00:01:14.520><c> commerce.biz</c><00:01:15.600><c> i</c><00:01:15.759><c> replaced</c>

00:01:16.550 --> 00:01:16.560 align:start position:0%
king commerce.biz i replaced
 

00:01:16.560 --> 00:01:18.190 align:start position:0%
king commerce.biz i replaced
yourdomain.com<00:01:17.600><c> over</c><00:01:17.840><c> here</c>

00:01:18.190 --> 00:01:18.200 align:start position:0%
yourdomain.com over here
 

00:01:18.200 --> 00:01:20.310 align:start position:0%
yourdomain.com over here
kingcommerce.biz<00:01:19.280><c> and</c><00:01:19.520><c> then</c><00:01:19.759><c> i</c><00:01:19.840><c> added</c><00:01:20.080><c> that</c>

00:01:20.310 --> 00:01:20.320 align:start position:0%
kingcommerce.biz and then i added that
 

00:01:20.320 --> 00:01:23.030 align:start position:0%
kingcommerce.biz and then i added that
line<00:01:20.640><c> as</c><00:01:20.799><c> well</c><00:01:21.520><c> and</c><00:01:21.759><c> then</c><00:01:22.000><c> i</c><00:01:22.080><c> just</c><00:01:22.320><c> press</c><00:01:22.640><c> save</c>

00:01:23.030 --> 00:01:23.040 align:start position:0%
line as well and then i just press save
 

00:01:23.040 --> 00:01:24.789 align:start position:0%
line as well and then i just press save
and<00:01:23.360><c> everything</c><00:01:23.840><c> was</c><00:01:24.080><c> good</c><00:01:24.240><c> to</c><00:01:24.400><c> go</c><00:01:24.560><c> and</c><00:01:24.720><c> it</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
and everything was good to go and it
 

00:01:24.799 --> 00:01:26.630 align:start position:0%
and everything was good to go and it
started<00:01:25.200><c> working</c><00:01:25.600><c> again</c><00:01:25.920><c> so</c><00:01:26.080><c> that's</c><00:01:26.320><c> very</c>

00:01:26.630 --> 00:01:26.640 align:start position:0%
started working again so that's very
 

00:01:26.640 --> 00:01:28.310 align:start position:0%
started working again so that's very
very<00:01:26.880><c> important</c><00:01:27.360><c> all</c><00:01:27.520><c> those</c><00:01:27.759><c> notes</c><00:01:28.000><c> will</c><00:01:28.159><c> be</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
very important all those notes will be
 

00:01:28.320 --> 00:01:30.789 align:start position:0%
very important all those notes will be
included<00:01:29.119><c> within</c><00:01:29.439><c> the</c><00:01:29.600><c> description</c><00:01:30.159><c> as</c><00:01:30.320><c> well</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
included within the description as well
 

00:01:30.799 --> 00:01:32.950 align:start position:0%
included within the description as well
let's<00:01:30.960><c> say</c><00:01:31.119><c> i</c><00:01:31.280><c> go</c><00:01:31.439><c> back</c><00:01:31.920><c> into</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
let's say i go back into
 

00:01:32.960 --> 00:01:36.789 align:start position:0%
let's say i go back into
my<00:01:33.360><c> uh</c><00:01:33.840><c> my</c><00:01:34.079><c> dashboard</c><00:01:35.119><c> um</c><00:01:36.079><c> and</c><00:01:36.240><c> i</c><00:01:36.400><c> try</c><00:01:36.560><c> to</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
my uh my dashboard um and i try to
 

00:01:36.799 --> 00:01:40.390 align:start position:0%
my uh my dashboard um and i try to
import<00:01:37.200><c> demo</c><00:01:37.600><c> data</c><00:01:37.920><c> now</c><00:01:38.400><c> i'm</c><00:01:38.560><c> gonna</c><00:01:39.040><c> uh</c><00:01:40.000><c> just</c>

00:01:40.390 --> 00:01:40.400 align:start position:0%
import demo data now i'm gonna uh just
 

00:01:40.400 --> 00:01:42.789 align:start position:0%
import demo data now i'm gonna uh just
verify<00:01:40.880><c> that</c><00:01:41.040><c> this</c><00:01:41.280><c> actually</c><00:01:41.840><c> works</c><00:01:42.320><c> okay</c>

00:01:42.789 --> 00:01:42.799 align:start position:0%
verify that this actually works okay
 

00:01:42.799 --> 00:01:44.950 align:start position:0%
verify that this actually works okay
import<00:01:43.119><c> for</c><00:01:43.360><c> elementor</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
import for elementor
 

00:01:44.960 --> 00:01:48.389 align:start position:0%
import for elementor
okay<00:01:45.360><c> yeah</c><00:01:45.680><c> that</c><00:01:46.000><c> is</c><00:01:46.720><c> and</c><00:01:47.360><c> the</c><00:01:47.520><c> reason</c><00:01:47.759><c> why</c><00:01:48.000><c> i</c>

00:01:48.389 --> 00:01:48.399 align:start position:0%
okay yeah that is and the reason why i
 

00:01:48.399 --> 00:01:50.630 align:start position:0%
okay yeah that is and the reason why i
stumbled<00:01:48.799><c> back</c><00:01:49.040><c> upon</c><00:01:49.360><c> that</c><00:01:49.600><c> is</c><00:01:49.680><c> because</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
stumbled back upon that is because
 

00:01:50.640 --> 00:01:52.069 align:start position:0%
stumbled back upon that is because
when<00:01:50.799><c> i</c><00:01:50.880><c> was</c><00:01:51.040><c> trying</c><00:01:51.280><c> to</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
when i was trying to
 

00:01:52.079 --> 00:01:54.630 align:start position:0%
when i was trying to
add<00:01:52.399><c> demo</c><00:01:52.799><c> data</c><00:01:53.200><c> for</c><00:01:53.360><c> this</c><00:01:53.600><c> new</c><00:01:53.920><c> theme</c><00:01:54.479><c> i</c>

00:01:54.630 --> 00:01:54.640 align:start position:0%
add demo data for this new theme i
 

00:01:54.640 --> 00:01:56.950 align:start position:0%
add demo data for this new theme i
wasn't<00:01:54.960><c> able</c><00:01:55.200><c> to</c><00:01:55.280><c> do</c><00:01:55.439><c> it</c><00:01:55.600><c> it</c><00:01:55.680><c> would</c><00:01:55.920><c> crash</c><00:01:56.560><c> so</c>

00:01:56.950 --> 00:01:56.960 align:start position:0%
wasn't able to do it it would crash so
 

00:01:56.960 --> 00:02:00.149 align:start position:0%
wasn't able to do it it would crash so
it's<00:01:57.119><c> very</c><00:01:57.360><c> important</c><00:01:57.680><c> to</c><00:01:57.840><c> have</c><00:01:58.479><c> https</c><00:01:59.439><c> across</c>

00:02:00.149 --> 00:02:00.159 align:start position:0%
it's very important to have https across
 

00:02:00.159 --> 00:02:02.389 align:start position:0%
it's very important to have https across
all<00:02:00.320><c> the</c><00:02:00.479><c> different</c><00:02:00.799><c> parts</c><00:02:01.119><c> of</c><00:02:01.280><c> your</c><00:02:01.520><c> site</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
all the different parts of your site
 

00:02:02.399 --> 00:02:04.069 align:start position:0%
all the different parts of your site
um

00:02:04.069 --> 00:02:04.079 align:start position:0%
um
 

00:02:04.079 --> 00:02:06.069 align:start position:0%
um
and<00:02:04.320><c> yeah</c><00:02:04.560><c> that's</c><00:02:04.960><c> that's</c><00:02:05.280><c> uh</c><00:02:05.680><c> that's</c><00:02:05.920><c> pretty</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
and yeah that's that's uh that's pretty
 

00:02:06.079 --> 00:02:09.190 align:start position:0%
and yeah that's that's uh that's pretty
much<00:02:06.240><c> how</c><00:02:06.399><c> we</c><00:02:06.560><c> do</c><00:02:06.719><c> it</c><00:02:06.960><c> all</c><00:02:07.200><c> the</c><00:02:08.000><c> uh</c><00:02:08.560><c> the</c><00:02:08.720><c> data</c><00:02:09.039><c> is</c>

00:02:09.190 --> 00:02:09.200 align:start position:0%
much how we do it all the uh the data is
 

00:02:09.200 --> 00:02:11.510 align:start position:0%
much how we do it all the uh the data is
going<00:02:09.280><c> to</c><00:02:09.360><c> be</c><00:02:09.759><c> in</c><00:02:10.000><c> the</c><00:02:10.239><c> uh</c><00:02:10.720><c> in</c><00:02:10.879><c> the</c><00:02:10.959><c> description</c>

00:02:11.510 --> 00:02:11.520 align:start position:0%
going to be in the uh in the description
 

00:02:11.520 --> 00:02:13.750 align:start position:0%
going to be in the uh in the description
of<00:02:11.599><c> the</c><00:02:11.760><c> video</c><00:02:12.160><c> so</c><00:02:12.400><c> thanks</c><00:02:12.720><c> guys</c><00:02:13.040><c> and</c><00:02:13.520><c> hope</c>

00:02:13.750 --> 00:02:13.760 align:start position:0%
of the video so thanks guys and hope
 

00:02:13.760 --> 00:02:15.430 align:start position:0%
of the video so thanks guys and hope
that<00:02:13.920><c> helps</c><00:02:14.160><c> you</c><00:02:14.319><c> out</c>

00:02:15.430 --> 00:02:15.440 align:start position:0%
that helps you out
 

00:02:15.440 --> 00:02:17.430 align:start position:0%
that helps you out
um<00:02:16.319><c> let's</c><00:02:16.480><c> just</c><00:02:16.640><c> double</c><00:02:16.959><c> check</c><00:02:17.200><c> that</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
um let's just double check that
 

00:02:17.440 --> 00:02:19.510 align:start position:0%
um let's just double check that
everything<00:02:18.319><c> actually</c><00:02:18.720><c> was</c><00:02:18.959><c> imported</c>

00:02:19.510 --> 00:02:19.520 align:start position:0%
everything actually was imported
 

00:02:19.520 --> 00:02:22.790 align:start position:0%
everything actually was imported
successfully<00:02:20.319><c> if</c><00:02:20.480><c> i</c><00:02:20.640><c> go</c><00:02:20.879><c> into</c><00:02:22.000><c> my</c><00:02:22.160><c> home</c><00:02:22.480><c> page</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
successfully if i go into my home page
 

00:02:22.800 --> 00:02:24.070 align:start position:0%
successfully if i go into my home page
now

00:02:24.070 --> 00:02:24.080 align:start position:0%
now
 

00:02:24.080 --> 00:02:25.030 align:start position:0%
now
ah

00:02:25.030 --> 00:02:25.040 align:start position:0%
ah
 

00:02:25.040 --> 00:02:27.510 align:start position:0%
ah
okay<00:02:25.599><c> did</c><00:02:25.760><c> the</c><00:02:25.920><c> demo</c><00:02:26.239><c> data</c><00:02:26.560><c> come</c><00:02:26.800><c> in</c><00:02:27.040><c> hopefully</c>

00:02:27.510 --> 00:02:27.520 align:start position:0%
okay did the demo data come in hopefully
 

00:02:27.520 --> 00:02:29.910 align:start position:0%
okay did the demo data come in hopefully
it<00:02:27.680><c> did</c><00:02:28.160><c> all</c><00:02:28.319><c> right</c><00:02:28.560><c> guys</c><00:02:29.200><c> so</c><00:02:29.360><c> that's</c><00:02:29.599><c> that</c><00:02:29.840><c> and</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
it did all right guys so that's that and
 

00:02:29.920 --> 00:02:32.959 align:start position:0%
it did all right guys so that's that and
i'll<00:02:30.080><c> see</c><00:02:30.239><c> you</c><00:02:30.319><c> in</c><00:02:30.400><c> the</c><00:02:30.480><c> next</c><00:02:30.640><c> one</c>

