WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.690 align:start position:0%
 
hey<00:00:00.599><c> everyone</c><00:00:00.780><c> I</c><00:00:01.740><c> want</c><00:00:01.800><c> to</c><00:00:01.979><c> talk</c><00:00:02.040><c> about</c><00:00:02.220><c> GitHub</c>

00:00:02.690 --> 00:00:02.700 align:start position:0%
hey everyone I want to talk about GitHub
 

00:00:02.700 --> 00:00:05.450 align:start position:0%
hey everyone I want to talk about GitHub
co-pilot<00:00:03.360><c> chat</c><00:00:03.659><c> and</c><00:00:04.319><c> more</c><00:00:04.500><c> specifically</c><00:00:04.920><c> I</c>

00:00:05.450 --> 00:00:05.460 align:start position:0%
co-pilot chat and more specifically I
 

00:00:05.460 --> 00:00:07.010 align:start position:0%
co-pilot chat and more specifically I
want<00:00:05.580><c> to</c><00:00:05.700><c> talk</c><00:00:05.819><c> about</c><00:00:05.940><c> how</c><00:00:06.240><c> we</c><00:00:06.359><c> can</c><00:00:06.480><c> use</c><00:00:06.600><c> GitHub</c>

00:00:07.010 --> 00:00:07.020 align:start position:0%
want to talk about how we can use GitHub
 

00:00:07.020 --> 00:00:09.290 align:start position:0%
want to talk about how we can use GitHub
copilot<00:00:07.560><c> chat</c><00:00:07.859><c> and</c><00:00:08.400><c> infrastructure</c><00:00:09.000><c> as</c><00:00:09.179><c> code</c>

00:00:09.290 --> 00:00:09.300 align:start position:0%
copilot chat and infrastructure as code
 

00:00:09.300 --> 00:00:11.390 align:start position:0%
copilot chat and infrastructure as code
So<00:00:10.200><c> today</c><00:00:10.440><c> we're</c><00:00:10.800><c> going</c><00:00:10.920><c> to</c><00:00:10.980><c> look</c><00:00:11.040><c> at</c><00:00:11.219><c> some</c>

00:00:11.390 --> 00:00:11.400 align:start position:0%
So today we're going to look at some
 

00:00:11.400 --> 00:00:13.190 align:start position:0%
So today we're going to look at some
existing<00:00:11.700><c> terraform</c><00:00:12.179><c> code</c><00:00:12.480><c> and</c><00:00:12.719><c> how</c><00:00:12.840><c> GitHub</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
existing terraform code and how GitHub
 

00:00:13.200 --> 00:00:15.530 align:start position:0%
existing terraform code and how GitHub
copilot<00:00:13.799><c> chat</c><00:00:14.040><c> can</c><00:00:14.340><c> tell</c><00:00:14.880><c> us</c><00:00:15.000><c> what</c><00:00:15.240><c> it's</c><00:00:15.360><c> doing</c>

00:00:15.530 --> 00:00:15.540 align:start position:0%
copilot chat can tell us what it's doing
 

00:00:15.540 --> 00:00:17.510 align:start position:0%
copilot chat can tell us what it's doing
because<00:00:15.780><c> when</c><00:00:16.020><c> we</c><00:00:16.139><c> start</c><00:00:16.379><c> any</c><00:00:16.740><c> project</c><00:00:16.980><c> we</c>

00:00:17.510 --> 00:00:17.520 align:start position:0%
because when we start any project we
 

00:00:17.520 --> 00:00:18.950 align:start position:0%
because when we start any project we
kind<00:00:17.699><c> of</c><00:00:17.760><c> need</c><00:00:17.940><c> a</c><00:00:18.119><c> starting</c><00:00:18.240><c> point</c><00:00:18.539><c> and</c><00:00:18.840><c> to</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
kind of need a starting point and to
 

00:00:18.960 --> 00:00:20.750 align:start position:0%
kind of need a starting point and to
know<00:00:19.080><c> what</c><00:00:19.680><c> our</c><00:00:19.800><c> code</c><00:00:19.980><c> is</c><00:00:20.160><c> doing</c><00:00:20.340><c> or</c><00:00:20.640><c> maybe</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
know what our code is doing or maybe
 

00:00:20.760 --> 00:00:22.429 align:start position:0%
know what our code is doing or maybe
we're<00:00:21.000><c> working</c><00:00:21.240><c> with</c><00:00:21.420><c> a</c><00:00:21.539><c> new</c><00:00:21.660><c> language</c><00:00:21.900><c> and</c>

00:00:22.429 --> 00:00:22.439 align:start position:0%
we're working with a new language and
 

00:00:22.439 --> 00:00:24.230 align:start position:0%
we're working with a new language and
we're<00:00:22.619><c> not</c><00:00:22.800><c> as</c><00:00:22.920><c> familiar</c><00:00:23.340><c> with</c><00:00:23.520><c> it</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
we're not as familiar with it
 

00:00:24.240 --> 00:00:26.210 align:start position:0%
we're not as familiar with it
now<00:00:24.660><c> GitHub</c><00:00:25.019><c> co-pilot</c><00:00:25.500><c> chat</c><00:00:25.740><c> is</c><00:00:25.920><c> also</c><00:00:26.160><c> going</c>

00:00:26.210 --> 00:00:26.220 align:start position:0%
now GitHub co-pilot chat is also going
 

00:00:26.220 --> 00:00:27.950 align:start position:0%
now GitHub co-pilot chat is also going
to<00:00:26.279><c> help</c><00:00:26.400><c> us</c><00:00:26.519><c> write</c><00:00:26.699><c> some</c><00:00:26.939><c> code</c><00:00:27.119><c> today</c><00:00:27.359><c> so</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
to help us write some code today so
 

00:00:27.960 --> 00:00:30.290 align:start position:0%
to help us write some code today so
let's<00:00:28.199><c> dig</c><00:00:28.439><c> in</c><00:00:28.680><c> and</c><00:00:28.980><c> get</c><00:00:29.220><c> started</c><00:00:29.580><c> I'm</c><00:00:30.119><c> going</c>

00:00:30.290 --> 00:00:30.300 align:start position:0%
let's dig in and get started I'm going
 

00:00:30.300 --> 00:00:32.330 align:start position:0%
let's dig in and get started I'm going
to<00:00:30.359><c> go</c><00:00:30.480><c> ahead</c><00:00:30.660><c> and</c><00:00:30.779><c> open</c><00:00:30.960><c> up</c><00:00:31.199><c> my</c><00:00:31.439><c> IDE</c><00:00:31.560><c> for</c><00:00:32.220><c> me</c>

00:00:32.330 --> 00:00:32.340 align:start position:0%
to go ahead and open up my IDE for me
 

00:00:32.340 --> 00:00:34.130 align:start position:0%
to go ahead and open up my IDE for me
this<00:00:32.520><c> is</c><00:00:32.640><c> GitHub</c><00:00:32.940><c> code</c><00:00:33.180><c> spaces</c><00:00:33.660><c> but</c><00:00:33.840><c> for</c><00:00:33.960><c> many</c>

00:00:34.130 --> 00:00:34.140 align:start position:0%
this is GitHub code spaces but for many
 

00:00:34.140 --> 00:00:35.750 align:start position:0%
this is GitHub code spaces but for many
of<00:00:34.260><c> you</c><00:00:34.380><c> this</c><00:00:34.620><c> will</c><00:00:34.739><c> be</c><00:00:34.860><c> Visual</c><00:00:35.160><c> Studio</c><00:00:35.340><c> code</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
of you this will be Visual Studio code
 

00:00:35.760 --> 00:00:37.670 align:start position:0%
of you this will be Visual Studio code
on<00:00:36.480><c> the</c><00:00:36.540><c> left</c><00:00:36.660><c> hand</c><00:00:36.840><c> side</c><00:00:37.020><c> I'm</c><00:00:37.320><c> going</c><00:00:37.440><c> to</c><00:00:37.500><c> click</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
on the left hand side I'm going to click
 

00:00:37.680 --> 00:00:40.130 align:start position:0%
on the left hand side I'm going to click
on<00:00:37.800><c> the</c><00:00:37.920><c> extension</c><00:00:38.219><c> for</c><00:00:38.399><c> GitHub</c><00:00:38.700><c> copilot</c><00:00:39.300><c> chat</c>

00:00:40.130 --> 00:00:40.140 align:start position:0%
on the extension for GitHub copilot chat
 

00:00:40.140 --> 00:00:42.049 align:start position:0%
on the extension for GitHub copilot chat
when<00:00:40.620><c> I</c><00:00:40.739><c> click</c><00:00:40.920><c> on</c><00:00:41.040><c> that</c><00:00:41.160><c> I'm</c><00:00:41.640><c> going</c><00:00:41.760><c> to</c><00:00:41.879><c> ask</c>

00:00:42.049 --> 00:00:42.059 align:start position:0%
when I click on that I'm going to ask
 

00:00:42.059 --> 00:00:44.090 align:start position:0%
when I click on that I'm going to ask
GitHub<00:00:42.480><c> copilot</c><00:00:43.079><c> chat</c><00:00:43.379><c> to</c><00:00:43.500><c> do</c><00:00:43.620><c> something</c><00:00:43.800><c> for</c>

00:00:44.090 --> 00:00:44.100 align:start position:0%
GitHub copilot chat to do something for
 

00:00:44.100 --> 00:00:47.030 align:start position:0%
GitHub copilot chat to do something for
me<00:00:44.760><c> first</c><00:00:45.180><c> thing</c><00:00:45.360><c> is</c><00:00:45.540><c> I'm</c><00:00:46.020><c> going</c><00:00:46.200><c> to</c><00:00:46.260><c> ask</c><00:00:46.440><c> it</c><00:00:46.680><c> to</c>

00:00:47.030 --> 00:00:47.040 align:start position:0%
me first thing is I'm going to ask it to
 

00:00:47.040 --> 00:00:48.770 align:start position:0%
me first thing is I'm going to ask it to
write<00:00:47.280><c> us</c><00:00:47.460><c> a</c><00:00:47.640><c> deployment</c><00:00:47.940><c> script</c><00:00:48.239><c> for</c><00:00:48.600><c> an</c>

00:00:48.770 --> 00:00:48.780 align:start position:0%
write us a deployment script for an
 

00:00:48.780 --> 00:00:51.170 align:start position:0%
write us a deployment script for an
Azure<00:00:49.140><c> web</c><00:00:49.620><c> app</c><00:00:49.800><c> service</c><00:00:50.160><c> to</c><00:00:50.520><c> go</c><00:00:50.640><c> into</c><00:00:50.760><c> Azure</c>

00:00:51.170 --> 00:00:51.180 align:start position:0%
Azure web app service to go into Azure
 

00:00:51.180 --> 00:00:53.930 align:start position:0%
Azure web app service to go into Azure
with<00:00:51.539><c> deployment</c><00:00:51.960><c> slots</c><00:00:52.800><c> once</c><00:00:53.460><c> it</c><00:00:53.579><c> gives</c><00:00:53.760><c> us</c>

00:00:53.930 --> 00:00:53.940 align:start position:0%
with deployment slots once it gives us
 

00:00:53.940 --> 00:00:55.369 align:start position:0%
with deployment slots once it gives us
this<00:00:54.180><c> deployment</c><00:00:54.539><c> script</c><00:00:54.780><c> I</c><00:00:55.079><c> have</c><00:00:55.199><c> a</c><00:00:55.260><c> couple</c>

00:00:55.369 --> 00:00:55.379 align:start position:0%
this deployment script I have a couple
 

00:00:55.379 --> 00:00:57.770 align:start position:0%
this deployment script I have a couple
options<00:00:55.680><c> I</c><00:00:56.100><c> can</c><00:00:56.160><c> select</c><00:00:56.520><c> to</c><00:00:56.820><c> copy</c><00:00:57.239><c> this</c><00:00:57.480><c> script</c>

00:00:57.770 --> 00:00:57.780 align:start position:0%
options I can select to copy this script
 

00:00:57.780 --> 00:01:00.529 align:start position:0%
options I can select to copy this script
over<00:00:58.020><c> or</c><00:00:58.980><c> I</c><00:00:59.399><c> have</c><00:00:59.520><c> this</c><00:00:59.760><c> other</c><00:00:59.879><c> option</c><00:01:00.300><c> here</c>

00:01:00.529 --> 00:01:00.539 align:start position:0%
over or I have this other option here
 

00:01:00.539 --> 00:01:02.569 align:start position:0%
over or I have this other option here
that<00:01:00.960><c> deploys</c><00:01:01.320><c> it</c><00:01:01.500><c> to</c><00:01:01.620><c> where</c><00:01:01.739><c> my</c><00:01:01.980><c> cursor</c><00:01:02.219><c> is</c><00:01:02.399><c> in</c>

00:01:02.569 --> 00:01:02.579 align:start position:0%
that deploys it to where my cursor is in
 

00:01:02.579 --> 00:01:04.430 align:start position:0%
that deploys it to where my cursor is in
the<00:01:02.699><c> blank</c><00:01:02.940><c> file</c><00:01:03.239><c> and</c><00:01:03.480><c> when</c><00:01:03.600><c> I</c><00:01:03.719><c> select</c><00:01:04.019><c> that</c><00:01:04.140><c> it</c>

00:01:04.430 --> 00:01:04.440 align:start position:0%
the blank file and when I select that it
 

00:01:04.440 --> 00:01:06.350 align:start position:0%
the blank file and when I select that it
moves<00:01:04.739><c> all</c><00:01:04.860><c> that</c><00:01:05.040><c> selected</c><00:01:05.460><c> code</c><00:01:05.700><c> over</c><00:01:06.060><c> I</c>

00:01:06.350 --> 00:01:06.360 align:start position:0%
moves all that selected code over I
 

00:01:06.360 --> 00:01:07.550 align:start position:0%
moves all that selected code over I
wanted<00:01:06.540><c> to</c><00:01:06.720><c> make</c><00:01:06.840><c> some</c><00:01:06.960><c> changes</c><00:01:07.260><c> because</c><00:01:07.380><c> the</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
wanted to make some changes because the
 

00:01:07.560 --> 00:01:09.830 align:start position:0%
wanted to make some changes because the
script<00:01:07.799><c> wasn't</c><00:01:08.040><c> 100</c><00:01:08.400><c> to</c><00:01:08.880><c> how</c><00:01:09.060><c> I</c><00:01:09.180><c> wanted</c><00:01:09.299><c> it</c><00:01:09.540><c> I</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
script wasn't 100 to how I wanted it I
 

00:01:09.840 --> 00:01:11.750 align:start position:0%
script wasn't 100 to how I wanted it I
want<00:01:09.960><c> to</c><00:01:10.080><c> change</c><00:01:10.260><c> the</c><00:01:10.439><c> tier</c><00:01:10.740><c> to</c><00:01:10.920><c> a</c><00:01:11.100><c> basic</c><00:01:11.400><c> tier</c>

00:01:11.750 --> 00:01:11.760 align:start position:0%
want to change the tier to a basic tier
 

00:01:11.760 --> 00:01:14.090 align:start position:0%
want to change the tier to a basic tier
it<00:01:12.540><c> will</c><00:01:12.659><c> go</c><00:01:12.780><c> ahead</c><00:01:12.900><c> and</c><00:01:13.020><c> do</c><00:01:13.200><c> that</c><00:01:13.380><c> I'll</c><00:01:13.740><c> select</c>

00:01:14.090 --> 00:01:14.100 align:start position:0%
it will go ahead and do that I'll select
 

00:01:14.100 --> 00:01:16.070 align:start position:0%
it will go ahead and do that I'll select
the<00:01:14.340><c> exact</c><00:01:14.700><c> block</c><00:01:15.119><c> of</c><00:01:15.299><c> code</c><00:01:15.479><c> that</c><00:01:15.720><c> I</c><00:01:15.840><c> want</c><00:01:15.960><c> to</c>

00:01:16.070 --> 00:01:16.080 align:start position:0%
the exact block of code that I want to
 

00:01:16.080 --> 00:01:18.530 align:start position:0%
the exact block of code that I want to
change<00:01:16.200><c> and</c><00:01:16.680><c> copy</c><00:01:16.979><c> that</c><00:01:17.159><c> over</c><00:01:17.280><c> next</c><00:01:17.880><c> up</c><00:01:18.060><c> I'm</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
change and copy that over next up I'm
 

00:01:18.540 --> 00:01:19.609 align:start position:0%
change and copy that over next up I'm
going<00:01:18.600><c> to</c><00:01:18.659><c> ask</c><00:01:18.780><c> it</c><00:01:18.960><c> to</c><00:01:19.140><c> add</c><00:01:19.320><c> some</c><00:01:19.439><c> code</c>

00:01:19.609 --> 00:01:19.619 align:start position:0%
going to ask it to add some code
 

00:01:19.619 --> 00:01:21.770 align:start position:0%
going to ask it to add some code
comments<00:01:20.159><c> into</c><00:01:20.280><c> our</c><00:01:20.460><c> code</c><00:01:20.700><c> because</c><00:01:21.299><c> actually</c>

00:01:21.770 --> 00:01:21.780 align:start position:0%
comments into our code because actually
 

00:01:21.780 --> 00:01:23.990 align:start position:0%
comments into our code because actually
I<00:01:22.200><c> want</c><00:01:22.320><c> the</c><00:01:22.500><c> next</c><00:01:22.680><c> person</c><00:01:22.860><c> to</c><00:01:23.100><c> understand</c><00:01:23.280><c> why</c>

00:01:23.990 --> 00:01:24.000 align:start position:0%
I want the next person to understand why
 

00:01:24.000 --> 00:01:25.969 align:start position:0%
I want the next person to understand why
the<00:01:24.180><c> script</c><00:01:24.420><c> is</c><00:01:24.720><c> doing</c><00:01:24.900><c> what</c><00:01:25.140><c> it's</c><00:01:25.259><c> doing</c><00:01:25.500><c> and</c>

00:01:25.969 --> 00:01:25.979 align:start position:0%
the script is doing what it's doing and
 

00:01:25.979 --> 00:01:28.190 align:start position:0%
the script is doing what it's doing and
also<00:01:26.220><c> help</c><00:01:26.400><c> them</c><00:01:26.640><c> navigate</c><00:01:26.880><c> it</c><00:01:27.240><c> so</c><00:01:27.900><c> I'm</c><00:01:28.080><c> going</c>

00:01:28.190 --> 00:01:28.200 align:start position:0%
also help them navigate it so I'm going
 

00:01:28.200 --> 00:01:30.350 align:start position:0%
also help them navigate it so I'm going
to<00:01:28.259><c> go</c><00:01:28.380><c> ahead</c><00:01:28.500><c> and</c><00:01:28.619><c> copy</c><00:01:28.979><c> that</c><00:01:29.220><c> code</c><00:01:29.460><c> into</c><00:01:30.180><c> our</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
to go ahead and copy that code into our
 

00:01:30.360 --> 00:01:35.870 align:start position:0%
to go ahead and copy that code into our
terraform<00:01:30.720><c> script</c><00:01:31.140><c> file</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
 
 

00:01:35.880 --> 00:01:37.850 align:start position:0%
 
now<00:01:36.420><c> now</c><00:01:36.540><c> that</c><00:01:36.720><c> we</c><00:01:36.900><c> have</c><00:01:37.020><c> everything</c><00:01:37.320><c> where</c><00:01:37.680><c> we</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
now now that we have everything where we
 

00:01:37.860 --> 00:01:39.710 align:start position:0%
now now that we have everything where we
want<00:01:37.979><c> it</c><00:01:38.100><c> to</c><00:01:38.220><c> be</c><00:01:38.340><c> in</c><00:01:38.640><c> our</c><00:01:38.759><c> terraform</c><00:01:39.119><c> script</c><00:01:39.479><c> I</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
want it to be in our terraform script I
 

00:01:39.720 --> 00:01:41.149 align:start position:0%
want it to be in our terraform script I
want<00:01:39.840><c> to</c><00:01:39.960><c> go</c><00:01:40.020><c> ahead</c><00:01:40.140><c> and</c><00:01:40.259><c> look</c><00:01:40.500><c> at</c><00:01:40.799><c> one</c><00:01:41.040><c> of</c><00:01:41.100><c> my</c>

00:01:41.149 --> 00:01:41.159 align:start position:0%
want to go ahead and look at one of my
 

00:01:41.159 --> 00:01:44.090 align:start position:0%
want to go ahead and look at one of my
other<00:01:41.340><c> files</c><00:01:41.759><c> in</c><00:01:41.939><c> my</c><00:01:42.119><c> code</c><00:01:42.240><c> base</c><00:01:42.500><c> now</c><00:01:43.500><c> I</c><00:01:43.979><c> think</c>

00:01:44.090 --> 00:01:44.100 align:start position:0%
other files in my code base now I think
 

00:01:44.100 --> 00:01:46.010 align:start position:0%
other files in my code base now I think
I<00:01:44.280><c> have</c><00:01:44.400><c> some</c><00:01:44.520><c> bugs</c><00:01:44.820><c> in</c><00:01:45.000><c> my</c><00:01:45.119><c> code</c><00:01:45.240><c> but</c><00:01:45.600><c> I'm</c><00:01:45.780><c> not</c>

00:01:46.010 --> 00:01:46.020 align:start position:0%
I have some bugs in my code but I'm not
 

00:01:46.020 --> 00:01:48.170 align:start position:0%
I have some bugs in my code but I'm not
sure<00:01:46.259><c> so</c><00:01:46.920><c> I'm</c><00:01:47.040><c> going</c><00:01:47.159><c> to</c><00:01:47.220><c> go</c><00:01:47.340><c> ahead</c><00:01:47.520><c> and</c><00:01:47.700><c> select</c>

00:01:48.170 --> 00:01:48.180 align:start position:0%
sure so I'm going to go ahead and select
 

00:01:48.180 --> 00:01:49.850 align:start position:0%
sure so I'm going to go ahead and select
some<00:01:48.780><c> of</c><00:01:48.900><c> the</c><00:01:48.960><c> highlighted</c><00:01:49.439><c> text</c><00:01:49.619><c> that's</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
some of the highlighted text that's
 

00:01:49.860 --> 00:01:51.889 align:start position:0%
some of the highlighted text that's
deploying<00:01:50.280><c> a</c><00:01:50.399><c> website</c><00:01:50.700><c> in</c><00:01:51.060><c> my</c><00:01:51.180><c> Azure</c><00:01:51.540><c> web</c><00:01:51.720><c> app</c>

00:01:51.889 --> 00:01:51.899 align:start position:0%
deploying a website in my Azure web app
 

00:01:51.899 --> 00:01:54.170 align:start position:0%
deploying a website in my Azure web app
and<00:01:52.439><c> I'm</c><00:01:52.560><c> going</c><00:01:52.680><c> to</c><00:01:52.740><c> ask</c><00:01:52.860><c> copilot</c><00:01:53.579><c> to</c><00:01:53.939><c> see</c><00:01:54.119><c> if</c>

00:01:54.170 --> 00:01:54.180 align:start position:0%
and I'm going to ask copilot to see if
 

00:01:54.180 --> 00:01:56.030 align:start position:0%
and I'm going to ask copilot to see if
it<00:01:54.299><c> can</c><00:01:54.420><c> find</c><00:01:54.540><c> any</c><00:01:54.720><c> bugs</c><00:01:55.079><c> in</c><00:01:55.200><c> my</c><00:01:55.320><c> code</c>

00:01:56.030 --> 00:01:56.040 align:start position:0%
it can find any bugs in my code
 

00:01:56.040 --> 00:01:58.370 align:start position:0%
it can find any bugs in my code
now<00:01:56.520><c> fortunately</c><00:01:57.000><c> GitHub</c><00:01:57.720><c> copilot</c><00:01:58.200><c> didn't</c>

00:01:58.370 --> 00:01:58.380 align:start position:0%
now fortunately GitHub copilot didn't
 

00:01:58.380 --> 00:02:00.410 align:start position:0%
now fortunately GitHub copilot didn't
find<00:01:58.619><c> any</c><00:01:58.799><c> bugs</c><00:01:59.100><c> in</c><00:01:59.220><c> our</c><00:01:59.399><c> code</c><00:01:59.520><c> but</c><00:02:00.060><c> that's</c><00:02:00.240><c> not</c>

00:02:00.410 --> 00:02:00.420 align:start position:0%
find any bugs in our code but that's not
 

00:02:00.420 --> 00:02:02.510 align:start position:0%
find any bugs in our code but that's not
always<00:02:00.600><c> true</c><00:02:00.899><c> for</c><00:02:01.140><c> everyone</c>

00:02:02.510 --> 00:02:02.520 align:start position:0%
always true for everyone
 

00:02:02.520 --> 00:02:03.950 align:start position:0%
always true for everyone
so<00:02:03.000><c> while</c><00:02:03.119><c> it's</c><00:02:03.299><c> really</c><00:02:03.479><c> great</c><00:02:03.659><c> that</c><00:02:03.840><c> I</c><00:02:03.960><c> don't</c>

00:02:03.950 --> 00:02:03.960 align:start position:0%
so while it's really great that I don't
 

00:02:03.960 --> 00:02:05.810 align:start position:0%
so while it's really great that I don't
have<00:02:04.140><c> any</c><00:02:04.259><c> bugs</c><00:02:04.560><c> in</c><00:02:04.680><c> my</c><00:02:04.799><c> code</c><00:02:04.979><c> I</c><00:02:05.460><c> do</c><00:02:05.579><c> need</c><00:02:05.700><c> to</c>

00:02:05.810 --> 00:02:05.820 align:start position:0%
have any bugs in my code I do need to
 

00:02:05.820 --> 00:02:07.130 align:start position:0%
have any bugs in my code I do need to
think<00:02:05.939><c> about</c><00:02:06.060><c> something</c><00:02:06.360><c> else</c><00:02:06.659><c> that's</c><00:02:06.899><c> really</c>

00:02:07.130 --> 00:02:07.140 align:start position:0%
think about something else that's really
 

00:02:07.140 --> 00:02:08.690 align:start position:0%
think about something else that's really
important<00:02:07.439><c> with</c><00:02:07.740><c> every</c><00:02:08.039><c> single</c><00:02:08.280><c> bit</c><00:02:08.399><c> of</c><00:02:08.520><c> code</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
important with every single bit of code
 

00:02:08.700 --> 00:02:10.969 align:start position:0%
important with every single bit of code
I<00:02:08.940><c> write</c><00:02:09.119><c> and</c><00:02:09.660><c> that</c><00:02:09.840><c> is</c><00:02:10.020><c> tests</c><00:02:10.440><c> and</c><00:02:10.679><c> writing</c>

00:02:10.969 --> 00:02:10.979 align:start position:0%
I write and that is tests and writing
 

00:02:10.979 --> 00:02:12.530 align:start position:0%
I write and that is tests and writing
tests<00:02:11.280><c> can</c><00:02:11.400><c> be</c><00:02:11.580><c> hard</c><00:02:11.760><c> and</c><00:02:11.940><c> difficult</c><00:02:12.239><c> for</c><00:02:12.480><c> a</c>

00:02:12.530 --> 00:02:12.540 align:start position:0%
tests can be hard and difficult for a
 

00:02:12.540 --> 00:02:14.150 align:start position:0%
tests can be hard and difficult for a
lot<00:02:12.660><c> of</c><00:02:12.720><c> us</c><00:02:12.840><c> and</c><00:02:13.379><c> sometimes</c><00:02:13.620><c> really</c><00:02:13.800><c> a</c><00:02:13.980><c> pain</c>

00:02:14.150 --> 00:02:14.160 align:start position:0%
lot of us and sometimes really a pain
 

00:02:14.160 --> 00:02:16.550 align:start position:0%
lot of us and sometimes really a pain
Point<00:02:14.340><c> as</c><00:02:14.640><c> a</c><00:02:14.760><c> developer</c><00:02:15.599><c> so</c><00:02:16.080><c> I</c><00:02:16.260><c> can</c><00:02:16.379><c> go</c><00:02:16.440><c> into</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
Point as a developer so I can go into
 

00:02:16.560 --> 00:02:19.610 align:start position:0%
Point as a developer so I can go into
GitHub<00:02:16.920><c> copilot</c><00:02:17.520><c> chat</c><00:02:17.819><c> and</c><00:02:18.420><c> do</c><00:02:18.599><c> a</c><00:02:18.900><c> slash</c><00:02:19.319><c> and</c>

00:02:19.610 --> 00:02:19.620 align:start position:0%
GitHub copilot chat and do a slash and
 

00:02:19.620 --> 00:02:21.770 align:start position:0%
GitHub copilot chat and do a slash and
write<00:02:19.860><c> tests</c><00:02:20.400><c> and</c><00:02:20.640><c> it</c><00:02:20.760><c> will</c><00:02:20.819><c> go</c><00:02:21.060><c> ahead</c><00:02:21.239><c> and</c><00:02:21.540><c> get</c>

00:02:21.770 --> 00:02:21.780 align:start position:0%
write tests and it will go ahead and get
 

00:02:21.780 --> 00:02:23.869 align:start position:0%
write tests and it will go ahead and get
tests<00:02:22.140><c> for</c><00:02:22.260><c> the</c><00:02:22.379><c> selected</c><00:02:22.800><c> code</c><00:02:22.980><c> the</c><00:02:23.819><c> other</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
tests for the selected code the other
 

00:02:23.879 --> 00:02:25.970 align:start position:0%
tests for the selected code the other
option<00:02:24.239><c> is</c><00:02:24.420><c> I</c><00:02:24.720><c> can</c><00:02:24.840><c> just</c><00:02:25.020><c> go</c><00:02:25.200><c> ahead</c><00:02:25.379><c> and</c><00:02:25.500><c> ask</c>

00:02:25.970 --> 00:02:25.980 align:start position:0%
option is I can just go ahead and ask
 

00:02:25.980 --> 00:02:27.830 align:start position:0%
option is I can just go ahead and ask
GitHub<00:02:26.459><c> copalot</c><00:02:27.000><c> to</c><00:02:27.120><c> generate</c><00:02:27.360><c> a</c><00:02:27.480><c> unit</c><00:02:27.599><c> test</c>

00:02:27.830 --> 00:02:27.840 align:start position:0%
GitHub copalot to generate a unit test
 

00:02:27.840 --> 00:02:29.809 align:start position:0%
GitHub copalot to generate a unit test
and<00:02:28.140><c> in</c><00:02:28.260><c> this</c><00:02:28.440><c> case</c><00:02:28.560><c> I'm</c><00:02:29.160><c> asking</c><00:02:29.459><c> GitHub</c>

00:02:29.809 --> 00:02:29.819 align:start position:0%
and in this case I'm asking GitHub
 

00:02:29.819 --> 00:02:32.449 align:start position:0%
and in this case I'm asking GitHub
co-pilot<00:02:30.360><c> test</c><00:02:30.540><c> to</c><00:02:30.900><c> generate</c><00:02:31.260><c> a</c><00:02:31.920><c> Terra</c><00:02:32.280><c> test</c>

00:02:32.449 --> 00:02:32.459 align:start position:0%
co-pilot test to generate a Terra test
 

00:02:32.459 --> 00:02:33.530 align:start position:0%
co-pilot test to generate a Terra test
for<00:02:32.760><c> me</c>

00:02:33.530 --> 00:02:33.540 align:start position:0%
for me
 

00:02:33.540 --> 00:02:35.690 align:start position:0%
for me
so<00:02:33.959><c> once</c><00:02:34.319><c> it</c><00:02:34.379><c> has</c><00:02:34.560><c> the</c><00:02:34.739><c> output</c><00:02:34.980><c> I</c><00:02:35.340><c> create</c><00:02:35.459><c> the</c>

00:02:35.690 --> 00:02:35.700 align:start position:0%
so once it has the output I create the
 

00:02:35.700 --> 00:02:38.030 align:start position:0%
so once it has the output I create the
new<00:02:35.879><c> file</c><00:02:36.180><c> and</c><00:02:36.599><c> then</c><00:02:36.720><c> I</c><00:02:36.900><c> select</c><00:02:37.200><c> and</c><00:02:37.319><c> put</c><00:02:37.500><c> in</c><00:02:37.680><c> my</c>

00:02:38.030 --> 00:02:38.040 align:start position:0%
new file and then I select and put in my
 

00:02:38.040 --> 00:02:40.490 align:start position:0%
new file and then I select and put in my
testing<00:02:38.400><c> code</c><00:02:38.640><c> in</c><00:02:39.060><c> that</c><00:02:39.239><c> space</c>

00:02:40.490 --> 00:02:40.500 align:start position:0%
testing code in that space
 

00:02:40.500 --> 00:02:42.229 align:start position:0%
testing code in that space
now<00:02:40.980><c> that</c><00:02:41.099><c> we've</c><00:02:41.280><c> done</c><00:02:41.400><c> all</c><00:02:41.580><c> that</c><00:02:41.700><c> we've</c><00:02:42.120><c> seen</c>

00:02:42.229 --> 00:02:42.239 align:start position:0%
now that we've done all that we've seen
 

00:02:42.239 --> 00:02:44.089 align:start position:0%
now that we've done all that we've seen
how<00:02:42.420><c> GitHub</c><00:02:42.720><c> co-pilot</c><00:02:43.260><c> chat</c><00:02:43.500><c> can</c><00:02:43.739><c> really</c><00:02:43.920><c> help</c>

00:02:44.089 --> 00:02:44.099 align:start position:0%
how GitHub co-pilot chat can really help
 

00:02:44.099 --> 00:02:45.650 align:start position:0%
how GitHub co-pilot chat can really help
us<00:02:44.220><c> in</c><00:02:44.400><c> the</c><00:02:44.519><c> IDE</c>

00:02:45.650 --> 00:02:45.660 align:start position:0%
us in the IDE
 

00:02:45.660 --> 00:02:47.869 align:start position:0%
us in the IDE
so<00:02:46.140><c> today</c><00:02:46.260><c> we</c><00:02:46.680><c> looked</c><00:02:46.980><c> at</c><00:02:47.040><c> all</c><00:02:47.340><c> the</c><00:02:47.400><c> great</c><00:02:47.519><c> ways</c>

00:02:47.869 --> 00:02:47.879 align:start position:0%
so today we looked at all the great ways
 

00:02:47.879 --> 00:02:49.610 align:start position:0%
so today we looked at all the great ways
that<00:02:48.060><c> GitHub</c><00:02:48.360><c> co-pilot</c><00:02:48.959><c> chat</c><00:02:49.200><c> can</c><00:02:49.379><c> help</c><00:02:49.500><c> us</c>

00:02:49.610 --> 00:02:49.620 align:start position:0%
that GitHub co-pilot chat can help us
 

00:02:49.620 --> 00:02:52.070 align:start position:0%
that GitHub co-pilot chat can help us
write<00:02:49.860><c> better</c><00:02:50.099><c> infrastructure</c><00:02:50.819><c> as</c><00:02:50.879><c> code</c><00:02:51.599><c> we</c>

00:02:52.070 --> 00:02:52.080 align:start position:0%
write better infrastructure as code we
 

00:02:52.080 --> 00:02:53.630 align:start position:0%
write better infrastructure as code we
start<00:02:52.200><c> off</c><00:02:52.379><c> with</c><00:02:52.560><c> looking</c><00:02:52.680><c> at</c><00:02:52.920><c> how</c><00:02:53.099><c> we</c><00:02:53.519><c> can</c>

00:02:53.630 --> 00:02:53.640 align:start position:0%
start off with looking at how we can
 

00:02:53.640 --> 00:02:55.910 align:start position:0%
start off with looking at how we can
explain<00:02:53.819><c> our</c><00:02:54.180><c> existing</c><00:02:54.540><c> code</c><00:02:54.780><c> how</c><00:02:55.379><c> we</c><00:02:55.560><c> can</c><00:02:55.680><c> fix</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
explain our existing code how we can fix
 

00:02:55.920 --> 00:02:58.070 align:start position:0%
explain our existing code how we can fix
our<00:02:56.220><c> code</c><00:02:56.400><c> and</c><00:02:57.060><c> better</c><00:02:57.239><c> yet</c><00:02:57.420><c> we're</c><00:02:57.720><c> able</c><00:02:57.959><c> to</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
our code and better yet we're able to
 

00:02:58.080 --> 00:03:00.110 align:start position:0%
our code and better yet we're able to
generate<00:02:58.440><c> some</c><00:02:58.680><c> new</c><00:02:58.920><c> terraform</c><00:02:59.340><c> files</c><00:02:59.819><c> using</c>

00:03:00.110 --> 00:03:00.120 align:start position:0%
generate some new terraform files using
 

00:03:00.120 --> 00:03:02.449 align:start position:0%
generate some new terraform files using
GitHub<00:03:00.420><c> co-pilot</c><00:03:01.019><c> chat</c><00:03:01.260><c> then</c><00:03:01.800><c> we</c><00:03:01.980><c> went</c><00:03:02.099><c> a</c><00:03:02.340><c> step</c>

00:03:02.449 --> 00:03:02.459 align:start position:0%
GitHub co-pilot chat then we went a step
 

00:03:02.459 --> 00:03:04.369 align:start position:0%
GitHub co-pilot chat then we went a step
further<00:03:02.760><c> and</c><00:03:03.120><c> wrote</c><00:03:03.360><c> some</c><00:03:03.540><c> great</c><00:03:03.720><c> unit</c><00:03:03.900><c> tests</c>

00:03:04.369 --> 00:03:04.379 align:start position:0%
further and wrote some great unit tests
 

00:03:04.379 --> 00:03:05.990 align:start position:0%
further and wrote some great unit tests
to<00:03:04.560><c> test</c><00:03:04.739><c> our</c><00:03:05.040><c> new</c><00:03:05.160><c> code</c>

00:03:05.990 --> 00:03:06.000 align:start position:0%
to test our new code
 

00:03:06.000 --> 00:03:07.670 align:start position:0%
to test our new code
so<00:03:06.540><c> I'm</c><00:03:06.660><c> looking</c><00:03:06.840><c> forward</c><00:03:07.080><c> to</c><00:03:07.260><c> hearing</c><00:03:07.500><c> how</c>

00:03:07.670 --> 00:03:07.680 align:start position:0%
so I'm looking forward to hearing how
 

00:03:07.680 --> 00:03:09.710 align:start position:0%
so I'm looking forward to hearing how
all<00:03:07.980><c> of</c><00:03:08.099><c> you</c><00:03:08.220><c> can</c><00:03:08.459><c> leverage</c><00:03:08.760><c> GitHub</c><00:03:09.120><c> co-pilot</c>

00:03:09.710 --> 00:03:09.720 align:start position:0%
all of you can leverage GitHub co-pilot
 

00:03:09.720 --> 00:03:11.449 align:start position:0%
all of you can leverage GitHub co-pilot
chat<00:03:09.959><c> to</c><00:03:10.440><c> write</c><00:03:10.620><c> better</c><00:03:10.860><c> infrastructures</c>

00:03:11.449 --> 00:03:11.459 align:start position:0%
chat to write better infrastructures
 

00:03:11.459 --> 00:03:13.760 align:start position:0%
chat to write better infrastructures
code

