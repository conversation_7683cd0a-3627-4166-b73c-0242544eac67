WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.210 align:start position:0%
 
okay<00:00:00.539><c> in</c><00:00:00.900><c> the</c><00:00:01.020><c> last</c><00:00:01.140><c> video</c><00:00:01.380><c> we</c><00:00:01.800><c> looked</c><00:00:02.100><c> at</c>

00:00:02.210 --> 00:00:02.220 align:start position:0%
okay in the last video we looked at
 

00:00:02.220 --> 00:00:05.210 align:start position:0%
okay in the last video we looked at
koala<00:00:03.000><c> a</c><00:00:03.600><c> dialogue</c><00:00:03.959><c> model</c><00:00:04.140><c> for</c><00:00:04.500><c> academic</c>

00:00:05.210 --> 00:00:05.220 align:start position:0%
koala a dialogue model for academic
 

00:00:05.220 --> 00:00:06.410 align:start position:0%
koala a dialogue model for academic
research

00:00:06.410 --> 00:00:06.420 align:start position:0%
research
 

00:00:06.420 --> 00:00:08.870 align:start position:0%
research
and<00:00:06.960><c> while</c><00:00:07.080><c> they've</c><00:00:07.379><c> got</c><00:00:07.560><c> a</c><00:00:07.919><c> demo</c><00:00:08.340><c> up</c><00:00:08.460><c> that</c><00:00:08.760><c> you</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
and while they've got a demo up that you
 

00:00:08.880 --> 00:00:10.430 align:start position:0%
and while they've got a demo up that you
can<00:00:09.000><c> try</c><00:00:09.240><c> for</c><00:00:09.540><c> this</c>

00:00:10.430 --> 00:00:10.440 align:start position:0%
can try for this
 

00:00:10.440 --> 00:00:12.530 align:start position:0%
can try for this
I<00:00:10.920><c> wanted</c><00:00:10.980><c> to</c><00:00:11.160><c> put</c><00:00:11.280><c> it</c><00:00:11.460><c> together</c><00:00:11.639><c> in</c><00:00:12.120><c> a</c><00:00:12.360><c> code</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
I wanted to put it together in a code
 

00:00:12.540 --> 00:00:16.310 align:start position:0%
I wanted to put it together in a code
lab<00:00:12.900><c> so</c><00:00:13.799><c> here</c><00:00:14.340><c> is</c><00:00:14.580><c> basically</c><00:00:14.940><c> a</c><00:00:15.420><c> co-lab</c><00:00:15.960><c> that</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
lab so here is basically a co-lab that
 

00:00:16.320 --> 00:00:18.830 align:start position:0%
lab so here is basically a co-lab that
you<00:00:16.500><c> can</c><00:00:16.560><c> run</c><00:00:16.859><c> for</c><00:00:17.220><c> free</c><00:00:17.460><c> so</c><00:00:18.180><c> this</c><00:00:18.359><c> is</c><00:00:18.480><c> using</c>

00:00:18.830 --> 00:00:18.840 align:start position:0%
you can run for free so this is using
 

00:00:18.840 --> 00:00:21.710 align:start position:0%
you can run for free so this is using
the<00:00:19.020><c> 7</c><00:00:19.260><c> billion</c><00:00:19.680><c> version</c><00:00:20.039><c> of</c><00:00:20.640><c> koala</c><00:00:21.300><c> remember</c>

00:00:21.710 --> 00:00:21.720 align:start position:0%
the 7 billion version of koala remember
 

00:00:21.720 --> 00:00:24.170 align:start position:0%
the 7 billion version of koala remember
koala<00:00:22.439><c> is</c><00:00:22.619><c> basically</c><00:00:22.980><c> a</c><00:00:23.279><c> fine-tuned</c><00:00:23.939><c> version</c>

00:00:24.170 --> 00:00:24.180 align:start position:0%
koala is basically a fine-tuned version
 

00:00:24.180 --> 00:00:28.550 align:start position:0%
koala is basically a fine-tuned version
of<00:00:24.779><c> a</c><00:00:24.900><c> llama</c><00:00:25.199><c> model</c><00:00:25.500><c> but</c><00:00:26.220><c> tuned</c><00:00:26.760><c> on</c><00:00:27.180><c> a</c><00:00:27.720><c> lot</c><00:00:27.900><c> of</c>

00:00:28.550 --> 00:00:28.560 align:start position:0%
of a llama model but tuned on a lot of
 

00:00:28.560 --> 00:00:31.669 align:start position:0%
of a llama model but tuned on a lot of
chat<00:00:29.279><c> and</c><00:00:29.519><c> conversation</c><00:00:30.060><c> data</c><00:00:30.660><c> and</c><00:00:31.320><c> a</c><00:00:31.439><c> variety</c>

00:00:31.669 --> 00:00:31.679 align:start position:0%
chat and conversation data and a variety
 

00:00:31.679 --> 00:00:33.950 align:start position:0%
chat and conversation data and a variety
of<00:00:31.740><c> other</c><00:00:32.040><c> instruction</c><00:00:32.399><c> tuning</c><00:00:33.180><c> as</c><00:00:33.420><c> well</c><00:00:33.600><c> if</c>

00:00:33.950 --> 00:00:33.960 align:start position:0%
of other instruction tuning as well if
 

00:00:33.960 --> 00:00:35.270 align:start position:0%
of other instruction tuning as well if
you're<00:00:34.140><c> interested</c><00:00:34.620><c> in</c><00:00:34.739><c> that</c><00:00:34.860><c> and</c><00:00:35.040><c> you</c><00:00:35.160><c> didn't</c>

00:00:35.270 --> 00:00:35.280 align:start position:0%
you're interested in that and you didn't
 

00:00:35.280 --> 00:00:36.950 align:start position:0%
you're interested in that and you didn't
see<00:00:35.460><c> the</c><00:00:35.640><c> previous</c><00:00:35.820><c> video</c><00:00:36.120><c> go</c><00:00:36.480><c> back</c><00:00:36.660><c> and</c><00:00:36.780><c> look</c>

00:00:36.950 --> 00:00:36.960 align:start position:0%
see the previous video go back and look
 

00:00:36.960 --> 00:00:38.450 align:start position:0%
see the previous video go back and look
at<00:00:37.079><c> the</c><00:00:37.200><c> previous</c><00:00:37.380><c> video</c>

00:00:38.450 --> 00:00:38.460 align:start position:0%
at the previous video
 

00:00:38.460 --> 00:00:40.970 align:start position:0%
at the previous video
so<00:00:39.120><c> here</c><00:00:39.600><c> not</c><00:00:39.899><c> only</c><00:00:40.020><c> are</c><00:00:40.140><c> we</c><00:00:40.260><c> gonna</c><00:00:40.379><c> be</c><00:00:40.739><c> able</c><00:00:40.860><c> to</c>

00:00:40.970 --> 00:00:40.980 align:start position:0%
so here not only are we gonna be able to
 

00:00:40.980 --> 00:00:43.670 align:start position:0%
so here not only are we gonna be able to
load<00:00:41.219><c> it</c><00:00:41.340><c> in</c><00:00:41.460><c> colab</c><00:00:42.360><c> but</c><00:00:42.899><c> we're</c><00:00:42.960><c> gonna</c><00:00:43.140><c> load</c><00:00:43.500><c> it</c>

00:00:43.670 --> 00:00:43.680 align:start position:0%
load it in colab but we're gonna load it
 

00:00:43.680 --> 00:00:46.310 align:start position:0%
load it in colab but we're gonna load it
on<00:00:43.860><c> a</c><00:00:44.040><c> T4</c><00:00:44.399><c> which</c><00:00:44.760><c> means</c><00:00:45.059><c> you</c><00:00:45.360><c> can</c><00:00:45.480><c> use</c><00:00:46.079><c> this</c>

00:00:46.310 --> 00:00:46.320 align:start position:0%
on a T4 which means you can use this
 

00:00:46.320 --> 00:00:49.130 align:start position:0%
on a T4 which means you can use this
collab<00:00:46.800><c> in</c><00:00:47.340><c> the</c><00:00:47.700><c> totally</c><00:00:47.940><c> free</c><00:00:48.300><c> collab</c><00:00:48.899><c> as</c>

00:00:49.130 --> 00:00:49.140 align:start position:0%
collab in the totally free collab as
 

00:00:49.140 --> 00:00:51.590 align:start position:0%
collab in the totally free collab as
long<00:00:49.320><c> as</c><00:00:49.440><c> we</c><00:00:49.559><c> get</c><00:00:49.800><c> allocated</c><00:00:50.280><c> at</c><00:00:50.520><c> GPU</c><00:00:51.000><c> you</c><00:00:51.480><c> can</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
long as we get allocated at GPU you can
 

00:00:51.600 --> 00:00:53.090 align:start position:0%
long as we get allocated at GPU you can
basically<00:00:51.840><c> run</c><00:00:52.140><c> this</c><00:00:52.379><c> so</c><00:00:52.620><c> you</c><00:00:52.739><c> can</c><00:00:52.800><c> see</c><00:00:52.920><c> here</c>

00:00:53.090 --> 00:00:53.100 align:start position:0%
basically run this so you can see here
 

00:00:53.100 --> 00:00:55.130 align:start position:0%
basically run this so you can see here
I'm<00:00:53.280><c> bringing</c><00:00:53.579><c> it</c><00:00:53.700><c> in</c><00:00:53.940><c> I'm</c><00:00:54.420><c> running</c><00:00:54.600><c> it</c><00:00:54.840><c> on</c><00:00:55.020><c> the</c>

00:00:55.130 --> 00:00:55.140 align:start position:0%
I'm bringing it in I'm running it on the
 

00:00:55.140 --> 00:00:57.130 align:start position:0%
I'm bringing it in I'm running it on the
Tesla<00:00:55.559><c> T4</c>

00:00:57.130 --> 00:00:57.140 align:start position:0%
Tesla T4
 

00:00:57.140 --> 00:00:59.569 align:start position:0%
Tesla T4
we're<00:00:58.140><c> doing</c><00:00:58.320><c> the</c><00:00:58.620><c> standard</c><00:00:58.920><c> stuff</c><00:00:59.100><c> of</c>

00:00:59.569 --> 00:00:59.579 align:start position:0%
we're doing the standard stuff of
 

00:00:59.579 --> 00:01:01.670 align:start position:0%
we're doing the standard stuff of
bringing<00:00:59.940><c> in</c><00:01:00.059><c> the</c><00:01:00.239><c> Llama</c><00:01:00.420><c> tokenizer</c><00:01:01.199><c> Llama</c>

00:01:01.670 --> 00:01:01.680 align:start position:0%
bringing in the Llama tokenizer Llama
 

00:01:01.680 --> 00:01:03.709 align:start position:0%
bringing in the Llama tokenizer Llama
for<00:01:01.920><c> causal</c><00:01:02.399><c> language</c><00:01:02.640><c> modeling</c><00:01:03.239><c> and</c><00:01:03.600><c> I'm</c>

00:01:03.709 --> 00:01:03.719 align:start position:0%
for causal language modeling and I'm
 

00:01:03.719 --> 00:01:05.270 align:start position:0%
for causal language modeling and I'm
going<00:01:03.840><c> to</c><00:01:03.899><c> be</c><00:01:03.960><c> using</c><00:01:04.199><c> the</c><00:01:04.320><c> pipeline</c><00:01:04.799><c> for</c><00:01:05.100><c> this</c>

00:01:05.270 --> 00:01:05.280 align:start position:0%
going to be using the pipeline for this
 

00:01:05.280 --> 00:01:06.950 align:start position:0%
going to be using the pipeline for this
one<00:01:05.400><c> and</c><00:01:05.820><c> then</c><00:01:05.939><c> basically</c><00:01:06.299><c> I'm</c><00:01:06.420><c> bringing</c><00:01:06.840><c> in</c>

00:01:06.950 --> 00:01:06.960 align:start position:0%
one and then basically I'm bringing in
 

00:01:06.960 --> 00:01:10.310 align:start position:0%
one and then basically I'm bringing in
just<00:01:07.380><c> the</c><00:01:07.860><c> the</c><00:01:08.180><c> tokenizer</c><00:01:09.180><c> and</c><00:01:10.020><c> the</c><00:01:10.200><c> model</c>

00:01:10.310 --> 00:01:10.320 align:start position:0%
just the the tokenizer and the model
 

00:01:10.320 --> 00:01:13.130 align:start position:0%
just the the tokenizer and the model
itself<00:01:10.740><c> now</c><00:01:11.100><c> we're</c><00:01:11.700><c> loading</c><00:01:12.119><c> it</c><00:01:12.299><c> in</c><00:01:12.600><c> 8-bit</c>

00:01:13.130 --> 00:01:13.140 align:start position:0%
itself now we're loading it in 8-bit
 

00:01:13.140 --> 00:01:14.750 align:start position:0%
itself now we're loading it in 8-bit
equals<00:01:13.619><c> true</c>

00:01:14.750 --> 00:01:14.760 align:start position:0%
equals true
 

00:01:14.760 --> 00:01:17.270 align:start position:0%
equals true
here<00:01:15.360><c> if</c><00:01:15.900><c> you</c><00:01:16.080><c> wanted</c><00:01:16.320><c> to</c><00:01:16.560><c> upload</c><00:01:16.920><c> the</c><00:01:17.159><c> full</c>

00:01:17.270 --> 00:01:17.280 align:start position:0%
here if you wanted to upload the full
 

00:01:17.280 --> 00:01:19.429 align:start position:0%
here if you wanted to upload the full
version<00:01:17.520><c> you</c><00:01:18.060><c> don't</c><00:01:18.180><c> have</c><00:01:18.420><c> to</c><00:01:18.600><c> load</c><00:01:18.900><c> this</c><00:01:19.140><c> in</c>

00:01:19.429 --> 00:01:19.439 align:start position:0%
version you don't have to load this in
 

00:01:19.439 --> 00:01:22.130 align:start position:0%
version you don't have to load this in
8-bit<00:01:19.920><c> but</c><00:01:20.460><c> you</c><00:01:20.640><c> won't</c><00:01:20.759><c> be</c><00:01:20.939><c> able</c><00:01:21.119><c> to</c><00:01:21.299><c> load</c><00:01:22.020><c> it</c>

00:01:22.130 --> 00:01:22.140 align:start position:0%
8-bit but you won't be able to load it
 

00:01:22.140 --> 00:01:24.710 align:start position:0%
8-bit but you won't be able to load it
in<00:01:22.439><c> full</c><00:01:22.920><c> with</c><00:01:23.340><c> a</c><00:01:23.520><c> T4</c><00:01:23.820><c> I</c><00:01:24.119><c> think</c><00:01:24.240><c> you'll</c><00:01:24.420><c> need</c><00:01:24.540><c> a</c>

00:01:24.710 --> 00:01:24.720 align:start position:0%
in full with a T4 I think you'll need a
 

00:01:24.720 --> 00:01:26.870 align:start position:0%
in full with a T4 I think you'll need a
slightly<00:01:24.900><c> bigger</c><00:01:25.259><c> GPU</c><00:01:25.799><c> to</c><00:01:26.040><c> do</c><00:01:26.220><c> that</c><00:01:26.400><c> you</c><00:01:26.759><c> can</c>

00:01:26.870 --> 00:01:26.880 align:start position:0%
slightly bigger GPU to do that you can
 

00:01:26.880 --> 00:01:28.609 align:start position:0%
slightly bigger GPU to do that you can
see<00:01:27.000><c> it</c><00:01:27.299><c> takes</c><00:01:27.540><c> a</c><00:01:27.720><c> while</c><00:01:27.840><c> to</c><00:01:28.020><c> bring</c><00:01:28.140><c> it</c><00:01:28.320><c> in</c><00:01:28.439><c> but</c>

00:01:28.609 --> 00:01:28.619 align:start position:0%
see it takes a while to bring it in but
 

00:01:28.619 --> 00:01:30.830 align:start position:0%
see it takes a while to bring it in but
once<00:01:29.040><c> it's</c><00:01:29.159><c> in</c><00:01:29.520><c> you</c><00:01:30.119><c> will</c><00:01:30.240><c> be</c><00:01:30.360><c> able</c><00:01:30.479><c> to</c><00:01:30.600><c> then</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
once it's in you will be able to then
 

00:01:30.840 --> 00:01:33.350 align:start position:0%
once it's in you will be able to then
look<00:01:31.080><c> at</c><00:01:31.320><c> your</c><00:01:31.619><c> GPU</c><00:01:32.159><c> again</c><00:01:32.400><c> and</c><00:01:33.000><c> you</c><00:01:33.119><c> can</c><00:01:33.180><c> see</c>

00:01:33.350 --> 00:01:33.360 align:start position:0%
look at your GPU again and you can see
 

00:01:33.360 --> 00:01:34.969 align:start position:0%
look at your GPU again and you can see
that<00:01:33.540><c> the</c><00:01:33.780><c> ram</c><00:01:33.900><c> that</c><00:01:34.200><c> we're</c><00:01:34.380><c> using</c><00:01:34.680><c> is</c>

00:01:34.969 --> 00:01:34.979 align:start position:0%
that the ram that we're using is
 

00:01:34.979 --> 00:01:37.310 align:start position:0%
that the ram that we're using is
actually<00:01:35.220><c> quite</c><00:01:35.700><c> a</c><00:01:35.880><c> small</c><00:01:36.060><c> amount</c><00:01:36.420><c> so</c><00:01:36.900><c> even</c><00:01:37.079><c> if</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
actually quite a small amount so even if
 

00:01:37.320 --> 00:01:40.190 align:start position:0%
actually quite a small amount so even if
you've<00:01:37.500><c> got</c><00:01:37.740><c> an</c><00:01:38.040><c> old</c><00:01:38.340><c> GPU</c><00:01:39.180><c> yourself</c><00:01:39.479><c> there's</c><00:01:40.020><c> a</c>

00:01:40.190 --> 00:01:40.200 align:start position:0%
you've got an old GPU yourself there's a
 

00:01:40.200 --> 00:01:41.210 align:start position:0%
you've got an old GPU yourself there's a
good<00:01:40.259><c> chance</c><00:01:40.439><c> you</c><00:01:40.619><c> might</c><00:01:40.860><c> be</c><00:01:40.979><c> able</c><00:01:41.100><c> to</c>

00:01:41.210 --> 00:01:41.220 align:start position:0%
good chance you might be able to
 

00:01:41.220 --> 00:01:43.310 align:start position:0%
good chance you might be able to
actually<00:01:41.340><c> run</c><00:01:41.700><c> this</c><00:01:42.000><c> locally</c><00:01:42.420><c> using</c><00:01:43.079><c> this</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
actually run this locally using this
 

00:01:43.320 --> 00:01:46.490 align:start position:0%
actually run this locally using this
version<00:01:43.500><c> in</c><00:01:43.920><c> 8-bit</c><00:01:44.460><c> as</c><00:01:44.820><c> long</c><00:01:45.000><c> as</c><00:01:45.180><c> your</c><00:01:46.020><c> GPU</c>

00:01:46.490 --> 00:01:46.500 align:start position:0%
version in 8-bit as long as your GPU
 

00:01:46.500 --> 00:01:48.469 align:start position:0%
version in 8-bit as long as your GPU
will<00:01:46.740><c> support</c><00:01:46.920><c> the</c><00:01:47.220><c> bits</c><00:01:47.520><c> and</c><00:01:47.640><c> bytes</c><00:01:47.939><c> 8-bit</c>

00:01:48.469 --> 00:01:48.479 align:start position:0%
will support the bits and bytes 8-bit
 

00:01:48.479 --> 00:01:50.210 align:start position:0%
will support the bits and bytes 8-bit
stuff<00:01:48.780><c> you</c><00:01:48.960><c> should</c><00:01:49.140><c> be</c><00:01:49.259><c> fine</c><00:01:49.439><c> okay</c><00:01:49.860><c> so</c><00:01:50.040><c> we're</c>

00:01:50.210 --> 00:01:50.220 align:start position:0%
stuff you should be fine okay so we're
 

00:01:50.220 --> 00:01:52.249 align:start position:0%
stuff you should be fine okay so we're
setting<00:01:50.520><c> up</c><00:01:50.579><c> a</c><00:01:50.820><c> pipeline</c><00:01:51.360><c> here</c>

00:01:52.249 --> 00:01:52.259 align:start position:0%
setting up a pipeline here
 

00:01:52.259 --> 00:01:53.990 align:start position:0%
setting up a pipeline here
we're<00:01:52.740><c> going</c><00:01:52.920><c> to</c><00:01:52.979><c> be</c><00:01:53.040><c> doing</c><00:01:53.159><c> text</c><00:01:53.399><c> generation</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
we're going to be doing text generation
 

00:01:54.000 --> 00:01:56.270 align:start position:0%
we're going to be doing text generation
we're<00:01:54.659><c> passing</c><00:01:55.020><c> in</c><00:01:55.079><c> the</c><00:01:55.259><c> base</c><00:01:55.380><c> model</c><00:01:55.619><c> passing</c>

00:01:56.270 --> 00:01:56.280 align:start position:0%
we're passing in the base model passing
 

00:01:56.280 --> 00:01:58.249 align:start position:0%
we're passing in the base model passing
in<00:01:56.399><c> the</c><00:01:56.520><c> tokenizer</c><00:01:57.180><c> setting</c><00:01:57.720><c> a</c><00:01:57.780><c> Max</c><00:01:57.960><c> link</c>

00:01:58.249 --> 00:01:58.259 align:start position:0%
in the tokenizer setting a Max link
 

00:01:58.259 --> 00:02:00.410 align:start position:0%
in the tokenizer setting a Max link
please<00:01:58.740><c> feel</c><00:01:58.920><c> free</c><00:01:59.100><c> to</c><00:01:59.280><c> change</c><00:01:59.640><c> these</c><00:02:00.180><c> ones</c>

00:02:00.410 --> 00:02:00.420 align:start position:0%
please feel free to change these ones
 

00:02:00.420 --> 00:02:03.230 align:start position:0%
please feel free to change these ones
temperature<00:02:00.960><c> and</c><00:02:01.740><c> the</c><00:02:01.979><c> top</c><00:02:02.100><c> P</c><00:02:02.399><c> repetition</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
temperature and the top P repetition
 

00:02:03.240 --> 00:02:05.209 align:start position:0%
temperature and the top P repetition
penalty<00:02:03.840><c> all</c><00:02:04.079><c> of</c><00:02:04.200><c> these</c><00:02:04.380><c> can</c><00:02:04.560><c> be</c><00:02:04.680><c> you</c><00:02:04.920><c> can</c><00:02:05.100><c> play</c>

00:02:05.209 --> 00:02:05.219 align:start position:0%
penalty all of these can be you can play
 

00:02:05.219 --> 00:02:06.649 align:start position:0%
penalty all of these can be you can play
around<00:02:05.340><c> with</c><00:02:05.640><c> them</c><00:02:05.759><c> and</c><00:02:06.060><c> see</c><00:02:06.180><c> how</c><00:02:06.360><c> they</c><00:02:06.479><c> go</c>

00:02:06.649 --> 00:02:06.659 align:start position:0%
around with them and see how they go
 

00:02:06.659 --> 00:02:09.710 align:start position:0%
around with them and see how they go
I've<00:02:06.960><c> just</c><00:02:07.140><c> got</c><00:02:07.320><c> a</c><00:02:07.500><c> small</c><00:02:07.979><c> utility</c><00:02:08.720><c> function</c>

00:02:09.710 --> 00:02:09.720 align:start position:0%
I've just got a small utility function
 

00:02:09.720 --> 00:02:11.809 align:start position:0%
I've just got a small utility function
here<00:02:10.080><c> it</c><00:02:10.440><c> was</c><00:02:10.560><c> written</c><00:02:10.860><c> by</c><00:02:11.099><c> a</c><00:02:11.280><c> language</c><00:02:11.459><c> model</c>

00:02:11.809 --> 00:02:11.819 align:start position:0%
here it was written by a language model
 

00:02:11.819 --> 00:02:13.550 align:start position:0%
here it was written by a language model
to<00:02:12.300><c> help</c><00:02:12.480><c> just</c><00:02:12.599><c> make</c><00:02:12.780><c> the</c><00:02:12.959><c> text</c><00:02:13.080><c> look</c><00:02:13.319><c> a</c><00:02:13.500><c> bit</c>

00:02:13.550 --> 00:02:13.560 align:start position:0%
to help just make the text look a bit
 

00:02:13.560 --> 00:02:15.589 align:start position:0%
to help just make the text look a bit
nicer<00:02:13.980><c> and</c><00:02:14.459><c> then</c><00:02:14.580><c> we're</c><00:02:14.819><c> just</c><00:02:15.000><c> going</c><00:02:15.120><c> into</c><00:02:15.360><c> the</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
nicer and then we're just going into the
 

00:02:15.599 --> 00:02:16.850 align:start position:0%
nicer and then we're just going into the
generation

00:02:16.850 --> 00:02:16.860 align:start position:0%
generation
 

00:02:16.860 --> 00:02:18.890 align:start position:0%
generation
and<00:02:17.340><c> you</c><00:02:17.459><c> can</c><00:02:17.580><c> see</c><00:02:17.700><c> that</c><00:02:17.879><c> okay</c><00:02:18.120><c> one</c><00:02:18.480><c> of</c><00:02:18.660><c> the</c><00:02:18.720><c> big</c>

00:02:18.890 --> 00:02:18.900 align:start position:0%
and you can see that okay one of the big
 

00:02:18.900 --> 00:02:21.410 align:start position:0%
and you can see that okay one of the big
issues<00:02:19.379><c> that</c><00:02:19.920><c> I</c><00:02:20.459><c> had</c><00:02:20.580><c> getting</c><00:02:20.819><c> this</c><00:02:21.180><c> working</c>

00:02:21.410 --> 00:02:21.420 align:start position:0%
issues that I had getting this working
 

00:02:21.420 --> 00:02:24.650 align:start position:0%
issues that I had getting this working
was<00:02:22.140><c> actually</c><00:02:22.500><c> not</c><00:02:23.160><c> the</c><00:02:23.580><c> technical</c><00:02:24.180><c> things</c><00:02:24.420><c> at</c>

00:02:24.650 --> 00:02:24.660 align:start position:0%
was actually not the technical things at
 

00:02:24.660 --> 00:02:27.350 align:start position:0%
was actually not the technical things at
all<00:02:24.840><c> it</c><00:02:25.080><c> was</c><00:02:25.319><c> just</c><00:02:25.560><c> the</c><00:02:26.220><c> the</c><00:02:26.459><c> this</c><00:02:27.120><c> model</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
all it was just the the this model
 

00:02:27.360 --> 00:02:30.830 align:start position:0%
all it was just the the this model
really<00:02:28.200><c> relies</c><00:02:28.800><c> on</c><00:02:29.220><c> this</c><00:02:29.640><c> prompt</c><00:02:30.360><c> at</c><00:02:30.660><c> the</c>

00:02:30.830 --> 00:02:30.840 align:start position:0%
really relies on this prompt at the
 

00:02:30.840 --> 00:02:32.990 align:start position:0%
really relies on this prompt at the
start<00:02:31.020><c> so</c><00:02:31.920><c> this</c><00:02:32.160><c> prompt</c><00:02:32.459><c> where</c><00:02:32.700><c> you</c><00:02:32.879><c> say</c>

00:02:32.990 --> 00:02:33.000 align:start position:0%
start so this prompt where you say
 

00:02:33.000 --> 00:02:35.809 align:start position:0%
start so this prompt where you say
beginning<00:02:33.300><c> of</c><00:02:33.660><c> conversation</c><00:02:34.260><c> and</c><00:02:35.160><c> then</c><00:02:35.340><c> user</c>

00:02:35.809 --> 00:02:35.819 align:start position:0%
beginning of conversation and then user
 

00:02:35.819 --> 00:02:37.490 align:start position:0%
beginning of conversation and then user
and<00:02:36.060><c> then</c><00:02:36.180><c> you</c><00:02:36.300><c> pass</c><00:02:36.480><c> in</c><00:02:36.660><c> what</c><00:02:36.959><c> the</c><00:02:37.080><c> user</c><00:02:37.379><c> is</c>

00:02:37.490 --> 00:02:37.500 align:start position:0%
and then you pass in what the user is
 

00:02:37.500 --> 00:02:39.770 align:start position:0%
and then you pass in what the user is
going<00:02:37.620><c> to</c><00:02:37.800><c> say</c><00:02:37.920><c> is</c><00:02:38.520><c> crucial</c><00:02:38.940><c> I</c><00:02:39.360><c> encourage</c><00:02:39.660><c> you</c>

00:02:39.770 --> 00:02:39.780 align:start position:0%
going to say is crucial I encourage you
 

00:02:39.780 --> 00:02:41.809 align:start position:0%
going to say is crucial I encourage you
to<00:02:39.959><c> try</c><00:02:40.200><c> it</c><00:02:40.379><c> without</c><00:02:40.560><c> it</c><00:02:40.980><c> and</c><00:02:41.220><c> you'll</c><00:02:41.400><c> see</c><00:02:41.580><c> that</c>

00:02:41.809 --> 00:02:41.819 align:start position:0%
to try it without it and you'll see that
 

00:02:41.819 --> 00:02:44.390 align:start position:0%
to try it without it and you'll see that
it's<00:02:42.060><c> very</c><00:02:42.360><c> hit</c><00:02:42.720><c> and</c><00:02:42.900><c> miss</c><00:02:43.080><c> without</c><00:02:43.500><c> it</c><00:02:43.860><c> in</c>

00:02:44.390 --> 00:02:44.400 align:start position:0%
it's very hit and miss without it in
 

00:02:44.400 --> 00:02:45.830 align:start position:0%
it's very hit and miss without it in
fact<00:02:44.519><c> so</c><00:02:44.819><c> much</c><00:02:44.940><c> so</c><00:02:45.060><c> like</c><00:02:45.300><c> there's</c><00:02:45.540><c> a</c><00:02:45.720><c> question</c>

00:02:45.830 --> 00:02:45.840 align:start position:0%
fact so much so like there's a question
 

00:02:45.840 --> 00:02:48.589 align:start position:0%
fact so much so like there's a question
I<00:02:46.080><c> put</c><00:02:46.260><c> down</c><00:02:46.440><c> here</c><00:02:46.739><c> what</c><00:02:47.519><c> is</c><00:02:47.700><c> the</c><00:02:47.879><c> capital</c><00:02:48.060><c> of</c>

00:02:48.589 --> 00:02:48.599 align:start position:0%
I put down here what is the capital of
 

00:02:48.599 --> 00:02:51.710 align:start position:0%
I put down here what is the capital of
England<00:02:49.080><c> and</c><00:02:49.920><c> when</c><00:02:50.280><c> I</c><00:02:50.459><c> put</c><00:02:50.760><c> this</c><00:02:51.000><c> in</c><00:02:51.120><c> it</c><00:02:51.480><c> gets</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
England and when I put this in it gets
 

00:02:51.720 --> 00:02:53.690 align:start position:0%
England and when I put this in it gets
it<00:02:51.840><c> perfectly</c><00:02:52.200><c> correct</c><00:02:52.680><c> when</c><00:02:53.099><c> I</c><00:02:53.340><c> don't</c><00:02:53.459><c> put</c>

00:02:53.690 --> 00:02:53.700 align:start position:0%
it perfectly correct when I don't put
 

00:02:53.700 --> 00:02:56.150 align:start position:0%
it perfectly correct when I don't put
this<00:02:53.940><c> in</c><00:02:54.120><c> it</c><00:02:54.840><c> goes</c><00:02:55.620><c> in</c><00:02:55.739><c> a</c><00:02:55.860><c> whole</c><00:02:55.980><c> different</c>

00:02:56.150 --> 00:02:56.160 align:start position:0%
this in it goes in a whole different
 

00:02:56.160 --> 00:02:59.630 align:start position:0%
this in it goes in a whole different
direction<00:02:56.599><c> so</c><00:02:57.599><c> it</c><00:02:57.900><c> has</c><00:02:58.319><c> been</c><00:02:58.620><c> reading</c><00:02:59.519><c> their</c>

00:02:59.630 --> 00:02:59.640 align:start position:0%
direction so it has been reading their
 

00:02:59.640 --> 00:03:01.009 align:start position:0%
direction so it has been reading their
docs<00:02:59.940><c> and</c><00:03:00.060><c> stuff</c><00:03:00.180><c> like</c><00:03:00.360><c> that</c><00:03:00.480><c> this</c><00:03:00.660><c> is</c><00:03:00.780><c> how</c>

00:03:01.009 --> 00:03:01.019 align:start position:0%
docs and stuff like that this is how
 

00:03:01.019 --> 00:03:03.050 align:start position:0%
docs and stuff like that this is how
they<00:03:01.260><c> basically</c><00:03:01.500><c> train</c><00:03:01.800><c> the</c><00:03:02.040><c> model</c><00:03:02.160><c> so</c><00:03:02.819><c> you</c>

00:03:03.050 --> 00:03:03.060 align:start position:0%
they basically train the model so you
 

00:03:03.060 --> 00:03:04.850 align:start position:0%
they basically train the model so you
need<00:03:03.239><c> to</c><00:03:03.480><c> basically</c><00:03:03.780><c> put</c><00:03:04.260><c> this</c><00:03:04.500><c> in</c><00:03:04.680><c> your</c>

00:03:04.850 --> 00:03:04.860 align:start position:0%
need to basically put this in your
 

00:03:04.860 --> 00:03:06.589 align:start position:0%
need to basically put this in your
prompt<00:03:05.220><c> as</c><00:03:05.580><c> you're</c><00:03:05.700><c> fitting</c><00:03:06.000><c> it</c><00:03:06.120><c> in</c><00:03:06.239><c> we</c><00:03:06.480><c> can</c>

00:03:06.589 --> 00:03:06.599 align:start position:0%
prompt as you're fitting it in we can
 

00:03:06.599 --> 00:03:08.030 align:start position:0%
prompt as you're fitting it in we can
see<00:03:06.720><c> that</c><00:03:06.840><c> okay</c><00:03:07.019><c> asking</c><00:03:07.500><c> it</c><00:03:07.620><c> what's</c><00:03:07.860><c> the</c>

00:03:08.030 --> 00:03:08.040 align:start position:0%
see that okay asking it what's the
 

00:03:08.040 --> 00:03:09.949 align:start position:0%
see that okay asking it what's the
difference<00:03:08.099><c> between</c><00:03:08.400><c> llamas</c><00:03:08.940><c> alpacas</c><00:03:09.599><c> and</c>

00:03:09.949 --> 00:03:09.959 align:start position:0%
difference between llamas alpacas and
 

00:03:09.959 --> 00:03:13.070 align:start position:0%
difference between llamas alpacas and
koalas<00:03:10.560><c> it</c><00:03:10.920><c> gives</c><00:03:11.159><c> this</c><00:03:11.340><c> really</c><00:03:11.519><c> nice</c><00:03:11.879><c> list</c><00:03:12.540><c> of</c>

00:03:13.070 --> 00:03:13.080 align:start position:0%
koalas it gives this really nice list of
 

00:03:13.080 --> 00:03:15.130 align:start position:0%
koalas it gives this really nice list of
things

00:03:15.130 --> 00:03:15.140 align:start position:0%
things
 

00:03:15.140 --> 00:03:17.990 align:start position:0%
things
and<00:03:16.140><c> it</c><00:03:16.260><c> basically</c><00:03:16.560><c> goes</c><00:03:16.920><c> through</c><00:03:17.099><c> some</c><00:03:17.519><c> the</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
and it basically goes through some the
 

00:03:18.000 --> 00:03:20.449 align:start position:0%
and it basically goes through some the
details<00:03:18.480><c> about</c><00:03:18.659><c> how</c><00:03:19.379><c> they're</c><00:03:19.560><c> different</c>

00:03:20.449 --> 00:03:20.459 align:start position:0%
details about how they're different
 

00:03:20.459 --> 00:03:22.190 align:start position:0%
details about how they're different
Etc<00:03:20.640><c> you</c><00:03:21.120><c> can</c><00:03:21.239><c> see</c><00:03:21.300><c> that</c><00:03:21.480><c> this</c><00:03:21.599><c> took</c><00:03:21.840><c> a</c><00:03:21.959><c> bit</c><00:03:22.019><c> of</c>

00:03:22.190 --> 00:03:22.200 align:start position:0%
Etc you can see that this took a bit of
 

00:03:22.200 --> 00:03:25.790 align:start position:0%
Etc you can see that this took a bit of
time<00:03:22.560><c> to</c><00:03:23.099><c> to</c><00:03:23.280><c> generate</c><00:03:23.879><c> so</c><00:03:24.540><c> on</c><00:03:24.900><c> the</c><00:03:25.019><c> T4</c><00:03:25.379><c> you</c>

00:03:25.790 --> 00:03:25.800 align:start position:0%
time to to generate so on the T4 you
 

00:03:25.800 --> 00:03:27.410 align:start position:0%
time to to generate so on the T4 you
will<00:03:26.040><c> have</c><00:03:26.280><c> to</c><00:03:26.459><c> wait</c><00:03:26.640><c> a</c><00:03:26.879><c> little</c><00:03:26.940><c> bit</c><00:03:27.120><c> for</c>

00:03:27.410 --> 00:03:27.420 align:start position:0%
will have to wait a little bit for
 

00:03:27.420 --> 00:03:29.089 align:start position:0%
will have to wait a little bit for
things<00:03:27.659><c> to</c><00:03:27.959><c> generate</c>

00:03:29.089 --> 00:03:29.099 align:start position:0%
things to generate
 

00:03:29.099 --> 00:03:31.190 align:start position:0%
things to generate
all<00:03:29.760><c> right</c><00:03:29.760><c> okay</c><00:03:30.180><c> the</c><00:03:30.659><c> standard</c><00:03:30.900><c> one</c><00:03:31.019><c> that</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
all right okay the standard one that
 

00:03:31.200 --> 00:03:33.229 align:start position:0%
all right okay the standard one that
I've<00:03:31.319><c> been</c><00:03:31.500><c> using</c><00:03:31.800><c> a</c><00:03:31.920><c> lot</c><00:03:32.040><c> laterally</c><00:03:32.519><c> is</c><00:03:32.879><c> write</c>

00:03:33.229 --> 00:03:33.239 align:start position:0%
I've been using a lot laterally is write
 

00:03:33.239 --> 00:03:35.809 align:start position:0%
I've been using a lot laterally is write
a<00:03:33.480><c> short</c><00:03:33.659><c> note</c><00:03:33.900><c> or</c><00:03:34.200><c> write</c><00:03:34.440><c> an</c><00:03:34.620><c> email</c><00:03:34.800><c> to</c><00:03:35.400><c> Sam</c>

00:03:35.809 --> 00:03:35.819 align:start position:0%
a short note or write an email to Sam
 

00:03:35.819 --> 00:03:38.149 align:start position:0%
a short note or write an email to Sam
Oatman<00:03:36.360><c> giving</c><00:03:36.720><c> reasons</c><00:03:37.379><c> to</c><00:03:37.560><c> open</c><00:03:37.739><c> source</c>

00:03:38.149 --> 00:03:38.159 align:start position:0%
Oatman giving reasons to open source
 

00:03:38.159 --> 00:03:40.610 align:start position:0%
Oatman giving reasons to open source
gpt4<00:03:38.879><c> this</c><00:03:39.360><c> model</c><00:03:39.480><c> does</c><00:03:39.780><c> really</c><00:03:40.019><c> well</c><00:03:40.200><c> at</c><00:03:40.440><c> this</c>

00:03:40.610 --> 00:03:40.620 align:start position:0%
gpt4 this model does really well at this
 

00:03:40.620 --> 00:03:43.369 align:start position:0%
gpt4 this model does really well at this
it<00:03:41.159><c> basically</c><00:03:41.459><c> goes</c><00:03:42.000><c> through</c><00:03:42.299><c> a</c><00:03:42.959><c> nice</c><00:03:43.080><c> sort</c><00:03:43.260><c> of</c>

00:03:43.369 --> 00:03:43.379 align:start position:0%
it basically goes through a nice sort of
 

00:03:43.379 --> 00:03:45.890 align:start position:0%
it basically goes through a nice sort of
coherent<00:03:43.860><c> argument</c><00:03:44.340><c> it</c><00:03:45.060><c> talks</c><00:03:45.360><c> about</c><00:03:45.480><c> like</c>

00:03:45.890 --> 00:03:45.900 align:start position:0%
coherent argument it talks about like
 

00:03:45.900 --> 00:03:47.390 align:start position:0%
coherent argument it talks about like
different<00:03:46.319><c> parts</c><00:03:46.560><c> of</c><00:03:46.739><c> it</c><00:03:46.920><c> and</c><00:03:47.159><c> stuff</c><00:03:47.280><c> like</c>

00:03:47.390 --> 00:03:47.400 align:start position:0%
different parts of it and stuff like
 

00:03:47.400 --> 00:03:49.250 align:start position:0%
different parts of it and stuff like
that<00:03:47.519><c> it's</c><00:03:47.760><c> got</c><00:03:48.000><c> it</c><00:03:48.120><c> to</c><00:03:48.360><c> paragraphs</c><00:03:48.900><c> about</c>

00:03:49.250 --> 00:03:49.260 align:start position:0%
that it's got it to paragraphs about
 

00:03:49.260 --> 00:03:51.410 align:start position:0%
that it's got it to paragraphs about
what's<00:03:49.620><c> going</c><00:03:49.860><c> on</c><00:03:50.280><c> Etc</c><00:03:50.459><c> play</c><00:03:51.000><c> around</c><00:03:51.180><c> with</c>

00:03:51.410 --> 00:03:51.420 align:start position:0%
what's going on Etc play around with
 

00:03:51.420 --> 00:03:53.089 align:start position:0%
what's going on Etc play around with
these<00:03:51.659><c> it's</c><00:03:52.019><c> been</c><00:03:52.200><c> different</c><00:03:52.379><c> each</c><00:03:52.680><c> time</c><00:03:52.860><c> I've</c>

00:03:53.089 --> 00:03:53.099 align:start position:0%
these it's been different each time I've
 

00:03:53.099 --> 00:03:54.890 align:start position:0%
these it's been different each time I've
generated<00:03:53.580><c> it</c><00:03:53.819><c> too</c><00:03:54.000><c> that's</c><00:03:54.299><c> one</c><00:03:54.540><c> of</c><00:03:54.659><c> the</c><00:03:54.780><c> key</c>

00:03:54.890 --> 00:03:54.900 align:start position:0%
generated it too that's one of the key
 

00:03:54.900 --> 00:03:56.750 align:start position:0%
generated it too that's one of the key
things<00:03:55.080><c> I</c><00:03:55.440><c> mentioned</c><00:03:55.799><c> already</c><00:03:55.980><c> asking</c><00:03:56.580><c> just</c>

00:03:56.750 --> 00:03:56.760 align:start position:0%
things I mentioned already asking just
 

00:03:56.760 --> 00:03:59.869 align:start position:0%
things I mentioned already asking just
simple<00:03:57.000><c> questions</c><00:03:57.420><c> that</c><00:03:58.379><c> are</c><00:03:58.620><c> quite</c><00:03:58.860><c> short</c><00:03:59.220><c> if</c>

00:03:59.869 --> 00:03:59.879 align:start position:0%
simple questions that are quite short if
 

00:03:59.879 --> 00:04:02.449 align:start position:0%
simple questions that are quite short if
you<00:04:00.060><c> don't</c><00:04:00.239><c> have</c><00:04:00.540><c> this</c><00:04:01.019><c> prompt</c><00:04:01.860><c> part</c><00:04:02.040><c> here</c>

00:04:02.449 --> 00:04:02.459 align:start position:0%
you don't have this prompt part here
 

00:04:02.459 --> 00:04:04.610 align:start position:0%
you don't have this prompt part here
you'll<00:04:03.120><c> find</c><00:04:03.299><c> that</c><00:04:03.480><c> it</c><00:04:03.599><c> tends</c><00:04:03.840><c> to</c><00:04:03.959><c> not</c><00:04:04.140><c> do</c><00:04:04.379><c> very</c>

00:04:04.610 --> 00:04:04.620 align:start position:0%
you'll find that it tends to not do very
 

00:04:04.620 --> 00:04:06.949 align:start position:0%
you'll find that it tends to not do very
well<00:04:04.799><c> at</c><00:04:05.040><c> all</c><00:04:05.159><c> whereas</c><00:04:05.940><c> if</c><00:04:06.180><c> you</c><00:04:06.360><c> do</c><00:04:06.480><c> suddenly</c>

00:04:06.949 --> 00:04:06.959 align:start position:0%
well at all whereas if you do suddenly
 

00:04:06.959 --> 00:04:09.289 align:start position:0%
well at all whereas if you do suddenly
you<00:04:07.260><c> get</c><00:04:07.379><c> this</c><00:04:07.680><c> nice</c><00:04:07.920><c> key</c><00:04:08.340><c> answer</c><00:04:08.640><c> another</c>

00:04:09.289 --> 00:04:09.299 align:start position:0%
you get this nice key answer another
 

00:04:09.299 --> 00:04:11.570 align:start position:0%
you get this nice key answer another
interesting<00:04:10.140><c> little</c><00:04:10.560><c> observation</c><00:04:11.099><c> for</c><00:04:11.459><c> you</c>

00:04:11.570 --> 00:04:11.580 align:start position:0%
interesting little observation for you
 

00:04:11.580 --> 00:04:14.149 align:start position:0%
interesting little observation for you
too<00:04:11.879><c> is</c><00:04:12.360><c> that</c><00:04:12.659><c> just</c><00:04:13.080><c> putting</c><00:04:13.500><c> the</c><00:04:13.680><c> new</c><00:04:13.860><c> line</c>

00:04:14.149 --> 00:04:14.159 align:start position:0%
too is that just putting the new line
 

00:04:14.159 --> 00:04:16.189 align:start position:0%
too is that just putting the new line
character<00:04:14.580><c> at</c><00:04:14.879><c> the</c><00:04:15.060><c> end</c><00:04:15.120><c> there</c><00:04:15.540><c> totally</c>

00:04:16.189 --> 00:04:16.199 align:start position:0%
character at the end there totally
 

00:04:16.199 --> 00:04:19.550 align:start position:0%
character at the end there totally
changes<00:04:16.799><c> how</c><00:04:17.040><c> the</c><00:04:17.220><c> model</c><00:04:17.459><c> reacts</c><00:04:18.299><c> so</c><00:04:19.139><c> if</c><00:04:19.380><c> I</c>

00:04:19.550 --> 00:04:19.560 align:start position:0%
changes how the model reacts so if I
 

00:04:19.560 --> 00:04:22.009 align:start position:0%
changes how the model reacts so if I
don't<00:04:19.680><c> put</c><00:04:19.919><c> a</c><00:04:20.040><c> new</c><00:04:20.160><c> line</c><00:04:20.340><c> character</c><00:04:20.639><c> there</c><00:04:21.120><c> it</c>

00:04:22.009 --> 00:04:22.019 align:start position:0%
don't put a new line character there it
 

00:04:22.019 --> 00:04:24.590 align:start position:0%
don't put a new line character there it
will<00:04:22.199><c> basically</c><00:04:22.740><c> not</c><00:04:23.340><c> have</c><00:04:23.639><c> this</c><00:04:23.940><c> word</c><00:04:24.180><c> answer</c>

00:04:24.590 --> 00:04:24.600 align:start position:0%
will basically not have this word answer
 

00:04:24.600 --> 00:04:26.450 align:start position:0%
will basically not have this word answer
here<00:04:25.020><c> it</c><00:04:25.500><c> will</c><00:04:25.680><c> just</c><00:04:25.860><c> basically</c><00:04:26.100><c> treat</c><00:04:26.340><c> it</c>

00:04:26.450 --> 00:04:26.460 align:start position:0%
here it will just basically treat it
 

00:04:26.460 --> 00:04:28.129 align:start position:0%
here it will just basically treat it
like<00:04:26.699><c> it's</c><00:04:26.880><c> a</c><00:04:27.060><c> conversation</c><00:04:27.419><c> if</c><00:04:27.720><c> I</c><00:04:27.840><c> put</c><00:04:28.020><c> the</c>

00:04:28.129 --> 00:04:28.139 align:start position:0%
like it's a conversation if I put the
 

00:04:28.139 --> 00:04:30.350 align:start position:0%
like it's a conversation if I put the
new<00:04:28.320><c> line</c><00:04:28.500><c> character</c><00:04:28.800><c> there</c><00:04:29.040><c> then</c><00:04:29.880><c> I</c><00:04:30.120><c> tend</c><00:04:30.300><c> to</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
new line character there then I tend to
 

00:04:30.360 --> 00:04:33.469 align:start position:0%
new line character there then I tend to
get<00:04:30.540><c> either</c><00:04:31.020><c> the</c><00:04:31.320><c> word</c><00:04:31.440><c> AI</c><00:04:32.100><c> colon</c><00:04:32.759><c> or</c><00:04:33.180><c> answer</c>

00:04:33.469 --> 00:04:33.479 align:start position:0%
get either the word AI colon or answer
 

00:04:33.479 --> 00:04:35.749 align:start position:0%
get either the word AI colon or answer
colon<00:04:34.139><c> my</c><00:04:34.620><c> guess</c><00:04:34.800><c> is</c><00:04:34.919><c> this</c><00:04:35.220><c> is</c><00:04:35.400><c> basically</c>

00:04:35.749 --> 00:04:35.759 align:start position:0%
colon my guess is this is basically
 

00:04:35.759 --> 00:04:37.370 align:start position:0%
colon my guess is this is basically
because<00:04:36.300><c> they've</c><00:04:36.660><c> trained</c><00:04:36.960><c> from</c><00:04:37.139><c> different</c>

00:04:37.370 --> 00:04:37.380 align:start position:0%
because they've trained from different
 

00:04:37.380 --> 00:04:39.170 align:start position:0%
because they've trained from different
things<00:04:37.680><c> on</c><00:04:37.919><c> different</c><00:04:38.160><c> data</c><00:04:38.580><c> sets</c><00:04:38.880><c> there</c>

00:04:39.170 --> 00:04:39.180 align:start position:0%
things on different data sets there
 

00:04:39.180 --> 00:04:41.870 align:start position:0%
things on different data sets there
here's<00:04:39.720><c> another</c><00:04:39.780><c> one</c><00:04:40.080><c> so</c><00:04:40.500><c> this</c><00:04:40.979><c> koala</c><00:04:41.639><c> has</c>

00:04:41.870 --> 00:04:41.880 align:start position:0%
here's another one so this koala has
 

00:04:41.880 --> 00:04:44.749 align:start position:0%
here's another one so this koala has
been<00:04:42.060><c> trained</c><00:04:42.479><c> on</c><00:04:43.020><c> story</c><00:04:43.440><c> writing</c><00:04:44.040><c> and</c><00:04:44.400><c> poem</c>

00:04:44.749 --> 00:04:44.759 align:start position:0%
been trained on story writing and poem
 

00:04:44.759 --> 00:04:46.370 align:start position:0%
been trained on story writing and poem
writing<00:04:45.180><c> and</c><00:04:45.300><c> stuff</c><00:04:45.479><c> like</c><00:04:45.600><c> that</c><00:04:45.780><c> I</c><00:04:46.020><c> asked</c><00:04:46.320><c> it</c>

00:04:46.370 --> 00:04:46.380 align:start position:0%
writing and stuff like that I asked it
 

00:04:46.380 --> 00:04:48.770 align:start position:0%
writing and stuff like that I asked it
to<00:04:46.560><c> write</c><00:04:46.740><c> a</c><00:04:46.979><c> story</c><00:04:47.580><c> about</c><00:04:47.880><c> a</c><00:04:48.180><c> koala</c><00:04:48.600><c> playing</c>

00:04:48.770 --> 00:04:48.780 align:start position:0%
to write a story about a koala playing
 

00:04:48.780 --> 00:04:51.590 align:start position:0%
to write a story about a koala playing
pool<00:04:49.139><c> and</c><00:04:49.860><c> beating</c><00:04:50.220><c> all</c><00:04:50.340><c> the</c><00:04:50.460><c> camel</c><00:04:50.759><c> Lids</c><00:04:51.120><c> I</c><00:04:51.479><c> do</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
pool and beating all the camel Lids I do
 

00:04:51.600 --> 00:04:52.850 align:start position:0%
pool and beating all the camel Lids I do
think<00:04:51.780><c> that</c><00:04:51.960><c> this</c><00:04:52.080><c> is</c><00:04:52.199><c> probably</c><00:04:52.380><c> better</c><00:04:52.620><c> than</c>

00:04:52.850 --> 00:04:52.860 align:start position:0%
think that this is probably better than
 

00:04:52.860 --> 00:04:55.129 align:start position:0%
think that this is probably better than
a<00:04:53.040><c> lot</c><00:04:53.160><c> of</c><00:04:53.280><c> the</c><00:04:53.400><c> other</c><00:04:53.639><c> camel</c><00:04:54.419><c> lid</c><00:04:54.660><c> models</c><00:04:55.020><c> that</c>

00:04:55.129 --> 00:04:55.139 align:start position:0%
a lot of the other camel lid models that
 

00:04:55.139 --> 00:04:57.050 align:start position:0%
a lot of the other camel lid models that
we've<00:04:55.320><c> played</c><00:04:55.620><c> with</c><00:04:55.800><c> and</c><00:04:56.220><c> sure</c><00:04:56.400><c> enough</c><00:04:56.580><c> here</c>

00:04:57.050 --> 00:04:57.060 align:start position:0%
we've played with and sure enough here
 

00:04:57.060 --> 00:04:59.390 align:start position:0%
we've played with and sure enough here
you<00:04:57.300><c> notice</c><00:04:57.540><c> that</c><00:04:57.660><c> I</c><00:04:57.840><c> didn't</c><00:04:58.020><c> put</c><00:04:58.380><c> the</c><00:04:58.800><c> the</c><00:04:59.160><c> new</c>

00:04:59.390 --> 00:04:59.400 align:start position:0%
you notice that I didn't put the the new
 

00:04:59.400 --> 00:05:01.129 align:start position:0%
you notice that I didn't put the the new
line<00:04:59.580><c> character</c><00:04:59.940><c> at</c><00:05:00.240><c> the</c><00:05:00.419><c> end</c>

00:05:01.129 --> 00:05:01.139 align:start position:0%
line character at the end
 

00:05:01.139 --> 00:05:04.670 align:start position:0%
line character at the end
and<00:05:01.979><c> it</c><00:05:02.400><c> basically</c><00:05:02.759><c> then</c><00:05:03.479><c> we</c><00:05:03.960><c> don't</c><00:05:04.080><c> see</c><00:05:04.380><c> like</c>

00:05:04.670 --> 00:05:04.680 align:start position:0%
and it basically then we don't see like
 

00:05:04.680 --> 00:05:06.469 align:start position:0%
and it basically then we don't see like
an<00:05:04.919><c> answer</c><00:05:05.100><c> or</c><00:05:05.460><c> anything</c><00:05:05.639><c> like</c><00:05:05.880><c> that</c><00:05:06.000><c> we</c><00:05:06.300><c> just</c>

00:05:06.469 --> 00:05:06.479 align:start position:0%
an answer or anything like that we just
 

00:05:06.479 --> 00:05:08.270 align:start position:0%
an answer or anything like that we just
see<00:05:06.660><c> what</c><00:05:06.960><c> we</c><00:05:07.139><c> wrote</c><00:05:07.380><c> and</c><00:05:07.680><c> then</c><00:05:07.800><c> it</c><00:05:07.919><c> goes</c><00:05:08.160><c> into</c>

00:05:08.270 --> 00:05:08.280 align:start position:0%
see what we wrote and then it goes into
 

00:05:08.280 --> 00:05:10.790 align:start position:0%
see what we wrote and then it goes into
generation<00:05:08.880><c> once</c><00:05:09.479><c> upon</c><00:05:09.720><c> a</c><00:05:09.840><c> time</c><00:05:09.960><c> in</c><00:05:10.380><c> a</c><00:05:10.500><c> far-off</c>

00:05:10.790 --> 00:05:10.800 align:start position:0%
generation once upon a time in a far-off
 

00:05:10.800 --> 00:05:13.310 align:start position:0%
generation once upon a time in a far-off
land<00:05:11.040><c> where</c><00:05:11.340><c> animals</c><00:05:11.699><c> could</c><00:05:12.000><c> talk</c><00:05:12.360><c> they</c><00:05:12.960><c> lived</c>

00:05:13.310 --> 00:05:13.320 align:start position:0%
land where animals could talk they lived
 

00:05:13.320 --> 00:05:15.590 align:start position:0%
land where animals could talk they lived
a<00:05:13.440><c> koala</c><00:05:13.800><c> named</c><00:05:14.160><c> Coco</c><00:05:14.460><c> Coco</c><00:05:14.940><c> was</c><00:05:15.180><c> known</c><00:05:15.360><c> for</c>

00:05:15.590 --> 00:05:15.600 align:start position:0%
a koala named Coco Coco was known for
 

00:05:15.600 --> 00:05:17.689 align:start position:0%
a koala named Coco Coco was known for
his<00:05:16.199><c> love</c><00:05:16.380><c> of</c><00:05:16.620><c> playing</c><00:05:16.800><c> pool</c><00:05:17.160><c> and</c><00:05:17.520><c> his</c>

00:05:17.689 --> 00:05:17.699 align:start position:0%
his love of playing pool and his
 

00:05:17.699 --> 00:05:19.790 align:start position:0%
his love of playing pool and his
exceptional<00:05:18.240><c> skills</c><00:05:18.720><c> at</c><00:05:18.900><c> it</c><00:05:19.020><c> one</c><00:05:19.380><c> day</c><00:05:19.560><c> he</c>

00:05:19.790 --> 00:05:19.800 align:start position:0%
exceptional skills at it one day he
 

00:05:19.800 --> 00:05:21.350 align:start position:0%
exceptional skills at it one day he
decided<00:05:20.160><c> to</c><00:05:20.280><c> challenge</c><00:05:20.580><c> all</c><00:05:21.060><c> the</c><00:05:21.180><c> other</c>

00:05:21.350 --> 00:05:21.360 align:start position:0%
decided to challenge all the other
 

00:05:21.360 --> 00:05:23.270 align:start position:0%
decided to challenge all the other
animals<00:05:21.780><c> in</c><00:05:21.960><c> the</c><00:05:22.139><c> Kingdom</c><00:05:22.320><c> to</c><00:05:22.560><c> a</c><00:05:22.740><c> game</c><00:05:22.860><c> of</c><00:05:23.039><c> pool</c>

00:05:23.270 --> 00:05:23.280 align:start position:0%
animals in the Kingdom to a game of pool
 

00:05:23.280 --> 00:05:25.070 align:start position:0%
animals in the Kingdom to a game of pool
and<00:05:23.520><c> it</c><00:05:23.759><c> basically</c><00:05:24.060><c> goes</c><00:05:24.419><c> through</c><00:05:24.479><c> and</c><00:05:24.720><c> talks</c>

00:05:25.070 --> 00:05:25.080 align:start position:0%
and it basically goes through and talks
 

00:05:25.080 --> 00:05:27.650 align:start position:0%
and it basically goes through and talks
a<00:05:25.139><c> bit</c><00:05:25.259><c> about</c><00:05:25.440><c> Lola</c><00:05:25.979><c> the</c><00:05:26.340><c> Llama</c><00:05:26.699><c> Cami</c><00:05:27.479><c> the</c>

00:05:27.650 --> 00:05:27.660 align:start position:0%
a bit about Lola the Llama Cami the
 

00:05:27.660 --> 00:05:29.810 align:start position:0%
a bit about Lola the Llama Cami the
camel<00:05:28.139><c> I</c><00:05:28.620><c> don't</c><00:05:28.740><c> know</c><00:05:28.860><c> if</c><00:05:28.860><c> it's</c><00:05:29.039><c> got</c><00:05:29.160><c> an</c><00:05:29.280><c> alpaca</c>

00:05:29.810 --> 00:05:29.820 align:start position:0%
camel I don't know if it's got an alpaca
 

00:05:29.820 --> 00:05:31.370 align:start position:0%
camel I don't know if it's got an alpaca
in<00:05:30.000><c> there</c><00:05:30.180><c> maybe</c><00:05:30.479><c> we</c><00:05:30.720><c> should</c><00:05:30.780><c> have</c><00:05:30.960><c> told</c><00:05:31.080><c> it</c><00:05:31.199><c> to</c>

00:05:31.370 --> 00:05:31.380 align:start position:0%
in there maybe we should have told it to
 

00:05:31.380 --> 00:05:34.070 align:start position:0%
in there maybe we should have told it to
put<00:05:31.500><c> that</c><00:05:31.740><c> he</c><00:05:31.919><c> beats</c><00:05:32.160><c> in</c><00:05:32.220><c> alpaca</c><00:05:32.759><c> as</c><00:05:33.000><c> well</c><00:05:33.180><c> but</c>

00:05:34.070 --> 00:05:34.080 align:start position:0%
put that he beats in alpaca as well but
 

00:05:34.080 --> 00:05:37.909 align:start position:0%
put that he beats in alpaca as well but
uh<00:05:34.740><c> you</c><00:05:35.340><c> can</c><00:05:35.460><c> see</c><00:05:35.600><c> again</c><00:05:36.600><c> very</c><00:05:36.960><c> coherent</c><00:05:37.560><c> text</c>

00:05:37.909 --> 00:05:37.919 align:start position:0%
uh you can see again very coherent text
 

00:05:37.919 --> 00:05:40.249 align:start position:0%
uh you can see again very coherent text
interesting<00:05:38.820><c> results</c><00:05:39.300><c> and</c><00:05:39.720><c> stuff</c><00:05:39.960><c> like</c><00:05:40.080><c> that</c>

00:05:40.249 --> 00:05:40.259 align:start position:0%
interesting results and stuff like that
 

00:05:40.259 --> 00:05:42.590 align:start position:0%
interesting results and stuff like that
so<00:05:40.500><c> experiment</c><00:05:40.979><c> with</c><00:05:41.280><c> this</c><00:05:41.460><c> as</c><00:05:41.639><c> well</c><00:05:41.820><c> finally</c>

00:05:42.590 --> 00:05:42.600 align:start position:0%
so experiment with this as well finally
 

00:05:42.600 --> 00:05:44.510 align:start position:0%
so experiment with this as well finally
the<00:05:43.020><c> last</c><00:05:43.199><c> one</c><00:05:43.380><c> was</c><00:05:43.620><c> I</c><00:05:43.800><c> wanted</c><00:05:43.979><c> to</c><00:05:44.160><c> basically</c>

00:05:44.510 --> 00:05:44.520 align:start position:0%
the last one was I wanted to basically
 

00:05:44.520 --> 00:05:47.150 align:start position:0%
the last one was I wanted to basically
ask<00:05:44.880><c> it</c><00:05:45.120><c> about</c><00:05:45.479><c> to</c><00:05:45.900><c> see</c><00:05:46.259><c> how</c><00:05:46.680><c> much</c><00:05:46.800><c> sort</c><00:05:47.039><c> of</c>

00:05:47.150 --> 00:05:47.160 align:start position:0%
ask it about to see how much sort of
 

00:05:47.160 --> 00:05:49.070 align:start position:0%
ask it about to see how much sort of
safety<00:05:47.580><c> stuff</c><00:05:47.759><c> is</c><00:05:48.000><c> in</c><00:05:48.180><c> it</c><00:05:48.300><c> we</c><00:05:48.600><c> looked</c><00:05:48.900><c> at</c><00:05:48.960><c> this</c>

00:05:49.070 --> 00:05:49.080 align:start position:0%
safety stuff is in it we looked at this
 

00:05:49.080 --> 00:05:50.570 align:start position:0%
safety stuff is in it we looked at this
in<00:05:49.259><c> the</c><00:05:49.380><c> last</c><00:05:49.560><c> video</c>

00:05:50.570 --> 00:05:50.580 align:start position:0%
in the last video
 

00:05:50.580 --> 00:05:52.790 align:start position:0%
in the last video
there<00:05:51.120><c> is</c><00:05:51.300><c> quite</c><00:05:51.419><c> a</c><00:05:51.539><c> bit</c><00:05:51.660><c> of</c><00:05:51.780><c> that</c><00:05:51.900><c> in</c><00:05:52.259><c> this</c><00:05:52.440><c> so</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
there is quite a bit of that in this so
 

00:05:52.800 --> 00:05:54.350 align:start position:0%
there is quite a bit of that in this so
if<00:05:53.039><c> you're</c><00:05:53.160><c> not</c><00:05:53.340><c> a</c><00:05:53.520><c> fan</c><00:05:53.580><c> of</c><00:05:53.759><c> that</c><00:05:53.880><c> and</c><00:05:54.060><c> I</c><00:05:54.240><c> know</c>

00:05:54.350 --> 00:05:54.360 align:start position:0%
if you're not a fan of that and I know
 

00:05:54.360 --> 00:05:55.850 align:start position:0%
if you're not a fan of that and I know
from<00:05:54.539><c> the</c><00:05:54.660><c> comments</c><00:05:54.960><c> some</c><00:05:55.139><c> of</c><00:05:55.259><c> you</c><00:05:55.380><c> are</c><00:05:55.500><c> not</c><00:05:55.680><c> a</c>

00:05:55.850 --> 00:05:55.860 align:start position:0%
from the comments some of you are not a
 

00:05:55.860 --> 00:05:57.890 align:start position:0%
from the comments some of you are not a
fan<00:05:55.919><c> of</c><00:05:56.100><c> that</c><00:05:56.340><c> experiment</c><00:05:57.240><c> we</c><00:05:57.479><c> can</c><00:05:57.660><c> definitely</c>

00:05:57.890 --> 00:05:57.900 align:start position:0%
fan of that experiment we can definitely
 

00:05:57.900 --> 00:06:00.110 align:start position:0%
fan of that experiment we can definitely
fine<00:05:58.199><c> tune</c><00:05:58.500><c> on</c><00:05:58.979><c> on</c><00:05:59.160><c> things</c><00:05:59.400><c> that</c><00:05:59.639><c> don't</c><00:05:59.880><c> have</c>

00:06:00.110 --> 00:06:00.120 align:start position:0%
fine tune on on things that don't have
 

00:06:00.120 --> 00:06:02.330 align:start position:0%
fine tune on on things that don't have
that<00:06:00.360><c> if</c><00:06:00.660><c> you</c><00:06:00.840><c> really</c><00:06:00.960><c> want</c><00:06:01.199><c> it</c><00:06:01.380><c> to</c><00:06:01.500><c> as</c><00:06:01.860><c> an</c><00:06:02.039><c> AI</c>

00:06:02.330 --> 00:06:02.340 align:start position:0%
that if you really want it to as an AI
 

00:06:02.340 --> 00:06:04.189 align:start position:0%
that if you really want it to as an AI
do<00:06:02.699><c> you</c><00:06:02.820><c> like</c><00:06:02.940><c> The</c><00:06:03.120><c> Simpsons</c><00:06:03.600><c> okay</c><00:06:03.780><c> it's</c>

00:06:04.189 --> 00:06:04.199 align:start position:0%
do you like The Simpsons okay it's
 

00:06:04.199 --> 00:06:05.990 align:start position:0%
do you like The Simpsons okay it's
responses<00:06:04.740><c> as</c><00:06:04.979><c> an</c><00:06:05.100><c> AI</c><00:06:05.400><c> I</c><00:06:05.520><c> don't</c><00:06:05.699><c> have</c><00:06:05.820><c> personal</c>

00:06:05.990 --> 00:06:06.000 align:start position:0%
responses as an AI I don't have personal
 

00:06:06.000 --> 00:06:08.390 align:start position:0%
responses as an AI I don't have personal
preferences<00:06:06.660><c> or</c><00:06:06.840><c> feelings</c><00:06:07.259><c> however</c><00:06:08.039><c> I</c><00:06:08.340><c> can</c>

00:06:08.390 --> 00:06:08.400 align:start position:0%
preferences or feelings however I can
 

00:06:08.400 --> 00:06:10.070 align:start position:0%
preferences or feelings however I can
tell<00:06:08.580><c> you</c><00:06:08.759><c> because</c><00:06:09.060><c> I</c><00:06:09.300><c> asked</c><00:06:09.660><c> it</c><00:06:09.720><c> a</c><00:06:09.900><c> little</c><00:06:09.960><c> bit</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
tell you because I asked it a little bit
 

00:06:10.080 --> 00:06:12.409 align:start position:0%
tell you because I asked it a little bit
about<00:06:10.320><c> and</c><00:06:10.740><c> I</c><00:06:10.919><c> spelled</c><00:06:11.280><c> it</c><00:06:11.340><c> wrong</c><00:06:11.520><c> I</c><00:06:11.880><c> asked</c><00:06:12.300><c> a</c>

00:06:12.409 --> 00:06:12.419 align:start position:0%
about and I spelled it wrong I asked a
 

00:06:12.419 --> 00:06:14.870 align:start position:0%
about and I spelled it wrong I asked a
little<00:06:12.539><c> bit</c><00:06:12.720><c> about</c><00:06:13.020><c> Homer</c><00:06:13.919><c> and</c><00:06:14.280><c> I</c><00:06:14.580><c> can</c><00:06:14.759><c> tell</c>

00:06:14.870 --> 00:06:14.880 align:start position:0%
little bit about Homer and I can tell
 

00:06:14.880 --> 00:06:16.249 align:start position:0%
little bit about Homer and I can tell
you<00:06:14.940><c> about</c><00:06:15.000><c> the</c><00:06:15.180><c> Simpsons</c><00:06:15.600><c> is</c><00:06:15.900><c> a</c><00:06:16.080><c> popular</c>

00:06:16.249 --> 00:06:16.259 align:start position:0%
you about the Simpsons is a popular
 

00:06:16.259 --> 00:06:18.950 align:start position:0%
you about the Simpsons is a popular
animated<00:06:16.800><c> TV</c><00:06:17.160><c> series</c><00:06:17.699><c> tells</c><00:06:18.360><c> us</c><00:06:18.419><c> got</c><00:06:18.660><c> some</c>

00:06:18.950 --> 00:06:18.960 align:start position:0%
animated TV series tells us got some
 

00:06:18.960 --> 00:06:20.689 align:start position:0%
animated TV series tells us got some
nice<00:06:19.080><c> facts</c><00:06:19.500><c> about</c><00:06:19.620><c> it</c><00:06:19.800><c> and</c><00:06:19.979><c> then</c><00:06:20.160><c> tells</c><00:06:20.460><c> us</c><00:06:20.520><c> a</c>

00:06:20.689 --> 00:06:20.699 align:start position:0%
nice facts about it and then tells us a
 

00:06:20.699 --> 00:06:24.050 align:start position:0%
nice facts about it and then tells us a
bit<00:06:20.759><c> about</c><00:06:20.880><c> Homer</c><00:06:21.479><c> in</c><00:06:21.780><c> there</c><00:06:21.960><c> uh</c><00:06:22.919><c> and</c><00:06:23.699><c> he</c><00:06:23.880><c> is</c>

00:06:24.050 --> 00:06:24.060 align:start position:0%
bit about Homer in there uh and he is
 

00:06:24.060 --> 00:06:26.510 align:start position:0%
bit about Homer in there uh and he is
known<00:06:24.300><c> for</c><00:06:24.600><c> his</c><00:06:24.840><c> catchphrase</c><00:06:25.620><c> and</c><00:06:26.039><c> his</c><00:06:26.280><c> love</c>

00:06:26.510 --> 00:06:26.520 align:start position:0%
known for his catchphrase and his love
 

00:06:26.520 --> 00:06:27.950 align:start position:0%
known for his catchphrase and his love
of<00:06:26.759><c> beer</c>

00:06:27.950 --> 00:06:27.960 align:start position:0%
of beer
 

00:06:27.960 --> 00:06:30.710 align:start position:0%
of beer
so<00:06:28.560><c> on</c><00:06:29.220><c> that</c><00:06:29.340><c> note</c><00:06:29.520><c> I</c><00:06:29.880><c> think</c><00:06:29.940><c> I</c><00:06:30.180><c> will</c><00:06:30.300><c> leave</c><00:06:30.539><c> it</c>

00:06:30.710 --> 00:06:30.720 align:start position:0%
so on that note I think I will leave it
 

00:06:30.720 --> 00:06:32.629 align:start position:0%
so on that note I think I will leave it
and<00:06:31.139><c> I</c><00:06:31.319><c> encourage</c><00:06:31.560><c> you</c><00:06:31.680><c> to</c><00:06:31.860><c> play</c><00:06:31.979><c> it</c><00:06:32.220><c> write</c>

00:06:32.629 --> 00:06:32.639 align:start position:0%
and I encourage you to play it write
 

00:06:32.639 --> 00:06:34.909 align:start position:0%
and I encourage you to play it write
your<00:06:32.880><c> sponsors</c><00:06:33.360><c> of</c><00:06:33.660><c> anything</c><00:06:34.139><c> you</c><00:06:34.500><c> notice</c>

00:06:34.909 --> 00:06:34.919 align:start position:0%
your sponsors of anything you notice
 

00:06:34.919 --> 00:06:36.409 align:start position:0%
your sponsors of anything you notice
anything<00:06:35.400><c> you</c><00:06:35.639><c> notice</c><00:06:35.880><c> really</c><00:06:36.060><c> interesting</c>

00:06:36.409 --> 00:06:36.419 align:start position:0%
anything you notice really interesting
 

00:06:36.419 --> 00:06:38.450 align:start position:0%
anything you notice really interesting
please<00:06:36.660><c> write</c><00:06:36.900><c> it</c><00:06:37.080><c> in</c><00:06:37.259><c> the</c><00:06:37.380><c> comments</c><00:06:37.620><c> and</c><00:06:38.039><c> I</c>

00:06:38.450 --> 00:06:38.460 align:start position:0%
please write it in the comments and I
 

00:06:38.460 --> 00:06:40.370 align:start position:0%
please write it in the comments and I
was<00:06:38.520><c> definitely</c><00:06:38.880><c> surprised</c><00:06:39.600><c> by</c><00:06:39.780><c> just</c><00:06:40.080><c> how</c>

00:06:40.370 --> 00:06:40.380 align:start position:0%
was definitely surprised by just how
 

00:06:40.380 --> 00:06:42.950 align:start position:0%
was definitely surprised by just how
much<00:06:40.560><c> a</c><00:06:41.160><c> new</c><00:06:41.280><c> line</c><00:06:41.520><c> character</c><00:06:42.060><c> can</c><00:06:42.720><c> really</c>

00:06:42.950 --> 00:06:42.960 align:start position:0%
much a new line character can really
 

00:06:42.960 --> 00:06:45.710 align:start position:0%
much a new line character can really
move<00:06:43.500><c> this</c><00:06:43.860><c> model</c><00:06:44.039><c> whereas</c><00:06:44.759><c> oh</c><00:06:45.120><c> that</c><00:06:45.419><c> isn't</c>

00:06:45.710 --> 00:06:45.720 align:start position:0%
move this model whereas oh that isn't
 

00:06:45.720 --> 00:06:47.210 align:start position:0%
move this model whereas oh that isn't
the<00:06:45.840><c> case</c><00:06:45.960><c> for</c><00:06:46.139><c> a</c><00:06:46.259><c> lot</c><00:06:46.380><c> of</c><00:06:46.440><c> the</c><00:06:46.560><c> other</c><00:06:46.740><c> models</c>

00:06:47.210 --> 00:06:47.220 align:start position:0%
the case for a lot of the other models
 

00:06:47.220 --> 00:06:49.010 align:start position:0%
the case for a lot of the other models
and<00:06:47.520><c> then</c><00:06:47.639><c> also</c><00:06:47.940><c> you</c><00:06:48.000><c> could</c><00:06:48.180><c> experiment</c><00:06:48.780><c> with</c>

00:06:49.010 --> 00:06:49.020 align:start position:0%
and then also you could experiment with
 

00:06:49.020 --> 00:06:51.409 align:start position:0%
and then also you could experiment with
some<00:06:49.319><c> other</c><00:06:49.560><c> sort</c><00:06:49.860><c> of</c><00:06:49.979><c> Beginnings</c><00:06:50.520><c> or</c><00:06:50.940><c> prompts</c>

00:06:51.409 --> 00:06:51.419 align:start position:0%
some other sort of Beginnings or prompts
 

00:06:51.419 --> 00:06:52.969 align:start position:0%
some other sort of Beginnings or prompts
and<00:06:51.660><c> see</c><00:06:51.780><c> what</c><00:06:51.960><c> you</c><00:06:52.080><c> can</c><00:06:52.139><c> do</c><00:06:52.259><c> with</c><00:06:52.500><c> the</c><00:06:52.680><c> various</c>

00:06:52.969 --> 00:06:52.979 align:start position:0%
and see what you can do with the various
 

00:06:52.979 --> 00:06:54.770 align:start position:0%
and see what you can do with the various
kind<00:06:53.160><c> of</c><00:06:53.280><c> prompts</c><00:06:53.699><c> as</c><00:06:53.940><c> well</c>

00:06:54.770 --> 00:06:54.780 align:start position:0%
kind of prompts as well
 

00:06:54.780 --> 00:06:56.809 align:start position:0%
kind of prompts as well
anyway<00:06:55.199><c> as</c><00:06:55.740><c> always</c><00:06:55.919><c> if</c><00:06:56.340><c> you</c><00:06:56.460><c> have</c><00:06:56.580><c> any</c>

00:06:56.809 --> 00:06:56.819 align:start position:0%
anyway as always if you have any
 

00:06:56.819 --> 00:06:58.370 align:start position:0%
anyway as always if you have any
questions<00:06:57.060><c> please</c><00:06:57.660><c> put</c><00:06:57.900><c> them</c><00:06:58.020><c> in</c><00:06:58.199><c> the</c>

00:06:58.370 --> 00:06:58.380 align:start position:0%
questions please put them in the
 

00:06:58.380 --> 00:07:00.230 align:start position:0%
questions please put them in the
comments<00:06:58.680><c> if</c><00:06:59.100><c> you</c><00:06:59.220><c> found</c><00:06:59.400><c> this</c><00:06:59.580><c> video</c><00:06:59.819><c> useful</c>

00:07:00.230 --> 00:07:00.240 align:start position:0%
comments if you found this video useful
 

00:07:00.240 --> 00:07:02.390 align:start position:0%
comments if you found this video useful
please<00:07:00.600><c> click</c><00:07:00.960><c> and</c><00:07:01.080><c> subscribe</c><00:07:01.560><c> I</c><00:07:02.039><c> will</c><00:07:02.220><c> see</c>

00:07:02.390 --> 00:07:02.400 align:start position:0%
please click and subscribe I will see
 

00:07:02.400 --> 00:07:06.560 align:start position:0%
please click and subscribe I will see
you<00:07:02.520><c> in</c><00:07:02.639><c> the</c><00:07:02.819><c> next</c><00:07:02.940><c> video</c><00:07:03.259><c> bye</c><00:07:04.259><c> for</c><00:07:04.380><c> now</c>

