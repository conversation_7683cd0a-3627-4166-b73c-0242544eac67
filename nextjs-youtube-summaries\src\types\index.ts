export interface VideoSummary {
  id: string
  video_id: string
  title: string
  channel_name: string
  published_at: string
  duration: string
  view_count: number
  transcript: string
  llm_summary: string
  embedding_summary?: number[] // pgvector embedding
  topic_category: string
  thumbnail_url: string
  video_url: string
  created_at: string
  updated_at: string
}

export interface TopicTable {
  name: string
  display_name: string
  description: string
  video_count: number
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  context_videos?: VideoSummary[]
}

export interface SearchResult {
  video: VideoSummary
  similarity_score: number
  relevant_transcript_chunks?: string[]
}

export interface ChatRequest {
  message: string
  topic_filters?: string[]
  search_query?: string
  session_id?: string  // Add session ID for conversation memory
}

export interface ChatResponse {
  response: string
  context_videos: VideoSummary[]
  search_results?: SearchResult[]
  session_id?: string  // Add session ID for conversation continuity
}
