WEBVTT
Kind: captions
Language: en

00:00:00.640 --> 00:00:02.110 align:start position:0%
 
[Music]

00:00:02.110 --> 00:00:02.120 align:start position:0%
[Music]
 

00:00:02.120 --> 00:00:03.750 align:start position:0%
[Music]
the<00:00:02.240><c> role</c><00:00:02.480><c> that</c><00:00:02.639><c> engineering</c><00:00:03.080><c> leadership</c><00:00:03.600><c> has</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
the role that engineering leadership has
 

00:00:03.760 --> 00:00:06.710 align:start position:0%
the role that engineering leadership has
to<00:00:04.000><c> play</c><00:00:04.920><c> in</c><00:00:05.200><c> developer</c><00:00:05.720><c> productivity</c><00:00:06.279><c> starts</c>

00:00:06.710 --> 00:00:06.720 align:start position:0%
to play in developer productivity starts
 

00:00:06.720 --> 00:00:08.070 align:start position:0%
to play in developer productivity starts
with<00:00:06.919><c> making</c><00:00:07.160><c> sure</c><00:00:07.439><c> that</c><00:00:07.560><c> the</c><00:00:07.680><c> things</c><00:00:07.839><c> we're</c>

00:00:08.070 --> 00:00:08.080 align:start position:0%
with making sure that the things we're
 

00:00:08.080 --> 00:00:10.310 align:start position:0%
with making sure that the things we're
asking<00:00:08.400><c> developers</c><00:00:08.840><c> to</c><00:00:09.000><c> do</c><00:00:09.679><c> are</c><00:00:09.840><c> worth</c><00:00:10.120><c> their</c>

00:00:10.310 --> 00:00:10.320 align:start position:0%
asking developers to do are worth their
 

00:00:10.320 --> 00:00:12.270 align:start position:0%
asking developers to do are worth their
time<00:00:11.080><c> I</c><00:00:11.200><c> don't</c><00:00:11.440><c> think</c><00:00:11.679><c> that</c><00:00:11.840><c> people</c><00:00:12.080><c> are</c>

00:00:12.270 --> 00:00:12.280 align:start position:0%
time I don't think that people are
 

00:00:12.280 --> 00:00:14.709 align:start position:0%
time I don't think that people are
always<00:00:12.559><c> taking</c><00:00:12.960><c> product</c><00:00:13.320><c> fit</c><00:00:13.839><c> and</c><00:00:14.280><c> feature</c>

00:00:14.709 --> 00:00:14.719 align:start position:0%
always taking product fit and feature
 

00:00:14.719 --> 00:00:16.750 align:start position:0%
always taking product fit and feature
set<00:00:15.160><c> and</c><00:00:15.559><c> what</c><00:00:15.679><c> the</c><00:00:15.799><c> users</c><00:00:16.279><c> actually</c><00:00:16.440><c> want</c><00:00:16.600><c> to</c>

00:00:16.750 --> 00:00:16.760 align:start position:0%
set and what the users actually want to
 

00:00:16.760 --> 00:00:20.109 align:start position:0%
set and what the users actually want to
pay<00:00:16.960><c> for</c><00:00:17.560><c> into</c><00:00:18.320><c> account</c><00:00:18.640><c> when</c><00:00:18.800><c> they're</c><00:00:19.720><c> asking</c>

00:00:20.109 --> 00:00:20.119 align:start position:0%
pay for into account when they're asking
 

00:00:20.119 --> 00:00:23.230 align:start position:0%
pay for into account when they're asking
developers<00:00:20.600><c> to</c><00:00:20.760><c> do</c><00:00:21.000><c> stuff</c><00:00:21.760><c> if</c><00:00:22.080><c> what</c><00:00:22.359><c> we</c><00:00:22.640><c> want</c>

00:00:23.230 --> 00:00:23.240 align:start position:0%
developers to do stuff if what we want
 

00:00:23.240 --> 00:00:26.470 align:start position:0%
developers to do stuff if what we want
is<00:00:23.800><c> developer</c><00:00:24.519><c> experience</c><00:00:25.519><c> why</c><00:00:25.720><c> we</c><00:00:25.880><c> want</c><00:00:26.119><c> it</c>

00:00:26.470 --> 00:00:26.480 align:start position:0%
is developer experience why we want it
 

00:00:26.480 --> 00:00:28.269 align:start position:0%
is developer experience why we want it
from<00:00:26.599><c> a</c><00:00:26.800><c> company</c><00:00:27.080><c> or</c><00:00:27.199><c> Enterprise</c><00:00:27.760><c> perspective</c>

00:00:28.269 --> 00:00:28.279 align:start position:0%
from a company or Enterprise perspective
 

00:00:28.279 --> 00:00:30.390 align:start position:0%
from a company or Enterprise perspective
is<00:00:28.840><c> we</c><00:00:29.000><c> want</c><00:00:29.119><c> to</c><00:00:29.320><c> make</c><00:00:29.439><c> it</c><00:00:29.599><c> fast</c><00:00:29.800><c> and</c><00:00:30.039><c> safe</c><00:00:30.240><c> to</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
is we want to make it fast and safe to
 

00:00:30.400 --> 00:00:33.990 align:start position:0%
is we want to make it fast and safe to
go<00:00:30.640><c> from</c><00:00:31.359><c> idea</c><00:00:31.800><c> or</c><00:00:32.079><c> or</c><00:00:32.320><c> concept</c><00:00:32.680><c> to</c><00:00:33.000><c> customer</c>

00:00:33.990 --> 00:00:34.000 align:start position:0%
go from idea or or concept to customer
 

00:00:34.000 --> 00:00:37.110 align:start position:0%
go from idea or or concept to customer
we<00:00:34.160><c> are</c><00:00:34.559><c> pushing</c><00:00:35.360><c> security</c><00:00:36.000><c> left</c><00:00:36.640><c> more</c><00:00:36.920><c> and</c>

00:00:37.110 --> 00:00:37.120 align:start position:0%
we are pushing security left more and
 

00:00:37.120 --> 00:00:39.869 align:start position:0%
we are pushing security left more and
more<00:00:37.399><c> we</c><00:00:37.600><c> have</c><00:00:37.800><c> this</c><00:00:38.239><c> powerful</c><00:00:38.840><c> AI</c><00:00:39.399><c> friend</c>

00:00:39.869 --> 00:00:39.879 align:start position:0%
more we have this powerful AI friend
 

00:00:39.879 --> 00:00:42.190 align:start position:0%
more we have this powerful AI friend
who's<00:00:40.160><c> coming</c><00:00:40.520><c> with</c><00:00:40.680><c> us</c><00:00:40.920><c> on</c><00:00:41.120><c> this</c><00:00:41.360><c> development</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
who's coming with us on this development
 

00:00:42.200 --> 00:00:45.750 align:start position:0%
who's coming with us on this development
Journey<00:00:43.200><c> as</c><00:00:43.280><c> a</c><00:00:43.600><c> manager</c><00:00:44.559><c> you're</c><00:00:44.879><c> being</c><00:00:45.200><c> asked</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
Journey as a manager you're being asked
 

00:00:45.760 --> 00:00:47.830 align:start position:0%
Journey as a manager you're being asked
to<00:00:46.079><c> go</c><00:00:46.199><c> to</c><00:00:46.360><c> meetings</c><00:00:46.800><c> and</c><00:00:46.960><c> make</c><00:00:47.160><c> architecture</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
to go to meetings and make architecture
 

00:00:47.840 --> 00:00:50.590 align:start position:0%
to go to meetings and make architecture
decisions<00:00:48.840><c> AI</c><00:00:49.199><c> can</c><00:00:49.399><c> really</c><00:00:49.680><c> be</c><00:00:50.160><c> how</c><00:00:50.280><c> can</c><00:00:50.440><c> I</c>

00:00:50.590 --> 00:00:50.600 align:start position:0%
decisions AI can really be how can I
 

00:00:50.600 --> 00:00:52.670 align:start position:0%
decisions AI can really be how can I
quickly<00:00:51.000><c> get</c><00:00:51.160><c> a</c><00:00:51.360><c> handle</c><00:00:51.680><c> on</c><00:00:51.879><c> what's</c><00:00:52.120><c> happening</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
quickly get a handle on what's happening
 

00:00:52.680 --> 00:00:54.869 align:start position:0%
quickly get a handle on what's happening
so<00:00:52.879><c> I</c><00:00:53.000><c> can</c><00:00:53.160><c> still</c><00:00:53.600><c> guide</c><00:00:53.960><c> and</c><00:00:54.079><c> mentor</c><00:00:54.559><c> and</c><00:00:54.680><c> do</c>

00:00:54.869 --> 00:00:54.879 align:start position:0%
so I can still guide and mentor and do
 

00:00:54.879 --> 00:00:56.270 align:start position:0%
so I can still guide and mentor and do
some<00:00:55.000><c> of</c><00:00:55.160><c> those</c><00:00:55.320><c> things</c><00:00:55.520><c> that</c><00:00:55.680><c> managers</c><00:00:56.079><c> are</c>

00:00:56.270 --> 00:00:56.280 align:start position:0%
some of those things that managers are
 

00:00:56.280 --> 00:00:58.990 align:start position:0%
some of those things that managers are
required<00:00:56.640><c> to</c><00:00:56.760><c> do</c><00:00:57.600><c> when</c><00:00:57.719><c> I</c><00:00:57.879><c> jump</c><00:00:58.160><c> into</c><00:00:58.399><c> vs</c><00:00:58.719><c> code</c>

00:00:58.990 --> 00:00:59.000 align:start position:0%
required to do when I jump into vs code
 

00:00:59.000 --> 00:01:02.029 align:start position:0%
required to do when I jump into vs code
on<00:00:59.160><c> a</c><00:00:59.399><c> Sunday</c><00:01:00.079><c> and</c><00:01:00.239><c> I</c><00:01:00.359><c> have</c><00:01:00.559><c> an</c><00:01:00.760><c> hour</c><00:01:01.079><c> to</c><00:01:01.239><c> fill</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
on a Sunday and I have an hour to fill
 

00:01:02.039 --> 00:01:04.630 align:start position:0%
on a Sunday and I have an hour to fill
co-pilot<00:01:02.559><c> chat</c><00:01:02.800><c> is</c><00:01:03.039><c> really</c><00:01:03.280><c> useful</c><00:01:03.680><c> for</c><00:01:03.840><c> me</c><00:01:04.040><c> to</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
co-pilot chat is really useful for me to
 

00:01:04.640 --> 00:01:06.550 align:start position:0%
co-pilot chat is really useful for me to
get<00:01:04.879><c> back</c><00:01:05.199><c> up</c><00:01:05.360><c> to</c><00:01:05.519><c> speed</c><00:01:05.880><c> with</c><00:01:06.000><c> my</c><00:01:06.200><c> existing</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
get back up to speed with my existing
 

00:01:06.560 --> 00:01:09.469 align:start position:0%
get back up to speed with my existing
code<00:01:06.920><c> base</c><00:01:07.560><c> and</c><00:01:07.880><c> also</c><00:01:08.280><c> to</c><00:01:08.520><c> ask</c><00:01:08.799><c> questions</c><00:01:09.159><c> so</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
code base and also to ask questions so
 

00:01:09.479 --> 00:01:11.390 align:start position:0%
code base and also to ask questions so
how<00:01:09.600><c> I</c><00:01:09.720><c> can</c><00:01:09.840><c> get</c><00:01:10.000><c> stuff</c><00:01:10.280><c> done</c><00:01:10.640><c> so</c><00:01:10.799><c> it</c><00:01:10.920><c> is</c><00:01:11.119><c> really</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
how I can get stuff done so it is really
 

00:01:11.400 --> 00:01:14.550 align:start position:0%
how I can get stuff done so it is really
like<00:01:11.680><c> unlocking</c><00:01:12.680><c> my</c><00:01:12.960><c> creativity</c><00:01:13.799><c> in</c><00:01:14.080><c> that</c>

00:01:14.550 --> 00:01:14.560 align:start position:0%
like unlocking my creativity in that
 

00:01:14.560 --> 00:01:17.630 align:start position:0%
like unlocking my creativity in that
moment<00:01:15.560><c> 15</c><00:01:16.080><c> years</c><00:01:16.320><c> ago</c><00:01:16.680><c> we</c><00:01:16.840><c> had</c><00:01:17.000><c> teams</c><00:01:17.360><c> we</c><00:01:17.479><c> had</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
moment 15 years ago we had teams we had
 

00:01:17.640 --> 00:01:19.590 align:start position:0%
moment 15 years ago we had teams we had
test<00:01:17.880><c> team</c><00:01:18.159><c> we</c><00:01:18.280><c> had</c><00:01:18.400><c> build</c><00:01:18.720><c> team</c><00:01:19.159><c> today</c><00:01:19.439><c> we</c>

00:01:19.590 --> 00:01:19.600 align:start position:0%
test team we had build team today we
 

00:01:19.600 --> 00:01:20.789 align:start position:0%
test team we had build team today we
like<00:01:19.759><c> oh</c><00:01:19.880><c> we</c><00:01:20.000><c> don't</c><00:01:20.119><c> need</c><00:01:20.280><c> that</c><00:01:20.400><c> we'll</c><00:01:20.560><c> put</c><00:01:20.680><c> in</c>

00:01:20.789 --> 00:01:20.799 align:start position:0%
like oh we don't need that we'll put in
 

00:01:20.799 --> 00:01:22.870 align:start position:0%
like oh we don't need that we'll put in
cicd<00:01:21.320><c> and</c><00:01:21.439><c> let</c><00:01:21.560><c> the</c><00:01:21.680><c> developer</c><00:01:22.119><c> work</c><00:01:22.360><c> with</c><00:01:22.479><c> it</c>

00:01:22.870 --> 00:01:22.880 align:start position:0%
cicd and let the developer work with it
 

00:01:22.880 --> 00:01:24.550 align:start position:0%
cicd and let the developer work with it
so<00:01:23.119><c> that's</c><00:01:23.360><c> like</c><00:01:23.479><c> a</c><00:01:23.600><c> whole</c><00:01:23.880><c> mindset</c><00:01:24.280><c> change</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
so that's like a whole mindset change
 

00:01:24.560 --> 00:01:26.109 align:start position:0%
so that's like a whole mindset change
and<00:01:24.759><c> security</c><00:01:25.119><c> is</c><00:01:25.320><c> just</c><00:01:25.479><c> like</c><00:01:25.640><c> the</c><00:01:25.799><c> next</c>

00:01:26.109 --> 00:01:26.119 align:start position:0%
and security is just like the next
 

00:01:26.119 --> 00:01:28.030 align:start position:0%
and security is just like the next
element<00:01:26.560><c> that's</c><00:01:26.759><c> coming</c><00:01:27.040><c> in</c><00:01:27.560><c> but</c><00:01:27.680><c> we</c><00:01:27.799><c> need</c><00:01:27.920><c> to</c>

00:01:28.030 --> 00:01:28.040 align:start position:0%
element that's coming in but we need to
 

00:01:28.040 --> 00:01:30.149 align:start position:0%
element that's coming in but we need to
do<00:01:28.159><c> it</c><00:01:28.240><c> in</c><00:01:28.360><c> the</c><00:01:28.560><c> right</c><00:01:28.759><c> ways</c><00:01:29.600><c> it's</c><00:01:29.960><c> not</c><00:01:30.040><c> the</c>

00:01:30.149 --> 00:01:30.159 align:start position:0%
do it in the right ways it's not the
 

00:01:30.159 --> 00:01:32.030 align:start position:0%
do it in the right ways it's not the
ciso<00:01:30.520><c> that's</c><00:01:30.680><c> responsible</c><00:01:31.079><c> with</c><00:01:31.240><c> security</c>

00:01:32.030 --> 00:01:32.040 align:start position:0%
ciso that's responsible with security
 

00:01:32.040 --> 00:01:34.550 align:start position:0%
ciso that's responsible with security
it's<00:01:32.200><c> all</c><00:01:32.360><c> of</c><00:01:32.520><c> us</c><00:01:33.079><c> we</c><00:01:33.479><c> are</c><00:01:33.640><c> going</c><00:01:33.840><c> to</c><00:01:34.040><c> embrace</c>

00:01:34.550 --> 00:01:34.560 align:start position:0%
it's all of us we are going to embrace
 

00:01:34.560 --> 00:01:36.630 align:start position:0%
it's all of us we are going to embrace
AI<00:01:35.399><c> but</c><00:01:35.560><c> we</c><00:01:35.680><c> have</c><00:01:35.759><c> to</c><00:01:35.880><c> be</c><00:01:36.079><c> really</c><00:01:36.360><c> really</c>

00:01:36.630 --> 00:01:36.640 align:start position:0%
AI but we have to be really really
 

00:01:36.640 --> 00:01:39.190 align:start position:0%
AI but we have to be really really
focused<00:01:37.079><c> on</c><00:01:37.320><c> how</c><00:01:37.439><c> do</c><00:01:37.560><c> you</c><00:01:37.680><c> mitigate</c><00:01:38.200><c> that</c><00:01:38.439><c> risk</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
focused on how do you mitigate that risk
 

00:01:39.200 --> 00:01:41.230 align:start position:0%
focused on how do you mitigate that risk
with<00:01:39.439><c> security</c><00:01:40.240><c> and</c><00:01:40.439><c> really</c><00:01:40.640><c> bake</c><00:01:40.880><c> that</c><00:01:41.040><c> into</c>

00:01:41.230 --> 00:01:41.240 align:start position:0%
with security and really bake that into
 

00:01:41.240 --> 00:01:44.270 align:start position:0%
with security and really bake that into
the<00:01:41.360><c> DNA</c><00:01:41.880><c> of</c><00:01:42.040><c> what</c><00:01:42.200><c> we</c><00:01:42.320><c> do</c><00:01:43.119><c> trust</c><00:01:43.520><c> is</c><00:01:43.880><c> hard</c><00:01:44.079><c> to</c>

00:01:44.270 --> 00:01:44.280 align:start position:0%
the DNA of what we do trust is hard to
 

00:01:44.280 --> 00:01:47.709 align:start position:0%
the DNA of what we do trust is hard to
earn<00:01:44.640><c> and</c><00:01:44.840><c> easy</c><00:01:45.079><c> to</c><00:01:45.360><c> lose</c><00:01:46.360><c> the</c><00:01:46.719><c> AI</c><00:01:47.119><c> assisted</c>

00:01:47.709 --> 00:01:47.719 align:start position:0%
earn and easy to lose the AI assisted
 

00:01:47.719 --> 00:01:51.270 align:start position:0%
earn and easy to lose the AI assisted
autofix<00:01:48.680><c> for</c><00:01:49.399><c> vulnerable</c><00:01:49.960><c> code</c><00:01:50.719><c> that</c><00:01:50.880><c> happens</c>

00:01:51.270 --> 00:01:51.280 align:start position:0%
autofix for vulnerable code that happens
 

00:01:51.280 --> 00:01:52.910 align:start position:0%
autofix for vulnerable code that happens
potentially<00:01:51.759><c> before</c><00:01:51.960><c> you</c><00:01:52.119><c> even</c><00:01:52.399><c> get</c><00:01:52.560><c> to</c><00:01:52.759><c> a</c>

00:01:52.910 --> 00:01:52.920 align:start position:0%
potentially before you even get to a
 

00:01:52.920 --> 00:01:54.389 align:start position:0%
potentially before you even get to a
review<00:01:53.280><c> with</c><00:01:53.399><c> another</c><00:01:53.680><c> human</c><00:01:53.960><c> being</c><00:01:54.240><c> there's</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
review with another human being there's
 

00:01:54.399 --> 00:01:56.590 align:start position:0%
review with another human being there's
a<00:01:54.799><c> a</c><00:01:54.960><c> fix</c><00:01:55.280><c> suggested</c><00:01:55.799><c> for</c><00:01:55.960><c> you</c><00:01:56.200><c> right</c><00:01:56.360><c> out</c><00:01:56.479><c> of</c>

00:01:56.590 --> 00:01:56.600 align:start position:0%
a a fix suggested for you right out of
 

00:01:56.600 --> 00:01:58.749 align:start position:0%
a a fix suggested for you right out of
the<00:01:56.759><c> gate</c><00:01:57.520><c> it's</c><00:01:57.799><c> it's</c><00:01:57.960><c> hard</c><00:01:58.119><c> to</c><00:01:58.320><c> make</c><00:01:58.399><c> it</c><00:01:58.560><c> much</c>

00:01:58.749 --> 00:01:58.759 align:start position:0%
the gate it's it's hard to make it much
 

00:01:58.759 --> 00:02:00.469 align:start position:0%
the gate it's it's hard to make it much
easier<00:01:59.159><c> to</c><00:01:59.280><c> build</c><00:01:59.479><c> in</c><00:01:59.840><c> security</c><00:02:00.159><c> as</c><00:02:00.280><c> part</c><00:02:00.399><c> of</c>

00:02:00.469 --> 00:02:00.479 align:start position:0%
easier to build in security as part of
 

00:02:00.479 --> 00:02:02.550 align:start position:0%
easier to build in security as part of
the<00:02:00.600><c> workflow</c><00:02:01.560><c> if</c><00:02:01.640><c> you</c><00:02:01.719><c> don't</c><00:02:01.880><c> have</c><00:02:02.000><c> a</c><00:02:02.200><c> proper</c>

00:02:02.550 --> 00:02:02.560 align:start position:0%
the workflow if you don't have a proper
 

00:02:02.560 --> 00:02:04.709 align:start position:0%
the workflow if you don't have a proper
AI<00:02:02.960><c> strategy</c><00:02:03.880><c> you're</c><00:02:04.079><c> going</c><00:02:04.200><c> to</c><00:02:04.320><c> get</c><00:02:04.479><c> left</c>

00:02:04.709 --> 00:02:04.719 align:start position:0%
AI strategy you're going to get left
 

00:02:04.719 --> 00:02:06.389 align:start position:0%
AI strategy you're going to get left
behind<00:02:05.039><c> your</c><00:02:05.320><c> competitors</c><00:02:05.799><c> are</c><00:02:05.920><c> going</c><00:02:06.039><c> to</c><00:02:06.320><c> be</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
behind your competitors are going to be
 

00:02:06.399 --> 00:02:08.309 align:start position:0%
behind your competitors are going to be
able<00:02:06.560><c> to</c><00:02:06.680><c> move</c><00:02:06.880><c> so</c><00:02:07.079><c> much</c><00:02:07.320><c> faster</c><00:02:07.960><c> there's</c><00:02:08.160><c> a</c>

00:02:08.309 --> 00:02:08.319 align:start position:0%
able to move so much faster there's a
 

00:02:08.319 --> 00:02:09.949 align:start position:0%
able to move so much faster there's a
great<00:02:08.520><c> study</c><00:02:08.800><c> that</c><00:02:08.959><c> came</c><00:02:09.080><c> out</c><00:02:09.239><c> from</c><00:02:09.399><c> IDC</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
great study that came out from IDC
 

00:02:09.959 --> 00:02:11.589 align:start position:0%
great study that came out from IDC
recently<00:02:10.360><c> that</c><00:02:10.479><c> said</c><00:02:10.640><c> for</c><00:02:10.840><c> every</c><00:02:11.120><c> dollar</c><00:02:11.400><c> you</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
recently that said for every dollar you
 

00:02:11.599 --> 00:02:13.589 align:start position:0%
recently that said for every dollar you
invest<00:02:11.879><c> in</c><00:02:12.080><c> AI</c><00:02:12.480><c> in</c><00:02:12.599><c> your</c><00:02:12.720><c> organization</c><00:02:13.480><c> you</c>

00:02:13.589 --> 00:02:13.599 align:start position:0%
invest in AI in your organization you
 

00:02:13.599 --> 00:02:17.309 align:start position:0%
invest in AI in your organization you
get<00:02:13.760><c> $3.5</c><00:02:14.760><c> of</c><00:02:15.080><c> return</c><00:02:15.519><c> that's</c><00:02:16.120><c> massive</c><00:02:17.120><c> we</c>

00:02:17.309 --> 00:02:17.319 align:start position:0%
get $3.5 of return that's massive we
 

00:02:17.319 --> 00:02:19.430 align:start position:0%
get $3.5 of return that's massive we
changed<00:02:18.000><c> fundamentally</c><00:02:18.760><c> the</c><00:02:18.920><c> way</c><00:02:19.080><c> software</c>

00:02:19.430 --> 00:02:19.440 align:start position:0%
changed fundamentally the way software
 

00:02:19.440 --> 00:02:21.350 align:start position:0%
changed fundamentally the way software
development<00:02:19.920><c> is</c><00:02:20.080><c> being</c><00:02:20.280><c> done</c><00:02:20.920><c> there</c><00:02:21.040><c> is</c><00:02:21.160><c> no</c>

00:02:21.350 --> 00:02:21.360 align:start position:0%
development is being done there is no
 

00:02:21.360 --> 00:02:22.790 align:start position:0%
development is being done there is no
going<00:02:21.599><c> back</c><00:02:21.800><c> from</c><00:02:22.040><c> that</c><00:02:22.200><c> so</c><00:02:22.400><c> if</c><00:02:22.480><c> you</c><00:02:22.599><c> don't</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
going back from that so if you don't
 

00:02:22.800 --> 00:02:24.869 align:start position:0%
going back from that so if you don't
embrace<00:02:23.160><c> it</c><00:02:23.319><c> now</c><00:02:23.560><c> you</c><00:02:23.720><c> will</c><00:02:23.920><c> be</c><00:02:24.080><c> late</c><00:02:24.640><c> and</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
embrace it now you will be late and
 

00:02:24.879 --> 00:02:26.910 align:start position:0%
embrace it now you will be late and
every<00:02:25.160><c> company</c><00:02:25.519><c> want</c><00:02:25.680><c> to</c><00:02:25.800><c> be</c><00:02:25.920><c> a</c><00:02:26.000><c> leader</c><00:02:26.680><c> every</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
every company want to be a leader every
 

00:02:26.920 --> 00:02:28.630 align:start position:0%
every company want to be a leader every
company<00:02:27.239><c> want</c><00:02:27.440><c> to</c><00:02:27.560><c> be</c><00:02:27.879><c> the</c><00:02:28.000><c> Forefront</c><00:02:28.480><c> of</c>

00:02:28.630 --> 00:02:28.640 align:start position:0%
company want to be the Forefront of
 

00:02:28.640 --> 00:02:31.390 align:start position:0%
company want to be the Forefront of
innovation<00:02:29.440><c> so</c><00:02:29.840><c> every</c><00:02:30.040><c> company</c><00:02:30.400><c> should</c><00:02:30.800><c> have</c>

00:02:31.390 --> 00:02:31.400 align:start position:0%
innovation so every company should have
 

00:02:31.400 --> 00:02:36.070 align:start position:0%
innovation so every company should have
an<00:02:31.640><c> AI</c><00:02:31.959><c> strategy</c><00:02:32.519><c> from</c><00:02:32.879><c> right</c>

00:02:36.070 --> 00:02:36.080 align:start position:0%
 
 

00:02:36.080 --> 00:02:39.080 align:start position:0%
 
now

