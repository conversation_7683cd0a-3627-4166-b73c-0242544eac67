WEBVTT
Kind: captions
Language: en

00:00:00.599 --> 00:00:02.869 align:start position:0%
 
hey<00:00:01.079><c> there</c><00:00:01.319><c> it's</c><00:00:01.800><c> Matt</c><00:00:02.040><c> <PERSON></c><00:00:02.340><c> your</c>

00:00:02.869 --> 00:00:02.879 align:start position:0%
hey there it's <PERSON> your
 

00:00:02.879 --> 00:00:05.090 align:start position:0%
hey there it's <PERSON> your
favorite<00:00:03.060><c> evangelist</c><00:00:03.780><c> somebody</c><00:00:04.319><c> on</c><00:00:04.680><c> one</c><00:00:04.980><c> of</c>

00:00:05.090 --> 00:00:05.100 align:start position:0%
favorite evangelist somebody on one of
 

00:00:05.100 --> 00:00:06.950 align:start position:0%
favorite evangelist somebody on one of
the<00:00:05.160><c> AI</c><00:00:05.460><c> channels</c><00:00:05.880><c> that</c><00:00:06.000><c> I'm</c><00:00:06.120><c> on</c><00:00:06.299><c> on</c><00:00:06.540><c> Discord</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
the AI channels that I'm on on Discord
 

00:00:06.960 --> 00:00:09.589 align:start position:0%
the AI channels that I'm on on Discord
was<00:00:07.560><c> saying</c><00:00:07.919><c> oh</c><00:00:08.099><c> hey</c><00:00:08.400><c> it's</c><00:00:08.580><c> great</c><00:00:08.760><c> YouTube</c><00:00:09.179><c> is</c>

00:00:09.589 --> 00:00:09.599 align:start position:0%
was saying oh hey it's great YouTube is
 

00:00:09.599 --> 00:00:11.690 align:start position:0%
was saying oh hey it's great YouTube is
going<00:00:09.780><c> to</c><00:00:09.960><c> offer</c><00:00:10.320><c> free</c><00:00:10.800><c> transcripts</c><00:00:11.460><c> for</c>

00:00:11.690 --> 00:00:11.700 align:start position:0%
going to offer free transcripts for
 

00:00:11.700 --> 00:00:14.810 align:start position:0%
going to offer free transcripts for
videos<00:00:11.880><c> coming</c><00:00:12.360><c> soon</c><00:00:12.740><c> uh</c><00:00:13.740><c> well</c><00:00:14.219><c> what</c><00:00:14.639><c> do</c><00:00:14.759><c> you</c>

00:00:14.810 --> 00:00:14.820 align:start position:0%
videos coming soon uh well what do you
 

00:00:14.820 --> 00:00:17.029 align:start position:0%
videos coming soon uh well what do you
mean<00:00:14.880><c> they've</c><00:00:15.120><c> been</c><00:00:15.299><c> doing</c><00:00:15.480><c> that</c><00:00:15.719><c> for</c><00:00:16.199><c> 10</c>

00:00:17.029 --> 00:00:17.039 align:start position:0%
mean they've been doing that for 10
 

00:00:17.039 --> 00:00:20.269 align:start position:0%
mean they've been doing that for 10
years<00:00:17.340><c> at</c><00:00:18.240><c> least</c><00:00:18.420><c> uh</c><00:00:19.080><c> maybe</c><00:00:19.440><c> even</c><00:00:19.680><c> longer</c><00:00:20.100><c> than</c>

00:00:20.269 --> 00:00:20.279 align:start position:0%
years at least uh maybe even longer than
 

00:00:20.279 --> 00:00:22.730 align:start position:0%
years at least uh maybe even longer than
that<00:00:20.460><c> it</c><00:00:20.820><c> says</c><00:00:21.060><c> oh</c><00:00:21.180><c> well</c><00:00:21.359><c> you</c><00:00:21.720><c> have</c><00:00:21.900><c> to</c><00:00:22.020><c> go</c><00:00:22.260><c> into</c>

00:00:22.730 --> 00:00:22.740 align:start position:0%
that it says oh well you have to go into
 

00:00:22.740 --> 00:00:25.490 align:start position:0%
that it says oh well you have to go into
your<00:00:23.220><c> studio</c><00:00:23.640><c> and</c><00:00:24.300><c> and</c><00:00:24.539><c> then</c><00:00:24.779><c> you</c><00:00:24.960><c> can</c><00:00:25.080><c> get</c><00:00:25.320><c> to</c>

00:00:25.490 --> 00:00:25.500 align:start position:0%
your studio and and then you can get to
 

00:00:25.500 --> 00:00:27.529 align:start position:0%
your studio and and then you can get to
the<00:00:25.740><c> the</c><00:00:26.039><c> subtitles</c><00:00:26.880><c> and</c><00:00:27.180><c> then</c><00:00:27.240><c> you</c><00:00:27.420><c> can</c>

00:00:27.529 --> 00:00:27.539 align:start position:0%
the the subtitles and then you can
 

00:00:27.539 --> 00:00:29.750 align:start position:0%
the the subtitles and then you can
download<00:00:27.720><c> the</c><00:00:28.260><c> file</c><00:00:28.560><c> and</c><00:00:28.980><c> and</c><00:00:29.220><c> it's</c><00:00:29.460><c> a</c><00:00:29.580><c> bit</c><00:00:29.640><c> of</c>

00:00:29.750 --> 00:00:29.760 align:start position:0%
download the file and and it's a bit of
 

00:00:29.760 --> 00:00:31.910 align:start position:0%
download the file and and it's a bit of
a<00:00:29.939><c> pain</c><00:00:30.060><c> to</c><00:00:30.300><c> do</c><00:00:30.480><c> so</c><00:00:30.840><c> it'd</c><00:00:31.320><c> be</c><00:00:31.380><c> great</c><00:00:31.560><c> if</c><00:00:31.800><c> you</c>

00:00:31.910 --> 00:00:31.920 align:start position:0%
a pain to do so it'd be great if you
 

00:00:31.920 --> 00:00:33.410 align:start position:0%
a pain to do so it'd be great if you
could<00:00:32.040><c> just</c><00:00:32.160><c> do</c><00:00:32.340><c> it</c><00:00:32.460><c> from</c><00:00:32.759><c> going</c><00:00:33.059><c> to</c><00:00:33.239><c> theater</c>

00:00:33.410 --> 00:00:33.420 align:start position:0%
could just do it from going to theater
 

00:00:33.420 --> 00:00:38.090 align:start position:0%
could just do it from going to theater
mode<00:00:33.719><c> well</c><00:00:34.380><c> you</c><00:00:34.680><c> can</c><00:00:35.100><c> so</c><00:00:35.760><c> let's</c><00:00:36.000><c> go</c><00:00:36.239><c> into</c><00:00:37.100><c> uh</c>

00:00:38.090 --> 00:00:38.100 align:start position:0%
mode well you can so let's go into uh
 

00:00:38.100 --> 00:00:40.369 align:start position:0%
mode well you can so let's go into uh
let's<00:00:38.340><c> scroll</c><00:00:38.820><c> down</c><00:00:39.000><c> to</c><00:00:39.300><c> a</c><00:00:39.540><c> video</c><00:00:39.780><c> that</c>

00:00:40.369 --> 00:00:40.379 align:start position:0%
let's scroll down to a video that
 

00:00:40.379 --> 00:00:43.850 align:start position:0%
let's scroll down to a video that
probably<00:00:40.739><c> has</c><00:00:41.160><c> a</c><00:00:41.460><c> transcript</c>

00:00:43.850 --> 00:00:43.860 align:start position:0%
probably has a transcript
 

00:00:43.860 --> 00:00:46.250 align:start position:0%
probably has a transcript
um<00:00:44.040><c> here's</c><00:00:45.000><c> this</c><00:00:45.120><c> uh</c><00:00:45.420><c> this</c><00:00:45.660><c> Gerald</c><00:00:45.960><c> undone</c>

00:00:46.250 --> 00:00:46.260 align:start position:0%
um here's this uh this Gerald undone
 

00:00:46.260 --> 00:00:50.389 align:start position:0%
um here's this uh this Gerald undone
video<00:00:46.500><c> about</c><00:00:47.120><c> Linus's</c><00:00:48.120><c> studio</c><00:00:48.920><c> and</c><00:00:49.920><c> if</c><00:00:50.219><c> I</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
video about Linus's studio and if I
 

00:00:50.399 --> 00:00:52.670 align:start position:0%
video about Linus's studio and if I
click<00:00:50.700><c> on</c><00:00:50.820><c> this</c><00:00:51.059><c> little</c><00:00:51.239><c> three</c><00:00:51.600><c> dots</c><00:00:52.020><c> here</c>

00:00:52.670 --> 00:00:52.680 align:start position:0%
click on this little three dots here
 

00:00:52.680 --> 00:00:55.189 align:start position:0%
click on this little three dots here
I'll<00:00:53.100><c> be</c><00:00:53.280><c> able</c><00:00:53.399><c> to</c><00:00:53.579><c> show</c><00:00:53.940><c> transcript</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
I'll be able to show transcript
 

00:00:55.199 --> 00:00:57.229 align:start position:0%
I'll be able to show transcript
and<00:00:55.620><c> there's</c><00:00:55.739><c> the</c><00:00:55.920><c> transcript</c><00:00:56.280><c> toggle</c>

00:00:57.229 --> 00:00:57.239 align:start position:0%
and there's the transcript toggle
 

00:00:57.239 --> 00:00:58.430 align:start position:0%
and there's the transcript toggle
timestamps<00:00:57.780><c> if</c><00:00:57.899><c> you</c><00:00:58.020><c> want</c><00:00:58.079><c> to</c><00:00:58.199><c> get</c><00:00:58.320><c> the</c>

00:00:58.430 --> 00:00:58.440 align:start position:0%
timestamps if you want to get the
 

00:00:58.440 --> 00:01:00.729 align:start position:0%
timestamps if you want to get the
timestamps<00:00:59.100><c> off</c><00:00:59.280><c> uh</c><00:01:00.000><c> that's</c><00:01:00.239><c> what</c><00:01:00.360><c> you</c><00:01:00.480><c> can</c><00:01:00.600><c> do</c>

00:01:00.729 --> 00:01:00.739 align:start position:0%
timestamps off uh that's what you can do
 

00:01:00.739 --> 00:01:03.650 align:start position:0%
timestamps off uh that's what you can do
and<00:01:01.739><c> then</c><00:01:02.039><c> select</c>

00:01:03.650 --> 00:01:03.660 align:start position:0%
and then select
 

00:01:03.660 --> 00:01:07.130 align:start position:0%
and then select
and<00:01:04.500><c> scroll</c><00:01:05.280><c> to</c><00:01:05.700><c> the</c><00:01:05.880><c> bottom</c>

00:01:07.130 --> 00:01:07.140 align:start position:0%
and scroll to the bottom
 

00:01:07.140 --> 00:01:09.890 align:start position:0%
and scroll to the bottom
and<00:01:08.040><c> copy</c><00:01:08.520><c> and</c><00:01:09.000><c> there</c><00:01:09.119><c> you</c><00:01:09.299><c> go</c><00:01:09.420><c> there's</c><00:01:09.659><c> your</c>

00:01:09.890 --> 00:01:09.900 align:start position:0%
and copy and there you go there's your
 

00:01:09.900 --> 00:01:12.530 align:start position:0%
and copy and there you go there's your
transcript<00:01:10.260><c> it</c><00:01:10.920><c> looks</c><00:01:11.220><c> like</c><00:01:11.340><c> we</c><00:01:12.060><c> might</c><00:01:12.240><c> also</c>

00:01:12.530 --> 00:01:12.540 align:start position:0%
transcript it looks like we might also
 

00:01:12.540 --> 00:01:15.170 align:start position:0%
transcript it looks like we might also
oh<00:01:12.900><c> well</c><00:01:13.200><c> if</c><00:01:13.680><c> the</c><00:01:13.799><c> video</c><00:01:14.340><c> had</c><00:01:14.580><c> been</c><00:01:14.760><c> translated</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
oh well if the video had been translated
 

00:01:15.180 --> 00:01:16.850 align:start position:0%
oh well if the video had been translated
into<00:01:15.420><c> other</c><00:01:15.659><c> languages</c><00:01:16.020><c> we'd</c><00:01:16.439><c> be</c><00:01:16.560><c> able</c><00:01:16.680><c> to</c>

00:01:16.850 --> 00:01:16.860 align:start position:0%
into other languages we'd be able to
 

00:01:16.860 --> 00:01:19.609 align:start position:0%
into other languages we'd be able to
choose<00:01:17.280><c> which</c><00:01:18.000><c> language</c><00:01:18.180><c> we</c><00:01:18.540><c> want</c><00:01:18.780><c> usually</c>

00:01:19.609 --> 00:01:19.619 align:start position:0%
choose which language we want usually
 

00:01:19.619 --> 00:01:22.550 align:start position:0%
choose which language we want usually
there's<00:01:19.799><c> an</c><00:01:20.100><c> auto-generated</c><00:01:20.820><c> language</c><00:01:21.560><c> and</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
there's an auto-generated language and
 

00:01:22.560 --> 00:01:24.770 align:start position:0%
there's an auto-generated language and
that<00:01:23.220><c> is</c><00:01:23.700><c> you</c><00:01:24.000><c> know</c><00:01:24.060><c> where</c><00:01:24.240><c> YouTube</c><00:01:24.420><c> is</c>

00:01:24.770 --> 00:01:24.780 align:start position:0%
that is you know where YouTube is
 

00:01:24.780 --> 00:01:27.410 align:start position:0%
that is you know where YouTube is
automatically<00:01:25.259><c> trying</c><00:01:25.560><c> to</c><00:01:25.979><c> figure</c><00:01:26.460><c> out</c><00:01:26.759><c> uh</c>

00:01:27.410 --> 00:01:27.420 align:start position:0%
automatically trying to figure out uh
 

00:01:27.420 --> 00:01:29.270 align:start position:0%
automatically trying to figure out uh
the<00:01:27.659><c> what</c><00:01:27.960><c> the</c><00:01:28.200><c> person</c><00:01:28.320><c> said</c><00:01:28.619><c> and</c><00:01:29.040><c> this</c><00:01:29.159><c> is</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
the what the person said and this is
 

00:01:29.280 --> 00:01:32.810 align:start position:0%
the what the person said and this is
getting<00:01:29.520><c> better</c><00:01:29.820><c> and</c><00:01:30.000><c> better</c><00:01:30.240><c> every</c><00:01:31.140><c> year</c><00:01:31.820><c> but</c>

00:01:32.810 --> 00:01:32.820 align:start position:0%
getting better and better every year but
 

00:01:32.820 --> 00:01:35.149 align:start position:0%
getting better and better every year but
then<00:01:33.060><c> there</c><00:01:33.299><c> is</c><00:01:33.479><c> the</c><00:01:33.900><c> English</c><00:01:34.020><c> one</c><00:01:34.439><c> and</c><00:01:34.920><c> that</c>

00:01:35.149 --> 00:01:35.159 align:start position:0%
then there is the English one and that
 

00:01:35.159 --> 00:01:37.310 align:start position:0%
then there is the English one and that
is<00:01:35.280><c> probably</c><00:01:35.520><c> somebody's</c><00:01:36.119><c> gone</c><00:01:36.240><c> in</c><00:01:36.540><c> and</c><00:01:37.079><c> used</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
is probably somebody's gone in and used
 

00:01:37.320 --> 00:01:40.249 align:start position:0%
is probably somebody's gone in and used
some<00:01:37.560><c> other</c><00:01:37.680><c> service</c><00:01:38.119><c> to</c><00:01:39.119><c> transcribe</c><00:01:39.780><c> it</c><00:01:39.900><c> but</c>

00:01:40.249 --> 00:01:40.259 align:start position:0%
some other service to transcribe it but
 

00:01:40.259 --> 00:01:42.649 align:start position:0%
some other service to transcribe it but
usually<00:01:40.619><c> auto-generated</c><00:01:41.520><c> gets</c><00:01:42.060><c> you</c><00:01:42.180><c> pretty</c>

00:01:42.649 --> 00:01:42.659 align:start position:0%
usually auto-generated gets you pretty
 

00:01:42.659 --> 00:01:45.109 align:start position:0%
usually auto-generated gets you pretty
close<00:01:42.900><c> to</c><00:01:43.079><c> what</c><00:01:43.380><c> you</c><00:01:43.500><c> want</c><00:01:43.619><c> anyway</c><00:01:44.340><c> that's</c><00:01:44.820><c> a</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
close to what you want anyway that's a
 

00:01:45.119 --> 00:01:47.810 align:start position:0%
close to what you want anyway that's a
quick<00:01:45.540><c> tip</c><00:01:45.900><c> and</c><00:01:46.380><c> in</c><00:01:46.619><c> case</c><00:01:46.799><c> that</c><00:01:47.100><c> is</c><00:01:47.340><c> useful</c><00:01:47.640><c> to</c>

00:01:47.810 --> 00:01:47.820 align:start position:0%
quick tip and in case that is useful to
 

00:01:47.820 --> 00:01:48.710 align:start position:0%
quick tip and in case that is useful to
you

00:01:48.710 --> 00:01:48.720 align:start position:0%
you
 

00:01:48.720 --> 00:01:50.870 align:start position:0%
you
thanks<00:01:49.200><c> so</c><00:01:49.380><c> much</c><00:01:49.500><c> for</c><00:01:49.619><c> watching</c><00:01:49.979><c> and</c><00:01:50.460><c> uh</c><00:01:50.700><c> see</c>

00:01:50.870 --> 00:01:50.880 align:start position:0%
thanks so much for watching and uh see
 

00:01:50.880 --> 00:02:09.889 align:start position:0%
thanks so much for watching and uh see
you<00:01:51.000><c> in</c><00:01:51.000><c> the</c><00:01:51.119><c> next</c><00:01:51.240><c> one</c><00:01:51.420><c> bye</c>

00:02:09.889 --> 00:02:09.899 align:start position:0%
 
 

00:02:09.899 --> 00:02:12.739 align:start position:0%
 
okay

