WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:02.389 align:start position:0%
 
hello<00:00:01.000><c> I</c><00:00:01.079><c> want</c><00:00:01.240><c> to</c><00:00:01.400><c> go</c><00:00:01.560><c> over</c><00:00:01.760><c> an</c><00:00:01.959><c> important</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
hello I want to go over an important
 

00:00:02.399 --> 00:00:04.789 align:start position:0%
hello I want to go over an important
subject<00:00:02.840><c> about</c><00:00:03.080><c> web</c><00:00:03.399><c> Hooks</c><00:00:04.000><c> and</c><00:00:04.120><c> it</c><00:00:04.240><c> is</c><00:00:04.440><c> how</c><00:00:04.560><c> to</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
subject about web Hooks and it is how to
 

00:00:04.799 --> 00:00:08.190 align:start position:0%
subject about web Hooks and it is how to
test<00:00:05.120><c> web</c><00:00:05.560><c> hooks</c><00:00:06.560><c> so</c><00:00:07.080><c> one</c><00:00:07.319><c> way</c><00:00:07.480><c> to</c><00:00:07.640><c> test</c><00:00:07.919><c> web</c>

00:00:08.190 --> 00:00:08.200 align:start position:0%
test web hooks so one way to test web
 

00:00:08.200 --> 00:00:10.749 align:start position:0%
test web hooks so one way to test web
hooks<00:00:08.960><c> is</c><00:00:09.120><c> to</c><00:00:09.559><c> kind</c><00:00:09.679><c> of</c><00:00:09.920><c> like</c><00:00:10.080><c> trigger</c><00:00:10.480><c> web</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
hooks is to kind of like trigger web
 

00:00:10.759 --> 00:00:12.910 align:start position:0%
hooks is to kind of like trigger web
hooks<00:00:11.240><c> by</c><00:00:11.400><c> adding</c><00:00:11.679><c> aliases</c><00:00:12.320><c> or</c><00:00:12.480><c> whatever</c><00:00:12.799><c> but</c>

00:00:12.910 --> 00:00:12.920 align:start position:0%
hooks by adding aliases or whatever but
 

00:00:12.920 --> 00:00:15.150 align:start position:0%
hooks by adding aliases or whatever but
that's<00:00:13.080><c> kind</c><00:00:13.200><c> of</c><00:00:13.320><c> cumbersome</c><00:00:14.200><c> to</c><00:00:14.440><c> just</c><00:00:14.719><c> like</c>

00:00:15.150 --> 00:00:15.160 align:start position:0%
that's kind of cumbersome to just like
 

00:00:15.160 --> 00:00:17.310 align:start position:0%
that's kind of cumbersome to just like
add<00:00:15.320><c> an</c><00:00:15.480><c> alias</c><00:00:16.160><c> just</c><00:00:16.279><c> so</c><00:00:16.480><c> you</c><00:00:16.600><c> can</c><00:00:16.760><c> trigger</c><00:00:17.160><c> a</c>

00:00:17.310 --> 00:00:17.320 align:start position:0%
add an alias just so you can trigger a
 

00:00:17.320 --> 00:00:19.910 align:start position:0%
add an alias just so you can trigger a
web<00:00:17.560><c> hook</c><00:00:18.480><c> and</c><00:00:18.680><c> actually</c><00:00:18.920><c> was</c><00:00:19.160><c> and</c><00:00:19.320><c> biases</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
web hook and actually was and biases
 

00:00:19.920 --> 00:00:25.550 align:start position:0%
web hook and actually was and biases
offers<00:00:20.800><c> a</c><00:00:21.039><c> way</c><00:00:21.680><c> to</c><00:00:22.039><c> test</c><00:00:22.800><c> web</c><00:00:23.240><c> hooks</c><00:00:24.240><c> um</c><00:00:25.240><c> like</c><00:00:25.359><c> a</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
offers a way to test web hooks um like a
 

00:00:25.560 --> 00:00:27.429 align:start position:0%
offers a way to test web hooks um like a
specific<00:00:26.039><c> testing</c><00:00:26.480><c> functionality</c><00:00:27.080><c> so</c><00:00:27.199><c> let</c><00:00:27.279><c> me</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
specific testing functionality so let me
 

00:00:27.439 --> 00:00:30.589 align:start position:0%
specific testing functionality so let me
show<00:00:27.679><c> you</c><00:00:27.840><c> that</c><00:00:28.080><c> right</c><00:00:28.240><c> now</c><00:00:28.960><c> so</c><00:00:29.119><c> if</c><00:00:29.199><c> you</c><00:00:29.400><c> recall</c>

00:00:30.589 --> 00:00:30.599 align:start position:0%
show you that right now so if you recall
 

00:00:30.599 --> 00:00:33.190 align:start position:0%
show you that right now so if you recall
uh<00:00:31.080><c> the</c><00:00:31.439><c> the</c><00:00:31.599><c> web</c><00:00:31.920><c> hook</c><00:00:32.160><c> code</c><00:00:32.520><c> looks</c><00:00:32.840><c> something</c>

00:00:33.190 --> 00:00:33.200 align:start position:0%
uh the the web hook code looks something
 

00:00:33.200 --> 00:00:36.389 align:start position:0%
uh the the web hook code looks something
like<00:00:33.440><c> this</c><00:00:34.239><c> it's</c><00:00:34.480><c> just</c><00:00:34.719><c> it's</c><00:00:34.840><c> a</c><00:00:35.000><c> web</c><00:00:35.399><c> server</c>

00:00:36.389 --> 00:00:36.399 align:start position:0%
like this it's just it's a web server
 

00:00:36.399 --> 00:00:39.869 align:start position:0%
like this it's just it's a web server
and<00:00:37.040><c> it's</c><00:00:37.200><c> using</c><00:00:37.600><c> fast</c><00:00:38.280><c> API</c><00:00:39.280><c> and</c><00:00:39.600><c> if</c><00:00:39.680><c> you're</c>

00:00:39.869 --> 00:00:39.879 align:start position:0%
and it's using fast API and if you're
 

00:00:39.879 --> 00:00:42.069 align:start position:0%
and it's using fast API and if you're
familiar<00:00:40.200><c> with</c><00:00:40.360><c> fast</c><00:00:40.600><c> API</c><00:00:41.200><c> we're</c><00:00:41.440><c> defining</c><00:00:41.879><c> a</c>

00:00:42.069 --> 00:00:42.079 align:start position:0%
familiar with fast API we're defining a
 

00:00:42.079 --> 00:00:43.069 align:start position:0%
familiar with fast API we're defining a
data

00:00:43.069 --> 00:00:43.079 align:start position:0%
data
 

00:00:43.079 --> 00:00:46.150 align:start position:0%
data
model<00:00:44.079><c> that</c><00:00:44.360><c> expects</c><00:00:45.039><c> the</c><00:00:45.239><c> payload</c><00:00:45.680><c> to</c><00:00:45.800><c> be</c><00:00:46.000><c> a</c>

00:00:46.150 --> 00:00:46.160 align:start position:0%
model that expects the payload to be a
 

00:00:46.160 --> 00:00:47.029 align:start position:0%
model that expects the payload to be a
certain

00:00:47.029 --> 00:00:47.039 align:start position:0%
certain
 

00:00:47.039 --> 00:00:49.430 align:start position:0%
certain
schema<00:00:48.039><c> so</c><00:00:48.559><c> we're</c><00:00:48.800><c> you</c><00:00:48.920><c> know</c><00:00:49.160><c> you're</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
schema so we're you know you're
 

00:00:49.440 --> 00:00:51.229 align:start position:0%
schema so we're you know you're
expecting<00:00:49.879><c> a</c><00:00:50.039><c> payload</c><00:00:50.480><c> to</c><00:00:50.600><c> have</c><00:00:50.840><c> all</c><00:00:50.960><c> of</c><00:00:51.120><c> these</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
expecting a payload to have all of these
 

00:00:51.239 --> 00:00:53.389 align:start position:0%
expecting a payload to have all of these
fields<00:00:51.680><c> event</c><00:00:51.960><c> type</c><00:00:52.239><c> event</c><00:00:52.520><c> author</c><00:00:52.920><c> Alias</c>

00:00:53.389 --> 00:00:53.399 align:start position:0%
fields event type event author Alias
 

00:00:53.399 --> 00:00:56.029 align:start position:0%
fields event type event author Alias
artifact<00:00:53.800><c> version</c><00:00:54.199><c> so</c><00:00:54.320><c> on</c><00:00:54.440><c> and</c><00:00:54.600><c> so</c><00:00:54.800><c> forth</c><00:00:55.800><c> and</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
artifact version so on and so forth and
 

00:00:56.039 --> 00:00:59.270 align:start position:0%
artifact version so on and so forth and
if<00:00:56.239><c> you</c><00:00:56.600><c> send</c><00:00:56.840><c> a</c><00:00:57.000><c> payload</c><00:00:57.440><c> that</c><00:00:57.840><c> doesn't</c><00:00:58.840><c> um</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
if you send a payload that doesn't um
 

00:00:59.280 --> 00:01:02.069 align:start position:0%
if you send a payload that doesn't um
that<00:00:59.399><c> doesn't</c><00:00:59.640><c> conf</c><00:00:59.960><c> form</c><00:01:00.239><c> to</c><00:01:00.399><c> the</c><00:01:00.719><c> schema</c>

00:01:02.069 --> 00:01:02.079 align:start position:0%
that doesn't conf form to the schema
 

00:01:02.079 --> 00:01:04.469 align:start position:0%
that doesn't conf form to the schema
then<00:01:03.079><c> fast</c><00:01:03.359><c> API</c><00:01:03.719><c> was</c><00:01:03.840><c> going</c><00:01:03.920><c> to</c><00:01:04.040><c> throw</c><00:01:04.199><c> you</c><00:01:04.360><c> an</c>

00:01:04.469 --> 00:01:04.479 align:start position:0%
then fast API was going to throw you an
 

00:01:04.479 --> 00:01:06.550 align:start position:0%
then fast API was going to throw you an
error<00:01:05.080><c> specifically</c><00:01:05.640><c> pantic</c><00:01:06.240><c> is</c><00:01:06.360><c> going</c><00:01:06.479><c> to</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
error specifically pantic is going to
 

00:01:06.560 --> 00:01:08.310 align:start position:0%
error specifically pantic is going to
throw<00:01:06.760><c> you</c><00:01:06.840><c> an</c><00:01:06.960><c> error</c><00:01:07.680><c> and</c><00:01:07.799><c> this</c><00:01:07.960><c> a</c><00:01:08.119><c> very</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
throw you an error and this a very
 

00:01:08.320 --> 00:01:09.310 align:start position:0%
throw you an error and this a very
common

00:01:09.310 --> 00:01:09.320 align:start position:0%
common
 

00:01:09.320 --> 00:01:13.789 align:start position:0%
common
pattern<00:01:10.320><c> and</c><00:01:10.479><c> so</c><00:01:11.360><c> one</c><00:01:11.640><c> way</c><00:01:11.799><c> to</c><00:01:12.000><c> do</c><00:01:12.280><c> that</c><00:01:13.240><c> is</c><00:01:13.640><c> one</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
pattern and so one way to do that is one
 

00:01:13.799 --> 00:01:16.710 align:start position:0%
pattern and so one way to do that is one
way<00:01:13.920><c> to</c><00:01:14.240><c> debug</c><00:01:15.240><c> again</c><00:01:15.479><c> is</c><00:01:15.600><c> to</c><00:01:15.799><c> like</c><00:01:16.080><c> trigger</c><00:01:16.439><c> it</c>

00:01:16.710 --> 00:01:16.720 align:start position:0%
way to debug again is to like trigger it
 

00:01:16.720 --> 00:01:19.390 align:start position:0%
way to debug again is to like trigger it
manually<00:01:17.280><c> and</c><00:01:17.400><c> see</c><00:01:17.600><c> it</c><00:01:17.680><c> on</c><00:01:17.799><c> the</c><00:01:17.920><c> server</c><00:01:18.400><c> side</c>

00:01:19.390 --> 00:01:19.400 align:start position:0%
manually and see it on the server side
 

00:01:19.400 --> 00:01:21.230 align:start position:0%
manually and see it on the server side
um<00:01:19.720><c> but</c><00:01:19.880><c> that</c><00:01:20.200><c> again</c><00:01:20.360><c> is</c><00:01:20.560><c> cumbersome</c><00:01:21.079><c> so</c><00:01:21.200><c> I</c>

00:01:21.230 --> 00:01:21.240 align:start position:0%
um but that again is cumbersome so I
 

00:01:21.240 --> 00:01:24.510 align:start position:0%
um but that again is cumbersome so I
want<00:01:21.360><c> to</c><00:01:21.479><c> show</c><00:01:21.640><c> you</c><00:01:21.920><c> a</c><00:01:22.119><c> neat</c><00:01:23.079><c> trick</c><00:01:24.079><c> the</c><00:01:24.240><c> ne</c>

00:01:24.510 --> 00:01:24.520 align:start position:0%
want to show you a neat trick the ne
 

00:01:24.520 --> 00:01:28.510 align:start position:0%
want to show you a neat trick the ne
trick<00:01:25.240><c> is</c><00:01:26.240><c> if</c><00:01:26.320><c> you</c><00:01:26.560><c> go</c><00:01:26.759><c> into</c><00:01:27.360><c> your</c><00:01:27.600><c> teams</c><00:01:28.159><c> page</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
trick is if you go into your teams page
 

00:01:28.520 --> 00:01:33.429 align:start position:0%
trick is if you go into your teams page
so<00:01:28.720><c> this</c><00:01:28.840><c> is</c><00:01:29.119><c> the</c><00:01:29.360><c> the</c><00:01:30.000><c> view</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
 
 

00:01:33.439 --> 00:01:38.230 align:start position:0%
 
Co<00:01:34.439><c> and</c><00:01:34.920><c> you</c><00:01:35.320><c> go</c><00:01:35.600><c> down</c><00:01:35.840><c> to</c><00:01:36.040><c> your</c><00:01:36.360><c> web</c><00:01:37.119><c> hooks</c><00:01:38.119><c> if</c>

00:01:38.230 --> 00:01:38.240 align:start position:0%
Co and you go down to your web hooks if
 

00:01:38.240 --> 00:01:41.830 align:start position:0%
Co and you go down to your web hooks if
you<00:01:38.399><c> click</c><00:01:38.720><c> this</c><00:01:39.079><c> triple</c><00:01:39.640><c> dot</c><00:01:40.640><c> thing</c><00:01:41.600><c> you'll</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
you click this triple dot thing you'll
 

00:01:41.840 --> 00:01:43.590 align:start position:0%
you click this triple dot thing you'll
see<00:01:42.240><c> this</c><00:01:42.520><c> handy</c>

00:01:43.590 --> 00:01:43.600 align:start position:0%
see this handy
 

00:01:43.600 --> 00:01:46.749 align:start position:0%
see this handy
test<00:01:44.600><c> and</c><00:01:44.840><c> this</c><00:01:45.040><c> test</c><00:01:45.600><c> what</c><00:01:45.719><c> it</c><00:01:45.880><c> will</c><00:01:46.079><c> do</c><00:01:46.479><c> is</c><00:01:46.600><c> it</c>

00:01:46.749 --> 00:01:46.759 align:start position:0%
test and this test what it will do is it
 

00:01:46.759 --> 00:01:51.270 align:start position:0%
test and this test what it will do is it
will<00:01:47.040><c> give</c><00:01:47.439><c> you</c><00:01:48.439><c> sort</c><00:01:48.960><c> of</c><00:01:49.960><c> um</c><00:01:50.399><c> this</c><00:01:50.640><c> playground</c>

00:01:51.270 --> 00:01:51.280 align:start position:0%
will give you sort of um this playground
 

00:01:51.280 --> 00:01:54.789 align:start position:0%
will give you sort of um this playground
where<00:01:51.479><c> you</c><00:01:51.640><c> can</c><00:01:52.240><c> have</c><00:01:52.560><c> this</c><00:01:53.079><c> payload</c><00:01:54.079><c> and</c><00:01:54.200><c> so</c>

00:01:54.789 --> 00:01:54.799 align:start position:0%
where you can have this payload and so
 

00:01:54.799 --> 00:01:56.870 align:start position:0%
where you can have this payload and so
um<00:01:54.920><c> there's</c><00:01:55.159><c> different</c><00:01:55.439><c> ways</c><00:01:55.680><c> you</c><00:01:55.880><c> can</c><00:01:56.759><c> you</c>

00:01:56.870 --> 00:01:56.880 align:start position:0%
um there's different ways you can you
 

00:01:56.880 --> 00:01:59.029 align:start position:0%
um there's different ways you can you
can<00:01:57.200><c> like</c><00:01:57.439><c> start</c><00:01:57.880><c> from</c><00:01:58.399><c> so</c><00:01:58.560><c> I</c><00:01:58.680><c> have</c><00:01:58.880><c> this</c>

00:01:59.029 --> 00:01:59.039 align:start position:0%
can like start from so I have this
 

00:01:59.039 --> 00:02:01.789 align:start position:0%
can like start from so I have this
payload<00:01:59.479><c> already</c><00:02:00.280><c> from</c><00:02:01.159><c> this</c><00:02:01.240><c> is</c><00:02:01.479><c> my</c><00:02:01.680><c> the</c>

00:02:01.789 --> 00:02:01.799 align:start position:0%
payload already from this is my the
 

00:02:01.799 --> 00:02:04.190 align:start position:0%
payload already from this is my the
model<00:02:02.119><c> registry</c><00:02:02.680><c> that</c><00:02:02.759><c> I</c><00:02:02.880><c> showed</c><00:02:03.079><c> you</c><00:02:03.280><c> earlier</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
model registry that I showed you earlier
 

00:02:04.200 --> 00:02:06.389 align:start position:0%
model registry that I showed you earlier
and<00:02:04.360><c> I'm</c><00:02:04.520><c> going</c><00:02:04.840><c> back</c><00:02:05.079><c> to</c><00:02:05.520><c> this</c><00:02:05.920><c> this</c><00:02:06.079><c> one</c><00:02:06.240><c> that</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
and I'm going back to this this one that
 

00:02:06.399 --> 00:02:08.990 align:start position:0%
and I'm going back to this this one that
already<00:02:06.640><c> set</c><00:02:06.840><c> up</c><00:02:07.759><c> and</c><00:02:08.000><c> if</c><00:02:08.080><c> you</c><00:02:08.280><c> recall</c><00:02:08.800><c> if</c><00:02:08.879><c> I</c>

00:02:08.990 --> 00:02:09.000 align:start position:0%
already set up and if you recall if I
 

00:02:09.000 --> 00:02:12.470 align:start position:0%
already set up and if you recall if I
view<00:02:09.239><c> the</c><00:02:09.360><c> details</c><00:02:09.800><c> I</c><00:02:09.920><c> have</c><00:02:10.080><c> this</c><00:02:10.560><c> payload</c><00:02:11.560><c> now</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
view the details I have this payload now
 

00:02:12.480 --> 00:02:14.350 align:start position:0%
view the details I have this payload now
I<00:02:12.560><c> want</c><00:02:12.680><c> to</c><00:02:12.800><c> share</c><00:02:13.200><c> like</c><00:02:13.520><c> when</c><00:02:13.680><c> I</c><00:02:13.800><c> was</c><00:02:14.040><c> first</c>

00:02:14.350 --> 00:02:14.360 align:start position:0%
I want to share like when I was first
 

00:02:14.360 --> 00:02:17.509 align:start position:0%
I want to share like when I was first
creating<00:02:14.760><c> this</c><00:02:14.879><c> web</c><00:02:15.160><c> hook</c><00:02:15.480><c> I</c><00:02:15.599><c> kind</c><00:02:15.720><c> of</c><00:02:16.319><c> had</c><00:02:16.519><c> to</c>

00:02:17.509 --> 00:02:17.519 align:start position:0%
creating this web hook I kind of had to
 

00:02:17.519 --> 00:02:21.869 align:start position:0%
creating this web hook I kind of had to
like<00:02:17.800><c> think</c><00:02:18.200><c> about</c><00:02:19.200><c> what</c><00:02:19.400><c> was</c><00:02:19.560><c> being</c><00:02:20.160><c> sent</c><00:02:21.160><c> and</c>

00:02:21.869 --> 00:02:21.879 align:start position:0%
like think about what was being sent and
 

00:02:21.879 --> 00:02:23.589 align:start position:0%
like think about what was being sent and
so

00:02:23.589 --> 00:02:23.599 align:start position:0%
so
 

00:02:23.599 --> 00:02:25.710 align:start position:0%
so
um<00:02:24.599><c> what</c><00:02:24.760><c> happens</c><00:02:25.080><c> is</c><00:02:25.280><c> like</c><00:02:25.400><c> you</c><00:02:25.480><c> might</c><00:02:25.640><c> want</c>

00:02:25.710 --> 00:02:25.720 align:start position:0%
um what happens is like you might want
 

00:02:25.720 --> 00:02:28.670 align:start position:0%
um what happens is like you might want
to<00:02:25.879><c> test</c><00:02:26.160><c> this</c><00:02:26.360><c> payload</c><00:02:26.760><c> so</c><00:02:26.959><c> this</c><00:02:27.040><c> is</c><00:02:27.200><c> the</c><00:02:27.319><c> same</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
to test this payload so this is the same
 

00:02:28.680 --> 00:02:30.790 align:start position:0%
to test this payload so this is the same
payload<00:02:29.680><c> and</c>

00:02:30.790 --> 00:02:30.800 align:start position:0%
payload and
 

00:02:30.800 --> 00:02:32.350 align:start position:0%
payload and
so<00:02:31.000><c> there's</c><00:02:31.360><c> there's</c><00:02:31.760><c> different</c><00:02:32.000><c> ways</c><00:02:32.239><c> that</c>

00:02:32.350 --> 00:02:32.360 align:start position:0%
so there's there's different ways that
 

00:02:32.360 --> 00:02:34.030 align:start position:0%
so there's there's different ways that
you<00:02:32.480><c> can</c><00:02:32.640><c> try</c><00:02:32.840><c> to</c><00:02:33.120><c> figure</c><00:02:33.360><c> out</c><00:02:33.599><c> what's</c><00:02:33.840><c> going</c>

00:02:34.030 --> 00:02:34.040 align:start position:0%
you can try to figure out what's going
 

00:02:34.040 --> 00:02:35.910 align:start position:0%
you can try to figure out what's going
on<00:02:34.560><c> if</c><00:02:34.680><c> you</c><00:02:34.800><c> have</c><00:02:34.920><c> an</c><00:02:35.080><c> error</c><00:02:35.400><c> one</c><00:02:35.560><c> is</c><00:02:35.640><c> to</c><00:02:35.800><c> look</c>

00:02:35.910 --> 00:02:35.920 align:start position:0%
on if you have an error one is to look
 

00:02:35.920 --> 00:02:38.550 align:start position:0%
on if you have an error one is to look
at<00:02:36.040><c> the</c><00:02:36.239><c> logs</c><00:02:36.879><c> so</c><00:02:37.000><c> in</c><00:02:37.120><c> model's</c><00:02:37.640><c> case</c><00:02:38.239><c> you</c><00:02:38.360><c> can</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
at the logs so in model's case you can
 

00:02:38.560 --> 00:02:40.509 align:start position:0%
at the logs so in model's case you can
look<00:02:38.680><c> at</c><00:02:38.800><c> the</c><00:02:38.959><c> logs</c><00:02:39.720><c> like</c><00:02:39.879><c> I</c><00:02:39.959><c> can</c><00:02:40.080><c> see</c><00:02:40.280><c> a</c>

00:02:40.509 --> 00:02:40.519 align:start position:0%
look at the logs like I can see a
 

00:02:40.519 --> 00:02:42.589 align:start position:0%
look at the logs like I can see a
request<00:02:40.959><c> I</c><00:02:41.040><c> can</c><00:02:41.200><c> see</c><00:02:41.959><c> okay</c><00:02:42.120><c> there's</c><00:02:42.319><c> some</c><00:02:42.480><c> kind</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
request I can see okay there's some kind
 

00:02:42.599 --> 00:02:45.110 align:start position:0%
request I can see okay there's some kind
of<00:02:43.120><c> logs</c><00:02:44.120><c> now</c><00:02:44.480><c> depending</c><00:02:44.800><c> on</c><00:02:44.920><c> your</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
of logs now depending on your
 

00:02:45.120 --> 00:02:46.790 align:start position:0%
of logs now depending on your
application<00:02:45.599><c> you</c><00:02:45.720><c> might</c><00:02:46.040><c> even</c><00:02:46.319><c> have</c><00:02:46.560><c> access</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
application you might even have access
 

00:02:46.800 --> 00:02:50.350 align:start position:0%
application you might even have access
to<00:02:46.920><c> the</c><00:02:47.040><c> logs</c><00:02:47.959><c> like</c><00:02:48.480><c> and</c><00:02:48.760><c> in</c><00:02:48.920><c> this</c><00:02:49.120><c> case</c><00:02:49.760><c> um</c><00:02:50.239><c> you</c>

00:02:50.350 --> 00:02:50.360 align:start position:0%
to the logs like and in this case um you
 

00:02:50.360 --> 00:02:52.190 align:start position:0%
to the logs like and in this case um you
know<00:02:50.519><c> these</c><00:02:50.720><c> logs</c><00:02:51.000><c> are</c><00:02:51.159><c> not</c><00:02:51.480><c> really</c><00:02:51.720><c> showing</c>

00:02:52.190 --> 00:02:52.200 align:start position:0%
know these logs are not really showing
 

00:02:52.200 --> 00:02:54.589 align:start position:0%
know these logs are not really showing
me<00:02:52.640><c> maybe</c><00:02:53.120><c> what</c><00:02:53.319><c> I'm</c><00:02:53.440><c> looking</c><00:02:53.640><c> for</c><00:02:54.319><c> and</c><00:02:54.480><c> have</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
me maybe what I'm looking for and have
 

00:02:54.599 --> 00:02:57.070 align:start position:0%
me maybe what I'm looking for and have
to<00:02:54.760><c> dig</c><00:02:55.000><c> through</c><00:02:55.200><c> it</c><00:02:55.800><c> so</c><00:02:56.159><c> it's</c><00:02:56.400><c> actually</c><00:02:56.920><c> uh</c>

00:02:57.070 --> 00:02:57.080 align:start position:0%
to dig through it so it's actually uh
 

00:02:57.080 --> 00:02:59.949 align:start position:0%
to dig through it so it's actually uh
helpful<00:02:57.480><c> to</c><00:02:57.640><c> see</c><00:02:57.840><c> the</c><00:02:58.280><c> response</c><00:02:59.280><c> um</c><00:02:59.400><c> you</c><00:02:59.519><c> know</c>

00:02:59.949 --> 00:02:59.959 align:start position:0%
helpful to see the response um you know
 

00:02:59.959 --> 00:03:01.830 align:start position:0%
helpful to see the response um you know
especially<00:03:00.239><c> if</c><00:03:00.319><c> you're</c><00:03:00.440><c> not</c><00:03:00.599><c> using</c>

00:03:01.830 --> 00:03:01.840 align:start position:0%
especially if you're not using
 

00:03:01.840 --> 00:03:03.830 align:start position:0%
especially if you're not using
curl<00:03:02.840><c> um</c><00:03:02.920><c> you</c><00:03:03.000><c> want</c><00:03:03.120><c> to</c><00:03:03.239><c> see</c><00:03:03.519><c> what</c><00:03:03.640><c> the</c>

00:03:03.830 --> 00:03:03.840 align:start position:0%
curl um you want to see what the
 

00:03:03.840 --> 00:03:06.350 align:start position:0%
curl um you want to see what the
response<00:03:04.319><c> is</c><00:03:04.879><c> and</c><00:03:05.280><c> so</c><00:03:05.519><c> basically</c><00:03:06.080><c> what</c><00:03:06.239><c> I'm</c>

00:03:06.350 --> 00:03:06.360 align:start position:0%
response is and so basically what I'm
 

00:03:06.360 --> 00:03:08.390 align:start position:0%
response is and so basically what I'm
going<00:03:06.480><c> to</c><00:03:06.680><c> do</c><00:03:07.440><c> is</c><00:03:07.599><c> let's</c><00:03:07.840><c> say</c><00:03:07.959><c> I</c><00:03:08.080><c> have</c><00:03:08.200><c> this</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
going to do is let's say I have this
 

00:03:08.400 --> 00:03:10.470 align:start position:0%
going to do is let's say I have this
payload<00:03:08.879><c> and</c><00:03:09.000><c> let's</c><00:03:09.159><c> say</c><00:03:09.400><c> when</c><00:03:09.879><c> um</c><00:03:10.159><c> let's</c><00:03:10.319><c> say</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
payload and let's say when um let's say
 

00:03:10.480 --> 00:03:13.589 align:start position:0%
payload and let's say when um let's say
I<00:03:10.599><c> forgot</c><00:03:10.879><c> to</c><00:03:11.080><c> include</c><00:03:11.440><c> the</c><00:03:11.599><c> author</c><00:03:12.440><c> for</c>

00:03:13.589 --> 00:03:13.599 align:start position:0%
I forgot to include the author for
 

00:03:13.599 --> 00:03:16.070 align:start position:0%
I forgot to include the author for
example<00:03:14.599><c> and</c><00:03:14.720><c> that's</c><00:03:14.920><c> really</c><00:03:15.080><c> easy</c><00:03:15.319><c> to</c><00:03:15.480><c> do</c><00:03:15.920><c> is</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
example and that's really easy to do is
 

00:03:16.080 --> 00:03:17.949 align:start position:0%
example and that's really easy to do is
to<00:03:16.280><c> make</c><00:03:16.440><c> a</c><00:03:16.599><c> silly</c><00:03:16.959><c> mistake</c><00:03:17.360><c> like</c><00:03:17.560><c> this</c><00:03:17.799><c> it</c>

00:03:17.949 --> 00:03:17.959 align:start position:0%
to make a silly mistake like this it
 

00:03:17.959 --> 00:03:20.589 align:start position:0%
to make a silly mistake like this it
happens<00:03:18.200><c> to</c><00:03:18.599><c> everybody</c><00:03:19.599><c> um</c><00:03:19.799><c> and</c><00:03:20.120><c> again</c><00:03:20.360><c> if</c><00:03:20.480><c> I</c>

00:03:20.589 --> 00:03:20.599 align:start position:0%
happens to everybody um and again if I
 

00:03:20.599 --> 00:03:22.270 align:start position:0%
happens to everybody um and again if I
don't<00:03:20.840><c> have</c><00:03:21.040><c> the</c><00:03:21.200><c> author</c><00:03:21.599><c> it's</c><00:03:21.799><c> going</c><00:03:21.959><c> to</c>

00:03:22.270 --> 00:03:22.280 align:start position:0%
don't have the author it's going to
 

00:03:22.280 --> 00:03:24.470 align:start position:0%
don't have the author it's going to
error<00:03:22.799><c> because</c><00:03:23.120><c> it's</c><00:03:23.319><c> not</c><00:03:23.599><c> having</c><00:03:24.040><c> this</c><00:03:24.239><c> like</c>

00:03:24.470 --> 00:03:24.480 align:start position:0%
error because it's not having this like
 

00:03:24.480 --> 00:03:28.149 align:start position:0%
error because it's not having this like
field<00:03:25.480><c> and</c><00:03:25.599><c> so</c><00:03:25.760><c> let's</c><00:03:26.040><c> test</c><00:03:26.720><c> that</c><00:03:27.720><c> let's</c><00:03:27.959><c> test</c>

00:03:28.149 --> 00:03:28.159 align:start position:0%
field and so let's test that let's test
 

00:03:28.159 --> 00:03:30.350 align:start position:0%
field and so let's test that let's test
that<00:03:28.319><c> against</c><00:03:28.599><c> this</c><00:03:28.720><c> modal</c><00:03:29.040><c> endpoint</c>

00:03:30.350 --> 00:03:30.360 align:start position:0%
that against this modal endpoint
 

00:03:30.360 --> 00:03:32.869 align:start position:0%
that against this modal endpoint
I'm<00:03:30.439><c> going</c><00:03:30.519><c> to</c><00:03:30.640><c> go</c><00:03:30.760><c> back</c><00:03:30.879><c> to</c><00:03:31.080><c> test</c><00:03:31.400><c> web</c><00:03:31.720><c> hook</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
I'm going to go back to test web hook
 

00:03:32.879 --> 00:03:35.670 align:start position:0%
I'm going to go back to test web hook
here<00:03:33.879><c> and</c><00:03:34.000><c> what</c><00:03:34.120><c> it's</c><00:03:34.239><c> going</c><00:03:34.360><c> to</c><00:03:34.519><c> do</c><00:03:34.760><c> is</c><00:03:35.120><c> um</c><00:03:35.439><c> W</c>

00:03:35.670 --> 00:03:35.680 align:start position:0%
here and what it's going to do is um W
 

00:03:35.680 --> 00:03:37.309 align:start position:0%
here and what it's going to do is um W
and<00:03:35.840><c> bies</c><00:03:36.239><c> is</c><00:03:36.360><c> going</c><00:03:36.480><c> to</c><00:03:36.640><c> show</c><00:03:36.840><c> you</c><00:03:37.080><c> the</c>

00:03:37.309 --> 00:03:37.319 align:start position:0%
and bies is going to show you the
 

00:03:37.319 --> 00:03:39.670 align:start position:0%
and bies is going to show you the
response<00:03:38.200><c> from</c><00:03:38.640><c> the</c><00:03:38.799><c> web</c><00:03:39.040><c> server</c><00:03:39.360><c> in</c><00:03:39.519><c> this</c>

00:03:39.670 --> 00:03:39.680 align:start position:0%
response from the web server in this
 

00:03:39.680 --> 00:03:43.070 align:start position:0%
response from the web server in this
case<00:03:40.000><c> what</c><00:03:40.120><c> modal</c><00:03:40.799><c> is</c><00:03:41.280><c> is</c><00:03:41.640><c> uh</c><00:03:41.760><c> saying</c><00:03:42.720><c> in</c><00:03:42.879><c> this</c>

00:03:43.070 --> 00:03:43.080 align:start position:0%
case what modal is is uh saying in this
 

00:03:43.080 --> 00:03:46.789 align:start position:0%
case what modal is is uh saying in this
case<00:03:43.560><c> this</c><00:03:43.680><c> is</c><00:03:43.840><c> a</c><00:03:44.000><c> little</c><00:03:44.280><c> bit</c><00:03:45.239><c> um</c><00:03:46.120><c> confusing</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
case this is a little bit um confusing
 

00:03:46.799 --> 00:03:49.270 align:start position:0%
case this is a little bit um confusing
because<00:03:46.959><c> this</c><00:03:47.080><c> is</c><00:03:47.239><c> actually</c><00:03:48.040><c> pantic</c><00:03:49.040><c> this</c><00:03:49.159><c> is</c>

00:03:49.270 --> 00:03:49.280 align:start position:0%
because this is actually pantic this is
 

00:03:49.280 --> 00:03:52.069 align:start position:0%
because this is actually pantic this is
like<00:03:49.400><c> a</c><00:03:49.519><c> lowlevel</c><00:03:50.080><c> pantic</c><00:03:50.640><c> error</c><00:03:51.239><c> or</c><00:03:51.720><c> uh</c><00:03:51.959><c> kind</c>

00:03:52.069 --> 00:03:52.079 align:start position:0%
like a lowlevel pantic error or uh kind
 

00:03:52.079 --> 00:03:53.630 align:start position:0%
like a lowlevel pantic error or uh kind
of<00:03:52.319><c> response</c><00:03:52.799><c> and</c><00:03:52.920><c> it's</c><00:03:53.040><c> showing</c><00:03:53.360><c> you</c><00:03:53.519><c> that</c>

00:03:53.630 --> 00:03:53.640 align:start position:0%
of response and it's showing you that
 

00:03:53.640 --> 00:03:57.670 align:start position:0%
of response and it's showing you that
it's<00:03:54.200><c> missing</c><00:03:55.200><c> a</c><00:03:55.360><c> field</c><00:03:56.200><c> what</c><00:03:56.400><c> this</c><00:03:57.360><c> like</c><00:03:57.519><c> how</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
it's missing a field what this like how
 

00:03:57.680 --> 00:03:59.789 align:start position:0%
it's missing a field what this like how
you<00:03:57.840><c> translate</c><00:03:58.439><c> this</c><00:03:58.879><c> is</c><00:03:59.079><c> like</c><00:03:59.280><c> your</c><00:03:59.519><c> your</c>

00:03:59.789 --> 00:03:59.799 align:start position:0%
you translate this is like your your
 

00:03:59.799 --> 00:04:01.270 align:start position:0%
you translate this is like your your
missing<00:04:00.079><c> this</c><00:04:00.239><c> event</c><00:04:00.480><c> author</c><00:04:00.760><c> field</c><00:04:01.159><c> that's</c>

00:04:01.270 --> 00:04:01.280 align:start position:0%
missing this event author field that's
 

00:04:01.280 --> 00:04:02.630 align:start position:0%
missing this event author field that's
what<00:04:01.360><c> this</c><00:04:01.480><c> means</c><00:04:01.680><c> and</c><00:04:01.799><c> this</c><00:04:01.920><c> is</c><00:04:02.079><c> ptic</c>

00:04:02.630 --> 00:04:02.640 align:start position:0%
what this means and this is ptic
 

00:04:02.640 --> 00:04:03.990 align:start position:0%
what this means and this is ptic
specific<00:04:03.040><c> this</c><00:04:03.120><c> is</c><00:04:03.280><c> not</c><00:04:03.480><c> anything</c><00:04:03.680><c> to</c><00:04:03.799><c> do</c>

00:04:03.990 --> 00:04:04.000 align:start position:0%
specific this is not anything to do
 

00:04:04.000 --> 00:04:05.470 align:start position:0%
specific this is not anything to do
weights<00:04:04.159><c> and</c><00:04:04.319><c> bies</c><00:04:04.760><c> this</c><00:04:04.840><c> is</c><00:04:05.040><c> like</c><00:04:05.200><c> what</c><00:04:05.319><c> your</c>

00:04:05.470 --> 00:04:05.480 align:start position:0%
weights and bies this is like what your
 

00:04:05.480 --> 00:04:07.350 align:start position:0%
weights and bies this is like what your
web<00:04:05.680><c> server</c><00:04:05.959><c> is</c><00:04:06.159><c> responding</c><00:04:06.640><c> with</c><00:04:07.200><c> it's</c>

00:04:07.350 --> 00:04:07.360 align:start position:0%
web server is responding with it's
 

00:04:07.360 --> 00:04:09.309 align:start position:0%
web server is responding with it's
actually<00:04:07.560><c> really</c><00:04:07.799><c> useful</c><00:04:08.120><c> to</c><00:04:08.239><c> see</c><00:04:08.519><c> this</c><00:04:09.040><c> so</c><00:04:09.200><c> if</c>

00:04:09.309 --> 00:04:09.319 align:start position:0%
actually really useful to see this so if
 

00:04:09.319 --> 00:04:12.149 align:start position:0%
actually really useful to see this so if
I<00:04:09.480><c> if</c><00:04:09.560><c> I</c><00:04:09.760><c> add</c><00:04:10.000><c> this</c><00:04:10.519><c> back</c><00:04:11.519><c> for</c><00:04:11.680><c> example</c><00:04:12.079><c> I'm</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
I if I add this back for example I'm
 

00:04:12.159 --> 00:04:13.750 align:start position:0%
I if I add this back for example I'm
going<00:04:12.239><c> to</c><00:04:12.439><c> add</c><00:04:12.640><c> that</c><00:04:12.760><c> field</c><00:04:13.200><c> back</c><00:04:13.360><c> and</c><00:04:13.519><c> test</c>

00:04:13.750 --> 00:04:13.760 align:start position:0%
going to add that field back and test
 

00:04:13.760 --> 00:04:16.150 align:start position:0%
going to add that field back and test
the<00:04:13.920><c> web</c><00:04:14.159><c> hook</c><00:04:15.159><c> we</c><00:04:15.239><c> will</c><00:04:15.439><c> see</c><00:04:15.760><c> that</c><00:04:16.000><c> now</c>

00:04:16.150 --> 00:04:16.160 align:start position:0%
the web hook we will see that now
 

00:04:16.160 --> 00:04:17.390 align:start position:0%
the web hook we will see that now
there's<00:04:16.359><c> a</c>

00:04:17.390 --> 00:04:17.400 align:start position:0%
there's a
 

00:04:17.400 --> 00:04:20.710 align:start position:0%
there's a
success<00:04:18.720><c> um</c><00:04:19.720><c> that</c><00:04:19.880><c> there's</c><00:04:20.040><c> a</c><00:04:20.199><c> response</c><00:04:20.560><c> and</c>

00:04:20.710 --> 00:04:20.720 align:start position:0%
success um that there's a response and
 

00:04:20.720 --> 00:04:23.950 align:start position:0%
success um that there's a response and
there's<00:04:20.919><c> no</c><00:04:21.560><c> error</c><00:04:22.560><c> and</c><00:04:22.960><c> you</c><00:04:23.080><c> know</c><00:04:23.360><c> again</c><00:04:23.880><c> we</c>

00:04:23.950 --> 00:04:23.960 align:start position:0%
there's no error and you know again we
 

00:04:23.960 --> 00:04:26.070 align:start position:0%
there's no error and you know again we
can<00:04:24.120><c> see</c><00:04:24.360><c> that</c><00:04:24.520><c> if</c><00:04:24.600><c> I</c><00:04:24.720><c> look</c><00:04:24.840><c> at</c><00:04:24.960><c> the</c><00:04:25.120><c> logs</c><00:04:25.960><c> we</c>

00:04:26.070 --> 00:04:26.080 align:start position:0%
can see that if I look at the logs we
 

00:04:26.080 --> 00:04:27.909 align:start position:0%
can see that if I look at the logs we
can<00:04:26.199><c> see</c><00:04:26.479><c> that</c><00:04:26.759><c> hey</c><00:04:26.960><c> there</c><00:04:27.120><c> was</c><00:04:27.240><c> a</c><00:04:27.440><c> success</c>

00:04:27.909 --> 00:04:27.919 align:start position:0%
can see that hey there was a success
 

00:04:27.919 --> 00:04:30.270 align:start position:0%
can see that hey there was a success
here<00:04:28.800><c> and</c><00:04:28.919><c> then</c><00:04:29.080><c> that</c><00:04:29.199><c> was</c><00:04:29.400><c> preced</c><00:04:29.680><c> ceeded</c><00:04:30.160><c> by</c>

00:04:30.270 --> 00:04:30.280 align:start position:0%
here and then that was preced ceeded by
 

00:04:30.280 --> 00:04:30.990 align:start position:0%
here and then that was preced ceeded by
some

00:04:30.990 --> 00:04:31.000 align:start position:0%
some
 

00:04:31.000 --> 00:04:33.230 align:start position:0%
some
errors<00:04:32.000><c> and</c><00:04:32.360><c> you</c><00:04:32.479><c> know</c><00:04:32.639><c> in</c><00:04:32.800><c> this</c><00:04:32.960><c> case</c>

00:04:33.230 --> 00:04:33.240 align:start position:0%
errors and you know in this case
 

00:04:33.240 --> 00:04:35.870 align:start position:0%
errors and you know in this case
actually<00:04:33.680><c> like</c><00:04:34.639><c> I</c><00:04:34.800><c> can't</c><00:04:35.080><c> really</c><00:04:35.600><c> I</c><00:04:35.680><c> don't</c>

00:04:35.870 --> 00:04:35.880 align:start position:0%
actually like I can't really I don't
 

00:04:35.880 --> 00:04:37.510 align:start position:0%
actually like I can't really I don't
really<00:04:36.080><c> know</c><00:04:36.280><c> what</c><00:04:36.440><c> those</c><00:04:36.600><c> errors</c><00:04:36.960><c> are</c><00:04:37.280><c> to</c><00:04:37.400><c> be</c>

00:04:37.510 --> 00:04:37.520 align:start position:0%
really know what those errors are to be
 

00:04:37.520 --> 00:04:40.029 align:start position:0%
really know what those errors are to be
honest<00:04:37.960><c> like</c><00:04:38.240><c> I</c><00:04:38.360><c> can't</c><00:04:38.520><c> even</c><00:04:38.720><c> see</c><00:04:38.960><c> them</c><00:04:39.880><c> and</c>

00:04:40.029 --> 00:04:40.039 align:start position:0%
honest like I can't even see them and
 

00:04:40.039 --> 00:04:43.830 align:start position:0%
honest like I can't even see them and
this<00:04:40.120><c> is</c><00:04:40.400><c> like</c><00:04:40.520><c> a</c><00:04:41.000><c> real</c><00:04:42.320><c> um</c><00:04:43.320><c> this</c><00:04:43.400><c> is</c><00:04:43.600><c> kind</c><00:04:43.720><c> of</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
this is like a real um this is kind of
 

00:04:43.840 --> 00:04:45.830 align:start position:0%
this is like a real um this is kind of
like<00:04:43.919><c> a</c><00:04:44.039><c> real</c><00:04:44.440><c> case</c><00:04:44.759><c> where</c><00:04:45.280><c> having</c><00:04:45.560><c> the</c>

00:04:45.830 --> 00:04:45.840 align:start position:0%
like a real case where having the
 

00:04:45.840 --> 00:04:47.830 align:start position:0%
like a real case where having the
response<00:04:46.560><c> so</c><00:04:46.759><c> there's</c><00:04:46.960><c> no</c><00:04:47.199><c> there's</c><00:04:47.360><c> no</c><00:04:47.520><c> error</c>

00:04:47.830 --> 00:04:47.840 align:start position:0%
response so there's no there's no error
 

00:04:47.840 --> 00:04:49.830 align:start position:0%
response so there's no there's no error
being<00:04:48.080><c> logged</c><00:04:48.479><c> on</c><00:04:48.639><c> the</c><00:04:48.759><c> server</c><00:04:49.160><c> side</c><00:04:49.520><c> I</c><00:04:49.600><c> could</c>

00:04:49.830 --> 00:04:49.840 align:start position:0%
being logged on the server side I could
 

00:04:49.840 --> 00:04:52.629 align:start position:0%
being logged on the server side I could
improve<00:04:50.280><c> my</c><00:04:50.479><c> code</c><00:04:51.400><c> to</c><00:04:51.680><c> log</c><00:04:52.039><c> errors</c><00:04:52.360><c> on</c><00:04:52.520><c> the</c>

00:04:52.629 --> 00:04:52.639 align:start position:0%
improve my code to log errors on the
 

00:04:52.639 --> 00:04:55.070 align:start position:0%
improve my code to log errors on the
server<00:04:53.000><c> side</c><00:04:53.440><c> but</c><00:04:53.720><c> I</c><00:04:53.880><c> don't</c><00:04:54.160><c> do</c><00:04:54.400><c> that</c><00:04:54.800><c> like</c><00:04:54.960><c> if</c>

00:04:55.070 --> 00:04:55.080 align:start position:0%
server side but I don't do that like if
 

00:04:55.080 --> 00:04:56.390 align:start position:0%
server side but I don't do that like if
there's<00:04:55.240><c> an</c><00:04:55.400><c> error</c><00:04:55.720><c> it</c><00:04:55.800><c> just</c><00:04:55.960><c> errors</c><00:04:56.320><c> it</c>

00:04:56.390 --> 00:04:56.400 align:start position:0%
there's an error it just errors it
 

00:04:56.400 --> 00:04:58.430 align:start position:0%
there's an error it just errors it
doesn't<00:04:56.639><c> print</c><00:04:56.919><c> anything</c><00:04:57.680><c> so</c><00:04:58.000><c> having</c><00:04:58.240><c> the</c>

00:04:58.430 --> 00:04:58.440 align:start position:0%
doesn't print anything so having the
 

00:04:58.440 --> 00:05:00.110 align:start position:0%
doesn't print anything so having the
response<00:04:58.880><c> from</c><00:04:59.039><c> the</c><00:04:59.120><c> server</c><00:04:59.720><c> actually</c><00:04:59.919><c> is</c>

00:05:00.110 --> 00:05:00.120 align:start position:0%
response from the server actually is
 

00:05:00.120 --> 00:05:03.189 align:start position:0%
response from the server actually is
really<00:05:00.400><c> useful</c><00:05:01.400><c> um</c><00:05:01.759><c> and</c><00:05:01.919><c> has</c><00:05:02.120><c> helped</c><00:05:02.360><c> me</c><00:05:02.800><c> cat</c>

00:05:03.189 --> 00:05:03.199 align:start position:0%
really useful um and has helped me cat
 

00:05:03.199 --> 00:05:04.350 align:start position:0%
really useful um and has helped me cat
catch<00:05:03.400><c> errors</c><00:05:03.720><c> and</c><00:05:03.840><c> this</c><00:05:03.919><c> has</c><00:05:04.120><c> actually</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
catch errors and this has actually
 

00:05:04.360 --> 00:05:07.270 align:start position:0%
catch errors and this has actually
helped<00:05:04.600><c> me</c><00:05:04.720><c> in</c><00:05:04.880><c> real</c><00:05:05.120><c> life</c><00:05:06.080><c> um</c><00:05:06.320><c> and</c><00:05:06.479><c> it</c><00:05:06.720><c> and</c><00:05:06.840><c> it</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
helped me in real life um and it and it
 

00:05:07.280 --> 00:05:10.430 align:start position:0%
helped me in real life um and it and it
uh<00:05:07.479><c> like</c><00:05:07.720><c> be</c><00:05:07.880><c> able</c><00:05:08.080><c> to</c><00:05:08.280><c> rapidly</c><00:05:09.199><c> test</c><00:05:10.199><c> and</c>

00:05:10.430 --> 00:05:10.440 align:start position:0%
uh like be able to rapidly test and
 

00:05:10.440 --> 00:05:14.070 align:start position:0%
uh like be able to rapidly test and
basically<00:05:11.400><c> another</c><00:05:11.759><c> way</c><00:05:11.880><c> to</c><00:05:12.039><c> use</c><00:05:12.360><c> this</c><00:05:13.080><c> is</c>

00:05:14.070 --> 00:05:14.080 align:start position:0%
basically another way to use this is
 

00:05:14.080 --> 00:05:15.710 align:start position:0%
basically another way to use this is
when<00:05:14.240><c> you're</c><00:05:14.479><c> actually</c><00:05:14.800><c> developing</c><00:05:15.280><c> your</c><00:05:15.479><c> web</c>

00:05:15.710 --> 00:05:15.720 align:start position:0%
when you're actually developing your web
 

00:05:15.720 --> 00:05:17.350 align:start position:0%
when you're actually developing your web
server<00:05:16.400><c> like</c><00:05:16.560><c> let's</c><00:05:16.720><c> say</c><00:05:16.880><c> I</c><00:05:16.960><c> want</c><00:05:17.080><c> to</c>

00:05:17.350 --> 00:05:17.360 align:start position:0%
server like let's say I want to
 

00:05:17.360 --> 00:05:19.830 align:start position:0%
server like let's say I want to
continuously<00:05:18.000><c> change</c><00:05:18.440><c> this</c><00:05:18.639><c> code</c><00:05:19.360><c> and</c><00:05:19.520><c> change</c>

00:05:19.830 --> 00:05:19.840 align:start position:0%
continuously change this code and change
 

00:05:19.840 --> 00:05:22.390 align:start position:0%
continuously change this code and change
the<00:05:20.039><c> schema</c><00:05:20.520><c> and</c><00:05:20.680><c> change</c><00:05:21.199><c> like</c><00:05:21.800><c> you</c><00:05:21.880><c> know</c><00:05:22.240><c> the</c>

00:05:22.390 --> 00:05:22.400 align:start position:0%
the schema and change like you know the
 

00:05:22.400 --> 00:05:24.830 align:start position:0%
the schema and change like you know the
code<00:05:22.720><c> of</c><00:05:22.880><c> the</c><00:05:23.000><c> web</c><00:05:23.240><c> server</c><00:05:24.199><c> well</c><00:05:24.400><c> while</c><00:05:24.600><c> you're</c>

00:05:24.830 --> 00:05:24.840 align:start position:0%
code of the web server well while you're
 

00:05:24.840 --> 00:05:27.790 align:start position:0%
code of the web server well while you're
developing<00:05:25.400><c> it</c><00:05:26.199><c> um</c><00:05:26.360><c> you</c><00:05:26.479><c> can</c><00:05:26.720><c> just</c><00:05:27.000><c> like</c>

00:05:27.790 --> 00:05:27.800 align:start position:0%
developing it um you can just like
 

00:05:27.800 --> 00:05:30.550 align:start position:0%
developing it um you can just like
basically<00:05:28.800><c> uh</c><00:05:28.960><c> have</c><00:05:29.160><c> two</c><00:05:29.319><c> windows</c><00:05:29.919><c> open</c><00:05:30.240><c> where</c>

00:05:30.550 --> 00:05:30.560 align:start position:0%
basically uh have two windows open where
 

00:05:30.560 --> 00:05:33.350 align:start position:0%
basically uh have two windows open where
this<00:05:30.840><c> code</c><00:05:31.840><c> and</c><00:05:32.440><c> sort</c><00:05:32.639><c> of</c><00:05:32.840><c> this</c><00:05:33.000><c> testing</c>

00:05:33.350 --> 00:05:33.360 align:start position:0%
this code and sort of this testing
 

00:05:33.360 --> 00:05:36.230 align:start position:0%
this code and sort of this testing
window<00:05:33.840><c> and</c><00:05:34.319><c> keep</c><00:05:34.560><c> sending</c><00:05:35.039><c> payloads</c><00:05:35.919><c> and</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
window and keep sending payloads and
 

00:05:36.240 --> 00:05:39.189 align:start position:0%
window and keep sending payloads and
sort<00:05:36.400><c> of</c><00:05:36.759><c> keep</c><00:05:37.039><c> debugging</c><00:05:38.039><c> and</c><00:05:38.240><c> that's</c><00:05:38.479><c> also</c>

00:05:39.189 --> 00:05:39.199 align:start position:0%
sort of keep debugging and that's also
 

00:05:39.199 --> 00:05:41.110 align:start position:0%
sort of keep debugging and that's also
uh<00:05:39.360><c> very</c><00:05:39.560><c> helpful</c><00:05:40.199><c> so</c><00:05:40.400><c> just</c><00:05:40.520><c> want</c><00:05:40.639><c> to</c><00:05:40.720><c> show</c><00:05:40.919><c> you</c>

00:05:41.110 --> 00:05:41.120 align:start position:0%
uh very helpful so just want to show you
 

00:05:41.120 --> 00:05:44.270 align:start position:0%
uh very helpful so just want to show you
that<00:05:41.360><c> this</c><00:05:41.479><c> is</c><00:05:42.199><c> actually</c><00:05:43.199><c> like</c><00:05:43.960><c> like</c><00:05:44.080><c> one</c><00:05:44.199><c> of</c>

00:05:44.270 --> 00:05:44.280 align:start position:0%
that this is actually like like one of
 

00:05:44.280 --> 00:05:46.309 align:start position:0%
that this is actually like like one of
the<00:05:44.400><c> most</c><00:05:44.560><c> helpful</c><00:05:45.039><c> things</c><00:05:45.680><c> like</c><00:05:45.840><c> features</c><00:05:46.160><c> to</c>

00:05:46.309 --> 00:05:46.319 align:start position:0%
the most helpful things like features to
 

00:05:46.319 --> 00:05:48.670 align:start position:0%
the most helpful things like features to
know<00:05:46.520><c> about</c><00:05:46.759><c> in</c><00:05:47.039><c> web</c><00:05:47.360><c> hooks</c><00:05:47.919><c> is</c><00:05:48.240><c> act</c><00:05:48.479><c> is</c>

00:05:48.670 --> 00:05:48.680 align:start position:0%
know about in web hooks is act is
 

00:05:48.680 --> 00:05:50.710 align:start position:0%
know about in web hooks is act is
testing<00:05:49.319><c> because</c><00:05:49.440><c> it</c><00:05:49.560><c> helps</c><00:05:49.759><c> you</c><00:05:49.960><c> develop</c><00:05:50.479><c> web</c>

00:05:50.710 --> 00:05:50.720 align:start position:0%
testing because it helps you develop web
 

00:05:50.720 --> 00:05:53.720 align:start position:0%
testing because it helps you develop web
hooks

