WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:02.750 align:start position:0%
 
this<00:00:00.320><c> is</c><00:00:00.520><c> autog</c><00:00:00.880><c> gr</c><00:00:01.160><c> and</c><00:00:01.360><c> it's</c><00:00:01.560><c> beta</c><00:00:01.839><c> version</c>

00:00:02.750 --> 00:00:02.760 align:start position:0%
this is autog gr and it's beta version
 

00:00:02.760 --> 00:00:05.710 align:start position:0%
this is autog gr and it's beta version
54220<00:00:03.760><c> I</c><00:00:03.879><c> got</c><00:00:04.000><c> some</c><00:00:04.200><c> grief</c><00:00:04.480><c> for</c><00:00:04.680><c> using</c><00:00:04.960><c> 11</c><00:00:05.359><c> labs</c>

00:00:05.710 --> 00:00:05.720 align:start position:0%
54220 I got some grief for using 11 labs
 

00:00:05.720 --> 00:00:07.749 align:start position:0%
54220 I got some grief for using 11 labs
to<00:00:05.920><c> narrate</c><00:00:06.440><c> these</c><00:00:06.680><c> videos</c><00:00:07.120><c> so</c><00:00:07.240><c> I'm</c><00:00:07.359><c> using</c><00:00:07.640><c> my</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
to narrate these videos so I'm using my
 

00:00:07.759 --> 00:00:10.910 align:start position:0%
to narrate these videos so I'm using my
own<00:00:08.080><c> voice</c><00:00:08.440><c> this</c><00:00:08.639><c> time</c><00:00:09.599><c> it's</c><00:00:09.760><c> still</c><00:00:10.000><c> 11</c><00:00:10.440><c> Labs</c>

00:00:10.910 --> 00:00:10.920 align:start position:0%
own voice this time it's still 11 Labs
 

00:00:10.920 --> 00:00:13.190 align:start position:0%
own voice this time it's still 11 Labs
though<00:00:11.360><c> I</c><00:00:11.480><c> have</c><00:00:11.679><c> Parkinson's</c><00:00:12.440><c> and</c><00:00:12.599><c> for</c><00:00:12.759><c> boring</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
though I have Parkinson's and for boring
 

00:00:13.200 --> 00:00:16.310 align:start position:0%
though I have Parkinson's and for boring
logistical<00:00:13.839><c> reasons</c><00:00:14.519><c> 11</c><00:00:14.920><c> Labs</c><00:00:15.240><c> is</c><00:00:15.440><c> easier</c><00:00:16.240><c> I</c>

00:00:16.310 --> 00:00:16.320 align:start position:0%
logistical reasons 11 Labs is easier I
 

00:00:16.320 --> 00:00:17.950 align:start position:0%
logistical reasons 11 Labs is easier I
made<00:00:16.560><c> autog</c><00:00:16.880><c> Gro</c><00:00:17.199><c> after</c><00:00:17.439><c> wrestling</c><00:00:17.840><c> with</c>

00:00:17.950 --> 00:00:17.960 align:start position:0%
made autog Gro after wrestling with
 

00:00:17.960 --> 00:00:20.429 align:start position:0%
made autog Gro after wrestling with
autogen<00:00:18.520><c> to</c><00:00:18.680><c> create</c><00:00:18.920><c> a</c><00:00:19.039><c> team</c><00:00:19.279><c> of</c><00:00:19.480><c> Agents</c><00:00:20.359><c> I</c>

00:00:20.429 --> 00:00:20.439 align:start position:0%
autogen to create a team of Agents I
 

00:00:20.439 --> 00:00:21.990 align:start position:0%
autogen to create a team of Agents I
mean<00:00:20.600><c> how</c><00:00:20.720><c> do</c><00:00:20.880><c> we</c><00:00:21.000><c> know</c><00:00:21.279><c> which</c><00:00:21.439><c> agents</c><00:00:21.800><c> to</c>

00:00:21.990 --> 00:00:22.000 align:start position:0%
mean how do we know which agents to
 

00:00:22.000 --> 00:00:23.790 align:start position:0%
mean how do we know which agents to
create<00:00:22.359><c> if</c><00:00:22.480><c> we</c><00:00:22.600><c> don't</c><00:00:22.800><c> yet</c><00:00:22.960><c> know</c><00:00:23.199><c> what</c><00:00:23.480><c> problem</c>

00:00:23.790 --> 00:00:23.800 align:start position:0%
create if we don't yet know what problem
 

00:00:23.800 --> 00:00:26.589 align:start position:0%
create if we don't yet know what problem
they<00:00:24.000><c> should</c><00:00:24.240><c> solve</c><00:00:25.039><c> Auto</c><00:00:25.480><c> genen</c><00:00:25.760><c> and</c><00:00:25.920><c> crew</c><00:00:26.240><c> AI</c>

00:00:26.589 --> 00:00:26.599 align:start position:0%
they should solve Auto genen and crew AI
 

00:00:26.599 --> 00:00:28.109 align:start position:0%
they should solve Auto genen and crew AI
want<00:00:26.800><c> you</c><00:00:26.880><c> to</c><00:00:27.039><c> put</c><00:00:27.320><c> together</c><00:00:27.560><c> your</c><00:00:27.760><c> team</c>

00:00:28.109 --> 00:00:28.119 align:start position:0%
want you to put together your team
 

00:00:28.119 --> 00:00:29.589 align:start position:0%
want you to put together your team
before<00:00:28.400><c> you</c><00:00:28.519><c> know</c><00:00:28.800><c> what</c><00:00:28.960><c> their</c><00:00:29.160><c> assignment</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
before you know what their assignment
 

00:00:29.599 --> 00:00:30.550 align:start position:0%
before you know what their assignment
will<00:00:29.759><c> be</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
will be
 

00:00:30.560 --> 00:00:32.830 align:start position:0%
will be
but<00:00:30.720><c> that's</c><00:00:30.880><c> not</c><00:00:31.039><c> how</c><00:00:31.199><c> the</c><00:00:31.320><c> real</c><00:00:31.560><c> world</c><00:00:31.920><c> works</c>

00:00:32.830 --> 00:00:32.840 align:start position:0%
but that's not how the real world works
 

00:00:32.840 --> 00:00:34.670 align:start position:0%
but that's not how the real world works
at<00:00:33.040><c> the</c><00:00:33.280><c> office</c><00:00:33.640><c> the</c><00:00:33.800><c> boss</c><00:00:34.160><c> first</c><00:00:34.440><c> comes</c>

00:00:34.670 --> 00:00:34.680 align:start position:0%
at the office the boss first comes
 

00:00:34.680 --> 00:00:37.030 align:start position:0%
at the office the boss first comes
forward<00:00:35.040><c> with</c><00:00:35.200><c> a</c><00:00:35.440><c> problem</c><00:00:35.760><c> to</c><00:00:35.960><c> solve</c><00:00:36.760><c> and</c><00:00:36.879><c> if</c>

00:00:37.030 --> 00:00:37.040 align:start position:0%
forward with a problem to solve and if
 

00:00:37.040 --> 00:00:38.670 align:start position:0%
forward with a problem to solve and if
nobody<00:00:37.360><c> at</c><00:00:37.480><c> the</c><00:00:37.600><c> office</c><00:00:37.960><c> is</c><00:00:38.079><c> qualified</c><00:00:38.559><c> to</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
nobody at the office is qualified to
 

00:00:38.680 --> 00:00:40.990 align:start position:0%
nobody at the office is qualified to
perform<00:00:39.000><c> a</c><00:00:39.160><c> specific</c><00:00:39.640><c> task</c><00:00:39.960><c> a</c><00:00:40.160><c> contractor</c><00:00:40.760><c> or</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
perform a specific task a contractor or
 

00:00:41.000 --> 00:00:43.310 align:start position:0%
perform a specific task a contractor or
additional<00:00:41.399><c> employee</c><00:00:41.879><c> is</c><00:00:42.039><c> added</c><00:00:42.280><c> to</c><00:00:42.399><c> the</c><00:00:42.559><c> team</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
additional employee is added to the team
 

00:00:43.320 --> 00:00:46.389 align:start position:0%
additional employee is added to the team
so<00:00:43.640><c> that</c><00:00:43.920><c> is</c><00:00:44.079><c> what</c><00:00:44.239><c> autog</c><00:00:44.879><c> does</c><00:00:45.520><c> autog</c><00:00:46.199><c> looks</c>

00:00:46.389 --> 00:00:46.399 align:start position:0%
so that is what autog does autog looks
 

00:00:46.399 --> 00:00:48.630 align:start position:0%
so that is what autog does autog looks
at<00:00:46.520><c> the</c><00:00:46.760><c> problem</c><00:00:47.199><c> and</c><00:00:47.719><c> and</c><00:00:47.879><c> then</c><00:00:48.039><c> creates</c><00:00:48.440><c> the</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
at the problem and and then creates the
 

00:00:48.640 --> 00:00:50.709 align:start position:0%
at the problem and and then creates the
specific<00:00:49.079><c> tailor</c><00:00:49.600><c> made</c><00:00:49.879><c> expert</c><00:00:50.280><c> agents</c>

00:00:50.709 --> 00:00:50.719 align:start position:0%
specific tailor made expert agents
 

00:00:50.719 --> 00:00:53.189 align:start position:0%
specific tailor made expert agents
required<00:00:51.120><c> to</c><00:00:51.280><c> solve</c><00:00:51.600><c> it</c><00:00:52.239><c> agents</c><00:00:52.640><c> can</c><00:00:52.800><c> talk</c><00:00:53.000><c> to</c>

00:00:53.189 --> 00:00:53.199 align:start position:0%
required to solve it agents can talk to
 

00:00:53.199 --> 00:00:55.590 align:start position:0%
required to solve it agents can talk to
you<00:00:53.559><c> the</c><00:00:53.680><c> user</c><00:00:54.199><c> they</c><00:00:54.320><c> can</c><00:00:54.480><c> talk</c><00:00:54.640><c> to</c><00:00:54.800><c> each</c><00:00:54.960><c> other</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
you the user they can talk to each other
 

00:00:55.600 --> 00:00:57.270 align:start position:0%
you the user they can talk to each other
they<00:00:55.719><c> can</c><00:00:55.879><c> plan</c><00:00:56.239><c> projects</c><00:00:56.719><c> Benchmark</c>

00:00:57.270 --> 00:00:57.280 align:start position:0%
they can plan projects Benchmark
 

00:00:57.280 --> 00:00:59.750 align:start position:0%
they can plan projects Benchmark
timelines<00:00:57.840><c> write</c><00:00:58.079><c> code</c><00:00:58.440><c> and</c><00:00:58.600><c> more</c><00:00:59.399><c> and</c><00:00:59.559><c> thanks</c>

00:00:59.750 --> 00:00:59.760 align:start position:0%
timelines write code and more and thanks
 

00:00:59.760 --> 00:01:01.670 align:start position:0%
timelines write code and more and thanks
to<00:01:00.039><c> our</c><00:01:00.120><c> friends</c><00:01:00.280><c> at</c><00:01:00.440><c> grock</c><00:01:00.920><c> they</c><00:01:01.039><c> can</c><00:01:01.199><c> do</c><00:01:01.359><c> it</c>

00:01:01.670 --> 00:01:01.680 align:start position:0%
to our friends at grock they can do it
 

00:01:01.680 --> 00:01:05.149 align:start position:0%
to our friends at grock they can do it
fast<00:01:02.480><c> really</c><00:01:03.000><c> fast</c><00:01:04.000><c> here's</c><00:01:04.280><c> how</c><00:01:04.799><c> you'll</c><00:01:05.000><c> need</c>

00:01:05.149 --> 00:01:05.159 align:start position:0%
fast really fast here's how you'll need
 

00:01:05.159 --> 00:01:08.590 align:start position:0%
fast really fast here's how you'll need
an<00:01:05.320><c> API</c><00:01:05.799><c> key</c><00:01:06.000><c> from</c><00:01:06.200><c> gro.com</c><00:01:07.280><c> go</c><00:01:08.280><c> to</c><00:01:08.400><c> their</c>

00:01:08.590 --> 00:01:08.600 align:start position:0%
an API key from gro.com go to their
 

00:01:08.600 --> 00:01:11.350 align:start position:0%
an API key from gro.com go to their
Cloud<00:01:08.880><c> playground</c><00:01:09.280><c> and</c><00:01:09.439><c> click</c><00:01:09.600><c> on</c><00:01:09.840><c> API</c><00:01:10.360><c> Keys</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
Cloud playground and click on API Keys
 

00:01:11.360 --> 00:01:13.429 align:start position:0%
Cloud playground and click on API Keys
click<00:01:11.600><c> the</c><00:01:11.799><c> create</c><00:01:12.119><c> API</c><00:01:12.600><c> key</c><00:01:12.840><c> button</c><00:01:13.080><c> and</c><00:01:13.240><c> name</c>

00:01:13.429 --> 00:01:13.439 align:start position:0%
click the create API key button and name
 

00:01:13.439 --> 00:01:15.109 align:start position:0%
click the create API key button and name
your<00:01:13.640><c> key</c><00:01:13.920><c> whatever</c><00:01:14.200><c> you</c>

00:01:15.109 --> 00:01:15.119 align:start position:0%
your key whatever you
 

00:01:15.119 --> 00:01:19.469 align:start position:0%
your key whatever you
want<00:01:16.119><c> I'll</c><00:01:16.320><c> call</c><00:01:16.520><c> ours</c><00:01:16.840><c> demo</c><00:01:17.200><c> and</c><00:01:17.360><c> hit</c>

00:01:19.469 --> 00:01:19.479 align:start position:0%
want I'll call ours demo and hit
 

00:01:19.479 --> 00:01:21.870 align:start position:0%
want I'll call ours demo and hit
submit<00:01:20.479><c> then</c><00:01:20.640><c> click</c><00:01:20.880><c> the</c><00:01:21.079><c> copy</c><00:01:21.360><c> button</c><00:01:21.640><c> save</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
submit then click the copy button save
 

00:01:21.880 --> 00:01:24.069 align:start position:0%
submit then click the copy button save
your<00:01:22.079><c> key</c><00:01:22.280><c> and</c><00:01:22.400><c> we're</c><00:01:22.560><c> done</c><00:01:22.840><c> here</c><00:01:23.600><c> let's</c><00:01:23.840><c> head</c>

00:01:24.069 --> 00:01:24.079 align:start position:0%
your key and we're done here let's head
 

00:01:24.079 --> 00:01:26.749 align:start position:0%
your key and we're done here let's head
back<00:01:24.200><c> to</c><00:01:24.320><c> the</c><00:01:24.479><c> demo</c><00:01:24.880><c> site</c><00:01:25.759><c> paste</c><00:01:26.000><c> your</c><00:01:26.159><c> API</c><00:01:26.560><c> key</c>

00:01:26.749 --> 00:01:26.759 align:start position:0%
back to the demo site paste your API key
 

00:01:26.759 --> 00:01:29.469 align:start position:0%
back to the demo site paste your API key
in<00:01:26.960><c> up</c><00:01:27.159><c> here</c><00:01:27.720><c> we</c><00:01:27.880><c> don't</c><00:01:28.200><c> validate</c><00:01:28.720><c> keys</c><00:01:29.320><c> so</c>

00:01:29.469 --> 00:01:29.479 align:start position:0%
in up here we don't validate keys so
 

00:01:29.479 --> 00:01:31.670 align:start position:0%
in up here we don't validate keys so
you'll<00:01:29.680><c> get</c><00:01:30.040><c> this</c><00:01:30.200><c> message</c><00:01:30.560><c> no</c><00:01:30.720><c> matter</c><00:01:31.079><c> what</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
you'll get this message no matter what
 

00:01:31.680 --> 00:01:34.270 align:start position:0%
you'll get this message no matter what
but<00:01:31.799><c> you</c><00:01:31.920><c> need</c><00:01:32.079><c> a</c><00:01:32.240><c> working</c><00:01:32.640><c> key</c><00:01:32.960><c> so</c><00:01:33.159><c> be</c><00:01:33.360><c> careful</c>

00:01:34.270 --> 00:01:34.280 align:start position:0%
but you need a working key so be careful
 

00:01:34.280 --> 00:01:35.990 align:start position:0%
but you need a working key so be careful
then<00:01:34.479><c> select</c><00:01:34.840><c> your</c><00:01:35.000><c> model</c><00:01:35.320><c> from</c><00:01:35.479><c> the</c><00:01:35.640><c> drop-</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
then select your model from the drop-
 

00:01:36.000 --> 00:01:38.270 align:start position:0%
then select your model from the drop-
down<00:01:36.439><c> you</c><00:01:36.560><c> can</c><00:01:36.720><c> switch</c><00:01:37.119><c> back</c><00:01:37.360><c> and</c><00:01:37.560><c> forth</c><00:01:38.119><c> from</c>

00:01:38.270 --> 00:01:38.280 align:start position:0%
down you can switch back and forth from
 

00:01:38.280 --> 00:01:40.870 align:start position:0%
down you can switch back and forth from
one<00:01:38.479><c> model</c><00:01:38.840><c> to</c><00:01:39.000><c> the</c><00:01:39.159><c> next</c><00:01:39.439><c> at</c><00:01:39.600><c> any</c><00:01:39.880><c> time</c><00:01:40.439><c> then</c>

00:01:40.870 --> 00:01:40.880 align:start position:0%
one model to the next at any time then
 

00:01:40.880 --> 00:01:43.190 align:start position:0%
one model to the next at any time then
dial<00:01:41.240><c> in</c><00:01:41.399><c> a</c><00:01:41.560><c> temperature</c><00:01:42.439><c> the</c><00:01:42.560><c> farther</c><00:01:42.880><c> to</c><00:01:43.040><c> the</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
dial in a temperature the farther to the
 

00:01:43.200 --> 00:01:44.990 align:start position:0%
dial in a temperature the farther to the
right<00:01:43.439><c> the</c><00:01:43.560><c> more</c><00:01:43.799><c> creative</c><00:01:44.200><c> your</c><00:01:44.360><c> agents</c><00:01:44.759><c> will</c>

00:01:44.990 --> 00:01:45.000 align:start position:0%
right the more creative your agents will
 

00:01:45.000 --> 00:01:47.749 align:start position:0%
right the more creative your agents will
be<00:01:45.920><c> to</c><00:01:46.079><c> the</c><00:01:46.280><c> left</c><00:01:46.680><c> they</c><00:01:46.840><c> become</c><00:01:47.200><c> more</c>

00:01:47.749 --> 00:01:47.759 align:start position:0%
be to the left they become more
 

00:01:47.759 --> 00:01:50.429 align:start position:0%
be to the left they become more
fact-based<00:01:48.360><c> and</c><00:01:48.799><c> pragmatic</c><00:01:49.479><c> and</c><00:01:49.600><c> that's</c><00:01:49.840><c> it</c>

00:01:50.429 --> 00:01:50.439 align:start position:0%
fact-based and pragmatic and that's it
 

00:01:50.439 --> 00:01:51.950 align:start position:0%
fact-based and pragmatic and that's it
we're<00:01:50.600><c> ready</c><00:01:50.799><c> to</c><00:01:50.960><c> get</c><00:01:51.119><c> started</c><00:01:51.560><c> I'm</c><00:01:51.680><c> going</c><00:01:51.799><c> to</c>

00:01:51.950 --> 00:01:51.960 align:start position:0%
we're ready to get started I'm going to
 

00:01:51.960 --> 00:01:54.429 align:start position:0%
we're ready to get started I'm going to
ask<00:01:52.159><c> autog</c><00:01:52.560><c> Gro</c><00:01:52.840><c> to</c><00:01:53.000><c> create</c><00:01:53.240><c> an</c><00:01:53.439><c> online</c><00:01:53.960><c> random</c>

00:01:54.429 --> 00:01:54.439 align:start position:0%
ask autog Gro to create an online random
 

00:01:54.439 --> 00:01:57.950 align:start position:0%
ask autog Gro to create an online random
algorithm

00:01:57.950 --> 00:01:57.960 align:start position:0%
 
 

00:01:57.960 --> 00:02:00.389 align:start position:0%
 
generator<00:01:58.960><c> and</c><00:01:59.159><c> there</c><00:01:59.320><c> we</c><00:01:59.479><c> go</c><00:02:00.000><c> a</c><00:02:00.119><c> couple</c><00:02:00.280><c> of</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
generator and there we go a couple of
 

00:02:00.399 --> 00:02:02.310 align:start position:0%
generator and there we go a couple of
things<00:02:00.640><c> happened</c><00:02:01.039><c> there</c><00:02:01.560><c> first</c><00:02:01.799><c> our</c><00:02:02.000><c> prompt</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
things happened there first our prompt
 

00:02:02.320 --> 00:02:05.429 align:start position:0%
things happened there first our prompt
got<00:02:02.479><c> re-engineered</c><00:02:03.200><c> and</c><00:02:03.360><c> now</c><00:02:03.960><c> reads</c><00:02:04.960><c> design</c><00:02:05.280><c> a</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
got re-engineered and now reads design a
 

00:02:05.439 --> 00:02:07.310 align:start position:0%
got re-engineered and now reads design a
web-based<00:02:06.000><c> tool</c><00:02:06.320><c> that</c><00:02:06.439><c> generates</c><00:02:06.960><c> random</c>

00:02:07.310 --> 00:02:07.320 align:start position:0%
web-based tool that generates random
 

00:02:07.320 --> 00:02:09.190 align:start position:0%
web-based tool that generates random
algorithms<00:02:07.960><c> for</c><00:02:08.119><c> a</c><00:02:08.319><c> specified</c><00:02:08.879><c> problem</c>

00:02:09.190 --> 00:02:09.200 align:start position:0%
algorithms for a specified problem
 

00:02:09.200 --> 00:02:11.910 align:start position:0%
algorithms for a specified problem
domain<00:02:09.520><c> or</c><00:02:09.720><c> programming</c><00:02:10.560><c> language</c><00:02:11.560><c> allowing</c>

00:02:11.910 --> 00:02:11.920 align:start position:0%
domain or programming language allowing
 

00:02:11.920 --> 00:02:13.910 align:start position:0%
domain or programming language allowing
users<00:02:12.239><c> to</c><00:02:12.440><c> input</c><00:02:12.800><c> parameters</c><00:02:13.239><c> and</c><00:02:13.440><c> receive</c><00:02:13.720><c> a</c>

00:02:13.910 --> 00:02:13.920 align:start position:0%
users to input parameters and receive a
 

00:02:13.920 --> 00:02:16.229 align:start position:0%
users to input parameters and receive a
unique<00:02:14.440><c> executable</c><00:02:15.080><c> algorithm</c><00:02:15.560><c> in</c><00:02:15.879><c> blah</c><00:02:16.080><c> blah</c>

00:02:16.229 --> 00:02:16.239 align:start position:0%
unique executable algorithm in blah blah
 

00:02:16.239 --> 00:02:18.470 align:start position:0%
unique executable algorithm in blah blah
blah<00:02:16.760><c> this</c><00:02:16.920><c> new</c><00:02:17.160><c> prompt</c><00:02:17.560><c> provides</c><00:02:17.959><c> the</c><00:02:18.160><c> actual</c>

00:02:18.470 --> 00:02:18.480 align:start position:0%
blah this new prompt provides the actual
 

00:02:18.480 --> 00:02:20.790 align:start position:0%
blah this new prompt provides the actual
instructions<00:02:19.040><c> our</c><00:02:19.200><c> new</c><00:02:19.400><c> agents</c><00:02:19.800><c> will</c><00:02:20.000><c> follow</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
instructions our new agents will follow
 

00:02:20.800 --> 00:02:22.309 align:start position:0%
instructions our new agents will follow
and<00:02:20.920><c> you'll</c><00:02:21.080><c> see</c><00:02:21.280><c> our</c><00:02:21.440><c> team</c><00:02:21.680><c> of</c><00:02:21.879><c> expert</c>

00:02:22.309 --> 00:02:22.319 align:start position:0%
and you'll see our team of expert
 

00:02:22.319 --> 00:02:24.350 align:start position:0%
and you'll see our team of expert
appeared<00:02:22.680><c> here</c><00:02:22.840><c> on</c><00:02:23.000><c> the</c><00:02:23.239><c> left</c><00:02:23.879><c> if</c><00:02:24.000><c> you</c><00:02:24.080><c> wanted</c>

00:02:24.350 --> 00:02:24.360 align:start position:0%
appeared here on the left if you wanted
 

00:02:24.360 --> 00:02:26.309 align:start position:0%
appeared here on the left if you wanted
to<00:02:24.560><c> you</c><00:02:24.680><c> could</c><00:02:24.840><c> stop</c><00:02:25.239><c> right</c><00:02:25.480><c> here</c><00:02:25.879><c> open</c><00:02:26.120><c> up</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
to you could stop right here open up
 

00:02:26.319 --> 00:02:28.470 align:start position:0%
to you could stop right here open up
Auto<00:02:26.720><c> genen</c><00:02:27.319><c> and</c><00:02:27.480><c> hit</c><00:02:27.640><c> the</c><00:02:27.760><c> ground</c><00:02:28.040><c> running</c><00:02:28.319><c> on</c>

00:02:28.470 --> 00:02:28.480 align:start position:0%
Auto genen and hit the ground running on
 

00:02:28.480 --> 00:02:30.949 align:start position:0%
Auto genen and hit the ground running on
your<00:02:28.640><c> new</c><00:02:28.800><c> multi-agent</c><00:02:29.519><c> project</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
your new multi-agent project
 

00:02:30.959 --> 00:02:32.990 align:start position:0%
your new multi-agent project
here<00:02:31.040><c> are</c><00:02:31.239><c> our</c><00:02:31.440><c> agents</c><00:02:32.360><c> they</c><00:02:32.480><c> are</c><00:02:32.680><c> fully</c>

00:02:32.990 --> 00:02:33.000 align:start position:0%
here are our agents they are fully
 

00:02:33.000 --> 00:02:35.150 align:start position:0%
here are our agents they are fully
configured<00:02:33.560><c> and</c><00:02:33.760><c> ready</c><00:02:33.959><c> to</c><00:02:34.120><c> be</c><00:02:34.280><c> imported</c><00:02:34.760><c> into</c>

00:02:35.150 --> 00:02:35.160 align:start position:0%
configured and ready to be imported into
 

00:02:35.160 --> 00:02:37.550 align:start position:0%
configured and ready to be imported into
autogen<00:02:36.160><c> I'll</c><00:02:36.360><c> link</c><00:02:36.560><c> to</c><00:02:36.720><c> one</c><00:02:36.840><c> of</c><00:02:37.000><c> my</c><00:02:37.160><c> earlier</c>

00:02:37.550 --> 00:02:37.560 align:start position:0%
autogen I'll link to one of my earlier
 

00:02:37.560 --> 00:02:40.110 align:start position:0%
autogen I'll link to one of my earlier
videos<00:02:38.040><c> below</c><00:02:38.480><c> to</c><00:02:38.640><c> show</c><00:02:38.879><c> you</c><00:02:39.040><c> how</c><00:02:39.159><c> to</c><00:02:39.360><c> do</c><00:02:39.599><c> that</c>

00:02:40.110 --> 00:02:40.120 align:start position:0%
videos below to show you how to do that
 

00:02:40.120 --> 00:02:42.670 align:start position:0%
videos below to show you how to do that
and<00:02:40.440><c> importing</c><00:02:41.040><c> workflow</c><00:02:41.640><c> files</c><00:02:42.120><c> works</c><00:02:42.519><c> the</c>

00:02:42.670 --> 00:02:42.680 align:start position:0%
and importing workflow files works the
 

00:02:42.680 --> 00:02:43.790 align:start position:0%
and importing workflow files works the
same

00:02:43.790 --> 00:02:43.800 align:start position:0%
same
 

00:02:43.800 --> 00:02:47.350 align:start position:0%
same
way<00:02:44.800><c> we</c><00:02:45.000><c> also</c><00:02:45.319><c> create</c><00:02:45.720><c> downloadable</c><00:02:46.480><c> crew</c><00:02:46.879><c> AI</c>

00:02:47.350 --> 00:02:47.360 align:start position:0%
way we also create downloadable crew AI
 

00:02:47.360 --> 00:02:49.949 align:start position:0%
way we also create downloadable crew AI
files<00:02:47.879><c> which</c><00:02:48.000><c> are</c><00:02:48.200><c> more</c><00:02:48.800><c> fundamental</c><00:02:49.800><c> but</c>

00:02:49.949 --> 00:02:49.959 align:start position:0%
files which are more fundamental but
 

00:02:49.959 --> 00:02:51.550 align:start position:0%
files which are more fundamental but
should<00:02:50.120><c> be</c><00:02:50.319><c> enough</c><00:02:50.560><c> to</c><00:02:50.720><c> get</c><00:02:50.840><c> you</c><00:02:51.000><c> started</c><00:02:51.400><c> with</c>

00:02:51.550 --> 00:02:51.560 align:start position:0%
should be enough to get you started with
 

00:02:51.560 --> 00:02:53.670 align:start position:0%
should be enough to get you started with
your<00:02:51.720><c> own</c><00:02:52.000><c> team</c><00:02:52.239><c> of</c><00:02:52.440><c> AI</c><00:02:52.800><c> agents</c><00:02:53.200><c> on</c><00:02:53.480><c> that</c>

00:02:53.670 --> 00:02:53.680 align:start position:0%
your own team of AI agents on that
 

00:02:53.680 --> 00:02:56.149 align:start position:0%
your own team of AI agents on that
platform<00:02:54.360><c> as</c><00:02:54.519><c> well</c><00:02:55.360><c> now</c><00:02:55.560><c> if</c><00:02:55.640><c> you</c><00:02:55.760><c> don't</c><00:02:55.920><c> mind</c>

00:02:56.149 --> 00:02:56.159 align:start position:0%
platform as well now if you don't mind
 

00:02:56.159 --> 00:02:57.710 align:start position:0%
platform as well now if you don't mind
hanging<00:02:56.400><c> out</c><00:02:56.560><c> for</c><00:02:56.680><c> a</c><00:02:56.760><c> few</c><00:02:56.959><c> more</c><00:02:57.120><c> minutes</c><00:02:57.519><c> we've</c>

00:02:57.710 --> 00:02:57.720 align:start position:0%
hanging out for a few more minutes we've
 

00:02:57.720 --> 00:02:59.390 align:start position:0%
hanging out for a few more minutes we've
got<00:02:57.840><c> a</c><00:02:57.959><c> lot</c><00:02:58.080><c> of</c><00:02:58.239><c> other</c><00:02:58.440><c> things</c><00:02:58.680><c> to</c><00:02:58.800><c> show</c><00:02:59.080><c> you</c>

00:02:59.390 --> 00:02:59.400 align:start position:0%
got a lot of other things to show you
 

00:02:59.400 --> 00:03:01.430 align:start position:0%
got a lot of other things to show you
autog<00:02:59.879><c> Ro</c><00:03:00.080><c> allows</c><00:03:00.319><c> you</c><00:03:00.400><c> to</c><00:03:00.560><c> test</c><00:03:00.879><c> drive</c><00:03:01.239><c> these</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
autog Ro allows you to test drive these
 

00:03:01.440 --> 00:03:03.350 align:start position:0%
autog Ro allows you to test drive these
agents<00:03:01.879><c> by</c><00:03:02.040><c> chatting</c><00:03:02.440><c> with</c><00:03:02.599><c> them</c><00:03:02.879><c> having</c><00:03:03.120><c> them</c>

00:03:03.350 --> 00:03:03.360 align:start position:0%
agents by chatting with them having them
 

00:03:03.360 --> 00:03:05.110 align:start position:0%
agents by chatting with them having them
talk<00:03:03.599><c> to</c><00:03:03.760><c> one</c><00:03:03.959><c> another</c><00:03:04.319><c> with</c><00:03:04.480><c> just</c><00:03:04.680><c> the</c><00:03:04.879><c> click</c>

00:03:05.110 --> 00:03:05.120 align:start position:0%
talk to one another with just the click
 

00:03:05.120 --> 00:03:07.630 align:start position:0%
talk to one another with just the click
of<00:03:05.239><c> a</c><00:03:05.680><c> button</c><00:03:06.680><c> I've</c><00:03:06.879><c> asked</c><00:03:07.120><c> our</c><00:03:07.319><c> project</c>

00:03:07.630 --> 00:03:07.640 align:start position:0%
of a button I've asked our project
 

00:03:07.640 --> 00:03:09.789 align:start position:0%
of a button I've asked our project
manager<00:03:08.080><c> to</c><00:03:08.239><c> chime</c><00:03:08.519><c> in</c><00:03:09.159><c> they've</c><00:03:09.360><c> come</c><00:03:09.519><c> up</c><00:03:09.680><c> with</c>

00:03:09.789 --> 00:03:09.799 align:start position:0%
manager to chime in they've come up with
 

00:03:09.799 --> 00:03:12.550 align:start position:0%
manager to chime in they've come up with
a<00:03:09.920><c> pretty</c><00:03:10.239><c> comprehensive</c><00:03:11.200><c> reply</c><00:03:12.200><c> project</c>

00:03:12.550 --> 00:03:12.560 align:start position:0%
a pretty comprehensive reply project
 

00:03:12.560 --> 00:03:15.949 align:start position:0%
a pretty comprehensive reply project
plan<00:03:13.120><c> scope</c><00:03:13.720><c> and</c><00:03:13.840><c> even</c><00:03:14.000><c> a</c><00:03:14.159><c> very</c><00:03:14.480><c> pessimistic</c>

00:03:15.949 --> 00:03:15.959 align:start position:0%
plan scope and even a very pessimistic
 

00:03:15.959 --> 00:03:18.789 align:start position:0%
plan scope and even a very pessimistic
timeline<00:03:16.959><c> by</c><00:03:17.080><c> the</c><00:03:17.200><c> way</c><00:03:17.480><c> Auto</c><00:03:17.840><c> Gro</c><00:03:18.200><c> is</c><00:03:18.400><c> faster</c>

00:03:18.789 --> 00:03:18.799 align:start position:0%
timeline by the way Auto Gro is faster
 

00:03:18.799 --> 00:03:21.030 align:start position:0%
timeline by the way Auto Gro is faster
than<00:03:18.920><c> you</c><00:03:19.000><c> see</c><00:03:19.200><c> it</c><00:03:19.400><c> here</c><00:03:20.239><c> I've</c><00:03:20.400><c> had</c><00:03:20.560><c> to</c><00:03:20.680><c> program</c>

00:03:21.030 --> 00:03:21.040 align:start position:0%
than you see it here I've had to program
 

00:03:21.040 --> 00:03:23.149 align:start position:0%
than you see it here I've had to program
in<00:03:21.159><c> a</c><00:03:21.319><c> two-c</c><00:03:21.840><c> delay</c><00:03:22.200><c> with</c><00:03:22.360><c> each</c><00:03:22.599><c> request</c><00:03:22.959><c> to</c>

00:03:23.149 --> 00:03:23.159 align:start position:0%
in a two-c delay with each request to
 

00:03:23.159 --> 00:03:26.149 align:start position:0%
in a two-c delay with each request to
avoid<00:03:23.480><c> violating</c><00:03:24.000><c> grock's</c><00:03:24.920><c> policies</c><00:03:25.920><c> in</c>

00:03:26.149 --> 00:03:26.159 align:start position:0%
avoid violating grock's policies in
 

00:03:26.159 --> 00:03:28.470 align:start position:0%
avoid violating grock's policies in
production<00:03:26.680><c> this</c><00:03:26.840><c> thing</c><00:03:27.000><c> will</c><00:03:27.239><c> really</c>

00:03:28.470 --> 00:03:28.480 align:start position:0%
production this thing will really
 

00:03:28.480 --> 00:03:30.789 align:start position:0%
production this thing will really
fly<00:03:29.480><c> and</c><00:03:29.599><c> then</c><00:03:29.799><c> there</c><00:03:29.879><c> we</c><00:03:30.040><c> go</c><00:03:30.239><c> our</c><00:03:30.480><c> project</c>

00:03:30.789 --> 00:03:30.799 align:start position:0%
fly and then there we go our project
 

00:03:30.799 --> 00:03:32.670 align:start position:0%
fly and then there we go our project
manager<00:03:31.159><c> has</c><00:03:31.360><c> revised</c><00:03:31.760><c> his</c><00:03:31.959><c> projections</c><00:03:32.480><c> from</c>

00:03:32.670 --> 00:03:32.680 align:start position:0%
manager has revised his projections from
 

00:03:32.680 --> 00:03:35.990 align:start position:0%
manager has revised his projections from
weeks<00:03:33.000><c> down</c><00:03:33.200><c> to</c><00:03:33.920><c> days</c><00:03:34.920><c> well</c><00:03:35.080><c> from</c><00:03:35.280><c> 16</c><00:03:35.680><c> weeks</c>

00:03:35.990 --> 00:03:36.000 align:start position:0%
weeks down to days well from 16 weeks
 

00:03:36.000 --> 00:03:38.630 align:start position:0%
weeks down to days well from 16 weeks
down<00:03:36.159><c> to</c><00:03:36.319><c> four</c><00:03:36.760><c> so</c><00:03:37.080><c> yeah</c><00:03:37.400><c> that's</c><00:03:37.760><c> progress</c><00:03:38.519><c> we</c>

00:03:38.630 --> 00:03:38.640 align:start position:0%
down to four so yeah that's progress we
 

00:03:38.640 --> 00:03:41.470 align:start position:0%
down to four so yeah that's progress we
have<00:03:38.799><c> the</c><00:03:38.920><c> ability</c><00:03:39.319><c> to</c><00:03:39.480><c> load</c><00:03:40.000><c> small</c><00:03:40.439><c> CSV</c><00:03:41.120><c> files</c>

00:03:41.470 --> 00:03:41.480 align:start position:0%
have the ability to load small CSV files
 

00:03:41.480 --> 00:03:44.110 align:start position:0%
have the ability to load small CSV files
for<00:03:41.680><c> the</c><00:03:41.799><c> agents</c><00:03:42.159><c> to</c><00:03:42.360><c> discuss</c><00:03:42.799><c> too</c><00:03:43.799><c> if</c><00:03:43.920><c> you're</c>

00:03:44.110 --> 00:03:44.120 align:start position:0%
for the agents to discuss too if you're
 

00:03:44.120 --> 00:03:45.910 align:start position:0%
for the agents to discuss too if you're
familiar<00:03:44.519><c> with</c><00:03:44.720><c> python</c><00:03:45.159><c> you</c><00:03:45.239><c> may</c><00:03:45.400><c> know</c><00:03:45.599><c> how</c>

00:03:45.910 --> 00:03:45.920 align:start position:0%
familiar with python you may know how
 

00:03:45.920 --> 00:03:47.589 align:start position:0%
familiar with python you may know how
this<00:03:46.120><c> week's</c><00:03:46.480><c> features</c><00:03:47.159><c> sometimes</c><00:03:47.400><c> don't</c>

00:03:47.589 --> 00:03:47.599 align:start position:0%
this week's features sometimes don't
 

00:03:47.599 --> 00:03:50.229 align:start position:0%
this week's features sometimes don't
work<00:03:47.879><c> as</c><00:03:48.000><c> well</c><00:03:48.280><c> next</c><00:03:48.560><c> week</c><00:03:49.560><c> anyway</c><00:03:49.959><c> this</c><00:03:50.080><c> is</c>

00:03:50.229 --> 00:03:50.239 align:start position:0%
work as well next week anyway this is
 

00:03:50.239 --> 00:03:52.350 align:start position:0%
work as well next week anyway this is
one<00:03:50.400><c> of</c><00:03:50.599><c> those</c><00:03:50.920><c> it's</c><00:03:51.120><c> hit</c><00:03:51.280><c> and</c><00:03:51.480><c> miss</c><00:03:51.959><c> play</c><00:03:52.200><c> with</c>

00:03:52.350 --> 00:03:52.360 align:start position:0%
one of those it's hit and miss play with
 

00:03:52.360 --> 00:03:54.589 align:start position:0%
one of those it's hit and miss play with
it<00:03:52.519><c> but</c><00:03:52.680><c> don't</c><00:03:52.920><c> count</c><00:03:53.200><c> on</c><00:03:53.360><c> it</c><00:03:54.000><c> let's</c><00:03:54.239><c> get</c><00:03:54.360><c> some</c>

00:03:54.589 --> 00:03:54.599 align:start position:0%
it but don't count on it let's get some
 

00:03:54.599 --> 00:03:56.949 align:start position:0%
it but don't count on it let's get some
feedback<00:03:55.079><c> from</c><00:03:55.360><c> other</c><00:03:55.599><c> members</c><00:03:55.920><c> of</c><00:03:56.079><c> the</c><00:03:56.239><c> team</c>

00:03:56.949 --> 00:03:56.959 align:start position:0%
feedback from other members of the team
 

00:03:56.959 --> 00:03:58.949 align:start position:0%
feedback from other members of the team
there<00:03:57.079><c> are</c><00:03:57.360><c> three</c><00:03:57.680><c> display</c><00:03:58.079><c> modes</c><00:03:58.480><c> we</c><00:03:58.599><c> can</c><00:03:58.760><c> use</c>

00:03:58.949 --> 00:03:58.959 align:start position:0%
there are three display modes we can use
 

00:03:58.959 --> 00:04:01.149 align:start position:0%
there are three display modes we can use
to<00:03:59.120><c> view</c><00:03:59.319><c> the</c><00:03:59.439><c> information</c><00:04:00.200><c> generated</c><00:04:00.760><c> by</c><00:04:00.879><c> our</c>

00:04:01.149 --> 00:04:01.159 align:start position:0%
to view the information generated by our
 

00:04:01.159 --> 00:04:03.470 align:start position:0%
to view the information generated by our
agents<00:04:02.159><c> the</c><00:04:02.280><c> most</c><00:04:02.519><c> recent</c><00:04:02.920><c> comment</c><00:04:03.360><c> the</c>

00:04:03.470 --> 00:04:03.480 align:start position:0%
agents the most recent comment the
 

00:04:03.480 --> 00:04:05.589 align:start position:0%
agents the most recent comment the
Whiteboard<00:04:04.200><c> and</c><00:04:04.319><c> the</c><00:04:04.480><c> formatted</c><00:04:05.079><c> display</c><00:04:05.480><c> of</c>

00:04:05.589 --> 00:04:05.599 align:start position:0%
Whiteboard and the formatted display of
 

00:04:05.599 --> 00:04:08.429 align:start position:0%
Whiteboard and the formatted display of
the<00:04:05.760><c> entire</c><00:04:06.239><c> discussion</c><00:04:06.959><c> llms</c><00:04:07.799><c> tend</c><00:04:08.040><c> to</c><00:04:08.239><c> pay</c>

00:04:08.429 --> 00:04:08.439 align:start position:0%
the entire discussion llms tend to pay
 

00:04:08.439 --> 00:04:10.149 align:start position:0%
the entire discussion llms tend to pay
the<00:04:08.560><c> most</c><00:04:08.799><c> attention</c><00:04:09.200><c> to</c><00:04:09.360><c> the</c><00:04:09.560><c> first</c><00:04:09.840><c> and</c><00:04:10.000><c> the</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
the most attention to the first and the
 

00:04:10.159 --> 00:04:11.789 align:start position:0%
the most attention to the first and the
last<00:04:10.400><c> bits</c><00:04:10.640><c> of</c><00:04:10.799><c> information</c><00:04:11.360><c> they've</c><00:04:11.560><c> been</c>

00:04:11.789 --> 00:04:11.799 align:start position:0%
last bits of information they've been
 

00:04:11.799 --> 00:04:14.789 align:start position:0%
last bits of information they've been
given<00:04:12.519><c> that</c><00:04:12.680><c> works</c><00:04:12.959><c> out</c><00:04:13.280><c> well</c><00:04:13.560><c> for</c><00:04:13.840><c> us</c><00:04:14.680><c> it</c>

00:04:14.789 --> 00:04:14.799 align:start position:0%
given that works out well for us it
 

00:04:14.799 --> 00:04:16.789 align:start position:0%
given that works out well for us it
means<00:04:15.079><c> that</c><00:04:15.200><c> our</c><00:04:15.439><c> goal</c><00:04:15.959><c> and</c><00:04:16.120><c> the</c><00:04:16.239><c> most</c><00:04:16.440><c> recent</c>

00:04:16.789 --> 00:04:16.799 align:start position:0%
means that our goal and the most recent
 

00:04:16.799 --> 00:04:18.430 align:start position:0%
means that our goal and the most recent
discussion<00:04:17.239><c> will</c><00:04:17.440><c> get</c><00:04:17.600><c> the</c><00:04:17.720><c> most</c><00:04:18.000><c> attention</c>

00:04:18.430 --> 00:04:18.440 align:start position:0%
discussion will get the most attention
 

00:04:18.440 --> 00:04:20.629 align:start position:0%
discussion will get the most attention
and<00:04:18.759><c> consideration</c><00:04:19.759><c> that's</c><00:04:19.919><c> a</c><00:04:20.079><c> lot</c><00:04:20.320><c> like</c><00:04:20.519><c> the</c>

00:04:20.629 --> 00:04:20.639 align:start position:0%
and consideration that's a lot like the
 

00:04:20.639 --> 00:04:23.150 align:start position:0%
and consideration that's a lot like the
way<00:04:20.880><c> human</c><00:04:21.239><c> conversations</c><00:04:22.000><c> work</c><00:04:22.280><c> too</c><00:04:22.919><c> here's</c>

00:04:23.150 --> 00:04:23.160 align:start position:0%
way human conversations work too here's
 

00:04:23.160 --> 00:04:25.670 align:start position:0%
way human conversations work too here's
our<00:04:23.400><c> programmer</c><00:04:24.120><c> feedback</c><00:04:24.960><c> any</c><00:04:25.160><c> code</c><00:04:25.479><c> they've</c>

00:04:25.670 --> 00:04:25.680 align:start position:0%
our programmer feedback any code they've
 

00:04:25.680 --> 00:04:27.790 align:start position:0%
our programmer feedback any code they've
written<00:04:25.960><c> is</c><00:04:26.160><c> copied</c><00:04:26.520><c> to</c><00:04:26.639><c> the</c><00:04:26.800><c> whiteboard</c><00:04:27.680><c> in</c>

00:04:27.790 --> 00:04:27.800 align:start position:0%
written is copied to the whiteboard in
 

00:04:27.800 --> 00:04:29.870 align:start position:0%
written is copied to the whiteboard in
the<00:04:27.960><c> future</c><00:04:28.400><c> I</c><00:04:28.520><c> hope</c><00:04:28.720><c> to</c><00:04:28.880><c> give</c><00:04:29.000><c> our</c><00:04:29.199><c> whiteboard</c>

00:04:29.870 --> 00:04:29.880 align:start position:0%
the future I hope to give our whiteboard
 

00:04:29.880 --> 00:04:32.790 align:start position:0%
the future I hope to give our whiteboard
the<00:04:30.000><c> ability</c><00:04:30.400><c> to</c><00:04:30.759><c> execute</c><00:04:31.320><c> and</c><00:04:31.560><c> validate</c><00:04:32.080><c> code</c>

00:04:32.790 --> 00:04:32.800 align:start position:0%
the ability to execute and validate code
 

00:04:32.800 --> 00:04:35.310 align:start position:0%
the ability to execute and validate code
lastly<00:04:33.639><c> here</c><00:04:33.759><c> in</c><00:04:33.919><c> our</c><00:04:34.160><c> discussion</c><00:04:34.680><c> history</c>

00:04:35.310 --> 00:04:35.320 align:start position:0%
lastly here in our discussion history
 

00:04:35.320 --> 00:04:37.710 align:start position:0%
lastly here in our discussion history
you'll<00:04:35.600><c> find</c><00:04:36.000><c> all</c><00:04:36.320><c> the</c><00:04:36.479><c> text</c><00:04:36.880><c> neatly</c><00:04:37.240><c> form</c>

00:04:37.710 --> 00:04:37.720 align:start position:0%
you'll find all the text neatly form
 

00:04:37.720 --> 00:04:40.790 align:start position:0%
you'll find all the text neatly form
added<00:04:38.520><c> the</c><00:04:38.680><c> code</c><00:04:38.919><c> is</c><00:04:39.360><c> colorcoded</c><00:04:40.360><c> and</c><00:04:40.479><c> it</c><00:04:40.600><c> will</c>

00:04:40.790 --> 00:04:40.800 align:start position:0%
added the code is colorcoded and it will
 

00:04:40.800 --> 00:04:43.790 align:start position:0%
added the code is colorcoded and it will
even<00:04:41.039><c> draw</c><00:04:41.440><c> tables</c><00:04:41.880><c> for</c><00:04:42.039><c> the</c><00:04:42.240><c> display</c><00:04:42.639><c> of</c><00:04:42.840><c> data</c>

00:04:43.790 --> 00:04:43.800 align:start position:0%
even draw tables for the display of data
 

00:04:43.800 --> 00:04:45.990 align:start position:0%
even draw tables for the display of data
it<00:04:43.960><c> makes</c><00:04:44.160><c> it</c><00:04:44.360><c> easy</c><00:04:44.639><c> to</c><00:04:44.880><c> generate</c><00:04:45.320><c> reports</c><00:04:45.800><c> on</c>

00:04:45.990 --> 00:04:46.000 align:start position:0%
it makes it easy to generate reports on
 

00:04:46.000 --> 00:04:47.830 align:start position:0%
it makes it easy to generate reports on
any<00:04:46.320><c> project</c><00:04:46.639><c> in</c><00:04:46.840><c> real</c>

00:04:47.830 --> 00:04:47.840 align:start position:0%
any project in real
 

00:04:47.840 --> 00:04:50.550 align:start position:0%
any project in real
time<00:04:48.840><c> next</c><00:04:49.320><c> let</c><00:04:49.440><c> me</c><00:04:49.560><c> show</c><00:04:49.800><c> you</c><00:04:50.039><c> this</c><00:04:50.199><c> week's</c>

00:04:50.550 --> 00:04:50.560 align:start position:0%
time next let me show you this week's
 

00:04:50.560 --> 00:04:51.909 align:start position:0%
time next let me show you this week's
new

00:04:51.909 --> 00:04:51.919 align:start position:0%
new
 

00:04:51.919 --> 00:04:53.909 align:start position:0%
new
feature<00:04:52.919><c> those</c><00:04:53.080><c> of</c><00:04:53.199><c> you</c><00:04:53.360><c> who</c><00:04:53.479><c> have</c><00:04:53.639><c> been</c><00:04:53.800><c> with</c>

00:04:53.909 --> 00:04:53.919 align:start position:0%
feature those of you who have been with
 

00:04:53.919 --> 00:04:56.990 align:start position:0%
feature those of you who have been with
the<00:04:54.120><c> project</c><00:04:54.400><c> for</c><00:04:54.600><c> a</c><00:04:54.720><c> while</c><00:04:54.960><c> will</c><00:04:55.240><c> appreciate</c>

00:04:56.990 --> 00:04:57.000 align:start position:0%
the project for a while will appreciate
 

00:04:57.000 --> 00:05:00.189 align:start position:0%
the project for a while will appreciate
this<00:04:58.000><c> it's</c><00:04:58.199><c> the</c><00:04:58.400><c> ability</c><00:04:58.759><c> to</c><00:04:58.960><c> edit</c><00:04:59.199><c> our</c><00:04:59.720><c> agent</c>

00:05:00.189 --> 00:05:00.199 align:start position:0%
this it's the ability to edit our agent
 

00:05:00.199 --> 00:05:02.430 align:start position:0%
this it's the ability to edit our agent
properties<00:05:01.039><c> just</c><00:05:01.240><c> by</c><00:05:01.440><c> clicking</c><00:05:01.880><c> one</c><00:05:02.000><c> of</c><00:05:02.199><c> these</c>

00:05:02.430 --> 00:05:02.440 align:start position:0%
properties just by clicking one of these
 

00:05:02.440 --> 00:05:04.909 align:start position:0%
properties just by clicking one of these
gear<00:05:02.919><c> icons</c><00:05:03.919><c> this</c><00:05:04.000><c> is</c><00:05:04.160><c> the</c><00:05:04.320><c> description</c><00:05:04.759><c> that</c>

00:05:04.909 --> 00:05:04.919 align:start position:0%
gear icons this is the description that
 

00:05:04.919 --> 00:05:07.230 align:start position:0%
gear icons this is the description that
AI<00:05:05.280><c> created</c><00:05:05.720><c> after</c><00:05:06.039><c> analyzing</c><00:05:06.600><c> our</c><00:05:06.840><c> revised</c>

00:05:07.230 --> 00:05:07.240 align:start position:0%
AI created after analyzing our revised
 

00:05:07.240 --> 00:05:09.670 align:start position:0%
AI created after analyzing our revised
goal<00:05:07.600><c> prompt</c><00:05:08.360><c> but</c><00:05:08.560><c> now</c><00:05:08.759><c> it</c><00:05:08.919><c> has</c><00:05:09.039><c> a</c><00:05:09.199><c> lot</c><00:05:09.400><c> more</c>

00:05:09.670 --> 00:05:09.680 align:start position:0%
goal prompt but now it has a lot more
 

00:05:09.680 --> 00:05:11.710 align:start position:0%
goal prompt but now it has a lot more
information<00:05:10.120><c> to</c><00:05:10.320><c> work</c><00:05:10.600><c> with</c><00:05:11.039><c> it</c><00:05:11.160><c> can</c><00:05:11.320><c> read</c><00:05:11.520><c> our</c>

00:05:11.710 --> 00:05:11.720 align:start position:0%
information to work with it can read our
 

00:05:11.720 --> 00:05:13.830 align:start position:0%
information to work with it can read our
entire<00:05:12.160><c> discussion</c><00:05:12.639><c> and</c><00:05:12.800><c> modify</c><00:05:13.280><c> this</c><00:05:13.479><c> agent</c>

00:05:13.830 --> 00:05:13.840 align:start position:0%
entire discussion and modify this agent
 

00:05:13.840 --> 00:05:16.029 align:start position:0%
entire discussion and modify this agent
according<00:05:14.120><c> to</c><00:05:14.280><c> the</c><00:05:14.479><c> comments</c><00:05:14.919><c> made</c><00:05:15.160><c> to</c><00:05:15.360><c> them</c>

00:05:16.029 --> 00:05:16.039 align:start position:0%
according to the comments made to them
 

00:05:16.039 --> 00:05:18.309 align:start position:0%
according to the comments made to them
as<00:05:16.160><c> well</c><00:05:16.320><c> as</c><00:05:16.520><c> their</c><00:05:16.800><c> replies</c><00:05:17.800><c> clicking</c><00:05:18.120><c> the</c>

00:05:18.309 --> 00:05:18.319 align:start position:0%
as well as their replies clicking the
 

00:05:18.319 --> 00:05:20.670 align:start position:0%
as well as their replies clicking the
regenerate<00:05:18.919><c> button</c><00:05:19.319><c> expands</c><00:05:19.840><c> Upon</c><00:05:20.080><c> Our</c><00:05:20.319><c> agent</c>

00:05:20.670 --> 00:05:20.680 align:start position:0%
regenerate button expands Upon Our agent
 

00:05:20.680 --> 00:05:22.790 align:start position:0%
regenerate button expands Upon Our agent
resulting<00:05:21.080><c> in</c><00:05:21.199><c> a</c><00:05:21.319><c> more</c><00:05:21.639><c> robust</c><00:05:22.120><c> and</c><00:05:22.280><c> detailed</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
resulting in a more robust and detailed
 

00:05:22.800 --> 00:05:24.629 align:start position:0%
resulting in a more robust and detailed
agent<00:05:23.120><c> tailored</c><00:05:23.680><c> to</c><00:05:23.800><c> the</c><00:05:24.039><c> exacting</c>

00:05:24.629 --> 00:05:24.639 align:start position:0%
agent tailored to the exacting
 

00:05:24.639 --> 00:05:27.230 align:start position:0%
agent tailored to the exacting
specifications<00:05:25.479><c> of</c><00:05:25.639><c> our</c><00:05:25.960><c> project</c><00:05:26.840><c> so</c><00:05:27.000><c> if</c><00:05:27.120><c> you</c>

00:05:27.230 --> 00:05:27.240 align:start position:0%
specifications of our project so if you
 

00:05:27.240 --> 00:05:29.110 align:start position:0%
specifications of our project so if you
hung<00:05:27.479><c> around</c><00:05:27.840><c> this</c><00:05:28.000><c> long</c><00:05:28.440><c> you</c><00:05:28.720><c> probably</c><00:05:28.960><c> want</c>

00:05:29.110 --> 00:05:29.120 align:start position:0%
hung around this long you probably want
 

00:05:29.120 --> 00:05:30.990 align:start position:0%
hung around this long you probably want
to<00:05:29.240><c> know</c><00:05:29.440><c> how</c><00:05:29.759><c> to</c><00:05:29.880><c> install</c><00:05:30.280><c> autog</c><00:05:30.639><c> grock</c>

00:05:30.990 --> 00:05:31.000 align:start position:0%
to know how to install autog grock
 

00:05:31.000 --> 00:05:33.749 align:start position:0%
to know how to install autog grock
locally<00:05:31.919><c> first</c><00:05:32.240><c> go</c><00:05:32.400><c> to</c><00:05:32.639><c> GitHub</c><00:05:33.280><c> and</c><00:05:33.440><c> click</c>

00:05:33.749 --> 00:05:33.759 align:start position:0%
locally first go to GitHub and click
 

00:05:33.759 --> 00:05:35.670 align:start position:0%
locally first go to GitHub and click
this<00:05:33.919><c> button</c><00:05:34.160><c> to</c><00:05:34.360><c> copy</c><00:05:34.639><c> the</c><00:05:34.840><c> path</c><00:05:35.080><c> to</c><00:05:35.240><c> the</c><00:05:35.360><c> file</c>

00:05:35.670 --> 00:05:35.680 align:start position:0%
this button to copy the path to the file
 

00:05:35.680 --> 00:05:38.189 align:start position:0%
this button to copy the path to the file
archive<00:05:36.680><c> then</c><00:05:36.840><c> from</c><00:05:37.039><c> an</c><00:05:37.199><c> empty</c><00:05:37.520><c> folder</c><00:05:37.919><c> in</c><00:05:38.039><c> a</c>

00:05:38.189 --> 00:05:38.199 align:start position:0%
archive then from an empty folder in a
 

00:05:38.199 --> 00:05:40.749 align:start position:0%
archive then from an empty folder in a
command<00:05:38.639><c> prompt</c><00:05:39.160><c> type</c><00:05:39.479><c> git</c><00:05:39.759><c> clone</c><00:05:40.360><c> and</c><00:05:40.520><c> then</c>

00:05:40.749 --> 00:05:40.759 align:start position:0%
command prompt type git clone and then
 

00:05:40.759 --> 00:05:43.270 align:start position:0%
command prompt type git clone and then
paste<00:05:41.080><c> in</c><00:05:41.240><c> the</c><00:05:41.440><c> path</c><00:05:41.720><c> we</c><00:05:41.880><c> just</c><00:05:42.160><c> copied</c><00:05:43.120><c> I'm</c>

00:05:43.270 --> 00:05:43.280 align:start position:0%
paste in the path we just copied I'm
 

00:05:43.280 --> 00:05:44.830 align:start position:0%
paste in the path we just copied I'm
doing<00:05:43.560><c> this</c><00:05:43.680><c> in</c><00:05:43.759><c> the</c><00:05:43.880><c> cond</c><00:05:44.240><c> environment</c><00:05:44.759><c> you</c>

00:05:44.830 --> 00:05:44.840 align:start position:0%
doing this in the cond environment you
 

00:05:44.840 --> 00:05:47.350 align:start position:0%
doing this in the cond environment you
can<00:05:44.960><c> use</c><00:05:45.240><c> cond</c><00:05:45.800><c> or</c><00:05:45.960><c> a</c><00:05:46.199><c> python</c><00:05:46.639><c> or</c><00:05:46.919><c> Docker</c>

00:05:47.350 --> 00:05:47.360 align:start position:0%
can use cond or a python or Docker
 

00:05:47.360 --> 00:05:49.029 align:start position:0%
can use cond or a python or Docker
environment<00:05:47.919><c> or</c><00:05:48.080><c> none</c><00:05:48.319><c> at</c>

00:05:49.029 --> 00:05:49.039 align:start position:0%
environment or none at
 

00:05:49.039 --> 00:05:51.150 align:start position:0%
environment or none at
all<00:05:50.039><c> if</c><00:05:50.120><c> you</c><00:05:50.240><c> don't</c><00:05:50.400><c> know</c><00:05:50.600><c> what</c><00:05:50.720><c> any</c><00:05:50.880><c> of</c><00:05:51.039><c> that</c>

00:05:51.150 --> 00:05:51.160 align:start position:0%
all if you don't know what any of that
 

00:05:51.160 --> 00:05:55.350 align:start position:0%
all if you don't know what any of that
means<00:05:51.479><c> you</c><00:05:51.720><c> probably</c><00:05:52.000><c> shouldn't</c><00:05:52.360><c> do</c>

00:05:55.350 --> 00:05:55.360 align:start position:0%
 
 

00:05:55.360 --> 00:05:58.150 align:start position:0%
 
it<00:05:56.360><c> you</c><00:05:56.520><c> do</c><00:05:56.759><c> have</c><00:05:56.880><c> to</c><00:05:57.000><c> do</c><00:05:57.240><c> this</c><00:05:57.479><c> next</c><00:05:57.800><c> part</c>

00:05:58.150 --> 00:05:58.160 align:start position:0%
it you do have to do this next part
 

00:05:58.160 --> 00:05:59.909 align:start position:0%
it you do have to do this next part
though<00:05:58.840><c> there</c><00:05:58.960><c> are</c><00:05:59.120><c> three</c><00:05:59.600><c> reporting</c>

00:05:59.909 --> 00:05:59.919 align:start position:0%
though there are three reporting
 

00:05:59.919 --> 00:06:01.950 align:start position:0%
though there are three reporting
libraries<00:06:00.440><c> you</c><00:06:00.560><c> can</c><00:06:00.759><c> install</c><00:06:01.160><c> all</c><00:06:01.360><c> at</c><00:06:01.520><c> once</c>

00:06:01.950 --> 00:06:01.960 align:start position:0%
libraries you can install all at once
 

00:06:01.960 --> 00:06:04.390 align:start position:0%
libraries you can install all at once
with<00:06:02.120><c> the</c><00:06:02.280><c> PIP</c><00:06:02.840><c> install</c><00:06:03.400><c> minus</c><00:06:03.759><c> r</c>

00:06:04.390 --> 00:06:04.400 align:start position:0%
with the PIP install minus r
 

00:06:04.400 --> 00:06:08.309 align:start position:0%
with the PIP install minus r
requirements.txt

00:06:08.309 --> 00:06:08.319 align:start position:0%
 
 

00:06:08.319 --> 00:06:10.589 align:start position:0%
 
command<00:06:09.319><c> if</c><00:06:09.440><c> your</c><00:06:09.599><c> screen</c><00:06:09.880><c> fills</c><00:06:10.160><c> up</c><00:06:10.319><c> with</c><00:06:10.479><c> all</c>

00:06:10.589 --> 00:06:10.599 align:start position:0%
command if your screen fills up with all
 

00:06:10.599 --> 00:06:12.830 align:start position:0%
command if your screen fills up with all
this<00:06:10.800><c> garbage</c><00:06:11.479><c> congratulations</c><00:06:12.479><c> filling</c>

00:06:12.830 --> 00:06:12.840 align:start position:0%
this garbage congratulations filling
 

00:06:12.840 --> 00:06:14.550 align:start position:0%
this garbage congratulations filling
your<00:06:13.000><c> screen</c><00:06:13.319><c> up</c><00:06:13.479><c> with</c><00:06:13.680><c> garbage</c><00:06:14.039><c> is</c><00:06:14.160><c> a</c><00:06:14.360><c> good</c>

00:06:14.550 --> 00:06:14.560 align:start position:0%
your screen up with garbage is a good
 

00:06:14.560 --> 00:06:20.270 align:start position:0%
your screen up with garbage is a good
sign<00:06:15.240><c> yay</c>

00:06:20.270 --> 00:06:20.280 align:start position:0%
 
 

00:06:20.280 --> 00:06:22.309 align:start position:0%
 
you<00:06:21.280><c> I'll</c><00:06:21.400><c> show</c><00:06:21.599><c> you</c><00:06:21.759><c> what</c><00:06:21.880><c> you</c><00:06:22.080><c> just</c>

00:06:22.309 --> 00:06:22.319 align:start position:0%
you I'll show you what you just
 

00:06:22.319 --> 00:06:24.350 align:start position:0%
you I'll show you what you just
installed<00:06:23.000><c> if</c><00:06:23.160><c> this</c><00:06:23.319><c> video</c><00:06:23.720><c> is</c><00:06:23.840><c> a</c><00:06:23.919><c> few</c><00:06:24.120><c> weeks</c>

00:06:24.350 --> 00:06:24.360 align:start position:0%
installed if this video is a few weeks
 

00:06:24.360 --> 00:06:25.830 align:start position:0%
installed if this video is a few weeks
old<00:06:24.639><c> you</c><00:06:24.759><c> may</c><00:06:24.919><c> want</c><00:06:25.039><c> to</c><00:06:25.199><c> make</c><00:06:25.360><c> sure</c><00:06:25.639><c> these</c>

00:06:25.830 --> 00:06:25.840 align:start position:0%
old you may want to make sure these
 

00:06:25.840 --> 00:06:27.870 align:start position:0%
old you may want to make sure these
version<00:06:26.160><c> numbers</c><00:06:26.479><c> are</c><00:06:26.680><c> up</c><00:06:26.840><c> to</c>

00:06:27.870 --> 00:06:27.880 align:start position:0%
version numbers are up to
 

00:06:27.880 --> 00:06:34.550 align:start position:0%
version numbers are up to
date<00:06:28.880><c> then</c><00:06:29.039><c> just</c><00:06:29.199><c> fire</c><00:06:29.639><c> up</c><00:06:29.759><c> vsss</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
 
 

00:06:34.560 --> 00:06:37.029 align:start position:0%
 
code<00:06:35.560><c> finally</c><00:06:35.880><c> launch</c><00:06:36.280><c> this</c><00:06:36.440><c> stream</c><00:06:36.800><c> lit</c>

00:06:37.029 --> 00:06:37.039 align:start position:0%
code finally launch this stream lit
 

00:06:37.039 --> 00:06:40.430 align:start position:0%
code finally launch this stream lit
command<00:06:37.319><c> to</c><00:06:37.440><c> fire</c><00:06:37.680><c> up</c><00:06:37.800><c> our</c><00:06:37.919><c> main.py</c>

00:06:40.430 --> 00:06:40.440 align:start position:0%
command to fire up our main.py
 

00:06:40.440 --> 00:06:44.670 align:start position:0%
command to fire up our main.py
file<00:06:41.440><c> and</c><00:06:41.680><c> VOA</c><00:06:42.639><c> as</c><00:06:42.800><c> my</c><00:06:42.960><c> people</c><00:06:43.240><c> say</c><00:06:44.160><c> my</c><00:06:44.319><c> French</c>

00:06:44.670 --> 00:06:44.680 align:start position:0%
file and VOA as my people say my French
 

00:06:44.680 --> 00:06:47.230 align:start position:0%
file and VOA as my people say my French
Canadian<00:06:45.280><c> people</c><00:06:46.280><c> from</c><00:06:46.440><c> the</c><00:06:46.599><c> Canadian</c><00:06:47.080><c> part</c>

00:06:47.230 --> 00:06:47.240 align:start position:0%
Canadian people from the Canadian part
 

00:06:47.240 --> 00:06:49.870 align:start position:0%
Canadian people from the Canadian part
of<00:06:47.840><c> France</c><00:06:48.840><c> make</c><00:06:49.000><c> sure</c><00:06:49.199><c> you</c><00:06:49.319><c> set</c><00:06:49.520><c> up</c><00:06:49.680><c> these</c>

00:06:49.870 --> 00:06:49.880 align:start position:0%
of France make sure you set up these
 

00:06:49.880 --> 00:06:51.710 align:start position:0%
of France make sure you set up these
environment

00:06:51.710 --> 00:06:51.720 align:start position:0%
environment
 

00:06:51.720 --> 00:06:54.309 align:start position:0%
environment
variables<00:06:52.720><c> your</c><00:06:52.960><c> API</c><00:06:53.440><c> key</c><00:06:53.639><c> numbers</c><00:06:53.919><c> will</c><00:06:54.039><c> be</c><00:06:54.199><c> a</c>

00:06:54.309 --> 00:06:54.319 align:start position:0%
variables your API key numbers will be a
 

00:06:54.319 --> 00:06:56.869 align:start position:0%
variables your API key numbers will be a
lot<00:06:54.520><c> less</c><00:06:54.720><c> blurry</c><00:06:55.120><c> than</c><00:06:55.360><c> mine</c><00:06:56.360><c> anyway</c><00:06:56.720><c> that's</c>

00:06:56.869 --> 00:06:56.879 align:start position:0%
lot less blurry than mine anyway that's
 

00:06:56.879 --> 00:06:58.749 align:start position:0%
lot less blurry than mine anyway that's
what<00:06:57.000><c> autog</c><00:06:57.360><c> gr</c><00:06:57.680><c> is</c><00:06:57.919><c> that's</c><00:06:58.039><c> what</c><00:06:58.160><c> autog</c><00:06:58.479><c> gr</c>

00:06:58.749 --> 00:06:58.759 align:start position:0%
what autog gr is that's what autog gr
 

00:06:58.759 --> 00:07:00.830 align:start position:0%
what autog gr is that's what autog gr
does<00:06:59.039><c> and</c><00:06:59.160><c> that's</c><00:06:59.319><c> how</c><00:06:59.639><c> you</c><00:06:59.800><c> can</c><00:06:59.919><c> do</c><00:07:00.080><c> it</c><00:07:00.240><c> too</c>

00:07:00.830 --> 00:07:00.840 align:start position:0%
does and that's how you can do it too
 

00:07:00.840 --> 00:07:03.070 align:start position:0%
does and that's how you can do it too
this<00:07:01.000><c> thing</c><00:07:01.199><c> started</c><00:07:01.599><c> out</c><00:07:01.840><c> as</c><00:07:02.080><c> just</c><00:07:02.199><c> an</c><00:07:02.400><c> agent</c>

00:07:03.070 --> 00:07:03.080 align:start position:0%
this thing started out as just an agent
 

00:07:03.080 --> 00:07:05.350 align:start position:0%
this thing started out as just an agent
generator<00:07:04.080><c> but</c><00:07:04.240><c> thanks</c><00:07:04.479><c> to</c><00:07:04.680><c> everybody</c><00:07:05.160><c> who</c>

00:07:05.350 --> 00:07:05.360 align:start position:0%
generator but thanks to everybody who
 

00:07:05.360 --> 00:07:07.550 align:start position:0%
generator but thanks to everybody who
liked<00:07:05.720><c> and</c><00:07:06.000><c> subscribed</c><00:07:06.639><c> I</c><00:07:06.720><c> was</c><00:07:06.879><c> motivated</c><00:07:07.360><c> to</c>

00:07:07.550 --> 00:07:07.560 align:start position:0%
liked and subscribed I was motivated to
 

00:07:07.560 --> 00:07:09.909 align:start position:0%
liked and subscribed I was motivated to
keep<00:07:07.759><c> running</c><00:07:08.120><c> with</c><00:07:08.319><c> the</c><00:07:08.440><c> ball</c><00:07:09.400><c> there's</c><00:07:09.560><c> a</c><00:07:09.720><c> lot</c>

00:07:09.909 --> 00:07:09.919 align:start position:0%
keep running with the ball there's a lot
 

00:07:09.919 --> 00:07:14.280 align:start position:0%
keep running with the ball there's a lot
more<00:07:10.120><c> to</c><00:07:10.360><c> come</c><00:07:11.120><c> thanks</c><00:07:11.360><c> for</c><00:07:11.599><c> watching</c>

