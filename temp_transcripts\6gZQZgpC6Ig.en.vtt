WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.750 align:start position:0%
 
if<00:00:00.160><c> you're</c><00:00:00.320><c> just</c><00:00:00.480><c> joining</c><00:00:00.880><c> us</c><00:00:01.040><c> now</c><00:00:01.280><c> we're</c><00:00:01.520><c> just</c>

00:00:01.750 --> 00:00:01.760 align:start position:0%
if you're just joining us now we're just
 

00:00:01.760 --> 00:00:03.510 align:start position:0%
if you're just joining us now we're just
about<00:00:02.159><c> to</c>

00:00:03.510 --> 00:00:03.520 align:start position:0%
about to
 

00:00:03.520 --> 00:00:07.269 align:start position:0%
about to
start<00:00:03.840><c> testing</c><00:00:04.319><c> with</c><00:00:04.560><c> our</c><00:00:04.880><c> development</c><00:00:05.600><c> uh</c><00:00:06.080><c> db</c>

00:00:07.269 --> 00:00:07.279 align:start position:0%
start testing with our development uh db
 

00:00:07.279 --> 00:00:09.350 align:start position:0%
start testing with our development uh db
and<00:00:07.759><c> that</c><00:00:08.000><c> means</c><00:00:08.240><c> we're</c><00:00:08.400><c> going</c><00:00:08.480><c> to</c><00:00:08.639><c> go</c><00:00:09.040><c> into</c>

00:00:09.350 --> 00:00:09.360 align:start position:0%
and that means we're going to go into
 

00:00:09.360 --> 00:00:11.430 align:start position:0%
and that means we're going to go into
our<00:00:09.599><c> server</c><00:00:10.000><c> file</c><00:00:10.400><c> and</c><00:00:10.559><c> change</c><00:00:10.880><c> our</c><00:00:11.120><c> ports</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
our server file and change our ports
 

00:00:11.440 --> 00:00:12.709 align:start position:0%
our server file and change our ports
from<00:00:11.679><c> 80</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
from 80
 

00:00:12.719 --> 00:00:15.110 align:start position:0%
from 80
to<00:00:13.120><c> 8000</c><00:00:14.080><c> so</c><00:00:14.240><c> that's</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
to 8000 so that's
 

00:00:15.120 --> 00:00:16.790 align:start position:0%
to 8000 so that's
an<00:00:15.360><c> important</c><00:00:15.759><c> point</c><00:00:16.000><c> because</c><00:00:16.320><c> right</c><00:00:16.480><c> now</c><00:00:16.640><c> if</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
an important point because right now if
 

00:00:16.800 --> 00:00:19.590 align:start position:0%
an important point because right now if
you<00:00:16.880><c> navigate</c><00:00:17.359><c> to</c><00:00:17.760><c> localhost</c>

00:00:19.590 --> 00:00:19.600 align:start position:0%
you navigate to localhost
 

00:00:19.600 --> 00:00:22.070 align:start position:0%
you navigate to localhost
then<00:00:20.400><c> it's</c><00:00:21.039><c> going</c><00:00:21.199><c> to</c><00:00:21.279><c> be</c><00:00:21.439><c> taken</c><00:00:21.680><c> up</c><00:00:21.760><c> by</c><00:00:21.920><c> the</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
then it's going to be taken up by the
 

00:00:22.080 --> 00:00:25.589 align:start position:0%
then it's going to be taken up by the
apache<00:00:22.640><c> server</c><00:00:23.039><c> when</c><00:00:23.199><c> we</c><00:00:23.359><c> installed</c><00:00:23.840><c> samps</c><00:00:24.320><c> so</c>

00:00:25.589 --> 00:00:25.599 align:start position:0%
apache server when we installed samps so
 

00:00:25.599 --> 00:00:27.589 align:start position:0%
apache server when we installed samps so
important<00:00:26.160><c> to</c><00:00:26.240><c> just</c><00:00:26.560><c> do</c><00:00:26.800><c> that</c><00:00:27.119><c> and</c><00:00:27.359><c> have</c><00:00:27.519><c> a</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
important to just do that and have a
 

00:00:27.599 --> 00:00:29.109 align:start position:0%
important to just do that and have a
look<00:00:27.760><c> what</c><00:00:28.000><c> happens</c><00:00:28.320><c> here</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
look what happens here
 

00:00:29.119 --> 00:00:31.429 align:start position:0%
look what happens here
the<00:00:29.279><c> server</c><00:00:29.599><c> gets</c><00:00:29.920><c> updated</c><00:00:30.880><c> beforehand</c><00:00:31.359><c> it</c>

00:00:31.429 --> 00:00:31.439 align:start position:0%
the server gets updated beforehand it
 

00:00:31.439 --> 00:00:33.350 align:start position:0%
the server gets updated beforehand it
was<00:00:31.599><c> running</c><00:00:31.920><c> on</c><00:00:32.079><c> port</c><00:00:32.320><c> 80.</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
was running on port 80.
 

00:00:33.360 --> 00:00:35.910 align:start position:0%
was running on port 80.
hopefully<00:00:33.760><c> it's</c><00:00:33.840><c> going</c><00:00:34.000><c> to</c><00:00:34.079><c> run</c><00:00:34.480><c> on</c><00:00:34.960><c> port</c><00:00:35.360><c> 8</c>

00:00:35.910 --> 00:00:35.920 align:start position:0%
hopefully it's going to run on port 8
 

00:00:35.920 --> 00:00:38.150 align:start position:0%
hopefully it's going to run on port 8
000.<00:00:36.320><c> so</c><00:00:36.559><c> let's</c><00:00:36.880><c> see</c><00:00:37.120><c> here</c><00:00:37.760><c> this</c><00:00:37.920><c> is</c><00:00:38.000><c> what</c>

00:00:38.150 --> 00:00:38.160 align:start position:0%
000. so let's see here this is what
 

00:00:38.160 --> 00:00:39.430 align:start position:0%
000. so let's see here this is what
happens<00:00:38.399><c> when</c><00:00:38.559><c> you</c><00:00:38.640><c> go</c><00:00:38.800><c> to</c><00:00:38.879><c> localhost</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
happens when you go to localhost
 

00:00:39.440 --> 00:00:42.150 align:start position:0%
happens when you go to localhost
currently<00:00:39.840><c> we</c><00:00:40.000><c> see</c><00:00:40.160><c> the</c><00:00:40.320><c> apache</c><00:00:40.879><c> sign</c><00:00:41.600><c> oh</c><00:00:41.920><c> not</c>

00:00:42.150 --> 00:00:42.160 align:start position:0%
currently we see the apache sign oh not
 

00:00:42.160 --> 00:00:45.350 align:start position:0%
currently we see the apache sign oh not
localhost<00:00:42.800><c> i</c><00:00:42.879><c> want</c><00:00:43.200><c> localhost</c>

00:00:45.350 --> 00:00:45.360 align:start position:0%
localhost i want localhost
 

00:00:45.360 --> 00:00:48.869 align:start position:0%
localhost i want localhost
8<00:00:45.680><c> 000.</c><00:00:46.559><c> thank</c><00:00:46.719><c> you</c><00:00:46.879><c> very</c><00:00:47.200><c> much</c>

00:00:48.869 --> 00:00:48.879 align:start position:0%
8 000. thank you very much
 

00:00:48.879 --> 00:00:52.950 align:start position:0%
8 000. thank you very much
yeah<00:00:49.520><c> not</c>

00:00:52.950 --> 00:00:52.960 align:start position:0%
 
 

00:00:52.960 --> 00:00:55.910 align:start position:0%
 
oh<00:00:53.680><c> did</c><00:00:53.840><c> it</c><00:00:53.920><c> work</c>

00:00:55.910 --> 00:00:55.920 align:start position:0%
oh did it work
 

00:00:55.920 --> 00:01:08.950 align:start position:0%
oh did it work
local<00:00:56.399><c> estate</c><00:00:57.199><c> come</c><00:00:57.360><c> on</c><00:00:57.600><c> buddy</c>

00:01:08.950 --> 00:01:08.960 align:start position:0%
 
 

00:01:08.960 --> 00:01:11.350 align:start position:0%
 
this<00:01:09.200><c> is</c><00:01:09.360><c> not</c><00:01:09.600><c> working</c><00:01:10.000><c> well</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
this is not working well
 

00:01:11.360 --> 00:01:12.870 align:start position:0%
this is not working well
this<00:01:11.520><c> is</c><00:01:11.600><c> what</c><00:01:11.760><c> the</c><00:01:11.920><c> blog</c><00:01:12.240><c> actually</c><00:01:12.640><c> looks</c>

00:01:12.870 --> 00:01:12.880 align:start position:0%
this is what the blog actually looks
 

00:01:12.880 --> 00:01:14.820 align:start position:0%
this is what the blog actually looks
like<00:01:12.960><c> but</c><00:01:13.119><c> it's</c><00:01:13.280><c> on</c><00:01:13.439><c> koalacms.com</c>

00:01:14.820 --> 00:01:14.830 align:start position:0%
like but it's on koalacms.com
 

00:01:14.830 --> 00:01:19.749 align:start position:0%
like but it's on koalacms.com
[Music]

00:01:19.749 --> 00:01:19.759 align:start position:0%
 
 

00:01:19.759 --> 00:01:21.990 align:start position:0%
 
come<00:01:20.000><c> on</c><00:01:20.159><c> ready</c><00:01:20.479><c> local</c><00:01:20.880><c> s8</c><00:01:21.280><c> 000.</c><00:01:21.600><c> what</c><00:01:21.759><c> am</c><00:01:21.920><c> i</c>

00:01:21.990 --> 00:01:22.000 align:start position:0%
come on ready local s8 000. what am i
 

00:01:22.000 --> 00:01:23.350 align:start position:0%
come on ready local s8 000. what am i
doing<00:01:22.240><c> around</c><00:01:22.560><c> here</c>

00:01:23.350 --> 00:01:23.360 align:start position:0%
doing around here
 

00:01:23.360 --> 00:01:25.350 align:start position:0%
doing around here
i<00:01:23.680><c> restart</c><00:01:24.159><c> the</c><00:01:24.240><c> server</c><00:01:24.560><c> because</c><00:01:24.799><c> i</c><00:01:24.960><c> started</c>

00:01:25.350 --> 00:01:25.360 align:start position:0%
i restart the server because i started
 

00:01:25.360 --> 00:01:26.390 align:start position:0%
i restart the server because i started
it<00:01:25.439><c> on</c><00:01:25.600><c> a</c><00:01:25.680><c> new</c>

00:01:26.390 --> 00:01:26.400 align:start position:0%
it on a new
 

00:01:26.400 --> 00:01:28.870 align:start position:0%
it on a new
new<00:01:26.640><c> port</c><00:01:26.960><c> maybe</c><00:01:27.200><c> that's</c><00:01:27.439><c> why</c><00:01:27.680><c> it's</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
new port maybe that's why it's
 

00:01:28.880 --> 00:01:30.310 align:start position:0%
new port maybe that's why it's
that's<00:01:29.040><c> why</c><00:01:29.200><c> it's</c><00:01:29.360><c> giving</c><00:01:29.600><c> us</c><00:01:29.840><c> problems</c>

00:01:30.310 --> 00:01:30.320 align:start position:0%
that's why it's giving us problems
 

00:01:30.320 --> 00:01:32.550 align:start position:0%
that's why it's giving us problems
currently

00:01:32.550 --> 00:01:32.560 align:start position:0%
currently
 

00:01:32.560 --> 00:01:33.910 align:start position:0%
currently
we're<00:01:32.720><c> gonna</c><00:01:33.119><c> check</c>

00:01:33.910 --> 00:01:33.920 align:start position:0%
we're gonna check
 

00:01:33.920 --> 00:01:35.590 align:start position:0%
we're gonna check
we<00:01:34.079><c> had</c><00:01:34.320><c> it</c><00:01:34.400><c> on</c><00:01:34.560><c> port</c><00:01:34.880><c> 80</c><00:01:35.119><c> because</c><00:01:35.280><c> that's</c><00:01:35.520><c> how</c>

00:01:35.590 --> 00:01:35.600 align:start position:0%
we had it on port 80 because that's how
 

00:01:35.600 --> 00:01:37.510 align:start position:0%
we had it on port 80 because that's how
you<00:01:35.759><c> need</c><00:01:35.920><c> to</c><00:01:36.000><c> deploy</c><00:01:36.400><c> it</c><00:01:36.479><c> to</c><00:01:36.560><c> heroku</c><00:01:37.200><c> always</c>

00:01:37.510 --> 00:01:37.520 align:start position:0%
you need to deploy it to heroku always
 

00:01:37.520 --> 00:01:40.149 align:start position:0%
you need to deploy it to heroku always
on<00:01:37.680><c> port</c><00:01:37.920><c> 80.</c><00:01:38.400><c> so</c><00:01:38.479><c> that's</c><00:01:38.720><c> an</c><00:01:38.880><c> important</c><00:01:39.840><c> point</c>

00:01:40.149 --> 00:01:40.159 align:start position:0%
on port 80. so that's an important point
 

00:01:40.159 --> 00:01:41.510 align:start position:0%
on port 80. so that's an important point
to<00:01:40.240><c> think</c><00:01:40.479><c> in</c><00:01:40.640><c> mind</c><00:01:40.880><c> and</c><00:01:40.960><c> there</c><00:01:41.040><c> we</c><00:01:41.200><c> go</c><00:01:41.360><c> this</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
to think in mind and there we go this
 

00:01:41.520 --> 00:01:43.350 align:start position:0%
to think in mind and there we go this
time<00:01:41.680><c> we're</c><00:01:41.920><c> on</c><00:01:42.159><c> port</c><00:01:42.399><c> 8000</c><00:01:42.960><c> we're</c><00:01:43.119><c> going</c><00:01:43.280><c> to</c>

00:01:43.350 --> 00:01:43.360 align:start position:0%
time we're on port 8000 we're going to
 

00:01:43.360 --> 00:01:44.870 align:start position:0%
time we're on port 8000 we're going to
reload<00:01:43.840><c> this</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
reload this
 

00:01:44.880 --> 00:01:46.630 align:start position:0%
reload this
and<00:01:45.520><c> hopefully</c><00:01:45.920><c> we're</c><00:01:46.079><c> going</c><00:01:46.159><c> to</c><00:01:46.240><c> get</c><00:01:46.479><c> our</c>

00:01:46.630 --> 00:01:46.640 align:start position:0%
and hopefully we're going to get our
 

00:01:46.640 --> 00:01:48.950 align:start position:0%
and hopefully we're going to get our
blog

00:01:48.950 --> 00:01:48.960 align:start position:0%
blog
 

00:01:48.960 --> 00:01:51.190 align:start position:0%
blog
boom<00:01:49.360><c> so</c><00:01:49.600><c> it's</c><00:01:49.759><c> here</c><00:01:50.079><c> it's</c><00:01:50.320><c> on</c><00:01:50.479><c> localhost</c><00:01:51.040><c> and</c>

00:01:51.190 --> 00:01:51.200 align:start position:0%
boom so it's here it's on localhost and
 

00:01:51.200 --> 00:01:53.109 align:start position:0%
boom so it's here it's on localhost and
it's<00:01:51.280><c> currently</c><00:01:51.600><c> connected</c><00:01:52.079><c> to</c><00:01:52.320><c> our</c>

00:01:53.109 --> 00:01:53.119 align:start position:0%
it's currently connected to our
 

00:01:53.119 --> 00:01:54.550 align:start position:0%
it's currently connected to our
development<00:01:53.680><c> database</c><00:01:54.159><c> we're</c><00:01:54.320><c> going</c><00:01:54.399><c> to</c>

00:01:54.550 --> 00:01:54.560 align:start position:0%
development database we're going to
 

00:01:54.560 --> 00:01:57.429 align:start position:0%
development database we're going to
actually<00:01:54.880><c> log</c><00:01:55.200><c> in</c><00:01:55.360><c> and</c><00:01:55.520><c> sign</c><00:01:55.920><c> up</c>

00:01:57.429 --> 00:01:57.439 align:start position:0%
actually log in and sign up
 

00:01:57.439 --> 00:01:59.749 align:start position:0%
actually log in and sign up
and<00:01:57.759><c> let's</c><00:01:58.000><c> see</c><00:01:58.640><c> i'm</c><00:01:58.799><c> going</c><00:01:58.880><c> to</c><00:01:59.040><c> actually</c><00:01:59.439><c> log</c>

00:01:59.749 --> 00:01:59.759 align:start position:0%
and let's see i'm going to actually log
 

00:01:59.759 --> 00:02:02.550 align:start position:0%
and let's see i'm going to actually log
in<00:02:00.399><c> probably</c><00:02:01.280><c> it's</c><00:02:01.520><c> not</c><00:02:01.759><c> allowing</c><00:02:02.159><c> me</c><00:02:02.399><c> that</c>

00:02:02.550 --> 00:02:02.560 align:start position:0%
in probably it's not allowing me that
 

00:02:02.560 --> 00:02:06.389 align:start position:0%
in probably it's not allowing me that
username<00:02:03.040><c> does</c><00:02:03.200><c> not</c><00:02:03.360><c> exist</c><00:02:03.680><c> in</c><00:02:03.840><c> our</c><00:02:03.920><c> database</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
username does not exist in our database
 

00:02:06.399 --> 00:02:07.910 align:start position:0%
username does not exist in our database
okay<00:02:06.640><c> so</c><00:02:06.799><c> we're</c><00:02:06.960><c> going</c><00:02:07.040><c> to</c><00:02:07.119><c> need</c><00:02:07.280><c> to</c><00:02:07.439><c> register</c>

00:02:07.910 --> 00:02:07.920 align:start position:0%
okay so we're going to need to register
 

00:02:07.920 --> 00:02:10.710 align:start position:0%
okay so we're going to need to register
for<00:02:08.080><c> a</c><00:02:08.160><c> new</c><00:02:08.319><c> one</c>

00:02:10.710 --> 00:02:10.720 align:start position:0%
 
 

00:02:10.720 --> 00:02:13.670 align:start position:0%
 
and<00:02:11.280><c> save</c><00:02:11.680><c> it</c><00:02:12.319><c> and</c><00:02:12.560><c> as</c><00:02:12.720><c> soon</c><00:02:12.959><c> as</c><00:02:13.120><c> that</c><00:02:13.360><c> happens</c>

00:02:13.670 --> 00:02:13.680 align:start position:0%
and save it and as soon as that happens
 

00:02:13.680 --> 00:02:15.110 align:start position:0%
and save it and as soon as that happens
when<00:02:13.920><c> we</c><00:02:14.000><c> register</c><00:02:14.480><c> it's</c><00:02:14.640><c> going</c><00:02:14.720><c> to</c><00:02:14.800><c> take</c><00:02:15.040><c> us</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
when we register it's going to take us
 

00:02:15.120 --> 00:02:17.270 align:start position:0%
when we register it's going to take us
to<00:02:15.280><c> over</c><00:02:15.440><c> there</c><00:02:15.920><c> remember</c><00:02:16.400><c> it's</c><00:02:16.640><c> donations</c><00:02:17.200><c> of</c>

00:02:17.270 --> 00:02:17.280 align:start position:0%
to over there remember it's donations of
 

00:02:17.280 --> 00:02:19.670 align:start position:0%
to over there remember it's donations of
high<00:02:17.599><c> israel.org</c><00:02:18.319><c> so</c><00:02:18.480><c> let's</c><00:02:18.640><c> go</c><00:02:18.800><c> back</c>

00:02:19.670 --> 00:02:19.680 align:start position:0%
high israel.org so let's go back
 

00:02:19.680 --> 00:02:20.830 align:start position:0%
high israel.org so let's go back
into

00:02:20.830 --> 00:02:20.840 align:start position:0%
into
 

00:02:20.840 --> 00:02:25.350 align:start position:0%
into
our<00:02:22.080><c> file</c><00:02:22.959><c> and</c><00:02:23.120><c> we'll</c><00:02:23.360><c> see</c><00:02:23.920><c> that</c><00:02:24.160><c> right</c><00:02:24.400><c> now</c>

00:02:25.350 --> 00:02:25.360 align:start position:0%
our file and we'll see that right now
 

00:02:25.360 --> 00:02:27.350 align:start position:0%
our file and we'll see that right now
then<00:02:25.599><c> our</c><00:02:25.760><c> client</c><00:02:26.239><c> table</c>

00:02:27.350 --> 00:02:27.360 align:start position:0%
then our client table
 

00:02:27.360 --> 00:02:29.430 align:start position:0%
then our client table
we<00:02:27.599><c> have</c><00:02:27.760><c> exactly</c><00:02:28.160><c> one</c><00:02:28.400><c> client</c><00:02:28.800><c> whose</c><00:02:29.040><c> name</c><00:02:29.280><c> is</c>

00:02:29.430 --> 00:02:29.440 align:start position:0%
we have exactly one client whose name is
 

00:02:29.440 --> 00:02:32.390 align:start position:0%
we have exactly one client whose name is
donationsreal.org

00:02:32.390 --> 00:02:32.400 align:start position:0%
donationsreal.org
 

00:02:32.400 --> 00:02:33.990 align:start position:0%
donationsreal.org
there<00:02:32.560><c> you</c><00:02:32.720><c> go</c><00:02:32.959><c> oh</c><00:02:33.120><c> donations</c><00:02:33.599><c> okay</c>

00:02:33.990 --> 00:02:34.000 align:start position:0%
there you go oh donations okay
 

00:02:34.000 --> 00:02:36.229 align:start position:0%
there you go oh donations okay
israel.org<00:02:34.640><c> a</c><00:02:34.720><c> new</c><00:02:34.879><c> client</c><00:02:35.200><c> was</c><00:02:35.360><c> created</c><00:02:35.840><c> now</c>

00:02:36.229 --> 00:02:36.239 align:start position:0%
israel.org a new client was created now
 

00:02:36.239 --> 00:02:37.430 align:start position:0%
israel.org a new client was created now
okay<00:02:36.560><c> so</c>

00:02:37.430 --> 00:02:37.440 align:start position:0%
okay so
 

00:02:37.440 --> 00:02:39.990 align:start position:0%
okay so
we<00:02:38.160><c> have</c><00:02:38.400><c> over</c><00:02:38.640><c> here</c><00:02:38.800><c> a</c><00:02:38.879><c> little</c><00:02:39.040><c> more</c><00:02:39.360><c> data</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
we have over here a little more data
 

00:02:40.000 --> 00:02:41.750 align:start position:0%
we have over here a little more data
right<00:02:40.160><c> we</c><00:02:40.319><c> have</c><00:02:40.400><c> the</c><00:02:40.560><c> client</c><00:02:40.800><c> creation</c><00:02:41.360><c> they</c>

00:02:41.750 --> 00:02:41.760 align:start position:0%
right we have the client creation they
 

00:02:41.760 --> 00:02:43.270 align:start position:0%
right we have the client creation they
still<00:02:41.920><c> don't</c><00:02:42.080><c> have</c><00:02:42.239><c> the</c><00:02:42.400><c> client</c><00:02:42.720><c> analytics</c>

00:02:43.270 --> 00:02:43.280 align:start position:0%
still don't have the client analytics
 

00:02:43.280 --> 00:02:44.150 align:start position:0%
still don't have the client analytics
code

00:02:44.150 --> 00:02:44.160 align:start position:0%
code
 

00:02:44.160 --> 00:02:45.990 align:start position:0%
code
or<00:02:44.400><c> the</c><00:02:44.560><c> client</c><00:02:44.879><c> name</c><00:02:45.120><c> so</c><00:02:45.280><c> we</c><00:02:45.360><c> need</c><00:02:45.519><c> to</c><00:02:45.680><c> start</c>

00:02:45.990 --> 00:02:46.000 align:start position:0%
or the client name so we need to start
 

00:02:46.000 --> 00:02:48.390 align:start position:0%
or the client name so we need to start
inserting<00:02:46.560><c> the</c><00:02:46.800><c> the</c><00:02:47.040><c> client</c><00:02:47.519><c> name</c><00:02:47.920><c> into</c><00:02:48.160><c> our</c>

00:02:48.390 --> 00:02:48.400 align:start position:0%
inserting the the client name into our
 

00:02:48.400 --> 00:02:50.390 align:start position:0%
inserting the the client name into our
query<00:02:49.200><c> let's</c><00:02:49.360><c> see</c><00:02:49.599><c> about</c><00:02:49.760><c> how</c><00:02:50.000><c> we</c><00:02:50.080><c> can</c><00:02:50.239><c> go</c>

00:02:50.390 --> 00:02:50.400 align:start position:0%
query let's see about how we can go
 

00:02:50.400 --> 00:02:52.309 align:start position:0%
query let's see about how we can go
about<00:02:50.720><c> doing</c><00:02:51.040><c> that</c><00:02:51.280><c> let's</c><00:02:51.519><c> look</c><00:02:51.680><c> first</c><00:02:52.000><c> at</c><00:02:52.080><c> the</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
about doing that let's look first at the
 

00:02:52.319 --> 00:02:54.550 align:start position:0%
about doing that let's look first at the
actual<00:02:52.800><c> query</c><00:02:53.120><c> within</c>

00:02:54.550 --> 00:02:54.560 align:start position:0%
actual query within
 

00:02:54.560 --> 00:02:56.470 align:start position:0%
actual query within
um

00:02:56.470 --> 00:02:56.480 align:start position:0%
um
 

00:02:56.480 --> 00:02:59.350 align:start position:0%
um
let's<00:02:56.720><c> see</c><00:02:56.959><c> here</c>

00:02:59.350 --> 00:02:59.360 align:start position:0%
 
 

00:02:59.360 --> 00:03:00.790 align:start position:0%
 
okay<00:02:59.680><c> first</c><00:02:59.920><c> we're</c><00:03:00.080><c> going</c><00:03:00.159><c> to</c><00:03:00.319><c> go</c><00:03:00.480><c> into</c><00:03:00.640><c> our</c>

00:03:00.790 --> 00:03:00.800 align:start position:0%
okay first we're going to go into our
 

00:03:00.800 --> 00:03:02.149 align:start position:0%
okay first we're going to go into our
code<00:03:01.040><c> we're</c><00:03:01.200><c> going</c><00:03:01.280><c> to</c><00:03:01.360><c> look</c><00:03:01.519><c> at</c><00:03:01.599><c> the</c><00:03:01.760><c> actual</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
code we're going to look at the actual
 

00:03:02.159 --> 00:03:03.830 align:start position:0%
code we're going to look at the actual
query<00:03:02.400><c> and</c><00:03:02.560><c> all</c><00:03:02.640><c> the</c><00:03:02.800><c> queries</c><00:03:03.280><c> the</c><00:03:03.440><c> majority</c>

00:03:03.830 --> 00:03:03.840 align:start position:0%
query and all the queries the majority
 

00:03:03.840 --> 00:03:06.470 align:start position:0%
query and all the queries the majority
of<00:03:03.920><c> them</c><00:03:04.080><c> are</c><00:03:04.239><c> in</c><00:03:04.319><c> the</c><00:03:04.400><c> route</c><00:03:04.800><c> section</c>

00:03:06.470 --> 00:03:06.480 align:start position:0%
of them are in the route section
 

00:03:06.480 --> 00:03:08.630 align:start position:0%
of them are in the route section
but<00:03:06.640><c> this</c><00:03:06.879><c> specific</c><00:03:07.519><c> one</c>

00:03:08.630 --> 00:03:08.640 align:start position:0%
but this specific one
 

00:03:08.640 --> 00:03:11.430 align:start position:0%
but this specific one
is<00:03:08.800><c> located</c><00:03:09.360><c> in</c><00:03:09.760><c> the</c><00:03:10.080><c> config</c><00:03:10.800><c> section</c><00:03:11.200><c> which</c>

00:03:11.430 --> 00:03:11.440 align:start position:0%
is located in the config section which
 

00:03:11.440 --> 00:03:13.750 align:start position:0%
is located in the config section which
is<00:03:11.519><c> called</c><00:03:11.920><c> passport</c><00:03:13.040><c> so</c><00:03:13.200><c> if</c><00:03:13.360><c> we</c><00:03:13.440><c> go</c><00:03:13.599><c> to</c>

00:03:13.750 --> 00:03:13.760 align:start position:0%
is called passport so if we go to
 

00:03:13.760 --> 00:03:15.670 align:start position:0%
is called passport so if we go to
passport

00:03:15.670 --> 00:03:15.680 align:start position:0%
passport
 

00:03:15.680 --> 00:03:17.910 align:start position:0%
passport
i'm<00:03:15.840><c> going</c><00:03:16.000><c> to</c><00:03:16.080><c> go</c><00:03:16.239><c> into</c><00:03:16.800><c> insert</c><00:03:17.200><c> into</c><00:03:17.440><c> client</c>

00:03:17.910 --> 00:03:17.920 align:start position:0%
i'm going to go into insert into client
 

00:03:17.920 --> 00:03:20.550 align:start position:0%
i'm going to go into insert into client
ah<00:03:18.319><c> here</c><00:03:18.560><c> we</c><00:03:18.640><c> go</c><00:03:18.800><c> this</c><00:03:19.040><c> is</c><00:03:19.519><c> as</c><00:03:19.760><c> you</c><00:03:19.920><c> see</c><00:03:20.319><c> the</c>

00:03:20.550 --> 00:03:20.560 align:start position:0%
ah here we go this is as you see the
 

00:03:20.560 --> 00:03:22.949 align:start position:0%
ah here we go this is as you see the
register<00:03:21.440><c> strategy</c>

00:03:22.949 --> 00:03:22.959 align:start position:0%
register strategy
 

00:03:22.959 --> 00:03:24.550 align:start position:0%
register strategy
right<00:03:23.280><c> local</c><00:03:23.599><c> strategy</c><00:03:24.000><c> which</c><00:03:24.239><c> is</c><00:03:24.319><c> called</c>

00:03:24.550 --> 00:03:24.560 align:start position:0%
right local strategy which is called
 

00:03:24.560 --> 00:03:27.270 align:start position:0%
right local strategy which is called
local<00:03:25.040><c> sign</c><00:03:25.360><c> up</c>

00:03:27.270 --> 00:03:27.280 align:start position:0%
local sign up
 

00:03:27.280 --> 00:03:28.869 align:start position:0%
local sign up
instead<00:03:27.599><c> of</c><00:03:27.760><c> the</c><00:03:27.840><c> client</c><00:03:28.159><c> client</c><00:03:28.400><c> creation</c>

00:03:28.869 --> 00:03:28.879 align:start position:0%
instead of the client client creation
 

00:03:28.879 --> 00:03:30.830 align:start position:0%
instead of the client client creation
data<00:03:29.200><c> i</c><00:03:29.280><c> need</c><00:03:29.599><c> client</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
data i need client
 

00:03:30.840 --> 00:03:32.390 align:start position:0%
data i need client
name

00:03:32.390 --> 00:03:32.400 align:start position:0%
name
 

00:03:32.400 --> 00:03:35.830 align:start position:0%
name
client<00:03:33.280><c> underscore</c><00:03:34.080><c> name</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
client underscore name
 

00:03:35.840 --> 00:03:40.239 align:start position:0%
client underscore name
client<00:03:36.239><c> creation</c><00:03:36.799><c> date</c><00:03:37.360><c> values</c>

