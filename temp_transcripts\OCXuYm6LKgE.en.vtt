WEBVTT
Kind: captions
Language: en

00:00:01.040 --> 00:00:04.590 align:start position:0%
 
from<00:00:01.280><c> the</c><00:00:01.439><c> beginning</c><00:00:02.200><c> I</c><00:00:02.480><c> had</c><00:00:03.399><c> three</c><00:00:04.200><c> big</c>

00:00:04.590 --> 00:00:04.600 align:start position:0%
from the beginning I had three big
 

00:00:04.600 --> 00:00:07.749 align:start position:0%
from the beginning I had three big
annoyances<00:00:05.480><c> with</c><00:00:05.680><c> AMA</c><00:00:06.640><c> things</c><00:00:06.919><c> that</c><00:00:07.080><c> I</c><00:00:07.240><c> just</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
annoyances with AMA things that I just
 

00:00:07.759 --> 00:00:12.230 align:start position:0%
annoyances with AMA things that I just
couldn't<00:00:08.639><c> stand</c><00:00:09.639><c> in</c><00:00:09.800><c> the</c><00:00:10.040><c> product</c><00:00:11.040><c> the</c><00:00:11.320><c> first</c>

00:00:12.230 --> 00:00:12.240 align:start position:0%
couldn't stand in the product the first
 

00:00:12.240 --> 00:00:13.870 align:start position:0%
couldn't stand in the product the first
was<00:00:12.400><c> that</c><00:00:12.519><c> we</c><00:00:12.639><c> didn't</c><00:00:12.840><c> support</c><00:00:13.200><c> embedding</c>

00:00:13.870 --> 00:00:13.880 align:start position:0%
was that we didn't support embedding
 

00:00:13.880 --> 00:00:16.910 align:start position:0%
was that we didn't support embedding
well<00:00:14.280><c> that</c><00:00:14.480><c> was</c><00:00:14.719><c> solved</c><00:00:15.240><c> a</c><00:00:15.440><c> month</c><00:00:15.679><c> or</c><00:00:15.799><c> so</c><00:00:16.080><c> ago</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
well that was solved a month or so ago
 

00:00:16.920 --> 00:00:19.070 align:start position:0%
well that was solved a month or so ago
second<00:00:17.320><c> we</c><00:00:17.439><c> couldn't</c><00:00:17.680><c> load</c><00:00:18.039><c> more</c><00:00:18.320><c> than</c><00:00:18.800><c> one</c>

00:00:19.070 --> 00:00:19.080 align:start position:0%
second we couldn't load more than one
 

00:00:19.080 --> 00:00:21.590 align:start position:0%
second we couldn't load more than one
model<00:00:19.640><c> at</c><00:00:19.800><c> the</c><00:00:19.920><c> same</c><00:00:20.160><c> time</c><00:00:20.439><c> well</c><00:00:21.080><c> again</c><00:00:21.480><c> that</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
model at the same time well again that
 

00:00:21.600 --> 00:00:24.509 align:start position:0%
model at the same time well again that
was<00:00:21.720><c> solved</c><00:00:22.160><c> about</c><00:00:22.359><c> a</c><00:00:22.519><c> month</c><00:00:22.920><c> ago</c><00:00:23.920><c> the</c><00:00:24.240><c> third</c>

00:00:24.509 --> 00:00:24.519 align:start position:0%
was solved about a month ago the third
 

00:00:24.519 --> 00:00:27.310 align:start position:0%
was solved about a month ago the third
annoyance<00:00:25.480><c> was</c><00:00:25.640><c> that</c><00:00:25.880><c> there</c><00:00:26.080><c> isn't</c><00:00:26.519><c> any</c><00:00:26.920><c> tab</c>

00:00:27.310 --> 00:00:27.320 align:start position:0%
annoyance was that there isn't any tab
 

00:00:27.320 --> 00:00:29.550 align:start position:0%
annoyance was that there isn't any tab
completion<00:00:27.760><c> at</c><00:00:27.920><c> the</c><00:00:28.039><c> command</c><00:00:28.400><c> line</c><00:00:29.279><c> there</c><00:00:29.439><c> was</c>

00:00:29.550 --> 00:00:29.560 align:start position:0%
completion at the command line there was
 

00:00:29.560 --> 00:00:32.389 align:start position:0%
completion at the command line there was
a<00:00:29.720><c> PE</c><00:00:30.080><c> r</c><00:00:30.720><c> that</c><00:00:30.840><c> was</c><00:00:30.960><c> submitted</c><00:00:31.480><c> very</c><00:00:31.679><c> early</c><00:00:32.000><c> on</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
a PE r that was submitted very early on
 

00:00:32.399 --> 00:00:35.950 align:start position:0%
a PE r that was submitted very early on
and<00:00:32.599><c> a</c><00:00:32.800><c> few</c><00:00:33.160><c> more</c><00:00:34.000><c> very</c><00:00:34.320><c> recently</c><00:00:35.320><c> these</c><00:00:35.559><c> would</c>

00:00:35.950 --> 00:00:35.960 align:start position:0%
and a few more very recently these would
 

00:00:35.960 --> 00:00:39.990 align:start position:0%
and a few more very recently these would
solve<00:00:36.600><c> this</c><00:00:37.520><c> but</c><00:00:37.719><c> still</c><00:00:38.320><c> there</c><00:00:38.520><c> is</c><00:00:38.760><c> no</c><00:00:39.040><c> tab</c>

00:00:39.990 --> 00:00:40.000 align:start position:0%
solve this but still there is no tab
 

00:00:40.000 --> 00:00:43.310 align:start position:0%
solve this but still there is no tab
completion<00:00:41.000><c> I</c><00:00:41.280><c> find</c><00:00:41.640><c> this</c><00:00:42.160><c> super</c><00:00:42.640><c> annoying</c>

00:00:43.310 --> 00:00:43.320 align:start position:0%
completion I find this super annoying
 

00:00:43.320 --> 00:00:47.029 align:start position:0%
completion I find this super annoying
that<00:00:43.480><c> I</c><00:00:43.719><c> can't</c><00:00:44.039><c> just</c><00:00:44.239><c> do</c><00:00:44.480><c> ol</c><00:00:44.800><c> llama</c><00:00:45.520><c> Run</c><00:00:46.280><c> tab</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
that I can't just do ol llama Run tab
 

00:00:47.039 --> 00:00:48.709 align:start position:0%
that I can't just do ol llama Run tab
and<00:00:47.160><c> see</c><00:00:47.360><c> a</c><00:00:47.520><c> list</c><00:00:47.680><c> of</c><00:00:47.879><c> all</c><00:00:48.079><c> the</c><00:00:48.199><c> models</c><00:00:48.640><c> that</c>

00:00:48.709 --> 00:00:48.719 align:start position:0%
and see a list of all the models that
 

00:00:48.719 --> 00:00:51.350 align:start position:0%
and see a list of all the models that
are<00:00:48.920><c> available</c><00:00:49.559><c> choose</c><00:00:49.960><c> one</c><00:00:50.559><c> and</c><00:00:50.719><c> be</c><00:00:50.920><c> plopped</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
are available choose one and be plopped
 

00:00:51.360 --> 00:00:54.670 align:start position:0%
are available choose one and be plopped
Into<00:00:51.719><c> the</c><00:00:51.840><c> reppel</c><00:00:52.800><c> Now</c><00:00:53.039><c> sure</c><00:00:53.719><c> I</c><00:00:53.960><c> can</c><00:00:54.160><c> create</c><00:00:54.520><c> my</c>

00:00:54.670 --> 00:00:54.680 align:start position:0%
Into the reppel Now sure I can create my
 

00:00:54.680 --> 00:00:57.110 align:start position:0%
Into the reppel Now sure I can create my
own<00:00:54.960><c> command</c><00:00:55.399><c> that</c><00:00:55.600><c> wraps</c><00:00:56.079><c> around</c><00:00:56.520><c> that</c><00:00:56.760><c> using</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
own command that wraps around that using
 

00:00:57.120 --> 00:01:00.069 align:start position:0%
own command that wraps around that using
fuzzy<00:00:57.559><c> finder</c><00:00:58.280><c> but</c><00:00:58.440><c> that's</c><00:00:58.640><c> now</c><00:00:58.840><c> up</c><00:00:58.960><c> to</c><00:00:59.199><c> me</c><00:01:00.000><c> and</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
fuzzy finder but that's now up to me and
 

00:01:00.079 --> 00:01:03.470 align:start position:0%
fuzzy finder but that's now up to me and
up<00:01:00.239><c> to</c><00:01:00.480><c> every</c><00:01:00.760><c> user</c><00:01:01.359><c> to</c><00:01:01.559><c> do</c><00:01:01.800><c> that</c><00:01:02.519><c> so</c><00:01:02.920><c> I</c><00:01:03.039><c> was</c>

00:01:03.470 --> 00:01:03.480 align:start position:0%
up to every user to do that so I was
 

00:01:03.480 --> 00:01:06.550 align:start position:0%
up to every user to do that so I was
really<00:01:03.840><c> excited</c><00:01:04.360><c> when</c><00:01:04.519><c> I</c><00:01:04.680><c> found</c><00:01:05.320><c> go</c><00:01:05.640><c> Lama</c><00:01:06.400><c> on</c>

00:01:06.550 --> 00:01:06.560 align:start position:0%
really excited when I found go Lama on
 

00:01:06.560 --> 00:01:09.910 align:start position:0%
really excited when I found go Lama on
the<00:01:06.720><c> GitHub</c><00:01:07.080><c> repo</c><00:01:07.600><c> for</c><00:01:07.840><c> olama</c><00:01:08.840><c> Gama</c><00:01:09.360><c> is</c><00:01:09.439><c> a</c><00:01:09.640><c> text</c>

00:01:09.910 --> 00:01:09.920 align:start position:0%
the GitHub repo for olama Gama is a text
 

00:01:09.920 --> 00:01:12.789 align:start position:0%
the GitHub repo for olama Gama is a text
UI<00:01:10.240><c> front</c><00:01:10.479><c> end</c><00:01:10.759><c> for</c><00:01:11.040><c> AMA</c><00:01:11.759><c> and</c><00:01:11.880><c> it</c><00:01:12.040><c> does</c><00:01:12.240><c> it</c>

00:01:12.789 --> 00:01:12.799 align:start position:0%
UI front end for AMA and it does it
 

00:01:12.799 --> 00:01:17.310 align:start position:0%
UI front end for AMA and it does it
really<00:01:13.799><c> really</c><00:01:14.080><c> well</c><00:01:14.400><c> it's</c><00:01:15.119><c> so</c><00:01:16.200><c> awesome</c><00:01:17.200><c> you</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
really really well it's so awesome you
 

00:01:17.320 --> 00:01:19.670 align:start position:0%
really really well it's so awesome you
can<00:01:17.520><c> find</c><00:01:17.720><c> it</c><00:01:17.880><c> at</c><00:01:18.080><c> this</c><00:01:18.320><c> GitHub</c><00:01:18.720><c> repo</c><00:01:19.159><c> owned</c><00:01:19.479><c> by</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
can find it at this GitHub repo owned by
 

00:01:19.680 --> 00:01:23.149 align:start position:0%
can find it at this GitHub repo owned by
samcloud<00:01:20.680><c> out</c><00:01:20.840><c> of</c><00:01:21.000><c> Melbourne</c><00:01:21.840><c> Australia</c><00:01:22.840><c> so</c>

00:01:23.149 --> 00:01:23.159 align:start position:0%
samcloud out of Melbourne Australia so
 

00:01:23.159 --> 00:01:25.670 align:start position:0%
samcloud out of Melbourne Australia so
let's<00:01:23.360><c> start</c><00:01:23.720><c> go</c><00:01:24.040><c> Lama</c><00:01:24.439><c> at</c><00:01:24.680><c> the</c><00:01:24.840><c> command</c><00:01:25.200><c> line</c>

00:01:25.670 --> 00:01:25.680 align:start position:0%
let's start go Lama at the command line
 

00:01:25.680 --> 00:01:27.550 align:start position:0%
let's start go Lama at the command line
you<00:01:25.759><c> can</c><00:01:25.920><c> see</c><00:01:26.079><c> a</c><00:01:26.240><c> list</c><00:01:26.520><c> of</c><00:01:26.680><c> the</c><00:01:26.799><c> models</c><00:01:27.200><c> on</c><00:01:27.360><c> my</c>

00:01:27.550 --> 00:01:27.560 align:start position:0%
you can see a list of the models on my
 

00:01:27.560 --> 00:01:30.030 align:start position:0%
you can see a list of the models on my
machine<00:01:28.439><c> I</c><00:01:28.520><c> can</c><00:01:28.640><c> change</c><00:01:28.920><c> the</c><00:01:29.079><c> sort</c><00:01:29.360><c> order</c><00:01:29.680><c> by</c>

00:01:30.030 --> 00:01:30.040 align:start position:0%
machine I can change the sort order by
 

00:01:30.040 --> 00:01:34.149 align:start position:0%
machine I can change the sort order by
Name<00:01:30.520><c> by</c><00:01:30.680><c> pressing</c><00:01:31.079><c> n</c><00:01:32.079><c> size</c><00:01:32.920><c> by</c><00:01:33.119><c> pressing</c><00:01:33.479><c> s</c>

00:01:34.149 --> 00:01:34.159 align:start position:0%
Name by pressing n size by pressing s
 

00:01:34.159 --> 00:01:37.230 align:start position:0%
Name by pressing n size by pressing s
quantization<00:01:34.960><c> by</c><00:01:35.119><c> pressing</c><00:01:35.520><c> k</c><00:01:36.320><c> f</c><00:01:36.560><c> for</c><00:01:36.840><c> family</c>

00:01:37.230 --> 00:01:37.240 align:start position:0%
quantization by pressing k f for family
 

00:01:37.240 --> 00:01:39.710 align:start position:0%
quantization by pressing k f for family
M<00:01:37.439><c> for</c><00:01:37.600><c> modified</c><00:01:38.200><c> date</c><00:01:38.759><c> I</c><00:01:38.840><c> can</c><00:01:39.079><c> theoretically</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
M for modified date I can theoretically
 

00:01:39.720 --> 00:01:42.030 align:start position:0%
M for modified date I can theoretically
copy<00:01:40.000><c> a</c><00:01:40.119><c> model</c><00:01:40.479><c> with</c><00:01:40.640><c> C</c><00:01:41.159><c> but</c><00:01:41.640><c> that</c><00:01:41.799><c> doesn't</c>

00:01:42.030 --> 00:01:42.040 align:start position:0%
copy a model with C but that doesn't
 

00:01:42.040 --> 00:01:43.950 align:start position:0%
copy a model with C but that doesn't
seem<00:01:42.200><c> to</c><00:01:42.360><c> work</c><00:01:42.520><c> for</c><00:01:42.680><c> me</c><00:01:43.000><c> right</c><00:01:43.159><c> now</c><00:01:43.759><c> and</c>

00:01:43.950 --> 00:01:43.960 align:start position:0%
seem to work for me right now and
 

00:01:43.960 --> 00:01:45.950 align:start position:0%
seem to work for me right now and
neither<00:01:44.200><c> does</c><00:01:44.439><c> d</c><00:01:44.680><c> for</c><00:01:44.880><c> delete</c><00:01:45.240><c> or</c><00:01:45.439><c> U</c><00:01:45.680><c> for</c>

00:01:45.950 --> 00:01:45.960 align:start position:0%
neither does d for delete or U for
 

00:01:45.960 --> 00:01:48.350 align:start position:0%
neither does d for delete or U for
update<00:01:46.680><c> but</c><00:01:46.840><c> this</c><00:01:46.960><c> is</c><00:01:47.200><c> still</c><00:01:47.479><c> awesome</c><00:01:48.159><c> and</c>

00:01:48.350 --> 00:01:48.360 align:start position:0%
update but this is still awesome and
 

00:01:48.360 --> 00:01:50.550 align:start position:0%
update but this is still awesome and
will<00:01:48.560><c> be</c><00:01:48.880><c> even</c><00:01:49.320><c> more</c><00:01:49.640><c> awesome</c><00:01:50.159><c> when</c><00:01:50.360><c> those</c>

00:01:50.550 --> 00:01:50.560 align:start position:0%
will be even more awesome when those
 

00:01:50.560 --> 00:01:53.630 align:start position:0%
will be even more awesome when those
keys<00:01:50.960><c> are</c><00:01:51.240><c> fixed</c><00:01:52.240><c> so</c><00:01:52.680><c> navigate</c><00:01:53.159><c> up</c><00:01:53.320><c> and</c><00:01:53.479><c> down</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
keys are fixed so navigate up and down
 

00:01:53.640 --> 00:01:55.990 align:start position:0%
keys are fixed so navigate up and down
the<00:01:53.880><c> list</c><00:01:54.399><c> and</c><00:01:54.600><c> then</c><00:01:54.799><c> press</c><00:01:55.079><c> I</c><00:01:55.360><c> to</c><00:01:55.560><c> inspect</c><00:01:55.920><c> the</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
the list and then press I to inspect the
 

00:01:56.000 --> 00:01:59.310 align:start position:0%
the list and then press I to inspect the
model<00:01:56.719><c> I</c><00:01:56.960><c> love</c><00:01:57.280><c> this</c><00:01:58.079><c> then</c><00:01:58.320><c> escape</c><00:01:58.759><c> to</c><00:01:58.960><c> get</c><00:01:59.159><c> out</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
model I love this then escape to get out
 

00:01:59.320 --> 00:02:02.190 align:start position:0%
model I love this then escape to get out
of<00:01:59.479><c> that</c><00:01:59.600><c> view</c><00:02:00.200><c> and</c><00:02:00.399><c> press</c><00:02:00.680><c> enter</c><00:02:01.119><c> to</c><00:02:01.320><c> run</c><00:02:01.479><c> it</c>

00:02:02.190 --> 00:02:02.200 align:start position:0%
of that view and press enter to run it
 

00:02:02.200 --> 00:02:04.590 align:start position:0%
of that view and press enter to run it
exit<00:02:02.520><c> out</c><00:02:02.680><c> of</c><00:02:02.840><c> AMA</c><00:02:03.360><c> and</c><00:02:03.479><c> you're</c><00:02:03.759><c> back</c><00:02:03.960><c> in</c><00:02:04.320><c> go</c>

00:02:04.590 --> 00:02:04.600 align:start position:0%
exit out of AMA and you're back in go
 

00:02:04.600 --> 00:02:07.149 align:start position:0%
exit out of AMA and you're back in go
llama<00:02:05.399><c> press</c><00:02:05.600><c> T</c><00:02:05.840><c> to</c><00:02:06.000><c> show</c><00:02:06.280><c> the</c><00:02:06.399><c> output</c><00:02:06.840><c> of</c><00:02:07.000><c> the</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
llama press T to show the output of the
 

00:02:07.159 --> 00:02:11.229 align:start position:0%
llama press T to show the output of the
PS<00:02:07.479><c> command</c><00:02:07.920><c> in</c><00:02:08.200><c> ama</c><00:02:09.200><c> T</c><00:02:09.679><c> is</c><00:02:09.840><c> for</c><00:02:10.239><c> top</c><00:02:10.959><c> because</c>

00:02:11.229 --> 00:02:11.239 align:start position:0%
PS command in ama T is for top because
 

00:02:11.239 --> 00:02:14.430 align:start position:0%
PS command in ama T is for top because
it's<00:02:11.480><c> acting</c><00:02:11.800><c> like</c><00:02:12.080><c> top</c><00:02:12.480><c> or</c><00:02:12.879><c> HTP</c><00:02:13.879><c> you</c><00:02:14.000><c> can</c><00:02:14.200><c> also</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
it's acting like top or HTP you can also
 

00:02:14.440 --> 00:02:16.550 align:start position:0%
it's acting like top or HTP you can also
choose<00:02:14.760><c> models</c><00:02:15.120><c> to</c><00:02:15.280><c> sync</c><00:02:15.599><c> with</c><00:02:15.760><c> LM</c><00:02:16.120><c> Studio</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
choose models to sync with LM Studio
 

00:02:16.560 --> 00:02:18.670 align:start position:0%
choose models to sync with LM Studio
which<00:02:16.720><c> is</c><00:02:17.040><c> pretty</c><00:02:17.319><c> great</c><00:02:17.920><c> I</c><00:02:18.040><c> have</c><00:02:18.160><c> a</c><00:02:18.319><c> script</c>

00:02:18.670 --> 00:02:18.680 align:start position:0%
which is pretty great I have a script
 

00:02:18.680 --> 00:02:20.869 align:start position:0%
which is pretty great I have a script
that<00:02:18.840><c> works</c><00:02:19.160><c> well</c><00:02:19.360><c> for</c><00:02:19.599><c> this</c><00:02:19.840><c> but</c><00:02:20.400><c> I</c><00:02:20.519><c> always</c>

00:02:20.869 --> 00:02:20.879 align:start position:0%
that works well for this but I always
 

00:02:20.879 --> 00:02:24.030 align:start position:0%
that works well for this but I always
prefer<00:02:21.239><c> to</c><00:02:21.400><c> use</c><00:02:21.680><c> tools</c><00:02:22.160><c> someone</c><00:02:22.599><c> else</c><00:02:23.160><c> is</c>

00:02:24.030 --> 00:02:24.040 align:start position:0%
prefer to use tools someone else is
 

00:02:24.040 --> 00:02:27.550 align:start position:0%
prefer to use tools someone else is
maintaining<00:02:25.040><c> I</c><00:02:25.480><c> so</c><00:02:25.840><c> love</c><00:02:26.239><c> tools</c><00:02:26.760><c> like</c><00:02:27.000><c> this</c>

00:02:27.550 --> 00:02:27.560 align:start position:0%
maintaining I so love tools like this
 

00:02:27.560 --> 00:02:30.350 align:start position:0%
maintaining I so love tools like this
super<00:02:27.920><c> simple</c><00:02:28.599><c> and</c><00:02:28.840><c> super</c><00:02:29.200><c> powerful</c><00:02:30.120><c> to</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
super simple and super powerful to
 

00:02:30.360 --> 00:02:32.190 align:start position:0%
super simple and super powerful to
install<00:02:30.840><c> just</c><00:02:31.040><c> download</c><00:02:31.400><c> the</c><00:02:31.519><c> release</c><00:02:31.920><c> file</c>

00:02:32.190 --> 00:02:32.200 align:start position:0%
install just download the release file
 

00:02:32.200 --> 00:02:35.430 align:start position:0%
install just download the release file
from<00:02:32.400><c> GitHub</c><00:02:32.959><c> and</c><00:02:33.120><c> you're</c><00:02:33.440><c> mostly</c><00:02:33.840><c> good</c><00:02:34.000><c> to</c><00:02:34.440><c> go</c>

00:02:35.430 --> 00:02:35.440 align:start position:0%
from GitHub and you're mostly good to go
 

00:02:35.440 --> 00:02:37.390 align:start position:0%
from GitHub and you're mostly good to go
I'm<00:02:35.640><c> thinking</c><00:02:36.000><c> this</c><00:02:36.120><c> is</c><00:02:36.280><c> going</c><00:02:36.440><c> to</c><00:02:36.599><c> be</c><00:02:36.840><c> my</c><00:02:37.080><c> main</c>

00:02:37.390 --> 00:02:37.400 align:start position:0%
I'm thinking this is going to be my main
 

00:02:37.400 --> 00:02:40.309 align:start position:0%
I'm thinking this is going to be my main
way<00:02:37.599><c> to</c><00:02:37.879><c> run</c><00:02:38.080><c> olama</c><00:02:38.640><c> going</c><00:02:38.840><c> forward</c><00:02:39.599><c> that</c><00:02:39.800><c> said</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
way to run olama going forward that said
 

00:02:40.319 --> 00:02:42.990 align:start position:0%
way to run olama going forward that said
Sam<00:02:40.640><c> first</c><00:02:40.840><c> released</c><00:02:41.280><c> this</c><00:02:41.560><c> a</c><00:02:41.760><c> week</c><00:02:41.959><c> ago</c><00:02:42.280><c> so</c><00:02:42.800><c> it</c>

00:02:42.990 --> 00:02:43.000 align:start position:0%
Sam first released this a week ago so it
 

00:02:43.000 --> 00:02:45.070 align:start position:0%
Sam first released this a week ago so it
could<00:02:43.280><c> get</c><00:02:43.440><c> forgotten</c><00:02:44.159><c> when</c><00:02:44.319><c> the</c><00:02:44.519><c> excitement</c>

00:02:45.070 --> 00:02:45.080 align:start position:0%
could get forgotten when the excitement
 

00:02:45.080 --> 00:02:47.470 align:start position:0%
could get forgotten when the excitement
of<00:02:45.239><c> the</c><00:02:45.400><c> new</c><00:02:45.640><c> project</c><00:02:46.040><c> wears</c><00:02:46.360><c> off</c><00:02:46.720><c> but</c><00:02:47.040><c> if</c><00:02:47.200><c> it</c>

00:02:47.470 --> 00:02:47.480 align:start position:0%
of the new project wears off but if it
 

00:02:47.480 --> 00:02:50.990 align:start position:0%
of the new project wears off but if it
does<00:02:48.480><c> I</c><00:02:48.640><c> hope</c><00:02:48.879><c> we</c><00:02:49.200><c> all</c><00:02:49.560><c> can</c><00:02:49.760><c> work</c><00:02:50.159><c> together</c><00:02:50.760><c> to</c>

00:02:50.990 --> 00:02:51.000 align:start position:0%
does I hope we all can work together to
 

00:02:51.000 --> 00:02:54.589 align:start position:0%
does I hope we all can work together to
encourage<00:02:51.440><c> him</c><00:02:51.879><c> to</c><00:02:52.080><c> keep</c><00:02:52.280><c> it</c><00:02:52.480><c> going</c><00:02:53.480><c> that</c><00:02:53.640><c> said</c>

00:02:54.589 --> 00:02:54.599 align:start position:0%
encourage him to keep it going that said
 

00:02:54.599 --> 00:02:57.070 align:start position:0%
encourage him to keep it going that said
I<00:02:54.800><c> also</c><00:02:55.159><c> hope</c><00:02:55.480><c> that</c><00:02:55.720><c> Mike</c><00:02:56.040><c> or</c><00:02:56.319><c> Patrick</c><00:02:56.680><c> or</c><00:02:56.879><c> Jeff</c>

00:02:57.070 --> 00:02:57.080 align:start position:0%
I also hope that Mike or Patrick or Jeff
 

00:02:57.080 --> 00:02:59.430 align:start position:0%
I also hope that Mike or Patrick or Jeff
or<00:02:57.239><c> Bruce</c><00:02:57.560><c> or</c><00:02:58.120><c> anyone</c><00:02:58.480><c> on</c><00:02:58.599><c> the</c><00:02:58.720><c> olama</c><00:02:59.200><c> team</c>

00:02:59.430 --> 00:02:59.440 align:start position:0%
or Bruce or anyone on the olama team
 

00:02:59.440 --> 00:03:02.149 align:start position:0%
or Bruce or anyone on the olama team
would<00:02:59.640><c> just</c><00:02:59.840><c> just</c><00:03:00.000><c> get</c><00:03:00.239><c> T</c><00:03:00.760><c> completion</c><00:03:01.680><c> in</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
would just just get T completion in
 

00:03:02.159 --> 00:03:04.630 align:start position:0%
would just just get T completion in
there<00:03:03.159><c> this</c><00:03:03.360><c> reminds</c><00:03:03.680><c> me</c><00:03:03.920><c> of</c><00:03:04.080><c> the</c><00:03:04.200><c> internal</c>

00:03:04.630 --> 00:03:04.640 align:start position:0%
there this reminds me of the internal
 

00:03:04.640 --> 00:03:06.750 align:start position:0%
there this reminds me of the internal
tool<00:03:04.920><c> from</c><00:03:05.120><c> a</c><00:03:05.239><c> hackathon</c><00:03:05.879><c> that</c><00:03:06.200><c> integrated</c>

00:03:06.750 --> 00:03:06.760 align:start position:0%
tool from a hackathon that integrated
 

00:03:06.760 --> 00:03:10.390 align:start position:0%
tool from a hackathon that integrated
data<00:03:07.200><c> dog</c><00:03:07.519><c> into</c><00:03:07.879><c> htop</c><00:03:08.519><c> including</c><00:03:09.400><c> dashboards</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
data dog into htop including dashboards
 

00:03:10.400 --> 00:03:12.990 align:start position:0%
data dog into htop including dashboards
that<00:03:10.560><c> was</c><00:03:10.760><c> awesome</c><00:03:11.280><c> and</c><00:03:11.480><c> this</c><00:03:11.640><c> is</c><00:03:11.879><c> too</c><00:03:12.760><c> what</c><00:03:12.879><c> do</c>

00:03:12.990 --> 00:03:13.000 align:start position:0%
that was awesome and this is too what do
 

00:03:13.000 --> 00:03:15.589 align:start position:0%
that was awesome and this is too what do
you<00:03:13.200><c> think</c><00:03:13.680><c> are</c><00:03:13.840><c> you</c><00:03:14.040><c> as</c><00:03:14.239><c> excited</c><00:03:14.799><c> as</c><00:03:15.120><c> I</c><00:03:15.239><c> am</c><00:03:15.440><c> for</c>

00:03:15.589 --> 00:03:15.599 align:start position:0%
you think are you as excited as I am for
 

00:03:15.599 --> 00:03:18.190 align:start position:0%
you think are you as excited as I am for
this<00:03:15.799><c> tool</c><00:03:16.519><c> I</c><00:03:16.599><c> am</c><00:03:16.799><c> so</c><00:03:17.080><c> amazed</c><00:03:17.519><c> when</c><00:03:17.680><c> folks</c><00:03:18.000><c> come</c>

00:03:18.190 --> 00:03:18.200 align:start position:0%
this tool I am so amazed when folks come
 

00:03:18.200 --> 00:03:21.070 align:start position:0%
this tool I am so amazed when folks come
up<00:03:18.440><c> with</c><00:03:18.720><c> cool</c><00:03:19.120><c> stuff</c><00:03:19.519><c> like</c><00:03:19.760><c> this</c><00:03:20.680><c> well</c><00:03:20.920><c> check</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
up with cool stuff like this well check
 

00:03:21.080 --> 00:03:22.990 align:start position:0%
up with cool stuff like this well check
it<00:03:21.200><c> out</c><00:03:21.400><c> on</c><00:03:21.519><c> the</c><00:03:21.680><c> GitHub</c><00:03:22.280><c> and</c><00:03:22.440><c> you</c><00:03:22.560><c> can</c><00:03:22.799><c> find</c>

00:03:22.990 --> 00:03:23.000 align:start position:0%
it out on the GitHub and you can find
 

00:03:23.000 --> 00:03:25.509 align:start position:0%
it out on the GitHub and you can find
the<00:03:23.159><c> link</c><00:03:23.360><c> to</c><00:03:23.560><c> that</c><00:03:24.000><c> below</c><00:03:25.000><c> thanks</c><00:03:25.239><c> so</c><00:03:25.360><c> much</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
the link to that below thanks so much
 

00:03:25.519 --> 00:03:29.680 align:start position:0%
the link to that below thanks so much
for<00:03:25.720><c> watching</c><00:03:26.680><c> goodbye</c>

