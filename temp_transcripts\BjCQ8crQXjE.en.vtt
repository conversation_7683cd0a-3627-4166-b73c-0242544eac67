WEBVTT
Kind: captions
Language: en

00:00:00.030 --> 00:00:04.100 align:start position:0%
 
so<00:00:00.329><c> hey</c><00:00:00.480><c> guys</c><00:00:00.630><c> and</c><00:00:00.900><c> welcome</c><00:00:01.079><c> back</c><00:00:01.260><c> so</c><00:00:01.709><c> I</c><00:00:02.990><c> in</c><00:00:03.990><c> the</c>

00:00:04.100 --> 00:00:04.110 align:start position:0%
so hey guys and welcome back so I in the
 

00:00:04.110 --> 00:00:05.960 align:start position:0%
so hey guys and welcome back so I in the
last<00:00:04.259><c> video</c><00:00:04.620><c> we</c><00:00:04.770><c> were</c><00:00:04.890><c> talking</c><00:00:05.279><c> about</c><00:00:05.430><c> this</c>

00:00:05.960 --> 00:00:05.970 align:start position:0%
last video we were talking about this
 

00:00:05.970 --> 00:00:09.259 align:start position:0%
last video we were talking about this
specific<00:00:06.240><c> object</c><00:00:06.930><c> client</c><00:00:07.470><c> data</c><00:00:08.120><c> it's</c><00:00:09.120><c> the</c>

00:00:09.259 --> 00:00:09.269 align:start position:0%
specific object client data it's the
 

00:00:09.269 --> 00:00:12.830 align:start position:0%
specific object client data it's the
first<00:00:10.790><c> the</c><00:00:11.790><c> first</c><00:00:11.940><c> element</c><00:00:12.389><c> of</c><00:00:12.480><c> the</c><00:00:12.630><c> data</c>

00:00:12.830 --> 00:00:12.840 align:start position:0%
first the first element of the data
 

00:00:12.840 --> 00:00:16.490 align:start position:0%
first the first element of the data
array<00:00:13.469><c> okay</c><00:00:14.370><c> and</c><00:00:14.690><c> cordage</c><00:00:15.690><c> is</c><00:00:15.870><c> we're</c><00:00:16.410><c> gonna</c>

00:00:16.490 --> 00:00:16.500 align:start position:0%
array okay and cordage is we're gonna
 

00:00:16.500 --> 00:00:20.540 align:start position:0%
array okay and cordage is we're gonna
find<00:00:16.770><c> it</c><00:00:16.980><c> over</c><00:00:17.160><c> here</c><00:00:17.460><c> scope</c><00:00:17.940><c> that</c><00:00:18.240><c> client</c><00:00:19.550><c> okay</c>

00:00:20.540 --> 00:00:20.550 align:start position:0%
find it over here scope that client okay
 

00:00:20.550 --> 00:00:21.650 align:start position:0%
find it over here scope that client okay
sorry

00:00:21.650 --> 00:00:21.660 align:start position:0%
sorry
 

00:00:21.660 --> 00:00:26.570 align:start position:0%
sorry
da<00:00:22.470><c> de</c><00:00:22.830><c> de</c><00:00:23.750><c> da</c><00:00:24.750><c> client</c><00:00:25.410><c> theme</c><00:00:25.769><c> okay</c><00:00:26.279><c> so</c><00:00:26.340><c> we're</c>

00:00:26.570 --> 00:00:26.580 align:start position:0%
da de de da client theme okay so we're
 

00:00:26.580 --> 00:00:27.800 align:start position:0%
da de de da client theme okay so we're
gonna<00:00:26.670><c> add</c><00:00:26.910><c> a</c><00:00:26.939><c> client</c><00:00:27.240><c> theme</c><00:00:27.539><c> for</c><00:00:27.750><c> the</c>

00:00:27.800 --> 00:00:27.810 align:start position:0%
gonna add a client theme for the
 

00:00:27.810 --> 00:00:31.900 align:start position:0%
gonna add a client theme for the
specific<00:00:28.410><c> user</c><00:00:28.800><c> and</c><00:00:29.539><c> this</c><00:00:30.539><c> specific</c><00:00:30.720><c> user</c>

00:00:31.900 --> 00:00:31.910 align:start position:0%
specific user and this specific user
 

00:00:31.910 --> 00:00:34.130 align:start position:0%
specific user and this specific user
cannot<00:00:32.910><c> read</c><00:00:33.120><c> properly</c><00:00:33.329><c> client</c><00:00:33.840><c> theme</c><00:00:33.989><c> of</c>

00:00:34.130 --> 00:00:34.140 align:start position:0%
cannot read properly client theme of
 

00:00:34.140 --> 00:00:34.880 align:start position:0%
cannot read properly client theme of
undefined

00:00:34.880 --> 00:00:34.890 align:start position:0%
undefined
 

00:00:34.890 --> 00:00:38.090 align:start position:0%
undefined
once<00:00:35.070><c> that</c><00:00:35.219><c> guy's</c><00:00:36.260><c> let's</c><00:00:37.260><c> refresh</c><00:00:37.739><c> the</c><00:00:37.920><c> page</c>

00:00:38.090 --> 00:00:38.100 align:start position:0%
once that guy's let's refresh the page
 

00:00:38.100 --> 00:00:40.100 align:start position:0%
once that guy's let's refresh the page
over<00:00:38.370><c> here</c><00:00:38.489><c> okay</c><00:00:39.270><c> everything</c><00:00:39.750><c> is</c><00:00:39.870><c> working</c>

00:00:40.100 --> 00:00:40.110 align:start position:0%
over here okay everything is working
 

00:00:40.110 --> 00:00:54.650 align:start position:0%
over here okay everything is working
good<00:00:40.730><c> refresh</c><00:00:41.730><c> it</c><00:00:52.670><c> the</c><00:00:53.670><c> actual</c><00:00:54.090><c> site</c><00:00:54.300><c> is</c><00:00:54.449><c> much</c>

00:00:54.650 --> 00:00:54.660 align:start position:0%
good refresh it the actual site is much
 

00:00:54.660 --> 00:00:56.889 align:start position:0%
good refresh it the actual site is much
faster<00:00:55.199><c> so</c><00:00:55.410><c> I</c><00:00:55.620><c> don't</c><00:00:55.920><c> know</c><00:00:55.980><c> why</c><00:00:56.160><c> it</c><00:00:56.219><c> does</c><00:00:56.489><c> this</c>

00:00:56.889 --> 00:00:56.899 align:start position:0%
faster so I don't know why it does this
 

00:00:56.899 --> 00:01:01.819 align:start position:0%
faster so I don't know why it does this
canari<00:00:57.899><c> properties</c><00:00:58.469><c> 0</c><00:00:58.949><c> of</c><00:00:59.129><c> undefined</c><00:01:00.829><c> because</c>

00:01:01.819 --> 00:01:01.829 align:start position:0%
canari properties 0 of undefined because
 

00:01:01.829 --> 00:01:06.800 align:start position:0%
canari properties 0 of undefined because
we<00:01:01.949><c> don't</c><00:01:02.129><c> have</c><00:01:02.430><c> an</c><00:01:02.930><c> actual</c><00:01:05.450><c> theme</c><00:01:06.450><c> for</c><00:01:06.689><c> this</c>

00:01:06.800 --> 00:01:06.810 align:start position:0%
we don't have an actual theme for this
 

00:01:06.810 --> 00:01:09.859 align:start position:0%
we don't have an actual theme for this
guy<00:01:07.049><c> so</c><00:01:07.439><c> let's</c><00:01:08.100><c> do</c><00:01:08.340><c> select</c><00:01:08.820><c> all</c><00:01:09.030><c> from</c><00:01:09.210><c> client</c>

00:01:09.859 --> 00:01:09.869 align:start position:0%
guy so let's do select all from client
 

00:01:09.869 --> 00:01:22.550 align:start position:0%
guy so let's do select all from client
okay<00:01:11.360><c> no</c><00:01:12.360><c> no</c><00:01:13.290><c> one</c><00:01:13.470><c> here</c><00:01:13.830><c> right</c><00:01:20.960><c> all</c><00:01:21.960><c> centered</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
okay no no one here right all centered
 

00:01:22.560 --> 00:01:27.410 align:start position:0%
okay no no one here right all centered
around<00:01:22.680><c> the</c><00:01:23.460><c> my</c><00:01:24.090><c> sequel</c><00:01:24.770><c> and</c><00:01:25.770><c> lishi</c><00:01:26.520><c> baby</c><00:01:26.939><c> at</c>

00:01:27.410 --> 00:01:27.420 align:start position:0%
around the my sequel and lishi baby at
 

00:01:27.420 --> 00:01:29.810 align:start position:0%
around the my sequel and lishi baby at
gmail.com<00:01:28.080><c> this</c><00:01:28.890><c> is</c><00:01:29.009><c> what</c><00:01:29.130><c> we</c><00:01:29.250><c> have</c><00:01:29.369><c> currently</c>

00:01:29.810 --> 00:01:29.820 align:start position:0%
gmail.com this is what we have currently
 

00:01:29.820 --> 00:01:34.969 align:start position:0%
gmail.com this is what we have currently
client<00:01:30.299><c> theme</c><00:01:30.570><c> equals</c><00:01:31.820><c> that's</c><00:01:32.820><c> a</c><00:01:33.350><c> cyborg</c><00:01:34.350><c> okay</c>

00:01:34.969 --> 00:01:34.979 align:start position:0%
client theme equals that's a cyborg okay
 

00:01:34.979 --> 00:01:40.999 align:start position:0%
client theme equals that's a cyborg okay
which<00:01:35.189><c> is</c><00:01:35.340><c> the</c><00:01:35.369><c> dark</c><00:01:39.350><c> we</c><00:01:40.350><c> need</c><00:01:40.470><c> to</c><00:01:40.590><c> insert</c>

00:01:40.999 --> 00:01:41.009 align:start position:0%
which is the dark we need to insert
 

00:01:41.009 --> 00:01:43.429 align:start position:0%
which is the dark we need to insert
something<00:01:41.280><c> into</c><00:01:41.520><c> the</c><00:01:41.790><c> into</c><00:01:42.240><c> that</c><00:01:42.420><c> into</c><00:01:43.229><c> there</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
something into the into that into there
 

00:01:43.439 --> 00:01:45.319 align:start position:0%
something into the into that into there
so<00:01:43.770><c> in</c><00:01:44.250><c> order</c><00:01:44.369><c> to</c><00:01:44.549><c> insert</c><00:01:44.909><c> something</c><00:01:45.149><c> into</c>

00:01:45.319 --> 00:01:45.329 align:start position:0%
so in order to insert something into
 

00:01:45.329 --> 00:01:46.880 align:start position:0%
so in order to insert something into
there<00:01:45.630><c> let's</c><00:01:45.810><c> look</c><00:01:45.960><c> at</c><00:01:46.049><c> the</c><00:01:46.140><c> actual</c><00:01:46.290><c> sequel</c>

00:01:46.880 --> 00:01:46.890 align:start position:0%
there let's look at the actual sequel
 

00:01:46.890 --> 00:01:49.280 align:start position:0%
there let's look at the actual sequel
query<00:01:47.159><c> for</c><00:01:47.430><c> inserting</c><00:01:47.939><c> something</c><00:01:48.360><c> or</c><00:01:48.899><c> to</c><00:01:49.140><c> use</c>

00:01:49.280 --> 00:01:49.290 align:start position:0%
query for inserting something or to use
 

00:01:49.290 --> 00:01:52.999 align:start position:0%
query for inserting something or to use
the<00:01:49.530><c> alter</c><00:01:49.920><c> table</c><00:01:50.159><c> in</c><00:01:50.579><c> a</c><00:01:50.729><c> previous</c><00:01:51.680><c> and</c><00:01:52.680><c> maybe</c>

00:01:52.999 --> 00:01:53.009 align:start position:0%
the alter table in a previous and maybe
 

00:01:53.009 --> 00:02:03.590 align:start position:0%
the alter table in a previous and maybe
episode<00:01:53.490><c> one</c><00:01:53.729><c> I</c><00:01:53.759><c> think</c><00:01:54.060><c> okay</c><00:02:01.070><c> okay</c>

00:02:03.590 --> 00:02:03.600 align:start position:0%
episode one I think okay okay
 

00:02:03.600 --> 00:02:06.210 align:start position:0%
episode one I think okay okay
insert<00:02:04.600><c> into</c><00:02:04.750><c> table</c><00:02:05.049><c> by</c><00:02:05.409><c> the</c><00:02:05.439><c> way</c><00:02:05.619><c> in</c><00:02:05.799><c> terms</c><00:02:05.830><c> of</c>

00:02:06.210 --> 00:02:06.220 align:start position:0%
insert into table by the way in terms of
 

00:02:06.220 --> 00:02:08.460 align:start position:0%
insert into table by the way in terms of
the<00:02:06.460><c> angularjs</c><00:02:07.000><c> service</c><00:02:07.900><c> that</c><00:02:08.110><c> we</c><00:02:08.229><c> talked</c>

00:02:08.460 --> 00:02:08.470 align:start position:0%
the angularjs service that we talked
 

00:02:08.470 --> 00:02:10.380 align:start position:0%
the angularjs service that we talked
about<00:02:08.640><c> this</c><00:02:09.640><c> is</c><00:02:09.789><c> essentially</c><00:02:10.119><c> how</c><00:02:10.330><c> the</c>

00:02:10.380 --> 00:02:10.390 align:start position:0%
about this is essentially how the
 

00:02:10.390 --> 00:02:13.410 align:start position:0%
about this is essentially how the
service<00:02:10.959><c> works</c><00:02:10.989><c> in</c><00:02:11.590><c> angularjs</c><00:02:12.069><c> it's</c><00:02:12.640><c> when</c><00:02:13.239><c> you</c>

00:02:13.410 --> 00:02:13.420 align:start position:0%
service works in angularjs it's when you
 

00:02:13.420 --> 00:02:15.030 align:start position:0%
service works in angularjs it's when you
have<00:02:13.630><c> data</c><00:02:13.900><c> that</c><00:02:14.140><c> needs</c><00:02:14.500><c> to</c><00:02:14.680><c> be</c><00:02:14.800><c> shared</c>

00:02:15.030 --> 00:02:15.040 align:start position:0%
have data that needs to be shared
 

00:02:15.040 --> 00:02:19.699 align:start position:0%
have data that needs to be shared
between<00:02:15.280><c> two</c><00:02:15.610><c> controllers</c><00:02:16.739><c> instead</c><00:02:17.739><c> of</c>

00:02:19.699 --> 00:02:19.709 align:start position:0%
between two controllers instead of
 

00:02:19.709 --> 00:02:22.110 align:start position:0%
between two controllers instead of
loading<00:02:20.709><c> it</c><00:02:20.800><c> in</c><00:02:20.920><c> the</c><00:02:21.010><c> controller</c><00:02:21.430><c> himself</c><00:02:21.910><c> in</c>

00:02:22.110 --> 00:02:22.120 align:start position:0%
loading it in the controller himself in
 

00:02:22.120 --> 00:02:24.540 align:start position:0%
loading it in the controller himself in
itself<00:02:22.660><c> we're</c><00:02:22.870><c> doing</c><00:02:23.050><c> an</c><00:02:23.140><c> HTTP</c><00:02:23.860><c> call</c><00:02:24.130><c> in</c><00:02:24.400><c> the</c>

00:02:24.540 --> 00:02:24.550 align:start position:0%
itself we're doing an HTTP call in the
 

00:02:24.550 --> 00:02:27.390 align:start position:0%
itself we're doing an HTTP call in the
in<00:02:24.790><c> the</c><00:02:25.060><c> client</c><00:02:25.510><c> service</c><00:02:25.989><c> and</c><00:02:26.230><c> then</c><00:02:26.920><c> we</c><00:02:27.160><c> pass</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
in the client service and then we pass
 

00:02:27.400 --> 00:02:29.400 align:start position:0%
in the client service and then we pass
that<00:02:27.670><c> into</c><00:02:27.970><c> the</c><00:02:28.090><c> main</c><00:02:28.270><c> controller</c><00:02:28.840><c> and</c><00:02:29.230><c> then</c>

00:02:29.400 --> 00:02:29.410 align:start position:0%
that into the main controller and then
 

00:02:29.410 --> 00:02:30.840 align:start position:0%
that into the main controller and then
we'll<00:02:29.560><c> be</c><00:02:29.680><c> able</c><00:02:29.890><c> to</c><00:02:30.010><c> inject</c><00:02:30.370><c> that</c><00:02:30.550><c> same</c>

00:02:30.840 --> 00:02:30.850 align:start position:0%
we'll be able to inject that same
 

00:02:30.850 --> 00:02:32.699 align:start position:0%
we'll be able to inject that same
service<00:02:31.300><c> it's</c><00:02:31.450><c> only</c><00:02:31.630><c> gonna</c><00:02:31.870><c> load</c><00:02:32.080><c> once</c><00:02:32.410><c> that's</c>

00:02:32.699 --> 00:02:32.709 align:start position:0%
service it's only gonna load once that's
 

00:02:32.709 --> 00:02:34.770 align:start position:0%
service it's only gonna load once that's
why<00:02:32.830><c> it's</c><00:02:32.950><c> called</c><00:02:33.069><c> a</c><00:02:33.190><c> singleton</c><00:02:33.790><c> as</c><00:02:34.390><c> opposed</c>

00:02:34.770 --> 00:02:34.780 align:start position:0%
why it's called a singleton as opposed
 

00:02:34.780 --> 00:02:37.050 align:start position:0%
why it's called a singleton as opposed
to<00:02:34.840><c> the</c><00:02:34.959><c> controller</c><00:02:35.380><c> in</c><00:02:35.800><c> which</c><00:02:36.069><c> case</c><00:02:36.760><c> every</c>

00:02:37.050 --> 00:02:37.060 align:start position:0%
to the controller in which case every
 

00:02:37.060 --> 00:02:38.880 align:start position:0%
to the controller in which case every
time<00:02:37.090><c> you</c><00:02:37.360><c> load</c><00:02:37.540><c> that</c><00:02:37.600><c> specific</c><00:02:38.350><c> view</c><00:02:38.709><c> it</c>

00:02:38.880 --> 00:02:38.890 align:start position:0%
time you load that specific view it
 

00:02:38.890 --> 00:02:40.949 align:start position:0%
time you load that specific view it
loads<00:02:39.130><c> the</c><00:02:39.250><c> controller</c><00:02:39.850><c> again</c><00:02:40.090><c> but</c><00:02:40.750><c> if</c><00:02:40.840><c> you</c>

00:02:40.949 --> 00:02:40.959 align:start position:0%
loads the controller again but if you
 

00:02:40.959 --> 00:02:42.600 align:start position:0%
loads the controller again but if you
open<00:02:41.170><c> the</c><00:02:41.380><c> profile</c><00:02:41.800><c> controller</c><00:02:42.370><c> it's</c><00:02:42.580><c> going</c>

00:02:42.600 --> 00:02:42.610 align:start position:0%
open the profile controller it's going
 

00:02:42.610 --> 00:02:44.910 align:start position:0%
open the profile controller it's going
to<00:02:42.760><c> load</c><00:02:42.940><c> the</c><00:02:43.090><c> user</c><00:02:43.330><c> service</c><00:02:43.690><c> and</c><00:02:44.080><c> then</c><00:02:44.620><c> if</c><00:02:44.769><c> you</c>

00:02:44.910 --> 00:02:44.920 align:start position:0%
to load the user service and then if you
 

00:02:44.920 --> 00:02:46.890 align:start position:0%
to load the user service and then if you
go<00:02:45.130><c> ahead</c><00:02:45.370><c> and</c><00:02:45.670><c> open</c><00:02:45.819><c> the</c><00:02:46.209><c> dashboard</c><00:02:46.720><c> view</c>

00:02:46.890 --> 00:02:46.900 align:start position:0%
go ahead and open the dashboard view
 

00:02:46.900 --> 00:02:49.410 align:start position:0%
go ahead and open the dashboard view
which<00:02:47.080><c> also</c><00:02:47.319><c> uses</c><00:02:47.680><c> the</c><00:02:48.010><c> user</c><00:02:48.190><c> service</c><00:02:48.819><c> it's</c>

00:02:49.410 --> 00:02:49.420 align:start position:0%
which also uses the user service it's
 

00:02:49.420 --> 00:02:51.090 align:start position:0%
which also uses the user service it's
not<00:02:49.540><c> going</c><00:02:49.810><c> to</c><00:02:49.870><c> load</c><00:02:50.050><c> it</c><00:02:50.200><c> again</c><00:02:50.319><c> because</c><00:02:50.800><c> the</c>

00:02:51.090 --> 00:02:51.100 align:start position:0%
not going to load it again because the
 

00:02:51.100 --> 00:02:53.280 align:start position:0%
not going to load it again because the
user<00:02:51.250><c> service</c><00:02:51.670><c> has</c><00:02:51.790><c> already</c><00:02:52.120><c> been</c><00:02:52.269><c> loaded</c><00:02:52.690><c> so</c>

00:02:53.280 --> 00:02:53.290 align:start position:0%
user service has already been loaded so
 

00:02:53.290 --> 00:02:55.500 align:start position:0%
user service has already been loaded so
that's<00:02:53.470><c> a</c><00:02:53.769><c> small</c><00:02:54.370><c> load</c><00:02:54.580><c> into</c><00:02:54.850><c> why</c><00:02:55.060><c> we're</c><00:02:55.330><c> using</c>

00:02:55.500 --> 00:02:55.510 align:start position:0%
that's a small load into why we're using
 

00:02:55.510 --> 00:02:57.900 align:start position:0%
that's a small load into why we're using
that<00:02:55.750><c> an</c><00:02:55.870><c> angular</c><00:02:56.380><c> service</c><00:02:56.920><c> in</c><00:02:57.160><c> this</c><00:02:57.400><c> specific</c>

00:02:57.900 --> 00:02:57.910 align:start position:0%
that an angular service in this specific
 

00:02:57.910 --> 00:03:00.570 align:start position:0%
that an angular service in this specific
case<00:02:58.090><c> and</c><00:02:58.450><c> we</c><00:02:59.200><c> do</c><00:02:59.319><c> insert</c><00:02:59.680><c> into</c><00:02:59.860><c> table</c><00:03:00.160><c> name</c>

00:03:00.570 --> 00:03:00.580 align:start position:0%
case and we do insert into table name
 

00:03:00.580 --> 00:03:04.770 align:start position:0%
case and we do insert into table name
this<00:03:01.030><c> these</c><00:03:01.569><c> value</c><00:03:02.580><c> value</c><00:03:03.580><c> 1</c><00:03:03.910><c> I'll</c><00:03:04.299><c> insert</c>

00:03:04.770 --> 00:03:04.780 align:start position:0%
this these value value 1 I'll insert
 

00:03:04.780 --> 00:03:09.979 align:start position:0%
this these value value 1 I'll insert
into<00:03:04.930><c> table</c><00:03:05.170><c> name</c><00:03:05.530><c> the</c><00:03:05.739><c> column</c><00:03:06.510><c> okay</c><00:03:07.510><c> awesome</c>

00:03:09.979 --> 00:03:09.989 align:start position:0%
 
 

00:03:09.989 --> 00:03:15.060 align:start position:0%
 
you're<00:03:10.989><c> gonna</c><00:03:11.110><c> go</c><00:03:11.370><c> into</c><00:03:13.350><c> insert</c><00:03:14.350><c> into</c><00:03:14.650><c> table</c>

00:03:15.060 --> 00:03:15.070 align:start position:0%
you're gonna go into insert into table
 

00:03:15.070 --> 00:03:21.990 align:start position:0%
you're gonna go into insert into table
name<00:03:16.680><c> ok</c><00:03:18.120><c> where</c><00:03:20.070><c> client</c><00:03:21.070><c> underscore</c><00:03:21.730><c> ID</c>

00:03:21.990 --> 00:03:22.000 align:start position:0%
name ok where client underscore ID
 

00:03:22.000 --> 00:03:24.920 align:start position:0%
name ok where client underscore ID
equals<00:03:22.900><c> what's</c><00:03:23.380><c> the</c><00:03:23.500><c> client</c><00:03:23.890><c> ID</c><00:03:24.040><c> which</c><00:03:24.670><c> is</c>

00:03:24.920 --> 00:03:24.930 align:start position:0%
equals what's the client ID which is
 

00:03:24.930 --> 00:03:29.120 align:start position:0%
equals what's the client ID which is
Leisha<00:03:25.930><c> baby</c><00:03:26.290><c> at</c><00:03:26.500><c> gmail.com</c><00:03:26.799><c> client</c><00:03:27.670><c> ID</c><00:03:27.760><c> 8</c>

00:03:29.120 --> 00:03:29.130 align:start position:0%
Leisha baby at gmail.com client ID 8
 

00:03:29.130 --> 00:03:31.590 align:start position:0%
Leisha baby at gmail.com client ID 8
client<00:03:30.130><c> ID</c><00:03:30.370><c> equals</c><00:03:30.910><c> 8</c>

00:03:31.590 --> 00:03:31.600 align:start position:0%
client ID equals 8
 

00:03:31.600 --> 00:03:35.190 align:start position:0%
client ID equals 8
I'm<00:03:32.380><c> gonna</c><00:03:32.590><c> go</c><00:03:32.799><c> adding</c><00:03:33.670><c> into</c><00:03:34.000><c> the</c><00:03:34.200><c> client</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
I'm gonna go adding into the client
 

00:03:35.200 --> 00:03:40.020 align:start position:0%
I'm gonna go adding into the client
table<00:03:36.359><c> something</c><00:03:37.359><c> called</c><00:03:37.840><c> the</c><00:03:38.560><c> client</c><00:03:39.370><c> theme</c>

00:03:40.020 --> 00:03:40.030 align:start position:0%
table something called the client theme
 

00:03:40.030 --> 00:03:43.710 align:start position:0%
table something called the client theme
and<00:03:40.739><c> the</c><00:03:41.739><c> value</c><00:03:42.130><c> is</c><00:03:42.310><c> gonna</c><00:03:42.489><c> be</c><00:03:42.760><c> just</c><00:03:43.480><c> one</c>

00:03:43.710 --> 00:03:43.720 align:start position:0%
and the value is gonna be just one
 

00:03:43.720 --> 00:03:46.050 align:start position:0%
and the value is gonna be just one
record<00:03:44.230><c> which</c><00:03:44.500><c> is</c><00:03:44.620><c> gonna</c><00:03:44.799><c> be</c><00:03:44.980><c> a</c><00:03:45.220><c> string</c><00:03:45.640><c> and</c>

00:03:46.050 --> 00:03:46.060 align:start position:0%
record which is gonna be a string and
 

00:03:46.060 --> 00:03:49.979 align:start position:0%
record which is gonna be a string and
the<00:03:46.540><c> string</c><00:03:46.600><c> is</c><00:03:47.140><c> gonna</c><00:03:47.380><c> be</c><00:03:48.329><c> it's</c><00:03:49.329><c> gonna</c><00:03:49.510><c> be</c><00:03:49.630><c> a</c>

00:03:49.979 --> 00:03:49.989 align:start position:0%
the string is gonna be it's gonna be a
 

00:03:49.989 --> 00:03:53.819 align:start position:0%
the string is gonna be it's gonna be a
cyborg<00:03:50.560><c> okay</c><00:03:51.250><c> the</c><00:03:51.579><c> very</c><00:03:51.850><c> dark</c><00:03:52.209><c> theme</c><00:03:52.829><c> let's</c>

00:03:53.819 --> 00:03:53.829 align:start position:0%
cyborg okay the very dark theme let's
 

00:03:53.829 --> 00:04:01.410 align:start position:0%
cyborg okay the very dark theme let's
see<00:03:54.100><c> if</c><00:03:54.190><c> that</c><00:03:54.370><c> works</c><00:03:56.280><c> and</c><00:03:57.280><c> run</c><00:03:57.790><c> that</c><00:04:00.420><c> where</c>

00:04:01.410 --> 00:04:01.420 align:start position:0%
see if that works and run that where
 

00:04:01.420 --> 00:04:04.670 align:start position:0%
see if that works and run that where
client<00:04:01.870><c> underscore</c><00:04:02.260><c> ID</c><00:04:02.470><c> was</c><00:04:02.709><c> 8</c><00:04:02.950><c> at</c><00:04:03.130><c> line</c><00:04:03.340><c> 3</c>

00:04:04.670 --> 00:04:04.680 align:start position:0%
client underscore ID was 8 at line 3
 

00:04:04.680 --> 00:04:09.059 align:start position:0%
client underscore ID was 8 at line 3
incident<00:04:05.680><c> to</c><00:04:05.769><c> client</c><00:04:06.370><c> client</c><00:04:07.120><c> theme</c><00:04:08.069><c> not</c>

00:04:09.059 --> 00:04:09.069 align:start position:0%
incident to client client theme not
 

00:04:09.069 --> 00:04:12.090 align:start position:0%
incident to client client theme not
inside<00:04:09.750><c> client</c><00:04:10.750><c> underscore</c><00:04:11.199><c> ID</c><00:04:11.350><c> why</c><00:04:11.829><c> does</c><00:04:11.949><c> it</c>

00:04:12.090 --> 00:04:12.100 align:start position:0%
inside client underscore ID why does it
 

00:04:12.100 --> 00:04:14.110 align:start position:0%
inside client underscore ID why does it
not<00:04:12.250><c> work</c>

00:04:14.110 --> 00:04:14.120 align:start position:0%
not work
 

00:04:14.120 --> 00:04:26.779 align:start position:0%
not work
client<00:04:15.120><c> ID</c><00:04:19.880><c> look</c><00:04:20.880><c> back</c><00:04:21.120><c> here</c><00:04:25.430><c> still</c><00:04:26.430><c> we're</c>

00:04:26.779 --> 00:04:26.789 align:start position:0%
client ID look back here still we're
 

00:04:26.789 --> 00:04:46.460 align:start position:0%
client ID look back here still we're
here<00:04:28.729><c> okay</c><00:04:31.460><c> insert</c><00:04:33.199><c> where</c><00:04:34.199><c> I</c><00:04:34.620><c> sleep</c><00:04:35.190><c> oh</c><00:04:45.320><c> and</c><00:04:46.320><c> as</c>

00:04:46.460 --> 00:04:46.470 align:start position:0%
here okay insert where I sleep oh and as
 

00:04:46.470 --> 00:04:48.350 align:start position:0%
here okay insert where I sleep oh and as
you<00:04:46.560><c> can</c><00:04:46.680><c> see</c><00:04:46.740><c> encoding</c><00:04:47.639><c> almost</c><00:04:47.940><c> everything</c>

00:04:48.350 --> 00:04:48.360 align:start position:0%
you can see encoding almost everything
 

00:04:48.360 --> 00:04:50.390 align:start position:0%
you can see encoding almost everything
is<00:04:48.510><c> just</c><00:04:48.750><c> doing</c><00:04:49.110><c> Google</c><00:04:49.470><c> searches</c><00:04:49.650><c> and</c>

00:04:50.390 --> 00:04:50.400 align:start position:0%
is just doing Google searches and
 

00:04:50.400 --> 00:04:52.820 align:start position:0%
is just doing Google searches and
continually<00:04:51.590><c> understanding</c><00:04:52.590><c> that</c><00:04:52.680><c> you</c><00:04:52.800><c> don't</c>

00:04:52.820 --> 00:04:52.830 align:start position:0%
continually understanding that you don't
 

00:04:52.830 --> 00:04:54.200 align:start position:0%
continually understanding that you don't
know<00:04:53.190><c> everything</c><00:04:53.669><c> and</c><00:04:53.760><c> that</c><00:04:53.880><c> you</c><00:04:53.970><c> need</c><00:04:54.090><c> to</c>

00:04:54.200 --> 00:04:54.210 align:start position:0%
know everything and that you need to
 

00:04:54.210 --> 00:04:55.790 align:start position:0%
know everything and that you need to
continue<00:04:54.630><c> asking</c><00:04:55.050><c> Google</c><00:04:55.199><c> it's</c><00:04:55.530><c> all</c><00:04:55.620><c> about</c>

00:04:55.790 --> 00:04:55.800 align:start position:0%
continue asking Google it's all about
 

00:04:55.800 --> 00:04:57.800 align:start position:0%
continue asking Google it's all about
asking<00:04:55.979><c> the</c><00:04:56.340><c> right</c><00:04:56.370><c> questions</c><00:04:57.030><c> okay</c>

00:04:57.800 --> 00:04:57.810 align:start position:0%
asking the right questions okay
 

00:04:57.810 --> 00:05:01.250 align:start position:0%
asking the right questions okay
so<00:04:58.080><c> we'll</c><00:04:58.199><c> continue</c><00:04:58.380><c> in</c><00:04:58.620><c> the</c><00:04:58.680><c> next</c><00:04:58.919><c> video</c>

