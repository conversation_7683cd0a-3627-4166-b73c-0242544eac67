WEBVTT
Kind: captions
Language: en

00:00:01.120 --> 00:00:03.590 align:start position:0%
 
hey<00:00:01.280><c> there</c><00:00:01.760><c> it's</c><00:00:02.080><c> your</c><00:00:02.560><c> favorite</c><00:00:02.960><c> evangelist</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
hey there it's your favorite evangelist
 

00:00:03.600 --> 00:00:05.749 align:start position:0%
hey there it's your favorite evangelist
back<00:00:03.840><c> for</c><00:00:04.000><c> another</c><00:00:04.319><c> video</c><00:00:04.880><c> about</c><00:00:05.200><c> tools</c><00:00:05.600><c> that</c>

00:00:05.749 --> 00:00:05.759 align:start position:0%
back for another video about tools that
 

00:00:05.759 --> 00:00:07.430 align:start position:0%
back for another video about tools that
i<00:00:05.920><c> find</c><00:00:06.319><c> useful</c>

00:00:07.430 --> 00:00:07.440 align:start position:0%
i find useful
 

00:00:07.440 --> 00:00:10.150 align:start position:0%
i find useful
second<00:00:07.759><c> video</c><00:00:08.080><c> from</c><00:00:08.240><c> the</c><00:00:08.960><c> new</c><00:00:09.200><c> space</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
second video from the new space
 

00:00:10.160 --> 00:00:12.390 align:start position:0%
second video from the new space
and<00:00:10.480><c> hopefully</c><00:00:11.120><c> over</c><00:00:11.360><c> time</c><00:00:11.840><c> it'll</c><00:00:12.080><c> look</c>

00:00:12.390 --> 00:00:12.400 align:start position:0%
and hopefully over time it'll look
 

00:00:12.400 --> 00:00:14.910 align:start position:0%
and hopefully over time it'll look
better<00:00:12.719><c> and</c>

00:00:14.910 --> 00:00:14.920 align:start position:0%
 
 

00:00:14.920 --> 00:00:17.349 align:start position:0%
 
better<00:00:16.000><c> and</c><00:00:16.160><c> because</c><00:00:16.560><c> some</c><00:00:16.800><c> folks</c><00:00:17.119><c> hate</c>

00:00:17.349 --> 00:00:17.359 align:start position:0%
better and because some folks hate
 

00:00:17.359 --> 00:00:19.750 align:start position:0%
better and because some folks hate
sitting<00:00:17.680><c> through</c><00:00:17.920><c> my</c><00:00:18.160><c> intro</c><00:00:18.880><c> here</c><00:00:19.119><c> is</c><00:00:19.359><c> the</c>

00:00:19.750 --> 00:00:19.760 align:start position:0%
sitting through my intro here is the
 

00:00:19.760 --> 00:00:21.109 align:start position:0%
sitting through my intro here is the
tldr

00:00:21.109 --> 00:00:21.119 align:start position:0%
tldr
 

00:00:21.119 --> 00:00:23.670 align:start position:0%
tldr
i<00:00:21.359><c> am</c><00:00:21.520><c> using</c><00:00:21.920><c> doppler</c><00:00:22.320><c> for</c><00:00:22.560><c> personal</c><00:00:22.960><c> projects</c>

00:00:23.670 --> 00:00:23.680 align:start position:0%
i am using doppler for personal projects
 

00:00:23.680 --> 00:00:25.509 align:start position:0%
i am using doppler for personal projects
if<00:00:23.840><c> you</c><00:00:23.920><c> want</c><00:00:24.080><c> to</c><00:00:24.160><c> try</c><00:00:24.400><c> to</c><00:00:24.480><c> use</c><00:00:24.720><c> it</c><00:00:25.119><c> use</c><00:00:25.359><c> my</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
if you want to try to use it use my
 

00:00:25.519 --> 00:00:27.589 align:start position:0%
if you want to try to use it use my
referral<00:00:26.080><c> code</c><00:00:26.400><c> at</c><00:00:26.560><c> the</c><00:00:26.720><c> bottom</c><00:00:27.199><c> and</c><00:00:27.359><c> you'll</c>

00:00:27.589 --> 00:00:27.599 align:start position:0%
referral code at the bottom and you'll
 

00:00:27.599 --> 00:00:29.589 align:start position:0%
referral code at the bottom and you'll
get<00:00:27.760><c> a</c><00:00:27.840><c> hundred</c><00:00:28.080><c> dollar</c><00:00:28.400><c> credit</c><00:00:28.960><c> and</c><00:00:29.199><c> so</c><00:00:29.359><c> will</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
get a hundred dollar credit and so will
 

00:00:29.599 --> 00:00:31.509 align:start position:0%
get a hundred dollar credit and so will
i<00:00:30.080><c> you</c><00:00:30.240><c> don't</c><00:00:30.400><c> have</c><00:00:30.560><c> to</c><00:00:30.640><c> pay</c><00:00:30.800><c> for</c><00:00:30.960><c> anything</c><00:00:31.359><c> but</c>

00:00:31.509 --> 00:00:31.519 align:start position:0%
i you don't have to pay for anything but
 

00:00:31.519 --> 00:00:33.750 align:start position:0%
i you don't have to pay for anything but
you<00:00:31.679><c> get</c><00:00:31.840><c> more</c><00:00:32.079><c> features</c><00:00:32.640><c> if</c><00:00:32.800><c> you</c><00:00:32.880><c> do</c><00:00:33.440><c> and</c><00:00:33.680><c> at</c>

00:00:33.750 --> 00:00:33.760 align:start position:0%
you get more features if you do and at
 

00:00:33.760 --> 00:00:35.350 align:start position:0%
you get more features if you do and at
their<00:00:33.920><c> pro</c><00:00:34.160><c> level</c><00:00:34.399><c> the</c><00:00:34.640><c> credit</c><00:00:34.880><c> will</c><00:00:35.040><c> get</c><00:00:35.200><c> you</c>

00:00:35.350 --> 00:00:35.360 align:start position:0%
their pro level the credit will get you
 

00:00:35.360 --> 00:00:37.670 align:start position:0%
their pro level the credit will get you
at<00:00:35.440><c> least</c><00:00:35.840><c> five</c><00:00:36.079><c> months</c><00:00:36.480><c> of</c><00:00:36.640><c> use</c>

00:00:37.670 --> 00:00:37.680 align:start position:0%
at least five months of use
 

00:00:37.680 --> 00:00:39.430 align:start position:0%
at least five months of use
are<00:00:37.840><c> they</c><00:00:38.000><c> paying</c><00:00:38.239><c> me</c><00:00:38.399><c> to</c><00:00:38.559><c> say</c><00:00:38.719><c> this</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
are they paying me to say this
 

00:00:39.440 --> 00:00:41.750 align:start position:0%
are they paying me to say this
no<00:00:39.840><c> i</c><00:00:39.920><c> have</c><00:00:40.160><c> no</c><00:00:40.399><c> relationship</c><00:00:40.960><c> with</c><00:00:41.120><c> doppler</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
no i have no relationship with doppler
 

00:00:41.760 --> 00:00:44.069 align:start position:0%
no i have no relationship with doppler
at<00:00:41.920><c> the</c><00:00:42.079><c> time</c><00:00:42.399><c> of</c><00:00:42.559><c> this</c><00:00:42.800><c> recording</c><00:00:43.680><c> i</c><00:00:43.840><c> doubt</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
at the time of this recording i doubt
 

00:00:44.079 --> 00:00:46.069 align:start position:0%
at the time of this recording i doubt
they<00:00:44.559><c> know</c><00:00:44.800><c> that</c><00:00:44.960><c> i</c><00:00:45.120><c> exist</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
they know that i exist
 

00:00:46.079 --> 00:00:47.990 align:start position:0%
they know that i exist
but<00:00:46.320><c> i</c><00:00:46.480><c> do</c><00:00:46.640><c> honestly</c><00:00:47.200><c> use</c><00:00:47.360><c> them</c><00:00:47.520><c> for</c><00:00:47.680><c> personal</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
but i do honestly use them for personal
 

00:00:48.000 --> 00:00:50.310 align:start position:0%
but i do honestly use them for personal
projects<00:00:48.960><c> i</c><00:00:49.120><c> will</c><00:00:49.280><c> get</c><00:00:49.440><c> a</c><00:00:49.600><c> credit</c><00:00:49.920><c> if</c><00:00:50.079><c> you</c><00:00:50.160><c> try</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
projects i will get a credit if you try
 

00:00:50.320 --> 00:00:52.790 align:start position:0%
projects i will get a credit if you try
them<00:00:50.559><c> out</c><00:00:51.039><c> but</c><00:00:51.280><c> no</c><00:00:51.520><c> cash</c><00:00:51.920><c> will</c><00:00:52.160><c> ever</c><00:00:52.480><c> change</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
them out but no cash will ever change
 

00:00:52.800 --> 00:00:54.549 align:start position:0%
them out but no cash will ever change
hands<00:00:53.280><c> and</c><00:00:53.440><c> i've</c><00:00:53.600><c> been</c><00:00:53.760><c> paying</c><00:00:54.000><c> for</c><00:00:54.160><c> their</c><00:00:54.320><c> pro</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
hands and i've been paying for their pro
 

00:00:54.559 --> 00:00:56.950 align:start position:0%
hands and i've been paying for their pro
account<00:00:54.960><c> out</c><00:00:55.039><c> of</c><00:00:55.120><c> my</c><00:00:55.360><c> own</c><00:00:55.520><c> wallet</c><00:00:56.320><c> for</c><00:00:56.640><c> a</c><00:00:56.719><c> few</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
account out of my own wallet for a few
 

00:00:56.960 --> 00:01:00.310 align:start position:0%
account out of my own wallet for a few
months<00:00:57.199><c> now</c><00:00:57.760><c> okay</c><00:00:58.480><c> end</c><00:00:58.879><c> tldr</c>

00:01:00.310 --> 00:01:00.320 align:start position:0%
months now okay end tldr
 

00:01:00.320 --> 00:01:02.389 align:start position:0%
months now okay end tldr
if<00:01:00.480><c> you</c><00:01:00.640><c> do</c><00:01:00.960><c> any</c><00:01:01.199><c> kind</c><00:01:01.440><c> of</c><00:01:01.600><c> development</c><00:01:02.079><c> work</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
if you do any kind of development work
 

00:01:02.399 --> 00:01:04.310 align:start position:0%
if you do any kind of development work
you<00:01:02.559><c> know</c><00:01:02.800><c> that</c><00:01:02.960><c> dealing</c><00:01:03.359><c> with</c><00:01:03.600><c> secrets</c><00:01:04.080><c> is</c>

00:01:04.310 --> 00:01:04.320 align:start position:0%
you know that dealing with secrets is
 

00:01:04.320 --> 00:01:06.630 align:start position:0%
you know that dealing with secrets is
always<00:01:04.720><c> a</c><00:01:04.960><c> pain</c><00:01:05.760><c> lots</c><00:01:06.000><c> of</c><00:01:06.080><c> folks</c><00:01:06.320><c> will</c><00:01:06.479><c> put</c>

00:01:06.630 --> 00:01:06.640 align:start position:0%
always a pain lots of folks will put
 

00:01:06.640 --> 00:01:08.789 align:start position:0%
always a pain lots of folks will put
their<00:01:06.960><c> api</c><00:01:07.439><c> keys</c><00:01:07.760><c> and</c><00:01:07.920><c> tokens</c><00:01:08.479><c> and</c><00:01:08.640><c> other</c>

00:01:08.789 --> 00:01:08.799 align:start position:0%
their api keys and tokens and other
 

00:01:08.799 --> 00:01:11.030 align:start position:0%
their api keys and tokens and other
secrets<00:01:09.360><c> in</c><00:01:09.520><c> environment</c><00:01:10.000><c> variables</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
secrets in environment variables
 

00:01:11.040 --> 00:01:13.109 align:start position:0%
secrets in environment variables
but<00:01:11.200><c> if</c><00:01:11.360><c> you</c><00:01:11.439><c> have</c><00:01:11.680><c> multiple</c><00:01:12.159><c> projects</c><00:01:12.880><c> you'll</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
but if you have multiple projects you'll
 

00:01:13.119 --> 00:01:15.030 align:start position:0%
but if you have multiple projects you'll
have<00:01:13.439><c> dozens</c><00:01:13.840><c> of</c><00:01:13.920><c> secrets</c><00:01:14.320><c> defined</c><00:01:14.720><c> in</c><00:01:14.880><c> your</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
have dozens of secrets defined in your
 

00:01:15.040 --> 00:01:17.190 align:start position:0%
have dozens of secrets defined in your
shell<00:01:15.280><c> profiles</c><00:01:15.759><c> or</c><00:01:16.000><c> startup</c><00:01:16.320><c> scripts</c><00:01:17.119><c> and</c>

00:01:17.190 --> 00:01:17.200 align:start position:0%
shell profiles or startup scripts and
 

00:01:17.200 --> 00:01:19.590 align:start position:0%
shell profiles or startup scripts and
then<00:01:17.439><c> it's</c><00:01:17.759><c> always</c><00:01:18.080><c> hard</c><00:01:18.240><c> to</c><00:01:18.400><c> remember</c>

00:01:19.590 --> 00:01:19.600 align:start position:0%
then it's always hard to remember
 

00:01:19.600 --> 00:01:22.550 align:start position:0%
then it's always hard to remember
what<00:01:19.920><c> goes</c><00:01:20.400><c> with</c><00:01:20.880><c> which</c><00:01:21.280><c> project</c><00:01:21.759><c> and</c><00:01:22.240><c> are</c><00:01:22.400><c> any</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
what goes with which project and are any
 

00:01:22.560 --> 00:01:24.550 align:start position:0%
what goes with which project and are any
of<00:01:22.720><c> them</c><00:01:22.880><c> safe</c><00:01:23.119><c> to</c><00:01:23.280><c> delete</c><00:01:23.759><c> or</c><00:01:24.080><c> do</c><00:01:24.240><c> they</c><00:01:24.400><c> need</c>

00:01:24.550 --> 00:01:24.560 align:start position:0%
of them safe to delete or do they need
 

00:01:24.560 --> 00:01:26.789 align:start position:0%
of them safe to delete or do they need
to<00:01:24.640><c> be</c><00:01:24.880><c> updated</c><00:01:25.759><c> and</c><00:01:25.920><c> so</c><00:01:26.159><c> these</c><00:01:26.400><c> things</c>

00:01:26.789 --> 00:01:26.799 align:start position:0%
to be updated and so these things
 

00:01:26.799 --> 00:01:29.270 align:start position:0%
to be updated and so these things
multiply<00:01:27.439><c> and</c><00:01:27.680><c> soon</c><00:01:27.920><c> you</c><00:01:28.080><c> have</c><00:01:28.400><c> a</c><00:01:28.479><c> config</c><00:01:28.960><c> file</c>

00:01:29.270 --> 00:01:29.280 align:start position:0%
multiply and soon you have a config file
 

00:01:29.280 --> 00:01:31.749 align:start position:0%
multiply and soon you have a config file
that<00:01:29.520><c> is</c><00:01:29.759><c> just</c><00:01:30.240><c> miles</c><00:01:30.880><c> long</c>

00:01:31.749 --> 00:01:31.759 align:start position:0%
that is just miles long
 

00:01:31.759 --> 00:01:35.190 align:start position:0%
that is just miles long
so<00:01:32.000><c> one</c><00:01:32.240><c> solution</c><00:01:32.720><c> is</c><00:01:32.880><c> to</c><00:01:33.040><c> use</c><00:01:33.360><c> a</c><00:01:33.759><c> env</c><00:01:34.320><c> file</c>

00:01:35.190 --> 00:01:35.200 align:start position:0%
so one solution is to use a env file
 

00:01:35.200 --> 00:01:37.590 align:start position:0%
so one solution is to use a env file
there<00:01:35.520><c> you</c><00:01:35.680><c> have</c><00:01:36.159><c> different</c><00:01:36.479><c> env</c><00:01:36.960><c> files</c><00:01:37.360><c> in</c>

00:01:37.590 --> 00:01:37.600 align:start position:0%
there you have different env files in
 

00:01:37.600 --> 00:01:40.230 align:start position:0%
there you have different env files in
each<00:01:37.840><c> project</c><00:01:38.240><c> directory</c><00:01:38.799><c> and</c><00:01:38.960><c> a</c><00:01:39.119><c> cli</c><00:01:39.600><c> tool</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
each project directory and a cli tool
 

00:01:40.240 --> 00:01:42.149 align:start position:0%
each project directory and a cli tool
like<00:01:40.720><c> dot</c><00:01:40.960><c> env</c>

00:01:42.149 --> 00:01:42.159 align:start position:0%
like dot env
 

00:01:42.159 --> 00:01:44.069 align:start position:0%
like dot env
will<00:01:42.399><c> load</c><00:01:42.799><c> and</c><00:01:43.040><c> unload</c><00:01:43.439><c> them</c><00:01:43.759><c> as</c><00:01:43.920><c> you</c>

00:01:44.069 --> 00:01:44.079 align:start position:0%
will load and unload them as you
 

00:01:44.079 --> 00:01:46.069 align:start position:0%
will load and unload them as you
navigate<00:01:44.640><c> into</c><00:01:44.880><c> that</c><00:01:45.040><c> directory</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
navigate into that directory
 

00:01:46.079 --> 00:01:48.710 align:start position:0%
navigate into that directory
but<00:01:46.320><c> anytime</c><00:01:46.799><c> you</c><00:01:46.960><c> rely</c><00:01:47.360><c> on</c><00:01:47.520><c> a</c><00:01:47.680><c> file</c><00:01:48.399><c> you</c><00:01:48.560><c> have</c>

00:01:48.710 --> 00:01:48.720 align:start position:0%
but anytime you rely on a file you have
 

00:01:48.720 --> 00:01:51.350 align:start position:0%
but anytime you rely on a file you have
the<00:01:49.119><c> very</c><00:01:49.520><c> real</c><00:01:49.920><c> risk</c><00:01:50.479><c> of</c><00:01:50.799><c> sharing</c><00:01:51.119><c> those</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
the very real risk of sharing those
 

00:01:51.360 --> 00:01:53.350 align:start position:0%
the very real risk of sharing those
files<00:01:51.680><c> where</c><00:01:51.840><c> you</c><00:01:52.000><c> shouldn't</c><00:01:52.720><c> they</c><00:01:52.880><c> could</c><00:01:53.119><c> get</c>

00:01:53.350 --> 00:01:53.360 align:start position:0%
files where you shouldn't they could get
 

00:01:53.360 --> 00:01:55.830 align:start position:0%
files where you shouldn't they could get
exposed<00:01:53.840><c> on</c><00:01:53.920><c> a</c><00:01:54.000><c> zoom</c><00:01:54.240><c> call</c><00:01:54.640><c> or</c><00:01:54.880><c> even</c><00:01:55.200><c> worse</c><00:01:55.680><c> in</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
exposed on a zoom call or even worse in
 

00:01:55.840 --> 00:01:57.910 align:start position:0%
exposed on a zoom call or even worse in
your<00:01:56.000><c> next</c><00:01:56.320><c> push</c><00:01:56.640><c> to</c><00:01:56.799><c> github</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
your next push to github
 

00:01:57.920 --> 00:01:59.670 align:start position:0%
your next push to github
pushing<00:01:58.240><c> the</c><00:01:58.320><c> wrong</c><00:01:58.640><c> files</c><00:01:58.960><c> to</c><00:01:59.040><c> github</c><00:01:59.520><c> or</c>

00:01:59.670 --> 00:01:59.680 align:start position:0%
pushing the wrong files to github or
 

00:01:59.680 --> 00:02:01.830 align:start position:0%
pushing the wrong files to github or
gitlab<00:02:00.079><c> or</c><00:02:00.479><c> whatever</c><00:02:00.799><c> your</c><00:02:01.040><c> source</c><00:02:01.360><c> control</c>

00:02:01.830 --> 00:02:01.840 align:start position:0%
gitlab or whatever your source control
 

00:02:01.840 --> 00:02:03.830 align:start position:0%
gitlab or whatever your source control
solution<00:02:02.240><c> that</c><00:02:02.320><c> you're</c><00:02:02.479><c> using</c><00:02:02.880><c> is</c><00:02:03.439><c> is</c>

00:02:03.830 --> 00:02:03.840 align:start position:0%
solution that you're using is is
 

00:02:03.840 --> 00:02:06.069 align:start position:0%
solution that you're using is is
incredibly<00:02:04.560><c> easy</c><00:02:04.880><c> to</c><00:02:04.960><c> do</c><00:02:05.280><c> and</c><00:02:05.759><c> rather</c>

00:02:06.069 --> 00:02:06.079 align:start position:0%
incredibly easy to do and rather
 

00:02:06.079 --> 00:02:08.869 align:start position:0%
incredibly easy to do and rather
difficult<00:02:06.479><c> to</c><00:02:06.640><c> roll</c><00:02:06.880><c> back</c><00:02:07.119><c> from</c><00:02:08.239><c> we've</c><00:02:08.560><c> all</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
difficult to roll back from we've all
 

00:02:08.879 --> 00:02:11.190 align:start position:0%
difficult to roll back from we've all
exposed<00:02:09.360><c> secrets</c><00:02:09.759><c> we</c><00:02:10.000><c> shouldn't</c><00:02:10.560><c> and</c><00:02:10.800><c> if</c><00:02:10.959><c> you</c>

00:02:11.190 --> 00:02:11.200 align:start position:0%
exposed secrets we shouldn't and if you
 

00:02:11.200 --> 00:02:13.190 align:start position:0%
exposed secrets we shouldn't and if you
think<00:02:11.440><c> you</c><00:02:11.680><c> haven't</c><00:02:12.400><c> you</c><00:02:12.560><c> just</c><00:02:12.720><c> don't</c><00:02:12.959><c> know</c>

00:02:13.190 --> 00:02:13.200 align:start position:0%
think you haven't you just don't know
 

00:02:13.200 --> 00:02:15.270 align:start position:0%
think you haven't you just don't know
about<00:02:13.440><c> it</c><00:02:13.520><c> yet</c><00:02:14.160><c> there</c><00:02:14.400><c> are</c><00:02:14.480><c> plenty</c><00:02:14.800><c> of</c><00:02:14.959><c> tools</c>

00:02:15.270 --> 00:02:15.280 align:start position:0%
about it yet there are plenty of tools
 

00:02:15.280 --> 00:02:17.350 align:start position:0%
about it yet there are plenty of tools
out<00:02:15.360><c> there</c><00:02:15.599><c> to</c><00:02:15.760><c> scan</c><00:02:16.239><c> github</c><00:02:16.640><c> repos</c><00:02:17.120><c> for</c>

00:02:17.350 --> 00:02:17.360 align:start position:0%
out there to scan github repos for
 

00:02:17.360 --> 00:02:19.750 align:start position:0%
out there to scan github repos for
secrets<00:02:17.760><c> to</c><00:02:17.920><c> take</c><00:02:18.160><c> advantage</c><00:02:18.560><c> of</c><00:02:18.800><c> so</c><00:02:19.360><c> don't</c><00:02:19.599><c> be</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
secrets to take advantage of so don't be
 

00:02:19.760 --> 00:02:20.949 align:start position:0%
secrets to take advantage of so don't be
a<00:02:19.920><c> target</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
a target
 

00:02:20.959 --> 00:02:23.110 align:start position:0%
a target
now<00:02:21.360><c> there</c><00:02:21.520><c> are</c><00:02:21.680><c> lots</c><00:02:21.920><c> of</c><00:02:22.080><c> tools</c><00:02:22.480><c> and</c><00:02:22.640><c> services</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
now there are lots of tools and services
 

00:02:23.120 --> 00:02:25.670 align:start position:0%
now there are lots of tools and services
that<00:02:23.280><c> have</c><00:02:23.440><c> come</c><00:02:23.680><c> up</c><00:02:23.920><c> to</c><00:02:24.160><c> solve</c><00:02:24.480><c> this</c><00:02:24.720><c> problem</c>

00:02:25.670 --> 00:02:25.680 align:start position:0%
that have come up to solve this problem
 

00:02:25.680 --> 00:02:27.110 align:start position:0%
that have come up to solve this problem
one<00:02:25.840><c> of</c><00:02:25.920><c> the</c><00:02:26.000><c> biggest</c><00:02:26.319><c> at</c><00:02:26.400><c> the</c><00:02:26.480><c> moment</c><00:02:26.879><c> is</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
one of the biggest at the moment is
 

00:02:27.120 --> 00:02:29.110 align:start position:0%
one of the biggest at the moment is
vault<00:02:27.599><c> by</c><00:02:27.760><c> hashicorp</c>

00:02:29.110 --> 00:02:29.120 align:start position:0%
vault by hashicorp
 

00:02:29.120 --> 00:02:31.830 align:start position:0%
vault by hashicorp
but<00:02:29.599><c> it's</c><00:02:29.920><c> not</c><00:02:30.080><c> a</c><00:02:30.239><c> trivial</c><00:02:30.640><c> setup</c><00:02:31.200><c> and</c><00:02:31.519><c> now</c><00:02:31.680><c> you</c>

00:02:31.830 --> 00:02:31.840 align:start position:0%
but it's not a trivial setup and now you
 

00:02:31.840 --> 00:02:33.589 align:start position:0%
but it's not a trivial setup and now you
have<00:02:32.000><c> a</c><00:02:32.080><c> service</c><00:02:32.400><c> to</c><00:02:32.560><c> maintain</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
have a service to maintain
 

00:02:33.599 --> 00:02:35.670 align:start position:0%
have a service to maintain
if<00:02:33.760><c> you're</c><00:02:34.239><c> at</c><00:02:34.400><c> a</c><00:02:34.480><c> company</c><00:02:34.800><c> of</c><00:02:34.959><c> any</c><00:02:35.280><c> decent</c>

00:02:35.670 --> 00:02:35.680 align:start position:0%
if you're at a company of any decent
 

00:02:35.680 --> 00:02:37.509 align:start position:0%
if you're at a company of any decent
size<00:02:36.080><c> there's</c><00:02:36.319><c> probably</c><00:02:36.720><c> someone</c><00:02:37.040><c> to</c><00:02:37.280><c> deal</c>

00:02:37.509 --> 00:02:37.519 align:start position:0%
size there's probably someone to deal
 

00:02:37.519 --> 00:02:38.550 align:start position:0%
size there's probably someone to deal
with<00:02:37.760><c> this</c>

00:02:38.550 --> 00:02:38.560 align:start position:0%
with this
 

00:02:38.560 --> 00:02:40.630 align:start position:0%
with this
but<00:02:38.800><c> i</c><00:02:38.959><c> like</c><00:02:39.120><c> to</c><00:02:39.200><c> keep</c><00:02:39.440><c> things</c><00:02:39.760><c> simpler</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
but i like to keep things simpler
 

00:02:40.640 --> 00:02:42.869 align:start position:0%
but i like to keep things simpler
especially<00:02:41.040><c> for</c><00:02:41.200><c> my</c><00:02:41.360><c> personal</c><00:02:41.760><c> projects</c><00:02:42.720><c> now</c>

00:02:42.869 --> 00:02:42.879 align:start position:0%
especially for my personal projects now
 

00:02:42.879 --> 00:02:45.190 align:start position:0%
especially for my personal projects now
if<00:02:43.040><c> you're</c><00:02:43.200><c> just</c><00:02:43.360><c> dealing</c><00:02:43.680><c> with</c><00:02:43.920><c> aws</c><00:02:44.480><c> keys</c><00:02:45.040><c> you</c>

00:02:45.190 --> 00:02:45.200 align:start position:0%
if you're just dealing with aws keys you
 

00:02:45.200 --> 00:02:48.550 align:start position:0%
if you're just dealing with aws keys you
can<00:02:45.280><c> use</c><00:02:45.519><c> aws</c><00:02:46.160><c> vault</c><00:02:46.560><c> made</c><00:02:46.800><c> by</c><00:02:46.959><c> 99designs</c><00:02:48.319><c> and</c>

00:02:48.550 --> 00:02:48.560 align:start position:0%
can use aws vault made by 99designs and
 

00:02:48.560 --> 00:02:50.070 align:start position:0%
can use aws vault made by 99designs and
available<00:02:48.879><c> at</c><00:02:49.040><c> no</c><00:02:49.200><c> cost</c><00:02:49.440><c> on</c><00:02:49.599><c> their</c><00:02:49.680><c> github</c>

00:02:50.070 --> 00:02:50.080 align:start position:0%
available at no cost on their github
 

00:02:50.080 --> 00:02:53.509 align:start position:0%
available at no cost on their github
repo<00:02:50.879><c> but</c><00:02:51.280><c> that's</c><00:02:51.519><c> a</c><00:02:51.599><c> bit</c><00:02:51.840><c> specialized</c><00:02:52.640><c> to</c><00:02:53.200><c> one</c>

00:02:53.509 --> 00:02:53.519 align:start position:0%
repo but that's a bit specialized to one
 

00:02:53.519 --> 00:02:56.150 align:start position:0%
repo but that's a bit specialized to one
single<00:02:53.840><c> use</c><00:02:54.080><c> case</c><00:02:54.800><c> if</c><00:02:54.959><c> you're</c><00:02:55.040><c> using</c><00:02:55.440><c> aws</c><00:02:56.000><c> or</c>

00:02:56.150 --> 00:02:56.160 align:start position:0%
single use case if you're using aws or
 

00:02:56.160 --> 00:02:58.149 align:start position:0%
single use case if you're using aws or
azure<00:02:56.480><c> or</c><00:02:56.720><c> google</c><00:02:57.200><c> each</c><00:02:57.440><c> of</c><00:02:57.519><c> them</c><00:02:57.760><c> have</c><00:02:57.920><c> their</c>

00:02:58.149 --> 00:02:58.159 align:start position:0%
azure or google each of them have their
 

00:02:58.159 --> 00:03:00.390 align:start position:0%
azure or google each of them have their
own<00:02:58.319><c> secrets</c><00:02:58.800><c> manager</c><00:02:59.599><c> and</c><00:03:00.000><c> then</c><00:03:00.159><c> there</c><00:03:00.319><c> are</c>

00:03:00.390 --> 00:03:00.400 align:start position:0%
own secrets manager and then there are
 

00:03:00.400 --> 00:03:02.390 align:start position:0%
own secrets manager and then there are
the<00:03:00.640><c> dozens</c><00:03:01.040><c> of</c><00:03:01.200><c> other</c><00:03:01.440><c> startups</c><00:03:02.080><c> trying</c><00:03:02.319><c> to</c>

00:03:02.390 --> 00:03:02.400 align:start position:0%
the dozens of other startups trying to
 

00:03:02.400 --> 00:03:04.869 align:start position:0%
the dozens of other startups trying to
make<00:03:02.640><c> secrets</c><00:03:03.280><c> easier</c><00:03:03.599><c> to</c><00:03:03.680><c> work</c><00:03:04.000><c> with</c><00:03:04.640><c> i'm</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
make secrets easier to work with i'm
 

00:03:04.879 --> 00:03:06.630 align:start position:0%
make secrets easier to work with i'm
sure<00:03:05.040><c> there</c><00:03:05.200><c> are</c><00:03:05.360><c> plenty</c><00:03:05.760><c> of</c><00:03:05.920><c> choices</c><00:03:06.480><c> out</c>

00:03:06.630 --> 00:03:06.640 align:start position:0%
sure there are plenty of choices out
 

00:03:06.640 --> 00:03:08.949 align:start position:0%
sure there are plenty of choices out
there<00:03:07.120><c> that</c><00:03:07.360><c> would</c><00:03:07.519><c> be</c><00:03:07.680><c> perfect</c><00:03:08.000><c> for</c><00:03:08.159><c> me</c><00:03:08.720><c> but</c>

00:03:08.949 --> 00:03:08.959 align:start position:0%
there that would be perfect for me but
 

00:03:08.959 --> 00:03:10.790 align:start position:0%
there that would be perfect for me but
the<00:03:09.120><c> one</c><00:03:09.360><c> that</c><00:03:09.680><c> i've</c><00:03:09.920><c> been</c><00:03:10.159><c> pretty</c><00:03:10.400><c> excited</c>

00:03:10.790 --> 00:03:10.800 align:start position:0%
the one that i've been pretty excited
 

00:03:10.800 --> 00:03:12.790 align:start position:0%
the one that i've been pretty excited
about<00:03:11.040><c> for</c><00:03:11.200><c> my</c><00:03:11.360><c> personal</c><00:03:11.760><c> projects</c><00:03:12.480><c> has</c><00:03:12.640><c> been</c>

00:03:12.790 --> 00:03:12.800 align:start position:0%
about for my personal projects has been
 

00:03:12.800 --> 00:03:15.910 align:start position:0%
about for my personal projects has been
doppler<00:03:13.760><c> again</c><00:03:14.319><c> this</c><00:03:14.560><c> is</c><00:03:14.879><c> just</c><00:03:15.200><c> something</c><00:03:15.680><c> i'm</c>

00:03:15.910 --> 00:03:15.920 align:start position:0%
doppler again this is just something i'm
 

00:03:15.920 --> 00:03:17.750 align:start position:0%
doppler again this is just something i'm
using<00:03:16.159><c> for</c><00:03:16.319><c> personal</c><00:03:16.720><c> projects</c><00:03:17.120><c> and</c><00:03:17.280><c> not</c>

00:03:17.750 --> 00:03:17.760 align:start position:0%
using for personal projects and not
 

00:03:17.760 --> 00:03:19.830 align:start position:0%
using for personal projects and not
anything<00:03:18.159><c> that</c><00:03:18.319><c> i'm</c><00:03:18.480><c> using</c><00:03:19.360><c> in</c><00:03:19.599><c> any</c>

00:03:19.830 --> 00:03:19.840 align:start position:0%
anything that i'm using in any
 

00:03:19.840 --> 00:03:22.309 align:start position:0%
anything that i'm using in any
professional<00:03:20.319><c> capacity</c><00:03:21.040><c> at</c><00:03:21.200><c> datadog</c>

00:03:22.309 --> 00:03:22.319 align:start position:0%
professional capacity at datadog
 

00:03:22.319 --> 00:03:24.070 align:start position:0%
professional capacity at datadog
i<00:03:22.400><c> think</c><00:03:22.560><c> doppler</c><00:03:22.959><c> would</c><00:03:23.120><c> work</c><00:03:23.440><c> great</c><00:03:23.680><c> if</c><00:03:23.840><c> any</c>

00:03:24.070 --> 00:03:24.080 align:start position:0%
i think doppler would work great if any
 

00:03:24.080 --> 00:03:25.990 align:start position:0%
i think doppler would work great if any
company<00:03:24.480><c> chose</c><00:03:24.799><c> to</c><00:03:24.959><c> use</c><00:03:25.200><c> it</c><00:03:25.360><c> to</c><00:03:25.519><c> manage</c>

00:03:25.990 --> 00:03:26.000 align:start position:0%
company chose to use it to manage
 

00:03:26.000 --> 00:03:28.070 align:start position:0%
company chose to use it to manage
secrets<00:03:26.400><c> but</c><00:03:26.879><c> i</c><00:03:26.959><c> want</c><00:03:27.120><c> to</c><00:03:27.200><c> make</c><00:03:27.360><c> it</c><00:03:27.440><c> clear</c><00:03:27.760><c> that</c>

00:03:28.070 --> 00:03:28.080 align:start position:0%
secrets but i want to make it clear that
 

00:03:28.080 --> 00:03:30.830 align:start position:0%
secrets but i want to make it clear that
we<00:03:28.239><c> are</c><00:03:28.400><c> not</c><00:03:28.640><c> using</c><00:03:29.040><c> it</c><00:03:29.200><c> at</c><00:03:29.360><c> work</c><00:03:30.080><c> as</c><00:03:30.319><c> far</c><00:03:30.480><c> as</c><00:03:30.640><c> i</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
we are not using it at work as far as i
 

00:03:30.840 --> 00:03:33.830 align:start position:0%
we are not using it at work as far as i
know<00:03:32.080><c> setting</c><00:03:32.319><c> up</c><00:03:32.480><c> doppler</c><00:03:33.040><c> is</c><00:03:33.280><c> very</c><00:03:33.440><c> easy</c><00:03:33.680><c> to</c>

00:03:33.830 --> 00:03:33.840 align:start position:0%
know setting up doppler is very easy to
 

00:03:33.840 --> 00:03:35.830 align:start position:0%
know setting up doppler is very easy to
do<00:03:34.159><c> just</c><00:03:34.400><c> go</c><00:03:34.560><c> to</c><00:03:34.640><c> the</c><00:03:34.720><c> register</c><00:03:35.120><c> page</c><00:03:35.440><c> enter</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
do just go to the register page enter
 

00:03:35.840 --> 00:03:37.830 align:start position:0%
do just go to the register page enter
name<00:03:36.159><c> email</c><00:03:36.480><c> and</c><00:03:36.640><c> password</c><00:03:37.200><c> and</c><00:03:37.360><c> you're</c><00:03:37.599><c> ready</c>

00:03:37.830 --> 00:03:37.840 align:start position:0%
name email and password and you're ready
 

00:03:37.840 --> 00:03:38.710 align:start position:0%
name email and password and you're ready
to<00:03:38.000><c> go</c>

00:03:38.710 --> 00:03:38.720 align:start position:0%
to go
 

00:03:38.720 --> 00:03:40.630 align:start position:0%
to go
they<00:03:38.959><c> offer</c><00:03:39.280><c> two-factor</c><00:03:39.840><c> authentication</c><00:03:40.480><c> and</c>

00:03:40.630 --> 00:03:40.640 align:start position:0%
they offer two-factor authentication and
 

00:03:40.640 --> 00:03:42.710 align:start position:0%
they offer two-factor authentication and
anytime<00:03:41.280><c> any</c><00:03:41.599><c> site</c><00:03:41.840><c> has</c><00:03:42.080><c> that</c><00:03:42.239><c> you</c><00:03:42.480><c> should</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
anytime any site has that you should
 

00:03:42.720 --> 00:03:44.229 align:start position:0%
anytime any site has that you should
take<00:03:42.959><c> advantage</c><00:03:43.360><c> of</c><00:03:43.519><c> it</c>

00:03:44.229 --> 00:03:44.239 align:start position:0%
take advantage of it
 

00:03:44.239 --> 00:03:46.550 align:start position:0%
take advantage of it
so<00:03:44.560><c> once</c><00:03:44.799><c> you're</c><00:03:44.959><c> in</c><00:03:45.519><c> create</c><00:03:45.840><c> a</c><00:03:45.920><c> new</c><00:03:46.080><c> project</c>

00:03:46.550 --> 00:03:46.560 align:start position:0%
so once you're in create a new project
 

00:03:46.560 --> 00:03:48.550 align:start position:0%
so once you're in create a new project
and<00:03:46.720><c> give</c><00:03:46.959><c> it</c><00:03:47.120><c> a</c><00:03:47.360><c> relevant</c><00:03:47.760><c> name</c>

00:03:48.550 --> 00:03:48.560 align:start position:0%
and give it a relevant name
 

00:03:48.560 --> 00:03:50.470 align:start position:0%
and give it a relevant name
it<00:03:48.640><c> defaults</c><00:03:49.120><c> to</c><00:03:49.280><c> three</c><00:03:49.519><c> stages</c><00:03:49.920><c> so</c><00:03:50.159><c> you</c><00:03:50.239><c> can</c>

00:03:50.470 --> 00:03:50.480 align:start position:0%
it defaults to three stages so you can
 

00:03:50.480 --> 00:03:53.350 align:start position:0%
it defaults to three stages so you can
delete<00:03:50.959><c> or</c><00:03:51.280><c> add</c><00:03:51.599><c> however</c><00:03:52.000><c> many</c><00:03:52.319><c> you</c><00:03:52.480><c> need</c><00:03:53.120><c> i'll</c>

00:03:53.350 --> 00:03:53.360 align:start position:0%
delete or add however many you need i'll
 

00:03:53.360 --> 00:03:55.830 align:start position:0%
delete or add however many you need i'll
just<00:03:53.599><c> have</c><00:03:54.000><c> dev</c><00:03:54.400><c> and</c><00:03:54.640><c> prod</c>

00:03:55.830 --> 00:03:55.840 align:start position:0%
just have dev and prod
 

00:03:55.840 --> 00:03:57.350 align:start position:0%
just have dev and prod
so<00:03:56.080><c> the</c><00:03:56.159><c> first</c><00:03:56.319><c> thing</c><00:03:56.480><c> you</c><00:03:56.560><c> want</c><00:03:56.720><c> to</c><00:03:56.799><c> do</c><00:03:56.959><c> is</c><00:03:57.120><c> add</c>

00:03:57.350 --> 00:03:57.360 align:start position:0%
so the first thing you want to do is add
 

00:03:57.360 --> 00:04:00.309 align:start position:0%
so the first thing you want to do is add
a<00:03:57.439><c> secret</c><00:03:58.080><c> let's</c><00:03:58.319><c> add</c><00:03:58.640><c> an</c><00:03:58.879><c> api</c><00:03:59.280><c> key</c><00:03:59.920><c> all</c><00:04:00.080><c> right</c>

00:04:00.309 --> 00:04:00.319 align:start position:0%
a secret let's add an api key all right
 

00:04:00.319 --> 00:04:03.509 align:start position:0%
a secret let's add an api key all right
api<00:04:00.720><c> key</c><00:04:01.360><c> and</c><00:04:01.519><c> then</c><00:04:01.760><c> just</c><00:04:02.000><c> add</c><00:04:02.400><c> some</c><00:04:02.720><c> value</c>

00:04:03.509 --> 00:04:03.519 align:start position:0%
api key and then just add some value
 

00:04:03.519 --> 00:04:05.270 align:start position:0%
api key and then just add some value
since<00:04:03.760><c> this</c><00:04:03.920><c> is</c><00:04:04.000><c> just</c><00:04:04.239><c> a</c><00:04:04.319><c> demo</c><00:04:04.720><c> i'm</c><00:04:04.879><c> just</c><00:04:05.040><c> going</c>

00:04:05.270 --> 00:04:05.280 align:start position:0%
since this is just a demo i'm just going
 

00:04:05.280 --> 00:04:07.990 align:start position:0%
since this is just a demo i'm just going
to<00:04:05.439><c> add</c><00:04:05.680><c> some</c><00:04:05.920><c> random</c><00:04:06.319><c> text</c><00:04:06.959><c> with</c><00:04:07.280><c> dev</c><00:04:07.599><c> branch</c>

00:04:07.990 --> 00:04:08.000 align:start position:0%
to add some random text with dev branch
 

00:04:08.000 --> 00:04:09.990 align:start position:0%
to add some random text with dev branch
in<00:04:08.080><c> the</c><00:04:08.159><c> middle</c><00:04:08.720><c> and</c><00:04:08.879><c> click</c><00:04:09.120><c> save</c>

00:04:09.990 --> 00:04:10.000 align:start position:0%
in the middle and click save
 

00:04:10.000 --> 00:04:11.190 align:start position:0%
in the middle and click save
then<00:04:10.239><c> i'll</c><00:04:10.319><c> go</c><00:04:10.480><c> over</c><00:04:10.640><c> to</c><00:04:10.799><c> the</c><00:04:10.879><c> prod</c>

00:04:11.190 --> 00:04:11.200 align:start position:0%
then i'll go over to the prod
 

00:04:11.200 --> 00:04:13.190 align:start position:0%
then i'll go over to the prod
environment<00:04:11.680><c> and</c><00:04:11.840><c> update</c><00:04:12.159><c> the</c><00:04:12.319><c> api</c><00:04:12.720><c> key</c><00:04:12.959><c> to</c><00:04:13.040><c> be</c>

00:04:13.190 --> 00:04:13.200 align:start position:0%
environment and update the api key to be
 

00:04:13.200 --> 00:04:15.910 align:start position:0%
environment and update the api key to be
something<00:04:14.080><c> for</c><00:04:14.319><c> prod</c><00:04:14.720><c> branch</c><00:04:15.280><c> and</c><00:04:15.439><c> save</c><00:04:15.680><c> that</c>

00:04:15.910 --> 00:04:15.920 align:start position:0%
something for prod branch and save that
 

00:04:15.920 --> 00:04:17.030 align:start position:0%
something for prod branch and save that
too

00:04:17.030 --> 00:04:17.040 align:start position:0%
too
 

00:04:17.040 --> 00:04:18.949 align:start position:0%
too
now<00:04:17.519><c> let's</c><00:04:17.759><c> create</c><00:04:18.079><c> a</c><00:04:18.160><c> test</c><00:04:18.479><c> program</c><00:04:18.799><c> to</c>

00:04:18.949 --> 00:04:18.959 align:start position:0%
now let's create a test program to
 

00:04:18.959 --> 00:04:21.270 align:start position:0%
now let's create a test program to
verify<00:04:19.440><c> that</c><00:04:19.600><c> things</c><00:04:19.840><c> are</c><00:04:19.919><c> working</c><00:04:20.799><c> my</c><00:04:21.040><c> test</c>

00:04:21.270 --> 00:04:21.280 align:start position:0%
verify that things are working my test
 

00:04:21.280 --> 00:04:22.710 align:start position:0%
verify that things are working my test
program<00:04:21.600><c> is</c><00:04:21.680><c> just</c><00:04:21.840><c> going</c><00:04:21.919><c> to</c><00:04:22.079><c> spit</c><00:04:22.400><c> out</c><00:04:22.479><c> the</c>

00:04:22.710 --> 00:04:22.720 align:start position:0%
program is just going to spit out the
 

00:04:22.720 --> 00:04:24.790 align:start position:0%
program is just going to spit out the
api<00:04:23.120><c> key</c><00:04:23.440><c> environment</c><00:04:23.919><c> variable</c>

00:04:24.790 --> 00:04:24.800 align:start position:0%
api key environment variable
 

00:04:24.800 --> 00:04:26.310 align:start position:0%
api key environment variable
now<00:04:25.040><c> normally</c><00:04:25.440><c> this</c><00:04:25.600><c> would</c><00:04:25.759><c> be</c><00:04:25.919><c> something</c>

00:04:26.310 --> 00:04:26.320 align:start position:0%
now normally this would be something
 

00:04:26.320 --> 00:04:29.270 align:start position:0%
now normally this would be something
written<00:04:26.639><c> in</c><00:04:26.720><c> ruby</c><00:04:27.120><c> or</c><00:04:27.360><c> c</c><00:04:27.600><c> or</c><00:04:27.840><c> go</c><00:04:28.160><c> or</c><00:04:28.400><c> python</c><00:04:28.880><c> or</c>

00:04:29.270 --> 00:04:29.280 align:start position:0%
written in ruby or c or go or python or
 

00:04:29.280 --> 00:04:31.350 align:start position:0%
written in ruby or c or go or python or
javascript<00:04:29.840><c> or</c><00:04:30.240><c> i</c><00:04:30.320><c> like</c><00:04:30.479><c> to</c><00:04:30.639><c> use</c><00:04:30.800><c> typescript</c>

00:04:31.350 --> 00:04:31.360 align:start position:0%
javascript or i like to use typescript
 

00:04:31.360 --> 00:04:33.110 align:start position:0%
javascript or i like to use typescript
with<00:04:31.520><c> dino</c>

00:04:33.110 --> 00:04:33.120 align:start position:0%
with dino
 

00:04:33.120 --> 00:04:35.270 align:start position:0%
with dino
but<00:04:33.280><c> for</c><00:04:33.520><c> this</c><00:04:33.759><c> example</c><00:04:34.560><c> i'm</c><00:04:34.720><c> just</c><00:04:34.960><c> going</c><00:04:35.199><c> to</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
but for this example i'm just going to
 

00:04:35.280 --> 00:04:37.830 align:start position:0%
but for this example i'm just going to
make<00:04:35.680><c> a</c><00:04:36.000><c> shell</c><00:04:36.240><c> script</c><00:04:36.800><c> so</c><00:04:37.120><c> vim</c><00:04:37.440><c> testing</c>

00:04:37.830 --> 00:04:37.840 align:start position:0%
make a shell script so vim testing
 

00:04:37.840 --> 00:04:40.230 align:start position:0%
make a shell script so vim testing
doppler<00:04:38.560><c> i'll</c><00:04:38.800><c> add</c><00:04:38.960><c> a</c><00:04:39.040><c> hashbang</c><00:04:39.919><c> and</c><00:04:40.000><c> then</c>

00:04:40.230 --> 00:04:40.240 align:start position:0%
doppler i'll add a hashbang and then
 

00:04:40.240 --> 00:04:42.629 align:start position:0%
doppler i'll add a hashbang and then
echo<00:04:40.880><c> this</c><00:04:41.120><c> is</c><00:04:41.280><c> my</c><00:04:41.520><c> cool</c><00:04:41.840><c> testing</c><00:04:42.320><c> doppler</c>

00:04:42.629 --> 00:04:42.639 align:start position:0%
echo this is my cool testing doppler
 

00:04:42.639 --> 00:04:44.870 align:start position:0%
echo this is my cool testing doppler
program<00:04:43.199><c> and</c><00:04:43.280><c> then</c><00:04:43.440><c> echo</c><00:04:43.840><c> dollar</c><00:04:44.160><c> sign</c><00:04:44.560><c> api</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
program and then echo dollar sign api
 

00:04:44.880 --> 00:04:46.550 align:start position:0%
program and then echo dollar sign api
key<00:04:45.360><c> set</c><00:04:45.520><c> the</c><00:04:45.600><c> permissions</c><00:04:46.080><c> to</c><00:04:46.240><c> allow</c>

00:04:46.550 --> 00:04:46.560 align:start position:0%
key set the permissions to allow
 

00:04:46.560 --> 00:04:48.790 align:start position:0%
key set the permissions to allow
execution<00:04:47.040><c> on</c><00:04:47.120><c> the</c><00:04:47.199><c> script</c><00:04:48.080><c> and</c><00:04:48.320><c> i</c><00:04:48.400><c> have</c><00:04:48.560><c> a</c><00:04:48.639><c> bin</c>

00:04:48.790 --> 00:04:48.800 align:start position:0%
execution on the script and i have a bin
 

00:04:48.800 --> 00:04:50.950 align:start position:0%
execution on the script and i have a bin
directory<00:04:49.280><c> of</c><00:04:49.440><c> my</c><00:04:49.680><c> home</c><00:04:50.080><c> that</c><00:04:50.320><c> is</c><00:04:50.560><c> already</c><00:04:50.880><c> in</c>

00:04:50.950 --> 00:04:50.960 align:start position:0%
directory of my home that is already in
 

00:04:50.960 --> 00:04:53.350 align:start position:0%
directory of my home that is already in
my<00:04:51.120><c> path</c><00:04:51.360><c> so</c><00:04:51.520><c> i'll</c><00:04:51.680><c> just</c><00:04:52.240><c> shove</c><00:04:52.479><c> it</c><00:04:52.639><c> over</c><00:04:52.800><c> there</c>

00:04:53.350 --> 00:04:53.360 align:start position:0%
my path so i'll just shove it over there
 

00:04:53.360 --> 00:04:55.350 align:start position:0%
my path so i'll just shove it over there
now<00:04:53.680><c> run</c><00:04:54.000><c> testing</c><00:04:54.400><c> doppler</c><00:04:54.800><c> and</c><00:04:54.880><c> i</c><00:04:55.040><c> see</c><00:04:55.199><c> no</c>

00:04:55.350 --> 00:04:55.360 align:start position:0%
now run testing doppler and i see no
 

00:04:55.360 --> 00:04:56.390 align:start position:0%
now run testing doppler and i see no
results

00:04:56.390 --> 00:04:56.400 align:start position:0%
results
 

00:04:56.400 --> 00:04:57.830 align:start position:0%
results
okay<00:04:56.960><c> because</c><00:04:57.120><c> i</c><00:04:57.199><c> have</c><00:04:57.360><c> nothing</c><00:04:57.600><c> in</c><00:04:57.680><c> my</c>

00:04:57.830 --> 00:04:57.840 align:start position:0%
okay because i have nothing in my
 

00:04:57.840 --> 00:04:59.670 align:start position:0%
okay because i have nothing in my
environment<00:04:58.240><c> variables</c><00:04:58.880><c> but</c><00:04:59.120><c> if</c><00:04:59.280><c> i</c><00:04:59.360><c> run</c><00:04:59.520><c> the</c>

00:04:59.670 --> 00:04:59.680 align:start position:0%
environment variables but if i run the
 

00:04:59.680 --> 00:05:01.670 align:start position:0%
environment variables but if i run the
doppler<00:05:00.000><c> command</c><00:05:00.479><c> first</c><00:05:01.199><c> we'll</c><00:05:01.360><c> see</c><00:05:01.520><c> some</c>

00:05:01.670 --> 00:05:01.680 align:start position:0%
doppler command first we'll see some
 

00:05:01.680 --> 00:05:02.870 align:start position:0%
doppler command first we'll see some
magic

00:05:02.870 --> 00:05:02.880 align:start position:0%
magic
 

00:05:02.880 --> 00:05:05.990 align:start position:0%
magic
doppler<00:05:03.360><c> run</c><00:05:04.000><c> dash</c><00:05:04.320><c> p</c><00:05:04.800><c> for</c><00:05:05.039><c> project</c><00:05:05.680><c> and</c><00:05:05.759><c> then</c>

00:05:05.990 --> 00:05:06.000 align:start position:0%
doppler run dash p for project and then
 

00:05:06.000 --> 00:05:08.390 align:start position:0%
doppler run dash p for project and then
dash<00:05:06.400><c> c</c><00:05:06.880><c> for</c><00:05:07.039><c> config</c><00:05:07.600><c> or</c><00:05:07.840><c> stage</c><00:05:08.240><c> or</c>

00:05:08.390 --> 00:05:08.400 align:start position:0%
dash c for config or stage or
 

00:05:08.400 --> 00:05:10.390 align:start position:0%
dash c for config or stage or
environment<00:05:09.039><c> which</c><00:05:09.280><c> is</c><00:05:09.440><c> dev</c>

00:05:10.390 --> 00:05:10.400 align:start position:0%
environment which is dev
 

00:05:10.400 --> 00:05:12.469 align:start position:0%
environment which is dev
then<00:05:10.639><c> two</c><00:05:10.880><c> dashes</c><00:05:11.360><c> and</c><00:05:11.600><c> whatever</c><00:05:12.000><c> command</c><00:05:12.320><c> we</c>

00:05:12.469 --> 00:05:12.479 align:start position:0%
then two dashes and whatever command we
 

00:05:12.479 --> 00:05:13.749 align:start position:0%
then two dashes and whatever command we
want<00:05:12.720><c> to</c><00:05:12.880><c> run</c>

00:05:13.749 --> 00:05:13.759 align:start position:0%
want to run
 

00:05:13.759 --> 00:05:16.710 align:start position:0%
want to run
that<00:05:13.919><c> could</c><00:05:14.080><c> be</c><00:05:14.240><c> your</c><00:05:14.560><c> dino</c><00:05:14.880><c> run</c><00:05:15.120><c> command</c><00:05:15.520><c> or</c>

00:05:16.710 --> 00:05:16.720 align:start position:0%
that could be your dino run command or
 

00:05:16.720 --> 00:05:18.230 align:start position:0%
that could be your dino run command or
maybe<00:05:17.120><c> the</c>

00:05:18.230 --> 00:05:18.240 align:start position:0%
maybe the
 

00:05:18.240 --> 00:05:21.830 align:start position:0%
maybe the
npm<00:05:19.199><c> run</c><00:05:19.520><c> script</c><00:05:20.080><c> or</c><00:05:20.400><c> in</c><00:05:20.560><c> our</c><00:05:20.880><c> case</c><00:05:21.440><c> testing</c>

00:05:21.830 --> 00:05:21.840 align:start position:0%
npm run script or in our case testing
 

00:05:21.840 --> 00:05:24.550 align:start position:0%
npm run script or in our case testing
doppler<00:05:22.560><c> and</c><00:05:22.639><c> now</c><00:05:22.800><c> we</c><00:05:22.960><c> see</c><00:05:23.120><c> the</c><00:05:23.199><c> output</c>

00:05:24.550 --> 00:05:24.560 align:start position:0%
doppler and now we see the output
 

00:05:24.560 --> 00:05:26.310 align:start position:0%
doppler and now we see the output
change<00:05:24.800><c> the</c><00:05:24.960><c> environment</c><00:05:25.440><c> to</c><00:05:25.680><c> prd</c><00:05:26.160><c> for</c>

00:05:26.310 --> 00:05:26.320 align:start position:0%
change the environment to prd for
 

00:05:26.320 --> 00:05:28.550 align:start position:0%
change the environment to prd for
production<00:05:27.039><c> and</c><00:05:27.280><c> we</c><00:05:27.440><c> see</c><00:05:27.600><c> our</c><00:05:27.759><c> production</c><00:05:28.240><c> api</c>

00:05:28.550 --> 00:05:28.560 align:start position:0%
production and we see our production api
 

00:05:28.560 --> 00:05:29.430 align:start position:0%
production and we see our production api
key

00:05:29.430 --> 00:05:29.440 align:start position:0%
key
 

00:05:29.440 --> 00:05:31.590 align:start position:0%
key
now<00:05:29.680><c> this</c><00:05:29.919><c> works</c><00:05:30.240><c> because</c><00:05:30.800><c> when</c><00:05:31.039><c> i</c><00:05:31.120><c> set</c><00:05:31.360><c> up</c><00:05:31.440><c> the</c>

00:05:31.590 --> 00:05:31.600 align:start position:0%
now this works because when i set up the
 

00:05:31.600 --> 00:05:34.870 align:start position:0%
now this works because when i set up the
doppler<00:05:32.000><c> cli</c><00:05:32.960><c> i</c><00:05:33.120><c> logged</c><00:05:33.520><c> in</c><00:05:33.840><c> as</c><00:05:34.160><c> me</c>

00:05:34.870 --> 00:05:34.880 align:start position:0%
doppler cli i logged in as me
 

00:05:34.880 --> 00:05:36.950 align:start position:0%
doppler cli i logged in as me
but<00:05:35.120><c> when</c><00:05:35.280><c> you</c><00:05:35.440><c> deploy</c><00:05:36.000><c> you</c><00:05:36.320><c> don't</c><00:05:36.560><c> really</c>

00:05:36.950 --> 00:05:36.960 align:start position:0%
but when you deploy you don't really
 

00:05:36.960 --> 00:05:38.790 align:start position:0%
but when you deploy you don't really
want<00:05:37.120><c> to</c><00:05:37.360><c> hard</c><00:05:37.600><c> code</c><00:05:37.919><c> your</c><00:05:38.080><c> username</c><00:05:38.639><c> and</c>

00:05:38.790 --> 00:05:38.800 align:start position:0%
want to hard code your username and
 

00:05:38.800 --> 00:05:40.629 align:start position:0%
want to hard code your username and
password<00:05:39.360><c> in</c><00:05:39.600><c> whatever</c><00:05:40.000><c> platform</c><00:05:40.479><c> you're</c>

00:05:40.629 --> 00:05:40.639 align:start position:0%
password in whatever platform you're
 

00:05:40.639 --> 00:05:43.029 align:start position:0%
password in whatever platform you're
using<00:05:41.440><c> so</c><00:05:41.600><c> you</c><00:05:41.680><c> can</c><00:05:41.840><c> create</c><00:05:42.160><c> a</c><00:05:42.240><c> service</c><00:05:42.560><c> token</c>

00:05:43.029 --> 00:05:43.039 align:start position:0%
using so you can create a service token
 

00:05:43.039 --> 00:05:45.430 align:start position:0%
using so you can create a service token
for<00:05:43.280><c> each</c><00:05:43.520><c> stage</c><00:05:44.160><c> and</c><00:05:44.320><c> then</c><00:05:44.479><c> use</c><00:05:44.720><c> that</c><00:05:45.039><c> in</c><00:05:45.120><c> your</c>

00:05:45.430 --> 00:05:45.440 align:start position:0%
for each stage and then use that in your
 

00:05:45.440 --> 00:05:47.590 align:start position:0%
for each stage and then use that in your
kubernetes<00:05:46.000><c> deployment</c><00:05:46.479><c> manifest</c><00:05:47.039><c> or</c><00:05:47.199><c> docker</c>

00:05:47.590 --> 00:05:47.600 align:start position:0%
kubernetes deployment manifest or docker
 

00:05:47.600 --> 00:05:50.310 align:start position:0%
kubernetes deployment manifest or docker
yam<00:05:47.919><c> or</c><00:05:48.400><c> or</c><00:05:48.639><c> cloud</c><00:05:48.880><c> configuration</c>

00:05:50.310 --> 00:05:50.320 align:start position:0%
yam or or cloud configuration
 

00:05:50.320 --> 00:05:52.790 align:start position:0%
yam or or cloud configuration
there<00:05:50.560><c> are</c><00:05:50.800><c> also</c><00:05:51.120><c> integrations</c><00:05:51.680><c> with</c><00:05:51.919><c> aws</c><00:05:52.639><c> and</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
there are also integrations with aws and
 

00:05:52.800 --> 00:05:55.590 align:start position:0%
there are also integrations with aws and
netlify<00:05:53.360><c> and</c><00:05:53.520><c> cloudflare</c><00:05:54.080><c> and</c><00:05:54.160><c> gcp</c><00:05:54.880><c> firebase</c>

00:05:55.590 --> 00:05:55.600 align:start position:0%
netlify and cloudflare and gcp firebase
 

00:05:55.600 --> 00:05:57.990 align:start position:0%
netlify and cloudflare and gcp firebase
heroku<00:05:56.319><c> and</c><00:05:56.639><c> and</c><00:05:56.800><c> more</c><00:05:57.120><c> where</c><00:05:57.360><c> doppler</c><00:05:57.840><c> will</c>

00:05:57.990 --> 00:05:58.000 align:start position:0%
heroku and and more where doppler will
 

00:05:58.000 --> 00:06:00.469 align:start position:0%
heroku and and more where doppler will
sync<00:05:58.400><c> its</c><00:05:58.639><c> secrets</c><00:05:59.280><c> with</c><00:05:59.440><c> those</c><00:05:59.759><c> platforms</c>

00:06:00.469 --> 00:06:00.479 align:start position:0%
sync its secrets with those platforms
 

00:06:00.479 --> 00:06:02.710 align:start position:0%
sync its secrets with those platforms
and<00:06:00.639><c> you</c><00:06:00.720><c> can</c><00:06:01.039><c> ask</c><00:06:01.280><c> for</c><00:06:01.440><c> secrets</c><00:06:01.919><c> in</c><00:06:02.160><c> a</c><00:06:02.319><c> native</c>

00:06:02.710 --> 00:06:02.720 align:start position:0%
and you can ask for secrets in a native
 

00:06:02.720 --> 00:06:05.110 align:start position:0%
and you can ask for secrets in a native
way<00:06:03.120><c> in</c><00:06:03.199><c> your</c><00:06:03.360><c> projects</c>

00:06:05.110 --> 00:06:05.120 align:start position:0%
way in your projects
 

00:06:05.120 --> 00:06:07.830 align:start position:0%
way in your projects
if<00:06:05.280><c> you're</c><00:06:05.440><c> using</c><00:06:05.840><c> pro</c><00:06:06.560><c> which</c><00:06:06.800><c> costs</c><00:06:07.120><c> 18</c><00:06:07.520><c> bucks</c>

00:06:07.830 --> 00:06:07.840 align:start position:0%
if you're using pro which costs 18 bucks
 

00:06:07.840 --> 00:06:10.070 align:start position:0%
if you're using pro which costs 18 bucks
a<00:06:07.919><c> month</c><00:06:08.560><c> you</c><00:06:08.720><c> can</c><00:06:08.880><c> also</c><00:06:09.199><c> use</c><00:06:09.440><c> web</c><00:06:09.600><c> hooks</c><00:06:09.919><c> which</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
a month you can also use web hooks which
 

00:06:10.080 --> 00:06:12.230 align:start position:0%
a month you can also use web hooks which
means<00:06:10.400><c> you</c><00:06:10.560><c> can</c><00:06:10.720><c> pass</c><00:06:11.039><c> the</c><00:06:11.199><c> secrets</c><00:06:11.680><c> to</c><00:06:11.919><c> your</c>

00:06:12.230 --> 00:06:12.240 align:start position:0%
means you can pass the secrets to your
 

00:06:12.240 --> 00:06:15.350 align:start position:0%
means you can pass the secrets to your
github<00:06:12.639><c> actions</c><00:06:13.039><c> or</c><00:06:13.280><c> ci</c><00:06:13.600><c> cd</c><00:06:14.000><c> pipeline</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
github actions or ci cd pipeline
 

00:06:15.360 --> 00:06:16.870 align:start position:0%
github actions or ci cd pipeline
if<00:06:15.440><c> you're</c><00:06:15.600><c> working</c><00:06:15.840><c> with</c><00:06:16.080><c> other</c><00:06:16.319><c> folks</c><00:06:16.720><c> you</c>

00:06:16.870 --> 00:06:16.880 align:start position:0%
if you're working with other folks you
 

00:06:16.880 --> 00:06:18.550 align:start position:0%
if you're working with other folks you
can<00:06:17.039><c> add</c><00:06:17.199><c> them</c><00:06:17.360><c> to</c><00:06:17.440><c> your</c><00:06:17.600><c> team</c><00:06:18.080><c> to</c><00:06:18.319><c> share</c>

00:06:18.550 --> 00:06:18.560 align:start position:0%
can add them to your team to share
 

00:06:18.560 --> 00:06:20.629 align:start position:0%
can add them to your team to share
secrets<00:06:18.960><c> with</c><00:06:19.039><c> them</c><00:06:19.919><c> now</c><00:06:20.080><c> one</c><00:06:20.240><c> of</c><00:06:20.319><c> the</c><00:06:20.400><c> things</c>

00:06:20.629 --> 00:06:20.639 align:start position:0%
secrets with them now one of the things
 

00:06:20.639 --> 00:06:22.790 align:start position:0%
secrets with them now one of the things
i<00:06:20.800><c> think</c><00:06:20.960><c> is</c><00:06:21.199><c> really</c><00:06:21.600><c> cool</c><00:06:21.919><c> is</c><00:06:22.000><c> the</c><00:06:22.160><c> ability</c><00:06:22.560><c> to</c>

00:06:22.790 --> 00:06:22.800 align:start position:0%
i think is really cool is the ability to
 

00:06:22.800 --> 00:06:24.950 align:start position:0%
i think is really cool is the ability to
share<00:06:23.039><c> a</c><00:06:23.120><c> secret</c><00:06:23.759><c> with</c><00:06:24.000><c> anyone</c>

00:06:24.950 --> 00:06:24.960 align:start position:0%
share a secret with anyone
 

00:06:24.960 --> 00:06:26.390 align:start position:0%
share a secret with anyone
sometimes<00:06:25.360><c> you</c><00:06:25.520><c> just</c><00:06:25.680><c> need</c><00:06:25.840><c> to</c><00:06:26.000><c> share</c><00:06:26.240><c> a</c>

00:06:26.390 --> 00:06:26.400 align:start position:0%
sometimes you just need to share a
 

00:06:26.400 --> 00:06:28.070 align:start position:0%
sometimes you just need to share a
password<00:06:26.960><c> with</c><00:06:27.199><c> someone</c>

00:06:28.070 --> 00:06:28.080 align:start position:0%
password with someone
 

00:06:28.080 --> 00:06:29.430 align:start position:0%
password with someone
and<00:06:28.160><c> when</c><00:06:28.400><c> they</c><00:06:28.560><c> see</c><00:06:28.800><c> it</c><00:06:28.880><c> you</c><00:06:29.039><c> don't</c><00:06:29.199><c> want</c>

00:06:29.430 --> 00:06:29.440 align:start position:0%
and when they see it you don't want
 

00:06:29.440 --> 00:06:32.189 align:start position:0%
and when they see it you don't want
anyone<00:06:29.840><c> else</c><00:06:30.000><c> to</c><00:06:30.160><c> see</c><00:06:30.319><c> it</c><00:06:30.960><c> so</c><00:06:31.280><c> go</c><00:06:31.440><c> to</c>

00:06:32.189 --> 00:06:32.199 align:start position:0%
anyone else to see it so go to
 

00:06:32.199 --> 00:06:34.550 align:start position:0%
anyone else to see it so go to
share.doppler.com<00:06:33.520><c> and</c><00:06:33.680><c> you</c><00:06:33.759><c> can</c><00:06:34.000><c> enter</c><00:06:34.400><c> a</c>

00:06:34.550 --> 00:06:34.560 align:start position:0%
share.doppler.com and you can enter a
 

00:06:34.560 --> 00:06:37.350 align:start position:0%
share.doppler.com and you can enter a
secret<00:06:35.360><c> and</c><00:06:35.440><c> then</c><00:06:35.680><c> chose</c><00:06:36.000><c> to</c><00:06:36.080><c> have</c><00:06:36.319><c> it</c><00:06:36.560><c> expire</c>

00:06:37.350 --> 00:06:37.360 align:start position:0%
secret and then chose to have it expire
 

00:06:37.360 --> 00:06:40.870 align:start position:0%
secret and then chose to have it expire
after<00:06:37.919><c> a</c><00:06:38.080><c> single</c><00:06:38.479><c> day</c><00:06:38.880><c> or</c><00:06:39.360><c> even</c><00:06:39.600><c> a</c><00:06:39.680><c> single</c><00:06:40.080><c> view</c>

00:06:40.870 --> 00:06:40.880 align:start position:0%
after a single day or even a single view
 

00:06:40.880 --> 00:06:43.110 align:start position:0%
after a single day or even a single view
i<00:06:41.199><c> love</c><00:06:41.440><c> this</c><00:06:41.680><c> feature</c><00:06:42.160><c> and</c><00:06:42.319><c> it</c><00:06:42.400><c> works</c><00:06:42.800><c> even</c><00:06:43.039><c> if</c>

00:06:43.110 --> 00:06:43.120 align:start position:0%
i love this feature and it works even if
 

00:06:43.120 --> 00:06:45.830 align:start position:0%
i love this feature and it works even if
you<00:06:43.360><c> never</c><00:06:43.600><c> become</c><00:06:44.160><c> a</c><00:06:44.319><c> customer</c><00:06:45.199><c> now</c>

00:06:45.830 --> 00:06:45.840 align:start position:0%
you never become a customer now
 

00:06:45.840 --> 00:06:48.230 align:start position:0%
you never become a customer now
obviously<00:06:46.720><c> if</c><00:06:46.880><c> you're</c><00:06:47.120><c> using</c><00:06:47.520><c> a</c><00:06:47.600><c> company</c><00:06:48.000><c> like</c>

00:06:48.230 --> 00:06:48.240 align:start position:0%
obviously if you're using a company like
 

00:06:48.240 --> 00:06:50.390 align:start position:0%
obviously if you're using a company like
doppler<00:06:48.639><c> to</c><00:06:48.800><c> manage</c><00:06:49.199><c> your</c><00:06:49.360><c> secrets</c><00:06:50.080><c> you</c><00:06:50.240><c> need</c>

00:06:50.390 --> 00:06:50.400 align:start position:0%
doppler to manage your secrets you need
 

00:06:50.400 --> 00:06:52.390 align:start position:0%
doppler to manage your secrets you need
to<00:06:50.560><c> trust</c><00:06:50.960><c> them</c><00:06:51.520><c> i</c><00:06:51.680><c> encourage</c><00:06:52.000><c> you</c><00:06:52.160><c> to</c><00:06:52.240><c> read</c>

00:06:52.390 --> 00:06:52.400 align:start position:0%
to trust them i encourage you to read
 

00:06:52.400 --> 00:06:53.990 align:start position:0%
to trust them i encourage you to read
their<00:06:52.639><c> security</c><00:06:53.120><c> documentation</c><00:06:53.759><c> at</c><00:06:53.840><c> the</c>

00:06:53.990 --> 00:06:54.000 align:start position:0%
their security documentation at the
 

00:06:54.000 --> 00:06:56.469 align:start position:0%
their security documentation at the
bottom<00:06:54.319><c> of</c><00:06:54.639><c> every</c><00:06:54.960><c> page</c><00:06:55.599><c> to</c><00:06:55.759><c> understand</c><00:06:56.319><c> how</c>

00:06:56.469 --> 00:06:56.479 align:start position:0%
bottom of every page to understand how
 

00:06:56.479 --> 00:06:58.390 align:start position:0%
bottom of every page to understand how
they're<00:06:56.800><c> storing</c><00:06:57.120><c> your</c><00:06:57.360><c> secrets</c><00:06:58.080><c> it's</c>

00:06:58.390 --> 00:06:58.400 align:start position:0%
they're storing your secrets it's
 

00:06:58.400 --> 00:07:00.150 align:start position:0%
they're storing your secrets it's
actually<00:06:58.720><c> really</c><00:06:59.039><c> well</c><00:06:59.280><c> written</c><00:06:59.599><c> and</c><00:06:59.840><c> pretty</c>

00:07:00.150 --> 00:07:00.160 align:start position:0%
actually really well written and pretty
 

00:07:00.160 --> 00:07:02.710 align:start position:0%
actually really well written and pretty
easy<00:07:00.479><c> to</c><00:07:00.560><c> follow</c><00:07:01.360><c> in</c><00:07:01.520><c> fact</c><00:07:01.919><c> all</c><00:07:02.160><c> their</c><00:07:02.319><c> docs</c>

00:07:02.710 --> 00:07:02.720 align:start position:0%
easy to follow in fact all their docs
 

00:07:02.720 --> 00:07:04.070 align:start position:0%
easy to follow in fact all their docs
are<00:07:02.880><c> really</c><00:07:03.120><c> useful</c>

00:07:04.070 --> 00:07:04.080 align:start position:0%
are really useful
 

00:07:04.080 --> 00:07:05.670 align:start position:0%
are really useful
i<00:07:04.160><c> think</c><00:07:04.319><c> it's</c><00:07:04.479><c> a</c><00:07:04.560><c> great</c><00:07:04.720><c> tool</c><00:07:05.120><c> and</c><00:07:05.360><c> i</c><00:07:05.520><c> will</c>

00:07:05.670 --> 00:07:05.680 align:start position:0%
i think it's a great tool and i will
 

00:07:05.680 --> 00:07:07.589 align:start position:0%
i think it's a great tool and i will
continue<00:07:06.160><c> to</c><00:07:06.240><c> keep</c><00:07:06.479><c> paying</c><00:07:06.800><c> for</c><00:07:06.960><c> it</c><00:07:07.360><c> but</c>

00:07:07.589 --> 00:07:07.599 align:start position:0%
continue to keep paying for it but
 

00:07:07.599 --> 00:07:10.390 align:start position:0%
continue to keep paying for it but
remember<00:07:08.000><c> if</c><00:07:08.240><c> you</c><00:07:08.560><c> sign</c><00:07:08.880><c> up</c><00:07:09.120><c> using</c><00:07:09.759><c> this</c><00:07:10.080><c> offer</c>

00:07:10.390 --> 00:07:10.400 align:start position:0%
remember if you sign up using this offer
 

00:07:10.400 --> 00:07:12.870 align:start position:0%
remember if you sign up using this offer
code<00:07:11.039><c> you</c><00:07:11.199><c> get</c><00:07:11.360><c> a</c><00:07:11.440><c> hundred</c><00:07:11.680><c> dollar</c><00:07:12.000><c> credit</c><00:07:12.720><c> at</c>

00:07:12.870 --> 00:07:12.880 align:start position:0%
code you get a hundred dollar credit at
 

00:07:12.880 --> 00:07:15.110 align:start position:0%
code you get a hundred dollar credit at
least<00:07:13.199><c> at</c><00:07:13.360><c> the</c><00:07:13.440><c> time</c><00:07:13.680><c> of</c><00:07:13.840><c> recording</c>

00:07:15.110 --> 00:07:15.120 align:start position:0%
least at the time of recording
 

00:07:15.120 --> 00:07:17.749 align:start position:0%
least at the time of recording
to<00:07:15.280><c> play</c><00:07:15.440><c> around</c><00:07:15.680><c> with</c><00:07:15.840><c> it</c><00:07:16.160><c> and</c><00:07:16.720><c> and</c><00:07:16.880><c> i</c><00:07:17.039><c> get</c><00:07:17.199><c> 100</c>

00:07:17.749 --> 00:07:17.759 align:start position:0%
to play around with it and and i get 100
 

00:07:17.759 --> 00:07:20.870 align:start position:0%
to play around with it and and i get 100
credit<00:07:18.080><c> too</c><00:07:18.479><c> so</c><00:07:18.960><c> hey</c><00:07:19.199><c> we</c><00:07:19.440><c> all</c><00:07:19.599><c> win</c>

00:07:20.870 --> 00:07:20.880 align:start position:0%
credit too so hey we all win
 

00:07:20.880 --> 00:07:22.710 align:start position:0%
credit too so hey we all win
if<00:07:21.039><c> you</c><00:07:21.199><c> start</c><00:07:21.440><c> using</c><00:07:21.759><c> it</c><00:07:22.000><c> i'd</c><00:07:22.160><c> love</c><00:07:22.319><c> to</c><00:07:22.479><c> hear</c>

00:07:22.710 --> 00:07:22.720 align:start position:0%
if you start using it i'd love to hear
 

00:07:22.720 --> 00:07:24.710 align:start position:0%
if you start using it i'd love to hear
about<00:07:22.960><c> it</c><00:07:23.120><c> in</c><00:07:23.199><c> the</c><00:07:23.280><c> comments</c><00:07:23.680><c> below</c><00:07:24.479><c> if</c><00:07:24.560><c> you</c>

00:07:24.710 --> 00:07:24.720 align:start position:0%
about it in the comments below if you
 

00:07:24.720 --> 00:07:26.390 align:start position:0%
about it in the comments below if you
find<00:07:24.960><c> another</c><00:07:25.280><c> tool</c><00:07:25.520><c> to</c><00:07:25.680><c> be</c><00:07:25.840><c> better</c><00:07:26.080><c> for</c><00:07:26.240><c> you</c>

00:07:26.390 --> 00:07:26.400 align:start position:0%
find another tool to be better for you
 

00:07:26.400 --> 00:07:28.710 align:start position:0%
find another tool to be better for you
show<00:07:26.639><c> that</c><00:07:26.960><c> in</c><00:07:27.039><c> the</c><00:07:27.199><c> comments</c><00:07:27.599><c> too</c>

00:07:28.710 --> 00:07:28.720 align:start position:0%
show that in the comments too
 

00:07:28.720 --> 00:07:30.870 align:start position:0%
show that in the comments too
and<00:07:29.039><c> that</c><00:07:29.280><c> does</c><00:07:29.520><c> it</c><00:07:29.599><c> for</c><00:07:29.759><c> me</c><00:07:30.000><c> for</c><00:07:30.160><c> this</c><00:07:30.319><c> video</c>

00:07:30.870 --> 00:07:30.880 align:start position:0%
and that does it for me for this video
 

00:07:30.880 --> 00:07:33.860 align:start position:0%
and that does it for me for this video
thank<00:07:31.120><c> you</c><00:07:31.360><c> so</c><00:07:31.520><c> much</c><00:07:31.759><c> for</c><00:07:31.919><c> watching</c><00:07:32.560><c> goodbye</c>

00:07:33.860 --> 00:07:33.870 align:start position:0%
thank you so much for watching goodbye
 

00:07:33.870 --> 00:07:57.430 align:start position:0%
thank you so much for watching goodbye
[Music]

00:07:57.430 --> 00:07:57.440 align:start position:0%
 
 

00:07:57.440 --> 00:07:59.520 align:start position:0%
 
you

