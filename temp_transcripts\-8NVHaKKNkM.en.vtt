WEBVTT
Kind: captions
Language: en

00:00:00.799 --> 00:00:02.310 align:start position:0%
 
hey<00:00:00.960><c> everyone</c><00:00:01.240><c> it's</c><00:00:01.439><c> ashit</c><00:00:01.880><c> and</c><00:00:02.000><c> today</c><00:00:02.200><c> I'll</c>

00:00:02.310 --> 00:00:02.320 align:start position:0%
hey everyone it's ashit and today I'll
 

00:00:02.320 --> 00:00:04.349 align:start position:0%
hey everyone it's ashit and today I'll
show<00:00:02.480><c> you</c><00:00:02.600><c> how</c><00:00:02.720><c> to</c><00:00:02.840><c> run</c><00:00:03.040><c> llama</c><00:00:03.439><c> 3</c><00:00:03.800><c> and</c><00:00:03.959><c> build</c><00:00:04.200><c> a</c>

00:00:04.349 --> 00:00:04.359 align:start position:0%
show you how to run llama 3 and build a
 

00:00:04.359 --> 00:00:06.950 align:start position:0%
show you how to run llama 3 and build a
fully<00:00:04.720><c> local</c><00:00:05.040><c> rag</c><00:00:05.600><c> application</c><00:00:06.600><c> what</c><00:00:06.720><c> we'll</c>

00:00:06.950 --> 00:00:06.960 align:start position:0%
fully local rag application what we'll
 

00:00:06.960 --> 00:00:08.950 align:start position:0%
fully local rag application what we'll
do<00:00:07.120><c> today</c><00:00:07.480><c> is</c><00:00:07.640><c> connect</c><00:00:08.000><c> Lama</c><00:00:08.400><c> 3</c><00:00:08.679><c> to</c><00:00:08.840><c> a</c>

00:00:08.950 --> 00:00:08.960 align:start position:0%
do today is connect Lama 3 to a
 

00:00:08.960 --> 00:00:11.030 align:start position:0%
do today is connect Lama 3 to a
knowledge<00:00:09.320><c> base</c><00:00:09.559><c> of</c><00:00:09.760><c> websites</c><00:00:10.160><c> and</c><00:00:10.360><c> PDFs</c><00:00:10.920><c> that</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
knowledge base of websites and PDFs that
 

00:00:11.040 --> 00:00:12.110 align:start position:0%
knowledge base of websites and PDFs that
we're<00:00:11.160><c> going</c><00:00:11.240><c> to</c><00:00:11.360><c> store</c><00:00:11.559><c> in</c><00:00:11.679><c> a</c><00:00:11.799><c> wector</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
we're going to store in a wector
 

00:00:12.120 --> 00:00:14.190 align:start position:0%
we're going to store in a wector
database<00:00:12.840><c> and</c><00:00:12.960><c> then</c><00:00:13.120><c> ask</c><00:00:13.440><c> questions</c><00:00:13.799><c> of</c><00:00:14.000><c> it</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
database and then ask questions of it
 

00:00:14.200 --> 00:00:16.830 align:start position:0%
database and then ask questions of it
using<00:00:14.480><c> a</c><00:00:14.599><c> stream</c><00:00:15.199><c> application</c><00:00:16.199><c> the</c><00:00:16.320><c> full</c><00:00:16.560><c> code</c>

00:00:16.830 --> 00:00:16.840 align:start position:0%
using a stream application the full code
 

00:00:16.840 --> 00:00:18.790 align:start position:0%
using a stream application the full code
for<00:00:17.000><c> this</c><00:00:17.199><c> application</c><00:00:17.680><c> is</c><00:00:17.800><c> under</c><00:00:18.039><c> the</c><00:00:18.240><c> f</c><00:00:18.720><c> is</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
for this application is under the f is
 

00:00:18.800 --> 00:00:22.630 align:start position:0%
for this application is under the f is
under<00:00:19.000><c> a</c><00:00:19.119><c> f</c><00:00:19.600><c> cookbook</c><00:00:20.439><c> so</c><00:00:20.960><c> for</c><00:00:21.359><c> can</c><00:00:21.680><c> clone</c><00:00:22.119><c> this</c>

00:00:22.630 --> 00:00:22.640 align:start position:0%
under a f cookbook so for can clone this
 

00:00:22.640 --> 00:00:25.550 align:start position:0%
under a f cookbook so for can clone this
repository<00:00:23.640><c> and</c><00:00:23.800><c> go</c><00:00:24.039><c> to</c><00:00:24.160><c> the</c><00:00:24.320><c> cookbooks</c><00:00:24.920><c> llms</c>

00:00:25.550 --> 00:00:25.560 align:start position:0%
repository and go to the cookbooks llms
 

00:00:25.560 --> 00:00:27.429 align:start position:0%
repository and go to the cookbooks llms
olama<00:00:26.039><c> rag</c><00:00:26.359><c> folder</c><00:00:26.720><c> where</c><00:00:26.840><c> you</c><00:00:26.920><c> can</c><00:00:27.119><c> find</c><00:00:27.320><c> the</c>

00:00:27.429 --> 00:00:27.439 align:start position:0%
olama rag folder where you can find the
 

00:00:27.439 --> 00:00:28.630 align:start position:0%
olama rag folder where you can find the
code<00:00:27.679><c> for</c><00:00:27.880><c> this</c><00:00:28.080><c> app</c><00:00:28.240><c> we're</c><00:00:28.359><c> going</c><00:00:28.480><c> to</c><00:00:28.560><c> be</c>

00:00:28.630 --> 00:00:28.640 align:start position:0%
code for this app we're going to be
 

00:00:28.640 --> 00:00:31.069 align:start position:0%
code for this app we're going to be
running<00:00:28.920><c> today</c><00:00:30.080><c> once</c><00:00:30.279><c> you've</c><00:00:30.480><c> cloned</c><00:00:30.840><c> this</c>

00:00:31.069 --> 00:00:31.079 align:start position:0%
running today once you've cloned this
 

00:00:31.079 --> 00:00:33.470 align:start position:0%
running today once you've cloned this
repository<00:00:32.079><c> open</c><00:00:32.320><c> it</c><00:00:32.520><c> up</c><00:00:32.680><c> in</c><00:00:32.800><c> the</c><00:00:32.920><c> code</c><00:00:33.160><c> editor</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
repository open it up in the code editor
 

00:00:33.480 --> 00:00:36.069 align:start position:0%
repository open it up in the code editor
of<00:00:33.600><c> your</c><00:00:33.760><c> choice</c><00:00:34.719><c> again</c><00:00:34.920><c> F</c><00:00:35.239><c> dat</c><00:00:35.559><c> cookbooks</c>

00:00:36.069 --> 00:00:36.079 align:start position:0%
of your choice again F dat cookbooks
 

00:00:36.079 --> 00:00:39.990 align:start position:0%
of your choice again F dat cookbooks
llms<00:00:36.680><c> AMA</c><00:00:37.120><c> rack</c><00:00:37.480><c> folder</c><00:00:38.480><c> and</c><00:00:38.680><c> then</c><00:00:39.480><c> start</c><00:00:39.840><c> up</c>

00:00:39.990 --> 00:00:40.000 align:start position:0%
llms AMA rack folder and then start up
 

00:00:40.000 --> 00:00:42.709 align:start position:0%
llms AMA rack folder and then start up
your<00:00:40.200><c> terminal</c><00:00:41.200><c> where</c><00:00:41.320><c> we</c><00:00:41.520><c> run</c><00:00:41.760><c> this</c>

00:00:42.709 --> 00:00:42.719 align:start position:0%
your terminal where we run this
 

00:00:42.719 --> 00:00:45.110 align:start position:0%
your terminal where we run this
application<00:00:43.719><c> we've</c><00:00:44.000><c> given</c><00:00:44.399><c> step-by-step</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
application we've given step-by-step
 

00:00:45.120 --> 00:00:47.950 align:start position:0%
application we've given step-by-step
instruction<00:00:45.600><c> so</c><00:00:45.800><c> first</c><00:00:46.039><c> you'll</c><00:00:46.320><c> install</c>

00:00:47.950 --> 00:00:47.960 align:start position:0%
instruction so first you'll install
 

00:00:47.960 --> 00:00:52.430 align:start position:0%
instruction so first you'll install
raama<00:00:48.960><c> which</c><00:00:49.120><c> we'll</c><00:00:49.399><c> use</c><00:00:50.000><c> to</c><00:00:50.199><c> run</c><00:00:50.440><c> the</c><00:00:50.559><c> Llama</c><00:00:50.920><c> 3</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
raama which we'll use to run the Llama 3
 

00:00:52.440 --> 00:00:54.830 align:start position:0%
raama which we'll use to run the Llama 3
Model<00:00:53.440><c> then</c><00:00:53.559><c> we'll</c><00:00:53.840><c> create</c><00:00:54.160><c> a</c><00:00:54.359><c> virtual</c>

00:00:54.830 --> 00:00:54.840 align:start position:0%
Model then we'll create a virtual
 

00:00:54.840 --> 00:00:56.750 align:start position:0%
Model then we'll create a virtual
environment<00:00:55.559><c> where</c><00:00:55.719><c> we'll</c><00:00:56.120><c> install</c><00:00:56.520><c> our</c>

00:00:56.750 --> 00:00:56.760 align:start position:0%
environment where we'll install our
 

00:00:56.760 --> 00:00:58.869 align:start position:0%
environment where we'll install our
dependencies<00:00:57.440><c> like</c><00:00:57.600><c> PG</c><00:00:57.960><c> Vector</c><00:00:58.359><c> AMA</c>

00:00:58.869 --> 00:00:58.879 align:start position:0%
dependencies like PG Vector AMA
 

00:00:58.879 --> 00:01:01.349 align:start position:0%
dependencies like PG Vector AMA
streamlet<00:00:59.480><c> f</c><00:01:00.239><c> which</c><00:01:00.399><c> run</c><00:01:00.640><c> this</c>

00:01:01.349 --> 00:01:01.359 align:start position:0%
streamlet f which run this
 

00:01:01.359 --> 00:01:03.830 align:start position:0%
streamlet f which run this
application<00:01:02.359><c> then</c><00:01:02.519><c> we'll</c><00:01:02.719><c> run</c><00:01:03.000><c> PG</c><00:01:03.359><c> Vector</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
application then we'll run PG Vector
 

00:01:03.840 --> 00:01:05.670 align:start position:0%
application then we'll run PG Vector
using<00:01:04.239><c> Docker</c><00:01:05.000><c> which</c>

00:01:05.670 --> 00:01:05.680 align:start position:0%
using Docker which
 

00:01:05.680 --> 00:01:08.469 align:start position:0%
using Docker which
will<00:01:06.680><c> provide</c><00:01:07.080><c> storage</c><00:01:07.560><c> and</c><00:01:07.759><c> Vector</c><00:01:08.119><c> database</c>

00:01:08.469 --> 00:01:08.479 align:start position:0%
will provide storage and Vector database
 

00:01:08.479 --> 00:01:10.670 align:start position:0%
will provide storage and Vector database
for<00:01:08.600><c> our</c><00:01:08.920><c> application</c><00:01:09.920><c> finally</c><00:01:10.280><c> we'll</c><00:01:10.479><c> run</c>

00:01:10.670 --> 00:01:10.680 align:start position:0%
for our application finally we'll run
 

00:01:10.680 --> 00:01:12.030 align:start position:0%
for our application finally we'll run
our<00:01:10.840><c> app</c><00:01:11.000><c> using</c>

00:01:12.030 --> 00:01:12.040 align:start position:0%
our app using
 

00:01:12.040 --> 00:01:15.230 align:start position:0%
our app using
streamlet<00:01:13.040><c> and</c><00:01:13.159><c> we're</c><00:01:13.360><c> good</c><00:01:13.520><c> to</c><00:01:13.799><c> go</c><00:01:14.799><c> so</c><00:01:15.040><c> we</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
streamlet and we're good to go so we
 

00:01:15.240 --> 00:01:17.109 align:start position:0%
streamlet and we're good to go so we
have<00:01:15.400><c> our</c><00:01:15.640><c> app</c><00:01:15.880><c> running</c><00:01:16.280><c> which</c><00:01:16.400><c> is</c><00:01:16.560><c> local</c><00:01:16.840><c> rag</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
have our app running which is local rag
 

00:01:17.119 --> 00:01:19.350 align:start position:0%
have our app running which is local rag
with<00:01:17.240><c> Lama</c><00:01:17.479><c> 3</c><00:01:17.640><c> and</c><00:01:17.759><c> PG</c><00:01:18.040><c> Vector</c><00:01:18.799><c> you</c><00:01:18.920><c> can</c><00:01:19.080><c> add</c>

00:01:19.350 --> 00:01:19.360 align:start position:0%
with Lama 3 and PG Vector you can add
 

00:01:19.360 --> 00:01:20.670 align:start position:0%
with Lama 3 and PG Vector you can add
any<00:01:19.520><c> model</c><00:01:19.799><c> you</c><00:01:19.960><c> like</c><00:01:20.159><c> today</c><00:01:20.360><c> we're</c><00:01:20.520><c> going</c><00:01:20.600><c> to</c>

00:01:20.670 --> 00:01:20.680 align:start position:0%
any model you like today we're going to
 

00:01:20.680 --> 00:01:22.630 align:start position:0%
any model you like today we're going to
be<00:01:20.759><c> playing</c><00:01:21.000><c> around</c><00:01:21.240><c> with</c><00:01:21.320><c> the</c><00:01:21.439><c> Lama</c><00:01:21.720><c> 3</c><00:01:21.880><c> Model</c>

00:01:22.630 --> 00:01:22.640 align:start position:0%
be playing around with the Lama 3 Model
 

00:01:22.640 --> 00:01:24.510 align:start position:0%
be playing around with the Lama 3 Model
We'll<00:01:22.880><c> add</c><00:01:23.040><c> a</c><00:01:23.159><c> URL</c><00:01:23.560><c> to</c><00:01:23.680><c> the</c><00:01:23.799><c> knowledge</c><00:01:24.159><c> base</c><00:01:24.360><c> or</c>

00:01:24.510 --> 00:01:24.520 align:start position:0%
We'll add a URL to the knowledge base or
 

00:01:24.520 --> 00:01:26.550 align:start position:0%
We'll add a URL to the knowledge base or
we<00:01:24.640><c> can</c><00:01:24.759><c> add</c><00:01:24.960><c> PDFs</c><00:01:25.759><c> we'll</c><00:01:26.000><c> play</c><00:01:26.200><c> around</c><00:01:26.439><c> with</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
we can add PDFs we'll play around with
 

00:01:26.560 --> 00:01:28.030 align:start position:0%
we can add PDFs we'll play around with
the<00:01:26.680><c> URL</c><00:01:27.119><c> and</c><00:01:27.200><c> I</c><00:01:27.280><c> want</c><00:01:27.400><c> to</c><00:01:27.520><c> show</c><00:01:27.680><c> you</c><00:01:27.840><c> what</c>

00:01:28.030 --> 00:01:28.040 align:start position:0%
the URL and I want to show you what
 

00:01:28.040 --> 00:01:31.710 align:start position:0%
the URL and I want to show you what
happens<00:01:28.439><c> behind</c><00:01:28.720><c> the</c><00:01:28.880><c> scenes</c><00:01:29.320><c> step</c><00:01:29.600><c> by</c><00:01:29.759><c> step</c>

00:01:31.710 --> 00:01:31.720 align:start position:0%
happens behind the scenes step by step
 

00:01:31.720 --> 00:01:35.429 align:start position:0%
happens behind the scenes step by step
so<00:01:32.000><c> we'll</c><00:01:33.000><c> add</c><00:01:33.560><c> the</c><00:01:34.200><c> tech</c><00:01:34.520><c> crunch</c><00:01:34.840><c> release</c><00:01:35.240><c> for</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
so we'll add the tech crunch release for
 

00:01:35.439 --> 00:01:39.030 align:start position:0%
so we'll add the tech crunch release for
The<00:01:35.560><c> Llama</c><00:01:35.880><c> 3</c><00:01:36.520><c> models</c><00:01:37.520><c> behind</c><00:01:37.960><c> the</c><00:01:38.079><c> scenes</c><00:01:38.840><c> the</c>

00:01:39.030 --> 00:01:39.040 align:start position:0%
The Llama 3 models behind the scenes the
 

00:01:39.040 --> 00:01:42.710 align:start position:0%
The Llama 3 models behind the scenes the
assistant<00:01:40.000><c> will</c><00:01:40.320><c> crawl</c><00:01:40.799><c> the</c><00:01:41.320><c> website</c><00:01:42.200><c> and</c><00:01:42.479><c> add</c>

00:01:42.710 --> 00:01:42.720 align:start position:0%
assistant will crawl the website and add
 

00:01:42.720 --> 00:01:44.830 align:start position:0%
assistant will crawl the website and add
the<00:01:42.920><c> documents</c><00:01:43.600><c> one</c><00:01:43.840><c> by</c><00:01:44.040><c> one</c><00:01:44.200><c> to</c><00:01:44.360><c> our</c><00:01:44.520><c> V</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
the documents one by one to our V
 

00:01:44.840 --> 00:01:47.469 align:start position:0%
the documents one by one to our V
database<00:01:45.560><c> it's</c><00:01:45.719><c> added</c><00:01:46.079><c> three</c><00:01:46.360><c> documents</c><00:01:47.320><c> and</c>

00:01:47.469 --> 00:01:47.479 align:start position:0%
database it's added three documents and
 

00:01:47.479 --> 00:01:50.069 align:start position:0%
database it's added three documents and
now<00:01:47.600><c> we</c><00:01:47.719><c> can</c><00:01:47.880><c> ask</c><00:01:48.159><c> questions</c><00:01:49.159><c> like</c><00:01:49.439><c> what</c><00:01:49.640><c> did</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
now we can ask questions like what did
 

00:01:50.079 --> 00:01:52.030 align:start position:0%
now we can ask questions like what did
meta

00:01:52.030 --> 00:01:52.040 align:start position:0%
meta
 

00:01:52.040 --> 00:01:54.990 align:start position:0%
meta
release<00:01:53.040><c> so</c><00:01:53.280><c> Lama</c><00:01:53.600><c> 3</c><00:01:53.880><c> is</c><00:01:54.000><c> going</c><00:01:54.119><c> to</c><00:01:54.320><c> respond</c>

00:01:54.990 --> 00:01:55.000 align:start position:0%
release so Lama 3 is going to respond
 

00:01:55.000 --> 00:01:57.950 align:start position:0%
release so Lama 3 is going to respond
with<00:01:55.320><c> that</c><00:01:55.799><c> meta</c><00:01:56.159><c> release</c><00:01:56.640><c> a</c><00:01:56.759><c> new</c><00:01:57.360><c> uh</c><00:01:57.560><c> series</c>

00:01:57.950 --> 00:01:57.960 align:start position:0%
with that meta release a new uh series
 

00:01:57.960 --> 00:02:01.429 align:start position:0%
with that meta release a new uh series
of<00:01:58.399><c> generative</c><00:01:58.920><c> AI</c><00:01:59.200><c> models</c><00:02:00.320><c> Lama</c><00:02:00.680><c> 38b</c><00:02:01.320><c> and</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
of generative AI models Lama 38b and
 

00:02:01.439 --> 00:02:03.350 align:start position:0%
of generative AI models Lama 38b and
Lama

00:02:03.350 --> 00:02:03.360 align:start position:0%
Lama
 

00:02:03.360 --> 00:02:05.670 align:start position:0%
Lama
370b<00:02:04.360><c> so</c><00:02:04.600><c> what's</c><00:02:04.799><c> happening</c><00:02:05.200><c> behind</c><00:02:05.520><c> the</c>

00:02:05.670 --> 00:02:05.680 align:start position:0%
370b so what's happening behind the
 

00:02:05.680 --> 00:02:08.109 align:start position:0%
370b so what's happening behind the
scenes<00:02:06.240><c> is</c><00:02:06.399><c> that</c><00:02:06.560><c> we're</c><00:02:06.840><c> creating</c><00:02:07.320><c> a</c><00:02:07.479><c> f</c><00:02:07.799><c> dat</c>

00:02:08.109 --> 00:02:08.119 align:start position:0%
scenes is that we're creating a f dat
 

00:02:08.119 --> 00:02:10.670 align:start position:0%
scenes is that we're creating a f dat
assistant<00:02:09.119><c> that</c><00:02:09.239><c> is</c><00:02:09.479><c> querying</c><00:02:09.959><c> the</c><00:02:10.080><c> vector</c>

00:02:10.670 --> 00:02:10.680 align:start position:0%
assistant that is querying the vector
 

00:02:10.680 --> 00:02:13.350 align:start position:0%
assistant that is querying the vector
database<00:02:11.680><c> or</c><00:02:12.040><c> the</c><00:02:12.200><c> chat</c><00:02:12.520><c> history</c><00:02:12.879><c> whatever</c><00:02:13.200><c> it</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
database or the chat history whatever it
 

00:02:13.360 --> 00:02:16.790 align:start position:0%
database or the chat history whatever it
needs<00:02:14.000><c> to</c><00:02:14.200><c> generate</c><00:02:14.640><c> this</c><00:02:15.160><c> answer</c><00:02:16.160><c> so</c><00:02:16.400><c> what</c><00:02:16.599><c> is</c>

00:02:16.790 --> 00:02:16.800 align:start position:0%
needs to generate this answer so what is
 

00:02:16.800 --> 00:02:18.949 align:start position:0%
needs to generate this answer so what is
f<00:02:17.080><c> data</c><00:02:17.319><c> so</c><00:02:17.440><c> this</c><00:02:17.560><c> is</c><00:02:17.720><c> where</c><00:02:18.239><c> our</c><00:02:18.519><c> assistants</c>

00:02:18.949 --> 00:02:18.959 align:start position:0%
f data so this is where our assistants
 

00:02:18.959 --> 00:02:21.270 align:start position:0%
f data so this is where our assistants
come<00:02:19.120><c> into</c><00:02:19.360><c> play</c><00:02:20.040><c> F</c><00:02:20.319><c> data</c><00:02:20.560><c> is</c><00:02:20.599><c> a</c><00:02:20.720><c> framework</c><00:02:21.160><c> for</c>

00:02:21.270 --> 00:02:21.280 align:start position:0%
come into play F data is a framework for
 

00:02:21.280 --> 00:02:23.270 align:start position:0%
come into play F data is a framework for
building<00:02:21.640><c> AI</c><00:02:21.959><c> assistance</c><00:02:22.560><c> with</c><00:02:22.879><c> memory</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
building AI assistance with memory
 

00:02:23.280 --> 00:02:25.910 align:start position:0%
building AI assistance with memory
knowledge<00:02:23.599><c> and</c><00:02:23.760><c> tools</c><00:02:24.640><c> what</c><00:02:24.800><c> we</c><00:02:25.000><c> notice</c><00:02:25.599><c> is</c>

00:02:25.910 --> 00:02:25.920 align:start position:0%
knowledge and tools what we notice is
 

00:02:25.920 --> 00:02:30.430 align:start position:0%
knowledge and tools what we notice is
LMS<00:02:26.640><c> like</c><00:02:27.000><c> Llama</c><00:02:27.800><c> gbd4</c><00:02:28.800><c> Or</c><00:02:29.080><c> Claude</c><00:02:29.440><c> hou</c>

00:02:30.430 --> 00:02:30.440 align:start position:0%
LMS like Llama gbd4 Or Claude hou
 

00:02:30.440 --> 00:02:32.509 align:start position:0%
LMS like Llama gbd4 Or Claude hou
they<00:02:30.599><c> have</c><00:02:30.800><c> limited</c><00:02:31.319><c> contexts</c><00:02:32.120><c> and</c><00:02:32.360><c> they</c>

00:02:32.509 --> 00:02:32.519 align:start position:0%
they have limited contexts and they
 

00:02:32.519 --> 00:02:35.229 align:start position:0%
they have limited contexts and they
can't<00:02:32.800><c> take</c><00:02:33.040><c> actions</c><00:02:33.879><c> so</c><00:02:34.120><c> what</c><00:02:34.280><c> we</c><00:02:34.440><c> do</c><00:02:34.879><c> is</c><00:02:35.040><c> we</c>

00:02:35.229 --> 00:02:35.239 align:start position:0%
can't take actions so what we do is we
 

00:02:35.239 --> 00:02:38.270 align:start position:0%
can't take actions so what we do is we
add<00:02:35.720><c> memory</c><00:02:36.239><c> knowledge</c><00:02:36.760><c> and</c><00:02:37.000><c> tools</c><00:02:37.800><c> and</c><00:02:38.000><c> turn</c>

00:02:38.270 --> 00:02:38.280 align:start position:0%
add memory knowledge and tools and turn
 

00:02:38.280 --> 00:02:40.589 align:start position:0%
add memory knowledge and tools and turn
them<00:02:38.400><c> into</c><00:02:38.640><c> usable</c><00:02:39.040><c> AI</c><00:02:39.319><c> assistance</c><00:02:40.239><c> knowledge</c>

00:02:40.589 --> 00:02:40.599 align:start position:0%
them into usable AI assistance knowledge
 

00:02:40.599 --> 00:02:42.270 align:start position:0%
them into usable AI assistance knowledge
is<00:02:40.800><c> very</c><00:02:40.959><c> simple</c><00:02:41.200><c> to</c><00:02:41.680><c> understand</c><00:02:41.959><c> it's</c><00:02:42.080><c> your</c>

00:02:42.270 --> 00:02:42.280 align:start position:0%
is very simple to understand it's your
 

00:02:42.280 --> 00:02:44.990 align:start position:0%
is very simple to understand it's your
business<00:02:42.640><c> contacts</c><00:02:43.159><c> it's</c><00:02:43.319><c> your</c><00:02:43.519><c> PDFs</c><00:02:44.400><c> Json</c>

00:02:44.990 --> 00:02:45.000 align:start position:0%
business contacts it's your PDFs Json
 

00:02:45.000 --> 00:02:47.949 align:start position:0%
business contacts it's your PDFs Json
website<00:02:45.480><c> docs</c><00:02:46.360><c> whatever</c><00:02:47.120><c> extra</c><00:02:47.560><c> information</c>

00:02:47.949 --> 00:02:47.959 align:start position:0%
website docs whatever extra information
 

00:02:47.959 --> 00:02:50.630 align:start position:0%
website docs whatever extra information
you<00:02:48.120><c> provide</c><00:02:48.400><c> to</c><00:02:48.519><c> the</c><00:02:48.800><c> llm</c><00:02:49.800><c> memory</c><00:02:50.319><c> is</c><00:02:50.480><c> the</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
you provide to the llm memory is the
 

00:02:50.640 --> 00:02:52.390 align:start position:0%
you provide to the llm memory is the
chat<00:02:51.000><c> history</c><00:02:51.280><c> or</c><00:02:51.440><c> the</c><00:02:51.599><c> summaries</c><00:02:52.159><c> this</c>

00:02:52.390 --> 00:02:52.400 align:start position:0%
chat history or the summaries this
 

00:02:52.400 --> 00:02:55.070 align:start position:0%
chat history or the summaries this
allows<00:02:52.800><c> the</c><00:02:52.920><c> llm</c><00:02:53.440><c> to</c><00:02:53.640><c> have</c><00:02:54.000><c> long-term</c>

00:02:55.070 --> 00:02:55.080 align:start position:0%
allows the llm to have long-term
 

00:02:55.080 --> 00:02:57.630 align:start position:0%
allows the llm to have long-term
conversations<00:02:56.080><c> uh</c><00:02:56.239><c> with</c><00:02:56.440><c> the</c><00:02:56.599><c> user</c><00:02:57.360><c> now</c><00:02:57.519><c> when</c>

00:02:57.630 --> 00:02:57.640 align:start position:0%
conversations uh with the user now when
 

00:02:57.640 --> 00:02:59.509 align:start position:0%
conversations uh with the user now when
you<00:02:57.720><c> go</c><00:02:57.840><c> to</c><00:02:57.959><c> chat</c><00:02:58.200><c> chbt</c><00:02:58.680><c> it</c><00:02:58.800><c> can</c><00:02:59.000><c> already</c><00:02:59.319><c> have</c>

00:02:59.509 --> 00:02:59.519 align:start position:0%
you go to chat chbt it can already have
 

00:02:59.519 --> 00:03:01.430 align:start position:0%
you go to chat chbt it can already have
these<00:02:59.840><c> long-term</c><00:03:00.280><c> conversations</c><00:03:01.040><c> have</c><00:03:01.280><c> this</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
these long-term conversations have this
 

00:03:01.440 --> 00:03:03.990 align:start position:0%
these long-term conversations have this
memory<00:03:01.760><c> P</c><00:03:02.040><c> 10</c><00:03:02.680><c> but</c><00:03:02.840><c> you're</c><00:03:03.000><c> working</c><00:03:03.519><c> directly</c>

00:03:03.990 --> 00:03:04.000 align:start position:0%
memory P 10 but you're working directly
 

00:03:04.000 --> 00:03:06.630 align:start position:0%
memory P 10 but you're working directly
with<00:03:04.159><c> an</c><00:03:04.319><c> open</c><00:03:04.560><c> source</c><00:03:04.799><c> llm</c><00:03:05.440><c> or</c><00:03:05.680><c> with</c><00:03:05.840><c> the</c><00:03:06.000><c> API</c>

00:03:06.630 --> 00:03:06.640 align:start position:0%
with an open source llm or with the API
 

00:03:06.640 --> 00:03:08.110 align:start position:0%
with an open source llm or with the API
you<00:03:06.760><c> need</c><00:03:06.959><c> to</c><00:03:07.159><c> provide</c><00:03:07.480><c> this</c><00:03:07.640><c> memory</c>

00:03:08.110 --> 00:03:08.120 align:start position:0%
you need to provide this memory
 

00:03:08.120 --> 00:03:10.309 align:start position:0%
you need to provide this memory
separately<00:03:08.879><c> fire</c><00:03:09.159><c> data</c><00:03:09.400><c> takes</c><00:03:09.640><c> care</c><00:03:09.840><c> of</c><00:03:10.080><c> that</c>

00:03:10.309 --> 00:03:10.319 align:start position:0%
separately fire data takes care of that
 

00:03:10.319 --> 00:03:13.229 align:start position:0%
separately fire data takes care of that
for<00:03:10.599><c> you</c><00:03:11.599><c> finally</c><00:03:12.080><c> the</c><00:03:12.360><c> last</c><00:03:12.720><c> addition</c><00:03:13.120><c> which</c>

00:03:13.229 --> 00:03:13.239 align:start position:0%
for you finally the last addition which
 

00:03:13.239 --> 00:03:16.229 align:start position:0%
for you finally the last addition which
we<00:03:13.440><c> love</c><00:03:13.920><c> is</c><00:03:14.159><c> adding</c><00:03:14.560><c> tools</c><00:03:14.920><c> to</c><00:03:15.080><c> our</c><00:03:15.280><c> llms</c>

00:03:16.229 --> 00:03:16.239 align:start position:0%
we love is adding tools to our llms
 

00:03:16.239 --> 00:03:18.710 align:start position:0%
we love is adding tools to our llms
meaning<00:03:16.879><c> we</c><00:03:17.000><c> can</c><00:03:17.200><c> now</c><00:03:17.599><c> ask</c><00:03:17.840><c> the</c><00:03:17.920><c> llm</c><00:03:18.319><c> to</c><00:03:18.440><c> search</c>

00:03:18.710 --> 00:03:18.720 align:start position:0%
meaning we can now ask the llm to search
 

00:03:18.720 --> 00:03:21.350 align:start position:0%
meaning we can now ask the llm to search
the<00:03:18.879><c> web</c><00:03:19.080><c> for</c><00:03:19.360><c> information</c><00:03:20.080><c> send</c><00:03:20.480><c> emails</c><00:03:20.959><c> run</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
the web for information send emails run
 

00:03:21.360 --> 00:03:24.350 align:start position:0%
the web for information send emails run
queries<00:03:22.360><c> but</c><00:03:22.599><c> this</c><00:03:22.760><c> is</c><00:03:23.000><c> mostly</c><00:03:23.400><c> reserved</c><00:03:23.959><c> for</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
queries but this is mostly reserved for
 

00:03:24.360 --> 00:03:27.270 align:start position:0%
queries but this is mostly reserved for
the<00:03:24.519><c> more</c><00:03:24.840><c> powerful</c><00:03:25.280><c> models</c><00:03:25.720><c> like</c><00:03:25.920><c> llama</c><00:03:26.280><c> 70b</c>

00:03:27.270 --> 00:03:27.280 align:start position:0%
the more powerful models like llama 70b
 

00:03:27.280 --> 00:03:31.869 align:start position:0%
the more powerful models like llama 70b
or<00:03:27.480><c> gp4</c><00:03:28.480><c> or</c><00:03:29.080><c> Claude</c><00:03:30.120><c> three</c><00:03:31.120><c> uh</c><00:03:31.200><c> so</c><00:03:31.400><c> let's</c><00:03:31.640><c> go</c>

00:03:31.869 --> 00:03:31.879 align:start position:0%
or gp4 or Claude three uh so let's go
 

00:03:31.879 --> 00:03:35.550 align:start position:0%
or gp4 or Claude three uh so let's go
back<00:03:32.360><c> to</c><00:03:33.000><c> our</c><00:03:33.720><c> assistant</c><00:03:34.720><c> so</c><00:03:35.000><c> next</c><00:03:35.239><c> to</c><00:03:35.400><c> the</c>

00:03:35.550 --> 00:03:35.560 align:start position:0%
back to our assistant so next to the
 

00:03:35.560 --> 00:03:37.750 align:start position:0%
back to our assistant so next to the
readme<00:03:36.080><c> file</c><00:03:36.560><c> so</c><00:03:36.760><c> the</c><00:03:37.000><c> app</c><00:03:37.239><c> is</c><00:03:37.360><c> where</c><00:03:37.519><c> we</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
readme file so the app is where we
 

00:03:37.760 --> 00:03:39.710 align:start position:0%
readme file so the app is where we
running<00:03:38.000><c> our</c><00:03:38.200><c> application</c><00:03:39.040><c> so</c><00:03:39.280><c> this</c><00:03:39.400><c> is</c><00:03:39.560><c> the</c>

00:03:39.710 --> 00:03:39.720 align:start position:0%
running our application so this is the
 

00:03:39.720 --> 00:03:42.270 align:start position:0%
running our application so this is the
full<00:03:39.920><c> streamlet</c><00:03:40.680><c> application</c><00:03:41.680><c> you</c><00:03:41.799><c> can</c><00:03:42.000><c> read</c>

00:03:42.270 --> 00:03:42.280 align:start position:0%
full streamlet application you can read
 

00:03:42.280 --> 00:03:45.429 align:start position:0%
full streamlet application you can read
through<00:03:42.480><c> the</c><00:03:42.640><c> code</c><00:03:43.080><c> update</c><00:03:43.519><c> it</c><00:03:43.959><c> make</c><00:03:44.120><c> it</c><00:03:44.280><c> your</c>

00:03:45.429 --> 00:03:45.439 align:start position:0%
through the code update it make it your
 

00:03:45.439 --> 00:03:48.229 align:start position:0%
through the code update it make it your
own<00:03:46.439><c> but</c><00:03:46.599><c> the</c><00:03:46.760><c> part</c><00:03:46.920><c> I</c><00:03:47.000><c> want</c><00:03:47.120><c> to</c><00:03:47.319><c> talk</c><00:03:47.560><c> about</c><00:03:48.040><c> is</c>

00:03:48.229 --> 00:03:48.239 align:start position:0%
own but the part I want to talk about is
 

00:03:48.239 --> 00:03:49.710 align:start position:0%
own but the part I want to talk about is
this<00:03:48.560><c> assistant</c><00:03:49.080><c> which</c><00:03:49.200><c> is</c><00:03:49.319><c> under</c><00:03:49.560><c> the</c>

00:03:49.710 --> 00:03:49.720 align:start position:0%
this assistant which is under the
 

00:03:49.720 --> 00:03:53.509 align:start position:0%
this assistant which is under the
assistant<00:03:50.280><c> file</c><00:03:51.280><c> so</c><00:03:51.480><c> we're</c><00:03:51.760><c> creating</c><00:03:52.280><c> a</c><00:03:52.439><c> f</c><00:03:52.760><c> dat</c>

00:03:53.509 --> 00:03:53.519 align:start position:0%
assistant file so we're creating a f dat
 

00:03:53.519 --> 00:03:57.069 align:start position:0%
assistant file so we're creating a f dat
assistant<00:03:54.519><c> that</c><00:03:54.760><c> takes</c><00:03:55.120><c> the</c><00:03:56.000><c> Llama</c><00:03:56.439><c> 3</c><00:03:56.720><c> Model</c>

00:03:57.069 --> 00:03:57.079 align:start position:0%
assistant that takes the Llama 3 Model
 

00:03:57.079 --> 00:03:59.789 align:start position:0%
assistant that takes the Llama 3 Model
running<00:03:57.360><c> on</c><00:03:57.519><c> a</c><00:03:57.680><c> llama</c><00:03:58.400><c> add</c><00:03:58.720><c> storage</c><00:03:59.159><c> to</c><00:03:59.400><c> it</c>

00:03:59.789 --> 00:03:59.799 align:start position:0%
running on a llama add storage to it
 

00:03:59.799 --> 00:04:02.509 align:start position:0%
running on a llama add storage to it
using<00:04:00.239><c> a</c><00:04:00.400><c> postc</c><00:04:00.720><c> cross</c><00:04:01.079><c> database</c><00:04:02.079><c> adds</c><00:04:02.319><c> a</c>

00:04:02.509 --> 00:04:02.519 align:start position:0%
using a postc cross database adds a
 

00:04:02.519 --> 00:04:04.350 align:start position:0%
using a postc cross database adds a
knowledge<00:04:02.840><c> base</c><00:04:03.079><c> to</c><00:04:03.319><c> which</c><00:04:03.640><c> which</c><00:04:03.799><c> uses</c><00:04:04.200><c> the</c>

00:04:04.350 --> 00:04:04.360 align:start position:0%
knowledge base to which which uses the
 

00:04:04.360 --> 00:04:07.110 align:start position:0%
knowledge base to which which uses the
PG<00:04:04.720><c> Vector</c><00:04:05.200><c> database</c><00:04:06.200><c> which</c><00:04:06.400><c> provides</c><00:04:06.840><c> three</c>

00:04:07.110 --> 00:04:07.120 align:start position:0%
PG Vector database which provides three
 

00:04:07.120 --> 00:04:09.589 align:start position:0%
PG Vector database which provides three
references<00:04:07.560><c> to</c><00:04:07.760><c> the</c><00:04:07.920><c> prompt</c><00:04:08.840><c> and</c><00:04:09.000><c> does</c><00:04:09.200><c> a</c><00:04:09.319><c> few</c>

00:04:09.589 --> 00:04:09.599 align:start position:0%
references to the prompt and does a few
 

00:04:09.599 --> 00:04:12.069 align:start position:0%
references to the prompt and does a few
extra<00:04:09.959><c> things</c><00:04:10.760><c> like</c><00:04:10.879><c> it</c><00:04:11.040><c> tells</c><00:04:11.319><c> the</c><00:04:11.439><c> llm</c><00:04:11.879><c> to</c>

00:04:12.069 --> 00:04:12.079 align:start position:0%
extra things like it tells the llm to
 

00:04:12.079 --> 00:04:15.149 align:start position:0%
extra things like it tells the llm to
respond<00:04:12.640><c> in</c><00:04:12.959><c> markdown</c><00:04:13.560><c> format</c><00:04:14.480><c> it</c><00:04:14.640><c> adds</c><00:04:14.879><c> date</c>

00:04:15.149 --> 00:04:15.159 align:start position:0%
respond in markdown format it adds date
 

00:04:15.159 --> 00:04:18.069 align:start position:0%
respond in markdown format it adds date
time<00:04:15.319><c> to</c><00:04:15.439><c> the</c><00:04:15.519><c> llm</c><00:04:16.079><c> uh</c><00:04:16.199><c> to</c><00:04:16.320><c> the</c><00:04:16.560><c> system</c><00:04:17.079><c> prompt</c>

00:04:18.069 --> 00:04:18.079 align:start position:0%
time to the llm uh to the system prompt
 

00:04:18.079 --> 00:04:21.789 align:start position:0%
time to the llm uh to the system prompt
and<00:04:18.720><c> it</c><00:04:18.959><c> describes</c><00:04:19.720><c> the</c><00:04:19.919><c> assistant</c><00:04:20.560><c> using</c><00:04:21.079><c> a</c>

00:04:21.789 --> 00:04:21.799 align:start position:0%
and it describes the assistant using a
 

00:04:21.799 --> 00:04:23.629 align:start position:0%
and it describes the assistant using a
set<00:04:22.040><c> of</c><00:04:22.360><c> instructions</c><00:04:22.960><c> it</c><00:04:23.080><c> tells</c><00:04:23.280><c> it</c><00:04:23.440><c> what</c><00:04:23.560><c> to</c>

00:04:23.629 --> 00:04:23.639 align:start position:0%
set of instructions it tells it what to
 

00:04:23.639 --> 00:04:25.790 align:start position:0%
set of instructions it tells it what to
do<00:04:23.800><c> using</c><00:04:24.000><c> a</c><00:04:24.120><c> set</c><00:04:24.280><c> of</c><00:04:24.400><c> instructions</c><00:04:25.320><c> now</c><00:04:25.520><c> again</c>

00:04:25.790 --> 00:04:25.800 align:start position:0%
do using a set of instructions now again
 

00:04:25.800 --> 00:04:27.909 align:start position:0%
do using a set of instructions now again
this<00:04:25.880><c> is</c><00:04:26.080><c> just</c><00:04:26.199><c> a</c><00:04:26.440><c> formatting</c><00:04:27.000><c> for</c><00:04:27.160><c> the</c><00:04:27.280><c> system</c>

00:04:27.909 --> 00:04:27.919 align:start position:0%
this is just a formatting for the system
 

00:04:27.919 --> 00:04:31.110 align:start position:0%
this is just a formatting for the system
prompt<00:04:28.919><c> at</c><00:04:29.160><c> any</c><00:04:29.400><c> time</c><00:04:29.840><c> you</c><00:04:29.960><c> can</c><00:04:30.240><c> use</c><00:04:30.520><c> your</c><00:04:30.800><c> own</c>

00:04:31.110 --> 00:04:31.120 align:start position:0%
prompt at any time you can use your own
 

00:04:31.120 --> 00:04:33.070 align:start position:0%
prompt at any time you can use your own
system<00:04:31.520><c> prompt</c><00:04:32.000><c> using</c><00:04:32.280><c> a</c><00:04:32.479><c> system</c><00:04:32.759><c> prompt</c>

00:04:33.070 --> 00:04:33.080 align:start position:0%
system prompt using a system prompt
 

00:04:33.080 --> 00:04:36.110 align:start position:0%
system prompt using a system prompt
variable<00:04:33.600><c> that's</c><00:04:33.840><c> fine</c><00:04:34.560><c> too</c><00:04:35.560><c> now</c><00:04:35.720><c> let's</c><00:04:35.919><c> see</c>

00:04:36.110 --> 00:04:36.120 align:start position:0%
variable that's fine too now let's see
 

00:04:36.120 --> 00:04:37.469 align:start position:0%
variable that's fine too now let's see
what<00:04:36.280><c> happens</c><00:04:36.639><c> behind</c><00:04:36.919><c> the</c><00:04:37.039><c> scenes</c><00:04:37.280><c> with</c>

00:04:37.469 --> 00:04:37.479 align:start position:0%
what happens behind the scenes with
 

00:04:37.479 --> 00:04:39.870 align:start position:0%
what happens behind the scenes with
these<00:04:38.000><c> values</c><00:04:39.000><c> I'm</c><00:04:39.080><c> going</c><00:04:39.199><c> to</c><00:04:39.360><c> clear</c><00:04:39.639><c> my</c>

00:04:39.870 --> 00:04:39.880 align:start position:0%
these values I'm going to clear my
 

00:04:39.880 --> 00:04:42.070 align:start position:0%
these values I'm going to clear my
terminal<00:04:40.880><c> so</c><00:04:41.039><c> I</c><00:04:41.120><c> can</c><00:04:41.240><c> see</c><00:04:41.479><c> exactly</c><00:04:41.840><c> what's</c>

00:04:42.070 --> 00:04:42.080 align:start position:0%
terminal so I can see exactly what's
 

00:04:42.080 --> 00:04:45.070 align:start position:0%
terminal so I can see exactly what's
going<00:04:42.240><c> on</c><00:04:42.919><c> open</c><00:04:43.160><c> up</c><00:04:43.320><c> a</c><00:04:43.440><c> rag</c><00:04:43.759><c> application</c><00:04:44.320><c> again</c>

00:04:45.070 --> 00:04:45.080 align:start position:0%
going on open up a rag application again
 

00:04:45.080 --> 00:04:48.950 align:start position:0%
going on open up a rag application again
and<00:04:45.240><c> see</c><00:04:45.919><c> and</c><00:04:46.080><c> say</c><00:04:46.360><c> tell</c><00:04:46.520><c> me</c><00:04:46.759><c> more</c><00:04:47.720><c> about</c><00:04:48.520><c> llama</c>

00:04:48.950 --> 00:04:48.960 align:start position:0%
and see and say tell me more about llama
 

00:04:48.960 --> 00:04:51.590 align:start position:0%
and see and say tell me more about llama
3<00:04:49.320><c> models</c><00:04:50.320><c> now</c><00:04:50.560><c> behind</c><00:04:50.919><c> the</c>

00:04:51.590 --> 00:04:51.600 align:start position:0%
3 models now behind the
 

00:04:51.600 --> 00:04:54.189 align:start position:0%
3 models now behind the
scenes<00:04:52.600><c> we'll</c><00:04:52.880><c> see</c><00:04:53.320><c> that</c><00:04:53.680><c> first</c><00:04:53.960><c> the</c>

00:04:54.189 --> 00:04:54.199 align:start position:0%
scenes we'll see that first the
 

00:04:54.199 --> 00:04:56.629 align:start position:0%
scenes we'll see that first the
assistant<00:04:55.199><c> searches</c><00:04:55.720><c> for</c><00:04:56.160><c> Relevant</c>

00:04:56.629 --> 00:04:56.639 align:start position:0%
assistant searches for Relevant
 

00:04:56.639 --> 00:04:59.749 align:start position:0%
assistant searches for Relevant
documents<00:04:57.000><c> from</c><00:04:57.120><c> the</c><00:04:57.280><c> database</c><00:04:57.880><c> Vector</c><00:04:58.160><c> DB</c>

00:04:59.749 --> 00:04:59.759 align:start position:0%
documents from the database Vector DB
 

00:04:59.759 --> 00:05:01.950 align:start position:0%
documents from the database Vector DB
then<00:04:59.919><c> it</c><00:05:00.120><c> adds</c><00:05:00.400><c> the</c><00:05:00.600><c> system</c><00:05:00.919><c> prompt</c><00:05:01.639><c> again</c>

00:05:01.950 --> 00:05:01.960 align:start position:0%
then it adds the system prompt again
 

00:05:01.960 --> 00:05:04.110 align:start position:0%
then it adds the system prompt again
this<00:05:02.080><c> is</c><00:05:02.400><c> just</c><00:05:02.680><c> a</c><00:05:02.919><c> formatting</c><00:05:03.479><c> benefit</c><00:05:04.039><c> you</c>

00:05:04.110 --> 00:05:04.120 align:start position:0%
this is just a formatting benefit you
 

00:05:04.120 --> 00:05:07.870 align:start position:0%
this is just a formatting benefit you
can<00:05:04.280><c> use</c><00:05:04.479><c> your</c><00:05:04.639><c> own</c><00:05:04.840><c> system</c><00:05:05.199><c> prompt</c><00:05:05.680><c> no</c>

00:05:07.870 --> 00:05:07.880 align:start position:0%
can use your own system prompt no
 

00:05:07.880 --> 00:05:10.670 align:start position:0%
can use your own system prompt no
issues<00:05:08.880><c> generates</c><00:05:09.400><c> the</c><00:05:09.520><c> user</c><00:05:09.840><c> prompt</c><00:05:10.400><c> with</c>

00:05:10.670 --> 00:05:10.680 align:start position:0%
issues generates the user prompt with
 

00:05:10.680 --> 00:05:12.909 align:start position:0%
issues generates the user prompt with
the<00:05:11.039><c> context</c><00:05:11.560><c> from</c><00:05:11.800><c> the</c><00:05:11.960><c> knowledge</c>

00:05:12.909 --> 00:05:12.919 align:start position:0%
the context from the knowledge
 

00:05:12.919 --> 00:05:15.749 align:start position:0%
the context from the knowledge
base<00:05:13.919><c> and</c><00:05:14.080><c> then</c><00:05:14.400><c> Returns</c><00:05:14.720><c> the</c>

00:05:15.749 --> 00:05:15.759 align:start position:0%
base and then Returns the
 

00:05:15.759 --> 00:05:17.909 align:start position:0%
base and then Returns the
answer<00:05:16.759><c> from</c><00:05:17.000><c> the</c>

00:05:17.909 --> 00:05:17.919 align:start position:0%
answer from the
 

00:05:17.919 --> 00:05:21.350 align:start position:0%
answer from the
llm<00:05:18.919><c> and</c><00:05:19.120><c> that's</c><00:05:19.319><c> it</c><00:05:20.080><c> that's</c><00:05:20.240><c> your</c><00:05:20.479><c> local</c><00:05:20.919><c> rag</c>

00:05:21.350 --> 00:05:21.360 align:start position:0%
llm and that's it that's your local rag
 

00:05:21.360 --> 00:05:24.510 align:start position:0%
llm and that's it that's your local rag
application<00:05:22.360><c> built</c><00:05:22.680><c> using</c><00:05:23.000><c> llama</c><00:05:23.400><c> 3</c><00:05:24.240><c> if</c><00:05:24.360><c> you</c>

00:05:24.510 --> 00:05:24.520 align:start position:0%
application built using llama 3 if you
 

00:05:24.520 --> 00:05:26.189 align:start position:0%
application built using llama 3 if you
have<00:05:24.680><c> any</c><00:05:24.919><c> questions</c><00:05:25.520><c> drop</c><00:05:25.800><c> by</c><00:05:25.960><c> in</c><00:05:26.080><c> the</c>

00:05:26.189 --> 00:05:26.199 align:start position:0%
have any questions drop by in the
 

00:05:26.199 --> 00:05:29.830 align:start position:0%
have any questions drop by in the
Discord<00:05:26.880><c> create</c><00:05:27.120><c> a</c><00:05:27.280><c> GitHub</c><00:05:27.639><c> issue</c><00:05:28.600><c> and</c><00:05:29.080><c> uh</c><00:05:29.319><c> see</c>

00:05:29.830 --> 00:05:29.840 align:start position:0%
Discord create a GitHub issue and uh see
 

00:05:29.840 --> 00:05:33.400 align:start position:0%
Discord create a GitHub issue and uh see
around<00:05:30.400><c> thank</c><00:05:30.600><c> you</c>

