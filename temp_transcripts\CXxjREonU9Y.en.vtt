WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:03.310 align:start position:0%
 
hello<00:00:00.719><c> welcome</c><00:00:01.079><c> back</c><00:00:02.040><c> so</c><00:00:02.240><c> far</c><00:00:02.440><c> in</c><00:00:02.600><c> this</c><00:00:02.760><c> course</c>

00:00:03.310 --> 00:00:03.320 align:start position:0%
hello welcome back so far in this course
 

00:00:03.320 --> 00:00:05.910 align:start position:0%
hello welcome back so far in this course
you've<00:00:03.560><c> learned</c><00:00:03.919><c> about</c><00:00:04.400><c> model</c><00:00:04.920><c> registry</c>

00:00:05.910 --> 00:00:05.920 align:start position:0%
you've learned about model registry
 

00:00:05.920 --> 00:00:07.990 align:start position:0%
you've learned about model registry
specific<00:00:06.359><c> types</c><00:00:06.560><c> of</c><00:00:06.720><c> automation</c><00:00:07.520><c> including</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
specific types of automation including
 

00:00:08.000 --> 00:00:11.030 align:start position:0%
specific types of automation including
web<00:00:08.280><c> Hooks</c><00:00:08.840><c> and</c><00:00:09.000><c> weights</c><00:00:09.200><c> and</c><00:00:09.360><c> bu's</c><00:00:10.040><c> launch</c>

00:00:11.030 --> 00:00:11.040 align:start position:0%
web Hooks and weights and bu's launch
 

00:00:11.040 --> 00:00:13.990 align:start position:0%
web Hooks and weights and bu's launch
and<00:00:11.200><c> now</c><00:00:11.400><c> we</c><00:00:11.519><c> want</c><00:00:11.679><c> to</c><00:00:11.880><c> help</c><00:00:12.440><c> you</c><00:00:13.440><c> apply</c><00:00:13.799><c> these</c>

00:00:13.990 --> 00:00:14.000 align:start position:0%
and now we want to help you apply these
 

00:00:14.000 --> 00:00:16.429 align:start position:0%
and now we want to help you apply these
tools<00:00:14.480><c> to</c><00:00:14.679><c> a</c><00:00:14.879><c> real</c>

00:00:16.429 --> 00:00:16.439 align:start position:0%
tools to a real
 

00:00:16.439 --> 00:00:19.990 align:start position:0%
tools to a real
project<00:00:17.439><c> and</c><00:00:17.560><c> for</c><00:00:17.880><c> this</c><00:00:18.720><c> my</c><00:00:18.920><c> colleague</c><00:00:19.400><c> darck</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
project and for this my colleague darck
 

00:00:20.000 --> 00:00:21.670 align:start position:0%
project and for this my colleague darck
is<00:00:20.160><c> going</c><00:00:20.320><c> to</c><00:00:20.439><c> be</c><00:00:20.600><c> walking</c><00:00:21.039><c> you</c><00:00:21.240><c> through</c><00:00:21.519><c> a</c>

00:00:21.670 --> 00:00:21.680 align:start position:0%
is going to be walking you through a
 

00:00:21.680 --> 00:00:22.870 align:start position:0%
is going to be walking you through a
case

00:00:22.870 --> 00:00:22.880 align:start position:0%
case
 

00:00:22.880 --> 00:00:25.189 align:start position:0%
case
study<00:00:23.880><c> and</c><00:00:24.080><c> dar</c><00:00:24.480><c> is</c><00:00:24.680><c> actually</c><00:00:24.880><c> a</c><00:00:25.000><c> really</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
study and dar is actually a really
 

00:00:25.199 --> 00:00:28.230 align:start position:0%
study and dar is actually a really
impressive<00:00:25.680><c> person</c><00:00:26.160><c> so</c><00:00:26.480><c> first</c><00:00:26.679><c> of</c><00:00:26.840><c> all</c><00:00:27.640><c> uh</c><00:00:27.760><c> Dar</c>

00:00:28.230 --> 00:00:28.240 align:start position:0%
impressive person so first of all uh Dar
 

00:00:28.240 --> 00:00:30.669 align:start position:0%
impressive person so first of all uh Dar
is<00:00:28.439><c> a</c><00:00:28.679><c> kaggle</c><00:00:29.039><c> Grandmaster</c>

00:00:30.669 --> 00:00:30.679 align:start position:0%
is a kaggle Grandmaster
 

00:00:30.679 --> 00:00:33.190 align:start position:0%
is a kaggle Grandmaster
um<00:00:31.400><c> he</c><00:00:31.920><c> he</c><00:00:32.040><c> is</c><00:00:32.279><c> really</c><00:00:32.520><c> good</c><00:00:32.719><c> at</c><00:00:32.880><c> machine</c>

00:00:33.190 --> 00:00:33.200 align:start position:0%
um he he is really good at machine
 

00:00:33.200 --> 00:00:37.150 align:start position:0%
um he he is really good at machine
learning<00:00:34.040><c> but</c><00:00:34.239><c> also</c><00:00:34.800><c> he</c><00:00:35.559><c> has</c><00:00:36.520><c> uh</c><00:00:36.719><c> many</c><00:00:36.960><c> years</c>

00:00:37.150 --> 00:00:37.160 align:start position:0%
learning but also he has uh many years
 

00:00:37.160 --> 00:00:39.389 align:start position:0%
learning but also he has uh many years
of<00:00:37.399><c> experience</c><00:00:37.840><c> in</c><00:00:38.079><c> Industry</c><00:00:39.000><c> applying</c>

00:00:39.389 --> 00:00:39.399 align:start position:0%
of experience in Industry applying
 

00:00:39.399 --> 00:00:41.790 align:start position:0%
of experience in Industry applying
machine<00:00:39.680><c> learning</c><00:00:39.960><c> and</c><00:00:40.200><c> production</c><00:00:41.200><c> and</c><00:00:41.480><c> also</c>

00:00:41.790 --> 00:00:41.800 align:start position:0%
machine learning and production and also
 

00:00:41.800 --> 00:00:45.029 align:start position:0%
machine learning and production and also
he's<00:00:42.120><c> one</c><00:00:42.239><c> of</c><00:00:42.360><c> my</c><00:00:42.520><c> favorite</c><00:00:42.920><c> teachers</c><00:00:43.920><c> he's</c><00:00:44.920><c> uh</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
he's one of my favorite teachers he's uh
 

00:00:45.039 --> 00:00:47.709 align:start position:0%
he's one of my favorite teachers he's uh
taught<00:00:45.360><c> courses</c><00:00:45.800><c> with</c><00:00:46.000><c> me</c><00:00:46.160><c> in</c><00:00:46.280><c> the</c><00:00:46.559><c> past</c><00:00:47.559><c> uh</c>

00:00:47.709 --> 00:00:47.719 align:start position:0%
taught courses with me in the past uh
 

00:00:47.719 --> 00:00:49.910 align:start position:0%
taught courses with me in the past uh
especially<00:00:48.160><c> courses</c><00:00:48.520><c> on</c><00:00:48.680><c> weights</c><00:00:48.920><c> and</c><00:00:49.079><c> biases</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
especially courses on weights and biases
 

00:00:49.920 --> 00:00:51.270 align:start position:0%
especially courses on weights and biases
and<00:00:50.039><c> so</c><00:00:50.199><c> I</c><00:00:50.399><c> really</c><00:00:50.640><c> think</c><00:00:50.800><c> you're</c><00:00:50.960><c> going</c><00:00:51.079><c> to</c>

00:00:51.270 --> 00:00:51.280 align:start position:0%
and so I really think you're going to
 

00:00:51.280 --> 00:00:54.110 align:start position:0%
and so I really think you're going to
enjoy<00:00:51.719><c> this</c><00:00:51.879><c> lesson</c><00:00:52.840><c> thanks</c><00:00:53.039><c> a</c><00:00:53.160><c> lot</c><00:00:53.359><c> ham</c><00:00:53.760><c> I</c><00:00:53.960><c> I</c>

00:00:54.110 --> 00:00:54.120 align:start position:0%
enjoy this lesson thanks a lot ham I I
 

00:00:54.120 --> 00:00:55.590 align:start position:0%
enjoy this lesson thanks a lot ham I I
really<00:00:54.320><c> appreciate</c><00:00:54.800><c> this</c>

00:00:55.590 --> 00:00:55.600 align:start position:0%
really appreciate this
 

00:00:55.600 --> 00:00:58.029 align:start position:0%
really appreciate this
introduction<00:00:56.600><c> so</c><00:00:57.239><c> again</c><00:00:57.399><c> I'm</c><00:00:57.520><c> a</c><00:00:57.760><c> machine</c>

00:00:58.029 --> 00:00:58.039 align:start position:0%
introduction so again I'm a machine
 

00:00:58.039 --> 00:01:00.110 align:start position:0%
introduction so again I'm a machine
learning<00:00:58.359><c> engineer</c><00:00:58.719><c> at</c><00:00:58.879><c> weights</c><00:00:59.079><c> and</c><00:00:59.239><c> biases</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
learning engineer at weights and biases
 

00:01:00.120 --> 00:01:01.590 align:start position:0%
learning engineer at weights and biases
one<00:01:00.280><c> thing</c><00:01:00.480><c> that</c><00:01:00.600><c> we</c><00:01:00.760><c> do</c><00:01:00.960><c> at</c><00:01:01.160><c> weights</c><00:01:01.440><c> and</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
one thing that we do at weights and
 

00:01:01.600 --> 00:01:03.509 align:start position:0%
one thing that we do at weights and
biases<00:01:02.000><c> is</c><00:01:02.120><c> we</c><00:01:02.239><c> work</c><00:01:02.480><c> a</c><00:01:02.640><c> lot</c><00:01:02.920><c> with</c><00:01:03.239><c> our</c>

00:01:03.509 --> 00:01:03.519 align:start position:0%
biases is we work a lot with our
 

00:01:03.519 --> 00:01:06.469 align:start position:0%
biases is we work a lot with our
customers<00:01:04.119><c> a</c><00:01:04.239><c> lot</c><00:01:04.360><c> of</c><00:01:04.600><c> customers</c><00:01:05.360><c> are</c><00:01:05.680><c> now</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
customers a lot of customers are now
 

00:01:06.479 --> 00:01:08.310 align:start position:0%
customers a lot of customers are now
very<00:01:06.760><c> excited</c><00:01:07.240><c> about</c><00:01:07.680><c> training</c><00:01:08.159><c> and</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
very excited about training and
 

00:01:08.320 --> 00:01:10.149 align:start position:0%
very excited about training and
fine-tuning

00:01:10.149 --> 00:01:10.159 align:start position:0%
fine-tuning
 

00:01:10.159 --> 00:01:12.590 align:start position:0%
fine-tuning
llms<00:01:11.159><c> uh</c><00:01:11.280><c> in</c><00:01:11.479><c> fact</c><00:01:11.799><c> we</c><00:01:11.920><c> have</c><00:01:12.080><c> a</c><00:01:12.240><c> separate</c>

00:01:12.590 --> 00:01:12.600 align:start position:0%
llms uh in fact we have a separate
 

00:01:12.600 --> 00:01:14.350 align:start position:0%
llms uh in fact we have a separate
course<00:01:12.880><c> about</c><00:01:13.080><c> training</c><00:01:13.439><c> and</c><00:01:13.600><c> fine-tuning</c>

00:01:14.350 --> 00:01:14.360 align:start position:0%
course about training and fine-tuning
 

00:01:14.360 --> 00:01:17.310 align:start position:0%
course about training and fine-tuning
llms<00:01:15.360><c> uh</c><00:01:15.600><c> that</c><00:01:15.720><c> we</c><00:01:15.880><c> will</c><00:01:16.200><c> link</c><00:01:16.720><c> uh</c><00:01:16.840><c> below</c><00:01:17.159><c> this</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
llms uh that we will link uh below this
 

00:01:17.320 --> 00:01:18.950 align:start position:0%
llms uh that we will link uh below this
video<00:01:17.640><c> and</c><00:01:17.799><c> that</c><00:01:17.960><c> we</c><00:01:18.119><c> definitely</c><00:01:18.520><c> recommend</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
video and that we definitely recommend
 

00:01:18.960 --> 00:01:21.350 align:start position:0%
video and that we definitely recommend
everyone<00:01:19.320><c> taking</c><00:01:20.240><c> but</c><00:01:20.400><c> we</c><00:01:20.560><c> also</c><00:01:20.759><c> want</c><00:01:20.880><c> to</c><00:01:21.079><c> take</c>

00:01:21.350 --> 00:01:21.360 align:start position:0%
everyone taking but we also want to take
 

00:01:21.360 --> 00:01:23.789 align:start position:0%
everyone taking but we also want to take
this<00:01:21.640><c> this</c><00:01:21.799><c> use</c><00:01:22.119><c> case</c><00:01:22.280><c> of</c><00:01:22.520><c> fine-tuning</c><00:01:23.520><c> um</c><00:01:23.680><c> a</c>

00:01:23.789 --> 00:01:23.799 align:start position:0%
this this use case of fine-tuning um a
 

00:01:23.799 --> 00:01:26.069 align:start position:0%
this this use case of fine-tuning um a
large<00:01:24.119><c> language</c><00:01:24.479><c> model</c><00:01:24.840><c> into</c><00:01:25.119><c> this</c><00:01:25.320><c> course</c>

00:01:26.069 --> 00:01:26.079 align:start position:0%
large language model into this course
 

00:01:26.079 --> 00:01:28.030 align:start position:0%
large language model into this course
and<00:01:26.200><c> show</c><00:01:26.560><c> how</c><00:01:26.680><c> to</c><00:01:26.880><c> set</c><00:01:27.040><c> up</c><00:01:27.520><c> U</c><00:01:27.720><c> Model</c>

00:01:28.030 --> 00:01:28.040 align:start position:0%
and show how to set up U Model
 

00:01:28.040 --> 00:01:30.550 align:start position:0%
and show how to set up U Model
Management<00:01:28.759><c> processes</c><00:01:29.759><c> that</c><00:01:29.960><c> that</c><00:01:30.240><c> uh</c><00:01:30.360><c> will</c>

00:01:30.550 --> 00:01:30.560 align:start position:0%
Management processes that that uh will
 

00:01:30.560 --> 00:01:32.590 align:start position:0%
Management processes that that uh will
support<00:01:31.240><c> uh</c><00:01:31.360><c> both</c><00:01:31.560><c> training</c><00:01:31.880><c> and</c>

00:01:32.590 --> 00:01:32.600 align:start position:0%
support uh both training and
 

00:01:32.600 --> 00:01:36.789 align:start position:0%
support uh both training and
evaluation<00:01:33.600><c> of</c><00:01:34.000><c> uh</c><00:01:34.159><c> of</c><00:01:34.479><c> uh</c><00:01:34.560><c> llm</c><00:01:35.240><c> models</c><00:01:36.240><c> and</c><00:01:36.600><c> um</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
evaluation of uh of uh llm models and um
 

00:01:36.799 --> 00:01:38.870 align:start position:0%
evaluation of uh of uh llm models and um
since<00:01:37.040><c> this</c><00:01:37.119><c> is</c><00:01:37.240><c> a</c><00:01:37.399><c> case</c><00:01:37.600><c> study</c><00:01:37.880><c> we</c><00:01:38.000><c> will</c><00:01:38.240><c> take</c>

00:01:38.870 --> 00:01:38.880 align:start position:0%
since this is a case study we will take
 

00:01:38.880 --> 00:01:41.310 align:start position:0%
since this is a case study we will take
an<00:01:39.040><c> existing</c><00:01:39.520><c> data</c><00:01:39.759><c> set</c><00:01:39.960><c> called</c><00:01:40.200><c> alpaka</c><00:01:41.119><c> it</c>

00:01:41.310 --> 00:01:41.320 align:start position:0%
an existing data set called alpaka it
 

00:01:41.320 --> 00:01:44.830 align:start position:0%
an existing data set called alpaka it
comes<00:01:41.880><c> uh</c><00:01:42.079><c> from</c><00:01:42.640><c> uh</c><00:01:42.840><c> from</c><00:01:43.320><c> Stanford</c><00:01:44.320><c> it's</c><00:01:44.520><c> a</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
comes uh from uh from Stanford it's a
 

00:01:44.840 --> 00:01:46.870 align:start position:0%
comes uh from uh from Stanford it's a
it's<00:01:44.960><c> a</c><00:01:45.119><c> synthetic</c><00:01:45.560><c> data</c><00:01:45.799><c> set</c><00:01:46.520><c> that</c><00:01:46.640><c> was</c>

00:01:46.870 --> 00:01:46.880 align:start position:0%
it's a synthetic data set that was
 

00:01:46.880 --> 00:01:51.149 align:start position:0%
it's a synthetic data set that was
created<00:01:47.759><c> with</c><00:01:48.280><c> um</c><00:01:48.479><c> N</c><00:01:48.680><c> llm</c><00:01:49.119><c> Text</c><00:01:49.360><c> D</c><00:01:49.479><c> Vinci</c><00:01:50.159><c> 003</c>

00:01:51.149 --> 00:01:51.159 align:start position:0%
created with um N llm Text D Vinci 003
 

00:01:51.159 --> 00:01:54.749 align:start position:0%
created with um N llm Text D Vinci 003
and<00:01:51.439><c> it</c><00:01:51.600><c> contains</c><00:01:52.520><c> um</c><00:01:53.520><c> uh</c><00:01:53.960><c> several</c><00:01:54.360><c> thousand</c>

00:01:54.749 --> 00:01:54.759 align:start position:0%
and it contains um uh several thousand
 

00:01:54.759 --> 00:01:57.429 align:start position:0%
and it contains um uh several thousand
of<00:01:55.119><c> like</c><00:01:55.439><c> in</c><00:01:55.560><c> fact</c><00:01:55.799><c> 5050</c><00:01:56.399><c> around</c><00:01:56.640><c> 50,000</c><00:01:57.280><c> of</c>

00:01:57.429 --> 00:01:57.439 align:start position:0%
of like in fact 5050 around 50,000 of
 

00:01:57.439 --> 00:02:00.029 align:start position:0%
of like in fact 5050 around 50,000 of
instruction<00:01:57.920><c> following</c><00:01:58.600><c> examples</c><00:01:59.600><c> and</c><00:01:59.920><c> in</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
instruction following examples and in
 

00:02:00.039 --> 00:02:02.789 align:start position:0%
instruction following examples and in
the<00:02:00.159><c> original</c><00:02:00.880><c> alpaka</c><00:02:01.439><c> paper</c><00:02:02.280><c> uh</c><00:02:02.399><c> the</c><00:02:02.520><c> team</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
the original alpaka paper uh the team
 

00:02:02.799 --> 00:02:05.749 align:start position:0%
the original alpaka paper uh the team
has<00:02:02.960><c> Fine</c><00:02:03.240><c> tuned</c><00:02:03.520><c> a</c><00:02:03.680><c> 7</c><00:02:03.960><c> billion</c><00:02:04.479><c> llama</c><00:02:04.840><c> model</c>

00:02:05.749 --> 00:02:05.759 align:start position:0%
has Fine tuned a 7 billion llama model
 

00:02:05.759 --> 00:02:08.790 align:start position:0%
has Fine tuned a 7 billion llama model
uh<00:02:05.880><c> on</c><00:02:06.039><c> this</c><00:02:06.240><c> data</c><00:02:06.520><c> set</c><00:02:07.520><c> in</c><00:02:07.680><c> our</c><00:02:08.160><c> case</c><00:02:08.479><c> since</c><00:02:08.679><c> we</c>

00:02:08.790 --> 00:02:08.800 align:start position:0%
uh on this data set in our case since we
 

00:02:08.800 --> 00:02:10.630 align:start position:0%
uh on this data set in our case since we
are<00:02:08.959><c> trying</c><00:02:09.160><c> to</c><00:02:09.280><c> be</c><00:02:09.479><c> more</c><00:02:09.679><c> efficient</c><00:02:10.119><c> here</c><00:02:10.520><c> and</c>

00:02:10.630 --> 00:02:10.640 align:start position:0%
are trying to be more efficient here and
 

00:02:10.640 --> 00:02:12.150 align:start position:0%
are trying to be more efficient here and
we<00:02:10.759><c> want</c><00:02:10.920><c> to</c><00:02:11.120><c> also</c><00:02:11.360><c> make</c><00:02:11.480><c> it</c><00:02:11.640><c> easier</c><00:02:11.959><c> for</c>

00:02:12.150 --> 00:02:12.160 align:start position:0%
we want to also make it easier for
 

00:02:12.160 --> 00:02:13.589 align:start position:0%
we want to also make it easier for
people<00:02:12.360><c> following</c><00:02:12.720><c> the</c><00:02:12.840><c> course</c><00:02:13.080><c> will</c><00:02:13.239><c> be</c><00:02:13.400><c> fine</c>

00:02:13.589 --> 00:02:13.599 align:start position:0%
people following the course will be fine
 

00:02:13.599 --> 00:02:16.190 align:start position:0%
people following the course will be fine
tuning<00:02:13.920><c> a</c><00:02:14.080><c> smaller</c><00:02:15.040><c> a</c><00:02:15.200><c> tiny</c><00:02:15.440><c> Lama</c><00:02:15.760><c> model</c><00:02:16.080><c> that</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
tuning a smaller a tiny Lama model that
 

00:02:16.200 --> 00:02:17.990 align:start position:0%
tuning a smaller a tiny Lama model that
has<00:02:16.400><c> around</c><00:02:16.680><c> 1</c><00:02:16.879><c> billion</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
has around 1 billion
 

00:02:18.000 --> 00:02:20.710 align:start position:0%
has around 1 billion
parameters<00:02:19.000><c> the</c><00:02:19.519><c> important</c><00:02:20.040><c> thing</c><00:02:20.319><c> when</c><00:02:20.560><c> when</c>

00:02:20.710 --> 00:02:20.720 align:start position:0%
parameters the important thing when when
 

00:02:20.720 --> 00:02:24.270 align:start position:0%
parameters the important thing when when
fine-tuning<00:02:21.560><c> llms</c><00:02:22.280><c> is</c><00:02:23.000><c> evaluation</c><00:02:24.000><c> and</c><00:02:24.120><c> in</c>

00:02:24.270 --> 00:02:24.280 align:start position:0%
fine-tuning llms is evaluation and in
 

00:02:24.280 --> 00:02:26.630 align:start position:0%
fine-tuning llms is evaluation and in
this<00:02:24.519><c> case</c><00:02:24.760><c> study</c><00:02:25.040><c> we'll</c><00:02:25.519><c> um</c><00:02:25.920><c> we'll</c><00:02:26.319><c> uh</c><00:02:26.400><c> build</c>

00:02:26.630 --> 00:02:26.640 align:start position:0%
this case study we'll um we'll uh build
 

00:02:26.640 --> 00:02:29.309 align:start position:0%
this case study we'll um we'll uh build
on<00:02:26.760><c> the</c><00:02:26.920><c> concept</c><00:02:27.239><c> of</c><00:02:27.360><c> using</c><00:02:27.680><c> llm</c><00:02:28.200><c> as</c><00:02:28.319><c> a</c><00:02:28.440><c> judge</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
on the concept of using llm as a judge
 

00:02:29.319 --> 00:02:32.350 align:start position:0%
on the concept of using llm as a judge
so<00:02:29.720><c> uh</c><00:02:29.959><c> we</c><00:02:30.080><c> will</c><00:02:30.400><c> take</c><00:02:30.959><c> um</c><00:02:31.480><c> the</c><00:02:31.720><c> generations</c>

00:02:32.350 --> 00:02:32.360 align:start position:0%
so uh we will take um the generations
 

00:02:32.360 --> 00:02:34.270 align:start position:0%
so uh we will take um the generations
from<00:02:32.560><c> the</c><00:02:32.680><c> model</c><00:02:32.920><c> that</c><00:02:33.040><c> we</c><00:02:33.160><c> fine</c><00:02:33.400><c> tune</c><00:02:33.959><c> we'll</c>

00:02:34.270 --> 00:02:34.280 align:start position:0%
from the model that we fine tune we'll
 

00:02:34.280 --> 00:02:36.589 align:start position:0%
from the model that we fine tune we'll
compare<00:02:34.680><c> them</c><00:02:35.000><c> to</c><00:02:35.239><c> Generations</c><00:02:35.840><c> from</c><00:02:36.000><c> a</c><00:02:36.160><c> model</c>

00:02:36.589 --> 00:02:36.599 align:start position:0%
compare them to Generations from a model
 

00:02:36.599 --> 00:02:39.390 align:start position:0%
compare them to Generations from a model
that<00:02:36.959><c> uh</c><00:02:37.280><c> we</c><00:02:37.440><c> we</c><00:02:37.560><c> defined</c><00:02:37.959><c> as</c><00:02:38.080><c> our</c><00:02:38.400><c> Baseline</c>

00:02:39.390 --> 00:02:39.400 align:start position:0%
that uh we we defined as our Baseline
 

00:02:39.400 --> 00:02:41.790 align:start position:0%
that uh we we defined as our Baseline
and<00:02:39.599><c> we</c><00:02:39.720><c> will</c><00:02:39.879><c> use</c><00:02:40.400><c> uh</c><00:02:40.640><c> another</c><00:02:40.959><c> llm</c><00:02:41.560><c> in</c><00:02:41.680><c> this</c>

00:02:41.790 --> 00:02:41.800 align:start position:0%
and we will use uh another llm in this
 

00:02:41.800 --> 00:02:45.070 align:start position:0%
and we will use uh another llm in this
case<00:02:41.959><c> a</c><00:02:42.080><c> more</c><00:02:42.319><c> powerful</c><00:02:42.840><c> llm</c><00:02:43.280><c> which</c><00:02:43.400><c> is</c><00:02:43.640><c> gp4</c><00:02:44.640><c> to</c>

00:02:45.070 --> 00:02:45.080 align:start position:0%
case a more powerful llm which is gp4 to
 

00:02:45.080 --> 00:02:47.869 align:start position:0%
case a more powerful llm which is gp4 to
compare<00:02:45.560><c> which</c><00:02:45.680><c> one</c><00:02:45.920><c> is</c><00:02:46.400><c> better</c><00:02:47.400><c> and</c><00:02:47.760><c> this</c>

00:02:47.869 --> 00:02:47.879 align:start position:0%
compare which one is better and this
 

00:02:47.879 --> 00:02:50.229 align:start position:0%
compare which one is better and this
will<00:02:48.440><c> result</c><00:02:48.720><c> in</c><00:02:48.840><c> a</c><00:02:49.000><c> metric</c><00:02:49.680><c> that</c><00:02:49.800><c> we</c><00:02:49.920><c> will</c><00:02:50.040><c> use</c>

00:02:50.229 --> 00:02:50.239 align:start position:0%
will result in a metric that we will use
 

00:02:50.239 --> 00:02:52.990 align:start position:0%
will result in a metric that we will use
to<00:02:50.400><c> make</c><00:02:50.720><c> decisions</c><00:02:51.720><c> if</c><00:02:52.200><c> uh</c><00:02:52.319><c> the</c><00:02:52.480><c> new</c><00:02:52.680><c> model</c>

00:02:52.990 --> 00:02:53.000 align:start position:0%
to make decisions if uh the new model
 

00:02:53.000 --> 00:02:54.430 align:start position:0%
to make decisions if uh the new model
that<00:02:53.120><c> we</c><00:02:53.239><c> trained</c><00:02:53.519><c> is</c><00:02:53.680><c> better</c><00:02:54.159><c> than</c><00:02:54.319><c> the</c>

00:02:54.430 --> 00:02:54.440 align:start position:0%
that we trained is better than the
 

00:02:54.440 --> 00:02:56.309 align:start position:0%
that we trained is better than the
Baseline<00:02:54.920><c> and</c><00:02:55.040><c> if</c><00:02:55.120><c> you</c><00:02:55.239><c> want</c><00:02:55.360><c> to</c><00:02:55.560><c> proceed</c>

00:02:56.309 --> 00:02:56.319 align:start position:0%
Baseline and if you want to proceed
 

00:02:56.319 --> 00:02:58.470 align:start position:0%
Baseline and if you want to proceed
further<00:02:56.800><c> with</c><00:02:57.200><c> uh</c><00:02:57.319><c> quantizing</c><00:02:57.920><c> it</c><00:02:58.080><c> preparing</c>

00:02:58.470 --> 00:02:58.480 align:start position:0%
further with uh quantizing it preparing
 

00:02:58.480 --> 00:02:59.869 align:start position:0%
further with uh quantizing it preparing
it<00:02:58.599><c> for</c><00:02:58.760><c> deployment</c><00:02:59.239><c> and</c><00:02:59.360><c> ultimately</c>

00:02:59.869 --> 00:02:59.879 align:start position:0%
it for deployment and ultimately
 

00:02:59.879 --> 00:03:02.149 align:start position:0%
it for deployment and ultimately
deploying<00:03:00.319><c> it</c><00:03:00.840><c> into</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
deploying it into
 

00:03:02.159 --> 00:03:04.710 align:start position:0%
deploying it into
production<00:03:03.159><c> and</c><00:03:03.519><c> with</c><00:03:03.680><c> this</c><00:03:03.920><c> I</c><00:03:04.000><c> want</c><00:03:04.200><c> to</c><00:03:04.480><c> now</c>

00:03:04.710 --> 00:03:04.720 align:start position:0%
production and with this I want to now
 

00:03:04.720 --> 00:03:07.070 align:start position:0%
production and with this I want to now
transition<00:03:05.280><c> into</c><00:03:05.599><c> the</c><00:03:05.920><c> the</c><00:03:06.120><c> live</c><00:03:06.640><c> uh</c><00:03:06.760><c> the</c><00:03:06.879><c> live</c>

00:03:07.070 --> 00:03:07.080 align:start position:0%
transition into the the live uh the live
 

00:03:07.080 --> 00:03:09.430 align:start position:0%
transition into the the live uh the live
demo<00:03:07.400><c> session</c><00:03:08.239><c> and</c><00:03:08.440><c> I</c><00:03:08.519><c> want</c><00:03:08.680><c> to</c><00:03:08.840><c> encourage</c><00:03:09.280><c> you</c>

00:03:09.430 --> 00:03:09.440 align:start position:0%
demo session and I want to encourage you
 

00:03:09.440 --> 00:03:11.750 align:start position:0%
demo session and I want to encourage you
to<00:03:09.720><c> work</c><00:03:09.959><c> along</c><00:03:10.280><c> with</c><00:03:10.440><c> us</c><00:03:11.159><c> uh</c><00:03:11.280><c> check</c><00:03:11.480><c> out</c><00:03:11.599><c> the</c>

00:03:11.750 --> 00:03:11.760 align:start position:0%
to work along with us uh check out the
 

00:03:11.760 --> 00:03:14.589 align:start position:0%
to work along with us uh check out the
repo<00:03:12.159><c> that</c><00:03:12.280><c> we</c><00:03:12.400><c> are</c><00:03:12.720><c> uh</c><00:03:13.000><c> we</c><00:03:13.120><c> will</c><00:03:13.400><c> provide</c><00:03:14.400><c> for</c>

00:03:14.589 --> 00:03:14.599 align:start position:0%
repo that we are uh we will provide for
 

00:03:14.599 --> 00:03:17.550 align:start position:0%
repo that we are uh we will provide for
the<00:03:14.760><c> exercises</c><00:03:15.400><c> follow</c><00:03:15.680><c> along</c><00:03:16.480><c> and</c><00:03:16.959><c> practice</c>

00:03:17.550 --> 00:03:17.560 align:start position:0%
the exercises follow along and practice
 

00:03:17.560 --> 00:03:19.270 align:start position:0%
the exercises follow along and practice
uh<00:03:17.640><c> setting</c><00:03:18.000><c> up</c><00:03:18.239><c> this</c><00:03:18.440><c> Model</c><00:03:18.720><c> Management</c>

00:03:19.270 --> 00:03:19.280 align:start position:0%
uh setting up this Model Management
 

00:03:19.280 --> 00:03:23.840 align:start position:0%
uh setting up this Model Management
processes<00:03:20.200><c> uh</c><00:03:20.440><c> together</c><00:03:20.680><c> with</c><00:03:20.840><c> us</c>

