WEBVTT
Kind: captions
Language: en

00:00:03.040 --> 00:00:04.070 align:start position:0%
 
here<00:00:03.320><c> my</c><00:00:03.439><c> name</c>

00:00:04.070 --> 00:00:04.080 align:start position:0%
here my name
 

00:00:04.080 --> 00:00:07.670 align:start position:0%
here my name
is<00:00:05.080><c> by</c><00:00:06.000><c> I</c><00:00:06.240><c> currently</c><00:00:06.600><c> a</c><00:00:06.759><c> final</c><00:00:07.040><c> year</c><00:00:07.279><c> PhD</c>

00:00:07.670 --> 00:00:07.680 align:start position:0%
is by I currently a final year PhD
 

00:00:07.680 --> 00:00:09.669 align:start position:0%
is by I currently a final year PhD
student<00:00:08.040><c> at</c><00:00:08.200><c> University</c><00:00:08.599><c> of</c><00:00:08.719><c> Minnesota</c><00:00:09.519><c> so</c>

00:00:09.669 --> 00:00:09.679 align:start position:0%
student at University of Minnesota so
 

00:00:09.679 --> 00:00:11.870 align:start position:0%
student at University of Minnesota so
today<00:00:09.960><c> I'm</c><00:00:10.160><c> presenting</c><00:00:10.559><c> my</c><00:00:10.679><c> dra</c><00:00:11.040><c> work</c><00:00:11.360><c> with</c><00:00:11.599><c> my</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
today I'm presenting my dra work with my
 

00:00:11.880 --> 00:00:15.630 align:start position:0%
today I'm presenting my dra work with my
my<00:00:12.080><c> S</c><00:00:12.559><c> Chad</c><00:00:13.360><c> on</c><00:00:13.599><c> the</c><00:00:13.799><c> L</c><00:00:14.080><c> language</c><00:00:14.400><c> model</c><00:00:14.719><c> in</c>

00:00:15.630 --> 00:00:15.640 align:start position:0%
my S Chad on the L language model in
 

00:00:15.640 --> 00:00:23.630 align:start position:0%
my S Chad on the L language model in
cre<00:00:16.640><c> so</c><00:00:17.160><c> in</c><00:00:18.160><c> um</c><00:00:18.840><c> let's</c><00:00:19.080><c> see</c><00:00:19.359><c> if</c><00:00:19.600><c> that</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
 
 

00:00:23.640 --> 00:00:26.310 align:start position:0%
 
worked<00:00:24.640><c> yeah</c><00:00:24.840><c> so</c><00:00:25.080><c> let</c><00:00:25.199><c> me</c><00:00:25.359><c> just</c><00:00:25.599><c> continue</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
worked yeah so let me just continue
 

00:00:26.320 --> 00:00:29.509 align:start position:0%
worked yeah so let me just continue
right<00:00:26.720><c> uh</c><00:00:26.840><c> so</c><00:00:27.160><c> in</c><00:00:27.400><c> early</c><00:00:27.880><c> 2022</c><00:00:28.880><c> sorry</c><00:00:29.199><c> in</c><00:00:29.320><c> early</c>

00:00:29.509 --> 00:00:29.519 align:start position:0%
right uh so in early 2022 sorry in early
 

00:00:29.519 --> 00:00:33.350 align:start position:0%
right uh so in early 2022 sorry in early
202<00:00:30.039><c> three</c><00:00:30.560><c> open</c><00:00:31.000><c> AI</c><00:00:31.480><c> first</c><00:00:32.320><c> uh</c><00:00:32.439><c> launch</c><00:00:32.880><c> here</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
202 three open AI first uh launch here
 

00:00:33.360 --> 00:00:36.670 align:start position:0%
202 three open AI first uh launch here
API<00:00:34.360><c> actually</c><00:00:34.640><c> Let</c><00:00:34.760><c> me</c><00:00:35.079><c> refresh</c><00:00:35.520><c> the</c><00:00:35.680><c> right</c>

00:00:36.670 --> 00:00:36.680 align:start position:0%
API actually Let me refresh the right
 

00:00:36.680 --> 00:00:43.790 align:start position:0%
API actually Let me refresh the right
page<00:00:37.680><c> sorry</c><00:00:38.160><c> about</c>

00:00:43.790 --> 00:00:43.800 align:start position:0%
 
 

00:00:43.800 --> 00:00:46.590 align:start position:0%
 
that<00:00:44.800><c> there</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
that there
 

00:00:46.600 --> 00:00:55.349 align:start position:0%
that there
go<00:00:47.760><c> so</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
 
 

00:00:55.359 --> 00:00:59.270 align:start position:0%
 
um<00:00:56.359><c> right</c><00:00:56.640><c> so</c><00:00:56.960><c> with</c><00:00:57.120><c> the</c><00:00:57.280><c> release</c><00:00:57.719><c> of</c><00:00:58.239><c> uh</c><00:00:58.480><c> the</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
um right so with the release of uh the
 

00:00:59.280 --> 00:01:02.670 align:start position:0%
um right so with the release of uh the
uh<00:00:59.440><c> API</c><00:01:00.359><c> from</c><00:01:00.680><c> the</c><00:01:00.920><c> chbt</c><00:01:01.920><c> there's</c><00:01:02.199><c> been</c><00:01:02.399><c> a</c><00:01:02.559><c> lot</c>

00:01:02.670 --> 00:01:02.680 align:start position:0%
uh API from the chbt there's been a lot
 

00:01:02.680 --> 00:01:06.670 align:start position:0%
uh API from the chbt there's been a lot
of<00:01:02.960><c> interest</c><00:01:03.440><c> in</c><00:01:03.640><c> building</c><00:01:04.559><c> LM</c><00:01:05.280><c> based</c><00:01:05.680><c> Tools</c>

00:01:06.670 --> 00:01:06.680 align:start position:0%
of interest in building LM based Tools
 

00:01:06.680 --> 00:01:08.510 align:start position:0%
of interest in building LM based Tools
in<00:01:06.880><c> a</c><00:01:07.000><c> lot</c><00:01:07.119><c> of</c><00:01:07.240><c> different</c><00:01:07.520><c> applications</c><00:01:08.360><c> for</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
in a lot of different applications for
 

00:01:08.520 --> 00:01:11.310 align:start position:0%
in a lot of different applications for
example<00:01:09.040><c> here</c><00:01:09.439><c> I</c><00:01:09.680><c> built</c><00:01:10.240><c> a</c><00:01:10.479><c> universal</c><00:01:11.000><c> tool</c>

00:01:11.310 --> 00:01:11.320 align:start position:0%
example here I built a universal tool
 

00:01:11.320 --> 00:01:13.590 align:start position:0%
example here I built a universal tool
that<00:01:11.439><c> allows</c><00:01:11.759><c> you</c><00:01:11.920><c> to</c><00:01:12.400><c> harness</c><00:01:12.840><c> the</c><00:01:13.040><c> power</c><00:01:13.360><c> of</c>

00:01:13.590 --> 00:01:13.600 align:start position:0%
that allows you to harness the power of
 

00:01:13.600 --> 00:01:17.350 align:start position:0%
that allows you to harness the power of
LM<00:01:14.400><c> anywhere</c><00:01:15.200><c> on</c><00:01:15.400><c> your</c><00:01:15.640><c> computer</c><00:01:16.640><c> here</c><00:01:16.960><c> I</c><00:01:17.159><c> just</c>

00:01:17.350 --> 00:01:17.360 align:start position:0%
LM anywhere on your computer here I just
 

00:01:17.360 --> 00:01:20.149 align:start position:0%
LM anywhere on your computer here I just
asked<00:01:17.640><c> you</c><00:01:17.799><c> to</c><00:01:18.040><c> write</c><00:01:18.400><c> a</c><00:01:18.880><c> email</c><00:01:19.240><c> for</c><00:01:19.439><c> me</c><00:01:19.799><c> and</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
asked you to write a email for me and
 

00:01:20.159 --> 00:01:23.630 align:start position:0%
asked you to write a email for me and
instantly<00:01:20.799><c> CH</c><00:01:21.520><c> output</c><00:01:22.280><c> a</c><00:01:22.520><c> very</c><00:01:23.000><c> professional</c>

00:01:23.630 --> 00:01:23.640 align:start position:0%
instantly CH output a very professional
 

00:01:23.640 --> 00:01:27.749 align:start position:0%
instantly CH output a very professional
and<00:01:24.479><c> full-blown</c><00:01:25.479><c> uh</c><00:01:25.720><c> email</c><00:01:26.119><c> for</c><00:01:26.400><c> me</c><00:01:27.400><c> so</c><00:01:27.600><c> it's</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
and full-blown uh email for me so it's
 

00:01:27.759 --> 00:01:29.390 align:start position:0%
and full-blown uh email for me so it's
not<00:01:27.960><c> just</c><00:01:28.200><c> individuals</c><00:01:28.880><c> who</c><00:01:29.000><c> are</c><00:01:29.200><c> very</c>

00:01:29.390 --> 00:01:29.400 align:start position:0%
not just individuals who are very
 

00:01:29.400 --> 00:01:32.350 align:start position:0%
not just individuals who are very
interested<00:01:30.040><c> in</c><00:01:30.280><c> harnessing</c><00:01:30.960><c> the</c><00:01:31.280><c> power</c><00:01:31.600><c> of</c><00:01:31.799><c> L</c>

00:01:32.350 --> 00:01:32.360 align:start position:0%
interested in harnessing the power of L
 

00:01:32.360 --> 00:01:35.270 align:start position:0%
interested in harnessing the power of L
models<00:01:33.280><c> all</c><00:01:33.520><c> these</c><00:01:33.720><c> companies</c><00:01:34.159><c> of</c><00:01:34.320><c> all</c><00:01:34.600><c> sizes</c>

00:01:35.270 --> 00:01:35.280 align:start position:0%
models all these companies of all sizes
 

00:01:35.280 --> 00:01:38.350 align:start position:0%
models all these companies of all sizes
from<00:01:35.799><c> Coca-Cola</c><00:01:36.560><c> to</c><00:01:36.759><c> small</c><00:01:37.079><c> businesses</c><00:01:38.079><c> uh</c>

00:01:38.350 --> 00:01:38.360 align:start position:0%
from Coca-Cola to small businesses uh
 

00:01:38.360 --> 00:01:40.950 align:start position:0%
from Coca-Cola to small businesses uh
like<00:01:38.520><c> the</c><00:01:38.640><c> sellers</c><00:01:39.000><c> on</c><00:01:39.240><c> eBay</c><00:01:39.920><c> are</c><00:01:40.240><c> integrating</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
like the sellers on eBay are integrating
 

00:01:40.960 --> 00:01:44.990 align:start position:0%
like the sellers on eBay are integrating
L<00:01:41.240><c> rage</c><00:01:41.520><c> models</c><00:01:42.520><c> uh</c><00:01:42.680><c> in</c><00:01:42.880><c> their</c><00:01:43.200><c> daily</c><00:01:43.520><c> business</c>

00:01:44.990 --> 00:01:45.000 align:start position:0%
L rage models uh in their daily business
 

00:01:45.000 --> 00:01:48.310 align:start position:0%
L rage models uh in their daily business
operations<00:01:46.000><c> so</c><00:01:46.320><c> as</c><00:01:46.560><c> Scholars</c><00:01:47.479><c> and</c>

00:01:48.310 --> 00:01:48.320 align:start position:0%
operations so as Scholars and
 

00:01:48.320 --> 00:01:50.749 align:start position:0%
operations so as Scholars and
practitioners<00:01:49.119><c> are</c><00:01:49.439><c> starting</c><00:01:49.880><c> to</c><00:01:50.119><c> explore</c>

00:01:50.749 --> 00:01:50.759 align:start position:0%
practitioners are starting to explore
 

00:01:50.759 --> 00:01:53.230 align:start position:0%
practitioners are starting to explore
the<00:01:50.960><c> capabilities</c><00:01:51.560><c> of</c><00:01:51.759><c> L</c><00:01:52.000><c> langage</c><00:01:52.320><c> models</c><00:01:53.119><c> it</c>

00:01:53.230 --> 00:01:53.240 align:start position:0%
the capabilities of L langage models it
 

00:01:53.240 --> 00:01:55.310 align:start position:0%
the capabilities of L langage models it
has<00:01:53.439><c> become</c><00:01:53.880><c> clear</c><00:01:54.200><c> that</c><00:01:54.360><c> there</c><00:01:54.479><c> are</c><00:01:54.759><c> two</c>

00:01:55.310 --> 00:01:55.320 align:start position:0%
has become clear that there are two
 

00:01:55.320 --> 00:01:58.029 align:start position:0%
has become clear that there are two
distinct<00:01:55.840><c> modalities</c><00:01:56.600><c> of</c><00:01:56.880><c> utilizing</c><00:01:57.840><c> these</c>

00:01:58.029 --> 00:01:58.039 align:start position:0%
distinct modalities of utilizing these
 

00:01:58.039 --> 00:02:03.069 align:start position:0%
distinct modalities of utilizing these
large<00:01:58.320><c> language</c><00:01:58.840><c> models</c><00:01:59.920><c> so</c><00:02:00.719><c> in</c><00:02:01.000><c> one</c><00:02:01.320><c> way</c>

00:02:03.069 --> 00:02:03.079 align:start position:0%
large language models so in one way
 

00:02:03.079 --> 00:02:06.310 align:start position:0%
large language models so in one way
um<00:02:04.079><c> we</c><00:02:04.200><c> can</c><00:02:04.360><c> use</c><00:02:04.680><c> last</c><00:02:05.240><c> models</c><00:02:05.560><c> to</c><00:02:05.840><c> directly</c>

00:02:06.310 --> 00:02:06.320 align:start position:0%
um we can use last models to directly
 

00:02:06.320 --> 00:02:08.589 align:start position:0%
um we can use last models to directly
generate<00:02:06.719><c> the</c><00:02:06.920><c> content</c><00:02:07.320><c> that</c><00:02:07.439><c> we</c><00:02:07.560><c> want</c><00:02:08.360><c> so</c>

00:02:08.589 --> 00:02:08.599 align:start position:0%
generate the content that we want so
 

00:02:08.599 --> 00:02:10.550 align:start position:0%
generate the content that we want so
it's<00:02:08.759><c> similar</c><00:02:09.119><c> to</c><00:02:09.319><c> how</c><00:02:09.479><c> we</c><00:02:09.720><c> hire</c><00:02:10.039><c> a</c><00:02:10.239><c> ghost</c>

00:02:10.550 --> 00:02:10.560 align:start position:0%
it's similar to how we hire a ghost
 

00:02:10.560 --> 00:02:13.270 align:start position:0%
it's similar to how we hire a ghost
riter<00:02:11.520><c> uh</c><00:02:11.720><c> who</c><00:02:11.920><c> does</c><00:02:12.160><c> the</c><00:02:12.319><c> work</c><00:02:12.520><c> for</c><00:02:12.760><c> us</c><00:02:13.080><c> where</c>

00:02:13.270 --> 00:02:13.280 align:start position:0%
riter uh who does the work for us where
 

00:02:13.280 --> 00:02:14.630 align:start position:0%
riter uh who does the work for us where
we<00:02:13.480><c> give</c><00:02:13.720><c> high</c><00:02:13.879><c> level</c>

00:02:14.630 --> 00:02:14.640 align:start position:0%
we give high level
 

00:02:14.640 --> 00:02:17.229 align:start position:0%
we give high level
instructions<00:02:15.640><c> and</c><00:02:15.840><c> another</c><00:02:16.120><c> modality</c><00:02:16.879><c> is</c>

00:02:17.229 --> 00:02:17.239 align:start position:0%
instructions and another modality is
 

00:02:17.239 --> 00:02:19.470 align:start position:0%
instructions and another modality is
known<00:02:17.640><c> as</c><00:02:17.879><c> the</c><00:02:18.080><c> sounding</c><00:02:18.480><c> more</c><00:02:18.720><c> modality</c>

00:02:19.470 --> 00:02:19.480 align:start position:0%
known as the sounding more modality
 

00:02:19.480 --> 00:02:22.030 align:start position:0%
known as the sounding more modality
where<00:02:19.640><c> we</c><00:02:19.800><c> do</c><00:02:20.000><c> not</c><00:02:20.280><c> actually</c><00:02:20.640><c> ask</c><00:02:20.959><c> the</c><00:02:21.160><c> L</c><00:02:21.680><c> to</c><00:02:21.840><c> do</c>

00:02:22.030 --> 00:02:22.040 align:start position:0%
where we do not actually ask the L to do
 

00:02:22.040 --> 00:02:24.430 align:start position:0%
where we do not actually ask the L to do
the<00:02:22.200><c> job</c><00:02:22.360><c> for</c><00:02:22.560><c> us</c><00:02:23.040><c> but</c><00:02:23.280><c> rather</c><00:02:23.519><c> to</c><00:02:23.840><c> asking</c><00:02:24.239><c> for</c>

00:02:24.430 --> 00:02:24.440 align:start position:0%
the job for us but rather to asking for
 

00:02:24.440 --> 00:02:27.190 align:start position:0%
the job for us but rather to asking for
feedback<00:02:24.959><c> after</c><00:02:25.280><c> we</c><00:02:25.519><c> have</c><00:02:26.080><c> done</c><00:02:26.319><c> the</c>

00:02:27.190 --> 00:02:27.200 align:start position:0%
feedback after we have done the
 

00:02:27.200 --> 00:02:30.670 align:start position:0%
feedback after we have done the
work<00:02:28.200><c> so</c><00:02:28.480><c> one</c><00:02:28.640><c> of</c><00:02:28.840><c> the</c><00:02:29.000><c> unique</c><00:02:29.400><c> capab</c><00:02:30.200><c> of</c><00:02:30.400><c> L</c>

00:02:30.670 --> 00:02:30.680 align:start position:0%
work so one of the unique capab of L
 

00:02:30.680 --> 00:02:33.830 align:start position:0%
work so one of the unique capab of L
language<00:02:31.080><c> models</c><00:02:31.720><c> is</c><00:02:32.120><c> its</c><00:02:32.440><c> ability</c><00:02:33.040><c> to</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
language models is its ability to
 

00:02:33.840 --> 00:02:36.670 align:start position:0%
language models is its ability to
understand<00:02:34.200><c> natural</c><00:02:34.680><c> language</c><00:02:35.480><c> and</c><00:02:35.680><c> Al</c>

00:02:36.670 --> 00:02:36.680 align:start position:0%
understand natural language and Al
 

00:02:36.680 --> 00:02:39.309 align:start position:0%
understand natural language and Al
astonishingly<00:02:37.680><c> coherent</c><00:02:38.239><c> and</c><00:02:38.480><c> high</c><00:02:38.680><c> quality</c>

00:02:39.309 --> 00:02:39.319 align:start position:0%
astonishingly coherent and high quality
 

00:02:39.319 --> 00:02:41.790 align:start position:0%
astonishingly coherent and high quality
outputs<00:02:40.319><c> and</c><00:02:40.480><c> therefore</c><00:02:40.800><c> L</c><00:02:41.040><c> Lang</c><00:02:41.360><c> models</c><00:02:41.680><c> has</c>

00:02:41.790 --> 00:02:41.800 align:start position:0%
outputs and therefore L Lang models has
 

00:02:41.800 --> 00:02:45.110 align:start position:0%
outputs and therefore L Lang models has
been<00:02:42.000><c> used</c><00:02:42.319><c> in</c><00:02:42.440><c> the</c><00:02:42.640><c> host</c><00:02:43.080><c> of</c><00:02:43.560><c> creative</c><00:02:44.120><c> task</c>

00:02:45.110 --> 00:02:45.120 align:start position:0%
been used in the host of creative task
 

00:02:45.120 --> 00:02:47.630 align:start position:0%
been used in the host of creative task
such<00:02:45.280><c> as</c><00:02:45.480><c> running</c><00:02:45.959><c> songs</c><00:02:46.480><c> writing</c><00:02:46.959><c> poem</c><00:02:47.480><c> or</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
such as running songs writing poem or
 

00:02:47.640 --> 00:02:50.309 align:start position:0%
such as running songs writing poem or
even<00:02:47.920><c> writing</c><00:02:48.280><c> ad</c><00:02:48.760><c> copies</c><00:02:49.760><c> which</c><00:02:49.920><c> are</c><00:02:50.120><c> the</c>

00:02:50.309 --> 00:02:50.319 align:start position:0%
even writing ad copies which are the
 

00:02:50.319 --> 00:02:51.990 align:start position:0%
even writing ad copies which are the
tasks<00:02:50.680><c> that</c><00:02:50.800><c> were</c><00:02:51.040><c> thought</c><00:02:51.280><c> to</c><00:02:51.440><c> be</c><00:02:51.680><c> quite</c>

00:02:51.990 --> 00:02:52.000 align:start position:0%
tasks that were thought to be quite
 

00:02:52.000 --> 00:02:54.070 align:start position:0%
tasks that were thought to be quite
impossible<00:02:52.560><c> to</c><00:02:52.680><c> be</c><00:02:52.879><c> tackled</c><00:02:53.319><c> by</c><00:02:53.480><c> the</c><00:02:53.599><c> previous</c>

00:02:54.070 --> 00:02:54.080 align:start position:0%
impossible to be tackled by the previous
 

00:02:54.080 --> 00:02:57.790 align:start position:0%
impossible to be tackled by the previous
generations<00:02:54.720><c> of</c><00:02:55.239><c> machine</c><00:02:55.760><c> models</c><00:02:56.519><c> or</c><00:02:57.519><c> but</c>

00:02:57.790 --> 00:02:57.800 align:start position:0%
generations of machine models or but
 

00:02:57.800 --> 00:03:00.509 align:start position:0%
generations of machine models or but
that<00:02:57.959><c> being</c><00:02:58.400><c> said</c><00:02:59.400><c> there's</c><00:02:59.879><c> little</c><00:03:00.159><c> that</c><00:03:00.319><c> we</c>

00:03:00.509 --> 00:03:00.519 align:start position:0%
that being said there's little that we
 

00:03:00.519 --> 00:03:03.550 align:start position:0%
that being said there's little that we
know<00:03:01.040><c> about</c><00:03:01.640><c> how</c><00:03:01.840><c> large</c><00:03:02.159><c> D</c><00:03:02.560><c> models</c><00:03:03.120><c> can</c>

00:03:03.550 --> 00:03:03.560 align:start position:0%
know about how large D models can
 

00:03:03.560 --> 00:03:05.830 align:start position:0%
know about how large D models can
interact<00:03:04.080><c> with</c><00:03:04.319><c> humans</c><00:03:04.799><c> and</c><00:03:05.040><c> affect</c><00:03:05.400><c> humans</c>

00:03:05.830 --> 00:03:05.840 align:start position:0%
interact with humans and affect humans
 

00:03:05.840 --> 00:03:08.430 align:start position:0%
interact with humans and affect humans
behavior<00:03:06.280><c> and</c><00:03:07.080><c> performance</c><00:03:08.080><c> and</c><00:03:08.280><c> in</c>

00:03:08.430 --> 00:03:08.440 align:start position:0%
behavior and performance and in
 

00:03:08.440 --> 00:03:11.949 align:start position:0%
behavior and performance and in
particular<00:03:08.959><c> there</c><00:03:09.080><c> are</c><00:03:09.480><c> several</c><00:03:10.239><c> um</c><00:03:11.120><c> super</c>

00:03:11.949 --> 00:03:11.959 align:start position:0%
particular there are several um super
 

00:03:11.959 --> 00:03:14.229 align:start position:0%
particular there are several um super
classical<00:03:12.560><c> effects</c><00:03:13.159><c> that</c><00:03:13.280><c> has</c><00:03:13.440><c> been</c><00:03:13.640><c> found</c><00:03:14.040><c> in</c>

00:03:14.229 --> 00:03:14.239 align:start position:0%
classical effects that has been found in
 

00:03:14.239 --> 00:03:17.550 align:start position:0%
classical effects that has been found in
the<00:03:14.680><c> human</c><00:03:15.159><c> AI</c><00:03:15.519><c> gen</c><00:03:16.080><c> uh</c><00:03:16.440><c> interaction</c><00:03:17.440><c> uh</c>

00:03:17.550 --> 00:03:17.560 align:start position:0%
the human AI gen uh interaction uh
 

00:03:17.560 --> 00:03:21.070 align:start position:0%
the human AI gen uh interaction uh
literature<00:03:18.440><c> so</c><00:03:18.640><c> for</c><00:03:19.040><c> one</c><00:03:20.040><c> there</c><00:03:20.239><c> is</c><00:03:20.440><c> a</c><00:03:20.640><c> bias</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
literature so for one there is a bias
 

00:03:21.080 --> 00:03:23.270 align:start position:0%
literature so for one there is a bias
called<00:03:21.319><c> the</c><00:03:21.480><c> algorithm</c><00:03:22.040><c> rsion</c><00:03:22.840><c> where</c><00:03:23.080><c> there's</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
called the algorithm rsion where there's
 

00:03:23.280 --> 00:03:27.110 align:start position:0%
called the algorithm rsion where there's
a<00:03:23.480><c> tendency</c><00:03:23.959><c> for</c><00:03:24.159><c> human</c><00:03:24.440><c> to</c><00:03:24.760><c> distrust</c><00:03:25.760><c> AI</c><00:03:26.760><c> even</c>

00:03:27.110 --> 00:03:27.120 align:start position:0%
a tendency for human to distrust AI even
 

00:03:27.120 --> 00:03:29.309 align:start position:0%
a tendency for human to distrust AI even
though<00:03:27.440><c> AI</c><00:03:28.000><c> is</c><00:03:28.319><c> objectively</c><00:03:28.879><c> better</c><00:03:29.159><c> or</c>

00:03:29.309 --> 00:03:29.319 align:start position:0%
though AI is objectively better or
 

00:03:29.319 --> 00:03:30.470 align:start position:0%
though AI is objectively better or
correct

00:03:30.470 --> 00:03:30.480 align:start position:0%
correct
 

00:03:30.480 --> 00:03:32.229 align:start position:0%
correct
so<00:03:30.760><c> this</c><00:03:30.959><c> effect</c><00:03:31.280><c> is</c><00:03:31.400><c> found</c><00:03:31.680><c> to</c><00:03:31.799><c> be</c><00:03:31.959><c> more</c>

00:03:32.229 --> 00:03:32.239 align:start position:0%
so this effect is found to be more
 

00:03:32.239 --> 00:03:34.190 align:start position:0%
so this effect is found to be more
pronounced<00:03:32.720><c> for</c><00:03:33.040><c> experts</c><00:03:33.519><c> who</c><00:03:33.680><c> are</c><00:03:33.879><c> more</c>

00:03:34.190 --> 00:03:34.200 align:start position:0%
pronounced for experts who are more
 

00:03:34.200 --> 00:03:36.470 align:start position:0%
pronounced for experts who are more
confident<00:03:34.840><c> in</c><00:03:35.000><c> their</c><00:03:35.159><c> own</c>

00:03:36.470 --> 00:03:36.480 align:start position:0%
confident in their own
 

00:03:36.480 --> 00:03:39.550 align:start position:0%
confident in their own
capabilities<00:03:37.480><c> but</c><00:03:38.239><c> uh</c><00:03:38.360><c> for</c><00:03:38.560><c> non</c><00:03:38.959><c> experts</c><00:03:39.439><c> who</c>

00:03:39.550 --> 00:03:39.560 align:start position:0%
capabilities but uh for non experts who
 

00:03:39.560 --> 00:03:41.309 align:start position:0%
capabilities but uh for non experts who
are<00:03:39.959><c> less</c><00:03:40.239><c> knowledgeable</c><00:03:40.799><c> in</c><00:03:40.920><c> their</c><00:03:41.080><c> own</c>

00:03:41.309 --> 00:03:41.319 align:start position:0%
are less knowledgeable in their own
 

00:03:41.319 --> 00:03:43.630 align:start position:0%
are less knowledgeable in their own
domains<00:03:41.760><c> they</c><00:03:41.879><c> may</c><00:03:42.159><c> actually</c><00:03:42.519><c> rely</c><00:03:43.080><c> more</c><00:03:43.360><c> on</c>

00:03:43.630 --> 00:03:43.640 align:start position:0%
domains they may actually rely more on
 

00:03:43.640 --> 00:03:46.470 align:start position:0%
domains they may actually rely more on
AI<00:03:44.040><c> and</c><00:03:44.200><c> therefore</c><00:03:44.560><c> benefit</c><00:03:45.040><c> from</c><00:03:45.200><c> the</c><00:03:45.480><c> usage</c>

00:03:46.470 --> 00:03:46.480 align:start position:0%
AI and therefore benefit from the usage
 

00:03:46.480 --> 00:03:50.229 align:start position:0%
AI and therefore benefit from the usage
of<00:03:46.760><c> AI</c><00:03:47.760><c> but</c><00:03:47.879><c> on</c><00:03:48.000><c> the</c><00:03:48.159><c> flip</c><00:03:48.599><c> side</c><00:03:49.080><c> when</c><00:03:49.360><c> human</c><00:03:49.840><c> do</c>

00:03:50.229 --> 00:03:50.239 align:start position:0%
of AI but on the flip side when human do
 

00:03:50.239 --> 00:03:54.229 align:start position:0%
of AI but on the flip side when human do
Char<00:03:51.239><c> the</c><00:03:51.640><c> output</c><00:03:52.120><c> from</c><00:03:52.360><c> AI</c><00:03:53.280><c> the</c><00:03:53.720><c> anchoring</c>

00:03:54.229 --> 00:03:54.239 align:start position:0%
Char the output from AI the anchoring
 

00:03:54.239 --> 00:03:55.949 align:start position:0%
Char the output from AI the anchoring
effects<00:03:54.599><c> can</c><00:03:54.799><c> come</c><00:03:55.000><c> into</c><00:03:55.239><c> play</c><00:03:55.560><c> which</c><00:03:55.680><c> is</c><00:03:55.760><c> a</c>

00:03:55.949 --> 00:03:55.959 align:start position:0%
effects can come into play which is a
 

00:03:55.959 --> 00:03:58.990 align:start position:0%
effects can come into play which is a
cognitive<00:03:56.640><c> bias</c><00:03:57.239><c> where</c><00:03:57.599><c> the</c><00:03:58.239><c> uh</c><00:03:58.400><c> humans</c>

00:03:58.990 --> 00:03:59.000 align:start position:0%
cognitive bias where the uh humans
 

00:03:59.000 --> 00:04:01.069 align:start position:0%
cognitive bias where the uh humans
subsequent<00:03:59.680><c> behavior</c><00:04:00.079><c> is</c><00:04:00.319><c> anchored</c><00:04:00.799><c> by</c><00:04:00.920><c> the</c>

00:04:01.069 --> 00:04:01.079 align:start position:0%
subsequent behavior is anchored by the
 

00:04:01.079 --> 00:04:05.270 align:start position:0%
subsequent behavior is anchored by the
as<00:04:01.439><c> outputs</c><00:04:02.319><c> so</c><00:04:02.560><c> this</c><00:04:02.799><c> effect</c><00:04:03.280><c> has</c><00:04:03.480><c> been</c><00:04:04.319><c> found</c>

00:04:05.270 --> 00:04:05.280 align:start position:0%
as outputs so this effect has been found
 

00:04:05.280 --> 00:04:07.670 align:start position:0%
as outputs so this effect has been found
to<00:04:05.439><c> be</c><00:04:05.720><c> univ</c><00:04:06.599><c> in</c><00:04:06.840><c> the</c><00:04:07.000><c> decision-</c><00:04:07.360><c> making</c>

00:04:07.670 --> 00:04:07.680 align:start position:0%
to be univ in the decision- making
 

00:04:07.680 --> 00:04:10.149 align:start position:0%
to be univ in the decision- making
context<00:04:08.360><c> but</c><00:04:08.560><c> recently</c><00:04:09.159><c> people</c><00:04:09.480><c> also</c><00:04:09.799><c> found</c>

00:04:10.149 --> 00:04:10.159 align:start position:0%
context but recently people also found
 

00:04:10.159 --> 00:04:12.750 align:start position:0%
context but recently people also found
that<00:04:10.360><c> this</c><00:04:10.480><c> is</c><00:04:11.120><c> also</c><00:04:11.439><c> true</c><00:04:11.799><c> in</c><00:04:11.920><c> the</c><00:04:12.079><c> creative</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
that this is also true in the creative
 

00:04:12.760 --> 00:04:15.630 align:start position:0%
that this is also true in the creative
process<00:04:13.760><c> and</c><00:04:13.920><c> interestingly</c><00:04:14.760><c> this</c><00:04:15.040><c> effect</c><00:04:15.439><c> is</c>

00:04:15.630 --> 00:04:15.640 align:start position:0%
process and interestingly this effect is
 

00:04:15.640 --> 00:04:19.150 align:start position:0%
process and interestingly this effect is
found<00:04:15.879><c> to</c><00:04:16.040><c> be</c><00:04:16.639><c> smaller</c><00:04:17.320><c> for</c><00:04:17.720><c> experts</c><00:04:18.720><c> so</c><00:04:18.959><c> with</c>

00:04:19.150 --> 00:04:19.160 align:start position:0%
found to be smaller for experts so with
 

00:04:19.160 --> 00:04:22.350 align:start position:0%
found to be smaller for experts so with
this<00:04:19.519><c> competing</c><00:04:20.519><c> different</c><00:04:21.079><c> perspectives</c><00:04:22.079><c> of</c>

00:04:22.350 --> 00:04:22.360 align:start position:0%
this competing different perspectives of
 

00:04:22.360 --> 00:04:24.950 align:start position:0%
this competing different perspectives of
how<00:04:22.639><c> non</c><00:04:23.000><c> experts</c><00:04:23.440><c> and</c><00:04:23.800><c> experts</c><00:04:24.560><c> should</c>

00:04:24.950 --> 00:04:24.960 align:start position:0%
how non experts and experts should
 

00:04:24.960 --> 00:04:28.629 align:start position:0%
how non experts and experts should
utilize<00:04:25.840><c> AI</c><00:04:26.840><c> uh</c><00:04:27.639><c> you</c><00:04:27.759><c> know</c><00:04:27.919><c> should</c><00:04:28.120><c> utilize</c>

00:04:28.629 --> 00:04:28.639 align:start position:0%
utilize AI uh you know should utilize
 

00:04:28.639 --> 00:04:30.950 align:start position:0%
utilize AI uh you know should utilize
lar<00:04:29.199><c> models</c><00:04:29.639><c> in</c><00:04:29.800><c> Creative</c>

00:04:30.950 --> 00:04:30.960 align:start position:0%
lar models in Creative
 

00:04:30.960 --> 00:04:34.590 align:start position:0%
lar models in Creative
work<00:04:32.039><c> there's</c><00:04:33.039><c> there</c><00:04:33.160><c> is</c><00:04:33.360><c> no</c><00:04:34.039><c> uh</c><00:04:34.199><c> clear</c>

00:04:34.590 --> 00:04:34.600 align:start position:0%
work there's there is no uh clear
 

00:04:34.600 --> 00:04:37.390 align:start position:0%
work there's there is no uh clear
guideline<00:04:35.240><c> how</c><00:04:35.800><c> they</c><00:04:35.919><c> should</c><00:04:36.520><c> utilize</c><00:04:36.919><c> Master</c>

00:04:37.390 --> 00:04:37.400 align:start position:0%
guideline how they should utilize Master
 

00:04:37.400 --> 00:04:40.390 align:start position:0%
guideline how they should utilize Master
models<00:04:37.960><c> therefore</c><00:04:38.680><c> we</c><00:04:38.919><c> set</c><00:04:39.320><c> out</c><00:04:39.720><c> to</c><00:04:40.080><c> answer</c>

00:04:40.390 --> 00:04:40.400 align:start position:0%
models therefore we set out to answer
 

00:04:40.400 --> 00:04:43.110 align:start position:0%
models therefore we set out to answer
the<00:04:40.520><c> following</c><00:04:40.840><c> two</c><00:04:41.120><c> B</c><00:04:41.560><c> questions</c><00:04:42.560><c> first</c>

00:04:43.110 --> 00:04:43.120 align:start position:0%
the following two B questions first
 

00:04:43.120 --> 00:04:45.029 align:start position:0%
the following two B questions first
which<00:04:43.560><c> human</c><00:04:44.080><c> large</c><00:04:44.360><c> range</c><00:04:44.680><c> models</c>

00:04:45.029 --> 00:04:45.039 align:start position:0%
which human large range models
 

00:04:45.039 --> 00:04:47.550 align:start position:0%
which human large range models
collaboration<00:04:45.520><c> modality</c><00:04:46.240><c> works</c><00:04:46.759><c> better</c><00:04:47.240><c> for</c>

00:04:47.550 --> 00:04:47.560 align:start position:0%
collaboration modality works better for
 

00:04:47.560 --> 00:04:49.830 align:start position:0%
collaboration modality works better for
Creative<00:04:48.000><c> work</c><00:04:48.560><c> and</c><00:04:48.840><c> go</c>

00:04:49.830 --> 00:04:49.840 align:start position:0%
Creative work and go
 

00:04:49.840 --> 00:04:52.310 align:start position:0%
Creative work and go
who<00:04:50.840><c> so</c><00:04:51.120><c> there</c><00:04:51.280><c> have</c><00:04:51.520><c> been</c><00:04:51.759><c> some</c><00:04:52.000><c> early</c>

00:04:52.310 --> 00:04:52.320 align:start position:0%
who so there have been some early
 

00:04:52.320 --> 00:04:54.550 align:start position:0%
who so there have been some early
attempts<00:04:52.840><c> in</c><00:04:53.199><c> you</c><00:04:53.280><c> know</c><00:04:53.600><c> studying</c><00:04:54.160><c> the</c>

00:04:54.550 --> 00:04:54.560 align:start position:0%
attempts in you know studying the
 

00:04:54.560 --> 00:04:57.150 align:start position:0%
attempts in you know studying the
effects<00:04:54.960><c> of</c><00:04:55.160><c> large</c><00:04:55.479><c> V</c><00:04:55.840><c> models</c><00:04:56.199><c> on</c><00:04:56.400><c> human</c>

00:04:57.150 --> 00:04:57.160 align:start position:0%
effects of large V models on human
 

00:04:57.160 --> 00:05:00.670 align:start position:0%
effects of large V models on human
productivity<00:04:58.560><c> um</c><00:04:59.600><c> some</c><00:04:59.840><c> earlier</c><00:05:00.160><c> work</c><00:05:00.479><c> has</c>

00:05:00.670 --> 00:05:00.680 align:start position:0%
productivity um some earlier work has
 

00:05:00.680 --> 00:05:03.790 align:start position:0%
productivity um some earlier work has
found<00:05:01.120><c> that</c><00:05:01.639><c> U</c><00:05:01.919><c> large</c><00:05:02.240><c> D</c><00:05:02.560><c> models</c><00:05:02.960><c> can</c><00:05:03.280><c> increase</c>

00:05:03.790 --> 00:05:03.800 align:start position:0%
found that U large D models can increase
 

00:05:03.800 --> 00:05:06.110 align:start position:0%
found that U large D models can increase
the<00:05:03.960><c> speed</c><00:05:04.440><c> of</c><00:05:04.680><c> completing</c><00:05:05.400><c> you</c><00:05:05.520><c> know</c>

00:05:06.110 --> 00:05:06.120 align:start position:0%
the speed of completing you know
 

00:05:06.120 --> 00:05:09.270 align:start position:0%
the speed of completing you know
relative<00:05:06.960><c> routine</c><00:05:07.560><c> or</c><00:05:07.759><c> standard</c><00:05:08.160><c> Tas</c><00:05:08.800><c> such</c><00:05:09.000><c> as</c>

00:05:09.270 --> 00:05:09.280 align:start position:0%
relative routine or standard Tas such as
 

00:05:09.280 --> 00:05:11.110 align:start position:0%
relative routine or standard Tas such as
report<00:05:09.639><c> writing</c><00:05:10.199><c> customer</c><00:05:10.720><c> question</c>

00:05:11.110 --> 00:05:11.120 align:start position:0%
report writing customer question
 

00:05:11.120 --> 00:05:13.710 align:start position:0%
report writing customer question
answering<00:05:12.000><c> and</c><00:05:12.520><c> further</c><00:05:12.880><c> more</c><00:05:13.120><c> improve</c><00:05:13.560><c> the</c>

00:05:13.710 --> 00:05:13.720 align:start position:0%
answering and further more improve the
 

00:05:13.720 --> 00:05:15.270 align:start position:0%
answering and further more improve the
quality<00:05:14.160><c> of</c><00:05:14.320><c> human</c>

00:05:15.270 --> 00:05:15.280 align:start position:0%
quality of human
 

00:05:15.280 --> 00:05:17.550 align:start position:0%
quality of human
outputs<00:05:16.280><c> and</c><00:05:16.639><c> they</c><00:05:16.840><c> also</c><00:05:17.080><c> found</c><00:05:17.360><c> that</c>

00:05:17.550 --> 00:05:17.560 align:start position:0%
outputs and they also found that
 

00:05:17.560 --> 00:05:19.430 align:start position:0%
outputs and they also found that
inexperienced<00:05:18.319><c> worker</c><00:05:18.680><c> actually</c><00:05:18.960><c> benefits</c>

00:05:19.430 --> 00:05:19.440 align:start position:0%
inexperienced worker actually benefits
 

00:05:19.440 --> 00:05:23.790 align:start position:0%
inexperienced worker actually benefits
more<00:05:19.720><c> from</c><00:05:19.880><c> the</c><00:05:20.000><c> usage</c><00:05:20.319><c> of</c><00:05:20.680><c> L</c><00:05:21.479><c> models</c><00:05:22.800><c> but</c>

00:05:23.790 --> 00:05:23.800 align:start position:0%
more from the usage of L models but
 

00:05:23.800 --> 00:05:26.230 align:start position:0%
more from the usage of L models but
these<00:05:24.240><c> Studies</c><00:05:24.720><c> have</c><00:05:24.880><c> not</c><00:05:25.080><c> looked</c><00:05:25.319><c> at</c><00:05:25.680><c> how</c>

00:05:26.230 --> 00:05:26.240 align:start position:0%
these Studies have not looked at how
 

00:05:26.240 --> 00:05:29.670 align:start position:0%
these Studies have not looked at how
human<00:05:26.720><c> engage</c><00:05:27.199><c> with</c><00:05:27.400><c> L</c><00:05:27.919><c> models</c><00:05:28.840><c> and</c><00:05:29.120><c> there's</c>

00:05:29.670 --> 00:05:29.680 align:start position:0%
human engage with L models and there's
 

00:05:29.680 --> 00:05:31.749 align:start position:0%
human engage with L models and there's
is<00:05:29.800><c> do</c><00:05:30.000><c> not</c><00:05:30.240><c> tackle</c><00:05:30.680><c> more</c><00:05:31.039><c> creative</c><00:05:31.479><c> kind</c><00:05:31.639><c> of</c>

00:05:31.749 --> 00:05:31.759 align:start position:0%
is do not tackle more creative kind of
 

00:05:31.759 --> 00:05:35.070 align:start position:0%
is do not tackle more creative kind of
talk<00:05:32.120><c> such</c><00:05:32.280><c> as</c><00:05:32.400><c> songwriting</c><00:05:33.120><c> you</c><00:05:33.240><c> know</c><00:05:33.479><c> or</c><00:05:33.800><c> ad</c>

00:05:35.070 --> 00:05:35.080 align:start position:0%
talk such as songwriting you know or ad
 

00:05:35.080 --> 00:05:36.990 align:start position:0%
talk such as songwriting you know or ad
copywriting

00:05:36.990 --> 00:05:37.000 align:start position:0%
copywriting
 

00:05:37.000 --> 00:05:39.749 align:start position:0%
copywriting
so<00:05:38.000><c> so</c><00:05:38.199><c> we</c><00:05:38.319><c> set</c><00:05:38.560><c> out</c><00:05:38.720><c> to</c><00:05:38.880><c> answer</c><00:05:39.160><c> our</c><00:05:39.400><c> written</c>

00:05:39.749 --> 00:05:39.759 align:start position:0%
so so we set out to answer our written
 

00:05:39.759 --> 00:05:42.270 align:start position:0%
so so we set out to answer our written
questions<00:05:40.120><c> using</c><00:05:40.440><c> a</c><00:05:40.639><c> control</c><00:05:41.120><c> experiment</c><00:05:42.120><c> we</c>

00:05:42.270 --> 00:05:42.280 align:start position:0%
questions using a control experiment we
 

00:05:42.280 --> 00:05:44.790 align:start position:0%
questions using a control experiment we
choose<00:05:42.680><c> the</c><00:05:43.199><c> uh</c><00:05:43.319><c> add</c><00:05:43.639><c> copywriting</c><00:05:44.280><c> talkx</c><00:05:44.639><c> as</c>

00:05:44.790 --> 00:05:44.800 align:start position:0%
choose the uh add copywriting talkx as
 

00:05:44.800 --> 00:05:48.029 align:start position:0%
choose the uh add copywriting talkx as
our<00:05:45.120><c> creative</c><00:05:45.680><c> context</c><00:05:46.680><c> so</c><00:05:46.960><c> advertising</c><00:05:47.759><c> is</c><00:05:47.880><c> a</c>

00:05:48.029 --> 00:05:48.039 align:start position:0%
our creative context so advertising is a
 

00:05:48.039 --> 00:05:50.029 align:start position:0%
our creative context so advertising is a
humongous<00:05:48.600><c> industry</c><00:05:49.160><c> that</c><00:05:49.360><c> was</c><00:05:49.759><c> you</c><00:05:49.840><c> know</c>

00:05:50.029 --> 00:05:50.039 align:start position:0%
humongous industry that was you know
 

00:05:50.039 --> 00:05:53.230 align:start position:0%
humongous industry that was you know
$180<00:05:50.800><c> billion</c><00:05:51.440><c> in</c><00:05:51.560><c> 2020</c><00:05:52.120><c> to</c><00:05:52.639><c> United</c><00:05:52.960><c> States</c>

00:05:53.230 --> 00:05:53.240 align:start position:0%
$180 billion in 2020 to United States
 

00:05:53.240 --> 00:05:56.110 align:start position:0%
$180 billion in 2020 to United States
along<00:05:54.240><c> and</c><00:05:54.440><c> there</c><00:05:54.639><c> has</c><00:05:54.880><c> been</c>

00:05:56.110 --> 00:05:56.120 align:start position:0%
along and there has been
 

00:05:56.120 --> 00:05:59.670 align:start position:0%
along and there has been
a<00:05:57.120><c> a</c><00:05:57.240><c> large</c><00:05:57.479><c> L</c><00:05:57.759><c> modor</c><00:05:58.080><c> based</c><00:05:58.759><c> uh</c><00:05:58.919><c> application</c>

00:05:59.670 --> 00:05:59.680 align:start position:0%
a a large L modor based uh application
 

00:05:59.680 --> 00:06:02.230 align:start position:0%
a a large L modor based uh application
has<00:05:59.840><c> been</c><00:06:00.319><c> super</c><00:06:00.720><c> popular</c><00:06:01.160><c> in</c><00:06:01.360><c> this</c><00:06:01.600><c> area</c><00:06:02.080><c> in</c>

00:06:02.230 --> 00:06:02.240 align:start position:0%
has been super popular in this area in
 

00:06:02.240 --> 00:06:04.870 align:start position:0%
has been super popular in this area in
which<00:06:02.680><c> people</c><00:06:02.960><c> use</c><00:06:03.199><c> it</c><00:06:03.360><c> to</c><00:06:03.800><c> generate</c><00:06:04.280><c> ad</c>

00:06:04.870 --> 00:06:04.880 align:start position:0%
which people use it to generate ad
 

00:06:04.880 --> 00:06:07.550 align:start position:0%
which people use it to generate ad
copies<00:06:05.880><c> for</c><00:06:06.039><c> example</c><00:06:06.400><c> these</c><00:06:06.520><c> are</c><00:06:06.720><c> the</c><00:06:06.840><c> two</c><00:06:07.440><c> you</c>

00:06:07.550 --> 00:06:07.560 align:start position:0%
copies for example these are the two you
 

00:06:07.560 --> 00:06:11.350 align:start position:0%
copies for example these are the two you
know<00:06:08.160><c> um</c><00:06:08.919><c> startups</c><00:06:09.520><c> that</c><00:06:09.720><c> has</c><00:06:10.520><c> gen</c><00:06:11.039><c> uh</c>

00:06:11.350 --> 00:06:11.360 align:start position:0%
know um startups that has gen uh
 

00:06:11.360 --> 00:06:13.950 align:start position:0%
know um startups that has gen uh
gathered<00:06:11.880><c> 101</c><00:06:12.479><c> billion</c><00:06:13.479><c> in</c><00:06:13.599><c> the</c><00:06:13.759><c> first</c>

00:06:13.950 --> 00:06:13.960 align:start position:0%
gathered 101 billion in the first
 

00:06:13.960 --> 00:06:16.830 align:start position:0%
gathered 101 billion in the first
quarter<00:06:14.240><c> of</c><00:06:14.360><c> 2023</c><00:06:15.240><c> so</c><00:06:15.479><c> given</c><00:06:15.720><c> the</c><00:06:15.919><c> significant</c>

00:06:16.830 --> 00:06:16.840 align:start position:0%
quarter of 2023 so given the significant
 

00:06:16.840 --> 00:06:18.909 align:start position:0%
quarter of 2023 so given the significant
uh<00:06:17.039><c> implication</c><00:06:17.680><c> in</c><00:06:17.880><c> business</c><00:06:18.240><c> outcomes</c><00:06:18.759><c> we</c>

00:06:18.909 --> 00:06:18.919 align:start position:0%
uh implication in business outcomes we
 

00:06:18.919 --> 00:06:21.430 align:start position:0%
uh implication in business outcomes we
choose<00:06:19.240><c> this</c><00:06:19.400><c> as</c><00:06:19.599><c> our</c><00:06:20.280><c> uh</c><00:06:20.400><c> creative</c>

00:06:21.430 --> 00:06:21.440 align:start position:0%
choose this as our uh creative
 

00:06:21.440 --> 00:06:24.469 align:start position:0%
choose this as our uh creative
task<00:06:22.440><c> and</c><00:06:22.720><c> in</c><00:06:22.960><c> addition</c><00:06:23.560><c> we</c><00:06:23.720><c> also</c><00:06:24.000><c> want</c><00:06:24.199><c> to</c>

00:06:24.469 --> 00:06:24.479 align:start position:0%
task and in addition we also want to
 

00:06:24.479 --> 00:06:27.909 align:start position:0%
task and in addition we also want to
have<00:06:24.599><c> a</c><00:06:24.800><c> more</c><00:06:25.440><c> objective</c><00:06:26.440><c> view</c><00:06:27.039><c> of</c><00:06:27.280><c> the</c><00:06:27.479><c> output</c>

00:06:27.909 --> 00:06:27.919 align:start position:0%
have a more objective view of the output
 

00:06:27.919 --> 00:06:30.589 align:start position:0%
have a more objective view of the output
quality<00:06:28.520><c> so</c><00:06:28.759><c> passws</c><00:06:29.479><c> has</c><00:06:29.599><c> been</c><00:06:29.759><c> relying</c>

00:06:30.589 --> 00:06:30.599 align:start position:0%
quality so passws has been relying
 

00:06:30.599 --> 00:06:33.830 align:start position:0%
quality so passws has been relying
primarily<00:06:31.160><c> on</c><00:06:31.400><c> human</c><00:06:31.680><c> readers</c><00:06:32.199><c> to</c><00:06:32.960><c> gauge</c><00:06:33.639><c> the</c>

00:06:33.830 --> 00:06:33.840 align:start position:0%
primarily on human readers to gauge the
 

00:06:33.840 --> 00:06:37.110 align:start position:0%
primarily on human readers to gauge the
out<00:06:34.240><c> quality</c><00:06:34.720><c> of</c><00:06:35.000><c> creative</c><00:06:35.960><c> open-ended</c><00:06:36.960><c> uh</c>

00:06:37.110 --> 00:06:37.120 align:start position:0%
out quality of creative open-ended uh
 

00:06:37.120 --> 00:06:39.629 align:start position:0%
out quality of creative open-ended uh
outputs<00:06:38.039><c> here</c><00:06:38.240><c> we</c><00:06:38.400><c> want</c><00:06:38.599><c> a</c><00:06:38.800><c> more</c><00:06:39.120><c> direct</c>

00:06:39.629 --> 00:06:39.639 align:start position:0%
outputs here we want a more direct
 

00:06:39.639 --> 00:06:42.510 align:start position:0%
outputs here we want a more direct
measure<00:06:40.360><c> by</c><00:06:40.520><c> sending</c><00:06:40.960><c> this</c><00:06:41.599><c> ads</c><00:06:41.960><c> out</c><00:06:42.160><c> to</c><00:06:42.319><c> the</c>

00:06:42.510 --> 00:06:42.520 align:start position:0%
measure by sending this ads out to the
 

00:06:42.520 --> 00:06:46.070 align:start position:0%
measure by sending this ads out to the
public<00:06:43.039><c> and</c><00:06:43.800><c> see</c><00:06:44.319><c> how</c><00:06:45.120><c> well</c><00:06:45.440><c> they</c><00:06:45.639><c> fair</c><00:06:45.960><c> in</c>

00:06:46.070 --> 00:06:46.080 align:start position:0%
public and see how well they fair in
 

00:06:46.080 --> 00:06:49.110 align:start position:0%
public and see how well they fair in
terms<00:06:46.319><c> of</c><00:06:46.479><c> generating</c><00:06:47.039><c> accx</c><00:06:47.720><c> in</c><00:06:47.840><c> the</c><00:06:48.000><c> real</c>

00:06:49.110 --> 00:06:49.120 align:start position:0%
terms of generating accx in the real
 

00:06:49.120 --> 00:06:54.230 align:start position:0%
terms of generating accx in the real
world<00:06:50.120><c> so</c><00:06:50.919><c> um</c><00:06:51.199><c> we</c><00:06:51.479><c> recruited</c><00:06:52.400><c> to</c><00:06:52.880><c> BR</c><00:06:53.479><c> exclusive</c>

00:06:54.230 --> 00:06:54.240 align:start position:0%
world so um we recruited to BR exclusive
 

00:06:54.240 --> 00:06:57.830 align:start position:0%
world so um we recruited to BR exclusive
po<00:06:54.759><c> of</c><00:06:55.000><c> participants</c><00:06:55.599><c> on</c><00:06:56.039><c> prolific</c><00:06:57.039><c> so</c><00:06:57.639><c> the</c>

00:06:57.830 --> 00:06:57.840 align:start position:0%
po of participants on prolific so the
 

00:06:57.840 --> 00:07:00.430 align:start position:0%
po of participants on prolific so the
expert<00:06:58.280><c> group</c><00:06:58.800><c> were</c><00:06:59.120><c> conf</c><00:06:59.400><c> ass</c><00:07:00.000><c> of</c><00:07:00.280><c> the</c>

00:07:00.430 --> 00:07:00.440 align:start position:0%
expert group were conf ass of the
 

00:07:00.440 --> 00:07:02.510 align:start position:0%
expert group were conf ass of the
participants<00:07:01.039><c> whose</c><00:07:01.400><c> occupations</c><00:07:02.280><c> were</c>

00:07:02.510 --> 00:07:02.520 align:start position:0%
participants whose occupations were
 

00:07:02.520 --> 00:07:05.070 align:start position:0%
participants whose occupations were
coded<00:07:03.080><c> with</c><00:07:03.520><c> marketing</c><00:07:03.960><c> and</c><00:07:04.160><c> sales</c><00:07:04.800><c> whereas</c>

00:07:05.070 --> 00:07:05.080 align:start position:0%
coded with marketing and sales whereas
 

00:07:05.080 --> 00:07:07.430 align:start position:0%
coded with marketing and sales whereas
the<00:07:05.240><c> non</c><00:07:05.479><c> expert</c><00:07:05.800><c> group</c><00:07:06.080><c> were</c><00:07:06.560><c> participants</c>

00:07:07.430 --> 00:07:07.440 align:start position:0%
the non expert group were participants
 

00:07:07.440 --> 00:07:09.670 align:start position:0%
the non expert group were participants
who<00:07:07.680><c> has</c><00:07:07.879><c> unrelated</c><00:07:08.560><c> occupation</c><00:07:09.199><c> such</c><00:07:09.400><c> as</c>

00:07:09.670 --> 00:07:09.680 align:start position:0%
who has unrelated occupation such as
 

00:07:09.680 --> 00:07:12.390 align:start position:0%
who has unrelated occupation such as
Finance<00:07:10.160><c> or</c><00:07:10.400><c> architecture</c><00:07:11.400><c> so</c><00:07:11.560><c> in</c><00:07:11.680><c> the</c><00:07:11.800><c> end</c><00:07:12.120><c> we</c>

00:07:12.390 --> 00:07:12.400 align:start position:0%
Finance or architecture so in the end we
 

00:07:12.400 --> 00:07:15.150 align:start position:0%
Finance or architecture so in the end we
have<00:07:12.919><c> 355</c><00:07:13.919><c> participants</c><00:07:14.400><c> in</c><00:07:14.479><c> the</c><00:07:14.599><c> whole</c>

00:07:15.150 --> 00:07:15.160 align:start position:0%
have 355 participants in the whole
 

00:07:15.160 --> 00:07:17.589 align:start position:0%
have 355 participants in the whole
experiment<00:07:16.160><c> and</c><00:07:16.319><c> we</c><00:07:16.479><c> have</c><00:07:16.720><c> three</c><00:07:17.080><c> AI</c>

00:07:17.589 --> 00:07:17.599 align:start position:0%
experiment and we have three AI
 

00:07:17.599 --> 00:07:19.710 align:start position:0%
experiment and we have three AI
conditions<00:07:18.199><c> the</c><00:07:18.360><c> control</c><00:07:18.840><c> group</c><00:07:19.280><c> does</c><00:07:19.479><c> not</c>

00:07:19.710 --> 00:07:19.720 align:start position:0%
conditions the control group does not
 

00:07:19.720 --> 00:07:22.270 align:start position:0%
conditions the control group does not
have<00:07:19.960><c> access</c><00:07:20.680><c> to</c><00:07:20.919><c> the</c><00:07:21.120><c> AI</c><00:07:21.759><c> when</c><00:07:21.919><c> they</c><00:07:22.039><c> are</c>

00:07:22.270 --> 00:07:22.280 align:start position:0%
have access to the AI when they are
 

00:07:22.280 --> 00:07:24.390 align:start position:0%
have access to the AI when they are
completing<00:07:22.840><c> the</c><00:07:22.960><c> add</c><00:07:23.240><c> copyrighting</c><00:07:23.759><c> TX</c>

00:07:24.390 --> 00:07:24.400 align:start position:0%
completing the add copyrighting TX
 

00:07:24.400 --> 00:07:27.390 align:start position:0%
completing the add copyrighting TX
whereas<00:07:25.280><c> the</c><00:07:25.440><c> treatment</c><00:07:25.960><c> groups</c><00:07:26.720><c> are</c>

00:07:27.390 --> 00:07:27.400 align:start position:0%
whereas the treatment groups are
 

00:07:27.400 --> 00:07:29.390 align:start position:0%
whereas the treatment groups are
assigned<00:07:27.879><c> with</c><00:07:28.120><c> two</c><00:07:28.440><c> different</c><00:07:29.039><c> uh</c>

00:07:29.390 --> 00:07:29.400 align:start position:0%
assigned with two different uh
 

00:07:29.400 --> 00:07:32.110 align:start position:0%
assigned with two different uh
modalities<00:07:30.000><c> of</c><00:07:30.160><c> using</c><00:07:30.759><c> large</c><00:07:31.080><c> langage</c><00:07:31.440><c> models</c>

00:07:32.110 --> 00:07:32.120 align:start position:0%
modalities of using large langage models
 

00:07:32.120 --> 00:07:35.230 align:start position:0%
modalities of using large langage models
when<00:07:32.360><c> they</c><00:07:32.680><c> write</c><00:07:33.039><c> at</c><00:07:33.599><c> copies</c><00:07:34.599><c> so</c><00:07:34.759><c> in</c><00:07:34.879><c> the</c><00:07:35.000><c> end</c>

00:07:35.230 --> 00:07:35.240 align:start position:0%
when they write at copies so in the end
 

00:07:35.240 --> 00:07:37.869 align:start position:0%
when they write at copies so in the end
we<00:07:35.360><c> have</c><00:07:35.520><c> a</c><00:07:35.720><c> 3X</c><00:07:36.199><c> two</c><00:07:36.879><c> uh</c><00:07:37.000><c> you</c><00:07:37.120><c> know</c><00:07:37.479><c> between</c>

00:07:37.869 --> 00:07:37.879 align:start position:0%
we have a 3X two uh you know between
 

00:07:37.879 --> 00:07:39.189 align:start position:0%
we have a 3X two uh you know between
subject

00:07:39.189 --> 00:07:39.199 align:start position:0%
subject
 

00:07:39.199 --> 00:07:42.309 align:start position:0%
subject
design<00:07:40.199><c> and</c><00:07:40.360><c> to</c><00:07:40.639><c> motivate</c><00:07:41.400><c> our</c><00:07:41.680><c> participants</c>

00:07:42.309 --> 00:07:42.319 align:start position:0%
design and to motivate our participants
 

00:07:42.319 --> 00:07:44.710 align:start position:0%
design and to motivate our participants
to<00:07:42.639><c> exert</c><00:07:43.120><c> their</c><00:07:43.360><c> best</c><00:07:43.720><c> effort</c><00:07:44.159><c> when</c><00:07:44.360><c> they</c>

00:07:44.710 --> 00:07:44.720 align:start position:0%
to exert their best effort when they
 

00:07:44.720 --> 00:07:47.710 align:start position:0%
to exert their best effort when they
generate<00:07:45.120><c> the</c><00:07:45.360><c> ad</c><00:07:45.560><c> copies</c><00:07:46.440><c> we</c><00:07:46.919><c> implemented</c><00:07:47.560><c> a</c>

00:07:47.710 --> 00:07:47.720 align:start position:0%
generate the ad copies we implemented a
 

00:07:47.720 --> 00:07:50.309 align:start position:0%
generate the ad copies we implemented a
outcome<00:07:48.120><c> based</c><00:07:48.400><c> incentives</c><00:07:49.159><c> where</c><00:07:49.960><c> uh</c><00:07:50.120><c> the</c>

00:07:50.309 --> 00:07:50.319 align:start position:0%
outcome based incentives where uh the
 

00:07:50.319 --> 00:07:53.589 align:start position:0%
outcome based incentives where uh the
better<00:07:50.919><c> your</c><00:07:51.159><c> ads</c><00:07:51.639><c> perform</c><00:07:52.639><c> the</c><00:07:52.759><c> more</c><00:07:53.080><c> payment</c>

00:07:53.589 --> 00:07:53.599 align:start position:0%
better your ads perform the more payment
 

00:07:53.599 --> 00:07:55.790 align:start position:0%
better your ads perform the more payment
the<00:07:54.120><c> the</c><00:07:54.319><c> more</c><00:07:54.720><c> bonus</c><00:07:55.120><c> payment</c><00:07:55.440><c> that</c><00:07:55.560><c> you</c><00:07:55.639><c> will</c>

00:07:55.790 --> 00:07:55.800 align:start position:0%
the the more bonus payment that you will
 

00:07:55.800 --> 00:07:58.950 align:start position:0%
the the more bonus payment that you will
get<00:07:55.960><c> at</c><00:07:56.080><c> the</c><00:07:56.159><c> end</c><00:07:56.280><c> of</c><00:07:56.400><c> the</c><00:07:56.800><c> St</c><00:07:57.800><c> so</c><00:07:58.039><c> this</c><00:07:58.280><c> ensures</c>

00:07:58.950 --> 00:07:58.960 align:start position:0%
get at the end of the St so this ensures
 

00:07:58.960 --> 00:08:00.749 align:start position:0%
get at the end of the St so this ensures
that<00:07:59.400><c> our</c><00:07:59.599><c> participants</c><00:08:00.120><c> are</c><00:08:00.400><c> properly</c>

00:08:00.749 --> 00:08:00.759 align:start position:0%
that our participants are properly
 

00:08:00.759 --> 00:08:03.990 align:start position:0%
that our participants are properly
motivated<00:08:01.319><c> to</c><00:08:02.120><c> actually</c><00:08:02.720><c> put</c><00:08:03.240><c> real</c><00:08:03.599><c> efforts</c>

00:08:03.990 --> 00:08:04.000 align:start position:0%
motivated to actually put real efforts
 

00:08:04.000 --> 00:08:05.070 align:start position:0%
motivated to actually put real efforts
into<00:08:04.159><c> the</c>

00:08:05.070 --> 00:08:05.080 align:start position:0%
into the
 

00:08:05.080 --> 00:08:08.909 align:start position:0%
into the
task<00:08:06.080><c> and</c><00:08:06.240><c> to</c><00:08:06.759><c> manipulate</c><00:08:07.759><c> how</c><00:08:08.120><c> the</c><00:08:08.360><c> users</c>

00:08:08.909 --> 00:08:08.919 align:start position:0%
task and to manipulate how the users
 

00:08:08.919 --> 00:08:11.110 align:start position:0%
task and to manipulate how the users
engage<00:08:09.560><c> with</c><00:08:09.759><c> large</c><00:08:10.120><c> vry</c><00:08:10.440><c> models</c><00:08:10.759><c> using</c>

00:08:11.110 --> 00:08:11.120 align:start position:0%
engage with large vry models using
 

00:08:11.120 --> 00:08:13.830 align:start position:0%
engage with large vry models using
different<00:08:11.520><c> modalities</c><00:08:12.520><c> we</c><00:08:12.840><c> implemented</c><00:08:13.560><c> a</c>

00:08:13.830 --> 00:08:13.840 align:start position:0%
different modalities we implemented a
 

00:08:13.840 --> 00:08:16.390 align:start position:0%
different modalities we implemented a
costume<00:08:14.319><c> chat</c><00:08:14.680><c> interface</c><00:08:15.280><c> using</c><00:08:15.520><c> the</c><00:08:15.639><c> GPT</c><00:08:16.039><c> 4</c>

00:08:16.390 --> 00:08:16.400 align:start position:0%
costume chat interface using the GPT 4
 

00:08:16.400 --> 00:08:19.830 align:start position:0%
costume chat interface using the GPT 4
API<00:08:17.400><c> so</c><00:08:17.680><c> this</c><00:08:17.960><c> provides</c><00:08:18.360><c> multiple</c><00:08:18.840><c> benefits</c>

00:08:19.830 --> 00:08:19.840 align:start position:0%
API so this provides multiple benefits
 

00:08:19.840 --> 00:08:24.189 align:start position:0%
API so this provides multiple benefits
one<00:08:20.800><c> being</c><00:08:21.639><c> our</c><00:08:21.919><c> ability</c><00:08:22.319><c> to</c><00:08:23.319><c> directly</c><00:08:23.759><c> embed</c>

00:08:24.189 --> 00:08:24.199 align:start position:0%
one being our ability to directly embed
 

00:08:24.199 --> 00:08:27.869 align:start position:0%
one being our ability to directly embed
this<00:08:24.360><c> TR</c><00:08:24.639><c> interface</c><00:08:25.440><c> on</c><00:08:25.680><c> the</c><00:08:26.240><c> quarrix</c><00:08:27.240><c> uh</c>

00:08:27.869 --> 00:08:27.879 align:start position:0%
this TR interface on the quarrix uh
 

00:08:27.879 --> 00:08:31.390 align:start position:0%
this TR interface on the quarrix uh
platform<00:08:28.879><c> and</c><00:08:29.280><c> we</c><00:08:29.440><c> can</c><00:08:29.680><c> also</c><00:08:30.039><c> retain</c><00:08:30.520><c> the</c><00:08:30.840><c> full</c>

00:08:31.390 --> 00:08:31.400 align:start position:0%
platform and we can also retain the full
 

00:08:31.400 --> 00:08:36.909 align:start position:0%
platform and we can also retain the full
chat<00:08:31.800><c> LW</c><00:08:32.320><c> for</c><00:08:32.640><c> tal</c><00:08:33.159><c> analysis</c><00:08:34.159><c> later</c><00:08:35.640><c> and</c><00:08:36.640><c> the</c>

00:08:36.909 --> 00:08:36.919 align:start position:0%
chat LW for tal analysis later and the
 

00:08:36.919 --> 00:08:40.589 align:start position:0%
chat LW for tal analysis later and the
the<00:08:37.039><c> most</c><00:08:37.640><c> important</c><00:08:38.640><c> um</c><00:08:39.159><c> feature</c><00:08:39.839><c> of</c><00:08:40.039><c> this</c><00:08:40.479><c> uh</c>

00:08:40.589 --> 00:08:40.599 align:start position:0%
the most important um feature of this uh
 

00:08:40.599 --> 00:08:43.149 align:start position:0%
the most important um feature of this uh
interface<00:08:41.159><c> is</c><00:08:41.320><c> the</c><00:08:41.479><c> ability</c><00:08:41.959><c> to</c><00:08:42.640><c> manipulate</c>

00:08:43.149 --> 00:08:43.159 align:start position:0%
interface is the ability to manipulate
 

00:08:43.159 --> 00:08:45.230 align:start position:0%
interface is the ability to manipulate
the<00:08:43.279><c> system</c><00:08:43.640><c> prompts</c><00:08:44.080><c> that</c><00:08:44.200><c> we</c><00:08:44.399><c> suppli</c><00:08:44.760><c> to</c><00:08:44.920><c> the</c>

00:08:45.230 --> 00:08:45.240 align:start position:0%
the system prompts that we suppli to the
 

00:08:45.240 --> 00:08:48.949 align:start position:0%
the system prompts that we suppli to the
gbd4<00:08:45.760><c> API</c><00:08:46.680><c> so</c><00:08:47.120><c> in</c><00:08:47.240><c> the</c><00:08:47.399><c> Ghost</c><00:08:47.720><c> Rider</c><00:08:48.040><c> group</c><00:08:48.720><c> we</c>

00:08:48.949 --> 00:08:48.959 align:start position:0%
gbd4 API so in the Ghost Rider group we
 

00:08:48.959 --> 00:08:52.870 align:start position:0%
gbd4 API so in the Ghost Rider group we
ask<00:08:49.360><c> the</c><00:08:49.600><c> LM</c><00:08:50.240><c> to</c><00:08:51.120><c> only</c><00:08:52.120><c> you</c><00:08:52.240><c> know</c><00:08:52.440><c> generate</c>

00:08:52.870 --> 00:08:52.880 align:start position:0%
ask the LM to only you know generate
 

00:08:52.880 --> 00:08:54.910 align:start position:0%
ask the LM to only you know generate
content<00:08:53.320><c> for</c><00:08:53.519><c> our</c><00:08:53.720><c> participants</c><00:08:54.360><c> but</c><00:08:54.600><c> never</c>

00:08:54.910 --> 00:08:54.920 align:start position:0%
content for our participants but never
 

00:08:54.920 --> 00:08:56.910 align:start position:0%
content for our participants but never
provide<00:08:55.279><c> any</c><00:08:55.480><c> feedback</c><00:08:56.200><c> whereas</c><00:08:56.600><c> for</c><00:08:56.760><c> the</c>

00:08:56.910 --> 00:08:56.920 align:start position:0%
provide any feedback whereas for the
 

00:08:56.920 --> 00:08:59.630 align:start position:0%
provide any feedback whereas for the
sounding<00:08:57.279><c> board</c><00:08:57.720><c> modality</c><00:08:58.519><c> the</c><00:08:58.720><c> Lar</c><00:08:59.320><c> models</c>

00:08:59.630 --> 00:08:59.640 align:start position:0%
sounding board modality the Lar models
 

00:08:59.640 --> 00:09:03.910 align:start position:0%
sounding board modality the Lar models
will<00:08:59.920><c> refuse</c><00:09:00.880><c> to</c><00:09:01.440><c> provide</c><00:09:02.000><c> any</c><00:09:02.480><c> add</c><00:09:02.920><c> copies</c>

00:09:03.910 --> 00:09:03.920 align:start position:0%
will refuse to provide any add copies
 

00:09:03.920 --> 00:09:07.870 align:start position:0%
will refuse to provide any add copies
and<00:09:04.240><c> only</c><00:09:04.600><c> provide</c><00:09:05.279><c> feedback</c><00:09:06.279><c> uh</c><00:09:06.399><c> for</c><00:09:06.640><c> the</c>

00:09:07.870 --> 00:09:07.880 align:start position:0%
and only provide feedback uh for the
 

00:09:07.880 --> 00:09:10.630 align:start position:0%
and only provide feedback uh for the
participants<00:09:08.880><c> and</c><00:09:09.360><c> we</c><00:09:09.640><c> done</c><00:09:09.959><c> a</c><00:09:10.120><c> lot</c><00:09:10.320><c> of</c><00:09:10.519><c> you</c>

00:09:10.630 --> 00:09:10.640 align:start position:0%
participants and we done a lot of you
 

00:09:10.640 --> 00:09:13.190 align:start position:0%
participants and we done a lot of you
know<00:09:10.839><c> promp</c><00:09:11.120><c> engineering</c><00:09:11.640><c> and</c><00:09:11.839><c> testing</c><00:09:12.720><c> so</c><00:09:13.000><c> we</c>

00:09:13.190 --> 00:09:13.200 align:start position:0%
know promp engineering and testing so we
 

00:09:13.200 --> 00:09:15.389 align:start position:0%
know promp engineering and testing so we
actually<00:09:13.480><c> look</c><00:09:13.800><c> into</c><00:09:14.120><c> the</c><00:09:14.320><c> chatlog</c><00:09:14.839><c> to</c><00:09:15.040><c> find</c>

00:09:15.389 --> 00:09:15.399 align:start position:0%
actually look into the chatlog to find
 

00:09:15.399 --> 00:09:17.949 align:start position:0%
actually look into the chatlog to find
any<00:09:15.760><c> potential</c><00:09:16.279><c> violation</c><00:09:16.920><c> where</c><00:09:17.160><c> people</c><00:09:17.760><c> in</c>

00:09:17.949 --> 00:09:17.959 align:start position:0%
any potential violation where people in
 

00:09:17.959 --> 00:09:19.710 align:start position:0%
any potential violation where people in
one<00:09:18.240><c> group</c><00:09:18.480><c> may</c><00:09:18.680><c> be</c><00:09:18.800><c> able</c><00:09:19.000><c> to</c><00:09:19.120><c> find</c><00:09:19.279><c> a</c><00:09:19.480><c> way</c>

00:09:19.710 --> 00:09:19.720 align:start position:0%
one group may be able to find a way
 

00:09:19.720 --> 00:09:21.829 align:start position:0%
one group may be able to find a way
around<00:09:20.320><c> to</c><00:09:20.480><c> use</c><00:09:20.760><c> the</c><00:09:20.920><c> lar</c><00:09:21.240><c> Dage</c><00:09:21.480><c> model</c><00:09:21.720><c> in</c>

00:09:21.829 --> 00:09:21.839 align:start position:0%
around to use the lar Dage model in
 

00:09:21.839 --> 00:09:23.910 align:start position:0%
around to use the lar Dage model in
another<00:09:22.160><c> modality</c><00:09:22.880><c> and</c><00:09:23.000><c> we</c><00:09:23.160><c> did</c><00:09:23.320><c> not</c><00:09:23.480><c> find</c><00:09:23.680><c> any</c>

00:09:23.910 --> 00:09:23.920 align:start position:0%
another modality and we did not find any
 

00:09:23.920 --> 00:09:25.110 align:start position:0%
another modality and we did not find any
evidence<00:09:24.279><c> of</c>

00:09:25.110 --> 00:09:25.120 align:start position:0%
evidence of
 

00:09:25.120 --> 00:09:28.230 align:start position:0%
evidence of
that<00:09:26.120><c> so</c><00:09:26.760><c> here's</c><00:09:27.079><c> the</c><00:09:27.440><c> overview</c><00:09:27.959><c> of</c><00:09:28.120><c> the</c>

00:09:28.230 --> 00:09:28.240 align:start position:0%
that so here's the overview of the
 

00:09:28.240 --> 00:09:29.710 align:start position:0%
that so here's the overview of the
experimental<00:09:28.800><c> procedure</c>

00:09:29.710 --> 00:09:29.720 align:start position:0%
experimental procedure
 

00:09:29.720 --> 00:09:32.750 align:start position:0%
experimental procedure
Fe<00:09:30.720><c> um</c><00:09:30.880><c> to</c><00:09:31.120><c> make</c><00:09:31.240><c> sure</c><00:09:31.560><c> that</c><00:09:31.720><c> our</c><00:09:32.240><c> participants</c>

00:09:32.750 --> 00:09:32.760 align:start position:0%
Fe um to make sure that our participants
 

00:09:32.760 --> 00:09:35.030 align:start position:0%
Fe um to make sure that our participants
are<00:09:32.959><c> familiar</c><00:09:33.600><c> with</c><00:09:33.920><c> the</c><00:09:34.480><c> uh</c><00:09:34.600><c> the</c><00:09:34.720><c> chat</c>

00:09:35.030 --> 00:09:35.040 align:start position:0%
are familiar with the uh the chat
 

00:09:35.040 --> 00:09:37.389 align:start position:0%
are familiar with the uh the chat
interface<00:09:35.680><c> as</c><00:09:35.800><c> well</c><00:09:36.079><c> as</c><00:09:36.399><c> the</c><00:09:36.760><c> modality</c><00:09:37.320><c> that</c>

00:09:37.389 --> 00:09:37.399 align:start position:0%
interface as well as the modality that
 

00:09:37.399 --> 00:09:39.350 align:start position:0%
interface as well as the modality that
are<00:09:37.560><c> assigned</c><00:09:38.000><c> with</c><00:09:38.680><c> they</c><00:09:38.880><c> first</c><00:09:39.160><c> are</c>

00:09:39.350 --> 00:09:39.360 align:start position:0%
are assigned with they first are
 

00:09:39.360 --> 00:09:41.550 align:start position:0%
are assigned with they first are
required<00:09:39.720><c> to</c><00:09:39.920><c> complete</c><00:09:40.320><c> a</c><00:09:40.519><c> familiarization</c>

00:09:41.550 --> 00:09:41.560 align:start position:0%
required to complete a familiarization
 

00:09:41.560 --> 00:09:45.509 align:start position:0%
required to complete a familiarization
task<00:09:42.560><c> so</c><00:09:42.920><c> in</c><00:09:43.040><c> the</c><00:09:43.240><c> familiarization</c><00:09:44.000><c> tasks</c><00:09:44.680><c> the</c>

00:09:45.509 --> 00:09:45.519 align:start position:0%
task so in the familiarization tasks the
 

00:09:45.519 --> 00:09:47.790 align:start position:0%
task so in the familiarization tasks the
uh<00:09:45.720><c> participants</c><00:09:46.320><c> were</c><00:09:46.560><c> not</c><00:09:46.839><c> required</c><00:09:47.560><c> to</c>

00:09:47.790 --> 00:09:47.800 align:start position:0%
uh participants were not required to
 

00:09:47.800 --> 00:09:49.509 align:start position:0%
uh participants were not required to
write<00:09:48.000><c> an</c><00:09:48.160><c> add</c><00:09:48.480><c> copy</c><00:09:48.760><c> but</c><00:09:48.880><c> nevertheless</c><00:09:49.440><c> they</c>

00:09:49.509 --> 00:09:49.519 align:start position:0%
write an add copy but nevertheless they
 

00:09:49.519 --> 00:09:52.150 align:start position:0%
write an add copy but nevertheless they
were<00:09:49.760><c> given</c><00:09:50.480><c> a</c><00:09:50.920><c> example</c><00:09:51.399><c> product</c><00:09:51.800><c> which</c><00:09:51.920><c> is</c><00:09:52.000><c> a</c>

00:09:52.150 --> 00:09:52.160 align:start position:0%
were given a example product which is a
 

00:09:52.160 --> 00:09:55.630 align:start position:0%
were given a example product which is a
tap<00:09:52.480><c> holder</c><00:09:52.839><c> over</c><00:09:53.000><c> here</c><00:09:53.519><c> to</c><00:09:54.240><c> uh</c><00:09:54.360><c> to</c><00:09:54.959><c> simulate</c>

00:09:55.630 --> 00:09:55.640 align:start position:0%
tap holder over here to uh to simulate
 

00:09:55.640 --> 00:09:56.870 align:start position:0%
tap holder over here to uh to simulate
the<00:09:56.000><c> test</c>

00:09:56.870 --> 00:09:56.880 align:start position:0%
the test
 

00:09:56.880 --> 00:10:01.269 align:start position:0%
the test
structure<00:09:57.880><c> so</c><00:09:58.240><c> here</c><00:09:58.800><c> all</c><00:09:59.120><c> all</c><00:09:59.279><c> of</c><00:09:59.720><c> the</c><00:10:00.720><c> uh</c>

00:10:01.269 --> 00:10:01.279 align:start position:0%
structure so here all all of the uh
 

00:10:01.279 --> 00:10:02.910 align:start position:0%
structure so here all all of the uh
experimental<00:10:01.839><c> groups</c><00:10:02.320><c> including</c><00:10:02.760><c> the</c>

00:10:02.910 --> 00:10:02.920 align:start position:0%
experimental groups including the
 

00:10:02.920 --> 00:10:05.389 align:start position:0%
experimental groups including the
control<00:10:03.399><c> groups</c><00:10:03.959><c> were</c><00:10:04.279><c> required</c><00:10:04.680><c> to</c><00:10:04.839><c> engage</c>

00:10:05.389 --> 00:10:05.399 align:start position:0%
control groups were required to engage
 

00:10:05.399 --> 00:10:09.230 align:start position:0%
control groups were required to engage
with<00:10:05.640><c> the</c><00:10:06.279><c> uh</c><00:10:06.560><c> TR4</c><00:10:07.360><c> based</c><00:10:08.000><c> uh</c><00:10:08.360><c> TR</c><00:10:08.680><c> interface</c><00:10:09.120><c> by</c>

00:10:09.230 --> 00:10:09.240 align:start position:0%
with the uh TR4 based uh TR interface by
 

00:10:09.240 --> 00:10:12.509 align:start position:0%
with the uh TR4 based uh TR interface by
sending<00:10:09.640><c> at</c><00:10:09.800><c> least</c><00:10:10.079><c> one</c><00:10:10.320><c> message</c><00:10:11.279><c> so</c><00:10:11.680><c> we</c><00:10:11.880><c> do</c><00:10:12.160><c> so</c>

00:10:12.509 --> 00:10:12.519 align:start position:0%
sending at least one message so we do so
 

00:10:12.519 --> 00:10:15.110 align:start position:0%
sending at least one message so we do so
to<00:10:12.800><c> ensure</c><00:10:13.279><c> that</c><00:10:13.519><c> the</c><00:10:13.760><c> fatigue</c><00:10:14.279><c> level</c><00:10:14.720><c> across</c>

00:10:15.110 --> 00:10:15.120 align:start position:0%
to ensure that the fatigue level across
 

00:10:15.120 --> 00:10:16.670 align:start position:0%
to ensure that the fatigue level across
all<00:10:15.320><c> the</c><00:10:15.399><c> treatment</c><00:10:15.760><c> groups</c><00:10:16.040><c> were</c><00:10:16.279><c> exactly</c>

00:10:16.670 --> 00:10:16.680 align:start position:0%
all the treatment groups were exactly
 

00:10:16.680 --> 00:10:18.710 align:start position:0%
all the treatment groups were exactly
the<00:10:16.800><c> same</c><00:10:17.399><c> before</c><00:10:17.720><c> they</c><00:10:17.839><c> go</c><00:10:18.040><c> into</c><00:10:18.240><c> the</c><00:10:18.360><c> main</c>

00:10:18.710 --> 00:10:18.720 align:start position:0%
the same before they go into the main
 

00:10:18.720 --> 00:10:21.710 align:start position:0%
the same before they go into the main
copyrighted<00:10:19.680><c> task</c><00:10:20.680><c> and</c><00:10:20.959><c> for</c><00:10:21.120><c> the</c><00:10:21.240><c> control</c>

00:10:21.710 --> 00:10:21.720 align:start position:0%
copyrighted task and for the control
 

00:10:21.720 --> 00:10:24.150 align:start position:0%
copyrighted task and for the control
group<00:10:22.079><c> they</c><00:10:22.240><c> get</c><00:10:22.480><c> randomly</c><00:10:22.959><c> assigned</c><00:10:23.800><c> uh</c>

00:10:24.150 --> 00:10:24.160 align:start position:0%
group they get randomly assigned uh
 

00:10:24.160 --> 00:10:27.509 align:start position:0%
group they get randomly assigned uh
modalities<00:10:24.839><c> for</c><00:10:25.079><c> the</c><00:10:26.040><c> interface</c><00:10:27.040><c> and</c><00:10:27.200><c> to</c>

00:10:27.509 --> 00:10:27.519 align:start position:0%
modalities for the interface and to
 

00:10:27.519 --> 00:10:29.910 align:start position:0%
modalities for the interface and to
prevent<00:10:28.000><c> our</c><00:10:28.279><c> participants</c><00:10:28.880><c> from</c><00:10:29.160><c> from</c><00:10:29.360><c> using</c>

00:10:29.910 --> 00:10:29.920 align:start position:0%
prevent our participants from from using
 

00:10:29.920 --> 00:10:32.670 align:start position:0%
prevent our participants from from using
large<00:10:30.240><c> Lang</c><00:10:30.560><c> models</c><00:10:30.839><c> or</c><00:10:31.000><c> other</c><00:10:31.360><c> AI</c><00:10:31.880><c> outside</c><00:10:32.440><c> of</c>

00:10:32.670 --> 00:10:32.680 align:start position:0%
large Lang models or other AI outside of
 

00:10:32.680 --> 00:10:35.069 align:start position:0%
large Lang models or other AI outside of
our<00:10:32.959><c> our</c><00:10:33.240><c> experiment</c><00:10:34.200><c> the</c><00:10:34.399><c> copy</c><00:10:34.640><c> and</c><00:10:34.839><c> paste</c>

00:10:35.069 --> 00:10:35.079 align:start position:0%
our our experiment the copy and paste
 

00:10:35.079 --> 00:10:37.829 align:start position:0%
our our experiment the copy and paste
function<00:10:35.440><c> for</c><00:10:35.639><c> the</c><00:10:35.760><c> clickard</c><00:10:36.399><c> is</c><00:10:37.240><c> restricted</c>

00:10:37.829 --> 00:10:37.839 align:start position:0%
function for the clickard is restricted
 

00:10:37.839 --> 00:10:39.949 align:start position:0%
function for the clickard is restricted
strictly<00:10:38.399><c> within</c><00:10:38.680><c> the</c><00:10:38.839><c> web</c><00:10:39.040><c> page</c><00:10:39.480><c> so</c><00:10:39.639><c> that</c><00:10:39.800><c> the</c>

00:10:39.949 --> 00:10:39.959 align:start position:0%
strictly within the web page so that the
 

00:10:39.959 --> 00:10:41.430 align:start position:0%
strictly within the web page so that the
participants<00:10:40.480><c> cannot</c><00:10:40.760><c> transfer</c><00:10:41.240><c> any</c>

00:10:41.430 --> 00:10:41.440 align:start position:0%
participants cannot transfer any
 

00:10:41.440 --> 00:10:44.590 align:start position:0%
participants cannot transfer any
information<00:10:42.399><c> in</c><00:10:42.680><c> or</c><00:10:43.040><c> out</c><00:10:43.680><c> of</c><00:10:43.880><c> the</c><00:10:44.040><c> web</c><00:10:44.240><c> page</c>

00:10:44.590 --> 00:10:44.600 align:start position:0%
information in or out of the web page
 

00:10:44.600 --> 00:10:46.910 align:start position:0%
information in or out of the web page
make<00:10:44.800><c> it</c><00:10:44.920><c> a</c><00:10:45.079><c> pointless</c><00:10:45.600><c> to</c><00:10:45.839><c> actually</c><00:10:46.519><c> go</c><00:10:46.720><c> out</c>

00:10:46.910 --> 00:10:46.920 align:start position:0%
make it a pointless to actually go out
 

00:10:46.920 --> 00:10:49.670 align:start position:0%
make it a pointless to actually go out
and<00:10:47.040><c> use</c><00:10:47.279><c> chbt</c><00:10:48.200><c> to</c><00:10:48.399><c> complete</c><00:10:48.760><c> the</c>

00:10:49.670 --> 00:10:49.680 align:start position:0%
and use chbt to complete the
 

00:10:49.680 --> 00:10:53.069 align:start position:0%
and use chbt to complete the
task<00:10:50.680><c> so</c><00:10:51.000><c> after</c><00:10:51.399><c> that</c><00:10:51.839><c> they</c><00:10:52.000><c> were</c><00:10:52.440><c> they</c><00:10:52.600><c> go</c>

00:10:53.069 --> 00:10:53.079 align:start position:0%
task so after that they were they go
 

00:10:53.079 --> 00:10:55.069 align:start position:0%
task so after that they were they go
straight<00:10:53.360><c> into</c><00:10:53.639><c> the</c><00:10:53.800><c> main</c><00:10:54.079><c> copyrighting</c><00:10:54.600><c> task</c>

00:10:55.069 --> 00:10:55.079 align:start position:0%
straight into the main copyrighting task
 

00:10:55.079 --> 00:10:56.829 align:start position:0%
straight into the main copyrighting task
so<00:10:55.279><c> this</c><00:10:55.399><c> is</c><00:10:55.519><c> the</c><00:10:55.760><c> task</c><00:10:56.160><c> where</c><00:10:56.320><c> they</c><00:10:56.560><c> actually</c>

00:10:56.829 --> 00:10:56.839 align:start position:0%
so this is the task where they actually
 

00:10:56.839 --> 00:10:59.990 align:start position:0%
so this is the task where they actually
need<00:10:57.000><c> to</c><00:10:57.160><c> write</c><00:10:57.360><c> an</c><00:10:57.560><c> add</c><00:10:58.040><c> copy</c><00:10:59.040><c> so</c><00:10:59.279><c> again</c><00:10:59.839><c> the</c>

00:10:59.990 --> 00:11:00.000 align:start position:0%
need to write an add copy so again the
 

00:11:00.000 --> 00:11:02.269 align:start position:0%
need to write an add copy so again the
participant<00:11:00.560><c> here</c><00:11:00.839><c> are</c><00:11:01.240><c> reminded</c><00:11:01.800><c> about</c><00:11:02.079><c> the</c>

00:11:02.269 --> 00:11:02.279 align:start position:0%
participant here are reminded about the
 

00:11:02.279 --> 00:11:05.790 align:start position:0%
participant here are reminded about the
outcome<00:11:02.680><c> based</c><00:11:03.440><c> incentives</c><00:11:04.440><c> and</c><00:11:05.399><c> they</c><00:11:05.560><c> all</c>

00:11:05.790 --> 00:11:05.800 align:start position:0%
outcome based incentives and they all
 

00:11:05.800 --> 00:11:09.590 align:start position:0%
outcome based incentives and they all
receive<00:11:06.240><c> the</c><00:11:06.399><c> same</c><00:11:06.880><c> uh</c><00:11:07.200><c> LM</c><00:11:07.839><c> interface</c><00:11:08.800><c> and</c><00:11:09.079><c> get</c>

00:11:09.590 --> 00:11:09.600 align:start position:0%
receive the same uh LM interface and get
 

00:11:09.600 --> 00:11:11.949 align:start position:0%
receive the same uh LM interface and get
the<00:11:10.079><c> uh</c><00:11:10.240><c> the</c><00:11:10.440><c> assign</c><00:11:10.839><c> modality</c><00:11:11.639><c> of</c><00:11:11.760><c> course</c>

00:11:11.949 --> 00:11:11.959 align:start position:0%
the uh the assign modality of course
 

00:11:11.959 --> 00:11:15.710 align:start position:0%
the uh the assign modality of course
except<00:11:12.240><c> for</c><00:11:12.399><c> the</c><00:11:12.519><c> control</c><00:11:13.360><c> group</c><00:11:14.360><c> so</c><00:11:14.839><c> here</c><00:11:15.480><c> all</c>

00:11:15.710 --> 00:11:15.720 align:start position:0%
except for the control group so here all
 

00:11:15.720 --> 00:11:17.350 align:start position:0%
except for the control group so here all
the<00:11:15.839><c> treatment</c><00:11:16.200><c> groups</c><00:11:16.519><c> can</c><00:11:16.720><c> engage</c><00:11:17.200><c> with</c>

00:11:17.350 --> 00:11:17.360 align:start position:0%
the treatment groups can engage with
 

00:11:17.360 --> 00:11:19.790 align:start position:0%
the treatment groups can engage with
large<00:11:17.680><c> langage</c><00:11:18.000><c> models</c><00:11:18.760><c> as</c><00:11:19.000><c> much</c><00:11:19.440><c> or</c><00:11:19.639><c> as</c>

00:11:19.790 --> 00:11:19.800 align:start position:0%
large langage models as much or as
 

00:11:19.800 --> 00:11:21.710 align:start position:0%
large langage models as much or as
little<00:11:20.120><c> as</c><00:11:20.279><c> they</c><00:11:20.440><c> want</c><00:11:20.959><c> which</c><00:11:21.160><c> simulates</c><00:11:21.600><c> the</c>

00:11:21.710 --> 00:11:21.720 align:start position:0%
little as they want which simulates the
 

00:11:21.720 --> 00:11:23.750 align:start position:0%
little as they want which simulates the
real<00:11:21.920><c> world</c><00:11:22.079><c> scenario</c><00:11:22.680><c> where</c><00:11:23.200><c> you</c><00:11:23.320><c> know</c><00:11:23.600><c> you</c>

00:11:23.750 --> 00:11:23.760 align:start position:0%
real world scenario where you know you
 

00:11:23.760 --> 00:11:28.470 align:start position:0%
real world scenario where you know you
can<00:11:24.399><c> engage</c><00:11:24.959><c> with</c><00:11:25.399><c> uh</c><00:11:25.519><c> CHT</c><00:11:26.519><c> at</c><00:11:26.680><c> your</c><00:11:26.839><c> own</c><00:11:27.480><c> will</c>

00:11:28.470 --> 00:11:28.480 align:start position:0%
can engage with uh CHT at your own will
 

00:11:28.480 --> 00:11:30.230 align:start position:0%
can engage with uh CHT at your own will
and<00:11:28.639><c> before</c><00:11:29.079><c> they</c><00:11:29.200><c> finally</c><00:11:29.560><c> submitted</c><00:11:30.040><c> their</c>

00:11:30.230 --> 00:11:30.240 align:start position:0%
and before they finally submitted their
 

00:11:30.240 --> 00:11:33.389 align:start position:0%
and before they finally submitted their
act<00:11:30.519><c> copy</c><00:11:30.959><c> they</c><00:11:31.160><c> got</c><00:11:31.320><c> a</c><00:11:31.480><c> chance</c><00:11:31.800><c> to</c><00:11:32.079><c> revise</c><00:11:32.920><c> or</c>

00:11:33.389 --> 00:11:33.399 align:start position:0%
act copy they got a chance to revise or
 

00:11:33.399 --> 00:11:36.430 align:start position:0%
act copy they got a chance to revise or
edit<00:11:33.760><c> their</c><00:11:33.959><c> ad</c><00:11:34.200><c> copies</c><00:11:35.120><c> uh</c><00:11:35.240><c> before</c><00:11:35.480><c> the</c><00:11:35.600><c> final</c>

00:11:36.430 --> 00:11:36.440 align:start position:0%
edit their ad copies uh before the final
 

00:11:36.440 --> 00:11:39.870 align:start position:0%
edit their ad copies uh before the final
submission<00:11:37.440><c> so</c><00:11:37.920><c> after</c><00:11:38.480><c> the</c><00:11:39.240><c> uh</c><00:11:39.399><c> participants</c>

00:11:39.870 --> 00:11:39.880 align:start position:0%
submission so after the uh participants
 

00:11:39.880 --> 00:11:42.030 align:start position:0%
submission so after the uh participants
submitted<00:11:40.320><c> the</c><00:11:40.480><c> ad</c><00:11:40.760><c> copy</c><00:11:41.120><c> we</c><00:11:41.279><c> take</c><00:11:41.519><c> this</c><00:11:41.720><c> ad</c>

00:11:42.030 --> 00:11:42.040 align:start position:0%
submitted the ad copy we take this ad
 

00:11:42.040 --> 00:11:45.670 align:start position:0%
submitted the ad copy we take this ad
copy<00:11:42.880><c> and</c><00:11:43.160><c> launch</c><00:11:43.920><c> a</c><00:11:44.279><c> campaigns</c><00:11:44.920><c> on</c><00:11:45.240><c> Facebook</c>

00:11:45.670 --> 00:11:45.680 align:start position:0%
copy and launch a campaigns on Facebook
 

00:11:45.680 --> 00:11:49.350 align:start position:0%
copy and launch a campaigns on Facebook
and<00:11:46.079><c> Instagram</c><00:11:47.079><c> so</c><00:11:47.440><c> to</c><00:11:48.120><c> to</c><00:11:48.440><c> prevent</c><00:11:49.120><c> any</c>

00:11:49.350 --> 00:11:49.360 align:start position:0%
and Instagram so to to prevent any
 

00:11:49.360 --> 00:11:52.470 align:start position:0%
and Instagram so to to prevent any
retargeting<00:11:50.079><c> effects</c><00:11:50.959><c> we</c><00:11:51.320><c> assigned</c><00:11:51.800><c> a</c><00:11:51.959><c> rening</c>

00:11:52.470 --> 00:11:52.480 align:start position:0%
retargeting effects we assigned a rening
 

00:11:52.480 --> 00:11:54.949 align:start position:0%
retargeting effects we assigned a rening
county<00:11:52.920><c> in</c><00:11:53.040><c> the</c><00:11:53.160><c> United</c><00:11:53.560><c> States</c><00:11:54.440><c> uh</c><00:11:54.519><c> to</c><00:11:54.760><c> each</c>

00:11:54.949 --> 00:11:54.959 align:start position:0%
county in the United States uh to each
 

00:11:54.959 --> 00:11:58.629 align:start position:0%
county in the United States uh to each
of<00:11:55.079><c> the</c><00:11:55.600><c> Ealy</c><00:11:56.600><c> and</c><00:11:57.600><c> uh</c><00:11:57.800><c> these</c><00:11:58.040><c> counties</c><00:11:58.519><c> they</c>

00:11:58.629 --> 00:11:58.639 align:start position:0%
of the Ealy and uh these counties they
 

00:11:58.639 --> 00:12:00.870 align:start position:0%
of the Ealy and uh these counties they
all<00:11:58.959><c> share</c><00:11:59.240><c> the</c><00:11:59.480><c> same</c><00:12:00.240><c> the</c><00:12:00.399><c> similar</c>

00:12:00.870 --> 00:12:00.880 align:start position:0%
all share the same the similar
 

00:12:00.880 --> 00:12:03.590 align:start position:0%
all share the same the similar
demographic<00:12:01.639><c> such</c><00:12:01.800><c> as</c><00:12:02.320><c> population</c><00:12:03.320><c> and</c>

00:12:03.590 --> 00:12:03.600 align:start position:0%
demographic such as population and
 

00:12:03.600 --> 00:12:06.990 align:start position:0%
demographic such as population and
median<00:12:04.279><c> income</c><00:12:05.279><c> uh</c><00:12:05.399><c> all</c><00:12:05.639><c> these</c><00:12:05.880><c> ads</c><00:12:06.320><c> have</c><00:12:06.600><c> the</c>

00:12:06.990 --> 00:12:07.000 align:start position:0%
median income uh all these ads have the
 

00:12:07.000 --> 00:12:09.190 align:start position:0%
median income uh all these ads have the
exactly<00:12:07.440><c> the</c><00:12:07.560><c> same</c><00:12:07.839><c> at</c><00:12:08.079><c> budget</c><00:12:08.639><c> exact</c><00:12:09.040><c> the</c>

00:12:09.190 --> 00:12:09.200 align:start position:0%
exactly the same at budget exact the
 

00:12:09.200 --> 00:12:11.870 align:start position:0%
exactly the same at budget exact the
same<00:12:09.880><c> uh</c><00:12:10.079><c> at</c><00:12:10.320><c> configuration</c><00:12:11.240><c> exactly</c><00:12:11.720><c> the</c>

00:12:11.870 --> 00:12:11.880 align:start position:0%
same uh at configuration exactly the
 

00:12:11.880 --> 00:12:15.550 align:start position:0%
same uh at configuration exactly the
same<00:12:13.000><c> duration</c><00:12:14.000><c> uh</c><00:12:14.120><c> to</c><00:12:14.399><c> avoid</c><00:12:14.959><c> any</c><00:12:15.240><c> time</c>

00:12:15.550 --> 00:12:15.560 align:start position:0%
same duration uh to avoid any time
 

00:12:15.560 --> 00:12:18.110 align:start position:0%
same duration uh to avoid any time
specific<00:12:15.959><c> facts</c><00:12:16.639><c> these</c><00:12:16.920><c> ads</c><00:12:17.199><c> were</c><00:12:17.399><c> R</c><00:12:17.720><c> both</c><00:12:17.920><c> on</c>

00:12:18.110 --> 00:12:18.120 align:start position:0%
specific facts these ads were R both on
 

00:12:18.120 --> 00:12:20.829 align:start position:0%
specific facts these ads were R both on
weekdays<00:12:18.720><c> and</c><00:12:19.000><c> weekends</c><00:12:20.000><c> so</c><00:12:20.240><c> everything</c><00:12:20.560><c> else</c>

00:12:20.829 --> 00:12:20.839 align:start position:0%
weekdays and weekends so everything else
 

00:12:20.839 --> 00:12:23.670 align:start position:0%
weekdays and weekends so everything else
being<00:12:21.120><c> equal</c><00:12:21.480><c> we</c><00:12:21.639><c> believe</c><00:12:22.079><c> that</c><00:12:22.320><c> the</c><00:12:22.800><c> uh</c><00:12:23.040><c> at</c>

00:12:23.670 --> 00:12:23.680 align:start position:0%
being equal we believe that the uh at
 

00:12:23.680 --> 00:12:27.030 align:start position:0%
being equal we believe that the uh at
should<00:12:24.560><c> reflect</c><00:12:25.079><c> the</c><00:12:25.199><c> true</c><00:12:25.519><c> quality</c><00:12:26.000><c> of</c><00:12:26.120><c> the</c>

00:12:27.030 --> 00:12:27.040 align:start position:0%
should reflect the true quality of the
 

00:12:27.040 --> 00:12:29.829 align:start position:0%
should reflect the true quality of the
ads<00:12:28.040><c> so</c><00:12:28.279><c> before</c><00:12:28.480><c> we</c><00:12:28.600><c> jum</c><00:12:29.000><c> the</c><00:12:29.079><c> main</c><00:12:29.360><c> results</c>

00:12:29.829 --> 00:12:29.839 align:start position:0%
ads so before we jum the main results
 

00:12:29.839 --> 00:12:32.110 align:start position:0%
ads so before we jum the main results
let's<00:12:30.079><c> just</c><00:12:30.320><c> quickly</c><00:12:30.760><c> do</c><00:12:30.959><c> a</c><00:12:31.160><c> mation</c><00:12:31.639><c> check</c><00:12:31.959><c> so</c>

00:12:32.110 --> 00:12:32.120 align:start position:0%
let's just quickly do a mation check so
 

00:12:32.120 --> 00:12:34.389 align:start position:0%
let's just quickly do a mation check so
we<00:12:32.240><c> want</c><00:12:32.320><c> to</c><00:12:32.560><c> know</c><00:12:33.320><c> whether</c><00:12:33.760><c> experts</c><00:12:34.199><c> are</c>

00:12:34.389 --> 00:12:34.399 align:start position:0%
we want to know whether experts are
 

00:12:34.399 --> 00:12:36.389 align:start position:0%
we want to know whether experts are
truly<00:12:34.760><c> doing</c><00:12:35.120><c> better</c><00:12:35.399><c> than</c><00:12:35.519><c> the</c><00:12:35.639><c> non</c><00:12:35.920><c> experts</c>

00:12:36.389 --> 00:12:36.399 align:start position:0%
truly doing better than the non experts
 

00:12:36.399 --> 00:12:39.030 align:start position:0%
truly doing better than the non experts
and<00:12:36.600><c> whether</c><00:12:37.000><c> the</c><00:12:37.959><c> uh</c><00:12:38.120><c> the</c><00:12:38.279><c> applix</c><00:12:38.800><c> are</c>

00:12:39.030 --> 00:12:39.040 align:start position:0%
and whether the uh the applix are
 

00:12:39.040 --> 00:12:43.470 align:start position:0%
and whether the uh the applix are
reflective<00:12:39.880><c> of</c><00:12:40.320><c> the</c><00:12:41.320><c> uh</c><00:12:41.519><c> the</c><00:12:41.639><c> app</c><00:12:42.240><c> quality</c><00:12:43.240><c> so</c>

00:12:43.470 --> 00:12:43.480 align:start position:0%
reflective of the uh the app quality so
 

00:12:43.480 --> 00:12:45.230 align:start position:0%
reflective of the uh the app quality so
here<00:12:43.680><c> we</c><00:12:43.880><c> compare</c><00:12:44.240><c> the</c><00:12:44.480><c> expert</c><00:12:44.920><c> and</c><00:12:45.040><c> non</c>

00:12:45.230 --> 00:12:45.240 align:start position:0%
here we compare the expert and non
 

00:12:45.240 --> 00:12:47.230 align:start position:0%
here we compare the expert and non
experts<00:12:45.600><c> in</c><00:12:45.680><c> the</c><00:12:45.800><c> control</c><00:12:46.199><c> group</c><00:12:46.760><c> and</c><00:12:46.880><c> we</c><00:12:47.000><c> find</c>

00:12:47.230 --> 00:12:47.240 align:start position:0%
experts in the control group and we find
 

00:12:47.240 --> 00:12:50.509 align:start position:0%
experts in the control group and we find
that<00:12:47.800><c> experts</c><00:12:48.800><c> does</c><00:12:49.360><c> perform</c><00:12:49.880><c> better</c><00:12:50.320><c> than</c>

00:12:50.509 --> 00:12:50.519 align:start position:0%
that experts does perform better than
 

00:12:50.519 --> 00:12:52.550 align:start position:0%
that experts does perform better than
nonexperts<00:12:51.199><c> meaning</c><00:12:51.519><c> that</c><00:12:51.639><c> our</c><00:12:51.920><c> manipulation</c>

00:12:52.550 --> 00:12:52.560 align:start position:0%
nonexperts meaning that our manipulation
 

00:12:52.560 --> 00:12:55.389 align:start position:0%
nonexperts meaning that our manipulation
is<00:12:52.839><c> successful</c><00:12:53.839><c> and</c><00:12:54.240><c> uh</c><00:12:54.360><c> the</c><00:12:54.519><c> ACC</c><00:12:55.160><c> is</c>

00:12:55.389 --> 00:12:55.399 align:start position:0%
is successful and uh the ACC is
 

00:12:55.399 --> 00:12:58.189 align:start position:0%
is successful and uh the ACC is
reflective<00:12:55.920><c> of</c><00:12:56.040><c> the</c><00:12:56.160><c> true</c><00:12:56.839><c> qualtity</c><00:12:57.839><c> so</c><00:12:58.000><c> let's</c>

00:12:58.189 --> 00:12:58.199 align:start position:0%
reflective of the true qualtity so let's
 

00:12:58.199 --> 00:12:59.829 align:start position:0%
reflective of the true qualtity so let's
get<00:12:58.320><c> into</c><00:12:58.480><c> the</c><00:12:58.600><c> main</c>

00:12:59.829 --> 00:12:59.839 align:start position:0%
get into the main
 

00:12:59.839 --> 00:13:03.310 align:start position:0%
get into the main
results<00:13:00.839><c> so</c><00:13:01.279><c> first</c><00:13:01.720><c> for</c><00:13:02.120><c> among</c><00:13:02.440><c> non</c><00:13:02.760><c> experts</c>

00:13:03.310 --> 00:13:03.320 align:start position:0%
results so first for among non experts
 

00:13:03.320 --> 00:13:05.230 align:start position:0%
results so first for among non experts
we<00:13:03.480><c> found</c><00:13:03.920><c> that</c><00:13:04.240><c> the</c><00:13:04.399><c> sounding</c><00:13:04.839><c> board</c>

00:13:05.230 --> 00:13:05.240 align:start position:0%
we found that the sounding board
 

00:13:05.240 --> 00:13:06.829 align:start position:0%
we found that the sounding board
modality<00:13:05.800><c> actually</c><00:13:06.120><c> significantly</c>

00:13:06.829 --> 00:13:06.839 align:start position:0%
modality actually significantly
 

00:13:06.839 --> 00:13:09.590 align:start position:0%
modality actually significantly
increased<00:13:07.440><c> the</c><00:13:07.680><c> add</c><00:13:08.079><c> performance</c><00:13:09.079><c> but</c><00:13:09.320><c> we</c><00:13:09.440><c> did</c>

00:13:09.590 --> 00:13:09.600 align:start position:0%
increased the add performance but we did
 

00:13:09.600 --> 00:13:11.590 align:start position:0%
increased the add performance but we did
not<00:13:09.720><c> find</c><00:13:09.959><c> any</c><00:13:10.199><c> significant</c><00:13:10.720><c> effects</c><00:13:11.279><c> for</c><00:13:11.440><c> the</c>

00:13:11.590 --> 00:13:11.600 align:start position:0%
not find any significant effects for the
 

00:13:11.600 --> 00:13:14.470 align:start position:0%
not find any significant effects for the
ghost<00:13:11.839><c> rer</c><00:13:12.519><c> group</c><00:13:13.519><c> however</c><00:13:14.000><c> among</c><00:13:14.279><c> the</c>

00:13:14.470 --> 00:13:14.480 align:start position:0%
ghost rer group however among the
 

00:13:14.480 --> 00:13:16.750 align:start position:0%
ghost rer group however among the
experts<00:13:15.320><c> we</c><00:13:15.440><c> did</c><00:13:15.600><c> not</c><00:13:15.800><c> find</c><00:13:16.040><c> any</c><00:13:16.240><c> significant</c>

00:13:16.750 --> 00:13:16.760 align:start position:0%
experts we did not find any significant
 

00:13:16.760 --> 00:13:18.590 align:start position:0%
experts we did not find any significant
effect<00:13:17.079><c> in</c><00:13:17.160><c> terms</c><00:13:17.440><c> of</c>

00:13:18.590 --> 00:13:18.600 align:start position:0%
effect in terms of
 

00:13:18.600 --> 00:13:22.430 align:start position:0%
effect in terms of
the<00:13:19.600><c> in</c><00:13:19.680><c> terms</c><00:13:19.920><c> of</c><00:13:20.120><c> the</c><00:13:20.880><c> selling</c><00:13:21.880><c> but</c><00:13:22.040><c> we</c><00:13:22.160><c> do</c>

00:13:22.430 --> 00:13:22.440 align:start position:0%
the in terms of the selling but we do
 

00:13:22.440 --> 00:13:24.790 align:start position:0%
the in terms of the selling but we do
find<00:13:22.760><c> that</c><00:13:23.560><c> their</c><00:13:23.800><c> performance</c><00:13:24.279><c> dropped</c>

00:13:24.790 --> 00:13:24.800 align:start position:0%
find that their performance dropped
 

00:13:24.800 --> 00:13:27.590 align:start position:0%
find that their performance dropped
significantly<00:13:25.720><c> after</c><00:13:26.000><c> using</c><00:13:26.399><c> the</c><00:13:27.199><c> uh</c><00:13:27.360><c> Ghost</c>

00:13:27.590 --> 00:13:27.600 align:start position:0%
significantly after using the uh Ghost
 

00:13:27.600 --> 00:13:30.629 align:start position:0%
significantly after using the uh Ghost
Rider<00:13:27.839><c> modality</c><00:13:29.240><c> so</c><00:13:29.399><c> the</c><00:13:29.519><c> key</c><00:13:29.760><c> Insight</c><00:13:30.279><c> here</c>

00:13:30.629 --> 00:13:30.639 align:start position:0%
Rider modality so the key Insight here
 

00:13:30.639 --> 00:13:33.150 align:start position:0%
Rider modality so the key Insight here
is<00:13:30.920><c> that</c><00:13:31.199><c> the</c><00:13:31.440><c> benefits</c><00:13:31.920><c> of</c><00:13:32.120><c> large</c><00:13:32.399><c> vage</c><00:13:32.720><c> model</c>

00:13:33.150 --> 00:13:33.160 align:start position:0%
is that the benefits of large vage model
 

00:13:33.160 --> 00:13:35.990 align:start position:0%
is that the benefits of large vage model
depends<00:13:33.519><c> on</c><00:13:33.680><c> the</c><00:13:34.160><c> combination</c><00:13:35.160><c> of</c><00:13:35.399><c> the</c><00:13:35.560><c> usage</c>

00:13:35.990 --> 00:13:36.000 align:start position:0%
depends on the combination of the usage
 

00:13:36.000 --> 00:13:38.710 align:start position:0%
depends on the combination of the usage
modality<00:13:36.920><c> as</c><00:13:37.079><c> well</c><00:13:37.320><c> as</c><00:13:37.600><c> the</c><00:13:37.760><c> humans</c><00:13:38.160><c> expertise</c>

00:13:38.710 --> 00:13:38.720 align:start position:0%
modality as well as the humans expertise
 

00:13:38.720 --> 00:13:41.430 align:start position:0%
modality as well as the humans expertise
level<00:13:39.440><c> so</c><00:13:39.800><c> Ghost</c><00:13:40.079><c> Rider</c><00:13:40.399><c> hars</c><00:13:40.800><c> the</c><00:13:41.000><c> experts</c>

00:13:41.430 --> 00:13:41.440 align:start position:0%
level so Ghost Rider hars the experts
 

00:13:41.440 --> 00:13:46.750 align:start position:0%
level so Ghost Rider hars the experts
are<00:13:41.639><c> sounding</c><00:13:42.040><c> more</c><00:13:42.680><c> benefits</c><00:13:43.680><c> the</c><00:13:44.600><c> um</c><00:13:45.600><c> in</c><00:13:45.839><c> nor</c>

00:13:46.750 --> 00:13:46.760 align:start position:0%
are sounding more benefits the um in nor
 

00:13:46.760 --> 00:13:50.150 align:start position:0%
are sounding more benefits the um in nor
experts<00:13:47.760><c> now</c><00:13:48.360><c> we</c><00:13:48.480><c> are</c><00:13:48.720><c> curious</c><00:13:49.199><c> about</c><00:13:49.959><c> uh</c>

00:13:50.150 --> 00:13:50.160 align:start position:0%
experts now we are curious about uh
 

00:13:50.160 --> 00:13:52.269 align:start position:0%
experts now we are curious about uh
what's<00:13:50.440><c> the</c><00:13:50.680><c> underlying</c><00:13:51.440><c> mechanisms</c><00:13:52.160><c> that</c>

00:13:52.269 --> 00:13:52.279 align:start position:0%
what's the underlying mechanisms that
 

00:13:52.279 --> 00:13:55.350 align:start position:0%
what's the underlying mechanisms that
may<00:13:52.399><c> be</c><00:13:52.600><c> driving</c><00:13:53.279><c> this</c><00:13:53.480><c> neon</c><00:13:54.040><c> effects</c><00:13:54.880><c> so</c><00:13:55.120><c> here</c>

00:13:55.350 --> 00:13:55.360 align:start position:0%
may be driving this neon effects so here
 

00:13:55.360 --> 00:13:57.790 align:start position:0%
may be driving this neon effects so here
we<00:13:55.800><c> rely</c><00:13:56.199><c> on</c><00:13:56.360><c> the</c><00:13:56.560><c> TX</c><00:13:56.839><c> edings</c><00:13:57.440><c> that</c><00:13:57.560><c> we</c>

00:13:57.790 --> 00:13:57.800 align:start position:0%
we rely on the TX edings that we
 

00:13:57.800 --> 00:14:01.030 align:start position:0%
we rely on the TX edings that we
extracted<00:13:58.480><c> from</c><00:13:59.040><c> the</c><00:13:59.759><c> um</c><00:14:00.120><c> final</c><00:14:00.480><c> submitted</c>

00:14:01.030 --> 00:14:01.040 align:start position:0%
extracted from the um final submitted
 

00:14:01.040 --> 00:14:03.910 align:start position:0%
extracted from the um final submitted
ads<00:14:01.600><c> as</c><00:14:01.720><c> well</c><00:14:01.920><c> as</c><00:14:02.079><c> the</c><00:14:02.240><c> track</c><00:14:02.600><c> history</c><00:14:03.360><c> to</c>

00:14:03.910 --> 00:14:03.920 align:start position:0%
ads as well as the track history to
 

00:14:03.920 --> 00:14:05.389 align:start position:0%
ads as well as the track history to
perform<00:14:04.279><c> a</c><00:14:04.399><c> testal</c>

00:14:05.389 --> 00:14:05.399 align:start position:0%
perform a testal
 

00:14:05.399 --> 00:14:08.749 align:start position:0%
perform a testal
analysis<00:14:06.399><c> so</c><00:14:06.560><c> the</c><00:14:06.759><c> first</c><00:14:07.000><c> thing</c><00:14:07.240><c> we</c><00:14:07.920><c> do</c><00:14:08.440><c> is</c><00:14:08.560><c> to</c>

00:14:08.749 --> 00:14:08.759 align:start position:0%
analysis so the first thing we do is to
 

00:14:08.759 --> 00:14:11.949 align:start position:0%
analysis so the first thing we do is to
look<00:14:08.920><c> at</c><00:14:09.079><c> the</c><00:14:09.240><c> originality</c><00:14:10.040><c> of</c><00:14:10.199><c> the</c><00:14:10.440><c> sets</c><00:14:11.320><c> so</c>

00:14:11.949 --> 00:14:11.959 align:start position:0%
look at the originality of the sets so
 

00:14:11.959 --> 00:14:15.069 align:start position:0%
look at the originality of the sets so
but<00:14:12.279><c> uh</c><00:14:13.000><c> we</c><00:14:13.320><c> obtain</c><00:14:13.759><c> all</c><00:14:13.880><c> the</c><00:14:14.000><c> TX</c><00:14:14.360><c> Bings</c><00:14:14.759><c> of</c><00:14:14.920><c> the</c>

00:14:15.069 --> 00:14:15.079 align:start position:0%
but uh we obtain all the TX Bings of the
 

00:14:15.079 --> 00:14:18.629 align:start position:0%
but uh we obtain all the TX Bings of the
final<00:14:15.560><c> submitt</c><00:14:16.079><c> Xs</c><00:14:16.959><c> and</c><00:14:17.160><c> we</c><00:14:17.399><c> calculate</c><00:14:17.920><c> this</c><00:14:18.160><c> V</c>

00:14:18.629 --> 00:14:18.639 align:start position:0%
final submitt Xs and we calculate this V
 

00:14:18.639 --> 00:14:20.710 align:start position:0%
final submitt Xs and we calculate this V
Divergence<00:14:19.360><c> which</c><00:14:19.440><c> is</c><00:14:19.560><c> a</c><00:14:19.759><c> very</c><00:14:20.040><c> popular</c><00:14:20.480><c> way</c>

00:14:20.710 --> 00:14:20.720 align:start position:0%
Divergence which is a very popular way
 

00:14:20.720 --> 00:14:24.189 align:start position:0%
Divergence which is a very popular way
of<00:14:20.959><c> measuring</c><00:14:21.720><c> how</c><00:14:22.720><c> original</c><00:14:23.639><c> or</c><00:14:23.920><c> how</c>

00:14:24.189 --> 00:14:24.199 align:start position:0%
of measuring how original or how
 

00:14:24.199 --> 00:14:28.189 align:start position:0%
of measuring how original or how
creative<00:14:25.199><c> um</c><00:14:25.959><c> the</c><00:14:26.480><c> uh</c><00:14:26.680><c> the</c><00:14:27.040><c> the</c><00:14:27.199><c> the</c><00:14:27.440><c> text</c><00:14:27.920><c> is</c>

00:14:28.189 --> 00:14:28.199 align:start position:0%
creative um the uh the the the text is
 

00:14:28.199 --> 00:14:30.470 align:start position:0%
creative um the uh the the the text is
within<00:14:28.519><c> a</c><00:14:28.800><c> group</c><00:14:29.360><c> so</c><00:14:29.639><c> here</c><00:14:29.800><c> we</c><00:14:29.959><c> calculate</c><00:14:30.320><c> the</c>

00:14:30.470 --> 00:14:30.480 align:start position:0%
within a group so here we calculate the
 

00:14:30.480 --> 00:14:32.749 align:start position:0%
within a group so here we calculate the
cosine<00:14:30.920><c> distance</c><00:14:31.279><c> of</c><00:14:31.440><c> each</c><00:14:31.839><c> Tex</c><00:14:32.120><c> and</c><00:14:32.279><c> Bing</c>

00:14:32.749 --> 00:14:32.759 align:start position:0%
cosine distance of each Tex and Bing
 

00:14:32.759 --> 00:14:35.189 align:start position:0%
cosine distance of each Tex and Bing
with<00:14:33.000><c> respect</c><00:14:33.279><c> to</c><00:14:33.440><c> its</c><00:14:33.600><c> own</c><00:14:33.800><c> cint</c><00:14:34.759><c> so</c><00:14:34.959><c> the</c>

00:14:35.189 --> 00:14:35.199 align:start position:0%
with respect to its own cint so the
 

00:14:35.199 --> 00:14:37.870 align:start position:0%
with respect to its own cint so the
higher<00:14:35.759><c> distance</c><00:14:36.440><c> the</c><00:14:36.720><c> higher</c><00:14:37.079><c> the</c>

00:14:37.870 --> 00:14:37.880 align:start position:0%
higher distance the higher the
 

00:14:37.880 --> 00:14:39.829 align:start position:0%
higher distance the higher the
originality<00:14:38.880><c> and</c><00:14:39.079><c> here</c><00:14:39.279><c> we</c><00:14:39.440><c> find</c><00:14:39.680><c> that</c>

00:14:39.829 --> 00:14:39.839 align:start position:0%
originality and here we find that
 

00:14:39.839 --> 00:14:42.150 align:start position:0%
originality and here we find that
compared<00:14:40.199><c> to</c><00:14:40.399><c> control</c><00:14:40.800><c> group</c><00:14:41.160><c> the</c><00:14:41.320><c> S</c><00:14:41.720><c> B</c><00:14:41.920><c> group</c>

00:14:42.150 --> 00:14:42.160 align:start position:0%
compared to control group the S B group
 

00:14:42.160 --> 00:14:44.870 align:start position:0%
compared to control group the S B group
has<00:14:42.240><c> a</c><00:14:42.399><c> slightly</c><00:14:42.880><c> lower</c><00:14:43.560><c> at</c><00:14:43.880><c> originality</c>

00:14:44.870 --> 00:14:44.880 align:start position:0%
has a slightly lower at originality
 

00:14:44.880 --> 00:14:47.990 align:start position:0%
has a slightly lower at originality
whereas<00:14:45.320><c> the</c><00:14:45.480><c> G</c><00:14:46.120><c> group</c><00:14:46.360><c> has</c><00:14:46.440><c> a</c><00:14:46.759><c> much</c><00:14:47.079><c> lower</c>

00:14:47.990 --> 00:14:48.000 align:start position:0%
whereas the G group has a much lower
 

00:14:48.000 --> 00:14:50.629 align:start position:0%
whereas the G group has a much lower
originality<00:14:49.000><c> so</c><00:14:49.320><c> this</c><00:14:49.680><c> is</c><00:14:49.880><c> the</c><00:14:50.079><c> first</c><00:14:50.399><c> piece</c>

00:14:50.629 --> 00:14:50.639 align:start position:0%
originality so this is the first piece
 

00:14:50.639 --> 00:14:53.389 align:start position:0%
originality so this is the first piece
of<00:14:50.839><c> evidence</c><00:14:51.279><c> suggesting</c><00:14:51.880><c> of</c><00:14:52.199><c> the</c><00:14:52.560><c> potential</c>

00:14:53.389 --> 00:14:53.399 align:start position:0%
of evidence suggesting of the potential
 

00:14:53.399 --> 00:14:56.470 align:start position:0%
of evidence suggesting of the potential
anchoring<00:14:54.480><c> effects</c><00:14:55.480><c> and</c><00:14:55.720><c> next</c><00:14:56.000><c> we</c><00:14:56.160><c> want</c><00:14:56.320><c> to</c>

00:14:56.470 --> 00:14:56.480 align:start position:0%
anchoring effects and next we want to
 

00:14:56.480 --> 00:14:58.670 align:start position:0%
anchoring effects and next we want to
have<00:14:56.639><c> a</c><00:14:56.839><c> more</c><00:14:57.320><c> direct</c><00:14:57.880><c> evidence</c><00:14:58.320><c> of</c><00:14:58.440><c> the</c>

00:14:58.670 --> 00:14:58.680 align:start position:0%
have a more direct evidence of the
 

00:14:58.680 --> 00:15:00.949 align:start position:0%
have a more direct evidence of the
anchoring<00:14:59.079><c> effect</c><00:14:59.680><c> so</c><00:14:59.880><c> what</c><00:15:00.000><c> we</c><00:15:00.199><c> do</c><00:15:00.600><c> is</c><00:15:00.759><c> that</c>

00:15:00.949 --> 00:15:00.959 align:start position:0%
anchoring effect so what we do is that
 

00:15:00.959 --> 00:15:02.590 align:start position:0%
anchoring effect so what we do is that
we<00:15:01.079><c> look</c><00:15:01.240><c> at</c><00:15:01.360><c> the</c><00:15:01.480><c> track</c><00:15:01.800><c> history</c><00:15:02.279><c> and</c>

00:15:02.590 --> 00:15:02.600 align:start position:0%
we look at the track history and
 

00:15:02.600 --> 00:15:06.230 align:start position:0%
we look at the track history and
extracted<00:15:03.600><c> the</c><00:15:03.839><c> first</c><00:15:04.199><c> ever</c><00:15:04.680><c> draft</c><00:15:05.560><c> of</c><00:15:05.759><c> the</c><00:15:05.920><c> a</c>

00:15:06.230 --> 00:15:06.240 align:start position:0%
extracted the first ever draft of the a
 

00:15:06.240 --> 00:15:09.269 align:start position:0%
extracted the first ever draft of the a
copy<00:15:06.639><c> that's</c><00:15:06.920><c> generated</c><00:15:07.880><c> uh</c><00:15:08.000><c> in</c><00:15:08.120><c> the</c><00:15:08.399><c> process</c>

00:15:09.269 --> 00:15:09.279 align:start position:0%
copy that's generated uh in the process
 

00:15:09.279 --> 00:15:12.069 align:start position:0%
copy that's generated uh in the process
of<00:15:09.480><c> engaging</c><00:15:09.920><c> with</c><00:15:10.120><c> FL</c><00:15:10.680><c> models</c><00:15:11.639><c> uh</c><00:15:11.759><c> using</c>

00:15:12.069 --> 00:15:12.079 align:start position:0%
of engaging with FL models uh using
 

00:15:12.079 --> 00:15:14.829 align:start position:0%
of engaging with FL models uh using
sounding<00:15:12.600><c> board</c><00:15:12.920><c> modality</c><00:15:13.720><c> as</c><00:15:13.920><c> the</c><00:15:14.120><c> Baseline</c>

00:15:14.829 --> 00:15:14.839 align:start position:0%
sounding board modality as the Baseline
 

00:15:14.839 --> 00:15:17.110 align:start position:0%
sounding board modality as the Baseline
we<00:15:15.000><c> find</c><00:15:15.320><c> that</c><00:15:15.639><c> the</c><00:15:16.240><c> uh</c><00:15:16.360><c> participants</c><00:15:16.839><c> in</c><00:15:16.959><c> the</c>

00:15:17.110 --> 00:15:17.120 align:start position:0%
we find that the uh participants in the
 

00:15:17.120 --> 00:15:21.030 align:start position:0%
we find that the uh participants in the
go<00:15:17.680><c> modality</c><00:15:18.320><c> actually</c><00:15:19.279><c> uh</c><00:15:19.440><c> revise</c><00:15:20.240><c> their</c><00:15:20.519><c> ads</c>

00:15:21.030 --> 00:15:21.040 align:start position:0%
go modality actually uh revise their ads
 

00:15:21.040 --> 00:15:23.710 align:start position:0%
go modality actually uh revise their ads
throughout<00:15:21.360><c> the</c><00:15:21.639><c> process</c><00:15:22.120><c> much</c><00:15:22.440><c> less</c><00:15:23.440><c> than</c>

00:15:23.710 --> 00:15:23.720 align:start position:0%
throughout the process much less than
 

00:15:23.720 --> 00:15:26.470 align:start position:0%
throughout the process much less than
the<00:15:24.199><c> sounding</c><00:15:24.560><c> bity</c><00:15:25.480><c> which</c><00:15:25.600><c> is</c><00:15:25.680><c> a</c><00:15:25.839><c> more</c><00:15:26.120><c> direct</c>

00:15:26.470 --> 00:15:26.480 align:start position:0%
the sounding bity which is a more direct
 

00:15:26.480 --> 00:15:29.670 align:start position:0%
the sounding bity which is a more direct
evidence<00:15:26.920><c> of</c><00:15:27.079><c> the</c><00:15:27.160><c> entering</c><00:15:27.720><c> ver</c><00:15:29.079><c> and</c><00:15:29.279><c> finally</c>

00:15:29.670 --> 00:15:29.680 align:start position:0%
evidence of the entering ver and finally
 

00:15:29.680 --> 00:15:32.790 align:start position:0%
evidence of the entering ver and finally
we<00:15:29.759><c> are</c><00:15:29.959><c> curious</c><00:15:30.360><c> about</c><00:15:31.240><c> how</c><00:15:31.920><c> does</c><00:15:32.279><c> the</c><00:15:32.480><c> non</c>

00:15:32.790 --> 00:15:32.800 align:start position:0%
we are curious about how does the non
 

00:15:32.800 --> 00:15:35.470 align:start position:0%
we are curious about how does the non
expus<00:15:33.600><c> gain</c><00:15:33.959><c> such</c><00:15:34.079><c> a</c><00:15:34.279><c> significant</c><00:15:34.839><c> Boost</c><00:15:35.199><c> from</c>

00:15:35.470 --> 00:15:35.480 align:start position:0%
expus gain such a significant Boost from
 

00:15:35.480 --> 00:15:39.430 align:start position:0%
expus gain such a significant Boost from
using<00:15:35.920><c> the</c><00:15:36.079><c> sounding</c><00:15:36.519><c> B</c><00:15:36.920><c> modality</c><00:15:38.160><c> well</c><00:15:39.160><c> we</c>

00:15:39.430 --> 00:15:39.440 align:start position:0%
using the sounding B modality well we
 

00:15:39.440 --> 00:15:42.309 align:start position:0%
using the sounding B modality well we
then<00:15:39.680><c> look</c><00:15:39.920><c> at</c><00:15:40.720><c> uh</c><00:15:41.040><c> whether</c><00:15:41.480><c> these</c><00:15:42.040><c> non</c>

00:15:42.309 --> 00:15:42.319 align:start position:0%
then look at uh whether these non
 

00:15:42.319 --> 00:15:44.389 align:start position:0%
then look at uh whether these non
experts<00:15:42.759><c> are</c><00:15:43.000><c> writing</c><00:15:43.480><c> more</c><00:15:43.720><c> like</c><00:15:43.959><c> human</c>

00:15:44.389 --> 00:15:44.399 align:start position:0%
experts are writing more like human
 

00:15:44.399 --> 00:15:48.030 align:start position:0%
experts are writing more like human
experts<00:15:45.399><c> so</c><00:15:45.600><c> to</c><00:15:45.759><c> do</c><00:15:45.959><c> so</c><00:15:46.560><c> this</c><00:15:46.680><c> is</c><00:15:46.839><c> our</c><00:15:47.199><c> approach</c>

00:15:48.030 --> 00:15:48.040 align:start position:0%
experts so to do so this is our approach
 

00:15:48.040 --> 00:15:51.790 align:start position:0%
experts so to do so this is our approach
First<00:15:48.560><c> We</c><00:15:48.880><c> Take</c><00:15:49.880><c> the</c><00:15:50.160><c> TX</c><00:15:50.600><c> Bing</c><00:15:51.160><c> from</c><00:15:51.480><c> the</c>

00:15:51.790 --> 00:15:51.800 align:start position:0%
First We Take the TX Bing from the
 

00:15:51.800 --> 00:15:54.749 align:start position:0%
First We Take the TX Bing from the
experts<00:15:52.800><c> in</c><00:15:53.000><c> the</c><00:15:53.199><c> control</c><00:15:53.800><c> group</c><00:15:54.519><c> and</c>

00:15:54.749 --> 00:15:54.759 align:start position:0%
experts in the control group and
 

00:15:54.759 --> 00:15:56.749 align:start position:0%
experts in the control group and
calculate<00:15:55.160><c> It</c><00:15:55.360><c> Center</c><00:15:55.920><c> which</c><00:15:56.079><c> is</c><00:15:56.240><c> the</c><00:15:56.440><c> Green</c>

00:15:56.749 --> 00:15:56.759 align:start position:0%
calculate It Center which is the Green
 

00:15:56.759 --> 00:16:00.949 align:start position:0%
calculate It Center which is the Green
Dot<00:15:57.120><c> over</c><00:15:57.600><c> here</c><00:15:58.839><c> then</c><00:15:59.680><c> we</c><00:15:59.880><c> take</c><00:16:00.120><c> this</c><00:16:00.279><c> centroid</c>

00:16:00.949 --> 00:16:00.959 align:start position:0%
Dot over here then we take this centroid
 

00:16:00.959 --> 00:16:03.790 align:start position:0%
Dot over here then we take this centroid
and<00:16:01.160><c> look</c><00:16:01.360><c> at</c><00:16:01.480><c> the</c><00:16:01.639><c> null</c><00:16:02.040><c> experts</c><00:16:02.759><c> across</c><00:16:03.560><c> all</c>

00:16:03.790 --> 00:16:03.800 align:start position:0%
and look at the null experts across all
 

00:16:03.800 --> 00:16:06.949 align:start position:0%
and look at the null experts across all
the<00:16:04.079><c> three</c><00:16:04.399><c> treatment</c><00:16:05.279><c> groups</c><00:16:06.279><c> and</c><00:16:06.440><c> then</c><00:16:06.759><c> we</c>

00:16:06.949 --> 00:16:06.959 align:start position:0%
the three treatment groups and then we
 

00:16:06.959 --> 00:16:09.990 align:start position:0%
the three treatment groups and then we
calculate<00:16:07.360><c> the</c><00:16:07.560><c> distance</c><00:16:08.560><c> between</c><00:16:09.560><c> uh</c><00:16:09.800><c> these</c>

00:16:09.990 --> 00:16:10.000 align:start position:0%
calculate the distance between uh these
 

00:16:10.000 --> 00:16:12.910 align:start position:0%
calculate the distance between uh these
non<00:16:10.360><c> experts</c><00:16:10.839><c> embeddings</c><00:16:11.720><c> with</c><00:16:12.000><c> respect</c><00:16:12.360><c> to</c>

00:16:12.910 --> 00:16:12.920 align:start position:0%
non experts embeddings with respect to
 

00:16:12.920 --> 00:16:15.430 align:start position:0%
non experts embeddings with respect to
the<00:16:13.240><c> expert</c><00:16:13.720><c> embeddings</c><00:16:14.160><c> in</c><00:16:14.279><c> the</c>

00:16:15.430 --> 00:16:15.440 align:start position:0%
the expert embeddings in the
 

00:16:15.440 --> 00:16:18.910 align:start position:0%
the expert embeddings in the
control<00:16:16.440><c> so</c><00:16:16.759><c> these</c><00:16:17.079><c> distances</c><00:16:17.839><c> would</c><00:16:18.519><c> reflect</c>

00:16:18.910 --> 00:16:18.920 align:start position:0%
control so these distances would reflect
 

00:16:18.920 --> 00:16:21.430 align:start position:0%
control so these distances would reflect
the<00:16:19.160><c> distance</c><00:16:19.880><c> uh</c><00:16:20.040><c> how</c><00:16:20.319><c> different</c><00:16:20.920><c> the</c><00:16:21.120><c> N</c>

00:16:21.430 --> 00:16:21.440 align:start position:0%
the distance uh how different the N
 

00:16:21.440 --> 00:16:24.749 align:start position:0%
the distance uh how different the N
experts<00:16:21.839><c> and</c><00:16:22.040><c> experts</c><00:16:22.680><c> are</c><00:16:23.680><c> um</c><00:16:24.399><c> in</c><00:16:24.519><c> the</c>

00:16:24.749 --> 00:16:24.759 align:start position:0%
experts and experts are um in the
 

00:16:24.759 --> 00:16:28.069 align:start position:0%
experts and experts are um in the
experiment<00:16:25.759><c> so</c><00:16:26.399><c> here</c><00:16:26.639><c> we</c><00:16:26.800><c> find</c><00:16:27.160><c> that</c><00:16:27.480><c> there</c><00:16:27.639><c> is</c>

00:16:28.069 --> 00:16:28.079 align:start position:0%
experiment so here we find that there is
 

00:16:28.079 --> 00:16:30.829 align:start position:0%
experiment so here we find that there is
a<00:16:28.600><c> the</c><00:16:28.800><c> distance</c><00:16:29.199><c> is</c><00:16:29.399><c> similar</c><00:16:29.959><c> in</c><00:16:30.160><c> the</c><00:16:30.399><c> control</c>

00:16:30.829 --> 00:16:30.839 align:start position:0%
a the distance is similar in the control
 

00:16:30.839 --> 00:16:32.710 align:start position:0%
a the distance is similar in the control
group<00:16:31.040><c> and</c><00:16:31.160><c> the</c><00:16:31.240><c> goost</c><00:16:31.519><c> wrer</c><00:16:31.839><c> group</c><00:16:32.480><c> but</c><00:16:32.600><c> for</c>

00:16:32.710 --> 00:16:32.720 align:start position:0%
group and the goost wrer group but for
 

00:16:32.720 --> 00:16:34.790 align:start position:0%
group and the goost wrer group but for
the<00:16:32.839><c> sounding</c><00:16:33.319><c> B</c><00:16:33.519><c> group</c><00:16:33.759><c> we</c><00:16:33.880><c> find</c><00:16:34.120><c> that</c><00:16:34.279><c> non</c>

00:16:34.790 --> 00:16:34.800 align:start position:0%
the sounding B group we find that non
 

00:16:34.800 --> 00:16:39.470 align:start position:0%
the sounding B group we find that non
experts<00:16:35.800><c> are</c><00:16:36.160><c> much</c><00:16:36.639><c> closer</c><00:16:37.639><c> to</c><00:16:38.000><c> the</c><00:16:38.920><c> experts</c>

00:16:39.470 --> 00:16:39.480 align:start position:0%
experts are much closer to the experts
 

00:16:39.480 --> 00:16:41.910 align:start position:0%
experts are much closer to the experts
the<00:16:39.600><c> control</c><00:16:40.000><c> group</c><00:16:40.399><c> meaning</c><00:16:40.880><c> that</c><00:16:41.639><c> if</c><00:16:41.720><c> you</c>

00:16:41.910 --> 00:16:41.920 align:start position:0%
the control group meaning that if you
 

00:16:41.920 --> 00:16:45.030 align:start position:0%
the control group meaning that if you
use<00:16:42.199><c> LM</c><00:16:42.800><c> sounding</c><00:16:43.399><c> group</c><00:16:44.120><c> no</c><00:16:44.440><c> experts</c>

00:16:45.030 --> 00:16:45.040 align:start position:0%
use LM sounding group no experts
 

00:16:45.040 --> 00:16:46.790 align:start position:0%
use LM sounding group no experts
actually<00:16:45.360><c> learn</c><00:16:45.800><c> to</c><00:16:46.079><c> write</c><00:16:46.319><c> more</c><00:16:46.519><c> like</c>

00:16:46.790 --> 00:16:46.800 align:start position:0%
actually learn to write more like
 

00:16:46.800 --> 00:16:48.350 align:start position:0%
actually learn to write more like
experts<00:16:47.440><c> in</c><00:16:47.560><c> the</c>

00:16:48.350 --> 00:16:48.360 align:start position:0%
experts in the
 

00:16:48.360 --> 00:16:51.350 align:start position:0%
experts in the
control<00:16:49.360><c> so</c><00:16:49.600><c> that</c><00:16:49.759><c> also</c><00:16:50.040><c> helps</c><00:16:50.319><c> us</c><00:16:50.480><c> to</c><00:16:50.720><c> explain</c>

00:16:51.350 --> 00:16:51.360 align:start position:0%
control so that also helps us to explain
 

00:16:51.360 --> 00:16:54.430 align:start position:0%
control so that also helps us to explain
why<00:16:51.600><c> we</c><00:16:51.880><c> observe</c><00:16:52.319><c> a</c><00:16:52.519><c> slightly</c><00:16:52.920><c> lower</c><00:16:53.800><c> uh</c><00:16:53.959><c> syic</c>

00:16:54.430 --> 00:16:54.440 align:start position:0%
why we observe a slightly lower uh syic
 

00:16:54.440 --> 00:16:56.269 align:start position:0%
why we observe a slightly lower uh syic
Divergence<00:16:55.199><c> in</c><00:16:55.319><c> the</c><00:16:55.480><c> ghost</c><00:16:55.720><c> wrer</c><00:16:55.959><c> group</c><00:16:56.160><c> in</c>

00:16:56.269 --> 00:16:56.279 align:start position:0%
Divergence in the ghost wrer group in
 

00:16:56.279 --> 00:16:58.790 align:start position:0%
Divergence in the ghost wrer group in
our<00:16:56.480><c> first</c><00:16:56.720><c> test</c><00:16:57.079><c> because</c><00:16:57.680><c> if</c><00:16:57.959><c> not</c><00:16:58.199><c> EXP</c><00:16:58.600><c> are</c>

00:16:58.790 --> 00:16:58.800 align:start position:0%
our first test because if not EXP are
 

00:16:58.800 --> 00:17:00.749 align:start position:0%
our first test because if not EXP are
writing<00:16:59.120><c> more</c><00:16:59.360><c> like</c><00:16:59.560><c> experts</c><00:17:00.120><c> you</c><00:17:00.440><c> when</c><00:17:00.560><c> you</c>

00:17:00.749 --> 00:17:00.759 align:start position:0%
writing more like experts you when you
 

00:17:00.759 --> 00:17:02.710 align:start position:0%
writing more like experts you when you
put<00:17:00.920><c> them</c><00:17:01.199><c> together</c><00:17:01.519><c> you</c><00:17:01.639><c> would</c><00:17:02.040><c> expect</c><00:17:02.519><c> a</c>

00:17:02.710 --> 00:17:02.720 align:start position:0%
put them together you would expect a
 

00:17:02.720 --> 00:17:05.990 align:start position:0%
put them together you would expect a
lower<00:17:03.279><c> group</c><00:17:03.600><c> level</c>

00:17:05.990 --> 00:17:06.000 align:start position:0%
 
 

00:17:06.000 --> 00:17:10.029 align:start position:0%
 
Divergence<00:17:07.000><c> so</c><00:17:07.839><c> um</c><00:17:08.079><c> to</c><00:17:08.600><c> summarize</c><00:17:09.600><c> um</c><00:17:09.839><c> the</c>

00:17:10.029 --> 00:17:10.039 align:start position:0%
Divergence so um to summarize um the
 

00:17:10.039 --> 00:17:12.110 align:start position:0%
Divergence so um to summarize um the
benefits<00:17:10.600><c> of</c><00:17:10.799><c> large</c><00:17:11.079><c> range</c><00:17:11.360><c> models</c><00:17:11.799><c> depends</c>

00:17:12.110 --> 00:17:12.120 align:start position:0%
benefits of large range models depends
 

00:17:12.120 --> 00:17:14.350 align:start position:0%
benefits of large range models depends
on<00:17:12.360><c> both</c><00:17:12.559><c> the</c><00:17:12.720><c> modality</c><00:17:13.319><c> and</c><00:17:13.439><c> the</c><00:17:13.600><c> expertise</c>

00:17:14.350 --> 00:17:14.360 align:start position:0%
on both the modality and the expertise
 

00:17:14.360 --> 00:17:16.829 align:start position:0%
on both the modality and the expertise
of<00:17:14.679><c> the</c><00:17:15.280><c> uh</c><00:17:15.400><c> users</c><00:17:15.959><c> in</c><00:17:16.120><c> the</c><00:17:16.319><c> context</c><00:17:16.679><c> of</c>

00:17:16.829 --> 00:17:16.839 align:start position:0%
of the uh users in the context of
 

00:17:16.839 --> 00:17:18.949 align:start position:0%
of the uh users in the context of
creative<00:17:17.280><c> task</c><00:17:18.000><c> using</c><00:17:18.360><c> large</c><00:17:18.640><c> language</c>

00:17:18.949 --> 00:17:18.959 align:start position:0%
creative task using large language
 

00:17:18.959 --> 00:17:21.789 align:start position:0%
creative task using large language
models<00:17:19.439><c> as</c><00:17:20.120><c> um</c><00:17:20.760><c> the</c><00:17:20.919><c> Ghost</c><00:17:21.199><c> Rider</c><00:17:21.520><c> actually</c>

00:17:21.789 --> 00:17:21.799 align:start position:0%
models as um the Ghost Rider actually
 

00:17:21.799 --> 00:17:25.909 align:start position:0%
models as um the Ghost Rider actually
hars<00:17:22.400><c> experts</c><00:17:23.400><c> making</c><00:17:23.799><c> their</c><00:17:24.120><c> ads</c><00:17:24.559><c> more</c><00:17:25.439><c> less</c>

00:17:25.909 --> 00:17:25.919 align:start position:0%
hars experts making their ads more less
 

00:17:25.919 --> 00:17:29.590 align:start position:0%
hars experts making their ads more less
original<00:17:26.919><c> and</c><00:17:27.360><c> making</c><00:17:27.679><c> them</c><00:17:27.919><c> Revis</c><00:17:28.600><c> less</c>

00:17:29.590 --> 00:17:29.600 align:start position:0%
original and making them Revis less
 

00:17:29.600 --> 00:17:31.470 align:start position:0%
original and making them Revis less
which<00:17:29.760><c> is</c><00:17:30.039><c> likely</c><00:17:30.360><c> due</c><00:17:30.559><c> to</c><00:17:30.799><c> the</c><00:17:31.000><c> anchoring</c>

00:17:31.470 --> 00:17:31.480 align:start position:0%
which is likely due to the anchoring
 

00:17:31.480 --> 00:17:33.870 align:start position:0%
which is likely due to the anchoring
effects<00:17:32.240><c> however</c><00:17:32.559><c> if</c><00:17:32.679><c> you</c><00:17:32.799><c> are</c><00:17:32.919><c> using</c><00:17:33.280><c> the</c><00:17:33.440><c> L</c>

00:17:33.870 --> 00:17:33.880 align:start position:0%
effects however if you are using the L
 

00:17:33.880 --> 00:17:36.230 align:start position:0%
effects however if you are using the L
models<00:17:34.200><c> as</c><00:17:34.360><c> sounding</c><00:17:34.720><c> board</c><00:17:35.160><c> no</c><00:17:35.520><c> experts</c>

00:17:36.230 --> 00:17:36.240 align:start position:0%
models as sounding board no experts
 

00:17:36.240 --> 00:17:38.870 align:start position:0%
models as sounding board no experts
benefit<00:17:36.760><c> from</c><00:17:37.000><c> it</c><00:17:37.320><c> by</c><00:17:37.600><c> learning</c><00:17:38.280><c> to</c><00:17:38.600><c> write</c>

00:17:38.870 --> 00:17:38.880 align:start position:0%
benefit from it by learning to write
 

00:17:38.880 --> 00:17:39.909 align:start position:0%
benefit from it by learning to write
more<00:17:39.120><c> like</c>

00:17:39.909 --> 00:17:39.919 align:start position:0%
more like
 

00:17:39.919 --> 00:17:42.430 align:start position:0%
more like
experts<00:17:40.919><c> so</c><00:17:41.280><c> that</c><00:17:41.520><c> concludes</c><00:17:42.080><c> my</c>

00:17:42.430 --> 00:17:42.440 align:start position:0%
experts so that concludes my
 

00:17:42.440 --> 00:17:44.950 align:start position:0%
experts so that concludes my
presentation<00:17:43.440><c> and</c><00:17:43.720><c> by</c><00:17:43.880><c> the</c><00:17:44.000><c> way</c><00:17:44.559><c> uh</c><00:17:44.760><c> in</c>

00:17:44.950 --> 00:17:44.960 align:start position:0%
presentation and by the way uh in
 

00:17:44.960 --> 00:17:46.710 align:start position:0%
presentation and by the way uh in
addition<00:17:45.240><c> to</c><00:17:45.440><c> the</c><00:17:45.760><c> first</c><00:17:46.080><c> example</c><00:17:46.480><c> that</c><00:17:46.600><c> I</c>

00:17:46.710 --> 00:17:46.720 align:start position:0%
addition to the first example that I
 

00:17:46.720 --> 00:17:49.029 align:start position:0%
addition to the first example that I
showed<00:17:47.240><c> where</c><00:17:47.400><c> you</c><00:17:47.520><c> can</c><00:17:47.720><c> utilize</c><00:17:48.440><c> AR</c><00:17:48.760><c> strange</c>

00:17:49.029 --> 00:17:49.039 align:start position:0%
showed where you can utilize AR strange
 

00:17:49.039 --> 00:17:51.150 align:start position:0%
showed where you can utilize AR strange
models<00:17:49.400><c> to</c><00:17:49.559><c> write</c><00:17:49.760><c> emails</c><00:17:50.120><c> for</c><00:17:50.320><c> you</c><00:17:50.840><c> I</c><00:17:50.960><c> have</c>

00:17:51.150 --> 00:17:51.160 align:start position:0%
models to write emails for you I have
 

00:17:51.160 --> 00:17:53.549 align:start position:0%
models to write emails for you I have
another<00:17:51.440><c> tool</c><00:17:51.840><c> that</c><00:17:52.280><c> accommodates</c><00:17:53.280><c> the</c>

00:17:53.549 --> 00:17:53.559 align:start position:0%
another tool that accommodates the
 

00:17:53.559 --> 00:17:55.390 align:start position:0%
another tool that accommodates the
sounding<00:17:53.960><c> B</c><00:17:54.240><c> modalities</c><00:17:54.880><c> that</c><00:17:55.000><c> you</c><00:17:55.120><c> can</c>

00:17:55.390 --> 00:17:55.400 align:start position:0%
sounding B modalities that you can
 

00:17:55.400 --> 00:17:57.630 align:start position:0%
sounding B modalities that you can
download<00:17:56.159><c> by</c><00:17:56.320><c> scanning</c><00:17:56.679><c> this</c><00:17:56.880><c> QR</c><00:17:57.240><c> code</c><00:17:57.480><c> it's</c>

00:17:57.630 --> 00:17:57.640 align:start position:0%
download by scanning this QR code it's
 

00:17:57.640 --> 00:17:59.029 align:start position:0%
download by scanning this QR code it's
also<00:17:57.880><c> open</c><00:17:58.159><c> source</c>

00:17:59.029 --> 00:17:59.039 align:start position:0%
also open source
 

00:17:59.039 --> 00:18:07.070 align:start position:0%
also open source
so<00:17:59.960><c> um</c><00:18:00.360><c> thank</c><00:18:00.520><c> you</c><00:18:01.039><c> and</c><00:18:01.280><c> open</c><00:18:01.520><c> to</c>

00:18:07.070 --> 00:18:07.080 align:start position:0%
 
 

00:18:07.080 --> 00:18:10.080 align:start position:0%
 
questions

