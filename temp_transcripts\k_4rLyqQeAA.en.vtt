WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.670 align:start position:0%
 
hi<00:00:00.269><c> guys</c><00:00:00.450><c> for</c><00:00:00.480><c> my</c><00:00:00.780><c> day</c><00:00:00.840><c> job</c><00:00:00.960><c> I'm</c><00:00:01.290><c> a</c><00:00:01.319><c> Python</c>

00:00:01.670 --> 00:00:01.680 align:start position:0%
hi guys for my day job I'm a Python
 

00:00:01.680 --> 00:00:03.020 align:start position:0%
hi guys for my day job I'm a Python
developer<00:00:01.740><c> but</c><00:00:02.280><c> today</c><00:00:02.460><c> I'd</c><00:00:02.550><c> like</c><00:00:02.610><c> to</c><00:00:02.730><c> talk</c><00:00:02.909><c> to</c>

00:00:03.020 --> 00:00:03.030 align:start position:0%
developer but today I'd like to talk to
 

00:00:03.030 --> 00:00:04.700 align:start position:0%
developer but today I'd like to talk to
you<00:00:03.120><c> about</c><00:00:03.179><c> Lisp</c><00:00:03.510><c> specifically</c><00:00:04.440><c> closure</c>

00:00:04.700 --> 00:00:04.710 align:start position:0%
you about Lisp specifically closure
 

00:00:04.710 --> 00:00:06.320 align:start position:0%
you about Lisp specifically closure
buckle<00:00:05.250><c> up</c><00:00:05.310><c> this</c><00:00:05.400><c> is</c><00:00:05.549><c> gonna</c><00:00:05.640><c> be</c><00:00:05.790><c> fast</c><00:00:06.000><c> I</c><00:00:06.240><c> need</c>

00:00:06.320 --> 00:00:06.330 align:start position:0%
buckle up this is gonna be fast I need
 

00:00:06.330 --> 00:00:07.700 align:start position:0%
buckle up this is gonna be fast I need
you<00:00:06.540><c> to</c><00:00:06.660><c> read</c><00:00:06.839><c> and</c><00:00:06.960><c> listen</c><00:00:07.200><c> at</c><00:00:07.290><c> the</c><00:00:07.350><c> same</c><00:00:07.410><c> time</c>

00:00:07.700 --> 00:00:07.710 align:start position:0%
you to read and listen at the same time
 

00:00:07.710 --> 00:00:09.500 align:start position:0%
you to read and listen at the same time
I<00:00:07.919><c> believe</c><00:00:08.160><c> in</c><00:00:08.460><c> you</c><00:00:08.700><c> have</c><00:00:09.000><c> I</c><00:00:09.090><c> got</c><00:00:09.240><c> a</c><00:00:09.269><c> deal</c><00:00:09.420><c> for</c>

00:00:09.500 --> 00:00:09.510 align:start position:0%
I believe in you have I got a deal for
 

00:00:09.510 --> 00:00:11.600 align:start position:0%
I believe in you have I got a deal for
you<00:00:09.750><c> closure</c><00:00:10.710><c> has</c><00:00:10.950><c> all</c><00:00:11.070><c> the</c><00:00:11.160><c> good</c><00:00:11.309><c> stuff</c><00:00:11.490><c> you</c>

00:00:11.600 --> 00:00:11.610 align:start position:0%
you closure has all the good stuff you
 

00:00:11.610 --> 00:00:13.280 align:start position:0%
you closure has all the good stuff you
expect<00:00:11.969><c> from</c><00:00:12.030><c> a</c><00:00:12.150><c> modern</c><00:00:12.269><c> language</c><00:00:12.480><c> but</c><00:00:12.929><c> also</c>

00:00:13.280 --> 00:00:13.290 align:start position:0%
expect from a modern language but also
 

00:00:13.290 --> 00:00:15.200 align:start position:0%
expect from a modern language but also
supports<00:00:13.710><c> any</c><00:00:13.889><c> possible</c><00:00:14.370><c> future</c><00:00:14.820><c> features</c>

00:00:15.200 --> 00:00:15.210 align:start position:0%
supports any possible future features
 

00:00:15.210 --> 00:00:16.849 align:start position:0%
supports any possible future features
you<00:00:15.389><c> need</c><00:00:15.540><c> for</c><00:00:15.719><c> your</c><00:00:15.780><c> next</c><00:00:15.960><c> project</c><00:00:16.619><c> it</c>

00:00:16.849 --> 00:00:16.859 align:start position:0%
you need for your next project it
 

00:00:16.859 --> 00:00:19.670 align:start position:0%
you need for your next project it
targets<00:00:17.220><c> the</c><00:00:17.279><c> JVM</c><00:00:17.670><c> Java</c><00:00:18.210><c> Script</c><00:00:18.510><c> CLR</c><00:00:18.990><c> and</c><00:00:19.230><c> kind</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
targets the JVM Java Script CLR and kind
 

00:00:19.680 --> 00:00:22.040 align:start position:0%
targets the JVM Java Script CLR and kind
of<00:00:19.800><c> Python</c><00:00:20.189><c> via</c><00:00:20.400><c> high</c><00:00:20.609><c> closure</c><00:00:21.270><c> is</c><00:00:21.510><c> concise</c>

00:00:22.040 --> 00:00:22.050 align:start position:0%
of Python via high closure is concise
 

00:00:22.050 --> 00:00:23.689 align:start position:0%
of Python via high closure is concise
this<00:00:22.740><c> isn't</c><00:00:23.070><c> exactly</c><00:00:23.130><c> the</c><00:00:23.460><c> same</c><00:00:23.519><c> as</c>

00:00:23.689 --> 00:00:23.699 align:start position:0%
this isn't exactly the same as
 

00:00:23.699 --> 00:00:26.480 align:start position:0%
this isn't exactly the same as
high-level<00:00:24.060><c> leti</c><00:00:24.779><c> but</c><00:00:25.439><c> it's</c><00:00:25.590><c> a</c><00:00:25.680><c> good</c><00:00:25.830><c> clue</c><00:00:26.039><c> by</c>

00:00:26.480 --> 00:00:26.490 align:start position:0%
high-level leti but it's a good clue by
 

00:00:26.490 --> 00:00:27.560 align:start position:0%
high-level leti but it's a good clue by
the<00:00:26.550><c> way</c><00:00:26.699><c> if</c><00:00:26.820><c> someone</c><00:00:27.000><c> can</c><00:00:27.180><c> tell</c><00:00:27.300><c> me</c><00:00:27.420><c> in</c><00:00:27.480><c> the</c>

00:00:27.560 --> 00:00:27.570 align:start position:0%
the way if someone can tell me in the
 

00:00:27.570 --> 00:00:29.120 align:start position:0%
the way if someone can tell me in the
comments<00:00:27.869><c> what</c><00:00:27.990><c> CoffeeScript</c><00:00:28.590><c> is</c><00:00:28.710><c> doing</c><00:00:28.920><c> so</c>

00:00:29.120 --> 00:00:29.130 align:start position:0%
comments what CoffeeScript is doing so
 

00:00:29.130 --> 00:00:30.470 align:start position:0%
comments what CoffeeScript is doing so
far<00:00:29.340><c> to</c><00:00:29.490><c> the</c><00:00:29.580><c> left</c><00:00:29.609><c> please</c><00:00:30.119><c> let</c><00:00:30.300><c> me</c><00:00:30.390><c> know</c>

00:00:30.470 --> 00:00:30.480 align:start position:0%
far to the left please let me know
 

00:00:30.480 --> 00:00:33.020 align:start position:0%
far to the left please let me know
closure<00:00:31.019><c> is</c><00:00:31.260><c> popular</c><00:00:31.740><c> here's</c><00:00:32.460><c> a</c><00:00:32.550><c> matrix</c><00:00:32.700><c> of</c>

00:00:33.020 --> 00:00:33.030 align:start position:0%
closure is popular here's a matrix of
 

00:00:33.030 --> 00:00:34.400 align:start position:0%
closure is popular here's a matrix of
how<00:00:33.120><c> popular</c><00:00:33.300><c> programming</c><00:00:33.809><c> languages</c><00:00:34.110><c> arm</c>

00:00:34.400 --> 00:00:34.410 align:start position:0%
how popular programming languages arm
 

00:00:34.410 --> 00:00:35.870 align:start position:0%
how popular programming languages arm
from<00:00:34.680><c> Nimrod</c><00:00:35.040><c> at</c><00:00:35.160><c> the</c><00:00:35.219><c> bottom</c><00:00:35.489><c> left</c><00:00:35.670><c> its</c>

00:00:35.870 --> 00:00:35.880 align:start position:0%
from Nimrod at the bottom left its
 

00:00:35.880 --> 00:00:37.250 align:start position:0%
from Nimrod at the bottom left its
languages<00:00:36.300><c> with</c><00:00:36.420><c> Java</c><00:00:36.660><c> in</c><00:00:36.840><c> the</c><00:00:36.930><c> name</c><00:00:37.079><c> of</c><00:00:37.200><c> the</c>

00:00:37.250 --> 00:00:37.260 align:start position:0%
languages with Java in the name of the
 

00:00:37.260 --> 00:00:39.229 align:start position:0%
languages with Java in the name of the
top<00:00:37.500><c> Coughlin's</c><00:00:38.070><c> on</c><00:00:38.340><c> the</c><00:00:38.430><c> way</c><00:00:38.520><c> up</c><00:00:38.820><c> closure</c><00:00:39.030><c> is</c>

00:00:39.229 --> 00:00:39.239 align:start position:0%
top Coughlin's on the way up closure is
 

00:00:39.239 --> 00:00:41.030 align:start position:0%
top Coughlin's on the way up closure is
as<00:00:39.329><c> popular</c><00:00:39.690><c> as</c><00:00:39.780><c> go</c><00:00:39.960><c> swift</c><00:00:40.500><c> and</c><00:00:40.710><c> Haskell</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
as popular as go swift and Haskell
 

00:00:41.040 --> 00:00:43.880 align:start position:0%
as popular as go swift and Haskell
closure<00:00:41.910><c> is</c><00:00:42.120><c> a</c><00:00:42.149><c> fantastic</c><00:00:42.870><c> modern</c><00:00:43.320><c> Lisp</c><00:00:43.590><c> a</c>

00:00:43.880 --> 00:00:43.890 align:start position:0%
closure is a fantastic modern Lisp a
 

00:00:43.890 --> 00:00:45.440 align:start position:0%
closure is a fantastic modern Lisp a
truly<00:00:44.280><c> powerful</c><00:00:44.489><c> weapon</c><00:00:44.850><c> we</c><00:00:45.120><c> keep</c><00:00:45.300><c> making</c>

00:00:45.440 --> 00:00:45.450 align:start position:0%
truly powerful weapon we keep making
 

00:00:45.450 --> 00:00:47.660 align:start position:0%
truly powerful weapon we keep making
inferior<00:00:46.170><c> languages</c><00:00:46.680><c> to</c><00:00:46.800><c> Lisp</c><00:00:47.010><c> and</c><00:00:47.250><c> here's</c>

00:00:47.660 --> 00:00:47.670 align:start position:0%
inferior languages to Lisp and here's
 

00:00:47.670 --> 00:00:49.160 align:start position:0%
inferior languages to Lisp and here's
what<00:00:47.789><c> I</c><00:00:47.820><c> mean</c><00:00:47.879><c> by</c><00:00:48.149><c> that</c><00:00:48.510><c> Lisp</c><00:00:48.840><c> had</c><00:00:49.050><c> these</c>

00:00:49.160 --> 00:00:49.170 align:start position:0%
what I mean by that Lisp had these
 

00:00:49.170 --> 00:00:51.889 align:start position:0%
what I mean by that Lisp had these
sorted<00:00:49.590><c> out</c><00:00:49.739><c> in</c><00:00:49.920><c> the</c><00:00:50.100><c> 1950s</c><00:00:51.090><c> a</c><00:00:51.329><c> highlight</c><00:00:51.870><c> a</c>

00:00:51.889 --> 00:00:51.899 align:start position:0%
sorted out in the 1950s a highlight a
 

00:00:51.899 --> 00:00:54.439 align:start position:0%
sorted out in the 1950s a highlight a
few<00:00:51.989><c> number</c><00:00:52.829><c> six</c><00:00:53.039><c> statements</c><00:00:54.000><c> in</c><00:00:54.329><c> other</c>

00:00:54.439 --> 00:00:54.449 align:start position:0%
few number six statements in other
 

00:00:54.449 --> 00:00:56.540 align:start position:0%
few number six statements in other
languages<00:00:54.870><c> are</c><00:00:55.050><c> line</c><00:00:55.289><c> oriented</c><00:00:55.620><c> which</c><00:00:56.399><c> is</c><00:00:56.520><c> a</c>

00:00:56.540 --> 00:00:56.550 align:start position:0%
languages are line oriented which is a
 

00:00:56.550 --> 00:00:59.060 align:start position:0%
languages are line oriented which is a
relic<00:00:56.850><c> of</c><00:00:56.969><c> punched</c><00:00:57.300><c> cards</c><00:00:57.739><c> most</c><00:00:58.739><c> languages</c>

00:00:59.060 --> 00:00:59.070 align:start position:0%
relic of punched cards most languages
 

00:00:59.070 --> 00:01:01.130 align:start position:0%
relic of punched cards most languages
are<00:00:59.340><c> still</c><00:00:59.399><c> hung</c><00:00:59.670><c> up</c><00:00:59.789><c> on</c><00:00:59.910><c> this</c><00:01:00.030><c> boy</c><00:01:00.660><c> lambdas</c><00:01:01.020><c> in</c>

00:01:01.130 --> 00:01:01.140 align:start position:0%
are still hung up on this boy lambdas in
 

00:01:01.140 --> 00:01:03.770 align:start position:0%
are still hung up on this boy lambdas in
Python<00:01:01.500><c> are</c><00:01:01.530><c> ugly</c><00:01:01.829><c> number</c><00:01:02.640><c> seven</c><00:01:03.030><c> symbols</c><00:01:03.690><c> are</c>

00:01:03.770 --> 00:01:03.780 align:start position:0%
Python are ugly number seven symbols are
 

00:01:03.780 --> 00:01:05.719 align:start position:0%
Python are ugly number seven symbols are
useful<00:01:04.140><c> ask</c><00:01:04.470><c> group</c><00:01:04.769><c> Easter's</c><00:01:05.100><c> Python</c><00:01:05.610><c> has</c>

00:01:05.719 --> 00:01:05.729 align:start position:0%
useful ask group Easter's Python has
 

00:01:05.729 --> 00:01:07.550 align:start position:0%
useful ask group Easter's Python has
them<00:01:05.850><c> too</c><00:01:06.030><c> but</c><00:01:06.180><c> only</c><00:01:06.299><c> theoretically</c><00:01:07.110><c> eight</c>

00:01:07.550 --> 00:01:07.560 align:start position:0%
them too but only theoretically eight
 

00:01:07.560 --> 00:01:09.410 align:start position:0%
them too but only theoretically eight
and<00:01:07.890><c> nine</c><00:01:08.070><c> these</c><00:01:08.490><c> are</c><00:01:08.670><c> the</c><00:01:08.760><c> two</c><00:01:08.909><c> points</c><00:01:09.180><c> I</c><00:01:09.270><c> want</c>

00:01:09.410 --> 00:01:09.420 align:start position:0%
and nine these are the two points I want
 

00:01:09.420 --> 00:01:10.820 align:start position:0%
and nine these are the two points I want
to<00:01:09.479><c> talk</c><00:01:09.600><c> about</c><00:01:09.750><c> the</c><00:01:10.080><c> world</c><00:01:10.229><c> hasn't</c><00:01:10.470><c> caught</c><00:01:10.799><c> up</c>

00:01:10.820 --> 00:01:10.830 align:start position:0%
to talk about the world hasn't caught up
 

00:01:10.830 --> 00:01:12.230 align:start position:0%
to talk about the world hasn't caught up
to<00:01:11.100><c> these</c><00:01:11.220><c> yet</c><00:01:11.400><c> this</c><00:01:11.760><c> talk</c><00:01:11.909><c> about</c><00:01:12.030><c> number</c>

00:01:12.230 --> 00:01:12.240 align:start position:0%
to these yet this talk about number
 

00:01:12.240 --> 00:01:14.539 align:start position:0%
to these yet this talk about number
eight<00:01:12.299><c> a</c><00:01:12.570><c> notation</c><00:01:13.229><c> for</c><00:01:13.260><c> code</c><00:01:13.650><c> using</c><00:01:14.010><c> trees</c><00:01:14.340><c> of</c>

00:01:14.539 --> 00:01:14.549 align:start position:0%
eight a notation for code using trees of
 

00:01:14.549 --> 00:01:16.609 align:start position:0%
eight a notation for code using trees of
symbols<00:01:15.030><c> let's</c><00:01:15.720><c> press</c><00:01:15.900><c> this</c><00:01:16.020><c> reputation</c><00:01:16.290><c> of</c>

00:01:16.609 --> 00:01:16.619 align:start position:0%
symbols let's press this reputation of
 

00:01:16.619 --> 00:01:18.380 align:start position:0%
symbols let's press this reputation of
being<00:01:16.680><c> old</c><00:01:17.040><c> and</c><00:01:17.250><c> full</c><00:01:17.400><c> of</c><00:01:17.430><c> parens</c><00:01:17.759><c> as</c><00:01:18.119><c> if</c><00:01:18.270><c> this</c>

00:01:18.380 --> 00:01:18.390 align:start position:0%
being old and full of parens as if this
 

00:01:18.390 --> 00:01:19.580 align:start position:0%
being old and full of parens as if this
were<00:01:18.540><c> a</c><00:01:18.600><c> feature</c><00:01:18.840><c> that</c><00:01:19.080><c> should</c><00:01:19.229><c> be</c><00:01:19.290><c> relegated</c>

00:01:19.580 --> 00:01:19.590 align:start position:0%
were a feature that should be relegated
 

00:01:19.590 --> 00:01:21.320 align:start position:0%
were a feature that should be relegated
to<00:01:19.920><c> the</c><00:01:20.009><c> history</c><00:01:20.310><c> books</c><00:01:20.490><c> like</c><00:01:20.759><c> go</c><00:01:20.970><c> to</c><00:01:21.030><c> s</c><00:01:21.210><c> on</c>

00:01:21.320 --> 00:01:21.330 align:start position:0%
to the history books like go to s on
 

00:01:21.330 --> 00:01:23.179 align:start position:0%
to the history books like go to s on
uppercase<00:01:21.810><c> first</c><00:01:22.259><c> of</c><00:01:22.380><c> all</c><00:01:22.470><c> it's</c><00:01:22.680><c> so</c><00:01:22.770><c> easy</c><00:01:23.070><c> to</c>

00:01:23.179 --> 00:01:23.189 align:start position:0%
uppercase first of all it's so easy to
 

00:01:23.189 --> 00:01:25.399 align:start position:0%
uppercase first of all it's so easy to
read<00:01:23.400><c> Lisp</c><00:01:23.640><c> if</c><00:01:23.939><c> you</c><00:01:24.450><c> can</c><00:01:24.600><c> read</c><00:01:24.750><c> HTML</c><00:01:25.229><c> you</c><00:01:25.380><c> can</c>

00:01:25.399 --> 00:01:25.409 align:start position:0%
read Lisp if you can read HTML you can
 

00:01:25.409 --> 00:01:27.770 align:start position:0%
read Lisp if you can read HTML you can
read<00:01:25.710><c> Lisp</c><00:01:25.950><c> if</c><00:01:26.400><c> you</c><00:01:26.640><c> can</c><00:01:26.759><c> read</c><00:01:26.970><c> HTML</c><00:01:27.479><c> you</c><00:01:27.630><c> can</c>

00:01:27.770 --> 00:01:27.780 align:start position:0%
read Lisp if you can read HTML you can
 

00:01:27.780 --> 00:01:30.260 align:start position:0%
read Lisp if you can read HTML you can
read<00:01:27.930><c> Lisp</c><00:01:28.200><c> if</c><00:01:28.439><c> you</c><00:01:28.560><c> can</c><00:01:28.710><c> read</c><00:01:28.920><c> HTML</c><00:01:29.490><c> you</c><00:01:30.060><c> can</c>

00:01:30.260 --> 00:01:30.270 align:start position:0%
read Lisp if you can read HTML you can
 

00:01:30.270 --> 00:01:32.960 align:start position:0%
read Lisp if you can read HTML you can
read<00:01:30.450><c> less</c><00:01:30.689><c> HTML</c><00:01:31.409><c> DOM</c><00:01:31.710><c> is</c><00:01:31.920><c> a</c><00:01:31.950><c> tree</c><00:01:32.369><c> most</c><00:01:32.820><c> other</c>

00:01:32.960 --> 00:01:32.970 align:start position:0%
read less HTML DOM is a tree most other
 

00:01:32.970 --> 00:01:34.550 align:start position:0%
read less HTML DOM is a tree most other
languages<00:01:33.329><c> have</c><00:01:33.450><c> this</c><00:01:33.600><c> tree</c><00:01:34.020><c> of</c><00:01:34.140><c> symbols</c>

00:01:34.550 --> 00:01:34.560 align:start position:0%
languages have this tree of symbols
 

00:01:34.560 --> 00:01:36.109 align:start position:0%
languages have this tree of symbols
behind<00:01:34.950><c> the</c><00:01:35.070><c> scenes</c><00:01:35.310><c> that</c><00:01:35.490><c> the</c><00:01:35.579><c> compiler</c><00:01:35.880><c> sees</c>

00:01:36.109 --> 00:01:36.119 align:start position:0%
behind the scenes that the compiler sees
 

00:01:36.119 --> 00:01:37.760 align:start position:0%
behind the scenes that the compiler sees
but<00:01:36.450><c> not</c><00:01:36.600><c> the</c><00:01:36.720><c> user</c><00:01:36.930><c> the</c><00:01:37.170><c> source</c><00:01:37.380><c> code</c><00:01:37.560><c> goes</c>

00:01:37.760 --> 00:01:37.770 align:start position:0%
but not the user the source code goes
 

00:01:37.770 --> 00:01:39.710 align:start position:0%
but not the user the source code goes
through<00:01:37.979><c> a</c><00:01:38.009><c> passing</c><00:01:38.579><c> step</c><00:01:38.850><c> to</c><00:01:39.030><c> get</c><00:01:39.180><c> to</c><00:01:39.329><c> it</c><00:01:39.450><c> Lisp</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
through a passing step to get to it Lisp
 

00:01:39.720 --> 00:01:41.600 align:start position:0%
through a passing step to get to it Lisp
is<00:01:39.869><c> different</c><00:01:40.229><c> when</c><00:01:40.710><c> you</c><00:01:40.799><c> write</c><00:01:40.950><c> in</c><00:01:41.130><c> Lisp</c><00:01:41.369><c> you</c>

00:01:41.600 --> 00:01:41.610 align:start position:0%
is different when you write in Lisp you
 

00:01:41.610 --> 00:01:43.310 align:start position:0%
is different when you write in Lisp you
directly<00:01:42.060><c> express</c><00:01:42.509><c> the</c><00:01:42.689><c> abstract</c><00:01:43.140><c> syntax</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
directly express the abstract syntax
 

00:01:43.320 --> 00:01:44.899 align:start position:0%
directly express the abstract syntax
tree<00:01:43.710><c> of</c><00:01:43.799><c> your</c><00:01:43.950><c> code</c><00:01:44.159><c> you're</c><00:01:44.430><c> directly</c>

00:01:44.899 --> 00:01:44.909 align:start position:0%
tree of your code you're directly
 

00:01:44.909 --> 00:01:46.310 align:start position:0%
tree of your code you're directly
writing<00:01:45.119><c> the</c><00:01:45.360><c> same</c><00:01:45.570><c> data</c><00:01:45.840><c> structures</c><00:01:46.290><c> that</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
writing the same data structures that
 

00:01:46.320 --> 00:01:48.380 align:start position:0%
writing the same data structures that
the<00:01:46.590><c> compiler</c><00:01:46.979><c> is</c><00:01:47.189><c> consuming</c><00:01:47.759><c> this</c><00:01:48.000><c> is</c><00:01:48.149><c> a</c><00:01:48.180><c> very</c>

00:01:48.380 --> 00:01:48.390 align:start position:0%
the compiler is consuming this is a very
 

00:01:48.390 --> 00:01:50.090 align:start position:0%
the compiler is consuming this is a very
powerful<00:01:48.540><c> technique</c><00:01:49.049><c> in</c><00:01:49.229><c> court</c><00:01:49.530><c> to</c><00:01:49.770><c> lisps</c>

00:01:50.090 --> 00:01:50.100 align:start position:0%
powerful technique in court to lisps
 

00:01:50.100 --> 00:01:51.530 align:start position:0%
powerful technique in court to lisps
macro<00:01:50.460><c> power</c><00:01:50.640><c> which</c><00:01:50.939><c> we'll</c><00:01:51.060><c> get</c><00:01:51.149><c> onto</c><00:01:51.299><c> in</c><00:01:51.479><c> a</c>

00:01:51.530 --> 00:01:51.540 align:start position:0%
macro power which we'll get onto in a
 

00:01:51.540 --> 00:01:53.420 align:start position:0%
macro power which we'll get onto in a
moment<00:01:52.020><c> let's</c><00:01:52.259><c> press</c><00:01:52.409><c> about</c><00:01:52.770><c> the</c><00:01:52.979><c> same</c><00:01:53.159><c> number</c>

00:01:53.420 --> 00:01:53.430 align:start position:0%
moment let's press about the same number
 

00:01:53.430 --> 00:01:54.950 align:start position:0%
moment let's press about the same number
of<00:01:53.460><c> closing</c><00:01:53.880><c> friends</c><00:01:54.180><c> as</c><00:01:54.270><c> JavaScript</c><00:01:54.810><c> has</c>

00:01:54.950 --> 00:01:54.960 align:start position:0%
of closing friends as JavaScript has
 

00:01:54.960 --> 00:01:58.389 align:start position:0%
of closing friends as JavaScript has
these<00:01:55.799><c> guys</c><00:01:56.100><c> winking</c><00:01:56.939><c> sad</c><00:01:57.240><c> bearded</c><00:01:57.810><c> hipster</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
these guys winking sad bearded hipster
 

00:01:58.399 --> 00:02:01.280 align:start position:0%
these guys winking sad bearded hipster
it's<00:01:59.399><c> not</c><00:01:59.520><c> an</c><00:01:59.610><c> immediate</c><00:01:59.759><c> function</c><00:02:00.329><c> is</c><00:02:00.600><c> it</c><00:02:00.899><c> the</c>

00:02:01.280 --> 00:02:01.290 align:start position:0%
it's not an immediate function is it the
 

00:02:01.290 --> 00:02:03.289 align:start position:0%
it's not an immediate function is it the
function<00:02:01.710><c> block</c><00:02:02.070><c> scoping</c><00:02:02.790><c> trick</c><00:02:03.119><c> what</c><00:02:03.210><c> is</c>

00:02:03.289 --> 00:02:03.299 align:start position:0%
function block scoping trick what is
 

00:02:03.299 --> 00:02:04.580 align:start position:0%
function block scoping trick what is
this<00:02:03.420><c> pattern</c><00:02:03.659><c> called</c><00:02:03.899><c> thumbing</c><00:02:04.229><c> the</c><00:02:04.290><c> cones</c>

00:02:04.580 --> 00:02:04.590 align:start position:0%
this pattern called thumbing the cones
 

00:02:04.590 --> 00:02:05.810 align:start position:0%
this pattern called thumbing the cones
now<00:02:04.740><c> the</c><00:02:04.799><c> expressing</c><00:02:05.250><c> your</c><00:02:05.340><c> code</c><00:02:05.490><c> is</c><00:02:05.610><c> a</c><00:02:05.640><c> tree</c>

00:02:05.810 --> 00:02:05.820 align:start position:0%
now the expressing your code is a tree
 

00:02:05.820 --> 00:02:07.190 align:start position:0%
now the expressing your code is a tree
of<00:02:05.850><c> symbols</c><00:02:06.180><c> you</c><00:02:06.270><c> can</c><00:02:06.299><c> really</c><00:02:06.810><c> start</c><00:02:07.020><c> to</c><00:02:07.079><c> do</c>

00:02:07.190 --> 00:02:07.200 align:start position:0%
of symbols you can really start to do
 

00:02:07.200 --> 00:02:08.960 align:start position:0%
of symbols you can really start to do
powerful<00:02:07.560><c> things</c><00:02:07.740><c> with</c><00:02:07.979><c> macros</c><00:02:08.520><c> you</c><00:02:08.729><c> may</c><00:02:08.879><c> have</c>

00:02:08.960 --> 00:02:08.970 align:start position:0%
powerful things with macros you may have
 

00:02:08.970 --> 00:02:10.699 align:start position:0%
powerful things with macros you may have
come<00:02:09.119><c> across</c><00:02:09.239><c> C</c><00:02:09.479><c> macros</c><00:02:09.869><c> these</c><00:02:10.200><c> things</c><00:02:10.530><c> are</c>

00:02:10.699 --> 00:02:10.709 align:start position:0%
come across C macros these things are
 

00:02:10.709 --> 00:02:12.140 align:start position:0%
come across C macros these things are
just<00:02:10.800><c> simple</c><00:02:11.129><c> text</c><00:02:11.190><c> manipulation</c><00:02:11.730><c> a</c>

00:02:12.140 --> 00:02:12.150 align:start position:0%
just simple text manipulation a
 

00:02:12.150 --> 00:02:14.179 align:start position:0%
just simple text manipulation a
templating<00:02:12.810><c> system</c><00:02:13.140><c> there</c><00:02:13.440><c> are</c><00:02:13.890><c> let's</c><00:02:14.069><c> look</c>

00:02:14.179 --> 00:02:14.189 align:start position:0%
templating system there are let's look
 

00:02:14.189 --> 00:02:16.039 align:start position:0%
templating system there are let's look
at<00:02:14.280><c> a</c><00:02:14.340><c> theoretical</c><00:02:14.939><c> example</c><00:02:15.269><c> of</c><00:02:15.329><c> a</c><00:02:15.390><c> lisp</c><00:02:15.569><c> macro</c>

00:02:16.039 --> 00:02:16.049 align:start position:0%
at a theoretical example of a lisp macro
 

00:02:16.049 --> 00:02:17.569 align:start position:0%
at a theoretical example of a lisp macro
Python<00:02:16.620><c> doesn't</c><00:02:16.769><c> have</c><00:02:16.980><c> a</c><00:02:17.010><c> case</c><00:02:17.189><c> statement</c>

00:02:17.569 --> 00:02:17.579 align:start position:0%
Python doesn't have a case statement
 

00:02:17.579 --> 00:02:19.640 align:start position:0%
Python doesn't have a case statement
while<00:02:17.970><c> closure</c><00:02:18.180><c> does</c><00:02:18.480><c> have</c><00:02:18.689><c> one</c><00:02:18.900><c> if</c><00:02:19.140><c> it</c><00:02:19.349><c> didn't</c>

00:02:19.640 --> 00:02:19.650 align:start position:0%
while closure does have one if it didn't
 

00:02:19.650 --> 00:02:20.990 align:start position:0%
while closure does have one if it didn't
you<00:02:19.920><c> could</c><00:02:20.069><c> write</c><00:02:20.220><c> your</c><00:02:20.250><c> own</c><00:02:20.370><c> case</c><00:02:20.640><c> statement</c>

00:02:20.990 --> 00:02:21.000 align:start position:0%
you could write your own case statement
 

00:02:21.000 --> 00:02:22.520 align:start position:0%
you could write your own case statement
and<00:02:21.090><c> you</c><00:02:21.180><c> could</c><00:02:21.299><c> have</c><00:02:21.450><c> it</c><00:02:21.540><c> today</c><00:02:22.200><c> let's</c><00:02:22.409><c> look</c>

00:02:22.520 --> 00:02:22.530 align:start position:0%
and you could have it today let's look
 

00:02:22.530 --> 00:02:24.020 align:start position:0%
and you could have it today let's look
at<00:02:22.620><c> a</c><00:02:22.680><c> familiar</c><00:02:22.829><c> example</c><00:02:23.069><c> JavaScript</c>

00:02:24.020 --> 00:02:24.030 align:start position:0%
at a familiar example JavaScript
 

00:02:24.030 --> 00:02:25.550 align:start position:0%
at a familiar example JavaScript
developers<00:02:24.420><c> were</c><00:02:24.569><c> literally</c><00:02:24.900><c> dancing</c><00:02:25.379><c> in</c><00:02:25.470><c> the</c>

00:02:25.550 --> 00:02:25.560 align:start position:0%
developers were literally dancing in the
 

00:02:25.560 --> 00:02:27.140 align:start position:0%
developers were literally dancing in the
streets<00:02:25.860><c> when</c><00:02:26.069><c> a</c><00:02:26.099><c> Sinkin</c><00:02:26.549><c> a</c><00:02:26.640><c> way</c><00:02:26.760><c> keywords</c>

00:02:27.140 --> 00:02:27.150 align:start position:0%
streets when a Sinkin a way keywords
 

00:02:27.150 --> 00:02:28.850 align:start position:0%
streets when a Sinkin a way keywords
came<00:02:27.450><c> to</c><00:02:27.569><c> JavaScript</c><00:02:28.019><c> and</c><00:02:28.200><c> with</c><00:02:28.349><c> good</c><00:02:28.470><c> reason</c>

00:02:28.850 --> 00:02:28.860 align:start position:0%
came to JavaScript and with good reason
 

00:02:28.860 --> 00:02:31.280 align:start position:0%
came to JavaScript and with good reason
oh<00:02:28.890><c> but</c><00:02:29.519><c> it</c><00:02:29.610><c> hasn't</c><00:02:29.790><c> yet</c><00:02:29.939><c> come</c><00:02:30.269><c> to</c><00:02:30.390><c> browsers</c><00:02:30.780><c> it</c>

00:02:31.280 --> 00:02:31.290 align:start position:0%
oh but it hasn't yet come to browsers it
 

00:02:31.290 --> 00:02:33.410 align:start position:0%
oh but it hasn't yet come to browsers it
didn't<00:02:31.500><c> get</c><00:02:31.560><c> into</c><00:02:31.799><c> es</c><00:02:32.370><c> 2016</c><00:02:33.150><c> to</c><00:02:33.329><c> my</c>

00:02:33.410 --> 00:02:33.420 align:start position:0%
didn't get into es 2016 to my
 

00:02:33.420 --> 00:02:35.089 align:start position:0%
didn't get into es 2016 to my
understanding<00:02:33.599><c> and</c><00:02:34.049><c> jazz</c><00:02:34.530><c> devs</c><00:02:34.769><c> please</c>

00:02:35.089 --> 00:02:35.099 align:start position:0%
understanding and jazz devs please
 

00:02:35.099 --> 00:02:36.259 align:start position:0%
understanding and jazz devs please
ambushed<00:02:35.430><c> me</c><00:02:35.549><c> in</c><00:02:35.579><c> a</c><00:02:35.670><c> dark</c><00:02:35.790><c> alley</c><00:02:36.000><c> if</c><00:02:36.120><c> i've</c><00:02:36.239><c> ever</c>

00:02:36.259 --> 00:02:36.269 align:start position:0%
ambushed me in a dark alley if i've ever
 

00:02:36.269 --> 00:02:38.030 align:start position:0%
ambushed me in a dark alley if i've ever
simplified<00:02:36.840><c> this</c><00:02:36.989><c> a</c><00:02:37.140><c> wait</c><00:02:37.379><c> is</c><00:02:37.500><c> a</c><00:02:37.530><c> lovely</c><00:02:37.920><c> bit</c>

00:02:38.030 --> 00:02:38.040 align:start position:0%
simplified this a wait is a lovely bit
 

00:02:38.040 --> 00:02:39.679 align:start position:0%
simplified this a wait is a lovely bit
of<00:02:38.069><c> syntactic</c><00:02:38.609><c> sugar</c><00:02:38.640><c> over</c><00:02:39.180><c> javascript</c>

00:02:39.679 --> 00:02:39.689 align:start position:0%
of syntactic sugar over javascript
 

00:02:39.689 --> 00:02:41.420 align:start position:0%
of syntactic sugar over javascript
powerful<00:02:40.140><c> native</c><00:02:40.349><c> callbacks</c><00:02:40.920><c> but</c><00:02:41.220><c> if</c><00:02:41.310><c> it's</c>

00:02:41.420 --> 00:02:41.430 align:start position:0%
powerful native callbacks but if it's
 

00:02:41.430 --> 00:02:42.949 align:start position:0%
powerful native callbacks but if it's
not<00:02:41.489><c> in</c><00:02:41.670><c> jeaious</c><00:02:42.120><c> implementations</c><00:02:42.659><c> yet</c><00:02:42.840><c> how</c>

00:02:42.949 --> 00:02:42.959 align:start position:0%
not in jeaious implementations yet how
 

00:02:42.959 --> 00:02:44.360 align:start position:0%
not in jeaious implementations yet how
do<00:02:43.019><c> we</c><00:02:43.170><c> use</c><00:02:43.290><c> this</c><00:02:43.439><c> feature</c><00:02:43.829><c> the</c><00:02:44.069><c> answer</c><00:02:44.280><c> is</c>

00:02:44.360 --> 00:02:44.370 align:start position:0%
do we use this feature the answer is
 

00:02:44.370 --> 00:02:47.000 align:start position:0%
do we use this feature the answer is
Babel<00:02:44.730><c> what</c><00:02:45.420><c> is</c><00:02:45.540><c> babel</c><00:02:45.870><c> babel</c><00:02:46.590><c> compiles</c>

00:02:47.000 --> 00:02:47.010 align:start position:0%
Babel what is babel babel compiles
 

00:02:47.010 --> 00:02:49.369 align:start position:0%
Babel what is babel babel compiles
JavaScript<00:02:47.040><c> to</c><00:02:47.970><c> javascript</c><00:02:48.870><c> and</c><00:02:49.109><c> this</c><00:02:49.260><c> is</c>

00:02:49.369 --> 00:02:49.379 align:start position:0%
JavaScript to javascript and this is
 

00:02:49.379 --> 00:02:50.660 align:start position:0%
JavaScript to javascript and this is
exactly<00:02:49.500><c> what</c><00:02:49.769><c> lists</c><00:02:50.010><c> macros</c><00:02:50.370><c> do</c><00:02:50.519><c> they</c>

00:02:50.660 --> 00:02:50.670 align:start position:0%
exactly what lists macros do they
 

00:02:50.670 --> 00:02:52.910 align:start position:0%
exactly what lists macros do they
compile<00:02:51.000><c> Lisp</c><00:02:51.269><c> to</c><00:02:51.540><c> more</c><00:02:51.959><c> Lisp</c><00:02:52.170><c> Babel</c><00:02:52.739><c> is</c><00:02:52.889><c> a</c>

00:02:52.910 --> 00:02:52.920 align:start position:0%
compile Lisp to more Lisp Babel is a
 

00:02:52.920 --> 00:02:54.740 align:start position:0%
compile Lisp to more Lisp Babel is a
very<00:02:53.190><c> elaborate</c><00:02:53.609><c> complicated</c><00:02:53.879><c> and</c><00:02:54.450><c> macro</c>

00:02:54.740 --> 00:02:54.750 align:start position:0%
very elaborate complicated and macro
 

00:02:54.750 --> 00:02:56.780 align:start position:0%
very elaborate complicated and macro
system<00:02:55.139><c> list</c><00:02:55.709><c> macros</c><00:02:56.069><c> lets</c><00:02:56.310><c> you</c><00:02:56.400><c> write</c><00:02:56.579><c> Lisp</c>

00:02:56.780 --> 00:02:56.790 align:start position:0%
system list macros lets you write Lisp
 

00:02:56.790 --> 00:02:59.030 align:start position:0%
system list macros lets you write Lisp
that<00:02:56.940><c> runs</c><00:02:57.209><c> at</c><00:02:57.359><c> compile</c><00:02:57.750><c> time</c><00:02:57.780><c> and</c><00:02:58.260><c> alters</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
that runs at compile time and alters
 

00:02:59.040 --> 00:03:00.949 align:start position:0%
that runs at compile time and alters
itself<00:02:59.700><c> penultimate</c><00:03:00.299><c> slide</c><00:03:00.480><c> stick</c><00:03:00.780><c> with</c><00:03:00.900><c> the</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
itself penultimate slide stick with the
 

00:03:00.959 --> 00:03:02.629 align:start position:0%
itself penultimate slide stick with the
guys<00:03:01.109><c> we've</c><00:03:01.319><c> arrived</c><00:03:01.650><c> at</c><00:03:01.980><c> the</c><00:03:02.099><c> secret</c><00:03:02.430><c> source</c>

00:03:02.629 --> 00:03:02.639 align:start position:0%
guys we've arrived at the secret source
 

00:03:02.639 --> 00:03:04.819 align:start position:0%
guys we've arrived at the secret source
let's<00:03:03.090><c> talk</c><00:03:03.269><c> about</c><00:03:03.359><c> the</c><00:03:03.540><c> RepRap</c><00:03:03.930><c> to</c><00:03:04.620><c> build</c><00:03:04.769><c> a</c>

00:03:04.819 --> 00:03:04.829 align:start position:0%
let's talk about the RepRap to build a
 

00:03:04.829 --> 00:03:06.649 align:start position:0%
let's talk about the RepRap to build a
RepRap<00:03:05.159><c> you</c><00:03:05.310><c> first</c><00:03:05.519><c> build</c><00:03:05.760><c> your</c><00:03:05.879><c> own</c><00:03:06.000><c> bad</c><00:03:06.239><c> 3d</c>

00:03:06.649 --> 00:03:06.659 align:start position:0%
RepRap you first build your own bad 3d
 

00:03:06.659 --> 00:03:08.240 align:start position:0%
RepRap you first build your own bad 3d
printer<00:03:07.109><c> with</c><00:03:07.260><c> homemade</c><00:03:07.530><c> parts</c><00:03:07.889><c> and</c><00:03:08.069><c> plans</c>

00:03:08.240 --> 00:03:08.250 align:start position:0%
printer with homemade parts and plans
 

00:03:08.250 --> 00:03:09.979 align:start position:0%
printer with homemade parts and plans
from<00:03:08.430><c> the</c><00:03:08.519><c> internet</c><00:03:08.819><c> then</c><00:03:09.030><c> you</c><00:03:09.060><c> use</c><00:03:09.299><c> that</c><00:03:09.329><c> 3d</c>

00:03:09.979 --> 00:03:09.989 align:start position:0%
from the internet then you use that 3d
 

00:03:09.989 --> 00:03:11.990 align:start position:0%
from the internet then you use that 3d
printer<00:03:10.379><c> to</c><00:03:10.650><c> print</c><00:03:10.920><c> a</c><00:03:11.040><c> better</c><00:03:11.310><c> printer</c><00:03:11.819><c> this</c>

00:03:11.990 --> 00:03:12.000 align:start position:0%
printer to print a better printer this
 

00:03:12.000 --> 00:03:13.160 align:start position:0%
printer to print a better printer this
is<00:03:12.060><c> like</c><00:03:12.299><c> using</c><00:03:12.450><c> lisp</c><00:03:12.750><c> with</c><00:03:13.019><c> other</c>

00:03:13.160 --> 00:03:13.170 align:start position:0%
is like using lisp with other
 

00:03:13.170 --> 00:03:14.479 align:start position:0%
is like using lisp with other
programming<00:03:13.500><c> languages</c><00:03:13.889><c> you</c><00:03:14.040><c> adapt</c><00:03:14.340><c> the</c>

00:03:14.479 --> 00:03:14.489 align:start position:0%
programming languages you adapt the
 

00:03:14.489 --> 00:03:16.460 align:start position:0%
programming languages you adapt the
problem<00:03:14.849><c> to</c><00:03:15.000><c> the</c><00:03:15.120><c> language</c><00:03:15.450><c> with</c><00:03:16.079><c> Lisp</c><00:03:16.319><c> you</c>

00:03:16.460 --> 00:03:16.470 align:start position:0%
problem to the language with Lisp you
 

00:03:16.470 --> 00:03:18.770 align:start position:0%
problem to the language with Lisp you
adapt<00:03:16.859><c> the</c><00:03:17.010><c> language</c><00:03:17.400><c> to</c><00:03:17.730><c> the</c><00:03:17.849><c> problem</c><00:03:18.209><c> this</c>

00:03:18.770 --> 00:03:18.780 align:start position:0%
adapt the language to the problem this
 

00:03:18.780 --> 00:03:20.210 align:start position:0%
adapt the language to the problem this
can<00:03:19.019><c> sound</c><00:03:19.169><c> terrifying</c><00:03:19.709><c> with</c><00:03:19.829><c> traditional</c>

00:03:20.210 --> 00:03:20.220 align:start position:0%
can sound terrifying with traditional
 

00:03:20.220 --> 00:03:21.439 align:start position:0%
can sound terrifying with traditional
languages<00:03:20.549><c> even</c><00:03:20.819><c> with</c><00:03:21.060><c> meta</c><00:03:21.299><c> programming</c>

00:03:21.439 --> 00:03:21.449 align:start position:0%
languages even with meta programming
 

00:03:21.449 --> 00:03:23.059 align:start position:0%
languages even with meta programming
happy<00:03:21.930><c> languages</c><00:03:22.349><c> like</c><00:03:22.500><c> Python</c><00:03:22.859><c> where</c><00:03:23.010><c> you</c>

00:03:23.059 --> 00:03:23.069 align:start position:0%
happy languages like Python where you
 

00:03:23.069 --> 00:03:25.189 align:start position:0%
happy languages like Python where you
can<00:03:23.400><c> overwrite</c><00:03:23.970><c> the</c><00:03:24.120><c> addition</c><00:03:24.480><c> operator</c><00:03:24.810><c> it's</c>

00:03:25.189 --> 00:03:25.199 align:start position:0%
can overwrite the addition operator it's
 

00:03:25.199 --> 00:03:27.020 align:start position:0%
can overwrite the addition operator it's
considered<00:03:25.680><c> bad</c><00:03:25.829><c> form</c><00:03:25.859><c> and</c><00:03:26.370><c> confusing</c><00:03:26.910><c> for</c>

00:03:27.020 --> 00:03:27.030 align:start position:0%
considered bad form and confusing for
 

00:03:27.030 --> 00:03:28.640 align:start position:0%
considered bad form and confusing for
the<00:03:27.090><c> next</c><00:03:27.269><c> person</c><00:03:27.660><c> for</c><00:03:27.930><c> the</c><00:03:27.989><c> Lisp</c><00:03:28.169><c> developer</c>

00:03:28.640 --> 00:03:28.650 align:start position:0%
the next person for the Lisp developer
 

00:03:28.650 --> 00:03:30.830 align:start position:0%
the next person for the Lisp developer
to<00:03:28.949><c> program</c><00:03:29.340><c> is</c><00:03:29.489><c> too</c><00:03:29.699><c> meta</c><00:03:29.909><c> program</c><00:03:30.329><c> there's</c>

00:03:30.830 --> 00:03:30.840 align:start position:0%
to program is too meta program there's
 

00:03:30.840 --> 00:03:32.569 align:start position:0%
to program is too meta program there's
no<00:03:31.019><c> distinction</c><00:03:31.409><c> to</c><00:03:31.739><c> program</c><00:03:32.010><c> and</c><00:03:32.129><c> Lisp</c><00:03:32.280><c> is</c><00:03:32.459><c> to</c>

00:03:32.569 --> 00:03:32.579 align:start position:0%
no distinction to program and Lisp is to
 

00:03:32.579 --> 00:03:34.069 align:start position:0%
no distinction to program and Lisp is to
program<00:03:32.909><c> the</c><00:03:33.030><c> compiler</c><00:03:33.389><c> with</c><00:03:33.599><c> each</c><00:03:33.750><c> new</c>

00:03:34.069 --> 00:03:34.079 align:start position:0%
program the compiler with each new
 

00:03:34.079 --> 00:03:36.050 align:start position:0%
program the compiler with each new
program<00:03:34.620><c> having</c><00:03:35.159><c> the</c><00:03:35.250><c> whole</c><00:03:35.430><c> language</c><00:03:35.609><c> always</c>

00:03:36.050 --> 00:03:36.060 align:start position:0%
program having the whole language always
 

00:03:36.060 --> 00:03:37.699 align:start position:0%
program having the whole language always
available<00:03:36.239><c> also</c><00:03:36.750><c> means</c><00:03:37.199><c> that</c><00:03:37.319><c> you're</c><00:03:37.500><c> rarely</c>

00:03:37.699 --> 00:03:37.709 align:start position:0%
available also means that you're rarely
 

00:03:37.709 --> 00:03:39.289 align:start position:0%
available also means that you're rarely
discouraged<00:03:38.280><c> from</c><00:03:38.400><c> peeking</c><00:03:38.819><c> inside</c><00:03:39.120><c> the</c>

00:03:39.289 --> 00:03:39.299 align:start position:0%
discouraged from peeking inside the
 

00:03:39.299 --> 00:03:41.569 align:start position:0%
discouraged from peeking inside the
black<00:03:39.480><c> box</c><00:03:39.510><c> these</c><00:03:40.260><c> JavaScript</c><00:03:40.620><c> and</c><00:03:41.280><c> Python</c>

00:03:41.569 --> 00:03:41.579 align:start position:0%
black box these JavaScript and Python
 

00:03:41.579 --> 00:03:43.640 align:start position:0%
black box these JavaScript and Python
functions<00:03:42.269><c> are</c><00:03:42.449><c> actually</c><00:03:42.540><c> written</c><00:03:42.870><c> in</c><00:03:43.049><c> C</c><00:03:43.319><c> and</c>

00:03:43.640 --> 00:03:43.650 align:start position:0%
functions are actually written in C and
 

00:03:43.650 --> 00:03:45.140 align:start position:0%
functions are actually written in C and
you'll<00:03:43.919><c> be</c><00:03:44.010><c> writing</c><00:03:44.190><c> your</c><00:03:44.370><c> own</c><00:03:44.459><c> C</c><00:03:44.669><c> code</c><00:03:44.909><c> if</c><00:03:45.060><c> you</c>

00:03:45.140 --> 00:03:45.150 align:start position:0%
you'll be writing your own C code if you
 

00:03:45.150 --> 00:03:46.789 align:start position:0%
you'll be writing your own C code if you
want<00:03:45.299><c> to</c><00:03:45.359><c> tweak</c><00:03:45.569><c> them</c><00:03:45.750><c> with</c><00:03:46.229><c> Lisp</c><00:03:46.440><c> it's</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
want to tweak them with Lisp it's
 

00:03:46.799 --> 00:03:49.879 align:start position:0%
want to tweak them with Lisp it's
Turtles<00:03:47.250><c> all</c><00:03:47.459><c> the</c><00:03:47.519><c> way</c><00:03:47.790><c> down</c>

