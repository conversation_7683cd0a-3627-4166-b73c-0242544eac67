WEBVTT
Kind: captions
Language: en

00:00:01.360 --> 00:00:03.149 align:start position:0%
 
hey<00:00:01.560><c> everyone</c><00:00:02.159><c> this</c><00:00:02.240><c> is</c><00:00:02.399><c> Ravi</c><00:00:02.720><c> from</c><00:00:02.879><c> Lama</c>

00:00:03.149 --> 00:00:03.159 align:start position:0%
hey everyone this is <PERSON> from <PERSON>
 

00:00:03.159 --> 00:00:05.110 align:start position:0%
hey everyone this is <PERSON> from <PERSON>
index<00:00:04.040><c> welcome</c><00:00:04.279><c> to</c><00:00:04.480><c> another</c><00:00:04.720><c> video</c><00:00:04.920><c> in</c><00:00:05.000><c> the</c>

00:00:05.110 --> 00:00:05.120 align:start position:0%
index welcome to another video in the
 

00:00:05.120 --> 00:00:07.150 align:start position:0%
index welcome to another video in the
series<00:00:05.400><c> of</c><00:00:05.560><c> videos</c><00:00:06.000><c> on</c><00:00:06.440><c> building</c><00:00:06.759><c> llm</c>

00:00:07.150 --> 00:00:07.160 align:start position:0%
series of videos on building llm
 

00:00:07.160 --> 00:00:09.950 align:start position:0%
series of videos on building llm
applications<00:00:07.680><c> with</c><00:00:07.799><c> L</c><00:00:08.160><c> index</c><00:00:08.400><c> and</c><00:00:08.639><c> cla3</c><00:00:09.639><c> so</c><00:00:09.840><c> in</c>

00:00:09.950 --> 00:00:09.960 align:start position:0%
applications with L index and cla3 so in
 

00:00:09.960 --> 00:00:11.629 align:start position:0%
applications with L index and cla3 so in
this<00:00:10.120><c> video</c><00:00:10.320><c> we'll</c><00:00:10.519><c> look</c><00:00:10.679><c> into</c><00:00:11.000><c> Data</c><00:00:11.280><c> agents</c>

00:00:11.629 --> 00:00:11.639 align:start position:0%
this video we'll look into Data agents
 

00:00:11.639 --> 00:00:13.870 align:start position:0%
this video we'll look into Data agents
and<00:00:11.799><c> multi-document</c><00:00:12.440><c> agents</c><00:00:13.440><c> where</c><00:00:13.639><c> you</c><00:00:13.719><c> can</c>

00:00:13.870 --> 00:00:13.880 align:start position:0%
and multi-document agents where you can
 

00:00:13.880 --> 00:00:16.070 align:start position:0%
and multi-document agents where you can
build<00:00:14.440><c> rack</c><00:00:14.719><c> pipelines</c><00:00:15.120><c> on</c><00:00:15.280><c> huge</c><00:00:15.519><c> number</c><00:00:15.719><c> of</c>

00:00:16.070 --> 00:00:16.080 align:start position:0%
build rack pipelines on huge number of
 

00:00:16.080 --> 00:00:18.790 align:start position:0%
build rack pipelines on huge number of
documents<00:00:17.080><c> and</c><00:00:17.240><c> as</c><00:00:17.359><c> well</c><00:00:17.560><c> as</c><00:00:17.920><c> uh</c><00:00:18.080><c> use</c><00:00:18.439><c> agents</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
documents and as well as uh use agents
 

00:00:18.800 --> 00:00:20.990 align:start position:0%
documents and as well as uh use agents
for<00:00:19.240><c> uh</c><00:00:19.320><c> calling</c><00:00:19.680><c> out</c><00:00:19.960><c> different</c><00:00:20.240><c> tools</c><00:00:20.640><c> and</c>

00:00:20.990 --> 00:00:21.000 align:start position:0%
for uh calling out different tools and
 

00:00:21.000 --> 00:00:23.109 align:start position:0%
for uh calling out different tools and
uh<00:00:21.119><c> comput</c><00:00:21.480><c> some</c><00:00:21.680><c> tasks</c><00:00:22.519><c> so</c><00:00:22.720><c> let's</c><00:00:22.960><c> get</c>

00:00:23.109 --> 00:00:23.119 align:start position:0%
uh comput some tasks so let's get
 

00:00:23.119 --> 00:00:26.150 align:start position:0%
uh comput some tasks so let's get
started<00:00:23.439><c> with</c><00:00:23.680><c> it</c><00:00:24.680><c> so</c><00:00:24.920><c> we'll</c><00:00:25.199><c> first</c><00:00:25.439><c> look</c><00:00:25.640><c> into</c>

00:00:26.150 --> 00:00:26.160 align:start position:0%
started with it so we'll first look into
 

00:00:26.160 --> 00:00:29.710 align:start position:0%
started with it so we'll first look into
uh<00:00:26.480><c> agents</c><00:00:27.480><c> agents</c><00:00:27.760><c> are</c><00:00:28.240><c> nothing</c><00:00:28.519><c> but</c><00:00:28.920><c> the</c><00:00:29.320><c> uh</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
uh agents agents are nothing but the uh
 

00:00:29.720 --> 00:00:31.950 align:start position:0%
uh agents agents are nothing but the uh
they<00:00:30.039><c> have</c><00:00:30.480><c> access</c><00:00:30.720><c> to</c><00:00:30.920><c> different</c><00:00:31.560><c> number</c><00:00:31.800><c> of</c>

00:00:31.950 --> 00:00:31.960 align:start position:0%
they have access to different number of
 

00:00:31.960 --> 00:00:35.350 align:start position:0%
they have access to different number of
tools<00:00:32.320><c> such</c><00:00:32.520><c> as</c><00:00:33.040><c> uh</c><00:00:34.040><c> calculator</c><00:00:34.520><c> tools</c><00:00:34.840><c> or</c>

00:00:35.350 --> 00:00:35.360 align:start position:0%
tools such as uh calculator tools or
 

00:00:35.360 --> 00:00:37.389 align:start position:0%
tools such as uh calculator tools or
rack<00:00:35.680><c> pipeline</c><00:00:36.079><c> itself</c><00:00:36.440><c> as</c><00:00:36.640><c> in</c><00:00:36.719><c> the</c><00:00:36.840><c> form</c><00:00:37.040><c> of</c>

00:00:37.389 --> 00:00:37.399 align:start position:0%
rack pipeline itself as in the form of
 

00:00:37.399 --> 00:00:40.430 align:start position:0%
rack pipeline itself as in the form of
quy<00:00:37.680><c> engine</c><00:00:38.160><c> tools</c><00:00:39.160><c> or</c><00:00:39.440><c> any</c><00:00:39.840><c> uh</c><00:00:39.960><c> real</c><00:00:40.160><c> world</c>

00:00:40.430 --> 00:00:40.440 align:start position:0%
quy engine tools or any uh real world
 

00:00:40.440 --> 00:00:44.750 align:start position:0%
quy engine tools or any uh real world
API<00:00:40.879><c> external</c><00:00:41.760><c> uh</c><00:00:42.559><c> uh</c><00:00:42.719><c> tools</c><00:00:43.160><c> as</c><00:00:43.440><c> well</c><00:00:44.440><c> so</c><00:00:44.640><c> for</c>

00:00:44.750 --> 00:00:44.760 align:start position:0%
API external uh uh tools as well so for
 

00:00:44.760 --> 00:00:46.910 align:start position:0%
API external uh uh tools as well so for
a<00:00:44.920><c> given</c><00:00:45.200><c> query</c><00:00:45.600><c> agent</c><00:00:45.960><c> decides</c><00:00:46.440><c> which</c><00:00:46.719><c> of</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
a given query agent decides which of
 

00:00:46.920 --> 00:00:51.029 align:start position:0%
a given query agent decides which of
these<00:00:47.120><c> tools</c><00:00:47.440><c> to</c><00:00:47.640><c> use</c><00:00:48.520><c> and</c><00:00:48.800><c> then</c><00:00:49.480><c> uh</c><00:00:50.480><c> use</c><00:00:50.879><c> the</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
these tools to use and then uh use the
 

00:00:51.039 --> 00:00:54.349 align:start position:0%
these tools to use and then uh use the
tool<00:00:51.360><c> and</c><00:00:51.559><c> get</c><00:00:51.719><c> an</c><00:00:51.920><c> answer</c><00:00:52.480><c> accordingly</c><00:00:53.480><c> uh</c><00:00:53.600><c> so</c>

00:00:54.349 --> 00:00:54.359 align:start position:0%
tool and get an answer accordingly uh so
 

00:00:54.359 --> 00:00:56.950 align:start position:0%
tool and get an answer accordingly uh so
uh<00:00:54.559><c> these</c><00:00:54.800><c> agents</c><00:00:55.160><c> need</c><00:00:55.440><c> a</c><00:00:55.600><c> reasoning</c><00:00:56.480><c> uh</c><00:00:56.640><c> loop</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
uh these agents need a reasoning uh loop
 

00:00:56.960 --> 00:00:59.069 align:start position:0%
uh these agents need a reasoning uh loop
to<00:00:57.160><c> decide</c><00:00:57.480><c> the</c><00:00:57.640><c> tool</c><00:00:57.920><c> as</c><00:00:58.039><c> well</c><00:00:58.239><c> as</c><00:00:58.680><c> generate</c>

00:00:59.069 --> 00:00:59.079 align:start position:0%
to decide the tool as well as generate
 

00:00:59.079 --> 00:01:01.910 align:start position:0%
to decide the tool as well as generate
tool<00:00:59.359><c> parameters</c><00:00:59.760><c> in</c><00:00:59.920><c> in</c><00:01:00.320><c> general</c><00:01:01.320><c> uh</c><00:01:01.480><c> so</c><00:01:01.719><c> we</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
tool parameters in in general uh so we
 

00:01:01.920 --> 00:01:05.750 align:start position:0%
tool parameters in in general uh so we
have<00:01:02.519><c> uh</c><00:01:02.840><c> react</c><00:01:03.239><c> agent</c><00:01:03.920><c> framework</c><00:01:04.920><c> um</c><00:01:05.600><c> which</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
have uh react agent framework um which
 

00:01:05.760 --> 00:01:09.429 align:start position:0%
have uh react agent framework um which
works<00:01:06.119><c> on</c><00:01:07.240><c> observation</c><00:01:08.240><c> uh</c><00:01:08.400><c> thought</c><00:01:08.720><c> and</c>

00:01:09.429 --> 00:01:09.439 align:start position:0%
works on observation uh thought and
 

00:01:09.439 --> 00:01:11.670 align:start position:0%
works on observation uh thought and
action<00:01:09.759><c> cycle</c><00:01:10.159><c> and</c><00:01:10.320><c> you</c><00:01:10.439><c> can</c><00:01:10.600><c> use</c><00:01:10.880><c> any</c><00:01:11.080><c> llm</c>

00:01:11.670 --> 00:01:11.680 align:start position:0%
action cycle and you can use any llm
 

00:01:11.680 --> 00:01:15.230 align:start position:0%
action cycle and you can use any llm
like<00:01:12.040><c> CLA</c><00:01:12.320><c> A3</c><00:01:13.080><c> into</c><00:01:13.400><c> react</c><00:01:13.720><c> agent</c><00:01:14.240><c> framework</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
like CLA A3 into react agent framework
 

00:01:15.240 --> 00:01:17.950 align:start position:0%
like CLA A3 into react agent framework
so<00:01:15.720><c> as</c><00:01:15.960><c> said</c><00:01:16.200><c> we</c><00:01:16.360><c> have</c><00:01:16.960><c> qu</c><00:01:17.240><c> engine</c><00:01:17.479><c> tools</c><00:01:17.799><c> as</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
so as said we have qu engine tools as
 

00:01:17.960 --> 00:01:19.789 align:start position:0%
so as said we have qu engine tools as
one<00:01:18.080><c> of</c><00:01:18.240><c> these</c><00:01:18.360><c> tools</c><00:01:18.600><c> or</c><00:01:18.759><c> external</c><00:01:19.119><c> API</c>

00:01:19.789 --> 00:01:19.799 align:start position:0%
one of these tools or external API
 

00:01:19.799 --> 00:01:22.670 align:start position:0%
one of these tools or external API
Services<00:01:20.799><c> wherein</c><00:01:21.119><c> Laha</c><00:01:21.600><c> provides</c><00:01:21.920><c> 30</c><00:01:22.200><c> plus</c>

00:01:22.670 --> 00:01:22.680 align:start position:0%
Services wherein Laha provides 30 plus
 

00:01:22.680 --> 00:01:25.429 align:start position:0%
Services wherein Laha provides 30 plus
uh<00:01:23.479><c> tools</c><00:01:23.880><c> to</c><00:01:24.119><c> external</c><00:01:24.520><c> services</c><00:01:25.240><c> to</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
uh tools to external services to
 

00:01:25.439 --> 00:01:27.670 align:start position:0%
uh tools to external services to
experiment<00:01:25.920><c> with</c><00:01:26.119><c> this</c><00:01:26.320><c> agent</c>

00:01:27.670 --> 00:01:27.680 align:start position:0%
experiment with this agent
 

00:01:27.680 --> 00:01:30.990 align:start position:0%
experiment with this agent
framework<00:01:28.680><c> and</c><00:01:29.200><c> uh</c><00:01:29.439><c> we</c><00:01:29.640><c> look</c><00:01:30.079><c> more</c><00:01:30.280><c> into</c><00:01:30.640><c> using</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
framework and uh we look more into using
 

00:01:31.000 --> 00:01:33.469 align:start position:0%
framework and uh we look more into using
a<00:01:31.520><c> react</c><00:01:31.840><c> agent</c><00:01:32.159><c> with</c><00:01:32.320><c> Cloud</c><00:01:32.600><c> A3</c><00:01:32.880><c> in</c><00:01:33.000><c> the</c>

00:01:33.469 --> 00:01:33.479 align:start position:0%
a react agent with Cloud A3 in the
 

00:01:33.479 --> 00:01:37.030 align:start position:0%
a react agent with Cloud A3 in the
notebook<00:01:34.040><c> walk</c><00:01:34.520><c> through</c><00:01:35.520><c> um</c><00:01:36.399><c> let's</c><00:01:36.799><c> move</c>

00:01:37.030 --> 00:01:37.040 align:start position:0%
notebook walk through um let's move
 

00:01:37.040 --> 00:01:39.710 align:start position:0%
notebook walk through um let's move
forward<00:01:37.360><c> to</c><00:01:37.560><c> another</c><00:01:38.200><c> concept</c>

00:01:39.710 --> 00:01:39.720 align:start position:0%
forward to another concept
 

00:01:39.720 --> 00:01:42.550 align:start position:0%
forward to another concept
um<00:01:40.720><c> so</c><00:01:40.920><c> we</c><00:01:41.040><c> have</c><00:01:41.200><c> seen</c><00:01:41.640><c> uh</c><00:01:41.759><c> the</c><00:01:41.920><c> rack</c><00:01:42.240><c> stack</c>

00:01:42.550 --> 00:01:42.560 align:start position:0%
um so we have seen uh the rack stack
 

00:01:42.560 --> 00:01:44.350 align:start position:0%
um so we have seen uh the rack stack
right<00:01:42.759><c> given</c><00:01:42.960><c> a</c><00:01:43.159><c> document</c><00:01:43.520><c> we</c><00:01:43.680><c> create</c><00:01:43.960><c> chunks</c>

00:01:44.350 --> 00:01:44.360 align:start position:0%
right given a document we create chunks
 

00:01:44.360 --> 00:01:46.830 align:start position:0%
right given a document we create chunks
and<00:01:44.799><c> push</c><00:01:45.040><c> it</c><00:01:45.159><c> to</c><00:01:45.320><c> Vector</c><00:01:45.560><c> DB</c><00:01:46.280><c> and</c><00:01:46.439><c> given</c><00:01:46.680><c> a</c>

00:01:46.830 --> 00:01:46.840 align:start position:0%
and push it to Vector DB and given a
 

00:01:46.840 --> 00:01:49.310 align:start position:0%
and push it to Vector DB and given a
quiry<00:01:47.560><c> we'll</c><00:01:47.840><c> retrieve</c><00:01:48.320><c> relevant</c><00:01:48.719><c> chunks</c><00:01:49.119><c> and</c>

00:01:49.310 --> 00:01:49.320 align:start position:0%
quiry we'll retrieve relevant chunks and
 

00:01:49.320 --> 00:01:50.950 align:start position:0%
quiry we'll retrieve relevant chunks and
pass<00:01:49.479><c> it</c><00:01:49.600><c> to</c><00:01:49.719><c> llm</c><00:01:50.079><c> to</c><00:01:50.200><c> generate</c><00:01:50.479><c> a</c><00:01:50.640><c> final</c>

00:01:50.950 --> 00:01:50.960 align:start position:0%
pass it to llm to generate a final
 

00:01:50.960 --> 00:01:54.030 align:start position:0%
pass it to llm to generate a final
answer<00:01:51.920><c> so</c><00:01:52.119><c> this</c><00:01:52.320><c> process</c><00:01:52.799><c> is</c><00:01:52.960><c> good</c><00:01:53.159><c> for</c><00:01:53.719><c> few</c>

00:01:54.030 --> 00:01:54.040 align:start position:0%
answer so this process is good for few
 

00:01:54.040 --> 00:01:55.990 align:start position:0%
answer so this process is good for few
documents<00:01:54.600><c> but</c><00:01:55.040><c> how</c><00:01:55.200><c> can</c><00:01:55.320><c> you</c><00:01:55.439><c> build</c><00:01:55.799><c> a</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
documents but how can you build a
 

00:01:56.000 --> 00:01:57.590 align:start position:0%
documents but how can you build a
efficient<00:01:56.320><c> rack</c><00:01:56.640><c> system</c><00:01:56.840><c> for</c><00:01:57.000><c> a</c><00:01:57.200><c> lot</c><00:01:57.360><c> of</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
efficient rack system for a lot of
 

00:01:57.600 --> 00:01:59.870 align:start position:0%
efficient rack system for a lot of
documents<00:01:58.600><c> right</c><00:01:59.079><c> so</c><00:01:59.240><c> this</c><00:01:59.360><c> is</c><00:01:59.560><c> where</c>

00:01:59.870 --> 00:01:59.880 align:start position:0%
documents right so this is where
 

00:01:59.880 --> 00:02:02.069 align:start position:0%
documents right so this is where
document<00:02:00.280><c> agents</c><00:02:00.680><c> concept</c><00:02:01.039><c> is</c><00:02:01.159><c> useful</c><00:02:01.840><c> uh</c><00:02:01.960><c> in</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
document agents concept is useful uh in
 

00:02:02.079 --> 00:02:04.109 align:start position:0%
document agents concept is useful uh in
general<00:02:02.439><c> to</c><00:02:02.640><c> build</c><00:02:03.039><c> uh</c><00:02:03.159><c> multi-document</c>

00:02:04.109 --> 00:02:04.119 align:start position:0%
general to build uh multi-document
 

00:02:04.119 --> 00:02:07.429 align:start position:0%
general to build uh multi-document
agents<00:02:05.119><c> so</c><00:02:05.320><c> given</c><00:02:05.520><c> a</c><00:02:05.719><c> documents</c><00:02:06.560><c> we</c><00:02:06.719><c> build</c><00:02:07.280><c> uh</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
agents so given a documents we build uh
 

00:02:07.439 --> 00:02:09.790 align:start position:0%
agents so given a documents we build uh
summary<00:02:08.239><c> index</c><00:02:08.599><c> and</c><00:02:08.800><c> Vector</c><00:02:09.119><c> index</c><00:02:09.560><c> and</c><00:02:09.679><c> then</c>

00:02:09.790 --> 00:02:09.800 align:start position:0%
summary index and Vector index and then
 

00:02:09.800 --> 00:02:12.030 align:start position:0%
summary index and Vector index and then
on<00:02:10.000><c> top</c><00:02:10.200><c> of</c><00:02:10.360><c> that</c><00:02:10.679><c> create</c><00:02:11.000><c> tools</c><00:02:11.440><c> quare</c><00:02:11.720><c> engine</c>

00:02:12.030 --> 00:02:12.040 align:start position:0%
on top of that create tools quare engine
 

00:02:12.040 --> 00:02:14.710 align:start position:0%
on top of that create tools quare engine
tools<00:02:12.599><c> for</c><00:02:12.800><c> summary</c><00:02:13.200><c> index</c><00:02:13.520><c> and</c><00:02:13.760><c> Vector</c><00:02:14.120><c> index</c>

00:02:14.710 --> 00:02:14.720 align:start position:0%
tools for summary index and Vector index
 

00:02:14.720 --> 00:02:18.150 align:start position:0%
tools for summary index and Vector index
separately<00:02:15.720><c> and</c><00:02:15.959><c> then</c><00:02:16.599><c> uh</c><00:02:16.840><c> we'll</c><00:02:17.560><c> wrap</c><00:02:17.959><c> these</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
separately and then uh we'll wrap these
 

00:02:18.160 --> 00:02:21.830 align:start position:0%
separately and then uh we'll wrap these
tools<00:02:18.959><c> around</c><00:02:19.200><c> an</c><00:02:19.360><c> agent</c><00:02:19.959><c> to</c><00:02:20.560><c> select</c><00:02:21.560><c> uh</c><00:02:21.680><c> one</c>

00:02:21.830 --> 00:02:21.840 align:start position:0%
tools around an agent to select uh one
 

00:02:21.840 --> 00:02:25.509 align:start position:0%
tools around an agent to select uh one
of<00:02:22.080><c> these</c><00:02:22.560><c> tools</c><00:02:23.400><c> using</c><00:02:24.000><c> uh</c><00:02:24.160><c> llm</c><00:02:24.599><c> like</c>

00:02:25.509 --> 00:02:25.519 align:start position:0%
of these tools using uh llm like
 

00:02:25.519 --> 00:02:30.309 align:start position:0%
of these tools using uh llm like
cla3<00:02:26.519><c> um</c><00:02:27.120><c> to</c><00:02:27.720><c> answer</c><00:02:28.000><c> a</c><00:02:28.120><c> given</c><00:02:28.760><c> query</c>

00:02:30.309 --> 00:02:30.319 align:start position:0%
cla3 um to answer a given query
 

00:02:30.319 --> 00:02:32.270 align:start position:0%
cla3 um to answer a given query
now<00:02:30.760><c> this</c><00:02:31.080><c> process</c><00:02:31.400><c> is</c><00:02:31.519><c> for</c><00:02:31.760><c> one</c><00:02:31.920><c> single</c>

00:02:32.270 --> 00:02:32.280 align:start position:0%
now this process is for one single
 

00:02:32.280 --> 00:02:35.350 align:start position:0%
now this process is for one single
document<00:02:33.280><c> uh</c><00:02:33.400><c> we</c><00:02:33.519><c> can</c><00:02:33.680><c> build</c><00:02:34.040><c> such</c><00:02:34.760><c> agents</c>

00:02:35.350 --> 00:02:35.360 align:start position:0%
document uh we can build such agents
 

00:02:35.360 --> 00:02:37.270 align:start position:0%
document uh we can build such agents
ideally<00:02:35.800><c> document</c><00:02:36.200><c> agents</c><00:02:36.560><c> for</c><00:02:36.800><c> multiple</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
ideally document agents for multiple
 

00:02:37.280 --> 00:02:40.630 align:start position:0%
ideally document agents for multiple
documents<00:02:38.040><c> and</c><00:02:38.200><c> when</c><00:02:38.319><c> a</c><00:02:38.440><c> query</c><00:02:38.879><c> comes</c><00:02:39.879><c> uh</c><00:02:40.080><c> we</c>

00:02:40.630 --> 00:02:40.640 align:start position:0%
documents and when a query comes uh we
 

00:02:40.640 --> 00:02:42.750 align:start position:0%
documents and when a query comes uh we
can<00:02:41.120><c> retrieve</c><00:02:41.560><c> one</c><00:02:41.680><c> of</c><00:02:41.879><c> these</c>

00:02:42.750 --> 00:02:42.760 align:start position:0%
can retrieve one of these
 

00:02:42.760 --> 00:02:45.229 align:start position:0%
can retrieve one of these
agents<00:02:43.760><c> and</c><00:02:44.239><c> accordingly</c><00:02:44.720><c> the</c><00:02:44.800><c> agents</c><00:02:45.080><c> will</c>

00:02:45.229 --> 00:02:45.239 align:start position:0%
agents and accordingly the agents will
 

00:02:45.239 --> 00:02:46.750 align:start position:0%
agents and accordingly the agents will
select<00:02:45.519><c> one</c><00:02:45.599><c> of</c><00:02:45.720><c> these</c><00:02:45.879><c> tools</c><00:02:46.159><c> to</c><00:02:46.280><c> generate</c><00:02:46.560><c> an</c>

00:02:46.750 --> 00:02:46.760 align:start position:0%
select one of these tools to generate an
 

00:02:46.760 --> 00:02:49.550 align:start position:0%
select one of these tools to generate an
answer<00:02:47.760><c> so</c><00:02:48.040><c> here</c><00:02:48.480><c> as</c><00:02:48.560><c> you</c><00:02:48.680><c> can</c><00:02:48.800><c> see</c><00:02:48.959><c> for</c><00:02:49.200><c> each</c>

00:02:49.550 --> 00:02:49.560 align:start position:0%
answer so here as you can see for each
 

00:02:49.560 --> 00:02:52.750 align:start position:0%
answer so here as you can see for each
document<00:02:49.920><c> there</c><00:02:50.040><c> is</c><00:02:50.159><c> an</c><00:02:50.319><c> agent</c><00:02:50.920><c> and</c><00:02:51.120><c> then</c><00:02:51.760><c> uh</c>

00:02:52.750 --> 00:02:52.760 align:start position:0%
document there is an agent and then uh
 

00:02:52.760 --> 00:02:55.430 align:start position:0%
document there is an agent and then uh
tool<00:02:53.280><c> on</c><00:02:53.480><c> top</c><00:02:53.640><c> of</c><00:02:53.840><c> it</c><00:02:54.599><c> so</c><00:02:54.800><c> for</c><00:02:55.040><c> lot</c><00:02:55.239><c> of</c>

00:02:55.430 --> 00:02:55.440 align:start position:0%
tool on top of it so for lot of
 

00:02:55.440 --> 00:02:58.190 align:start position:0%
tool on top of it so for lot of
documents<00:02:56.159><c> you'll</c><00:02:56.599><c> have</c><00:02:56.879><c> lots</c><00:02:57.200><c> of</c><00:02:57.760><c> different</c>

00:02:58.190 --> 00:02:58.200 align:start position:0%
documents you'll have lots of different
 

00:02:58.200 --> 00:03:00.430 align:start position:0%
documents you'll have lots of different
agents<00:02:59.200><c> and</c><00:02:59.360><c> then</c><00:02:59.519><c> this</c><00:02:59.800><c> retrieval</c><00:03:00.239><c> would</c>

00:03:00.430 --> 00:03:00.440 align:start position:0%
agents and then this retrieval would
 

00:03:00.440 --> 00:03:02.670 align:start position:0%
agents and then this retrieval would
retrieve<00:03:00.800><c> one</c><00:03:00.920><c> of</c><00:03:01.120><c> these</c><00:03:01.680><c> agent</c><00:03:02.239><c> and</c><00:03:02.400><c> then</c><00:03:02.560><c> the</c>

00:03:02.670 --> 00:03:02.680 align:start position:0%
retrieve one of these agent and then the
 

00:03:02.680 --> 00:03:04.589 align:start position:0%
retrieve one of these agent and then the
agent<00:03:02.920><c> will</c><00:03:03.080><c> select</c><00:03:03.400><c> one</c><00:03:03.519><c> of</c><00:03:03.680><c> these</c><00:03:04.120><c> the</c><00:03:04.239><c> tools</c>

00:03:04.589 --> 00:03:04.599 align:start position:0%
agent will select one of these the tools
 

00:03:04.599 --> 00:03:07.390 align:start position:0%
agent will select one of these the tools
that<00:03:04.720><c> are</c><00:03:04.920><c> available</c><00:03:05.680><c> to</c><00:03:05.920><c> it</c><00:03:06.640><c> so</c><00:03:07.040><c> and</c><00:03:07.200><c> then</c>

00:03:07.390 --> 00:03:07.400 align:start position:0%
that are available to it so and then
 

00:03:07.400 --> 00:03:09.589 align:start position:0%
that are available to it so and then
accordingly<00:03:08.040><c> it</c><00:03:08.120><c> will</c><00:03:08.319><c> generate</c><00:03:08.640><c> an</c>

00:03:09.589 --> 00:03:09.599 align:start position:0%
accordingly it will generate an
 

00:03:09.599 --> 00:03:12.869 align:start position:0%
accordingly it will generate an
answer<00:03:10.599><c> right</c><00:03:10.840><c> so</c><00:03:11.440><c> this</c><00:03:11.599><c> way</c><00:03:11.840><c> you</c><00:03:11.959><c> can</c><00:03:12.159><c> build</c><00:03:12.680><c> a</c>

00:03:12.869 --> 00:03:12.879 align:start position:0%
answer right so this way you can build a
 

00:03:12.879 --> 00:03:14.550 align:start position:0%
answer right so this way you can build a
multi-document<00:03:13.360><c> agent</c><00:03:13.879><c> system</c><00:03:14.159><c> to</c><00:03:14.319><c> build</c>

00:03:14.550 --> 00:03:14.560 align:start position:0%
multi-document agent system to build
 

00:03:14.560 --> 00:03:17.589 align:start position:0%
multi-document agent system to build
over<00:03:14.840><c> large</c><00:03:15.200><c> number</c><00:03:15.440><c> of</c><00:03:16.159><c> documents</c><00:03:17.159><c> and</c><00:03:17.480><c> uh</c>

00:03:17.589 --> 00:03:17.599 align:start position:0%
over large number of documents and uh
 

00:03:17.599 --> 00:03:20.430 align:start position:0%
over large number of documents and uh
we'll<00:03:17.879><c> see</c><00:03:18.560><c> uh</c><00:03:18.720><c> how</c><00:03:18.920><c> you</c><00:03:19.040><c> can</c><00:03:19.280><c> use</c><00:03:20.000><c> uh</c>

00:03:20.430 --> 00:03:20.440 align:start position:0%
we'll see uh how you can use uh
 

00:03:20.440 --> 00:03:23.470 align:start position:0%
we'll see uh how you can use uh
multidocument<00:03:20.959><c> agents</c><00:03:21.959><c> and</c><00:03:22.400><c> react</c><00:03:22.799><c> agents</c>

00:03:23.470 --> 00:03:23.480 align:start position:0%
multidocument agents and react agents
 

00:03:23.480 --> 00:03:26.229 align:start position:0%
multidocument agents and react agents
with<00:03:23.640><c> Cloud</c><00:03:23.879><c> A3</c><00:03:24.159><c> models</c><00:03:24.440><c> in</c><00:03:24.560><c> the</c><00:03:24.760><c> notebooks</c><00:03:25.760><c> so</c>

00:03:26.229 --> 00:03:26.239 align:start position:0%
with Cloud A3 models in the notebooks so
 

00:03:26.239 --> 00:03:28.670 align:start position:0%
with Cloud A3 models in the notebooks so
let's<00:03:26.720><c> go</c><00:03:26.920><c> with</c><00:03:27.080><c> the</c><00:03:27.400><c> notebooks</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
let's go with the notebooks
 

00:03:28.680 --> 00:03:31.270 align:start position:0%
let's go with the notebooks
part

00:03:31.270 --> 00:03:31.280 align:start position:0%
part
 

00:03:31.280 --> 00:03:33.550 align:start position:0%
part
so<00:03:31.480><c> we'll</c><00:03:31.760><c> start</c><00:03:32.120><c> with</c><00:03:32.439><c> the</c><00:03:32.599><c> react</c><00:03:32.959><c> agent</c>

00:03:33.550 --> 00:03:33.560 align:start position:0%
so we'll start with the react agent
 

00:03:33.560 --> 00:03:35.630 align:start position:0%
so we'll start with the react agent
framework<00:03:34.560><c> so</c><00:03:34.720><c> in</c><00:03:34.840><c> this</c><00:03:34.959><c> notebook</c><00:03:35.319><c> we'll</c><00:03:35.480><c> look</c>

00:03:35.630 --> 00:03:35.640 align:start position:0%
framework so in this notebook we'll look
 

00:03:35.640 --> 00:03:37.710 align:start position:0%
framework so in this notebook we'll look
into<00:03:35.840><c> creating</c><00:03:36.200><c> react</c><00:03:36.519><c> agent</c><00:03:36.879><c> or</c><00:03:37.360><c> different</c>

00:03:37.710 --> 00:03:37.720 align:start position:0%
into creating react agent or different
 

00:03:37.720 --> 00:03:39.869 align:start position:0%
into creating react agent or different
tools<00:03:38.239><c> simple</c><00:03:38.519><c> tools</c><00:03:38.799><c> like</c><00:03:39.040><c> calculator</c><00:03:39.519><c> tools</c>

00:03:39.869 --> 00:03:39.879 align:start position:0%
tools simple tools like calculator tools
 

00:03:39.879 --> 00:03:42.110 align:start position:0%
tools simple tools like calculator tools
and<00:03:40.080><c> then</c><00:03:40.400><c> a</c><00:03:40.640><c> rag</c><00:03:41.000><c> acquir</c><00:03:41.360><c> engine</c><00:03:41.599><c> tools</c><00:03:41.959><c> as</c>

00:03:42.110 --> 00:03:42.120 align:start position:0%
and then a rag acquir engine tools as
 

00:03:42.120 --> 00:03:46.309 align:start position:0%
and then a rag acquir engine tools as
well<00:03:43.080><c> so</c><00:03:43.599><c> we'll</c><00:03:44.360><c> uh</c><00:03:44.920><c> install</c><00:03:45.439><c> anthropic</c><00:03:45.920><c> LM</c>

00:03:46.309 --> 00:03:46.319 align:start position:0%
well so we'll uh install anthropic LM
 

00:03:46.319 --> 00:03:48.910 align:start position:0%
well so we'll uh install anthropic LM
and<00:03:46.480><c> then</c><00:03:47.000><c> hugging</c><00:03:47.280><c> pH</c><00:03:47.599><c> embeddings</c><00:03:48.599><c> right</c><00:03:48.799><c> you</c>

00:03:48.910 --> 00:03:48.920 align:start position:0%
and then hugging pH embeddings right you
 

00:03:48.920 --> 00:03:53.069 align:start position:0%
and then hugging pH embeddings right you
need<00:03:49.159><c> anthropic</c><00:03:49.720><c> AP</c><00:03:50.120><c> key</c><00:03:50.720><c> uh</c><00:03:50.920><c> to</c><00:03:51.840><c> have</c><00:03:52.840><c> walk</c>

00:03:53.069 --> 00:03:53.079 align:start position:0%
need anthropic AP key uh to have walk
 

00:03:53.079 --> 00:03:54.949 align:start position:0%
need anthropic AP key uh to have walk
through<00:03:53.280><c> this</c><00:03:53.400><c> notebook</c><00:03:54.000><c> so</c><00:03:54.319><c> get</c><00:03:54.480><c> your</c><00:03:54.720><c> ke</c>

00:03:54.949 --> 00:03:54.959 align:start position:0%
through this notebook so get your ke
 

00:03:54.959 --> 00:03:57.309 align:start position:0%
through this notebook so get your ke
before<00:03:55.560><c> running</c><00:03:55.840><c> through</c><00:03:56.040><c> this</c><00:03:56.200><c> notebook</c><00:03:57.159><c> and</c>

00:03:57.309 --> 00:03:57.319 align:start position:0%
before running through this notebook and
 

00:03:57.319 --> 00:04:00.429 align:start position:0%
before running through this notebook and
we'll<00:03:57.519><c> set</c><00:03:57.760><c> the</c><00:03:57.879><c> llm</c><00:03:58.640><c> uh</c><00:03:58.799><c> which</c><00:03:58.879><c> is</c><00:03:59.079><c> CLA</c><00:03:59.920><c> Opus</c>

00:04:00.429 --> 00:04:00.439 align:start position:0%
we'll set the llm uh which is CLA Opus
 

00:04:00.439 --> 00:04:02.670 align:start position:0%
we'll set the llm uh which is CLA Opus
llm<00:04:00.959><c> as</c><00:04:01.079><c> well</c><00:04:01.239><c> as</c><00:04:01.400><c> the</c><00:04:01.560><c> hugging</c><00:04:01.920><c> face</c>

00:04:02.670 --> 00:04:02.680 align:start position:0%
llm as well as the hugging face
 

00:04:02.680 --> 00:04:04.670 align:start position:0%
llm as well as the hugging face
embedding<00:04:03.120><c> model</c><00:04:03.840><c> and</c><00:04:04.000><c> then</c><00:04:04.239><c> create</c><00:04:04.519><c> the</c>

00:04:04.670 --> 00:04:04.680 align:start position:0%
embedding model and then create the
 

00:04:04.680 --> 00:04:06.270 align:start position:0%
embedding model and then create the
chunk<00:04:05.000><c> size</c>

00:04:06.270 --> 00:04:06.280 align:start position:0%
chunk size
 

00:04:06.280 --> 00:04:09.750 align:start position:0%
chunk size
right<00:04:07.280><c> and</c><00:04:07.799><c> uh</c><00:04:08.000><c> we'll</c><00:04:08.239><c> Define</c><00:04:08.519><c> the</c><00:04:08.640><c> tools</c><00:04:09.400><c> like</c>

00:04:09.750 --> 00:04:09.760 align:start position:0%
right and uh we'll Define the tools like
 

00:04:09.760 --> 00:04:12.789 align:start position:0%
right and uh we'll Define the tools like
we<00:04:09.959><c> have</c><00:04:10.439><c> a</c><00:04:10.599><c> multiply</c><00:04:11.079><c> tool</c><00:04:11.439><c> given</c><00:04:12.239><c> a</c><00:04:12.400><c> and</c><00:04:12.560><c> b</c>

00:04:12.789 --> 00:04:12.799 align:start position:0%
we have a multiply tool given a and b
 

00:04:12.799 --> 00:04:15.110 align:start position:0%
we have a multiply tool given a and b
we'll<00:04:13.040><c> just</c><00:04:13.200><c> return</c><00:04:13.599><c> multiplication</c><00:04:14.200><c> of</c><00:04:14.799><c> two</c>

00:04:15.110 --> 00:04:15.120 align:start position:0%
we'll just return multiplication of two
 

00:04:15.120 --> 00:04:17.310 align:start position:0%
we'll just return multiplication of two
these<00:04:15.239><c> two</c><00:04:15.400><c> integers</c><00:04:16.000><c> and</c><00:04:16.199><c> then</c>

00:04:17.310 --> 00:04:17.320 align:start position:0%
these two integers and then
 

00:04:17.320 --> 00:04:21.030 align:start position:0%
these two integers and then
add<00:04:18.320><c> given</c><00:04:18.880><c> two</c><00:04:19.519><c> integers</c><00:04:19.959><c> A</c><00:04:20.079><c> and</c><00:04:20.280><c> B</c><00:04:20.560><c> We'll</c><00:04:20.840><c> add</c>

00:04:21.030 --> 00:04:21.040 align:start position:0%
add given two integers A and B We'll add
 

00:04:21.040 --> 00:04:23.629 align:start position:0%
add given two integers A and B We'll add
these<00:04:21.199><c> two</c><00:04:21.359><c> integers</c><00:04:21.759><c> and</c><00:04:22.199><c> return</c><00:04:22.520><c> them</c><00:04:23.440><c> and</c>

00:04:23.629 --> 00:04:23.639 align:start position:0%
these two integers and return them and
 

00:04:23.639 --> 00:04:26.030 align:start position:0%
these two integers and return them and
then<00:04:23.800><c> we'll</c><00:04:24.120><c> wrap</c><00:04:24.639><c> them</c><00:04:24.919><c> as</c><00:04:25.199><c> two</c><00:04:25.440><c> different</c>

00:04:26.030 --> 00:04:26.040 align:start position:0%
then we'll wrap them as two different
 

00:04:26.040 --> 00:04:28.749 align:start position:0%
then we'll wrap them as two different
tools<00:04:27.040><c> and</c><00:04:27.240><c> then</c><00:04:27.600><c> now</c><00:04:27.840><c> you</c><00:04:28.000><c> create</c><00:04:28.280><c> a</c><00:04:28.440><c> react</c>

00:04:28.749 --> 00:04:28.759 align:start position:0%
tools and then now you create a react
 

00:04:28.759 --> 00:04:30.790 align:start position:0%
tools and then now you create a react
agent<00:04:29.120><c> on</c><00:04:29.320><c> top</c><00:04:29.479><c> of</c><00:04:29.759><c> these</c><00:04:29.880><c> tools</c><00:04:30.600><c> like</c>

00:04:30.790 --> 00:04:30.800 align:start position:0%
agent on top of these tools like
 

00:04:30.800 --> 00:04:33.390 align:start position:0%
agent on top of these tools like
multiply<00:04:31.280><c> tool</c><00:04:31.600><c> and</c><00:04:31.800><c> add</c><00:04:32.000><c> tool</c><00:04:32.880><c> right</c><00:04:33.199><c> and</c>

00:04:33.390 --> 00:04:33.400 align:start position:0%
multiply tool and add tool right and
 

00:04:33.400 --> 00:04:37.310 align:start position:0%
multiply tool and add tool right and
then<00:04:34.360><c> you</c><00:04:34.479><c> can</c><00:04:34.720><c> start</c><00:04:35.479><c> quing</c><00:04:35.919><c> over</c><00:04:36.120><c> this</c><00:04:36.320><c> agent</c>

00:04:37.310 --> 00:04:37.320 align:start position:0%
then you can start quing over this agent
 

00:04:37.320 --> 00:04:39.189 align:start position:0%
then you can start quing over this agent
chat<00:04:37.600><c> with</c><00:04:37.720><c> the</c><00:04:37.840><c> agent</c><00:04:38.199><c> like</c><00:04:38.720><c> here</c><00:04:38.880><c> is</c><00:04:39.000><c> an</c>

00:04:39.189 --> 00:04:39.199 align:start position:0%
chat with the agent like here is an
 

00:04:39.199 --> 00:04:41.469 align:start position:0%
chat with the agent like here is an
example<00:04:39.560><c> what</c><00:04:39.680><c> is</c><00:04:39.800><c> 20</c><00:04:40.080><c> +</c><00:04:40.320><c> 2</c><00:04:40.479><c> into</c><00:04:40.720><c> 4</c><00:04:41.039><c> calculate</c>

00:04:41.469 --> 00:04:41.479 align:start position:0%
example what is 20 + 2 into 4 calculate
 

00:04:41.479 --> 00:04:44.469 align:start position:0%
example what is 20 + 2 into 4 calculate
step<00:04:41.680><c> by</c><00:04:42.160><c> step</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
step by step
 

00:04:44.479 --> 00:04:48.390 align:start position:0%
step by step
so<00:04:45.479><c> so</c><00:04:46.360><c> yeah</c><00:04:46.759><c> so</c><00:04:47.000><c> I</c><00:04:47.160><c> said</c><00:04:47.800><c> uh</c><00:04:47.919><c> this</c><00:04:48.080><c> is</c><00:04:48.199><c> a</c>

00:04:48.390 --> 00:04:48.400 align:start position:0%
so so yeah so I said uh this is a
 

00:04:48.400 --> 00:04:52.270 align:start position:0%
so so yeah so I said uh this is a
thought<00:04:48.800><c> action</c><00:04:49.800><c> observation</c><00:04:51.080><c> framework</c><00:04:52.080><c> uh</c>

00:04:52.270 --> 00:04:52.280 align:start position:0%
thought action observation framework uh
 

00:04:52.280 --> 00:04:54.830 align:start position:0%
thought action observation framework uh
so<00:04:52.759><c> you</c><00:04:52.880><c> can</c><00:04:53.080><c> see</c><00:04:53.880><c> the</c><00:04:54.080><c> different</c><00:04:54.400><c> steps</c><00:04:54.720><c> that</c>

00:04:54.830 --> 00:04:54.840 align:start position:0%
so you can see the different steps that
 

00:04:54.840 --> 00:04:58.950 align:start position:0%
so you can see the different steps that
it<00:04:55.000><c> is</c><00:04:55.199><c> taking</c><00:04:55.600><c> to</c><00:04:56.240><c> get</c><00:04:56.360><c> to</c><00:04:56.520><c> a</c><00:04:56.680><c> final</c><00:04:57.400><c> answer</c><00:04:58.400><c> so</c>

00:04:58.950 --> 00:04:58.960 align:start position:0%
it is taking to get to a final answer so
 

00:04:58.960 --> 00:05:00.990 align:start position:0%
it is taking to get to a final answer so
this<00:04:59.080><c> is</c><00:04:59.199><c> giving</c><00:04:59.680><c> final</c><00:04:59.960><c> answer</c><00:05:00.680><c> you</c><00:05:00.840><c> can</c>

00:05:00.990 --> 00:05:01.000 align:start position:0%
this is giving final answer you can
 

00:05:01.000 --> 00:05:04.390 align:start position:0%
this is giving final answer you can
actually<00:05:01.280><c> expl</c><00:05:01.720><c> with</c><00:05:02.000><c> much</c><00:05:02.160><c> more</c><00:05:02.400><c> complex</c><00:05:03.280><c> uh</c>

00:05:04.390 --> 00:05:04.400 align:start position:0%
actually expl with much more complex uh
 

00:05:04.400 --> 00:05:07.270 align:start position:0%
actually expl with much more complex uh
queries<00:05:05.400><c> for</c><00:05:05.960><c> using</c><00:05:06.320><c> these</c><00:05:06.479><c> tools</c><00:05:06.919><c> and</c><00:05:07.039><c> see</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
queries for using these tools and see
 

00:05:07.280 --> 00:05:10.830 align:start position:0%
queries for using these tools and see
how<00:05:07.840><c> uh</c><00:05:08.240><c> it</c><00:05:08.400><c> generates</c><00:05:08.759><c> an</c><00:05:08.960><c> answer</c>

00:05:10.830 --> 00:05:10.840 align:start position:0%
how uh it generates an answer
 

00:05:10.840 --> 00:05:12.909 align:start position:0%
how uh it generates an answer
accordingly<00:05:11.840><c> and</c><00:05:11.960><c> then</c><00:05:12.120><c> you</c><00:05:12.199><c> can</c><00:05:12.400><c> even</c><00:05:12.680><c> look</c>

00:05:12.909 --> 00:05:12.919 align:start position:0%
accordingly and then you can even look
 

00:05:12.919 --> 00:05:15.310 align:start position:0%
accordingly and then you can even look
into<00:05:13.400><c> what</c><00:05:13.520><c> is</c><00:05:13.639><c> a</c>

00:05:15.310 --> 00:05:15.320 align:start position:0%
into what is a
 

00:05:15.320 --> 00:05:17.189 align:start position:0%
into what is a
prompt<00:05:16.320><c> you</c>

00:05:17.189 --> 00:05:17.199 align:start position:0%
prompt you
 

00:05:17.199 --> 00:05:21.510 align:start position:0%
prompt you
can<00:05:18.639><c> use</c><00:05:19.639><c> or</c><00:05:20.280><c> what</c><00:05:20.400><c> is</c><00:05:20.520><c> a</c><00:05:20.680><c> prompt</c><00:05:21.000><c> underlying</c>

00:05:21.510 --> 00:05:21.520 align:start position:0%
can use or what is a prompt underlying
 

00:05:21.520 --> 00:05:23.909 align:start position:0%
can use or what is a prompt underlying
it<00:05:21.639><c> is</c><00:05:21.840><c> being</c><00:05:22.360><c> used</c>

00:05:23.909 --> 00:05:23.919 align:start position:0%
it is being used
 

00:05:23.919 --> 00:05:27.830 align:start position:0%
it is being used
uh<00:05:24.919><c> by</c><00:05:25.280><c> checking</c><00:05:25.720><c> the</c><00:05:26.199><c> agent.</c><00:05:26.800><c> getet</c><00:05:27.000><c> prompts</c>

00:05:27.830 --> 00:05:27.840 align:start position:0%
uh by checking the agent. getet prompts
 

00:05:27.840 --> 00:05:30.950 align:start position:0%
uh by checking the agent. getet prompts
and<00:05:28.080><c> then</c><00:05:28.600><c> uh</c><00:05:29.520><c> accordingly</c><00:05:29.840><c> see</c><00:05:30.000><c> the</c><00:05:30.160><c> prompts</c>

00:05:30.950 --> 00:05:30.960 align:start position:0%
and then uh accordingly see the prompts
 

00:05:30.960 --> 00:05:33.150 align:start position:0%
and then uh accordingly see the prompts
so<00:05:31.160><c> you</c><00:05:31.280><c> can</c><00:05:31.600><c> see</c><00:05:32.319><c> we</c><00:05:32.440><c> are</c><00:05:32.560><c> using</c><00:05:32.840><c> tool</c>

00:05:33.150 --> 00:05:33.160 align:start position:0%
so you can see we are using tool
 

00:05:33.160 --> 00:05:36.870 align:start position:0%
so you can see we are using tool
description<00:05:33.960><c> and</c><00:05:34.479><c> then</c><00:05:35.479><c> um</c><00:05:36.039><c> other</c>

00:05:36.870 --> 00:05:36.880 align:start position:0%
description and then um other
 

00:05:36.880 --> 00:05:40.150 align:start position:0%
description and then um other
things<00:05:37.880><c> right</c><00:05:38.720><c> and</c><00:05:38.880><c> then</c><00:05:39.120><c> as</c><00:05:39.319><c> said</c><00:05:39.840><c> we</c><00:05:39.960><c> can</c>

00:05:40.150 --> 00:05:40.160 align:start position:0%
things right and then as said we can
 

00:05:40.160 --> 00:05:42.309 align:start position:0%
things right and then as said we can
even<00:05:40.360><c> use</c><00:05:40.600><c> Query</c><00:05:40.919><c> engine</c><00:05:41.160><c> tools</c><00:05:41.919><c> uh</c><00:05:42.120><c> each</c>

00:05:42.309 --> 00:05:42.319 align:start position:0%
even use Query engine tools uh each
 

00:05:42.319 --> 00:05:44.629 align:start position:0%
even use Query engine tools uh each
query<00:05:42.639><c> engine</c><00:05:42.919><c> is</c><00:05:43.440><c> something</c><00:05:43.720><c> sort</c><00:05:44.000><c> of</c><00:05:44.199><c> a</c><00:05:44.360><c> rack</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
query engine is something sort of a rack
 

00:05:44.639 --> 00:05:49.230 align:start position:0%
query engine is something sort of a rack
pipeline<00:05:45.080><c> itself</c><00:05:45.880><c> so</c><00:05:46.199><c> here</c><00:05:46.840><c> um</c><00:05:47.360><c> we</c><00:05:47.880><c> have</c><00:05:48.880><c> Uber</c>

00:05:49.230 --> 00:05:49.240 align:start position:0%
pipeline itself so here um we have Uber
 

00:05:49.240 --> 00:05:53.629 align:start position:0%
pipeline itself so here um we have Uber
and<00:05:49.960><c> uh</c><00:05:50.160><c> lift</c><00:05:50.440><c> and</c><00:05:50.639><c> KC</c><00:05:51.440><c> filings</c><00:05:52.440><c> and</c><00:05:52.720><c> then</c><00:05:53.360><c> we</c>

00:05:53.629 --> 00:05:53.639 align:start position:0%
and uh lift and KC filings and then we
 

00:05:53.639 --> 00:05:57.029 align:start position:0%
and uh lift and KC filings and then we
load<00:05:53.919><c> the</c><00:05:54.160><c> documents</c><00:05:54.720><c> build</c><00:05:55.000><c> an</c><00:05:55.280><c> index</c><00:05:56.280><c> uh</c><00:05:56.720><c> get</c>

00:05:57.029 --> 00:05:57.039 align:start position:0%
load the documents build an index uh get
 

00:05:57.039 --> 00:05:59.590 align:start position:0%
load the documents build an index uh get
a<00:05:57.199><c> query</c><00:05:57.520><c> engine</c><00:05:57.880><c> and</c><00:05:58.080><c> then</c><00:05:58.680><c> uh</c><00:05:58.840><c> create</c><00:05:59.160><c> query</c>

00:05:59.590 --> 00:05:59.600 align:start position:0%
a query engine and then uh create query
 

00:05:59.600 --> 00:06:03.230 align:start position:0%
a query engine and then uh create query
engine<00:06:00.120><c> tools</c><00:06:01.120><c> and</c><00:06:01.319><c> then</c><00:06:01.919><c> build</c><00:06:02.319><c> a</c><00:06:02.600><c> react</c><00:06:02.960><c> reg</c>

00:06:03.230 --> 00:06:03.240 align:start position:0%
engine tools and then build a react reg
 

00:06:03.240 --> 00:06:05.390 align:start position:0%
engine tools and then build a react reg
on<00:06:03.440><c> top</c><00:06:03.639><c> of</c><00:06:03.919><c> these</c><00:06:04.120><c> Square</c><00:06:04.440><c> engine</c>

00:06:05.390 --> 00:06:05.400 align:start position:0%
on top of these Square engine
 

00:06:05.400 --> 00:06:08.629 align:start position:0%
on top of these Square engine
tools<00:06:06.400><c> now</c><00:06:06.599><c> you</c><00:06:06.759><c> can</c><00:06:07.240><c> ask</c><00:06:07.560><c> the</c><00:06:07.720><c> same</c><00:06:08.000><c> question</c>

00:06:08.629 --> 00:06:08.639 align:start position:0%
tools now you can ask the same question
 

00:06:08.639 --> 00:06:12.390 align:start position:0%
tools now you can ask the same question
uh<00:06:09.360><c> like</c><00:06:09.919><c> what</c><00:06:10.080><c> was</c><00:06:10.240><c> the</c><00:06:10.759><c> revenue</c><00:06:11.160><c> growth</c><00:06:11.440><c> in</c>

00:06:12.390 --> 00:06:12.400 align:start position:0%
uh like what was the revenue growth in
 

00:06:12.400 --> 00:06:15.230 align:start position:0%
uh like what was the revenue growth in
2021<00:06:13.400><c> and</c><00:06:13.639><c> get</c><00:06:13.840><c> the</c><00:06:14.039><c> response</c><00:06:14.479><c> accordingly</c>

00:06:15.230 --> 00:06:15.240 align:start position:0%
2021 and get the response accordingly
 

00:06:15.240 --> 00:06:18.550 align:start position:0%
2021 and get the response accordingly
and<00:06:15.479><c> you</c><00:06:15.599><c> can</c><00:06:15.800><c> compare</c><00:06:16.720><c> uh</c><00:06:17.720><c> the</c><00:06:18.160><c> revenue</c>

00:06:18.550 --> 00:06:18.560 align:start position:0%
and you can compare uh the revenue
 

00:06:18.560 --> 00:06:21.110 align:start position:0%
and you can compare uh the revenue
growth<00:06:18.880><c> of</c><00:06:19.039><c> uber</c><00:06:19.360><c> and</c><00:06:19.639><c> lip</c><00:06:19.960><c> in</c>

00:06:21.110 --> 00:06:21.120 align:start position:0%
growth of uber and lip in
 

00:06:21.120 --> 00:06:26.870 align:start position:0%
growth of uber and lip in
2021<00:06:22.120><c> uh</c><00:06:22.919><c> so</c><00:06:23.919><c> then</c><00:06:24.400><c> it</c><00:06:24.599><c> compared</c><00:06:25.440><c> by</c><00:06:25.880><c> giving</c>

00:06:26.870 --> 00:06:26.880 align:start position:0%
2021 uh so then it compared by giving
 

00:06:26.880 --> 00:06:30.510 align:start position:0%
2021 uh so then it compared by giving
different<00:06:27.680><c> uh</c><00:06:27.840><c> metrics</c><00:06:28.560><c> around</c><00:06:28.880><c> it</c><00:06:29.840><c> right</c><00:06:30.120><c> so</c>

00:06:30.510 --> 00:06:30.520 align:start position:0%
different uh metrics around it right so
 

00:06:30.520 --> 00:06:33.390 align:start position:0%
different uh metrics around it right so
in<00:06:30.720><c> this</c><00:06:30.919><c> way</c><00:06:31.319><c> you</c><00:06:31.440><c> can</c><00:06:31.639><c> use</c><00:06:32.000><c> react</c><00:06:32.400><c> agent</c><00:06:32.759><c> over</c>

00:06:33.390 --> 00:06:33.400 align:start position:0%
in this way you can use react agent over
 

00:06:33.400 --> 00:06:38.350 align:start position:0%
in this way you can use react agent over
query<00:06:33.720><c> engine</c><00:06:34.000><c> tools</c><00:06:34.560><c> or</c><00:06:35.360><c> maybe</c><00:06:36.360><c> uh</c><00:06:37.360><c> even</c>

00:06:38.350 --> 00:06:38.360 align:start position:0%
query engine tools or maybe uh even
 

00:06:38.360 --> 00:06:41.309 align:start position:0%
query engine tools or maybe uh even
calculator<00:06:38.840><c> tools</c><00:06:39.160><c> or</c><00:06:39.680><c> in</c><00:06:39.840><c> general</c><00:06:40.400><c> tools</c><00:06:40.840><c> as</c>

00:06:41.309 --> 00:06:41.319 align:start position:0%
calculator tools or in general tools as
 

00:06:41.319 --> 00:06:44.390 align:start position:0%
calculator tools or in general tools as
well<00:06:42.319><c> so</c><00:06:43.199><c> next</c><00:06:43.440><c> we'll</c><00:06:43.599><c> see</c><00:06:43.880><c> look</c><00:06:44.120><c> into</c>

00:06:44.390 --> 00:06:44.400 align:start position:0%
well so next we'll see look into
 

00:06:44.400 --> 00:06:46.350 align:start position:0%
well so next we'll see look into
document<00:06:44.759><c> agents</c><00:06:45.039><c> and</c><00:06:45.160><c> M</c><00:06:45.479><c> document</c>

00:06:46.350 --> 00:06:46.360 align:start position:0%
document agents and M document
 

00:06:46.360 --> 00:06:49.710 align:start position:0%
document agents and M document
agents<00:06:47.360><c> concept</c><00:06:47.840><c> that</c><00:06:48.360><c> we</c><00:06:48.520><c> have</c><00:06:48.840><c> explored</c><00:06:49.520><c> for</c>

00:06:49.710 --> 00:06:49.720 align:start position:0%
agents concept that we have explored for
 

00:06:49.720 --> 00:06:53.070 align:start position:0%
agents concept that we have explored for
building<00:06:50.120><c> rag</c><00:06:50.560><c> over</c><00:06:50.919><c> large</c><00:06:51.240><c> number</c><00:06:51.479><c> of</c>

00:06:53.070 --> 00:06:53.080 align:start position:0%
building rag over large number of
 

00:06:53.080 --> 00:06:57.790 align:start position:0%
building rag over large number of
documents<00:06:54.080><c> so</c><00:06:54.759><c> we'll</c><00:06:55.000><c> use</c><00:06:56.160><c> similar</c><00:06:57.160><c> um</c>

00:06:57.790 --> 00:06:57.800 align:start position:0%
documents so we'll use similar um
 

00:06:57.800 --> 00:06:59.390 align:start position:0%
documents so we'll use similar um
anthropic<00:06:58.199><c> LM</c><00:06:58.560><c> and</c><00:06:58.680><c> then</c><00:06:58.840><c> hugging</c><00:06:59.120><c> face</c>

00:06:59.390 --> 00:06:59.400 align:start position:0%
anthropic LM and then hugging face
 

00:06:59.400 --> 00:07:02.990 align:start position:0%
anthropic LM and then hugging face
embeddings<00:07:00.039><c> to</c><00:07:00.280><c> handle</c><00:07:00.560><c> it</c><00:07:01.520><c> uh</c><00:07:01.680><c> set</c><00:07:02.039><c> the</c><00:07:02.400><c> llm</c>

00:07:02.990 --> 00:07:03.000 align:start position:0%
embeddings to handle it uh set the llm
 

00:07:03.000 --> 00:07:07.350 align:start position:0%
embeddings to handle it uh set the llm
and<00:07:03.319><c> embedding</c><00:07:03.800><c> model</c><00:07:04.199><c> into</c><00:07:04.680><c> it</c><00:07:05.680><c> um</c><00:07:06.160><c> so</c><00:07:06.879><c> we'll</c>

00:07:07.350 --> 00:07:07.360 align:start position:0%
and embedding model into it um so we'll
 

00:07:07.360 --> 00:07:10.710 align:start position:0%
and embedding model into it um so we'll
consider<00:07:08.080><c> uh</c><00:07:08.199><c> Wikipedia</c><00:07:09.120><c> Pages</c><00:07:09.960><c> U</c><00:07:10.280><c> of</c><00:07:10.520><c> these</c>

00:07:10.710 --> 00:07:10.720 align:start position:0%
consider uh Wikipedia Pages U of these
 

00:07:10.720 --> 00:07:14.110 align:start position:0%
consider uh Wikipedia Pages U of these
cities<00:07:11.520><c> Toronto</c><00:07:12.039><c> Seattle</c><00:07:12.840><c> Chicago</c><00:07:13.520><c> Boston</c>

00:07:14.110 --> 00:07:14.120 align:start position:0%
cities Toronto Seattle Chicago Boston
 

00:07:14.120 --> 00:07:14.909 align:start position:0%
cities Toronto Seattle Chicago Boston
and

00:07:14.909 --> 00:07:14.919 align:start position:0%
and
 

00:07:14.919 --> 00:07:18.670 align:start position:0%
and
hoston<00:07:15.919><c> and</c><00:07:16.440><c> uh</c><00:07:16.960><c> once</c><00:07:17.160><c> you</c><00:07:17.360><c> get</c><00:07:17.599><c> this</c><00:07:18.319><c> uh</c><00:07:18.440><c> we'll</c>

00:07:18.670 --> 00:07:18.680 align:start position:0%
hoston and uh once you get this uh we'll
 

00:07:18.680 --> 00:07:23.550 align:start position:0%
hoston and uh once you get this uh we'll
save<00:07:19.680><c> locally</c><00:07:20.720><c> and</c><00:07:21.720><c> and</c><00:07:21.879><c> then</c><00:07:22.080><c> we'll</c><00:07:22.680><c> uh</c><00:07:22.960><c> load</c>

00:07:23.550 --> 00:07:23.560 align:start position:0%
save locally and and then we'll uh load
 

00:07:23.560 --> 00:07:26.110 align:start position:0%
save locally and and then we'll uh load
these<00:07:24.160><c> documents</c><00:07:25.160><c> into</c><00:07:25.400><c> a</c><00:07:25.520><c> dictionary</c><00:07:25.919><c> with</c><00:07:26.039><c> a</c>

00:07:26.110 --> 00:07:26.120 align:start position:0%
these documents into a dictionary with a
 

00:07:26.120 --> 00:07:30.909 align:start position:0%
these documents into a dictionary with a
city<00:07:26.440><c> title</c><00:07:27.440><c> right</c><00:07:27.840><c> and</c><00:07:28.840><c> once</c><00:07:29.479><c> we</c><00:07:29.680><c> have</c><00:07:30.360><c> um</c>

00:07:30.909 --> 00:07:30.919 align:start position:0%
city title right and once we have um
 

00:07:30.919 --> 00:07:33.790 align:start position:0%
city title right and once we have um
these<00:07:31.199><c> documents</c><00:07:32.160><c> we'll</c><00:07:32.400><c> build</c><00:07:32.759><c> both</c><00:07:33.360><c> summary</c>

00:07:33.790 --> 00:07:33.800 align:start position:0%
these documents we'll build both summary
 

00:07:33.800 --> 00:07:36.629 align:start position:0%
these documents we'll build both summary
index<00:07:34.160><c> Square</c><00:07:34.639><c> summary</c><00:07:35.000><c> index</c><00:07:35.440><c> and</c><00:07:36.360><c> Vector</c>

00:07:36.629 --> 00:07:36.639 align:start position:0%
index Square summary index and Vector
 

00:07:36.639 --> 00:07:38.869 align:start position:0%
index Square summary index and Vector
index<00:07:36.919><c> for</c><00:07:37.080><c> each</c><00:07:37.240><c> of</c><00:07:37.479><c> these</c><00:07:37.759><c> documents</c><00:07:38.759><c> and</c>

00:07:38.869 --> 00:07:38.879 align:start position:0%
index for each of these documents and
 

00:07:38.879 --> 00:07:41.390 align:start position:0%
index for each of these documents and
then<00:07:39.000><c> on</c><00:07:39.199><c> top</c><00:07:39.400><c> of</c><00:07:39.599><c> that</c><00:07:40.120><c> create</c><00:07:40.560><c> tools</c><00:07:41.120><c> for</c>

00:07:41.390 --> 00:07:41.400 align:start position:0%
then on top of that create tools for
 

00:07:41.400 --> 00:07:43.950 align:start position:0%
then on top of that create tools for
both<00:07:41.599><c> of</c><00:07:42.039><c> these</c><00:07:43.039><c> can</c><00:07:43.199><c> Define</c><00:07:43.440><c> the</c><00:07:43.560><c> tools</c><00:07:43.840><c> and</c>

00:07:43.950 --> 00:07:43.960 align:start position:0%
both of these can Define the tools and
 

00:07:43.960 --> 00:07:46.110 align:start position:0%
both of these can Define the tools and
then<00:07:44.120><c> build</c><00:07:44.400><c> an</c><00:07:44.720><c> react</c><00:07:45.039><c> agent</c><00:07:45.599><c> like</c><00:07:45.840><c> we</c><00:07:45.960><c> have</c>

00:07:46.110 --> 00:07:46.120 align:start position:0%
then build an react agent like we have
 

00:07:46.120 --> 00:07:48.110 align:start position:0%
then build an react agent like we have
seen<00:07:46.360><c> earlier</c><00:07:46.840><c> right</c><00:07:47.000><c> the</c><00:07:47.159><c> same</c><00:07:47.440><c> thing</c><00:07:47.960><c> but</c>

00:07:48.110 --> 00:07:48.120 align:start position:0%
seen earlier right the same thing but
 

00:07:48.120 --> 00:07:50.790 align:start position:0%
seen earlier right the same thing but
we'll<00:07:48.360><c> do</c><00:07:48.720><c> this</c><00:07:48.879><c> for</c><00:07:49.199><c> all</c><00:07:49.479><c> the</c><00:07:49.879><c> city</c><00:07:50.360><c> documents</c>

00:07:50.790 --> 00:07:50.800 align:start position:0%
we'll do this for all the city documents
 

00:07:50.800 --> 00:07:54.189 align:start position:0%
we'll do this for all the city documents
that<00:07:50.919><c> are</c><00:07:51.240><c> available</c><00:07:52.240><c> so</c><00:07:53.039><c> so</c><00:07:53.400><c> here</c><00:07:53.680><c> we</c><00:07:53.840><c> have</c><00:07:54.000><c> 1</c>

00:07:54.189 --> 00:07:54.199 align:start position:0%
that are available so so here we have 1
 

00:07:54.199 --> 00:07:57.550 align:start position:0%
that are available so so here we have 1
2<00:07:54.440><c> 3</c><00:07:54.680><c> 4</c><00:07:55.039><c> five</c><00:07:55.560><c> itties</c><00:07:55.960><c> right</c><00:07:56.159><c> so</c><00:07:56.599><c> there</c><00:07:56.759><c> are</c>

00:07:57.550 --> 00:07:57.560 align:start position:0%
2 3 4 five itties right so there are
 

00:07:57.560 --> 00:07:59.869 align:start position:0%
2 3 4 five itties right so there are
there<00:07:57.680><c> will</c><00:07:57.800><c> be</c><00:07:58.039><c> five</c><00:07:58.680><c> different</c><00:07:58.960><c> agents</c>

00:07:59.869 --> 00:07:59.879 align:start position:0%
there will be five different agents
 

00:07:59.879 --> 00:08:03.629 align:start position:0%
there will be five different agents
right<00:08:00.680><c> and</c><00:08:01.120><c> then</c><00:08:02.120><c> and</c><00:08:02.280><c> then</c><00:08:02.479><c> on</c><00:08:02.680><c> top</c><00:08:02.840><c> of</c><00:08:03.039><c> that</c>

00:08:03.629 --> 00:08:03.639 align:start position:0%
right and then and then on top of that
 

00:08:03.639 --> 00:08:06.469 align:start position:0%
right and then and then on top of that
we'll<00:08:03.960><c> create</c><00:08:04.199><c> an</c><00:08:04.360><c> index</c><00:08:04.759><c> node</c><00:08:05.759><c> which</c><00:08:05.919><c> will</c>

00:08:06.469 --> 00:08:06.479 align:start position:0%
we'll create an index node which will
 

00:08:06.479 --> 00:08:08.589 align:start position:0%
we'll create an index node which will
give<00:08:06.800><c> summary</c><00:08:07.360><c> of</c><00:08:07.919><c> which</c><00:08:08.039><c> will</c><00:08:08.280><c> have</c><00:08:08.440><c> the</c>

00:08:08.589 --> 00:08:08.599 align:start position:0%
give summary of which will have the
 

00:08:08.599 --> 00:08:12.230 align:start position:0%
give summary of which will have the
summary<00:08:09.000><c> of</c><00:08:09.240><c> what</c><00:08:09.479><c> each</c><00:08:09.919><c> agent</c><00:08:10.479><c> can</c><00:08:10.720><c> do</c><00:08:11.360><c> like</c>

00:08:12.230 --> 00:08:12.240 align:start position:0%
summary of what each agent can do like
 

00:08:12.240 --> 00:08:15.309 align:start position:0%
summary of what each agent can do like
uh<00:08:12.479><c> this</c><00:08:13.080><c> contains</c><00:08:14.000><c> articles</c><00:08:14.400><c> about</c><00:08:14.680><c> specific</c>

00:08:15.309 --> 00:08:15.319 align:start position:0%
uh this contains articles about specific
 

00:08:15.319 --> 00:08:20.830 align:start position:0%
uh this contains articles about specific
City<00:08:16.199><c> and</c><00:08:16.440><c> then</c><00:08:16.960><c> uh</c><00:08:17.639><c> use</c><00:08:18.000><c> this</c><00:08:18.520><c> uh</c><00:08:19.000><c> to</c>

00:08:20.830 --> 00:08:20.840 align:start position:0%
City and then uh use this uh to
 

00:08:20.840 --> 00:08:23.710 align:start position:0%
City and then uh use this uh to
analyze<00:08:21.840><c> uh</c><00:08:22.039><c> for</c><00:08:22.319><c> specific</c><00:08:22.759><c> facts</c><00:08:23.080><c> about</c><00:08:23.479><c> that</c>

00:08:23.710 --> 00:08:23.720 align:start position:0%
analyze uh for specific facts about that
 

00:08:23.720 --> 00:08:25.830 align:start position:0%
analyze uh for specific facts about that
specific<00:08:24.120><c> City</c><00:08:24.440><c> and</c><00:08:24.639><c> not</c><00:08:24.919><c> about</c><00:08:25.280><c> multiple</c>

00:08:25.830 --> 00:08:25.840 align:start position:0%
specific City and not about multiple
 

00:08:25.840 --> 00:08:29.510 align:start position:0%
specific City and not about multiple
cities<00:08:26.840><c> right</c><00:08:27.440><c> and</c><00:08:27.639><c> then</c><00:08:27.879><c> on</c><00:08:28.120><c> top</c><00:08:28.280><c> of</c><00:08:28.479><c> this</c><00:08:29.000><c> you</c>

00:08:29.510 --> 00:08:29.520 align:start position:0%
cities right and then on top of this you
 

00:08:29.520 --> 00:08:32.909 align:start position:0%
cities right and then on top of this you
create<00:08:29.800><c> a</c><00:08:29.960><c> vector</c><00:08:30.240><c> store</c><00:08:30.639><c> index</c><00:08:31.639><c> uh</c><00:08:31.800><c> so</c><00:08:32.719><c> this</c>

00:08:32.909 --> 00:08:32.919 align:start position:0%
create a vector store index uh so this
 

00:08:32.919 --> 00:08:36.149 align:start position:0%
create a vector store index uh so this
Vector<00:08:33.159><c> store</c><00:08:33.399><c> index</c><00:08:33.640><c> will</c><00:08:33.800><c> Reve</c><00:08:34.159><c> the</c><00:08:34.440><c> most</c><00:08:35.440><c> uh</c>

00:08:36.149 --> 00:08:36.159 align:start position:0%
Vector store index will Reve the most uh
 

00:08:36.159 --> 00:08:38.310 align:start position:0%
Vector store index will Reve the most uh
correct

00:08:38.310 --> 00:08:38.320 align:start position:0%
correct
 

00:08:38.320 --> 00:08:41.509 align:start position:0%
correct
uh<00:08:39.320><c> City</c><00:08:39.880><c> agent</c><00:08:40.560><c> that</c><00:08:40.719><c> will</c><00:08:40.880><c> be</c><00:08:41.039><c> useful</c><00:08:41.360><c> to</c>

00:08:41.509 --> 00:08:41.519 align:start position:0%
uh City agent that will be useful to
 

00:08:41.519 --> 00:08:46.150 align:start position:0%
uh City agent that will be useful to
answer<00:08:41.760><c> a</c><00:08:41.919><c> query</c><00:08:42.800><c> right</c><00:08:43.440><c> so</c><00:08:43.760><c> here</c><00:08:44.760><c> um</c><00:08:45.760><c> uh</c><00:08:46.000><c> with</c>

00:08:46.150 --> 00:08:46.160 align:start position:0%
answer a query right so here um uh with
 

00:08:46.160 --> 00:08:48.150 align:start position:0%
answer a query right so here um uh with
the<00:08:46.279><c> test</c><00:08:46.480><c> queries</c><00:08:46.959><c> as</c><00:08:47.040><c> you</c><00:08:47.160><c> can</c><00:08:47.320><c> see</c><00:08:47.959><c> the</c>

00:08:48.150 --> 00:08:48.160 align:start position:0%
the test queries as you can see the
 

00:08:48.160 --> 00:08:49.590 align:start position:0%
the test queries as you can see the
first<00:08:48.399><c> question</c><00:08:48.640><c> is</c><00:08:48.839><c> what</c><00:08:48.959><c> is</c><00:08:49.040><c> the</c><00:08:49.200><c> population</c>

00:08:49.590 --> 00:08:49.600 align:start position:0%
first question is what is the population
 

00:08:49.600 --> 00:08:52.550 align:start position:0%
first question is what is the population
of<00:08:49.720><c> Toronto</c><00:08:50.480><c> right</c><00:08:51.080><c> so</c><00:08:51.279><c> this</c><00:08:51.440><c> is</c><00:08:51.920><c> a</c><00:08:52.120><c> specific</c>

00:08:52.550 --> 00:08:52.560 align:start position:0%
of Toronto right so this is a specific
 

00:08:52.560 --> 00:08:55.470 align:start position:0%
of Toronto right so this is a specific
question<00:08:52.959><c> and</c><00:08:53.440><c> um</c><00:08:53.600><c> a</c><00:08:53.720><c> vector</c><00:08:54.000><c> tool</c><00:08:54.279><c> should</c><00:08:54.480><c> be</c>

00:08:55.470 --> 00:08:55.480 align:start position:0%
question and um a vector tool should be
 

00:08:55.480 --> 00:08:59.750 align:start position:0%
question and um a vector tool should be
used<00:08:56.519><c> so</c><00:08:57.519><c> we</c><00:08:57.680><c> need</c><00:08:57.880><c> to</c><00:08:58.600><c> uh</c>

00:08:59.750 --> 00:08:59.760 align:start position:0%
used so we need to uh
 

00:08:59.760 --> 00:09:01.870 align:start position:0%
used so we need to uh
you<00:08:59.959><c> can</c><00:09:00.160><c> see</c><00:09:00.440><c> that</c><00:09:00.600><c> a</c><00:09:00.760><c> vector</c><00:09:01.040><c> tool</c><00:09:01.279><c> is</c><00:09:01.399><c> used</c>

00:09:01.870 --> 00:09:01.880 align:start position:0%
you can see that a vector tool is used
 

00:09:01.880 --> 00:09:05.190 align:start position:0%
you can see that a vector tool is used
with<00:09:02.399><c> for</c><00:09:02.760><c> Toronto</c><00:09:03.519><c> and</c><00:09:03.880><c> then</c><00:09:04.880><c> and</c><00:09:05.040><c> then</c>

00:09:05.190 --> 00:09:05.200 align:start position:0%
with for Toronto and then and then
 

00:09:05.200 --> 00:09:08.430 align:start position:0%
with for Toronto and then and then
accordingly<00:09:06.200><c> yeah</c><00:09:06.680><c> it</c><00:09:06.920><c> has</c><00:09:07.560><c> used</c><00:09:08.000><c> the</c>

00:09:08.430 --> 00:09:08.440 align:start position:0%
accordingly yeah it has used the
 

00:09:08.440 --> 00:09:10.750 align:start position:0%
accordingly yeah it has used the
specific<00:09:08.839><c> tool</c><00:09:09.360><c> and</c><00:09:09.920><c> got</c><00:09:10.160><c> an</c><00:09:10.399><c> answer</c>

00:09:10.750 --> 00:09:10.760 align:start position:0%
specific tool and got an answer
 

00:09:10.760 --> 00:09:14.470 align:start position:0%
specific tool and got an answer
accordingly<00:09:11.760><c> right</c><00:09:12.760><c> and</c><00:09:13.040><c> then</c><00:09:13.959><c> the</c><00:09:14.120><c> next</c><00:09:14.320><c> one</c>

00:09:14.470 --> 00:09:14.480 align:start position:0%
accordingly right and then the next one
 

00:09:14.480 --> 00:09:18.750 align:start position:0%
accordingly right and then the next one
is<00:09:14.640><c> about</c><00:09:15.120><c> uh</c><00:09:15.600><c> hon</c><00:09:16.519><c> and</c><00:09:16.720><c> it</c><00:09:16.920><c> used</c><00:09:17.560><c> hoston</c><00:09:18.000><c> agent</c>

00:09:18.750 --> 00:09:18.760 align:start position:0%
is about uh hon and it used hoston agent
 

00:09:18.760 --> 00:09:21.389 align:start position:0%
is about uh hon and it used hoston agent
and<00:09:19.000><c> Vector</c><00:09:19.279><c> tool</c><00:09:19.640><c> inside</c><00:09:19.959><c> it</c><00:09:20.640><c> to</c><00:09:20.959><c> give</c><00:09:21.160><c> an</c>

00:09:21.389 --> 00:09:21.399 align:start position:0%
and Vector tool inside it to give an
 

00:09:21.399 --> 00:09:22.829 align:start position:0%
and Vector tool inside it to give an
answer<00:09:21.720><c> for</c>

00:09:22.829 --> 00:09:22.839 align:start position:0%
answer for
 

00:09:22.839 --> 00:09:26.509 align:start position:0%
answer for
it<00:09:23.839><c> right</c><00:09:24.720><c> and</c><00:09:25.079><c> similarly</c><00:09:25.519><c> it</c><00:09:25.640><c> can</c><00:09:25.880><c> even</c><00:09:26.120><c> use</c>

00:09:26.509 --> 00:09:26.519 align:start position:0%
it right and similarly it can even use
 

00:09:26.519 --> 00:09:29.470 align:start position:0%
it right and similarly it can even use
summary<00:09:27.000><c> tools</c><00:09:27.399><c> as</c><00:09:27.560><c> well</c><00:09:27.839><c> right</c><00:09:28.120><c> so</c><00:09:29.200><c> so</c><00:09:29.320><c> for</c>

00:09:29.470 --> 00:09:29.480 align:start position:0%
summary tools as well right so so for
 

00:09:29.480 --> 00:09:31.509 align:start position:0%
summary tools as well right so so for
Boston<00:09:30.200><c> we</c><00:09:30.360><c> have</c><00:09:30.519><c> summarize</c><00:09:30.959><c> about</c><00:09:31.200><c> sports</c>

00:09:31.509 --> 00:09:31.519 align:start position:0%
Boston we have summarize about sports
 

00:09:31.519 --> 00:09:34.630 align:start position:0%
Boston we have summarize about sports
teams<00:09:31.800><c> in</c><00:09:32.000><c> Boston</c><00:09:32.600><c> so</c><00:09:33.240><c> it</c><00:09:33.399><c> use</c><00:09:33.680><c> summary</c><00:09:34.079><c> tool</c>

00:09:34.630 --> 00:09:34.640 align:start position:0%
teams in Boston so it use summary tool
 

00:09:34.640 --> 00:09:37.949 align:start position:0%
teams in Boston so it use summary tool
and<00:09:35.040><c> then</c><00:09:36.240><c> accordingly</c><00:09:37.240><c> gave</c><00:09:37.440><c> an</c><00:09:37.600><c> answer</c>

00:09:37.949 --> 00:09:37.959 align:start position:0%
and then accordingly gave an answer
 

00:09:37.959 --> 00:09:41.990 align:start position:0%
and then accordingly gave an answer
about<00:09:38.600><c> Boston</c><00:09:39.000><c> sports</c><00:09:39.800><c> teams</c><00:09:40.800><c> similarly</c><00:09:41.399><c> for</c>

00:09:41.990 --> 00:09:42.000 align:start position:0%
about Boston sports teams similarly for
 

00:09:42.000 --> 00:09:46.230 align:start position:0%
about Boston sports teams similarly for
Chicago<00:09:42.959><c> it's</c><00:09:43.640><c> uh</c><00:09:44.640><c> all</c><00:09:45.200><c> po</c><00:09:45.519><c> by</c><00:09:45.680><c> aspects</c><00:09:46.040><c> of</c>

00:09:46.230 --> 00:09:46.240 align:start position:0%
Chicago it's uh all po by aspects of
 

00:09:46.240 --> 00:09:49.350 align:start position:0%
Chicago it's uh all po by aspects of
Chicago<00:09:46.760><c> so</c><00:09:46.920><c> it</c><00:09:47.040><c> used</c><00:09:47.279><c> summary</c><00:09:47.640><c> tool</c><00:09:48.000><c> here</c><00:09:48.800><c> and</c>

00:09:49.350 --> 00:09:49.360 align:start position:0%
Chicago so it used summary tool here and
 

00:09:49.360 --> 00:09:50.790 align:start position:0%
Chicago so it used summary tool here and
got<00:09:49.560><c> an</c><00:09:49.760><c> answer</c>

00:09:50.790 --> 00:09:50.800 align:start position:0%
got an answer
 

00:09:50.800 --> 00:09:54.550 align:start position:0%
got an answer
accordingly<00:09:51.800><c> all</c><00:09:52.040><c> right</c><00:09:52.480><c> so</c><00:09:53.240><c> so</c><00:09:53.440><c> this</c><00:09:53.640><c> way</c><00:09:54.360><c> uh</c>

00:09:54.550 --> 00:09:54.560 align:start position:0%
accordingly all right so so this way uh
 

00:09:54.560 --> 00:09:57.470 align:start position:0%
accordingly all right so so this way uh
were<00:09:54.760><c> a</c><00:09:54.880><c> lot</c><00:09:55.040><c> of</c><00:09:55.320><c> documents</c><00:09:56.320><c> here</c><00:09:56.480><c> for</c><00:09:57.040><c> example</c>

00:09:57.470 --> 00:09:57.480 align:start position:0%
were a lot of documents here for example
 

00:09:57.480 --> 00:10:01.630 align:start position:0%
were a lot of documents here for example
in<00:09:57.560><c> a</c><00:09:57.680><c> simpl</c><00:09:58.399><c> case</c><00:09:58.640><c> I</c><00:09:58.720><c> have</c><00:09:58.839><c> used</c>

00:10:01.630 --> 00:10:01.640 align:start position:0%
 
 

00:10:01.640 --> 00:10:04.750 align:start position:0%
 
uh<00:10:02.200><c> Wikipedia</c><00:10:03.079><c> Pages</c><00:10:03.600><c> for</c><00:10:04.120><c> a</c><00:10:04.200><c> certain</c><00:10:04.480><c> number</c>

00:10:04.750 --> 00:10:04.760 align:start position:0%
uh Wikipedia Pages for a certain number
 

00:10:04.760 --> 00:10:06.630 align:start position:0%
uh Wikipedia Pages for a certain number
of<00:10:04.880><c> cities</c><00:10:05.320><c> you</c><00:10:05.399><c> can</c><00:10:05.600><c> expand</c><00:10:05.959><c> this</c><00:10:06.079><c> to</c><00:10:06.279><c> lot</c><00:10:06.440><c> of</c>

00:10:06.630 --> 00:10:06.640 align:start position:0%
of cities you can expand this to lot of
 

00:10:06.640 --> 00:10:09.550 align:start position:0%
of cities you can expand this to lot of
documents<00:10:07.320><c> and</c><00:10:08.079><c> build</c><00:10:08.519><c> similar</c><00:10:08.920><c> agents</c><00:10:09.360><c> and</c>

00:10:09.550 --> 00:10:09.560 align:start position:0%
documents and build similar agents and
 

00:10:09.560 --> 00:10:13.350 align:start position:0%
documents and build similar agents and
tools<00:10:09.839><c> and</c><00:10:10.040><c> reters</c><00:10:10.519><c> on</c><00:10:10.720><c> top</c><00:10:10.920><c> of</c><00:10:11.079><c> it</c><00:10:11.880><c> and</c><00:10:12.519><c> um</c><00:10:13.200><c> get</c>

00:10:13.350 --> 00:10:13.360 align:start position:0%
tools and reters on top of it and um get
 

00:10:13.360 --> 00:10:16.069 align:start position:0%
tools and reters on top of it and um get
an<00:10:13.560><c> answer</c><00:10:14.000><c> accordingly</c><00:10:15.000><c> um</c><00:10:15.320><c> do</c><00:10:15.640><c> experiment</c>

00:10:16.069 --> 00:10:16.079 align:start position:0%
an answer accordingly um do experiment
 

00:10:16.079 --> 00:10:19.509 align:start position:0%
an answer accordingly um do experiment
and<00:10:16.240><c> explore</c><00:10:16.880><c> uh</c><00:10:17.000><c> with</c><00:10:17.200><c> these</c><00:10:17.480><c> things</c><00:10:17.959><c> and</c><00:10:18.519><c> uh</c>

00:10:19.509 --> 00:10:19.519 align:start position:0%
and explore uh with these things and uh
 

00:10:19.519 --> 00:10:25.240 align:start position:0%
and explore uh with these things and uh
uh<00:10:19.680><c> see</c><00:10:19.880><c> you</c><00:10:20.120><c> in</c><00:10:20.240><c> the</c><00:10:20.399><c> next</c><00:10:20.839><c> video</c><00:10:21.839><c> thank</c><00:10:22.240><c> you</c>

