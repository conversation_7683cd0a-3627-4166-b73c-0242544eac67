WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.790 align:start position:0%
 
hey<00:00:00.480><c> and</c><00:00:00.719><c> welcome</c><00:00:00.900><c> back</c><00:00:01.020><c> to</c><00:00:01.199><c> the</c><00:00:01.319><c> third</c><00:00:01.560><c> of</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
hey and welcome back to the third of
 

00:00:01.800 --> 00:00:03.649 align:start position:0%
hey and welcome back to the third of
five<00:00:02.040><c> principles</c><00:00:02.460><c> and</c><00:00:03.000><c> this</c><00:00:03.179><c> one</c><00:00:03.300><c> is</c><00:00:03.419><c> called</c>

00:00:03.649 --> 00:00:03.659 align:start position:0%
five principles and this one is called
 

00:00:03.659 --> 00:00:05.630 align:start position:0%
five principles and this one is called
The<00:00:03.899><c> List</c><00:00:04.080><c> golf</c><00:00:04.319><c> substitution</c><00:00:05.100><c> principle</c>

00:00:05.630 --> 00:00:05.640 align:start position:0%
The List golf substitution principle
 

00:00:05.640 --> 00:00:06.889 align:start position:0%
The List golf substitution principle
let's<00:00:06.060><c> break</c><00:00:06.240><c> down</c><00:00:06.359><c> what</c><00:00:06.540><c> this</c><00:00:06.600><c> principle</c>

00:00:06.889 --> 00:00:06.899 align:start position:0%
let's break down what this principle
 

00:00:06.899 --> 00:00:08.570 align:start position:0%
let's break down what this principle
means<00:00:07.259><c> when</c><00:00:07.560><c> we</c><00:00:07.740><c> talk</c><00:00:07.859><c> about</c><00:00:07.980><c> this</c><00:00:08.220><c> principle</c>

00:00:08.570 --> 00:00:08.580 align:start position:0%
means when we talk about this principle
 

00:00:08.580 --> 00:00:10.030 align:start position:0%
means when we talk about this principle
we're<00:00:08.940><c> going</c><00:00:09.059><c> to</c><00:00:09.120><c> be</c><00:00:09.179><c> using</c><00:00:09.480><c> the</c><00:00:09.599><c> words</c>

00:00:10.030 --> 00:00:10.040 align:start position:0%
we're going to be using the words
 

00:00:10.040 --> 00:00:12.650 align:start position:0%
we're going to be using the words
substitutability<00:00:11.040><c> and</c><00:00:11.760><c> preserved</c><00:00:12.240><c> Behavior</c>

00:00:12.650 --> 00:00:12.660 align:start position:0%
substitutability and preserved Behavior
 

00:00:12.660 --> 00:00:14.390 align:start position:0%
substitutability and preserved Behavior
according<00:00:13.259><c> to</c><00:00:13.380><c> the</c><00:00:13.500><c> list</c><00:00:13.620><c> of</c><00:00:13.920><c> substitution</c>

00:00:14.390 --> 00:00:14.400 align:start position:0%
according to the list of substitution
 

00:00:14.400 --> 00:00:16.849 align:start position:0%
according to the list of substitution
principle<00:00:14.880><c> any</c><00:00:15.480><c> instance</c><00:00:15.960><c> of</c><00:00:16.199><c> a</c><00:00:16.379><c> derived</c>

00:00:16.849 --> 00:00:16.859 align:start position:0%
principle any instance of a derived
 

00:00:16.859 --> 00:00:19.310 align:start position:0%
principle any instance of a derived
class<00:00:17.160><c> should</c><00:00:17.880><c> be</c><00:00:18.000><c> able</c><00:00:18.180><c> to</c><00:00:18.420><c> substitute</c><00:00:19.020><c> the</c>

00:00:19.310 --> 00:00:19.320 align:start position:0%
class should be able to substitute the
 

00:00:19.320 --> 00:00:21.349 align:start position:0%
class should be able to substitute the
base<00:00:19.500><c> class</c><00:00:19.859><c> without</c><00:00:20.520><c> affecting</c><00:00:21.119><c> the</c>

00:00:21.349 --> 00:00:21.359 align:start position:0%
base class without affecting the
 

00:00:21.359 --> 00:00:23.269 align:start position:0%
base class without affecting the
correctness<00:00:21.779><c> of</c><00:00:22.080><c> the</c><00:00:22.320><c> program</c><00:00:22.500><c> basically</c>

00:00:23.269 --> 00:00:23.279 align:start position:0%
correctness of the program basically
 

00:00:23.279 --> 00:00:25.849 align:start position:0%
correctness of the program basically
meaning<00:00:23.460><c> we</c><00:00:23.640><c> can</c><00:00:23.820><c> take</c><00:00:23.939><c> any</c><00:00:24.300><c> subclass</c><00:00:24.960><c> of</c><00:00:25.560><c> its</c>

00:00:25.849 --> 00:00:25.859 align:start position:0%
meaning we can take any subclass of its
 

00:00:25.859 --> 00:00:28.130 align:start position:0%
meaning we can take any subclass of its
superclass<00:00:26.400><c> no</c><00:00:26.820><c> matter</c><00:00:27.060><c> what</c><00:00:27.359><c> we</c><00:00:27.660><c> substitute</c>

00:00:28.130 --> 00:00:28.140 align:start position:0%
superclass no matter what we substitute
 

00:00:28.140 --> 00:00:31.009 align:start position:0%
superclass no matter what we substitute
any<00:00:28.500><c> of</c><00:00:28.680><c> them</c><00:00:28.800><c> in</c><00:00:29.279><c> for</c><00:00:29.580><c> the</c><00:00:30.180><c> base</c><00:00:30.359><c> class</c><00:00:30.660><c> or</c><00:00:30.900><c> the</c>

00:00:31.009 --> 00:00:31.019 align:start position:0%
any of them in for the base class or the
 

00:00:31.019 --> 00:00:33.110 align:start position:0%
any of them in for the base class or the
superclass<00:00:31.619><c> and</c><00:00:32.099><c> it's</c><00:00:32.340><c> not</c><00:00:32.579><c> going</c><00:00:32.759><c> to</c><00:00:32.880><c> change</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
superclass and it's not going to change
 

00:00:33.120 --> 00:00:35.750 align:start position:0%
superclass and it's not going to change
how<00:00:33.480><c> the</c><00:00:33.719><c> program</c><00:00:33.960><c> behaves</c><00:00:34.800><c> which</c><00:00:35.340><c> brings</c><00:00:35.640><c> us</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
how the program behaves which brings us
 

00:00:35.760 --> 00:00:38.209 align:start position:0%
how the program behaves which brings us
to<00:00:35.940><c> the</c><00:00:36.120><c> next</c><00:00:36.239><c> part</c><00:00:36.480><c> preserving</c><00:00:37.140><c> Behavior</c><00:00:37.680><c> the</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
to the next part preserving Behavior the
 

00:00:38.219 --> 00:00:40.430 align:start position:0%
to the next part preserving Behavior the
derived<00:00:38.700><c> classes</c><00:00:39.239><c> should</c><00:00:39.840><c> preserve</c><00:00:40.320><c> the</c>

00:00:40.430 --> 00:00:40.440 align:start position:0%
derived classes should preserve the
 

00:00:40.440 --> 00:00:42.290 align:start position:0%
derived classes should preserve the
behavior<00:00:40.860><c> of</c><00:00:41.160><c> the</c><00:00:41.280><c> Base</c><00:00:41.460><c> Class</c><00:00:41.760><c> basically</c>

00:00:42.290 --> 00:00:42.300 align:start position:0%
behavior of the Base Class basically
 

00:00:42.300 --> 00:00:43.610 align:start position:0%
behavior of the Base Class basically
what<00:00:42.420><c> I</c><00:00:42.600><c> just</c><00:00:42.719><c> said</c><00:00:42.840><c> you</c><00:00:43.140><c> can</c><00:00:43.200><c> take</c><00:00:43.320><c> the</c><00:00:43.440><c> Base</c>

00:00:43.610 --> 00:00:43.620 align:start position:0%
what I just said you can take the Base
 

00:00:43.620 --> 00:00:46.069 align:start position:0%
what I just said you can take the Base
Class<00:00:43.920><c> you</c><00:00:44.460><c> can</c><00:00:44.640><c> take</c><00:00:44.820><c> all</c><00:00:45.180><c> of</c><00:00:45.300><c> these</c><00:00:45.600><c> derived</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
Class you can take all of these derived
 

00:00:46.079 --> 00:00:47.330 align:start position:0%
Class you can take all of these derived
classes<00:00:46.440><c> that</c><00:00:46.620><c> you</c><00:00:46.739><c> have</c><00:00:46.920><c> over</c><00:00:47.160><c> here</c>

00:00:47.330 --> 00:00:47.340 align:start position:0%
classes that you have over here
 

00:00:47.340 --> 00:00:49.430 align:start position:0%
classes that you have over here
substitute<00:00:48.059><c> them</c><00:00:48.180><c> for</c><00:00:48.420><c> the</c><00:00:48.539><c> Base</c><00:00:48.719><c> Class</c><00:00:49.020><c> any</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
substitute them for the Base Class any
 

00:00:49.440 --> 00:00:51.529 align:start position:0%
substitute them for the Base Class any
one<00:00:49.680><c> of</c><00:00:49.800><c> them</c><00:00:49.920><c> and</c><00:00:50.460><c> the</c><00:00:50.700><c> behavior</c><00:00:51.120><c> of</c><00:00:51.360><c> the</c>

00:00:51.529 --> 00:00:51.539 align:start position:0%
one of them and the behavior of the
 

00:00:51.539 --> 00:00:53.630 align:start position:0%
one of them and the behavior of the
program<00:00:51.719><c> won't</c><00:00:52.379><c> change</c><00:00:52.680><c> let's</c><00:00:53.160><c> dive</c><00:00:53.399><c> in</c><00:00:53.520><c> and</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
program won't change let's dive in and
 

00:00:53.640 --> 00:00:54.889 align:start position:0%
program won't change let's dive in and
just<00:00:53.760><c> look</c><00:00:53.879><c> at</c><00:00:53.940><c> an</c><00:00:54.059><c> example</c><00:00:54.239><c> of</c><00:00:54.420><c> this</c><00:00:54.600><c> all</c>

00:00:54.889 --> 00:00:54.899 align:start position:0%
just look at an example of this all
 

00:00:54.899 --> 00:00:56.090 align:start position:0%
just look at an example of this all
right<00:00:55.079><c> so</c><00:00:55.260><c> for</c><00:00:55.379><c> the</c><00:00:55.500><c> example</c><00:00:55.739><c> we're</c><00:00:55.980><c> gonna</c>

00:00:56.090 --> 00:00:56.100 align:start position:0%
right so for the example we're gonna
 

00:00:56.100 --> 00:00:57.770 align:start position:0%
right so for the example we're gonna
have<00:00:56.219><c> a</c><00:00:56.340><c> vehicle</c><00:00:56.640><c> interface</c><00:00:57.120><c> and</c><00:00:57.539><c> then</c><00:00:57.660><c> we're</c>

00:00:57.770 --> 00:00:57.780 align:start position:0%
have a vehicle interface and then we're
 

00:00:57.780 --> 00:00:59.510 align:start position:0%
have a vehicle interface and then we're
gonna<00:00:57.899><c> have</c><00:00:58.020><c> two</c><00:00:58.260><c> implementations</c><00:00:58.800><c> a</c><00:00:59.219><c> bicycle</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
gonna have two implementations a bicycle
 

00:00:59.520 --> 00:01:01.430 align:start position:0%
gonna have two implementations a bicycle
and<00:00:59.699><c> a</c><00:00:59.820><c> car</c><00:01:00.120><c> now</c><00:01:00.600><c> we</c><00:01:00.840><c> should</c><00:01:00.960><c> be</c><00:01:01.079><c> able</c><00:01:01.199><c> to</c><00:01:01.260><c> take</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
and a car now we should be able to take
 

00:01:01.440 --> 00:01:03.889 align:start position:0%
and a car now we should be able to take
any<00:01:01.800><c> implementation</c><00:01:02.460><c> of</c><00:01:02.879><c> this</c><00:01:03.059><c> vehicle</c><00:01:03.359><c> even</c>

00:01:03.889 --> 00:01:03.899 align:start position:0%
any implementation of this vehicle even
 

00:01:03.899 --> 00:01:05.750 align:start position:0%
any implementation of this vehicle even
if<00:01:04.080><c> we</c><00:01:04.260><c> had</c><00:01:04.500><c> 20</c><00:01:04.860><c> different</c><00:01:05.159><c> implementations</c>

00:01:05.750 --> 00:01:05.760 align:start position:0%
if we had 20 different implementations
 

00:01:05.760 --> 00:01:08.810 align:start position:0%
if we had 20 different implementations
no<00:01:06.299><c> matter</c><00:01:06.540><c> which</c><00:01:06.840><c> one</c><00:01:07.080><c> we</c><00:01:07.320><c> use</c><00:01:07.680><c> in</c><00:01:08.280><c> our</c><00:01:08.580><c> code</c>

00:01:08.810 --> 00:01:08.820 align:start position:0%
no matter which one we use in our code
 

00:01:08.820 --> 00:01:10.550 align:start position:0%
no matter which one we use in our code
they're<00:01:09.600><c> all</c><00:01:09.780><c> going</c><00:01:09.960><c> to</c><00:01:10.080><c> preserve</c><00:01:10.439><c> the</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
they're all going to preserve the
 

00:01:10.560 --> 00:01:13.130 align:start position:0%
they're all going to preserve the
behavior<00:01:10.979><c> of</c><00:01:11.340><c> the</c><00:01:11.520><c> interface</c><00:01:11.939><c> vehicle</c><00:01:12.659><c> okay</c>

00:01:13.130 --> 00:01:13.140 align:start position:0%
behavior of the interface vehicle okay
 

00:01:13.140 --> 00:01:15.170 align:start position:0%
behavior of the interface vehicle okay
so<00:01:13.500><c> the</c><00:01:13.860><c> vehicle</c><00:01:14.159><c> interface</c><00:01:14.640><c> has</c><00:01:14.939><c> a</c><00:01:15.060><c> start</c>

00:01:15.170 --> 00:01:15.180 align:start position:0%
so the vehicle interface has a start
 

00:01:15.180 --> 00:01:17.750 align:start position:0%
so the vehicle interface has a start
engine<00:01:15.420><c> method</c><00:01:15.840><c> so</c><00:01:16.020><c> any</c><00:01:16.260><c> implementation</c><00:01:17.040><c> has</c>

00:01:17.750 --> 00:01:17.760 align:start position:0%
engine method so any implementation has
 

00:01:17.760 --> 00:01:19.969 align:start position:0%
engine method so any implementation has
to<00:01:17.939><c> use</c><00:01:18.119><c> this</c><00:01:18.299><c> method</c><00:01:18.659><c> so</c><00:01:19.320><c> let's</c><00:01:19.439><c> look</c><00:01:19.619><c> at</c><00:01:19.740><c> car</c>

00:01:19.969 --> 00:01:19.979 align:start position:0%
to use this method so let's look at car
 

00:01:19.979 --> 00:01:21.649 align:start position:0%
to use this method so let's look at car
first<00:01:20.280><c> well</c><00:01:20.759><c> the</c><00:01:21.000><c> method</c><00:01:21.240><c> is</c><00:01:21.360><c> called</c><00:01:21.479><c> start</c>

00:01:21.649 --> 00:01:21.659 align:start position:0%
first well the method is called start
 

00:01:21.659 --> 00:01:24.950 align:start position:0%
first well the method is called start
engine<00:01:21.900><c> so</c><00:01:22.320><c> a</c><00:01:23.040><c> car</c><00:01:23.220><c> does</c><00:01:23.880><c> have</c><00:01:24.000><c> an</c><00:01:24.119><c> engine</c><00:01:24.299><c> so</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
engine so a car does have an engine so
 

00:01:24.960 --> 00:01:26.570 align:start position:0%
engine so a car does have an engine so
when<00:01:25.259><c> we</c><00:01:25.439><c> call</c><00:01:25.619><c> this</c><00:01:25.799><c> method</c><00:01:26.100><c> we're</c><00:01:26.340><c> simply</c>

00:01:26.570 --> 00:01:26.580 align:start position:0%
when we call this method we're simply
 

00:01:26.580 --> 00:01:28.010 align:start position:0%
when we call this method we're simply
just<00:01:26.759><c> going</c><00:01:26.880><c> to</c><00:01:27.000><c> print</c><00:01:27.119><c> out</c><00:01:27.299><c> starting</c><00:01:27.540><c> a</c><00:01:27.840><c> car</c>

00:01:28.010 --> 00:01:28.020 align:start position:0%
just going to print out starting a car
 

00:01:28.020 --> 00:01:30.350 align:start position:0%
just going to print out starting a car
so<00:01:28.320><c> the</c><00:01:28.500><c> next</c><00:01:28.619><c> vehicle</c><00:01:28.979><c> is</c><00:01:29.340><c> a</c><00:01:29.460><c> bicycle</c><00:01:29.820><c> it</c><00:01:30.240><c> has</c>

00:01:30.350 --> 00:01:30.360 align:start position:0%
so the next vehicle is a bicycle it has
 

00:01:30.360 --> 00:01:32.210 align:start position:0%
so the next vehicle is a bicycle it has
to<00:01:30.479><c> implement</c><00:01:30.720><c> the</c><00:01:30.900><c> method</c><00:01:31.200><c> start</c><00:01:31.380><c> engine</c><00:01:31.619><c> but</c>

00:01:32.210 --> 00:01:32.220 align:start position:0%
to implement the method start engine but
 

00:01:32.220 --> 00:01:34.429 align:start position:0%
to implement the method start engine but
a<00:01:32.460><c> bicycle</c><00:01:32.820><c> doesn't</c><00:01:33.119><c> have</c><00:01:33.360><c> an</c><00:01:33.600><c> engine</c><00:01:33.720><c> so</c><00:01:34.200><c> this</c>

00:01:34.429 --> 00:01:34.439 align:start position:0%
a bicycle doesn't have an engine so this
 

00:01:34.439 --> 00:01:36.469 align:start position:0%
a bicycle doesn't have an engine so this
violates<00:01:34.979><c> the</c><00:01:35.340><c> list</c><00:01:35.520><c> call</c><00:01:35.759><c> of</c><00:01:35.939><c> substitution</c>

00:01:36.469 --> 00:01:36.479 align:start position:0%
violates the list call of substitution
 

00:01:36.479 --> 00:01:38.929 align:start position:0%
violates the list call of substitution
principle<00:01:36.960><c> we</c><00:01:37.560><c> can't</c><00:01:37.740><c> replace</c><00:01:38.100><c> a</c><00:01:38.640><c> vehicle</c>

00:01:38.929 --> 00:01:38.939 align:start position:0%
principle we can't replace a vehicle
 

00:01:38.939 --> 00:01:41.510 align:start position:0%
principle we can't replace a vehicle
with<00:01:39.720><c> a</c><00:01:39.900><c> bicycle</c><00:01:40.259><c> because</c><00:01:40.740><c> a</c><00:01:40.920><c> bicycle</c><00:01:41.220><c> doesn't</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
with a bicycle because a bicycle doesn't
 

00:01:41.520 --> 00:01:43.429 align:start position:0%
with a bicycle because a bicycle doesn't
have<00:01:41.640><c> an</c><00:01:41.759><c> engine</c><00:01:41.880><c> so</c><00:01:42.360><c> the</c><00:01:42.600><c> behavior</c><00:01:43.020><c> isn't</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
have an engine so the behavior isn't
 

00:01:43.439 --> 00:01:46.130 align:start position:0%
have an engine so the behavior isn't
going<00:01:43.619><c> to</c><00:01:43.799><c> be</c><00:01:43.920><c> the</c><00:01:44.159><c> same</c><00:01:44.280><c> now</c><00:01:44.700><c> yes</c><00:01:45.119><c> I</c><00:01:45.600><c> have</c><00:01:45.840><c> in</c>

00:01:46.130 --> 00:01:46.140 align:start position:0%
going to be the same now yes I have in
 

00:01:46.140 --> 00:01:47.870 align:start position:0%
going to be the same now yes I have in
here<00:01:46.259><c> I'm</c><00:01:46.560><c> throwing</c><00:01:46.860><c> a</c><00:01:47.040><c> new</c><00:01:47.220><c> unsupported</c>

00:01:47.870 --> 00:01:47.880 align:start position:0%
here I'm throwing a new unsupported
 

00:01:47.880 --> 00:01:49.670 align:start position:0%
here I'm throwing a new unsupported
operation<00:01:48.180><c> exception</c><00:01:48.720><c> it</c><00:01:48.960><c> doesn't</c><00:01:49.140><c> matter</c><00:01:49.380><c> if</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
operation exception it doesn't matter if
 

00:01:49.680 --> 00:01:51.469 align:start position:0%
operation exception it doesn't matter if
you<00:01:49.799><c> have</c><00:01:49.920><c> a</c><00:01:50.040><c> system.print</c><00:01:50.759><c> out</c><00:01:50.939><c> bicycle</c>

00:01:51.469 --> 00:01:51.479 align:start position:0%
you have a system.print out bicycle
 

00:01:51.479 --> 00:01:53.330 align:start position:0%
you have a system.print out bicycle
doesn't<00:01:51.659><c> have</c><00:01:51.899><c> an</c><00:01:52.020><c> engine</c><00:01:52.140><c> or</c><00:01:52.680><c> whatever</c><00:01:52.979><c> you</c>

00:01:53.330 --> 00:01:53.340 align:start position:0%
doesn't have an engine or whatever you
 

00:01:53.340 --> 00:01:55.969 align:start position:0%
doesn't have an engine or whatever you
say<00:01:53.520><c> the</c><00:01:54.000><c> idea</c><00:01:54.360><c> is</c><00:01:54.659><c> that</c><00:01:55.140><c> we</c><00:01:55.320><c> can't</c><00:01:55.439><c> substitute</c>

00:01:55.969 --> 00:01:55.979 align:start position:0%
say the idea is that we can't substitute
 

00:01:55.979 --> 00:01:58.190 align:start position:0%
say the idea is that we can't substitute
a<00:01:56.100><c> bicycle</c><00:01:56.460><c> for</c><00:01:56.939><c> a</c><00:01:57.119><c> vehicle</c><00:01:57.479><c> because</c><00:01:57.960><c> a</c>

00:01:58.190 --> 00:01:58.200 align:start position:0%
a bicycle for a vehicle because a
 

00:01:58.200 --> 00:02:00.530 align:start position:0%
a bicycle for a vehicle because a
bicycle<00:01:58.560><c> doesn't</c><00:01:58.920><c> have</c><00:01:59.159><c> an</c><00:01:59.399><c> engine</c><00:01:59.579><c> okay</c><00:02:00.119><c> so</c>

00:02:00.530 --> 00:02:00.540 align:start position:0%
bicycle doesn't have an engine okay so
 

00:02:00.540 --> 00:02:01.670 align:start position:0%
bicycle doesn't have an engine okay so
how<00:02:00.780><c> are</c><00:02:00.960><c> we</c><00:02:01.020><c> going</c><00:02:01.140><c> to</c><00:02:01.200><c> solve</c><00:02:01.439><c> this</c><00:02:01.500><c> well</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
how are we going to solve this well
 

00:02:01.680 --> 00:02:02.929 align:start position:0%
how are we going to solve this well
there's<00:02:01.920><c> a</c><00:02:02.040><c> couple</c><00:02:02.100><c> different</c><00:02:02.280><c> ways</c><00:02:02.579><c> the</c><00:02:02.820><c> way</c>

00:02:02.929 --> 00:02:02.939 align:start position:0%
there's a couple different ways the way
 

00:02:02.939 --> 00:02:04.370 align:start position:0%
there's a couple different ways the way
that<00:02:03.119><c> I'm</c><00:02:03.240><c> going</c><00:02:03.360><c> to</c><00:02:03.479><c> be</c><00:02:03.540><c> doing</c><00:02:03.720><c> it</c><00:02:03.899><c> is</c><00:02:04.140><c> I'm</c>

00:02:04.370 --> 00:02:04.380 align:start position:0%
that I'm going to be doing it is I'm
 

00:02:04.380 --> 00:02:05.209 align:start position:0%
that I'm going to be doing it is I'm
going<00:02:04.500><c> to</c><00:02:04.619><c> have</c><00:02:04.740><c> a</c><00:02:04.860><c> couple</c><00:02:04.979><c> different</c>

00:02:05.209 --> 00:02:05.219 align:start position:0%
going to have a couple different
 

00:02:05.219 --> 00:02:07.609 align:start position:0%
going to have a couple different
interfaces<00:02:05.880><c> and</c><00:02:06.479><c> each</c><00:02:06.780><c> interface</c><00:02:07.200><c> is</c><00:02:07.500><c> going</c>

00:02:07.609 --> 00:02:07.619 align:start position:0%
interfaces and each interface is going
 

00:02:07.619 --> 00:02:09.290 align:start position:0%
interfaces and each interface is going
to<00:02:07.740><c> do</c><00:02:07.860><c> something</c><00:02:08.039><c> different</c><00:02:08.340><c> so</c><00:02:08.759><c> one</c><00:02:09.060><c> might</c>

00:02:09.290 --> 00:02:09.300 align:start position:0%
to do something different so one might
 

00:02:09.300 --> 00:02:10.969 align:start position:0%
to do something different so one might
start<00:02:09.479><c> anything</c><00:02:09.720><c> that</c><00:02:09.959><c> has</c><00:02:10.080><c> an</c><00:02:10.200><c> engine</c><00:02:10.380><c> and</c>

00:02:10.969 --> 00:02:10.979 align:start position:0%
start anything that has an engine and
 

00:02:10.979 --> 00:02:12.830 align:start position:0%
start anything that has an engine and
another<00:02:11.160><c> just</c><00:02:11.580><c> my</c><00:02:11.700><c> sort</c><00:02:12.000><c> of</c><00:02:12.120><c> General</c><00:02:12.420><c> vehicle</c>

00:02:12.830 --> 00:02:12.840 align:start position:0%
another just my sort of General vehicle
 

00:02:12.840 --> 00:02:14.930 align:start position:0%
another just my sort of General vehicle
and<00:02:13.260><c> this</c><00:02:13.560><c> method</c><00:02:13.800><c> of</c><00:02:13.980><c> solving</c><00:02:14.280><c> it</c><00:02:14.459><c> is</c><00:02:14.700><c> also</c>

00:02:14.930 --> 00:02:14.940 align:start position:0%
and this method of solving it is also
 

00:02:14.940 --> 00:02:16.550 align:start position:0%
and this method of solving it is also
used<00:02:15.120><c> in</c><00:02:15.300><c> another</c><00:02:15.480><c> principle</c><00:02:15.900><c> that</c><00:02:16.140><c> I'll</c><00:02:16.379><c> get</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
used in another principle that I'll get
 

00:02:16.560 --> 00:02:17.930 align:start position:0%
used in another principle that I'll get
to<00:02:16.680><c> soon</c><00:02:16.920><c> alright</c><00:02:17.280><c> so</c><00:02:17.400><c> let's</c><00:02:17.520><c> begin</c><00:02:17.640><c> making</c>

00:02:17.930 --> 00:02:17.940 align:start position:0%
to soon alright so let's begin making
 

00:02:17.940 --> 00:02:19.490 align:start position:0%
to soon alright so let's begin making
the<00:02:18.180><c> interfaces</c><00:02:18.660><c> so</c><00:02:18.959><c> we're</c><00:02:19.080><c> gonna</c><00:02:19.140><c> have</c><00:02:19.319><c> two</c>

00:02:19.490 --> 00:02:19.500 align:start position:0%
the interfaces so we're gonna have two
 

00:02:19.500 --> 00:02:22.390 align:start position:0%
the interfaces so we're gonna have two
of<00:02:19.620><c> them</c><00:02:19.800><c> the</c><00:02:20.340><c> first</c><00:02:20.459><c> one</c><00:02:20.580><c> will</c><00:02:20.819><c> be</c><00:02:20.940><c> called</c>

00:02:22.390 --> 00:02:22.400 align:start position:0%
of them the first one will be called
 

00:02:22.400 --> 00:02:24.050 align:start position:0%
of them the first one will be called
non-engine

00:02:24.050 --> 00:02:24.060 align:start position:0%
non-engine
 

00:02:24.060 --> 00:02:26.869 align:start position:0%
non-engine
powered<00:02:25.020><c> vehicle</c><00:02:25.920><c> and</c><00:02:26.280><c> this</c><00:02:26.400><c> is</c><00:02:26.520><c> simply</c><00:02:26.760><c> going</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
powered vehicle and this is simply going
 

00:02:26.879 --> 00:02:30.050 align:start position:0%
powered vehicle and this is simply going
to<00:02:27.000><c> have</c><00:02:27.120><c> one</c><00:02:27.239><c> method</c><00:02:27.660><c> public</c><00:02:28.200><c> void</c>

00:02:30.050 --> 00:02:30.060 align:start position:0%
to have one method public void
 

00:02:30.060 --> 00:02:32.570 align:start position:0%
to have one method public void
start<00:02:30.540><c> vehicle</c>

00:02:32.570 --> 00:02:32.580 align:start position:0%
start vehicle
 

00:02:32.580 --> 00:02:35.030 align:start position:0%
start vehicle
we're<00:02:32.940><c> gonna</c><00:02:33.120><c> create</c><00:02:33.300><c> another</c><00:02:33.420><c> one</c>

00:02:35.030 --> 00:02:35.040 align:start position:0%
we're gonna create another one
 

00:02:35.040 --> 00:02:38.830 align:start position:0%
we're gonna create another one
this<00:02:35.520><c> one's</c><00:02:35.700><c> going</c><00:02:35.879><c> to</c><00:02:36.000><c> be</c><00:02:36.120><c> an</c><00:02:36.720><c> engine</c><00:02:37.760><c> powered</c>

00:02:38.830 --> 00:02:38.840 align:start position:0%
this one's going to be an engine powered
 

00:02:38.840 --> 00:02:41.270 align:start position:0%
this one's going to be an engine powered
vehicle<00:02:39.840><c> and</c><00:02:40.500><c> this</c><00:02:40.739><c> is</c><00:02:40.800><c> also</c><00:02:40.980><c> going</c><00:02:41.099><c> to</c><00:02:41.220><c> have</c>

00:02:41.270 --> 00:02:41.280 align:start position:0%
vehicle and this is also going to have
 

00:02:41.280 --> 00:02:42.589 align:start position:0%
vehicle and this is also going to have
one<00:02:41.400><c> method</c>

00:02:42.589 --> 00:02:42.599 align:start position:0%
one method
 

00:02:42.599 --> 00:02:46.009 align:start position:0%
one method
public<00:02:43.080><c> void</c><00:02:43.860><c> start</c><00:02:44.280><c> engine</c>

00:02:46.009 --> 00:02:46.019 align:start position:0%
public void start engine
 

00:02:46.019 --> 00:02:48.710 align:start position:0%
public void start engine
okay<00:02:46.440><c> so</c><00:02:46.680><c> now</c><00:02:46.800><c> what</c><00:02:46.980><c> we</c><00:02:47.160><c> can</c><00:02:47.280><c> do</c><00:02:47.400><c> is</c><00:02:48.180><c> we</c><00:02:48.540><c> can</c>

00:02:48.710 --> 00:02:48.720 align:start position:0%
okay so now what we can do is we can
 

00:02:48.720 --> 00:02:51.110 align:start position:0%
okay so now what we can do is we can
have<00:02:48.959><c> multiple</c><00:02:49.920><c> different</c><00:02:50.099><c> classes</c><00:02:50.640><c> but</c>

00:02:51.110 --> 00:02:51.120 align:start position:0%
have multiple different classes but
 

00:02:51.120 --> 00:02:53.210 align:start position:0%
have multiple different classes but
we're<00:02:51.540><c> going</c><00:02:51.720><c> to</c><00:02:51.780><c> recreate</c><00:02:52.200><c> the</c><00:02:52.500><c> bicycle</c><00:02:52.980><c> in</c>

00:02:53.210 --> 00:02:53.220 align:start position:0%
we're going to recreate the bicycle in
 

00:02:53.220 --> 00:02:55.190 align:start position:0%
we're going to recreate the bicycle in
the<00:02:53.400><c> car</c><00:02:53.519><c> except</c><00:02:54.000><c> this</c><00:02:54.300><c> time</c><00:02:54.480><c> the</c><00:02:54.840><c> car</c><00:02:54.959><c> is</c>

00:02:55.190 --> 00:02:55.200 align:start position:0%
the car except this time the car is
 

00:02:55.200 --> 00:02:56.630 align:start position:0%
the car except this time the car is
going<00:02:55.379><c> to</c><00:02:55.440><c> implement</c><00:02:55.680><c> the</c><00:02:55.980><c> engine</c><00:02:56.099><c> powered</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
going to implement the engine powered
 

00:02:56.640 --> 00:02:58.610 align:start position:0%
going to implement the engine powered
vehicle<00:02:56.940><c> and</c><00:02:57.540><c> the</c><00:02:57.720><c> bicycle</c><00:02:58.019><c> is</c><00:02:58.260><c> going</c><00:02:58.440><c> to</c>

00:02:58.610 --> 00:02:58.620 align:start position:0%
vehicle and the bicycle is going to
 

00:02:58.620 --> 00:03:01.009 align:start position:0%
vehicle and the bicycle is going to
implement<00:02:59.160><c> the</c><00:02:59.580><c> non-engine</c><00:03:00.239><c> powered</c><00:03:00.660><c> vehicle</c>

00:03:01.009 --> 00:03:01.019 align:start position:0%
implement the non-engine powered vehicle
 

00:03:01.019 --> 00:03:03.050 align:start position:0%
implement the non-engine powered vehicle
so<00:03:01.319><c> let</c><00:03:01.440><c> me</c><00:03:01.560><c> code</c><00:03:01.680><c> that</c><00:03:01.860><c> real</c><00:03:02.040><c> quick</c><00:03:02.160><c> okay</c><00:03:02.760><c> so</c>

00:03:03.050 --> 00:03:03.060 align:start position:0%
so let me code that real quick okay so
 

00:03:03.060 --> 00:03:04.190 align:start position:0%
so let me code that real quick okay so
now<00:03:03.180><c> we</c><00:03:03.360><c> have</c><00:03:03.480><c> both</c><00:03:03.599><c> of</c><00:03:03.660><c> our</c><00:03:03.780><c> implementations</c>

00:03:04.190 --> 00:03:04.200 align:start position:0%
now we have both of our implementations
 

00:03:04.200 --> 00:03:06.110 align:start position:0%
now we have both of our implementations
the<00:03:04.500><c> bicycle</c><00:03:04.800><c> and</c><00:03:05.040><c> the</c><00:03:05.160><c> car</c><00:03:05.280><c> as</c><00:03:05.760><c> you</c><00:03:05.879><c> can</c><00:03:06.000><c> see</c>

00:03:06.110 --> 00:03:06.120 align:start position:0%
the bicycle and the car as you can see
 

00:03:06.120 --> 00:03:07.790 align:start position:0%
the bicycle and the car as you can see
here<00:03:06.239><c> the</c><00:03:06.599><c> bicycle</c><00:03:06.900><c> implements</c><00:03:07.560><c> the</c>

00:03:07.790 --> 00:03:07.800 align:start position:0%
here the bicycle implements the
 

00:03:07.800 --> 00:03:09.770 align:start position:0%
here the bicycle implements the
non-engine<00:03:08.400><c> powered</c><00:03:08.819><c> vehicle</c><00:03:09.180><c> and</c><00:03:09.599><c> this</c>

00:03:09.770 --> 00:03:09.780 align:start position:0%
non-engine powered vehicle and this
 

00:03:09.780 --> 00:03:11.270 align:start position:0%
non-engine powered vehicle and this
works<00:03:10.200><c> because</c><00:03:10.379><c> we're</c><00:03:10.680><c> just</c><00:03:10.920><c> starting</c><00:03:11.099><c> a</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
works because we're just starting a
 

00:03:11.280 --> 00:03:12.649 align:start position:0%
works because we're just starting a
vehicle<00:03:11.580><c> now</c><00:03:11.819><c> we're</c><00:03:12.060><c> not</c><00:03:12.239><c> saying</c><00:03:12.540><c> we're</c>

00:03:12.649 --> 00:03:12.659 align:start position:0%
vehicle now we're not saying we're
 

00:03:12.659 --> 00:03:14.149 align:start position:0%
vehicle now we're not saying we're
starting<00:03:12.840><c> an</c><00:03:13.140><c> engine</c><00:03:13.319><c> and</c><00:03:13.860><c> this</c><00:03:14.040><c> is</c>

00:03:14.149 --> 00:03:14.159 align:start position:0%
starting an engine and this is
 

00:03:14.159 --> 00:03:16.250 align:start position:0%
starting an engine and this is
preserving<00:03:14.700><c> the</c><00:03:15.000><c> behavior</c><00:03:15.360><c> of</c><00:03:15.659><c> the</c><00:03:15.840><c> interface</c>

00:03:16.250 --> 00:03:16.260 align:start position:0%
preserving the behavior of the interface
 

00:03:16.260 --> 00:03:18.589 align:start position:0%
preserving the behavior of the interface
and<00:03:16.860><c> now</c><00:03:16.980><c> we</c><00:03:17.099><c> have</c><00:03:17.220><c> the</c><00:03:17.400><c> car</c><00:03:17.640><c> which</c><00:03:18.120><c> implements</c>

00:03:18.589 --> 00:03:18.599 align:start position:0%
and now we have the car which implements
 

00:03:18.599 --> 00:03:20.270 align:start position:0%
and now we have the car which implements
the<00:03:18.720><c> engine</c><00:03:18.840><c> powered</c><00:03:19.319><c> vehicle</c><00:03:19.680><c> and</c><00:03:20.099><c> this</c>

00:03:20.270 --> 00:03:20.280 align:start position:0%
the engine powered vehicle and this
 

00:03:20.280 --> 00:03:21.830 align:start position:0%
the engine powered vehicle and this
makes<00:03:20.459><c> sense</c><00:03:20.700><c> because</c><00:03:20.940><c> it</c><00:03:21.239><c> implements</c><00:03:21.659><c> the</c>

00:03:21.830 --> 00:03:21.840 align:start position:0%
makes sense because it implements the
 

00:03:21.840 --> 00:03:23.930 align:start position:0%
makes sense because it implements the
method<00:03:22.260><c> star</c><00:03:22.560><c> engine</c><00:03:22.860><c> now</c><00:03:23.459><c> we</c><00:03:23.640><c> could</c><00:03:23.760><c> also</c>

00:03:23.930 --> 00:03:23.940 align:start position:0%
method star engine now we could also
 

00:03:23.940 --> 00:03:25.190 align:start position:0%
method star engine now we could also
have<00:03:24.000><c> a</c><00:03:24.120><c> truck</c><00:03:24.239><c> which</c><00:03:24.540><c> implements</c><00:03:24.900><c> the</c><00:03:25.080><c> engine</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
have a truck which implements the engine
 

00:03:25.200 --> 00:03:26.690 align:start position:0%
have a truck which implements the engine
powered<00:03:25.620><c> vehicle</c><00:03:25.920><c> and</c><00:03:26.220><c> that</c><00:03:26.400><c> would</c><00:03:26.519><c> make</c>

00:03:26.690 --> 00:03:26.700 align:start position:0%
powered vehicle and that would make
 

00:03:26.700 --> 00:03:28.729 align:start position:0%
powered vehicle and that would make
sense<00:03:26.879><c> because</c><00:03:27.239><c> you</c><00:03:27.900><c> would</c><00:03:28.019><c> use</c><00:03:28.260><c> the</c><00:03:28.560><c> start</c>

00:03:28.729 --> 00:03:28.739 align:start position:0%
sense because you would use the start
 

00:03:28.739 --> 00:03:31.550 align:start position:0%
sense because you would use the start
engine<00:03:29.099><c> method</c><00:03:29.700><c> on</c><00:03:30.239><c> a</c><00:03:30.420><c> truck</c><00:03:30.599><c> now</c><00:03:31.260><c> we're</c><00:03:31.440><c> not</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
engine method on a truck now we're not
 

00:03:31.560 --> 00:03:32.930 align:start position:0%
engine method on a truck now we're not
quite<00:03:31.680><c> done</c><00:03:31.920><c> yet</c><00:03:32.099><c> there</c><00:03:32.400><c> are</c><00:03:32.519><c> a</c><00:03:32.580><c> few</c><00:03:32.700><c> other</c>

00:03:32.930 --> 00:03:32.940 align:start position:0%
quite done yet there are a few other
 

00:03:32.940 --> 00:03:34.369 align:start position:0%
quite done yet there are a few other
things<00:03:33.180><c> that</c><00:03:33.480><c> you</c><00:03:33.599><c> should</c><00:03:33.780><c> know</c><00:03:33.900><c> for</c><00:03:34.140><c> the</c><00:03:34.260><c> list</c>

00:03:34.369 --> 00:03:34.379 align:start position:0%
things that you should know for the list
 

00:03:34.379 --> 00:03:36.290 align:start position:0%
things that you should know for the list
golf<00:03:34.620><c> substitution</c><00:03:35.159><c> principle</c><00:03:35.580><c> in</c><00:03:36.180><c> their</c>

00:03:36.290 --> 00:03:36.300 align:start position:0%
golf substitution principle in their
 

00:03:36.300 --> 00:03:38.149 align:start position:0%
golf substitution principle in their
book<00:03:36.540><c> program</c><00:03:37.080><c> development</c><00:03:37.620><c> in</c><00:03:37.800><c> Java</c>

00:03:38.149 --> 00:03:38.159 align:start position:0%
book program development in Java
 

00:03:38.159 --> 00:03:40.309 align:start position:0%
book program development in Java
abstraction<00:03:38.879><c> specification</c><00:03:39.720><c> and</c><00:03:40.019><c> object</c>

00:03:40.309 --> 00:03:40.319 align:start position:0%
abstraction specification and object
 

00:03:40.319 --> 00:03:43.009 align:start position:0%
abstraction specification and object
oriented<00:03:40.799><c> design</c><00:03:40.980><c> uh</c><00:03:41.940><c> liskov</c><00:03:42.360><c> and</c><00:03:42.540><c> gutag</c>

00:03:43.009 --> 00:03:43.019 align:start position:0%
oriented design uh liskov and gutag
 

00:03:43.019 --> 00:03:44.330 align:start position:0%
oriented design uh liskov and gutag
describe<00:03:43.500><c> the</c><00:03:43.680><c> violations</c><00:03:43.980><c> of</c><00:03:44.220><c> this</c>

00:03:44.330 --> 00:03:44.340 align:start position:0%
describe the violations of this
 

00:03:44.340 --> 00:03:45.770 align:start position:0%
describe the violations of this
principle<00:03:44.700><c> and</c><00:03:45.120><c> to</c><00:03:45.299><c> be</c><00:03:45.420><c> honest</c><00:03:45.599><c> there</c><00:03:45.720><c> are</c>

00:03:45.770 --> 00:03:45.780 align:start position:0%
principle and to be honest there are
 

00:03:45.780 --> 00:03:48.170 align:start position:0%
principle and to be honest there are
quite<00:03:45.900><c> a</c><00:03:45.959><c> a</c><00:03:46.440><c> few</c><00:03:46.620><c> of</c><00:03:46.739><c> them</c><00:03:46.920><c> and</c><00:03:47.640><c> they</c><00:03:47.819><c> can</c><00:03:47.940><c> be</c><00:03:48.060><c> a</c>

00:03:48.170 --> 00:03:48.180 align:start position:0%
quite a a few of them and they can be a
 

00:03:48.180 --> 00:03:50.149 align:start position:0%
quite a a few of them and they can be a
little<00:03:48.239><c> wordy</c><00:03:48.780><c> to</c><00:03:49.080><c> understand</c><00:03:49.200><c> it</c><00:03:49.560><c> first</c><00:03:49.739><c> so</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
little wordy to understand it first so
 

00:03:50.159 --> 00:03:51.410 align:start position:0%
little wordy to understand it first so
I'm<00:03:50.220><c> going</c><00:03:50.400><c> to</c><00:03:50.459><c> try</c><00:03:50.580><c> and</c><00:03:50.700><c> make</c><00:03:50.819><c> it</c><00:03:51.000><c> simpler</c><00:03:51.299><c> to</c>

00:03:51.410 --> 00:03:51.420 align:start position:0%
I'm going to try and make it simpler to
 

00:03:51.420 --> 00:03:52.850 align:start position:0%
I'm going to try and make it simpler to
understand<00:03:51.480><c> and</c><00:03:51.840><c> go</c><00:03:51.959><c> over</c><00:03:52.080><c> the</c><00:03:52.319><c> main</c><00:03:52.500><c> ones</c>

00:03:52.850 --> 00:03:52.860 align:start position:0%
understand and go over the main ones
 

00:03:52.860 --> 00:03:54.470 align:start position:0%
understand and go over the main ones
that<00:03:53.099><c> I</c><00:03:53.280><c> think</c><00:03:53.400><c> are</c><00:03:53.700><c> important</c><00:03:54.060><c> and</c><00:03:54.299><c> that</c><00:03:54.420><c> you</c>

00:03:54.470 --> 00:03:54.480 align:start position:0%
that I think are important and that you
 

00:03:54.480 --> 00:03:56.210 align:start position:0%
that I think are important and that you
should<00:03:54.659><c> know</c><00:03:54.780><c> a</c><00:03:55.200><c> subclass</c><00:03:55.680><c> should</c><00:03:55.980><c> require</c>

00:03:56.210 --> 00:03:56.220 align:start position:0%
should know a subclass should require
 

00:03:56.220 --> 00:03:59.509 align:start position:0%
should know a subclass should require
nothing<00:03:56.819><c> more</c><00:03:57.299><c> and</c><00:03:57.780><c> promise</c><00:03:58.140><c> nothing</c><00:03:58.379><c> less</c><00:03:58.739><c> no</c>

00:03:59.509 --> 00:03:59.519 align:start position:0%
nothing more and promise nothing less no
 

00:03:59.519 --> 00:04:00.890 align:start position:0%
nothing more and promise nothing less no
that's<00:03:59.760><c> not</c><00:03:59.940><c> a</c><00:04:00.060><c> riddle</c><00:04:00.420><c> even</c><00:04:00.540><c> though</c><00:04:00.720><c> it</c><00:04:00.780><c> kind</c>

00:04:00.890 --> 00:04:00.900 align:start position:0%
that's not a riddle even though it kind
 

00:04:00.900 --> 00:04:02.690 align:start position:0%
that's not a riddle even though it kind
of<00:04:01.019><c> sounds</c><00:04:01.140><c> like</c><00:04:01.379><c> it</c><00:04:01.500><c> but</c><00:04:01.799><c> let's</c><00:04:01.920><c> say</c><00:04:02.099><c> we</c><00:04:02.459><c> have</c>

00:04:02.690 --> 00:04:02.700 align:start position:0%
of sounds like it but let's say we have
 

00:04:02.700 --> 00:04:05.570 align:start position:0%
of sounds like it but let's say we have
a<00:04:03.180><c> base</c><00:04:03.480><c> class</c><00:04:03.780><c> that</c><00:04:04.019><c> has</c><00:04:04.260><c> a</c><00:04:04.379><c> method</c><00:04:04.739><c> that</c><00:04:05.459><c> will</c>

00:04:05.570 --> 00:04:05.580 align:start position:0%
a base class that has a method that will
 

00:04:05.580 --> 00:04:08.089 align:start position:0%
a base class that has a method that will
print<00:04:05.819><c> out</c><00:04:06.120><c> any</c><00:04:06.480><c> numbers</c><00:04:06.780><c> less</c><00:04:07.260><c> than</c><00:04:07.440><c> 100</c><00:04:07.620><c> and</c>

00:04:08.089 --> 00:04:08.099 align:start position:0%
print out any numbers less than 100 and
 

00:04:08.099 --> 00:04:10.550 align:start position:0%
print out any numbers less than 100 and
then<00:04:08.280><c> we</c><00:04:08.459><c> implement</c><00:04:08.940><c> or</c><00:04:09.599><c> override</c><00:04:10.019><c> the</c><00:04:10.260><c> method</c>

00:04:10.550 --> 00:04:10.560 align:start position:0%
then we implement or override the method
 

00:04:10.560 --> 00:04:12.530 align:start position:0%
then we implement or override the method
from<00:04:10.680><c> That</c><00:04:10.860><c> Base</c><00:04:10.980><c> Class</c><00:04:11.220><c> and</c><00:04:11.580><c> we</c><00:04:11.760><c> say</c><00:04:11.939><c> we</c><00:04:12.420><c> want</c>

00:04:12.530 --> 00:04:12.540 align:start position:0%
from That Base Class and we say we want
 

00:04:12.540 --> 00:04:15.229 align:start position:0%
from That Base Class and we say we want
to<00:04:12.720><c> print</c><00:04:12.840><c> out</c><00:04:12.959><c> any</c><00:04:13.260><c> numbers</c><00:04:13.439><c> from</c><00:04:14.280><c> zero</c><00:04:14.819><c> or</c>

00:04:15.229 --> 00:04:15.239 align:start position:0%
to print out any numbers from zero or
 

00:04:15.239 --> 00:04:17.090 align:start position:0%
to print out any numbers from zero or
less<00:04:15.540><c> than</c><00:04:15.720><c> 50</c><00:04:16.019><c> basically</c><00:04:16.500><c> we</c><00:04:16.680><c> want</c><00:04:16.919><c> to</c><00:04:16.919><c> print</c>

00:04:17.090 --> 00:04:17.100 align:start position:0%
less than 50 basically we want to print
 

00:04:17.100 --> 00:04:18.590 align:start position:0%
less than 50 basically we want to print
out<00:04:17.160><c> any</c><00:04:17.280><c> numbers</c><00:04:17.459><c> less</c><00:04:17.760><c> than</c><00:04:17.880><c> 50.</c><00:04:18.299><c> the</c>

00:04:18.590 --> 00:04:18.600 align:start position:0%
out any numbers less than 50. the
 

00:04:18.600 --> 00:04:21.770 align:start position:0%
out any numbers less than 50. the
behavior<00:04:19.139><c> is</c><00:04:19.440><c> changed</c><00:04:20.280><c> correct</c><00:04:20.940><c> we</c>

00:04:21.770 --> 00:04:21.780 align:start position:0%
behavior is changed correct we
 

00:04:21.780 --> 00:04:24.110 align:start position:0%
behavior is changed correct we
technically<00:04:22.199><c> changed</c><00:04:22.740><c> it</c><00:04:22.919><c> but</c><00:04:23.520><c> we're</c>

00:04:24.110 --> 00:04:24.120 align:start position:0%
technically changed it but we're
 

00:04:24.120 --> 00:04:26.689 align:start position:0%
technically changed it but we're
preserving<00:04:24.720><c> the</c><00:04:24.960><c> behavior</c><00:04:25.440><c> it</c><00:04:26.160><c> does</c><00:04:26.400><c> not</c>

00:04:26.689 --> 00:04:26.699 align:start position:0%
preserving the behavior it does not
 

00:04:26.699 --> 00:04:29.210 align:start position:0%
preserving the behavior it does not
violate<00:04:27.300><c> the</c><00:04:27.540><c> principle</c><00:04:27.900><c> all</c><00:04:28.440><c> we</c><00:04:28.620><c> said</c><00:04:28.800><c> was</c><00:04:29.040><c> in</c>

00:04:29.210 --> 00:04:29.220 align:start position:0%
violate the principle all we said was in
 

00:04:29.220 --> 00:04:30.770 align:start position:0%
violate the principle all we said was in
the<00:04:29.340><c> extended</c><00:04:29.699><c> class</c><00:04:29.880><c> we</c><00:04:30.180><c> want</c><00:04:30.360><c> any</c><00:04:30.540><c> number</c>

00:04:30.770 --> 00:04:30.780 align:start position:0%
the extended class we want any number
 

00:04:30.780 --> 00:04:33.670 align:start position:0%
the extended class we want any number
less<00:04:31.080><c> than</c><00:04:31.259><c> 50.</c><00:04:31.860><c> so</c><00:04:32.460><c> any</c><00:04:32.699><c> number</c><00:04:32.880><c> less</c><00:04:33.180><c> than</c><00:04:33.360><c> 50</c>

00:04:33.670 --> 00:04:33.680 align:start position:0%
less than 50. so any number less than 50
 

00:04:33.680 --> 00:04:36.469 align:start position:0%
less than 50. so any number less than 50
is<00:04:34.680><c> going</c><00:04:34.860><c> to</c><00:04:35.040><c> preserve</c><00:04:35.520><c> the</c><00:04:35.699><c> behavior</c><00:04:36.120><c> of</c><00:04:36.360><c> the</c>

00:04:36.469 --> 00:04:36.479 align:start position:0%
is going to preserve the behavior of the
 

00:04:36.479 --> 00:04:38.749 align:start position:0%
is going to preserve the behavior of the
base<00:04:36.660><c> class</c><00:04:36.960><c> which</c><00:04:37.740><c> said</c><00:04:37.979><c> it</c><00:04:38.220><c> needed</c><00:04:38.520><c> any</c>

00:04:38.749 --> 00:04:38.759 align:start position:0%
base class which said it needed any
 

00:04:38.759 --> 00:04:41.150 align:start position:0%
base class which said it needed any
number<00:04:39.000><c> less</c><00:04:39.300><c> than</c><00:04:39.479><c> 100</c><00:04:39.780><c> however</c><00:04:40.620><c> if</c><00:04:40.860><c> I</c><00:04:41.040><c> have</c>

00:04:41.150 --> 00:04:41.160 align:start position:0%
number less than 100 however if I have
 

00:04:41.160 --> 00:04:43.070 align:start position:0%
number less than 100 however if I have
another<00:04:41.340><c> class</c><00:04:41.759><c> that</c><00:04:42.419><c> extends</c><00:04:42.720><c> That</c><00:04:42.900><c> Base</c>

00:04:43.070 --> 00:04:43.080 align:start position:0%
another class that extends That Base
 

00:04:43.080 --> 00:04:45.590 align:start position:0%
another class that extends That Base
Class<00:04:43.380><c> and</c><00:04:43.620><c> says</c><00:04:43.979><c> now</c><00:04:44.759><c> I</c><00:04:45.000><c> want</c><00:04:45.120><c> to</c><00:04:45.240><c> have</c><00:04:45.300><c> any</c>

00:04:45.590 --> 00:04:45.600 align:start position:0%
Class and says now I want to have any
 

00:04:45.600 --> 00:04:47.570 align:start position:0%
Class and says now I want to have any
number<00:04:45.960><c> I</c><00:04:46.620><c> want</c><00:04:46.740><c> to</c><00:04:46.800><c> print</c><00:04:46.979><c> out</c><00:04:47.160><c> any</c><00:04:47.400><c> number</c>

00:04:47.570 --> 00:04:47.580 align:start position:0%
number I want to print out any number
 

00:04:47.580 --> 00:04:50.749 align:start position:0%
number I want to print out any number
less<00:04:48.180><c> than</c><00:04:48.419><c> 200</c><00:04:48.960><c> now</c><00:04:49.680><c> this</c><00:04:50.040><c> violates</c><00:04:50.520><c> it</c>

00:04:50.749 --> 00:04:50.759 align:start position:0%
less than 200 now this violates it
 

00:04:50.759 --> 00:04:53.689 align:start position:0%
less than 200 now this violates it
because<00:04:51.180><c> if</c><00:04:51.360><c> it</c><00:04:51.540><c> prints</c><00:04:51.780><c> out</c><00:04:51.900><c> 150</c><00:04:52.860><c> well</c><00:04:53.400><c> that</c>

00:04:53.689 --> 00:04:53.699 align:start position:0%
because if it prints out 150 well that
 

00:04:53.699 --> 00:04:55.909 align:start position:0%
because if it prints out 150 well that
number<00:04:53.880><c> is</c><00:04:54.120><c> not</c><00:04:54.360><c> less</c><00:04:54.780><c> than</c><00:04:55.259><c> the</c><00:04:55.500><c> Base</c><00:04:55.740><c> Class</c>

00:04:55.909 --> 00:04:55.919 align:start position:0%
number is not less than the Base Class
 

00:04:55.919 --> 00:04:58.430 align:start position:0%
number is not less than the Base Class
method<00:04:56.460><c> that</c><00:04:57.180><c> said</c><00:04:57.360><c> it</c><00:04:57.660><c> only</c><00:04:57.840><c> wants</c><00:04:58.259><c> numbers</c>

00:04:58.430 --> 00:04:58.440 align:start position:0%
method that said it only wants numbers
 

00:04:58.440 --> 00:05:01.129 align:start position:0%
method that said it only wants numbers
less<00:04:58.860><c> than</c><00:04:59.040><c> 100</c><00:04:59.280><c> okay</c><00:05:00.060><c> so</c><00:05:00.300><c> that</c><00:05:00.540><c> violates</c><00:05:00.960><c> the</c>

00:05:01.129 --> 00:05:01.139 align:start position:0%
less than 100 okay so that violates the
 

00:05:01.139 --> 00:05:03.290 align:start position:0%
less than 100 okay so that violates the
principle<00:05:01.500><c> because</c><00:05:02.040><c> we</c><00:05:02.400><c> can't</c><00:05:02.580><c> substitute</c>

00:05:03.290 --> 00:05:03.300 align:start position:0%
principle because we can't substitute
 

00:05:03.300 --> 00:05:05.990 align:start position:0%
principle because we can't substitute
that<00:05:03.600><c> class</c><00:05:03.840><c> and</c><00:05:04.500><c> press</c>

00:05:05.990 --> 00:05:06.000 align:start position:0%
that class and press
 

00:05:06.000 --> 00:05:09.050 align:start position:0%
that class and press
and<00:05:06.540><c> preserve</c><00:05:06.960><c> the</c><00:05:07.020><c> behavior</c><00:05:07.560><c> of</c><00:05:08.520><c> the</c><00:05:08.880><c> base</c>

00:05:09.050 --> 00:05:09.060 align:start position:0%
and preserve the behavior of the base
 

00:05:09.060 --> 00:05:11.689 align:start position:0%
and preserve the behavior of the base
but<00:05:09.540><c> the</c><00:05:09.660><c> next</c><00:05:09.780><c> one</c><00:05:09.960><c> is</c><00:05:10.320><c> keep</c><00:05:10.680><c> exceptions</c><00:05:11.220><c> in</c>

00:05:11.689 --> 00:05:11.699 align:start position:0%
but the next one is keep exceptions in
 

00:05:11.699 --> 00:05:14.570 align:start position:0%
but the next one is keep exceptions in
check<00:05:11.880><c> basically</c><00:05:12.780><c> don't</c><00:05:13.259><c> throw</c><00:05:13.620><c> more</c><00:05:14.400><c> general</c>

00:05:14.570 --> 00:05:14.580 align:start position:0%
check basically don't throw more general
 

00:05:14.580 --> 00:05:17.930 align:start position:0%
check basically don't throw more general
or<00:05:15.540><c> broader</c><00:05:16.080><c> exceptions</c><00:05:16.500><c> in</c><00:05:16.919><c> a</c><00:05:17.040><c> subclass</c><00:05:17.520><c> than</c>

00:05:17.930 --> 00:05:17.940 align:start position:0%
or broader exceptions in a subclass than
 

00:05:17.940 --> 00:05:20.210 align:start position:0%
or broader exceptions in a subclass than
superclass<00:05:18.540><c> if</c><00:05:18.840><c> in</c><00:05:19.020><c> a</c><00:05:19.199><c> super</c><00:05:19.320><c> class</c><00:05:19.560><c> or</c><00:05:20.100><c> the</c>

00:05:20.210 --> 00:05:20.220 align:start position:0%
superclass if in a super class or the
 

00:05:20.220 --> 00:05:22.850 align:start position:0%
superclass if in a super class or the
Base<00:05:20.340><c> Class</c><00:05:20.639><c> you</c><00:05:21.180><c> have</c><00:05:21.540><c> an</c><00:05:21.960><c> unsupported</c><00:05:22.560><c> or</c><00:05:22.740><c> an</c>

00:05:22.850 --> 00:05:22.860 align:start position:0%
Base Class you have an unsupported or an
 

00:05:22.860 --> 00:05:25.010 align:start position:0%
Base Class you have an unsupported or an
arithmetic<00:05:23.400><c> exception</c><00:05:23.880><c> in</c><00:05:24.479><c> the</c><00:05:24.660><c> subclass</c>

00:05:25.010 --> 00:05:25.020 align:start position:0%
arithmetic exception in the subclass
 

00:05:25.020 --> 00:05:27.650 align:start position:0%
arithmetic exception in the subclass
don't<00:05:25.500><c> throw</c><00:05:25.800><c> just</c><00:05:26.220><c> exception</c><00:05:26.759><c> and</c><00:05:27.360><c> for</c><00:05:27.539><c> the</c>

00:05:27.650 --> 00:05:27.660 align:start position:0%
don't throw just exception and for the
 

00:05:27.660 --> 00:05:29.689 align:start position:0%
don't throw just exception and for the
last<00:05:27.780><c> two</c><00:05:27.960><c> stick</c><00:05:28.440><c> to</c><00:05:28.620><c> the</c><00:05:28.800><c> is</c><00:05:29.039><c> a</c><00:05:29.280><c> relationship</c>

00:05:29.689 --> 00:05:29.699 align:start position:0%
last two stick to the is a relationship
 

00:05:29.699 --> 00:05:32.210 align:start position:0%
last two stick to the is a relationship
make<00:05:30.180><c> sure</c><00:05:30.360><c> the</c><00:05:30.479><c> subclass</c><00:05:31.020><c> can</c><00:05:31.500><c> be</c><00:05:31.620><c> used</c><00:05:31.800><c> as</c><00:05:32.039><c> a</c>

00:05:32.210 --> 00:05:32.220 align:start position:0%
make sure the subclass can be used as a
 

00:05:32.220 --> 00:05:33.650 align:start position:0%
make sure the subclass can be used as a
substitute<00:05:32.520><c> for</c><00:05:32.699><c> the</c><00:05:32.820><c> superclass</c><00:05:33.300><c> without</c>

00:05:33.650 --> 00:05:33.660 align:start position:0%
substitute for the superclass without
 

00:05:33.660 --> 00:05:36.050 align:start position:0%
substitute for the superclass without
causing<00:05:34.080><c> problems</c><00:05:34.500><c> and</c><00:05:35.160><c> finally</c><00:05:35.460><c> don't</c>

00:05:36.050 --> 00:05:36.060 align:start position:0%
causing problems and finally don't
 

00:05:36.060 --> 00:05:38.150 align:start position:0%
causing problems and finally don't
override<00:05:36.479><c> methods</c><00:05:37.080><c> in</c><00:05:37.199><c> a</c><00:05:37.380><c> subclass</c><00:05:37.800><c> that</c>

00:05:38.150 --> 00:05:38.160 align:start position:0%
override methods in a subclass that
 

00:05:38.160 --> 00:05:40.430 align:start position:0%
override methods in a subclass that
aren't<00:05:38.460><c> meant</c><00:05:38.820><c> to</c><00:05:39.000><c> be</c><00:05:39.120><c> overridden</c><00:05:39.840><c> in</c><00:05:40.199><c> a</c>

00:05:40.430 --> 00:05:40.440 align:start position:0%
aren't meant to be overridden in a
 

00:05:40.440 --> 00:05:42.529 align:start position:0%
aren't meant to be overridden in a
superclass<00:05:40.979><c> okay</c><00:05:41.520><c> well</c><00:05:41.880><c> we</c><00:05:42.120><c> just</c><00:05:42.240><c> went</c><00:05:42.419><c> over</c>

00:05:42.529 --> 00:05:42.539 align:start position:0%
superclass okay well we just went over
 

00:05:42.539 --> 00:05:44.629 align:start position:0%
superclass okay well we just went over
the<00:05:42.660><c> list</c><00:05:42.720><c> golf</c><00:05:42.960><c> substitution</c><00:05:43.380><c> principle</c><00:05:43.860><c> and</c>

00:05:44.629 --> 00:05:44.639 align:start position:0%
the list golf substitution principle and
 

00:05:44.639 --> 00:05:46.310 align:start position:0%
the list golf substitution principle and
this<00:05:44.880><c> isn't</c><00:05:45.180><c> the</c><00:05:45.660><c> easiest</c><00:05:45.840><c> one</c><00:05:46.020><c> to</c><00:05:46.199><c> understand</c>

00:05:46.310 --> 00:05:46.320 align:start position:0%
this isn't the easiest one to understand
 

00:05:46.320 --> 00:05:48.230 align:start position:0%
this isn't the easiest one to understand
so<00:05:46.979><c> if</c><00:05:47.340><c> you</c><00:05:47.460><c> didn't</c><00:05:47.580><c> quite</c><00:05:47.820><c> get</c><00:05:48.000><c> something</c>

00:05:48.230 --> 00:05:48.240 align:start position:0%
so if you didn't quite get something
 

00:05:48.240 --> 00:05:50.090 align:start position:0%
so if you didn't quite get something
just<00:05:48.539><c> leave</c><00:05:48.720><c> a</c><00:05:48.900><c> comment</c><00:05:49.139><c> down</c><00:05:49.380><c> there</c><00:05:49.500><c> and</c><00:05:49.860><c> I'll</c>

00:05:50.090 --> 00:05:50.100 align:start position:0%
just leave a comment down there and I'll
 

00:05:50.100 --> 00:05:51.770 align:start position:0%
just leave a comment down there and I'll
get<00:05:50.280><c> I'll</c><00:05:50.460><c> get</c><00:05:50.580><c> to</c><00:05:50.699><c> you</c><00:05:50.820><c> watch</c><00:05:51.240><c> this</c><00:05:51.419><c> video</c><00:05:51.539><c> for</c>

00:05:51.770 --> 00:05:51.780 align:start position:0%
get I'll get to you watch this video for
 

00:05:51.780 --> 00:05:53.029 align:start position:0%
get I'll get to you watch this video for
the<00:05:51.900><c> previous</c><00:05:51.960><c> one</c><00:05:52.259><c> if</c><00:05:52.440><c> you</c><00:05:52.560><c> haven't</c><00:05:52.680><c> seen</c><00:05:52.919><c> it</c>

00:05:53.029 --> 00:05:53.039 align:start position:0%
the previous one if you haven't seen it
 

00:05:53.039 --> 00:05:55.940 align:start position:0%
the previous one if you haven't seen it
and<00:05:53.280><c> I'll</c><00:05:53.460><c> see</c><00:05:53.580><c> you</c><00:05:53.699><c> next</c><00:05:53.820><c> time</c>

