WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.149 align:start position:0%
 
in<00:00:00.480><c> previous</c><00:00:00.719><c> video</c><00:00:01.140><c> we</c><00:00:01.680><c> looked</c><00:00:01.979><c> at</c>

00:00:02.149 --> 00:00:02.159 align:start position:0%
in previous video we looked at
 

00:00:02.159 --> 00:00:04.970 align:start position:0%
in previous video we looked at
Stanford's<00:00:02.879><c> alpaca</c><00:00:03.480><c> which</c><00:00:03.959><c> is</c><00:00:04.080><c> a</c><00:00:04.319><c> fine</c><00:00:04.440><c> tuning</c>

00:00:04.970 --> 00:00:04.980 align:start position:0%
Stanford's alpaca which is a fine tuning
 

00:00:04.980 --> 00:00:07.309 align:start position:0%
Stanford's alpaca which is a fine tuning
of<00:00:05.460><c> the</c><00:00:06.000><c> Llama</c><00:00:06.359><c> model</c><00:00:06.600><c> and</c><00:00:06.839><c> they've</c><00:00:07.020><c> got</c><00:00:07.140><c> some</c>

00:00:07.309 --> 00:00:07.319 align:start position:0%
of the Llama model and they've got some
 

00:00:07.319 --> 00:00:09.049 align:start position:0%
of the Llama model and they've got some
great<00:00:07.560><c> descriptions</c><00:00:08.099><c> about</c><00:00:08.340><c> how</c><00:00:08.700><c> they</c><00:00:08.880><c> made</c>

00:00:09.049 --> 00:00:09.059 align:start position:0%
great descriptions about how they made
 

00:00:09.059 --> 00:00:13.190 align:start position:0%
great descriptions about how they made
it<00:00:09.720><c> Etc</c><00:00:09.900><c> in</c><00:00:10.260><c> here</c><00:00:10.440><c> they</c><00:00:10.980><c> also</c><00:00:11.280><c> put</c><00:00:11.519><c> up</c><00:00:11.780><c> a</c><00:00:12.780><c> nice</c>

00:00:13.190 --> 00:00:13.200 align:start position:0%
it Etc in here they also put up a nice
 

00:00:13.200 --> 00:00:14.930 align:start position:0%
it Etc in here they also put up a nice
place<00:00:13.500><c> where</c><00:00:13.799><c> you</c><00:00:13.920><c> can</c><00:00:14.040><c> try</c><00:00:14.219><c> it</c><00:00:14.400><c> out</c><00:00:14.519><c> which</c><00:00:14.700><c> I</c>

00:00:14.930 --> 00:00:14.940 align:start position:0%
place where you can try it out which I
 

00:00:14.940 --> 00:00:17.150 align:start position:0%
place where you can try it out which I
talked<00:00:15.120><c> about</c><00:00:15.240><c> in</c><00:00:15.599><c> the</c><00:00:15.780><c> previous</c><00:00:15.900><c> video</c><00:00:16.320><c> on</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
talked about in the previous video on
 

00:00:17.160 --> 00:00:18.710 align:start position:0%
talked about in the previous video on
top<00:00:17.279><c> of</c><00:00:17.400><c> all</c><00:00:17.580><c> this</c><00:00:17.760><c> they've</c><00:00:18.119><c> released</c><00:00:18.420><c> a</c><00:00:18.539><c> bunch</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
top of all this they've released a bunch
 

00:00:18.720 --> 00:00:20.210 align:start position:0%
top of all this they've released a bunch
of<00:00:18.840><c> code</c><00:00:19.140><c> for</c><00:00:19.500><c> it</c>

00:00:20.210 --> 00:00:20.220 align:start position:0%
of code for it
 

00:00:20.220 --> 00:00:21.590 align:start position:0%
of code for it
because<00:00:20.580><c> of</c><00:00:20.820><c> this</c><00:00:20.939><c> there</c><00:00:21.240><c> are</c><00:00:21.359><c> a</c><00:00:21.420><c> number</c><00:00:21.539><c> of</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
because of this there are a number of
 

00:00:21.600 --> 00:00:23.630 align:start position:0%
because of this there are a number of
people<00:00:21.720><c> sort</c><00:00:22.080><c> of</c><00:00:22.199><c> fine-tuning</c><00:00:22.800><c> this</c><00:00:22.980><c> now</c><00:00:23.279><c> I</c>

00:00:23.630 --> 00:00:23.640 align:start position:0%
people sort of fine-tuning this now I
 

00:00:23.640 --> 00:00:26.150 align:start position:0%
people sort of fine-tuning this now I
will<00:00:23.820><c> make</c><00:00:23.939><c> a</c><00:00:24.180><c> video</c><00:00:24.359><c> about</c><00:00:25.260><c> fine-tuning</c><00:00:26.039><c> it</c>

00:00:26.150 --> 00:00:26.160 align:start position:0%
will make a video about fine-tuning it
 

00:00:26.160 --> 00:00:28.670 align:start position:0%
will make a video about fine-tuning it
but<00:00:26.519><c> if</c><00:00:26.760><c> you</c><00:00:26.880><c> want</c><00:00:27.119><c> to</c><00:00:27.300><c> play</c><00:00:27.599><c> around</c><00:00:27.840><c> with</c><00:00:28.500><c> it</c>

00:00:28.670 --> 00:00:28.680 align:start position:0%
but if you want to play around with it
 

00:00:28.680 --> 00:00:31.070 align:start position:0%
but if you want to play around with it
here<00:00:29.340><c> is</c><00:00:29.519><c> a</c><00:00:29.820><c> version</c><00:00:30.000><c> of</c><00:00:30.300><c> it</c><00:00:30.420><c> that</c><00:00:30.779><c> I've</c>

00:00:31.070 --> 00:00:31.080 align:start position:0%
here is a version of it that I've
 

00:00:31.080 --> 00:00:33.770 align:start position:0%
here is a version of it that I've
fine-tuned<00:00:31.740><c> using</c><00:00:32.220><c> the</c><00:00:32.520><c> Laura</c><00:00:33.000><c> low</c><00:00:33.420><c> rank</c>

00:00:33.770 --> 00:00:33.780 align:start position:0%
fine-tuned using the Laura low rank
 

00:00:33.780 --> 00:00:36.170 align:start position:0%
fine-tuned using the Laura low rank
adaption<00:00:34.620><c> and</c><00:00:34.800><c> stuff</c><00:00:34.980><c> so</c><00:00:35.520><c> here's</c><00:00:35.880><c> the</c><00:00:35.940><c> collab</c>

00:00:36.170 --> 00:00:36.180 align:start position:0%
adaption and stuff so here's the collab
 

00:00:36.180 --> 00:00:37.790 align:start position:0%
adaption and stuff so here's the collab
you<00:00:36.420><c> can</c><00:00:36.480><c> basically</c><00:00:36.719><c> just</c><00:00:36.899><c> run</c><00:00:37.020><c> it</c><00:00:37.200><c> it</c><00:00:37.559><c> will</c>

00:00:37.790 --> 00:00:37.800 align:start position:0%
you can basically just run it it will
 

00:00:37.800 --> 00:00:41.030 align:start position:0%
you can basically just run it it will
actually<00:00:38.100><c> do</c><00:00:38.399><c> inference</c><00:00:38.940><c> with</c><00:00:39.239><c> a</c><00:00:39.420><c> T4</c><00:00:39.899><c> so</c><00:00:40.680><c> as</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
actually do inference with a T4 so as
 

00:00:41.040 --> 00:00:43.069 align:start position:0%
actually do inference with a T4 so as
long<00:00:41.160><c> as</c><00:00:41.280><c> you've</c><00:00:41.520><c> got</c><00:00:41.640><c> a</c><00:00:42.180><c> even</c><00:00:42.420><c> a</c><00:00:42.660><c> very</c><00:00:42.840><c> low</c>

00:00:43.069 --> 00:00:43.079 align:start position:0%
long as you've got a even a very low
 

00:00:43.079 --> 00:00:45.530 align:start position:0%
long as you've got a even a very low
level<00:00:43.320><c> GPU</c><00:00:44.219><c> this</c><00:00:44.460><c> should</c><00:00:44.640><c> be</c><00:00:44.760><c> enough</c><00:00:45.059><c> for</c>

00:00:45.530 --> 00:00:45.540 align:start position:0%
level GPU this should be enough for
 

00:00:45.540 --> 00:00:48.470 align:start position:0%
level GPU this should be enough for
doing<00:00:45.719><c> inference</c><00:00:46.379><c> so</c><00:00:47.160><c> basically</c><00:00:47.700><c> this</c><00:00:48.300><c> is</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
doing inference so basically this is
 

00:00:48.480 --> 00:00:50.810 align:start position:0%
doing inference so basically this is
making<00:00:48.780><c> use</c><00:00:49.260><c> of</c><00:00:49.739><c> some</c><00:00:50.039><c> weights</c><00:00:50.460><c> that</c><00:00:50.579><c> are</c><00:00:50.700><c> on</c>

00:00:50.810 --> 00:00:50.820 align:start position:0%
making use of some weights that are on
 

00:00:50.820 --> 00:00:53.510 align:start position:0%
making use of some weights that are on
hugging<00:00:51.239><c> face</c><00:00:51.480><c> for</c><00:00:51.960><c> the</c><00:00:52.200><c> Llama</c><00:00:52.500><c> model</c><00:00:52.920><c> they've</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
hugging face for the Llama model they've
 

00:00:53.520 --> 00:00:55.310 align:start position:0%
hugging face for the Llama model they've
stayed<00:00:53.820><c> up</c><00:00:54.120><c> there</c><00:00:54.300><c> so</c><00:00:54.660><c> this</c><00:00:54.899><c> is</c><00:00:54.960><c> why</c><00:00:55.199><c> I'm</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
stayed up there so this is why I'm
 

00:00:55.320 --> 00:00:57.650 align:start position:0%
stayed up there so this is why I'm
comfortable<00:00:55.739><c> actually</c><00:00:56.460><c> releasing</c><00:00:57.180><c> this</c><00:00:57.420><c> now</c>

00:00:57.650 --> 00:00:57.660 align:start position:0%
comfortable actually releasing this now
 

00:00:57.660 --> 00:01:00.110 align:start position:0%
comfortable actually releasing this now
I'm<00:00:58.199><c> not</c><00:00:58.379><c> sure</c><00:00:58.620><c> why</c><00:00:58.980><c> they</c><00:00:59.280><c> haven't</c><00:00:59.520><c> been</c><00:00:59.760><c> taken</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
I'm not sure why they haven't been taken
 

00:01:00.120 --> 00:01:01.970 align:start position:0%
I'm not sure why they haven't been taken
down<00:01:00.239><c> but</c><00:01:00.480><c> they've</c><00:01:00.840><c> stayed</c><00:01:01.079><c> up</c><00:01:01.199><c> there</c><00:01:01.440><c> and</c>

00:01:01.970 --> 00:01:01.980 align:start position:0%
down but they've stayed up there and
 

00:01:01.980 --> 00:01:04.009 align:start position:0%
down but they've stayed up there and
because<00:01:02.520><c> of</c><00:01:02.699><c> that</c><00:01:02.820><c> we</c><00:01:03.120><c> can</c><00:01:03.239><c> fine</c><00:01:03.539><c> tune</c><00:01:03.780><c> this</c>

00:01:04.009 --> 00:01:04.019 align:start position:0%
because of that we can fine tune this
 

00:01:04.019 --> 00:01:05.870 align:start position:0%
because of that we can fine tune this
I've<00:01:04.320><c> done</c><00:01:04.500><c> a</c><00:01:04.619><c> fine</c><00:01:04.739><c> tuning</c><00:01:05.220><c> of</c><00:01:05.460><c> this</c><00:01:05.700><c> for</c>

00:01:05.870 --> 00:01:05.880 align:start position:0%
I've done a fine tuning of this for
 

00:01:05.880 --> 00:01:08.510 align:start position:0%
I've done a fine tuning of this for
myself<00:01:06.119><c> and</c><00:01:06.840><c> so</c><00:01:07.140><c> this</c><00:01:07.380><c> will</c><00:01:07.560><c> basically</c><00:01:07.979><c> load</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
myself and so this will basically load
 

00:01:08.520 --> 00:01:10.550 align:start position:0%
myself and so this will basically load
the<00:01:08.640><c> original</c><00:01:08.820><c> llama</c><00:01:09.299><c> weights</c><00:01:09.780><c> and</c><00:01:10.140><c> then</c><00:01:10.260><c> the</c>

00:01:10.550 --> 00:01:10.560 align:start position:0%
the original llama weights and then the
 

00:01:10.560 --> 00:01:12.770 align:start position:0%
the original llama weights and then the
Laura<00:01:10.979><c> fine</c><00:01:11.220><c> tuning</c><00:01:11.700><c> that</c><00:01:11.880><c> I've</c><00:01:12.060><c> done</c><00:01:12.360><c> here</c>

00:01:12.770 --> 00:01:12.780 align:start position:0%
Laura fine tuning that I've done here
 

00:01:12.780 --> 00:01:14.870 align:start position:0%
Laura fine tuning that I've done here
and<00:01:13.200><c> you</c><00:01:13.320><c> can</c><00:01:13.439><c> then</c><00:01:13.619><c> basically</c><00:01:14.040><c> just</c><00:01:14.340><c> open</c><00:01:14.580><c> it</c>

00:01:14.870 --> 00:01:14.880 align:start position:0%
and you can then basically just open it
 

00:01:14.880 --> 00:01:18.170 align:start position:0%
and you can then basically just open it
up<00:01:15.060><c> and</c><00:01:15.960><c> run</c><00:01:16.260><c> it</c><00:01:16.439><c> and</c><00:01:16.680><c> go</c><00:01:16.860><c> through</c><00:01:17.100><c> it</c><00:01:17.280><c> and</c><00:01:17.939><c> you</c>

00:01:18.170 --> 00:01:18.180 align:start position:0%
up and run it and go through it and you
 

00:01:18.180 --> 00:01:19.850 align:start position:0%
up and run it and go through it and you
can<00:01:18.299><c> see</c><00:01:18.420><c> that</c><00:01:18.659><c> the</c><00:01:18.900><c> output</c><00:01:19.260><c> of</c><00:01:19.439><c> it</c><00:01:19.619><c> is</c>

00:01:19.850 --> 00:01:19.860 align:start position:0%
can see that the output of it is
 

00:01:19.860 --> 00:01:22.789 align:start position:0%
can see that the output of it is
actually<00:01:20.159><c> be</c><00:01:20.580><c> pretty</c><00:01:20.820><c> good</c><00:01:21.119><c> right</c><00:01:21.780><c> you</c><00:01:22.140><c> can</c><00:01:22.380><c> go</c>

00:01:22.789 --> 00:01:22.799 align:start position:0%
actually be pretty good right you can go
 

00:01:22.799 --> 00:01:24.890 align:start position:0%
actually be pretty good right you can go
back<00:01:22.979><c> to</c><00:01:23.280><c> the</c><00:01:23.520><c> code</c><00:01:23.820><c> to</c><00:01:24.180><c> see</c><00:01:24.360><c> the</c><00:01:24.720><c> different</c>

00:01:24.890 --> 00:01:24.900 align:start position:0%
back to the code to see the different
 

00:01:24.900 --> 00:01:26.510 align:start position:0%
back to the code to see the different
the<00:01:25.439><c> different</c><00:01:25.619><c> instructions</c><00:01:26.159><c> and</c><00:01:26.340><c> stuff</c>

00:01:26.510 --> 00:01:26.520 align:start position:0%
the different instructions and stuff
 

00:01:26.520 --> 00:01:27.890 align:start position:0%
the different instructions and stuff
like<00:01:26.640><c> that</c><00:01:26.759><c> they</c><00:01:26.939><c> use</c><00:01:27.180><c> so</c><00:01:27.420><c> you</c><00:01:27.540><c> can</c><00:01:27.659><c> certainly</c>

00:01:27.890 --> 00:01:27.900 align:start position:0%
like that they use so you can certainly
 

00:01:27.900 --> 00:01:29.690 align:start position:0%
like that they use so you can certainly
play<00:01:28.140><c> around</c><00:01:28.320><c> with</c><00:01:28.740><c> that</c><00:01:28.979><c> but</c><00:01:29.400><c> you</c><00:01:29.580><c> can</c><00:01:29.580><c> see</c>

00:01:29.690 --> 00:01:29.700 align:start position:0%
play around with that but you can see
 

00:01:29.700 --> 00:01:31.310 align:start position:0%
play around with that but you can see
here<00:01:29.939><c> okay</c><00:01:30.119><c> we're</c><00:01:30.360><c> asking</c><00:01:30.720><c> it</c><00:01:30.840><c> what</c><00:01:31.200><c> are</c>

00:01:31.310 --> 00:01:31.320 align:start position:0%
here okay we're asking it what are
 

00:01:31.320 --> 00:01:32.870 align:start position:0%
here okay we're asking it what are
alpacas<00:01:31.860><c> and</c><00:01:32.100><c> how</c><00:01:32.220><c> are</c><00:01:32.340><c> they</c><00:01:32.460><c> different</c><00:01:32.580><c> to</c>

00:01:32.870 --> 00:01:32.880 align:start position:0%
alpacas and how are they different to
 

00:01:32.880 --> 00:01:35.450 align:start position:0%
alpacas and how are they different to
llama<00:01:33.180><c> so</c><00:01:33.479><c> if</c><00:01:33.600><c> we</c><00:01:33.780><c> compare</c><00:01:34.320><c> this</c><00:01:34.560><c> to</c><00:01:34.920><c> the</c>

00:01:35.450 --> 00:01:35.460 align:start position:0%
llama so if we compare this to the
 

00:01:35.460 --> 00:01:36.950 align:start position:0%
llama so if we compare this to the
original<00:01:35.880><c> one</c>

00:01:36.950 --> 00:01:36.960 align:start position:0%
original one
 

00:01:36.960 --> 00:01:38.390 align:start position:0%
original one
we<00:01:37.500><c> can</c><00:01:37.560><c> see</c><00:01:37.680><c> that</c><00:01:37.799><c> we're</c><00:01:37.979><c> not</c><00:01:38.280><c> getting</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
we can see that we're not getting
 

00:01:38.400 --> 00:01:39.770 align:start position:0%
we can see that we're not getting
exactly<00:01:38.820><c> the</c><00:01:38.939><c> same</c><00:01:39.119><c> answer</c><00:01:39.299><c> but</c><00:01:39.600><c> we're</c>

00:01:39.770 --> 00:01:39.780 align:start position:0%
exactly the same answer but we're
 

00:01:39.780 --> 00:01:41.690 align:start position:0%
exactly the same answer but we're
definitely<00:01:40.020><c> getting</c><00:01:40.200><c> something</c><00:01:40.740><c> similar</c><00:01:41.400><c> and</c>

00:01:41.690 --> 00:01:41.700 align:start position:0%
definitely getting something similar and
 

00:01:41.700 --> 00:01:43.370 align:start position:0%
definitely getting something similar and
we're<00:01:41.820><c> getting</c><00:01:42.060><c> something</c><00:01:42.360><c> in</c><00:01:42.840><c> the</c><00:01:43.140><c> right</c>

00:01:43.370 --> 00:01:43.380 align:start position:0%
we're getting something in the right
 

00:01:43.380 --> 00:01:45.830 align:start position:0%
we're getting something in the right
range<00:01:43.920><c> for</c><00:01:44.400><c> these</c><00:01:44.640><c> and</c><00:01:45.000><c> I</c><00:01:45.119><c> found</c><00:01:45.299><c> the</c><00:01:45.600><c> the</c>

00:01:45.830 --> 00:01:45.840 align:start position:0%
range for these and I found the the
 

00:01:45.840 --> 00:01:47.510 align:start position:0%
range for these and I found the the
answers<00:01:46.200><c> that</c><00:01:46.560><c> it</c><00:01:46.680><c> gives</c><00:01:46.799><c> or</c><00:01:47.040><c> the</c><00:01:47.159><c> text</c><00:01:47.280><c> that</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
answers that it gives or the text that
 

00:01:47.520 --> 00:01:49.190 align:start position:0%
answers that it gives or the text that
it's<00:01:47.640><c> generating</c><00:01:48.000><c> is</c><00:01:48.299><c> actually</c><00:01:48.479><c> very</c><00:01:48.900><c> very</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
it's generating is actually very very
 

00:01:49.200 --> 00:01:51.830 align:start position:0%
it's generating is actually very very
impressive<00:01:49.799><c> to</c><00:01:50.220><c> be</c><00:01:50.340><c> honest</c><00:01:50.640><c> so</c><00:01:51.299><c> he</c><00:01:51.540><c> can</c><00:01:51.720><c> see</c>

00:01:51.830 --> 00:01:51.840 align:start position:0%
impressive to be honest so he can see
 

00:01:51.840 --> 00:01:53.270 align:start position:0%
impressive to be honest so he can see
another<00:01:52.200><c> one</c><00:01:52.380><c> where</c><00:01:52.619><c> I've</c><00:01:52.799><c> basically</c><00:01:53.100><c> put</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
another one where I've basically put
 

00:01:53.280 --> 00:01:55.789 align:start position:0%
another one where I've basically put
writing<00:01:54.060><c> Ode</c><00:01:54.060><c> to</c><00:01:54.299><c> why</c><00:01:54.600><c> alpacas</c><00:01:55.200><c> make</c><00:01:55.439><c> the</c><00:01:55.619><c> best</c>

00:01:55.789 --> 00:01:55.799 align:start position:0%
writing Ode to why alpacas make the best
 

00:01:55.799 --> 00:01:58.850 align:start position:0%
writing Ode to why alpacas make the best
pet<00:01:56.100><c> and</c><00:01:56.579><c> it's</c><00:01:57.180><c> able</c><00:01:57.420><c> to</c><00:01:57.540><c> come</c><00:01:57.720><c> up</c><00:01:57.899><c> with</c><00:01:58.320><c> some</c>

00:01:58.850 --> 00:01:58.860 align:start position:0%
pet and it's able to come up with some
 

00:01:58.860 --> 00:02:01.310 align:start position:0%
pet and it's able to come up with some
nice<00:01:59.340><c> Pros</c><00:01:59.880><c> for</c><00:02:00.180><c> this</c><00:02:00.540><c> kind</c><00:02:00.659><c> of</c><00:02:00.780><c> thing</c><00:02:00.899><c> another</c>

00:02:01.310 --> 00:02:01.320 align:start position:0%
nice Pros for this kind of thing another
 

00:02:01.320 --> 00:02:04.190 align:start position:0%
nice Pros for this kind of thing another
one<00:02:01.560><c> I</c><00:02:01.860><c> put</c><00:02:02.340><c> in</c><00:02:02.520><c> there</c><00:02:02.700><c> was</c><00:02:03.000><c> write</c><00:02:03.299><c> an</c><00:02:03.540><c> email</c><00:02:03.780><c> to</c>

00:02:04.190 --> 00:02:04.200 align:start position:0%
one I put in there was write an email to
 

00:02:04.200 --> 00:02:06.950 align:start position:0%
one I put in there was write an email to
open<00:02:04.380><c> AI</c><00:02:04.860><c> saying</c><00:02:05.340><c> why</c><00:02:05.520><c> GPT</c><00:02:05.939><c> should</c><00:02:06.420><c> be</c><00:02:06.600><c> open</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
open AI saying why GPT should be open
 

00:02:06.960 --> 00:02:08.870 align:start position:0%
open AI saying why GPT should be open
source<00:02:07.560><c> and</c><00:02:07.860><c> you</c><00:02:07.979><c> can</c><00:02:08.099><c> see</c><00:02:08.220><c> here</c><00:02:08.399><c> and</c><00:02:08.580><c> all</c><00:02:08.759><c> I've</c>

00:02:08.870 --> 00:02:08.880 align:start position:0%
source and you can see here and all I've
 

00:02:08.880 --> 00:02:10.609 align:start position:0%
source and you can see here and all I've
done<00:02:09.060><c> is</c><00:02:09.179><c> just</c><00:02:09.360><c> copy</c><00:02:09.720><c> this</c><00:02:10.020><c> so</c><00:02:10.259><c> that</c><00:02:10.440><c> it's</c>

00:02:10.609 --> 00:02:10.619 align:start position:0%
done is just copy this so that it's
 

00:02:10.619 --> 00:02:13.250 align:start position:0%
done is just copy this so that it's
easier<00:02:11.039><c> for</c><00:02:11.520><c> us</c><00:02:11.640><c> to</c><00:02:11.819><c> read</c><00:02:12.060><c> so</c><00:02:12.720><c> it</c><00:02:12.840><c> comes</c><00:02:13.200><c> up</c>

00:02:13.250 --> 00:02:13.260 align:start position:0%
easier for us to read so it comes up
 

00:02:13.260 --> 00:02:15.830 align:start position:0%
easier for us to read so it comes up
with<00:02:13.500><c> you</c><00:02:13.739><c> sir</c><00:02:14.040><c> Madam</c><00:02:14.459><c> opening</c><00:02:14.879><c> up</c><00:02:15.000><c> gpt4</c><00:02:15.660><c> to</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
with you sir Madam opening up gpt4 to
 

00:02:15.840 --> 00:02:17.869 align:start position:0%
with you sir Madam opening up gpt4 to
Public<00:02:16.080><c> Access</c><00:02:16.319><c> will</c><00:02:16.800><c> allow</c><00:02:17.160><c> researchers</c><00:02:17.700><c> and</c>

00:02:17.869 --> 00:02:17.879 align:start position:0%
Public Access will allow researchers and
 

00:02:17.879 --> 00:02:20.030 align:start position:0%
Public Access will allow researchers and
developers<00:02:18.360><c> from</c><00:02:19.020><c> around</c><00:02:19.140><c> the</c><00:02:19.379><c> world</c><00:02:19.500><c> to</c><00:02:19.860><c> use</c>

00:02:20.030 --> 00:02:20.040 align:start position:0%
developers from around the world to use
 

00:02:20.040 --> 00:02:22.670 align:start position:0%
developers from around the world to use
it<00:02:20.280><c> for</c><00:02:20.700><c> their</c><00:02:21.000><c> own</c><00:02:21.239><c> projects</c><00:02:21.840><c> in</c><00:02:22.260><c> order</c><00:02:22.379><c> to</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
it for their own projects in order to
 

00:02:22.680 --> 00:02:24.830 align:start position:0%
it for their own projects in order to
advance<00:02:22.860><c> artificial</c><00:02:23.640><c> intelligence</c><00:02:24.180><c> and</c><00:02:24.720><c> it</c>

00:02:24.830 --> 00:02:24.840 align:start position:0%
advance artificial intelligence and it
 

00:02:24.840 --> 00:02:27.110 align:start position:0%
advance artificial intelligence and it
just<00:02:25.020><c> keeps</c><00:02:25.319><c> going</c><00:02:25.440><c> a</c><00:02:25.980><c> very</c><00:02:26.400><c> coherent</c><00:02:26.940><c> text</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
just keeps going a very coherent text
 

00:02:27.120 --> 00:02:29.809 align:start position:0%
just keeps going a very coherent text
very<00:02:27.840><c> on</c><00:02:28.260><c> point</c><00:02:28.500><c> if</c><00:02:28.800><c> you've</c><00:02:28.980><c> got</c><00:02:29.220><c> some</c><00:02:29.580><c> good</c>

00:02:29.809 --> 00:02:29.819 align:start position:0%
very on point if you've got some good
 

00:02:29.819 --> 00:02:33.770 align:start position:0%
very on point if you've got some good
settings<00:02:30.180><c> up</c><00:02:31.020><c> here</c><00:02:31.400><c> for</c><00:02:32.400><c> the</c><00:02:32.879><c> temperature</c><00:02:33.180><c> for</c>

00:02:33.770 --> 00:02:33.780 align:start position:0%
settings up here for the temperature for
 

00:02:33.780 --> 00:02:36.050 align:start position:0%
settings up here for the temperature for
the<00:02:33.900><c> repetition</c><00:02:34.319><c> penalty</c><00:02:35.160><c> Etc</c><00:02:35.280><c> you'll</c><00:02:35.819><c> find</c>

00:02:36.050 --> 00:02:36.060 align:start position:0%
the repetition penalty Etc you'll find
 

00:02:36.060 --> 00:02:38.930 align:start position:0%
the repetition penalty Etc you'll find
that<00:02:36.420><c> it</c><00:02:36.840><c> generates</c><00:02:37.560><c> very</c><00:02:37.920><c> nice</c><00:02:38.160><c> coherent</c>

00:02:38.930 --> 00:02:38.940 align:start position:0%
that it generates very nice coherent
 

00:02:38.940 --> 00:02:41.390 align:start position:0%
that it generates very nice coherent
text<00:02:39.180><c> and</c><00:02:39.959><c> you</c><00:02:40.260><c> can</c><00:02:40.319><c> play</c><00:02:40.500><c> around</c><00:02:40.739><c> with</c><00:02:40.980><c> it</c><00:02:41.160><c> I</c>

00:02:41.390 --> 00:02:41.400 align:start position:0%
text and you can play around with it I
 

00:02:41.400 --> 00:02:43.309 align:start position:0%
text and you can play around with it I
found<00:02:41.580><c> also</c><00:02:41.879><c> that</c><00:02:42.120><c> it</c><00:02:42.300><c> can</c><00:02:42.420><c> generate</c><00:02:42.900><c> code</c>

00:02:43.309 --> 00:02:43.319 align:start position:0%
found also that it can generate code
 

00:02:43.319 --> 00:02:45.350 align:start position:0%
found also that it can generate code
pretty<00:02:43.800><c> well</c><00:02:44.040><c> for</c><00:02:44.400><c> certain</c><00:02:44.580><c> things</c><00:02:44.879><c> for</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
pretty well for certain things for
 

00:02:45.360 --> 00:02:47.089 align:start position:0%
pretty well for certain things for
simple<00:02:45.660><c> python</c><00:02:46.200><c> functions</c><00:02:46.620><c> and</c><00:02:46.800><c> stuff</c><00:02:46.980><c> like</c>

00:02:47.089 --> 00:02:47.099 align:start position:0%
simple python functions and stuff like
 

00:02:47.099 --> 00:02:49.850 align:start position:0%
simple python functions and stuff like
that<00:02:47.280><c> it</c><00:02:47.580><c> can</c><00:02:47.760><c> do</c><00:02:48.060><c> a</c><00:02:48.660><c> nice</c><00:02:48.840><c> job</c><00:02:49.080><c> it</c><00:02:49.379><c> comes</c><00:02:49.739><c> up</c>

00:02:49.850 --> 00:02:49.860 align:start position:0%
that it can do a nice job it comes up
 

00:02:49.860 --> 00:02:51.350 align:start position:0%
that it can do a nice job it comes up
with<00:02:49.980><c> some</c><00:02:50.040><c> quite</c><00:02:50.280><c> funny</c><00:02:50.459><c> things</c><00:02:50.700><c> here's</c><00:02:51.180><c> one</c>

00:02:51.350 --> 00:02:51.360 align:start position:0%
with some quite funny things here's one
 

00:02:51.360 --> 00:02:53.270 align:start position:0%
with some quite funny things here's one
write<00:02:51.840><c> a</c><00:02:52.080><c> convincing</c><00:02:52.440><c> message</c><00:02:52.680><c> about</c><00:02:52.920><c> how</c>

00:02:53.270 --> 00:02:53.280 align:start position:0%
write a convincing message about how
 

00:02:53.280 --> 00:02:56.290 align:start position:0%
write a convincing message about how
alpacas<00:02:53.940><c> are</c><00:02:54.180><c> the</c><00:02:54.300><c> real</c><00:02:54.480><c> brains</c><00:02:54.959><c> behind</c><00:02:55.319><c> gpt5</c>

00:02:56.290 --> 00:02:56.300 align:start position:0%
alpacas are the real brains behind gpt5
 

00:02:56.300 --> 00:02:58.790 align:start position:0%
alpacas are the real brains behind gpt5
although<00:02:57.300><c> it</c><00:02:57.480><c> may</c><00:02:57.599><c> seem</c><00:02:57.900><c> like</c><00:02:58.080><c> humans</c><00:02:58.500><c> have</c>

00:02:58.790 --> 00:02:58.800 align:start position:0%
although it may seem like humans have
 

00:02:58.800 --> 00:03:01.190 align:start position:0%
although it may seem like humans have
all<00:02:59.099><c> of</c><00:02:59.220><c> the</c><00:02:59.400><c> intelligence</c><00:02:59.900><c> alpacas</c><00:03:00.900><c> actually</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
all of the intelligence alpacas actually
 

00:03:01.200 --> 00:03:03.290 align:start position:0%
all of the intelligence alpacas actually
possess<00:03:01.920><c> much</c><00:03:02.220><c> more</c><00:03:02.459><c> advanced</c><00:03:02.819><c> cognitive</c>

00:03:03.290 --> 00:03:03.300 align:start position:0%
possess much more advanced cognitive
 

00:03:03.300 --> 00:03:05.930 align:start position:0%
possess much more advanced cognitive
abilities<00:03:03.900><c> than</c><00:03:04.319><c> we</c><00:03:04.500><c> do</c><00:03:04.680><c> so</c><00:03:05.040><c> it's</c><00:03:05.400><c> it's</c><00:03:05.640><c> very</c>

00:03:05.930 --> 00:03:05.940 align:start position:0%
abilities than we do so it's it's very
 

00:03:05.940 --> 00:03:08.390 align:start position:0%
abilities than we do so it's it's very
fun<00:03:06.300><c> to</c><00:03:06.599><c> play</c><00:03:06.660><c> with</c><00:03:06.900><c> and</c><00:03:07.140><c> try</c><00:03:07.379><c> it</c><00:03:07.620><c> out</c><00:03:07.739><c> it</c><00:03:08.220><c> can</c>

00:03:08.390 --> 00:03:08.400 align:start position:0%
fun to play with and try it out it can
 

00:03:08.400 --> 00:03:10.970 align:start position:0%
fun to play with and try it out it can
even<00:03:08.519><c> do</c><00:03:08.760><c> things</c><00:03:08.940><c> like</c><00:03:09.120><c> tell</c><00:03:09.360><c> jokes</c><00:03:10.019><c> so</c><00:03:10.620><c> you</c>

00:03:10.970 --> 00:03:10.980 align:start position:0%
even do things like tell jokes so you
 

00:03:10.980 --> 00:03:12.410 align:start position:0%
even do things like tell jokes so you
can<00:03:11.040><c> have</c><00:03:11.340><c> a</c><00:03:11.459><c> look</c><00:03:11.580><c> at</c><00:03:11.700><c> it</c><00:03:11.879><c> at</c><00:03:12.120><c> the</c><00:03:12.239><c> different</c>

00:03:12.410 --> 00:03:12.420 align:start position:0%
can have a look at it at the different
 

00:03:12.420 --> 00:03:14.630 align:start position:0%
can have a look at it at the different
sort<00:03:12.659><c> of</c><00:03:12.780><c> joke</c><00:03:12.959><c> things</c><00:03:13.260><c> then</c><00:03:13.980><c> but</c><00:03:14.400><c> always</c>

00:03:14.630 --> 00:03:14.640 align:start position:0%
sort of joke things then but always
 

00:03:14.640 --> 00:03:16.729 align:start position:0%
sort of joke things then but always
funny<00:03:15.000><c> but</c><00:03:15.540><c> they</c><00:03:15.959><c> come</c><00:03:16.140><c> up</c><00:03:16.260><c> with</c><00:03:16.440><c> some</c>

00:03:16.729 --> 00:03:16.739 align:start position:0%
funny but they come up with some
 

00:03:16.739 --> 00:03:18.410 align:start position:0%
funny but they come up with some
interesting<00:03:17.220><c> sort</c><00:03:17.459><c> of</c><00:03:17.580><c> jokes</c><00:03:17.940><c> and</c><00:03:18.239><c> they're</c>

00:03:18.410 --> 00:03:18.420 align:start position:0%
interesting sort of jokes and they're
 

00:03:18.420 --> 00:03:19.910 align:start position:0%
interesting sort of jokes and they're
definitely<00:03:18.659><c> in</c><00:03:18.900><c> the</c><00:03:19.019><c> format</c><00:03:19.379><c> that</c><00:03:19.620><c> you</c><00:03:19.800><c> would</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
definitely in the format that you would
 

00:03:19.920 --> 00:03:22.009 align:start position:0%
definitely in the format that you would
expect<00:03:20.159><c> for</c><00:03:20.640><c> this</c><00:03:20.879><c> kind</c><00:03:21.000><c> of</c><00:03:21.120><c> thing</c><00:03:21.239><c> have</c><00:03:21.840><c> a</c>

00:03:22.009 --> 00:03:22.019 align:start position:0%
expect for this kind of thing have a
 

00:03:22.019 --> 00:03:24.290 align:start position:0%
expect for this kind of thing have a
play<00:03:22.140><c> with</c><00:03:22.319><c> this</c><00:03:22.560><c> see</c><00:03:23.159><c> you</c><00:03:23.519><c> know</c><00:03:23.580><c> how</c><00:03:23.819><c> it</c><00:03:24.000><c> goes</c>

00:03:24.290 --> 00:03:24.300 align:start position:0%
play with this see you know how it goes
 

00:03:24.300 --> 00:03:26.270 align:start position:0%
play with this see you know how it goes
any<00:03:24.540><c> questions</c><00:03:24.780><c> as</c><00:03:25.260><c> always</c><00:03:25.440><c> just</c><00:03:25.800><c> put</c><00:03:25.920><c> them</c><00:03:26.099><c> in</c>

00:03:26.270 --> 00:03:26.280 align:start position:0%
any questions as always just put them in
 

00:03:26.280 --> 00:03:28.490 align:start position:0%
any questions as always just put them in
the<00:03:26.400><c> comments</c><00:03:26.760><c> and</c><00:03:27.000><c> I'm</c><00:03:27.120><c> happy</c><00:03:27.300><c> to</c><00:03:27.540><c> answer</c><00:03:27.780><c> in</c>

00:03:28.490 --> 00:03:28.500 align:start position:0%
the comments and I'm happy to answer in
 

00:03:28.500 --> 00:03:30.050 align:start position:0%
the comments and I'm happy to answer in
the<00:03:28.620><c> next</c><00:03:28.739><c> video</c><00:03:29.040><c> I'll</c><00:03:29.519><c> walk</c><00:03:29.760><c> through</c>

00:03:30.050 --> 00:03:30.060 align:start position:0%
the next video I'll walk through
 

00:03:30.060 --> 00:03:32.869 align:start position:0%
the next video I'll walk through
actually<00:03:30.739><c> fine-tuning</c><00:03:31.739><c> an</c><00:03:32.099><c> alpaca</c><00:03:32.700><c> model</c>

00:03:32.869 --> 00:03:32.879 align:start position:0%
actually fine-tuning an alpaca model
 

00:03:32.879 --> 00:03:35.149 align:start position:0%
actually fine-tuning an alpaca model
myself<00:03:33.300><c> just</c><00:03:34.080><c> another</c><00:03:34.260><c> quick</c><00:03:34.500><c> point</c><00:03:34.680><c> before</c><00:03:34.920><c> I</c>

00:03:35.149 --> 00:03:35.159 align:start position:0%
myself just another quick point before I
 

00:03:35.159 --> 00:03:38.449 align:start position:0%
myself just another quick point before I
finish<00:03:35.280><c> up</c><00:03:35.580><c> if</c><00:03:36.239><c> you</c><00:03:36.420><c> wanted</c><00:03:36.840><c> play</c><00:03:37.800><c> with</c><00:03:38.040><c> the</c>

00:03:38.449 --> 00:03:38.459 align:start position:0%
finish up if you wanted play with the
 

00:03:38.459 --> 00:03:40.250 align:start position:0%
finish up if you wanted play with the
Llama<00:03:38.760><c> model</c><00:03:39.060><c> you</c><00:03:39.720><c> could</c><00:03:39.840><c> actually</c><00:03:40.019><c> just</c>

00:03:40.250 --> 00:03:40.260 align:start position:0%
Llama model you could actually just
 

00:03:40.260 --> 00:03:42.470 align:start position:0%
Llama model you could actually just
change<00:03:40.620><c> this</c><00:03:41.040><c> text</c><00:03:41.220><c> to</c><00:03:41.700><c> do</c><00:03:41.819><c> inference</c><00:03:42.180><c> on</c><00:03:42.360><c> the</c>

00:03:42.470 --> 00:03:42.480 align:start position:0%
change this text to do inference on the
 

00:03:42.480 --> 00:03:44.750 align:start position:0%
change this text to do inference on the
Llama<00:03:42.780><c> model</c><00:03:43.080><c> so</c><00:03:43.560><c> you</c><00:03:43.799><c> just</c><00:03:43.920><c> would</c><00:03:44.220><c> not</c><00:03:44.400><c> load</c>

00:03:44.750 --> 00:03:44.760 align:start position:0%
Llama model so you just would not load
 

00:03:44.760 --> 00:03:46.550 align:start position:0%
Llama model so you just would not load
this<00:03:45.000><c> last</c><00:03:45.239><c> bit</c><00:03:45.480><c> here</c><00:03:45.780><c> which</c><00:03:46.140><c> is</c><00:03:46.200><c> the</c><00:03:46.440><c> fine</c>

00:03:46.550 --> 00:03:46.560 align:start position:0%
this last bit here which is the fine
 

00:03:46.560 --> 00:03:48.410 align:start position:0%
this last bit here which is the fine
tuning<00:03:47.159><c> and</c><00:03:47.459><c> then</c><00:03:47.580><c> you</c><00:03:47.700><c> could</c><00:03:47.819><c> access</c><00:03:48.060><c> the</c>

00:03:48.410 --> 00:03:48.420 align:start position:0%
tuning and then you could access the
 

00:03:48.420 --> 00:03:50.089 align:start position:0%
tuning and then you could access the
Llama<00:03:48.720><c> model</c><00:03:48.959><c> you</c><00:03:49.319><c> could</c><00:03:49.379><c> use</c><00:03:49.560><c> the</c><00:03:49.799><c> same</c><00:03:49.920><c> code</c>

00:03:50.089 --> 00:03:50.099 align:start position:0%
Llama model you could use the same code
 

00:03:50.099 --> 00:03:51.949 align:start position:0%
Llama model you could use the same code
for<00:03:50.400><c> generating</c><00:03:50.879><c> and</c><00:03:51.180><c> for</c><00:03:51.299><c> trying</c><00:03:51.480><c> out</c><00:03:51.720><c> that</c>

00:03:51.949 --> 00:03:51.959 align:start position:0%
for generating and for trying out that
 

00:03:51.959 --> 00:03:53.089 align:start position:0%
for generating and for trying out that
as<00:03:52.200><c> well</c>

00:03:53.089 --> 00:03:53.099 align:start position:0%
as well
 

00:03:53.099 --> 00:03:55.250 align:start position:0%
as well
all<00:03:53.640><c> right</c><00:03:53.760><c> as</c><00:03:54.120><c> always</c><00:03:54.299><c> if</c><00:03:54.599><c> this</c><00:03:54.780><c> was</c><00:03:54.900><c> useful</c>

00:03:55.250 --> 00:03:55.260 align:start position:0%
all right as always if this was useful
 

00:03:55.260 --> 00:03:57.649 align:start position:0%
all right as always if this was useful
to<00:03:55.500><c> you</c><00:03:55.680><c> please</c><00:03:56.099><c> click</c><00:03:56.459><c> and</c><00:03:56.580><c> subscribe</c><00:03:57.120><c> and</c>

00:03:57.649 --> 00:03:57.659 align:start position:0%
to you please click and subscribe and
 

00:03:57.659 --> 00:03:59.030 align:start position:0%
to you please click and subscribe and
let<00:03:57.780><c> me</c><00:03:57.959><c> know</c><00:03:58.019><c> what</c><00:03:58.200><c> videos</c><00:03:58.379><c> you'd</c><00:03:58.799><c> like</c><00:03:58.920><c> to</c>

00:03:59.030 --> 00:03:59.040 align:start position:0%
let me know what videos you'd like to
 

00:03:59.040 --> 00:04:02.780 align:start position:0%
let me know what videos you'd like to
see<00:03:59.099><c> in</c><00:03:59.220><c> the</c><00:03:59.400><c> future</c><00:03:59.540><c> bye</c><00:04:00.540><c> for</c><00:04:00.659><c> now</c>

