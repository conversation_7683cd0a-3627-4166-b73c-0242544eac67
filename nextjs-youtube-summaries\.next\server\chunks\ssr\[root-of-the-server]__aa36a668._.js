module.exports = {

"[project]/.next-internal/server/app/video/[topic]/[videoId]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-rsc] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://nppxvpupvhszcdkspsiq.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5wcHh2cHVwdmhzemNka3Nwc2lxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTQzMzUyNzcsImV4cCI6MjAyOTkxMTI3N30.ltX4WYk2krVxnsSGOXSA72BSZCJGFhqn1X-H8fZ_q9k");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY);
}}),
"[project]/src/lib/database.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DatabaseService": (()=>DatabaseService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-rsc] (ecmascript)");
;
class DatabaseService {
    // Get all available topic tables with better error handling
    static async getTopicTables() {
        try {
            // Use the actual YouTube topic table names discovered in the database
            const youtubeTopics = [
                'youtube_artificial_intelligence',
                'youtube_sustainability',
                'youtube_startups',
                'youtube_financial_markets',
                'youtube_gme',
                'youtube_general',
                'youtube_legal',
                'youtube_renewable_energy'
            ];
            const existingTables = [];
            console.log('Checking for YouTube topic tables...');
            for (const topic of youtubeTopics){
                try {
                    const { error, count } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topic).select('id', {
                        count: 'exact',
                        head: true
                    }).limit(1);
                    if (!error) {
                        console.log(`Found table: ${topic} with ${count || 0} videos`);
                        existingTables.push({
                            id: topic,
                            table_name: topic,
                            name: topic,
                            display_name: this.formatDisplayName(topic),
                            description: `${this.formatDisplayName(topic)} videos`,
                            video_count: count || 0,
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString() // Placeholder
                        });
                    }
                } catch  {
                // Table doesn't exist, skip silently
                }
            }
            if (existingTables.length > 0) {
                return existingTables;
            }
            console.warn('No YouTube topic tables found in database, using fallback topics');
            // Return fallback topics for development
            const fallbackDate = new Date().toISOString();
            return [
                {
                    id: 'youtube_artificial_intelligence',
                    table_name: 'youtube_artificial_intelligence',
                    name: 'youtube_artificial_intelligence',
                    display_name: 'Artificial Intelligence',
                    description: 'AI and machine learning videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_sustainability',
                    table_name: 'youtube_sustainability',
                    name: 'youtube_sustainability',
                    display_name: 'Sustainability',
                    description: 'Sustainability and environment videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_startups',
                    table_name: 'youtube_startups',
                    name: 'youtube_startups',
                    display_name: 'Startups',
                    description: 'Startup and entrepreneurship videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_financial_markets',
                    table_name: 'youtube_financial_markets',
                    name: 'youtube_financial_markets',
                    display_name: 'Financial Markets',
                    description: 'Finance and market videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                }
            ];
        } catch (error) {
            console.error('Error fetching topic tables:', error);
            // Return fallback topics when database is not accessible
            const fallbackDate = new Date().toISOString();
            return [
                {
                    id: 'youtube_artificial_intelligence',
                    table_name: 'youtube_artificial_intelligence',
                    name: 'youtube_artificial_intelligence',
                    display_name: 'Artificial Intelligence',
                    description: 'AI and machine learning videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_sustainability',
                    table_name: 'youtube_sustainability',
                    name: 'youtube_sustainability',
                    display_name: 'Sustainability',
                    description: 'Sustainability and environment videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_startups',
                    table_name: 'youtube_startups',
                    name: 'youtube_startups',
                    display_name: 'Startups',
                    description: 'Startup and entrepreneurship videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                },
                {
                    id: 'youtube_financial_markets',
                    table_name: 'youtube_financial_markets',
                    name: 'youtube_financial_markets',
                    display_name: 'Financial Markets',
                    description: 'Finance and market videos',
                    video_count: 0,
                    created_at: fallbackDate,
                    updated_at: fallbackDate
                }
            ];
        }
    }
    // Helper method to format display names
    static formatDisplayName(tableName) {
        return tableName.replace('youtube_', '') // Remove youtube_ prefix
        .replace('_', ' ').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    }
    // Get all unique channels from available topic tables
    static async getChannels(topicFilter) {
        try {
            const allChannels = new Set();
            // Get all available topic tables
            const allTopicTables = await this.getTopicTables();
            // Filter topic tables if topicFilter is provided
            const topicTables = topicFilter && topicFilter.length > 0 ? allTopicTables.filter((t)=>{
                const simpleName = t.name.startsWith('youtube_') ? t.name.substring(8) : t.name;
                return topicFilter.includes(simpleName) || topicFilter.includes(t.name);
            }) : allTopicTables;
            console.log('Fetching channels from tables:', topicTables.map((t)=>t.name));
            console.log('Topic filter applied:', topicFilter);
            // Query each table for unique channels
            for (const topic of topicTables){
                try {
                    // Use distinct to get unique channel names without arbitrary limits
                    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topic.name).select('channel_name').not('channel_name', 'is', null).order('channel_name');
                    if (!error && data) {
                        console.log(`Found ${data.length} channel entries in ${topic.name}`);
                        data.forEach((item)=>{
                            if (item.channel_name && item.channel_name.trim()) {
                                allChannels.add(item.channel_name.trim());
                            }
                        });
                    } else if (error) {
                        console.warn(`Error fetching channels from ${topic.name}:`, error.message);
                    }
                } catch (tableError) {
                    console.warn(`Error accessing table ${topic.name}:`, tableError);
                // Continue with other tables
                }
            }
            const channels = Array.from(allChannels).sort();
            console.log(`Total unique channels found: ${channels.length}`);
            // If no channels found in database, return some sample channels for development
            if (channels.length === 0) {
                console.warn('No channels found in database, returning sample channels for development');
                return [
                    'Sample Tech Channel',
                    'Sample Programming Channel',
                    'Sample AI Channel',
                    'Sample Science Channel'
                ];
            }
            return channels;
        } catch (error) {
            console.error('Error fetching channels:', error);
            // Return sample channels for development when database is not accessible
            return [
                'Sample Tech Channel',
                'Sample Programming Channel',
                'Sample AI Channel',
                'Sample Science Channel'
            ];
        }
    }
    static async getFilteredVideos(topicTables, channels, limit = 20, offset = 0, startDate, endDate) {
        try {
            const allVideos = [];
            const seenVideoIds = new Set() // Track video IDs to prevent duplicates
            ;
            // Get available topic tables if not provided
            const tablesToQuery = topicTables && topicTables.length > 0 ? topicTables : (await this.getTopicTables()).map((t)=>t.table_name); // Use table_name
            // Calculate how many videos to fetch from each table to account for deduplication
            // When we have filters (channels, dates), we need to be more generous with the limit
            // to ensure we get enough results after filtering and deduplication
            const hasFilters = channels && channels.length > 0 || startDate || endDate;
            const multiplier = hasFilters ? 3 : 2 // Be more generous when filtering
            ;
            const perTableLimit = Math.max((limit + offset) * multiplier, 200);
            // Query each table
            for (const tableName of tablesToQuery){
                try {
                    let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(tableName).select('*').order('published_at', {
                        ascending: false
                    });
                    // Filter by channels if provided
                    if (channels && channels.length > 0) {
                        query = query.in('channel_name', channels);
                    }
                    // Filter by date range if provided
                    if (startDate) {
                        query = query.gte('published_at', startDate);
                    }
                    if (endDate) {
                        query = query.lte('published_at', endDate);
                    }
                    const { data, error } = await query.limit(perTableLimit);
                    if (!error && data) {
                        // Add videos, but only if we haven't seen this video_id before
                        for (const video of data){
                            if (!seenVideoIds.has(video.video_id)) {
                                seenVideoIds.add(video.video_id);
                                allVideos.push(video);
                            }
                        }
                    }
                } catch (tableError) {
                    console.warn(`Error fetching videos from ${tableName}:`, tableError);
                // Continue with other tables
                }
            }
            // Sort all videos by published date (most recent first)
            const sortedVideos = allVideos.sort((a, b)=>{
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            });
            // Apply pagination after sorting and deduplication
            return sortedVideos.slice(offset, offset + limit);
        } catch (error) {
            console.error('Error fetching filtered videos:', error);
            return [];
        }
    }
    // Get videos from a specific topic table
    static async getVideosByTopic(topicTable, limit = 20, offset = 0) {
        try {
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(topicTable).select('*').order('published_at', {
                ascending: false
            }).range(offset, offset + limit - 1);
            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error(`Error fetching videos from ${topicTable}:`, error);
            return [];
        }
    }
    // Get a single video by ID from a specific topic table or across all tables
    static async getVideoById(topicNameFromUrl, videoId) {
        let video = null;
        let attemptedSpecificTable = false;
        let specificTableQueryError = null;
        // Validate topicNameFromUrl and videoId
        if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {
            console.error(`getVideoById: Called with invalid videoId: '${videoId}'. Cannot fetch video.`);
            return null;
        }
        if (topicNameFromUrl && typeof topicNameFromUrl === 'string' && topicNameFromUrl.trim() !== '') {
            const actualTableName = topicNameFromUrl.startsWith('youtube_') ? topicNameFromUrl : `youtube_${topicNameFromUrl}`;
            console.log(`getVideoById: Attempting to fetch videoId '${videoId}' from specific table '${actualTableName}' (original topic: '${topicNameFromUrl}')`);
            attemptedSpecificTable = true;
            try {
                const { data, error: supabaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(actualTableName).select('*').eq('video_id', videoId).single();
                if (supabaseError) {
                    specificTableQueryError = supabaseError; // Store error for logging
                    console.warn(`getVideoById: Supabase error fetching videoId '${videoId}' from table '${actualTableName}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}. Will try searching all tables.`);
                // Do not throw here, let it fall through to findVideoAcrossAllTables logic
                } else if (data) {
                    console.log(`getVideoById: Successfully found videoId '${videoId}' in table '${actualTableName}'`);
                    const simpleTopicName = actualTableName.startsWith('youtube_') ? actualTableName.substring(8) : actualTableName;
                    // Ensure llm_response is at least an empty object if null/undefined from DB
                    video = {
                        ...data,
                        topic_category: simpleTopicName,
                        llm_response: data.llm_response || {}
                    };
                } else {
                    // This case (no error, no data with .single()) should ideally not happen as .single() errors out.
                    console.warn(`getVideoById: No data and no error for videoId '${videoId}' from table '${actualTableName}'. This is unexpected with .single(). Will try searching all tables.`);
                }
            } catch (catchedError) {
                specificTableQueryError = catchedError;
                const errorMessage = catchedError instanceof Error ? catchedError.message : String(catchedError);
                console.error(`getVideoById: Exception during fetch from specific table '${actualTableName}' for videoId '${videoId}'. Error: ${errorMessage}. Will try searching all tables.`);
            }
        } else {
            console.warn(`getVideoById: Called with invalid or empty topicNameFromUrl ('${topicNameFromUrl}'). Proceeding directly to search across all tables for videoId: '${videoId}'.`);
        }
        // If video not found in specific table (or specific table was not attempted/valid)
        if (!video) {
            if (attemptedSpecificTable) {
                let errMessage = "No specific error object";
                if (specificTableQueryError) {
                    if (specificTableQueryError instanceof Error) {
                        errMessage = specificTableQueryError.message;
                    } else {
                        try {
                            errMessage = JSON.stringify(specificTableQueryError);
                        } catch  {
                            errMessage = "Could not stringify error object";
                        }
                    }
                }
                console.warn(`getVideoById: VideoId '${videoId}' not found in specific table or an error occurred. Specific error (if any): ${errMessage}`);
            }
            console.log(`getVideoById: Falling back to findVideoAcrossAllTables for videoId '${videoId}'.`);
            try {
                const videoFromFallback = await this.findVideoAcrossAllTables(videoId);
                if (videoFromFallback) {
                    console.log(`getVideoById: Found videoId '${videoId}' via findVideoAcrossAllTables.`);
                    // Ensure llm_response is at least an empty object if null/undefined from DB
                    video = {
                        ...videoFromFallback,
                        llm_response: videoFromFallback.llm_response || {}
                    };
                } else {
                    console.error(`getVideoById: VideoId '${videoId}' ultimately NOT FOUND even after searching all tables.`);
                }
            } catch (fallbackError) {
                const fallbackErrorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
                console.error(`getVideoById: CRITICAL ERROR during findVideoAcrossAllTables for videoId '${videoId}'. Error: ${fallbackErrorMessage}`);
            // video remains null
            }
        }
        if (!video) {
            const specificErrorMsg = specificTableQueryError instanceof Error ? specificTableQueryError.message : specificTableQueryError ? "Error object present" : "N/A";
            console.error(`[FINAL RESULT] getVideoById for videoId '${videoId}' (original topic: '${topicNameFromUrl}'): Video NOT FOUND. Specific table attempt error (if any): ${specificErrorMsg}`);
        } else {
            // Ensure topic_category is sensible
            if (!video.topic_category && topicNameFromUrl) {
                video.topic_category = topicNameFromUrl.replace('youtube_', '');
            } else if (!video.topic_category) {
                video.topic_category = 'unknown'; // Default if no topic info at all
            }
        }
        return video;
    }
    // Find a video by ID across all topic tables
    static async findVideoAcrossAllTables(videoId) {
        console.log(`findVideoAcrossAllTables: Searching for videoId '${videoId}'`);
        if (!videoId || typeof videoId !== 'string' || videoId.trim() === '') {
            console.error(`findVideoAcrossAllTables: Called with invalid videoId: '${videoId}'.`);
            return null;
        }
        try {
            const topics = await this.getTopicTables();
            if (!topics || topics.length === 0) {
                console.warn("findVideoAcrossAllTables: No topic tables found to search in.");
                return null;
            }
            for (const topic of topics){
                try {
                    const tableNameToQuery = topic.table_name;
                    if (!tableNameToQuery) {
                        console.warn(`findVideoAcrossAllTables: Topic '${topic.name}' has no valid table_name. Skipping.`);
                        continue;
                    }
                    console.log(`findVideoAcrossAllTables: Checking table '${tableNameToQuery}' for videoId '${videoId}'`);
                    const { data, error: supabaseError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].from(tableNameToQuery).select('*').eq('video_id', videoId).single();
                    if (!supabaseError && data) {
                        console.log(`findVideoAcrossAllTables: Found videoId '${videoId}' in table '${tableNameToQuery}'`);
                        const simpleTopicName = topic.name.startsWith('youtube_') ? topic.name.substring(8) : topic.name;
                        // Ensure llm_response is at least an empty object if null/undefined from DB
                        return {
                            ...data,
                            topic_category: simpleTopicName,
                            llm_response: data.llm_response || {}
                        };
                    }
                    if (supabaseError && supabaseError.code !== 'PGRST116') {
                        console.warn(`findVideoAcrossAllTables: Supabase error querying table '${tableNameToQuery}' for videoId '${videoId}'. Code: ${supabaseError.code}, Message: ${supabaseError.message}`);
                    }
                } catch (tableQueryError) {
                    const tableQueryErrorMessage = tableQueryError instanceof Error ? tableQueryError.message : String(tableQueryError);
                    console.warn(`findVideoAcrossAllTables: Exception while querying table '${topic.table_name}' for videoId '${videoId}'. Message: ${tableQueryErrorMessage}`);
                }
            }
            console.log(`findVideoAcrossAllTables: VideoId '${videoId}' not found in any topic table after checking all.`);
            return null;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(`findVideoAcrossAllTables: General error searching for videoId '${videoId}' across all tables. Message: ${errorMessage}`);
            return null;
        }
    }
    // Search videos across all topic tables using vector similarity
    static async searchVideos(query, topicFilters = [], limit = 10) {
        try {
            // This will call our Python AI service to generate embeddings
            const embeddingResponse = await fetch('/api/embeddings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: query
                })
            });
            if (!embeddingResponse.ok) throw new Error('Failed to generate embedding');
            const { embedding } = await embeddingResponse.json();
            // Search across specified topic tables or all tables
            const searchPromises = topicFilters.length > 0 ? topicFilters.map((table)=>this.searchInTable(table, embedding, limit)) : await this.searchAllTables(embedding, limit);
            const results = await Promise.all(searchPromises);
            // Flatten and sort by similarity score
            return results.flat().sort((a, b)=>b.similarity_score - a.similarity_score).slice(0, limit);
        } catch (error) {
            console.error('Error searching videos:', error);
            return [];
        }
    }
    static async searchInTable(tableName, embedding, limit) {
        try {
            // Use pgvector similarity search
            const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["supabaseAdmin"].rpc('search_videos_by_embedding', {
                table_name: tableName,
                query_embedding: embedding,
                match_threshold: 0.7,
                match_count: limit
            });
            if (error) throw error;
            return (data || []).map((item)=>({
                    video: item.video,
                    similarity_score: item.similarity_score,
                    relevant_transcript_chunks: item.relevant_chunks
                }));
        } catch (error) {
            console.error(`Error searching in table ${tableName}:`, error);
            return [];
        }
    }
    static async searchAllTables(embedding, limit) {
        const topics = await this.getTopicTables();
        return Promise.all(topics.map((topic)=>this.searchInTable(topic.name, embedding, limit)));
    }
    // Get recent videos across all topics
    static async getRecentVideos(limit = 20) {
        try {
            console.log('🔄 getRecentVideos called with limit:', limit);
            const topics = await this.getTopicTables();
            console.log('🔄 Found topics:', topics.length, topics.map((t)=>t.table_name));
            const videoPromises = topics.map((topic)=>this.getVideosByTopic(topic.table_name, Math.ceil(limit / topics.length)) // Use table_name
            );
            const videoArrays = await Promise.all(videoPromises);
            console.log('🔄 Video arrays lengths:', videoArrays.map((arr)=>arr.length));
            const allVideos = videoArrays.flat();
            console.log('🔄 Total videos before sorting:', allVideos.length);
            // Sort by published date and return top results
            const sortedVideos = allVideos.sort((a, b)=>{
                const dateA = a.published_at ? new Date(a.published_at).getTime() : 0;
                const dateB = b.published_at ? new Date(b.published_at).getTime() : 0;
                return dateB - dateA;
            }).slice(0, limit);
            console.log('🔄 Final sorted videos:', sortedVideos.length);
            return sortedVideos;
        } catch (error) {
            console.error('Error fetching recent videos:', error);
            return [];
        }
    }
}
}}),
"[project]/src/lib/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatDate": (()=>formatDate),
    "formatDuration": (()=>formatDuration),
    "formatViewCount": (()=>formatViewCount),
    "truncateText": (()=>truncateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDuration(duration) {
    // Handle number input (duration in seconds)
    if (typeof duration === 'number') {
        const hours = Math.floor(duration / 3600);
        const minutes = Math.floor(duration % 3600 / 60);
        const seconds = duration % 60;
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    // Handle string input (YouTube duration format PT4M13S)
    if (typeof duration === 'string') {
        const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
        if (!match) return duration;
        const hours = match[1] ? parseInt(match[1].replace('H', '')) : 0;
        const minutes = match[2] ? parseInt(match[2].replace('M', '')) : 0;
        const seconds = match[3] ? parseInt(match[3].replace('S', '')) : 0;
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    // Fallback for unexpected input types
    return String(duration);
}
function formatViewCount(count) {
    if (count >= 1000000) {
        return `${(count / 1000000).toFixed(1)}M views`;
    } else if (count >= 1000) {
        return `${(count / 1000).toFixed(1)}K views`;
    }
    return `${count} views`;
}
function formatDate(dateString) {
    if (!dateString || dateString === 'null' || dateString === 'undefined') {
        // Handle NULL/empty dates - these are considered "older" videos
        return "Older video";
    }
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        console.warn(`formatDate: Invalid date string received: "${dateString}"`);
        return `Invalid date: ${dateString.substring(0, 20)}${dateString.length > 20 ? '...' : ''}`;
    }
    // Return the actual publication date instead of relative time
    // Format: "MMM DD, YYYY" (e.g., "Dec 15, 2024")
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
}
}}),
"[project]/src/components/CopyButtons.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryCopyButton": (()=>CategoryCopyButton),
    "FieldCopyButton": (()=>FieldCopyButton),
    "SummaryCopyButton": (()=>SummaryCopyButton),
    "TranscriptCopyButton": (()=>TranscriptCopyButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryCopyButton() from the server but CategoryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx <module evaluation>", "CategoryCopyButton");
const FieldCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FieldCopyButton() from the server but FieldCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx <module evaluation>", "FieldCopyButton");
const SummaryCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SummaryCopyButton() from the server but SummaryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx <module evaluation>", "SummaryCopyButton");
const TranscriptCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TranscriptCopyButton() from the server but TranscriptCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx <module evaluation>", "TranscriptCopyButton");
}}),
"[project]/src/components/CopyButtons.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CategoryCopyButton": (()=>CategoryCopyButton),
    "FieldCopyButton": (()=>FieldCopyButton),
    "SummaryCopyButton": (()=>SummaryCopyButton),
    "TranscriptCopyButton": (()=>TranscriptCopyButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const CategoryCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call CategoryCopyButton() from the server but CategoryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx", "CategoryCopyButton");
const FieldCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call FieldCopyButton() from the server but FieldCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx", "FieldCopyButton");
const SummaryCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call SummaryCopyButton() from the server but SummaryCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx", "SummaryCopyButton");
const TranscriptCopyButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call TranscriptCopyButton() from the server but TranscriptCopyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/CopyButtons.tsx", "TranscriptCopyButton");
}}),
"[project]/src/components/CopyButtons.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/CopyButtons.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/CopyButtons.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/video/[topic]/[videoId]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>VideoPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLinkIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-rsc] (ecmascript) <export default as ExternalLinkIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-rsc] (ecmascript) <export default as EyeIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2d$days$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarDaysIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar-days.js [app-rsc] (ecmascript) <export default as CalendarDaysIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-rsc] (ecmascript) <export default as ClockIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$play$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayCircleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-play.js [app-rsc] (ecmascript) <export default as PlayCircleIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircleIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-rsc] (ecmascript) <export default as MessageCircleIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/CopyButtons.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Helper to organize and render LLM response fields
const renderLlmResponse = (llmResponse)=>{
    if (!llmResponse || Object.keys(llmResponse).length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            className: "text-gray-500 dark:text-gray-400",
            children: "No detailed summary information available."
        }, void 0, false, {
            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
            lineNumber: 26,
            columnNumber: 12
        }, this);
    }
    // Define categories and their priority fields
    const categories = {
        "Summary": [
            'summary',
            'short_summary',
            'tldr',
            'overview'
        ],
        "Key Points": [
            'insights',
            'key_insights',
            'main_points',
            'key_takeaways',
            'key_points',
            'important_points',
            'highlights',
            'Insights'
        ],
        "Topics": [
            'keywords',
            'topics',
            'key_topics',
            'subject_areas',
            'categories',
            'themes',
            'important_concepts',
            'Keywords'
        ],
        "Questions": [
            'questions',
            'key_questions',
            'questions_answered',
            'faqs',
            'common_questions',
            'Questions'
        ],
        "Recommendations": [
            'recommendations',
            'suggestions',
            'advice',
            'next_steps',
            'Recommendations'
        ],
        "Analysis": [
            'analysis',
            'conclusion',
            'evaluation',
            'perspective',
            'sentiment',
            'sentiment_analysis'
        ],
        "Technical Details": [
            'technical_details',
            'specifications',
            'methodology',
            'technical_concepts'
        ],
        "Other": []
    };
    const availableKeys = Object.keys(llmResponse);
    const organizedData = {};
    Object.keys(categories).forEach((category)=>{
        organizedData[category] = [];
    });
    // Ensure llmResponse is not null before processing
    if (llmResponse) {
        availableKeys.forEach((key)=>{
            if ([
                'original_transcript_summary',
                'title',
                'channel_name'
            ].includes(key)) {
                return;
            }
            let assigned = false; // Correctly scoped variable
            for (const [category, fields] of Object.entries(categories)){
                if (fields.includes(key)) {
                    organizedData[category].push([
                        key,
                        llmResponse[key]
                    ]);
                    assigned = true;
                    break;
                }
            }
            if (!assigned) {
                organizedData["Other"].push([
                    key,
                    llmResponse[key]
                ]);
            }
        });
    }
    // Format field names nicely
    const formatFieldName = (key)=>{
        return key.replace(/_/g, ' ').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    };
    const formatFieldValue = (key, value)=>{
        if (value === null || value === undefined) return 'N/A';
        if ((key.toLowerCase().includes('keyword') || key.toLowerCase().includes('topic') || key === 'tags' || key === 'Keywords') && Array.isArray(value)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-2",
                children: value.map((item, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-2 py-1 rounded-full text-lg font-medium",
                        children: typeof item === 'object' ? JSON.stringify(item) : String(item)
                    }, idx, false, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 80,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 78,
                columnNumber: 9
            }, this);
        }
        if ((key.toLowerCase().includes('insight') || key.toLowerCase().includes('recommendation') || key.toLowerCase().includes('question') || key === 'Insights' || key === 'Recommendations' || key === 'Questions') && Array.isArray(value)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                className: "space-y-2 pl-0",
                children: value.map((item, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: "bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-gray-800 dark:text-gray-200 text-lg",
                            children: typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)
                        }, void 0, false, {
                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                            lineNumber: 91,
                            columnNumber: 15
                        }, this)
                    }, idx, false, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 90,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 88,
                columnNumber: 9
            }, this);
        }
        if (Array.isArray(value)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                className: "list-disc pl-5 space-y-1",
                children: value.map((item, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: "text-gray-800 dark:text-gray-200 text-lg",
                        children: typeof item === 'object' ? JSON.stringify(item, null, 2) : String(item)
                    }, idx, false, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 102,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 100,
                columnNumber: 9
            }, this);
        }
        if (typeof value === 'object') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                className: "bg-gray-50 dark:bg-gray-800 p-2 rounded-md overflow-auto text-lg",
                children: JSON.stringify(value, null, 2)
            }, void 0, false, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 110,
                columnNumber: 9
            }, this);
        }
        if ([
            'key_insights',
            'insights',
            'main_points',
            'key_takeaways',
            'Insights'
        ].includes(key) && typeof value === 'string') {
            if (value.includes("- ") || value.includes("• ")) {
                const bulletPoints = value.split(/\n\s*[-•]\s+/);
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    className: "space-y-2 pl-0",
                    children: bulletPoints.filter(Boolean).map((point, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-blue-500 dark:border-blue-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-gray-800 dark:text-gray-200 text-lg",
                                children: point.trim()
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 121,
                                columnNumber: 17
                            }, this)
                        }, idx, false, {
                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                            lineNumber: 120,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                    lineNumber: 118,
                    columnNumber: 11
                }, this);
            }
        }
        if ((key.toLowerCase().includes('question') || key.toLowerCase().includes('faq') || key === 'Questions') && typeof value === 'string') {
            if (value.includes("\n")) {
                const questions = value.split(/\n+/).filter(Boolean);
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    className: "space-y-2 pl-0",
                    children: questions.map((question, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            className: "bg-gray-50 dark:bg-gray-800 p-3 rounded-md border-l-2 border-purple-500 dark:border-purple-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-gray-800 dark:text-gray-200 text-lg",
                                children: question.trim()
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 134,
                                columnNumber: 17
                            }, this)
                        }, idx, false, {
                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                            lineNumber: 133,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                    lineNumber: 131,
                    columnNumber: 11
                }, this);
            }
        }
        if ([
            'key_insights',
            'insights',
            'summary',
            'tldr',
            'key_takeaways',
            'main_points',
            'recommendations',
            'Insights',
            'Recommendations'
        ].includes(key)) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "font-medium text-gray-900 dark:text-gray-100 text-lg",
                children: String(value)
            }, void 0, false, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 141,
                columnNumber: 14
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "text-gray-800 dark:text-gray-200 text-lg",
            children: String(value)
        }, void 0, false, {
            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
            lineNumber: 143,
            columnNumber: 12
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: Object.entries(organizedData).map(([category, fields])=>{
            if (fields.length === 0) return null;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow transition-shadow duration-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-gray-100 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "font-medium text-gray-900 dark:text-gray-100 text-xl",
                                children: category
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 152,
                                columnNumber: 15
                            }, this),
                            (category === "Key Points" || category === "Recommendations" || category === "Topics" || category === "Questions") && fields.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CategoryCopyButton"], {
                                category: category,
                                fields: fields
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 154,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 151,
                        columnNumber: 13
                    }, this),
                    "            ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("dl", {
                        className: "divide-y divide-gray-200 dark:divide-gray-800",
                        children: fields.map(([key, value])=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "px-4 py-3 group",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("dt", {
                                        className: "text-lg font-medium text-gray-500 dark:text-gray-400",
                                        children: formatFieldName(key)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 162,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("dd", {
                                        className: "mt-1 text-lg text-gray-800 dark:text-gray-200 whitespace-pre-wrap",
                                        children: formatFieldValue(key, value)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 163,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, key, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 161,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 159,
                        columnNumber: 31
                    }, this)
                ]
            }, category, true, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 150,
                columnNumber: 27
            }, this);
        })
    }, void 0, false, {
        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
        lineNumber: 147,
        columnNumber: 5
    }, this);
};
async function VideoPage({ params }) {
    // In Next.js 14/15, using await with params is now required for dynamic path segments
    const topic = String(await Promise.resolve(params.topic));
    const videoId = String(await Promise.resolve(params.videoId));
    const video = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DatabaseService"].getVideoById(topic, videoId);
    if (!video) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    const youtubeEmbedUrl = video.video_id ? `https://www.youtube.com/embed/${video.video_id}` : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-4 sm:p-6 lg:p-8",
        children: [
            " ",
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col md:flex-row md:items-start gap-6 lg:gap-8 mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-grow space-y-4 md:pr-6 lg:pr-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        className: "hover:text-primary dark:hover:text-primary-light",
                                        children: "Home"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 197,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "/"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 198,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-muted text-muted-foreground px-2 py-1 rounded-full text-xs",
                                        children: video.topic_category || 'Uncategorized'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 199,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 196,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold text-foreground leading-tight",
                                children: video.title
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: video.channel_name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 209,
                                        columnNumber: 13
                                    }, this),
                                    video.view_count !== null && video.view_count !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__EyeIcon$3e$__["EyeIcon"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 212,
                                                columnNumber: 17
                                            }, this),
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatViewCount"])(video.view_count)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 211,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2d$days$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarDaysIcon$3e$__["CalendarDaysIcon"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 217,
                                                columnNumber: 15
                                            }, this),
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDate"])(video.published_at)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 216,
                                        columnNumber: 13
                                    }, this),
                                    video.duration && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ClockIcon$3e$__["ClockIcon"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 223,
                                                columnNumber: 17
                                            }, this),
                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["formatDuration"])(video.duration)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 208,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-3 mt-4",
                                children: [
                                    video.video_url && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: video.video_url,
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        className: "inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$play$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayCircleIcon$3e$__["PlayCircleIcon"], {
                                                className: "w-4 h-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 238,
                                                columnNumber: 17
                                            }, this),
                                            "Watch on YouTube"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 232,
                                        columnNumber: 15
                                    }, this),
                                    video.channel_id && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                                        href: `https://www.youtube.com/channel/${video.channel_id}`,
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        className: "inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLinkIcon$3e$__["ExternalLinkIcon"], {
                                                className: "w-4 h-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 249,
                                                columnNumber: 17
                                            }, this),
                                            "View Channel"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 243,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 230,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 194,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-full md:w-[320px] lg:w-[400px] xl:w-[480px] flex-shrink-0 self-start md:sticky md:top-6",
                        children: youtubeEmbedUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "aspect-video overflow-hidden rounded-lg border border-border dark:border-gray-700 relative shadow-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                                width: "100%",
                                height: "100%",
                                src: youtubeEmbedUrl,
                                title: video.title ?? "YouTube video player",
                                frameBorder: "0",
                                allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",
                                allowFullScreen: true,
                                className: "rounded-lg"
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 260,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                            lineNumber: 259,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "aspect-video overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 relative bg-muted flex items-center justify-center shadow-lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$play$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__PlayCircleIcon$3e$__["PlayCircleIcon"], {
                                    className: "w-16 h-16 text-muted-foreground"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                    lineNumber: 273,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "ml-2 text-muted-foreground",
                                    children: "Video not available"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                    lineNumber: 274,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                            lineNumber: 272,
                            columnNumber: 14
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 257,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 192,
                columnNumber: 7
            }, this),
            " ",
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full space-y-8",
                children: [
                    " ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-2xl font-semibold flex items-center text-foreground",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircleIcon$3e$__["MessageCircleIcon"], {
                                            className: "w-6 h-6 mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                            lineNumber: 286,
                                            columnNumber: 15
                                        }, this),
                                        " AI Analysis"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                    lineNumber: 285,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 284,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    video.llm_response && (typeof video.llm_response.tldr === 'string' || typeof video.llm_response.short_summary === 'string' || typeof video.llm_response.summary === 'string') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-6 p-4 bg-primary/10 rounded-lg border border-primary/20 shadow-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "font-medium text-primary mb-2 text-2xl",
                                                        children: "Quick Summary"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                        lineNumber: 299,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SummaryCopyButton"], {
                                                        text: typeof video.llm_response?.tldr === 'string' ? video.llm_response.tldr : typeof video.llm_response?.short_summary === 'string' ? video.llm_response.short_summary : typeof video.llm_response?.summary === 'string' ? video.llm_response.summary : 'No summary available'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                        lineNumber: 300,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 298,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-foreground font-medium text-lg",
                                                children: typeof video.llm_response.tldr === 'string' ? video.llm_response.tldr : typeof video.llm_response.short_summary === 'string' ? video.llm_response.short_summary : typeof video.llm_response.summary === 'string' ? video.llm_response.summary : 'No summary available'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 311,
                                                columnNumber: 17
                                            }, this),
                                            " "
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 297,
                                        columnNumber: 33
                                    }, this),
                                    renderLlmResponse(video.llm_response)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 291,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 283,
                        columnNumber: 9
                    }, this),
                    video.transcript && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-2xl font-semibold flex items-center text-foreground",
                                        children: "Transcript"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 332,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$CopyButtons$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TranscriptCopyButton"], {
                                        transcript: video.transcript || ''
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 335,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 331,
                                columnNumber: 13
                            }, this),
                            "            ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                                className: "mt-1 group",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                        className: "cursor-pointer font-medium text-primary hover:underline flex items-center text-2xl",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                xmlns: "http://www.w3.org/2000/svg",
                                                className: "h-5 w-5 mr-1 transform transition-transform group-open:rotate-90",
                                                fill: "none",
                                                viewBox: "0 0 24 24",
                                                stroke: "currentColor",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M9 5l7 7-7 7"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                    lineNumber: 339,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                                lineNumber: 338,
                                                columnNumber: 17
                                            }, this),
                                            "View Full Transcript"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 337,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 p-4 bg-muted rounded-lg shadow max-h-96 overflow-y-auto border border-border",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-muted-foreground whitespace-pre-wrap text-lg leading-relaxed",
                                            children: video.transcript
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                            lineNumber: 344,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                        lineNumber: 343,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                                lineNumber: 336,
                                columnNumber: 31
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                        lineNumber: 330,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
                lineNumber: 281,
                columnNumber: 7
            }, this),
            " "
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/video/[topic]/[videoId]/page.tsx",
        lineNumber: 190,
        columnNumber: 5
    }, this);
}
async function generateMetadata({ params }) {
    // In Next.js 14/15, using await with params is now required for dynamic path segments
    const topic = String(await Promise.resolve(params.topic));
    const videoId = String(await Promise.resolve(params.videoId));
    const video = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DatabaseService"].getVideoById(topic, videoId);
    if (!video) {
        return {
            title: 'Video Not Found',
            description: 'This video could not be found or is not available.'
        };
    }
    // Get the best summary from the JSONB data
    let metaDescription = 'No summary available.';
    let keywords = [];
    if (video.llm_response) {
        // Find the best summary source
        if (typeof video.llm_response.tldr === 'string') {
            metaDescription = video.llm_response.tldr;
        } else if (typeof video.llm_response.short_summary === 'string') {
            metaDescription = video.llm_response.short_summary;
        } else if (typeof video.llm_response.summary === 'string') {
            metaDescription = video.llm_response.summary;
        }
        // Extract keywords if available (handle both lowercase and capitalized keys)
        if (Array.isArray(video.llm_response.keywords)) {
            keywords = video.llm_response.keywords.map((k)=>String(k));
        } else if (Array.isArray(video.llm_response.Keywords)) {
            keywords = video.llm_response.Keywords.map((k)=>String(k));
        } else if (Array.isArray(video.llm_response.topics)) {
            keywords = video.llm_response.topics.map((k)=>String(k));
        } else if (typeof video.llm_response.keywords === 'string') {
            // Try to parse keywords from string (comma separated)
            keywords = video.llm_response.keywords.split(/,\\s*/);
        } else if (typeof video.llm_response.Keywords === 'string') {
            // Try to parse keywords from string (comma separated)
            keywords = video.llm_response.Keywords.split(/,\\s*/);
        }
    }
    // Limit description length
    metaDescription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["truncateText"])(metaDescription, 160);
    // Create a rich metadata object
    return {
        title: video.title ?? 'Video Summary',
        description: metaDescription,
        keywords: keywords.length > 0 ? keywords : undefined,
        openGraph: {
            title: video.title ?? 'Video Summary',
            description: metaDescription,
            images: [
                {
                    url: video.thumbnail_url ?? '/placeholder.svg',
                    width: 1200,
                    height: 630,
                    alt: video.title ?? 'Video thumbnail'
                }
            ],
            type: 'video.other',
            url: `/video/${topic}/${videoId}`,
            siteName: 'YouTube Summaries'
        },
        twitter: {
            card: 'summary_large_image',
            title: video.title ?? 'Video Summary',
            description: metaDescription,
            images: [
                video.thumbnail_url ?? '/placeholder.svg'
            ]
        },
        authors: video.channel_name ? [
            {
                name: video.channel_name
            }
        ] : undefined
    };
}
}}),
"[project]/src/app/video/[topic]/[videoId]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/video/[topic]/[videoId]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__aa36a668._.js.map