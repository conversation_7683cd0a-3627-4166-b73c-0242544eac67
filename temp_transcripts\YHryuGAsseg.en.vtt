WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.610 align:start position:0%
 
earlier<00:00:00.599><c> this</c><00:00:00.780><c> year</c><00:00:00.960><c> GitHub</c><00:00:01.380><c> Enterprise</c>

00:00:01.610 --> 00:00:01.620 align:start position:0%
earlier this year GitHub Enterprise
 

00:00:01.620 --> 00:00:03.290 align:start position:0%
earlier this year GitHub Enterprise
importer<00:00:02.340><c> became</c><00:00:02.700><c> generally</c><00:00:03.060><c> available</c>

00:00:03.290 --> 00:00:03.300 align:start position:0%
importer became generally available
 

00:00:03.300 --> 00:00:05.690 align:start position:0%
importer became generally available
making<00:00:04.080><c> it</c><00:00:04.380><c> easier</c><00:00:04.620><c> than</c><00:00:04.799><c> ever</c><00:00:04.920><c> for</c><00:00:05.400><c> you</c><00:00:05.520><c> to</c>

00:00:05.690 --> 00:00:05.700 align:start position:0%
making it easier than ever for you to
 

00:00:05.700 --> 00:00:07.970 align:start position:0%
making it easier than ever for you to
migrate<00:00:06.060><c> to</c><00:00:06.180><c> your</c><00:00:06.359><c> git</c><00:00:06.600><c> history</c><00:00:06.839><c> work</c><00:00:07.500><c> items</c>

00:00:07.970 --> 00:00:07.980 align:start position:0%
migrate to your git history work items
 

00:00:07.980 --> 00:00:10.129 align:start position:0%
migrate to your git history work items
pull<00:00:08.160><c> requests</c><00:00:08.580><c> comments</c><00:00:09.059><c> and</c><00:00:09.300><c> more</c><00:00:09.540><c> major</c>

00:00:10.129 --> 00:00:10.139 align:start position:0%
pull requests comments and more major
 

00:00:10.139 --> 00:00:13.070 align:start position:0%
pull requests comments and more major
Devils<00:00:10.740><c> services</c><00:00:10.860><c> github.com</c><00:00:11.760><c> or</c><00:00:12.360><c> getabents</c>

00:00:13.070 --> 00:00:13.080 align:start position:0%
Devils services github.com or getabents
 

00:00:13.080 --> 00:00:15.890 align:start position:0%
Devils services github.com or getabents
by<00:00:13.139><c> server</c><00:00:13.500><c> to</c><00:00:13.920><c> GitHub</c><00:00:14.340><c> Enterprise</c><00:00:14.519><c> cloud</c>

00:00:15.890 --> 00:00:15.900 align:start position:0%
by server to GitHub Enterprise cloud
 

00:00:15.900 --> 00:00:17.630 align:start position:0%
by server to GitHub Enterprise cloud
but<00:00:16.260><c> we</c><00:00:16.560><c> know</c><00:00:16.680><c> that</c><00:00:16.920><c> there</c><00:00:17.160><c> are</c><00:00:17.279><c> additional</c>

00:00:17.630 --> 00:00:17.640 align:start position:0%
but we know that there are additional
 

00:00:17.640 --> 00:00:19.330 align:start position:0%
but we know that there are additional
platforms<00:00:18.119><c> that</c><00:00:18.300><c> you</c><00:00:18.420><c> would</c><00:00:18.539><c> like</c><00:00:18.720><c> supported</c>

00:00:19.330 --> 00:00:19.340 align:start position:0%
platforms that you would like supported
 

00:00:19.340 --> 00:00:21.470 align:start position:0%
platforms that you would like supported
bitbucket<00:00:20.340><c> server</c><00:00:20.640><c> is</c><00:00:20.820><c> a</c><00:00:20.939><c> common</c><00:00:21.060><c> one</c><00:00:21.240><c> that</c>

00:00:21.470 --> 00:00:21.480 align:start position:0%
bitbucket server is a common one that
 

00:00:21.480 --> 00:00:23.330 align:start position:0%
bitbucket server is a common one that
our<00:00:21.660><c> customers</c><00:00:22.020><c> and</c><00:00:22.320><c> Community</c><00:00:22.619><c> have</c><00:00:23.100><c> been</c>

00:00:23.330 --> 00:00:23.340 align:start position:0%
our customers and Community have been
 

00:00:23.340 --> 00:00:25.490 align:start position:0%
our customers and Community have been
asking<00:00:23.699><c> for</c><00:00:23.840><c> particularly</c><00:00:24.840><c> with</c><00:00:25.080><c> its</c><00:00:25.380><c> support</c>

00:00:25.490 --> 00:00:25.500 align:start position:0%
asking for particularly with its support
 

00:00:25.500 --> 00:00:28.250 align:start position:0%
asking for particularly with its support
due<00:00:26.039><c> to</c><00:00:26.160><c> end</c><00:00:26.279><c> in</c><00:00:26.580><c> February</c><00:00:26.880><c> 2024</c>

00:00:28.250 --> 00:00:28.260 align:start position:0%
due to end in February 2024
 

00:00:28.260 --> 00:00:30.830 align:start position:0%
due to end in February 2024
or<00:00:28.800><c> perhaps</c><00:00:29.160><c> you're</c><00:00:29.640><c> on</c><00:00:29.880><c> bitbucket</c><00:00:30.480><c> data</c>

00:00:30.830 --> 00:00:30.840 align:start position:0%
or perhaps you're on bitbucket data
 

00:00:30.840 --> 00:00:33.110 align:start position:0%
or perhaps you're on bitbucket data
center<00:00:30.900><c> but</c><00:00:31.260><c> have</c><00:00:31.439><c> plans</c><00:00:31.679><c> to</c><00:00:31.859><c> migrate</c><00:00:32.759><c> well</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
center but have plans to migrate well
 

00:00:33.120 --> 00:00:34.610 align:start position:0%
center but have plans to migrate well
I'm<00:00:33.300><c> happy</c><00:00:33.480><c> to</c><00:00:33.660><c> share</c><00:00:33.780><c> the</c><00:00:34.020><c> migrations</c><00:00:34.320><c> from</c>

00:00:34.610 --> 00:00:34.620 align:start position:0%
I'm happy to share the migrations from
 

00:00:34.620 --> 00:00:36.229 align:start position:0%
I'm happy to share the migrations from
bitbucket<00:00:35.280><c> server</c><00:00:35.579><c> to</c><00:00:35.760><c> GitHub</c><00:00:36.059><c> Enterprise</c>

00:00:36.229 --> 00:00:36.239 align:start position:0%
bitbucket server to GitHub Enterprise
 

00:00:36.239 --> 00:00:38.510 align:start position:0%
bitbucket server to GitHub Enterprise
Cloud<00:00:36.660><c> are</c><00:00:36.960><c> Now</c><00:00:37.200><c> supported</c><00:00:37.680><c> in</c><00:00:38.100><c> GitHub</c>

00:00:38.510 --> 00:00:38.520 align:start position:0%
Cloud are Now supported in GitHub
 

00:00:38.520 --> 00:00:40.250 align:start position:0%
Cloud are Now supported in GitHub
Enterprise<00:00:38.760><c> importer</c>

00:00:40.250 --> 00:00:40.260 align:start position:0%
Enterprise importer
 

00:00:40.260 --> 00:00:42.049 align:start position:0%
Enterprise importer
so<00:00:40.620><c> don't</c><00:00:40.739><c> waste</c><00:00:41.100><c> time</c><00:00:41.219><c> get</c><00:00:41.520><c> planning</c><00:00:41.879><c> your</c>

00:00:42.049 --> 00:00:42.059 align:start position:0%
so don't waste time get planning your
 

00:00:42.059 --> 00:00:43.910 align:start position:0%
so don't waste time get planning your
migration<00:00:42.360><c> and</c><00:00:42.719><c> check</c><00:00:42.960><c> out</c><00:00:43.200><c> the</c><00:00:43.559><c> getup</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
migration and check out the getup
 

00:00:43.920 --> 00:00:47.180 align:start position:0%
migration and check out the getup
Enterprise<00:00:44.100><c> importer</c><00:00:44.820><c> today</c>

