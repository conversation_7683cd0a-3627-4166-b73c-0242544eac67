WEBVTT
Kind: captions
Language: en

00:00:00.120 --> 00:00:01.470 align:start position:0%
 
you<00:00:00.240><c> may</c><00:00:00.440><c> have</c><00:00:00.560><c> heard</c><00:00:00.840><c> about</c><00:00:01.079><c> GitHub</c>

00:00:01.470 --> 00:00:01.480 align:start position:0%
you may have heard about GitHub
 

00:00:01.480 --> 00:00:03.669 align:start position:0%
you may have heard about GitHub
repository<00:00:01.920><c> rules</c><00:00:02.240><c> the</c><00:00:02.480><c> evolution</c><00:00:03.080><c> of</c><00:00:03.280><c> GitHub</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
repository rules the evolution of GitHub
 

00:00:03.679 --> 00:00:05.309 align:start position:0%
repository rules the evolution of GitHub
Branch<00:00:03.959><c> protection</c><00:00:04.319><c> rules</c><00:00:04.799><c> so</c><00:00:04.960><c> that</c><00:00:05.040><c> you</c><00:00:05.160><c> can</c>

00:00:05.309 --> 00:00:05.319 align:start position:0%
Branch protection rules so that you can
 

00:00:05.319 --> 00:00:07.429 align:start position:0%
Branch protection rules so that you can
apply<00:00:05.640><c> scalable</c><00:00:06.240><c> governance</c><00:00:06.680><c> policies</c>

00:00:07.429 --> 00:00:07.439 align:start position:0%
apply scalable governance policies
 

00:00:07.439 --> 00:00:09.950 align:start position:0%
apply scalable governance policies
across<00:00:07.720><c> your</c><00:00:07.919><c> GitHub</c><00:00:08.360><c> organization</c><00:00:09.360><c> on</c>

00:00:09.950 --> 00:00:09.960 align:start position:0%
across your GitHub organization on
 

00:00:09.960 --> 00:00:12.470 align:start position:0%
across your GitHub organization on
GitHub<00:00:10.440><c> Enterprise</c><00:00:11.120><c> Cloud</c><00:00:12.120><c> you</c><00:00:12.280><c> could</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
GitHub Enterprise Cloud you could
 

00:00:12.480 --> 00:00:14.150 align:start position:0%
GitHub Enterprise Cloud you could
require<00:00:12.840><c> checks</c><00:00:13.240><c> like</c><00:00:13.400><c> multiple</c><00:00:13.799><c> reviewers</c>

00:00:14.150 --> 00:00:14.160 align:start position:0%
require checks like multiple reviewers
 

00:00:14.160 --> 00:00:16.029 align:start position:0%
require checks like multiple reviewers
for<00:00:14.280><c> a</c><00:00:14.400><c> poll</c><00:00:14.639><c> request</c><00:00:15.160><c> require</c><00:00:15.599><c> specific</c>

00:00:16.029 --> 00:00:16.039 align:start position:0%
for a poll request require specific
 

00:00:16.039 --> 00:00:18.750 align:start position:0%
for a poll request require specific
status<00:00:16.359><c> checks</c><00:00:16.640><c> to</c><00:00:16.840><c> pass</c><00:00:17.199><c> and</c><00:00:17.439><c> more</c><00:00:18.439><c> but</c><00:00:18.640><c> what</c>

00:00:18.750 --> 00:00:18.760 align:start position:0%
status checks to pass and more but what
 

00:00:18.760 --> 00:00:20.310 align:start position:0%
status checks to pass and more but what
if<00:00:18.920><c> there</c><00:00:19.039><c> was</c><00:00:19.160><c> a</c><00:00:19.359><c> common</c><00:00:19.680><c> GitHub</c><00:00:20.080><c> action</c>

00:00:20.310 --> 00:00:20.320 align:start position:0%
if there was a common GitHub action
 

00:00:20.320 --> 00:00:22.070 align:start position:0%
if there was a common GitHub action
workflow<00:00:20.880><c> that</c><00:00:21.039><c> you</c><00:00:21.199><c> wanted</c><00:00:21.480><c> to</c><00:00:21.640><c> enforce</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
workflow that you wanted to enforce
 

00:00:22.080 --> 00:00:25.070 align:start position:0%
workflow that you wanted to enforce
across<00:00:22.439><c> many</c><00:00:22.760><c> repositories</c><00:00:23.760><c> in</c><00:00:24.240><c> your</c>

00:00:25.070 --> 00:00:25.080 align:start position:0%
across many repositories in your
 

00:00:25.080 --> 00:00:26.830 align:start position:0%
across many repositories in your
organization<00:00:26.080><c> well</c><00:00:26.279><c> that</c><00:00:26.439><c> is</c><00:00:26.560><c> where</c>

00:00:26.830 --> 00:00:26.840 align:start position:0%
organization well that is where
 

00:00:26.840 --> 00:00:28.429 align:start position:0%
organization well that is where
requiring<00:00:27.279><c> workflows</c><00:00:27.760><c> to</c><00:00:27.960><c> pass</c><00:00:28.199><c> before</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
requiring workflows to pass before
 

00:00:28.439 --> 00:00:30.870 align:start position:0%
requiring workflows to pass before
merging<00:00:28.920><c> comes</c><00:00:29.160><c> in</c><00:00:30.080><c> this</c><00:00:30.199><c> new</c><00:00:30.359><c> rule</c><00:00:30.679><c> set</c>

00:00:30.870 --> 00:00:30.880 align:start position:0%
merging comes in this new rule set
 

00:00:30.880 --> 00:00:32.790 align:start position:0%
merging comes in this new rule set
enables<00:00:31.279><c> you</c><00:00:31.400><c> to</c><00:00:31.560><c> require</c><00:00:32.040><c> several</c><00:00:32.399><c> giup</c>

00:00:32.790 --> 00:00:32.800 align:start position:0%
enables you to require several giup
 

00:00:32.800 --> 00:00:35.030 align:start position:0%
enables you to require several giup
action<00:00:33.040><c> workflows</c><00:00:33.559><c> to</c><00:00:33.760><c> successfully</c><00:00:34.239><c> run</c><00:00:34.840><c> as</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
action workflows to successfully run as
 

00:00:35.040 --> 00:00:36.990 align:start position:0%
action workflows to successfully run as
part<00:00:35.200><c> of</c><00:00:35.320><c> your</c><00:00:35.440><c> pull</c><00:00:35.800><c> requests</c><00:00:36.800><c> these</c>

00:00:36.990 --> 00:00:37.000 align:start position:0%
part of your pull requests these
 

00:00:37.000 --> 00:00:38.389 align:start position:0%
part of your pull requests these
workflows<00:00:37.520><c> could</c><00:00:37.640><c> be</c><00:00:37.760><c> to</c><00:00:37.960><c> find</c><00:00:38.120><c> an</c><00:00:38.239><c> a</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
workflows could be to find an a
 

00:00:38.399 --> 00:00:40.310 align:start position:0%
workflows could be to find an a
centralized<00:00:38.960><c> repository</c><00:00:39.719><c> so</c><00:00:39.879><c> that</c><00:00:40.000><c> you</c><00:00:40.079><c> can</c>

00:00:40.310 --> 00:00:40.320 align:start position:0%
centralized repository so that you can
 

00:00:40.320 --> 00:00:42.750 align:start position:0%
centralized repository so that you can
reuse<00:00:40.800><c> your</c><00:00:40.960><c> core</c><00:00:41.239><c> workflows</c><00:00:41.920><c> as</c><00:00:42.079><c> needed</c>

00:00:42.750 --> 00:00:42.760 align:start position:0%
reuse your core workflows as needed
 

00:00:42.760 --> 00:00:43.910 align:start position:0%
reuse your core workflows as needed
across<00:00:43.200><c> your</c>

00:00:43.910 --> 00:00:43.920 align:start position:0%
across your
 

00:00:43.920 --> 00:00:46.510 align:start position:0%
across your
organization<00:00:44.920><c> want</c><00:00:45.120><c> to</c><00:00:45.280><c> find</c><00:00:45.440><c> out</c><00:00:45.600><c> more</c><00:00:46.320><c> check</c>

00:00:46.510 --> 00:00:46.520 align:start position:0%
organization want to find out more check
 

00:00:46.520 --> 00:00:49.830 align:start position:0%
organization want to find out more check
out<00:00:46.680><c> more</c><00:00:46.920><c> details</c><00:00:47.600><c> using</c><00:00:48.079><c> this</c>

00:00:49.830 --> 00:00:49.840 align:start position:0%
out more details using this
 

00:00:49.840 --> 00:00:52.840 align:start position:0%
out more details using this
link

