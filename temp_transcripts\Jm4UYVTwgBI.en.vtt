WEBVTT
Kind: captions
Language: en

00:00:00.480 --> 00:00:04.789 align:start position:0%
 
this<00:00:01.120><c> is</c><00:00:01.280><c> autog</c><00:00:02.240><c> beta</c><00:00:02.560><c> version</c><00:00:03.120><c> 2.0</c><00:00:04.120><c> autog</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
this is autog beta version 2.0 autog
 

00:00:04.799 --> 00:00:06.269 align:start position:0%
this is autog beta version 2.0 autog
automatically<00:00:05.319><c> creates</c><00:00:05.680><c> agents</c><00:00:06.040><c> and</c>

00:00:06.269 --> 00:00:06.279 align:start position:0%
automatically creates agents and
 

00:00:06.279 --> 00:00:09.230 align:start position:0%
automatically creates agents and
workflows<00:00:07.279><c> all</c><00:00:07.480><c> from</c><00:00:07.640><c> a</c><00:00:07.720><c> single</c><00:00:08.120><c> prompt</c><00:00:08.960><c> teams</c>

00:00:09.230 --> 00:00:09.240 align:start position:0%
workflows all from a single prompt teams
 

00:00:09.240 --> 00:00:12.150 align:start position:0%
workflows all from a single prompt teams
of<00:00:09.400><c> experts</c><00:00:09.719><c> are</c><00:00:09.880><c> just</c><00:00:10.000><c> a</c><00:00:10.160><c> click</c><00:00:10.799><c> away</c><00:00:11.799><c> even</c><00:00:12.000><c> if</c>

00:00:12.150 --> 00:00:12.160 align:start position:0%
of experts are just a click away even if
 

00:00:12.160 --> 00:00:15.789 align:start position:0%
of experts are just a click away even if
the<00:00:12.320><c> topic</c><00:00:13.040><c> is</c><00:00:14.040><c> belly</c><00:00:14.320><c> button</c><00:00:14.639><c> lint</c><00:00:15.639><c> you're</c>

00:00:15.789 --> 00:00:15.799 align:start position:0%
the topic is belly button lint you're
 

00:00:15.799 --> 00:00:17.710 align:start position:0%
the topic is belly button lint you're
seeing<00:00:16.199><c> autog</c><00:00:16.560><c> grock</c><00:00:16.800><c> Run</c><00:00:17.000><c> in</c><00:00:17.160><c> real</c><00:00:17.400><c> time</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
seeing autog grock Run in real time
 

00:00:17.720 --> 00:00:19.830 align:start position:0%
seeing autog grock Run in real time
we're<00:00:17.880><c> not</c><00:00:18.039><c> speeding</c><00:00:18.359><c> up</c><00:00:18.520><c> the</c><00:00:18.640><c> video</c><00:00:19.600><c> five</c>

00:00:19.830 --> 00:00:19.840 align:start position:0%
we're not speeding up the video five
 

00:00:19.840 --> 00:00:21.630 align:start position:0%
we're not speeding up the video five
fully<00:00:20.119><c> functional</c><00:00:20.560><c> agents</c><00:00:20.920><c> and</c><00:00:21.039><c> a</c><00:00:21.160><c> workflow</c>

00:00:21.630 --> 00:00:21.640 align:start position:0%
fully functional agents and a workflow
 

00:00:21.640 --> 00:00:23.990 align:start position:0%
fully functional agents and a workflow
file<00:00:21.880><c> were</c><00:00:22.080><c> created</c><00:00:22.400><c> in</c><00:00:22.519><c> the</c><00:00:22.640><c> blink</c><00:00:22.880><c> of</c><00:00:23.000><c> an</c><00:00:23.160><c> eye</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
file were created in the blink of an eye
 

00:00:24.000 --> 00:00:25.630 align:start position:0%
file were created in the blink of an eye
click<00:00:24.199><c> on</c><00:00:24.439><c> any</c><00:00:24.599><c> of</c><00:00:24.760><c> them</c><00:00:24.960><c> to</c><00:00:25.080><c> see</c><00:00:25.320><c> how</c><00:00:25.480><c> they</c>

00:00:25.630 --> 00:00:25.640 align:start position:0%
click on any of them to see how they
 

00:00:25.640 --> 00:00:27.509 align:start position:0%
click on any of them to see how they
work<00:00:26.039><c> together</c><00:00:26.640><c> by</c><00:00:26.840><c> discussing</c><00:00:27.359><c> and</c>

00:00:27.509 --> 00:00:27.519 align:start position:0%
work together by discussing and
 

00:00:27.519 --> 00:00:29.630 align:start position:0%
work together by discussing and
addressing<00:00:27.960><c> the</c><00:00:28.080><c> user's</c><00:00:28.480><c> request</c><00:00:29.359><c> you</c><00:00:29.480><c> can</c>

00:00:29.630 --> 00:00:29.640 align:start position:0%
addressing the user's request you can
 

00:00:29.640 --> 00:00:31.349 align:start position:0%
addressing the user's request you can
review<00:00:30.039><c> their</c><00:00:30.199><c> conversation</c><00:00:31.039><c> in</c><00:00:31.199><c> the</c>

00:00:31.349 --> 00:00:31.359 align:start position:0%
review their conversation in the
 

00:00:31.359 --> 00:00:33.830 align:start position:0%
review their conversation in the
discussion<00:00:31.840><c> window</c><00:00:32.640><c> it's</c><00:00:32.800><c> pretty</c><00:00:33.040><c> impressive</c>

00:00:33.830 --> 00:00:33.840 align:start position:0%
discussion window it's pretty impressive
 

00:00:33.840 --> 00:00:35.270 align:start position:0%
discussion window it's pretty impressive
these<00:00:34.040><c> agents</c><00:00:34.360><c> know</c><00:00:34.520><c> a</c><00:00:34.640><c> lot</c><00:00:34.800><c> about</c><00:00:35.000><c> belly</c>

00:00:35.270 --> 00:00:35.280 align:start position:0%
these agents know a lot about belly
 

00:00:35.280 --> 00:00:37.549 align:start position:0%
these agents know a lot about belly
button<00:00:35.559><c> lint</c><00:00:36.559><c> it</c><00:00:36.640><c> doesn't</c><00:00:36.920><c> matter</c><00:00:37.239><c> what</c><00:00:37.360><c> the</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
button lint it doesn't matter what the
 

00:00:37.559 --> 00:00:39.830 align:start position:0%
button lint it doesn't matter what the
topic<00:00:37.800><c> is</c><00:00:38.399><c> autog</c><00:00:38.760><c> gr</c><00:00:39.000><c> will</c><00:00:39.160><c> custom</c><00:00:39.480><c> create</c><00:00:39.719><c> a</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
topic is autog gr will custom create a
 

00:00:39.840 --> 00:00:41.869 align:start position:0%
topic is autog gr will custom create a
team<00:00:40.039><c> of</c><00:00:40.200><c> agents</c><00:00:40.559><c> who</c><00:00:40.680><c> will</c><00:00:40.840><c> be</c><00:00:41.000><c> tailor</c><00:00:41.480><c> made</c>

00:00:41.869 --> 00:00:41.879 align:start position:0%
team of agents who will be tailor made
 

00:00:41.879 --> 00:00:45.950 align:start position:0%
team of agents who will be tailor made
to<00:00:42.079><c> have</c><00:00:42.320><c> expertise</c><00:00:42.960><c> in</c><00:00:43.160><c> virtually</c><00:00:43.680><c> any</c><00:00:44.000><c> topic</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
to have expertise in virtually any topic
 

00:00:45.960 --> 00:00:47.830 align:start position:0%
to have expertise in virtually any topic
imaginable<00:00:46.960><c> let's</c><00:00:47.160><c> see</c><00:00:47.320><c> what</c><00:00:47.399><c> our</c><00:00:47.559><c> content</c>

00:00:47.830 --> 00:00:47.840 align:start position:0%
imaginable let's see what our content
 

00:00:47.840 --> 00:00:52.430 align:start position:0%
imaginable let's see what our content
writer<00:00:48.120><c> comes</c><00:00:48.320><c> up</c><00:00:48.559><c> with</c><00:00:49.559><c> I</c><00:00:49.680><c> bet</c><00:00:49.800><c> it's</c>

00:00:52.430 --> 00:00:52.440 align:start position:0%
 
 

00:00:52.440 --> 00:00:56.510 align:start position:0%
 
impressive<00:00:53.440><c> yep</c><00:00:54.280><c> I</c><00:00:54.399><c> was</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
impressive yep I was
 

00:00:56.520 --> 00:00:59.110 align:start position:0%
impressive yep I was
right<00:00:57.520><c> we'll</c><00:00:57.719><c> skip</c><00:00:58.039><c> past</c><00:00:58.280><c> our</c><00:00:58.440><c> web</c><00:00:58.640><c> developer</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
right we'll skip past our web developer
 

00:00:59.120 --> 00:01:00.830 align:start position:0%
right we'll skip past our web developer
and<00:00:59.239><c> see</c><00:00:59.359><c> what</c><00:00:59.519><c> SEO</c><00:01:00.039><c> advice</c><00:01:00.320><c> we</c><00:01:00.399><c> can</c><00:01:00.519><c> use</c><00:01:00.680><c> to</c>

00:01:00.830 --> 00:01:00.840 align:start position:0%
and see what SEO advice we can use to
 

00:01:00.840 --> 00:01:02.630 align:start position:0%
and see what SEO advice we can use to
create<00:01:01.039><c> our</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
create our
 

00:01:02.640 --> 00:01:04.670 align:start position:0%
create our
website<00:01:03.640><c> all</c><00:01:03.800><c> the</c><00:01:03.960><c> information</c><00:01:04.400><c> in</c><00:01:04.519><c> the</c>

00:01:04.670 --> 00:01:04.680 align:start position:0%
website all the information in the
 

00:01:04.680 --> 00:01:06.469 align:start position:0%
website all the information in the
discussion<00:01:05.080><c> window</c><00:01:05.439><c> is</c><00:01:05.640><c> available</c><00:01:06.040><c> to</c><00:01:06.240><c> each</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
discussion window is available to each
 

00:01:06.479 --> 00:01:08.510 align:start position:0%
discussion window is available to each
agent<00:01:07.000><c> so</c><00:01:07.159><c> they're</c><00:01:07.360><c> able</c><00:01:07.560><c> to</c><00:01:07.680><c> collaborate</c><00:01:08.320><c> and</c>

00:01:08.510 --> 00:01:08.520 align:start position:0%
agent so they're able to collaborate and
 

00:01:08.520 --> 00:01:10.350 align:start position:0%
agent so they're able to collaborate and
consider<00:01:08.920><c> everybody's</c>

00:01:10.350 --> 00:01:10.360 align:start position:0%
consider everybody's
 

00:01:10.360 --> 00:01:12.550 align:start position:0%
consider everybody's
input<00:01:11.360><c> and</c><00:01:11.479><c> not</c><00:01:11.640><c> only</c><00:01:11.799><c> do</c><00:01:11.960><c> they</c><00:01:12.119><c> talk</c><00:01:12.240><c> to</c><00:01:12.400><c> one</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
input and not only do they talk to one
 

00:01:12.560 --> 00:01:15.789 align:start position:0%
input and not only do they talk to one
another<00:01:12.960><c> but</c><00:01:13.200><c> you</c><00:01:13.680><c> can</c><00:01:13.880><c> speak</c><00:01:14.119><c> with</c><00:01:14.320><c> them</c><00:01:14.560><c> as</c>

00:01:15.789 --> 00:01:15.799 align:start position:0%
another but you can speak with them as
 

00:01:15.799 --> 00:01:18.230 align:start position:0%
another but you can speak with them as
well<00:01:16.799><c> let's</c><00:01:17.000><c> ask</c><00:01:17.200><c> our</c><00:01:17.400><c> web</c><00:01:17.600><c> designer</c><00:01:17.920><c> to</c><00:01:18.080><c> write</c>

00:01:18.230 --> 00:01:18.240 align:start position:0%
well let's ask our web designer to write
 

00:01:18.240 --> 00:01:20.230 align:start position:0%
well let's ask our web designer to write
some<00:01:18.439><c> code</c><00:01:18.720><c> for</c><00:01:18.920><c> us</c><00:01:19.479><c> they</c><00:01:19.560><c> should</c><00:01:19.759><c> take</c><00:01:19.920><c> into</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
some code for us they should take into
 

00:01:20.240 --> 00:01:22.149 align:start position:0%
some code for us they should take into
account<00:01:20.640><c> everything</c><00:01:21.000><c> that's</c><00:01:21.200><c> been</c><00:01:21.400><c> discussed</c>

00:01:22.149 --> 00:01:22.159 align:start position:0%
account everything that's been discussed
 

00:01:22.159 --> 00:01:23.670 align:start position:0%
account everything that's been discussed
including<00:01:22.600><c> the</c><00:01:22.720><c> additional</c><00:01:23.159><c> instructions</c>

00:01:23.670 --> 00:01:23.680 align:start position:0%
including the additional instructions
 

00:01:23.680 --> 00:01:25.910 align:start position:0%
including the additional instructions
being<00:01:23.960><c> typed</c><00:01:24.200><c> in</c>

00:01:25.910 --> 00:01:25.920 align:start position:0%
being typed in
 

00:01:25.920 --> 00:01:28.310 align:start position:0%
being typed in
below<00:01:26.920><c> again</c><00:01:27.159><c> you're</c><00:01:27.320><c> seeing</c><00:01:27.680><c> this</c><00:01:27.880><c> happen</c><00:01:28.119><c> in</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
below again you're seeing this happen in
 

00:01:28.320 --> 00:01:31.469 align:start position:0%
below again you're seeing this happen in
real<00:01:28.600><c> time</c><00:01:29.159><c> autog</c><00:01:29.960><c> really</c><00:01:30.200><c> is</c><00:01:30.439><c> that</c><00:01:30.720><c> fast</c><00:01:31.320><c> but</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
real time autog really is that fast but
 

00:01:31.479 --> 00:01:32.990 align:start position:0%
real time autog really is that fast but
now<00:01:31.640><c> I'm</c><00:01:31.720><c> going</c><00:01:31.840><c> to</c><00:01:32.000><c> speed</c><00:01:32.280><c> up</c><00:01:32.479><c> the</c><00:01:32.600><c> video</c>

00:01:32.990 --> 00:01:33.000 align:start position:0%
now I'm going to speed up the video
 

00:01:33.000 --> 00:01:35.230 align:start position:0%
now I'm going to speed up the video
while<00:01:33.159><c> I</c><00:01:33.360><c> copy</c><00:01:33.600><c> and</c><00:01:33.799><c> paste</c><00:01:34.079><c> the</c><00:01:34.240><c> HTML</c>

00:01:35.230 --> 00:01:35.240 align:start position:0%
while I copy and paste the HTML
 

00:01:35.240 --> 00:01:39.640 align:start position:0%
while I copy and paste the HTML
JavaScript<00:01:35.880><c> and</c><00:01:36.079><c> CSS</c><00:01:36.840><c> into</c><00:01:37.119><c> this</c><00:01:37.320><c> online</c>

00:01:39.640 --> 00:01:39.650 align:start position:0%
JavaScript and CSS into this online
 

00:01:39.650 --> 00:01:42.350 align:start position:0%
JavaScript and CSS into this online
[Music]

00:01:42.350 --> 00:01:42.360 align:start position:0%
[Music]
 

00:01:42.360 --> 00:01:44.830 align:start position:0%
[Music]
renderer<00:01:43.360><c> and</c><00:01:43.520><c> there</c><00:01:43.640><c> you</c><00:01:43.799><c> have</c><00:01:43.920><c> it</c><00:01:44.520><c> perhaps</c>

00:01:44.830 --> 00:01:44.840 align:start position:0%
renderer and there you have it perhaps
 

00:01:44.840 --> 00:01:46.550 align:start position:0%
renderer and there you have it perhaps
the<00:01:45.000><c> most</c><00:01:45.240><c> authoritative</c><00:01:45.920><c> website</c><00:01:46.280><c> on</c><00:01:46.399><c> the</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
the most authoritative website on the
 

00:01:46.560 --> 00:01:48.789 align:start position:0%
the most authoritative website on the
topic<00:01:47.000><c> all</c><00:01:47.200><c> from</c><00:01:47.439><c> one</c><00:01:47.719><c> request</c><00:01:48.280><c> and</c><00:01:48.439><c> a</c><00:01:48.560><c> few</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
topic all from one request and a few
 

00:01:48.799 --> 00:01:51.789 align:start position:0%
topic all from one request and a few
clicks<00:01:49.680><c> but</c><00:01:49.840><c> hold</c><00:01:50.040><c> on</c><00:01:50.960><c> this</c><00:01:51.079><c> is</c><00:01:51.240><c> just</c><00:01:51.399><c> the</c>

00:01:51.789 --> 00:01:51.799 align:start position:0%
clicks but hold on this is just the
 

00:01:51.799 --> 00:01:53.630 align:start position:0%
clicks but hold on this is just the
beginning<00:01:52.799><c> you</c><00:01:52.920><c> can</c><00:01:53.079><c> download</c><00:01:53.439><c> all</c><00:01:53.520><c> the</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
beginning you can download all the
 

00:01:53.640 --> 00:01:55.510 align:start position:0%
beginning you can download all the
agents<00:01:54.000><c> we've</c><00:01:54.200><c> been</c><00:01:54.320><c> working</c><00:01:54.640><c> with</c><00:01:55.079><c> and</c><00:01:55.360><c> an</c>

00:01:55.510 --> 00:01:55.520 align:start position:0%
agents we've been working with and an
 

00:01:55.520 --> 00:01:57.630 align:start position:0%
agents we've been working with and an
autogen<00:01:56.039><c> compatible</c><00:01:56.479><c> workflow</c><00:01:56.960><c> file</c><00:01:57.320><c> just</c><00:01:57.479><c> by</c>

00:01:57.630 --> 00:01:57.640 align:start position:0%
autogen compatible workflow file just by
 

00:01:57.640 --> 00:01:59.910 align:start position:0%
autogen compatible workflow file just by
clicking<00:01:57.920><c> on</c><00:01:58.119><c> them</c><00:01:58.960><c> they're</c><00:01:59.119><c> easily</c><00:01:59.399><c> imported</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
clicking on them they're easily imported
 

00:01:59.920 --> 00:02:01.670 align:start position:0%
clicking on them they're easily imported
into<00:02:00.079><c> autogen</c><00:02:00.719><c> unfortunately</c><00:02:01.280><c> there's</c><00:02:01.479><c> no</c>

00:02:01.670 --> 00:02:01.680 align:start position:0%
into autogen unfortunately there's no
 

00:02:01.680 --> 00:02:03.389 align:start position:0%
into autogen unfortunately there's no
batch<00:02:01.960><c> upload</c><00:02:02.280><c> in</c><00:02:02.399><c> autogen</c><00:02:02.920><c> yet</c><00:02:03.079><c> so</c><00:02:03.200><c> we</c><00:02:03.320><c> have</c>

00:02:03.389 --> 00:02:03.399 align:start position:0%
batch upload in autogen yet so we have
 

00:02:03.399 --> 00:02:05.950 align:start position:0%
batch upload in autogen yet so we have
to<00:02:03.520><c> do</c><00:02:03.719><c> these</c><00:02:03.920><c> one</c><00:02:04.119><c> by</c><00:02:04.320><c> one</c><00:02:05.039><c> still</c><00:02:05.520><c> it's</c><00:02:05.680><c> much</c>

00:02:05.950 --> 00:02:05.960 align:start position:0%
to do these one by one still it's much
 

00:02:05.960 --> 00:02:07.910 align:start position:0%
to do these one by one still it's much
faster<00:02:06.280><c> than</c><00:02:06.439><c> creating</c><00:02:06.799><c> all</c><00:02:07.000><c> these</c><00:02:07.159><c> agents</c>

00:02:07.910 --> 00:02:07.920 align:start position:0%
faster than creating all these agents
 

00:02:07.920 --> 00:02:10.029 align:start position:0%
faster than creating all these agents
manually<00:02:08.920><c> I'll</c><00:02:09.119><c> fast</c><00:02:09.319><c> forward</c><00:02:09.640><c> through</c><00:02:09.840><c> this</c>

00:02:10.029 --> 00:02:10.039 align:start position:0%
manually I'll fast forward through this
 

00:02:10.039 --> 00:02:12.520 align:start position:0%
manually I'll fast forward through this
part<00:02:10.239><c> too</c><00:02:10.840><c> you</c><00:02:10.920><c> can</c><00:02:11.120><c> thank</c><00:02:11.280><c> me</c>

00:02:12.520 --> 00:02:12.530 align:start position:0%
part too you can thank me
 

00:02:12.530 --> 00:02:22.309 align:start position:0%
part too you can thank me
[Music]

00:02:22.309 --> 00:02:22.319 align:start position:0%
[Music]
 

00:02:22.319 --> 00:02:24.350 align:start position:0%
[Music]
later<00:02:23.319><c> we'll</c><00:02:23.519><c> give</c><00:02:23.640><c> autogen</c><00:02:24.239><c> the</c>

00:02:24.350 --> 00:02:24.360 align:start position:0%
later we'll give autogen the
 

00:02:24.360 --> 00:02:26.309 align:start position:0%
later we'll give autogen the
re-engineered<00:02:25.040><c> prompt</c><00:02:25.480><c> created</c><00:02:25.800><c> for</c><00:02:25.959><c> us</c><00:02:26.160><c> by</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
re-engineered prompt created for us by
 

00:02:26.319 --> 00:02:30.390 align:start position:0%
re-engineered prompt created for us by
Auto<00:02:26.720><c> Gro</c><00:02:27.480><c> and</c><00:02:27.599><c> we'll</c><00:02:27.800><c> make</c><00:02:28.000><c> one</c><00:02:28.280><c> small</c><00:02:28.840><c> change</c>

00:02:30.390 --> 00:02:30.400 align:start position:0%
Auto Gro and we'll make one small change
 

00:02:30.400 --> 00:02:32.430 align:start position:0%
Auto Gro and we'll make one small change
here's<00:02:30.599><c> where</c><00:02:30.760><c> the</c><00:02:30.879><c> difference</c><00:02:31.200><c> is</c><00:02:31.440><c> clear</c>

00:02:32.430 --> 00:02:32.440 align:start position:0%
here's where the difference is clear
 

00:02:32.440 --> 00:02:36.070 align:start position:0%
here's where the difference is clear
while<00:02:32.599><c> our</c><00:02:32.760><c> autogen</c><00:02:33.360><c> is</c><00:02:33.480><c> using</c><00:02:33.760><c> the</c><00:02:33.920><c> chat</c><00:02:34.239><c> GPT</c>

00:02:36.070 --> 00:02:36.080 align:start position:0%
while our autogen is using the chat GPT
 

00:02:36.080 --> 00:02:38.430 align:start position:0%
while our autogen is using the chat GPT
4.0m<00:02:37.080><c> it</c><00:02:37.160><c> can</c><00:02:37.319><c> take</c><00:02:37.440><c> up</c><00:02:37.560><c> to</c><00:02:37.720><c> 10</c><00:02:38.000><c> minutes</c><00:02:38.280><c> for</c>

00:02:38.430 --> 00:02:38.440 align:start position:0%
4.0m it can take up to 10 minutes for
 

00:02:38.440 --> 00:02:40.750 align:start position:0%
4.0m it can take up to 10 minutes for
the<00:02:38.560><c> team</c><00:02:38.800><c> to</c><00:02:39.000><c> tackle</c><00:02:39.319><c> our</c><00:02:39.519><c> request</c><00:02:40.319><c> compare</c>

00:02:40.750 --> 00:02:40.760 align:start position:0%
the team to tackle our request compare
 

00:02:40.760 --> 00:02:43.589 align:start position:0%
the team to tackle our request compare
that<00:02:41.000><c> with</c><00:02:41.239><c> how</c><00:02:41.680><c> Auto</c><00:02:42.080><c> Gro</c><00:02:42.480><c> had</c><00:02:42.640><c> our</c><00:02:42.840><c> website</c>

00:02:43.589 --> 00:02:43.599 align:start position:0%
that with how Auto Gro had our website
 

00:02:43.599 --> 00:02:46.270 align:start position:0%
that with how Auto Gro had our website
code<00:02:43.879><c> ready</c><00:02:44.120><c> for</c><00:02:44.280><c> us</c><00:02:44.400><c> in</c><00:02:44.560><c> mere</c><00:02:44.840><c> seconds</c><00:02:45.519><c> still</c>

00:02:46.270 --> 00:02:46.280 align:start position:0%
code ready for us in mere seconds still
 

00:02:46.280 --> 00:02:48.350 align:start position:0%
code ready for us in mere seconds still
our<00:02:46.480><c> autogen</c><00:02:47.080><c> results</c><00:02:47.640><c> are</c><00:02:48.040><c> pretty</c>

00:02:48.350 --> 00:02:48.360 align:start position:0%
our autogen results are pretty
 

00:02:48.360 --> 00:02:50.589 align:start position:0%
our autogen results are pretty
impressive<00:02:49.200><c> it</c><00:02:49.360><c> has</c><00:02:49.519><c> produced</c><00:02:49.920><c> a</c><00:02:50.159><c> lot</c><00:02:50.360><c> of</c>

00:02:50.589 --> 00:02:50.599 align:start position:0%
impressive it has produced a lot of
 

00:02:50.599 --> 00:02:52.430 align:start position:0%
impressive it has produced a lot of
content<00:02:51.000><c> for</c><00:02:51.200><c> us</c><00:02:51.680><c> and</c><00:02:51.800><c> we</c><00:02:51.920><c> didn't</c><00:02:52.159><c> have</c><00:02:52.280><c> to</c>

00:02:52.430 --> 00:02:52.440 align:start position:0%
content for us and we didn't have to
 

00:02:52.440 --> 00:02:55.550 align:start position:0%
content for us and we didn't have to
create<00:02:52.760><c> a</c><00:02:52.920><c> single</c><00:02:53.280><c> agent</c><00:02:53.640><c> or</c><00:02:54.080><c> workflow</c><00:02:54.640><c> file</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
create a single agent or workflow file
 

00:02:55.560 --> 00:02:59.680 align:start position:0%
create a single agent or workflow file
ourselves<00:02:56.560><c> thanks</c><00:02:56.840><c> autog</c><00:02:57.159><c> GR</c>

