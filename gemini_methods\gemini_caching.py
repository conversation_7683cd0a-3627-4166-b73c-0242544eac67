"""
Gemini Context Caching Utilities (Aligned with Official API)

This module provides asynchronous functions for managing Gemini CachedContent
using the official `client.caches` API.

Key Concepts:
- Caches are model-specific.
- Caches store input tokens (from text, system instructions, files) for reuse.
- Caching can reduce costs for repetitive prompts with large contexts.
- TTL (Time-To-Live) is managed by the API.

Functions provide wrappers around:
- `client.caches.create()`
- `client.caches.get()`
- `client.caches.list()`
- `client.caches.update()` (primarily for TTL/expiry)
- `client.caches.delete()`
"""

import logging
import datetime
from typing import Optional, Union, List

# Use the official google.genai SDK
from google import genai
from google.genai import types as genai_types # Use alias to avoid potential conflicts
from google.api_core import exceptions as google_exceptions
from google.protobuf.field_mask_pb2 import FieldMask # Import FieldMask

# Assuming genai_utils provides the initialized async client
# from .genai_utils import initialize_async_client (Import where needed, or pass client)

logger = logging.getLogger(__name__) # Use standard logging

# --- Constants --- (Example, adjust as needed)
DEFAULT_CACHE_TTL = datetime.timedelta(hours=1) # Default TTL if not specified

# --- Core Caching Functions --- #

async def create_cache(
    client: genai.client.Client, # Pass the initialized AsyncClient (client.aio)
    model_name: str,
    contents: Union[genai_types.ContentDict, List[genai_types.ContentDict]], # Correct type hint
    system_instruction: Optional[str] = None,
    display_name: Optional[str] = None,
    ttl: Optional[Union[str, datetime.timedelta]] = DEFAULT_CACHE_TTL,
) -> Optional[str]:
    """
    Creates a new CachedContent entry using the official API.

    Args:
        client: The initialized google-genai asynchronous client (client.aio).
        model_name: The specific model name (e.g., 'models/gemini-1.5-flash-001').
                    Note: Model must support caching.
        contents: The content to cache (e.g., list of Parts including file URIs).
        system_instruction: Optional system instruction text to include in the cache.
        display_name: Optional display name for identifying the cache.
        ttl: Time-to-live for the cache (datetime.timedelta, or ISO 8601 duration string like '300s').
             Defaults to DEFAULT_CACHE_TTL.

    Returns:
        The cache name (e.g., 'cachedContents/...') if successful, otherwise None.
    """
    try:
        logger.info(f"Attempting to create cache for model '{model_name}' with display name: '{display_name}' and TTL: {ttl}")
 
        # Ensure model name has 'models/' prefix if not present
        if not model_name.startswith('models/'):
            model_name = f'models/{model_name}'
 
        # Convert TTL to string format if necessary
        ttl_str = ttl
        if isinstance(ttl, datetime.timedelta):
            ttl_str = f"{int(ttl.total_seconds())}s"
 
        logger.info(f"Constructing CachedContent for creation with model '{model_name}', display name: '{display_name}', TTL: {ttl_str}")
 
        # Revert AGAIN: Construct CachedContent object based on persistent lint errors
        cache_content_data = genai_types.CachedContent(
            display_name=display_name,
            system_instruction=system_instruction,  # type: ignore
            contents=contents,  # type: ignore
            ttl=ttl_str  # type: ignore
        )
        cache_response = await client.caches.create(
            # Note: We are deliberately ignoring the lint error here as it contradicts SDK structure
            cached_content=cache_content_data,  # type: ignore
            model=model_name,
        )
        cache_name = getattr(cache_response, 'name', None)

        if cache_name:
            token_count = getattr(getattr(cache_response, 'usage_metadata', None), 'total_token_count', 'N/A')
            logger.info(f"Successfully created cache '{cache_name}' ({token_count} tokens) for model '{model_name}'.")
            return cache_name
        else:
            logger.error("Cache creation response did not contain a name.")
            return None

    except google_exceptions.GoogleAPICallError as e:
        logger.error(f"API error creating cache for model '{model_name}': {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error creating cache for model '{model_name}': {e}", exc_info=True)
        return None

async def get_cache(
    client: genai.client.Client,
    cache_name: str
) -> Optional[genai_types.CachedContent]:
    """
    Retrieves metadata for a specific cache by its name.

    Args:
        client: The initialized google-genai asynchronous client.
        cache_name: The full name of the cache (e.g., 'cachedContents/...').

    Returns:
        The CachedContent object if found, otherwise None.
    """
    try:
        logger.debug(f"Attempting to get cache metadata for: {cache_name}")
        cache = client.caches.get(name=cache_name)
        logger.debug(f"Successfully retrieved cache metadata for: {cache_name}")
        return cache
    except google_exceptions.NotFound:
        logger.warning(f"Cache not found: {cache_name}")
        return None
    except google_exceptions.GoogleAPICallError as e:
        logger.error(f"API error getting cache '{cache_name}': {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting cache '{cache_name}': {e}", exc_info=True)
        return None

def list_caches(
    client: genai.client.Client,
) -> List[genai_types.CachedContent]:
    """
    Lists metadata for all available caches by iterating through the pager.

    Args:
        client: The initialized google.generativeai client.

    Returns:
        A list of CachedContent objects.
    """
    all_caches = []
    try:
        logger.info(f"Listing available caches.")
        # client.caches.list() returns a synchronous Pager
        pager = client.caches.list()
        for cache in pager: # Iterate directly over the Pager
            all_caches.append(cache)
        logger.info(f"Found {len(all_caches)} caches.")
        return all_caches
    except google_exceptions.GoogleAPICallError as e:
        logger.error(f"API Error listing caches: {e}", exc_info=True)
        return [] # Return empty list on error
    except Exception as e:
        logger.error(f"Unexpected error listing caches: {e}", exc_info=True)
        return []

async def update_cache_ttl(
    client: genai.client.Client,
    cache_name: str,
    new_ttl: Optional[Union[str, datetime.timedelta]] = None,
    expire_time: Optional[datetime.datetime] = None,
) -> bool:
    """
    Updates the Time-To-Live (TTL) or specific expire time of a cache.
    Provide *either* new_ttl *or* expire_time.

    Args:
        client: The initialized google-genai asynchronous client.
        cache_name: The name of the cache to update.
        new_ttl: The new TTL (timedelta or ISO 8601 duration string).
        expire_time: The specific UTC expiration time (timezone-aware datetime).

    Returns:
        True if the update was successful, False otherwise.
    """
    if not (new_ttl is None) ^ (expire_time is None):
        logger.error("Must provide exactly one of 'new_ttl' or 'expire_time' for update.")
        return False

    config = {}
    update_mask_paths = []
    expire_time_str = None # Initialize

    if new_ttl is not None:
        if isinstance(new_ttl, datetime.timedelta):
            ttl_str = f"{int(new_ttl.total_seconds())}s" # Convert to ISO string
        else:
            ttl_str = new_ttl # Pass TTL as string
        config['ttl'] = ttl_str
        update_mask_paths.append('ttl')
        logger.info(f"Attempting to update cache '{cache_name}' with ttl: {ttl_str}")
    elif expire_time is not None:
        expire_time_str = expire_time.isoformat() + "Z" # Convert to ISO string (RFC 3339 format often needs 'Z')
        config['expire_time'] = expire_time_str
        update_mask_paths.append('expire_time')
        logger.info(f"Attempting to update cache '{cache_name}' with expire_time: {expire_time_str}")

    if not config: # Check if config dict is empty
        logger.error("No update field (ttl or expire_time) added to config.")
        return False

    try:
        update_mask = FieldMask(paths=update_mask_paths)
        logger.info(f"Updating cache '{cache_name}' with config: {config}, update_mask: {update_mask.paths}")
        # Note: update() is synchronous based on SDK definition
        # We are deliberately ignoring the lint error for update_mask as it seems incorrect.
        response = client.caches.update(
            name=cache_name,
            config=config,
            update_mask=update_mask  # type: ignore
        )
        logger.info(f"Update response for cache '{cache_name}': {response}")
        logger.info(f"Successfully updated cache '{cache_name}'.")
        return True
    except google_exceptions.GoogleAPICallError as e:
        logger.error(f"API error updating cache '{cache_name}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error updating cache '{cache_name}': {e}", exc_info=True)
        return False

async def delete_cache(
    client: genai.client.Client,
    cache_name: str
) -> bool:
    """
    Deletes a specific cache by its name.

    Args:
        client: The initialized google-genai asynchronous client.
        cache_name: The name of the cache to delete.

    Returns:
        True if deletion was successful, False otherwise.
    """
    try:
        logger.warning(f"Attempting to delete cache: {cache_name}")
        client.caches.delete(name=cache_name) # REMOVE await
        logger.info(f"Successfully deleted cache: {cache_name}")
        return True
    except google_exceptions.NotFound:
        logger.warning(f"Cache not found for deletion: {cache_name}")
        return False # Or True, depending on desired idempotency semantics
    except google_exceptions.GoogleAPICallError as e:
        logger.error(f"API error deleting cache '{cache_name}': {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error deleting cache '{cache_name}': {e}", exc_info=True)
        return False

# --- Example Usage Placeholder --- #

# async def example_usage(client, model_name, file_manager):
#     # 1. Upload files using file_manager -> get file_parts (Parts with URIs)
#     # 2. Define system instruction
#     # 3. Create cache
#     cache_name = await create_cache(
#         client=client,
#         model_name=model_name, # e.g., 'models/gemini-1.5-flash-001'
#         contents=[system_instruction_part, file_part1, file_part2],
#         display_name="My Document Cache",
#         ttl=datetime.timedelta(minutes=30)
#     )
#     # 4. If cache_name exists, use it in generate_content
#     if cache_name:
#         response = await client.models.generate_content(
#             model=model_name,
#             contents=["Summarize the key points from the documents."],
#             config=types.GenerateContentConfig(cached_content=cache_name)
#         )
#         print(response.text)
#         print(response.usage_metadata)
#     # 5. Optionally delete the cache later
#     # await delete_cache(client, cache_name)


# --- Deprecated / Removed Functions from old implementation --- #
# - _generate_cache_key
# - find_existing_cache (replaced by list_caches/get_cache)
# - create_new_cache (replaced by create_cache)
# - get_or_create_cache (caller should orchestrate get/create)
# - cleanup_expired_caches (API handles TTL)
# - _save_gemini_cache_to_db (No longer needed)
