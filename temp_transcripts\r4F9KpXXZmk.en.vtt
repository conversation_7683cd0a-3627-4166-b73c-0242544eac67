WEBVTT
Kind: captions
Language: en

00:00:01.680 --> 00:00:03.750 align:start position:0%
 
Hi,<00:00:02.080><c> my</c><00:00:02.240><c> name's</c><00:00:02.560><c> Jay</c><00:00:02.879><c> and</c><00:00:03.120><c> welcome</c><00:00:03.360><c> to</c><00:00:03.520><c> my</c>

00:00:03.750 --> 00:00:03.760 align:start position:0%
Hi, my name's <PERSON> and welcome to my
 

00:00:03.760 --> 00:00:07.790 align:start position:0%
Hi, my name's <PERSON> and welcome to my
brief<00:00:04.080><c> tutorial</c><00:00:04.720><c> on</c>

00:00:07.790 --> 00:00:07.800 align:start position:0%
 
 

00:00:07.800 --> 00:00:10.390 align:start position:0%
 
namespaces.<00:00:08.800><c> Why</c><00:00:09.040><c> do</c><00:00:09.200><c> we</c><00:00:09.360><c> have</c><00:00:09.519><c> namespaces?</c>

00:00:10.390 --> 00:00:10.400 align:start position:0%
namespaces. Why do we have namespaces?
 

00:00:10.400 --> 00:00:11.629 align:start position:0%
namespaces. Why do we have namespaces?
Why<00:00:10.559><c> do</c><00:00:10.719><c> we</c><00:00:10.880><c> need</c>

00:00:11.629 --> 00:00:11.639 align:start position:0%
Why do we need
 

00:00:11.639 --> 00:00:14.549 align:start position:0%
Why do we need
namespaces?<00:00:12.639><c> Simply</c><00:00:12.960><c> enough,</c><00:00:13.679><c> it's</c><00:00:14.160><c> so</c><00:00:14.400><c> that</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
namespaces? Simply enough, it's so that
 

00:00:14.559 --> 00:00:18.029 align:start position:0%
namespaces? Simply enough, it's so that
we<00:00:14.799><c> can</c><00:00:14.960><c> tell</c><00:00:15.360><c> one</c><00:00:15.679><c> thing</c><00:00:15.839><c> apart</c><00:00:16.240><c> from</c><00:00:16.400><c> the</c>

00:00:18.029 --> 00:00:18.039 align:start position:0%
we can tell one thing apart from the
 

00:00:18.039 --> 00:00:20.550 align:start position:0%
we can tell one thing apart from the
next.<00:00:19.039><c> Here</c><00:00:19.199><c> are</c><00:00:19.359><c> two</c><00:00:19.600><c> houses,</c><00:00:20.000><c> the</c><00:00:20.160><c> Smith's</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
next. Here are two houses, the Smith's
 

00:00:20.560 --> 00:00:22.630 align:start position:0%
next. Here are two houses, the Smith's
house<00:00:20.720><c> and</c><00:00:20.880><c> the</c><00:00:21.039><c> Jones</c><00:00:21.439><c> house.</c><00:00:22.080><c> If</c><00:00:22.320><c> I</c><00:00:22.480><c> asked</c>

00:00:22.630 --> 00:00:22.640 align:start position:0%
house and the Jones house. If I asked
 

00:00:22.640 --> 00:00:23.990 align:start position:0%
house and the Jones house. If I asked
you<00:00:22.800><c> to</c><00:00:22.960><c> get</c><00:00:23.039><c> me</c><00:00:23.119><c> a</c><00:00:23.279><c> spatula</c><00:00:23.680><c> from</c><00:00:23.920><c> the</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
you to get me a spatula from the
 

00:00:24.000 --> 00:00:25.269 align:start position:0%
you to get me a spatula from the
kitchen,<00:00:24.320><c> you'd</c><00:00:24.560><c> probably</c><00:00:24.800><c> say,</c><00:00:24.960><c> "Well,</c>

00:00:25.269 --> 00:00:25.279 align:start position:0%
kitchen, you'd probably say, "Well,
 

00:00:25.279 --> 00:00:27.349 align:start position:0%
kitchen, you'd probably say, "Well,
which<00:00:25.519><c> kitchen?</c><00:00:25.840><c> Which</c><00:00:26.080><c> house?"</c><00:00:27.039><c> That's</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
which kitchen? Which house?" That's
 

00:00:27.359 --> 00:00:29.669 align:start position:0%
which kitchen? Which house?" That's
because<00:00:27.680><c> my</c><00:00:28.000><c> request</c><00:00:28.640><c> for</c><00:00:28.960><c> just</c><00:00:29.199><c> simply</c><00:00:29.519><c> a</c>

00:00:29.669 --> 00:00:29.679 align:start position:0%
because my request for just simply a
 

00:00:29.679 --> 00:00:33.150 align:start position:0%
because my request for just simply a
spatula<00:00:30.640><c> was</c>

00:00:33.150 --> 00:00:33.160 align:start position:0%
 
 

00:00:33.160 --> 00:00:35.670 align:start position:0%
 
ambiguous.<00:00:34.160><c> What</c><00:00:34.399><c> a</c><00:00:34.559><c> namespace</c><00:00:35.200><c> does</c><00:00:35.360><c> is</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
ambiguous. What a namespace does is
 

00:00:35.680 --> 00:00:38.069 align:start position:0%
ambiguous. What a namespace does is
eliminate<00:00:36.160><c> this</c><00:00:36.399><c> sort</c><00:00:36.559><c> of</c><00:00:37.079><c> ambiguity.</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
eliminate this sort of ambiguity.
 

00:00:38.079 --> 00:00:41.030 align:start position:0%
eliminate this sort of ambiguity.
Programmers<00:00:38.800><c> use</c><00:00:39.600><c> namespaces</c><00:00:40.399><c> to</c><00:00:40.559><c> avoid</c><00:00:40.879><c> this</c>

00:00:41.030 --> 00:00:41.040 align:start position:0%
Programmers use namespaces to avoid this
 

00:00:41.040 --> 00:00:43.430 align:start position:0%
Programmers use namespaces to avoid this
sort<00:00:41.200><c> of</c><00:00:41.360><c> confusion.</c><00:00:42.160><c> Both</c><00:00:42.480><c> houses</c><00:00:42.960><c> very</c><00:00:43.280><c> well</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
sort of confusion. Both houses very well
 

00:00:43.440 --> 00:00:45.590 align:start position:0%
sort of confusion. Both houses very well
might<00:00:43.680><c> have</c><00:00:43.760><c> a</c><00:00:44.000><c> spatula</c><00:00:44.399><c> in</c><00:00:44.559><c> the</c><00:00:44.719><c> kitchen,</c><00:00:45.360><c> but</c>

00:00:45.590 --> 00:00:45.600 align:start position:0%
might have a spatula in the kitchen, but
 

00:00:45.600 --> 00:00:47.790 align:start position:0%
might have a spatula in the kitchen, but
to<00:00:45.760><c> tell</c><00:00:45.920><c> them</c><00:00:46.079><c> apart,</c><00:00:46.800><c> we</c><00:00:47.039><c> use</c>

00:00:47.790 --> 00:00:47.800 align:start position:0%
to tell them apart, we use
 

00:00:47.800 --> 00:00:49.590 align:start position:0%
to tell them apart, we use
namespaces.<00:00:48.800><c> Taking</c><00:00:49.120><c> a</c><00:00:49.280><c> look</c><00:00:49.360><c> at</c><00:00:49.440><c> the</c>

00:00:49.590 --> 00:00:49.600 align:start position:0%
namespaces. Taking a look at the
 

00:00:49.600 --> 00:00:51.590 align:start position:0%
namespaces. Taking a look at the
namespace<00:00:50.079><c> I've</c><00:00:50.399><c> assigned</c><00:00:50.640><c> to</c><00:00:50.879><c> this</c><00:00:51.039><c> spatula,</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
namespace I've assigned to this spatula,
 

00:00:51.600 --> 00:00:53.590 align:start position:0%
namespace I've assigned to this spatula,
you<00:00:51.760><c> can</c><00:00:51.920><c> probably</c><00:00:52.320><c> guess</c><00:00:52.719><c> whose</c><00:00:53.039><c> spatula</c>

00:00:53.590 --> 00:00:53.600 align:start position:0%
you can probably guess whose spatula
 

00:00:53.600 --> 00:00:56.229 align:start position:0%
you can probably guess whose spatula
this<00:00:53.840><c> is.</c><00:00:54.879><c> And</c><00:00:55.039><c> you'd</c><00:00:55.360><c> also</c><00:00:55.680><c> be</c><00:00:55.760><c> able</c><00:00:55.920><c> to</c><00:00:56.079><c> tell</c>

00:00:56.229 --> 00:00:56.239 align:start position:0%
this is. And you'd also be able to tell
 

00:00:56.239 --> 00:00:58.389 align:start position:0%
this is. And you'd also be able to tell
where<00:00:56.480><c> they</c><00:00:56.719><c> kept</c><00:00:56.960><c> it.</c><00:00:57.760><c> You'll</c><00:00:58.000><c> notice</c><00:00:58.239><c> that</c>

00:00:58.389 --> 00:00:58.399 align:start position:0%
where they kept it. You'll notice that
 

00:00:58.399 --> 00:01:00.709 align:start position:0%
where they kept it. You'll notice that
the<00:00:58.559><c> name</c><00:00:58.879><c> space,</c><00:00:59.280><c> Smith,</c><00:00:59.840><c> house,</c><00:01:00.320><c> kitchen,</c>

00:01:00.709 --> 00:01:00.719 align:start position:0%
the name space, Smith, house, kitchen,
 

00:01:00.719 --> 00:01:02.869 align:start position:0%
the name space, Smith, house, kitchen,
drawers,<00:01:01.359><c> utensils,</c><00:01:02.079><c> finally</c><00:01:02.399><c> leading</c><00:01:02.719><c> down</c>

00:01:02.869 --> 00:01:02.879 align:start position:0%
drawers, utensils, finally leading down
 

00:01:02.879 --> 00:01:04.630 align:start position:0%
drawers, utensils, finally leading down
to<00:01:03.039><c> our</c><00:01:03.199><c> object,</c><00:01:03.600><c> our</c><00:01:03.760><c> spatula.</c><00:01:04.479><c> The</c>

00:01:04.630 --> 00:01:04.640 align:start position:0%
to our object, our spatula. The
 

00:01:04.640 --> 00:01:08.030 align:start position:0%
to our object, our spatula. The
namespace<00:01:05.240><c> itself</c><00:01:06.240><c> gets</c><00:01:06.880><c> more</c><00:01:07.119><c> and</c><00:01:07.360><c> more</c>

00:01:08.030 --> 00:01:08.040 align:start position:0%
namespace itself gets more and more
 

00:01:08.040 --> 00:01:11.030 align:start position:0%
namespace itself gets more and more
refined<00:01:09.040><c> as</c><00:01:09.360><c> it</c><00:01:09.600><c> progresses.</c><00:01:10.240><c> It</c><00:01:10.479><c> starts</c><00:01:10.720><c> very</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
refined as it progresses. It starts very
 

00:01:11.040 --> 00:01:14.149 align:start position:0%
refined as it progresses. It starts very
generally<00:01:11.600><c> with</c><00:01:11.760><c> a</c><00:01:12.000><c> category</c><00:01:12.400><c> of</c><00:01:12.920><c> smith</c><00:01:13.920><c> and</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
generally with a category of smith and
 

00:01:14.159 --> 00:01:17.149 align:start position:0%
generally with a category of smith and
works<00:01:14.400><c> its</c><00:01:14.640><c> way</c><00:01:14.799><c> all</c><00:01:14.880><c> the</c><00:01:15.040><c> way</c><00:01:15.119><c> down</c><00:01:15.360><c> to</c>

00:01:17.149 --> 00:01:17.159 align:start position:0%
works its way all the way down to
 

00:01:17.159 --> 00:01:19.510 align:start position:0%
works its way all the way down to
spatula.<00:01:18.159><c> Aside</c><00:01:18.479><c> from</c><00:01:18.720><c> just</c><00:01:18.960><c> eliminating</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
spatula. Aside from just eliminating
 

00:01:19.520 --> 00:01:22.070 align:start position:0%
spatula. Aside from just eliminating
ambiguity,<00:01:20.240><c> namespaces</c><00:01:21.040><c> help</c><00:01:21.439><c> keep</c><00:01:21.680><c> things</c>

00:01:22.070 --> 00:01:22.080 align:start position:0%
ambiguity, namespaces help keep things
 

00:01:22.080 --> 00:01:23.590 align:start position:0%
ambiguity, namespaces help keep things
organized.

00:01:23.590 --> 00:01:23.600 align:start position:0%
organized.
 

00:01:23.600 --> 00:01:25.590 align:start position:0%
organized.
There's<00:01:24.000><c> no</c><00:01:24.240><c> need</c><00:01:24.400><c> for</c><00:01:24.560><c> a</c><00:01:24.799><c> program</c><00:01:25.360><c> to</c>

00:01:25.590 --> 00:01:25.600 align:start position:0%
There's no need for a program to
 

00:01:25.600 --> 00:01:27.830 align:start position:0%
There's no need for a program to
reference<00:01:26.080><c> every</c><00:01:26.479><c> possible</c><00:01:27.119><c> piece</c><00:01:27.360><c> of</c><00:01:27.600><c> code</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
reference every possible piece of code
 

00:01:27.840 --> 00:01:30.630 align:start position:0%
reference every possible piece of code
at<00:01:28.080><c> its</c><00:01:28.400><c> disposal</c><00:01:29.119><c> if</c><00:01:29.360><c> we</c><00:01:29.520><c> use</c><00:01:29.759><c> namespaces</c><00:01:30.479><c> to</c>

00:01:30.630 --> 00:01:30.640 align:start position:0%
at its disposal if we use namespaces to
 

00:01:30.640 --> 00:01:33.429 align:start position:0%
at its disposal if we use namespaces to
tell<00:01:30.799><c> it</c><00:01:31.040><c> specifically</c><00:01:31.920><c> which</c><00:01:32.240><c> tool</c><00:01:32.640><c> using</c><00:01:33.200><c> on</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
tell it specifically which tool using on
 

00:01:33.439 --> 00:01:37.550 align:start position:0%
tell it specifically which tool using on
which<00:01:33.680><c> page</c><00:01:34.079><c> and</c>

00:01:37.550 --> 00:01:37.560 align:start position:0%
 
 

00:01:37.560 --> 00:01:40.789 align:start position:0%
 
when.<00:01:38.640><c> When</c><00:01:38.880><c> we</c><00:01:39.040><c> declare</c><00:01:39.439><c> a</c><00:01:39.680><c> namespace</c><00:01:40.400><c> at</c><00:01:40.640><c> the</c>

00:01:40.789 --> 00:01:40.799 align:start position:0%
when. When we declare a namespace at the
 

00:01:40.799 --> 00:01:43.510 align:start position:0%
when. When we declare a namespace at the
top<00:01:41.200><c> of</c><00:01:41.439><c> one</c><00:01:41.680><c> of</c><00:01:41.759><c> our</c><00:01:42.000><c> programming</c><00:01:42.479><c> pages</c><00:01:43.280><c> with</c>

00:01:43.510 --> 00:01:43.520 align:start position:0%
top of one of our programming pages with
 

00:01:43.520 --> 00:01:45.670 align:start position:0%
top of one of our programming pages with
the<00:01:43.680><c> keyword</c><00:01:44.159><c> using,</c><00:01:44.880><c> what</c><00:01:45.040><c> we're</c><00:01:45.280><c> doing</c><00:01:45.439><c> is</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
the keyword using, what we're doing is
 

00:01:45.680 --> 00:01:48.630 align:start position:0%
the keyword using, what we're doing is
we're<00:01:45.920><c> invoking</c><00:01:46.720><c> the</c><00:01:47.040><c> using</c><00:01:47.640><c> namespace</c>

00:01:48.630 --> 00:01:48.640 align:start position:0%
we're invoking the using namespace
 

00:01:48.640 --> 00:01:50.789 align:start position:0%
we're invoking the using namespace
directive,<00:01:49.439><c> which</c><00:01:49.680><c> is</c><00:01:49.759><c> the</c><00:01:49.920><c> way</c><00:01:50.079><c> we</c><00:01:50.320><c> turn</c><00:01:50.560><c> big</c>

00:01:50.789 --> 00:01:50.799 align:start position:0%
directive, which is the way we turn big
 

00:01:50.799 --> 00:01:53.109 align:start position:0%
directive, which is the way we turn big
long<00:01:51.200><c> ugly</c><00:01:51.640><c> namespaces</c><00:01:52.640><c> into</c><00:01:52.960><c> more</c>

00:01:53.109 --> 00:01:53.119 align:start position:0%
long ugly namespaces into more
 

00:01:53.119 --> 00:01:56.230 align:start position:0%
long ugly namespaces into more
user-friendly<00:01:53.759><c> aliases,</c><00:01:54.720><c> as</c><00:01:55.040><c> shown</c><00:01:55.360><c> here.</c>

00:01:56.230 --> 00:01:56.240 align:start position:0%
user-friendly aliases, as shown here.
 

00:01:56.240 --> 00:01:58.590 align:start position:0%
user-friendly aliases, as shown here.
Instead<00:01:56.560><c> of</c><00:01:56.720><c> having</c><00:01:56.960><c> to</c><00:01:57.119><c> type</c>

00:01:58.590 --> 00:01:58.600 align:start position:0%
Instead of having to type
 

00:01:58.600 --> 00:02:00.310 align:start position:0%
Instead of having to type
system.drawing.drawing2D<00:01:59.600><c> every</c><00:01:59.920><c> time</c><00:02:00.079><c> we</c>

00:02:00.310 --> 00:02:00.320 align:start position:0%
system.drawing.drawing2D every time we
 

00:02:00.320 --> 00:02:02.789 align:start position:0%
system.drawing.drawing2D every time we
want<00:02:00.399><c> to</c><00:02:00.560><c> use</c><00:02:00.719><c> the</c><00:02:00.880><c> drawing</c><00:02:01.360><c> 2D</c><00:02:01.759><c> class,</c><00:02:02.399><c> we</c><00:02:02.640><c> can</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
want to use the drawing 2D class, we can
 

00:02:02.799 --> 00:02:05.590 align:start position:0%
want to use the drawing 2D class, we can
simply<00:02:03.200><c> alias</c><00:02:03.680><c> it</c><00:02:04.079><c> with</c><00:02:04.320><c> the</c><00:02:04.479><c> word</c><00:02:04.719><c> using.</c><00:02:05.360><c> And</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
simply alias it with the word using. And
 

00:02:05.600 --> 00:02:07.749 align:start position:0%
simply alias it with the word using. And
from<00:02:05.840><c> this</c><00:02:06.079><c> point</c><00:02:06.320><c> forward,</c><00:02:07.040><c> when</c><00:02:07.280><c> we</c><00:02:07.439><c> simply</c>

00:02:07.749 --> 00:02:07.759 align:start position:0%
from this point forward, when we simply
 

00:02:07.759 --> 00:02:11.270 align:start position:0%
from this point forward, when we simply
use<00:02:08.160><c> 2D</c><00:02:08.640><c> in</c><00:02:08.959><c> our</c><00:02:09.319><c> program,</c><00:02:10.319><c> the</c><00:02:10.560><c> compiler</c><00:02:11.039><c> in</c>

00:02:11.270 --> 00:02:11.280 align:start position:0%
use 2D in our program, the compiler in
 

00:02:11.280 --> 00:02:14.550 align:start position:0%
use 2D in our program, the compiler in
our<00:02:11.520><c> computer</c><00:02:12.400><c> will</c><00:02:12.879><c> recognize</c><00:02:13.520><c> that</c><00:02:14.080><c> we</c><00:02:14.400><c> want</c>

00:02:14.550 --> 00:02:14.560 align:start position:0%
our computer will recognize that we want
 

00:02:14.560 --> 00:02:15.790 align:start position:0%
our computer will recognize that we want
to<00:02:14.720><c> use</c>

00:02:15.790 --> 00:02:15.800 align:start position:0%
to use
 

00:02:15.800 --> 00:02:18.070 align:start position:0%
to use
system.drawing.drawing<00:02:16.800><c> 2D</c><00:02:17.520><c> without</c><00:02:17.920><c> having</c>

00:02:18.070 --> 00:02:18.080 align:start position:0%
system.drawing.drawing 2D without having
 

00:02:18.080 --> 00:02:19.830 align:start position:0%
system.drawing.drawing 2D without having
to<00:02:18.239><c> type</c><00:02:18.480><c> all</c><00:02:18.640><c> of</c><00:02:18.800><c> that.</c><00:02:19.040><c> We</c><00:02:19.200><c> do</c><00:02:19.360><c> this</c><00:02:19.520><c> all</c><00:02:19.680><c> the</c>

00:02:19.830 --> 00:02:19.840 align:start position:0%
to type all of that. We do this all the
 

00:02:19.840 --> 00:02:22.550 align:start position:0%
to type all of that. We do this all the
time<00:02:20.000><c> in</c><00:02:20.400><c> in</c><00:02:20.720><c> ordinary</c><00:02:21.200><c> life.</c><00:02:21.599><c> We</c><00:02:21.840><c> say</c><00:02:22.319><c> I'm</c>

00:02:22.550 --> 00:02:22.560 align:start position:0%
time in in ordinary life. We say I'm
 

00:02:22.560 --> 00:02:24.710 align:start position:0%
time in in ordinary life. We say I'm
going<00:02:22.800><c> next</c><00:02:23.120><c> door.</c><00:02:23.599><c> We</c><00:02:23.840><c> don't</c><00:02:24.000><c> say</c><00:02:24.239><c> I'm</c><00:02:24.480><c> going</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
going next door. We don't say I'm going
 

00:02:24.720 --> 00:02:26.869 align:start position:0%
going next door. We don't say I'm going
to<00:02:25.200><c> our</c><00:02:25.520><c> next</c><00:02:25.760><c> door</c><00:02:25.920><c> neighbors,</c><00:02:26.239><c> the</c><00:02:26.480><c> Stannis</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
to our next door neighbors, the Stannis
 

00:02:26.879 --> 00:02:29.270 align:start position:0%
to our next door neighbors, the Stannis
Papadopoulos<00:02:27.680><c> family's</c><00:02:28.080><c> house.</c><00:02:28.879><c> And</c><00:02:29.040><c> what</c>

00:02:29.270 --> 00:02:29.280 align:start position:0%
Papadopoulos family's house. And what
 

00:02:29.280 --> 00:02:31.390 align:start position:0%
Papadopoulos family's house. And what
we've<00:02:29.520><c> done</c><00:02:29.599><c> there</c><00:02:29.840><c> is</c><00:02:30.080><c> simply</c><00:02:30.800><c> create</c><00:02:31.040><c> an</c>

00:02:31.390 --> 00:02:31.400 align:start position:0%
we've done there is simply create an
 

00:02:31.400 --> 00:02:34.309 align:start position:0%
we've done there is simply create an
alias<00:02:32.400><c> meaning</c><00:02:32.800><c> next</c><00:02:33.040><c> door</c><00:02:33.360><c> is</c><00:02:33.680><c> our</c><00:02:33.920><c> neighbors</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
alias meaning next door is our neighbors
 

00:02:34.319 --> 00:02:36.470 align:start position:0%
alias meaning next door is our neighbors
the<00:02:34.480><c> Stannis</c><00:02:34.879><c> Papadopoulos's</c><00:02:35.840><c> house.</c><00:02:36.239><c> It</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
the Stannis Papadopoulos's house. It
 

00:02:36.480 --> 00:02:39.190 align:start position:0%
the Stannis Papadopoulos's house. It
saves<00:02:36.720><c> us</c><00:02:36.879><c> a</c><00:02:37.040><c> lot</c><00:02:37.120><c> of</c><00:02:37.720><c> talking.</c><00:02:38.720><c> Likewise,</c>

00:02:39.190 --> 00:02:39.200 align:start position:0%
saves us a lot of talking. Likewise,
 

00:02:39.200 --> 00:02:41.509 align:start position:0%
saves us a lot of talking. Likewise,
namespace<00:02:39.840><c> aliases</c><00:02:40.480><c> and</c><00:02:40.640><c> programming</c><00:02:41.280><c> can</c>

00:02:41.509 --> 00:02:41.519 align:start position:0%
namespace aliases and programming can
 

00:02:41.519 --> 00:02:44.350 align:start position:0%
namespace aliases and programming can
save<00:02:41.760><c> us</c><00:02:41.840><c> a</c><00:02:42.000><c> lot</c><00:02:42.080><c> of</c>

00:02:44.350 --> 00:02:44.360 align:start position:0%
 
 

00:02:44.360 --> 00:02:46.790 align:start position:0%
 
typing.<00:02:45.360><c> Keep</c><00:02:45.519><c> in</c><00:02:45.680><c> mind</c><00:02:45.760><c> that</c><00:02:46.000><c> a</c><00:02:46.160><c> namespace</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
typing. Keep in mind that a namespace
 

00:02:46.800 --> 00:02:49.509 align:start position:0%
typing. Keep in mind that a namespace
must<00:02:47.200><c> exist.</c><00:02:48.400><c> If</c><00:02:48.640><c> we</c><00:02:48.800><c> try</c><00:02:48.959><c> to</c><00:02:49.120><c> declare</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
must exist. If we try to declare
 

00:02:49.519 --> 00:02:51.350 align:start position:0%
must exist. If we try to declare
something<00:02:49.840><c> that</c><00:02:50.160><c> doesn't</c><00:02:50.480><c> exist,</c><00:02:50.959><c> in</c><00:02:51.200><c> this</c>

00:02:51.350 --> 00:02:51.360 align:start position:0%
something that doesn't exist, in this
 

00:02:51.360 --> 00:02:53.430 align:start position:0%
something that doesn't exist, in this
case<00:02:51.680><c> the</c><00:02:51.920><c> Brownhouse,</c><00:02:52.480><c> when</c><00:02:52.800><c> only</c><00:02:52.959><c> the</c><00:02:53.200><c> Smith</c>

00:02:53.430 --> 00:02:53.440 align:start position:0%
case the Brownhouse, when only the Smith
 

00:02:53.440 --> 00:02:55.910 align:start position:0%
case the Brownhouse, when only the Smith
and<00:02:53.680><c> Jones</c><00:02:54.000><c> house</c><00:02:54.319><c> exists,</c><00:02:55.280><c> our</c><00:02:55.519><c> compiler</c>

00:02:55.910 --> 00:02:55.920 align:start position:0%
and Jones house exists, our compiler
 

00:02:55.920 --> 00:02:58.070 align:start position:0%
and Jones house exists, our compiler
will<00:02:56.160><c> throw</c><00:02:56.400><c> an</c><00:02:56.720><c> error</c><00:02:57.280><c> because</c><00:02:57.599><c> there</c><00:02:57.840><c> isn't</c>

00:02:58.070 --> 00:02:58.080 align:start position:0%
will throw an error because there isn't
 

00:02:58.080 --> 00:03:00.070 align:start position:0%
will throw an error because there isn't
a<00:02:58.200><c> Brownhouse.</c><00:02:59.200><c> But</c><00:02:59.360><c> as</c><00:02:59.519><c> soon</c><00:02:59.680><c> as</c><00:02:59.760><c> somebody</c>

00:03:00.070 --> 00:03:00.080 align:start position:0%
a Brownhouse. But as soon as somebody
 

00:03:00.080 --> 00:03:02.070 align:start position:0%
a Brownhouse. But as soon as somebody
builds<00:03:00.319><c> the</c><00:03:00.480><c> Brownhouse</c><00:03:00.959><c> and</c><00:03:01.200><c> we</c><00:03:01.360><c> add</c><00:03:01.519><c> it</c><00:03:01.680><c> as</c><00:03:01.840><c> a</c>

00:03:02.070 --> 00:03:02.080 align:start position:0%
builds the Brownhouse and we add it as a
 

00:03:02.080 --> 00:03:03.910 align:start position:0%
builds the Brownhouse and we add it as a
reference,<00:03:02.480><c> we'll</c><00:03:02.800><c> be</c><00:03:02.879><c> able</c><00:03:03.040><c> to</c><00:03:03.200><c> use</c><00:03:03.360><c> it</c><00:03:03.680><c> in</c>

00:03:03.910 --> 00:03:03.920 align:start position:0%
reference, we'll be able to use it in
 

00:03:03.920 --> 00:03:06.229 align:start position:0%
reference, we'll be able to use it in
our<00:03:04.159><c> computer</c><00:03:04.760><c> program.</c><00:03:05.760><c> You</c><00:03:05.920><c> might</c><00:03:06.080><c> be</c>

00:03:06.229 --> 00:03:06.239 align:start position:0%
our computer program. You might be
 

00:03:06.239 --> 00:03:08.710 align:start position:0%
our computer program. You might be
wondering<00:03:06.560><c> how</c><00:03:06.720><c> we</c><00:03:06.879><c> add</c><00:03:07.120><c> references.</c>

00:03:08.710 --> 00:03:08.720 align:start position:0%
wondering how we add references.
 

00:03:08.720 --> 00:03:10.390 align:start position:0%
wondering how we add references.
Well,<00:03:08.959><c> I'll</c><00:03:09.200><c> tell</c><00:03:09.360><c> you</c><00:03:09.760><c> in</c><00:03:10.000><c> our</c><00:03:10.239><c> next</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
Well, I'll tell you in our next
 

00:03:10.400 --> 00:03:14.400 align:start position:0%
Well, I'll tell you in our next
tutorial,<00:03:11.280><c> assemblies</c><00:03:11.840><c> and</c><00:03:12.000><c> references.</c>

