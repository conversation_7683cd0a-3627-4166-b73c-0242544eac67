WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.230 align:start position:0%
 
today<00:00:00.199><c> we're</c><00:00:00.320><c> going</c><00:00:00.399><c> to</c><00:00:00.480><c> learn</c><00:00:00.680><c> how</c><00:00:00.760><c> to</c><00:00:00.919><c> use</c>

00:00:01.230 --> 00:00:01.240 align:start position:0%
today we're going to learn how to use
 

00:00:01.240 --> 00:00:03.590 align:start position:0%
today we're going to learn how to use
text<00:00:01.560><c> to</c><00:00:01.800><c> video</c><00:00:02.399><c> well</c><00:00:02.560><c> in</c><00:00:02.679><c> this</c><00:00:02.840><c> case</c><00:00:03.080><c> text</c><00:00:03.320><c> to</c>

00:00:03.590 --> 00:00:03.600 align:start position:0%
text to video well in this case text to
 

00:00:03.600 --> 00:00:06.470 align:start position:0%
text to video well in this case text to
GIF<00:00:04.040><c> not</c><00:00:04.279><c> GIF</c><00:00:04.720><c> using</c><00:00:05.400><c> just</c><00:00:05.600><c> a</c><00:00:05.759><c> hugging</c><00:00:06.160><c> face</c>

00:00:06.470 --> 00:00:06.480 align:start position:0%
GIF not GIF using just a hugging face
 

00:00:06.480 --> 00:00:07.869 align:start position:0%
GIF not GIF using just a hugging face
model<00:00:06.919><c> now</c><00:00:07.040><c> hugging</c><00:00:07.359><c> face</c><00:00:07.560><c> if</c><00:00:07.600><c> you're</c><00:00:07.720><c> not</c>

00:00:07.869 --> 00:00:07.879 align:start position:0%
model now hugging face if you're not
 

00:00:07.879 --> 00:00:10.270 align:start position:0%
model now hugging face if you're not
familiar<00:00:08.280><c> with</c><00:00:08.400><c> it</c><00:00:08.639><c> has</c><00:00:09.040><c> ton</c><00:00:09.280><c> of</c><00:00:09.480><c> models</c><00:00:09.880><c> as</c><00:00:10.040><c> of</c>

00:00:10.270 --> 00:00:10.280 align:start position:0%
familiar with it has ton of models as of
 

00:00:10.280 --> 00:00:28.950 align:start position:0%
familiar with it has ton of models as of
just<00:00:10.440><c> the</c><00:00:10.519><c> other</c><00:00:10.719><c> day</c><00:00:10.880><c> has</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
 
 

00:00:28.960 --> 00:00:32.190 align:start position:0%
 
537,500<00:00:30.080><c> it</c><00:00:30.199><c> created</c><00:00:30.759><c> here</c><00:00:30.880><c> is</c><00:00:31.279><c> I</c><00:00:31.439><c> had</c><00:00:31.759><c> Mario</c>

00:00:32.190 --> 00:00:32.200 align:start position:0%
537,500 it created here is I had Mario
 

00:00:32.200 --> 00:00:34.030 align:start position:0%
537,500 it created here is I had Mario
Brothers<00:00:32.719><c> jumping</c><00:00:33.399><c> it</c><00:00:33.559><c> actually</c><00:00:33.760><c> looks</c><00:00:33.920><c> like</c>

00:00:34.030 --> 00:00:34.040 align:start position:0%
Brothers jumping it actually looks like
 

00:00:34.040 --> 00:00:35.549 align:start position:0%
Brothers jumping it actually looks like
they're<00:00:34.239><c> kissing</c><00:00:34.680><c> I</c><00:00:34.760><c> don't</c><00:00:34.920><c> know</c><00:00:35.280><c> what's</c>

00:00:35.549 --> 00:00:35.559 align:start position:0%
they're kissing I don't know what's
 

00:00:35.559 --> 00:00:37.229 align:start position:0%
they're kissing I don't know what's
going<00:00:35.760><c> on</c><00:00:36.000><c> here</c><00:00:36.360><c> and</c><00:00:36.480><c> then</c><00:00:36.640><c> I</c><00:00:36.760><c> had</c><00:00:36.920><c> something</c>

00:00:37.229 --> 00:00:37.239 align:start position:0%
going on here and then I had something
 

00:00:37.239 --> 00:00:39.310 align:start position:0%
going on here and then I had something
about<00:00:37.559><c> the</c><00:00:37.719><c> city</c><00:00:38.120><c> skylines</c><00:00:38.680><c> and</c><00:00:38.840><c> it</c><00:00:39.000><c> create</c>

00:00:39.310 --> 00:00:39.320 align:start position:0%
about the city skylines and it create
 

00:00:39.320 --> 00:00:41.150 align:start position:0%
about the city skylines and it create
this<00:00:39.480><c> gift</c><00:00:39.719><c> so</c><00:00:40.000><c> here</c><00:00:40.120><c> are</c><00:00:40.280><c> some</c><00:00:40.480><c> examples</c><00:00:40.920><c> of</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
this gift so here are some examples of
 

00:00:41.160 --> 00:00:43.229 align:start position:0%
this gift so here are some examples of
what<00:00:41.399><c> you</c><00:00:41.640><c> can</c><00:00:42.000><c> create</c><00:00:42.520><c> so</c><00:00:42.680><c> let's</c><00:00:42.840><c> get</c><00:00:42.960><c> started</c>

00:00:43.229 --> 00:00:43.239 align:start position:0%
what you can create so let's get started
 

00:00:43.239 --> 00:00:44.830 align:start position:0%
what you can create so let's get started
and<00:00:43.360><c> see</c><00:00:43.559><c> what</c><00:00:43.680><c> we</c><00:00:43.760><c> need</c><00:00:43.879><c> to</c><00:00:44.000><c> do</c><00:00:44.440><c> okay</c><00:00:44.600><c> well</c><00:00:44.719><c> the</c>

00:00:44.830 --> 00:00:44.840 align:start position:0%
and see what we need to do okay well the
 

00:00:44.840 --> 00:00:46.350 align:start position:0%
and see what we need to do okay well the
first<00:00:45.000><c> thing</c><00:00:45.079><c> you</c><00:00:45.160><c> need</c><00:00:45.280><c> to</c><00:00:45.440><c> do</c><00:00:45.760><c> is</c><00:00:45.960><c> install</c>

00:00:46.350 --> 00:00:46.360 align:start position:0%
first thing you need to do is install
 

00:00:46.360 --> 00:00:48.630 align:start position:0%
first thing you need to do is install
the<00:00:46.559><c> requirements</c><00:00:47.440><c> I</c><00:00:47.559><c> have</c><00:00:47.680><c> a</c><00:00:47.840><c> requirements.</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
the requirements I have a requirements.
 

00:00:48.640 --> 00:00:50.869 align:start position:0%
the requirements I have a requirements.
text<00:00:48.920><c> file</c><00:00:49.399><c> in</c><00:00:49.640><c> this</c><00:00:49.879><c> directory</c><00:00:50.280><c> for</c><00:00:50.480><c> the</c><00:00:50.640><c> text</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
text file in this directory for the text
 

00:00:50.879 --> 00:00:53.110 align:start position:0%
text file in this directory for the text
to<00:00:51.120><c> GIF</c><00:00:51.680><c> you</c><00:00:51.840><c> just</c><00:00:51.960><c> need</c><00:00:52.079><c> to</c><00:00:52.239><c> type</c><00:00:52.440><c> pip</c><00:00:52.680><c> install</c>

00:00:53.110 --> 00:00:53.120 align:start position:0%
to GIF you just need to type pip install
 

00:00:53.120 --> 00:00:55.150 align:start position:0%
to GIF you just need to type pip install
dasr<00:00:53.520><c> requirements.</c><00:00:54.199><c> text</c><00:00:54.640><c> and</c><00:00:54.840><c> you'll</c><00:00:55.000><c> be</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
dasr requirements. text and you'll be
 

00:00:55.160 --> 00:00:56.950 align:start position:0%
dasr requirements. text and you'll be
good<00:00:55.280><c> to</c><00:00:55.440><c> go</c><00:00:55.680><c> there</c><00:00:55.800><c> is</c><00:00:55.920><c> no</c><00:00:56.160><c> inference</c><00:00:56.640><c> server</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
good to go there is no inference server
 

00:00:56.960 --> 00:00:58.229 align:start position:0%
good to go there is no inference server
so<00:00:57.120><c> we</c><00:00:57.199><c> have</c><00:00:57.320><c> to</c><00:00:57.440><c> actually</c><00:00:57.640><c> use</c><00:00:57.920><c> all</c><00:00:58.079><c> the</c>

00:00:58.229 --> 00:00:58.239 align:start position:0%
so we have to actually use all the
 

00:00:58.239 --> 00:01:00.430 align:start position:0%
so we have to actually use all the
python<00:00:58.640><c> code</c><00:00:58.920><c> that's</c><00:00:59.160><c> needed</c><00:00:59.600><c> to</c><00:01:00.000><c> create</c><00:01:00.320><c> this</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
python code that's needed to create this
 

00:01:00.440 --> 00:01:01.950 align:start position:0%
python code that's needed to create this
text<00:01:00.840><c> video</c><00:01:01.120><c> so</c><00:01:01.199><c> the</c><00:01:01.320><c> first</c><00:01:01.440><c> thing</c><00:01:01.519><c> you</c><00:01:01.640><c> need</c>

00:01:01.950 --> 00:01:01.960 align:start position:0%
text video so the first thing you need
 

00:01:01.960 --> 00:01:03.869 align:start position:0%
text video so the first thing you need
is<00:01:02.160><c> all</c><00:01:02.399><c> the</c><00:01:02.519><c> Imports</c><00:01:03.120><c> which</c><00:01:03.239><c> is</c><00:01:03.480><c> really</c><00:01:03.760><c> just</c>

00:01:03.869 --> 00:01:03.879 align:start position:0%
is all the Imports which is really just
 

00:01:03.879 --> 00:01:05.990 align:start position:0%
is all the Imports which is really just
a<00:01:03.960><c> lot</c><00:01:04.080><c> of</c><00:01:04.320><c> diffusers</c><00:01:05.080><c> and</c><00:01:05.280><c> you</c><00:01:05.600><c> basically</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
a lot of diffusers and you basically
 

00:01:06.000 --> 00:01:07.749 align:start position:0%
a lot of diffusers and you basically
need<00:01:06.360><c> if</c><00:01:06.439><c> you're</c><00:01:06.560><c> doing</c><00:01:06.799><c> something</c><00:01:07.080><c> like</c><00:01:07.360><c> this</c>

00:01:07.749 --> 00:01:07.759 align:start position:0%
need if you're doing something like this
 

00:01:07.759 --> 00:01:09.670 align:start position:0%
need if you're doing something like this
and<00:01:07.880><c> not</c><00:01:08.000><c> using</c><00:01:08.200><c> an</c><00:01:08.280><c> inference</c><00:01:08.640><c> server</c><00:01:09.159><c> you</c>

00:01:09.670 --> 00:01:09.680 align:start position:0%
and not using an inference server you
 

00:01:09.680 --> 00:01:12.149 align:start position:0%
and not using an inference server you
probably<00:01:10.080><c> always</c><00:01:10.400><c> need</c><00:01:11.000><c> uh</c><00:01:11.240><c> Transformers</c><00:01:11.960><c> and</c>

00:01:12.149 --> 00:01:12.159 align:start position:0%
probably always need uh Transformers and
 

00:01:12.159 --> 00:01:13.390 align:start position:0%
probably always need uh Transformers and
anything<00:01:12.439><c> with</c><00:01:12.640><c> like</c><00:01:12.840><c> image</c><00:01:13.119><c> you're</c><00:01:13.240><c> going</c><00:01:13.320><c> to</c>

00:01:13.390 --> 00:01:13.400 align:start position:0%
anything with like image you're going to
 

00:01:13.400 --> 00:01:14.950 align:start position:0%
anything with like image you're going to
need<00:01:13.520><c> diffusers</c><00:01:14.280><c> okay</c><00:01:14.400><c> and</c><00:01:14.520><c> I'm</c><00:01:14.600><c> not</c><00:01:14.720><c> going</c><00:01:14.840><c> to</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
need diffusers okay and I'm not going to
 

00:01:14.960 --> 00:01:17.469 align:start position:0%
need diffusers okay and I'm not going to
go<00:01:15.159><c> through</c><00:01:15.680><c> all</c><00:01:15.920><c> the</c><00:01:16.040><c> lines</c><00:01:16.280><c> of</c><00:01:16.479><c> code</c><00:01:16.880><c> here</c>

00:01:17.469 --> 00:01:17.479 align:start position:0%
go through all the lines of code here
 

00:01:17.479 --> 00:01:19.630 align:start position:0%
go through all the lines of code here
but<00:01:17.720><c> basically</c><00:01:18.200><c> we</c><00:01:18.400><c> create</c><00:01:18.759><c> this</c><00:01:19.000><c> pipeline</c>

00:01:19.630 --> 00:01:19.640 align:start position:0%
but basically we create this pipeline
 

00:01:19.640 --> 00:01:21.870 align:start position:0%
but basically we create this pipeline
using<00:01:20.040><c> the</c><00:01:20.240><c> animate</c><00:01:20.840><c> diffusion</c><00:01:21.439><c> pipeline</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
using the animate diffusion pipeline
 

00:01:21.880 --> 00:01:23.710 align:start position:0%
using the animate diffusion pipeline
that<00:01:22.040><c> they</c><00:01:22.240><c> have</c><00:01:22.600><c> and</c><00:01:22.680><c> they</c><00:01:22.840><c> pre-trained</c><00:01:23.479><c> all</c>

00:01:23.710 --> 00:01:23.720 align:start position:0%
that they have and they pre-trained all
 

00:01:23.720 --> 00:01:25.390 align:start position:0%
that they have and they pre-trained all
this<00:01:23.920><c> from</c><00:01:24.159><c> these</c><00:01:24.360><c> data</c><00:01:24.640><c> sets</c><00:01:25.079><c> and</c><00:01:25.159><c> what</c><00:01:25.280><c> we</c>

00:01:25.390 --> 00:01:25.400 align:start position:0%
this from these data sets and what we
 

00:01:25.400 --> 00:01:26.550 align:start position:0%
this from these data sets and what we
really<00:01:25.560><c> need</c><00:01:25.680><c> to</c><00:01:25.799><c> know</c><00:01:26.000><c> is</c><00:01:26.159><c> this</c><00:01:26.320><c> pipe</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
really need to know is this pipe
 

00:01:26.560 --> 00:01:28.910 align:start position:0%
really need to know is this pipe
function<00:01:27.159><c> this</c><00:01:27.320><c> takes</c><00:01:27.520><c> a</c><00:01:27.720><c> prompt</c><00:01:28.479><c> which</c><00:01:28.640><c> is</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
function this takes a prompt which is
 

00:01:28.920 --> 00:01:30.510 align:start position:0%
function this takes a prompt which is
the<00:01:29.119><c> first</c><00:01:29.439><c> gift</c><00:01:29.680><c> that</c><00:01:29.960><c> you</c><00:01:30.040><c> saw</c><00:01:30.280><c> about</c><00:01:30.439><c> the</c>

00:01:30.510 --> 00:01:30.520 align:start position:0%
the first gift that you saw about the
 

00:01:30.520 --> 00:01:32.749 align:start position:0%
the first gift that you saw about the
space<00:01:30.840><c> rocket</c><00:01:31.360><c> it</c><00:01:31.479><c> has</c><00:01:31.560><c> a</c><00:01:31.720><c> negative</c><00:01:32.079><c> prompt</c>

00:01:32.749 --> 00:01:32.759 align:start position:0%
space rocket it has a negative prompt
 

00:01:32.759 --> 00:01:34.590 align:start position:0%
space rocket it has a negative prompt
the<00:01:32.920><c> number</c><00:01:33.240><c> of</c><00:01:33.479><c> frames</c><00:01:33.880><c> so</c><00:01:34.000><c> you</c><00:01:34.079><c> can</c><00:01:34.240><c> increase</c>

00:01:34.590 --> 00:01:34.600 align:start position:0%
the number of frames so you can increase
 

00:01:34.600 --> 00:01:36.429 align:start position:0%
the number of frames so you can increase
this<00:01:34.720><c> so</c><00:01:34.880><c> you</c><00:01:34.960><c> can</c><00:01:35.119><c> have</c><00:01:35.280><c> a</c><00:01:35.479><c> longer</c><00:01:35.920><c> video</c><00:01:36.280><c> for</c>

00:01:36.429 --> 00:01:36.439 align:start position:0%
this so you can have a longer video for
 

00:01:36.439 --> 00:01:37.710 align:start position:0%
this so you can have a longer video for
the<00:01:36.600><c> guidance</c><00:01:36.880><c> scale</c><00:01:37.200><c> I</c><00:01:37.280><c> seen</c><00:01:37.520><c> where</c><00:01:37.600><c> it's</c>

00:01:37.710 --> 00:01:37.720 align:start position:0%
the guidance scale I seen where it's
 

00:01:37.720 --> 00:01:39.510 align:start position:0%
the guidance scale I seen where it's
actually<00:01:37.960><c> recommended</c><00:01:38.360><c> to</c><00:01:38.479><c> use</c><00:01:38.680><c> 3.0</c><00:01:39.280><c> instead</c>

00:01:39.510 --> 00:01:39.520 align:start position:0%
actually recommended to use 3.0 instead
 

00:01:39.520 --> 00:01:40.990 align:start position:0%
actually recommended to use 3.0 instead
of<00:01:39.640><c> 2.0</c><00:01:40.280><c> and</c><00:01:40.360><c> if</c><00:01:40.399><c> you're</c><00:01:40.520><c> curious</c><00:01:40.799><c> what</c><00:01:40.880><c> the</c>

00:01:40.990 --> 00:01:41.000 align:start position:0%
of 2.0 and if you're curious what the
 

00:01:41.000 --> 00:01:43.749 align:start position:0%
of 2.0 and if you're curious what the
guidance<00:01:41.360><c> scale</c><00:01:41.799><c> is</c><00:01:42.240><c> basically</c><00:01:43.240><c> a</c><00:01:43.439><c> higher</c>

00:01:43.749 --> 00:01:43.759 align:start position:0%
guidance scale is basically a higher
 

00:01:43.759 --> 00:01:45.429 align:start position:0%
guidance scale is basically a higher
value<00:01:44.079><c> encourages</c><00:01:44.520><c> the</c><00:01:44.640><c> model</c><00:01:44.880><c> to</c><00:01:45.040><c> generate</c>

00:01:45.429 --> 00:01:45.439 align:start position:0%
value encourages the model to generate
 

00:01:45.439 --> 00:01:48.109 align:start position:0%
value encourages the model to generate
images<00:01:46.000><c> closely</c><00:01:46.479><c> linked</c><00:01:46.920><c> to</c><00:01:47.200><c> the</c><00:01:47.399><c> text</c><00:01:47.920><c> and</c><00:01:48.000><c> so</c>

00:01:48.109 --> 00:01:48.119 align:start position:0%
images closely linked to the text and so
 

00:01:48.119 --> 00:01:49.950 align:start position:0%
images closely linked to the text and so
you<00:01:48.200><c> don't</c><00:01:48.320><c> want</c><00:01:48.520><c> something</c><00:01:48.880><c> too</c><00:01:49.240><c> high</c><00:01:49.840><c> which</c>

00:01:49.950 --> 00:01:49.960 align:start position:0%
you don't want something too high which
 

00:01:49.960 --> 00:01:53.190 align:start position:0%
you don't want something too high which
is<00:01:50.200><c> why</c><00:01:50.399><c> I've</c><00:01:50.560><c> seen</c><00:01:51.399><c> 3.0</c><00:01:52.399><c> as</c><00:01:52.719><c> a</c><00:01:52.880><c> kind</c><00:01:53.000><c> of</c><00:01:53.079><c> a</c>

00:01:53.190 --> 00:01:53.200 align:start position:0%
is why I've seen 3.0 as a kind of a
 

00:01:53.200 --> 00:01:55.429 align:start position:0%
is why I've seen 3.0 as a kind of a
standard<00:01:53.680><c> well</c><00:01:53.960><c> after</c><00:01:54.200><c> reading</c><00:01:54.479><c> online</c><00:01:54.920><c> 3.0</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
standard well after reading online 3.0
 

00:01:55.439 --> 00:01:57.590 align:start position:0%
standard well after reading online 3.0
seems<00:01:55.680><c> to</c><00:01:55.799><c> be</c><00:01:56.280><c> a</c><00:01:56.479><c> good</c><00:01:56.719><c> choice</c><00:01:57.039><c> here</c><00:01:57.360><c> so</c><00:01:57.520><c> we</c>

00:01:57.590 --> 00:01:57.600 align:start position:0%
seems to be a good choice here so we
 

00:01:57.600 --> 00:01:59.550 align:start position:0%
seems to be a good choice here so we
have<00:01:57.759><c> 3.0</c><00:01:58.320><c> here</c><00:01:58.600><c> number</c><00:01:58.799><c> of</c><00:01:58.880><c> inference</c><00:01:59.280><c> steps</c>

00:01:59.550 --> 00:01:59.560 align:start position:0%
have 3.0 here number of inference steps
 

00:01:59.560 --> 00:02:01.109 align:start position:0%
have 3.0 here number of inference steps
and<00:01:59.680><c> then</c><00:01:59.960><c> the</c><00:02:00.119><c> generator</c><00:02:00.680><c> and</c><00:02:00.759><c> then</c><00:02:00.880><c> we</c><00:02:00.960><c> must</c>

00:02:01.109 --> 00:02:01.119 align:start position:0%
and then the generator and then we must
 

00:02:01.119 --> 00:02:03.429 align:start position:0%
and then the generator and then we must
say<00:02:01.280><c> frames</c><00:02:01.680><c> equals</c><00:02:02.000><c> output.</c><00:02:02.640><c> frames</c><00:02:02.920><c> of</c><00:02:03.119><c> zero</c>

00:02:03.429 --> 00:02:03.439 align:start position:0%
say frames equals output. frames of zero
 

00:02:03.439 --> 00:02:04.630 align:start position:0%
say frames equals output. frames of zero
so<00:02:03.600><c> this</c><00:02:03.680><c> is</c><00:02:03.759><c> going</c><00:02:03.880><c> to</c><00:02:04.039><c> get</c><00:02:04.280><c> if</c><00:02:04.360><c> you</c><00:02:04.479><c> have</c>

00:02:04.630 --> 00:02:04.640 align:start position:0%
so this is going to get if you have
 

00:02:04.640 --> 00:02:06.510 align:start position:0%
so this is going to get if you have
multiple<00:02:05.079><c> videos</c><00:02:05.600><c> you</c><00:02:05.719><c> know</c><00:02:06.119><c> this</c><00:02:06.240><c> is</c><00:02:06.360><c> an</c>

00:02:06.510 --> 00:02:06.520 align:start position:0%
multiple videos you know this is an
 

00:02:06.520 --> 00:02:07.830 align:start position:0%
multiple videos you know this is an
array<00:02:06.840><c> here</c><00:02:06.960><c> so</c><00:02:07.119><c> we're</c><00:02:07.320><c> just</c><00:02:07.479><c> basically</c>

00:02:07.830 --> 00:02:07.840 align:start position:0%
array here so we're just basically
 

00:02:07.840 --> 00:02:10.070 align:start position:0%
array here so we're just basically
getting<00:02:08.319><c> the</c><00:02:08.520><c> first</c><00:02:08.840><c> video</c><00:02:09.160><c> output</c><00:02:09.720><c> from</c><00:02:09.920><c> this</c>

00:02:10.070 --> 00:02:10.080 align:start position:0%
getting the first video output from this
 

00:02:10.080 --> 00:02:12.390 align:start position:0%
getting the first video output from this
array<00:02:10.640><c> and</c><00:02:10.759><c> then</c><00:02:10.920><c> from</c><00:02:11.160><c> diffusers.</c><00:02:11.879><c> utils</c>

00:02:12.390 --> 00:02:12.400 align:start position:0%
array and then from diffusers. utils
 

00:02:12.400 --> 00:02:14.589 align:start position:0%
array and then from diffusers. utils
they<00:02:12.560><c> have</c><00:02:12.720><c> an</c><00:02:12.920><c> export</c><00:02:13.360><c> to</c><00:02:13.840><c> GIF</c><00:02:14.120><c> function</c><00:02:14.519><c> that</c>

00:02:14.589 --> 00:02:14.599 align:start position:0%
they have an export to GIF function that
 

00:02:14.599 --> 00:02:16.350 align:start position:0%
they have an export to GIF function that
we<00:02:14.720><c> can</c><00:02:14.840><c> just</c><00:02:14.959><c> call</c><00:02:15.200><c> so</c><00:02:15.319><c> we</c><00:02:15.440><c> can</c><00:02:15.720><c> just</c><00:02:15.959><c> export</c>

00:02:16.350 --> 00:02:16.360 align:start position:0%
we can just call so we can just export
 

00:02:16.360 --> 00:02:19.509 align:start position:0%
we can just call so we can just export
the<00:02:16.519><c> frames</c><00:02:17.400><c> to</c><00:02:17.959><c> this</c><00:02:18.280><c> output</c><00:02:19.040><c> okay</c><00:02:19.160><c> so</c><00:02:19.319><c> I</c><00:02:19.400><c> just</c>

00:02:19.509 --> 00:02:19.519 align:start position:0%
the frames to this output okay so I just
 

00:02:19.519 --> 00:02:22.470 align:start position:0%
the frames to this output okay so I just
started<00:02:19.920><c> a</c><00:02:20.080><c> notebook</c><00:02:20.560><c> in</c><00:02:20.800><c> runpod</c><00:02:21.440><c> on</c><00:02:21.640><c> a</c><00:02:22.239><c> much</c>

00:02:22.470 --> 00:02:22.480 align:start position:0%
started a notebook in runpod on a much
 

00:02:22.480 --> 00:02:24.869 align:start position:0%
started a notebook in runpod on a much
better<00:02:22.800><c> machine</c><00:02:23.599><c> and</c><00:02:23.840><c> this</c><00:02:24.000><c> is</c><00:02:24.319><c> this</c><00:02:24.400><c> is</c><00:02:24.599><c> the</c>

00:02:24.869 --> 00:02:24.879 align:start position:0%
better machine and this is this is the
 

00:02:24.879 --> 00:02:26.869 align:start position:0%
better machine and this is this is the
same<00:02:25.440><c> exact</c><00:02:25.840><c> code</c><00:02:26.120><c> this</c><00:02:26.200><c> is</c><00:02:26.319><c> the</c><00:02:26.440><c> same</c><00:02:26.640><c> exact</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
same exact code this is the same exact
 

00:02:26.879 --> 00:02:29.830 align:start position:0%
same exact code this is the same exact
code<00:02:27.080><c> that</c><00:02:27.200><c> I</c><00:02:27.319><c> copy</c><00:02:27.680><c> locally</c><00:02:28.680><c> and</c><00:02:28.920><c> whenever</c><00:02:29.480><c> I</c>

00:02:29.830 --> 00:02:29.840 align:start position:0%
code that I copy locally and whenever I
 

00:02:29.840 --> 00:02:31.910 align:start position:0%
code that I copy locally and whenever I
called<00:02:30.040><c> it</c><00:02:30.160><c> to</c><00:02:30.400><c> export</c><00:02:31.080><c> you</c><00:02:31.239><c> see</c><00:02:31.400><c> it</c><00:02:31.480><c> has</c><00:02:31.599><c> to</c>

00:02:31.910 --> 00:02:31.920 align:start position:0%
called it to export you see it has to
 

00:02:31.920 --> 00:02:33.830 align:start position:0%
called it to export you see it has to
install<00:02:32.280><c> a</c><00:02:32.400><c> bunch</c><00:02:32.560><c> of</c><00:02:32.760><c> things</c><00:02:33.519><c> and</c><00:02:33.640><c> then</c>

00:02:33.830 --> 00:02:33.840 align:start position:0%
install a bunch of things and then
 

00:02:33.840 --> 00:02:35.630 align:start position:0%
install a bunch of things and then
eventually<00:02:34.519><c> we</c><00:02:34.680><c> come</c><00:02:34.920><c> down</c><00:02:35.120><c> here</c><00:02:35.360><c> and</c><00:02:35.480><c> it</c>

00:02:35.630 --> 00:02:35.640 align:start position:0%
eventually we come down here and it
 

00:02:35.640 --> 00:02:37.710 align:start position:0%
eventually we come down here and it
creates<00:02:36.000><c> it</c><00:02:36.360><c> and</c><00:02:36.480><c> then</c><00:02:36.720><c> here</c><00:02:37.000><c> is</c><00:02:37.160><c> the</c><00:02:37.360><c> gift</c><00:02:37.599><c> now</c>

00:02:37.710 --> 00:02:37.720 align:start position:0%
creates it and then here is the gift now
 

00:02:37.720 --> 00:02:39.509 align:start position:0%
creates it and then here is the gift now
let's<00:02:37.920><c> just</c><00:02:38.040><c> do</c><00:02:38.239><c> another</c><00:02:38.519><c> one</c><00:02:39.200><c> okay</c><00:02:39.280><c> so</c><00:02:39.440><c> I</c>

00:02:39.509 --> 00:02:39.519 align:start position:0%
let's just do another one okay so I
 

00:02:39.519 --> 00:02:40.750 align:start position:0%
let's just do another one okay so I
created<00:02:39.840><c> another</c><00:02:40.040><c> one</c><00:02:40.200><c> this</c><00:02:40.280><c> is</c><00:02:40.360><c> a</c><00:02:40.480><c> shark</c>

00:02:40.750 --> 00:02:40.760 align:start position:0%
created another one this is a shark
 

00:02:40.760 --> 00:02:42.910 align:start position:0%
created another one this is a shark
jumping<00:02:41.040><c> out</c><00:02:41.159><c> of</c><00:02:41.360><c> water</c><00:02:41.599><c> to</c><00:02:41.840><c> catch</c><00:02:42.000><c> a</c><00:02:42.200><c> fish</c><00:02:42.760><c> I</c>

00:02:42.910 --> 00:02:42.920 align:start position:0%
jumping out of water to catch a fish I
 

00:02:42.920 --> 00:02:44.869 align:start position:0%
jumping out of water to catch a fish I
increased<00:02:43.440><c> the</c><00:02:43.599><c> frames</c><00:02:44.000><c> to</c><00:02:44.239><c> 30</c><00:02:44.599><c> so</c><00:02:44.760><c> this</c>

00:02:44.869 --> 00:02:44.879 align:start position:0%
increased the frames to 30 so this
 

00:02:44.879 --> 00:02:47.030 align:start position:0%
increased the frames to 30 so this
should<00:02:45.000><c> be</c><00:02:45.120><c> a</c><00:02:45.239><c> little</c><00:02:45.400><c> bit</c><00:02:45.640><c> longer</c><00:02:46.640><c> now</c><00:02:46.800><c> if</c><00:02:46.879><c> we</c>

00:02:47.030 --> 00:02:47.040 align:start position:0%
should be a little bit longer now if we
 

00:02:47.040 --> 00:02:49.030 align:start position:0%
should be a little bit longer now if we
look<00:02:47.200><c> at</c><00:02:47.319><c> it</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
look at it
 

00:02:49.040 --> 00:02:52.589 align:start position:0%
look at it
ah<00:02:50.040><c> it's</c><00:02:50.400><c> doing</c><00:02:50.800><c> something</c><00:02:51.239><c> there's</c><00:02:51.879><c> there's</c>

00:02:52.589 --> 00:02:52.599 align:start position:0%
ah it's doing something there's there's
 

00:02:52.599 --> 00:02:54.869 align:start position:0%
ah it's doing something there's there's
another<00:02:52.920><c> shark</c><00:02:53.319><c> out</c><00:02:53.480><c> of</c><00:02:53.640><c> the</c><00:02:53.800><c> water</c><00:02:54.440><c> it's</c><00:02:54.720><c> you</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
another shark out of the water it's you
 

00:02:54.879 --> 00:02:57.990 align:start position:0%
another shark out of the water it's you
know<00:02:55.680><c> it</c><00:02:55.840><c> tried</c><00:02:56.560><c> right</c><00:02:56.840><c> it</c><00:02:57.000><c> tried</c><00:02:57.800><c> okay</c>

00:02:57.990 --> 00:02:58.000 align:start position:0%
know it tried right it tried okay
 

00:02:58.000 --> 00:02:59.869 align:start position:0%
know it tried right it tried okay
awesome<00:02:58.519><c> now</c><00:02:58.680><c> we</c><00:02:58.800><c> learned</c><00:02:59.040><c> how</c><00:02:59.120><c> to</c><00:02:59.239><c> use</c><00:02:59.400><c> actual</c>

00:02:59.869 --> 00:02:59.879 align:start position:0%
awesome now we learned how to use actual
 

00:02:59.879 --> 00:03:02.390 align:start position:0%
awesome now we learned how to use actual
python<00:03:00.319><c> code</c><00:03:00.640><c> to</c><00:03:00.879><c> get</c><00:03:01.040><c> all</c><00:03:01.200><c> the</c><00:03:01.480><c> pipeline</c><00:03:02.040><c> for</c>

00:03:02.390 --> 00:03:02.400 align:start position:0%
python code to get all the pipeline for
 

00:03:02.400 --> 00:03:04.309 align:start position:0%
python code to get all the pipeline for
this<00:03:02.560><c> text</c><00:03:03.000><c> video</c><00:03:03.239><c> to</c><00:03:03.400><c> work</c><00:03:03.879><c> we</c><00:03:04.000><c> couldn't</c><00:03:04.200><c> use</c>

00:03:04.309 --> 00:03:04.319 align:start position:0%
this text video to work we couldn't use
 

00:03:04.319 --> 00:03:06.149 align:start position:0%
this text video to work we couldn't use
an<00:03:04.480><c> inference</c><00:03:04.840><c> server</c><00:03:05.239><c> with</c><00:03:05.400><c> this</c><00:03:05.599><c> model</c><00:03:06.000><c> so</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
an inference server with this model so
 

00:03:06.159 --> 00:03:07.990 align:start position:0%
an inference server with this model so
we<00:03:06.400><c> resorted</c><00:03:06.799><c> to</c><00:03:07.080><c> actually</c><00:03:07.280><c> writing</c><00:03:07.720><c> all</c><00:03:07.879><c> the</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
we resorted to actually writing all the
 

00:03:08.000 --> 00:03:09.869 align:start position:0%
we resorted to actually writing all the
python<00:03:08.360><c> code</c><00:03:08.680><c> that</c><00:03:09.040><c> is</c><00:03:09.200><c> done</c><00:03:09.519><c> behind</c><00:03:09.760><c> the</c>

00:03:09.869 --> 00:03:09.879 align:start position:0%
python code that is done behind the
 

00:03:09.879 --> 00:03:11.470 align:start position:0%
python code that is done behind the
scenes<00:03:10.480><c> I</c><00:03:10.599><c> believe</c><00:03:10.799><c> this</c><00:03:10.879><c> is</c><00:03:10.959><c> day</c><00:03:11.120><c> 12</c><00:03:11.360><c> this</c>

00:03:11.470 --> 00:03:11.480 align:start position:0%
scenes I believe this is day 12 this
 

00:03:11.480 --> 00:03:12.789 align:start position:0%
scenes I believe this is day 12 this
month<00:03:11.640><c> and</c><00:03:11.760><c> this</c><00:03:11.840><c> is</c><00:03:11.959><c> the</c><00:03:12.080><c> third</c><00:03:12.400><c> video</c><00:03:12.720><c> that</c>

00:03:12.789 --> 00:03:12.799 align:start position:0%
month and this is the third video that
 

00:03:12.799 --> 00:03:14.309 align:start position:0%
month and this is the third video that
I've<00:03:12.959><c> done</c><00:03:13.120><c> with</c><00:03:13.280><c> hugging</c><00:03:13.599><c> face</c><00:03:13.799><c> I</c><00:03:13.920><c> got</c><00:03:14.080><c> a</c>

00:03:14.309 --> 00:03:14.319 align:start position:0%
I've done with hugging face I got a
 

00:03:14.319 --> 00:03:15.990 align:start position:0%
I've done with hugging face I got a
couple<00:03:14.560><c> more</c><00:03:14.680><c> to</c><00:03:14.840><c> go</c><00:03:15.080><c> maybe</c><00:03:15.280><c> a</c><00:03:15.360><c> few</c><00:03:15.519><c> more</c><00:03:15.879><c> but</c>

00:03:15.990 --> 00:03:16.000 align:start position:0%
couple more to go maybe a few more but
 

00:03:16.000 --> 00:03:17.350 align:start position:0%
couple more to go maybe a few more but
we're<00:03:16.120><c> learning</c><00:03:16.440><c> how</c><00:03:16.519><c> to</c><00:03:16.680><c> use</c><00:03:17.000><c> different</c>

00:03:17.350 --> 00:03:17.360 align:start position:0%
we're learning how to use different
 

00:03:17.360 --> 00:03:20.229 align:start position:0%
we're learning how to use different
models<00:03:17.879><c> free</c><00:03:18.280><c> models</c><00:03:18.920><c> and</c><00:03:19.080><c> use</c><00:03:19.360><c> them</c><00:03:19.599><c> locally</c>

00:03:20.229 --> 00:03:20.239 align:start position:0%
models free models and use them locally
 

00:03:20.239 --> 00:03:22.309 align:start position:0%
models free models and use them locally
and<00:03:20.360><c> then</c><00:03:20.519><c> the</c><00:03:20.680><c> idea</c><00:03:20.959><c> is</c><00:03:21.159><c> we</c><00:03:21.280><c> can</c><00:03:21.400><c> string</c><00:03:21.920><c> these</c>

00:03:22.309 --> 00:03:22.319 align:start position:0%
and then the idea is we can string these
 

00:03:22.319 --> 00:03:24.350 align:start position:0%
and then the idea is we can string these
together<00:03:23.000><c> then</c><00:03:23.239><c> use</c><00:03:23.519><c> them</c><00:03:23.879><c> with</c><00:03:24.080><c> something</c>

00:03:24.350 --> 00:03:24.360 align:start position:0%
together then use them with something
 

00:03:24.360 --> 00:03:26.430 align:start position:0%
together then use them with something
like<00:03:24.519><c> autogen</c><00:03:25.319><c> which</c><00:03:25.440><c> is</c><00:03:25.560><c> an</c><00:03:25.720><c> orchestrator</c><00:03:26.319><c> of</c>

00:03:26.430 --> 00:03:26.440 align:start position:0%
like autogen which is an orchestrator of
 

00:03:26.440 --> 00:03:28.030 align:start position:0%
like autogen which is an orchestrator of
all<00:03:26.599><c> these</c><00:03:26.760><c> agents</c><00:03:27.319><c> and</c><00:03:27.440><c> we</c><00:03:27.560><c> can</c><00:03:27.720><c> have</c><00:03:27.879><c> this</c>

00:03:28.030 --> 00:03:28.040 align:start position:0%
all these agents and we can have this
 

00:03:28.040 --> 00:03:30.429 align:start position:0%
all these agents and we can have this
kind<00:03:28.159><c> of</c><00:03:28.400><c> pipeline</c><00:03:29.239><c> of</c><00:03:29.400><c> all</c><00:03:29.879><c> agent</c><00:03:30.120><c> doing</c>

00:03:30.429 --> 00:03:30.439 align:start position:0%
kind of pipeline of all agent doing
 

00:03:30.439 --> 00:03:32.670 align:start position:0%
kind of pipeline of all agent doing
different<00:03:30.760><c> things</c><00:03:31.080><c> for</c><00:03:31.319><c> us</c><00:03:31.720><c> and</c><00:03:31.920><c> each</c><00:03:32.200><c> agent</c>

00:03:32.670 --> 00:03:32.680 align:start position:0%
different things for us and each agent
 

00:03:32.680 --> 00:03:34.949 align:start position:0%
different things for us and each agent
can<00:03:32.879><c> then</c><00:03:33.040><c> perform</c><00:03:33.519><c> task</c><00:03:34.080><c> based</c><00:03:34.400><c> on</c><00:03:34.680><c> different</c>

00:03:34.949 --> 00:03:34.959 align:start position:0%
can then perform task based on different
 

00:03:34.959 --> 00:03:36.830 align:start position:0%
can then perform task based on different
models<00:03:35.400><c> from</c><00:03:35.640><c> hugging</c><00:03:36.000><c> face</c><00:03:36.480><c> but</c><00:03:36.599><c> the</c><00:03:36.680><c> first</c>

00:03:36.830 --> 00:03:36.840 align:start position:0%
models from hugging face but the first
 

00:03:36.840 --> 00:03:38.350 align:start position:0%
models from hugging face but the first
thing<00:03:36.959><c> is</c><00:03:37.080><c> we</c><00:03:37.159><c> got</c><00:03:37.239><c> to</c><00:03:37.319><c> learn</c><00:03:37.480><c> how</c><00:03:37.560><c> to</c><00:03:37.799><c> use</c><00:03:38.080><c> them</c>

00:03:38.350 --> 00:03:38.360 align:start position:0%
thing is we got to learn how to use them
 

00:03:38.360 --> 00:03:39.350 align:start position:0%
thing is we got to learn how to use them
so<00:03:38.480><c> let</c><00:03:38.599><c> me</c><00:03:38.680><c> know</c><00:03:38.760><c> in</c><00:03:38.840><c> the</c><00:03:38.920><c> comments</c><00:03:39.159><c> if</c><00:03:39.280><c> you</c>

00:03:39.350 --> 00:03:39.360 align:start position:0%
so let me know in the comments if you
 

00:03:39.360 --> 00:03:41.270 align:start position:0%
so let me know in the comments if you
used<00:03:39.720><c> other</c><00:03:40.000><c> texed</c><00:03:40.360><c> video</c><00:03:40.640><c> models</c><00:03:40.959><c> and</c><00:03:41.159><c> the</c>

00:03:41.270 --> 00:03:41.280 align:start position:0%
used other texed video models and the
 

00:03:41.280 --> 00:03:43.110 align:start position:0%
used other texed video models and the
results<00:03:41.599><c> that</c><00:03:41.760><c> you've</c><00:03:42.000><c> gotten</c><00:03:42.799><c> if</c><00:03:42.879><c> you</c><00:03:42.959><c> have</c>

00:03:43.110 --> 00:03:43.120 align:start position:0%
results that you've gotten if you have
 

00:03:43.120 --> 00:03:44.830 align:start position:0%
results that you've gotten if you have
any<00:03:43.319><c> questions</c><00:03:43.920><c> please</c><00:03:44.120><c> leave</c><00:03:44.319><c> them</c><00:03:44.519><c> there</c><00:03:44.720><c> as</c>

00:03:44.830 --> 00:03:44.840 align:start position:0%
any questions please leave them there as
 

00:03:44.840 --> 00:03:46.110 align:start position:0%
any questions please leave them there as
well<00:03:45.000><c> and</c><00:03:45.080><c> I'll</c><00:03:45.239><c> get</c><00:03:45.360><c> back</c><00:03:45.480><c> to</c><00:03:45.560><c> you</c><00:03:45.760><c> soon</c><00:03:45.879><c> as</c><00:03:46.000><c> I</c>

00:03:46.110 --> 00:03:46.120 align:start position:0%
well and I'll get back to you soon as I
 

00:03:46.120 --> 00:03:47.429 align:start position:0%
well and I'll get back to you soon as I
can<00:03:46.319><c> thank</c><00:03:46.439><c> you</c><00:03:46.519><c> for</c><00:03:46.680><c> watching</c><00:03:47.040><c> here</c><00:03:47.200><c> are</c><00:03:47.319><c> some</c>

00:03:47.429 --> 00:03:47.439 align:start position:0%
can thank you for watching here are some
 

00:03:47.439 --> 00:03:49.429 align:start position:0%
can thank you for watching here are some
more<00:03:47.599><c> videos</c><00:03:47.879><c> on</c><00:03:48.040><c> AI</c><00:03:48.360><c> and</c><00:03:48.519><c> autogen</c><00:03:49.159><c> I'll</c><00:03:49.319><c> see</c>

00:03:49.429 --> 00:03:49.439 align:start position:0%
more videos on AI and autogen I'll see
 

00:03:49.439 --> 00:03:52.159 align:start position:0%
more videos on AI and autogen I'll see
you<00:03:49.640><c> next</c><00:03:49.840><c> video</c>

