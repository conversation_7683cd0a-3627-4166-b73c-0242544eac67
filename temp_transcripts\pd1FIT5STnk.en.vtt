WEBVTT
Kind: captions
Language: en

00:00:00.760 --> 00:00:02.270 align:start position:0%
 
hey<00:00:00.960><c> there</c><00:00:01.280><c> it's</c><00:00:01.480><c> Matt</c><00:00:01.959><c> one</c><00:00:02.080><c> of</c><00:00:02.200><c> the</c>

00:00:02.270 --> 00:00:02.280 align:start position:0%
hey there it's Matt one of the
 

00:00:02.280 --> 00:00:04.749 align:start position:0%
hey there it's Matt one of the
maintainers<00:00:02.919><c> of</c><00:00:03.120><c> olama</c><00:00:03.959><c> and</c><00:00:04.200><c> I</c><00:00:04.279><c> want</c><00:00:04.400><c> to</c><00:00:04.560><c> show</c>

00:00:04.749 --> 00:00:04.759 align:start position:0%
maintainers of olama and I want to show
 

00:00:04.759 --> 00:00:07.070 align:start position:0%
maintainers of olama and I want to show
you<00:00:05.120><c> one</c><00:00:05.279><c> of</c><00:00:05.600><c> the</c><00:00:05.759><c> new</c><00:00:05.960><c> models</c><00:00:06.480><c> we</c><00:00:06.640><c> added</c><00:00:06.960><c> to</c>

00:00:07.070 --> 00:00:07.080 align:start position:0%
you one of the new models we added to
 

00:00:07.080 --> 00:00:11.270 align:start position:0%
you one of the new models we added to
the<00:00:07.240><c> library</c><00:00:08.120><c> it's</c><00:00:08.320><c> called</c><00:00:08.599><c> stable</c><00:00:09.360><c> lm-</c><00:00:10.360><c> zhr</c>

00:00:11.270 --> 00:00:11.280 align:start position:0%
the library it's called stable lm- zhr
 

00:00:11.280 --> 00:00:13.669 align:start position:0%
the library it's called stable lm- zhr
it's<00:00:11.440><c> a</c><00:00:11.679><c> 3</c><00:00:12.040><c> billion</c><00:00:12.480><c> parameter</c><00:00:12.920><c> model</c><00:00:13.440><c> that</c>

00:00:13.669 --> 00:00:13.679 align:start position:0%
it's a 3 billion parameter model that
 

00:00:13.679 --> 00:00:15.990 align:start position:0%
it's a 3 billion parameter model that
feels<00:00:14.120><c> like</c><00:00:14.400><c> a</c><00:00:14.599><c> 7</c><00:00:14.920><c> billion</c><00:00:15.280><c> parameter</c><00:00:15.639><c> model</c>

00:00:15.990 --> 00:00:16.000 align:start position:0%
feels like a 7 billion parameter model
 

00:00:16.000 --> 00:00:18.950 align:start position:0%
feels like a 7 billion parameter model
but<00:00:16.279><c> a</c><00:00:16.560><c> whole</c><00:00:17.039><c> lot</c><00:00:17.520><c> faster</c><00:00:18.439><c> let's</c><00:00:18.680><c> check</c><00:00:18.840><c> it</c>

00:00:18.950 --> 00:00:18.960 align:start position:0%
but a whole lot faster let's check it
 

00:00:18.960 --> 00:00:21.470 align:start position:0%
but a whole lot faster let's check it
out<00:00:19.680><c> first</c><00:00:20.039><c> if</c><00:00:20.119><c> you</c><00:00:20.240><c> don't</c><00:00:20.480><c> have</c><00:00:20.640><c> olama</c><00:00:21.359><c> you</c>

00:00:21.470 --> 00:00:21.480 align:start position:0%
out first if you don't have olama you
 

00:00:21.480 --> 00:00:24.990 align:start position:0%
out first if you don't have olama you
can<00:00:21.640><c> get</c><00:00:21.800><c> started</c><00:00:22.240><c> at</c><00:00:22.439><c> ol.</c><00:00:23.359><c> a</c><00:00:24.359><c> then</c><00:00:24.720><c> after</c>

00:00:24.990 --> 00:00:25.000 align:start position:0%
can get started at ol. a then after
 

00:00:25.000 --> 00:00:29.589 align:start position:0%
can get started at ol. a then after
installing<00:00:25.439><c> run</c><00:00:25.720><c> olama</c><00:00:26.439><c> run</c><00:00:27.080><c> stable</c><00:00:27.560><c> lm-</c><00:00:28.599><c> zeer</c>

00:00:29.589 --> 00:00:29.599 align:start position:0%
installing run olama run stable lm- zeer
 

00:00:29.599 --> 00:00:32.589 align:start position:0%
installing run olama run stable lm- zeer
why<00:00:29.759><c> is</c><00:00:30.039><c> the</c><00:00:30.199><c> sky</c><00:00:30.560><c> blue</c><00:00:31.480><c> boom</c><00:00:32.079><c> look</c><00:00:32.200><c> at</c><00:00:32.399><c> that</c>

00:00:32.589 --> 00:00:32.599 align:start position:0%
why is the sky blue boom look at that
 

00:00:32.599 --> 00:00:36.470 align:start position:0%
why is the sky blue boom look at that
thing<00:00:32.840><c> go</c><00:00:33.600><c> that</c><00:00:33.840><c> is</c><00:00:34.440><c> awesome</c><00:00:35.440><c> now</c><00:00:35.800><c> the</c><00:00:35.960><c> output</c>

00:00:36.470 --> 00:00:36.480 align:start position:0%
thing go that is awesome now the output
 

00:00:36.480 --> 00:00:39.750 align:start position:0%
thing go that is awesome now the output
isn't<00:00:36.879><c> always</c><00:00:37.399><c> perfect</c><00:00:38.399><c> sometimes</c><00:00:38.800><c> it's</c><00:00:39.280><c> it's</c>

00:00:39.750 --> 00:00:39.760 align:start position:0%
isn't always perfect sometimes it's it's
 

00:00:39.760 --> 00:00:41.830 align:start position:0%
isn't always perfect sometimes it's it's
weird<00:00:40.760><c> well</c><00:00:41.000><c> let's</c><00:00:41.200><c> take</c><00:00:41.320><c> a</c><00:00:41.480><c> look</c><00:00:41.600><c> at</c><00:00:41.719><c> the</c>

00:00:41.830 --> 00:00:41.840 align:start position:0%
weird well let's take a look at the
 

00:00:41.840 --> 00:00:45.750 align:start position:0%
weird well let's take a look at the
model<00:00:42.160><c> file</c><00:00:42.800><c> in</c><00:00:42.920><c> the</c><00:00:43.039><c> reppel</c><00:00:43.640><c> I</c><00:00:43.719><c> can</c><00:00:43.960><c> type</c><00:00:44.760><c> slow</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
model file in the reppel I can type slow
 

00:00:45.760 --> 00:00:48.270 align:start position:0%
model file in the reppel I can type slow
model<00:00:46.199><c> file</c><00:00:47.000><c> and</c><00:00:47.160><c> you</c><00:00:47.280><c> can</c><00:00:47.440><c> see</c><00:00:47.719><c> there's</c><00:00:48.000><c> no</c>

00:00:48.270 --> 00:00:48.280 align:start position:0%
model file and you can see there's no
 

00:00:48.280 --> 00:00:50.270 align:start position:0%
model file and you can see there's no
system<00:00:48.640><c> prompt</c><00:00:49.440><c> we'll</c><00:00:49.640><c> take</c><00:00:49.760><c> a</c><00:00:49.879><c> look</c><00:00:50.000><c> at</c><00:00:50.160><c> how</c>

00:00:50.270 --> 00:00:50.280 align:start position:0%
system prompt we'll take a look at how
 

00:00:50.280 --> 00:00:53.510 align:start position:0%
system prompt we'll take a look at how
to<00:00:50.440><c> change</c><00:00:50.840><c> that</c><00:00:51.079><c> a</c><00:00:51.239><c> bit</c><00:00:51.480><c> later</c><00:00:52.480><c> stable</c><00:00:52.960><c> LM</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
to change that a bit later stable LM
 

00:00:53.520 --> 00:00:57.430 align:start position:0%
to change that a bit later stable LM
Zephyr<00:00:54.520><c> is</c><00:00:54.640><c> a</c><00:00:54.840><c> new</c><00:00:55.039><c> model</c><00:00:55.600><c> from</c><00:00:55.879><c> stability</c><00:00:56.640><c> AI</c>

00:00:57.430 --> 00:00:57.440 align:start position:0%
Zephyr is a new model from stability AI
 

00:00:57.440 --> 00:01:00.270 align:start position:0%
Zephyr is a new model from stability AI
the<00:00:57.559><c> folks</c><00:00:57.920><c> behind</c><00:00:58.280><c> stable</c><00:00:58.920><c> diffusions</c><00:01:00.120><c> it's</c>

00:01:00.270 --> 00:01:00.280 align:start position:0%
the folks behind stable diffusions it's
 

00:01:00.280 --> 00:01:02.270 align:start position:0%
the folks behind stable diffusions it's
being<00:01:00.519><c> released</c><00:01:01.000><c> with</c><00:01:01.160><c> a</c><00:01:01.320><c> non-commercial</c>

00:01:02.270 --> 00:01:02.280 align:start position:0%
being released with a non-commercial
 

00:01:02.280 --> 00:01:04.990 align:start position:0%
being released with a non-commercial
Community<00:01:02.879><c> license</c><00:01:03.719><c> it</c><00:01:03.840><c> has</c><00:01:03.960><c> a</c><00:01:04.080><c> 4K</c><00:01:04.519><c> context</c>

00:01:04.990 --> 00:01:05.000 align:start position:0%
Community license it has a 4K context
 

00:01:05.000 --> 00:01:07.910 align:start position:0%
Community license it has a 4K context
which<00:01:05.119><c> is</c><00:01:05.280><c> decent</c><00:01:06.200><c> but</c><00:01:06.640><c> it</c><00:01:06.840><c> isn't</c><00:01:07.159><c> uncensored</c>

00:01:07.910 --> 00:01:07.920 align:start position:0%
which is decent but it isn't uncensored
 

00:01:07.920 --> 00:01:09.950 align:start position:0%
which is decent but it isn't uncensored
so<00:01:08.320><c> asking</c><00:01:08.759><c> questions</c><00:01:09.080><c> about</c><00:01:09.280><c> making</c><00:01:09.600><c> drugs</c>

00:01:09.950 --> 00:01:09.960 align:start position:0%
so asking questions about making drugs
 

00:01:09.960 --> 00:01:12.670 align:start position:0%
so asking questions about making drugs
or<00:01:10.159><c> bombs</c><00:01:10.640><c> won't</c><00:01:10.960><c> work</c><00:01:11.799><c> and</c><00:01:12.000><c> you</c><00:01:12.119><c> can't</c><00:01:12.360><c> even</c>

00:01:12.670 --> 00:01:12.680 align:start position:0%
or bombs won't work and you can't even
 

00:01:12.680 --> 00:01:15.030 align:start position:0%
or bombs won't work and you can't even
ask<00:01:12.880><c> for</c><00:01:13.119><c> a</c><00:01:13.400><c> recipe</c><00:01:13.720><c> for</c><00:01:13.960><c> dangerously</c><00:01:14.520><c> spicy</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
ask for a recipe for dangerously spicy
 

00:01:15.040 --> 00:01:17.789 align:start position:0%
ask for a recipe for dangerously spicy
mayo<00:01:16.040><c> some</c><00:01:16.240><c> folks</c><00:01:16.560><c> may</c><00:01:16.799><c> think</c><00:01:17.000><c> the</c><00:01:17.200><c> license</c>

00:01:17.789 --> 00:01:17.799 align:start position:0%
mayo some folks may think the license
 

00:01:17.799 --> 00:01:20.270 align:start position:0%
mayo some folks may think the license
and<00:01:17.920><c> the</c><00:01:18.080><c> sensoring</c><00:01:18.759><c> is</c><00:01:18.920><c> a</c><00:01:19.080><c> deal</c><00:01:19.320><c> breaker</c><00:01:19.799><c> but</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
and the sensoring is a deal breaker but
 

00:01:20.280 --> 00:01:22.350 align:start position:0%
and the sensoring is a deal breaker but
depending<00:01:20.640><c> on</c><00:01:20.840><c> what</c><00:01:20.960><c> you're</c><00:01:21.159><c> looking</c><00:01:21.439><c> for</c><00:01:22.200><c> I</c>

00:01:22.350 --> 00:01:22.360 align:start position:0%
depending on what you're looking for I
 

00:01:22.360 --> 00:01:24.749 align:start position:0%
depending on what you're looking for I
think<00:01:22.520><c> it</c><00:01:22.640><c> can</c><00:01:22.799><c> be</c><00:01:23.000><c> the</c><00:01:23.280><c> perfect</c><00:01:23.600><c> model</c><00:01:24.320><c> for</c><00:01:24.520><c> a</c>

00:01:24.749 --> 00:01:24.759 align:start position:0%
think it can be the perfect model for a
 

00:01:24.759 --> 00:01:27.590 align:start position:0%
think it can be the perfect model for a
lot<00:01:24.880><c> of</c><00:01:25.119><c> people</c><00:01:26.079><c> like</c><00:01:26.400><c> for</c><00:01:26.680><c> those</c><00:01:27.200><c> really</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
lot of people like for those really
 

00:01:27.600 --> 00:01:30.749 align:start position:0%
lot of people like for those really
low-end<00:01:28.200><c> machines</c><00:01:28.720><c> with</c><00:01:28.920><c> Just</c><00:01:29.200><c> 4</c><00:01:29.560><c> G</c><00:01:30.240><c> of</c><00:01:30.360><c> memory</c>

00:01:30.749 --> 00:01:30.759 align:start position:0%
low-end machines with Just 4 G of memory
 

00:01:30.759 --> 00:01:32.910 align:start position:0%
low-end machines with Just 4 G of memory
or<00:01:31.439><c> just</c><00:01:31.600><c> when</c><00:01:31.720><c> you</c><00:01:31.840><c> need</c><00:01:32.040><c> a</c><00:01:32.280><c> super</c><00:01:32.640><c> fast</c>

00:01:32.910 --> 00:01:32.920 align:start position:0%
or just when you need a super fast
 

00:01:32.920 --> 00:01:36.190 align:start position:0%
or just when you need a super fast
response<00:01:33.479><c> it's</c><00:01:33.960><c> absolutely</c><00:01:34.920><c> amazing</c><00:01:35.920><c> so</c>

00:01:36.190 --> 00:01:36.200 align:start position:0%
response it's absolutely amazing so
 

00:01:36.200 --> 00:01:39.190 align:start position:0%
response it's absolutely amazing so
let's<00:01:36.560><c> tweak</c><00:01:37.000><c> The</c><00:01:37.200><c> Prompt</c><00:01:37.680><c> a</c><00:01:37.880><c> bit</c><00:01:38.680><c> if</c><00:01:38.759><c> you</c><00:01:39.040><c> just</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
let's tweak The Prompt a bit if you just
 

00:01:39.200 --> 00:01:40.710 align:start position:0%
let's tweak The Prompt a bit if you just
want<00:01:39.320><c> to</c><00:01:39.479><c> change</c><00:01:39.759><c> a</c><00:01:39.880><c> few</c><00:01:40.079><c> things</c><00:01:40.280><c> you</c><00:01:40.399><c> can</c><00:01:40.560><c> make</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
want to change a few things you can make
 

00:01:40.720 --> 00:01:44.789 align:start position:0%
want to change a few things you can make
changes<00:01:41.200><c> in</c><00:01:41.399><c> the</c><00:01:41.640><c> rapple</c><00:01:42.720><c> slet</c><00:01:43.720><c> system</c><00:01:44.600><c> you</c>

00:01:44.789 --> 00:01:44.799 align:start position:0%
changes in the rapple slet system you
 

00:01:44.799 --> 00:01:48.069 align:start position:0%
changes in the rapple slet system you
are<00:01:45.159><c> a</c><00:01:45.360><c> helpful</c><00:01:45.920><c> AI</c><00:01:46.399><c> assistant</c><00:01:47.159><c> answer</c><00:01:47.640><c> every</c>

00:01:48.069 --> 00:01:48.079 align:start position:0%
are a helpful AI assistant answer every
 

00:01:48.079 --> 00:01:51.590 align:start position:0%
are a helpful AI assistant answer every
question<00:01:48.719><c> in</c><00:01:49.159><c> Dutch</c><00:01:50.159><c> and</c><00:01:50.360><c> enter</c><00:01:50.960><c> and</c><00:01:51.119><c> now</c><00:01:51.320><c> my</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
question in Dutch and enter and now my
 

00:01:51.600 --> 00:01:54.709 align:start position:0%
question in Dutch and enter and now my
question<00:01:52.600><c> why</c><00:01:52.920><c> do</c><00:01:53.320><c> dogs</c><00:01:53.799><c> always</c><00:01:54.240><c> spin</c><00:01:54.520><c> in</c>

00:01:54.709 --> 00:01:54.719 align:start position:0%
question why do dogs always spin in
 

00:01:54.719 --> 00:01:57.630 align:start position:0%
question why do dogs always spin in
circles<00:01:55.479><c> before</c><00:01:55.759><c> they</c><00:01:55.920><c> poop</c><00:01:56.880><c> now</c><00:01:57.320><c> I</c><00:01:57.439><c> think</c>

00:01:57.630 --> 00:01:57.640 align:start position:0%
circles before they poop now I think
 

00:01:57.640 --> 00:01:59.670 align:start position:0%
circles before they poop now I think
that<00:01:57.759><c> looks</c><00:01:58.119><c> okay</c><00:01:58.399><c> but</c><00:01:58.920><c> then</c><00:01:59.119><c> again</c><00:01:59.320><c> my</c><00:01:59.479><c> Dutch</c>

00:01:59.670 --> 00:01:59.680 align:start position:0%
that looks okay but then again my Dutch
 

00:01:59.680 --> 00:02:02.109 align:start position:0%
that looks okay but then again my Dutch
is<00:01:59.840><c> is</c><00:01:59.960><c> horrible</c><00:02:00.799><c> and</c><00:02:01.159><c> I</c><00:02:01.240><c> don't</c><00:02:01.439><c> think</c><00:02:01.600><c> Rosetta</c>

00:02:02.109 --> 00:02:02.119 align:start position:0%
is is horrible and I don't think Rosetta
 

00:02:02.119 --> 00:02:05.109 align:start position:0%
is is horrible and I don't think Rosetta
even<00:02:02.360><c> ever</c><00:02:02.799><c> covered</c><00:02:03.520><c> that</c><00:02:03.799><c> topic</c><00:02:04.680><c> I</c><00:02:04.759><c> lived</c><00:02:05.000><c> in</c>

00:02:05.109 --> 00:02:05.119 align:start position:0%
even ever covered that topic I lived in
 

00:02:05.119 --> 00:02:07.789 align:start position:0%
even ever covered that topic I lived in
Amsterdam<00:02:05.600><c> for</c><00:02:05.759><c> almost</c><00:02:06.000><c> 10</c><00:02:06.280><c> years</c><00:02:06.680><c> but</c><00:02:07.159><c> my</c><00:02:07.399><c> job</c>

00:02:07.789 --> 00:02:07.799 align:start position:0%
Amsterdam for almost 10 years but my job
 

00:02:07.799 --> 00:02:10.949 align:start position:0%
Amsterdam for almost 10 years but my job
involved<00:02:08.640><c> close</c><00:02:08.879><c> to</c><00:02:09.119><c> 90%</c><00:02:09.720><c> travel</c><00:02:10.160><c> so</c><00:02:10.679><c> never</c>

00:02:10.949 --> 00:02:10.959 align:start position:0%
involved close to 90% travel so never
 

00:02:10.959 --> 00:02:12.790 align:start position:0%
involved close to 90% travel so never
really<00:02:11.200><c> got</c><00:02:11.360><c> a</c><00:02:11.520><c> chance</c><00:02:11.760><c> to</c><00:02:11.959><c> learn</c><00:02:12.319><c> and</c><00:02:12.640><c> my</c>

00:02:12.790 --> 00:02:12.800 align:start position:0%
really got a chance to learn and my
 

00:02:12.800 --> 00:02:15.030 align:start position:0%
really got a chance to learn and my
manager<00:02:13.239><c> openex</c><00:02:14.239><c> didn't</c><00:02:14.519><c> think</c><00:02:14.680><c> it</c><00:02:14.760><c> was</c><00:02:14.920><c> an</c>

00:02:15.030 --> 00:02:15.040 align:start position:0%
manager openex didn't think it was an
 

00:02:15.040 --> 00:02:17.670 align:start position:0%
manager openex didn't think it was an
important<00:02:15.360><c> thing</c><00:02:15.480><c> to</c><00:02:15.640><c> focus</c><00:02:15.959><c> on</c><00:02:16.760><c> oh</c><00:02:16.959><c> well</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
important thing to focus on oh well
 

00:02:17.680 --> 00:02:19.550 align:start position:0%
important thing to focus on oh well
let's<00:02:17.959><c> update</c><00:02:18.400><c> that</c><00:02:18.560><c> to</c><00:02:18.760><c> be</c><00:02:19.040><c> a</c><00:02:19.200><c> bit</c><00:02:19.360><c> more</c>

00:02:19.550 --> 00:02:19.560 align:start position:0%
let's update that to be a bit more
 

00:02:19.560 --> 00:02:24.190 align:start position:0%
let's update that to be a bit more
useful<00:02:20.760><c> slet</c><00:02:21.760><c> system</c><00:02:22.480><c> you</c><00:02:22.680><c> are</c><00:02:22.879><c> a</c><00:02:23.080><c> helpful</c><00:02:23.800><c> AI</c>

00:02:24.190 --> 00:02:24.200 align:start position:0%
useful slet system you are a helpful AI
 

00:02:24.200 --> 00:02:26.910 align:start position:0%
useful slet system you are a helpful AI
assistant<00:02:25.040><c> always</c><00:02:25.519><c> answer</c><00:02:26.000><c> every</c><00:02:26.319><c> question</c>

00:02:26.910 --> 00:02:26.920 align:start position:0%
assistant always answer every question
 

00:02:26.920 --> 00:02:29.910 align:start position:0%
assistant always answer every question
in<00:02:27.080><c> the</c><00:02:27.239><c> same</c><00:02:27.519><c> language</c><00:02:27.879><c> it</c><00:02:28.000><c> was</c><00:02:28.239><c> asked</c><00:02:28.560><c> in</c><00:02:29.400><c> now</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
in the same language it was asked in now
 

00:02:29.920 --> 00:02:32.070 align:start position:0%
in the same language it was asked in now
that<00:02:30.120><c> same</c><00:02:30.480><c> question</c><00:02:31.080><c> gets</c><00:02:31.280><c> a</c><00:02:31.560><c> better</c>

00:02:32.070 --> 00:02:32.080 align:start position:0%
that same question gets a better
 

00:02:32.080 --> 00:02:34.350 align:start position:0%
that same question gets a better
response<00:02:33.080><c> if</c><00:02:33.160><c> you</c><00:02:33.280><c> want</c><00:02:33.400><c> to</c><00:02:33.560><c> make</c><00:02:33.800><c> that</c><00:02:34.040><c> a</c><00:02:34.200><c> bit</c>

00:02:34.350 --> 00:02:34.360 align:start position:0%
response if you want to make that a bit
 

00:02:34.360 --> 00:02:37.470 align:start position:0%
response if you want to make that a bit
more<00:02:34.640><c> permanent</c><00:02:35.519><c> turn</c><00:02:35.720><c> it</c><00:02:35.920><c> into</c><00:02:36.200><c> a</c><00:02:36.360><c> model</c><00:02:36.760><c> file</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
more permanent turn it into a model file
 

00:02:37.480 --> 00:02:42.110 align:start position:0%
more permanent turn it into a model file
I<00:02:37.599><c> can</c><00:02:37.920><c> add</c><00:02:38.440><c> from</c><00:02:39.280><c> stable</c><00:02:39.920><c> lm-</c><00:02:40.920><c> zhr</c><00:02:41.840><c> then</c>

00:02:42.110 --> 00:02:42.120 align:start position:0%
I can add from stable lm- zhr then
 

00:02:42.120 --> 00:02:44.470 align:start position:0%
I can add from stable lm- zhr then
system<00:02:42.840><c> and</c><00:02:43.000><c> three</c><00:02:43.280><c> double</c><00:02:43.560><c> quotes</c><00:02:44.000><c> and</c><00:02:44.159><c> then</c>

00:02:44.470 --> 00:02:44.480 align:start position:0%
system and three double quotes and then
 

00:02:44.480 --> 00:02:46.550 align:start position:0%
system and three double quotes and then
my<00:02:44.720><c> system</c><00:02:45.040><c> prompt</c><00:02:45.720><c> and</c><00:02:45.879><c> then</c><00:02:46.080><c> three</c><00:02:46.280><c> double</c>

00:02:46.550 --> 00:02:46.560 align:start position:0%
my system prompt and then three double
 

00:02:46.560 --> 00:02:49.390 align:start position:0%
my system prompt and then three double
quotes<00:02:47.080><c> to</c><00:02:47.319><c> close</c><00:02:47.560><c> it</c><00:02:47.680><c> out</c><00:02:48.560><c> the</c><00:02:48.720><c> template</c><00:02:49.239><c> and</c>

00:02:49.390 --> 00:02:49.400 align:start position:0%
quotes to close it out the template and
 

00:02:49.400 --> 00:02:51.229 align:start position:0%
quotes to close it out the template and
stop<00:02:49.720><c> wordss</c><00:02:49.959><c> are</c><00:02:50.159><c> inherited</c><00:02:50.920><c> from</c><00:02:51.080><c> the</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
stop wordss are inherited from the
 

00:02:51.239 --> 00:02:53.390 align:start position:0%
stop wordss are inherited from the
parent<00:02:51.560><c> model</c><00:02:51.959><c> so</c><00:02:52.440><c> I</c><00:02:52.519><c> don't</c><00:02:52.680><c> need</c><00:02:52.840><c> to</c><00:02:53.000><c> include</c>

00:02:53.390 --> 00:02:53.400 align:start position:0%
parent model so I don't need to include
 

00:02:53.400 --> 00:02:57.030 align:start position:0%
parent model so I don't need to include
them<00:02:54.120><c> I'll</c><00:02:54.400><c> exit</c><00:02:54.920><c> and</c><00:02:55.080><c> run</c><00:02:55.440><c> AMA</c><00:02:56.040><c> create</c><00:02:56.760><c> my</c>

00:02:57.030 --> 00:02:57.040 align:start position:0%
them I'll exit and run AMA create my
 

00:02:57.040 --> 00:03:01.190 align:start position:0%
them I'll exit and run AMA create my
Zephyr<00:02:57.920><c> and</c><00:02:58.040><c> then</c><00:02:58.280><c> olama</c><00:02:59.000><c> run</c><00:02:59.560><c> my</c><00:02:59.760><c> my</c><00:03:00.000><c> Zer</c><00:03:00.959><c> ask</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
Zephyr and then olama run my my Zer ask
 

00:03:01.200 --> 00:03:04.390 align:start position:0%
Zephyr and then olama run my my Zer ask
my<00:03:01.360><c> dog</c><00:03:01.680><c> spinning</c><00:03:02.159><c> question</c><00:03:02.560><c> and</c><00:03:03.159><c> boom</c><00:03:04.159><c> I</c>

00:03:04.390 --> 00:03:04.400 align:start position:0%
my dog spinning question and boom I
 

00:03:04.400 --> 00:03:06.070 align:start position:0%
my dog spinning question and boom I
really<00:03:04.680><c> like</c><00:03:04.879><c> this</c><00:03:05.040><c> model</c><00:03:05.519><c> it's</c><00:03:05.680><c> going</c><00:03:05.799><c> to</c><00:03:05.920><c> be</c>

00:03:06.070 --> 00:03:06.080 align:start position:0%
really like this model it's going to be
 

00:03:06.080 --> 00:03:08.270 align:start position:0%
really like this model it's going to be
my<00:03:06.319><c> default</c><00:03:06.760><c> for</c><00:03:06.959><c> a</c><00:03:07.080><c> lot</c><00:03:07.239><c> of</c><00:03:07.400><c> things</c>

00:03:08.270 --> 00:03:08.280 align:start position:0%
my default for a lot of things
 

00:03:08.280 --> 00:03:09.830 align:start position:0%
my default for a lot of things
especially<00:03:08.799><c> when</c><00:03:08.959><c> I</c><00:03:09.080><c> need</c><00:03:09.319><c> something</c><00:03:09.640><c> that</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
especially when I need something that
 

00:03:09.840 --> 00:03:14.110 align:start position:0%
especially when I need something that
runs<00:03:10.840><c> really</c><00:03:11.560><c> really</c><00:03:12.239><c> fast</c><00:03:13.239><c> I</c><00:03:13.400><c> hope</c><00:03:13.599><c> you</c><00:03:13.760><c> found</c>

00:03:14.110 --> 00:03:14.120 align:start position:0%
runs really really fast I hope you found
 

00:03:14.120 --> 00:03:15.710 align:start position:0%
runs really really fast I hope you found
this<00:03:14.360><c> interesting</c><00:03:14.840><c> and</c><00:03:15.159><c> let</c><00:03:15.319><c> me</c><00:03:15.440><c> know</c><00:03:15.599><c> if</c>

00:03:15.710 --> 00:03:15.720 align:start position:0%
this interesting and let me know if
 

00:03:15.720 --> 00:03:17.670 align:start position:0%
this interesting and let me know if
you'd<00:03:15.920><c> like</c><00:03:16.040><c> to</c><00:03:16.120><c> see</c><00:03:16.319><c> something</c><00:03:16.640><c> else</c><00:03:17.440><c> thanks</c>

00:03:17.670 --> 00:03:17.680 align:start position:0%
you'd like to see something else thanks
 

00:03:17.680 --> 00:03:28.670 align:start position:0%
you'd like to see something else thanks
so<00:03:17.799><c> much</c><00:03:17.959><c> for</c><00:03:18.159><c> watching</c>

00:03:28.670 --> 00:03:28.680 align:start position:0%
 
 

00:03:28.680 --> 00:03:31.680 align:start position:0%
 
goodbye

