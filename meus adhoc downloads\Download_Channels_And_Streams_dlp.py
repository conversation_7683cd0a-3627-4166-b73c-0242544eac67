import os
import yt_dlp
import logging
from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound
import openai

# --- Configuration ---
CHANNEL_ID = "UC0patpmwYbhcEUap0bTX3JQ"  # Target YouTube Channel ID
OUTPUT_FOLDER = "D:\\1 - Youtube_Vimeo_Videos\\Roaring Kitty\\" # Output directory for downloads
# Set to True to download oldest videos first, False for newest first
DOWNLOAD_OLDEST_FIRST = True
# Set to True to download transcripts for each video
DOWNLOAD_TRANSCRIPTS = True
# Set your OpenAI API key here if you want to use Whisper as fallback for transcription
# OPENAI_API_KEY = "your_openai_api_key_here"
# --- End Configuration ---

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_ydl_opts_download(output_path):
    """Creates yt-dlp options for downloading a single video."""
    return {
        'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'quiet': False, # Show download progress
        'no_warnings': True,
        'ignoreerrors': True, # Continue if a single video fails
        'merge_output_format': 'mp4', # Ensure output is mp4 if merging is needed
        'postprocessors': [{
            'key': 'FFmpegVideoConvertor',
            'preferedformat': 'mp4',
        }],
        'retries': 5, # Retry downloads on transient errors
        'fragment_retries': 5,
    }

def get_all_urls_from_endpoint(channel_id, endpoint_suffix):
    """Fetches all video URLs from a specific channel endpoint (e.g., /videos, /streams)."""
    channel_url = f"https://www.youtube.com/channel/{channel_id}/{endpoint_suffix}"
    logging.info(f"Fetching URLs from: {channel_url}")

    ydl_opts_extract = {
        'extract_flat': 'in_playlist',  # Extract only URLs quickly
        'skip_download': True,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
        'force_generic_extractor': True, # Helps ensure channel URL is processed correctly
        'playlistreverse': DOWNLOAD_OLDEST_FIRST,
        'playlistend': None,  # Ensure all videos are fetched
    }

    urls = set()
    try:
        with yt_dlp.YoutubeDL(ydl_opts_extract) as ydl:
            info = ydl.extract_info(channel_url, download=False)
            if info and 'entries' in info and info['entries']:
                for entry in info['entries']:
                    if entry and entry.get('url'):
                        # Construct the standard watch URL
                        urls.add(f"https://www.youtube.com/watch?v={entry['id']}")
                logging.info(f"Found {len(urls)} unique URLs from {endpoint_suffix}")
            else:
                logging.warning(f"No entries found or failed to extract info from {channel_url}")
    except Exception as e:
        logging.error(f"Error fetching URLs from {channel_url}: {str(e)}")
    return list(urls) # Return as list

def sanitize_filename(filename):
    """Sanitizes a filename by removing invalid characters."""
    return "".join([c for c in filename if c.isalpha() or c.isdigit() or c in (' ', '.', '_', '-')]).rstrip()

def download_audio(video_url, output_path, title):
    """Downloads just the audio track of a video for transcription."""
    audio_file_path = os.path.join(output_path, f"{title}.mp3")
    
    ydl_opts = {
        'format': 'bestaudio/best',
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',
            'preferredcodec': 'mp3',
            'preferredquality': '192',
        }],
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'quiet': True,
        'no_warnings': True,
    }
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([video_url])
        return True, audio_file_path
    except Exception as e:
        logging.error(f"Audio download failed: {str(e)}")
        return False, None

def transcribe_with_whisper(audio_file_path):
    """Transcribes audio using OpenAI's Whisper API (if configured)."""
    try:
        # Check if OpenAI API key is configured
        api_key = globals().get('OPENAI_API_KEY')
        if not api_key or api_key == "your_openai_api_key_here":
            logging.warning("OpenAI API key not configured. Skipping Whisper transcription.")
            return None
            
        openai.api_key = api_key
        with open(audio_file_path, "rb") as audio_file:
            transcript = openai.Audio.transcribe("whisper-1", audio_file)
        return transcript["text"]
    except Exception as e:
        logging.error(f"Whisper transcription failed: {str(e)}")
        return None

def download_transcript(video_id, output_path, title, video_url):
    """Attempts to download transcript for a video, with Whisper fallback."""
    transcript_file_path = os.path.join(output_path, f"{title}.md")
    
    try:
        # First try YouTube's built-in transcripts
        transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])
        if transcript_data:
            transcript = " ".join([item["text"] for item in transcript_data])
            with open(transcript_file_path, "w", encoding="utf-8") as f:
                f.write(transcript)
            logging.info(f"YouTube transcript downloaded for: {title}")
            return True
    except NoTranscriptFound:
        logging.info(f"No YouTube transcript found for: {title}. Trying Whisper fallback...")
        # Try Whisper fallback if configured
        success, audio_file_path = download_audio(video_url, output_path, title)
        if success and audio_file_path:
            whisper_transcript = transcribe_with_whisper(audio_file_path)
            if whisper_transcript:
                with open(transcript_file_path, "w", encoding="utf-8") as f:
                    f.write(whisper_transcript)
                try:
                    os.remove(audio_file_path)  # Remove temporary audio file
                except Exception as rm_err:
                    logging.warning(f"Could not remove temporary audio file: {rm_err}")
                logging.info(f"Whisper transcript generated for: {title}")
                return True
            else:
                logging.warning(f"Whisper transcription failed for: {title}")
        else:
            logging.warning(f"Audio download failed for Whisper transcription: {title}")
    except Exception as e:
        logging.error(f"Failed to download transcript: {str(e)}")
    
    return False

def download_video(video_url, output_path):
    """Downloads a single video and its transcript from the given URL."""
    logging.info(f"Attempting to download: {video_url}")
    ydl_opts = create_ydl_opts_download(output_path)
    video_id = None
    title = None
    transcript_success = False
    
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Extract info first to get title and video_id for logging and transcript
            try:
                info = ydl.extract_info(video_url, download=False)
                video_id = info.get("id")
                raw_title = info.get("title", "unknown_video")
                title = sanitize_filename(raw_title)
                logging.info(f"Starting download for: {title}")
            except Exception as title_ex:
                logging.warning(f"Could not pre-fetch info for {video_url}: {title_ex}. Proceeding with download.")
                title = "unknown_video"
                video_id = video_url.split("v=")[-1] if "v=" in video_url else None

            # Perform the actual video download
            ydl.download([video_url])
            logging.info(f"Successfully downloaded video: {title}")
            
            # Download transcript if enabled
            if DOWNLOAD_TRANSCRIPTS and video_id:
                if download_transcript(video_id, output_path, title, video_url):
                    logging.info(f"Successfully downloaded transcript for: {title}")
                    transcript_success = True
                else:
                    logging.warning(f"Failed to download transcript for: {title}")
            
            return True, transcript_success
    except yt_dlp.utils.DownloadError as dl_err:
        # Specific download errors (like video unavailable, private, etc.)
        logging.error(f"DownloadError for {video_url}: {str(dl_err)}")
        return False, False
    except Exception as e:
        # Other unexpected errors
        logging.error(f"Unexpected error downloading {video_url}: {str(e)}")
        return False, False

def main():
    logging.info("--- Starting YouTube Channel Downloader (Videos & Streams with Transcripts) ---")

    # Check for dependencies (optional, as import would fail anyway)
    try:
        import yt_dlp
    except ImportError:
        logging.error("FATAL: yt-dlp is not installed. Please install it using 'pip install yt-dlp'")
        return
        
    if DOWNLOAD_TRANSCRIPTS:
        try:
            from youtube_transcript_api import YouTubeTranscriptApi
        except ImportError:
            logging.error("FATAL: youtube_transcript_api is not installed. Please install it using 'pip install youtube_transcript_api'")
            return

    # Ensure output folder exists
    if not os.path.exists(OUTPUT_FOLDER):
        try:
            os.makedirs(OUTPUT_FOLDER)
            logging.info(f"Created output folder: {OUTPUT_FOLDER}")
        except OSError as e:
            logging.error(f"FATAL: Could not create output folder {OUTPUT_FOLDER}: {e}")
            return

    logging.info(f"Target Channel ID: {CHANNEL_ID}")
    logging.info(f"Output Folder: {OUTPUT_FOLDER}")
    logging.info(f"Download Order: {'Oldest First' if DOWNLOAD_OLDEST_FIRST else 'Newest First'}")

    # Fetch URLs from both endpoints
    video_urls = get_all_urls_from_endpoint(CHANNEL_ID, "videos")
    stream_urls = get_all_urls_from_endpoint(CHANNEL_ID, "streams") # YouTube often uses /streams for past lives

    # Combine and deduplicate
    all_unique_urls = set(video_urls) | set(stream_urls) # Use set union for deduplication
    final_url_list = list(all_unique_urls)

    # Respect download order if needed (sets are unordered)
    # Note: This simple sort might not perfectly match YouTube's playlist order if videos appear in both lists.
    # A more complex approach would be needed for strict chronological order across both lists.
    if DOWNLOAD_OLDEST_FIRST:
         # Simple sort assuming IDs roughly correlate with upload time (not always true)
         final_url_list.sort(key=lambda url: url.split('v=')[-1])
    else:
         final_url_list.sort(key=lambda url: url.split('v=')[-1], reverse=True)


    if not final_url_list:
        logging.warning("No video URLs found for the channel. Exiting.")
        return

    total_videos = len(final_url_list)
    logging.info(f"Found {total_videos} unique videos/streams to download.")

    download_count = 0
    fail_count = 0
    transcript_success_count = 0
    transcript_fail_count = 0
    
    # Download videos one by one
    for i, url in enumerate(final_url_list, 1):
        logging.info(f"\n--- Processing video {i} of {total_videos} ---")
        video_success, transcript_success = download_video(url, OUTPUT_FOLDER)
        
        if video_success:
            download_count += 1
            if DOWNLOAD_TRANSCRIPTS:
                if transcript_success:
                    transcript_success_count += 1
                else:
                    transcript_fail_count += 1
        else:
            fail_count += 1
            logging.warning(f"Failed to download video {i}: {url}")

    logging.info("\n--- Script Execution Summary ---")
    logging.info(f"Total videos found: {total_videos}")
    logging.info(f"Successfully downloaded videos: {download_count}")
    logging.info(f"Failed video downloads: {fail_count}")
    
    if DOWNLOAD_TRANSCRIPTS:
        logging.info(f"Successfully downloaded transcripts: {transcript_success_count}")
        logging.info(f"Failed transcript downloads: {transcript_fail_count}")
        
    logging.info("--- Downloader finished ---")

if __name__ == "__main__":
    main()
