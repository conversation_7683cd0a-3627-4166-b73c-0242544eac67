WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:03.909 align:start position:0%
 
Are<00:00:00.400><c> you</c><00:00:00.560><c> a</c><00:00:00.719><c> solar</c><00:00:01.120><c> developer,</c><00:00:02.080><c> IP</c><00:00:02.960><c> or</c><00:00:03.200><c> EPC</c>

00:00:03.909 --> 00:00:03.919 align:start position:0%
Are you a solar developer, IP or EPC
 

00:00:03.919 --> 00:00:05.590 align:start position:0%
Are you a solar developer, IP or EPC
who's<00:00:04.240><c> trying</c><00:00:04.400><c> to</c><00:00:04.560><c> determine</c><00:00:04.960><c> your</c><00:00:05.200><c> domestic</c>

00:00:05.590 --> 00:00:05.600 align:start position:0%
who's trying to determine your domestic
 

00:00:05.600 --> 00:00:07.749 align:start position:0%
who's trying to determine your domestic
content<00:00:06.080><c> strategy?</c><00:00:06.799><c> We</c><00:00:07.040><c> wanted</c><00:00:07.200><c> to</c><00:00:07.440><c> make</c><00:00:07.600><c> sure</c>

00:00:07.749 --> 00:00:07.759 align:start position:0%
content strategy? We wanted to make sure
 

00:00:07.759 --> 00:00:10.150 align:start position:0%
content strategy? We wanted to make sure
you<00:00:08.080><c> knew</c><00:00:08.320><c> about</c><00:00:08.559><c> the</c><00:00:08.880><c> instant</c><00:00:09.280><c> data</c><00:00:09.920><c> and</c>

00:00:10.150 --> 00:00:10.160 align:start position:0%
you knew about the instant data and
 

00:00:10.160 --> 00:00:12.070 align:start position:0%
you knew about the instant data and
automated<00:00:10.719><c> analytics</c><00:00:11.360><c> you</c><00:00:11.599><c> could</c><00:00:11.759><c> have</c><00:00:11.920><c> at</c>

00:00:12.070 --> 00:00:12.080 align:start position:0%
automated analytics you could have at
 

00:00:12.080 --> 00:00:14.549 align:start position:0%
automated analytics you could have at
your<00:00:12.320><c> fingertips.</c><00:00:12.960><c> With</c><00:00:13.120><c> the</c><00:00:13.280><c> Anza</c><00:00:13.759><c> platform,</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
your fingertips. With the Anza platform,
 

00:00:14.559 --> 00:00:16.710 align:start position:0%
your fingertips. With the Anza platform,
you<00:00:14.799><c> no</c><00:00:15.040><c> longer</c><00:00:15.360><c> need</c><00:00:15.519><c> to</c><00:00:15.679><c> depend</c><00:00:16.000><c> on</c><00:00:16.240><c> manual</c>

00:00:16.710 --> 00:00:16.720 align:start position:0%
you no longer need to depend on manual
 

00:00:16.720 --> 00:00:19.349 align:start position:0%
you no longer need to depend on manual
analysis<00:00:17.600><c> or</c><00:00:17.920><c> static</c><00:00:18.320><c> intel</c><00:00:18.800><c> from</c><00:00:19.039><c> third</c>

00:00:19.349 --> 00:00:19.359 align:start position:0%
analysis or static intel from third
 

00:00:19.359 --> 00:00:21.750 align:start position:0%
analysis or static intel from third
parties<00:00:19.760><c> that</c><00:00:20.000><c> goes</c><00:00:20.240><c> stale</c><00:00:20.720><c> quickly.</c><00:00:21.520><c> With</c>

00:00:21.750 --> 00:00:21.760 align:start position:0%
parties that goes stale quickly. With
 

00:00:21.760 --> 00:00:25.509 align:start position:0%
parties that goes stale quickly. With
Anza,<00:00:22.640><c> you</c><00:00:22.960><c> can</c><00:00:23.119><c> see</c><00:00:23.840><c> what</c><00:00:24.320><c> module</c><00:00:24.800><c> cells</c><00:00:25.199><c> are</c>

00:00:25.509 --> 00:00:25.519 align:start position:0%
Anza, you can see what module cells are
 

00:00:25.519 --> 00:00:28.390 align:start position:0%
Anza, you can see what module cells are
manufactured<00:00:26.240><c> in</c><00:00:26.400><c> the</c><00:00:26.560><c> US</c><00:00:27.359><c> or</c><00:00:27.760><c> assembled</c><00:00:28.240><c> in</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
manufactured in the US or assembled in
 

00:00:28.400 --> 00:00:31.029 align:start position:0%
manufactured in the US or assembled in
the<00:00:28.560><c> US,</c><00:00:29.279><c> direct</c><00:00:29.599><c> from</c><00:00:29.840><c> supplier</c><00:00:30.400><c> pricing</c><00:00:30.720><c> and</c>

00:00:31.029 --> 00:00:31.039 align:start position:0%
the US, direct from supplier pricing and
 

00:00:31.039 --> 00:00:33.910 align:start position:0%
the US, direct from supplier pricing and
availability,<00:00:32.079><c> updated</c><00:00:32.640><c> monthly</c><00:00:33.360><c> or</c><00:00:33.600><c> even</c>

00:00:33.910 --> 00:00:33.920 align:start position:0%
availability, updated monthly or even
 

00:00:33.920 --> 00:00:36.389 align:start position:0%
availability, updated monthly or even
more<00:00:34.160><c> frequently,</c><00:00:35.040><c> IRS</c><00:00:35.600><c> assigned</c><00:00:36.079><c> cost</c>

00:00:36.389 --> 00:00:36.399 align:start position:0%
more frequently, IRS assigned cost
 

00:00:36.399 --> 00:00:38.790 align:start position:0%
more frequently, IRS assigned cost
percentages,<00:00:37.440><c> and</c><00:00:37.760><c> analytics</c><00:00:38.320><c> to</c><00:00:38.480><c> determine</c>

00:00:38.790 --> 00:00:38.800 align:start position:0%
percentages, and analytics to determine
 

00:00:38.800 --> 00:00:41.110 align:start position:0%
percentages, and analytics to determine
which<00:00:39.120><c> modules</c><00:00:39.680><c> create</c><00:00:39.920><c> the</c><00:00:40.160><c> most</c><00:00:40.480><c> lifetime</c>

00:00:41.110 --> 00:00:41.120 align:start position:0%
which modules create the most lifetime
 

00:00:41.120 --> 00:00:43.270 align:start position:0%
which modules create the most lifetime
value<00:00:41.360><c> for</c><00:00:41.600><c> your</c><00:00:41.840><c> project.</c><00:00:42.719><c> I'm</c><00:00:43.040><c> going</c><00:00:43.120><c> to</c>

00:00:43.270 --> 00:00:43.280 align:start position:0%
value for your project. I'm going to
 

00:00:43.280 --> 00:00:45.750 align:start position:0%
value for your project. I'm going to
show<00:00:43.440><c> you</c><00:00:43.600><c> our</c><00:00:43.840><c> latest</c><00:00:44.160><c> tool</c><00:00:44.960><c> which</c><00:00:45.200><c> automates</c>

00:00:45.750 --> 00:00:45.760 align:start position:0%
show you our latest tool which automates
 

00:00:45.760 --> 00:00:47.910 align:start position:0%
show you our latest tool which automates
your<00:00:46.000><c> domestic</c><00:00:46.480><c> and</c><00:00:46.800><c> international</c><00:00:47.440><c> blending</c>

00:00:47.910 --> 00:00:47.920 align:start position:0%
your domestic and international blending
 

00:00:47.920 --> 00:00:50.310 align:start position:0%
your domestic and international blending
analysis<00:00:48.879><c> to</c><00:00:49.200><c> determine</c><00:00:49.600><c> what</c><00:00:49.840><c> is</c><00:00:50.000><c> most</c>

00:00:50.310 --> 00:00:50.320 align:start position:0%
analysis to determine what is most
 

00:00:50.320 --> 00:00:53.510 align:start position:0%
analysis to determine what is most
optimal<00:00:51.039><c> from</c><00:00:51.280><c> a</c><00:00:51.520><c> financial</c><00:00:52.000><c> perspective.</c>

00:00:53.510 --> 00:00:53.520 align:start position:0%
optimal from a financial perspective.
 

00:00:53.520 --> 00:00:56.389 align:start position:0%
optimal from a financial perspective.
A<00:00:53.840><c> client</c><00:00:54.239><c> recently</c><00:00:54.800><c> used</c><00:00:55.039><c> this</c><00:00:55.199><c> tool</c><00:00:55.920><c> and</c><00:00:56.160><c> in</c>

00:00:56.389 --> 00:00:56.399 align:start position:0%
A client recently used this tool and in
 

00:00:56.399 --> 00:00:58.229 align:start position:0%
A client recently used this tool and in
seconds<00:00:57.199><c> determined</c><00:00:57.600><c> how</c><00:00:57.840><c> they</c><00:00:58.079><c> could</c>

00:00:58.229 --> 00:00:58.239 align:start position:0%
seconds determined how they could
 

00:00:58.239 --> 00:01:01.029 align:start position:0%
seconds determined how they could
achieve<00:00:58.640><c> the</c><00:00:58.879><c> 10%</c><00:00:59.520><c> bonus</c><00:00:59.920><c> tax</c><00:01:00.239><c> credit.</c><00:01:00.879><c> For</c>

00:01:01.029 --> 00:01:01.039 align:start position:0%
achieve the 10% bonus tax credit. For
 

00:01:01.039 --> 00:01:03.349 align:start position:0%
achieve the 10% bonus tax credit. For
example,<00:01:01.920><c> let's</c><00:01:02.160><c> say</c><00:01:02.320><c> we</c><00:01:02.559><c> have</c><00:01:02.640><c> a</c><00:01:02.879><c> tracker</c>

00:01:03.349 --> 00:01:03.359 align:start position:0%
example, let's say we have a tracker
 

00:01:03.359 --> 00:01:05.910 align:start position:0%
example, let's say we have a tracker
project<00:01:04.000><c> starting</c><00:01:04.400><c> construction</c><00:01:05.119><c> this</c><00:01:05.439><c> year</c>

00:01:05.910 --> 00:01:05.920 align:start position:0%
project starting construction this year
 

00:01:05.920 --> 00:01:08.310 align:start position:0%
project starting construction this year
with<00:01:06.240><c> US-made</c><00:01:06.880><c> racking</c><00:01:07.280><c> and</c><00:01:07.439><c> inverters.</c><00:01:08.159><c> So</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
with US-made racking and inverters. So
 

00:01:08.320 --> 00:01:10.149 align:start position:0%
with US-made racking and inverters. So
we<00:01:08.560><c> need</c><00:01:08.720><c> modules</c><00:01:09.280><c> to</c><00:01:09.439><c> contribute</c><00:01:09.920><c> the</c>

00:01:10.149 --> 00:01:10.159 align:start position:0%
we need modules to contribute the
 

00:01:10.159 --> 00:01:13.270 align:start position:0%
we need modules to contribute the
remaining<00:01:10.720><c> 15%</c><00:01:11.520><c> domestic</c><00:01:12.080><c> content.</c><00:01:12.960><c> Each</c>

00:01:13.270 --> 00:01:13.280 align:start position:0%
remaining 15% domestic content. Each
 

00:01:13.280 --> 00:01:15.510 align:start position:0%
remaining 15% domestic content. Each
results<00:01:13.920><c> shows</c><00:01:14.320><c> which</c><00:01:14.640><c> modules</c><00:01:15.119><c> meet</c><00:01:15.360><c> the</c>

00:01:15.510 --> 00:01:15.520 align:start position:0%
results shows which modules meet the
 

00:01:15.520 --> 00:01:17.910 align:start position:0%
results shows which modules meet the
domestic<00:01:15.920><c> content</c><00:01:16.320><c> criteria</c><00:01:17.280><c> and</c><00:01:17.520><c> if</c><00:01:17.680><c> it</c><00:01:17.840><c> is</c>

00:01:17.910 --> 00:01:17.920 align:start position:0%
domestic content criteria and if it is
 

00:01:17.920 --> 00:01:20.230 align:start position:0%
domestic content criteria and if it is
achieved<00:01:18.320><c> through</c><00:01:18.560><c> a</c><00:01:18.799><c> single</c><00:01:19.119><c> module</c><00:01:19.840><c> or</c><00:01:20.080><c> a</c>

00:01:20.230 --> 00:01:20.240 align:start position:0%
achieved through a single module or a
 

00:01:20.240 --> 00:01:22.710 align:start position:0%
achieved through a single module or a
domestic<00:01:20.799><c> and</c><00:01:20.960><c> international</c><00:01:21.520><c> blend.</c><00:01:22.479><c> In</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
domestic and international blend. In
 

00:01:22.720 --> 00:01:24.789 align:start position:0%
domestic and international blend. In
this<00:01:22.960><c> example,</c><00:01:23.680><c> the</c><00:01:23.920><c> most</c><00:01:24.159><c> financially</c>

00:01:24.789 --> 00:01:24.799 align:start position:0%
this example, the most financially
 

00:01:24.799 --> 00:01:27.830 align:start position:0%
this example, the most financially
attractive<00:01:25.439><c> result</c><00:01:26.240><c> has</c><00:01:26.640><c> domestic</c><00:01:27.439><c> and</c>

00:01:27.830 --> 00:01:27.840 align:start position:0%
attractive result has domestic and
 

00:01:27.840 --> 00:01:30.630 align:start position:0%
attractive result has domestic and
international<00:01:28.479><c> modules.</c><00:01:29.520><c> The</c><00:01:29.759><c> first</c><00:01:30.159><c> covers</c>

00:01:30.630 --> 00:01:30.640 align:start position:0%
international modules. The first covers
 

00:01:30.640 --> 00:01:33.429 align:start position:0%
international modules. The first covers
just<00:01:30.960><c> under</c><00:01:31.280><c> 40%</c><00:01:31.840><c> of</c><00:01:32.000><c> the</c><00:01:32.159><c> project</c><00:01:32.560><c> size</c><00:01:33.200><c> and</c>

00:01:33.429 --> 00:01:33.439 align:start position:0%
just under 40% of the project size and
 

00:01:33.439 --> 00:01:36.310 align:start position:0%
just under 40% of the project size and
the<00:01:33.680><c> second</c><00:01:34.079><c> just</c><00:01:34.400><c> over</c><00:01:34.720><c> 60.</c><00:01:35.520><c> You</c><00:01:35.759><c> can</c><00:01:35.920><c> also</c>

00:01:36.310 --> 00:01:36.320 align:start position:0%
the second just over 60. You can also
 

00:01:36.320 --> 00:01:39.190 align:start position:0%
the second just over 60. You can also
see<00:01:36.640><c> that</c><00:01:36.960><c> the</c><00:01:37.200><c> first</c><00:01:37.439><c> module</c><00:01:38.000><c> is</c><00:01:38.400><c> 38%</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
see that the first module is 38%
 

00:01:39.200 --> 00:01:41.749 align:start position:0%
see that the first module is 38%
domestic<00:01:40.000><c> while</c><00:01:40.320><c> the</c><00:01:40.479><c> second</c><00:01:40.880><c> is</c><00:01:41.280><c> completely</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
domestic while the second is completely
 

00:01:41.759 --> 00:01:44.149 align:start position:0%
domestic while the second is completely
international.<00:01:42.960><c> From</c><00:01:43.200><c> there,</c><00:01:43.600><c> you</c><00:01:43.840><c> can</c><00:01:43.920><c> add</c>

00:01:44.149 --> 00:01:44.159 align:start position:0%
international. From there, you can add
 

00:01:44.159 --> 00:01:46.789 align:start position:0%
international. From there, you can add
filters<00:01:44.640><c> to</c><00:01:44.880><c> meet</c><00:01:45.040><c> your</c><00:01:45.280><c> project's</c><00:01:45.759><c> criteria,</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
filters to meet your project's criteria,
 

00:01:46.799 --> 00:01:49.670 align:start position:0%
filters to meet your project's criteria,
such<00:01:47.040><c> as</c><00:01:47.360><c> a</c><00:01:47.600><c> tier</c><00:01:47.920><c> one</c><00:01:48.159><c> supplier,</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
such as a tier one supplier,
 

00:01:49.680 --> 00:01:51.590 align:start position:0%
such as a tier one supplier,
or<00:01:50.000><c> enable</c><00:01:50.320><c> the</c><00:01:50.560><c> tool</c><00:01:50.799><c> to</c><00:01:50.960><c> make</c><00:01:51.119><c> even</c><00:01:51.360><c> more</c>

00:01:51.590 --> 00:01:51.600 align:start position:0%
or enable the tool to make even more
 

00:01:51.600 --> 00:01:54.310 align:start position:0%
or enable the tool to make even more
valuable<00:01:52.079><c> recommendations</c><00:01:53.040><c> by</c><00:01:53.360><c> optimizing</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
valuable recommendations by optimizing
 

00:01:54.320 --> 00:01:57.190 align:start position:0%
valuable recommendations by optimizing
around<00:01:54.720><c> different</c><00:01:55.040><c> wattages,</c><00:01:56.079><c> suppliers,</c><00:01:56.960><c> or</c>

00:01:57.190 --> 00:01:57.200 align:start position:0%
around different wattages, suppliers, or
 

00:01:57.200 --> 00:01:59.350 align:start position:0%
around different wattages, suppliers, or
frame<00:01:57.520><c> sizes.</c><00:01:58.399><c> Once</c><00:01:58.719><c> you've</c><00:01:58.960><c> made</c><00:01:59.119><c> your</c>

00:01:59.350 --> 00:01:59.360 align:start position:0%
frame sizes. Once you've made your
 

00:01:59.360 --> 00:02:02.310 align:start position:0%
frame sizes. Once you've made your
selection,<00:02:00.159><c> you</c><00:02:00.399><c> can</c><00:02:00.560><c> access</c><00:02:01.200><c> all</c><00:02:01.439><c> of</c><00:02:01.600><c> ANZA's</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
selection, you can access all of ANZA's
 

00:02:02.320 --> 00:02:05.109 align:start position:0%
selection, you can access all of ANZA's
technical,<00:02:03.280><c> commercial,</c><00:02:04.159><c> and</c><00:02:04.479><c> counterparty</c>

00:02:05.109 --> 00:02:05.119 align:start position:0%
technical, commercial, and counterparty
 

00:02:05.119 --> 00:02:07.910 align:start position:0%
technical, commercial, and counterparty
data<00:02:05.840><c> across</c><00:02:06.320><c> hundreds</c><00:02:06.640><c> of</c><00:02:06.799><c> data</c><00:02:07.119><c> points</c><00:02:07.680><c> like</c>

00:02:07.910 --> 00:02:07.920 align:start position:0%
data across hundreds of data points like
 

00:02:07.920 --> 00:02:10.309 align:start position:0%
data across hundreds of data points like
the<00:02:08.160><c> most</c><00:02:08.399><c> accurate</c><00:02:08.879><c> PAN</c><00:02:09.280><c> file</c><00:02:09.679><c> and</c><00:02:09.920><c> how</c><00:02:10.160><c> to</c>

00:02:10.309 --> 00:02:10.319 align:start position:0%
the most accurate PAN file and how to
 

00:02:10.319 --> 00:02:12.710 align:start position:0%
the most accurate PAN file and how to
contact<00:02:10.720><c> the</c><00:02:10.959><c> supplier.</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
contact the supplier.
 

00:02:12.720 --> 00:02:15.589 align:start position:0%
contact the supplier.
With<00:02:12.959><c> ANZA,</c><00:02:13.920><c> you</c><00:02:14.160><c> can</c><00:02:14.319><c> save</c><00:02:14.640><c> weeks</c><00:02:15.040><c> to</c><00:02:15.280><c> months</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
With ANZA, you can save weeks to months
 

00:02:15.599 --> 00:02:17.670 align:start position:0%
With ANZA, you can save weeks to months
of<00:02:15.840><c> time,</c><00:02:16.560><c> improve</c><00:02:16.959><c> your</c><00:02:17.200><c> competitive</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
of time, improve your competitive
 

00:02:17.680 --> 00:02:20.229 align:start position:0%
of time, improve your competitive
position,<00:02:18.400><c> and</c><00:02:18.879><c> ultimately</c><00:02:19.760><c> increase</c>

00:02:20.229 --> 00:02:20.239 align:start position:0%
position, and ultimately increase
 

00:02:20.239 --> 00:02:23.589 align:start position:0%
position, and ultimately increase
project<00:02:20.640><c> profits.</c><00:02:21.520><c> Visit</c><00:02:22.000><c> anzrenewables.com</c>

00:02:23.589 --> 00:02:23.599 align:start position:0%
project profits. Visit anzrenewables.com
 

00:02:23.599 --> 00:02:29.530 align:start position:0%
project profits. Visit anzrenewables.com
to<00:02:23.840><c> schedule</c><00:02:24.160><c> a</c><00:02:24.400><c> demo</c><00:02:24.879><c> to</c><00:02:25.120><c> learn</c><00:02:25.360><c> more.</c>

00:02:29.530 --> 00:02:29.540 align:start position:0%
 
 

00:02:29.540 --> 00:02:33.990 align:start position:0%
 
[Music]

