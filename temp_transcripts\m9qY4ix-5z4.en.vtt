WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:08.070 align:start position:0%
 
[Music]

00:00:08.070 --> 00:00:08.080 align:start position:0%
 
 

00:00:08.080 --> 00:00:09.830 align:start position:0%
 
thank<00:00:08.240><c> you</c><00:00:08.400><c> all</c><00:00:08.719><c> for</c><00:00:09.080><c> watching</c><00:00:09.519><c> this</c>

00:00:09.830 --> 00:00:09.840 align:start position:0%
thank you all for watching this
 

00:00:09.840 --> 00:00:14.549 align:start position:0%
thank you all for watching this
presentation<00:00:10.599><c> about</c><00:00:11.320><c> uh</c><00:00:11.519><c> we8</c><00:00:12.519><c> so</c><00:00:12.719><c> we8</c><00:00:13.240><c> is</c><00:00:13.360><c> a</c><00:00:14.120><c> um</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
presentation about uh we8 so we8 is a um
 

00:00:14.559 --> 00:00:16.510 align:start position:0%
presentation about uh we8 so we8 is a um
effected<00:00:14.960><c> database</c><00:00:15.360><c> or</c><00:00:15.480><c> an</c><00:00:15.639><c> AI</c><00:00:15.879><c> native</c><00:00:16.199><c> Factor</c>

00:00:16.510 --> 00:00:16.520 align:start position:0%
effected database or an AI native Factor
 

00:00:16.520 --> 00:00:19.070 align:start position:0%
effected database or an AI native Factor
database<00:00:16.960><c> and</c><00:00:17.119><c> one</c><00:00:17.279><c> of</c><00:00:17.439><c> the</c><00:00:18.439><c> um</c><00:00:18.760><c> you</c><00:00:18.880><c> know</c>

00:00:19.070 --> 00:00:19.080 align:start position:0%
database and one of the um you know
 

00:00:19.080 --> 00:00:22.790 align:start position:0%
database and one of the um you know
Focus<00:00:19.439><c> areas</c><00:00:19.800><c> that</c><00:00:19.960><c> we</c><00:00:20.279><c> have</c><00:00:21.160><c> is</c><00:00:21.680><c> on</c><00:00:22.279><c> allowing</c>

00:00:22.790 --> 00:00:22.800 align:start position:0%
Focus areas that we have is on allowing
 

00:00:22.800 --> 00:00:25.630 align:start position:0%
Focus areas that we have is on allowing
people<00:00:23.119><c> to</c><00:00:23.400><c> create</c><00:00:24.400><c> endtoend</c><00:00:24.760><c> AI</c><00:00:25.240><c> native</c>

00:00:25.630 --> 00:00:25.640 align:start position:0%
people to create endtoend AI native
 

00:00:25.640 --> 00:00:28.189 align:start position:0%
people to create endtoend AI native
applications<00:00:26.279><c> and</c><00:00:26.400><c> in</c><00:00:26.880><c> this</c><00:00:27.199><c> presentation</c>

00:00:28.189 --> 00:00:28.199 align:start position:0%
applications and in this presentation
 

00:00:28.199 --> 00:00:30.070 align:start position:0%
applications and in this presentation
I'm<00:00:28.359><c> going</c><00:00:28.480><c> to</c><00:00:28.880><c> talk</c><00:00:29.119><c> about</c><00:00:29.400><c> what</c><00:00:29.560><c> that</c><00:00:29.720><c> is</c><00:00:30.000><c> and</c>

00:00:30.070 --> 00:00:30.080 align:start position:0%
I'm going to talk about what that is and
 

00:00:30.080 --> 00:00:32.150 align:start position:0%
I'm going to talk about what that is and
I'm<00:00:30.199><c> going</c><00:00:30.279><c> to</c><00:00:30.400><c> give</c><00:00:30.560><c> a</c><00:00:30.720><c> quick</c><00:00:31.000><c> demo</c><00:00:31.880><c> where</c><00:00:32.040><c> you</c>

00:00:32.150 --> 00:00:32.160 align:start position:0%
I'm going to give a quick demo where you
 

00:00:32.160 --> 00:00:33.350 align:start position:0%
I'm going to give a quick demo where you
can<00:00:32.320><c> see</c><00:00:32.559><c> that</c><00:00:32.719><c> so</c><00:00:32.840><c> we're</c><00:00:32.960><c> going</c><00:00:33.079><c> to</c><00:00:33.200><c> talk</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
can see that so we're going to talk
 

00:00:33.360 --> 00:00:35.030 align:start position:0%
can see that so we're going to talk
about<00:00:33.600><c> Factor</c><00:00:33.960><c> search</c><00:00:34.280><c> we're</c><00:00:34.399><c> going</c><00:00:34.520><c> to</c><00:00:34.680><c> talk</c>

00:00:35.030 --> 00:00:35.040 align:start position:0%
about Factor search we're going to talk
 

00:00:35.040 --> 00:00:37.950 align:start position:0%
about Factor search we're going to talk
about<00:00:36.040><c> retrieval</c><00:00:36.559><c> augmented</c><00:00:37.040><c> generation</c><00:00:37.680><c> or</c>

00:00:37.950 --> 00:00:37.960 align:start position:0%
about retrieval augmented generation or
 

00:00:37.960 --> 00:00:42.350 align:start position:0%
about retrieval augmented generation or
rag<00:00:38.559><c> and</c><00:00:39.399><c> um</c><00:00:40.239><c> gener</c><00:00:40.800><c> generic</c><00:00:41.520><c> or</c><00:00:41.920><c> generative</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
rag and um gener generic or generative
 

00:00:42.360 --> 00:00:46.430 align:start position:0%
rag and um gener generic or generative
feedback<00:00:42.680><c> loops</c><00:00:43.360><c> so</c><00:00:43.600><c> let's</c><00:00:43.879><c> Dive</c><00:00:44.239><c> Right</c><00:00:44.480><c> In</c><00:00:45.480><c> so</c>

00:00:46.430 --> 00:00:46.440 align:start position:0%
feedback loops so let's Dive Right In so
 

00:00:46.440 --> 00:00:48.830 align:start position:0%
feedback loops so let's Dive Right In so
um<00:00:46.800><c> a</c><00:00:47.000><c> lot</c><00:00:47.239><c> is</c><00:00:47.440><c> happening</c><00:00:48.000><c> in</c><00:00:48.160><c> our</c><00:00:48.360><c> space</c><00:00:48.719><c> that</c>

00:00:48.830 --> 00:00:48.840 align:start position:0%
um a lot is happening in our space that
 

00:00:48.840 --> 00:00:51.470 align:start position:0%
um a lot is happening in our space that
I'm<00:00:49.039><c> very</c><00:00:49.280><c> proud</c><00:00:49.520><c> of</c><00:00:49.680><c> so</c><00:00:49.960><c> we</c><00:00:50.120><c> are</c><00:00:50.680><c> nearing</c><00:00:51.280><c> the</c>

00:00:51.470 --> 00:00:51.480 align:start position:0%
I'm very proud of so we are nearing the
 

00:00:51.480 --> 00:00:54.910 align:start position:0%
I'm very proud of so we are nearing the
end<00:00:52.039><c> of</c><00:00:52.800><c> um</c><00:00:53.120><c> 2023</c><00:00:53.960><c> but</c><00:00:54.079><c> it</c><00:00:54.199><c> has</c><00:00:54.320><c> been</c><00:00:54.520><c> an</c>

00:00:54.910 --> 00:00:54.920 align:start position:0%
end of um 2023 but it has been an
 

00:00:54.920 --> 00:00:59.110 align:start position:0%
end of um 2023 but it has been an
amazing<00:00:55.920><c> uh</c><00:00:56.199><c> year</c><00:00:57.199><c> so</c><00:00:57.920><c> um</c><00:00:58.160><c> I</c><00:00:58.239><c> was</c><00:00:58.480><c> very</c><00:00:58.760><c> proud</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
amazing uh year so um I was very proud
 

00:00:59.120 --> 00:01:00.430 align:start position:0%
amazing uh year so um I was very proud
this<00:00:59.280><c> year</c><00:00:59.559><c> during</c>

00:01:00.430 --> 00:01:00.440 align:start position:0%
this year during
 

00:01:00.440 --> 00:01:02.670 align:start position:0%
this year during
uh<00:01:00.719><c> uh</c><00:01:00.879><c> Google</c><00:01:01.239><c> IO</c><00:01:01.760><c> that</c><00:01:01.960><c> when</c><00:01:02.120><c> they</c><00:01:02.280><c> released</c>

00:01:02.670 --> 00:01:02.680 align:start position:0%
uh uh Google IO that when they released
 

00:01:02.680 --> 00:01:05.390 align:start position:0%
uh uh Google IO that when they released
their<00:01:02.879><c> models</c><00:01:03.680><c> they</c><00:01:03.800><c> were</c><00:01:04.000><c> so</c><00:01:04.320><c> kind</c><00:01:04.640><c> to</c><00:01:05.000><c> to</c><00:01:05.239><c> to</c>

00:01:05.390 --> 00:01:05.400 align:start position:0%
their models they were so kind to to to
 

00:01:05.400 --> 00:01:08.070 align:start position:0%
their models they were so kind to to to
feature<00:01:05.799><c> we</c><00:01:06.119><c> it</c><00:01:06.280><c> as</c><00:01:06.400><c> well</c><00:01:07.080><c> but</c><00:01:07.240><c> also</c><00:01:07.479><c> from</c><00:01:07.680><c> a</c>

00:01:08.070 --> 00:01:08.080 align:start position:0%
feature we it as well but also from a
 

00:01:08.080 --> 00:01:11.390 align:start position:0%
feature we it as well but also from a
you<00:01:08.200><c> know</c><00:01:08.400><c> more</c><00:01:08.720><c> larger</c><00:01:09.720><c> uh</c><00:01:09.880><c> infra</c><00:01:10.759><c> structure</c>

00:01:11.390 --> 00:01:11.400 align:start position:0%
you know more larger uh infra structure
 

00:01:11.400 --> 00:01:14.590 align:start position:0%
you know more larger uh infra structure
perspective<00:01:12.400><c> um</c><00:01:13.040><c> uh</c><00:01:13.200><c> people</c><00:01:13.479><c> also</c><00:01:13.720><c> start</c><00:01:14.000><c> to</c>

00:01:14.590 --> 00:01:14.600 align:start position:0%
perspective um uh people also start to
 

00:01:14.600 --> 00:01:16.270 align:start position:0%
perspective um uh people also start to
you<00:01:14.720><c> know</c><00:01:14.960><c> appreciate</c><00:01:15.439><c> what's</c><00:01:15.640><c> happening</c><00:01:16.040><c> it</c>

00:01:16.270 --> 00:01:16.280 align:start position:0%
you know appreciate what's happening it
 

00:01:16.280 --> 00:01:20.270 align:start position:0%
you know appreciate what's happening it
really<00:01:16.560><c> starts</c><00:01:16.960><c> to</c><00:01:17.880><c> um</c><00:01:18.280><c> become</c><00:01:18.479><c> a</c><00:01:18.880><c> new</c><00:01:19.880><c> um</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
really starts to um become a new um
 

00:01:20.280 --> 00:01:22.510 align:start position:0%
really starts to um become a new um
category<00:01:21.040><c> in</c><00:01:21.200><c> the</c><00:01:21.439><c> space</c><00:01:21.720><c> of</c><00:01:21.920><c> infrastructure</c>

00:01:22.510 --> 00:01:22.520 align:start position:0%
category in the space of infrastructure
 

00:01:22.520 --> 00:01:25.030 align:start position:0%
category in the space of infrastructure
these<00:01:22.720><c> Factor</c><00:01:23.040><c> databases</c><00:01:23.600><c> so</c><00:01:23.880><c> I'm</c><00:01:24.720><c> super</c>

00:01:25.030 --> 00:01:25.040 align:start position:0%
these Factor databases so I'm super
 

00:01:25.040 --> 00:01:27.749 align:start position:0%
these Factor databases so I'm super
happy<00:01:25.320><c> to</c><00:01:25.759><c> you</c><00:01:25.880><c> know</c><00:01:26.040><c> talk</c><00:01:26.240><c> more</c><00:01:26.479><c> about</c><00:01:26.759><c> them</c>

00:01:27.749 --> 00:01:27.759 align:start position:0%
happy to you know talk more about them
 

00:01:27.759 --> 00:01:29.429 align:start position:0%
happy to you know talk more about them
uh<00:01:28.079><c> so</c><00:01:28.280><c> to</c><00:01:28.400><c> start</c><00:01:28.640><c> off</c><00:01:28.759><c> so</c><00:01:29.079><c> the</c><00:01:29.240><c> what</c><00:01:29.360><c> we</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
uh so to start off so the what we
 

00:01:29.439 --> 00:01:31.510 align:start position:0%
uh so to start off so the what we
believe<00:01:29.640><c> is</c><00:01:29.759><c> that</c><00:01:30.040><c> the</c><00:01:30.200><c> best</c><00:01:30.680><c> AI</c><00:01:31.040><c> applications</c>

00:01:31.510 --> 00:01:31.520 align:start position:0%
believe is that the best AI applications
 

00:01:31.520 --> 00:01:33.590 align:start position:0%
believe is that the best AI applications
are<00:01:31.720><c> created</c><00:01:32.200><c> by</c><00:01:32.439><c> weaving</c><00:01:32.960><c> together</c><00:01:33.240><c> the</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
are created by weaving together the
 

00:01:33.600 --> 00:01:37.310 align:start position:0%
are created by weaving together the
database<00:01:34.399><c> and</c><00:01:34.600><c> the</c><00:01:34.880><c> and</c><00:01:35.000><c> the</c><00:01:35.119><c> model</c><00:01:35.560><c> so</c><00:01:36.560><c> um</c><00:01:36.920><c> the</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
database and the and the model so um the
 

00:01:37.320 --> 00:01:40.990 align:start position:0%
database and the and the model so um the
strong<00:01:37.680><c> the</c><00:01:37.920><c> the</c><00:01:38.200><c> the</c><00:01:38.920><c> the</c><00:01:39.640><c> uh</c><00:01:40.360><c> the</c><00:01:40.560><c> better</c><00:01:40.880><c> we</c>

00:01:40.990 --> 00:01:41.000 align:start position:0%
strong the the the the uh the better we
 

00:01:41.000 --> 00:01:43.350 align:start position:0%
strong the the the the uh the better we
can<00:01:41.159><c> bring</c><00:01:41.520><c> the</c><00:01:41.840><c> database</c><00:01:42.479><c> and</c><00:01:42.600><c> the</c><00:01:42.720><c> models</c>

00:01:43.350 --> 00:01:43.360 align:start position:0%
can bring the database and the models
 

00:01:43.360 --> 00:01:45.510 align:start position:0%
can bring the database and the models
together<00:01:44.240><c> the</c><00:01:44.439><c> easier</c><00:01:44.799><c> it</c><00:01:44.960><c> becomes</c><00:01:45.240><c> for</c><00:01:45.399><c> you</c>

00:01:45.510 --> 00:01:45.520 align:start position:0%
together the easier it becomes for you
 

00:01:45.520 --> 00:01:47.910 align:start position:0%
together the easier it becomes for you
to<00:01:45.680><c> create</c><00:01:46.040><c> these</c><00:01:46.240><c> kinds</c><00:01:46.439><c> of</c><00:01:46.920><c> applications</c>

00:01:47.910 --> 00:01:47.920 align:start position:0%
to create these kinds of applications
 

00:01:47.920 --> 00:01:50.310 align:start position:0%
to create these kinds of applications
and<00:01:48.079><c> we</c><00:01:48.399><c> is</c><00:01:48.560><c> open</c><00:01:48.880><c> source</c><00:01:49.600><c> so</c><00:01:49.799><c> that</c><00:01:49.920><c> means</c><00:01:50.159><c> that</c>

00:01:50.310 --> 00:01:50.320 align:start position:0%
and we is open source so that means that
 

00:01:50.320 --> 00:01:52.429 align:start position:0%
and we is open source so that means that
it<00:01:50.479><c> allows</c><00:01:50.799><c> our</c><00:01:51.000><c> community</c><00:01:51.479><c> of</c><00:01:51.719><c> customers</c><00:01:52.240><c> and</c>

00:01:52.429 --> 00:01:52.439 align:start position:0%
it allows our community of customers and
 

00:01:52.439 --> 00:01:56.190 align:start position:0%
it allows our community of customers and
users<00:01:52.920><c> to</c><00:01:53.880><c> um</c><00:01:54.079><c> use</c><00:01:54.479><c> with</c><00:01:54.719><c> it</c><00:01:55.000><c> any</c><00:01:55.280><c> way</c><00:01:55.840><c> shape</c><00:01:56.039><c> or</c>

00:01:56.190 --> 00:01:56.200 align:start position:0%
users to um use with it any way shape or
 

00:01:56.200 --> 00:01:59.069 align:start position:0%
users to um use with it any way shape or
form<00:01:56.439><c> that</c><00:01:56.600><c> they</c><00:01:56.759><c> want</c><00:01:57.240><c> so</c><00:01:58.000><c> we</c><00:01:58.280><c> have</c><00:01:58.680><c> of</c><00:01:58.840><c> course</c>

00:01:59.069 --> 00:01:59.079 align:start position:0%
form that they want so we have of course
 

00:01:59.079 --> 00:02:02.590 align:start position:0%
form that they want so we have of course
open<00:01:59.439><c> source</c><00:02:00.200><c> we</c><00:02:00.439><c> have</c><00:02:01.119><c> uh</c><00:02:01.439><c> containers</c><00:02:02.439><c> we</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
open source we have uh containers we
 

00:02:02.600 --> 00:02:05.510 align:start position:0%
open source we have uh containers we
need<00:02:02.719><c> a</c><00:02:03.039><c> setup</c><00:02:04.039><c> embedded</c><00:02:04.680><c> but</c><00:02:04.840><c> we</c><00:02:04.960><c> also</c><00:02:05.200><c> have</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
need a setup embedded but we also have
 

00:02:05.520 --> 00:02:07.389 align:start position:0%
need a setup embedded but we also have
server<00:02:06.000><c> the</c><00:02:06.280><c> SAS</c><00:02:06.640><c> we</c><00:02:06.719><c> have</c><00:02:06.840><c> bring</c><00:02:07.039><c> your</c><00:02:07.159><c> on</c>

00:02:07.389 --> 00:02:07.399 align:start position:0%
server the SAS we have bring your on
 

00:02:07.399 --> 00:02:09.749 align:start position:0%
server the SAS we have bring your on
cloud<00:02:08.239><c> so</c><00:02:08.679><c> really</c><00:02:08.920><c> depending</c><00:02:09.239><c> on</c><00:02:09.440><c> how</c><00:02:09.640><c> you</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
cloud so really depending on how you
 

00:02:09.759 --> 00:02:11.790 align:start position:0%
cloud so really depending on how you
want<00:02:09.879><c> to</c><00:02:10.160><c> consume</c><00:02:10.560><c> the</c><00:02:10.720><c> database</c><00:02:11.319><c> we</c><00:02:11.520><c> have</c>

00:02:11.790 --> 00:02:11.800 align:start position:0%
want to consume the database we have
 

00:02:11.800 --> 00:02:15.910 align:start position:0%
want to consume the database we have
that<00:02:12.080><c> for</c><00:02:12.680><c> you</c><00:02:13.680><c> and</c><00:02:14.480><c> um</c><00:02:14.800><c> this</c><00:02:15.000><c> enables</c><00:02:15.440><c> you</c><00:02:15.680><c> to</c>

00:02:15.910 --> 00:02:15.920 align:start position:0%
that for you and um this enables you to
 

00:02:15.920 --> 00:02:18.110 align:start position:0%
that for you and um this enables you to
create<00:02:16.360><c> secure</c><00:02:17.200><c> okay</c><00:02:17.360><c> so</c><00:02:17.480><c> what</c><00:02:17.599><c> we</c><00:02:17.720><c> mean</c><00:02:17.920><c> with</c>

00:02:18.110 --> 00:02:18.120 align:start position:0%
create secure okay so what we mean with
 

00:02:18.120 --> 00:02:20.750 align:start position:0%
create secure okay so what we mean with
secure<00:02:18.680><c> in</c><00:02:18.840><c> this</c><00:02:19.080><c> context</c><00:02:19.720><c> is</c><00:02:20.440><c> when</c><00:02:20.599><c> it's</c>

00:02:20.750 --> 00:02:20.760 align:start position:0%
secure in this context is when it's
 

00:02:20.760 --> 00:02:23.229 align:start position:0%
secure in this context is when it's
serverless<00:02:21.319><c> you</c><00:02:21.440><c> might</c><00:02:21.599><c> want</c><00:02:21.720><c> to</c><00:02:21.879><c> have</c><00:02:22.040><c> sock</c><00:02:22.360><c> 2</c>

00:02:23.229 --> 00:02:23.239 align:start position:0%
serverless you might want to have sock 2
 

00:02:23.239 --> 00:02:25.190 align:start position:0%
serverless you might want to have sock 2
or<00:02:23.480><c> when</c><00:02:23.640><c> you</c><00:02:23.879><c> want</c><00:02:24.000><c> to</c><00:02:24.319><c> keep</c><00:02:24.480><c> the</c><00:02:24.640><c> data</c><00:02:24.879><c> closed</c>

00:02:25.190 --> 00:02:25.200 align:start position:0%
or when you want to keep the data closed
 

00:02:25.200 --> 00:02:27.270 align:start position:0%
or when you want to keep the data closed
you<00:02:25.319><c> want</c><00:02:25.400><c> to</c><00:02:25.599><c> bring</c><00:02:25.840><c> your</c><00:02:25.959><c> own</c><00:02:26.280><c> cloud</c>

00:02:27.270 --> 00:02:27.280 align:start position:0%
you want to bring your own cloud
 

00:02:27.280 --> 00:02:28.869 align:start position:0%
you want to bring your own cloud
explainable<00:02:28.040><c> so</c><00:02:28.239><c> what</c><00:02:28.319><c> we</c><00:02:28.440><c> mean</c><00:02:28.640><c> with</c>

00:02:28.869 --> 00:02:28.879 align:start position:0%
explainable so what we mean with
 

00:02:28.879 --> 00:02:31.550 align:start position:0%
explainable so what we mean with
explainable<00:02:29.480><c> is</c><00:02:29.599><c> that</c><00:02:29.920><c> every</c><00:02:30.200><c> time</c><00:02:30.840><c> you</c><00:02:31.160><c> have</c>

00:02:31.550 --> 00:02:31.560 align:start position:0%
explainable is that every time you have
 

00:02:31.560 --> 00:02:36.910 align:start position:0%
explainable is that every time you have
a<00:02:32.200><c> um</c><00:02:33.080><c> uh</c><00:02:33.239><c> you</c><00:02:33.440><c> use</c><00:02:33.840><c> a</c><00:02:34.519><c> uh</c><00:02:34.720><c> generative</c><00:02:35.720><c> uh</c><00:02:35.920><c> model</c>

00:02:36.910 --> 00:02:36.920 align:start position:0%
a um uh you use a uh generative uh model
 

00:02:36.920 --> 00:02:38.869 align:start position:0%
a um uh you use a uh generative uh model
you<00:02:37.040><c> can</c><00:02:37.360><c> always</c><00:02:37.800><c> say</c><00:02:38.120><c> okay</c><00:02:38.400><c> whatever</c><00:02:38.720><c> was</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
you can always say okay whatever was
 

00:02:38.879 --> 00:02:42.030 align:start position:0%
you can always say okay whatever was
generated<00:02:39.440><c> was</c><00:02:39.680><c> based</c><00:02:40.080><c> on</c><00:02:40.920><c> this</c><00:02:41.239><c> data</c><00:02:41.720><c> so</c><00:02:41.879><c> it</c>

00:02:42.030 --> 00:02:42.040 align:start position:0%
generated was based on this data so it
 

00:02:42.040 --> 00:02:43.790 align:start position:0%
generated was based on this data so it
creates<00:02:42.480><c> like</c><00:02:42.640><c> it's</c><00:02:42.760><c> a</c><00:02:42.879><c> form</c><00:02:43.120><c> of</c><00:02:43.280><c> explainable</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
creates like it's a form of explainable
 

00:02:43.800 --> 00:02:46.270 align:start position:0%
creates like it's a form of explainable
AI<00:02:44.760><c> and</c><00:02:44.879><c> something</c><00:02:45.159><c> that</c><00:02:45.280><c> we</c><00:02:45.400><c> call</c><00:02:45.640><c> stateful</c>

00:02:46.270 --> 00:02:46.280 align:start position:0%
AI and something that we call stateful
 

00:02:46.280 --> 00:02:48.110 align:start position:0%
AI and something that we call stateful
and<00:02:46.400><c> what</c><00:02:46.519><c> we</c><00:02:46.640><c> mean</c><00:02:46.920><c> with</c><00:02:47.120><c> stateful</c><00:02:47.640><c> is</c><00:02:47.800><c> that</c>

00:02:48.110 --> 00:02:48.120 align:start position:0%
and what we mean with stateful is that
 

00:02:48.120 --> 00:02:51.550 align:start position:0%
and what we mean with stateful is that
the<00:02:49.040><c> uh</c><00:02:49.159><c> generative</c><00:02:49.760><c> models</c><00:02:50.760><c> are</c><00:02:51.080><c> currently</c>

00:02:51.550 --> 00:02:51.560 align:start position:0%
the uh generative models are currently
 

00:02:51.560 --> 00:02:54.309 align:start position:0%
the uh generative models are currently
stateless<00:02:52.480><c> so</c><00:02:53.200><c> if</c><00:02:53.280><c> you</c><00:02:53.440><c> ever</c><00:02:53.640><c> Ed</c><00:02:53.920><c> one</c><00:02:54.040><c> of</c><00:02:54.159><c> these</c>

00:02:54.309 --> 00:02:54.319 align:start position:0%
stateless so if you ever Ed one of these
 

00:02:54.319 --> 00:02:56.710 align:start position:0%
stateless so if you ever Ed one of these
models<00:02:54.720><c> it</c><00:02:54.879><c> might</c><00:02:55.319><c> sometimes</c><00:02:56.120><c> reply</c><00:02:56.560><c> with</c>

00:02:56.710 --> 00:02:56.720 align:start position:0%
models it might sometimes reply with
 

00:02:56.720 --> 00:02:58.509 align:start position:0%
models it might sometimes reply with
something<00:02:57.120><c> like</c><00:02:57.280><c> you</c><00:02:57.360><c> know</c><00:02:57.640><c> I'm</c><00:02:57.760><c> trained</c><00:02:58.200><c> in</c><00:02:58.440><c> I</c>

00:02:58.509 --> 00:02:58.519 align:start position:0%
something like you know I'm trained in I
 

00:02:58.519 --> 00:03:02.110 align:start position:0%
something like you know I'm trained in I
don't<00:02:58.640><c> know</c><00:02:58.840><c> 2021</c><00:02:59.800><c> or</c><00:03:00.200><c> 2022</c><00:03:01.200><c> so</c><00:03:01.400><c> I</c><00:03:01.480><c> don't</c><00:03:01.760><c> have</c>

00:03:02.110 --> 00:03:02.120 align:start position:0%
don't know 2021 or 2022 so I don't have
 

00:03:02.120 --> 00:03:04.149 align:start position:0%
don't know 2021 or 2022 so I don't have
access<00:03:02.400><c> to</c><00:03:02.560><c> this</c><00:03:02.760><c> information</c><00:03:03.680><c> or</c><00:03:03.840><c> it</c><00:03:04.000><c> might</c>

00:03:04.149 --> 00:03:04.159 align:start position:0%
access to this information or it might
 

00:03:04.159 --> 00:03:06.149 align:start position:0%
access to this information or it might
say<00:03:04.440><c> something</c><00:03:04.920><c> like</c><00:03:05.239><c> hey</c><00:03:05.480><c> the</c><00:03:05.760><c> the</c><00:03:05.920><c> question</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
say something like hey the the question
 

00:03:06.159 --> 00:03:07.509 align:start position:0%
say something like hey the the question
you're<00:03:06.360><c> asking</c><00:03:06.640><c> or</c><00:03:06.799><c> the</c><00:03:07.000><c> information</c><00:03:07.400><c> you</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
you're asking or the information you
 

00:03:07.519 --> 00:03:09.830 align:start position:0%
you're asking or the information you
want<00:03:07.920><c> that's</c><00:03:08.159><c> something</c><00:03:09.080><c> I</c><00:03:09.200><c> was</c><00:03:09.360><c> not</c><00:03:09.519><c> trained</c>

00:03:09.830 --> 00:03:09.840 align:start position:0%
want that's something I was not trained
 

00:03:09.840 --> 00:03:12.270 align:start position:0%
want that's something I was not trained
on<00:03:10.000><c> so</c><00:03:10.159><c> I</c><00:03:10.280><c> don't</c><00:03:10.480><c> know</c><00:03:11.080><c> so</c><00:03:11.319><c> what</c><00:03:11.440><c> we</c><00:03:11.560><c> can</c><00:03:11.760><c> do</c><00:03:12.080><c> by</c>

00:03:12.270 --> 00:03:12.280 align:start position:0%
on so I don't know so what we can do by
 

00:03:12.280 --> 00:03:14.789 align:start position:0%
on so I don't know so what we can do by
combining<00:03:12.799><c> the</c><00:03:12.920><c> models</c><00:03:13.360><c> with</c><00:03:13.640><c> database</c><00:03:14.640><c> is</c>

00:03:14.789 --> 00:03:14.799 align:start position:0%
combining the models with database is
 

00:03:14.799 --> 00:03:17.869 align:start position:0%
combining the models with database is
that<00:03:15.000><c> we</c><00:03:15.120><c> can</c><00:03:15.360><c> make</c><00:03:16.040><c> the</c><00:03:16.400><c> S</c><00:03:16.879><c> um</c><00:03:17.280><c> the</c><00:03:17.519><c> the</c><00:03:17.799><c> the</c>

00:03:17.869 --> 00:03:17.879 align:start position:0%
that we can make the S um the the the
 

00:03:17.879 --> 00:03:20.030 align:start position:0%
that we can make the S um the the the
apps<00:03:18.120><c> that</c><00:03:18.239><c> you're</c><00:03:18.400><c> building</c><00:03:18.680><c> State</c><00:03:19.000><c> full</c><00:03:19.920><c> so</c>

00:03:20.030 --> 00:03:20.040 align:start position:0%
apps that you're building State full so
 

00:03:20.040 --> 00:03:21.350 align:start position:0%
apps that you're building State full so
you<00:03:20.159><c> can</c><00:03:20.360><c> basically</c><00:03:20.760><c> regardless</c><00:03:21.120><c> if</c><00:03:21.200><c> you're</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
you can basically regardless if you're
 

00:03:21.360 --> 00:03:23.869 align:start position:0%
you can basically regardless if you're
creating<00:03:21.920><c> agents</c><00:03:22.560><c> or</c><00:03:22.760><c> search</c><00:03:23.159><c> applications</c>

00:03:23.869 --> 00:03:23.879 align:start position:0%
creating agents or search applications
 

00:03:23.879 --> 00:03:25.910 align:start position:0%
creating agents or search applications
or<00:03:24.120><c> those</c><00:03:24.280><c> kind</c><00:03:24.400><c> of</c><00:03:24.560><c> things</c><00:03:25.480><c> you</c><00:03:25.640><c> can</c>

00:03:25.910 --> 00:03:25.920 align:start position:0%
or those kind of things you can
 

00:03:25.920 --> 00:03:28.750 align:start position:0%
or those kind of things you can
basically<00:03:26.480><c> inject</c><00:03:27.319><c> the</c><00:03:27.560><c> data</c><00:03:28.000><c> into</c><00:03:28.599><c> the</c>

00:03:28.750 --> 00:03:28.760 align:start position:0%
basically inject the data into the
 

00:03:28.760 --> 00:03:30.550 align:start position:0%
basically inject the data into the
models

00:03:30.550 --> 00:03:30.560 align:start position:0%
models
 

00:03:30.560 --> 00:03:31.990 align:start position:0%
models
and<00:03:30.680><c> that</c><00:03:30.799><c> is</c><00:03:30.920><c> one</c><00:03:31.040><c> of</c><00:03:31.159><c> our</c><00:03:31.319><c> Focus</c><00:03:31.599><c> point</c><00:03:31.840><c> right</c>

00:03:31.990 --> 00:03:32.000 align:start position:0%
and that is one of our Focus point right
 

00:03:32.000 --> 00:03:34.309 align:start position:0%
and that is one of our Focus point right
so<00:03:32.480><c> we</c><00:03:32.640><c> want</c><00:03:32.920><c> people</c><00:03:33.599><c> uh</c><00:03:33.720><c> to</c><00:03:33.840><c> be</c><00:03:33.959><c> able</c><00:03:34.159><c> to</c>

00:03:34.309 --> 00:03:34.319 align:start position:0%
so we want people uh to be able to
 

00:03:34.319 --> 00:03:37.270 align:start position:0%
so we want people uh to be able to
create<00:03:34.720><c> critical</c><00:03:35.360><c> AI</c><00:03:35.720><c> native</c><00:03:36.360><c> applications</c>

00:03:37.270 --> 00:03:37.280 align:start position:0%
create critical AI native applications
 

00:03:37.280 --> 00:03:40.710 align:start position:0%
create critical AI native applications
right<00:03:37.439><c> so</c><00:03:38.400><c> that</c><00:03:38.519><c> means</c><00:03:39.080><c> production</c><00:03:39.680><c> cases</c><00:03:40.319><c> end</c>

00:03:40.710 --> 00:03:40.720 align:start position:0%
right so that means production cases end
 

00:03:40.720 --> 00:03:44.390 align:start position:0%
right so that means production cases end
to<00:03:41.319><c> end</c><00:03:42.319><c> um</c><00:03:42.519><c> so</c><00:03:42.720><c> quick</c><00:03:42.959><c> recap</c><00:03:43.599><c> right</c><00:03:43.879><c> so</c><00:03:44.200><c> we're</c>

00:03:44.390 --> 00:03:44.400 align:start position:0%
to end um so quick recap right so we're
 

00:03:44.400 --> 00:03:46.630 align:start position:0%
to end um so quick recap right so we're
talking<00:03:44.720><c> about</c><00:03:45.360><c> weaving</c><00:03:45.879><c> the</c><00:03:46.040><c> database</c><00:03:46.519><c> and</c>

00:03:46.630 --> 00:03:46.640 align:start position:0%
talking about weaving the database and
 

00:03:46.640 --> 00:03:48.869 align:start position:0%
talking about weaving the database and
the<00:03:46.720><c> models</c><00:03:47.200><c> together</c><00:03:48.040><c> the</c><00:03:48.280><c> open</c><00:03:48.599><c> source</c>

00:03:48.869 --> 00:03:48.879 align:start position:0%
the models together the open source
 

00:03:48.879 --> 00:03:50.830 align:start position:0%
the models together the open source
nature<00:03:49.280><c> allows</c><00:03:49.599><c> us</c><00:03:49.760><c> to</c><00:03:50.000><c> create</c><00:03:50.280><c> a</c><00:03:50.439><c> community</c>

00:03:50.830 --> 00:03:50.840 align:start position:0%
nature allows us to create a community
 

00:03:50.840 --> 00:03:53.069 align:start position:0%
nature allows us to create a community
of<00:03:51.000><c> customers</c><00:03:51.400><c> and</c><00:03:51.560><c> users</c><00:03:52.000><c> that</c><00:03:52.159><c> can</c><00:03:52.760><c> range</c>

00:03:53.069 --> 00:03:53.079 align:start position:0%
of customers and users that can range
 

00:03:53.079 --> 00:03:55.149 align:start position:0%
of customers and users that can range
from<00:03:53.640><c> open</c><00:03:53.959><c> source</c><00:03:54.200><c> to</c><00:03:54.360><c> bring</c><00:03:54.519><c> your</c><00:03:54.640><c> own</c><00:03:54.879><c> cloud</c>

00:03:55.149 --> 00:03:55.159 align:start position:0%
from open source to bring your own cloud
 

00:03:55.159 --> 00:03:57.789 align:start position:0%
from open source to bring your own cloud
to<00:03:55.400><c> SAS</c><00:03:55.760><c> serverless</c><00:03:56.319><c> what</c><00:03:56.439><c> have</c><00:03:56.599><c> you</c><00:03:57.400><c> focusing</c>

00:03:57.789 --> 00:03:57.799 align:start position:0%
to SAS serverless what have you focusing
 

00:03:57.799 --> 00:04:01.670 align:start position:0%
to SAS serverless what have you focusing
on<00:03:58.000><c> secure</c><00:03:58.560><c> explainable</c><00:03:59.120><c> stateful</c><00:04:00.000><c> AI</c>

00:04:01.670 --> 00:04:01.680 align:start position:0%
on secure explainable stateful AI
 

00:04:01.680 --> 00:04:04.509 align:start position:0%
on secure explainable stateful AI
applications<00:04:02.680><c> so</c><00:04:02.840><c> a</c><00:04:02.959><c> little</c><00:04:03.120><c> bit</c><00:04:03.239><c> about</c><00:04:03.519><c> weate</c>

00:04:04.509 --> 00:04:04.519 align:start position:0%
applications so a little bit about weate
 

00:04:04.519 --> 00:04:07.509 align:start position:0%
applications so a little bit about weate
so<00:04:04.840><c> weate</c><00:04:05.239><c> is</c><00:04:05.319><c> a</c><00:04:05.480><c> database</c><00:04:06.280><c> so</c><00:04:07.000><c> um</c><00:04:07.239><c> from</c>

00:04:07.509 --> 00:04:07.519 align:start position:0%
so weate is a database so um from
 

00:04:07.519 --> 00:04:10.069 align:start position:0%
so weate is a database so um from
starting<00:04:07.879><c> point</c><00:04:08.040><c> you</c><00:04:08.159><c> can</c><00:04:08.760><c> import</c><00:04:09.239><c> any</c><00:04:09.640><c> type</c>

00:04:10.069 --> 00:04:10.079 align:start position:0%
starting point you can import any type
 

00:04:10.079 --> 00:04:12.589 align:start position:0%
starting point you can import any type
of<00:04:10.319><c> data</c><00:04:10.799><c> right</c><00:04:11.040><c> ranging</c><00:04:11.439><c> from</c><00:04:11.720><c> text</c><00:04:12.159><c> audio</c>

00:04:12.589 --> 00:04:12.599 align:start position:0%
of data right ranging from text audio
 

00:04:12.599 --> 00:04:14.390 align:start position:0%
of data right ranging from text audio
images<00:04:13.120><c> whatever</c><00:04:13.439><c> you</c><00:04:13.640><c> have</c><00:04:13.920><c> you</c><00:04:14.000><c> can</c><00:04:14.159><c> store</c>

00:04:14.390 --> 00:04:14.400 align:start position:0%
images whatever you have you can store
 

00:04:14.400 --> 00:04:16.390 align:start position:0%
images whatever you have you can store
it<00:04:14.480><c> in</c><00:04:14.599><c> weate</c><00:04:15.079><c> as</c><00:04:15.159><c> you</c><00:04:15.239><c> can</c><00:04:15.360><c> see</c><00:04:15.480><c> in</c><00:04:15.560><c> the</c><00:04:15.680><c> middle</c>

00:04:16.390 --> 00:04:16.400 align:start position:0%
it in weate as you can see in the middle
 

00:04:16.400 --> 00:04:20.110 align:start position:0%
it in weate as you can see in the middle
is<00:04:16.639><c> represented</c><00:04:17.280><c> as</c><00:04:17.440><c> a</c><00:04:18.000><c> um</c><00:04:18.479><c> Json</c><00:04:19.000><c> object</c><00:04:20.000><c> we</c>

00:04:20.110 --> 00:04:20.120 align:start position:0%
is represented as a um Json object we
 

00:04:20.120 --> 00:04:22.390 align:start position:0%
is represented as a um Json object we
have<00:04:20.280><c> these</c><00:04:20.600><c> optional</c><00:04:21.440><c> factorization</c>

00:04:22.390 --> 00:04:22.400 align:start position:0%
have these optional factorization
 

00:04:22.400 --> 00:04:24.310 align:start position:0%
have these optional factorization
modules<00:04:23.040><c> you</c><00:04:23.199><c> can</c><00:04:23.400><c> use</c><00:04:23.639><c> them</c><00:04:23.800><c> you</c><00:04:23.919><c> don't</c><00:04:24.160><c> have</c>

00:04:24.310 --> 00:04:24.320 align:start position:0%
modules you can use them you don't have
 

00:04:24.320 --> 00:04:26.870 align:start position:0%
modules you can use them you don't have
to<00:04:24.479><c> use</c><00:04:24.680><c> them</c><00:04:25.600><c> um</c><00:04:25.720><c> so</c><00:04:25.880><c> they</c><00:04:26.040><c> integrate</c><00:04:26.680><c> with</c>

00:04:26.870 --> 00:04:26.880 align:start position:0%
to use them um so they integrate with
 

00:04:26.880 --> 00:04:29.029 align:start position:0%
to use them um so they integrate with
the<00:04:27.479><c> um</c><00:04:27.680><c> with</c><00:04:27.800><c> the</c><00:04:27.880><c> models</c><00:04:28.160><c> from</c><00:04:28.360><c> hugging</c><00:04:28.759><c> fish</c>

00:04:29.029 --> 00:04:29.039 align:start position:0%
the um with the models from hugging fish
 

00:04:29.039 --> 00:04:30.950 align:start position:0%
the um with the models from hugging fish
from<00:04:29.199><c> Google</c><00:04:29.639><c> opening</c><00:04:30.000><c> I</c><00:04:30.240><c> call</c><00:04:30.400><c> here</c><00:04:30.639><c> whatever</c>

00:04:30.950 --> 00:04:30.960 align:start position:0%
from Google opening I call here whatever
 

00:04:30.960 --> 00:04:33.029 align:start position:0%
from Google opening I call here whatever
you<00:04:31.039><c> want</c><00:04:31.160><c> to</c><00:04:31.320><c> use</c><00:04:31.680><c> you</c><00:04:31.800><c> can</c><00:04:32.080><c> integrate</c><00:04:32.639><c> with</c>

00:04:33.029 --> 00:04:33.039 align:start position:0%
you want to use you can integrate with
 

00:04:33.039 --> 00:04:35.270 align:start position:0%
you want to use you can integrate with
uh<00:04:33.160><c> weate</c><00:04:33.720><c> if</c><00:04:33.840><c> you</c><00:04:33.960><c> want</c><00:04:34.120><c> to</c><00:04:34.759><c> you</c><00:04:34.840><c> can</c><00:04:35.000><c> also</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
uh weate if you want to you can also
 

00:04:35.280 --> 00:04:38.270 align:start position:0%
uh weate if you want to you can also
bring<00:04:35.560><c> your</c><00:04:36.080><c> embedding</c><00:04:36.600><c> yourself</c><00:04:37.160><c> if</c><00:04:37.360><c> that's</c>

00:04:38.270 --> 00:04:38.280 align:start position:0%
bring your embedding yourself if that's
 

00:04:38.280 --> 00:04:39.990 align:start position:0%
bring your embedding yourself if that's
what<00:04:38.400><c> you</c><00:04:38.520><c> want</c><00:04:38.800><c> so</c><00:04:39.080><c> really</c><00:04:39.280><c> depends</c><00:04:39.560><c> on</c><00:04:39.720><c> use</c>

00:04:39.990 --> 00:04:40.000 align:start position:0%
what you want so really depends on use
 

00:04:40.000 --> 00:04:44.749 align:start position:0%
what you want so really depends on use
case<00:04:40.199><c> and</c><00:04:40.320><c> we</c><00:04:40.440><c> have</c><00:04:40.840><c> these</c><00:04:41.840><c> um</c><00:04:42.800><c> uh</c><00:04:43.479><c> um</c><00:04:44.120><c> factor</c>

00:04:44.749 --> 00:04:44.759 align:start position:0%
case and we have these um uh um factor
 

00:04:44.759 --> 00:04:47.029 align:start position:0%
case and we have these um uh um factor
factorization<00:04:45.759><c> modules</c><00:04:46.360><c> and</c><00:04:46.520><c> we</c><00:04:46.639><c> have</c><00:04:46.800><c> these</c>

00:04:47.029 --> 00:04:47.039 align:start position:0%
factorization modules and we have these
 

00:04:47.039 --> 00:04:48.990 align:start position:0%
factorization modules and we have these
generative<00:04:47.560><c> modules</c><00:04:48.400><c> we</c><00:04:48.520><c> also</c><00:04:48.680><c> have</c><00:04:48.800><c> reer</c>

00:04:48.990 --> 00:04:49.000 align:start position:0%
generative modules we also have reer
 

00:04:49.000 --> 00:04:50.870 align:start position:0%
generative modules we also have reer
rankers<00:04:49.360><c> and</c><00:04:49.560><c> those</c><00:04:49.720><c> kind</c><00:04:49.840><c> of</c><00:04:50.000><c> things</c><00:04:50.560><c> so</c><00:04:50.759><c> if</c>

00:04:50.870 --> 00:04:50.880 align:start position:0%
rankers and those kind of things so if
 

00:04:50.880 --> 00:04:52.510 align:start position:0%
rankers and those kind of things so if
you<00:04:51.039><c> want</c><00:04:51.160><c> to</c><00:04:51.280><c> build</c><00:04:51.520><c> that</c><00:04:51.680><c> end</c><00:04:51.919><c> to</c><00:04:52.000><c> end</c><00:04:52.320><c> and</c>

00:04:52.510 --> 00:04:52.520 align:start position:0%
you want to build that end to end and
 

00:04:52.520 --> 00:04:54.390 align:start position:0%
you want to build that end to end and
you<00:04:52.639><c> don't</c><00:04:52.800><c> want</c><00:04:52.919><c> to</c><00:04:53.120><c> care</c><00:04:53.320><c> too</c><00:04:53.520><c> much</c><00:04:53.840><c> about</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
you don't want to care too much about
 

00:04:54.400 --> 00:04:56.270 align:start position:0%
you don't want to care too much about
where<00:04:54.639><c> the</c><00:04:54.759><c> embedding</c><00:04:55.240><c> is</c><00:04:55.400><c> coming</c><00:04:55.720><c> from</c><00:04:56.039><c> or</c>

00:04:56.270 --> 00:04:56.280 align:start position:0%
where the embedding is coming from or
 

00:04:56.280 --> 00:04:58.150 align:start position:0%
where the embedding is coming from or
how<00:04:56.440><c> you're</c><00:04:56.919><c> integrating</c><00:04:57.440><c> with</c><00:04:57.520><c> the</c><00:04:57.639><c> models</c>

00:04:58.150 --> 00:04:58.160 align:start position:0%
how you're integrating with the models
 

00:04:58.160 --> 00:04:59.990 align:start position:0%
how you're integrating with the models
you<00:04:58.280><c> can</c><00:04:58.479><c> optionally</c><00:04:58.919><c> do</c><00:04:59.080><c> that</c><00:04:59.240><c> in</c><00:04:59.360><c> we</c><00:04:59.720><c> it</c><00:04:59.880><c> as</c>

00:04:59.990 --> 00:05:00.000 align:start position:0%
you can optionally do that in we it as
 

00:05:00.000 --> 00:05:02.350 align:start position:0%
you can optionally do that in we it as
well<00:05:00.840><c> um</c><00:05:01.080><c> so</c><00:05:01.280><c> there's</c><00:05:01.560><c> one</c><00:05:01.880><c> place</c><00:05:02.120><c> where</c><00:05:02.240><c> you</c>

00:05:02.350 --> 00:05:02.360 align:start position:0%
well um so there's one place where you
 

00:05:02.360 --> 00:05:04.350 align:start position:0%
well um so there's one place where you
store<00:05:02.680><c> that</c><00:05:02.880><c> so</c><00:05:03.039><c> you</c><00:05:03.160><c> don't</c><00:05:03.400><c> need</c><00:05:03.520><c> a</c><00:05:03.759><c> secondary</c>

00:05:04.350 --> 00:05:04.360 align:start position:0%
store that so you don't need a secondary
 

00:05:04.360 --> 00:05:06.469 align:start position:0%
store that so you don't need a secondary
database<00:05:05.199><c> you</c><00:05:05.320><c> can</c><00:05:05.479><c> just</c><00:05:05.639><c> have</c><00:05:05.800><c> one</c><00:05:06.039><c> database</c>

00:05:06.469 --> 00:05:06.479 align:start position:0%
database you can just have one database
 

00:05:06.479 --> 00:05:08.670 align:start position:0%
database you can just have one database
to<00:05:06.600><c> have</c><00:05:06.919><c> ENT</c><00:05:07.199><c> your</c><00:05:07.400><c> data</c><00:05:07.840><c> and</c><00:05:08.000><c> your</c><00:05:08.199><c> effect</c><00:05:08.479><c> R</c>

00:05:08.670 --> 00:05:08.680 align:start position:0%
to have ENT your data and your effect R
 

00:05:08.680 --> 00:05:11.070 align:start position:0%
to have ENT your data and your effect R
Bings<00:05:09.520><c> and</c><00:05:09.639><c> the</c><00:05:09.800><c> majority</c><00:05:10.160><c> of</c><00:05:10.360><c> applications</c>

00:05:11.070 --> 00:05:11.080 align:start position:0%
Bings and the majority of applications
 

00:05:11.080 --> 00:05:14.110 align:start position:0%
Bings and the majority of applications
are<00:05:11.320><c> around</c><00:05:11.639><c> search</c><00:05:12.440><c> generation</c><00:05:13.240><c> and</c>

00:05:14.110 --> 00:05:14.120 align:start position:0%
are around search generation and
 

00:05:14.120 --> 00:05:16.270 align:start position:0%
are around search generation and
recommendations<00:05:15.120><c> so</c><00:05:15.560><c> again</c><00:05:15.840><c> quickly</c><00:05:16.120><c> to</c>

00:05:16.270 --> 00:05:16.280 align:start position:0%
recommendations so again quickly to
 

00:05:16.280 --> 00:05:19.189 align:start position:0%
recommendations so again quickly to
recap<00:05:16.840><c> so</c><00:05:16.960><c> we</c><00:05:17.080><c> have</c><00:05:17.160><c> serverless</c><00:05:17.840><c> SAS</c><00:05:18.840><c> and</c><00:05:19.039><c> we</c>

00:05:19.189 --> 00:05:19.199 align:start position:0%
recap so we have serverless SAS and we
 

00:05:19.199 --> 00:05:20.909 align:start position:0%
recap so we have serverless SAS and we
have<00:05:19.440><c> bring</c><00:05:19.759><c> your</c><00:05:19.919><c> on</c>

00:05:20.909 --> 00:05:20.919 align:start position:0%
have bring your on
 

00:05:20.919 --> 00:05:23.749 align:start position:0%
have bring your on
cloud<00:05:21.919><c> um</c><00:05:22.080><c> that</c><00:05:22.160><c> brings</c><00:05:22.400><c> me</c><00:05:22.479><c> to</c><00:05:22.600><c> the</c><00:05:22.759><c> demo</c><00:05:23.520><c> so</c><00:05:23.639><c> I</c>

00:05:23.749 --> 00:05:23.759 align:start position:0%
cloud um that brings me to the demo so I
 

00:05:23.759 --> 00:05:26.110 align:start position:0%
cloud um that brings me to the demo so I
quickly<00:05:24.039><c> want</c><00:05:24.199><c> to</c><00:05:24.400><c> give</c><00:05:24.639><c> a</c><00:05:24.880><c> demo</c><00:05:25.639><c> um</c><00:05:25.800><c> of</c><00:05:25.919><c> how</c>

00:05:26.110 --> 00:05:26.120 align:start position:0%
quickly want to give a demo um of how
 

00:05:26.120 --> 00:05:28.990 align:start position:0%
quickly want to give a demo um of how
weate<00:05:26.600><c> Works</c><00:05:26.919><c> what</c><00:05:27.039><c> I'm</c><00:05:27.240><c> about</c><00:05:27.479><c> to</c><00:05:27.639><c> do</c><00:05:28.120><c> is</c><00:05:28.440><c> the</c>

00:05:28.990 --> 00:05:29.000 align:start position:0%
weate Works what I'm about to do is the
 

00:05:29.000 --> 00:05:31.990 align:start position:0%
weate Works what I'm about to do is the
um<00:05:29.919><c> I'm</c><00:05:30.680><c> uh</c><00:05:30.840><c> showing</c><00:05:31.199><c> something</c><00:05:31.520><c> based</c><00:05:31.759><c> on</c><00:05:31.880><c> an</c>

00:05:31.990 --> 00:05:32.000 align:start position:0%
um I'm uh showing something based on an
 

00:05:32.000 --> 00:05:34.350 align:start position:0%
um I'm uh showing something based on an
Airbnb<00:05:32.639><c> data</c><00:05:32.919><c> set</c><00:05:33.240><c> this</c><00:05:33.400><c> data</c><00:05:33.639><c> set</c><00:05:33.840><c> is</c><00:05:34.000><c> also</c>

00:05:34.350 --> 00:05:34.360 align:start position:0%
Airbnb data set this data set is also
 

00:05:34.360 --> 00:05:36.189 align:start position:0%
Airbnb data set this data set is also
open<00:05:34.639><c> source</c><00:05:35.000><c> available</c><00:05:35.400><c> on</c><00:05:35.520><c> our</c><00:05:35.720><c> website</c><00:05:36.080><c> so</c>

00:05:36.189 --> 00:05:36.199 align:start position:0%
open source available on our website so
 

00:05:36.199 --> 00:05:37.270 align:start position:0%
open source available on our website so
if<00:05:36.280><c> you</c><00:05:36.360><c> want</c><00:05:36.440><c> to</c><00:05:36.600><c> play</c><00:05:36.800><c> around</c><00:05:37.039><c> with</c><00:05:37.160><c> it</c>

00:05:37.270 --> 00:05:37.280 align:start position:0%
if you want to play around with it
 

00:05:37.280 --> 00:05:40.830 align:start position:0%
if you want to play around with it
yourself<00:05:38.280><c> you</c><00:05:38.440><c> can</c><00:05:38.560><c> do</c><00:05:38.720><c> that</c><00:05:38.960><c> too</c><00:05:39.960><c> and</c><00:05:40.680><c> the</c>

00:05:40.830 --> 00:05:40.840 align:start position:0%
yourself you can do that too and the
 

00:05:40.840 --> 00:05:42.390 align:start position:0%
yourself you can do that too and the
language<00:05:41.199><c> that</c><00:05:41.319><c> I'm</c><00:05:41.440><c> going</c><00:05:41.560><c> to</c><00:05:41.639><c> show</c><00:05:41.840><c> it</c><00:05:42.000><c> in</c><00:05:42.240><c> is</c>

00:05:42.390 --> 00:05:42.400 align:start position:0%
language that I'm going to show it in is
 

00:05:42.400 --> 00:05:45.830 align:start position:0%
language that I'm going to show it in is
going<00:05:42.600><c> to</c><00:05:42.759><c> be</c><00:05:43.360><c> um</c><00:05:43.759><c> graphql</c><00:05:44.759><c> you</c><00:05:44.880><c> can</c><00:05:45.039><c> use</c><00:05:45.319><c> weate</c>

00:05:45.830 --> 00:05:45.840 align:start position:0%
going to be um graphql you can use weate
 

00:05:45.840 --> 00:05:47.710 align:start position:0%
going to be um graphql you can use weate
with<00:05:46.080><c> python</c><00:05:46.680><c> with</c><00:05:46.840><c> JavaScript</c><00:05:47.440><c> and</c><00:05:47.560><c> all</c>

00:05:47.710 --> 00:05:47.720 align:start position:0%
with python with JavaScript and all
 

00:05:47.720 --> 00:05:49.990 align:start position:0%
with python with JavaScript and all
these<00:05:47.880><c> other</c><00:05:48.160><c> clients</c><00:05:49.120><c> but</c><00:05:49.360><c> this</c><00:05:49.479><c> is</c><00:05:49.680><c> like</c><00:05:49.840><c> a</c>

00:05:49.990 --> 00:05:50.000 align:start position:0%
these other clients but this is like a
 

00:05:50.000 --> 00:05:51.590 align:start position:0%
these other clients but this is like a
generic<00:05:50.440><c> language</c><00:05:50.840><c> how</c><00:05:50.960><c> you</c><00:05:51.039><c> can</c><00:05:51.199><c> interact</c>

00:05:51.590 --> 00:05:51.600 align:start position:0%
generic language how you can interact
 

00:05:51.600 --> 00:05:53.350 align:start position:0%
generic language how you can interact
with<00:05:51.759><c> the</c><00:05:51.960><c> with</c><00:05:52.080><c> the</c><00:05:52.240><c> database</c><00:05:53.000><c> and</c><00:05:53.160><c> for</c>

00:05:53.350 --> 00:05:53.360 align:start position:0%
with the with the database and for
 

00:05:53.360 --> 00:05:56.309 align:start position:0%
with the with the database and for
everybody<00:05:53.880><c> you</c><00:05:54.000><c> know</c><00:05:54.639><c> that's</c><00:05:54.919><c> easy</c><00:05:55.400><c> to</c><00:05:55.759><c> read</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
everybody you know that's easy to read
 

00:05:56.319 --> 00:05:58.710 align:start position:0%
everybody you know that's easy to read
so<00:05:56.880><c> what</c><00:05:57.000><c> we're</c><00:05:57.199><c> looking</c><00:05:57.400><c> at</c><00:05:57.680><c> right</c><00:05:57.840><c> now</c><00:05:58.240><c> is</c><00:05:58.479><c> a</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
so what we're looking at right now is a
 

00:05:58.720 --> 00:06:01.469 align:start position:0%
so what we're looking at right now is a
demo<00:05:59.039><c> instance</c><00:05:59.680><c> that</c><00:05:59.840><c> we</c><00:06:00.039><c> have</c><00:06:00.759><c> and</c><00:06:00.919><c> this</c><00:06:01.120><c> demo</c>

00:06:01.469 --> 00:06:01.479 align:start position:0%
demo instance that we have and this demo
 

00:06:01.479 --> 00:06:04.070 align:start position:0%
demo instance that we have and this demo
instance<00:06:02.000><c> has</c><00:06:02.280><c> these</c><00:06:02.800><c> Airbnb</c><00:06:03.479><c> listings</c><00:06:03.919><c> in</c>

00:06:04.070 --> 00:06:04.080 align:start position:0%
instance has these Airbnb listings in
 

00:06:04.080 --> 00:06:05.510 align:start position:0%
instance has these Airbnb listings in
them<00:06:04.280><c> so</c><00:06:04.479><c> one</c><00:06:04.600><c> of</c><00:06:04.720><c> the</c><00:06:04.840><c> things</c><00:06:05.000><c> that</c><00:06:05.120><c> we</c><00:06:05.199><c> can</c><00:06:05.319><c> do</c>

00:06:05.510 --> 00:06:05.520 align:start position:0%
them so one of the things that we can do
 

00:06:05.520 --> 00:06:10.110 align:start position:0%
them so one of the things that we can do
we<00:06:05.639><c> can</c><00:06:05.800><c> say</c><00:06:06.080><c> oh</c><00:06:06.919><c> say</c><00:06:07.280><c> get</c><00:06:08.280><c> we</c><00:06:08.400><c> can</c><00:06:08.560><c> say</c><00:06:09.120><c> listing</c>

00:06:10.110 --> 00:06:10.120 align:start position:0%
we can say oh say get we can say listing
 

00:06:10.120 --> 00:06:11.550 align:start position:0%
we can say oh say get we can say listing
and<00:06:10.199><c> then</c><00:06:10.360><c> we</c><00:06:10.479><c> can</c><00:06:10.639><c> list</c><00:06:10.919><c> all</c><00:06:11.080><c> the</c><00:06:11.160><c> listings</c>

00:06:11.550 --> 00:06:11.560 align:start position:0%
and then we can list all the listings
 

00:06:11.560 --> 00:06:13.469 align:start position:0%
and then we can list all the listings
that<00:06:11.639><c> we</c><00:06:11.759><c> have</c><00:06:11.880><c> in</c><00:06:11.960><c> the</c><00:06:12.080><c> database</c><00:06:13.080><c> so</c><00:06:13.240><c> as</c><00:06:13.360><c> you</c>

00:06:13.469 --> 00:06:13.479 align:start position:0%
that we have in the database so as you
 

00:06:13.479 --> 00:06:16.990 align:start position:0%
that we have in the database so as you
can<00:06:13.639><c> see</c><00:06:13.919><c> here</c><00:06:14.240><c> we</c><00:06:14.479><c> have</c><00:06:15.199><c> here</c><00:06:15.840><c> um</c><00:06:16.360><c> uh</c><00:06:16.720><c> host</c>

00:06:16.990 --> 00:06:17.000 align:start position:0%
can see here we have here um uh host
 

00:06:17.000 --> 00:06:19.469 align:start position:0%
can see here we have here um uh host
name<00:06:17.520><c> uh</c><00:06:17.639><c> John</c><00:06:18.360><c> clean</c><00:06:18.599><c> and</c><00:06:18.720><c> qu</c><00:06:19.000><c> apartment</c>

00:06:19.469 --> 00:06:19.479 align:start position:0%
name uh John clean and qu apartment
 

00:06:19.479 --> 00:06:22.589 align:start position:0%
name uh John clean and qu apartment
Kingston<00:06:20.199><c> the</c><00:06:20.360><c> prize</c><00:06:20.599><c> and</c><00:06:20.759><c> the</c><00:06:20.840><c> room</c><00:06:21.080><c> type</c><00:06:21.599><c> now</c>

00:06:22.589 --> 00:06:22.599 align:start position:0%
Kingston the prize and the room type now
 

00:06:22.599 --> 00:06:24.150 align:start position:0%
Kingston the prize and the room type now
what<00:06:22.720><c> you</c><00:06:22.840><c> might</c><00:06:23.080><c> notice</c><00:06:23.680><c> with</c><00:06:23.840><c> all</c><00:06:24.000><c> these</c>

00:06:24.150 --> 00:06:24.160 align:start position:0%
what you might notice with all these
 

00:06:24.160 --> 00:06:26.029 align:start position:0%
what you might notice with all these
listings<00:06:24.720><c> is</c><00:06:24.840><c> that</c><00:06:25.000><c> all</c><00:06:25.160><c> these</c><00:06:25.319><c> indiv</c>

00:06:26.029 --> 00:06:26.039 align:start position:0%
listings is that all these indiv
 

00:06:26.039 --> 00:06:27.830 align:start position:0%
listings is that all these indiv
individual<00:06:26.520><c> listings</c><00:06:27.000><c> do</c><00:06:27.199><c> not</c><00:06:27.520><c> have</c><00:06:27.639><c> a</c>

00:06:27.830 --> 00:06:27.840 align:start position:0%
individual listings do not have a
 

00:06:27.840 --> 00:06:29.749 align:start position:0%
individual listings do not have a
description<00:06:28.360><c> so</c><00:06:28.479><c> the</c><00:06:28.639><c> description</c><00:06:29.039><c> is</c><00:06:29.199><c> not</c>

00:06:29.749 --> 00:06:29.759 align:start position:0%
description so the description is not
 

00:06:29.759 --> 00:06:31.909 align:start position:0%
description so the description is not
everywhere<00:06:30.759><c> and</c><00:06:30.880><c> the</c><00:06:31.039><c> reason</c><00:06:31.360><c> it's</c><00:06:31.520><c> null</c>

00:06:31.909 --> 00:06:31.919 align:start position:0%
everywhere and the reason it's null
 

00:06:31.919 --> 00:06:35.029 align:start position:0%
everywhere and the reason it's null
everywhere<00:06:32.759><c> is</c><00:06:33.240><c> because</c><00:06:33.639><c> we</c><00:06:33.919><c> simply</c><00:06:34.360><c> do</c><00:06:34.560><c> not</c>

00:06:35.029 --> 00:06:35.039 align:start position:0%
everywhere is because we simply do not
 

00:06:35.039 --> 00:06:36.909 align:start position:0%
everywhere is because we simply do not
have<00:06:35.400><c> that</c><00:06:35.599><c> information</c><00:06:36.000><c> so</c><00:06:36.199><c> the</c><00:06:36.400><c> original</c>

00:06:36.909 --> 00:06:36.919 align:start position:0%
have that information so the original
 

00:06:36.919 --> 00:06:39.230 align:start position:0%
have that information so the original
data<00:06:37.199><c> set</c><00:06:37.520><c> does</c><00:06:37.720><c> not</c><00:06:38.160><c> include</c><00:06:38.720><c> this</c>

00:06:39.230 --> 00:06:39.240 align:start position:0%
data set does not include this
 

00:06:39.240 --> 00:06:41.270 align:start position:0%
data set does not include this
information<00:06:40.240><c> so</c><00:06:40.479><c> one</c><00:06:40.680><c> thing</c><00:06:40.840><c> that</c><00:06:40.960><c> you</c><00:06:41.039><c> can</c><00:06:41.160><c> do</c>

00:06:41.270 --> 00:06:41.280 align:start position:0%
information so one thing that you can do
 

00:06:41.280 --> 00:06:42.390 align:start position:0%
information so one thing that you can do
in

00:06:42.390 --> 00:06:42.400 align:start position:0%
in
 

00:06:42.400 --> 00:06:45.230 align:start position:0%
in
weate<00:06:43.400><c> so</c><00:06:43.720><c> limit</c><00:06:44.199><c> let</c><00:06:44.319><c> me</c><00:06:44.479><c> limit</c><00:06:44.800><c> this</c><00:06:44.919><c> to</c><00:06:45.080><c> the</c>

00:06:45.230 --> 00:06:45.240 align:start position:0%
weate so limit let me limit this to the
 

00:06:45.240 --> 00:06:47.670 align:start position:0%
weate so limit let me limit this to the
first<00:06:46.000><c> result</c><00:06:46.800><c> so</c><00:06:46.960><c> here</c><00:06:47.080><c> you</c><00:06:47.160><c> see</c><00:06:47.319><c> the</c><00:06:47.400><c> first</c>

00:06:47.670 --> 00:06:47.680 align:start position:0%
first result so here you see the first
 

00:06:47.680 --> 00:06:50.029 align:start position:0%
first result so here you see the first
result<00:06:48.599><c> one</c><00:06:48.800><c> thing</c><00:06:48.960><c> that</c><00:06:49.080><c> you</c><00:06:49.199><c> can</c><00:06:49.639><c> uh</c><00:06:49.759><c> do</c><00:06:49.880><c> in</c>

00:06:50.029 --> 00:06:50.039 align:start position:0%
result one thing that you can uh do in
 

00:06:50.039 --> 00:06:51.950 align:start position:0%
result one thing that you can uh do in
we<00:06:50.400><c> is</c><00:06:50.479><c> that</c><00:06:50.599><c> you</c><00:06:50.680><c> can</c><00:06:50.919><c> pipe</c><00:06:51.240><c> the</c><00:06:51.440><c> result</c>

00:06:51.950 --> 00:06:51.960 align:start position:0%
we is that you can pipe the result
 

00:06:51.960 --> 00:06:54.510 align:start position:0%
we is that you can pipe the result
through<00:06:52.440><c> a</c><00:06:52.680><c> generative</c><00:06:53.680><c> uh</c><00:06:53.800><c> module</c><00:06:54.199><c> so</c><00:06:54.440><c> you</c>

00:06:54.510 --> 00:06:54.520 align:start position:0%
through a generative uh module so you
 

00:06:54.520 --> 00:06:55.830 align:start position:0%
through a generative uh module so you
can<00:06:54.720><c> say</c>

00:06:55.830 --> 00:06:55.840 align:start position:0%
can say
 

00:06:55.840 --> 00:06:57.390 align:start position:0%
can say
additional

00:06:57.390 --> 00:06:57.400 align:start position:0%
additional
 

00:06:57.400 --> 00:06:59.070 align:start position:0%
additional
generate<00:06:58.400><c> and</c><00:06:58.479><c> then</c><00:06:58.599><c> we're</c><00:06:58.720><c> going</c><00:06:58.840><c> to</c><00:06:58.919><c> say</c>

00:06:59.070 --> 00:06:59.080 align:start position:0%
generate and then we're going to say
 

00:06:59.080 --> 00:07:01.029 align:start position:0%
generate and then we're going to say
single

00:07:01.029 --> 00:07:01.039 align:start position:0%
single
 

00:07:01.039 --> 00:07:04.350 align:start position:0%
single
result<00:07:02.039><c> and</c><00:07:02.599><c> then</c><00:07:02.720><c> we</c><00:07:02.840><c> say</c><00:07:03.039><c> single</c><00:07:03.400><c> result</c><00:07:04.160><c> we</c>

00:07:04.350 --> 00:07:04.360 align:start position:0%
result and then we say single result we
 

00:07:04.360 --> 00:07:06.990 align:start position:0%
result and then we say single result we
give<00:07:04.520><c> that</c><00:07:04.639><c> a</c><00:07:05.000><c> prompt</c><00:07:06.000><c> and</c><00:07:06.160><c> then</c><00:07:06.319><c> we</c><00:07:06.479><c> can</c><00:07:06.840><c> give</c>

00:07:06.990 --> 00:07:07.000 align:start position:0%
give that a prompt and then we can give
 

00:07:07.000 --> 00:07:10.070 align:start position:0%
give that a prompt and then we can give
a<00:07:07.240><c> prompt</c><00:07:08.240><c> how</c><00:07:08.400><c> we</c><00:07:08.520><c> want</c><00:07:08.639><c> to</c><00:07:08.960><c> pipe</c><00:07:09.479><c> these</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
a prompt how we want to pipe these
 

00:07:10.080 --> 00:07:13.790 align:start position:0%
a prompt how we want to pipe these
results<00:07:11.080><c> to</c><00:07:11.720><c> a</c><00:07:12.199><c> a</c><00:07:12.360><c> prompt</c><00:07:12.759><c> so</c><00:07:13.280><c> I</c><00:07:13.400><c> prepared</c>

00:07:13.790 --> 00:07:13.800 align:start position:0%
results to a a prompt so I prepared
 

00:07:13.800 --> 00:07:16.749 align:start position:0%
results to a a prompt so I prepared
something<00:07:14.120><c> for</c><00:07:14.360><c> that</c><00:07:14.520><c> so</c><00:07:14.720><c> let</c><00:07:14.879><c> me</c><00:07:15.120><c> paste</c><00:07:15.440><c> it</c><00:07:15.759><c> in</c>

00:07:16.749 --> 00:07:16.759 align:start position:0%
something for that so let me paste it in
 

00:07:16.759 --> 00:07:18.550 align:start position:0%
something for that so let me paste it in
so<00:07:17.000><c> what</c><00:07:17.120><c> you</c><00:07:17.240><c> can</c><00:07:17.440><c> see</c>

00:07:18.550 --> 00:07:18.560 align:start position:0%
so what you can see
 

00:07:18.560 --> 00:07:21.790 align:start position:0%
so what you can see
here<00:07:19.560><c> it's</c><00:07:19.680><c> a</c><00:07:19.919><c> prompt</c><00:07:20.440><c> right</c><00:07:20.560><c> so</c><00:07:20.720><c> it</c><00:07:20.800><c> say</c><00:07:21.039><c> like</c>

00:07:21.790 --> 00:07:21.800 align:start position:0%
here it's a prompt right so it say like
 

00:07:21.800 --> 00:07:23.950 align:start position:0%
here it's a prompt right so it say like
write<00:07:22.000><c> a</c><00:07:22.120><c> short</c><00:07:22.319><c> Airbnb</c><00:07:22.960><c> description</c><00:07:23.440><c> for</c><00:07:23.680><c> the</c>

00:07:23.950 --> 00:07:23.960 align:start position:0%
write a short Airbnb description for the
 

00:07:23.960 --> 00:07:27.869 align:start position:0%
write a short Airbnb description for the
listing<00:07:24.759><c> name</c><00:07:25.319><c> so</c><00:07:25.560><c> that</c><00:07:25.720><c> refers</c><00:07:26.120><c> to</c><00:07:26.319><c> this</c><00:07:26.879><c> name</c>

00:07:27.869 --> 00:07:27.879 align:start position:0%
listing name so that refers to this name
 

00:07:27.879 --> 00:07:29.990 align:start position:0%
listing name so that refers to this name
let<00:07:28.080><c> like</c><00:07:28.240><c> include</c><00:07:28.560><c> the</c><00:07:28.680><c> name</c><00:07:28.840><c> of</c><00:07:28.960><c> the</c><00:07:29.039><c> host</c><00:07:29.759><c> so</c>

00:07:29.990 --> 00:07:30.000 align:start position:0%
let like include the name of the host so
 

00:07:30.000 --> 00:07:32.029 align:start position:0%
let like include the name of the host so
that's<00:07:30.199><c> your</c><00:07:30.479><c> host</c><00:07:30.800><c> name</c><00:07:31.160><c> and</c><00:07:31.319><c> so</c><00:07:31.440><c> on</c><00:07:31.680><c> and</c><00:07:31.840><c> so</c>

00:07:32.029 --> 00:07:32.039 align:start position:0%
that's your host name and so on and so
 

00:07:32.039 --> 00:07:36.110 align:start position:0%
that's your host name and so on and so
forth<00:07:32.919><c> so</c><00:07:33.840><c> um</c><00:07:34.240><c> by</c><00:07:34.479><c> running</c><00:07:34.919><c> this</c><00:07:35.160><c> in</c><00:07:35.400><c> this</c><00:07:35.919><c> for</c>

00:07:36.110 --> 00:07:36.120 align:start position:0%
forth so um by running this in this for
 

00:07:36.120 --> 00:07:38.189 align:start position:0%
forth so um by running this in this for
this<00:07:36.319><c> specific</c><00:07:36.720><c> use</c><00:07:37.039><c> case</c><00:07:37.160><c> we</c><00:07:37.360><c> use</c><00:07:37.639><c> the</c><00:07:37.879><c> open</c>

00:07:38.189 --> 00:07:38.199 align:start position:0%
this specific use case we use the open
 

00:07:38.199 --> 00:07:43.790 align:start position:0%
this specific use case we use the open
AI<00:07:39.280><c> um</c><00:07:40.280><c> uh</c><00:07:41.039><c> um</c><00:07:42.039><c> uh</c><00:07:42.160><c> endpoint</c><00:07:43.000><c> so</c><00:07:43.199><c> what</c><00:07:43.319><c> you</c><00:07:43.479><c> see</c>

00:07:43.790 --> 00:07:43.800 align:start position:0%
AI um uh um uh endpoint so what you see
 

00:07:43.800 --> 00:07:47.350 align:start position:0%
AI um uh um uh endpoint so what you see
is<00:07:43.919><c> that</c><00:07:44.080><c> it</c><00:07:44.280><c> basically</c><00:07:44.919><c> returns</c><00:07:45.199><c> here</c><00:07:45.680><c> a</c><00:07:46.680><c> um</c>

00:07:47.350 --> 00:07:47.360 align:start position:0%
is that it basically returns here a um
 

00:07:47.360 --> 00:07:51.950 align:start position:0%
is that it basically returns here a um
um<00:07:47.720><c> a</c><00:07:47.960><c> single</c><00:07:48.639><c> result</c><00:07:49.639><c> where</c><00:07:50.400><c> it</c><00:07:50.599><c> created</c><00:07:51.240><c> a</c>

00:07:51.950 --> 00:07:51.960 align:start position:0%
um a single result where it created a
 

00:07:51.960 --> 00:07:54.070 align:start position:0%
um a single result where it created a
description<00:07:52.599><c> for</c><00:07:52.840><c> this</c><00:07:53.000><c> listing</c><00:07:53.599><c> based</c><00:07:53.919><c> on</c>

00:07:54.070 --> 00:07:54.080 align:start position:0%
description for this listing based on
 

00:07:54.080 --> 00:07:57.670 align:start position:0%
description for this listing based on
the<00:07:54.240><c> input</c><00:07:55.080><c> data</c><00:07:56.080><c> however</c><00:07:57.000><c> what</c><00:07:57.120><c> you</c><00:07:57.280><c> can</c><00:07:57.440><c> also</c>

00:07:57.670 --> 00:07:57.680 align:start position:0%
the input data however what you can also
 

00:07:57.680 --> 00:08:00.990 align:start position:0%
the input data however what you can also
see<00:07:58.000><c> now</c><00:07:58.440><c> is</c><00:07:58.639><c> that</c><00:07:58.800><c> this</c><00:07:58.960><c> single</c><00:07:59.440><c> result</c><00:08:00.440><c> is</c>

00:08:00.990 --> 00:08:01.000 align:start position:0%
see now is that this single result is
 

00:08:01.000 --> 00:08:02.990 align:start position:0%
see now is that this single result is
still<00:08:01.240><c> not</c><00:08:01.440><c> set</c><00:08:01.680><c> in</c><00:08:01.840><c> the</c><00:08:02.080><c> description</c><00:08:02.720><c> so</c><00:08:02.879><c> one</c>

00:08:02.990 --> 00:08:03.000 align:start position:0%
still not set in the description so one
 

00:08:03.000 --> 00:08:04.749 align:start position:0%
still not set in the description so one
of<00:08:03.120><c> the</c><00:08:03.240><c> things</c><00:08:03.440><c> that</c><00:08:03.599><c> we</c><00:08:03.720><c> can</c><00:08:03.879><c> do</c><00:08:04.240><c> is</c><00:08:04.400><c> that</c><00:08:04.599><c> we</c>

00:08:04.749 --> 00:08:04.759 align:start position:0%
of the things that we can do is that we
 

00:08:04.759 --> 00:08:10.710 align:start position:0%
of the things that we can do is that we
can<00:08:05.240><c> kick</c><00:08:05.520><c> off</c><00:08:05.960><c> a</c><00:08:06.680><c> um</c><00:08:07.680><c> uh</c><00:08:07.919><c> basically</c><00:08:08.440><c> a</c><00:08:08.960><c> um</c><00:08:09.560><c> uh</c><00:08:10.440><c> a</c>

00:08:10.710 --> 00:08:10.720 align:start position:0%
can kick off a um uh basically a um uh a
 

00:08:10.720 --> 00:08:12.350 align:start position:0%
can kick off a um uh basically a um uh a
a<00:08:10.879><c> rack</c><00:08:11.319><c> application</c><00:08:11.800><c> where</c><00:08:11.919><c> we're</c><00:08:12.080><c> going</c><00:08:12.159><c> to</c>

00:08:12.350 --> 00:08:12.360 align:start position:0%
a rack application where we're going to
 

00:08:12.360 --> 00:08:14.909 align:start position:0%
a rack application where we're going to
say<00:08:13.080><c> store</c><00:08:13.520><c> the</c><00:08:13.680><c> single</c><00:08:14.120><c> result</c><00:08:14.520><c> in</c><00:08:14.680><c> the</c>

00:08:14.909 --> 00:08:14.919 align:start position:0%
say store the single result in the
 

00:08:14.919 --> 00:08:18.629 align:start position:0%
say store the single result in the
description<00:08:15.919><c> so</c><00:08:16.199><c> if</c><00:08:16.319><c> we</c><00:08:16.919><c> remove</c><00:08:17.479><c> this</c><00:08:18.240><c> and</c>

00:08:18.629 --> 00:08:18.639 align:start position:0%
description so if we remove this and
 

00:08:18.639 --> 00:08:19.950 align:start position:0%
description so if we remove this and
I've<00:08:18.879><c> just</c><00:08:19.120><c> kicked</c><00:08:19.440><c> this</c><00:08:19.560><c> off</c><00:08:19.720><c> in</c><00:08:19.840><c> the</c>

00:08:19.950 --> 00:08:19.960 align:start position:0%
I've just kicked this off in the
 

00:08:19.960 --> 00:08:22.029 align:start position:0%
I've just kicked this off in the
background<00:08:20.879><c> so</c><00:08:21.039><c> if</c><00:08:21.159><c> we</c><00:08:21.280><c> now</c><00:08:21.440><c> run</c><00:08:21.680><c> this</c><00:08:21.840><c> same</c>

00:08:22.029 --> 00:08:22.039 align:start position:0%
background so if we now run this same
 

00:08:22.039 --> 00:08:24.309 align:start position:0%
background so if we now run this same
query<00:08:22.479><c> again</c><00:08:23.319><c> what</c><00:08:23.440><c> you</c><00:08:23.520><c> will</c><00:08:23.680><c> see</c><00:08:23.919><c> is</c><00:08:24.120><c> that</c>

00:08:24.309 --> 00:08:24.319 align:start position:0%
query again what you will see is that
 

00:08:24.319 --> 00:08:28.589 align:start position:0%
query again what you will see is that
now<00:08:24.479><c> the</c><00:08:24.800><c> description</c><00:08:25.800><c> you</c><00:08:25.919><c> see</c><00:08:26.360><c> it</c><00:08:27.360><c> is</c><00:08:28.000><c> um</c><00:08:28.440><c> has</c>

00:08:28.589 --> 00:08:28.599 align:start position:0%
now the description you see it is um has
 

00:08:28.599 --> 00:08:30.350 align:start position:0%
now the description you see it is um has
the<00:08:28.759><c> generated</c><00:08:29.319><c> content</c><00:08:29.639><c> in</c><00:08:29.840><c> that</c><00:08:30.080><c> and</c><00:08:30.199><c> what</c>

00:08:30.350 --> 00:08:30.360 align:start position:0%
the generated content in that and what
 

00:08:30.360 --> 00:08:32.389 align:start position:0%
the generated content in that and what
we<00:08:30.520><c> did</c><00:08:30.800><c> by</c><00:08:30.919><c> storing</c><00:08:31.319><c> this</c><00:08:31.520><c> back</c><00:08:31.720><c> into</c><00:08:31.879><c> the</c>

00:08:32.389 --> 00:08:32.399 align:start position:0%
we did by storing this back into the
 

00:08:32.399 --> 00:08:35.430 align:start position:0%
we did by storing this back into the
database<00:08:33.399><c> is</c><00:08:33.519><c> that</c><00:08:33.680><c> we</c><00:08:33.800><c> can</c><00:08:33.959><c> say</c><00:08:34.320><c> additional</c>

00:08:35.430 --> 00:08:35.440 align:start position:0%
database is that we can say additional
 

00:08:35.440 --> 00:08:38.190 align:start position:0%
database is that we can say additional
Vector<00:08:36.440><c> so</c><00:08:36.640><c> the</c><00:08:36.880><c> vector</c><00:08:37.279><c> representation</c><00:08:38.039><c> that</c>

00:08:38.190 --> 00:08:38.200 align:start position:0%
Vector so the vector representation that
 

00:08:38.200 --> 00:08:40.750 align:start position:0%
Vector so the vector representation that
got<00:08:38.360><c> added</c><00:08:38.599><c> to</c><00:08:38.760><c> this</c><00:08:38.959><c> as</c><00:08:39.080><c> you</c><00:08:39.200><c> can</c><00:08:39.360><c> see</c><00:08:39.680><c> here</c><00:08:40.479><c> is</c>

00:08:40.750 --> 00:08:40.760 align:start position:0%
got added to this as you can see here is
 

00:08:40.760 --> 00:08:44.110 align:start position:0%
got added to this as you can see here is
based<00:08:41.159><c> on</c><00:08:41.360><c> the</c><00:08:41.560><c> generated</c><00:08:42.680><c> content</c><00:08:43.680><c> and</c><00:08:43.880><c> what</c>

00:08:44.110 --> 00:08:44.120 align:start position:0%
based on the generated content and what
 

00:08:44.120 --> 00:08:46.070 align:start position:0%
based on the generated content and what
can<00:08:44.240><c> we</c><00:08:44.440><c> do</c><00:08:44.640><c> with</c><00:08:44.839><c> that</c><00:08:45.320><c> well</c><00:08:45.640><c> we</c><00:08:45.720><c> can</c><00:08:45.880><c> do</c>

00:08:46.070 --> 00:08:46.080 align:start position:0%
can we do with that well we can do
 

00:08:46.080 --> 00:08:48.829 align:start position:0%
can we do with that well we can do
things<00:08:46.360><c> like</c><00:08:46.600><c> this</c><00:08:46.839><c> so</c><00:08:47.080><c> we</c><00:08:47.200><c> can</c><00:08:47.399><c> now</c><00:08:47.600><c> do</c><00:08:47.880><c> a</c>

00:08:48.829 --> 00:08:48.839 align:start position:0%
things like this so we can now do a
 

00:08:48.839 --> 00:08:52.070 align:start position:0%
things like this so we can now do a
vector<00:08:49.240><c> search</c><00:08:49.720><c> and</c><00:08:49.839><c> then</c><00:08:50.000><c> so</c><00:08:50.760><c> place</c>

00:08:52.070 --> 00:08:52.080 align:start position:0%
vector search and then so place
 

00:08:52.080 --> 00:08:57.110 align:start position:0%
vector search and then so place
in<00:08:53.480><c> to</c>

00:08:57.110 --> 00:08:57.120 align:start position:0%
 
 

00:08:57.120 --> 00:09:00.750 align:start position:0%
 
near<00:08:58.200><c> dog</c><00:08:59.279><c> right</c><00:08:59.440><c> and</c><00:08:59.560><c> if</c><00:08:59.640><c> I</c><00:08:59.760><c> now</c><00:08:59.959><c> run</c><00:09:00.200><c> this</c>

00:09:00.750 --> 00:09:00.760 align:start position:0%
near dog right and if I now run this
 

00:09:00.760 --> 00:09:04.710 align:start position:0%
near dog right and if I now run this
query<00:09:01.760><c> here</c><00:09:01.920><c> you</c><00:09:02.120><c> go</c><00:09:02.560><c> we</c><00:09:02.720><c> get</c><00:09:02.959><c> back</c><00:09:03.240><c> a</c><00:09:03.560><c> result</c><00:09:04.560><c> a</c>

00:09:04.710 --> 00:09:04.720 align:start position:0%
query here you go we get back a result a
 

00:09:04.720 --> 00:09:08.630 align:start position:0%
query here you go we get back a result a
vector<00:09:05.040><c> search</c><00:09:05.399><c> result</c><00:09:06.000><c> based</c><00:09:06.440><c> on</c><00:09:06.760><c> the</c><00:09:07.519><c> um</c><00:09:08.519><c> uh</c>

00:09:08.630 --> 00:09:08.640 align:start position:0%
vector search result based on the um uh
 

00:09:08.640 --> 00:09:11.230 align:start position:0%
vector search result based on the um uh
on<00:09:08.839><c> the</c><00:09:09.000><c> query</c><00:09:09.519><c> so</c><00:09:10.079><c> in</c><00:09:10.200><c> this</c><00:09:10.399><c> case</c><00:09:10.640><c> The</c><00:09:10.920><c> East</c>

00:09:11.230 --> 00:09:11.240 align:start position:0%
on the query so in this case The East
 

00:09:11.240 --> 00:09:14.509 align:start position:0%
on the query so in this case The East
Harlem<00:09:11.880><c> apartment</c><00:09:12.640><c> right</c><00:09:13.600><c> and</c><00:09:14.079><c> um</c><00:09:14.240><c> oh</c><00:09:14.399><c> yeah</c>

00:09:14.509 --> 00:09:14.519 align:start position:0%
Harlem apartment right and um oh yeah
 

00:09:14.519 --> 00:09:16.470 align:start position:0%
Harlem apartment right and um oh yeah
and<00:09:14.600><c> it</c><00:09:14.680><c> says</c><00:09:14.839><c> like</c><00:09:15.000><c> located</c><00:09:15.519><c> by</c><00:09:15.680><c> Central</c><00:09:16.120><c> Park</c>

00:09:16.470 --> 00:09:16.480 align:start position:0%
and it says like located by Central Park
 

00:09:16.480 --> 00:09:19.269 align:start position:0%
and it says like located by Central Park
hence<00:09:16.839><c> the</c><00:09:17.399><c> that</c><00:09:17.519><c> it</c><00:09:17.640><c> makes</c><00:09:17.959><c> that</c><00:09:18.680><c> relation</c><00:09:19.040><c> to</c>

00:09:19.269 --> 00:09:19.279 align:start position:0%
hence the that it makes that relation to
 

00:09:19.279 --> 00:09:21.829 align:start position:0%
hence the that it makes that relation to
park<00:09:19.519><c> to</c><00:09:19.720><c> walk</c><00:09:19.920><c> my</c><00:09:20.120><c> dog</c><00:09:20.920><c> so</c><00:09:21.160><c> what</c><00:09:21.240><c> you've</c><00:09:21.480><c> seen</c>

00:09:21.829 --> 00:09:21.839 align:start position:0%
park to walk my dog so what you've seen
 

00:09:21.839 --> 00:09:23.710 align:start position:0%
park to walk my dog so what you've seen
here<00:09:22.240><c> is</c><00:09:22.399><c> that</c><00:09:22.560><c> we've</c><00:09:22.720><c> stored</c><00:09:23.040><c> all</c><00:09:23.240><c> these</c><00:09:23.399><c> data</c>

00:09:23.710 --> 00:09:23.720 align:start position:0%
here is that we've stored all these data
 

00:09:23.720 --> 00:09:26.389 align:start position:0%
here is that we've stored all these data
objects<00:09:24.720><c> we've</c><00:09:24.959><c> done</c><00:09:25.279><c> a</c><00:09:25.399><c> form</c><00:09:25.600><c> of</c><00:09:25.760><c> Rag</c><00:09:26.200><c> by</c>

00:09:26.389 --> 00:09:26.399 align:start position:0%
objects we've done a form of Rag by
 

00:09:26.399 --> 00:09:28.509 align:start position:0%
objects we've done a form of Rag by
piping<00:09:26.880><c> the</c><00:09:27.040><c> results</c><00:09:27.440><c> through</c><00:09:27.839><c> a</c><00:09:28.040><c> generative</c>

00:09:28.509 --> 00:09:28.519 align:start position:0%
piping the results through a generative
 

00:09:28.519 --> 00:09:30.990 align:start position:0%
piping the results through a generative
model<00:09:29.279><c> we</c><00:09:29.440><c> stored</c><00:09:29.880><c> that</c><00:09:30.040><c> generative</c><00:09:30.519><c> model</c>

00:09:30.990 --> 00:09:31.000 align:start position:0%
model we stored that generative model
 

00:09:31.000 --> 00:09:34.030 align:start position:0%
model we stored that generative model
back<00:09:31.600><c> into</c><00:09:32.079><c> weate</c><00:09:32.920><c> and</c><00:09:33.040><c> now</c><00:09:33.200><c> we</c><00:09:33.320><c> can</c><00:09:33.480><c> do</c><00:09:33.680><c> Factor</c>

00:09:34.030 --> 00:09:34.040 align:start position:0%
back into weate and now we can do Factor
 

00:09:34.040 --> 00:09:37.710 align:start position:0%
back into weate and now we can do Factor
search<00:09:34.600><c> over</c><00:09:35.200><c> these</c><00:09:35.839><c> results</c><00:09:36.839><c> this</c><00:09:37.040><c> data</c><00:09:37.399><c> set</c>

00:09:37.710 --> 00:09:37.720 align:start position:0%
search over these results this data set
 

00:09:37.720 --> 00:09:41.829 align:start position:0%
search over these results this data set
can<00:09:37.920><c> be</c><00:09:38.160><c> found</c><00:09:38.720><c> on</c><00:09:39.440><c> um</c><00:09:39.720><c> on</c><00:09:39.839><c> the</c><00:09:40.120><c> weate</c><00:09:41.120><c> uh</c><00:09:41.600><c> uh</c>

00:09:41.829 --> 00:09:41.839 align:start position:0%
can be found on um on the weate uh uh
 

00:09:41.839 --> 00:09:45.190 align:start position:0%
can be found on um on the weate uh uh
website<00:09:42.839><c> this</c><00:09:43.160><c> concept</c><00:09:44.040><c> um</c><00:09:44.399><c> is</c><00:09:44.680><c> we</c><00:09:44.839><c> call</c><00:09:45.000><c> it</c>

00:09:45.190 --> 00:09:45.200 align:start position:0%
website this concept um is we call it
 

00:09:45.200 --> 00:09:47.389 align:start position:0%
website this concept um is we call it
generative<00:09:45.600><c> feedback</c><00:09:46.000><c> loops</c><00:09:46.720><c> so</c><00:09:46.920><c> that</c><00:09:47.079><c> is</c><00:09:47.200><c> a</c>

00:09:47.389 --> 00:09:47.399 align:start position:0%
generative feedback loops so that is a
 

00:09:47.399 --> 00:09:51.269 align:start position:0%
generative feedback loops so that is a
combination<00:09:48.240><c> of</c><00:09:48.959><c> um</c><00:09:49.320><c> a</c><00:09:49.480><c> retrieval</c><00:09:50.040><c> augmented</c>

00:09:51.269 --> 00:09:51.279 align:start position:0%
combination of um a retrieval augmented
 

00:09:51.279 --> 00:09:54.230 align:start position:0%
combination of um a retrieval augmented
Generation<00:09:52.279><c> Um</c><00:09:52.880><c> and</c><00:09:53.040><c> Vector</c><00:09:53.360><c> search</c><00:09:53.920><c> so</c><00:09:54.160><c> if</c>

00:09:54.230 --> 00:09:54.240 align:start position:0%
Generation Um and Vector search so if
 

00:09:54.240 --> 00:09:55.590 align:start position:0%
Generation Um and Vector search so if
you<00:09:54.360><c> just</c><00:09:54.560><c> search</c><00:09:54.800><c> on</c><00:09:54.920><c> a</c><00:09:55.079><c> website</c><00:09:55.440><c> for</c>

00:09:55.590 --> 00:09:55.600 align:start position:0%
you just search on a website for
 

00:09:55.600 --> 00:09:57.590 align:start position:0%
you just search on a website for
generative<00:09:56.040><c> feedback</c><00:09:56.360><c> loops</c><00:09:57.160><c> you</c><00:09:57.279><c> can</c><00:09:57.440><c> find</c>

00:09:57.590 --> 00:09:57.600 align:start position:0%
generative feedback loops you can find
 

00:09:57.600 --> 00:10:00.150 align:start position:0%
generative feedback loops you can find
it<00:09:57.800><c> yourself</c><00:09:58.240><c> you</c><00:09:58.360><c> can</c><00:09:58.480><c> find</c><00:09:58.680><c> the</c><00:09:58.800><c> data</c><00:09:59.279><c> set</c>

00:10:00.150 --> 00:10:00.160 align:start position:0%
it yourself you can find the data set
 

00:10:00.160 --> 00:10:04.030 align:start position:0%
it yourself you can find the data set
the<00:10:00.959><c> complete</c><00:10:01.959><c> um</c><00:10:02.680><c> data</c><00:10:02.959><c> set</c><00:10:03.200><c> including</c><00:10:03.640><c> weate</c>

00:10:04.030 --> 00:10:04.040 align:start position:0%
the complete um data set including weate
 

00:10:04.040 --> 00:10:06.230 align:start position:0%
the complete um data set including weate
is<00:10:04.200><c> open</c><00:10:04.480><c> source</c><00:10:04.800><c> available</c><00:10:05.560><c> so</c><00:10:05.720><c> you</c><00:10:05.839><c> can</c><00:10:06.040><c> play</c>

00:10:06.230 --> 00:10:06.240 align:start position:0%
is open source available so you can play
 

00:10:06.240 --> 00:10:08.470 align:start position:0%
is open source available so you can play
around<00:10:06.480><c> with</c><00:10:06.600><c> it</c><00:10:06.760><c> yourself</c><00:10:07.680><c> or</c><00:10:08.040><c> you</c><00:10:08.160><c> can</c><00:10:08.279><c> use</c>

00:10:08.470 --> 00:10:08.480 align:start position:0%
around with it yourself or you can use
 

00:10:08.480 --> 00:10:11.269 align:start position:0%
around with it yourself or you can use
the<00:10:08.600><c> weate</c><00:10:09.040><c> cloud</c><00:10:09.279><c> service</c><00:10:09.720><c> as</c><00:10:09.839><c> I</c><00:10:10.120><c> just</c><00:10:10.320><c> did</c>

00:10:11.269 --> 00:10:11.279 align:start position:0%
the weate cloud service as I just did
 

00:10:11.279 --> 00:10:12.750 align:start position:0%
the weate cloud service as I just did
where<00:10:11.399><c> you</c><00:10:11.519><c> don't</c><00:10:11.680><c> have</c><00:10:11.800><c> to</c><00:10:11.920><c> run</c><00:10:12.120><c> the</c><00:10:12.279><c> database</c>

00:10:12.750 --> 00:10:12.760 align:start position:0%
where you don't have to run the database
 

00:10:12.760 --> 00:10:14.230 align:start position:0%
where you don't have to run the database
yourself<00:10:13.200><c> if</c><00:10:13.279><c> you</c><00:10:13.360><c> don't</c><00:10:13.519><c> want</c>

00:10:14.230 --> 00:10:14.240 align:start position:0%
yourself if you don't want
 

00:10:14.240 --> 00:10:18.190 align:start position:0%
yourself if you don't want
to<00:10:15.240><c> so</c><00:10:15.720><c> that</c><00:10:15.880><c> was</c><00:10:16.079><c> my</c><00:10:16.279><c> demo</c><00:10:16.720><c> about</c><00:10:16.959><c> we8</c><00:10:17.800><c> um</c><00:10:17.959><c> feel</c>

00:10:18.190 --> 00:10:18.200 align:start position:0%
to so that was my demo about we8 um feel
 

00:10:18.200 --> 00:10:20.990 align:start position:0%
to so that was my demo about we8 um feel
free<00:10:18.399><c> to</c><00:10:18.560><c> reach</c><00:10:18.720><c> out</c><00:10:18.880><c> to</c><00:10:19.000><c> me</c><00:10:19.160><c> on</c><00:10:19.360><c> Bob</c><00:10:19.839><c> we.</c><00:10:20.839><c> or</c>

00:10:20.990 --> 00:10:21.000 align:start position:0%
free to reach out to me on Bob we. or
 

00:10:21.000 --> 00:10:23.310 align:start position:0%
free to reach out to me on Bob we. or
just<00:10:21.160><c> go</c><00:10:21.279><c> to</c><00:10:21.399><c> the</c><00:10:21.519><c> website</c><00:10:21.880><c> we.</c><00:10:22.880><c> where</c><00:10:23.000><c> you</c><00:10:23.160><c> can</c>

00:10:23.310 --> 00:10:23.320 align:start position:0%
just go to the website we. where you can
 

00:10:23.320 --> 00:10:25.630 align:start position:0%
just go to the website we. where you can
start<00:10:23.560><c> using</c><00:10:23.880><c> the</c><00:10:24.200><c> database</c><00:10:25.200><c> I'm</c><00:10:25.399><c> looking</c>

00:10:25.630 --> 00:10:25.640 align:start position:0%
start using the database I'm looking
 

00:10:25.640 --> 00:10:27.350 align:start position:0%
start using the database I'm looking
forward<00:10:26.000><c> to</c><00:10:26.160><c> see</c><00:10:26.399><c> what</c><00:10:26.519><c> you</c><00:10:26.600><c> will</c><00:10:26.760><c> be</c><00:10:26.880><c> building</c>

00:10:27.350 --> 00:10:27.360 align:start position:0%
forward to see what you will be building
 

00:10:27.360 --> 00:10:32.800 align:start position:0%
forward to see what you will be building
or<00:10:27.560><c> to</c><00:10:27.800><c> hear</c><00:10:28.000><c> from</c><00:10:28.200><c> you</c><00:10:28.720><c> thank</c><00:10:28.880><c> you</c><00:10:29.240><c> so</c><00:10:29.800><c> much</c>

