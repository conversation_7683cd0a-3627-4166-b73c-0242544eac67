WEBVTT
Kind: captions
Language: en

00:00:01.199 --> 00:00:02.950 align:start position:0%
 
in<00:00:01.400><c> this</c><00:00:01.560><c> movie</c><00:00:01.880><c> I'd</c><00:00:02.040><c> like</c><00:00:02.159><c> to</c><00:00:02.280><c> show</c><00:00:02.520><c> you</c><00:00:02.800><c> how</c>

00:00:02.950 --> 00:00:02.960 align:start position:0%
in this movie I'd like to show you how
 

00:00:02.960 --> 00:00:05.150 align:start position:0%
in this movie I'd like to show you how
to<00:00:03.199><c> set</c><00:00:03.400><c> up</c><00:00:04.400><c> the</c>

00:00:05.150 --> 00:00:05.160 align:start position:0%
to set up the
 

00:00:05.160 --> 00:00:09.549 align:start position:0%
to set up the
SMTP<00:00:06.160><c> integration</c><00:00:07.080><c> module</c><00:00:08.080><c> with</c><00:00:08.320><c> capis</c>

00:00:09.549 --> 00:00:09.559 align:start position:0%
SMTP integration module with capis
 

00:00:09.559 --> 00:00:12.430 align:start position:0%
SMTP integration module with capis
rightfax<00:00:10.559><c> first</c><00:00:10.840><c> I'm</c><00:00:11.000><c> going</c><00:00:11.200><c> to</c><00:00:11.440><c> install</c><00:00:12.160><c> a</c>

00:00:12.430 --> 00:00:12.440 align:start position:0%
rightfax first I'm going to install a
 

00:00:12.440 --> 00:00:16.269 align:start position:0%
rightfax first I'm going to install a
POP<00:00:12.719><c> 3</c><00:00:13.480><c> server</c><00:00:14.480><c> this</c><00:00:14.599><c> is</c><00:00:14.839><c> the</c><00:00:15.360><c> pop</c><00:00:15.599><c> 3</c><00:00:15.839><c> service</c>

00:00:16.269 --> 00:00:16.279 align:start position:0%
POP 3 server this is the pop 3 service
 

00:00:16.279 --> 00:00:18.870 align:start position:0%
POP 3 server this is the pop 3 service
that<00:00:16.440><c> comes</c><00:00:16.840><c> with</c><00:00:17.680><c> uh</c><00:00:17.800><c> Microsoft</c><00:00:18.279><c> Windows</c><00:00:18.680><c> on</c>

00:00:18.870 --> 00:00:18.880 align:start position:0%
that comes with uh Microsoft Windows on
 

00:00:18.880 --> 00:00:22.109 align:start position:0%
that comes with uh Microsoft Windows on
the<00:00:19.119><c> on</c><00:00:19.279><c> the</c><00:00:19.760><c> uh</c><00:00:19.920><c> installation</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
the on the uh installation
 

00:00:22.119 --> 00:00:25.390 align:start position:0%
the on the uh installation
CD<00:00:23.119><c> so</c><00:00:23.279><c> I've</c><00:00:23.400><c> just</c><00:00:23.519><c> done</c><00:00:23.720><c> a</c><00:00:24.000><c> basic</c><00:00:24.400><c> install</c>

00:00:25.390 --> 00:00:25.400 align:start position:0%
CD so I've just done a basic install
 

00:00:25.400 --> 00:00:27.070 align:start position:0%
CD so I've just done a basic install
haven't<00:00:25.640><c> chosen</c><00:00:26.039><c> any</c><00:00:26.320><c> options</c><00:00:26.679><c> there</c><00:00:26.800><c> aren't</c>

00:00:27.070 --> 00:00:27.080 align:start position:0%
haven't chosen any options there aren't
 

00:00:27.080 --> 00:00:28.950 align:start position:0%
haven't chosen any options there aren't
any<00:00:27.359><c> options</c><00:00:27.640><c> to</c><00:00:27.840><c> choose</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
any options to choose
 

00:00:28.960 --> 00:00:31.749 align:start position:0%
any options to choose
really

00:00:31.749 --> 00:00:31.759 align:start position:0%
really
 

00:00:31.759 --> 00:00:36.310 align:start position:0%
really
letting<00:00:32.040><c> it</c><00:00:32.200><c> finish</c><00:00:32.520><c> the</c>

00:00:36.310 --> 00:00:36.320 align:start position:0%
 
 

00:00:36.320 --> 00:00:38.389 align:start position:0%
 
install<00:00:37.320><c> and</c><00:00:37.440><c> we're</c>

00:00:38.389 --> 00:00:38.399 align:start position:0%
install and we're
 

00:00:38.399 --> 00:00:41.029 align:start position:0%
install and we're
done<00:00:39.399><c> now</c><00:00:39.559><c> I'm</c><00:00:39.680><c> going</c><00:00:39.800><c> to</c><00:00:39.920><c> go</c><00:00:40.039><c> to</c><00:00:40.239><c> the</c><00:00:40.480><c> pop</c><00:00:40.719><c> 3</c>

00:00:41.029 --> 00:00:41.039 align:start position:0%
done now I'm going to go to the pop 3
 

00:00:41.039 --> 00:00:45.549 align:start position:0%
done now I'm going to go to the pop 3
service<00:00:41.920><c> configuration</c>

00:00:45.549 --> 00:00:45.559 align:start position:0%
 
 

00:00:45.559 --> 00:00:48.950 align:start position:0%
 
tool<00:00:46.559><c> and</c><00:00:46.800><c> first</c><00:00:47.000><c> thing</c><00:00:47.120><c> I</c><00:00:47.239><c> want</c><00:00:47.320><c> to</c><00:00:47.520><c> do</c><00:00:48.000><c> is</c>

00:00:48.950 --> 00:00:48.960 align:start position:0%
tool and first thing I want to do is
 

00:00:48.960 --> 00:00:51.910 align:start position:0%
tool and first thing I want to do is
change<00:00:49.960><c> uh</c><00:00:50.120><c> it</c><00:00:50.239><c> from</c><00:00:50.440><c> using</c><00:00:50.840><c> active</c><00:00:51.199><c> directory</c>

00:00:51.910 --> 00:00:51.920 align:start position:0%
change uh it from using active directory
 

00:00:51.920 --> 00:00:54.670 align:start position:0%
change uh it from using active directory
integrated<00:00:52.920><c> authentication</c><00:00:53.760><c> just</c><00:00:54.000><c> to</c><00:00:54.160><c> local</c>

00:00:54.670 --> 00:00:54.680 align:start position:0%
integrated authentication just to local
 

00:00:54.680 --> 00:00:57.709 align:start position:0%
integrated authentication just to local
Windows<00:00:55.800><c> accounts</c><00:00:56.800><c> the</c><00:00:56.960><c> reason</c><00:00:57.239><c> is</c><00:00:57.399><c> that</c><00:00:57.559><c> I've</c>

00:00:57.709 --> 00:00:57.719 align:start position:0%
Windows accounts the reason is that I've
 

00:00:57.719 --> 00:00:59.189 align:start position:0%
Windows accounts the reason is that I've
got<00:00:57.879><c> a</c><00:00:58.120><c> Exchange</c><00:00:58.519><c> Server</c><00:00:58.840><c> and</c><00:00:58.920><c> I</c><00:00:59.000><c> don't</c><00:00:59.120><c> want</c>

00:00:59.189 --> 00:00:59.199 align:start position:0%
got a Exchange Server and I don't want
 

00:00:59.199 --> 00:01:01.750 align:start position:0%
got a Exchange Server and I don't want
to<00:00:59.320><c> screw</c><00:00:59.600><c> that</c><00:00:59.760><c> up</c>

00:01:01.750 --> 00:01:01.760 align:start position:0%
to screw that up
 

00:01:01.760 --> 00:01:06.630 align:start position:0%
to screw that up
so<00:01:02.079><c> my</c><00:01:02.280><c> domain</c><00:01:02.640><c> is</c><00:01:02.920><c> going</c><00:01:03.000><c> to</c><00:01:03.120><c> be</c><00:01:03.320><c> sntp</c><00:01:04.159><c> doc.</c>

00:01:06.630 --> 00:01:06.640 align:start position:0%
so my domain is going to be sntp doc.
 

00:01:06.640 --> 00:01:09.990 align:start position:0%
so my domain is going to be sntp doc.
looc<00:01:07.640><c> and</c><00:01:07.960><c> I'm</c><00:01:08.040><c> going</c><00:01:08.119><c> to</c><00:01:08.200><c> create</c><00:01:08.439><c> some</c><00:01:09.000><c> users</c>

00:01:09.990 --> 00:01:10.000 align:start position:0%
looc and I'm going to create some users
 

00:01:10.000 --> 00:01:12.070 align:start position:0%
looc and I'm going to create some users
first<00:01:10.479><c> administrator</c><00:01:11.320><c> but</c><00:01:11.680><c> you</c><00:01:11.799><c> know</c><00:01:11.920><c> I've</c>

00:01:12.070 --> 00:01:12.080 align:start position:0%
first administrator but you know I've
 

00:01:12.080 --> 00:01:14.390 align:start position:0%
first administrator but you know I've
already<00:01:12.280><c> got</c><00:01:12.600><c> an</c><00:01:12.759><c> administrator</c><00:01:13.360><c> user</c><00:01:14.159><c> so</c><00:01:14.320><c> I</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
already got an administrator user so I
 

00:01:14.400 --> 00:01:19.030 align:start position:0%
already got an administrator user so I
don't<00:01:14.520><c> want</c><00:01:14.600><c> to</c><00:01:14.799><c> create</c><00:01:15.280><c> that</c><00:01:16.080><c> Windows</c>

00:01:19.030 --> 00:01:19.040 align:start position:0%
 
 

00:01:19.040 --> 00:01:21.870 align:start position:0%
 
account<00:01:20.040><c> now</c><00:01:20.159><c> I'm</c><00:01:20.240><c> going</c><00:01:20.320><c> to</c><00:01:20.439><c> create</c><00:01:20.680><c> the</c><00:01:20.759><c> RFA</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
account now I'm going to create the RFA
 

00:01:21.880 --> 00:01:24.630 align:start position:0%
account now I'm going to create the RFA
user<00:01:22.880><c> which</c><00:01:23.119><c> is</c><00:01:23.520><c> the</c><00:01:23.680><c> user</c><00:01:24.240><c> that</c><00:01:24.360><c> who's</c><00:01:24.560><c> going</c>

00:01:24.630 --> 00:01:24.640 align:start position:0%
user which is the user that who's going
 

00:01:24.640 --> 00:01:28.910 align:start position:0%
user which is the user that who's going
to<00:01:24.880><c> receive</c><00:01:25.799><c> all</c><00:01:26.240><c> outgoing</c><00:01:26.840><c> and</c><00:01:27.040><c> incoming</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
to receive all outgoing and incoming
 

00:01:28.920 --> 00:01:37.950 align:start position:0%
to receive all outgoing and incoming
faxes<00:01:30.320><c> so</c><00:01:30.520><c> I'll</c><00:01:30.680><c> click</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
 
 

00:01:37.960 --> 00:01:43.190 align:start position:0%
 
okay<00:01:38.960><c> okay</c><00:01:39.479><c> so</c><00:01:40.240><c> now</c><00:01:41.079><c> I</c><00:01:41.240><c> want</c><00:01:41.399><c> to</c><00:01:41.720><c> see</c><00:01:42.119><c> if</c><00:01:42.399><c> my</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
okay okay so now I want to see if my
 

00:01:43.200 --> 00:01:46.310 align:start position:0%
okay okay so now I want to see if my
mailboxes<00:01:44.240><c> work</c><00:01:45.240><c> so</c><00:01:45.439><c> first</c><00:01:45.680><c> I'm</c><00:01:45.799><c> going</c><00:01:45.880><c> to</c><00:01:46.079><c> try</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
mailboxes work so first I'm going to try
 

00:01:46.320 --> 00:01:51.350 align:start position:0%
mailboxes work so first I'm going to try
out<00:01:46.680><c> the</c><00:01:47.119><c> administrator</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
 
 

00:01:51.360 --> 00:01:54.310 align:start position:0%
 
account<00:01:52.360><c> so</c><00:01:52.560><c> I'm</c><00:01:52.920><c> administrator</c><00:01:53.920><c> and</c><00:01:54.079><c> here's</c>

00:01:54.310 --> 00:01:54.320 align:start position:0%
account so I'm administrator and here's
 

00:01:54.320 --> 00:01:55.389 align:start position:0%
account so I'm administrator and here's
my<00:01:54.439><c> email</c>

00:01:55.389 --> 00:01:55.399 align:start position:0%
my email
 

00:01:55.399 --> 00:01:57.870 align:start position:0%
my email
address<00:01:56.399><c> there's</c><00:01:56.680><c> my</c><00:01:56.840><c> pop</c><00:01:57.119><c> 3</c>

00:01:57.870 --> 00:01:57.880 align:start position:0%
address there's my pop 3
 

00:01:57.880 --> 00:02:02.389 align:start position:0%
address there's my pop 3
server<00:01:58.880><c> and</c><00:01:59.159><c> outgoing</c><00:01:59.600><c> as</c><00:01:59.680><c> well</c>

00:02:02.389 --> 00:02:02.399 align:start position:0%
 
 

00:02:02.399 --> 00:02:14.509 align:start position:0%
 
and<00:02:02.600><c> my</c><00:02:02.799><c> account</c><00:02:03.079><c> name</c><00:02:03.920><c> and</c>

00:02:14.509 --> 00:02:14.519 align:start position:0%
 
 

00:02:14.519 --> 00:02:17.070 align:start position:0%
 
password<00:02:15.519><c> okay</c><00:02:15.959><c> so</c><00:02:16.200><c> I'm</c><00:02:16.480><c> creating</c><00:02:16.800><c> a</c><00:02:16.879><c> new</c>

00:02:17.070 --> 00:02:17.080 align:start position:0%
password okay so I'm creating a new
 

00:02:17.080 --> 00:02:19.470 align:start position:0%
password okay so I'm creating a new
message<00:02:17.360><c> to</c><00:02:17.680><c> myself</c><00:02:18.680><c> you</c><00:02:18.800><c> know</c><00:02:18.920><c> if</c><00:02:19.080><c> this</c><00:02:19.239><c> stuff</c>

00:02:19.470 --> 00:02:19.480 align:start position:0%
message to myself you know if this stuff
 

00:02:19.480 --> 00:02:21.910 align:start position:0%
message to myself you know if this stuff
doesn't<00:02:19.840><c> work</c><00:02:20.280><c> there's</c><00:02:20.560><c> no</c><00:02:20.840><c> chance</c><00:02:21.319><c> that</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
doesn't work there's no chance that
 

00:02:21.920 --> 00:02:24.070 align:start position:0%
doesn't work there's no chance that
rightfax<00:02:22.920><c> is</c><00:02:23.040><c> going</c><00:02:23.160><c> to</c><00:02:23.280><c> be</c><00:02:23.400><c> able</c><00:02:23.560><c> to</c><00:02:23.680><c> use</c><00:02:23.959><c> the</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
rightfax is going to be able to use the
 

00:02:24.080 --> 00:02:26.430 align:start position:0%
rightfax is going to be able to use the
SMTP<00:02:24.640><c> server</c><00:02:24.959><c> so</c><00:02:25.200><c> I</c><00:02:25.599><c> I</c><00:02:25.720><c> really</c><00:02:25.920><c> need</c><00:02:26.080><c> to</c><00:02:26.239><c> make</c>

00:02:26.430 --> 00:02:26.440 align:start position:0%
SMTP server so I I really need to make
 

00:02:26.440 --> 00:02:29.910 align:start position:0%
SMTP server so I I really need to make
sure<00:02:26.840><c> that</c><00:02:27.720><c> my</c><00:02:28.080><c> installation</c><00:02:29.080><c> my</c><00:02:29.280><c> basic</c>

00:02:29.910 --> 00:02:29.920 align:start position:0%
sure that my installation my basic
 

00:02:29.920 --> 00:02:35.309 align:start position:0%
sure that my installation my basic
installation<00:02:30.360><c> of</c><00:02:30.519><c> the</c><00:02:30.879><c> SMTP</c><00:02:31.519><c> server</c><00:02:32.160><c> actually</c>

00:02:35.309 --> 00:02:35.319 align:start position:0%
 
 

00:02:35.319 --> 00:02:37.710 align:start position:0%
 
works<00:02:36.319><c> once</c><00:02:36.519><c> I've</c><00:02:36.720><c> tested</c><00:02:37.120><c> this</c><00:02:37.319><c> the</c><00:02:37.480><c> actual</c>

00:02:37.710 --> 00:02:37.720 align:start position:0%
works once I've tested this the actual
 

00:02:37.720 --> 00:02:40.390 align:start position:0%
works once I've tested this the actual
rightfax<00:02:38.280><c> stuff</c><00:02:38.560><c> is</c><00:02:38.920><c> is</c><00:02:39.120><c> easy</c><00:02:39.879><c> so</c><00:02:40.159><c> now</c><00:02:40.280><c> I'm</c>

00:02:40.390 --> 00:02:40.400 align:start position:0%
rightfax stuff is is easy so now I'm
 

00:02:40.400 --> 00:02:45.149 align:start position:0%
rightfax stuff is is easy so now I'm
trying<00:02:40.720><c> out</c><00:02:41.000><c> the</c><00:02:41.200><c> RFA</c>

00:02:45.149 --> 00:02:45.159 align:start position:0%
 
 

00:02:45.159 --> 00:02:48.229 align:start position:0%
 
user<00:02:46.159><c> and</c><00:02:46.720><c> um</c><00:02:47.400><c> it's</c><00:02:47.560><c> all</c><00:02:47.840><c> the</c><00:02:47.959><c> same</c>

00:02:48.229 --> 00:02:48.239 align:start position:0%
user and um it's all the same
 

00:02:48.239 --> 00:02:49.949 align:start position:0%
user and um it's all the same
configuration<00:02:48.879><c> just</c><00:02:49.040><c> using</c><00:02:49.360><c> a</c><00:02:49.560><c> different</c>

00:02:49.949 --> 00:02:49.959 align:start position:0%
configuration just using a different
 

00:02:49.959 --> 00:02:54.149 align:start position:0%
configuration just using a different
username<00:02:50.440><c> to</c><00:02:50.640><c> log</c><00:02:50.879><c> in</c><00:02:51.280><c> as</c><00:02:52.280><c> RFA</c><00:02:53.000><c> at</c>

00:02:54.149 --> 00:02:54.159 align:start position:0%
username to log in as RFA at
 

00:02:54.159 --> 00:02:58.830 align:start position:0%
username to log in as RFA at
smtp.at

00:02:58.830 --> 00:02:58.840 align:start position:0%
 
 

00:02:58.840 --> 00:03:00.670 align:start position:0%
 
looc

00:03:00.670 --> 00:03:00.680 align:start position:0%
looc
 

00:03:00.680 --> 00:03:03.030 align:start position:0%
looc
click<00:03:00.879><c> on</c><00:03:01.080><c> next</c><00:03:01.360><c> and</c><00:03:01.640><c> finish</c><00:03:02.640><c> and</c><00:03:02.760><c> now</c><00:03:02.920><c> I'm</c>

00:03:03.030 --> 00:03:03.040 align:start position:0%
click on next and finish and now I'm
 

00:03:03.040 --> 00:03:04.670 align:start position:0%
click on next and finish and now I'm
just<00:03:03.159><c> going</c><00:03:03.280><c> to</c><00:03:03.400><c> send</c><00:03:03.799><c> myself</c><00:03:04.319><c> well</c><00:03:04.519><c> I'm</c><00:03:04.599><c> going</c>

00:03:04.670 --> 00:03:04.680 align:start position:0%
just going to send myself well I'm going
 

00:03:04.680 --> 00:03:07.350 align:start position:0%
just going to send myself well I'm going
to<00:03:04.799><c> send</c><00:03:05.480><c> RFA</c><00:03:06.480><c> a</c>

00:03:07.350 --> 00:03:07.360 align:start position:0%
to send RFA a
 

00:03:07.360 --> 00:03:14.710 align:start position:0%
to send RFA a
email<00:03:08.360><c> from</c><00:03:08.680><c> me</c><00:03:09.640><c> from</c>

00:03:14.710 --> 00:03:14.720 align:start position:0%
 
 

00:03:14.720 --> 00:03:17.509 align:start position:0%
 
administrator<00:03:15.720><c> and</c><00:03:15.920><c> since</c><00:03:16.159><c> I've</c><00:03:16.319><c> set</c><00:03:16.760><c> RFA</c><00:03:17.360><c> up</c>

00:03:17.509 --> 00:03:17.519 align:start position:0%
administrator and since I've set RFA up
 

00:03:17.519 --> 00:03:21.070 align:start position:0%
administrator and since I've set RFA up
as<00:03:17.640><c> an</c><00:03:17.879><c> account</c><00:03:18.640><c> for</c><00:03:19.000><c> Outlook</c><00:03:19.920><c> Express</c><00:03:20.920><c> I'll</c>

00:03:21.070 --> 00:03:21.080 align:start position:0%
as an account for Outlook Express I'll
 

00:03:21.080 --> 00:03:23.190 align:start position:0%
as an account for Outlook Express I'll
be<00:03:21.200><c> able</c><00:03:21.400><c> to</c><00:03:21.519><c> send</c><00:03:21.799><c> from</c><00:03:22.159><c> administrator</c><00:03:23.000><c> and</c>

00:03:23.190 --> 00:03:23.200 align:start position:0%
be able to send from administrator and
 

00:03:23.200 --> 00:03:26.670 align:start position:0%
be able to send from administrator and
receive<00:03:23.519><c> an</c><00:03:23.920><c> RFA</c><00:03:24.920><c> from</c><00:03:25.120><c> the</c><00:03:25.239><c> same</c>

00:03:26.670 --> 00:03:26.680 align:start position:0%
receive an RFA from the same
 

00:03:26.680 --> 00:03:29.350 align:start position:0%
receive an RFA from the same
mailbox<00:03:27.680><c> okay</c><00:03:27.959><c> so</c><00:03:28.120><c> I</c><00:03:28.280><c> got</c><00:03:28.480><c> that</c><00:03:28.680><c> working</c>

00:03:29.350 --> 00:03:29.360 align:start position:0%
mailbox okay so I got that working
 

00:03:29.360 --> 00:03:30.910 align:start position:0%
mailbox okay so I got that working
that's<00:03:29.560><c> great</c>

00:03:30.910 --> 00:03:30.920 align:start position:0%
that's great
 

00:03:30.920 --> 00:03:33.830 align:start position:0%
that's great
now<00:03:31.159><c> I</c><00:03:31.239><c> want</c><00:03:31.360><c> to</c><00:03:31.599><c> delete</c><00:03:32.480><c> the</c><00:03:32.760><c> rfx</c><00:03:33.439><c> account</c>

00:03:33.830 --> 00:03:33.840 align:start position:0%
now I want to delete the rfx account
 

00:03:33.840 --> 00:03:36.910 align:start position:0%
now I want to delete the rfx account
from<00:03:34.120><c> Outlook</c><00:03:34.640><c> Express</c><00:03:35.640><c> if</c><00:03:35.760><c> I</c><00:03:35.879><c> don't</c><00:03:36.120><c> do</c><00:03:36.400><c> that</c>

00:03:36.910 --> 00:03:36.920 align:start position:0%
from Outlook Express if I don't do that
 

00:03:36.920 --> 00:03:38.589 align:start position:0%
from Outlook Express if I don't do that
Outlook<00:03:37.360><c> Express</c><00:03:37.760><c> is</c><00:03:37.840><c> going</c><00:03:37.959><c> to</c><00:03:38.080><c> grab</c><00:03:38.400><c> that</c>

00:03:38.589 --> 00:03:38.599 align:start position:0%
Outlook Express is going to grab that
 

00:03:38.599 --> 00:03:41.550 align:start position:0%
Outlook Express is going to grab that
email<00:03:39.519><c> before</c><00:03:40.080><c> the</c><00:03:40.280><c> rightfax</c><00:03:40.879><c> module</c><00:03:41.319><c> has</c><00:03:41.439><c> a</c>

00:03:41.550 --> 00:03:41.560 align:start position:0%
email before the rightfax module has a
 

00:03:41.560 --> 00:03:43.830 align:start position:0%
email before the rightfax module has a
chance<00:03:41.840><c> to</c><00:03:42.000><c> even</c><00:03:42.239><c> look</c><00:03:42.439><c> in</c><00:03:42.560><c> the</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
chance to even look in the
 

00:03:43.840 --> 00:03:51.190 align:start position:0%
chance to even look in the
mailbox<00:03:44.840><c> so</c><00:03:45.159><c> no</c><00:03:45.319><c> faxes</c><00:03:45.799><c> would</c><00:03:46.000><c> ever</c><00:03:46.239><c> go</c>

00:03:51.190 --> 00:03:51.200 align:start position:0%
 
 

00:03:51.200 --> 00:03:54.589 align:start position:0%
 
out<00:03:52.200><c> okay</c><00:03:52.439><c> now</c><00:03:52.760><c> I'm</c><00:03:52.959><c> going</c><00:03:53.319><c> into</c><00:03:54.079><c> Enterprise</c>

00:03:54.589 --> 00:03:54.599 align:start position:0%
out okay now I'm going into Enterprise
 

00:03:54.599 --> 00:03:58.710 align:start position:0%
out okay now I'm going into Enterprise
fax<00:03:55.000><c> manager</c><00:03:56.000><c> and</c><00:03:56.200><c> creating</c><00:03:56.799><c> the</c><00:03:57.760><c> email</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
fax manager and creating the email
 

00:03:58.720 --> 00:04:00.750 align:start position:0%
fax manager and creating the email
Gateway<00:03:59.760><c> but</c><00:03:59.879><c> of</c><00:03:59.959><c> course</c><00:04:00.120><c> I</c><00:04:00.239><c> don't</c><00:04:00.400><c> have</c><00:04:00.560><c> any</c>

00:04:00.750 --> 00:04:00.760 align:start position:0%
Gateway but of course I don't have any
 

00:04:00.760 --> 00:04:04.390 align:start position:0%
Gateway but of course I don't have any
email<00:04:01.159><c> gateways</c><00:04:01.680><c> so</c><00:04:02.599><c> nothing's</c><00:04:03.079><c> shown</c><00:04:03.480><c> in</c><00:04:03.640><c> my</c>

00:04:04.390 --> 00:04:04.400 align:start position:0%
email gateways so nothing's shown in my
 

00:04:04.400 --> 00:04:07.229 align:start position:0%
email gateways so nothing's shown in my
services<00:04:05.400><c> so</c><00:04:05.599><c> I</c><00:04:05.680><c> can</c><00:04:05.799><c> go</c><00:04:05.920><c> to</c><00:04:06.079><c> control</c>

00:04:07.229 --> 00:04:07.239 align:start position:0%
services so I can go to control
 

00:04:07.239 --> 00:04:11.550 align:start position:0%
services so I can go to control
panel<00:04:08.239><c> and</c><00:04:08.840><c> um</c><00:04:09.640><c> add</c><00:04:10.000><c> that</c><00:04:10.200><c> service</c><00:04:11.120><c> here</c><00:04:11.239><c> it</c><00:04:11.360><c> is</c>

00:04:11.550 --> 00:04:11.560 align:start position:0%
panel and um add that service here it is
 

00:04:11.560 --> 00:04:13.110 align:start position:0%
panel and um add that service here it is
rightfax<00:04:12.120><c> email</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
rightfax email
 

00:04:13.120 --> 00:04:17.310 align:start position:0%
rightfax email
Gateway<00:04:14.120><c> and</c><00:04:14.360><c> I'm</c><00:04:14.480><c> going</c><00:04:14.599><c> to</c><00:04:15.359><c> add</c><00:04:16.359><c> the</c><00:04:16.600><c> SMTP</c>

00:04:17.310 --> 00:04:17.320 align:start position:0%
Gateway and I'm going to add the SMTP
 

00:04:17.320 --> 00:04:20.150 align:start position:0%
Gateway and I'm going to add the SMTP
POP<00:04:17.600><c> 3</c>

00:04:20.150 --> 00:04:20.160 align:start position:0%
POP 3
 

00:04:20.160 --> 00:04:24.030 align:start position:0%
POP 3
Gateway<00:04:21.160><c> my</c><00:04:21.359><c> server</c><00:04:21.720><c> address</c><00:04:22.040><c> is</c>

00:04:24.030 --> 00:04:24.040 align:start position:0%
Gateway my server address is
 

00:04:24.040 --> 00:04:28.710 align:start position:0%
Gateway my server address is
smtp.ao<00:04:25.040><c> and</c><00:04:25.199><c> my</c><00:04:25.360><c> pop</c><00:04:25.600><c> three</c><00:04:25.800><c> mailbox</c><00:04:26.440><c> is</c><00:04:27.199><c> RFA</c>

00:04:28.710 --> 00:04:28.720 align:start position:0%
smtp.ao and my pop three mailbox is RFA
 

00:04:28.720 --> 00:04:30.510 align:start position:0%
smtp.ao and my pop three mailbox is RFA
smtp.at

00:04:30.510 --> 00:04:30.520 align:start position:0%
smtp.at
 

00:04:30.520 --> 00:04:33.310 align:start position:0%
smtp.at
local<00:04:31.520><c> now</c><00:04:31.720><c> depending</c><00:04:32.000><c> on</c><00:04:32.160><c> your</c><00:04:32.400><c> POP</c><00:04:32.639><c> 3</c><00:04:32.840><c> server</c>

00:04:33.310 --> 00:04:33.320 align:start position:0%
local now depending on your POP 3 server
 

00:04:33.320 --> 00:04:35.270 align:start position:0%
local now depending on your POP 3 server
you<00:04:33.520><c> may</c><00:04:33.720><c> or</c><00:04:33.919><c> may</c><00:04:34.120><c> not</c><00:04:34.320><c> be</c><00:04:34.520><c> required</c><00:04:34.880><c> to</c><00:04:35.000><c> put</c><00:04:35.160><c> in</c>

00:04:35.270 --> 00:04:35.280 align:start position:0%
you may or may not be required to put in
 

00:04:35.280 --> 00:04:37.909 align:start position:0%
you may or may not be required to put in
the<00:04:35.560><c> full</c><00:04:35.880><c> email</c><00:04:36.440><c> address</c><00:04:37.440><c> sometimes</c><00:04:37.800><c> the</c>

00:04:37.909 --> 00:04:37.919 align:start position:0%
the full email address sometimes the
 

00:04:37.919 --> 00:04:40.749 align:start position:0%
the full email address sometimes the
mailbox<00:04:38.520><c> name</c><00:04:38.960><c> might</c><00:04:39.199><c> just</c><00:04:39.360><c> be</c><00:04:39.520><c> RFA</c><00:04:40.520><c> or</c>

00:04:40.749 --> 00:04:40.759 align:start position:0%
mailbox name might just be RFA or
 

00:04:40.759 --> 00:04:43.950 align:start position:0%
mailbox name might just be RFA or
whatever<00:04:41.160><c> user</c><00:04:41.520><c> you've</c><00:04:41.759><c> created</c><00:04:42.160><c> on</c>

00:04:43.950 --> 00:04:43.960 align:start position:0%
whatever user you've created on
 

00:04:43.960 --> 00:04:51.909 align:start position:0%
whatever user you've created on
there<00:04:44.960><c> so</c><00:04:45.199><c> starting</c><00:04:45.520><c> up</c><00:04:45.680><c> my</c>

00:04:51.909 --> 00:04:51.919 align:start position:0%
 
 

00:04:51.919 --> 00:04:54.270 align:start position:0%
 
Gateway<00:04:52.919><c> and</c><00:04:53.120><c> but</c><00:04:53.320><c> still</c><00:04:53.520><c> not</c><00:04:53.759><c> listed</c><00:04:54.120><c> there</c>

00:04:54.270 --> 00:04:54.280 align:start position:0%
Gateway and but still not listed there
 

00:04:54.280 --> 00:04:56.990 align:start position:0%
Gateway and but still not listed there
so<00:04:54.440><c> I</c><00:04:54.520><c> can</c><00:04:54.720><c> close</c><00:04:55.080><c> Enterprise</c><00:04:55.560><c> fax</c><00:04:56.000><c> manager</c>

00:04:56.990 --> 00:04:57.000 align:start position:0%
so I can close Enterprise fax manager
 

00:04:57.000 --> 00:04:58.629 align:start position:0%
so I can close Enterprise fax manager
and<00:04:57.120><c> start</c><00:04:57.400><c> it</c><00:04:57.600><c> up</c>

00:04:58.629 --> 00:04:58.639 align:start position:0%
and start it up
 

00:04:58.639 --> 00:05:00.230 align:start position:0%
and start it up
again

00:05:00.230 --> 00:05:00.240 align:start position:0%
again
 

00:05:00.240 --> 00:05:06.950 align:start position:0%
again
and<00:05:00.600><c> uh</c><00:05:00.720><c> it</c><00:05:00.800><c> should</c><00:05:01.000><c> be</c><00:05:01.240><c> there</c><00:05:01.479><c> this</c>

00:05:06.950 --> 00:05:06.960 align:start position:0%
 
 

00:05:06.960 --> 00:05:09.230 align:start position:0%
 
time<00:05:07.960><c> so</c><00:05:08.199><c> there</c><00:05:08.320><c> it</c><00:05:08.440><c> is</c><00:05:08.720><c> I'm</c><00:05:08.840><c> going</c><00:05:08.919><c> to</c><00:05:09.039><c> start</c>

00:05:09.230 --> 00:05:09.240 align:start position:0%
time so there it is I'm going to start
 

00:05:09.240 --> 00:05:12.830 align:start position:0%
time so there it is I'm going to start
up<00:05:09.440><c> the</c>

00:05:12.830 --> 00:05:12.840 align:start position:0%
 
 

00:05:12.840 --> 00:05:16.029 align:start position:0%
 
module<00:05:13.840><c> and</c><00:05:13.919><c> it</c><00:05:14.120><c> started</c><00:05:14.800><c> now</c>

00:05:16.029 --> 00:05:16.039 align:start position:0%
module and it started now
 

00:05:16.039 --> 00:05:20.510 align:start position:0%
module and it started now
now<00:05:17.039><c> and</c><00:05:17.120><c> I'm</c><00:05:17.320><c> going</c><00:05:17.600><c> into</c><00:05:18.560><c> Outlook</c><00:05:19.520><c> Express</c>

00:05:20.510 --> 00:05:20.520 align:start position:0%
now and I'm going into Outlook Express
 

00:05:20.520 --> 00:05:24.550 align:start position:0%
now and I'm going into Outlook Express
and<00:05:20.680><c> sending</c><00:05:21.360><c> a</c><00:05:21.800><c> email</c><00:05:22.800><c> addressed</c><00:05:23.240><c> as</c><00:05:23.360><c> a</c><00:05:23.680><c> facts</c>

00:05:24.550 --> 00:05:24.560 align:start position:0%
and sending a email addressed as a facts
 

00:05:24.560 --> 00:05:27.070 align:start position:0%
and sending a email addressed as a facts
so<00:05:24.759><c> it's</c><00:05:24.919><c> got</c><00:05:25.199><c> this</c><00:05:25.680><c> uh</c>

00:05:27.070 --> 00:05:27.080 align:start position:0%
so it's got this uh
 

00:05:27.080 --> 00:05:30.790 align:start position:0%
so it's got this uh
weird<00:05:28.080><c> uh</c><00:05:28.960><c> addressing</c><00:05:29.400><c> SC</c><00:05:29.759><c> game</c><00:05:30.479><c> but</c><00:05:30.639><c> I'm</c>

00:05:30.790 --> 00:05:30.800 align:start position:0%
weird uh addressing SC game but I'm
 

00:05:30.800 --> 00:05:33.710 align:start position:0%
weird uh addressing SC game but I'm
sending<00:05:31.160><c> the</c><00:05:31.360><c> email</c><00:05:31.840><c> actually</c><00:05:32.120><c> to</c><00:05:32.360><c> RFA</c><00:05:33.280><c> SM</c>

00:05:33.710 --> 00:05:33.720 align:start position:0%
sending the email actually to RFA SM
 

00:05:33.720 --> 00:05:44.950 align:start position:0%
sending the email actually to RFA SM
smtp.at

00:05:44.950 --> 00:05:44.960 align:start position:0%
 
 

00:05:44.960 --> 00:05:47.510 align:start position:0%
 
loal<00:05:45.960><c> and</c><00:05:46.400><c> uh</c><00:05:46.680><c> everything</c><00:05:46.960><c> that</c><00:05:47.080><c> goes</c><00:05:47.280><c> in</c><00:05:47.360><c> the</c>

00:05:47.510 --> 00:05:47.520 align:start position:0%
loal and uh everything that goes in the
 

00:05:47.520 --> 00:05:50.990 align:start position:0%
loal and uh everything that goes in the
subject<00:05:47.880><c> line</c><00:05:48.400><c> shows</c><00:05:48.759><c> up</c><00:05:48.919><c> in</c><00:05:49.440><c> the</c><00:05:50.440><c> uh</c><00:05:50.720><c> cover</c>

00:05:50.990 --> 00:05:51.000 align:start position:0%
subject line shows up in the uh cover
 

00:05:51.000 --> 00:05:53.350 align:start position:0%
subject line shows up in the uh cover
sheet<00:05:51.199><c> notes</c><00:05:51.919><c> and</c><00:05:52.080><c> everything</c><00:05:52.360><c> in</c><00:05:52.479><c> the</c><00:05:52.639><c> body</c>

00:05:53.350 --> 00:05:53.360 align:start position:0%
sheet notes and everything in the body
 

00:05:53.360 --> 00:05:56.309 align:start position:0%
sheet notes and everything in the body
shows<00:05:53.800><c> up</c><00:05:53.960><c> on</c><00:05:54.080><c> the</c><00:05:54.240><c> first</c><00:05:54.560><c> page</c><00:05:54.720><c> of</c><00:05:54.880><c> the</c><00:05:55.319><c> facts</c>

00:05:56.309 --> 00:05:56.319 align:start position:0%
shows up on the first page of the facts
 

00:05:56.319 --> 00:05:59.350 align:start position:0%
shows up on the first page of the facts
any<00:05:56.919><c> attachments</c><00:05:57.919><c> will</c><00:05:58.120><c> show</c><00:05:58.360><c> up</c><00:05:58.600><c> as</c><00:05:58.840><c> the</c><00:05:59.199><c> uh</c>

00:05:59.350 --> 00:05:59.360 align:start position:0%
any attachments will show up as the uh
 

00:05:59.360 --> 00:06:02.670 align:start position:0%
any attachments will show up as the uh
yeah<00:05:59.759><c> second</c><00:06:00.160><c> third</c><00:06:00.520><c> fourth</c><00:06:00.919><c> and</c><00:06:01.080><c> so</c><00:06:01.319><c> forth</c><00:06:01.880><c> uh</c>

00:06:02.670 --> 00:06:02.680 align:start position:0%
yeah second third fourth and so forth uh
 

00:06:02.680 --> 00:06:07.710 align:start position:0%
yeah second third fourth and so forth uh
um<00:06:02.919><c> files</c><00:06:03.479><c> or</c><00:06:03.800><c> pages</c><00:06:04.479><c> in</c><00:06:04.639><c> my</c>

00:06:07.710 --> 00:06:07.720 align:start position:0%
 
 

00:06:07.720 --> 00:06:14.309 align:start position:0%
 
facts<00:06:08.720><c> I'll</c><00:06:08.919><c> click</c>

00:06:14.309 --> 00:06:14.319 align:start position:0%
 
 

00:06:14.319 --> 00:06:17.070 align:start position:0%
 
Send<00:06:15.319><c> I</c><00:06:15.720><c> clicked</c><00:06:16.000><c> send</c><00:06:16.280><c> receive</c><00:06:16.680><c> just</c><00:06:16.759><c> to</c><00:06:16.919><c> make</c>

00:06:17.070 --> 00:06:17.080 align:start position:0%
Send I clicked send receive just to make
 

00:06:17.080 --> 00:06:19.430 align:start position:0%
Send I clicked send receive just to make
sure<00:06:17.360><c> that</c><00:06:17.479><c> it</c><00:06:17.639><c> really</c><00:06:17.880><c> did</c><00:06:18.120><c> go</c><00:06:18.680><c> from</c><00:06:18.960><c> Outlook</c>

00:06:19.430 --> 00:06:19.440 align:start position:0%
sure that it really did go from Outlook
 

00:06:19.440 --> 00:06:22.270 align:start position:0%
sure that it really did go from Outlook
Express<00:06:20.440><c> in</c><00:06:20.639><c> fax</c><00:06:20.919><c> UIL</c><00:06:21.479><c> I</c><00:06:21.560><c> can</c><00:06:21.680><c> see</c><00:06:21.919><c> that</c><00:06:22.120><c> my</c>

00:06:22.270 --> 00:06:22.280 align:start position:0%
Express in fax UIL I can see that my
 

00:06:22.280 --> 00:06:26.589 align:start position:0%
Express in fax UIL I can see that my
facts<00:06:22.680><c> sent</c><00:06:23.400><c> and</c><00:06:23.680><c> I've</c><00:06:24.080><c> received</c>

00:06:26.589 --> 00:06:26.599 align:start position:0%
 
 

00:06:26.599 --> 00:06:29.270 align:start position:0%
 
it<00:06:27.599><c> now</c><00:06:27.840><c> ignore</c><00:06:28.240><c> the</c><00:06:28.440><c> cover</c><00:06:28.720><c> sheet</c><00:06:28.880><c> notes</c><00:06:29.120><c> for</c>

00:06:29.270 --> 00:06:29.280 align:start position:0%
it now ignore the cover sheet notes for
 

00:06:29.280 --> 00:06:30.749 align:start position:0%
it now ignore the cover sheet notes for
now<00:06:29.639><c> I'm</c><00:06:29.720><c> going</c><00:06:29.840><c> to</c><00:06:29.960><c> look</c><00:06:30.120><c> through</c><00:06:30.440><c> the</c><00:06:30.599><c> rest</c>

00:06:30.749 --> 00:06:30.759 align:start position:0%
now I'm going to look through the rest
 

00:06:30.759 --> 00:06:36.710 align:start position:0%
now I'm going to look through the rest
of<00:06:30.880><c> the</c><00:06:31.039><c> facts</c><00:06:31.840><c> let</c><00:06:31.919><c> me</c><00:06:32.080><c> zoom</c><00:06:32.360><c> in</c><00:06:32.520><c> a</c>

00:06:36.710 --> 00:06:36.720 align:start position:0%
 
 

00:06:36.720 --> 00:06:40.070 align:start position:0%
 
bit<00:06:37.720><c> so</c><00:06:37.960><c> there's</c><00:06:38.240><c> the</c><00:06:38.840><c> um</c><00:06:39.000><c> cover</c><00:06:39.280><c> sheet</c><00:06:39.680><c> or</c><00:06:39.800><c> sry</c>

00:06:40.070 --> 00:06:40.080 align:start position:0%
bit so there's the um cover sheet or sry
 

00:06:40.080 --> 00:06:42.670 align:start position:0%
bit so there's the um cover sheet or sry
the<00:06:40.360><c> body</c><00:06:41.240><c> and</c><00:06:41.479><c> the</c>

00:06:42.670 --> 00:06:42.680 align:start position:0%
the body and the
 

00:06:42.680 --> 00:06:44.790 align:start position:0%
the body and the
attachment<00:06:43.680><c> now</c><00:06:43.960><c> take</c><00:06:44.120><c> a</c><00:06:44.240><c> look</c><00:06:44.360><c> at</c><00:06:44.440><c> the</c><00:06:44.560><c> cover</c>

00:06:44.790 --> 00:06:44.800 align:start position:0%
attachment now take a look at the cover
 

00:06:44.800 --> 00:06:49.110 align:start position:0%
attachment now take a look at the cover
sheet<00:06:45.160><c> notes</c><00:06:46.160><c> not</c><00:06:46.960><c> exactly</c>

00:06:49.110 --> 00:06:49.120 align:start position:0%
sheet notes not exactly
 

00:06:49.120 --> 00:06:51.909 align:start position:0%
sheet notes not exactly
pretty<00:06:50.120><c> cuz</c><00:06:50.280><c> I</c><00:06:50.440><c> got</c><00:06:50.680><c> that</c><00:06:51.120><c> um</c><00:06:51.319><c> subject</c><00:06:51.639><c> line</c>

00:06:51.909 --> 00:06:51.919 align:start position:0%
pretty cuz I got that um subject line
 

00:06:51.919 --> 00:06:53.749 align:start position:0%
pretty cuz I got that um subject line
then<00:06:52.039><c> I've</c><00:06:52.199><c> got</c><00:06:52.319><c> all</c><00:06:52.520><c> this</c><00:06:52.680><c> Header</c>

00:06:53.749 --> 00:06:53.759 align:start position:0%
then I've got all this Header
 

00:06:53.759 --> 00:06:55.629 align:start position:0%
then I've got all this Header
information<00:06:54.759><c> I</c><00:06:54.840><c> don't</c><00:06:55.080><c> like</c><00:06:55.280><c> that</c><00:06:55.440><c> so</c><00:06:55.560><c> I'm</c>

00:06:55.629 --> 00:06:55.639 align:start position:0%
information I don't like that so I'm
 

00:06:55.639 --> 00:06:57.710 align:start position:0%
information I don't like that so I'm
going<00:06:55.759><c> to</c><00:06:55.840><c> get</c><00:06:56.000><c> rid</c><00:06:56.120><c> of</c><00:06:56.319><c> that</c><00:06:57.199><c> I'm</c><00:06:57.280><c> going</c><00:06:57.360><c> to</c><00:06:57.520><c> go</c>

00:06:57.710 --> 00:06:57.720 align:start position:0%
going to get rid of that I'm going to go
 

00:06:57.720 --> 00:07:00.550 align:start position:0%
going to get rid of that I'm going to go
back<00:06:57.879><c> to</c><00:06:58.280><c> Enterprise</c><00:06:58.800><c> fact</c><00:06:59.039><c> manager</c>

00:07:00.550 --> 00:07:00.560 align:start position:0%
back to Enterprise fact manager
 

00:07:00.560 --> 00:07:07.469 align:start position:0%
back to Enterprise fact manager
and<00:07:00.800><c> the</c><00:07:01.000><c> configuration</c><00:07:01.919><c> for</c><00:07:02.879><c> the</c><00:07:03.039><c> email</c>

00:07:07.469 --> 00:07:07.479 align:start position:0%
 
 

00:07:07.479 --> 00:07:10.469 align:start position:0%
 
Gateway<00:07:08.479><c> I'm</c><00:07:08.599><c> going</c><00:07:08.680><c> to</c><00:07:08.800><c> turn</c><00:07:09.039><c> on</c><00:07:09.599><c> the</c><00:07:09.960><c> remove</c>

00:07:10.469 --> 00:07:10.479 align:start position:0%
Gateway I'm going to turn on the remove
 

00:07:10.479 --> 00:07:16.270 align:start position:0%
Gateway I'm going to turn on the remove
mail<00:07:10.879><c> headers</c><00:07:11.840><c> feature</c><00:07:12.560><c> or</c>

00:07:16.270 --> 00:07:16.280 align:start position:0%
 
 

00:07:16.280 --> 00:07:18.790 align:start position:0%
 
function<00:07:17.280><c> and</c><00:07:17.599><c> switch</c><00:07:17.960><c> over</c><00:07:18.160><c> to</c><00:07:18.360><c> Outlook</c>

00:07:18.790 --> 00:07:18.800 align:start position:0%
function and switch over to Outlook
 

00:07:18.800 --> 00:07:22.469 align:start position:0%
function and switch over to Outlook
Express<00:07:19.599><c> and</c><00:07:19.879><c> send</c><00:07:20.360><c> that</c><00:07:20.599><c> fax</c><00:07:21.080><c> again</c><00:07:21.960><c> oh</c>

00:07:22.469 --> 00:07:22.479 align:start position:0%
Express and send that fax again oh
 

00:07:22.479 --> 00:07:24.670 align:start position:0%
Express and send that fax again oh
here's<00:07:22.759><c> a</c><00:07:23.199><c> notification</c><00:07:23.879><c> from</c><00:07:24.080><c> that</c><00:07:24.280><c> previous</c>

00:07:24.670 --> 00:07:24.680 align:start position:0%
here's a notification from that previous
 

00:07:24.680 --> 00:07:28.189 align:start position:0%
here's a notification from that previous
fax<00:07:25.039><c> that</c><00:07:25.160><c> I</c>

00:07:28.189 --> 00:07:28.199 align:start position:0%
 
 

00:07:28.199 --> 00:07:54.510 align:start position:0%
 
sent<00:07:29.199><c> close</c><00:07:29.720><c> that</c><00:07:30.160><c> and</c><00:07:30.360><c> send</c><00:07:30.639><c> a</c>

00:07:54.510 --> 00:07:54.520 align:start position:0%
 
 

00:07:54.520 --> 00:07:56.189 align:start position:0%
 
facts

00:07:56.189 --> 00:07:56.199 align:start position:0%
facts
 

00:07:56.199 --> 00:08:03.149 align:start position:0%
facts
okay<00:07:57.199><c> so</c><00:07:58.120><c> that</c><00:07:58.400><c> should</c><00:07:58.919><c> go</c><00:07:59.639><c> click</c><00:07:59.840><c> on</c>

00:08:03.149 --> 00:08:03.159 align:start position:0%
 
 

00:08:03.159 --> 00:08:08.990 align:start position:0%
 
send<00:08:04.159><c> send</c><00:08:04.520><c> receive</c><00:08:04.960><c> just</c><00:08:05.080><c> to</c><00:08:05.280><c> make</c>

00:08:08.990 --> 00:08:09.000 align:start position:0%
 
 

00:08:09.000 --> 00:08:11.469 align:start position:0%
 
sure<00:08:10.000><c> and</c><00:08:10.199><c> in</c><00:08:10.400><c> fax</c>

00:08:11.469 --> 00:08:11.479 align:start position:0%
sure and in fax
 

00:08:11.479 --> 00:08:15.909 align:start position:0%
sure and in fax
retail<00:08:12.720><c> refresh</c><00:08:13.720><c> I</c><00:08:13.800><c> can</c><00:08:13.960><c> see</c><00:08:14.159><c> it's</c><00:08:14.319><c> being</c><00:08:14.919><c> sent</c>

00:08:15.909 --> 00:08:15.919 align:start position:0%
retail refresh I can see it's being sent
 

00:08:15.919 --> 00:08:17.350 align:start position:0%
retail refresh I can see it's being sent
it's<00:08:16.159><c> been</c>

00:08:17.350 --> 00:08:17.360 align:start position:0%
it's been
 

00:08:17.360 --> 00:08:19.830 align:start position:0%
it's been
sent<00:08:18.360><c> and</c><00:08:18.520><c> I</c><00:08:18.639><c> should</c><00:08:18.840><c> see</c><00:08:19.039><c> it</c><00:08:19.159><c> show</c><00:08:19.440><c> up</c><00:08:19.560><c> in</c><00:08:19.680><c> my</c>

00:08:19.830 --> 00:08:19.840 align:start position:0%
sent and I should see it show up in my
 

00:08:19.840 --> 00:08:21.950 align:start position:0%
sent and I should see it show up in my
mailbox<00:08:20.440><c> there</c><00:08:20.560><c> it</c>

00:08:21.950 --> 00:08:21.960 align:start position:0%
mailbox there it
 

00:08:21.960 --> 00:08:25.869 align:start position:0%
mailbox there it
is<00:08:22.960><c> and</c><00:08:23.120><c> now</c><00:08:23.360><c> my</c><00:08:23.560><c> notes</c><00:08:23.960><c> text</c><00:08:24.479><c> is</c><00:08:25.000><c> uh</c><00:08:25.560><c> a</c><00:08:25.680><c> lot</c>

00:08:25.869 --> 00:08:25.879 align:start position:0%
is and now my notes text is uh a lot
 

00:08:25.879 --> 00:08:27.469 align:start position:0%
is and now my notes text is uh a lot
cleaner<00:08:26.280><c> I</c><00:08:26.360><c> don't</c><00:08:26.560><c> see</c><00:08:26.759><c> all</c><00:08:26.919><c> that</c><00:08:27.039><c> hitter</c>

00:08:27.469 --> 00:08:27.479 align:start position:0%
cleaner I don't see all that hitter
 

00:08:27.479 --> 00:08:30.029 align:start position:0%
cleaner I don't see all that hitter
information<00:08:28.440><c> which</c><00:08:28.560><c> is</c><00:08:28.800><c> great</c>

00:08:30.029 --> 00:08:30.039 align:start position:0%
information which is great
 

00:08:30.039 --> 00:08:31.869 align:start position:0%
information which is great
so<00:08:30.240><c> that's</c><00:08:30.400><c> all</c><00:08:30.599><c> that's</c><00:08:30.840><c> required</c><00:08:31.199><c> to</c><00:08:31.360><c> set</c><00:08:31.560><c> up</c>

00:08:31.869 --> 00:08:31.879 align:start position:0%
so that's all that's required to set up
 

00:08:31.879 --> 00:08:35.829 align:start position:0%
so that's all that's required to set up
SMTP<00:08:32.959><c> integration</c><00:08:33.959><c> with</c><00:08:34.560><c> kep</c>

00:08:35.829 --> 00:08:35.839 align:start position:0%
SMTP integration with kep
 

00:08:35.839 --> 00:08:38.350 align:start position:0%
SMTP integration with kep
raax<00:08:36.839><c> uh</c><00:08:37.080><c> hope</c><00:08:37.240><c> you</c><00:08:37.519><c> enjoyed</c><00:08:37.959><c> this</c><00:08:38.120><c> little</c>

00:08:38.350 --> 00:08:38.360 align:start position:0%
raax uh hope you enjoyed this little
 

00:08:38.360 --> 00:08:40.389 align:start position:0%
raax uh hope you enjoyed this little
movie<00:08:38.760><c> oh</c><00:08:39.000><c> there's</c><00:08:39.240><c> my</c>

00:08:40.389 --> 00:08:40.399 align:start position:0%
movie oh there's my
 

00:08:40.399 --> 00:08:44.350 align:start position:0%
movie oh there's my
notification<00:08:41.399><c> thanks</c><00:08:41.599><c> a</c><00:08:41.719><c> lot</c><00:08:41.880><c> for</c><00:08:42.560><c> watching</c>

00:08:44.350 --> 00:08:44.360 align:start position:0%
notification thanks a lot for watching
 

00:08:44.360 --> 00:08:47.360 align:start position:0%
notification thanks a lot for watching
bye

