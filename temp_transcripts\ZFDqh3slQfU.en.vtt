WEBVTT
Kind: captions
Language: en

00:00:00.420 --> 00:00:02.690 align:start position:0%
 
hi<00:00:00.900><c> folks</c><00:00:01.260><c> I</c><00:00:01.439><c> have</c><00:00:01.560><c> a</c><00:00:01.680><c> short</c><00:00:01.860><c> bonus</c><00:00:02.280><c> viewer</c>

00:00:02.690 --> 00:00:02.700 align:start position:0%
hi folks I have a short bonus viewer
 

00:00:02.700 --> 00:00:03.770 align:start position:0%
hi folks I have a short bonus viewer
comment<00:00:02.939><c> video</c>

00:00:03.770 --> 00:00:03.780 align:start position:0%
comment video
 

00:00:03.780 --> 00:00:05.510 align:start position:0%
comment video
Chris<00:00:04.200><c> and</c><00:00:04.440><c> I</c><00:00:04.560><c> talked</c><00:00:04.859><c> briefly</c><00:00:05.160><c> on</c><00:00:05.339><c> my</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
Chris and I talked briefly on my
 

00:00:05.520 --> 00:00:07.610 align:start position:0%
Chris and I talked briefly on my
previous<00:00:05.580><c> video</c><00:00:05.940><c> stop</c><00:00:06.420><c> writing</c><00:00:06.839><c> rust</c><00:00:07.140><c> which</c>

00:00:07.610 --> 00:00:07.620 align:start position:0%
previous video stop writing rust which
 

00:00:07.620 --> 00:00:09.169 align:start position:0%
previous video stop writing rust which
is<00:00:07.740><c> about</c><00:00:07.919><c> how</c><00:00:08.160><c> I</c><00:00:08.280><c> feel</c><00:00:08.460><c> like</c><00:00:08.580><c> I</c><00:00:08.820><c> can</c><00:00:09.000><c> finish</c>

00:00:09.169 --> 00:00:09.179 align:start position:0%
is about how I feel like I can finish
 

00:00:09.179 --> 00:00:11.330 align:start position:0%
is about how I feel like I can finish
projects<00:00:09.780><c> much</c><00:00:10.019><c> easier</c><00:00:10.260><c> in</c><00:00:10.440><c> Rust</c><00:00:10.679><c> than</c><00:00:11.099><c> in</c>

00:00:11.330 --> 00:00:11.340 align:start position:0%
projects much easier in Rust than in
 

00:00:11.340 --> 00:00:13.370 align:start position:0%
projects much easier in Rust than in
other<00:00:11.460><c> languages</c><00:00:11.820><c> I</c><00:00:12.059><c> have</c><00:00:12.179><c> tried</c><00:00:12.420><c> after</c><00:00:13.139><c> a</c>

00:00:13.370 --> 00:00:13.380 align:start position:0%
other languages I have tried after a
 

00:00:13.380 --> 00:00:15.230 align:start position:0%
other languages I have tried after a
brief<00:00:13.620><c> back</c><00:00:13.860><c> and</c><00:00:13.980><c> forth</c><00:00:14.280><c> Chris</c><00:00:14.639><c> shared</c><00:00:15.059><c> this</c>

00:00:15.230 --> 00:00:15.240 align:start position:0%
brief back and forth Chris shared this
 

00:00:15.240 --> 00:00:17.269 align:start position:0%
brief back and forth Chris shared this
about<00:00:15.540><c> his</c><00:00:15.839><c> experience</c><00:00:16.139><c> with</c><00:00:16.440><c> rust</c><00:00:16.680><c> and</c><00:00:17.160><c> I</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
about his experience with rust and I
 

00:00:17.279 --> 00:00:20.029 align:start position:0%
about his experience with rust and I
wanted<00:00:17.460><c> to</c><00:00:17.640><c> Signal</c><00:00:17.880><c> boosted</c><00:00:18.300><c> verbatim</c><00:00:19.560><c> the</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
wanted to Signal boosted verbatim the
 

00:00:20.039 --> 00:00:22.910 align:start position:0%
wanted to Signal boosted verbatim the
forms<00:00:20.400><c> that</c><00:00:20.640><c> once</c><00:00:20.939><c> are</c><00:00:21.119><c> pressed</c><00:00:21.480><c> now</c><00:00:22.260><c> in</c><00:00:22.740><c> fact</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
forms that once are pressed now in fact
 

00:00:22.920 --> 00:00:24.590 align:start position:0%
forms that once are pressed now in fact
liberate

00:00:24.590 --> 00:00:24.600 align:start position:0%
liberate
 

00:00:24.600 --> 00:00:27.349 align:start position:0%
liberate
I<00:00:25.080><c> was</c><00:00:25.260><c> and</c><00:00:25.740><c> still</c><00:00:25.980><c> am</c><00:00:26.220><c> a</c><00:00:26.699><c> big</c><00:00:26.820><c> fan</c><00:00:27.060><c> of</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
I was and still am a big fan of
 

00:00:27.359 --> 00:00:29.929 align:start position:0%
I was and still am a big fan of
languages<00:00:27.779><c> like</c><00:00:28.140><c> C</c><00:00:28.439><c> it</c><00:00:29.099><c> was</c><00:00:29.220><c> what</c><00:00:29.400><c> I</c><00:00:29.580><c> cut</c><00:00:29.699><c> my</c>

00:00:29.929 --> 00:00:29.939 align:start position:0%
languages like C it was what I cut my
 

00:00:29.939 --> 00:00:31.849 align:start position:0%
languages like C it was what I cut my
teeth<00:00:30.119><c> on</c><00:00:30.240><c> in</c><00:00:30.420><c> my</c><00:00:30.539><c> teens</c><00:00:30.900><c> and</c><00:00:31.439><c> still</c><00:00:31.619><c> enjoy</c>

00:00:31.849 --> 00:00:31.859 align:start position:0%
teeth on in my teens and still enjoy
 

00:00:31.859 --> 00:00:34.010 align:start position:0%
teeth on in my teens and still enjoy
picking<00:00:32.279><c> up</c><00:00:32.399><c> every</c><00:00:32.759><c> now</c><00:00:33.000><c> and</c><00:00:33.180><c> again</c>

00:00:34.010 --> 00:00:34.020 align:start position:0%
picking up every now and again
 

00:00:34.020 --> 00:00:36.170 align:start position:0%
picking up every now and again
when<00:00:34.500><c> writing</c><00:00:34.920><c> C</c><00:00:35.100><c> it</c><00:00:35.640><c> feels</c><00:00:35.880><c> like</c><00:00:35.940><c> I'm</c>

00:00:36.170 --> 00:00:36.180 align:start position:0%
when writing C it feels like I'm
 

00:00:36.180 --> 00:00:37.970 align:start position:0%
when writing C it feels like I'm
directly<00:00:36.600><c> working</c><00:00:36.780><c> with</c><00:00:37.079><c> the</c><00:00:37.260><c> computer</c><00:00:37.440><c> to</c>

00:00:37.970 --> 00:00:37.980 align:start position:0%
directly working with the computer to
 

00:00:37.980 --> 00:00:39.709 align:start position:0%
directly working with the computer to
build<00:00:38.100><c> up</c><00:00:38.219><c> a</c><00:00:38.399><c> baroque</c><00:00:38.640><c> machine</c><00:00:38.879><c> of</c><00:00:39.300><c> elegant</c>

00:00:39.709 --> 00:00:39.719 align:start position:0%
build up a baroque machine of elegant
 

00:00:39.719 --> 00:00:40.850 align:start position:0%
build up a baroque machine of elegant
computation

00:00:40.850 --> 00:00:40.860 align:start position:0%
computation
 

00:00:40.860 --> 00:00:42.590 align:start position:0%
computation
at<00:00:41.340><c> least</c><00:00:41.460><c> when</c><00:00:41.700><c> I'm</c><00:00:41.879><c> not</c><00:00:42.000><c> spending</c><00:00:42.300><c> half</c><00:00:42.420><c> my</c>

00:00:42.590 --> 00:00:42.600 align:start position:0%
at least when I'm not spending half my
 

00:00:42.600 --> 00:00:44.569 align:start position:0%
at least when I'm not spending half my
time<00:00:42.719><c> debugging</c><00:00:43.320><c> segvults</c><00:00:43.920><c> and</c><00:00:44.040><c> unexpected</c>

00:00:44.569 --> 00:00:44.579 align:start position:0%
time debugging segvults and unexpected
 

00:00:44.579 --> 00:00:47.270 align:start position:0%
time debugging segvults and unexpected
Behavior<00:00:45.539><c> granted</c><00:00:46.260><c> while</c><00:00:46.680><c> I</c><00:00:46.800><c> have</c><00:00:46.920><c> written</c><00:00:47.160><c> a</c>

00:00:47.270 --> 00:00:47.280 align:start position:0%
Behavior granted while I have written a
 

00:00:47.280 --> 00:00:49.549 align:start position:0%
Behavior granted while I have written a
good<00:00:47.399><c> amount</c><00:00:47.640><c> of</c><00:00:47.760><c> C</c><00:00:47.940><c> code</c><00:00:48.239><c> I</c><00:00:48.780><c> am</c><00:00:48.960><c> by</c><00:00:49.079><c> no</c><00:00:49.260><c> means</c>

00:00:49.549 --> 00:00:49.559 align:start position:0%
good amount of C code I am by no means
 

00:00:49.559 --> 00:00:51.770 align:start position:0%
good amount of C code I am by no means
great<00:00:49.860><c> at</c><00:00:50.219><c> writing</c><00:00:50.579><c> C</c><00:00:50.760><c> code</c>

00:00:51.770 --> 00:00:51.780 align:start position:0%
great at writing C code
 

00:00:51.780 --> 00:00:53.569 align:start position:0%
great at writing C code
then<00:00:52.140><c> the</c><00:00:52.379><c> search</c><00:00:52.500><c> began</c><00:00:52.860><c> for</c><00:00:53.219><c> higher</c><00:00:53.520><c> level</c>

00:00:53.569 --> 00:00:53.579 align:start position:0%
then the search began for higher level
 

00:00:53.579 --> 00:00:55.670 align:start position:0%
then the search began for higher level
languages<00:00:54.000><c> to</c><00:00:54.420><c> get</c><00:00:54.539><c> things</c><00:00:54.719><c> done</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
languages to get things done
 

00:00:55.680 --> 00:00:58.310 align:start position:0%
languages to get things done
I<00:00:55.980><c> have</c><00:00:56.100><c> used</c><00:00:56.280><c> multiple</c><00:00:56.760><c> in</c><00:00:57.120><c> my</c><00:00:57.300><c> time</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
I have used multiple in my time
 

00:00:58.320 --> 00:01:00.290 align:start position:0%
I have used multiple in my time
marked<00:00:58.800><c> around</c><00:00:58.920><c> in</c><00:00:59.160><c> Java</c><00:00:59.579><c> build</c><00:00:59.940><c> some</c><00:01:00.180><c> web</c>

00:01:00.290 --> 00:01:00.300 align:start position:0%
marked around in Java build some web
 

00:01:00.300 --> 00:01:01.910 align:start position:0%
marked around in Java build some web
apps<00:01:00.600><c> in</c><00:01:00.660><c> JavaScript</c><00:01:01.020><c> did</c><00:01:01.440><c> some</c><00:01:01.620><c> utility</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
apps in JavaScript did some utility
 

00:01:01.920 --> 00:01:04.369 align:start position:0%
apps in JavaScript did some utility
scripts<00:01:02.340><c> in</c><00:01:02.520><c> Python</c><00:01:02.940><c> tried</c><00:01:03.480><c> and</c><00:01:03.719><c> failed</c><00:01:04.080><c> to</c>

00:01:04.369 --> 00:01:04.379 align:start position:0%
scripts in Python tried and failed to
 

00:01:04.379 --> 00:01:06.170 align:start position:0%
scripts in Python tried and failed to
build<00:01:04.500><c> stable</c><00:01:04.799><c> middleware</c><00:01:05.220><c> and</c><00:01:05.519><c> Pearl</c>

00:01:06.170 --> 00:01:06.180 align:start position:0%
build stable middleware and Pearl
 

00:01:06.180 --> 00:01:08.690 align:start position:0%
build stable middleware and Pearl
written<00:01:06.780><c> some</c><00:01:06.900><c> games</c><00:01:07.140><c> in</c><00:01:07.380><c> C</c><00:01:07.500><c> plus</c><00:01:07.680><c> and</c><00:01:08.520><c> even</c>

00:01:08.690 --> 00:01:08.700 align:start position:0%
written some games in C plus and even
 

00:01:08.700 --> 00:01:11.030 align:start position:0%
written some games in C plus and even
used<00:01:08.939><c> PHP</c><00:01:09.479><c> to</c><00:01:09.780><c> do</c><00:01:09.900><c> some</c><00:01:10.080><c> back-end</c><00:01:10.380><c> work</c><00:01:10.619><c> for</c>

00:01:11.030 --> 00:01:11.040 align:start position:0%
used PHP to do some back-end work for
 

00:01:11.040 --> 00:01:13.250 align:start position:0%
used PHP to do some back-end work for
WordPress<00:01:11.460><c> sites</c><00:01:11.880><c> while</c><00:01:12.540><c> I</c><00:01:12.780><c> can</c><00:01:12.900><c> get</c><00:01:13.020><c> things</c>

00:01:13.250 --> 00:01:13.260 align:start position:0%
WordPress sites while I can get things
 

00:01:13.260 --> 00:01:15.230 align:start position:0%
WordPress sites while I can get things
done<00:01:13.500><c> in</c><00:01:13.799><c> each</c><00:01:13.979><c> and</c><00:01:14.400><c> the</c><00:01:14.520><c> process</c><00:01:14.760><c> of</c><00:01:14.939><c> learning</c>

00:01:15.230 --> 00:01:15.240 align:start position:0%
done in each and the process of learning
 

00:01:15.240 --> 00:01:17.390 align:start position:0%
done in each and the process of learning
was<00:01:15.420><c> fruitful</c><00:01:15.840><c> I</c><00:01:16.500><c> always</c><00:01:16.680><c> felt</c><00:01:17.040><c> like</c><00:01:17.100><c> I</c><00:01:17.340><c> was</c>

00:01:17.390 --> 00:01:17.400 align:start position:0%
was fruitful I always felt like I was
 

00:01:17.400 --> 00:01:19.190 align:start position:0%
was fruitful I always felt like I was
compromising<00:01:18.000><c> that</c><00:01:18.299><c> direct</c><00:01:18.479><c> relationship</c>

00:01:19.190 --> 00:01:19.200 align:start position:0%
compromising that direct relationship
 

00:01:19.200 --> 00:01:20.690 align:start position:0%
compromising that direct relationship
with<00:01:19.500><c> the</c><00:01:19.680><c> machine</c>

00:01:20.690 --> 00:01:20.700 align:start position:0%
with the machine
 

00:01:20.700 --> 00:01:22.850 align:start position:0%
with the machine
somehow<00:01:21.420><c> as</c><00:01:21.780><c> I</c><00:01:21.960><c> climbed</c><00:01:22.320><c> the</c><00:01:22.439><c> mountain</c><00:01:22.619><c> of</c>

00:01:22.850 --> 00:01:22.860 align:start position:0%
somehow as I climbed the mountain of
 

00:01:22.860 --> 00:01:24.950 align:start position:0%
somehow as I climbed the mountain of
abstraction<00:01:23.280><c> I</c><00:01:23.700><c> lost</c><00:01:23.880><c> the</c><00:01:24.119><c> sense</c><00:01:24.299><c> of</c><00:01:24.479><c> dominion</c>

00:01:24.950 --> 00:01:24.960 align:start position:0%
abstraction I lost the sense of dominion
 

00:01:24.960 --> 00:01:27.950 align:start position:0%
abstraction I lost the sense of dominion
over<00:01:25.259><c> the</c><00:01:25.560><c> processor</c><00:01:26.040><c> that</c><00:01:26.340><c> comes</c><00:01:26.700><c> with</c><00:01:26.880><c> C</c><00:01:27.180><c> I</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
over the processor that comes with C I
 

00:01:27.960 --> 00:01:29.330 align:start position:0%
over the processor that comes with C I
felt<00:01:28.259><c> like</c><00:01:28.380><c> the</c><00:01:28.560><c> abstraction</c><00:01:28.920><c> That</c><00:01:29.159><c> was</c>

00:01:29.330 --> 00:01:29.340 align:start position:0%
felt like the abstraction That was
 

00:01:29.340 --> 00:01:30.890 align:start position:0%
felt like the abstraction That was
supposed<00:01:29.460><c> to</c><00:01:29.640><c> help</c><00:01:29.820><c> me</c><00:01:30.060><c> be</c><00:01:30.240><c> more</c><00:01:30.479><c> efficient</c>

00:01:30.890 --> 00:01:30.900 align:start position:0%
supposed to help me be more efficient
 

00:01:30.900 --> 00:01:33.170 align:start position:0%
supposed to help me be more efficient
also<00:01:31.799><c> cloud</c><00:01:31.979><c> is</c><00:01:32.220><c> my</c><00:01:32.400><c> understanding</c><00:01:32.820><c> of</c><00:01:33.000><c> the</c>

00:01:33.170 --> 00:01:33.180 align:start position:0%
also cloud is my understanding of the
 

00:01:33.180 --> 00:01:35.330 align:start position:0%
also cloud is my understanding of the
threads<00:01:33.479><c> that</c><00:01:33.659><c> worked</c><00:01:33.960><c> under</c><00:01:34.079><c> my</c><00:01:34.380><c> feet</c><00:01:34.560><c> then</c>

00:01:35.330 --> 00:01:35.340 align:start position:0%
threads that worked under my feet then
 

00:01:35.340 --> 00:01:37.310 align:start position:0%
threads that worked under my feet then
one<00:01:35.579><c> day</c><00:01:35.759><c> I</c><00:01:36.119><c> finally</c><00:01:36.420><c> decided</c><00:01:36.840><c> to</c><00:01:37.020><c> pick</c><00:01:37.200><c> up</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
one day I finally decided to pick up
 

00:01:37.320 --> 00:01:39.469 align:start position:0%
one day I finally decided to pick up
rust<00:01:37.740><c> after</c><00:01:38.400><c> a</c><00:01:38.640><c> hearing</c><00:01:38.880><c> a</c><00:01:39.000><c> few</c><00:01:39.060><c> developers</c>

00:01:39.469 --> 00:01:39.479 align:start position:0%
rust after a hearing a few developers
 

00:01:39.479 --> 00:01:41.149 align:start position:0%
rust after a hearing a few developers
talk<00:01:39.659><c> about</c><00:01:39.840><c> how</c><00:01:40.079><c> it</c><00:01:40.200><c> was</c><00:01:40.380><c> the</c><00:01:40.619><c> best</c><00:01:40.799><c> thing</c>

00:01:41.149 --> 00:01:41.159 align:start position:0%
talk about how it was the best thing
 

00:01:41.159 --> 00:01:42.109 align:start position:0%
talk about how it was the best thing
ever

00:01:42.109 --> 00:01:42.119 align:start position:0%
ever
 

00:01:42.119 --> 00:01:44.510 align:start position:0%
ever
at<00:01:42.780><c> first</c><00:01:42.960><c> it</c><00:01:43.320><c> seemed</c><00:01:43.560><c> obtuse</c>

00:01:44.510 --> 00:01:44.520 align:start position:0%
at first it seemed obtuse
 

00:01:44.520 --> 00:01:46.069 align:start position:0%
at first it seemed obtuse
what<00:01:45.060><c> the</c><00:01:45.119><c> hell</c><00:01:45.299><c> are</c><00:01:45.420><c> all</c><00:01:45.540><c> these</c><00:01:45.720><c> unwrapped</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
what the hell are all these unwrapped
 

00:01:46.079 --> 00:01:48.050 align:start position:0%
what the hell are all these unwrapped
calls<00:01:46.439><c> why</c><00:01:47.159><c> do</c><00:01:47.340><c> they</c><00:01:47.460><c> throw</c><00:01:47.579><c> these</c><00:01:47.880><c> question</c>

00:01:48.050 --> 00:01:48.060 align:start position:0%
calls why do they throw these question
 

00:01:48.060 --> 00:01:49.850 align:start position:0%
calls why do they throw these question
marks<00:01:48.479><c> randomly</c><00:01:48.899><c> at</c><00:01:49.020><c> the</c><00:01:49.140><c> end</c><00:01:49.200><c> of</c><00:01:49.380><c> statements</c>

00:01:49.850 --> 00:01:49.860 align:start position:0%
marks randomly at the end of statements
 

00:01:49.860 --> 00:01:52.490 align:start position:0%
marks randomly at the end of statements
does<00:01:50.640><c> it</c><00:01:50.880><c> always</c><00:01:51.060><c> take</c><00:01:51.360><c> five</c><00:01:51.720><c> lines</c><00:01:52.020><c> of</c><00:01:52.200><c> error</c>

00:01:52.490 --> 00:01:52.500 align:start position:0%
does it always take five lines of error
 

00:01:52.500 --> 00:01:54.350 align:start position:0%
does it always take five lines of error
checking<00:01:52.860><c> for</c><00:01:53.100><c> one</c><00:01:53.340><c> line</c><00:01:53.520><c> of</c><00:01:53.700><c> working</c><00:01:53.939><c> code</c>

00:01:54.350 --> 00:01:54.360 align:start position:0%
checking for one line of working code
 

00:01:54.360 --> 00:01:56.810 align:start position:0%
checking for one line of working code
but<00:01:55.200><c> I</c><00:01:55.380><c> decided</c><00:01:55.740><c> to</c><00:01:55.860><c> give</c><00:01:55.979><c> the</c><00:01:56.219><c> language</c><00:01:56.399><c> an</c>

00:01:56.810 --> 00:01:56.820 align:start position:0%
but I decided to give the language an
 

00:01:56.820 --> 00:01:59.510 align:start position:0%
but I decided to give the language an
honest<00:01:57.119><c> shake</c><00:01:57.600><c> and</c><00:01:57.960><c> stick</c><00:01:58.140><c> with</c><00:01:58.320><c> it</c><00:01:58.500><c> over</c><00:01:59.159><c> time</c>

00:01:59.510 --> 00:01:59.520 align:start position:0%
honest shake and stick with it over time
 

00:01:59.520 --> 00:02:01.609 align:start position:0%
honest shake and stick with it over time
I<00:02:00.000><c> began</c><00:02:00.240><c> to</c><00:02:00.420><c> appreciate</c><00:02:00.840><c> that</c><00:02:01.140><c> Naggy</c>

00:02:01.609 --> 00:02:01.619 align:start position:0%
I began to appreciate that Naggy
 

00:02:01.619 --> 00:02:03.410 align:start position:0%
I began to appreciate that Naggy
compiler<00:02:02.159><c> was</c><00:02:02.460><c> actually</c><00:02:02.640><c> guiding</c><00:02:03.060><c> me</c><00:02:03.180><c> to</c>

00:02:03.410 --> 00:02:03.420 align:start position:0%
compiler was actually guiding me to
 

00:02:03.420 --> 00:02:05.690 align:start position:0%
compiler was actually guiding me to
consider<00:02:03.780><c> all</c><00:02:04.079><c> the</c><00:02:04.200><c> code</c><00:02:04.380><c> paths</c><00:02:04.920><c> and</c><00:02:05.460><c> think</c>

00:02:05.690 --> 00:02:05.700 align:start position:0%
consider all the code paths and think
 

00:02:05.700 --> 00:02:07.429 align:start position:0%
consider all the code paths and think
about<00:02:05.820><c> the</c><00:02:06.119><c> entire</c><00:02:06.360><c> possibility</c><00:02:06.840><c> space</c><00:02:07.200><c> that</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
about the entire possibility space that
 

00:02:07.439 --> 00:02:09.830 align:start position:0%
about the entire possibility space that
I<00:02:07.619><c> was</c><00:02:07.740><c> working</c><00:02:07.979><c> in</c><00:02:08.340><c> the</c><00:02:09.119><c> serpentine</c><00:02:09.599><c> borrow</c>

00:02:09.830 --> 00:02:09.840 align:start position:0%
I was working in the serpentine borrow
 

00:02:09.840 --> 00:02:11.570 align:start position:0%
I was working in the serpentine borrow
Checker<00:02:10.379><c> that</c><00:02:10.560><c> seemed</c><00:02:10.800><c> to</c><00:02:10.860><c> try</c><00:02:11.039><c> and</c><00:02:11.099><c> bite</c><00:02:11.400><c> me</c>

00:02:11.570 --> 00:02:11.580 align:start position:0%
Checker that seemed to try and bite me
 

00:02:11.580 --> 00:02:13.790 align:start position:0%
Checker that seemed to try and bite me
every<00:02:11.819><c> time</c><00:02:12.000><c> I</c><00:02:12.180><c> made</c><00:02:12.300><c> a</c><00:02:12.540><c> change</c><00:02:12.800><c> developed</c>

00:02:13.790 --> 00:02:13.800 align:start position:0%
every time I made a change developed
 

00:02:13.800 --> 00:02:15.830 align:start position:0%
every time I made a change developed
into<00:02:13.920><c> it</c><00:02:14.160><c> being</c><00:02:14.280><c> a</c><00:02:14.580><c> great</c><00:02:14.700><c> partner</c><00:02:15.239><c> that</c>

00:02:15.830 --> 00:02:15.840 align:start position:0%
into it being a great partner that
 

00:02:15.840 --> 00:02:17.690 align:start position:0%
into it being a great partner that
helped<00:02:16.140><c> me</c><00:02:16.260><c> keep</c><00:02:16.440><c> track</c><00:02:16.560><c> of</c><00:02:16.739><c> who</c><00:02:16.980><c> owns</c><00:02:17.520><c> what</c>

00:02:17.690 --> 00:02:17.700 align:start position:0%
helped me keep track of who owns what
 

00:02:17.700 --> 00:02:19.630 align:start position:0%
helped me keep track of who owns what
this<00:02:18.540><c> allowed</c><00:02:18.900><c> me</c><00:02:19.020><c> to</c><00:02:19.200><c> approach</c>

00:02:19.630 --> 00:02:19.640 align:start position:0%
this allowed me to approach
 

00:02:19.640 --> 00:02:21.290 align:start position:0%
this allowed me to approach
complexity-laden<00:02:20.640><c> multi-threaded</c>

00:02:21.290 --> 00:02:21.300 align:start position:0%
complexity-laden multi-threaded
 

00:02:21.300 --> 00:02:23.150 align:start position:0%
complexity-laden multi-threaded
programming<00:02:21.720><c> confident</c><00:02:22.440><c> that</c><00:02:22.800><c> I</c><00:02:22.920><c> would</c><00:02:23.040><c> not</c>

00:02:23.150 --> 00:02:23.160 align:start position:0%
programming confident that I would not
 

00:02:23.160 --> 00:02:24.589 align:start position:0%
programming confident that I would not
get<00:02:23.340><c> into</c><00:02:23.459><c> a</c><00:02:23.700><c> tangled</c><00:02:24.000><c> knot</c><00:02:24.300><c> of</c><00:02:24.420><c> race</c>

00:02:24.589 --> 00:02:24.599 align:start position:0%
get into a tangled knot of race
 

00:02:24.599 --> 00:02:27.050 align:start position:0%
get into a tangled knot of race
conditions<00:02:25.260><c> the</c><00:02:25.980><c> proof</c><00:02:26.280><c> came</c><00:02:26.400><c> to</c><00:02:26.580><c> me</c><00:02:26.700><c> when</c><00:02:26.940><c> I</c>

00:02:27.050 --> 00:02:27.060 align:start position:0%
conditions the proof came to me when I
 

00:02:27.060 --> 00:02:28.490 align:start position:0%
conditions the proof came to me when I
had<00:02:27.180><c> the</c><00:02:27.300><c> opportunity</c><00:02:27.540><c> to</c><00:02:27.900><c> write</c><00:02:28.020><c> a</c><00:02:28.200><c> piece</c><00:02:28.379><c> of</c>

00:02:28.490 --> 00:02:28.500 align:start position:0%
had the opportunity to write a piece of
 

00:02:28.500 --> 00:02:30.110 align:start position:0%
had the opportunity to write a piece of
middleware<00:02:28.860><c> at</c><00:02:29.040><c> work</c><00:02:29.220><c> that</c><00:02:29.640><c> helped</c><00:02:29.940><c> a</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
middleware at work that helped a
 

00:02:30.120 --> 00:02:31.729 align:start position:0%
middleware at work that helped a
front-end<00:02:30.420><c> web</c><00:02:30.660><c> application</c><00:02:31.140><c> communicate</c>

00:02:31.729 --> 00:02:31.739 align:start position:0%
front-end web application communicate
 

00:02:31.739 --> 00:02:34.070 align:start position:0%
front-end web application communicate
with<00:02:31.920><c> back-end</c><00:02:32.340><c> servers</c><00:02:32.760><c> and</c><00:02:32.879><c> databases</c><00:02:33.420><c> I</c>

00:02:34.070 --> 00:02:34.080 align:start position:0%
with back-end servers and databases I
 

00:02:34.080 --> 00:02:36.710 align:start position:0%
with back-end servers and databases I
put<00:02:34.260><c> together</c><00:02:34.440><c> Tokyo</c><00:02:35.160><c> Architects</c><00:02:35.879><c> tybers</c><00:02:36.420><c> and</c>

00:02:36.710 --> 00:02:36.720 align:start position:0%
put together Tokyo Architects tybers and
 

00:02:36.720 --> 00:02:39.110 align:start position:0%
put together Tokyo Architects tybers and
request<00:02:37.140><c> salted</c><00:02:38.040><c> in</c><00:02:38.160><c> some</c><00:02:38.280><c> mutexes</c><00:02:38.879><c> and</c>

00:02:39.110 --> 00:02:39.120 align:start position:0%
request salted in some mutexes and
 

00:02:39.120 --> 00:02:41.030 align:start position:0%
request salted in some mutexes and
rewrite<00:02:39.480><c> locks</c><00:02:39.959><c> then</c><00:02:40.440><c> put</c><00:02:40.680><c> the</c><00:02:40.860><c> thing</c>

00:02:41.030 --> 00:02:41.040 align:start position:0%
rewrite locks then put the thing
 

00:02:41.040 --> 00:02:43.430 align:start position:0%
rewrite locks then put the thing
together<00:02:41.340><c> the</c><00:02:42.120><c> first</c><00:02:42.239><c> build</c><00:02:42.540><c> after</c><00:02:42.959><c> a</c><00:02:43.200><c> bit</c><00:02:43.319><c> of</c>

00:02:43.430 --> 00:02:43.440 align:start position:0%
together the first build after a bit of
 

00:02:43.440 --> 00:02:45.410 align:start position:0%
together the first build after a bit of
tweaking<00:02:43.680><c> bran</c><00:02:44.280><c> can</c><00:02:44.459><c> continuously</c><00:02:45.120><c> for</c><00:02:45.300><c> a</c>

00:02:45.410 --> 00:02:45.420 align:start position:0%
tweaking bran can continuously for a
 

00:02:45.420 --> 00:02:47.270 align:start position:0%
tweaking bran can continuously for a
month<00:02:45.599><c> on</c><00:02:46.140><c> a</c><00:02:46.260><c> virtual</c><00:02:46.500><c> machine</c><00:02:46.680><c> in</c><00:02:46.920><c> production</c>

00:02:47.270 --> 00:02:47.280 align:start position:0%
month on a virtual machine in production
 

00:02:47.280 --> 00:02:49.610 align:start position:0%
month on a virtual machine in production
with<00:02:47.700><c> hardly</c><00:02:48.000><c> an</c><00:02:48.180><c> error</c><00:02:48.480><c> all</c><00:02:48.959><c> around</c><00:02:49.140><c> it</c><00:02:49.319><c> the</c>

00:02:49.610 --> 00:02:49.620 align:start position:0%
with hardly an error all around it the
 

00:02:49.620 --> 00:02:50.990 align:start position:0%
with hardly an error all around it the
infrastructure<00:02:50.160><c> it</c><00:02:50.280><c> connected</c><00:02:50.640><c> would</c><00:02:50.879><c> be</c>

00:02:50.990 --> 00:02:51.000 align:start position:0%
infrastructure it connected would be
 

00:02:51.000 --> 00:02:53.030 align:start position:0%
infrastructure it connected would be
rebooted<00:02:51.660><c> network</c><00:02:52.140><c> connectivity</c><00:02:52.800><c> would</c>

00:02:53.030 --> 00:02:53.040 align:start position:0%
rebooted network connectivity would
 

00:02:53.040 --> 00:02:55.490 align:start position:0%
rebooted network connectivity would
break<00:02:53.160><c> databases</c><00:02:53.879><c> would</c><00:02:54.060><c> have</c><00:02:54.180><c> lag</c><00:02:54.720><c> Etc</c><00:02:54.900><c> but</c>

00:02:55.490 --> 00:02:55.500 align:start position:0%
break databases would have lag Etc but
 

00:02:55.500 --> 00:02:57.290 align:start position:0%
break databases would have lag Etc but
the<00:02:55.680><c> middleware</c><00:02:56.040><c> just</c><00:02:56.400><c> quietly</c><00:02:56.819><c> worked</c><00:02:57.180><c> in</c>

00:02:57.290 --> 00:02:57.300 align:start position:0%
the middleware just quietly worked in
 

00:02:57.300 --> 00:02:59.030 align:start position:0%
the middleware just quietly worked in
the<00:02:57.420><c> background</c><00:02:57.599><c> serving</c><00:02:58.200><c> requests</c><00:02:58.620><c> and</c>

00:02:59.030 --> 00:02:59.040 align:start position:0%
the background serving requests and
 

00:02:59.040 --> 00:03:01.250 align:start position:0%
the background serving requests and
making<00:02:59.280><c> connections</c><00:03:00.000><c> eventually</c><00:03:00.840><c> I</c><00:03:00.959><c> had</c><00:03:01.080><c> to</c>

00:03:01.250 --> 00:03:01.260 align:start position:0%
making connections eventually I had to
 

00:03:01.260 --> 00:03:02.630 align:start position:0%
making connections eventually I had to
do<00:03:01.319><c> some</c><00:03:01.500><c> debugging</c><00:03:01.920><c> because</c><00:03:02.159><c> of</c><00:03:02.340><c> a</c><00:03:02.519><c> race</c>

00:03:02.630 --> 00:03:02.640 align:start position:0%
do some debugging because of a race
 

00:03:02.640 --> 00:03:04.070 align:start position:0%
do some debugging because of a race
condition<00:03:02.879><c> with</c><00:03:03.239><c> the</c><00:03:03.420><c> database</c><00:03:03.720><c> handle</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
condition with the database handle
 

00:03:04.080 --> 00:03:05.690 align:start position:0%
condition with the database handle
between<00:03:04.200><c> parallel</c><00:03:04.739><c> threads</c><00:03:05.099><c> that</c><00:03:05.220><c> served</c><00:03:05.519><c> web</c>

00:03:05.690 --> 00:03:05.700 align:start position:0%
between parallel threads that served web
 

00:03:05.700 --> 00:03:07.910 align:start position:0%
between parallel threads that served web
requests<00:03:06.180><c> if</c><00:03:06.900><c> the</c><00:03:07.019><c> connection</c><00:03:07.140><c> timed</c><00:03:07.620><c> out</c><00:03:07.680><c> it</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
requests if the connection timed out it
 

00:03:07.920 --> 00:03:09.470 align:start position:0%
requests if the connection timed out it
would<00:03:08.040><c> die</c><00:03:08.220><c> holding</c><00:03:08.640><c> a</c><00:03:08.760><c> lock</c><00:03:08.879><c> on</c><00:03:09.060><c> the</c><00:03:09.180><c> handle</c>

00:03:09.470 --> 00:03:09.480 align:start position:0%
would die holding a lock on the handle
 

00:03:09.480 --> 00:03:11.630 align:start position:0%
would die holding a lock on the handle
and<00:03:09.720><c> the</c><00:03:09.900><c> system</c><00:03:10.019><c> would</c><00:03:10.260><c> spiral</c><00:03:10.620><c> from</c><00:03:10.860><c> there</c><00:03:11.040><c> I</c>

00:03:11.630 --> 00:03:11.640 align:start position:0%
and the system would spiral from there I
 

00:03:11.640 --> 00:03:13.670 align:start position:0%
and the system would spiral from there I
fixed<00:03:12.000><c> that</c><00:03:12.120><c> one</c><00:03:12.360><c> bug</c><00:03:12.659><c> and</c><00:03:13.080><c> then</c><00:03:13.200><c> put</c><00:03:13.379><c> it</c><00:03:13.500><c> back</c>

00:03:13.670 --> 00:03:13.680 align:start position:0%
fixed that one bug and then put it back
 

00:03:13.680 --> 00:03:15.589 align:start position:0%
fixed that one bug and then put it back
into<00:03:13.920><c> production</c><00:03:14.340><c> and</c><00:03:14.819><c> the</c><00:03:15.000><c> system</c><00:03:15.120><c> has</c><00:03:15.420><c> run</c>

00:03:15.589 --> 00:03:15.599 align:start position:0%
into production and the system has run
 

00:03:15.599 --> 00:03:17.690 align:start position:0%
into production and the system has run
continuously<00:03:16.260><c> for</c><00:03:16.560><c> six</c><00:03:16.739><c> months</c><00:03:17.159><c> without</c><00:03:17.400><c> a</c>

00:03:17.690 --> 00:03:17.700 align:start position:0%
continuously for six months without a
 

00:03:17.700 --> 00:03:19.850 align:start position:0%
continuously for six months without a
squeak<00:03:18.060><c> this</c><00:03:18.599><c> has</c><00:03:18.720><c> been</c><00:03:18.840><c> a</c><00:03:19.080><c> game</c><00:03:19.200><c> changer</c><00:03:19.680><c> for</c>

00:03:19.850 --> 00:03:19.860 align:start position:0%
squeak this has been a game changer for
 

00:03:19.860 --> 00:03:21.890 align:start position:0%
squeak this has been a game changer for
me<00:03:20.040><c> I</c><00:03:20.519><c> work</c><00:03:20.700><c> at</c><00:03:20.879><c> a</c><00:03:21.000><c> small</c><00:03:21.120><c> company</c><00:03:21.360><c> where</c><00:03:21.720><c> I'm</c>

00:03:21.890 --> 00:03:21.900 align:start position:0%
me I work at a small company where I'm
 

00:03:21.900 --> 00:03:23.869 align:start position:0%
me I work at a small company where I'm
always<00:03:22.140><c> strapped</c><00:03:22.680><c> for</c><00:03:22.860><c> time</c><00:03:23.159><c> and</c><00:03:23.519><c> choosing</c>

00:03:23.869 --> 00:03:23.879 align:start position:0%
always strapped for time and choosing
 

00:03:23.879 --> 00:03:26.449 align:start position:0%
always strapped for time and choosing
between<00:03:24.120><c> competing</c><00:03:24.720><c> projects</c><00:03:25.200><c> I</c><00:03:25.920><c> do</c><00:03:26.099><c> not</c><00:03:26.220><c> have</c>

00:03:26.449 --> 00:03:26.459 align:start position:0%
between competing projects I do not have
 

00:03:26.459 --> 00:03:28.550 align:start position:0%
between competing projects I do not have
time<00:03:26.700><c> to</c><00:03:27.120><c> run</c><00:03:27.239><c> back</c><00:03:27.480><c> and</c><00:03:27.659><c> fix</c><00:03:27.780><c> a</c><00:03:28.019><c> misbehaving</c>

00:03:28.550 --> 00:03:28.560 align:start position:0%
time to run back and fix a misbehaving
 

00:03:28.560 --> 00:03:29.990 align:start position:0%
time to run back and fix a misbehaving
piece<00:03:28.739><c> of</c><00:03:28.860><c> software</c><00:03:29.099><c> I</c><00:03:29.220><c> wrote</c><00:03:29.400><c> three</c><00:03:29.700><c> months</c>

00:03:29.990 --> 00:03:30.000 align:start position:0%
piece of software I wrote three months
 

00:03:30.000 --> 00:03:33.170 align:start position:0%
piece of software I wrote three months
ago<00:03:30.239><c> I</c><00:03:30.720><c> need</c><00:03:30.959><c> software</c><00:03:31.319><c> that</c><00:03:31.560><c> works</c><00:03:31.980><c> now</c><00:03:32.280><c> I</c>

00:03:33.170 --> 00:03:33.180 align:start position:0%
ago I need software that works now I
 

00:03:33.180 --> 00:03:34.850 align:start position:0%
ago I need software that works now I
already<00:03:33.299><c> have</c><00:03:33.599><c> my</c><00:03:33.780><c> hands</c><00:03:34.080><c> full</c><00:03:34.200><c> making</c><00:03:34.560><c> the</c>

00:03:34.850 --> 00:03:34.860 align:start position:0%
already have my hands full making the
 

00:03:34.860 --> 00:03:36.410 align:start position:0%
already have my hands full making the
software<00:03:35.099><c> written</c><00:03:35.400><c> by</c><00:03:35.519><c> other</c><00:03:35.700><c> people</c><00:03:35.940><c> behave</c>

00:03:36.410 --> 00:03:36.420 align:start position:0%
software written by other people behave
 

00:03:36.420 --> 00:03:38.750 align:start position:0%
software written by other people behave
with<00:03:36.659><c> the</c><00:03:36.840><c> software</c><00:03:37.140><c> I</c><00:03:37.379><c> have</c><00:03:37.560><c> written</c><00:03:37.860><c> rust</c>

00:03:38.750 --> 00:03:38.760 align:start position:0%
with the software I have written rust
 

00:03:38.760 --> 00:03:40.970 align:start position:0%
with the software I have written rust
has<00:03:39.120><c> been</c><00:03:39.299><c> a</c><00:03:39.540><c> godsend</c><00:03:39.959><c> that</c><00:03:40.500><c> has</c><00:03:40.620><c> allowed</c><00:03:40.920><c> me</c>

00:03:40.970 --> 00:03:40.980 align:start position:0%
has been a godsend that has allowed me
 

00:03:40.980 --> 00:03:42.949 align:start position:0%
has been a godsend that has allowed me
to<00:03:41.099><c> solve</c><00:03:41.340><c> problems</c><00:03:41.700><c> once</c><00:03:42.120><c> and</c><00:03:42.780><c> then</c>

00:03:42.949 --> 00:03:42.959 align:start position:0%
to solve problems once and then
 

00:03:42.959 --> 00:03:45.649 align:start position:0%
to solve problems once and then
confidently<00:03:43.620><c> walk</c><00:03:43.860><c> away</c><00:03:44.040><c> to</c><00:03:44.400><c> apply</c><00:03:45.060><c> my</c><00:03:45.299><c> skills</c>

00:03:45.649 --> 00:03:45.659 align:start position:0%
confidently walk away to apply my skills
 

00:03:45.659 --> 00:03:49.070 align:start position:0%
confidently walk away to apply my skills
to<00:03:45.959><c> the</c><00:03:46.200><c> next</c><00:03:46.379><c> problem</c><00:03:46.739><c> set</c><00:03:47.120><c> in</c><00:03:48.120><c> Rust</c><00:03:48.360><c> the</c>

00:03:49.070 --> 00:03:49.080 align:start position:0%
to the next problem set in Rust the
 

00:03:49.080 --> 00:03:51.850 align:start position:0%
to the next problem set in Rust the
forms<00:03:49.379><c> that</c><00:03:49.560><c> once</c><00:03:49.799><c> are</c><00:03:49.920><c> pressed</c><00:03:50.280><c> now</c><00:03:51.239><c> in</c><00:03:51.659><c> fact</c>

00:03:51.850 --> 00:03:51.860 align:start position:0%
forms that once are pressed now in fact
 

00:03:51.860 --> 00:03:54.410 align:start position:0%
forms that once are pressed now in fact
liberate<00:03:52.860><c> I</c><00:03:53.760><c> couldn't</c><00:03:53.879><c> have</c><00:03:54.060><c> said</c><00:03:54.180><c> it</c><00:03:54.299><c> better</c>

00:03:54.410 --> 00:03:54.420 align:start position:0%
liberate I couldn't have said it better
 

00:03:54.420 --> 00:03:56.780 align:start position:0%
liberate I couldn't have said it better
myself

