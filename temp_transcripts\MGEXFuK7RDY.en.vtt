WEBVTT
Kind: captions
Language: en

00:00:00.399 --> 00:00:04.230 align:start position:0%
 
this<00:00:01.120><c> is</c><00:00:01.280><c> autog</c><00:00:01.640><c> Gro</c><00:00:02.320><c> beta</c><00:00:02.639><c> version</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
this is autog Gro beta version
 

00:00:04.240 --> 00:00:06.630 align:start position:0%
this is autog Gro beta version
90210<00:00:05.240><c> because</c><00:00:05.480><c> it's</c><00:00:05.640><c> fun</c><00:00:05.839><c> to</c><00:00:06.080><c> watch</c><00:00:06.359><c> even</c><00:00:06.520><c> if</c>

00:00:06.630 --> 00:00:06.640 align:start position:0%
90210 because it's fun to watch even if
 

00:00:06.640 --> 00:00:12.829 align:start position:0%
90210 because it's fun to watch even if
you<00:00:06.759><c> won't</c><00:00:07.000><c> admit</c>

00:00:12.829 --> 00:00:12.839 align:start position:0%
 
 

00:00:12.839 --> 00:00:15.230 align:start position:0%
 
itut<00:00:13.839><c> we're</c><00:00:13.960><c> going</c><00:00:14.120><c> to</c><00:00:14.200><c> run</c><00:00:14.360><c> autog</c><00:00:14.639><c> gr</c><00:00:14.920><c> locally</c>

00:00:15.230 --> 00:00:15.240 align:start position:0%
itut we're going to run autog gr locally
 

00:00:15.240 --> 00:00:16.790 align:start position:0%
itut we're going to run autog gr locally
for<00:00:15.440><c> today's</c><00:00:15.759><c> demo</c><00:00:16.160><c> and</c><00:00:16.240><c> you'll</c><00:00:16.400><c> see</c><00:00:16.600><c> we're</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
for today's demo and you'll see we're
 

00:00:16.800 --> 00:00:19.550 align:start position:0%
for today's demo and you'll see we're
configured<00:00:17.199><c> to</c><00:00:17.320><c> use</c><00:00:17.520><c> chat</c><00:00:17.960><c> GPT</c><00:00:18.960><c> autog</c><00:00:19.279><c> Gro</c>

00:00:19.550 --> 00:00:19.560 align:start position:0%
configured to use chat GPT autog Gro
 

00:00:19.560 --> 00:00:21.189 align:start position:0%
configured to use chat GPT autog Gro
will<00:00:19.760><c> always</c><00:00:20.039><c> remind</c><00:00:20.359><c> you</c><00:00:20.560><c> which</c><00:00:20.800><c> provider</c>

00:00:21.189 --> 00:00:21.199 align:start position:0%
will always remind you which provider
 

00:00:21.199 --> 00:00:23.189 align:start position:0%
will always remind you which provider
you've<00:00:21.400><c> configured</c><00:00:21.840><c> up</c><00:00:22.039><c> here</c><00:00:22.160><c> in</c><00:00:22.279><c> the</c><00:00:22.439><c> title</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
you've configured up here in the title
 

00:00:23.199 --> 00:00:25.070 align:start position:0%
you've configured up here in the title
our<00:00:23.400><c> new</c><00:00:23.560><c> feature</c><00:00:23.840><c> eats</c><00:00:24.160><c> tokens</c><00:00:24.519><c> like</c><00:00:24.720><c> Pac-Man</c>

00:00:25.070 --> 00:00:25.080 align:start position:0%
our new feature eats tokens like Pac-Man
 

00:00:25.080 --> 00:00:27.150 align:start position:0%
our new feature eats tokens like Pac-Man
Munch's<00:00:25.560><c> dots</c><00:00:26.199><c> and</c><00:00:26.320><c> will</c><00:00:26.480><c> bump</c><00:00:26.720><c> up</c><00:00:26.880><c> against</c>

00:00:27.150 --> 00:00:27.160 align:start position:0%
Munch's dots and will bump up against
 

00:00:27.160 --> 00:00:28.830 align:start position:0%
Munch's dots and will bump up against
grock's<00:00:27.519><c> developer</c><00:00:27.920><c> limits</c><00:00:28.240><c> if</c><00:00:28.359><c> we</c><00:00:28.439><c> don't</c><00:00:28.599><c> use</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
grock's developer limits if we don't use
 

00:00:28.840 --> 00:00:32.950 align:start position:0%
grock's developer limits if we don't use
open<00:00:29.080><c> AI</c><00:00:29.480><c> instead</c><00:00:30.400><c> still</c><00:00:31.599><c> gp-40</c><00:00:32.599><c> will</c><00:00:32.759><c> only</c>

00:00:32.950 --> 00:00:32.960 align:start position:0%
open AI instead still gp-40 will only
 

00:00:32.960 --> 00:00:34.229 align:start position:0%
open AI instead still gp-40 will only
charge<00:00:33.200><c> me</c><00:00:33.360><c> a</c><00:00:33.440><c> couple</c><00:00:33.680><c> cents</c><00:00:33.920><c> for</c><00:00:34.120><c> this</c>

00:00:34.229 --> 00:00:34.239 align:start position:0%
charge me a couple cents for this
 

00:00:34.239 --> 00:00:35.990 align:start position:0%
charge me a couple cents for this
demonstration<00:00:34.960><c> and</c><00:00:35.079><c> you're</c><00:00:35.239><c> worth</c><00:00:35.520><c> every</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
demonstration and you're worth every
 

00:00:36.000 --> 00:00:39.830 align:start position:0%
demonstration and you're worth every
penny<00:00:37.000><c> yeah</c><00:00:37.559><c> both</c><00:00:37.760><c> of</c><00:00:37.920><c> them</c><00:00:38.640><c> ignore</c><00:00:39.040><c> him</c><00:00:39.680><c> this</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
penny yeah both of them ignore him this
 

00:00:39.840 --> 00:00:42.590 align:start position:0%
penny yeah both of them ignore him this
week's<00:00:40.079><c> added</c><00:00:40.360><c> feature</c><00:00:40.600><c> is</c><00:00:40.719><c> a</c><00:00:40.840><c> shiny</c><00:00:41.120><c> new</c>

00:00:42.590 --> 00:00:42.600 align:start position:0%
week's added feature is a shiny new
 

00:00:42.600 --> 00:00:46.270 align:start position:0%
week's added feature is a shiny new
checkbox<00:00:43.600><c> I</c><00:00:43.680><c> know</c><00:00:44.039><c> right</c><00:00:44.800><c> You're</c><00:00:45.280><c> Pitiful</c>

00:00:46.270 --> 00:00:46.280 align:start position:0%
checkbox I know right You're Pitiful
 

00:00:46.280 --> 00:00:48.470 align:start position:0%
checkbox I know right You're Pitiful
fortunately<00:00:46.879><c> I</c><00:00:47.000><c> have</c><00:00:47.199><c> time</c><00:00:47.399><c> to</c><00:00:47.640><c> explain</c><00:00:48.239><c> since</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
fortunately I have time to explain since
 

00:00:48.480 --> 00:00:51.910 align:start position:0%
fortunately I have time to explain since
GPT<00:00:49.000><c> runs</c><00:00:49.320><c> so</c><00:00:49.520><c> much</c><00:00:49.640><c> slower</c><00:00:50.039><c> than</c><00:00:50.719><c> grock</c><00:00:51.719><c> we've</c>

00:00:51.910 --> 00:00:51.920 align:start position:0%
GPT runs so much slower than grock we've
 

00:00:51.920 --> 00:00:53.709 align:start position:0%
GPT runs so much slower than grock we've
added<00:00:52.160><c> a</c><00:00:52.280><c> built-in</c><00:00:52.719><c> moderator</c><00:00:53.239><c> function</c><00:00:53.600><c> that</c>

00:00:53.709 --> 00:00:53.719 align:start position:0%
added a built-in moderator function that
 

00:00:53.719 --> 00:00:55.709 align:start position:0%
added a built-in moderator function that
helps<00:00:54.039><c> keep</c><00:00:54.199><c> our</c><00:00:54.399><c> AI</c><00:00:54.719><c> agents</c><00:00:55.079><c> focused</c><00:00:55.440><c> on</c><00:00:55.559><c> the</c>

00:00:55.709 --> 00:00:55.719 align:start position:0%
helps keep our AI agents focused on the
 

00:00:55.719 --> 00:00:57.549 align:start position:0%
helps keep our AI agents focused on the
goal<00:00:56.440><c> and</c><00:00:56.680><c> productive</c><00:00:57.120><c> during</c><00:00:57.359><c> our</c>

00:00:57.549 --> 00:00:57.559 align:start position:0%
goal and productive during our
 

00:00:57.559 --> 00:01:00.069 align:start position:0%
goal and productive during our
conversational<00:00:58.280><c> testing</c><00:00:59.239><c> clicking</c><00:00:59.559><c> this</c><00:00:59.719><c> new</c>

00:01:00.069 --> 00:01:00.079 align:start position:0%
conversational testing clicking this new
 

00:01:00.079 --> 00:01:02.349 align:start position:0%
conversational testing clicking this new
checkbox<00:01:00.760><c> activates</c><00:01:01.199><c> an</c><00:01:01.320><c> internal</c><00:01:01.800><c> moderator</c>

00:01:02.349 --> 00:01:02.359 align:start position:0%
checkbox activates an internal moderator
 

00:01:02.359 --> 00:01:04.789 align:start position:0%
checkbox activates an internal moderator
bot<00:01:02.960><c> who</c><00:01:03.079><c> will</c><00:01:03.280><c> analyze</c><00:01:03.760><c> the</c><00:01:03.879><c> goal</c><00:01:04.400><c> the</c><00:01:04.519><c> scope</c>

00:01:04.789 --> 00:01:04.799 align:start position:0%
bot who will analyze the goal the scope
 

00:01:04.799 --> 00:01:06.789 align:start position:0%
bot who will analyze the goal the scope
of<00:01:04.920><c> the</c><00:01:05.080><c> conversation</c><00:01:06.080><c> and</c><00:01:06.240><c> the</c><00:01:06.360><c> previous</c>

00:01:06.789 --> 00:01:06.799 align:start position:0%
of the conversation and the previous
 

00:01:06.799 --> 00:01:08.630 align:start position:0%
of the conversation and the previous
remarks<00:01:07.400><c> and</c><00:01:07.560><c> decide</c><00:01:07.960><c> which</c><00:01:08.159><c> agent</c><00:01:08.400><c> should</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
remarks and decide which agent should
 

00:01:08.640 --> 00:01:11.469 align:start position:0%
remarks and decide which agent should
speak<00:01:09.119><c> next</c><00:01:10.119><c> moderator</c><00:01:10.640><c> bot</c><00:01:10.960><c> will</c><00:01:11.119><c> give</c><00:01:11.320><c> that</c>

00:01:11.469 --> 00:01:11.479 align:start position:0%
speak next moderator bot will give that
 

00:01:11.479 --> 00:01:13.270 align:start position:0%
speak next moderator bot will give that
agent<00:01:11.840><c> Specific</c><00:01:12.280><c> Instructions</c><00:01:12.840><c> aimed</c><00:01:13.119><c> at</c>

00:01:13.270 --> 00:01:13.280 align:start position:0%
agent Specific Instructions aimed at
 

00:01:13.280 --> 00:01:15.030 align:start position:0%
agent Specific Instructions aimed at
ensuring<00:01:13.759><c> each</c><00:01:13.960><c> team</c><00:01:14.200><c> member</c><00:01:14.640><c> is</c><00:01:14.799><c> as</c>

00:01:15.030 --> 00:01:15.040 align:start position:0%
ensuring each team member is as
 

00:01:15.040 --> 00:01:17.310 align:start position:0%
ensuring each team member is as
responsive<00:01:15.520><c> and</c><00:01:15.680><c> productive</c><00:01:16.200><c> as</c><00:01:16.400><c> possible</c>

00:01:17.310 --> 00:01:17.320 align:start position:0%
responsive and productive as possible
 

00:01:17.320 --> 00:01:18.870 align:start position:0%
responsive and productive as possible
based<00:01:17.600><c> on</c><00:01:17.720><c> our</c><00:01:17.920><c> project</c><00:01:18.159><c> manager's</c><00:01:18.640><c> vision</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
based on our project manager's vision
 

00:01:18.880 --> 00:01:20.510 align:start position:0%
based on our project manager's vision
and<00:01:19.080><c> instructions</c><00:01:19.600><c> moderator</c><00:01:20.079><c> bot</c><00:01:20.360><c> has</c>

00:01:20.510 --> 00:01:20.520 align:start position:0%
and instructions moderator bot has
 

00:01:20.520 --> 00:01:22.069 align:start position:0%
and instructions moderator bot has
provided<00:01:20.840><c> our</c><00:01:21.000><c> market</c><00:01:21.280><c> research</c><00:01:21.680><c> analyst</c>

00:01:22.069 --> 00:01:22.079 align:start position:0%
provided our market research analyst
 

00:01:22.079 --> 00:01:23.590 align:start position:0%
provided our market research analyst
with<00:01:22.280><c> specific</c><00:01:22.680><c> instructions</c><00:01:23.159><c> on</c><00:01:23.320><c> how</c><00:01:23.439><c> to</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
with specific instructions on how to
 

00:01:23.600 --> 00:01:25.710 align:start position:0%
with specific instructions on how to
proceed<00:01:24.520><c> we</c><00:01:24.640><c> can</c><00:01:24.840><c> continue</c><00:01:25.200><c> testing</c><00:01:25.560><c> our</c>

00:01:25.710 --> 00:01:25.720 align:start position:0%
proceed we can continue testing our
 

00:01:25.720 --> 00:01:28.030 align:start position:0%
proceed we can continue testing our
agents<00:01:26.079><c> like</c><00:01:26.240><c> that</c><00:01:26.439><c> again</c><00:01:26.600><c> and</c><00:01:26.840><c> again</c><00:01:27.640><c> combine</c>

00:01:28.030 --> 00:01:28.040 align:start position:0%
agents like that again and again combine
 

00:01:28.040 --> 00:01:29.590 align:start position:0%
agents like that again and again combine
this<00:01:28.200><c> enhanced</c><00:01:28.640><c> prompting</c><00:01:29.079><c> technique</c><00:01:29.439><c> with</c>

00:01:29.590 --> 00:01:29.600 align:start position:0%
this enhanced prompting technique with
 

00:01:29.600 --> 00:01:30.990 align:start position:0%
this enhanced prompting technique with
consist<00:01:29.880><c> consistant</c><00:01:30.200><c> agent</c><00:01:30.560><c> definition</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
consist consistant agent definition
 

00:01:31.000 --> 00:01:33.310 align:start position:0%
consist consistant agent definition
updates<00:01:31.439><c> through</c><00:01:31.640><c> each</c><00:01:31.880><c> agent's</c><00:01:32.320><c> gear</c><00:01:32.680><c> icon</c>

00:01:33.310 --> 00:01:33.320 align:start position:0%
updates through each agent's gear icon
 

00:01:33.320 --> 00:01:34.670 align:start position:0%
updates through each agent's gear icon
and<00:01:33.439><c> you'll</c><00:01:33.600><c> be</c><00:01:33.759><c> pretty</c><00:01:34.000><c> impressed</c><00:01:34.399><c> with</c><00:01:34.520><c> the</c>

00:01:34.670 --> 00:01:34.680 align:start position:0%
and you'll be pretty impressed with the
 

00:01:34.680 --> 00:01:36.670 align:start position:0%
and you'll be pretty impressed with the
results<00:01:35.399><c> I'll</c><00:01:35.600><c> fast</c><00:01:35.880><c> forward</c><00:01:36.200><c> through</c><00:01:36.560><c> the</c>

00:01:36.670 --> 00:01:36.680 align:start position:0%
results I'll fast forward through the
 

00:01:36.680 --> 00:01:38.389 align:start position:0%
results I'll fast forward through the
rest<00:01:36.840><c> of</c><00:01:36.960><c> the</c><00:01:37.119><c> team</c><00:01:37.360><c> out</c><00:01:37.520><c> of</c><00:01:37.640><c> respect</c><00:01:38.000><c> for</c><00:01:38.159><c> your</c>

00:01:38.389 --> 00:01:38.399 align:start position:0%
rest of the team out of respect for your
 

00:01:38.399 --> 00:01:40.950 align:start position:0%
rest of the team out of respect for your
time<00:01:39.399><c> while</c><00:01:39.640><c> that's</c><00:01:39.920><c> happening</c><00:01:40.680><c> let</c><00:01:40.799><c> me</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
time while that's happening let me
 

00:01:40.960 --> 00:01:42.670 align:start position:0%
time while that's happening let me
mention<00:01:41.240><c> that</c><00:01:41.399><c> we've</c><00:01:41.600><c> also</c><00:01:41.840><c> changed</c><00:01:42.200><c> up</c><00:01:42.360><c> our</c>

00:01:42.670 --> 00:01:42.680 align:start position:0%
mention that we've also changed up our
 

00:01:42.680 --> 00:01:44.670 align:start position:0%
mention that we've also changed up our
config<00:01:43.200><c> files</c><00:01:43.600><c> so</c><00:01:43.799><c> your</c><00:01:43.960><c> settings</c><00:01:44.320><c> don't</c><00:01:44.520><c> get</c>

00:01:44.670 --> 00:01:44.680 align:start position:0%
config files so your settings don't get
 

00:01:44.680 --> 00:01:46.510 align:start position:0%
config files so your settings don't get
blown<00:01:44.960><c> away</c><00:01:45.280><c> every</c><00:01:45.479><c> time</c><00:01:45.640><c> you</c><00:01:45.880><c> update</c><00:01:46.200><c> the</c>

00:01:46.510 --> 00:01:46.520 align:start position:0%
blown away every time you update the
 

00:01:46.520 --> 00:01:49.350 align:start position:0%
blown away every time you update the
software<00:01:47.520><c> we</c><00:01:47.680><c> have</c><00:01:47.880><c> user</c><00:01:48.360><c> scruffy</c><00:01:48.840><c> Nerf</c><00:01:49.200><c> to</c>

00:01:49.350 --> 00:01:49.360 align:start position:0%
software we have user scruffy Nerf to
 

00:01:49.360 --> 00:01:51.910 align:start position:0%
software we have user scruffy Nerf to
thank<00:01:49.560><c> for</c><00:01:49.799><c> that</c><00:01:50.399><c> suggestion</c><00:01:51.399><c> a</c><00:01:51.520><c> lot</c><00:01:51.640><c> of</c><00:01:51.759><c> our</c>

00:01:51.910 --> 00:01:51.920 align:start position:0%
thank for that suggestion a lot of our
 

00:01:51.920 --> 00:01:53.630 align:start position:0%
thank for that suggestion a lot of our
upgrades<00:01:52.320><c> have</c><00:01:52.479><c> come</c><00:01:52.640><c> from</c><00:01:52.799><c> your</c><00:01:53.040><c> suggestions</c>

00:01:53.630 --> 00:01:53.640 align:start position:0%
upgrades have come from your suggestions
 

00:01:53.640 --> 00:01:56.830 align:start position:0%
upgrades have come from your suggestions
so<00:01:54.000><c> don't</c><00:01:54.200><c> hesitate</c><00:01:54.600><c> to</c><00:01:54.960><c> ask</c><00:01:55.960><c> why</c><00:01:56.520><c> are</c><00:01:56.640><c> you</c>

00:01:56.830 --> 00:01:56.840 align:start position:0%
so don't hesitate to ask why are you
 

00:01:56.840 --> 00:01:59.350 align:start position:0%
so don't hesitate to ask why are you
kissing<00:01:57.159><c> up</c><00:01:57.320><c> to</c><00:01:57.520><c> them</c><00:01:58.240><c> I</c><00:01:58.360><c> need</c><00:01:58.560><c> the</c><00:01:58.759><c> likes</c><00:01:59.240><c> and</c>

00:01:59.350 --> 00:01:59.360 align:start position:0%
kissing up to them I need the likes and
 

00:01:59.360 --> 00:02:01.109 align:start position:0%
kissing up to them I need the likes and
thanks<00:01:59.520><c> for</c><00:01:59.640><c> asking</c><00:01:59.920><c> asking</c><00:02:00.399><c> I</c><00:02:00.520><c> guess</c><00:02:00.759><c> now</c><00:02:00.960><c> is</c>

00:02:01.109 --> 00:02:01.119 align:start position:0%
thanks for asking asking I guess now is
 

00:02:01.119 --> 00:02:02.870 align:start position:0%
thanks for asking asking I guess now is
as<00:02:01.240><c> good</c><00:02:01.360><c> a</c><00:02:01.520><c> time</c><00:02:01.680><c> as</c><00:02:01.799><c> any</c><00:02:02.079><c> to</c><00:02:02.240><c> beg</c><00:02:02.479><c> everybody</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
as good a time as any to beg everybody
 

00:02:02.880 --> 00:02:05.069 align:start position:0%
as good a time as any to beg everybody
to<00:02:03.039><c> subscribe</c><00:02:03.799><c> our</c><00:02:03.920><c> streamlit</c><00:02:04.479><c> logs</c><00:02:04.799><c> show</c>

00:02:05.069 --> 00:02:05.079 align:start position:0%
to subscribe our streamlit logs show
 

00:02:05.079 --> 00:02:07.109 align:start position:0%
to subscribe our streamlit logs show
we're<00:02:05.280><c> getting</c><00:02:05.520><c> close</c><00:02:05.759><c> to</c><00:02:05.960><c> 9,000</c><00:02:06.520><c> developers</c>

00:02:07.109 --> 00:02:07.119 align:start position:0%
we're getting close to 9,000 developers
 

00:02:07.119 --> 00:02:08.869 align:start position:0%
we're getting close to 9,000 developers
who<00:02:07.240><c> have</c><00:02:07.360><c> used</c><00:02:07.640><c> autog</c><00:02:08.000><c> gr</c><00:02:08.239><c> to</c><00:02:08.360><c> make</c><00:02:08.520><c> over</c><00:02:08.720><c> a</c>

00:02:08.869 --> 00:02:08.879 align:start position:0%
who have used autog gr to make over a
 

00:02:08.879 --> 00:02:10.790 align:start position:0%
who have used autog gr to make over a
half<00:02:09.039><c> million</c><00:02:09.360><c> agents</c><00:02:10.080><c> that's</c><00:02:10.239><c> not</c><00:02:10.440><c> counting</c>

00:02:10.790 --> 00:02:10.800 align:start position:0%
half million agents that's not counting
 

00:02:10.800 --> 00:02:12.589 align:start position:0%
half million agents that's not counting
everybody<00:02:11.160><c> who</c><00:02:11.280><c> runs</c><00:02:11.480><c> it</c><00:02:11.680><c> locally</c><00:02:12.239><c> if</c><00:02:12.400><c> every</c>

00:02:12.589 --> 00:02:12.599 align:start position:0%
everybody who runs it locally if every
 

00:02:12.599 --> 00:02:14.190 align:start position:0%
everybody who runs it locally if every
one<00:02:12.720><c> of</c><00:02:12.879><c> those</c><00:02:13.040><c> developers</c><00:02:13.520><c> clicked</c><00:02:13.879><c> like</c>

00:02:14.190 --> 00:02:14.200 align:start position:0%
one of those developers clicked like
 

00:02:14.200 --> 00:02:16.150 align:start position:0%
one of those developers clicked like
subscribe<00:02:14.680><c> and</c><00:02:14.800><c> share</c><00:02:15.239><c> we</c><00:02:15.599><c> can't</c><00:02:15.760><c> listen</c><00:02:15.959><c> to</c>

00:02:16.150 --> 00:02:16.160 align:start position:0%
subscribe and share we can't listen to
 

00:02:16.160 --> 00:02:18.670 align:start position:0%
subscribe and share we can't listen to
this<00:02:16.640><c> call</c><00:02:16.840><c> me</c><00:02:17.000><c> when</c><00:02:17.160><c> you're</c><00:02:17.360><c> done</c><00:02:18.280><c> just</c><00:02:18.480><c> as</c>

00:02:18.670 --> 00:02:18.680 align:start position:0%
this call me when you're done just as
 

00:02:18.680 --> 00:02:20.949 align:start position:0%
this call me when you're done just as
well<00:02:19.200><c> our</c><00:02:19.440><c> moderator</c><00:02:19.959><c> bot</c><00:02:20.280><c> has</c><00:02:20.440><c> finished</c><00:02:20.760><c> up</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
well our moderator bot has finished up
 

00:02:20.959 --> 00:02:22.869 align:start position:0%
well our moderator bot has finished up
with<00:02:21.080><c> our</c><00:02:21.239><c> agents</c><00:02:22.160><c> I'm</c><00:02:22.280><c> going</c><00:02:22.360><c> to</c><00:02:22.480><c> run</c><00:02:22.680><c> it</c>

00:02:22.869 --> 00:02:22.879 align:start position:0%
with our agents I'm going to run it
 

00:02:22.879 --> 00:02:24.790 align:start position:0%
with our agents I'm going to run it
again<00:02:23.319><c> and</c><00:02:23.560><c> this</c><00:02:23.720><c> time</c><00:02:24.319><c> I'll</c><00:02:24.480><c> try</c><00:02:24.640><c> and</c>

00:02:24.790 --> 00:02:24.800 align:start position:0%
again and this time I'll try and
 

00:02:24.800 --> 00:02:26.550 align:start position:0%
again and this time I'll try and
remember<00:02:25.080><c> to</c><00:02:25.239><c> save</c><00:02:25.440><c> a</c><00:02:25.640><c> copy</c><00:02:25.879><c> of</c><00:02:26.040><c> the</c><00:02:26.160><c> output</c>

00:02:26.550 --> 00:02:26.560 align:start position:0%
remember to save a copy of the output
 

00:02:26.560 --> 00:02:29.309 align:start position:0%
remember to save a copy of the output
for<00:02:26.680><c> you</c><00:02:26.840><c> to</c><00:02:27.080><c> examine</c><00:02:27.480><c> as</c><00:02:27.599><c> a</c><00:02:27.800><c> PDF</c><00:02:28.760><c> a</c><00:02:28.920><c> link</c><00:02:29.120><c> will</c>

00:02:29.309 --> 00:02:29.319 align:start position:0%
for you to examine as a PDF a link will
 

00:02:29.319 --> 00:02:31.309 align:start position:0%
for you to examine as a PDF a link will
appear<00:02:29.519><c> in</c><00:02:29.599><c> the</c><00:02:29.959><c> description</c><00:02:30.959><c> isn't</c><00:02:31.200><c> that</c>

00:02:31.309 --> 00:02:31.319 align:start position:0%
appear in the description isn't that
 

00:02:31.319 --> 00:02:33.229 align:start position:0%
appear in the description isn't that
going<00:02:31.480><c> to</c><00:02:31.640><c> cost</c><00:02:31.800><c> you</c><00:02:31.959><c> 2</c><00:02:32.120><c> more</c><00:02:32.360><c> cents</c><00:02:33.040><c> they're</c>

00:02:33.229 --> 00:02:33.239 align:start position:0%
going to cost you 2 more cents they're
 

00:02:33.239 --> 00:02:35.790 align:start position:0%
going to cost you 2 more cents they're
worth<00:02:33.560><c> every</c><00:02:33.879><c> penny</c><00:02:34.800><c> hey</c><00:02:35.000><c> I</c><00:02:35.120><c> thought</c><00:02:35.360><c> you</c>

00:02:35.790 --> 00:02:35.800 align:start position:0%
worth every penny hey I thought you
 

00:02:35.800 --> 00:02:38.149 align:start position:0%
worth every penny hey I thought you
[Music]

00:02:38.149 --> 00:02:38.159 align:start position:0%
[Music]
 

00:02:38.159 --> 00:02:40.509 align:start position:0%
[Music]
left

00:02:40.509 --> 00:02:40.519 align:start position:0%
left
 

00:02:40.519 --> 00:02:48.430 align:start position:0%
left
Auto<00:02:41.519><c> auto</c><00:02:42.400><c> auto</c><00:02:43.480><c> auto</c><00:02:44.480><c> auto</c><00:02:45.040><c> auto</c><00:02:45.879><c> auto</c><00:02:47.000><c> auto</c>

00:02:48.430 --> 00:02:48.440 align:start position:0%
Auto auto auto auto auto auto auto auto
 

00:02:48.440 --> 00:02:51.130 align:start position:0%
Auto auto auto auto auto auto auto auto
aut

00:02:51.130 --> 00:02:51.140 align:start position:0%
aut
 

00:02:51.140 --> 00:02:52.309 align:start position:0%
aut
[Music]

00:02:52.309 --> 00:02:52.319 align:start position:0%
[Music]
 

00:02:52.319 --> 00:02:58.830 align:start position:0%
[Music]
Auto

00:02:58.830 --> 00:02:58.840 align:start position:0%
 
 

00:02:58.840 --> 00:03:02.000 align:start position:0%
 
a<00:02:59.879><c> oh</c>

