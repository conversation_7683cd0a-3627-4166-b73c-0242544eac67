WEBVTT
Kind: captions
Language: en

00:00:01.000 --> 00:00:02.470 align:start position:0%
 
hey<00:00:01.199><c> everyone</c><00:00:01.480><c> it's</c><00:00:01.599><c> aspr</c><00:00:02.080><c> and</c><00:00:02.200><c> today</c><00:00:02.360><c> we're</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
hey everyone it's aspr and today we're
 

00:00:02.480 --> 00:00:03.669 align:start position:0%
hey everyone it's aspr and today we're
going<00:00:02.560><c> to</c><00:00:02.679><c> be</c><00:00:02.760><c> building</c><00:00:03.000><c> an</c><00:00:03.199><c> investment</c>

00:00:03.669 --> 00:00:03.679 align:start position:0%
going to be building an investment
 

00:00:03.679 --> 00:00:05.550 align:start position:0%
going to be building an investment
researcher<00:00:04.240><c> powered</c><00:00:04.560><c> by</c><00:00:04.799><c> drop</c><00:00:05.279><c> so</c><00:00:05.400><c> our</c>

00:00:05.550 --> 00:00:05.560 align:start position:0%
researcher powered by drop so our
 

00:00:05.560 --> 00:00:06.990 align:start position:0%
researcher powered by drop so our
researcher<00:00:06.040><c> today</c><00:00:06.319><c> is</c><00:00:06.440><c> going</c><00:00:06.560><c> to</c><00:00:06.720><c> take</c><00:00:06.839><c> a</c>

00:00:06.990 --> 00:00:07.000 align:start position:0%
researcher today is going to take a
 

00:00:07.000 --> 00:00:08.830 align:start position:0%
researcher today is going to take a
company<00:00:07.399><c> and</c><00:00:07.560><c> pull</c><00:00:07.720><c> up</c><00:00:08.040><c> all</c><00:00:08.200><c> the</c><00:00:08.360><c> information</c>

00:00:08.830 --> 00:00:08.840 align:start position:0%
company and pull up all the information
 

00:00:08.840 --> 00:00:10.830 align:start position:0%
company and pull up all the information
about<00:00:09.080><c> it</c><00:00:09.360><c> including</c><00:00:09.800><c> the</c><00:00:10.000><c> financials</c><00:00:10.679><c> the</c>

00:00:10.830 --> 00:00:10.840 align:start position:0%
about it including the financials the
 

00:00:10.840 --> 00:00:13.430 align:start position:0%
about it including the financials the
news<00:00:11.200><c> the</c><00:00:11.719><c> recommendations</c><00:00:12.719><c> and</c><00:00:12.880><c> send</c><00:00:13.120><c> it</c><00:00:13.240><c> to</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
news the recommendations and send it to
 

00:00:13.440 --> 00:00:16.150 align:start position:0%
news the recommendations and send it to
llama<00:00:13.920><c> 3</c><00:00:14.639><c> that's</c><00:00:14.839><c> going</c><00:00:14.960><c> to</c><00:00:15.160><c> generate</c><00:00:15.759><c> a</c>

00:00:16.150 --> 00:00:16.160 align:start position:0%
llama 3 that's going to generate a
 

00:00:16.160 --> 00:00:18.710 align:start position:0%
llama 3 that's going to generate a
investment<00:00:16.640><c> report</c><00:00:16.920><c> for</c><00:00:17.119><c> us</c><00:00:17.880><c> this</c><00:00:18.000><c> is</c><00:00:18.240><c> what</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
investment report for us this is what
 

00:00:18.720 --> 00:00:20.310 align:start position:0%
investment report for us this is what
the<00:00:18.880><c> investment</c><00:00:19.320><c> report</c><00:00:19.680><c> looks</c><00:00:19.960><c> like</c><00:00:20.119><c> for</c>

00:00:20.310 --> 00:00:20.320 align:start position:0%
the investment report looks like for
 

00:00:20.320 --> 00:00:23.550 align:start position:0%
the investment report looks like for
meta<00:00:21.199><c> it</c><00:00:21.400><c> contains</c><00:00:21.760><c> an</c><00:00:22.320><c> overview</c><00:00:23.320><c> core</c>

00:00:23.550 --> 00:00:23.560 align:start position:0%
meta it contains an overview core
 

00:00:23.560 --> 00:00:25.910 align:start position:0%
meta it contains an overview core
metrics<00:00:24.039><c> about</c><00:00:24.240><c> the</c><00:00:24.400><c> company</c><00:00:25.279><c> it</c><00:00:25.439><c> talks</c><00:00:25.720><c> about</c>

00:00:25.910 --> 00:00:25.920 align:start position:0%
metrics about the company it talks about
 

00:00:25.920 --> 00:00:28.349 align:start position:0%
metrics about the company it talks about
the<00:00:26.119><c> financial</c><00:00:26.760><c> performance</c><00:00:27.760><c> growth</c>

00:00:28.349 --> 00:00:28.359 align:start position:0%
the financial performance growth
 

00:00:28.359 --> 00:00:31.390 align:start position:0%
the financial performance growth
prospects<00:00:29.359><c> news</c><00:00:30.119><c> and</c><00:00:30.320><c> gives</c><00:00:30.560><c> us</c><00:00:31.119><c> a</c>

00:00:31.390 --> 00:00:31.400 align:start position:0%
prospects news and gives us a
 

00:00:31.400 --> 00:00:33.709 align:start position:0%
prospects news and gives us a
recommendation<00:00:32.160><c> on</c><00:00:32.320><c> the</c><00:00:32.520><c> company</c><00:00:33.320><c> again</c><00:00:33.640><c> this</c>

00:00:33.709 --> 00:00:33.719 align:start position:0%
recommendation on the company again this
 

00:00:33.719 --> 00:00:35.990 align:start position:0%
recommendation on the company again this
isn't<00:00:34.120><c> financial</c><00:00:34.680><c> advice</c><00:00:35.399><c> this</c><00:00:35.520><c> is</c><00:00:35.680><c> just</c><00:00:35.800><c> an</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
isn't financial advice this is just an
 

00:00:36.000 --> 00:00:38.510 align:start position:0%
isn't financial advice this is just an
AI<00:00:36.320><c> application</c><00:00:37.079><c> you</c><00:00:37.239><c> can</c><00:00:37.480><c> use</c><00:00:37.760><c> to</c><00:00:38.120><c> build</c><00:00:38.360><c> your</c>

00:00:38.510 --> 00:00:38.520 align:start position:0%
AI application you can use to build your
 

00:00:38.520 --> 00:00:41.549 align:start position:0%
AI application you can use to build your
own<00:00:38.760><c> investment</c><00:00:39.480><c> researcher</c><00:00:40.480><c> this</c><00:00:40.719><c> AI</c><00:00:41.120><c> app</c><00:00:41.399><c> is</c>

00:00:41.549 --> 00:00:41.559 align:start position:0%
own investment researcher this AI app is
 

00:00:41.559 --> 00:00:43.029 align:start position:0%
own investment researcher this AI app is
under<00:00:41.800><c> the</c><00:00:41.920><c> fire</c><00:00:42.239><c> data</c><00:00:42.440><c> repo</c><00:00:42.760><c> under</c><00:00:42.920><c> a</c>

00:00:43.029 --> 00:00:43.039 align:start position:0%
under the fire data repo under a
 

00:00:43.039 --> 00:00:45.310 align:start position:0%
under the fire data repo under a
cookbook<00:00:43.600><c> so</c><00:00:44.039><c> fork</c><00:00:44.520><c> and</c><00:00:44.800><c> clone</c><00:00:45.120><c> this</c>

00:00:45.310 --> 00:00:45.320 align:start position:0%
cookbook so fork and clone this
 

00:00:45.320 --> 00:00:47.709 align:start position:0%
cookbook so fork and clone this
repository<00:00:46.320><c> and</c><00:00:46.600><c> open</c><00:00:46.840><c> it</c><00:00:47.039><c> up</c><00:00:47.199><c> in</c><00:00:47.320><c> the</c><00:00:47.480><c> code</c>

00:00:47.709 --> 00:00:47.719 align:start position:0%
repository and open it up in the code
 

00:00:47.719 --> 00:00:50.389 align:start position:0%
repository and open it up in the code
editor<00:00:48.039><c> of</c><00:00:48.199><c> your</c><00:00:48.520><c> choice</c><00:00:49.520><c> once</c><00:00:49.719><c> you</c><00:00:49.920><c> fored</c><00:00:50.160><c> and</c>

00:00:50.389 --> 00:00:50.399 align:start position:0%
editor of your choice once you fored and
 

00:00:50.399 --> 00:00:53.709 align:start position:0%
editor of your choice once you fored and
cloner<00:00:51.399><c> open</c><00:00:51.640><c> it</c><00:00:51.760><c> up</c><00:00:51.879><c> under</c><00:00:52.120><c> fi</c><00:00:52.480><c> data</c>

00:00:53.709 --> 00:00:53.719 align:start position:0%
cloner open it up under fi data
 

00:00:53.719 --> 00:00:55.750 align:start position:0%
cloner open it up under fi data
cookbooks

00:00:55.750 --> 00:00:55.760 align:start position:0%
cookbooks
 

00:00:55.760 --> 00:00:59.349 align:start position:0%
cookbooks
llms<00:00:57.000><c> Gro</c><00:00:58.000><c> investment</c><00:00:58.440><c> researcher</c><00:00:58.960><c> folder</c>

00:00:59.349 --> 00:00:59.359 align:start position:0%
llms Gro investment researcher folder
 

00:00:59.359 --> 00:01:01.310 align:start position:0%
llms Gro investment researcher folder
and<00:00:59.480><c> then</c><00:00:59.600><c> we</c><00:00:59.719><c> can</c><00:01:00.039><c> on</c><00:01:00.199><c> it</c><00:01:00.600><c> let's</c><00:01:00.760><c> do</c><00:01:00.960><c> this</c><00:01:01.079><c> step</c>

00:01:01.310 --> 00:01:01.320 align:start position:0%
and then we can on it let's do this step
 

00:01:01.320 --> 00:01:02.349 align:start position:0%
and then we can on it let's do this step
by

00:01:02.349 --> 00:01:02.359 align:start position:0%
by
 

00:01:02.359 --> 00:01:06.350 align:start position:0%
by
step<00:01:03.359><c> open</c><00:01:03.640><c> up</c><00:01:03.800><c> your</c><00:01:04.400><c> terminal</c><00:01:05.400><c> then</c><00:01:05.640><c> create</c><00:01:06.080><c> a</c>

00:01:06.350 --> 00:01:06.360 align:start position:0%
step open up your terminal then create a
 

00:01:06.360 --> 00:01:08.190 align:start position:0%
step open up your terminal then create a
python<00:01:06.720><c> virtual</c><00:01:07.119><c> environment</c><00:01:07.640><c> so</c><00:01:07.840><c> all</c><00:01:08.000><c> our</c>

00:01:08.190 --> 00:01:08.200 align:start position:0%
python virtual environment so all our
 

00:01:08.200 --> 00:01:09.789 align:start position:0%
python virtual environment so all our
dependencies<00:01:08.840><c> are</c>

00:01:09.789 --> 00:01:09.799 align:start position:0%
dependencies are
 

00:01:09.799 --> 00:01:12.830 align:start position:0%
dependencies are
isolated<00:01:10.799><c> then</c><00:01:11.280><c> install</c><00:01:11.799><c> libraries</c><00:01:12.360><c> like</c><00:01:12.520><c> f</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
isolated then install libraries like f
 

00:01:12.840 --> 00:01:14.950 align:start position:0%
isolated then install libraries like f
data<00:01:13.240><c> Gro</c><00:01:13.759><c> streamlet</c>

00:01:14.950 --> 00:01:14.960 align:start position:0%
data Gro streamlet
 

00:01:14.960 --> 00:01:19.270 align:start position:0%
data Gro streamlet
Etc<00:01:15.960><c> export</c><00:01:16.280><c> your</c><00:01:16.439><c> grock</c><00:01:16.759><c> API</c><00:01:17.240><c> key</c><00:01:18.200><c> and</c><00:01:18.920><c> run</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
Etc export your grock API key and run
 

00:01:19.280 --> 00:01:22.710 align:start position:0%
Etc export your grock API key and run
the<00:01:19.799><c> investment</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
 
 

00:01:22.720 --> 00:01:24.870 align:start position:0%
 
researcher<00:01:23.720><c> once</c><00:01:23.960><c> you</c><00:01:24.079><c> run</c><00:01:24.240><c> the</c><00:01:24.320><c> researcher</c>

00:01:24.870 --> 00:01:24.880 align:start position:0%
researcher once you run the researcher
 

00:01:24.880 --> 00:01:26.830 align:start position:0%
researcher once you run the researcher
you<00:01:24.960><c> can</c><00:01:25.159><c> give</c><00:01:25.280><c> it</c><00:01:25.400><c> a</c><00:01:25.560><c> company</c><00:01:26.000><c> stock</c><00:01:26.400><c> and</c><00:01:26.600><c> ask</c>

00:01:26.830 --> 00:01:26.840 align:start position:0%
you can give it a company stock and ask
 

00:01:26.840 --> 00:01:28.469 align:start position:0%
you can give it a company stock and ask
it<00:01:26.960><c> to</c><00:01:27.079><c> generate</c><00:01:27.360><c> a</c><00:01:27.520><c> report</c><00:01:28.079><c> let's</c><00:01:28.240><c> see</c><00:01:28.400><c> what</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
it to generate a report let's see what
 

00:01:28.479 --> 00:01:30.950 align:start position:0%
it to generate a report let's see what
it<00:01:28.600><c> does</c><00:01:28.720><c> for</c><00:01:28.920><c> NVIDIA</c><00:01:30.000><c> it</c><00:01:30.159><c> first</c><00:01:30.400><c> pulls</c><00:01:30.720><c> the</c>

00:01:30.950 --> 00:01:30.960 align:start position:0%
it does for NVIDIA it first pulls the
 

00:01:30.960 --> 00:01:32.510 align:start position:0%
it does for NVIDIA it first pulls the
company

00:01:32.510 --> 00:01:32.520 align:start position:0%
company
 

00:01:32.520 --> 00:01:35.109 align:start position:0%
company
information<00:01:33.520><c> then</c><00:01:33.720><c> pulls</c><00:01:34.280><c> realtime</c><00:01:34.799><c> news</c>

00:01:35.109 --> 00:01:35.119 align:start position:0%
information then pulls realtime news
 

00:01:35.119 --> 00:01:36.350 align:start position:0%
information then pulls realtime news
about<00:01:35.320><c> the</c>

00:01:36.350 --> 00:01:36.360 align:start position:0%
about the
 

00:01:36.360 --> 00:01:39.069 align:start position:0%
about the
company<00:01:37.360><c> guess</c><00:01:37.640><c> the</c><00:01:37.720><c> analyst</c>

00:01:39.069 --> 00:01:39.079 align:start position:0%
company guess the analyst
 

00:01:39.079 --> 00:01:41.510 align:start position:0%
company guess the analyst
recommendations<00:01:40.079><c> generates</c><00:01:40.520><c> a</c><00:01:40.759><c> draft</c><00:01:41.159><c> of</c><00:01:41.320><c> the</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
recommendations generates a draft of the
 

00:01:41.520 --> 00:01:44.030 align:start position:0%
recommendations generates a draft of the
report<00:01:42.399><c> and</c><00:01:42.560><c> then</c><00:01:42.759><c> produces</c><00:01:43.159><c> a</c><00:01:43.360><c> final</c><00:01:43.720><c> report</c>

00:01:44.030 --> 00:01:44.040 align:start position:0%
report and then produces a final report
 

00:01:44.040 --> 00:01:47.910 align:start position:0%
report and then produces a final report
for<00:01:44.200><c> us</c><00:01:44.840><c> for</c><00:01:45.040><c> NVIDIA</c><00:01:45.880><c> it</c><00:01:46.040><c> generated</c><00:01:46.479><c> a</c><00:01:46.759><c> nicer</c>

00:01:47.910 --> 00:01:47.920 align:start position:0%
for us for NVIDIA it generated a nicer
 

00:01:47.920 --> 00:01:50.630 align:start position:0%
for us for NVIDIA it generated a nicer
overview<00:01:48.920><c> core</c><00:01:49.240><c> metrics</c><00:01:49.640><c> for</c><00:01:49.799><c> the</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
overview core metrics for the
 

00:01:50.640 --> 00:01:56.109 align:start position:0%
overview core metrics for the
company<00:01:51.640><c> talks</c><00:01:51.960><c> about</c><00:01:52.200><c> the</c><00:01:52.399><c> financial</c>

00:01:56.109 --> 00:01:56.119 align:start position:0%
 
 

00:01:56.119 --> 00:01:58.389 align:start position:0%
 
performance<00:01:57.119><c> now</c><00:01:57.360><c> that</c><00:01:57.600><c> those</c><00:01:57.719><c> are</c><00:01:58.159><c> high</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
performance now that those are high
 

00:01:58.399 --> 00:02:00.590 align:start position:0%
performance now that those are high
gross<00:01:58.640><c> margins</c>

00:02:00.590 --> 00:02:00.600 align:start position:0%
gross margins
 

00:02:00.600 --> 00:02:03.069 align:start position:0%
gross margins
talks<00:02:00.880><c> about</c><00:02:01.079><c> the</c><00:02:01.200><c> growth</c><00:02:01.920><c> prospects</c><00:02:02.920><c> the</c>

00:02:03.069 --> 00:02:03.079 align:start position:0%
talks about the growth prospects the
 

00:02:03.079 --> 00:02:09.469 align:start position:0%
talks about the growth prospects the
current<00:02:03.439><c> acquisition</c><00:02:04.079><c> of</c><00:02:04.840><c> run</c><00:02:05.159><c> the</c><00:02:05.320><c> a</c>

00:02:09.469 --> 00:02:09.479 align:start position:0%
 
 

00:02:09.479 --> 00:02:13.470 align:start position:0%
 
startup<00:02:10.479><c> and</c><00:02:10.640><c> then</c><00:02:10.840><c> talks</c><00:02:11.080><c> about</c><00:02:11.319><c> the</c>

00:02:13.470 --> 00:02:13.480 align:start position:0%
 
 

00:02:13.480 --> 00:02:15.990 align:start position:0%
 
news<00:02:14.480><c> upgrades</c><00:02:14.920><c> and</c><00:02:15.080><c> downgrades</c><00:02:15.560><c> is</c><00:02:15.680><c> stying</c>

00:02:15.990 --> 00:02:16.000 align:start position:0%
news upgrades and downgrades is stying
 

00:02:16.000 --> 00:02:21.750 align:start position:0%
news upgrades and downgrades is stying
it<00:02:16.120><c> that</c><00:02:16.280><c> Goldman</c><00:02:16.640><c> Sachs</c><00:02:17.040><c> has</c><00:02:17.239><c> reiterated</c><00:02:17.920><c> its</c>

00:02:21.750 --> 00:02:21.760 align:start position:0%
 
 

00:02:21.760 --> 00:02:24.350 align:start position:0%
 
buy<00:02:22.760><c> gives</c><00:02:23.000><c> us</c><00:02:23.160><c> a</c>

00:02:24.350 --> 00:02:24.360 align:start position:0%
buy gives us a
 

00:02:24.360 --> 00:02:27.509 align:start position:0%
buy gives us a
summary<00:02:25.360><c> and</c><00:02:25.879><c> a</c>

00:02:27.509 --> 00:02:27.519 align:start position:0%
summary and a
 

00:02:27.519 --> 00:02:30.070 align:start position:0%
summary and a
recommendation<00:02:28.519><c> Let's</c><00:02:28.760><c> test</c><00:02:29.000><c> it</c><00:02:29.120><c> out</c><00:02:29.280><c> on</c><00:02:29.440><c> meta</c>

00:02:30.070 --> 00:02:30.080 align:start position:0%
recommendation Let's test it out on meta
 

00:02:30.080 --> 00:02:31.990 align:start position:0%
recommendation Let's test it out on meta
again<00:02:30.319><c> this</c><00:02:30.440><c> isn't</c><00:02:30.800><c> financial</c><00:02:31.319><c> advice</c><00:02:31.800><c> this</c>

00:02:31.990 --> 00:02:32.000 align:start position:0%
again this isn't financial advice this
 

00:02:32.000 --> 00:02:33.750 align:start position:0%
again this isn't financial advice this
just<00:02:32.120><c> an</c><00:02:32.319><c> AI</c><00:02:32.560><c> application</c><00:02:33.000><c> you</c><00:02:33.120><c> can</c><00:02:33.280><c> use</c><00:02:33.519><c> to</c>

00:02:33.750 --> 00:02:33.760 align:start position:0%
just an AI application you can use to
 

00:02:33.760 --> 00:02:37.550 align:start position:0%
just an AI application you can use to
build<00:02:34.040><c> your</c><00:02:34.160><c> own</c><00:02:34.400><c> investment</c>

00:02:37.550 --> 00:02:37.560 align:start position:0%
 
 

00:02:37.560 --> 00:02:40.670 align:start position:0%
 
researcher<00:02:38.560><c> for</c><00:02:38.840><c> meta</c><00:02:39.319><c> again</c><00:02:39.599><c> it</c><00:02:39.760><c> produces</c><00:02:40.440><c> a</c>

00:02:40.670 --> 00:02:40.680 align:start position:0%
researcher for meta again it produces a
 

00:02:40.680 --> 00:02:43.670 align:start position:0%
researcher for meta again it produces a
nice<00:02:40.920><c> little</c><00:02:41.640><c> overview</c><00:02:42.640><c> talks</c><00:02:43.000><c> about</c><00:02:43.519><c> the</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
nice little overview talks about the
 

00:02:43.680 --> 00:02:45.350 align:start position:0%
nice little overview talks about the
core<00:02:43.959><c> metrics</c><00:02:44.519><c> let's</c><00:02:44.720><c> see</c><00:02:44.840><c> if</c><00:02:44.959><c> the</c><00:02:45.120><c> stock</c>

00:02:45.350 --> 00:02:45.360 align:start position:0%
core metrics let's see if the stock
 

00:02:45.360 --> 00:02:49.350 align:start position:0%
core metrics let's see if the stock
price<00:02:45.879><c> matches</c><00:02:46.879><c> 440</c><00:02:47.519><c> on</c>

00:02:49.350 --> 00:02:49.360 align:start position:0%
price matches 440 on
 

00:02:49.360 --> 00:02:54.750 align:start position:0%
price matches 440 on
there<00:02:50.360><c> it</c><00:02:50.599><c> does</c>

00:02:54.750 --> 00:02:54.760 align:start position:0%
 
 

00:02:54.760 --> 00:02:56.990 align:start position:0%
 
okay<00:02:55.760><c> tells</c><00:02:56.040><c> us</c><00:02:56.159><c> about</c><00:02:56.360><c> the</c><00:02:56.480><c> financial</c>

00:02:56.990 --> 00:02:57.000 align:start position:0%
okay tells us about the financial
 

00:02:57.000 --> 00:03:00.509 align:start position:0%
okay tells us about the financial
performance<00:02:57.640><c> of</c><00:02:57.840><c> the</c><00:02:58.040><c> company</c>

00:03:00.509 --> 00:03:00.519 align:start position:0%
performance of the company
 

00:03:00.519 --> 00:03:03.550 align:start position:0%
performance of the company
growth<00:03:01.120><c> prospects</c><00:03:02.120><c> news</c><00:03:03.000><c> and</c><00:03:03.120><c> a</c>

00:03:03.550 --> 00:03:03.560 align:start position:0%
growth prospects news and a
 

00:03:03.560 --> 00:03:06.149 align:start position:0%
growth prospects news and a
recommendation<00:03:04.560><c> finally</c><00:03:05.319><c> Let's</c><00:03:05.560><c> test</c><00:03:05.760><c> it</c><00:03:05.920><c> out</c>

00:03:06.149 --> 00:03:06.159 align:start position:0%
recommendation finally Let's test it out
 

00:03:06.159 --> 00:03:08.869 align:start position:0%
recommendation finally Let's test it out
for<00:03:06.920><c> Tesla</c><00:03:07.920><c> before</c><00:03:08.120><c> we</c><00:03:08.239><c> do</c><00:03:08.360><c> that</c><00:03:08.519><c> let's</c><00:03:08.720><c> talk</c>

00:03:08.869 --> 00:03:08.879 align:start position:0%
for Tesla before we do that let's talk
 

00:03:08.879 --> 00:03:11.229 align:start position:0%
for Tesla before we do that let's talk
about<00:03:09.120><c> what's</c><00:03:09.319><c> happening</c><00:03:09.640><c> behind</c><00:03:09.879><c> the</c><00:03:10.239><c> scenes</c>

00:03:11.229 --> 00:03:11.239 align:start position:0%
about what's happening behind the scenes
 

00:03:11.239 --> 00:03:13.270 align:start position:0%
about what's happening behind the scenes
behind<00:03:11.560><c> the</c><00:03:11.720><c> scenes</c><00:03:12.560><c> this</c><00:03:12.760><c> investment</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
behind the scenes this investment
 

00:03:13.280 --> 00:03:15.390 align:start position:0%
behind the scenes this investment
researcher<00:03:14.280><c> has</c><00:03:14.480><c> the</c><00:03:14.720><c> app</c><00:03:15.120><c> which</c><00:03:15.239><c> we're</c>

00:03:15.390 --> 00:03:15.400 align:start position:0%
researcher has the app which we're
 

00:03:15.400 --> 00:03:16.910 align:start position:0%
researcher has the app which we're
running<00:03:15.799><c> this</c><00:03:15.920><c> is</c><00:03:16.040><c> the</c><00:03:16.159><c> front</c><00:03:16.360><c> end</c><00:03:16.599><c> of</c><00:03:16.720><c> the</c>

00:03:16.910 --> 00:03:16.920 align:start position:0%
running this is the front end of the
 

00:03:16.920 --> 00:03:20.350 align:start position:0%
running this is the front end of the
application<00:03:17.920><c> so</c><00:03:18.200><c> over</c><00:03:18.560><c> here</c><00:03:19.560><c> so</c><00:03:19.879><c> this</c><00:03:20.120><c> is</c>

00:03:20.350 --> 00:03:20.360 align:start position:0%
application so over here so this is
 

00:03:20.360 --> 00:03:22.830 align:start position:0%
application so over here so this is
where<00:03:20.599><c> you</c><00:03:21.519><c> customize</c><00:03:22.040><c> the</c><00:03:22.159><c> front</c><00:03:22.400><c> end</c><00:03:22.640><c> over</c>

00:03:22.830 --> 00:03:22.840 align:start position:0%
where you customize the front end over
 

00:03:22.840 --> 00:03:25.229 align:start position:0%
where you customize the front end over
here<00:03:23.040><c> we</c><00:03:23.239><c> giving</c><00:03:23.480><c> it</c><00:03:23.640><c> these</c>

00:03:25.229 --> 00:03:25.239 align:start position:0%
here we giving it these
 

00:03:25.239 --> 00:03:27.350 align:start position:0%
here we giving it these
models<00:03:26.239><c> you</c><00:03:26.360><c> can</c><00:03:26.480><c> see</c><00:03:26.680><c> you</c><00:03:26.760><c> can</c><00:03:26.920><c> send</c><00:03:27.120><c> it</c><00:03:27.239><c> to</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
models you can see you can send it to
 

00:03:27.360 --> 00:03:28.830 align:start position:0%
models you can see you can send it to
the<00:03:27.400><c> 8</c><00:03:27.680><c> billion</c><00:03:27.959><c> parameter</c><00:03:28.319><c> model</c><00:03:28.519><c> you</c><00:03:28.599><c> can</c><00:03:28.720><c> do</c>

00:03:28.830 --> 00:03:28.840 align:start position:0%
the 8 billion parameter model you can do
 

00:03:28.840 --> 00:03:31.390 align:start position:0%
the 8 billion parameter model you can do
Mixr<00:03:29.360><c> you</c><00:03:29.439><c> can</c><00:03:29.560><c> even</c><00:03:29.840><c> run</c><00:03:30.000><c> it</c><00:03:30.159><c> locally</c><00:03:31.040><c> using</c>

00:03:31.390 --> 00:03:31.400 align:start position:0%
Mixr you can even run it locally using
 

00:03:31.400 --> 00:03:34.149 align:start position:0%
Mixr you can even run it locally using
ama<00:03:31.879><c> if</c><00:03:31.959><c> you'd</c>

00:03:34.149 --> 00:03:34.159 align:start position:0%
ama if you'd
 

00:03:34.159 --> 00:03:36.710 align:start position:0%
ama if you'd
like<00:03:35.159><c> and</c><00:03:35.319><c> then</c><00:03:35.480><c> it</c><00:03:35.599><c> builds</c><00:03:35.879><c> out</c><00:03:36.120><c> the</c><00:03:36.280><c> entire</c>

00:03:36.710 --> 00:03:36.720 align:start position:0%
like and then it builds out the entire
 

00:03:36.720 --> 00:03:38.550 align:start position:0%
like and then it builds out the entire
application<00:03:37.159><c> the</c><00:03:37.280><c> front</c><00:03:37.480><c> end</c><00:03:37.760><c> which</c><00:03:37.879><c> you</c><00:03:38.040><c> see</c>

00:03:38.550 --> 00:03:38.560 align:start position:0%
application the front end which you see
 

00:03:38.560 --> 00:03:40.670 align:start position:0%
application the front end which you see
is<00:03:38.760><c> over</c><00:03:39.040><c> here</c><00:03:39.239><c> you</c><00:03:39.360><c> can</c><00:03:39.680><c> customize</c><00:03:40.280><c> this</c><00:03:40.400><c> to</c>

00:03:40.670 --> 00:03:40.680 align:start position:0%
is over here you can customize this to
 

00:03:40.680 --> 00:03:43.030 align:start position:0%
is over here you can customize this to
your<00:03:40.959><c> taste</c><00:03:41.280><c> to</c><00:03:41.480><c> your</c><00:03:41.760><c> choice</c><00:03:42.760><c> and</c><00:03:42.879><c> then</c>

00:03:43.030 --> 00:03:43.040 align:start position:0%
your taste to your choice and then
 

00:03:43.040 --> 00:03:44.830 align:start position:0%
your taste to your choice and then
behind<00:03:43.319><c> the</c><00:03:43.439><c> scenes</c><00:03:43.799><c> once</c><00:03:44.080><c> this</c><00:03:44.360><c> information</c>

00:03:44.830 --> 00:03:44.840 align:start position:0%
behind the scenes once this information
 

00:03:44.840 --> 00:03:47.229 align:start position:0%
behind the scenes once this information
is<00:03:45.040><c> generated</c><00:03:46.000><c> it's</c><00:03:46.159><c> sending</c><00:03:46.560><c> it</c><00:03:46.680><c> to</c><00:03:46.840><c> a</c><00:03:46.959><c> fi</c>

00:03:47.229 --> 00:03:47.239 align:start position:0%
is generated it's sending it to a fi
 

00:03:47.239 --> 00:03:49.750 align:start position:0%
is generated it's sending it to a fi
data<00:03:47.599><c> assistant</c><00:03:48.599><c> that</c><00:03:48.840><c> adds</c><00:03:49.360><c> memory</c>

00:03:49.750 --> 00:03:49.760 align:start position:0%
data assistant that adds memory
 

00:03:49.760 --> 00:03:52.350 align:start position:0%
data assistant that adds memory
knowledge<00:03:50.120><c> and</c><00:03:50.280><c> tools</c><00:03:50.560><c> to</c><00:03:50.720><c> llms</c><00:03:51.720><c> so</c><00:03:51.920><c> over</c><00:03:52.159><c> here</c>

00:03:52.350 --> 00:03:52.360 align:start position:0%
knowledge and tools to llms so over here
 

00:03:52.360 --> 00:03:53.789 align:start position:0%
knowledge and tools to llms so over here
our<00:03:52.640><c> assistant</c><00:03:53.040><c> we're</c><00:03:53.200><c> given</c><00:03:53.400><c> it</c><00:03:53.519><c> a</c>

00:03:53.789 --> 00:03:53.799 align:start position:0%
our assistant we're given it a
 

00:03:53.799 --> 00:03:55.589 align:start position:0%
our assistant we're given it a
description<00:03:54.799><c> okay</c><00:03:55.000><c> you're</c><00:03:55.159><c> a</c><00:03:55.280><c> senior</c>

00:03:55.589 --> 00:03:55.599 align:start position:0%
description okay you're a senior
 

00:03:55.599 --> 00:03:58.309 align:start position:0%
description okay you're a senior
investment<00:03:56.000><c> analyst</c><00:03:56.400><c> for</c><00:03:56.560><c> Goldman</c><00:03:57.200><c> Sachs</c><00:03:58.200><c> and</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
investment analyst for Goldman Sachs and
 

00:03:58.319 --> 00:03:59.789 align:start position:0%
investment analyst for Goldman Sachs and
you're<00:03:58.439><c> going</c><00:03:58.519><c> to</c><00:03:58.680><c> produce</c><00:03:58.920><c> a</c><00:03:59.040><c> report</c><00:03:59.280><c> for</c><00:03:59.400><c> an</c>

00:03:59.789 --> 00:03:59.799 align:start position:0%
you're going to produce a report for an
 

00:03:59.799 --> 00:04:01.710 align:start position:0%
you're going to produce a report for an
on<00:04:00.000><c> client</c><00:04:00.760><c> then</c><00:04:00.879><c> we're</c><00:04:01.040><c> going</c><00:04:01.159><c> to</c><00:04:01.400><c> give</c><00:04:01.519><c> it</c>

00:04:01.710 --> 00:04:01.720 align:start position:0%
on client then we're going to give it
 

00:04:01.720 --> 00:04:03.670 align:start position:0%
on client then we're going to give it
instructions<00:04:02.360><c> on</c><00:04:02.640><c> how</c><00:04:02.840><c> to</c><00:04:03.040><c> produce</c><00:04:03.400><c> that</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
instructions on how to produce that
 

00:04:03.680 --> 00:04:05.149 align:start position:0%
instructions on how to produce that
report<00:04:04.280><c> some</c><00:04:04.519><c> things</c><00:04:04.760><c> like</c><00:04:04.879><c> you</c><00:04:04.959><c> know</c><00:04:05.079><c> when</c>

00:04:05.149 --> 00:04:05.159 align:start position:0%
report some things like you know when
 

00:04:05.159 --> 00:04:07.429 align:start position:0%
report some things like you know when
you're<00:04:05.239><c> sharing</c><00:04:06.000><c> numbers</c><00:04:07.000><c> make</c><00:04:07.159><c> sure</c><00:04:07.319><c> to</c>

00:04:07.429 --> 00:04:07.439 align:start position:0%
you're sharing numbers make sure to
 

00:04:07.439 --> 00:04:10.470 align:start position:0%
you're sharing numbers make sure to
include<00:04:07.799><c> the</c><00:04:07.959><c> units</c><00:04:08.560><c> because</c><00:04:09.200><c> from</c><00:04:09.799><c> our</c><00:04:10.280><c> back</c>

00:04:10.470 --> 00:04:10.480 align:start position:0%
include the units because from our back
 

00:04:10.480 --> 00:04:12.710 align:start position:0%
include the units because from our back
end<00:04:10.680><c> the</c><00:04:10.799><c> numbers</c><00:04:11.040><c> are</c><00:04:11.200><c> coming</c><00:04:11.519><c> up</c><00:04:11.799><c> as</c><00:04:12.159><c> uh</c>

00:04:12.710 --> 00:04:12.720 align:start position:0%
end the numbers are coming up as uh
 

00:04:12.720 --> 00:04:14.589 align:start position:0%
end the numbers are coming up as uh
proper<00:04:13.000><c> numbers</c><00:04:13.280><c> so</c><00:04:13.400><c> it's</c><00:04:13.519><c> like</c><00:04:13.720><c> 10</c><00:04:14.000><c> 12</c><00:04:14.319><c> digits</c>

00:04:14.589 --> 00:04:14.599 align:start position:0%
proper numbers so it's like 10 12 digits
 

00:04:14.599 --> 00:04:16.349 align:start position:0%
proper numbers so it's like 10 12 digits
long<00:04:14.840><c> so</c><00:04:14.959><c> we're</c><00:04:15.120><c> asking</c><00:04:15.400><c> the</c><00:04:15.519><c> Alm</c><00:04:15.959><c> to</c><00:04:16.160><c> change</c>

00:04:16.349 --> 00:04:16.359 align:start position:0%
long so we're asking the Alm to change
 

00:04:16.359 --> 00:04:19.710 align:start position:0%
long so we're asking the Alm to change
it<00:04:16.479><c> to</c><00:04:16.799><c> proper</c><00:04:17.079><c> units</c><00:04:18.079><c> and</c><00:04:18.440><c> then</c><00:04:19.440><c> we're</c>

00:04:19.710 --> 00:04:19.720 align:start position:0%
it to proper units and then we're
 

00:04:19.720 --> 00:04:21.670 align:start position:0%
it to proper units and then we're
reiterating<00:04:20.519><c> that</c><00:04:20.680><c> it</c><00:04:20.799><c> should</c><00:04:21.040><c> be</c><00:04:21.280><c> properly</c>

00:04:21.670 --> 00:04:21.680 align:start position:0%
reiterating that it should be properly
 

00:04:21.680 --> 00:04:24.310 align:start position:0%
reiterating that it should be properly
formatted<00:04:22.400><c> and</c><00:04:22.600><c> follow</c><00:04:23.040><c> this</c><00:04:23.199><c> report</c><00:04:23.520><c> format</c>

00:04:24.310 --> 00:04:24.320 align:start position:0%
formatted and follow this report format
 

00:04:24.320 --> 00:04:26.189 align:start position:0%
formatted and follow this report format
we<00:04:24.520><c> give</c><00:04:24.639><c> it</c><00:04:24.759><c> a</c><00:04:24.919><c> format</c><00:04:25.400><c> because</c><00:04:25.600><c> we</c><00:04:25.759><c> want</c><00:04:25.960><c> to</c>

00:04:26.189 --> 00:04:26.199 align:start position:0%
we give it a format because we want to
 

00:04:26.199 --> 00:04:28.270 align:start position:0%
we give it a format because we want to
generate<00:04:26.919><c> the</c><00:04:27.120><c> report</c><00:04:27.560><c> consistently</c><00:04:28.199><c> it</c>

00:04:28.270 --> 00:04:28.280 align:start position:0%
generate the report consistently it
 

00:04:28.280 --> 00:04:30.150 align:start position:0%
generate the report consistently it
should<00:04:28.479><c> look</c><00:04:28.720><c> the</c><00:04:28.880><c> same</c><00:04:29.280><c> across</c><00:04:29.759><c> every</c><00:04:30.000><c> time</c>

00:04:30.150 --> 00:04:30.160 align:start position:0%
should look the same across every time
 

00:04:30.160 --> 00:04:32.469 align:start position:0%
should look the same across every time
we<00:04:30.320><c> generated</c><00:04:30.639><c> it</c><00:04:31.440><c> so</c><00:04:31.639><c> over</c><00:04:31.880><c> here</c><00:04:32.120><c> we're</c><00:04:32.280><c> given</c>

00:04:32.469 --> 00:04:32.479 align:start position:0%
we generated it so over here we're given
 

00:04:32.479 --> 00:04:35.029 align:start position:0%
we generated it so over here we're given
it<00:04:32.560><c> a</c><00:04:32.759><c> title</c><00:04:33.639><c> talk</c><00:04:33.880><c> about</c><00:04:34.080><c> the</c><00:04:34.160><c> metrics</c>

00:04:35.029 --> 00:04:35.039 align:start position:0%
it a title talk about the metrics
 

00:04:35.039 --> 00:04:37.230 align:start position:0%
it a title talk about the metrics
performance<00:04:35.919><c> and</c><00:04:36.039><c> we're</c><00:04:36.199><c> given</c><00:04:36.639><c> exactly</c><00:04:37.120><c> what</c>

00:04:37.230 --> 00:04:37.240 align:start position:0%
performance and we're given exactly what
 

00:04:37.240 --> 00:04:39.990 align:start position:0%
performance and we're given exactly what
we<00:04:37.400><c> want</c><00:04:37.759><c> in</c><00:04:37.880><c> each</c><00:04:38.320><c> section</c><00:04:39.320><c> using</c><00:04:39.759><c> this</c>

00:04:39.990 --> 00:04:40.000 align:start position:0%
we want in each section using this
 

00:04:40.000 --> 00:04:42.550 align:start position:0%
we want in each section using this
approach<00:04:40.680><c> it</c><00:04:40.840><c> generates</c><00:04:41.280><c> reports</c><00:04:41.639><c> for</c><00:04:41.800><c> us</c>

00:04:42.550 --> 00:04:42.560 align:start position:0%
approach it generates reports for us
 

00:04:42.560 --> 00:04:45.029 align:start position:0%
approach it generates reports for us
that's<00:04:42.800><c> it</c><00:04:43.199><c> simple</c><00:04:43.479><c> as</c>

00:04:45.029 --> 00:04:45.039 align:start position:0%
that's it simple as
 

00:04:45.039 --> 00:04:47.749 align:start position:0%
that's it simple as
that<00:04:46.039><c> okay</c><00:04:46.199><c> let's</c><00:04:46.400><c> look</c><00:04:46.520><c> at</c><00:04:46.680><c> for</c><00:04:46.880><c> Tesla</c>

00:04:47.749 --> 00:04:47.759 align:start position:0%
that okay let's look at for Tesla
 

00:04:47.759 --> 00:04:51.230 align:start position:0%
that okay let's look at for Tesla
overview<00:04:48.759><c> core</c>

00:04:51.230 --> 00:04:51.240 align:start position:0%
 
 

00:04:51.240 --> 00:04:53.270 align:start position:0%
 
metrics<00:04:52.240><c> talks</c><00:04:52.479><c> about</c><00:04:52.680><c> the</c><00:04:52.840><c> financial</c>

00:04:53.270 --> 00:04:53.280 align:start position:0%
metrics talks about the financial
 

00:04:53.280 --> 00:04:56.230 align:start position:0%
metrics talks about the financial
performance<00:04:53.759><c> the</c><00:04:53.919><c> growth</c><00:04:54.680><c> prospects</c><00:04:55.680><c> and</c><00:04:56.000><c> on</c>

00:04:56.230 --> 00:04:56.240 align:start position:0%
performance the growth prospects and on
 

00:04:56.240 --> 00:04:58.150 align:start position:0%
performance the growth prospects and on
here<00:04:56.440><c> it</c><00:04:56.560><c> says</c><00:04:57.280><c> this</c><00:04:57.400><c> is</c><00:04:57.600><c> a</c><00:04:57.759><c> hold</c>

00:04:58.150 --> 00:04:58.160 align:start position:0%
here it says this is a hold
 

00:04:58.160 --> 00:05:00.430 align:start position:0%
here it says this is a hold
recommendation<00:04:59.160><c> based</c><00:04:59.440><c> on</c><00:04:59.800><c> the</c><00:04:59.919><c> information</c>

00:05:00.430 --> 00:05:00.440 align:start position:0%
recommendation based on the information
 

00:05:00.440 --> 00:05:02.110 align:start position:0%
recommendation based on the information
which<00:05:00.600><c> it</c><00:05:00.800><c> has</c><00:05:01.280><c> again</c><00:05:01.479><c> none</c><00:05:01.639><c> of</c><00:05:01.800><c> this</c><00:05:01.919><c> is</c>

00:05:02.110 --> 00:05:02.120 align:start position:0%
which it has again none of this is
 

00:05:02.120 --> 00:05:05.510 align:start position:0%
which it has again none of this is
financial<00:05:02.639><c> advice</c><00:05:03.639><c> use</c><00:05:04.000><c> this</c><00:05:04.199><c> AI</c><00:05:04.600><c> application</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
financial advice use this AI application
 

00:05:05.520 --> 00:05:07.830 align:start position:0%
financial advice use this AI application
to<00:05:05.800><c> build</c><00:05:06.039><c> your</c><00:05:06.240><c> own</c><00:05:06.520><c> investment</c><00:05:07.000><c> researcher</c>

00:05:07.830 --> 00:05:07.840 align:start position:0%
to build your own investment researcher
 

00:05:07.840 --> 00:05:09.870 align:start position:0%
to build your own investment researcher
and<00:05:07.960><c> if</c><00:05:08.080><c> you</c><00:05:08.240><c> have</c><00:05:08.400><c> any</c><00:05:08.680><c> questions</c><00:05:09.400><c> open</c><00:05:09.600><c> up</c><00:05:09.759><c> a</c>

00:05:09.870 --> 00:05:09.880 align:start position:0%
and if you have any questions open up a
 

00:05:09.880 --> 00:05:12.029 align:start position:0%
and if you have any questions open up a
GitHub<00:05:10.280><c> issue</c><00:05:10.800><c> drop</c><00:05:11.080><c> by</c><00:05:11.240><c> in</c><00:05:11.320><c> the</c><00:05:11.479><c> Discord</c><00:05:11.880><c> or</c>

00:05:12.029 --> 00:05:12.039 align:start position:0%
GitHub issue drop by in the Discord or
 

00:05:12.039 --> 00:05:14.469 align:start position:0%
GitHub issue drop by in the Discord or
just<00:05:12.199><c> DM</c><00:05:12.520><c> me</c><00:05:12.759><c> with</c><00:05:13.080><c> any</c><00:05:13.320><c> questions</c><00:05:13.680><c> you</c><00:05:13.880><c> have</c>

00:05:14.469 --> 00:05:14.479 align:start position:0%
just DM me with any questions you have
 

00:05:14.479 --> 00:05:17.880 align:start position:0%
just DM me with any questions you have
thank<00:05:14.639><c> you</c><00:05:14.759><c> so</c><00:05:14.880><c> much</c><00:05:15.080><c> have</c><00:05:15.199><c> a</c><00:05:15.320><c> great</c><00:05:15.520><c> day</c>

