WEBVTT
Kind: captions
Language: en

00:00:00.539 --> 00:00:00.980 align:start position:0%
 
foreign

00:00:00.980 --> 00:00:00.990 align:start position:0%
foreign
 

00:00:00.990 --> 00:00:06.849 align:start position:0%
foreign
[Music]

00:00:06.849 --> 00:00:06.859 align:start position:0%
[Music]
 

00:00:06.859 --> 00:00:09.410 align:start position:0%
[Music]
to<00:00:07.859><c> introduce</c><00:00:08.220><c> webhook</c><00:00:08.760><c> automations</c><00:00:09.120><c> and</c>

00:00:09.410 --> 00:00:09.420 align:start position:0%
to introduce webhook automations and
 

00:00:09.420 --> 00:00:10.850 align:start position:0%
to introduce webhook automations and
weights<00:00:09.780><c> and</c><00:00:09.840><c> biases</c>

00:00:10.850 --> 00:00:10.860 align:start position:0%
weights and biases
 

00:00:10.860 --> 00:00:12.589 align:start position:0%
weights and biases
this<00:00:11.280><c> is</c><00:00:11.400><c> going</c><00:00:11.580><c> to</c><00:00:11.760><c> allow</c><00:00:12.000><c> teams</c><00:00:12.420><c> to</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
this is going to allow teams to
 

00:00:12.599 --> 00:00:15.169 align:start position:0%
this is going to allow teams to
integrate<00:00:13.019><c> CI</c><00:00:13.740><c> CD</c><00:00:14.040><c> tools</c><00:00:14.400><c> like</c><00:00:14.759><c> GitHub</c>

00:00:15.169 --> 00:00:15.179 align:start position:0%
integrate CI CD tools like GitHub
 

00:00:15.179 --> 00:00:16.730 align:start position:0%
integrate CI CD tools like GitHub
actions<00:00:15.599><c> with</c><00:00:15.839><c> the</c><00:00:15.960><c> weights</c><00:00:16.260><c> and</c><00:00:16.320><c> biases</c>

00:00:16.730 --> 00:00:16.740 align:start position:0%
actions with the weights and biases
 

00:00:16.740 --> 00:00:18.710 align:start position:0%
actions with the weights and biases
model<00:00:16.920><c> registry</c><00:00:17.460><c> and</c><00:00:17.940><c> facilitate</c><00:00:18.480><c> clean</c>

00:00:18.710 --> 00:00:18.720 align:start position:0%
model registry and facilitate clean
 

00:00:18.720 --> 00:00:21.230 align:start position:0%
model registry and facilitate clean
handoff<00:00:19.199><c> between</c><00:00:19.740><c> ml</c><00:00:20.220><c> development</c><00:00:20.580><c> and</c><00:00:20.939><c> ml</c>

00:00:21.230 --> 00:00:21.240 align:start position:0%
handoff between ml development and ml
 

00:00:21.240 --> 00:00:22.189 align:start position:0%
handoff between ml development and ml
deployment

00:00:22.189 --> 00:00:22.199 align:start position:0%
deployment
 

00:00:22.199 --> 00:00:24.349 align:start position:0%
deployment
to<00:00:22.439><c> set</c><00:00:22.619><c> up</c><00:00:22.740><c> a</c><00:00:22.800><c> web</c><00:00:22.980><c> hook</c><00:00:23.160><c> we</c><00:00:23.880><c> first</c><00:00:24.060><c> need</c><00:00:24.240><c> to</c>

00:00:24.349 --> 00:00:24.359 align:start position:0%
to set up a web hook we first need to
 

00:00:24.359 --> 00:00:26.630 align:start position:0%
to set up a web hook we first need to
create<00:00:24.480><c> a</c><00:00:24.840><c> secret</c><00:00:24.960><c> so</c><00:00:25.439><c> that</c><00:00:25.619><c> way</c><00:00:25.859><c> devices</c><00:00:26.220><c> can</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
create a secret so that way devices can
 

00:00:26.640 --> 00:00:28.490 align:start position:0%
create a secret so that way devices can
authenticate<00:00:27.240><c> to</c><00:00:27.539><c> an</c><00:00:27.660><c> external</c><00:00:28.019><c> tool</c><00:00:28.320><c> like</c>

00:00:28.490 --> 00:00:28.500 align:start position:0%
authenticate to an external tool like
 

00:00:28.500 --> 00:00:31.030 align:start position:0%
authenticate to an external tool like
GitHub<00:00:28.920><c> actions</c>

00:00:31.030 --> 00:00:31.040 align:start position:0%
GitHub actions
 

00:00:31.040 --> 00:00:33.170 align:start position:0%
GitHub actions
has<00:00:32.040><c> a</c><00:00:32.160><c> secret</c><00:00:32.220><c> store</c><00:00:32.520><c> where</c><00:00:32.759><c> you</c><00:00:32.940><c> can</c><00:00:33.000><c> add</c>

00:00:33.170 --> 00:00:33.180 align:start position:0%
has a secret store where you can add
 

00:00:33.180 --> 00:00:34.850 align:start position:0%
has a secret store where you can add
this<00:00:33.360><c> information</c><00:00:33.600><c> and</c><00:00:34.140><c> access</c><00:00:34.320><c> it</c><00:00:34.559><c> in</c><00:00:34.739><c> the</c>

00:00:34.850 --> 00:00:34.860 align:start position:0%
this information and access it in the
 

00:00:34.860 --> 00:00:36.709 align:start position:0%
this information and access it in the
web<00:00:34.980><c> hook</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
web hook
 

00:00:36.719 --> 00:00:38.690 align:start position:0%
web hook
next<00:00:37.140><c> we're</c><00:00:37.380><c> going</c><00:00:37.500><c> to</c><00:00:37.680><c> add</c><00:00:37.860><c> the</c><00:00:38.100><c> web</c><00:00:38.219><c> clerk</c>

00:00:38.690 --> 00:00:38.700 align:start position:0%
next we're going to add the web clerk
 

00:00:38.700 --> 00:00:41.630 align:start position:0%
next we're going to add the web clerk
which<00:00:39.120><c> is</c><00:00:39.300><c> just</c><00:00:39.540><c> simply</c><00:00:39.899><c> the</c><00:00:40.739><c> URL</c><00:00:41.219><c> and</c><00:00:41.460><c> secret</c>

00:00:41.630 --> 00:00:41.640 align:start position:0%
which is just simply the URL and secret
 

00:00:41.640 --> 00:00:43.490 align:start position:0%
which is just simply the URL and secret
combination<00:00:42.180><c> that</c><00:00:42.540><c> we'll</c><00:00:42.660><c> use</c>

00:00:43.490 --> 00:00:43.500 align:start position:0%
combination that we'll use
 

00:00:43.500 --> 00:00:50.990 align:start position:0%
combination that we'll use
and<00:00:43.860><c> configure</c><00:00:44.340><c> later</c><00:00:44.520><c> with</c><00:00:44.760><c> the</c><00:00:44.879><c> payload</c>

00:00:50.990 --> 00:00:51.000 align:start position:0%
 
 

00:00:51.000 --> 00:00:52.490 align:start position:0%
 
next<00:00:51.360><c> we'll</c><00:00:51.539><c> need</c><00:00:51.719><c> to</c><00:00:51.840><c> go</c><00:00:52.020><c> to</c><00:00:52.079><c> the</c><00:00:52.379><c> model</c>

00:00:52.490 --> 00:00:52.500 align:start position:0%
next we'll need to go to the model
 

00:00:52.500 --> 00:00:54.950 align:start position:0%
next we'll need to go to the model
registry<00:00:53.100><c> and</c><00:00:54.000><c> configure</c><00:00:54.420><c> the</c><00:00:54.600><c> automation</c>

00:00:54.950 --> 00:00:54.960 align:start position:0%
registry and configure the automation
 

00:00:54.960 --> 00:00:56.510 align:start position:0%
registry and configure the automation
for<00:00:55.320><c> a</c><00:00:55.440><c> registered</c><00:00:55.800><c> model</c><00:00:55.980><c> that</c><00:00:56.219><c> we</c><00:00:56.340><c> care</c>

00:00:56.510 --> 00:00:56.520 align:start position:0%
for a registered model that we care
 

00:00:56.520 --> 00:00:58.310 align:start position:0%
for a registered model that we care
about

00:00:58.310 --> 00:00:58.320 align:start position:0%
about
 

00:00:58.320 --> 00:00:59.510 align:start position:0%
about
I'm<00:00:58.620><c> going</c><00:00:58.800><c> to</c><00:00:58.860><c> choose</c><00:00:59.100><c> this</c><00:00:59.219><c> nature</c>

00:00:59.510 --> 00:00:59.520 align:start position:0%
I'm going to choose this nature
 

00:00:59.520 --> 00:01:01.310 align:start position:0%
I'm going to choose this nature
classification<00:01:00.120><c> model</c><00:01:00.360><c> which</c><00:01:00.840><c> has</c><00:01:00.960><c> several</c>

00:01:01.310 --> 00:01:01.320 align:start position:0%
classification model which has several
 

00:01:01.320 --> 00:01:04.070 align:start position:0%
classification model which has several
models<00:01:01.680><c> that</c><00:01:01.860><c> have</c><00:01:01.980><c> been</c><00:01:02.100><c> trained</c><00:01:02.460><c> over</c><00:01:02.760><c> time</c>

00:01:04.070 --> 00:01:04.080 align:start position:0%
models that have been trained over time
 

00:01:04.080 --> 00:01:05.690 align:start position:0%
models that have been trained over time
as<00:01:04.500><c> we</c><00:01:04.680><c> can</c><00:01:04.739><c> see</c><00:01:04.860><c> here</c><00:01:04.979><c> we</c><00:01:05.159><c> have</c><00:01:05.220><c> a</c><00:01:05.339><c> staging</c>

00:01:05.690 --> 00:01:05.700 align:start position:0%
as we can see here we have a staging
 

00:01:05.700 --> 00:01:08.270 align:start position:0%
as we can see here we have a staging
model<00:01:05.880><c> and</c><00:01:06.119><c> a</c><00:01:06.299><c> production</c><00:01:06.600><c> model</c>

00:01:08.270 --> 00:01:08.280 align:start position:0%
model and a production model
 

00:01:08.280 --> 00:01:10.250 align:start position:0%
model and a production model
automations<00:01:09.000><c> will</c><00:01:09.360><c> come</c><00:01:09.600><c> in</c><00:01:09.720><c> handy</c><00:01:09.960><c> in</c><00:01:10.140><c> being</c>

00:01:10.250 --> 00:01:10.260 align:start position:0%
automations will come in handy in being
 

00:01:10.260 --> 00:01:11.570 align:start position:0%
automations will come in handy in being
able<00:01:10.439><c> to</c><00:01:10.560><c> hand</c><00:01:10.799><c> off</c>

00:01:11.570 --> 00:01:11.580 align:start position:0%
able to hand off
 

00:01:11.580 --> 00:01:14.570 align:start position:0%
able to hand off
the<00:01:12.000><c> production</c><00:01:12.360><c> model</c><00:01:12.600><c> the</c><00:01:13.380><c> 2ci</c><00:01:13.860><c> CD</c><00:01:14.159><c> to</c><00:01:14.460><c> be</c>

00:01:14.570 --> 00:01:14.580 align:start position:0%
the production model the 2ci CD to be
 

00:01:14.580 --> 00:01:17.990 align:start position:0%
the production model the 2ci CD to be
containerized<00:01:15.240><c> in</c><00:01:15.420><c> an</c><00:01:15.540><c> imprint</c><00:01:15.840><c> server</c>

00:01:17.990 --> 00:01:18.000 align:start position:0%
containerized in an imprint server
 

00:01:18.000 --> 00:01:19.550 align:start position:0%
containerized in an imprint server
viewing<00:01:18.420><c> details</c><00:01:18.780><c> we</c><00:01:18.960><c> can</c><00:01:19.080><c> understand</c><00:01:19.200><c> a</c>

00:01:19.550 --> 00:01:19.560 align:start position:0%
viewing details we can understand a
 

00:01:19.560 --> 00:01:21.170 align:start position:0%
viewing details we can understand a
little<00:01:19.619><c> bit</c><00:01:19.740><c> more</c><00:01:19.860><c> about</c><00:01:20.040><c> this</c><00:01:20.280><c> model</c><00:01:20.520><c> where</c>

00:01:21.170 --> 00:01:21.180 align:start position:0%
little bit more about this model where
 

00:01:21.180 --> 00:01:22.190 align:start position:0%
little bit more about this model where
it<00:01:21.299><c> came</c><00:01:21.479><c> from</c>

00:01:22.190 --> 00:01:22.200 align:start position:0%
it came from
 

00:01:22.200 --> 00:01:25.070 align:start position:0%
it came from
and<00:01:22.560><c> how</c><00:01:22.680><c> it</c><00:01:22.860><c> was</c><00:01:22.979><c> built</c>

00:01:25.070 --> 00:01:25.080 align:start position:0%
and how it was built
 

00:01:25.080 --> 00:01:28.190 align:start position:0%
and how it was built
creating<00:01:25.560><c> an</c><00:01:25.740><c> automation</c>

00:01:28.190 --> 00:01:28.200 align:start position:0%
 
 

00:01:28.200 --> 00:01:30.649 align:start position:0%
 
we<00:01:28.560><c> can</c><00:01:28.680><c> determine</c><00:01:29.040><c> which</c><00:01:29.520><c> event</c><00:01:29.880><c> type</c><00:01:30.240><c> in</c><00:01:30.540><c> the</c>

00:01:30.649 --> 00:01:30.659 align:start position:0%
we can determine which event type in the
 

00:01:30.659 --> 00:01:33.710 align:start position:0%
we can determine which event type in the
model<00:01:30.780><c> registry</c><00:01:31.320><c> will</c><00:01:31.680><c> trigger</c><00:01:32.040><c> the</c><00:01:32.220><c> webboat</c>

00:01:33.710 --> 00:01:33.720 align:start position:0%
model registry will trigger the webboat
 

00:01:33.720 --> 00:01:35.870 align:start position:0%
model registry will trigger the webboat
here<00:01:34.080><c> we</c><00:01:34.200><c> have</c><00:01:34.320><c> two</c><00:01:34.500><c> options</c><00:01:34.860><c> one</c><00:01:35.280><c> when</c><00:01:35.579><c> a</c><00:01:35.759><c> new</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
here we have two options one when a new
 

00:01:35.880 --> 00:01:38.510 align:start position:0%
here we have two options one when a new
model<00:01:36.060><c> is</c><00:01:36.360><c> added</c><00:01:36.659><c> to</c><00:01:36.840><c> this</c><00:01:36.960><c> registered</c><00:01:37.439><c> model</c>

00:01:38.510 --> 00:01:38.520 align:start position:0%
model is added to this registered model
 

00:01:38.520 --> 00:01:41.149 align:start position:0%
model is added to this registered model
or<00:01:39.060><c> when</c><00:01:39.299><c> an</c><00:01:39.479><c> artifact</c><00:01:39.840><c> Alias</c><00:01:40.380><c> is</c><00:01:40.500><c> added</c><00:01:40.860><c> to</c><00:01:41.040><c> a</c>

00:01:41.149 --> 00:01:41.159 align:start position:0%
or when an artifact Alias is added to a
 

00:01:41.159 --> 00:01:43.429 align:start position:0%
or when an artifact Alias is added to a
particular<00:01:41.460><c> model</c><00:01:41.700><c> version</c><00:01:42.000><c> such</c><00:01:42.960><c> as</c><00:01:43.140><c> when</c>

00:01:43.429 --> 00:01:43.439 align:start position:0%
particular model version such as when
 

00:01:43.439 --> 00:01:45.289 align:start position:0%
particular model version such as when
the<00:01:43.619><c> production</c><00:01:43.920><c> Alias</c><00:01:44.460><c> is</c><00:01:44.700><c> added</c><00:01:44.939><c> to</c><00:01:45.119><c> a</c>

00:01:45.289 --> 00:01:45.299 align:start position:0%
the production Alias is added to a
 

00:01:45.299 --> 00:01:47.510 align:start position:0%
the production Alias is added to a
particular<00:01:45.600><c> model</c><00:01:45.840><c> version</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
particular model version
 

00:01:47.520 --> 00:01:49.429 align:start position:0%
particular model version
I'm<00:01:47.939><c> going</c><00:01:48.119><c> to</c><00:01:48.180><c> select</c><00:01:48.420><c> that</c><00:01:48.600><c> one</c>

00:01:49.429 --> 00:01:49.439 align:start position:0%
I'm going to select that one
 

00:01:49.439 --> 00:01:51.350 align:start position:0%
I'm going to select that one
in<00:01:49.920><c> particular</c><00:01:50.340><c> I'm</c><00:01:50.640><c> going</c><00:01:50.759><c> to</c><00:01:50.820><c> choose</c><00:01:51.119><c> the</c>

00:01:51.350 --> 00:01:51.360 align:start position:0%
in particular I'm going to choose the
 

00:01:51.360 --> 00:01:53.510 align:start position:0%
in particular I'm going to choose the
production<00:01:51.659><c> Alias</c><00:01:52.259><c> whenever</c><00:01:53.100><c> the</c><00:01:53.280><c> production</c>

00:01:53.510 --> 00:01:53.520 align:start position:0%
production Alias whenever the production
 

00:01:53.520 --> 00:01:55.130 align:start position:0%
production Alias whenever the production
Alias<00:01:53.939><c> is</c><00:01:54.119><c> added</c><00:01:54.360><c> to</c><00:01:54.479><c> a</c><00:01:54.600><c> particular</c><00:01:54.899><c> model</c>

00:01:55.130 --> 00:01:55.140 align:start position:0%
Alias is added to a particular model
 

00:01:55.140 --> 00:01:58.550 align:start position:0%
Alias is added to a particular model
version<00:01:55.439><c> this</c><00:01:56.159><c> web</c><00:01:56.340><c> hook</c><00:01:56.520><c> will</c><00:01:56.820><c> trigger</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
version this web hook will trigger
 

00:01:58.560 --> 00:02:00.230 align:start position:0%
version this web hook will trigger
I<00:01:58.860><c> want</c><00:01:59.040><c> this</c><00:01:59.220><c> to</c><00:01:59.399><c> happen</c><00:01:59.520><c> for</c><00:01:59.820><c> the</c><00:01:59.939><c> nature</c>

00:02:00.230 --> 00:02:00.240 align:start position:0%
I want this to happen for the nature
 

00:02:00.240 --> 00:02:03.170 align:start position:0%
I want this to happen for the nature
classification<00:02:00.720><c> model</c>

00:02:03.170 --> 00:02:03.180 align:start position:0%
 
 

00:02:03.180 --> 00:02:04.850 align:start position:0%
 
and<00:02:03.479><c> I</c><00:02:03.600><c> want</c><00:02:03.720><c> a</c><00:02:03.840><c> web</c><00:02:03.899><c> poke</c><00:02:04.140><c> to</c><00:02:04.380><c> trigger</c><00:02:04.680><c> when</c>

00:02:04.850 --> 00:02:04.860 align:start position:0%
and I want a web poke to trigger when
 

00:02:04.860 --> 00:02:06.709 align:start position:0%
and I want a web poke to trigger when
that<00:02:05.040><c> occurs</c>

00:02:06.709 --> 00:02:06.719 align:start position:0%
that occurs
 

00:02:06.719 --> 00:02:08.690 align:start position:0%
that occurs
I'm<00:02:07.439><c> going</c><00:02:07.560><c> to</c><00:02:07.619><c> select</c><00:02:07.860><c> the</c><00:02:07.920><c> web</c><00:02:08.099><c> hook</c><00:02:08.280><c> that</c><00:02:08.520><c> I</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
I'm going to select the web hook that I
 

00:02:08.700 --> 00:02:11.089 align:start position:0%
I'm going to select the web hook that I
chose<00:02:09.000><c> before</c><00:02:09.239><c> and</c><00:02:09.959><c> next</c><00:02:10.080><c> I</c><00:02:10.259><c> need</c><00:02:10.440><c> to</c><00:02:10.560><c> add</c><00:02:10.860><c> the</c>

00:02:11.089 --> 00:02:11.099 align:start position:0%
chose before and next I need to add the
 

00:02:11.099 --> 00:02:12.410 align:start position:0%
chose before and next I need to add the
payload

00:02:12.410 --> 00:02:12.420 align:start position:0%
payload
 

00:02:12.420 --> 00:02:14.750 align:start position:0%
payload
we<00:02:12.660><c> can</c><00:02:12.840><c> use</c><00:02:12.900><c> template</c><00:02:13.319><c> strings</c><00:02:13.680><c> to</c><00:02:14.280><c> configure</c>

00:02:14.750 --> 00:02:14.760 align:start position:0%
we can use template strings to configure
 

00:02:14.760 --> 00:02:17.150 align:start position:0%
we can use template strings to configure
what<00:02:15.599><c> context</c><00:02:15.959><c> from</c><00:02:16.200><c> weights</c><00:02:16.560><c> and</c><00:02:16.620><c> biases</c><00:02:16.980><c> we</c>

00:02:17.150 --> 00:02:17.160 align:start position:0%
what context from weights and biases we
 

00:02:17.160 --> 00:02:19.250 align:start position:0%
what context from weights and biases we
want<00:02:17.340><c> to</c><00:02:17.520><c> pass</c><00:02:17.700><c> to</c><00:02:18.000><c> the</c><00:02:18.120><c> web</c><00:02:18.239><c> hook</c><00:02:18.480><c> and</c>

00:02:19.250 --> 00:02:19.260 align:start position:0%
want to pass to the web hook and
 

00:02:19.260 --> 00:02:21.470 align:start position:0%
want to pass to the web hook and
therefore<00:02:19.560><c> to</c><00:02:19.860><c> GitHub</c><00:02:20.459><c> actions</c><00:02:20.940><c> I'm</c><00:02:21.300><c> going</c><00:02:21.420><c> to</c>

00:02:21.470 --> 00:02:21.480 align:start position:0%
therefore to GitHub actions I'm going to
 

00:02:21.480 --> 00:02:23.390 align:start position:0%
therefore to GitHub actions I'm going to
paste<00:02:21.780><c> this</c><00:02:21.900><c> payload</c><00:02:22.379><c> here</c><00:02:22.800><c> here</c><00:02:23.160><c> we're</c>

00:02:23.390 --> 00:02:23.400 align:start position:0%
paste this payload here here we're
 

00:02:23.400 --> 00:02:25.729 align:start position:0%
paste this payload here here we're
passing<00:02:23.760><c> the</c><00:02:23.879><c> event</c><00:02:24.060><c> type</c><00:02:24.360><c> along</c><00:02:25.260><c> with</c><00:02:25.620><c> the</c>

00:02:25.729 --> 00:02:25.739 align:start position:0%
passing the event type along with the
 

00:02:25.739 --> 00:02:29.570 align:start position:0%
passing the event type along with the
production<00:02:26.040><c> model</c><00:02:26.280><c> artifact</c><00:02:27.000><c> string</c>

00:02:29.570 --> 00:02:29.580 align:start position:0%
 
 

00:02:29.580 --> 00:02:32.330 align:start position:0%
 
along<00:02:30.120><c> with</c><00:02:30.360><c> the</c><00:02:30.480><c> way</c><00:02:30.599><c> to</c><00:02:30.780><c> mice's</c><00:02:31.200><c> entity</c>

00:02:32.330 --> 00:02:32.340 align:start position:0%
along with the way to mice's entity
 

00:02:32.340 --> 00:02:34.309 align:start position:0%
along with the way to mice's entity
our<00:02:32.760><c> kid</c><00:02:33.060><c> of</c><00:02:33.239><c> action</c><00:02:33.420><c> will</c><00:02:33.599><c> use</c><00:02:33.840><c> this</c><00:02:33.959><c> context</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
our kid of action will use this context
 

00:02:34.319 --> 00:02:36.470 align:start position:0%
our kid of action will use this context
later<00:02:34.560><c> on</c><00:02:34.800><c> to</c><00:02:35.040><c> pull</c><00:02:35.220><c> the</c><00:02:35.400><c> model</c><00:02:35.580><c> and</c><00:02:36.180><c> put</c><00:02:36.360><c> it</c>

00:02:36.470 --> 00:02:36.480 align:start position:0%
later on to pull the model and put it
 

00:02:36.480 --> 00:02:45.229 align:start position:0%
later on to pull the model and put it
into<00:02:36.720><c> a</c><00:02:37.140><c> containerized</c><00:02:38.040><c> and</c><00:02:38.160><c> print</c><00:02:38.400><c> server</c>

00:02:45.229 --> 00:02:45.239 align:start position:0%
 
 

00:02:45.239 --> 00:02:48.110 align:start position:0%
 
now<00:02:45.660><c> that</c><00:02:45.840><c> the</c><00:02:46.019><c> automation</c><00:02:46.260><c> has</c><00:02:46.440><c> been</c><00:02:46.560><c> created</c>

00:02:48.110 --> 00:02:48.120 align:start position:0%
now that the automation has been created
 

00:02:48.120 --> 00:02:50.930 align:start position:0%
now that the automation has been created
we<00:02:48.420><c> can</c><00:02:48.599><c> go</c><00:02:48.720><c> to</c><00:02:48.840><c> our</c><00:02:49.080><c> staging</c><00:02:49.379><c> model</c>

00:02:50.930 --> 00:02:50.940 align:start position:0%
we can go to our staging model
 

00:02:50.940 --> 00:02:56.110 align:start position:0%
we can go to our staging model
let's<00:02:51.239><c> add</c><00:02:51.720><c> the</c><00:02:51.959><c> production</c><00:02:52.260><c> alias</c>

00:02:56.110 --> 00:02:56.120 align:start position:0%
 
 

00:02:56.120 --> 00:02:58.910 align:start position:0%
 
and<00:02:57.120><c> with</c><00:02:57.360><c> that</c><00:02:57.540><c> you'll</c><00:02:58.140><c> notice</c><00:02:58.440><c> that</c><00:02:58.739><c> this</c>

00:02:58.910 --> 00:02:58.920 align:start position:0%
and with that you'll notice that this
 

00:02:58.920 --> 00:03:01.009 align:start position:0%
and with that you'll notice that this
production<00:02:59.220><c> list</c><00:02:59.519><c> is</c><00:02:59.819><c> purple</c><00:03:00.000><c> it's</c><00:03:00.480><c> protected</c>

00:03:01.009 --> 00:03:01.019 align:start position:0%
production list is purple it's protected
 

00:03:01.019 --> 00:03:03.170 align:start position:0%
production list is purple it's protected
so<00:03:01.319><c> only</c><00:03:01.500><c> monorada</c><00:03:02.160><c> Street</c><00:03:02.340><c> registry</c><00:03:02.819><c> admins</c>

00:03:03.170 --> 00:03:03.180 align:start position:0%
so only monorada Street registry admins
 

00:03:03.180 --> 00:03:05.690 align:start position:0%
so only monorada Street registry admins
can<00:03:03.480><c> make</c><00:03:03.599><c> this</c><00:03:03.780><c> change</c>

00:03:05.690 --> 00:03:05.700 align:start position:0%
can make this change
 

00:03:05.700 --> 00:03:08.990 align:start position:0%
can make this change
when<00:03:06.180><c> I</c><00:03:06.300><c> made</c><00:03:06.420><c> that</c><00:03:06.599><c> change</c><00:03:06.840><c> that</c><00:03:07.800><c> triggered</c><00:03:08.280><c> a</c>

00:03:08.990 --> 00:03:09.000 align:start position:0%
when I made that change that triggered a
 

00:03:09.000 --> 00:03:10.850 align:start position:0%
when I made that change that triggered a
GitHub<00:03:09.420><c> action</c>

00:03:10.850 --> 00:03:10.860 align:start position:0%
GitHub action
 

00:03:10.860 --> 00:03:15.589 align:start position:0%
GitHub action
to<00:03:11.340><c> consume</c><00:03:11.700><c> that</c><00:03:11.879><c> model</c><00:03:12.000><c> artifact</c>

00:03:15.589 --> 00:03:15.599 align:start position:0%
 
 

00:03:15.599 --> 00:03:18.170 align:start position:0%
 
containerize<00:03:16.440><c> it</c><00:03:16.680><c> into</c><00:03:17.040><c> a</c><00:03:17.459><c> torch</c><00:03:17.700><c> serve</c><00:03:18.000><c> and</c>

00:03:18.170 --> 00:03:18.180 align:start position:0%
containerize it into a torch serve and
 

00:03:18.180 --> 00:03:23.630 align:start position:0%
containerize it into a torch serve and
print<00:03:18.300><c> server</c><00:03:19.200><c> and</c><00:03:19.800><c> deploy</c><00:03:20.099><c> it</c><00:03:20.220><c> to</c><00:03:20.400><c> kubernetes</c>

00:03:23.630 --> 00:03:23.640 align:start position:0%
 
 

00:03:23.640 --> 00:03:25.190 align:start position:0%
 
so<00:03:23.940><c> with</c><00:03:24.060><c> webbook</c><00:03:24.420><c> automations</c><00:03:24.780><c> you</c><00:03:25.080><c> can</c>

00:03:25.190 --> 00:03:25.200 align:start position:0%
so with webbook automations you can
 

00:03:25.200 --> 00:03:27.530 align:start position:0%
so with webbook automations you can
facilitate<00:03:25.739><c> clean</c><00:03:26.159><c> handoff</c><00:03:26.700><c> of</c><00:03:27.360><c> model</c>

00:03:27.530 --> 00:03:27.540 align:start position:0%
facilitate clean handoff of model
 

00:03:27.540 --> 00:03:30.410 align:start position:0%
facilitate clean handoff of model
artifacts<00:03:28.260><c> from</c><00:03:28.980><c> model</c><00:03:29.220><c> development</c><00:03:29.819><c> to</c>

00:03:30.410 --> 00:03:30.420 align:start position:0%
artifacts from model development to
 

00:03:30.420 --> 00:03:32.700 align:start position:0%
artifacts from model development to
model<00:03:30.720><c> deployment</c><00:03:31.260><c> and</c><00:03:31.560><c> testing</c>

00:03:32.700 --> 00:03:32.710 align:start position:0%
model deployment and testing
 

00:03:32.710 --> 00:03:43.550 align:start position:0%
model deployment and testing
[Music]

00:03:43.550 --> 00:03:43.560 align:start position:0%
[Music]
 

00:03:43.560 --> 00:03:46.560 align:start position:0%
[Music]
foreign

