import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json()

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      )
    }

    // Call your Python AI service to generate embeddings
    const pythonServiceUrl = process.env.PYTHON_AI_SERVICE_URL || 'http://localhost:9000'
    const embeddingResponse = await fetch(`${pythonServiceUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.PYTHON_AI_SERVICE_API_KEY || ''}`
      },
      body: JSON.stringify({ text })
    })

    if (!embeddingResponse.ok) {
      throw new Error(`Python AI service responded with status: ${embeddingResponse.status}`)
    }

    const embeddingData = await embeddingResponse.json()

    return NextResponse.json({
      embedding: embeddingData.embedding,
      model: embeddingData.model || 'unknown',
      dimensions: embeddingData.dimensions || embeddingData.embedding?.length
    })
  } catch (error) {
    console.error('Error generating embedding:', error)
    return NextResponse.json(
      { error: 'Failed to generate embedding' },
      { status: 500 }
    )
  }
}
