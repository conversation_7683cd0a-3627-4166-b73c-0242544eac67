WEBVTT
Kind: captions
Language: en

00:00:00.210 --> 00:00:04.950 align:start position:0%
 
[Music]

00:00:04.950 --> 00:00:04.960 align:start position:0%
[Music]
 

00:00:04.960 --> 00:00:06.749 align:start position:0%
[Music]
giant<00:00:05.600><c> security</c><00:00:06.000><c> is</c><00:00:06.160><c> having</c><00:00:06.440><c> a</c>

00:00:06.749 --> 00:00:06.759 align:start position:0%
giant security is having a
 

00:00:06.759 --> 00:00:08.870 align:start position:0%
giant security is having a
transformative<00:00:07.560><c> impact</c><00:00:07.919><c> on</c><00:00:08.240><c> how</c><00:00:08.440><c> quickly</c><00:00:08.760><c> we</c>

00:00:08.870 --> 00:00:08.880 align:start position:0%
transformative impact on how quickly we
 

00:00:08.880 --> 00:00:10.749 align:start position:0%
transformative impact on how quickly we
can<00:00:09.000><c> deliver</c><00:00:09.360><c> software</c><00:00:09.760><c> to</c><00:00:09.880><c> our</c><00:00:10.040><c> customers</c>

00:00:10.749 --> 00:00:10.759 align:start position:0%
can deliver software to our customers
 

00:00:10.759 --> 00:00:12.990 align:start position:0%
can deliver software to our customers
our<00:00:10.960><c> customers</c><00:00:11.360><c> can</c><00:00:11.480><c> be</c><00:00:11.639><c> highly</c><00:00:12.000><c> regulated</c>

00:00:12.990 --> 00:00:13.000 align:start position:0%
our customers can be highly regulated
 

00:00:13.000 --> 00:00:14.549 align:start position:0%
our customers can be highly regulated
they<00:00:13.120><c> might</c><00:00:13.280><c> be</c><00:00:13.400><c> dealing</c><00:00:13.679><c> with</c><00:00:14.000><c> incredibly</c>

00:00:14.549 --> 00:00:14.559 align:start position:0%
they might be dealing with incredibly
 

00:00:14.559 --> 00:00:16.910 align:start position:0%
they might be dealing with incredibly
sensitive<00:00:15.000><c> personal</c><00:00:15.520><c> data</c><00:00:16.520><c> they</c><00:00:16.640><c> want</c><00:00:16.760><c> to</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
sensitive personal data they want to
 

00:00:16.920 --> 00:00:19.230 align:start position:0%
sensitive personal data they want to
make<00:00:17.039><c> sure</c><00:00:17.240><c> that</c><00:00:17.359><c> their</c><00:00:17.520><c> data</c><00:00:17.760><c> is</c><00:00:17.920><c> kept</c><00:00:18.240><c> safe</c>

00:00:19.230 --> 00:00:19.240 align:start position:0%
make sure that their data is kept safe
 

00:00:19.240 --> 00:00:21.590 align:start position:0%
make sure that their data is kept safe
developers<00:00:19.920><c> may</c><00:00:20.199><c> be</c><00:00:20.400><c> store</c><00:00:20.800><c> our</c><00:00:21.039><c> username</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
developers may be store our username
 

00:00:21.600 --> 00:00:24.390 align:start position:0%
developers may be store our username
password<00:00:22.119><c> or</c><00:00:22.480><c> API</c><00:00:22.920><c> keys</c><00:00:23.320><c> on</c><00:00:23.480><c> PL</c><00:00:23.840><c> text</c><00:00:24.160><c> in</c><00:00:24.279><c> our</c>

00:00:24.390 --> 00:00:24.400 align:start position:0%
password or API keys on PL text in our
 

00:00:24.400 --> 00:00:26.710 align:start position:0%
password or API keys on PL text in our
solos<00:00:24.880><c> Cod</c><00:00:25.160><c> what</c><00:00:25.279><c> the</c><00:00:25.400><c> tool</c><00:00:25.680><c> does</c><00:00:26.080><c> in</c><00:00:26.279><c> terms</c><00:00:26.519><c> of</c>

00:00:26.710 --> 00:00:26.720 align:start position:0%
solos Cod what the tool does in terms of
 

00:00:26.720 --> 00:00:28.910 align:start position:0%
solos Cod what the tool does in terms of
secret<00:00:27.080><c> scanning</c><00:00:27.560><c> is</c><00:00:27.760><c> bring</c><00:00:28.000><c> fun</c><00:00:28.240><c> of</c><00:00:28.439><c> mind</c><00:00:28.720><c> to</c>

00:00:28.910 --> 00:00:28.920 align:start position:0%
secret scanning is bring fun of mind to
 

00:00:28.920 --> 00:00:31.830 align:start position:0%
secret scanning is bring fun of mind to
our<00:00:29.119><c> engineers</c><00:00:30.039><c> that's</c><00:00:30.240><c> not</c><00:00:30.560><c> safe</c><00:00:31.039><c> to</c><00:00:31.240><c> do</c><00:00:31.439><c> it</c>

00:00:31.830 --> 00:00:31.840 align:start position:0%
our engineers that's not safe to do it
 

00:00:31.840 --> 00:00:34.869 align:start position:0%
our engineers that's not safe to do it
and<00:00:32.000><c> it</c><00:00:32.200><c> prompt</c><00:00:32.520><c> them</c><00:00:32.800><c> to</c><00:00:33.079><c> safely</c><00:00:34.079><c> handle</c><00:00:34.680><c> like</c>

00:00:34.869 --> 00:00:34.879 align:start position:0%
and it prompt them to safely handle like
 

00:00:34.879 --> 00:00:37.270 align:start position:0%
and it prompt them to safely handle like
username<00:00:35.520><c> password</c><00:00:36.040><c> Secrets</c><00:00:36.760><c> instead</c><00:00:37.120><c> of</c>

00:00:37.270 --> 00:00:37.280 align:start position:0%
username password Secrets instead of
 

00:00:37.280 --> 00:00:39.110 align:start position:0%
username password Secrets instead of
store<00:00:37.719><c> them</c><00:00:37.960><c> in</c><00:00:38.079><c> the</c><00:00:38.200><c> source</c><00:00:38.520><c> code</c><00:00:38.800><c> in</c><00:00:38.879><c> terms</c>

00:00:39.110 --> 00:00:39.120 align:start position:0%
store them in the source code in terms
 

00:00:39.120 --> 00:00:41.590 align:start position:0%
store them in the source code in terms
of<00:00:39.320><c> security</c><00:00:39.719><c> scanning</c><00:00:40.440><c> we</c><00:00:40.600><c> want</c><00:00:40.719><c> to</c><00:00:41.239><c> catch</c>

00:00:41.590 --> 00:00:41.600 align:start position:0%
of security scanning we want to catch
 

00:00:41.600 --> 00:00:43.790 align:start position:0%
of security scanning we want to catch
that<00:00:41.920><c> at</c><00:00:42.039><c> the</c><00:00:42.239><c> commit</c><00:00:42.760><c> or</c><00:00:43.079><c> at</c><00:00:43.239><c> the</c><00:00:43.480><c> pool</c>

00:00:43.790 --> 00:00:43.800 align:start position:0%
that at the commit or at the pool
 

00:00:43.800 --> 00:00:46.510 align:start position:0%
that at the commit or at the pool
request<00:00:44.360><c> level</c><00:00:45.039><c> one</c><00:00:45.160><c> of</c><00:00:45.320><c> the</c><00:00:45.480><c> main</c><00:00:46.039><c> feedbacks</c>

00:00:46.510 --> 00:00:46.520 align:start position:0%
request level one of the main feedbacks
 

00:00:46.520 --> 00:00:48.630 align:start position:0%
request level one of the main feedbacks
that<00:00:46.680><c> we</c><00:00:46.800><c> collected</c><00:00:47.199><c> from</c><00:00:47.440><c> developers</c><00:00:48.160><c> it</c><00:00:48.320><c> was</c>

00:00:48.630 --> 00:00:48.640 align:start position:0%
that we collected from developers it was
 

00:00:48.640 --> 00:00:51.229 align:start position:0%
that we collected from developers it was
that<00:00:49.039><c> security</c><00:00:49.520><c> was</c><00:00:49.879><c> outside</c><00:00:50.680><c> of</c><00:00:50.840><c> software</c>

00:00:51.229 --> 00:00:51.239 align:start position:0%
that security was outside of software
 

00:00:51.239 --> 00:00:53.670 align:start position:0%
that security was outside of software
development<00:00:51.960><c> uh</c><00:00:52.160><c> cycle</c><00:00:52.840><c> you're</c><00:00:53.239><c> creating</c>

00:00:53.670 --> 00:00:53.680 align:start position:0%
development uh cycle you're creating
 

00:00:53.680 --> 00:00:56.029 align:start position:0%
development uh cycle you're creating
your<00:00:53.960><c> new</c><00:00:54.120><c> feature</c><00:00:54.879><c> and</c><00:00:55.079><c> maybe</c><00:00:55.440><c> the</c><00:00:55.600><c> security</c>

00:00:56.029 --> 00:00:56.039 align:start position:0%
your new feature and maybe the security
 

00:00:56.039 --> 00:00:58.590 align:start position:0%
your new feature and maybe the security
feedback<00:00:56.760><c> is</c><00:00:56.960><c> coming</c><00:00:57.680><c> when</c><00:00:57.920><c> that</c><00:00:58.160><c> product</c><00:00:58.480><c> is</c>

00:00:58.590 --> 00:00:58.600 align:start position:0%
feedback is coming when that product is
 

00:00:58.600 --> 00:01:00.189 align:start position:0%
feedback is coming when that product is
going<00:00:58.760><c> to</c><00:00:58.879><c> be</c><00:00:59.000><c> released</c><00:00:59.440><c> which</c><00:00:59.640><c> can</c><00:01:00.000><c> take</c>

00:01:00.189 --> 00:01:00.199 align:start position:0%
going to be released which can take
 

00:01:00.199 --> 00:01:02.389 align:start position:0%
going to be released which can take
easily<00:01:00.600><c> weeks</c><00:01:01.000><c> or</c><00:01:01.160><c> even</c><00:01:01.440><c> months</c><00:01:02.079><c> we</c><00:01:02.199><c> were</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
easily weeks or even months we were
 

00:01:02.399 --> 00:01:04.030 align:start position:0%
easily weeks or even months we were
looking<00:01:02.640><c> for</c><00:01:02.879><c> Alternatives</c><00:01:03.559><c> that</c><00:01:03.719><c> could</c>

00:01:04.030 --> 00:01:04.040 align:start position:0%
looking for Alternatives that could
 

00:01:04.040 --> 00:01:06.390 align:start position:0%
looking for Alternatives that could
introduce<00:01:04.920><c> security</c><00:01:05.320><c> feedback</c><00:01:05.840><c> as</c><00:01:06.040><c> part</c><00:01:06.240><c> of</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
introduce security feedback as part of
 

00:01:06.400 --> 00:01:08.830 align:start position:0%
introduce security feedback as part of
your<00:01:06.600><c> day-to-day</c><00:01:07.360><c> so</c><00:01:07.560><c> with</c><00:01:07.720><c> Advance</c><00:01:08.119><c> Security</c>

00:01:08.830 --> 00:01:08.840 align:start position:0%
your day-to-day so with Advance Security
 

00:01:08.840 --> 00:01:10.950 align:start position:0%
your day-to-day so with Advance Security
we<00:01:09.040><c> had</c><00:01:09.200><c> a</c><00:01:09.360><c> way</c><00:01:09.640><c> not</c><00:01:09.840><c> only</c><00:01:10.119><c> to</c><00:01:10.320><c> identify</c><00:01:10.799><c> the</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
we had a way not only to identify the
 

00:01:10.960 --> 00:01:13.789 align:start position:0%
we had a way not only to identify the
secrets<00:01:11.520><c> as</c><00:01:11.680><c> part</c><00:01:11.840><c> of</c><00:01:12.000><c> the</c><00:01:12.200><c> process</c><00:01:12.960><c> but</c><00:01:13.240><c> even</c>

00:01:13.789 --> 00:01:13.799 align:start position:0%
secrets as part of the process but even
 

00:01:13.799 --> 00:01:16.670 align:start position:0%
secrets as part of the process but even
to<00:01:14.280><c> uh</c><00:01:14.439><c> prevent</c><00:01:14.880><c> the</c><00:01:15.080><c> secret</c><00:01:15.479><c> to</c><00:01:15.640><c> be</c><00:01:15.960><c> into</c><00:01:16.240><c> your</c>

00:01:16.670 --> 00:01:16.680 align:start position:0%
to uh prevent the secret to be into your
 

00:01:16.680 --> 00:01:19.270 align:start position:0%
to uh prevent the secret to be into your
repository<00:01:17.400><c> history</c><00:01:18.280><c> there's</c><00:01:18.640><c> a</c><00:01:18.840><c> tab</c>

00:01:19.270 --> 00:01:19.280 align:start position:0%
repository history there's a tab
 

00:01:19.280 --> 00:01:22.109 align:start position:0%
repository history there's a tab
directly<00:01:19.680><c> in</c><00:01:19.799><c> your</c><00:01:19.960><c> repo</c><00:01:20.720><c> that</c><00:01:21.200><c> shows</c><00:01:21.560><c> you</c><00:01:21.960><c> all</c>

00:01:22.109 --> 00:01:22.119 align:start position:0%
directly in your repo that shows you all
 

00:01:22.119 --> 00:01:23.950 align:start position:0%
directly in your repo that shows you all
of<00:01:22.240><c> your</c><00:01:22.439><c> vulnerabilities</c><00:01:23.439><c> they're</c><00:01:23.680><c> front</c>

00:01:23.950 --> 00:01:23.960 align:start position:0%
of your vulnerabilities they're front
 

00:01:23.960 --> 00:01:26.069 align:start position:0%
of your vulnerabilities they're front
and<00:01:24.119><c> center</c><00:01:24.720><c> you</c><00:01:24.840><c> can't</c><00:01:25.079><c> ignore</c><00:01:25.400><c> them</c><00:01:25.880><c> uh</c>

00:01:26.069 --> 00:01:26.079 align:start position:0%
and center you can't ignore them uh
 

00:01:26.079 --> 00:01:28.469 align:start position:0%
and center you can't ignore them uh
that's<00:01:26.360><c> been</c><00:01:26.840><c> really</c><00:01:27.159><c> helpful</c><00:01:27.840><c> for</c>

00:01:28.469 --> 00:01:28.479 align:start position:0%
that's been really helpful for
 

00:01:28.479 --> 00:01:30.789 align:start position:0%
that's been really helpful for
encouraging<00:01:28.960><c> our</c><00:01:29.159><c> developers</c><00:01:29.960><c> to</c><00:01:30.479><c> take</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
encouraging our developers to take
 

00:01:30.799 --> 00:01:33.510 align:start position:0%
encouraging our developers to take
control<00:01:31.320><c> over</c><00:01:31.920><c> their</c><00:01:32.320><c> own</c><00:01:32.840><c> application</c>

00:01:33.510 --> 00:01:33.520 align:start position:0%
control over their own application
 

00:01:33.520 --> 00:01:36.149 align:start position:0%
control over their own application
security<00:01:34.439><c> and</c><00:01:34.759><c> it's</c><00:01:35.000><c> empowered</c><00:01:35.479><c> them</c><00:01:35.680><c> too</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
security and it's empowered them too
 

00:01:36.159 --> 00:01:39.109 align:start position:0%
security and it's empowered them too
because<00:01:36.600><c> there's</c><00:01:36.840><c> often</c><00:01:37.240><c> suggestions</c><00:01:38.240><c> on</c><00:01:38.799><c> how</c>

00:01:39.109 --> 00:01:39.119 align:start position:0%
because there's often suggestions on how
 

00:01:39.119 --> 00:01:41.670 align:start position:0%
because there's often suggestions on how
to<00:01:39.640><c> improve</c><00:01:40.240><c> Security</c><00:01:40.840><c> in</c><00:01:41.280><c> each</c><00:01:41.439><c> of</c><00:01:41.560><c> the</c>

00:01:41.670 --> 00:01:41.680 align:start position:0%
to improve Security in each of the
 

00:01:41.680 --> 00:01:44.190 align:start position:0%
to improve Security in each of the
vulnerability<00:01:42.360><c> reports</c><00:01:43.360><c> we</c><00:01:43.560><c> tried</c><00:01:43.799><c> to</c><00:01:43.920><c> meet</c>

00:01:44.190 --> 00:01:44.200 align:start position:0%
vulnerability reports we tried to meet
 

00:01:44.200 --> 00:01:45.670 align:start position:0%
vulnerability reports we tried to meet
the<00:01:44.360><c> developers</c><00:01:44.920><c> where</c><00:01:45.119><c> they</c><00:01:45.240><c> are</c><00:01:45.399><c> in</c><00:01:45.560><c> their</c>

00:01:45.670 --> 00:01:45.680 align:start position:0%
the developers where they are in their
 

00:01:45.680 --> 00:01:47.950 align:start position:0%
the developers where they are in their
development<00:01:46.280><c> process</c><00:01:46.840><c> the</c><00:01:47.000><c> code</c><00:01:47.240><c> we</c><00:01:47.439><c> write</c><00:01:47.880><c> is</c>

00:01:47.950 --> 00:01:47.960 align:start position:0%
development process the code we write is
 

00:01:47.960 --> 00:01:50.109 align:start position:0%
development process the code we write is
it<00:01:48.240><c> secure</c><00:01:48.759><c> is</c><00:01:48.840><c> it</c><00:01:49.079><c> safe</c><00:01:49.360><c> does</c><00:01:49.479><c> it</c><00:01:49.640><c> contain</c><00:01:49.960><c> any</c>

00:01:50.109 --> 00:01:50.119 align:start position:0%
it secure is it safe does it contain any
 

00:01:50.119 --> 00:01:51.670 align:start position:0%
it secure is it safe does it contain any
secrets<00:01:50.640><c> so</c><00:01:50.759><c> when</c><00:01:50.880><c> a</c><00:01:51.000><c> developer</c><00:01:51.360><c> is</c><00:01:51.479><c> looking</c>

00:01:51.670 --> 00:01:51.680 align:start position:0%
secrets so when a developer is looking
 

00:01:51.680 --> 00:01:53.990 align:start position:0%
secrets so when a developer is looking
to<00:01:51.880><c> push</c><00:01:52.119><c> that</c><00:01:52.280><c> code</c><00:01:52.520><c> in</c><00:01:52.640><c> for</c><00:01:52.799><c> the</c><00:01:52.960><c> first</c><00:01:53.240><c> time</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
to push that code in for the first time
 

00:01:54.000 --> 00:01:55.630 align:start position:0%
to push that code in for the first time
we<00:01:54.119><c> want</c><00:01:54.320><c> secret</c><00:01:54.680><c> detection</c><00:01:55.040><c> to</c><00:01:55.159><c> happen</c><00:01:55.479><c> right</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
we want secret detection to happen right
 

00:01:55.640 --> 00:01:57.510 align:start position:0%
we want secret detection to happen right
there<00:01:55.799><c> in</c><00:01:55.960><c> that</c><00:01:56.159><c> moment</c><00:01:56.640><c> get</c><00:01:56.960><c> helped</c>

00:01:57.510 --> 00:01:57.520 align:start position:0%
there in that moment get helped
 

00:01:57.520 --> 00:01:59.350 align:start position:0%
there in that moment get helped
developers<00:01:58.200><c> with</c><00:01:58.640><c> visibility</c><00:01:59.159><c> and</c><00:01:59.280><c> the</c>

00:01:59.350 --> 00:01:59.360 align:start position:0%
developers with visibility and the
 

00:01:59.360 --> 00:02:01.469 align:start position:0%
developers with visibility and the
transparen<00:02:00.159><c> into</c><00:02:00.360><c> the</c><00:02:00.520><c> security</c><00:02:01.039><c> health</c><00:02:01.360><c> of</c>

00:02:01.469 --> 00:02:01.479 align:start position:0%
transparen into the security health of
 

00:02:01.479 --> 00:02:04.109 align:start position:0%
transparen into the security health of
their<00:02:01.680><c> code</c><00:02:01.920><c> base</c><00:02:02.799><c> the</c><00:02:02.920><c> minute</c><00:02:03.600><c> a</c><00:02:03.680><c> secret</c>

00:02:04.109 --> 00:02:04.119 align:start position:0%
their code base the minute a secret
 

00:02:04.119 --> 00:02:06.230 align:start position:0%
their code base the minute a secret
makes<00:02:04.360><c> it</c><00:02:04.880><c> onto</c><00:02:05.320><c> get</c><00:02:05.600><c> you</c><00:02:05.719><c> essentially</c><00:02:06.119><c> need</c>

00:02:06.230 --> 00:02:06.240 align:start position:0%
makes it onto get you essentially need
 

00:02:06.240 --> 00:02:08.749 align:start position:0%
makes it onto get you essentially need
to<00:02:06.399><c> consider</c><00:02:06.759><c> it</c><00:02:06.880><c> to</c><00:02:07.079><c> have</c><00:02:07.280><c> been</c><00:02:08.080><c> compromises</c>

00:02:08.749 --> 00:02:08.759 align:start position:0%
to consider it to have been compromises
 

00:02:08.759 --> 00:02:10.589 align:start position:0%
to consider it to have been compromises
enabling<00:02:09.200><c> push</c><00:02:09.479><c> protection</c><00:02:09.920><c> across</c><00:02:10.319><c> all</c><00:02:10.440><c> of</c>

00:02:10.589 --> 00:02:10.599 align:start position:0%
enabling push protection across all of
 

00:02:10.599 --> 00:02:12.350 align:start position:0%
enabling push protection across all of
our<00:02:10.840><c> repositories</c><00:02:11.480><c> means</c><00:02:11.840><c> that</c><00:02:12.080><c> we</c><00:02:12.160><c> don't</c>

00:02:12.350 --> 00:02:12.360 align:start position:0%
our repositories means that we don't
 

00:02:12.360 --> 00:02:18.940 align:start position:0%
our repositories means that we don't
have<00:02:12.480><c> to</c><00:02:12.640><c> work</c><00:02:12.920><c> as</c><00:02:13.120><c> much</c>

00:02:18.940 --> 00:02:18.950 align:start position:0%
 
 

00:02:18.950 --> 00:02:22.030 align:start position:0%
 
[Music]

