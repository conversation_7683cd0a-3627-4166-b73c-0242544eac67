WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.390 align:start position:0%
 
hey<00:00:00.160><c> and</c><00:00:00.280><c> welcome</c><00:00:00.520><c> back</c><00:00:00.680><c> and</c><00:00:00.799><c> in</c><00:00:00.919><c> today's</c>

00:00:01.390 --> 00:00:01.400 align:start position:0%
hey and welcome back and in today's
 

00:00:01.400 --> 00:00:03.149 align:start position:0%
hey and welcome back and in today's
video<00:00:01.599><c> we're</c><00:00:01.719><c> going</c><00:00:01.839><c> to</c><00:00:01.920><c> see</c><00:00:02.120><c> how</c><00:00:02.240><c> to</c><00:00:02.520><c> install</c>

00:00:03.149 --> 00:00:03.159 align:start position:0%
video we're going to see how to install
 

00:00:03.159 --> 00:00:05.670 align:start position:0%
video we're going to see how to install
and<00:00:03.320><c> run</c><00:00:03.719><c> olama</c><00:00:04.359><c> locally</c><00:00:04.720><c> so</c><00:00:04.880><c> we</c><00:00:05.000><c> can</c><00:00:05.120><c> use</c><00:00:05.440><c> any</c>

00:00:05.670 --> 00:00:05.680 align:start position:0%
and run olama locally so we can use any
 

00:00:05.680 --> 00:00:07.990 align:start position:0%
and run olama locally so we can use any
local<00:00:06.080><c> open-</c><00:00:06.440><c> Source</c><00:00:06.720><c> llm</c><00:00:07.359><c> like</c><00:00:07.480><c> I</c><00:00:07.560><c> mentioned</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
local open- Source llm like I mentioned
 

00:00:08.000 --> 00:00:10.350 align:start position:0%
local open- Source llm like I mentioned
olama<00:00:08.400><c> allows</c><00:00:08.599><c> us</c><00:00:08.719><c> to</c><00:00:09.040><c> easily</c><00:00:09.480><c> use</c><00:00:09.920><c> an</c><00:00:10.080><c> open-</c>

00:00:10.350 --> 00:00:10.360 align:start position:0%
olama allows us to easily use an open-
 

00:00:10.360 --> 00:00:12.230 align:start position:0%
olama allows us to easily use an open-
Source<00:00:10.679><c> local</c><00:00:11.000><c> llm</c><00:00:11.519><c> instead</c><00:00:11.719><c> of</c><00:00:11.840><c> paying</c><00:00:12.080><c> the</c>

00:00:12.230 --> 00:00:12.240 align:start position:0%
Source local llm instead of paying the
 

00:00:12.240 --> 00:00:14.110 align:start position:0%
Source local llm instead of paying the
price<00:00:12.440><c> for</c><00:00:12.639><c> open</c><00:00:12.920><c> Ai</c><00:00:13.320><c> and</c><00:00:13.400><c> they</c><00:00:13.559><c> offer</c><00:00:13.839><c> many</c>

00:00:14.110 --> 00:00:14.120 align:start position:0%
price for open Ai and they offer many
 

00:00:14.120 --> 00:00:16.430 align:start position:0%
price for open Ai and they offer many
great<00:00:14.360><c> models</c><00:00:14.799><c> such</c><00:00:14.960><c> as</c><00:00:15.160><c> llama</c><00:00:15.519><c> 2</c><00:00:15.839><c> mistal</c><00:00:16.279><c> and</c>

00:00:16.430 --> 00:00:16.440 align:start position:0%
great models such as llama 2 mistal and
 

00:00:16.440 --> 00:00:18.029 align:start position:0%
great models such as llama 2 mistal and
lava<00:00:16.800><c> now</c><00:00:16.920><c> one</c><00:00:17.000><c> of</c><00:00:17.080><c> the</c><00:00:17.160><c> ways</c><00:00:17.320><c> to</c><00:00:17.400><c> use</c><00:00:17.600><c> a</c><00:00:17.760><c> llama</c>

00:00:18.029 --> 00:00:18.039 align:start position:0%
lava now one of the ways to use a llama
 

00:00:18.039 --> 00:00:19.870 align:start position:0%
lava now one of the ways to use a llama
is<00:00:18.160><c> to</c><00:00:18.279><c> install</c><00:00:18.600><c> a</c><00:00:18.760><c> library</c><00:00:19.160><c> and</c><00:00:19.320><c> they</c><00:00:19.439><c> have</c>

00:00:19.870 --> 00:00:19.880 align:start position:0%
is to install a library and they have
 

00:00:19.880 --> 00:00:21.189 align:start position:0%
is to install a library and they have
many<00:00:20.119><c> different</c><00:00:20.359><c> libraries</c><00:00:20.800><c> for</c><00:00:20.960><c> different</c>

00:00:21.189 --> 00:00:21.199 align:start position:0%
many different libraries for different
 

00:00:21.199 --> 00:00:22.990 align:start position:0%
many different libraries for different
languages<00:00:21.600><c> and</c><00:00:21.760><c> Frameworks</c><00:00:22.560><c> another</c><00:00:22.800><c> way</c><00:00:22.920><c> is</c>

00:00:22.990 --> 00:00:23.000 align:start position:0%
languages and Frameworks another way is
 

00:00:23.000 --> 00:00:24.910 align:start position:0%
languages and Frameworks another way is
to<00:00:23.119><c> run</c><00:00:23.320><c> it</c><00:00:23.439><c> from</c><00:00:23.560><c> a</c><00:00:23.760><c> dock</c><00:00:24.039><c> your</c><00:00:24.199><c> image</c><00:00:24.680><c> you</c><00:00:24.800><c> can</c>

00:00:24.910 --> 00:00:24.920 align:start position:0%
to run it from a dock your image you can
 

00:00:24.920 --> 00:00:27.470 align:start position:0%
to run it from a dock your image you can
also<00:00:25.160><c> download</c><00:00:25.480><c> the</c><00:00:25.560><c> olama</c><00:00:26.080><c> software</c><00:00:26.800><c> for</c><00:00:27.119><c> Mac</c>

00:00:27.470 --> 00:00:27.480 align:start position:0%
also download the olama software for Mac
 

00:00:27.480 --> 00:00:28.990 align:start position:0%
also download the olama software for Mac
Windows<00:00:27.960><c> preview</c><00:00:28.359><c> which</c><00:00:28.480><c> just</c><00:00:28.640><c> came</c><00:00:28.800><c> out</c>

00:00:28.990 --> 00:00:29.000 align:start position:0%
Windows preview which just came out
 

00:00:29.000 --> 00:00:31.630 align:start position:0%
Windows preview which just came out
recently<00:00:29.599><c> and</c><00:00:30.000><c> L</c><00:00:30.560><c> and</c><00:00:30.720><c> then</c><00:00:30.880><c> simply</c><00:00:31.400><c> all</c><00:00:31.519><c> you</c>

00:00:31.630 --> 00:00:31.640 align:start position:0%
recently and L and then simply all you
 

00:00:31.640 --> 00:00:33.350 align:start position:0%
recently and L and then simply all you
need<00:00:31.759><c> to</c><00:00:31.920><c> do</c><00:00:32.079><c> is</c><00:00:32.200><c> you</c><00:00:32.279><c> can</c><00:00:32.439><c> open</c><00:00:32.680><c> up</c><00:00:32.759><c> a</c><00:00:32.880><c> terminal</c>

00:00:33.350 --> 00:00:33.360 align:start position:0%
need to do is you can open up a terminal
 

00:00:33.360 --> 00:00:35.229 align:start position:0%
need to do is you can open up a terminal
and<00:00:33.520><c> type</c><00:00:33.680><c> in</c><00:00:33.879><c> O</c><00:00:34.079><c> llama</c><00:00:34.399><c> run</c><00:00:34.840><c> give</c><00:00:34.960><c> it</c><00:00:35.079><c> the</c>

00:00:35.229 --> 00:00:35.239 align:start position:0%
and type in O llama run give it the
 

00:00:35.239 --> 00:00:37.270 align:start position:0%
and type in O llama run give it the
models<00:00:35.600><c> such</c><00:00:35.800><c> as</c><00:00:35.960><c> llama</c><00:00:36.280><c> 2</c><00:00:36.920><c> and</c><00:00:37.040><c> you</c><00:00:37.120><c> can</c>

00:00:37.270 --> 00:00:37.280 align:start position:0%
models such as llama 2 and you can
 

00:00:37.280 --> 00:00:38.670 align:start position:0%
models such as llama 2 and you can
already<00:00:37.480><c> start</c><00:00:37.719><c> chatting</c><00:00:38.160><c> with</c><00:00:38.280><c> the</c><00:00:38.399><c> local</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
already start chatting with the local
 

00:00:38.680 --> 00:00:41.549 align:start position:0%
already start chatting with the local
llm<00:00:39.320><c> but</c><00:00:39.440><c> to</c><00:00:39.600><c> use</c><00:00:39.800><c> it</c><00:00:40.000><c> with</c><00:00:40.239><c> AI</c><00:00:40.719><c> agents</c><00:00:41.320><c> we</c><00:00:41.440><c> have</c>

00:00:41.549 --> 00:00:41.559 align:start position:0%
llm but to use it with AI agents we have
 

00:00:41.559 --> 00:00:44.150 align:start position:0%
llm but to use it with AI agents we have
to<00:00:41.800><c> integrate</c><00:00:42.239><c> it</c><00:00:42.440><c> into</c><00:00:42.840><c> a</c><00:00:43.039><c> configuration</c><00:00:43.960><c> so</c>

00:00:44.150 --> 00:00:44.160 align:start position:0%
to integrate it into a configuration so
 

00:00:44.160 --> 00:00:46.590 align:start position:0%
to integrate it into a configuration so
that<00:00:44.320><c> our</c><00:00:44.600><c> AI</c><00:00:45.039><c> agent</c><00:00:45.360><c> knows</c><00:00:45.719><c> how</c><00:00:45.960><c> to</c><00:00:46.120><c> use</c><00:00:46.320><c> it</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
that our AI agent knows how to use it
 

00:00:46.600 --> 00:00:48.150 align:start position:0%
that our AI agent knows how to use it
and<00:00:46.719><c> we</c><00:00:46.800><c> can</c><00:00:46.920><c> do</c><00:00:47.079><c> this</c><00:00:47.280><c> by</c><00:00:47.480><c> downloading</c><00:00:47.960><c> a</c>

00:00:48.150 --> 00:00:48.160 align:start position:0%
and we can do this by downloading a
 

00:00:48.160 --> 00:00:51.029 align:start position:0%
and we can do this by downloading a
model<00:00:48.480><c> from</c><00:00:48.719><c> AMA</c><00:00:49.360><c> saying</c><00:00:49.760><c> AMA</c><00:00:50.320><c> pool</c><00:00:50.800><c> and</c><00:00:50.920><c> then</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
model from AMA saying AMA pool and then
 

00:00:51.039 --> 00:00:52.709 align:start position:0%
model from AMA saying AMA pool and then
the<00:00:51.199><c> model</c><00:00:51.520><c> name</c><00:00:52.079><c> we</c><00:00:52.239><c> do</c><00:00:52.359><c> this</c><00:00:52.480><c> in</c><00:00:52.600><c> the</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
the model name we do this in the
 

00:00:52.719 --> 00:00:54.630 align:start position:0%
the model name we do this in the
terminal<00:00:53.160><c> and</c><00:00:53.280><c> now</c><00:00:53.440><c> we</c><00:00:53.559><c> have</c><00:00:53.840><c> that</c><00:00:54.160><c> model</c><00:00:54.480><c> to</c>

00:00:54.630 --> 00:00:54.640 align:start position:0%
terminal and now we have that model to
 

00:00:54.640 --> 00:00:56.670 align:start position:0%
terminal and now we have that model to
use<00:00:54.960><c> locally</c><00:00:55.520><c> what</c><00:00:55.640><c> we</c><00:00:55.760><c> are</c><00:00:55.879><c> going</c><00:00:56.000><c> to</c><00:00:56.160><c> do</c><00:00:56.600><c> is</c>

00:00:56.670 --> 00:00:56.680 align:start position:0%
use locally what we are going to do is
 

00:00:56.680 --> 00:00:58.470 align:start position:0%
use locally what we are going to do is
we're<00:00:56.840><c> going</c><00:00:56.920><c> to</c><00:00:57.120><c> download</c><00:00:57.520><c> olama</c><00:00:58.079><c> software</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
we're going to download olama software
 

00:00:58.480 --> 00:00:59.910 align:start position:0%
we're going to download olama software
for<00:00:58.680><c> the</c><00:00:58.800><c> Mac</c><00:00:59.199><c> then</c><00:00:59.320><c> we're</c><00:00:59.440><c> going</c><00:00:59.519><c> to</c><00:00:59.640><c> pull</c>

00:00:59.910 --> 00:00:59.920 align:start position:0%
for the Mac then we're going to pull
 

00:00:59.920 --> 00:01:01.630 align:start position:0%
for the Mac then we're going to pull
pull<00:01:00.160><c> any</c><00:01:00.359><c> model</c><00:01:00.680><c> I'm</c><00:01:00.800><c> going</c><00:01:00.879><c> to</c><00:01:01.000><c> pull</c><00:01:01.199><c> the</c><00:01:01.320><c> F</c>

00:01:01.630 --> 00:01:01.640 align:start position:0%
pull any model I'm going to pull the F
 

00:01:01.640 --> 00:01:03.430 align:start position:0%
pull any model I'm going to pull the F
model<00:01:02.320><c> and</c><00:01:02.440><c> then</c><00:01:02.519><c> we're</c><00:01:02.640><c> going</c><00:01:02.760><c> to</c><00:01:02.960><c> create</c><00:01:03.280><c> the</c>

00:01:03.430 --> 00:01:03.440 align:start position:0%
model and then we're going to create the
 

00:01:03.440 --> 00:01:05.630 align:start position:0%
model and then we're going to create the
agents<00:01:04.000><c> with</c><00:01:04.119><c> the</c><00:01:04.320><c> configuration</c><00:01:05.080><c> for</c><00:01:05.360><c> a</c>

00:01:05.630 --> 00:01:05.640 align:start position:0%
agents with the configuration for a
 

00:01:05.640 --> 00:01:08.429 align:start position:0%
agents with the configuration for a
local<00:01:06.040><c> host</c><00:01:06.479><c> which</c><00:01:06.600><c> is</c><00:01:06.720><c> how</c><00:01:06.880><c> we</c><00:01:07.040><c> connect</c><00:01:07.720><c> oama</c>

00:01:08.429 --> 00:01:08.439 align:start position:0%
local host which is how we connect oama
 

00:01:08.439 --> 00:01:10.030 align:start position:0%
local host which is how we connect oama
which<00:01:08.560><c> is</c><00:01:08.840><c> going</c><00:01:08.960><c> to</c><00:01:09.080><c> be</c><00:01:09.280><c> running</c><00:01:09.600><c> a</c><00:01:09.759><c> local</c>

00:01:10.030 --> 00:01:10.040 align:start position:0%
which is going to be running a local
 

00:01:10.040 --> 00:01:12.350 align:start position:0%
which is going to be running a local
server<00:01:10.560><c> with</c><00:01:10.720><c> our</c><00:01:10.960><c> agents</c><00:01:11.560><c> let's</c><00:01:11.759><c> get</c><00:01:11.920><c> started</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
server with our agents let's get started
 

00:01:12.360 --> 00:01:13.469 align:start position:0%
server with our agents let's get started
all<00:01:12.479><c> right</c><00:01:12.680><c> well</c><00:01:12.880><c> the</c><00:01:13.000><c> first</c><00:01:13.159><c> thing</c><00:01:13.280><c> we</c><00:01:13.360><c> need</c>

00:01:13.469 --> 00:01:13.479 align:start position:0%
all right well the first thing we need
 

00:01:13.479 --> 00:01:15.550 align:start position:0%
all right well the first thing we need
to<00:01:13.560><c> do</c><00:01:13.799><c> is</c><00:01:14.000><c> download</c><00:01:14.360><c> it</c><00:01:14.520><c> so</c><00:01:14.680><c> if</c><00:01:14.759><c> you</c><00:01:14.880><c> go</c><00:01:15.000><c> to</c><00:01:15.280><c> O</c>

00:01:15.550 --> 00:01:15.560 align:start position:0%
to do is download it so if you go to O
 

00:01:15.560 --> 00:01:18.870 align:start position:0%
to do is download it so if you go to O
llama.com<00:01:16.439><c> in</c><00:01:17.439><c> the</c><00:01:17.560><c> middle</c><00:01:17.920><c> there's</c><00:01:18.119><c> a</c><00:01:18.520><c> small</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
llama.com in the middle there's a small
 

00:01:18.880 --> 00:01:20.550 align:start position:0%
llama.com in the middle there's a small
download<00:01:19.280><c> button</c><00:01:19.600><c> just</c><00:01:19.840><c> click</c><00:01:20.079><c> it</c><00:01:20.400><c> and</c><00:01:20.479><c> then</c>

00:01:20.550 --> 00:01:20.560 align:start position:0%
download button just click it and then
 

00:01:20.560 --> 00:01:22.429 align:start position:0%
download button just click it and then
on<00:01:20.680><c> the</c><00:01:20.799><c> next</c><00:01:20.960><c> screen</c><00:01:21.240><c> you</c><00:01:21.320><c> can</c><00:01:21.479><c> choose</c><00:01:21.799><c> Mac</c><00:01:22.040><c> OS</c>

00:01:22.429 --> 00:01:22.439 align:start position:0%
on the next screen you can choose Mac OS
 

00:01:22.439 --> 00:01:24.749 align:start position:0%
on the next screen you can choose Mac OS
Linux<00:01:22.920><c> or</c><00:01:23.119><c> Windows</c><00:01:23.600><c> I'm</c><00:01:23.680><c> going</c><00:01:23.799><c> to</c><00:01:23.920><c> choose</c><00:01:24.200><c> Mac</c>

00:01:24.749 --> 00:01:24.759 align:start position:0%
Linux or Windows I'm going to choose Mac
 

00:01:24.759 --> 00:01:26.310 align:start position:0%
Linux or Windows I'm going to choose Mac
and<00:01:24.880><c> then</c><00:01:25.079><c> click</c><00:01:25.360><c> download</c><00:01:25.960><c> and</c><00:01:26.079><c> then</c><00:01:26.200><c> after</c>

00:01:26.310 --> 00:01:26.320 align:start position:0%
and then click download and then after
 

00:01:26.320 --> 00:01:27.710 align:start position:0%
and then click download and then after
you're<00:01:26.479><c> done</c><00:01:26.640><c> downloading</c><00:01:27.079><c> it</c><00:01:27.280><c> simply</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
you're done downloading it simply
 

00:01:27.720 --> 00:01:29.350 align:start position:0%
you're done downloading it simply
install<00:01:28.079><c> it</c><00:01:28.240><c> and</c><00:01:28.439><c> run</c><00:01:28.640><c> it</c><00:01:29.000><c> and</c><00:01:29.079><c> you'll</c><00:01:29.240><c> know</c>

00:01:29.350 --> 00:01:29.360 align:start position:0%
install it and run it and you'll know
 

00:01:29.360 --> 00:01:30.390 align:start position:0%
install it and run it and you'll know
you're<00:01:29.479><c> running</c><00:01:29.720><c> it</c><00:01:30.000><c> especially</c><00:01:30.280><c> if</c><00:01:30.320><c> you're</c>

00:01:30.390 --> 00:01:30.400 align:start position:0%
you're running it especially if you're
 

00:01:30.400 --> 00:01:32.069 align:start position:0%
you're running it especially if you're
on<00:01:30.479><c> a</c><00:01:30.600><c> Mac</c><00:01:31.000><c> at</c><00:01:31.119><c> the</c><00:01:31.280><c> top</c><00:01:31.520><c> right</c><00:01:31.680><c> here</c><00:01:31.880><c> it</c><00:01:31.920><c> will</c>

00:01:32.069 --> 00:01:32.079 align:start position:0%
on a Mac at the top right here it will
 

00:01:32.079 --> 00:01:33.590 align:start position:0%
on a Mac at the top right here it will
have<00:01:32.200><c> a</c><00:01:32.320><c> llama</c><00:01:32.680><c> symbol</c><00:01:33.119><c> which</c><00:01:33.240><c> means</c><00:01:33.399><c> you're</c>

00:01:33.590 --> 00:01:33.600 align:start position:0%
have a llama symbol which means you're
 

00:01:33.600 --> 00:01:34.830 align:start position:0%
have a llama symbol which means you're
now<00:01:33.720><c> running</c><00:01:33.960><c> a</c><00:01:34.079><c> local</c><00:01:34.320><c> server</c><00:01:34.680><c> all</c><00:01:34.759><c> right</c>

00:01:34.830 --> 00:01:34.840 align:start position:0%
now running a local server all right
 

00:01:34.840 --> 00:01:36.190 align:start position:0%
now running a local server all right
we're<00:01:34.960><c> going</c><00:01:35.040><c> to</c><00:01:35.119><c> need</c><00:01:35.280><c> two</c><00:01:35.520><c> files</c><00:01:35.799><c> for</c><00:01:35.960><c> our</c>

00:01:36.190 --> 00:01:36.200 align:start position:0%
we're going to need two files for our
 

00:01:36.200 --> 00:01:38.630 align:start position:0%
we're going to need two files for our
example<00:01:36.799><c> a</c><00:01:36.920><c> main</c><00:01:37.240><c> python</c><00:01:37.640><c> file</c><00:01:38.200><c> and</c><00:01:38.320><c> then</c><00:01:38.439><c> an</c>

00:01:38.630 --> 00:01:38.640 align:start position:0%
example a main python file and then an
 

00:01:38.640 --> 00:01:41.069 align:start position:0%
example a main python file and then an
oi<00:01:39.159><c> config</c><00:01:39.560><c> list</c><00:01:39.840><c> Json</c><00:01:40.360><c> file</c><00:01:40.680><c> let's</c><00:01:40.880><c> first</c>

00:01:41.069 --> 00:01:41.079 align:start position:0%
oi config list Json file let's first
 

00:01:41.079 --> 00:01:42.870 align:start position:0%
oi config list Json file let's first
open<00:01:41.280><c> up</c><00:01:41.479><c> the</c><00:01:41.600><c> Json</c><00:01:42.079><c> file</c><00:01:42.520><c> and</c><00:01:42.640><c> all</c><00:01:42.759><c> we're</c>

00:01:42.870 --> 00:01:42.880 align:start position:0%
open up the Json file and all we're
 

00:01:42.880 --> 00:01:44.950 align:start position:0%
open up the Json file and all we're
going<00:01:43.000><c> to</c><00:01:43.240><c> have</c><00:01:43.520><c> here</c><00:01:43.880><c> is</c><00:01:44.040><c> we</c><00:01:44.200><c> need</c><00:01:44.360><c> the</c><00:01:44.520><c> model</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
going to have here is we need the model
 

00:01:44.960 --> 00:01:46.789 align:start position:0%
going to have here is we need the model
so<00:01:45.159><c> I'm</c><00:01:45.240><c> going</c><00:01:45.360><c> to</c><00:01:45.479><c> download</c><00:01:45.880><c> the</c><00:01:46.000><c> FI</c><00:01:46.320><c> model</c><00:01:46.680><c> we</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
so I'm going to download the FI model we
 

00:01:46.799 --> 00:01:49.149 align:start position:0%
so I'm going to download the FI model we
need<00:01:46.960><c> an</c><00:01:47.159><c> API</c><00:01:47.640><c> key</c><00:01:48.200><c> which</c><00:01:48.560><c> just</c><00:01:48.799><c> insert</c>

00:01:49.149 --> 00:01:49.159 align:start position:0%
need an API key which just insert
 

00:01:49.159 --> 00:01:50.749 align:start position:0%
need an API key which just insert
anything<00:01:49.479><c> here</c><00:01:49.640><c> we</c><00:01:50.000><c> need</c><00:01:50.159><c> to</c><00:01:50.360><c> have</c><00:01:50.560><c> the</c>

00:01:50.749 --> 00:01:50.759 align:start position:0%
anything here we need to have the
 

00:01:50.759 --> 00:01:52.389 align:start position:0%
anything here we need to have the
property<00:01:51.240><c> with</c><00:01:51.479><c> something</c><00:01:51.799><c> in</c><00:01:51.920><c> there</c><00:01:52.159><c> but</c><00:01:52.320><c> we</c>

00:01:52.389 --> 00:01:52.399 align:start position:0%
property with something in there but we
 

00:01:52.399 --> 00:01:54.109 align:start position:0%
property with something in there but we
don't<00:01:52.560><c> need</c><00:01:52.719><c> an</c><00:01:52.880><c> API</c><00:01:53.280><c> key</c><00:01:53.640><c> because</c><00:01:53.920><c> we're</c><00:01:54.040><c> not</c>

00:01:54.109 --> 00:01:54.119 align:start position:0%
don't need an API key because we're not
 

00:01:54.119 --> 00:01:55.990 align:start position:0%
don't need an API key because we're not
using<00:01:54.399><c> open</c><00:01:54.600><c> ai's</c><00:01:54.960><c> API</c><00:01:55.439><c> and</c><00:01:55.560><c> then</c><00:01:55.640><c> we</c><00:01:55.719><c> need</c><00:01:55.880><c> the</c>

00:01:55.990 --> 00:01:56.000 align:start position:0%
using open ai's API and then we need the
 

00:01:56.000 --> 00:01:58.109 align:start position:0%
using open ai's API and then we need the
base<00:01:56.240><c> URL</c><00:01:56.719><c> property</c><00:01:57.520><c> which</c><00:01:57.640><c> is</c><00:01:57.759><c> going</c><00:01:57.840><c> to</c><00:01:57.960><c> take</c>

00:01:58.109 --> 00:01:58.119 align:start position:0%
base URL property which is going to take
 

00:01:58.119 --> 00:02:01.550 align:start position:0%
base URL property which is going to take
in<00:01:58.560><c> this</c><00:01:58.840><c> HTTP</c><00:01:59.520><c> UR</c><00:01:59.799><c> URL</c><00:02:00.560><c> which</c><00:02:00.719><c> connects</c><00:02:01.119><c> us</c><00:02:01.280><c> to</c>

00:02:01.550 --> 00:02:01.560 align:start position:0%
in this HTTP UR URL which connects us to
 

00:02:01.560 --> 00:02:03.310 align:start position:0%
in this HTTP UR URL which connects us to
our<00:02:01.759><c> local</c><00:02:02.039><c> olama</c><00:02:02.560><c> server</c><00:02:02.880><c> we</c><00:02:03.000><c> now</c><00:02:03.200><c> have</c>

00:02:03.310 --> 00:02:03.320 align:start position:0%
our local olama server we now have
 

00:02:03.320 --> 00:02:04.910 align:start position:0%
our local olama server we now have
running<00:02:03.719><c> and</c><00:02:03.840><c> now</c><00:02:04.000><c> back</c><00:02:04.079><c> in</c><00:02:04.200><c> our</c><00:02:04.320><c> main</c><00:02:04.520><c> python</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
running and now back in our main python
 

00:02:04.920 --> 00:02:06.630 align:start position:0%
running and now back in our main python
file<00:02:05.360><c> the</c><00:02:05.479><c> first</c><00:02:05.640><c> thing</c><00:02:05.759><c> we</c><00:02:05.880><c> need</c><00:02:06.039><c> to</c><00:02:06.200><c> do</c><00:02:06.439><c> is</c>

00:02:06.630 --> 00:02:06.640 align:start position:0%
file the first thing we need to do is
 

00:02:06.640 --> 00:02:08.910 align:start position:0%
file the first thing we need to do is
install<00:02:07.119><c> Pi</c><00:02:07.360><c> autogen</c><00:02:07.880><c> open</c><00:02:08.080><c> up</c><00:02:08.200><c> your</c><00:02:08.319><c> terminal</c>

00:02:08.910 --> 00:02:08.920 align:start position:0%
install Pi autogen open up your terminal
 

00:02:08.920 --> 00:02:11.110 align:start position:0%
install Pi autogen open up your terminal
type<00:02:09.200><c> pip</c><00:02:09.440><c> install</c><00:02:09.879><c> pi</c><00:02:10.119><c> autogen</c><00:02:10.640><c> and</c><00:02:10.800><c> install</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
type pip install pi autogen and install
 

00:02:11.120 --> 00:02:12.470 align:start position:0%
type pip install pi autogen and install
it<00:02:11.280><c> once</c><00:02:11.400><c> you're</c><00:02:11.560><c> done</c><00:02:11.760><c> with</c><00:02:11.920><c> that</c><00:02:12.200><c> just</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
it once you're done with that just
 

00:02:12.480 --> 00:02:14.430 align:start position:0%
it once you're done with that just
import<00:02:12.879><c> autogen</c><00:02:13.680><c> and</c><00:02:13.800><c> we're</c><00:02:13.920><c> going</c><00:02:14.040><c> to</c><00:02:14.239><c> use</c>

00:02:14.430 --> 00:02:14.440 align:start position:0%
import autogen and we're going to use
 

00:02:14.440 --> 00:02:16.190 align:start position:0%
import autogen and we're going to use
the<00:02:14.640><c> config</c><00:02:15.000><c> list</c><00:02:15.280><c> that</c><00:02:15.400><c> we</c><00:02:15.640><c> just</c><00:02:15.920><c> talked</c>

00:02:16.190 --> 00:02:16.200 align:start position:0%
the config list that we just talked
 

00:02:16.200 --> 00:02:17.990 align:start position:0%
the config list that we just talked
about<00:02:16.440><c> in</c><00:02:16.519><c> the</c><00:02:16.720><c> previous</c><00:02:17.120><c> video</c><00:02:17.760><c> we're</c><00:02:17.879><c> going</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
about in the previous video we're going
 

00:02:18.000 --> 00:02:20.390 align:start position:0%
about in the previous video we're going
to<00:02:18.080><c> use</c><00:02:18.239><c> the</c><00:02:18.400><c> config</c><00:02:18.760><c> list</c><00:02:19.040><c> from</c><00:02:19.319><c> Json</c><00:02:19.920><c> method</c>

00:02:20.390 --> 00:02:20.400 align:start position:0%
to use the config list from Json method
 

00:02:20.400 --> 00:02:22.670 align:start position:0%
to use the config list from Json method
and<00:02:20.640><c> all</c><00:02:20.760><c> we</c><00:02:20.920><c> need</c><00:02:21.280><c> here</c><00:02:21.560><c> is</c><00:02:21.680><c> to</c><00:02:21.920><c> put</c><00:02:22.120><c> in</c><00:02:22.519><c> the</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
and all we need here is to put in the
 

00:02:22.680 --> 00:02:25.270 align:start position:0%
and all we need here is to put in the
config<00:02:23.040><c> list</c><00:02:23.280><c> Json</c><00:02:23.800><c> file</c><00:02:24.200><c> we</c><00:02:24.440><c> just</c><00:02:24.680><c> created</c><00:02:25.160><c> we</c>

00:02:25.270 --> 00:02:25.280 align:start position:0%
config list Json file we just created we
 

00:02:25.280 --> 00:02:27.070 align:start position:0%
config list Json file we just created we
have<00:02:25.360><c> an</c><00:02:25.480><c> assistant</c><00:02:25.840><c> agent</c><00:02:26.360><c> for</c><00:02:26.519><c> the</c><00:02:26.640><c> llm</c>

00:02:27.070 --> 00:02:27.080 align:start position:0%
have an assistant agent for the llm
 

00:02:27.080 --> 00:02:28.750 align:start position:0%
have an assistant agent for the llm
config<00:02:27.560><c> property</c><00:02:28.040><c> we</c><00:02:28.200><c> just</c><00:02:28.360><c> give</c><00:02:28.519><c> it</c><00:02:28.640><c> the</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
config property we just give it the
 

00:02:28.760 --> 00:02:30.550 align:start position:0%
config property we just give it the
config<00:02:29.080><c> list</c><00:02:29.280><c> we</c><00:02:29.400><c> just</c><00:02:29.519><c> created</c><00:02:30.160><c> which</c><00:02:30.280><c> means</c>

00:02:30.550 --> 00:02:30.560 align:start position:0%
config list we just created which means
 

00:02:30.560 --> 00:02:32.030 align:start position:0%
config list we just created which means
this<00:02:30.720><c> AI</c><00:02:31.040><c> agent</c><00:02:31.280><c> is</c><00:02:31.360><c> going</c><00:02:31.440><c> to</c><00:02:31.560><c> talk</c><00:02:31.720><c> to</c><00:02:31.840><c> our</c>

00:02:32.030 --> 00:02:32.040 align:start position:0%
this AI agent is going to talk to our
 

00:02:32.040 --> 00:02:34.630 align:start position:0%
this AI agent is going to talk to our
local<00:02:32.319><c> oama</c><00:02:32.840><c> server</c><00:02:33.519><c> with</c><00:02:33.720><c> the</c><00:02:33.879><c> F</c><00:02:34.160><c> model</c><00:02:34.560><c> then</c>

00:02:34.630 --> 00:02:34.640 align:start position:0%
local oama server with the F model then
 

00:02:34.640 --> 00:02:36.750 align:start position:0%
local oama server with the F model then
we<00:02:34.760><c> have</c><00:02:34.840><c> a</c><00:02:34.920><c> user</c><00:02:35.239><c> proxy</c><00:02:35.640><c> agent</c><00:02:36.160><c> for</c><00:02:36.319><c> the</c><00:02:36.480><c> code</c>

00:02:36.750 --> 00:02:36.760 align:start position:0%
we have a user proxy agent for the code
 

00:02:36.760 --> 00:02:38.589 align:start position:0%
we have a user proxy agent for the code
execution<00:02:37.280><c> config</c><00:02:37.840><c> the</c><00:02:37.959><c> working</c><00:02:38.200><c> directory</c>

00:02:38.589 --> 00:02:38.599 align:start position:0%
execution config the working directory
 

00:02:38.599 --> 00:02:40.030 align:start position:0%
execution config the working directory
is<00:02:38.680><c> going</c><00:02:38.760><c> to</c><00:02:38.840><c> be</c><00:02:38.920><c> called</c><00:02:39.159><c> coding</c><00:02:39.599><c> and</c><00:02:39.760><c> I'm</c><00:02:39.879><c> not</c>

00:02:40.030 --> 00:02:40.040 align:start position:0%
is going to be called coding and I'm not
 

00:02:40.040 --> 00:02:41.470 align:start position:0%
is going to be called coding and I'm not
going<00:02:40.120><c> to</c><00:02:40.239><c> use</c><00:02:40.440><c> Docker</c><00:02:40.920><c> then</c><00:02:41.040><c> we</c><00:02:41.159><c> simply</c>

00:02:41.470 --> 00:02:41.480 align:start position:0%
going to use Docker then we simply
 

00:02:41.480 --> 00:02:43.550 align:start position:0%
going to use Docker then we simply
initiate<00:02:41.879><c> a</c><00:02:42.040><c> chat</c><00:02:42.440><c> with</c><00:02:42.599><c> the</c><00:02:42.760><c> assistant</c><00:02:43.360><c> to</c>

00:02:43.550 --> 00:02:43.560 align:start position:0%
initiate a chat with the assistant to
 

00:02:43.560 --> 00:02:45.830 align:start position:0%
initiate a chat with the assistant to
have<00:02:43.680><c> it</c><00:02:43.800><c> write</c><00:02:44.080><c> a</c><00:02:44.239><c> python</c><00:02:44.680><c> file</c><00:02:45.200><c> plotting</c><00:02:45.560><c> out</c>

00:02:45.830 --> 00:02:45.840 align:start position:0%
have it write a python file plotting out
 

00:02:45.840 --> 00:02:47.670 align:start position:0%
have it write a python file plotting out
the<00:02:46.000><c> top</c><00:02:46.159><c> 10</c><00:02:46.360><c> countries</c><00:02:46.680><c> by</c><00:02:46.879><c> landmass</c><00:02:47.519><c> now</c>

00:02:47.670 --> 00:02:47.680 align:start position:0%
the top 10 countries by landmass now
 

00:02:47.680 --> 00:02:49.430 align:start position:0%
the top 10 countries by landmass now
there<00:02:47.760><c> are</c><00:02:47.959><c> two</c><00:02:48.200><c> things</c><00:02:48.560><c> that</c><00:02:48.680><c> we</c><00:02:48.840><c> need</c><00:02:49.000><c> to</c><00:02:49.200><c> do</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
there are two things that we need to do
 

00:02:49.440 --> 00:02:51.509 align:start position:0%
there are two things that we need to do
here<00:02:49.800><c> the</c><00:02:49.959><c> first</c><00:02:50.200><c> is</c><00:02:50.519><c> download</c><00:02:50.959><c> our</c><00:02:51.159><c> model</c>

00:02:51.509 --> 00:02:51.519 align:start position:0%
here the first is download our model
 

00:02:51.519 --> 00:02:53.790 align:start position:0%
here the first is download our model
you're<00:02:51.640><c> going</c><00:02:51.760><c> to</c><00:02:51.879><c> type</c><00:02:52.040><c> oama</c><00:02:53.040><c> pull</c><00:02:53.480><c> and</c><00:02:53.599><c> then</c>

00:02:53.790 --> 00:02:53.800 align:start position:0%
you're going to type oama pull and then
 

00:02:53.800 --> 00:02:55.270 align:start position:0%
you're going to type oama pull and then
whichever<00:02:54.120><c> model</c><00:02:54.440><c> you</c><00:02:54.560><c> want</c><00:02:54.800><c> so</c><00:02:54.879><c> if</c><00:02:54.959><c> you</c><00:02:55.040><c> want</c>

00:02:55.270 --> 00:02:55.280 align:start position:0%
whichever model you want so if you want
 

00:02:55.280 --> 00:02:57.830 align:start position:0%
whichever model you want so if you want
llama<00:02:55.599><c> 2</c><00:02:55.959><c> you'll</c><00:02:56.239><c> type</c><00:02:56.400><c> in</c><00:02:56.519><c> llama</c><00:02:56.840><c> 2</c><00:02:57.599><c> I'm</c><00:02:57.760><c> going</c>

00:02:57.830 --> 00:02:57.840 align:start position:0%
llama 2 you'll type in llama 2 I'm going
 

00:02:57.840 --> 00:02:59.630 align:start position:0%
llama 2 you'll type in llama 2 I'm going
to<00:02:57.959><c> use</c><00:02:58.200><c> fi</c><00:02:58.720><c> so</c><00:02:58.879><c> I'm</c><00:02:58.959><c> just</c><00:02:59.040><c> going</c><00:02:59.120><c> to</c><00:02:59.200><c> type</c><00:02:59.360><c> in</c><00:02:59.440><c> F</c>

00:02:59.630 --> 00:02:59.640 align:start position:0%
to use fi so I'm just going to type in F
 

00:02:59.640 --> 00:03:00.949 align:start position:0%
to use fi so I'm just going to type in F
and<00:02:59.840><c> press</c><00:03:00.040><c> enter</c><00:03:00.360><c> you'll</c><00:03:00.560><c> know</c><00:03:00.720><c> when</c><00:03:00.800><c> it's</c>

00:03:00.949 --> 00:03:00.959 align:start position:0%
and press enter you'll know when it's
 

00:03:00.959 --> 00:03:03.190 align:start position:0%
and press enter you'll know when it's
done<00:03:01.319><c> whenever</c><00:03:01.800><c> it's</c><00:03:02.400><c> installed</c><00:03:02.920><c> everything</c>

00:03:03.190 --> 00:03:03.200 align:start position:0%
done whenever it's installed everything
 

00:03:03.200 --> 00:03:04.670 align:start position:0%
done whenever it's installed everything
you<00:03:03.319><c> need</c><00:03:03.560><c> for</c><00:03:03.760><c> this</c><00:03:03.920><c> model</c><00:03:04.360><c> and</c><00:03:04.480><c> you've</c>

00:03:04.670 --> 00:03:04.680 align:start position:0%
you need for this model and you've
 

00:03:04.680 --> 00:03:06.309 align:start position:0%
you need for this model and you've
gotten<00:03:04.920><c> a</c><00:03:05.080><c> success</c><00:03:05.680><c> and</c><00:03:05.760><c> now</c><00:03:05.879><c> I</c><00:03:05.959><c> can</c><00:03:06.040><c> simply</c>

00:03:06.309 --> 00:03:06.319 align:start position:0%
gotten a success and now I can simply
 

00:03:06.319 --> 00:03:08.949 align:start position:0%
gotten a success and now I can simply
type<00:03:06.480><c> in</c><00:03:06.680><c> Python</c><00:03:07.000><c> 3</c><00:03:07.640><c> main.py</c><00:03:08.640><c> and</c><00:03:08.720><c> now</c><00:03:08.840><c> it's</c>

00:03:08.949 --> 00:03:08.959 align:start position:0%
type in Python 3 main.py and now it's
 

00:03:08.959 --> 00:03:10.550 align:start position:0%
type in Python 3 main.py and now it's
going<00:03:09.080><c> to</c><00:03:09.200><c> connect</c><00:03:09.480><c> to</c><00:03:09.640><c> our</c><00:03:09.799><c> olama</c><00:03:10.280><c> local</c>

00:03:10.550 --> 00:03:10.560 align:start position:0%
going to connect to our olama local
 

00:03:10.560 --> 00:03:12.509 align:start position:0%
going to connect to our olama local
server<00:03:10.959><c> and</c><00:03:11.120><c> run</c><00:03:11.560><c> all</c><00:03:11.680><c> right</c><00:03:11.799><c> so</c><00:03:11.959><c> it</c><00:03:12.080><c> worked</c><00:03:12.400><c> it</c>

00:03:12.509 --> 00:03:12.519 align:start position:0%
server and run all right so it worked it
 

00:03:12.519 --> 00:03:14.470 align:start position:0%
server and run all right so it worked it
connected<00:03:12.840><c> to</c><00:03:13.000><c> our</c><00:03:13.200><c> local</c><00:03:13.519><c> olama</c><00:03:14.040><c> server</c>

00:03:14.470 --> 00:03:14.480 align:start position:0%
connected to our local olama server
 

00:03:14.480 --> 00:03:16.589 align:start position:0%
connected to our local olama server
initiated<00:03:14.959><c> chat</c><00:03:15.159><c> with</c><00:03:15.280><c> the</c><00:03:15.400><c> assistant</c><00:03:15.799><c> agent</c>

00:03:16.589 --> 00:03:16.599 align:start position:0%
initiated chat with the assistant agent
 

00:03:16.599 --> 00:03:18.149 align:start position:0%
initiated chat with the assistant agent
and<00:03:16.760><c> it</c><00:03:16.879><c> didn't</c><00:03:17.120><c> really</c><00:03:17.360><c> give</c><00:03:17.519><c> me</c><00:03:17.640><c> the</c><00:03:17.840><c> python</c>

00:03:18.149 --> 00:03:18.159 align:start position:0%
and it didn't really give me the python
 

00:03:18.159 --> 00:03:20.910 align:start position:0%
and it didn't really give me the python
code<00:03:18.440><c> it</c><00:03:18.920><c> essentially</c><00:03:19.599><c> gave</c><00:03:19.760><c> me</c><00:03:20.159><c> a</c><00:03:20.360><c> bunch</c><00:03:20.599><c> of</c>

00:03:20.910 --> 00:03:20.920 align:start position:0%
code it essentially gave me a bunch of
 

00:03:20.920 --> 00:03:23.070 align:start position:0%
code it essentially gave me a bunch of
information<00:03:21.480><c> on</c><00:03:22.000><c> what</c><00:03:22.159><c> I</c><00:03:22.360><c> could</c><00:03:22.599><c> do</c><00:03:22.879><c> and</c><00:03:23.000><c> I</c>

00:03:23.070 --> 00:03:23.080 align:start position:0%
information on what I could do and I
 

00:03:23.080 --> 00:03:25.110 align:start position:0%
information on what I could do and I
think<00:03:23.200><c> some</c><00:03:23.360><c> of</c><00:03:23.519><c> this</c><00:03:23.799><c> has</c><00:03:24.280><c> nothing</c><00:03:24.640><c> to</c><00:03:24.920><c> maybe</c>

00:03:25.110 --> 00:03:25.120 align:start position:0%
think some of this has nothing to maybe
 

00:03:25.120 --> 00:03:27.630 align:start position:0%
think some of this has nothing to maybe
even<00:03:25.319><c> do</c><00:03:25.519><c> with</c><00:03:25.720><c> this</c><00:03:26.000><c> but</c><00:03:26.840><c> that's</c><00:03:27.080><c> fine</c><00:03:27.360><c> I</c><00:03:27.440><c> even</c>

00:03:27.630 --> 00:03:27.640 align:start position:0%
even do with this but that's fine I even
 

00:03:27.640 --> 00:03:29.270 align:start position:0%
even do with this but that's fine I even
asked<00:03:27.840><c> it</c><00:03:27.959><c> to</c><00:03:28.120><c> just</c><00:03:28.360><c> write</c><00:03:28.720><c> python</c><00:03:29.080><c> code</c>

00:03:29.270 --> 00:03:29.280 align:start position:0%
asked it to just write python code
 

00:03:29.280 --> 00:03:31.429 align:start position:0%
asked it to just write python code
please<00:03:29.799><c> and</c><00:03:30.239><c> it</c><00:03:30.400><c> tells</c><00:03:30.640><c> me</c><00:03:30.920><c> that</c><00:03:31.159><c> it's</c>

00:03:31.429 --> 00:03:31.439 align:start position:0%
please and it tells me that it's
 

00:03:31.439 --> 00:03:33.110 align:start position:0%
please and it tells me that it's
impossible<00:03:32.280><c> and</c><00:03:32.400><c> now</c><00:03:32.519><c> you</c><00:03:32.640><c> know</c><00:03:32.799><c> how</c><00:03:32.879><c> to</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
impossible and now you know how to
 

00:03:33.120 --> 00:03:35.830 align:start position:0%
impossible and now you know how to
install<00:03:33.680><c> and</c><00:03:33.920><c> run</c><00:03:34.319><c> a</c><00:03:34.480><c> local</c><00:03:34.799><c> AMA</c><00:03:35.280><c> server</c><00:03:35.640><c> and</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
install and run a local AMA server and
 

00:03:35.840 --> 00:03:37.509 align:start position:0%
install and run a local AMA server and
download<00:03:36.200><c> any</c><00:03:36.360><c> model</c><00:03:36.640><c> you</c><00:03:36.760><c> want</c><00:03:37.040><c> and</c><00:03:37.159><c> then</c><00:03:37.319><c> use</c>

00:03:37.509 --> 00:03:37.519 align:start position:0%
download any model you want and then use
 

00:03:37.519 --> 00:03:39.470 align:start position:0%
download any model you want and then use
it<00:03:37.760><c> in</c><00:03:37.879><c> your</c><00:03:38.000><c> agent</c><00:03:38.319><c> workflow</c><00:03:38.959><c> this</c><00:03:39.040><c> is</c><00:03:39.239><c> day</c>

00:03:39.470 --> 00:03:39.480 align:start position:0%
it in your agent workflow this is day
 

00:03:39.480 --> 00:03:41.990 align:start position:0%
it in your agent workflow this is day
six<00:03:39.840><c> of</c><00:03:39.959><c> my</c><00:03:40.080><c> 31</c><00:03:40.480><c> days</c><00:03:40.680><c> of</c><00:03:40.959><c> videos</c><00:03:41.560><c> if</c><00:03:41.680><c> you</c><00:03:41.799><c> need</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
six of my 31 days of videos if you need
 

00:03:42.000 --> 00:03:43.509 align:start position:0%
six of my 31 days of videos if you need
more<00:03:42.200><c> audio</c><00:03:42.560><c> genen</c><00:03:42.760><c> videos</c><00:03:43.040><c> they're</c><00:03:43.319><c> right</c>

00:03:43.509 --> 00:03:43.519 align:start position:0%
more audio genen videos they're right
 

00:03:43.519 --> 00:03:45.229 align:start position:0%
more audio genen videos they're right
here<00:03:43.879><c> thank</c><00:03:44.040><c> you</c><00:03:44.120><c> for</c><00:03:44.280><c> watching</c><00:03:44.720><c> I'll</c><00:03:44.879><c> see</c><00:03:45.040><c> you</c>

00:03:45.229 --> 00:03:45.239 align:start position:0%
here thank you for watching I'll see you
 

00:03:45.239 --> 00:03:47.879 align:start position:0%
here thank you for watching I'll see you
next<00:03:45.439><c> video</c>

