WEBVTT
Kind: captions
Language: en

00:00:02.960 --> 00:00:05.430 align:start position:0%
 
focus<00:00:03.320><c> on</c><00:00:03.600><c> the</c><00:00:03.800><c> basics</c><00:00:04.480><c> focus</c><00:00:04.759><c> on</c><00:00:04.960><c> the</c><00:00:05.120><c> things</c>

00:00:05.430 --> 00:00:05.440 align:start position:0%
focus on the basics focus on the things
 

00:00:05.440 --> 00:00:08.150 align:start position:0%
focus on the basics focus on the things
that<00:00:05.560><c> you</c><00:00:05.839><c> can</c><00:00:06.240><c> control</c><00:00:07.080><c> figure</c><00:00:07.399><c> out</c><00:00:07.720><c> what</c><00:00:07.879><c> the</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
that you can control figure out what the
 

00:00:08.160 --> 00:00:10.190 align:start position:0%
that you can control figure out what the
processes<00:00:08.639><c> and</c><00:00:08.840><c> tooling</c><00:00:09.280><c> is</c><00:00:09.599><c> that</c><00:00:09.760><c> you</c><00:00:09.920><c> need</c>

00:00:10.190 --> 00:00:10.200 align:start position:0%
processes and tooling is that you need
 

00:00:10.200 --> 00:00:12.470 align:start position:0%
processes and tooling is that you need
to<00:00:10.360><c> be</c><00:00:10.599><c> secure</c><00:00:10.920><c> for</c><00:00:11.120><c> your</c><00:00:11.360><c> situation</c><00:00:12.160><c> and</c><00:00:12.280><c> then</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
to be secure for your situation and then
 

00:00:12.480 --> 00:00:15.190 align:start position:0%
to be secure for your situation and then
automate<00:00:12.880><c> it</c><00:00:13.200><c> automate</c><00:00:13.599><c> it</c><00:00:13.759><c> away</c><00:00:14.519><c> so</c><00:00:14.759><c> that</c><00:00:15.000><c> you</c>

00:00:15.190 --> 00:00:15.200 align:start position:0%
automate it automate it away so that you
 

00:00:15.200 --> 00:00:17.470 align:start position:0%
automate it automate it away so that you
can<00:00:15.679><c> get</c><00:00:16.000><c> focused</c><00:00:16.320><c> on</c><00:00:16.520><c> whatever</c><00:00:16.840><c> the</c><00:00:16.960><c> next</c><00:00:17.240><c> big</c>

00:00:17.470 --> 00:00:17.480 align:start position:0%
can get focused on whatever the next big
 

00:00:17.480 --> 00:00:19.590 align:start position:0%
can get focused on whatever the next big
thing<00:00:17.760><c> is</c><00:00:18.520><c> when</c><00:00:18.640><c> you're</c><00:00:18.840><c> working</c><00:00:19.240><c> right</c><00:00:19.439><c> there</c>

00:00:19.590 --> 00:00:19.600 align:start position:0%
thing is when you're working right there
 

00:00:19.600 --> 00:00:22.349 align:start position:0%
thing is when you're working right there
in<00:00:19.720><c> the</c><00:00:19.880><c> IDE</c><00:00:20.760><c> or</c><00:00:21.160><c> you've</c><00:00:21.439><c> pushed</c><00:00:21.720><c> to</c><00:00:21.880><c> a</c><00:00:22.080><c> poll</c>

00:00:22.349 --> 00:00:22.359 align:start position:0%
in the IDE or you've pushed to a poll
 

00:00:22.359 --> 00:00:24.189 align:start position:0%
in the IDE or you've pushed to a poll
request<00:00:22.800><c> now</c><00:00:23.400><c> you're</c><00:00:23.599><c> starting</c><00:00:23.880><c> to</c><00:00:24.039><c> get</c>

00:00:24.189 --> 00:00:24.199 align:start position:0%
request now you're starting to get
 

00:00:24.199 --> 00:00:25.990 align:start position:0%
request now you're starting to get
real-time<00:00:24.680><c> feedback</c><00:00:25.160><c> that</c><00:00:25.359><c> hey</c><00:00:25.599><c> this</c><00:00:25.760><c> code</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
real-time feedback that hey this code
 

00:00:26.000 --> 00:00:27.349 align:start position:0%
real-time feedback that hey this code
might<00:00:26.119><c> be</c><00:00:26.279><c> vulnerable</c><00:00:26.800><c> there</c><00:00:26.960><c> might</c><00:00:27.119><c> be</c><00:00:27.240><c> a</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
might be vulnerable there might be a
 

00:00:27.359 --> 00:00:28.990 align:start position:0%
might be vulnerable there might be a
more<00:00:27.560><c> secure</c><00:00:27.960><c> pattern</c><00:00:28.320><c> you</c><00:00:28.400><c> should</c><00:00:28.640><c> fix</c><00:00:28.880><c> this</c>

00:00:28.990 --> 00:00:29.000 align:start position:0%
more secure pattern you should fix this
 

00:00:29.000 --> 00:00:30.550 align:start position:0%
more secure pattern you should fix this
and<00:00:29.160><c> here's</c><00:00:29.359><c> how</c><00:00:29.439><c> you</c><00:00:29.560><c> do</c><00:00:29.679><c> it</c><00:00:30.039><c> right</c><00:00:30.199><c> here</c><00:00:30.359><c> from</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
and here's how you do it right here from
 

00:00:30.560 --> 00:00:32.389 align:start position:0%
and here's how you do it right here from
this<00:00:30.679><c> paint</c><00:00:30.880><c> of</c><00:00:31.039><c> glass</c><00:00:31.359><c> without</c><00:00:31.599><c> having</c><00:00:31.840><c> to</c><00:00:32.200><c> go</c>

00:00:32.389 --> 00:00:32.399 align:start position:0%
this paint of glass without having to go
 

00:00:32.399 --> 00:00:34.790 align:start position:0%
this paint of glass without having to go
talk<00:00:32.559><c> to</c><00:00:32.719><c> someone</c><00:00:33.079><c> else</c><00:00:33.920><c> without</c><00:00:34.239><c> having</c><00:00:34.480><c> to</c>

00:00:34.790 --> 00:00:34.800 align:start position:0%
talk to someone else without having to
 

00:00:34.800 --> 00:00:38.630 align:start position:0%
talk to someone else without having to
wait<00:00:35.079><c> for</c><00:00:35.399><c> a</c><00:00:35.559><c> human</c><00:00:35.920><c> LE</c><00:00:36.520><c> review</c><00:00:37.520><c> Secrets</c><00:00:38.040><c> cause</c>

00:00:38.630 --> 00:00:38.640 align:start position:0%
wait for a human LE review Secrets cause
 

00:00:38.640 --> 00:00:41.229 align:start position:0%
wait for a human LE review Secrets cause
80%<00:00:39.640><c> U</c><00:00:39.920><c> data</c><00:00:40.160><c> breaches</c><00:00:40.760><c> according</c><00:00:41.039><c> to</c><00:00:41.160><c> the</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
80% U data breaches according to the
 

00:00:41.239 --> 00:00:43.630 align:start position:0%
80% U data breaches according to the
phyto<00:00:41.559><c> alliance</c><00:00:42.480><c> we</c><00:00:42.600><c> need</c><00:00:42.920><c> security</c><00:00:43.399><c> by</c>

00:00:43.630 --> 00:00:43.640 align:start position:0%
phyto alliance we need security by
 

00:00:43.640 --> 00:00:46.069 align:start position:0%
phyto alliance we need security by
Design<00:00:44.399><c> security</c><00:00:44.800><c> from</c><00:00:45.000><c> the</c><00:00:45.200><c> start</c><00:00:45.800><c> so</c><00:00:46.000><c> you</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
Design security from the start so you
 

00:00:46.079 --> 00:00:47.869 align:start position:0%
Design security from the start so you
should<00:00:46.280><c> simply</c><00:00:46.600><c> treat</c><00:00:46.879><c> a</c><00:00:47.039><c> security</c><00:00:47.480><c> issue</c>

00:00:47.869 --> 00:00:47.879 align:start position:0%
should simply treat a security issue
 

00:00:47.879 --> 00:00:50.069 align:start position:0%
should simply treat a security issue
like<00:00:48.120><c> any</c><00:00:48.360><c> other</c><00:00:48.640><c> bug</c><00:00:49.120><c> it's</c><00:00:49.320><c> simply</c><00:00:49.600><c> a</c><00:00:49.719><c> defect</c>

00:00:50.069 --> 00:00:50.079 align:start position:0%
like any other bug it's simply a defect
 

00:00:50.079 --> 00:00:52.590 align:start position:0%
like any other bug it's simply a defect
in<00:00:50.199><c> your</c><00:00:50.440><c> code</c><00:00:51.239><c> or</c><00:00:51.600><c> perhaps</c><00:00:51.920><c> more</c><00:00:52.160><c> likely</c><00:00:52.480><c> a</c>

00:00:52.590 --> 00:00:52.600 align:start position:0%
in your code or perhaps more likely a
 

00:00:52.600 --> 00:00:54.549 align:start position:0%
in your code or perhaps more likely a
defect<00:00:52.960><c> in</c><00:00:53.079><c> a</c><00:00:53.239><c> third</c><00:00:53.559><c> party</c><00:00:54.000><c> library</c><00:00:54.480><c> that</c>

00:00:54.549 --> 00:00:54.559 align:start position:0%
defect in a third party library that
 

00:00:54.559 --> 00:00:56.830 align:start position:0%
defect in a third party library that
you've<00:00:54.760><c> included</c><00:00:55.120><c> in</c><00:00:55.239><c> your</c><00:00:55.440><c> code</c><00:00:56.359><c> it</c><00:00:56.520><c> has</c><00:00:56.640><c> to</c>

00:00:56.830 --> 00:00:56.840 align:start position:0%
you've included in your code it has to
 

00:00:56.840 --> 00:00:58.910 align:start position:0%
you've included in your code it has to
be<00:00:57.079><c> frictionless</c><00:00:57.879><c> so</c><00:00:58.000><c> it</c><00:00:58.120><c> needs</c><00:00:58.280><c> to</c><00:00:58.480><c> happen</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
be frictionless so it needs to happen
 

00:00:58.920 --> 00:01:01.069 align:start position:0%
be frictionless so it needs to happen
automatically<00:01:00.120><c> and</c><00:01:00.239><c> it</c><00:01:00.440><c> has</c><00:01:00.559><c> to</c><00:01:00.680><c> be</c><00:01:00.800><c> built</c>

00:01:01.069 --> 00:01:01.079 align:start position:0%
automatically and it has to be built
 

00:01:01.079 --> 00:01:02.830 align:start position:0%
automatically and it has to be built
into<00:01:01.320><c> the</c><00:01:01.440><c> developer</c><00:01:02.000><c> workflow</c><00:01:02.559><c> or</c><00:01:02.719><c> the</c>

00:01:02.830 --> 00:01:02.840 align:start position:0%
into the developer workflow or the
 

00:01:02.840 --> 00:01:05.070 align:start position:0%
into the developer workflow or the
developer<00:01:03.519><c> experience</c><00:01:04.519><c> said</c><00:01:04.720><c> it's</c><00:01:04.839><c> about</c>

00:01:05.070 --> 00:01:05.080 align:start position:0%
developer experience said it's about
 

00:01:05.080 --> 00:01:07.789 align:start position:0%
developer experience said it's about
helping<00:01:05.400><c> developers</c><00:01:05.880><c> fix</c><00:01:06.119><c> things</c><00:01:06.680><c> sooner</c><00:01:07.680><c> we</c>

00:01:07.789 --> 00:01:07.799 align:start position:0%
helping developers fix things sooner we
 

00:01:07.799 --> 00:01:09.230 align:start position:0%
helping developers fix things sooner we
know<00:01:08.040><c> that's</c><00:01:08.159><c> a</c><00:01:08.320><c> really</c><00:01:08.520><c> big</c><00:01:08.680><c> challenge</c><00:01:09.119><c> we</c>

00:01:09.230 --> 00:01:09.240 align:start position:0%
know that's a really big challenge we
 

00:01:09.240 --> 00:01:10.590 align:start position:0%
know that's a really big challenge we
know<00:01:09.400><c> that</c><00:01:09.520><c> delers</c><00:01:10.040><c> today</c><00:01:10.240><c> are</c><00:01:10.439><c> really</c>

00:01:10.590 --> 00:01:10.600 align:start position:0%
know that delers today are really
 

00:01:10.600 --> 00:01:12.789 align:start position:0%
know that delers today are really
struggling<00:01:11.000><c> to</c><00:01:11.159><c> fix</c><00:01:11.560><c> vulnerabilities</c><00:01:12.560><c> and</c>

00:01:12.789 --> 00:01:12.799 align:start position:0%
struggling to fix vulnerabilities and
 

00:01:12.799 --> 00:01:14.390 align:start position:0%
struggling to fix vulnerabilities and
providing<00:01:13.200><c> an</c><00:01:13.360><c> ecosystem</c><00:01:13.920><c> for</c><00:01:14.040><c> them</c><00:01:14.159><c> to</c><00:01:14.280><c> be</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
providing an ecosystem for them to be
 

00:01:14.400 --> 00:01:16.510 align:start position:0%
providing an ecosystem for them to be
able<00:01:14.560><c> to</c><00:01:14.720><c> do</c><00:01:14.920><c> that</c><00:01:15.159><c> I</c><00:01:15.240><c> think</c><00:01:15.400><c> is</c><00:01:15.640><c> pretty</c><00:01:16.280><c> giving</c>

00:01:16.510 --> 00:01:16.520 align:start position:0%
able to do that I think is pretty giving
 

00:01:16.520 --> 00:01:18.350 align:start position:0%
able to do that I think is pretty giving
them<00:01:16.680><c> the</c><00:01:16.840><c> right</c><00:01:17.000><c> tools</c><00:01:17.720><c> you</c><00:01:17.840><c> know</c><00:01:18.000><c> you</c><00:01:18.159><c> can't</c>

00:01:18.350 --> 00:01:18.360 align:start position:0%
them the right tools you know you can't
 

00:01:18.360 --> 00:01:20.350 align:start position:0%
them the right tools you know you can't
put<00:01:18.479><c> a</c><00:01:18.560><c> screw</c><00:01:18.840><c> in</c><00:01:18.960><c> with</c><00:01:19.040><c> a</c><00:01:19.200><c> hammer</c><00:01:19.840><c> in</c><00:01:20.000><c> the</c><00:01:20.119><c> same</c>

00:01:20.350 --> 00:01:20.360 align:start position:0%
put a screw in with a hammer in the same
 

00:01:20.360 --> 00:01:21.870 align:start position:0%
put a screw in with a hammer in the same
way<00:01:20.479><c> you</c><00:01:20.600><c> can</c><00:01:20.880><c> fix</c><00:01:21.119><c> vulnerabilities</c><00:01:21.720><c> if</c><00:01:21.799><c> you</c>

00:01:21.870 --> 00:01:21.880 align:start position:0%
way you can fix vulnerabilities if you
 

00:01:21.880 --> 00:01:23.550 align:start position:0%
way you can fix vulnerabilities if you
don't<00:01:22.040><c> have</c><00:01:22.159><c> the</c><00:01:22.280><c> right</c><00:01:22.439><c> Tooling</c><00:01:22.799><c> in</c><00:01:22.960><c> place</c><00:01:23.200><c> to</c>

00:01:23.550 --> 00:01:23.560 align:start position:0%
don't have the right Tooling in place to
 

00:01:23.560 --> 00:01:25.870 align:start position:0%
don't have the right Tooling in place to
help<00:01:23.759><c> them</c><00:01:23.920><c> be</c><00:01:24.079><c> successful</c><00:01:25.079><c> the</c><00:01:25.240><c> outcomes</c><00:01:25.759><c> we</c>

00:01:25.870 --> 00:01:25.880 align:start position:0%
help them be successful the outcomes we
 

00:01:25.880 --> 00:01:28.109 align:start position:0%
help them be successful the outcomes we
want<00:01:26.000><c> to</c><00:01:26.119><c> Foster</c><00:01:26.479><c> is</c><00:01:26.799><c> trust</c><00:01:27.119><c> the</c><00:01:27.320><c> developer</c>

00:01:28.109 --> 00:01:28.119 align:start position:0%
want to Foster is trust the developer
 

00:01:28.119 --> 00:01:30.069 align:start position:0%
want to Foster is trust the developer
but<00:01:28.320><c> allow</c><00:01:28.680><c> the</c><00:01:28.840><c> security</c><00:01:29.240><c> team</c><00:01:29.439><c> to</c><00:01:29.600><c> bear</c><00:01:29.920><c> CL</c>

00:01:30.069 --> 00:01:30.079 align:start position:0%
but allow the security team to bear CL
 

00:01:30.079 --> 00:01:32.510 align:start position:0%
but allow the security team to bear CL
ify<00:01:30.840><c> what</c><00:01:30.960><c> the</c><00:01:31.119><c> developer</c><00:01:31.560><c> is</c><00:01:31.720><c> doing</c><00:01:32.360><c> again</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
ify what the developer is doing again
 

00:01:32.520 --> 00:01:34.030 align:start position:0%
ify what the developer is doing again
we're<00:01:32.680><c> not</c><00:01:32.799><c> trying</c><00:01:33.000><c> to</c><00:01:33.200><c> distance</c><00:01:33.640><c> developers</c>

00:01:34.030 --> 00:01:34.040 align:start position:0%
we're not trying to distance developers
 

00:01:34.040 --> 00:01:35.429 align:start position:0%
we're not trying to distance developers
in<00:01:34.240><c> security</c><00:01:34.600><c> we're</c><00:01:34.720><c> trying</c><00:01:34.880><c> to</c><00:01:35.000><c> bring</c><00:01:35.240><c> them</c>

00:01:35.429 --> 00:01:35.439 align:start position:0%
in security we're trying to bring them
 

00:01:35.439 --> 00:01:37.670 align:start position:0%
in security we're trying to bring them
closer<00:01:36.040><c> together</c><00:01:36.560><c> so</c><00:01:36.759><c> the</c><00:01:36.960><c> likely</c><00:01:37.320><c> larger</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
closer together so the likely larger
 

00:01:37.680 --> 00:01:40.230 align:start position:0%
closer together so the likely larger
developer<00:01:38.159><c> org</c><00:01:38.680><c> to</c><00:01:38.880><c> the</c><00:01:39.079><c> Security</c><00:01:39.479><c> Org</c><00:01:40.040><c> can</c>

00:01:40.230 --> 00:01:40.240 align:start position:0%
developer org to the Security Org can
 

00:01:40.240 --> 00:01:42.429 align:start position:0%
developer org to the Security Org can
now<00:01:40.479><c> run</c><00:01:40.840><c> at</c><00:01:40.960><c> a</c><00:01:41.119><c> scale</c><00:01:41.479><c> motion</c><00:01:41.840><c> where</c><00:01:42.040><c> security</c>

00:01:42.429 --> 00:01:42.439 align:start position:0%
now run at a scale motion where security
 

00:01:42.439 --> 00:01:44.709 align:start position:0%
now run at a scale motion where security
is<00:01:42.560><c> now</c><00:01:42.680><c> an</c><00:01:42.880><c> enabling</c><00:01:43.560><c> function</c><00:01:44.280><c> versus</c><00:01:44.600><c> the</c>

00:01:44.709 --> 00:01:44.719 align:start position:0%
is now an enabling function versus the
 

00:01:44.719 --> 00:01:47.789 align:start position:0%
is now an enabling function versus the
team<00:01:44.960><c> that</c><00:01:45.040><c> has</c><00:01:45.159><c> to</c><00:01:45.320><c> do</c><00:01:45.479><c> all</c><00:01:45.640><c> this</c><00:01:45.920><c> work</c><00:01:46.920><c> AI</c><00:01:47.560><c> is</c>

00:01:47.789 --> 00:01:47.799 align:start position:0%
team that has to do all this work AI is
 

00:01:47.799 --> 00:01:49.990 align:start position:0%
team that has to do all this work AI is
definitely<00:01:48.560><c> the</c><00:01:48.719><c> most</c><00:01:49.040><c> interesting</c><00:01:49.640><c> thing</c><00:01:49.799><c> in</c>

00:01:49.990 --> 00:01:50.000 align:start position:0%
definitely the most interesting thing in
 

00:01:50.000 --> 00:01:54.350 align:start position:0%
definitely the most interesting thing in
security<00:01:51.240><c> because</c><00:01:52.240><c> it</c><00:01:52.880><c> allows</c><00:01:53.320><c> us</c><00:01:53.479><c> to</c><00:01:53.880><c> crank</c>

00:01:54.350 --> 00:01:54.360 align:start position:0%
security because it allows us to crank
 

00:01:54.360 --> 00:01:57.709 align:start position:0%
security because it allows us to crank
through<00:01:55.079><c> information</c><00:01:55.960><c> and</c><00:01:56.240><c> data</c><00:01:56.759><c> in</c><00:01:57.200><c> unheard</c>

00:01:57.709 --> 00:01:57.719 align:start position:0%
through information and data in unheard
 

00:01:57.719 --> 00:02:05.429 align:start position:0%
through information and data in unheard
of<00:01:58.079><c> velocities</c><00:01:58.960><c> that</c><00:01:59.119><c> we</c><00:01:59.240><c> have</c><00:01:59.360><c> just</c><00:01:59.520><c> not</c><00:01:59.960><c> seen</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
 
 

00:02:05.439 --> 00:02:08.439 align:start position:0%
 
before

