WEBVTT
Kind: captions
Language: en

00:00:01.520 --> 00:00:05.869 align:start position:0%
 
today<00:00:01.839><c> I</c><00:00:02.200><c> received</c><00:00:02.600><c> a</c><00:00:03.000><c> new</c><00:00:03.720><c> 2019</c><00:00:04.720><c> iMac</c><00:00:05.520><c> and</c><00:00:05.680><c> did</c>

00:00:05.869 --> 00:00:05.879 align:start position:0%
today I received a new 2019 iMac and did
 

00:00:05.879 --> 00:00:08.669 align:start position:0%
today I received a new 2019 iMac and did
an<00:00:06.080><c> unboxing</c><00:00:06.720><c> video</c><00:00:07.080><c> right</c><00:00:07.279><c> away</c><00:00:08.160><c> I</c><00:00:08.240><c> got</c><00:00:08.360><c> a</c><00:00:08.480><c> few</c>

00:00:08.669 --> 00:00:08.679 align:start position:0%
an unboxing video right away I got a few
 

00:00:08.679 --> 00:00:11.749 align:start position:0%
an unboxing video right away I got a few
questions<00:00:09.040><c> about</c><00:00:09.639><c> throttling</c><00:00:10.639><c> so</c><00:00:11.000><c> here's</c><00:00:11.440><c> my</c>

00:00:11.749 --> 00:00:11.759 align:start position:0%
questions about throttling so here's my
 

00:00:11.759 --> 00:00:13.829 align:start position:0%
questions about throttling so here's my
first<00:00:12.120><c> look</c><00:00:12.599><c> at</c>

00:00:13.829 --> 00:00:13.839 align:start position:0%
first look at
 

00:00:13.839 --> 00:00:17.790 align:start position:0%
first look at
that<00:00:14.839><c> I'm</c><00:00:15.000><c> piping</c><00:00:15.440><c> yes</c><00:00:15.719><c> to/</c><00:00:16.600><c> devnull</c><00:00:17.600><c> which</c>

00:00:17.790 --> 00:00:17.800 align:start position:0%
that I'm piping yes to/ devnull which
 

00:00:17.800 --> 00:00:20.550 align:start position:0%
that I'm piping yes to/ devnull which
pegs<00:00:18.080><c> the</c><00:00:18.240><c> CPU</c><00:00:19.039><c> I</c><00:00:19.119><c> run</c><00:00:19.320><c> it</c><00:00:19.480><c> 16</c><00:00:19.920><c> times</c><00:00:20.240><c> pegging</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
pegs the CPU I run it 16 times pegging
 

00:00:20.560 --> 00:00:22.790 align:start position:0%
pegs the CPU I run it 16 times pegging
it<00:00:20.760><c> all</c><00:00:21.240><c> 16</c><00:00:21.720><c> virtual</c>

00:00:22.790 --> 00:00:22.800 align:start position:0%
it all 16 virtual
 

00:00:22.800 --> 00:00:25.429 align:start position:0%
it all 16 virtual
cores<00:00:23.800><c> I've</c><00:00:24.000><c> sped</c><00:00:24.320><c> things</c><00:00:24.519><c> up</c><00:00:24.760><c> here</c><00:00:25.080><c> by</c><00:00:25.240><c> about</c>

00:00:25.429 --> 00:00:25.439 align:start position:0%
cores I've sped things up here by about
 

00:00:25.439 --> 00:00:27.470 align:start position:0%
cores I've sped things up here by about
60<00:00:25.880><c> times</c><00:00:26.160><c> to</c><00:00:26.320><c> make</c><00:00:26.480><c> it</c><00:00:26.760><c> a</c><00:00:26.960><c> little</c><00:00:27.240><c> less</c>

00:00:27.470 --> 00:00:27.480 align:start position:0%
60 times to make it a little less
 

00:00:27.480 --> 00:00:28.950 align:start position:0%
60 times to make it a little less
painful<00:00:27.960><c> to</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
painful to
 

00:00:28.960 --> 00:00:31.109 align:start position:0%
painful to
watch

00:00:31.109 --> 00:00:31.119 align:start position:0%
watch
 

00:00:31.119 --> 00:00:33.270 align:start position:0%
watch
essentially<00:00:31.679><c> I'm</c><00:00:31.800><c> maxing</c><00:00:32.160><c> out</c><00:00:32.399><c> the</c><00:00:32.520><c> CPU</c><00:00:33.120><c> and</c>

00:00:33.270 --> 00:00:33.280 align:start position:0%
essentially I'm maxing out the CPU and
 

00:00:33.280 --> 00:00:37.190 align:start position:0%
essentially I'm maxing out the CPU and
not<00:00:33.440><c> seeing</c><00:00:33.840><c> any</c><00:00:34.079><c> throttling</c><00:00:34.680><c> for</c><00:00:35.200><c> 20ish</c>

00:00:37.190 --> 00:00:37.200 align:start position:0%
not seeing any throttling for 20ish
 

00:00:37.200 --> 00:00:39.750 align:start position:0%
not seeing any throttling for 20ish
minutes<00:00:38.200><c> here</c><00:00:38.360><c> I've</c><00:00:38.520><c> loaded</c><00:00:38.840><c> up</c><00:00:39.000><c> cinebench</c>

00:00:39.750 --> 00:00:39.760 align:start position:0%
minutes here I've loaded up cinebench
 

00:00:39.760 --> 00:00:42.190 align:start position:0%
minutes here I've loaded up cinebench
outside<00:00:40.200><c> of</c><00:00:40.360><c> the</c><00:00:40.480><c> view</c><00:00:40.680><c> of</c><00:00:40.879><c> this</c><00:00:41.079><c> window</c><00:00:42.079><c> and</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
outside of the view of this window and
 

00:00:42.200 --> 00:00:44.670 align:start position:0%
outside of the view of this window and
run<00:00:42.440><c> it</c><00:00:42.600><c> a</c><00:00:42.719><c> few</c><00:00:43.000><c> times</c><00:00:43.680><c> I</c><00:00:43.800><c> see</c><00:00:44.039><c> the</c><00:00:44.200><c> CPU</c>

00:00:44.670 --> 00:00:44.680 align:start position:0%
run it a few times I see the CPU
 

00:00:44.680 --> 00:00:47.990 align:start position:0%
run it a few times I see the CPU
frequency<00:00:45.160><c> drop</c><00:00:45.640><c> Just</c><00:00:46.039><c> a</c><00:00:46.239><c> Touch</c><00:00:46.760><c> while</c><00:00:46.960><c> it's</c>

00:00:47.990 --> 00:00:48.000 align:start position:0%
frequency drop Just a Touch while it's
 

00:00:48.000 --> 00:00:50.750 align:start position:0%
frequency drop Just a Touch while it's
running<00:00:49.000><c> now</c><00:00:49.199><c> the</c><00:00:49.320><c> whole</c><00:00:49.520><c> time</c><00:00:50.039><c> I've</c><00:00:50.360><c> barely</c>

00:00:50.750 --> 00:00:50.760 align:start position:0%
running now the whole time I've barely
 

00:00:50.760 --> 00:00:54.630 align:start position:0%
running now the whole time I've barely
been<00:00:50.920><c> able</c><00:00:51.120><c> to</c><00:00:51.239><c> hear</c><00:00:51.480><c> the</c><00:00:51.600><c> fan</c><00:00:52.120><c> at</c>

00:00:54.630 --> 00:00:54.640 align:start position:0%
 
 

00:00:54.640 --> 00:00:57.830 align:start position:0%
 
all<00:00:55.640><c> I'll</c><00:00:55.760><c> move</c><00:00:56.039><c> the</c><00:00:56.199><c> cinebench</c><00:00:57.000><c> window</c><00:00:57.600><c> into</c>

00:00:57.830 --> 00:00:57.840 align:start position:0%
all I'll move the cinebench window into
 

00:00:57.840 --> 00:00:59.270 align:start position:0%
all I'll move the cinebench window into
the<00:00:57.960><c> screen</c><00:00:58.280><c> so</c><00:00:58.440><c> you</c><00:00:58.559><c> can</c><00:00:58.719><c> see</c><00:00:59.039><c> what's</c>

00:00:59.270 --> 00:00:59.280 align:start position:0%
the screen so you can see what's
 

00:00:59.280 --> 00:01:05.229 align:start position:0%
the screen so you can see what's
happening

00:01:05.229 --> 00:01:05.239 align:start position:0%
 
 

00:01:05.239 --> 00:01:07.990 align:start position:0%
 
and<00:01:05.519><c> here</c><00:01:05.920><c> on</c><00:01:06.119><c> the</c><00:01:06.400><c> second</c><00:01:06.680><c> run</c><00:01:06.920><c> of</c><00:01:07.080><c> cin</c><00:01:07.479><c> bench</c>

00:01:07.990 --> 00:01:08.000 align:start position:0%
and here on the second run of cin bench
 

00:01:08.000 --> 00:01:09.630 align:start position:0%
and here on the second run of cin bench
is<00:01:08.200><c> the</c><00:01:08.360><c> first</c><00:01:08.600><c> time</c><00:01:08.840><c> I've</c><00:01:08.960><c> been</c><00:01:09.119><c> able</c><00:01:09.320><c> to</c>

00:01:09.630 --> 00:01:09.640 align:start position:0%
is the first time I've been able to
 

00:01:09.640 --> 00:01:12.350 align:start position:0%
is the first time I've been able to
clearly<00:01:10.200><c> hear</c><00:01:10.439><c> the</c><00:01:10.560><c> fans</c><00:01:11.040><c> wearing</c><00:01:12.040><c> and</c><00:01:12.200><c> that's</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
clearly hear the fans wearing and that's
 

00:01:12.360 --> 00:01:14.469 align:start position:0%
clearly hear the fans wearing and that's
only<00:01:12.560><c> for</c><00:01:12.759><c> about</c><00:01:13.000><c> 15</c><00:01:13.400><c> to</c><00:01:13.600><c> 20</c><00:01:13.880><c> seconds</c><00:01:14.240><c> then</c><00:01:14.360><c> it</c>

00:01:14.469 --> 00:01:14.479 align:start position:0%
only for about 15 to 20 seconds then it
 

00:01:14.479 --> 00:01:15.910 align:start position:0%
only for about 15 to 20 seconds then it
goes<00:01:14.720><c> back</c><00:01:14.840><c> to</c>

00:01:15.910 --> 00:01:15.920 align:start position:0%
goes back to
 

00:01:15.920 --> 00:01:18.469 align:start position:0%
goes back to
normal<00:01:16.920><c> I'll</c><00:01:17.040><c> run</c><00:01:17.280><c> the</c><00:01:17.400><c> Center</c><00:01:17.759><c> bench</c>

00:01:18.469 --> 00:01:18.479 align:start position:0%
normal I'll run the Center bench
 

00:01:18.479 --> 00:01:20.630 align:start position:0%
normal I'll run the Center bench
Benchmark<00:01:19.400><c> a</c><00:01:19.520><c> few</c><00:01:19.680><c> more</c><00:01:19.880><c> times</c><00:01:20.119><c> and</c><00:01:20.240><c> see</c><00:01:20.479><c> the</c>

00:01:20.630 --> 00:01:20.640 align:start position:0%
Benchmark a few more times and see the
 

00:01:20.640 --> 00:01:24.749 align:start position:0%
Benchmark a few more times and see the
exact<00:01:20.920><c> same</c><00:01:21.159><c> thing</c><00:01:21.560><c> every</c><00:01:22.000><c> single</c>

00:01:24.749 --> 00:01:24.759 align:start position:0%
 
 

00:01:24.759 --> 00:01:28.109 align:start position:0%
 
time<00:01:25.759><c> finally</c><00:01:26.159><c> a</c><00:01:26.360><c> run</c><00:01:26.560><c> thegeek</c><00:01:26.960><c> bench</c><00:01:27.520><c> compute</c>

00:01:28.109 --> 00:01:28.119 align:start position:0%
time finally a run thegeek bench compute
 

00:01:28.119 --> 00:01:30.109 align:start position:0%
time finally a run thegeek bench compute
Benchmark<00:01:29.119><c> I</c><00:01:29.200><c> don't</c><00:01:29.360><c> have</c><00:01:29.479><c> the</c><00:01:29.560><c> full</c><00:01:29.920><c> version</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
Benchmark I don't have the full version
 

00:01:30.119 --> 00:01:32.590 align:start position:0%
Benchmark I don't have the full version
of<00:01:30.280><c> geekbench</c><00:01:31.159><c> so</c><00:01:31.320><c> I</c><00:01:31.439><c> can</c><00:01:31.600><c> only</c><00:01:31.840><c> run</c><00:01:32.119><c> the</c><00:01:32.320><c> open</c>

00:01:32.590 --> 00:01:32.600 align:start position:0%
of geekbench so I can only run the open
 

00:01:32.600 --> 00:01:35.590 align:start position:0%
of geekbench so I can only run the open
CL<00:01:33.040><c> version</c><00:01:33.560><c> but</c><00:01:33.680><c> it</c><00:01:33.960><c> still</c><00:01:34.240><c> seems</c><00:01:34.799><c> plenty</c>

00:01:35.590 --> 00:01:35.600 align:start position:0%
CL version but it still seems plenty
 

00:01:35.600 --> 00:01:39.190 align:start position:0%
CL version but it still seems plenty
fast<00:01:36.600><c> so</c><00:01:36.920><c> those</c><00:01:37.040><c> are</c><00:01:37.439><c> some</c><00:01:37.600><c> of</c><00:01:37.759><c> the</c><00:01:38.320><c> um</c><00:01:38.840><c> initial</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
fast so those are some of the um initial
 

00:01:39.200 --> 00:01:41.710 align:start position:0%
fast so those are some of the um initial
tests<00:01:39.600><c> I've</c><00:01:39.759><c> done</c><00:01:40.079><c> about</c><00:01:40.520><c> throttling</c><00:01:41.520><c> I'm</c><00:01:41.600><c> not</c>

00:01:41.710 --> 00:01:41.720 align:start position:0%
tests I've done about throttling I'm not
 

00:01:41.720 --> 00:01:43.950 align:start position:0%
tests I've done about throttling I'm not
sure<00:01:41.920><c> if</c><00:01:42.000><c> I'm</c><00:01:42.119><c> doing</c><00:01:42.399><c> this</c><00:01:42.640><c> right</c><00:01:42.840><c> so</c><00:01:43.079><c> if</c><00:01:43.280><c> you</c>

00:01:43.950 --> 00:01:43.960 align:start position:0%
sure if I'm doing this right so if you
 

00:01:43.960 --> 00:01:47.510 align:start position:0%
sure if I'm doing this right so if you
have<00:01:44.360><c> any</c><00:01:44.680><c> comments</c><00:01:45.479><c> or</c><00:01:46.000><c> suggestions</c><00:01:46.640><c> about</c>

00:01:47.510 --> 00:01:47.520 align:start position:0%
have any comments or suggestions about
 

00:01:47.520 --> 00:01:50.069 align:start position:0%
have any comments or suggestions about
other<00:01:47.759><c> tests</c><00:01:48.159><c> I</c><00:01:48.240><c> can</c><00:01:48.399><c> do</c><00:01:48.960><c> uh</c><00:01:49.079><c> send</c><00:01:49.280><c> them</c><00:01:49.479><c> my</c><00:01:49.680><c> way</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
other tests I can do uh send them my way
 

00:01:50.079 --> 00:01:52.870 align:start position:0%
other tests I can do uh send them my way
just<00:01:50.280><c> by</c><00:01:50.680><c> uh</c><00:01:50.840><c> leaving</c><00:01:51.159><c> a</c><00:01:51.320><c> comment</c><00:01:51.680><c> below</c><00:01:52.680><c> or</c>

00:01:52.870 --> 00:01:52.880 align:start position:0%
just by uh leaving a comment below or
 

00:01:52.880 --> 00:01:55.469 align:start position:0%
just by uh leaving a comment below or
sending<00:01:53.159><c> me</c><00:01:53.320><c> a</c><00:01:53.439><c> tweet</c><00:01:54.040><c> on</c><00:01:54.360><c> I'm</c><00:01:54.479><c> on</c><00:01:54.640><c> Twitter</c><00:01:55.040><c> at</c>

00:01:55.469 --> 00:01:55.479 align:start position:0%
sending me a tweet on I'm on Twitter at
 

00:01:55.479 --> 00:01:57.910 align:start position:0%
sending me a tweet on I'm on Twitter at
techno<00:01:56.200><c> evangelist</c><00:01:57.200><c> okay</c><00:01:57.439><c> thanks</c><00:01:57.640><c> so</c><00:01:57.799><c> much</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
techno evangelist okay thanks so much
 

00:01:57.920 --> 00:02:01.840 align:start position:0%
techno evangelist okay thanks so much
for<00:01:58.119><c> watching</c><00:01:58.840><c> bye</c>

