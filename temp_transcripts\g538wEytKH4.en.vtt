WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.949 align:start position:0%
 
while<00:00:00.240><c> I</c><00:00:00.359><c> love</c><00:00:00.599><c> being</c><00:00:00.840><c> able</c><00:00:01.079><c> to</c><00:00:01.280><c> review</c><00:00:01.640><c> pull</c>

00:00:01.949 --> 00:00:01.959 align:start position:0%
while I love being able to review pull
 

00:00:01.959 --> 00:00:04.110 align:start position:0%
while I love being able to review pull
requests<00:00:02.520><c> from</c><00:00:02.679><c> my</c><00:00:02.840><c> mobile</c><00:00:03.240><c> device</c><00:00:03.879><c> one</c><00:00:04.000><c> of</c>

00:00:04.110 --> 00:00:04.120 align:start position:0%
requests from my mobile device one of
 

00:00:04.120 --> 00:00:06.590 align:start position:0%
requests from my mobile device one of
the<00:00:04.240><c> challenges</c><00:00:04.759><c> I</c><00:00:04.880><c> run</c><00:00:05.120><c> into</c><00:00:05.920><c> is</c><00:00:06.080><c> if</c><00:00:06.200><c> a</c><00:00:06.319><c> pull</c>

00:00:06.590 --> 00:00:06.600 align:start position:0%
the challenges I run into is if a pull
 

00:00:06.600 --> 00:00:09.509 align:start position:0%
the challenges I run into is if a pull
request<00:00:07.200><c> is</c><00:00:07.359><c> missing</c><00:00:07.720><c> required</c><00:00:08.280><c> items</c><00:00:09.040><c> like</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
request is missing required items like
 

00:00:09.519 --> 00:00:12.350 align:start position:0%
request is missing required items like
unit<00:00:09.920><c> tests</c><00:00:10.920><c> fortunately</c><00:00:11.759><c> with</c><00:00:11.880><c> the</c><00:00:12.000><c> help</c><00:00:12.200><c> of</c>

00:00:12.350 --> 00:00:12.360 align:start position:0%
unit tests fortunately with the help of
 

00:00:12.360 --> 00:00:14.829 align:start position:0%
unit tests fortunately with the help of
co-pilot<00:00:12.840><c> workspace</c><00:00:13.759><c> I</c><00:00:13.880><c> can</c><00:00:14.080><c> get</c><00:00:14.280><c> support</c>

00:00:14.829 --> 00:00:14.839 align:start position:0%
co-pilot workspace I can get support
 

00:00:14.839 --> 00:00:17.710 align:start position:0%
co-pilot workspace I can get support
right<00:00:15.080><c> here</c><00:00:15.599><c> for</c><00:00:15.920><c> generating</c><00:00:16.560><c> those</c><00:00:17.560><c> I</c>

00:00:17.710 --> 00:00:17.720 align:start position:0%
right here for generating those I
 

00:00:17.720 --> 00:00:19.990 align:start position:0%
right here for generating those I
describe<00:00:18.240><c> what</c><00:00:18.359><c> it</c><00:00:18.480><c> is</c><00:00:18.640><c> that</c><00:00:18.800><c> I</c><00:00:18.960><c> need</c><00:00:19.800><c> and</c>

00:00:19.990 --> 00:00:20.000 align:start position:0%
describe what it is that I need and
 

00:00:20.000 --> 00:00:22.509 align:start position:0%
describe what it is that I need and
co-pilot<00:00:20.480><c> workspace</c><00:00:21.279><c> will</c><00:00:21.519><c> take</c><00:00:21.640><c> a</c><00:00:21.840><c> look</c><00:00:22.199><c> at</c>

00:00:22.509 --> 00:00:22.519 align:start position:0%
co-pilot workspace will take a look at
 

00:00:22.519 --> 00:00:24.589 align:start position:0%
co-pilot workspace will take a look at
everything<00:00:22.880><c> that</c><00:00:23.039><c> currently</c><00:00:23.519><c> exists</c><00:00:24.279><c> inside</c>

00:00:24.589 --> 00:00:24.599 align:start position:0%
everything that currently exists inside
 

00:00:24.599 --> 00:00:27.150 align:start position:0%
everything that currently exists inside
of<00:00:24.800><c> the</c><00:00:25.000><c> project</c><00:00:25.840><c> and</c><00:00:26.039><c> let</c><00:00:26.199><c> me</c><00:00:26.400><c> know</c><00:00:26.720><c> what</c><00:00:26.880><c> it</c>

00:00:27.150 --> 00:00:27.160 align:start position:0%
of the project and let me know what it
 

00:00:27.160 --> 00:00:29.830 align:start position:0%
of the project and let me know what it
proposes<00:00:28.119><c> to</c><00:00:28.320><c> help</c><00:00:28.640><c> resolve</c><00:00:29.359><c> the</c><00:00:29.480><c> challenge</c>

00:00:29.830 --> 00:00:29.840 align:start position:0%
proposes to help resolve the challenge
 

00:00:29.840 --> 00:00:32.670 align:start position:0%
proposes to help resolve the challenge
that<00:00:30.199><c> I've</c><00:00:30.480><c> put</c><00:00:30.920><c> forth</c><00:00:31.920><c> I</c><00:00:32.040><c> look</c><00:00:32.279><c> through</c><00:00:32.480><c> the</c>

00:00:32.670 --> 00:00:32.680 align:start position:0%
that I've put forth I look through the
 

00:00:32.680 --> 00:00:34.389 align:start position:0%
that I've put forth I look through the
proposal<00:00:33.480><c> and</c><00:00:33.600><c> I</c><00:00:33.680><c> can</c><00:00:33.879><c> make</c><00:00:34.160><c> any</c>

00:00:34.389 --> 00:00:34.399 align:start position:0%
proposal and I can make any
 

00:00:34.399 --> 00:00:36.590 align:start position:0%
proposal and I can make any
modifications<00:00:35.160><c> that</c><00:00:35.320><c> I</c><00:00:35.480><c> might</c><00:00:35.680><c> want</c><00:00:36.399><c> for</c>

00:00:36.590 --> 00:00:36.600 align:start position:0%
modifications that I might want for
 

00:00:36.600 --> 00:00:38.990 align:start position:0%
modifications that I might want for
example<00:00:37.120><c> I</c><00:00:37.239><c> want</c><00:00:37.360><c> to</c><00:00:37.559><c> make</c><00:00:37.719><c> sure</c><00:00:38.520><c> that</c><00:00:38.680><c> we're</c>

00:00:38.990 --> 00:00:39.000 align:start position:0%
example I want to make sure that we're
 

00:00:39.000 --> 00:00:41.510 align:start position:0%
example I want to make sure that we're
going<00:00:39.280><c> to</c><00:00:39.600><c> suppress</c><00:00:40.280><c> any</c><00:00:40.640><c> console</c><00:00:41.160><c> error</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
going to suppress any console error
 

00:00:41.520 --> 00:00:45.069 align:start position:0%
going to suppress any console error
messages<00:00:42.360><c> when</c><00:00:42.600><c> the</c><00:00:42.760><c> tests</c><00:00:43.320><c> are</c><00:00:43.879><c> run</c><00:00:44.879><c> once</c>

00:00:45.069 --> 00:00:45.079 align:start position:0%
messages when the tests are run once
 

00:00:45.079 --> 00:00:46.910 align:start position:0%
messages when the tests are run once
I've<00:00:45.280><c> decided</c><00:00:45.680><c> the</c><00:00:45.960><c> specification</c><00:00:46.600><c> looks</c>

00:00:46.910 --> 00:00:46.920 align:start position:0%
I've decided the specification looks
 

00:00:46.920 --> 00:00:49.470 align:start position:0%
I've decided the specification looks
good<00:00:47.360><c> I</c><00:00:47.520><c> let</c><00:00:47.640><c> it</c><00:00:47.800><c> generate</c><00:00:48.120><c> a</c><00:00:48.280><c> plan</c><00:00:49.039><c> it</c><00:00:49.199><c> shows</c>

00:00:49.470 --> 00:00:49.480 align:start position:0%
good I let it generate a plan it shows
 

00:00:49.480 --> 00:00:51.750 align:start position:0%
good I let it generate a plan it shows
me<00:00:49.680><c> the</c><00:00:49.800><c> two</c><00:00:50.039><c> files</c><00:00:50.399><c> that</c><00:00:50.520><c> it's</c><00:00:50.760><c> now</c><00:00:51.039><c> going</c><00:00:51.320><c> to</c>

00:00:51.750 --> 00:00:51.760 align:start position:0%
me the two files that it's now going to
 

00:00:51.760 --> 00:00:55.670 align:start position:0%
me the two files that it's now going to
create<00:00:52.640><c> and</c><00:00:52.800><c> I</c><00:00:53.039><c> let</c><00:00:53.359><c> it</c><00:00:53.680><c> Implement</c><00:00:54.399><c> those</c><00:00:55.399><c> once</c>

00:00:55.670 --> 00:00:55.680 align:start position:0%
create and I let it Implement those once
 

00:00:55.680 --> 00:00:58.549 align:start position:0%
create and I let it Implement those once
those<00:00:55.840><c> are</c><00:00:56.160><c> created</c><00:00:57.000><c> I</c><00:00:57.079><c> can</c><00:00:57.320><c> then</c><00:00:57.559><c> review</c><00:00:57.960><c> them</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
those are created I can then review them
 

00:00:58.559 --> 00:01:01.549 align:start position:0%
those are created I can then review them
right<00:00:58.800><c> here</c><00:00:59.039><c> from</c><00:00:59.239><c> my</c><00:00:59.399><c> mobile</c><00:01:00.160><c> device</c><00:01:01.160><c> I</c><00:01:01.280><c> can</c>

00:01:01.549 --> 00:01:01.559 align:start position:0%
right here from my mobile device I can
 

00:01:01.559 --> 00:01:04.070 align:start position:0%
right here from my mobile device I can
also<00:01:02.000><c> even</c><00:01:02.239><c> bring</c><00:01:02.480><c> up</c><00:01:02.640><c> the</c><00:01:02.840><c> project</c><00:01:03.199><c> structure</c>

00:01:04.070 --> 00:01:04.080 align:start position:0%
also even bring up the project structure
 

00:01:04.080 --> 00:01:06.230 align:start position:0%
also even bring up the project structure
navigate<00:01:04.640><c> through</c><00:01:05.320><c> and</c><00:01:05.519><c> ensure</c><00:01:06.000><c> that</c><00:01:06.119><c> the</c>

00:01:06.230 --> 00:01:06.240 align:start position:0%
navigate through and ensure that the
 

00:01:06.240 --> 00:01:09.270 align:start position:0%
navigate through and ensure that the
files<00:01:06.640><c> are</c><00:01:07.000><c> created</c><00:01:07.560><c> in</c><00:01:07.720><c> the</c><00:01:07.840><c> right</c><00:01:08.280><c> location</c>

00:01:09.270 --> 00:01:09.280 align:start position:0%
files are created in the right location
 

00:01:09.280 --> 00:01:12.830 align:start position:0%
files are created in the right location
and<00:01:09.680><c> also</c><00:01:10.040><c> utilize</c><00:01:10.720><c> any</c><00:01:11.000><c> helper</c><00:01:11.479><c> functions</c><00:01:12.479><c> or</c>

00:01:12.830 --> 00:01:12.840 align:start position:0%
and also utilize any helper functions or
 

00:01:12.840 --> 00:01:15.670 align:start position:0%
and also utilize any helper functions or
other<00:01:13.080><c> Frameworks</c><00:01:13.920><c> that</c><00:01:14.240><c> I</c><00:01:14.520><c> might</c><00:01:14.960><c> have</c>

00:01:15.670 --> 00:01:15.680 align:start position:0%
other Frameworks that I might have
 

00:01:15.680 --> 00:01:17.710 align:start position:0%
other Frameworks that I might have
configured<00:01:16.680><c> once</c><00:01:16.920><c> I've</c><00:01:17.119><c> determined</c><00:01:17.560><c> that</c>

00:01:17.710 --> 00:01:17.720 align:start position:0%
configured once I've determined that
 

00:01:17.720 --> 00:01:20.270 align:start position:0%
configured once I've determined that
everything<00:01:18.080><c> looks</c><00:01:18.439><c> good</c><00:01:19.159><c> I</c><00:01:19.479><c> return</c><00:01:19.840><c> back</c><00:01:20.040><c> to</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
everything looks good I return back to
 

00:01:20.280 --> 00:01:23.190 align:start position:0%
everything looks good I return back to
the<00:01:20.560><c> workspace</c><00:01:21.560><c> but</c><00:01:21.799><c> before</c><00:01:22.200><c> I</c><00:01:22.400><c> create</c><00:01:22.720><c> a</c><00:01:22.840><c> pull</c>

00:01:23.190 --> 00:01:23.200 align:start position:0%
the workspace but before I create a pull
 

00:01:23.200 --> 00:01:25.749 align:start position:0%
the workspace but before I create a pull
request<00:01:24.000><c> with</c><00:01:24.240><c> this</c><00:01:24.400><c> new</c><00:01:24.680><c> code</c><00:01:25.280><c> I</c><00:01:25.400><c> want</c><00:01:25.560><c> to</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
request with this new code I want to
 

00:01:25.759 --> 00:01:28.510 align:start position:0%
request with this new code I want to
make<00:01:25.920><c> sure</c><00:01:26.200><c> that</c><00:01:26.439><c> those</c><00:01:26.720><c> tests</c><00:01:27.159><c> run</c><00:01:27.960><c> so</c><00:01:28.200><c> I</c><00:01:28.320><c> can</c>

00:01:28.510 --> 00:01:28.520 align:start position:0%
make sure that those tests run so I can
 

00:01:28.520 --> 00:01:31.350 align:start position:0%
make sure that those tests run so I can
open<00:01:28.759><c> up</c><00:01:28.880><c> a</c><00:01:29.000><c> terminal</c><00:01:29.439><c> window</c><00:01:30.240><c> and</c><00:01:30.400><c> run</c><00:01:30.759><c> npm</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
open up a terminal window and run npm
 

00:01:31.360 --> 00:01:35.190 align:start position:0%
open up a terminal window and run npm
install<00:01:32.159><c> and</c><00:01:32.399><c> npm</c><00:01:33.079><c> run</c><00:01:33.520><c> test</c><00:01:34.320><c> just</c><00:01:34.600><c> like</c><00:01:34.920><c> I</c>

00:01:35.190 --> 00:01:35.200 align:start position:0%
install and npm run test just like I
 

00:01:35.200 --> 00:01:39.030 align:start position:0%
install and npm run test just like I
normally<00:01:35.920><c> would</c><00:01:36.920><c> I</c><00:01:37.040><c> see</c><00:01:37.360><c> the</c><00:01:37.520><c> test</c><00:01:38.040><c> results</c>

00:01:39.030 --> 00:01:39.040 align:start position:0%
normally would I see the test results
 

00:01:39.040 --> 00:01:41.950 align:start position:0%
normally would I see the test results
confirm<00:01:39.479><c> that</c><00:01:39.640><c> they</c><00:01:39.799><c> look</c><00:01:40.119><c> good</c><00:01:41.119><c> and</c><00:01:41.360><c> then</c>

00:01:41.950 --> 00:01:41.960 align:start position:0%
confirm that they look good and then
 

00:01:41.960 --> 00:01:44.749 align:start position:0%
confirm that they look good and then
generate<00:01:42.640><c> a</c><00:01:42.920><c> brand</c><00:01:43.280><c> new</c><00:01:43.560><c> poll</c><00:01:43.920><c> request</c>

00:01:44.749 --> 00:01:44.759 align:start position:0%
generate a brand new poll request
 

00:01:44.759 --> 00:01:46.670 align:start position:0%
generate a brand new poll request
co-pilot<00:01:45.280><c> workspace</c><00:01:45.759><c> will</c><00:01:46.000><c> even</c><00:01:46.200><c> set</c><00:01:46.399><c> up</c><00:01:46.520><c> the</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
co-pilot workspace will even set up the
 

00:01:46.680 --> 00:01:50.230 align:start position:0%
co-pilot workspace will even set up the
summary<00:01:47.119><c> for</c><00:01:47.360><c> me</c><00:01:47.960><c> and</c><00:01:48.240><c> now</c><00:01:48.880><c> I</c><00:01:49.200><c> have</c><00:01:49.759><c> that</c><00:01:49.960><c> brand</c>

00:01:50.230 --> 00:01:50.240 align:start position:0%
summary for me and now I have that brand
 

00:01:50.240 --> 00:01:52.990 align:start position:0%
summary for me and now I have that brand
new<00:01:50.439><c> pull</c><00:01:50.759><c> request</c><00:01:51.520><c> with</c><00:01:51.719><c> those</c><00:01:51.960><c> unit</c><00:01:52.320><c> tests</c>

00:01:52.990 --> 00:01:53.000 align:start position:0%
new pull request with those unit tests
 

00:01:53.000 --> 00:01:55.429 align:start position:0%
new pull request with those unit tests
for<00:01:53.200><c> me</c><00:01:53.399><c> to</c><00:01:53.759><c> return</c><00:01:54.119><c> to</c><00:01:54.719><c> and</c><00:01:54.880><c> make</c><00:01:55.200><c> any</c>

00:01:55.429 --> 00:01:55.439 align:start position:0%
for me to return to and make any
 

00:01:55.439 --> 00:01:59.029 align:start position:0%
for me to return to and make any
additional<00:01:55.920><c> updates</c><00:01:56.640><c> that</c><00:01:56.960><c> I</c><00:01:57.240><c> see</c><00:01:57.880><c> fit</c><00:01:58.880><c> by</c>

00:01:59.029 --> 00:01:59.039 align:start position:0%
additional updates that I see fit by
 

00:01:59.039 --> 00:02:02.069 align:start position:0%
additional updates that I see fit by
utilizing<00:01:59.920><c> co-pilot</c><00:02:00.360><c> workspace</c><00:02:01.240><c> I</c><00:02:01.360><c> was</c><00:02:01.560><c> able</c>

00:02:02.069 --> 00:02:02.079 align:start position:0%
utilizing co-pilot workspace I was able
 

00:02:02.079 --> 00:02:05.190 align:start position:0%
utilizing co-pilot workspace I was able
to<00:02:02.399><c> get</c><00:02:02.719><c> those</c><00:02:03.119><c> initial</c><00:02:03.640><c> test</c><00:02:03.960><c> set</c><00:02:04.159><c> up</c><00:02:04.360><c> for</c><00:02:04.600><c> me</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
to get those initial test set up for me
 

00:02:05.200 --> 00:02:09.960 align:start position:0%
to get those initial test set up for me
without<00:02:05.799><c> having</c><00:02:06.399><c> to</c><00:02:06.600><c> leave</c><00:02:07.000><c> my</c><00:02:07.240><c> device</c>

