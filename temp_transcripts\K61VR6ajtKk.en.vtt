WEBVTT
Kind: captions
Language: en

00:00:00.440 --> 00:00:06.630 align:start position:0%
 
[Music]

00:00:06.630 --> 00:00:06.640 align:start position:0%
 
 

00:00:06.640 --> 00:00:09.790 align:start position:0%
 
hey<00:00:06.879><c> everyone</c><00:00:07.799><c> welcome</c><00:00:08.200><c> to</c><00:00:08.360><c> my</c><00:00:08.519><c> session</c><00:00:09.480><c> uh</c><00:00:09.639><c> my</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
hey everyone welcome to my session uh my
 

00:00:09.800 --> 00:00:12.190 align:start position:0%
hey everyone welcome to my session uh my
name<00:00:09.960><c> is</c><00:00:10.120><c> desan</c><00:00:10.639><c> banga</c><00:00:11.360><c> I'm</c><00:00:11.759><c> currently</c>

00:00:12.190 --> 00:00:12.200 align:start position:0%
name is desan banga I'm currently
 

00:00:12.200 --> 00:00:14.190 align:start position:0%
name is desan banga I'm currently
working<00:00:12.519><c> as</c><00:00:12.719><c> senior</c><00:00:13.080><c> data</c><00:00:13.440><c> analyst</c><00:00:14.000><c> with</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
working as senior data analyst with
 

00:00:14.200 --> 00:00:17.070 align:start position:0%
working as senior data analyst with
bridg<00:00:14.559><c> tree</c><00:00:15.559><c> and</c><00:00:15.879><c> uh</c><00:00:16.240><c> today</c><00:00:16.560><c> I'm</c><00:00:16.720><c> going</c><00:00:16.840><c> to</c>

00:00:17.070 --> 00:00:17.080 align:start position:0%
bridg tree and uh today I'm going to
 

00:00:17.080 --> 00:00:19.230 align:start position:0%
bridg tree and uh today I'm going to
present<00:00:17.560><c> one</c><00:00:17.720><c> of</c><00:00:17.880><c> my</c><00:00:18.119><c> published</c><00:00:18.600><c> work</c><00:00:18.960><c> called</c>

00:00:19.230 --> 00:00:19.240 align:start position:0%
present one of my published work called
 

00:00:19.240 --> 00:00:22.710 align:start position:0%
present one of my published work called
emotion<00:00:19.640><c> detection</c><00:00:20.039><c> in</c><00:00:20.560><c> speech</c><00:00:21.560><c> as</c><00:00:21.840><c> the</c><00:00:22.320><c> title</c>

00:00:22.710 --> 00:00:22.720 align:start position:0%
emotion detection in speech as the title
 

00:00:22.720 --> 00:00:24.870 align:start position:0%
emotion detection in speech as the title
says<00:00:23.320><c> that</c><00:00:23.480><c> we</c><00:00:23.640><c> are</c><00:00:23.800><c> trying</c><00:00:24.119><c> to</c><00:00:24.279><c> detect</c><00:00:24.680><c> an</c>

00:00:24.870 --> 00:00:24.880 align:start position:0%
says that we are trying to detect an
 

00:00:24.880 --> 00:00:28.230 align:start position:0%
says that we are trying to detect an
emotion<00:00:25.519><c> of</c><00:00:25.720><c> of</c><00:00:25.960><c> speech</c><00:00:26.240><c> of</c><00:00:26.400><c> an</c><00:00:26.760><c> individual</c><00:00:27.760><c> so</c>

00:00:28.230 --> 00:00:28.240 align:start position:0%
emotion of of speech of an individual so
 

00:00:28.240 --> 00:00:30.790 align:start position:0%
emotion of of speech of an individual so
we<00:00:29.119><c> uh</c><00:00:29.519><c> for</c><00:00:29.720><c> this</c>

00:00:30.790 --> 00:00:30.800 align:start position:0%
we uh for this
 

00:00:30.800 --> 00:00:32.870 align:start position:0%
we uh for this
research<00:00:31.240><c> work</c><00:00:31.560><c> we</c><00:00:31.720><c> use</c><00:00:31.960><c> a</c><00:00:32.160><c> data</c><00:00:32.439><c> set</c><00:00:32.759><c> which</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
research work we use a data set which
 

00:00:32.880 --> 00:00:33.910 align:start position:0%
research work we use a data set which
will<00:00:33.040><c> be</c><00:00:33.200><c> going</c><00:00:33.440><c> through</c><00:00:33.680><c> with</c><00:00:33.800><c> the</c>

00:00:33.910 --> 00:00:33.920 align:start position:0%
will be going through with the
 

00:00:33.920 --> 00:00:35.590 align:start position:0%
will be going through with the
methodology<00:00:34.600><c> what</c><00:00:34.719><c> kind</c><00:00:34.879><c> of</c><00:00:35.000><c> data</c><00:00:35.239><c> sets</c><00:00:35.440><c> we</c>

00:00:35.590 --> 00:00:35.600 align:start position:0%
methodology what kind of data sets we
 

00:00:35.600 --> 00:00:38.470 align:start position:0%
methodology what kind of data sets we
use<00:00:36.239><c> the</c><00:00:36.360><c> main</c><00:00:36.640><c> goal</c><00:00:36.920><c> was</c><00:00:37.079><c> to</c><00:00:37.280><c> identify</c><00:00:38.280><c> what</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
use the main goal was to identify what
 

00:00:38.480 --> 00:00:40.110 align:start position:0%
use the main goal was to identify what
kind<00:00:38.640><c> of</c><00:00:38.840><c> emotion</c><00:00:39.239><c> is</c><00:00:39.399><c> present</c><00:00:39.760><c> in</c><00:00:39.920><c> the</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
kind of emotion is present in the
 

00:00:40.120 --> 00:00:42.190 align:start position:0%
kind of emotion is present in the
person's<00:00:40.879><c> speech</c><00:00:41.320><c> when</c><00:00:41.440><c> they</c><00:00:41.559><c> are</c><00:00:41.719><c> talking</c><00:00:42.000><c> to</c>

00:00:42.190 --> 00:00:42.200 align:start position:0%
person's speech when they are talking to
 

00:00:42.200 --> 00:00:44.430 align:start position:0%
person's speech when they are talking to
somebody<00:00:42.680><c> or</c><00:00:42.879><c> when</c><00:00:43.000><c> they</c><00:00:43.120><c> are</c><00:00:43.600><c> when</c><00:00:43.760><c> they</c><00:00:43.879><c> are</c>

00:00:44.430 --> 00:00:44.440 align:start position:0%
somebody or when they are when they are
 

00:00:44.440 --> 00:00:46.350 align:start position:0%
somebody or when they are when they are
doing<00:00:44.760><c> their</c><00:00:44.960><c> daily</c><00:00:45.239><c> work</c><00:00:45.520><c> or</c><00:00:45.760><c> what</c><00:00:46.039><c> what</c><00:00:46.199><c> kind</c>

00:00:46.350 --> 00:00:46.360 align:start position:0%
doing their daily work or what what kind
 

00:00:46.360 --> 00:00:48.350 align:start position:0%
doing their daily work or what what kind
of<00:00:46.760><c> what</c><00:00:46.920><c> kind</c><00:00:47.079><c> of</c><00:00:47.199><c> speech</c><00:00:47.600><c> it</c>

00:00:48.350 --> 00:00:48.360 align:start position:0%
of what kind of speech it
 

00:00:48.360 --> 00:00:52.470 align:start position:0%
of what kind of speech it
is<00:00:49.760><c> so</c><00:00:50.760><c> going</c><00:00:51.079><c> ahead</c><00:00:51.440><c> this</c><00:00:51.559><c> should</c><00:00:51.760><c> be</c><00:00:52.000><c> our</c>

00:00:52.470 --> 00:00:52.480 align:start position:0%
is so going ahead this should be our
 

00:00:52.480 --> 00:00:54.029 align:start position:0%
is so going ahead this should be our
agenda<00:00:52.840><c> for</c><00:00:53.039><c> the</c><00:00:53.199><c> day</c><00:00:53.359><c> to</c><00:00:53.520><c> start</c><00:00:53.800><c> with</c>

00:00:54.029 --> 00:00:54.039 align:start position:0%
agenda for the day to start with
 

00:00:54.039 --> 00:00:55.869 align:start position:0%
agenda for the day to start with
introduction<00:00:54.719><c> then</c><00:00:54.840><c> we'll</c><00:00:55.079><c> come</c><00:00:55.280><c> up</c><00:00:55.440><c> with</c><00:00:55.559><c> our</c>

00:00:55.869 --> 00:00:55.879 align:start position:0%
introduction then we'll come up with our
 

00:00:55.879 --> 00:00:58.189 align:start position:0%
introduction then we'll come up with our
proposed<00:00:56.440><c> methodology</c><00:00:57.440><c> which</c><00:00:57.600><c> was</c><00:00:57.760><c> used</c><00:00:58.039><c> in</c>

00:00:58.189 --> 00:00:58.199 align:start position:0%
proposed methodology which was used in
 

00:00:58.199 --> 00:01:00.150 align:start position:0%
proposed methodology which was used in
building<00:00:58.600><c> this</c><00:00:58.800><c> model</c><00:00:59.199><c> for</c><00:00:59.440><c> This</c><00:00:59.559><c> research</c>

00:01:00.150 --> 00:01:00.160 align:start position:0%
building this model for This research
 

00:01:00.160 --> 00:01:03.029 align:start position:0%
building this model for This research
work<00:01:00.760><c> and</c><00:01:00.920><c> then</c><00:01:01.079><c> we'll</c><00:01:01.320><c> go</c><00:01:01.600><c> with</c><00:01:02.039><c> different</c><00:01:02.879><c> uh</c>

00:01:03.029 --> 00:01:03.039 align:start position:0%
work and then we'll go with different uh
 

00:01:03.039 --> 00:01:05.310 align:start position:0%
work and then we'll go with different uh
parts<00:01:03.320><c> of</c><00:01:03.480><c> the</c><00:01:03.600><c> methodology</c><00:01:04.320><c> like</c><00:01:04.519><c> data</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
parts of the methodology like data
 

00:01:05.320 --> 00:01:07.350 align:start position:0%
parts of the methodology like data
extraction<00:01:06.040><c> preparation</c><00:01:06.760><c> our</c><00:01:06.960><c> modeling</c>

00:01:07.350 --> 00:01:07.360 align:start position:0%
extraction preparation our modeling
 

00:01:07.360 --> 00:01:09.749 align:start position:0%
extraction preparation our modeling
research<00:01:08.159><c> and</c><00:01:08.360><c> finally</c><00:01:08.680><c> the</c>

00:01:09.749 --> 00:01:09.759 align:start position:0%
research and finally the
 

00:01:09.759 --> 00:01:12.310 align:start position:0%
research and finally the
conclusion<00:01:10.759><c> so</c><00:01:10.960><c> moving</c><00:01:11.360><c> ahead</c><00:01:11.960><c> uh</c><00:01:12.080><c> we'll</c>

00:01:12.310 --> 00:01:12.320 align:start position:0%
conclusion so moving ahead uh we'll
 

00:01:12.320 --> 00:01:13.870 align:start position:0%
conclusion so moving ahead uh we'll
start<00:01:12.560><c> with</c><00:01:12.680><c> the</c><00:01:12.799><c> introduction</c><00:01:13.400><c> so</c><00:01:13.640><c> as</c><00:01:13.759><c> I</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
start with the introduction so as I
 

00:01:13.880 --> 00:01:15.749 align:start position:0%
start with the introduction so as I
mentioned<00:01:14.360><c> as</c><00:01:14.479><c> a</c><00:01:14.680><c> topic</c><00:01:14.960><c> space</c><00:01:15.439><c> it's</c><00:01:15.600><c> the</c>

00:01:15.749 --> 00:01:15.759 align:start position:0%
mentioned as a topic space it's the
 

00:01:15.759 --> 00:01:17.670 align:start position:0%
mentioned as a topic space it's the
ability<00:01:16.159><c> of</c><00:01:16.280><c> the</c><00:01:16.439><c> humans</c><00:01:17.200><c> how</c><00:01:17.400><c> they</c><00:01:17.520><c> are</c>

00:01:17.670 --> 00:01:17.680 align:start position:0%
ability of the humans how they are
 

00:01:17.680 --> 00:01:19.830 align:start position:0%
ability of the humans how they are
verbally<00:01:18.159><c> communicating</c><00:01:18.759><c> with</c><00:01:18.960><c> each</c><00:01:19.200><c> other</c>

00:01:19.830 --> 00:01:19.840 align:start position:0%
verbally communicating with each other
 

00:01:19.840 --> 00:01:21.390 align:start position:0%
verbally communicating with each other
what<00:01:19.960><c> are</c><00:01:20.119><c> their</c><00:01:20.360><c> emotions</c><00:01:20.960><c> when</c><00:01:21.119><c> they</c><00:01:21.240><c> are</c>

00:01:21.390 --> 00:01:21.400 align:start position:0%
what are their emotions when they are
 

00:01:21.400 --> 00:01:23.550 align:start position:0%
what are their emotions when they are
communicating<00:01:22.320><c> you</c><00:01:22.439><c> know</c><00:01:22.720><c> they</c><00:01:22.840><c> are</c><00:01:23.079><c> angry</c>

00:01:23.550 --> 00:01:23.560 align:start position:0%
communicating you know they are angry
 

00:01:23.560 --> 00:01:25.749 align:start position:0%
communicating you know they are angry
they<00:01:23.680><c> can</c><00:01:24.000><c> be</c><00:01:24.240><c> sad</c><00:01:24.600><c> they</c><00:01:24.720><c> can</c><00:01:24.880><c> be</c><00:01:25.040><c> surprised</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
they can be sad they can be surprised
 

00:01:25.759 --> 00:01:28.429 align:start position:0%
they can be sad they can be surprised
they<00:01:25.920><c> can</c><00:01:26.119><c> be</c><00:01:26.680><c> they</c><00:01:26.840><c> can</c><00:01:27.040><c> be</c><00:01:28.000><c> any</c><00:01:28.200><c> other</c>

00:01:28.429 --> 00:01:28.439 align:start position:0%
they can be they can be any other
 

00:01:28.439 --> 00:01:30.990 align:start position:0%
they can be they can be any other
emotion<00:01:29.240><c> so</c><00:01:29.880><c> n</c><00:01:30.000><c> LP</c><00:01:30.320><c> natural</c><00:01:30.680><c> language</c>

00:01:30.990 --> 00:01:31.000 align:start position:0%
emotion so n LP natural language
 

00:01:31.000 --> 00:01:32.950 align:start position:0%
emotion so n LP natural language
processing<00:01:31.360><c> is</c><00:01:31.479><c> an</c><00:01:31.640><c> ability</c><00:01:32.000><c> of</c><00:01:32.119><c> machine</c><00:01:32.439><c> to</c>

00:01:32.950 --> 00:01:32.960 align:start position:0%
processing is an ability of machine to
 

00:01:32.960 --> 00:01:34.990 align:start position:0%
processing is an ability of machine to
understand<00:01:33.479><c> and</c><00:01:33.680><c> interrupt</c><00:01:34.159><c> human</c><00:01:34.560><c> language</c>

00:01:34.990 --> 00:01:35.000 align:start position:0%
understand and interrupt human language
 

00:01:35.000 --> 00:01:37.670 align:start position:0%
understand and interrupt human language
in<00:01:35.159><c> written</c><00:01:35.439><c> or</c><00:01:35.640><c> spoken</c><00:01:36.479><c> so</c><00:01:36.759><c> in</c><00:01:37.040><c> for</c><00:01:37.200><c> Forever</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
in written or spoken so in for Forever
 

00:01:37.680 --> 00:01:39.670 align:start position:0%
in written or spoken so in for Forever
This<00:01:37.880><c> research</c><00:01:38.240><c> work</c><00:01:38.479><c> we'll</c><00:01:38.680><c> be</c><00:01:38.799><c> using</c><00:01:39.200><c> human</c>

00:01:39.670 --> 00:01:39.680 align:start position:0%
This research work we'll be using human
 

00:01:39.680 --> 00:01:43.190 align:start position:0%
This research work we'll be using human
audio<00:01:40.159><c> data</c><00:01:40.880><c> and</c><00:01:41.079><c> identifying</c><00:01:41.799><c> their</c><00:01:42.240><c> emotion</c>

00:01:43.190 --> 00:01:43.200 align:start position:0%
audio data and identifying their emotion
 

00:01:43.200 --> 00:01:45.870 align:start position:0%
audio data and identifying their emotion
in<00:01:43.479><c> in</c><00:01:43.600><c> their</c><00:01:43.799><c> audio</c><00:01:44.479><c> in</c><00:01:44.719><c> in</c><00:01:44.840><c> their</c><00:01:45.040><c> speech</c>

00:01:45.870 --> 00:01:45.880 align:start position:0%
in in their audio in in their speech
 

00:01:45.880 --> 00:01:48.389 align:start position:0%
in in their audio in in their speech
using<00:01:46.200><c> natural</c><00:01:46.600><c> language</c>

00:01:48.389 --> 00:01:48.399 align:start position:0%
using natural language
 

00:01:48.399 --> 00:01:51.030 align:start position:0%
using natural language
process<00:01:49.399><c> so</c><00:01:49.640><c> there</c><00:01:49.759><c> are</c><00:01:49.960><c> certain</c><00:01:50.439><c> factors</c>

00:01:51.030 --> 00:01:51.040 align:start position:0%
process so there are certain factors
 

00:01:51.040 --> 00:01:52.749 align:start position:0%
process so there are certain factors
which<00:01:51.320><c> which</c><00:01:51.479><c> helps</c><00:01:51.759><c> us</c><00:01:52.119><c> identifying</c>

00:01:52.749 --> 00:01:52.759 align:start position:0%
which which helps us identifying
 

00:01:52.759 --> 00:01:54.870 align:start position:0%
which which helps us identifying
especially<00:01:53.159><c> when</c><00:01:53.280><c> it</c><00:01:53.439><c> comes</c><00:01:53.640><c> to</c><00:01:53.799><c> the</c><00:01:54.520><c> comes</c><00:01:54.719><c> to</c>

00:01:54.870 --> 00:01:54.880 align:start position:0%
especially when it comes to the comes to
 

00:01:54.880 --> 00:01:58.190 align:start position:0%
especially when it comes to the comes to
the<00:01:55.040><c> speech</c><00:01:55.799><c> how</c><00:01:56.799><c> person</c><00:01:57.119><c> is</c><00:01:57.280><c> talking</c><00:01:58.039><c> that</c>

00:01:58.190 --> 00:01:58.200 align:start position:0%
the speech how person is talking that
 

00:01:58.200 --> 00:02:00.350 align:start position:0%
the speech how person is talking that
can<00:01:58.360><c> be</c><00:01:58.520><c> the</c><00:01:58.680><c> intensity</c><00:01:59.360><c> how</c><00:01:59.560><c> loud</c><00:01:59.880><c> loud</c><00:02:00.159><c> or</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
can be the intensity how loud loud or
 

00:02:00.360 --> 00:02:03.029 align:start position:0%
can be the intensity how loud loud or
how<00:02:00.759><c> pressure</c><00:02:01.200><c> level</c><00:02:01.840><c> when</c><00:02:02.039><c> they</c><00:02:02.119><c> are</c><00:02:02.280><c> talking</c>

00:02:03.029 --> 00:02:03.039 align:start position:0%
how pressure level when they are talking
 

00:02:03.039 --> 00:02:04.910 align:start position:0%
how pressure level when they are talking
that<00:02:03.200><c> can</c><00:02:03.360><c> be</c><00:02:03.520><c> their</c><00:02:03.759><c> pitch</c><00:02:04.200><c> their</c><00:02:04.439><c> variation</c>

00:02:04.910 --> 00:02:04.920 align:start position:0%
that can be their pitch their variation
 

00:02:04.920 --> 00:02:06.990 align:start position:0%
that can be their pitch their variation
of<00:02:05.159><c> frequency</c><00:02:06.159><c> that</c><00:02:06.320><c> can</c><00:02:06.479><c> be</c><00:02:06.640><c> if</c><00:02:06.759><c> they</c><00:02:06.880><c> are</c>

00:02:06.990 --> 00:02:07.000 align:start position:0%
of frequency that can be if they are
 

00:02:07.000 --> 00:02:09.070 align:start position:0%
of frequency that can be if they are
stammering<00:02:07.520><c> or</c><00:02:07.640><c> not</c><00:02:08.239><c> that</c><00:02:08.399><c> can</c><00:02:08.560><c> be</c><00:02:08.759><c> their</c>

00:02:09.070 --> 00:02:09.080 align:start position:0%
stammering or not that can be their
 

00:02:09.080 --> 00:02:11.670 align:start position:0%
stammering or not that can be their
gloal<00:02:09.640><c> pulse</c><00:02:10.640><c> it</c><00:02:10.800><c> can</c><00:02:10.920><c> be</c><00:02:11.080><c> their</c><00:02:11.280><c> speaking</c>

00:02:11.670 --> 00:02:11.680 align:start position:0%
gloal pulse it can be their speaking
 

00:02:11.680 --> 00:02:14.270 align:start position:0%
gloal pulse it can be their speaking
rate<00:02:12.280><c> how</c><00:02:12.560><c> fast</c><00:02:12.920><c> somebody</c><00:02:13.319><c> is</c><00:02:13.480><c> talking</c><00:02:13.879><c> or</c><00:02:14.080><c> how</c>

00:02:14.270 --> 00:02:14.280 align:start position:0%
rate how fast somebody is talking or how
 

00:02:14.280 --> 00:02:16.390 align:start position:0%
rate how fast somebody is talking or how
nervous<00:02:14.760><c> they</c><00:02:14.920><c> are</c><00:02:15.239><c> like</c><00:02:15.440><c> the</c><00:02:15.560><c> way</c><00:02:15.800><c> I</c><00:02:15.920><c> am</c><00:02:16.239><c> right</c>

00:02:16.390 --> 00:02:16.400 align:start position:0%
nervous they are like the way I am right
 

00:02:16.400 --> 00:02:19.630 align:start position:0%
nervous they are like the way I am right
now<00:02:17.040><c> so</c><00:02:17.319><c> how</c><00:02:17.480><c> fast</c><00:02:17.840><c> they</c><00:02:17.920><c> are</c><00:02:18.120><c> talking</c><00:02:18.840><c> how</c><00:02:19.560><c> uh</c>

00:02:19.630 --> 00:02:19.640 align:start position:0%
now so how fast they are talking how uh
 

00:02:19.640 --> 00:02:21.110 align:start position:0%
now so how fast they are talking how uh
you<00:02:19.760><c> know</c><00:02:19.959><c> what</c><00:02:20.120><c> kind</c><00:02:20.280><c> of</c><00:02:20.480><c> action</c><00:02:20.840><c> they</c><00:02:20.920><c> are</c>

00:02:21.110 --> 00:02:21.120 align:start position:0%
you know what kind of action they are
 

00:02:21.120 --> 00:02:23.630 align:start position:0%
you know what kind of action they are
using<00:02:21.560><c> what</c><00:02:21.720><c> kind</c><00:02:21.840><c> of</c><00:02:22.000><c> WS</c><00:02:22.440><c> they</c><00:02:22.560><c> are</c><00:02:22.720><c> using</c><00:02:23.480><c> but</c>

00:02:23.630 --> 00:02:23.640 align:start position:0%
using what kind of WS they are using but
 

00:02:23.640 --> 00:02:26.150 align:start position:0%
using what kind of WS they are using but
for<00:02:23.920><c> this</c><00:02:24.080><c> research</c><00:02:24.599><c> we</c><00:02:24.879><c> have</c><00:02:25.400><c> you</c>

00:02:26.150 --> 00:02:26.160 align:start position:0%
for this research we have you
 

00:02:26.160 --> 00:02:28.710 align:start position:0%
for this research we have you
know<00:02:27.160><c> M</c><00:02:27.440><c> maintain</c><00:02:27.800><c> our</c><00:02:28.040><c> Focus</c><00:02:28.360><c> to</c><00:02:28.519><c> a</c>

00:02:28.710 --> 00:02:28.720 align:start position:0%
know M maintain our Focus to a
 

00:02:28.720 --> 00:02:30.350 align:start position:0%
know M maintain our Focus to a
particular<00:02:29.160><c> speech</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
particular speech
 

00:02:30.360 --> 00:02:32.030 align:start position:0%
particular speech
which<00:02:30.480><c> will</c><00:02:30.680><c> be</c><00:02:30.840><c> coming</c><00:02:31.160><c> across</c><00:02:31.480><c> in</c><00:02:31.599><c> the</c><00:02:31.840><c> next</c>

00:02:32.030 --> 00:02:32.040 align:start position:0%
which will be coming across in the next
 

00:02:32.040 --> 00:02:34.270 align:start position:0%
which will be coming across in the next
slide<00:02:32.400><c> so</c><00:02:32.680><c> what</c><00:02:32.920><c> what</c><00:02:33.319><c> what</c><00:02:33.480><c> that</c><00:02:33.720><c> data</c><00:02:34.000><c> looks</c>

00:02:34.270 --> 00:02:34.280 align:start position:0%
slide so what what what that data looks
 

00:02:34.280 --> 00:02:37.150 align:start position:0%
slide so what what what that data looks
like<00:02:34.840><c> and</c><00:02:35.239><c> identifying</c><00:02:36.239><c> which</c><00:02:36.519><c> emotion</c><00:02:36.920><c> is</c>

00:02:37.150 --> 00:02:37.160 align:start position:0%
like and identifying which emotion is
 

00:02:37.160 --> 00:02:38.990 align:start position:0%
like and identifying which emotion is
present<00:02:37.519><c> in</c><00:02:37.680><c> this</c><00:02:37.959><c> speech</c><00:02:38.319><c> so</c><00:02:38.519><c> for</c><00:02:38.800><c> This</c>

00:02:38.990 --> 00:02:39.000 align:start position:0%
present in this speech so for This
 

00:02:39.000 --> 00:02:40.869 align:start position:0%
present in this speech so for This
research<00:02:39.400><c> purposes</c><00:02:39.879><c> we</c><00:02:40.000><c> have</c><00:02:40.159><c> to</c><00:02:40.360><c> narrow</c><00:02:40.720><c> it</c>

00:02:40.869 --> 00:02:40.879 align:start position:0%
research purposes we have to narrow it
 

00:02:40.879 --> 00:02:43.110 align:start position:0%
research purposes we have to narrow it
down<00:02:41.040><c> to</c><00:02:41.239><c> certain</c>

00:02:43.110 --> 00:02:43.120 align:start position:0%
down to certain
 

00:02:43.120 --> 00:02:46.790 align:start position:0%
down to certain
Notions<00:02:44.120><c> so</c><00:02:44.480><c> there</c><00:02:44.680><c> are</c><00:02:45.000><c> already</c><00:02:45.599><c> some</c><00:02:46.239><c> system</c>

00:02:46.790 --> 00:02:46.800 align:start position:0%
Notions so there are already some system
 

00:02:46.800 --> 00:02:49.390 align:start position:0%
Notions so there are already some system
exist<00:02:47.239><c> which</c><00:02:47.480><c> identify</c><00:02:48.120><c> the</c><00:02:48.280><c> speech</c><00:02:49.120><c> and</c>

00:02:49.390 --> 00:02:49.400 align:start position:0%
exist which identify the speech and
 

00:02:49.400 --> 00:02:51.229 align:start position:0%
exist which identify the speech and
other<00:02:49.720><c> capabilities</c><00:02:50.400><c> so</c><00:02:50.599><c> what</c><00:02:50.760><c> we</c><00:02:50.840><c> are</c><00:02:51.000><c> trying</c>

00:02:51.229 --> 00:02:51.239 align:start position:0%
other capabilities so what we are trying
 

00:02:51.239 --> 00:02:53.790 align:start position:0%
other capabilities so what we are trying
to<00:02:51.400><c> do</c><00:02:51.640><c> here</c><00:02:51.840><c> is</c><00:02:51.959><c> to</c><00:02:52.200><c> contribute</c><00:02:52.680><c> to</c><00:02:52.959><c> system</c>

00:02:53.790 --> 00:02:53.800 align:start position:0%
to do here is to contribute to system
 

00:02:53.800 --> 00:02:55.830 align:start position:0%
to do here is to contribute to system
speech<00:02:54.159><c> and</c><00:02:54.400><c> sentiment</c><00:02:54.959><c> analysis</c><00:02:55.480><c> for</c><00:02:55.680><c> the</c>

00:02:55.830 --> 00:02:55.840 align:start position:0%
speech and sentiment analysis for the
 

00:02:55.840 --> 00:02:58.589 align:start position:0%
speech and sentiment analysis for the
AIU<00:02:56.840><c> we</c><00:02:56.959><c> are</c><00:02:57.120><c> implementing</c><00:02:57.720><c> predictor</c><00:02:58.239><c> models</c>

00:02:58.589 --> 00:02:58.599 align:start position:0%
AIU we are implementing predictor models
 

00:02:58.599 --> 00:03:00.630 align:start position:0%
AIU we are implementing predictor models
to<00:02:58.800><c> classify</c><00:02:59.319><c> the</c><00:02:59.440><c> emotion</c><00:03:00.040><c> action</c><00:03:00.319><c> in</c><00:03:00.440><c> the</c>

00:03:00.630 --> 00:03:00.640 align:start position:0%
to classify the emotion action in the
 

00:03:00.640 --> 00:03:02.910 align:start position:0%
to classify the emotion action in the
speech<00:03:01.480><c> we</c><00:03:01.640><c> would</c><00:03:01.879><c> be</c><00:03:02.080><c> determining</c><00:03:02.680><c> the</c>

00:03:02.910 --> 00:03:02.920 align:start position:0%
speech we would be determining the
 

00:03:02.920 --> 00:03:06.070 align:start position:0%
speech we would be determining the
accuracies<00:03:03.920><c> and</c><00:03:04.920><c> of</c><00:03:05.080><c> the</c><00:03:05.239><c> different</c><00:03:05.560><c> models</c>

00:03:06.070 --> 00:03:06.080 align:start position:0%
accuracies and of the different models
 

00:03:06.080 --> 00:03:08.910 align:start position:0%
accuracies and of the different models
and<00:03:06.280><c> identify</c><00:03:07.000><c> which</c><00:03:07.159><c> one</c><00:03:07.360><c> is</c><00:03:07.560><c> good</c><00:03:08.239><c> because</c>

00:03:08.910 --> 00:03:08.920 align:start position:0%
and identify which one is good because
 

00:03:08.920 --> 00:03:11.070 align:start position:0%
and identify which one is good because
uh<00:03:09.120><c> it</c><00:03:09.319><c> it</c><00:03:09.480><c> varies</c><00:03:09.840><c> from</c><00:03:10.280><c> from</c><00:03:10.560><c> person</c><00:03:10.840><c> to</c>

00:03:11.070 --> 00:03:11.080 align:start position:0%
uh it it varies from from person to
 

00:03:11.080 --> 00:03:13.229 align:start position:0%
uh it it varies from from person to
person<00:03:11.319><c> or</c><00:03:11.480><c> from</c><00:03:11.640><c> gender</c><00:03:12.000><c> to</c><00:03:12.200><c> gender</c><00:03:12.879><c> the</c><00:03:13.000><c> way</c>

00:03:13.229 --> 00:03:13.239 align:start position:0%
person or from gender to gender the way
 

00:03:13.239 --> 00:03:15.430 align:start position:0%
person or from gender to gender the way
male<00:03:13.599><c> person</c><00:03:13.920><c> talks</c><00:03:14.280><c> is</c><00:03:14.480><c> different</c><00:03:15.040><c> from</c><00:03:15.319><c> the</c>

00:03:15.430 --> 00:03:15.440 align:start position:0%
male person talks is different from the
 

00:03:15.440 --> 00:03:18.070 align:start position:0%
male person talks is different from the
from<00:03:15.599><c> a</c><00:03:15.760><c> female</c>

00:03:18.070 --> 00:03:18.080 align:start position:0%
from a female
 

00:03:18.080 --> 00:03:21.110 align:start position:0%
from a female
person<00:03:19.080><c> so</c><00:03:19.319><c> our</c><00:03:19.640><c> proposed</c><00:03:20.040><c> methodology</c><00:03:20.840><c> or</c>

00:03:21.110 --> 00:03:21.120 align:start position:0%
person so our proposed methodology or
 

00:03:21.120 --> 00:03:24.390 align:start position:0%
person so our proposed methodology or
the<00:03:21.360><c> or</c><00:03:21.560><c> the</c><00:03:22.000><c> pathway</c><00:03:22.480><c> to</c><00:03:22.840><c> to</c><00:03:23.400><c> to</c><00:03:23.720><c> this</c><00:03:24.000><c> to</c><00:03:24.239><c> this</c>

00:03:24.390 --> 00:03:24.400 align:start position:0%
the or the pathway to to to this to this
 

00:03:24.400 --> 00:03:26.670 align:start position:0%
the or the pathway to to to this to this
model<00:03:24.760><c> is</c><00:03:25.319><c> of</c><00:03:25.480><c> course</c><00:03:25.760><c> the</c><00:03:25.920><c> audio</c><00:03:26.319><c> audio</c>

00:03:26.670 --> 00:03:26.680 align:start position:0%
model is of course the audio audio
 

00:03:26.680 --> 00:03:28.830 align:start position:0%
model is of course the audio audio
extraction<00:03:27.519><c> or</c><00:03:27.680><c> the</c><00:03:27.760><c> feature</c><00:03:28.200><c> extraction</c>

00:03:28.830 --> 00:03:28.840 align:start position:0%
extraction or the feature extraction
 

00:03:28.840 --> 00:03:30.710 align:start position:0%
extraction or the feature extraction
from<00:03:29.080><c> from</c><00:03:29.239><c> the</c><00:03:29.319><c> audio</c><00:03:29.799><c> data</c><00:03:30.080><c> set</c><00:03:30.560><c> and</c>

00:03:30.710 --> 00:03:30.720 align:start position:0%
from from the audio data set and
 

00:03:30.720 --> 00:03:33.070 align:start position:0%
from from the audio data set and
building<00:03:31.080><c> a</c><00:03:31.280><c> model</c><00:03:32.040><c> training</c><00:03:32.439><c> of</c><00:03:32.640><c> training</c><00:03:32.959><c> of</c>

00:03:33.070 --> 00:03:33.080 align:start position:0%
building a model training of training of
 

00:03:33.080 --> 00:03:35.509 align:start position:0%
building a model training of training of
a<00:03:33.239><c> data</c><00:03:33.519><c> set</c><00:03:33.760><c> and</c><00:03:33.920><c> then</c><00:03:34.080><c> testing</c><00:03:34.439><c> our</c><00:03:34.760><c> data</c><00:03:35.000><c> set</c>

00:03:35.509 --> 00:03:35.519 align:start position:0%
a data set and then testing our data set
 

00:03:35.519 --> 00:03:37.710 align:start position:0%
a data set and then testing our data set
and<00:03:35.680><c> then</c><00:03:35.959><c> calculating</c><00:03:36.480><c> at</c><00:03:36.640><c> the</c><00:03:36.760><c> end</c><00:03:37.040><c> accuracy</c>

00:03:37.710 --> 00:03:37.720 align:start position:0%
and then calculating at the end accuracy
 

00:03:37.720 --> 00:03:39.830 align:start position:0%
and then calculating at the end accuracy
which<00:03:37.959><c> model</c><00:03:38.319><c> works</c><00:03:38.840><c> best</c><00:03:39.120><c> for</c><00:03:39.360><c> for</c><00:03:39.599><c> which</c>

00:03:39.830 --> 00:03:39.840 align:start position:0%
which model works best for for which
 

00:03:39.840 --> 00:03:41.949 align:start position:0%
which model works best for for which
data<00:03:40.159><c> set</c><00:03:40.599><c> we'll</c><00:03:40.799><c> be</c><00:03:41.000><c> coming</c><00:03:41.319><c> we'll</c><00:03:41.560><c> see</c><00:03:41.840><c> that</c>

00:03:41.949 --> 00:03:41.959 align:start position:0%
data set we'll be coming we'll see that
 

00:03:41.959 --> 00:03:44.309 align:start position:0%
data set we'll be coming we'll see that
in<00:03:42.080><c> the</c><00:03:42.280><c> result</c>

00:03:44.309 --> 00:03:44.319 align:start position:0%
in the result
 

00:03:44.319 --> 00:03:48.550 align:start position:0%
in the result
section<00:03:45.319><c> so</c><00:03:45.519><c> now</c><00:03:46.159><c> so</c><00:03:46.680><c> for</c><00:03:47.040><c> for</c><00:03:47.640><c> for</c><00:03:47.920><c> for</c><00:03:48.159><c> this</c>

00:03:48.550 --> 00:03:48.560 align:start position:0%
section so now so for for for for this
 

00:03:48.560 --> 00:03:50.750 align:start position:0%
section so now so for for for for this
for<00:03:48.840><c> this</c><00:03:49.439><c> exercise</c><00:03:49.959><c> we</c><00:03:50.080><c> have</c><00:03:50.239><c> used</c><00:03:50.560><c> the</c>

00:03:50.750 --> 00:03:50.760 align:start position:0%
for this exercise we have used the
 

00:03:50.760 --> 00:03:53.750 align:start position:0%
for this exercise we have used the
ravness<00:03:51.680><c> data</c><00:03:52.000><c> set</c><00:03:52.599><c> which</c><00:03:52.720><c> is</c><00:03:52.920><c> called</c><00:03:53.200><c> redon</c>

00:03:53.750 --> 00:03:53.760 align:start position:0%
ravness data set which is called redon
 

00:03:53.760 --> 00:03:55.990 align:start position:0%
ravness data set which is called redon
audio<00:03:54.079><c> visual</c><00:03:54.400><c> data</c><00:03:54.640><c> of</c><00:03:54.840><c> emotion</c><00:03:55.239><c> speech</c><00:03:55.760><c> and</c>

00:03:55.990 --> 00:03:56.000 align:start position:0%
audio visual data of emotion speech and
 

00:03:56.000 --> 00:03:58.309 align:start position:0%
audio visual data of emotion speech and
and<00:03:56.120><c> we</c><00:03:56.280><c> have</c><00:03:56.480><c> also</c><00:03:56.799><c> used</c><00:03:57.519><c> survey</c><00:03:57.959><c> audio</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
and we have also used survey audio
 

00:03:58.319 --> 00:04:00.710 align:start position:0%
and we have also used survey audio
visual<00:03:58.680><c> experimental</c><00:03:59.280><c> emotion</c><00:03:59.879><c> data</c><00:04:00.120><c> sets</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
visual experimental emotion data sets
 

00:04:00.720 --> 00:04:02.309 align:start position:0%
visual experimental emotion data sets
which<00:04:00.840><c> is</c><00:04:01.000><c> a</c><00:04:01.280><c> which</c><00:04:01.400><c> is</c><00:04:01.599><c> again</c><00:04:01.799><c> a</c><00:04:01.959><c> British</c>

00:04:02.309 --> 00:04:02.319 align:start position:0%
which is a which is again a British
 

00:04:02.319 --> 00:04:04.830 align:start position:0%
which is a which is again a British
English<00:04:02.680><c> data</c><00:04:02.959><c> set</c><00:04:03.879><c> so</c><00:04:04.079><c> the</c><00:04:04.200><c> characteristics</c>

00:04:04.830 --> 00:04:04.840 align:start position:0%
English data set so the characteristics
 

00:04:04.840 --> 00:04:07.110 align:start position:0%
English data set so the characteristics
of<00:04:05.040><c> that</c><00:04:05.200><c> data</c><00:04:05.480><c> set</c><00:04:05.720><c> it</c><00:04:05.959><c> consists</c><00:04:06.319><c> of</c><00:04:06.480><c> 12</c><00:04:06.760><c> males</c>

00:04:07.110 --> 00:04:07.120 align:start position:0%
of that data set it consists of 12 males
 

00:04:07.120 --> 00:04:10.509 align:start position:0%
of that data set it consists of 12 males
and<00:04:07.319><c> 12</c><00:04:07.680><c> female</c><00:04:08.480><c> female</c><00:04:08.879><c> actors</c><00:04:09.519><c> both</c><00:04:09.920><c> in</c><00:04:10.319><c> in</c>

00:04:10.509 --> 00:04:10.519 align:start position:0%
and 12 female female actors both in in
 

00:04:10.519 --> 00:04:12.750 align:start position:0%
and 12 female female actors both in in
audio<00:04:10.879><c> and</c><00:04:11.040><c> the</c><00:04:11.239><c> video</c><00:04:11.920><c> so</c><00:04:12.319><c> they</c><00:04:12.519><c> have</c>

00:04:12.750 --> 00:04:12.760 align:start position:0%
audio and the video so they have
 

00:04:12.760 --> 00:04:14.309 align:start position:0%
audio and the video so they have
different<00:04:13.159><c> emotions</c><00:04:13.720><c> throughout</c><00:04:14.159><c> their</c>

00:04:14.309 --> 00:04:14.319 align:start position:0%
different emotions throughout their
 

00:04:14.319 --> 00:04:16.550 align:start position:0%
different emotions throughout their
speech<00:04:14.959><c> we</c><00:04:15.120><c> have</c><00:04:15.360><c> categorize</c><00:04:15.959><c> some</c><00:04:16.359><c> like</c>

00:04:16.550 --> 00:04:16.560 align:start position:0%
speech we have categorize some like
 

00:04:16.560 --> 00:04:20.789 align:start position:0%
speech we have categorize some like
neutral<00:04:17.239><c> calm</c><00:04:17.759><c> happy</c><00:04:18.199><c> sad</c><00:04:18.639><c> anger</c><00:04:19.400><c> surprise</c><00:04:20.400><c> we</c>

00:04:20.789 --> 00:04:20.799 align:start position:0%
neutral calm happy sad anger surprise we
 

00:04:20.799 --> 00:04:23.230 align:start position:0%
neutral calm happy sad anger surprise we
have<00:04:21.040><c> used</c><00:04:21.440><c> their</c><00:04:21.720><c> we</c><00:04:21.880><c> have</c><00:04:22.120><c> extracted</c><00:04:22.720><c> their</c>

00:04:23.230 --> 00:04:23.240 align:start position:0%
have used their we have extracted their
 

00:04:23.240 --> 00:04:27.670 align:start position:0%
have used their we have extracted their
data<00:04:23.680><c> files</c><00:04:24.120><c> audio</c><00:04:24.759><c> files</c><00:04:25.759><c> in</c><00:04:25.919><c> the</c><00:04:26.440><c> bav</c><00:04:26.960><c> format</c>

00:04:27.670 --> 00:04:27.680 align:start position:0%
data files audio files in the bav format
 

00:04:27.680 --> 00:04:29.749 align:start position:0%
data files audio files in the bav format
so<00:04:27.840><c> when</c><00:04:28.000><c> we</c><00:04:28.160><c> go</c><00:04:28.320><c> for</c><00:04:28.520><c> our</c><00:04:28.759><c> data</c><00:04:29.080><c> preparation</c>

00:04:29.749 --> 00:04:29.759 align:start position:0%
so when we go for our data preparation
 

00:04:29.759 --> 00:04:32.749 align:start position:0%
so when we go for our data preparation
we<00:04:29.960><c> have</c><00:04:30.160><c> used</c><00:04:30.520><c> those</c><00:04:30.720><c> audio</c><00:04:31.160><c> files</c><00:04:32.080><c> and</c><00:04:32.520><c> then</c>

00:04:32.749 --> 00:04:32.759 align:start position:0%
we have used those audio files and then
 

00:04:32.759 --> 00:04:35.950 align:start position:0%
we have used those audio files and then
we<00:04:32.919><c> use</c><00:04:33.360><c> Python</c><00:04:34.199><c> language</c><00:04:34.800><c> or</c><00:04:35.320><c> especially</c><00:04:35.800><c> the</c>

00:04:35.950 --> 00:04:35.960 align:start position:0%
we use Python language or especially the
 

00:04:35.960 --> 00:04:38.310 align:start position:0%
we use Python language or especially the
python<00:04:36.280><c> Library</c><00:04:36.840><c> Pi</c><00:04:37.080><c> audio</c><00:04:37.400><c> analysis</c><00:04:38.080><c> to</c>

00:04:38.310 --> 00:04:38.320 align:start position:0%
python Library Pi audio analysis to
 

00:04:38.320 --> 00:04:40.230 align:start position:0%
python Library Pi audio analysis to
extract<00:04:38.840><c> the</c><00:04:39.240><c> to</c><00:04:39.440><c> extract</c><00:04:39.840><c> the</c><00:04:39.960><c> different</c>

00:04:40.230 --> 00:04:40.240 align:start position:0%
extract the to extract the different
 

00:04:40.240 --> 00:04:41.749 align:start position:0%
extract the to extract the different
features<00:04:40.720><c> to</c><00:04:40.880><c> extract</c><00:04:41.240><c> the</c><00:04:41.400><c> different</c>

00:04:41.749 --> 00:04:41.759 align:start position:0%
features to extract the different
 

00:04:41.759 --> 00:04:43.830 align:start position:0%
features to extract the different
emotions<00:04:42.280><c> which</c><00:04:42.400><c> are</c><00:04:42.680><c> present</c><00:04:43.160><c> or</c><00:04:43.320><c> to</c><00:04:43.479><c> extract</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
emotions which are present or to extract
 

00:04:43.840 --> 00:04:45.189 align:start position:0%
emotions which are present or to extract
the<00:04:43.960><c> different</c><00:04:44.240><c> characteristics</c><00:04:44.880><c> of</c><00:04:45.000><c> the</c>

00:04:45.189 --> 00:04:45.199 align:start position:0%
the different characteristics of the
 

00:04:45.199 --> 00:04:48.189 align:start position:0%
the different characteristics of the
speech<00:04:46.199><c> and</c><00:04:46.479><c> those</c><00:04:47.080><c> characteristics</c><00:04:47.880><c> those</c>

00:04:48.189 --> 00:04:48.199 align:start position:0%
speech and those characteristics those
 

00:04:48.199 --> 00:04:49.670 align:start position:0%
speech and those characteristics those
features<00:04:48.960><c> can</c>

00:04:49.670 --> 00:04:49.680 align:start position:0%
features can
 

00:04:49.680 --> 00:04:53.749 align:start position:0%
features can
be<00:04:50.680><c> ZR</c><00:04:51.280><c> zcr</c><00:04:52.039><c> energy</c><00:04:52.600><c> entropy</c><00:04:53.120><c> of</c><00:04:53.240><c> energy</c>

00:04:53.749 --> 00:04:53.759 align:start position:0%
be ZR zcr energy entropy of energy
 

00:04:53.759 --> 00:04:56.469 align:start position:0%
be ZR zcr energy entropy of energy
spectral<00:04:54.199><c> score</c><00:04:55.080><c> spectral</c><00:04:55.600><c> energy</c><00:04:56.039><c> and</c><00:04:56.199><c> other</c>

00:04:56.469 --> 00:04:56.479 align:start position:0%
spectral score spectral energy and other
 

00:04:56.479 --> 00:04:58.110 align:start position:0%
spectral score spectral energy and other
parameters<00:04:57.120><c> which</c><00:04:57.240><c> will</c><00:04:57.520><c> help</c><00:04:57.759><c> us</c><00:04:57.960><c> in</c>

00:04:58.110 --> 00:04:58.120 align:start position:0%
parameters which will help us in
 

00:04:58.120 --> 00:05:00.189 align:start position:0%
parameters which will help us in
building<00:04:58.479><c> the</c><00:04:58.680><c> model</c><00:04:59.080><c> which</c><00:04:59.199><c> will</c><00:04:59.320><c> be</c>

00:05:00.189 --> 00:05:00.199 align:start position:0%
building the model which will be
 

00:05:00.199 --> 00:05:02.310 align:start position:0%
building the model which will be
kind<00:05:00.360><c> of</c><00:05:00.600><c> feature</c><00:05:01.120><c> input</c><00:05:01.479><c> features</c><00:05:01.880><c> in</c><00:05:02.039><c> our</c>

00:05:02.310 --> 00:05:02.320 align:start position:0%
kind of feature input features in our
 

00:05:02.320 --> 00:05:04.510 align:start position:0%
kind of feature input features in our
model<00:05:03.000><c> so</c><00:05:03.160><c> for</c><00:05:03.400><c> different</c><00:05:03.720><c> data</c><00:05:04.000><c> set</c><00:05:04.240><c> we</c><00:05:04.360><c> have</c>

00:05:04.510 --> 00:05:04.520 align:start position:0%
model so for different data set we have
 

00:05:04.520 --> 00:05:07.550 align:start position:0%
model so for different data set we have
tried<00:05:04.919><c> different</c><00:05:05.479><c> models</c><00:05:06.479><c> so</c><00:05:06.720><c> for</c><00:05:07.000><c> ravness</c>

00:05:07.550 --> 00:05:07.560 align:start position:0%
tried different models so for ravness
 

00:05:07.560 --> 00:05:09.629 align:start position:0%
tried different models so for ravness
data<00:05:07.880><c> set</c><00:05:08.320><c> we</c><00:05:08.479><c> have</c><00:05:08.720><c> all</c><00:05:08.919><c> the</c><00:05:09.080><c> emotions</c>

00:05:09.629 --> 00:05:09.639 align:start position:0%
data set we have all the emotions
 

00:05:09.639 --> 00:05:11.830 align:start position:0%
data set we have all the emotions
included<00:05:10.560><c> which</c><00:05:10.759><c> which</c><00:05:11.000><c> we</c><00:05:11.240><c> which</c><00:05:11.360><c> we</c><00:05:11.479><c> saw</c><00:05:11.639><c> in</c>

00:05:11.830 --> 00:05:11.840 align:start position:0%
included which which we which we saw in
 

00:05:11.840 --> 00:05:12.790 align:start position:0%
included which which we which we saw in
previous

00:05:12.790 --> 00:05:12.800 align:start position:0%
previous
 

00:05:12.800 --> 00:05:15.510 align:start position:0%
previous
slides<00:05:13.800><c> these</c><00:05:14.039><c> ones</c><00:05:14.520><c> all</c><00:05:14.759><c> all</c><00:05:15.039><c> all</c><00:05:15.199><c> of</c><00:05:15.320><c> the</c>

00:05:15.510 --> 00:05:15.520 align:start position:0%
slides these ones all all all of the
 

00:05:15.520 --> 00:05:18.390 align:start position:0%
slides these ones all all all of the
neutral<00:05:16.160><c> Cal</c><00:05:17.160><c> all</c><00:05:17.360><c> the</c><00:05:17.520><c> all</c><00:05:17.680><c> the</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
neutral Cal all the all the
 

00:05:18.400 --> 00:05:20.390 align:start position:0%
neutral Cal all the all the
sevention<00:05:19.400><c> and</c><00:05:19.560><c> then</c><00:05:19.720><c> we</c><00:05:19.840><c> have</c><00:05:20.000><c> used</c>

00:05:20.390 --> 00:05:20.400 align:start position:0%
sevention and then we have used
 

00:05:20.400 --> 00:05:22.950 align:start position:0%
sevention and then we have used
different<00:05:20.759><c> models</c><00:05:21.280><c> like</c><00:05:21.479><c> sport</c><00:05:21.960><c> Vector</c>

00:05:22.950 --> 00:05:22.960 align:start position:0%
different models like sport Vector
 

00:05:22.960 --> 00:05:26.430 align:start position:0%
different models like sport Vector
random<00:05:23.319><c> forest</c><00:05:23.759><c> rnm</c><00:05:24.759><c> and</c><00:05:25.319><c> then</c><00:05:25.840><c> again</c><00:05:26.120><c> we</c><00:05:26.240><c> have</c>

00:05:26.430 --> 00:05:26.440 align:start position:0%
random forest rnm and then again we have
 

00:05:26.440 --> 00:05:29.150 align:start position:0%
random forest rnm and then again we have
taken<00:05:26.680><c> a</c><00:05:26.840><c> subset</c><00:05:27.319><c> of</c><00:05:27.560><c> that</c><00:05:27.840><c> dness</c><00:05:28.520><c> model</c><00:05:29.000><c> which</c>

00:05:29.150 --> 00:05:29.160 align:start position:0%
taken a subset of that dness model which
 

00:05:29.160 --> 00:05:32.029 align:start position:0%
taken a subset of that dness model which
only<00:05:29.800><c> specified</c><00:05:30.319><c> to</c><00:05:30.479><c> few</c><00:05:30.759><c> emotions</c><00:05:31.520><c> angry</c>

00:05:32.029 --> 00:05:32.039 align:start position:0%
only specified to few emotions angry
 

00:05:32.039 --> 00:05:35.309 align:start position:0%
only specified to few emotions angry
happy<00:05:32.400><c> neutral</c><00:05:32.880><c> inside</c><00:05:33.800><c> and</c><00:05:34.000><c> using</c><00:05:34.360><c> the</c><00:05:34.560><c> same</c>

00:05:35.309 --> 00:05:35.319 align:start position:0%
happy neutral inside and using the same
 

00:05:35.319 --> 00:05:37.070 align:start position:0%
happy neutral inside and using the same
same<00:05:35.639><c> kind</c><00:05:35.800><c> of</c><00:05:35.960><c> machine</c><00:05:36.280><c> learning</c><00:05:36.720><c> model</c>

00:05:37.070 --> 00:05:37.080 align:start position:0%
same kind of machine learning model
 

00:05:37.080 --> 00:05:39.670 align:start position:0%
same kind of machine learning model
machine<00:05:37.360><c> learning</c><00:05:37.680><c> and</c><00:05:37.840><c> prods</c><00:05:38.280><c> to</c><00:05:38.400><c> build</c>

00:05:39.670 --> 00:05:39.680 align:start position:0%
machine learning and prods to build
 

00:05:39.680 --> 00:05:42.830 align:start position:0%
machine learning and prods to build
them<00:05:40.680><c> so</c><00:05:40.960><c> this</c><00:05:41.160><c> slides</c><00:05:41.720><c> represents</c><00:05:42.360><c> how</c><00:05:42.639><c> the</c>

00:05:42.830 --> 00:05:42.840 align:start position:0%
them so this slides represents how the
 

00:05:42.840 --> 00:05:45.710 align:start position:0%
them so this slides represents how the
different<00:05:43.400><c> categories</c><00:05:44.400><c> uh</c><00:05:44.720><c> of</c><00:05:45.000><c> data</c><00:05:45.319><c> sets</c>

00:05:45.710 --> 00:05:45.720 align:start position:0%
different categories uh of data sets
 

00:05:45.720 --> 00:05:47.550 align:start position:0%
different categories uh of data sets
perform<00:05:46.120><c> across</c><00:05:46.520><c> different</c><00:05:46.880><c> models</c><00:05:47.280><c> so</c><00:05:47.440><c> we</c>

00:05:47.550 --> 00:05:47.560 align:start position:0%
perform across different models so we
 

00:05:47.560 --> 00:05:49.790 align:start position:0%
perform across different models so we
can<00:05:47.759><c> clearly</c><00:05:48.160><c> see</c><00:05:48.840><c> for</c><00:05:49.080><c> the</c><00:05:49.240><c> for</c><00:05:49.400><c> the</c><00:05:49.520><c> whole</c>

00:05:49.790 --> 00:05:49.800 align:start position:0%
can clearly see for the for the whole
 

00:05:49.800 --> 00:05:52.790 align:start position:0%
can clearly see for the for the whole
data<00:05:50.160><c> set</c><00:05:50.919><c> random</c><00:05:51.280><c> Forest</c><00:05:51.680><c> is</c><00:05:51.840><c> the</c><00:05:52.039><c> best</c><00:05:52.240><c> model</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
data set random Forest is the best model
 

00:05:52.800 --> 00:05:56.430 align:start position:0%
data set random Forest is the best model
if<00:05:52.919><c> we</c><00:05:53.080><c> focus</c><00:05:53.520><c> only</c><00:05:54.199><c> the</c><00:05:54.440><c> full</c><00:05:55.440><c> full</c><00:05:55.840><c> full</c><00:05:56.120><c> data</c>

00:05:56.430 --> 00:05:56.440 align:start position:0%
if we focus only the full full full data
 

00:05:56.440 --> 00:05:59.070 align:start position:0%
if we focus only the full full full data
set<00:05:57.080><c> but</c><00:05:57.400><c> when</c><00:05:57.520><c> we</c><00:05:57.720><c> come</c><00:05:58.000><c> down</c><00:05:58.280><c> to</c><00:05:58.440><c> the</c><00:05:58.680><c> Fe</c><00:05:58.919><c> to</c>

00:05:59.070 --> 00:05:59.080 align:start position:0%
set but when we come down to the Fe to
 

00:05:59.080 --> 00:06:01.590 align:start position:0%
set but when we come down to the Fe to
the<00:05:59.520><c> subsets</c><00:06:00.360><c> we</c><00:06:00.520><c> see</c><00:06:00.800><c> for</c><00:06:00.960><c> the</c><00:06:01.120><c> male</c><00:06:01.400><c> and</c>

00:06:01.590 --> 00:06:01.600 align:start position:0%
the subsets we see for the male and
 

00:06:01.600 --> 00:06:04.309 align:start position:0%
the subsets we see for the male and
female<00:06:02.319><c> K</c><00:06:02.600><c> and</c><00:06:02.840><c> perform</c><00:06:03.240><c> the</c><00:06:03.440><c> best</c><00:06:03.960><c> and</c><00:06:04.160><c> the</c>

00:06:04.309 --> 00:06:04.319 align:start position:0%
female K and perform the best and the
 

00:06:04.319 --> 00:06:06.830 align:start position:0%
female K and perform the best and the
for<00:06:04.560><c> the</c><00:06:04.759><c> for</c><00:06:05.000><c> for</c><00:06:05.160><c> the</c><00:06:05.280><c> male</c><00:06:05.560><c> subset</c><00:06:06.440><c> random</c>

00:06:06.830 --> 00:06:06.840 align:start position:0%
for the for for the male subset random
 

00:06:06.840 --> 00:06:08.990 align:start position:0%
for the for for the male subset random
Forest<00:06:07.280><c> perform</c><00:06:07.720><c> the</c><00:06:07.919><c> best</c><00:06:08.280><c> and</c><00:06:08.560><c> again</c><00:06:08.840><c> for</c>

00:06:08.990 --> 00:06:09.000 align:start position:0%
Forest perform the best and again for
 

00:06:09.000 --> 00:06:12.230 align:start position:0%
Forest perform the best and again for
the<00:06:09.160><c> female</c><00:06:09.919><c> Cann</c><00:06:10.400><c> perform</c><00:06:10.759><c> the</c><00:06:11.000><c> best</c><00:06:11.840><c> so</c><00:06:12.120><c> this</c>

00:06:12.230 --> 00:06:12.240 align:start position:0%
the female Cann perform the best so this
 

00:06:12.240 --> 00:06:15.830 align:start position:0%
the female Cann perform the best so this
shows<00:06:12.720><c> that</c><00:06:13.039><c> these</c><00:06:13.720><c> these</c><00:06:14.479><c> these</c><00:06:15.479><c> speech</c>

00:06:15.830 --> 00:06:15.840 align:start position:0%
shows that these these these speech
 

00:06:15.840 --> 00:06:18.430 align:start position:0%
shows that these these these speech
detection<00:06:16.479><c> speech</c><00:06:16.880><c> emotion</c><00:06:17.360><c> varies</c><00:06:17.759><c> from</c>

00:06:18.430 --> 00:06:18.440 align:start position:0%
detection speech emotion varies from
 

00:06:18.440 --> 00:06:19.909 align:start position:0%
detection speech emotion varies from
different<00:06:18.960><c> on</c><00:06:19.120><c> based</c><00:06:19.360><c> on</c><00:06:19.560><c> various</c>

00:06:19.909 --> 00:06:19.919 align:start position:0%
different on based on various
 

00:06:19.919 --> 00:06:22.790 align:start position:0%
different on based on various
characteristics<00:06:20.599><c> and</c><00:06:20.759><c> gender</c><00:06:21.160><c> is</c><00:06:21.319><c> one</c><00:06:21.479><c> of</c>

00:06:22.790 --> 00:06:22.800 align:start position:0%
characteristics and gender is one of
 

00:06:22.800 --> 00:06:25.550 align:start position:0%
characteristics and gender is one of
them<00:06:23.800><c> and</c><00:06:24.000><c> then</c><00:06:24.280><c> when</c><00:06:24.440><c> we</c><00:06:24.759><c> when</c><00:06:24.880><c> we</c><00:06:25.080><c> combine</c>

00:06:25.550 --> 00:06:25.560 align:start position:0%
them and then when we when we combine
 

00:06:25.560 --> 00:06:27.309 align:start position:0%
them and then when we when we combine
this<00:06:25.759><c> data</c><00:06:26.039><c> set</c><00:06:26.280><c> with</c><00:06:26.440><c> different</c><00:06:26.800><c> data</c><00:06:27.039><c> sets</c>

00:06:27.309 --> 00:06:27.319 align:start position:0%
this data set with different data sets
 

00:06:27.319 --> 00:06:30.070 align:start position:0%
this data set with different data sets
Berlin<00:06:27.759><c> and</c><00:06:27.960><c> Savvy</c><00:06:28.440><c> which</c><00:06:28.560><c> is</c><00:06:28.800><c> again</c><00:06:29.759><c> in</c><00:06:29.880><c> a</c>

00:06:30.070 --> 00:06:30.080 align:start position:0%
Berlin and Savvy which is again in a
 

00:06:30.080 --> 00:06:31.909 align:start position:0%
Berlin and Savvy which is again in a
German<00:06:30.520><c> language</c><00:06:30.880><c> and</c><00:06:31.039><c> a</c><00:06:31.199><c> British</c><00:06:31.520><c> language</c>

00:06:31.909 --> 00:06:31.919 align:start position:0%
German language and a British language
 

00:06:31.919 --> 00:06:34.430 align:start position:0%
German language and a British language
stus<00:06:32.759><c> we</c><00:06:32.960><c> use</c><00:06:33.240><c> the</c><00:06:33.400><c> same</c><00:06:33.639><c> machine</c><00:06:34.000><c> learning</c>

00:06:34.430 --> 00:06:34.440 align:start position:0%
stus we use the same machine learning
 

00:06:34.440 --> 00:06:37.230 align:start position:0%
stus we use the same machine learning
learning<00:06:34.840><c> algorithms</c><00:06:35.759><c> and</c><00:06:36.000><c> results</c><00:06:36.599><c> are</c><00:06:36.840><c> as</c>

00:06:37.230 --> 00:06:37.240 align:start position:0%
learning algorithms and results are as
 

00:06:37.240 --> 00:06:39.909 align:start position:0%
learning algorithms and results are as
follows<00:06:38.240><c> for</c><00:06:38.440><c> the</c><00:06:38.599><c> male</c><00:06:39.280><c> for</c><00:06:39.440><c> the</c><00:06:39.560><c> male</c><00:06:39.759><c> and</c>

00:06:39.909 --> 00:06:39.919 align:start position:0%
follows for the male for the male and
 

00:06:39.919 --> 00:06:41.790 align:start position:0%
follows for the male for the male and
female<00:06:40.240><c> data</c><00:06:40.560><c> set</c><00:06:40.720><c> for</c><00:06:40.840><c> the</c><00:06:40.919><c> whole</c><00:06:41.199><c> data</c><00:06:41.479><c> set</c>

00:06:41.790 --> 00:06:41.800 align:start position:0%
female data set for the whole data set
 

00:06:41.800 --> 00:06:44.350 align:start position:0%
female data set for the whole data set
where<00:06:42.000><c> we</c><00:06:42.199><c> used</c><00:06:43.080><c> all</c><00:06:43.280><c> the</c><00:06:43.479><c> emotions</c><00:06:44.080><c> all</c><00:06:44.240><c> the</c>

00:06:44.350 --> 00:06:44.360 align:start position:0%
where we used all the emotions all the
 

00:06:44.360 --> 00:06:46.870 align:start position:0%
where we used all the emotions all the
sem<00:06:44.759><c> emotions</c><00:06:45.199><c> SC</c><00:06:45.520><c> and</c><00:06:45.680><c> perform</c><00:06:46.000><c> the</c><00:06:46.199><c> best</c><00:06:46.720><c> but</c>

00:06:46.870 --> 00:06:46.880 align:start position:0%
sem emotions SC and perform the best but
 

00:06:46.880 --> 00:06:49.629 align:start position:0%
sem emotions SC and perform the best but
when<00:06:47.039><c> we</c><00:06:47.199><c> subset</c><00:06:47.639><c> it</c><00:06:47.759><c> to</c><00:06:48.280><c> particular</c><00:06:48.759><c> top</c><00:06:49.000><c> four</c>

00:06:49.629 --> 00:06:49.639 align:start position:0%
when we subset it to particular top four
 

00:06:49.639 --> 00:06:52.629 align:start position:0%
when we subset it to particular top four
emotions<00:06:50.639><c> we</c><00:06:50.800><c> see</c><00:06:51.199><c> that</c><00:06:51.400><c> the</c><00:06:51.639><c> RNN</c><00:06:52.199><c> model</c>

00:06:52.629 --> 00:06:52.639 align:start position:0%
emotions we see that the RNN model
 

00:06:52.639 --> 00:06:55.629 align:start position:0%
emotions we see that the RNN model
performs<00:06:53.080><c> the</c><00:06:53.440><c> best</c><00:06:54.440><c> so</c><00:06:54.599><c> we</c><00:06:54.720><c> can</c><00:06:54.919><c> clearly</c><00:06:55.319><c> see</c>

00:06:55.629 --> 00:06:55.639 align:start position:0%
performs the best so we can clearly see
 

00:06:55.639 --> 00:06:58.110 align:start position:0%
performs the best so we can clearly see
that<00:06:55.919><c> our</c><00:06:56.520><c> our</c><00:06:56.720><c> model</c><00:06:57.080><c> is</c><00:06:57.240><c> performing</c><00:06:57.720><c> good</c>

00:06:58.110 --> 00:06:58.120 align:start position:0%
that our our model is performing good
 

00:06:58.120 --> 00:06:59.629 align:start position:0%
that our our model is performing good
when<00:06:58.400><c> but</c><00:06:58.879><c> they</c><00:06:59.000><c> have</c><00:06:59.160><c> different</c><00:06:59.400><c> different</c>

00:06:59.629 --> 00:06:59.639 align:start position:0%
when but they have different different
 

00:06:59.639 --> 00:07:01.469 align:start position:0%
when but they have different different
result<00:07:00.000><c> based</c><00:07:00.280><c> on</c><00:07:00.440><c> different</c><00:07:00.720><c> algorithms</c><00:07:01.319><c> and</c>

00:07:01.469 --> 00:07:01.479 align:start position:0%
result based on different algorithms and
 

00:07:01.479 --> 00:07:03.510 align:start position:0%
result based on different algorithms and
kind<00:07:01.639><c> of</c><00:07:01.840><c> differ</c><00:07:02.440><c> different</c><00:07:02.759><c> data</c><00:07:03.000><c> sets</c><00:07:03.240><c> being</c>

00:07:03.510 --> 00:07:03.520 align:start position:0%
kind of differ different data sets being
 

00:07:03.520 --> 00:07:06.189 align:start position:0%
kind of differ different data sets being
used<00:07:04.199><c> the</c><00:07:04.440><c> data</c><00:07:04.720><c> sets</c><00:07:05.039><c> are</c><00:07:05.560><c> are</c><00:07:05.800><c> easily</c>

00:07:06.189 --> 00:07:06.199 align:start position:0%
used the data sets are are easily
 

00:07:06.199 --> 00:07:08.830 align:start position:0%
used the data sets are are easily
available<00:07:06.680><c> on</c><00:07:06.919><c> on</c><00:07:07.199><c> on</c><00:07:07.360><c> our</c><00:07:07.599><c> GitHub</c>

00:07:08.830 --> 00:07:08.840 align:start position:0%
available on on on our GitHub
 

00:07:08.840 --> 00:07:11.430 align:start position:0%
available on on on our GitHub
reposit<00:07:09.840><c> so</c><00:07:10.120><c> our</c><00:07:10.360><c> conclusion</c><00:07:10.840><c> comes</c><00:07:11.080><c> that</c><00:07:11.280><c> the</c>

00:07:11.430 --> 00:07:11.440 align:start position:0%
reposit so our conclusion comes that the
 

00:07:11.440 --> 00:07:13.670 align:start position:0%
reposit so our conclusion comes that the
accuracy<00:07:11.879><c> obtained</c><00:07:12.360><c> from</c><00:07:12.520><c> the</c><00:07:12.720><c> data</c><00:07:13.319><c> female</c>

00:07:13.670 --> 00:07:13.680 align:start position:0%
accuracy obtained from the data female
 

00:07:13.680 --> 00:07:15.710 align:start position:0%
accuracy obtained from the data female
speaker<00:07:14.000><c> is</c><00:07:14.120><c> more</c><00:07:14.360><c> than</c><00:07:14.479><c> the</c><00:07:14.599><c> male</c><00:07:14.840><c> speakers</c>

00:07:15.710 --> 00:07:15.720 align:start position:0%
speaker is more than the male speakers
 

00:07:15.720 --> 00:07:17.550 align:start position:0%
speaker is more than the male speakers
aren<00:07:16.199><c> performed</c><00:07:16.599><c> the</c><00:07:16.759><c> best</c><00:07:17.039><c> when</c><00:07:17.160><c> we</c><00:07:17.319><c> use</c>

00:07:17.550 --> 00:07:17.560 align:start position:0%
aren performed the best when we use
 

00:07:17.560 --> 00:07:19.430 align:start position:0%
aren performed the best when we use
consolidate<00:07:18.120><c> data</c><00:07:18.400><c> sets</c><00:07:18.639><c> considering</c><00:07:19.080><c> fewer</c>

00:07:19.430 --> 00:07:19.440 align:start position:0%
consolidate data sets considering fewer
 

00:07:19.440 --> 00:07:21.629 align:start position:0%
consolidate data sets considering fewer
emotions<00:07:20.240><c> we</c><00:07:20.360><c> noted</c><00:07:20.720><c> that</c><00:07:20.960><c> taking</c><00:07:21.319><c> bigger</c>

00:07:21.629 --> 00:07:21.639 align:start position:0%
emotions we noted that taking bigger
 

00:07:21.639 --> 00:07:25.510 align:start position:0%
emotions we noted that taking bigger
data<00:07:21.919><c> sets</c><00:07:22.400><c> improved</c><00:07:22.840><c> our</c><00:07:23.080><c> result</c><00:07:23.639><c> but</c><00:07:23.759><c> not</c>

00:07:25.510 --> 00:07:25.520 align:start position:0%
data sets improved our result but not
 

00:07:25.520 --> 00:07:28.309 align:start position:0%
data sets improved our result but not
significantly<00:07:26.520><c> and</c><00:07:26.680><c> then</c><00:07:27.240><c> these</c><00:07:27.720><c> these</c><00:07:28.120><c> kind</c>

00:07:28.309 --> 00:07:28.319 align:start position:0%
significantly and then these these kind
 

00:07:28.319 --> 00:07:30.230 align:start position:0%
significantly and then these these kind
of<00:07:28.479><c> research</c><00:07:28.919><c> these</c><00:07:29.080><c> kind</c><00:07:29.479><c> of</c><00:07:29.599><c> modeling</c><00:07:30.039><c> can</c>

00:07:30.230 --> 00:07:30.240 align:start position:0%
of research these kind of modeling can
 

00:07:30.240 --> 00:07:32.110 align:start position:0%
of research these kind of modeling can
have<00:07:30.360><c> a</c><00:07:30.520><c> various</c><00:07:30.879><c> potential</c><00:07:31.360><c> applications</c>

00:07:32.110 --> 00:07:32.120 align:start position:0%
have a various potential applications
 

00:07:32.120 --> 00:07:35.189 align:start position:0%
have a various potential applications
like<00:07:32.599><c> playing</c><00:07:33.000><c> music</c><00:07:33.680><c> changing</c><00:07:34.080><c> room</c><00:07:34.440><c> lights</c>

00:07:35.189 --> 00:07:35.199 align:start position:0%
like playing music changing room lights
 

00:07:35.199 --> 00:07:37.189 align:start position:0%
like playing music changing room lights
we<00:07:35.400><c> come</c><00:07:35.639><c> we</c><00:07:35.800><c> came</c><00:07:36.039><c> across</c><00:07:36.319><c> some</c><00:07:36.479><c> of</c><00:07:36.599><c> the</c><00:07:36.759><c> few</c>

00:07:37.189 --> 00:07:37.199 align:start position:0%
we come we came across some of the few
 

00:07:37.199 --> 00:07:39.430 align:start position:0%
we come we came across some of the few
but<00:07:37.639><c> but</c><00:07:37.840><c> yeah</c><00:07:38.000><c> there</c><00:07:38.120><c> can</c><00:07:38.280><c> be</c><00:07:38.520><c> multi</c><00:07:38.919><c> M</c><00:07:39.240><c> wide</c>

00:07:39.430 --> 00:07:39.440 align:start position:0%
but but yeah there can be multi M wide
 

00:07:39.440 --> 00:07:41.830 align:start position:0%
but but yeah there can be multi M wide
range<00:07:39.639><c> of</c><00:07:39.840><c> potential</c><00:07:40.280><c> future</c><00:07:40.840><c> applications</c>

00:07:41.830 --> 00:07:41.840 align:start position:0%
range of potential future applications
 

00:07:41.840 --> 00:07:43.950 align:start position:0%
range of potential future applications
for<00:07:42.160><c> for</c><00:07:42.400><c> such</c><00:07:42.639><c> kind</c><00:07:42.840><c> of</c>

00:07:43.950 --> 00:07:43.960 align:start position:0%
for for such kind of
 

00:07:43.960 --> 00:07:46.390 align:start position:0%
for for such kind of
experiments<00:07:44.960><c> and</c><00:07:45.080><c> for</c><00:07:45.280><c> your</c><00:07:45.479><c> reference</c><00:07:46.280><c> this</c>

00:07:46.390 --> 00:07:46.400 align:start position:0%
experiments and for your reference this
 

00:07:46.400 --> 00:07:48.710 align:start position:0%
experiments and for your reference this
is<00:07:46.560><c> the</c><00:07:46.759><c> published</c><00:07:47.159><c> paper</c><00:07:47.599><c> on</c><00:07:47.879><c> on</c><00:07:48.120><c> on</c><00:07:48.360><c> the</c><00:07:48.520><c> on</c>

00:07:48.710 --> 00:07:48.720 align:start position:0%
is the published paper on on on the on
 

00:07:48.720 --> 00:07:51.110 align:start position:0%
is the published paper on on on the on
This<00:07:48.919><c> research</c><00:07:49.720><c> you</c><00:07:49.840><c> can</c><00:07:50.199><c> read</c><00:07:50.440><c> it</c><00:07:50.599><c> out</c><00:07:50.800><c> online</c>

00:07:51.110 --> 00:07:51.120 align:start position:0%
This research you can read it out online
 

00:07:51.120 --> 00:07:54.029 align:start position:0%
This research you can read it out online
on<00:07:51.280><c> the</c><00:07:51.400><c> G</c><00:07:51.720><c> link</c><00:07:52.479><c> and</c><00:07:52.720><c> other</c><00:07:52.960><c> team</c><00:07:53.199><c> member</c><00:07:53.800><c> and</c>

00:07:54.029 --> 00:07:54.039 align:start position:0%
on the G link and other team member and
 

00:07:54.039 --> 00:07:56.149 align:start position:0%
on the G link and other team member and
my<00:07:54.199><c> partner</c><00:07:54.520><c> in</c><00:07:54.680><c> crime</c><00:07:54.960><c> pra</c><00:07:55.319><c> Singh</c><00:07:55.599><c> is</c><00:07:55.759><c> also</c><00:07:56.000><c> a</c>

00:07:56.149 --> 00:07:56.159 align:start position:0%
my partner in crime pra Singh is also a
 

00:07:56.159 --> 00:07:58.149 align:start position:0%
my partner in crime pra Singh is also a
part<00:07:56.319><c> of</c><00:07:56.520><c> this</c><00:07:56.720><c> project</c><00:07:57.319><c> we</c><00:07:57.520><c> both</c><00:07:57.759><c> published</c>

00:07:58.149 --> 00:07:58.159 align:start position:0%
part of this project we both published
 

00:07:58.159 --> 00:08:01.189 align:start position:0%
part of this project we both published
this<00:07:58.720><c> this</c><00:07:58.919><c> paper</c><00:07:59.680><c> early</c><00:08:00.120><c> this</c><00:08:00.319><c> year</c><00:08:00.879><c> summer</c>

00:08:01.189 --> 00:08:01.199 align:start position:0%
this this paper early this year summer
 

00:08:01.199 --> 00:08:04.189 align:start position:0%
this this paper early this year summer
of<00:08:01.400><c> this</c><00:08:01.560><c> year</c><00:08:02.000><c> in</c><00:08:02.280><c> and</c><00:08:02.680><c> it</c>

00:08:04.189 --> 00:08:04.199 align:start position:0%
of this year in and it
 

00:08:04.199 --> 00:08:06.790 align:start position:0%
of this year in and it
conference<00:08:05.199><c> and</c><00:08:05.360><c> if</c><00:08:05.479><c> you</c><00:08:05.639><c> have</c><00:08:05.800><c> any</c><00:08:06.039><c> questions</c>

00:08:06.790 --> 00:08:06.800 align:start position:0%
conference and if you have any questions
 

00:08:06.800 --> 00:08:09.309 align:start position:0%
conference and if you have any questions
please<00:08:07.080><c> feel</c><00:08:07.440><c> free</c><00:08:07.720><c> to</c><00:08:07.879><c> reach</c><00:08:08.120><c> out</c><00:08:08.319><c> to</c><00:08:08.479><c> me</c><00:08:09.039><c> over</c>

00:08:09.309 --> 00:08:09.319 align:start position:0%
please feel free to reach out to me over
 

00:08:09.319 --> 00:08:12.070 align:start position:0%
please feel free to reach out to me over
my<00:08:09.560><c> email</c><00:08:10.000><c> at</c><00:08:10.240><c> tan.</c><00:08:11.159><c> banga</c><00:08:11.479><c> I'm</c><00:08:11.680><c> happy</c><00:08:11.919><c> to</c>

00:08:12.070 --> 00:08:12.080 align:start position:0%
my email at tan. banga I'm happy to
 

00:08:12.080 --> 00:08:14.469 align:start position:0%
my email at tan. banga I'm happy to
answer<00:08:12.360><c> any</c><00:08:12.599><c> questions</c><00:08:13.080><c> or</c><00:08:13.240><c> look</c><00:08:13.440><c> for</c><00:08:13.720><c> any</c><00:08:14.240><c> any</c>

00:08:14.469 --> 00:08:14.479 align:start position:0%
answer any questions or look for any any
 

00:08:14.479 --> 00:08:17.189 align:start position:0%
answer any questions or look for any any
kind<00:08:14.639><c> of</c><00:08:14.879><c> recommendations</c><00:08:15.599><c> you</c><00:08:16.000><c> need</c><00:08:17.000><c> thank</c>

00:08:17.189 --> 00:08:17.199 align:start position:0%
kind of recommendations you need thank
 

00:08:17.199 --> 00:08:23.570 align:start position:0%
kind of recommendations you need thank
you<00:08:17.319><c> so</c>

00:08:23.570 --> 00:08:23.580 align:start position:0%
 
 

00:08:23.580 --> 00:08:26.120 align:start position:0%
 
[Music]

00:08:26.120 --> 00:08:26.130 align:start position:0%
[Music]
 

00:08:26.130 --> 00:08:28.230 align:start position:0%
[Music]
[Applause]

00:08:28.230 --> 00:08:28.240 align:start position:0%
[Applause]
 

00:08:28.240 --> 00:08:29.790 align:start position:0%
[Applause]
much

00:08:29.790 --> 00:08:29.800 align:start position:0%
much
 

00:08:29.800 --> 00:08:58.900 align:start position:0%
much
[Music]

00:08:58.900 --> 00:08:58.910 align:start position:0%
 
 

00:08:58.910 --> 00:09:06.350 align:start position:0%
 
[Music]

00:09:06.350 --> 00:09:06.360 align:start position:0%
 
 

00:09:06.360 --> 00:09:49.120 align:start position:0%
 
[Music]

00:09:49.120 --> 00:09:49.130 align:start position:0%
 
 

00:09:49.130 --> 00:09:58.230 align:start position:0%
 
[Music]

00:09:58.230 --> 00:09:58.240 align:start position:0%
 
 

00:09:58.240 --> 00:10:28.030 align:start position:0%
 
[Music]

00:10:28.030 --> 00:10:28.040 align:start position:0%
 
 

00:10:28.040 --> 00:10:30.230 align:start position:0%
 
he

00:10:30.230 --> 00:10:30.240 align:start position:0%
he
 

00:10:30.240 --> 00:10:58.160 align:start position:0%
he
[Music]

00:10:58.160 --> 00:10:58.170 align:start position:0%
 
 

00:10:58.170 --> 00:11:00.700 align:start position:0%
 
[Music]

00:11:00.700 --> 00:11:00.710 align:start position:0%
[Music]
 

00:11:00.710 --> 00:11:00.780 align:start position:0%
[Music]
[Applause]

00:11:00.780 --> 00:11:00.790 align:start position:0%
[Applause]
 

00:11:00.790 --> 00:11:33.480 align:start position:0%
[Applause]
[Music]

00:11:33.480 --> 00:11:33.490 align:start position:0%
 
 

00:11:33.490 --> 00:11:40.940 align:start position:0%
 
[Music]

00:11:40.940 --> 00:11:40.950 align:start position:0%
 
 

00:11:40.950 --> 00:12:23.700 align:start position:0%
 
[Music]

00:12:23.700 --> 00:12:23.710 align:start position:0%
 
 

00:12:23.710 --> 00:12:32.820 align:start position:0%
 
[Music]

00:12:32.820 --> 00:12:32.830 align:start position:0%
 
 

00:12:32.830 --> 00:12:57.829 align:start position:0%
 
[Music]

00:12:57.829 --> 00:12:57.839 align:start position:0%
[Music]
 

00:12:57.839 --> 00:13:01.050 align:start position:0%
[Music]
me

00:13:01.050 --> 00:13:01.060 align:start position:0%
 
 

00:13:01.060 --> 00:13:32.750 align:start position:0%
 
[Music]

00:13:32.750 --> 00:13:32.760 align:start position:0%
 
 

00:13:32.760 --> 00:13:57.749 align:start position:0%
 
[Music]

00:13:57.749 --> 00:13:57.759 align:start position:0%
 
 

00:13:57.759 --> 00:14:01.490 align:start position:0%
 
e

00:14:01.490 --> 00:14:01.500 align:start position:0%
 
 

00:14:01.500 --> 00:14:08.080 align:start position:0%
 
[Music]

00:14:08.080 --> 00:14:08.090 align:start position:0%
 
 

00:14:08.090 --> 00:14:22.080 align:start position:0%
 
[Music]

00:14:22.080 --> 00:14:22.090 align:start position:0%
 
 

00:14:22.090 --> 00:14:58.290 align:start position:0%
 
[Music]

00:14:58.290 --> 00:14:58.300 align:start position:0%
 
 

00:14:58.300 --> 00:15:07.410 align:start position:0%
 
[Music]

00:15:07.410 --> 00:15:07.420 align:start position:0%
 
 

00:15:07.420 --> 00:15:18.390 align:start position:0%
 
[Music]

00:15:18.390 --> 00:15:18.400 align:start position:0%
 
 

00:15:18.400 --> 00:16:07.340 align:start position:0%
 
[Music]

00:16:07.340 --> 00:16:07.350 align:start position:0%
 
 

00:16:07.350 --> 00:16:09.870 align:start position:0%
 
[Music]

00:16:09.870 --> 00:16:09.880 align:start position:0%
[Music]
 

00:16:09.880 --> 00:16:13.510 align:start position:0%
[Music]
[Applause]

00:16:13.510 --> 00:16:13.520 align:start position:0%
 
 

00:16:13.520 --> 00:16:42.670 align:start position:0%
 
[Music]

00:16:42.670 --> 00:16:42.680 align:start position:0%
 
 

00:16:42.680 --> 00:16:50.210 align:start position:0%
 
[Music]

00:16:50.210 --> 00:16:50.220 align:start position:0%
 
 

00:16:50.220 --> 00:17:42.000 align:start position:0%
 
[Music]

00:17:42.000 --> 00:17:42.010 align:start position:0%
 
 

00:17:42.010 --> 00:17:52.950 align:start position:0%
 
[Music]

00:17:52.950 --> 00:17:52.960 align:start position:0%
 
 

00:17:52.960 --> 00:18:41.930 align:start position:0%
 
[Music]

00:18:41.930 --> 00:18:41.940 align:start position:0%
 
 

00:18:41.940 --> 00:19:17.260 align:start position:0%
 
[Music]

00:19:17.260 --> 00:19:17.270 align:start position:0%
 
 

00:19:17.270 --> 00:19:24.800 align:start position:0%
 
[Music]

00:19:24.800 --> 00:19:24.810 align:start position:0%
 
 

00:19:24.810 --> 00:20:16.590 align:start position:0%
 
[Music]

00:20:16.590 --> 00:20:16.600 align:start position:0%
 
 

00:20:16.600 --> 00:20:27.540 align:start position:0%
 
[Music]

00:20:27.540 --> 00:20:27.550 align:start position:0%
 
 

00:20:27.550 --> 00:20:57.110 align:start position:0%
 
[Music]

00:20:57.110 --> 00:20:57.120 align:start position:0%
 
 

00:20:57.120 --> 00:20:59.630 align:start position:0%
 
a

00:20:59.630 --> 00:20:59.640 align:start position:0%
a
 

00:20:59.640 --> 00:21:16.510 align:start position:0%
a
[Music]

00:21:16.510 --> 00:21:16.520 align:start position:0%
 
 

00:21:16.520 --> 00:21:19.050 align:start position:0%
 
[Music]

00:21:19.050 --> 00:21:19.060 align:start position:0%
[Music]
 

00:21:19.060 --> 00:21:19.140 align:start position:0%
[Music]
[Applause]

00:21:19.140 --> 00:21:19.150 align:start position:0%
[Applause]
 

00:21:19.150 --> 00:21:51.850 align:start position:0%
[Applause]
[Music]

00:21:51.850 --> 00:21:51.860 align:start position:0%
 
 

00:21:51.860 --> 00:21:59.290 align:start position:0%
 
[Music]

00:21:59.290 --> 00:21:59.300 align:start position:0%
 
 

00:21:59.300 --> 00:22:26.950 align:start position:0%
 
[Music]

00:22:26.950 --> 00:22:26.960 align:start position:0%
 
 

00:22:26.960 --> 00:22:29.020 align:start position:0%
 
yeah

00:22:29.020 --> 00:22:29.030 align:start position:0%
yeah
 

00:22:29.030 --> 00:22:42.050 align:start position:0%
yeah
[Music]

00:22:42.050 --> 00:22:42.060 align:start position:0%
 
 

00:22:42.060 --> 00:22:51.170 align:start position:0%
 
[Music]

00:22:51.170 --> 00:22:51.180 align:start position:0%
 
 

00:22:51.180 --> 00:23:51.100 align:start position:0%
 
[Music]

00:23:51.100 --> 00:23:51.110 align:start position:0%
 
 

00:23:51.110 --> 00:24:26.420 align:start position:0%
 
[Music]

00:24:26.420 --> 00:24:26.430 align:start position:0%
 
 

00:24:26.430 --> 00:24:36.750 align:start position:0%
 
[Music]

00:24:36.750 --> 00:24:36.760 align:start position:0%
 
 

00:24:36.760 --> 00:25:16.640 align:start position:0%
 
[Music]

00:25:16.640 --> 00:25:16.650 align:start position:0%
 
 

00:25:16.650 --> 00:25:25.760 align:start position:0%
 
[Music]

00:25:25.760 --> 00:25:25.770 align:start position:0%
 
 

00:25:25.770 --> 00:25:36.740 align:start position:0%
 
[Music]

00:25:36.740 --> 00:25:36.750 align:start position:0%
 
 

00:25:36.750 --> 00:26:25.690 align:start position:0%
 
[Music]

00:26:25.690 --> 00:26:25.700 align:start position:0%
 
 

00:26:25.700 --> 00:27:01.020 align:start position:0%
 
[Music]

00:27:01.020 --> 00:27:01.030 align:start position:0%
 
 

00:27:01.030 --> 00:27:08.560 align:start position:0%
 
[Music]

00:27:08.560 --> 00:27:08.570 align:start position:0%
 
 

00:27:08.570 --> 00:27:51.230 align:start position:0%
 
[Music]

00:27:51.230 --> 00:27:51.240 align:start position:0%
 
 

00:27:51.240 --> 00:28:00.350 align:start position:0%
 
[Music]

00:28:00.350 --> 00:28:00.360 align:start position:0%
 
 

00:28:00.360 --> 00:28:26.430 align:start position:0%
 
[Music]

00:28:26.430 --> 00:28:26.440 align:start position:0%
 
 

00:28:26.440 --> 00:28:28.570 align:start position:0%
 
he

00:28:28.570 --> 00:28:28.580 align:start position:0%
he
 

00:28:28.580 --> 00:28:57.769 align:start position:0%
he
[Music]

