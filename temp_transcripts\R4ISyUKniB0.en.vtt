WEBVTT
Kind: captions
Language: en

00:00:00.280 --> 00:00:05.030 align:start position:0%
 
this<00:00:00.840><c> is</c><00:00:01.040><c> auto</c><00:00:01.439><c> Gro</c><00:00:02.320><c> beta</c><00:00:03.000><c> version</c><00:00:04.000><c> um</c><00:00:04.799><c> I</c><00:00:04.880><c> don't</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
this is auto Gro beta version um I don't
 

00:00:05.040 --> 00:00:09.180 align:start position:0%
this is auto Gro beta version um I don't
know<00:00:05.839><c> 11</c><00:00:06.839><c> sure</c><00:00:07.279><c> what</c><00:00:07.399><c> the</c><00:00:07.560><c> hell</c><00:00:08.120><c> let's</c><00:00:08.280><c> say</c>

00:00:09.180 --> 00:00:09.190 align:start position:0%
know 11 sure what the hell let's say
 

00:00:09.190 --> 00:00:18.269 align:start position:0%
know 11 sure what the hell let's say
[Music]

00:00:18.269 --> 00:00:18.279 align:start position:0%
[Music]
 

00:00:18.279 --> 00:00:21.550 align:start position:0%
[Music]
11<00:00:19.279><c> so</c><00:00:20.160><c> a</c><00:00:20.279><c> few</c><00:00:20.480><c> of</c><00:00:20.600><c> you</c><00:00:20.720><c> weren't</c><00:00:21.080><c> happy</c><00:00:21.400><c> with</c>

00:00:21.550 --> 00:00:21.560 align:start position:0%
11 so a few of you weren't happy with
 

00:00:21.560 --> 00:00:23.470 align:start position:0%
11 so a few of you weren't happy with
Simply<00:00:21.880><c> Having</c><00:00:22.160><c> anthropics</c><00:00:22.760><c> Claude</c><00:00:23.119><c> models</c>

00:00:23.470 --> 00:00:23.480 align:start position:0%
Simply Having anthropics Claude models
 

00:00:23.480 --> 00:00:25.589 align:start position:0%
Simply Having anthropics Claude models
show<00:00:23.680><c> up</c><00:00:23.840><c> in</c><00:00:24.000><c> the</c><00:00:24.119><c> provider</c><00:00:24.439><c> and</c><00:00:24.599><c> model</c>

00:00:25.589 --> 00:00:25.599 align:start position:0%
show up in the provider and model
 

00:00:25.599 --> 00:00:27.750 align:start position:0%
show up in the provider and model
dropdowns<00:00:26.599><c> you</c><00:00:26.720><c> wanted</c><00:00:27.000><c> them</c><00:00:27.160><c> to</c><00:00:27.400><c> actually</c>

00:00:27.750 --> 00:00:27.760 align:start position:0%
dropdowns you wanted them to actually
 

00:00:27.760 --> 00:00:30.630 align:start position:0%
dropdowns you wanted them to actually
work<00:00:28.640><c> when</c><00:00:28.800><c> you</c><00:00:28.960><c> selected</c><00:00:29.439><c> them</c><00:00:30.039><c> people</c><00:00:30.480><c> can</c>

00:00:30.630 --> 00:00:30.640 align:start position:0%
work when you selected them people can
 

00:00:30.640 --> 00:00:33.630 align:start position:0%
work when you selected them people can
be<00:00:30.759><c> so</c><00:00:31.080><c> demanding</c><00:00:31.840><c> preach</c><00:00:32.200><c> brother</c><00:00:32.559><c> man</c><00:00:33.520><c> what</c>

00:00:33.630 --> 00:00:33.640 align:start position:0%
be so demanding preach brother man what
 

00:00:33.640 --> 00:00:36.510 align:start position:0%
be so demanding preach brother man what
the<00:00:33.760><c> hell</c><00:00:33.960><c> does</c><00:00:34.160><c> that</c><00:00:34.320><c> mean</c><00:00:35.079><c> I'm</c><00:00:35.200><c> not</c><00:00:35.440><c> sure</c><00:00:36.440><c> I</c>

00:00:36.510 --> 00:00:36.520 align:start position:0%
the hell does that mean I'm not sure I
 

00:00:36.520 --> 00:00:39.350 align:start position:0%
the hell does that mean I'm not sure I
learned<00:00:36.800><c> it</c><00:00:36.920><c> from</c><00:00:37.079><c> a</c><00:00:37.200><c> Spike</c><00:00:37.480><c> Lee</c><00:00:37.680><c> movie</c><00:00:38.480><c> anyway</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
learned it from a Spike Lee movie anyway
 

00:00:39.360 --> 00:00:41.150 align:start position:0%
learned it from a Spike Lee movie anyway
we've<00:00:39.559><c> got</c><00:00:39.760><c> Claud</c><00:00:40.160><c> acity</c><00:00:40.480><c> working</c><00:00:40.879><c> but</c><00:00:41.000><c> I'll</c>

00:00:41.150 --> 00:00:41.160 align:start position:0%
we've got Claud acity working but I'll
 

00:00:41.160 --> 00:00:44.069 align:start position:0%
we've got Claud acity working but I'll
warn<00:00:41.399><c> you</c><00:00:41.600><c> it's</c><00:00:41.760><c> pretty</c><00:00:42.000><c> damn</c><00:00:42.680><c> slow</c><00:00:43.680><c> I'll</c><00:00:43.879><c> keep</c>

00:00:44.069 --> 00:00:44.079 align:start position:0%
warn you it's pretty damn slow I'll keep
 

00:00:44.079 --> 00:00:45.950 align:start position:0%
warn you it's pretty damn slow I'll keep
the<00:00:44.239><c> video</c><00:00:44.559><c> in</c><00:00:44.719><c> real</c><00:00:45.000><c> time</c><00:00:45.280><c> here</c><00:00:45.480><c> so</c><00:00:45.640><c> you</c><00:00:45.760><c> can</c>

00:00:45.950 --> 00:00:45.960 align:start position:0%
the video in real time here so you can
 

00:00:45.960 --> 00:00:49.310 align:start position:0%
the video in real time here so you can
see<00:00:46.160><c> for</c>

00:00:49.310 --> 00:00:49.320 align:start position:0%
 
 

00:00:49.320 --> 00:00:51.389 align:start position:0%
 
yourself<00:00:50.320><c> what</c><00:00:50.399><c> do</c><00:00:50.520><c> we</c><00:00:50.640><c> do</c><00:00:50.840><c> while</c><00:00:50.960><c> we're</c>

00:00:51.389 --> 00:00:51.399 align:start position:0%
yourself what do we do while we're
 

00:00:51.399 --> 00:00:55.430 align:start position:0%
yourself what do we do while we're
waiting<00:00:52.399><c> we</c><00:00:52.640><c> just</c><00:00:53.280><c> you</c><00:00:53.440><c> know</c>

00:00:55.430 --> 00:00:55.440 align:start position:0%
waiting we just you know
 

00:00:55.440 --> 00:00:58.709 align:start position:0%
waiting we just you know
wait<00:00:56.440><c> I</c><00:00:56.559><c> could</c><00:00:56.680><c> sing</c><00:00:56.920><c> the</c><00:00:57.039><c> autog</c><00:00:57.359><c> gro</c><00:00:57.680><c> song</c><00:00:58.399><c> no</c>

00:00:58.709 --> 00:00:58.719 align:start position:0%
wait I could sing the autog gro song no
 

00:00:58.719 --> 00:01:01.270 align:start position:0%
wait I could sing the autog gro song no
nobody<00:00:59.079><c> wants</c><00:00:59.320><c> to</c><00:00:59.480><c> hear</c>

00:01:01.270 --> 00:01:01.280 align:start position:0%
nobody wants to hear
 

00:01:01.280 --> 00:01:05.190 align:start position:0%
nobody wants to hear
gr<00:01:02.280><c> stop</c>

00:01:05.190 --> 00:01:05.200 align:start position:0%
 
 

00:01:05.200 --> 00:01:07.270 align:start position:0%
 
it<00:01:06.200><c> people</c><00:01:06.400><c> have</c><00:01:06.560><c> asked</c><00:01:06.760><c> me</c><00:01:06.920><c> about</c><00:01:07.119><c> this</c>

00:01:07.270 --> 00:01:07.280 align:start position:0%
it people have asked me about this
 

00:01:07.280 --> 00:01:09.230 align:start position:0%
it people have asked me about this
warning<00:01:07.640><c> message</c><00:01:08.040><c> the</c><00:01:08.119><c> short</c><00:01:08.360><c> story</c><00:01:08.759><c> is</c>

00:01:09.230 --> 00:01:09.240 align:start position:0%
warning message the short story is
 

00:01:09.240 --> 00:01:11.630 align:start position:0%
warning message the short story is
getting<00:01:09.479><c> rid</c><00:01:09.640><c> of</c><00:01:09.759><c> it</c><00:01:09.880><c> will</c><00:01:10.040><c> break</c><00:01:10.439><c> things</c><00:01:11.439><c> for</c>

00:01:11.630 --> 00:01:11.640 align:start position:0%
getting rid of it will break things for
 

00:01:11.640 --> 00:01:13.030 align:start position:0%
getting rid of it will break things for
now<00:01:11.840><c> we'll</c><00:01:12.040><c> put</c><00:01:12.200><c> up</c><00:01:12.400><c> with</c><00:01:12.520><c> the</c><00:01:12.640><c> annoying</c>

00:01:13.030 --> 00:01:13.040 align:start position:0%
now we'll put up with the annoying
 

00:01:13.040 --> 00:01:14.670 align:start position:0%
now we'll put up with the annoying
message<00:01:13.759><c> I've</c><00:01:13.920><c> gotten</c><00:01:14.159><c> pretty</c><00:01:14.400><c> good</c><00:01:14.520><c> at</c>

00:01:14.670 --> 00:01:14.680 align:start position:0%
message I've gotten pretty good at
 

00:01:14.680 --> 00:01:16.870 align:start position:0%
message I've gotten pretty good at
putting<00:01:14.920><c> up</c><00:01:15.080><c> with</c><00:01:15.240><c> Annoying</c><00:01:15.840><c> hey</c><00:01:16.080><c> there</c><00:01:16.200><c> we</c><00:01:16.360><c> go</c>

00:01:16.870 --> 00:01:16.880 align:start position:0%
putting up with Annoying hey there we go
 

00:01:16.880 --> 00:01:19.109 align:start position:0%
putting up with Annoying hey there we go
Claude<00:01:17.280><c> has</c><00:01:17.479><c> finally</c><00:01:17.920><c> responded</c><00:01:18.840><c> and</c><00:01:19.000><c> In</c>

00:01:19.109 --> 00:01:19.119 align:start position:0%
Claude has finally responded and In
 

00:01:19.119 --> 00:01:21.030 align:start position:0%
Claude has finally responded and In
fairness<00:01:19.520><c> it</c><00:01:19.640><c> does</c><00:01:19.799><c> a</c><00:01:19.920><c> hell</c><00:01:20.040><c> of</c><00:01:20.159><c> a</c><00:01:20.320><c> job</c><00:01:20.840><c> those</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
fairness it does a hell of a job those
 

00:01:21.040 --> 00:01:23.390 align:start position:0%
fairness it does a hell of a job those
10<00:01:21.280><c> agents</c><00:01:21.600><c> are</c><00:01:21.840><c> very</c><00:01:22.040><c> well</c><00:01:22.320><c> crafted</c><00:01:23.079><c> keep</c><00:01:23.240><c> in</c>

00:01:23.390 --> 00:01:23.400 align:start position:0%
10 agents are very well crafted keep in
 

00:01:23.400 --> 00:01:26.270 align:start position:0%
10 agents are very well crafted keep in
mind<00:01:24.159><c> autog</c><00:01:24.520><c> Gro</c><00:01:24.840><c> made</c><00:01:25.079><c> like</c><00:01:25.240><c> a</c><00:01:25.400><c> dozen</c><00:01:25.759><c> API</c>

00:01:26.270 --> 00:01:26.280 align:start position:0%
mind autog Gro made like a dozen API
 

00:01:26.280 --> 00:01:28.910 align:start position:0%
mind autog Gro made like a dozen API
calls<00:01:26.759><c> while</c><00:01:26.920><c> we</c><00:01:27.040><c> were</c><00:01:27.680><c> waiting</c><00:01:28.680><c> you'll</c>

00:01:28.910 --> 00:01:28.920 align:start position:0%
calls while we were waiting you'll
 

00:01:28.920 --> 00:01:30.830 align:start position:0%
calls while we were waiting you'll
notice<00:01:29.240><c> that</c><00:01:29.360><c> the</c><00:01:29.439><c> auto</c><00:01:29.920><c> moderation</c><00:01:30.439><c> session</c>

00:01:30.830 --> 00:01:30.840 align:start position:0%
notice that the auto moderation session
 

00:01:30.840 --> 00:01:32.830 align:start position:0%
notice that the auto moderation session
I<00:01:30.960><c> just</c><00:01:31.200><c> kicked</c><00:01:31.520><c> off</c><00:01:31.759><c> will</c><00:01:31.960><c> go</c><00:01:32.200><c> substantially</c>

00:01:32.830 --> 00:01:32.840 align:start position:0%
I just kicked off will go substantially
 

00:01:32.840 --> 00:01:34.230 align:start position:0%
I just kicked off will go substantially
quicker<00:01:33.200><c> since</c><00:01:33.439><c> there</c><00:01:33.560><c> aren't</c><00:01:33.799><c> nearly</c><00:01:34.119><c> as</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
quicker since there aren't nearly as
 

00:01:34.240 --> 00:01:36.310 align:start position:0%
quicker since there aren't nearly as
many<00:01:34.479><c> round</c><00:01:34.799><c> trips</c><00:01:35.079><c> to</c><00:01:35.200><c> the</c><00:01:35.320><c> server</c><00:01:35.640><c> and</c><00:01:35.880><c> back</c>

00:01:36.310 --> 00:01:36.320 align:start position:0%
many round trips to the server and back
 

00:01:36.320 --> 00:01:37.950 align:start position:0%
many round trips to the server and back
one<00:01:36.439><c> of</c><00:01:36.520><c> the</c><00:01:36.640><c> recent</c><00:01:36.960><c> changes</c><00:01:37.360><c> we</c><00:01:37.479><c> made</c><00:01:37.720><c> was</c>

00:01:37.950 --> 00:01:37.960 align:start position:0%
one of the recent changes we made was
 

00:01:37.960 --> 00:01:39.870 align:start position:0%
one of the recent changes we made was
giving<00:01:38.200><c> each</c><00:01:38.439><c> agent</c><00:01:38.759><c> the</c><00:01:38.920><c> ability</c><00:01:39.280><c> to</c><00:01:39.439><c> use</c><00:01:39.680><c> a</c>

00:01:39.870 --> 00:01:39.880 align:start position:0%
giving each agent the ability to use a
 

00:01:39.880 --> 00:01:42.350 align:start position:0%
giving each agent the ability to use a
different<00:01:40.240><c> provider</c><00:01:40.640><c> and</c><00:01:41.159><c> model</c><00:01:42.159><c> I'll</c>

00:01:42.350 --> 00:01:42.360 align:start position:0%
different provider and model I'll
 

00:01:42.360 --> 00:01:44.389 align:start position:0%
different provider and model I'll
configure<00:01:42.799><c> our</c><00:01:43.079><c> solution</c><00:01:43.520><c> architect</c><00:01:44.000><c> to</c><00:01:44.119><c> use</c>

00:01:44.389 --> 00:01:44.399 align:start position:0%
configure our solution architect to use
 

00:01:44.399 --> 00:01:46.109 align:start position:0%
configure our solution architect to use
grock<00:01:44.799><c> so</c><00:01:45.000><c> we</c><00:01:45.079><c> can</c><00:01:45.240><c> compare</c><00:01:45.560><c> it</c><00:01:45.640><c> to</c><00:01:45.840><c> another</c>

00:01:46.109 --> 00:01:46.119 align:start position:0%
grock so we can compare it to another
 

00:01:46.119 --> 00:01:48.310 align:start position:0%
grock so we can compare it to another
agent<00:01:46.399><c> running</c><00:01:46.759><c> Claude</c><00:01:47.520><c> for</c><00:01:47.759><c> this</c><00:01:47.960><c> part</c><00:01:48.200><c> I</c>

00:01:48.310 --> 00:01:48.320 align:start position:0%
agent running Claude for this part I
 

00:01:48.320 --> 00:01:50.270 align:start position:0%
agent running Claude for this part I
will<00:01:48.600><c> actually</c><00:01:48.920><c> speed</c><00:01:49.240><c> up</c><00:01:49.439><c> the</c><00:01:49.600><c> video</c><00:01:49.960><c> or</c><00:01:50.159><c> you</c>

00:01:50.270 --> 00:01:50.280 align:start position:0%
will actually speed up the video or you
 

00:01:50.280 --> 00:01:54.389 align:start position:0%
will actually speed up the video or you
know<00:01:50.520><c> who</c><00:01:50.759><c> might</c><00:01:50.960><c> start</c><00:01:51.240><c> singing</c><00:01:52.280><c> a</c><00:01:53.280><c> a</c><00:01:53.680><c> gr</c><00:01:54.200><c> oh</c>

00:01:54.389 --> 00:01:54.399 align:start position:0%
know who might start singing a a gr oh
 

00:01:54.399 --> 00:01:56.230 align:start position:0%
know who might start singing a a gr oh
good

00:01:56.230 --> 00:01:56.240 align:start position:0%
good
 

00:01:56.240 --> 00:01:59.310 align:start position:0%
good
goduto<00:01:57.240><c> all</c><00:01:57.360><c> right</c><00:01:57.840><c> already</c><00:01:58.840><c> here</c><00:01:59.000><c> I'll</c><00:01:59.159><c> be</c>

00:01:59.310 --> 00:01:59.320 align:start position:0%
goduto all right already here I'll be
 

00:01:59.320 --> 00:02:01.029 align:start position:0%
goduto all right already here I'll be
clicking<00:01:59.560><c> on</c><00:02:00.039><c> solution</c><00:02:00.399><c> architect</c><00:02:00.920><c> and</c>

00:02:01.029 --> 00:02:01.039 align:start position:0%
clicking on solution architect and
 

00:02:01.039 --> 00:02:02.590 align:start position:0%
clicking on solution architect and
you'll<00:02:01.200><c> see</c><00:02:01.399><c> it</c><00:02:01.560><c> takes</c><00:02:01.840><c> that</c><00:02:02.000><c> agent</c><00:02:02.280><c> running</c>

00:02:02.590 --> 00:02:02.600 align:start position:0%
you'll see it takes that agent running
 

00:02:02.600 --> 00:02:05.510 align:start position:0%
you'll see it takes that agent running
grock<00:02:03.000><c> about</c><00:02:03.280><c> 6</c><00:02:03.520><c> seconds</c><00:02:03.880><c> to</c><00:02:04.360><c> respond</c><00:02:05.360><c> and</c>

00:02:05.510 --> 00:02:05.520 align:start position:0%
grock about 6 seconds to respond and
 

00:02:05.520 --> 00:02:06.950 align:start position:0%
grock about 6 seconds to respond and
some<00:02:05.640><c> of</c><00:02:05.759><c> that</c><00:02:05.920><c> delay</c><00:02:06.240><c> was</c><00:02:06.399><c> due</c><00:02:06.600><c> to</c><00:02:06.759><c> other</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
some of that delay was due to other
 

00:02:06.960 --> 00:02:08.710 align:start position:0%
some of that delay was due to other
requests<00:02:07.399><c> in</c><00:02:07.520><c> the</c><00:02:07.640><c> background</c><00:02:08.160><c> automatically</c>

00:02:08.710 --> 00:02:08.720 align:start position:0%
requests in the background automatically
 

00:02:08.720 --> 00:02:11.150 align:start position:0%
requests in the background automatically
being<00:02:08.959><c> sent</c><00:02:09.200><c> to</c><00:02:09.360><c> Claude</c><00:02:10.160><c> since</c><00:02:10.440><c> anthropic</c><00:02:11.039><c> is</c>

00:02:11.150 --> 00:02:11.160 align:start position:0%
being sent to Claude since anthropic is
 

00:02:11.160 --> 00:02:14.270 align:start position:0%
being sent to Claude since anthropic is
our<00:02:11.360><c> default</c><00:02:12.040><c> Provider</c><00:02:13.040><c> by</c><00:02:13.280><c> comparison</c>

00:02:14.270 --> 00:02:14.280 align:start position:0%
our default Provider by comparison
 

00:02:14.280 --> 00:02:16.070 align:start position:0%
our default Provider by comparison
clicking<00:02:14.640><c> our</c><00:02:14.879><c> business</c><00:02:15.239><c> analyst</c><00:02:15.640><c> will</c><00:02:15.879><c> take</c>

00:02:16.070 --> 00:02:16.080 align:start position:0%
clicking our business analyst will take
 

00:02:16.080 --> 00:02:17.949 align:start position:0%
clicking our business analyst will take
three<00:02:16.360><c> times</c><00:02:16.680><c> that</c><00:02:16.879><c> long</c><00:02:17.200><c> since</c><00:02:17.480><c> Claude</c><00:02:17.840><c> is</c>

00:02:17.949 --> 00:02:17.959 align:start position:0%
three times that long since Claude is
 

00:02:17.959 --> 00:02:20.070 align:start position:0%
three times that long since Claude is
the<00:02:18.080><c> only</c><00:02:18.360><c> AI</c><00:02:18.720><c> servicing</c><00:02:19.280><c> those</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
the only AI servicing those
 

00:02:20.080 --> 00:02:22.710 align:start position:0%
the only AI servicing those
requests<00:02:21.080><c> but</c><00:02:21.319><c> again</c><00:02:21.920><c> you</c><00:02:22.040><c> could</c><00:02:22.239><c> argue</c><00:02:22.599><c> that</c>

00:02:22.710 --> 00:02:22.720 align:start position:0%
requests but again you could argue that
 

00:02:22.720 --> 00:02:24.390 align:start position:0%
requests but again you could argue that
the<00:02:22.879><c> responses</c><00:02:23.360><c> are</c><00:02:23.560><c> much</c><00:02:23.800><c> better</c><00:02:24.120><c> than</c><00:02:24.280><c> what</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
the responses are much better than what
 

00:02:24.400 --> 00:02:27.910 align:start position:0%
the responses are much better than what
we<00:02:24.560><c> get</c><00:02:24.760><c> back</c><00:02:24.959><c> from</c><00:02:25.120><c> llama</c><00:02:25.440><c> 3</c><00:02:25.760><c> via</c><00:02:26.200><c> grock</c><00:02:27.200><c> so</c>

00:02:27.910 --> 00:02:27.920 align:start position:0%
we get back from llama 3 via grock so
 

00:02:27.920 --> 00:02:30.070 align:start position:0%
we get back from llama 3 via grock so
that's<00:02:28.120><c> the</c><00:02:28.480><c> tradeoff</c><00:02:29.480><c> if</c><00:02:29.560><c> you</c><00:02:29.680><c> don't</c><00:02:29.920><c> want</c><00:02:30.000><c> to</c>

00:02:30.070 --> 00:02:30.080 align:start position:0%
that's the tradeoff if you don't want to
 

00:02:30.080 --> 00:02:31.309 align:start position:0%
that's the tradeoff if you don't want to
spend<00:02:30.280><c> your</c><00:02:30.400><c> whole</c><00:02:30.560><c> weekend</c><00:02:30.879><c> waiting</c><00:02:31.120><c> for</c>

00:02:31.309 --> 00:02:31.319 align:start position:0%
spend your whole weekend waiting for
 

00:02:31.319 --> 00:02:33.869 align:start position:0%
spend your whole weekend waiting for
Claude<00:02:31.680><c> use</c><00:02:31.959><c> grock</c><00:02:32.879><c> so</c><00:02:33.040><c> you</c><00:02:33.200><c> spent</c><00:02:33.440><c> the</c><00:02:33.560><c> entire</c>

00:02:33.869 --> 00:02:33.879 align:start position:0%
Claude use grock so you spent the entire
 

00:02:33.879 --> 00:02:35.509 align:start position:0%
Claude use grock so you spent the entire
weekend<00:02:34.160><c> writing</c><00:02:34.560><c> software</c><00:02:35.040><c> so</c><00:02:35.160><c> that</c><00:02:35.319><c> people</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
weekend writing software so that people
 

00:02:35.519 --> 00:02:36.589 align:start position:0%
weekend writing software so that people
wouldn't<00:02:35.800><c> have</c><00:02:35.879><c> to</c><00:02:36.000><c> spend</c><00:02:36.239><c> their</c><00:02:36.400><c> whole</c>

00:02:36.589 --> 00:02:36.599 align:start position:0%
wouldn't have to spend their whole
 

00:02:36.599 --> 00:02:38.869 align:start position:0%
wouldn't have to spend their whole
weekend<00:02:36.920><c> writing</c><00:02:37.360><c> software</c><00:02:38.319><c> well</c><00:02:38.599><c> when</c><00:02:38.720><c> you</c>

00:02:38.869 --> 00:02:38.879 align:start position:0%
weekend writing software well when you
 

00:02:38.879 --> 00:02:40.710 align:start position:0%
weekend writing software well when you
say<00:02:39.080><c> it</c><00:02:39.280><c> that</c><00:02:39.480><c> way</c><00:02:39.720><c> I</c><00:02:39.840><c> sound</c><00:02:40.200><c> kind</c><00:02:40.360><c> of</c>

00:02:40.710 --> 00:02:40.720 align:start position:0%
say it that way I sound kind of
 

00:02:40.720 --> 00:02:42.750 align:start position:0%
say it that way I sound kind of
altruistic<00:02:41.720><c> when</c><00:02:41.840><c> I</c><00:02:41.959><c> say</c><00:02:42.120><c> it</c><00:02:42.280><c> that</c><00:02:42.440><c> way</c><00:02:42.640><c> you</c>

00:02:42.750 --> 00:02:42.760 align:start position:0%
altruistic when I say it that way you
 

00:02:42.760 --> 00:02:45.070 align:start position:0%
altruistic when I say it that way you
sound<00:02:43.040><c> kind</c><00:02:43.159><c> of</c><00:02:43.519><c> moronic</c><00:02:44.519><c> just</c><00:02:44.720><c> play</c><00:02:44.959><c> the</c>

00:02:45.070 --> 00:02:45.080 align:start position:0%
sound kind of moronic just play the
 

00:02:45.080 --> 00:02:47.470 align:start position:0%
sound kind of moronic just play the
theme<00:02:45.360><c> song</c><00:02:45.640><c> and</c><00:02:45.879><c> get</c><00:02:46.040><c> out</c><00:02:46.159><c> of</c>

00:02:47.470 --> 00:02:47.480 align:start position:0%
theme song and get out of
 

00:02:47.480 --> 00:02:49.030 align:start position:0%
theme song and get out of
here

00:02:49.030 --> 00:02:49.040 align:start position:0%
here
 

00:02:49.040 --> 00:02:52.550 align:start position:0%
here
aut<00:02:50.040><c> aut</c><00:02:51.040><c> and</c><00:02:51.280><c> no</c>

00:02:52.550 --> 00:02:52.560 align:start position:0%
aut aut and no
 

00:02:52.560 --> 00:02:54.470 align:start position:0%
aut aut and no
singing

00:02:54.470 --> 00:02:54.480 align:start position:0%
singing
 

00:02:54.480 --> 00:03:01.509 align:start position:0%
singing
Auto<00:02:55.879><c> autut</c><00:02:56.879><c> aut</c><00:02:57.959><c> aut</c><00:02:58.959><c> Auto</c>

00:03:01.509 --> 00:03:01.519 align:start position:0%
Auto autut aut aut Auto
 

00:03:01.519 --> 00:03:05.519 align:start position:0%
Auto autut aut aut Auto
aut<00:03:02.519><c> a</c>

