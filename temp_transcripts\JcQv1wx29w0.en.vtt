WEBVTT
Kind: captions
Language: en

00:00:05.299 --> 00:00:07.730 align:start position:0%
 
imagine<00:00:06.299><c> asking</c><00:00:06.899><c> a</c><00:00:07.020><c> medically</c><00:00:07.319><c> trained</c>

00:00:07.730 --> 00:00:07.740 align:start position:0%
imagine asking a medically trained
 

00:00:07.740 --> 00:00:09.710 align:start position:0%
imagine asking a medically trained
Healthcare<00:00:08.160><c> specific</c><00:00:08.700><c> large</c><00:00:09.059><c> language</c><00:00:09.360><c> model</c>

00:00:09.710 --> 00:00:09.720 align:start position:0%
Healthcare specific large language model
 

00:00:09.720 --> 00:00:11.870 align:start position:0%
Healthcare specific large language model
any<00:00:10.139><c> question</c><00:00:10.440><c> and</c><00:00:11.040><c> getting</c><00:00:11.280><c> specialty</c>

00:00:11.870 --> 00:00:11.880 align:start position:0%
any question and getting specialty
 

00:00:11.880 --> 00:00:13.730 align:start position:0%
any question and getting specialty
answers<00:00:12.240><c> extracted</c><00:00:12.960><c> from</c><00:00:13.200><c> millions</c><00:00:13.559><c> of</c>

00:00:13.730 --> 00:00:13.740 align:start position:0%
answers extracted from millions of
 

00:00:13.740 --> 00:00:17.570 align:start position:0%
answers extracted from millions of
medical<00:00:13.980><c> records</c><00:00:14.519><c> instantly</c>

00:00:17.570 --> 00:00:17.580 align:start position:0%
 
 

00:00:17.580 --> 00:00:20.150 align:start position:0%
 
centered<00:00:18.420><c> around</c><00:00:18.600><c> any</c><00:00:18.960><c> given</c><00:00:19.320><c> context</c><00:00:19.680><c> a</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
centered around any given context a
 

00:00:20.160 --> 00:00:22.010 align:start position:0%
centered around any given context a
feature<00:00:20.460><c> which</c><00:00:20.640><c> we</c><00:00:20.820><c> call</c><00:00:21.000><c> open</c><00:00:21.359><c> book</c><00:00:21.660><c> question</c>

00:00:22.010 --> 00:00:22.020 align:start position:0%
feature which we call open book question
 

00:00:22.020 --> 00:00:23.150 align:start position:0%
feature which we call open book question
answering

00:00:23.150 --> 00:00:23.160 align:start position:0%
answering
 

00:00:23.160 --> 00:00:25.130 align:start position:0%
answering
you<00:00:23.520><c> can</c><00:00:23.580><c> ask</c><00:00:23.699><c> a</c><00:00:23.939><c> series</c><00:00:24.060><c> of</c><00:00:24.300><c> questions</c><00:00:24.539><c> in</c><00:00:24.960><c> our</c>

00:00:25.130 --> 00:00:25.140 align:start position:0%
you can ask a series of questions in our
 

00:00:25.140 --> 00:00:26.750 align:start position:0%
you can ask a series of questions in our
medical<00:00:25.380><c> large</c><00:00:25.740><c> language</c><00:00:26.039><c> model</c><00:00:26.460><c> will</c>

00:00:26.750 --> 00:00:26.760 align:start position:0%
medical large language model will
 

00:00:26.760 --> 00:00:28.429 align:start position:0%
medical large language model will
provide<00:00:27.060><c> relevant</c><00:00:27.539><c> information</c><00:00:27.840><c> and</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
provide relevant information and
 

00:00:28.439 --> 00:00:30.370 align:start position:0%
provide relevant information and
appropriate<00:00:28.859><c> responses</c>

00:00:30.370 --> 00:00:30.380 align:start position:0%
appropriate responses
 

00:00:30.380 --> 00:00:32.269 align:start position:0%
appropriate responses
objectively<00:00:31.380><c> too</c>

00:00:32.269 --> 00:00:32.279 align:start position:0%
objectively too
 

00:00:32.279 --> 00:00:34.370 align:start position:0%
objectively too
an<00:00:32.820><c> additional</c><00:00:33.180><c> feature</c><00:00:33.600><c> of</c><00:00:33.840><c> our</c><00:00:34.079><c> large</c>

00:00:34.370 --> 00:00:34.380 align:start position:0%
an additional feature of our large
 

00:00:34.380 --> 00:00:35.750 align:start position:0%
an additional feature of our large
language<00:00:34.620><c> model</c><00:00:34.980><c> is</c><00:00:35.219><c> medical</c><00:00:35.399><c> text</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
language model is medical text
 

00:00:35.760 --> 00:00:37.130 align:start position:0%
language model is medical text
generation

00:00:37.130 --> 00:00:37.140 align:start position:0%
generation
 

00:00:37.140 --> 00:00:40.250 align:start position:0%
generation
just<00:00:37.739><c> type</c><00:00:37.980><c> in</c><00:00:38.219><c> half</c><00:00:38.579><c> of</c><00:00:38.880><c> a</c><00:00:39.059><c> query</c><00:00:39.300><c> and</c><00:00:40.140><c> our</c>

00:00:40.250 --> 00:00:40.260 align:start position:0%
just type in half of a query and our
 

00:00:40.260 --> 00:00:41.690 align:start position:0%
just type in half of a query and our
large<00:00:40.440><c> language</c><00:00:40.680><c> model</c><00:00:40.980><c> will</c><00:00:41.219><c> produce</c><00:00:41.520><c> the</c>

00:00:41.690 --> 00:00:41.700 align:start position:0%
large language model will produce the
 

00:00:41.700 --> 00:00:42.950 align:start position:0%
large language model will produce the
rest<00:00:41.820><c> of</c><00:00:42.000><c> the</c><00:00:42.120><c> data</c><00:00:42.360><c> to</c><00:00:42.540><c> complete</c><00:00:42.780><c> that</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
rest of the data to complete that
 

00:00:42.960 --> 00:00:45.170 align:start position:0%
rest of the data to complete that
statement<00:00:43.320><c> answer</c><00:00:43.500><c> for</c><00:00:43.800><c> you</c>

00:00:45.170 --> 00:00:45.180 align:start position:0%
statement answer for you
 

00:00:45.180 --> 00:00:47.030 align:start position:0%
statement answer for you
often<00:00:45.780><c> times</c><00:00:45.899><c> medical</c><00:00:46.320><c> notes</c><00:00:46.800><c> are</c>

00:00:47.030 --> 00:00:47.040 align:start position:0%
often times medical notes are
 

00:00:47.040 --> 00:00:49.250 align:start position:0%
often times medical notes are
comprehensive<00:00:47.640><c> and</c><00:00:48.420><c> sometimes</c><00:00:48.780><c> there's</c><00:00:49.020><c> a</c>

00:00:49.250 --> 00:00:49.260 align:start position:0%
comprehensive and sometimes there's a
 

00:00:49.260 --> 00:00:51.350 align:start position:0%
comprehensive and sometimes there's a
need<00:00:49.379><c> for</c><00:00:49.620><c> summarization</c><00:00:50.280><c> our</c><00:00:51.059><c> large</c>

00:00:51.350 --> 00:00:51.360 align:start position:0%
need for summarization our large
 

00:00:51.360 --> 00:00:53.630 align:start position:0%
need for summarization our large
language<00:00:51.600><c> model</c><00:00:52.020><c> can</c><00:00:52.320><c> analyze</c><00:00:52.800><c> vast</c><00:00:53.280><c> amounts</c>

00:00:53.630 --> 00:00:53.640 align:start position:0%
language model can analyze vast amounts
 

00:00:53.640 --> 00:00:55.970 align:start position:0%
language model can analyze vast amounts
of<00:00:53.700><c> medical</c><00:00:53.940><c> data</c><00:00:54.480><c> instantly</c><00:00:54.960><c> to</c><00:00:55.379><c> give</c><00:00:55.500><c> you</c><00:00:55.680><c> an</c>

00:00:55.970 --> 00:00:55.980 align:start position:0%
of medical data instantly to give you an
 

00:00:55.980 --> 00:00:58.790 align:start position:0%
of medical data instantly to give you an
accurate<00:00:56.399><c> clinical</c><00:00:56.760><c> summary</c><00:00:57.559><c> it's</c><00:00:58.559><c> not</c>

00:00:58.790 --> 00:00:58.800 align:start position:0%
accurate clinical summary it's not
 

00:00:58.800 --> 00:01:00.010 align:start position:0%
accurate clinical summary it's not
always<00:00:58.980><c> doctors</c><00:00:59.399><c> who</c><00:00:59.640><c> have</c><00:00:59.820><c> questions</c>

00:01:00.010 --> 00:01:00.020 align:start position:0%
always doctors who have questions
 

00:01:00.020 --> 00:01:02.389 align:start position:0%
always doctors who have questions
sometimes<00:01:01.020><c> patients</c><00:01:01.559><c> ask</c><00:01:01.800><c> their</c><00:01:02.100><c> doctors</c>

00:01:02.389 --> 00:01:02.399 align:start position:0%
sometimes patients ask their doctors
 

00:01:02.399 --> 00:01:04.369 align:start position:0%
sometimes patients ask their doctors
questions<00:01:02.760><c> too</c><00:01:03.059><c> now</c><00:01:03.420><c> a</c><00:01:03.660><c> doctor</c><00:01:03.780><c> can</c><00:01:04.019><c> utilize</c>

00:01:04.369 --> 00:01:04.379 align:start position:0%
questions too now a doctor can utilize
 

00:01:04.379 --> 00:01:06.649 align:start position:0%
questions too now a doctor can utilize
summarization<00:01:05.100><c> on</c><00:01:05.519><c> patient</c><00:01:05.820><c> notes</c><00:01:06.180><c> and</c><00:01:06.479><c> get</c>

00:01:06.649 --> 00:01:06.659 align:start position:0%
summarization on patient notes and get
 

00:01:06.659 --> 00:01:09.060 align:start position:0%
summarization on patient notes and get
right<00:01:06.840><c> to</c><00:01:07.080><c> the</c><00:01:07.200><c> point</c>

00:01:09.060 --> 00:01:09.070 align:start position:0%
right to the point
 

00:01:09.070 --> 00:01:11.469 align:start position:0%
right to the point
[Music]

00:01:11.469 --> 00:01:11.479 align:start position:0%
[Music]
 

00:01:11.479 --> 00:01:13.890 align:start position:0%
[Music]
thank<00:01:12.479><c> you</c>

00:01:13.890 --> 00:01:13.900 align:start position:0%
thank you
 

00:01:13.900 --> 00:01:15.070 align:start position:0%
thank you
[Music]

00:01:15.070 --> 00:01:15.080 align:start position:0%
[Music]
 

00:01:15.080 --> 00:01:17.870 align:start position:0%
[Music]
when<00:01:16.080><c> compared</c><00:01:16.500><c> to</c><00:01:16.619><c> our</c><00:01:16.799><c> competition</c><00:01:17.159><c> Jon</c>

00:01:17.870 --> 00:01:17.880 align:start position:0%
when compared to our competition Jon
 

00:01:17.880 --> 00:01:19.670 align:start position:0%
when compared to our competition Jon
Snow<00:01:18.119><c> Labs</c><00:01:18.659><c> large</c><00:01:18.840><c> language</c><00:01:19.080><c> models</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
Snow Labs large language models
 

00:01:19.680 --> 00:01:21.770 align:start position:0%
Snow Labs large language models
outperform<00:01:20.280><c> by</c><00:01:20.700><c> over</c><00:01:20.880><c> 30</c><00:01:21.180><c> percent</c><00:01:21.479><c> in</c>

00:01:21.770 --> 00:01:21.780 align:start position:0%
outperform by over 30 percent in
 

00:01:21.780 --> 00:01:24.410 align:start position:0%
outperform by over 30 percent in
accuracy<00:01:22.320><c> and</c><00:01:23.100><c> again</c><00:01:23.280><c> compared</c><00:01:23.939><c> with</c><00:01:24.060><c> chat</c>

00:01:24.410 --> 00:01:24.420 align:start position:0%
accuracy and again compared with chat
 

00:01:24.420 --> 00:01:26.929 align:start position:0%
accuracy and again compared with chat
GPT<00:01:24.840><c> Jon</c><00:01:25.439><c> Snow</c><00:01:25.680><c> Labs</c><00:01:26.100><c> spark</c><00:01:26.280><c> NLP</c><00:01:26.759><c> for</c>

00:01:26.929 --> 00:01:26.939 align:start position:0%
GPT Jon Snow Labs spark NLP for
 

00:01:26.939 --> 00:01:29.210 align:start position:0%
GPT Jon Snow Labs spark NLP for
healthcare<00:01:27.240><c> makes</c><00:01:27.600><c> half</c><00:01:27.960><c> the</c><00:01:28.259><c> errors</c>

00:01:29.210 --> 00:01:29.220 align:start position:0%
healthcare makes half the errors
 

00:01:29.220 --> 00:01:32.450 align:start position:0%
healthcare makes half the errors
our<00:01:29.580><c> models</c><00:01:30.060><c> scored</c><00:01:30.360><c> 93</c><00:01:30.720><c> on</c><00:01:31.320><c> detecting</c><00:01:31.680><c> Phi</c><00:01:32.280><c> in</c>

00:01:32.450 --> 00:01:32.460 align:start position:0%
our models scored 93 on detecting Phi in
 

00:01:32.460 --> 00:01:34.789 align:start position:0%
our models scored 93 on detecting Phi in
clinical<00:01:32.700><c> notes</c><00:01:33.060><c> compared</c><00:01:33.420><c> to</c><00:01:33.479><c> chat</c><00:01:33.840><c> gpt's</c><00:01:34.560><c> 60</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
clinical notes compared to chat gpt's 60
 

00:01:34.799 --> 00:01:35.990 align:start position:0%
clinical notes compared to chat gpt's 60
percent

00:01:35.990 --> 00:01:36.000 align:start position:0%
percent
 

00:01:36.000 --> 00:01:38.330 align:start position:0%
percent
Jon<00:01:36.360><c> Snow</c><00:01:36.600><c> Labs</c><00:01:37.020><c> delivered</c><00:01:37.320><c> a</c><00:01:37.439><c> whopping</c><00:01:37.799><c> 76</c>

00:01:38.330 --> 00:01:38.340 align:start position:0%
Jon Snow Labs delivered a whopping 76
 

00:01:38.340 --> 00:01:40.969 align:start position:0%
Jon Snow Labs delivered a whopping 76
accuracy<00:01:39.299><c> on</c><00:01:39.479><c> code</c><00:01:39.659><c> mapping</c><00:01:40.079><c> versus</c><00:01:40.320><c> 26</c>

00:01:40.969 --> 00:01:40.979 align:start position:0%
accuracy on code mapping versus 26
 

00:01:40.979 --> 00:01:46.460 align:start position:0%
accuracy on code mapping versus 26
percent<00:01:41.220><c> for</c><00:01:41.579><c> GPT</c><00:01:41.880><c> 3</c><00:01:42.240><c> and</c><00:01:42.479><c> 36</c><00:01:42.900><c> percent</c><00:01:43.140><c> on</c><00:01:43.439><c> gbt4</c>

00:01:46.460 --> 00:01:46.470 align:start position:0%
 
 

00:01:46.470 --> 00:01:49.389 align:start position:0%
 
[Music]

00:01:49.389 --> 00:01:49.399 align:start position:0%
[Music]
 

00:01:49.399 --> 00:01:52.249 align:start position:0%
[Music]
Jon<00:01:50.399><c> Snow</c><00:01:50.640><c> Labs</c><00:01:51.060><c> runs</c><00:01:51.360><c> behind</c><00:01:51.600><c> your</c><00:01:51.780><c> firewall</c>

00:01:52.249 --> 00:01:52.259 align:start position:0%
Jon Snow Labs runs behind your firewall
 

00:01:52.259 --> 00:01:55.069 align:start position:0%
Jon Snow Labs runs behind your firewall
under<00:01:52.799><c> your</c><00:01:53.159><c> security</c><00:01:53.399><c> controls</c><00:01:54.079><c> designed</c>

00:01:55.069 --> 00:01:55.079 align:start position:0%
under your security controls designed
 

00:01:55.079 --> 00:01:56.749 align:start position:0%
under your security controls designed
from<00:01:55.200><c> the</c><00:01:55.380><c> ground</c><00:01:55.500><c> up</c><00:01:55.740><c> for</c><00:01:56.040><c> high</c><00:01:56.280><c> compliance</c>

00:01:56.749 --> 00:01:56.759 align:start position:0%
from the ground up for high compliance
 

00:01:56.759 --> 00:01:59.030 align:start position:0%
from the ground up for high compliance
environments<00:01:57.299><c> you</c><00:01:57.960><c> can</c><00:01:58.140><c> be</c><00:01:58.259><c> assured</c><00:01:58.619><c> that</c><00:01:58.799><c> no</c>

00:01:59.030 --> 00:01:59.040 align:start position:0%
environments you can be assured that no
 

00:01:59.040 --> 00:02:01.069 align:start position:0%
environments you can be assured that no
data<00:01:59.399><c> is</c><00:01:59.640><c> ever</c><00:01:59.820><c> shared</c><00:02:00.240><c> to</c><00:02:00.420><c> any</c><00:02:00.600><c> third</c><00:02:00.840><c> party</c>

00:02:01.069 --> 00:02:01.079 align:start position:0%
data is ever shared to any third party
 

00:02:01.079 --> 00:02:03.289 align:start position:0%
data is ever shared to any third party
or<00:02:01.500><c> cloud</c><00:02:01.799><c> service</c>

00:02:03.289 --> 00:02:03.299 align:start position:0%
or cloud service
 

00:02:03.299 --> 00:02:05.330 align:start position:0%
or cloud service
our<00:02:03.720><c> large</c><00:02:04.020><c> language</c><00:02:04.259><c> models</c><00:02:04.860><c> also</c><00:02:05.159><c> don't</c>

00:02:05.330 --> 00:02:05.340 align:start position:0%
our large language models also don't
 

00:02:05.340 --> 00:02:07.310 align:start position:0%
our large language models also don't
require<00:02:05.640><c> high-end</c><00:02:06.240><c> machines</c><00:02:06.600><c> with</c><00:02:06.960><c> multiple</c>

00:02:07.310 --> 00:02:07.320 align:start position:0%
require high-end machines with multiple
 

00:02:07.320 --> 00:02:10.430 align:start position:0%
require high-end machines with multiple
gpus<00:02:07.920><c> to</c><00:02:08.520><c> generate</c><00:02:08.819><c> results</c><00:02:09.000><c> quickly</c><00:02:09.479><c> our</c>

00:02:10.430 --> 00:02:10.440 align:start position:0%
gpus to generate results quickly our
 

00:02:10.440 --> 00:02:12.110 align:start position:0%
gpus to generate results quickly our
models<00:02:10.860><c> are</c><00:02:10.979><c> updated</c><00:02:11.220><c> every</c><00:02:11.459><c> two</c><00:02:11.700><c> weeks</c><00:02:11.940><c> to</c>

00:02:12.110 --> 00:02:12.120 align:start position:0%
models are updated every two weeks to
 

00:02:12.120 --> 00:02:14.330 align:start position:0%
models are updated every two weeks to
reflect<00:02:12.420><c> new</c><00:02:12.599><c> research</c><00:02:13.080><c> clinical</c><00:02:13.980><c> trials</c>

00:02:14.330 --> 00:02:14.340 align:start position:0%
reflect new research clinical trials
 

00:02:14.340 --> 00:02:17.869 align:start position:0%
reflect new research clinical trials
guidelines<00:02:15.120><c> and</c><00:02:15.420><c> terminologies</c>

00:02:17.869 --> 00:02:17.879 align:start position:0%
guidelines and terminologies
 

00:02:17.879 --> 00:02:19.910 align:start position:0%
guidelines and terminologies
in<00:02:18.420><c> order</c><00:02:18.540><c> to</c><00:02:18.780><c> not</c><00:02:18.900><c> be</c><00:02:19.080><c> obsolete</c><00:02:19.620><c> we</c><00:02:19.739><c> went</c>

00:02:19.910 --> 00:02:19.920 align:start position:0%
in order to not be obsolete we went
 

00:02:19.920 --> 00:02:21.290 align:start position:0%
in order to not be obsolete we went
through<00:02:20.099><c> three</c><00:02:20.340><c> architectures</c><00:02:20.879><c> over</c><00:02:21.120><c> the</c>

00:02:21.290 --> 00:02:21.300 align:start position:0%
through three architectures over the
 

00:02:21.300 --> 00:02:23.089 align:start position:0%
through three architectures over the
past<00:02:21.420><c> two</c><00:02:21.660><c> months</c><00:02:21.900><c> alone</c><00:02:22.080><c> we</c><00:02:22.739><c> continually</c>

00:02:23.089 --> 00:02:23.099 align:start position:0%
past two months alone we continually
 

00:02:23.099 --> 00:02:24.890 align:start position:0%
past two months alone we continually
update<00:02:23.520><c> our</c><00:02:23.760><c> large</c><00:02:23.940><c> language</c><00:02:24.180><c> models</c><00:02:24.660><c> every</c>

00:02:24.890 --> 00:02:24.900 align:start position:0%
update our large language models every
 

00:02:24.900 --> 00:02:27.110 align:start position:0%
update our large language models every
two<00:02:25.140><c> weeks</c><00:02:25.379><c> as</c><00:02:25.620><c> research</c><00:02:26.040><c> evolves</c><00:02:26.520><c> to</c><00:02:26.879><c> ensure</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
two weeks as research evolves to ensure
 

00:02:27.120 --> 00:02:30.350 align:start position:0%
two weeks as research evolves to ensure
they<00:02:27.420><c> remain</c><00:02:27.720><c> Cutting</c><00:02:28.200><c> Edge</c>

00:02:30.350 --> 00:02:30.360 align:start position:0%
 
 

00:02:30.360 --> 00:02:32.480 align:start position:0%
 
thank<00:02:30.480><c> you</c>

