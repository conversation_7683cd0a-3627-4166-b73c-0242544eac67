WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.690 align:start position:0%
 
Heat. Heat.

00:00:01.690 --> 00:00:01.700 align:start position:0%
Heat. Heat.
 

00:00:01.700 --> 00:00:20.390 align:start position:0%
Heat. Heat.
[Music]

00:00:20.390 --> 00:00:20.400 align:start position:0%
[Music]
 

00:00:20.400 --> 00:00:24.210 align:start position:0%
[Music]
Heat. Heat.

00:00:24.210 --> 00:00:24.220 align:start position:0%
 
 

00:00:24.220 --> 00:00:40.230 align:start position:0%
 
[Music]

00:00:40.230 --> 00:00:40.240 align:start position:0%
 
 

00:00:40.240 --> 00:00:51.140 align:start position:0%
 
[Music]

00:00:51.140 --> 00:00:51.150 align:start position:0%
[Music]
 

00:00:51.150 --> 00:00:55.450 align:start position:0%
[Music]
[Applause]

00:00:55.450 --> 00:00:55.460 align:start position:0%
 
 

00:00:55.460 --> 00:01:08.550 align:start position:0%
 
[Music]

00:01:08.550 --> 00:01:08.560 align:start position:0%
[Music]
 

00:01:08.560 --> 00:01:11.350 align:start position:0%
[Music]
Holes<00:01:08.960><c> can</c><00:01:09.200><c> be</c><00:01:09.920><c> um</c><00:01:10.080><c> just</c><00:01:10.479><c> screwed.</c><00:01:10.640><c> glued</c><00:01:11.040><c> onto</c>

00:01:11.350 --> 00:01:11.360 align:start position:0%
Holes can be um just screwed. glued onto
 

00:01:11.360 --> 00:01:14.310 align:start position:0%
Holes can be um just screwed. glued onto
this<00:01:11.840><c> um</c><00:01:12.000><c> rope</c><00:01:12.240><c> out</c><00:01:12.479><c> here.</c><00:01:13.280><c> Then</c><00:01:13.520><c> we</c><00:01:13.760><c> hang</c><00:01:14.000><c> in</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
this um rope out here. Then we hang in
 

00:01:14.320 --> 00:01:16.630 align:start position:0%
this um rope out here. Then we hang in
this<00:01:14.960><c> um</c><00:01:15.119><c> beams,</c>

00:01:16.630 --> 00:01:16.640 align:start position:0%
this um beams,
 

00:01:16.640 --> 00:01:18.950 align:start position:0%
this um beams,
horizontal<00:01:17.280><c> beams</c><00:01:17.759><c> with</c><00:01:18.080><c> the</c><00:01:18.400><c> holders</c><00:01:18.799><c> for</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
horizontal beams with the holders for
 

00:01:18.960 --> 00:01:22.550 align:start position:0%
horizontal beams with the holders for
the<00:01:19.119><c> models</c><00:01:20.320><c> and</c><00:01:20.720><c> the</c><00:01:20.960><c> models</c><00:01:21.600><c> are</c><00:01:21.840><c> just</c><00:01:22.240><c> then</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
the models and the models are just then
 

00:01:22.560 --> 00:01:25.109 align:start position:0%
the models and the models are just then
put<00:01:22.880><c> into</c><00:01:23.280><c> the</c><00:01:23.759><c> model</c><00:01:24.080><c> holders</c><00:01:24.560><c> and</c><00:01:24.880><c> are</c>

00:01:25.109 --> 00:01:25.119 align:start position:0%
put into the model holders and are
 

00:01:25.119 --> 00:01:27.190 align:start position:0%
put into the model holders and are
tension<00:01:25.600><c> free.</c><00:01:25.920><c> So</c><00:01:26.080><c> if</c><00:01:26.240><c> there</c><00:01:26.400><c> is</c><00:01:26.640><c> wings,</c><00:01:26.960><c> they</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
tension free. So if there is wings, they
 

00:01:27.200 --> 00:01:30.390 align:start position:0%
tension free. So if there is wings, they
can<00:01:27.360><c> swing</c><00:01:27.680><c> a</c><00:01:27.920><c> little</c><00:01:28.000><c> bit</c><00:01:28.720><c> and</c><00:01:29.520><c> uh</c><00:01:29.840><c> should</c><00:01:30.159><c> not</c>

00:01:30.390 --> 00:01:30.400 align:start position:0%
can swing a little bit and uh should not
 

00:01:30.400 --> 00:01:32.230 align:start position:0%
can swing a little bit and uh should not
break<00:01:30.720><c> like</c><00:01:30.880><c> in</c><00:01:31.119><c> this</c><00:01:31.360><c> example,</c><00:01:31.759><c> but</c><00:01:31.920><c> this</c><00:01:32.079><c> is</c>

00:01:32.230 --> 00:01:32.240 align:start position:0%
break like in this example, but this is
 

00:01:32.240 --> 00:01:36.069 align:start position:0%
break like in this example, but this is
from<00:01:32.479><c> the</c><00:01:32.560><c> stone.</c><00:01:33.520><c> Yeah,</c><00:01:34.079><c> we</c><00:01:34.240><c> have</c><00:01:34.400><c> in</c><00:01:34.640><c> total</c>

00:01:36.069 --> 00:01:36.079 align:start position:0%
from the stone. Yeah, we have in total
 

00:01:36.079 --> 00:01:40.630 align:start position:0%
from the stone. Yeah, we have in total
4,500<00:01:37.520><c> models</c><00:01:38.320><c> in</c><00:01:38.640><c> this</c><00:01:38.799><c> front</c><00:01:39.759><c> and</c><00:01:40.240><c> at</c><00:01:40.479><c> the</c>

00:01:40.630 --> 00:01:40.640 align:start position:0%
4,500 models in this front and at the
 

00:01:40.640 --> 00:01:43.510 align:start position:0%
4,500 models in this front and at the
moment

00:01:43.510 --> 00:01:43.520 align:start position:0%
moment
 

00:01:43.520 --> 00:01:49.030 align:start position:0%
moment
there<00:01:43.680><c> are</c><00:01:44.000><c> seven</c><00:01:44.720><c> with</c><00:01:45.360><c> um</c><00:01:45.759><c> these</c><00:01:45.920><c> kind</c><00:01:46.240><c> of</c>

00:01:49.030 --> 00:01:49.040 align:start position:0%
there are seven with um these kind of
 

00:01:49.040 --> 00:01:51.590 align:start position:0%
there are seven with um these kind of
different

00:01:51.590 --> 00:01:51.600 align:start position:0%
different
 

00:01:51.600 --> 00:01:54.149 align:start position:0%
different
and<00:01:52.560><c> a</c><00:01:52.799><c> really</c><00:01:53.119><c> interesting</c><00:01:53.600><c> thing</c><00:01:53.759><c> as</c><00:01:54.000><c> well</c>

00:01:54.149 --> 00:01:54.159 align:start position:0%
and a really interesting thing as well
 

00:01:54.159 --> 00:01:57.030 align:start position:0%
and a really interesting thing as well
is<00:01:54.320><c> that</c><00:01:54.560><c> we</c><00:01:54.720><c> put</c><00:01:54.960><c> all</c><00:01:55.119><c> the</c><00:01:55.360><c> cables</c><00:01:56.399><c> into</c><00:01:56.799><c> the</c>

00:01:57.030 --> 00:01:57.040 align:start position:0%
is that we put all the cables into the
 

00:01:57.040 --> 00:01:59.710 align:start position:0%
is that we put all the cables into the
structure.

00:01:59.710 --> 00:01:59.720 align:start position:0%
 
 

00:01:59.720 --> 00:02:14.949 align:start position:0%
 
[Music]

00:02:14.949 --> 00:02:14.959 align:start position:0%
[Music]
 

00:02:14.959 --> 00:02:16.440 align:start position:0%
[Music]
Heat.<00:02:15.040><c> Heat.</c>

00:02:16.440 --> 00:02:16.450 align:start position:0%
Heat. Heat.
 

00:02:16.450 --> 00:02:22.860 align:start position:0%
Heat. Heat.
[Music]

00:02:22.860 --> 00:02:22.870 align:start position:0%
[Music]
 

00:02:22.870 --> 00:02:24.030 align:start position:0%
[Music]
[Applause]

00:02:24.030 --> 00:02:24.040 align:start position:0%
[Applause]
 

00:02:24.040 --> 00:02:33.430 align:start position:0%
[Applause]
[Music]

00:02:33.430 --> 00:02:33.440 align:start position:0%
[Music]
 

00:02:33.440 --> 00:02:37.509 align:start position:0%
[Music]
a<00:02:33.760><c> very</c><00:02:34.080><c> slim</c><00:02:34.800><c> structure.</c>

00:02:37.509 --> 00:02:37.519 align:start position:0%
a very slim structure.
 

00:02:37.519 --> 00:02:41.350 align:start position:0%
a very slim structure.
We<00:02:37.840><c> even</c><00:02:38.239><c> focus</c><00:02:38.879><c> on</c><00:02:39.680><c> optimizing</c>

00:02:41.350 --> 00:02:41.360 align:start position:0%
We even focus on optimizing
 

00:02:41.360 --> 00:02:43.990 align:start position:0%
We even focus on optimizing
the<00:02:42.080><c> position</c><00:02:42.560><c> of</c><00:02:42.720><c> the</c><00:02:42.879><c> bottles</c><00:02:43.360><c> within</c><00:02:43.760><c> the</c>

00:02:43.990 --> 00:02:44.000 align:start position:0%
the position of the bottles within the
 

00:02:44.000 --> 00:02:46.630 align:start position:0%
the position of the bottles within the
frame.<00:02:44.400><c> You</c><00:02:44.560><c> can</c><00:02:44.720><c> see</c><00:02:44.959><c> there</c><00:02:46.080><c> it's</c><00:02:46.319><c> bigger</c>

00:02:46.630 --> 00:02:46.640 align:start position:0%
frame. You can see there it's bigger
 

00:02:46.640 --> 00:02:48.949 align:start position:0%
frame. You can see there it's bigger
than<00:02:47.200><c> over</c><00:02:47.440><c> there.</c>

00:02:48.949 --> 00:02:48.959 align:start position:0%
than over there.
 

00:02:48.959 --> 00:02:53.350 align:start position:0%
than over there.
The<00:02:49.120><c> sun</c><00:02:49.519><c> goes</c><00:02:50.720><c> south.</c><00:02:51.760><c> This</c><00:02:52.480><c> room</c><00:02:52.800><c> here</c><00:02:53.040><c> would</c>

00:02:53.350 --> 00:02:53.360 align:start position:0%
The sun goes south. This room here would
 

00:02:53.360 --> 00:02:56.229 align:start position:0%
The sun goes south. This room here would
make<00:02:53.519><c> a</c><00:02:53.760><c> shadow</c><00:02:54.560><c> on</c><00:02:54.800><c> the</c><00:02:55.040><c> model.</c><00:02:55.440><c> And</c><00:02:55.599><c> if</c><00:02:55.760><c> you</c>

00:02:56.229 --> 00:02:56.239 align:start position:0%
make a shadow on the model. And if you
 

00:02:56.239 --> 00:02:57.910 align:start position:0%
make a shadow on the model. And if you
put<00:02:56.400><c> it</c><00:02:56.560><c> in</c><00:02:56.720><c> this</c><00:02:56.959><c> direction,</c><00:02:57.280><c> we</c><00:02:57.519><c> can</c>

00:02:57.910 --> 00:02:57.920 align:start position:0%
put it in this direction, we can
 

00:02:57.920 --> 00:03:05.660 align:start position:0%
put it in this direction, we can
optimize

00:03:05.660 --> 00:03:05.670 align:start position:0%
 
 

00:03:05.670 --> 00:03:13.550 align:start position:0%
 
[Music]

00:03:13.550 --> 00:03:13.560 align:start position:0%
 
 

00:03:13.560 --> 00:03:22.710 align:start position:0%
 
[Music]

00:03:22.710 --> 00:03:22.720 align:start position:0%
 
 

00:03:22.720 --> 00:03:25.589 align:start position:0%
 
Heat.

00:03:25.589 --> 00:03:25.599 align:start position:0%
Heat.
 

00:03:25.599 --> 00:03:26.710 align:start position:0%
Heat.
Heat.

00:03:26.710 --> 00:03:26.720 align:start position:0%
Heat.
 

00:03:26.720 --> 00:03:35.120 align:start position:0%
Heat.
[Music]

00:03:35.120 --> 00:03:35.130 align:start position:0%
 
 

00:03:35.130 --> 00:03:37.940 align:start position:0%
 
[Music]

