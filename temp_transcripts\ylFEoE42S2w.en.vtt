WEBVTT
Kind: captions
Language: en

00:00:00.719 --> 00:00:02.230 align:start position:0%
 
hey<00:00:00.880><c> everyone</c><00:00:01.160><c> it's</c><00:00:01.319><c> aspr</c><00:00:01.760><c> and</c><00:00:01.839><c> today</c><00:00:02.040><c> let's</c>

00:00:02.230 --> 00:00:02.240 align:start position:0%
hey everyone it's aspr and today let's
 

00:00:02.240 --> 00:00:04.749 align:start position:0%
hey everyone it's aspr and today let's
build<00:00:02.480><c> Out</c><00:00:02.720><c> Auto</c><00:00:03.080><c> rag</c><00:00:03.280><c> with</c><00:00:03.399><c> Lama</c><00:00:03.719><c> 3</c><00:00:04.359><c> today</c>

00:00:04.749 --> 00:00:04.759 align:start position:0%
build Out Auto rag with Lama 3 today
 

00:00:04.759 --> 00:00:07.590 align:start position:0%
build Out Auto rag with Lama 3 today
we'll<00:00:05.240><c> give</c><00:00:05.480><c> Lama</c><00:00:05.839><c> 3</c><00:00:06.240><c> running</c><00:00:06.560><c> on</c><00:00:06.799><c> Gro</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
we'll give Lama 3 running on Gro
 

00:00:07.600 --> 00:00:09.390 align:start position:0%
we'll give Lama 3 running on Gro
long-term<00:00:08.160><c> memory</c><00:00:08.480><c> packed</c><00:00:08.760><c> by</c><00:00:08.880><c> postc</c><00:00:09.120><c> crust</c>

00:00:09.390 --> 00:00:09.400 align:start position:0%
long-term memory packed by postc crust
 

00:00:09.400 --> 00:00:12.110 align:start position:0%
long-term memory packed by postc crust
database<00:00:10.240><c> knowledge</c><00:00:10.679><c> packed</c><00:00:10.920><c> by</c><00:00:11.080><c> PG</c><00:00:11.400><c> vector</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
database knowledge packed by PG vector
 

00:00:12.120 --> 00:00:14.749 align:start position:0%
database knowledge packed by PG vector
and<00:00:12.320><c> tools</c><00:00:12.639><c> to</c><00:00:12.880><c> search</c><00:00:13.280><c> the</c><00:00:13.480><c> web</c><00:00:13.920><c> send</c><00:00:14.280><c> emails</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
and tools to search the web send emails
 

00:00:14.759 --> 00:00:17.269 align:start position:0%
and tools to search the web send emails
trigger<00:00:15.120><c> workflows</c><00:00:15.639><c> ET</c><00:00:16.520><c> so</c><00:00:16.720><c> when</c><00:00:16.880><c> the</c><00:00:17.000><c> user</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
trigger workflows ET so when the user
 

00:00:17.279 --> 00:00:19.870 align:start position:0%
trigger workflows ET so when the user
asks<00:00:17.520><c> a</c><00:00:17.720><c> question</c><00:00:18.480><c> Lama</c><00:00:18.880><c> 3</c><00:00:19.160><c> decides</c><00:00:19.720><c> whether</c>

00:00:19.870 --> 00:00:19.880 align:start position:0%
asks a question Lama 3 decides whether
 

00:00:19.880 --> 00:00:22.070 align:start position:0%
asks a question Lama 3 decides whether
to<00:00:20.039><c> search</c><00:00:20.320><c> its</c><00:00:20.560><c> knowledge</c><00:00:21.240><c> memory</c><00:00:21.960><c> the</c>

00:00:22.070 --> 00:00:22.080 align:start position:0%
to search its knowledge memory the
 

00:00:22.080 --> 00:00:23.790 align:start position:0%
to search its knowledge memory the
internet<00:00:22.439><c> or</c><00:00:22.560><c> make</c><00:00:22.720><c> an</c><00:00:22.840><c> API</c><00:00:23.160><c> call</c><00:00:23.519><c> and</c><00:00:23.640><c> then</c>

00:00:23.790 --> 00:00:23.800 align:start position:0%
internet or make an API call and then
 

00:00:23.800 --> 00:00:26.150 align:start position:0%
internet or make an API call and then
answer<00:00:24.160><c> with</c><00:00:24.279><c> that</c><00:00:24.519><c> context</c><00:00:25.519><c> this</c><00:00:25.640><c> is</c><00:00:25.800><c> how</c><00:00:26.000><c> the</c>

00:00:26.150 --> 00:00:26.160 align:start position:0%
answer with that context this is how the
 

00:00:26.160 --> 00:00:28.150 align:start position:0%
answer with that context this is how the
app<00:00:26.439><c> looks</c><00:00:26.760><c> like</c><00:00:27.400><c> we're</c><00:00:27.560><c> going</c><00:00:27.679><c> to</c><00:00:27.800><c> give</c><00:00:27.920><c> it</c><00:00:28.039><c> a</c>

00:00:28.150 --> 00:00:28.160 align:start position:0%
app looks like we're going to give it a
 

00:00:28.160 --> 00:00:30.390 align:start position:0%
app looks like we're going to give it a
blog<00:00:28.480><c> post</c><00:00:29.000><c> and</c><00:00:29.160><c> then</c><00:00:29.400><c> ask</c><00:00:29.679><c> question</c><00:00:30.160><c> from</c><00:00:30.320><c> the</c>

00:00:30.390 --> 00:00:30.400 align:start position:0%
blog post and then ask question from the
 

00:00:30.400 --> 00:00:32.429 align:start position:0%
blog post and then ask question from the
blog<00:00:30.720><c> post</c><00:00:31.039><c> or</c><00:00:31.240><c> the</c><00:00:31.400><c> internet</c><00:00:31.840><c> or</c><00:00:32.000><c> its</c><00:00:32.160><c> chat</c>

00:00:32.429 --> 00:00:32.439 align:start position:0%
blog post or the internet or its chat
 

00:00:32.439 --> 00:00:35.069 align:start position:0%
blog post or the internet or its chat
history<00:00:33.120><c> so</c><00:00:33.320><c> let's</c><00:00:33.520><c> start</c><00:00:33.920><c> with</c><00:00:34.280><c> what</c><00:00:34.480><c> did</c>

00:00:35.069 --> 00:00:35.079 align:start position:0%
history so let's start with what did
 

00:00:35.079 --> 00:00:35.990 align:start position:0%
history so let's start with what did
meta

00:00:35.990 --> 00:00:36.000 align:start position:0%
meta
 

00:00:36.000 --> 00:00:38.549 align:start position:0%
meta
release<00:00:37.000><c> so</c><00:00:37.239><c> over</c><00:00:37.520><c> here</c><00:00:37.719><c> what</c><00:00:37.960><c> we</c><00:00:38.079><c> want</c><00:00:38.280><c> is</c><00:00:38.440><c> we</c>

00:00:38.549 --> 00:00:38.559 align:start position:0%
release so over here what we want is we
 

00:00:38.559 --> 00:00:40.750 align:start position:0%
release so over here what we want is we
want<00:00:38.719><c> the</c><00:00:39.000><c> Llama</c><00:00:39.399><c> 3</c><00:00:39.719><c> to</c><00:00:39.920><c> search</c><00:00:40.239><c> its</c><00:00:40.440><c> knowledge</c>

00:00:40.750 --> 00:00:40.760 align:start position:0%
want the Llama 3 to search its knowledge
 

00:00:40.760 --> 00:00:42.549 align:start position:0%
want the Llama 3 to search its knowledge
base<00:00:41.000><c> for</c><00:00:41.160><c> this</c><00:00:41.320><c> information</c><00:00:41.960><c> and</c><00:00:42.200><c> then</c><00:00:42.399><c> give</c>

00:00:42.549 --> 00:00:42.559 align:start position:0%
base for this information and then give
 

00:00:42.559 --> 00:00:45.110 align:start position:0%
base for this information and then give
an<00:00:42.760><c> answer</c><00:00:43.640><c> let's</c><00:00:43.840><c> see</c><00:00:44.000><c> how</c><00:00:44.120><c> it</c>

00:00:45.110 --> 00:00:45.120 align:start position:0%
an answer let's see how it
 

00:00:45.120 --> 00:00:48.430 align:start position:0%
an answer let's see how it
faes<00:00:46.120><c> now</c><00:00:46.480><c> Gro</c><00:00:47.039><c> does</c><00:00:47.280><c> not</c><00:00:47.520><c> yet</c><00:00:47.920><c> support</c>

00:00:48.430 --> 00:00:48.440 align:start position:0%
faes now Gro does not yet support
 

00:00:48.440 --> 00:00:51.029 align:start position:0%
faes now Gro does not yet support
streaming<00:00:49.039><c> with</c><00:00:49.199><c> function</c><00:00:49.600><c> calls</c><00:00:50.399><c> so</c><00:00:50.840><c> we're</c>

00:00:51.029 --> 00:00:51.039 align:start position:0%
streaming with function calls so we're
 

00:00:51.039 --> 00:00:52.790 align:start position:0%
streaming with function calls so we're
going<00:00:51.120><c> to</c><00:00:51.280><c> get</c><00:00:51.480><c> the</c><00:00:51.640><c> answers</c><00:00:52.399><c> without</c>

00:00:52.790 --> 00:00:52.800 align:start position:0%
going to get the answers without
 

00:00:52.800 --> 00:00:55.950 align:start position:0%
going to get the answers without
streaming<00:00:53.800><c> but</c><00:00:53.960><c> it's</c><00:00:54.120><c> not</c><00:00:54.280><c> too</c><00:00:54.440><c> bad</c><00:00:54.960><c> really</c>

00:00:55.950 --> 00:00:55.960 align:start position:0%
streaming but it's not too bad really
 

00:00:55.960 --> 00:00:57.790 align:start position:0%
streaming but it's not too bad really
it's<00:00:56.320><c> grock</c><00:00:56.680><c> is</c><00:00:56.840><c> pretty</c><00:00:57.079><c> fast</c><00:00:57.320><c> so</c><00:00:57.520><c> it</c><00:00:57.640><c> did</c>

00:00:57.790 --> 00:00:57.800 align:start position:0%
it's grock is pretty fast so it did
 

00:00:57.800 --> 00:00:59.750 align:start position:0%
it's grock is pretty fast so it did
search<00:00:58.120><c> its</c><00:00:58.280><c> knowledge</c><00:00:58.640><c> base</c><00:00:59.320><c> gave</c><00:00:59.480><c> us</c><00:00:59.640><c> the</c>

00:00:59.750 --> 00:00:59.760 align:start position:0%
search its knowledge base gave us the
 

00:00:59.760 --> 00:01:03.070 align:start position:0%
search its knowledge base gave us the
right<00:01:00.199><c> answer</c><00:01:00.640><c> now</c><00:01:00.840><c> let's</c><00:01:01.160><c> ask</c><00:01:01.800><c> um</c><00:01:02.760><c> what's</c>

00:01:03.070 --> 00:01:03.080 align:start position:0%
right answer now let's ask um what's
 

00:01:03.080 --> 00:01:06.510 align:start position:0%
right answer now let's ask um what's
happening<00:01:04.080><c> in</c><00:01:04.760><c> France</c><00:01:05.760><c> over</c><00:01:06.040><c> here</c><00:01:06.240><c> we're</c>

00:01:06.510 --> 00:01:06.520 align:start position:0%
happening in France over here we're
 

00:01:06.520 --> 00:01:08.670 align:start position:0%
happening in France over here we're
expecting<00:01:07.040><c> llama</c><00:01:07.439><c> 3</c><00:01:07.640><c> to</c><00:01:07.759><c> search</c><00:01:08.159><c> the</c><00:01:08.320><c> internet</c>

00:01:08.670 --> 00:01:08.680 align:start position:0%
expecting llama 3 to search the internet
 

00:01:08.680 --> 00:01:09.950 align:start position:0%
expecting llama 3 to search the internet
and<00:01:08.840><c> then</c><00:01:09.000><c> give</c><00:01:09.200><c> the</c>

00:01:09.950 --> 00:01:09.960 align:start position:0%
and then give the
 

00:01:09.960 --> 00:01:12.350 align:start position:0%
and then give the
answer<00:01:10.960><c> now</c><00:01:11.159><c> there's</c><00:01:11.320><c> a</c><00:01:11.560><c> small</c><00:01:11.920><c> possibility</c>

00:01:12.350 --> 00:01:12.360 align:start position:0%
answer now there's a small possibility
 

00:01:12.360 --> 00:01:14.030 align:start position:0%
answer now there's a small possibility
you'll<00:01:12.520><c> search</c><00:01:12.799><c> its</c><00:01:13.040><c> knowledge</c><00:01:13.400><c> base</c><00:01:13.880><c> but</c>

00:01:14.030 --> 00:01:14.040 align:start position:0%
you'll search its knowledge base but
 

00:01:14.040 --> 00:01:15.429 align:start position:0%
you'll search its knowledge base but
let's<00:01:14.200><c> see</c><00:01:14.360><c> what</c><00:01:14.479><c> the</c><00:01:14.600><c> model</c>

00:01:15.429 --> 00:01:15.439 align:start position:0%
let's see what the model
 

00:01:15.439 --> 00:01:18.670 align:start position:0%
let's see what the model
does<00:01:16.439><c> okay</c><00:01:16.720><c> it</c><00:01:16.920><c> searched</c><00:01:17.320><c> the</c><00:01:17.439><c> internet</c><00:01:18.200><c> and</c>

00:01:18.670 --> 00:01:18.680 align:start position:0%
does okay it searched the internet and
 

00:01:18.680 --> 00:01:22.510 align:start position:0%
does okay it searched the internet and
gave<00:01:19.080><c> a</c><00:01:19.439><c> very</c><00:01:19.720><c> nice</c><00:01:20.000><c> answer</c><00:01:20.400><c> about</c><00:01:20.799><c> the</c><00:01:21.520><c> latest</c>

00:01:22.510 --> 00:01:22.520 align:start position:0%
gave a very nice answer about the latest
 

00:01:22.520 --> 00:01:25.469 align:start position:0%
gave a very nice answer about the latest
uh<00:01:22.680><c> from</c><00:01:22.840><c> France</c><00:01:23.200><c> and</c><00:01:23.360><c> then</c><00:01:23.479><c> we'll</c><00:01:23.680><c> say</c><00:01:24.479><c> uh</c>

00:01:25.469 --> 00:01:25.479 align:start position:0%
uh from France and then we'll say uh
 

00:01:25.479 --> 00:01:29.190 align:start position:0%
uh from France and then we'll say uh
summarize<00:01:26.000><c> our</c><00:01:26.400><c> conversation</c><00:01:27.400><c> and</c><00:01:27.600><c> over</c><00:01:28.200><c> here</c>

00:01:29.190 --> 00:01:29.200 align:start position:0%
summarize our conversation and over here
 

00:01:29.200 --> 00:01:32.510 align:start position:0%
summarize our conversation and over here
it'll<00:01:29.880><c> search</c><00:01:30.079><c> as</c><00:01:30.280><c> chat</c><00:01:30.600><c> history</c><00:01:31.560><c> or</c><00:01:32.000><c> we</c><00:01:32.159><c> also</c>

00:01:32.510 --> 00:01:32.520 align:start position:0%
it'll search as chat history or we also
 

00:01:32.520 --> 00:01:35.870 align:start position:0%
it'll search as chat history or we also
provide<00:01:33.000><c> the</c><00:01:33.159><c> last</c><00:01:33.399><c> two</c><00:01:33.640><c> three</c><00:01:34.200><c> messages</c><00:01:35.200><c> in</c>

00:01:35.870 --> 00:01:35.880 align:start position:0%
provide the last two three messages in
 

00:01:35.880 --> 00:01:37.469 align:start position:0%
provide the last two three messages in
the<00:01:36.040><c> messages</c><00:01:36.479><c> list</c><00:01:36.720><c> so</c><00:01:36.840><c> it</c><00:01:36.920><c> might</c><00:01:37.119><c> just</c><00:01:37.280><c> give</c>

00:01:37.469 --> 00:01:37.479 align:start position:0%
the messages list so it might just give
 

00:01:37.479 --> 00:01:38.550 align:start position:0%
the messages list so it might just give
directly<00:01:37.799><c> from</c><00:01:37.960><c> that</c><00:01:38.119><c> let's</c><00:01:38.240><c> see</c><00:01:38.360><c> what</c><00:01:38.479><c> it</c>

00:01:38.550 --> 00:01:38.560 align:start position:0%
directly from that let's see what it
 

00:01:38.560 --> 00:01:41.030 align:start position:0%
directly from that let's see what it
does<00:01:39.560><c> okay</c><00:01:39.720><c> so</c><00:01:39.920><c> it</c><00:01:40.079><c> did</c><00:01:40.240><c> search</c><00:01:40.520><c> US</c><00:01:40.680><c> chat</c>

00:01:41.030 --> 00:01:41.040 align:start position:0%
does okay so it did search US chat
 

00:01:41.040 --> 00:01:44.109 align:start position:0%
does okay so it did search US chat
history<00:01:41.640><c> and</c><00:01:42.000><c> gave</c><00:01:42.240><c> us</c><00:01:42.439><c> the</c><00:01:42.799><c> right</c><00:01:43.040><c> answer</c><00:01:43.799><c> so</c>

00:01:44.109 --> 00:01:44.119 align:start position:0%
history and gave us the right answer so
 

00:01:44.119 --> 00:01:46.190 align:start position:0%
history and gave us the right answer so
this<00:01:44.240><c> is</c><00:01:44.479><c> how</c><00:01:44.840><c> autonomous</c><00:01:45.360><c> rag</c>

00:01:46.190 --> 00:01:46.200 align:start position:0%
this is how autonomous rag
 

00:01:46.200 --> 00:01:50.069 align:start position:0%
this is how autonomous rag
Works<00:01:47.200><c> uh</c><00:01:47.280><c> to</c><00:01:47.520><c> answer</c><00:01:48.280><c> questions</c><00:01:49.280><c> the</c><00:01:49.680><c> model</c>

00:01:50.069 --> 00:01:50.079 align:start position:0%
Works uh to answer questions the model
 

00:01:50.079 --> 00:01:51.749 align:start position:0%
Works uh to answer questions the model
will<00:01:50.320><c> decide</c><00:01:50.680><c> depending</c><00:01:51.000><c> on</c><00:01:51.119><c> the</c><00:01:51.280><c> context</c>

00:01:51.749 --> 00:01:51.759 align:start position:0%
will decide depending on the context
 

00:01:51.759 --> 00:01:53.310 align:start position:0%
will decide depending on the context
whether<00:01:51.960><c> it</c><00:01:52.040><c> needs</c><00:01:52.240><c> to</c><00:01:52.399><c> search</c><00:01:52.600><c> its</c><00:01:52.799><c> memory</c>

00:01:53.310 --> 00:01:53.320 align:start position:0%
whether it needs to search its memory
 

00:01:53.320 --> 00:01:55.590 align:start position:0%
whether it needs to search its memory
knowledge<00:01:53.719><c> or</c><00:01:54.079><c> executor</c><00:01:54.560><c> tool</c>

00:01:55.590 --> 00:01:55.600 align:start position:0%
knowledge or executor tool
 

00:01:55.600 --> 00:01:58.550 align:start position:0%
knowledge or executor tool
call<00:01:56.600><c> uh</c><00:01:56.960><c> now</c><00:01:57.119><c> let's</c><00:01:57.399><c> show</c><00:01:57.759><c> step</c><00:01:58.039><c> by</c><00:01:58.200><c> step</c><00:01:58.439><c> how</c>

00:01:58.550 --> 00:01:58.560 align:start position:0%
call uh now let's show step by step how
 

00:01:58.560 --> 00:02:00.870 align:start position:0%
call uh now let's show step by step how
you<00:01:58.640><c> can</c><00:01:58.799><c> run</c><00:01:59.039><c> this</c><00:01:59.200><c> yourself</c><00:02:00.000><c> the</c><00:02:00.200><c> code</c><00:02:00.479><c> for</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
you can run this yourself the code for
 

00:02:00.880 --> 00:02:03.670 align:start position:0%
you can run this yourself the code for
auto<00:02:01.280><c> rag</c><00:02:01.719><c> is</c><00:02:01.920><c> under</c><00:02:02.159><c> the</c><00:02:02.280><c> F</c><00:02:02.520><c> dat</c><00:02:02.719><c> repo</c><00:02:03.399><c> so</c><00:02:03.560><c> you</c>

00:02:03.670 --> 00:02:03.680 align:start position:0%
auto rag is under the F dat repo so you
 

00:02:03.680 --> 00:02:06.389 align:start position:0%
auto rag is under the F dat repo so you
can<00:02:03.840><c> fork</c><00:02:04.360><c> and</c><00:02:04.600><c> clone</c><00:02:04.960><c> this</c><00:02:05.240><c> repository</c><00:02:06.240><c> and</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
can fork and clone this repository and
 

00:02:06.399 --> 00:02:08.070 align:start position:0%
can fork and clone this repository and
go<00:02:06.520><c> to</c><00:02:06.680><c> the</c><00:02:06.799><c> cookbooks</c><00:02:07.280><c> folder</c><00:02:07.560><c> for</c><00:02:07.680><c> the</c><00:02:07.880><c> code</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
go to the cookbooks folder for the code
 

00:02:08.080 --> 00:02:09.669 align:start position:0%
go to the cookbooks folder for the code
for<00:02:08.239><c> this</c><00:02:08.399><c> so</c><00:02:08.560><c> under</c><00:02:08.800><c> the</c><00:02:08.920><c> cookbooks</c><00:02:09.440><c> you</c><00:02:09.560><c> can</c>

00:02:09.669 --> 00:02:09.679 align:start position:0%
for this so under the cookbooks you can
 

00:02:09.679 --> 00:02:12.670 align:start position:0%
for this so under the cookbooks you can
find<00:02:09.879><c> the</c><00:02:10.000><c> code</c><00:02:10.200><c> under</c><00:02:10.479><c> llms</c><00:02:11.200><c> gr</c><00:02:11.480><c> and</c><00:02:11.680><c> autoag</c>

00:02:12.670 --> 00:02:12.680 align:start position:0%
find the code under llms gr and autoag
 

00:02:12.680 --> 00:02:15.670 align:start position:0%
find the code under llms gr and autoag
where<00:02:12.879><c> we</c><00:02:13.120><c> have</c><00:02:13.520><c> stepbystep</c><00:02:14.680><c> instructions</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
where we have stepbystep instructions
 

00:02:15.680 --> 00:02:17.430 align:start position:0%
where we have stepbystep instructions
how<00:02:15.920><c> to</c><00:02:16.040><c> run</c><00:02:16.239><c> autonomous</c><00:02:16.680><c> rag</c><00:02:16.959><c> with</c><00:02:17.040><c> Lama</c><00:02:17.280><c> 3</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
how to run autonomous rag with Lama 3
 

00:02:17.440 --> 00:02:19.869 align:start position:0%
how to run autonomous rag with Lama 3
and<00:02:17.599><c> drop</c><00:02:18.400><c> after</c><00:02:18.599><c> you've</c><00:02:18.760><c> cloned</c><00:02:19.040><c> the</c><00:02:19.120><c> repo</c>

00:02:19.869 --> 00:02:19.879 align:start position:0%
and drop after you've cloned the repo
 

00:02:19.879 --> 00:02:21.470 align:start position:0%
and drop after you've cloned the repo
open<00:02:20.080><c> it</c><00:02:20.239><c> up</c><00:02:20.400><c> in</c><00:02:20.560><c> the</c><00:02:20.720><c> code</c><00:02:20.920><c> letterer</c><00:02:21.239><c> of</c><00:02:21.319><c> your</c>

00:02:21.470 --> 00:02:21.480 align:start position:0%
open it up in the code letterer of your
 

00:02:21.480 --> 00:02:22.830 align:start position:0%
open it up in the code letterer of your
choice<00:02:21.800><c> I'm</c><00:02:21.879><c> going</c><00:02:22.000><c> to</c><00:02:22.080><c> stop</c><00:02:22.319><c> the</c><00:02:22.440><c> existing</c>

00:02:22.830 --> 00:02:22.840 align:start position:0%
choice I'm going to stop the existing
 

00:02:22.840 --> 00:02:25.070 align:start position:0%
choice I'm going to stop the existing
application<00:02:23.599><c> so</c><00:02:24.040><c> I'll</c><00:02:24.319><c> show</c><00:02:24.480><c> you</c><00:02:24.640><c> step</c><00:02:24.879><c> by</c>

00:02:25.070 --> 00:02:25.080 align:start position:0%
application so I'll show you step by
 

00:02:25.080 --> 00:02:27.710 align:start position:0%
application so I'll show you step by
step<00:02:25.239><c> how</c><00:02:25.360><c> to</c><00:02:25.480><c> run</c><00:02:25.720><c> this</c><00:02:26.720><c> so</c><00:02:26.959><c> open</c><00:02:27.200><c> it</c><00:02:27.360><c> up</c><00:02:27.599><c> in</c>

00:02:27.710 --> 00:02:27.720 align:start position:0%
step how to run this so open it up in
 

00:02:27.720 --> 00:02:29.190 align:start position:0%
step how to run this so open it up in
the<00:02:27.840><c> code</c><00:02:28.040><c> edor</c><00:02:28.280><c> of</c><00:02:28.400><c> your</c><00:02:28.519><c> choice</c><00:02:28.920><c> under</c>

00:02:29.190 --> 00:02:29.200 align:start position:0%
the code edor of your choice under
 

00:02:29.200 --> 00:02:32.550 align:start position:0%
the code edor of your choice under
cookbooks<00:02:29.920><c> llms</c><00:02:30.800><c> Gro</c><00:02:31.160><c> and</c><00:02:31.280><c> autoag</c><00:02:32.280><c> where</c><00:02:32.440><c> the</c>

00:02:32.550 --> 00:02:32.560 align:start position:0%
cookbooks llms Gro and autoag where the
 

00:02:32.560 --> 00:02:34.430 align:start position:0%
cookbooks llms Gro and autoag where the
readme<00:02:33.040><c> has</c><00:02:33.400><c> step-by-step</c><00:02:34.040><c> instructions</c>

00:02:34.430 --> 00:02:34.440 align:start position:0%
readme has step-by-step instructions
 

00:02:34.440 --> 00:02:37.630 align:start position:0%
readme has step-by-step instructions
with<00:02:34.920><c> everything</c><00:02:35.920><c> after</c><00:02:36.200><c> that</c><00:02:37.080><c> fire</c><00:02:37.400><c> up</c><00:02:37.519><c> your</c>

00:02:37.630 --> 00:02:37.640 align:start position:0%
with everything after that fire up your
 

00:02:37.640 --> 00:02:39.149 align:start position:0%
with everything after that fire up your
favorite

00:02:39.149 --> 00:02:39.159 align:start position:0%
favorite
 

00:02:39.159 --> 00:02:41.869 align:start position:0%
favorite
terminal<00:02:40.159><c> and</c><00:02:40.400><c> first</c><00:02:40.720><c> create</c><00:02:41.120><c> a</c><00:02:41.440><c> virtual</c>

00:02:41.869 --> 00:02:41.879 align:start position:0%
terminal and first create a virtual
 

00:02:41.879 --> 00:02:43.790 align:start position:0%
terminal and first create a virtual
environment<00:02:42.400><c> where</c><00:02:42.560><c> we'll</c><00:02:42.760><c> install</c><00:02:43.080><c> our</c>

00:02:43.790 --> 00:02:43.800 align:start position:0%
environment where we'll install our
 

00:02:43.800 --> 00:02:46.390 align:start position:0%
environment where we'll install our
dependencies<00:02:44.800><c> then</c><00:02:45.159><c> export</c><00:02:45.519><c> your</c><00:02:45.760><c> grock</c><00:02:46.080><c> API</c>

00:02:46.390 --> 00:02:46.400 align:start position:0%
dependencies then export your grock API
 

00:02:46.400 --> 00:02:49.550 align:start position:0%
dependencies then export your grock API
key<00:02:46.640><c> you</c><00:02:46.720><c> can</c><00:02:46.879><c> get</c><00:02:47.120><c> from</c><00:02:47.400><c> console.</c><00:02:48.360><c> grock</c><00:02:49.360><c> and</c>

00:02:49.550 --> 00:02:49.560 align:start position:0%
key you can get from console. grock and
 

00:02:49.560 --> 00:02:53.190 align:start position:0%
key you can get from console. grock and
then<00:02:50.319><c> we'll</c><00:02:50.760><c> either</c><00:02:51.080><c> use</c><00:02:51.640><c> olaba</c><00:02:52.360><c> or</c><00:02:52.640><c> open</c><00:02:52.959><c> AI</c>

00:02:53.190 --> 00:02:53.200 align:start position:0%
then we'll either use olaba or open AI
 

00:02:53.200 --> 00:02:55.589 align:start position:0%
then we'll either use olaba or open AI
for<00:02:53.560><c> embeddings</c><00:02:54.560><c> so</c><00:02:54.720><c> if</c><00:02:54.800><c> you</c><00:02:54.920><c> want</c><00:02:55.040><c> to</c><00:02:55.200><c> use</c>

00:02:55.589 --> 00:02:55.599 align:start position:0%
for embeddings so if you want to use
 

00:02:55.599 --> 00:02:58.509 align:start position:0%
for embeddings so if you want to use
local<00:02:56.000><c> embeddings</c><00:02:57.000><c> you</c><00:02:57.120><c> can</c><00:02:57.319><c> run</c><00:02:57.680><c> AMA</c><00:02:58.120><c> run</c>

00:02:58.509 --> 00:02:58.519 align:start position:0%
local embeddings you can run AMA run
 

00:02:58.519 --> 00:03:01.350 align:start position:0%
local embeddings you can run AMA run
nomic<00:02:58.840><c> embed</c><00:02:59.200><c> tax</c><00:02:59.640><c> and</c><00:02:59.879><c> and</c><00:03:00.440><c> use</c><00:03:00.800><c> local</c>

00:03:01.350 --> 00:03:01.360 align:start position:0%
nomic embed tax and and use local
 

00:03:01.360 --> 00:03:04.190 align:start position:0%
nomic embed tax and and use local
embeddings<00:03:02.000><c> for</c><00:03:02.640><c> um</c><00:03:03.080><c> for</c><00:03:03.319><c> theem</c><00:03:03.920><c> uh</c><00:03:04.000><c> for</c>

00:03:04.190 --> 00:03:04.200 align:start position:0%
embeddings for um for theem uh for
 

00:03:04.200 --> 00:03:06.869 align:start position:0%
embeddings for um for theem uh for
embedding<00:03:04.680><c> the</c><00:03:05.040><c> documents</c><00:03:06.040><c> or</c><00:03:06.440><c> you</c><00:03:06.560><c> can</c><00:03:06.760><c> just</c>

00:03:06.869 --> 00:03:06.879 align:start position:0%
embedding the documents or you can just
 

00:03:06.879 --> 00:03:09.750 align:start position:0%
embedding the documents or you can just
use<00:03:07.159><c> open</c><00:03:07.440><c> AI</c><00:03:07.799><c> I</c><00:03:07.959><c> normally</c><00:03:08.319><c> just</c><00:03:08.720><c> prefer</c><00:03:09.239><c> the</c>

00:03:09.750 --> 00:03:09.760 align:start position:0%
use open AI I normally just prefer the
 

00:03:09.760 --> 00:03:13.270 align:start position:0%
use open AI I normally just prefer the
um<00:03:10.360><c> the</c><00:03:10.519><c> latest</c><00:03:11.159><c> small</c><00:03:11.640><c> embedding</c><00:03:12.159><c> model</c><00:03:12.640><c> so</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
um the latest small embedding model so
 

00:03:13.280 --> 00:03:15.789 align:start position:0%
um the latest small embedding model so
you<00:03:13.400><c> can</c><00:03:13.560><c> export</c><00:03:13.879><c> the</c><00:03:13.959><c> open</c><00:03:14.159><c> a</c><00:03:14.560><c> key</c><00:03:15.560><c> then</c>

00:03:15.789 --> 00:03:15.799 align:start position:0%
you can export the open a key then
 

00:03:15.799 --> 00:03:18.550 align:start position:0%
you can export the open a key then
install<00:03:16.280><c> the</c>

00:03:18.550 --> 00:03:18.560 align:start position:0%
install the
 

00:03:18.560 --> 00:03:21.430 align:start position:0%
install the
libraries<00:03:19.560><c> run</c><00:03:20.200><c> the</c><00:03:20.360><c> postest</c><00:03:20.920><c> database</c><00:03:21.319><c> which</c>

00:03:21.430 --> 00:03:21.440 align:start position:0%
libraries run the postest database which
 

00:03:21.440 --> 00:03:23.229 align:start position:0%
libraries run the postest database which
also<00:03:21.640><c> has</c><00:03:21.799><c> PG</c><00:03:22.080><c> Factor</c><00:03:22.560><c> it</c><00:03:22.680><c> throws</c><00:03:22.920><c> an</c><00:03:23.040><c> error</c>

00:03:23.229 --> 00:03:23.239 align:start position:0%
also has PG Factor it throws an error
 

00:03:23.239 --> 00:03:24.630 align:start position:0%
also has PG Factor it throws an error
for<00:03:23.360><c> me</c><00:03:23.519><c> because</c><00:03:23.720><c> it's</c><00:03:23.920><c> already</c><00:03:24.200><c> running</c><00:03:24.519><c> but</c>

00:03:24.630 --> 00:03:24.640 align:start position:0%
for me because it's already running but
 

00:03:24.640 --> 00:03:28.030 align:start position:0%
for me because it's already running but
for<00:03:24.799><c> you</c><00:03:24.920><c> it'll</c><00:03:25.159><c> run</c><00:03:25.920><c> the</c><00:03:26.640><c> uh</c><00:03:27.000><c> PG</c><00:03:27.400><c> Vector</c>

00:03:28.030 --> 00:03:28.040 align:start position:0%
for you it'll run the uh PG Vector
 

00:03:28.040 --> 00:03:31.429 align:start position:0%
for you it'll run the uh PG Vector
container<00:03:29.040><c> using</c><00:03:29.400><c> Doer</c>

00:03:31.429 --> 00:03:31.439 align:start position:0%
container using Doer
 

00:03:31.439 --> 00:03:33.070 align:start position:0%
container using Doer
then<00:03:31.599><c> run</c><00:03:31.879><c> the</c><00:03:32.040><c> application</c><00:03:32.439><c> with</c><00:03:32.599><c> streamlet</c>

00:03:33.070 --> 00:03:33.080 align:start position:0%
then run the application with streamlet
 

00:03:33.080 --> 00:03:36.229 align:start position:0%
then run the application with streamlet
and<00:03:33.400><c> there</c><00:03:33.519><c> it</c><00:03:34.000><c> is</c><00:03:35.000><c> so</c><00:03:35.200><c> over</c><00:03:35.480><c> here</c><00:03:35.640><c> you</c><00:03:35.760><c> can</c><00:03:36.120><c> add</c>

00:03:36.229 --> 00:03:36.239 align:start position:0%
and there it is so over here you can add
 

00:03:36.239 --> 00:03:39.470 align:start position:0%
and there it is so over here you can add
a<00:03:36.400><c> PDF</c><00:03:36.840><c> if</c><00:03:36.959><c> you</c><00:03:37.120><c> like</c><00:03:37.840><c> or</c><00:03:38.120><c> you</c><00:03:38.239><c> can</c><00:03:38.439><c> drop</c><00:03:38.640><c> in</c><00:03:38.760><c> a</c>

00:03:39.470 --> 00:03:39.480 align:start position:0%
a PDF if you like or you can drop in a
 

00:03:39.480 --> 00:03:41.990 align:start position:0%
a PDF if you like or you can drop in a
URL<00:03:40.480><c> I'm</c><00:03:40.560><c> going</c><00:03:40.680><c> to</c><00:03:40.799><c> just</c><00:03:40.920><c> drop</c><00:03:41.159><c> in</c><00:03:41.319><c> this</c><00:03:41.439><c> URL</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
URL I'm going to just drop in this URL
 

00:03:42.000 --> 00:03:43.550 align:start position:0%
URL I'm going to just drop in this URL
and<00:03:42.159><c> we'll</c><00:03:42.360><c> ask</c><00:03:42.640><c> questions</c><00:03:42.879><c> from</c><00:03:43.040><c> it</c><00:03:43.200><c> again</c><00:03:43.439><c> to</c>

00:03:43.550 --> 00:03:43.560 align:start position:0%
and we'll ask questions from it again to
 

00:03:43.560 --> 00:03:45.949 align:start position:0%
and we'll ask questions from it again to
see<00:03:43.920><c> how</c><00:03:44.599><c> Auto</c><00:03:45.000><c> rag</c>

00:03:45.949 --> 00:03:45.959 align:start position:0%
see how Auto rag
 

00:03:45.959 --> 00:03:50.149 align:start position:0%
see how Auto rag
works<00:03:46.959><c> so</c><00:03:47.760><c> again</c><00:03:48.040><c> to</c><00:03:48.519><c> sum</c><00:03:48.840><c> it</c><00:03:49.040><c> up</c><00:03:49.720><c> with</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
works so again to sum it up with
 

00:03:50.159 --> 00:03:52.350 align:start position:0%
works so again to sum it up with
autonomous<00:03:50.680><c> rag</c><00:03:51.280><c> the</c><00:03:51.400><c> model</c><00:03:51.720><c> decides</c><00:03:52.239><c> to</c>

00:03:52.350 --> 00:03:52.360 align:start position:0%
autonomous rag the model decides to
 

00:03:52.360 --> 00:03:55.470 align:start position:0%
autonomous rag the model decides to
search<00:03:52.599><c> its</c><00:03:52.799><c> memory</c><00:03:53.439><c> knowledge</c><00:03:54.000><c> or</c><00:03:54.480><c> tools</c>

00:03:55.470 --> 00:03:55.480 align:start position:0%
search its memory knowledge or tools
 

00:03:55.480 --> 00:03:57.670 align:start position:0%
search its memory knowledge or tools
let's<00:03:55.640><c> see</c><00:03:55.799><c> what</c><00:03:55.920><c> happens</c><00:03:56.200><c> behind</c><00:03:56.439><c> the</c><00:03:56.680><c> scenes</c>

00:03:57.670 --> 00:03:57.680 align:start position:0%
let's see what happens behind the scenes
 

00:03:57.680 --> 00:04:00.309 align:start position:0%
let's see what happens behind the scenes
behind<00:03:58.040><c> the</c><00:03:58.200><c> scenes</c>

00:04:00.309 --> 00:04:00.319 align:start position:0%
behind the scenes
 

00:04:00.319 --> 00:04:04.190 align:start position:0%
behind the scenes
our<00:04:00.799><c> application</c><00:04:01.680><c> is</c><00:04:02.680><c> running</c><00:04:03.120><c> it</c><00:04:03.319><c> by</c><00:04:03.519><c> this</c>

00:04:04.190 --> 00:04:04.200 align:start position:0%
our application is running it by this
 

00:04:04.200 --> 00:04:08.509 align:start position:0%
our application is running it by this
app.<00:04:05.200><c> P</c><00:04:05.560><c> py</c><00:04:05.959><c> file</c><00:04:06.760><c> which</c><00:04:07.000><c> is</c><00:04:07.680><c> the</c><00:04:07.840><c> streamlet</c>

00:04:08.509 --> 00:04:08.519 align:start position:0%
app. P py file which is the streamlet
 

00:04:08.519 --> 00:04:10.750 align:start position:0%
app. P py file which is the streamlet
application<00:04:09.519><c> which</c><00:04:09.720><c> is</c><00:04:09.920><c> running</c><00:04:10.480><c> the</c>

00:04:10.750 --> 00:04:10.760 align:start position:0%
application which is running the
 

00:04:10.760 --> 00:04:13.550 align:start position:0%
application which is running the
assistant<00:04:11.599><c> from</c><00:04:11.840><c> the</c><00:04:12.040><c> assistant</c><00:04:12.480><c> file</c><00:04:13.439><c> this</c>

00:04:13.550 --> 00:04:13.560 align:start position:0%
assistant from the assistant file this
 

00:04:13.560 --> 00:04:15.990 align:start position:0%
assistant from the assistant file this
is<00:04:13.720><c> where</c><00:04:13.879><c> the</c><00:04:14.040><c> magic</c><00:04:14.400><c> happens</c><00:04:15.400><c> so</c><00:04:15.599><c> the</c><00:04:15.760><c> first</c>

00:04:15.990 --> 00:04:16.000 align:start position:0%
is where the magic happens so the first
 

00:04:16.000 --> 00:04:18.150 align:start position:0%
is where the magic happens so the first
we'll<00:04:16.199><c> see</c><00:04:16.479><c> it</c><00:04:16.600><c> takes</c><00:04:16.759><c> the</c><00:04:16.880><c> embeddings</c><00:04:17.359><c> model</c>

00:04:18.150 --> 00:04:18.160 align:start position:0%
we'll see it takes the embeddings model
 

00:04:18.160 --> 00:04:20.629 align:start position:0%
we'll see it takes the embeddings model
it<00:04:18.280><c> takes</c><00:04:18.560><c> the</c><00:04:18.959><c> llm</c><00:04:19.680><c> it</c><00:04:19.799><c> shouldn't</c><00:04:20.079><c> be</c><00:04:20.239><c> llm</c>

00:04:20.629 --> 00:04:20.639 align:start position:0%
it takes the llm it shouldn't be llm
 

00:04:20.639 --> 00:04:23.070 align:start position:0%
it takes the llm it shouldn't be llm
model<00:04:20.919><c> is</c><00:04:21.199><c> just</c><00:04:21.560><c> should</c><00:04:21.759><c> be</c><00:04:21.919><c> llm</c><00:04:22.400><c> ID</c><00:04:22.680><c> or</c><00:04:22.880><c> name</c>

00:04:23.070 --> 00:04:23.080 align:start position:0%
model is just should be llm ID or name
 

00:04:23.080 --> 00:04:24.030 align:start position:0%
model is just should be llm ID or name
or

00:04:24.030 --> 00:04:24.040 align:start position:0%
or
 

00:04:24.040 --> 00:04:28.029 align:start position:0%
or
something<00:04:25.040><c> and</c><00:04:25.680><c> then</c><00:04:26.680><c> it</c><00:04:26.919><c> creates</c><00:04:27.320><c> an</c>

00:04:28.029 --> 00:04:28.039 align:start position:0%
something and then it creates an
 

00:04:28.039 --> 00:04:30.350 align:start position:0%
something and then it creates an
assistant<00:04:29.039><c> where</c><00:04:29.199><c> we</c><00:04:29.320><c> give</c><00:04:29.440><c> it</c><00:04:29.720><c> description</c>

00:04:30.350 --> 00:04:30.360 align:start position:0%
assistant where we give it description
 

00:04:30.360 --> 00:04:33.270 align:start position:0%
assistant where we give it description
instructions<00:04:31.000><c> and</c><00:04:31.160><c> a</c><00:04:31.360><c> bunch</c><00:04:31.600><c> of</c><00:04:31.800><c> tools</c><00:04:32.280><c> to</c>

00:04:33.270 --> 00:04:33.280 align:start position:0%
instructions and a bunch of tools to
 

00:04:33.280 --> 00:04:35.990 align:start position:0%
instructions and a bunch of tools to
access<00:04:33.600><c> the</c><00:04:33.720><c> memory</c><00:04:34.120><c> knowledge</c><00:04:34.600><c> or</c><00:04:35.440><c> API</c><00:04:35.840><c> of</c>

00:04:35.990 --> 00:04:36.000 align:start position:0%
access the memory knowledge or API of
 

00:04:36.000 --> 00:04:37.310 align:start position:0%
access the memory knowledge or API of
the<00:04:36.080><c> internet</c><00:04:36.479><c> we'll</c><00:04:36.680><c> give</c><00:04:36.840><c> it</c><00:04:37.039><c> bunch</c><00:04:37.199><c> of</c>

00:04:37.310 --> 00:04:37.320 align:start position:0%
the internet we'll give it bunch of
 

00:04:37.320 --> 00:04:40.350 align:start position:0%
the internet we'll give it bunch of
tools<00:04:38.160><c> so</c><00:04:38.320><c> the</c><00:04:38.520><c> description</c><00:04:39.039><c> is</c><00:04:39.919><c> you're</c><00:04:40.160><c> an</c>

00:04:40.350 --> 00:04:40.360 align:start position:0%
tools so the description is you're an
 

00:04:40.360 --> 00:04:42.070 align:start position:0%
tools so the description is you're an
assistant<00:04:41.120><c> that</c><00:04:41.320><c> answer</c><00:04:41.680><c> question</c><00:04:41.960><c> by</c>

00:04:42.070 --> 00:04:42.080 align:start position:0%
assistant that answer question by
 

00:04:42.080 --> 00:04:43.310 align:start position:0%
assistant that answer question by
calling<00:04:42.400><c> function</c><00:04:42.759><c> so</c><00:04:42.840><c> we're</c><00:04:43.000><c> letting</c><00:04:43.199><c> the</c>

00:04:43.310 --> 00:04:43.320 align:start position:0%
calling function so we're letting the
 

00:04:43.320 --> 00:04:45.670 align:start position:0%
calling function so we're letting the
model<00:04:43.639><c> know</c><00:04:44.639><c> that</c><00:04:44.759><c> to</c><00:04:44.919><c> answer</c><00:04:45.199><c> questions</c><00:04:45.520><c> you</c>

00:04:45.670 --> 00:04:45.680 align:start position:0%
model know that to answer questions you
 

00:04:45.680 --> 00:04:48.270 align:start position:0%
model know that to answer questions you
need<00:04:45.800><c> to</c><00:04:45.919><c> call</c><00:04:46.440><c> functions</c>

00:04:48.270 --> 00:04:48.280 align:start position:0%
need to call functions
 

00:04:48.280 --> 00:04:51.150 align:start position:0%
need to call functions
then<00:04:49.280><c> we're</c><00:04:49.639><c> guiding</c><00:04:50.080><c> it</c><00:04:50.360><c> through</c><00:04:50.600><c> a</c><00:04:50.720><c> set</c><00:04:50.919><c> of</c>

00:04:51.150 --> 00:04:51.160 align:start position:0%
then we're guiding it through a set of
 

00:04:51.160 --> 00:04:53.270 align:start position:0%
then we're guiding it through a set of
instructions<00:04:52.080><c> we're</c><00:04:52.320><c> telling</c><00:04:52.600><c> it</c><00:04:52.800><c> the</c><00:04:52.919><c> flow</c>

00:04:53.270 --> 00:04:53.280 align:start position:0%
instructions we're telling it the flow
 

00:04:53.280 --> 00:04:55.870 align:start position:0%
instructions we're telling it the flow
it<00:04:53.440><c> should</c><00:04:53.720><c> follow</c><00:04:54.720><c> we're</c><00:04:54.919><c> saying</c><00:04:55.240><c> first</c><00:04:55.560><c> get</c>

00:04:55.870 --> 00:04:55.880 align:start position:0%
it should follow we're saying first get
 

00:04:55.880 --> 00:04:57.830 align:start position:0%
it should follow we're saying first get
additional<00:04:56.360><c> information</c><00:04:56.800><c> about</c><00:04:57.000><c> the</c><00:04:57.080><c> user's</c>

00:04:57.830 --> 00:04:57.840 align:start position:0%
additional information about the user's
 

00:04:57.840 --> 00:05:00.189 align:start position:0%
additional information about the user's
question<00:04:58.840><c> you</c><00:04:58.960><c> can</c><00:04:59.240><c> either</c><00:04:59.440><c> use</c><00:04:59.800><c> a</c><00:04:59.919><c> search</c>

00:05:00.189 --> 00:05:00.199 align:start position:0%
question you can either use a search
 

00:05:00.199 --> 00:05:01.629 align:start position:0%
question you can either use a search
knowledge<00:05:00.520><c> based</c><00:05:00.759><c> tool</c><00:05:01.120><c> to</c><00:05:01.280><c> search</c><00:05:01.520><c> your</c>

00:05:01.629 --> 00:05:01.639 align:start position:0%
knowledge based tool to search your
 

00:05:01.639 --> 00:05:03.749 align:start position:0%
knowledge based tool to search your
knowledge<00:05:01.919><c> based</c><00:05:02.199><c> or</c><00:05:02.360><c> drct</c><00:05:02.759><c> Co</c><00:05:02.960><c> Search</c><00:05:03.520><c> tool</c>

00:05:03.749 --> 00:05:03.759 align:start position:0%
knowledge based or drct Co Search tool
 

00:05:03.759 --> 00:05:05.189 align:start position:0%
knowledge based or drct Co Search tool
to<00:05:03.880><c> search</c><00:05:04.199><c> the</c><00:05:04.320><c> internet</c><00:05:04.680><c> so</c><00:05:04.800><c> we're</c><00:05:04.960><c> telling</c>

00:05:05.189 --> 00:05:05.199 align:start position:0%
to search the internet so we're telling
 

00:05:05.199 --> 00:05:06.909 align:start position:0%
to search the internet so we're telling
it<00:05:05.360><c> you</c><00:05:05.440><c> can</c><00:05:05.600><c> either</c><00:05:05.800><c> search</c><00:05:06.000><c> your</c><00:05:06.160><c> knowledge</c>

00:05:06.909 --> 00:05:06.919 align:start position:0%
it you can either search your knowledge
 

00:05:06.919 --> 00:05:08.430 align:start position:0%
it you can either search your knowledge
or<00:05:07.120><c> search</c><00:05:07.400><c> the</c><00:05:07.520><c> internet</c><00:05:07.960><c> over</c><00:05:08.160><c> here</c><00:05:08.280><c> we'll</c>

00:05:08.430 --> 00:05:08.440 align:start position:0%
or search the internet over here we'll
 

00:05:08.440 --> 00:05:10.670 align:start position:0%
or search the internet over here we'll
just<00:05:08.600><c> play</c><00:05:08.759><c> with</c><00:05:08.880><c> a</c><00:05:09.000><c> few</c><00:05:09.199><c> tools</c><00:05:09.560><c> here</c><00:05:10.160><c> you</c><00:05:10.280><c> can</c>

00:05:10.670 --> 00:05:10.680 align:start position:0%
just play with a few tools here you can
 

00:05:10.680 --> 00:05:13.390 align:start position:0%
just play with a few tools here you can
add<00:05:11.000><c> tools</c><00:05:11.360><c> to</c><00:05:12.240><c> to</c><00:05:12.400><c> search</c><00:05:12.720><c> your</c><00:05:12.960><c> favorite</c>

00:05:13.390 --> 00:05:13.400 align:start position:0%
add tools to to search your favorite
 

00:05:13.400 --> 00:05:15.670 align:start position:0%
add tools to to search your favorite
kind<00:05:13.520><c> of</c><00:05:13.800><c> to</c><00:05:13.919><c> make</c><00:05:14.199><c> API</c><00:05:14.680><c> calls</c><00:05:15.000><c> here</c><00:05:15.160><c> to</c><00:05:15.320><c> send</c>

00:05:15.670 --> 00:05:15.680 align:start position:0%
kind of to make API calls here to send
 

00:05:15.680 --> 00:05:18.629 align:start position:0%
kind of to make API calls here to send
emails<00:05:16.160><c> here</c><00:05:16.600><c> to</c><00:05:17.039><c> query</c><00:05:17.360><c> a</c>

00:05:18.629 --> 00:05:18.639 align:start position:0%
emails here to query a
 

00:05:18.639 --> 00:05:20.790 align:start position:0%
emails here to query a
database<00:05:19.639><c> now</c><00:05:19.800><c> we're</c><00:05:19.960><c> saying</c><00:05:20.319><c> if</c><00:05:20.440><c> the</c><00:05:20.560><c> user</c>

00:05:20.790 --> 00:05:20.800 align:start position:0%
database now we're saying if the user
 

00:05:20.800 --> 00:05:22.230 align:start position:0%
database now we're saying if the user
asked<00:05:21.039><c> a</c><00:05:21.160><c> question</c><00:05:21.360><c> about</c><00:05:21.560><c> current</c><00:05:21.840><c> events</c><00:05:22.120><c> so</c>

00:05:22.230 --> 00:05:22.240 align:start position:0%
asked a question about current events so
 

00:05:22.240 --> 00:05:24.390 align:start position:0%
asked a question about current events so
if<00:05:22.360><c> we</c><00:05:22.479><c> ask</c><00:05:22.720><c> what's</c><00:05:22.880><c> the</c><00:05:23.000><c> latest</c><00:05:23.280><c> about</c><00:05:24.080><c> uh</c>

00:05:24.390 --> 00:05:24.400 align:start position:0%
if we ask what's the latest about uh
 

00:05:24.400 --> 00:05:26.270 align:start position:0%
if we ask what's the latest about uh
Lama<00:05:24.759><c> 3</c><00:05:25.120><c> or</c><00:05:25.319><c> what's</c><00:05:25.479><c> happening</c><00:05:25.759><c> in</c><00:05:25.840><c> France</c><00:05:26.120><c> so</c>

00:05:26.270 --> 00:05:26.280 align:start position:0%
Lama 3 or what's happening in France so
 

00:05:26.280 --> 00:05:28.189 align:start position:0%
Lama 3 or what's happening in France so
that's<00:05:26.440><c> a</c><00:05:26.639><c> current</c><00:05:26.960><c> event</c><00:05:27.319><c> question</c><00:05:27.840><c> use</c><00:05:28.039><c> the</c>

00:05:28.189 --> 00:05:28.199 align:start position:0%
that's a current event question use the
 

00:05:28.199 --> 00:05:30.670 align:start position:0%
that's a current event question use the
DU<00:05:28.600><c> Co</c><00:05:28.720><c> Search</c><00:05:29.000><c> tool</c><00:05:29.960><c> otherwise</c><00:05:30.400><c> it</c><00:05:30.520><c> would</c>

00:05:30.670 --> 00:05:30.680 align:start position:0%
DU Co Search tool otherwise it would
 

00:05:30.680 --> 00:05:32.670 align:start position:0%
DU Co Search tool otherwise it would
search<00:05:30.919><c> its</c><00:05:31.080><c> knowledge</c><00:05:31.400><c> base</c><00:05:31.600><c> for</c><00:05:31.800><c> that</c><00:05:32.560><c> if</c>

00:05:32.670 --> 00:05:32.680 align:start position:0%
search its knowledge base for that if
 

00:05:32.680 --> 00:05:33.990 align:start position:0%
search its knowledge base for that if
the<00:05:32.800><c> user</c><00:05:33.120><c> asked</c><00:05:33.319><c> to</c><00:05:33.440><c> summarize</c><00:05:33.880><c> the</c>

00:05:33.990 --> 00:05:34.000 align:start position:0%
the user asked to summarize the
 

00:05:34.000 --> 00:05:36.110 align:start position:0%
the user asked to summarize the
conversation<00:05:34.759><c> use</c><00:05:35.080><c> get</c><00:05:35.280><c> chat</c><00:05:35.560><c> history</c><00:05:35.840><c> to</c><00:05:36.000><c> get</c>

00:05:36.110 --> 00:05:36.120 align:start position:0%
conversation use get chat history to get
 

00:05:36.120 --> 00:05:37.870 align:start position:0%
conversation use get chat history to get
the<00:05:36.199><c> chat</c><00:05:36.440><c> history</c><00:05:36.720><c> with</c><00:05:36.800><c> the</c><00:05:36.919><c> user</c><00:05:37.520><c> so</c>

00:05:37.870 --> 00:05:37.880 align:start position:0%
the chat history with the user so
 

00:05:37.880 --> 00:05:40.510 align:start position:0%
the chat history with the user so
describe<00:05:38.280><c> a</c><00:05:38.400><c> few</c><00:05:38.639><c> use</c><00:05:39.000><c> cases</c><00:05:39.800><c> and</c><00:05:40.120><c> that's</c><00:05:40.319><c> how</c>

00:05:40.510 --> 00:05:40.520 align:start position:0%
describe a few use cases and that's how
 

00:05:40.520 --> 00:05:42.469 align:start position:0%
describe a few use cases and that's how
we<00:05:40.759><c> guide</c><00:05:41.080><c> the</c><00:05:41.199><c> model</c><00:05:41.520><c> we</c><00:05:41.639><c> stare</c><00:05:41.960><c> the</c><00:05:42.039><c> model</c><00:05:42.280><c> on</c>

00:05:42.469 --> 00:05:42.479 align:start position:0%
we guide the model we stare the model on
 

00:05:42.479 --> 00:05:43.350 align:start position:0%
we guide the model we stare the model on
what<00:05:42.600><c> to</c>

00:05:43.350 --> 00:05:43.360 align:start position:0%
what to
 

00:05:43.360 --> 00:05:46.590 align:start position:0%
what to
do<00:05:44.360><c> then</c><00:05:44.479><c> we</c><00:05:44.600><c> say</c><00:05:44.880><c> process</c><00:05:45.240><c> this</c><00:05:45.600><c> information</c>

00:05:46.590 --> 00:05:46.600 align:start position:0%
do then we say process this information
 

00:05:46.600 --> 00:05:48.230 align:start position:0%
do then we say process this information
and<00:05:46.800><c> provide</c><00:05:47.080><c> a</c><00:05:47.199><c> clear</c><00:05:47.440><c> and</c><00:05:47.600><c> concise</c><00:05:48.000><c> answer</c>

00:05:48.230 --> 00:05:48.240 align:start position:0%
and provide a clear and concise answer
 

00:05:48.240 --> 00:05:49.950 align:start position:0%
and provide a clear and concise answer
to<00:05:48.360><c> the</c><00:05:48.440><c> user</c><00:05:48.800><c> so</c><00:05:49.000><c> we</c><00:05:49.120><c> don't</c><00:05:49.360><c> want</c><00:05:49.560><c> it</c><00:05:49.639><c> to</c><00:05:49.759><c> give</c>

00:05:49.950 --> 00:05:49.960 align:start position:0%
to the user so we don't want it to give
 

00:05:49.960 --> 00:05:51.550 align:start position:0%
to the user so we don't want it to give
like<00:05:50.120><c> long</c><00:05:50.360><c> lengthy</c><00:05:50.759><c> answer</c><00:05:51.080><c> we</c><00:05:51.160><c> want</c><00:05:51.319><c> it</c><00:05:51.440><c> to</c>

00:05:51.550 --> 00:05:51.560 align:start position:0%
like long lengthy answer we want it to
 

00:05:51.560 --> 00:05:53.749 align:start position:0%
like long lengthy answer we want it to
be<00:05:51.720><c> clear</c><00:05:52.039><c> concise</c><00:05:52.400><c> to</c><00:05:52.520><c> the</c><00:05:52.680><c> point</c><00:05:53.400><c> and</c><00:05:53.479><c> then</c>

00:05:53.749 --> 00:05:53.759 align:start position:0%
be clear concise to the point and then
 

00:05:53.759 --> 00:05:56.350 align:start position:0%
be clear concise to the point and then
finally<00:05:54.600><c> I've</c><00:05:54.800><c> noticed</c><00:05:55.120><c> that</c><00:05:55.280><c> a</c><00:05:55.400><c> lot</c><00:05:55.520><c> of</c><00:05:55.720><c> times</c>

00:05:56.350 --> 00:05:56.360 align:start position:0%
finally I've noticed that a lot of times
 

00:05:56.360 --> 00:05:58.710 align:start position:0%
finally I've noticed that a lot of times
Lama<00:05:56.720><c> 3</c><00:05:56.919><c> will</c><00:05:57.120><c> say</c><00:05:57.520><c> here's</c><00:05:57.800><c> the</c><00:05:57.960><c> answer</c><00:05:58.440><c> or</c>

00:05:58.710 --> 00:05:58.720 align:start position:0%
Lama 3 will say here's the answer or
 

00:05:58.720 --> 00:06:01.629 align:start position:0%
Lama 3 will say here's the answer or
according<00:05:59.039><c> to</c><00:05:59.160><c> the</c><00:05:59.280><c> information</c><00:05:59.919><c> provided</c><00:06:00.639><c> or</c>

00:06:01.629 --> 00:06:01.639 align:start position:0%
according to the information provided or
 

00:06:01.639 --> 00:06:02.990 align:start position:0%
according to the information provided or
according<00:06:01.919><c> to</c><00:06:02.039><c> the</c><00:06:02.120><c> search</c><00:06:02.400><c> knowledge</c><00:06:02.680><c> base</c>

00:06:02.990 --> 00:06:03.000 align:start position:0%
according to the search knowledge base
 

00:06:03.000 --> 00:06:05.870 align:start position:0%
according to the search knowledge base
tool<00:06:04.000><c> we</c><00:06:04.120><c> want</c><00:06:04.240><c> to</c><00:06:04.360><c> give</c><00:06:04.479><c> it</c><00:06:04.680><c> instructions</c><00:06:05.319><c> to</c>

00:06:05.870 --> 00:06:05.880 align:start position:0%
tool we want to give it instructions to
 

00:06:05.880 --> 00:06:07.950 align:start position:0%
tool we want to give it instructions to
not<00:06:06.240><c> say</c><00:06:06.560><c> that</c><00:06:06.919><c> and</c><00:06:07.120><c> just</c><00:06:07.319><c> respond</c><00:06:07.680><c> directly</c>

00:06:07.950 --> 00:06:07.960 align:start position:0%
not say that and just respond directly
 

00:06:07.960 --> 00:06:10.629 align:start position:0%
not say that and just respond directly
to<00:06:08.080><c> the</c><00:06:08.400><c> user</c><00:06:09.400><c> then</c><00:06:09.599><c> we</c><00:06:09.800><c> going</c><00:06:09.919><c> to</c><00:06:10.120><c> say</c><00:06:10.360><c> show</c>

00:06:10.629 --> 00:06:10.639 align:start position:0%
to the user then we going to say show
 

00:06:10.639 --> 00:06:12.430 align:start position:0%
to the user then we going to say show
tool<00:06:10.919><c> calls</c><00:06:11.240><c> so</c><00:06:11.479><c> so</c><00:06:11.639><c> we</c><00:06:11.720><c> want</c><00:06:11.840><c> to</c><00:06:11.960><c> see</c><00:06:12.120><c> the</c><00:06:12.240><c> tool</c>

00:06:12.430 --> 00:06:12.440 align:start position:0%
tool calls so so we want to see the tool
 

00:06:12.440 --> 00:06:13.629 align:start position:0%
tool calls so so we want to see the tool
calls<00:06:12.680><c> that's</c><00:06:12.919><c> happening</c><00:06:13.400><c> if</c><00:06:13.520><c> you're</c>

00:06:13.629 --> 00:06:13.639 align:start position:0%
calls that's happening if you're
 

00:06:13.639 --> 00:06:15.029 align:start position:0%
calls that's happening if you're
building<00:06:13.880><c> an</c><00:06:14.039><c> application</c><00:06:14.479><c> with</c><00:06:14.639><c> this</c><00:06:14.800><c> you'll</c>

00:06:15.029 --> 00:06:15.039 align:start position:0%
building an application with this you'll
 

00:06:15.039 --> 00:06:18.950 align:start position:0%
building an application with this you'll
probably<00:06:15.599><c> not</c><00:06:15.800><c> use</c><00:06:16.479><c> this</c><00:06:17.680><c> then</c><00:06:18.680><c> we're</c><00:06:18.840><c> going</c>

00:06:18.950 --> 00:06:18.960 align:start position:0%
probably not use this then we're going
 

00:06:18.960 --> 00:06:21.270 align:start position:0%
probably not use this then we're going
to<00:06:19.599><c> add</c><00:06:19.880><c> a</c><00:06:20.080><c> default</c><00:06:20.520><c> function</c><00:06:20.759><c> to</c><00:06:20.880><c> search</c><00:06:21.160><c> the</c>

00:06:21.270 --> 00:06:21.280 align:start position:0%
to add a default function to search the
 

00:06:21.280 --> 00:06:22.909 align:start position:0%
to add a default function to search the
knowledge<00:06:21.599><c> base</c><00:06:21.880><c> so</c><00:06:22.080><c> if</c><00:06:22.160><c> you're</c><00:06:22.440><c> adding</c><00:06:22.759><c> a</c>

00:06:22.909 --> 00:06:22.919 align:start position:0%
knowledge base so if you're adding a
 

00:06:22.919 --> 00:06:25.270 align:start position:0%
knowledge base so if you're adding a
knowledge<00:06:23.280><c> base</c><00:06:23.800><c> you</c><00:06:23.919><c> can</c><00:06:24.120><c> give</c><00:06:24.440><c> any</c><00:06:24.680><c> F</c><00:06:25.000><c> data</c>

00:06:25.270 --> 00:06:25.280 align:start position:0%
knowledge base you can give any F data
 

00:06:25.280 --> 00:06:27.430 align:start position:0%
knowledge base you can give any F data
assistant<00:06:25.960><c> search</c><00:06:26.319><c> knowledge</c><00:06:26.759><c> true</c><00:06:27.319><c> and</c>

00:06:27.430 --> 00:06:27.440 align:start position:0%
assistant search knowledge true and
 

00:06:27.440 --> 00:06:29.589 align:start position:0%
assistant search knowledge true and
it'll<00:06:27.720><c> add</c><00:06:28.000><c> a</c><00:06:28.199><c> function</c><00:06:28.599><c> call</c><00:06:28.880><c> to</c><00:06:29.120><c> search</c>

00:06:29.589 --> 00:06:29.599 align:start position:0%
it'll add a function call to search
 

00:06:29.599 --> 00:06:31.990 align:start position:0%
it'll add a function call to search
knowledge<00:06:29.880><c> base</c><00:06:30.840><c> then</c><00:06:31.000><c> you</c><00:06:31.120><c> can</c><00:06:31.720><c> give</c>

00:06:31.990 --> 00:06:32.000 align:start position:0%
knowledge base then you can give
 

00:06:32.000 --> 00:06:33.710 align:start position:0%
knowledge base then you can give
similarly<00:06:32.440><c> you</c><00:06:32.520><c> can</c><00:06:32.639><c> say</c><00:06:32.840><c> read</c><00:06:33.080><c> chart</c><00:06:33.360><c> history</c>

00:06:33.710 --> 00:06:33.720 align:start position:0%
similarly you can say read chart history
 

00:06:33.720 --> 00:06:35.589 align:start position:0%
similarly you can say read chart history
as<00:06:33.919><c> true</c><00:06:34.199><c> as</c><00:06:34.319><c> well</c><00:06:34.560><c> each</c><00:06:34.720><c> five</c><00:06:35.000><c> dat</c><00:06:35.240><c> assistant</c>

00:06:35.589 --> 00:06:35.599 align:start position:0%
as true as well each five dat assistant
 

00:06:35.599 --> 00:06:37.670 align:start position:0%
as true as well each five dat assistant
comes<00:06:35.800><c> with</c><00:06:36.000><c> built-in</c><00:06:36.360><c> memory</c><00:06:37.280><c> that</c><00:06:37.400><c> you</c><00:06:37.560><c> can</c>

00:06:37.670 --> 00:06:37.680 align:start position:0%
comes with built-in memory that you can
 

00:06:37.680 --> 00:06:40.710 align:start position:0%
comes with built-in memory that you can
store<00:06:37.919><c> to</c><00:06:38.039><c> a</c><00:06:38.160><c> database</c><00:06:38.599><c> using</c><00:06:39.319><c> a</c><00:06:40.120><c> uh</c><00:06:40.199><c> using</c><00:06:40.520><c> the</c>

00:06:40.710 --> 00:06:40.720 align:start position:0%
store to a database using a uh using the
 

00:06:40.720 --> 00:06:43.350 align:start position:0%
store to a database using a uh using the
storage<00:06:41.240><c> class</c><00:06:42.240><c> but</c><00:06:42.400><c> built-in</c><00:06:42.800><c> memory</c><00:06:43.120><c> is</c>

00:06:43.350 --> 00:06:43.360 align:start position:0%
storage class but built-in memory is
 

00:06:43.360 --> 00:06:45.350 align:start position:0%
storage class but built-in memory is
always<00:06:43.800><c> there</c><00:06:44.199><c> which</c><00:06:44.360><c> is</c><00:06:44.880><c> um</c><00:06:45.120><c> which</c><00:06:45.199><c> is</c>

00:06:45.350 --> 00:06:45.360 align:start position:0%
always there which is um which is
 

00:06:45.360 --> 00:06:47.510 align:start position:0%
always there which is um which is
available<00:06:45.759><c> through</c><00:06:45.919><c> the</c><00:06:46.080><c> session</c><00:06:46.840><c> so</c><00:06:47.160><c> this</c>

00:06:47.510 --> 00:06:47.520 align:start position:0%
available through the session so this
 

00:06:47.520 --> 00:06:49.230 align:start position:0%
available through the session so this
read<00:06:47.840><c> chat</c><00:06:48.080><c> history</c><00:06:48.400><c> adds</c><00:06:48.599><c> a</c><00:06:48.759><c> function</c><00:06:49.039><c> to</c>

00:06:49.230 --> 00:06:49.240 align:start position:0%
read chat history adds a function to
 

00:06:49.240 --> 00:06:51.350 align:start position:0%
read chat history adds a function to
read<00:06:49.560><c> the</c><00:06:49.720><c> assistant</c><00:06:50.120><c> memory</c><00:06:51.120><c> and</c><00:06:51.199><c> then</c>

00:06:51.350 --> 00:06:51.360 align:start position:0%
read the assistant memory and then
 

00:06:51.360 --> 00:06:53.589 align:start position:0%
read the assistant memory and then
finally<00:06:51.639><c> be</c><00:06:51.800><c> giving</c><00:06:52.000><c> a</c><00:06:52.160><c> duct</c><00:06:52.520><c> go</c><00:06:53.039><c> tool</c><00:06:53.319><c> but</c><00:06:53.440><c> you</c>

00:06:53.589 --> 00:06:53.599 align:start position:0%
finally be giving a duct go tool but you
 

00:06:53.599 --> 00:06:56.830 align:start position:0%
finally be giving a duct go tool but you
can<00:06:53.880><c> give</c><00:06:54.080><c> a</c><00:06:54.199><c> tool</c><00:06:54.400><c> to</c><00:06:54.639><c> query</c><00:06:55.360><c> Yahoo</c><00:06:55.840><c> finance</c>

00:06:56.830 --> 00:06:56.840 align:start position:0%
can give a tool to query Yahoo finance
 

00:06:56.840 --> 00:06:59.909 align:start position:0%
can give a tool to query Yahoo finance
open<00:06:57.160><c> bb</c><00:06:58.080><c> or</c><00:06:58.479><c> any</c><00:06:58.680><c> of</c><00:06:58.840><c> the</c><00:06:59.199><c> hundreds</c><00:06:59.599><c> of</c><00:06:59.720><c> tools</c>

00:06:59.909 --> 00:06:59.919 align:start position:0%
open bb or any of the hundreds of tools
 

00:06:59.919 --> 00:07:01.869 align:start position:0%
open bb or any of the hundreds of tools
which<00:07:00.080><c> fire</c><00:07:00.280><c> data</c><00:07:00.560><c> supports</c><00:07:01.560><c> and</c><00:07:01.680><c> then</c>

00:07:01.869 --> 00:07:01.879 align:start position:0%
which fire data supports and then
 

00:07:01.879 --> 00:07:04.350 align:start position:0%
which fire data supports and then
finally<00:07:02.360><c> we</c><00:07:02.479><c> are</c><00:07:03.039><c> adding</c><00:07:03.400><c> some</c><00:07:03.639><c> chat</c><00:07:03.879><c> hiss</c><00:07:04.240><c> to</c>

00:07:04.350 --> 00:07:04.360 align:start position:0%
finally we are adding some chat hiss to
 

00:07:04.360 --> 00:07:05.710 align:start position:0%
finally we are adding some chat hiss to
messages<00:07:04.720><c> you</c><00:07:04.800><c> can</c><00:07:04.919><c> turn</c><00:07:05.120><c> these</c><00:07:05.280><c> off</c><00:07:05.479><c> if</c><00:07:05.599><c> you</c>

00:07:05.710 --> 00:07:05.720 align:start position:0%
messages you can turn these off if you
 

00:07:05.720 --> 00:07:08.629 align:start position:0%
messages you can turn these off if you
like<00:07:05.919><c> to</c><00:07:06.160><c> just</c><00:07:06.280><c> save</c><00:07:06.520><c> on</c><00:07:06.879><c> context</c><00:07:07.879><c> but</c><00:07:08.319><c> given</c>

00:07:08.629 --> 00:07:08.639 align:start position:0%
like to just save on context but given
 

00:07:08.639 --> 00:07:10.510 align:start position:0%
like to just save on context but given
this<00:07:08.840><c> assistant</c><00:07:09.400><c> now</c><00:07:09.879><c> let's</c><00:07:10.080><c> ask</c><00:07:10.319><c> some</c>

00:07:10.510 --> 00:07:10.520 align:start position:0%
this assistant now let's ask some
 

00:07:10.520 --> 00:07:13.710 align:start position:0%
this assistant now let's ask some
questions<00:07:11.240><c> again</c><00:07:11.440><c> we</c><00:07:11.680><c> ask</c><00:07:12.599><c> what</c><00:07:12.759><c> did</c><00:07:12.960><c> meta</c>

00:07:13.710 --> 00:07:13.720 align:start position:0%
questions again we ask what did meta
 

00:07:13.720 --> 00:07:15.790 align:start position:0%
questions again we ask what did meta
release<00:07:14.720><c> and</c><00:07:14.879><c> after</c><00:07:15.120><c> this</c><00:07:15.240><c> I'm</c><00:07:15.319><c> going</c><00:07:15.440><c> to</c><00:07:15.599><c> ask</c>

00:07:15.790 --> 00:07:15.800 align:start position:0%
release and after this I'm going to ask
 

00:07:15.800 --> 00:07:18.270 align:start position:0%
release and after this I'm going to ask
a<00:07:15.960><c> question</c><00:07:16.280><c> which</c><00:07:16.520><c> should</c><00:07:16.960><c> probably</c><00:07:17.560><c> break</c>

00:07:18.270 --> 00:07:18.280 align:start position:0%
a question which should probably break
 

00:07:18.280 --> 00:07:21.550 align:start position:0%
a question which should probably break
the<00:07:18.440><c> flow</c><00:07:18.919><c> so</c><00:07:19.599><c> it's</c><00:07:19.759><c> not</c><00:07:20.039><c> all</c><00:07:20.680><c> like</c><00:07:20.919><c> you</c><00:07:21.080><c> know</c>

00:07:21.550 --> 00:07:21.560 align:start position:0%
the flow so it's not all like you know
 

00:07:21.560 --> 00:07:23.869 align:start position:0%
the flow so it's not all like you know
um<00:07:21.840><c> we're</c><00:07:22.000><c> going</c><00:07:22.120><c> to</c><00:07:22.240><c> go</c><00:07:22.440><c> according</c><00:07:23.080><c> to</c><00:07:23.680><c> we're</c>

00:07:23.869 --> 00:07:23.879 align:start position:0%
um we're going to go according to we're
 

00:07:23.879 --> 00:07:25.589 align:start position:0%
um we're going to go according to we're
only<00:07:24.080><c> going</c><00:07:24.160><c> to</c><00:07:24.280><c> see</c><00:07:24.440><c> the</c><00:07:24.599><c> good</c><00:07:24.840><c> questions</c>

00:07:25.589 --> 00:07:25.599 align:start position:0%
only going to see the good questions
 

00:07:25.599 --> 00:07:26.990 align:start position:0%
only going to see the good questions
we're<00:07:25.759><c> going</c><00:07:25.840><c> to</c><00:07:26.039><c> ask</c><00:07:26.199><c> a</c><00:07:26.400><c> question</c><00:07:26.680><c> that</c><00:07:26.800><c> will</c>

00:07:26.990 --> 00:07:27.000 align:start position:0%
we're going to ask a question that will
 

00:07:27.000 --> 00:07:29.869 align:start position:0%
we're going to ask a question that will
break<00:07:27.280><c> the</c><00:07:27.400><c> model</c><00:07:27.840><c> as</c><00:07:28.240><c> well</c><00:07:29.240><c> um</c><00:07:29.560><c> after</c><00:07:29.759><c> this</c>

00:07:29.869 --> 00:07:29.879 align:start position:0%
break the model as well um after this
 

00:07:29.879 --> 00:07:32.350 align:start position:0%
break the model as well um after this
we're<00:07:30.000><c> going</c><00:07:30.080><c> to</c><00:07:30.319><c> ask</c><00:07:30.759><c> okay</c><00:07:30.919><c> so</c><00:07:31.160><c> this</c><00:07:31.280><c> one</c><00:07:31.720><c> it's</c>

00:07:32.350 --> 00:07:32.360 align:start position:0%
we're going to ask okay so this one it's
 

00:07:32.360 --> 00:07:34.270 align:start position:0%
we're going to ask okay so this one it's
a<00:07:32.560><c> good</c><00:07:32.800><c> answer</c><00:07:33.280><c> we've</c><00:07:33.560><c> told</c><00:07:33.879><c> okay</c><00:07:34.080><c> search</c>

00:07:34.270 --> 00:07:34.280 align:start position:0%
a good answer we've told okay search
 

00:07:34.280 --> 00:07:36.309 align:start position:0%
a good answer we've told okay search
your<00:07:34.440><c> knowledge</c><00:07:34.759><c> base</c><00:07:35.440><c> for</c><00:07:35.680><c> this</c><00:07:35.879><c> information</c>

00:07:36.309 --> 00:07:36.319 align:start position:0%
your knowledge base for this information
 

00:07:36.319 --> 00:07:40.189 align:start position:0%
your knowledge base for this information
now<00:07:36.440><c> we're</c><00:07:36.560><c> going</c><00:07:36.639><c> to</c><00:07:37.319><c> ask</c><00:07:38.319><c> what's</c><00:07:38.599><c> the</c><00:07:39.199><c> latest</c>

00:07:40.189 --> 00:07:40.199 align:start position:0%
now we're going to ask what's the latest
 

00:07:40.199 --> 00:07:43.390 align:start position:0%
now we're going to ask what's the latest
or<00:07:41.080><c> tell</c><00:07:41.319><c> me</c><00:07:41.599><c> more</c>

00:07:43.390 --> 00:07:43.400 align:start position:0%
or tell me more
 

00:07:43.400 --> 00:07:45.790 align:start position:0%
or tell me more
about<00:07:44.400><c> the</c><00:07:44.599><c> Llama</c><00:07:44.960><c> 3</c>

00:07:45.790 --> 00:07:45.800 align:start position:0%
about the Llama 3
 

00:07:45.800 --> 00:07:48.869 align:start position:0%
about the Llama 3
models<00:07:46.800><c> so</c><00:07:46.960><c> I</c><00:07:47.039><c> want</c><00:07:47.199><c> you</c><00:07:47.319><c> to</c><00:07:47.440><c> think</c><00:07:48.120><c> how</c><00:07:48.440><c> should</c>

00:07:48.869 --> 00:07:48.879 align:start position:0%
models so I want you to think how should
 

00:07:48.879 --> 00:07:51.390 align:start position:0%
models so I want you to think how should
the<00:07:49.479><c> assistant</c><00:07:49.960><c> respond</c><00:07:50.280><c> to</c><00:07:50.440><c> this</c><00:07:51.000><c> shall</c><00:07:51.240><c> it</c>

00:07:51.390 --> 00:07:51.400 align:start position:0%
the assistant respond to this shall it
 

00:07:51.400 --> 00:07:53.790 align:start position:0%
the assistant respond to this shall it
search<00:07:51.680><c> the</c><00:07:51.840><c> knowledge</c><00:07:52.159><c> base</c><00:07:52.479><c> or</c><00:07:52.720><c> the</c>

00:07:53.790 --> 00:07:53.800 align:start position:0%
search the knowledge base or the
 

00:07:53.800 --> 00:07:55.749 align:start position:0%
search the knowledge base or the
internet<00:07:54.800><c> I</c><00:07:54.960><c> think</c><00:07:55.080><c> it</c><00:07:55.199><c> should</c><00:07:55.360><c> search</c><00:07:55.639><c> the</c>

00:07:55.749 --> 00:07:55.759 align:start position:0%
internet I think it should search the
 

00:07:55.759 --> 00:07:57.230 align:start position:0%
internet I think it should search the
knowledge<00:07:56.039><c> base</c><00:07:56.240><c> because</c><00:07:56.440><c> it</c><00:07:56.599><c> already</c><00:07:57.039><c> has</c>

00:07:57.230 --> 00:07:57.240 align:start position:0%
knowledge base because it already has
 

00:07:57.240 --> 00:07:58.790 align:start position:0%
knowledge base because it already has
the<00:07:57.400><c> documents</c><00:07:57.840><c> that's</c><00:07:58.000><c> what</c><00:07:58.159><c> I</c><00:07:58.240><c> would</c><00:07:58.440><c> expect</c>

00:07:58.790 --> 00:07:58.800 align:start position:0%
the documents that's what I would expect
 

00:07:58.800 --> 00:08:00.189 align:start position:0%
the documents that's what I would expect
as<00:07:58.919><c> a</c><00:07:59.080><c> user</c>

00:08:00.189 --> 00:08:00.199 align:start position:0%
as a user
 

00:08:00.199 --> 00:08:02.270 align:start position:0%
as a user
but<00:08:00.360><c> I</c><00:08:00.440><c> would</c><00:08:00.599><c> be</c><00:08:01.400><c> you</c><00:08:01.520><c> know</c><00:08:01.759><c> I</c><00:08:01.840><c> would</c>

00:08:02.270 --> 00:08:02.280 align:start position:0%
but I would be you know I would
 

00:08:02.280 --> 00:08:03.869 align:start position:0%
but I would be you know I would
understand<00:08:02.440><c> if</c><00:08:02.599><c> it</c><00:08:02.800><c> searched</c><00:08:03.280><c> the</c><00:08:03.479><c> internet</c>

00:08:03.869 --> 00:08:03.879 align:start position:0%
understand if it searched the internet
 

00:08:03.879 --> 00:08:07.629 align:start position:0%
understand if it searched the internet
for<00:08:04.479><c> it</c><00:08:05.479><c> okay</c><00:08:06.479><c> it</c><00:08:06.680><c> searched</c><00:08:07.000><c> the</c><00:08:07.120><c> knowledge</c>

00:08:07.629 --> 00:08:07.639 align:start position:0%
for it okay it searched the knowledge
 

00:08:07.639 --> 00:08:09.270 align:start position:0%
for it okay it searched the knowledge
which

00:08:09.270 --> 00:08:09.280 align:start position:0%
which
 

00:08:09.280 --> 00:08:12.990 align:start position:0%
which
is<00:08:10.280><c> and</c><00:08:10.479><c> gave</c><00:08:10.680><c> me</c><00:08:10.840><c> a</c><00:08:10.960><c> very</c><00:08:11.159><c> good</c><00:08:11.599><c> answer</c><00:08:12.599><c> um</c>

00:08:12.990 --> 00:08:13.000 align:start position:0%
is and gave me a very good answer um
 

00:08:13.000 --> 00:08:15.189 align:start position:0%
is and gave me a very good answer um
with<00:08:13.199><c> key</c><00:08:13.440><c> points</c><00:08:14.080><c> that's</c><00:08:14.280><c> a</c><00:08:14.520><c> very</c><00:08:14.759><c> very</c><00:08:15.000><c> good</c>

00:08:15.189 --> 00:08:15.199 align:start position:0%
with key points that's a very very good
 

00:08:15.199 --> 00:08:17.550 align:start position:0%
with key points that's a very very good
answer<00:08:15.520><c> from</c><00:08:15.759><c> this</c><00:08:16.000><c> I</c><00:08:16.120><c> wasn't</c><00:08:16.599><c> expecting</c><00:08:16.960><c> it</c><00:08:17.400><c> I</c>

00:08:17.550 --> 00:08:17.560 align:start position:0%
answer from this I wasn't expecting it I
 

00:08:17.560 --> 00:08:19.110 align:start position:0%
answer from this I wasn't expecting it I
was<00:08:17.720><c> hoping</c><00:08:17.960><c> it</c><00:08:18.039><c> to</c><00:08:18.199><c> break</c><00:08:18.560><c> okay</c><00:08:18.759><c> now</c><00:08:18.919><c> let's</c>

00:08:19.110 --> 00:08:19.120 align:start position:0%
was hoping it to break okay now let's
 

00:08:19.120 --> 00:08:23.230 align:start position:0%
was hoping it to break okay now let's
try<00:08:19.440><c> something</c><00:08:20.440><c> that</c><00:08:20.639><c> says</c>

00:08:23.230 --> 00:08:23.240 align:start position:0%
try something that says
 

00:08:23.240 --> 00:08:28.990 align:start position:0%
try something that says
um<00:08:24.240><c> summarize</c><00:08:25.080><c> the</c><00:08:25.879><c> latest</c><00:08:26.759><c> news</c><00:08:27.680><c> about</c><00:08:28.400><c> llama</c>

00:08:28.990 --> 00:08:29.000 align:start position:0%
um summarize the latest news about llama
 

00:08:29.000 --> 00:08:30.110 align:start position:0%
um summarize the latest news about llama
3

00:08:30.110 --> 00:08:30.120 align:start position:0%
3
 

00:08:30.120 --> 00:08:37.190 align:start position:0%
3
now<00:08:30.520><c> now</c><00:08:30.680><c> let's</c><00:08:30.840><c> see</c><00:08:31.039><c> what</c>

00:08:37.190 --> 00:08:37.200 align:start position:0%
 
 

00:08:37.200 --> 00:08:39.990 align:start position:0%
 
happens<00:08:38.200><c> now</c><00:08:38.519><c> just</c><00:08:38.719><c> answerers</c><00:08:39.240><c> from</c><00:08:39.519><c> its</c><00:08:39.719><c> own</c>

00:08:39.990 --> 00:08:40.000 align:start position:0%
happens now just answerers from its own
 

00:08:40.000 --> 00:08:42.230 align:start position:0%
happens now just answerers from its own
knowledge<00:08:40.440><c> it</c><00:08:40.560><c> did</c><00:08:40.719><c> not</c><00:08:40.880><c> run</c><00:08:41.279><c> any</c><00:08:41.519><c> function</c>

00:08:42.230 --> 00:08:42.240 align:start position:0%
knowledge it did not run any function
 

00:08:42.240 --> 00:08:45.750 align:start position:0%
knowledge it did not run any function
call<00:08:43.240><c> so</c><00:08:43.719><c> that's</c><00:08:43.880><c> it</c><00:08:44.120><c> folks</c><00:08:44.640><c> so</c><00:08:44.920><c> this</c><00:08:45.040><c> is</c><00:08:45.279><c> auto</c>

00:08:45.750 --> 00:08:45.760 align:start position:0%
call so that's it folks so this is auto
 

00:08:45.760 --> 00:08:49.230 align:start position:0%
call so that's it folks so this is auto
rag<00:08:46.240><c> we</c><00:08:46.399><c> also</c><00:08:46.680><c> saw</c><00:08:47.160><c> where</c><00:08:47.360><c> it's</c><00:08:48.080><c> breaking</c><00:08:49.080><c> uh</c>

00:08:49.230 --> 00:08:49.240 align:start position:0%
rag we also saw where it's breaking uh
 

00:08:49.240 --> 00:08:50.870 align:start position:0%
rag we also saw where it's breaking uh
we<00:08:49.399><c> also</c><00:08:49.600><c> saw</c><00:08:49.839><c> that</c><00:08:50.040><c> this</c><00:08:50.399><c> doesn't</c><00:08:50.760><c> have</c>

00:08:50.870 --> 00:08:50.880 align:start position:0%
we also saw that this doesn't have
 

00:08:50.880 --> 00:08:52.350 align:start position:0%
we also saw that this doesn't have
streaming<00:08:51.320><c> yet</c><00:08:51.480><c> but</c><00:08:51.600><c> the</c><00:08:51.680><c> answers</c><00:08:52.040><c> are</c>

00:08:52.350 --> 00:08:52.360 align:start position:0%
streaming yet but the answers are
 

00:08:52.360 --> 00:08:55.470 align:start position:0%
streaming yet but the answers are
reasonably<00:08:52.839><c> fast</c><00:08:53.560><c> so</c><00:08:54.000><c> not</c><00:08:54.200><c> that</c><00:08:54.320><c> big</c><00:08:54.480><c> a</c><00:08:54.600><c> deal</c>

00:08:55.470 --> 00:08:55.480 align:start position:0%
reasonably fast so not that big a deal
 

00:08:55.480 --> 00:08:57.949 align:start position:0%
reasonably fast so not that big a deal
but<00:08:55.760><c> this</c><00:08:56.040><c> lens</c><00:08:56.480><c> itself</c><00:08:57.120><c> this</c><00:08:57.279><c> opens</c><00:08:57.600><c> up</c><00:08:57.800><c> so</c>

00:08:57.949 --> 00:08:57.959 align:start position:0%
but this lens itself this opens up so
 

00:08:57.959 --> 00:08:59.790 align:start position:0%
but this lens itself this opens up so
many<00:08:58.240><c> possibilities</c><00:08:58.880><c> as</c><00:08:58.959><c> soon</c><00:08:59.120><c> as</c><00:08:59.440><c> streaming</c>

00:08:59.790 --> 00:08:59.800 align:start position:0%
many possibilities as soon as streaming
 

00:08:59.800 --> 00:09:01.949 align:start position:0%
many possibilities as soon as streaming
with<00:09:00.000><c> Gro</c><00:09:00.320><c> and</c><00:09:00.519><c> function</c><00:09:00.880><c> call</c><00:09:01.279><c> uh</c><00:09:01.680><c> grock</c>

00:09:01.949 --> 00:09:01.959 align:start position:0%
with Gro and function call uh grock
 

00:09:01.959 --> 00:09:04.150 align:start position:0%
with Gro and function call uh grock
supports<00:09:02.279><c> streaming</c><00:09:02.600><c> for</c><00:09:02.800><c> function</c><00:09:03.160><c> calling</c>

00:09:04.150 --> 00:09:04.160 align:start position:0%
supports streaming for function calling
 

00:09:04.160 --> 00:09:07.470 align:start position:0%
supports streaming for function calling
this<00:09:04.360><c> becomes</c><00:09:04.800><c> a</c><00:09:05.160><c> very</c><00:09:05.560><c> viable</c><00:09:06.040><c> Contender</c><00:09:06.800><c> for</c>

00:09:07.470 --> 00:09:07.480 align:start position:0%
this becomes a very viable Contender for
 

00:09:07.480 --> 00:09:10.910 align:start position:0%
this becomes a very viable Contender for
some<00:09:08.320><c> exceptional</c><00:09:09.160><c> applications</c><00:09:10.160><c> that</c><00:09:10.680><c> uh</c>

00:09:10.910 --> 00:09:10.920 align:start position:0%
some exceptional applications that uh
 

00:09:10.920 --> 00:09:14.230 align:start position:0%
some exceptional applications that uh
can<00:09:11.120><c> be</c><00:09:11.279><c> suited</c><00:09:11.800><c> to</c><00:09:12.600><c> autoag</c><00:09:13.600><c> if</c><00:09:13.680><c> you</c><00:09:13.880><c> have</c><00:09:14.040><c> any</c>

00:09:14.230 --> 00:09:14.240 align:start position:0%
can be suited to autoag if you have any
 

00:09:14.240 --> 00:09:16.790 align:start position:0%
can be suited to autoag if you have any
questions<00:09:15.079><c> drop</c><00:09:15.399><c> by</c><00:09:15.560><c> in</c><00:09:15.640><c> the</c><00:09:15.760><c> Discord</c><00:09:16.240><c> or</c><00:09:16.560><c> open</c>

00:09:16.790 --> 00:09:16.800 align:start position:0%
questions drop by in the Discord or open
 

00:09:16.800 --> 00:09:18.430 align:start position:0%
questions drop by in the Discord or open
up<00:09:16.920><c> a</c><00:09:17.040><c> GitHub</c><00:09:17.360><c> issue</c><00:09:17.760><c> I</c><00:09:17.880><c> hope</c><00:09:18.000><c> you</c><00:09:18.120><c> enjoyed</c>

00:09:18.430 --> 00:09:18.440 align:start position:0%
up a GitHub issue I hope you enjoyed
 

00:09:18.440 --> 00:09:22.360 align:start position:0%
up a GitHub issue I hope you enjoyed
this<00:09:18.640><c> video</c><00:09:19.120><c> have</c><00:09:19.240><c> a</c><00:09:19.360><c> great</c><00:09:19.600><c> day</c>

