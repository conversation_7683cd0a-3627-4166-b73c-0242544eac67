WEBVTT
Kind: captions
Language: en

00:00:00.199 --> 00:00:01.790 align:start position:0%
 
okay<00:00:00.359><c> so</c><00:00:00.520><c> in</c><00:00:00.680><c> this</c><00:00:00.840><c> video</c><00:00:01.120><c> I</c><00:00:01.199><c> thought</c><00:00:01.400><c> I'd</c><00:00:01.599><c> make</c>

00:00:01.790 --> 00:00:01.800 align:start position:0%
okay so in this video I thought I'd make
 

00:00:01.800 --> 00:00:05.030 align:start position:0%
okay so in this video I thought I'd make
a<00:00:02.120><c> quick</c><00:00:02.520><c> introduction</c><00:00:03.399><c> to</c><00:00:03.639><c> make</c><00:00:03.919><c> a</c><00:00:04.120><c> suite</c><00:00:04.880><c> so</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
a quick introduction to make a suite so
 

00:00:05.040 --> 00:00:06.510 align:start position:0%
a quick introduction to make a suite so
when<00:00:05.200><c> I</c><00:00:05.279><c> talk</c><00:00:05.440><c> to</c><00:00:05.560><c> a</c><00:00:05.640><c> lot</c><00:00:05.759><c> of</c><00:00:05.879><c> people</c><00:00:06.200><c> I</c><00:00:06.319><c> find</c>

00:00:06.510 --> 00:00:06.520 align:start position:0%
when I talk to a lot of people I find
 

00:00:06.520 --> 00:00:08.589 align:start position:0%
when I talk to a lot of people I find
that<00:00:06.720><c> they</c><00:00:06.960><c> haven't</c><00:00:07.319><c> tried</c><00:00:07.680><c> out</c><00:00:08.080><c> the</c><00:00:08.240><c> Google</c>

00:00:08.589 --> 00:00:08.599 align:start position:0%
that they haven't tried out the Google
 

00:00:08.599 --> 00:00:11.589 align:start position:0%
that they haven't tried out the Google
Palm<00:00:08.960><c> 2</c><00:00:09.360><c> models</c><00:00:10.360><c> and</c><00:00:10.519><c> often</c><00:00:10.840><c> that's</c><00:00:11.120><c> because</c>

00:00:11.589 --> 00:00:11.599 align:start position:0%
Palm 2 models and often that's because
 

00:00:11.599 --> 00:00:13.310 align:start position:0%
Palm 2 models and often that's because
that<00:00:11.719><c> they</c><00:00:11.880><c> don't</c><00:00:12.120><c> have</c><00:00:12.440><c> access</c><00:00:12.759><c> to</c><00:00:12.960><c> Google</c>

00:00:13.310 --> 00:00:13.320 align:start position:0%
that they don't have access to Google
 

00:00:13.320 --> 00:00:15.629 align:start position:0%
that they don't have access to Google
cloud<00:00:14.160><c> and</c><00:00:14.320><c> I</c><00:00:14.719><c> understand</c><00:00:15.160><c> if</c><00:00:15.240><c> you</c><00:00:15.320><c> don't</c><00:00:15.480><c> have</c>

00:00:15.629 --> 00:00:15.639 align:start position:0%
cloud and I understand if you don't have
 

00:00:15.639 --> 00:00:16.910 align:start position:0%
cloud and I understand if you don't have
access<00:00:15.839><c> to</c><00:00:16.039><c> Google</c><00:00:16.279><c> Cloud</c><00:00:16.560><c> if</c><00:00:16.640><c> you're</c><00:00:16.760><c> not</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
access to Google Cloud if you're not
 

00:00:16.920 --> 00:00:18.750 align:start position:0%
access to Google Cloud if you're not
used<00:00:17.160><c> to</c><00:00:17.279><c> using</c><00:00:17.600><c> Google</c><00:00:17.920><c> Cloud</c><00:00:18.400><c> it</c><00:00:18.520><c> can</c><00:00:18.640><c> be</c>

00:00:18.750 --> 00:00:18.760 align:start position:0%
used to using Google Cloud it can be
 

00:00:18.760 --> 00:00:20.750 align:start position:0%
used to using Google Cloud it can be
very<00:00:18.920><c> frustrating</c><00:00:19.480><c> to</c><00:00:19.600><c> set</c><00:00:19.760><c> up</c><00:00:19.920><c> a</c><00:00:20.160><c> project</c><00:00:20.600><c> to</c>

00:00:20.750 --> 00:00:20.760 align:start position:0%
very frustrating to set up a project to
 

00:00:20.760 --> 00:00:22.189 align:start position:0%
very frustrating to set up a project to
set<00:00:20.920><c> up</c><00:00:21.039><c> all</c><00:00:21.199><c> the</c><00:00:21.320><c> permissions</c><00:00:21.800><c> and</c><00:00:22.000><c> stuff</c>

00:00:22.189 --> 00:00:22.199 align:start position:0%
set up all the permissions and stuff
 

00:00:22.199 --> 00:00:24.589 align:start position:0%
set up all the permissions and stuff
like<00:00:22.400><c> that</c><00:00:22.800><c> to</c><00:00:22.960><c> be</c><00:00:23.039><c> able</c><00:00:23.240><c> to</c><00:00:23.359><c> use</c><00:00:23.560><c> it</c><00:00:24.240><c> so</c><00:00:24.480><c> the</c>

00:00:24.589 --> 00:00:24.599 align:start position:0%
like that to be able to use it so the
 

00:00:24.599 --> 00:00:26.189 align:start position:0%
like that to be able to use it so the
cool<00:00:24.760><c> thing</c><00:00:24.880><c> with</c><00:00:25.039><c> maker</c><00:00:25.400><c> Suite</c><00:00:25.720><c> is</c><00:00:25.840><c> it</c><00:00:26.000><c> lets</c>

00:00:26.189 --> 00:00:26.199 align:start position:0%
cool thing with maker Suite is it lets
 

00:00:26.199 --> 00:00:28.990 align:start position:0%
cool thing with maker Suite is it lets
you<00:00:26.560><c> access</c><00:00:27.000><c> these</c><00:00:27.320><c> models</c><00:00:28.320><c> without</c><00:00:28.760><c> having</c>

00:00:28.990 --> 00:00:29.000 align:start position:0%
you access these models without having
 

00:00:29.000 --> 00:00:31.269 align:start position:0%
you access these models without having
to<00:00:29.199><c> have</c><00:00:29.320><c> a</c><00:00:29.439><c> Google</c><00:00:29.720><c> Cloud</c><00:00:30.160><c> account</c><00:00:31.000><c> you</c><00:00:31.119><c> can</c>

00:00:31.269 --> 00:00:31.279 align:start position:0%
to have a Google Cloud account you can
 

00:00:31.279 --> 00:00:33.310 align:start position:0%
to have a Google Cloud account you can
just<00:00:31.679><c> basically</c><00:00:32.000><c> come</c><00:00:32.160><c> in</c><00:00:32.399><c> here</c><00:00:32.800><c> currently</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
just basically come in here currently
 

00:00:33.320 --> 00:00:35.670 align:start position:0%
just basically come in here currently
this<00:00:33.480><c> is</c><00:00:33.879><c> free</c><00:00:34.719><c> I</c><00:00:34.800><c> don't</c><00:00:34.960><c> know</c><00:00:35.160><c> how</c><00:00:35.320><c> long</c><00:00:35.520><c> it's</c>

00:00:35.670 --> 00:00:35.680 align:start position:0%
this is free I don't know how long it's
 

00:00:35.680 --> 00:00:38.069 align:start position:0%
this is free I don't know how long it's
going<00:00:35.760><c> to</c><00:00:35.920><c> stay</c><00:00:36.399><c> free</c><00:00:37.320><c> and</c><00:00:37.520><c> when</c><00:00:37.640><c> it</c><00:00:37.760><c> came</c><00:00:37.920><c> out</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
going to stay free and when it came out
 

00:00:38.079 --> 00:00:40.110 align:start position:0%
going to stay free and when it came out
earlier<00:00:38.440><c> this</c><00:00:38.640><c> year</c><00:00:39.200><c> the</c><00:00:39.320><c> reason</c><00:00:39.559><c> I</c><00:00:39.680><c> didn't</c>

00:00:40.110 --> 00:00:40.120 align:start position:0%
earlier this year the reason I didn't
 

00:00:40.120 --> 00:00:41.750 align:start position:0%
earlier this year the reason I didn't
make<00:00:40.320><c> a</c><00:00:40.440><c> video</c><00:00:40.719><c> of</c><00:00:40.840><c> it</c><00:00:41.039><c> was</c><00:00:41.239><c> just</c><00:00:41.399><c> because</c><00:00:41.640><c> it</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
make a video of it was just because it
 

00:00:41.760 --> 00:00:44.790 align:start position:0%
make a video of it was just because it
was<00:00:42.399><c> still</c><00:00:43.280><c> in</c><00:00:43.399><c> sort</c><00:00:43.559><c> of</c><00:00:43.680><c> open</c><00:00:43.960><c> preview</c><00:00:44.600><c> and</c>

00:00:44.790 --> 00:00:44.800 align:start position:0%
was still in sort of open preview and
 

00:00:44.800 --> 00:00:46.470 align:start position:0%
was still in sort of open preview and
most<00:00:45.039><c> countries</c><00:00:45.399><c> didn't</c><00:00:45.680><c> have</c><00:00:45.960><c> access</c><00:00:46.239><c> to</c>

00:00:46.470 --> 00:00:46.480 align:start position:0%
most countries didn't have access to
 

00:00:46.480 --> 00:00:48.470 align:start position:0%
most countries didn't have access to
this<00:00:47.079><c> now</c><00:00:47.360><c> most</c><00:00:47.600><c> countries</c><00:00:47.920><c> around</c><00:00:48.120><c> the</c><00:00:48.239><c> world</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
this now most countries around the world
 

00:00:48.480 --> 00:00:50.229 align:start position:0%
this now most countries around the world
do<00:00:48.719><c> have</c><00:00:48.960><c> access</c><00:00:49.440><c> there</c><00:00:49.520><c> are</c><00:00:49.760><c> some</c><00:00:50.039><c> that</c>

00:00:50.229 --> 00:00:50.239 align:start position:0%
do have access there are some that
 

00:00:50.239 --> 00:00:52.869 align:start position:0%
do have access there are some that
probably<00:00:50.559><c> still</c><00:00:50.879><c> don't</c><00:00:51.680><c> this</c><00:00:51.800><c> will</c><00:00:52.039><c> come</c><00:00:52.719><c> but</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
probably still don't this will come but
 

00:00:52.879 --> 00:00:55.029 align:start position:0%
probably still don't this will come but
this<00:00:53.000><c> is</c><00:00:53.160><c> going</c><00:00:53.239><c> to</c><00:00:53.399><c> be</c><00:00:53.960><c> where</c><00:00:54.120><c> you</c><00:00:54.320><c> get</c><00:00:54.440><c> to</c><00:00:54.680><c> try</c>

00:00:55.029 --> 00:00:55.039 align:start position:0%
this is going to be where you get to try
 

00:00:55.039 --> 00:00:57.470 align:start position:0%
this is going to be where you get to try
out<00:00:55.680><c> using</c><00:00:56.199><c> large</c><00:00:56.520><c> language</c><00:00:56.879><c> models</c><00:00:57.239><c> from</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
out using large language models from
 

00:00:57.480 --> 00:00:59.470 align:start position:0%
out using large language models from
Google<00:00:58.320><c> and</c><00:00:58.440><c> if</c><00:00:58.519><c> you</c><00:00:58.640><c> wanted</c><00:00:58.879><c> to</c><00:00:59.039><c> use</c><00:00:59.239><c> them</c><00:00:59.359><c> in</c>

00:00:59.470 --> 00:00:59.480 align:start position:0%
Google and if you wanted to use them in
 

00:00:59.480 --> 00:01:01.229 align:start position:0%
Google and if you wanted to use them in
a<00:00:59.640><c> project</c><00:01:00.120><c> or</c><00:01:00.280><c> something</c><00:01:00.719><c> you</c><00:01:00.840><c> can</c><00:01:00.960><c> get</c><00:01:01.079><c> an</c>

00:01:01.229 --> 00:01:01.239 align:start position:0%
a project or something you can get an
 

00:01:01.239 --> 00:01:03.830 align:start position:0%
a project or something you can get an
API<00:01:01.760><c> key</c><00:01:02.039><c> to</c><00:01:02.239><c> actually</c><00:01:02.519><c> do</c><00:01:02.840><c> this</c><00:01:03.559><c> now</c>

00:01:03.830 --> 00:01:03.840 align:start position:0%
API key to actually do this now
 

00:01:03.840 --> 00:01:05.310 align:start position:0%
API key to actually do this now
eventually<00:01:04.159><c> I</c><00:01:04.239><c> think</c><00:01:04.400><c> this</c><00:01:04.519><c> will</c><00:01:04.680><c> become</c><00:01:05.000><c> paid</c>

00:01:05.310 --> 00:01:05.320 align:start position:0%
eventually I think this will become paid
 

00:01:05.320 --> 00:01:07.710 align:start position:0%
eventually I think this will become paid
in<00:01:05.479><c> some</c><00:01:05.760><c> ways</c><00:01:06.520><c> but</c><00:01:06.760><c> currently</c><00:01:07.200><c> for</c><00:01:07.400><c> now</c><00:01:07.600><c> come</c>

00:01:07.710 --> 00:01:07.720 align:start position:0%
in some ways but currently for now come
 

00:01:07.720 --> 00:01:10.870 align:start position:0%
in some ways but currently for now come
along<00:01:07.960><c> and</c><00:01:08.119><c> try</c><00:01:08.320><c> it</c><00:01:08.439><c> out</c><00:01:08.759><c> while</c><00:01:08.920><c> it's</c><00:01:09.200><c> here</c><00:01:09.920><c> so</c>

00:01:10.870 --> 00:01:10.880 align:start position:0%
along and try it out while it's here so
 

00:01:10.880 --> 00:01:12.630 align:start position:0%
along and try it out while it's here so
while<00:01:11.240><c> the</c><00:01:11.400><c> models</c><00:01:11.799><c> that</c><00:01:11.920><c> are</c><00:01:12.119><c> available</c><00:01:12.520><c> at</c>

00:01:12.630 --> 00:01:12.640 align:start position:0%
while the models that are available at
 

00:01:12.640 --> 00:01:15.070 align:start position:0%
while the models that are available at
the<00:01:12.799><c> moment</c><00:01:13.119><c> are</c><00:01:13.280><c> the</c><00:01:13.439><c> Palm</c><00:01:13.759><c> 2</c><00:01:14.040><c> models</c><00:01:14.960><c> you</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
the moment are the Palm 2 models you
 

00:01:15.080 --> 00:01:16.590 align:start position:0%
the moment are the Palm 2 models you
could<00:01:15.280><c> imagine</c><00:01:15.680><c> that</c><00:01:15.880><c> in</c><00:01:15.960><c> the</c><00:01:16.080><c> not</c><00:01:16.320><c> too</c>

00:01:16.590 --> 00:01:16.600 align:start position:0%
could imagine that in the not too
 

00:01:16.600 --> 00:01:18.590 align:start position:0%
could imagine that in the not too
distant<00:01:16.920><c> future</c><00:01:17.520><c> this</c><00:01:17.640><c> will</c><00:01:17.799><c> be</c><00:01:18.040><c> also</c><00:01:18.280><c> a</c><00:01:18.439><c> way</c>

00:01:18.590 --> 00:01:18.600 align:start position:0%
distant future this will be also a way
 

00:01:18.600 --> 00:01:20.910 align:start position:0%
distant future this will be also a way
that<00:01:18.680><c> you</c><00:01:18.799><c> can</c><00:01:19.040><c> access</c><00:01:19.360><c> the</c><00:01:19.560><c> Gemini</c><00:01:20.040><c> models</c><00:01:20.680><c> in</c>

00:01:20.910 --> 00:01:20.920 align:start position:0%
that you can access the Gemini models in
 

00:01:20.920 --> 00:01:22.710 align:start position:0%
that you can access the Gemini models in
here<00:01:21.479><c> so</c><00:01:21.640><c> let's</c><00:01:21.799><c> jump</c><00:01:22.000><c> in</c><00:01:22.119><c> and</c><00:01:22.320><c> actually</c><00:01:22.600><c> have</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
here so let's jump in and actually have
 

00:01:22.720 --> 00:01:24.990 align:start position:0%
here so let's jump in and actually have
a<00:01:22.880><c> look</c><00:01:23.119><c> at</c><00:01:23.280><c> maker</c><00:01:23.680><c> Suite</c><00:01:24.280><c> okay</c><00:01:24.439><c> so</c><00:01:24.680><c> this</c><00:01:24.799><c> is</c>

00:01:24.990 --> 00:01:25.000 align:start position:0%
a look at maker Suite okay so this is
 

00:01:25.000 --> 00:01:26.550 align:start position:0%
a look at maker Suite okay so this is
what<00:01:25.119><c> you</c><00:01:25.360><c> agreeded</c><00:01:25.840><c> with</c><00:01:26.040><c> when</c><00:01:26.159><c> you</c><00:01:26.280><c> go</c><00:01:26.400><c> to</c>

00:01:26.550 --> 00:01:26.560 align:start position:0%
what you agreeded with when you go to
 

00:01:26.560 --> 00:01:29.310 align:start position:0%
what you agreeded with when you go to
make<00:01:26.759><c> A.G</c><00:01:27.799><c> google.com</c><00:01:28.799><c> you'll</c><00:01:29.079><c> have</c><00:01:29.200><c> to</c>

00:01:29.310 --> 00:01:29.320 align:start position:0%
make A.G google.com you'll have to
 

00:01:29.320 --> 00:01:31.469 align:start position:0%
make A.G google.com you'll have to
probably<00:01:29.560><c> log</c><00:01:30.000><c> again</c><00:01:30.520><c> for</c><00:01:30.799><c> this</c><00:01:31.040><c> and</c><00:01:31.119><c> so</c><00:01:31.360><c> just</c>

00:01:31.469 --> 00:01:31.479 align:start position:0%
probably log again for this and so just
 

00:01:31.479 --> 00:01:33.550 align:start position:0%
probably log again for this and so just
use<00:01:31.720><c> your</c><00:01:31.960><c> normal</c><00:01:32.320><c> Google</c><00:01:32.680><c> account</c><00:01:33.000><c> is</c><00:01:33.200><c> fine</c>

00:01:33.550 --> 00:01:33.560 align:start position:0%
use your normal Google account is fine
 

00:01:33.560 --> 00:01:36.149 align:start position:0%
use your normal Google account is fine
for<00:01:33.799><c> this</c><00:01:34.520><c> and</c><00:01:34.640><c> you've</c><00:01:34.840><c> got</c><00:01:35.159><c> three</c><00:01:35.560><c> ways</c><00:01:36.000><c> of</c>

00:01:36.149 --> 00:01:36.159 align:start position:0%
for this and you've got three ways of
 

00:01:36.159 --> 00:01:38.990 align:start position:0%
for this and you've got three ways of
working<00:01:36.560><c> with</c><00:01:36.880><c> the</c><00:01:37.040><c> the</c><00:01:37.200><c> Palm</c><00:01:37.560><c> API</c><00:01:38.159><c> in</c><00:01:38.399><c> here</c><00:01:38.840><c> so</c>

00:01:38.990 --> 00:01:39.000 align:start position:0%
working with the the Palm API in here so
 

00:01:39.000 --> 00:01:40.710 align:start position:0%
working with the the Palm API in here so
I<00:01:39.079><c> can</c><00:01:39.200><c> just</c><00:01:39.320><c> come</c><00:01:39.439><c> in</c><00:01:39.600><c> here</c><00:01:40.079><c> start</c><00:01:40.320><c> and</c><00:01:40.439><c> make</c><00:01:40.560><c> a</c>

00:01:40.710 --> 00:01:40.720 align:start position:0%
I can just come in here start and make a
 

00:01:40.720 --> 00:01:44.069 align:start position:0%
I can just come in here start and make a
text<00:01:41.040><c> prompt</c><00:01:41.840><c> so</c><00:01:42.119><c> the</c><00:01:42.240><c> text</c><00:01:42.560><c> prompt</c><00:01:43.079><c> is</c><00:01:43.560><c> just</c>

00:01:44.069 --> 00:01:44.079 align:start position:0%
text prompt so the text prompt is just
 

00:01:44.079 --> 00:01:46.310 align:start position:0%
text prompt so the text prompt is just
standard<00:01:44.560><c> sort</c><00:01:44.759><c> of</c><00:01:44.920><c> llm</c><00:01:45.680><c> where</c><00:01:46.000><c> I'm</c><00:01:46.079><c> going</c><00:01:46.200><c> to</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
standard sort of llm where I'm going to
 

00:01:46.320 --> 00:01:48.510 align:start position:0%
standard sort of llm where I'm going to
put<00:01:46.520><c> something</c><00:01:46.920><c> in</c><00:01:47.560><c> and</c><00:01:47.719><c> it's</c><00:01:47.960><c> going</c><00:01:48.159><c> to</c>

00:01:48.510 --> 00:01:48.520 align:start position:0%
put something in and it's going to
 

00:01:48.520 --> 00:01:50.350 align:start position:0%
put something in and it's going to
complete<00:01:48.920><c> it</c><00:01:49.159><c> if</c><00:01:49.240><c> I</c><00:01:49.360><c> come</c><00:01:49.560><c> in</c><00:01:49.719><c> and</c><00:01:49.840><c> I</c><00:01:50.040><c> write</c><00:01:50.240><c> the</c>

00:01:50.350 --> 00:01:50.360 align:start position:0%
complete it if I come in and I write the
 

00:01:50.360 --> 00:01:52.550 align:start position:0%
complete it if I come in and I write the
catat<00:01:50.880><c> on</c><00:01:51.000><c> the</c><00:01:51.159><c> mat</c><00:01:51.600><c> I</c><00:01:51.680><c> can</c><00:01:51.840><c> come</c><00:01:52.079><c> down</c><00:01:52.360><c> here</c>

00:01:52.550 --> 00:01:52.560 align:start position:0%
catat on the mat I can come down here
 

00:01:52.560 --> 00:01:54.789 align:start position:0%
catat on the mat I can come down here
and<00:01:52.680><c> I</c><00:01:52.799><c> can</c><00:01:53.000><c> run</c><00:01:53.240><c> it</c><00:01:53.759><c> and</c><00:01:53.920><c> you'll</c><00:01:54.119><c> see</c><00:01:54.399><c> that</c><00:01:54.719><c> it</c>

00:01:54.789 --> 00:01:54.799 align:start position:0%
and I can run it and you'll see that it
 

00:01:54.799 --> 00:01:57.870 align:start position:0%
and I can run it and you'll see that it
will<00:01:55.079><c> basically</c><00:01:55.840><c> do</c><00:01:56.000><c> a</c><00:01:56.520><c> continuation</c><00:01:57.520><c> right</c>

00:01:57.870 --> 00:01:57.880 align:start position:0%
will basically do a continuation right
 

00:01:57.880 --> 00:01:59.389 align:start position:0%
will basically do a continuation right
so<00:01:58.039><c> if</c><00:01:58.119><c> I</c><00:01:58.240><c> type</c><00:01:58.399><c> in</c><00:01:58.520><c> a</c><00:01:58.680><c> prompt</c><00:01:58.960><c> you</c><00:01:59.079><c> can</c><00:01:59.200><c> see</c>

00:01:59.389 --> 00:01:59.399 align:start position:0%
so if I type in a prompt you can see
 

00:01:59.399 --> 00:02:01.389 align:start position:0%
so if I type in a prompt you can see
that<00:01:59.520><c> it</c><00:01:59.600><c> will</c><00:01:59.880><c> come</c><00:02:00.039><c> along</c><00:02:00.320><c> and</c><00:02:00.479><c> we'll</c><00:02:00.680><c> do</c><00:02:01.200><c> the</c>

00:02:01.389 --> 00:02:01.399 align:start position:0%
that it will come along and we'll do the
 

00:02:01.399 --> 00:02:03.870 align:start position:0%
that it will come along and we'll do the
continuation<00:02:02.159><c> for</c><00:02:02.479><c> this</c><00:02:03.159><c> and</c><00:02:03.320><c> I</c><00:02:03.439><c> can</c><00:02:03.600><c> choose</c>

00:02:03.870 --> 00:02:03.880 align:start position:0%
continuation for this and I can choose
 

00:02:03.880 --> 00:02:06.310 align:start position:0%
continuation for this and I can choose
to<00:02:04.159><c> either</c><00:02:04.479><c> accept</c><00:02:04.799><c> the</c><00:02:05.000><c> response</c><00:02:05.680><c> or</c><00:02:05.920><c> I</c><00:02:06.000><c> can</c>

00:02:06.310 --> 00:02:06.320 align:start position:0%
to either accept the response or I can
 

00:02:06.320 --> 00:02:09.589 align:start position:0%
to either accept the response or I can
reject<00:02:06.680><c> the</c><00:02:06.840><c> respon</c><00:02:07.600><c> Etc</c><00:02:08.080><c> in</c><00:02:08.280><c> here</c><00:02:09.000><c> now</c><00:02:09.440><c> I'm</c>

00:02:09.589 --> 00:02:09.599 align:start position:0%
reject the respon Etc in here now I'm
 

00:02:09.599 --> 00:02:13.510 align:start position:0%
reject the respon Etc in here now I'm
using<00:02:10.160><c> the</c><00:02:10.479><c> Palm</c><00:02:10.840><c> 2</c><00:02:11.160><c> text</c><00:02:11.480><c> bison</c><00:02:11.959><c> model</c><00:02:12.840><c> here</c>

00:02:13.510 --> 00:02:13.520 align:start position:0%
using the Palm 2 text bison model here
 

00:02:13.520 --> 00:02:15.589 align:start position:0%
using the Palm 2 text bison model here
so<00:02:13.680><c> I</c><00:02:13.760><c> can</c><00:02:13.920><c> change</c><00:02:14.239><c> the</c><00:02:14.400><c> temperature</c><00:02:14.920><c> on</c><00:02:15.200><c> this</c>

00:02:15.589 --> 00:02:15.599 align:start position:0%
so I can change the temperature on this
 

00:02:15.599 --> 00:02:18.390 align:start position:0%
so I can change the temperature on this
I<00:02:15.680><c> can</c><00:02:16.080><c> change</c><00:02:16.599><c> the</c><00:02:17.200><c> outputs</c><00:02:17.840><c> I</c><00:02:17.920><c> can</c><00:02:18.080><c> change</c>

00:02:18.390 --> 00:02:18.400 align:start position:0%
I can change the outputs I can change
 

00:02:18.400 --> 00:02:21.190 align:start position:0%
I can change the outputs I can change
the<00:02:18.599><c> safety</c><00:02:19.040><c> settings</c><00:02:19.840><c> if</c><00:02:19.959><c> I</c><00:02:20.080><c> want</c><00:02:20.160><c> to</c><00:02:20.440><c> adjust</c>

00:02:21.190 --> 00:02:21.200 align:start position:0%
the safety settings if I want to adjust
 

00:02:21.200 --> 00:02:23.869 align:start position:0%
the safety settings if I want to adjust
how<00:02:21.400><c> much</c><00:02:21.800><c> the</c><00:02:22.239><c> model</c><00:02:22.840><c> could</c><00:02:23.000><c> be</c><00:02:23.239><c> toxic</c><00:02:23.680><c> or</c>

00:02:23.869 --> 00:02:23.879 align:start position:0%
how much the model could be toxic or
 

00:02:23.879 --> 00:02:25.150 align:start position:0%
how much the model could be toxic or
anything<00:02:24.120><c> like</c><00:02:24.280><c> that</c><00:02:24.440><c> I</c><00:02:24.519><c> can</c><00:02:24.680><c> play</c><00:02:24.879><c> around</c>

00:02:25.150 --> 00:02:25.160 align:start position:0%
anything like that I can play around
 

00:02:25.160 --> 00:02:27.390 align:start position:0%
anything like that I can play around
with<00:02:25.360><c> those</c><00:02:25.640><c> in</c><00:02:25.840><c> here</c><00:02:26.080><c> as</c><00:02:26.280><c> well</c><00:02:26.959><c> so</c><00:02:27.160><c> this</c><00:02:27.239><c> is</c>

00:02:27.390 --> 00:02:27.400 align:start position:0%
with those in here as well so this is
 

00:02:27.400 --> 00:02:30.110 align:start position:0%
with those in here as well so this is
sort<00:02:27.599><c> of</c><00:02:27.720><c> the</c><00:02:27.959><c> first</c><00:02:28.680><c> use</c><00:02:29.040><c> of</c><00:02:29.200><c> it</c><00:02:29.599><c> and</c><00:02:29.840><c> and</c>

00:02:30.110 --> 00:02:30.120 align:start position:0%
sort of the first use of it and and
 

00:02:30.120 --> 00:02:32.430 align:start position:0%
sort of the first use of it and and
they've<00:02:30.319><c> got</c><00:02:30.480><c> some</c><00:02:30.720><c> nice</c><00:02:31.160><c> examples</c><00:02:31.640><c> in</c><00:02:31.879><c> here</c>

00:02:32.430 --> 00:02:32.440 align:start position:0%
they've got some nice examples in here
 

00:02:32.440 --> 00:02:34.550 align:start position:0%
they've got some nice examples in here
for<00:02:32.599><c> example</c><00:02:33.080><c> here</c><00:02:33.319><c> if</c><00:02:33.440><c> I</c><00:02:33.640><c> go</c><00:02:33.800><c> to</c><00:02:34.040><c> summarize</c>

00:02:34.550 --> 00:02:34.560 align:start position:0%
for example here if I go to summarize
 

00:02:34.560 --> 00:02:36.910 align:start position:0%
for example here if I go to summarize
the<00:02:34.840><c> paragraph</c><00:02:35.840><c> I</c><00:02:35.879><c> can</c><00:02:36.040><c> put</c><00:02:36.200><c> in</c><00:02:36.319><c> a</c><00:02:36.480><c> paragraph</c>

00:02:36.910 --> 00:02:36.920 align:start position:0%
the paragraph I can put in a paragraph
 

00:02:36.920 --> 00:02:39.990 align:start position:0%
the paragraph I can put in a paragraph
of<00:02:37.120><c> text</c><00:02:37.920><c> if</c><00:02:38.040><c> I</c><00:02:38.160><c> run</c><00:02:38.519><c> this</c><00:02:39.280><c> it</c><00:02:39.400><c> will</c><00:02:39.599><c> basically</c>

00:02:39.990 --> 00:02:40.000 align:start position:0%
of text if I run this it will basically
 

00:02:40.000 --> 00:02:43.750 align:start position:0%
of text if I run this it will basically
write<00:02:40.319><c> out</c><00:02:41.040><c> the</c><00:02:41.640><c> summarization</c><00:02:42.519><c> for</c><00:02:42.720><c> me</c><00:02:43.080><c> here</c>

00:02:43.750 --> 00:02:43.760 align:start position:0%
write out the summarization for me here
 

00:02:43.760 --> 00:02:45.910 align:start position:0%
write out the summarization for me here
so<00:02:44.239><c> you</c><00:02:44.360><c> can</c><00:02:44.599><c> learn</c><00:02:45.000><c> how</c><00:02:45.120><c> to</c><00:02:45.360><c> structure</c>

00:02:45.910 --> 00:02:45.920 align:start position:0%
so you can learn how to structure
 

00:02:45.920 --> 00:02:47.750 align:start position:0%
so you can learn how to structure
prompts<00:02:46.519><c> and</c><00:02:46.640><c> one</c><00:02:46.760><c> of</c><00:02:46.879><c> the</c><00:02:47.000><c> cool</c><00:02:47.280><c> things</c><00:02:47.640><c> is</c>

00:02:47.750 --> 00:02:47.760 align:start position:0%
prompts and one of the cool things is
 

00:02:47.760 --> 00:02:49.990 align:start position:0%
prompts and one of the cool things is
that<00:02:47.920><c> they've</c><00:02:48.120><c> got</c><00:02:48.239><c> a</c><00:02:48.360><c> whole</c><00:02:48.640><c> prompt</c><00:02:49.080><c> gallery</c>

00:02:49.990 --> 00:02:50.000 align:start position:0%
that they've got a whole prompt gallery
 

00:02:50.000 --> 00:02:51.470 align:start position:0%
that they've got a whole prompt gallery
that<00:02:50.120><c> you</c><00:02:50.200><c> can</c><00:02:50.319><c> go</c><00:02:50.400><c> and</c><00:02:50.560><c> look</c><00:02:50.720><c> at</c><00:02:51.000><c> for</c>

00:02:51.470 --> 00:02:51.480 align:start position:0%
that you can go and look at for
 

00:02:51.480 --> 00:02:53.309 align:start position:0%
that you can go and look at for
different<00:02:51.760><c> things</c><00:02:52.040><c> like</c><00:02:52.239><c> email</c><00:02:52.680><c> writers</c>

00:02:53.309 --> 00:02:53.319 align:start position:0%
different things like email writers
 

00:02:53.319 --> 00:02:55.790 align:start position:0%
different things like email writers
grammar<00:02:54.000><c> extraction</c><00:02:55.000><c> a</c><00:02:55.159><c> whole</c><00:02:55.400><c> bunch</c><00:02:55.599><c> of</c>

00:02:55.790 --> 00:02:55.800 align:start position:0%
grammar extraction a whole bunch of
 

00:02:55.800 --> 00:02:59.070 align:start position:0%
grammar extraction a whole bunch of
different<00:02:56.440><c> tasks</c><00:02:57.440><c> and</c><00:02:58.080><c> this</c><00:02:58.200><c> is</c><00:02:58.480><c> Loosely</c>

00:02:59.070 --> 00:02:59.080 align:start position:0%
different tasks and this is Loosely
 

00:02:59.080 --> 00:03:01.990 align:start position:0%
different tasks and this is Loosely
based<00:02:59.480><c> on</c><00:02:59.760><c> on</c><00:03:00.440><c> I</c><00:03:00.519><c> think</c><00:03:00.760><c> what</c><00:03:00.879><c> was</c><00:03:01.040><c> a</c><00:03:01.280><c> a</c><00:03:01.560><c> Google</c>

00:03:01.990 --> 00:03:02.000 align:start position:0%
based on on I think what was a a Google
 

00:03:02.000 --> 00:03:04.509 align:start position:0%
based on on I think what was a a Google
internal<00:03:02.560><c> tool</c><00:03:03.440><c> that</c><00:03:03.680><c> allowed</c><00:03:04.120><c> people</c><00:03:04.319><c> to</c>

00:03:04.509 --> 00:03:04.519 align:start position:0%
internal tool that allowed people to
 

00:03:04.519 --> 00:03:06.589 align:start position:0%
internal tool that allowed people to
test<00:03:04.840><c> these</c><00:03:05.120><c> out</c><00:03:05.599><c> for</c><00:03:05.879><c> this</c><00:03:06.080><c> kind</c><00:03:06.200><c> of</c><00:03:06.360><c> thing</c>

00:03:06.589 --> 00:03:06.599 align:start position:0%
test these out for this kind of thing
 

00:03:06.599 --> 00:03:08.110 align:start position:0%
test these out for this kind of thing
but<00:03:06.720><c> anyway</c><00:03:07.040><c> you</c><00:03:07.120><c> can</c><00:03:07.239><c> come</c><00:03:07.400><c> in</c><00:03:07.519><c> here</c><00:03:07.840><c> you</c><00:03:07.920><c> can</c>

00:03:08.110 --> 00:03:08.120 align:start position:0%
but anyway you can come in here you can
 

00:03:08.120 --> 00:03:10.070 align:start position:0%
but anyway you can come in here you can
preview<00:03:08.599><c> these</c><00:03:08.799><c> things</c><00:03:09.080><c> you</c><00:03:09.200><c> can</c><00:03:09.360><c> see</c><00:03:09.840><c> okay</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
preview these things you can see okay
 

00:03:10.080 --> 00:03:12.390 align:start position:0%
preview these things you can see okay
what<00:03:10.239><c> it</c><00:03:10.360><c> can</c><00:03:10.599><c> actually</c><00:03:10.920><c> do</c><00:03:11.799><c> and</c><00:03:11.959><c> then</c><00:03:12.080><c> you</c><00:03:12.200><c> can</c>

00:03:12.390 --> 00:03:12.400 align:start position:0%
what it can actually do and then you can
 

00:03:12.400 --> 00:03:14.470 align:start position:0%
what it can actually do and then you can
basically<00:03:12.879><c> just</c><00:03:13.120><c> open</c><00:03:13.360><c> it</c><00:03:13.560><c> up</c><00:03:13.840><c> in</c><00:03:14.040><c> make</c><00:03:14.280><c> a</c>

00:03:14.470 --> 00:03:14.480 align:start position:0%
basically just open it up in make a
 

00:03:14.480 --> 00:03:16.869 align:start position:0%
basically just open it up in make a
suite<00:03:15.000><c> and</c><00:03:15.120><c> try</c><00:03:15.319><c> it</c><00:03:15.400><c> out</c><00:03:15.560><c> yourself</c><00:03:16.480><c> so</c><00:03:16.760><c> the</c>

00:03:16.869 --> 00:03:16.879 align:start position:0%
suite and try it out yourself so the
 

00:03:16.879 --> 00:03:18.430 align:start position:0%
suite and try it out yourself so the
first<00:03:17.120><c> example</c><00:03:17.440><c> I</c><00:03:17.480><c> showed</c><00:03:17.720><c> you</c><00:03:17.799><c> was</c><00:03:17.920><c> a</c><00:03:18.080><c> text</c>

00:03:18.430 --> 00:03:18.440 align:start position:0%
first example I showed you was a text
 

00:03:18.440 --> 00:03:20.149 align:start position:0%
first example I showed you was a text
prompt<00:03:19.080><c> there</c><00:03:19.159><c> are</c><00:03:19.360><c> two</c><00:03:19.519><c> other</c><00:03:19.720><c> ones</c><00:03:20.000><c> there's</c>

00:03:20.149 --> 00:03:20.159 align:start position:0%
prompt there are two other ones there's
 

00:03:20.159 --> 00:03:22.589 align:start position:0%
prompt there are two other ones there's
a<00:03:20.360><c> chat</c><00:03:20.680><c> one</c><00:03:21.040><c> which</c><00:03:21.239><c> I</c><00:03:21.319><c> think</c><00:03:21.560><c> most</c><00:03:21.879><c> people</c><00:03:22.519><c> if</c>

00:03:22.589 --> 00:03:22.599 align:start position:0%
a chat one which I think most people if
 

00:03:22.599 --> 00:03:23.750 align:start position:0%
a chat one which I think most people if
you've<00:03:22.760><c> been</c><00:03:22.879><c> playing</c><00:03:23.120><c> around</c><00:03:23.280><c> with</c><00:03:23.400><c> language</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
you've been playing around with language
 

00:03:23.760 --> 00:03:24.990 align:start position:0%
you've been playing around with language
models<00:03:24.200><c> this</c><00:03:24.319><c> is</c><00:03:24.480><c> something</c><00:03:24.720><c> that</c><00:03:24.840><c> you've</c>

00:03:24.990 --> 00:03:25.000 align:start position:0%
models this is something that you've
 

00:03:25.000 --> 00:03:26.750 align:start position:0%
models this is something that you've
probably<00:03:25.280><c> seen</c><00:03:25.560><c> already</c><00:03:26.280><c> so</c><00:03:26.440><c> you</c><00:03:26.519><c> can</c>

00:03:26.750 --> 00:03:26.760 align:start position:0%
probably seen already so you can
 

00:03:26.760 --> 00:03:29.229 align:start position:0%
probably seen already so you can
basically<00:03:27.280><c> input</c><00:03:28.080><c> context</c><00:03:28.599><c> which</c><00:03:28.720><c> is</c><00:03:28.959><c> like</c><00:03:29.080><c> a</c>

00:03:29.229 --> 00:03:29.239 align:start position:0%
basically input context which is like a
 

00:03:29.239 --> 00:03:31.550 align:start position:0%
basically input context which is like a
system<00:03:29.760><c> prompt</c><00:03:30.439><c> and</c><00:03:30.680><c> here</c><00:03:31.120><c> and</c><00:03:31.239><c> then</c><00:03:31.319><c> you</c><00:03:31.400><c> can</c>

00:03:31.550 --> 00:03:31.560 align:start position:0%
system prompt and here and then you can
 

00:03:31.560 --> 00:03:33.470 align:start position:0%
system prompt and here and then you can
put<00:03:31.720><c> in</c><00:03:31.879><c> some</c><00:03:32.319><c> you</c><00:03:32.439><c> know</c><00:03:32.640><c> various</c><00:03:32.959><c> user</c>

00:03:33.470 --> 00:03:33.480 align:start position:0%
put in some you know various user
 

00:03:33.480 --> 00:03:35.990 align:start position:0%
put in some you know various user
examples<00:03:34.319><c> and</c><00:03:34.439><c> then</c><00:03:34.599><c> you</c><00:03:34.720><c> can</c><00:03:35.400><c> continue</c><00:03:35.840><c> the</c>

00:03:35.990 --> 00:03:36.000 align:start position:0%
examples and then you can continue the
 

00:03:36.000 --> 00:03:37.830 align:start position:0%
examples and then you can continue the
conversation<00:03:36.959><c> so</c><00:03:37.120><c> you</c><00:03:37.200><c> can</c><00:03:37.280><c> see</c><00:03:37.560><c> this</c>

00:03:37.830 --> 00:03:37.840 align:start position:0%
conversation so you can see this
 

00:03:37.840 --> 00:03:39.229 align:start position:0%
conversation so you can see this
conversation<00:03:38.360><c> is</c><00:03:38.519><c> actually</c><00:03:38.720><c> using</c><00:03:39.080><c> the</c>

00:03:39.229 --> 00:03:39.239 align:start position:0%
conversation is actually using the
 

00:03:39.239 --> 00:03:41.990 align:start position:0%
conversation is actually using the
memory<00:03:39.879><c> of</c><00:03:40.319><c> this</c><00:03:40.760><c> of</c><00:03:40.920><c> what's</c><00:03:41.120><c> gone</c><00:03:41.360><c> in</c><00:03:41.560><c> here</c><00:03:41.840><c> so</c>

00:03:41.990 --> 00:03:42.000 align:start position:0%
memory of this of what's gone in here so
 

00:03:42.000 --> 00:03:44.869 align:start position:0%
memory of this of what's gone in here so
it's<00:03:42.200><c> all</c><00:03:42.400><c> being</c><00:03:42.720><c> packaged</c><00:03:43.200><c> up</c><00:03:43.640><c> and</c><00:03:43.840><c> fed</c><00:03:44.239><c> to</c>

00:03:44.869 --> 00:03:44.879 align:start position:0%
it's all being packaged up and fed to
 

00:03:44.879 --> 00:03:48.589 align:start position:0%
it's all being packaged up and fed to
the<00:03:45.120><c> actual</c><00:03:45.799><c> model</c><00:03:46.720><c> so</c><00:03:47.000><c> this</c><00:03:47.120><c> in</c><00:03:47.360><c> many</c><00:03:47.680><c> ways</c><00:03:48.280><c> is</c>

00:03:48.589 --> 00:03:48.599 align:start position:0%
the actual model so this in many ways is
 

00:03:48.599 --> 00:03:51.630 align:start position:0%
the actual model so this in many ways is
kind<00:03:48.760><c> of</c><00:03:49.080><c> like</c><00:03:49.439><c> what</c><00:03:49.920><c> Lambda</c><00:03:50.560><c> and</c><00:03:50.760><c> Lambda</c><00:03:51.159><c> 2</c>

00:03:51.630 --> 00:03:51.640 align:start position:0%
kind of like what Lambda and Lambda 2
 

00:03:51.640 --> 00:03:53.030 align:start position:0%
kind of like what Lambda and Lambda 2
were<00:03:51.879><c> doing</c><00:03:52.239><c> where</c><00:03:52.360><c> you</c><00:03:52.480><c> could</c><00:03:52.680><c> basically</c>

00:03:53.030 --> 00:03:53.040 align:start position:0%
were doing where you could basically
 

00:03:53.040 --> 00:03:54.789 align:start position:0%
were doing where you could basically
have<00:03:53.159><c> a</c><00:03:53.319><c> conversation</c><00:03:54.040><c> with</c><00:03:54.280><c> different</c>

00:03:54.789 --> 00:03:54.799 align:start position:0%
have a conversation with different
 

00:03:54.799 --> 00:03:56.309 align:start position:0%
have a conversation with different
objects<00:03:55.200><c> or</c><00:03:55.439><c> different</c><00:03:55.720><c> in</c><00:03:55.840><c> this</c><00:03:56.000><c> case</c><00:03:56.159><c> it's</c>

00:03:56.309 --> 00:03:56.319 align:start position:0%
objects or different in this case it's
 

00:03:56.319 --> 00:03:58.509 align:start position:0%
objects or different in this case it's
an<00:03:56.480><c> alien</c><00:03:57.000><c> that</c><00:03:57.159><c> kind</c><00:03:57.280><c> of</c><00:03:57.480><c> thing</c><00:03:58.120><c> another</c>

00:03:58.509 --> 00:03:58.519 align:start position:0%
an alien that kind of thing another
 

00:03:58.519 --> 00:04:00.990 align:start position:0%
an alien that kind of thing another
example<00:03:59.000><c> that</c><00:03:59.120><c> I</c><00:03:59.239><c> find</c><00:03:59.400><c> to</c><00:03:59.519><c> be</c><00:03:59.879><c> really</c><00:04:00.079><c> cool</c><00:04:00.799><c> is</c>

00:04:00.990 --> 00:04:01.000 align:start position:0%
example that I find to be really cool is
 

00:04:01.000 --> 00:04:03.030 align:start position:0%
example that I find to be really cool is
where<00:04:01.120><c> you</c><00:04:01.200><c> can</c><00:04:01.360><c> create</c><00:04:01.599><c> a</c><00:04:01.799><c> data</c><00:04:02.120><c> prompt</c><00:04:02.840><c> so</c>

00:04:03.030 --> 00:04:03.040 align:start position:0%
where you can create a data prompt so
 

00:04:03.040 --> 00:04:04.190 align:start position:0%
where you can create a data prompt so
I'm<00:04:03.120><c> going</c><00:04:03.239><c> to</c><00:04:03.319><c> show</c><00:04:03.480><c> you</c><00:04:03.680><c> one</c><00:04:03.879><c> that</c><00:04:04.000><c> I've</c>

00:04:04.190 --> 00:04:04.200 align:start position:0%
I'm going to show you one that I've
 

00:04:04.200 --> 00:04:05.550 align:start position:0%
I'm going to show you one that I've
actually<00:04:04.400><c> made</c><00:04:04.680><c> before</c><00:04:05.040><c> so</c><00:04:05.200><c> I've</c><00:04:05.319><c> got</c><00:04:05.439><c> a</c>

00:04:05.550 --> 00:04:05.560 align:start position:0%
actually made before so I've got a
 

00:04:05.560 --> 00:04:07.550 align:start position:0%
actually made before so I've got a
little<00:04:05.799><c> Library</c><00:04:06.319><c> here</c><00:04:06.879><c> so</c><00:04:07.079><c> this</c><00:04:07.159><c> is</c><00:04:07.319><c> one</c><00:04:07.480><c> that</c>

00:04:07.550 --> 00:04:07.560 align:start position:0%
little Library here so this is one that
 

00:04:07.560 --> 00:04:10.110 align:start position:0%
little Library here so this is one that
I<00:04:07.720><c> made</c><00:04:07.920><c> quite</c><00:04:08.319><c> a</c><00:04:08.480><c> while</c><00:04:08.760><c> back</c><00:04:09.480><c> and</c><00:04:09.599><c> the</c><00:04:09.760><c> idea</c>

00:04:10.110 --> 00:04:10.120 align:start position:0%
I made quite a while back and the idea
 

00:04:10.120 --> 00:04:12.309 align:start position:0%
I made quite a while back and the idea
with<00:04:10.400><c> this</c><00:04:10.879><c> was</c><00:04:11.319><c> taken</c><00:04:11.640><c> from</c><00:04:11.840><c> the</c><00:04:12.000><c> Harry</c>

00:04:12.309 --> 00:04:12.319 align:start position:0%
with this was taken from the Harry
 

00:04:12.319 --> 00:04:14.910 align:start position:0%
with this was taken from the Harry
Potter<00:04:12.640><c> books</c><00:04:13.439><c> of</c><00:04:13.720><c> where</c><00:04:14.120><c> there</c><00:04:14.280><c> is</c><00:04:14.599><c> the</c><00:04:14.840><c> the</c>

00:04:14.910 --> 00:04:14.920 align:start position:0%
Potter books of where there is the the
 

00:04:14.920 --> 00:04:17.830 align:start position:0%
Potter books of where there is the the
sort<00:04:15.079><c> of</c><00:04:15.239><c> magical</c><00:04:15.799><c> sorting</c><00:04:16.440><c> hat</c><00:04:17.280><c> that</c><00:04:17.519><c> looks</c>

00:04:17.830 --> 00:04:17.840 align:start position:0%
sort of magical sorting hat that looks
 

00:04:17.840 --> 00:04:20.189 align:start position:0%
sort of magical sorting hat that looks
at<00:04:18.280><c> someone's</c><00:04:18.840><c> name</c><00:04:19.280><c> or</c><00:04:19.479><c> looks</c><00:04:19.639><c> at</c><00:04:19.840><c> you</c><00:04:19.959><c> put</c><00:04:20.079><c> it</c>

00:04:20.189 --> 00:04:20.199 align:start position:0%
at someone's name or looks at you put it
 

00:04:20.199 --> 00:04:22.670 align:start position:0%
at someone's name or looks at you put it
on<00:04:20.320><c> a</c><00:04:20.479><c> person</c><00:04:21.120><c> and</c><00:04:21.600><c> it</c><00:04:21.720><c> predicts</c><00:04:22.199><c> what</c><00:04:22.400><c> house</c>

00:04:22.670 --> 00:04:22.680 align:start position:0%
on a person and it predicts what house
 

00:04:22.680 --> 00:04:24.710 align:start position:0%
on a person and it predicts what house
they<00:04:22.800><c> should</c><00:04:23.040><c> go</c><00:04:23.240><c> to</c><00:04:23.919><c> and</c><00:04:24.040><c> so</c><00:04:24.199><c> what</c><00:04:24.360><c> I've</c><00:04:24.520><c> done</c>

00:04:24.710 --> 00:04:24.720 align:start position:0%
they should go to and so what I've done
 

00:04:24.720 --> 00:04:27.870 align:start position:0%
they should go to and so what I've done
here<00:04:24.880><c> is</c><00:04:25.080><c> just</c><00:04:25.280><c> put</c><00:04:25.520><c> in</c><00:04:26.120><c> some</c><00:04:26.479><c> examples</c><00:04:27.440><c> of</c>

00:04:27.870 --> 00:04:27.880 align:start position:0%
here is just put in some examples of
 

00:04:27.880 --> 00:04:29.909 align:start position:0%
here is just put in some examples of
different<00:04:28.360><c> names</c><00:04:28.960><c> and</c><00:04:29.080><c> then</c><00:04:29.199><c> the</c><00:04:29.360><c> house</c><00:04:29.759><c> that</c>

00:04:29.909 --> 00:04:29.919 align:start position:0%
different names and then the house that
 

00:04:29.919 --> 00:04:32.150 align:start position:0%
different names and then the house that
they<00:04:30.120><c> got</c><00:04:30.840><c> and</c><00:04:30.960><c> then</c><00:04:31.080><c> we</c><00:04:31.199><c> can</c><00:04:31.360><c> take</c><00:04:31.600><c> anyone's</c>

00:04:32.150 --> 00:04:32.160 align:start position:0%
they got and then we can take anyone's
 

00:04:32.160 --> 00:04:34.550 align:start position:0%
they got and then we can take anyone's
name<00:04:32.560><c> and</c><00:04:32.720><c> we</c><00:04:32.800><c> can</c><00:04:33.000><c> stick</c><00:04:33.280><c> it</c><00:04:33.479><c> in</c><00:04:33.720><c> here</c><00:04:34.360><c> and</c><00:04:34.479><c> we</c>

00:04:34.550 --> 00:04:34.560 align:start position:0%
name and we can stick it in here and we
 

00:04:34.560 --> 00:04:36.909 align:start position:0%
name and we can stick it in here and we
can<00:04:34.720><c> run</c><00:04:34.919><c> it</c><00:04:35.160><c> and</c><00:04:35.240><c> you'll</c><00:04:35.440><c> see</c><00:04:35.720><c> that</c><00:04:36.280><c> now</c><00:04:36.720><c> we're</c>

00:04:36.909 --> 00:04:36.919 align:start position:0%
can run it and you'll see that now we're
 

00:04:36.919 --> 00:04:39.390 align:start position:0%
can run it and you'll see that now we're
using<00:04:37.240><c> a</c><00:04:37.479><c> data</c><00:04:37.840><c> prompt</c><00:04:38.600><c> where</c><00:04:38.800><c> we've</c><00:04:39.000><c> set</c><00:04:39.199><c> up</c>

00:04:39.390 --> 00:04:39.400 align:start position:0%
using a data prompt where we've set up
 

00:04:39.400 --> 00:04:42.270 align:start position:0%
using a data prompt where we've set up
this<00:04:39.639><c> input</c><00:04:40.280><c> output</c><00:04:40.880><c> examples</c><00:04:41.880><c> and</c><00:04:42.000><c> now</c><00:04:42.160><c> we're</c>

00:04:42.270 --> 00:04:42.280 align:start position:0%
this input output examples and now we're
 

00:04:42.280 --> 00:04:44.390 align:start position:0%
this input output examples and now we're
going<00:04:42.400><c> to</c><00:04:42.520><c> put</c><00:04:42.680><c> in</c><00:04:43.000><c> you</c><00:04:43.120><c> know</c><00:04:43.320><c> one</c><00:04:43.639><c> input</c><00:04:44.080><c> here</c>

00:04:44.390 --> 00:04:44.400 align:start position:0%
going to put in you know one input here
 

00:04:44.400 --> 00:04:46.590 align:start position:0%
going to put in you know one input here
and<00:04:44.479><c> then</c><00:04:44.600><c> it's</c><00:04:44.759><c> got</c><00:04:44.880><c> to</c><00:04:45.160><c> generate</c><00:04:45.520><c> an</c><00:04:45.720><c> output</c>

00:04:46.590 --> 00:04:46.600 align:start position:0%
and then it's got to generate an output
 

00:04:46.600 --> 00:04:49.189 align:start position:0%
and then it's got to generate an output
based<00:04:46.960><c> on</c><00:04:47.639><c> these</c><00:04:47.800><c> ones</c><00:04:48.160><c> here</c><00:04:48.440><c> so</c><00:04:48.800><c> let's</c><00:04:48.919><c> see</c><00:04:49.080><c> if</c>

00:04:49.189 --> 00:04:49.199 align:start position:0%
based on these ones here so let's see if
 

00:04:49.199 --> 00:04:51.950 align:start position:0%
based on these ones here so let's see if
I<00:04:49.360><c> type</c><00:04:49.560><c> in</c><00:04:49.759><c> my</c><00:04:49.960><c> name</c><00:04:50.639><c> what</c><00:04:50.880><c> house</c><00:04:51.240><c> do</c><00:04:51.400><c> I</c><00:04:51.560><c> get</c><00:04:51.720><c> to</c>

00:04:51.950 --> 00:04:51.960 align:start position:0%
I type in my name what house do I get to
 

00:04:51.960 --> 00:04:53.950 align:start position:0%
I type in my name what house do I get to
so<00:04:52.120><c> if</c><00:04:52.199><c> I</c><00:04:52.360><c> come</c><00:04:52.479><c> to</c><00:04:52.720><c> this</c><00:04:52.880><c> button</c><00:04:53.280><c> here</c><00:04:53.639><c> we</c><00:04:53.759><c> can</c>

00:04:53.950 --> 00:04:53.960 align:start position:0%
so if I come to this button here we can
 

00:04:53.960 --> 00:04:56.029 align:start position:0%
so if I come to this button here we can
just<00:04:54.199><c> predict</c><00:04:55.120><c> and</c><00:04:55.240><c> you</c><00:04:55.320><c> can</c><00:04:55.440><c> see</c><00:04:55.680><c> that</c><00:04:55.880><c> okay</c>

00:04:56.029 --> 00:04:56.039 align:start position:0%
just predict and you can see that okay
 

00:04:56.039 --> 00:04:58.790 align:start position:0%
just predict and you can see that okay
for<00:04:56.160><c> me</c><00:04:56.320><c> it's</c><00:04:56.560><c> predicted</c><00:04:57.000><c> house</c><00:04:57.360><c> Slytherin</c><00:04:58.360><c> so</c>

00:04:58.790 --> 00:04:58.800 align:start position:0%
for me it's predicted house Slytherin so
 

00:04:58.800 --> 00:05:00.270 align:start position:0%
for me it's predicted house Slytherin so
this<00:04:59.000><c> can</c><00:04:59.160><c> kind</c><00:04:59.280><c> of</c><00:04:59.360><c> think</c><00:04:59.639><c> can</c><00:04:59.759><c> be</c><00:04:59.880><c> fun</c><00:05:00.080><c> to</c>

00:05:00.270 --> 00:05:00.280 align:start position:0%
this can kind of think can be fun to
 

00:05:00.280 --> 00:05:02.390 align:start position:0%
this can kind of think can be fun to
play<00:05:00.560><c> with</c><00:05:00.960><c> you</c><00:05:01.039><c> can</c><00:05:01.280><c> also</c><00:05:01.560><c> use</c><00:05:01.800><c> this</c><00:05:01.960><c> for</c><00:05:02.199><c> much</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
play with you can also use this for much
 

00:05:02.400 --> 00:05:04.310 align:start position:0%
play with you can also use this for much
more<00:05:03.000><c> in-depth</c><00:05:03.520><c> things</c><00:05:03.759><c> if</c><00:05:03.880><c> you</c><00:05:03.960><c> wanted</c><00:05:04.199><c> to</c>

00:05:04.310 --> 00:05:04.320 align:start position:0%
more in-depth things if you wanted to
 

00:05:04.320 --> 00:05:06.909 align:start position:0%
more in-depth things if you wanted to
build<00:05:04.639><c> like</c><00:05:04.759><c> your</c><00:05:04.919><c> own</c><00:05:05.240><c> paraphrasing</c><00:05:06.039><c> engine</c>

00:05:06.909 --> 00:05:06.919 align:start position:0%
build like your own paraphrasing engine
 

00:05:06.919 --> 00:05:09.390 align:start position:0%
build like your own paraphrasing engine
you<00:05:07.000><c> could</c><00:05:07.160><c> come</c><00:05:07.320><c> in</c><00:05:07.440><c> and</c><00:05:07.639><c> put</c><00:05:07.919><c> like</c><00:05:08.120><c> inputs</c><00:05:09.000><c> of</c>

00:05:09.390 --> 00:05:09.400 align:start position:0%
you could come in and put like inputs of
 

00:05:09.400 --> 00:05:11.870 align:start position:0%
you could come in and put like inputs of
the<00:05:09.560><c> the</c><00:05:09.720><c> text</c><00:05:10.080><c> in</c><00:05:10.520><c> and</c><00:05:10.720><c> then</c><00:05:11.199><c> output</c><00:05:11.639><c> being</c>

00:05:11.870 --> 00:05:11.880 align:start position:0%
the the text in and then output being
 

00:05:11.880 --> 00:05:14.790 align:start position:0%
the the text in and then output being
the<00:05:12.080><c> paraphrased</c><00:05:12.800><c> version</c><00:05:13.759><c> and</c><00:05:14.160><c> with</c><00:05:14.360><c> not</c><00:05:14.560><c> too</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
the paraphrased version and with not too
 

00:05:14.800 --> 00:05:17.469 align:start position:0%
the paraphrased version and with not too
many<00:05:15.120><c> examples</c><00:05:15.880><c> you</c><00:05:16.000><c> can</c><00:05:16.280><c> get</c><00:05:16.479><c> it</c><00:05:16.680><c> to</c><00:05:17.280><c> go</c>

00:05:17.469 --> 00:05:17.479 align:start position:0%
many examples you can get it to go
 

00:05:17.479 --> 00:05:19.390 align:start position:0%
many examples you can get it to go
through<00:05:17.720><c> and</c><00:05:17.919><c> actually</c><00:05:18.160><c> run</c><00:05:18.520><c> something</c><00:05:19.280><c> now</c>

00:05:19.390 --> 00:05:19.400 align:start position:0%
through and actually run something now
 

00:05:19.400 --> 00:05:21.230 align:start position:0%
through and actually run something now
we<00:05:19.479><c> can</c><00:05:19.600><c> see</c><00:05:19.759><c> down</c><00:05:20.000><c> here</c><00:05:20.199><c> we've</c><00:05:20.400><c> got</c><00:05:20.800><c> our</c><00:05:21.000><c> text</c>

00:05:21.230 --> 00:05:21.240 align:start position:0%
we can see down here we've got our text
 

00:05:21.240 --> 00:05:25.390 align:start position:0%
we can see down here we've got our text
window<00:05:21.560><c> can</c><00:05:21.759><c> up</c><00:05:21.919><c> to</c><00:05:22.039><c> 8,000</c><00:05:22.800><c> tokens</c><00:05:23.800><c> here</c><00:05:24.400><c> 8,196</c>

00:05:25.390 --> 00:05:25.400 align:start position:0%
window can up to 8,000 tokens here 8,196
 

00:05:25.400 --> 00:05:27.870 align:start position:0%
window can up to 8,000 tokens here 8,196
tokens<00:05:25.800><c> we're</c><00:05:25.919><c> only</c><00:05:26.120><c> using</c><00:05:26.479><c> 120</c><00:05:27.319><c> of</c><00:05:27.520><c> these</c>

00:05:27.870 --> 00:05:27.880 align:start position:0%
tokens we're only using 120 of these
 

00:05:27.880 --> 00:05:29.550 align:start position:0%
tokens we're only using 120 of these
here<00:05:28.520><c> so</c><00:05:28.680><c> there</c><00:05:28.759><c> are</c><00:05:28.840><c> a</c><00:05:28.919><c> whole</c><00:05:29.080><c> bunch</c><00:05:29.240><c> of</c>

00:05:29.550 --> 00:05:29.560 align:start position:0%
here so there are a whole bunch of
 

00:05:29.560 --> 00:05:31.309 align:start position:0%
here so there are a whole bunch of
different<00:05:30.080><c> prompts</c><00:05:30.479><c> and</c><00:05:30.639><c> stuff</c><00:05:30.919><c> that</c><00:05:31.000><c> you</c><00:05:31.120><c> can</c>

00:05:31.309 --> 00:05:31.319 align:start position:0%
different prompts and stuff that you can
 

00:05:31.319 --> 00:05:33.590 align:start position:0%
different prompts and stuff that you can
do<00:05:31.560><c> for</c><00:05:31.960><c> this</c><00:05:32.560><c> now</c><00:05:32.800><c> one</c><00:05:32.919><c> of</c><00:05:33.039><c> the</c><00:05:33.199><c> key</c><00:05:33.400><c> things</c>

00:05:33.590 --> 00:05:33.600 align:start position:0%
do for this now one of the key things
 

00:05:33.600 --> 00:05:35.150 align:start position:0%
do for this now one of the key things
that<00:05:33.720><c> you'll</c><00:05:33.919><c> often</c><00:05:34.160><c> want</c><00:05:34.280><c> to</c><00:05:34.440><c> do</c><00:05:34.800><c> is</c><00:05:34.919><c> if</c><00:05:35.039><c> you</c>

00:05:35.150 --> 00:05:35.160 align:start position:0%
that you'll often want to do is if you
 

00:05:35.160 --> 00:05:36.870 align:start position:0%
that you'll often want to do is if you
want<00:05:35.280><c> to</c><00:05:35.479><c> basically</c><00:05:35.880><c> share</c><00:05:36.280><c> something</c><00:05:36.680><c> with</c>

00:05:36.870 --> 00:05:36.880 align:start position:0%
want to basically share something with
 

00:05:36.880 --> 00:05:39.390 align:start position:0%
want to basically share something with
someone<00:05:37.360><c> or</c><00:05:37.720><c> get</c><00:05:37.919><c> code</c><00:05:38.240><c> for</c><00:05:38.440><c> it</c><00:05:38.720><c> so</c><00:05:38.960><c> sharing</c>

00:05:39.390 --> 00:05:39.400 align:start position:0%
someone or get code for it so sharing
 

00:05:39.400 --> 00:05:41.550 align:start position:0%
someone or get code for it so sharing
with<00:05:39.560><c> someone</c><00:05:39.840><c> is</c><00:05:40.039><c> quite</c><00:05:40.280><c> simple</c><00:05:41.080><c> I</c><00:05:41.160><c> can</c><00:05:41.400><c> just</c>

00:05:41.550 --> 00:05:41.560 align:start position:0%
with someone is quite simple I can just
 

00:05:41.560 --> 00:05:43.749 align:start position:0%
with someone is quite simple I can just
click<00:05:41.880><c> the</c><00:05:42.120><c> share</c><00:05:42.520><c> button</c><00:05:43.160><c> and</c><00:05:43.280><c> it</c><00:05:43.360><c> will</c><00:05:43.560><c> come</c>

00:05:43.749 --> 00:05:43.759 align:start position:0%
click the share button and it will come
 

00:05:43.759 --> 00:05:46.469 align:start position:0%
click the share button and it will come
up<00:05:44.240><c> and</c><00:05:44.759><c> I</c><00:05:44.840><c> can</c><00:05:45.080><c> then</c><00:05:45.240><c> share</c><00:05:45.639><c> it</c><00:05:45.800><c> with</c><00:05:45.960><c> someone</c>

00:05:46.469 --> 00:05:46.479 align:start position:0%
up and I can then share it with someone
 

00:05:46.479 --> 00:05:47.830 align:start position:0%
up and I can then share it with someone
and<00:05:46.600><c> then</c><00:05:46.759><c> they</c><00:05:46.880><c> can</c><00:05:47.039><c> see</c><00:05:47.280><c> that</c><00:05:47.440><c> in</c><00:05:47.639><c> their</c>

00:05:47.830 --> 00:05:47.840 align:start position:0%
and then they can see that in their
 

00:05:47.840 --> 00:05:50.950 align:start position:0%
and then they can see that in their
maker<00:05:48.240><c> Suite</c><00:05:49.039><c> the</c><00:05:49.160><c> other</c><00:05:49.400><c> options</c><00:05:49.800><c> I</c><00:05:50.000><c> have</c><00:05:50.400><c> are</c>

00:05:50.950 --> 00:05:50.960 align:start position:0%
maker Suite the other options I have are
 

00:05:50.960 --> 00:05:52.790 align:start position:0%
maker Suite the other options I have are
actually<00:05:51.199><c> bringing</c><00:05:51.520><c> up</c><00:05:51.680><c> the</c><00:05:51.880><c> code</c><00:05:52.479><c> so</c><00:05:52.600><c> you</c><00:05:52.720><c> can</c>

00:05:52.790 --> 00:05:52.800 align:start position:0%
actually bringing up the code so you can
 

00:05:52.800 --> 00:05:55.150 align:start position:0%
actually bringing up the code so you can
see<00:05:53.039><c> the</c><00:05:53.160><c> code</c><00:05:53.520><c> I</c><00:05:53.639><c> can</c><00:05:53.759><c> get</c><00:05:53.880><c> it</c><00:05:54.000><c> in</c><00:05:54.280><c> JavaScript</c>

00:05:55.150 --> 00:05:55.160 align:start position:0%
see the code I can get it in JavaScript
 

00:05:55.160 --> 00:05:57.110 align:start position:0%
see the code I can get it in JavaScript
I<00:05:55.240><c> can</c><00:05:55.400><c> get</c><00:05:55.520><c> it</c><00:05:55.680><c> in</c><00:05:55.919><c> python</c><00:05:56.560><c> or</c><00:05:56.720><c> I</c><00:05:56.800><c> can</c><00:05:56.960><c> just</c>

00:05:57.110 --> 00:05:57.120 align:start position:0%
I can get it in python or I can just
 

00:05:57.120 --> 00:05:59.870 align:start position:0%
I can get it in python or I can just
open<00:05:57.360><c> it</c><00:05:57.560><c> up</c><00:05:57.759><c> in</c><00:05:57.880><c> a</c><00:05:58.039><c> cab</c><00:05:58.960><c> so</c><00:05:59.160><c> if</c><00:05:59.240><c> I</c><00:05:59.520><c> open</c><00:05:59.680><c> it</c><00:05:59.800><c> up</c>

00:05:59.870 --> 00:05:59.880 align:start position:0%
open it up in a cab so if I open it up
 

00:05:59.880 --> 00:06:01.990 align:start position:0%
open it up in a cab so if I open it up
in<00:06:00.000><c> a</c><00:06:00.160><c> collab</c><00:06:00.680><c> this</c><00:06:00.800><c> will</c><00:06:01.120><c> actually</c><00:06:01.440><c> allow</c><00:06:01.759><c> me</c>

00:06:01.990 --> 00:06:02.000 align:start position:0%
in a collab this will actually allow me
 

00:06:02.000 --> 00:06:04.990 align:start position:0%
in a collab this will actually allow me
to<00:06:02.240><c> run</c><00:06:02.560><c> it</c><00:06:03.160><c> remotely</c><00:06:04.039><c> now</c><00:06:04.520><c> it</c><00:06:04.639><c> doesn't</c><00:06:04.880><c> have</c>

00:06:04.990 --> 00:06:05.000 align:start position:0%
to run it remotely now it doesn't have
 

00:06:05.000 --> 00:06:07.870 align:start position:0%
to run it remotely now it doesn't have
to<00:06:05.120><c> be</c><00:06:05.360><c> collab</c><00:06:06.319><c> this</c><00:06:06.440><c> is</c><00:06:06.639><c> not</c><00:06:06.919><c> tied</c><00:06:07.479><c> together</c>

00:06:07.870 --> 00:06:07.880 align:start position:0%
to be collab this is not tied together
 

00:06:07.880 --> 00:06:09.150 align:start position:0%
to be collab this is not tied together
meaning<00:06:08.199><c> that</c><00:06:08.319><c> we're</c><00:06:08.479><c> going</c><00:06:08.560><c> to</c><00:06:08.720><c> need</c><00:06:08.919><c> some</c>

00:06:09.150 --> 00:06:09.160 align:start position:0%
meaning that we're going to need some
 

00:06:09.160 --> 00:06:11.110 align:start position:0%
meaning that we're going to need some
kind<00:06:09.280><c> of</c><00:06:09.479><c> API</c><00:06:10.000><c> key</c><00:06:10.319><c> so</c><00:06:10.479><c> let's</c><00:06:10.680><c> go</c><00:06:10.840><c> back</c><00:06:10.960><c> and</c>

00:06:11.110 --> 00:06:11.120 align:start position:0%
kind of API key so let's go back and
 

00:06:11.120 --> 00:06:13.749 align:start position:0%
kind of API key so let's go back and
look<00:06:11.240><c> at</c><00:06:11.440><c> how</c><00:06:11.560><c> you</c><00:06:11.680><c> would</c><00:06:12.080><c> get</c><00:06:12.240><c> an</c><00:06:12.440><c> API</c><00:06:12.919><c> key</c>

00:06:13.749 --> 00:06:13.759 align:start position:0%
look at how you would get an API key
 

00:06:13.759 --> 00:06:15.350 align:start position:0%
look at how you would get an API key
okay<00:06:13.880><c> so</c><00:06:14.039><c> if</c><00:06:14.120><c> you're</c><00:06:14.240><c> in</c><00:06:14.400><c> makeer</c><00:06:14.759><c> Suite</c><00:06:15.240><c> you</c>

00:06:15.350 --> 00:06:15.360 align:start position:0%
okay so if you're in makeer Suite you
 

00:06:15.360 --> 00:06:17.790 align:start position:0%
okay so if you're in makeer Suite you
just<00:06:15.520><c> come</c><00:06:15.680><c> down</c><00:06:15.840><c> to</c><00:06:16.000><c> the</c><00:06:16.160><c> Keys</c><00:06:16.599><c> area</c><00:06:17.560><c> I'm</c><00:06:17.680><c> not</c>

00:06:17.790 --> 00:06:17.800 align:start position:0%
just come down to the Keys area I'm not
 

00:06:17.800 --> 00:06:19.749 align:start position:0%
just come down to the Keys area I'm not
going<00:06:17.919><c> to</c><00:06:18.080><c> save</c><00:06:18.440><c> that</c><00:06:18.960><c> you</c><00:06:19.039><c> can</c><00:06:19.240><c> come</c><00:06:19.360><c> and</c><00:06:19.560><c> get</c>

00:06:19.749 --> 00:06:19.759 align:start position:0%
going to save that you can come and get
 

00:06:19.759 --> 00:06:23.029 align:start position:0%
going to save that you can come and get
your<00:06:20.000><c> API</c><00:06:20.560><c> keys</c><00:06:20.919><c> for</c><00:06:21.240><c> this</c><00:06:21.880><c> so</c><00:06:22.360><c> here</c><00:06:22.720><c> I</c><00:06:22.840><c> can</c>

00:06:23.029 --> 00:06:23.039 align:start position:0%
your API keys for this so here I can
 

00:06:23.039 --> 00:06:26.350 align:start position:0%
your API keys for this so here I can
make<00:06:23.720><c> a</c><00:06:23.880><c> new</c><00:06:24.199><c> API</c><00:06:24.720><c> key</c><00:06:25.280><c> and</c><00:06:25.400><c> it</c><00:06:25.520><c> will</c><00:06:25.759><c> basically</c>

00:06:26.350 --> 00:06:26.360 align:start position:0%
make a new API key and it will basically
 

00:06:26.360 --> 00:06:29.510 align:start position:0%
make a new API key and it will basically
make<00:06:26.560><c> a</c><00:06:26.720><c> new</c><00:06:26.960><c> API</c><00:06:27.440><c> key</c><00:06:28.280><c> which</c><00:06:28.400><c> is</c><00:06:28.599><c> unique</c><00:06:28.919><c> to</c><00:06:29.039><c> my</c>

00:06:29.510 --> 00:06:29.520 align:start position:0%
make a new API key which is unique to my
 

00:06:29.520 --> 00:06:31.749 align:start position:0%
make a new API key which is unique to my
account<00:06:30.240><c> and</c><00:06:30.360><c> then</c><00:06:30.479><c> I</c><00:06:30.599><c> get</c><00:06:30.800><c> the</c><00:06:31.039><c> API</c><00:06:31.440><c> key</c><00:06:31.680><c> you</c>

00:06:31.749 --> 00:06:31.759 align:start position:0%
account and then I get the API key you
 

00:06:31.759 --> 00:06:33.830 align:start position:0%
account and then I get the API key you
can<00:06:31.880><c> see</c><00:06:32.120><c> obviously</c><00:06:32.440><c> I</c><00:06:32.520><c> can</c><00:06:32.759><c> kill</c><00:06:33.280><c> some</c><00:06:33.440><c> of</c><00:06:33.560><c> my</c>

00:06:33.830 --> 00:06:33.840 align:start position:0%
can see obviously I can kill some of my
 

00:06:33.840 --> 00:06:37.189 align:start position:0%
can see obviously I can kill some of my
other<00:06:34.199><c> API</c><00:06:34.680><c> Keys</c><00:06:35.080><c> Etc</c><00:06:35.720><c> as</c><00:06:35.840><c> I</c><00:06:35.960><c> go</c><00:06:36.160><c> through</c><00:06:36.479><c> this</c>

00:06:37.189 --> 00:06:37.199 align:start position:0%
other API Keys Etc as I go through this
 

00:06:37.199 --> 00:06:39.189 align:start position:0%
other API Keys Etc as I go through this
now<00:06:37.360><c> I</c><00:06:37.520><c> come</c><00:06:37.720><c> back</c><00:06:37.840><c> to</c><00:06:38.160><c> the</c><00:06:38.280><c> Google</c><00:06:38.639><c> collab</c>

00:06:39.189 --> 00:06:39.199 align:start position:0%
now I come back to the Google collab
 

00:06:39.199 --> 00:06:41.270 align:start position:0%
now I come back to the Google collab
that<00:06:39.520><c> it</c><00:06:39.680><c> gave</c><00:06:39.919><c> me</c><00:06:40.280><c> I've</c><00:06:40.440><c> now</c><00:06:40.639><c> pasted</c><00:06:40.960><c> in</c><00:06:41.120><c> my</c>

00:06:41.270 --> 00:06:41.280 align:start position:0%
that it gave me I've now pasted in my
 

00:06:41.280 --> 00:06:44.230 align:start position:0%
that it gave me I've now pasted in my
API<00:06:41.759><c> key</c><00:06:42.560><c> once</c><00:06:42.759><c> you</c><00:06:43.080><c> install</c><00:06:43.720><c> this</c><00:06:43.919><c> Google</c>

00:06:44.230 --> 00:06:44.240 align:start position:0%
API key once you install this Google
 

00:06:44.240 --> 00:06:46.749 align:start position:0%
API key once you install this Google
generative<00:06:44.880><c> AI</c><00:06:45.880><c> package</c><00:06:46.240><c> you</c><00:06:46.400><c> actually</c><00:06:46.599><c> need</c>

00:06:46.749 --> 00:06:46.759 align:start position:0%
generative AI package you actually need
 

00:06:46.759 --> 00:06:48.670 align:start position:0%
generative AI package you actually need
to<00:06:46.919><c> restart</c><00:06:47.440><c> the</c><00:06:47.599><c> collab</c><00:06:48.160><c> so</c><00:06:48.319><c> that's</c><00:06:48.479><c> one</c><00:06:48.599><c> of</c>

00:06:48.670 --> 00:06:48.680 align:start position:0%
to restart the collab so that's one of
 

00:06:48.680 --> 00:06:50.309 align:start position:0%
to restart the collab so that's one of
the<00:06:48.840><c> key</c><00:06:49.000><c> things</c><00:06:49.160><c> you</c><00:06:49.280><c> want</c><00:06:49.400><c> to</c><00:06:49.520><c> do</c><00:06:49.800><c> there</c><00:06:50.199><c> okay</c>

00:06:50.309 --> 00:06:50.319 align:start position:0%
the key things you want to do there okay
 

00:06:50.319 --> 00:06:53.230 align:start position:0%
the key things you want to do there okay
so<00:06:50.560><c> now</c><00:06:50.720><c> we</c><00:06:50.840><c> can</c><00:06:51.039><c> see</c><00:06:51.960><c> the</c><00:06:52.479><c> settings</c><00:06:52.960><c> that</c><00:06:53.080><c> we</c>

00:06:53.230 --> 00:06:53.240 align:start position:0%
so now we can see the settings that we
 

00:06:53.240 --> 00:06:56.350 align:start position:0%
so now we can see the settings that we
have<00:06:53.520><c> before</c><00:06:54.080><c> in</c><00:06:54.440><c> the</c><00:06:54.720><c> guy</c><00:06:55.199><c> interface</c><00:06:56.080><c> we've</c>

00:06:56.350 --> 00:06:56.360 align:start position:0%
have before in the guy interface we've
 

00:06:56.360 --> 00:06:58.629 align:start position:0%
have before in the guy interface we've
now<00:06:56.599><c> actually</c><00:06:56.879><c> got</c><00:06:57.160><c> them</c><00:06:57.720><c> in</c><00:06:57.960><c> here</c><00:06:58.199><c> so</c><00:06:58.360><c> we</c><00:06:58.479><c> can</c>

00:06:58.629 --> 00:06:58.639 align:start position:0%
now actually got them in here so we can
 

00:06:58.639 --> 00:07:00.869 align:start position:0%
now actually got them in here so we can
basically<00:06:59.000><c> pass</c><00:06:59.199><c> this</c><00:06:59.440><c> this</c><00:06:59.599><c> in</c><00:07:00.199><c> so</c><00:07:00.520><c> we</c><00:07:00.720><c> just</c>

00:07:00.869 --> 00:07:00.879 align:start position:0%
basically pass this this in so we just
 

00:07:00.879 --> 00:07:05.110 align:start position:0%
basically pass this this in so we just
set<00:07:01.120><c> up</c><00:07:01.599><c> our</c><00:07:01.919><c> model</c><00:07:02.680><c> with</c><00:07:02.840><c> our</c><00:07:03.120><c> prompt</c><00:07:04.120><c> here</c>

00:07:05.110 --> 00:07:05.120 align:start position:0%
set up our model with our prompt here
 

00:07:05.120 --> 00:07:06.950 align:start position:0%
set up our model with our prompt here
now<00:07:05.280><c> they</c><00:07:05.400><c> have</c><00:07:05.520><c> a</c><00:07:05.720><c> function</c><00:07:06.039><c> in</c><00:07:06.280><c> here</c><00:07:06.680><c> for</c>

00:07:06.950 --> 00:07:06.960 align:start position:0%
now they have a function in here for
 

00:07:06.960 --> 00:07:09.990 align:start position:0%
now they have a function in here for
converting<00:07:07.520><c> the</c><00:07:07.720><c> text</c><00:07:08.080><c> to</c><00:07:08.479><c> base</c><00:07:08.800><c> 64</c><00:07:09.800><c> but</c><00:07:09.919><c> you</c>

00:07:09.990 --> 00:07:10.000 align:start position:0%
converting the text to base 64 but you
 

00:07:10.000 --> 00:07:11.309 align:start position:0%
converting the text to base 64 but you
can<00:07:10.120><c> see</c><00:07:10.319><c> this</c><00:07:10.400><c> is</c><00:07:10.560><c> basically</c><00:07:10.960><c> the</c><00:07:11.080><c> same</c>

00:07:11.309 --> 00:07:11.319 align:start position:0%
can see this is basically the same
 

00:07:11.319 --> 00:07:13.869 align:start position:0%
can see this is basically the same
prompt<00:07:11.680><c> as</c><00:07:11.840><c> what</c><00:07:11.960><c> we</c><00:07:12.120><c> had</c><00:07:12.400><c> here</c><00:07:13.160><c> where</c><00:07:13.440><c> we've</c>

00:07:13.869 --> 00:07:13.879 align:start position:0%
prompt as what we had here where we've
 

00:07:13.879 --> 00:07:16.309 align:start position:0%
prompt as what we had here where we've
gone<00:07:14.199><c> through</c><00:07:14.639><c> we've</c><00:07:14.840><c> gotten</c><00:07:15.120><c> up</c><00:07:15.360><c> to</c><00:07:15.919><c> uh</c><00:07:16.039><c> Yan</c>

00:07:16.309 --> 00:07:16.319 align:start position:0%
gone through we've gotten up to uh Yan
 

00:07:16.319 --> 00:07:18.749 align:start position:0%
gone through we've gotten up to uh Yan
Lun<00:07:16.800><c> and</c><00:07:16.879><c> we're</c><00:07:17.000><c> going</c><00:07:17.120><c> to</c><00:07:17.280><c> predict</c><00:07:17.960><c> for</c><00:07:18.280><c> him</c>

00:07:18.749 --> 00:07:18.759 align:start position:0%
Lun and we're going to predict for him
 

00:07:18.759 --> 00:07:20.629 align:start position:0%
Lun and we're going to predict for him
if<00:07:18.879><c> we</c><00:07:19.000><c> come</c><00:07:19.199><c> down</c><00:07:19.360><c> here</c><00:07:19.479><c> and</c><00:07:19.680><c> rerun</c><00:07:20.160><c> that</c><00:07:20.560><c> we</c>

00:07:20.629 --> 00:07:20.639 align:start position:0%
if we come down here and rerun that we
 

00:07:20.639 --> 00:07:22.270 align:start position:0%
if we come down here and rerun that we
can<00:07:20.759><c> see</c><00:07:20.919><c> sure</c><00:07:21.160><c> enough</c><00:07:21.440><c> it's</c><00:07:21.560><c> going</c><00:07:21.680><c> to</c><00:07:21.840><c> Output</c>

00:07:22.270 --> 00:07:22.280 align:start position:0%
can see sure enough it's going to Output
 

00:07:22.280 --> 00:07:24.830 align:start position:0%
can see sure enough it's going to Output
the<00:07:22.479><c> result</c><00:07:23.080><c> for</c><00:07:23.360><c> here</c><00:07:23.639><c> so</c><00:07:23.960><c> we</c><00:07:24.039><c> could</c><00:07:24.280><c> write</c><00:07:24.720><c> a</c>

00:07:24.830 --> 00:07:24.840 align:start position:0%
the result for here so we could write a
 

00:07:24.840 --> 00:07:27.150 align:start position:0%
the result for here so we could write a
simple<00:07:25.199><c> function</c><00:07:25.680><c> here</c><00:07:25.879><c> to</c><00:07:26.160><c> take</c><00:07:26.400><c> this</c><00:07:26.599><c> in</c>

00:07:27.150 --> 00:07:27.160 align:start position:0%
simple function here to take this in
 

00:07:27.160 --> 00:07:30.710 align:start position:0%
simple function here to take this in
take<00:07:27.520><c> our</c><00:07:27.840><c> text</c><00:07:28.759><c> with</c><00:07:28.960><c> this</c><00:07:29.360><c> input</c><00:07:29.680><c> output</c><00:07:30.120><c> Etc</c>

00:07:30.710 --> 00:07:30.720 align:start position:0%
take our text with this input output Etc
 

00:07:30.720 --> 00:07:32.270 align:start position:0%
take our text with this input output Etc
put<00:07:30.879><c> in</c><00:07:31.039><c> someone's</c><00:07:31.440><c> name</c><00:07:31.680><c> and</c><00:07:31.800><c> then</c><00:07:31.960><c> be</c><00:07:32.080><c> able</c>

00:07:32.270 --> 00:07:32.280 align:start position:0%
put in someone's name and then be able
 

00:07:32.280 --> 00:07:35.869 align:start position:0%
put in someone's name and then be able
to<00:07:32.440><c> generate</c><00:07:32.960><c> the</c><00:07:33.160><c> house</c><00:07:33.720><c> for</c><00:07:34.479><c> this</c><00:07:35.479><c> so</c><00:07:35.639><c> if</c><00:07:35.720><c> you</c>

00:07:35.869 --> 00:07:35.879 align:start position:0%
to generate the house for this so if you
 

00:07:35.879 --> 00:07:39.070 align:start position:0%
to generate the house for this so if you
haven't<00:07:36.120><c> tried</c><00:07:36.440><c> out</c><00:07:36.759><c> the</c><00:07:36.919><c> palm</c><00:07:37.599><c> apis</c><00:07:38.599><c> and</c>

00:07:39.070 --> 00:07:39.080 align:start position:0%
haven't tried out the palm apis and
 

00:07:39.080 --> 00:07:40.909 align:start position:0%
haven't tried out the palm apis and
you're<00:07:39.400><c> interested</c><00:07:39.759><c> in</c><00:07:39.960><c> making</c><00:07:40.319><c> some</c><00:07:40.560><c> kind</c><00:07:40.680><c> of</c>

00:07:40.909 --> 00:07:40.919 align:start position:0%
you're interested in making some kind of
 

00:07:40.919 --> 00:07:43.550 align:start position:0%
you're interested in making some kind of
app<00:07:41.240><c> that</c><00:07:41.479><c> pings</c><00:07:41.960><c> an</c><00:07:42.199><c> API</c><00:07:43.199><c> and</c><00:07:43.280><c> you</c><00:07:43.319><c> want</c><00:07:43.440><c> to</c>

00:07:43.550 --> 00:07:43.560 align:start position:0%
app that pings an API and you want to
 

00:07:43.560 --> 00:07:45.149 align:start position:0%
app that pings an API and you want to
play<00:07:43.720><c> around</c><00:07:44.000><c> with</c><00:07:44.159><c> it</c><00:07:44.440><c> you</c><00:07:44.560><c> know</c><00:07:44.759><c> like</c><00:07:44.919><c> I</c><00:07:45.080><c> I</c>

00:07:45.149 --> 00:07:45.159 align:start position:0%
play around with it you know like I I
 

00:07:45.159 --> 00:07:47.110 align:start position:0%
play around with it you know like I I
mentioned<00:07:45.560><c> currently</c><00:07:46.120><c> this</c><00:07:46.240><c> is</c><00:07:46.680><c> available</c>

00:07:47.110 --> 00:07:47.120 align:start position:0%
mentioned currently this is available
 

00:07:47.120 --> 00:07:49.869 align:start position:0%
mentioned currently this is available
for<00:07:47.360><c> free</c><00:07:48.039><c> people</c><00:07:48.280><c> can</c><00:07:48.479><c> try</c><00:07:48.879><c> this</c><00:07:49.039><c> out</c><00:07:49.720><c> my</c>

00:07:49.869 --> 00:07:49.879 align:start position:0%
for free people can try this out my
 

00:07:49.879 --> 00:07:52.309 align:start position:0%
for free people can try this out my
guess<00:07:50.039><c> is</c><00:07:50.360><c> at</c><00:07:50.520><c> some</c><00:07:50.759><c> point</c><00:07:51.240><c> they</c><00:07:51.360><c> will</c><00:07:52.080><c> have</c>

00:07:52.309 --> 00:07:52.319 align:start position:0%
guess is at some point they will have
 

00:07:52.319 --> 00:07:54.070 align:start position:0%
guess is at some point they will have
some<00:07:52.599><c> kind</c><00:07:52.720><c> of</c><00:07:52.840><c> fee</c><00:07:53.120><c> structure</c><00:07:53.599><c> or</c><00:07:53.759><c> something</c>

00:07:54.070 --> 00:07:54.080 align:start position:0%
some kind of fee structure or something
 

00:07:54.080 --> 00:07:55.950 align:start position:0%
some kind of fee structure or something
like<00:07:54.319><c> that</c><00:07:54.840><c> and</c><00:07:55.000><c> I'm</c><00:07:55.120><c> not</c><00:07:55.240><c> sure</c><00:07:55.520><c> how</c><00:07:55.680><c> much</c><00:07:55.840><c> you</c>

00:07:55.950 --> 00:07:55.960 align:start position:0%
like that and I'm not sure how much you
 

00:07:55.960 --> 00:07:57.869 align:start position:0%
like that and I'm not sure how much you
can<00:07:56.159><c> Hammer</c><00:07:56.479><c> the</c><00:07:56.639><c> API</c><00:07:57.120><c> before</c><00:07:57.440><c> you</c><00:07:57.639><c> get</c>

00:07:57.869 --> 00:07:57.879 align:start position:0%
can Hammer the API before you get
 

00:07:57.879 --> 00:08:00.029 align:start position:0%
can Hammer the API before you get
stopped<00:07:58.440><c> at</c><00:07:58.599><c> the</c><00:07:58.720><c> moment</c><00:07:59.400><c> but</c><00:07:59.560><c> certainly</c><00:07:59.919><c> it's</c>

00:08:00.029 --> 00:08:00.039 align:start position:0%
stopped at the moment but certainly it's
 

00:08:00.039 --> 00:08:01.950 align:start position:0%
stopped at the moment but certainly it's
worth<00:08:00.319><c> trying</c><00:08:00.639><c> out</c><00:08:00.879><c> to</c><00:08:01.199><c> just</c><00:08:01.360><c> check</c><00:08:01.560><c> out</c><00:08:01.840><c> and</c>

00:08:01.950 --> 00:08:01.960 align:start position:0%
worth trying out to just check out and
 

00:08:01.960 --> 00:08:04.790 align:start position:0%
worth trying out to just check out and
see<00:08:02.440><c> okay</c><00:08:02.879><c> what</c><00:08:03.039><c> can</c><00:08:03.199><c> the</c><00:08:03.360><c> Palm</c><00:08:03.639><c> API</c><00:08:04.159><c> do</c>

00:08:04.790 --> 00:08:04.800 align:start position:0%
see okay what can the Palm API do
 

00:08:04.800 --> 00:08:06.990 align:start position:0%
see okay what can the Palm API do
definitely<00:08:05.280><c> the</c><00:08:05.680><c> current</c><00:08:06.080><c> Palm</c><00:08:06.400><c> bison</c><00:08:06.800><c> so</c><00:08:06.919><c> if</c>

00:08:06.990 --> 00:08:07.000 align:start position:0%
definitely the current Palm bison so if
 

00:08:07.000 --> 00:08:08.990 align:start position:0%
definitely the current Palm bison so if
you<00:08:07.080><c> remember</c><00:08:07.440><c> the</c><00:08:07.560><c> Palm</c><00:08:07.879><c> bison</c><00:08:08.319><c> model</c><00:08:08.680><c> is</c><00:08:08.840><c> not</c>

00:08:08.990 --> 00:08:09.000 align:start position:0%
you remember the Palm bison model is not
 

00:08:09.000 --> 00:08:12.309 align:start position:0%
you remember the Palm bison model is not
the<00:08:09.159><c> full-size</c><00:08:09.919><c> Palm</c><00:08:10.560><c> API</c><00:08:11.560><c> it's</c><00:08:11.800><c> the</c><00:08:11.919><c> one</c>

00:08:12.309 --> 00:08:12.319 align:start position:0%
the full-size Palm API it's the one
 

00:08:12.319 --> 00:08:15.869 align:start position:0%
the full-size Palm API it's the one
below<00:08:12.840><c> that</c><00:08:13.599><c> but</c><00:08:13.759><c> it</c><00:08:13.879><c> allows</c><00:08:14.240><c> you</c><00:08:14.440><c> to</c><00:08:14.960><c> have</c><00:08:15.440><c> a</c>

00:08:15.869 --> 00:08:15.879 align:start position:0%
below that but it allows you to have a
 

00:08:15.879 --> 00:08:17.950 align:start position:0%
below that but it allows you to have a
test<00:08:16.280><c> of</c><00:08:16.599><c> playing</c><00:08:16.879><c> around</c><00:08:17.199><c> with</c><00:08:17.440><c> this</c><00:08:17.800><c> and</c>

00:08:17.950 --> 00:08:17.960 align:start position:0%
test of playing around with this and
 

00:08:17.960 --> 00:08:19.430 align:start position:0%
test of playing around with this and
it's<00:08:18.080><c> very</c><00:08:18.280><c> simple</c><00:08:18.520><c> to</c><00:08:18.680><c> use</c><00:08:18.919><c> and</c><00:08:19.080><c> to</c><00:08:19.240><c> get</c>

00:08:19.430 --> 00:08:19.440 align:start position:0%
it's very simple to use and to get
 

00:08:19.440 --> 00:08:22.110 align:start position:0%
it's very simple to use and to get
started<00:08:19.919><c> with</c><00:08:20.599><c> so</c><00:08:20.840><c> check</c><00:08:21.000><c> it</c><00:08:21.159><c> out</c><00:08:21.720><c> as</c><00:08:21.879><c> always</c>

00:08:22.110 --> 00:08:22.120 align:start position:0%
started with so check it out as always
 

00:08:22.120 --> 00:08:23.589 align:start position:0%
started with so check it out as always
if<00:08:22.199><c> you've</c><00:08:22.360><c> got</c><00:08:22.520><c> any</c><00:08:22.759><c> questions</c><00:08:23.199><c> put</c><00:08:23.400><c> them</c><00:08:23.479><c> in</c>

00:08:23.589 --> 00:08:23.599 align:start position:0%
if you've got any questions put them in
 

00:08:23.599 --> 00:08:25.510 align:start position:0%
if you've got any questions put them in
the<00:08:23.680><c> comments</c><00:08:24.080><c> below</c><00:08:24.919><c> if</c><00:08:25.000><c> you</c><00:08:25.120><c> found</c><00:08:25.360><c> this</c>

00:08:25.510 --> 00:08:25.520 align:start position:0%
the comments below if you found this
 

00:08:25.520 --> 00:08:27.309 align:start position:0%
the comments below if you found this
video<00:08:25.919><c> useful</c><00:08:26.440><c> please</c><00:08:26.680><c> click</c><00:08:26.960><c> like</c><00:08:27.120><c> And</c>

00:08:27.309 --> 00:08:27.319 align:start position:0%
video useful please click like And
 

00:08:27.319 --> 00:08:29.029 align:start position:0%
video useful please click like And
subscribe<00:08:28.159><c> I</c><00:08:28.240><c> will</c><00:08:28.400><c> talk</c><00:08:28.560><c> to</c><00:08:28.639><c> you</c><00:08:28.720><c> in</c><00:08:28.800><c> the</c><00:08:28.879><c> next</c>

00:08:29.029 --> 00:08:29.039 align:start position:0%
subscribe I will talk to you in the next
 

00:08:29.039 --> 00:08:30.550 align:start position:0%
subscribe I will talk to you in the next
video<00:08:29.680><c> bye</c><00:08:29.840><c> for</c>

00:08:30.550 --> 00:08:30.560 align:start position:0%
video bye for
 

00:08:30.560 --> 00:08:33.560 align:start position:0%
video bye for
now

