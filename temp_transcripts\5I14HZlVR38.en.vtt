WEBVTT
Kind: captions
Language: en

00:00:00.179 --> 00:00:02.389 align:start position:0%
 
web<00:00:00.900><c> applications</c><00:00:01.380><c> are</c><00:00:01.740><c> vital</c><00:00:01.979><c> for</c><00:00:02.280><c> everyday</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
web applications are vital for everyday
 

00:00:02.399 --> 00:00:05.090 align:start position:0%
web applications are vital for everyday
life<00:00:02.820><c> from</c><00:00:03.300><c> shopping</c><00:00:03.659><c> and</c><00:00:04.200><c> remote</c><00:00:04.380><c> work</c><00:00:04.740><c> to</c>

00:00:05.090 --> 00:00:05.100 align:start position:0%
life from shopping and remote work to
 

00:00:05.100 --> 00:00:07.909 align:start position:0%
life from shopping and remote work to
life-saving<00:00:05.940><c> services</c><00:00:06.120><c> in</c><00:00:06.720><c> hospitals</c><00:00:07.259><c> and</c>

00:00:07.909 --> 00:00:07.919 align:start position:0%
life-saving services in hospitals and
 

00:00:07.919 --> 00:00:09.890 align:start position:0%
life-saving services in hospitals and
critical<00:00:08.280><c> infrastructure</c><00:00:08.940><c> large</c>

00:00:09.890 --> 00:00:09.900 align:start position:0%
critical infrastructure large
 

00:00:09.900 --> 00:00:11.750 align:start position:0%
critical infrastructure large
Enterprises<00:00:10.559><c> have</c><00:00:10.980><c> numerous</c><00:00:11.460><c> custom</c>

00:00:11.750 --> 00:00:11.760 align:start position:0%
Enterprises have numerous custom
 

00:00:11.760 --> 00:00:14.570 align:start position:0%
Enterprises have numerous custom
applications<00:00:12.360><c> deployed</c><00:00:13.139><c> and</c><00:00:13.500><c> in</c><00:00:13.740><c> development</c>

00:00:14.570 --> 00:00:14.580 align:start position:0%
applications deployed and in development
 

00:00:14.580 --> 00:00:16.609 align:start position:0%
applications deployed and in development
a<00:00:15.000><c> gardener</c><00:00:15.360><c> report</c><00:00:15.540><c> predicts</c><00:00:16.199><c> that</c><00:00:16.320><c> supply</c>

00:00:16.609 --> 00:00:16.619 align:start position:0%
a gardener report predicts that supply
 

00:00:16.619 --> 00:00:19.430 align:start position:0%
a gardener report predicts that supply
chain<00:00:16.920><c> attacks</c><00:00:17.400><c> will</c><00:00:17.640><c> impact</c><00:00:18.000><c> 45</c><00:00:18.480><c> percent</c><00:00:19.020><c> of</c>

00:00:19.430 --> 00:00:19.440 align:start position:0%
chain attacks will impact 45 percent of
 

00:00:19.440 --> 00:00:22.550 align:start position:0%
chain attacks will impact 45 percent of
global<00:00:19.800><c> organizations</c><00:00:20.340><c> by</c><00:00:21.119><c> 2025.</c>

00:00:22.550 --> 00:00:22.560 align:start position:0%
global organizations by 2025.
 

00:00:22.560 --> 00:00:25.189 align:start position:0%
global organizations by 2025.
applications<00:00:23.400><c> remain</c><00:00:24.060><c> a</c><00:00:24.240><c> top</c><00:00:24.420><c> attack</c><00:00:24.660><c> Vector</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
applications remain a top attack Vector
 

00:00:25.199 --> 00:00:28.130 align:start position:0%
applications remain a top attack Vector
contributing<00:00:26.160><c> to</c><00:00:26.340><c> over</c><00:00:26.519><c> 40</c><00:00:26.939><c> percent</c><00:00:27.300><c> of</c><00:00:27.720><c> data</c>

00:00:28.130 --> 00:00:28.140 align:start position:0%
contributing to over 40 percent of data
 

00:00:28.140 --> 00:00:30.529 align:start position:0%
contributing to over 40 percent of data
breaches<00:00:28.500><c> according</c><00:00:28.980><c> to</c><00:00:29.160><c> the</c><00:00:29.400><c> 2022</c><00:00:30.119><c> Verizon</c>

00:00:30.529 --> 00:00:30.539 align:start position:0%
breaches according to the 2022 Verizon
 

00:00:30.539 --> 00:00:32.990 align:start position:0%
breaches according to the 2022 Verizon
data<00:00:31.199><c> breach</c><00:00:31.500><c> investigation</c><00:00:31.980><c> report</c>

00:00:32.990 --> 00:00:33.000 align:start position:0%
data breach investigation report
 

00:00:33.000 --> 00:00:35.450 align:start position:0%
data breach investigation report
preventing<00:00:33.840><c> attacks</c><00:00:34.320><c> and</c><00:00:34.559><c> securing</c><00:00:35.040><c> software</c>

00:00:35.450 --> 00:00:35.460 align:start position:0%
preventing attacks and securing software
 

00:00:35.460 --> 00:00:38.209 align:start position:0%
preventing attacks and securing software
is<00:00:35.940><c> challenging</c><00:00:36.420><c> due</c><00:00:37.200><c> to</c><00:00:37.320><c> evolving</c><00:00:37.680><c> attacker</c>

00:00:38.209 --> 00:00:38.219 align:start position:0%
is challenging due to evolving attacker
 

00:00:38.219 --> 00:00:39.830 align:start position:0%
is challenging due to evolving attacker
tactics<00:00:38.579><c> Reliance</c><00:00:39.239><c> on</c><00:00:39.420><c> vulnerable</c>

00:00:39.830 --> 00:00:39.840 align:start position:0%
tactics Reliance on vulnerable
 

00:00:39.840 --> 00:00:42.590 align:start position:0%
tactics Reliance on vulnerable
third-party<00:00:40.620><c> software</c><00:00:40.980><c> staffing</c><00:00:41.879><c> issues</c><00:00:42.300><c> and</c>

00:00:42.590 --> 00:00:42.600 align:start position:0%
third-party software staffing issues and
 

00:00:42.600 --> 00:00:44.930 align:start position:0%
third-party software staffing issues and
a<00:00:42.780><c> lack</c><00:00:42.960><c> of</c><00:00:43.140><c> secure</c><00:00:43.440><c> development</c><00:00:43.920><c> training</c>

00:00:44.930 --> 00:00:44.940 align:start position:0%
a lack of secure development training
 

00:00:44.940 --> 00:00:47.090 align:start position:0%
a lack of secure development training
traditional<00:00:45.719><c> application</c><00:00:46.140><c> security</c><00:00:46.500><c> testing</c>

00:00:47.090 --> 00:00:47.100 align:start position:0%
traditional application security testing
 

00:00:47.100 --> 00:00:49.250 align:start position:0%
traditional application security testing
Solutions<00:00:47.520><c> are</c><00:00:48.120><c> not</c><00:00:48.300><c> developer</c><00:00:48.899><c> friendly</c>

00:00:49.250 --> 00:00:49.260 align:start position:0%
Solutions are not developer friendly
 

00:00:49.260 --> 00:00:52.490 align:start position:0%
Solutions are not developer friendly
leading<00:00:50.100><c> to</c><00:00:50.219><c> disjointed</c><00:00:50.820><c> experiences</c><00:00:51.500><c> and</c>

00:00:52.490 --> 00:00:52.500 align:start position:0%
leading to disjointed experiences and
 

00:00:52.500 --> 00:00:54.290 align:start position:0%
leading to disjointed experiences and
the<00:00:52.620><c> release</c><00:00:52.800><c> of</c><00:00:53.100><c> vulnerable</c><00:00:53.520><c> code</c><00:00:53.820><c> to</c><00:00:54.059><c> meet</c>

00:00:54.290 --> 00:00:54.300 align:start position:0%
the release of vulnerable code to meet
 

00:00:54.300 --> 00:00:55.369 align:start position:0%
the release of vulnerable code to meet
deadlines

00:00:55.369 --> 00:00:55.379 align:start position:0%
deadlines
 

00:00:55.379 --> 00:00:57.170 align:start position:0%
deadlines
data<00:00:55.800><c> provides</c><00:00:56.100><c> an</c><00:00:56.340><c> alternative</c><00:00:56.699><c> solution</c>

00:00:57.170 --> 00:00:57.180 align:start position:0%
data provides an alternative solution
 

00:00:57.180 --> 00:01:00.049 align:start position:0%
data provides an alternative solution
empowering<00:00:58.079><c> devsecops</c><00:00:58.800><c> teams</c><00:00:59.100><c> to</c><00:00:59.579><c> prioritize</c>

00:01:00.049 --> 00:01:00.059 align:start position:0%
empowering devsecops teams to prioritize
 

00:01:00.059 --> 00:01:02.029 align:start position:0%
empowering devsecops teams to prioritize
Innovation<00:01:00.600><c> and</c><00:01:01.199><c> enhance</c><00:01:01.559><c> developer</c>

00:01:02.029 --> 00:01:02.039 align:start position:0%
Innovation and enhance developer
 

00:01:02.039 --> 00:01:04.969 align:start position:0%
Innovation and enhance developer
productivity<00:01:02.579><c> while</c><00:01:03.359><c> ensuring</c><00:01:03.960><c> security</c>

00:01:04.969 --> 00:01:04.979 align:start position:0%
productivity while ensuring security
 

00:01:04.979 --> 00:01:08.210 align:start position:0%
productivity while ensuring security
GitHub<00:01:05.700><c> Advanced</c><00:01:06.000><c> security</c><00:01:06.299><c> or</c><00:01:06.960><c> gas</c><00:01:07.200><c> is</c><00:01:07.979><c> an</c>

00:01:08.210 --> 00:01:08.220 align:start position:0%
GitHub Advanced security or gas is an
 

00:01:08.220 --> 00:01:10.130 align:start position:0%
GitHub Advanced security or gas is an
embedded<00:01:08.640><c> application</c><00:01:09.180><c> security</c><00:01:09.540><c> testing</c>

00:01:10.130 --> 00:01:10.140 align:start position:0%
embedded application security testing
 

00:01:10.140 --> 00:01:12.590 align:start position:0%
embedded application security testing
solution<00:01:10.560><c> that</c><00:01:11.340><c> seamlessly</c><00:01:11.880><c> integrates</c><00:01:12.299><c> with</c>

00:01:12.590 --> 00:01:12.600 align:start position:0%
solution that seamlessly integrates with
 

00:01:12.600 --> 00:01:14.690 align:start position:0%
solution that seamlessly integrates with
your<00:01:12.780><c> developer</c><00:01:13.200><c> workflow</c><00:01:13.680><c> it</c><00:01:14.340><c> performs</c>

00:01:14.690 --> 00:01:14.700 align:start position:0%
your developer workflow it performs
 

00:01:14.700 --> 00:01:16.969 align:start position:0%
your developer workflow it performs
automated<00:01:15.240><c> Security</c><00:01:15.659><c> checks</c><00:01:16.200><c> on</c><00:01:16.500><c> every</c><00:01:16.799><c> pull</c>

00:01:16.969 --> 00:01:16.979 align:start position:0%
automated Security checks on every pull
 

00:01:16.979 --> 00:01:18.530 align:start position:0%
automated Security checks on every pull
request<00:01:17.400><c> providing</c><00:01:18.060><c> real-time</c>

00:01:18.530 --> 00:01:18.540 align:start position:0%
request providing real-time
 

00:01:18.540 --> 00:01:20.090 align:start position:0%
request providing real-time
vulnerability<00:01:19.140><c> feedback</c><00:01:19.619><c> for</c><00:01:19.920><c> quick</c>

00:01:20.090 --> 00:01:20.100 align:start position:0%
vulnerability feedback for quick
 

00:01:20.100 --> 00:01:21.170 align:start position:0%
vulnerability feedback for quick
resolution

00:01:21.170 --> 00:01:21.180 align:start position:0%
resolution
 

00:01:21.180 --> 00:01:23.749 align:start position:0%
resolution
but<00:01:21.780><c> guys</c><00:01:22.080><c> you</c><00:01:22.439><c> can</c><00:01:22.560><c> keep</c><00:01:22.740><c> your</c><00:01:22.979><c> code</c><00:01:23.220><c> supply</c>

00:01:23.749 --> 00:01:23.759 align:start position:0%
but guys you can keep your code supply
 

00:01:23.759 --> 00:01:26.810 align:start position:0%
but guys you can keep your code supply
chain<00:01:24.119><c> and</c><00:01:24.900><c> secret</c><00:01:25.140><c> secure</c><00:01:25.799><c> before</c><00:01:26.280><c> deploying</c>

00:01:26.810 --> 00:01:26.820 align:start position:0%
chain and secret secure before deploying
 

00:01:26.820 --> 00:01:27.950 align:start position:0%
chain and secret secure before deploying
to<00:01:26.939><c> production</c>

00:01:27.950 --> 00:01:27.960 align:start position:0%
to production
 

00:01:27.960 --> 00:01:30.649 align:start position:0%
to production
it<00:01:28.439><c> offers</c><00:01:28.680><c> scalability</c><00:01:29.340><c> and</c><00:01:30.060><c> customization</c>

00:01:30.649 --> 00:01:30.659 align:start position:0%
it offers scalability and customization
 

00:01:30.659 --> 00:01:32.630 align:start position:0%
it offers scalability and customization
options<00:01:31.080><c> allowing</c><00:01:31.680><c> integration</c><00:01:32.159><c> with</c><00:01:32.460><c> open</c>

00:01:32.630 --> 00:01:32.640 align:start position:0%
options allowing integration with open
 

00:01:32.640 --> 00:01:34.969 align:start position:0%
options allowing integration with open
source<00:01:33.119><c> and</c><00:01:33.720><c> third-party</c><00:01:34.320><c> tools</c><00:01:34.619><c> of</c><00:01:34.799><c> your</c>

00:01:34.969 --> 00:01:34.979 align:start position:0%
source and third-party tools of your
 

00:01:34.979 --> 00:01:37.850 align:start position:0%
source and third-party tools of your
choice<00:01:35.220><c> gas</c><00:01:36.119><c> also</c><00:01:36.720><c> provides</c><00:01:37.079><c> security</c><00:01:37.320><c> teams</c>

00:01:37.850 --> 00:01:37.860 align:start position:0%
choice gas also provides security teams
 

00:01:37.860 --> 00:01:39.649 align:start position:0%
choice gas also provides security teams
with<00:01:38.100><c> visibility</c><00:01:38.520><c> into</c><00:01:38.939><c> organizational</c>

00:01:39.649 --> 00:01:39.659 align:start position:0%
with visibility into organizational
 

00:01:39.659 --> 00:01:42.230 align:start position:0%
with visibility into organizational
security<00:01:40.020><c> and</c><00:01:40.619><c> supply</c><00:01:40.860><c> chain</c><00:01:41.159><c> along</c><00:01:41.939><c> with</c>

00:01:42.230 --> 00:01:42.240 align:start position:0%
security and supply chain along with
 

00:01:42.240 --> 00:01:44.330 align:start position:0%
security and supply chain along with
access<00:01:42.420><c> to</c><00:01:42.840><c> curated</c><00:01:43.259><c> security</c><00:01:43.680><c> intelligence</c>

00:01:44.330 --> 00:01:44.340 align:start position:0%
access to curated security intelligence
 

00:01:44.340 --> 00:01:46.310 align:start position:0%
access to curated security intelligence
from<00:01:44.640><c> Global</c><00:01:45.060><c> developers</c><00:01:45.479><c> and</c><00:01:45.780><c> researchers</c>

00:01:46.310 --> 00:01:46.320 align:start position:0%
from Global developers and researchers
 

00:01:46.320 --> 00:01:50.000 align:start position:0%
from Global developers and researchers
click<00:01:47.159><c> here</c><00:01:47.280><c> to</c><00:01:47.579><c> learn</c><00:01:47.700><c> more</c>

