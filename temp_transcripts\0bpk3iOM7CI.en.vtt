WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.850 align:start position:0%
 
have<00:00:00.480><c> you</c><00:00:00.719><c> ever</c><00:00:00.840><c> wanted</c><00:00:01.079><c> to</c><00:00:01.319><c> standardize</c>

00:00:01.850 --> 00:00:01.860 align:start position:0%
have you ever wanted to standardize
 

00:00:01.860 --> 00:00:03.949 align:start position:0%
have you ever wanted to standardize
working<00:00:02.159><c> patterns</c><00:00:02.879><c> and</c><00:00:03.179><c> structures</c><00:00:03.540><c> within</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
working patterns and structures within
 

00:00:03.959 --> 00:00:05.030 align:start position:0%
working patterns and structures within
your<00:00:04.080><c> project</c>

00:00:05.030 --> 00:00:05.040 align:start position:0%
your project
 

00:00:05.040 --> 00:00:07.490 align:start position:0%
your project
how<00:00:05.460><c> about</c><00:00:05.580><c> sharing</c><00:00:06.120><c> best</c><00:00:06.359><c> practices</c><00:00:06.839><c> across</c>

00:00:07.490 --> 00:00:07.500 align:start position:0%
how about sharing best practices across
 

00:00:07.500 --> 00:00:10.730 align:start position:0%
how about sharing best practices across
teams<00:00:08.400><c> or</c><00:00:08.880><c> even</c><00:00:09.120><c> reducing</c><00:00:09.599><c> manual</c><00:00:10.080><c> effort</c><00:00:10.440><c> to</c>

00:00:10.730 --> 00:00:10.740 align:start position:0%
teams or even reducing manual effort to
 

00:00:10.740 --> 00:00:12.530 align:start position:0%
teams or even reducing manual effort to
get<00:00:10.860><c> started</c><00:00:11.219><c> with</c><00:00:11.400><c> a</c><00:00:11.519><c> new</c><00:00:11.639><c> project</c>

00:00:12.530 --> 00:00:12.540 align:start position:0%
get started with a new project
 

00:00:12.540 --> 00:00:14.749 align:start position:0%
get started with a new project
if<00:00:13.019><c> you</c><00:00:13.139><c> answered</c><00:00:13.500><c> yes</c><00:00:13.799><c> to</c><00:00:14.099><c> any</c><00:00:14.280><c> of</c><00:00:14.460><c> these</c>

00:00:14.749 --> 00:00:14.759 align:start position:0%
if you answered yes to any of these
 

00:00:14.759 --> 00:00:17.090 align:start position:0%
if you answered yes to any of these
questions<00:00:14.940><c> then</c><00:00:15.719><c> check</c><00:00:15.960><c> out</c><00:00:16.139><c> GitHub</c><00:00:16.619><c> projects</c>

00:00:17.090 --> 00:00:17.100 align:start position:0%
questions then check out GitHub projects
 

00:00:17.100 --> 00:00:18.890 align:start position:0%
questions then check out GitHub projects
organization<00:00:17.699><c> templates</c>

00:00:18.890 --> 00:00:18.900 align:start position:0%
organization templates
 

00:00:18.900 --> 00:00:21.109 align:start position:0%
organization templates
GitHub<00:00:19.560><c> projects</c><00:00:20.100><c> organization</c><00:00:20.580><c> templates</c>

00:00:21.109 --> 00:00:21.119 align:start position:0%
GitHub projects organization templates
 

00:00:21.119 --> 00:00:24.590 align:start position:0%
GitHub projects organization templates
help<00:00:21.600><c> you</c><00:00:21.720><c> create</c><00:00:22.080><c> save</c><00:00:22.980><c> and</c><00:00:23.699><c> reuse</c><00:00:24.060><c> projects</c>

00:00:24.590 --> 00:00:24.600 align:start position:0%
help you create save and reuse projects
 

00:00:24.600 --> 00:00:27.529 align:start position:0%
help you create save and reuse projects
with<00:00:24.960><c> templates</c><00:00:25.439><c> helping</c><00:00:26.279><c> you</c><00:00:26.400><c> save</c><00:00:26.640><c> time</c><00:00:27.000><c> and</c>

00:00:27.529 --> 00:00:27.539 align:start position:0%
with templates helping you save time and
 

00:00:27.539 --> 00:00:29.630 align:start position:0%
with templates helping you save time and
bring<00:00:27.779><c> consistency</c><00:00:28.320><c> to</c><00:00:28.859><c> managing</c><00:00:29.340><c> your</c>

00:00:29.630 --> 00:00:29.640 align:start position:0%
bring consistency to managing your
 

00:00:29.640 --> 00:00:30.769 align:start position:0%
bring consistency to managing your
projects

00:00:30.769 --> 00:00:30.779 align:start position:0%
projects
 

00:00:30.779 --> 00:00:33.470 align:start position:0%
projects
getting<00:00:31.500><c> started</c><00:00:31.920><c> is</c><00:00:32.279><c> simple</c><00:00:32.520><c> simply</c>

00:00:33.470 --> 00:00:33.480 align:start position:0%
getting started is simple simply
 

00:00:33.480 --> 00:00:35.870 align:start position:0%
getting started is simple simply
navigate<00:00:33.719><c> to</c><00:00:34.320><c> the</c><00:00:34.500><c> settings</c><00:00:34.680><c> page</c><00:00:35.160><c> of</c><00:00:35.640><c> an</c>

00:00:35.870 --> 00:00:35.880 align:start position:0%
navigate to the settings page of an
 

00:00:35.880 --> 00:00:37.870 align:start position:0%
navigate to the settings page of an
existing<00:00:36.360><c> GitHub</c><00:00:36.960><c> project</c>

00:00:37.870 --> 00:00:37.880 align:start position:0%
existing GitHub project
 

00:00:37.880 --> 00:00:41.389 align:start position:0%
existing GitHub project
admins<00:00:38.880><c> will</c><00:00:39.059><c> see</c><00:00:39.239><c> a</c><00:00:39.480><c> new</c><00:00:39.600><c> Option</c><00:00:40.020><c> called</c><00:00:40.860><c> make</c>

00:00:41.389 --> 00:00:41.399 align:start position:0%
admins will see a new Option called make
 

00:00:41.399 --> 00:00:42.590 align:start position:0%
admins will see a new Option called make
template

00:00:42.590 --> 00:00:42.600 align:start position:0%
template
 

00:00:42.600 --> 00:00:45.010 align:start position:0%
template
that<00:00:43.140><c> will</c><00:00:43.260><c> set</c><00:00:43.500><c> this</c><00:00:43.739><c> project</c><00:00:43.920><c> as</c><00:00:44.340><c> a</c><00:00:44.579><c> template</c>

00:00:45.010 --> 00:00:45.020 align:start position:0%
that will set this project as a template
 

00:00:45.020 --> 00:00:48.830 align:start position:0%
that will set this project as a template
simply<00:00:46.020><c> toggle</c><00:00:46.500><c> the</c><00:00:46.680><c> switch</c><00:00:46.920><c> to</c><00:00:47.460><c> on</c>

00:00:48.830 --> 00:00:48.840 align:start position:0%
simply toggle the switch to on
 

00:00:48.840 --> 00:00:52.069 align:start position:0%
simply toggle the switch to on
you<00:00:49.379><c> can</c><00:00:49.440><c> now</c><00:00:49.680><c> use</c><00:00:50.160><c> the</c><00:00:51.059><c> use</c><00:00:51.360><c> this</c><00:00:51.600><c> template</c>

00:00:52.069 --> 00:00:52.079 align:start position:0%
you can now use the use this template
 

00:00:52.079 --> 00:00:55.189 align:start position:0%
you can now use the use this template
button<00:00:52.559><c> to</c><00:00:53.280><c> create</c><00:00:53.399><c> a</c><00:00:53.760><c> new</c><00:00:53.820><c> project</c><00:00:54.059><c> based</c><00:00:54.960><c> on</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
button to create a new project based on
 

00:00:55.199 --> 00:00:56.990 align:start position:0%
button to create a new project based on
this<00:00:55.440><c> template</c>

00:00:56.990 --> 00:00:57.000 align:start position:0%
this template
 

00:00:57.000 --> 00:00:59.090 align:start position:0%
this template
want<00:00:57.539><c> a</c><00:00:57.840><c> list</c><00:00:57.960><c> of</c><00:00:58.079><c> all</c><00:00:58.260><c> the</c><00:00:58.379><c> templates</c><00:00:58.680><c> in</c><00:00:58.920><c> your</c>

00:00:59.090 --> 00:00:59.100 align:start position:0%
want a list of all the templates in your
 

00:00:59.100 --> 00:01:01.910 align:start position:0%
want a list of all the templates in your
organization<00:00:59.579><c> no</c><00:01:00.480><c> problem</c><00:01:00.719><c> simply</c><00:01:01.680><c> search</c>

00:01:01.910 --> 00:01:01.920 align:start position:0%
organization no problem simply search
 

00:01:01.920 --> 00:01:05.329 align:start position:0%
organization no problem simply search
for<00:01:02.160><c> is</c><00:01:02.340><c> template</c><00:01:02.940><c> on</c><00:01:03.420><c> the</c><00:01:03.600><c> projects</c><00:01:04.019><c> page</c>

00:01:05.329 --> 00:01:05.339 align:start position:0%
for is template on the projects page
 

00:01:05.339 --> 00:01:07.730 align:start position:0%
for is template on the projects page
when<00:01:06.000><c> creating</c><00:01:06.360><c> a</c><00:01:06.659><c> new</c><00:01:06.780><c> project</c><00:01:07.020><c> you</c><00:01:07.560><c> will</c>

00:01:07.730 --> 00:01:07.740 align:start position:0%
when creating a new project you will
 

00:01:07.740 --> 00:01:09.469 align:start position:0%
when creating a new project you will
also<00:01:08.040><c> be</c><00:01:08.159><c> able</c><00:01:08.400><c> to</c><00:01:08.520><c> see</c><00:01:08.880><c> all</c><00:01:09.240><c> the</c><00:01:09.360><c> available</c>

00:01:09.469 --> 00:01:09.479 align:start position:0%
also be able to see all the available
 

00:01:09.479 --> 00:01:12.109 align:start position:0%
also be able to see all the available
templates<00:01:10.260><c> within</c><00:01:11.159><c> the</c><00:01:11.400><c> sidebar</c><00:01:11.760><c> of</c>

00:01:12.109 --> 00:01:12.119 align:start position:0%
templates within the sidebar of
 

00:01:12.119 --> 00:01:14.390 align:start position:0%
templates within the sidebar of
organization<00:01:12.780><c> templates</c><00:01:13.260><c> try</c><00:01:13.799><c> out</c><00:01:13.979><c> GitHub</c>

00:01:14.390 --> 00:01:14.400 align:start position:0%
organization templates try out GitHub
 

00:01:14.400 --> 00:01:16.130 align:start position:0%
organization templates try out GitHub
projects<00:01:14.820><c> and</c><00:01:15.060><c> organization</c><00:01:15.659><c> templates</c>

00:01:16.130 --> 00:01:16.140 align:start position:0%
projects and organization templates
 

00:01:16.140 --> 00:01:18.380 align:start position:0%
projects and organization templates
today

