WEBVTT
Kind: captions
Language: en

00:00:01.560 --> 00:00:04.430 align:start position:0%
 
hey<00:00:01.760><c> everyone</c><00:00:02.320><c> Ravi</c><00:00:02.639><c> here</c><00:00:02.840><c> from</c><00:00:02.960><c> Lama</c><00:00:03.440><c> index</c>

00:00:04.430 --> 00:00:04.440 align:start position:0%
hey everyone Ravi here from <PERSON> index
 

00:00:04.440 --> 00:00:07.110 align:start position:0%
hey everyone Ravi here from <PERSON> index
um<00:00:04.720><c> so</c><00:00:05.040><c> in</c><00:00:05.200><c> this</c><00:00:05.359><c> video</c><00:00:05.920><c> we'll</c><00:00:06.200><c> look</c><00:00:06.399><c> into</c><00:00:06.960><c> uh</c>

00:00:07.110 --> 00:00:07.120 align:start position:0%
um so in this video we'll look into uh
 

00:00:07.120 --> 00:00:10.230 align:start position:0%
um so in this video we'll look into uh
stepwise<00:00:07.759><c> control</c><00:00:08.160><c> level</c><00:00:08.559><c> agent</c><00:00:09.559><c> so</c><00:00:09.840><c> this</c><00:00:10.000><c> is</c>

00:00:10.230 --> 00:00:10.240 align:start position:0%
stepwise control level agent so this is
 

00:00:10.240 --> 00:00:13.629 align:start position:0%
stepwise control level agent so this is
a<00:00:10.400><c> low</c><00:00:10.679><c> level</c><00:00:11.080><c> lower</c><00:00:11.400><c> level</c><00:00:12.040><c> API</c><00:00:13.040><c> um</c><00:00:13.480><c> that</c>

00:00:13.629 --> 00:00:13.639 align:start position:0%
a low level lower level API um that
 

00:00:13.639 --> 00:00:15.829 align:start position:0%
a low level lower level API um that
allows<00:00:14.320><c> you</c><00:00:14.519><c> to</c><00:00:14.719><c> step</c><00:00:15.040><c> through</c><00:00:15.320><c> and</c><00:00:15.519><c> control</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
allows you to step through and control
 

00:00:15.839 --> 00:00:19.950 align:start position:0%
allows you to step through and control
an<00:00:16.080><c> agent</c><00:00:16.840><c> on</c><00:00:17.000><c> a</c><00:00:17.160><c> more</c><00:00:17.880><c> uh</c><00:00:18.279><c> granular</c><00:00:18.960><c> approach</c>

00:00:19.950 --> 00:00:19.960 align:start position:0%
an agent on a more uh granular approach
 

00:00:19.960 --> 00:00:23.189 align:start position:0%
an agent on a more uh granular approach
so<00:00:20.199><c> basically</c><00:00:21.160><c> uh</c><00:00:21.279><c> in</c><00:00:21.439><c> these</c><00:00:21.920><c> cases</c><00:00:22.920><c> task</c>

00:00:23.189 --> 00:00:23.199 align:start position:0%
so basically uh in these cases task
 

00:00:23.199 --> 00:00:26.029 align:start position:0%
so basically uh in these cases task
creation<00:00:23.599><c> and</c><00:00:23.800><c> the</c><00:00:24.119><c> tax</c><00:00:24.920><c> execution</c><00:00:25.400><c> are</c>

00:00:26.029 --> 00:00:26.039 align:start position:0%
creation and the tax execution are
 

00:00:26.039 --> 00:00:30.029 align:start position:0%
creation and the tax execution are
separated<00:00:27.039><c> so</c><00:00:27.960><c> so</c><00:00:28.240><c> that</c><00:00:28.880><c> you</c><00:00:29.039><c> can</c><00:00:29.320><c> view</c><00:00:29.640><c> each</c>

00:00:30.029 --> 00:00:30.039 align:start position:0%
separated so so that you can view each
 

00:00:30.039 --> 00:00:32.749 align:start position:0%
separated so so that you can view each
step<00:00:30.840><c> and</c><00:00:31.359><c> uh</c><00:00:31.720><c> together</c><00:00:31.960><c> with</c><00:00:32.119><c> the</c><00:00:32.279><c> upcoming</c>

00:00:32.749 --> 00:00:32.759 align:start position:0%
step and uh together with the upcoming
 

00:00:32.759 --> 00:00:35.630 align:start position:0%
step and uh together with the upcoming
steps<00:00:33.200><c> as</c><00:00:33.360><c> well</c><00:00:34.160><c> so</c><00:00:34.480><c> we'll</c><00:00:35.120><c> uh</c><00:00:35.280><c> you</c><00:00:35.360><c> can</c>

00:00:35.630 --> 00:00:35.640 align:start position:0%
steps as well so we'll uh you can
 

00:00:35.640 --> 00:00:37.350 align:start position:0%
steps as well so we'll uh you can
actually<00:00:35.920><c> see</c><00:00:36.280><c> the</c><00:00:36.480><c> differentiation</c><00:00:37.160><c> when</c>

00:00:37.350 --> 00:00:37.360 align:start position:0%
actually see the differentiation when
 

00:00:37.360 --> 00:00:39.869 align:start position:0%
actually see the differentiation when
you<00:00:37.640><c> execute</c><00:00:38.079><c> it</c><00:00:38.280><c> directly</c><00:00:39.000><c> without</c><00:00:39.320><c> any</c><00:00:39.760><c> uh</c>

00:00:39.869 --> 00:00:39.879 align:start position:0%
you execute it directly without any uh
 

00:00:39.879 --> 00:00:43.150 align:start position:0%
you execute it directly without any uh
stepwise<00:00:40.440><c> execution</c><00:00:41.320><c> and</c><00:00:42.320><c> doing</c><00:00:42.680><c> stepwise</c>

00:00:43.150 --> 00:00:43.160 align:start position:0%
stepwise execution and doing stepwise
 

00:00:43.160 --> 00:00:46.069 align:start position:0%
stepwise execution and doing stepwise
execution<00:00:43.879><c> uh</c><00:00:44.039><c> in</c><00:00:44.200><c> this</c><00:00:44.520><c> notebook</c><00:00:45.520><c> so</c>

00:00:46.069 --> 00:00:46.079 align:start position:0%
execution uh in this notebook so
 

00:00:46.079 --> 00:00:50.430 align:start position:0%
execution uh in this notebook so
stepwise<00:00:47.079><c> U</c><00:00:47.719><c> controllable</c><00:00:48.280><c> agents</c><00:00:48.879><c> are</c><00:00:49.440><c> uh</c>

00:00:50.430 --> 00:00:50.440 align:start position:0%
stepwise U controllable agents are uh
 

00:00:50.440 --> 00:00:53.069 align:start position:0%
stepwise U controllable agents are uh
available<00:00:50.920><c> with</c><00:00:51.239><c> both</c><00:00:51.600><c> react</c><00:00:52.039><c> agent</c><00:00:52.399><c> and</c><00:00:52.800><c> uh</c>

00:00:53.069 --> 00:00:53.079 align:start position:0%
available with both react agent and uh
 

00:00:53.079 --> 00:00:55.069 align:start position:0%
available with both react agent and uh
function<00:00:53.480><c> calling</c><00:00:53.800><c> agent</c><00:00:54.719><c> uh</c><00:00:54.800><c> in</c><00:00:54.960><c> this</c>

00:00:55.069 --> 00:00:55.079 align:start position:0%
function calling agent uh in this
 

00:00:55.079 --> 00:00:57.470 align:start position:0%
function calling agent uh in this
notebook<00:00:55.879><c> uh</c><00:00:56.000><c> I'll</c><00:00:56.199><c> show</c><00:00:56.640><c> with</c><00:00:56.960><c> the</c><00:00:57.160><c> function</c>

00:00:57.470 --> 00:00:57.480 align:start position:0%
notebook uh I'll show with the function
 

00:00:57.480 --> 00:01:00.630 align:start position:0%
notebook uh I'll show with the function
calling<00:00:57.840><c> agent</c><00:00:58.359><c> but</c><00:00:58.519><c> feel</c><00:00:59.359><c> free</c><00:00:59.640><c> to</c><00:01:00.199><c> exper</c>

00:01:00.630 --> 00:01:00.640 align:start position:0%
calling agent but feel free to exper
 

00:01:00.640 --> 00:01:03.270 align:start position:0%
calling agent but feel free to exper
with<00:01:00.800><c> the</c><00:01:00.960><c> react</c><00:01:01.320><c> agent</c><00:01:01.680><c> as</c><00:01:01.840><c> well</c><00:01:02.680><c> so</c><00:01:02.920><c> let's</c>

00:01:03.270 --> 00:01:03.280 align:start position:0%
with the react agent as well so let's
 

00:01:03.280 --> 00:01:05.990 align:start position:0%
with the react agent as well so let's
get<00:01:03.480><c> started</c><00:01:03.879><c> with</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
get started with
 

00:01:06.000 --> 00:01:11.590 align:start position:0%
get started with
it<00:01:07.000><c> we'll</c><00:01:07.640><c> import</c><00:01:08.280><c> all</c><00:01:08.680><c> the</c><00:01:09.680><c> modules</c><00:01:10.600><c> required</c>

00:01:11.590 --> 00:01:11.600 align:start position:0%
it we'll import all the modules required
 

00:01:11.600 --> 00:01:12.630 align:start position:0%
it we'll import all the modules required
uh

00:01:12.630 --> 00:01:12.640 align:start position:0%
uh
 

00:01:12.640 --> 00:01:18.429 align:start position:0%
uh
setup<00:01:13.640><c> API</c><00:01:14.439><c> key</c><00:01:15.439><c> uh</c><00:01:15.600><c> we</c><00:01:15.759><c> have</c><00:01:16.119><c> all</c><00:01:16.439><c> the</c><00:01:16.720><c> usual</c>

00:01:18.429 --> 00:01:18.439 align:start position:0%
setup API key uh we have all the usual
 

00:01:18.439 --> 00:01:22.710 align:start position:0%
setup API key uh we have all the usual
uh<00:01:19.439><c> calculator</c><00:01:20.240><c> functions</c><00:01:20.759><c> and</c><00:01:21.560><c> we'll</c><00:01:22.360><c> create</c>

00:01:22.710 --> 00:01:22.720 align:start position:0%
uh calculator functions and we'll create
 

00:01:22.720 --> 00:01:23.390 align:start position:0%
uh calculator functions and we'll create
the

00:01:23.390 --> 00:01:23.400 align:start position:0%
the
 

00:01:23.400 --> 00:01:27.630 align:start position:0%
the
tools<00:01:24.400><c> out</c><00:01:24.640><c> of</c><00:01:24.840><c> it</c><00:01:25.360><c> and</c><00:01:25.560><c> then</c><00:01:26.079><c> uh</c><00:01:27.079><c> we'll</c><00:01:27.360><c> create</c>

00:01:27.630 --> 00:01:27.640 align:start position:0%
tools out of it and then uh we'll create
 

00:01:27.640 --> 00:01:29.910 align:start position:0%
tools out of it and then uh we'll create
the<00:01:28.159><c> uh</c><00:01:28.280><c> function</c><00:01:28.640><c> calling</c><00:01:29.000><c> agent</c><00:01:29.520><c> as</c><00:01:29.640><c> usual</c>

00:01:29.910 --> 00:01:29.920 align:start position:0%
the uh function calling agent as usual
 

00:01:29.920 --> 00:01:32.590 align:start position:0%
the uh function calling agent as usual
ual<00:01:30.720><c> first</c><00:01:30.960><c> we'll</c><00:01:31.640><c> execute</c><00:01:32.040><c> it</c><00:01:32.200><c> directly</c>

00:01:32.590 --> 00:01:32.600 align:start position:0%
ual first we'll execute it directly
 

00:01:32.600 --> 00:01:35.270 align:start position:0%
ual first we'll execute it directly
without<00:01:32.880><c> any</c><00:01:33.119><c> stepwise</c><00:01:33.840><c> execution</c><00:01:34.840><c> so</c><00:01:35.119><c> this</c>

00:01:35.270 --> 00:01:35.280 align:start position:0%
without any stepwise execution so this
 

00:01:35.280 --> 00:01:38.350 align:start position:0%
without any stepwise execution so this
will<00:01:35.640><c> help</c><00:01:35.880><c> you</c><00:01:36.280><c> to</c><00:01:37.280><c> understand</c><00:01:37.600><c> the</c>

00:01:38.350 --> 00:01:38.360 align:start position:0%
will help you to understand the
 

00:01:38.360 --> 00:01:41.910 align:start position:0%
will help you to understand the
differentiation<00:01:39.360><c> so</c><00:01:39.880><c> here</c><00:01:40.079><c> the</c><00:01:40.240><c> result</c><00:01:40.680><c> is</c><00:01:41.320><c> 20</c>

00:01:41.910 --> 00:01:41.920 align:start position:0%
differentiation so here the result is 20
 

00:01:41.920 --> 00:01:46.950 align:start position:0%
differentiation so here the result is 20
like<00:01:42.200><c> 20</c><00:01:42.920><c> +</c><00:01:43.920><c> 2</c><00:01:44.200><c> *</c><00:01:44.880><c> 4</c><00:01:45.280><c> -</c><00:01:45.560><c> 8</c><00:01:46.360><c> so</c><00:01:46.640><c> there</c><00:01:46.759><c> is</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
like 20 + 2 * 4 - 8 so there is
 

00:01:46.960 --> 00:01:48.789 align:start position:0%
like 20 + 2 * 4 - 8 so there is
multiplication<00:01:47.680><c> first</c><00:01:47.960><c> and</c><00:01:48.119><c> then</c><00:01:48.280><c> addition</c>

00:01:48.789 --> 00:01:48.799 align:start position:0%
multiplication first and then addition
 

00:01:48.799 --> 00:01:49.709 align:start position:0%
multiplication first and then addition
and<00:01:48.960><c> then</c>

00:01:49.709 --> 00:01:49.719 align:start position:0%
and then
 

00:01:49.719 --> 00:01:51.990 align:start position:0%
and then
subtraction<00:01:50.719><c> and</c><00:01:50.880><c> then</c><00:01:51.000><c> a</c><00:01:51.119><c> final</c><00:01:51.479><c> llm</c>

00:01:51.990 --> 00:01:52.000 align:start position:0%
subtraction and then a final llm
 

00:01:52.000 --> 00:01:55.510 align:start position:0%
subtraction and then a final llm
response<00:01:52.920><c> so</c><00:01:53.640><c> um</c><00:01:54.200><c> that's</c><00:01:54.399><c> how</c><00:01:54.640><c> it</c><00:01:54.719><c> is</c><00:01:54.920><c> executed</c>

00:01:55.510 --> 00:01:55.520 align:start position:0%
response so um that's how it is executed
 

00:01:55.520 --> 00:01:58.789 align:start position:0%
response so um that's how it is executed
right<00:01:55.840><c> there</c><00:01:55.960><c> are</c><00:01:56.119><c> four</c><00:01:56.479><c> steps</c><00:01:57.479><c> uh</c><00:01:57.799><c> one</c>

00:01:58.789 --> 00:01:58.799 align:start position:0%
right there are four steps uh one
 

00:01:58.799 --> 00:02:01.429 align:start position:0%
right there are four steps uh one
multiply<00:01:59.560><c> then</c><00:02:00.000><c> the</c><00:02:00.119><c> addition</c><00:02:00.799><c> then</c><00:02:01.280><c> the</c>

00:02:01.429 --> 00:02:01.439 align:start position:0%
multiply then the addition then the
 

00:02:01.439 --> 00:02:03.270 align:start position:0%
multiply then the addition then the
subtraction<00:02:02.119><c> and</c><00:02:02.280><c> then</c><00:02:02.399><c> the</c><00:02:02.520><c> final</c><00:02:02.799><c> llm</c>

00:02:03.270 --> 00:02:03.280 align:start position:0%
subtraction and then the final llm
 

00:02:03.280 --> 00:02:05.590 align:start position:0%
subtraction and then the final llm
response<00:02:04.200><c> now</c><00:02:04.360><c> let's</c><00:02:04.560><c> see</c><00:02:04.799><c> how</c><00:02:05.000><c> you</c><00:02:05.159><c> can</c><00:02:05.360><c> do</c>

00:02:05.590 --> 00:02:05.600 align:start position:0%
response now let's see how you can do
 

00:02:05.600 --> 00:02:08.630 align:start position:0%
response now let's see how you can do
this<00:02:06.039><c> stepwise</c><00:02:07.039><c> rather</c><00:02:07.280><c> than</c><00:02:07.520><c> in</c><00:02:08.080><c> one</c><00:02:08.280><c> single</c>

00:02:08.630 --> 00:02:08.640 align:start position:0%
this stepwise rather than in one single
 

00:02:08.640 --> 00:02:11.990 align:start position:0%
this stepwise rather than in one single
go<00:02:09.520><c> right</c><00:02:10.000><c> so</c><00:02:10.239><c> first</c><00:02:10.479><c> step</c><00:02:10.759><c> is</c><00:02:11.000><c> to</c><00:02:11.520><c> create</c><00:02:11.800><c> this</c>

00:02:11.990 --> 00:02:12.000 align:start position:0%
go right so first step is to create this
 

00:02:12.000 --> 00:02:15.430 align:start position:0%
go right so first step is to create this
task<00:02:12.440><c> this</c><00:02:12.599><c> is</c><00:02:12.720><c> the</c><00:02:13.040><c> task</c><00:02:14.040><c> and</c><00:02:14.239><c> then</c><00:02:14.720><c> we'll</c><00:02:15.200><c> go</c>

00:02:15.430 --> 00:02:15.440 align:start position:0%
task this is the task and then we'll go
 

00:02:15.440 --> 00:02:19.670 align:start position:0%
task this is the task and then we'll go
with<00:02:15.599><c> the</c><00:02:16.519><c> uh</c><00:02:16.959><c> executing</c><00:02:17.519><c> the</c><00:02:18.360><c> first</c><00:02:18.680><c> step</c>

00:02:19.670 --> 00:02:19.680 align:start position:0%
with the uh executing the first step
 

00:02:19.680 --> 00:02:23.190 align:start position:0%
with the uh executing the first step
which<00:02:19.800><c> is</c><00:02:20.040><c> run</c><00:02:20.360><c> step</c><00:02:21.360><c> and</c><00:02:21.519><c> then</c><00:02:21.720><c> you</c><00:02:22.000><c> get</c><00:02:22.280><c> the</c>

00:02:23.190 --> 00:02:23.200 align:start position:0%
which is run step and then you get the
 

00:02:23.200 --> 00:02:25.470 align:start position:0%
which is run step and then you get the
one<00:02:23.480><c> step</c>

00:02:25.470 --> 00:02:25.480 align:start position:0%
one step
 

00:02:25.480 --> 00:02:28.750 align:start position:0%
one step
output<00:02:26.480><c> you</c><00:02:26.599><c> can</c><00:02:27.000><c> check</c><00:02:27.280><c> the</c><00:02:27.800><c> uh</c><00:02:28.000><c> step</c><00:02:28.319><c> output</c>

00:02:28.750 --> 00:02:28.760 align:start position:0%
output you can check the uh step output
 

00:02:28.760 --> 00:02:30.430 align:start position:0%
output you can check the uh step output
here<00:02:29.040><c> you</c><00:02:29.160><c> can</c><00:02:29.400><c> see</c>

00:02:30.430 --> 00:02:30.440 align:start position:0%
here you can see
 

00:02:30.440 --> 00:02:33.790 align:start position:0%
here you can see
that<00:02:30.640><c> this</c><00:02:30.760><c> is</c><00:02:31.000><c> not</c><00:02:32.000><c> uh</c><00:02:32.239><c> last</c><00:02:32.599><c> step</c><00:02:33.440><c> and</c><00:02:33.680><c> what</c>

00:02:33.790 --> 00:02:33.800 align:start position:0%
that this is not uh last step and what
 

00:02:33.800 --> 00:02:36.430 align:start position:0%
that this is not uh last step and what
is<00:02:33.920><c> the</c><00:02:34.200><c> output</c><00:02:34.680><c> what</c><00:02:34.800><c> are</c><00:02:35.040><c> the</c><00:02:35.440><c> arguments</c>

00:02:36.430 --> 00:02:36.440 align:start position:0%
is the output what are the arguments
 

00:02:36.440 --> 00:02:40.990 align:start position:0%
is the output what are the arguments
what<00:02:36.560><c> is</c><00:02:36.800><c> output</c><00:02:37.239><c> from</c><00:02:37.560><c> this</c><00:02:38.560><c> right</c><00:02:39.159><c> all</c><00:02:39.440><c> those</c>

00:02:40.990 --> 00:02:41.000 align:start position:0%
what is output from this right all those
 

00:02:41.000 --> 00:02:44.190 align:start position:0%
what is output from this right all those
things<00:02:42.000><c> you</c><00:02:42.120><c> can</c><00:02:42.319><c> check</c><00:02:42.640><c> the</c><00:02:43.040><c> uh</c><00:02:43.159><c> step</c><00:02:43.440><c> output.</c>

00:02:44.190 --> 00:02:44.200 align:start position:0%
things you can check the uh step output.
 

00:02:44.200 --> 00:02:47.430 align:start position:0%
things you can check the uh step output.
e<00:02:44.519><c> last</c><00:02:44.800><c> which</c><00:02:44.920><c> is</c><00:02:45.040><c> false</c><00:02:45.400><c> so</c><00:02:45.560><c> that</c><00:02:46.000><c> are</c><00:02:47.000><c> steps</c>

00:02:47.430 --> 00:02:47.440 align:start position:0%
e last which is false so that are steps
 

00:02:47.440 --> 00:02:52.309 align:start position:0%
e last which is false so that are steps
ahead<00:02:47.840><c> so</c><00:02:48.680><c> run</c><00:02:48.920><c> it</c><00:02:49.519><c> again</c><00:02:50.519><c> uh</c><00:02:51.080><c> so</c><00:02:51.599><c> you</c><00:02:51.920><c> got</c><00:02:52.159><c> the</c>

00:02:52.309 --> 00:02:52.319 align:start position:0%
ahead so run it again uh so you got the
 

00:02:52.319 --> 00:02:54.990 align:start position:0%
ahead so run it again uh so you got the
next<00:02:52.560><c> step</c><00:02:52.800><c> which</c><00:02:52.920><c> is</c><00:02:53.080><c> addition</c><00:02:53.720><c> 28</c><00:02:54.680><c> you</c><00:02:54.800><c> can</c>

00:02:54.990 --> 00:02:55.000 align:start position:0%
next step which is addition 28 you can
 

00:02:55.000 --> 00:02:56.750 align:start position:0%
next step which is addition 28 you can
check<00:02:55.280><c> again</c><00:02:55.640><c> whether</c><00:02:55.879><c> it's</c><00:02:56.120><c> last</c><00:02:56.400><c> step</c><00:02:56.599><c> or</c>

00:02:56.750 --> 00:02:56.760 align:start position:0%
check again whether it's last step or
 

00:02:56.760 --> 00:03:01.110 align:start position:0%
check again whether it's last step or
not<00:02:57.080><c> this</c><00:02:57.159><c> is</c><00:02:57.360><c> not</c><00:02:58.159><c> so</c><00:02:58.360><c> run</c><00:02:58.560><c> it</c><00:02:58.800><c> again</c><00:02:59.959><c> and</c><00:03:00.239><c> then</c>

00:03:01.110 --> 00:03:01.120 align:start position:0%
not this is not so run it again and then
 

00:03:01.120 --> 00:03:04.070 align:start position:0%
not this is not so run it again and then
you<00:03:01.280><c> can</c><00:03:01.440><c> see</c><00:03:01.720><c> that</c><00:03:02.640><c> uh</c><00:03:03.040><c> this</c><00:03:03.159><c> is</c><00:03:03.560><c> subtraction</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
you can see that uh this is subtraction
 

00:03:04.080 --> 00:03:07.270 align:start position:0%
you can see that uh this is subtraction
is<00:03:04.239><c> done</c><00:03:05.080><c> and</c><00:03:05.239><c> then</c><00:03:05.720><c> probably</c><00:03:06.440><c> you</c><00:03:06.640><c> check</c><00:03:07.040><c> if</c>

00:03:07.270 --> 00:03:07.280 align:start position:0%
is done and then probably you check if
 

00:03:07.280 --> 00:03:10.509 align:start position:0%
is done and then probably you check if
this<00:03:07.440><c> is</c><00:03:08.080><c> the</c><00:03:08.239><c> last</c><00:03:08.560><c> step</c>

00:03:10.509 --> 00:03:10.519 align:start position:0%
this is the last step
 

00:03:10.519 --> 00:03:13.990 align:start position:0%
this is the last step
again<00:03:11.519><c> um</c><00:03:12.080><c> this</c><00:03:12.200><c> is</c><00:03:12.400><c> false</c><00:03:13.120><c> right</c><00:03:13.360><c> so</c><00:03:13.720><c> you</c><00:03:13.840><c> need</c>

00:03:13.990 --> 00:03:14.000 align:start position:0%
again um this is false right so you need
 

00:03:14.000 --> 00:03:16.550 align:start position:0%
again um this is false right so you need
to<00:03:14.159><c> run</c><00:03:14.360><c> it</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
to run it
 

00:03:16.560 --> 00:03:19.869 align:start position:0%
to run it
again<00:03:17.560><c> so</c><00:03:17.959><c> and</c><00:03:18.159><c> the</c><00:03:18.280><c> lrm</c><00:03:18.840><c> response</c><00:03:19.440><c> here</c><00:03:19.599><c> it</c>

00:03:19.869 --> 00:03:19.879 align:start position:0%
again so and the lrm response here it
 

00:03:19.879 --> 00:03:22.430 align:start position:0%
again so and the lrm response here it
ends<00:03:20.879><c> so</c><00:03:21.319><c> and</c><00:03:21.480><c> then</c><00:03:21.680><c> finally</c><00:03:21.959><c> you</c><00:03:22.080><c> get</c><00:03:22.200><c> a</c>

00:03:22.430 --> 00:03:22.440 align:start position:0%
ends so and then finally you get a
 

00:03:22.440 --> 00:03:25.589 align:start position:0%
ends so and then finally you get a
response<00:03:22.959><c> right</c><00:03:23.440><c> so</c><00:03:23.920><c> here</c><00:03:24.280><c> you</c><00:03:24.400><c> can</c><00:03:24.879><c> see</c><00:03:25.480><c> what</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
response right so here you can see what
 

00:03:25.599 --> 00:03:27.630 align:start position:0%
response right so here you can see what
are<00:03:25.799><c> all</c><00:03:26.040><c> the</c><00:03:26.200><c> different</c><00:03:26.560><c> steps</c><00:03:26.920><c> you</c><00:03:27.040><c> can</c><00:03:27.280><c> run</c>

00:03:27.630 --> 00:03:27.640 align:start position:0%
are all the different steps you can run
 

00:03:27.640 --> 00:03:30.270 align:start position:0%
are all the different steps you can run
through<00:03:27.879><c> it</c><00:03:28.879><c> uh</c><00:03:29.319><c> by</c><00:03:29.439><c> checking</c><00:03:29.840><c> whether</c><00:03:30.040><c> it's</c><00:03:30.120><c> a</c>

00:03:30.270 --> 00:03:30.280 align:start position:0%
through it uh by checking whether it's a
 

00:03:30.280 --> 00:03:32.710 align:start position:0%
through it uh by checking whether it's a
last<00:03:30.560><c> step</c><00:03:30.799><c> or</c><00:03:31.000><c> not</c><00:03:31.480><c> and</c><00:03:31.640><c> get</c><00:03:31.760><c> a</c><00:03:31.920><c> final</c>

00:03:32.710 --> 00:03:32.720 align:start position:0%
last step or not and get a final
 

00:03:32.720 --> 00:03:36.309 align:start position:0%
last step or not and get a final
response<00:03:33.720><c> so</c><00:03:34.040><c> this</c><00:03:34.159><c> is</c><00:03:34.599><c> more</c><00:03:34.879><c> of</c><00:03:35.519><c> lower</c><00:03:35.879><c> level</c>

00:03:36.309 --> 00:03:36.319 align:start position:0%
response so this is more of lower level
 

00:03:36.319 --> 00:03:39.869 align:start position:0%
response so this is more of lower level
API<00:03:36.799><c> agent</c><00:03:37.319><c> API</c><00:03:38.319><c> um</c><00:03:38.840><c> uh</c><00:03:39.040><c> that</c><00:03:39.280><c> you</c><00:03:39.400><c> can</c><00:03:39.599><c> see</c>

00:03:39.869 --> 00:03:39.879 align:start position:0%
API agent API um uh that you can see
 

00:03:39.879 --> 00:03:42.710 align:start position:0%
API agent API um uh that you can see
what<00:03:40.040><c> are</c><00:03:40.200><c> the</c><00:03:40.360><c> different</c><00:03:40.680><c> steps</c><00:03:41.040><c> going</c><00:03:41.360><c> on</c><00:03:42.080><c> um</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
what are the different steps going on um
 

00:03:42.720 --> 00:03:45.030 align:start position:0%
what are the different steps going on um
executing<00:03:43.720><c> one</c><00:03:43.920><c> step</c><00:03:44.200><c> at</c><00:03:44.319><c> a</c>

00:03:45.030 --> 00:03:45.040 align:start position:0%
executing one step at a
 

00:03:45.040 --> 00:03:48.070 align:start position:0%
executing one step at a
time<00:03:46.040><c> okay</c><00:03:46.879><c> we'll</c><00:03:47.200><c> observe</c><00:03:47.640><c> the</c><00:03:47.760><c> similar</c>

00:03:48.070 --> 00:03:48.080 align:start position:0%
time okay we'll observe the similar
 

00:03:48.080 --> 00:03:49.990 align:start position:0%
time okay we'll observe the similar
thing<00:03:48.239><c> with</c><00:03:48.360><c> the</c><00:03:48.480><c> rack</c><00:03:48.720><c> tools</c><00:03:49.120><c> as</c><00:03:49.239><c> well</c><00:03:49.680><c> so</c>

00:03:49.990 --> 00:03:50.000 align:start position:0%
thing with the rack tools as well so
 

00:03:50.000 --> 00:03:53.149 align:start position:0%
thing with the rack tools as well so
we'll<00:03:50.400><c> consider</c><00:03:51.239><c> uh</c><00:03:52.079><c> uh</c><00:03:52.239><c> March</c><00:03:52.640><c> June</c><00:03:52.920><c> and</c>

00:03:53.149 --> 00:03:53.159 align:start position:0%
we'll consider uh uh March June and
 

00:03:53.159 --> 00:03:56.110 align:start position:0%
we'll consider uh uh March June and
September<00:03:54.120><c> Uber</c><00:03:54.840><c> uh</c>

00:03:56.110 --> 00:03:56.120 align:start position:0%
September Uber uh
 

00:03:56.120 --> 00:04:00.309 align:start position:0%
September Uber uh
documents<00:03:57.120><c> and</c><00:03:57.760><c> uh</c><00:03:58.760><c> let's</c><00:03:59.000><c> load</c><00:03:59.319><c> the</c><00:03:59.480><c> data</c>

00:04:00.309 --> 00:04:00.319 align:start position:0%
documents and uh let's load the data
 

00:04:00.319 --> 00:04:02.830 align:start position:0%
documents and uh let's load the data
this<00:04:00.439><c> will</c><00:04:00.680><c> take</c><00:04:00.879><c> some</c><00:04:01.280><c> time</c><00:04:02.280><c> uh</c><00:04:02.439><c> because</c><00:04:02.680><c> of</c>

00:04:02.830 --> 00:04:02.840 align:start position:0%
this will take some time uh because of
 

00:04:02.840 --> 00:04:05.030 align:start position:0%
this will take some time uh because of
the<00:04:03.040><c> huge</c><00:04:03.680><c> uh</c><00:04:03.799><c> number</c><00:04:04.079><c> of</c><00:04:04.280><c> pages</c><00:04:04.560><c> in</c><00:04:04.720><c> each</c><00:04:04.920><c> of</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
the huge uh number of pages in each of
 

00:04:05.040 --> 00:04:08.390 align:start position:0%
the huge uh number of pages in each of
the<00:04:05.439><c> document</c><00:04:06.439><c> and</c><00:04:06.599><c> then</c><00:04:06.799><c> we'll</c><00:04:07.480><c> uh</c><00:04:07.959><c> create</c>

00:04:08.390 --> 00:04:08.400 align:start position:0%
the document and then we'll uh create
 

00:04:08.400 --> 00:04:11.589 align:start position:0%
the document and then we'll uh create
index<00:04:08.799><c> and</c><00:04:09.480><c> tools</c><00:04:10.480><c> um</c>

00:04:11.589 --> 00:04:11.599 align:start position:0%
index and tools um
 

00:04:11.599 --> 00:04:14.350 align:start position:0%
index and tools um
so<00:04:12.599><c> we</c><00:04:12.760><c> have</c><00:04:12.879><c> a</c><00:04:13.040><c> function</c><00:04:13.400><c> to</c><00:04:13.680><c> Define</c><00:04:14.040><c> get</c><00:04:14.239><c> the</c>

00:04:14.350 --> 00:04:14.360 align:start position:0%
so we have a function to Define get the
 

00:04:14.360 --> 00:04:18.069 align:start position:0%
so we have a function to Define get the
Tool<00:04:15.040><c> uh</c><00:04:15.200><c> this</c><00:04:15.360><c> will</c><00:04:16.120><c> uh</c><00:04:16.639><c> create</c><00:04:17.000><c> index</c><00:04:17.799><c> and</c>

00:04:18.069 --> 00:04:18.079 align:start position:0%
Tool uh this will uh create index and
 

00:04:18.079 --> 00:04:21.509 align:start position:0%
Tool uh this will uh create index and
then<00:04:18.799><c> uh</c><00:04:19.799><c> create</c><00:04:20.160><c> query</c><00:04:20.479><c> engine</c><00:04:21.040><c> and</c><00:04:21.199><c> then</c>

00:04:21.509 --> 00:04:21.519 align:start position:0%
then uh create query engine and then
 

00:04:21.519 --> 00:04:23.950 align:start position:0%
then uh create query engine and then
Define<00:04:21.880><c> the</c>

00:04:23.950 --> 00:04:23.960 align:start position:0%
Define the
 

00:04:23.960 --> 00:04:28.390 align:start position:0%
Define the
tool<00:04:25.280><c> so</c><00:04:26.280><c> let's</c><00:04:26.800><c> do</c><00:04:27.120><c> that</c><00:04:27.639><c> and</c><00:04:27.800><c> then</c><00:04:28.040><c> let's</c>

00:04:28.390 --> 00:04:28.400 align:start position:0%
tool so let's do that and then let's
 

00:04:28.400 --> 00:04:30.710 align:start position:0%
tool so let's do that and then let's
create<00:04:29.080><c> uh</c>

00:04:30.710 --> 00:04:30.720 align:start position:0%
create uh
 

00:04:30.720 --> 00:04:34.230 align:start position:0%
create uh
uh<00:04:30.919><c> tools</c><00:04:31.320><c> for</c><00:04:31.880><c> uh</c><00:04:32.039><c> March</c><00:04:32.479><c> June</c><00:04:32.759><c> and</c><00:04:33.240><c> September</c>

00:04:34.230 --> 00:04:34.240 align:start position:0%
uh tools for uh March June and September
 

00:04:34.240 --> 00:04:37.029 align:start position:0%
uh tools for uh March June and September
and<00:04:35.039><c> get</c><00:04:35.320><c> the</c><00:04:35.639><c> query</c><00:04:35.960><c> engine</c>

00:04:37.029 --> 00:04:37.039 align:start position:0%
and get the query engine
 

00:04:37.039 --> 00:04:42.150 align:start position:0%
and get the query engine
tools<00:04:38.039><c> now</c><00:04:38.240><c> we'll</c><00:04:39.080><c> uh</c><00:04:40.080><c> create</c><00:04:40.440><c> the</c><00:04:40.960><c> agent</c><00:04:41.960><c> and</c>

00:04:42.150 --> 00:04:42.160 align:start position:0%
tools now we'll uh create the agent and
 

00:04:42.160 --> 00:04:44.830 align:start position:0%
tools now we'll uh create the agent and
then<00:04:42.560><c> first</c><00:04:43.000><c> let's</c><00:04:43.280><c> do</c><00:04:43.479><c> the</c><00:04:43.680><c> direct</c><00:04:44.000><c> execution</c>

00:04:44.830 --> 00:04:44.840 align:start position:0%
then first let's do the direct execution
 

00:04:44.840 --> 00:04:47.230 align:start position:0%
then first let's do the direct execution
analyze<00:04:45.320><c> the</c><00:04:45.440><c> changes</c><00:04:45.919><c> in</c><00:04:46.120><c> R&amp;D</c><00:04:46.639><c> expenditures</c>

00:04:47.230 --> 00:04:47.240 align:start position:0%
analyze the changes in R&amp;D expenditures
 

00:04:47.240 --> 00:04:52.029 align:start position:0%
analyze the changes in R&amp;D expenditures
and<00:04:47.759><c> revenue</c><00:04:48.759><c> let's</c><00:04:49.000><c> see</c><00:04:49.320><c> how</c><00:04:49.560><c> it</c><00:04:50.080><c> works</c><00:04:51.080><c> so</c>

00:04:52.029 --> 00:04:52.039 align:start position:0%
and revenue let's see how it works so
 

00:04:52.039 --> 00:04:57.070 align:start position:0%
and revenue let's see how it works so
think<00:04:52.360><c> still</c><00:04:52.680><c> it's</c><00:04:52.880><c> loading</c><00:04:53.240><c> the</c>

00:04:57.070 --> 00:04:57.080 align:start position:0%
 
 

00:04:57.080 --> 00:05:01.070 align:start position:0%
 
data<00:04:58.080><c> we</c><00:04:58.400><c> take</c><00:04:58.759><c> a</c><00:04:58.960><c> minute</c><00:04:59.240><c> of</c><00:04:59.440><c> time</c><00:05:00.080><c> probably</c>

00:05:01.070 --> 00:05:01.080 align:start position:0%
data we take a minute of time probably
 

00:05:01.080 --> 00:05:05.670 align:start position:0%
data we take a minute of time probably
uh<00:05:01.600><c> even</c><00:05:01.919><c> indexing</c><00:05:02.520><c> might</c><00:05:02.720><c> take</c><00:05:02.960><c> more</c>

00:05:05.670 --> 00:05:05.680 align:start position:0%
 
 

00:05:05.680 --> 00:05:08.830 align:start position:0%
 
time<00:05:06.680><c> so</c><00:05:07.120><c> let's</c>

00:05:08.830 --> 00:05:08.840 align:start position:0%
time so let's
 

00:05:08.840 --> 00:05:12.510 align:start position:0%
time so let's
see<00:05:09.840><c> and</c><00:05:10.120><c> then</c><00:05:10.479><c> once</c><00:05:10.720><c> it</c><00:05:10.840><c> is</c><00:05:11.039><c> executed</c><00:05:11.800><c> so</c><00:05:12.360><c> and</c>

00:05:12.510 --> 00:05:12.520 align:start position:0%
see and then once it is executed so and
 

00:05:12.520 --> 00:05:14.749 align:start position:0%
see and then once it is executed so and
then<00:05:12.680><c> we</c><00:05:12.840><c> can</c><00:05:13.039><c> see</c><00:05:13.520><c> the</c><00:05:13.680><c> stepwise</c><00:05:14.240><c> execution</c>

00:05:14.749 --> 00:05:14.759 align:start position:0%
then we can see the stepwise execution
 

00:05:14.759 --> 00:05:18.390 align:start position:0%
then we can see the stepwise execution
as<00:05:14.919><c> well</c><00:05:15.560><c> uh</c><00:05:15.880><c> Again</c><00:05:16.280><c> by</c><00:05:16.600><c> creating</c><00:05:17.080><c> the</c><00:05:17.400><c> task</c>

00:05:18.390 --> 00:05:18.400 align:start position:0%
as well uh Again by creating the task
 

00:05:18.400 --> 00:05:20.909 align:start position:0%
as well uh Again by creating the task
and<00:05:19.400><c> running</c><00:05:19.840><c> one</c><00:05:20.000><c> step</c><00:05:20.280><c> at</c><00:05:20.400><c> a</c><00:05:20.560><c> time</c><00:05:20.800><c> and</c>

00:05:20.909 --> 00:05:20.919 align:start position:0%
and running one step at a time and
 

00:05:20.919 --> 00:05:23.909 align:start position:0%
and running one step at a time and
checking<00:05:21.560><c> whether</c><00:05:21.759><c> it's</c><00:05:21.919><c> a</c><00:05:22.080><c> last</c><00:05:22.360><c> step</c><00:05:22.600><c> or</c><00:05:22.919><c> not</c>

00:05:23.909 --> 00:05:23.919 align:start position:0%
checking whether it's a last step or not
 

00:05:23.919 --> 00:05:25.870 align:start position:0%
checking whether it's a last step or not
right

00:05:25.870 --> 00:05:25.880 align:start position:0%
right
 

00:05:25.880 --> 00:05:34.230 align:start position:0%
right
so<00:05:26.880><c> we'll</c><00:05:27.160><c> see</c><00:05:27.479><c> how</c><00:05:27.759><c> it</c><00:05:27.919><c> works</c><00:05:28.880><c> so</c><00:05:29.479><c> yeah</c>

00:05:34.230 --> 00:05:34.240 align:start position:0%
 
 

00:05:34.240 --> 00:05:35.790 align:start position:0%
 
yeah

00:05:35.790 --> 00:05:35.800 align:start position:0%
yeah
 

00:05:35.800 --> 00:05:39.510 align:start position:0%
yeah
so<00:05:36.800><c> here</c><00:05:37.000><c> you</c><00:05:37.120><c> can</c><00:05:37.280><c> see</c><00:05:37.759><c> it</c><00:05:37.919><c> called</c><00:05:38.240><c> the</c><00:05:38.400><c> March</c>

00:05:39.510 --> 00:05:39.520 align:start position:0%
so here you can see it called the March
 

00:05:39.520 --> 00:05:43.189 align:start position:0%
so here you can see it called the March
2022<00:05:40.720><c> first</c><00:05:41.720><c> and</c>

00:05:43.189 --> 00:05:43.199 align:start position:0%
2022 first and
 

00:05:43.199 --> 00:05:47.670 align:start position:0%
2022 first and
then<00:05:44.199><c> uh</c><00:05:44.360><c> it</c><00:05:44.560><c> gave</c><00:05:44.840><c> some</c>

00:05:47.670 --> 00:05:47.680 align:start position:0%
 
 

00:05:47.680 --> 00:05:52.670 align:start position:0%
 
output<00:05:48.680><c> then</c><00:05:48.880><c> it</c><00:05:49.039><c> went</c><00:05:49.360><c> for</c><00:05:50.000><c> uh</c>

00:05:52.670 --> 00:05:52.680 align:start position:0%
 
 

00:05:52.680 --> 00:05:54.830 align:start position:0%
 
June

00:05:54.830 --> 00:05:54.840 align:start position:0%
June
 

00:05:54.840 --> 00:06:01.189 align:start position:0%
June
tool<00:05:55.840><c> and</c><00:05:56.280><c> then</c><00:05:56.840><c> again</c>

00:06:01.189 --> 00:06:01.199 align:start position:0%
 
 

00:06:01.199 --> 00:06:03.350 align:start position:0%
 
think<00:06:01.759><c> the</c><00:06:01.919><c> final</c><00:06:02.199><c> one</c><00:06:02.400><c> the</c>

00:06:03.350 --> 00:06:03.360 align:start position:0%
think the final one the
 

00:06:03.360 --> 00:06:08.029 align:start position:0%
think the final one the
September<00:06:04.400><c> right</c><00:06:05.720><c> so</c><00:06:06.720><c> this</c><00:06:06.960><c> give</c><00:06:07.199><c> the</c><00:06:07.319><c> output</c>

00:06:08.029 --> 00:06:08.039 align:start position:0%
September right so this give the output
 

00:06:08.039 --> 00:06:11.909 align:start position:0%
September right so this give the output
and<00:06:08.280><c> you'll</c><00:06:08.520><c> get</c><00:06:08.639><c> a</c><00:06:08.800><c> final</c><00:06:09.560><c> response</c><00:06:10.639><c> um</c><00:06:11.639><c> so</c>

00:06:11.909 --> 00:06:11.919 align:start position:0%
and you'll get a final response um so
 

00:06:11.919 --> 00:06:14.870 align:start position:0%
and you'll get a final response um so
here<00:06:12.080><c> as</c><00:06:12.240><c> well</c><00:06:12.560><c> there</c><00:06:12.680><c> are</c><00:06:13.199><c> four</c><00:06:13.479><c> steps</c><00:06:13.960><c> right</c>

00:06:14.870 --> 00:06:14.880 align:start position:0%
here as well there are four steps right
 

00:06:14.880 --> 00:06:18.469 align:start position:0%
here as well there are four steps right
uh<00:06:15.039><c> first</c><00:06:15.360><c> calling</c>

00:06:18.469 --> 00:06:18.479 align:start position:0%
 
 

00:06:18.479 --> 00:06:22.710 align:start position:0%
 
the<00:06:19.479><c> March</c><00:06:20.280><c> then</c><00:06:20.840><c> uh</c><00:06:21.319><c> June</c><00:06:21.840><c> September</c><00:06:22.560><c> and</c>

00:06:22.710 --> 00:06:22.720 align:start position:0%
the March then uh June September and
 

00:06:22.720 --> 00:06:24.430 align:start position:0%
the March then uh June September and
then<00:06:22.880><c> the</c><00:06:23.000><c> final</c><00:06:23.280><c> llm</c>

00:06:24.430 --> 00:06:24.440 align:start position:0%
then the final llm
 

00:06:24.440 --> 00:06:28.790 align:start position:0%
then the final llm
response<00:06:25.759><c> so</c><00:06:26.759><c> so</c><00:06:27.000><c> that's</c><00:06:27.199><c> how</c><00:06:27.360><c> it</c><00:06:27.479><c> is</c><00:06:28.319><c> uh</c><00:06:28.599><c> let's</c>

00:06:28.790 --> 00:06:28.800 align:start position:0%
response so so that's how it is uh let's
 

00:06:28.800 --> 00:06:31.469 align:start position:0%
response so so that's how it is uh let's
do<00:06:29.000><c> the</c><00:06:29.400><c> uh</c><00:06:29.680><c> stepwise</c><00:06:30.160><c> execution</c>

00:06:31.469 --> 00:06:31.479 align:start position:0%
do the uh stepwise execution
 

00:06:31.479 --> 00:06:34.790 align:start position:0%
do the uh stepwise execution
now<00:06:32.479><c> and</c><00:06:32.720><c> then</c><00:06:33.080><c> create</c><00:06:33.400><c> the</c>

00:06:34.790 --> 00:06:34.800 align:start position:0%
now and then create the
 

00:06:34.800 --> 00:06:40.309 align:start position:0%
now and then create the
task<00:06:35.800><c> and</c><00:06:36.080><c> you</c><00:06:36.199><c> can</c><00:06:36.400><c> check</c><00:06:36.759><c> the</c><00:06:37.160><c> first</c>

00:06:40.309 --> 00:06:40.319 align:start position:0%
 
 

00:06:40.319 --> 00:06:45.390 align:start position:0%
 
step<00:06:41.319><c> so</c><00:06:41.560><c> it</c><00:06:41.800><c> called</c><00:06:42.520><c> U</c><00:06:43.120><c> March</c><00:06:43.520><c> 2022</c><00:06:44.440><c> tool</c><00:06:45.160><c> with</c>

00:06:45.390 --> 00:06:45.400 align:start position:0%
step so it called U March 2022 tool with
 

00:06:45.400 --> 00:06:48.629 align:start position:0%
step so it called U March 2022 tool with
the<00:06:45.680><c> input</c><00:06:46.000><c> R&amp;D</c><00:06:46.440><c> expenditures</c><00:06:47.000><c> and</c><00:06:47.639><c> revenue</c>

00:06:48.629 --> 00:06:48.639 align:start position:0%
the input R&amp;D expenditures and revenue
 

00:06:48.639 --> 00:06:51.510 align:start position:0%
the input R&amp;D expenditures and revenue
and<00:06:48.800><c> then</c><00:06:49.000><c> check</c><00:06:49.560><c> if</c><00:06:49.680><c> it</c><00:06:50.000><c> last</c><00:06:50.360><c> step</c><00:06:51.120><c> which</c><00:06:51.280><c> is</c>

00:06:51.510 --> 00:06:51.520 align:start position:0%
and then check if it last step which is
 

00:06:51.520 --> 00:06:54.390 align:start position:0%
and then check if it last step which is
false<00:06:52.280><c> and</c><00:06:52.440><c> then</c><00:06:52.599><c> run</c><00:06:52.840><c> the</c><00:06:52.960><c> next</c><00:06:53.199><c> step</c>

00:06:54.390 --> 00:06:54.400 align:start position:0%
false and then run the next step
 

00:06:54.400 --> 00:06:57.469 align:start position:0%
false and then run the next step
again<00:06:55.639><c> right</c>

00:06:57.469 --> 00:06:57.479 align:start position:0%
again right
 

00:06:57.479 --> 00:07:02.309 align:start position:0%
again right
um<00:06:58.479><c> I</c><00:06:58.639><c> should</c><00:06:58.960><c> ideally</c><00:06:59.639><c> call</c><00:06:59.919><c> the</c>

00:07:02.309 --> 00:07:02.319 align:start position:0%
 
 

00:07:02.319 --> 00:07:07.390 align:start position:0%
 
June<00:07:03.319><c> yeah</c><00:07:03.759><c> June</c><00:07:04.039><c> 2022</c>

00:07:07.390 --> 00:07:07.400 align:start position:0%
 
 

00:07:07.400 --> 00:07:11.589 align:start position:0%
 
tool<00:07:08.879><c> and</c><00:07:09.879><c> then</c><00:07:10.199><c> if</c><00:07:10.599><c> check</c><00:07:10.840><c> if</c><00:07:11.000><c> the</c><00:07:11.120><c> last</c><00:07:11.360><c> step</c>

00:07:11.589 --> 00:07:11.599 align:start position:0%
tool and then if check if the last step
 

00:07:11.599 --> 00:07:13.029 align:start position:0%
tool and then if check if the last step
no<00:07:11.960><c> run</c>

00:07:13.029 --> 00:07:13.039 align:start position:0%
no run
 

00:07:13.039 --> 00:07:21.909 align:start position:0%
no run
again<00:07:14.039><c> it</c><00:07:14.199><c> should</c><00:07:14.560><c> call</c><00:07:14.919><c> the</c><00:07:15.120><c> September</c><00:07:15.599><c> one</c>

00:07:21.909 --> 00:07:21.919 align:start position:0%
 
 

00:07:21.919 --> 00:07:26.990 align:start position:0%
 
now<00:07:23.360><c> so</c><00:07:24.360><c> yeah</c><00:07:24.840><c> this</c><00:07:24.960><c> is</c><00:07:25.080><c> the</c><00:07:25.199><c> September</c><00:07:26.000><c> one</c>

00:07:26.990 --> 00:07:27.000 align:start position:0%
now so yeah this is the September one
 

00:07:27.000 --> 00:07:29.589 align:start position:0%
now so yeah this is the September one
and<00:07:27.560><c> then</c><00:07:28.560><c> yeah</c><00:07:28.720><c> let's</c><00:07:28.919><c> check</c><00:07:29.120><c> this</c><00:07:29.240><c> is</c><00:07:29.319><c> the</c>

00:07:29.589 --> 00:07:29.599 align:start position:0%
and then yeah let's check this is the
 

00:07:29.599 --> 00:07:34.070 align:start position:0%
and then yeah let's check this is the
last<00:07:29.840><c> step</c><00:07:30.520><c> I</c><00:07:30.720><c> should</c><00:07:31.080><c> be</c><00:07:31.319><c> know</c><00:07:31.840><c> and</c><00:07:32.039><c> then</c><00:07:32.879><c> next</c>

00:07:34.070 --> 00:07:34.080 align:start position:0%
last step I should be know and then next
 

00:07:34.080 --> 00:07:36.510 align:start position:0%
last step I should be know and then next
step<00:07:35.080><c> this</c><00:07:35.199><c> is</c><00:07:35.440><c> like</c>

00:07:36.510 --> 00:07:36.520 align:start position:0%
step this is like
 

00:07:36.520 --> 00:07:40.629 align:start position:0%
step this is like
the<00:07:37.520><c> llm</c><00:07:38.160><c> response</c>

00:07:40.629 --> 00:07:40.639 align:start position:0%
the llm response
 

00:07:40.639 --> 00:07:44.230 align:start position:0%
the llm response
ideally<00:07:41.639><c> yeah</c><00:07:42.440><c> so</c><00:07:43.000><c> this</c><00:07:43.080><c> is</c><00:07:43.199><c> the</c><00:07:43.360><c> last</c><00:07:43.639><c> step</c>

00:07:44.230 --> 00:07:44.240 align:start position:0%
ideally yeah so this is the last step
 

00:07:44.240 --> 00:07:46.350 align:start position:0%
ideally yeah so this is the last step
and<00:07:44.599><c> finally</c><00:07:45.240><c> get</c><00:07:45.479><c> the</c>

00:07:46.350 --> 00:07:46.360 align:start position:0%
and finally get the
 

00:07:46.360 --> 00:07:51.230 align:start position:0%
and finally get the
response<00:07:47.360><c> that's</c><00:07:47.599><c> it</c><00:07:48.440><c> so</c><00:07:49.639><c> we</c><00:07:50.639><c> went</c><00:07:50.919><c> through</c>

00:07:51.230 --> 00:07:51.240 align:start position:0%
response that's it so we went through
 

00:07:51.240 --> 00:07:54.070 align:start position:0%
response that's it so we went through
each<00:07:51.520><c> step</c><00:07:51.960><c> individually</c><00:07:52.720><c> and</c><00:07:52.960><c> got</c><00:07:53.159><c> the</c><00:07:53.319><c> final</c>

00:07:54.070 --> 00:07:54.080 align:start position:0%
each step individually and got the final
 

00:07:54.080 --> 00:07:57.149 align:start position:0%
each step individually and got the final
response<00:07:55.080><c> and</c><00:07:55.280><c> also</c><00:07:55.800><c> you</c><00:07:55.919><c> can</c><00:07:56.319><c> actually</c><00:07:56.759><c> give</c>

00:07:57.149 --> 00:07:57.159 align:start position:0%
response and also you can actually give
 

00:07:57.159 --> 00:07:59.629 align:start position:0%
response and also you can actually give
a<00:07:57.360><c> separate</c><00:07:57.840><c> input</c><00:07:58.360><c> in</c><00:07:58.599><c> the</c><00:07:58.840><c> one</c><00:07:59.000><c> of</c><00:07:59.199><c> these</c>

00:07:59.629 --> 00:07:59.639 align:start position:0%
a separate input in the one of these
 

00:07:59.639 --> 00:08:04.029 align:start position:0%
a separate input in the one of these
steps<00:08:00.639><c> right</c><00:08:01.120><c> as</c><00:08:01.199><c> a</c><00:08:01.400><c> human</c>

00:08:04.029 --> 00:08:04.039 align:start position:0%
 
 

00:08:04.039 --> 00:08:07.270 align:start position:0%
 
feedback<00:08:05.039><c> so</c><00:08:05.400><c> let's</c><00:08:05.680><c> create</c><00:08:06.000><c> the</c><00:08:06.159><c> agent</c><00:08:07.000><c> and</c>

00:08:07.270 --> 00:08:07.280 align:start position:0%
feedback so let's create the agent and
 

00:08:07.280 --> 00:08:10.309 align:start position:0%
feedback so let's create the agent and
create<00:08:07.599><c> the</c><00:08:07.800><c> task</c><00:08:08.800><c> let's</c><00:08:09.039><c> run</c><00:08:09.319><c> first</c><00:08:09.599><c> step</c><00:08:10.159><c> the</c>

00:08:10.309 --> 00:08:10.319 align:start position:0%
create the task let's run first step the
 

00:08:10.319 --> 00:08:13.830 align:start position:0%
create the task let's run first step the
first<00:08:10.520><c> step</c><00:08:10.800><c> is</c><00:08:11.240><c> uh</c><00:08:11.440><c> March</c><00:08:12.080><c> whatever</c><00:08:12.479><c> we</c><00:08:12.599><c> have</c>

00:08:13.830 --> 00:08:13.840 align:start position:0%
first step is uh March whatever we have
 

00:08:13.840 --> 00:08:19.390 align:start position:0%
first step is uh March whatever we have
seen<00:08:14.840><c> um</c><00:08:16.039><c> yeah</c><00:08:17.039><c> it</c><00:08:17.280><c> call</c><00:08:17.520><c> the</c><00:08:17.639><c> March</c><00:08:18.280><c> tool</c><00:08:19.199><c> and</c>

00:08:19.390 --> 00:08:19.400 align:start position:0%
seen um yeah it call the March tool and
 

00:08:19.400 --> 00:08:22.110 align:start position:0%
seen um yeah it call the March tool and
then<00:08:19.759><c> it</c><00:08:19.919><c> gets</c><00:08:20.159><c> the</c><00:08:20.479><c> response</c><00:08:21.479><c> obviously</c><00:08:22.000><c> this</c>

00:08:22.110 --> 00:08:22.120 align:start position:0%
then it gets the response obviously this
 

00:08:22.120 --> 00:08:24.990 align:start position:0%
then it gets the response obviously this
is<00:08:22.520><c> not</c><00:08:22.759><c> the</c><00:08:22.879><c> last</c><00:08:23.240><c> step</c><00:08:24.240><c> uh</c><00:08:24.360><c> the</c><00:08:24.479><c> next</c><00:08:24.680><c> step</c><00:08:24.879><c> we</c>

00:08:24.990 --> 00:08:25.000 align:start position:0%
is not the last step uh the next step we
 

00:08:25.000 --> 00:08:27.830 align:start position:0%
is not the last step uh the next step we
have<00:08:25.120><c> seen</c><00:08:25.319><c> is</c><00:08:25.440><c> June</c><00:08:25.960><c> so</c><00:08:26.599><c> we'll</c><00:08:26.919><c> ask</c><00:08:27.560><c> uh</c><00:08:27.720><c> with</c>

00:08:27.830 --> 00:08:27.840 align:start position:0%
have seen is June so we'll ask uh with
 

00:08:27.840 --> 00:08:30.629 align:start position:0%
have seen is June so we'll ask uh with
the<00:08:28.039><c> input</c><00:08:28.400><c> what</c><00:08:28.599><c> about</c><00:08:28.840><c> September</c>

00:08:30.629 --> 00:08:30.639 align:start position:0%
the input what about September
 

00:08:30.639 --> 00:08:33.709 align:start position:0%
the input what about September
so<00:08:31.280><c> now</c><00:08:31.639><c> it</c><00:08:31.800><c> will</c><00:08:32.479><c> it</c><00:08:32.599><c> should</c><00:08:32.959><c> ideally</c><00:08:33.399><c> call</c>

00:08:33.709 --> 00:08:33.719 align:start position:0%
so now it will it should ideally call
 

00:08:33.719 --> 00:08:36.029 align:start position:0%
so now it will it should ideally call
September<00:08:34.680><c> yeah</c><00:08:34.959><c> so</c><00:08:35.240><c> with</c><00:08:35.399><c> the</c><00:08:35.560><c> September</c>

00:08:36.029 --> 00:08:36.039 align:start position:0%
September yeah so with the September
 

00:08:36.039 --> 00:08:38.750 align:start position:0%
September yeah so with the September
tool<00:08:37.039><c> and</c><00:08:37.560><c> it</c><00:08:37.719><c> generates</c><00:08:38.039><c> a</c><00:08:38.240><c> response</c>

00:08:38.750 --> 00:08:38.760 align:start position:0%
tool and it generates a response
 

00:08:38.760 --> 00:08:41.630 align:start position:0%
tool and it generates a response
accordingly<00:08:39.760><c> so</c><00:08:40.000><c> in</c><00:08:40.159><c> this</c><00:08:40.360><c> way</c><00:08:40.719><c> you</c><00:08:40.880><c> can</c><00:08:41.479><c> uh</c>

00:08:41.630 --> 00:08:41.640 align:start position:0%
accordingly so in this way you can uh
 

00:08:41.640 --> 00:08:45.470 align:start position:0%
accordingly so in this way you can uh
check<00:08:42.039><c> individual</c><00:08:43.000><c> uh</c><00:08:43.320><c> stepwise</c><00:08:44.320><c> in</c><00:08:44.640><c> outputs</c>

00:08:45.470 --> 00:08:45.480 align:start position:0%
check individual uh stepwise in outputs
 

00:08:45.480 --> 00:08:49.310 align:start position:0%
check individual uh stepwise in outputs
and<00:08:45.839><c> also</c><00:08:46.839><c> uh</c><00:08:47.040><c> add</c><00:08:47.440><c> human</c><00:08:47.839><c> feedback</c><00:08:48.800><c> uh</c><00:08:49.040><c> into</c>

00:08:49.310 --> 00:08:49.320 align:start position:0%
and also uh add human feedback uh into
 

00:08:49.320 --> 00:08:52.590 align:start position:0%
and also uh add human feedback uh into
the<00:08:49.519><c> system</c><00:08:50.519><c> and</c><00:08:50.720><c> then</c><00:08:50.920><c> see</c><00:08:51.680><c> uh</c><00:08:51.959><c> how</c><00:08:52.200><c> the</c>

00:08:52.590 --> 00:08:52.600 align:start position:0%
the system and then see uh how the
 

00:08:52.600 --> 00:08:55.430 align:start position:0%
the system and then see uh how the
output<00:08:53.000><c> changes</c><00:08:54.000><c> so</c><00:08:54.200><c> let's</c><00:08:54.399><c> say</c><00:08:54.640><c> some</c>

00:08:55.430 --> 00:08:55.440 align:start position:0%
output changes so let's say some
 

00:08:55.440 --> 00:08:59.069 align:start position:0%
output changes so let's say some
sometimes<00:08:56.000><c> the</c><00:08:56.360><c> some</c><00:08:56.560><c> of</c><00:08:56.760><c> these</c><00:08:57.200><c> steps</c><00:08:58.200><c> uh</c><00:08:58.959><c> uh</c>

00:08:59.069 --> 00:08:59.079 align:start position:0%
sometimes the some of these steps uh uh
 

00:08:59.079 --> 00:09:01.829 align:start position:0%
sometimes the some of these steps uh uh
maybe<00:08:59.480><c> missed</c><00:09:00.079><c> then</c><00:09:00.279><c> you</c><00:09:00.399><c> can</c><00:09:00.680><c> add</c><00:09:01.160><c> a</c><00:09:01.440><c> human</c>

00:09:01.829 --> 00:09:01.839 align:start position:0%
maybe missed then you can add a human
 

00:09:01.839 --> 00:09:04.350 align:start position:0%
maybe missed then you can add a human
feedback<00:09:02.480><c> and</c><00:09:02.760><c> then</c>

00:09:04.350 --> 00:09:04.360 align:start position:0%
feedback and then
 

00:09:04.360 --> 00:09:06.750 align:start position:0%
feedback and then
uh<00:09:05.360><c> write</c><00:09:05.640><c> into</c><00:09:05.959><c> get</c><00:09:06.160><c> into</c><00:09:06.360><c> the</c><00:09:06.519><c> right</c>

00:09:06.750 --> 00:09:06.760 align:start position:0%
uh write into get into the right
 

00:09:06.760 --> 00:09:09.829 align:start position:0%
uh write into get into the right
direction<00:09:07.760><c> so</c><00:09:08.600><c> that's</c><00:09:08.760><c> how</c><00:09:08.959><c> you</c><00:09:09.079><c> can</c><00:09:09.240><c> use</c><00:09:09.720><c> uh</c>

00:09:09.829 --> 00:09:09.839 align:start position:0%
direction so that's how you can use uh
 

00:09:09.839 --> 00:09:12.870 align:start position:0%
direction so that's how you can use uh
stepwise<00:09:10.399><c> controllable</c><00:09:11.079><c> agents</c><00:09:12.079><c> um</c><00:09:12.600><c> do</c>

00:09:12.870 --> 00:09:12.880 align:start position:0%
stepwise controllable agents um do
 

00:09:12.880 --> 00:09:15.389 align:start position:0%
stepwise controllable agents um do
experiment<00:09:13.320><c> with</c><00:09:13.480><c> it</c><00:09:13.959><c> and</c><00:09:14.480><c> uh</c><00:09:14.880><c> explore</c><00:09:15.240><c> the</c>

00:09:15.389 --> 00:09:15.399 align:start position:0%
experiment with it and uh explore the
 

00:09:15.399 --> 00:09:19.240 align:start position:0%
experiment with it and uh explore the
notebook<00:09:16.040><c> see</c><00:09:16.240><c> you</c><00:09:16.320><c> in</c><00:09:16.399><c> the</c><00:09:16.560><c> next</c><00:09:16.760><c> video</c>

