WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:01.589 align:start position:0%
 
today<00:00:00.320><c> we're</c><00:00:00.440><c> going</c><00:00:00.560><c> to</c><00:00:00.680><c> create</c><00:00:00.960><c> our</c><00:00:01.199><c> own</c>

00:00:01.589 --> 00:00:01.599 align:start position:0%
today we're going to create our own
 

00:00:01.599 --> 00:00:04.230 align:start position:0%
today we're going to create our own
music<00:00:02.159><c> using</c><00:00:02.679><c> a</c><00:00:02.919><c> music</c><00:00:03.240><c> generation</c><00:00:03.679><c> model</c><00:00:04.120><c> so</c>

00:00:04.230 --> 00:00:04.240 align:start position:0%
music using a music generation model so
 

00:00:04.240 --> 00:00:05.910 align:start position:0%
music using a music generation model so
we're<00:00:04.359><c> going</c><00:00:04.440><c> to</c><00:00:04.560><c> use</c><00:00:04.799><c> a</c><00:00:05.040><c> Facebook</c><00:00:05.560><c> music</c>

00:00:05.910 --> 00:00:05.920 align:start position:0%
we're going to use a Facebook music
 

00:00:05.920 --> 00:00:07.590 align:start position:0%
we're going to use a Facebook music
generation<00:00:06.560><c> model</c><00:00:07.080><c> and</c><00:00:07.160><c> when</c><00:00:07.279><c> you</c><00:00:07.439><c> have</c>

00:00:07.590 --> 00:00:07.600 align:start position:0%
generation model and when you have
 

00:00:07.600 --> 00:00:09.549 align:start position:0%
generation model and when you have
something<00:00:07.839><c> like</c><00:00:08.200><c> an</c><00:00:08.360><c> 80s</c><00:00:08.840><c> pop</c><00:00:09.080><c> track</c><00:00:09.400><c> with</c>

00:00:09.549 --> 00:00:09.559 align:start position:0%
something like an 80s pop track with
 

00:00:09.559 --> 00:00:11.350 align:start position:0%
something like an 80s pop track with
Bassy<00:00:10.000><c> drums</c><00:00:10.320><c> and</c><00:00:10.519><c> synth</c><00:00:11.040><c> you'll</c><00:00:11.200><c> get</c>

00:00:11.350 --> 00:00:11.360 align:start position:0%
Bassy drums and synth you'll get
 

00:00:11.360 --> 00:00:16.750 align:start position:0%
Bassy drums and synth you'll get
something<00:00:11.679><c> like</c>

00:00:16.750 --> 00:00:16.760 align:start position:0%
 
 

00:00:16.760 --> 00:00:19.630 align:start position:0%
 
this<00:00:17.760><c> now</c><00:00:17.880><c> for</c><00:00:18.039><c> the</c><00:00:18.199><c> second</c><00:00:18.480><c> audio</c><00:00:18.880><c> file</c><00:00:19.520><c> what</c>

00:00:19.630 --> 00:00:19.640 align:start position:0%
this now for the second audio file what
 

00:00:19.640 --> 00:00:22.269 align:start position:0%
this now for the second audio file what
I<00:00:19.800><c> did</c><00:00:20.080><c> was</c><00:00:20.560><c> we</c><00:00:20.720><c> have</c><00:00:20.840><c> a</c><00:00:20.960><c> Max</c><00:00:21.279><c> new</c><00:00:21.560><c> tokens</c>

00:00:22.269 --> 00:00:22.279 align:start position:0%
I did was we have a Max new tokens
 

00:00:22.279 --> 00:00:23.990 align:start position:0%
I did was we have a Max new tokens
parameter<00:00:22.760><c> here</c><00:00:22.960><c> when</c><00:00:23.080><c> we</c><00:00:23.199><c> go</c><00:00:23.279><c> to</c><00:00:23.519><c> generate</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
parameter here when we go to generate
 

00:00:24.000 --> 00:00:26.870 align:start position:0%
parameter here when we go to generate
this<00:00:24.640><c> and</c><00:00:25.000><c> the</c><00:00:25.160><c> first</c><00:00:25.359><c> one</c><00:00:25.519><c> was</c><00:00:25.640><c> set</c><00:00:25.800><c> to</c><00:00:25.920><c> 256</c>

00:00:26.870 --> 00:00:26.880 align:start position:0%
this and the first one was set to 256
 

00:00:26.880 --> 00:00:28.950 align:start position:0%
this and the first one was set to 256
the<00:00:27.039><c> next</c><00:00:27.199><c> one</c><00:00:27.359><c> was</c><00:00:27.519><c> set</c><00:00:27.640><c> to</c><00:00:27.840><c> 512</c><00:00:28.640><c> so</c><00:00:28.800><c> it's</c>

00:00:28.950 --> 00:00:28.960 align:start position:0%
the next one was set to 512 so it's
 

00:00:28.960 --> 00:00:30.550 align:start position:0%
the next one was set to 512 so it's
going<00:00:29.039><c> to</c><00:00:29.119><c> be</c><00:00:29.240><c> about</c><00:00:29.439><c> double</c><00:00:30.119><c> length</c><00:00:30.359><c> and</c><00:00:30.480><c> this</c>

00:00:30.550 --> 00:00:30.560 align:start position:0%
going to be about double length and this
 

00:00:30.560 --> 00:00:39.430 align:start position:0%
going to be about double length and this
is<00:00:30.679><c> how</c><00:00:30.840><c> you</c><00:00:31.000><c> increase</c><00:00:31.439><c> the</c><00:00:31.640><c> length</c><00:00:32.160><c> of</c><00:00:32.320><c> your</c>

00:00:39.430 --> 00:00:39.440 align:start position:0%
 
 

00:00:39.440 --> 00:00:41.510 align:start position:0%
 
music<00:00:40.440><c> if</c><00:00:40.520><c> you're</c><00:00:40.680><c> not</c><00:00:40.920><c> familiar</c><00:00:41.360><c> with</c>

00:00:41.510 --> 00:00:41.520 align:start position:0%
music if you're not familiar with
 

00:00:41.520 --> 00:00:58.910 align:start position:0%
music if you're not familiar with
hugging<00:00:41.879><c> face</c><00:00:42.320><c> they</c><00:00:42.440><c> have</c><00:00:42.680><c> over</c>

00:00:58.910 --> 00:00:58.920 align:start position:0%
 
 

00:00:58.920 --> 00:01:01.790 align:start position:0%
 
537,500<00:00:59.960><c> still</c><00:01:00.160><c> had</c><00:01:00.359><c> about</c><00:01:01.079><c> 160,000</c>

00:01:01.790 --> 00:01:01.800 align:start position:0%
537,500 still had about 160,000
 

00:01:01.800 --> 00:01:03.869 align:start position:0%
537,500 still had about 160,000
downloads<00:01:02.680><c> okay</c><00:01:02.960><c> now</c><00:01:03.079><c> for</c><00:01:03.199><c> the</c><00:01:03.320><c> coding</c><00:01:03.600><c> part</c>

00:01:03.869 --> 00:01:03.879 align:start position:0%
downloads okay now for the coding part
 

00:01:03.879 --> 00:01:04.710 align:start position:0%
downloads okay now for the coding part
the<00:01:03.960><c> first</c><00:01:04.119><c> thing</c><00:01:04.199><c> we</c><00:01:04.320><c> going</c><00:01:04.439><c> to</c><00:01:04.519><c> do</c><00:01:04.640><c> is</c>

00:01:04.710 --> 00:01:04.720 align:start position:0%
the first thing we going to do is
 

00:01:04.720 --> 00:01:06.870 align:start position:0%
the first thing we going to do is
install<00:01:05.040><c> all</c><00:01:05.159><c> the</c><00:01:05.360><c> requirements</c><00:01:06.040><c> I'll</c><00:01:06.280><c> have</c><00:01:06.560><c> a</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
install all the requirements I'll have a
 

00:01:06.880 --> 00:01:09.870 align:start position:0%
install all the requirements I'll have a
requirements.txt<00:01:07.880><c> file</c><00:01:08.680><c> for</c><00:01:08.960><c> you</c><00:01:09.360><c> so</c><00:01:09.600><c> all</c><00:01:09.759><c> you</c>

00:01:09.870 --> 00:01:09.880 align:start position:0%
requirements.txt file for you so all you
 

00:01:09.880 --> 00:01:11.950 align:start position:0%
requirements.txt file for you so all you
have<00:01:10.000><c> to</c><00:01:10.200><c> type</c><00:01:10.439><c> is</c><00:01:10.640><c> PIP</c><00:01:10.920><c> install</c><00:01:11.439><c> d</c><00:01:11.680><c> r</c>

00:01:11.950 --> 00:01:11.960 align:start position:0%
have to type is PIP install d r
 

00:01:11.960 --> 00:01:14.550 align:start position:0%
have to type is PIP install d r
requirements.txt<00:01:12.880><c> now</c><00:01:13.680><c> this</c><00:01:13.840><c> Facebook</c><00:01:14.240><c> music</c>

00:01:14.550 --> 00:01:14.560 align:start position:0%
requirements.txt now this Facebook music
 

00:01:14.560 --> 00:01:16.390 align:start position:0%
requirements.txt now this Facebook music
gen<00:01:14.880><c> large</c><00:01:15.200><c> model</c><00:01:15.520><c> didn't</c><00:01:15.759><c> have</c><00:01:15.960><c> an</c><00:01:16.080><c> inference</c>

00:01:16.390 --> 00:01:16.400 align:start position:0%
gen large model didn't have an inference
 

00:01:16.400 --> 00:01:18.190 align:start position:0%
gen large model didn't have an inference
server<00:01:16.680><c> that</c><00:01:16.759><c> was</c><00:01:16.880><c> free</c><00:01:17.119><c> that</c><00:01:17.280><c> we</c><00:01:17.400><c> could</c><00:01:17.560><c> try</c>

00:01:18.190 --> 00:01:18.200 align:start position:0%
server that was free that we could try
 

00:01:18.200 --> 00:01:21.030 align:start position:0%
server that was free that we could try
if<00:01:18.320><c> you</c><00:01:18.560><c> go</c><00:01:18.759><c> to</c><00:01:18.960><c> the</c><00:01:19.159><c> deploy</c><00:01:19.799><c> of</c><00:01:20.040><c> this</c><00:01:20.280><c> model</c>

00:01:21.030 --> 00:01:21.040 align:start position:0%
if you go to the deploy of this model
 

00:01:21.040 --> 00:01:22.550 align:start position:0%
if you go to the deploy of this model
you<00:01:21.159><c> can</c><00:01:21.360><c> look</c><00:01:21.520><c> at</c><00:01:21.680><c> inference</c><00:01:22.040><c> end</c><00:01:22.280><c> points</c><00:01:22.439><c> and</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
you can look at inference end points and
 

00:01:22.560 --> 00:01:25.429 align:start position:0%
you can look at inference end points and
has<00:01:22.680><c> a</c><00:01:22.960><c> dedicated</c><00:01:23.479><c> one</c><00:01:24.079><c> but</c><00:01:24.320><c> this</c><00:01:24.520><c> isn't</c><00:01:24.840><c> free</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
has a dedicated one but this isn't free
 

00:01:25.439 --> 00:01:27.510 align:start position:0%
has a dedicated one but this isn't free
right<00:01:25.600><c> this</c><00:01:25.720><c> is</c><00:01:25.880><c> something</c><00:01:26.479><c> for</c><00:01:26.840><c> production</c>

00:01:27.510 --> 00:01:27.520 align:start position:0%
right this is something for production
 

00:01:27.520 --> 00:01:29.350 align:start position:0%
right this is something for production
ready<00:01:27.960><c> deployments</c><00:01:28.680><c> you</c><00:01:28.799><c> don't</c><00:01:28.960><c> want</c><00:01:29.040><c> to</c><00:01:29.159><c> do</c>

00:01:29.350 --> 00:01:29.360 align:start position:0%
ready deployments you don't want to do
 

00:01:29.360 --> 00:01:31.030 align:start position:0%
ready deployments you don't want to do
that<00:01:29.560><c> so</c><00:01:29.920><c> instead</c><00:01:30.400><c> we're</c><00:01:30.560><c> going</c><00:01:30.680><c> to</c><00:01:30.840><c> actually</c>

00:01:31.030 --> 00:01:31.040 align:start position:0%
that so instead we're going to actually
 

00:01:31.040 --> 00:01:33.230 align:start position:0%
that so instead we're going to actually
write<00:01:31.280><c> the</c><00:01:31.479><c> python</c><00:01:31.880><c> code</c><00:01:32.320><c> that</c><00:01:32.680><c> would</c><00:01:32.840><c> be</c><00:01:33.000><c> used</c>

00:01:33.230 --> 00:01:33.240 align:start position:0%
write the python code that would be used
 

00:01:33.240 --> 00:01:34.789 align:start position:0%
write the python code that would be used
on<00:01:33.320><c> the</c><00:01:33.439><c> server</c><00:01:33.759><c> anyways</c><00:01:34.439><c> I'm</c><00:01:34.560><c> going</c><00:01:34.640><c> to</c><00:01:34.720><c> be</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
on the server anyways I'm going to be
 

00:01:34.799 --> 00:01:36.710 align:start position:0%
on the server anyways I'm going to be
using<00:01:35.079><c> Transformers</c><00:01:36.040><c> and</c><00:01:36.240><c> this</c><00:01:36.360><c> is</c><00:01:36.479><c> a</c><00:01:36.560><c> little</c>

00:01:36.710 --> 00:01:36.720 align:start position:0%
using Transformers and this is a little
 

00:01:36.720 --> 00:01:38.830 align:start position:0%
using Transformers and this is a little
bit<00:01:36.840><c> more</c><00:01:37.119><c> intense</c><00:01:37.560><c> on</c><00:01:37.680><c> my</c><00:01:37.880><c> local</c><00:01:38.119><c> machine</c><00:01:38.640><c> so</c>

00:01:38.830 --> 00:01:38.840 align:start position:0%
bit more intense on my local machine so
 

00:01:38.840 --> 00:01:40.789 align:start position:0%
bit more intense on my local machine so
we're<00:01:39.000><c> going</c><00:01:39.079><c> to</c><00:01:39.399><c> try</c><00:01:39.640><c> this</c><00:01:39.759><c> on</c><00:01:39.960><c> local</c><00:01:40.200><c> machine</c>

00:01:40.789 --> 00:01:40.799 align:start position:0%
we're going to try this on local machine
 

00:01:40.799 --> 00:01:41.910 align:start position:0%
we're going to try this on local machine
but<00:01:40.920><c> I'm</c><00:01:41.040><c> probably</c><00:01:41.240><c> going</c><00:01:41.320><c> to</c><00:01:41.399><c> end</c><00:01:41.520><c> up</c><00:01:41.640><c> running</c>

00:01:41.910 --> 00:01:41.920 align:start position:0%
but I'm probably going to end up running
 

00:01:41.920 --> 00:01:44.389 align:start position:0%
but I'm probably going to end up running
it<00:01:42.159><c> on</c><00:01:42.520><c> a</c><00:01:42.759><c> better</c><00:01:42.960><c> server</c><00:01:43.240><c> on</c><00:01:43.399><c> runpod</c><00:01:44.119><c> really</c>

00:01:44.389 --> 00:01:44.399 align:start position:0%
it on a better server on runpod really
 

00:01:44.399 --> 00:01:45.709 align:start position:0%
it on a better server on runpod really
quick<00:01:44.719><c> just</c><00:01:44.840><c> so</c><00:01:44.960><c> I</c><00:01:45.079><c> can</c><00:01:45.240><c> actually</c><00:01:45.439><c> get</c><00:01:45.600><c> an</c>

00:01:45.709 --> 00:01:45.719 align:start position:0%
quick just so I can actually get an
 

00:01:45.719 --> 00:01:47.550 align:start position:0%
quick just so I can actually get an
output<00:01:46.159><c> to</c><00:01:46.280><c> show</c><00:01:46.479><c> you</c><00:01:46.840><c> but</c><00:01:47.000><c> if</c><00:01:47.119><c> your</c><00:01:47.280><c> local</c>

00:01:47.550 --> 00:01:47.560 align:start position:0%
output to show you but if your local
 

00:01:47.560 --> 00:01:49.069 align:start position:0%
output to show you but if your local
machine<00:01:47.840><c> can</c><00:01:48.000><c> handle</c><00:01:48.280><c> this</c><00:01:48.560><c> you're</c><00:01:48.799><c> not</c>

00:01:49.069 --> 00:01:49.079 align:start position:0%
machine can handle this you're not
 

00:01:49.079 --> 00:01:50.870 align:start position:0%
machine can handle this you're not
paying<00:01:49.320><c> for</c><00:01:49.560><c> anything</c><00:01:50.000><c> now</c><00:01:50.200><c> back</c><00:01:50.320><c> to</c><00:01:50.439><c> the</c><00:01:50.560><c> code</c>

00:01:50.870 --> 00:01:50.880 align:start position:0%
paying for anything now back to the code
 

00:01:50.880 --> 00:01:53.389 align:start position:0%
paying for anything now back to the code
we<00:01:51.200><c> import</c><00:01:51.640><c> everything</c><00:01:52.320><c> we</c><00:01:52.520><c> get</c><00:01:52.799><c> all</c><00:01:52.960><c> of</c><00:01:53.119><c> the</c>

00:01:53.389 --> 00:01:53.399 align:start position:0%
we import everything we get all of the
 

00:01:53.399 --> 00:01:56.709 align:start position:0%
we import everything we get all of the
pre-train<00:01:54.079><c> data</c><00:01:54.799><c> and</c><00:01:55.000><c> now</c><00:01:55.240><c> for</c><00:01:55.680><c> the</c><00:01:55.920><c> processor</c>

00:01:56.709 --> 00:01:56.719 align:start position:0%
pre-train data and now for the processor
 

00:01:56.719 --> 00:01:59.190 align:start position:0%
pre-train data and now for the processor
this<00:01:56.799><c> is</c><00:01:56.960><c> where</c><00:01:57.119><c> we</c><00:01:57.320><c> put</c><00:01:57.600><c> in</c><00:01:58.039><c> the</c><00:01:58.240><c> text</c><00:01:58.600><c> of</c><00:01:58.880><c> what</c>

00:01:59.190 --> 00:01:59.200 align:start position:0%
this is where we put in the text of what
 

00:01:59.200 --> 00:02:01.630 align:start position:0%
this is where we put in the text of what
kind<00:01:59.320><c> of</c><00:01:59.439><c> music</c><00:01:59.880><c> we</c><00:02:00.079><c> want</c><00:02:00.280><c> to</c><00:02:00.600><c> create</c><00:02:01.320><c> okay</c><00:02:01.479><c> for</c>

00:02:01.630 --> 00:02:01.640 align:start position:0%
kind of music we want to create okay for
 

00:02:01.640 --> 00:02:03.230 align:start position:0%
kind of music we want to create okay for
the<00:02:01.799><c> textt</c><00:02:02.200><c> this</c><00:02:02.399><c> time</c><00:02:02.640><c> we're</c><00:02:02.799><c> going</c><00:02:02.880><c> to</c><00:02:03.039><c> do</c>

00:02:03.230 --> 00:02:03.240 align:start position:0%
the textt this time we're going to do
 

00:02:03.240 --> 00:02:05.870 align:start position:0%
the textt this time we're going to do
something<00:02:03.680><c> I</c><00:02:03.799><c> want</c><00:02:04.159><c> like</c><00:02:04.280><c> a</c><00:02:04.479><c> Metallica</c><00:02:05.200><c> sound</c>

00:02:05.870 --> 00:02:05.880 align:start position:0%
something I want like a Metallica sound
 

00:02:05.880 --> 00:02:07.149 align:start position:0%
something I want like a Metallica sound
we'll<00:02:06.079><c> see</c><00:02:06.320><c> what</c><00:02:06.479><c> actually</c><00:02:06.680><c> happens</c><00:02:06.960><c> from</c>

00:02:07.149 --> 00:02:07.159 align:start position:0%
we'll see what actually happens from
 

00:02:07.159 --> 00:02:09.270 align:start position:0%
we'll see what actually happens from
this<00:02:07.560><c> okay</c><00:02:07.759><c> now</c><00:02:07.880><c> for</c><00:02:08.000><c> the</c><00:02:08.119><c> audio</c><00:02:08.440><c> values</c><00:02:09.000><c> for</c>

00:02:09.270 --> 00:02:09.280 align:start position:0%
this okay now for the audio values for
 

00:02:09.280 --> 00:02:11.750 align:start position:0%
this okay now for the audio values for
the<00:02:09.440><c> model</c><00:02:10.039><c> the</c><00:02:10.200><c> guidance</c><00:02:10.560><c> scale</c><00:02:11.200><c> uh</c><00:02:11.400><c> set</c><00:02:11.599><c> to</c>

00:02:11.750 --> 00:02:11.760 align:start position:0%
the model the guidance scale uh set to
 

00:02:11.760 --> 00:02:13.949 align:start position:0%
the model the guidance scale uh set to
three<00:02:12.120><c> here</c><00:02:12.440><c> this</c><00:02:12.599><c> is</c><00:02:12.840><c> seems</c><00:02:13.120><c> to</c><00:02:13.319><c> be</c><00:02:13.520><c> kind</c><00:02:13.640><c> of</c><00:02:13.800><c> a</c>

00:02:13.949 --> 00:02:13.959 align:start position:0%
three here this is seems to be kind of a
 

00:02:13.959 --> 00:02:16.790 align:start position:0%
three here this is seems to be kind of a
standard<00:02:14.400><c> to</c><00:02:14.560><c> get</c><00:02:14.800><c> the</c><00:02:15.239><c> best</c><00:02:15.519><c> output</c><00:02:16.200><c> from</c><00:02:16.560><c> the</c>

00:02:16.790 --> 00:02:16.800 align:start position:0%
standard to get the best output from the
 

00:02:16.800 --> 00:02:18.830 align:start position:0%
standard to get the best output from the
input<00:02:17.280><c> that</c><00:02:17.400><c> you</c><00:02:17.519><c> want</c><00:02:17.720><c> the</c><00:02:17.879><c> model</c><00:02:18.160><c> to</c><00:02:18.360><c> create</c>

00:02:18.830 --> 00:02:18.840 align:start position:0%
input that you want the model to create
 

00:02:18.840 --> 00:02:21.309 align:start position:0%
input that you want the model to create
the<00:02:19.000><c> max</c><00:02:19.280><c> new</c><00:02:19.480><c> tokens</c><00:02:20.080><c> again</c><00:02:20.480><c> this</c><00:02:20.640><c> is</c><00:02:21.040><c> the</c>

00:02:21.309 --> 00:02:21.319 align:start position:0%
the max new tokens again this is the
 

00:02:21.319 --> 00:02:23.110 align:start position:0%
the max new tokens again this is the
length<00:02:21.879><c> of</c><00:02:22.160><c> the</c><00:02:22.280><c> music</c><00:02:22.560><c> file</c><00:02:22.800><c> which</c><00:02:22.920><c> is</c><00:02:23.000><c> going</c>

00:02:23.110 --> 00:02:23.120 align:start position:0%
length of the music file which is going
 

00:02:23.120 --> 00:02:24.710 align:start position:0%
length of the music file which is going
to<00:02:23.160><c> be</c><00:02:23.280><c> a</c><00:02:23.640><c> wave</c><00:02:23.920><c> file</c><00:02:24.239><c> that</c><00:02:24.360><c> you</c><00:02:24.440><c> want</c><00:02:24.560><c> to</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
to be a wave file that you want to
 

00:02:24.720 --> 00:02:28.350 align:start position:0%
to be a wave file that you want to
create<00:02:25.319><c> so</c><00:02:25.560><c> we</c><00:02:25.640><c> can</c><00:02:25.840><c> try</c><00:02:26.720><c> 1024</c><00:02:27.720><c> and</c><00:02:27.920><c> see</c><00:02:28.200><c> how</c>

00:02:28.350 --> 00:02:28.360 align:start position:0%
create so we can try 1024 and see how
 

00:02:28.360 --> 00:02:30.869 align:start position:0%
create so we can try 1024 and see how
long<00:02:28.599><c> this</c><00:02:28.760><c> is</c><00:02:29.239><c> 512</c><00:02:29.840><c> was</c><00:02:30.000><c> about</c><00:02:30.160><c> 10</c><00:02:30.400><c> seconds</c><00:02:30.720><c> so</c>

00:02:30.869 --> 00:02:30.879 align:start position:0%
long this is 512 was about 10 seconds so
 

00:02:30.879 --> 00:02:32.830 align:start position:0%
long this is 512 was about 10 seconds so
I<00:02:31.040><c> suspect</c><00:02:31.360><c> it</c><00:02:31.480><c> should</c><00:02:31.680><c> be</c><00:02:31.920><c> about</c><00:02:32.120><c> 20</c><00:02:32.400><c> seconds</c>

00:02:32.830 --> 00:02:32.840 align:start position:0%
I suspect it should be about 20 seconds
 

00:02:32.840 --> 00:02:34.470 align:start position:0%
I suspect it should be about 20 seconds
okay<00:02:32.959><c> we</c><00:02:33.080><c> retrieve</c><00:02:33.440><c> the</c><00:02:33.560><c> sampling</c><00:02:34.000><c> rate</c><00:02:34.400><c> and</c>

00:02:34.470 --> 00:02:34.480 align:start position:0%
okay we retrieve the sampling rate and
 

00:02:34.480 --> 00:02:36.670 align:start position:0%
okay we retrieve the sampling rate and
then<00:02:34.599><c> in</c><00:02:34.720><c> order</c><00:02:34.920><c> to</c><00:02:35.160><c> write</c><00:02:35.519><c> this</c><00:02:35.680><c> to</c><00:02:36.000><c> an</c><00:02:36.239><c> actual</c>

00:02:36.670 --> 00:02:36.680 align:start position:0%
then in order to write this to an actual
 

00:02:36.680 --> 00:02:40.309 align:start position:0%
then in order to write this to an actual
file<00:02:37.560><c> you</c><00:02:37.879><c> use</c><00:02:38.200><c> the</c><00:02:38.400><c> scipi</c><00:02:39.239><c> library</c><00:02:40.080><c> and</c><00:02:40.200><c> then</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
file you use the scipi library and then
 

00:02:40.319 --> 00:02:42.430 align:start position:0%
file you use the scipi library and then
we're<00:02:40.440><c> going</c><00:02:40.560><c> to</c><00:02:40.680><c> write</c><00:02:40.879><c> it</c><00:02:41.000><c> to</c><00:02:41.680><c> music</c><00:02:42.040><c> gen</c>

00:02:42.430 --> 00:02:42.440 align:start position:0%
we're going to write it to music gen
 

00:02:42.440 --> 00:02:45.430 align:start position:0%
we're going to write it to music gen
output<00:02:43.319><c> Metallica</c><00:02:44.200><c> and</c><00:02:44.319><c> then</c><00:02:44.519><c> we</c><00:02:44.720><c> run</c><00:02:44.959><c> it</c><00:02:45.319><c> okay</c>

00:02:45.430 --> 00:02:45.440 align:start position:0%
output Metallica and then we run it okay
 

00:02:45.440 --> 00:02:47.390 align:start position:0%
output Metallica and then we run it okay
I<00:02:45.519><c> ran</c><00:02:45.720><c> it</c><00:02:46.120><c> it</c><00:02:46.280><c> created</c><00:02:46.599><c> the</c><00:02:46.720><c> Metallica</c><00:02:47.159><c> file</c>

00:02:47.390 --> 00:02:47.400 align:start position:0%
I ran it it created the Metallica file
 

00:02:47.400 --> 00:02:49.869 align:start position:0%
I ran it it created the Metallica file
over<00:02:47.640><c> here</c><00:02:48.080><c> I</c><00:02:48.239><c> imported</c><00:02:48.680><c> it</c><00:02:48.800><c> into</c><00:02:49.120><c> VLC</c><00:02:49.560><c> media</c>

00:02:49.869 --> 00:02:49.879 align:start position:0%
over here I imported it into VLC media
 

00:02:49.879 --> 00:02:58.750 align:start position:0%
over here I imported it into VLC media
player<00:02:50.519><c> let's</c><00:02:50.680><c> see</c><00:02:50.840><c> how</c><00:02:50.959><c> it</c><00:02:51.120><c> actually</c>

00:02:58.750 --> 00:02:58.760 align:start position:0%
 
 

00:02:58.760 --> 00:03:00.390 align:start position:0%
 
sounds

00:03:00.390 --> 00:03:00.400 align:start position:0%
sounds
 

00:03:00.400 --> 00:03:10.110 align:start position:0%
sounds
not<00:03:00.680><c> bad</c><00:03:01.000><c> sort</c>

00:03:10.110 --> 00:03:10.120 align:start position:0%
 
 

00:03:10.120 --> 00:03:12.589 align:start position:0%
 
of<00:03:11.120><c> think</c><00:03:11.280><c> it</c><00:03:11.360><c> maybe</c><00:03:11.599><c> try</c><00:03:11.799><c> to</c><00:03:11.959><c> do</c><00:03:12.239><c> a</c><00:03:12.319><c> little</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
of think it maybe try to do a little
 

00:03:12.599 --> 00:03:14.309 align:start position:0%
of think it maybe try to do a little
guitar<00:03:12.879><c> solo</c><00:03:13.560><c> at</c><00:03:13.680><c> the</c><00:03:13.760><c> end</c><00:03:13.959><c> there</c><00:03:14.120><c> I</c><00:03:14.159><c> don't</c>

00:03:14.309 --> 00:03:14.319 align:start position:0%
guitar solo at the end there I don't
 

00:03:14.319 --> 00:03:16.869 align:start position:0%
guitar solo at the end there I don't
know<00:03:14.879><c> but</c><00:03:15.480><c> hey</c><00:03:15.640><c> it</c><00:03:15.920><c> kind</c><00:03:16.080><c> of</c><00:03:16.200><c> did</c><00:03:16.400><c> what</c><00:03:16.519><c> I</c><00:03:16.640><c> asked</c>

00:03:16.869 --> 00:03:16.879 align:start position:0%
know but hey it kind of did what I asked
 

00:03:16.879 --> 00:03:18.470 align:start position:0%
know but hey it kind of did what I asked
it<00:03:17.040><c> to</c><00:03:17.319><c> all</c><00:03:17.400><c> right</c><00:03:17.519><c> this</c><00:03:17.599><c> is</c><00:03:17.720><c> day</c><00:03:17.879><c> 14</c><00:03:18.280><c> thank</c><00:03:18.440><c> you</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
it to all right this is day 14 thank you
 

00:03:18.480 --> 00:03:20.070 align:start position:0%
it to all right this is day 14 thank you
for<00:03:18.599><c> following</c><00:03:18.920><c> along</c><00:03:19.159><c> with</c><00:03:19.360><c> this</c><00:03:19.519><c> journey</c>

00:03:20.070 --> 00:03:20.080 align:start position:0%
for following along with this journey
 

00:03:20.080 --> 00:03:21.589 align:start position:0%
for following along with this journey
hope<00:03:20.239><c> this</c><00:03:20.360><c> was</c><00:03:20.480><c> like</c><00:03:20.640><c> a</c><00:03:20.720><c> little</c><00:03:20.879><c> bit</c><00:03:21.000><c> of</c><00:03:21.120><c> a</c><00:03:21.280><c> fun</c>

00:03:21.589 --> 00:03:21.599 align:start position:0%
hope this was like a little bit of a fun
 

00:03:21.599 --> 00:03:23.630 align:start position:0%
hope this was like a little bit of a fun
model<00:03:22.120><c> exercise</c><00:03:22.760><c> to</c><00:03:22.920><c> where</c><00:03:23.120><c> we</c><00:03:23.239><c> can</c><00:03:23.400><c> create</c>

00:03:23.630 --> 00:03:23.640 align:start position:0%
model exercise to where we can create
 

00:03:23.640 --> 00:03:25.550 align:start position:0%
model exercise to where we can create
our<00:03:23.799><c> own</c><00:03:24.040><c> music</c><00:03:24.400><c> for</c><00:03:24.599><c> whatever</c><00:03:24.840><c> you</c><00:03:24.959><c> want</c><00:03:25.360><c> now</c>

00:03:25.550 --> 00:03:25.560 align:start position:0%
our own music for whatever you want now
 

00:03:25.560 --> 00:03:28.070 align:start position:0%
our own music for whatever you want now
again<00:03:25.879><c> all</c><00:03:26.159><c> these</c><00:03:26.360><c> can</c><00:03:26.560><c> be</c><00:03:26.720><c> run</c><00:03:27.120><c> locally</c>

00:03:28.070 --> 00:03:28.080 align:start position:0%
again all these can be run locally
 

00:03:28.080 --> 00:03:30.229 align:start position:0%
again all these can be run locally
sometimes<00:03:28.360><c> I</c><00:03:28.480><c> have</c><00:03:28.599><c> to</c><00:03:28.720><c> use</c><00:03:29.040><c> a</c><00:03:29.159><c> run</c><00:03:29.480><c> pod</c>

00:03:30.229 --> 00:03:30.239 align:start position:0%
sometimes I have to use a run pod
 

00:03:30.239 --> 00:03:32.190 align:start position:0%
sometimes I have to use a run pod
GPU<00:03:31.000><c> because</c><00:03:31.200><c> it's</c><00:03:31.439><c> so</c><00:03:31.640><c> much</c><00:03:31.799><c> better</c><00:03:32.040><c> than</c><00:03:32.120><c> my</c>

00:03:32.190 --> 00:03:32.200 align:start position:0%
GPU because it's so much better than my
 

00:03:32.200 --> 00:03:33.789 align:start position:0%
GPU because it's so much better than my
local<00:03:32.439><c> machine</c><00:03:32.680><c> my</c><00:03:32.799><c> local</c><00:03:33.000><c> machine</c><00:03:33.400><c> just</c>

00:03:33.789 --> 00:03:33.799 align:start position:0%
local machine my local machine just
 

00:03:33.799 --> 00:03:35.990 align:start position:0%
local machine my local machine just
would<00:03:34.040><c> take</c><00:03:34.360><c> forever</c><00:03:34.840><c> to</c><00:03:35.040><c> run</c><00:03:35.200><c> it</c><00:03:35.560><c> and</c><00:03:35.720><c> that's</c>

00:03:35.990 --> 00:03:36.000 align:start position:0%
would take forever to run it and that's
 

00:03:36.000 --> 00:03:37.990 align:start position:0%
would take forever to run it and that's
okay<00:03:36.239><c> but</c><00:03:36.439><c> just</c><00:03:37.159><c> understand</c><00:03:37.400><c> that</c><00:03:37.560><c> all</c><00:03:37.840><c> these</c>

00:03:37.990 --> 00:03:38.000 align:start position:0%
okay but just understand that all these
 

00:03:38.000 --> 00:03:40.309 align:start position:0%
okay but just understand that all these
can<00:03:38.120><c> be</c><00:03:38.239><c> run</c><00:03:38.640><c> locally</c><00:03:39.080><c> on</c><00:03:39.239><c> your</c><00:03:39.400><c> machine</c><00:03:40.080><c> if</c><00:03:40.200><c> it</c>

00:03:40.309 --> 00:03:40.319 align:start position:0%
can be run locally on your machine if it
 

00:03:40.319 --> 00:03:41.750 align:start position:0%
can be run locally on your machine if it
can<00:03:40.439><c> handle</c><00:03:40.680><c> it</c><00:03:41.040><c> the</c><00:03:41.200><c> idea</c><00:03:41.480><c> with</c><00:03:41.560><c> some</c><00:03:41.680><c> of</c>

00:03:41.750 --> 00:03:41.760 align:start position:0%
can handle it the idea with some of
 

00:03:41.760 --> 00:03:43.830 align:start position:0%
can handle it the idea with some of
these<00:03:41.959><c> videos</c><00:03:42.599><c> is</c><00:03:42.760><c> that</c><00:03:42.920><c> it</c><00:03:43.239><c> gives</c><00:03:43.480><c> you</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
these videos is that it gives you
 

00:03:43.840 --> 00:03:46.350 align:start position:0%
these videos is that it gives you
Insight<00:03:44.200><c> on</c><00:03:44.480><c> how</c><00:03:44.640><c> to</c><00:03:44.840><c> do</c><00:03:45.040><c> it</c><00:03:45.599><c> then</c><00:03:45.799><c> you</c><00:03:45.879><c> can</c><00:03:46.120><c> get</c>

00:03:46.350 --> 00:03:46.360 align:start position:0%
Insight on how to do it then you can get
 

00:03:46.360 --> 00:03:49.509 align:start position:0%
Insight on how to do it then you can get
more<00:03:46.760><c> ideas</c><00:03:47.200><c> how</c><00:03:47.319><c> to</c><00:03:47.599><c> improve</c><00:03:48.000><c> it</c><00:03:48.640><c> okay</c><00:03:49.200><c> always</c>

00:03:49.509 --> 00:03:49.519 align:start position:0%
more ideas how to improve it okay always
 

00:03:49.519 --> 00:03:51.390 align:start position:0%
more ideas how to improve it okay always
try<00:03:49.720><c> to</c><00:03:49.959><c> improve</c><00:03:50.439><c> anything</c><00:03:50.760><c> that</c><00:03:50.879><c> I'm</c><00:03:51.000><c> doing</c>

00:03:51.390 --> 00:03:51.400 align:start position:0%
try to improve anything that I'm doing
 

00:03:51.400 --> 00:03:54.720 align:start position:0%
try to improve anything that I'm doing
I'll<00:03:51.560><c> see</c><00:03:51.680><c> you</c><00:03:51.840><c> guys</c><00:03:52.120><c> next</c><00:03:52.319><c> video</c>

