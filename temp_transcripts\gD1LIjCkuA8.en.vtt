WEBVTT
Kind: captions
Language: en

00:00:01.870 --> 00:00:04.230 align:start position:0%
 
[Music]

00:00:04.230 --> 00:00:04.240 align:start position:0%
[Music]
 

00:00:04.240 --> 00:00:06.550 align:start position:0%
[Music]
So<00:00:04.400><c> my</c><00:00:04.560><c> name</c><00:00:04.640><c> is</c><00:00:04.720><c> Yuk</c><00:00:05.200><c> <PERSON><PERSON></c><00:00:05.759><c> and</c><00:00:06.000><c> I'm</c><00:00:06.240><c> reading</c>

00:00:06.550 --> 00:00:06.560 align:start position:0%
So my name is <PERSON><PERSON> and I'm reading
 

00:00:06.560 --> 00:00:08.629 align:start position:0%
So my name is <PERSON><PERSON> and I'm reading
the<00:00:06.720><c> product</c><00:00:07.040><c> and</c><00:00:07.279><c> engineering</c><00:00:07.839><c> and</c><00:00:08.000><c> rakuten</c>

00:00:08.629 --> 00:00:08.639 align:start position:0%
the product and engineering and rakuten
 

00:00:08.639 --> 00:00:11.110 align:start position:0%
the product and engineering and rakuten
as<00:00:08.800><c> a</c><00:00:08.960><c> general</c><00:00:09.280><c> manager</c><00:00:09.760><c> AI</c><00:00:10.080><c> for</c><00:00:10.320><c> business.</c><00:00:10.960><c> So</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
as a general manager AI for business. So
 

00:00:11.120 --> 00:00:12.950 align:start position:0%
as a general manager AI for business. So
at<00:00:11.280><c> Rakuten</c><00:00:11.920><c> uh</c><00:00:12.000><c> we</c><00:00:12.080><c> are</c><00:00:12.240><c> building</c><00:00:12.400><c> a</c><00:00:12.559><c> suite</c><00:00:12.800><c> of</c>

00:00:12.950 --> 00:00:12.960 align:start position:0%
at Rakuten uh we are building a suite of
 

00:00:12.960 --> 00:00:14.870 align:start position:0%
at Rakuten uh we are building a suite of
AI<00:00:13.280><c> product</c><00:00:13.679><c> uh</c><00:00:13.759><c> that</c><00:00:14.000><c> empower</c><00:00:14.400><c> both</c><00:00:14.639><c> our</c>

00:00:14.870 --> 00:00:14.880 align:start position:0%
AI product uh that empower both our
 

00:00:14.880 --> 00:00:17.189 align:start position:0%
AI product uh that empower both our
employee<00:00:15.280><c> and</c><00:00:15.519><c> our</c><00:00:15.759><c> customers.</c><00:00:16.720><c> So</c><00:00:16.880><c> we</c><00:00:17.039><c> have</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
employee and our customers. So we have
 

00:00:17.199 --> 00:00:19.429 align:start position:0%
employee and our customers. So we have
built<00:00:17.440><c> the</c><00:00:17.680><c> racketen</c><00:00:18.240><c> AI</c><00:00:18.560><c> for</c><00:00:18.800><c> business</c><00:00:19.199><c> to</c>

00:00:19.429 --> 00:00:19.439 align:start position:0%
built the racketen AI for business to
 

00:00:19.439 --> 00:00:21.910 align:start position:0%
built the racketen AI for business to
support<00:00:19.840><c> our</c><00:00:20.160><c> business</c><00:00:20.480><c> client</c><00:00:21.119><c> in</c><00:00:21.520><c> essential</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
support our business client in essential
 

00:00:21.920 --> 00:00:23.750 align:start position:0%
support our business client in essential
business<00:00:22.240><c> operation</c><00:00:22.720><c> from</c><00:00:22.960><c> market</c><00:00:23.279><c> analysis</c>

00:00:23.750 --> 00:00:23.760 align:start position:0%
business operation from market analysis
 

00:00:23.760 --> 00:00:26.230 align:start position:0%
business operation from market analysis
to<00:00:24.000><c> customer</c><00:00:24.320><c> support.</c><00:00:25.199><c> So</c><00:00:25.439><c> in</c><00:00:25.600><c> addition</c><00:00:26.160><c> uh</c>

00:00:26.230 --> 00:00:26.240 align:start position:0%
to customer support. So in addition uh
 

00:00:26.240 --> 00:00:28.310 align:start position:0%
to customer support. So in addition uh
we<00:00:26.400><c> have</c><00:00:26.560><c> built</c><00:00:26.960><c> our</c><00:00:27.199><c> internal</c><00:00:27.599><c> generative</c><00:00:28.080><c> AI</c>

00:00:28.310 --> 00:00:28.320 align:start position:0%
we have built our internal generative AI
 

00:00:28.320 --> 00:00:31.189 align:start position:0%
we have built our internal generative AI
platform<00:00:28.800><c> designed</c><00:00:29.199><c> for</c><00:00:29.760><c> over</c><00:00:30.160><c> 70</c><00:00:30.560><c> plus</c><00:00:31.039><c> uh</c>

00:00:31.189 --> 00:00:31.199 align:start position:0%
platform designed for over 70 plus uh
 

00:00:31.199 --> 00:00:33.990 align:start position:0%
platform designed for over 70 plus uh
business<00:00:31.519><c> across</c><00:00:31.840><c> Japan</c><00:00:32.239><c> and</c><00:00:32.520><c> beyond.</c><00:00:33.520><c> So</c><00:00:33.760><c> our</c>

00:00:33.990 --> 00:00:34.000 align:start position:0%
business across Japan and beyond. So our
 

00:00:34.000 --> 00:00:36.790 align:start position:0%
business across Japan and beyond. So our
agentic<00:00:34.880><c> workflow</c><00:00:35.600><c> are</c><00:00:35.680><c> powered</c><00:00:36.000><c> by</c><00:00:36.239><c> Rangraph</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
agentic workflow are powered by Rangraph
 

00:00:36.800 --> 00:00:39.350 align:start position:0%
agentic workflow are powered by Rangraph
and<00:00:37.040><c> our</c><00:00:37.360><c> employee</c><00:00:37.760><c> to</c><00:00:38.000><c> create</c><00:00:38.480><c> uh</c><00:00:38.640><c> and</c><00:00:38.879><c> shared</c>

00:00:39.350 --> 00:00:39.360 align:start position:0%
and our employee to create uh and shared
 

00:00:39.360 --> 00:00:41.830 align:start position:0%
and our employee to create uh and shared
AI<00:00:39.680><c> agent</c><00:00:40.160><c> uh</c><00:00:40.320><c> with</c><00:00:40.559><c> minimal</c><00:00:40.879><c> coding.</c><00:00:41.600><c> Our</c>

00:00:41.830 --> 00:00:41.840 align:start position:0%
AI agent uh with minimal coding. Our
 

00:00:41.840 --> 00:00:44.470 align:start position:0%
AI agent uh with minimal coding. Our
goal<00:00:42.160><c> is</c><00:00:42.399><c> to</c><00:00:42.640><c> democratize</c><00:00:43.360><c> AI</c><00:00:43.760><c> so</c><00:00:43.920><c> that</c><00:00:44.239><c> team</c>

00:00:44.470 --> 00:00:44.480 align:start position:0%
goal is to democratize AI so that team
 

00:00:44.480 --> 00:00:46.950 align:start position:0%
goal is to democratize AI so that team
across<00:00:44.879><c> Rakuten</c><00:00:45.600><c> and</c><00:00:45.760><c> our</c><00:00:46.000><c> client</c><00:00:46.640><c> could</c>

00:00:46.950 --> 00:00:46.960 align:start position:0%
across Rakuten and our client could
 

00:00:46.960 --> 00:00:49.110 align:start position:0%
across Rakuten and our client could
build<00:00:47.280><c> and</c><00:00:47.600><c> deploy</c><00:00:47.920><c> useful</c><00:00:48.320><c> agent</c><00:00:48.640><c> quickly.</c>

00:00:49.110 --> 00:00:49.120 align:start position:0%
build and deploy useful agent quickly.
 

00:00:49.120 --> 00:00:50.869 align:start position:0%
build and deploy useful agent quickly.
Before<00:00:49.520><c> we</c><00:00:49.680><c> started</c><00:00:50.000><c> using</c><00:00:50.239><c> Rangraph</c><00:00:50.800><c> and</c>

00:00:50.869 --> 00:00:50.879 align:start position:0%
Before we started using Rangraph and
 

00:00:50.879 --> 00:00:53.029 align:start position:0%
Before we started using Rangraph and
Rangumis,<00:00:51.760><c> our</c><00:00:52.079><c> biggest</c><00:00:52.399><c> struggle</c><00:00:52.800><c> was</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
Rangumis, our biggest struggle was
 

00:00:53.039 --> 00:00:55.189 align:start position:0%
Rangumis, our biggest struggle was
evaluating<00:00:53.600><c> new</c><00:00:53.840><c> model</c><00:00:54.160><c> and</c><00:00:54.399><c> tool</c><00:00:54.719><c> built</c><00:00:55.039><c> on</c>

00:00:55.189 --> 00:00:55.199 align:start position:0%
evaluating new model and tool built on
 

00:00:55.199 --> 00:00:57.430 align:start position:0%
evaluating new model and tool built on
top<00:00:55.360><c> of</c><00:00:55.600><c> them</c><00:00:56.000><c> as</c><00:00:56.239><c> well</c><00:00:56.399><c> as</c><00:00:56.559><c> implementing</c><00:00:57.199><c> and</c>

00:00:57.430 --> 00:00:57.440 align:start position:0%
top of them as well as implementing and
 

00:00:57.440 --> 00:01:00.229 align:start position:0%
top of them as well as implementing and
testing<00:00:57.840><c> new</c><00:00:58.079><c> agent</c><00:00:58.760><c> architecture.</c><00:00:59.760><c> The</c><00:00:59.920><c> AI</c>

00:01:00.229 --> 00:01:00.239 align:start position:0%
testing new agent architecture. The AI
 

00:01:00.239 --> 00:01:02.310 align:start position:0%
testing new agent architecture. The AI
space<00:01:00.559><c> move</c><00:01:00.800><c> very</c><00:01:01.039><c> fast</c><00:01:01.359><c> so</c><00:01:01.600><c> that</c><00:01:01.920><c> there's</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
space move very fast so that there's
 

00:01:02.320 --> 00:01:04.390 align:start position:0%
space move very fast so that there's
something<00:01:02.640><c> new</c><00:01:02.960><c> every</c><00:01:03.280><c> week</c><00:01:03.600><c> and</c><00:01:03.840><c> our</c><00:01:04.080><c> team</c>

00:01:04.390 --> 00:01:04.400 align:start position:0%
something new every week and our team
 

00:01:04.400 --> 00:01:06.870 align:start position:0%
something new every week and our team
was<00:01:04.640><c> forced</c><00:01:04.879><c> to</c><00:01:05.199><c> make</c><00:01:05.680><c> decision</c><00:01:06.159><c> based</c><00:01:06.400><c> on</c><00:01:06.640><c> our</c>

00:01:06.870 --> 00:01:06.880 align:start position:0%
was forced to make decision based on our
 

00:01:06.880 --> 00:01:08.630 align:start position:0%
was forced to make decision based on our
intuition<00:01:07.360><c> which</c><00:01:07.600><c> isn't</c><00:01:07.920><c> a</c><00:01:08.159><c> reliable</c>

00:01:08.630 --> 00:01:08.640 align:start position:0%
intuition which isn't a reliable
 

00:01:08.640 --> 00:01:10.789 align:start position:0%
intuition which isn't a reliable
foundation<00:01:09.040><c> for</c><00:01:09.320><c> innovation.</c><00:01:10.320><c> What</c><00:01:10.479><c> we</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
foundation for innovation. What we
 

00:01:10.799 --> 00:01:13.109 align:start position:0%
foundation for innovation. What we
really<00:01:11.119><c> need</c><00:01:11.439><c> was</c><00:01:11.680><c> a</c><00:01:11.840><c> more</c><00:01:12.080><c> structured</c><00:01:12.640><c> way</c><00:01:12.799><c> to</c>

00:01:13.109 --> 00:01:13.119 align:start position:0%
really need was a more structured way to
 

00:01:13.119 --> 00:01:15.190 align:start position:0%
really need was a more structured way to
test<00:01:13.360><c> new</c><00:01:13.600><c> approach</c><00:01:14.400><c> something</c><00:01:14.720><c> better</c><00:01:14.960><c> than</c>

00:01:15.190 --> 00:01:15.200 align:start position:0%
test new approach something better than
 

00:01:15.200 --> 00:01:17.429 align:start position:0%
test new approach something better than
just<00:01:15.439><c> sip</c><00:01:15.680><c> it</c><00:01:15.920><c> and</c><00:01:16.159><c> see</c><00:01:16.320><c> what</c><00:01:16.560><c> happen.</c><00:01:17.119><c> Brand</c>

00:01:17.429 --> 00:01:17.439 align:start position:0%
just sip it and see what happen. Brand
 

00:01:17.439 --> 00:01:19.670 align:start position:0%
just sip it and see what happen. Brand
Smith<00:01:17.759><c> gave</c><00:01:17.920><c> us</c><00:01:18.080><c> a</c><00:01:18.320><c> structured</c><00:01:19.040><c> scientific</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
Smith gave us a structured scientific
 

00:01:19.680 --> 00:01:21.749 align:start position:0%
Smith gave us a structured scientific
way<00:01:19.920><c> to</c><00:01:20.240><c> figure</c><00:01:20.400><c> out</c><00:01:20.799><c> what</c><00:01:21.119><c> was</c><00:01:21.360><c> actually</c>

00:01:21.749 --> 00:01:21.759 align:start position:0%
way to figure out what was actually
 

00:01:21.759 --> 00:01:23.590 align:start position:0%
way to figure out what was actually
working<00:01:22.400><c> whether</c><00:01:22.640><c> it</c><00:01:22.880><c> was</c><00:01:23.119><c> pair</c><00:01:23.360><c> wise</c>

00:01:23.590 --> 00:01:23.600 align:start position:0%
working whether it was pair wise
 

00:01:23.600 --> 00:01:26.310 align:start position:0%
working whether it was pair wise
abration<00:01:24.080><c> or</c><00:01:24.640><c> digging</c><00:01:25.040><c> into</c><00:01:25.600><c> why</c><00:01:25.840><c> accuracy</c>

00:01:26.310 --> 00:01:26.320 align:start position:0%
abration or digging into why accuracy
 

00:01:26.320 --> 00:01:29.429 align:start position:0%
abration or digging into why accuracy
jumped<00:01:26.640><c> from</c><00:01:26.880><c> 70%</c><00:01:27.520><c> to</c><00:01:27.759><c> 80%.</c><00:01:28.640><c> Our</c><00:01:28.880><c> engineers</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
jumped from 70% to 80%. Our engineers
 

00:01:29.439 --> 00:01:31.350 align:start position:0%
jumped from 70% to 80%. Our engineers
especially<00:01:29.920><c> love</c><00:01:30.159><c> the</c><00:01:30.400><c> intuitive</c><00:01:30.880><c> debugging</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
especially love the intuitive debugging
 

00:01:31.360 --> 00:01:33.749 align:start position:0%
especially love the intuitive debugging
experience.<00:01:32.079><c> It</c><00:01:32.320><c> saved</c><00:01:32.560><c> us</c><00:01:32.799><c> a</c><00:01:32.960><c> lot</c><00:01:33.119><c> of</c><00:01:33.360><c> time.</c>

00:01:33.749 --> 00:01:33.759 align:start position:0%
experience. It saved us a lot of time.
 

00:01:33.759 --> 00:01:35.990 align:start position:0%
experience. It saved us a lot of time.
Rangraph<00:01:34.320><c> became</c><00:01:34.640><c> our</c><00:01:34.799><c> go-to</c><00:01:35.280><c> for</c><00:01:35.600><c> building</c>

00:01:35.990 --> 00:01:36.000 align:start position:0%
Rangraph became our go-to for building
 

00:01:36.000 --> 00:01:38.390 align:start position:0%
Rangraph became our go-to for building
production<00:01:36.479><c> ready</c><00:01:36.720><c> agent.</c><00:01:37.439><c> It</c><00:01:37.680><c> give</c><00:01:37.920><c> us</c><00:01:38.079><c> the</c>

00:01:38.390 --> 00:01:38.400 align:start position:0%
production ready agent. It give us the
 

00:01:38.400 --> 00:01:40.390 align:start position:0%
production ready agent. It give us the
flexibility<00:01:38.960><c> to</c><00:01:39.200><c> add</c><00:01:39.520><c> human</c><00:01:39.759><c> in</c><00:01:39.840><c> the</c><00:01:39.920><c> loop</c>

00:01:40.390 --> 00:01:40.400 align:start position:0%
flexibility to add human in the loop
 

00:01:40.400 --> 00:01:42.390 align:start position:0%
flexibility to add human in the loop
where<00:01:40.640><c> needed.</c><00:01:41.119><c> So</c><00:01:41.280><c> rang</c><00:01:41.600><c> graph</c><00:01:41.840><c> also</c><00:01:42.159><c> helped</c>

00:01:42.390 --> 00:01:42.400 align:start position:0%
where needed. So rang graph also helped
 

00:01:42.400 --> 00:01:44.469 align:start position:0%
where needed. So rang graph also helped
us<00:01:42.640><c> avoid</c><00:01:43.040><c> bender</c><00:01:43.439><c> locking</c><00:01:43.920><c> by</c><00:01:44.079><c> easily</c>

00:01:44.469 --> 00:01:44.479 align:start position:0%
us avoid bender locking by easily
 

00:01:44.479 --> 00:01:46.789 align:start position:0%
us avoid bender locking by easily
swapping<00:01:44.960><c> models</c><00:01:45.520><c> and</c><00:01:45.759><c> keep</c><00:01:46.079><c> everything</c><00:01:46.479><c> in</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
swapping models and keep everything in
 

00:01:46.799 --> 00:01:49.350 align:start position:0%
swapping models and keep everything in
one<00:01:47.119><c> ecosystem</c><00:01:47.759><c> across</c><00:01:48.159><c> teams</c><00:01:48.880><c> which</c><00:01:49.119><c> made</c>

00:01:49.350 --> 00:01:49.360 align:start position:0%
one ecosystem across teams which made
 

00:01:49.360 --> 00:01:51.429 align:start position:0%
one ecosystem across teams which made
coordination<00:01:50.000><c> way</c><00:01:50.240><c> easier.</c><00:01:50.799><c> The</c><00:01:50.960><c> biggest</c><00:01:51.200><c> win</c>

00:01:51.429 --> 00:01:51.439 align:start position:0%
coordination way easier. The biggest win
 

00:01:51.439 --> 00:01:53.590 align:start position:0%
coordination way easier. The biggest win
for<00:01:51.680><c> us</c><00:01:51.840><c> from</c><00:01:52.079><c> using</c><00:01:52.320><c> rang</c><00:01:52.720><c> chains</c><00:01:53.040><c> tool</c><00:01:53.360><c> has</c>

00:01:53.590 --> 00:01:53.600 align:start position:0%
for us from using rang chains tool has
 

00:01:53.600 --> 00:01:55.749 align:start position:0%
for us from using rang chains tool has
been<00:01:53.840><c> achieving</c><00:01:54.320><c> faster</c><00:01:54.720><c> time</c><00:01:54.880><c> to</c><00:01:55.200><c> market.</c>

00:01:55.749 --> 00:01:55.759 align:start position:0%
been achieving faster time to market.
 

00:01:55.759 --> 00:01:58.230 align:start position:0%
been achieving faster time to market.
Thanks<00:01:56.079><c> to</c><00:01:56.159><c> ranksmith</c><00:01:56.799><c> and</c><00:01:56.960><c> rang</c><00:01:57.360><c> graph</c><00:01:58.000><c> we</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
Thanks to ranksmith and rang graph we
 

00:01:58.240 --> 00:02:00.630 align:start position:0%
Thanks to ranksmith and rang graph we
can<00:01:58.399><c> iterate</c><00:01:58.880><c> and</c><00:01:59.119><c> release</c><00:01:59.520><c> new</c><00:01:59.840><c> AI</c><00:02:00.159><c> features</c>

00:02:00.630 --> 00:02:00.640 align:start position:0%
can iterate and release new AI features
 

00:02:00.640 --> 00:02:02.870 align:start position:0%
can iterate and release new AI features
much<00:02:00.960><c> faster</c><00:02:01.280><c> than</c><00:02:01.600><c> competitors</c><00:02:02.399><c> which</c><00:02:02.640><c> give</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
much faster than competitors which give
 

00:02:02.880 --> 00:02:05.350 align:start position:0%
much faster than competitors which give
us<00:02:03.040><c> a</c><00:02:03.360><c> strong</c><00:02:03.680><c> edge.</c><00:02:04.320><c> Internally,</c><00:02:05.119><c> the</c>

00:02:05.350 --> 00:02:05.360 align:start position:0%
us a strong edge. Internally, the
 

00:02:05.360 --> 00:02:07.429 align:start position:0%
us a strong edge. Internally, the
engineering<00:02:05.840><c> team</c><00:02:06.159><c> test</c><00:02:06.479><c> and</c><00:02:06.719><c> deliver</c><00:02:07.119><c> more</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
engineering team test and deliver more
 

00:02:07.439 --> 00:02:09.749 align:start position:0%
engineering team test and deliver more
features<00:02:07.920><c> thanks</c><00:02:08.239><c> to</c><00:02:08.479><c> reusable</c><00:02:09.119><c> evaration</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
features thanks to reusable evaration
 

00:02:09.759 --> 00:02:12.470 align:start position:0%
features thanks to reusable evaration
template,<00:02:10.640><c> faster</c><00:02:11.039><c> debugging</c><00:02:11.760><c> and</c><00:02:12.080><c> easier</c>

00:02:12.470 --> 00:02:12.480 align:start position:0%
template, faster debugging and easier
 

00:02:12.480 --> 00:02:15.110 align:start position:0%
template, faster debugging and easier
deployment<00:02:13.040><c> flows.</c><00:02:13.920><c> This</c><00:02:14.160><c> ties</c><00:02:14.480><c> back</c><00:02:14.640><c> to</c><00:02:14.879><c> our</c>

00:02:15.110 --> 00:02:15.120 align:start position:0%
deployment flows. This ties back to our
 

00:02:15.120 --> 00:02:17.430 align:start position:0%
deployment flows. This ties back to our
vision<00:02:15.440><c> of</c><00:02:15.680><c> empowering</c><00:02:16.239><c> both</c><00:02:16.560><c> business,</c><00:02:17.120><c> our</c>

00:02:17.430 --> 00:02:17.440 align:start position:0%
vision of empowering both business, our
 

00:02:17.440 --> 00:02:20.070 align:start position:0%
vision of empowering both business, our
employees<00:02:18.000><c> and</c><00:02:18.319><c> society</c><00:02:18.720><c> as</c><00:02:18.959><c> a</c><00:02:19.120><c> whole.</c><00:02:19.680><c> So</c><00:02:19.840><c> if</c>

00:02:20.070 --> 00:02:20.080 align:start position:0%
employees and society as a whole. So if
 

00:02:20.080 --> 00:02:21.990 align:start position:0%
employees and society as a whole. So if
you<00:02:20.239><c> are</c><00:02:20.400><c> building</c><00:02:20.800><c> AI</c><00:02:21.120><c> application</c><00:02:21.680><c> at</c>

00:02:21.990 --> 00:02:22.000 align:start position:0%
you are building AI application at
 

00:02:22.000 --> 00:02:24.949 align:start position:0%
you are building AI application at
scale,<00:02:22.480><c> Rangraph,</c><00:02:23.120><c> Rangmith</c><00:02:23.920><c> and</c><00:02:24.160><c> Rang</c>

00:02:24.949 --> 00:02:24.959 align:start position:0%
scale, Rangraph, Rangmith and Rang
 

00:02:24.959 --> 00:02:26.869 align:start position:0%
scale, Rangraph, Rangmith and Rang
ecosystem<00:02:25.599><c> let</c><00:02:25.840><c> you</c><00:02:26.080><c> stand</c><00:02:26.319><c> on</c><00:02:26.480><c> the</c><00:02:26.640><c> shoulder</c>

00:02:26.869 --> 00:02:26.879 align:start position:0%
ecosystem let you stand on the shoulder
 

00:02:26.879 --> 00:02:28.869 align:start position:0%
ecosystem let you stand on the shoulder
of<00:02:27.040><c> the</c><00:02:27.200><c> open</c><00:02:27.440><c> source</c><00:02:27.760><c> community,</c><00:02:28.640><c> move</c>

00:02:28.869 --> 00:02:28.879 align:start position:0%
of the open source community, move
 

00:02:28.879 --> 00:02:31.030 align:start position:0%
of the open source community, move
faster<00:02:29.360><c> and</c><00:02:29.680><c> stay</c><00:02:29.920><c> flexible</c><00:02:30.400><c> in</c><00:02:30.640><c> a</c><00:02:30.800><c> fast</c>

00:02:31.030 --> 00:02:31.040 align:start position:0%
faster and stay flexible in a fast
 

00:02:31.040 --> 00:02:33.589 align:start position:0%
faster and stay flexible in a fast
changing<00:02:31.440><c> landscape.</c><00:02:32.560><c> We</c><00:02:32.879><c> consider</c><00:02:33.200><c> them</c><00:02:33.440><c> as</c>

00:02:33.589 --> 00:02:33.599 align:start position:0%
changing landscape. We consider them as
 

00:02:33.599 --> 00:02:37.560 align:start position:0%
changing landscape. We consider them as
a<00:02:33.840><c> foundation</c><00:02:34.480><c> for</c><00:02:34.800><c> innovation.</c>

00:02:37.560 --> 00:02:37.570 align:start position:0%
a foundation for innovation.
 

00:02:37.570 --> 00:02:40.639 align:start position:0%
a foundation for innovation.
[Music]

