WEBVTT
Kind: captions
Language: en

00:00:00.040 --> 00:00:03.030 align:start position:0%
 
co-pilot<00:00:00.680><c> autofix</c><00:00:01.439><c> is</c><00:00:01.640><c> now</c><00:00:01.959><c> available</c><00:00:02.760><c> for</c>

00:00:03.030 --> 00:00:03.040 align:start position:0%
co-pilot autofix is now available for
 

00:00:03.040 --> 00:00:05.910 align:start position:0%
co-pilot autofix is now available for
free<00:00:03.600><c> for</c><00:00:03.879><c> open</c><00:00:04.240><c> source</c><00:00:04.960><c> GitHub</c><00:00:05.480><c> has</c><00:00:05.640><c> long</c>

00:00:05.910 --> 00:00:05.920 align:start position:0%
free for open source GitHub has long
 

00:00:05.920 --> 00:00:08.110 align:start position:0%
free for open source GitHub has long
offered<00:00:06.399><c> code</c><00:00:06.680><c> scanning</c><00:00:07.279><c> which</c><00:00:07.480><c> detects</c>

00:00:08.110 --> 00:00:08.120 align:start position:0%
offered code scanning which detects
 

00:00:08.120 --> 00:00:10.030 align:start position:0%
offered code scanning which detects
security<00:00:08.559><c> vulnerabilities</c><00:00:09.360><c> in</c><00:00:09.519><c> your</c><00:00:09.719><c> pool</c>

00:00:10.030 --> 00:00:10.040 align:start position:0%
security vulnerabilities in your pool
 

00:00:10.040 --> 00:00:12.589 align:start position:0%
security vulnerabilities in your pool
requests<00:00:10.679><c> and</c><00:00:10.840><c> alerts</c><00:00:11.280><c> you</c><00:00:11.559><c> to</c><00:00:11.719><c> fix</c><00:00:11.960><c> the</c><00:00:12.160><c> bag</c>

00:00:12.589 --> 00:00:12.599 align:start position:0%
requests and alerts you to fix the bag
 

00:00:12.599 --> 00:00:14.190 align:start position:0%
requests and alerts you to fix the bag
you<00:00:12.719><c> will</c><00:00:13.000><c> then</c><00:00:13.200><c> go</c><00:00:13.400><c> back</c><00:00:13.599><c> to</c><00:00:13.719><c> your</c><00:00:13.920><c> code</c>

00:00:14.190 --> 00:00:14.200 align:start position:0%
you will then go back to your code
 

00:00:14.200 --> 00:00:17.189 align:start position:0%
you will then go back to your code
editor<00:00:15.000><c> make</c><00:00:15.280><c> changes</c><00:00:15.920><c> and</c><00:00:16.119><c> push</c><00:00:16.440><c> again</c><00:00:16.960><c> to</c>

00:00:17.189 --> 00:00:17.199 align:start position:0%
editor make changes and push again to
 

00:00:17.199 --> 00:00:19.070 align:start position:0%
editor make changes and push again to
check<00:00:17.600><c> if</c><00:00:17.760><c> the</c><00:00:17.960><c> security</c><00:00:18.359><c> alert</c><00:00:18.720><c> has</c><00:00:18.880><c> been</c>

00:00:19.070 --> 00:00:19.080 align:start position:0%
check if the security alert has been
 

00:00:19.080 --> 00:00:21.990 align:start position:0%
check if the security alert has been
resolved<00:00:19.720><c> now</c><00:00:20.039><c> with</c><00:00:20.279><c> co-pilot</c><00:00:20.920><c> autofix</c><00:00:21.800><c> the</c>

00:00:21.990 --> 00:00:22.000 align:start position:0%
resolved now with co-pilot autofix the
 

00:00:22.000 --> 00:00:25.029 align:start position:0%
resolved now with co-pilot autofix the
power<00:00:22.320><c> of</c><00:00:22.560><c> AI</c><00:00:23.320><c> combined</c><00:00:23.800><c> with</c><00:00:24.039><c> code</c><00:00:24.320><c> scanning</c>

00:00:25.029 --> 00:00:25.039 align:start position:0%
power of AI combined with code scanning
 

00:00:25.039 --> 00:00:27.230 align:start position:0%
power of AI combined with code scanning
will<00:00:25.320><c> suggest</c><00:00:25.720><c> a</c><00:00:25.880><c> fix</c><00:00:26.080><c> for</c><00:00:26.279><c> the</c><00:00:26.400><c> alert</c><00:00:26.880><c> inside</c>

00:00:27.230 --> 00:00:27.240 align:start position:0%
will suggest a fix for the alert inside
 

00:00:27.240 --> 00:00:29.349 align:start position:0%
will suggest a fix for the alert inside
the<00:00:27.400><c> pull</c><00:00:27.679><c> request</c><00:00:28.439><c> here</c><00:00:28.800><c> I'm</c><00:00:29.000><c> pushing</c>

00:00:29.349 --> 00:00:29.359 align:start position:0%
the pull request here I'm pushing
 

00:00:29.359 --> 00:00:32.069 align:start position:0%
the pull request here I'm pushing
vulnerable<00:00:30.080><c> code</c><00:00:30.519><c> and</c><00:00:30.720><c> copilot</c><00:00:31.359><c> data</c><00:00:31.640><c> fix</c>

00:00:32.069 --> 00:00:32.079 align:start position:0%
vulnerable code and copilot data fix
 

00:00:32.079 --> 00:00:34.110 align:start position:0%
vulnerable code and copilot data fix
immediately<00:00:32.719><c> explains</c><00:00:33.120><c> the</c><00:00:33.320><c> vulnerability</c>

00:00:34.110 --> 00:00:34.120 align:start position:0%
immediately explains the vulnerability
 

00:00:34.120 --> 00:00:37.590 align:start position:0%
immediately explains the vulnerability
detected<00:00:35.000><c> and</c><00:00:35.280><c> wow</c><00:00:35.960><c> here's</c><00:00:36.480><c> suggested</c><00:00:37.120><c> code</c>

00:00:37.590 --> 00:00:37.600 align:start position:0%
detected and wow here's suggested code
 

00:00:37.600 --> 00:00:40.190 align:start position:0%
detected and wow here's suggested code
that<00:00:37.760><c> I</c><00:00:37.960><c> can</c><00:00:38.239><c> push</c><00:00:38.559><c> straight</c><00:00:38.960><c> away</c><00:00:39.559><c> or</c><00:00:39.840><c> edit</c>

00:00:40.190 --> 00:00:40.200 align:start position:0%
that I can push straight away or edit
 

00:00:40.200 --> 00:00:42.510 align:start position:0%
that I can push straight away or edit
first<00:00:40.600><c> if</c><00:00:40.760><c> I</c><00:00:40.879><c> want</c><00:00:41.120><c> to</c><00:00:41.719><c> directly</c><00:00:42.120><c> from</c><00:00:42.320><c> my</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
first if I want to directly from my
 

00:00:42.520 --> 00:00:45.029 align:start position:0%
first if I want to directly from my
browser<00:00:43.200><c> the</c><00:00:43.399><c> suggested</c><00:00:43.960><c> code</c><00:00:44.440><c> removes</c><00:00:44.840><c> the</c>

00:00:45.029 --> 00:00:45.039 align:start position:0%
browser the suggested code removes the
 

00:00:45.039 --> 00:00:47.029 align:start position:0%
browser the suggested code removes the
security<00:00:45.440><c> issue</c><00:00:45.719><c> automagically</c><00:00:46.640><c> which</c><00:00:46.800><c> is</c>

00:00:47.029 --> 00:00:47.039 align:start position:0%
security issue automagically which is
 

00:00:47.039 --> 00:00:49.670 align:start position:0%
security issue automagically which is
bad<00:00:47.280><c> news</c><00:00:47.600><c> for</c><00:00:47.920><c> open</c><00:00:48.280><c> security</c><00:00:48.760><c> alerts</c><00:00:49.480><c> with</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
bad news for open security alerts with
 

00:00:49.680 --> 00:00:52.310 align:start position:0%
bad news for open security alerts with
co-pilot<00:00:50.280><c> autofix</c><00:00:50.960><c> found</c><00:00:51.280><c> means</c><00:00:51.680><c> fixed</c><00:00:52.160><c> you</c>

00:00:52.310 --> 00:00:52.320 align:start position:0%
co-pilot autofix found means fixed you
 

00:00:52.320 --> 00:00:54.630 align:start position:0%
co-pilot autofix found means fixed you
can<00:00:52.559><c> fix</c><00:00:52.840><c> alerts</c><00:00:53.359><c> as</c><00:00:53.559><c> fast</c><00:00:53.800><c> as</c><00:00:53.960><c> you</c><00:00:54.079><c> find</c><00:00:54.320><c> them</c>

00:00:54.630 --> 00:00:54.640 align:start position:0%
can fix alerts as fast as you find them
 

00:00:54.640 --> 00:01:00.590 align:start position:0%
can fix alerts as fast as you find them
and<00:00:54.840><c> become</c><00:00:55.280><c> three</c><00:00:55.559><c> times</c><00:00:55.879><c> faster</c><00:00:56.520><c> try</c><00:00:56.760><c> it</c><00:00:56.920><c> out</c>

00:01:00.590 --> 00:01:00.600 align:start position:0%
 
 

00:01:00.600 --> 00:01:03.600 align:start position:0%
 
oh

