WEBVTT
Kind: captions
Language: en

00:00:04.000 --> 00:00:04.870 align:start position:0%
 
hi<00:00:04.240><c> everybody</c>

00:00:04.870 --> 00:00:04.880 align:start position:0%
hi everybody
 

00:00:04.880 --> 00:00:06.550 align:start position:0%
hi everybody
welcome<00:00:05.200><c> to</c><00:00:05.440><c> our</c><00:00:05.520><c> presentation</c><00:00:06.240><c> from</c><00:00:06.399><c> the</c>

00:00:06.550 --> 00:00:06.560 align:start position:0%
welcome to our presentation from the
 

00:00:06.560 --> 00:00:09.509 align:start position:0%
welcome to our presentation from the
2021<00:00:07.359><c> nfl</c><00:00:07.839><c> big</c><00:00:08.000><c> data</c><00:00:08.320><c> bowl</c>

00:00:09.509 --> 00:00:09.519 align:start position:0%
2021 nfl big data bowl
 

00:00:09.519 --> 00:00:11.830 align:start position:0%
2021 nfl big data bowl
i'm<00:00:09.840><c> zach</c><00:00:10.080><c> bradleau</c><00:00:10.639><c> i'm</c><00:00:10.800><c> a</c><00:00:10.880><c> junior</c><00:00:11.280><c> in</c><00:00:11.440><c> warden</c>

00:00:11.830 --> 00:00:11.840 align:start position:0%
i'm zach bradleau i'm a junior in warden
 

00:00:11.840 --> 00:00:13.749 align:start position:0%
i'm zach bradleau i'm a junior in warden
studying<00:00:12.240><c> finance-based</c><00:00:13.040><c> analytics</c><00:00:13.519><c> with</c>

00:00:13.749 --> 00:00:13.759 align:start position:0%
studying finance-based analytics with
 

00:00:13.759 --> 00:00:16.790 align:start position:0%
studying finance-based analytics with
minors<00:00:14.160><c> in</c><00:00:14.240><c> data</c><00:00:14.559><c> science</c><00:00:14.880><c> computer</c><00:00:15.280><c> science</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
minors in data science computer science
 

00:00:16.800 --> 00:00:18.790 align:start position:0%
minors in data science computer science
i'm<00:00:17.119><c> zach</c><00:00:17.359><c> drapkin</c><00:00:17.840><c> i'm</c><00:00:18.000><c> a</c><00:00:18.080><c> junior</c><00:00:18.400><c> in</c><00:00:18.480><c> wharton</c>

00:00:18.790 --> 00:00:18.800 align:start position:0%
i'm zach drapkin i'm a junior in wharton
 

00:00:18.800 --> 00:00:20.150 align:start position:0%
i'm zach drapkin i'm a junior in wharton
studying<00:00:19.119><c> statistics</c><00:00:19.600><c> and</c><00:00:19.680><c> decision</c>

00:00:20.150 --> 00:00:20.160 align:start position:0%
studying statistics and decision
 

00:00:20.160 --> 00:00:24.230 align:start position:0%
studying statistics and decision
processes

00:00:24.230 --> 00:00:24.240 align:start position:0%
 
 

00:00:24.240 --> 00:00:25.990 align:start position:0%
 
i'm<00:00:24.480><c> ryan</c><00:00:24.800><c> gross</c><00:00:25.119><c> and</c><00:00:25.279><c> i'm</c><00:00:25.439><c> a</c><00:00:25.519><c> second</c><00:00:25.760><c> year</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
i'm ryan gross and i'm a second year
 

00:00:26.000 --> 00:00:31.349 align:start position:0%
i'm ryan gross and i'm a second year
statistics<00:00:26.560><c> graduate</c><00:00:27.039><c> student</c><00:00:27.359><c> at</c><00:00:28.840><c> wharton</c>

00:00:31.349 --> 00:00:31.359 align:start position:0%
statistics graduate student at wharton
 

00:00:31.359 --> 00:00:33.190 align:start position:0%
statistics graduate student at wharton
i'm<00:00:31.679><c> sarah</c><00:00:32.000><c> who</c><00:00:32.239><c> and</c><00:00:32.399><c> i'm</c><00:00:32.559><c> a</c><00:00:32.640><c> sophomore</c><00:00:33.040><c> in</c>

00:00:33.190 --> 00:00:33.200 align:start position:0%
i'm sarah who and i'm a sophomore in
 

00:00:33.200 --> 00:00:35.990 align:start position:0%
i'm sarah who and i'm a sophomore in
wharton<00:00:33.600><c> studying</c><00:00:33.920><c> statistics</c><00:00:34.480><c> and</c><00:00:34.640><c> finance</c>

00:00:35.990 --> 00:00:36.000 align:start position:0%
wharton studying statistics and finance
 

00:00:36.000 --> 00:00:38.069 align:start position:0%
wharton studying statistics and finance
our<00:00:36.239><c> task</c><00:00:36.559><c> for</c><00:00:36.719><c> the</c><00:00:37.040><c> big</c><00:00:37.280><c> data</c><00:00:37.520><c> bill</c><00:00:37.760><c> was</c><00:00:37.920><c> to</c>

00:00:38.069 --> 00:00:38.079 align:start position:0%
our task for the big data bill was to
 

00:00:38.079 --> 00:00:40.310 align:start position:0%
our task for the big data bill was to
quantify<00:00:38.719><c> different</c><00:00:39.040><c> aspects</c><00:00:39.440><c> of</c><00:00:39.600><c> nfl</c><00:00:40.079><c> pass</c>

00:00:40.310 --> 00:00:40.320 align:start position:0%
quantify different aspects of nfl pass
 

00:00:40.320 --> 00:00:40.950 align:start position:0%
quantify different aspects of nfl pass
coverage

00:00:40.950 --> 00:00:40.960 align:start position:0%
coverage
 

00:00:40.960 --> 00:00:42.950 align:start position:0%
coverage
using<00:00:41.280><c> detailed</c><00:00:41.760><c> tracking</c><00:00:42.079><c> data</c><00:00:42.399><c> provided</c><00:00:42.800><c> by</c>

00:00:42.950 --> 00:00:42.960 align:start position:0%
using detailed tracking data provided by
 

00:00:42.960 --> 00:00:43.990 align:start position:0%
using detailed tracking data provided by
the<00:00:43.040><c> week</c><00:00:43.760><c> our</c>

00:00:43.990 --> 00:00:44.000 align:start position:0%
the week our
 

00:00:44.000 --> 00:00:45.910 align:start position:0%
the week our
submission<00:00:44.480><c> is</c><00:00:44.559><c> titled</c><00:00:44.960><c> check</c><00:00:45.200><c> the</c><00:00:45.360><c> tape</c><00:00:45.680><c> he's</c>

00:00:45.910 --> 00:00:45.920 align:start position:0%
submission is titled check the tape he's
 

00:00:45.920 --> 00:00:48.310 align:start position:0%
submission is titled check the tape he's
wide<00:00:46.239><c> open</c><00:00:46.640><c> a</c><00:00:46.800><c> target</c><00:00:47.120><c> agnostic</c><00:00:47.600><c> evaluation</c>

00:00:48.310 --> 00:00:48.320 align:start position:0%
wide open a target agnostic evaluation
 

00:00:48.320 --> 00:00:49.910 align:start position:0%
wide open a target agnostic evaluation
method<00:00:48.640><c> for</c><00:00:48.879><c> cornerbacks</c><00:00:49.520><c> and</c><00:00:49.600><c> wide</c>

00:00:49.910 --> 00:00:49.920 align:start position:0%
method for cornerbacks and wide
 

00:00:49.920 --> 00:00:53.029 align:start position:0%
method for cornerbacks and wide
receivers

00:00:53.029 --> 00:00:53.039 align:start position:0%
 
 

00:00:53.039 --> 00:00:55.350 align:start position:0%
 
we<00:00:53.199><c> had</c><00:00:53.440><c> three</c><00:00:53.680><c> goals</c><00:00:54.000><c> for</c><00:00:54.160><c> our</c><00:00:54.320><c> project</c><00:00:55.120><c> first</c>

00:00:55.350 --> 00:00:55.360 align:start position:0%
we had three goals for our project first
 

00:00:55.360 --> 00:00:57.110 align:start position:0%
we had three goals for our project first
we<00:00:55.520><c> wanted</c><00:00:55.760><c> to</c><00:00:55.920><c> quantify</c><00:00:56.399><c> cornerback</c><00:00:56.960><c> and</c>

00:00:57.110 --> 00:00:57.120 align:start position:0%
we wanted to quantify cornerback and
 

00:00:57.120 --> 00:00:57.510 align:start position:0%
we wanted to quantify cornerback and
wide

00:00:57.510 --> 00:00:57.520 align:start position:0%
wide
 

00:00:57.520 --> 00:00:59.990 align:start position:0%
wide
receiver<00:00:58.000><c> performance</c><00:00:58.559><c> on</c><00:00:58.719><c> all</c><00:00:58.960><c> routes</c><00:00:59.680><c> this</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
receiver performance on all routes this
 

00:01:00.000 --> 00:01:01.189 align:start position:0%
receiver performance on all routes this
includes<00:01:00.320><c> targeted</c><00:01:00.879><c> and</c>

00:01:01.189 --> 00:01:01.199 align:start position:0%
includes targeted and
 

00:01:01.199 --> 00:01:03.670 align:start position:0%
includes targeted and
non-targeted<00:01:01.920><c> routes</c><00:01:02.719><c> second</c><00:01:03.120><c> we</c><00:01:03.280><c> wanted</c><00:01:03.520><c> to</c>

00:01:03.670 --> 00:01:03.680 align:start position:0%
non-targeted routes second we wanted to
 

00:01:03.680 --> 00:01:05.830 align:start position:0%
non-targeted routes second we wanted to
develop<00:01:04.159><c> a</c><00:01:04.239><c> system</c><00:01:04.640><c> for</c><00:01:04.799><c> finding</c><00:01:05.199><c> undervalued</c>

00:01:05.830 --> 00:01:05.840 align:start position:0%
develop a system for finding undervalued
 

00:01:05.840 --> 00:01:07.590 align:start position:0%
develop a system for finding undervalued
players<00:01:06.159><c> at</c><00:01:06.240><c> these</c><00:01:06.479><c> positions</c>

00:01:07.590 --> 00:01:07.600 align:start position:0%
players at these positions
 

00:01:07.600 --> 00:01:09.830 align:start position:0%
players at these positions
lastly<00:01:08.000><c> we</c><00:01:08.159><c> wanted</c><00:01:08.479><c> to</c><00:01:08.640><c> investigate</c><00:01:09.200><c> how</c><00:01:09.360><c> nfl</c>

00:01:09.830 --> 00:01:09.840 align:start position:0%
lastly we wanted to investigate how nfl
 

00:01:09.840 --> 00:01:11.190 align:start position:0%
lastly we wanted to investigate how nfl
teams<00:01:10.080><c> could</c><00:01:10.320><c> optimize</c><00:01:10.720><c> man-to-man</c>

00:01:11.190 --> 00:01:11.200 align:start position:0%
teams could optimize man-to-man
 

00:01:11.200 --> 00:01:13.030 align:start position:0%
teams could optimize man-to-man
match-ups<00:01:11.760><c> across</c><00:01:12.080><c> a</c><00:01:12.159><c> given</c><00:01:12.479><c> game</c>

00:01:13.030 --> 00:01:13.040 align:start position:0%
match-ups across a given game
 

00:01:13.040 --> 00:01:14.789 align:start position:0%
match-ups across a given game
by<00:01:13.200><c> maximizing</c><00:01:13.840><c> cornerback</c><00:01:14.400><c> and</c><00:01:14.479><c> wide</c>

00:01:14.789 --> 00:01:14.799 align:start position:0%
by maximizing cornerback and wide
 

00:01:14.799 --> 00:01:17.910 align:start position:0%
by maximizing cornerback and wide
receiver<00:01:15.119><c> win</c><00:01:15.360><c> probabilities</c>

00:01:17.910 --> 00:01:17.920 align:start position:0%
receiver win probabilities
 

00:01:17.920 --> 00:01:19.749 align:start position:0%
receiver win probabilities
we<00:01:18.080><c> took</c><00:01:18.240><c> a</c><00:01:18.320><c> three-step</c><00:01:18.880><c> approach</c><00:01:19.280><c> to</c><00:01:19.439><c> achieve</c>

00:01:19.749 --> 00:01:19.759 align:start position:0%
we took a three-step approach to achieve
 

00:01:19.759 --> 00:01:21.109 align:start position:0%
we took a three-step approach to achieve
these<00:01:19.920><c> goals</c><00:01:20.720><c> first</c>

00:01:21.109 --> 00:01:21.119 align:start position:0%
these goals first
 

00:01:21.119 --> 00:01:23.429 align:start position:0%
these goals first
we<00:01:21.280><c> classified</c><00:01:21.840><c> coverages</c><00:01:22.400><c> as</c><00:01:22.560><c> man-to-man</c><00:01:23.280><c> or</c>

00:01:23.429 --> 00:01:23.439 align:start position:0%
we classified coverages as man-to-man or
 

00:01:23.439 --> 00:01:25.190 align:start position:0%
we classified coverages as man-to-man or
zone<00:01:23.680><c> for</c><00:01:23.840><c> every</c><00:01:24.080><c> receiver</c><00:01:24.640><c> cornerback</c>

00:01:25.190 --> 00:01:25.200 align:start position:0%
zone for every receiver cornerback
 

00:01:25.200 --> 00:01:26.149 align:start position:0%
zone for every receiver cornerback
matchup

00:01:26.149 --> 00:01:26.159 align:start position:0%
matchup
 

00:01:26.159 --> 00:01:28.230 align:start position:0%
matchup
then<00:01:26.640><c> for</c><00:01:26.880><c> all</c><00:01:27.040><c> man-to-man</c><00:01:27.600><c> routes</c><00:01:28.080><c> we</c>

00:01:28.230 --> 00:01:28.240 align:start position:0%
then for all man-to-man routes we
 

00:01:28.240 --> 00:01:30.069 align:start position:0%
then for all man-to-man routes we
modeled<00:01:28.560><c> the</c><00:01:28.640><c> probability</c><00:01:29.360><c> that</c><00:01:29.520><c> a</c><00:01:29.600><c> receiver</c>

00:01:30.069 --> 00:01:30.079 align:start position:0%
modeled the probability that a receiver
 

00:01:30.079 --> 00:01:31.670 align:start position:0%
modeled the probability that a receiver
would<00:01:30.240><c> beat</c><00:01:30.479><c> his</c><00:01:30.640><c> defender</c><00:01:31.200><c> and</c><00:01:31.360><c> make</c><00:01:31.600><c> a</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
would beat his defender and make a
 

00:01:31.680 --> 00:01:32.789 align:start position:0%
would beat his defender and make a
successful<00:01:32.320><c> play</c>

00:01:32.789 --> 00:01:32.799 align:start position:0%
successful play
 

00:01:32.799 --> 00:01:34.630 align:start position:0%
successful play
which<00:01:33.040><c> we</c><00:01:33.200><c> define</c><00:01:33.600><c> as</c><00:01:33.840><c> either</c><00:01:34.079><c> a</c><00:01:34.159><c> catch</c><00:01:34.400><c> for</c>

00:01:34.630 --> 00:01:34.640 align:start position:0%
which we define as either a catch for
 

00:01:34.640 --> 00:01:36.789 align:start position:0%
which we define as either a catch for
positive<00:01:35.360><c> expected</c><00:01:35.840><c> points</c><00:01:36.240><c> added</c>

00:01:36.789 --> 00:01:36.799 align:start position:0%
positive expected points added
 

00:01:36.799 --> 00:01:39.190 align:start position:0%
positive expected points added
or<00:01:37.119><c> a</c><00:01:37.200><c> drawn</c><00:01:37.520><c> pass</c><00:01:37.759><c> interference</c><00:01:38.320><c> penalty</c>

00:01:39.190 --> 00:01:39.200 align:start position:0%
or a drawn pass interference penalty
 

00:01:39.200 --> 00:01:41.190 align:start position:0%
or a drawn pass interference penalty
lastly<00:01:39.680><c> we</c><00:01:39.840><c> took</c><00:01:40.079><c> the</c><00:01:40.240><c> success</c><00:01:40.720><c> metric</c><00:01:41.119><c> and</c>

00:01:41.190 --> 00:01:41.200 align:start position:0%
lastly we took the success metric and
 

00:01:41.200 --> 00:01:42.950 align:start position:0%
lastly we took the success metric and
put<00:01:41.360><c> it</c><00:01:41.520><c> into</c><00:01:41.680><c> a</c><00:01:41.759><c> head-to-head</c><00:01:42.320><c> comparison</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
put it into a head-to-head comparison
 

00:01:42.960 --> 00:01:43.429 align:start position:0%
put it into a head-to-head comparison
model

00:01:43.429 --> 00:01:43.439 align:start position:0%
model
 

00:01:43.439 --> 00:01:45.109 align:start position:0%
model
to<00:01:43.600><c> develop</c><00:01:44.000><c> player</c><00:01:44.320><c> ratings</c><00:01:44.720><c> for</c><00:01:44.880><c> all</c>

00:01:45.109 --> 00:01:45.119 align:start position:0%
to develop player ratings for all
 

00:01:45.119 --> 00:01:48.230 align:start position:0%
to develop player ratings for all
receivers<00:01:45.759><c> and</c><00:01:45.920><c> cornerback</c>

00:01:48.230 --> 00:01:48.240 align:start position:0%
receivers and cornerback
 

00:01:48.240 --> 00:01:49.749 align:start position:0%
receivers and cornerback
now<00:01:48.720><c> starting</c><00:01:49.119><c> with</c><00:01:49.280><c> the</c><00:01:49.360><c> coverage</c>

00:01:49.749 --> 00:01:49.759 align:start position:0%
now starting with the coverage
 

00:01:49.759 --> 00:01:52.469 align:start position:0%
now starting with the coverage
classification<00:01:50.640><c> we</c><00:01:50.799><c> use</c><00:01:51.040><c> an</c><00:01:51.200><c> xg</c><00:01:51.600><c> boost</c><00:01:52.000><c> model</c>

00:01:52.469 --> 00:01:52.479 align:start position:0%
classification we use an xg boost model
 

00:01:52.479 --> 00:01:54.230 align:start position:0%
classification we use an xg boost model
which<00:01:52.720><c> is</c><00:01:52.880><c> a</c><00:01:52.960><c> gradient</c><00:01:53.360><c> boosting</c><00:01:53.759><c> approach</c>

00:01:54.230 --> 00:01:54.240 align:start position:0%
which is a gradient boosting approach
 

00:01:54.240 --> 00:01:55.830 align:start position:0%
which is a gradient boosting approach
that<00:01:54.399><c> works</c><00:01:54.720><c> well</c><00:01:54.880><c> with</c><00:01:55.040><c> the</c><00:01:55.200><c> complex</c>

00:01:55.830 --> 00:01:55.840 align:start position:0%
that works well with the complex
 

00:01:55.840 --> 00:01:57.749 align:start position:0%
that works well with the complex
high-dimensional<00:01:56.560><c> player</c><00:01:56.880><c> tracking</c><00:01:57.439><c> data</c>

00:01:57.749 --> 00:01:57.759 align:start position:0%
high-dimensional player tracking data
 

00:01:57.759 --> 00:01:59.910 align:start position:0%
high-dimensional player tracking data
provided<00:01:58.159><c> by</c><00:01:58.560><c> next-gen</c><00:01:59.040><c> stats</c>

00:01:59.910 --> 00:01:59.920 align:start position:0%
provided by next-gen stats
 

00:01:59.920 --> 00:02:02.870 align:start position:0%
provided by next-gen stats
our<00:02:00.240><c> model</c><00:02:00.560><c> achieves</c><00:02:00.880><c> 77</c><00:02:01.920><c> accuracy</c><00:02:02.640><c> for</c>

00:02:02.870 --> 00:02:02.880 align:start position:0%
our model achieves 77 accuracy for
 

00:02:02.880 --> 00:02:04.469 align:start position:0%
our model achieves 77 accuracy for
predicting<00:02:03.280><c> the</c><00:02:03.439><c> type</c><00:02:03.680><c> of</c><00:02:03.759><c> coverage</c><00:02:04.159><c> on</c><00:02:04.320><c> each</c>

00:02:04.469 --> 00:02:04.479 align:start position:0%
predicting the type of coverage on each
 

00:02:04.479 --> 00:02:05.190 align:start position:0%
predicting the type of coverage on each
route

00:02:05.190 --> 00:02:05.200 align:start position:0%
route
 

00:02:05.200 --> 00:02:07.429 align:start position:0%
route
not<00:02:05.360><c> surprisingly</c><00:02:06.320><c> variables</c><00:02:06.880><c> such</c><00:02:07.119><c> as</c><00:02:07.280><c> the</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
not surprisingly variables such as the
 

00:02:07.439 --> 00:02:09.029 align:start position:0%
not surprisingly variables such as the
separation<00:02:08.000><c> between</c><00:02:08.319><c> a</c><00:02:08.399><c> corner</c><00:02:08.720><c> and</c><00:02:08.879><c> his</c>

00:02:09.029 --> 00:02:09.039 align:start position:0%
separation between a corner and his
 

00:02:09.039 --> 00:02:09.669 align:start position:0%
separation between a corner and his
receiver

00:02:09.669 --> 00:02:09.679 align:start position:0%
receiver
 

00:02:09.679 --> 00:02:11.589 align:start position:0%
receiver
and<00:02:10.000><c> the</c><00:02:10.080><c> speed</c><00:02:10.479><c> and</c><00:02:10.640><c> orientation</c><00:02:11.360><c> of</c><00:02:11.440><c> the</c>

00:02:11.589 --> 00:02:11.599 align:start position:0%
and the speed and orientation of the
 

00:02:11.599 --> 00:02:13.350 align:start position:0%
and the speed and orientation of the
corner<00:02:12.000><c> relative</c><00:02:12.400><c> to</c><00:02:12.560><c> the</c><00:02:12.720><c> receiver</c>

00:02:13.350 --> 00:02:13.360 align:start position:0%
corner relative to the receiver
 

00:02:13.360 --> 00:02:14.949 align:start position:0%
corner relative to the receiver
and<00:02:13.440><c> the</c><00:02:13.599><c> quarterback</c><00:02:14.319><c> were</c><00:02:14.560><c> the</c><00:02:14.640><c> most</c>

00:02:14.949 --> 00:02:14.959 align:start position:0%
and the quarterback were the most
 

00:02:14.959 --> 00:02:18.150 align:start position:0%
and the quarterback were the most
important<00:02:15.280><c> predictors</c><00:02:15.920><c> of</c><00:02:16.000><c> this</c><00:02:16.239><c> model</c>

00:02:18.150 --> 00:02:18.160 align:start position:0%
important predictors of this model
 

00:02:18.160 --> 00:02:20.790 align:start position:0%
important predictors of this model
so<00:02:18.400><c> using</c><00:02:19.120><c> these</c><00:02:19.440><c> coverage</c><00:02:19.840><c> classifications</c>

00:02:20.790 --> 00:02:20.800 align:start position:0%
so using these coverage classifications
 

00:02:20.800 --> 00:02:23.270 align:start position:0%
so using these coverage classifications
we<00:02:21.040><c> isolated</c><00:02:21.599><c> man-to-man</c><00:02:22.160><c> routes</c><00:02:22.560><c> to</c><00:02:22.640><c> compare</c>

00:02:23.270 --> 00:02:23.280 align:start position:0%
we isolated man-to-man routes to compare
 

00:02:23.280 --> 00:02:25.430 align:start position:0%
we isolated man-to-man routes to compare
receivers<00:02:23.920><c> and</c><00:02:24.080><c> corners</c><00:02:24.480><c> head</c><00:02:24.640><c> to</c><00:02:24.800><c> head</c><00:02:25.280><c> we</c>

00:02:25.430 --> 00:02:25.440 align:start position:0%
receivers and corners head to head we
 

00:02:25.440 --> 00:02:27.110 align:start position:0%
receivers and corners head to head we
were<00:02:25.599><c> able</c><00:02:25.760><c> to</c><00:02:25.920><c> predict</c><00:02:26.239><c> with</c><00:02:26.400><c> 70</c>

00:02:27.110 --> 00:02:27.120 align:start position:0%
were able to predict with 70
 

00:02:27.120 --> 00:02:29.110 align:start position:0%
were able to predict with 70
accuracy<00:02:27.840><c> whether</c><00:02:28.160><c> a</c><00:02:28.239><c> receiver</c><00:02:28.720><c> would</c><00:02:28.879><c> beat</c>

00:02:29.110 --> 00:02:29.120 align:start position:0%
accuracy whether a receiver would beat
 

00:02:29.120 --> 00:02:30.869 align:start position:0%
accuracy whether a receiver would beat
his<00:02:29.280><c> defender</c><00:02:29.680><c> on</c><00:02:29.760><c> a</c><00:02:29.840><c> given</c><00:02:30.160><c> play</c>

00:02:30.869 --> 00:02:30.879 align:start position:0%
his defender on a given play
 

00:02:30.879 --> 00:02:32.710 align:start position:0%
his defender on a given play
the<00:02:31.040><c> features</c><00:02:31.519><c> which</c><00:02:31.680><c> were</c><00:02:31.840><c> most</c><00:02:32.160><c> impactful</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
the features which were most impactful
 

00:02:32.720 --> 00:02:34.309 align:start position:0%
the features which were most impactful
in<00:02:32.800><c> this</c><00:02:32.959><c> xg</c><00:02:33.280><c> boost</c><00:02:33.840><c> model</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
in this xg boost model
 

00:02:34.319 --> 00:02:35.910 align:start position:0%
in this xg boost model
were<00:02:34.480><c> the</c><00:02:34.640><c> separation</c><00:02:35.120><c> between</c><00:02:35.440><c> the</c><00:02:35.519><c> corner</c>

00:02:35.910 --> 00:02:35.920 align:start position:0%
were the separation between the corner
 

00:02:35.920 --> 00:02:38.070 align:start position:0%
were the separation between the corner
and<00:02:36.000><c> the</c><00:02:36.080><c> receiver</c><00:02:36.879><c> the</c><00:02:37.040><c> depth</c><00:02:37.360><c> of</c><00:02:37.440><c> the</c><00:02:37.599><c> route</c>

00:02:38.070 --> 00:02:38.080 align:start position:0%
and the receiver the depth of the route
 

00:02:38.080 --> 00:02:39.589 align:start position:0%
and the receiver the depth of the route
and<00:02:38.319><c> the</c><00:02:38.400><c> difference</c><00:02:38.800><c> in</c><00:02:38.959><c> the</c><00:02:39.040><c> two</c><00:02:39.280><c> players</c>

00:02:39.589 --> 00:02:39.599 align:start position:0%
and the difference in the two players
 

00:02:39.599 --> 00:02:41.670 align:start position:0%
and the difference in the two players
orientation<00:02:40.560><c> we</c><00:02:40.720><c> believe</c><00:02:41.040><c> our</c><00:02:41.120><c> accuracy</c>

00:02:41.670 --> 00:02:41.680 align:start position:0%
orientation we believe our accuracy
 

00:02:41.680 --> 00:02:42.790 align:start position:0%
orientation we believe our accuracy
could<00:02:41.840><c> be</c><00:02:41.920><c> enhanced</c><00:02:42.319><c> further</c>

00:02:42.790 --> 00:02:42.800 align:start position:0%
could be enhanced further
 

00:02:42.800 --> 00:02:44.790 align:start position:0%
could be enhanced further
by<00:02:43.040><c> implementing</c><00:02:43.599><c> other</c><00:02:43.840><c> proven</c><00:02:44.319><c> effective</c>

00:02:44.790 --> 00:02:44.800 align:start position:0%
by implementing other proven effective
 

00:02:44.800 --> 00:02:46.309 align:start position:0%
by implementing other proven effective
approaches<00:02:45.360><c> such</c><00:02:45.599><c> as</c><00:02:45.760><c> bayesian</c>

00:02:46.309 --> 00:02:46.319 align:start position:0%
approaches such as bayesian
 

00:02:46.319 --> 00:02:50.390 align:start position:0%
approaches such as bayesian
additive<00:02:46.720><c> regression</c><00:02:48.840><c> trees</c><00:02:49.920><c> now</c><00:02:50.160><c> let's</c>

00:02:50.390 --> 00:02:50.400 align:start position:0%
additive regression trees now let's
 

00:02:50.400 --> 00:02:52.070 align:start position:0%
additive regression trees now let's
check<00:02:50.640><c> out</c><00:02:50.720><c> an</c><00:02:50.879><c> example</c><00:02:51.280><c> of</c><00:02:51.360><c> these</c><00:02:51.599><c> models</c><00:02:51.920><c> in</c>

00:02:52.070 --> 00:02:52.080 align:start position:0%
check out an example of these models in
 

00:02:52.080 --> 00:02:52.710 align:start position:0%
check out an example of these models in
action

00:02:52.710 --> 00:02:52.720 align:start position:0%
action
 

00:02:52.720 --> 00:02:54.390 align:start position:0%
action
during<00:02:53.040><c> a</c><00:02:53.120><c> week</c><00:02:53.360><c> three</c><00:02:53.599><c> match-up</c><00:02:54.000><c> between</c>

00:02:54.390 --> 00:02:54.400 align:start position:0%
during a week three match-up between
 

00:02:54.400 --> 00:02:55.830 align:start position:0%
during a week three match-up between
green<00:02:54.640><c> bay</c><00:02:54.879><c> and</c><00:02:55.040><c> washington</c>

00:02:55.830 --> 00:02:55.840 align:start position:0%
green bay and washington
 

00:02:55.840 --> 00:02:57.589 align:start position:0%
green bay and washington
aaron<00:02:56.239><c> rodgers</c><00:02:56.560><c> threw</c><00:02:56.800><c> an</c><00:02:56.879><c> incomplete</c><00:02:57.360><c> pass</c>

00:02:57.589 --> 00:02:57.599 align:start position:0%
aaron rodgers threw an incomplete pass
 

00:02:57.599 --> 00:02:59.509 align:start position:0%
aaron rodgers threw an incomplete pass
to<00:02:57.760><c> a</c><00:02:57.840><c> heavily</c><00:02:58.159><c> guarded</c><00:02:58.560><c> randall</c><00:02:58.879><c> cobb</c>

00:02:59.509 --> 00:02:59.519 align:start position:0%
to a heavily guarded randall cobb
 

00:02:59.519 --> 00:03:01.430 align:start position:0%
to a heavily guarded randall cobb
however<00:03:00.080><c> if</c><00:03:00.159><c> you</c><00:03:00.319><c> look</c><00:03:00.560><c> closer</c><00:03:00.879><c> at</c><00:03:00.959><c> the</c><00:03:01.040><c> play</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
however if you look closer at the play
 

00:03:01.440 --> 00:03:03.270 align:start position:0%
however if you look closer at the play
devonta<00:03:02.000><c> adams</c><00:03:02.319><c> had</c><00:03:02.480><c> beaten</c><00:03:02.800><c> cornerback</c>

00:03:03.270 --> 00:03:03.280 align:start position:0%
devonta adams had beaten cornerback
 

00:03:03.280 --> 00:03:05.589 align:start position:0%
devonta adams had beaten cornerback
quinton<00:03:03.680><c> dunbar</c><00:03:04.159><c> in</c><00:03:04.239><c> his</c><00:03:04.480><c> route</c><00:03:04.720><c> down</c><00:03:05.040><c> field</c>

00:03:05.589 --> 00:03:05.599 align:start position:0%
quinton dunbar in his route down field
 

00:03:05.599 --> 00:03:07.589 align:start position:0%
quinton dunbar in his route down field
and<00:03:05.760><c> our</c><00:03:06.000><c> model</c><00:03:06.319><c> gives</c><00:03:06.560><c> him</c><00:03:06.720><c> an</c><00:03:06.879><c> 80</c>

00:03:07.589 --> 00:03:07.599 align:start position:0%
and our model gives him an 80
 

00:03:07.599 --> 00:03:09.509 align:start position:0%
and our model gives him an 80
probability<00:03:08.080><c> of</c><00:03:08.159><c> success</c><00:03:08.640><c> on</c><00:03:08.800><c> that</c><00:03:09.040><c> route</c>

00:03:09.509 --> 00:03:09.519 align:start position:0%
probability of success on that route
 

00:03:09.519 --> 00:03:11.509 align:start position:0%
probability of success on that route
despite<00:03:09.920><c> the</c><00:03:10.239><c> ball</c><00:03:10.560><c> not</c><00:03:10.800><c> being</c><00:03:10.959><c> thrown</c><00:03:11.360><c> his</c>

00:03:11.509 --> 00:03:11.519 align:start position:0%
despite the ball not being thrown his
 

00:03:11.519 --> 00:03:12.949 align:start position:0%
despite the ball not being thrown his
way<00:03:12.080><c> in</c><00:03:12.319><c> this</c><00:03:12.480><c> sense</c>

00:03:12.949 --> 00:03:12.959 align:start position:0%
way in this sense
 

00:03:12.959 --> 00:03:16.470 align:start position:0%
way in this sense
our<00:03:13.120><c> approach</c><00:03:13.440><c> is</c><00:03:13.599><c> target</c><00:03:13.920><c> agnostic</c>

00:03:16.470 --> 00:03:16.480 align:start position:0%
our approach is target agnostic
 

00:03:16.480 --> 00:03:17.990 align:start position:0%
our approach is target agnostic
we<00:03:16.640><c> plugged</c><00:03:16.959><c> our</c><00:03:17.120><c> estimates</c><00:03:17.599><c> of</c><00:03:17.760><c> every</c>

00:03:17.990 --> 00:03:18.000 align:start position:0%
we plugged our estimates of every
 

00:03:18.000 --> 00:03:19.910 align:start position:0%
we plugged our estimates of every
head-to-head<00:03:18.560><c> matchup</c><00:03:18.959><c> from</c><00:03:19.120><c> the</c><00:03:19.280><c> 2018</c>

00:03:19.910 --> 00:03:19.920 align:start position:0%
head-to-head matchup from the 2018
 

00:03:19.920 --> 00:03:21.509 align:start position:0%
head-to-head matchup from the 2018
season<00:03:20.239><c> into</c><00:03:20.480><c> an</c><00:03:20.640><c> ewo</c><00:03:20.959><c> model</c>

00:03:21.509 --> 00:03:21.519 align:start position:0%
season into an ewo model
 

00:03:21.519 --> 00:03:23.509 align:start position:0%
season into an ewo model
to<00:03:21.680><c> assign</c><00:03:22.239><c> each</c><00:03:22.560><c> cornerback</c><00:03:23.120><c> and</c><00:03:23.280><c> wide</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
to assign each cornerback and wide
 

00:03:23.519 --> 00:03:25.589 align:start position:0%
to assign each cornerback and wide
receiver<00:03:24.000><c> a</c><00:03:24.080><c> strength</c><00:03:24.400><c> rating</c><00:03:24.799><c> that</c><00:03:24.959><c> we</c><00:03:25.200><c> call</c>

00:03:25.589 --> 00:03:25.599 align:start position:0%
receiver a strength rating that we call
 

00:03:25.599 --> 00:03:28.710 align:start position:0%
receiver a strength rating that we call
target<00:03:25.920><c> agnostic</c><00:03:26.480><c> player</c><00:03:26.799><c> elo</c><00:03:27.360><c> or</c><00:03:27.760><c> tape</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
target agnostic player elo or tape
 

00:03:28.720 --> 00:03:30.309 align:start position:0%
target agnostic player elo or tape
this<00:03:28.959><c> score</c><00:03:29.360><c> assesses</c><00:03:29.840><c> the</c><00:03:29.920><c> skill</c><00:03:30.159><c> of</c>

00:03:30.309 --> 00:03:30.319 align:start position:0%
this score assesses the skill of
 

00:03:30.319 --> 00:03:31.910 align:start position:0%
this score assesses the skill of
cornerbacks<00:03:31.040><c> and</c><00:03:31.200><c> wider</c><00:03:31.519><c> savers</c><00:03:31.840><c> in</c>

00:03:31.910 --> 00:03:31.920 align:start position:0%
cornerbacks and wider savers in
 

00:03:31.920 --> 00:03:33.110 align:start position:0%
cornerbacks and wider savers in
man-to-man<00:03:32.400><c> matchups</c>

00:03:33.110 --> 00:03:33.120 align:start position:0%
man-to-man matchups
 

00:03:33.120 --> 00:03:34.789 align:start position:0%
man-to-man matchups
and<00:03:33.280><c> can</c><00:03:33.440><c> be</c><00:03:33.599><c> utilized</c><00:03:34.000><c> to</c><00:03:34.159><c> calculate</c><00:03:34.640><c> the</c>

00:03:34.789 --> 00:03:34.799 align:start position:0%
and can be utilized to calculate the
 

00:03:34.799 --> 00:03:36.390 align:start position:0%
and can be utilized to calculate the
probability<00:03:35.280><c> of</c><00:03:35.440><c> route</c><00:03:35.680><c> to</c><00:03:35.760><c> success</c>

00:03:36.390 --> 00:03:36.400 align:start position:0%
probability of route to success
 

00:03:36.400 --> 00:03:38.390 align:start position:0%
probability of route to success
for<00:03:36.560><c> a</c><00:03:36.640><c> matchup</c><00:03:36.959><c> of</c><00:03:37.120><c> any</c><00:03:37.360><c> two</c><00:03:37.599><c> players</c><00:03:38.000><c> a</c><00:03:38.159><c> wide</c>

00:03:38.390 --> 00:03:38.400 align:start position:0%
for a matchup of any two players a wide
 

00:03:38.400 --> 00:03:40.949 align:start position:0%
for a matchup of any two players a wide
receiver<00:03:39.040><c> and</c><00:03:39.280><c> cornerback</c>

00:03:40.949 --> 00:03:40.959 align:start position:0%
receiver and cornerback
 

00:03:40.959 --> 00:03:42.710 align:start position:0%
receiver and cornerback
the<00:03:41.120><c> final</c><00:03:41.519><c> tape</c><00:03:41.760><c> ranking</c><00:03:42.080><c> of</c><00:03:42.159><c> cornerbacks</c>

00:03:42.710 --> 00:03:42.720 align:start position:0%
the final tape ranking of cornerbacks
 

00:03:42.720 --> 00:03:44.869 align:start position:0%
the final tape ranking of cornerbacks
from<00:03:42.879><c> our</c><00:03:42.959><c> model</c><00:03:43.519><c> shows</c><00:03:43.920><c> all</c><00:03:44.080><c> pro</c><00:03:44.319><c> cornerbacks</c>

00:03:44.869 --> 00:03:44.879 align:start position:0%
from our model shows all pro cornerbacks
 

00:03:44.879 --> 00:03:46.789 align:start position:0%
from our model shows all pro cornerbacks
stefan<00:03:45.280><c> gilmore</c><00:03:45.840><c> as</c><00:03:46.000><c> the</c><00:03:46.080><c> top</c><00:03:46.319><c> cornerback</c>

00:03:46.789 --> 00:03:46.799 align:start position:0%
stefan gilmore as the top cornerback
 

00:03:46.799 --> 00:03:48.309 align:start position:0%
stefan gilmore as the top cornerback
from<00:03:46.959><c> the</c><00:03:47.040><c> 2018</c><00:03:47.680><c> season</c>

00:03:48.309 --> 00:03:48.319 align:start position:0%
from the 2018 season
 

00:03:48.319 --> 00:03:50.149 align:start position:0%
from the 2018 season
and<00:03:48.560><c> features</c><00:03:49.040><c> three</c><00:03:49.360><c> pro</c><00:03:49.599><c> bowlers</c><00:03:50.000><c> in</c><00:03:50.080><c> the</c>

00:03:50.149 --> 00:03:50.159 align:start position:0%
and features three pro bowlers in the
 

00:03:50.159 --> 00:03:52.390 align:start position:0%
and features three pro bowlers in the
top<00:03:50.480><c> ten</c><00:03:50.959><c> which</c><00:03:51.200><c> is</c><00:03:51.280><c> some</c><00:03:51.599><c> validation</c>

00:03:52.390 --> 00:03:52.400 align:start position:0%
top ten which is some validation
 

00:03:52.400 --> 00:03:54.149 align:start position:0%
top ten which is some validation
but<00:03:52.640><c> also</c><00:03:52.959><c> suggest</c><00:03:53.360><c> there</c><00:03:53.519><c> could</c><00:03:53.680><c> be</c><00:03:53.840><c> many</c>

00:03:54.149 --> 00:03:54.159 align:start position:0%
but also suggest there could be many
 

00:03:54.159 --> 00:03:55.670 align:start position:0%
but also suggest there could be many
undervalued<00:03:54.720><c> players</c>

00:03:55.670 --> 00:03:55.680 align:start position:0%
undervalued players
 

00:03:55.680 --> 00:03:57.350 align:start position:0%
undervalued players
additionally<00:03:56.400><c> a</c><00:03:56.480><c> player's</c><00:03:56.879><c> tape</c><00:03:57.120><c> can</c><00:03:57.280><c> be</c>

00:03:57.350 --> 00:03:57.360 align:start position:0%
additionally a player's tape can be
 

00:03:57.360 --> 00:03:59.190 align:start position:0%
additionally a player's tape can be
observed<00:03:57.760><c> week</c><00:03:58.000><c> by</c><00:03:58.159><c> week</c><00:03:58.480><c> in</c><00:03:58.560><c> order</c><00:03:58.799><c> to</c><00:03:58.959><c> assess</c>

00:03:59.190 --> 00:03:59.200 align:start position:0%
observed week by week in order to assess
 

00:03:59.200 --> 00:04:00.869 align:start position:0%
observed week by week in order to assess
his<00:03:59.360><c> relative</c><00:03:59.840><c> performance</c><00:04:00.480><c> across</c><00:04:00.799><c> the</c>

00:04:00.869 --> 00:04:00.879 align:start position:0%
his relative performance across the
 

00:04:00.879 --> 00:04:04.470 align:start position:0%
his relative performance across the
different<00:04:01.120><c> games</c><00:04:01.439><c> he</c><00:04:01.599><c> plays</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
 
 

00:04:04.480 --> 00:04:06.309 align:start position:0%
 
these<00:04:04.799><c> ratings</c><00:04:05.120><c> can</c><00:04:05.280><c> be</c><00:04:05.439><c> applied</c><00:04:05.760><c> by</c><00:04:05.920><c> nfl</c>

00:04:06.309 --> 00:04:06.319 align:start position:0%
these ratings can be applied by nfl
 

00:04:06.319 --> 00:04:08.309 align:start position:0%
these ratings can be applied by nfl
teams<00:04:06.640><c> in</c><00:04:06.720><c> a</c><00:04:06.799><c> number</c><00:04:07.040><c> of</c><00:04:07.200><c> different</c><00:04:07.519><c> settings</c>

00:04:08.309 --> 00:04:08.319 align:start position:0%
teams in a number of different settings
 

00:04:08.319 --> 00:04:09.910 align:start position:0%
teams in a number of different settings
when<00:04:08.480><c> it</c><00:04:08.560><c> comes</c><00:04:08.799><c> to</c><00:04:08.879><c> finding</c><00:04:09.280><c> and</c><00:04:09.360><c> evaluating</c>

00:04:09.910 --> 00:04:09.920 align:start position:0%
when it comes to finding and evaluating
 

00:04:09.920 --> 00:04:11.509 align:start position:0%
when it comes to finding and evaluating
talent<00:04:10.239><c> at</c><00:04:10.319><c> the</c><00:04:10.400><c> cornerback</c><00:04:10.879><c> position</c>

00:04:11.509 --> 00:04:11.519 align:start position:0%
talent at the cornerback position
 

00:04:11.519 --> 00:04:13.110 align:start position:0%
talent at the cornerback position
our<00:04:11.840><c> tape</c><00:04:12.080><c> metric</c><00:04:12.400><c> correlates</c><00:04:12.799><c> well</c><00:04:12.959><c> with</c>

00:04:13.110 --> 00:04:13.120 align:start position:0%
our tape metric correlates well with
 

00:04:13.120 --> 00:04:14.949 align:start position:0%
our tape metric correlates well with
salary<00:04:13.599><c> and</c><00:04:13.680><c> therefore</c><00:04:14.080><c> can</c><00:04:14.239><c> be</c><00:04:14.400><c> used</c><00:04:14.640><c> to</c><00:04:14.720><c> find</c>

00:04:14.949 --> 00:04:14.959 align:start position:0%
salary and therefore can be used to find
 

00:04:14.959 --> 00:04:16.310 align:start position:0%
salary and therefore can be used to find
undervalued<00:04:15.519><c> players</c>

00:04:16.310 --> 00:04:16.320 align:start position:0%
undervalued players
 

00:04:16.320 --> 00:04:18.629 align:start position:0%
undervalued players
in<00:04:16.560><c> fact</c><00:04:17.040><c> our</c><00:04:17.280><c> model</c><00:04:17.519><c> of</c><00:04:17.600><c> the</c><00:04:17.680><c> 2018</c><00:04:18.320><c> season</c>

00:04:18.629 --> 00:04:18.639 align:start position:0%
in fact our model of the 2018 season
 

00:04:18.639 --> 00:04:20.469 align:start position:0%
in fact our model of the 2018 season
identified<00:04:19.199><c> each</c><00:04:19.359><c> of</c><00:04:19.440><c> the</c><00:04:19.519><c> top</c><00:04:19.759><c> pff</c><00:04:20.239><c> created</c>

00:04:20.469 --> 00:04:20.479 align:start position:0%
identified each of the top pff created
 

00:04:20.479 --> 00:04:22.150 align:start position:0%
identified each of the top pff created
cornerbacks<00:04:21.040><c> from</c><00:04:21.120><c> the</c><00:04:21.280><c> 2020</c><00:04:21.759><c> season</c><00:04:22.079><c> that</c>

00:04:22.150 --> 00:04:22.160 align:start position:0%
cornerbacks from the 2020 season that
 

00:04:22.160 --> 00:04:23.990 align:start position:0%
cornerbacks from the 2020 season that
were<00:04:22.320><c> active</c><00:04:22.639><c> in</c><00:04:22.720><c> 2018</c><00:04:23.280><c> as</c><00:04:23.440><c> undervalued</c>

00:04:23.990 --> 00:04:24.000 align:start position:0%
were active in 2018 as undervalued
 

00:04:24.000 --> 00:04:24.870 align:start position:0%
were active in 2018 as undervalued
players

00:04:24.870 --> 00:04:24.880 align:start position:0%
players
 

00:04:24.880 --> 00:04:26.230 align:start position:0%
players
clearly<00:04:25.280><c> this</c><00:04:25.440><c> shows</c><00:04:25.680><c> that</c><00:04:25.840><c> teams</c><00:04:26.080><c> could</c>

00:04:26.230 --> 00:04:26.240 align:start position:0%
clearly this shows that teams could
 

00:04:26.240 --> 00:04:27.510 align:start position:0%
clearly this shows that teams could
implement<00:04:26.639><c> the</c><00:04:26.720><c> tape</c><00:04:26.960><c> system</c><00:04:27.280><c> to</c><00:04:27.360><c> find</c>

00:04:27.510 --> 00:04:27.520 align:start position:0%
implement the tape system to find
 

00:04:27.520 --> 00:04:29.189 align:start position:0%
implement the tape system to find
bargains<00:04:27.919><c> at</c><00:04:28.000><c> cornerback</c><00:04:28.560><c> before</c><00:04:28.960><c> the</c><00:04:29.040><c> rest</c>

00:04:29.189 --> 00:04:29.199 align:start position:0%
bargains at cornerback before the rest
 

00:04:29.199 --> 00:04:31.430 align:start position:0%
bargains at cornerback before the rest
of<00:04:29.280><c> the</c><00:04:29.360><c> league</c><00:04:29.600><c> catches</c><00:04:30.000><c> on</c>

00:04:31.430 --> 00:04:31.440 align:start position:0%
of the league catches on
 

00:04:31.440 --> 00:04:33.350 align:start position:0%
of the league catches on
nfl<00:04:31.840><c> defenses</c><00:04:32.400><c> could</c><00:04:32.639><c> also</c><00:04:32.800><c> use</c><00:04:33.040><c> the</c><00:04:33.120><c> tape</c>

00:04:33.350 --> 00:04:33.360 align:start position:0%
nfl defenses could also use the tape
 

00:04:33.360 --> 00:04:34.950 align:start position:0%
nfl defenses could also use the tape
rating<00:04:33.600><c> system</c><00:04:33.919><c> to</c><00:04:34.080><c> optimize</c><00:04:34.479><c> their</c><00:04:34.639><c> weekly</c>

00:04:34.950 --> 00:04:34.960 align:start position:0%
rating system to optimize their weekly
 

00:04:34.960 --> 00:04:36.629 align:start position:0%
rating system to optimize their weekly
matchups<00:04:35.360><c> and</c><00:04:35.440><c> pass</c><00:04:35.759><c> coverage</c>

00:04:36.629 --> 00:04:36.639 align:start position:0%
matchups and pass coverage
 

00:04:36.639 --> 00:04:38.230 align:start position:0%
matchups and pass coverage
elo<00:04:36.960><c> ratings</c><00:04:37.280><c> allow</c><00:04:37.520><c> us</c><00:04:37.680><c> to</c><00:04:37.759><c> calculate</c><00:04:38.160><c> the</c>

00:04:38.230 --> 00:04:38.240 align:start position:0%
elo ratings allow us to calculate the
 

00:04:38.240 --> 00:04:40.310 align:start position:0%
elo ratings allow us to calculate the
predicted<00:04:38.960><c> the</c><00:04:39.040><c> predicted</c><00:04:39.440><c> success</c><00:04:39.919><c> of</c><00:04:40.080><c> every</c>

00:04:40.310 --> 00:04:40.320 align:start position:0%
predicted the predicted success of every
 

00:04:40.320 --> 00:04:41.670 align:start position:0%
predicted the predicted success of every
possible<00:04:40.720><c> cornerback</c><00:04:41.120><c> wide</c><00:04:41.360><c> receiver</c>

00:04:41.670 --> 00:04:41.680 align:start position:0%
possible cornerback wide receiver
 

00:04:41.680 --> 00:04:42.310 align:start position:0%
possible cornerback wide receiver
matchup

00:04:42.310 --> 00:04:42.320 align:start position:0%
matchup
 

00:04:42.320 --> 00:04:43.830 align:start position:0%
matchup
and<00:04:42.400><c> we</c><00:04:42.560><c> can</c><00:04:42.720><c> use</c><00:04:42.880><c> these</c><00:04:43.120><c> probabilities</c><00:04:43.759><c> to</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
and we can use these probabilities to
 

00:04:43.840 --> 00:04:45.590 align:start position:0%
and we can use these probabilities to
predict<00:04:44.080><c> a</c><00:04:44.160><c> defense's</c><00:04:44.639><c> overall</c><00:04:45.040><c> success</c><00:04:45.440><c> for</c>

00:04:45.590 --> 00:04:45.600 align:start position:0%
predict a defense's overall success for
 

00:04:45.600 --> 00:04:47.110 align:start position:0%
predict a defense's overall success for
each<00:04:45.759><c> combination</c><00:04:46.240><c> of</c><00:04:46.320><c> matchups</c><00:04:46.800><c> against</c><00:04:47.040><c> a</c>

00:04:47.110 --> 00:04:47.120 align:start position:0%
each combination of matchups against a
 

00:04:47.120 --> 00:04:48.230 align:start position:0%
each combination of matchups against a
given<00:04:47.360><c> opponent</c>

00:04:48.230 --> 00:04:48.240 align:start position:0%
given opponent
 

00:04:48.240 --> 00:04:49.590 align:start position:0%
given opponent
teams<00:04:48.560><c> can</c><00:04:48.720><c> pick</c><00:04:48.880><c> the</c><00:04:48.960><c> combination</c><00:04:49.520><c> of</c>

00:04:49.590 --> 00:04:49.600 align:start position:0%
teams can pick the combination of
 

00:04:49.600 --> 00:04:50.870 align:start position:0%
teams can pick the combination of
matchups<00:04:50.000><c> that</c><00:04:50.160><c> maximizes</c><00:04:50.720><c> their</c>

00:04:50.870 --> 00:04:50.880 align:start position:0%
matchups that maximizes their
 

00:04:50.880 --> 00:04:52.390 align:start position:0%
matchups that maximizes their
probability<00:04:51.440><c> of</c><00:04:51.520><c> winning</c><00:04:51.759><c> every</c><00:04:52.000><c> round</c><00:04:52.240><c> on</c><00:04:52.320><c> a</c>

00:04:52.390 --> 00:04:52.400 align:start position:0%
probability of winning every round on a
 

00:04:52.400 --> 00:04:52.870 align:start position:0%
probability of winning every round on a
play

00:04:52.870 --> 00:04:52.880 align:start position:0%
play
 

00:04:52.880 --> 00:04:54.390 align:start position:0%
play
to<00:04:53.040><c> optimize</c><00:04:53.440><c> their</c><00:04:53.600><c> success</c><00:04:53.919><c> defending</c>

00:04:54.390 --> 00:04:54.400 align:start position:0%
to optimize their success defending
 

00:04:54.400 --> 00:04:55.990 align:start position:0%
to optimize their success defending
passing<00:04:54.720><c> plays</c><00:04:55.440><c> so</c><00:04:55.759><c> for</c>

00:04:55.990 --> 00:04:56.000 align:start position:0%
passing plays so for
 

00:04:56.000 --> 00:04:57.749 align:start position:0%
passing plays so for
example<00:04:56.560><c> if</c><00:04:56.720><c> the</c><00:04:56.800><c> patriots</c><00:04:57.199><c> were</c><00:04:57.360><c> facing</c><00:04:57.600><c> the</c>

00:04:57.749 --> 00:04:57.759 align:start position:0%
example if the patriots were facing the
 

00:04:57.759 --> 00:04:59.749 align:start position:0%
example if the patriots were facing the
packers<00:04:58.400><c> matching</c><00:04:58.720><c> stephon</c><00:04:59.199><c> gilmore</c><00:04:59.600><c> up</c><00:04:59.680><c> with</c>

00:04:59.749 --> 00:04:59.759 align:start position:0%
packers matching stephon gilmore up with
 

00:04:59.759 --> 00:05:01.590 align:start position:0%
packers matching stephon gilmore up with
devonte<00:05:00.240><c> adams</c><00:05:00.560><c> and</c><00:05:00.639><c> putting</c><00:05:00.880><c> jonathan</c><00:05:01.280><c> jones</c>

00:05:01.590 --> 00:05:01.600 align:start position:0%
devonte adams and putting jonathan jones
 

00:05:01.600 --> 00:05:02.390 align:start position:0%
devonte adams and putting jonathan jones
at<00:05:01.680><c> randall</c><00:05:02.000><c> cobb</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
at randall cobb
 

00:05:02.400 --> 00:05:03.830 align:start position:0%
at randall cobb
would<00:05:02.639><c> increase</c><00:05:02.880><c> the</c><00:05:02.960><c> new</c><00:05:03.120><c> england</c><00:05:03.360><c> defense's</c>

00:05:03.830 --> 00:05:03.840 align:start position:0%
would increase the new england defense's
 

00:05:03.840 --> 00:05:05.749 align:start position:0%
would increase the new england defense's
chance<00:05:04.320><c> of</c><00:05:04.479><c> winning</c><00:05:04.880><c> all</c><00:05:05.039><c> routes</c><00:05:05.360><c> on</c><00:05:05.440><c> a</c><00:05:05.520><c> pass</c>

00:05:05.749 --> 00:05:05.759 align:start position:0%
chance of winning all routes on a pass
 

00:05:05.759 --> 00:05:07.270 align:start position:0%
chance of winning all routes on a pass
play<00:05:05.919><c> by</c><00:05:06.080><c> more</c><00:05:06.240><c> than</c><00:05:06.400><c> two</c><00:05:06.560><c> percentage</c><00:05:06.960><c> points</c>

00:05:07.270 --> 00:05:07.280 align:start position:0%
play by more than two percentage points
 

00:05:07.280 --> 00:05:08.790 align:start position:0%
play by more than two percentage points
over<00:05:07.440><c> putting</c><00:05:07.680><c> gilmore</c><00:05:08.080><c> on</c><00:05:08.160><c> cobb</c><00:05:08.400><c> and</c><00:05:08.479><c> putting</c>

00:05:08.790 --> 00:05:08.800 align:start position:0%
over putting gilmore on cobb and putting
 

00:05:08.800 --> 00:05:09.909 align:start position:0%
over putting gilmore on cobb and putting
jones<00:05:09.120><c> on</c><00:05:09.280><c> atoms</c>

00:05:09.909 --> 00:05:09.919 align:start position:0%
jones on atoms
 

00:05:09.919 --> 00:05:11.749 align:start position:0%
jones on atoms
an<00:05:10.160><c> increase</c><00:05:10.479><c> that</c><00:05:10.720><c> large</c><00:05:11.120><c> experienced</c><00:05:11.600><c> over</c>

00:05:11.749 --> 00:05:11.759 align:start position:0%
an increase that large experienced over
 

00:05:11.759 --> 00:05:13.189 align:start position:0%
an increase that large experienced over
the<00:05:11.840><c> course</c><00:05:12.080><c> of</c><00:05:12.160><c> an</c><00:05:12.240><c> entire</c><00:05:12.639><c> season</c>

00:05:13.189 --> 00:05:13.199 align:start position:0%
the course of an entire season
 

00:05:13.199 --> 00:05:14.790 align:start position:0%
the course of an entire season
could<00:05:13.360><c> have</c><00:05:13.440><c> a</c><00:05:13.520><c> massive</c><00:05:14.000><c> impact</c><00:05:14.320><c> on</c><00:05:14.400><c> a</c><00:05:14.479><c> team's</c>

00:05:14.790 --> 00:05:14.800 align:start position:0%
could have a massive impact on a team's
 

00:05:14.800 --> 00:05:18.230 align:start position:0%
could have a massive impact on a team's
overall<00:05:15.120><c> success</c>

00:05:18.230 --> 00:05:18.240 align:start position:0%
 
 

00:05:18.240 --> 00:05:19.830 align:start position:0%
 
we<00:05:18.400><c> have</c><00:05:18.560><c> developed</c><00:05:18.960><c> an</c><00:05:19.039><c> effective</c><00:05:19.440><c> method</c>

00:05:19.830 --> 00:05:19.840 align:start position:0%
we have developed an effective method
 

00:05:19.840 --> 00:05:21.590 align:start position:0%
we have developed an effective method
for<00:05:20.000><c> evaluating</c><00:05:20.479><c> players</c><00:05:20.800><c> at</c><00:05:20.960><c> cornerback</c><00:05:21.440><c> and</c>

00:05:21.590 --> 00:05:21.600 align:start position:0%
for evaluating players at cornerback and
 

00:05:21.600 --> 00:05:23.110 align:start position:0%
for evaluating players at cornerback and
wide<00:05:21.840><c> receiver</c><00:05:22.320><c> using</c><00:05:22.560><c> the</c><00:05:22.639><c> space</c><00:05:22.880><c> show</c>

00:05:23.110 --> 00:05:23.120 align:start position:0%
wide receiver using the space show
 

00:05:23.120 --> 00:05:24.790 align:start position:0%
wide receiver using the space show
temporal<00:05:23.520><c> features</c><00:05:23.919><c> provided</c><00:05:24.240><c> by</c><00:05:24.400><c> next-gen</c>

00:05:24.790 --> 00:05:24.800 align:start position:0%
temporal features provided by next-gen
 

00:05:24.800 --> 00:05:26.150 align:start position:0%
temporal features provided by next-gen
stats<00:05:25.120><c> tracking</c><00:05:25.520><c> data</c>

00:05:26.150 --> 00:05:26.160 align:start position:0%
stats tracking data
 

00:05:26.160 --> 00:05:28.070 align:start position:0%
stats tracking data
our<00:05:26.479><c> tape</c><00:05:26.800><c> rating</c><00:05:27.039><c> system</c><00:05:27.360><c> can</c><00:05:27.520><c> be</c><00:05:27.680><c> utilized</c>

00:05:28.070 --> 00:05:28.080 align:start position:0%
our tape rating system can be utilized
 

00:05:28.080 --> 00:05:30.070 align:start position:0%
our tape rating system can be utilized
by<00:05:28.240><c> nfl</c><00:05:28.639><c> teams</c><00:05:28.880><c> for</c><00:05:29.039><c> week-to-week</c><00:05:29.440><c> game</c><00:05:29.680><c> prep</c>

00:05:30.070 --> 00:05:30.080 align:start position:0%
by nfl teams for week-to-week game prep
 

00:05:30.080 --> 00:05:31.670 align:start position:0%
by nfl teams for week-to-week game prep
and<00:05:30.240><c> for</c><00:05:30.479><c> long-term</c><00:05:30.800><c> roster</c><00:05:31.199><c> construction</c><00:05:31.600><c> in</c>

00:05:31.670 --> 00:05:31.680 align:start position:0%
and for long-term roster construction in
 

00:05:31.680 --> 00:05:33.430 align:start position:0%
and for long-term roster construction in
order<00:05:31.919><c> to</c><00:05:32.080><c> substantially</c><00:05:32.639><c> improve</c><00:05:33.039><c> on-field</c>

00:05:33.430 --> 00:05:33.440 align:start position:0%
order to substantially improve on-field
 

00:05:33.440 --> 00:05:35.029 align:start position:0%
order to substantially improve on-field
success

00:05:35.029 --> 00:05:35.039 align:start position:0%
success
 

00:05:35.039 --> 00:05:36.629 align:start position:0%
success
we<00:05:35.199><c> hope</c><00:05:35.360><c> to</c><00:05:35.440><c> see</c><00:05:35.600><c> this</c><00:05:35.759><c> method</c><00:05:36.080><c> utilized</c><00:05:36.479><c> by</c>

00:05:36.629 --> 00:05:36.639 align:start position:0%
we hope to see this method utilized by
 

00:05:36.639 --> 00:05:38.230 align:start position:0%
we hope to see this method utilized by
teams<00:05:36.960><c> in</c><00:05:37.039><c> the</c><00:05:37.120><c> future</c><00:05:37.680><c> and</c><00:05:37.840><c> we</c><00:05:37.919><c> hope</c><00:05:38.080><c> you</c>

00:05:38.230 --> 00:05:38.240 align:start position:0%
teams in the future and we hope you
 

00:05:38.240 --> 00:05:39.749 align:start position:0%
teams in the future and we hope you
enjoyed<00:05:38.560><c> our</c><00:05:38.639><c> presentation</c>

00:05:39.749 --> 00:05:39.759 align:start position:0%
enjoyed our presentation
 

00:05:39.759 --> 00:05:49.919 align:start position:0%
enjoyed our presentation
thank<00:05:39.919><c> you</c><00:05:40.080><c> all</c><00:05:40.160><c> for</c><00:05:46.919><c> listening</c>

