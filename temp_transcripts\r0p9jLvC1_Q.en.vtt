WEBVTT
Kind: captions
Language: en

00:00:08.880 --> 00:00:11.789 align:start position:0%
 
hello<00:00:09.240><c> and</c><00:00:09.599><c> welcome</c><00:00:10.599><c> in</c><00:00:10.759><c> this</c><00:00:11.000><c> presentation</c>

00:00:11.789 --> 00:00:11.799 align:start position:0%
hello and welcome in this presentation
 

00:00:11.799 --> 00:00:13.789 align:start position:0%
hello and welcome in this presentation
we'll<00:00:12.080><c> briefly</c><00:00:12.480><c> talk</c><00:00:12.759><c> about</c><00:00:13.120><c> the</c><00:00:13.360><c> hydrop</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
we'll briefly talk about the hydrop
 

00:00:13.799 --> 00:00:15.829 align:start position:0%
we'll briefly talk about the hydrop
power<00:00:14.040><c> modeling</c><00:00:14.799><c> behind</c><00:00:15.120><c> the</c><00:00:15.320><c> paneuropean</c>

00:00:15.829 --> 00:00:15.839 align:start position:0%
power modeling behind the paneuropean
 

00:00:15.839 --> 00:00:20.630 align:start position:0%
power modeling behind the paneuropean
climate<00:00:16.560><c> database</c><00:00:17.520><c> or</c><00:00:17.880><c> PCD</c>

00:00:20.630 --> 00:00:20.640 align:start position:0%
 
 

00:00:20.640 --> 00:00:23.509 align:start position:0%
 
4.2<00:00:21.640><c> the</c><00:00:21.800><c> hydrop</c><00:00:22.199><c> power</c><00:00:22.439><c> conversion</c><00:00:22.920><c> model</c>

00:00:23.509 --> 00:00:23.519 align:start position:0%
4.2 the hydrop power conversion model
 

00:00:23.519 --> 00:00:25.189 align:start position:0%
4.2 the hydrop power conversion model
consists<00:00:24.000><c> of</c><00:00:24.119><c> a</c><00:00:24.359><c> statistical</c><00:00:24.880><c> machine</c>

00:00:25.189 --> 00:00:25.199 align:start position:0%
consists of a statistical machine
 

00:00:25.199 --> 00:00:28.189 align:start position:0%
consists of a statistical machine
learning<00:00:25.680><c> model</c><00:00:26.480><c> based</c><00:00:26.760><c> on</c><00:00:27.039><c> emble</c><00:00:27.480><c> learning</c>

00:00:28.189 --> 00:00:28.199 align:start position:0%
learning model based on emble learning
 

00:00:28.199 --> 00:00:30.910 align:start position:0%
learning model based on emble learning
called<00:00:28.840><c> random</c><00:00:29.199><c> Forest</c><00:00:29.560><c> regression</c><00:00:30.119><c> model</c>

00:00:30.910 --> 00:00:30.920 align:start position:0%
called random Forest regression model
 

00:00:30.920 --> 00:00:32.630 align:start position:0%
called random Forest regression model
where<00:00:31.160><c> hundreds</c><00:00:31.519><c> of</c><00:00:31.720><c> independent</c><00:00:32.279><c> decision</c>

00:00:32.630 --> 00:00:32.640 align:start position:0%
where hundreds of independent decision
 

00:00:32.640 --> 00:00:36.030 align:start position:0%
where hundreds of independent decision
trees<00:00:33.440><c> learn</c><00:00:33.879><c> patterns</c><00:00:34.559><c> from</c><00:00:35.079><c> predictors</c>

00:00:36.030 --> 00:00:36.040 align:start position:0%
trees learn patterns from predictors
 

00:00:36.040 --> 00:00:38.910 align:start position:0%
trees learn patterns from predictors
namely<00:00:36.480><c> temperature</c><00:00:37.280><c> and</c><00:00:37.680><c> precipitation</c><00:00:38.680><c> to</c>

00:00:38.910 --> 00:00:38.920 align:start position:0%
namely temperature and precipitation to
 

00:00:38.920 --> 00:00:41.029 align:start position:0%
namely temperature and precipitation to
reproduce<00:00:39.440><c> the</c><00:00:39.600><c> target</c><00:00:40.000><c> variables</c><00:00:40.600><c> or</c>

00:00:41.029 --> 00:00:41.039 align:start position:0%
reproduce the target variables or
 

00:00:41.039 --> 00:00:43.950 align:start position:0%
reproduce the target variables or
predicts<00:00:42.039><c> which</c><00:00:42.200><c> are</c><00:00:42.600><c> the</c><00:00:42.800><c> energy</c><00:00:43.200><c> data</c><00:00:43.800><c> of</c>

00:00:43.950 --> 00:00:43.960 align:start position:0%
predicts which are the energy data of
 

00:00:43.960 --> 00:00:46.189 align:start position:0%
predicts which are the energy data of
hydrop<00:00:44.320><c> power</c><00:00:44.520><c> generation</c><00:00:44.960><c> or</c>

00:00:46.189 --> 00:00:46.199 align:start position:0%
hydrop power generation or
 

00:00:46.199 --> 00:00:49.430 align:start position:0%
hydrop power generation or
inflow<00:00:47.199><c> the</c><00:00:47.440><c> final</c><00:00:47.760><c> prediction</c><00:00:48.600><c> is</c><00:00:48.800><c> obtained</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
inflow the final prediction is obtained
 

00:00:49.440 --> 00:00:51.310 align:start position:0%
inflow the final prediction is obtained
by<00:00:49.600><c> averaging</c><00:00:50.079><c> the</c><00:00:50.239><c> outputs</c><00:00:50.840><c> of</c><00:00:50.960><c> all</c><00:00:51.199><c> the</c>

00:00:51.310 --> 00:00:51.320 align:start position:0%
by averaging the outputs of all the
 

00:00:51.320 --> 00:00:54.229 align:start position:0%
by averaging the outputs of all the
decision<00:00:51.680><c> trees</c><00:00:52.160><c> in</c><00:00:52.320><c> the</c><00:00:52.480><c> assemble</c><00:00:53.120><c> or</c>

00:00:54.229 --> 00:00:54.239 align:start position:0%
decision trees in the assemble or
 

00:00:54.239 --> 00:00:57.750 align:start position:0%
decision trees in the assemble or
Forest<00:00:55.239><c> the</c><00:00:55.399><c> temporal</c><00:00:55.800><c> resolution</c><00:00:56.520><c> is</c><00:00:56.760><c> weekly</c>

00:00:57.750 --> 00:00:57.760 align:start position:0%
Forest the temporal resolution is weekly
 

00:00:57.760 --> 00:00:59.990 align:start position:0%
Forest the temporal resolution is weekly
the<00:00:57.960><c> spal</c><00:00:58.359><c> resolution</c><00:00:59.079><c> is</c><00:00:59.280><c> that</c><00:00:59.399><c> of</c><00:00:59.519><c> the</c><00:00:59.680><c> p</c>

00:00:59.990 --> 00:01:00.000 align:start position:0%
the spal resolution is that of the p
 

00:01:00.000 --> 00:01:03.189 align:start position:0%
the spal resolution is that of the p
European<00:01:00.519><c> zones</c><00:01:01.039><c> on</c><00:01:01.519><c> Shore</c><00:01:02.519><c> which</c><00:01:02.800><c> as</c><00:01:02.920><c> you</c><00:01:03.000><c> can</c>

00:01:03.189 --> 00:01:03.199 align:start position:0%
European zones on Shore which as you can
 

00:01:03.199 --> 00:01:06.390 align:start position:0%
European zones on Shore which as you can
see<00:01:03.800><c> from</c><00:01:04.000><c> this</c><00:01:04.239><c> map</c><00:01:05.239><c> correspond</c><00:01:05.760><c> to</c><00:01:06.040><c> Country</c>

00:01:06.390 --> 00:01:06.400 align:start position:0%
see from this map correspond to Country
 

00:01:06.400 --> 00:01:09.190 align:start position:0%
see from this map correspond to Country
level<00:01:06.840><c> for</c><00:01:07.080><c> most</c><00:01:07.320><c> countries</c><00:01:08.200><c> with</c><00:01:08.320><c> a</c><00:01:08.439><c> few</c>

00:01:09.190 --> 00:01:09.200 align:start position:0%
level for most countries with a few
 

00:01:09.200 --> 00:01:11.469 align:start position:0%
level for most countries with a few
exceptions<00:01:10.200><c> now</c><00:01:10.400><c> given</c><00:01:10.640><c> the</c><00:01:10.840><c> size</c><00:01:11.200><c> of</c><00:01:11.320><c> the</c>

00:01:11.469 --> 00:01:11.479 align:start position:0%
exceptions now given the size of the
 

00:01:11.479 --> 00:01:13.870 align:start position:0%
exceptions now given the size of the
domain<00:01:12.040><c> and</c><00:01:12.240><c> the</c><00:01:12.439><c> absence</c><00:01:13.159><c> of</c><00:01:13.360><c> detailed</c>

00:01:13.870 --> 00:01:13.880 align:start position:0%
domain and the absence of detailed
 

00:01:13.880 --> 00:01:15.710 align:start position:0%
domain and the absence of detailed
publicly<00:01:14.280><c> available</c><00:01:14.759><c> data</c><00:01:15.240><c> for</c><00:01:15.439><c> the</c>

00:01:15.710 --> 00:01:15.720 align:start position:0%
publicly available data for the
 

00:01:15.720 --> 00:01:18.149 align:start position:0%
publicly available data for the
individual<00:01:16.320><c> hydropower</c><00:01:16.880><c> plants</c><00:01:17.799><c> such</c><00:01:18.000><c> as</c>

00:01:18.149 --> 00:01:18.159 align:start position:0%
individual hydropower plants such as
 

00:01:18.159 --> 00:01:20.830 align:start position:0%
individual hydropower plants such as
plant<00:01:18.600><c> heads</c><00:01:19.200><c> installed</c><00:01:19.680><c> Technologies</c><00:01:20.640><c> and</c>

00:01:20.830 --> 00:01:20.840 align:start position:0%
plant heads installed Technologies and
 

00:01:20.840 --> 00:01:23.190 align:start position:0%
plant heads installed Technologies and
artificial<00:01:21.680><c> regulations</c><00:01:22.680><c> the</c><00:01:22.840><c> idea</c><00:01:23.079><c> of</c>

00:01:23.190 --> 00:01:23.200 align:start position:0%
artificial regulations the idea of
 

00:01:23.200 --> 00:01:25.149 align:start position:0%
artificial regulations the idea of
employing<00:01:23.600><c> a</c><00:01:23.720><c> physical</c><00:01:24.119><c> ideological</c><00:01:24.720><c> model</c>

00:01:25.149 --> 00:01:25.159 align:start position:0%
employing a physical ideological model
 

00:01:25.159 --> 00:01:27.789 align:start position:0%
employing a physical ideological model
was<00:01:25.600><c> discarded</c><00:01:26.600><c> this</c><00:01:26.840><c> approach</c><00:01:27.280><c> was</c><00:01:27.439><c> instead</c>

00:01:27.789 --> 00:01:27.799 align:start position:0%
was discarded this approach was instead
 

00:01:27.799 --> 00:01:30.149 align:start position:0%
was discarded this approach was instead
adopted<00:01:28.439><c> for</c><00:01:28.680><c> its</c><00:01:28.920><c> adaptability</c><00:01:29.920><c> to</c><00:01:30.040><c> the</c>

00:01:30.149 --> 00:01:30.159 align:start position:0%
adopted for its adaptability to the
 

00:01:30.159 --> 00:01:32.469 align:start position:0%
adopted for its adaptability to the
available<00:01:30.600><c> inputs</c><00:01:31.560><c> and</c><00:01:31.759><c> it</c><00:01:31.920><c> reduced</c>

00:01:32.469 --> 00:01:32.479 align:start position:0%
available inputs and it reduced
 

00:01:32.479 --> 00:01:34.069 align:start position:0%
available inputs and it reduced
computational

00:01:34.069 --> 00:01:34.079 align:start position:0%
computational
 

00:01:34.079 --> 00:01:37.510 align:start position:0%
computational
costs<00:01:35.079><c> since</c><00:01:35.399><c> this</c><00:01:35.600><c> is</c><00:01:35.680><c> a</c><00:01:35.880><c> data</c><00:01:36.159><c> driven</c><00:01:36.600><c> model</c>

00:01:37.510 --> 00:01:37.520 align:start position:0%
costs since this is a data driven model
 

00:01:37.520 --> 00:01:40.030 align:start position:0%
costs since this is a data driven model
the<00:01:37.720><c> importance</c><00:01:38.439><c> of</c><00:01:38.640><c> reliable</c><00:01:39.159><c> energy</c><00:01:39.600><c> input</c>

00:01:40.030 --> 00:01:40.040 align:start position:0%
the importance of reliable energy input
 

00:01:40.040 --> 00:01:43.310 align:start position:0%
the importance of reliable energy input
data<00:01:40.640><c> is</c>

00:01:43.310 --> 00:01:43.320 align:start position:0%
 
 

00:01:43.320 --> 00:01:46.950 align:start position:0%
 
crucial<00:01:44.320><c> in</c><00:01:44.479><c> the</c><00:01:44.719><c> PCD</c><00:01:45.320><c> 4.2</c><00:01:46.320><c> we</c><00:01:46.520><c> address</c>

00:01:46.950 --> 00:01:46.960 align:start position:0%
crucial in the PCD 4.2 we address
 

00:01:46.960 --> 00:01:48.630 align:start position:0%
crucial in the PCD 4.2 we address
several<00:01:47.360><c> hydropower</c>

00:01:48.630 --> 00:01:48.640 align:start position:0%
several hydropower
 

00:01:48.640 --> 00:01:51.630 align:start position:0%
several hydropower
Technologies<00:01:50.000><c> reservoirs</c><00:01:51.000><c> turn</c><00:01:51.240><c> of</c><00:01:51.360><c> Reaver</c>

00:01:51.630 --> 00:01:51.640 align:start position:0%
Technologies reservoirs turn of Reaver
 

00:01:51.640 --> 00:01:55.310 align:start position:0%
Technologies reservoirs turn of Reaver
and<00:01:51.799><c> pondage</c><00:01:52.719><c> and</c><00:01:52.920><c> open</c><00:01:53.240><c> loop</c><00:01:53.759><c> pump</c><00:01:54.320><c> storage</c>

00:01:55.310 --> 00:01:55.320 align:start position:0%
and pondage and open loop pump storage
 

00:01:55.320 --> 00:01:57.830 align:start position:0%
and pondage and open loop pump storage
the<00:01:55.479><c> input</c><00:01:55.840><c> data</c><00:01:56.119><c> are</c><00:01:56.439><c> hence</c><00:01:57.039><c> time</c><00:01:57.320><c> series</c><00:01:57.680><c> of</c>

00:01:57.830 --> 00:01:57.840 align:start position:0%
the input data are hence time series of
 

00:01:57.840 --> 00:02:00.029 align:start position:0%
the input data are hence time series of
hydrop<00:01:58.159><c> power</c><00:01:58.399><c> generation</c><00:01:59.360><c> and</c><00:01:59.520><c> Storage</c>

00:02:00.029 --> 00:02:00.039 align:start position:0%
hydrop power generation and Storage
 

00:02:00.039 --> 00:02:01.510 align:start position:0%
hydrop power generation and Storage
energy<00:02:00.399><c> into</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
energy into
 

00:02:01.520 --> 00:02:04.109 align:start position:0%
energy into
reservoirs<00:02:02.520><c> where</c><00:02:02.719><c> monthly</c><00:02:03.119><c> resolution</c><00:02:03.640><c> data</c>

00:02:04.109 --> 00:02:04.119 align:start position:0%
reservoirs where monthly resolution data
 

00:02:04.119 --> 00:02:07.029 align:start position:0%
reservoirs where monthly resolution data
are<00:02:04.399><c> available</c><00:02:05.399><c> also</c><00:02:05.759><c> install</c><00:02:06.240><c> capacity</c><00:02:06.799><c> time</c>

00:02:07.029 --> 00:02:07.039 align:start position:0%
are available also install capacity time
 

00:02:07.039 --> 00:02:09.270 align:start position:0%
are available also install capacity time
series<00:02:07.520><c> are</c><00:02:07.680><c> used</c><00:02:08.360><c> to</c><00:02:08.560><c> get</c><00:02:08.759><c> rid</c><00:02:08.920><c> of</c><00:02:09.080><c> those</c>

00:02:09.270 --> 00:02:09.280 align:start position:0%
series are used to get rid of those
 

00:02:09.280 --> 00:02:11.470 align:start position:0%
series are used to get rid of those
changes<00:02:09.720><c> in</c><00:02:09.920><c> generation</c><00:02:10.440><c> signal</c><00:02:11.239><c> that</c><00:02:11.360><c> are</c>

00:02:11.470 --> 00:02:11.480 align:start position:0%
changes in generation signal that are
 

00:02:11.480 --> 00:02:13.910 align:start position:0%
changes in generation signal that are
due<00:02:11.720><c> to</c><00:02:11.879><c> new</c><00:02:12.440><c> installations</c><00:02:13.440><c> this</c><00:02:13.640><c> way</c>

00:02:13.910 --> 00:02:13.920 align:start position:0%
due to new installations this way
 

00:02:13.920 --> 00:02:16.430 align:start position:0%
due to new installations this way
improving<00:02:14.480><c> the</c><00:02:14.599><c> training</c><00:02:14.920><c> of</c><00:02:15.080><c> the</c>

00:02:16.430 --> 00:02:16.440 align:start position:0%
improving the training of the
 

00:02:16.440 --> 00:02:19.750 align:start position:0%
improving the training of the
model<00:02:17.440><c> the</c><00:02:17.560><c> sources</c><00:02:18.040><c> of</c><00:02:18.200><c> energy</c><00:02:18.640><c> data</c><00:02:19.120><c> are</c>

00:02:19.750 --> 00:02:19.760 align:start position:0%
model the sources of energy data are
 

00:02:19.760 --> 00:02:23.430 align:start position:0%
model the sources of energy data are
three<00:02:20.760><c> some</c><00:02:21.160><c> data</c><00:02:22.160><c> come</c><00:02:22.519><c> directly</c><00:02:23.000><c> from</c><00:02:23.200><c> the</c>

00:02:23.430 --> 00:02:23.440 align:start position:0%
three some data come directly from the
 

00:02:23.440 --> 00:02:26.110 align:start position:0%
three some data come directly from the
single<00:02:23.879><c> transmission</c><00:02:24.400><c> system</c><00:02:24.720><c> operators</c><00:02:25.440><c> the</c>

00:02:26.110 --> 00:02:26.120 align:start position:0%
single transmission system operators the
 

00:02:26.120 --> 00:02:28.670 align:start position:0%
single transmission system operators the
tsos<00:02:27.120><c> these</c><00:02:27.239><c> are</c><00:02:27.519><c> regarded</c><00:02:28.120><c> as</c><00:02:28.280><c> the</c><00:02:28.440><c> most</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
tsos these are regarded as the most
 

00:02:28.680 --> 00:02:31.470 align:start position:0%
tsos these are regarded as the most
reliable<00:02:29.519><c> in</c><00:02:29.680><c> a</c><00:02:29.920><c> Bren</c><00:02:30.280><c> with</c><00:02:30.440><c> n</c>

00:02:31.470 --> 00:02:31.480 align:start position:0%
reliable in a Bren with n
 

00:02:31.480 --> 00:02:34.990 align:start position:0%
reliable in a Bren with n
soe<00:02:32.480><c> publicly</c><00:02:32.920><c> available</c><00:02:33.519><c> data</c><00:02:34.519><c> coming</c><00:02:34.800><c> from</c>

00:02:34.990 --> 00:02:35.000 align:start position:0%
soe publicly available data coming from
 

00:02:35.000 --> 00:02:37.470 align:start position:0%
soe publicly available data coming from
the<00:02:35.160><c> transparency</c><00:02:35.720><c> platform</c><00:02:36.440><c> are</c><00:02:36.720><c> used</c><00:02:37.200><c> where</c>

00:02:37.470 --> 00:02:37.480 align:start position:0%
the transparency platform are used where
 

00:02:37.480 --> 00:02:39.790 align:start position:0%
the transparency platform are used where
possible<00:02:38.080><c> in</c><00:02:38.319><c> absence</c><00:02:38.920><c> of</c><00:02:39.040><c> the</c><00:02:39.159><c> former</c><00:02:39.519><c> source</c>

00:02:39.790 --> 00:02:39.800 align:start position:0%
possible in absence of the former source
 

00:02:39.800 --> 00:02:43.750 align:start position:0%
possible in absence of the former source
of<00:02:40.319><c> information</c><00:02:41.319><c> and</c><00:02:42.000><c> finally</c><00:02:43.000><c> info</c><00:02:43.440><c> from</c><00:02:43.599><c> the</c>

00:02:43.750 --> 00:02:43.760 align:start position:0%
of information and finally info from the
 

00:02:43.760 --> 00:02:47.509 align:start position:0%
of information and finally info from the
previous<00:02:44.120><c> PCD</c><00:02:44.720><c> version</c><00:02:45.319><c> the</c><00:02:45.879><c> 3.1</c><00:02:46.879><c> were</c><00:02:47.120><c> used</c>

00:02:47.509 --> 00:02:47.519 align:start position:0%
previous PCD version the 3.1 were used
 

00:02:47.519 --> 00:02:52.509 align:start position:0%
previous PCD version the 3.1 were used
where<00:02:47.720><c> no</c><00:02:47.879><c> other</c><00:02:48.120><c> source</c>

00:02:52.509 --> 00:02:52.519 align:start position:0%
 
 

00:02:52.519 --> 00:02:55.550 align:start position:0%
 
could<00:02:53.519><c> while</c><00:02:53.760><c> the</c><00:02:53.959><c> generation</c><00:02:54.519><c> can</c><00:02:54.640><c> be</c><00:02:54.879><c> almost</c>

00:02:55.550 --> 00:02:55.560 align:start position:0%
could while the generation can be almost
 

00:02:55.560 --> 00:02:57.949 align:start position:0%
could while the generation can be almost
directly<00:02:56.040><c> fed</c><00:02:56.319><c> to</c><00:02:56.400><c> the</c><00:02:56.560><c> model</c><00:02:57.239><c> the</c><00:02:57.400><c> inflows</c>

00:02:57.949 --> 00:02:57.959 align:start position:0%
directly fed to the model the inflows
 

00:02:57.959 --> 00:03:00.270 align:start position:0%
directly fed to the model the inflows
must<00:02:58.159><c> first</c><00:02:58.400><c> be</c><00:02:58.640><c> estimated</c>

00:03:00.270 --> 00:03:00.280 align:start position:0%
must first be estimated
 

00:03:00.280 --> 00:03:03.110 align:start position:0%
must first be estimated
therefore<00:03:01.280><c> when</c><00:03:01.519><c> enough</c><00:03:01.840><c> data</c><00:03:02.080><c> is</c><00:03:02.239><c> available</c>

00:03:03.110 --> 00:03:03.120 align:start position:0%
therefore when enough data is available
 

00:03:03.120 --> 00:03:05.350 align:start position:0%
therefore when enough data is available
the<00:03:03.280><c> inflows</c><00:03:03.760><c> are</c><00:03:04.040><c> estimated</c><00:03:04.879><c> through</c>

00:03:05.350 --> 00:03:05.360 align:start position:0%
the inflows are estimated through
 

00:03:05.360 --> 00:03:07.990 align:start position:0%
the inflows are estimated through
balance<00:03:06.000><c> equations</c><00:03:07.000><c> taking</c><00:03:07.319><c> into</c><00:03:07.560><c> account</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
balance equations taking into account
 

00:03:08.000 --> 00:03:11.789 align:start position:0%
balance equations taking into account
both<00:03:08.599><c> generation</c><00:03:09.599><c> and</c><00:03:09.720><c> stored</c><00:03:10.159><c> energy</c><00:03:10.519><c> time</c>

00:03:11.789 --> 00:03:11.799 align:start position:0%
both generation and stored energy time
 

00:03:11.799 --> 00:03:16.070 align:start position:0%
both generation and stored energy time
series<00:03:12.799><c> otherwise</c><00:03:13.720><c> the</c><00:03:13.920><c> PCD</c><00:03:14.440><c> 3.1</c><00:03:15.080><c> inflows</c><00:03:15.840><c> are</c>

00:03:16.070 --> 00:03:16.080 align:start position:0%
series otherwise the PCD 3.1 inflows are
 

00:03:16.080 --> 00:03:19.229 align:start position:0%
series otherwise the PCD 3.1 inflows are
taken<00:03:16.599><c> as</c><00:03:16.840><c> best</c><00:03:17.280><c> guess</c><00:03:18.080><c> and</c><00:03:18.319><c> directly</c><00:03:18.760><c> fed</c><00:03:19.120><c> to</c>

00:03:19.229 --> 00:03:19.239 align:start position:0%
taken as best guess and directly fed to
 

00:03:19.239 --> 00:03:20.229 align:start position:0%
taken as best guess and directly fed to
the

00:03:20.229 --> 00:03:20.239 align:start position:0%
the
 

00:03:20.239 --> 00:03:24.190 align:start position:0%
the
model<00:03:21.239><c> depending</c><00:03:22.040><c> on</c><00:03:22.400><c> the</c><00:03:22.640><c> input</c><00:03:23.120><c> data</c><00:03:24.040><c> the</c>

00:03:24.190 --> 00:03:24.200 align:start position:0%
model depending on the input data the
 

00:03:24.200 --> 00:03:26.910 align:start position:0%
model depending on the input data the
model<00:03:24.599><c> will</c><00:03:24.799><c> then</c><00:03:25.040><c> output</c><00:03:25.799><c> several</c><00:03:26.519><c> different</c>

00:03:26.910 --> 00:03:26.920 align:start position:0%
model will then output several different
 

00:03:26.920 --> 00:03:29.550 align:start position:0%
model will then output several different
Technologies<00:03:27.560><c> as</c><00:03:27.720><c> mentioned</c><00:03:28.280><c> earlier</c><00:03:29.200><c> each</c>

00:03:29.550 --> 00:03:29.560 align:start position:0%
Technologies as mentioned earlier each
 

00:03:29.560 --> 00:03:33.390 align:start position:0%
Technologies as mentioned earlier each
with<00:03:29.879><c> its</c><00:03:30.080><c> own</c>

00:03:33.390 --> 00:03:33.400 align:start position:0%
 
 

00:03:33.400 --> 00:03:36.190 align:start position:0%
 
abbreviation<00:03:34.400><c> coming</c><00:03:34.720><c> now</c><00:03:34.920><c> to</c><00:03:35.200><c> the</c>

00:03:36.190 --> 00:03:36.200 align:start position:0%
abbreviation coming now to the
 

00:03:36.200 --> 00:03:38.670 align:start position:0%
abbreviation coming now to the
predictors<00:03:37.200><c> temperature</c><00:03:37.760><c> and</c><00:03:37.959><c> precipitation</c>

00:03:38.670 --> 00:03:38.680 align:start position:0%
predictors temperature and precipitation
 

00:03:38.680 --> 00:03:41.350 align:start position:0%
predictors temperature and precipitation
data<00:03:39.280><c> are</c><00:03:39.560><c> first</c><00:03:40.000><c> aggregated</c><00:03:41.000><c> and</c><00:03:41.200><c> then</c>

00:03:41.350 --> 00:03:41.360 align:start position:0%
data are first aggregated and then
 

00:03:41.360 --> 00:03:43.030 align:start position:0%
data are first aggregated and then
lagged<00:03:41.959><c> over</c>

00:03:43.030 --> 00:03:43.040 align:start position:0%
lagged over
 

00:03:43.040 --> 00:03:46.429 align:start position:0%
lagged over
time<00:03:44.040><c> this</c><00:03:44.360><c> pre-processing</c><00:03:45.239><c> step</c><00:03:45.560><c> is</c><00:03:45.840><c> very</c>

00:03:46.429 --> 00:03:46.439 align:start position:0%
time this pre-processing step is very
 

00:03:46.439 --> 00:03:49.789 align:start position:0%
time this pre-processing step is very
important<00:03:47.439><c> the</c><00:03:47.560><c> two</c><00:03:47.760><c> variables</c><00:03:48.519><c> are</c><00:03:49.120><c> average</c>

00:03:49.789 --> 00:03:49.799 align:start position:0%
important the two variables are average
 

00:03:49.799 --> 00:03:52.990 align:start position:0%
important the two variables are average
in<00:03:50.000><c> case</c><00:03:50.239><c> of</c><00:03:50.799><c> temperature</c><00:03:51.680><c> or</c><00:03:52.000><c> accumulated</c><00:03:52.840><c> in</c>

00:03:52.990 --> 00:03:53.000 align:start position:0%
in case of temperature or accumulated in
 

00:03:53.000 --> 00:03:55.630 align:start position:0%
in case of temperature or accumulated in
case<00:03:53.159><c> of</c><00:03:53.400><c> precipitation</c><00:03:54.400><c> over</c><00:03:54.720><c> multiple</c>

00:03:55.630 --> 00:03:55.640 align:start position:0%
case of precipitation over multiple
 

00:03:55.640 --> 00:03:58.830 align:start position:0%
case of precipitation over multiple
weeks<00:03:56.640><c> this</c><00:03:56.879><c> to</c><00:03:57.120><c> account</c><00:03:57.400><c> for</c><00:03:57.640><c> the</c><00:03:57.840><c> history</c><00:03:58.680><c> of</c>

00:03:58.830 --> 00:03:58.840 align:start position:0%
weeks this to account for the history of
 

00:03:58.840 --> 00:04:01.030 align:start position:0%
weeks this to account for the history of
the<00:03:58.959><c> weather</c><00:03:59.239><c> and</c><00:03:59.400><c> climate</c><00:04:00.040><c> conditions</c><00:04:00.840><c> of</c>

00:04:01.030 --> 00:04:01.040 align:start position:0%
the weather and climate conditions of
 

00:04:01.040 --> 00:04:02.110 align:start position:0%
the weather and climate conditions of
each

00:04:02.110 --> 00:04:02.120 align:start position:0%
each
 

00:04:02.120 --> 00:04:04.470 align:start position:0%
each
region<00:04:03.120><c> at</c><00:04:03.319><c> this</c><00:04:03.480><c> point</c><00:04:03.879><c> the</c><00:04:04.000><c> model</c><00:04:04.319><c> is</c>

00:04:04.470 --> 00:04:04.480 align:start position:0%
region at this point the model is
 

00:04:04.480 --> 00:04:08.270 align:start position:0%
region at this point the model is
trained<00:04:05.120><c> on</c><00:04:05.680><c> the</c><00:04:05.920><c> ER</c><00:04:06.319><c> five</c><00:04:06.519><c> reanalysis</c><00:04:07.280><c> data</c>

00:04:08.270 --> 00:04:08.280 align:start position:0%
trained on the ER five reanalysis data
 

00:04:08.280 --> 00:04:11.830 align:start position:0%
trained on the ER five reanalysis data
and<00:04:08.560><c> on</c><00:04:08.879><c> the</c><00:04:09.239><c> available</c><00:04:09.720><c> energy</c><00:04:10.480><c> data</c><00:04:11.480><c> and</c><00:04:11.599><c> is</c>

00:04:11.830 --> 00:04:11.840 align:start position:0%
and on the available energy data and is
 

00:04:11.840 --> 00:04:15.469 align:start position:0%
and on the available energy data and is
validated<00:04:12.439><c> using</c><00:04:12.760><c> a</c><00:04:12.879><c> leave</c><00:04:13.120><c> onee</c><00:04:13.519><c> out</c>

00:04:15.469 --> 00:04:15.479 align:start position:0%
validated using a leave onee out
 

00:04:15.479 --> 00:04:17.509 align:start position:0%
validated using a leave onee out
procedure<00:04:16.479><c> this</c><00:04:16.600><c> means</c><00:04:16.840><c> that</c><00:04:17.000><c> the</c><00:04:17.079><c> model</c><00:04:17.359><c> is</c>

00:04:17.509 --> 00:04:17.519 align:start position:0%
procedure this means that the model is
 

00:04:17.519 --> 00:04:20.270 align:start position:0%
procedure this means that the model is
trained<00:04:18.040><c> to</c><00:04:18.280><c> predict</c><00:04:18.799><c> or</c><00:04:19.079><c> estimate</c><00:04:19.799><c> one</c><00:04:20.000><c> year</c>

00:04:20.270 --> 00:04:20.280 align:start position:0%
trained to predict or estimate one year
 

00:04:20.280 --> 00:04:24.350 align:start position:0%
trained to predict or estimate one year
at<00:04:20.400><c> a</c><00:04:20.639><c> time</c><00:04:21.320><c> in</c><00:04:21.479><c> this</c><00:04:21.680><c> case</c><00:04:22.560><c> 2015</c><00:04:23.560><c> by</c><00:04:23.759><c> training</c>

00:04:24.350 --> 00:04:24.360 align:start position:0%
at a time in this case 2015 by training
 

00:04:24.360 --> 00:04:30.150 align:start position:0%
at a time in this case 2015 by training
on<00:04:24.600><c> all</c><00:04:24.880><c> other</c><00:04:25.280><c> available</c><00:04:25.720><c> years</c>

00:04:30.150 --> 00:04:30.160 align:start position:0%
 
 

00:04:30.160 --> 00:04:32.629 align:start position:0%
 
it<00:04:30.320><c> is</c><00:04:30.600><c> then</c><00:04:30.880><c> tested</c><00:04:31.360><c> on</c>

00:04:32.629 --> 00:04:32.639 align:start position:0%
it is then tested on
 

00:04:32.639 --> 00:04:36.270 align:start position:0%
it is then tested on
2016<00:04:33.639><c> and</c><00:04:33.759><c> on</c><00:04:34.400><c> 2017</c><00:04:35.400><c> and</c><00:04:35.520><c> so</c>

00:04:36.270 --> 00:04:36.280 align:start position:0%
2016 and on 2017 and so
 

00:04:36.280 --> 00:04:39.150 align:start position:0%
2016 and on 2017 and so
on<00:04:37.280><c> and</c><00:04:37.440><c> the</c><00:04:37.680><c> model</c><00:04:37.960><c> parameters</c><00:04:38.600><c> yielding</c><00:04:39.000><c> the</c>

00:04:39.150 --> 00:04:39.160 align:start position:0%
on and the model parameters yielding the
 

00:04:39.160 --> 00:04:41.629 align:start position:0%
on and the model parameters yielding the
best<00:04:39.479><c> results</c><00:04:40.080><c> are</c><00:04:40.280><c> memorized</c><00:04:41.199><c> for</c><00:04:41.440><c> each</c>

00:04:41.629 --> 00:04:41.639 align:start position:0%
best results are memorized for each
 

00:04:41.639 --> 00:04:43.390 align:start position:0%
best results are memorized for each
region<00:04:42.240><c> and</c><00:04:42.360><c> for</c><00:04:42.600><c> each</c>

00:04:43.390 --> 00:04:43.400 align:start position:0%
region and for each
 

00:04:43.400 --> 00:04:46.110 align:start position:0%
region and for each
technology<00:04:44.400><c> the</c><00:04:44.560><c> model</c><00:04:44.960><c> at</c><00:04:45.120><c> this</c><00:04:45.320><c> point</c><00:04:45.960><c> is</c>

00:04:46.110 --> 00:04:46.120 align:start position:0%
technology the model at this point is
 

00:04:46.120 --> 00:04:48.870 align:start position:0%
technology the model at this point is
strain<00:04:46.639><c> on</c><00:04:46.880><c> all</c><00:04:47.160><c> years</c><00:04:47.479><c> available</c><00:04:48.320><c> depending</c>

00:04:48.870 --> 00:04:48.880 align:start position:0%
strain on all years available depending
 

00:04:48.880 --> 00:04:52.350 align:start position:0%
strain on all years available depending
on<00:04:49.280><c> the</c><00:04:49.520><c> energy</c><00:04:49.880><c> input</c><00:04:50.280><c> data</c><00:04:51.080><c> at</c><00:04:51.280><c> hand</c><00:04:52.199><c> the</c>

00:04:52.350 --> 00:04:52.360 align:start position:0%
on the energy input data at hand the
 

00:04:52.360 --> 00:04:56.670 align:start position:0%
on the energy input data at hand the
longer<00:04:52.639><c> the</c><00:04:52.759><c> time</c><00:04:52.960><c> series</c><00:04:53.400><c> of</c><00:04:53.520><c> course</c><00:04:53.960><c> the</c>

00:04:56.670 --> 00:04:56.680 align:start position:0%
 
 

00:04:56.680 --> 00:04:59.469 align:start position:0%
 
better<00:04:57.680><c> once</c><00:04:57.880><c> the</c><00:04:58.000><c> model</c><00:04:58.320><c> is</c><00:04:58.440><c> trained</c><00:04:59.199><c> it</c><00:04:59.320><c> can</c>

00:04:59.469 --> 00:04:59.479 align:start position:0%
better once the model is trained it can
 

00:04:59.479 --> 00:05:02.390 align:start position:0%
better once the model is trained it can
be<00:04:59.759><c> fed</c><00:05:00.120><c> by</c><00:05:00.280><c> different</c><00:05:00.680><c> climate</c><00:05:01.400><c> forcings</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
be fed by different climate forcings
 

00:05:02.400 --> 00:05:05.510 align:start position:0%
be fed by different climate forcings
such<00:05:02.600><c> as</c><00:05:02.840><c> the</c><00:05:03.160><c> complete</c><00:05:03.600><c> your</c><00:05:03.960><c> five</c><00:05:04.199><c> data</c><00:05:04.520><c> set</c>

00:05:05.510 --> 00:05:05.520 align:start position:0%
such as the complete your five data set
 

00:05:05.520 --> 00:05:08.590 align:start position:0%
such as the complete your five data set
or<00:05:05.919><c> several</c><00:05:06.280><c> SIM</c><00:05:06.639><c> 6</c><00:05:06.919><c> models</c><00:05:07.400><c> with</c><00:05:07.680><c> different</c>

00:05:08.590 --> 00:05:08.600 align:start position:0%
or several SIM 6 models with different
 

00:05:08.600 --> 00:05:11.390 align:start position:0%
or several SIM 6 models with different
scenarios<00:05:09.600><c> in</c><00:05:09.800><c> this</c><00:05:10.000><c> last</c><00:05:10.320><c> case</c><00:05:10.960><c> mind</c><00:05:11.199><c> that</c>

00:05:11.390 --> 00:05:11.400 align:start position:0%
scenarios in this last case mind that
 

00:05:11.400 --> 00:05:13.390 align:start position:0%
scenarios in this last case mind that
the<00:05:11.520><c> climate</c><00:05:11.880><c> data</c><00:05:12.160><c> will</c><00:05:12.360><c> be</c><00:05:12.600><c> first</c><00:05:13.080><c> B</c>

00:05:13.390 --> 00:05:13.400 align:start position:0%
the climate data will be first B
 

00:05:13.400 --> 00:05:16.390 align:start position:0%
the climate data will be first B
adjusted<00:05:14.280><c> with</c><00:05:14.520><c> respect</c><00:05:14.960><c> to</c><00:05:15.199><c> era</c>

00:05:16.390 --> 00:05:16.400 align:start position:0%
adjusted with respect to era
 

00:05:16.400 --> 00:05:19.270 align:start position:0%
adjusted with respect to era
5<00:05:17.400><c> this</c><00:05:17.560><c> way</c><00:05:17.840><c> we</c><00:05:17.960><c> can</c><00:05:18.199><c> reconstruct</c><00:05:19.080><c> the</c>

00:05:19.270 --> 00:05:19.280 align:start position:0%
5 this way we can reconstruct the
 

00:05:19.280 --> 00:05:21.909 align:start position:0%
5 this way we can reconstruct the
historical<00:05:19.800><c> series</c><00:05:20.440><c> of</c><00:05:20.680><c> generation</c><00:05:21.479><c> or</c>

00:05:21.909 --> 00:05:21.919 align:start position:0%
historical series of generation or
 

00:05:21.919 --> 00:05:26.230 align:start position:0%
historical series of generation or
inflow<00:05:22.919><c> as</c><00:05:23.080><c> well</c><00:05:23.280><c> as</c><00:05:23.560><c> estimate</c><00:05:24.280><c> their</c><00:05:24.520><c> future</c>

00:05:26.230 --> 00:05:26.240 align:start position:0%
inflow as well as estimate their future
 

00:05:26.240 --> 00:05:28.870 align:start position:0%
inflow as well as estimate their future
projections<00:05:27.240><c> the</c><00:05:27.400><c> figure</c><00:05:27.680><c> to</c><00:05:27.880><c> the</c><00:05:28.080><c> right</c>

00:05:28.870 --> 00:05:28.880 align:start position:0%
projections the figure to the right
 

00:05:28.880 --> 00:05:31.270 align:start position:0%
projections the figure to the right
shows<00:05:29.160><c> an</c><00:05:29.319><c> example</c><00:05:29.840><c> of</c><00:05:29.960><c> model</c><00:05:30.280><c> output</c><00:05:30.680><c> for</c>

00:05:31.270 --> 00:05:31.280 align:start position:0%
shows an example of model output for
 

00:05:31.280 --> 00:05:33.990 align:start position:0%
shows an example of model output for
fronts<00:05:32.280><c> reconstructed</c><00:05:33.160><c> and</c><00:05:33.360><c> projected</c><00:05:33.800><c> time</c>

00:05:33.990 --> 00:05:34.000 align:start position:0%
fronts reconstructed and projected time
 

00:05:34.000 --> 00:05:36.230 align:start position:0%
fronts reconstructed and projected time
series<00:05:34.479><c> are</c><00:05:34.680><c> plotted</c><00:05:35.120><c> as</c><00:05:35.319><c> annual</c><00:05:35.720><c> Aggregates</c>

00:05:36.230 --> 00:05:36.240 align:start position:0%
series are plotted as annual Aggregates
 

00:05:36.240 --> 00:05:40.270 align:start position:0%
series are plotted as annual Aggregates
for<00:05:36.400><c> runoff</c><00:05:36.759><c> rever</c><00:05:37.520><c> inflows</c><00:05:38.520><c> in</c><00:05:39.039><c> blue</c><00:05:40.039><c> you</c><00:05:40.160><c> can</c>

00:05:40.270 --> 00:05:40.280 align:start position:0%
for runoff rever inflows in blue you can
 

00:05:40.280 --> 00:05:42.629 align:start position:0%
for runoff rever inflows in blue you can
see<00:05:40.440><c> the</c><00:05:40.560><c> E</c><00:05:40.880><c> 5</c><00:05:41.080><c> driven</c><00:05:41.479><c> output</c><00:05:41.919><c> spanning</c><00:05:42.400><c> from</c>

00:05:42.629 --> 00:05:42.639 align:start position:0%
see the E 5 driven output spanning from
 

00:05:42.639 --> 00:05:45.990 align:start position:0%
see the E 5 driven output spanning from
1950<00:05:43.639><c> to</c><00:05:43.840><c> near</c><00:05:44.400><c> present</c><00:05:45.400><c> while</c><00:05:45.800><c> with</c>

00:05:45.990 --> 00:05:46.000 align:start position:0%
1950 to near present while with
 

00:05:46.000 --> 00:05:47.950 align:start position:0%
1950 to near present while with
different<00:05:46.360><c> colors</c><00:05:47.080><c> to</c><00:05:47.240><c> the</c><00:05:47.440><c> right</c><00:05:47.720><c> you</c><00:05:47.840><c> can</c>

00:05:47.950 --> 00:05:47.960 align:start position:0%
different colors to the right you can
 

00:05:47.960 --> 00:05:50.909 align:start position:0%
different colors to the right you can
see<00:05:48.199><c> the</c><00:05:48.360><c> output</c><00:05:48.919><c> of</c><00:05:49.160><c> the</c><00:05:49.479><c> model</c><00:05:50.199><c> driven</c><00:05:50.680><c> by</c>

00:05:50.909 --> 00:05:50.919 align:start position:0%
see the output of the model driven by
 

00:05:50.919 --> 00:05:54.469 align:start position:0%
see the output of the model driven by
six<00:05:51.280><c> different</c><00:05:51.639><c> SIM</c><00:05:52.080><c> 6</c><00:05:52.400><c> models</c><00:05:53.199><c> under</c><00:05:53.560><c> two</c><00:05:54.280><c> out</c>

00:05:54.469 --> 00:05:54.479 align:start position:0%
six different SIM 6 models under two out
 

00:05:54.479 --> 00:06:00.950 align:start position:0%
six different SIM 6 models under two out
of<00:05:54.639><c> four</c><00:05:55.080><c> considered</c><00:05:55.680><c> scenarios</c>

00:06:00.950 --> 00:06:00.960 align:start position:0%
 
 

00:06:00.960 --> 00:06:02.950 align:start position:0%
 
in<00:06:01.160><c> this</c><00:06:01.440><c> presentation</c><00:06:02.440><c> we've</c><00:06:02.600><c> seen</c><00:06:02.800><c> an</c>

00:06:02.950 --> 00:06:02.960 align:start position:0%
in this presentation we've seen an
 

00:06:02.960 --> 00:06:05.350 align:start position:0%
in this presentation we've seen an
overview<00:06:03.479><c> of</c><00:06:03.600><c> the</c><00:06:03.759><c> hydrop</c><00:06:04.120><c> power</c><00:06:04.400><c> statistical</c>

00:06:05.350 --> 00:06:05.360 align:start position:0%
overview of the hydrop power statistical
 

00:06:05.360 --> 00:06:07.350 align:start position:0%
overview of the hydrop power statistical
model<00:06:06.360><c> we've</c><00:06:06.560><c> mentioned</c><00:06:06.880><c> the</c><00:06:07.039><c> different</c>

00:06:07.350 --> 00:06:07.360 align:start position:0%
model we've mentioned the different
 

00:06:07.360 --> 00:06:10.550 align:start position:0%
model we've mentioned the different
energy<00:06:07.800><c> data</c><00:06:08.160><c> used</c><00:06:08.639><c> as</c><00:06:09.000><c> input</c><00:06:10.000><c> and</c><00:06:10.160><c> learned</c>

00:06:10.550 --> 00:06:10.560 align:start position:0%
energy data used as input and learned
 

00:06:10.560 --> 00:06:13.070 align:start position:0%
energy data used as input and learned
how<00:06:10.800><c> crucial</c><00:06:11.240><c> it</c><00:06:11.440><c> is</c><00:06:11.759><c> to</c><00:06:11.960><c> obtain</c><00:06:12.479><c> a</c><00:06:12.639><c> long</c><00:06:12.919><c> and</c>

00:06:13.070 --> 00:06:13.080 align:start position:0%
how crucial it is to obtain a long and
 

00:06:13.080 --> 00:06:15.150 align:start position:0%
how crucial it is to obtain a long and
reliable<00:06:13.560><c> time</c><00:06:13.759><c> series</c><00:06:14.120><c> of</c><00:06:14.319><c> generation</c>

00:06:15.150 --> 00:06:15.160 align:start position:0%
reliable time series of generation
 

00:06:15.160 --> 00:06:18.150 align:start position:0%
reliable time series of generation
stored<00:06:15.599><c> energy</c><00:06:16.199><c> and</c><00:06:16.400><c> install</c><00:06:16.960><c> capacity</c><00:06:17.960><c> to</c>

00:06:18.150 --> 00:06:18.160 align:start position:0%
stored energy and install capacity to
 

00:06:18.160 --> 00:06:19.390 align:start position:0%
stored energy and install capacity to
train<00:06:18.479><c> the</c>

00:06:19.390 --> 00:06:19.400 align:start position:0%
train the
 

00:06:19.400 --> 00:06:22.070 align:start position:0%
train the
model<00:06:20.400><c> we</c><00:06:20.560><c> touched</c><00:06:20.960><c> upon</c><00:06:21.199><c> the</c><00:06:21.400><c> climate</c><00:06:21.800><c> data</c>

00:06:22.070 --> 00:06:22.080 align:start position:0%
model we touched upon the climate data
 

00:06:22.080 --> 00:06:24.270 align:start position:0%
model we touched upon the climate data
used<00:06:22.639><c> and</c><00:06:22.759><c> the</c><00:06:22.919><c> importance</c><00:06:23.520><c> of</c><00:06:23.759><c> considering</c>

00:06:24.270 --> 00:06:24.280 align:start position:0%
used and the importance of considering
 

00:06:24.280 --> 00:06:27.629 align:start position:0%
used and the importance of considering
the<00:06:24.479><c> recent</c><00:06:25.240><c> history</c><00:06:26.240><c> and</c><00:06:26.440><c> finally</c><00:06:27.280><c> we</c><00:06:27.440><c> went</c>

00:06:27.629 --> 00:06:27.639 align:start position:0%
the recent history and finally we went
 

00:06:27.639 --> 00:06:29.029 align:start position:0%
the recent history and finally we went
through<00:06:27.840><c> the</c><00:06:27.960><c> different</c><00:06:28.280><c> runs</c><00:06:28.800><c> of</c><00:06:28.919><c> the</c>

00:06:29.029 --> 00:06:29.039 align:start position:0%
through the different runs of the
 

00:06:29.039 --> 00:06:33.390 align:start position:0%
through the different runs of the
modeling<00:06:29.360><c> change</c>

00:06:33.390 --> 00:06:33.400 align:start position:0%
 
 

00:06:33.400 --> 00:06:35.909 align:start position:0%
 
in<00:06:33.599><c> brief</c><00:06:34.440><c> this</c><00:06:34.599><c> was</c><00:06:34.880><c> the</c><00:06:35.160><c> hydr</c><00:06:35.560><c> power</c>

00:06:35.909 --> 00:06:35.919 align:start position:0%
in brief this was the hydr power
 

00:06:35.919 --> 00:06:39.589 align:start position:0%
in brief this was the hydr power
conversion<00:06:36.479><c> model</c><00:06:37.120><c> behind</c><00:06:37.479><c> the</c><00:06:37.680><c> PCD</c><00:06:38.599><c> 4.2</c>

00:06:39.589 --> 00:06:39.599 align:start position:0%
conversion model behind the PCD 4.2
 

00:06:39.599 --> 00:06:43.120 align:start position:0%
conversion model behind the PCD 4.2
thanks<00:06:39.880><c> for</c><00:06:40.120><c> listening</c>

