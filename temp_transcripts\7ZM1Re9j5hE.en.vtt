WEBVTT
Kind: captions
Language: en

00:00:00.680 --> 00:00:03.189 align:start position:0%
 
hey<00:00:00.919><c> there</c><00:00:01.360><c> when</c><00:00:01.520><c> using</c><00:00:01.839><c> the</c><00:00:02.000><c> olama</c><00:00:02.520><c> CLI</c><00:00:03.040><c> or</c>

00:00:03.189 --> 00:00:03.199 align:start position:0%
hey there when using the olama CLI or
 

00:00:03.199 --> 00:00:05.470 align:start position:0%
hey there when using the olama CLI or
repple<00:00:03.760><c> there</c><00:00:03.840><c> are</c><00:00:04.040><c> now</c><00:00:04.240><c> keyboard</c><00:00:04.680><c> shortcuts</c>

00:00:05.470 --> 00:00:05.480 align:start position:0%
repple there are now keyboard shortcuts
 

00:00:05.480 --> 00:00:07.470 align:start position:0%
repple there are now keyboard shortcuts
that<00:00:05.640><c> you</c><00:00:05.759><c> can</c><00:00:05.920><c> use</c><00:00:06.279><c> to</c><00:00:06.640><c> to</c><00:00:06.799><c> move</c><00:00:07.040><c> around</c><00:00:07.319><c> the</c>

00:00:07.470 --> 00:00:07.480 align:start position:0%
that you can use to to move around the
 

00:00:07.480 --> 00:00:09.310 align:start position:0%
that you can use to to move around the
prompt<00:00:08.320><c> these</c><00:00:08.519><c> can</c><00:00:08.639><c> be</c><00:00:08.800><c> useful</c><00:00:09.120><c> when</c>

00:00:09.310 --> 00:00:09.320 align:start position:0%
prompt these can be useful when
 

00:00:09.320 --> 00:00:11.070 align:start position:0%
prompt these can be useful when
leveraging<00:00:09.800><c> the</c><00:00:10.000><c> previous</c><00:00:10.360><c> prompt</c><00:00:10.800><c> but</c><00:00:10.960><c> you</c>

00:00:11.070 --> 00:00:11.080 align:start position:0%
leveraging the previous prompt but you
 

00:00:11.080 --> 00:00:13.549 align:start position:0%
leveraging the previous prompt but you
need<00:00:11.240><c> to</c><00:00:11.480><c> change</c><00:00:11.880><c> something</c><00:00:12.719><c> on</c><00:00:12.880><c> Linux</c><00:00:13.360><c> the</c>

00:00:13.549 --> 00:00:13.559 align:start position:0%
need to change something on Linux the
 

00:00:13.559 --> 00:00:15.509 align:start position:0%
need to change something on Linux the
shortcuts<00:00:14.120><c> should</c><00:00:14.360><c> work</c><00:00:14.679><c> as</c><00:00:14.920><c> expected</c><00:00:15.400><c> but</c>

00:00:15.509 --> 00:00:15.519 align:start position:0%
shortcuts should work as expected but
 

00:00:15.519 --> 00:00:18.109 align:start position:0%
shortcuts should work as expected but
it's<00:00:15.719><c> possible</c><00:00:16.279><c> that</c><00:00:16.440><c> on</c><00:00:16.640><c> Mac</c><00:00:17.520><c> they</c><00:00:17.680><c> might</c><00:00:17.920><c> not</c>

00:00:18.109 --> 00:00:18.119 align:start position:0%
it's possible that on Mac they might not
 

00:00:18.119 --> 00:00:20.590 align:start position:0%
it's possible that on Mac they might not
work<00:00:18.359><c> as</c><00:00:18.560><c> designed</c><00:00:18.960><c> at</c><00:00:19.080><c> least</c><00:00:19.640><c> that</c><00:00:19.800><c> was</c><00:00:20.199><c> my</c>

00:00:20.590 --> 00:00:20.600 align:start position:0%
work as designed at least that was my
 

00:00:20.600 --> 00:00:22.910 align:start position:0%
work as designed at least that was my
experience<00:00:21.600><c> let's</c><00:00:21.840><c> take</c><00:00:21.960><c> a</c><00:00:22.119><c> look</c><00:00:22.240><c> at</c><00:00:22.480><c> why</c><00:00:22.720><c> this</c>

00:00:22.910 --> 00:00:22.920 align:start position:0%
experience let's take a look at why this
 

00:00:22.920 --> 00:00:26.310 align:start position:0%
experience let's take a look at why this
happens<00:00:23.359><c> to</c><00:00:23.599><c> get</c><00:00:23.720><c> to</c><00:00:23.880><c> the</c><00:00:24.000><c> repple</c><00:00:24.560><c> Run</c><00:00:24.880><c> AMA</c><00:00:25.680><c> run</c>

00:00:26.310 --> 00:00:26.320 align:start position:0%
happens to get to the repple Run AMA run
 

00:00:26.320 --> 00:00:28.589 align:start position:0%
happens to get to the repple Run AMA run
and<00:00:26.439><c> then</c><00:00:26.599><c> the</c><00:00:26.679><c> model</c><00:00:27.000><c> name</c><00:00:27.679><c> llama</c><00:00:28.000><c> 2</c><00:00:28.279><c> came</c><00:00:28.400><c> out</c>

00:00:28.589 --> 00:00:28.599 align:start position:0%
and then the model name llama 2 came out
 

00:00:28.599 --> 00:00:31.310 align:start position:0%
and then the model name llama 2 came out
pretty<00:00:28.840><c> recently</c><00:00:29.279><c> so</c><00:00:29.560><c> I'll</c><00:00:29.720><c> use</c><00:00:30.119><c> that</c><00:00:30.279><c> one</c><00:00:31.119><c> to</c>

00:00:31.310 --> 00:00:31.320 align:start position:0%
pretty recently so I'll use that one to
 

00:00:31.320 --> 00:00:33.389 align:start position:0%
pretty recently so I'll use that one to
find<00:00:31.560><c> the</c><00:00:31.720><c> list</c><00:00:31.920><c> of</c><00:00:32.040><c> shortcuts</c><00:00:32.640><c> type</c><00:00:32.960><c> SL</c>

00:00:33.389 --> 00:00:33.399 align:start position:0%
find the list of shortcuts type SL
 

00:00:33.399 --> 00:00:36.069 align:start position:0%
find the list of shortcuts type SL
question<00:00:33.680><c> mark</c><00:00:34.040><c> and</c><00:00:34.200><c> then</c><00:00:34.840><c> shortcuts</c><00:00:35.840><c> you</c><00:00:35.920><c> can</c>

00:00:36.069 --> 00:00:36.079 align:start position:0%
question mark and then shortcuts you can
 

00:00:36.079 --> 00:00:38.670 align:start position:0%
question mark and then shortcuts you can
see<00:00:36.399><c> there</c><00:00:36.559><c> are</c><00:00:36.719><c> a</c><00:00:36.879><c> few</c><00:00:37.120><c> options</c><00:00:37.520><c> here</c><00:00:38.079><c> crl</c><00:00:38.440><c> A</c>

00:00:38.670 --> 00:00:38.680 align:start position:0%
see there are a few options here crl A
 

00:00:38.680 --> 00:00:41.430 align:start position:0%
see there are a few options here crl A
and<00:00:38.879><c> contr</c><00:00:39.239><c> E</c><00:00:39.920><c> move</c><00:00:40.200><c> to</c><00:00:40.360><c> the</c><00:00:40.520><c> beginning</c><00:00:41.120><c> and</c><00:00:41.280><c> to</c>

00:00:41.430 --> 00:00:41.440 align:start position:0%
and contr E move to the beginning and to
 

00:00:41.440 --> 00:00:43.790 align:start position:0%
and contr E move to the beginning and to
the<00:00:41.600><c> end</c><00:00:41.960><c> of</c><00:00:42.079><c> the</c><00:00:42.239><c> line</c><00:00:43.039><c> now</c><00:00:43.480><c> that</c><00:00:43.600><c> can</c>

00:00:43.790 --> 00:00:43.800 align:start position:0%
the end of the line now that can
 

00:00:43.800 --> 00:00:46.229 align:start position:0%
the end of the line now that can
actually<00:00:44.000><c> be</c><00:00:44.360><c> a</c><00:00:44.480><c> little</c><00:00:44.760><c> confusing</c><00:00:45.760><c> I</c><00:00:45.960><c> think</c>

00:00:46.229 --> 00:00:46.239 align:start position:0%
actually be a little confusing I think
 

00:00:46.239 --> 00:00:49.270 align:start position:0%
actually be a little confusing I think
anytime<00:00:46.559><c> you</c><00:00:46.680><c> see</c><00:00:47.239><c> line</c><00:00:47.719><c> or</c><00:00:48.039><c> sentence</c><00:00:48.920><c> really</c>

00:00:49.270 --> 00:00:49.280 align:start position:0%
anytime you see line or sentence really
 

00:00:49.280 --> 00:00:52.349 align:start position:0%
anytime you see line or sentence really
think<00:00:49.440><c> of</c><00:00:49.600><c> it</c><00:00:49.879><c> as</c><00:00:50.399><c> prompt</c><00:00:51.239><c> so</c><00:00:51.480><c> contr</c><00:00:51.840><c> a</c><00:00:52.079><c> moves</c>

00:00:52.349 --> 00:00:52.359 align:start position:0%
think of it as prompt so contr a moves
 

00:00:52.359 --> 00:00:55.150 align:start position:0%
think of it as prompt so contr a moves
to<00:00:52.520><c> the</c><00:00:52.719><c> beginning</c><00:00:53.079><c> of</c><00:00:53.320><c> the</c><00:00:53.760><c> prompt</c><00:00:54.600><c> and</c><00:00:54.800><c> contr</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
to the beginning of the prompt and contr
 

00:00:55.160 --> 00:00:58.549 align:start position:0%
to the beginning of the prompt and contr
E<00:00:55.440><c> moves</c><00:00:55.719><c> to</c><00:00:55.879><c> the</c><00:00:56.000><c> end</c><00:00:56.239><c> of</c><00:00:56.440><c> The</c><00:00:57.280><c> Prompt</c><00:00:58.280><c> next</c>

00:00:58.549 --> 00:00:58.559 align:start position:0%
E moves to the end of The Prompt next
 

00:00:58.559 --> 00:01:01.509 align:start position:0%
E moves to the end of The Prompt next
comes<00:00:58.960><c> alt</c><00:00:59.280><c> B</c><00:00:59.600><c> and</c><00:00:59.960><c> alt</c><00:01:00.320><c> f</c><00:01:00.600><c> for</c><00:01:00.800><c> moving</c><00:01:01.280><c> back</c>

00:01:01.509 --> 00:01:01.519 align:start position:0%
comes alt B and alt f for moving back
 

00:01:01.519 --> 00:01:04.590 align:start position:0%
comes alt B and alt f for moving back
and<00:01:01.680><c> forward</c><00:01:02.239><c> by</c><00:01:02.399><c> a</c><00:01:02.519><c> word</c><00:01:03.480><c> on</c><00:01:03.680><c> my</c><00:01:03.879><c> Mac</c><00:01:04.439><c> this</c>

00:01:04.590 --> 00:01:04.600 align:start position:0%
and forward by a word on my Mac this
 

00:01:04.600 --> 00:01:06.590 align:start position:0%
and forward by a word on my Mac this
didn't<00:01:04.879><c> work</c><00:01:05.600><c> if</c><00:01:05.720><c> you're</c><00:01:05.840><c> seeing</c><00:01:06.159><c> a</c><00:01:06.280><c> similar</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
didn't work if you're seeing a similar
 

00:01:06.600 --> 00:01:08.710 align:start position:0%
didn't work if you're seeing a similar
issue<00:01:07.040><c> then</c><00:01:07.360><c> this</c><00:01:07.600><c> might</c><00:01:07.880><c> be</c><00:01:08.040><c> the</c><00:01:08.159><c> way</c><00:01:08.320><c> to</c><00:01:08.520><c> fix</c>

00:01:08.710 --> 00:01:08.720 align:start position:0%
issue then this might be the way to fix
 

00:01:08.720 --> 00:01:11.310 align:start position:0%
issue then this might be the way to fix
it<00:01:09.040><c> for</c><00:01:09.240><c> you</c><00:01:10.080><c> if</c><00:01:10.200><c> you're</c><00:01:10.360><c> using</c><00:01:10.600><c> the</c><00:01:10.759><c> standard</c>

00:01:11.310 --> 00:01:11.320 align:start position:0%
it for you if you're using the standard
 

00:01:11.320 --> 00:01:13.590 align:start position:0%
it for you if you're using the standard
Mac<00:01:11.640><c> terminal</c><00:01:12.240><c> then</c><00:01:12.439><c> open</c><00:01:12.799><c> preferences</c><00:01:13.360><c> and</c>

00:01:13.590 --> 00:01:13.600 align:start position:0%
Mac terminal then open preferences and
 

00:01:13.600 --> 00:01:15.550 align:start position:0%
Mac terminal then open preferences and
navigate<00:01:14.000><c> to</c><00:01:14.240><c> profiles</c><00:01:14.680><c> and</c><00:01:15.159><c> whichever</c>

00:01:15.550 --> 00:01:15.560 align:start position:0%
navigate to profiles and whichever
 

00:01:15.560 --> 00:01:17.950 align:start position:0%
navigate to profiles and whichever
profile<00:01:16.000><c> you're</c><00:01:16.200><c> using</c><00:01:17.119><c> go</c><00:01:17.240><c> to</c><00:01:17.400><c> the</c><00:01:17.520><c> keyboard</c>

00:01:17.950 --> 00:01:17.960 align:start position:0%
profile you're using go to the keyboard
 

00:01:17.960 --> 00:01:20.510 align:start position:0%
profile you're using go to the keyboard
Tab<00:01:18.400><c> and</c><00:01:18.520><c> then</c><00:01:18.680><c> check</c><00:01:18.920><c> the</c><00:01:19.080><c> checkbox</c><00:01:19.640><c> for</c><00:01:20.079><c> use</c>

00:01:20.510 --> 00:01:20.520 align:start position:0%
Tab and then check the checkbox for use
 

00:01:20.520 --> 00:01:23.870 align:start position:0%
Tab and then check the checkbox for use
option<00:01:21.079><c> as</c><00:01:21.280><c> meta</c><00:01:21.720><c> key</c><00:01:22.720><c> if</c><00:01:22.799><c> you're</c><00:01:22.960><c> using</c><00:01:23.280><c> iterm</c>

00:01:23.870 --> 00:01:23.880 align:start position:0%
option as meta key if you're using iterm
 

00:01:23.880 --> 00:01:25.870 align:start position:0%
option as meta key if you're using iterm
bring<00:01:24.000><c> up</c><00:01:24.200><c> preferences</c><00:01:24.799><c> then</c><00:01:25.040><c> profiles</c><00:01:25.600><c> then</c>

00:01:25.870 --> 00:01:25.880 align:start position:0%
bring up preferences then profiles then
 

00:01:25.880 --> 00:01:28.310 align:start position:0%
bring up preferences then profiles then
the<00:01:26.000><c> keys</c><00:01:26.320><c> Tab</c><00:01:27.119><c> and</c><00:01:27.240><c> then</c><00:01:27.400><c> the</c><00:01:27.560><c> subtab</c><00:01:28.119><c> for</c>

00:01:28.310 --> 00:01:28.320 align:start position:0%
the keys Tab and then the subtab for
 

00:01:28.320 --> 00:01:30.670 align:start position:0%
the keys Tab and then the subtab for
General<00:01:29.200><c> you</c><00:01:29.320><c> can</c><00:01:29.439><c> see</c><00:01:29.600><c> there</c><00:01:30.040><c> settings</c><00:01:30.400><c> for</c>

00:01:30.670 --> 00:01:30.680 align:start position:0%
General you can see there settings for
 

00:01:30.680 --> 00:01:33.469 align:start position:0%
General you can see there settings for
the<00:01:30.920><c> left</c><00:01:31.159><c> and</c><00:01:31.520><c> right</c><00:01:31.840><c> option</c><00:01:32.240><c> key</c><00:01:33.040><c> mine</c><00:01:33.240><c> were</c>

00:01:33.469 --> 00:01:33.479 align:start position:0%
the left and right option key mine were
 

00:01:33.479 --> 00:01:35.789 align:start position:0%
the left and right option key mine were
already<00:01:33.720><c> set</c><00:01:33.960><c> to</c><00:01:34.159><c> escape</c><00:01:34.560><c> plus</c><00:01:34.920><c> but</c><00:01:35.320><c> my</c><00:01:35.560><c> left</c>

00:01:35.789 --> 00:01:35.799 align:start position:0%
already set to escape plus but my left
 

00:01:35.799 --> 00:01:38.789 align:start position:0%
already set to escape plus but my left
option<00:01:36.119><c> key</c><00:01:36.479><c> had</c><00:01:36.920><c> apps</c><00:01:37.280><c> can</c><00:01:37.479><c> change</c><00:01:38.040><c> this</c>

00:01:38.789 --> 00:01:38.799 align:start position:0%
option key had apps can change this
 

00:01:38.799 --> 00:01:41.950 align:start position:0%
option key had apps can change this
enabled<00:01:39.799><c> disable</c><00:01:40.399><c> that</c><00:01:41.200><c> now</c><00:01:41.399><c> go</c><00:01:41.600><c> back</c><00:01:41.720><c> to</c><00:01:41.840><c> the</c>

00:01:41.950 --> 00:01:41.960 align:start position:0%
enabled disable that now go back to the
 

00:01:41.960 --> 00:01:45.630 align:start position:0%
enabled disable that now go back to the
terminal<00:01:42.439><c> and</c><00:01:42.720><c> ALT</c><00:01:43.399><c> or</c><00:01:43.680><c> on</c><00:01:43.880><c> the</c><00:01:44.040><c> Mac</c><00:01:44.479><c> option</c><00:01:45.240><c> b</c>

00:01:45.630 --> 00:01:45.640 align:start position:0%
terminal and ALT or on the Mac option b
 

00:01:45.640 --> 00:01:48.830 align:start position:0%
terminal and ALT or on the Mac option b
and<00:01:45.960><c> F</c><00:01:46.439><c> work</c><00:01:46.880><c> as</c><00:01:47.240><c> expected</c><00:01:48.240><c> finally</c><00:01:48.600><c> there's</c>

00:01:48.830 --> 00:01:48.840 align:start position:0%
and F work as expected finally there's
 

00:01:48.840 --> 00:01:50.990 align:start position:0%
and F work as expected finally there's
contrl<00:01:49.200><c> K</c><00:01:49.399><c> and</c><00:01:49.600><c> contrl</c><00:01:49.960><c> U</c><00:01:50.280><c> to</c><00:01:50.479><c> delete</c>

00:01:50.990 --> 00:01:51.000 align:start position:0%
contrl K and contrl U to delete
 

00:01:51.000 --> 00:01:52.870 align:start position:0%
contrl K and contrl U to delete
everything<00:01:51.520><c> before</c><00:01:52.000><c> or</c><00:01:52.320><c> after</c><00:01:52.600><c> where</c><00:01:52.759><c> the</c>

00:01:52.870 --> 00:01:52.880 align:start position:0%
everything before or after where the
 

00:01:52.880 --> 00:01:55.749 align:start position:0%
everything before or after where the
cursor<00:01:53.320><c> is</c><00:01:54.320><c> it</c><00:01:54.439><c> says</c><00:01:54.719><c> sentence</c><00:01:55.280><c> as</c><00:01:55.399><c> of</c><00:01:55.600><c> this</c>

00:01:55.749 --> 00:01:55.759 align:start position:0%
cursor is it says sentence as of this
 

00:01:55.759 --> 00:01:57.350 align:start position:0%
cursor is it says sentence as of this
recording<00:01:56.200><c> but</c><00:01:56.479><c> just</c><00:01:56.640><c> think</c><00:01:56.799><c> of</c><00:01:56.920><c> it</c><00:01:57.039><c> as</c><00:01:57.200><c> the</c>

00:01:57.350 --> 00:01:57.360 align:start position:0%
recording but just think of it as the
 

00:01:57.360 --> 00:02:00.870 align:start position:0%
recording but just think of it as the
entire<00:01:57.759><c> prompt</c><00:01:58.439><c> to</c><00:01:58.759><c> or</c><00:01:59.000><c> from</c><00:01:59.439><c> the</c><00:01:59.560><c> cursor</c><00:01:59.880><c> C</c>

00:02:00.870 --> 00:02:00.880 align:start position:0%
entire prompt to or from the cursor C
 

00:02:00.880 --> 00:02:03.310 align:start position:0%
entire prompt to or from the cursor C
the<00:02:01.079><c> last</c><00:02:01.280><c> three</c><00:02:01.520><c> options</c><00:02:01.840><c> are</c><00:02:02.079><c> contrl</c><00:02:02.479><c> L</c><00:02:03.079><c> to</c>

00:02:03.310 --> 00:02:03.320 align:start position:0%
the last three options are contrl L to
 

00:02:03.320 --> 00:02:06.510 align:start position:0%
the last three options are contrl L to
clear<00:02:03.600><c> the</c><00:02:03.759><c> screen</c><00:02:04.719><c> contrl</c><00:02:05.159><c> C</c><00:02:05.960><c> to</c><00:02:06.159><c> stop</c><00:02:06.399><c> the</c>

00:02:06.510 --> 00:02:06.520 align:start position:0%
clear the screen contrl C to stop the
 

00:02:06.520 --> 00:02:09.749 align:start position:0%
clear the screen contrl C to stop the
model<00:02:06.840><c> from</c><00:02:07.119><c> responding</c><00:02:08.039><c> and</c><00:02:08.239><c> control</c><00:02:08.720><c> D</c><00:02:09.560><c> to</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
model from responding and control D to
 

00:02:09.759 --> 00:02:12.229 align:start position:0%
model from responding and control D to
quit<00:02:10.000><c> the</c><00:02:10.560><c> reppel</c><00:02:11.560><c> if</c><00:02:11.680><c> you're</c><00:02:11.840><c> using</c><00:02:12.120><c> a</c>

00:02:12.229 --> 00:02:12.239 align:start position:0%
quit the reppel if you're using a
 

00:02:12.239 --> 00:02:13.750 align:start position:0%
quit the reppel if you're using a
different<00:02:12.560><c> terminal</c><00:02:13.239><c> and</c><00:02:13.319><c> you're</c><00:02:13.480><c> having</c>

00:02:13.750 --> 00:02:13.760 align:start position:0%
different terminal and you're having
 

00:02:13.760 --> 00:02:15.790 align:start position:0%
different terminal and you're having
similar<00:02:14.160><c> problems</c><00:02:14.800><c> look</c><00:02:15.040><c> around</c><00:02:15.319><c> for</c><00:02:15.480><c> any</c>

00:02:15.790 --> 00:02:15.800 align:start position:0%
similar problems look around for any
 

00:02:15.800 --> 00:02:17.990 align:start position:0%
similar problems look around for any
preferences<00:02:16.360><c> that</c><00:02:16.519><c> deal</c><00:02:16.840><c> with</c><00:02:17.080><c> the</c><00:02:17.319><c> alt</c><00:02:17.720><c> or</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
preferences that deal with the alt or
 

00:02:18.000 --> 00:02:21.470 align:start position:0%
preferences that deal with the alt or
option<00:02:18.400><c> key</c><00:02:19.400><c> that</c><00:02:19.560><c> may</c><00:02:19.760><c> be</c><00:02:20.000><c> the</c><00:02:20.440><c> key</c><00:02:20.959><c> to</c><00:02:21.160><c> the</c>

00:02:21.470 --> 00:02:21.480 align:start position:0%
option key that may be the key to the
 

00:02:21.480 --> 00:02:24.350 align:start position:0%
option key that may be the key to the
solution<00:02:22.480><c> maybe</c><00:02:22.800><c> even</c><00:02:23.040><c> include</c><00:02:23.480><c> what</c><00:02:23.599><c> you</c><00:02:23.800><c> did</c>

00:02:24.350 --> 00:02:24.360 align:start position:0%
solution maybe even include what you did
 

00:02:24.360 --> 00:02:26.830 align:start position:0%
solution maybe even include what you did
in<00:02:24.480><c> the</c><00:02:24.640><c> comments</c><00:02:25.120><c> below</c><00:02:26.120><c> well</c><00:02:26.360><c> hopefully</c>

00:02:26.830 --> 00:02:26.840 align:start position:0%
in the comments below well hopefully
 

00:02:26.840 --> 00:02:28.670 align:start position:0%
in the comments below well hopefully
this<00:02:26.959><c> is</c><00:02:27.120><c> useful</c><00:02:27.519><c> for</c><00:02:27.680><c> you</c><00:02:28.200><c> if</c><00:02:28.360><c> there's</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
this is useful for you if there's
 

00:02:28.680 --> 00:02:30.710 align:start position:0%
this is useful for you if there's
anything<00:02:29.040><c> else</c><00:02:29.280><c> you'd</c><00:02:29.480><c> like</c><00:02:29.560><c> to</c><00:02:29.920><c> see</c><00:02:30.440><c> let</c><00:02:30.560><c> me</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
anything else you'd like to see let me
 

00:02:30.720 --> 00:02:32.750 align:start position:0%
anything else you'd like to see let me
know<00:02:30.959><c> in</c><00:02:31.040><c> the</c><00:02:31.200><c> comments</c><00:02:31.560><c> below</c><00:02:32.400><c> thanks</c><00:02:32.599><c> so</c>

00:02:32.750 --> 00:02:32.760 align:start position:0%
know in the comments below thanks so
 

00:02:32.760 --> 00:02:49.550 align:start position:0%
know in the comments below thanks so
much<00:02:32.920><c> for</c><00:02:33.080><c> watching</c>

00:02:49.550 --> 00:02:49.560 align:start position:0%
 
 

00:02:49.560 --> 00:02:52.560 align:start position:0%
 
goodbye

