WEBVTT
Kind: captions
Language: en

00:00:00.160 --> 00:00:03.429 align:start position:0%
 
You<00:00:00.400><c> remind</c><00:00:00.719><c> me</c><00:00:00.960><c> of</c><00:00:01.280><c> the</c><00:00:02.320><c> founder</c><00:00:02.720><c> of</c><00:00:03.040><c> that</c>

00:00:03.429 --> 00:00:03.439 align:start position:0%
You remind me of the founder of that
 

00:00:03.439 --> 00:00:05.590 align:start position:0%
You remind me of the founder of that
company<00:00:03.760><c> called</c><00:00:04.319><c> I</c><00:00:04.560><c> think</c><00:00:04.640><c> it's</c><00:00:04.880><c> Bass</c><00:00:05.200><c> 10</c><00:00:05.440><c> or</c>

00:00:05.590 --> 00:00:05.600 align:start position:0%
company called I think it's Bass 10 or
 

00:00:05.600 --> 00:00:08.070 align:start position:0%
company called I think it's Bass 10 or
Base<00:00:05.839><c> Camp.</c><00:00:06.160><c> No,</c><00:00:06.400><c> Base</c><00:00:06.640><c> Camp</c><00:00:07.279><c> is</c><00:00:07.520><c> what</c><00:00:07.680><c> it</c><00:00:07.839><c> is.</c>

00:00:08.070 --> 00:00:08.080 align:start position:0%
Base Camp. No, Base Camp is what it is.
 

00:00:08.080 --> 00:00:10.390 align:start position:0%
Base Camp. No, Base Camp is what it is.
And<00:00:08.880><c> they</c>

00:00:10.390 --> 00:00:10.400 align:start position:0%
And they
 

00:00:10.400 --> 00:00:12.470 align:start position:0%
And they
I<00:00:10.719><c> have</c><00:00:10.960><c> heard</c><00:00:11.120><c> a</c><00:00:11.280><c> few</c><00:00:11.440><c> podcasts</c><00:00:11.920><c> with</c><00:00:12.160><c> him</c><00:00:12.320><c> on</c>

00:00:12.470 --> 00:00:12.480 align:start position:0%
I have heard a few podcasts with him on
 

00:00:12.480 --> 00:00:17.269 align:start position:0%
I have heard a few podcasts with him on
it<00:00:12.800><c> and</c><00:00:13.519><c> he</c><00:00:13.840><c> talks</c><00:00:14.160><c> about</c><00:00:14.480><c> how</c><00:00:15.519><c> when</c><00:00:15.839><c> he</c><00:00:16.800><c> sees</c>

00:00:17.269 --> 00:00:17.279 align:start position:0%
it and he talks about how when he sees
 

00:00:17.279 --> 00:00:19.269 align:start position:0%
it and he talks about how when he sees
or<00:00:17.760><c> uses</c>

00:00:19.269 --> 00:00:19.279 align:start position:0%
or uses
 

00:00:19.279 --> 00:00:22.950 align:start position:0%
or uses
poorly<00:00:19.920><c> designed</c><00:00:20.560><c> or</c><00:00:21.119><c> just</c><00:00:21.520><c> not</c><00:00:22.000><c> the</c><00:00:22.400><c> best</c>

00:00:22.950 --> 00:00:22.960 align:start position:0%
poorly designed or just not the best
 

00:00:22.960 --> 00:00:25.590 align:start position:0%
poorly designed or just not the best
thoughtout<00:00:23.519><c> software,</c><00:00:24.560><c> it</c><00:00:24.880><c> drives</c><00:00:25.199><c> him</c>

00:00:25.590 --> 00:00:25.600 align:start position:0%
thoughtout software, it drives him
 

00:00:25.600 --> 00:00:29.429 align:start position:0%
thoughtout software, it drives him
crazy.<00:00:26.800><c> and</c><00:00:27.359><c> so</c><00:00:27.760><c> crazy</c><00:00:28.160><c> that</c><00:00:28.400><c> he</c><00:00:28.640><c> has</c><00:00:28.880><c> started</c>

00:00:29.429 --> 00:00:29.439 align:start position:0%
crazy. and so crazy that he has started
 

00:00:29.439 --> 00:00:33.110 align:start position:0%
crazy. and so crazy that he has started
a<00:00:29.760><c> few</c><00:00:29.920><c> different</c><00:00:30.640><c> companies</c><00:00:31.840><c> to</c><00:00:32.320><c> combat</c>

00:00:33.110 --> 00:00:33.120 align:start position:0%
a few different companies to combat
 

00:00:33.120 --> 00:00:36.150 align:start position:0%
a few different companies to combat
poorly<00:00:33.680><c> designed</c><00:00:34.160><c> software.</c><00:00:35.520><c> And</c><00:00:35.680><c> I</c><00:00:35.920><c> think</c>

00:00:36.150 --> 00:00:36.160 align:start position:0%
poorly designed software. And I think
 

00:00:36.160 --> 00:00:39.910 align:start position:0%
poorly designed software. And I think
one<00:00:36.399><c> of</c><00:00:36.480><c> them</c><00:00:36.640><c> was</c><00:00:36.880><c> even</c><00:00:37.280><c> a</c><00:00:37.600><c> parking</c><00:00:38.879><c> lot</c>

00:00:39.910 --> 00:00:39.920 align:start position:0%
one of them was even a parking lot
 

00:00:39.920 --> 00:00:43.510 align:start position:0%
one of them was even a parking lot
software<00:00:40.640><c> that</c><00:00:41.120><c> his</c><00:00:41.680><c> HOA</c><00:00:42.320><c> or</c><00:00:42.640><c> his</c><00:00:43.360><c> uh</c>

00:00:43.510 --> 00:00:43.520 align:start position:0%
software that his HOA or his uh
 

00:00:43.520 --> 00:00:46.630 align:start position:0%
software that his HOA or his uh
condominium<00:00:44.239><c> complex</c><00:00:44.800><c> used</c><00:00:45.600><c> and</c><00:00:45.840><c> he</c><00:00:46.079><c> hated</c><00:00:46.399><c> it</c>

00:00:46.630 --> 00:00:46.640 align:start position:0%
condominium complex used and he hated it
 

00:00:46.640 --> 00:00:48.470 align:start position:0%
condominium complex used and he hated it
so<00:00:46.800><c> much</c><00:00:47.039><c> he</c><00:00:47.200><c> just</c><00:00:47.360><c> went</c><00:00:47.520><c> and</c><00:00:47.760><c> created</c><00:00:48.079><c> a</c><00:00:48.320><c> new</c>

00:00:48.470 --> 00:00:48.480 align:start position:0%
so much he just went and created a new
 

00:00:48.480 --> 00:00:51.910 align:start position:0%
so much he just went and created a new
one<00:00:49.039><c> if</c><00:00:49.200><c> I'm</c><00:00:49.520><c> getting</c><00:00:49.760><c> that</c><00:00:50.160><c> grip.</c><00:00:51.039><c> And</c><00:00:51.280><c> it</c><00:00:51.440><c> it</c>

00:00:51.910 --> 00:00:51.920 align:start position:0%
one if I'm getting that grip. And it it
 

00:00:51.920 --> 00:00:53.990 align:start position:0%
one if I'm getting that grip. And it it
reminds<00:00:52.320><c> me</c><00:00:52.480><c> of</c><00:00:52.640><c> you</c><00:00:52.960><c> kind</c><00:00:53.120><c> of</c><00:00:53.360><c> you</c><00:00:53.680><c> go</c><00:00:53.840><c> through</c>

00:00:53.990 --> 00:00:54.000 align:start position:0%
reminds me of you kind of you go through
 

00:00:54.000 --> 00:00:56.950 align:start position:0%
reminds me of you kind of you go through
and<00:00:54.239><c> you</c><00:00:54.399><c> say,</c><00:00:54.559><c> "Wow,</c><00:00:55.840><c> this</c><00:00:56.320><c> developer</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
and you say, "Wow, this developer
 

00:00:56.960 --> 00:01:02.310 align:start position:0%
and you say, "Wow, this developer
experience<00:00:58.160><c> is</c><00:00:58.559><c> painful."</c><00:00:59.840><c> And</c><00:01:00.719><c> then</c>

00:01:02.310 --> 00:01:02.320 align:start position:0%
experience is painful." And then
 

00:01:02.320 --> 00:01:05.990 align:start position:0%
experience is painful." And then
I'm<00:01:02.640><c> sure</c><00:01:02.960><c> there</c><00:01:03.280><c> is</c><00:01:03.760><c> a</c><00:01:04.879><c> long</c><00:01:05.199><c> period</c><00:01:05.519><c> of</c><00:01:05.760><c> time</c>

00:01:05.990 --> 00:01:06.000 align:start position:0%
I'm sure there is a long period of time
 

00:01:06.000 --> 00:01:08.310 align:start position:0%
I'm sure there is a long period of time
where<00:01:06.240><c> you</c><00:01:06.479><c> sit</c><00:01:06.799><c> with</c><00:01:06.960><c> it</c><00:01:07.200><c> being</c><00:01:07.520><c> painful</c><00:01:08.080><c> and</c>

00:01:08.310 --> 00:01:08.320 align:start position:0%
where you sit with it being painful and
 

00:01:08.320 --> 00:01:10.950 align:start position:0%
where you sit with it being painful and
it<00:01:08.640><c> like</c><00:01:09.119><c> continues</c><00:01:09.600><c> to</c><00:01:09.760><c> be</c><00:01:09.920><c> painful,</c><00:01:10.320><c> but</c><00:01:10.640><c> it</c>

00:01:10.950 --> 00:01:10.960 align:start position:0%
it like continues to be painful, but it
 

00:01:10.960 --> 00:01:14.149 align:start position:0%
it like continues to be painful, but it
eats<00:01:11.360><c> away</c><00:01:11.680><c> at</c><00:01:11.920><c> your</c><00:01:12.080><c> soul</c><00:01:12.720><c> in</c><00:01:12.960><c> a</c><00:01:13.520><c> in</c><00:01:13.840><c> a</c><00:01:14.000><c> way</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
eats away at your soul in a in a way
 

00:01:14.159 --> 00:01:16.230 align:start position:0%
eats away at your soul in a in a way
that<00:01:14.479><c> for</c><00:01:14.880><c> some</c><00:01:15.040><c> of</c><00:01:15.200><c> us,</c><00:01:15.360><c> we</c><00:01:15.600><c> just</c><00:01:15.760><c> put</c><00:01:15.920><c> up</c><00:01:16.080><c> with</c>

00:01:16.230 --> 00:01:16.240 align:start position:0%
that for some of us, we just put up with
 

00:01:16.240 --> 00:01:18.870 align:start position:0%
that for some of us, we just put up with
it.<00:01:16.720><c> But</c><00:01:16.960><c> for</c><00:01:17.119><c> you,</c><00:01:17.360><c> you're</c><00:01:17.680><c> like,</c><00:01:18.159><c> I'm</c><00:01:18.560><c> gonna</c>

00:01:18.870 --> 00:01:18.880 align:start position:0%
it. But for you, you're like, I'm gonna
 

00:01:18.880 --> 00:01:20.550 align:start position:0%
it. But for you, you're like, I'm gonna
have<00:01:19.040><c> to</c><00:01:19.119><c> go</c><00:01:19.280><c> and</c><00:01:19.520><c> create</c><00:01:19.680><c> a</c><00:01:19.920><c> product</c><00:01:20.240><c> around</c>

00:01:20.550 --> 00:01:20.560 align:start position:0%
have to go and create a product around
 

00:01:20.560 --> 00:01:23.990 align:start position:0%
have to go and create a product around
this.<00:01:21.840><c> Yeah,</c><00:01:22.799><c> maybe.</c><00:01:23.200><c> Maybe</c><00:01:23.439><c> it's</c><00:01:23.520><c> a</c><00:01:23.759><c> maybe</c>

00:01:23.990 --> 00:01:24.000 align:start position:0%
this. Yeah, maybe. Maybe it's a maybe
 

00:01:24.000 --> 00:01:25.749 align:start position:0%
this. Yeah, maybe. Maybe it's a maybe
it's<00:01:24.159><c> a</c><00:01:24.320><c> bit</c><00:01:24.479><c> like</c><00:01:24.640><c> that.</c><00:01:24.960><c> Like,</c><00:01:25.280><c> yeah,</c><00:01:25.520><c> I</c>

00:01:25.749 --> 00:01:25.759 align:start position:0%
it's a bit like that. Like, yeah, I
 

00:01:25.759 --> 00:01:27.590 align:start position:0%
it's a bit like that. Like, yeah, I
don't<00:01:25.840><c> know.</c><00:01:26.159><c> And</c><00:01:26.400><c> like,</c><00:01:26.560><c> you</c><00:01:26.720><c> know,</c><00:01:26.960><c> like</c>

00:01:27.590 --> 00:01:27.600 align:start position:0%
don't know. And like, you know, like
 

00:01:27.600 --> 00:01:29.109 align:start position:0%
don't know. And like, you know, like
there<00:01:27.840><c> are</c><00:01:28.000><c> many</c><00:01:28.159><c> tools</c><00:01:28.400><c> that</c><00:01:28.640><c> are</c><00:01:28.799><c> just</c>

00:01:29.109 --> 00:01:29.119 align:start position:0%
there are many tools that are just
 

00:01:29.119 --> 00:01:31.830 align:start position:0%
there are many tools that are just
great,<00:01:29.600><c> but</c><00:01:29.920><c> they</c><00:01:30.159><c> are</c><00:01:30.320><c> also</c>

00:01:31.830 --> 00:01:31.840 align:start position:0%
great, but they are also
 

00:01:31.840 --> 00:01:34.469 align:start position:0%
great, but they are also
super<00:01:32.560><c> complex</c><00:01:33.040><c> or</c><00:01:33.200><c> like</c><00:01:33.439><c> dealing</c><00:01:33.680><c> with</c><00:01:33.920><c> a</c><00:01:34.240><c> lot</c>

00:01:34.469 --> 00:01:34.479 align:start position:0%
super complex or like dealing with a lot
 

00:01:34.479 --> 00:01:37.270 align:start position:0%
super complex or like dealing with a lot
of<00:01:34.960><c> nuances</c><00:01:35.600><c> and</c><00:01:35.840><c> it's</c><00:01:36.079><c> just</c><00:01:36.240><c> like</c><00:01:36.479><c> some</c><00:01:37.040><c> layer</c>

00:01:37.270 --> 00:01:37.280 align:start position:0%
of nuances and it's just like some layer
 

00:01:37.280 --> 00:01:39.830 align:start position:0%
of nuances and it's just like some layer
of<00:01:37.520><c> abstraction</c><00:01:38.720><c> and</c><00:01:38.880><c> then</c><00:01:39.119><c> sometimes</c><00:01:39.759><c> you</c>

00:01:39.830 --> 00:01:39.840 align:start position:0%
of abstraction and then sometimes you
 

00:01:39.840 --> 00:01:41.830 align:start position:0%
of abstraction and then sometimes you
just<00:01:39.920><c> just</c><00:01:40.240><c> having</c><00:01:40.479><c> to</c><00:01:40.720><c> deal</c><00:01:40.960><c> with</c><00:01:41.200><c> that</c><00:01:41.600><c> is</c>

00:01:41.830 --> 00:01:41.840 align:start position:0%
just just having to deal with that is
 

00:01:41.840 --> 00:01:43.270 align:start position:0%
just just having to deal with that is
like<00:01:42.240><c> it's</c><00:01:42.400><c> so</c><00:01:42.640><c> complex.</c><00:01:42.880><c> It's</c><00:01:42.960><c> like,</c><00:01:43.119><c> you</c>

00:01:43.270 --> 00:01:43.280 align:start position:0%
like it's so complex. It's like, you
 

00:01:43.280 --> 00:01:45.270 align:start position:0%
like it's so complex. It's like, you
know,<00:01:43.360><c> like</c><00:01:43.520><c> if</c><00:01:43.680><c> you</c><00:01:43.840><c> had</c><00:01:44.000><c> to</c><00:01:44.240><c> go</c><00:01:44.479><c> and</c><00:01:44.720><c> buy</c><00:01:45.040><c> the</c>

00:01:45.270 --> 00:01:45.280 align:start position:0%
know, like if you had to go and buy the
 

00:01:45.280 --> 00:01:48.950 align:start position:0%
know, like if you had to go and buy the
the<00:01:46.000><c> motor</c><00:01:46.479><c> and</c><00:01:46.799><c> the</c><00:01:47.119><c> tires</c><00:01:47.600><c> and</c><00:01:47.840><c> the</c><00:01:48.079><c> doors</c><00:01:48.640><c> to</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
the motor and the tires and the doors to
 

00:01:48.960 --> 00:01:51.270 align:start position:0%
the motor and the tires and the doors to
finally<00:01:49.360><c> assemble</c><00:01:49.840><c> your</c><00:01:50.079><c> own</c><00:01:50.240><c> car,</c><00:01:50.479><c> it's</c><00:01:50.720><c> like</c>

00:01:51.270 --> 00:01:51.280 align:start position:0%
finally assemble your own car, it's like
 

00:01:51.280 --> 00:01:53.270 align:start position:0%
finally assemble your own car, it's like
that's<00:01:51.600><c> that's</c><00:01:51.920><c> just</c><00:01:52.159><c> too</c><00:01:52.399><c> much.</c><00:01:52.720><c> You</c><00:01:52.880><c> just</c><00:01:53.119><c> go</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
that's that's just too much. You just go
 

00:01:53.280 --> 00:01:56.149 align:start position:0%
that's that's just too much. You just go
and<00:01:53.520><c> buy</c><00:01:53.680><c> a</c><00:01:53.840><c> car.</c><00:01:54.479><c> Yeah.</c><00:01:54.799><c> So,</c><00:01:55.200><c> yeah,</c><00:01:55.439><c> in</c><00:01:55.920><c> I</c>

00:01:56.149 --> 00:01:56.159 align:start position:0%
and buy a car. Yeah. So, yeah, in I
 

00:01:56.159 --> 00:01:57.990 align:start position:0%
and buy a car. Yeah. So, yeah, in I
think<00:01:56.320><c> in</c><00:01:56.479><c> many</c><00:01:56.799><c> cases</c><00:01:57.200><c> it</c><00:01:57.439><c> doesn't</c><00:01:57.680><c> really</c>

00:01:57.990 --> 00:01:58.000 align:start position:0%
think in many cases it doesn't really
 

00:01:58.000 --> 00:02:00.550 align:start position:0%
think in many cases it doesn't really
make<00:01:58.320><c> sense</c><00:01:58.799><c> to</c><00:01:59.119><c> assemble</c><00:01:59.680><c> your</c><00:01:59.920><c> own</c><00:02:00.159><c> cars</c>

00:02:00.550 --> 00:02:00.560 align:start position:0%
make sense to assemble your own cars
 

00:02:00.560 --> 00:02:02.310 align:start position:0%
make sense to assemble your own cars
when<00:02:00.880><c> your</c><00:02:01.119><c> business</c><00:02:01.360><c> is</c><00:02:01.600><c> not</c><00:02:01.840><c> assembling</c>

00:02:02.310 --> 00:02:02.320 align:start position:0%
when your business is not assembling
 

00:02:02.320 --> 00:02:04.310 align:start position:0%
when your business is not assembling
cars.<00:02:02.719><c> Your</c><00:02:02.960><c> business</c><00:02:03.280><c> is</c><00:02:03.759><c> doing</c><00:02:04.079><c> something</c>

00:02:04.310 --> 00:02:04.320 align:start position:0%
cars. Your business is doing something
 

00:02:04.320 --> 00:02:06.389 align:start position:0%
cars. Your business is doing something
to<00:02:04.640><c> ride</c><00:02:04.880><c> them</c><00:02:05.119><c> or</c><00:02:05.360><c> maybe</c><00:02:05.600><c> not</c><00:02:05.840><c> even</c><00:02:06.000><c> riding</c>

00:02:06.389 --> 00:02:06.399 align:start position:0%
to ride them or maybe not even riding
 

00:02:06.399 --> 00:02:07.990 align:start position:0%
to ride them or maybe not even riding
them.<00:02:06.719><c> Maybe</c><00:02:06.880><c> you</c><00:02:07.119><c> just</c><00:02:07.280><c> need</c><00:02:07.520><c> them</c><00:02:07.680><c> to</c><00:02:07.840><c> be</c>

00:02:07.990 --> 00:02:08.000 align:start position:0%
them. Maybe you just need them to be
 

00:02:08.000 --> 00:02:10.389 align:start position:0%
them. Maybe you just need them to be
able<00:02:08.160><c> to</c><00:02:08.479><c> get</c><00:02:08.800><c> something</c><00:02:09.119><c> across,</c><00:02:09.920><c> but</c><00:02:10.239><c> you</c>

00:02:10.389 --> 00:02:10.399 align:start position:0%
able to get something across, but you
 

00:02:10.399 --> 00:02:12.070 align:start position:0%
able to get something across, but you
need<00:02:10.560><c> a</c><00:02:10.720><c> car</c><00:02:10.879><c> to</c><00:02:11.120><c> be</c><00:02:11.280><c> able</c><00:02:11.440><c> to</c><00:02:11.520><c> do</c><00:02:11.680><c> that.</c><00:02:11.920><c> And</c>

00:02:12.070 --> 00:02:12.080 align:start position:0%
need a car to be able to do that. And
 

00:02:12.080 --> 00:02:14.470 align:start position:0%
need a car to be able to do that. And
like<00:02:12.319><c> Yeah.</c><00:02:12.560><c> Like</c><00:02:12.720><c> I</c><00:02:12.959><c> feel</c><00:02:13.360><c> for</c><00:02:13.680><c> many</c><00:02:14.000><c> many</c><00:02:14.239><c> use</c>

00:02:14.470 --> 00:02:14.480 align:start position:0%
like Yeah. Like I feel for many many use
 

00:02:14.480 --> 00:02:16.550 align:start position:0%
like Yeah. Like I feel for many many use
cases<00:02:15.040><c> being</c><00:02:15.280><c> able</c><00:02:15.440><c> to</c><00:02:15.599><c> just</c><00:02:15.840><c> like</c><00:02:16.080><c> get</c><00:02:16.319><c> the</c>

00:02:16.550 --> 00:02:16.560 align:start position:0%
cases being able to just like get the
 

00:02:16.560 --> 00:02:19.030 align:start position:0%
cases being able to just like get the
car<00:02:16.720><c> and</c><00:02:17.040><c> like</c><00:02:17.760><c> not</c><00:02:18.160><c> having</c><00:02:18.480><c> to</c><00:02:18.720><c> deal</c><00:02:18.879><c> with</c>

00:02:19.030 --> 00:02:19.040 align:start position:0%
car and like not having to deal with
 

00:02:19.040 --> 00:02:21.910 align:start position:0%
car and like not having to deal with
that<00:02:19.440><c> makes</c><00:02:19.760><c> makes</c><00:02:20.080><c> total</c><00:02:20.400><c> sense.</c><00:02:21.280><c> Yeah.</c><00:02:21.680><c> You</c>

00:02:21.910 --> 00:02:21.920 align:start position:0%
that makes makes total sense. Yeah. You
 

00:02:21.920 --> 00:02:24.070 align:start position:0%
that makes makes total sense. Yeah. You
don't<00:02:22.080><c> even</c><00:02:22.319><c> need</c><00:02:22.480><c> to</c><00:02:22.720><c> create</c><00:02:23.120><c> an</c><00:02:23.440><c> Uber</c><00:02:23.840><c> or</c>

00:02:24.070 --> 00:02:24.080 align:start position:0%
don't even need to create an Uber or
 

00:02:24.080 --> 00:02:25.750 align:start position:0%
don't even need to create an Uber or
you're<00:02:24.239><c> not</c><00:02:24.400><c> even</c><00:02:24.560><c> an</c><00:02:24.720><c> Uber</c><00:02:25.040><c> driver</c><00:02:25.520><c> using</c>

00:02:25.750 --> 00:02:25.760 align:start position:0%
you're not even an Uber driver using
 

00:02:25.760 --> 00:02:27.589 align:start position:0%
you're not even an Uber driver using
your<00:02:26.000><c> car</c><00:02:26.160><c> every</c><00:02:26.400><c> day.</c><00:02:26.640><c> It's</c><00:02:26.879><c> just</c><00:02:27.040><c> that</c>

00:02:27.589 --> 00:02:27.599 align:start position:0%
your car every day. It's just that
 

00:02:27.599 --> 00:02:30.150 align:start position:0%
your car every day. It's just that
you're<00:02:28.000><c> getting</c><00:02:28.239><c> in</c><00:02:28.720><c> a</c><00:02:29.040><c> car</c><00:02:29.280><c> and</c><00:02:29.599><c> it's</c><00:02:29.920><c> taking</c>

00:02:30.150 --> 00:02:30.160 align:start position:0%
you're getting in a car and it's taking
 

00:02:30.160 --> 00:02:32.790 align:start position:0%
you're getting in a car and it's taking
you<00:02:30.319><c> from</c><00:02:30.560><c> point</c><00:02:30.720><c> A</c><00:02:30.879><c> to</c><00:02:31.040><c> point</c><00:02:31.280><c> B.</c><00:02:32.000><c> I</c><00:02:32.400><c> like</c><00:02:32.560><c> that</c>

00:02:32.790 --> 00:02:32.800 align:start position:0%
you from point A to point B. I like that
 

00:02:32.800 --> 00:02:35.910 align:start position:0%
you from point A to point B. I like that
metaphor<00:02:33.680><c> because</c><00:02:34.319><c> you</c>

00:02:35.910 --> 00:02:35.920 align:start position:0%
metaphor because you
 

00:02:35.920 --> 00:02:39.350 align:start position:0%
metaphor because you
at<00:02:36.160><c> the</c><00:02:36.319><c> end</c><00:02:36.400><c> of</c><00:02:36.480><c> the</c><00:02:36.640><c> day</c><00:02:36.959><c> everybody</c><00:02:37.760><c> needs</c>

00:02:39.350 --> 00:02:39.360 align:start position:0%
at the end of the day everybody needs
 

00:02:39.360 --> 00:02:43.670 align:start position:0%
at the end of the day everybody needs
to<00:02:40.239><c> use</c><00:02:40.400><c> a</c><00:02:40.640><c> car</c><00:02:40.879><c> or</c><00:02:41.280><c> use</c><00:02:41.519><c> a</c><00:02:41.840><c> API</c><00:02:43.040><c> in</c><00:02:43.440><c> their</c>

00:02:43.670 --> 00:02:43.680 align:start position:0%
to use a car or use a API in their
 

00:02:43.680 --> 00:02:46.470 align:start position:0%
to use a car or use a API in their
software<00:02:44.080><c> that</c><00:02:44.319><c> they're</c><00:02:44.560><c> building.</c><00:02:45.120><c> And</c><00:02:45.360><c> so</c>

00:02:46.470 --> 00:02:46.480 align:start position:0%
software that they're building. And so
 

00:02:46.480 --> 00:02:50.150 align:start position:0%
software that they're building. And so
how<00:02:46.720><c> can</c><00:02:46.879><c> you</c><00:02:47.120><c> make</c><00:02:47.360><c> that</c><00:02:47.680><c> the</c><00:02:48.000><c> least</c><00:02:48.560><c> friction</c>

00:02:50.150 --> 00:02:50.160 align:start position:0%
how can you make that the least friction
 

00:02:50.160 --> 00:02:52.949 align:start position:0%
how can you make that the least friction
or<00:02:51.280><c> the</c>

00:02:52.949 --> 00:02:52.959 align:start position:0%
or the
 

00:02:52.959 --> 00:02:55.030 align:start position:0%
or the
easiest

00:02:55.030 --> 00:02:55.040 align:start position:0%
easiest
 

00:02:55.040 --> 00:02:57.589 align:start position:0%
easiest
for<00:02:55.360><c> getting</c><00:02:55.680><c> that</c><00:02:55.920><c> outcome</c><00:02:56.400><c> that</c><00:02:56.959><c> someone</c><00:02:57.360><c> is</c>

00:02:57.589 --> 00:02:57.599 align:start position:0%
for getting that outcome that someone is
 

00:02:57.599 --> 00:03:00.309 align:start position:0%
for getting that outcome that someone is
looking<00:02:57.840><c> for?</c><00:02:58.800><c> Yeah.</c><00:02:59.280><c> Exactly.</c><00:02:59.840><c> Exactly.</c><00:03:00.080><c> And</c>

00:03:00.309 --> 00:03:00.319 align:start position:0%
looking for? Yeah. Exactly. Exactly. And
 

00:03:00.319 --> 00:03:01.670 align:start position:0%
looking for? Yeah. Exactly. Exactly. And
you<00:03:00.480><c> know</c><00:03:00.560><c> like</c><00:03:00.720><c> that</c><00:03:00.879><c> was</c><00:03:01.040><c> the</c><00:03:01.200><c> spirit</c><00:03:01.519><c> of</c>

00:03:01.670 --> 00:03:01.680 align:start position:0%
you know like that was the spirit of
 

00:03:01.680 --> 00:03:04.790 align:start position:0%
you know like that was the spirit of
fast<00:03:02.000><c> API.</c><00:03:02.720><c> How</c><00:03:02.959><c> can</c><00:03:03.120><c> you</c><00:03:03.280><c> make</c><00:03:03.519><c> it</c><00:03:03.680><c> so</c><00:03:03.920><c> that</c>

00:03:04.790 --> 00:03:04.800 align:start position:0%
fast API. How can you make it so that
 

00:03:04.800 --> 00:03:07.509 align:start position:0%
fast API. How can you make it so that
you<00:03:05.040><c> just</c><00:03:05.360><c> write</c><00:03:05.920><c> the</c><00:03:06.239><c> code</c><00:03:06.560><c> in</c><00:03:06.800><c> the</c><00:03:07.120><c> way</c><00:03:07.280><c> that</c>

00:03:07.509 --> 00:03:07.519 align:start position:0%
you just write the code in the way that
 

00:03:07.519 --> 00:03:10.149 align:start position:0%
you just write the code in the way that
will<00:03:07.760><c> be</c><00:03:08.080><c> the</c><00:03:08.400><c> most</c><00:03:08.640><c> intuitive</c><00:03:09.200><c> to</c><00:03:09.440><c> you</c><00:03:09.920><c> and</c>

00:03:10.149 --> 00:03:10.159 align:start position:0%
will be the most intuitive to you and
 

00:03:10.159 --> 00:03:12.149 align:start position:0%
will be the most intuitive to you and
then<00:03:10.400><c> it</c><00:03:10.640><c> does</c><00:03:10.800><c> the</c><00:03:11.040><c> right</c><00:03:11.280><c> thing</c><00:03:11.599><c> and</c><00:03:11.920><c> does</c>

00:03:12.149 --> 00:03:12.159 align:start position:0%
then it does the right thing and does
 

00:03:12.159 --> 00:03:13.990 align:start position:0%
then it does the right thing and does
all<00:03:12.319><c> the</c><00:03:12.560><c> stuff</c><00:03:12.720><c> that</c><00:03:12.959><c> you</c><00:03:13.120><c> need</c><00:03:13.200><c> it</c><00:03:13.440><c> to</c><00:03:13.599><c> do</c>

00:03:13.990 --> 00:03:14.000 align:start position:0%
all the stuff that you need it to do
 

00:03:14.000 --> 00:03:16.550 align:start position:0%
all the stuff that you need it to do
underneath<00:03:14.800><c> by</c><00:03:15.040><c> default</c><00:03:15.599><c> data</c><00:03:16.000><c> validation</c>

00:03:16.550 --> 00:03:16.560 align:start position:0%
underneath by default data validation
 

00:03:16.560 --> 00:03:18.229 align:start position:0%
underneath by default data validation
documentation<00:03:17.200><c> everything</c><00:03:17.440><c> is</c><00:03:17.599><c> just</c><00:03:17.760><c> done</c><00:03:18.000><c> by</c>

00:03:18.229 --> 00:03:18.239 align:start position:0%
documentation everything is just done by
 

00:03:18.239 --> 00:03:20.949 align:start position:0%
documentation everything is just done by
default<00:03:19.040><c> and</c><00:03:19.360><c> you</c><00:03:19.519><c> it</c><00:03:19.760><c> just</c><00:03:19.920><c> gets</c><00:03:20.400><c> out</c><00:03:20.640><c> of</c><00:03:20.800><c> the</c>

00:03:20.949 --> 00:03:20.959 align:start position:0%
default and you it just gets out of the
 

00:03:20.959 --> 00:03:23.750 align:start position:0%
default and you it just gets out of the
way<00:03:21.040><c> so</c><00:03:21.280><c> you</c><00:03:21.440><c> can</c><00:03:21.519><c> like</c><00:03:21.680><c> focus</c><00:03:22.000><c> on</c><00:03:22.640><c> your</c><00:03:22.959><c> code</c>

00:03:23.750 --> 00:03:23.760 align:start position:0%
way so you can like focus on your code
 

00:03:23.760 --> 00:03:25.509 align:start position:0%
way so you can like focus on your code
you<00:03:23.920><c> know</c><00:03:24.000><c> like</c><00:03:24.159><c> when</c><00:03:24.319><c> you</c><00:03:24.480><c> see</c><00:03:24.640><c> a</c><00:03:24.879><c> fast</c><00:03:25.120><c> API</c>

00:03:25.509 --> 00:03:25.519 align:start position:0%
you know like when you see a fast API
 

00:03:25.519 --> 00:03:29.750 align:start position:0%
you know like when you see a fast API
application<00:03:26.720><c> h</c><00:03:27.519><c> the</c><00:03:27.840><c> amount</c><00:03:28.080><c> of</c><00:03:28.319><c> fast</c><00:03:29.200><c> API</c>

00:03:29.750 --> 00:03:29.760 align:start position:0%
application h the amount of fast API
 

00:03:29.760 --> 00:03:31.910 align:start position:0%
application h the amount of fast API
code<00:03:30.000><c> is</c><00:03:30.239><c> minimal</c><00:03:30.799><c> is</c><00:03:30.959><c> it</c><00:03:31.200><c> mostly</c><00:03:31.599><c> is</c><00:03:31.760><c> just</c>

00:03:31.910 --> 00:03:31.920 align:start position:0%
code is minimal is it mostly is just
 

00:03:31.920 --> 00:03:34.070 align:start position:0%
code is minimal is it mostly is just
like<00:03:32.159><c> the</c><00:03:32.400><c> actual</c><00:03:33.040><c> code</c><00:03:33.360><c> of</c><00:03:33.599><c> the</c><00:03:33.760><c> business</c>

00:03:34.070 --> 00:03:34.080 align:start position:0%
like the actual code of the business
 

00:03:34.080 --> 00:03:36.070 align:start position:0%
like the actual code of the business
logic.<00:03:34.720><c> That</c><00:03:34.879><c> is</c><00:03:34.959><c> the</c><00:03:35.120><c> idea</c><00:03:35.440><c> to</c><00:03:35.599><c> try</c><00:03:35.760><c> to</c><00:03:35.920><c> make</c>

00:03:36.070 --> 00:03:36.080 align:start position:0%
logic. That is the idea to try to make
 

00:03:36.080 --> 00:03:39.589 align:start position:0%
logic. That is the idea to try to make
it<00:03:36.319><c> super</c><00:03:36.799><c> simple</c><00:03:37.360><c> to</c><00:03:37.840><c> use</c><00:03:38.080><c> the</c><00:03:38.400><c> tool</c><00:03:39.120><c> and</c><00:03:39.440><c> you</c>

00:03:39.589 --> 00:03:39.599 align:start position:0%
it super simple to use the tool and you
 

00:03:39.599 --> 00:03:42.390 align:start position:0%
it super simple to use the tool and you
know<00:03:39.760><c> like</c><00:03:40.720><c> to</c><00:03:40.959><c> do</c><00:03:41.200><c> not</c><00:03:41.440><c> get</c><00:03:41.599><c> in</c><00:03:41.840><c> the</c><00:03:42.000><c> way</c><00:03:42.159><c> of</c>

00:03:42.390 --> 00:03:42.400 align:start position:0%
know like to do not get in the way of
 

00:03:42.400 --> 00:03:43.670 align:start position:0%
know like to do not get in the way of
building<00:03:42.720><c> the</c><00:03:42.879><c> thing</c><00:03:42.959><c> that</c><00:03:43.200><c> you</c><00:03:43.360><c> need</c><00:03:43.519><c> to</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
building the thing that you need to
 

00:03:43.680 --> 00:03:45.320 align:start position:0%
building the thing that you need to
build.

00:03:45.320 --> 00:03:45.330 align:start position:0%
build.
 

00:03:45.330 --> 00:03:58.419 align:start position:0%
build.
[Music]

