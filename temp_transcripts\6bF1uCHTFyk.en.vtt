WEBVTT
Kind: captions
Language: en

00:00:00.919 --> 00:00:04.190 align:start position:0%
 
where<00:00:01.199><c> does</c><00:00:01.640><c> olama</c><00:00:02.280><c> store</c><00:00:02.919><c> the</c><00:00:03.080><c> models</c><00:00:04.080><c> that's</c>

00:00:04.190 --> 00:00:04.200 align:start position:0%
where does olama store the models that's
 

00:00:04.200 --> 00:00:06.430 align:start position:0%
where does olama store the models that's
a<00:00:04.359><c> common</c><00:00:04.720><c> question</c><00:00:05.000><c> in</c><00:00:05.120><c> the</c><00:00:05.279><c> Discord</c><00:00:06.160><c> but</c>

00:00:06.430 --> 00:00:06.440 align:start position:0%
a common question in the Discord but
 

00:00:06.440 --> 00:00:08.910 align:start position:0%
a common question in the Discord but
when<00:00:06.640><c> folks</c><00:00:07.000><c> find</c><00:00:07.240><c> out</c><00:00:07.839><c> they're</c><00:00:08.160><c> even</c><00:00:08.559><c> more</c>

00:00:08.910 --> 00:00:08.920 align:start position:0%
when folks find out they're even more
 

00:00:08.920 --> 00:00:11.430 align:start position:0%
when folks find out they're even more
confused<00:00:09.760><c> than</c><00:00:09.960><c> ever</c><00:00:10.759><c> just</c><00:00:10.960><c> take</c><00:00:11.080><c> a</c><00:00:11.160><c> look</c><00:00:11.320><c> at</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
confused than ever just take a look at
 

00:00:11.440 --> 00:00:13.430 align:start position:0%
confused than ever just take a look at
this<00:00:11.599><c> directory</c><00:00:12.360><c> do</c><00:00:12.519><c> you</c><00:00:12.639><c> know</c><00:00:12.880><c> what's</c><00:00:13.200><c> in</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
this directory do you know what's in
 

00:00:13.440 --> 00:00:18.630 align:start position:0%
this directory do you know what's in
here<00:00:14.240><c> what's</c><00:00:14.879><c> this</c><00:00:15.759><c> versus</c><00:00:16.279><c> this</c><00:00:17.080><c> or</c><00:00:17.400><c> or</c><00:00:17.720><c> this</c>

00:00:18.630 --> 00:00:18.640 align:start position:0%
here what's this versus this or or this
 

00:00:18.640 --> 00:00:21.269 align:start position:0%
here what's this versus this or or this
well<00:00:18.800><c> the</c><00:00:18.920><c> way</c><00:00:19.039><c> to</c><00:00:19.240><c> figure</c><00:00:19.480><c> it</c><00:00:19.640><c> out</c><00:00:20.480><c> is</c><00:00:20.880><c> by</c>

00:00:21.269 --> 00:00:21.279 align:start position:0%
well the way to figure it out is by
 

00:00:21.279 --> 00:00:24.630 align:start position:0%
well the way to figure it out is by
looking<00:00:21.600><c> at</c><00:00:21.840><c> the</c><00:00:22.039><c> olama</c><00:00:23.080><c> Manifest</c><00:00:24.080><c> olama</c><00:00:24.519><c> is</c>

00:00:24.630 --> 00:00:24.640 align:start position:0%
looking at the olama Manifest olama is
 

00:00:24.640 --> 00:00:26.750 align:start position:0%
looking at the olama Manifest olama is
available<00:00:25.080><c> on</c><00:00:25.279><c> two</c><00:00:25.519><c> different</c><00:00:25.840><c> platforms</c><00:00:26.400><c> at</c>

00:00:26.750 --> 00:00:26.760 align:start position:0%
available on two different platforms at
 

00:00:26.760 --> 00:00:29.710 align:start position:0%
available on two different platforms at
least<00:00:27.279><c> today</c><00:00:27.880><c> as</c><00:00:28.000><c> of</c><00:00:28.240><c> this</c><00:00:28.400><c> recording</c><00:00:29.199><c> Linux</c>

00:00:29.710 --> 00:00:29.720 align:start position:0%
least today as of this recording Linux
 

00:00:29.720 --> 00:00:33.310 align:start position:0%
least today as of this recording Linux
and<00:00:30.039><c> Mac</c><00:00:30.720><c> Windows</c><00:00:31.240><c> is</c><00:00:31.439><c> coming</c><00:00:32.000><c> soon</c><00:00:32.640><c> really</c>

00:00:33.310 --> 00:00:33.320 align:start position:0%
and Mac Windows is coming soon really
 

00:00:33.320 --> 00:00:35.430 align:start position:0%
and Mac Windows is coming soon really
you<00:00:33.520><c> have</c><00:00:33.920><c> my</c><00:00:34.239><c> word</c><00:00:34.440><c> on</c><00:00:34.559><c> it</c><00:00:34.760><c> actively</c><00:00:35.200><c> being</c>

00:00:35.430 --> 00:00:35.440 align:start position:0%
you have my word on it actively being
 

00:00:35.440 --> 00:00:38.190 align:start position:0%
you have my word on it actively being
worked<00:00:35.719><c> on</c><00:00:36.520><c> so</c><00:00:36.800><c> we</c><00:00:36.920><c> can</c><00:00:37.079><c> find</c><00:00:37.399><c> all</c><00:00:37.640><c> the</c><00:00:37.760><c> files</c>

00:00:38.190 --> 00:00:38.200 align:start position:0%
worked on so we can find all the files
 

00:00:38.200 --> 00:00:40.549 align:start position:0%
worked on so we can find all the files
olama<00:00:38.680><c> uses</c><00:00:39.360><c> in</c><00:00:39.520><c> one</c><00:00:39.640><c> of</c><00:00:39.879><c> two</c><00:00:40.200><c> different</c>

00:00:40.549 --> 00:00:40.559 align:start position:0%
olama uses in one of two different
 

00:00:40.559 --> 00:00:42.990 align:start position:0%
olama uses in one of two different
places<00:00:41.320><c> on</c><00:00:41.600><c> Mac</c><00:00:42.039><c> you</c><00:00:42.160><c> can</c><00:00:42.280><c> find</c><00:00:42.480><c> it</c><00:00:42.640><c> under</c><00:00:42.920><c> the</c>

00:00:42.990 --> 00:00:43.000 align:start position:0%
places on Mac you can find it under the
 

00:00:43.000 --> 00:00:46.590 align:start position:0%
places on Mac you can find it under the
user<00:00:43.320><c> root</c><00:00:44.000><c> then</c><00:00:44.559><c> olama</c><00:00:45.160><c> and</c><00:00:45.320><c> then</c><00:00:45.440><c> models</c><00:00:46.399><c> on</c>

00:00:46.590 --> 00:00:46.600 align:start position:0%
user root then olama and then models on
 

00:00:46.600 --> 00:00:49.670 align:start position:0%
user root then olama and then models on
Linux<00:00:47.199><c> it's</c><00:00:47.320><c> under</c><00:00:47.559><c> user</c><00:00:48.000><c> share</c><00:00:48.760><c> olama</c><00:00:49.480><c> and</c>

00:00:49.670 --> 00:00:49.680 align:start position:0%
Linux it's under user share olama and
 

00:00:49.680 --> 00:00:52.990 align:start position:0%
Linux it's under user share olama and
then<00:00:50.399><c> olama</c><00:00:51.120><c> and</c><00:00:51.399><c> models</c><00:00:52.399><c> so</c><00:00:52.640><c> here</c><00:00:52.760><c> you</c><00:00:52.879><c> can</c>

00:00:52.990 --> 00:00:53.000 align:start position:0%
then olama and models so here you can
 

00:00:53.000 --> 00:00:55.310 align:start position:0%
then olama and models so here you can
see<00:00:53.359><c> two</c><00:00:53.559><c> different</c><00:00:53.879><c> directories</c><00:00:54.359><c> blobs</c><00:00:55.039><c> and</c>

00:00:55.310 --> 00:00:55.320 align:start position:0%
see two different directories blobs and
 

00:00:55.320 --> 00:00:58.349 align:start position:0%
see two different directories blobs and
manifests<00:00:56.320><c> let's</c><00:00:56.480><c> go</c><00:00:56.600><c> into</c><00:00:57.079><c> Manifest</c><00:00:58.079><c> this</c><00:00:58.199><c> is</c>

00:00:58.349 --> 00:00:58.359 align:start position:0%
manifests let's go into Manifest this is
 

00:00:58.359 --> 00:01:00.830 align:start position:0%
manifests let's go into Manifest this is
organized<00:00:58.840><c> by</c><00:00:59.000><c> registry</c><00:00:59.680><c> that</c><00:01:00.079><c> organization</c>

00:01:00.830 --> 00:01:00.840 align:start position:0%
organized by registry that organization
 

00:01:00.840 --> 00:01:03.470 align:start position:0%
organized by registry that organization
then<00:01:01.079><c> model</c><00:01:01.879><c> for</c><00:01:02.120><c> now</c><00:01:02.399><c> there's</c><00:01:02.680><c> only</c><00:01:02.920><c> a</c><00:01:03.079><c> single</c>

00:01:03.470 --> 00:01:03.480 align:start position:0%
then model for now there's only a single
 

00:01:03.480 --> 00:01:06.070 align:start position:0%
then model for now there's only a single
registry<00:01:04.199><c> which</c><00:01:04.320><c> can</c><00:01:04.439><c> be</c><00:01:04.640><c> found</c><00:01:04.920><c> at</c>

00:01:06.070 --> 00:01:06.080 align:start position:0%
registry which can be found at
 

00:01:06.080 --> 00:01:09.149 align:start position:0%
registry which can be found at
ol.<00:01:07.080><c> but</c><00:01:07.200><c> at</c><00:01:07.360><c> some</c><00:01:07.560><c> point</c><00:01:07.720><c> in</c><00:01:07.880><c> the</c><00:01:08.040><c> future</c><00:01:08.799><c> more</c>

00:01:09.149 --> 00:01:09.159 align:start position:0%
ol. but at some point in the future more
 

00:01:09.159 --> 00:01:12.590 align:start position:0%
ol. but at some point in the future more
options<00:01:09.759><c> may</c><00:01:10.320><c> come</c><00:01:10.960><c> about</c><00:01:11.960><c> you</c><00:01:12.080><c> can</c><00:01:12.200><c> see</c><00:01:12.479><c> when</c>

00:01:12.590 --> 00:01:12.600 align:start position:0%
options may come about you can see when
 

00:01:12.600 --> 00:01:14.710 align:start position:0%
options may come about you can see when
I<00:01:12.759><c> look</c><00:01:13.000><c> at</c><00:01:13.159><c> the</c><00:01:13.320><c> registry</c><00:01:13.840><c> directory</c><00:01:14.560><c> there</c>

00:01:14.710 --> 00:01:14.720 align:start position:0%
I look at the registry directory there
 

00:01:14.720 --> 00:01:17.109 align:start position:0%
I look at the registry directory there
are<00:01:14.960><c> a</c><00:01:15.159><c> bunch</c><00:01:15.400><c> of</c><00:01:15.560><c> names</c><00:01:16.439><c> I</c><00:01:16.560><c> usually</c><00:01:16.840><c> post</c>

00:01:17.109 --> 00:01:17.119 align:start position:0%
are a bunch of names I usually post
 

00:01:17.119 --> 00:01:19.990 align:start position:0%
are a bunch of names I usually post
models<00:01:17.479><c> to</c><00:01:17.720><c> Matt</c><00:01:18.080><c> W</c><00:01:18.520><c> or</c><00:01:18.720><c> M</c><00:01:19.080><c> or</c><00:01:19.280><c> even</c><00:01:19.520><c> techno</c>

00:01:19.990 --> 00:01:20.000 align:start position:0%
models to Matt W or M or even techno
 

00:01:20.000 --> 00:01:22.310 align:start position:0%
models to Matt W or M or even techno
evangelist<00:01:21.000><c> but</c><00:01:21.119><c> the</c><00:01:21.280><c> official</c><00:01:21.640><c> models</c><00:01:22.159><c> are</c>

00:01:22.310 --> 00:01:22.320 align:start position:0%
evangelist but the official models are
 

00:01:22.320 --> 00:01:24.870 align:start position:0%
evangelist but the official models are
under<00:01:23.000><c> Library</c><00:01:24.000><c> so</c><00:01:24.159><c> let's</c><00:01:24.320><c> take</c><00:01:24.439><c> a</c><00:01:24.560><c> look</c><00:01:24.680><c> at</c>

00:01:24.870 --> 00:01:24.880 align:start position:0%
under Library so let's take a look at
 

00:01:24.880 --> 00:01:27.270 align:start position:0%
under Library so let's take a look at
that<00:01:25.560><c> llama</c><00:01:25.920><c> 2</c><00:01:26.200><c> is</c><00:01:26.400><c> probably</c><00:01:26.680><c> the</c><00:01:26.840><c> model</c><00:01:27.119><c> you</c>

00:01:27.270 --> 00:01:27.280 align:start position:0%
that llama 2 is probably the model you
 

00:01:27.280 --> 00:01:29.950 align:start position:0%
that llama 2 is probably the model you
started<00:01:27.680><c> with</c><00:01:28.200><c> so</c><00:01:28.479><c> let's</c><00:01:28.680><c> go</c><00:01:28.799><c> in</c><00:01:29.040><c> there</c><00:01:29.520><c> and</c>

00:01:29.950 --> 00:01:29.960 align:start position:0%
started with so let's go in there and
 

00:01:29.960 --> 00:01:32.550 align:start position:0%
started with so let's go in there and
here<00:01:30.079><c> we</c><00:01:30.200><c> have</c><00:01:30.360><c> a</c><00:01:30.520><c> file</c><00:01:31.159><c> for</c><00:01:31.479><c> each</c><00:01:31.640><c> of</c><00:01:31.799><c> the</c><00:01:32.000><c> tags</c>

00:01:32.550 --> 00:01:32.560 align:start position:0%
here we have a file for each of the tags
 

00:01:32.560 --> 00:01:35.190 align:start position:0%
here we have a file for each of the tags
that<00:01:32.720><c> we've</c><00:01:33.200><c> pulled</c><00:01:34.200><c> so</c><00:01:34.600><c> let's</c><00:01:34.799><c> take</c><00:01:34.920><c> a</c><00:01:35.079><c> look</c>

00:01:35.190 --> 00:01:35.200 align:start position:0%
that we've pulled so let's take a look
 

00:01:35.200 --> 00:01:38.310 align:start position:0%
that we've pulled so let's take a look
at<00:01:35.320><c> the</c><00:01:35.439><c> 7B</c><00:01:36.119><c> file</c><00:01:37.119><c> at</c><00:01:37.240><c> the</c><00:01:37.479><c> top</c><00:01:37.920><c> there's</c><00:01:38.119><c> a</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
at the 7B file at the top there's a
 

00:01:38.320 --> 00:01:40.910 align:start position:0%
at the 7B file at the top there's a
schema<00:01:38.759><c> version</c><00:01:39.079><c> set</c><00:01:39.320><c> to</c><00:01:39.479><c> two</c><00:01:40.399><c> you</c><00:01:40.560><c> might</c><00:01:40.759><c> be</c>

00:01:40.910 --> 00:01:40.920 align:start position:0%
schema version set to two you might be
 

00:01:40.920 --> 00:01:42.950 align:start position:0%
schema version set to two you might be
wondering<00:01:41.399><c> if</c><00:01:41.600><c> this</c><00:01:41.720><c> is</c><00:01:41.920><c> the</c><00:01:42.159><c> second</c><00:01:42.520><c> version</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
wondering if this is the second version
 

00:01:42.960 --> 00:01:46.550 align:start position:0%
wondering if this is the second version
of<00:01:43.119><c> the</c><00:01:43.240><c> AMA</c><00:01:43.840><c> manifest</c><00:01:44.399><c> schema</c><00:01:45.360><c> it's</c><00:01:45.520><c> not</c><00:01:46.320><c> it's</c>

00:01:46.550 --> 00:01:46.560 align:start position:0%
of the AMA manifest schema it's not it's
 

00:01:46.560 --> 00:01:47.990 align:start position:0%
of the AMA manifest schema it's not it's
actually<00:01:46.719><c> a</c><00:01:46.840><c> schema</c><00:01:47.200><c> defined</c><00:01:47.560><c> for</c><00:01:47.719><c> another</c>

00:01:47.990 --> 00:01:48.000 align:start position:0%
actually a schema defined for another
 

00:01:48.000 --> 00:01:50.910 align:start position:0%
actually a schema defined for another
tool<00:01:48.920><c> Docker</c><00:01:49.920><c> quickly</c><00:01:50.240><c> look</c><00:01:50.479><c> over</c><00:01:50.759><c> this</c>

00:01:50.910 --> 00:01:50.920 align:start position:0%
tool Docker quickly look over this
 

00:01:50.920 --> 00:01:53.030 align:start position:0%
tool Docker quickly look over this
format<00:01:51.560><c> of</c><00:01:51.719><c> this</c><00:01:51.920><c> manifest</c><00:01:52.600><c> then</c><00:01:52.719><c> let's</c><00:01:52.920><c> take</c>

00:01:53.030 --> 00:01:53.040 align:start position:0%
format of this manifest then let's take
 

00:01:53.040 --> 00:01:55.469 align:start position:0%
format of this manifest then let's take
a<00:01:53.200><c> quick</c><00:01:53.399><c> side</c><00:01:53.600><c> trip</c><00:01:54.119><c> to</c><00:01:54.320><c> look</c><00:01:54.479><c> at</c><00:01:54.680><c> the</c><00:01:54.880><c> olama</c>

00:01:55.469 --> 00:01:55.479 align:start position:0%
a quick side trip to look at the olama
 

00:01:55.479 --> 00:01:57.870 align:start position:0%
a quick side trip to look at the olama
docker<00:01:55.960><c> container</c><00:01:56.920><c> using</c><00:01:57.240><c> the</c><00:01:57.399><c> docker</c>

00:01:57.870 --> 00:01:57.880 align:start position:0%
docker container using the docker
 

00:01:57.880 --> 00:02:01.510 align:start position:0%
docker container using the docker
manifest<00:01:58.560><c> tool</c><00:01:59.560><c> dock</c><00:01:59.960><c> ER</c><00:02:00.119><c> manifest</c><00:02:00.880><c> inspect</c>

00:02:01.510 --> 00:02:01.520 align:start position:0%
manifest tool dock ER manifest inspect
 

00:02:01.520 --> 00:02:04.830 align:start position:0%
manifest tool dock ER manifest inspect
ol/<00:02:02.600><c> olama</c><00:02:03.600><c> does</c><00:02:03.799><c> that</c><00:02:03.960><c> look</c>

00:02:04.830 --> 00:02:04.840 align:start position:0%
ol/ olama does that look
 

00:02:04.840 --> 00:02:08.070 align:start position:0%
ol/ olama does that look
familiar<00:02:05.840><c> okay</c><00:02:06.240><c> back</c><00:02:06.360><c> to</c><00:02:06.520><c> the</c><00:02:06.640><c> olama</c><00:02:07.119><c> Manifest</c>

00:02:08.070 --> 00:02:08.080 align:start position:0%
familiar okay back to the olama Manifest
 

00:02:08.080 --> 00:02:10.190 align:start position:0%
familiar okay back to the olama Manifest
the<00:02:08.200><c> media</c><00:02:08.640><c> type</c><00:02:08.920><c> for</c><00:02:09.200><c> the</c><00:02:09.399><c> file</c><00:02:09.879><c> is</c>

00:02:10.190 --> 00:02:10.200 align:start position:0%
the media type for the file is
 

00:02:10.200 --> 00:02:12.830 align:start position:0%
the media type for the file is
application<00:02:10.879><c> vnd</c><00:02:11.560><c> Docker</c><00:02:12.080><c> distribution</c>

00:02:12.830 --> 00:02:12.840 align:start position:0%
application vnd Docker distribution
 

00:02:12.840 --> 00:02:13.949 align:start position:0%
application vnd Docker distribution
Docker

00:02:13.949 --> 00:02:13.959 align:start position:0%
Docker
 

00:02:13.959 --> 00:02:17.070 align:start position:0%
Docker
container<00:02:14.959><c> uh</c><00:02:15.160><c> image</c><00:02:15.800><c> V1</c>

00:02:17.070 --> 00:02:17.080 align:start position:0%
container uh image V1
 

00:02:17.080 --> 00:02:20.430 align:start position:0%
container uh image V1
Json<00:02:18.080><c> AMA</c><00:02:18.599><c> isn't</c><00:02:18.920><c> Docker</c><00:02:19.400><c> but</c><00:02:19.640><c> the</c><00:02:19.760><c> team</c><00:02:20.239><c> has</c>

00:02:20.430 --> 00:02:20.440 align:start position:0%
Json AMA isn't Docker but the team has
 

00:02:20.440 --> 00:02:23.150 align:start position:0%
Json AMA isn't Docker but the team has
its<00:02:20.640><c> roots</c><00:02:21.080><c> in</c><00:02:21.280><c> Docker</c><00:02:21.959><c> creating</c><00:02:22.360><c> kitematic</c>

00:02:23.150 --> 00:02:23.160 align:start position:0%
its roots in Docker creating kitematic
 

00:02:23.160 --> 00:02:24.670 align:start position:0%
its roots in Docker creating kitematic
which<00:02:23.400><c> essentially</c><00:02:23.879><c> became</c><00:02:24.160><c> Docker</c><00:02:24.480><c> for</c>

00:02:24.670 --> 00:02:24.680 align:start position:0%
which essentially became Docker for
 

00:02:24.680 --> 00:02:26.830 align:start position:0%
which essentially became Docker for
desktop<00:02:25.120><c> over</c><00:02:25.280><c> the</c><00:02:25.400><c> years</c><00:02:26.040><c> and</c><00:02:26.200><c> also</c><00:02:26.440><c> creating</c>

00:02:26.830 --> 00:02:26.840 align:start position:0%
desktop over the years and also creating
 

00:02:26.840 --> 00:02:29.830 align:start position:0%
desktop over the years and also creating
much<00:02:27.000><c> of</c><00:02:27.160><c> what</c><00:02:27.280><c> we</c><00:02:27.440><c> know</c><00:02:27.680><c> of</c><00:02:28.000><c> as</c><00:02:28.239><c> Docker</c><00:02:28.760><c> hub</c>

00:02:29.830 --> 00:02:29.840 align:start position:0%
much of what we know of as Docker hub
 

00:02:29.840 --> 00:02:31.350 align:start position:0%
much of what we know of as Docker hub
everything<00:02:30.080><c> else</c><00:02:30.239><c> in</c><00:02:30.400><c> this</c><00:02:30.560><c> file</c><00:02:30.879><c> is</c><00:02:31.040><c> going</c><00:02:31.200><c> to</c>

00:02:31.350 --> 00:02:31.360 align:start position:0%
everything else in this file is going to
 

00:02:31.360 --> 00:02:34.470 align:start position:0%
everything else in this file is going to
be<00:02:31.640><c> in</c><00:02:31.959><c> config</c><00:02:32.959><c> which</c><00:02:33.120><c> is</c><00:02:33.360><c> an</c><00:02:33.640><c> object</c><00:02:34.040><c> with</c><00:02:34.200><c> a</c>

00:02:34.470 --> 00:02:34.480 align:start position:0%
be in config which is an object with a
 

00:02:34.480 --> 00:02:37.990 align:start position:0%
be in config which is an object with a
media<00:02:34.920><c> type</c><00:02:35.440><c> digest</c><00:02:35.920><c> and</c><00:02:36.160><c> size</c><00:02:36.840><c> or</c><00:02:37.000><c> in</c><00:02:37.160><c> layers</c>

00:02:37.990 --> 00:02:38.000 align:start position:0%
media type digest and size or in layers
 

00:02:38.000 --> 00:02:40.070 align:start position:0%
media type digest and size or in layers
which<00:02:38.120><c> is</c><00:02:38.239><c> an</c><00:02:38.400><c> array</c><00:02:38.800><c> of</c><00:02:38.959><c> config</c><00:02:39.400><c> objects</c><00:02:39.840><c> each</c>

00:02:40.070 --> 00:02:40.080 align:start position:0%
which is an array of config objects each
 

00:02:40.080 --> 00:02:43.350 align:start position:0%
which is an array of config objects each
with<00:02:40.239><c> a</c><00:02:40.400><c> media</c><00:02:40.840><c> type</c><00:02:41.239><c> digest</c><00:02:41.720><c> and</c><00:02:42.159><c> size</c><00:02:43.159><c> the</c>

00:02:43.350 --> 00:02:43.360 align:start position:0%
with a media type digest and size the
 

00:02:43.360 --> 00:02:45.509 align:start position:0%
with a media type digest and size the
contents<00:02:43.879><c> of</c><00:02:44.080><c> config</c><00:02:44.680><c> aren't</c><00:02:45.200><c> that</c>

00:02:45.509 --> 00:02:45.519 align:start position:0%
contents of config aren't that
 

00:02:45.519 --> 00:02:47.630 align:start position:0%
contents of config aren't that
interesting<00:02:46.519><c> so</c><00:02:46.680><c> let's</c><00:02:46.879><c> skip</c><00:02:47.159><c> ahead</c><00:02:47.360><c> to</c><00:02:47.480><c> the</c>

00:02:47.630 --> 00:02:47.640 align:start position:0%
interesting so let's skip ahead to the
 

00:02:47.640 --> 00:02:51.030 align:start position:0%
interesting so let's skip ahead to the
layers<00:02:48.640><c> and</c><00:02:48.879><c> this</c><00:02:49.159><c> does</c><00:02:49.480><c> get</c><00:02:49.920><c> interesting</c><00:02:50.920><c> I'm</c>

00:02:51.030 --> 00:02:51.040 align:start position:0%
layers and this does get interesting I'm
 

00:02:51.040 --> 00:02:53.149 align:start position:0%
layers and this does get interesting I'm
not<00:02:51.200><c> sure</c><00:02:51.400><c> if</c><00:02:51.560><c> the</c><00:02:51.800><c> order</c><00:02:52.080><c> of</c><00:02:52.280><c> layers</c><00:02:52.680><c> matters</c>

00:02:53.149 --> 00:02:53.159 align:start position:0%
not sure if the order of layers matters
 

00:02:53.159 --> 00:02:55.750 align:start position:0%
not sure if the order of layers matters
much<00:02:53.800><c> but</c><00:02:53.959><c> the</c><00:02:54.120><c> first</c><00:02:54.400><c> layer</c><00:02:54.680><c> you</c><00:02:54.879><c> usually</c><00:02:55.200><c> see</c>

00:02:55.750 --> 00:02:55.760 align:start position:0%
much but the first layer you usually see
 

00:02:55.760 --> 00:02:59.550 align:start position:0%
much but the first layer you usually see
has<00:02:55.879><c> a</c><00:02:56.000><c> media</c><00:02:56.440><c> type</c><00:02:56.680><c> of</c><00:02:56.920><c> olama</c><00:02:57.879><c> image</c><00:02:58.560><c> model</c>

00:02:59.550 --> 00:02:59.560 align:start position:0%
has a media type of olama image model
 

00:02:59.560 --> 00:03:02.030 align:start position:0%
has a media type of olama image model
and<00:02:59.760><c> and</c><00:02:59.920><c> comparing</c><00:03:00.480><c> the</c><00:03:00.720><c> size</c><00:03:01.000><c> of</c><00:03:01.239><c> this</c><00:03:01.400><c> layer</c>

00:03:02.030 --> 00:03:02.040 align:start position:0%
and and comparing the size of this layer
 

00:03:02.040 --> 00:03:04.869 align:start position:0%
and and comparing the size of this layer
to<00:03:02.239><c> the</c><00:03:02.400><c> others</c><00:03:03.239><c> this</c><00:03:03.400><c> is</c><00:03:03.560><c> the</c><00:03:03.720><c> biggest</c><00:03:04.360><c> digest</c>

00:03:04.869 --> 00:03:04.879 align:start position:0%
to the others this is the biggest digest
 

00:03:04.879 --> 00:03:07.789 align:start position:0%
to the others this is the biggest digest
is<00:03:05.040><c> the</c><00:03:05.159><c> Sha</c><00:03:05.480><c> 256</c><00:03:06.120><c> digest</c><00:03:06.519><c> of</c><00:03:06.720><c> the</c><00:03:06.879><c> file</c><00:03:07.720><c> you</c>

00:03:07.789 --> 00:03:07.799 align:start position:0%
is the Sha 256 digest of the file you
 

00:03:07.799 --> 00:03:10.390 align:start position:0%
is the Sha 256 digest of the file you
can<00:03:07.959><c> think</c><00:03:08.120><c> of</c><00:03:08.319><c> a</c><00:03:08.440><c> sha</c><00:03:08.799><c> 256</c><00:03:09.480><c> digest</c><00:03:10.000><c> as</c><00:03:10.120><c> being</c>

00:03:10.390 --> 00:03:10.400 align:start position:0%
can think of a sha 256 digest as being
 

00:03:10.400 --> 00:03:12.589 align:start position:0%
can think of a sha 256 digest as being
like<00:03:10.519><c> a</c><00:03:10.760><c> fingerprint</c><00:03:11.319><c> for</c><00:03:11.480><c> the</c><00:03:11.640><c> file</c><00:03:12.440><c> and</c>

00:03:12.589 --> 00:03:12.599 align:start position:0%
like a fingerprint for the file and
 

00:03:12.599 --> 00:03:14.589 align:start position:0%
like a fingerprint for the file and
those<00:03:12.799><c> fingerprints</c><00:03:13.599><c> are</c><00:03:13.840><c> actually</c><00:03:14.080><c> the</c><00:03:14.319><c> file</c>

00:03:14.589 --> 00:03:14.599 align:start position:0%
those fingerprints are actually the file
 

00:03:14.599 --> 00:03:18.830 align:start position:0%
those fingerprints are actually the file
names<00:03:15.200><c> that</c><00:03:15.280><c> you</c><00:03:15.400><c> can</c><00:03:15.720><c> find</c><00:03:16.080><c> in</c><00:03:16.799><c> l/m</c><00:03:17.799><c> models</c>

00:03:18.830 --> 00:03:18.840 align:start position:0%
names that you can find in l/m models
 

00:03:18.840 --> 00:03:21.350 align:start position:0%
names that you can find in l/m models
blobs<00:03:19.840><c> so</c><00:03:20.000><c> the</c><00:03:20.080><c> model</c><00:03:20.440><c> digest</c><00:03:20.879><c> starts</c><00:03:21.200><c> with</c>

00:03:21.350 --> 00:03:21.360 align:start position:0%
blobs so the model digest starts with
 

00:03:21.360 --> 00:03:25.990 align:start position:0%
blobs so the model digest starts with
Shaw<00:03:21.720><c> 256</c><00:03:22.519><c> colon</c><00:03:23.480><c> 8934</c><00:03:24.480><c> D</c><00:03:25.280><c> and</c><00:03:25.400><c> we</c><00:03:25.519><c> can</c><00:03:25.720><c> find</c>

00:03:25.990 --> 00:03:26.000 align:start position:0%
Shaw 256 colon 8934 D and we can find
 

00:03:26.000 --> 00:03:27.990 align:start position:0%
Shaw 256 colon 8934 D and we can find
that<00:03:26.159><c> file</c><00:03:26.360><c> in</c><00:03:26.440><c> the</c><00:03:26.560><c> blobs</c><00:03:26.879><c> directory</c><00:03:27.599><c> it's</c><00:03:27.720><c> a</c>

00:03:27.990 --> 00:03:28.000 align:start position:0%
that file in the blobs directory it's a
 

00:03:28.000 --> 00:03:30.390 align:start position:0%
that file in the blobs directory it's a
huge<00:03:28.360><c> file</c><00:03:28.720><c> so</c><00:03:28.920><c> we</c><00:03:29.000><c> won't</c><00:03:29.200><c> be</c><00:03:29.280><c> able</c><00:03:29.439><c> to</c><00:03:29.760><c> open</c><00:03:29.959><c> it</c>

00:03:30.390 --> 00:03:30.400 align:start position:0%
huge file so we won't be able to open it
 

00:03:30.400 --> 00:03:33.149 align:start position:0%
huge file so we won't be able to open it
in<00:03:30.599><c> vs</c><00:03:31.000><c> code</c><00:03:32.000><c> so</c><00:03:32.159><c> let's</c><00:03:32.360><c> move</c><00:03:32.560><c> on</c><00:03:32.680><c> to</c><00:03:32.840><c> the</c><00:03:32.959><c> next</c>

00:03:33.149 --> 00:03:33.159 align:start position:0%
in vs code so let's move on to the next
 

00:03:33.159 --> 00:03:36.869 align:start position:0%
in vs code so let's move on to the next
layer<00:03:33.760><c> it</c><00:03:33.879><c> has</c><00:03:34.000><c> a</c><00:03:34.159><c> media</c><00:03:34.599><c> type</c><00:03:34.879><c> of</c><00:03:35.200><c> olama</c><00:03:36.080><c> image</c>

00:03:36.869 --> 00:03:36.879 align:start position:0%
layer it has a media type of olama image
 

00:03:36.879 --> 00:03:40.190 align:start position:0%
layer it has a media type of olama image
license<00:03:37.879><c> the</c><00:03:38.040><c> digest</c><00:03:38.439><c> starts</c><00:03:38.720><c> with</c><00:03:38.879><c> sha</c><00:03:39.239><c> 256</c>

00:03:40.190 --> 00:03:40.200 align:start position:0%
license the digest starts with sha 256
 

00:03:40.200 --> 00:03:44.190 align:start position:0%
license the digest starts with sha 256
colon<00:03:40.599><c> 8</c><00:03:41.280><c> c17</c><00:03:42.400><c> C2</c><00:03:43.400><c> so</c><00:03:43.560><c> let's</c><00:03:43.720><c> take</c><00:03:43.840><c> a</c><00:03:43.959><c> look</c><00:03:44.080><c> at</c>

00:03:44.190 --> 00:03:44.200 align:start position:0%
colon 8 c17 C2 so let's take a look at
 

00:03:44.200 --> 00:03:47.309 align:start position:0%
colon 8 c17 C2 so let's take a look at
that<00:03:44.360><c> file</c><00:03:44.840><c> and</c><00:03:44.959><c> if</c><00:03:45.080><c> we</c><00:03:45.200><c> run</c><00:03:45.480><c> ol</c><00:03:45.760><c> llama</c><00:03:46.319><c> show--</c>

00:03:47.309 --> 00:03:47.319 align:start position:0%
that file and if we run ol llama show--
 

00:03:47.319 --> 00:03:51.270 align:start position:0%
that file and if we run ol llama show--
license<00:03:47.840><c> llama</c><00:03:48.640><c> 27b</c><00:03:49.640><c> we</c><00:03:49.760><c> see</c><00:03:50.000><c> the</c><00:03:50.120><c> same</c><00:03:50.400><c> text</c>

00:03:51.270 --> 00:03:51.280 align:start position:0%
license llama 27b we see the same text
 

00:03:51.280 --> 00:03:53.710 align:start position:0%
license llama 27b we see the same text
as<00:03:51.519><c> compelling</c><00:03:52.200><c> and</c><00:03:52.519><c> exciting</c><00:03:52.959><c> as</c><00:03:53.200><c> license</c>

00:03:53.710 --> 00:03:53.720 align:start position:0%
as compelling and exciting as license
 

00:03:53.720 --> 00:03:56.190 align:start position:0%
as compelling and exciting as license
agreements<00:03:54.319><c> can</c><00:03:54.599><c> be</c><00:03:55.400><c> let's</c><00:03:55.640><c> move</c><00:03:55.840><c> on</c><00:03:56.000><c> to</c>

00:03:56.190 --> 00:03:56.200 align:start position:0%
agreements can be let's move on to
 

00:03:56.200 --> 00:03:58.309 align:start position:0%
agreements can be let's move on to
something<00:03:56.560><c> potentially</c><00:03:57.000><c> more</c><00:03:57.319><c> interesting</c>

00:03:58.309 --> 00:03:58.319 align:start position:0%
something potentially more interesting
 

00:03:58.319 --> 00:04:00.229 align:start position:0%
something potentially more interesting
the<00:03:58.519><c> digest</c><00:03:58.920><c> for</c><00:03:59.120><c> the</c><00:03:59.239><c> templates</c><00:03:59.720><c> starts</c><00:04:00.040><c> with</c>

00:04:00.229 --> 00:04:00.239 align:start position:0%
the digest for the templates starts with
 

00:04:00.239 --> 00:04:03.550 align:start position:0%
the digest for the templates starts with
sha<00:04:00.680><c> 256</c><00:04:01.560><c> colon</c>

00:04:03.550 --> 00:04:03.560 align:start position:0%
sha 256 colon
 

00:04:03.560 --> 00:04:06.830 align:start position:0%
sha 256 colon
2493<00:04:04.560><c> and</c><00:04:04.799><c> there</c><00:04:05.000><c> we</c><00:04:05.159><c> see</c><00:04:05.480><c> the</c><00:04:05.599><c> template</c><00:04:06.599><c> now</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
2493 and there we see the template now
 

00:04:06.840 --> 00:04:09.550 align:start position:0%
2493 and there we see the template now
take<00:04:06.959><c> a</c><00:04:07.120><c> look</c><00:04:07.280><c> at</c><00:04:07.400><c> the</c><00:04:07.640><c> Manifest</c><00:04:08.200><c> for</c><00:04:08.560><c> llama</c><00:04:08.959><c> 2</c>

00:04:09.550 --> 00:04:09.560 align:start position:0%
take a look at the Manifest for llama 2
 

00:04:09.560 --> 00:04:12.869 align:start position:0%
take a look at the Manifest for llama 2
13B<00:04:10.400><c> model</c><00:04:11.239><c> scroll</c><00:04:11.640><c> down</c><00:04:11.799><c> to</c><00:04:11.959><c> the</c><00:04:12.120><c> template</c>

00:04:12.869 --> 00:04:12.879 align:start position:0%
13B model scroll down to the template
 

00:04:12.879 --> 00:04:15.350 align:start position:0%
13B model scroll down to the template
and<00:04:13.000><c> it's</c><00:04:13.200><c> the</c><00:04:13.439><c> same</c><00:04:13.799><c> template</c><00:04:14.239><c> digest</c><00:04:15.200><c> which</c>

00:04:15.350 --> 00:04:15.360 align:start position:0%
and it's the same template digest which
 

00:04:15.360 --> 00:04:17.710 align:start position:0%
and it's the same template digest which
means<00:04:16.079><c> it's</c><00:04:16.280><c> using</c><00:04:16.639><c> the</c><00:04:16.840><c> same</c><00:04:17.199><c> file</c><00:04:17.479><c> in</c><00:04:17.600><c> the</c>

00:04:17.710 --> 00:04:17.720 align:start position:0%
means it's using the same file in the
 

00:04:17.720 --> 00:04:20.229 align:start position:0%
means it's using the same file in the
blobs<00:04:18.280><c> directory</c><00:04:19.280><c> now</c><00:04:19.440><c> here's</c><00:04:19.639><c> a</c><00:04:19.799><c> command</c><00:04:20.160><c> we</c>

00:04:20.229 --> 00:04:20.239 align:start position:0%
blobs directory now here's a command we
 

00:04:20.239 --> 00:04:22.550 align:start position:0%
blobs directory now here's a command we
can<00:04:20.440><c> use</c><00:04:20.680><c> to</c><00:04:20.799><c> see</c><00:04:21.160><c> all</c><00:04:21.400><c> the</c><00:04:21.560><c> manifests</c><00:04:22.440><c> that</c>

00:04:22.550 --> 00:04:22.560 align:start position:0%
can use to see all the manifests that
 

00:04:22.560 --> 00:04:24.830 align:start position:0%
can use to see all the manifests that
use<00:04:22.840><c> the</c><00:04:22.960><c> same</c><00:04:23.240><c> template</c><00:04:24.240><c> and</c><00:04:24.360><c> we</c><00:04:24.479><c> can</c><00:04:24.600><c> see</c>

00:04:24.830 --> 00:04:24.840 align:start position:0%
use the same template and we can see
 

00:04:24.840 --> 00:04:27.189 align:start position:0%
use the same template and we can see
that<00:04:25.080><c> many</c><00:04:25.320><c> of</c><00:04:25.400><c> the</c><00:04:25.600><c> models</c><00:04:26.320><c> share</c><00:04:26.720><c> the</c><00:04:26.880><c> same</c>

00:04:27.189 --> 00:04:27.199 align:start position:0%
that many of the models share the same
 

00:04:27.199 --> 00:04:29.670 align:start position:0%
that many of the models share the same
template<00:04:28.199><c> there</c><00:04:28.320><c> are</c><00:04:28.479><c> a</c><00:04:28.639><c> lot</c><00:04:28.800><c> of</c><00:04:29.000><c> other</c><00:04:29.199><c> models</c>

00:04:29.670 --> 00:04:29.680 align:start position:0%
template there are a lot of other models
 

00:04:29.680 --> 00:04:31.670 align:start position:0%
template there are a lot of other models
that<00:04:30.039><c> use</c><00:04:30.479><c> essentially</c><00:04:30.919><c> the</c><00:04:31.039><c> same</c><00:04:31.240><c> template</c>

00:04:31.670 --> 00:04:31.680 align:start position:0%
that use essentially the same template
 

00:04:31.680 --> 00:04:34.110 align:start position:0%
that use essentially the same template
but<00:04:31.840><c> they</c><00:04:31.919><c> may</c><00:04:32.120><c> be</c><00:04:32.320><c> off</c><00:04:32.600><c> by</c><00:04:32.759><c> a</c><00:04:33.000><c> space</c><00:04:33.400><c> somewhere</c>

00:04:34.110 --> 00:04:34.120 align:start position:0%
but they may be off by a space somewhere
 

00:04:34.120 --> 00:04:36.310 align:start position:0%
but they may be off by a space somewhere
so<00:04:34.320><c> we'll</c><00:04:34.520><c> have</c><00:04:34.639><c> a</c><00:04:34.800><c> different</c><00:04:35.160><c> digest</c><00:04:36.039><c> so</c><00:04:36.199><c> what</c>

00:04:36.310 --> 00:04:36.320 align:start position:0%
so we'll have a different digest so what
 

00:04:36.320 --> 00:04:38.230 align:start position:0%
so we'll have a different digest so what
happens<00:04:36.600><c> when</c><00:04:36.720><c> we</c><00:04:36.840><c> pull</c><00:04:37.039><c> a</c><00:04:37.160><c> new</c><00:04:37.320><c> model</c><00:04:37.960><c> is</c><00:04:38.080><c> that</c>

00:04:38.230 --> 00:04:38.240 align:start position:0%
happens when we pull a new model is that
 

00:04:38.240 --> 00:04:40.550 align:start position:0%
happens when we pull a new model is that
it<00:04:38.479><c> grabs</c><00:04:38.840><c> the</c><00:04:39.039><c> Manifest</c><00:04:39.759><c> and</c><00:04:39.880><c> then</c><00:04:40.080><c> looks</c><00:04:40.280><c> for</c>

00:04:40.550 --> 00:04:40.560 align:start position:0%
it grabs the Manifest and then looks for
 

00:04:40.560 --> 00:04:42.710 align:start position:0%
it grabs the Manifest and then looks for
the<00:04:40.680><c> list</c><00:04:40.880><c> of</c><00:04:41.000><c> blobs</c><00:04:41.360><c> it</c><00:04:41.479><c> needs</c><00:04:41.639><c> to</c><00:04:41.800><c> download</c>

00:04:42.710 --> 00:04:42.720 align:start position:0%
the list of blobs it needs to download
 

00:04:42.720 --> 00:04:44.870 align:start position:0%
the list of blobs it needs to download
if<00:04:42.840><c> your</c><00:04:43.000><c> machine</c><00:04:43.440><c> already</c><00:04:43.840><c> has</c><00:04:44.080><c> any</c><00:04:44.280><c> blob</c><00:04:44.800><c> it</c>

00:04:44.870 --> 00:04:44.880 align:start position:0%
if your machine already has any blob it
 

00:04:44.880 --> 00:04:46.830 align:start position:0%
if your machine already has any blob it
doesn't<00:04:45.160><c> download</c><00:04:45.520><c> it</c><00:04:46.199><c> let's</c><00:04:46.400><c> take</c><00:04:46.520><c> a</c><00:04:46.639><c> look</c><00:04:46.759><c> at</c>

00:04:46.830 --> 00:04:46.840 align:start position:0%
doesn't download it let's take a look at
 

00:04:46.840 --> 00:04:49.270 align:start position:0%
doesn't download it let's take a look at
a<00:04:46.960><c> few</c><00:04:47.160><c> other</c><00:04:47.360><c> scenarios</c><00:04:48.120><c> let's</c><00:04:48.360><c> copy</c><00:04:48.639><c> a</c><00:04:48.759><c> model</c>

00:04:49.270 --> 00:04:49.280 align:start position:0%
a few other scenarios let's copy a model
 

00:04:49.280 --> 00:04:54.590 align:start position:0%
a few other scenarios let's copy a model
olama<00:04:49.880><c> CP</c><00:04:50.680><c> llama</c><00:04:51.080><c> 2</c><00:04:51.880><c> 7B</c><00:04:52.880><c> Matt</c><00:04:53.240><c> w/</c><00:04:53.960><c> a</c><00:04:54.199><c> model</c><00:04:54.400><c> for</c>

00:04:54.590 --> 00:04:54.600 align:start position:0%
olama CP llama 2 7B Matt w/ a model for
 

00:04:54.600 --> 00:04:57.430 align:start position:0%
olama CP llama 2 7B Matt w/ a model for
the<00:04:54.840><c> video</c><00:04:55.840><c> let's</c><00:04:56.039><c> take</c><00:04:56.160><c> a</c><00:04:56.360><c> look</c><00:04:56.680><c> at</c><00:04:56.960><c> the</c><00:04:57.199><c> new</c>

00:04:57.430 --> 00:04:57.440 align:start position:0%
the video let's take a look at the new
 

00:04:57.440 --> 00:05:01.070 align:start position:0%
the video let's take a look at the new
manifest<00:04:58.440><c> the</c><00:04:58.560><c> model</c><00:04:58.919><c> Digest</c><00:04:59.639><c> is</c><00:04:59.840><c> the</c><00:05:00.039><c> same</c><00:05:01.000><c> if</c>

00:05:01.070 --> 00:05:01.080 align:start position:0%
manifest the model Digest is the same if
 

00:05:01.080 --> 00:05:03.430 align:start position:0%
manifest the model Digest is the same if
we<00:05:01.280><c> create</c><00:05:01.520><c> a</c><00:05:01.639><c> new</c><00:05:01.800><c> model</c><00:05:02.080><c> file</c><00:05:02.440><c> and</c><00:05:02.560><c> say</c><00:05:03.000><c> from</c>

00:05:03.430 --> 00:05:03.440 align:start position:0%
we create a new model file and say from
 

00:05:03.440 --> 00:05:06.430 align:start position:0%
we create a new model file and say from
llama<00:05:03.800><c> 27b</c><00:05:04.680><c> with</c><00:05:04.919><c> system</c><00:05:05.600><c> you're</c><00:05:05.840><c> an</c><00:05:06.039><c> insane</c>

00:05:06.430 --> 00:05:06.440 align:start position:0%
llama 27b with system you're an insane
 

00:05:06.440 --> 00:05:09.629 align:start position:0%
llama 27b with system you're an insane
model<00:05:07.240><c> and</c><00:05:07.360><c> then</c><00:05:07.600><c> AMA</c><00:05:08.160><c> create</c><00:05:08.680><c> Matt</c><00:05:08.960><c> w/</c>

00:05:09.629 --> 00:05:09.639 align:start position:0%
model and then AMA create Matt w/
 

00:05:09.639 --> 00:05:11.469 align:start position:0%
model and then AMA create Matt w/
Insanity<00:05:10.639><c> let's</c><00:05:10.840><c> take</c><00:05:10.960><c> a</c><00:05:11.080><c> look</c><00:05:11.199><c> at</c><00:05:11.320><c> the</c>

00:05:11.469 --> 00:05:11.479 align:start position:0%
Insanity let's take a look at the
 

00:05:11.479 --> 00:05:14.870 align:start position:0%
Insanity let's take a look at the
Manifest<00:05:11.960><c> for</c><00:05:12.160><c> Matt</c><00:05:12.440><c> w/</c><00:05:13.440><c> Insanity</c><00:05:14.440><c> it's</c><00:05:14.639><c> the</c>

00:05:14.870 --> 00:05:14.880 align:start position:0%
Manifest for Matt w/ Insanity it's the
 

00:05:14.880 --> 00:05:18.629 align:start position:0%
Manifest for Matt w/ Insanity it's the
same<00:05:15.520><c> model</c><00:05:15.880><c> cha</c><00:05:16.240><c> 256</c><00:05:17.120><c> digest</c><00:05:18.120><c> okay</c><00:05:18.319><c> so</c><00:05:18.479><c> what</c>

00:05:18.629 --> 00:05:18.639 align:start position:0%
same model cha 256 digest okay so what
 

00:05:18.639 --> 00:05:21.710 align:start position:0%
same model cha 256 digest okay so what
happens<00:05:18.919><c> when</c><00:05:19.039><c> we</c><00:05:19.199><c> delete</c><00:05:19.479><c> a</c><00:05:19.919><c> model</c><00:05:20.919><c> RM</c><00:05:21.440><c> Matt</c>

00:05:21.710 --> 00:05:21.720 align:start position:0%
happens when we delete a model RM Matt
 

00:05:21.720 --> 00:05:24.749 align:start position:0%
happens when we delete a model RM Matt
w/<00:05:22.440><c> a</c><00:05:22.680><c> model</c><00:05:22.960><c> for</c><00:05:23.280><c> the</c><00:05:23.440><c> video</c><00:05:24.199><c> and</c><00:05:24.319><c> we</c><00:05:24.440><c> see</c><00:05:24.639><c> that</c>

00:05:24.749 --> 00:05:24.759 align:start position:0%
w/ a model for the video and we see that
 

00:05:24.759 --> 00:05:29.629 align:start position:0%
w/ a model for the video and we see that
the<00:05:24.880><c> blob</c><00:05:25.319><c> is</c><00:05:25.479><c> still</c><00:05:25.840><c> there</c><00:05:26.840><c> AMA</c><00:05:27.360><c> RM</c><00:05:27.919><c> llama</c><00:05:28.479><c> 27b</c>

00:05:29.629 --> 00:05:29.639 align:start position:0%
the blob is still there AMA RM llama 27b
 

00:05:29.639 --> 00:05:33.270 align:start position:0%
the blob is still there AMA RM llama 27b
and<00:05:29.919><c> still</c><00:05:30.479><c> The</c><00:05:30.680><c> Blob</c><00:05:31.199><c> is</c><00:05:31.360><c> still</c><00:05:31.720><c> there</c><00:05:32.520><c> but</c><00:05:32.720><c> ol</c>

00:05:33.270 --> 00:05:33.280 align:start position:0%
and still The Blob is still there but ol
 

00:05:33.280 --> 00:05:36.870 align:start position:0%
and still The Blob is still there but ol
RM<00:05:33.759><c> Matt</c><00:05:34.039><c> w/</c><00:05:34.840><c> insanity</c><00:05:35.840><c> and</c><00:05:36.000><c> now</c><00:05:36.319><c> that</c><00:05:36.479><c> blob</c>

00:05:36.870 --> 00:05:36.880 align:start position:0%
RM Matt w/ insanity and now that blob
 

00:05:36.880 --> 00:05:40.110 align:start position:0%
RM Matt w/ insanity and now that blob
file<00:05:37.400><c> is</c><00:05:37.680><c> gone</c><00:05:38.639><c> so</c><00:05:38.800><c> we</c><00:05:38.960><c> keep</c><00:05:39.160><c> the</c><00:05:39.280><c> blob</c><00:05:39.680><c> files</c>

00:05:40.110 --> 00:05:40.120 align:start position:0%
file is gone so we keep the blob files
 

00:05:40.120 --> 00:05:42.309 align:start position:0%
file is gone so we keep the blob files
for<00:05:40.319><c> as</c><00:05:40.560><c> long</c><00:05:40.840><c> as</c><00:05:41.000><c> there</c><00:05:41.120><c> is</c><00:05:41.240><c> a</c><00:05:41.400><c> model</c><00:05:41.800><c> file</c><00:05:42.039><c> or</c>

00:05:42.309 --> 00:05:42.319 align:start position:0%
for as long as there is a model file or
 

00:05:42.319 --> 00:05:44.950 align:start position:0%
for as long as there is a model file or
manifest<00:05:42.880><c> that</c><00:05:43.039><c> references</c><00:05:43.560><c> it</c><00:05:44.240><c> and</c><00:05:44.400><c> we</c><00:05:44.600><c> only</c>

00:05:44.950 --> 00:05:44.960 align:start position:0%
manifest that references it and we only
 

00:05:44.960 --> 00:05:48.070 align:start position:0%
manifest that references it and we only
keep<00:05:45.120><c> a</c><00:05:45.280><c> single</c><00:05:45.720><c> copy</c><00:05:46.199><c> of</c><00:05:46.400><c> any</c><00:05:46.720><c> blob</c><00:05:47.479><c> and</c><00:05:47.639><c> reuse</c>

00:05:48.070 --> 00:05:48.080 align:start position:0%
keep a single copy of any blob and reuse
 

00:05:48.080 --> 00:05:51.469 align:start position:0%
keep a single copy of any blob and reuse
it<00:05:48.759><c> if</c><00:05:49.120><c> another</c><00:05:49.759><c> model</c><00:05:50.199><c> includes</c><00:05:50.600><c> it</c><00:05:51.280><c> that's</c>

00:05:51.469 --> 00:05:51.479 align:start position:0%
it if another model includes it that's
 

00:05:51.479 --> 00:05:53.150 align:start position:0%
it if another model includes it that's
why<00:05:51.600><c> we</c><00:05:51.720><c> can</c><00:05:51.840><c> delete</c><00:05:52.160><c> dozens</c><00:05:52.520><c> of</c><00:05:52.680><c> models</c><00:05:53.000><c> and</c>

00:05:53.150 --> 00:05:53.160 align:start position:0%
why we can delete dozens of models and
 

00:05:53.160 --> 00:05:56.070 align:start position:0%
why we can delete dozens of models and
not<00:05:53.400><c> get</c><00:05:53.720><c> any</c><00:05:54.000><c> decent</c><00:05:54.400><c> space</c><00:05:54.759><c> back</c><00:05:55.520><c> or</c><00:05:55.759><c> why</c><00:05:55.960><c> we</c>

00:05:56.070 --> 00:05:56.080 align:start position:0%
not get any decent space back or why we
 

00:05:56.080 --> 00:05:59.670 align:start position:0%
not get any decent space back or why we
can<00:05:56.319><c> add</c><00:05:56.600><c> hundreds</c><00:05:57.600><c> and</c><00:05:57.759><c> not</c><00:05:58.240><c> consume</c><00:05:58.840><c> more</c>

00:05:59.670 --> 00:05:59.680 align:start position:0%
can add hundreds and not consume more
 

00:05:59.680 --> 00:06:01.390 align:start position:0%
can add hundreds and not consume more
assuming<00:06:00.160><c> all</c><00:06:00.400><c> those</c><00:06:00.560><c> models</c><00:06:00.880><c> are</c><00:06:01.039><c> based</c><00:06:01.280><c> on</c>

00:06:01.390 --> 00:06:01.400 align:start position:0%
assuming all those models are based on
 

00:06:01.400 --> 00:06:03.590 align:start position:0%
assuming all those models are based on
the<00:06:01.520><c> same</c><00:06:01.720><c> Source</c><00:06:02.160><c> model</c><00:06:03.000><c> hopefully</c><00:06:03.440><c> that</c>

00:06:03.590 --> 00:06:03.600 align:start position:0%
the same Source model hopefully that
 

00:06:03.600 --> 00:06:05.710 align:start position:0%
the same Source model hopefully that
clears<00:06:03.960><c> up</c><00:06:04.160><c> a</c><00:06:04.280><c> few</c><00:06:04.520><c> questions</c><00:06:04.960><c> like</c><00:06:05.440><c> where</c><00:06:05.600><c> do</c>

00:06:05.710 --> 00:06:05.720 align:start position:0%
clears up a few questions like where do
 

00:06:05.720 --> 00:06:08.189 align:start position:0%
clears up a few questions like where do
we<00:06:05.880><c> store</c><00:06:06.280><c> the</c><00:06:06.400><c> models</c><00:06:07.039><c> why</c><00:06:07.160><c> don't</c><00:06:07.520><c> the</c><00:06:07.759><c> models</c>

00:06:08.189 --> 00:06:08.199 align:start position:0%
we store the models why don't the models
 

00:06:08.199 --> 00:06:10.469 align:start position:0%
we store the models why don't the models
have<00:06:08.440><c> sensible</c><00:06:08.960><c> names</c><00:06:09.680><c> what</c><00:06:09.880><c> happens</c><00:06:10.160><c> when</c><00:06:10.319><c> I</c>

00:06:10.469 --> 00:06:10.479 align:start position:0%
have sensible names what happens when I
 

00:06:10.479 --> 00:06:13.270 align:start position:0%
have sensible names what happens when I
delete<00:06:10.800><c> a</c><00:06:10.960><c> model</c><00:06:11.360><c> and</c><00:06:11.599><c> so</c><00:06:11.880><c> much</c><00:06:12.199><c> more</c><00:06:13.039><c> let</c><00:06:13.160><c> me</c>

00:06:13.270 --> 00:06:13.280 align:start position:0%
delete a model and so much more let me
 

00:06:13.280 --> 00:06:14.990 align:start position:0%
delete a model and so much more let me
know<00:06:13.440><c> if</c><00:06:13.560><c> you</c><00:06:13.680><c> have</c><00:06:13.840><c> any</c><00:06:14.039><c> other</c><00:06:14.280><c> questions</c><00:06:14.720><c> and</c>

00:06:14.990 --> 00:06:15.000 align:start position:0%
know if you have any other questions and
 

00:06:15.000 --> 00:06:17.110 align:start position:0%
know if you have any other questions and
be<00:06:15.160><c> sure</c><00:06:15.520><c> to</c><00:06:15.759><c> like</c><00:06:16.000><c> And</c><00:06:16.199><c> subscribe</c><00:06:16.680><c> to</c><00:06:16.880><c> get</c>

00:06:17.110 --> 00:06:17.120 align:start position:0%
be sure to like And subscribe to get
 

00:06:17.120 --> 00:06:19.870 align:start position:0%
be sure to like And subscribe to get
more<00:06:17.759><c> similar</c><00:06:18.199><c> videos</c><00:06:19.160><c> thanks</c><00:06:19.400><c> so</c><00:06:19.520><c> much</c><00:06:19.680><c> for</c>

00:06:19.870 --> 00:06:19.880 align:start position:0%
more similar videos thanks so much for
 

00:06:19.880 --> 00:06:22.300 align:start position:0%
more similar videos thanks so much for
watching

00:06:22.300 --> 00:06:22.310 align:start position:0%
watching
 

00:06:22.310 --> 00:06:28.390 align:start position:0%
watching
[Music]

00:06:28.390 --> 00:06:28.400 align:start position:0%
 
 

00:06:28.400 --> 00:06:31.340 align:start position:0%
 
goodbye

00:06:31.340 --> 00:06:31.350 align:start position:0%
goodbye
 

00:06:31.350 --> 00:06:36.759 align:start position:0%
goodbye
[Music]

