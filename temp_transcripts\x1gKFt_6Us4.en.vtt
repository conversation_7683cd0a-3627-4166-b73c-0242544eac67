WEBVTT
Kind: captions
Language: en

00:00:00.320 --> 00:00:02.470 align:start position:0%
 
and<00:00:00.599><c> welcome</c><00:00:01.040><c> everybody</c><00:00:01.800><c> name</c><00:00:01.959><c> is</c><00:00:02.080><c> Alicia</c>

00:00:02.470 --> 00:00:02.480 align:start position:0%
and welcome everybody name is <PERSON>
 

00:00:02.480 --> 00:00:04.789 align:start position:0%
and welcome everybody name is <PERSON><00:00:03.240><c> today</c><00:00:03.480><c> I'm</c><00:00:03.600><c> going</c><00:00:03.719><c> to</c><00:00:03.919><c> demo</c><00:00:04.240><c> for</c><00:00:04.480><c> you</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
<PERSON> today I'm going to demo for you
 

00:00:04.799 --> 00:00:07.709 align:start position:0%
<PERSON> today I'm going to demo for you
how<00:00:04.920><c> to</c><00:00:05.120><c> get</c><00:00:05.359><c> up</c><00:00:05.520><c> and</c><00:00:05.720><c> running</c><00:00:06.600><c> with</c><00:00:06.879><c> GPT</c>

00:00:07.709 --> 00:00:07.719 align:start position:0%
how to get up and running with GPT
 

00:00:07.719 --> 00:00:09.549 align:start position:0%
how to get up and running with GPT
researcher<00:00:08.719><c> first</c><00:00:08.960><c> thing</c><00:00:09.120><c> you're</c><00:00:09.280><c> going</c><00:00:09.400><c> to</c>

00:00:09.549 --> 00:00:09.559 align:start position:0%
researcher first thing you're going to
 

00:00:09.559 --> 00:00:12.669 align:start position:0%
researcher first thing you're going to
do<00:00:09.960><c> go</c><00:00:10.160><c> into</c><00:00:10.400><c> the</c><00:00:10.559><c> GitHub</c><00:00:11.320><c> package</c><00:00:12.320><c> and</c><00:00:12.519><c> you're</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
do go into the GitHub package and you're
 

00:00:12.679 --> 00:00:15.110 align:start position:0%
do go into the GitHub package and you're
G<00:00:12.799><c> to</c><00:00:13.240><c> press</c><00:00:13.559><c> this</c><00:00:13.719><c> little</c><00:00:14.000><c> green</c><00:00:14.480><c> guy</c><00:00:15.000><c> and</c>

00:00:15.110 --> 00:00:15.120 align:start position:0%
G to press this little green guy and
 

00:00:15.120 --> 00:00:17.870 align:start position:0%
G to press this little green guy and
you're<00:00:15.240><c> going</c><00:00:15.360><c> to</c><00:00:15.519><c> do</c><00:00:16.000><c> download</c><00:00:16.680><c> zip</c><00:00:17.680><c> once</c>

00:00:17.870 --> 00:00:17.880 align:start position:0%
you're going to do download zip once
 

00:00:17.880 --> 00:00:19.670 align:start position:0%
you're going to do download zip once
we're<00:00:18.080><c> going</c><00:00:18.160><c> to</c><00:00:18.359><c> download</c><00:00:18.800><c> our</c><00:00:19.039><c> ZIP</c><00:00:19.560><c> then</c>

00:00:19.670 --> 00:00:19.680 align:start position:0%
we're going to download our ZIP then
 

00:00:19.680 --> 00:00:21.910 align:start position:0%
we're going to download our ZIP then
we're<00:00:19.840><c> just</c><00:00:20.000><c> going</c><00:00:20.080><c> to</c><00:00:20.279><c> unzip</c><00:00:20.800><c> it</c><00:00:21.600><c> and</c><00:00:21.760><c> we're</c>

00:00:21.910 --> 00:00:21.920 align:start position:0%
we're just going to unzip it and we're
 

00:00:21.920 --> 00:00:24.029 align:start position:0%
we're just going to unzip it and we're
going<00:00:22.000><c> to</c><00:00:22.199><c> open</c><00:00:22.439><c> it</c><00:00:22.640><c> up</c><00:00:23.160><c> in</c><00:00:23.359><c> this</c><00:00:23.560><c> case</c><00:00:23.840><c> we're</c>

00:00:24.029 --> 00:00:24.039 align:start position:0%
going to open it up in this case we're
 

00:00:24.039 --> 00:00:28.830 align:start position:0%
going to open it up in this case we're
going<00:00:24.160><c> to</c><00:00:24.359><c> open</c><00:00:24.560><c> it</c><00:00:24.760><c> up</c><00:00:25.160><c> with</c><00:00:25.679><c> our</c><00:00:26.679><c> uh</c><00:00:26.960><c> vs</c><00:00:27.840><c> code</c>

00:00:28.830 --> 00:00:28.840 align:start position:0%
going to open it up with our uh vs code
 

00:00:28.840 --> 00:00:31.950 align:start position:0%
going to open it up with our uh vs code
all<00:00:28.960><c> righty</c><00:00:29.480><c> so</c><00:00:29.759><c> now</c><00:00:30.039><c> now</c><00:00:30.640><c> once</c><00:00:30.840><c> we</c><00:00:31.000><c> open</c><00:00:31.240><c> it</c><00:00:31.439><c> up</c>

00:00:31.950 --> 00:00:31.960 align:start position:0%
all righty so now now once we open it up
 

00:00:31.960 --> 00:00:34.630 align:start position:0%
all righty so now now once we open it up
with<00:00:32.520><c> vs</c><00:00:33.079><c> code</c><00:00:33.559><c> we're</c><00:00:33.719><c> going</c><00:00:33.840><c> to</c><00:00:33.920><c> see</c><00:00:34.120><c> a</c><00:00:34.280><c> file</c>

00:00:34.630 --> 00:00:34.640 align:start position:0%
with vs code we're going to see a file
 

00:00:34.640 --> 00:00:35.790 align:start position:0%
with vs code we're going to see a file
here<00:00:34.879><c> called</c>

00:00:35.790 --> 00:00:35.800 align:start position:0%
here called
 

00:00:35.800 --> 00:00:39.069 align:start position:0%
here called
env.<00:00:37.120><c> example</c><00:00:38.120><c> and</c><00:00:38.320><c> there's</c><00:00:38.520><c> a</c><00:00:38.680><c> couple</c><00:00:38.879><c> of</c>

00:00:39.069 --> 00:00:39.079 align:start position:0%
env. example and there's a couple of
 

00:00:39.079 --> 00:00:41.389 align:start position:0%
env. example and there's a couple of
important<00:00:39.520><c> credentials</c><00:00:40.120><c> for</c><00:00:40.280><c> you</c><00:00:40.399><c> to</c><00:00:40.559><c> fetch</c>

00:00:41.389 --> 00:00:41.399 align:start position:0%
important credentials for you to fetch
 

00:00:41.399 --> 00:00:44.270 align:start position:0%
important credentials for you to fetch
number<00:00:41.640><c> one</c><00:00:41.800><c> is</c><00:00:41.960><c> the</c><00:00:42.160><c> open</c><00:00:42.480><c> AI</c><00:00:43.039><c> API</c><00:00:43.640><c> key</c><00:00:44.079><c> the</c>

00:00:44.270 --> 00:00:44.280 align:start position:0%
number one is the open AI API key the
 

00:00:44.280 --> 00:00:48.750 align:start position:0%
number one is the open AI API key the
tavil<00:00:44.920><c> API</c><00:00:45.520><c> key</c><00:00:46.320><c> the</c><00:00:46.480><c> Lang</c><00:00:46.920><c> chain</c><00:00:47.399><c> API</c><00:00:48.120><c> key</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
tavil API key the Lang chain API key
 

00:00:48.760 --> 00:00:51.630 align:start position:0%
tavil API key the Lang chain API key
that's<00:00:48.920><c> going</c><00:00:49.039><c> to</c><00:00:49.160><c> be</c><00:00:49.360><c> helpful</c><00:00:50.039><c> for</c><00:00:51.039><c> uh</c>

00:00:51.630 --> 00:00:51.640 align:start position:0%
that's going to be helpful for uh
 

00:00:51.640 --> 00:00:55.950 align:start position:0%
that's going to be helpful for uh
debugging<00:00:52.480><c> your</c><00:00:52.879><c> llm</c><00:00:53.640><c> flows</c><00:00:54.640><c> and</c><00:00:55.079><c> your</c><00:00:55.480><c> dock</c>

00:00:55.950 --> 00:00:55.960 align:start position:0%
debugging your llm flows and your dock
 

00:00:55.960 --> 00:00:58.029 align:start position:0%
debugging your llm flows and your dock
path<00:00:56.440><c> that</c><00:00:56.600><c> is</c><00:00:56.840><c> the</c><00:00:57.120><c> path</c><00:00:57.600><c> where</c><00:00:57.760><c> your</c>

00:00:58.029 --> 00:00:58.039 align:start position:0%
path that is the path where your
 

00:00:58.039 --> 00:01:01.430 align:start position:0%
path that is the path where your
documents<00:00:58.640><c> sit</c><00:00:59.640><c> and</c><00:01:00.320><c> once</c><00:01:00.600><c> you</c><00:01:00.800><c> have</c><00:01:01.000><c> defined</c>

00:01:01.430 --> 00:01:01.440 align:start position:0%
documents sit and once you have defined
 

00:01:01.440 --> 00:01:03.750 align:start position:0%
documents sit and once you have defined
all<00:01:01.760><c> those</c><00:01:02.039><c> we're</c><00:01:02.199><c> just</c><00:01:02.320><c> going</c><00:01:02.440><c> to</c><00:01:02.800><c> copy</c><00:01:03.199><c> that</c>

00:01:03.750 --> 00:01:03.760 align:start position:0%
all those we're just going to copy that
 

00:01:03.760 --> 00:01:06.590 align:start position:0%
all those we're just going to copy that
we're<00:01:03.920><c> going</c><00:01:04.000><c> to</c><00:01:04.159><c> create</c><00:01:04.400><c> a</c><00:01:04.559><c> new</c><00:01:05.000><c> file</c><00:01:06.000><c> and</c><00:01:06.280><c> you</c>

00:01:06.590 --> 00:01:06.600 align:start position:0%
we're going to create a new file and you
 

00:01:06.600 --> 00:01:08.550 align:start position:0%
we're going to create a new file and you
can<00:01:06.920><c> add</c><00:01:07.119><c> in</c><00:01:07.320><c> your</c><00:01:07.560><c> keys</c><00:01:07.960><c> over</c><00:01:08.240><c> there</c><00:01:08.439><c> you're</c>

00:01:08.550 --> 00:01:08.560 align:start position:0%
can add in your keys over there you're
 

00:01:08.560 --> 00:01:10.749 align:start position:0%
can add in your keys over there you're
going<00:01:08.680><c> to</c><00:01:08.920><c> press</c><00:01:09.280><c> save</c><00:01:10.080><c> you're</c><00:01:10.200><c> going</c><00:01:10.320><c> to</c><00:01:10.479><c> save</c>

00:01:10.749 --> 00:01:10.759 align:start position:0%
going to press save you're going to save
 

00:01:10.759 --> 00:01:13.950 align:start position:0%
going to press save you're going to save
it<00:01:11.080><c> in</c><00:01:11.200><c> a</c><00:01:11.360><c> file</c><00:01:11.640><c> called</c><00:01:12.320><c> EnV</c><00:01:13.280><c> right</c><00:01:13.439><c> so</c><00:01:13.640><c> just</c>

00:01:13.950 --> 00:01:13.960 align:start position:0%
it in a file called EnV right so just
 

00:01:13.960 --> 00:01:18.429 align:start position:0%
it in a file called EnV right so just
remove<00:01:14.439><c> the</c><00:01:15.159><c> example</c><00:01:16.159><c> and</c><00:01:16.360><c> the</c><00:01:16.520><c> final</c><00:01:16.880><c> step</c><00:01:17.600><c> is</c>

00:01:18.429 --> 00:01:18.439 align:start position:0%
remove the example and the final step is
 

00:01:18.439 --> 00:01:20.630 align:start position:0%
remove the example and the final step is
go<00:01:18.680><c> into</c><00:01:19.080><c> your</c><00:01:19.520><c> command</c><00:01:19.960><c> line</c><00:01:20.400><c> you're</c><00:01:20.520><c> going</c>

00:01:20.630 --> 00:01:20.640 align:start position:0%
go into your command line you're going
 

00:01:20.640 --> 00:01:24.590 align:start position:0%
go into your command line you're going
to<00:01:20.840><c> press</c><00:01:21.119><c> do</c><00:01:21.600><c> compose</c><00:01:22.200><c> up</c><00:01:23.079><c> Dash</c><00:01:23.520><c> Dash</c><00:01:23.960><c> build</c>

00:01:24.590 --> 00:01:24.600 align:start position:0%
to press do compose up Dash Dash build
 

00:01:24.600 --> 00:01:27.710 align:start position:0%
to press do compose up Dash Dash build
and<00:01:24.840><c> you</c><00:01:25.000><c> are</c><00:01:25.280><c> good</c><00:01:25.479><c> to</c><00:01:25.880><c> go</c><00:01:26.880><c> now</c><00:01:27.079><c> the</c><00:01:27.200><c> beauty</c><00:01:27.520><c> of</c>

00:01:27.710 --> 00:01:27.720 align:start position:0%
and you are good to go now the beauty of
 

00:01:27.720 --> 00:01:30.789 align:start position:0%
and you are good to go now the beauty of
this<00:01:28.000><c> is</c><00:01:28.320><c> that</c><00:01:29.320><c> um</c><00:01:29.680><c> oh</c><00:01:30.040><c> and</c><00:01:30.159><c> you</c><00:01:30.280><c> also</c><00:01:30.520><c> need</c>

00:01:30.789 --> 00:01:30.799 align:start position:0%
this is that um oh and you also need
 

00:01:30.799 --> 00:01:33.710 align:start position:0%
this is that um oh and you also need
you're<00:01:30.920><c> going</c><00:01:31.040><c> to</c><00:01:31.159><c> need</c><00:01:31.280><c> to</c><00:01:31.439><c> go</c><00:01:31.680><c> into</c><00:01:32.399><c> docs</c><00:01:33.320><c> and</c>

00:01:33.710 --> 00:01:33.720 align:start position:0%
you're going to need to go into docs and
 

00:01:33.720 --> 00:01:37.310 align:start position:0%
you're going to need to go into docs and
my<00:01:34.040><c> docs</c><00:01:34.960><c> you</c><00:01:35.079><c> can</c><00:01:35.360><c> put</c><00:01:35.520><c> in</c><00:01:36.000><c> your</c><00:01:36.840><c> personal</c>

00:01:37.310 --> 00:01:37.320 align:start position:0%
my docs you can put in your personal
 

00:01:37.320 --> 00:01:40.030 align:start position:0%
my docs you can put in your personal
documents<00:01:37.960><c> there</c><00:01:38.240><c> in</c><00:01:38.439><c> my</c><00:01:39.119><c> specific</c><00:01:39.600><c> case</c><00:01:39.840><c> I've</c>

00:01:40.030 --> 00:01:40.040 align:start position:0%
documents there in my specific case I've
 

00:01:40.040 --> 00:01:43.109 align:start position:0%
documents there in my specific case I've
put<00:01:40.200><c> in</c><00:01:40.840><c> my</c><00:01:41.119><c> developer</c><00:01:41.720><c> CV</c><00:01:42.399><c> and</c><00:01:42.600><c> some</c><00:01:42.759><c> of</c><00:01:42.920><c> my</c>

00:01:43.109 --> 00:01:43.119 align:start position:0%
put in my developer CV and some of my
 

00:01:43.119 --> 00:01:44.190 align:start position:0%
put in my developer CV and some of my
income

00:01:44.190 --> 00:01:44.200 align:start position:0%
income
 

00:01:44.200 --> 00:01:47.230 align:start position:0%
income
analysis<00:01:45.200><c> uh</c><00:01:45.360><c> over</c><00:01:45.560><c> the</c><00:01:45.759><c> past</c><00:01:46.520><c> uh</c><00:01:46.799><c> the</c><00:01:47.000><c> past</c>

00:01:47.230 --> 00:01:47.240 align:start position:0%
analysis uh over the past uh the past
 

00:01:47.240 --> 00:01:51.709 align:start position:0%
analysis uh over the past uh the past
several<00:01:47.759><c> years</c><00:01:48.759><c> uh</c><00:01:48.960><c> because</c><00:01:49.560><c> I</c><00:01:49.719><c> want</c><00:01:50.320><c> GPT</c><00:01:51.240><c> to</c>

00:01:51.709 --> 00:01:51.719 align:start position:0%
several years uh because I want GPT to
 

00:01:51.719 --> 00:01:53.870 align:start position:0%
several years uh because I want GPT to
analyze<00:01:52.640><c> and</c><00:01:53.119><c> uh</c><00:01:53.240><c> give</c><00:01:53.360><c> me</c><00:01:53.520><c> some</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
analyze and uh give me some
 

00:01:53.880 --> 00:01:56.789 align:start position:0%
analyze and uh give me some
recommendations<00:01:54.719><c> based</c><00:01:55.000><c> off</c><00:01:55.159><c> of</c><00:01:55.399><c> my</c><00:01:56.159><c> personal</c>

00:01:56.789 --> 00:01:56.799 align:start position:0%
recommendations based off of my personal
 

00:01:56.799 --> 00:01:58.990 align:start position:0%
recommendations based off of my personal
documents<00:01:57.799><c> once</c><00:01:58.000><c> we</c><00:01:58.159><c> run</c><00:01:58.399><c> that</c><00:01:58.600><c> Docker</c>

00:01:58.990 --> 00:01:59.000 align:start position:0%
documents once we run that Docker
 

00:01:59.000 --> 00:02:00.350 align:start position:0%
documents once we run that Docker
compose<00:01:59.439><c> command</c>

00:02:00.350 --> 00:02:00.360 align:start position:0%
compose command
 

00:02:00.360 --> 00:02:02.149 align:start position:0%
compose command
we're<00:02:00.560><c> going</c><00:02:00.640><c> to</c><00:02:01.079><c> it's</c><00:02:01.240><c> going</c><00:02:01.320><c> to</c><00:02:01.719><c> uh</c><00:02:01.880><c> the</c><00:02:02.000><c> app</c>

00:02:02.149 --> 00:02:02.159 align:start position:0%
we're going to it's going to uh the app
 

00:02:02.159 --> 00:02:04.550 align:start position:0%
we're going to it's going to uh the app
is<00:02:02.280><c> going</c><00:02:02.360><c> to</c><00:02:02.479><c> be</c><00:02:02.600><c> available</c><00:02:03.119><c> on</c><00:02:03.399><c> Local</c><00:02:03.799><c> Host</c>

00:02:04.550 --> 00:02:04.560 align:start position:0%
is going to be available on Local Host
 

00:02:04.560 --> 00:02:07.109 align:start position:0%
is going to be available on Local Host
8000<00:02:05.560><c> and</c><00:02:05.719><c> I'm</c><00:02:05.840><c> going</c><00:02:05.920><c> to</c><00:02:06.079><c> go</c><00:02:06.200><c> in</c><00:02:06.479><c> here</c><00:02:07.000><c> I'm</c>

00:02:07.109 --> 00:02:07.119 align:start position:0%
8000 and I'm going to go in here I'm
 

00:02:07.119 --> 00:02:09.949 align:start position:0%
8000 and I'm going to go in here I'm
going<00:02:07.200><c> to</c><00:02:07.439><c> choose</c><00:02:08.239><c> the</c><00:02:08.440><c> source</c><00:02:08.959><c> is</c><00:02:09.360><c> my</c>

00:02:09.949 --> 00:02:09.959 align:start position:0%
going to choose the source is my
 

00:02:09.959 --> 00:02:12.309 align:start position:0%
going to choose the source is my
documents<00:02:10.959><c> and</c><00:02:11.080><c> I'm</c><00:02:11.200><c> going</c><00:02:11.319><c> to</c><00:02:11.520><c> say</c><00:02:11.879><c> tell</c><00:02:12.080><c> me</c>

00:02:12.309 --> 00:02:12.319 align:start position:0%
documents and I'm going to say tell me
 

00:02:12.319 --> 00:02:14.710 align:start position:0%
documents and I'm going to say tell me
about<00:02:12.720><c> Alicia</c><00:02:13.160><c> Kramer</c><00:02:13.840><c> tell</c><00:02:13.959><c> me</c><00:02:14.120><c> about</c><00:02:14.319><c> Alicia</c>

00:02:14.710 --> 00:02:14.720 align:start position:0%
about Alicia Kramer tell me about Alicia
 

00:02:14.720 --> 00:02:19.110 align:start position:0%
about Alicia Kramer tell me about Alicia
Kramer<00:02:15.680><c> how</c><00:02:15.920><c> can</c><00:02:16.080><c> he</c><00:02:16.560><c> improve</c><00:02:17.560><c> how</c><00:02:17.720><c> can</c><00:02:17.959><c> he</c><00:02:18.800><c> uh</c>

00:02:19.110 --> 00:02:19.120 align:start position:0%
Kramer how can he improve how can he uh
 

00:02:19.120 --> 00:02:22.630 align:start position:0%
Kramer how can he improve how can he uh
how<00:02:20.120><c> what</c><00:02:20.360><c> what</c><00:02:20.599><c> kind</c><00:02:20.879><c> of</c><00:02:21.760><c> right</c><00:02:22.120><c> what</c><00:02:22.360><c> what</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
how what what kind of right what what
 

00:02:22.640 --> 00:02:25.229 align:start position:0%
how what what kind of right what what
kind<00:02:23.160><c> of</c><00:02:23.920><c> career</c>

00:02:25.229 --> 00:02:25.239 align:start position:0%
kind of career
 

00:02:25.239 --> 00:02:31.030 align:start position:0%
kind of career
advice<00:02:26.239><c> would</c><00:02:27.560><c> you</c><00:02:28.560><c> give</c><00:02:30.040><c> him</c><00:02:30.560><c> all</c><00:02:30.680><c> right</c><00:02:30.920><c> okay</c>

00:02:31.030 --> 00:02:31.040 align:start position:0%
advice would you give him all right okay
 

00:02:31.040 --> 00:02:32.710 align:start position:0%
advice would you give him all right okay
so<00:02:31.160><c> it's</c><00:02:31.280><c> totally</c><00:02:31.640><c> up</c><00:02:31.800><c> to</c><00:02:31.959><c> you</c><00:02:32.239><c> what</c><00:02:32.400><c> kind</c><00:02:32.519><c> of</c>

00:02:32.710 --> 00:02:32.720 align:start position:0%
so it's totally up to you what kind of
 

00:02:32.720 --> 00:02:34.309 align:start position:0%
so it's totally up to you what kind of
documents<00:02:33.200><c> you</c><00:02:33.280><c> want</c><00:02:33.400><c> to</c><00:02:33.560><c> put</c><00:02:33.720><c> in</c><00:02:33.920><c> there</c><00:02:34.200><c> you</c>

00:02:34.309 --> 00:02:34.319 align:start position:0%
documents you want to put in there you
 

00:02:34.319 --> 00:02:36.550 align:start position:0%
documents you want to put in there you
might<00:02:34.480><c> want</c><00:02:34.599><c> to</c><00:02:34.760><c> put</c><00:02:34.879><c> in</c><00:02:35.480><c> some</c><00:02:35.800><c> correspondence</c>

00:02:36.550 --> 00:02:36.560 align:start position:0%
might want to put in some correspondence
 

00:02:36.560 --> 00:02:38.270 align:start position:0%
might want to put in some correspondence
with<00:02:36.720><c> your</c><00:02:36.920><c> mother</c><00:02:37.360><c> with</c><00:02:37.519><c> an</c>

00:02:38.270 --> 00:02:38.280 align:start position:0%
with your mother with an
 

00:02:38.280 --> 00:02:41.430 align:start position:0%
with your mother with an
exgirlfriend<00:02:39.280><c> just</c><00:02:39.599><c> something</c><00:02:39.920><c> for</c><00:02:40.599><c> GPT</c><00:02:41.239><c> to</c>

00:02:41.430 --> 00:02:41.440 align:start position:0%
exgirlfriend just something for GPT to
 

00:02:41.440 --> 00:02:44.390 align:start position:0%
exgirlfriend just something for GPT to
be<00:02:42.040><c> um</c><00:02:42.360><c> to</c><00:02:42.480><c> be</c><00:02:42.599><c> able</c><00:02:42.800><c> to</c><00:02:43.000><c> analyze</c><00:02:43.920><c> and</c><00:02:44.120><c> give</c><00:02:44.280><c> you</c>

00:02:44.390 --> 00:02:44.400 align:start position:0%
be um to be able to analyze and give you
 

00:02:44.400 --> 00:02:46.509 align:start position:0%
be um to be able to analyze and give you
some<00:02:44.560><c> meaningful</c><00:02:45.120><c> information</c><00:02:45.640><c> about</c><00:02:46.360><c> tell</c>

00:02:46.509 --> 00:02:46.519 align:start position:0%
some meaningful information about tell
 

00:02:46.519 --> 00:02:48.830 align:start position:0%
some meaningful information about tell
me<00:02:46.640><c> about</c><00:02:46.840><c> Alicia</c><00:02:47.200><c> Kramer</c><00:02:47.959><c> and</c><00:02:48.360><c> let's</c><00:02:48.560><c> have</c><00:02:48.680><c> a</c>

00:02:48.830 --> 00:02:48.840 align:start position:0%
me about Alicia Kramer and let's have a
 

00:02:48.840 --> 00:02:51.710 align:start position:0%
me about Alicia Kramer and let's have a
look<00:02:49.480><c> at</c><00:02:49.640><c> how</c><00:02:50.000><c> GPT</c><00:02:51.000><c> uh</c><00:02:51.120><c> gives</c><00:02:51.319><c> me</c><00:02:51.480><c> some</c>

00:02:51.710 --> 00:02:51.720 align:start position:0%
look at how GPT uh gives me some
 

00:02:51.720 --> 00:02:54.790 align:start position:0%
look at how GPT uh gives me some
personal<00:02:52.519><c> recommendations</c><00:02:53.519><c> based</c><00:02:53.879><c> off</c><00:02:54.040><c> of</c><00:02:54.280><c> my</c>

00:02:54.790 --> 00:02:54.800 align:start position:0%
personal recommendations based off of my
 

00:02:54.800 --> 00:02:56.750 align:start position:0%
personal recommendations based off of my
documents<00:02:55.400><c> awesome</c><00:02:55.800><c> it's</c><00:02:56.040><c> already</c><00:02:56.319><c> choosing</c>

00:02:56.750 --> 00:02:56.760 align:start position:0%
documents awesome it's already choosing
 

00:02:56.760 --> 00:02:59.270 align:start position:0%
documents awesome it's already choosing
my<00:02:56.959><c> career</c><00:02:57.360><c> coach</c><00:02:57.800><c> agent</c><00:02:58.640><c> and</c><00:02:58.760><c> you</c><00:02:58.879><c> can</c><00:02:59.080><c> just</c>

00:02:59.270 --> 00:02:59.280 align:start position:0%
my career coach agent and you can just
 

00:02:59.280 --> 00:03:02.430 align:start position:0%
my career coach agent and you can just
think<00:02:59.480><c> of</c><00:02:59.599><c> the</c><00:02:59.959><c> possibilities</c><00:03:00.720><c> here</c><00:03:01.360><c> you</c><00:03:01.560><c> can</c>

00:03:02.430 --> 00:03:02.440 align:start position:0%
think of the possibilities here you can
 

00:03:02.440 --> 00:03:04.990 align:start position:0%
think of the possibilities here you can
use<00:03:02.840><c> these</c><00:03:03.120><c> same</c><00:03:03.599><c> design</c><00:03:04.040><c> patterns</c><00:03:04.560><c> to</c><00:03:04.840><c> kind</c>

00:03:04.990 --> 00:03:05.000 align:start position:0%
use these same design patterns to kind
 

00:03:05.000 --> 00:03:09.270 align:start position:0%
use these same design patterns to kind
of<00:03:06.000><c> uh</c><00:03:06.200><c> have</c><00:03:06.440><c> GPT</c><00:03:07.360><c> crunch</c><00:03:07.879><c> your</c><00:03:08.319><c> internal</c><00:03:08.920><c> data</c>

00:03:09.270 --> 00:03:09.280 align:start position:0%
of uh have GPT crunch your internal data
 

00:03:09.280 --> 00:03:11.750 align:start position:0%
of uh have GPT crunch your internal data
your<00:03:09.519><c> help</c><00:03:09.840><c> center</c><00:03:10.360><c> your</c><00:03:10.560><c> Zen</c><00:03:11.040><c> desk</c>

00:03:11.750 --> 00:03:11.760 align:start position:0%
your help center your Zen desk
 

00:03:11.760 --> 00:03:14.509 align:start position:0%
your help center your Zen desk
everything<00:03:12.760><c> and</c><00:03:13.159><c> uh</c><00:03:13.280><c> so</c><00:03:13.440><c> there's</c><00:03:13.840><c> a</c><00:03:14.000><c> lot</c><00:03:14.200><c> a</c><00:03:14.400><c> lot</c>

00:03:14.509 --> 00:03:14.519 align:start position:0%
everything and uh so there's a lot a lot
 

00:03:14.519 --> 00:03:16.750 align:start position:0%
everything and uh so there's a lot a lot
of<00:03:14.959><c> uh</c><00:03:15.159><c> directions</c><00:03:15.799><c> where</c><00:03:16.000><c> this</c><00:03:16.159><c> can</c><00:03:16.360><c> go</c><00:03:16.640><c> but</c>

00:03:16.750 --> 00:03:16.760 align:start position:0%
of uh directions where this can go but
 

00:03:16.760 --> 00:03:19.270 align:start position:0%
of uh directions where this can go but
in<00:03:16.879><c> this</c><00:03:17.120><c> specific</c><00:03:17.599><c> case</c><00:03:17.840><c> I'm</c><00:03:18.080><c> just</c><00:03:18.799><c> wanted</c><00:03:19.120><c> to</c>

00:03:19.270 --> 00:03:19.280 align:start position:0%
in this specific case I'm just wanted to
 

00:03:19.280 --> 00:03:21.190 align:start position:0%
in this specific case I'm just wanted to
tell<00:03:19.440><c> me</c><00:03:19.599><c> about</c><00:03:19.879><c> myself</c><00:03:20.319><c> and</c><00:03:20.680><c> of</c><00:03:20.840><c> course</c><00:03:21.080><c> I'm</c>

00:03:21.190 --> 00:03:21.200 align:start position:0%
tell me about myself and of course I'm
 

00:03:21.200 --> 00:03:23.270 align:start position:0%
tell me about myself and of course I'm
sure<00:03:21.440><c> you've</c><00:03:21.680><c> had</c><00:03:21.879><c> experience</c><00:03:22.400><c> of</c><00:03:22.640><c> llm</c>

00:03:23.270 --> 00:03:23.280 align:start position:0%
sure you've had experience of llm
 

00:03:23.280 --> 00:03:25.789 align:start position:0%
sure you've had experience of llm
hallucinations<00:03:24.280><c> going</c><00:03:24.560><c> off</c><00:03:24.720><c> in</c><00:03:25.280><c> Wild</c>

00:03:25.789 --> 00:03:25.799 align:start position:0%
hallucinations going off in Wild
 

00:03:25.799 --> 00:03:27.789 align:start position:0%
hallucinations going off in Wild
directions<00:03:26.440><c> and</c><00:03:26.599><c> this</c><00:03:26.720><c> is</c><00:03:26.879><c> supposed</c><00:03:27.159><c> to</c><00:03:27.319><c> be</c><00:03:27.640><c> a</c>

00:03:27.789 --> 00:03:27.799 align:start position:0%
directions and this is supposed to be a
 

00:03:27.799 --> 00:03:30.030 align:start position:0%
directions and this is supposed to be a
remedy<00:03:28.200><c> for</c><00:03:28.560><c> that</c><00:03:28.920><c> because</c><00:03:29.480><c> you're</c><00:03:29.799><c> giving</c>

00:03:30.030 --> 00:03:30.040 align:start position:0%
remedy for that because you're giving
 

00:03:30.040 --> 00:03:32.550 align:start position:0%
remedy for that because you're giving
GPT<00:03:30.680><c> really</c><00:03:31.000><c> really</c><00:03:31.280><c> specific</c><00:03:31.760><c> context</c><00:03:32.439><c> this</c>

00:03:32.550 --> 00:03:32.560 align:start position:0%
GPT really really specific context this
 

00:03:32.560 --> 00:03:35.190 align:start position:0%
GPT really really specific context this
is<00:03:32.720><c> who</c><00:03:32.920><c> I</c><00:03:33.080><c> am</c><00:03:33.920><c> give</c><00:03:34.120><c> me</c><00:03:34.280><c> something</c><00:03:34.560><c> meaningful</c>

00:03:35.190 --> 00:03:35.200 align:start position:0%
is who I am give me something meaningful
 

00:03:35.200 --> 00:03:36.990 align:start position:0%
is who I am give me something meaningful
about<00:03:35.439><c> it</c><00:03:36.280><c> all</c><00:03:36.400><c> right</c><00:03:36.599><c> guys</c><00:03:36.720><c> and</c><00:03:36.840><c> that's</c>

00:03:36.990 --> 00:03:37.000 align:start position:0%
about it all right guys and that's
 

00:03:37.000 --> 00:03:39.390 align:start position:0%
about it all right guys and that's
pretty<00:03:37.200><c> much</c><00:03:37.319><c> all</c><00:03:37.519><c> there</c><00:03:37.640><c> is</c><00:03:37.840><c> to</c><00:03:38.120><c> it</c><00:03:39.120><c> uh</c><00:03:39.319><c> you</c>

00:03:39.390 --> 00:03:39.400 align:start position:0%
pretty much all there is to it uh you
 

00:03:39.400 --> 00:03:42.390 align:start position:0%
pretty much all there is to it uh you
can<00:03:39.640><c> then</c><00:03:39.920><c> download</c><00:03:40.480><c> as</c><00:03:40.640><c> a</c><00:03:40.879><c> PDF</c><00:03:41.599><c> you</c><00:03:41.720><c> can</c><00:03:41.959><c> also</c>

00:03:42.390 --> 00:03:42.400 align:start position:0%
can then download as a PDF you can also
 

00:03:42.400 --> 00:03:45.190 align:start position:0%
can then download as a PDF you can also
download<00:03:42.959><c> as</c><00:03:43.080><c> a</c><00:03:43.239><c> docx</c><00:03:43.879><c> hope</c><00:03:44.080><c> you</c><00:03:44.239><c> enjoyed</c><00:03:44.959><c> feel</c>

00:03:45.190 --> 00:03:45.200 align:start position:0%
download as a docx hope you enjoyed feel
 

00:03:45.200 --> 00:03:47.949 align:start position:0%
download as a docx hope you enjoyed feel
free<00:03:45.360><c> to</c><00:03:45.480><c> check</c><00:03:45.680><c> out</c><00:03:46.000><c> the</c><00:03:46.159><c> GPT</c><00:03:46.720><c> researcher</c><00:03:47.400><c> app</c>

00:03:47.949 --> 00:03:47.959 align:start position:0%
free to check out the GPT researcher app
 

00:03:47.959 --> 00:03:52.439 align:start position:0%
free to check out the GPT researcher app
and<00:03:48.280><c> thank</c><00:03:48.480><c> you</c><00:03:48.760><c> guys</c><00:03:49.000><c> for</c><00:03:49.439><c> watching</c>

