import os
import logging
import csv
from dotenv import load_dotenv
from supabase import create_client, Client
from supabase.lib.client_options import ClientOptions
import yt_dlp
from tqdm import tqdm
from datetime import datetime, timedelta, timezone
import uuid
from youtube_transcript_api import YouTubeTranscriptApi
import urllib3
import pytz
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Boolean, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import sessionmaker
from rich import print as rprint
from rich.console import Console
from rich.table import Table as RichTable
import warnings
import webvtt
import re
from io import StringIO
import nltk
from nltk.tokenize import sent_tokenize
import time
import sys
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential
import ssl
import certifi
from httpx import Timeout
import asyncio
from rich.logging import RichHandler
from rich.markdown import Markdown
from rich.theme import Theme
from pathlib import Path
from functools import wraps

CHOSEN_WATCHLIST_FILE = "youtube_channels_Watchlist_last_48hours_videos.md"  # youtube_channels_Watchlist_download_test  #youtube_channels_Watchlist_re-fetch_Transcripts.md

# Your existing code starts here
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Ensure NLTK sentence tokenizer is downloaded
nltk.download('punkt', quiet=True)

#option here to run without API
USE_YOUTUBE_API = os.getenv('USE_YOUTUBE_API', 'true').lower() == 'false'

# Add this near the top of the file with other constants
VTT_FOLDER = "aux_vtt_files"

# Add this function to handle the VTT folder creation and cleanup
def setup_vtt_folder():
    """Creates the VTT folder if it doesn't exist and cleans any leftover files"""
    vtt_folder = Path(VTT_FOLDER)
    vtt_folder.mkdir(exist_ok=True)
    
    # Clean any existing .vtt files
    for vtt_file in vtt_folder.glob("*.vtt"):
        try:
            vtt_file.unlink()
        except Exception as e:
            logging.warning(f"Could not delete existing VTT file {vtt_file}: {e}")

"""
This script is designed to fetch youtube videos THAT ARE NOT YET IN THE DATABASE, and save their transcripts and metadata to a Supabase database.
It reads the markdown file "youtube_channels_Watchlist.md" that defines the channels to fetch and a Supabase database to store the metadata.
# Metadata collection explanation

This script collects metadata for YouTube videos using the following process:

1. Channel data is read from a markdown file ("youtube_channels_Watchlist.md") using the `read_channel_data` function.
   - The file contains topics and associated YouTube channel names and IDs.

2. The script uses yt-dlp (a fork of youtube-dl) to fetch video information from each channel.
   - This is likely done in a function not shown in the current file snippet.

3. For each video, the script collects the following metadata:
   - Video ID
   - Channel name
   - Video title
   - Published date
   - Duration
   - Transcript (using YouTubeTranscriptApi)

4. The collected metadata is then saved to a Supabase database.
   - The database has separate tables for different topics (e.g., "youtube_renewable_energy", "youtube_artificial_intelligence", etc.)

5. The script checks for existing videos in the database to avoid duplicates.
   - This is done by comparing the video IDs of newly fetched videos with those already in the database.

6. New videos (those not yet in the database) have their metadata and transcripts saved to the appropriate table in the Supabase database.

The script uses environment variables for Supabase credentials and employs logging for tracking the process and any errors that occur during execution.


"""

# Load environment variables and set up logging
load_dotenv()

# Replace the existing logging configuration with this (after load_dotenv())
def setup_logging():
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Generate filenames with today's date
    today = datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f"youtube_fetcher_{today}.log"
    md_log_file = log_dir / f"youtube_fetcher_{today}.md"
    
    # Custom theme for Rich
    custom_theme = Theme({
        "info": "cyan",
        "warning": "yellow",
        "error": "red bold",
        "critical": "red bold reverse",
    })
    
    # Create Rich console with theme
    console = Console(theme=custom_theme, file=open(md_log_file, "a", encoding="utf-8"))
    
    # Configure logging with both file and Rich handlers
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            RichHandler(console=console, rich_tracebacks=True, markup=True),
            logging.FileHandler(log_file, encoding='utf-8'),
        ]
    )
    
    # Suppress specific loggers
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Add markdown header to the MD file
    with open(md_log_file, "a", encoding="utf-8") as md_file:
        md_file.write(f"""# YouTube Fetcher Log - {datetime.now().strftime('%Y-%m-%d')}

## System Information
- Script Version: V25
- Execution Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python Version: {sys.version.split()[0]}

## Log Entries

""")
    
    return console, md_log_file

# Initialize logging and get console object
console, md_log_file = setup_logging()

# Constants
MAX_VIDEOS = -1  # Set to -1 to fetch all videos, or any positive number to limit

# Comment out these lines to use all tables from the .md file
# TABLES_TO_CHECK = [
#     "youtube_gme",
# ]

# New retry decorator for Supabase API calls

@retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=4, max=10))
def supabase_api_call(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logging.error(f"Supabase API Error: {str(e)}")
            raise
    return wrapper

# Move the Supabase client creation into an async function
async def create_supabase_client():
    url: str = os.getenv('SUPABASE_URL')
    key: str = os.getenv('SERVICE_ROLE_KEY')

    ssl_context = ssl.create_default_context(cafile=certifi.where())
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    conn = aiohttp.TCPConnector(limit=100, force_close=True, enable_cleanup_closed=True, ssl=ssl_context)
    session = aiohttp.ClientSession(connector=conn)

    options = ClientOptions(
        schema="public",
        headers={},
        postgrest_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
        storage_client_timeout=Timeout(connect=30, read=30, write=30, pool=30),
    )

    return create_client(url, key, options=options), session

# Global variable for Supabase client
supabase = None

def read_channel_data(file_name):
    file_path = os.path.join(os.getcwd(), file_name)
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"The file {file_name} does not exist in the current directory.")

    with open(file_path, 'r') as file:
        lines = file.readlines()
    
    channel_data = {}
    current_topic = None
    
    for line in lines:
        line = line.strip()
        if line.startswith('## '):
            current_topic = line[3:]
            channel_data[current_topic] = []
        elif line.startswith('- '):
            parts = line[2:].split(' - ')
            if len(parts) == 2:
                channel_name, channel_id = parts
                channel_data[current_topic].append((channel_name.strip(), channel_id.strip()))
            elif len(parts) == 1:
                channel_id = parts[0].strip()
                channel_data[current_topic].append((None, channel_id))
    
    return channel_data

def check_for_duplicate_ids(channel_data):
    seen_ids = set()
    for topic, channels in channel_data.items():
        for _, channel_id in channels:
            if channel_id in seen_ids:
                logging.error(f"Duplicate channel ID found: {channel_id} in topic {topic}")
                return False
            seen_ids.add(channel_id)
    return True

async def load_existing_video_data(table_name, channel_name):
    try:
        response = supabase.table(table_name) \
            .select("video_id") \
            .eq("channel_name", channel_name) \
            .in_("processed", ["pending", "completed"]) \
            .execute()
        
        return {row['video_id'] for row in response.data}
    except Exception as e:
        logging.error(f"Error loading existing videos: {str(e)}")
        return set()
    
    
def check_channel_name_consistency(channel_data):
    for topic, channels in channel_data.items():
        table_name = f"youtube_{topic.lower().replace(' ', '_')}"
        db_channels = supabase.table(table_name).select("channel_name").execute().data
        db_channel_names = {c['channel_name'] for c in db_channels}
        for channel_name, _ in channels:
            if channel_name and channel_name not in db_channel_names:
                logging.warning(f"Channel name mismatch: '{channel_name}' in .md file not found in database for topic '{topic}'")

def fetch_channel_videos(channel_id, max_videos=MAX_VIDEOS):
    """
    Fetches basic video info - modified to ignore time constraints since we're retrying transcripts
    """
    channel_url = f"https://www.youtube.com/channel/{channel_id}/videos"
    ydl_opts = {
        'extract_flat': 'in_playlist',
        'force_generic_extractor': False,
        'quiet': True,
        'no_warnings': True,
        'ignoreerrors': True,
    }
    
    if max_videos > 0:
        ydl_opts['playlist_items'] = f'1-{max_videos}'
    
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            info = ydl.extract_info(channel_url, download=False)
            if not info or 'entries' not in info:
                logging.error(f"No video information found for channel ID: {channel_id}")
                return [], {}
            
            video_ids = []
            video_info = {}
            for entry in info['entries']:
                if entry:
                    video_id = entry['id']
                    video_ids.append(video_id)
                    video_info[video_id] = {
                        'id': video_id,
                        'title': entry.get('title'),
                        'duration': entry.get('duration'),
                    }
            
            return video_ids, video_info
        except Exception as e:
            logging.error(f"Error fetching videos for channel ID {channel_id}: {str(e)}")
            return [], {}

# Add this function to create SQLAlchemy engine
def get_db_engine():
    return create_engine(os.getenv('DIL_POSTGRES_CONNECTION_STRING'))

# Replace the load_videos_without_transcript function with this SQLAlchemy version
async def load_videos_without_transcript(table_name, channel_name):
    """
    Loads videos that have 'Transcript not available' using SQLAlchemy
    """
    try:
        engine = get_db_engine()
        query = text(f"""
            SELECT video_id, transcript 
            FROM {table_name} 
            WHERE channel_name = :channel_name 
            AND (transcript = 'Transcript not available' OR transcript IS NULL OR transcript = '')
        """)
        
        with engine.connect() as connection:
            result = connection.execute(query, {"channel_name": channel_name})
            videos = [row[0] for row in result]  # row[0] is video_id
        
        logging.info(f"Found {len(videos)} videos without transcript for channel '{channel_name}' in {table_name}")
        print(f"Found {len(videos)} videos without transcript for channel '{channel_name}' in {table_name}")
        return videos
        
    except Exception as e:
        error_msg = f"Error loading videos without transcript for {channel_name}: {str(e)}"
        logging.error(error_msg)
        print(error_msg)
        return []
    finally:
        engine.dispose()

# Modify the compare_videos function to focus on missing transcripts
async def compare_videos(table_name, channel_name, channel_id):
    """
    Modified to focus on videos with missing transcripts rather than new videos
    """
    videos_to_retry = await load_videos_without_transcript(table_name, channel_name)
    
    summary = f"""Transcript retry check for channel: {channel_name} (ID: {channel_id})
Videos with missing transcripts: {len(videos_to_retry)}
Videos to retry: {', '.join(videos_to_retry) if videos_to_retry else 'None'}"""
    
    logging.info(summary)
    return videos_to_retry, {}, summary  # Empty dict as we don't need new video info

def format_transcript(transcript):
    formatted_text = ""
    for segment in transcript:
        start_time = f"{int(segment['start'] // 60):02d}:{int(segment['start'] % 60):02d}"
        formatted_text += f"[{start_time}] {segment['text']}\n"
    
    return {"formatted_text": formatted_text.strip()}

import sys
import logging

# Suppress HTTP request logs
logging.getLogger("httpx").setLevel(logging.WARNING)

def clean_text(text):
    # Remove HTML-like tags and extra spaces
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def find_overlap(text1, text2):
    # Find the longest overlap between the end of text1 and the start of text2
    max_overlap = min(len(text1), len(text2))
    for i in range(max_overlap, 0, -1):
        if text1[-i:] == text2[:i]:
            return i
    return 0

def merge_captions(captions):
    # Merge captions while removing overlaps
    full_text = ""
    for caption in captions:
        clean_caption = clean_text(caption)
        if not full_text:
            full_text = clean_caption
        else:
            overlap_len = find_overlap(full_text, clean_caption)
            non_overlapping_part = clean_caption[overlap_len:]
            full_text += ' ' + non_overlapping_part
    return full_text

def remove_repeated_sentences(text):
    # Tokenize the text into sentences and remove duplicates
    sentences = sent_tokenize(text)
    unique_sentences = []
    seen_sentences = set()
    for sentence in sentences:
        cleaned_sentence = sentence.strip()
        lower_sentence = cleaned_sentence.lower()
        if lower_sentence not in seen_sentences:
            unique_sentences.append(cleaned_sentence)
            seen_sentences.add(lower_sentence)
    return ' '.join(unique_sentences)

# Modify the fetch_transcript_yt_dlp function
def fetch_transcript_yt_dlp(video_id):
    vtt_path = Path(VTT_FOLDER) / f'{video_id}.en.vtt'
    
    ydl_opts = {
        'skip_download': True,
        'writesubtitles': False,
        'writeautomaticsub': True,
        'subtitlesformat': 'vtt',
        'subtitleslangs': ['en'],
        'outtmpl': str(Path(VTT_FOLDER) / f'{video_id}'),  # Save in VTT folder
        'quiet': True,
        'no_warnings': True,
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(f'https://www.youtube.com/watch?v={video_id}', download=False)
            
            # Check if automatic captions are available
            subtitles_available = False
            if 'automatic_captions' in info and info['automatic_captions']:
                for lang in info['automatic_captions']:
                    if lang == 'en':
                        subtitles_available = True
                        break

            if not subtitles_available:
                logging.warning(f"No automatic English subtitles available for video {video_id}")
                return None
            
            # Download the automatic subtitles
            ydl.download([f'https://www.youtube.com/watch?v={video_id}'])
            
            if vtt_path.exists():
                with open(vtt_path, 'r', encoding='utf-8') as vtt_file:
                    vtt_content = vtt_file.read()
                
                vtt_file = webvtt.read_buffer(StringIO(vtt_content))
                captions = [caption.text for caption in vtt_file]
                full_text = merge_captions(captions)
                
                # Clean the transcript
                cleaned_transcript = remove_repeated_sentences(full_text)
                
                # Clean up VTT file after use
                vtt_path.unlink()
                
                return {"formatted_text": cleaned_transcript}
            else:
                logging.error(f"VTT file not created for video {video_id}")
                return None
                
    except Exception as e:
        logging.error(f"Error in yt-dlp processing for video {video_id}: {str(e)}")
        # Clean up VTT file if it exists, even if processing failed
        if vtt_path.exists():
            vtt_path.unlink()
        return None

# Also update the fetch_and_save_metadata function to use SQLAlchemy for updates
async def fetch_and_save_metadata(table_name, channel_name, channel_id, retry_videos):
    """
    Modified to focus on updating transcripts for existing videos using SQLAlchemy
    """
    logging.info(f"Processing channel: '{channel_name}'")
    print(f"Found {len(retry_videos)} videos to retry for channel: {channel_name}")
    
    successful_updates = 0
    failed_updates = 0
    engine = get_db_engine()
    
    for index, video_id in enumerate(retry_videos, start=1):
        try:
            # Try to fetch transcript
            transcript = None
            print(f"\nTrying to fetch transcript for video {video_id}...")
            
            # First try with yt-dlp
            transcript = fetch_transcript_yt_dlp(video_id)
            if transcript:
                print(f"Successfully fetched transcript with yt-dlp for {video_id}")
            
            # If yt-dlp fails and YouTube API is enabled, try that
            if transcript is None and USE_YOUTUBE_API:
                try:
                    print(f"Trying YouTube API for {video_id}...")
                    yt_transcript = YouTubeTranscriptApi.get_transcript(
                        video_id, 
                        languages=['en', 'pt', 'es', 'fr', 'sv', 'zh-Hans', 'it', 'pl', 'ru', 'ar']
                    )
                    transcript = format_transcript(yt_transcript)
                    transcript['formatted_text'] = remove_repeated_sentences(transcript['formatted_text'])
                    print(f"Successfully fetched transcript with YouTube API for {video_id}")
                except Exception as e:
                    print(f"YouTube API transcript fetch failed for {video_id}: {str(e)}")
            
            # Update the database if we got a transcript
            if transcript and transcript.get('formatted_text'):
                try:
                    update_query = text(f"""
                        UPDATE {table_name}
                        SET transcript = :transcript,
                            processed = 'pending'
                        WHERE video_id = :video_id
                    """)
                    
                    with engine.begin() as connection:
                        connection.execute(
                            update_query,
                            {
                                "transcript": transcript['formatted_text'],
                                "video_id": video_id
                            }
                        )
                    successful_updates += 1
                    print(f"Successfully updated transcript for video {video_id}")
                except Exception as e:
                    print(f"Failed to update database for {video_id}: {str(e)}")
                    failed_updates += 1
            else:
                print(f"Could not fetch transcript for {video_id}")
                failed_updates += 1
            
            # Progress update
            progress = (index / len(retry_videos)) * 100
            print(f"[Channel: {channel_name}] Progress: {progress:.2f}% | Video {index}/{len(retry_videos)} | ID: {video_id}")
            
        except Exception as e:
            error_msg = f"Error processing video {video_id}: {str(e)}"
            logging.error(error_msg)
            print(error_msg)
            failed_updates += 1
        
        # Small delay between requests
        await asyncio.sleep(0.5)
    
    summary = f"""
Completed processing for {channel_name}:
- Total videos processed: {len(retry_videos)}
- Successfully updated: {successful_updates}
- Failed updates: {failed_updates}
"""
    print(summary)
    logging.info(summary)
    
    engine.dispose()

def create_table_if_not_exists(table_name):
    engine = create_engine(os.getenv('DIL_POSTGRES_CONNECTION_STRING'))
    metadata = MetaData()

    try:
        # Check if the table exists
        with engine.connect() as connection:
            if not engine.dialect.has_table(connection, table_name):
                # Table doesn't exist, so create it
                table = Table(table_name, metadata,
                    Column('id', UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
                    Column('channel_name', String),
                    Column('video_id', String, unique=True, nullable=False),
                    Column('title', String),
                    Column('published_at', DateTime(timezone=True)),
                    Column('duration', Integer),
                    Column('transcript', String),
                    Column('summary', String),
                    Column('keywords', JSON),
                    Column('visual_description', String),
                    Column('llm_response', JSONB),
                    Column('llm_call_date', DateTime(timezone=True)),
                    Column('processed', String, check="processed IN ('pending', 'completed', 'error', 'skipped')"),
                    Column('created_at', DateTime(timezone=True), server_default=text('CURRENT_TIMESTAMP'))
                )
                
                metadata.create_all(engine)
                print(f"Table {table_name} created successfully.")
            else:
                print(f"Table {table_name} already exists.")

        # Enable RLS for the table (whether it's new or existing)
        Session = sessionmaker(bind=engine)
        with Session() as session:
            enable_rls_for_table(session, table_name)
    except Exception as e:
        print(f"Error creating table {table_name}: {str(e)}")
    finally:
        engine.dispose()

def enable_rls_for_table(session, table_name):
    try:
        session.execute(text(f"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;"))
        session.commit()
        print(f"RLS enabled for table {table_name}")
    except Exception as e:
        print(f"Error enabling RLS for table {table_name}: {str(e)}")
        session.rollback()

def print_table(title, data, columns):
    # Create Rich table as before
    table = RichTable(title=title)
    for column in columns:
        table.add_column(column, style="cyan")
    for row in data:
        table.add_row(*[str(item) for item in row])
    
    # Print to console
    console.print(table)
    
    # Create markdown version of the table
    md_table = f"### {title}\n\n|" + "|".join(columns) + "|\n|" + "|".join(["---" for _ in columns]) + "|\n"
    for row in data:
        md_table += "|" + "|".join(str(item) for item in row) + "|\n"
    
    # Fix: Change md_file to md_log_file
    log_to_markdown(md_log_file, "TABLE", md_table)

def generate_table_names(channel_data):
    return [f"youtube_{topic.lower().replace(' ', '_')}" for topic in channel_data.keys()]

def get_tables_to_check(channel_data):
    if 'TABLES_TO_CHECK' in globals():
        return globals()['TABLES_TO_CHECK']
    else:
        return generate_table_names(channel_data)

def validate_processed_status(status):
    """Validate that a processed status is one of the allowed values."""
    valid_statuses = {'pending', 'completed', 'error', 'skipped'}
    if status not in valid_statuses:
        raise ValueError(f"Invalid processed status: {status}. Must be one of {valid_statuses}")
    return status

# Add this function after setup_logging() and before other functions
def log_to_markdown(md_file_path, level, message):
    """
    Logs a message to the markdown file with timestamp and level.
    
    Args:
        md_file_path (Path): Path to the markdown log file
        level (str): Log level (INFO, ERROR, TABLE, etc.)
        message (str): The message to log
    """
    timestamp = datetime.now().strftime('%H:%M:%S')
    with open(md_file_path, "a", encoding="utf-8") as f:
        f.write(f"### {timestamp} - {level}\n")
        f.write(f"{message}\n\n")

# Modify the main function
async def main():
    global supabase
    session = None
    
    logging.info("=== Starting YouTube Transcript Retry Script ===")
    print("Starting YouTube Transcript Retry Script...")
    
    try:
        # Setup VTT folder at start
        setup_vtt_folder()
        
        supabase, session = await create_supabase_client()
        console, md_log_file = setup_logging()
        
        print(f"Reading channel data from {CHOSEN_WATCHLIST_FILE}...")
        channel_data = read_channel_data(CHOSEN_WATCHLIST_FILE)
        print(f"Found {len(channel_data)} topics in channel data")
        
        if not check_for_duplicate_ids(channel_data):
            logging.error("Duplicate channel IDs found. Exiting.")
            return
        
        tables_to_check = get_tables_to_check(channel_data)
        print(f"Will process these tables: {tables_to_check}")
        
        for topic, channels in channel_data.items():
            table_name = f"youtube_{topic.lower().replace(' ', '_')}"
            print(f"\nProcessing topic: {topic} (table: {table_name})")
            
            if table_name not in tables_to_check:
                print(f"Skipping table: {table_name}")
                continue
            
            print(f"Found {len(channels)} channels for topic {topic}")
            
            for channel_name, channel_id in channels:
                print(f"\nProcessing channel: {channel_name} ({channel_id})")
                try:
                    retry_videos, _, summary = await compare_videos(table_name, channel_name, channel_id)
                    print(summary)
                    
                    if retry_videos:
                        print(f"Found {len(retry_videos)} videos to retry for {channel_name}")
                        await fetch_and_save_metadata(table_name, channel_name, channel_id, retry_videos)
                    else:
                        print(f"No videos to retry for {channel_name}")
                    
                    await asyncio.sleep(1)  # Delay between channels
                    
                except Exception as e:
                    error_msg = f"Error processing channel {channel_name}: {str(e)}"
                    logging.error(error_msg)
                    print(error_msg)
                    continue
    
    except Exception as e:
        error_msg = f"Critical error: {str(e)}"
        logging.error(error_msg)
        print(error_msg)
        raise
    finally:
        if session:
            await session.close()
        
        # Clean up VTT folder at end
        try:
            for vtt_file in Path(VTT_FOLDER).glob("*.vtt"):
                try:
                    vtt_file.unlink()
                except Exception as e:
                    logging.warning(f"Could not delete VTT file {vtt_file} during cleanup: {e}")
        except Exception as e:
            logging.error(f"Error during final VTT cleanup: {e}")

async def run_script(script_name):
    print(f"Starting {script_name}...")
    sys.stdout.flush()
    
    try:
        # Use '-u' flag for unbuffered output and set PYTHONUNBUFFERED to '1'
        process = await asyncio.create_subprocess_exec(
            sys.executable, '-u', script_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env=dict(os.environ, PYTHONUNBUFFERED='1')
        )

        async def read_stream(stream, prefix):
            try:
                while True:
                    data = await stream.read(1024)
                    if not data:
                        break
                    print(f"{prefix}: {data.decode()}", end='', flush=True)
            except Exception as e:
                logging.error(f"Error reading stream: {e}")

        # Run both stdout and stderr readers concurrently
        await asyncio.gather(
            read_stream(process.stdout, f"{script_name} [OUT]"),
            read_stream(process.stderr, f"{script_name} [ERR]")
        )

        return_code = await process.wait()
        print(f"{script_name} completed with return code {return_code}", flush=True)
    finally:
        # Ensure proper cleanup
        if process.stdout:
            process.stdout.close()
        if process.stderr:
            process.stderr.close()
        await process.wait()



# Modify the script execution
if __name__ == "__main__":
    try:
        # Create and get event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Run the main function
        loop.run_until_complete(main())
        
        # Add a small delay before running additional scripts
        loop.run_until_complete(asyncio.sleep(2))
        

    except Exception as e:
        error_msg = f"Critical error in main process: {str(e)}"
        logging.error(error_msg)
        log_to_markdown(md_log_file, "ERROR", f"```\n{error_msg}\n```")
        sys.exit(1)
    finally:
        # Proper cleanup of the event loop
        try:
            pending = asyncio.all_tasks(loop)
            loop.run_until_complete(asyncio.gather(*pending))
        except Exception:
            pass
        
        # Close the loop
        loop.close()
        
        # Final completion message
        print("\n=== Script Execution Completed ===")
