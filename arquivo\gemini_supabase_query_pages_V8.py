import os
import asyncio
import json
import time
from datetime import datetime, timedelta, timezone
from groq import Groq
from supabase import create_client, Client
from supabase.client import ClientOptions
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
import logging
from collections import deque
from llama31_promptsV2 import prompt_template
import google.generativeai as genai
import grpc._cython.cygrpc as _cygrpc
from grpc import aio

load_dotenv()

# Constants
ROWS_PER_TABLE = 10000
MAX_CONCURRENT_REQUESTS = 6
MAX_PROCESSING_ATTEMPTS = 4
MAX_PROMPT_TOKENS = 1000000  # 1 million tokens for Gemini input
SAFETY_MARGIN_TOKENS = 1000
MAX_TOKENS_PER_MINUTE = 110000
MAX_TOKENS_PER_DAY = 900000
MAX_REQUESTS_PER_MINUTE = 30

tables_to_process = [
    "youtube_artificial_intelligence", "youtube_renewable_energy", "youtube_gme",
    "youtube_sustainability", "youtube_startups", "youtube_financial_markets",
    "youtube_general", "youtube_legal"
]

# Define schemas
initial_response_schema = {
    "type": "object",
    "properties": {
        "keywords": {
            "type": "array",
            "items": {"type": "string"}
        },
        "questions": {
            "type": "array",
            "items": {"type": "string"}
        },
        "insights": {
            "type": "array",
            "items": {"type": "string"}
        },
        "recommendations": {
            "type": "array",
            "items": {"type": "string"}
        }
    },
    "required": ["keywords", "questions", "insights", "recommendations"]
}

# Initialize clients
supabase: Client = create_client(
    os.environ.get("SUPABASE_URL"),
    os.environ.get("SERVICE_ROLE_KEY"),
    options=ClientOptions(
        schema="public",
        headers={},
        auto_refresh_token=True,
        persist_session=True,
        storage=None,
        realtime=None,
        postgrest_client_timeout=3600,
        storage_client_timeout=3600,
        flow_type=None
    )
)

# Disable Supabase HTTP request logging
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)

# Token counting and rate limiting
token_usage_queue = deque()
request_timestamps = deque()
daily_token_usage = 0
daily_token_reset_time = time.time()

# Update Gemini configuration
genai.configure(api_key=os.environ.get("GOOGLE_API_KEY"))
gemini_model = genai.GenerativeModel('gemini-1.5-flash-002',
    generation_config=genai.types.GenerationConfig(
        max_output_tokens=8192,
        temperature=0.05,
        top_p=0.95,
        top_k=40,
        response_mime_type="application/json"
    )
)

# Function to count tokens
def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))

# Function to truncate the transcript
def truncate_transcript(transcript, max_tokens, additional_text=""):
    max_tokens_with_margin = max_tokens - SAFETY_MARGIN_TOKENS - count_tokens(additional_text)
    tokens = count_tokens(transcript)
    if tokens <= max_tokens_with_margin:
        return transcript
    else:
        truncated_transcript = transcript[:max_tokens_with_margin * 4]  # Approximate char to token ratio
        while count_tokens(truncated_transcript) > max_tokens_with_margin:
            truncated_transcript = truncated_transcript[:-1]
        return truncated_transcript

# Function to query Gemini
async def query_gemini(prompt, schema):
    try:
        response = await gemini_model.generate_content_async(
            prompt,
            generation_config=genai.types.GenerationConfig(
                response_schema=schema
            )
        )
        result = response.text
        print(f"Gemini result: {result}")  # Log the result
        return result
    except Exception as e:
        print(f"An error occurred with Gemini API: {str(e)}")
        raise

# Add these helper functions after the existing imports
def sanitize_text(text):
    """Sanitize text to ensure UTF-8 compatibility"""
    if not isinstance(text, str):
        return str(text)
    try:
        # Try to encode then decode to catch any invalid characters
        return text.encode('utf-8', errors='ignore').decode('utf-8')
    except UnicodeError:
        return text.encode('ascii', errors='ignore').decode('ascii')

# Function to process a row
async def process_row(row, table_name):
    if row['processed'] == "completed":
        print(f"Skipping already processed video: {sanitize_text(row['title'])}")
        return {'processed': "completed"}

    if row['transcript'] != "Transcript not available":
        for attempt in range(MAX_PROCESSING_ATTEMPTS):
            # Sanitize all text inputs
            sanitized_transcript = sanitize_text(row['transcript'])
            sanitized_channel = sanitize_text(row['channel_name'])
            sanitized_title = sanitize_text(row['title'])
            
            truncated_transcript = truncate_transcript(sanitized_transcript, MAX_PROMPT_TOKENS)
            prompt = prompt_template.format(
                channel_name=sanitized_channel,
                title=sanitized_title,
                published_at=row['published_at'],
                transcript=truncated_transcript
            )
            
            print(f"\nProcessing video: {sanitized_title}")
            print(f"Video ID: {row['video_id']}")
            print(f"Attempt: {attempt + 1}")
            
            try:
                llm_response = await query_gemini(prompt, initial_response_schema)
                # Sanitize the response before storing
                sanitized_response = sanitize_text(llm_response)
                current_datetime = datetime.now().isoformat()
                
                supabase.table(table_name).update({
                    "llm_response": sanitized_response,
                    "llm_call_date": current_datetime,
                    "processed": "completed"
                }).eq("id", row['id']).execute()
                
                print(f"Successfully processed video_id: {row['video_id']}")
                return {'processed': "completed"}
            
            except Exception as e:
                print(f"Error processing video_id: {row['video_id']} (Attempt {attempt + 1}): {str(e)}")
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    current_datetime = datetime.now().isoformat()
                    supabase.table(table_name).update({
                        "llm_response": f"Error: Failed after {MAX_PROCESSING_ATTEMPTS} attempts. Last error: {str(e)}",
                        "llm_call_date": current_datetime,
                        "processed": "error"  # Use the correct ENUM value
                    }).eq("id", row['id']).execute()
                    print(f"Marked video_id: {row['video_id']} as Error after {MAX_PROCESSING_ATTEMPTS} attempts")
                    return {'processed': "error"}
                continue

    else:
        print(f"Skipping video with no transcript: {row['title']}")
        return {'processed': "pending"}

# Function to get the timestamp for 48 hours ago
def get_48_hours_ago():
    date_48_hours_ago = datetime.now(timezone.utc) - timedelta(hours=48)
    return date_48_hours_ago.isoformat()

# Modify the cleanup function
async def cleanup():
    """Gracefully cleanup resources"""
    try:
        # Cancel all pending tasks except the current one
        current_task = asyncio.current_task()
        pending = [task for task in asyncio.all_tasks() 
                  if task is not current_task]
        
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            except Exception as e:
                print(f"Error cancelling task: {e}")
        
        # Properly shutdown gRPC
        try:
            if hasattr(_cygrpc, '_actual_aio_shutdown'):
                _cygrpc._actual_aio_shutdown = lambda: None
        except Exception:
            pass
                
    except Exception as e:
        print(f"Cleanup error: {e}")

# Add this cleanup function
async def cleanup_grpc():
    """Gracefully cleanup gRPC resources"""
    try:
        # Patch the problematic shutdown function
        if hasattr(_cygrpc, '_actual_aio_shutdown'):
            _cygrpc._actual_aio_shutdown = lambda: None
            
        # Cancel all pending tasks
        pending = asyncio.all_tasks()
        for task in pending:
            if not task.done():
                task.cancel()
                try:
                    await asyncio.wait_for(task, timeout=1.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    pass
    except Exception as e:
        print(f"gRPC cleanup error: {e}")

# Main function
async def main():
    try:
        for table_name in tables_to_process:
            print(f"Processing table: {table_name}")
            
            # Get the timestamp for 48 hours ago
            cutoff_time = get_48_hours_ago()
            
            # Fetch unprocessed rows from the table, published in the last 48 hours
            response = supabase.table(table_name).select("*").eq("processed", "pending").gte("published_at", cutoff_time).execute()
            rows = response.data
            
            if not rows:
                print(f"No unprocessed rows found in table: {table_name} for the last 48 hours")
                continue
            
            # Process each row
            for row in rows:
                try:
                    result = await process_row(row, table_name)
                    print(f"Row processing result: {result}")
                except Exception as e:
                    print(f"Error processing row {row.get('id', 'unknown')}: {e}")
                    continue
    except Exception as e:
        print(f"Error in main execution: {e}")
        raise
    finally:
        await cleanup()

# Modified script execution
if __name__ == "__main__":
    try:
        # Initialize gRPC async
        aio.init_grpc_aio()
        
        # Create and set event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Run main function
        loop.run_until_complete(main())
        
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Error during execution: {e}")
    finally:
        try:
            # Cleanup gRPC
            loop.run_until_complete(cleanup_grpc())
            
            # Close the loop
            loop.close()
            
            # Shutdown gRPC
            aio.shutdown_grpc_aio()
        except Exception as e:
            print(f"Error during final cleanup: {e}")
