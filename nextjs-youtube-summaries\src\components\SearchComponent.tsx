'use client'

import { useState } from 'react'
import { Search, Loader2, X } from 'lucide-react'
import { SearchResult } from '@/types'
import { VideoCard } from './VideoCard'

interface SearchComponentProps {
  selectedTopics?: string[]
  onResultsChange?: (results: SearchResult[]) => void
}

export function SearchComponent({ selectedTopics = [], onResultsChange }: SearchComponentProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)

  const handleSearch = async (searchQuery: string = query) => {
    if (!searchQuery.trim()) return

    setIsLoading(true)
    setHasSearched(true)

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: searchQuery.trim(),
          topicFilters: selectedTopics,
          limit: 20
        })
      })

      if (!response.ok) throw new Error('Search failed')

      const data = await response.json()
      setResults(data.results || [])
      onResultsChange?.(data.results || [])
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
      onResultsChange?.([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch()
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setHasSearched(false)
    onResultsChange?.([])
  }

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search YouTube video summaries..."
            className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />          {query && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              aria-label="Clear search"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
        
        {selectedTopics.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-1">
            <span className="text-sm text-gray-600">Searching in:</span>
            {selectedTopics.map((topic) => (
              <span
                key={topic}
                className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
              >
                {topic}
              </span>
            ))}
          </div>
        )}
      </form>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Searching videos...</span>
        </div>
      )}

      {/* Search Results */}
      {hasSearched && !isLoading && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Search Results
            </h3>
            <span className="text-sm text-gray-600">
              {results.length} video{results.length !== 1 ? 's' : ''} found
            </span>
          </div>

          {results.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No videos found for your search.</p>
              <p className="text-sm mt-2">Try different keywords or adjust your topic filters.</p>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {results.map((result) => (
                <div key={result.video.id} className="relative">
                  <VideoCard video={result.video} showTopic />
                  {result.similarity_score && (
                    <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                      {Math.round(result.similarity_score * 100)}% match
                    </div>
                  )}
                  {result.relevant_transcript_chunks && result.relevant_transcript_chunks.length > 0 && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                      <p className="text-xs text-gray-600 mb-1">Relevant excerpt:</p>                      <p className="text-sm text-gray-800 italic">
                        &ldquo;{result.relevant_transcript_chunks[0].substring(0, 150)}...&rdquo;
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
