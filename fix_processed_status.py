import os
import asyncio
import json
from supabase import create_client, Client
from dotenv import load_dotenv
from rich import print
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from datetime import datetime

load_dotenv()

console = Console()

# Initialize Supabase client - Using the simpler connection approach
supabase = create_client(
    os.environ.get("SUPABASE_URL"),
    os.environ.get("SERVICE_ROLE_KEY")
)

# Tables to process
TABLES = [
    "youtube_artificial_intelligence",
    "youtube_renewable_energy",
    "youtube_gme",
    "youtube_sustainability",
    "youtube_startups",
    "youtube_financial_markets",
    "youtube_general",
    "youtube_legal"
]

async def process_table(table_name):
    print(f"\nProcessing table: {table_name}")
    
    try:
        # Get all rows with "impossible" status
        response = supabase.table(table_name)\
            .select("*")\
            .eq("processed", "impossible")\
            .execute()
        
        rows = response.data
        print(f"Found {len(rows)} rows with 'impossible' status in {table_name}")
        
        processed = 0
        errors = 0
        
        for row in rows:
            try:
                # Update the status to "pending"
                supabase.table(table_name).update({
                    "processed": "pending"
                }).eq("id", row['id']).execute()
                
                processed += 1
                print(f"Updated video: {row['video_id']} - {row['title'][:50]}...")
                
            except Exception as e:
                print(f"Error processing video {row['video_id']}: {str(e)}")
                errors += 1
            
            await asyncio.sleep(0.1)  # Small delay between rows
        
        print(f"\nStats for {table_name}:")
        print(f"Processed: {processed}")
        print(f"Errors: {errors}")
        print(f"Total rows: {len(rows)}")
        
    except Exception as e:
        print(f"Error processing table {table_name}: {str(e)}")

async def main():
    start_time = datetime.now()
    print("[bold green]Starting Process Status Fix[/bold green]")
    
    for table in TABLES:
        await process_table(table)
    
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\nTotal time taken: {duration}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
    except Exception as e:
        print(f"Critical error: {str(e)}") 