WEBVTT
Kind: captions
Language: en

00:00:01.410 --> 00:00:06.829 align:start position:0%
 
[Music]

00:00:06.829 --> 00:00:06.839 align:start position:0%
 
 

00:00:06.839 --> 00:00:09.990 align:start position:0%
 
hey<00:00:07.080><c> everyone</c><00:00:07.680><c> welcome</c><00:00:08.280><c> to</c><00:00:08.559><c> quadrant</c><00:00:09.360><c> I'm</c><00:00:09.559><c> sun</c>

00:00:09.990 --> 00:00:10.000 align:start position:0%
hey everyone welcome to quadrant I'm sun
 

00:00:10.000 --> 00:00:11.549 align:start position:0%
hey everyone welcome to quadrant I'm sun
pankush<00:00:10.599><c> and</c><00:00:10.759><c> today</c><00:00:11.040><c> we</c><00:00:11.120><c> are</c><00:00:11.240><c> going</c><00:00:11.360><c> to</c>

00:00:11.549 --> 00:00:11.559 align:start position:0%
pankush and today we are going to
 

00:00:11.559 --> 00:00:13.870 align:start position:0%
pankush and today we are going to
discuss<00:00:12.080><c> about</c><00:00:12.719><c> one</c><00:00:12.840><c> of</c><00:00:12.960><c> the</c><00:00:13.120><c> most</c><00:00:13.360><c> trending</c>

00:00:13.870 --> 00:00:13.880 align:start position:0%
discuss about one of the most trending
 

00:00:13.880 --> 00:00:16.830 align:start position:0%
discuss about one of the most trending
topics<00:00:14.360><c> in</c><00:00:14.599><c> Vector</c><00:00:15.000><c> databases</c><00:00:15.759><c> and</c><00:00:16.000><c> search</c>

00:00:16.830 --> 00:00:16.840 align:start position:0%
topics in Vector databases and search
 

00:00:16.840 --> 00:00:19.189 align:start position:0%
topics in Vector databases and search
that<00:00:17.000><c> is</c><00:00:17.520><c> retrieval</c><00:00:18.039><c> augmented</c><00:00:18.560><c> generation</c>

00:00:19.189 --> 00:00:19.199 align:start position:0%
that is retrieval augmented generation
 

00:00:19.199 --> 00:00:22.790 align:start position:0%
that is retrieval augmented generation
or<00:00:19.640><c> RG</c><00:00:20.640><c> we</c><00:00:20.760><c> will</c><00:00:21.279><c> demonstrate</c><00:00:21.880><c> a</c><00:00:22.080><c> streamlined</c>

00:00:22.790 --> 00:00:22.800 align:start position:0%
or RG we will demonstrate a streamlined
 

00:00:22.800 --> 00:00:25.470 align:start position:0%
or RG we will demonstrate a streamlined
implementation<00:00:23.560><c> of</c><00:00:23.720><c> R</c><00:00:24.279><c> pipeline</c><00:00:24.760><c> using</c><00:00:25.119><c> only</c>

00:00:25.470 --> 00:00:25.480 align:start position:0%
implementation of R pipeline using only
 

00:00:25.480 --> 00:00:29.109 align:start position:0%
implementation of R pipeline using only
quadrant<00:00:26.279><c> and</c><00:00:26.519><c> open</c><00:00:26.880><c> AI</c><00:00:27.480><c> SDK</c><00:00:28.480><c> so</c><00:00:28.679><c> the</c><00:00:28.840><c> question</c>

00:00:29.109 --> 00:00:29.119 align:start position:0%
quadrant and open AI SDK so the question
 

00:00:29.119 --> 00:00:32.910 align:start position:0%
quadrant and open AI SDK so the question
is<00:00:29.400><c> what</c><00:00:29.599><c> is</c><00:00:30.000><c> R</c><00:00:31.000><c> solms</c><00:00:31.880><c> have</c><00:00:32.200><c> exploded</c>

00:00:32.910 --> 00:00:32.920 align:start position:0%
is what is R solms have exploded
 

00:00:32.920 --> 00:00:35.110 align:start position:0%
is what is R solms have exploded
recently<00:00:33.559><c> and</c><00:00:33.760><c> thus</c><00:00:34.000><c> the</c><00:00:34.160><c> solution</c><00:00:34.600><c> to</c><00:00:34.800><c> their</c>

00:00:35.110 --> 00:00:35.120 align:start position:0%
recently and thus the solution to their
 

00:00:35.120 --> 00:00:37.709 align:start position:0%
recently and thus the solution to their
problems<00:00:35.920><c> Vector</c><00:00:36.280><c> databases</c><00:00:37.000><c> serves</c><00:00:37.440><c> as</c><00:00:37.600><c> a</c>

00:00:37.709 --> 00:00:37.719 align:start position:0%
problems Vector databases serves as a
 

00:00:37.719 --> 00:00:40.990 align:start position:0%
problems Vector databases serves as a
longterm<00:00:38.239><c> memory</c><00:00:38.640><c> to</c><00:00:38.879><c> these</c><00:00:39.120><c> llms</c><00:00:40.000><c> and</c><00:00:40.200><c> R</c><00:00:40.879><c> is</c>

00:00:40.990 --> 00:00:41.000 align:start position:0%
longterm memory to these llms and R is
 

00:00:41.000 --> 00:00:43.709 align:start position:0%
longterm memory to these llms and R is
the<00:00:41.280><c> method</c><00:00:41.680><c> to</c><00:00:41.920><c> retrieve</c><00:00:42.480><c> information</c><00:00:43.440><c> from</c>

00:00:43.709 --> 00:00:43.719 align:start position:0%
the method to retrieve information from
 

00:00:43.719 --> 00:00:45.790 align:start position:0%
the method to retrieve information from
external<00:00:44.160><c> knowledge</c><00:00:44.600><c> base</c><00:00:45.120><c> for</c><00:00:45.320><c> the</c><00:00:45.440><c> user</c>

00:00:45.790 --> 00:00:45.800 align:start position:0%
external knowledge base for the user
 

00:00:45.800 --> 00:00:49.430 align:start position:0%
external knowledge base for the user
query<00:00:46.600><c> it</c><00:00:46.840><c> reduces</c><00:00:47.760><c> the</c><00:00:47.960><c> cases</c><00:00:48.320><c> of</c><00:00:48.520><c> llms</c>

00:00:49.430 --> 00:00:49.440 align:start position:0%
query it reduces the cases of llms
 

00:00:49.440 --> 00:00:51.590 align:start position:0%
query it reduces the cases of llms
giving<00:00:49.760><c> making</c><00:00:50.079><c> up</c><00:00:50.399><c> answers</c><00:00:50.920><c> that</c><00:00:51.160><c> is</c>

00:00:51.590 --> 00:00:51.600 align:start position:0%
giving making up answers that is
 

00:00:51.600 --> 00:00:54.110 align:start position:0%
giving making up answers that is
hallucination<00:00:52.600><c> and</c><00:00:52.840><c> grounds</c><00:00:53.320><c> the</c><00:00:53.440><c> llm</c><00:00:53.960><c> to</c>

00:00:54.110 --> 00:00:54.120 align:start position:0%
hallucination and grounds the llm to
 

00:00:54.120 --> 00:00:57.310 align:start position:0%
hallucination and grounds the llm to
factually<00:00:54.640><c> correct</c><00:00:55.359><c> answer</c><00:00:56.359><c> so</c><00:00:56.559><c> let's</c><00:00:56.840><c> dive</c>

00:00:57.310 --> 00:00:57.320 align:start position:0%
factually correct answer so let's dive
 

00:00:57.320 --> 00:01:01.389 align:start position:0%
factually correct answer so let's dive
in<00:00:57.520><c> the</c><00:00:57.760><c> demo</c><00:00:58.280><c> that</c><00:00:58.440><c> we</c><00:00:58.760><c> have</c><00:00:59.239><c> of</c><00:00:59.399><c> r</c><00:01:00.160><c> using</c>

00:01:01.389 --> 00:01:01.399 align:start position:0%
in the demo that we have of r using
 

00:01:01.399 --> 00:01:04.229 align:start position:0%
in the demo that we have of r using
quadrants<00:01:02.399><c> so</c><00:01:02.680><c> before</c><00:01:02.960><c> moving</c><00:01:03.359><c> on</c><00:01:03.680><c> to</c><00:01:04.040><c> the</c>

00:01:04.229 --> 00:01:04.239 align:start position:0%
quadrants so before moving on to the
 

00:01:04.239 --> 00:01:07.149 align:start position:0%
quadrants so before moving on to the
walkth<00:01:04.640><c> through</c><00:01:04.960><c> Let's</c><00:01:05.360><c> uh</c><00:01:05.640><c> let's</c><00:01:06.080><c> discuss</c><00:01:06.880><c> uh</c>

00:01:07.149 --> 00:01:07.159 align:start position:0%
walkth through Let's uh let's discuss uh
 

00:01:07.159 --> 00:01:11.070 align:start position:0%
walkth through Let's uh let's discuss uh
important<00:01:07.720><c> terms</c><00:01:08.479><c> and</c><00:01:08.720><c> the</c><00:01:08.880><c> data</c><00:01:09.240><c> flow</c><00:01:10.240><c> so</c><00:01:10.920><c> the</c>

00:01:11.070 --> 00:01:11.080 align:start position:0%
important terms and the data flow so the
 

00:01:11.080 --> 00:01:13.429 align:start position:0%
important terms and the data flow so the
three<00:01:11.400><c> most</c><00:01:11.680><c> important</c><00:01:12.080><c> terms</c><00:01:12.520><c> are</c><00:01:12.759><c> the</c><00:01:12.920><c> query</c>

00:01:13.429 --> 00:01:13.439 align:start position:0%
three most important terms are the query
 

00:01:13.439 --> 00:01:16.510 align:start position:0%
three most important terms are the query
the<00:01:13.640><c> context</c><00:01:14.320><c> and</c><00:01:14.560><c> the</c><00:01:14.880><c> prompt</c><00:01:15.880><c> query</c><00:01:16.159><c> is</c><00:01:16.280><c> the</c>

00:01:16.510 --> 00:01:16.520 align:start position:0%
the context and the prompt query is the
 

00:01:16.520 --> 00:01:19.510 align:start position:0%
the context and the prompt query is the
question<00:01:17.040><c> asked</c><00:01:17.400><c> by</c><00:01:17.600><c> the</c><00:01:17.759><c> user</c><00:01:18.479><c> as</c><00:01:18.920><c> context</c><00:01:19.400><c> is</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
question asked by the user as context is
 

00:01:19.520 --> 00:01:22.069 align:start position:0%
question asked by the user as context is
a<00:01:19.720><c> piece</c><00:01:19.960><c> of</c><00:01:20.280><c> information</c><00:01:21.280><c> relevant</c><00:01:21.720><c> to</c><00:01:21.920><c> the</c>

00:01:22.069 --> 00:01:22.079 align:start position:0%
a piece of information relevant to the
 

00:01:22.079 --> 00:01:25.429 align:start position:0%
a piece of information relevant to the
user's<00:01:22.840><c> query</c><00:01:23.840><c> uh</c><00:01:24.479><c> retrieve</c><00:01:25.159><c> from</c><00:01:25.320><c> the</c>

00:01:25.429 --> 00:01:25.439 align:start position:0%
user's query uh retrieve from the
 

00:01:25.439 --> 00:01:28.270 align:start position:0%
user's query uh retrieve from the
knowledge<00:01:25.840><c> base</c><00:01:26.640><c> this</c><00:01:26.799><c> is</c><00:01:26.920><c> the</c><00:01:27.400><c> uh</c><00:01:27.560><c> data</c><00:01:27.840><c> flow</c>

00:01:28.270 --> 00:01:28.280 align:start position:0%
knowledge base this is the uh data flow
 

00:01:28.280 --> 00:01:31.109 align:start position:0%
knowledge base this is the uh data flow
the<00:01:28.439><c> user</c><00:01:28.880><c> ask</c><00:01:29.119><c> the</c><00:01:29.360><c> query</c><00:01:30.040><c> and</c><00:01:30.159><c> the</c><00:01:30.360><c> query</c><00:01:30.799><c> is</c>

00:01:31.109 --> 00:01:31.119 align:start position:0%
the user ask the query and the query is
 

00:01:31.119 --> 00:01:34.950 align:start position:0%
the user ask the query and the query is
then<00:01:31.360><c> used</c><00:01:31.720><c> to</c><00:01:32.200><c> retrieve</c><00:01:33.200><c> uh</c><00:01:33.799><c> similar</c><00:01:34.399><c> context</c>

00:01:34.950 --> 00:01:34.960 align:start position:0%
then used to retrieve uh similar context
 

00:01:34.960 --> 00:01:38.190 align:start position:0%
then used to retrieve uh similar context
from<00:01:35.119><c> the</c><00:01:35.240><c> knowledge</c><00:01:35.680><c> base</c><00:01:36.520><c> and</c><00:01:36.880><c> which</c><00:01:37.280><c> goes</c>

00:01:38.190 --> 00:01:38.200 align:start position:0%
from the knowledge base and which goes
 

00:01:38.200 --> 00:01:40.630 align:start position:0%
from the knowledge base and which goes
with<00:01:38.520><c> along</c><00:01:38.880><c> with</c><00:01:39.040><c> the</c><00:01:39.399><c> instruction</c><00:01:40.280><c> meta</c>

00:01:40.630 --> 00:01:40.640 align:start position:0%
with along with the instruction meta
 

00:01:40.640 --> 00:01:43.749 align:start position:0%
with along with the instruction meta
prompt<00:01:41.079><c> that</c><00:01:41.280><c> we</c><00:01:41.399><c> will</c><00:01:42.000><c> uh</c><00:01:42.119><c> learn</c><00:01:43.119><c> about</c><00:01:43.560><c> in</c>

00:01:43.749 --> 00:01:43.759 align:start position:0%
prompt that we will uh learn about in
 

00:01:43.759 --> 00:01:46.109 align:start position:0%
prompt that we will uh learn about in
some<00:01:44.079><c> time</c><00:01:44.479><c> how</c><00:01:44.600><c> to</c><00:01:44.799><c> write</c><00:01:45.000><c> a</c><00:01:45.159><c> meta</c><00:01:45.520><c> prompt</c>

00:01:46.109 --> 00:01:46.119 align:start position:0%
some time how to write a meta prompt
 

00:01:46.119 --> 00:01:47.990 align:start position:0%
some time how to write a meta prompt
which<00:01:46.360><c> basically</c><00:01:46.799><c> consist</c><00:01:47.320><c> of</c><00:01:47.479><c> the</c><00:01:47.640><c> query</c>

00:01:47.990 --> 00:01:48.000 align:start position:0%
which basically consist of the query
 

00:01:48.000 --> 00:01:50.590 align:start position:0%
which basically consist of the query
context<00:01:48.399><c> and</c><00:01:48.680><c> prompt</c><00:01:49.280><c> written</c><00:01:49.600><c> in</c><00:01:49.759><c> this</c><00:01:50.200><c> uh</c>

00:01:50.590 --> 00:01:50.600 align:start position:0%
context and prompt written in this uh
 

00:01:50.600 --> 00:01:53.990 align:start position:0%
context and prompt written in this uh
written<00:01:51.000><c> in</c><00:01:51.560><c> uh</c><00:01:51.880><c> you</c><00:01:52.000><c> know</c><00:01:52.320><c> uh</c><00:01:52.439><c> in</c><00:01:52.560><c> a</c><00:01:53.360><c> uniform</c>

00:01:53.990 --> 00:01:54.000 align:start position:0%
written in uh you know uh in a uniform
 

00:01:54.000 --> 00:01:57.029 align:start position:0%
written in uh you know uh in a uniform
way<00:01:54.719><c> so</c><00:01:55.360><c> this</c><00:01:55.479><c> is</c><00:01:55.600><c> the</c><00:01:55.759><c> data</c><00:01:56.039><c> flow</c><00:01:56.719><c> and</c><00:01:56.840><c> now</c>

00:01:57.029 --> 00:01:57.039 align:start position:0%
way so this is the data flow and now
 

00:01:57.039 --> 00:01:59.990 align:start position:0%
way so this is the data flow and now
let's<00:01:57.320><c> walk</c><00:01:57.759><c> through</c><00:01:58.759><c> so</c><00:01:58.920><c> for</c><00:01:59.119><c> the</c><00:01:59.320><c> demo</c><00:01:59.880><c> I</c>

00:01:59.990 --> 00:02:00.000 align:start position:0%
let's walk through so for the demo I
 

00:02:00.000 --> 00:02:01.590 align:start position:0%
let's walk through so for the demo I
would<00:02:00.159><c> like</c><00:02:00.320><c> to</c><00:02:00.520><c> recommend</c><00:02:01.039><c> you</c><00:02:01.240><c> if</c><00:02:01.360><c> you're</c>

00:02:01.590 --> 00:02:01.600 align:start position:0%
would like to recommend you if you're
 

00:02:01.600 --> 00:02:05.429 align:start position:0%
would like to recommend you if you're
using<00:02:02.119><c> Windows</c><00:02:03.079><c> you</c><00:02:03.280><c> must</c><00:02:03.960><c> uh</c><00:02:04.159><c> install</c><00:02:04.799><c> this</c>

00:02:05.429 --> 00:02:05.439 align:start position:0%
using Windows you must uh install this
 

00:02:05.439 --> 00:02:07.150 align:start position:0%
using Windows you must uh install this
uh<00:02:05.719><c> Docker</c>

00:02:07.150 --> 00:02:07.160 align:start position:0%
uh Docker
 

00:02:07.160 --> 00:02:11.030 align:start position:0%
uh Docker
app<00:02:08.160><c> let's</c><00:02:08.399><c> run</c>

00:02:11.030 --> 00:02:11.040 align:start position:0%
 
 

00:02:11.040 --> 00:02:12.570 align:start position:0%
 
it<00:02:12.040><c> our</c>

00:02:12.570 --> 00:02:12.580 align:start position:0%
it our
 

00:02:12.580 --> 00:02:15.110 align:start position:0%
it our
[Music]

00:02:15.110 --> 00:02:15.120 align:start position:0%
[Music]
 

00:02:15.120 --> 00:02:17.670 align:start position:0%
[Music]
first<00:02:16.120><c> and</c><00:02:16.319><c> also</c><00:02:16.680><c> previously</c><00:02:17.360><c> I</c><00:02:17.480><c> have</c>

00:02:17.670 --> 00:02:17.680 align:start position:0%
first and also previously I have
 

00:02:17.680 --> 00:02:20.949 align:start position:0%
first and also previously I have
installed<00:02:18.360><c> all</c><00:02:18.680><c> the</c><00:02:19.480><c> packages</c><00:02:20.160><c> here</c><00:02:20.680><c> uh</c><00:02:20.840><c> I</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
installed all the packages here uh I
 

00:02:20.959 --> 00:02:23.830 align:start position:0%
installed all the packages here uh I
have<00:02:21.120><c> done</c><00:02:21.480><c> pip</c><00:02:21.920><c> install</c><00:02:22.920><c> quadrant</c><00:02:23.400><c> client</c>

00:02:23.830 --> 00:02:23.840 align:start position:0%
have done pip install quadrant client
 

00:02:23.840 --> 00:02:27.470 align:start position:0%
have done pip install quadrant client
fast<00:02:24.160><c> embed</c><00:02:24.560><c> and</c><00:02:24.720><c> open</c><00:02:25.040><c> AI</c><00:02:25.480><c> in</c><00:02:26.000><c> an</c><00:02:26.280><c> EnV</c>

00:02:27.470 --> 00:02:27.480 align:start position:0%
fast embed and open AI in an EnV
 

00:02:27.480 --> 00:02:29.509 align:start position:0%
fast embed and open AI in an EnV
environment<00:02:28.480><c> virtual</c><00:02:28.840><c> environment</c><00:02:29.280><c> that</c><00:02:29.400><c> I</c>

00:02:29.509 --> 00:02:29.519 align:start position:0%
environment virtual environment that I
 

00:02:29.519 --> 00:02:30.830 align:start position:0%
environment virtual environment that I
created

00:02:30.830 --> 00:02:30.840 align:start position:0%
created
 

00:02:30.840 --> 00:02:33.470 align:start position:0%
created
so<00:02:31.280><c> this</c><00:02:31.400><c> is</c><00:02:31.599><c> running</c><00:02:32.160><c> we</c><00:02:32.319><c> check</c><00:02:32.640><c> the</c><00:02:32.800><c> docker</c>

00:02:33.470 --> 00:02:33.480 align:start position:0%
so this is running we check the docker
 

00:02:33.480 --> 00:02:37.270 align:start position:0%
so this is running we check the docker
app<00:02:34.239><c> and</c><00:02:34.360><c> it</c><00:02:34.480><c> shows</c><00:02:35.200><c> quadrant</c><00:02:36.200><c> uh</c><00:02:36.319><c> in</c>

00:02:37.270 --> 00:02:37.280 align:start position:0%
app and it shows quadrant uh in
 

00:02:37.280 --> 00:02:41.630 align:start position:0%
app and it shows quadrant uh in
use<00:02:38.360><c> so</c><00:02:39.360><c> so</c><00:02:39.879><c> here</c><00:02:40.040><c> you</c><00:02:40.200><c> have</c><00:02:40.400><c> seen</c><00:02:41.040><c> uh</c><00:02:41.200><c> I</c><00:02:41.319><c> have</c>

00:02:41.630 --> 00:02:41.640 align:start position:0%
use so so here you have seen uh I have
 

00:02:41.640 --> 00:02:45.470 align:start position:0%
use so so here you have seen uh I have
imported<00:02:42.120><c> quadrant</c><00:02:42.920><c> client</c><00:02:43.920><c> and</c><00:02:44.480><c> uh</c><00:02:45.319><c> have</c>

00:02:45.470 --> 00:02:45.480 align:start position:0%
imported quadrant client and uh have
 

00:02:45.480 --> 00:02:49.509 align:start position:0%
imported quadrant client and uh have
given<00:02:45.840><c> it</c><00:02:46.120><c> this</c><00:02:46.360><c> port</c><00:02:46.840><c> 633</c><00:02:47.840><c> that</c><00:02:47.959><c> we</c><00:02:48.080><c> are</c><00:02:48.519><c> using</c>

00:02:49.509 --> 00:02:49.519 align:start position:0%
given it this port 633 that we are using
 

00:02:49.519 --> 00:02:52.710 align:start position:0%
given it this port 633 that we are using
and<00:02:49.959><c> we</c><00:02:50.280><c> also</c><00:02:50.560><c> did</c><00:02:50.879><c> a</c><00:02:51.200><c> get</c><00:02:51.480><c> collection</c><00:02:52.360><c> so</c><00:02:52.560><c> if</c>

00:02:52.710 --> 00:02:52.720 align:start position:0%
and we also did a get collection so if
 

00:02:52.720 --> 00:02:54.910 align:start position:0%
and we also did a get collection so if
the<00:02:52.879><c> client</c><00:02:53.239><c> already</c><00:02:53.840><c> has</c><00:02:54.319><c> collection</c><00:02:54.840><c> we</c>

00:02:54.910 --> 00:02:54.920 align:start position:0%
the client already has collection we
 

00:02:54.920 --> 00:02:56.390 align:start position:0%
the client already has collection we
will<00:02:55.120><c> just</c><00:02:55.319><c> print</c><00:02:55.560><c> out</c><00:02:55.760><c> the</c><00:02:55.920><c> name</c><00:02:56.080><c> of</c><00:02:56.239><c> the</c>

00:02:56.390 --> 00:02:56.400 align:start position:0%
will just print out the name of the
 

00:02:56.400 --> 00:02:58.869 align:start position:0%
will just print out the name of the
collection<00:02:57.200><c> so</c><00:02:57.400><c> it</c><00:02:57.519><c> seems</c><00:02:57.920><c> that</c><00:02:58.120><c> we</c><00:02:58.280><c> do</c><00:02:58.440><c> not</c>

00:02:58.869 --> 00:02:58.879 align:start position:0%
collection so it seems that we do not
 

00:02:58.879 --> 00:03:02.509 align:start position:0%
collection so it seems that we do not
have<00:02:59.120><c> it</c><00:02:59.400><c> so</c><00:02:59.800><c> collection</c><00:03:00.360><c> list</c><00:03:00.680><c> is</c><00:03:01.319><c> empty</c><00:03:02.319><c> now</c>

00:03:02.509 --> 00:03:02.519 align:start position:0%
have it so collection list is empty now
 

00:03:02.519 --> 00:03:05.030 align:start position:0%
have it so collection list is empty now
let's<00:03:02.879><c> add</c><00:03:03.120><c> some</c><00:03:03.440><c> documents</c><00:03:04.000><c> to</c><00:03:04.360><c> add</c><00:03:04.599><c> to</c><00:03:04.760><c> our</c>

00:03:05.030 --> 00:03:05.040 align:start position:0%
let's add some documents to add to our
 

00:03:05.040 --> 00:03:08.390 align:start position:0%
let's add some documents to add to our
collection

00:03:08.390 --> 00:03:08.400 align:start position:0%
 
 

00:03:08.400 --> 00:03:14.670 align:start position:0%
 
so<00:03:09.440><c> equals</c><00:03:10.440><c> a</c><00:03:11.000><c> list</c><00:03:12.159><c> of</c><00:03:13.159><c> uh</c><00:03:13.480><c> tools</c><00:03:14.080><c> that</c><00:03:14.319><c> we</c><00:03:14.440><c> can</c>

00:03:14.670 --> 00:03:14.680 align:start position:0%
so equals a list of uh tools that we can
 

00:03:14.680 --> 00:03:17.550 align:start position:0%
so equals a list of uh tools that we can
use<00:03:15.000><c> to</c><00:03:15.239><c> make</c><00:03:15.920><c> uh</c><00:03:16.080><c> web</c><00:03:16.319><c> services</c><00:03:16.840><c> for</c><00:03:17.400><c> uh</c>

00:03:17.550 --> 00:03:17.560 align:start position:0%
use to make uh web services for uh
 

00:03:17.560 --> 00:03:26.350 align:start position:0%
use to make uh web services for uh
Vector<00:03:17.920><c> databases</c><00:03:18.640><c> so</c><00:03:19.519><c> first</c><00:03:19.879><c> thing</c><00:03:21.000><c> R</c>

00:03:26.350 --> 00:03:26.360 align:start position:0%
 
 

00:03:26.360 --> 00:03:28.710 align:start position:0%
 
okay<00:03:27.360><c> uh</c><00:03:27.440><c> so</c><00:03:27.640><c> we're</c><00:03:27.840><c> going</c><00:03:27.959><c> to</c><00:03:28.159><c> add</c><00:03:28.400><c> this</c><00:03:28.560><c> in</c>

00:03:28.710 --> 00:03:28.720 align:start position:0%
okay uh so we're going to add this in
 

00:03:28.720 --> 00:03:30.750 align:start position:0%
okay uh so we're going to add this in
our<00:03:28.959><c> collection</c>

00:03:30.750 --> 00:03:30.760 align:start position:0%
our collection
 

00:03:30.760 --> 00:03:32.270 align:start position:0%
our collection
let<00:03:31.120><c> add</c>

00:03:32.270 --> 00:03:32.280 align:start position:0%
let add
 

00:03:32.280 --> 00:03:34.229 align:start position:0%
let add
client.

00:03:34.229 --> 00:03:34.239 align:start position:0%
client.
 

00:03:34.239 --> 00:03:38.229 align:start position:0%
client.
add

00:03:38.229 --> 00:03:38.239 align:start position:0%
 
 

00:03:38.239 --> 00:03:41.630 align:start position:0%
 
uh

00:03:41.630 --> 00:03:41.640 align:start position:0%
 
 

00:03:41.640 --> 00:03:50.550 align:start position:0%
 
collection<00:03:42.640><c> I</c><00:03:42.720><c> think</c><00:03:42.879><c> it's</c><00:03:43.120><c> collection</c>

00:03:50.550 --> 00:03:50.560 align:start position:0%
 
 

00:03:50.560 --> 00:03:55.509 align:start position:0%
 
name<00:03:51.560><c> let's</c><00:03:51.920><c> put</c><00:03:52.720><c> it</c><00:03:53.720><c> knowledge</c><00:03:54.360><c> base</c><00:03:55.360><c> this</c>

00:03:55.509 --> 00:03:55.519 align:start position:0%
name let's put it knowledge base this
 

00:03:55.519 --> 00:03:57.949 align:start position:0%
name let's put it knowledge base this
many<00:03:55.879><c> collection</c><00:03:56.400><c> has</c><00:03:56.560><c> been</c><00:03:56.799><c> made</c><00:03:57.480><c> so</c><00:03:57.680><c> let's</c>

00:03:57.949 --> 00:03:57.959 align:start position:0%
many collection has been made so let's
 

00:03:57.959 --> 00:04:00.670 align:start position:0%
many collection has been made so let's
just<00:03:58.159><c> check</c><00:03:58.439><c> it</c><00:03:58.799><c> if</c><00:03:59.040><c> there</c><00:03:59.239><c> is</c><00:03:59.680><c> collection</c><00:04:00.400><c> yes</c>

00:04:00.670 --> 00:04:00.680 align:start position:0%
just check it if there is collection yes
 

00:04:00.680 --> 00:04:02.869 align:start position:0%
just check it if there is collection yes
there's<00:04:01.040><c> a</c><00:04:01.239><c> collection</c><00:04:01.680><c> name</c><00:04:02.120><c> knowledge</c><00:04:02.720><c> for</c>

00:04:02.869 --> 00:04:02.879 align:start position:0%
there's a collection name knowledge for
 

00:04:02.879 --> 00:04:05.030 align:start position:0%
there's a collection name knowledge for
normal<00:04:03.159><c> chat</c><00:04:03.560><c> completion</c><00:04:04.480><c> so</c><00:04:04.680><c> we</c><00:04:04.840><c> have</c>

00:04:05.030 --> 00:04:05.040 align:start position:0%
normal chat completion so we have
 

00:04:05.040 --> 00:04:06.830 align:start position:0%
normal chat completion so we have
imported<00:04:05.560><c> all</c><00:04:05.760><c> the</c><00:04:06.079><c> files</c><00:04:06.480><c> that</c><00:04:06.640><c> are</c>

00:04:06.830 --> 00:04:06.840 align:start position:0%
imported all the files that are
 

00:04:06.840 --> 00:04:10.350 align:start position:0%
imported all the files that are
important<00:04:07.760><c> we</c><00:04:07.879><c> have</c><00:04:08.040><c> given</c><00:04:08.439><c> a</c><00:04:09.120><c> prompt</c><00:04:10.120><c> what</c>

00:04:10.350 --> 00:04:10.360 align:start position:0%
important we have given a prompt what
 

00:04:10.360 --> 00:04:13.429 align:start position:0%
important we have given a prompt what
tools<00:04:11.079><c> should</c><00:04:11.400><c> I</c><00:04:11.560><c> need</c><00:04:11.920><c> to</c><00:04:12.120><c> use</c><00:04:12.560><c> build</c><00:04:12.879><c> a</c><00:04:13.120><c> web</c>

00:04:13.429 --> 00:04:13.439 align:start position:0%
tools should I need to use build a web
 

00:04:13.439 --> 00:04:16.430 align:start position:0%
tools should I need to use build a web
service<00:04:14.439><c> uh</c><00:04:14.640><c> using</c><00:04:14.959><c> Vector</c><00:04:15.319><c> embedding</c><00:04:15.760><c> for</c>

00:04:16.430 --> 00:04:16.440 align:start position:0%
service uh using Vector embedding for
 

00:04:16.440 --> 00:04:19.710 align:start position:0%
service uh using Vector embedding for
search<00:04:17.440><c> uh</c><00:04:17.560><c> if</c><00:04:17.639><c> you</c><00:04:17.799><c> recall</c><00:04:18.320><c> the</c><00:04:18.560><c> documents</c><00:04:19.519><c> uh</c>

00:04:19.710 --> 00:04:19.720 align:start position:0%
search uh if you recall the documents uh
 

00:04:19.720 --> 00:04:22.950 align:start position:0%
search uh if you recall the documents uh
it's<00:04:20.079><c> basically</c><00:04:20.720><c> it's</c><00:04:20.919><c> related</c><00:04:21.560><c> to</c><00:04:22.560><c> uh</c>

00:04:22.950 --> 00:04:22.960 align:start position:0%
it's basically it's related to uh
 

00:04:22.960 --> 00:04:24.990 align:start position:0%
it's basically it's related to uh
different<00:04:23.320><c> kind</c><00:04:23.479><c> of</c><00:04:23.680><c> tools</c><00:04:24.080><c> that</c><00:04:24.199><c> is</c>

00:04:24.990 --> 00:04:25.000 align:start position:0%
different kind of tools that is
 

00:04:25.000 --> 00:04:29.270 align:start position:0%
different kind of tools that is
available<00:04:26.000><c> uh</c><00:04:27.000><c> but</c><00:04:28.000><c> this</c><00:04:28.199><c> particular</c><00:04:28.840><c> example</c>

00:04:29.270 --> 00:04:29.280 align:start position:0%
available uh but this particular example
 

00:04:29.280 --> 00:04:32.029 align:start position:0%
available uh but this particular example
doesn't<00:04:29.759><c> use</c><00:04:29.960><c> R</c><00:04:30.600><c> we</c><00:04:30.680><c> are</c><00:04:30.880><c> just</c><00:04:31.039><c> doing</c><00:04:31.320><c> it</c><00:04:31.520><c> to</c>

00:04:32.029 --> 00:04:32.039 align:start position:0%
doesn't use R we are just doing it to
 

00:04:32.039 --> 00:04:35.590 align:start position:0%
doesn't use R we are just doing it to
compare<00:04:33.039><c> what</c><00:04:33.560><c> kind</c><00:04:33.720><c> of</c><00:04:33.960><c> answer</c><00:04:34.840><c> this</c><00:04:35.120><c> gives</c>

00:04:35.590 --> 00:04:35.600 align:start position:0%
compare what kind of answer this gives
 

00:04:35.600 --> 00:04:38.230 align:start position:0%
compare what kind of answer this gives
and<00:04:35.800><c> then</c><00:04:36.000><c> later</c><00:04:36.680><c> what</c><00:04:36.919><c> kind</c><00:04:37.080><c> of</c><00:04:37.320><c> answer</c><00:04:37.919><c> this</c>

00:04:38.230 --> 00:04:38.240 align:start position:0%
and then later what kind of answer this
 

00:04:38.240 --> 00:04:42.870 align:start position:0%
and then later what kind of answer this
well<00:04:38.440><c> RJ</c><00:04:39.039><c> gave</c><00:04:39.280><c> so</c><00:04:39.960><c> let's</c><00:04:40.240><c> just</c><00:04:40.440><c> run</c><00:04:40.759><c> it</c><00:04:41.880><c> and</c>

00:04:42.870 --> 00:04:42.880 align:start position:0%
well RJ gave so let's just run it and
 

00:04:42.880 --> 00:04:46.350 align:start position:0%
well RJ gave so let's just run it and
and<00:04:43.120><c> yes</c><00:04:43.800><c> here</c><00:04:44.280><c> you</c><00:04:44.400><c> will</c><00:04:44.600><c> give</c><00:04:44.800><c> your</c><00:04:45.000><c> API</c><00:04:45.639><c> key</c>

00:04:46.350 --> 00:04:46.360 align:start position:0%
and yes here you will give your API key
 

00:04:46.360 --> 00:04:49.430 align:start position:0%
and yes here you will give your API key
before<00:04:46.639><c> moving</c><00:04:47.000><c> on</c><00:04:47.400><c> I</c><00:04:47.560><c> just</c><00:04:47.759><c> want</c><00:04:48.080><c> to</c><00:04:48.720><c> be</c><00:04:49.280><c> uh</c>

00:04:49.430 --> 00:04:49.440 align:start position:0%
before moving on I just want to be uh
 

00:04:49.440 --> 00:04:52.310 align:start position:0%
before moving on I just want to be uh
quickly<00:04:49.800><c> explaining</c><00:04:50.440><c> the</c><00:04:50.800><c> output</c><00:04:51.440><c> of</c><00:04:51.720><c> the</c>

00:04:52.310 --> 00:04:52.320 align:start position:0%
quickly explaining the output of the
 

00:04:52.320 --> 00:04:55.029 align:start position:0%
quickly explaining the output of the
chat<00:04:52.680><c> competition</c><00:04:53.199><c> so</c><00:04:53.440><c> you</c><00:04:53.639><c> get</c><00:04:53.960><c> this</c><00:04:54.520><c> Jason</c>

00:04:55.029 --> 00:04:55.039 align:start position:0%
chat competition so you get this Jason
 

00:04:55.039 --> 00:04:58.950 align:start position:0%
chat competition so you get this Jason
output<00:04:55.759><c> with</c><00:04:56.080><c> ID</c><00:04:56.680><c> object</c><00:04:57.280><c> models</c><00:04:58.280><c> and</c><00:04:58.440><c> choices</c>

00:04:58.950 --> 00:04:58.960 align:start position:0%
output with ID object models and choices
 

00:04:58.960 --> 00:05:01.550 align:start position:0%
output with ID object models and choices
and<00:04:59.120><c> all</c><00:04:59.280><c> you</c><00:04:59.400><c> have</c><00:04:59.639><c> have</c><00:04:59.759><c> to</c><00:04:59.919><c> do</c><00:05:00.280><c> is</c><00:05:00.960><c> extract</c>

00:05:01.550 --> 00:05:01.560 align:start position:0%
and all you have have to do is extract
 

00:05:01.560 --> 00:05:06.830 align:start position:0%
and all you have have to do is extract
this<00:05:02.160><c> content</c><00:05:03.160><c> uh</c><00:05:03.840><c> for</c><00:05:04.840><c> for</c><00:05:05.080><c> the</c><00:05:05.199><c> output</c><00:05:05.720><c> so</c>

00:05:06.830 --> 00:05:06.840 align:start position:0%
this content uh for for the output so
 

00:05:06.840 --> 00:05:10.590 align:start position:0%
this content uh for for the output so
completion<00:05:07.840><c> uh</c><00:05:08.400><c> then</c><00:05:08.600><c> we</c><00:05:08.759><c> should</c><00:05:09.120><c> get</c><00:05:09.960><c> uh</c><00:05:10.240><c> what</c>

00:05:10.590 --> 00:05:10.600 align:start position:0%
completion uh then we should get uh what
 

00:05:10.600 --> 00:05:12.110 align:start position:0%
completion uh then we should get uh what
we<00:05:10.720><c> should</c><00:05:10.960><c> get</c>

00:05:12.110 --> 00:05:12.120 align:start position:0%
we should get
 

00:05:12.120 --> 00:05:14.510 align:start position:0%
we should get
choices

00:05:14.510 --> 00:05:14.520 align:start position:0%
choices
 

00:05:14.520 --> 00:05:17.390 align:start position:0%
choices
uh

00:05:17.390 --> 00:05:17.400 align:start position:0%
uh
 

00:05:17.400 --> 00:05:21.909 align:start position:0%
uh
choices<00:05:18.400><c> and</c><00:05:19.280><c> it's</c><00:05:19.479><c> a</c><00:05:19.759><c> list</c><00:05:20.759><c> so</c><00:05:21.080><c> we</c><00:05:21.360><c> just</c><00:05:21.720><c> have</c>

00:05:21.909 --> 00:05:21.919 align:start position:0%
choices and it's a list so we just have
 

00:05:21.919 --> 00:05:23.990 align:start position:0%
choices and it's a list so we just have
to<00:05:22.199><c> get</c><00:05:22.560><c> the</c><00:05:22.800><c> first</c>

00:05:23.990 --> 00:05:24.000 align:start position:0%
to get the first
 

00:05:24.000 --> 00:05:27.909 align:start position:0%
to get the first
element<00:05:25.000><c> and</c><00:05:25.280><c> now</c><00:05:25.520><c> we</c><00:05:25.720><c> can</c><00:05:26.520><c> just</c><00:05:26.840><c> go</c><00:05:27.039><c> on</c><00:05:27.440><c> and</c>

00:05:27.909 --> 00:05:27.919 align:start position:0%
element and now we can just go on and
 

00:05:27.919 --> 00:05:33.189 align:start position:0%
element and now we can just go on and
ask<00:05:28.199><c> for</c><00:05:28.440><c> the</c><00:05:28.680><c> messages</c>

00:05:33.189 --> 00:05:33.199 align:start position:0%
 
 

00:05:33.199 --> 00:05:37.309 align:start position:0%
 
message<00:05:34.400><c> and</c><00:05:35.400><c> we</c><00:05:35.960><c> we</c><00:05:36.319><c> understood</c><00:05:36.960><c> that</c><00:05:37.160><c> the</c>

00:05:37.309 --> 00:05:37.319 align:start position:0%
message and we we understood that the
 

00:05:37.319 --> 00:05:39.430 align:start position:0%
message and we we understood that the
completion<00:05:37.919><c> portion</c><00:05:38.360><c> was</c><00:05:38.600><c> from</c><00:05:39.000><c> the</c>

00:05:39.430 --> 00:05:39.440 align:start position:0%
completion portion was from the
 

00:05:39.440 --> 00:05:43.309 align:start position:0%
completion portion was from the
assistant<00:05:40.240><c> role</c><00:05:41.240><c> and</c><00:05:41.560><c> we</c><00:05:41.720><c> would</c><00:05:42.000><c> want</c><00:05:42.319><c> to</c>

00:05:43.309 --> 00:05:43.319 align:start position:0%
assistant role and we would want to
 

00:05:43.319 --> 00:05:46.870 align:start position:0%
assistant role and we would want to
extract<00:05:43.880><c> the</c><00:05:44.120><c> content</c><00:05:44.600><c> of</c><00:05:44.800><c> the</c><00:05:45.120><c> assistant</c>

00:05:46.870 --> 00:05:46.880 align:start position:0%
extract the content of the assistant
 

00:05:46.880 --> 00:05:49.990 align:start position:0%
extract the content of the assistant
so<00:05:47.880><c> as</c><00:05:48.000><c> you</c><00:05:48.120><c> can</c><00:05:48.240><c> see</c><00:05:48.479><c> the</c><00:05:48.639><c> results</c><00:05:49.039><c> is</c><00:05:49.400><c> quite</c>

00:05:49.990 --> 00:05:50.000 align:start position:0%
so as you can see the results is quite
 

00:05:50.000 --> 00:05:52.150 align:start position:0%
so as you can see the results is quite
generic<00:05:50.600><c> it</c><00:05:50.759><c> has</c><00:05:51.319><c> uh</c><00:05:51.440><c> given</c><00:05:51.800><c> different</c>

00:05:52.150 --> 00:05:52.160 align:start position:0%
generic it has uh given different
 

00:05:52.160 --> 00:05:54.230 align:start position:0%
generic it has uh given different
programming<00:05:52.639><c> language</c><00:05:53.120><c> web</c><00:05:53.360><c> frame</c><00:05:53.600><c> work</c><00:05:53.960><c> that</c>

00:05:54.230 --> 00:05:54.240 align:start position:0%
programming language web frame work that
 

00:05:54.240 --> 00:05:55.870 align:start position:0%
programming language web frame work that
you<00:05:54.360><c> can</c><00:05:54.520><c> use</c><00:05:54.759><c> and</c><00:05:54.919><c> it</c><00:05:55.080><c> has</c><00:05:55.280><c> nothing</c><00:05:55.520><c> to</c><00:05:55.680><c> do</c>

00:05:55.870 --> 00:05:55.880 align:start position:0%
you can use and it has nothing to do
 

00:05:55.880 --> 00:05:58.029 align:start position:0%
you can use and it has nothing to do
with<00:05:56.000><c> the</c><00:05:56.199><c> document</c><00:05:56.680><c> that</c><00:05:56.800><c> we</c><00:05:57.000><c> provided</c>

00:05:58.029 --> 00:05:58.039 align:start position:0%
with the document that we provided
 

00:05:58.039 --> 00:06:00.189 align:start position:0%
with the document that we provided
initially<00:05:59.039><c> let's</c>

00:06:00.189 --> 00:06:00.199 align:start position:0%
initially let's
 

00:06:00.199 --> 00:06:02.830 align:start position:0%
initially let's
test<00:06:01.080><c> uh</c><00:06:01.199><c> chat</c><00:06:01.560><c> completion</c><00:06:02.080><c> with</c>

00:06:02.830 --> 00:06:02.840 align:start position:0%
test uh chat completion with
 

00:06:02.840 --> 00:06:08.110 align:start position:0%
test uh chat completion with
RG<00:06:03.840><c> and</c><00:06:04.479><c> uh</c><00:06:04.600><c> see</c><00:06:04.840><c> the</c>

00:06:08.110 --> 00:06:08.120 align:start position:0%
 
 

00:06:08.120 --> 00:06:11.430 align:start position:0%
 
difference<00:06:09.120><c> so</c><00:06:09.280><c> to</c><00:06:09.479><c> perform</c><00:06:09.880><c> the</c><00:06:10.039><c> r</c><00:06:10.720><c> we</c><00:06:10.880><c> need</c>

00:06:11.430 --> 00:06:11.440 align:start position:0%
difference so to perform the r we need
 

00:06:11.440 --> 00:06:15.270 align:start position:0%
difference so to perform the r we need
three<00:06:11.759><c> metadatas</c><00:06:12.720><c> right</c><00:06:13.240><c> The</c><00:06:13.440><c> Prompt</c><00:06:14.319><c> the</c><00:06:14.880><c> uh</c>

00:06:15.270 --> 00:06:15.280 align:start position:0%
three metadatas right The Prompt the uh
 

00:06:15.280 --> 00:06:19.710 align:start position:0%
three metadatas right The Prompt the uh
the<00:06:15.919><c> context</c><00:06:16.639><c> and</c><00:06:16.840><c> the</c><00:06:17.520><c> question</c><00:06:18.520><c> uh</c><00:06:18.880><c> so</c><00:06:19.360><c> to</c>

00:06:19.710 --> 00:06:19.720 align:start position:0%
the context and the question uh so to
 

00:06:19.720 --> 00:06:21.629 align:start position:0%
the context and the question uh so to
extract<00:06:20.199><c> the</c><00:06:20.400><c> context</c><00:06:20.919><c> from</c><00:06:21.160><c> the</c><00:06:21.560><c> uh</c>

00:06:21.629 --> 00:06:21.639 align:start position:0%
extract the context from the uh
 

00:06:21.639 --> 00:06:25.790 align:start position:0%
extract the context from the uh
knowledge<00:06:22.039><c> base</c><00:06:22.800><c> we</c><00:06:23.000><c> need</c><00:06:23.840><c> to</c><00:06:24.800><c> get</c><00:06:25.080><c> at</c><00:06:25.240><c> least</c>

00:06:25.790 --> 00:06:25.800 align:start position:0%
knowledge base we need to get at least
 

00:06:25.800 --> 00:06:27.629 align:start position:0%
knowledge base we need to get at least
three<00:06:26.360><c> relevant</c><00:06:26.880><c> information</c><00:06:27.360><c> from</c><00:06:27.520><c> the</c>

00:06:27.629 --> 00:06:27.639 align:start position:0%
three relevant information from the
 

00:06:27.639 --> 00:06:29.469 align:start position:0%
three relevant information from the
knowledge<00:06:28.039><c> Bas</c><00:06:28.199><c> so</c><00:06:28.360><c> let's</c><00:06:28.599><c> check</c><00:06:28.840><c> out</c><00:06:29.039><c> how</c><00:06:29.160><c> to</c>

00:06:29.469 --> 00:06:29.479 align:start position:0%
knowledge Bas so let's check out how to
 

00:06:29.479 --> 00:06:33.189 align:start position:0%
knowledge Bas so let's check out how to
do<00:06:29.720><c> that</c><00:06:30.639><c> so</c><00:06:31.000><c> result</c><00:06:31.560><c> is</c><00:06:31.720><c> equal</c><00:06:32.080><c> to</c>

00:06:33.189 --> 00:06:33.199 align:start position:0%
do that so result is equal to
 

00:06:33.199 --> 00:06:36.309 align:start position:0%
do that so result is equal to
client<00:06:34.199><c> do</c>

00:06:36.309 --> 00:06:36.319 align:start position:0%
client do
 

00:06:36.319 --> 00:06:40.350 align:start position:0%
client do
query<00:06:37.440><c> um</c><00:06:38.440><c> we</c><00:06:38.599><c> need</c><00:06:38.960><c> three</c><00:06:39.280><c> things</c><00:06:39.960><c> uh</c><00:06:40.160><c> the</c>

00:06:40.350 --> 00:06:40.360 align:start position:0%
query um we need three things uh the
 

00:06:40.360 --> 00:06:42.150 align:start position:0%
query um we need three things uh the
collection

00:06:42.150 --> 00:06:42.160 align:start position:0%
collection
 

00:06:42.160 --> 00:06:48.189 align:start position:0%
collection
name<00:06:43.160><c> uh</c><00:06:43.319><c> we</c><00:06:43.440><c> all</c><00:06:43.639><c> know</c><00:06:43.919><c> that</c><00:06:44.120><c> is</c><00:06:44.360><c> knowledge</c>

00:06:48.189 --> 00:06:48.199 align:start position:0%
 
 

00:06:48.199 --> 00:06:57.629 align:start position:0%
 
base<00:06:49.199><c> uh</c><00:06:49.919><c> then</c><00:06:50.160><c> we</c><00:06:50.360><c> need</c><00:06:51.080><c> uh</c><00:06:51.720><c> the</c><00:06:52.000><c> query</c>

00:06:57.629 --> 00:06:57.639 align:start position:0%
 
 

00:06:57.639 --> 00:07:02.070 align:start position:0%
 
text<00:06:58.639><c> mhm</c><00:06:59.160><c> okay</c><00:06:59.560><c> so</c><00:07:00.360><c> uh</c><00:07:00.599><c> the</c><00:07:01.280><c> uh</c><00:07:01.440><c> name</c><00:07:01.720><c> of</c><00:07:01.879><c> the</c>

00:07:02.070 --> 00:07:02.080 align:start position:0%
text mhm okay so uh the uh name of the
 

00:07:02.080 --> 00:07:06.790 align:start position:0%
text mhm okay so uh the uh name of the
knowledge<00:07:02.479><c> base</c><00:07:02.800><c> was</c><00:07:03.080><c> actually</c>

00:07:06.790 --> 00:07:06.800 align:start position:0%
 
 

00:07:06.800 --> 00:07:09.990 align:start position:0%
 
underscore<00:07:07.800><c> so</c><00:07:08.120><c> what</c><00:07:08.280><c> we</c><00:07:08.479><c> did</c><00:07:08.800><c> is</c><00:07:09.120><c> we</c><00:07:09.360><c> just</c><00:07:09.879><c> uh</c>

00:07:09.990 --> 00:07:10.000 align:start position:0%
underscore so what we did is we just uh
 

00:07:10.000 --> 00:07:12.869 align:start position:0%
underscore so what we did is we just uh
added<00:07:10.360><c> all</c><00:07:10.520><c> the</c><00:07:11.039><c> context</c><00:07:11.960><c> together</c><00:07:12.240><c> we</c>

00:07:12.869 --> 00:07:12.879 align:start position:0%
added all the context together we
 

00:07:12.879 --> 00:07:16.670 align:start position:0%
added all the context together we
concatenated<00:07:13.879><c> uh</c><00:07:14.120><c> it</c><00:07:14.560><c> and</c><00:07:14.800><c> then</c><00:07:15.280><c> we</c><00:07:16.199><c> made</c><00:07:16.479><c> our</c>

00:07:16.670 --> 00:07:16.680 align:start position:0%
concatenated uh it and then we made our
 

00:07:16.680 --> 00:07:19.029 align:start position:0%
concatenated uh it and then we made our
meta<00:07:17.160><c> prompt</c><00:07:17.960><c> if</c><00:07:18.080><c> you</c><00:07:18.199><c> cannot</c><00:07:18.599><c> find</c><00:07:18.879><c> the</c>

00:07:19.029 --> 00:07:19.039 align:start position:0%
meta prompt if you cannot find the
 

00:07:19.039 --> 00:07:21.909 align:start position:0%
meta prompt if you cannot find the
answer<00:07:19.360><c> do</c><00:07:19.520><c> not</c><00:07:19.720><c> pretend</c><00:07:20.680><c> you</c><00:07:20.840><c> know</c><00:07:21.120><c> it</c><00:07:21.639><c> but</c>

00:07:21.909 --> 00:07:21.919 align:start position:0%
answer do not pretend you know it but
 

00:07:21.919 --> 00:07:24.510 align:start position:0%
answer do not pretend you know it but
answer<00:07:22.280><c> I</c><00:07:22.440><c> did</c><00:07:22.680><c> not</c><00:07:22.919><c> know</c><00:07:23.240><c> this</c><00:07:23.400><c> is</c><00:07:23.599><c> to</c><00:07:23.879><c> prevent</c>

00:07:24.510 --> 00:07:24.520 align:start position:0%
answer I did not know this is to prevent
 

00:07:24.520 --> 00:07:26.950 align:start position:0%
answer I did not know this is to prevent
the<00:07:24.759><c> hallucination</c><00:07:25.479><c> part</c><00:07:26.240><c> so</c><00:07:26.560><c> the</c><00:07:26.720><c> question</c>

00:07:26.950 --> 00:07:26.960 align:start position:0%
the hallucination part so the question
 

00:07:26.960 --> 00:07:29.869 align:start position:0%
the hallucination part so the question
is<00:07:27.120><c> still</c><00:07:27.360><c> the</c><00:07:27.560><c> prompt</c><00:07:28.560><c> what</c><00:07:28.720><c> tools</c><00:07:29.039><c> I</c><00:07:29.440><c> need</c><00:07:29.720><c> to</c>

00:07:29.869 --> 00:07:29.879 align:start position:0%
is still the prompt what tools I need to
 

00:07:29.879 --> 00:07:32.710 align:start position:0%
is still the prompt what tools I need to
use<00:07:30.160><c> to</c><00:07:30.360><c> build</c><00:07:30.560><c> a</c><00:07:30.759><c> web</c><00:07:31.080><c> service</c><00:07:31.800><c> uh</c><00:07:31.879><c> using</c><00:07:32.360><c> ve</c>

00:07:32.710 --> 00:07:32.720 align:start position:0%
use to build a web service uh using ve
 

00:07:32.720 --> 00:07:35.070 align:start position:0%
use to build a web service uh using ve
Vector<00:07:33.039><c> embedding</c><00:07:33.479><c> for</c><00:07:33.680><c> search</c><00:07:34.400><c> because</c><00:07:34.960><c> uh</c>

00:07:35.070 --> 00:07:35.080 align:start position:0%
Vector embedding for search because uh
 

00:07:35.080 --> 00:07:37.749 align:start position:0%
Vector embedding for search because uh
you<00:07:35.199><c> see</c><00:07:35.720><c> we</c><00:07:35.960><c> already</c><00:07:36.319><c> talked</c><00:07:36.639><c> about</c><00:07:36.960><c> that</c><00:07:37.599><c> we</c>

00:07:37.749 --> 00:07:37.759 align:start position:0%
you see we already talked about that we
 

00:07:37.759 --> 00:07:40.990 align:start position:0%
you see we already talked about that we
should<00:07:38.039><c> need</c><00:07:38.720><c> uh</c><00:07:38.960><c> at</c><00:07:39.160><c> least</c><00:07:39.759><c> uh</c><00:07:39.960><c> three</c><00:07:40.400><c> parts</c>

00:07:40.990 --> 00:07:41.000 align:start position:0%
should need uh at least uh three parts
 

00:07:41.000 --> 00:07:43.629 align:start position:0%
should need uh at least uh three parts
the<00:07:41.599><c> the</c><00:07:41.800><c> context</c><00:07:42.360><c> the</c><00:07:42.599><c> question</c><00:07:43.280><c> and</c><00:07:43.479><c> the</c>

00:07:43.629 --> 00:07:43.639 align:start position:0%
the the context the question and the
 

00:07:43.639 --> 00:07:46.830 align:start position:0%
the the context the question and the
prompt<00:07:44.639><c> now</c><00:07:44.879><c> the</c><00:07:45.080><c> context</c><00:07:46.000><c> uh</c><00:07:46.159><c> we</c><00:07:46.319><c> have</c><00:07:46.560><c> also</c>

00:07:46.830 --> 00:07:46.840 align:start position:0%
prompt now the context uh we have also
 

00:07:46.840 --> 00:07:52.670 align:start position:0%
prompt now the context uh we have also
given<00:07:47.720><c> uh</c><00:07:47.840><c> from</c><00:07:48.440><c> here</c><00:07:49.919><c> now</c><00:07:50.919><c> we</c><00:07:51.199><c> will</c><00:07:51.560><c> just</c><00:07:51.960><c> do</c>

00:07:52.670 --> 00:07:52.680 align:start position:0%
given uh from here now we will just do
 

00:07:52.680 --> 00:07:58.309 align:start position:0%
given uh from here now we will just do
the<00:07:53.000><c> chat</c><00:07:53.720><c> completion</c><00:07:54.720><c> for</c><00:07:55.440><c> this</c>

00:07:58.309 --> 00:07:58.319 align:start position:0%
 
 

00:07:58.319 --> 00:08:00.070 align:start position:0%
 
meta

00:08:00.070 --> 00:08:00.080 align:start position:0%
meta
 

00:08:00.080 --> 00:08:04.990 align:start position:0%
meta
okay<00:08:00.639><c> so</c><00:08:01.639><c> voila</c><00:08:02.639><c> it</c><00:08:02.919><c> gives</c><00:08:03.599><c> exact</c><00:08:04.440><c> context</c>

00:08:04.990 --> 00:08:05.000 align:start position:0%
okay so voila it gives exact context
 

00:08:05.000 --> 00:08:08.270 align:start position:0%
okay so voila it gives exact context
from<00:08:05.240><c> our</c><00:08:05.440><c> knowledge</c><00:08:05.919><c> base</c><00:08:06.840><c> and</c><00:08:07.840><c> if</c><00:08:08.000><c> we</c><00:08:08.120><c> want</c>

00:08:08.270 --> 00:08:08.280 align:start position:0%
from our knowledge base and if we want
 

00:08:08.280 --> 00:08:10.230 align:start position:0%
from our knowledge base and if we want
to<00:08:08.440><c> go</c><00:08:08.639><c> through</c><00:08:08.840><c> the</c><00:08:09.000><c> pipeline</c><00:08:09.560><c> again</c><00:08:10.000><c> we've</c>

00:08:10.230 --> 00:08:10.240 align:start position:0%
to go through the pipeline again we've
 

00:08:10.240 --> 00:08:13.710 align:start position:0%
to go through the pipeline again we've
got<00:08:10.440><c> the</c><00:08:11.000><c> context</c><00:08:12.000><c> and</c><00:08:12.360><c> join</c><00:08:12.840><c> that</c><00:08:13.159><c> those</c><00:08:13.440><c> or</c>

00:08:13.710 --> 00:08:13.720 align:start position:0%
got the context and join that those or
 

00:08:13.720 --> 00:08:17.270 align:start position:0%
got the context and join that those or
conc<00:08:14.440><c> concatenated</c><00:08:15.199><c> those</c><00:08:15.879><c> uh</c><00:08:16.280><c> context</c><00:08:17.120><c> and</c>

00:08:17.270 --> 00:08:17.280 align:start position:0%
conc concatenated those uh context and
 

00:08:17.280 --> 00:08:19.029 align:start position:0%
conc concatenated those uh context and
in<00:08:17.400><c> The</c><00:08:17.520><c> Meta</c><00:08:17.879><c> prompt</c><00:08:18.159><c> we</c><00:08:18.280><c> have</c><00:08:18.440><c> given</c><00:08:18.720><c> the</c>

00:08:19.029 --> 00:08:19.039 align:start position:0%
in The Meta prompt we have given the
 

00:08:19.039 --> 00:08:21.510 align:start position:0%
in The Meta prompt we have given the
prompt<00:08:19.520><c> the</c><00:08:19.680><c> question</c><00:08:20.080><c> the</c><00:08:20.280><c> cont</c><00:08:21.080><c> the</c><00:08:21.159><c> chart</c>

00:08:21.510 --> 00:08:21.520 align:start position:0%
prompt the question the cont the chart
 

00:08:21.520 --> 00:08:25.230 align:start position:0%
prompt the question the cont the chart
completion<00:08:22.240><c> with</c><00:08:22.400><c> this</c><00:08:22.560><c> meta</c><00:08:23.000><c> prompt</c><00:08:23.520><c> and</c><00:08:24.240><c> th</c>

00:08:25.230 --> 00:08:25.240 align:start position:0%
completion with this meta prompt and th
 

00:08:25.240 --> 00:08:28.909 align:start position:0%
completion with this meta prompt and th
uh<00:08:25.639><c> the</c><00:08:25.879><c> r</c><00:08:26.400><c> pipeline</c><00:08:26.879><c> works</c><00:08:27.840><c> so</c><00:08:28.560><c> that's</c><00:08:28.759><c> all</c>

00:08:28.909 --> 00:08:28.919 align:start position:0%
uh the r pipeline works so that's all
 

00:08:28.919 --> 00:08:31.670 align:start position:0%
uh the r pipeline works so that's all
for<00:08:29.280><c> today</c><00:08:30.080><c> uh</c><00:08:30.479><c> thank</c><00:08:30.639><c> you</c><00:08:30.759><c> for</c><00:08:30.960><c> joining</c><00:08:31.360><c> me</c>

00:08:31.670 --> 00:08:31.680 align:start position:0%
for today uh thank you for joining me
 

00:08:31.680 --> 00:08:35.149 align:start position:0%
for today uh thank you for joining me
for<00:08:32.640><c> uh</c><00:08:32.800><c> this</c><00:08:33.039><c> important</c><00:08:33.599><c> topic</c><00:08:34.479><c> in</c><00:08:34.680><c> future</c><00:08:35.039><c> we</c>

00:08:35.149 --> 00:08:35.159 align:start position:0%
for uh this important topic in future we
 

00:08:35.159 --> 00:08:37.670 align:start position:0%
for uh this important topic in future we
are<00:08:35.399><c> also</c><00:08:35.719><c> covering</c><00:08:36.200><c> a</c><00:08:36.440><c> lot</c><00:08:36.680><c> of</c><00:08:36.880><c> important</c>

00:08:37.670 --> 00:08:37.680 align:start position:0%
are also covering a lot of important
 

00:08:37.680 --> 00:08:40.269 align:start position:0%
are also covering a lot of important
topics<00:08:38.360><c> not</c><00:08:38.519><c> only</c><00:08:39.039><c> related</c><00:08:39.479><c> with</c><00:08:39.719><c> quadrant</c>

00:08:40.269 --> 00:08:40.279 align:start position:0%
topics not only related with quadrant
 

00:08:40.279 --> 00:08:47.310 align:start position:0%
topics not only related with quadrant
but<00:08:40.479><c> also</c><00:08:40.719><c> with</c><00:08:41.000><c> the</c><00:08:41.320><c> AI</c><00:08:41.719><c> and</c><00:08:41.880><c> llm</c><00:08:42.560><c> so</c><00:08:43.320><c> stay</c>

00:08:47.310 --> 00:08:47.320 align:start position:0%
 
 

00:08:47.320 --> 00:08:50.320 align:start position:0%
 
tuned

