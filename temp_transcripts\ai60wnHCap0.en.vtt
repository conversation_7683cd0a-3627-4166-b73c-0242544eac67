WEBVTT
Kind: captions
Language: en

00:00:00.060 --> 00:00:02.629 align:start position:0%
 
all<00:00:00.570><c> right</c><00:00:01.079><c> or</c><00:00:01.439><c> back</c><00:00:01.949><c> with</c><00:00:02.190><c> some</c><00:00:02.340><c> more</c><00:00:02.429><c> big</c>

00:00:02.629 --> 00:00:02.639 align:start position:0%
all right or back with some more big
 

00:00:02.639 --> 00:00:05.510 align:start position:0%
all right or back with some more big
o-notation<00:00:02.790><c> interview</c><00:00:03.570><c> questions</c><00:00:04.400><c> so</c><00:00:05.400><c> if</c>

00:00:05.510 --> 00:00:05.520 align:start position:0%
o-notation interview questions so if
 

00:00:05.520 --> 00:00:07.340 align:start position:0%
o-notation interview questions so if
let's<00:00:05.670><c> get</c><00:00:05.790><c> rid</c><00:00:05.910><c> into</c><00:00:06.060><c> it</c><00:00:06.180><c> okay</c><00:00:06.930><c> so</c><00:00:07.140><c> the</c><00:00:07.230><c> first</c>

00:00:07.340 --> 00:00:07.350 align:start position:0%
let's get rid into it okay so the first
 

00:00:07.350 --> 00:00:09.440 align:start position:0%
let's get rid into it okay so the first
example<00:00:07.500><c> here</c><00:00:07.890><c> is</c><00:00:08.370><c> we</c><00:00:08.639><c> have</c><00:00:08.670><c> a</c><00:00:08.820><c> balanced</c>

00:00:09.440 --> 00:00:09.450 align:start position:0%
example here is we have a balanced
 

00:00:09.450 --> 00:00:13.520 align:start position:0%
example here is we have a balanced
binary<00:00:09.690><c> search</c><00:00:09.900><c> tree</c><00:00:10.349><c> some</c><00:00:10.880><c> algorithm</c><00:00:12.530><c> let's</c>

00:00:13.520 --> 00:00:13.530 align:start position:0%
binary search tree some algorithm let's
 

00:00:13.530 --> 00:00:14.720 align:start position:0%
binary search tree some algorithm let's
see<00:00:13.590><c> what's</c><00:00:13.740><c> going</c><00:00:13.860><c> on</c><00:00:13.980><c> here</c><00:00:14.160><c> first</c><00:00:14.400><c> all</c><00:00:14.670><c> right</c>

00:00:14.720 --> 00:00:14.730 align:start position:0%
see what's going on here first all right
 

00:00:14.730 --> 00:00:16.910 align:start position:0%
see what's going on here first all right
I<00:00:15.000><c> suppose</c><00:00:15.269><c> you</c><00:00:15.690><c> want</c><00:00:15.960><c> to</c><00:00:16.020><c> forewarn</c><00:00:16.410><c> you</c><00:00:16.560><c> that</c>

00:00:16.910 --> 00:00:16.920 align:start position:0%
I suppose you want to forewarn you that
 

00:00:16.920 --> 00:00:20.240 align:start position:0%
I suppose you want to forewarn you that
these<00:00:17.820><c> two</c><00:00:18.090><c> examples</c><00:00:18.600><c> are</c><00:00:18.840><c> a</c><00:00:19.289><c> little</c><00:00:19.740><c> tricky</c>

00:00:20.240 --> 00:00:20.250 align:start position:0%
these two examples are a little tricky
 

00:00:20.250 --> 00:00:21.890 align:start position:0%
these two examples are a little tricky
alright<00:00:20.760><c> these</c><00:00:20.910><c> aren't</c><00:00:21.090><c> straightforward</c><00:00:21.689><c> a</c>

00:00:21.890 --> 00:00:21.900 align:start position:0%
alright these aren't straightforward a
 

00:00:21.900 --> 00:00:26.540 align:start position:0%
alright these aren't straightforward a
little<00:00:22.260><c> tricky</c><00:00:22.560><c> okay</c><00:00:23.390><c> so</c><00:00:24.560><c> we</c><00:00:25.560><c> have</c><00:00:25.740><c> a</c><00:00:26.070><c> function</c>

00:00:26.540 --> 00:00:26.550 align:start position:0%
little tricky okay so we have a function
 

00:00:26.550 --> 00:00:27.950 align:start position:0%
little tricky okay so we have a function
called<00:00:26.580><c> some</c><00:00:26.970><c> we're</c><00:00:27.210><c> taking</c><00:00:27.480><c> in</c><00:00:27.599><c> a</c><00:00:27.660><c> single</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
called some we're taking in a single
 

00:00:27.960 --> 00:00:29.839 align:start position:0%
called some we're taking in a single
node<00:00:28.140><c> that's</c><00:00:28.769><c> also</c><00:00:29.310><c> how</c><00:00:29.429><c> you</c><00:00:29.490><c> know</c><00:00:29.699><c> we're</c>

00:00:29.839 --> 00:00:29.849 align:start position:0%
node that's also how you know we're
 

00:00:29.849 --> 00:00:31.460 align:start position:0%
node that's also how you know we're
talking<00:00:30.060><c> about</c><00:00:30.119><c> the</c><00:00:30.240><c> trees</c><00:00:30.510><c> generally</c><00:00:31.260><c> I</c>

00:00:31.460 --> 00:00:31.470 align:start position:0%
talking about the trees generally I
 

00:00:31.470 --> 00:00:34.000 align:start position:0%
talking about the trees generally I
guess<00:00:32.430><c> gonna</c><00:00:32.610><c> be</c><00:00:32.700><c> Legolas</c><00:00:33.180><c> or</c><00:00:33.360><c> something</c><00:00:33.600><c> but</c>

00:00:34.000 --> 00:00:34.010 align:start position:0%
guess gonna be Legolas or something but
 

00:00:34.010 --> 00:00:36.709 align:start position:0%
guess gonna be Legolas or something but
we're<00:00:35.010><c> bringing</c><00:00:35.579><c> in</c><00:00:35.670><c> a</c><00:00:35.730><c> node</c><00:00:35.940><c> and</c><00:00:36.239><c> we're</c>

00:00:36.709 --> 00:00:36.719 align:start position:0%
we're bringing in a node and we're
 

00:00:36.719 --> 00:00:38.510 align:start position:0%
we're bringing in a node and we're
saying<00:00:36.899><c> the</c><00:00:37.230><c> base</c><00:00:37.410><c> case</c><00:00:37.680><c> here</c><00:00:37.950><c> because</c><00:00:38.219><c> it's</c><00:00:38.489><c> a</c>

00:00:38.510 --> 00:00:38.520 align:start position:0%
saying the base case here because it's a
 

00:00:38.520 --> 00:00:41.750 align:start position:0%
saying the base case here because it's a
recursion<00:00:38.879><c> if</c><00:00:39.870><c> node</c><00:00:40.230><c> equals</c><00:00:40.590><c> there's</c><00:00:41.550><c> equal</c>

00:00:41.750 --> 00:00:41.760 align:start position:0%
recursion if node equals there's equal
 

00:00:41.760 --> 00:00:44.450 align:start position:0%
recursion if node equals there's equal
in<00:00:41.850><c> all</c><00:00:42.000><c> return</c><00:00:42.329><c> zero</c><00:00:42.629><c> that</c><00:00:42.930><c> means</c><00:00:43.250><c> we're</c><00:00:44.250><c> done</c>

00:00:44.450 --> 00:00:44.460 align:start position:0%
in all return zero that means we're done
 

00:00:44.460 --> 00:00:46.490 align:start position:0%
in all return zero that means we're done
basically<00:00:45.149><c> finish</c><00:00:45.660><c> with</c><00:00:45.750><c> the</c><00:00:45.809><c> recursion</c><00:00:46.079><c> as</c>

00:00:46.490 --> 00:00:46.500 align:start position:0%
basically finish with the recursion as
 

00:00:46.500 --> 00:00:49.580 align:start position:0%
basically finish with the recursion as
the<00:00:46.620><c> base</c><00:00:46.800><c> case</c><00:00:47.090><c> if</c><00:00:48.090><c> not</c><00:00:48.360><c> and</c><00:00:48.750><c> we</c><00:00:48.840><c> return</c><00:00:49.230><c> some</c>

00:00:49.580 --> 00:00:49.590 align:start position:0%
the base case if not and we return some
 

00:00:49.590 --> 00:00:52.939 align:start position:0%
the base case if not and we return some
of<00:00:49.620><c> node</c><00:00:50.010><c> left</c><00:00:50.550><c> so</c><00:00:51.270><c> the</c><00:00:51.510><c> the</c><00:00:51.809><c> child</c><00:00:52.140><c> the</c><00:00:52.739><c> left</c>

00:00:52.939 --> 00:00:52.949 align:start position:0%
of node left so the the child the left
 

00:00:52.949 --> 00:00:55.400 align:start position:0%
of node left so the the child the left
child<00:00:53.250><c> node</c><00:00:53.610><c> plus</c><00:00:54.270><c> the</c><00:00:54.600><c> current</c><00:00:54.780><c> node</c><00:00:54.989><c> value</c>

00:00:55.400 --> 00:00:55.410 align:start position:0%
child node plus the current node value
 

00:00:55.410 --> 00:00:58.569 align:start position:0%
child node plus the current node value
plus<00:00:55.890><c> the</c><00:00:56.250><c> current</c><00:00:56.399><c> nodes</c><00:00:56.699><c> right</c><00:00:57.030><c> child</c><00:00:57.300><c> and</c>

00:00:58.569 --> 00:00:58.579 align:start position:0%
plus the current nodes right child and
 

00:00:58.579 --> 00:01:02.360 align:start position:0%
plus the current nodes right child and
we're<00:00:59.579><c> weird</c><00:01:00.300><c> easing</c><00:01:00.539><c> recursion</c><00:01:01.079><c> on</c><00:01:01.260><c> the</c><00:01:02.070><c> left</c>

00:01:02.360 --> 00:01:02.370 align:start position:0%
we're weird easing recursion on the left
 

00:01:02.370 --> 00:01:06.469 align:start position:0%
we're weird easing recursion on the left
and<00:01:02.640><c> right</c><00:01:02.699><c> child</c><00:01:03.510><c> in</c><00:01:03.750><c> the</c><00:01:03.809><c> turn</c><00:01:04.019><c> then</c><00:01:05.060><c> so</c><00:01:06.060><c> if</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
and right child in the turn then so if
 

00:01:06.479 --> 00:01:07.850 align:start position:0%
and right child in the turn then so if
we<00:01:06.630><c> would</c><00:01:06.750><c> get</c><00:01:06.869><c> to</c><00:01:07.020><c> it</c><00:01:07.110><c> know</c><00:01:07.229><c> that</c><00:01:07.260><c> equals</c><00:01:07.619><c> null</c>

00:01:07.850 --> 00:01:07.860 align:start position:0%
we would get to it know that equals null
 

00:01:07.860 --> 00:01:10.340 align:start position:0%
we would get to it know that equals null
that<00:01:08.189><c> means</c><00:01:08.580><c> I</c><00:01:08.909><c> doesn't</c><00:01:09.570><c> it's</c><00:01:09.900><c> a</c><00:01:09.990><c> left</c><00:01:10.170><c> or</c><00:01:10.229><c> a</c>

00:01:10.340 --> 00:01:10.350 align:start position:0%
that means I doesn't it's a left or a
 

00:01:10.350 --> 00:01:12.410 align:start position:0%
that means I doesn't it's a left or a
child<00:01:10.590><c> at</c><00:01:10.740><c> all</c><00:01:10.890><c> and</c><00:01:11.250><c> that</c><00:01:11.670><c> doesn't</c><00:01:11.909><c> mean</c><00:01:12.000><c> they</c>

00:01:12.410 --> 00:01:12.420 align:start position:0%
child at all and that doesn't mean they
 

00:01:12.420 --> 00:01:15.620 align:start position:0%
child at all and that doesn't mean they
have<00:01:12.659><c> there</c><00:01:13.170><c> is</c><00:01:13.290><c> no</c><00:01:13.520><c> known</c><00:01:14.520><c> there</c><00:01:14.760><c> we</c><00:01:15.060><c> be</c><00:01:15.210><c> like</c>

00:01:15.620 --> 00:01:15.630 align:start position:0%
have there is no known there we be like
 

00:01:15.630 --> 00:01:19.670 align:start position:0%
have there is no known there we be like
the<00:01:15.869><c> leaf</c><00:01:16.049><c> node</c><00:01:16.320><c> right</c><00:01:17.450><c> so</c><00:01:18.450><c> what's</c><00:01:19.409><c> the</c><00:01:19.470><c> bigger</c>

00:01:19.670 --> 00:01:19.680 align:start position:0%
the leaf node right so what's the bigger
 

00:01:19.680 --> 00:01:22.820 align:start position:0%
the leaf node right so what's the bigger
of<00:01:19.799><c> this</c><00:01:19.970><c> well</c><00:01:20.970><c> we</c><00:01:21.479><c> do</c><00:01:21.780><c> know</c><00:01:21.960><c> from</c><00:01:22.290><c> balanced</c>

00:01:22.820 --> 00:01:22.830 align:start position:0%
of this well we do know from balanced
 

00:01:22.830 --> 00:01:25.340 align:start position:0%
of this well we do know from balanced
binary<00:01:23.040><c> search</c><00:01:23.159><c> trees</c><00:01:23.549><c> that</c><00:01:24.330><c> they</c><00:01:24.630><c> perform</c><00:01:25.080><c> in</c>

00:01:25.340 --> 00:01:25.350 align:start position:0%
binary search trees that they perform in
 

00:01:25.350 --> 00:01:27.770 align:start position:0%
binary search trees that they perform in
log<00:01:25.650><c> at</c><00:01:25.860><c> the</c><00:01:25.950><c> end</c><00:01:26.070><c> time</c><00:01:26.340><c> so</c><00:01:27.150><c> you</c><00:01:27.240><c> might</c><00:01:27.600><c> be</c>

00:01:27.770 --> 00:01:27.780 align:start position:0%
log at the end time so you might be
 

00:01:27.780 --> 00:01:30.560 align:start position:0%
log at the end time so you might be
inclined<00:01:28.320><c> to</c><00:01:28.650><c> think</c><00:01:28.829><c> that</c><00:01:29.280><c> this</c><00:01:29.520><c> is</c><00:01:29.670><c> big</c><00:01:30.450><c> ol</c>

00:01:30.560 --> 00:01:30.570 align:start position:0%
inclined to think that this is big ol
 

00:01:30.570 --> 00:01:32.319 align:start position:0%
inclined to think that this is big ol
log<00:01:30.780><c> of</c><00:01:30.960><c> n</c><00:01:31.079><c> easily</c>

00:01:32.319 --> 00:01:32.329 align:start position:0%
log of n easily
 

00:01:32.329 --> 00:01:35.230 align:start position:0%
log of n easily
unfortunately<00:01:33.329><c> you'd</c><00:01:33.450><c> be</c><00:01:33.600><c> incorrect</c><00:01:33.930><c> right</c>

00:01:35.230 --> 00:01:35.240 align:start position:0%
unfortunately you'd be incorrect right
 

00:01:35.240 --> 00:01:37.969 align:start position:0%
unfortunately you'd be incorrect right
but<00:01:36.240><c> why</c><00:01:36.420><c> would</c><00:01:36.869><c> you</c><00:01:36.960><c> be</c><00:01:37.079><c> incorrect</c><00:01:37.439><c> and</c><00:01:37.530><c> why</c>

00:01:37.969 --> 00:01:37.979 align:start position:0%
but why would you be incorrect and why
 

00:01:37.979 --> 00:01:41.510 align:start position:0%
but why would you be incorrect and why
would<00:01:38.130><c> you</c><00:01:38.159><c> be</c><00:01:38.340><c> incorrect</c><00:01:38.729><c> saying</c><00:01:39.060><c> that</c><00:01:40.520><c> this</c>

00:01:41.510 --> 00:01:41.520 align:start position:0%
would you be incorrect saying that this
 

00:01:41.520 --> 00:01:42.980 align:start position:0%
would you be incorrect saying that this
is<00:01:41.670><c> a</c><00:01:41.700><c> little</c><00:01:41.970><c> tricky</c><00:01:42.240><c> because</c><00:01:42.360><c> you</c><00:01:42.689><c> just</c><00:01:42.840><c> need</c>

00:01:42.980 --> 00:01:42.990 align:start position:0%
is a little tricky because you just need
 

00:01:42.990 --> 00:01:44.870 align:start position:0%
is a little tricky because you just need
to<00:01:43.170><c> make</c><00:01:43.320><c> sure</c><00:01:43.470><c> you</c><00:01:43.560><c> look</c><00:01:43.860><c> at</c><00:01:44.369><c> the</c><00:01:44.610><c> algorithm</c>

00:01:44.870 --> 00:01:44.880 align:start position:0%
to make sure you look at the algorithm
 

00:01:44.880 --> 00:01:46.670 align:start position:0%
to make sure you look at the algorithm
whenever<00:01:45.689><c> you're</c><00:01:45.899><c> doing</c><00:01:46.229><c> any</c><00:01:46.380><c> good</c><00:01:46.470><c> any</c>

00:01:46.670 --> 00:01:46.680 align:start position:0%
whenever you're doing any good any
 

00:01:46.680 --> 00:01:49.039 align:start position:0%
whenever you're doing any good any
questions<00:01:47.070><c> right</c><00:01:47.280><c> I</c><00:01:47.490><c> might</c><00:01:48.329><c> be</c><00:01:48.390><c> nervous</c><00:01:48.600><c> but</c>

00:01:49.039 --> 00:01:49.049 align:start position:0%
questions right I might be nervous but
 

00:01:49.049 --> 00:01:51.620 align:start position:0%
questions right I might be nervous but
make<00:01:49.530><c> sure</c><00:01:49.610><c> breathe</c><00:01:50.610><c> and</c><00:01:50.909><c> look</c><00:01:51.329><c> at</c><00:01:51.479><c> them</c>

00:01:51.620 --> 00:01:51.630 align:start position:0%
make sure breathe and look at them
 

00:01:51.630 --> 00:01:53.330 align:start position:0%
make sure breathe and look at them
because<00:01:51.930><c> they</c><00:01:52.170><c> may</c><00:01:52.320><c> do</c><00:01:52.799><c> something</c><00:01:53.009><c> like</c><00:01:53.159><c> this</c>

00:01:53.330 --> 00:01:53.340 align:start position:0%
because they may do something like this
 

00:01:53.340 --> 00:01:58.190 align:start position:0%
because they may do something like this
to<00:01:53.520><c> trick</c><00:01:53.700><c> you</c><00:01:53.880><c> okay</c><00:01:54.740><c> so</c><00:01:55.740><c> why</c><00:01:56.090><c> well</c>

00:01:58.190 --> 00:01:58.200 align:start position:0%
to trick you okay so why well
 

00:01:58.200 --> 00:02:01.280 align:start position:0%
to trick you okay so why well
we're<00:01:58.590><c> returning</c><00:01:59.310><c> the</c><00:02:00.090><c> sum</c><00:02:00.390><c> of</c><00:02:00.570><c> the</c><00:02:00.899><c> left</c>

00:02:01.280 --> 00:02:01.290 align:start position:0%
we're returning the sum of the left
 

00:02:01.290 --> 00:02:04.130 align:start position:0%
we're returning the sum of the left
basically<00:02:01.820><c> the</c><00:02:02.820><c> left</c><00:02:03.090><c> sub-tree</c><00:02:03.329><c> plus</c><00:02:04.020><c> the</c>

00:02:04.130 --> 00:02:04.140 align:start position:0%
basically the left sub-tree plus the
 

00:02:04.140 --> 00:02:05.780 align:start position:0%
basically the left sub-tree plus the
current<00:02:04.290><c> node</c><00:02:04.500><c> value</c><00:02:04.860><c> plus</c><00:02:05.340><c> its</c><00:02:05.549><c> right</c>

00:02:05.780 --> 00:02:05.790 align:start position:0%
current node value plus its right
 

00:02:05.790 --> 00:02:06.499 align:start position:0%
current node value plus its right
subtree

00:02:06.499 --> 00:02:06.509 align:start position:0%
subtree
 

00:02:06.509 --> 00:02:09.320 align:start position:0%
subtree
we<00:02:07.500><c> recursively</c><00:02:07.950><c> go</c><00:02:08.399><c> down</c><00:02:08.580><c> there</c><00:02:08.849><c> sub</c><00:02:09.060><c> trees</c>

00:02:09.320 --> 00:02:09.330 align:start position:0%
we recursively go down there sub trees
 

00:02:09.330 --> 00:02:12.320 align:start position:0%
we recursively go down there sub trees
okay<00:02:10.289><c> so</c><00:02:10.610><c> what</c><00:02:11.610><c> this</c><00:02:11.760><c> means</c><00:02:11.970><c> is</c><00:02:12.090><c> we're</c><00:02:12.209><c> going</c>

00:02:12.320 --> 00:02:12.330 align:start position:0%
okay so what this means is we're going
 

00:02:12.330 --> 00:02:13.160 align:start position:0%
okay so what this means is we're going
to<00:02:12.390><c> be</c><00:02:12.450><c> hitting</c>

00:02:13.160 --> 00:02:13.170 align:start position:0%
to be hitting
 

00:02:13.170 --> 00:02:16.700 align:start position:0%
to be hitting
every<00:02:13.470><c> node</c><00:02:13.770><c> in</c><00:02:13.980><c> the</c><00:02:14.069><c> tree</c><00:02:14.750><c> right</c><00:02:15.750><c> so</c><00:02:16.230><c> there's</c>

00:02:16.700 --> 00:02:16.710 align:start position:0%
every node in the tree right so there's
 

00:02:16.710 --> 00:02:20.360 align:start position:0%
every node in the tree right so there's
some<00:02:17.100><c> n</c><00:02:17.690><c> number</c><00:02:18.690><c> of</c><00:02:18.810><c> nodes</c><00:02:19.170><c> in</c><00:02:19.470><c> the</c><00:02:19.560><c> tree</c><00:02:19.709><c> we</c>

00:02:20.360 --> 00:02:20.370 align:start position:0%
some n number of nodes in the tree we
 

00:02:20.370 --> 00:02:22.330 align:start position:0%
some n number of nodes in the tree we
don't<00:02:20.550><c> know</c><00:02:20.760><c> we're</c><00:02:21.390><c> just</c><00:02:21.569><c> giving</c><00:02:21.870><c> a</c><00:02:21.959><c> node</c>

00:02:22.330 --> 00:02:22.340 align:start position:0%
don't know we're just giving a node
 

00:02:22.340 --> 00:02:24.680 align:start position:0%
don't know we're just giving a node
we're<00:02:23.340><c> we're</c><00:02:23.790><c> just</c><00:02:23.910><c> given</c><00:02:24.090><c> a</c><00:02:24.150><c> node</c><00:02:24.390><c> that</c><00:02:24.540><c> we're</c>

00:02:24.680 --> 00:02:24.690 align:start position:0%
we're we're just given a node that we're
 

00:02:24.690 --> 00:02:26.660 align:start position:0%
we're we're just given a node that we're
going<00:02:24.780><c> to</c><00:02:24.840><c> start</c><00:02:25.110><c> out</c><00:02:25.430><c> typically</c><00:02:26.430><c> you</c>

00:02:26.660 --> 00:02:26.670 align:start position:0%
going to start out typically you
 

00:02:26.670 --> 00:02:28.670 align:start position:0%
going to start out typically you
wouldn't<00:02:26.840><c> if</c><00:02:27.840><c> trying</c><00:02:28.170><c> to</c><00:02:28.230><c> find</c><00:02:28.380><c> the</c><00:02:28.440><c> sum</c><00:02:28.650><c> of</c>

00:02:28.670 --> 00:02:28.680 align:start position:0%
wouldn't if trying to find the sum of
 

00:02:28.680 --> 00:02:29.960 align:start position:0%
wouldn't if trying to find the sum of
the<00:02:28.830><c> balanced</c><00:02:29.160><c> search</c><00:02:29.520><c> tree</c>

00:02:29.960 --> 00:02:29.970 align:start position:0%
the balanced search tree
 

00:02:29.970 --> 00:02:33.020 align:start position:0%
the balanced search tree
typically<00:02:30.390><c> I</c><00:02:30.450><c> started</c><00:02:30.720><c> to</c><00:02:30.810><c> root</c><00:02:31.050><c> so</c><00:02:31.910><c> you</c><00:02:32.910><c> know</c>

00:02:33.020 --> 00:02:33.030 align:start position:0%
typically I started to root so you know
 

00:02:33.030 --> 00:02:34.520 align:start position:0%
typically I started to root so you know
it<00:02:33.090><c> doesn't</c><00:02:33.240><c> take</c><00:02:33.360><c> me</c><00:02:33.510><c> matter</c><00:02:33.720><c> but</c><00:02:34.290><c> let's</c><00:02:34.470><c> say</c>

00:02:34.520 --> 00:02:34.530 align:start position:0%
it doesn't take me matter but let's say
 

00:02:34.530 --> 00:02:36.380 align:start position:0%
it doesn't take me matter but let's say
we're<00:02:34.680><c> starting</c><00:02:34.709><c> to</c><00:02:34.950><c> root</c><00:02:35.160><c> we're</c><00:02:36.030><c> be</c><00:02:36.120><c> hitting</c>

00:02:36.380 --> 00:02:36.390 align:start position:0%
we're starting to root we're be hitting
 

00:02:36.390 --> 00:02:42.110 align:start position:0%
we're starting to root we're be hitting
every<00:02:36.900><c> node</c><00:02:37.200><c> in</c><00:02:37.500><c> the</c><00:02:37.709><c> tree</c><00:02:39.890><c> his</c><00:02:40.890><c> in</c><00:02:41.160><c> it</c><00:02:41.280><c> let's</c>

00:02:42.110 --> 00:02:42.120 align:start position:0%
every node in the tree his in it let's
 

00:02:42.120 --> 00:02:44.540 align:start position:0%
every node in the tree his in it let's
let's<00:02:42.540><c> go</c><00:02:42.750><c> back</c><00:02:42.780><c> to</c><00:02:43.080><c> why</c><00:02:43.260><c> if</c><00:02:43.620><c> it</c><00:02:43.890><c> was</c><00:02:44.010><c> log</c><00:02:44.220><c> of</c><00:02:44.400><c> N</c>

00:02:44.540 --> 00:02:44.550 align:start position:0%
let's go back to why if it was log of N
 

00:02:44.550 --> 00:02:47.360 align:start position:0%
let's go back to why if it was log of N
and<00:02:45.060><c> if</c><00:02:45.750><c> I</c><00:02:45.840><c> -</c><00:02:46.470><c> when</c><00:02:46.620><c> I</c><00:02:46.650><c> search</c><00:02:46.920><c> me</c><00:02:47.100><c> when</c><00:02:47.220><c> we're</c>

00:02:47.360 --> 00:02:47.370 align:start position:0%
and if I - when I search me when we're
 

00:02:47.370 --> 00:02:49.940 align:start position:0%
and if I - when I search me when we're
searching<00:02:47.850><c> or</c><00:02:48.120><c> inserting</c><00:02:48.600><c> deleting</c><00:02:48.800><c> we</c><00:02:49.800><c> can</c>

00:02:49.940 --> 00:02:49.950 align:start position:0%
searching or inserting deleting we can
 

00:02:49.950 --> 00:02:52.640 align:start position:0%
searching or inserting deleting we can
eliminate<00:02:50.220><c> half</c><00:02:51.090><c> the</c><00:02:51.390><c> tree</c><00:02:51.720><c> at</c><00:02:52.019><c> every</c><00:02:52.470><c> level</c>

00:02:52.640 --> 00:02:52.650 align:start position:0%
eliminate half the tree at every level
 

00:02:52.650 --> 00:02:55.130 align:start position:0%
eliminate half the tree at every level
we<00:02:52.890><c> go</c><00:02:53.070><c> down</c><00:02:53.340><c> I</c><00:02:53.700><c> so</c><00:02:54.570><c> we're</c><00:02:54.720><c> always</c><00:02:54.900><c> learning</c>

00:02:55.130 --> 00:02:55.140 align:start position:0%
we go down I so we're always learning
 

00:02:55.140 --> 00:02:58.400 align:start position:0%
we go down I so we're always learning
half<00:02:55.860><c> the</c><00:02:56.100><c> tree</c><00:02:56.340><c> if</c><00:02:56.610><c> we</c><00:02:57.060><c> starts</c><00:02:57.330><c> a</c><00:02:57.420><c> root</c><00:02:57.630><c> say</c>

00:02:58.400 --> 00:02:58.410 align:start position:0%
half the tree if we starts a root say
 

00:02:58.410 --> 00:03:00.500 align:start position:0%
half the tree if we starts a root say
that<00:02:58.650><c> value</c><00:02:59.130><c> is</c><00:02:59.220><c> five</c><00:02:59.459><c> but</c><00:03:00.000><c> we're</c><00:03:00.269><c> looking</c>

00:03:00.500 --> 00:03:00.510 align:start position:0%
that value is five but we're looking
 

00:03:00.510 --> 00:03:02.600 align:start position:0%
that value is five but we're looking
looking<00:03:01.019><c> for</c><00:03:01.080><c> seven</c><00:03:01.530><c> we</c><00:03:02.070><c> know</c><00:03:02.280><c> that</c><00:03:02.459><c> we</c><00:03:02.519><c> have</c>

00:03:02.600 --> 00:03:02.610 align:start position:0%
looking for seven we know that we have
 

00:03:02.610 --> 00:03:04.580 align:start position:0%
looking for seven we know that we have
to<00:03:02.670><c> go</c><00:03:02.790><c> to</c><00:03:02.850><c> the</c><00:03:03.120><c> to</c><00:03:03.900><c> the</c><00:03:04.019><c> right</c><00:03:04.290><c> subtree</c>

00:03:04.580 --> 00:03:04.590 align:start position:0%
to go to the to the right subtree
 

00:03:04.590 --> 00:03:07.280 align:start position:0%
to go to the to the right subtree
meaning<00:03:05.100><c> the</c><00:03:05.400><c> left</c><00:03:05.550><c> subtree</c><00:03:05.790><c> gone</c><00:03:06.690><c> we</c><00:03:07.140><c> don't</c>

00:03:07.280 --> 00:03:07.290 align:start position:0%
meaning the left subtree gone we don't
 

00:03:07.290 --> 00:03:08.900 align:start position:0%
meaning the left subtree gone we don't
care<00:03:07.470><c> about</c><00:03:07.500><c> any</c><00:03:07.739><c> more</c><00:03:07.860><c> right</c><00:03:08.610><c> and</c><00:03:08.790><c> then</c>

00:03:08.900 --> 00:03:08.910 align:start position:0%
care about any more right and then
 

00:03:08.910 --> 00:03:10.160 align:start position:0%
care about any more right and then
you're<00:03:09.000><c> gonna</c><00:03:09.120><c> keep</c><00:03:09.269><c> doing</c><00:03:09.600><c> that</c><00:03:09.690><c> as</c><00:03:09.900><c> you</c><00:03:10.050><c> go</c>

00:03:10.160 --> 00:03:10.170 align:start position:0%
you're gonna keep doing that as you go
 

00:03:10.170 --> 00:03:13.729 align:start position:0%
you're gonna keep doing that as you go
down<00:03:10.410><c> that's</c><00:03:10.769><c> wise</c><00:03:11.040><c> log</c><00:03:11.430><c> of</c><00:03:11.610><c> n</c><00:03:12.320><c> like</c><00:03:13.320><c> here</c>

00:03:13.729 --> 00:03:13.739 align:start position:0%
down that's wise log of n like here
 

00:03:13.739 --> 00:03:15.920 align:start position:0%
down that's wise log of n like here
we're<00:03:14.430><c> not</c><00:03:14.580><c> doing</c><00:03:14.910><c> that</c><00:03:15.120><c> we're</c><00:03:15.450><c> not</c><00:03:15.480><c> skipping</c>

00:03:15.920 --> 00:03:15.930 align:start position:0%
we're not doing that we're not skipping
 

00:03:15.930 --> 00:03:17.840 align:start position:0%
we're not doing that we're not skipping
half<00:03:16.230><c> the</c><00:03:16.290><c> trees</c><00:03:16.620><c> at</c><00:03:16.860><c> every</c><00:03:17.070><c> level</c><00:03:17.250><c> we're</c>

00:03:17.840 --> 00:03:17.850 align:start position:0%
half the trees at every level we're
 

00:03:17.850 --> 00:03:22.520 align:start position:0%
half the trees at every level we're
going<00:03:18.000><c> through</c><00:03:18.180><c> all</c><00:03:18.450><c> of</c><00:03:18.510><c> them</c><00:03:20.299><c> so</c><00:03:21.299><c> that</c><00:03:22.290><c> means</c>

00:03:22.520 --> 00:03:22.530 align:start position:0%
going through all of them so that means
 

00:03:22.530 --> 00:03:24.229 align:start position:0%
going through all of them so that means
whatever<00:03:22.799><c> how</c><00:03:23.340><c> many</c><00:03:23.579><c> nodes</c><00:03:23.820><c> are</c><00:03:24.000><c> in</c><00:03:24.120><c> the</c><00:03:24.180><c> tree</c>

00:03:24.229 --> 00:03:24.239 align:start position:0%
whatever how many nodes are in the tree
 

00:03:24.239 --> 00:03:27.350 align:start position:0%
whatever how many nodes are in the tree
is<00:03:24.750><c> the</c><00:03:24.900><c> determine</c><00:03:25.470><c> the</c><00:03:26.190><c> time</c><00:03:26.459><c> to</c><00:03:27.030><c> go</c><00:03:27.329><c> through</c>

00:03:27.350 --> 00:03:27.360 align:start position:0%
is the determine the time to go through
 

00:03:27.360 --> 00:03:29.660 align:start position:0%
is the determine the time to go through
Mon<00:03:27.810><c> so</c><00:03:28.200><c> it's</c><00:03:28.620><c> there's</c><00:03:28.920><c> like</c><00:03:29.130><c> ten</c><00:03:29.400><c> nodes</c><00:03:29.519><c> in</c>

00:03:29.660 --> 00:03:29.670 align:start position:0%
Mon so it's there's like ten nodes in
 

00:03:29.670 --> 00:03:31.610 align:start position:0%
Mon so it's there's like ten nodes in
the<00:03:29.730><c> tree</c><00:03:29.790><c> it</c><00:03:30.450><c> takes</c><00:03:30.750><c> some</c><00:03:30.930><c> amount</c><00:03:31.170><c> of</c><00:03:31.380><c> time</c>

00:03:31.610 --> 00:03:31.620 align:start position:0%
the tree it takes some amount of time
 

00:03:31.620 --> 00:03:34.819 align:start position:0%
the tree it takes some amount of time
well<00:03:32.280><c> if</c><00:03:32.370><c> we</c><00:03:32.459><c> increase</c><00:03:32.730><c> to</c><00:03:32.880><c> 100</c><00:03:33.530><c> it's</c><00:03:34.530><c> going</c><00:03:34.769><c> to</c>

00:03:34.819 --> 00:03:34.829 align:start position:0%
well if we increase to 100 it's going to
 

00:03:34.829 --> 00:03:38.000 align:start position:0%
well if we increase to 100 it's going to
be<00:03:36.079><c> proportional</c><00:03:37.079><c> the</c><00:03:37.650><c> times</c><00:03:37.860><c> will</c><00:03:37.980><c> be</c>

00:03:38.000 --> 00:03:38.010 align:start position:0%
be proportional the times will be
 

00:03:38.010 --> 00:03:40.220 align:start position:0%
be proportional the times will be
proportional<00:03:38.549><c> to</c><00:03:38.760><c> is</c><00:03:39.030><c> the</c><00:03:39.150><c> size</c><00:03:39.329><c> Li</c><00:03:39.570><c> of</c><00:03:39.780><c> the</c>

00:03:40.220 --> 00:03:40.230 align:start position:0%
proportional to is the size Li of the
 

00:03:40.230 --> 00:03:43.670 align:start position:0%
proportional to is the size Li of the
amount<00:03:40.440><c> of</c><00:03:40.530><c> nodes</c><00:03:40.860><c> in</c><00:03:41.010><c> the</c><00:03:41.070><c> tree</c><00:03:41.780><c> and</c><00:03:42.780><c> that's</c>

00:03:43.670 --> 00:03:43.680 align:start position:0%
amount of nodes in the tree and that's
 

00:03:43.680 --> 00:03:46.759 align:start position:0%
amount of nodes in the tree and that's
basically<00:03:44.670><c> all</c><00:03:44.970><c> this</c><00:03:45.360><c> means</c><00:03:45.600><c> right</c><00:03:45.840><c> so</c><00:03:46.709><c> this</c>

00:03:46.759 --> 00:03:46.769 align:start position:0%
basically all this means right so this
 

00:03:46.769 --> 00:03:49.250 align:start position:0%
basically all this means right so this
is<00:03:46.860><c> gonna</c><00:03:46.980><c> be</c><00:03:47.070><c> a</c><00:03:47.160><c> big</c><00:03:47.459><c> o</c><00:03:47.840><c> this</c><00:03:48.840><c> actually</c><00:03:49.110><c> may</c>

00:03:49.250 --> 00:03:49.260 align:start position:0%
is gonna be a big o this actually may
 

00:03:49.260 --> 00:03:52.640 align:start position:0%
is gonna be a big o this actually may
Big<00:03:49.560><c> O</c><00:03:49.590><c> M</c><00:03:50.269><c> because</c><00:03:51.269><c> added</c><00:03:51.600><c> turn</c><00:03:51.840><c> is</c><00:03:52.049><c> determined</c>

00:03:52.640 --> 00:03:52.650 align:start position:0%
Big O M because added turn is determined
 

00:03:52.650 --> 00:03:55.550 align:start position:0%
Big O M because added turn is determined
by<00:03:52.890><c> how</c><00:03:53.100><c> many</c><00:03:53.310><c> nodes</c><00:03:53.549><c> are</c><00:03:53.940><c> in</c><00:03:54.120><c> the</c><00:03:54.150><c> tree</c><00:03:54.560><c> and</c>

00:03:55.550 --> 00:03:55.560 align:start position:0%
by how many nodes are in the tree and
 

00:03:55.560 --> 00:03:58.420 align:start position:0%
by how many nodes are in the tree and
we're<00:03:55.739><c> going</c><00:03:55.860><c> to</c><00:03:55.920><c> traverse</c><00:03:56.070><c> all</c><00:03:56.459><c> of</c><00:03:56.579><c> them</c><00:03:56.760><c> and</c>

00:03:58.420 --> 00:03:58.430 align:start position:0%
we're going to traverse all of them and
 

00:03:58.430 --> 00:04:02.000 align:start position:0%
we're going to traverse all of them and
either<00:03:59.430><c> no</c><00:03:59.790><c> matter</c><00:03:59.820><c> no</c><00:04:00.239><c> matter</c><00:04:00.540><c> what</c><00:04:00.860><c> the</c><00:04:01.860><c> size</c>

00:04:02.000 --> 00:04:02.010 align:start position:0%
either no matter no matter what the size
 

00:04:02.010 --> 00:04:02.930 align:start position:0%
either no matter no matter what the size
the<00:04:02.190><c> input</c><00:04:02.430><c> which</c><00:04:02.579><c> I'm</c><00:04:02.670><c> going</c><00:04:02.790><c> to</c><00:04:02.850><c> traverse</c>

00:04:02.930 --> 00:04:02.940 align:start position:0%
the input which I'm going to traverse
 

00:04:02.940 --> 00:04:04.880 align:start position:0%
the input which I'm going to traverse
all<00:04:03.239><c> of</c><00:04:03.390><c> them</c><00:04:03.540><c> so</c><00:04:03.780><c> these</c><00:04:04.350><c> are</c><00:04:04.500><c> linearly</c>

00:04:04.880 --> 00:04:04.890 align:start position:0%
all of them so these are linearly
 

00:04:04.890 --> 00:04:07.190 align:start position:0%
all of them so these are linearly
proportional<00:04:05.130><c> to</c><00:04:06.090><c> each</c><00:04:06.209><c> other</c><00:04:06.450><c> however</c><00:04:06.930><c> long</c>

00:04:07.190 --> 00:04:07.200 align:start position:0%
proportional to each other however long
 

00:04:07.200 --> 00:04:09.890 align:start position:0%
proportional to each other however long
it<00:04:07.290><c> takes</c><00:04:07.320><c> to</c><00:04:08.299><c> go</c><00:04:09.299><c> through</c><00:04:09.540><c> all</c><00:04:09.600><c> the</c><00:04:09.690><c> nodes</c>

00:04:09.890 --> 00:04:09.900 align:start position:0%
it takes to go through all the nodes
 

00:04:09.900 --> 00:04:13.250 align:start position:0%
it takes to go through all the nodes
depends<00:04:10.650><c> on</c><00:04:10.769><c> the</c><00:04:11.040><c> size</c><00:04:11.250><c> of</c><00:04:11.430><c> the</c><00:04:11.489><c> input</c><00:04:11.609><c> so</c><00:04:12.510><c> this</c>

00:04:13.250 --> 00:04:13.260 align:start position:0%
depends on the size of the input so this
 

00:04:13.260 --> 00:04:15.259 align:start position:0%
depends on the size of the input so this
is<00:04:13.380><c> just</c><00:04:13.560><c> Big</c><00:04:13.950><c> O</c><00:04:14.070><c> of</c><00:04:14.100><c> n</c><00:04:14.340><c> all</c><00:04:14.670><c> right</c><00:04:14.730><c> so</c><00:04:15.090><c> just</c>

00:04:15.259 --> 00:04:15.269 align:start position:0%
is just Big O of n all right so just
 

00:04:15.269 --> 00:04:17.840 align:start position:0%
is just Big O of n all right so just
make<00:04:15.420><c> sure</c><00:04:15.530><c> that</c><00:04:16.530><c> you're</c><00:04:16.709><c> understanding</c><00:04:17.010><c> the</c>

00:04:17.840 --> 00:04:17.850 align:start position:0%
make sure that you're understanding the
 

00:04:17.850 --> 00:04:19.879 align:start position:0%
make sure that you're understanding the
code<00:04:18.590><c> this</c><00:04:19.590><c> was</c>

00:04:19.879 --> 00:04:19.889 align:start position:0%
code this was
 

00:04:19.889 --> 00:04:21.050 align:start position:0%
code this was
this<00:04:20.159><c> would</c><00:04:20.310><c> mean</c><00:04:20.430><c> that</c><00:04:20.579><c> you</c><00:04:20.669><c> kind</c><00:04:20.849><c> of</c><00:04:20.879><c> had</c><00:04:21.030><c> to</c>

00:04:21.050 --> 00:04:21.060 align:start position:0%
this would mean that you kind of had to
 

00:04:21.060 --> 00:04:22.520 align:start position:0%
this would mean that you kind of had to
understand<00:04:21.419><c> how</c><00:04:21.599><c> about</c><00:04:21.870><c> balanced</c><00:04:22.379><c> binary</c>

00:04:22.520 --> 00:04:22.530 align:start position:0%
understand how about balanced binary
 

00:04:22.530 --> 00:04:24.740 align:start position:0%
understand how about balanced binary
search<00:04:22.710><c> tree</c><00:04:23.009><c> works</c><00:04:23.189><c> and</c><00:04:23.939><c> why</c><00:04:24.180><c> it's</c><00:04:24.389><c> a</c><00:04:24.449><c> lot</c><00:04:24.599><c> of</c>

00:04:24.740 --> 00:04:24.750 align:start position:0%
search tree works and why it's a lot of
 

00:04:24.750 --> 00:04:26.899 align:start position:0%
search tree works and why it's a lot of
n<00:04:25.129><c> because</c><00:04:26.129><c> you're</c><00:04:26.250><c> not</c><00:04:26.400><c> iterating</c><00:04:26.879><c> through</c>

00:04:26.899 --> 00:04:26.909 align:start position:0%
n because you're not iterating through
 

00:04:26.909 --> 00:04:29.029 align:start position:0%
n because you're not iterating through
all<00:04:27.090><c> the</c><00:04:27.270><c> nodes</c><00:04:27.479><c> you're</c><00:04:27.900><c> removing</c><00:04:28.500><c> half</c><00:04:28.830><c> of</c>

00:04:29.029 --> 00:04:29.039 align:start position:0%
all the nodes you're removing half of
 

00:04:29.039 --> 00:04:31.550 align:start position:0%
all the nodes you're removing half of
them<00:04:29.189><c> every</c><00:04:30.150><c> depth</c><00:04:30.449><c> that</c><00:04:30.599><c> you</c><00:04:30.750><c> go</c><00:04:30.900><c> down</c><00:04:30.930><c> or</c>

00:04:31.550 --> 00:04:31.560 align:start position:0%
them every depth that you go down or
 

00:04:31.560 --> 00:04:34.520 align:start position:0%
them every depth that you go down or
level<00:04:32.009><c> whatever</c><00:04:32.719><c> okay</c><00:04:33.750><c> all</c><00:04:33.930><c> right</c><00:04:34.080><c> so</c><00:04:34.229><c> one</c><00:04:34.409><c> to</c>

00:04:34.520 --> 00:04:34.530 align:start position:0%
level whatever okay all right so one to
 

00:04:34.530 --> 00:04:38.240 align:start position:0%
level whatever okay all right so one to
our<00:04:34.699><c> next</c><00:04:35.699><c> example</c><00:04:35.759><c> here</c><00:04:36.650><c> this</c><00:04:37.650><c> one</c><00:04:37.889><c> is</c><00:04:37.919><c> we're</c>

00:04:38.240 --> 00:04:38.250 align:start position:0%
our next example here this one is we're
 

00:04:38.250 --> 00:04:41.719 align:start position:0%
our next example here this one is we're
doing<00:04:38.340><c> a</c><00:04:38.460><c> prime</c><00:04:38.729><c> check</c><00:04:39.030><c> so</c><00:04:39.900><c> we</c><00:04:40.289><c> have</c><00:04:40.379><c> a</c><00:04:40.669><c> we</c><00:04:41.669><c> have</c>

00:04:41.719 --> 00:04:41.729 align:start position:0%
doing a prime check so we have a we have
 

00:04:41.729 --> 00:04:43.550 align:start position:0%
doing a prime check so we have a we have
a<00:04:41.819><c> function</c><00:04:42.090><c> called</c><00:04:42.120><c> Prime</c><00:04:42.539><c> and</c><00:04:42.810><c> we're</c><00:04:43.469><c> just</c>

00:04:43.550 --> 00:04:43.560 align:start position:0%
a function called Prime and we're just
 

00:04:43.560 --> 00:04:45.700 align:start position:0%
a function called Prime and we're just
giving<00:04:44.400><c> it</c><00:04:44.490><c> some</c><00:04:44.729><c> number</c><00:04:45.030><c> eyes</c><00:04:45.090><c> the</c><00:04:45.210><c> argument</c>

00:04:45.700 --> 00:04:45.710 align:start position:0%
giving it some number eyes the argument
 

00:04:45.710 --> 00:04:49.390 align:start position:0%
giving it some number eyes the argument
some<00:04:46.710><c> some</c><00:04:47.180><c> primitive</c><00:04:48.180><c> int</c><00:04:48.419><c> number</c>

00:04:49.390 --> 00:04:49.400 align:start position:0%
some some primitive int number
 

00:04:49.400 --> 00:04:53.209 align:start position:0%
some some primitive int number
all<00:04:50.400><c> right</c><00:04:50.550><c> so</c><00:04:51.240><c> inside</c><00:04:51.569><c> it</c><00:04:51.689><c> here</c><00:04:52.159><c> there's</c><00:04:53.159><c> a</c>

00:04:53.209 --> 00:04:53.219 align:start position:0%
all right so inside it here there's a
 

00:04:53.219 --> 00:04:56.209 align:start position:0%
all right so inside it here there's a
for<00:04:53.490><c> loop</c><00:04:53.580><c> and</c><00:04:54.139><c> then</c><00:04:55.139><c> we're</c><00:04:55.349><c> checking</c><00:04:55.650><c> if</c><00:04:55.979><c> that</c>

00:04:56.209 --> 00:04:56.219 align:start position:0%
for loop and then we're checking if that
 

00:04:56.219 --> 00:04:59.839 align:start position:0%
for loop and then we're checking if that
number<00:04:56.990><c> so</c><00:04:57.990><c> just</c><00:04:58.500><c> do</c><00:04:59.219><c> it</c><00:04:59.250><c> once</c><00:04:59.580><c> let's</c><00:04:59.759><c> do</c>

00:04:59.839 --> 00:04:59.849 align:start position:0%
number so just do it once let's do
 

00:04:59.849 --> 00:05:02.360 align:start position:0%
number so just do it once let's do
through<00:05:00.029><c> 1</c><00:05:00.180><c> 1</c><00:05:00.629><c> through</c><00:05:00.840><c> let's</c><00:05:01.169><c> one</c><00:05:01.949><c> run</c>

00:05:02.360 --> 00:05:02.370 align:start position:0%
through 1 1 through let's one run
 

00:05:02.370 --> 00:05:07.700 align:start position:0%
through 1 1 through let's one run
through<00:05:02.580><c> let's</c><00:05:02.789><c> say</c><00:05:02.819><c> n</c><00:05:03.090><c> equals</c><00:05:03.779><c> 8</c><00:05:03.990><c> so</c><00:05:06.439><c> for</c><00:05:07.439><c> in</c><00:05:07.529><c> I</c>

00:05:07.700 --> 00:05:07.710 align:start position:0%
through let's say n equals 8 so for in I
 

00:05:07.710 --> 00:05:12.830 align:start position:0%
through let's say n equals 8 so for in I
equals<00:05:08.009><c> to</c><00:05:08.159><c> I</c><00:05:09.080><c> equals</c><00:05:10.080><c> to</c><00:05:10.139><c> I</c><00:05:11.120><c> times</c><00:05:12.120><c> I</c><00:05:12.479><c> is</c><00:05:12.509><c> less</c>

00:05:12.830 --> 00:05:12.840 align:start position:0%
equals to I equals to I times I is less
 

00:05:12.840 --> 00:05:15.200 align:start position:0%
equals to I equals to I times I is less
than<00:05:12.900><c> equal</c><00:05:13.169><c> to</c><00:05:13.199><c> n</c><00:05:13.379><c> so</c><00:05:13.740><c> I</c><00:05:14.099><c> times</c><00:05:14.520><c> I</c><00:05:14.639><c> two</c><00:05:15.000><c> times</c>

00:05:15.200 --> 00:05:15.210 align:start position:0%
than equal to n so I times I two times
 

00:05:15.210 --> 00:05:16.010 align:start position:0%
than equal to n so I times I two times
two<00:05:15.330><c> is</c><00:05:15.360><c> four</c>

00:05:16.010 --> 00:05:16.020 align:start position:0%
two is four
 

00:05:16.020 --> 00:05:18.170 align:start position:0%
two is four
that's<00:05:16.229><c> less</c><00:05:16.500><c> than</c><00:05:16.650><c> equal</c><00:05:16.830><c> to</c><00:05:16.860><c> 8</c><00:05:17.129><c> so</c><00:05:17.370><c> we</c><00:05:18.000><c> can</c><00:05:18.090><c> go</c>

00:05:18.170 --> 00:05:18.180 align:start position:0%
that's less than equal to 8 so we can go
 

00:05:18.180 --> 00:05:21.350 align:start position:0%
that's less than equal to 8 so we can go
inside<00:05:18.389><c> the</c><00:05:18.569><c> for</c><00:05:18.779><c> loop</c><00:05:18.810><c> is</c><00:05:19.199><c> n</c><00:05:19.770><c> mod</c><00:05:20.310><c> I</c><00:05:20.520><c> equals</c><00:05:21.120><c> 0</c>

00:05:21.350 --> 00:05:21.360 align:start position:0%
inside the for loop is n mod I equals 0
 

00:05:21.360 --> 00:05:23.719 align:start position:0%
inside the for loop is n mod I equals 0
what<00:05:22.169><c> does</c><00:05:22.259><c> mod</c><00:05:22.500><c> mean</c><00:05:22.740><c> that</c><00:05:23.009><c> just</c><00:05:23.129><c> means</c><00:05:23.370><c> we're</c>

00:05:23.719 --> 00:05:23.729 align:start position:0%
what does mod mean that just means we're
 

00:05:23.729 --> 00:05:27.740 align:start position:0%
what does mod mean that just means we're
technically<00:05:24.300><c> there's</c><00:05:24.539><c> a</c><00:05:24.599><c> remainder</c><00:05:24.930><c> so</c><00:05:26.690><c> n</c><00:05:27.690><c> is</c>

00:05:27.740 --> 00:05:27.750 align:start position:0%
technically there's a remainder so n is
 

00:05:27.750 --> 00:05:34.790 align:start position:0%
technically there's a remainder so n is
8<00:05:28.169><c> is</c><00:05:28.949><c> to</c><00:05:29.460><c> 8</c><00:05:30.180><c> mod</c><00:05:30.930><c> 2</c><00:05:31.770><c> it</c><00:05:32.389><c> does</c><00:05:33.389><c> equal</c><00:05:33.659><c> 0</c><00:05:33.719><c> now</c><00:05:34.560><c> this</c>

00:05:34.790 --> 00:05:34.800 align:start position:0%
8 is to 8 mod 2 it does equal 0 now this
 

00:05:34.800 --> 00:05:39.829 align:start position:0%
8 is to 8 mod 2 it does equal 0 now this
was<00:05:35.039><c> let's</c><00:05:35.069><c> say</c><00:05:36.110><c> let's</c><00:05:37.110><c> say</c><00:05:37.229><c> 9</c><00:05:37.680><c> so</c><00:05:38.370><c> 9</c><00:05:38.400><c> mod</c><00:05:39.060><c> 2</c>

00:05:39.829 --> 00:05:39.839 align:start position:0%
was let's say let's say 9 so 9 mod 2
 

00:05:39.839 --> 00:05:43.939 align:start position:0%
was let's say let's say 9 so 9 mod 2
would<00:05:40.409><c> actually</c><00:05:40.680><c> be</c><00:05:40.800><c> 1</c><00:05:41.250><c> because</c><00:05:42.319><c> 2009</c><00:05:43.319><c> 4</c><00:05:43.650><c> times</c>

00:05:43.939 --> 00:05:43.949 align:start position:0%
would actually be 1 because 2009 4 times
 

00:05:43.949 --> 00:05:47.719 align:start position:0%
would actually be 1 because 2009 4 times
and<00:05:44.430><c> there's</c><00:05:44.909><c> a</c><00:05:44.940><c> remainder</c><00:05:45.180><c> of</c><00:05:45.360><c> 1</c><00:05:45.659><c> so</c><00:05:46.219><c> why</c><00:05:47.219><c> we</c>

00:05:47.719 --> 00:05:47.729 align:start position:0%
and there's a remainder of 1 so why we
 

00:05:47.729 --> 00:05:48.829 align:start position:0%
and there's a remainder of 1 so why we
would<00:05:47.879><c> not</c><00:05:47.909><c> go</c><00:05:48.180><c> inside</c><00:05:48.300><c> that</c><00:05:48.509><c> for</c><00:05:48.689><c> loop</c>

00:05:48.829 --> 00:05:48.839 align:start position:0%
would not go inside that for loop
 

00:05:48.839 --> 00:05:52.670 align:start position:0%
would not go inside that for loop
because<00:05:48.900><c> 1</c><00:05:49.169><c> does</c><00:05:49.349><c> not</c><00:05:49.379><c> equal</c><00:05:49.529><c> 0</c><00:05:50.039><c> right</c><00:05:51.589><c> but</c><00:05:52.589><c> in</c>

00:05:52.670 --> 00:05:52.680 align:start position:0%
because 1 does not equal 0 right but in
 

00:05:52.680 --> 00:05:55.760 align:start position:0%
because 1 does not equal 0 right but in
this<00:05:52.740><c> case</c><00:05:52.919><c> 8</c><00:05:53.699><c> mod</c><00:05:54.029><c> 2</c><00:05:54.330><c> does</c><00:05:54.870><c> equal</c><00:05:55.080><c> 0</c><00:05:55.139><c> so</c><00:05:55.589><c> we</c>

00:05:55.760 --> 00:05:55.770 align:start position:0%
this case 8 mod 2 does equal 0 so we
 

00:05:55.770 --> 00:05:58.790 align:start position:0%
this case 8 mod 2 does equal 0 so we
return<00:05:56.159><c> false</c><00:05:56.490><c> and</c><00:05:56.879><c> we're</c><00:05:57.870><c> done</c><00:05:57.960><c> so</c><00:05:58.229><c> that</c>

00:05:58.790 --> 00:05:58.800 align:start position:0%
return false and we're done so that
 

00:05:58.800 --> 00:06:01.010 align:start position:0%
return false and we're done so that
means<00:05:59.009><c> that</c><00:05:59.189><c> 8</c><00:05:59.400><c> is</c><00:05:59.939><c> not</c><00:06:00.150><c> a</c><00:06:00.210><c> prime</c><00:06:00.479><c> number</c><00:06:00.509><c> which</c>

00:06:01.010 --> 00:06:01.020 align:start position:0%
means that 8 is not a prime number which
 

00:06:01.020 --> 00:06:02.689 align:start position:0%
means that 8 is not a prime number which
is<00:06:01.199><c> which</c><00:06:01.620><c> is</c><00:06:01.650><c> true</c><00:06:02.009><c> right</c><00:06:02.189><c> we</c><00:06:02.339><c> know</c><00:06:02.460><c> that</c><00:06:02.490><c> he's</c>

00:06:02.689 --> 00:06:02.699 align:start position:0%
is which is true right we know that he's
 

00:06:02.699 --> 00:06:04.579 align:start position:0%
is which is true right we know that he's
not<00:06:02.729><c> my</c><00:06:02.969><c> number</c><00:06:03.210><c> and</c><00:06:03.389><c> you</c><00:06:03.449><c> have</c><00:06:03.509><c> 4</c><00:06:03.719><c> times</c><00:06:03.870><c> 2</c><00:06:04.050><c> he</c>

00:06:04.579 --> 00:06:04.589 align:start position:0%
not my number and you have 4 times 2 he
 

00:06:04.589 --> 00:06:06.469 align:start position:0%
not my number and you have 4 times 2 he
comes<00:06:04.800><c> one</c><00:06:05.009><c> he</c><00:06:05.639><c> comes</c><00:06:05.819><c> forward</c><00:06:06.029><c> like</c><00:06:06.210><c> whatever</c>

00:06:06.469 --> 00:06:06.479 align:start position:0%
comes one he comes forward like whatever
 

00:06:06.479 --> 00:06:08.269 align:start position:0%
comes one he comes forward like whatever
right<00:06:06.839><c> so</c><00:06:07.199><c> now</c><00:06:07.589><c> that</c><00:06:07.650><c> we</c><00:06:07.860><c> understand</c><00:06:08.129><c> what's</c>

00:06:08.269 --> 00:06:08.279 align:start position:0%
right so now that we understand what's
 

00:06:08.279 --> 00:06:11.510 align:start position:0%
right so now that we understand what's
going<00:06:08.490><c> on</c><00:06:08.610><c> let's</c><00:06:09.300><c> let's</c><00:06:10.229><c> look</c><00:06:10.949><c> at</c><00:06:11.129><c> what</c><00:06:11.339><c> we</c><00:06:11.430><c> can</c>

00:06:11.510 --> 00:06:11.520 align:start position:0%
going on let's let's look at what we can
 

00:06:11.520 --> 00:06:12.829 align:start position:0%
going on let's let's look at what we can
eliminate<00:06:11.729><c> right</c><00:06:12.300><c> I</c><00:06:12.389><c> always</c><00:06:12.479><c> like</c><00:06:12.719><c> to</c>

00:06:12.829 --> 00:06:12.839 align:start position:0%
eliminate right I always like to
 

00:06:12.839 --> 00:06:14.540 align:start position:0%
eliminate right I always like to
eliminate<00:06:12.990><c> things</c><00:06:13.289><c> first</c><00:06:13.589><c> and</c><00:06:13.830><c> really</c><00:06:14.279><c> look</c><00:06:14.490><c> I</c>

00:06:14.540 --> 00:06:14.550 align:start position:0%
eliminate things first and really look I
 

00:06:14.550 --> 00:06:16.339 align:start position:0%
eliminate things first and really look I
would<00:06:14.699><c> need</c><00:06:14.819><c> to</c><00:06:14.909><c> care</c><00:06:15.089><c> about</c><00:06:15.120><c> this</c><00:06:16.110><c> if</c>

00:06:16.339 --> 00:06:16.349 align:start position:0%
would need to care about this if
 

00:06:16.349 --> 00:06:19.490 align:start position:0%
would need to care about this if
statement<00:06:16.439><c> inside</c><00:06:16.770><c> the</c><00:06:16.949><c> for</c><00:06:17.099><c> loop</c><00:06:18.500><c> preparing</c>

00:06:19.490 --> 00:06:19.500 align:start position:0%
statement inside the for loop preparing
 

00:06:19.500 --> 00:06:21.320 align:start position:0%
statement inside the for loop preparing
operator<00:06:19.860><c> operation</c><00:06:20.580><c> don't</c><00:06:20.879><c> care</c><00:06:21.000><c> about</c><00:06:21.150><c> it</c>

00:06:21.320 --> 00:06:21.330 align:start position:0%
operator operation don't care about it
 

00:06:21.330 --> 00:06:23.209 align:start position:0%
operator operation don't care about it
returning<00:06:21.990><c> false</c><00:06:22.289><c> don't</c><00:06:22.830><c> care</c><00:06:22.949><c> about</c><00:06:23.099><c> these</c>

00:06:23.209 --> 00:06:23.219 align:start position:0%
returning false don't care about these
 

00:06:23.219 --> 00:06:25.909 align:start position:0%
returning false don't care about these
are<00:06:23.400><c> all</c><00:06:23.490><c> big</c><00:06:23.759><c> o1</c><00:06:24.180><c> operations</c><00:06:24.719><c> returning</c><00:06:25.620><c> true</c>

00:06:25.909 --> 00:06:25.919 align:start position:0%
are all big o1 operations returning true
 

00:06:25.919 --> 00:06:27.949 align:start position:0%
are all big o1 operations returning true
Big<00:06:26.250><c> O</c><00:06:26.370><c> of</c><00:06:26.400><c> 1</c><00:06:26.699><c> so</c><00:06:27.210><c> all</c><00:06:27.240><c> we</c><00:06:27.479><c> care</c><00:06:27.659><c> about</c><00:06:27.810><c> now</c><00:06:27.930><c> is</c>

00:06:27.949 --> 00:06:27.959 align:start position:0%
Big O of 1 so all we care about now is
 

00:06:27.959 --> 00:06:30.019 align:start position:0%
Big O of 1 so all we care about now is
what's<00:06:28.380><c> happing</c><00:06:28.680><c> this</c><00:06:28.800><c> for</c><00:06:29.039><c> loop</c><00:06:29.190><c> number</c>

00:06:30.019 --> 00:06:30.029 align:start position:0%
what's happing this for loop number
 

00:06:30.029 --> 00:06:31.370 align:start position:0%
what's happing this for loop number
again<00:06:30.389><c> I</c><00:06:30.599><c> said</c><00:06:30.659><c> this</c><00:06:30.959><c> is</c><00:06:31.050><c> gonna</c><00:06:31.110><c> be</c><00:06:31.229><c> a</c><00:06:31.259><c> little</c>

00:06:31.370 --> 00:06:31.380 align:start position:0%
again I said this is gonna be a little
 

00:06:31.380 --> 00:06:32.840 align:start position:0%
again I said this is gonna be a little
tricky<00:06:31.889><c> all</c><00:06:32.339><c> right</c>

00:06:32.840 --> 00:06:32.850 align:start position:0%
tricky all right
 

00:06:32.850 --> 00:06:34.490 align:start position:0%
tricky all right
I'm<00:06:33.150><c> trying</c><00:06:33.450><c> to</c><00:06:33.510><c> trick</c><00:06:33.690><c> you</c><00:06:33.930><c> participation</c>

00:06:34.490 --> 00:06:34.500 align:start position:0%
I'm trying to trick you participation
 

00:06:34.500 --> 00:06:38.840 align:start position:0%
I'm trying to trick you participation
ease<00:06:34.680><c> make</c><00:06:35.340><c> sure</c><00:06:35.430><c> you</c><00:06:35.550><c> think</c><00:06:35.880><c> in</c><00:06:36.030><c> all</c><00:06:36.150><c> right</c><00:06:37.850><c> so</c>

00:06:38.840 --> 00:06:38.850 align:start position:0%
ease make sure you think in all right so
 

00:06:38.850 --> 00:06:41.900 align:start position:0%
ease make sure you think in all right so
we<00:06:38.970><c> have</c><00:06:39.150><c> int</c><00:06:39.900><c> I</c><00:06:40.080><c> equals</c><00:06:40.140><c> to</c><00:06:40.500><c> I</c><00:06:40.860><c> times</c><00:06:41.370><c> I</c><00:06:41.610><c> plus</c><00:06:41.790><c> R</c>

00:06:41.900 --> 00:06:41.910 align:start position:0%
we have int I equals to I times I plus R
 

00:06:41.910 --> 00:06:45.860 align:start position:0%
we have int I equals to I times I plus R
equal<00:06:42.090><c> to</c><00:06:42.120><c> n</c><00:06:42.360><c> I</c><00:06:42.840><c> plus</c><00:06:43.560><c> plus</c><00:06:44.040><c> all</c><00:06:44.670><c> right</c><00:06:44.870><c> that's</c>

00:06:45.860 --> 00:06:45.870 align:start position:0%
equal to n I plus plus all right that's
 

00:06:45.870 --> 00:06:47.690 align:start position:0%
equal to n I plus plus all right that's
what<00:06:46.020><c> you</c><00:06:46.110><c> know</c><00:06:46.350><c> ahead</c><00:06:46.800><c> of</c><00:06:46.920><c> time</c><00:06:47.040><c> this</c><00:06:47.430><c> is</c><00:06:47.550><c> not</c>

00:06:47.690 --> 00:06:47.700 align:start position:0%
what you know ahead of time this is not
 

00:06:47.700 --> 00:06:50.750 align:start position:0%
what you know ahead of time this is not
Big<00:06:47.850><c> O</c><00:06:47.970><c> of</c><00:06:48.000><c> n</c><00:06:49.040><c> hoping</c><00:06:50.040><c> and</c><00:06:50.160><c> disappoint</c><00:06:50.550><c> you</c><00:06:50.610><c> too</c>

00:06:50.750 --> 00:06:50.760 align:start position:0%
Big O of n hoping and disappoint you too
 

00:06:50.760 --> 00:06:53.060 align:start position:0%
Big O of n hoping and disappoint you too
much<00:06:50.940><c> there's</c><00:06:51.390><c> nothing</c><00:06:51.720><c> going</c><00:06:51.840><c> on</c><00:06:52.020><c> all</c><00:06:52.860><c> right</c>

00:06:53.060 --> 00:06:53.070 align:start position:0%
much there's nothing going on all right
 

00:06:53.070 --> 00:06:55.490 align:start position:0%
much there's nothing going on all right
we<00:06:53.400><c> had</c><00:06:53.550><c> this</c><00:06:53.670><c> thing</c><00:06:53.940><c> here</c><00:06:54.330><c> we're</c><00:06:54.930><c> saying</c><00:06:55.260><c> I</c>

00:06:55.490 --> 00:06:55.500 align:start position:0%
we had this thing here we're saying I
 

00:06:55.500 --> 00:06:59.660 align:start position:0%
we had this thing here we're saying I
times<00:06:55.950><c> I</c><00:06:56.130><c> is</c><00:06:56.790><c> less</c><00:06:57.330><c> than</c><00:06:57.390><c> equal</c><00:06:57.750><c> to</c><00:06:57.780><c> n</c><00:06:57.990><c> I</c><00:06:58.670><c> is</c>

00:06:59.660 --> 00:06:59.670 align:start position:0%
times I is less than equal to n I is
 

00:06:59.670 --> 00:07:03.050 align:start position:0%
times I is less than equal to n I is
less<00:07:00.630><c> than</c><00:07:00.810><c> equal</c><00:07:01.290><c> to</c><00:07:01.320><c> n</c><00:07:01.680><c> well</c><00:07:02.640><c> what's</c><00:07:02.790><c> pi</c>

00:07:03.050 --> 00:07:03.060 align:start position:0%
less than equal to n well what's pi
 

00:07:03.060 --> 00:07:07.370 align:start position:0%
less than equal to n well what's pi
times<00:07:03.360><c> I</c><00:07:03.510><c> the</c><00:07:03.570><c> same</c><00:07:03.810><c> as</c><00:07:04.020><c> I</c><00:07:04.320><c> times</c><00:07:05.040><c> I</c><00:07:06.200><c> just</c><00:07:07.200><c> same</c>

00:07:07.370 --> 00:07:07.380 align:start position:0%
times I the same as I times I just same
 

00:07:07.380 --> 00:07:12.550 align:start position:0%
times I the same as I times I just same
as<00:07:07.590><c> I</c><00:07:08.030><c> squared</c><00:07:09.030><c> is</c><00:07:10.020><c> less</c><00:07:10.200><c> than</c><00:07:10.290><c> equal</c><00:07:10.590><c> to</c><00:07:10.620><c> N</c><00:07:10.950><c> or</c>

00:07:12.550 --> 00:07:12.560 align:start position:0%
as I squared is less than equal to N or
 

00:07:12.560 --> 00:07:15.770 align:start position:0%
as I squared is less than equal to N or
we<00:07:13.560><c> could</c><00:07:13.740><c> say</c><00:07:13.980><c> I</c><00:07:14.280><c> is</c><00:07:14.490><c> less</c><00:07:14.790><c> than</c><00:07:14.940><c> equal</c><00:07:15.270><c> to</c><00:07:15.300><c> the</c>

00:07:15.770 --> 00:07:15.780 align:start position:0%
we could say I is less than equal to the
 

00:07:15.780 --> 00:07:22.400 align:start position:0%
we could say I is less than equal to the
square<00:07:16.080><c> root</c><00:07:16.110><c> of</c><00:07:16.560><c> n</c><00:07:19.640><c> this</c><00:07:20.640><c> means</c><00:07:21.060><c> that</c><00:07:21.750><c> this</c><00:07:22.200><c> is</c>

00:07:22.400 --> 00:07:22.410 align:start position:0%
square root of n this means that this is
 

00:07:22.410 --> 00:07:25.490 align:start position:0%
square root of n this means that this is
actually<00:07:22.920><c> bigger</c><00:07:23.310><c> the</c><00:07:23.520><c> square</c><00:07:23.910><c> root</c><00:07:24.150><c> of</c><00:07:24.270><c> n</c><00:07:24.500><c> but</c>

00:07:25.490 --> 00:07:25.500 align:start position:0%
actually bigger the square root of n but
 

00:07:25.500 --> 00:07:27.860 align:start position:0%
actually bigger the square root of n but
let's<00:07:25.800><c> but</c><00:07:26.670><c> you</c><00:07:26.760><c> still</c><00:07:27.000><c> might</c><00:07:27.150><c> be</c><00:07:27.210><c> wondering</c>

00:07:27.860 --> 00:07:27.870 align:start position:0%
let's but you still might be wondering
 

00:07:27.870 --> 00:07:30.260 align:start position:0%
let's but you still might be wondering
what<00:07:28.650><c> like</c><00:07:28.980><c> why</c><00:07:29.310><c> would</c><00:07:29.550><c> you</c><00:07:29.700><c> even</c><00:07:29.880><c> do</c>

00:07:30.260 --> 00:07:30.270 align:start position:0%
what like why would you even do
 

00:07:30.270 --> 00:07:32.150 align:start position:0%
what like why would you even do
something<00:07:30.570><c> like</c><00:07:30.690><c> this</c><00:07:30.960><c> okay</c>

00:07:32.150 --> 00:07:32.160 align:start position:0%
something like this okay
 

00:07:32.160 --> 00:07:34.340 align:start position:0%
something like this okay
it's<00:07:32.430><c> just</c><00:07:32.580><c> make</c><00:07:32.730><c> you</c><00:07:32.820><c> look</c><00:07:32.850><c> but</c><00:07:33.350><c> let's</c>

00:07:34.340 --> 00:07:34.350 align:start position:0%
it's just make you look but let's
 

00:07:34.350 --> 00:07:37.430 align:start position:0%
it's just make you look but let's
actually<00:07:34.560><c> bring</c><00:07:35.100><c> in</c><00:07:35.280><c> some</c><00:07:35.430><c> examples</c><00:07:36.090><c> so</c><00:07:36.960><c> if</c><00:07:37.230><c> N</c>

00:07:37.430 --> 00:07:37.440 align:start position:0%
actually bring in some examples so if N
 

00:07:37.440 --> 00:07:43.400 align:start position:0%
actually bring in some examples so if N
equals<00:07:38.450><c> four</c><00:07:39.920><c> then</c><00:07:41.210><c> we're</c><00:07:42.210><c> gonna</c><00:07:42.390><c> go</c><00:07:42.810><c> through</c>

00:07:43.400 --> 00:07:43.410 align:start position:0%
equals four then we're gonna go through
 

00:07:43.410 --> 00:07:47.000 align:start position:0%
equals four then we're gonna go through
well<00:07:44.660><c> not</c><00:07:45.660><c> technically</c><00:07:45.960><c> square</c><00:07:46.530><c> root</c><00:07:46.710><c> is</c><00:07:46.800><c> 2</c>

00:07:47.000 --> 00:07:47.010 align:start position:0%
well not technically square root is 2
 

00:07:47.010 --> 00:07:48.830 align:start position:0%
well not technically square root is 2
right<00:07:47.400><c> so</c><00:07:47.730><c> let's</c><00:07:47.910><c> just</c><00:07:47.970><c> was</c><00:07:48.270><c> just</c><00:07:48.480><c> first</c><00:07:48.600><c> right</c>

00:07:48.830 --> 00:07:48.840 align:start position:0%
right so let's just was just first right
 

00:07:48.840 --> 00:07:51.100 align:start position:0%
right so let's just was just first right
before<00:07:49.170><c> I</c><00:07:49.230><c> get</c><00:07:49.380><c> into</c><00:07:49.470><c> that</c><00:07:49.530><c> if</c><00:07:49.770><c> N</c><00:07:50.460><c> equals</c><00:07:50.550><c> 9</c>

00:07:51.100 --> 00:07:51.110 align:start position:0%
before I get into that if N equals 9
 

00:07:51.110 --> 00:07:55.790 align:start position:0%
before I get into that if N equals 9
then<00:07:52.110><c> we</c><00:07:52.140><c> have</c><00:07:52.290><c> 3n</c><00:07:53.250><c> equals</c><00:07:54.440><c> 16</c>

00:07:55.790 --> 00:07:55.800 align:start position:0%
then we have 3n equals 16
 

00:07:55.800 --> 00:08:01.420 align:start position:0%
then we have 3n equals 16
for<00:07:56.790><c> N</c><00:07:57.420><c> equals</c><00:07:58.170><c> 25</c><00:07:59.100><c> square</c><00:07:59.700><c> root</c><00:07:59.880><c> is</c><00:08:00.210><c> 5</c><00:08:00.630><c> and</c><00:08:01.230><c> so</c>

00:08:01.420 --> 00:08:01.430 align:start position:0%
for N equals 25 square root is 5 and so
 

00:08:01.430 --> 00:08:04.670 align:start position:0%
for N equals 25 square root is 5 and so
forth<00:08:02.430><c> right</c><00:08:02.960><c> so</c><00:08:03.960><c> this</c><00:08:04.230><c> means</c><00:08:04.470><c> that</c><00:08:04.530><c> the</c>

00:08:04.670 --> 00:08:04.680 align:start position:0%
forth right so this means that the
 

00:08:04.680 --> 00:08:05.570 align:start position:0%
forth right so this means that the
amount<00:08:04.860><c> of</c><00:08:04.920><c> time</c><00:08:05.070><c> that</c><00:08:05.100><c> we're</c><00:08:05.310><c> going</c><00:08:05.490><c> through</c>

00:08:05.570 --> 00:08:05.580 align:start position:0%
amount of time that we're going through
 

00:08:05.580 --> 00:08:08.270 align:start position:0%
amount of time that we're going through
this<00:08:06.090><c> is</c><00:08:06.810><c> proportional</c><00:08:07.230><c> to</c><00:08:07.440><c> the</c><00:08:07.620><c> square</c><00:08:07.770><c> root</c>

00:08:08.270 --> 00:08:08.280 align:start position:0%
this is proportional to the square root
 

00:08:08.280 --> 00:08:15.950 align:start position:0%
this is proportional to the square root
of<00:08:09.080><c> the</c><00:08:10.080><c> size</c><00:08:10.380><c> of</c><00:08:12.710><c> the</c><00:08:13.710><c> size</c><00:08:13.950><c> or</c><00:08:14.310><c> the</c><00:08:15.060><c> yet</c><00:08:15.780><c> the</c>

00:08:15.950 --> 00:08:15.960 align:start position:0%
of the size of the size or the yet the
 

00:08:15.960 --> 00:08:19.130 align:start position:0%
of the size of the size or the yet the
size<00:08:16.170><c> of</c><00:08:16.260><c> the</c><00:08:16.350><c> input</c><00:08:16.470><c> okay</c><00:08:17.900><c> one</c><00:08:18.900><c> thing</c><00:08:19.080><c> you</c>

00:08:19.130 --> 00:08:19.140 align:start position:0%
size of the input okay one thing you
 

00:08:19.140 --> 00:08:22.730 align:start position:0%
size of the input okay one thing you
have<00:08:19.260><c> to</c><00:08:19.290><c> understand</c><00:08:19.830><c> here</c><00:08:20.130><c> is</c><00:08:21.440><c> it's</c><00:08:22.440><c> the</c><00:08:22.560><c> way</c>

00:08:22.730 --> 00:08:22.740 align:start position:0%
have to understand here is it's the way
 

00:08:22.740 --> 00:08:24.860 align:start position:0%
have to understand here is it's the way
that<00:08:23.460><c> we're</c><00:08:23.670><c> iterating</c><00:08:24.030><c> over</c><00:08:24.360><c> the</c><00:08:24.510><c> elements</c>

00:08:24.860 --> 00:08:24.870 align:start position:0%
that we're iterating over the elements
 

00:08:24.870 --> 00:08:27.230 align:start position:0%
that we're iterating over the elements
saying<00:08:25.410><c> if</c><00:08:25.620><c> it</c><00:08:25.710><c> was</c><00:08:25.740><c> just</c><00:08:25.920><c> a</c><00:08:26.070><c> less</c><00:08:26.640><c> than</c><00:08:26.910><c> equal</c>

00:08:27.230 --> 00:08:27.240 align:start position:0%
saying if it was just a less than equal
 

00:08:27.240 --> 00:08:30.140 align:start position:0%
saying if it was just a less than equal
to<00:08:27.270><c> n</c><00:08:27.740><c> that</c><00:08:28.740><c> would</c><00:08:28.920><c> be</c><00:08:29.070><c> different</c><00:08:29.610><c> right</c><00:08:29.820><c> that</c>

00:08:30.140 --> 00:08:30.150 align:start position:0%
to n that would be different right that
 

00:08:30.150 --> 00:08:32.270 align:start position:0%
to n that would be different right that
would<00:08:30.300><c> be</c><00:08:30.420><c> big</c><00:08:30.660><c> old</c><00:08:30.840><c> and</c><00:08:31.110><c> however</c><00:08:31.830><c> we're</c>

00:08:32.270 --> 00:08:32.280 align:start position:0%
would be big old and however we're
 

00:08:32.280 --> 00:08:34.760 align:start position:0%
would be big old and however we're
saying<00:08:32.460><c> I</c><00:08:32.640><c> x</c><00:08:33.030><c> i's</c><00:08:33.300><c> lesser</c><00:08:33.599><c> you'll</c><00:08:33.810><c> 2n</c><00:08:34.050><c> which</c>

00:08:34.760 --> 00:08:34.770 align:start position:0%
saying I x i's lesser you'll 2n which
 

00:08:34.770 --> 00:08:37.130 align:start position:0%
saying I x i's lesser you'll 2n which
can<00:08:35.099><c> be</c><00:08:35.280><c> refactored</c><00:08:35.760><c> to</c><00:08:36.210><c> saying</c><00:08:36.479><c> i</c><00:08:36.750><c> is</c><00:08:36.870><c> less</c>

00:08:37.130 --> 00:08:37.140 align:start position:0%
can be refactored to saying i is less
 

00:08:37.140 --> 00:08:39.409 align:start position:0%
can be refactored to saying i is less
than<00:08:37.200><c> equal</c><00:08:37.469><c> to</c><00:08:37.560><c> square</c><00:08:37.680><c> root</c><00:08:37.860><c> of</c><00:08:38.010><c> n</c><00:08:38.160><c> and</c><00:08:38.419><c> it's</c>

00:08:39.409 --> 00:08:39.419 align:start position:0%
than equal to square root of n and it's
 

00:08:39.419 --> 00:08:43.040 align:start position:0%
than equal to square root of n and it's
a<00:08:39.510><c> little</c><00:08:39.690><c> tricky</c><00:08:40.110><c> but</c><00:08:40.440><c> because</c><00:08:41.370><c> it's</c><00:08:42.360><c> because</c>

00:08:43.040 --> 00:08:43.050 align:start position:0%
a little tricky but because it's because
 

00:08:43.050 --> 00:08:44.420 align:start position:0%
a little tricky but because it's because
we're<00:08:43.169><c> going</c><00:08:43.290><c> I</c><00:08:43.500><c> less</c><00:08:43.650><c> than</c><00:08:43.680><c> the</c><00:08:43.890><c> square</c><00:08:44.070><c> root</c>

00:08:44.420 --> 00:08:44.430 align:start position:0%
we're going I less than the square root
 

00:08:44.430 --> 00:08:46.580 align:start position:0%
we're going I less than the square root
of<00:08:44.550><c> n</c><00:08:44.700><c> that</c><00:08:44.970><c> means</c>

00:08:46.580 --> 00:08:46.590 align:start position:0%
of n that means
 

00:08:46.590 --> 00:08:48.950 align:start position:0%
of n that means
that's<00:08:47.070><c> bigger</c><00:08:47.790><c> operation</c><00:08:48.570><c> because</c><00:08:48.779><c> we're</c>

00:08:48.950 --> 00:08:48.960 align:start position:0%
that's bigger operation because we're
 

00:08:48.960 --> 00:08:51.560 align:start position:0%
that's bigger operation because we're
only<00:08:49.080><c> going</c><00:08:49.440><c> up</c><00:08:49.650><c> to</c><00:08:49.920><c> the</c><00:08:50.820><c> square</c><00:08:51.150><c> root</c><00:08:51.300><c> of</c><00:08:51.390><c> n</c>

00:08:51.560 --> 00:08:51.570 align:start position:0%
only going up to the square root of n
 

00:08:51.570 --> 00:08:53.780 align:start position:0%
only going up to the square root of n
times<00:08:52.020><c> and</c><00:08:52.740><c> it's</c><00:08:52.980><c> not</c><00:08:53.130><c> even</c><00:08:53.370><c> here</c><00:08:53.610><c> we're</c>

00:08:53.780 --> 00:08:53.790 align:start position:0%
times and it's not even here we're
 

00:08:53.790 --> 00:08:55.340 align:start position:0%
times and it's not even here we're
actually<00:08:53.940><c> technically</c><00:08:54.660><c> doing</c><00:08:54.870><c> it</c><00:08:54.990><c> less</c><00:08:55.140><c> time</c>

00:08:55.340 --> 00:08:55.350 align:start position:0%
actually technically doing it less time
 

00:08:55.350 --> 00:08:58.510 align:start position:0%
actually technically doing it less time
so<00:08:55.500><c> he's</c><00:08:55.680><c> starting</c><00:08:55.800><c> out</c><00:08:55.950><c> too</c><00:08:56.390><c> right</c>

00:08:58.510 --> 00:08:58.520 align:start position:0%
so he's starting out too right
 

00:08:58.520 --> 00:09:02.710 align:start position:0%
so he's starting out too right
okay<00:08:59.520><c> so</c><00:09:00.260><c> the</c><00:09:01.260><c> thing</c><00:09:01.560><c> key</c><00:09:01.920><c> takeaway</c><00:09:02.220><c> here</c><00:09:02.250><c> is</c>

00:09:02.710 --> 00:09:02.720 align:start position:0%
okay so the thing key takeaway here is
 

00:09:02.720 --> 00:09:04.970 align:start position:0%
okay so the thing key takeaway here is
make<00:09:03.720><c> sure</c><00:09:03.750><c> you</c><00:09:03.930><c> look</c><00:09:04.290><c> at</c><00:09:04.410><c> the</c><00:09:04.500><c> code</c>

00:09:04.970 --> 00:09:04.980 align:start position:0%
make sure you look at the code
 

00:09:04.980 --> 00:09:08.660 align:start position:0%
make sure you look at the code
don't<00:09:05.370><c> skim</c><00:09:05.790><c> over</c><00:09:06.000><c> it</c><00:09:06.860><c> you</c><00:09:07.860><c> have</c><00:09:07.890><c> time</c><00:09:08.310><c> okay</c>

00:09:08.660 --> 00:09:08.670 align:start position:0%
don't skim over it you have time okay
 

00:09:08.670 --> 00:09:12.140 align:start position:0%
don't skim over it you have time okay
you<00:09:09.660><c> have</c><00:09:09.779><c> time</c><00:09:09.990><c> to</c><00:09:10.140><c> take</c><00:09:10.320><c> these</c><00:09:10.550><c> briefs</c><00:09:11.550><c> relax</c>

00:09:12.140 --> 00:09:12.150 align:start position:0%
you have time to take these briefs relax
 

00:09:12.150 --> 00:09:15.290 align:start position:0%
you have time to take these briefs relax
and<00:09:12.510><c> look</c><00:09:13.320><c> over</c><00:09:13.529><c> these</c><00:09:13.860><c> because</c><00:09:14.850><c> this</c><00:09:14.940><c> was</c><00:09:15.120><c> an</c>

00:09:15.290 --> 00:09:15.300 align:start position:0%
and look over these because this was an
 

00:09:15.300 --> 00:09:17.150 align:start position:0%
and look over these because this was an
actual<00:09:15.600><c> real</c><00:09:15.870><c> interview</c><00:09:16.500><c> question</c><00:09:16.920><c> I</c><00:09:16.980><c> didn't</c>

00:09:17.150 --> 00:09:17.160 align:start position:0%
actual real interview question I didn't
 

00:09:17.160 --> 00:09:19.220 align:start position:0%
actual real interview question I didn't
just<00:09:17.279><c> make</c><00:09:17.339><c> this</c><00:09:17.490><c> up</c><00:09:17.730><c> right</c><00:09:18.150><c> it</c><00:09:18.330><c> came</c><00:09:18.480><c> from</c><00:09:18.750><c> the</c>

00:09:19.220 --> 00:09:19.230 align:start position:0%
just make this up right it came from the
 

00:09:19.230 --> 00:09:20.810 align:start position:0%
just make this up right it came from the
book<00:09:19.410><c> cracking</c><00:09:19.680><c> the</c><00:09:19.920><c> coding</c><00:09:20.220><c> interview</c><00:09:20.580><c> and</c>

00:09:20.810 --> 00:09:20.820 align:start position:0%
book cracking the coding interview and
 

00:09:20.820 --> 00:09:23.450 align:start position:0%
book cracking the coding interview and
all<00:09:21.180><c> those</c><00:09:21.390><c> questions</c><00:09:21.900><c> in</c><00:09:22.290><c> there</c><00:09:22.440><c> or</c><00:09:22.650><c> works</c>

00:09:23.450 --> 00:09:23.460 align:start position:0%
all those questions in there or works
 

00:09:23.460 --> 00:09:28.570 align:start position:0%
all those questions in there or works
there<00:09:23.760><c> real</c><00:09:24.060><c> interview</c><00:09:24.420><c> questions</c><00:09:26.120><c> so</c>

00:09:28.570 --> 00:09:28.580 align:start position:0%
there real interview questions so
 

00:09:28.580 --> 00:09:31.760 align:start position:0%
there real interview questions so
whenever<00:09:29.580><c> you</c><00:09:29.850><c> see</c><00:09:29.880><c> in</c><00:09:30.420><c> the</c><00:09:30.540><c> for</c><00:09:30.810><c> loop</c><00:09:30.839><c> is</c><00:09:31.230><c> less</c>

00:09:31.760 --> 00:09:31.770 align:start position:0%
whenever you see in the for loop is less
 

00:09:31.770 --> 00:09:35.090 align:start position:0%
whenever you see in the for loop is less
than<00:09:31.920><c> equal</c><00:09:32.160><c> to</c><00:09:32.190><c> and</c><00:09:33.260><c> typically</c><00:09:34.260><c> it's</c><00:09:35.010><c> going</c>

00:09:35.090 --> 00:09:35.100 align:start position:0%
than equal to and typically it's going
 

00:09:35.100 --> 00:09:38.120 align:start position:0%
than equal to and typically it's going
to<00:09:35.279><c> be</c><00:09:35.400><c> big</c><00:09:35.640><c> events</c><00:09:36.029><c> that</c><00:09:36.210><c> loop</c><00:09:36.420><c> but</c><00:09:37.140><c> if</c><00:09:37.740><c> it</c><00:09:37.980><c> can</c>

00:09:38.120 --> 00:09:38.130 align:start position:0%
to be big events that loop but if it can
 

00:09:38.130 --> 00:09:39.980 align:start position:0%
to be big events that loop but if it can
be<00:09:38.279><c> refactored</c><00:09:38.700><c> such</c><00:09:39.150><c> as</c><00:09:39.270><c> this</c><00:09:39.510><c> into</c>

00:09:39.980 --> 00:09:39.990 align:start position:0%
be refactored such as this into
 

00:09:39.990 --> 00:09:41.810 align:start position:0%
be refactored such as this into
something<00:09:40.200><c> else</c><00:09:40.500><c> on</c><00:09:40.740><c> the</c><00:09:41.130><c> right</c><00:09:41.370><c> silent</c>

00:09:41.810 --> 00:09:41.820 align:start position:0%
something else on the right silent
 

00:09:41.820 --> 00:09:43.190 align:start position:0%
something else on the right silent
operation<00:09:42.029><c> so</c><00:09:42.450><c> high</c><00:09:42.630><c> is</c><00:09:42.690><c> less</c><00:09:42.930><c> than</c><00:09:43.020><c> or</c><00:09:43.110><c> equal</c>

00:09:43.190 --> 00:09:43.200 align:start position:0%
operation so high is less than or equal
 

00:09:43.200 --> 00:09:45.200 align:start position:0%
operation so high is less than or equal
to<00:09:43.529><c> the</c><00:09:43.950><c> square</c><00:09:44.220><c> root</c><00:09:44.250><c> of</c><00:09:44.400><c> n</c><00:09:44.520><c> you're</c><00:09:45.089><c> gonna</c>

00:09:45.200 --> 00:09:45.210 align:start position:0%
to the square root of n you're gonna
 

00:09:45.210 --> 00:09:47.360 align:start position:0%
to the square root of n you're gonna
take<00:09:45.450><c> the</c><00:09:45.570><c> big</c><00:09:45.720><c> o</c><00:09:45.839><c> square</c><00:09:46.200><c> root</c><00:09:46.320><c> of</c><00:09:46.410><c> n</c><00:09:46.529><c> okay</c><00:09:47.130><c> if</c>

00:09:47.360 --> 00:09:47.370 align:start position:0%
take the big o square root of n okay if
 

00:09:47.370 --> 00:09:49.400 align:start position:0%
take the big o square root of n okay if
you<00:09:47.520><c> need</c><00:09:47.700><c> more</c><00:09:47.760><c> clarification</c><00:09:48.089><c> or</c><00:09:48.600><c> you</c><00:09:49.320><c> have</c>

00:09:49.400 --> 00:09:49.410 align:start position:0%
you need more clarification or you have
 

00:09:49.410 --> 00:09:51.320 align:start position:0%
you need more clarification or you have
any<00:09:49.529><c> questions</c><00:09:49.980><c> just</c><00:09:50.279><c> leave</c><00:09:50.820><c> comments</c><00:09:51.300><c> below</c>

00:09:51.320 --> 00:09:51.330 align:start position:0%
any questions just leave comments below
 

00:09:51.330 --> 00:09:53.180 align:start position:0%
any questions just leave comments below
and<00:09:51.810><c> I'll</c><00:09:52.170><c> try</c><00:09:52.320><c> to</c><00:09:52.350><c> answer</c><00:09:52.530><c> them</c><00:09:52.680><c> or</c><00:09:53.040><c> if</c><00:09:53.100><c> you</c>

00:09:53.180 --> 00:09:53.190 align:start position:0%
and I'll try to answer them or if you
 

00:09:53.190 --> 00:09:54.500 align:start position:0%
and I'll try to answer them or if you
have<00:09:53.310><c> any</c><00:09:53.520><c> other</c><00:09:53.790><c> interview</c><00:09:54.150><c> questions</c><00:09:54.480><c> that</c>

00:09:54.500 --> 00:09:54.510 align:start position:0%
have any other interview questions that
 

00:09:54.510 --> 00:09:56.960 align:start position:0%
have any other interview questions that
you've<00:09:54.750><c> had</c><00:09:55.020><c> that</c><00:09:55.800><c> you'd</c><00:09:56.100><c> like</c><00:09:56.250><c> me</c><00:09:56.520><c> to</c><00:09:56.550><c> just</c><00:09:56.820><c> go</c>

00:09:56.960 --> 00:09:56.970 align:start position:0%
you've had that you'd like me to just go
 

00:09:56.970 --> 00:09:59.420 align:start position:0%
you've had that you'd like me to just go
over<00:09:57.320><c> and</c><00:09:58.320><c> then</c><00:09:58.560><c> we</c><00:09:58.770><c> can</c><00:09:58.950><c> kind</c><00:09:59.160><c> of</c><00:09:59.250><c> go</c><00:09:59.370><c> through</c>

00:09:59.420 --> 00:09:59.430 align:start position:0%
over and then we can kind of go through
 

00:09:59.430 --> 00:10:01.940 align:start position:0%
over and then we can kind of go through
it<00:09:59.610><c> together</c><00:09:59.870><c> because</c><00:10:00.870><c> every</c><00:10:01.680><c> question</c>

00:10:01.940 --> 00:10:01.950 align:start position:0%
it together because every question
 

00:10:01.950 --> 00:10:04.310 align:start position:0%
it together because every question
sometimes<00:10:02.820><c> has</c><00:10:03.150><c> more</c><00:10:03.390><c> than</c><00:10:03.540><c> one</c><00:10:03.839><c> answer</c><00:10:04.050><c> this</c>

00:10:04.310 --> 00:10:04.320 align:start position:0%
sometimes has more than one answer this
 

00:10:04.320 --> 00:10:06.800 align:start position:0%
sometimes has more than one answer this
one<00:10:04.530><c> only</c><00:10:05.100><c> has</c><00:10:05.190><c> one</c><00:10:05.310><c> answer</c><00:10:05.700><c> right</c><00:10:06.390><c> but</c><00:10:06.690><c> if</c>

00:10:06.800 --> 00:10:06.810 align:start position:0%
one only has one answer right but if
 

00:10:06.810 --> 00:10:08.810 align:start position:0%
one only has one answer right but if
we're<00:10:06.930><c> talking</c><00:10:06.990><c> about</c><00:10:07.170><c> like</c><00:10:08.130><c> finding</c><00:10:08.580><c> a</c>

00:10:08.810 --> 00:10:08.820 align:start position:0%
we're talking about like finding a
 

00:10:08.820 --> 00:10:12.980 align:start position:0%
we're talking about like finding a
number<00:10:09.360><c> in</c><00:10:09.540><c> array</c><00:10:09.839><c> or</c><00:10:10.230><c> you</c><00:10:11.660><c> know</c><00:10:12.660><c> we</c><00:10:12.780><c> want</c><00:10:12.930><c> to</c>

00:10:12.980 --> 00:10:12.990 align:start position:0%
number in array or you know we want to
 

00:10:12.990 --> 00:10:14.390 align:start position:0%
number in array or you know we want to
term<00:10:13.110><c> a</c><00:10:13.140><c> duplication</c><00:10:13.560><c> array</c><00:10:13.890><c> something</c><00:10:14.280><c> like</c>

00:10:14.390 --> 00:10:14.400 align:start position:0%
term a duplication array something like
 

00:10:14.400 --> 00:10:16.340 align:start position:0%
term a duplication array something like
that<00:10:14.550><c> right</c><00:10:15.150><c> there</c><00:10:15.480><c> are</c><00:10:15.690><c> more</c><00:10:15.960><c> than</c><00:10:16.050><c> one</c><00:10:16.200><c> way</c>

00:10:16.340 --> 00:10:16.350 align:start position:0%
that right there are more than one way
 

00:10:16.350 --> 00:10:17.840 align:start position:0%
that right there are more than one way
to<00:10:16.410><c> do</c><00:10:16.560><c> it</c><00:10:16.710><c> but</c><00:10:17.010><c> typically</c><00:10:17.310><c> there</c><00:10:17.490><c> are</c><00:10:17.520><c> ways</c><00:10:17.820><c> to</c>

00:10:17.840 --> 00:10:17.850 align:start position:0%
to do it but typically there are ways to
 

00:10:17.850 --> 00:10:20.840 align:start position:0%
to do it but typically there are ways to
do<00:10:18.030><c> it</c><00:10:18.150><c> better</c><00:10:18.360><c> than</c><00:10:18.540><c> others</c><00:10:19.310><c> so</c><00:10:20.310><c> just</c><00:10:20.700><c> leave</c>

00:10:20.840 --> 00:10:20.850 align:start position:0%
do it better than others so just leave
 

00:10:20.850 --> 00:10:23.300 align:start position:0%
do it better than others so just leave
them<00:10:21.000><c> down</c><00:10:21.150><c> below</c><00:10:21.180><c> and</c><00:10:21.860><c> I'll</c><00:10:22.860><c> make</c><00:10:22.980><c> a</c><00:10:23.010><c> video</c><00:10:23.250><c> on</c>

00:10:23.300 --> 00:10:23.310 align:start position:0%
them down below and I'll make a video on
 

00:10:23.310 --> 00:10:25.790 align:start position:0%
them down below and I'll make a video on
it<00:10:23.490><c> but</c><00:10:24.240><c> in</c><00:10:24.450><c> the</c><00:10:24.510><c> meantime</c><00:10:24.540><c> I</c><00:10:25.080><c> see</c><00:10:25.620><c> you</c><00:10:25.680><c> in</c><00:10:25.740><c> the</c>

00:10:25.790 --> 00:10:25.800 align:start position:0%
it but in the meantime I see you in the
 

00:10:25.800 --> 00:10:28.160 align:start position:0%
it but in the meantime I see you in the
next<00:10:25.980><c> video</c>

