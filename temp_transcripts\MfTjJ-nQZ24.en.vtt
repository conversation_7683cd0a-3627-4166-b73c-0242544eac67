WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.090 align:start position:0%
 
back<00:00:00.149><c> we're</c><00:00:00.570><c> back</c><00:00:00.870><c> here</c><00:00:01.290><c> in</c><00:00:01.350><c> the</c><00:00:01.530><c> my</c><00:00:01.740><c> sequel</c>

00:00:02.090 --> 00:00:02.100 align:start position:0%
back we're back here in the my sequel
 

00:00:02.100 --> 00:00:04.249 align:start position:0%
back we're back here in the my sequel
node<00:00:02.419><c> project</c><00:00:03.419><c> by</c><00:00:03.570><c> the</c><00:00:03.659><c> way</c><00:00:03.750><c> an</c><00:00:03.870><c> important</c>

00:00:04.249 --> 00:00:04.259 align:start position:0%
node project by the way an important
 

00:00:04.259 --> 00:00:05.720 align:start position:0%
node project by the way an important
element<00:00:04.589><c> for</c><00:00:04.620><c> this</c><00:00:04.830><c> project</c><00:00:05.069><c> is</c><00:00:05.460><c> the</c><00:00:05.580><c> my</c>

00:00:05.720 --> 00:00:05.730 align:start position:0%
element for this project is the my
 

00:00:05.730 --> 00:00:07.849 align:start position:0%
element for this project is the my
sequel<00:00:06.180><c> database</c><00:00:06.660><c> I</c><00:00:06.930><c> have</c><00:00:07.350><c> a</c><00:00:07.379><c> full</c><00:00:07.649><c> tutorial</c>

00:00:07.849 --> 00:00:07.859 align:start position:0%
sequel database I have a full tutorial
 

00:00:07.859 --> 00:00:10.370 align:start position:0%
sequel database I have a full tutorial
series<00:00:08.550><c> which</c><00:00:08.910><c> is</c><00:00:09.059><c> called</c><00:00:09.120><c> mine</c><00:00:09.510><c> which</c><00:00:10.110><c> is</c><00:00:10.139><c> got</c>

00:00:10.370 --> 00:00:10.380 align:start position:0%
series which is called mine which is got
 

00:00:10.380 --> 00:00:12.709 align:start position:0%
series which is called mine which is got
modern-day<00:00:11.010><c> CMS</c><00:00:11.610><c> I'm</c><00:00:11.880><c> gonna</c><00:00:12.030><c> put</c><00:00:12.240><c> a</c><00:00:12.269><c> link</c><00:00:12.540><c> in</c>

00:00:12.709 --> 00:00:12.719 align:start position:0%
modern-day CMS I'm gonna put a link in
 

00:00:12.719 --> 00:00:14.780 align:start position:0%
modern-day CMS I'm gonna put a link in
the<00:00:12.750><c> description</c><00:00:13.009><c> which</c><00:00:14.009><c> has</c><00:00:14.219><c> the</c><00:00:14.490><c> entire</c>

00:00:14.780 --> 00:00:14.790 align:start position:0%
the description which has the entire
 

00:00:14.790 --> 00:00:17.330 align:start position:0%
the description which has the entire
course<00:00:15.330><c> on</c><00:00:15.599><c> this</c><00:00:15.839><c> on</c><00:00:16.470><c> a</c><00:00:16.590><c> specific</c><00:00:17.100><c> project</c>

00:00:17.330 --> 00:00:17.340 align:start position:0%
course on this on a specific project
 

00:00:17.340 --> 00:00:20.000 align:start position:0%
course on this on a specific project
it's<00:00:17.880><c> called</c><00:00:17.940><c> modern-day</c><00:00:18.570><c> CMS</c><00:00:19.109><c> it's</c><00:00:19.380><c> pretty</c>

00:00:20.000 --> 00:00:20.010 align:start position:0%
it's called modern-day CMS it's pretty
 

00:00:20.010 --> 00:00:21.650 align:start position:0%
it's called modern-day CMS it's pretty
much<00:00:20.100><c> an</c><00:00:20.250><c> angular</c><00:00:20.609><c> front-end</c><00:00:21.029><c> in</c><00:00:21.119><c> my</c><00:00:21.300><c> sequel</c>

00:00:21.650 --> 00:00:21.660 align:start position:0%
much an angular front-end in my sequel
 

00:00:21.660 --> 00:00:23.420 align:start position:0%
much an angular front-end in my sequel
backend<00:00:22.109><c> with</c><00:00:22.320><c> node</c><00:00:22.590><c> also</c><00:00:22.980><c> it's</c><00:00:23.100><c> got</c><00:00:23.189><c> a</c><00:00:23.250><c> lot</c><00:00:23.400><c> of</c>

00:00:23.420 --> 00:00:23.430 align:start position:0%
backend with node also it's got a lot of
 

00:00:23.430 --> 00:00:25.250 align:start position:0%
backend with node also it's got a lot of
cool<00:00:23.670><c> stuff</c><00:00:23.910><c> so</c><00:00:24.210><c> what</c><00:00:24.689><c> I'm</c><00:00:24.779><c> gonna</c><00:00:24.869><c> do</c><00:00:25.050><c> in</c><00:00:25.170><c> this</c>

00:00:25.250 --> 00:00:25.260 align:start position:0%
cool stuff so what I'm gonna do in this
 

00:00:25.260 --> 00:00:26.599 align:start position:0%
cool stuff so what I'm gonna do in this
video<00:00:25.470><c> is</c><00:00:25.650><c> I'm</c><00:00:25.830><c> gonna</c><00:00:25.920><c> integrate</c>

00:00:26.599 --> 00:00:26.609 align:start position:0%
video is I'm gonna integrate
 

00:00:26.609 --> 00:00:29.900 align:start position:0%
video is I'm gonna integrate
intercom<00:00:27.289><c> into</c><00:00:28.289><c> this</c><00:00:28.529><c> specific</c><00:00:28.800><c> project</c><00:00:29.429><c> so</c>

00:00:29.900 --> 00:00:29.910 align:start position:0%
intercom into this specific project so
 

00:00:29.910 --> 00:00:31.820 align:start position:0%
intercom into this specific project so
step<00:00:30.630><c> number</c><00:00:30.660><c> one</c><00:00:31.019><c> is</c><00:00:31.140><c> I'm</c><00:00:31.230><c> gonna</c><00:00:31.410><c> start</c><00:00:31.650><c> my</c>

00:00:31.820 --> 00:00:31.830 align:start position:0%
step number one is I'm gonna start my
 

00:00:31.830 --> 00:00:34.040 align:start position:0%
step number one is I'm gonna start my
sequel<00:00:32.279><c> this</c><00:00:32.610><c> is</c><00:00:32.790><c> I</c><00:00:32.940><c> have</c><00:00:33.090><c> samp</c><00:00:33.390><c> installed</c><00:00:33.870><c> on</c>

00:00:34.040 --> 00:00:34.050 align:start position:0%
sequel this is I have samp installed on
 

00:00:34.050 --> 00:00:38.869 align:start position:0%
sequel this is I have samp installed on
Windows<00:00:34.500><c> 10</c><00:00:34.940><c> and</c><00:00:36.079><c> I</c><00:00:37.079><c> am</c><00:00:37.590><c> starting</c><00:00:38.190><c> it</c><00:00:38.340><c> as</c><00:00:38.579><c> long</c>

00:00:38.869 --> 00:00:38.879 align:start position:0%
Windows 10 and I am starting it as long
 

00:00:38.879 --> 00:00:40.520 align:start position:0%
Windows 10 and I am starting it as long
as<00:00:39.030><c> it</c><00:00:39.149><c> doesn't</c><00:00:39.300><c> crash</c><00:00:39.629><c> and</c><00:00:39.930><c> both</c><00:00:40.290><c> of</c><00:00:40.320><c> them</c>

00:00:40.520 --> 00:00:40.530 align:start position:0%
as it doesn't crash and both of them
 

00:00:40.530 --> 00:00:42.350 align:start position:0%
as it doesn't crash and both of them
have<00:00:40.649><c> started</c><00:00:40.770><c> now</c><00:00:41.640><c> I'm</c><00:00:41.790><c> able</c><00:00:41.910><c> to</c><00:00:42.059><c> go</c><00:00:42.300><c> to</c>

00:00:42.350 --> 00:00:42.360 align:start position:0%
have started now I'm able to go to
 

00:00:42.360 --> 00:00:52.240 align:start position:0%
have started now I'm able to go to
localhost<00:00:43.430><c> over</c><00:00:44.430><c> here</c><00:00:45.170><c> localhost</c><00:00:48.050><c> 8080</c><00:00:50.510><c> re</c><00:00:51.600><c> I</c>

00:00:52.240 --> 00:00:52.250 align:start position:0%
localhost over here localhost 8080 re I
 

00:00:52.250 --> 00:00:58.310 align:start position:0%
localhost over here localhost 8080 re I
need<00:00:53.250><c> to</c><00:00:53.370><c> go</c><00:00:53.489><c> to</c><00:00:53.550><c> localhost</c><00:00:54.260><c> /</c><00:00:55.699><c> PHP</c><00:00:56.699><c> my</c><00:00:56.879><c> admin</c><00:00:57.320><c> /</c>

00:00:58.310 --> 00:00:58.320 align:start position:0%
need to go to localhost / PHP my admin /
 

00:00:58.320 --> 00:01:00.369 align:start position:0%
need to go to localhost / PHP my admin /
PHP<00:00:59.070><c> myadmin</c>

00:01:00.369 --> 00:01:00.379 align:start position:0%
PHP myadmin
 

00:01:00.379 --> 00:01:08.240 align:start position:0%
PHP myadmin
okay<00:01:01.410><c> amazing</c><00:01:03.769><c> here</c><00:01:04.769><c> is</c><00:01:05.070><c> my</c><00:01:06.140><c> Edmund</c><00:01:07.189><c> why</c><00:01:08.189><c> is</c>

00:01:08.240 --> 00:01:08.250 align:start position:0%
okay amazing here is my Edmund why is
 

00:01:08.250 --> 00:01:09.920 align:start position:0%
okay amazing here is my Edmund why is
that<00:01:08.369><c> not</c><00:01:08.790><c> working</c><00:01:08.850><c> I</c><00:01:09.390><c> need</c><00:01:09.510><c> to</c><00:01:09.659><c> change</c><00:01:09.869><c> my</c>

00:01:09.920 --> 00:01:09.930 align:start position:0%
that not working I need to change my
 

00:01:09.930 --> 00:01:16.969 align:start position:0%
that not working I need to change my
dotty<00:01:10.320><c> in</c><00:01:10.530><c> a</c><00:01:10.560><c> V</c><00:01:10.770><c> over</c><00:01:11.070><c> here</c><00:01:12.049><c> so</c><00:01:15.710><c> if</c><00:01:16.710><c> you</c><00:01:16.860><c> go</c><00:01:16.950><c> to</c>

00:01:16.969 --> 00:01:16.979 align:start position:0%
dotty in a V over here so if you go to
 

00:01:16.979 --> 00:01:18.440 align:start position:0%
dotty in a V over here so if you go to
Batman<00:01:17.369><c> V</c><00:01:17.640><c> here</c><00:01:17.909><c> and</c><00:01:18.060><c> we're</c><00:01:18.180><c> gonna</c><00:01:18.270><c> go</c><00:01:18.420><c> to</c>

00:01:18.440 --> 00:01:18.450 align:start position:0%
Batman V here and we're gonna go to
 

00:01:18.450 --> 00:01:23.270 align:start position:0%
Batman V here and we're gonna go to
localhost<00:01:18.869><c> no</c><00:01:19.350><c> DB</c><00:01:20.270><c> root</c><00:01:21.270><c> and</c><00:01:21.720><c> so</c><00:01:22.049><c> on</c><00:01:22.200><c> so</c><00:01:22.500><c> on</c><00:01:22.530><c> let</c>

00:01:23.270 --> 00:01:23.280 align:start position:0%
localhost no DB root and so on so on let
 

00:01:23.280 --> 00:01:30.499 align:start position:0%
localhost no DB root and so on so on let
me<00:01:23.630><c> start</c><00:01:24.630><c> this</c><00:01:24.780><c> again</c><00:01:29.150><c> let's</c><00:01:30.150><c> see</c><00:01:30.329><c> if</c><00:01:30.390><c> it</c>

00:01:30.499 --> 00:01:30.509 align:start position:0%
me start this again let's see if it
 

00:01:30.509 --> 00:01:37.429 align:start position:0%
me start this again let's see if it
works<00:01:35.210><c> and</c><00:01:36.210><c> it</c><00:01:36.360><c> turns</c><00:01:36.570><c> out</c><00:01:36.689><c> that</c><00:01:36.750><c> it's</c><00:01:37.020><c> working</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
works and it turns out that it's working
 

00:01:37.439 --> 00:01:41.620 align:start position:0%
works and it turns out that it's working
now<00:01:37.590><c> so</c><00:01:38.369><c> localhost</c><00:01:39.030><c> 8,000</c>

00:01:41.620 --> 00:01:41.630 align:start position:0%
now so localhost 8,000
 

00:01:41.630 --> 00:01:45.319 align:start position:0%
now so localhost 8,000
also<00:01:42.630><c> opening</c><00:01:42.960><c> up</c><00:01:43.079><c> intercom</c><00:01:43.680><c> here</c><00:01:44.149><c> see</c><00:01:45.149><c> this</c>

00:01:45.319 --> 00:01:45.329 align:start position:0%
also opening up intercom here see this
 

00:01:45.329 --> 00:01:48.639 align:start position:0%
also opening up intercom here see this
is<00:01:45.509><c> leash</c><00:01:45.780><c> -</c><00:01:46.079><c> I</c><00:01:46.290><c> want</c><00:01:46.530><c> this</c><00:01:46.740><c> other</c><00:01:46.950><c> one</c><00:01:47.159><c> Alicia</c>

00:01:48.639 --> 00:01:48.649 align:start position:0%
is leash - I want this other one Alicia
 

00:01:48.649 --> 00:02:04.480 align:start position:0%
is leash - I want this other one Alicia
intercom<00:01:50.630><c> which</c><00:01:51.630><c> PR</c><00:01:52.049><c> SB</c><00:01:52.320><c> this</c><00:01:52.799><c> is</c><00:01:52.950><c> me</c>

00:02:04.480 --> 00:02:04.490 align:start position:0%
 
 

00:02:04.490 --> 00:02:08.210 align:start position:0%
 
okay<00:02:05.600><c> let's</c><00:02:06.600><c> see</c><00:02:06.750><c> what</c><00:02:06.930><c> do</c><00:02:06.990><c> we</c><00:02:07.020><c> have</c><00:02:07.110><c> so</c><00:02:07.979><c> far</c>

00:02:08.210 --> 00:02:08.220 align:start position:0%
okay let's see what do we have so far
 

00:02:08.220 --> 00:02:11.390 align:start position:0%
okay let's see what do we have so far
and<00:02:09.470><c> nothing</c><00:02:10.470><c> we</c><00:02:10.590><c> can</c><00:02:10.770><c> do</c><00:02:10.860><c> in</c><00:02:10.979><c> the</c><00:02:11.069><c> meanwhile</c>

00:02:11.390 --> 00:02:11.400 align:start position:0%
and nothing we can do in the meanwhile
 

00:02:11.400 --> 00:02:18.610 align:start position:0%
and nothing we can do in the meanwhile
is<00:02:11.670><c> I'm</c><00:02:11.910><c> gonna</c><00:02:12.090><c> show</c><00:02:12.300><c> you</c><00:02:12.360><c> the</c><00:02:12.600><c> actual</c><00:02:12.870><c> repo</c><00:02:13.470><c> ah</c>

00:02:18.610 --> 00:02:18.620 align:start position:0%
 
 

00:02:18.620 --> 00:02:20.479 align:start position:0%
 
let<00:02:19.620><c> me</c><00:02:19.650><c> show</c><00:02:19.920><c> it</c><00:02:19.950><c> to</c><00:02:20.100><c> you</c><00:02:20.160><c> let</c><00:02:20.280><c> me</c><00:02:20.310><c> actually</c>

00:02:20.479 --> 00:02:20.489 align:start position:0%
let me show it to you let me actually
 

00:02:20.489 --> 00:02:22.670 align:start position:0%
let me show it to you let me actually
open<00:02:20.970><c> it</c><00:02:21.060><c> up</c><00:02:21.180><c> for</c><00:02:21.360><c> you</c><00:02:21.420><c> here</c><00:02:21.630><c> okay</c><00:02:22.170><c> aside</c><00:02:22.410><c> from</c>

00:02:22.670 --> 00:02:22.680 align:start position:0%
open it up for you here okay aside from
 

00:02:22.680 --> 00:02:25.940 align:start position:0%
open it up for you here okay aside from
my<00:02:23.360><c> env</c><00:02:24.360><c> file</c><00:02:24.660><c> probably</c><00:02:25.590><c> shouldn't</c><00:02:25.830><c> be</c>

00:02:25.940 --> 00:02:25.950 align:start position:0%
my env file probably shouldn't be
 

00:02:25.950 --> 00:02:29.210 align:start position:0%
my env file probably shouldn't be
showing<00:02:26.130><c> but</c><00:02:26.489><c> whatever</c><00:02:27.020><c> I'm</c><00:02:28.020><c> gonna</c><00:02:28.170><c> erase</c><00:02:28.860><c> all</c>

00:02:29.210 --> 00:02:29.220 align:start position:0%
showing but whatever I'm gonna erase all
 

00:02:29.220 --> 00:02:31.250 align:start position:0%
showing but whatever I'm gonna erase all
those<00:02:29.370><c> credentials</c><00:02:29.820><c> now</c><00:02:30.239><c> because</c><00:02:30.750><c> you</c><00:02:30.900><c> forced</c>

00:02:31.250 --> 00:02:31.260 align:start position:0%
those credentials now because you forced
 

00:02:31.260 --> 00:02:33.050 align:start position:0%
those credentials now because you forced
me<00:02:31.440><c> I</c><00:02:31.650><c> can't</c><00:02:31.980><c> trust</c><00:02:32.160><c> you</c><00:02:32.370><c> the</c><00:02:32.489><c> Internet</c><00:02:32.880><c> I</c>

00:02:33.050 --> 00:02:33.060 align:start position:0%
me I can't trust you the Internet I
 

00:02:33.060 --> 00:02:35.600 align:start position:0%
me I can't trust you the Internet I
can't<00:02:33.300><c> trust</c><00:02:33.480><c> you</c><00:02:33.630><c> guys</c><00:02:34.070><c> can't</c><00:02:35.070><c> trust</c><00:02:35.250><c> anyone</c>

00:02:35.600 --> 00:02:35.610 align:start position:0%
can't trust you guys can't trust anyone
 

00:02:35.610 --> 00:02:37.810 align:start position:0%
can't trust you guys can't trust anyone
these<00:02:35.640><c> days</c><00:02:36.000><c> so</c><00:02:36.180><c> anyways</c><00:02:36.480><c> I</c><00:02:36.720><c> found</c><00:02:37.050><c> this</c><00:02:37.350><c> other</c>

00:02:37.810 --> 00:02:37.820 align:start position:0%
these days so anyways I found this other
 

00:02:37.820 --> 00:02:40.340 align:start position:0%
these days so anyways I found this other
angularjs<00:02:38.820><c> library</c><00:02:39.600><c> here's</c><00:02:39.930><c> what</c><00:02:40.110><c> the</c><00:02:40.230><c> app</c>

00:02:40.340 --> 00:02:40.350 align:start position:0%
angularjs library here's what the app
 

00:02:40.350 --> 00:02:42.530 align:start position:0%
angularjs library here's what the app
actually<00:02:40.650><c> looks</c><00:02:41.100><c> like</c><00:02:41.310><c> here's</c><00:02:42.269><c> what</c><00:02:42.420><c> its</c>

00:02:42.530 --> 00:02:42.540 align:start position:0%
actually looks like here's what its
 

00:02:42.540 --> 00:02:44.000 align:start position:0%
actually looks like here's what its
gonna<00:02:42.690><c> look</c><00:02:42.810><c> on</c><00:02:43.080><c> intercom</c><00:02:43.470><c> every</c><00:02:43.709><c> time</c><00:02:43.860><c> that</c>

00:02:44.000 --> 00:02:44.010 align:start position:0%
gonna look on intercom every time that
 

00:02:44.010 --> 00:02:46.460 align:start position:0%
gonna look on intercom every time that
someone<00:02:44.280><c> logs</c><00:02:44.610><c> in</c><00:02:44.910><c> we</c><00:02:45.120><c> load</c><00:02:45.300><c> our</c><00:02:45.330><c> core</c><00:02:45.840><c> J's</c>

00:02:46.460 --> 00:02:46.470 align:start position:0%
someone logs in we load our core J's
 

00:02:46.470 --> 00:02:49.430 align:start position:0%
someone logs in we load our core J's
file<00:02:46.709><c> as</c><00:02:46.980><c> we</c><00:02:47.160><c> see</c><00:02:47.370><c> there's</c><00:02:48.060><c> a</c><00:02:48.180><c> dot</c><00:02:48.510><c> run</c><00:02:48.870><c> method</c>

00:02:49.430 --> 00:02:49.440 align:start position:0%
file as we see there's a dot run method
 

00:02:49.440 --> 00:02:52.340 align:start position:0%
file as we see there's a dot run method
on<00:02:49.800><c> the</c><00:02:49.980><c> angular</c><00:02:50.310><c> app</c><00:02:50.670><c> app</c><00:02:51.060><c> object</c><00:02:51.660><c> the</c><00:02:52.050><c> actual</c>

00:02:52.340 --> 00:02:52.350 align:start position:0%
on the angular app app object the actual
 

00:02:52.350 --> 00:02:57.740 align:start position:0%
on the angular app app object the actual
angular<00:02:53.069><c> app</c><00:02:54.769><c> excuse</c><00:02:55.769><c> me</c><00:02:56.420><c> this</c><00:02:57.420><c> is</c><00:02:57.569><c> pretty</c>

00:02:57.740 --> 00:02:57.750 align:start position:0%
angular app excuse me this is pretty
 

00:02:57.750 --> 00:02:59.330 align:start position:0%
angular app excuse me this is pretty
much<00:02:57.840><c> the</c><00:02:58.140><c> syntax</c><00:02:58.560><c> for</c><00:02:58.650><c> as</c><00:02:58.920><c> you</c><00:02:59.040><c> can</c><00:02:59.160><c> see</c><00:02:59.310><c> I</c>

00:02:59.330 --> 00:02:59.340 align:start position:0%
much the syntax for as you can see I
 

00:02:59.340 --> 00:03:01.430 align:start position:0%
much the syntax for as you can see I
wrote<00:02:59.550><c> the</c><00:02:59.850><c> name</c><00:03:00.030><c> you're</c><00:03:00.569><c> passing</c><00:03:00.930><c> an</c><00:03:01.050><c> object</c>

00:03:01.430 --> 00:03:01.440 align:start position:0%
wrote the name you're passing an object
 

00:03:01.440 --> 00:03:03.170 align:start position:0%
wrote the name you're passing an object
that's<00:03:01.620><c> called</c><00:03:01.829><c> new</c><00:03:02.070><c> user</c><00:03:02.400><c> and</c><00:03:02.790><c> then</c><00:03:02.940><c> you</c><00:03:03.030><c> do</c>

00:03:03.170 --> 00:03:03.180 align:start position:0%
that's called new user and then you do
 

00:03:03.180 --> 00:03:05.660 align:start position:0%
that's called new user and then you do
intercom<00:03:03.720><c> boot</c><00:03:04.260><c> new</c><00:03:04.560><c> user</c><00:03:04.860><c> you</c><00:03:05.160><c> pass</c><00:03:05.370><c> in</c><00:03:05.549><c> new</c>

00:03:05.660 --> 00:03:05.670 align:start position:0%
intercom boot new user you pass in new
 

00:03:05.670 --> 00:03:07.400 align:start position:0%
intercom boot new user you pass in new
user<00:03:05.820><c> as</c><00:03:06.030><c> a</c><00:03:06.060><c> parameter</c><00:03:06.329><c> here's</c><00:03:07.140><c> where</c><00:03:07.320><c> you</c>

00:03:07.400 --> 00:03:07.410 align:start position:0%
user as a parameter here's where you
 

00:03:07.410 --> 00:03:09.259 align:start position:0%
user as a parameter here's where you
create<00:03:07.739><c> the</c><00:03:08.010><c> key</c><00:03:08.489><c> value</c><00:03:08.850><c> pairs</c><00:03:09.090><c> for</c><00:03:09.239><c> that</c>

00:03:09.259 --> 00:03:09.269 align:start position:0%
create the key value pairs for that
 

00:03:09.269 --> 00:03:10.910 align:start position:0%
create the key value pairs for that
specific<00:03:09.780><c> object</c><00:03:09.900><c> and</c><00:03:10.320><c> of</c><00:03:10.440><c> course</c><00:03:10.680><c> you</c><00:03:10.769><c> can</c>

00:03:10.910 --> 00:03:10.920 align:start position:0%
specific object and of course you can
 

00:03:10.920 --> 00:03:12.800 align:start position:0%
specific object and of course you can
put<00:03:11.010><c> an</c><00:03:11.160><c> HTTP</c><00:03:11.790><c> over</c><00:03:12.030><c> there</c><00:03:12.209><c> we</c><00:03:12.390><c> need</c><00:03:12.540><c> to</c><00:03:12.660><c> work</c>

00:03:12.800 --> 00:03:12.810 align:start position:0%
put an HTTP over there we need to work
 

00:03:12.810 --> 00:03:14.750 align:start position:0%
put an HTTP over there we need to work
on<00:03:13.049><c> that</c><00:03:13.230><c> so</c><00:03:13.829><c> we're</c><00:03:13.950><c> gonna</c><00:03:14.070><c> practice</c><00:03:14.340><c> that</c>

00:03:14.750 --> 00:03:14.760 align:start position:0%
on that so we're gonna practice that
 

00:03:14.760 --> 00:03:17.140 align:start position:0%
on that so we're gonna practice that
very<00:03:15.030><c> shortly</c><00:03:15.420><c> but</c><00:03:15.720><c> this</c><00:03:15.870><c> is</c><00:03:15.989><c> mainly</c><00:03:16.200><c> and</c><00:03:16.590><c> a</c>

00:03:17.140 --> 00:03:17.150 align:start position:0%
very shortly but this is mainly and a
 

00:03:17.150 --> 00:03:22.610 align:start position:0%
very shortly but this is mainly and a
tutorial<00:03:18.150><c> on</c><00:03:18.600><c> intercom</c><00:03:19.079><c> anyways</c><00:03:19.940><c> but</c><00:03:20.940><c> I</c><00:03:21.620><c> found</c>

00:03:22.610 --> 00:03:22.620 align:start position:0%
tutorial on intercom anyways but I found
 

00:03:22.620 --> 00:03:25.000 align:start position:0%
tutorial on intercom anyways but I found
that<00:03:22.769><c> the</c><00:03:22.890><c> name</c><00:03:23.040><c> of</c><00:03:23.160><c> the</c><00:03:23.220><c> project</c><00:03:23.730><c> was</c><00:03:23.940><c> angular</c>

00:03:25.000 --> 00:03:25.010 align:start position:0%
that the name of the project was angular
 

00:03:25.010 --> 00:03:30.620 align:start position:0%
that the name of the project was angular
Jas<00:03:26.239><c> intercom</c><00:03:28.370><c> github</c><00:03:29.370><c> yeah</c><00:03:29.850><c> I</c><00:03:30.090><c> did</c><00:03:30.329><c> a</c><00:03:30.359><c> search</c>

00:03:30.620 --> 00:03:30.630 align:start position:0%
Jas intercom github yeah I did a search
 

00:03:30.630 --> 00:03:33.199 align:start position:0%
Jas intercom github yeah I did a search
like<00:03:30.870><c> that</c><00:03:30.900><c> and</c><00:03:31.380><c> it</c><00:03:31.950><c> was</c><00:03:32.010><c> relatively</c><00:03:32.489><c> simple</c><00:03:33.090><c> I</c>

00:03:33.199 --> 00:03:33.209 align:start position:0%
like that and it was relatively simple I
 

00:03:33.209 --> 00:03:36.020 align:start position:0%
like that and it was relatively simple I
just<00:03:33.390><c> wanted</c><00:03:33.570><c> to</c><00:03:33.720><c> record</c><00:03:33.989><c> a</c><00:03:34.200><c> s</c><00:03:34.560><c> and</c><00:03:34.890><c> so</c><00:03:35.700><c> on</c><00:03:35.730><c> and</c>

00:03:36.020 --> 00:03:36.030 align:start position:0%
just wanted to record a s and so on and
 

00:03:36.030 --> 00:03:37.849 align:start position:0%
just wanted to record a s and so on and
that's<00:03:36.150><c> how</c><00:03:36.209><c> I</c><00:03:36.299><c> loaded</c><00:03:36.630><c> it</c><00:03:36.870><c> up</c><00:03:36.989><c> okay</c><00:03:37.560><c> that's</c>

00:03:37.849 --> 00:03:37.859 align:start position:0%
that's how I loaded it up okay that's
 

00:03:37.859 --> 00:03:40.310 align:start position:0%
that's how I loaded it up okay that's
how<00:03:37.950><c> we</c><00:03:38.100><c> integrated</c><00:03:38.780><c> intercom</c><00:03:39.780><c> and</c><00:03:40.079><c> I'm</c><00:03:40.140><c> sure</c>

00:03:40.310 --> 00:03:40.320 align:start position:0%
how we integrated intercom and I'm sure
 

00:03:40.320 --> 00:03:41.900 align:start position:0%
how we integrated intercom and I'm sure
you<00:03:40.680><c> might</c><00:03:40.829><c> have</c><00:03:40.980><c> to</c><00:03:41.100><c> do</c><00:03:41.220><c> some</c><00:03:41.489><c> research</c><00:03:41.760><c> on</c>

00:03:41.900 --> 00:03:41.910 align:start position:0%
you might have to do some research on
 

00:03:41.910 --> 00:03:42.979 align:start position:0%
you might have to do some research on
your<00:03:41.940><c> own</c><00:03:42.060><c> I</c><00:03:42.299><c> don't</c><00:03:42.359><c> know</c><00:03:42.510><c> what</c><00:03:42.660><c> you</c><00:03:42.720><c> have</c><00:03:42.840><c> on</c>

00:03:42.979 --> 00:03:42.989 align:start position:0%
your own I don't know what you have on
 

00:03:42.989 --> 00:03:44.270 align:start position:0%
your own I don't know what you have on
your<00:03:43.019><c> front-end</c><00:03:43.440><c> maybe</c><00:03:43.680><c> you</c><00:03:43.769><c> have</c><00:03:43.890><c> to</c><00:03:44.010><c> react</c>

00:03:44.270 --> 00:03:44.280 align:start position:0%
your front-end maybe you have to react
 

00:03:44.280 --> 00:03:45.530 align:start position:0%
your front-end maybe you have to react
maybe<00:03:44.579><c> you</c><00:03:44.700><c> have</c><00:03:44.790><c> something</c><00:03:45.030><c> else</c><00:03:45.239><c> but</c>

00:03:45.530 --> 00:03:45.540 align:start position:0%
maybe you have something else but
 

00:03:45.540 --> 00:03:47.870 align:start position:0%
maybe you have something else but
Stewart<00:03:46.410><c> just</c><00:03:46.829><c> do</c><00:03:47.040><c> a</c><00:03:47.070><c> little</c><00:03:47.280><c> bit</c><00:03:47.430><c> of</c><00:03:47.519><c> research</c>

00:03:47.870 --> 00:03:47.880 align:start position:0%
Stewart just do a little bit of research
 

00:03:47.880 --> 00:03:49.400 align:start position:0%
Stewart just do a little bit of research
for<00:03:48.090><c> whether</c><00:03:48.299><c> there's</c><00:03:48.510><c> any</c><00:03:48.690><c> modules</c><00:03:49.200><c> that</c>

00:03:49.400 --> 00:03:49.410 align:start position:0%
for whether there's any modules that
 

00:03:49.410 --> 00:03:50.660 align:start position:0%
for whether there's any modules that
will<00:03:49.530><c> help</c><00:03:49.650><c> you</c><00:03:49.829><c> out</c><00:03:50.040><c> here</c><00:03:50.310><c> and</c><00:03:50.459><c> here's</c>

00:03:50.660 --> 00:03:50.670 align:start position:0%
will help you out here and here's
 

00:03:50.670 --> 00:03:52.940 align:start position:0%
will help you out here and here's
actually<00:03:50.880><c> the</c><00:03:51.150><c> name</c><00:03:51.390><c> of</c><00:03:51.510><c> it</c><00:03:51.660><c> I</c><00:03:51.840><c> can</c><00:03:52.650><c> put</c><00:03:52.680><c> this</c>

00:03:52.940 --> 00:03:52.950 align:start position:0%
actually the name of it I can put this
 

00:03:52.950 --> 00:03:55.449 align:start position:0%
actually the name of it I can put this
in<00:03:53.160><c> the</c><00:03:53.359><c> description</c><00:03:54.359><c> as</c><00:03:54.480><c> well</c><00:03:54.660><c> both</c><00:03:54.900><c> of</c><00:03:55.109><c> them</c>

00:03:55.449 --> 00:03:55.459 align:start position:0%
in the description as well both of them
 

00:03:55.459 --> 00:03:58.580 align:start position:0%
in the description as well both of them
what<00:03:56.459><c> the</c><00:03:56.609><c> github</c><00:03:56.820><c> repo</c><00:03:57.090><c> and</c><00:03:57.480><c> this</c><00:03:58.079><c> module</c>

00:03:58.580 --> 00:03:58.590 align:start position:0%
what the github repo and this module
 

00:03:58.590 --> 00:04:00.710 align:start position:0%
what the github repo and this module
that<00:03:58.739><c> was</c><00:03:58.890><c> integrated</c><00:03:59.430><c> into</c><00:03:59.700><c> it</c><00:03:59.820><c> as</c><00:04:00.150><c> you</c><00:04:00.570><c> can</c>

00:04:00.710 --> 00:04:00.720 align:start position:0%
that was integrated into it as you can
 

00:04:00.720 --> 00:04:04.640 align:start position:0%
that was integrated into it as you can
see<00:04:00.870><c> over</c><00:04:01.290><c> here</c><00:04:01.519><c> okay</c><00:04:02.540><c> we</c><00:04:03.540><c> have</c><00:04:03.750><c> the</c><00:04:04.140><c> user</c><00:04:04.380><c> ID</c>

00:04:04.640 --> 00:04:04.650 align:start position:0%
see over here okay we have the user ID
 

00:04:04.650 --> 00:04:07.399 align:start position:0%
see over here okay we have the user ID
was<00:04:05.250><c> a</c><00:04:05.280><c> random</c><00:04:05.730><c> one</c><00:04:05.970><c> intercom</c><00:04:06.540><c> app</c><00:04:06.780><c> ID</c><00:04:07.019><c> I</c>

00:04:07.399 --> 00:04:07.409 align:start position:0%
was a random one intercom app ID I
 

00:04:07.409 --> 00:04:09.350 align:start position:0%
was a random one intercom app ID I
described<00:04:08.069><c> it</c><00:04:08.220><c> over</c><00:04:08.430><c> here</c><00:04:08.670><c> this</c><00:04:08.879><c> is</c><00:04:09.090><c> the</c><00:04:09.239><c> app</c>

00:04:09.350 --> 00:04:09.360 align:start position:0%
described it over here this is the app
 

00:04:09.360 --> 00:04:09.800 align:start position:0%
described it over here this is the app
ID

00:04:09.800 --> 00:04:09.810 align:start position:0%
ID
 

00:04:09.810 --> 00:04:12.949 align:start position:0%
ID
again<00:04:10.709><c> you</c><00:04:11.310><c> may</c><00:04:11.550><c> want</c><00:04:11.819><c> to</c><00:04:11.940><c> hack</c><00:04:12.120><c> this</c><00:04:12.390><c> site</c><00:04:12.690><c> but</c>

00:04:12.949 --> 00:04:12.959 align:start position:0%
again you may want to hack this site but
 

00:04:12.959 --> 00:04:14.720 align:start position:0%
again you may want to hack this site but
I<00:04:13.020><c> might</c><00:04:13.319><c> try</c><00:04:13.470><c> to</c><00:04:13.590><c> add</c><00:04:13.890><c> some</c><00:04:14.160><c> more</c><00:04:14.310><c> protection</c>

00:04:14.720 --> 00:04:14.730 align:start position:0%
I might try to add some more protection
 

00:04:14.730 --> 00:04:17.810 align:start position:0%
I might try to add some more protection
if<00:04:14.819><c> I</c><00:04:14.970><c> can</c><00:04:15.209><c> but</c><00:04:16.280><c> intercom</c><00:04:17.280><c> is</c><00:04:17.400><c> really</c><00:04:17.700><c> awesome</c>

00:04:17.810 --> 00:04:17.820 align:start position:0%
if I can but intercom is really awesome
 

00:04:17.820 --> 00:04:19.670 align:start position:0%
if I can but intercom is really awesome
the<00:04:18.239><c> moment</c><00:04:18.540><c> that</c><00:04:18.660><c> everything</c><00:04:18.870><c> is</c><00:04:19.109><c> loaded</c><00:04:19.500><c> we</c>

00:04:19.670 --> 00:04:19.680 align:start position:0%
the moment that everything is loaded we
 

00:04:19.680 --> 00:04:20.479 align:start position:0%
the moment that everything is loaded we
go<00:04:19.829><c> in</c><00:04:20.010><c> here</c>

00:04:20.479 --> 00:04:20.489 align:start position:0%
go in here
 

00:04:20.489 --> 00:04:22.220 align:start position:0%
go in here
after<00:04:20.940><c> on</c><00:04:21.030><c> intercom</c><00:04:21.450><c> poke</c><00:04:21.720><c> sign-in</c><00:04:22.200><c> with</c>

00:04:22.220 --> 00:04:22.230 align:start position:0%
after on intercom poke sign-in with
 

00:04:22.230 --> 00:04:26.740 align:start position:0%
after on intercom poke sign-in with
Google<00:04:22.410><c> oh</c><00:04:23.780><c> crap</c>

00:04:26.740 --> 00:04:26.750 align:start position:0%
Google oh crap
 

00:04:26.750 --> 00:04:29.659 align:start position:0%
Google oh crap
I've<00:04:27.750><c> only</c><00:04:27.900><c> got</c><00:04:28.110><c> five</c><00:04:28.139><c> five</c><00:04:28.950><c> minutes</c><00:04:29.340><c> on</c><00:04:29.520><c> this</c>

00:04:29.659 --> 00:04:29.669 align:start position:0%
I've only got five five minutes on this
 

00:04:29.669 --> 00:04:34.700 align:start position:0%
I've only got five five minutes on this
intercom<00:04:30.240><c> software</c><00:04:30.950><c> its</c><00:04:31.950><c> maximum</c><00:04:32.669><c> so</c><00:04:33.710><c> this</c>

00:04:34.700 --> 00:04:34.710 align:start position:0%
intercom software its maximum so this
 

00:04:34.710 --> 00:04:38.000 align:start position:0%
intercom software its maximum so this
doesn't<00:04:35.040><c> work</c><00:04:35.250><c> come</c><00:04:35.790><c> on</c><00:04:35.970><c> man</c><00:04:36.240><c> got</c><00:04:37.050><c> 25</c><00:04:37.500><c> seconds</c>

00:04:38.000 --> 00:04:38.010 align:start position:0%
doesn't work come on man got 25 seconds
 

00:04:38.010 --> 00:04:39.950 align:start position:0%
doesn't work come on man got 25 seconds
for<00:04:38.160><c> you</c><00:04:38.220><c> to</c><00:04:38.340><c> show</c><00:04:38.520><c> you</c><00:04:38.690><c> show</c><00:04:39.690><c> you</c><00:04:39.720><c> what</c>

00:04:39.950 --> 00:04:39.960 align:start position:0%
for you to show you show you what
 

00:04:39.960 --> 00:04:43.969 align:start position:0%
for you to show you show you what
happens<00:04:40.350><c> in</c><00:04:40.500><c> the</c><00:04:40.590><c> users</c><00:04:40.950><c> column</c><00:04:41.400><c> ah</c><00:04:42.830><c> crap</c><00:04:43.830><c> I</c>

00:04:43.969 --> 00:04:43.979 align:start position:0%
happens in the users column ah crap I
 

00:04:43.979 --> 00:04:46.040 align:start position:0%
happens in the users column ah crap I
may<00:04:44.070><c> have</c><00:04:44.190><c> to</c><00:04:44.340><c> just</c><00:04:44.550><c> make</c><00:04:44.790><c> a</c><00:04:44.850><c> new</c><00:04:45.120><c> video</c><00:04:45.360><c> new</c>

00:04:46.040 --> 00:04:46.050 align:start position:0%
may have to just make a new video new
 

00:04:46.050 --> 00:04:49.850 align:start position:0%
may have to just make a new video new
part<00:04:46.350><c> to</c><00:04:46.470><c> this</c><00:04:46.820><c> sorry</c><00:04:47.820><c> guys</c><00:04:48.150><c> I</c><00:04:48.540><c> apologize</c><00:04:48.960><c> well</c>

00:04:49.850 --> 00:04:49.860 align:start position:0%
part to this sorry guys I apologize well
 

00:04:49.860 --> 00:04:54.520 align:start position:0%
part to this sorry guys I apologize well
I'll<00:04:50.070><c> see</c><00:04:50.250><c> you</c><00:04:50.310><c> very</c><00:04:50.490><c> shortly</c><00:04:50.639><c> ah</c>

00:04:54.520 --> 00:04:54.530 align:start position:0%
 
 

00:04:54.530 --> 00:05:02.000 align:start position:0%
 
Keanu<00:04:55.940><c> wait</c><00:04:57.680><c> and</c><00:04:58.680><c> there</c><00:04:58.979><c> we</c><00:04:59.100><c> go</c><00:04:59.250><c> guys</c><00:04:59.460><c> you</c><00:04:59.520><c> too</c>

