WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.850 align:start position:0%
 
hey<00:00:00.299><c> and</c><00:00:00.630><c> welcome</c><00:00:00.900><c> back</c><00:00:00.930><c> to</c><00:00:01.170><c> another</c><00:00:01.199><c> HTML</c>

00:00:01.850 --> 00:00:01.860 align:start position:0%
hey and welcome back to another HTML
 

00:00:01.860 --> 00:00:03.949 align:start position:0%
hey and welcome back to another HTML
video<00:00:02.120><c> today</c><00:00:03.120><c> we're</c><00:00:03.330><c> gonna</c><00:00:03.480><c> be</c><00:00:03.600><c> going</c><00:00:03.720><c> over</c>

00:00:03.949 --> 00:00:03.959 align:start position:0%
video today we're gonna be going over
 

00:00:03.959 --> 00:00:05.930 align:start position:0%
video today we're gonna be going over
links<00:00:04.470><c> and</c><00:00:04.740><c> these</c><00:00:05.220><c> are</c><00:00:05.339><c> pretty</c><00:00:05.490><c> important</c>

00:00:05.930 --> 00:00:05.940 align:start position:0%
links and these are pretty important
 

00:00:05.940 --> 00:00:10.790 align:start position:0%
links and these are pretty important
because<00:00:06.810><c> these</c><00:00:07.560><c> are</c><00:00:07.620><c> the</c><00:00:08.519><c> foundation</c><00:00:09.420><c> of</c><00:00:09.800><c> web</c>

00:00:10.790 --> 00:00:10.800 align:start position:0%
because these are the foundation of web
 

00:00:10.800 --> 00:00:12.980 align:start position:0%
because these are the foundation of web
browsing<00:00:11.070><c> or</c><00:00:11.490><c> web</c><00:00:11.670><c> surfing</c><00:00:11.940><c> there</c><00:00:12.599><c> is</c><00:00:12.750><c> what</c>

00:00:12.980 --> 00:00:12.990 align:start position:0%
browsing or web surfing there is what
 

00:00:12.990 --> 00:00:15.079 align:start position:0%
browsing or web surfing there is what
they<00:00:13.139><c> were</c><00:00:13.380><c> allowing</c><00:00:13.650><c> you</c><00:00:13.830><c> to</c><00:00:14.070><c> click</c><00:00:14.639><c> on</c><00:00:14.820><c> a</c>

00:00:15.079 --> 00:00:15.089 align:start position:0%
they were allowing you to click on a
 

00:00:15.089 --> 00:00:17.930 align:start position:0%
they were allowing you to click on a
button<00:00:15.120><c> or</c><00:00:16.220><c> specific</c><00:00:17.220><c> text</c><00:00:17.580><c> and</c><00:00:17.730><c> then</c><00:00:17.850><c> when</c>

00:00:17.930 --> 00:00:17.940 align:start position:0%
button or specific text and then when
 

00:00:17.940 --> 00:00:19.580 align:start position:0%
button or specific text and then when
you<00:00:18.060><c> click</c><00:00:18.240><c> on</c><00:00:18.390><c> there</c><00:00:18.570><c> it</c><00:00:19.170><c> takes</c><00:00:19.380><c> you</c><00:00:19.470><c> to</c>

00:00:19.580 --> 00:00:19.590 align:start position:0%
you click on there it takes you to
 

00:00:19.590 --> 00:00:22.160 align:start position:0%
you click on there it takes you to
another<00:00:19.619><c> web</c><00:00:19.920><c> page</c><00:00:19.949><c> and</c><00:00:20.460><c> this</c><00:00:21.390><c> is</c><00:00:21.449><c> achieved</c><00:00:21.660><c> by</c>

00:00:22.160 --> 00:00:22.170 align:start position:0%
another web page and this is achieved by
 

00:00:22.170 --> 00:00:25.960 align:start position:0%
another web page and this is achieved by
using<00:00:22.470><c> a</c><00:00:22.800><c> new</c><00:00:23.070><c> HTML</c><00:00:23.850><c> element</c><00:00:24.449><c> called</c><00:00:25.050><c> an</c><00:00:25.199><c> a</c><00:00:25.380><c> tag</c>

00:00:25.960 --> 00:00:25.970 align:start position:0%
using a new HTML element called an a tag
 

00:00:25.970 --> 00:00:28.429 align:start position:0%
using a new HTML element called an a tag
so<00:00:26.970><c> whenever</c><00:00:27.180><c> whenever</c><00:00:27.720><c> you</c><00:00:27.930><c> have</c><00:00:28.019><c> an</c><00:00:28.109><c> opening</c>

00:00:28.429 --> 00:00:28.439 align:start position:0%
so whenever whenever you have an opening
 

00:00:28.439 --> 00:00:31.460 align:start position:0%
so whenever whenever you have an opening
closing<00:00:28.740><c> a</c><00:00:28.949><c> tag</c><00:00:29.189><c> any</c><00:00:30.090><c> text</c><00:00:30.539><c> that's</c><00:00:30.840><c> put</c><00:00:31.199><c> in</c><00:00:31.349><c> the</c>

00:00:31.460 --> 00:00:31.470 align:start position:0%
closing a tag any text that's put in the
 

00:00:31.470 --> 00:00:32.930 align:start position:0%
closing a tag any text that's put in the
middle<00:00:31.769><c> is</c><00:00:32.130><c> what</c><00:00:32.340><c> you'll</c><00:00:32.520><c> see</c><00:00:32.669><c> on</c><00:00:32.759><c> the</c><00:00:32.850><c> screen</c>

00:00:32.930 --> 00:00:32.940 align:start position:0%
middle is what you'll see on the screen
 

00:00:32.940 --> 00:00:35.180 align:start position:0%
middle is what you'll see on the screen
and<00:00:33.390><c> one</c><00:00:33.480><c> of</c><00:00:33.570><c> you</c><00:00:33.660><c> click</c><00:00:33.989><c> that</c><00:00:34.200><c> that's</c><00:00:34.860><c> what</c>

00:00:35.180 --> 00:00:35.190 align:start position:0%
and one of you click that that's what
 

00:00:35.190 --> 00:00:37.790 align:start position:0%
and one of you click that that's what
directs<00:00:35.550><c> you</c><00:00:35.730><c> over</c><00:00:36.329><c> to</c><00:00:36.510><c> another</c><00:00:36.930><c> web</c><00:00:37.200><c> page</c><00:00:37.230><c> and</c>

00:00:37.790 --> 00:00:37.800 align:start position:0%
directs you over to another web page and
 

00:00:37.800 --> 00:00:40.940 align:start position:0%
directs you over to another web page and
we're<00:00:38.610><c> also</c><00:00:39.149><c> going</c><00:00:39.360><c> to</c><00:00:39.420><c> be</c><00:00:39.510><c> introduced</c><00:00:39.930><c> to</c><00:00:40.200><c> a</c>

00:00:40.940 --> 00:00:40.950 align:start position:0%
we're also going to be introduced to a
 

00:00:40.950 --> 00:00:44.180 align:start position:0%
we're also going to be introduced to a
new<00:00:41.700><c> new</c><00:00:42.390><c> terminology</c><00:00:42.930><c> and</c><00:00:43.110><c> that's</c><00:00:43.739><c> called</c><00:00:43.980><c> an</c>

00:00:44.180 --> 00:00:44.190 align:start position:0%
new new terminology and that's called an
 

00:00:44.190 --> 00:00:46.970 align:start position:0%
new new terminology and that's called an
HTML<00:00:45.030><c> attribute</c><00:00:45.600><c> and</c><00:00:45.840><c> in</c><00:00:46.379><c> this</c><00:00:46.530><c> case</c><00:00:46.770><c> our</c>

00:00:46.970 --> 00:00:46.980 align:start position:0%
HTML attribute and in this case our
 

00:00:46.980 --> 00:00:48.889 align:start position:0%
HTML attribute and in this case our
first<00:00:47.219><c> one</c><00:00:47.520><c> is</c><00:00:47.789><c> going</c><00:00:48.000><c> to</c><00:00:48.059><c> be</c><00:00:48.270><c> called</c><00:00:48.510><c> href</c>

00:00:48.889 --> 00:00:48.899 align:start position:0%
first one is going to be called href
 

00:00:48.899 --> 00:00:51.490 align:start position:0%
first one is going to be called href
which<00:00:49.200><c> stands</c><00:00:49.800><c> for</c><00:00:49.860><c> hypertext</c><00:00:50.280><c> reference</c><00:00:50.610><c> and</c>

00:00:51.490 --> 00:00:51.500 align:start position:0%
which stands for hypertext reference and
 

00:00:51.500 --> 00:00:53.779 align:start position:0%
which stands for hypertext reference and
that's<00:00:52.500><c> where</c><00:00:52.860><c> you</c><00:00:53.070><c> have</c><00:00:53.280><c> to</c><00:00:53.309><c> actually</c>

00:00:53.779 --> 00:00:53.789 align:start position:0%
that's where you have to actually
 

00:00:53.789 --> 00:00:57.650 align:start position:0%
that's where you have to actually
specify<00:00:54.750><c> the</c><00:00:55.440><c> webpage</c><00:00:55.980><c> you</c><00:00:56.789><c> want</c><00:00:57.000><c> the</c><00:00:57.329><c> user</c><00:00:57.539><c> to</c>

00:00:57.650 --> 00:00:57.660 align:start position:0%
specify the webpage you want the user to
 

00:00:57.660 --> 00:00:59.810 align:start position:0%
specify the webpage you want the user to
be<00:00:57.809><c> directed</c><00:00:58.289><c> to</c><00:00:58.410><c> okay</c><00:00:59.010><c> so</c><00:00:59.070><c> here</c><00:00:59.550><c> we</c><00:00:59.579><c> have</c><00:00:59.640><c> an</c>

00:00:59.810 --> 00:00:59.820 align:start position:0%
be directed to okay so here we have an
 

00:00:59.820 --> 00:01:02.900 align:start position:0%
be directed to okay so here we have an
example<00:01:00.059><c> a</c><00:01:00.480><c> tagged</c><00:01:00.780><c> as</c><00:01:01.559><c> you</c><00:01:01.710><c> can</c><00:01:01.829><c> see</c><00:01:02.039><c> we</c><00:01:02.699><c> have</c>

00:01:02.900 --> 00:01:02.910 align:start position:0%
example a tagged as you can see we have
 

00:01:02.910 --> 00:01:07.490 align:start position:0%
example a tagged as you can see we have
an<00:01:03.270><c> opening</c><00:01:03.870><c> and</c><00:01:03.989><c> closing</c><00:01:04.350><c> a</c><00:01:04.470><c> tag</c><00:01:05.330><c> we</c><00:01:06.330><c> have</c><00:01:06.500><c> MSN</c>

00:01:07.490 --> 00:01:07.500 align:start position:0%
an opening and closing a tag we have MSN
 

00:01:07.500 --> 00:01:10.280 align:start position:0%
an opening and closing a tag we have MSN
which<00:01:07.890><c> is</c><00:01:08.100><c> the</c><00:01:08.750><c> which</c><00:01:09.750><c> is</c><00:01:09.869><c> the</c><00:01:09.900><c> text</c><00:01:10.200><c> that</c>

00:01:10.280 --> 00:01:10.290 align:start position:0%
which is the which is the text that
 

00:01:10.290 --> 00:01:12.320 align:start position:0%
which is the which is the text that
you'll<00:01:10.470><c> see</c><00:01:10.650><c> on</c><00:01:10.770><c> the</c><00:01:10.860><c> screen</c><00:01:11.159><c> and</c><00:01:11.400><c> we</c><00:01:12.119><c> have</c>

00:01:12.320 --> 00:01:12.330 align:start position:0%
you'll see on the screen and we have
 

00:01:12.330 --> 00:01:15.260 align:start position:0%
you'll see on the screen and we have
what's<00:01:12.600><c> in</c><00:01:12.750><c> red</c><00:01:12.780><c> that's</c><00:01:14.000><c> that's</c><00:01:15.000><c> what</c><00:01:15.150><c> we're</c>

00:01:15.260 --> 00:01:15.270 align:start position:0%
what's in red that's that's what we're
 

00:01:15.270 --> 00:01:18.920 align:start position:0%
what's in red that's that's what we're
assigning<00:01:15.600><c> to</c><00:01:16.380><c> the</c><00:01:16.530><c> href</c><00:01:17.009><c> attribute</c><00:01:17.270><c> and</c><00:01:18.270><c> so</c>

00:01:18.920 --> 00:01:18.930 align:start position:0%
assigning to the href attribute and so
 

00:01:18.930 --> 00:01:21.560 align:start position:0%
assigning to the href attribute and so
whenever<00:01:19.140><c> we</c><00:01:19.439><c> click</c><00:01:19.770><c> on</c><00:01:19.979><c> the</c><00:01:20.400><c> text</c><00:01:20.610><c> MSN</c><00:01:21.360><c> it's</c>

00:01:21.560 --> 00:01:21.570 align:start position:0%
whenever we click on the text MSN it's
 

00:01:21.570 --> 00:01:25.340 align:start position:0%
whenever we click on the text MSN it's
in<00:01:21.689><c> white</c><00:01:21.900><c> it's</c><00:01:22.590><c> gonna</c><00:01:22.920><c> direct</c><00:01:23.310><c> us</c><00:01:23.930><c> to</c><00:01:24.930><c> msn.com</c>

00:01:25.340 --> 00:01:25.350 align:start position:0%
in white it's gonna direct us to msn.com
 

00:01:25.350 --> 00:01:27.890 align:start position:0%
in white it's gonna direct us to msn.com
because<00:01:26.220><c> we're</c><00:01:26.430><c> specifying</c><00:01:26.670><c> and</c><00:01:27.390><c> the</c>

00:01:27.890 --> 00:01:27.900 align:start position:0%
because we're specifying and the
 

00:01:27.900 --> 00:01:31.039 align:start position:0%
because we're specifying and the
attribute<00:01:28.500><c> that's</c><00:01:29.130><c> the</c><00:01:30.000><c> link</c><00:01:30.270><c> we</c><00:01:30.630><c> wanted</c><00:01:30.960><c> to</c>

00:01:31.039 --> 00:01:31.049 align:start position:0%
attribute that's the link we wanted to
 

00:01:31.049 --> 00:01:33.920 align:start position:0%
attribute that's the link we wanted to
take<00:01:31.229><c> that's</c><00:01:31.890><c> the</c><00:01:32.009><c> link</c><00:01:32.189><c> we</c><00:01:32.280><c> wanted</c><00:01:32.689><c> to</c><00:01:33.689><c> direct</c>

00:01:33.920 --> 00:01:33.930 align:start position:0%
take that's the link we wanted to direct
 

00:01:33.930 --> 00:01:37.670 align:start position:0%
take that's the link we wanted to direct
us<00:01:34.170><c> to</c><00:01:34.430><c> so</c><00:01:35.430><c> again</c><00:01:36.210><c> we</c><00:01:36.869><c> always</c><00:01:37.020><c> have</c><00:01:37.229><c> an</c><00:01:37.320><c> opening</c>

00:01:37.670 --> 00:01:37.680 align:start position:0%
us to so again we always have an opening
 

00:01:37.680 --> 00:01:42.289 align:start position:0%
us to so again we always have an opening
and<00:01:37.970><c> closing</c><00:01:38.970><c> tag</c><00:01:40.909><c> but</c><00:01:41.909><c> this</c><00:01:42.060><c> time</c><00:01:42.210><c> is</c>

00:01:42.289 --> 00:01:42.299 align:start position:0%
and closing tag but this time is
 

00:01:42.299 --> 00:01:43.760 align:start position:0%
and closing tag but this time is
different<00:01:42.329><c> because</c><00:01:42.840><c> we</c><00:01:43.140><c> have</c><00:01:43.259><c> an</c><00:01:43.350><c> href</c>

00:01:43.760 --> 00:01:43.770 align:start position:0%
different because we have an href
 

00:01:43.770 --> 00:01:45.649 align:start position:0%
different because we have an href
attribute<00:01:44.009><c> which</c><00:01:44.820><c> is</c><00:01:44.850><c> highlighted</c><00:01:45.329><c> in</c><00:01:45.450><c> green</c>

00:01:45.649 --> 00:01:45.659 align:start position:0%
attribute which is highlighted in green
 

00:01:45.659 --> 00:01:49.910 align:start position:0%
attribute which is highlighted in green
and<00:01:45.930><c> we</c><00:01:46.610><c> specify</c><00:01:47.610><c> that</c><00:01:48.350><c> the</c><00:01:49.350><c> web</c><00:01:49.500><c> page</c><00:01:49.649><c> we</c><00:01:49.799><c> want</c>

00:01:49.910 --> 00:01:49.920 align:start position:0%
and we specify that the web page we want
 

00:01:49.920 --> 00:01:53.270 align:start position:0%
and we specify that the web page we want
us<00:01:50.100><c> to</c><00:01:50.250><c> go</c><00:01:50.399><c> to</c><00:01:50.460><c> and</c><00:01:50.850><c> then</c><00:01:51.390><c> the</c><00:01:52.079><c> text</c><00:01:52.470><c> msn</c><00:01:53.009><c> is</c>

00:01:53.270 --> 00:01:53.280 align:start position:0%
us to go to and then the text msn is
 

00:01:53.280 --> 00:01:54.830 align:start position:0%
us to go to and then the text msn is
what<00:01:53.430><c> you'll</c><00:01:53.549><c> see</c><00:01:53.579><c> on</c><00:01:53.729><c> the</c><00:01:53.850><c> screen</c><00:01:54.149><c> whenever</c>

00:01:54.830 --> 00:01:54.840 align:start position:0%
what you'll see on the screen whenever
 

00:01:54.840 --> 00:01:56.359 align:start position:0%
what you'll see on the screen whenever
user<00:01:55.020><c> clicks</c><00:01:55.380><c> on</c><00:01:55.470><c> that</c><00:01:55.530><c> it'll</c><00:01:56.009><c> take</c><00:01:56.159><c> them</c><00:01:56.250><c> to</c>

00:01:56.359 --> 00:01:56.369 align:start position:0%
user clicks on that it'll take them to
 

00:01:56.369 --> 00:02:01.399 align:start position:0%
user clicks on that it'll take them to
that<00:01:56.490><c> web</c><00:01:56.640><c> page</c><00:01:57.350><c> okay</c><00:01:58.350><c> so</c><00:01:59.390><c> we've</c><00:02:00.390><c> seen</c><00:02:00.420><c> we've</c>

00:02:01.399 --> 00:02:01.409 align:start position:0%
that web page okay so we've seen we've
 

00:02:01.409 --> 00:02:03.020 align:start position:0%
that web page okay so we've seen we've
seen<00:02:01.649><c> the</c><00:02:01.799><c> a</c><00:02:01.829><c> tag</c><00:02:02.130><c> we've</c><00:02:02.549><c> seen</c><00:02:02.759><c> a</c><00:02:02.790><c> new</c>

00:02:03.020 --> 00:02:03.030 align:start position:0%
seen the a tag we've seen a new
 

00:02:03.030 --> 00:02:05.120 align:start position:0%
seen the a tag we've seen a new
attribute<00:02:03.210><c> and</c><00:02:03.750><c> what</c><00:02:04.560><c> we're</c><00:02:04.680><c> gonna</c><00:02:04.799><c> do</c><00:02:04.920><c> is</c>

00:02:05.120 --> 00:02:05.130 align:start position:0%
attribute and what we're gonna do is
 

00:02:05.130 --> 00:02:06.920 align:start position:0%
attribute and what we're gonna do is
we're<00:02:05.369><c> gonna</c><00:02:05.490><c> get</c><00:02:06.180><c> right</c><00:02:06.299><c> into</c><00:02:06.420><c> the</c><00:02:06.600><c> coding</c>

00:02:06.920 --> 00:02:06.930 align:start position:0%
we're gonna get right into the coding
 

00:02:06.930 --> 00:02:09.350 align:start position:0%
we're gonna get right into the coding
and<00:02:07.079><c> we're</c><00:02:07.890><c> actually</c><00:02:07.979><c> gonna</c><00:02:08.250><c> create</c><00:02:08.550><c> several</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
and we're actually gonna create several
 

00:02:09.360 --> 00:02:11.600 align:start position:0%
and we're actually gonna create several
HTML<00:02:10.170><c> files</c><00:02:10.440><c> because</c><00:02:11.039><c> I</c><00:02:11.069><c> want</c><00:02:11.310><c> to</c><00:02:11.370><c> show</c><00:02:11.550><c> you</c>

00:02:11.600 --> 00:02:11.610 align:start position:0%
HTML files because I want to show you
 

00:02:11.610 --> 00:02:12.770 align:start position:0%
HTML files because I want to show you
how<00:02:11.849><c> you</c><00:02:11.910><c> can</c>

00:02:12.770 --> 00:02:12.780 align:start position:0%
how you can
 

00:02:12.780 --> 00:02:16.740 align:start position:0%
how you can
linked<00:02:13.780><c> to</c><00:02:14.019><c> another</c><00:02:14.379><c> webpage</c><00:02:15.599><c> within</c><00:02:16.599><c> the</c>

00:02:16.740 --> 00:02:16.750 align:start position:0%
linked to another webpage within the
 

00:02:16.750 --> 00:02:19.920 align:start position:0%
linked to another webpage within the
same<00:02:16.930><c> website</c><00:02:17.170><c> and</c><00:02:17.620><c> it's</c><00:02:18.129><c> not</c><00:02:18.489><c> done</c><00:02:19.209><c> quite</c><00:02:19.629><c> the</c>

00:02:19.920 --> 00:02:19.930 align:start position:0%
same website and it's not done quite the
 

00:02:19.930 --> 00:02:22.890 align:start position:0%
same website and it's not done quite the
same<00:02:20.170><c> as</c><00:02:20.439><c> just</c><00:02:20.650><c> specifying</c><00:02:21.220><c> a</c><00:02:21.610><c> URL</c><00:02:22.329><c> and</c><00:02:22.720><c> the</c>

00:02:22.890 --> 00:02:22.900 align:start position:0%
same as just specifying a URL and the
 

00:02:22.900 --> 00:02:24.660 align:start position:0%
same as just specifying a URL and the
URL<00:02:22.930><c> is</c><00:02:23.290><c> what</c><00:02:23.680><c> you</c><00:02:23.769><c> would</c><00:02:23.890><c> type</c><00:02:24.099><c> in</c><00:02:24.340><c> the</c>

00:02:24.660 --> 00:02:24.670 align:start position:0%
URL is what you would type in the
 

00:02:24.670 --> 00:02:27.289 align:start position:0%
URL is what you would type in the
address<00:02:25.030><c> bar</c><00:02:25.480><c> like</c><00:02:25.930><c> google.com</c><00:02:26.260><c> or</c><00:02:26.829><c> msn.com</c>

00:02:27.289 --> 00:02:27.299 align:start position:0%
address bar like google.com or msn.com
 

00:02:27.299 --> 00:02:31.050 align:start position:0%
address bar like google.com or msn.com
or<00:02:28.299><c> facebook.com</c><00:02:29.430><c> that's</c><00:02:30.430><c> just</c><00:02:30.640><c> that's</c><00:02:30.849><c> just</c>

00:02:31.050 --> 00:02:31.060 align:start position:0%
or facebook.com that's just that's just
 

00:02:31.060 --> 00:02:34.440 align:start position:0%
or facebook.com that's just that's just
called<00:02:31.209><c> the</c><00:02:31.360><c> URL</c><00:02:31.750><c> all</c><00:02:32.260><c> right</c><00:02:32.670><c> so</c><00:02:33.670><c> let's</c><00:02:34.239><c> begin</c>

00:02:34.440 --> 00:02:34.450 align:start position:0%
called the URL all right so let's begin
 

00:02:34.450 --> 00:02:36.300 align:start position:0%
called the URL all right so let's begin
coding<00:02:34.720><c> we're</c><00:02:35.709><c> gonna</c><00:02:35.829><c> start</c><00:02:36.040><c> with</c><00:02:36.159><c> the</c>

00:02:36.300 --> 00:02:36.310 align:start position:0%
coding we're gonna start with the
 

00:02:36.310 --> 00:02:40.890 align:start position:0%
coding we're gonna start with the
doctype<00:02:37.560><c> heaven</c><00:02:38.640><c> opening</c><00:02:39.640><c> and</c><00:02:39.879><c> closing</c><00:02:40.540><c> HTML</c>

00:02:40.890 --> 00:02:40.900 align:start position:0%
doctype heaven opening and closing HTML
 

00:02:40.900 --> 00:02:44.460 align:start position:0%
doctype heaven opening and closing HTML
tag<00:02:42.209><c> also</c><00:02:43.209><c> if</c><00:02:43.629><c> you're</c><00:02:43.750><c> using</c><00:02:43.870><c> notepad</c><00:02:44.170><c> plus</c>

00:02:44.460 --> 00:02:44.470 align:start position:0%
tag also if you're using notepad plus
 

00:02:44.470 --> 00:02:47.399 align:start position:0%
tag also if you're using notepad plus
plus<00:02:44.500><c> you</c><00:02:45.459><c> just</c><00:02:45.640><c> go</c><00:02:45.730><c> up</c><00:02:45.790><c> here</c><00:02:45.819><c> to</c><00:02:46.000><c> language</c><00:02:46.409><c> go</c>

00:02:47.399 --> 00:02:47.409 align:start position:0%
plus you just go up here to language go
 

00:02:47.409 --> 00:02:49.800 align:start position:0%
plus you just go up here to language go
to<00:02:47.470><c> the</c><00:02:47.620><c> H</c><00:02:47.739><c> then</c><00:02:48.250><c> click</c><00:02:48.489><c> on</c><00:02:48.609><c> HTML</c><00:02:48.760><c> all</c><00:02:49.659><c> right</c>

00:02:49.800 --> 00:02:49.810 align:start position:0%
to the H then click on HTML all right
 

00:02:49.810 --> 00:02:52.640 align:start position:0%
to the H then click on HTML all right
that'll<00:02:50.379><c> give</c><00:02:50.799><c> you</c><00:02:50.859><c> the</c><00:02:50.889><c> syntax</c><00:02:51.310><c> highlighting</c>

00:02:52.640 --> 00:02:52.650 align:start position:0%
that'll give you the syntax highlighting
 

00:02:52.650 --> 00:02:56.580 align:start position:0%
that'll give you the syntax highlighting
okay<00:02:53.680><c> so</c><00:02:54.549><c> we're</c><00:02:54.730><c> gonna</c><00:02:55.629><c> have</c><00:02:55.780><c> an</c><00:02:55.900><c> opening</c><00:02:56.349><c> and</c>

00:02:56.580 --> 00:02:56.590 align:start position:0%
okay so we're gonna have an opening and
 

00:02:56.590 --> 00:03:00.660 align:start position:0%
okay so we're gonna have an opening and
closing<00:02:57.370><c> head</c><00:02:57.549><c> tag</c><00:02:57.579><c> in</c><00:02:58.209><c> the</c><00:02:59.079><c> middle</c><00:02:59.680><c> we're</c>

00:03:00.660 --> 00:03:00.670 align:start position:0%
closing head tag in the middle we're
 

00:03:00.670 --> 00:03:03.300 align:start position:0%
closing head tag in the middle we're
gonna<00:03:00.760><c> have</c><00:03:01.030><c> a</c><00:03:01.480><c> title</c><00:03:01.959><c> and</c><00:03:02.319><c> we're</c><00:03:02.889><c> call</c><00:03:03.129><c> this</c>

00:03:03.300 --> 00:03:03.310 align:start position:0%
gonna have a title and we're call this
 

00:03:03.310 --> 00:03:08.839 align:start position:0%
gonna have a title and we're call this
link<00:03:04.150><c> demo</c><00:03:04.590><c> I'm</c><00:03:05.590><c> very</c><00:03:05.799><c> original</c><00:03:06.489><c> with</c><00:03:06.760><c> names</c>

00:03:08.839 --> 00:03:08.849 align:start position:0%
link demo I'm very original with names
 

00:03:08.849 --> 00:03:12.690 align:start position:0%
link demo I'm very original with names
okay<00:03:09.849><c> and</c><00:03:10.090><c> then</c><00:03:10.599><c> we</c><00:03:10.720><c> need</c><00:03:10.900><c> a</c><00:03:10.930><c> body</c><00:03:11.440><c> tag</c><00:03:11.709><c> and</c><00:03:11.980><c> the</c>

00:03:12.690 --> 00:03:12.700 align:start position:0%
okay and then we need a body tag and the
 

00:03:12.700 --> 00:03:18.030 align:start position:0%
okay and then we need a body tag and the
closing<00:03:13.019><c> by</c><00:03:14.019><c> peg</c><00:03:15.329><c> okay</c><00:03:16.329><c> so</c><00:03:16.949><c> we're</c><00:03:17.949><c> gonna</c>

00:03:18.030 --> 00:03:18.040 align:start position:0%
closing by peg okay so we're gonna
 

00:03:18.040 --> 00:03:22.770 align:start position:0%
closing by peg okay so we're gonna
create<00:03:18.340><c> a</c><00:03:18.400><c> first</c><00:03:19.709><c> first</c><00:03:20.709><c> a</c><00:03:21.220><c> tag</c><00:03:21.879><c> so</c><00:03:22.449><c> we're</c>

00:03:22.770 --> 00:03:22.780 align:start position:0%
create a first first a tag so we're
 

00:03:22.780 --> 00:03:25.140 align:start position:0%
create a first first a tag so we're
going<00:03:22.930><c> to</c><00:03:22.989><c> opening</c><00:03:23.590><c> a</c><00:03:23.829><c> tag</c><00:03:24.250><c> with</c><00:03:24.639><c> an</c><00:03:24.819><c> href</c>

00:03:25.140 --> 00:03:25.150 align:start position:0%
going to opening a tag with an href
 

00:03:25.150 --> 00:03:29.129 align:start position:0%
going to opening a tag with an href
attribute<00:03:25.359><c> and</c><00:03:26.519><c> we're</c><00:03:27.519><c> gonna</c><00:03:27.639><c> put</c><00:03:27.910><c> in</c><00:03:28.030><c> here</c><00:03:28.139><c> H</c>

00:03:29.129 --> 00:03:29.139 align:start position:0%
attribute and we're gonna put in here H
 

00:03:29.139 --> 00:03:35.330 align:start position:0%
attribute and we're gonna put in here H
ap<00:03:29.470><c> PE</c><00:03:30.629><c> WW</c><00:03:31.629><c> msn.com</c>

00:03:35.330 --> 00:03:35.340 align:start position:0%
 
 

00:03:35.340 --> 00:03:38.550 align:start position:0%
 
MSN<00:03:36.340><c> as</c><00:03:36.669><c> the</c><00:03:37.090><c> text</c><00:03:37.480><c> that's</c><00:03:37.989><c> going</c><00:03:38.199><c> to</c><00:03:38.260><c> go</c><00:03:38.349><c> in</c>

00:03:38.550 --> 00:03:38.560 align:start position:0%
MSN as the text that's going to go in
 

00:03:38.560 --> 00:03:42.330 align:start position:0%
MSN as the text that's going to go in
between<00:03:38.709><c> the</c><00:03:39.400><c> a</c><00:03:39.849><c> tag</c><00:03:40.239><c> at</c><00:03:41.019><c> MSN</c><00:03:41.799><c> text</c><00:03:42.069><c> what</c><00:03:42.220><c> goes</c>

00:03:42.330 --> 00:03:42.340 align:start position:0%
between the a tag at MSN text what goes
 

00:03:42.340 --> 00:03:45.659 align:start position:0%
between the a tag at MSN text what goes
in<00:03:42.430><c> between</c><00:03:42.549><c> opening</c><00:03:43.060><c> and</c><00:03:43.209><c> closing</c><00:03:43.510><c> a</c><00:03:43.599><c> tag</c><00:03:44.669><c> so</c>

00:03:45.659 --> 00:03:45.669 align:start position:0%
in between opening and closing a tag so
 

00:03:45.669 --> 00:03:52.199 align:start position:0%
in between opening and closing a tag so
first<00:03:46.000><c> let's</c><00:03:46.660><c> save</c><00:03:47.620><c> this</c><00:03:48.120><c> on</c><00:03:49.120><c> my</c><00:03:49.440><c> desktop</c><00:03:50.440><c> as</c><00:03:51.209><c> a</c>

00:03:52.199 --> 00:03:52.209 align:start position:0%
first let's save this on my desktop as a
 

00:03:52.209 --> 00:03:57.960 align:start position:0%
first let's save this on my desktop as a
link<00:03:53.310><c> demoed</c><00:03:54.419><c> HTML</c><00:03:55.419><c> and</c><00:03:56.430><c> let's</c><00:03:57.430><c> open</c><00:03:57.639><c> it</c><00:03:57.849><c> up</c>

00:03:57.960 --> 00:03:57.970 align:start position:0%
link demoed HTML and let's open it up
 

00:03:57.970 --> 00:04:03.390 align:start position:0%
link demoed HTML and let's open it up
first<00:03:58.269><c> okay</c>

00:04:03.390 --> 00:04:03.400 align:start position:0%
 
 

00:04:03.400 --> 00:04:06.630 align:start position:0%
 
what<00:04:03.549><c> you</c><00:04:03.700><c> see</c><00:04:04.049><c> let</c><00:04:05.049><c> me</c><00:04:05.280><c> zoom</c><00:04:06.280><c> in</c><00:04:06.430><c> a</c><00:04:06.459><c> little</c><00:04:06.519><c> bit</c>

00:04:06.630 --> 00:04:06.640 align:start position:0%
what you see let me zoom in a little bit
 

00:04:06.640 --> 00:04:09.869 align:start position:0%
what you see let me zoom in a little bit
all<00:04:07.420><c> right</c><00:04:07.450><c> so</c><00:04:07.720><c> my</c><00:04:07.930><c> son</c><00:04:08.110><c> calm</c><00:04:08.489><c> when</c><00:04:09.489><c> we</c><00:04:09.579><c> click</c>

00:04:09.869 --> 00:04:09.879 align:start position:0%
all right so my son calm when we click
 

00:04:09.879 --> 00:04:12.720 align:start position:0%
all right so my son calm when we click
on<00:04:10.030><c> this</c><00:04:10.709><c> that</c><00:04:11.709><c> takes</c><00:04:11.950><c> us</c><00:04:12.040><c> demo</c><00:04:12.220><c> son</c><00:04:12.430><c> now</c><00:04:12.549><c> calm</c>

00:04:12.720 --> 00:04:12.730 align:start position:0%
on this that takes us demo son now calm
 

00:04:12.730 --> 00:04:16.249 align:start position:0%
on this that takes us demo son now calm
ok<00:04:13.360><c> perfect</c><00:04:13.629><c> it</c><00:04:13.930><c> worked</c>

00:04:16.249 --> 00:04:16.259 align:start position:0%
 
 

00:04:16.259 --> 00:04:20.189 align:start position:0%
 
so<00:04:17.259><c> let's</c><00:04:18.479><c> let's</c><00:04:19.479><c> free</c><00:04:19.600><c> another</c><00:04:19.810><c> one</c><00:04:19.930><c> let's</c><00:04:20.079><c> go</c>

00:04:20.189 --> 00:04:20.199 align:start position:0%
so let's let's free another one let's go
 

00:04:20.199 --> 00:04:24.960 align:start position:0%
so let's let's free another one let's go
to<00:04:20.229><c> let's</c><00:04:21.130><c> have</c><00:04:21.310><c> it</c><00:04:23.789><c> anyways</c><00:04:24.789><c> go</c><00:04:24.910><c> to</c>

00:04:24.960 --> 00:04:24.970 align:start position:0%
to let's have it anyways go to
 

00:04:24.970 --> 00:04:32.279 align:start position:0%
to let's have it anyways go to
google.com<00:04:25.240><c> ok</c><00:04:30.419><c> so</c><00:04:31.419><c> again</c><00:04:31.509><c> opening</c><00:04:32.139><c> and</c>

00:04:32.279 --> 00:04:32.289 align:start position:0%
google.com ok so again opening and
 

00:04:32.289 --> 00:04:35.070 align:start position:0%
google.com ok so again opening and
closing<00:04:32.590><c> a</c><00:04:32.680><c> tag</c><00:04:32.910><c> href</c><00:04:33.910><c> attribute</c><00:04:34.120><c> that's</c><00:04:34.750><c> what</c>

00:04:35.070 --> 00:04:35.080 align:start position:0%
closing a tag href attribute that's what
 

00:04:35.080 --> 00:04:38.129 align:start position:0%
closing a tag href attribute that's what
directs<00:04:35.680><c> us</c><00:04:36.180><c> that's</c><00:04:37.180><c> what</c><00:04:37.360><c> tells</c><00:04:37.660><c> the</c><00:04:37.990><c> browser</c>

00:04:38.129 --> 00:04:38.139 align:start position:0%
directs us that's what tells the browser
 

00:04:38.139 --> 00:04:41.969 align:start position:0%
directs us that's what tells the browser
what<00:04:38.590><c> a</c><00:04:39.389><c> specific</c><00:04:40.389><c> webpage</c><00:04:40.630><c> we</c><00:04:41.380><c> want</c><00:04:41.620><c> to</c><00:04:41.800><c> go</c><00:04:41.919><c> to</c>

00:04:41.969 --> 00:04:41.979 align:start position:0%
what a specific webpage we want to go to
 

00:04:41.979 --> 00:04:46.860 align:start position:0%
what a specific webpage we want to go to
okay<00:04:44.070><c> so</c><00:04:45.070><c> we're</c><00:04:45.430><c> gonna</c><00:04:45.520><c> save</c><00:04:45.880><c> this</c><00:04:46.120><c> actually</c>

00:04:46.860 --> 00:04:46.870 align:start position:0%
okay so we're gonna save this actually
 

00:04:46.870 --> 00:04:50.520 align:start position:0%
okay so we're gonna save this actually
let's<00:04:46.990><c> make</c><00:04:48.300><c> it</c><00:04:49.300><c> a</c><00:04:49.510><c> little</c><00:04:49.539><c> bit</c><00:04:49.750><c> better</c><00:04:49.870><c> let's</c>

00:04:50.520 --> 00:04:50.530 align:start position:0%
let's make it a little bit better let's
 

00:04:50.530 --> 00:04:54.360 align:start position:0%
let's make it a little bit better let's
put<00:04:50.800><c> an</c><00:04:50.919><c> h1</c><00:04:51.100><c> tag</c><00:04:51.340><c> above</c><00:04:52.150><c> the</c><00:04:52.360><c> a</c><00:04:52.660><c> tags</c><00:04:53.410><c> and</c><00:04:53.680><c> just</c>

00:04:54.360 --> 00:04:54.370 align:start position:0%
put an h1 tag above the a tags and just
 

00:04:54.370 --> 00:04:59.879 align:start position:0%
put an h1 tag above the a tags and just
call<00:04:54.580><c> it</c><00:04:56.789><c> link</c><00:04:57.789><c> demo</c><00:04:58.120><c> again</c><00:04:58.660><c> because</c><00:04:59.500><c> I'm</c><00:04:59.650><c> so</c>

00:04:59.879 --> 00:04:59.889 align:start position:0%
call it link demo again because I'm so
 

00:04:59.889 --> 00:05:00.589 align:start position:0%
call it link demo again because I'm so
original

00:05:00.589 --> 00:05:00.599 align:start position:0%
original
 

00:05:00.599 --> 00:05:06.659 align:start position:0%
original
alright<00:05:01.599><c> so</c><00:05:01.900><c> whenever</c><00:05:02.830><c> we</c><00:05:03.960><c> save</c><00:05:04.960><c> that</c><00:05:05.610><c> open</c><00:05:06.610><c> up</c>

00:05:06.659 --> 00:05:06.669 align:start position:0%
alright so whenever we save that open up
 

00:05:06.669 --> 00:05:10.260 align:start position:0%
alright so whenever we save that open up
here<00:05:06.940><c> link</c><00:05:07.599><c> them</c><00:05:07.750><c> again</c><00:05:08.580><c> okay</c><00:05:09.580><c> it</c><00:05:10.120><c> doesn't</c>

00:05:10.260 --> 00:05:10.270 align:start position:0%
here link them again okay it doesn't
 

00:05:10.270 --> 00:05:12.060 align:start position:0%
here link them again okay it doesn't
matter<00:05:10.419><c> how</c><00:05:10.960><c> it</c><00:05:11.050><c> looks</c><00:05:11.110><c> this</c><00:05:11.590><c> is</c><00:05:11.770><c> just</c><00:05:11.949><c> for</c>

00:05:12.060 --> 00:05:12.070 align:start position:0%
matter how it looks this is just for
 

00:05:12.070 --> 00:05:15.180 align:start position:0%
matter how it looks this is just for
demo<00:05:12.310><c> so</c><00:05:12.820><c> then</c><00:05:13.180><c> pick</c><00:05:13.389><c> google.com</c><00:05:13.979><c> there</c><00:05:14.979><c> we</c><00:05:15.039><c> go</c>

00:05:15.180 --> 00:05:15.190 align:start position:0%
demo so then pick google.com there we go
 

00:05:15.190 --> 00:05:18.540 align:start position:0%
demo so then pick google.com there we go
Google<00:05:15.639><c> calm</c><00:05:15.930><c> perfect</c><00:05:16.930><c> all</c><00:05:17.530><c> right</c><00:05:17.560><c> so</c><00:05:18.099><c> now</c>

00:05:18.540 --> 00:05:18.550 align:start position:0%
Google calm perfect all right so now
 

00:05:18.550 --> 00:05:22.560 align:start position:0%
Google calm perfect all right so now
we've<00:05:18.729><c> seen</c><00:05:19.479><c> a</c><00:05:19.840><c> tags</c><00:05:21.000><c> direct</c><00:05:22.000><c> us</c><00:05:22.150><c> to</c><00:05:22.360><c> an</c>

00:05:22.560 --> 00:05:22.570 align:start position:0%
we've seen a tags direct us to an
 

00:05:22.570 --> 00:05:24.839 align:start position:0%
we've seen a tags direct us to an
another<00:05:23.020><c> actual</c><00:05:23.289><c> web</c><00:05:23.470><c> page</c><00:05:23.680><c> but</c><00:05:24.400><c> that's</c><00:05:24.610><c> when</c>

00:05:24.839 --> 00:05:24.849 align:start position:0%
another actual web page but that's when
 

00:05:24.849 --> 00:05:26.730 align:start position:0%
another actual web page but that's when
we<00:05:25.030><c> go</c><00:05:25.150><c> that's</c><00:05:25.449><c> when</c><00:05:25.570><c> we</c><00:05:25.630><c> go</c><00:05:25.720><c> to</c><00:05:25.840><c> another</c><00:05:26.260><c> URL</c>

00:05:26.730 --> 00:05:26.740 align:start position:0%
we go that's when we go to another URL
 

00:05:26.740 --> 00:05:29.430 align:start position:0%
we go that's when we go to another URL
that's<00:05:27.340><c> not</c><00:05:27.729><c> on</c><00:05:27.909><c> the</c><00:05:27.970><c> same</c><00:05:28.150><c> site</c><00:05:28.449><c> so</c><00:05:29.050><c> we're</c>

00:05:29.430 --> 00:05:29.440 align:start position:0%
that's not on the same site so we're
 

00:05:29.440 --> 00:05:31.620 align:start position:0%
that's not on the same site so we're
gonna<00:05:29.530><c> create</c><00:05:29.889><c> a</c><00:05:29.949><c> couple</c><00:05:30.190><c> more</c><00:05:30.610><c> HTML</c><00:05:31.060><c> files</c>

00:05:31.620 --> 00:05:31.630 align:start position:0%
gonna create a couple more HTML files
 

00:05:31.630 --> 00:05:34.050 align:start position:0%
gonna create a couple more HTML files
and<00:05:32.020><c> we're</c><00:05:32.889><c> gonna</c><00:05:33.010><c> put</c><00:05:33.550><c> them</c><00:05:33.669><c> in</c><00:05:33.699><c> the</c><00:05:33.789><c> same</c>

00:05:34.050 --> 00:05:34.060 align:start position:0%
and we're gonna put them in the same
 

00:05:34.060 --> 00:05:37.529 align:start position:0%
and we're gonna put them in the same
folder<00:05:34.389><c> and</c><00:05:35.169><c> where</c><00:05:35.349><c> I</c><00:05:35.380><c> have</c><00:05:35.560><c> them</c><00:05:36.449><c> we're</c><00:05:37.449><c> gonna</c>

00:05:37.529 --> 00:05:37.539 align:start position:0%
folder and where I have them we're gonna
 

00:05:37.539 --> 00:05:39.060 align:start position:0%
folder and where I have them we're gonna
have<00:05:37.659><c> like</c><00:05:37.720><c> a</c><00:05:37.780><c> home</c><00:05:37.990><c> page</c><00:05:38.199><c> and</c><00:05:38.530><c> then</c><00:05:38.800><c> it's</c><00:05:38.949><c> got</c>

00:05:39.060 --> 00:05:39.070 align:start position:0%
have like a home page and then it's got
 

00:05:39.070 --> 00:05:42.589 align:start position:0%
have like a home page and then it's got
a<00:05:39.130><c> link</c><00:05:39.310><c> to</c><00:05:39.400><c> just</c><00:05:39.669><c> two</c><00:05:39.880><c> other</c><00:05:40.030><c> pages</c><00:05:40.960><c> alright</c>

00:05:42.589 --> 00:05:42.599 align:start position:0%
a link to just two other pages alright
 

00:05:42.599 --> 00:05:46.740 align:start position:0%
a link to just two other pages alright
so<00:05:43.599><c> you</c><00:05:44.349><c> can</c><00:05:44.380><c> go</c><00:05:44.800><c> up</c><00:05:44.830><c> here</c><00:05:44.919><c> and</c><00:05:45.220><c> hit</c><00:05:45.909><c> the</c><00:05:46.210><c> new</c>

00:05:46.740 --> 00:05:46.750 align:start position:0%
so you can go up here and hit the new
 

00:05:46.750 --> 00:05:48.990 align:start position:0%
so you can go up here and hit the new
button<00:05:46.780><c> I</c><00:05:47.229><c> think</c><00:05:47.650><c> you</c><00:05:47.740><c> can</c><00:05:47.860><c> hit</c><00:05:47.979><c> ctrl</c><00:05:48.430><c> a</c><00:05:48.669><c> new</c>

00:05:48.990 --> 00:05:49.000 align:start position:0%
button I think you can hit ctrl a new
 

00:05:49.000 --> 00:05:51.510 align:start position:0%
button I think you can hit ctrl a new
ctrl<00:05:49.300><c> n</c><00:05:49.560><c> appears</c><00:05:50.560><c> like</c><00:05:50.889><c> that</c><00:05:51.070><c> little</c><00:05:51.220><c> white</c>

00:05:51.510 --> 00:05:51.520 align:start position:0%
ctrl n appears like that little white
 

00:05:51.520 --> 00:05:55.379 align:start position:0%
ctrl n appears like that little white
sheet<00:05:51.849><c> with</c><00:05:52.000><c> a</c><00:05:52.030><c> green</c><00:05:52.270><c> plus</c><00:05:52.510><c> sign</c><00:05:52.750><c> and</c><00:05:54.090><c> if</c><00:05:55.090><c> you</c>

00:05:55.379 --> 00:05:55.389 align:start position:0%
sheet with a green plus sign and if you
 

00:05:55.389 --> 00:05:58.589 align:start position:0%
sheet with a green plus sign and if you
want<00:05:56.280><c> you</c><00:05:57.280><c> can</c><00:05:57.460><c> even</c><00:05:57.550><c> come</c><00:05:57.880><c> over</c><00:05:57.909><c> here</c>

00:05:58.589 --> 00:05:58.599 align:start position:0%
want you can even come over here
 

00:05:58.599 --> 00:06:01.170 align:start position:0%
want you can even come over here
ctrl<00:05:59.409><c> a</c><00:05:59.620><c> highlight</c><00:06:00.070><c> everything</c><00:06:00.250><c> ctrl</c><00:06:00.909><c> C</c><00:06:01.150><c> to</c>

00:06:01.170 --> 00:06:01.180 align:start position:0%
ctrl a highlight everything ctrl C to
 

00:06:01.180 --> 00:06:02.360 align:start position:0%
ctrl a highlight everything ctrl C to
copy

00:06:02.360 --> 00:06:02.370 align:start position:0%
copy
 

00:06:02.370 --> 00:06:07.969 align:start position:0%
copy
come<00:06:03.370><c> over</c><00:06:03.520><c> here</c><00:06:04.000><c> control</c><00:06:04.539><c> V</c><00:06:04.930><c> paste</c><00:06:05.789><c> it</c><00:06:06.789><c> and</c>

00:06:07.969 --> 00:06:07.979 align:start position:0%
come over here control V paste it and
 

00:06:07.979 --> 00:06:21.990 align:start position:0%
come over here control V paste it and
let's<00:06:08.979><c> say</c><00:06:09.460><c> let's</c><00:06:10.330><c> call</c><00:06:10.539><c> this</c><00:06:10.720><c> the</c><00:06:11.700><c> about</c><00:06:12.700><c> page</c>

00:06:21.990 --> 00:06:22.000 align:start position:0%
 
 

00:06:22.000 --> 00:06:24.730 align:start position:0%
 
okay<00:06:23.000><c> so</c><00:06:23.360><c> all</c><00:06:23.449><c> I've</c><00:06:23.599><c> done</c><00:06:23.630><c> here</c><00:06:23.810><c> is</c><00:06:24.229><c> copied</c>

00:06:24.730 --> 00:06:24.740 align:start position:0%
okay so all I've done here is copied
 

00:06:24.740 --> 00:06:27.100 align:start position:0%
okay so all I've done here is copied
what<00:06:24.949><c> we</c><00:06:25.069><c> just</c><00:06:25.280><c> did</c><00:06:25.460><c> for</c><00:06:25.669><c> the</c><00:06:25.849><c> link</c><00:06:26.060><c> demo</c><00:06:26.360><c> HTML</c>

00:06:27.100 --> 00:06:27.110 align:start position:0%
what we just did for the link demo HTML
 

00:06:27.110 --> 00:06:30.309 align:start position:0%
what we just did for the link demo HTML
file<00:06:27.970><c> copy</c><00:06:28.970><c> and</c><00:06:29.150><c> pasted</c><00:06:29.449><c> got</c><00:06:29.840><c> rid</c><00:06:29.990><c> of</c><00:06:30.020><c> all</c><00:06:30.229><c> the</c>

00:06:30.309 --> 00:06:30.319 align:start position:0%
file copy and pasted got rid of all the
 

00:06:30.319 --> 00:06:33.370 align:start position:0%
file copy and pasted got rid of all the
a<00:06:30.349><c> tags</c><00:06:30.680><c> and</c><00:06:31.479><c> just</c><00:06:32.479><c> changed</c><00:06:32.720><c> the</c><00:06:32.810><c> title</c><00:06:33.139><c> and</c>

00:06:33.370 --> 00:06:33.380 align:start position:0%
a tags and just changed the title and
 

00:06:33.380 --> 00:06:36.850 align:start position:0%
a tags and just changed the title and
the<00:06:33.949><c> h1</c><00:06:34.370><c> tag</c><00:06:34.550><c> just</c><00:06:34.759><c> to</c><00:06:34.880><c> say</c><00:06:35.030><c> about</c><00:06:35.389><c> page</c><00:06:35.740><c> so</c><00:06:36.740><c> if</c>

00:06:36.850 --> 00:06:36.860 align:start position:0%
the h1 tag just to say about page so if
 

00:06:36.860 --> 00:06:39.640 align:start position:0%
the h1 tag just to say about page so if
we<00:06:37.039><c> can</c><00:06:37.160><c> give</c><00:06:37.280><c> it</c><00:06:37.400><c> and</c><00:06:37.639><c> save</c><00:06:38.360><c> as</c><00:06:38.569><c> and</c><00:06:39.440><c> when</c><00:06:39.590><c> I</c>

00:06:39.640 --> 00:06:39.650 align:start position:0%
we can give it and save as and when I
 

00:06:39.650 --> 00:06:45.730 align:start position:0%
we can give it and save as and when I
call<00:06:39.830><c> this</c><00:06:40.099><c> about</c><00:06:41.139><c> dot</c><00:06:42.139><c> HTML</c><00:06:44.560><c> okay</c>

00:06:45.730 --> 00:06:45.740 align:start position:0%
call this about dot HTML okay
 

00:06:45.740 --> 00:06:48.129 align:start position:0%
call this about dot HTML okay
we're<00:06:46.039><c> going</c><00:06:46.160><c> to</c><00:06:46.220><c> create</c><00:06:46.430><c> another</c><00:06:46.490><c> one</c><00:06:47.139><c> well</c>

00:06:48.129 --> 00:06:48.139 align:start position:0%
we're going to create another one well
 

00:06:48.139 --> 00:06:50.890 align:start position:0%
we're going to create another one well
we<00:06:48.259><c> can</c><00:06:48.380><c> actually</c><00:06:48.620><c> just</c><00:06:48.770><c> copy</c><00:06:49.190><c> the</c><00:06:49.490><c> about</c><00:06:49.900><c> dot</c>

00:06:50.890 --> 00:06:50.900 align:start position:0%
we can actually just copy the about dot
 

00:06:50.900 --> 00:06:53.680 align:start position:0%
we can actually just copy the about dot
HTML<00:06:51.289><c> file</c><00:06:51.530><c> and</c><00:06:52.000><c> then</c><00:06:53.000><c> we'll</c><00:06:53.150><c> just</c><00:06:53.360><c> paste</c><00:06:53.599><c> it</c>

00:06:53.680 --> 00:06:53.690 align:start position:0%
HTML file and then we'll just paste it
 

00:06:53.690 --> 00:06:54.360 align:start position:0%
HTML file and then we'll just paste it
over<00:06:53.720><c> here</c>

00:06:54.360 --> 00:06:54.370 align:start position:0%
over here
 

00:06:54.370 --> 00:06:57.999 align:start position:0%
over here
so<00:06:55.370><c> it</c><00:06:55.490><c> can</c><00:06:56.229><c> highlight</c><00:06:57.229><c> everything</c><00:06:57.620><c> with</c><00:06:57.740><c> ctrl</c>

00:06:57.999 --> 00:06:58.009 align:start position:0%
so it can highlight everything with ctrl
 

00:06:58.009 --> 00:07:02.140 align:start position:0%
so it can highlight everything with ctrl
a<00:06:58.039><c> ctrl</c><00:06:58.460><c> C</c><00:06:59.240><c> copies</c><00:06:59.599><c> over</c><00:07:00.440><c> here</c><00:07:00.710><c> hit</c><00:07:01.250><c> ctrl</c><00:07:01.520><c> V</c><00:07:01.820><c> and</c>

00:07:02.140 --> 00:07:02.150 align:start position:0%
a ctrl C copies over here hit ctrl V and
 

00:07:02.150 --> 00:07:10.050 align:start position:0%
a ctrl C copies over here hit ctrl V and
let's<00:07:02.870><c> call</c><00:07:03.289><c> this</c><00:07:03.440><c> just</c><00:07:03.710><c> contact</c><00:07:04.370><c> page</c><00:07:06.849><c> yeah</c>

00:07:10.050 --> 00:07:10.060 align:start position:0%
 
 

00:07:10.060 --> 00:07:11.640 align:start position:0%
 
okay

00:07:11.640 --> 00:07:11.650 align:start position:0%
okay
 

00:07:11.650 --> 00:07:20.860 align:start position:0%
okay
save<00:07:12.650><c> as</c><00:07:13.810><c> contact</c><00:07:14.810><c> on</c><00:07:15.020><c> HTML</c><00:07:18.280><c> all</c><00:07:19.280><c> right</c><00:07:19.520><c> so</c><00:07:20.000><c> as</c>

00:07:20.860 --> 00:07:20.870 align:start position:0%
save as contact on HTML all right so as
 

00:07:20.870 --> 00:07:26.560 align:start position:0%
save as contact on HTML all right so as
you<00:07:21.080><c> can</c><00:07:21.199><c> see</c><00:07:21.380><c> here</c><00:07:21.849><c> these</c><00:07:22.849><c> are</c><00:07:22.909><c> all</c><00:07:25.449><c> these</c><00:07:26.449><c> are</c>

00:07:26.560 --> 00:07:26.570 align:start position:0%
you can see here these are all these are
 

00:07:26.570 --> 00:07:29.290 align:start position:0%
you can see here these are all these are
all<00:07:26.630><c> just</c><00:07:26.930><c> sitting</c><00:07:27.380><c> there</c><00:07:27.530><c> on</c><00:07:27.650><c> my</c><00:07:27.740><c> desktop</c><00:07:28.300><c> we</c>

00:07:29.290 --> 00:07:29.300 align:start position:0%
all just sitting there on my desktop we
 

00:07:29.300 --> 00:07:30.730 align:start position:0%
all just sitting there on my desktop we
can<00:07:29.389><c> make</c><00:07:29.479><c> this</c><00:07:29.570><c> we</c><00:07:30.139><c> can</c><00:07:30.289><c> make</c><00:07:30.380><c> this</c><00:07:30.500><c> look</c><00:07:30.680><c> a</c>

00:07:30.730 --> 00:07:30.740 align:start position:0%
can make this we can make this look a
 

00:07:30.740 --> 00:07:33.399 align:start position:0%
can make this we can make this look a
little<00:07:30.860><c> neater</c><00:07:31.729><c> doesn't</c><00:07:32.630><c> matter</c><00:07:32.810><c> but</c><00:07:33.199><c> we</c><00:07:33.320><c> can</c>

00:07:33.399 --> 00:07:33.409 align:start position:0%
little neater doesn't matter but we can
 

00:07:33.409 --> 00:07:36.520 align:start position:0%
little neater doesn't matter but we can
create<00:07:33.530><c> a</c><00:07:33.650><c> folder</c><00:07:34.000><c> call</c><00:07:35.000><c> it</c><00:07:35.120><c> demo</c><00:07:35.349><c> so</c><00:07:36.349><c> just</c>

00:07:36.520 --> 00:07:36.530 align:start position:0%
create a folder call it demo so just
 

00:07:36.530 --> 00:07:39.070 align:start position:0%
create a folder call it demo so just
right-click<00:07:36.860><c> create</c><00:07:37.820><c> a</c><00:07:37.969><c> folder</c><00:07:38.270><c> call</c><00:07:38.780><c> it</c><00:07:38.900><c> demo</c>

00:07:39.070 --> 00:07:39.080 align:start position:0%
right-click create a folder call it demo
 

00:07:39.080 --> 00:07:41.439 align:start position:0%
right-click create a folder call it demo
on<00:07:39.320><c> your</c><00:07:39.440><c> desktop</c><00:07:39.800><c> and</c><00:07:40.400><c> just</c><00:07:40.550><c> highlight</c><00:07:40.789><c> all</c>

00:07:41.439 --> 00:07:41.449 align:start position:0%
on your desktop and just highlight all
 

00:07:41.449 --> 00:07:45.360 align:start position:0%
on your desktop and just highlight all
through<00:07:41.960><c> these</c><00:07:42.080><c> files</c><00:07:42.289><c> put</c><00:07:42.860><c> them</c><00:07:42.979><c> in</c><00:07:43.070><c> there</c>

00:07:45.360 --> 00:07:45.370 align:start position:0%
 
 

00:07:45.370 --> 00:07:47.890 align:start position:0%
 
okay<00:07:46.370><c> so</c><00:07:46.699><c> now</c><00:07:46.820><c> we</c><00:07:46.880><c> have</c><00:07:46.969><c> all</c><00:07:47.210><c> three</c><00:07:47.240><c> all</c><00:07:47.659><c> these</c>

00:07:47.890 --> 00:07:47.900 align:start position:0%
okay so now we have all three all these
 

00:07:47.900 --> 00:07:52.120 align:start position:0%
okay so now we have all three all these
three<00:07:48.199><c> in</c><00:07:48.320><c> here</c><00:07:50.080><c> and</c><00:07:51.080><c> what</c><00:07:51.199><c> we</c><00:07:51.289><c> want</c><00:07:51.560><c> to</c><00:07:51.620><c> do</c><00:07:51.860><c> now</c>

00:07:52.120 --> 00:07:52.130 align:start position:0%
three in here and what we want to do now
 

00:07:52.130 --> 00:07:57.370 align:start position:0%
three in here and what we want to do now
oh<00:07:56.229><c> okay</c>

00:07:57.370 --> 00:07:57.380 align:start position:0%
oh okay
 

00:07:57.380 --> 00:07:58.510 align:start position:0%
oh okay
if<00:07:57.620><c> it</c>

00:07:58.510 --> 00:07:58.520 align:start position:0%
if it
 

00:07:58.520 --> 00:07:59.770 align:start position:0%
if it
when<00:07:58.580><c> you</c><00:07:58.669><c> go</c><00:07:58.789><c> back</c><00:07:58.970><c> and</c><00:07:59.150><c> anything</c><00:07:59.509><c> about</c><00:07:59.629><c> that</c>

00:07:59.770 --> 00:07:59.780 align:start position:0%
when you go back and anything about that
 

00:07:59.780 --> 00:08:01.600 align:start position:0%
when you go back and anything about that
and<00:07:59.960><c> when</c><00:08:00.050><c> you</c><00:08:00.110><c> go</c><00:08:00.229><c> back</c><00:08:00.440><c> it's</c><00:08:01.009><c> gonna</c><00:08:01.220><c> say</c><00:08:01.490><c> do</c>

00:08:01.600 --> 00:08:01.610 align:start position:0%
and when you go back it's gonna say do
 

00:08:01.610 --> 00:08:08.379 align:start position:0%
and when you go back it's gonna say do
you<00:08:01.669><c> want</c><00:08:01.819><c> keep</c><00:08:03.039><c> keep</c><00:08:04.039><c> these</c><00:08:04.250><c> in</c><00:08:05.349><c> your</c><00:08:07.389><c> keep</c>

00:08:08.379 --> 00:08:08.389 align:start position:0%
you want keep keep these in your keep
 

00:08:08.389 --> 00:08:10.480 align:start position:0%
you want keep keep these in your keep
these<00:08:08.509><c> on</c><00:08:08.599><c> your</c><00:08:08.720><c> notepad</c><00:08:08.870><c> plus</c><00:08:09.169><c> plus</c><00:08:09.490><c> editor</c>

00:08:10.480 --> 00:08:10.490 align:start position:0%
these on your notepad plus plus editor
 

00:08:10.490 --> 00:08:15.070 align:start position:0%
these on your notepad plus plus editor
but<00:08:11.210><c> we</c><00:08:11.270><c> actually</c><00:08:11.360><c> just</c><00:08:11.569><c> exit</c><00:08:12.020><c> out</c><00:08:14.050><c> come</c><00:08:15.050><c> back</c>

00:08:15.070 --> 00:08:15.080 align:start position:0%
but we actually just exit out come back
 

00:08:15.080 --> 00:08:18.279 align:start position:0%
but we actually just exit out come back
in<00:08:15.229><c> demo</c><00:08:15.650><c> and</c><00:08:16.009><c> we</c><00:08:16.159><c> can</c><00:08:16.370><c> open</c><00:08:16.970><c> up</c><00:08:17.270><c> the</c><00:08:17.659><c> link</c><00:08:18.020><c> demo</c>

00:08:18.279 --> 00:08:18.289 align:start position:0%
in demo and we can open up the link demo
 

00:08:18.289 --> 00:08:20.920 align:start position:0%
in demo and we can open up the link demo
which<00:08:18.530><c> is</c><00:08:18.560><c> the</c><00:08:18.800><c> main</c><00:08:18.979><c> page</c><00:08:19.629><c> if</c><00:08:20.629><c> your</c><00:08:20.780><c> right</c>

00:08:20.920 --> 00:08:20.930 align:start position:0%
which is the main page if your right
 

00:08:20.930 --> 00:08:24.330 align:start position:0%
which is the main page if your right
click<00:08:21.050><c> go</c><00:08:21.409><c> to</c><00:08:21.740><c> edit</c><00:08:22.069><c> with</c><00:08:22.190><c> notepad</c><00:08:22.220><c> plus</c><00:08:22.699><c> plus</c>

00:08:24.330 --> 00:08:24.340 align:start position:0%
click go to edit with notepad plus plus
 

00:08:24.340 --> 00:08:31.839 align:start position:0%
click go to edit with notepad plus plus
okay<00:08:25.479><c> so</c><00:08:26.560><c> we</c><00:08:27.560><c> need</c><00:08:27.710><c> another</c><00:08:27.830><c> a</c><00:08:28.129><c> tag</c><00:08:28.509><c> and</c><00:08:30.849><c> it's</c>

00:08:31.839 --> 00:08:31.849 align:start position:0%
okay so we need another a tag and it's
 

00:08:31.849 --> 00:08:36.399 align:start position:0%
okay so we need another a tag and it's
gonna<00:08:32.029><c> be</c><00:08:32.120><c> a</c><00:08:32.149><c> little</c><00:08:32.240><c> different</c><00:08:32.599><c> so</c><00:08:35.200><c> all</c><00:08:36.200><c> what</c>

00:08:36.399 --> 00:08:36.409 align:start position:0%
gonna be a little different so all what
 

00:08:36.409 --> 00:08:40.480 align:start position:0%
gonna be a little different so all what
you<00:08:36.529><c> do</c><00:08:36.740><c> is</c><00:08:37.930><c> create</c><00:08:38.930><c> two</c><00:08:39.020><c> more</c><00:08:39.229><c> a</c><00:08:39.289><c> tags</c><00:08:39.620><c> one</c><00:08:40.279><c> for</c>

00:08:40.480 --> 00:08:40.490 align:start position:0%
you do is create two more a tags one for
 

00:08:40.490 --> 00:08:42.730 align:start position:0%
you do is create two more a tags one for
the<00:08:40.550><c> about</c><00:08:40.849><c> pate</c><00:08:41.089><c> about</c><00:08:41.450><c> page</c><00:08:41.690><c> and</c><00:08:42.020><c> we're</c>

00:08:42.730 --> 00:08:42.740 align:start position:0%
the about pate about page and we're
 

00:08:42.740 --> 00:08:44.829 align:start position:0%
the about pate about page and we're
gonna<00:08:42.829><c> have</c><00:08:43.010><c> empty</c><00:08:43.339><c> href</c><00:08:43.820><c> attribute</c><00:08:44.029><c> right</c>

00:08:44.829 --> 00:08:44.839 align:start position:0%
gonna have empty href attribute right
 

00:08:44.839 --> 00:08:46.360 align:start position:0%
gonna have empty href attribute right
for<00:08:45.020><c> right</c><00:08:45.110><c> now</c><00:08:45.140><c> I'm</c><00:08:45.680><c> gonna</c><00:08:45.829><c> explain</c><00:08:46.100><c> that</c><00:08:46.250><c> in</c>

00:08:46.360 --> 00:08:46.370 align:start position:0%
for right now I'm gonna explain that in
 

00:08:46.370 --> 00:08:48.340 align:start position:0%
for right now I'm gonna explain that in
just<00:08:46.490><c> a</c><00:08:46.760><c> second</c><00:08:47.149><c> so</c><00:08:47.630><c> do</c><00:08:47.810><c> your</c><00:08:47.899><c> opening</c><00:08:48.260><c> and</c>

00:08:48.340 --> 00:08:48.350 align:start position:0%
just a second so do your opening and
 

00:08:48.350 --> 00:08:52.690 align:start position:0%
just a second so do your opening and
closing<00:08:48.709><c> a</c><00:08:49.070><c> tanks</c><00:08:49.610><c> and</c><00:08:49.820><c> empty</c><00:08:50.329><c> HRF</c><00:08:51.579><c> and</c><00:08:52.579><c> when</c>

00:08:52.690 --> 00:08:52.700 align:start position:0%
closing a tanks and empty HRF and when
 

00:08:52.700 --> 00:08:55.720 align:start position:0%
closing a tanks and empty HRF and when
you're<00:08:52.820><c> done</c><00:08:53.200><c> so</c><00:08:54.200><c> since</c><00:08:54.770><c> we're</c><00:08:54.950><c> not</c><00:08:55.040><c> going</c><00:08:55.279><c> to</c>

00:08:55.720 --> 00:08:55.730 align:start position:0%
you're done so since we're not going to
 

00:08:55.730 --> 00:08:58.740 align:start position:0%
you're done so since we're not going to
another<00:08:56.570><c> URL</c><00:08:56.870><c> we're</c><00:08:57.380><c> gonna</c><00:08:57.560><c> be</c><00:08:57.740><c> going</c><00:08:58.640><c> to</c>

00:08:58.740 --> 00:08:58.750 align:start position:0%
another URL we're gonna be going to
 

00:08:58.750 --> 00:09:01.960 align:start position:0%
another URL we're gonna be going to
another<00:08:59.750><c> HTML</c><00:09:00.260><c> file</c><00:09:00.680><c> that's</c><00:09:01.310><c> within</c><00:09:01.550><c> the</c><00:09:01.760><c> same</c>

00:09:01.960 --> 00:09:01.970 align:start position:0%
another HTML file that's within the same
 

00:09:01.970 --> 00:09:04.030 align:start position:0%
another HTML file that's within the same
folder<00:09:02.420><c> this</c><00:09:03.110><c> is</c><00:09:03.260><c> what's</c><00:09:03.470><c> called</c><00:09:03.680><c> a</c><00:09:03.770><c> relative</c>

00:09:04.030 --> 00:09:04.040 align:start position:0%
folder this is what's called a relative
 

00:09:04.040 --> 00:09:10.240 align:start position:0%
folder this is what's called a relative
URL<00:09:05.740><c> not</c><00:09:06.740><c> an</c><00:09:07.300><c> absolute</c><00:09:08.300><c> URL</c><00:09:08.839><c> which</c><00:09:09.050><c> is</c><00:09:09.079><c> up</c><00:09:10.040><c> here</c>

00:09:10.240 --> 00:09:10.250 align:start position:0%
URL not an absolute URL which is up here
 

00:09:10.250 --> 00:09:13.630 align:start position:0%
URL not an absolute URL which is up here
MSN<00:09:11.029><c> com</c><00:09:11.420><c> Google</c><00:09:11.870><c> com</c><00:09:12.250><c> these</c><00:09:13.250><c> are</c><00:09:13.430><c> called</c>

00:09:13.630 --> 00:09:13.640 align:start position:0%
MSN com Google com these are called
 

00:09:13.640 --> 00:09:16.690 align:start position:0%
MSN com Google com these are called
those<00:09:13.940><c> are</c><00:09:14.120><c> absolute</c><00:09:14.839><c> URLs</c><00:09:15.560><c> we're</c><00:09:16.520><c> going</c><00:09:16.640><c> to</c>

00:09:16.690 --> 00:09:16.700 align:start position:0%
those are absolute URLs we're going to
 

00:09:16.700 --> 00:09:20.380 align:start position:0%
those are absolute URLs we're going to
be<00:09:16.790><c> doing</c><00:09:16.910><c> a</c><00:09:17.089><c> relative</c><00:09:17.630><c> URL</c><00:09:18.020><c> so</c><00:09:18.860><c> all</c><00:09:19.820><c> we</c><00:09:20.240><c> have</c>

00:09:20.380 --> 00:09:20.390 align:start position:0%
be doing a relative URL so all we have
 

00:09:20.390 --> 00:09:22.900 align:start position:0%
be doing a relative URL so all we have
to<00:09:20.510><c> do</c><00:09:20.660><c> is</c><00:09:20.959><c> just</c><00:09:21.649><c> type</c><00:09:21.829><c> in</c><00:09:22.070><c> the</c><00:09:22.490><c> name</c><00:09:22.640><c> of</c><00:09:22.700><c> the</c>

00:09:22.900 --> 00:09:22.910 align:start position:0%
to do is just type in the name of the
 

00:09:22.910 --> 00:09:23.290 align:start position:0%
to do is just type in the name of the
file

00:09:23.290 --> 00:09:23.300 align:start position:0%
file
 

00:09:23.300 --> 00:09:28.170 align:start position:0%
file
about<00:09:23.750><c> HTML</c><00:09:24.470><c> and</c><00:09:24.680><c> then</c><00:09:25.579><c> contact</c><00:09:26.300><c> dot</c><00:09:27.050><c> HTML</c>

00:09:28.170 --> 00:09:28.180 align:start position:0%
about HTML and then contact dot HTML
 

00:09:28.180 --> 00:09:31.920 align:start position:0%
about HTML and then contact dot HTML
alright<00:09:29.180><c> that's</c><00:09:29.390><c> it</c><00:09:29.540><c> so</c><00:09:30.290><c> we're</c><00:09:30.440><c> gonna</c><00:09:30.529><c> save</c><00:09:30.770><c> it</c>

00:09:31.920 --> 00:09:31.930 align:start position:0%
alright that's it so we're gonna save it
 

00:09:31.930 --> 00:09:37.319 align:start position:0%
alright that's it so we're gonna save it
exit<00:09:32.930><c> and</c><00:09:34.060><c> then</c><00:09:35.230><c> open</c><00:09:36.230><c> up</c><00:09:36.260><c> the</c><00:09:36.380><c> link</c><00:09:36.500><c> demo</c><00:09:36.770><c> and</c>

00:09:37.319 --> 00:09:37.329 align:start position:0%
exit and then open up the link demo and
 

00:09:37.329 --> 00:09:41.350 align:start position:0%
exit and then open up the link demo and
as<00:09:38.329><c> you</c><00:09:38.480><c> can</c><00:09:38.540><c> see</c><00:09:38.709><c> we</c><00:09:39.709><c> still</c><00:09:39.890><c> have</c><00:09:39.970><c> let</c><00:09:40.970><c> me</c><00:09:41.060><c> zoom</c>

00:09:41.350 --> 00:09:41.360 align:start position:0%
as you can see we still have let me zoom
 

00:09:41.360 --> 00:09:47.889 align:start position:0%
as you can see we still have let me zoom
in<00:09:41.540><c> we</c><00:09:42.470><c> still</c><00:09:42.620><c> have</c><00:09:42.829><c> all</c><00:09:43.700><c> four</c><00:09:44.500><c> of</c><00:09:45.500><c> the</c><00:09:46.190><c> a</c><00:09:46.970><c> tags</c>

00:09:47.889 --> 00:09:47.899 align:start position:0%
in we still have all four of the a tags
 

00:09:47.899 --> 00:09:52.690 align:start position:0%
in we still have all four of the a tags
and<00:09:49.089><c> if</c><00:09:50.089><c> we</c><00:09:50.209><c> click</c><00:09:50.420><c> about</c><00:09:51.310><c> that</c><00:09:52.310><c> goes</c><00:09:52.520><c> you</c><00:09:52.550><c> get</c>

00:09:52.690 --> 00:09:52.700 align:start position:0%
and if we click about that goes you get
 

00:09:52.700 --> 00:09:56.370 align:start position:0%
and if we click about that goes you get
about<00:09:52.850><c> page</c><00:09:53.149><c> if</c><00:09:54.020><c> we</c><00:09:54.200><c> go</c><00:09:54.829><c> back</c><00:09:55.040><c> click</c><00:09:55.730><c> contact</c>

00:09:56.370 --> 00:09:56.380 align:start position:0%
about page if we go back click contact
 

00:09:56.380 --> 00:10:00.160 align:start position:0%
about page if we go back click contact
goes<00:09:57.380><c> to</c><00:09:57.529><c> the</c><00:09:57.649><c> contact</c><00:09:58.040><c> page</c><00:09:58.660><c> okay</c><00:09:59.660><c> so</c><00:09:59.959><c> we're</c>

00:10:00.160 --> 00:10:00.170 align:start position:0%
goes to the contact page okay so we're
 

00:10:00.170 --> 00:10:02.740 align:start position:0%
goes to the contact page okay so we're
gonna<00:10:00.260><c> do</c><00:10:00.410><c> a</c><00:10:00.440><c> quick</c><00:10:00.740><c> recap</c><00:10:01.310><c> of</c><00:10:01.520><c> kind</c><00:10:01.850><c> of</c><00:10:02.000><c> what</c>

00:10:02.740 --> 00:10:02.750 align:start position:0%
gonna do a quick recap of kind of what
 

00:10:02.750 --> 00:10:04.960 align:start position:0%
gonna do a quick recap of kind of what
we<00:10:02.870><c> just</c><00:10:03.050><c> learned</c><00:10:03.520><c> there</c><00:10:04.520><c> were</c><00:10:04.610><c> a</c><00:10:04.640><c> few</c><00:10:04.790><c> things</c>

00:10:04.960 --> 00:10:04.970 align:start position:0%
we just learned there were a few things
 

00:10:04.970 --> 00:10:07.630 align:start position:0%
we just learned there were a few things
okay<00:10:05.510><c> so</c><00:10:05.570><c> first</c><00:10:06.110><c> of</c><00:10:06.230><c> all</c><00:10:06.320><c> links</c><00:10:06.890><c> are</c><00:10:07.100><c> creating</c>

00:10:07.630 --> 00:10:07.640 align:start position:0%
okay so first of all links are creating
 

00:10:07.640 --> 00:10:11.879 align:start position:0%
okay so first of all links are creating
using<00:10:07.880><c> the</c><00:10:08.120><c> a</c><00:10:08.329><c> element</c><00:10:09.050><c> so</c><00:10:09.680><c> Yool</c><00:10:09.949><c> Yool</c><00:10:10.310><c> code</c><00:10:10.910><c> a</c>

00:10:11.879 --> 00:10:11.889 align:start position:0%
using the a element so Yool Yool code a
 

00:10:11.889 --> 00:10:14.429 align:start position:0%
using the a element so Yool Yool code a
tags<00:10:12.279><c> and</c><00:10:12.519><c> closing</c><00:10:12.850><c> tags</c><00:10:13.059><c> we</c><00:10:13.899><c> introduced</c><00:10:14.319><c> an</c>

00:10:14.429 --> 00:10:14.439 align:start position:0%
tags and closing tags we introduced an
 

00:10:14.439 --> 00:10:17.340 align:start position:0%
tags and closing tags we introduced an
attribute<00:10:15.009><c> for</c><00:10:15.160><c> the</c><00:10:15.220><c> first</c><00:10:15.489><c> time</c><00:10:16.079><c> which</c><00:10:17.079><c> is</c>

00:10:17.340 --> 00:10:17.350 align:start position:0%
attribute for the first time which is
 

00:10:17.350 --> 00:10:20.220 align:start position:0%
attribute for the first time which is
used<00:10:18.309><c> on</c><00:10:18.549><c> an</c><00:10:18.790><c> HTML</c><00:10:19.179><c> tag</c><00:10:19.359><c> which</c><00:10:20.019><c> either</c>

00:10:20.220 --> 00:10:20.230 align:start position:0%
used on an HTML tag which either
 

00:10:20.230 --> 00:10:23.519 align:start position:0%
used on an HTML tag which either
modifies<00:10:20.769><c> or</c><00:10:21.009><c> edge</c><00:10:21.429><c> to</c><00:10:21.730><c> the</c><00:10:21.850><c> element</c><00:10:22.529><c> there</c>

00:10:23.519 --> 00:10:23.529 align:start position:0%
modifies or edge to the element there
 

00:10:23.529 --> 00:10:26.239 align:start position:0%
modifies or edge to the element there
are<00:10:23.679><c> other</c><00:10:23.889><c> tags</c><00:10:24.670><c> that</c><00:10:24.819><c> will</c><00:10:25.149><c> use</c><00:10:25.600><c> soon</c>

00:10:26.239 --> 00:10:26.249 align:start position:0%
are other tags that will use soon
 

00:10:26.249 --> 00:10:29.329 align:start position:0%
are other tags that will use soon
obviously<00:10:27.249><c> that</c><00:10:27.369><c> do</c><00:10:27.459><c> different</c><00:10:27.730><c> things</c><00:10:27.759><c> and</c>

00:10:29.329 --> 00:10:29.339 align:start position:0%
obviously that do different things and
 

00:10:29.339 --> 00:10:33.509 align:start position:0%
obviously that do different things and
the<00:10:30.339><c> href</c><00:10:30.669><c> attribute</c><00:10:30.730><c> in</c><00:10:31.569><c> an</c><00:10:32.079><c> a</c><00:10:32.290><c> tag</c><00:10:32.589><c> is</c><00:10:32.919><c> what</c>

00:10:33.509 --> 00:10:33.519 align:start position:0%
the href attribute in an a tag is what
 

00:10:33.519 --> 00:10:36.650 align:start position:0%
the href attribute in an a tag is what
indicates<00:10:34.149><c> the</c><00:10:34.389><c> page</c><00:10:34.689><c> you</c><00:10:35.230><c> want</c><00:10:35.410><c> to</c><00:10:35.529><c> link</c><00:10:35.589><c> to</c>

00:10:36.650 --> 00:10:36.660 align:start position:0%
indicates the page you want to link to
 

00:10:36.660 --> 00:10:40.530 align:start position:0%
indicates the page you want to link to
and<00:10:37.660><c> if</c><00:10:38.139><c> you're</c><00:10:38.290><c> using</c><00:10:38.559><c> a</c><00:10:38.769><c> link</c><00:10:39.249><c> that's</c><00:10:40.239><c> within</c>

00:10:40.530 --> 00:10:40.540 align:start position:0%
and if you're using a link that's within
 

00:10:40.540 --> 00:10:43.229 align:start position:0%
and if you're using a link that's within
the<00:10:41.079><c> same</c><00:10:41.319><c> website</c><00:10:41.910><c> you</c><00:10:42.910><c> want</c><00:10:43.059><c> to</c><00:10:43.149><c> use</c>

00:10:43.229 --> 00:10:43.239 align:start position:0%
the same website you want to use
 

00:10:43.239 --> 00:10:46.409 align:start position:0%
the same website you want to use
relative<00:10:43.749><c> links</c><00:10:44.109><c> rather</c><00:10:44.589><c> than</c><00:10:45.419><c> qualified</c>

00:10:46.409 --> 00:10:46.419 align:start position:0%
relative links rather than qualified
 

00:10:46.419 --> 00:10:49.799 align:start position:0%
relative links rather than qualified
URLs<00:10:47.079><c> or</c><00:10:47.459><c> absolute</c><00:10:48.459><c> URLs</c><00:10:48.910><c> so</c><00:10:49.509><c> instead</c><00:10:49.749><c> of</c>

00:10:49.799 --> 00:10:49.809 align:start position:0%
URLs or absolute URLs so instead of
 

00:10:49.809 --> 00:11:00.869 align:start position:0%
URLs or absolute URLs so instead of
saying<00:10:53.309><c> http:www.youtube.com/watch</c><00:10:59.429><c> v</c><00:11:00.429><c> mail</c>

00:11:00.869 --> 00:11:00.879 align:start position:0%
saying http:www.youtube.com/watch v mail
 

00:11:00.879 --> 00:11:02.489 align:start position:0%
saying http:www.youtube.com/watch v mail
thought<00:11:01.299><c> you</c><00:11:01.389><c> smelled</c><00:11:01.540><c> that</c><00:11:01.779><c> we</c><00:11:01.899><c> just</c><00:11:02.169><c> that</c><00:11:02.410><c> we</c>

00:11:02.489 --> 00:11:02.499 align:start position:0%
thought you smelled that we just that we
 

00:11:02.499 --> 00:11:05.419 align:start position:0%
thought you smelled that we just that we
just<00:11:02.649><c> read</c><00:11:02.769><c> about</c><00:11:02.799><c> or</c><00:11:03.579><c> that</c><00:11:03.759><c> we</c><00:11:03.819><c> just</c><00:11:03.939><c> used</c>

00:11:05.419 --> 00:11:05.429 align:start position:0%
just read about or that we just used
 

00:11:05.429 --> 00:11:08.069 align:start position:0%
just read about or that we just used
okay<00:11:06.429><c> so</c><00:11:06.629><c> hope</c><00:11:07.629><c> you</c><00:11:07.749><c> guys</c><00:11:07.839><c> learned</c><00:11:08.019><c> something</c>

00:11:08.069 --> 00:11:08.079 align:start position:0%
okay so hope you guys learned something
 

00:11:08.079 --> 00:11:12.749 align:start position:0%
okay so hope you guys learned something
and<00:11:08.529><c> I</c><00:11:09.519><c> will</c><00:11:10.209><c> see</c><00:11:10.389><c> you</c><00:11:10.449><c> next</c><00:11:10.660><c> time</c>

