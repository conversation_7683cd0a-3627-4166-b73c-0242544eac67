WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:01.750 align:start position:0%
 
you<00:00:00.199><c> may</c><00:00:00.359><c> not</c><00:00:00.560><c> realize</c><00:00:01.120><c> this</c><00:00:01.360><c> but</c><00:00:01.520><c> not</c>

00:00:01.750 --> 00:00:01.760 align:start position:0%
you may not realize this but not
 

00:00:01.760 --> 00:00:03.630 align:start position:0%
you may not realize this but not
everything<00:00:02.200><c> you</c><00:00:02.360><c> read</c><00:00:02.639><c> on</c><00:00:02.800><c> the</c><00:00:02.960><c> internet</c><00:00:03.399><c> is</c>

00:00:03.630 --> 00:00:03.640 align:start position:0%
everything you read on the internet is
 

00:00:03.640 --> 00:00:07.269 align:start position:0%
everything you read on the internet is
true<00:00:04.640><c> what</c><00:00:05.319><c> I</c><00:00:05.440><c> am</c><00:00:05.720><c> shocked</c><00:00:06.359><c> shocked</c><00:00:06.919><c> I</c><00:00:07.040><c> tell</c>

00:00:07.269 --> 00:00:07.279 align:start position:0%
true what I am shocked shocked I tell
 

00:00:07.279 --> 00:00:09.790 align:start position:0%
true what I am shocked shocked I tell
you<00:00:07.759><c> when</c><00:00:07.919><c> I</c><00:00:08.080><c> ask</c><00:00:08.280><c> grock's</c><00:00:08.679><c> llama</c><00:00:09.000><c> 3</c><00:00:09.320><c> Model</c><00:00:09.639><c> to</c>

00:00:09.790 --> 00:00:09.800 align:start position:0%
you when I ask grock's llama 3 Model to
 

00:00:09.800 --> 00:00:11.430 align:start position:0%
you when I ask grock's llama 3 Model to
tell<00:00:09.960><c> me</c><00:00:10.120><c> what</c><00:00:10.240><c> the</c><00:00:10.360><c> weather</c><00:00:10.679><c> is</c><00:00:10.880><c> in</c><00:00:11.040><c> Fargo</c>

00:00:11.430 --> 00:00:11.440 align:start position:0%
tell me what the weather is in Fargo
 

00:00:11.440 --> 00:00:14.629 align:start position:0%
tell me what the weather is in Fargo
North<00:00:11.719><c> Dakota</c><00:00:12.480><c> it</c><00:00:12.679><c> does</c><00:00:13.160><c> give</c><00:00:13.280><c> me</c><00:00:13.440><c> the</c><00:00:13.639><c> details</c>

00:00:14.629 --> 00:00:14.639 align:start position:0%
North Dakota it does give me the details
 

00:00:14.639 --> 00:00:17.390 align:start position:0%
North Dakota it does give me the details
but<00:00:14.799><c> grock's</c><00:00:15.200><c> answer</c><00:00:15.519><c> is</c><00:00:15.639><c> a</c><00:00:15.799><c> big</c><00:00:16.119><c> fat</c><00:00:16.400><c> lie</c>

00:00:17.390 --> 00:00:17.400 align:start position:0%
but grock's answer is a big fat lie
 

00:00:17.400 --> 00:00:19.070 align:start position:0%
but grock's answer is a big fat lie
that's<00:00:17.600><c> because</c><00:00:17.920><c> our</c><00:00:18.199><c> temperature</c><00:00:18.640><c> setting</c>

00:00:19.070 --> 00:00:19.080 align:start position:0%
that's because our temperature setting
 

00:00:19.080 --> 00:00:21.830 align:start position:0%
that's because our temperature setting
is<00:00:19.199><c> at</c><00:00:19.439><c> 1</c><00:00:20.240><c> which</c><00:00:20.400><c> allows</c><00:00:20.800><c> the</c><00:00:20.960><c> llm</c><00:00:21.439><c> to</c><00:00:21.600><c> be</c>

00:00:21.830 --> 00:00:21.840 align:start position:0%
is at 1 which allows the llm to be
 

00:00:21.840 --> 00:00:24.189 align:start position:0%
is at 1 which allows the llm to be
imaginative<00:00:22.640><c> with</c><00:00:22.800><c> its</c><00:00:23.000><c> answer</c><00:00:23.760><c> when</c><00:00:23.880><c> I</c><00:00:24.039><c> set</c>

00:00:24.189 --> 00:00:24.199 align:start position:0%
imaginative with its answer when I set
 

00:00:24.199 --> 00:00:26.910 align:start position:0%
imaginative with its answer when I set
it<00:00:24.279><c> to</c><00:00:24.519><c> zero</c><00:00:25.199><c> we</c><00:00:25.359><c> get</c><00:00:25.480><c> an</c><00:00:25.599><c> honest</c><00:00:25.960><c> response</c>

00:00:26.910 --> 00:00:26.920 align:start position:0%
it to zero we get an honest response
 

00:00:26.920 --> 00:00:28.710 align:start position:0%
it to zero we get an honest response
llms<00:00:27.480><c> have</c><00:00:27.599><c> no</c><00:00:27.760><c> freaking</c><00:00:28.199><c> idea</c><00:00:28.480><c> what</c><00:00:28.599><c> the</c>

00:00:28.710 --> 00:00:28.720 align:start position:0%
llms have no freaking idea what the
 

00:00:28.720 --> 00:00:30.509 align:start position:0%
llms have no freaking idea what the
current<00:00:29.000><c> weather</c><00:00:29.359><c> conditions</c><00:00:29.640><c> are</c>

00:00:30.509 --> 00:00:30.519 align:start position:0%
current weather conditions are
 

00:00:30.519 --> 00:00:31.990 align:start position:0%
current weather conditions are
that's<00:00:30.640><c> a</c><00:00:30.880><c> problem</c><00:00:31.160><c> when</c><00:00:31.279><c> you</c><00:00:31.400><c> want</c><00:00:31.519><c> to</c><00:00:31.679><c> create</c>

00:00:31.990 --> 00:00:32.000 align:start position:0%
that's a problem when you want to create
 

00:00:32.000 --> 00:00:34.950 align:start position:0%
that's a problem when you want to create
an<00:00:32.239><c> AI</c><00:00:32.719><c> agent</c><00:00:33.079><c> with</c><00:00:33.239><c> any</c><00:00:33.399><c> sort</c><00:00:33.600><c> of</c><00:00:33.960><c> expertise</c>

00:00:34.950 --> 00:00:34.960 align:start position:0%
an AI agent with any sort of expertise
 

00:00:34.960 --> 00:00:36.630 align:start position:0%
an AI agent with any sort of expertise
you<00:00:35.079><c> want</c><00:00:35.239><c> to</c><00:00:35.360><c> be</c><00:00:35.559><c> confident</c><00:00:36.040><c> the</c><00:00:36.160><c> answers</c><00:00:36.520><c> you</c>

00:00:36.630 --> 00:00:36.640 align:start position:0%
you want to be confident the answers you
 

00:00:36.640 --> 00:00:39.270 align:start position:0%
you want to be confident the answers you
get<00:00:36.800><c> from</c><00:00:36.960><c> the</c><00:00:37.079><c> llm</c><00:00:37.680><c> aren't</c><00:00:38.120><c> just</c><00:00:38.360><c> made</c><00:00:38.559><c> up</c>

00:00:39.270 --> 00:00:39.280 align:start position:0%
get from the llm aren't just made up
 

00:00:39.280 --> 00:00:42.229 align:start position:0%
get from the llm aren't just made up
nonsense<00:00:40.280><c> and</c><00:00:41.000><c> if</c><00:00:41.200><c> AI</c><00:00:41.559><c> doesn't</c><00:00:41.840><c> know</c><00:00:42.079><c> the</c>

00:00:42.229 --> 00:00:42.239 align:start position:0%
nonsense and if AI doesn't know the
 

00:00:42.239 --> 00:00:44.510 align:start position:0%
nonsense and if AI doesn't know the
answer<00:00:42.800><c> you</c><00:00:42.960><c> want</c><00:00:43.120><c> it</c><00:00:43.239><c> to</c><00:00:43.360><c> be</c><00:00:43.600><c> proactive</c><00:00:44.320><c> and</c>

00:00:44.510 --> 00:00:44.520 align:start position:0%
answer you want it to be proactive and
 

00:00:44.520 --> 00:00:46.229 align:start position:0%
answer you want it to be proactive and
act<00:00:44.760><c> autonomously</c><00:00:45.440><c> to</c><00:00:45.600><c> go</c><00:00:45.719><c> find</c><00:00:46.079><c> the</c>

00:00:46.229 --> 00:00:46.239 align:start position:0%
act autonomously to go find the
 

00:00:46.239 --> 00:00:48.750 align:start position:0%
act autonomously to go find the
information<00:00:46.719><c> you</c><00:00:46.879><c> need</c><00:00:47.760><c> introducing</c><00:00:48.360><c> the</c><00:00:48.520><c> new</c>

00:00:48.750 --> 00:00:48.760 align:start position:0%
information you need introducing the new
 

00:00:48.760 --> 00:00:50.630 align:start position:0%
information you need introducing the new
autog<00:00:49.160><c> grock</c><00:00:49.399><c> agent</c><00:00:49.680><c> feature</c><00:00:50.160><c> available</c><00:00:50.520><c> in</c>

00:00:50.630 --> 00:00:50.640 align:start position:0%
autog grock agent feature available in
 

00:00:50.640 --> 00:00:53.229 align:start position:0%
autog grock agent feature available in
our<00:00:50.920><c> pocket</c><00:00:51.239><c> grock</c><00:00:51.640><c> Library</c><00:00:52.559><c> an</c><00:00:52.719><c> autog</c><00:00:53.000><c> gr</c>

00:00:53.229 --> 00:00:53.239 align:start position:0%
our pocket grock Library an autog gr
 

00:00:53.239 --> 00:00:55.150 align:start position:0%
our pocket grock Library an autog gr
agent<00:00:53.520><c> won't</c><00:00:53.719><c> lie</c><00:00:54.079><c> to</c><00:00:54.199><c> you</c><00:00:54.320><c> unless</c><00:00:54.600><c> you</c><00:00:54.760><c> ask</c><00:00:55.000><c> it</c>

00:00:55.150 --> 00:00:55.160 align:start position:0%
agent won't lie to you unless you ask it
 

00:00:55.160 --> 00:00:57.389 align:start position:0%
agent won't lie to you unless you ask it
to<00:00:55.680><c> better</c><00:00:55.960><c> still</c><00:00:56.320><c> if</c><00:00:56.399><c> your</c><00:00:56.559><c> autog</c><00:00:56.879><c> gr</c><00:00:57.120><c> agent</c>

00:00:57.389 --> 00:00:57.399 align:start position:0%
to better still if your autog gr agent
 

00:00:57.399 --> 00:00:59.150 align:start position:0%
to better still if your autog gr agent
doesn't<00:00:57.640><c> know</c><00:00:57.840><c> the</c><00:00:58.000><c> answer</c><00:00:58.440><c> it</c><00:00:58.519><c> will</c><00:00:58.719><c> scour</c>

00:00:59.150 --> 00:00:59.160 align:start position:0%
doesn't know the answer it will scour
 

00:00:59.160 --> 00:01:01.349 align:start position:0%
doesn't know the answer it will scour
the<00:00:59.239><c> internet</c><00:00:59.640><c> until</c><00:00:59.960><c> until</c><00:01:00.120><c> it</c><00:01:00.239><c> finds</c><00:01:00.519><c> out</c><00:01:01.280><c> if</c>

00:01:01.349 --> 00:01:01.359 align:start position:0%
the internet until until it finds out if
 

00:01:01.359 --> 00:01:03.110 align:start position:0%
the internet until until it finds out if
you're<00:01:01.559><c> used</c><00:01:01.760><c> to</c><00:01:01.920><c> grock</c><00:01:02.280><c> instantly</c><00:01:02.719><c> returning</c>

00:01:03.110 --> 00:01:03.120 align:start position:0%
you're used to grock instantly returning
 

00:01:03.120 --> 00:01:05.109 align:start position:0%
you're used to grock instantly returning
an<00:01:03.320><c> answer</c><00:01:04.000><c> this</c><00:01:04.199><c> delay</c><00:01:04.559><c> might</c><00:01:04.720><c> seem</c>

00:01:05.109 --> 00:01:05.119 align:start position:0%
an answer this delay might seem
 

00:01:05.119 --> 00:01:07.310 align:start position:0%
an answer this delay might seem
concerning<00:01:06.119><c> but</c><00:01:06.320><c> as</c><00:01:06.439><c> we'll</c><00:01:06.640><c> see</c><00:01:06.840><c> in</c><00:01:06.960><c> a</c><00:01:07.080><c> few</c>

00:01:07.310 --> 00:01:07.320 align:start position:0%
concerning but as we'll see in a few
 

00:01:07.320 --> 00:01:10.510 align:start position:0%
concerning but as we'll see in a few
seconds<00:01:08.080><c> our</c><00:01:08.320><c> agent</c><00:01:08.640><c> is</c><00:01:08.880><c> actually</c><00:01:09.240><c> very</c><00:01:09.520><c> busy</c>

00:01:10.510 --> 00:01:10.520 align:start position:0%
seconds our agent is actually very busy
 

00:01:10.520 --> 00:01:12.550 align:start position:0%
seconds our agent is actually very busy
this<00:01:10.799><c> running</c><00:01:11.280><c> notice</c><00:01:11.680><c> lets</c><00:01:11.920><c> us</c><00:01:12.119><c> know</c><00:01:12.320><c> we're</c>

00:01:12.550 --> 00:01:12.560 align:start position:0%
this running notice lets us know we're
 

00:01:12.560 --> 00:01:15.429 align:start position:0%
this running notice lets us know we're
making<00:01:13.000><c> progress</c><00:01:14.000><c> and</c><00:01:14.159><c> there's</c><00:01:14.400><c> our</c><00:01:14.680><c> answer</c>

00:01:15.429 --> 00:01:15.439 align:start position:0%
making progress and there's our answer
 

00:01:15.439 --> 00:01:17.630 align:start position:0%
making progress and there's our answer
remember<00:01:16.080><c> llms</c><00:01:16.759><c> don't</c><00:01:17.040><c> have</c><00:01:17.280><c> current</c>

00:01:17.630 --> 00:01:17.640 align:start position:0%
remember llms don't have current
 

00:01:17.640 --> 00:01:20.270 align:start position:0%
remember llms don't have current
knowledge<00:01:18.439><c> our</c><00:01:18.640><c> autog</c><00:01:19.000><c> gr</c><00:01:19.360><c> agent</c><00:01:19.840><c> didn't</c><00:01:20.079><c> know</c>

00:01:20.270 --> 00:01:20.280 align:start position:0%
knowledge our autog gr agent didn't know
 

00:01:20.280 --> 00:01:22.550 align:start position:0%
knowledge our autog gr agent didn't know
what<00:01:20.400><c> the</c><00:01:20.520><c> weather</c><00:01:20.840><c> was</c><00:01:21.040><c> in</c><00:01:21.200><c> Fargo</c><00:01:21.680><c> at</c><00:01:21.840><c> first</c>

00:01:22.550 --> 00:01:22.560 align:start position:0%
what the weather was in Fargo at first
 

00:01:22.560 --> 00:01:24.149 align:start position:0%
what the weather was in Fargo at first
but<00:01:22.720><c> it</c><00:01:22.799><c> went</c><00:01:23.000><c> through</c><00:01:23.200><c> a</c><00:01:23.360><c> self-learning</c>

00:01:24.149 --> 00:01:24.159 align:start position:0%
but it went through a self-learning
 

00:01:24.159 --> 00:01:27.190 align:start position:0%
but it went through a self-learning
process<00:01:24.479><c> to</c><00:01:24.720><c> find</c><00:01:24.960><c> out</c><00:01:25.799><c> let's</c><00:01:26.040><c> take</c><00:01:26.159><c> a</c><00:01:26.360><c> look</c><00:01:27.119><c> we</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
process to find out let's take a look we
 

00:01:27.200 --> 00:01:28.870 align:start position:0%
process to find out let's take a look we
can<00:01:27.320><c> see</c><00:01:27.520><c> in</c><00:01:27.720><c> step</c><00:01:28.000><c> two</c><00:01:28.320><c> that</c><00:01:28.479><c> the</c><00:01:28.600><c> first</c>

00:01:28.870 --> 00:01:28.880 align:start position:0%
can see in step two that the first
 

00:01:28.880 --> 00:01:31.350 align:start position:0%
can see in step two that the first
answer<00:01:29.200><c> was</c><00:01:29.360><c> the</c><00:01:29.479><c> standard</c><00:01:30.280><c> I'm</c><00:01:30.439><c> just</c><00:01:30.600><c> an</c><00:01:30.759><c> llm</c>

00:01:31.350 --> 00:01:31.360 align:start position:0%
answer was the standard I'm just an llm
 

00:01:31.360 --> 00:01:34.190 align:start position:0%
answer was the standard I'm just an llm
I<00:01:31.479><c> know</c><00:01:31.720><c> nothing</c><00:01:32.560><c> in</c><00:01:32.759><c> step</c><00:01:33.040><c> three</c><00:01:33.560><c> our</c><00:01:33.840><c> agent</c>

00:01:34.190 --> 00:01:34.200 align:start position:0%
I know nothing in step three our agent
 

00:01:34.200 --> 00:01:35.950 align:start position:0%
I know nothing in step three our agent
applies<00:01:34.520><c> some</c><00:01:34.720><c> introspection</c><00:01:35.360><c> and</c><00:01:35.520><c> decides</c>

00:01:35.950 --> 00:01:35.960 align:start position:0%
applies some introspection and decides
 

00:01:35.960 --> 00:01:38.310 align:start position:0%
applies some introspection and decides
that<00:01:36.240><c> I</c><00:01:36.360><c> don't</c><00:01:36.600><c> know</c><00:01:37.040><c> is</c><00:01:37.200><c> an</c><00:01:37.360><c> unsatisfactory</c>

00:01:38.310 --> 00:01:38.320 align:start position:0%
that I don't know is an unsatisfactory
 

00:01:38.320 --> 00:01:40.950 align:start position:0%
that I don't know is an unsatisfactory
answer<00:01:39.200><c> next</c><00:01:39.520><c> it</c><00:01:39.680><c> generates</c><00:01:40.079><c> a</c><00:01:40.240><c> query</c><00:01:40.680><c> and</c>

00:01:40.950 --> 00:01:40.960 align:start position:0%
answer next it generates a query and
 

00:01:40.960 --> 00:01:43.469 align:start position:0%
answer next it generates a query and
executes<00:01:41.399><c> a</c><00:01:41.560><c> web</c><00:01:41.840><c> search</c><00:01:42.280><c> finding</c><00:01:42.680><c> 10</c><00:01:43.040><c> sources</c>

00:01:43.469 --> 00:01:43.479 align:start position:0%
executes a web search finding 10 sources
 

00:01:43.479 --> 00:01:46.069 align:start position:0%
executes a web search finding 10 sources
of<00:01:43.799><c> information</c><00:01:44.799><c> our</c><00:01:45.079><c> agent</c><00:01:45.360><c> will</c><00:01:45.560><c> reference</c>

00:01:46.069 --> 00:01:46.079 align:start position:0%
of information our agent will reference
 

00:01:46.079 --> 00:01:48.270 align:start position:0%
of information our agent will reference
those<00:01:46.240><c> sources</c><00:01:46.759><c> one</c><00:01:47.000><c> by</c><00:01:47.200><c> one</c><00:01:47.640><c> until</c><00:01:47.840><c> it</c><00:01:48.000><c> finds</c>

00:01:48.270 --> 00:01:48.280 align:start position:0%
those sources one by one until it finds
 

00:01:48.280 --> 00:01:50.550 align:start position:0%
those sources one by one until it finds
an<00:01:48.479><c> answer</c><00:01:48.799><c> that</c><00:01:48.920><c> it's</c><00:01:49.159><c> happy</c><00:01:49.479><c> with</c><00:01:50.240><c> now</c><00:01:50.439><c> we</c>

00:01:50.550 --> 00:01:50.560 align:start position:0%
an answer that it's happy with now we
 

00:01:50.560 --> 00:01:52.950 align:start position:0%
an answer that it's happy with now we
can<00:01:50.759><c> appreciate</c><00:01:51.479><c> why</c><00:01:51.680><c> it</c><00:01:51.880><c> took</c><00:01:52.079><c> a</c><00:01:52.240><c> few</c><00:01:52.520><c> seconds</c>

00:01:52.950 --> 00:01:52.960 align:start position:0%
can appreciate why it took a few seconds
 

00:01:52.960 --> 00:01:55.630 align:start position:0%
can appreciate why it took a few seconds
to<00:01:53.119><c> get</c><00:01:53.280><c> our</c><00:01:53.600><c> answer</c><00:01:54.600><c> if</c><00:01:54.759><c> we</c><00:01:54.920><c> skip</c><00:01:55.240><c> ahead</c><00:01:55.439><c> to</c>

00:01:55.630 --> 00:01:55.640 align:start position:0%
to get our answer if we skip ahead to
 

00:01:55.640 --> 00:01:59.230 align:start position:0%
to get our answer if we skip ahead to
step<00:01:56.280><c> 16</c><00:01:57.280><c> we</c><00:01:57.479><c> find</c><00:01:57.759><c> something</c><00:01:58.240><c> interesting</c>

00:01:59.230 --> 00:01:59.240 align:start position:0%
step 16 we find something interesting
 

00:01:59.240 --> 00:02:01.709 align:start position:0%
step 16 we find something interesting
while<00:01:59.399><c> our</c><00:01:59.560><c> agent</c><00:02:00.240><c> did</c><00:02:00.840><c> find</c><00:02:01.240><c> weather</c>

00:02:01.709 --> 00:02:01.719 align:start position:0%
while our agent did find weather
 

00:02:01.719 --> 00:02:03.590 align:start position:0%
while our agent did find weather
conditions<00:02:02.039><c> at</c><00:02:02.240><c> that</c><00:02:02.439><c> Source</c><00:02:03.399><c> the</c>

00:02:03.590 --> 00:02:03.600 align:start position:0%
conditions at that Source the
 

00:02:03.600 --> 00:02:05.910 align:start position:0%
conditions at that Source the
information<00:02:04.240><c> didn't</c><00:02:04.600><c> appear</c><00:02:04.960><c> reliable</c><00:02:05.560><c> upon</c>

00:02:05.910 --> 00:02:05.920 align:start position:0%
information didn't appear reliable upon
 

00:02:05.920 --> 00:02:08.910 align:start position:0%
information didn't appear reliable upon
review<00:02:06.719><c> so</c><00:02:06.920><c> it</c><00:02:07.119><c> kept</c><00:02:07.399><c> looking</c><00:02:08.319><c> good</c><00:02:08.560><c> thing</c><00:02:08.720><c> it</c>

00:02:08.910 --> 00:02:08.920 align:start position:0%
review so it kept looking good thing it
 

00:02:08.920 --> 00:02:11.910 align:start position:0%
review so it kept looking good thing it
did<00:02:09.679><c> that</c><00:02:09.920><c> weather</c><00:02:10.280><c> information</c><00:02:10.879><c> was</c><00:02:11.360><c> not</c>

00:02:11.910 --> 00:02:11.920 align:start position:0%
did that weather information was not
 

00:02:11.920 --> 00:02:14.869 align:start position:0%
did that weather information was not
correct<00:02:12.920><c> by</c><00:02:13.080><c> step</c><00:02:13.319><c> 20</c><00:02:13.840><c> our</c><00:02:14.080><c> agent</c><00:02:14.440><c> has</c><00:02:14.560><c> found</c>

00:02:14.869 --> 00:02:14.879 align:start position:0%
correct by step 20 our agent has found
 

00:02:14.879 --> 00:02:16.670 align:start position:0%
correct by step 20 our agent has found
information<00:02:15.440><c> that</c><00:02:15.560><c> it</c><00:02:15.680><c> has</c><00:02:16.000><c> confidence</c><00:02:16.480><c> in</c>

00:02:16.670 --> 00:02:16.680 align:start position:0%
information that it has confidence in
 

00:02:16.680 --> 00:02:19.949 align:start position:0%
information that it has confidence in
presenting<00:02:17.239><c> as</c><00:02:17.480><c> the</c><00:02:17.640><c> correct</c><00:02:18.120><c> answer</c><00:02:19.120><c> and</c><00:02:19.319><c> so</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
presenting as the correct answer and so
 

00:02:19.959 --> 00:02:22.990 align:start position:0%
presenting as the correct answer and so
it<00:02:20.160><c> does</c><00:02:21.120><c> we'll</c><00:02:21.360><c> do</c><00:02:21.519><c> one</c><00:02:21.680><c> more</c><00:02:22.000><c> demonstration</c>

00:02:22.990 --> 00:02:23.000 align:start position:0%
it does we'll do one more demonstration
 

00:02:23.000 --> 00:02:25.509 align:start position:0%
it does we'll do one more demonstration
viewers<00:02:23.440><c> in</c><00:02:23.599><c> Arizona</c><00:02:24.599><c> you</c><00:02:24.800><c> may</c><00:02:25.000><c> want</c><00:02:25.160><c> to</c><00:02:25.319><c> look</c>

00:02:25.509 --> 00:02:25.519 align:start position:0%
viewers in Arizona you may want to look
 

00:02:25.519 --> 00:02:29.990 align:start position:0%
viewers in Arizona you may want to look
away<00:02:25.720><c> from</c><00:02:25.879><c> your</c><00:02:26.080><c> screen</c>

00:02:29.990 --> 00:02:30.000 align:start position:0%
 
 

00:02:30.000 --> 00:02:32.509 align:start position:0%
 
go<00:02:30.400><c> back</c><00:02:30.879><c> go</c><00:02:31.519><c> I</c><00:02:31.560><c> don't</c><00:02:31.760><c> really</c><00:02:31.959><c> know</c><00:02:32.200><c> anything</c>

00:02:32.509 --> 00:02:32.519 align:start position:0%
go back go I don't really know anything
 

00:02:32.519 --> 00:02:34.830 align:start position:0%
go back go I don't really know anything
about<00:02:32.800><c> football</c><00:02:33.680><c> apparently</c><00:02:34.200><c> neither</c><00:02:34.519><c> do</c><00:02:34.720><c> the</c>

00:02:34.830 --> 00:02:34.840 align:start position:0%
about football apparently neither do the
 

00:02:34.840 --> 00:02:35.910 align:start position:0%
about football apparently neither do the
Arizona

00:02:35.910 --> 00:02:35.920 align:start position:0%
Arizona
 

00:02:35.920 --> 00:02:39.589 align:start position:0%
Arizona
Cardinals<00:02:36.959><c> your</c><00:02:37.959><c> cool</c><00:02:38.280><c> take</c><00:02:38.519><c> a</c><00:02:38.800><c> hike</c>

00:02:39.589 --> 00:02:39.599 align:start position:0%
Cardinals your cool take a hike
 

00:02:39.599 --> 00:02:43.309 align:start position:0%
Cardinals your cool take a hike
otherwise<00:02:40.319><c> subscribe</c><00:02:40.959><c> and</c><00:02:41.280><c> like</c><00:02:42.000><c> to</c><00:02:42.440><c> AI</c><00:02:42.959><c> tips</c>

00:02:43.309 --> 00:02:43.319 align:start position:0%
otherwise subscribe and like to AI tips
 

00:02:43.319 --> 00:02:44.990 align:start position:0%
otherwise subscribe and like to AI tips
with

00:02:44.990 --> 00:02:45.000 align:start position:0%
with
 

00:02:45.000 --> 00:02:50.630 align:start position:0%
with
J<00:02:46.000><c> AI</c><00:02:46.440><c> tips</c><00:02:46.840><c> with</c><00:02:47.040><c> J</c><00:02:47.800><c> hooray</c><00:02:48.319><c> a</c><00:02:48.519><c> i</c><00:02:48.840><c> a</c><00:02:49.080><c> i</c><00:02:49.400><c> a</c><00:02:49.560><c> i</c><00:02:49.879><c> a</c><00:02:50.120><c> i</c>

00:02:50.630 --> 00:02:50.640 align:start position:0%
J AI tips with J hooray a i a i a i a i
 

00:02:50.640 --> 00:02:52.670 align:start position:0%
J AI tips with J hooray a i a i a i a i
AI<00:02:51.120><c> tips</c><00:02:51.480><c> with</c>

00:02:52.670 --> 00:02:52.680 align:start position:0%
AI tips with
 

00:02:52.680 --> 00:02:55.390 align:start position:0%
AI tips with
j<00:02:53.680><c> a</c><00:02:53.840><c> i</c><00:02:54.000><c> tips</c><00:02:54.200><c> with</c><00:02:54.360><c> J</c><00:02:54.560><c> is</c><00:02:54.680><c> a</c><00:02:54.840><c> copyrighted</c>

00:02:55.390 --> 00:02:55.400 align:start position:0%
j a i tips with J is a copyrighted
 

00:02:55.400 --> 00:02:56.630 align:start position:0%
j a i tips with J is a copyrighted
production<00:02:55.800><c> of</c>

00:02:56.630 --> 00:02:56.640 align:start position:0%
production of
 

00:02:56.640 --> 00:03:00.790 align:start position:0%
production of
j.g.<00:02:57.640><c> us</c><00:02:58.440><c> all</c><00:02:58.640><c> rights</c><00:02:58.920><c> reserved</c><00:02:59.400><c> by</c><00:02:59.920><c> AI</c><00:03:00.400><c> tips</c>

00:03:00.790 --> 00:03:00.800 align:start position:0%
j.g. us all rights reserved by AI tips
 

00:03:00.800 --> 00:03:04.000 align:start position:0%
j.g. us all rights reserved by AI tips
with<00:03:01.000><c> j</c>

