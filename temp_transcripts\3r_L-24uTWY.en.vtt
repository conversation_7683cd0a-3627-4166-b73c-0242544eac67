WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.600 align:start position:0%
 
that<00:00:00.269><c> so</c><00:00:00.599><c> in</c><00:00:00.930><c> order</c><00:00:01.140><c> to</c><00:00:01.350><c> push</c><00:00:01.680><c> to</c><00:00:01.920><c> Hero<PERSON></c><00:00:02.220><c> we</c>

00:00:02.600 --> 00:00:02.610 align:start position:0%
that so in order to push to Heroku we
 

00:00:02.610 --> 00:00:04.789 align:start position:0%
that so in order to push to Heroku we
realized<00:00:02.970><c> that</c><00:00:03.179><c> we</c><00:00:03.300><c> needed</c><00:00:03.510><c> to</c><00:00:03.870><c> have</c><00:00:04.259><c> all</c><00:00:04.620><c> of</c>

00:00:04.789 --> 00:00:04.799 align:start position:0%
realized that we needed to have all of
 

00:00:04.799 --> 00:00:08.780 align:start position:0%
realized that we needed to have all of
our<00:00:04.950><c> changes</c><00:00:05.339><c> that</c><00:00:05.910><c> we</c><00:00:06.029><c> want</c><00:00:06.270><c> in</c><00:00:06.540><c> the</c><00:00:06.870><c> in</c><00:00:07.790><c> the</c>

00:00:08.780 --> 00:00:08.790 align:start position:0%
our changes that we want in the in the
 

00:00:08.790 --> 00:00:12.110 align:start position:0%
our changes that we want in the in the
master<00:00:09.210><c> branch</c><00:00:09.420><c> of</c><00:00:09.750><c> our</c><00:00:09.900><c> local</c><00:00:10.380><c> repo</c><00:00:10.650><c> now</c><00:00:11.550><c> in</c>

00:00:12.110 --> 00:00:12.120 align:start position:0%
master branch of our local repo now in
 

00:00:12.120 --> 00:00:15.709 align:start position:0%
master branch of our local repo now in
order<00:00:12.389><c> to</c><00:00:12.719><c> grab</c><00:00:13.410><c> all</c><00:00:13.710><c> all</c><00:00:14.700><c> those</c><00:00:15.030><c> changes</c><00:00:15.690><c> that</c>

00:00:15.709 --> 00:00:15.719 align:start position:0%
order to grab all all those changes that
 

00:00:15.719 --> 00:00:20.000 align:start position:0%
order to grab all all those changes that
we<00:00:16.080><c> made</c><00:00:16.289><c> in</c><00:00:16.440><c> our</c><00:00:16.529><c> new</c><00:00:16.800><c> branch</c><00:00:18.289><c> we</c><00:00:19.289><c> put</c><00:00:19.560><c> we</c><00:00:19.770><c> put</c>

00:00:20.000 --> 00:00:20.010 align:start position:0%
we made in our new branch we put we put
 

00:00:20.010 --> 00:00:22.700 align:start position:0%
we made in our new branch we put we put
this<00:00:20.220><c> this</c><00:00:20.640><c> command</c><00:00:21.029><c> git</c><00:00:21.300><c> pull</c><00:00:21.600><c> origin</c><00:00:21.900><c> name</c>

00:00:22.700 --> 00:00:22.710 align:start position:0%
this this command git pull origin name
 

00:00:22.710 --> 00:00:24.859 align:start position:0%
this this command git pull origin name
of<00:00:22.890><c> branch</c><00:00:23.189><c> editing</c><00:00:23.640><c> admin</c><00:00:24.000><c> tab</c><00:00:24.210><c> and</c><00:00:24.720><c> now</c>

00:00:24.859 --> 00:00:24.869 align:start position:0%
of branch editing admin tab and now
 

00:00:24.869 --> 00:00:27.349 align:start position:0%
of branch editing admin tab and now
we're<00:00:25.019><c> gonna</c><00:00:25.109><c> go</c><00:00:25.350><c> into</c><00:00:25.500><c> our</c><00:00:25.650><c> actual</c><00:00:26.070><c> repo</c><00:00:26.609><c> and</c>

00:00:27.349 --> 00:00:27.359 align:start position:0%
we're gonna go into our actual repo and
 

00:00:27.359 --> 00:00:29.779 align:start position:0%
we're gonna go into our actual repo and
we<00:00:27.510><c> can</c><00:00:27.660><c> see</c><00:00:27.840><c> that</c><00:00:27.869><c> all</c><00:00:28.199><c> that</c><00:00:28.500><c> text</c><00:00:29.070><c> that</c><00:00:29.580><c> was</c>

00:00:29.779 --> 00:00:29.789 align:start position:0%
we can see that all that text that was
 

00:00:29.789 --> 00:00:32.060 align:start position:0%
we can see that all that text that was
there<00:00:30.000><c> a</c><00:00:30.029><c> second</c><00:00:30.510><c> ago</c><00:00:30.539><c> is</c><00:00:30.840><c> now</c><00:00:31.050><c> gone</c><00:00:31.349><c> so</c><00:00:31.650><c> every</c>

00:00:32.060 --> 00:00:32.070 align:start position:0%
there a second ago is now gone so every
 

00:00:32.070 --> 00:00:35.240 align:start position:0%
there a second ago is now gone so every
time<00:00:32.250><c> that</c><00:00:32.399><c> you</c><00:00:32.579><c> make</c><00:00:32.820><c> changes</c><00:00:33.420><c> to</c><00:00:33.480><c> you</c><00:00:34.350><c> to</c>

00:00:35.240 --> 00:00:35.250 align:start position:0%
time that you make changes to you to
 

00:00:35.250 --> 00:00:38.060 align:start position:0%
time that you make changes to you to
your<00:00:35.760><c> github</c><00:00:36.270><c> repos</c><00:00:37.260><c> and</c><00:00:37.559><c> your</c><00:00:37.800><c> github</c>

00:00:38.060 --> 00:00:38.070 align:start position:0%
your github repos and your github
 

00:00:38.070 --> 00:00:40.569 align:start position:0%
your github repos and your github
branches<00:00:38.760><c> it's</c><00:00:39.030><c> automatically</c><00:00:39.750><c> gonna</c><00:00:40.079><c> change</c>

00:00:40.569 --> 00:00:40.579 align:start position:0%
branches it's automatically gonna change
 

00:00:40.579 --> 00:00:44.750 align:start position:0%
branches it's automatically gonna change
the<00:00:41.579><c> files</c><00:00:41.910><c> within</c><00:00:42.660><c> that</c><00:00:43.170><c> specific</c><00:00:44.010><c> directory</c>

00:00:44.750 --> 00:00:44.760 align:start position:0%
the files within that specific directory
 

00:00:44.760 --> 00:00:47.330 align:start position:0%
the files within that specific directory
so<00:00:45.270><c> hopefully</c><00:00:45.690><c> that</c><00:00:46.379><c> makes</c><00:00:46.410><c> some</c><00:00:46.770><c> chain</c><00:00:47.070><c> that</c>

00:00:47.330 --> 00:00:47.340 align:start position:0%
so hopefully that makes some chain that
 

00:00:47.340 --> 00:00:50.979 align:start position:0%
so hopefully that makes some chain that
makes<00:00:47.579><c> some</c><00:00:47.789><c> sense</c><00:00:48.000><c> and</c><00:00:48.239><c> I'll</c><00:00:48.690><c> even</c><00:00:48.840><c> I'll</c><00:00:49.469><c> even</c>

00:00:50.979 --> 00:00:50.989 align:start position:0%
makes some sense and I'll even I'll even
 

00:00:50.989 --> 00:00:53.810 align:start position:0%
makes some sense and I'll even I'll even
push<00:00:51.989><c> the</c><00:00:52.020><c> point</c><00:00:52.410><c> home</c><00:00:52.649><c> a</c><00:00:53.010><c> little</c><00:00:53.340><c> bit</c><00:00:53.460><c> more</c><00:00:53.699><c> if</c>

00:00:53.810 --> 00:00:53.820 align:start position:0%
push the point home a little bit more if
 

00:00:53.820 --> 00:00:57.979 align:start position:0%
push the point home a little bit more if
I<00:00:53.940><c> do</c><00:00:54.000><c> get</c><00:00:54.300><c> branch</c><00:00:54.690><c> right</c><00:00:54.989><c> now</c><00:00:55.230><c> I</c><00:00:56.780><c> see</c><00:00:57.780><c> that</c><00:00:57.960><c> I</c>

00:00:57.979 --> 00:00:57.989 align:start position:0%
I do get branch right now I see that I
 

00:00:57.989 --> 00:01:00.470 align:start position:0%
I do get branch right now I see that I
have<00:00:58.079><c> a</c><00:00:58.379><c> branch</c><00:00:58.710><c> called</c><00:00:58.920><c> nicer</c><00:00:59.430><c> design</c><00:00:59.640><c> okay</c>

00:01:00.470 --> 00:01:00.480 align:start position:0%
have a branch called nicer design okay
 

00:01:00.480 --> 00:01:04.870 align:start position:0%
have a branch called nicer design okay
and<00:01:00.719><c> if</c><00:01:01.680><c> I</c><00:01:01.890><c> go</c><00:01:02.430><c> to</c><00:01:02.489><c> that</c><00:01:02.699><c> branch</c><00:01:03.180><c> get</c><00:01:03.750><c> checkout</c>

00:01:04.870 --> 00:01:04.880 align:start position:0%
and if I go to that branch get checkout
 

00:01:04.880 --> 00:01:09.590 align:start position:0%
and if I go to that branch get checkout
nicer<00:01:05.909><c> -</c><00:01:06.360><c> design</c><00:01:07.110><c> okay</c><00:01:07.680><c> have</c><00:01:08.640><c> a</c><00:01:08.760><c> look</c><00:01:08.790><c> at</c><00:01:09.030><c> what</c>

00:01:09.590 --> 00:01:09.600 align:start position:0%
nicer - design okay have a look at what
 

00:01:09.600 --> 00:01:12.950 align:start position:0%
nicer - design okay have a look at what
the<00:01:09.840><c> page</c><00:01:10.110><c> currently</c><00:01:10.830><c> looks</c><00:01:11.310><c> like</c><00:01:11.549><c> here</c><00:01:11.880><c> oh</c><00:01:12.030><c> no</c>

00:01:12.950 --> 00:01:12.960 align:start position:0%
the page currently looks like here oh no
 

00:01:12.960 --> 00:01:14.719 align:start position:0%
the page currently looks like here oh no
not<00:01:13.290><c> this</c><00:01:13.470><c> one</c><00:01:13.710><c> this</c><00:01:14.040><c> is</c><00:01:14.100><c> what</c><00:01:14.460><c> the</c><00:01:14.549><c> page</c>

00:01:14.719 --> 00:01:14.729 align:start position:0%
not this one this is what the page
 

00:01:14.729 --> 00:01:17.539 align:start position:0%
not this one this is what the page
currently<00:01:15.000><c> looks</c><00:01:15.390><c> like</c><00:01:15.600><c> on</c><00:01:15.840><c> the</c><00:01:16.500><c> master</c><00:01:16.979><c> on</c>

00:01:17.539 --> 00:01:17.549 align:start position:0%
currently looks like on the master on
 

00:01:17.549 --> 00:01:21.320 align:start position:0%
currently looks like on the master on
the<00:01:17.729><c> master</c><00:01:18.180><c> branch</c><00:01:18.509><c> of</c><00:01:18.990><c> localhost</c><00:01:19.920><c> okay</c><00:01:20.460><c> now</c>

00:01:21.320 --> 00:01:21.330 align:start position:0%
the master branch of localhost okay now
 

00:01:21.330 --> 00:01:25.240 align:start position:0%
the master branch of localhost okay now
if<00:01:21.720><c> I</c><00:01:21.900><c> go</c><00:01:22.080><c> to</c><00:01:22.110><c> get</c><00:01:22.530><c> checkout</c><00:01:22.950><c> nicer</c><00:01:23.369><c> design</c>

00:01:25.240 --> 00:01:25.250 align:start position:0%
if I go to get checkout nicer design
 

00:01:25.250 --> 00:01:27.980 align:start position:0%
if I go to get checkout nicer design
switch<00:01:26.250><c> to</c><00:01:26.490><c> branch</c><00:01:26.759><c> and</c><00:01:27.360><c> have</c><00:01:27.479><c> a</c><00:01:27.570><c> look</c><00:01:27.720><c> at</c><00:01:27.840><c> this</c>

00:01:27.980 --> 00:01:27.990 align:start position:0%
switch to branch and have a look at this
 

00:01:27.990 --> 00:01:30.109 align:start position:0%
switch to branch and have a look at this
all<00:01:28.290><c> of</c><00:01:28.320><c> a</c><00:01:28.439><c> sudden</c><00:01:28.799><c> I</c><00:01:29.040><c> got</c><00:01:29.579><c> all</c><00:01:29.790><c> this</c><00:01:29.970><c> other</c>

00:01:30.109 --> 00:01:30.119 align:start position:0%
all of a sudden I got all this other
 

00:01:30.119 --> 00:01:32.899 align:start position:0%
all of a sudden I got all this other
junk<00:01:30.689><c> back</c><00:01:31.200><c> okay</c><00:01:31.979><c> the</c><00:01:32.189><c> the</c><00:01:32.220><c> stuff</c><00:01:32.700><c> that</c><00:01:32.880><c> I</c>

00:01:32.899 --> 00:01:32.909 align:start position:0%
junk back okay the the stuff that I
 

00:01:32.909 --> 00:01:34.940 align:start position:0%
junk back okay the the stuff that I
didn't<00:01:33.240><c> want</c><00:01:33.770><c> okay</c>

00:01:34.940 --> 00:01:34.950 align:start position:0%
didn't want okay
 

00:01:34.950 --> 00:01:40.190 align:start position:0%
didn't want okay
I<00:01:34.979><c> dunno</c><00:01:35.280><c> it</c><00:01:35.549><c> man</c><00:01:36.180><c> I</c><00:01:36.470><c> start</c><00:01:37.470><c> up</c><00:01:37.590><c> my</c><00:01:37.770><c> server</c><00:01:39.200><c> it</c>

00:01:40.190 --> 00:01:40.200 align:start position:0%
I dunno it man I start up my server it
 

00:01:40.200 --> 00:01:53.960 align:start position:0%
I dunno it man I start up my server it
is<00:01:48.950><c> Oh</c><00:01:52.220><c> have</c><00:01:53.220><c> a</c><00:01:53.280><c> look</c><00:01:53.399><c> at</c><00:01:53.549><c> that</c><00:01:53.670><c> all</c><00:01:53.850><c> of</c><00:01:53.909><c> a</c>

00:01:53.960 --> 00:01:53.970 align:start position:0%
is Oh have a look at that all of a
 

00:01:53.970 --> 00:01:56.120 align:start position:0%
is Oh have a look at that all of a
sudden<00:01:54.180><c> is</c><00:01:54.270><c> listening</c><00:01:54.659><c> on</c><00:01:54.780><c> port</c><00:01:54.950><c> 8000</c><00:01:55.950><c> now</c>

00:01:56.120 --> 00:01:56.130 align:start position:0%
sudden is listening on port 8000 now
 

00:01:56.130 --> 00:01:59.139 align:start position:0%
sudden is listening on port 8000 now
alright<00:01:56.549><c> because</c><00:01:56.729><c> there</c><00:01:57.060><c> was</c><00:01:57.210><c> some</c><00:01:57.619><c> changes</c>

00:01:59.139 --> 00:01:59.149 align:start position:0%
alright because there was some changes
 

00:01:59.149 --> 00:02:01.280 align:start position:0%
alright because there was some changes
okay<00:02:00.149><c> so</c><00:02:00.210><c> now</c><00:02:00.420><c> I</c><00:02:00.450><c> actually</c><00:02:00.719><c> need</c><00:02:01.049><c> to</c><00:02:01.140><c> go</c><00:02:01.229><c> to</c>

00:02:01.280 --> 00:02:01.290 align:start position:0%
okay so now I actually need to go to
 

00:02:01.290 --> 00:02:15.270 align:start position:0%
okay so now I actually need to go to
localhost<00:02:01.670><c> 8002</c><00:02:02.670><c> get</c><00:02:02.820><c> back</c><00:02:03.030><c> to</c><00:02:03.210><c> my</c><00:02:03.329><c> app</c><00:02:04.520><c> okay</c>

00:02:15.270 --> 00:02:15.280 align:start position:0%
 
 

00:02:15.280 --> 00:02:17.289 align:start position:0%
 
something<00:02:16.280><c> here</c><00:02:16.400><c> doesn't</c><00:02:16.430><c> even</c><00:02:16.819><c> work</c><00:02:17.120><c> on</c>

00:02:17.289 --> 00:02:17.299 align:start position:0%
something here doesn't even work on
 

00:02:17.299 --> 00:02:24.339 align:start position:0%
something here doesn't even work on
localhost<00:02:17.870><c> 8080</c><00:02:21.790><c> so</c><00:02:22.790><c> bottom</c><00:02:23.780><c> line</c><00:02:23.900><c> is</c><00:02:24.019><c> that</c>

00:02:24.339 --> 00:02:24.349 align:start position:0%
localhost 8080 so bottom line is that
 

00:02:24.349 --> 00:02:27.670 align:start position:0%
localhost 8080 so bottom line is that
it's<00:02:24.530><c> not</c><00:02:24.920><c> very</c><00:02:25.190><c> good</c><00:02:25.690><c> slash</c><00:02:26.690><c> home</c><00:02:26.930><c> slash</c><00:02:26.989><c> list</c>

00:02:27.670 --> 00:02:27.680 align:start position:0%
it's not very good slash home slash list
 

00:02:27.680 --> 00:02:30.250 align:start position:0%
it's not very good slash home slash list
okay<00:02:28.129><c> look</c><00:02:28.819><c> okay</c><00:02:28.970><c> something</c><00:02:29.420><c> was</c><00:02:29.540><c> broken</c><00:02:30.019><c> here</c>

00:02:30.250 --> 00:02:30.260 align:start position:0%
okay look okay something was broken here
 

00:02:30.260 --> 00:02:32.979 align:start position:0%
okay look okay something was broken here
okay<00:02:31.040><c> so</c><00:02:31.099><c> this</c><00:02:31.310><c> specific</c><00:02:31.579><c> branch</c><00:02:32.299><c> really</c><00:02:32.870><c> is</c>

00:02:32.979 --> 00:02:32.989 align:start position:0%
okay so this specific branch really is
 

00:02:32.989 --> 00:02:35.800 align:start position:0%
okay so this specific branch really is
terrible<00:02:33.790><c> we</c><00:02:34.790><c> need</c><00:02:34.879><c> to</c><00:02:35.000><c> cancel</c><00:02:35.209><c> it</c><00:02:35.540><c> and</c><00:02:35.659><c> go</c>

00:02:35.800 --> 00:02:35.810 align:start position:0%
terrible we need to cancel it and go
 

00:02:35.810 --> 00:02:38.199 align:start position:0%
terrible we need to cancel it and go
back<00:02:36.019><c> to</c><00:02:36.230><c> our</c><00:02:36.349><c> other</c><00:02:36.530><c> branch</c><00:02:36.829><c> get</c><00:02:37.819><c> checkout</c>

00:02:38.199 --> 00:02:38.209 align:start position:0%
back to our other branch get checkout
 

00:02:38.209 --> 00:02:41.140 align:start position:0%
back to our other branch get checkout
master<00:02:38.930><c> okay</c><00:02:39.560><c> master</c><00:02:40.519><c> is</c><00:02:40.670><c> the</c><00:02:40.819><c> branch</c><00:02:41.060><c> that</c>

00:02:41.140 --> 00:02:41.150 align:start position:0%
master okay master is the branch that
 

00:02:41.150 --> 00:02:43.059 align:start position:0%
master okay master is the branch that
were<00:02:41.360><c> actually</c><00:02:41.810><c> interested</c><00:02:42.349><c> in</c><00:02:42.650><c> have</c><00:02:42.890><c> a</c><00:02:42.920><c> look</c>

00:02:43.059 --> 00:02:43.069 align:start position:0%
were actually interested in have a look
 

00:02:43.069 --> 00:02:44.229 align:start position:0%
were actually interested in have a look
at<00:02:43.129><c> what</c><00:02:43.220><c> happens</c><00:02:43.519><c> here</c><00:02:43.609><c> all</c><00:02:43.849><c> of</c><00:02:43.940><c> a</c><00:02:43.970><c> sudden</c>

00:02:44.229 --> 00:02:44.239 align:start position:0%
at what happens here all of a sudden
 

00:02:44.239 --> 00:02:46.300 align:start position:0%
at what happens here all of a sudden
that<00:02:44.359><c> text</c><00:02:44.720><c> that</c><00:02:44.840><c> was</c><00:02:44.959><c> there</c><00:02:45.109><c> a</c><00:02:45.140><c> second</c><00:02:45.500><c> ago</c><00:02:45.560><c> it</c>

00:02:46.300 --> 00:02:46.310 align:start position:0%
that text that was there a second ago it
 

00:02:46.310 --> 00:02:48.699 align:start position:0%
that text that was there a second ago it
gets<00:02:46.640><c> removed</c><00:02:46.849><c> and</c><00:02:47.299><c> if</c><00:02:47.390><c> I</c><00:02:47.540><c> do</c><00:02:47.599><c> note</c><00:02:48.079><c> LAN</c><00:02:48.319><c> server</c>

00:02:48.699 --> 00:02:48.709 align:start position:0%
gets removed and if I do note LAN server
 

00:02:48.709 --> 00:02:51.910 align:start position:0%
gets removed and if I do note LAN server
Jas<00:02:49.720><c> there</c><00:02:50.720><c> was</c><00:02:50.870><c> several</c><00:02:51.170><c> files</c><00:02:51.500><c> that</c><00:02:51.799><c> were</c>

00:02:51.910 --> 00:02:51.920 align:start position:0%
Jas there was several files that were
 

00:02:51.920 --> 00:02:53.289 align:start position:0%
Jas there was several files that were
changed<00:02:52.250><c> the</c><00:02:52.489><c> moment</c><00:02:52.670><c> that</c><00:02:52.849><c> I</c><00:02:52.879><c> switched</c>

00:02:53.289 --> 00:02:53.299 align:start position:0%
changed the moment that I switched
 

00:02:53.299 --> 00:02:55.630 align:start position:0%
changed the moment that I switched
between<00:02:53.540><c> the</c><00:02:53.930><c> branches</c><00:02:54.440><c> so</c><00:02:54.470><c> that's</c><00:02:55.430><c> really</c>

00:02:55.630 --> 00:02:55.640 align:start position:0%
between the branches so that's really
 

00:02:55.640 --> 00:02:57.399 align:start position:0%
between the branches so that's really
what<00:02:55.909><c> it's</c><00:02:56.030><c> good</c><00:02:56.239><c> for</c><00:02:56.269><c> all</c><00:02:56.659><c> the</c><00:02:56.810><c> sudden</c><00:02:57.079><c> I</c><00:02:57.170><c> make</c>

00:02:57.399 --> 00:02:57.409 align:start position:0%
what it's good for all the sudden I make
 

00:02:57.409 --> 00:03:00.099 align:start position:0%
what it's good for all the sudden I make
I<00:02:57.620><c> make</c><00:02:57.849><c> make</c><00:02:58.849><c> changes</c><00:02:59.420><c> to</c><00:02:59.480><c> one</c><00:02:59.870><c> specific</c>

00:03:00.099 --> 00:03:00.109 align:start position:0%
I make make changes to one specific
 

00:03:00.109 --> 00:03:02.559 align:start position:0%
I make make changes to one specific
branch<00:03:00.680><c> something</c><00:03:01.250><c> gets</c><00:03:01.430><c> broken</c><00:03:01.700><c> it's</c><00:03:02.060><c> okay</c><00:03:02.359><c> I</c>

00:03:02.559 --> 00:03:02.569 align:start position:0%
branch something gets broken it's okay I
 

00:03:02.569 --> 00:03:04.509 align:start position:0%
branch something gets broken it's okay I
can<00:03:02.959><c> go</c><00:03:03.079><c> back</c><00:03:03.260><c> to</c><00:03:03.409><c> the</c><00:03:03.500><c> previous</c><00:03:03.709><c> commits</c><00:03:04.310><c> or</c><00:03:04.489><c> I</c>

00:03:04.509 --> 00:03:04.519 align:start position:0%
can go back to the previous commits or I
 

00:03:04.519 --> 00:03:07.990 align:start position:0%
can go back to the previous commits or I
can<00:03:04.849><c> go</c><00:03:04.970><c> back</c><00:03:05.150><c> to</c><00:03:05.180><c> another</c><00:03:06.140><c> branch</c><00:03:06.519><c> so</c><00:03:07.519><c> I'm</c>

00:03:07.990 --> 00:03:08.000 align:start position:0%
can go back to another branch so I'm
 

00:03:08.000 --> 00:03:13.270 align:start position:0%
can go back to another branch so I'm
back<00:03:08.750><c> over</c><00:03:09.079><c> here</c><00:03:11.230><c> no</c><00:03:12.230><c> it's</c><00:03:12.409><c> not</c><00:03:12.560><c> unloaded</c><00:03:13.129><c> host</c>

00:03:13.270 --> 00:03:13.280 align:start position:0%
back over here no it's not unloaded host
 

00:03:13.280 --> 00:03:14.770 align:start position:0%
back over here no it's not unloaded host
a<00:03:13.459><c> pass</c><00:03:13.700><c> and</c><00:03:13.940><c> as</c><00:03:14.030><c> you</c><00:03:14.150><c> can</c><00:03:14.269><c> see</c><00:03:14.329><c> in</c><00:03:14.660><c> our</c>

00:03:14.770 --> 00:03:14.780 align:start position:0%
a pass and as you can see in our
 

00:03:14.780 --> 00:03:17.729 align:start position:0%
a pass and as you can see in our
terminal<00:03:15.440><c> we're</c><00:03:15.650><c> back</c><00:03:15.829><c> on</c><00:03:16.040><c> on</c><00:03:16.280><c> port</c><00:03:17.000><c> 80</c><00:03:17.209><c> so</c>

00:03:17.729 --> 00:03:17.739 align:start position:0%
terminal we're back on on port 80 so
 

00:03:17.739 --> 00:03:23.680 align:start position:0%
terminal we're back on on port 80 so
we're<00:03:18.739><c> gonna</c><00:03:18.889><c> refresh</c><00:03:19.280><c> this</c><00:03:19.639><c> page</c><00:03:19.700><c> and</c><00:03:22.690><c> but</c>

00:03:23.680 --> 00:03:23.690 align:start position:0%
we're gonna refresh this page and but
 

00:03:23.690 --> 00:03:25.059 align:start position:0%
we're gonna refresh this page and but
I'm<00:03:23.780><c> doing</c><00:03:24.019><c> better</c><00:03:24.169><c> boom</c><00:03:24.440><c> everything</c><00:03:24.829><c> works</c>

00:03:25.059 --> 00:03:25.069 align:start position:0%
I'm doing better boom everything works
 

00:03:25.069 --> 00:03:28.030 align:start position:0%
I'm doing better boom everything works
good<00:03:25.400><c> now</c><00:03:25.549><c> and</c><00:03:26.290><c> so</c><00:03:27.290><c> that's</c><00:03:27.410><c> good</c><00:03:27.739><c> so</c><00:03:27.769><c> we</c><00:03:28.010><c> can</c>

00:03:28.030 --> 00:03:28.040 align:start position:0%
good now and so that's good so we can
 

00:03:28.040 --> 00:03:31.240 align:start position:0%
good now and so that's good so we can
push<00:03:28.370><c> everything</c><00:03:28.700><c> here</c><00:03:29.180><c> to</c><00:03:30.019><c> Heroku</c><00:03:30.379><c> now</c><00:03:30.769><c> get</c>

00:03:31.240 --> 00:03:31.250 align:start position:0%
push everything here to Heroku now get
 

00:03:31.250 --> 00:03:35.110 align:start position:0%
push everything here to Heroku now get
push<00:03:31.660><c> Heroku</c><00:03:32.660><c> master</c><00:03:33.430><c> let's</c><00:03:34.430><c> still</c><00:03:34.700><c> get</c><00:03:34.970><c> out</c>

00:03:35.110 --> 00:03:35.120 align:start position:0%
push Heroku master let's still get out
 

00:03:35.120 --> 00:03:38.759 align:start position:0%
push Heroku master let's still get out
all<00:03:35.329><c> just</c><00:03:35.359><c> in</c><00:03:35.780><c> case</c><00:03:36.049><c> git</c><00:03:36.620><c> commit</c><00:03:37.129><c> dash</c><00:03:37.160><c> and</c>

00:03:38.759 --> 00:03:38.769 align:start position:0%
all just in case git commit dash and
 

00:03:38.769 --> 00:03:40.839 align:start position:0%
all just in case git commit dash and
Oroku<00:03:39.769><c> rocks</c>

00:03:40.839 --> 00:03:40.849 align:start position:0%
Oroku rocks
 

00:03:40.849 --> 00:03:46.300 align:start position:0%
Oroku rocks
git<00:03:41.599><c> push</c><00:03:42.129><c> real</c><00:03:43.129><c> good</c><00:03:43.430><c> master</c><00:03:45.040><c> okay</c><00:03:46.040><c> have</c><00:03:46.280><c> a</c>

00:03:46.300 --> 00:03:46.310 align:start position:0%
git push real good master okay have a
 

00:03:46.310 --> 00:03:48.900 align:start position:0%
git push real good master okay have a
look<00:03:46.489><c> at</c><00:03:46.579><c> what</c><00:03:46.699><c> our</c><00:03:46.730><c> Roku</c><00:03:47.359><c> looks</c><00:03:47.629><c> like</c><00:03:47.840><c> now</c>

00:03:48.900 --> 00:03:48.910 align:start position:0%
look at what our Roku looks like now
 

00:03:48.910 --> 00:03:51.280 align:start position:0%
look at what our Roku looks like now
right<00:03:49.910><c> now</c><00:03:50.150><c> we've</c><00:03:50.359><c> got</c><00:03:50.569><c> all</c><00:03:50.690><c> this</c><00:03:50.870><c> garbage</c>

00:03:51.280 --> 00:03:51.290 align:start position:0%
right now we've got all this garbage
 

00:03:51.290 --> 00:03:53.759 align:start position:0%
right now we've got all this garbage
here<00:03:51.709><c> that</c><00:03:52.310><c> we</c><00:03:52.430><c> want</c><00:03:52.639><c> to</c><00:03:52.669><c> get</c><00:03:52.849><c> rid</c><00:03:53.000><c> of</c><00:03:53.060><c> and</c>

00:03:53.759 --> 00:03:53.769 align:start position:0%
here that we want to get rid of and
 

00:03:53.769 --> 00:03:56.199 align:start position:0%
here that we want to get rid of and
every<00:03:54.769><c> time</c><00:03:54.919><c> that</c><00:03:54.949><c> you</c><00:03:55.099><c> pushed</c><00:03:55.459><c> in</c><00:03:55.639><c> Roku</c><00:03:56.060><c> it</c>

00:03:56.199 --> 00:03:56.209 align:start position:0%
every time that you pushed in Roku it
 

00:03:56.209 --> 00:03:58.629 align:start position:0%
every time that you pushed in Roku it
does<00:03:56.359><c> a</c><00:03:56.389><c> complex</c><00:03:56.989><c> process</c><00:03:57.590><c> of</c><00:03:58.099><c> pushing</c><00:03:58.340><c> your</c>

00:03:58.629 --> 00:03:58.639 align:start position:0%
does a complex process of pushing your
 

00:03:58.639 --> 00:04:01.360 align:start position:0%
does a complex process of pushing your
node<00:03:58.879><c> app</c><00:03:59.120><c> up</c><00:03:59.389><c> and</c><00:03:59.919><c> doing</c><00:04:00.919><c> a</c><00:04:00.980><c> bunch</c><00:04:01.160><c> of</c><00:04:01.250><c> other</c>

00:04:01.360 --> 00:04:01.370 align:start position:0%
node app up and doing a bunch of other
 

00:04:01.370 --> 00:04:03.970 align:start position:0%
node app up and doing a bunch of other
changes<00:04:02.030><c> so</c><00:04:02.319><c> that's</c><00:04:03.319><c> gonna</c><00:04:03.500><c> take</c><00:04:03.709><c> about</c><00:04:03.829><c> 30</c>

00:04:03.970 --> 00:04:03.980 align:start position:0%
changes so that's gonna take about 30
 

00:04:03.980 --> 00:04:09.280 align:start position:0%
changes so that's gonna take about 30
seconds<00:04:05.169><c> let's</c><00:04:06.169><c> wait</c><00:04:07.010><c> around</c>

