WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:02.119 align:start position:0%
 
welcome<00:00:00.390><c> to</c><00:00:00.750><c> the</c><00:00:00.870><c> next</c><00:00:01.020><c> episode</c><00:00:01.199><c> of</c><00:00:01.500><c> my</c><00:00:01.709><c> sequel</c>

00:00:02.119 --> 00:00:02.129 align:start position:0%
welcome to the next episode of my sequel
 

00:00:02.129 --> 00:00:03.860 align:start position:0%
welcome to the next episode of my sequel
node<00:00:02.340><c> angular</c><00:00:02.879><c> we</c><00:00:03.060><c> spoke</c><00:00:03.300><c> last</c><00:00:03.540><c> time</c><00:00:03.720><c> about</c>

00:00:03.860 --> 00:00:03.870 align:start position:0%
node angular we spoke last time about
 

00:00:03.870 --> 00:00:07.460 align:start position:0%
node angular we spoke last time about
how<00:00:04.380><c> we're</c><00:00:04.560><c> going</c><00:00:04.680><c> to</c><00:00:04.740><c> create</c><00:00:05.009><c> a</c><00:00:05.400><c> new</c><00:00:06.000><c> a</c><00:00:06.299><c> new</c><00:00:06.470><c> a</c>

00:00:07.460 --> 00:00:07.470 align:start position:0%
how we're going to create a new a new a
 

00:00:07.470 --> 00:00:12.459 align:start position:0%
how we're going to create a new a new a
new<00:00:07.620><c> option</c><00:00:08.309><c> which</c><00:00:08.730><c> is</c><00:00:08.760><c> gonna</c><00:00:09.120><c> allow</c><00:00:09.540><c> us</c><00:00:09.809><c> to</c>

00:00:12.459 --> 00:00:12.469 align:start position:0%
 
 

00:00:12.469 --> 00:00:14.299 align:start position:0%
 
grab<00:00:13.469><c> sorry</c>

00:00:14.299 --> 00:00:14.309 align:start position:0%
grab sorry
 

00:00:14.309 --> 00:00:17.150 align:start position:0%
grab sorry
gonna<00:00:15.299><c> allow</c><00:00:15.509><c> us</c><00:00:15.540><c> to</c><00:00:15.870><c> dynamically</c><00:00:16.470><c> load</c><00:00:16.859><c> a</c><00:00:17.130><c> new</c>

00:00:17.150 --> 00:00:17.160 align:start position:0%
gonna allow us to dynamically load a new
 

00:00:17.160 --> 00:00:21.080 align:start position:0%
gonna allow us to dynamically load a new
theme<00:00:17.910><c> okay</c><00:00:18.660><c> I'll</c><00:00:18.900><c> alter</c><00:00:19.619><c> table</c><00:00:19.859><c> client</c><00:00:20.850><c> we</c>

00:00:21.080 --> 00:00:21.090 align:start position:0%
theme okay I'll alter table client we
 

00:00:21.090 --> 00:00:23.660 align:start position:0%
theme okay I'll alter table client we
want<00:00:21.270><c> to</c><00:00:21.330><c> add</c><00:00:21.480><c> a</c><00:00:21.510><c> theme</c><00:00:22.050><c> client</c><00:00:22.619><c> theme</c><00:00:22.859><c> to</c><00:00:23.550><c> the</c>

00:00:23.660 --> 00:00:23.670 align:start position:0%
want to add a theme client theme to the
 

00:00:23.670 --> 00:00:26.000 align:start position:0%
want to add a theme client theme to the
client<00:00:24.119><c> table</c><00:00:24.480><c> we're</c><00:00:24.630><c> gonna</c><00:00:24.689><c> call</c><00:00:25.080><c> this</c><00:00:25.380><c> what</c>

00:00:26.000 --> 00:00:26.010 align:start position:0%
client table we're gonna call this what
 

00:00:26.010 --> 00:00:29.960 align:start position:0%
client table we're gonna call this what
exactly<00:00:26.599><c> I</c><00:00:27.599><c> have</c><00:00:28.050><c> it</c><00:00:28.500><c> already</c><00:00:29.220><c> listed</c><00:00:29.460><c> here</c><00:00:29.880><c> in</c>

00:00:29.960 --> 00:00:29.970 align:start position:0%
exactly I have it already listed here in
 

00:00:29.970 --> 00:00:32.900 align:start position:0%
exactly I have it already listed here in
the<00:00:30.060><c> database</c><00:00:30.269><c> that's</c><00:00:30.660><c> sequel</c><00:00:31.109><c> file</c><00:00:31.790><c> and</c><00:00:32.790><c> you</c>

00:00:32.900 --> 00:00:32.910 align:start position:0%
the database that's sequel file and you
 

00:00:32.910 --> 00:00:34.760 align:start position:0%
the database that's sequel file and you
can<00:00:32.940><c> look</c><00:00:33.120><c> back</c><00:00:33.180><c> at</c><00:00:33.450><c> the</c><00:00:33.540><c> my</c><00:00:33.719><c> sequel</c><00:00:34.200><c> tutorial</c>

00:00:34.760 --> 00:00:34.770 align:start position:0%
can look back at the my sequel tutorial
 

00:00:34.770 --> 00:00:36.979 align:start position:0%
can look back at the my sequel tutorial
series<00:00:35.010><c> on</c><00:00:35.309><c> how</c><00:00:35.820><c> to</c><00:00:35.850><c> start</c><00:00:36.059><c> setting</c><00:00:36.570><c> up</c><00:00:36.690><c> your</c>

00:00:36.979 --> 00:00:36.989 align:start position:0%
series on how to start setting up your
 

00:00:36.989 --> 00:00:39.380 align:start position:0%
series on how to start setting up your
your<00:00:37.680><c> actual</c><00:00:38.129><c> database</c><00:00:38.610><c> for</c><00:00:38.969><c> this</c><00:00:39.059><c> project</c>

00:00:39.380 --> 00:00:39.390 align:start position:0%
your actual database for this project
 

00:00:39.390 --> 00:00:42.200 align:start position:0%
your actual database for this project
and<00:00:40.110><c> you'll</c><00:00:40.920><c> notice</c><00:00:41.100><c> we</c><00:00:41.280><c> have</c><00:00:41.309><c> here</c><00:00:41.730><c> client</c>

00:00:42.200 --> 00:00:42.210 align:start position:0%
and you'll notice we have here client
 

00:00:42.210 --> 00:00:46.190 align:start position:0%
and you'll notice we have here client
theme<00:00:42.540><c> bar</c><00:00:43.230><c> chart</c><00:00:43.530><c> 15</c><00:00:44.070><c> ok</c><00:00:44.670><c> and</c><00:00:44.910><c> default</c><00:00:45.899><c> not</c>

00:00:46.190 --> 00:00:46.200 align:start position:0%
theme bar chart 15 ok and default not
 

00:00:46.200 --> 00:00:49.660 align:start position:0%
theme bar chart 15 ok and default not
know<00:00:46.710><c> meaning</c><00:00:47.340><c> that</c><00:00:47.430><c> it</c><00:00:47.579><c> can</c><00:00:47.700><c> be</c><00:00:47.879><c> default</c><00:00:48.360><c> good</c>

00:00:49.660 --> 00:00:49.670 align:start position:0%
know meaning that it can be default good
 

00:00:49.670 --> 00:00:53.450 align:start position:0%
know meaning that it can be default good
client<00:00:50.670><c> name</c><00:00:50.910><c> and</c><00:00:51.420><c> datatype</c><00:00:51.960><c> okay</c><00:00:52.440><c> ID</c><00:00:53.129><c> column</c>

00:00:53.450 --> 00:00:53.460 align:start position:0%
client name and datatype okay ID column
 

00:00:53.460 --> 00:00:56.720 align:start position:0%
client name and datatype okay ID column
name<00:00:53.760><c> and</c><00:00:54.000><c> data</c><00:00:54.329><c> type</c><00:00:54.629><c> and</c><00:00:54.899><c> like</c><00:00:55.410><c> this</c><00:00:55.680><c> and</c><00:00:56.039><c> run</c>

00:00:56.720 --> 00:00:56.730 align:start position:0%
name and data type and like this and run
 

00:00:56.730 --> 00:01:00.110 align:start position:0%
name and data type and like this and run
that<00:00:58.100><c> once</c><00:00:59.100><c> we</c><00:00:59.250><c> run</c><00:00:59.430><c> that</c><00:00:59.609><c> we're</c><00:00:59.820><c> gonna</c><00:00:59.940><c> go</c>

00:01:00.110 --> 00:01:00.120 align:start position:0%
that once we run that we're gonna go
 

00:01:00.120 --> 00:01:02.630 align:start position:0%
that once we run that we're gonna go
back<00:01:00.300><c> and</c><00:01:00.539><c> look</c><00:01:00.750><c> at</c><00:01:00.870><c> the</c><00:01:00.989><c> client</c><00:01:01.230><c> table</c><00:01:01.739><c> and</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
back and look at the client table and
 

00:01:02.640 --> 00:01:05.960 align:start position:0%
back and look at the client table and
see<00:01:03.600><c> whether</c><00:01:03.840><c> it</c><00:01:04.049><c> worked</c><00:01:04.460><c> now</c><00:01:05.460><c> go</c><00:01:05.610><c> back</c><00:01:05.760><c> to</c><00:01:05.880><c> the</c>

00:01:05.960 --> 00:01:05.970 align:start position:0%
see whether it worked now go back to the
 

00:01:05.970 --> 00:01:08.330 align:start position:0%
see whether it worked now go back to the
client<00:01:06.150><c> table</c><00:01:06.570><c> and</c><00:01:06.869><c> it</c><00:01:07.350><c> did</c><00:01:07.590><c> work</c><00:01:07.799><c> now</c><00:01:08.070><c> we</c><00:01:08.130><c> have</c>

00:01:08.330 --> 00:01:08.340 align:start position:0%
client table and it did work now we have
 

00:01:08.340 --> 00:01:11.090 align:start position:0%
client table and it did work now we have
a<00:01:08.369><c> seventh</c><00:01:08.939><c> column</c><00:01:09.240><c> which</c><00:01:09.720><c> is</c><00:01:09.900><c> varchar'</c><00:01:10.619><c> 15</c>

00:01:11.090 --> 00:01:11.100 align:start position:0%
a seventh column which is varchar' 15
 

00:01:11.100 --> 00:01:13.280 align:start position:0%
a seventh column which is varchar' 15
characters<00:01:11.520><c> shouldn't</c><00:01:12.420><c> be</c><00:01:12.510><c> the</c><00:01:12.570><c> client</c><00:01:12.869><c> theme</c>

00:01:13.280 --> 00:01:13.290 align:start position:0%
characters shouldn't be the client theme
 

00:01:13.290 --> 00:01:15.260 align:start position:0%
characters shouldn't be the client theme
okay<00:01:13.680><c> good</c><00:01:13.890><c> now</c><00:01:14.310><c> that</c><00:01:14.340><c> we</c><00:01:14.549><c> have</c><00:01:14.729><c> that</c><00:01:14.970><c> we're</c>

00:01:15.260 --> 00:01:15.270 align:start position:0%
okay good now that we have that we're
 

00:01:15.270 --> 00:01:18.830 align:start position:0%
okay good now that we have that we're
gonna<00:01:15.360><c> actually</c><00:01:15.659><c> go</c><00:01:16.290><c> into</c><00:01:16.770><c> our</c><00:01:17.670><c> core</c><00:01:18.240><c> jus</c>

00:01:18.830 --> 00:01:18.840 align:start position:0%
gonna actually go into our core jus
 

00:01:18.840 --> 00:01:20.719 align:start position:0%
gonna actually go into our core jus
you'll<00:01:19.140><c> notice</c><00:01:19.350><c> that</c><00:01:19.500><c> we</c><00:01:19.650><c> have</c><00:01:19.680><c> over</c><00:01:20.549><c> here</c><00:01:20.670><c> a</c>

00:01:20.719 --> 00:01:20.729 align:start position:0%
you'll notice that we have over here a
 

00:01:20.729 --> 00:01:23.090 align:start position:0%
you'll notice that we have over here a
root<00:01:21.030><c> scoped</c><00:01:21.450><c> out</c><00:01:21.540><c> my</c><00:01:21.780><c> theme</c><00:01:22.140><c> which</c><00:01:22.320><c> is</c><00:01:22.350><c> darkly</c>

00:01:23.090 --> 00:01:23.100 align:start position:0%
root scoped out my theme which is darkly
 

00:01:23.100 --> 00:01:25.520 align:start position:0%
root scoped out my theme which is darkly
right<00:01:23.490><c> so</c><00:01:23.790><c> if</c><00:01:23.850><c> I</c><00:01:24.030><c> check</c><00:01:24.479><c> change</c><00:01:24.930><c> this</c><00:01:25.200><c> to</c>

00:01:25.520 --> 00:01:25.530 align:start position:0%
right so if I check change this to
 

00:01:25.530 --> 00:01:29.060 align:start position:0%
right so if I check change this to
cerulean<00:01:26.659><c> okay</c><00:01:27.659><c> and</c><00:01:27.930><c> then</c><00:01:28.049><c> I'm</c><00:01:28.140><c> gonna</c><00:01:28.320><c> reload</c>

00:01:29.060 --> 00:01:29.070 align:start position:0%
cerulean okay and then I'm gonna reload
 

00:01:29.070 --> 00:01:32.390 align:start position:0%
cerulean okay and then I'm gonna reload
this<00:01:29.369><c> a</c><00:01:29.960><c> second</c><00:01:30.960><c> ago</c><00:01:31.110><c> it's</c><00:01:31.409><c> it's</c><00:01:31.829><c> this</c><00:01:32.130><c> one</c>

00:01:32.390 --> 00:01:32.400 align:start position:0%
this a second ago it's it's this one
 

00:01:32.400 --> 00:01:34.789 align:start position:0%
this a second ago it's it's this one
it's<00:01:32.790><c> this</c><00:01:33.030><c> black</c><00:01:33.329><c> theme</c><00:01:33.659><c> if</c><00:01:33.930><c> I</c><00:01:34.110><c> load</c><00:01:34.560><c> it</c><00:01:34.590><c> again</c>

00:01:34.789 --> 00:01:34.799 align:start position:0%
it's this black theme if I load it again
 

00:01:34.799 --> 00:01:37.999 align:start position:0%
it's this black theme if I load it again
it's<00:01:35.280><c> gonna</c><00:01:35.369><c> be</c><00:01:35.460><c> a</c><00:01:35.579><c> white</c><00:01:35.880><c> theme</c><00:01:36.350><c> and</c><00:01:37.350><c> what</c>

00:01:37.999 --> 00:01:38.009 align:start position:0%
it's gonna be a white theme and what
 

00:01:38.009 --> 00:01:40.399 align:start position:0%
it's gonna be a white theme and what
happened<00:01:38.400><c> you</c><00:01:38.520><c> start</c><00:01:39.180><c> again</c><00:01:39.390><c> okay</c><00:01:39.540><c> starting</c>

00:01:40.399 --> 00:01:40.409 align:start position:0%
happened you start again okay starting
 

00:01:40.409 --> 00:01:42.980 align:start position:0%
happened you start again okay starting
it<00:01:40.530><c> again</c><00:01:40.670><c> need</c><00:01:41.670><c> to</c><00:01:41.820><c> reload</c><00:01:42.150><c> the</c><00:01:42.180><c> page</c><00:01:42.509><c> so</c><00:01:42.869><c> one</c>

00:01:42.980 --> 00:01:42.990 align:start position:0%
it again need to reload the page so one
 

00:01:42.990 --> 00:01:47.539 align:start position:0%
it again need to reload the page so one
sec<00:01:43.229><c> guys</c><00:01:45.470><c> and</c><00:01:46.470><c> we're</c><00:01:46.979><c> gonna</c><00:01:47.040><c> see</c><00:01:47.280><c> a</c><00:01:47.310><c> white</c>

00:01:47.539 --> 00:01:47.549 align:start position:0%
sec guys and we're gonna see a white
 

00:01:47.549 --> 00:01:49.670 align:start position:0%
sec guys and we're gonna see a white
theme<00:01:47.850><c> exactly</c><00:01:48.420><c> it's</c><00:01:48.570><c> a</c><00:01:48.750><c> cerulean</c><00:01:49.110><c> theme</c><00:01:49.560><c> so</c>

00:01:49.670 --> 00:01:49.680 align:start position:0%
theme exactly it's a cerulean theme so
 

00:01:49.680 --> 00:01:50.960 align:start position:0%
theme exactly it's a cerulean theme so
now<00:01:49.770><c> we</c><00:01:49.829><c> want</c><00:01:50.070><c> to</c><00:01:50.130><c> talk</c><00:01:50.250><c> about</c><00:01:50.369><c> how</c><00:01:50.850><c> to</c>

00:01:50.960 --> 00:01:50.970 align:start position:0%
now we want to talk about how to
 

00:01:50.970 --> 00:01:53.420 align:start position:0%
now we want to talk about how to
actually<00:01:51.149><c> save</c><00:01:51.990><c> this</c><00:01:52.320><c> theme</c><00:01:52.619><c> in</c><00:01:52.799><c> the</c><00:01:52.890><c> database</c>

00:01:53.420 --> 00:01:53.430 align:start position:0%
actually save this theme in the database
 

00:01:53.430 --> 00:01:58.969 align:start position:0%
actually save this theme in the database
and<00:01:53.840><c> also</c><00:01:56.030><c> is</c><00:01:57.030><c> she</c><00:01:57.420><c> we</c><00:01:57.810><c> got</c><00:01:57.990><c> a</c><00:01:58.020><c> change</c><00:01:58.350><c> save</c><00:01:58.799><c> the</c>

00:01:58.969 --> 00:01:58.979 align:start position:0%
and also is she we got a change save the
 

00:01:58.979 --> 00:02:01.160 align:start position:0%
and also is she we got a change save the
theme<00:01:59.250><c> for</c><00:01:59.490><c> every</c><00:01:59.640><c> specific</c><00:01:59.969><c> logged</c><00:02:00.570><c> in</c><00:02:00.840><c> user</c>

00:02:01.160 --> 00:02:01.170 align:start position:0%
theme for every specific logged in user
 

00:02:01.170 --> 00:02:03.499 align:start position:0%
theme for every specific logged in user
well<00:02:01.649><c> we</c><00:02:01.799><c> have</c><00:02:01.920><c> a</c><00:02:01.950><c> problem</c><00:02:02.250><c> because</c><00:02:02.430><c> as</c><00:02:02.909><c> you</c>

00:02:03.499 --> 00:02:03.509 align:start position:0%
well we have a problem because as you
 

00:02:03.509 --> 00:02:05.330 align:start position:0%
well we have a problem because as you
can<00:02:03.659><c> see</c><00:02:03.899><c> I</c><00:02:03.930><c> have</c><00:02:04.140><c> to</c><00:02:04.290><c> hover</c><00:02:04.500><c> over</c><00:02:04.649><c> it</c><00:02:05.070><c> in</c><00:02:05.189><c> order</c>

00:02:05.330 --> 00:02:05.340 align:start position:0%
can see I have to hover over it in order
 

00:02:05.340 --> 00:02:07.429 align:start position:0%
can see I have to hover over it in order
to<00:02:05.460><c> see</c><00:02:05.790><c> but</c><00:02:06.000><c> the</c><00:02:06.540><c> majority</c><00:02:07.049><c> of</c><00:02:07.079><c> the</c><00:02:07.290><c> other</c>

00:02:07.429 --> 00:02:07.439 align:start position:0%
to see but the majority of the other
 

00:02:07.439 --> 00:02:08.330 align:start position:0%
to see but the majority of the other
fans

00:02:08.330 --> 00:02:08.340 align:start position:0%
fans
 

00:02:08.340 --> 00:02:12.569 align:start position:0%
fans
actually<00:02:09.340><c> like</c><00:02:10.299><c> work</c><00:02:10.719><c> work</c><00:02:11.319><c> beautifully</c><00:02:12.010><c> even</c>

00:02:12.569 --> 00:02:12.579 align:start position:0%
actually like work work beautifully even
 

00:02:12.579 --> 00:02:14.970 align:start position:0%
actually like work work beautifully even
even<00:02:13.030><c> in</c><00:02:13.150><c> the</c><00:02:13.239><c> heading</c><00:02:13.450><c> so</c><00:02:13.749><c> we</c><00:02:14.260><c> may</c><00:02:14.439><c> just</c><00:02:14.709><c> need</c>

00:02:14.970 --> 00:02:14.980 align:start position:0%
even in the heading so we may just need
 

00:02:14.980 --> 00:02:17.640 align:start position:0%
even in the heading so we may just need
to<00:02:15.040><c> remove</c><00:02:15.579><c> cerulean</c><00:02:16.269><c> okay</c><00:02:16.689><c> now</c><00:02:16.870><c> it</c><00:02:17.019><c> shows</c><00:02:17.590><c> a</c>

00:02:17.640 --> 00:02:17.650 align:start position:0%
to remove cerulean okay now it shows a
 

00:02:17.650 --> 00:02:20.069 align:start position:0%
to remove cerulean okay now it shows a
new<00:02:17.920><c> one</c><00:02:18.069><c> as</c><00:02:18.249><c> you</c><00:02:18.370><c> can</c><00:02:18.400><c> see</c><00:02:18.549><c> this</c><00:02:19.480><c> time</c><00:02:19.719><c> is</c><00:02:20.019><c> a</c>

00:02:20.069 --> 00:02:20.079 align:start position:0%
new one as you can see this time is a
 

00:02:20.079 --> 00:02:22.619 align:start position:0%
new one as you can see this time is a
different<00:02:20.530><c> theme</c><00:02:20.799><c> and</c><00:02:21.670><c> it's</c><00:02:21.819><c> called</c><00:02:21.999><c> Flatley</c>

00:02:22.619 --> 00:02:22.629 align:start position:0%
different theme and it's called Flatley
 

00:02:22.629 --> 00:02:26.309 align:start position:0%
different theme and it's called Flatley
and<00:02:22.980><c> that</c><00:02:23.980><c> one</c><00:02:24.219><c> actually</c><00:02:25.000><c> does</c><00:02:25.569><c> show</c><00:02:25.599><c> me</c><00:02:26.109><c> the</c>

00:02:26.309 --> 00:02:26.319 align:start position:0%
and that one actually does show me the
 

00:02:26.319 --> 00:02:28.589 align:start position:0%
and that one actually does show me the
navbar<00:02:26.769><c> Oh</c><00:02:27.129><c> everything</c><00:02:28.120><c> except</c><00:02:28.359><c> true</c>

00:02:28.589 --> 00:02:28.599 align:start position:0%
navbar Oh everything except true
 

00:02:28.599 --> 00:02:30.780 align:start position:0%
navbar Oh everything except true
cerulean<00:02:29.139><c> does</c><00:02:29.349><c> so</c><00:02:29.590><c> let's</c><00:02:30.129><c> go</c><00:02:30.340><c> back</c><00:02:30.370><c> over</c><00:02:30.760><c> here</c>

00:02:30.780 --> 00:02:30.790 align:start position:0%
cerulean does so let's go back over here
 

00:02:30.790 --> 00:02:32.610 align:start position:0%
cerulean does so let's go back over here
and<00:02:31.030><c> you'll</c><00:02:31.150><c> notice</c><00:02:31.299><c> that</c><00:02:31.480><c> we</c><00:02:31.599><c> have</c><00:02:31.629><c> a</c><00:02:31.989><c> factory</c>

00:02:32.610 --> 00:02:32.620 align:start position:0%
and you'll notice that we have a factory
 

00:02:32.620 --> 00:02:34.949 align:start position:0%
and you'll notice that we have a factory
service<00:02:33.189><c> ok</c><00:02:33.730><c> factory</c><00:02:34.180><c> called</c><00:02:34.419><c> the</c><00:02:34.540><c> client</c>

00:02:34.949 --> 00:02:34.959 align:start position:0%
service ok factory called the client
 

00:02:34.959 --> 00:02:38.369 align:start position:0%
service ok factory called the client
service<00:02:35.379><c> and</c><00:02:35.590><c> that</c><00:02:36.189><c> prints</c><00:02:36.639><c> out</c><00:02:37.379><c> control</c>

00:02:38.369 --> 00:02:38.379 align:start position:0%
service and that prints out control
 

00:02:38.379 --> 00:02:41.039 align:start position:0%
service and that prints out control
shift<00:02:38.680><c> J</c><00:02:39.090><c> something</c><00:02:40.090><c> called</c><00:02:40.389><c> the</c><00:02:40.480><c> client</c>

00:02:41.039 --> 00:02:41.049 align:start position:0%
shift J something called the client
 

00:02:41.049 --> 00:02:44.250 align:start position:0%
shift J something called the client
object<00:02:41.650><c> which</c><00:02:41.799><c> we're</c><00:02:41.980><c> passing</c><00:02:42.519><c> from</c><00:02:43.260><c> from</c>

00:02:44.250 --> 00:02:44.260 align:start position:0%
object which we're passing from from
 

00:02:44.260 --> 00:02:46.439 align:start position:0%
object which we're passing from from
this<00:02:44.530><c> service</c><00:02:45.040><c> here</c><00:02:45.340><c> something</c><00:02:46.209><c> called</c><00:02:46.359><c> the</c>

00:02:46.439 --> 00:02:46.449 align:start position:0%
this service here something called the
 

00:02:46.449 --> 00:02:49.530 align:start position:0%
this service here something called the
client<00:02:46.840><c> service</c><00:02:47.260><c> we're</c><00:02:47.590><c> passing</c><00:02:48.090><c> we</c><00:02:49.090><c> make</c><00:02:49.510><c> a</c>

00:02:49.530 --> 00:02:49.540 align:start position:0%
client service we're passing we make a
 

00:02:49.540 --> 00:02:51.689 align:start position:0%
client service we're passing we make a
call<00:02:49.840><c> to</c><00:02:49.959><c> API</c><00:02:50.319><c> slash</c><00:02:50.439><c> user</c><00:02:51.040><c> we</c><00:02:51.459><c> fetch</c>

00:02:51.689 --> 00:02:51.699 align:start position:0%
call to API slash user we fetch
 

00:02:51.699 --> 00:02:54.270 align:start position:0%
call to API slash user we fetch
everything<00:02:52.180><c> from</c><00:02:52.329><c> the</c><00:02:52.449><c> client</c><00:02:52.900><c> table</c><00:02:53.349><c> we</c><00:02:54.010><c> pass</c>

00:02:54.270 --> 00:02:54.280 align:start position:0%
everything from the client table we pass
 

00:02:54.280 --> 00:03:01.020 align:start position:0%
everything from the client table we pass
that<00:02:54.579><c> into</c><00:02:56.549><c> main</c><00:02:57.549><c> controller</c><00:02:58.150><c> over</c><00:02:58.870><c> here</c><00:03:00.030><c> the</c>

00:03:01.020 --> 00:03:01.030 align:start position:0%
that into main controller over here the
 

00:03:01.030 --> 00:03:02.879 align:start position:0%
that into main controller over here the
main<00:03:01.239><c> controllers</c><00:03:01.959><c> client</c><00:03:02.409><c> service</c><00:03:02.709><c> dot</c>

00:03:02.879 --> 00:03:02.889 align:start position:0%
main controllers client service dot
 

00:03:02.889 --> 00:03:05.520 align:start position:0%
main controllers client service dot
client<00:03:03.430><c> and</c><00:03:03.699><c> it's</c><00:03:04.599><c> this</c><00:03:04.810><c> object</c><00:03:05.079><c> over</c><00:03:05.500><c> here</c>

00:03:05.520 --> 00:03:05.530 align:start position:0%
client and it's this object over here
 

00:03:05.530 --> 00:03:07.619 align:start position:0%
client and it's this object over here
scope<00:03:05.980><c> that</c><00:03:06.189><c> client</c><00:03:06.730><c> so</c><00:03:06.969><c> all</c><00:03:07.120><c> I</c><00:03:07.150><c> need</c><00:03:07.269><c> to</c><00:03:07.480><c> do</c>

00:03:07.619 --> 00:03:07.629 align:start position:0%
scope that client so all I need to do
 

00:03:07.629 --> 00:03:14.869 align:start position:0%
scope that client so all I need to do
now<00:03:07.870><c> is</c><00:03:09.599><c> is</c><00:03:10.599><c> cut</c><00:03:10.930><c> this</c><00:03:11.189><c> and</c><00:03:12.189><c> I'm</c><00:03:12.819><c> gonna</c><00:03:12.939><c> say</c>

00:03:14.869 --> 00:03:14.879 align:start position:0%
now is is cut this and I'm gonna say
 

00:03:14.879 --> 00:03:17.699 align:start position:0%
now is is cut this and I'm gonna say
let's<00:03:15.879><c> go</c><00:03:16.150><c> okay</c><00:03:16.569><c> by</c><00:03:16.750><c> default</c><00:03:17.169><c> it's</c><00:03:17.349><c> gonna</c><00:03:17.530><c> be</c>

00:03:17.699 --> 00:03:17.709 align:start position:0%
let's go okay by default it's gonna be
 

00:03:17.709 --> 00:03:21.719 align:start position:0%
let's go okay by default it's gonna be
darkly<00:03:18.280><c> ok</c><00:03:19.259><c> by</c><00:03:20.259><c> default</c><00:03:20.769><c> it's</c><00:03:21.280><c> gonna</c><00:03:21.459><c> load</c>

00:03:21.719 --> 00:03:21.729 align:start position:0%
darkly ok by default it's gonna load
 

00:03:21.729 --> 00:03:26.189 align:start position:0%
darkly ok by default it's gonna load
darkly<00:03:22.329><c> ok</c><00:03:23.220><c> if</c><00:03:24.280><c> Darkly</c><00:03:25.269><c> doesn't</c><00:03:25.659><c> exist</c><00:03:26.049><c> if</c>

00:03:26.189 --> 00:03:26.199 align:start position:0%
darkly ok if Darkly doesn't exist if
 

00:03:26.199 --> 00:03:29.960 align:start position:0%
darkly ok if Darkly doesn't exist if
there<00:03:26.470><c> is</c><00:03:27.000><c> a</c><00:03:28.000><c> scope</c><00:03:28.720><c> sorry</c>

00:03:29.960 --> 00:03:29.970 align:start position:0%
there is a scope sorry
 

00:03:29.970 --> 00:03:37.830 align:start position:0%
there is a scope sorry
spoke<00:03:30.970><c> that</c><00:03:31.180><c> client</c><00:03:31.840><c> dot</c><00:03:36.239><c> root</c><00:03:37.239><c> scope</c><00:03:37.419><c> that</c><00:03:37.659><c> my</c>

00:03:37.830 --> 00:03:37.840 align:start position:0%
spoke that client dot root scope that my
 

00:03:37.840 --> 00:03:44.460 align:start position:0%
spoke that client dot root scope that my
theme<00:03:38.169><c> equals</c><00:03:39.479><c> scope</c><00:03:40.479><c> that</c><00:03:40.780><c> client</c><00:03:41.439><c> dot</c><00:03:43.470><c> what</c>

00:03:44.460 --> 00:03:44.470 align:start position:0%
theme equals scope that client dot what
 

00:03:44.470 --> 00:03:46.830 align:start position:0%
theme equals scope that client dot what
let's<00:03:45.280><c> look</c><00:03:45.519><c> in</c><00:03:45.639><c> the</c><00:03:45.669><c> actual</c><00:03:46.060><c> object</c><00:03:46.569><c> here</c><00:03:46.780><c> is</c>

00:03:46.830 --> 00:03:46.840 align:start position:0%
let's look in the actual object here is
 

00:03:46.840 --> 00:03:48.360 align:start position:0%
let's look in the actual object here is
called<00:03:47.019><c> that</c><00:03:47.169><c> client</c><00:03:47.620><c> object</c><00:03:47.769><c> here</c><00:03:48.159><c> it</c><00:03:48.250><c> is</c>

00:03:48.360 --> 00:03:48.370 align:start position:0%
called that client object here it is
 

00:03:48.370 --> 00:03:51.119 align:start position:0%
called that client object here it is
here<00:03:49.829><c> mmm</c>

00:03:51.119 --> 00:03:51.129 align:start position:0%
here mmm
 

00:03:51.129 --> 00:03:55.649 align:start position:0%
here mmm
I<00:03:51.909><c> need</c><00:03:52.269><c> to</c><00:03:52.389><c> login</c><00:03:52.599><c> sorry</c><00:03:53.079><c> folks</c><00:03:54.359><c> let's</c><00:03:55.359><c> login</c>

00:03:55.649 --> 00:03:55.659 align:start position:0%
I need to login sorry folks let's login
 

00:03:55.659 --> 00:04:00.990 align:start position:0%
I need to login sorry folks let's login
here<00:03:57.060><c> yeah</c><00:03:58.060><c> what</c><00:03:58.329><c> happened</c><00:03:59.340><c> it's</c><00:04:00.340><c> taking</c><00:04:00.759><c> some</c>

00:04:00.990 --> 00:04:01.000 align:start position:0%
here yeah what happened it's taking some
 

00:04:01.000 --> 00:04:02.369 align:start position:0%
here yeah what happened it's taking some
time<00:04:01.030><c> I</c><00:04:01.269><c> don't</c><00:04:01.569><c> know</c><00:04:01.629><c> exactly</c><00:04:01.959><c> why</c><00:04:02.139><c> that</c><00:04:02.199><c> is</c>

00:04:02.369 --> 00:04:02.379 align:start position:0%
time I don't know exactly why that is
 

00:04:02.379 --> 00:04:08.449 align:start position:0%
time I don't know exactly why that is
but<00:04:03.120><c> we'll</c><00:04:04.120><c> figure</c><00:04:04.299><c> that</c><00:04:04.479><c> out</c><00:04:04.539><c> shortly</c>

00:04:08.449 --> 00:04:08.459 align:start position:0%
 
 

00:04:08.459 --> 00:04:23.339 align:start position:0%
 
Wow<00:04:09.459><c> there</c><00:04:09.729><c> we</c><00:04:09.819><c> go</c><00:04:10.030><c> okay</c><00:04:10.599><c> login</c>

00:04:23.339 --> 00:04:23.349 align:start position:0%
 
 

00:04:23.349 --> 00:04:25.240 align:start position:0%
 
it<00:04:24.349><c> was</c><00:04:24.470><c> a</c><00:04:24.530><c> problem</c><00:04:24.889><c> here</c>

00:04:25.240 --> 00:04:25.250 align:start position:0%
it was a problem here
 

00:04:25.250 --> 00:04:27.670 align:start position:0%
it was a problem here
unexpected<00:04:25.939><c> token</c><00:04:26.120><c> slash</c><00:04:26.659><c> cordage</c><00:04:27.139><c> is</c><00:04:27.289><c> line</c>

00:04:27.670 --> 00:04:27.680 align:start position:0%
unexpected token slash cordage is line
 

00:04:27.680 --> 00:04:34.540 align:start position:0%
unexpected token slash cordage is line
137<00:04:30.189><c> kircheis</c><00:04:31.189><c> line</c><00:04:31.400><c> 137</c><00:04:32.300><c> I</c><00:04:33.340><c> scoped</c><00:04:34.340><c> that</c>

00:04:34.540 --> 00:04:34.550 align:start position:0%
137 kircheis line 137 I scoped that
 

00:04:34.550 --> 00:04:38.589 align:start position:0%
137 kircheis line 137 I scoped that
client<00:04:35.090><c> dot</c><00:04:35.360><c> I</c><00:04:37.000><c> need</c><00:04:38.000><c> to</c><00:04:38.120><c> go</c><00:04:38.270><c> into</c><00:04:38.419><c> the</c>

00:04:38.589 --> 00:04:38.599 align:start position:0%
client dot I need to go into the
 

00:04:38.599 --> 00:04:40.629 align:start position:0%
client dot I need to go into the
database<00:04:38.780><c> dense</c><00:04:39.199><c> equal</c><00:04:39.620><c> sorry</c><00:04:39.860><c> guys</c><00:04:40.039><c> client</c>

00:04:40.629 --> 00:04:40.639 align:start position:0%
database dense equal sorry guys client
 

00:04:40.639 --> 00:04:48.279 align:start position:0%
database dense equal sorry guys client
theme<00:04:40.909><c> ah</c><00:04:43.960><c> sorry</c><00:04:45.729><c> Scott</c><00:04:46.729><c> that</c><00:04:46.939><c> client</c><00:04:47.449><c> it's</c>

00:04:48.279 --> 00:04:48.289 align:start position:0%
theme ah sorry Scott that client it's
 

00:04:48.289 --> 00:04:52.619 align:start position:0%
theme ah sorry Scott that client it's
the<00:04:48.439><c> first</c><00:04:48.770><c> object</c><00:04:49.370><c> there</c><00:04:49.690><c> dot</c><00:04:50.690><c> client</c><00:04:51.680><c> e</c><00:04:51.830><c> ok</c>

00:04:52.619 --> 00:04:52.629 align:start position:0%
the first object there dot client e ok
 

00:04:52.629 --> 00:05:00.789 align:start position:0%
the first object there dot client e ok
and<00:04:53.889><c> save</c><00:04:54.889><c> that</c><00:04:57.039><c> and</c><00:04:58.039><c> reload</c><00:04:58.370><c> that</c>

