WEBVTT
Kind: captions
Language: en

00:00:00.240 --> 00:00:04.390 align:start position:0%
 
this<00:00:00.840><c> is</c><00:00:01.040><c> autog</c><00:00:01.480><c> gr</c><00:00:02.280><c> made</c><00:00:02.520><c> of</c><00:00:02.720><c> version</c><00:00:03.400><c> 4.0</c>

00:00:04.390 --> 00:00:04.400 align:start position:0%
this is autog gr made of version 4.0
 

00:00:04.400 --> 00:00:05.990 align:start position:0%
this is autog gr made of version 4.0
it's<00:00:04.600><c> great</c><00:00:04.839><c> that</c><00:00:04.960><c> so</c><00:00:05.120><c> many</c><00:00:05.319><c> of</c><00:00:05.440><c> you</c><00:00:05.600><c> are</c><00:00:05.759><c> using</c>

00:00:05.990 --> 00:00:06.000 align:start position:0%
it's great that so many of you are using
 

00:00:06.000 --> 00:00:08.230 align:start position:0%
it's great that so many of you are using
our<00:00:06.240><c> live</c><00:00:06.520><c> demo</c><00:00:07.080><c> but</c><00:00:07.200><c> you're</c><00:00:07.359><c> using</c><00:00:07.640><c> up</c><00:00:07.839><c> all</c><00:00:08.080><c> my</c>

00:00:08.230 --> 00:00:08.240 align:start position:0%
our live demo but you're using up all my
 

00:00:08.240 --> 00:00:11.230 align:start position:0%
our live demo but you're using up all my
grock<00:00:08.519><c> developer</c><00:00:08.960><c> credits</c><00:00:09.920><c> so</c><00:00:10.200><c> now</c><00:00:10.840><c> you'll</c><00:00:11.080><c> be</c>

00:00:11.230 --> 00:00:11.240 align:start position:0%
grock developer credits so now you'll be
 

00:00:11.240 --> 00:00:13.789 align:start position:0%
grock developer credits so now you'll be
required<00:00:11.599><c> to</c><00:00:11.759><c> enter</c><00:00:12.040><c> your</c><00:00:12.200><c> own</c><00:00:12.440><c> developer</c><00:00:12.960><c> key</c>

00:00:13.789 --> 00:00:13.799 align:start position:0%
required to enter your own developer key
 

00:00:13.799 --> 00:00:16.230 align:start position:0%
required to enter your own developer key
don't<00:00:14.000><c> worry</c><00:00:14.639><c> it's</c><00:00:14.839><c> session</c><00:00:15.320><c> specific</c><00:00:16.119><c> and</c>

00:00:16.230 --> 00:00:16.240 align:start position:0%
don't worry it's session specific and
 

00:00:16.240 --> 00:00:17.390 align:start position:0%
don't worry it's session specific and
you<00:00:16.320><c> can</c><00:00:16.480><c> always</c><00:00:16.720><c> delete</c><00:00:17.000><c> it</c><00:00:17.119><c> when</c><00:00:17.240><c> you're</c>

00:00:17.390 --> 00:00:17.400 align:start position:0%
you can always delete it when you're
 

00:00:17.400 --> 00:00:20.029 align:start position:0%
you can always delete it when you're
done<00:00:18.080><c> if</c><00:00:18.160><c> you're</c><00:00:18.359><c> new</c><00:00:18.520><c> to</c><00:00:18.680><c> autog</c><00:00:19.000><c> gr</c><00:00:19.359><c> welcome</c>

00:00:20.029 --> 00:00:20.039 align:start position:0%
done if you're new to autog gr welcome
 

00:00:20.039 --> 00:00:22.109 align:start position:0%
done if you're new to autog gr welcome
autog<00:00:20.600><c> generates</c><00:00:20.960><c> a</c><00:00:21.119><c> team</c><00:00:21.359><c> of</c><00:00:21.519><c> Agents</c><00:00:21.840><c> on</c><00:00:22.000><c> the</c>

00:00:22.109 --> 00:00:22.119 align:start position:0%
autog generates a team of Agents on the
 

00:00:22.119 --> 00:00:24.230 align:start position:0%
autog generates a team of Agents on the
Fly<00:00:22.560><c> just</c><00:00:22.760><c> from</c><00:00:22.920><c> your</c><00:00:23.119><c> initial</c><00:00:23.480><c> prompt</c><00:00:24.119><c> it</c>

00:00:24.230 --> 00:00:24.240 align:start position:0%
Fly just from your initial prompt it
 

00:00:24.240 --> 00:00:26.109 align:start position:0%
Fly just from your initial prompt it
even<00:00:24.439><c> has</c><00:00:24.560><c> a</c><00:00:24.720><c> built-in</c><00:00:25.160><c> prompt</c><00:00:25.480><c> engineer</c><00:00:26.000><c> to</c>

00:00:26.109 --> 00:00:26.119 align:start position:0%
even has a built-in prompt engineer to
 

00:00:26.119 --> 00:00:27.950 align:start position:0%
even has a built-in prompt engineer to
make<00:00:26.279><c> sure</c><00:00:26.519><c> your</c><00:00:26.760><c> request</c><00:00:27.160><c> is</c><00:00:27.320><c> as</c><00:00:27.519><c> effective</c>

00:00:27.950 --> 00:00:27.960 align:start position:0%
make sure your request is as effective
 

00:00:27.960 --> 00:00:30.349 align:start position:0%
make sure your request is as effective
as<00:00:28.199><c> possible</c><00:00:28.920><c> we</c><00:00:29.039><c> use</c><00:00:29.279><c> the</c><00:00:29.439><c> grock</c><00:00:30.000><c> inference</c>

00:00:30.349 --> 00:00:30.359 align:start position:0%
as possible we use the grock inference
 

00:00:30.359 --> 00:00:33.549 align:start position:0%
as possible we use the grock inference
engine<00:00:30.720><c> from</c><00:00:30.960><c> gro.com</c><00:00:31.640><c> and</c><00:00:32.360><c> it's</c><00:00:32.599><c> really</c><00:00:32.960><c> fast</c>

00:00:33.549 --> 00:00:33.559 align:start position:0%
engine from gro.com and it's really fast
 

00:00:33.559 --> 00:00:35.750 align:start position:0%
engine from gro.com and it's really fast
you're<00:00:33.719><c> seeing</c><00:00:34.000><c> it</c><00:00:34.239><c> react</c><00:00:34.640><c> in</c><00:00:34.840><c> real</c><00:00:35.160><c> time</c>

00:00:35.750 --> 00:00:35.760 align:start position:0%
you're seeing it react in real time
 

00:00:35.760 --> 00:00:37.830 align:start position:0%
you're seeing it react in real time
experienced<00:00:36.239><c> Auto</c><00:00:36.559><c> grock</c><00:00:36.840><c> users</c><00:00:37.239><c> will</c><00:00:37.440><c> notice</c>

00:00:37.830 --> 00:00:37.840 align:start position:0%
experienced Auto grock users will notice
 

00:00:37.840 --> 00:00:41.029 align:start position:0%
experienced Auto grock users will notice
that<00:00:38.079><c> my</c><00:00:38.200><c> team</c><00:00:38.440><c> of</c><00:00:38.600><c> Agents</c><00:00:39.120><c> was</c><00:00:39.399><c> not</c><00:00:40.039><c> created</c>

00:00:41.029 --> 00:00:41.039 align:start position:0%
that my team of Agents was not created
 

00:00:41.039 --> 00:00:42.510 align:start position:0%
that my team of Agents was not created
this<00:00:41.160><c> is</c><00:00:41.360><c> because</c><00:00:41.640><c> I'm</c><00:00:41.800><c> hitting</c><00:00:42.079><c> my</c><00:00:42.239><c> daily</c>

00:00:42.510 --> 00:00:42.520 align:start position:0%
this is because I'm hitting my daily
 

00:00:42.520 --> 00:00:45.670 align:start position:0%
this is because I'm hitting my daily
usage<00:00:42.960><c> threshold</c><00:00:43.480><c> for</c><00:00:43.640><c> the</c><00:00:43.760><c> mixl</c><00:00:44.399><c> llm</c><00:00:45.399><c> now</c><00:00:45.559><c> you</c>

00:00:45.670 --> 00:00:45.680 align:start position:0%
usage threshold for the mixl llm now you
 

00:00:45.680 --> 00:00:48.389 align:start position:0%
usage threshold for the mixl llm now you
know<00:00:45.840><c> why</c><00:00:46.000><c> I</c><00:00:46.120><c> added</c><00:00:46.360><c> the</c><00:00:46.480><c> API</c><00:00:46.879><c> key</c><00:00:47.120><c> input</c><00:00:48.120><c> no</c>

00:00:48.389 --> 00:00:48.399 align:start position:0%
know why I added the API key input no
 

00:00:48.399 --> 00:00:50.470 align:start position:0%
know why I added the API key input no
problem<00:00:49.160><c> we</c><00:00:49.280><c> can</c><00:00:49.440><c> just</c><00:00:49.559><c> switch</c><00:00:49.840><c> to</c><00:00:49.960><c> the</c><00:00:50.120><c> Llama</c>

00:00:50.470 --> 00:00:50.480 align:start position:0%
problem we can just switch to the Llama
 

00:00:50.480 --> 00:00:52.709 align:start position:0%
problem we can just switch to the Llama
model<00:00:50.920><c> and</c><00:00:51.120><c> continue</c><00:00:51.840><c> by</c><00:00:52.000><c> just</c><00:00:52.120><c> telling</c><00:00:52.359><c> autog</c>

00:00:52.709 --> 00:00:52.719 align:start position:0%
model and continue by just telling autog
 

00:00:52.719 --> 00:00:55.349 align:start position:0%
model and continue by just telling autog
Gro<00:00:53.320><c> hey</c><00:00:53.559><c> let's</c><00:00:53.760><c> make</c><00:00:53.960><c> a</c><00:00:54.079><c> CRM</c><00:00:54.879><c> we're</c><00:00:55.039><c> going</c><00:00:55.199><c> to</c>

00:00:55.349 --> 00:00:55.359 align:start position:0%
Gro hey let's make a CRM we're going to
 

00:00:55.359 --> 00:00:57.189 align:start position:0%
Gro hey let's make a CRM we're going to
get<00:00:55.559><c> an</c><00:00:55.719><c> automatically</c><00:00:56.320><c> generated</c><00:00:56.760><c> team</c><00:00:57.000><c> of</c>

00:00:57.189 --> 00:00:57.199 align:start position:0%
get an automatically generated team of
 

00:00:57.199 --> 00:00:59.110 align:start position:0%
get an automatically generated team of
Agents<00:00:57.800><c> specifically</c><00:00:58.320><c> tailored</c><00:00:58.719><c> to</c><00:00:58.840><c> work</c><00:00:59.000><c> on</c>

00:00:59.110 --> 00:00:59.120 align:start position:0%
Agents specifically tailored to work on
 

00:00:59.120 --> 00:01:03.549 align:start position:0%
Agents specifically tailored to work on
our<00:00:59.359><c> task</c><00:01:00.160><c> wow</c><00:01:00.920><c> nine</c><00:01:01.480><c> agents</c><00:01:02.480><c> nice</c><00:01:03.199><c> the</c><00:01:03.320><c> first</c>

00:01:03.549 --> 00:01:03.559 align:start position:0%
our task wow nine agents nice the first
 

00:01:03.559 --> 00:01:05.590 align:start position:0%
our task wow nine agents nice the first
agent<00:01:03.840><c> is</c><00:01:04.000><c> always</c><00:01:04.239><c> our</c><00:01:04.479><c> project</c><00:01:04.839><c> manager</c>

00:01:05.590 --> 00:01:05.600 align:start position:0%
agent is always our project manager
 

00:01:05.600 --> 00:01:07.070 align:start position:0%
agent is always our project manager
you'll<00:01:05.840><c> see</c><00:01:06.200><c> that</c><00:01:06.360><c> this</c><00:01:06.560><c> agent</c><00:01:06.880><c> is</c>

00:01:07.070 --> 00:01:07.080 align:start position:0%
you'll see that this agent is
 

00:01:07.080 --> 00:01:08.870 align:start position:0%
you'll see that this agent is
predisposed<00:01:07.680><c> to</c><00:01:07.799><c> laying</c><00:01:08.119><c> out</c><00:01:08.320><c> the</c><00:01:08.479><c> project</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
predisposed to laying out the project
 

00:01:08.880 --> 00:01:11.590 align:start position:0%
predisposed to laying out the project
plan<00:01:09.240><c> and</c><00:01:09.400><c> directing</c><00:01:09.799><c> the</c><00:01:10.040><c> team</c><00:01:11.040><c> in</c><00:01:11.240><c> future</c>

00:01:11.590 --> 00:01:11.600 align:start position:0%
plan and directing the team in future
 

00:01:11.600 --> 00:01:14.149 align:start position:0%
plan and directing the team in future
versions<00:01:12.080><c> of</c><00:01:12.280><c> autog</c><00:01:12.640><c> gro</c><00:01:13.400><c> we</c><00:01:13.560><c> plan</c><00:01:13.799><c> to</c><00:01:14.000><c> have</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
versions of autog gro we plan to have
 

00:01:14.159 --> 00:01:16.109 align:start position:0%
versions of autog gro we plan to have
him<00:01:14.360><c> act</c><00:01:14.600><c> as</c><00:01:14.720><c> the</c><00:01:14.840><c> chat</c><00:01:15.119><c> coordinator</c><00:01:15.680><c> for</c><00:01:15.960><c> both</c>

00:01:16.109 --> 00:01:16.119 align:start position:0%
him act as the chat coordinator for both
 

00:01:16.119 --> 00:01:19.510 align:start position:0%
him act as the chat coordinator for both
autogen<00:01:16.920><c> and</c><00:01:17.200><c> crew</c><00:01:17.799><c> AI</c><00:01:18.799><c> keep</c><00:01:18.960><c> in</c><00:01:19.119><c> mind</c><00:01:19.400><c> the</c>

00:01:19.510 --> 00:01:19.520 align:start position:0%
autogen and crew AI keep in mind the
 

00:01:19.520 --> 00:01:21.310 align:start position:0%
autogen and crew AI keep in mind the
autog<00:01:19.799><c> grock</c><00:01:20.119><c> interface</c><00:01:20.520><c> you</c><00:01:20.640><c> see</c><00:01:20.960><c> here</c><00:01:21.119><c> is</c>

00:01:21.310 --> 00:01:21.320 align:start position:0%
autog grock interface you see here is
 

00:01:21.320 --> 00:01:24.469 align:start position:0%
autog grock interface you see here is
designed<00:01:21.720><c> to</c><00:01:22.360><c> one</c><00:01:23.159><c> rapidly</c><00:01:23.600><c> create</c><00:01:24.040><c> teams</c><00:01:24.320><c> of</c>

00:01:24.469 --> 00:01:24.479 align:start position:0%
designed to one rapidly create teams of
 

00:01:24.479 --> 00:01:27.190 align:start position:0%
designed to one rapidly create teams of
agents<00:01:24.840><c> and</c><00:01:25.000><c> workflows</c><00:01:26.000><c> and</c><00:01:26.200><c> two</c><00:01:26.840><c> allow</c><00:01:27.119><c> you</c>

00:01:27.190 --> 00:01:27.200 align:start position:0%
agents and workflows and two allow you
 

00:01:27.200 --> 00:01:29.030 align:start position:0%
agents and workflows and two allow you
to<00:01:27.360><c> test</c><00:01:27.640><c> them</c><00:01:27.799><c> locally</c><00:01:28.320><c> before</c><00:01:28.600><c> exporting</c>

00:01:29.030 --> 00:01:29.040 align:start position:0%
to test them locally before exporting
 

00:01:29.040 --> 00:01:31.230 align:start position:0%
to test them locally before exporting
them<00:01:29.159><c> to</c><00:01:29.320><c> autogen</c><00:01:29.720><c> and</c><00:01:29.960><c> crew</c><00:01:30.240><c> AI</c><00:01:30.880><c> we'll</c><00:01:31.040><c> show</c>

00:01:31.230 --> 00:01:31.240 align:start position:0%
them to autogen and crew AI we'll show
 

00:01:31.240 --> 00:01:33.109 align:start position:0%
them to autogen and crew AI we'll show
you<00:01:31.439><c> new</c><00:01:31.720><c> users</c><00:01:32.200><c> what</c><00:01:32.360><c> the</c><00:01:32.479><c> Whiteboard</c><00:01:32.920><c> and</c>

00:01:33.109 --> 00:01:33.119 align:start position:0%
you new users what the Whiteboard and
 

00:01:33.119 --> 00:01:35.350 align:start position:0%
you new users what the Whiteboard and
discussion<00:01:33.560><c> history</c><00:01:33.920><c> can</c><00:01:34.119><c> do</c><00:01:34.720><c> by</c><00:01:34.920><c> asking</c><00:01:35.200><c> our</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
discussion history can do by asking our
 

00:01:35.360 --> 00:01:37.069 align:start position:0%
discussion history can do by asking our
database<00:01:35.840><c> administrator</c><00:01:36.439><c> to</c><00:01:36.600><c> write</c><00:01:36.799><c> us</c><00:01:36.920><c> some</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
database administrator to write us some
 

00:01:37.079 --> 00:01:39.389 align:start position:0%
database administrator to write us some
SQL

00:01:39.389 --> 00:01:39.399 align:start position:0%
SQL
 

00:01:39.399 --> 00:01:42.950 align:start position:0%
SQL
code<00:01:40.399><c> and</c><00:01:40.560><c> there</c><00:01:40.680><c> it</c><00:01:40.799><c> is</c><00:01:41.320><c> valid</c><00:01:41.680><c> SQL</c><00:01:42.520><c> to</c><00:01:42.720><c> create</c>

00:01:42.950 --> 00:01:42.960 align:start position:0%
code and there it is valid SQL to create
 

00:01:42.960 --> 00:01:44.789 align:start position:0%
code and there it is valid SQL to create
our<00:01:43.159><c> development</c><00:01:43.600><c> schema</c><00:01:44.079><c> and</c><00:01:44.240><c> get</c><00:01:44.360><c> us</c>

00:01:44.789 --> 00:01:44.799 align:start position:0%
our development schema and get us
 

00:01:44.799 --> 00:01:47.190 align:start position:0%
our development schema and get us
started<00:01:45.799><c> at</c><00:01:45.960><c> any</c><00:01:46.159><c> time</c><00:01:46.439><c> we</c><00:01:46.520><c> can</c><00:01:46.680><c> pull</c><00:01:46.880><c> up</c><00:01:47.040><c> the</c>

00:01:47.190 --> 00:01:47.200 align:start position:0%
started at any time we can pull up the
 

00:01:47.200 --> 00:01:49.190 align:start position:0%
started at any time we can pull up the
discussion<00:01:47.680><c> history</c><00:01:48.159><c> and</c><00:01:48.280><c> see</c><00:01:48.479><c> our</c><00:01:48.680><c> entire</c>

00:01:49.190 --> 00:01:49.200 align:start position:0%
discussion history and see our entire
 

00:01:49.200 --> 00:01:51.389 align:start position:0%
discussion history and see our entire
process<00:01:49.920><c> wonderfully</c><00:01:50.399><c> formatted</c><00:01:50.880><c> for</c><00:01:51.079><c> easy</c>

00:01:51.389 --> 00:01:51.399 align:start position:0%
process wonderfully formatted for easy
 

00:01:51.399 --> 00:01:53.950 align:start position:0%
process wonderfully formatted for easy
legibility<00:01:52.119><c> and</c><00:01:52.399><c> reference</c><00:01:53.399><c> it</c><00:01:53.520><c> even</c><00:01:53.719><c> puts</c>

00:01:53.950 --> 00:01:53.960 align:start position:0%
legibility and reference it even puts
 

00:01:53.960 --> 00:01:56.429 align:start position:0%
legibility and reference it even puts
our<00:01:54.159><c> code</c><00:01:54.479><c> into</c><00:01:54.719><c> color-coded</c><00:01:55.280><c> blocks</c><00:01:55.600><c> for</c><00:01:55.799><c> us</c>

00:01:56.429 --> 00:01:56.439 align:start position:0%
our code into color-coded blocks for us
 

00:01:56.439 --> 00:01:58.389 align:start position:0%
our code into color-coded blocks for us
if<00:01:56.560><c> you</c><00:01:56.719><c> have</c><00:01:56.880><c> some</c><00:01:57.159><c> existing</c><00:01:57.560><c> test</c><00:01:57.880><c> data</c><00:01:58.159><c> in</c><00:01:58.280><c> a</c>

00:01:58.389 --> 00:01:58.399 align:start position:0%
if you have some existing test data in a
 

00:01:58.399 --> 00:02:01.109 align:start position:0%
if you have some existing test data in a
CSV<00:01:59.079><c> file</c><00:01:59.880><c> autog</c><00:02:00.119><c> gr</c><00:02:00.360><c> will</c><00:02:00.479><c> incorporate</c><00:02:00.960><c> it</c>

00:02:01.109 --> 00:02:01.119 align:start position:0%
CSV file autog gr will incorporate it
 

00:02:01.119 --> 00:02:03.350 align:start position:0%
CSV file autog gr will incorporate it
into<00:02:01.360><c> the</c><00:02:01.479><c> agent</c><00:02:01.960><c> discussion</c><00:02:02.960><c> it's</c><00:02:03.159><c> not</c>

00:02:03.350 --> 00:02:03.360 align:start position:0%
into the agent discussion it's not
 

00:02:03.360 --> 00:02:05.069 align:start position:0%
into the agent discussion it's not
technically<00:02:03.799><c> rag</c><00:02:04.119><c> support</c><00:02:04.520><c> since</c><00:02:04.719><c> the</c><00:02:04.840><c> data</c>

00:02:05.069 --> 00:02:05.079 align:start position:0%
technically rag support since the data
 

00:02:05.079 --> 00:02:07.230 align:start position:0%
technically rag support since the data
is<00:02:05.240><c> never</c><00:02:05.439><c> vectorized</c><00:02:06.039><c> but</c><00:02:06.159><c> we'll</c><00:02:06.399><c> get</c><00:02:06.600><c> there</c>

00:02:07.230 --> 00:02:07.240 align:start position:0%
is never vectorized but we'll get there
 

00:02:07.240 --> 00:02:09.510 align:start position:0%
is never vectorized but we'll get there
oh<00:02:08.080><c> and</c><00:02:08.239><c> any</c><00:02:08.440><c> URLs</c><00:02:08.920><c> you</c><00:02:09.080><c> add</c><00:02:09.239><c> to</c><00:02:09.399><c> the</c>

00:02:09.510 --> 00:02:09.520 align:start position:0%
oh and any URLs you add to the
 

00:02:09.520 --> 00:02:11.309 align:start position:0%
oh and any URLs you add to the
additional<00:02:09.920><c> input</c><00:02:10.239><c> field</c><00:02:10.599><c> can</c><00:02:10.720><c> be</c><00:02:10.879><c> read</c><00:02:11.160><c> and</c>

00:02:11.309 --> 00:02:11.319 align:start position:0%
additional input field can be read and
 

00:02:11.319 --> 00:02:13.110 align:start position:0%
additional input field can be read and
referenced<00:02:11.800><c> by</c><00:02:11.920><c> all</c><00:02:12.080><c> the</c><00:02:12.239><c> agents</c><00:02:12.920><c> we've</c>

00:02:13.110 --> 00:02:13.120 align:start position:0%
referenced by all the agents we've
 

00:02:13.120 --> 00:02:15.030 align:start position:0%
referenced by all the agents we've
covered<00:02:13.440><c> autogen</c><00:02:13.879><c> and</c><00:02:14.040><c> crew</c><00:02:14.400><c> AI</c><00:02:14.760><c> file</c>

00:02:15.030 --> 00:02:15.040 align:start position:0%
covered autogen and crew AI file
 

00:02:15.040 --> 00:02:17.990 align:start position:0%
covered autogen and crew AI file
generation<00:02:15.519><c> in</c><00:02:15.720><c> past</c><00:02:16.239><c> videos</c><00:02:17.239><c> in</c><00:02:17.360><c> a</c><00:02:17.480><c> nutshell</c>

00:02:17.990 --> 00:02:18.000 align:start position:0%
generation in past videos in a nutshell
 

00:02:18.000 --> 00:02:19.949 align:start position:0%
generation in past videos in a nutshell
autog<00:02:18.400><c> gr</c><00:02:18.720><c> makes</c><00:02:19.000><c> these</c><00:02:19.239><c> exportable</c><00:02:19.800><c> and</c>

00:02:19.949 --> 00:02:19.959 align:start position:0%
autog gr makes these exportable and
 

00:02:19.959 --> 00:02:22.030 align:start position:0%
autog gr makes these exportable and
importable<00:02:20.480><c> agent</c><00:02:20.800><c> and</c><00:02:20.959><c> workflow</c><00:02:21.480><c> files</c><00:02:21.920><c> that</c>

00:02:22.030 --> 00:02:22.040 align:start position:0%
importable agent and workflow files that
 

00:02:22.040 --> 00:02:23.430 align:start position:0%
importable agent and workflow files that
you<00:02:22.160><c> can</c><00:02:22.280><c> use</c><00:02:22.480><c> to</c><00:02:22.640><c> hit</c><00:02:22.800><c> the</c><00:02:22.879><c> ground</c><00:02:23.160><c> running</c>

00:02:23.430 --> 00:02:23.440 align:start position:0%
you can use to hit the ground running
 

00:02:23.440 --> 00:02:25.990 align:start position:0%
you can use to hit the ground running
when<00:02:23.599><c> creating</c><00:02:24.040><c> new</c><00:02:24.280><c> autogen</c><00:02:24.840><c> or</c><00:02:25.040><c> crew</c><00:02:25.400><c> AI</c>

00:02:25.990 --> 00:02:26.000 align:start position:0%
when creating new autogen or crew AI
 

00:02:26.000 --> 00:02:27.990 align:start position:0%
when creating new autogen or crew AI
projects<00:02:27.000><c> go</c><00:02:27.160><c> ahead</c><00:02:27.360><c> and</c><00:02:27.519><c> download</c><00:02:27.920><c> The</c>

00:02:27.990 --> 00:02:28.000 align:start position:0%
projects go ahead and download The
 

00:02:28.000 --> 00:02:30.309 align:start position:0%
projects go ahead and download The
Source<00:02:28.360><c> from</c><00:02:28.519><c> our</c><00:02:28.720><c> GitHub</c><00:02:29.200><c> site</c><00:02:29.560><c> or</c><00:02:29.920><c> try</c><00:02:30.160><c> the</c>

00:02:30.309 --> 00:02:30.319 align:start position:0%
Source from our GitHub site or try the
 

00:02:30.319 --> 00:02:33.190 align:start position:0%
Source from our GitHub site or try the
demo<00:02:30.680><c> online</c><00:02:31.519><c> the</c><00:02:31.640><c> links</c><00:02:31.879><c> are</c><00:02:32.120><c> below</c><00:02:33.000><c> right</c>

00:02:33.190 --> 00:02:33.200 align:start position:0%
demo online the links are below right
 

00:02:33.200 --> 00:02:35.509 align:start position:0%
demo online the links are below right
below<00:02:33.519><c> the</c><00:02:33.720><c> like</c><00:02:34.120><c> And</c><00:02:34.319><c> subscribe</c><00:02:34.800><c> buttons</c>

00:02:35.509 --> 00:02:35.519 align:start position:0%
below the like And subscribe buttons
 

00:02:35.519 --> 00:02:38.509 align:start position:0%
below the like And subscribe buttons
hint<00:02:35.800><c> hint</c><00:02:36.680><c> if</c><00:02:36.800><c> you</c><00:02:36.879><c> run</c><00:02:37.120><c> autog</c><00:02:37.480><c> grock</c><00:02:37.760><c> locally</c>

00:02:38.509 --> 00:02:38.519 align:start position:0%
hint hint if you run autog grock locally
 

00:02:38.519 --> 00:02:40.430 align:start position:0%
hint hint if you run autog grock locally
you<00:02:38.640><c> can</c><00:02:38.800><c> set</c><00:02:39.000><c> your</c><00:02:39.200><c> key</c><00:02:39.599><c> as</c><00:02:39.720><c> an</c><00:02:39.920><c> environment</c>

00:02:40.430 --> 00:02:40.440 align:start position:0%
you can set your key as an environment
 

00:02:40.440 --> 00:02:41.949 align:start position:0%
you can set your key as an environment
variable<00:02:41.080><c> and</c><00:02:41.200><c> not</c><00:02:41.400><c> have</c><00:02:41.480><c> to</c><00:02:41.599><c> mess</c><00:02:41.840><c> with</c>

00:02:41.949 --> 00:02:41.959 align:start position:0%
variable and not have to mess with
 

00:02:41.959 --> 00:02:44.550 align:start position:0%
variable and not have to mess with
entering<00:02:42.280><c> your</c><00:02:42.440><c> grock</c><00:02:42.760><c> keys</c><00:02:43.319><c> manually</c><00:02:44.319><c> more</c>

00:02:44.550 --> 00:02:44.560 align:start position:0%
entering your grock keys manually more
 

00:02:44.560 --> 00:02:46.430 align:start position:0%
entering your grock keys manually more
exciting<00:02:44.959><c> updates</c><00:02:45.280><c> are</c><00:02:45.440><c> on</c><00:02:45.599><c> the</c><00:02:45.720><c> way</c><00:02:46.239><c> we'll</c>

00:02:46.430 --> 00:02:46.440 align:start position:0%
exciting updates are on the way we'll
 

00:02:46.440 --> 00:02:48.589 align:start position:0%
exciting updates are on the way we'll
see<00:02:46.560><c> you</c><00:02:46.680><c> in</c><00:02:46.800><c> the</c><00:02:46.920><c> next</c><00:02:47.159><c> video</c><00:02:48.159><c> thanks</c><00:02:48.400><c> for</c>

00:02:48.589 --> 00:02:48.599 align:start position:0%
see you in the next video thanks for
 

00:02:48.599 --> 00:02:51.440 align:start position:0%
see you in the next video thanks for
watching

