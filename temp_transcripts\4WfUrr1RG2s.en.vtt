WEBVTT
Kind: captions
Language: en

00:00:05.120 --> 00:00:07.990 align:start position:0%
 
in<00:00:05.240><c> a</c><00:00:05.520><c> world</c><00:00:06.240><c> where</c><00:00:06.520><c> programming</c><00:00:07.080><c> AI</c><00:00:07.480><c> agents</c>

00:00:07.990 --> 00:00:08.000 align:start position:0%
in a world where programming AI agents
 

00:00:08.000 --> 00:00:10.709 align:start position:0%
in a world where programming AI agents
requires<00:00:08.519><c> hours</c><00:00:09.240><c> days</c><00:00:09.800><c> even</c><00:00:10.080><c> weeks</c><00:00:10.440><c> spent</c>

00:00:10.709 --> 00:00:10.719 align:start position:0%
requires hours days even weeks spent
 

00:00:10.719 --> 00:00:12.669 align:start position:0%
requires hours days even weeks spent
learning<00:00:11.040><c> new</c><00:00:11.240><c> languages</c><00:00:12.040><c> installing</c><00:00:12.519><c> new</c>

00:00:12.669 --> 00:00:12.679 align:start position:0%
learning new languages installing new
 

00:00:12.679 --> 00:00:14.430 align:start position:0%
learning new languages installing new
Frameworks<00:00:13.599><c> and</c><00:00:13.759><c> trying</c><00:00:14.000><c> to</c><00:00:14.120><c> find</c><00:00:14.320><c> a</c>

00:00:14.430 --> 00:00:14.440 align:start position:0%
Frameworks and trying to find a
 

00:00:14.440 --> 00:00:16.790 align:start position:0%
Frameworks and trying to find a
pharmacist<00:00:15.000><c> who</c><00:00:15.160><c> doesn't</c><00:00:15.480><c> ask</c><00:00:15.800><c> questions</c>

00:00:16.790 --> 00:00:16.800 align:start position:0%
pharmacist who doesn't ask questions
 

00:00:16.800 --> 00:00:18.670 align:start position:0%
pharmacist who doesn't ask questions
there's<00:00:17.000><c> a</c><00:00:17.160><c> faint</c><00:00:17.480><c> glimmer</c><00:00:17.880><c> of</c><00:00:18.080><c> hope</c><00:00:18.320><c> on</c><00:00:18.520><c> the</c>

00:00:18.670 --> 00:00:18.680 align:start position:0%
there's a faint glimmer of hope on the
 

00:00:18.680 --> 00:00:21.180 align:start position:0%
there's a faint glimmer of hope on the
horizon

00:00:21.180 --> 00:00:21.190 align:start position:0%
horizon
 

00:00:21.190 --> 00:00:36.270 align:start position:0%
horizon
[Music]

00:00:36.270 --> 00:00:36.280 align:start position:0%
[Music]
 

00:00:36.280 --> 00:00:38.750 align:start position:0%
[Music]
autut<00:00:37.280><c> the</c><00:00:37.360><c> first</c><00:00:37.520><c> four</c><00:00:37.760><c> tabs</c><00:00:38.160><c> on</c><00:00:38.399><c> the</c><00:00:38.520><c> new</c>

00:00:38.750 --> 00:00:38.760 align:start position:0%
autut the first four tabs on the new
 

00:00:38.760 --> 00:00:41.229 align:start position:0%
autut the first four tabs on the new
auto<00:00:39.160><c> Gro</c><00:00:39.480><c> interface</c><00:00:40.079><c> have</c><00:00:40.520><c> one</c><00:00:40.760><c> to</c><00:00:40.960><c> many</c>

00:00:41.229 --> 00:00:41.239 align:start position:0%
auto Gro interface have one to many
 

00:00:41.239 --> 00:00:43.430 align:start position:0%
auto Gro interface have one to many
relationships<00:00:41.920><c> left</c><00:00:42.160><c> to</c><00:00:42.399><c> right</c><00:00:42.840><c> a</c><00:00:43.039><c> project</c>

00:00:43.430 --> 00:00:43.440 align:start position:0%
relationships left to right a project
 

00:00:43.440 --> 00:00:45.190 align:start position:0%
relationships left to right a project
can<00:00:43.640><c> contain</c><00:00:44.000><c> several</c><00:00:44.360><c> workflows</c><00:00:45.039><c> which</c>

00:00:45.190 --> 00:00:45.200 align:start position:0%
can contain several workflows which
 

00:00:45.200 --> 00:00:48.310 align:start position:0%
can contain several workflows which
contain<00:00:45.559><c> agents</c><00:00:46.480><c> which</c><00:00:46.640><c> contain</c><00:00:47.120><c> tools</c><00:00:48.120><c> your</c>

00:00:48.310 --> 00:00:48.320 align:start position:0%
contain agents which contain tools your
 

00:00:48.320 --> 00:00:50.189 align:start position:0%
contain agents which contain tools your
default<00:00:48.760><c> provider</c><00:00:49.120><c> and</c><00:00:49.320><c> model</c><00:00:49.680><c> is</c><00:00:49.840><c> set</c><00:00:50.039><c> up</c>

00:00:50.189 --> 00:00:50.199 align:start position:0%
default provider and model is set up
 

00:00:50.199 --> 00:00:53.430 align:start position:0%
default provider and model is set up
under<00:00:50.440><c> the</c><00:00:50.600><c> settings</c><00:00:51.280><c> tab</c><00:00:52.280><c> the</c><00:00:52.440><c> debug</c><00:00:53.039><c> content</c>

00:00:53.430 --> 00:00:53.440 align:start position:0%
under the settings tab the debug content
 

00:00:53.440 --> 00:00:56.990 align:start position:0%
under the settings tab the debug content
is<00:00:53.680><c> categorized</c><00:00:54.480><c> by</c><00:00:54.680><c> session</c><00:00:55.640><c> variables</c><00:00:56.640><c> file</c>

00:00:56.990 --> 00:00:57.000 align:start position:0%
is categorized by session variables file
 

00:00:57.000 --> 00:00:59.549 align:start position:0%
is categorized by session variables file
management<00:00:57.559><c> is</c><00:00:57.760><c> where</c><00:00:58.039><c> we</c><00:00:58.719><c> you</c><00:00:58.879><c> know</c><00:00:59.160><c> manage</c>

00:00:59.549 --> 00:00:59.559 align:start position:0%
management is where we you know manage
 

00:00:59.559 --> 00:01:01.590 align:start position:0%
management is where we you know manage
files<00:01:00.239><c> and</c><00:01:00.440><c> discussion</c><00:01:00.920><c> is</c><00:01:01.160><c> pretty</c>

00:01:01.590 --> 00:01:01.600 align:start position:0%
files and discussion is pretty
 

00:01:01.600 --> 00:01:04.950 align:start position:0%
files and discussion is pretty
self-explanatory<00:01:02.680><c> too</c><00:01:03.680><c> projects</c><00:01:04.239><c> workflows</c>

00:01:04.950 --> 00:01:04.960 align:start position:0%
self-explanatory too projects workflows
 

00:01:04.960 --> 00:01:07.230 align:start position:0%
self-explanatory too projects workflows
agents<00:01:05.320><c> and</c><00:01:05.520><c> tools</c><00:01:06.520><c> are</c><00:01:06.680><c> the</c><00:01:06.799><c> meat</c><00:01:07.040><c> and</c>

00:01:07.230 --> 00:01:07.240 align:start position:0%
agents and tools are the meat and
 

00:01:07.240 --> 00:01:09.070 align:start position:0%
agents and tools are the meat and
potatoes<00:01:07.640><c> of</c><00:01:07.799><c> autog</c><00:01:08.119><c> Gro</c><00:01:08.439><c> so</c><00:01:08.600><c> we'll</c><00:01:08.759><c> start</c>

00:01:09.070 --> 00:01:09.080 align:start position:0%
potatoes of autog Gro so we'll start
 

00:01:09.080 --> 00:01:10.990 align:start position:0%
potatoes of autog Gro so we'll start
there<00:01:09.280><c> by</c><00:01:09.479><c> asking</c><00:01:09.799><c> AI</c><00:01:10.159><c> to</c><00:01:10.320><c> create</c><00:01:10.600><c> a</c><00:01:10.720><c> simple</c>

00:01:10.990 --> 00:01:11.000 align:start position:0%
there by asking AI to create a simple
 

00:01:11.000 --> 00:01:13.149 align:start position:0%
there by asking AI to create a simple
accounting<00:01:11.400><c> app</c><00:01:11.640><c> for</c><00:01:11.840><c> us</c><00:01:12.600><c> you'll</c><00:01:12.799><c> see</c><00:01:13.000><c> that</c>

00:01:13.149 --> 00:01:13.159 align:start position:0%
accounting app for us you'll see that
 

00:01:13.159 --> 00:01:15.070 align:start position:0%
accounting app for us you'll see that
our<00:01:13.320><c> refactored</c><00:01:13.960><c> prompt</c><00:01:14.320><c> is</c><00:01:14.439><c> stored</c><00:01:14.759><c> at</c><00:01:14.920><c> the</c>

00:01:15.070 --> 00:01:15.080 align:start position:0%
our refactored prompt is stored at the
 

00:01:15.080 --> 00:01:16.990 align:start position:0%
our refactored prompt is stored at the
project<00:01:15.439><c> level</c><00:01:15.680><c> for</c><00:01:15.960><c> us</c><00:01:16.159><c> along</c><00:01:16.439><c> with</c><00:01:16.600><c> fields</c>

00:01:16.990 --> 00:01:17.000 align:start position:0%
project level for us along with fields
 

00:01:17.000 --> 00:01:18.630 align:start position:0%
project level for us along with fields
for<00:01:17.159><c> a</c><00:01:17.280><c> number</c><00:01:17.520><c> of</c><00:01:17.680><c> other</c><00:01:17.920><c> top</c><00:01:18.159><c> level</c>

00:01:18.630 --> 00:01:18.640 align:start position:0%
for a number of other top level
 

00:01:18.640 --> 00:01:21.789 align:start position:0%
for a number of other top level
properties<00:01:19.640><c> let's</c><00:01:19.880><c> rename</c><00:01:20.280><c> our</c><00:01:20.799><c> project</c>

00:01:21.789 --> 00:01:21.799 align:start position:0%
properties let's rename our project
 

00:01:21.799 --> 00:01:24.990 align:start position:0%
properties let's rename our project
we'll<00:01:22.000><c> need</c><00:01:22.119><c> a</c><00:01:22.280><c> workflow</c><00:01:23.079><c> too</c><00:01:24.079><c> and</c><00:01:24.200><c> the</c><00:01:24.360><c> AI</c><00:01:24.799><c> has</c>

00:01:24.990 --> 00:01:25.000 align:start position:0%
we'll need a workflow too and the AI has
 

00:01:25.000 --> 00:01:26.830 align:start position:0%
we'll need a workflow too and the AI has
already<00:01:25.360><c> made</c><00:01:25.640><c> one</c><00:01:25.880><c> for</c><00:01:26.079><c> us</c><00:01:26.320><c> just</c><00:01:26.479><c> need</c><00:01:26.680><c> to</c>

00:01:26.830 --> 00:01:26.840 align:start position:0%
already made one for us just need to
 

00:01:26.840 --> 00:01:28.590 align:start position:0%
already made one for us just need to
rename<00:01:27.280><c> it</c><00:01:27.759><c> here's</c><00:01:28.000><c> where</c><00:01:28.200><c> things</c><00:01:28.439><c> get</c>

00:01:28.590 --> 00:01:28.600 align:start position:0%
rename it here's where things get
 

00:01:28.600 --> 00:01:30.230 align:start position:0%
rename it here's where things get
interesting<00:01:29.119><c> I'm</c><00:01:29.240><c> not</c><00:01:29.360><c> sure</c><00:01:29.600><c> other</c><00:01:30.000><c> agent</c>

00:01:30.230 --> 00:01:30.240 align:start position:0%
interesting I'm not sure other agent
 

00:01:30.240 --> 00:01:32.030 align:start position:0%
interesting I'm not sure other agent
Frameworks<00:01:30.680><c> are</c><00:01:30.880><c> taking</c><00:01:31.079><c> a</c><00:01:31.200><c> multi-work</c><00:01:31.840><c> flow</c>

00:01:32.030 --> 00:01:32.040 align:start position:0%
Frameworks are taking a multi-work flow
 

00:01:32.040 --> 00:01:34.830 align:start position:0%
Frameworks are taking a multi-work flow
approach<00:01:32.439><c> but</c><00:01:32.600><c> we</c><00:01:32.960><c> can</c><00:01:33.960><c> imagine</c><00:01:34.320><c> if</c><00:01:34.439><c> we</c><00:01:34.600><c> make</c>

00:01:34.830 --> 00:01:34.840 align:start position:0%
approach but we can imagine if we make
 

00:01:34.840 --> 00:01:37.069 align:start position:0%
approach but we can imagine if we make
the<00:01:35.040><c> bookkeeping</c><00:01:35.759><c> facet</c><00:01:36.119><c> of</c><00:01:36.240><c> our</c><00:01:36.520><c> accounting</c>

00:01:37.069 --> 00:01:37.079 align:start position:0%
the bookkeeping facet of our accounting
 

00:01:37.079 --> 00:01:39.350 align:start position:0%
the bookkeeping facet of our accounting
app<00:01:38.079><c> separate</c><00:01:38.479><c> and</c><00:01:38.720><c> distinct</c><00:01:39.119><c> from</c><00:01:39.240><c> the</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
app separate and distinct from the
 

00:01:39.360 --> 00:01:40.670 align:start position:0%
app separate and distinct from the
number<00:01:39.640><c> crunching</c>

00:01:40.670 --> 00:01:40.680 align:start position:0%
number crunching
 

00:01:40.680 --> 00:01:43.950 align:start position:0%
number crunching
workflow<00:01:41.680><c> making</c><00:01:42.040><c> these</c><00:01:42.320><c> operations</c><00:01:43.320><c> more</c>

00:01:43.950 --> 00:01:43.960 align:start position:0%
workflow making these operations more
 

00:01:43.960 --> 00:01:46.789 align:start position:0%
workflow making these operations more
granular<00:01:44.560><c> and</c><00:01:44.880><c> focused</c><00:01:45.880><c> has</c><00:01:46.040><c> a</c><00:01:46.240><c> ton</c><00:01:46.600><c> of</c>

00:01:46.789 --> 00:01:46.799 align:start position:0%
granular and focused has a ton of
 

00:01:46.799 --> 00:01:49.469 align:start position:0%
granular and focused has a ton of
advantages<00:01:47.799><c> they</c><00:01:47.920><c> could</c><00:01:48.079><c> run</c><00:01:48.719><c> independently</c>

00:01:49.469 --> 00:01:49.479 align:start position:0%
advantages they could run independently
 

00:01:49.479 --> 00:01:52.069 align:start position:0%
advantages they could run independently
of<00:01:49.640><c> each</c><00:01:49.759><c> other</c><00:01:50.040><c> or</c><00:01:50.200><c> even</c><00:01:50.960><c> simultaneously</c><00:01:51.960><c> the</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
of each other or even simultaneously the
 

00:01:52.079 --> 00:01:53.350 align:start position:0%
of each other or even simultaneously the
bookkeeping<00:01:52.600><c> workflow</c><00:01:53.119><c> could</c><00:01:53.240><c> be</c>

00:01:53.350 --> 00:01:53.360 align:start position:0%
bookkeeping workflow could be
 

00:01:53.360 --> 00:01:55.709 align:start position:0%
bookkeeping workflow could be
incorporated<00:01:54.040><c> into</c><00:01:54.280><c> a</c><00:01:54.399><c> payroll</c><00:01:55.079><c> project</c><00:01:55.399><c> or</c><00:01:55.560><c> a</c>

00:01:55.709 --> 00:01:55.719 align:start position:0%
incorporated into a payroll project or a
 

00:01:55.719 --> 00:01:57.910 align:start position:0%
incorporated into a payroll project or a
personal<00:01:56.079><c> banking</c><00:01:56.399><c> Ledger</c><00:01:57.200><c> ideally</c><00:01:57.560><c> autog</c>

00:01:57.910 --> 00:01:57.920 align:start position:0%
personal banking Ledger ideally autog
 

00:01:57.920 --> 00:02:00.149 align:start position:0%
personal banking Ledger ideally autog
Gro<00:01:58.200><c> will</c><00:01:58.320><c> do</c><00:01:58.520><c> for</c><00:01:58.759><c> AI</c><00:01:59.119><c> agents</c><00:01:59.479><c> what</c><00:01:59.560><c> the</c>

00:02:00.149 --> 00:02:00.159 align:start position:0%
Gro will do for AI agents what the
 

00:02:00.159 --> 00:02:01.990 align:start position:0%
Gro will do for AI agents what the
Corporation<00:02:00.680><c> did</c><00:02:00.840><c> for</c><00:02:00.960><c> the</c><00:02:01.079><c> toy</c><00:02:01.360><c> industry</c><00:02:01.840><c> by</c>

00:02:01.990 --> 00:02:02.000 align:start position:0%
Corporation did for the toy industry by
 

00:02:02.000 --> 00:02:05.029 align:start position:0%
Corporation did for the toy industry by
allowing<00:02:02.399><c> us</c><00:02:02.600><c> to</c><00:02:03.079><c> build</c><00:02:03.759><c> whatever</c><00:02:04.119><c> we</c><00:02:04.280><c> want</c><00:02:04.640><c> by</c>

00:02:05.029 --> 00:02:05.039 align:start position:0%
allowing us to build whatever we want by
 

00:02:05.039 --> 00:02:07.910 align:start position:0%
allowing us to build whatever we want by
oh<00:02:05.280><c> I</c><00:02:05.360><c> freaking</c><00:02:05.680><c> love</c><00:02:06.280><c> Legos</c><00:02:07.280><c> my</c><00:02:07.399><c> point</c><00:02:07.680><c> is</c>

00:02:07.910 --> 00:02:07.920 align:start position:0%
oh I freaking love Legos my point is
 

00:02:07.920 --> 00:02:09.749 align:start position:0%
oh I freaking love Legos my point is
when<00:02:08.039><c> we</c><00:02:08.119><c> build</c><00:02:08.319><c> a</c><00:02:08.440><c> death</c><00:02:08.679><c> star</c><00:02:09.440><c> I</c><00:02:09.520><c> want</c><00:02:09.640><c> to</c>

00:02:09.749 --> 00:02:09.759 align:start position:0%
when we build a death star I want to
 

00:02:09.759 --> 00:02:12.710 align:start position:0%
when we build a death star I want to
build<00:02:09.959><c> a</c><00:02:10.080><c> death</c><00:02:10.319><c> star</c><00:02:11.039><c> maybe</c><00:02:11.360><c> after</c><00:02:11.560><c> your</c><00:02:11.760><c> nap</c>

00:02:12.710 --> 00:02:12.720 align:start position:0%
build a death star maybe after your nap
 

00:02:12.720 --> 00:02:15.309 align:start position:0%
build a death star maybe after your nap
anyway<00:02:13.080><c> you</c><00:02:13.200><c> get</c><00:02:13.400><c> the</c><00:02:13.720><c> idea</c><00:02:14.720><c> agents</c><00:02:15.120><c> are</c>

00:02:15.309 --> 00:02:15.319 align:start position:0%
anyway you get the idea agents are
 

00:02:15.319 --> 00:02:17.589 align:start position:0%
anyway you get the idea agents are
members<00:02:15.680><c> of</c><00:02:15.959><c> workflows</c><00:02:16.640><c> and</c><00:02:16.879><c> tools</c><00:02:17.400><c> are</c>

00:02:17.589 --> 00:02:17.599 align:start position:0%
members of workflows and tools are
 

00:02:17.599 --> 00:02:20.150 align:start position:0%
members of workflows and tools are
members<00:02:17.920><c> of</c><00:02:18.239><c> Agents</c><00:02:19.239><c> similar</c><00:02:19.599><c> to</c><00:02:19.760><c> the</c><00:02:19.920><c> old</c>

00:02:20.150 --> 00:02:20.160 align:start position:0%
members of Agents similar to the old
 

00:02:20.160 --> 00:02:22.630 align:start position:0%
members of Agents similar to the old
Auto<00:02:20.560><c> Gro</c><00:02:20.959><c> agents</c><00:02:21.400><c> appear</c><00:02:21.680><c> as</c><00:02:21.879><c> buttons</c><00:02:22.200><c> in</c><00:02:22.360><c> our</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
Auto Gro agents appear as buttons in our
 

00:02:22.640 --> 00:02:24.710 align:start position:0%
Auto Gro agents appear as buttons in our
sidebar<00:02:23.120><c> that</c><00:02:23.280><c> you</c><00:02:23.360><c> can</c><00:02:23.519><c> interact</c><00:02:24.160><c> with</c>

00:02:24.710 --> 00:02:24.720 align:start position:0%
sidebar that you can interact with
 

00:02:24.720 --> 00:02:26.830 align:start position:0%
sidebar that you can interact with
settings<00:02:25.480><c> we</c><00:02:25.599><c> talked</c><00:02:25.840><c> about</c><00:02:26.080><c> earlier</c><00:02:26.560><c> under</c>

00:02:26.830 --> 00:02:26.840 align:start position:0%
settings we talked about earlier under
 

00:02:26.840 --> 00:02:29.030 align:start position:0%
settings we talked about earlier under
debug<00:02:27.680><c> we</c><00:02:27.800><c> see</c><00:02:28.000><c> that</c><00:02:28.120><c> a</c><00:02:28.239><c> bunch</c><00:02:28.400><c> of</c><00:02:28.599><c> session</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
debug we see that a bunch of session
 

00:02:29.040 --> 00:02:30.710 align:start position:0%
debug we see that a bunch of session
properties<00:02:29.400><c> have</c><00:02:29.519><c> been</c><00:02:29.920><c> created</c><00:02:30.400><c> there's</c><00:02:30.599><c> a</c>

00:02:30.710 --> 00:02:30.720 align:start position:0%
properties have been created there's a
 

00:02:30.720 --> 00:02:33.350 align:start position:0%
properties have been created there's a
ton<00:02:31.000><c> of</c><00:02:31.200><c> info</c><00:02:31.560><c> on</c><00:02:31.720><c> each</c><00:02:32.360><c> nicely</c><00:02:32.720><c> formatted</c><00:02:33.239><c> to</c>

00:02:33.350 --> 00:02:33.360 align:start position:0%
ton of info on each nicely formatted to
 

00:02:33.360 --> 00:02:35.190 align:start position:0%
ton of info on each nicely formatted to
give<00:02:33.519><c> us</c><00:02:33.640><c> a</c><00:02:33.760><c> great</c><00:02:34.040><c> perspective</c><00:02:34.519><c> on</c><00:02:34.720><c> the</c>

00:02:35.190 --> 00:02:35.200 align:start position:0%
give us a great perspective on the
 

00:02:35.200 --> 00:02:36.910 align:start position:0%
give us a great perspective on the
behind<00:02:35.519><c> the-scenes</c><00:02:35.800><c> architecture</c><00:02:36.560><c> of</c><00:02:36.680><c> our</c>

00:02:36.910 --> 00:02:36.920 align:start position:0%
behind the-scenes architecture of our
 

00:02:36.920 --> 00:02:38.670 align:start position:0%
behind the-scenes architecture of our
object<00:02:37.239><c> models</c><00:02:37.599><c> and</c><00:02:37.720><c> their</c><00:02:37.959><c> relationships</c><00:02:38.519><c> to</c>

00:02:38.670 --> 00:02:38.680 align:start position:0%
object models and their relationships to
 

00:02:38.680 --> 00:02:40.830 align:start position:0%
object models and their relationships to
one<00:02:38.800><c> another</c><00:02:39.360><c> I'm</c><00:02:39.560><c> very</c><00:02:39.879><c> happy</c><00:02:40.200><c> with</c><00:02:40.400><c> how</c><00:02:40.599><c> this</c>

00:02:40.830 --> 00:02:40.840 align:start position:0%
one another I'm very happy with how this
 

00:02:40.840 --> 00:02:43.309 align:start position:0%
one another I'm very happy with how this
component<00:02:41.319><c> turned</c><00:02:41.640><c> out</c><00:02:42.440><c> yes</c><00:02:42.599><c> we're</c><00:02:42.800><c> all</c><00:02:43.000><c> so</c>

00:02:43.309 --> 00:02:43.319 align:start position:0%
component turned out yes we're all so
 

00:02:43.319 --> 00:02:47.470 align:start position:0%
component turned out yes we're all so
proud<00:02:43.560><c> of</c><00:02:43.720><c> you</c><00:02:44.280><c> nap</c><00:02:45.239><c> oh</c><00:02:45.800><c> okay</c><00:02:46.800><c> sorry</c><00:02:47.159><c> about</c>

00:02:47.470 --> 00:02:47.480 align:start position:0%
proud of you nap oh okay sorry about
 

00:02:47.480 --> 00:02:50.149 align:start position:0%
proud of you nap oh okay sorry about
that<00:02:48.040><c> our</c><00:02:48.239><c> last</c><00:02:48.480><c> tab</c><00:02:48.720><c> is</c><00:02:48.840><c> the</c><00:02:49.000><c> discussion</c><00:02:49.480><c> Tab</c>

00:02:50.149 --> 00:02:50.159 align:start position:0%
that our last tab is the discussion Tab
 

00:02:50.159 --> 00:02:52.710 align:start position:0%
that our last tab is the discussion Tab
and<00:02:50.280><c> you're</c><00:02:50.480><c> familiar</c><00:02:50.920><c> with</c><00:02:51.280><c> that</c><00:02:52.280><c> next</c><00:02:52.560><c> is</c>

00:02:52.710 --> 00:02:52.720 align:start position:0%
and you're familiar with that next is
 

00:02:52.720 --> 00:02:54.309 align:start position:0%
and you're familiar with that next is
the<00:02:52.840><c> file</c><00:02:53.120><c> management</c><00:02:53.640><c> tab</c><00:02:53.959><c> where</c><00:02:54.080><c> we</c><00:02:54.200><c> can</c>

00:02:54.309 --> 00:02:54.319 align:start position:0%
the file management tab where we can
 

00:02:54.319 --> 00:02:57.390 align:start position:0%
the file management tab where we can
edit<00:02:54.560><c> and</c><00:02:54.720><c> delete</c><00:02:55.080><c> individual</c><00:02:55.560><c> yaml</c><00:02:55.959><c> and</c><00:02:56.120><c> Json</c>

00:02:57.390 --> 00:02:57.400 align:start position:0%
edit and delete individual yaml and Json
 

00:02:57.400 --> 00:02:59.589 align:start position:0%
edit and delete individual yaml and Json
files<00:02:58.400><c> based</c><00:02:58.640><c> on</c><00:02:58.760><c> the</c><00:02:58.920><c> feedback</c><00:02:59.360><c> I've</c><00:02:59.480><c> been</c>

00:02:59.589 --> 00:02:59.599 align:start position:0%
files based on the feedback I've been
 

00:02:59.599 --> 00:03:01.190 align:start position:0%
files based on the feedback I've been
getting<00:03:00.000><c> I'll</c><00:03:00.200><c> probably</c><00:03:00.560><c> phasee</c><00:03:00.879><c> out</c><00:03:01.080><c> the</c>

00:03:01.190 --> 00:03:01.200 align:start position:0%
getting I'll probably phasee out the
 

00:03:01.200 --> 00:03:04.070 align:start position:0%
getting I'll probably phasee out the
Amel<00:03:01.640><c> stuff</c><00:03:02.640><c> it's</c><00:03:02.920><c> probably</c><00:03:03.239><c> an</c><00:03:03.400><c> unnecessary</c>

00:03:04.070 --> 00:03:04.080 align:start position:0%
Amel stuff it's probably an unnecessary
 

00:03:04.080 --> 00:03:05.949 align:start position:0%
Amel stuff it's probably an unnecessary
intermediate<00:03:04.720><c> step</c><00:03:05.040><c> our</c><00:03:05.239><c> last</c><00:03:05.480><c> tab</c><00:03:05.680><c> is</c><00:03:05.799><c> the</c>

00:03:05.949 --> 00:03:05.959 align:start position:0%
intermediate step our last tab is the
 

00:03:05.959 --> 00:03:08.149 align:start position:0%
intermediate step our last tab is the
discussion<00:03:06.440><c> Tab</c><00:03:07.120><c> and</c><00:03:07.239><c> you're</c><00:03:07.440><c> familiar</c><00:03:07.879><c> with</c>

00:03:08.149 --> 00:03:08.159 align:start position:0%
discussion Tab and you're familiar with
 

00:03:08.159 --> 00:03:10.470 align:start position:0%
discussion Tab and you're familiar with
that<00:03:09.159><c> anyway</c><00:03:09.480><c> that's</c><00:03:09.680><c> the</c><00:03:09.840><c> status</c><00:03:10.159><c> of</c><00:03:10.280><c> our</c>

00:03:10.470 --> 00:03:10.480 align:start position:0%
that anyway that's the status of our
 

00:03:10.480 --> 00:03:12.789 align:start position:0%
that anyway that's the status of our
revised<00:03:10.959><c> version</c><00:03:11.239><c> 2</c><00:03:11.599><c> Ultram</c><00:03:12.040><c> modular</c><00:03:12.480><c> Super</c>

00:03:12.789 --> 00:03:12.799 align:start position:0%
revised version 2 Ultram modular Super
 

00:03:12.799 --> 00:03:15.149 align:start position:0%
revised version 2 Ultram modular Super
upgrade<00:03:13.200><c> to</c><00:03:13.400><c> Auto</c><00:03:13.760><c> Gro</c><00:03:14.360><c> it's</c><00:03:14.519><c> shaping</c><00:03:14.879><c> up</c><00:03:15.000><c> to</c>

00:03:15.149 --> 00:03:15.159 align:start position:0%
upgrade to Auto Gro it's shaping up to
 

00:03:15.159 --> 00:03:17.070 align:start position:0%
upgrade to Auto Gro it's shaping up to
be<00:03:15.480><c> and</c><00:03:15.599><c> to</c><00:03:15.799><c> do</c><00:03:16.080><c> everything</c><00:03:16.400><c> I</c><00:03:16.560><c> imagined</c>

00:03:17.070 --> 00:03:17.080 align:start position:0%
be and to do everything I imagined
 

00:03:17.080 --> 00:03:18.110 align:start position:0%
be and to do everything I imagined
everything<00:03:17.400><c> I</c>

00:03:18.110 --> 00:03:18.120 align:start position:0%
everything I
 

00:03:18.120 --> 00:03:20.190 align:start position:0%
everything I
wanted<00:03:19.120><c> there's</c><00:03:19.360><c> just</c><00:03:19.560><c> one</c><00:03:19.720><c> more</c><00:03:19.920><c> thing</c><00:03:20.080><c> I</c>

00:03:20.190 --> 00:03:20.200 align:start position:0%
wanted there's just one more thing I
 

00:03:20.200 --> 00:03:22.110 align:start position:0%
wanted there's just one more thing I
wanted<00:03:20.440><c> to</c><00:03:20.560><c> let</c><00:03:20.680><c> you</c><00:03:20.840><c> know</c><00:03:21.000><c> and</c><00:03:21.120><c> it's</c><00:03:21.280><c> kind</c><00:03:21.440><c> of</c>

00:03:22.110 --> 00:03:22.120 align:start position:0%
wanted to let you know and it's kind of
 

00:03:22.120 --> 00:03:24.710 align:start position:0%
wanted to let you know and it's kind of
important<00:03:23.120><c> iing</c><00:03:23.920><c> hate</c>

00:03:24.710 --> 00:03:24.720 align:start position:0%
important iing hate
 

00:03:24.720 --> 00:03:27.509 align:start position:0%
important iing hate
it<00:03:25.720><c> that's</c><00:03:26.080><c> uh</c><00:03:26.239><c> not</c><00:03:26.400><c> your</c><00:03:26.599><c> best</c><00:03:26.799><c> sales</c><00:03:27.200><c> pitch</c>

00:03:27.509 --> 00:03:27.519 align:start position:0%
it that's uh not your best sales pitch
 

00:03:27.519 --> 00:03:30.670 align:start position:0%
it that's uh not your best sales pitch
there<00:03:27.680><c> Sparky</c><00:03:28.640><c> I</c><00:03:28.840><c> just</c><00:03:29.120><c> do</c><00:03:30.159><c> I</c><00:03:30.319><c> hate</c><00:03:30.519><c> the</c>

00:03:30.670 --> 00:03:30.680 align:start position:0%
there Sparky I just do I hate the
 

00:03:30.680 --> 00:03:32.350 align:start position:0%
there Sparky I just do I hate the
interface<00:03:31.200><c> I</c><00:03:31.319><c> hate</c><00:03:31.560><c> having</c><00:03:31.760><c> to</c><00:03:31.920><c> click</c><00:03:32.200><c> all</c>

00:03:32.350 --> 00:03:32.360 align:start position:0%
interface I hate having to click all
 

00:03:32.360 --> 00:03:35.830 align:start position:0%
interface I hate having to click all
over<00:03:32.599><c> the</c><00:03:32.760><c> place</c><00:03:33.040><c> I've</c><00:03:33.239><c> never</c><00:03:33.480><c> been</c><00:03:33.680><c> a</c><00:03:33.799><c> GUI</c><00:03:34.840><c> guy</c>

00:03:35.830 --> 00:03:35.840 align:start position:0%
over the place I've never been a GUI guy
 

00:03:35.840 --> 00:03:37.869 align:start position:0%
over the place I've never been a GUI guy
plus<00:03:36.120><c> the</c><00:03:36.239><c> auto</c><00:03:36.560><c> grock</c><00:03:36.879><c> with</c><00:03:37.000><c> a</c><00:03:37.159><c> k</c><00:03:37.439><c> thing</c><00:03:37.599><c> is</c>

00:03:37.869 --> 00:03:37.879 align:start position:0%
plus the auto grock with a k thing is
 

00:03:37.879 --> 00:03:40.470 align:start position:0%
plus the auto grock with a k thing is
stupid<00:03:38.840><c> auto</c><00:03:39.200><c> Gro</c><00:03:39.480><c> with</c><00:03:39.599><c> a</c><00:03:39.760><c> queue</c><00:03:40.080><c> has</c><00:03:40.280><c> all</c>

00:03:40.470 --> 00:03:40.480 align:start position:0%
stupid auto Gro with a queue has all
 

00:03:40.480 --> 00:03:42.670 align:start position:0%
stupid auto Gro with a queue has all
kinds<00:03:40.720><c> of</c><00:03:40.920><c> momentum</c><00:03:41.319><c> and</c><00:03:41.519><c> recognition</c><00:03:42.519><c> now</c>

00:03:42.670 --> 00:03:42.680 align:start position:0%
kinds of momentum and recognition now
 

00:03:42.680 --> 00:03:45.630 align:start position:0%
kinds of momentum and recognition now
who<00:03:42.799><c> needs</c><00:03:43.000><c> a</c><00:03:43.200><c> appp</c><00:03:43.959><c> I</c><00:03:44.040><c> mean</c><00:03:44.920><c> what</c><00:03:45.080><c> was</c><00:03:45.200><c> I</c>

00:03:45.630 --> 00:03:45.640 align:start position:0%
who needs a appp I mean what was I
 

00:03:45.640 --> 00:03:48.270 align:start position:0%
who needs a appp I mean what was I
thinking<00:03:46.640><c> sure</c><00:03:46.959><c> I</c><00:03:47.080><c> like</c><00:03:47.280><c> the</c><00:03:47.439><c> enhanced</c><00:03:47.879><c> debug</c>

00:03:48.270 --> 00:03:48.280 align:start position:0%
thinking sure I like the enhanced debug
 

00:03:48.280 --> 00:03:49.830 align:start position:0%
thinking sure I like the enhanced debug
feature<00:03:48.599><c> and</c><00:03:48.760><c> the</c><00:03:48.840><c> one</c><00:03:49.040><c> to</c><00:03:49.159><c> many</c><00:03:49.439><c> top</c><00:03:49.680><c> to</c>

00:03:49.830 --> 00:03:49.840 align:start position:0%
feature and the one to many top to
 

00:03:49.840 --> 00:03:52.110 align:start position:0%
feature and the one to many top to
bottom<00:03:50.120><c> architecture</c><00:03:50.799><c> rocks</c><00:03:51.799><c> but</c><00:03:51.959><c> this</c>

00:03:52.110 --> 00:03:52.120 align:start position:0%
bottom architecture rocks but this
 

00:03:52.120 --> 00:03:54.630 align:start position:0%
bottom architecture rocks but this
rebranding<00:03:52.640><c> and</c><00:03:52.799><c> new</c><00:03:52.959><c> interface</c><00:03:53.400><c> got</c><00:03:53.519><c> to</c><00:03:53.680><c> go</c>

00:03:54.630 --> 00:03:54.640 align:start position:0%
rebranding and new interface got to go
 

00:03:54.640 --> 00:03:56.869 align:start position:0%
rebranding and new interface got to go
they<00:03:54.840><c> suck</c><00:03:55.640><c> how</c><00:03:55.799><c> come</c><00:03:55.920><c> you</c><00:03:56.040><c> get</c><00:03:56.200><c> mad</c><00:03:56.400><c> at</c><00:03:56.519><c> me</c><00:03:56.680><c> for</c>

00:03:56.869 --> 00:03:56.879 align:start position:0%
they suck how come you get mad at me for
 

00:03:56.879 --> 00:03:59.110 align:start position:0%
they suck how come you get mad at me for
saying<00:03:57.120><c> you</c><00:03:57.280><c> suck</c><00:03:58.120><c> that's</c><00:03:58.319><c> not</c><00:03:58.480><c> your</c><00:03:58.760><c> job</c><00:03:59.040><c> I</c>

00:03:59.110 --> 00:03:59.120 align:start position:0%
saying you suck that's not your job I
 

00:03:59.120 --> 00:04:01.750 align:start position:0%
saying you suck that's not your job I
have<00:03:59.239><c> a</c><00:03:59.400><c> wife</c><00:03:59.799><c> for</c><00:04:00.079><c> that</c><00:04:01.079><c> okay</c><00:04:01.239><c> so</c><00:04:01.439><c> what's</c><00:04:01.560><c> your</c>

00:04:01.750 --> 00:04:01.760 align:start position:0%
have a wife for that okay so what's your
 

00:04:01.760 --> 00:04:03.990 align:start position:0%
have a wife for that okay so what's your
plan<00:04:02.079><c> boss</c><00:04:02.400><c> man</c><00:04:03.040><c> what</c><00:04:03.200><c> I'll</c><00:04:03.360><c> do</c><00:04:03.519><c> is</c><00:04:03.680><c> take</c><00:04:03.840><c> the</c>

00:04:03.990 --> 00:04:04.000 align:start position:0%
plan boss man what I'll do is take the
 

00:04:04.000 --> 00:04:05.789 align:start position:0%
plan boss man what I'll do is take the
stuff<00:04:04.239><c> I</c><00:04:04.360><c> like</c><00:04:04.560><c> in</c><00:04:04.680><c> the</c><00:04:04.799><c> new</c><00:04:05.000><c> architecture</c><00:04:05.599><c> and</c>

00:04:05.789 --> 00:04:05.799 align:start position:0%
stuff I like in the new architecture and
 

00:04:05.799 --> 00:04:07.630 align:start position:0%
stuff I like in the new architecture and
incorporate<00:04:06.280><c> it</c><00:04:06.439><c> into</c><00:04:06.640><c> the</c><00:04:06.799><c> original</c><00:04:07.239><c> autog</c>

00:04:07.630 --> 00:04:07.640 align:start position:0%
incorporate it into the original autog
 

00:04:07.640 --> 00:04:10.429 align:start position:0%
incorporate it into the original autog
grock<00:04:08.120><c> GUI</c><00:04:09.120><c> people</c><00:04:09.360><c> love</c><00:04:09.640><c> the</c><00:04:09.760><c> original</c><00:04:10.120><c> autog</c>

00:04:10.429 --> 00:04:10.439 align:start position:0%
grock GUI people love the original autog
 

00:04:10.439 --> 00:04:12.910 align:start position:0%
grock GUI people love the original autog
Gro<00:04:11.239><c> this</c><00:04:11.360><c> autog</c><00:04:11.680><c> Gro</c><00:04:11.959><c> with</c><00:04:12.040><c> a</c><00:04:12.200><c> K</c><00:04:12.519><c> site</c><00:04:12.760><c> will</c>

00:04:12.910 --> 00:04:12.920 align:start position:0%
Gro this autog Gro with a K site will
 

00:04:12.920 --> 00:04:15.229 align:start position:0%
Gro this autog Gro with a K site will
continue<00:04:13.280><c> to</c><00:04:13.400><c> be</c><00:04:13.560><c> my</c><00:04:13.760><c> online</c><00:04:14.239><c> Sandbox</c>

00:04:15.229 --> 00:04:15.239 align:start position:0%
continue to be my online Sandbox
 

00:04:15.239 --> 00:04:17.069 align:start position:0%
continue to be my online Sandbox
streamlit<00:04:15.840><c> Apps</c><00:04:16.120><c> often</c><00:04:16.400><c> act</c><00:04:16.639><c> differently</c>

00:04:17.069 --> 00:04:17.079 align:start position:0%
streamlit Apps often act differently
 

00:04:17.079 --> 00:04:19.749 align:start position:0%
streamlit Apps often act differently
online<00:04:17.799><c> that</c><00:04:17.959><c> they</c><00:04:18.079><c> do</c><00:04:18.280><c> locally</c><00:04:19.000><c> so</c><00:04:19.280><c> we</c><00:04:19.440><c> need</c>

00:04:19.749 --> 00:04:19.759 align:start position:0%
online that they do locally so we need
 

00:04:19.759 --> 00:04:22.230 align:start position:0%
online that they do locally so we need
this<00:04:20.199><c> resource</c><00:04:21.199><c> so</c><00:04:21.479><c> you're</c><00:04:21.639><c> going</c><00:04:21.759><c> to</c><00:04:21.959><c> dance</c>

00:04:22.230 --> 00:04:22.240 align:start position:0%
this resource so you're going to dance
 

00:04:22.240 --> 00:04:24.629 align:start position:0%
this resource so you're going to dance
with<00:04:22.400><c> the</c><00:04:22.520><c> girl</c><00:04:22.759><c> that</c><00:04:22.919><c> BR</c><00:04:23.280><c> you</c><00:04:23.919><c> in</c><00:04:24.040><c> a</c><00:04:24.199><c> manner</c><00:04:24.440><c> of</c>

00:04:24.629 --> 00:04:24.639 align:start position:0%
with the girl that BR you in a manner of
 

00:04:24.639 --> 00:04:26.870 align:start position:0%
with the girl that BR you in a manner of
speaking<00:04:25.120><c> I</c><00:04:25.280><c> suppose</c><00:04:26.040><c> ride</c><00:04:26.199><c> out</c><00:04:26.360><c> on</c><00:04:26.479><c> the</c><00:04:26.639><c> horse</c>

00:04:26.870 --> 00:04:26.880 align:start position:0%
speaking I suppose ride out on the horse
 

00:04:26.880 --> 00:04:28.870 align:start position:0%
speaking I suppose ride out on the horse
you<00:04:27.040><c> rode</c><00:04:27.280><c> in</c><00:04:27.400><c> on</c><00:04:27.680><c> enough</c><00:04:27.919><c> with</c><00:04:28.080><c> the</c><00:04:28.199><c> analogies</c>

00:04:28.870 --> 00:04:28.880 align:start position:0%
you rode in on enough with the analogies
 

00:04:28.880 --> 00:04:30.469 align:start position:0%
you rode in on enough with the analogies
swimming<00:04:29.199><c> Upstream</c><00:04:29.560><c> to</c><00:04:29.759><c> spawn</c><00:04:30.199><c> that</c><00:04:30.320><c> one</c>

00:04:30.469 --> 00:04:30.479 align:start position:0%
swimming Upstream to spawn that one
 

00:04:30.479 --> 00:04:31.670 align:start position:0%
swimming Upstream to spawn that one
doesn't<00:04:30.800><c> even</c><00:04:31.080><c> make</c>

00:04:31.670 --> 00:04:31.680 align:start position:0%
doesn't even make
 

00:04:31.680 --> 00:04:37.230 align:start position:0%
doesn't even make
[Music]

00:04:37.230 --> 00:04:37.240 align:start position:0%
 
 

00:04:37.240 --> 00:04:45.310 align:start position:0%
 
sense<00:04:38.240><c> Auto</c><00:04:39.400><c> auto</c><00:04:40.400><c> auto</c><00:04:40.960><c> auto</c><00:04:41.840><c> auto</c><00:04:42.960><c> auto</c><00:04:44.320><c> auto</c>

00:04:45.310 --> 00:04:45.320 align:start position:0%
sense Auto auto auto auto auto auto auto
 

00:04:45.320 --> 00:04:48.320 align:start position:0%
sense Auto auto auto auto auto auto auto
auto

