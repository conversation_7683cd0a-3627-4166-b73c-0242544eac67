import os
import yt_dlp
from dotenv import load_dotenv
import glob

# Load environment variables
load_dotenv()

# Define all necessary variables here
video_links = [
    #"http://vimeo.com/674056047/e5a554dd73",
    "http://vimeo.com/776501868",
]

base_download_folder = "D:\\1 - Youtube_Vimeo_Videos\\LiveDiligence_Vimeo\\"
download_video = "n"
download_transcript_flag = "y"

failed_downloads = []

def sanitize_filename(filename):
    return "".join([c for c in filename if c.isalpha() or c.isdigit() or c in (' ', '.', '_', '-')]).rstrip()

def video_exists(output_path, title):
    """
    Check if a video with the given title already exists in the output path.
    """
    sanitized_title = sanitize_filename(title)
    existing_files = glob.glob(os.path.join(output_path, f"{sanitized_title}.*"))
    return len(existing_files) > 0

def download_video(url, output_path):
    ydl_opts = {
        'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
        'no_check_certificates': True,
        'no_warnings': False,  # Enable warnings for debugging
        'quiet': False,  # Disable quiet mode for more output
        'no_call_home': True,
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'referer': 'http://vimeo.com/',
        'extractor_args': {'vimeo': {'http_headers': {'Referer': 'http://vimeo.com/'}}},
        'downloader_args': {'http': ['--add-header', 'Referer: http://vimeo.com/']},
        'external_downloader_args': ['ffmpeg_i', '-headers', 'Referer: http://vimeo.com/'],
        'compat_opts': {'no-youtube-unavailable-videos'},
        'extractor_retries': 5,
        'file_access_retries': 5,
        'fragment_retries': 5,
        'skip_download_archive': True,
        'overwrites': True,
        'http_headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-us,en;q=0.5',
            'Sec-Fetch-Mode': 'navigate',
            'Referer': 'http://vimeo.com/'
        },
        'verbose': True,  # Enable verbose output for debugging
    }
    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            try:
                title = info.get('title')
                if not title:
                    raise ValueError('Title missing in primary extraction')
            except Exception as ex:
                print(f"Error extracting title: {ex}. Falling back using yt_dlp default.")
                with yt_dlp.YoutubeDL({}) as fallback_ydl:
                    info = fallback_ydl.extract_info(url, download=False)
                title = info.get('title', 'unknown_video')
            
            # Check if the video already exists
            if video_exists(output_path, title):
                print(f"Video '{title}' already exists. Skipping download.")
                return True, title

            available_formats = info.get('formats', [])
            
            if not available_formats:
                print(f"No formats available for {url}")
                return False, None

            # Sort formats by quality (assuming higher resolution is better quality)
            available_formats.sort(key=lambda x: x.get('height', 0), reverse=True)
            
            # Select the best available format
            best_format = next((f for f in available_formats if f.get('ext') == 'mp4'), available_formats[0])
            
            ydl_opts['format'] = best_format['format_id']
            ydl.download([url])
            print(f"File downloaded to: {os.path.join(output_path, sanitize_filename(title))}.mp4")
            return True, info.get('title')
    except Exception as e:
        print(f"Error downloading {url}: {str(e)}")
        return False, None

def download_and_transcribe(video_url):
    try:
        ydl_opts = {
            'no_check_certificates': True,
            'no_warnings': False,
            'quiet': False,
            'no_call_home': True,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
            'referer': 'http://vimeo.com/',
            'extractor_args': {'vimeo': {'http_headers': {'Referer': 'http://vimeo.com/'}}},
            'downloader_args': {'http': ['--add-header', 'Referer: http://vimeo.com/']},
            'external_downloader_args': ['ffmpeg_i', '-headers', 'Referer: http://vimeo.com/'],
            'compat_opts': {'no-youtube-unavailable-videos'},
            'extractor_retries': 5,
            'file_access_retries': 5,
            'fragment_retries': 5,
            'skip_download_archive': True,
            'overwrites': True,
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.85 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-us,en;q=0.5',
                'Sec-Fetch-Mode': 'navigate',
                'Referer': 'http://vimeo.com/'
            },
            'verbose': True,
            'outtmpl': os.path.join(base_download_folder, '%(title)s.%(ext)s'),
        }

        # Add options for transcript download if enabled
        if download_transcript_flag.lower() == 'y':
            ydl_opts.update({
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitlesformat': 'srt',
                'skip_download': True  # Skip video download when only getting transcripts
            })

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(video_url, download=False)
            title = info.get('title')
            sanitized_title = sanitize_filename(title)
            
            file_path = os.path.join(base_download_folder, f"{sanitized_title}.mp4")
            transcript_path = os.path.join(base_download_folder, f"{sanitized_title}.srt")

            if download_video.lower() == 'y':
                if os.path.exists(file_path):
                    print(f"Video '{title}' already exists at {file_path}. Skipping download.")
                else:
                    print(f"Downloading video: {title}")
                    ydl.download([video_url])
            
            if download_transcript_flag.lower() == 'y':
                if os.path.exists(transcript_path):
                    print(f"Transcript for '{title}' already exists at {transcript_path}. Skipping download.")
                else:
                    print(f"Downloading transcript for: {title}")
                    ydl.download([video_url])

            return True

    except Exception as e:
        print(f"Failed to process video from URL: {video_url}")
        print(f"Error: {str(e)}")
        failed_downloads.append(video_url)
        return False

for download_link in video_links:
    print(f"Processing video: {download_link}")
    download_and_transcribe(download_link)

if failed_downloads:
    print("Some downloads failed.")
    print("Failed URLs:", failed_downloads)
else:
    print("All downloads completed successfully!")