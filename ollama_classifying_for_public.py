import os
import sys
import platform
import subprocess
import time
import unicodedata
import json
import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from tenacity import retry, stop_after_attempt, wait_exponential
import tiktoken
from dotenv import load_dotenv
from rich import print
from rich.console import Console
from rich.progress import Progress

# Configure console encoding for Windows
if platform.system() == 'Windows':
    # Enable unicode output in Windows console
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8')
    if sys.stderr.encoding != 'utf-8':
        sys.stderr.reconfigure(encoding='utf-8')
    # Ensure Windows console is in UTF-8 mode
    os.system('chcp 65001')

from supabase import create_client, Client
from supabase.client import ClientOptions

load_dotenv()

# Prompt template for classification
classification_prompt_template = """
You are an AI content classifier for a renewable energy advisory SaaS website. Your task is to determine if a YouTube video's content should be displayed to our clients.

Your response must be either "TRUE" or "FALSE" only.

CLASSIFICATION RULES:
- Return "FALSE" if the video is offering or marketing:
  * Software solutions
  * Data services 
  * Reports or data analysis services
  * Consulting services
  * Advisory services
  * Any other commercial services that could compete with our business

- Return "FALSE" if the video is completely unrelated to the energy industry

- Return "TRUE" for all other renewable energy content, including:
  * Educational content about renewable energy technologies
  * Industry news and updates
  * Technical discussions about energy systems
  * Policy and regulatory discussions
  * Market analysis and trends (not offering data services)
  * Research findings and studies
  * Even content from competitors that is educational/informational rather than promotional

CONTENT TO ANALYZE:

Video Information:
Channel: {channel_name}
Title: {title}
Published Date: {published_at}

Transcript Excerpt:
{transcript_excerpt}

Key Insights from Previous Analysis:
{insights}

Based on the classification rules above, should this content be displayed on our renewable energy advisory website?

Answer with "TRUE" or "FALSE" only.
"""


def check_and_start_ollama(ollama_command_str):
    """
    Checks if ollama.exe is running. If not, attempts to start it.
    """
    try:
        # Check if ollama.exe is running (Windows specific)
        output = subprocess.check_output('tasklist', shell=True, text=True, universal_newlines=True)
        if 'ollama.exe' in output.lower():
            print("Ollama is already running.")
            logging.info("Ollama is already running.")
            return True
        else:
            print("Ollama is not running. Attempting to start it...")
            logging.info("Ollama is not running. Attempting to start it...")
            
            # Parse the command string
            if ' serve' in ollama_command_str:
                parts = ollama_command_str.rsplit(' serve', 1)
                executable_path = parts[0]
                command_args = ['serve']
            else:
                executable_path = ollama_command_str
                command_args = []
            
            command_parts = [executable_path] + command_args
            print(f"DEBUG: executable_path: '{executable_path}', command_args: {command_args}")

            # Check if the executable exists
            if not os.path.exists(executable_path):
                print(f"Error: The executable '{executable_path}' does not exist at that path.")
                logging.error(f"Executable '{executable_path}' does not exist.")
                return False 
            
            executable_dir = os.path.dirname(executable_path)
            
            print(f"Attempting to start Ollama. Executable: '{executable_path}', Arguments: {command_parts[1:] if len(command_parts) > 1 else 'None'}")
            logging.info(f"Attempting to start Ollama. Executable: '{executable_path}', Arguments: {command_parts[1:] if len(command_parts) > 1 else 'None'}")
            
            # Try to start Ollama
            process = subprocess.Popen(command_parts, 
                                     creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NO_WINDOW, 
                                     shell=False,
                                     cwd=executable_dir)
            print(f"Ollama process started with PID: {process.pid}. Waiting for initialization...")
            logging.info(f"Ollama process started with PID: {process.pid}. Waiting for initialization.")
            time.sleep(10)  # Wait for Ollama to initialize
            
            # Re-check if ollama.exe is running
            output_after_start = subprocess.check_output('tasklist', shell=True, text=True, universal_newlines=True)
            if 'ollama.exe' in output_after_start.lower():
                print("Ollama successfully started and is now running.")
                logging.info("Ollama successfully started and is now running.")
                return True
            else:
                print("Failed to confirm Ollama is running after attempting to start.")
                logging.error("Failed to confirm Ollama is running after attempting to start.")
                return False

    except Exception as e:
        print(f"An unexpected error occurred while checking or starting Ollama: {e}")
        logging.error(f"Unexpected error with Ollama check/start: {e}", exc_info=True)
        return False


# Initialize Supabase client
url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SERVICE_ROLE_KEY")
supabase: Client = create_client(url, key,
  options=ClientOptions(
    schema="public",
    headers={},
    auto_refresh_token=True,
    persist_session=True,
    storage=None,
    realtime=None,
    postgrest_client_timeout=999999,
    storage_client_timeout=999999,
    flow_type=None
  ))

# Constants
TABLE_TO_PROCESS = "youtube_renewable_energy"
CHOSEN_MODEL = "gemma3:4b"  # Use the same model as the original script
MAX_PROCESSING_ATTEMPTS = 3
PARALLEL_REQUESTS = 1

# Token limits
MODEL_TOTAL_CONTEXT_WINDOW = 128000
MAX_OUTPUT_TOKENS = 100  # We only need TRUE/FALSE
SAFETY_MARGIN_TOKENS = 500
MAX_INPUT_PROMPT_TOKENS = MODEL_TOTAL_CONTEXT_WINDOW - MAX_OUTPUT_TOKENS - SAFETY_MARGIN_TOKENS

def count_tokens(text):
    encoding = tiktoken.get_encoding("cl100k_base")
    return len(encoding.encode(text))

def sanitize_text_for_prompt(text):
    """Sanitizes text to prevent encoding issues and characters problematic for JSON in prompts."""
    if not text:
        return ""
    
    # Normalize unicode characters to their closest ASCII representation
    text = unicodedata.normalize('NFKD', text).encode('ascii', 'ignore').decode('utf-8')
    
    # Replace problematic characters
    replacements = {
        '…': '...', # Ellipsis
        '–': '-',   # En dash
        '—': '-',   # Em dash
        '"': '"',   # Smart quotes
        '"': '"',
        ''': "'",
        ''': "'",
    }

    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text

def truncate_transcript_intelligently(transcript, insights_text, channel_name, title, published_at):
    """Truncate transcript to fit within token limits while maximizing content."""
    # Create a base prompt template to calculate overhead
    base_prompt = classification_prompt_template.format(
        channel_name=channel_name,
        title=title,
        published_at=published_at,
        transcript_excerpt="",
        insights=insights_text
    )
    
    base_tokens = count_tokens(base_prompt)
    available_tokens_for_transcript = MAX_INPUT_PROMPT_TOKENS - base_tokens
    
    if available_tokens_for_transcript <= 100:
        print("Warning: Very little space available for transcript after base prompt")
        return transcript[:500]  # Fallback to minimal transcript
    
    # Start with full transcript and truncate if needed
    current_transcript = transcript
    current_tokens = count_tokens(current_transcript)
    
    if current_tokens <= available_tokens_for_transcript:
        print(f"Using full transcript ({current_tokens} tokens)")
        return current_transcript
    
    # Need to truncate - estimate character count
    estimated_chars = int(available_tokens_for_transcript * 3.5)  # ~3.5 chars per token
    truncated_transcript = transcript[:estimated_chars]
    
    # Fine-tune to exact token limit
    while count_tokens(truncated_transcript) > available_tokens_for_transcript and len(truncated_transcript) > 100:
        truncated_transcript = truncated_transcript[:len(truncated_transcript) - 100]
    
    # Try to end at a sentence boundary for better context
    if len(truncated_transcript) < len(transcript):
        last_period = truncated_transcript.rfind('.')
        last_exclamation = truncated_transcript.rfind('!')
        last_question = truncated_transcript.rfind('?')
        
        sentence_end = max(last_period, last_exclamation, last_question)
        if sentence_end > len(truncated_transcript) * 0.8:  # Only if we don't lose too much
            truncated_transcript = truncated_transcript[:sentence_end + 1]
    
    final_tokens = count_tokens(truncated_transcript)
    print(f"Truncated transcript to {final_tokens} tokens (was {current_tokens})")
    
    return truncated_transcript

def extract_insights_from_llm_response(llm_response):
    """Extract insights from the llm_response JSONB field."""
    if not llm_response:
        return []
    
    try:
        if isinstance(llm_response, str):
            response_dict = json.loads(llm_response)
        else:
            response_dict = llm_response
            
        insights = response_dict.get('insights', [])
        
        # Convert insights to strings if they are dictionaries
        string_insights = []
        for insight in insights:
            if isinstance(insight, dict):
                # If insight is a dict, try to get a meaningful string representation
                if 'content' in insight:
                    string_insights.append(insight['content'])
                elif 'text' in insight:
                    string_insights.append(insight['text'])
                elif 'insight' in insight:
                    string_insights.append(insight['insight'])
                else:
                    # Fallback: convert dict to string
                    string_insights.append(str(insight))
            elif isinstance(insight, str):
                string_insights.append(insight)
            else:
                # Convert other types to string
                string_insights.append(str(insight))
        
        return string_insights
    except (json.JSONDecodeError, TypeError):
        return []

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=3))
async def query_ollama_async(prompt, session):
    """Query Ollama API for classification."""
    try:
        payload = {
            "model": CHOSEN_MODEL,
            "prompt": prompt,
            "stream": False,
            "options": {
                "num_predict": MAX_OUTPUT_TOKENS,
                "temperature": 0.1,  # Low temperature for consistent classification
                "top_p": 0.9,
                "num_ctx": MODEL_TOTAL_CONTEXT_WINDOW
            }
        }
        
        async with session.post("http://localhost:11434/api/generate", json=payload, timeout=300) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("response", "").strip()
            else:
                response.raise_for_status()
                
    except aiohttp.ClientResponseError as e:
        print(f"HTTP error during Ollama request: {e}")
        raise
    except asyncio.TimeoutError:
        print("Timeout during Ollama request")
        raise
    except Exception as e:
        print(f"Unexpected error during Ollama request: {e}")
        raise

def validate_classification_response(response):
    """Validate that the response is TRUE or FALSE."""
    response = response.strip().upper()
    if response in ["TRUE", "FALSE"]:
        return response == "TRUE"
    else:
        print(f"Invalid classification response: {response}")
        return None

async def add_classification_columns():
    """Add the new columns to the table if they don't exist."""
    print("Checking if classification columns exist...")
    
    # Check if columns exist by trying to select them
    try:
        # Try to select the columns to see if they exist
        response = supabase.table(TABLE_TO_PROCESS)\
            .select("ai_eval_visibility, visibility_congruent")\
            .limit(1)\
            .execute()
        print("Classification columns already exist")
        return True
    except Exception as e:
        if "does not exist" in str(e).lower():
            print("Classification columns don't exist, need to add them manually")
            print("Please run the following SQL commands in your Supabase SQL editor:")
            print(f"ALTER TABLE {TABLE_TO_PROCESS} ADD COLUMN IF NOT EXISTS ai_eval_visibility BOOLEAN;")
            print(f"ALTER TABLE {TABLE_TO_PROCESS} ADD COLUMN IF NOT EXISTS visibility_congruent BOOLEAN;")
            print("\nAfter running these commands, restart the script.")
            return False
        else:
            print(f"Error checking columns: {e}")
            return False

async def process_row_async(row, session):
    """Process a single row for classification."""
    video_id = row['video_id']
    print(f"Processing video_id: {video_id}")
    
    # Skip if already processed
    if row.get('ai_eval_visibility') is not None:
        print(f"Skipping video_id: {video_id} - already classified")
        return
    
    # Get transcript excerpt
    transcript = row.get('transcript', '')
    
    # Check for empty, missing, or barely any content transcripts
    if not transcript or transcript.strip() == "" or transcript == "Transcript not available":
        print(f"Video_id: {video_id} - no transcript available, marking as FALSE")
        supabase.table(TABLE_TO_PROCESS).update({
            "ai_eval_visibility": False,
            "visibility_congruent": row.get('is_visible', True) == False
        }).eq("id", row['id']).execute()
        return
    
    # Check for transcripts with barely any content (less than 50 meaningful characters)
    clean_transcript = transcript.strip()
    meaningful_chars = len(''.join(c for c in clean_transcript if c.isalnum() or c.isspace()))
    
    if meaningful_chars < 50:
        print(f"Video_id: {video_id} - transcript too short ({meaningful_chars} meaningful chars), marking as FALSE")
        supabase.table(TABLE_TO_PROCESS).update({
            "ai_eval_visibility": False,
            "visibility_congruent": row.get('is_visible', True) == False
        }).eq("id", row['id']).execute()
        return
    
    # Extract insights from llm_response
    insights = extract_insights_from_llm_response(row.get('llm_response'))
    insights_text = "\n".join(insights) if insights else "No insights available"
    
    # Sanitize text inputs
    channel_name = sanitize_text_for_prompt(row.get('channel_name', ''))
    title = sanitize_text_for_prompt(row.get('title', ''))
    published_at = row.get('published_at', '')
    insights_text = sanitize_text_for_prompt(insights_text)
    
    # Intelligently truncate transcript to maximize content within token limits
    transcript_excerpt = truncate_transcript_intelligently(
        transcript, insights_text, channel_name, title, published_at
    )
    transcript_excerpt = sanitize_text_for_prompt(transcript_excerpt)
    
    # Create the classification prompt
    prompt = classification_prompt_template.format(
        channel_name=channel_name,
        title=title,
        published_at=published_at,
        transcript_excerpt=transcript_excerpt,
        insights=insights_text
    )
    
    # Final token check (should be within limits now)
    prompt_tokens = count_tokens(prompt)
    print(f"Final prompt tokens for video_id {video_id}: {prompt_tokens}")
    
    if prompt_tokens > MAX_INPUT_PROMPT_TOKENS:
        print(f"WARNING: Prompt still too long for video_id: {video_id}. Tokens: {prompt_tokens}")
        # Emergency truncation of transcript
        while prompt_tokens > MAX_INPUT_PROMPT_TOKENS and len(transcript_excerpt) > 500:
            transcript_excerpt = transcript_excerpt[:len(transcript_excerpt)//2]
            prompt = classification_prompt_template.format(
                channel_name=channel_name,
                title=title,
                published_at=published_at,
                transcript_excerpt=transcript_excerpt,
                insights=insights_text
            )
            prompt_tokens = count_tokens(prompt)
    
    # Try classification with retries
    for attempt in range(MAX_PROCESSING_ATTEMPTS):
        try:
            print(f"Classification attempt {attempt + 1} for video_id: {video_id}")
            
            # Query Ollama for classification
            response = await query_ollama_async(prompt, session)
            
            # Validate response
            classification_result = validate_classification_response(response)
            
            if classification_result is not None:
                # Compare with existing is_visible value
                is_visible = row.get('is_visible', True)
                visibility_congruent = (classification_result == is_visible)
                
                # Update database
                current_datetime = datetime.now().isoformat()
                update_data = {
                    "ai_eval_visibility": classification_result,
                    "visibility_congruent": visibility_congruent
                }
                
                supabase.table(TABLE_TO_PROCESS).update(update_data).eq("id", row['id']).execute()
                
                print(f"Successfully classified video_id: {video_id} - AI: {classification_result}, Original: {is_visible}, Congruent: {visibility_congruent}")
                return
                
            else:
                print(f"Invalid response for video_id: {video_id}, attempt {attempt + 1}: {response}")
                if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                    # Mark as False if all attempts failed
                    supabase.table(TABLE_TO_PROCESS).update({
                        "ai_eval_visibility": False,
                        "visibility_congruent": row.get('is_visible', True) == False
                    }).eq("id", row['id']).execute()
                    print(f"Marked video_id: {video_id} as False after {MAX_PROCESSING_ATTEMPTS} failed attempts")
                
        except Exception as e:
            print(f"Error processing video_id: {video_id}, attempt {attempt + 1}: {e}")
            if attempt == MAX_PROCESSING_ATTEMPTS - 1:
                # Mark as False if all attempts failed
                supabase.table(TABLE_TO_PROCESS).update({
                    "ai_eval_visibility": False,
                    "visibility_congruent": row.get('is_visible', True) == False
                }).eq("id", row['id']).execute()
                print(f"Marked video_id: {video_id} as False after {MAX_PROCESSING_ATTEMPTS} failed attempts due to errors")

async def process_table_async():
    """Process the renewable energy table for classification."""
    print(f"Processing table: {TABLE_TO_PROCESS}")
    
    # Check if columns exist
    if not await add_classification_columns():
        print("ERROR: Classification columns don't exist. Please add them manually and restart.")
        return
    
    page_size = 50  # Smaller batches for better handling
    processed_count = 0
    
    timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout
    async with aiohttp.ClientSession(timeout=timeout) as session:
        while True:
            print(f"Fetching next batch of unprocessed rows (batch size: {page_size})")
            
            # Get rows that need classification - always start from 0 since we only get unprocessed rows
            try:
                response = supabase.table(TABLE_TO_PROCESS)\
                    .select("*")\
                    .eq("processed", "completed")\
                    .is_("ai_eval_visibility", "null")\
                    .range(0, page_size - 1)\
                    .execute()
            except Exception as e:
                if "does not exist" in str(e).lower():
                    print("ERROR: ai_eval_visibility column doesn't exist. Please add it manually.")
                    return
                else:
                    raise e
            
            rows = response.data
            
            if not rows:
                print("No more unprocessed rows found.")
                break
            
            print(f"Processing {len(rows)} unprocessed rows")
            
            # Process rows
            for row in rows:
                await process_row_async(row, session)
                processed_count += 1
                
                if processed_count % 10 == 0:
                    print(f"Processed {processed_count} rows so far...")
            
            # If we got fewer rows than page_size, we're done
            if len(rows) < page_size:
                print("Reached end of unprocessed rows.")
                break
    
    print(f"Finished processing {processed_count} rows in table: {TABLE_TO_PROCESS}")

def get_classification_stats():
    """Get statistics about the classification results."""
    try:
        response = supabase.table(TABLE_TO_PROCESS)\
            .select("ai_eval_visibility, is_visible, visibility_congruent")\
            .not_.is_("ai_eval_visibility", "null")\
            .execute()
        
        rows = response.data
        total_classified = len(rows)
        
        if total_classified == 0:
            print("No classified rows found.")
            return
        
        ai_true = sum(1 for row in rows if row.get('ai_eval_visibility') == True)
        ai_false = sum(1 for row in rows if row.get('ai_eval_visibility') == False)
        
        original_true = sum(1 for row in rows if row.get('is_visible') == True)
        original_false = sum(1 for row in rows if row.get('is_visible') == False)
        
        congruent = sum(1 for row in rows if row.get('visibility_congruent') == True)
        not_congruent = sum(1 for row in rows if row.get('visibility_congruent') == False)
        
        print(f"\n=== CLASSIFICATION STATISTICS ===")
        print(f"Total classified: {total_classified}")
        print(f"AI Classification - TRUE: {ai_true}, FALSE: {ai_false}")
        print(f"Original is_visible - TRUE: {original_true}, FALSE: {original_false}")
        print(f"Congruent classifications: {congruent} ({congruent/total_classified*100:.1f}%)")
        print(f"Non-congruent classifications: {not_congruent} ({not_congruent/total_classified*100:.1f}%)")
        
    except Exception as e:
        print(f"Error getting statistics: {e}")

async def main_async():
    """Main async function."""
    start_time = time.time()
    
    print(f"Starting classification for table: {TABLE_TO_PROCESS}")
    print(f"Model: {CHOSEN_MODEL}")
    print(f"Max processing attempts: {MAX_PROCESSING_ATTEMPTS}")
    
    await process_table_async()
    
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\nTotal processing time: {total_time:.2f} seconds")
    
    # Show statistics
    get_classification_stats()

if __name__ == "__main__":
    # Check and start Ollama
    ollama_start_cmd = r"C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe serve"
    
    print(f"INFO: Using Ollama start command: '{ollama_start_cmd}'. Ensure this is correct for your system.")
    
    if not check_and_start_ollama(ollama_start_cmd):
        print("ERROR: Could not start Ollama. Please ensure it's installed and accessible.")
        sys.exit(1)
    else:
        print("SUCCESS: Ollama is running and ready.")
    
    # Run the main async function
    asyncio.run(main_async())
