WEBVTT
Kind: captions
Language: en

00:00:00.680 --> 00:00:02.190 align:start position:0%
 
hey<00:00:00.880><c> everyone</c><00:00:01.160><c> it's</c><00:00:01.319><c> s</c><00:00:01.760><c> and</c><00:00:01.839><c> today</c><00:00:02.040><c> let's</c>

00:00:02.190 --> 00:00:02.200 align:start position:0%
hey everyone it's s and today let's
 

00:00:02.200 --> 00:00:04.269 align:start position:0%
hey everyone it's s and today let's
build<00:00:02.440><c> a</c><00:00:02.600><c> team</c><00:00:02.800><c> of</c><00:00:03.040><c> Agents</c><00:00:03.480><c> using</c><00:00:03.760><c> the</c><00:00:03.879><c> newly</c>

00:00:04.269 --> 00:00:04.279 align:start position:0%
build a team of Agents using the newly
 

00:00:04.279 --> 00:00:06.990 align:start position:0%
build a team of Agents using the newly
release<00:00:04.680><c> gp4</c><00:00:05.359><c> model</c><00:00:06.279><c> we'll</c><00:00:06.560><c> start</c><00:00:06.839><c> by</c>

00:00:06.990 --> 00:00:07.000 align:start position:0%
release gp4 model we'll start by
 

00:00:07.000 --> 00:00:09.390 align:start position:0%
release gp4 model we'll start by
building<00:00:07.359><c> a</c><00:00:07.520><c> single</c><00:00:07.839><c> agent</c><00:00:08.240><c> by</c><00:00:08.360><c> giving</c><00:00:08.599><c> gbd4</c><00:00:09.280><c> a</c>

00:00:09.390 --> 00:00:09.400 align:start position:0%
building a single agent by giving gbd4 a
 

00:00:09.400 --> 00:00:11.470 align:start position:0%
building a single agent by giving gbd4 a
set<00:00:09.559><c> of</c><00:00:09.679><c> tools</c><00:00:10.440><c> then</c><00:00:10.599><c> we'll</c><00:00:10.880><c> extend</c><00:00:11.320><c> that</c>

00:00:11.470 --> 00:00:11.480 align:start position:0%
set of tools then we'll extend that
 

00:00:11.480 --> 00:00:14.350 align:start position:0%
set of tools then we'll extend that
agent<00:00:11.840><c> with</c><00:00:12.000><c> a</c><00:00:12.200><c> team</c><00:00:12.679><c> of</c><00:00:13.080><c> dedicated</c><00:00:13.799><c> agents</c><00:00:14.200><c> it</c>

00:00:14.350 --> 00:00:14.360 align:start position:0%
agent with a team of dedicated agents it
 

00:00:14.360 --> 00:00:16.550 align:start position:0%
agent with a team of dedicated agents it
can<00:00:14.559><c> delegate</c><00:00:15.000><c> tasks</c><00:00:15.400><c> to</c><00:00:16.000><c> so</c><00:00:16.160><c> when</c><00:00:16.279><c> it</c><00:00:16.359><c> needs</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
can delegate tasks to so when it needs
 

00:00:16.560 --> 00:00:18.189 align:start position:0%
can delegate tasks to so when it needs
to<00:00:16.720><c> write</c><00:00:16.920><c> and</c><00:00:17.080><c> run</c><00:00:17.320><c> Python</c><00:00:17.560><c> scripts</c><00:00:17.960><c> it'll</c>

00:00:18.189 --> 00:00:18.199 align:start position:0%
to write and run Python scripts it'll
 

00:00:18.199 --> 00:00:19.910 align:start position:0%
to write and run Python scripts it'll
delegate<00:00:18.480><c> a</c><00:00:18.600><c> talks</c><00:00:18.840><c> to</c><00:00:18.960><c> the</c><00:00:19.119><c> python</c><00:00:19.439><c> agent</c>

00:00:19.910 --> 00:00:19.920 align:start position:0%
delegate a talks to the python agent
 

00:00:19.920 --> 00:00:21.590 align:start position:0%
delegate a talks to the python agent
similarly<00:00:20.320><c> we'll</c><00:00:20.519><c> give</c><00:00:20.600><c> it</c><00:00:20.720><c> a</c><00:00:20.840><c> research</c><00:00:21.320><c> and</c>

00:00:21.590 --> 00:00:21.600 align:start position:0%
similarly we'll give it a research and
 

00:00:21.600 --> 00:00:23.870 align:start position:0%
similarly we'll give it a research and
investment<00:00:22.119><c> agent</c><00:00:22.920><c> this</c><00:00:23.039><c> is</c><00:00:23.240><c> very</c><00:00:23.480><c> close</c><00:00:23.720><c> to</c>

00:00:23.870 --> 00:00:23.880 align:start position:0%
investment agent this is very close to
 

00:00:23.880 --> 00:00:25.990 align:start position:0%
investment agent this is very close to
the<00:00:24.080><c> llm</c><00:00:24.560><c> OS</c><00:00:25.080><c> which</c><00:00:25.240><c> we've</c><00:00:25.400><c> been</c><00:00:25.560><c> building</c><00:00:25.880><c> so</c>

00:00:25.990 --> 00:00:26.000 align:start position:0%
the llm OS which we've been building so
 

00:00:26.000 --> 00:00:27.910 align:start position:0%
the llm OS which we've been building so
you'll<00:00:26.160><c> see</c><00:00:26.320><c> a</c><00:00:26.400><c> lot</c><00:00:26.560><c> of</c><00:00:26.679><c> similarities</c><00:00:27.640><c> but</c><00:00:27.800><c> I</c>

00:00:27.910 --> 00:00:27.920 align:start position:0%
you'll see a lot of similarities but I
 

00:00:27.920 --> 00:00:30.230 align:start position:0%
you'll see a lot of similarities but I
wanted<00:00:28.160><c> to</c><00:00:28.400><c> demo</c><00:00:28.720><c> the</c><00:00:28.880><c> team</c><00:00:29.119><c> of</c><00:00:29.439><c> Agents</c><00:00:30.000><c> in</c>

00:00:30.230 --> 00:00:30.240 align:start position:0%
wanted to demo the team of Agents in
 

00:00:30.240 --> 00:00:32.470 align:start position:0%
wanted to demo the team of Agents in
action<00:00:30.679><c> here</c><00:00:30.880><c> as</c><00:00:31.039><c> well</c><00:00:31.439><c> so</c><00:00:31.599><c> let's</c><00:00:31.840><c> get</c><00:00:32.000><c> started</c>

00:00:32.470 --> 00:00:32.480 align:start position:0%
action here as well so let's get started
 

00:00:32.480 --> 00:00:34.270 align:start position:0%
action here as well so let's get started
this<00:00:32.599><c> is</c><00:00:32.759><c> what</c><00:00:32.920><c> the</c><00:00:33.120><c> application</c><00:00:33.559><c> looks</c><00:00:33.840><c> like</c>

00:00:34.270 --> 00:00:34.280 align:start position:0%
this is what the application looks like
 

00:00:34.280 --> 00:00:36.110 align:start position:0%
this is what the application looks like
I'll<00:00:34.440><c> give</c><00:00:34.559><c> you</c><00:00:34.640><c> the</c><00:00:34.760><c> code</c><00:00:34.960><c> for</c><00:00:35.160><c> this</c><00:00:35.399><c> app</c><00:00:36.000><c> we</c>

00:00:36.110 --> 00:00:36.120 align:start position:0%
I'll give you the code for this app we
 

00:00:36.120 --> 00:00:38.030 align:start position:0%
I'll give you the code for this app we
are<00:00:36.239><c> going</c><00:00:36.360><c> to</c><00:00:36.520><c> interact</c><00:00:36.960><c> with</c><00:00:37.079><c> our</c><00:00:37.399><c> main</c>

00:00:38.030 --> 00:00:38.040 align:start position:0%
are going to interact with our main
 

00:00:38.040 --> 00:00:40.510 align:start position:0%
are going to interact with our main
driver<00:00:38.520><c> agent</c><00:00:39.079><c> it</c><00:00:39.200><c> can</c><00:00:39.520><c> answer</c><00:00:40.039><c> questions</c>

00:00:40.510 --> 00:00:40.520 align:start position:0%
driver agent it can answer questions
 

00:00:40.520 --> 00:00:43.190 align:start position:0%
driver agent it can answer questions
directly<00:00:41.000><c> use</c><00:00:41.320><c> tools</c><00:00:41.680><c> or</c><00:00:41.879><c> delegate</c><00:00:42.320><c> tasks</c><00:00:43.000><c> so</c>

00:00:43.190 --> 00:00:43.200 align:start position:0%
directly use tools or delegate tasks so
 

00:00:43.200 --> 00:00:45.310 align:start position:0%
directly use tools or delegate tasks so
let's<00:00:43.399><c> get</c><00:00:43.520><c> started</c><00:00:43.879><c> so</c><00:00:44.039><c> first</c><00:00:44.239><c> we'll</c><00:00:44.440><c> ask</c><00:00:45.120><c> who</c>

00:00:45.310 --> 00:00:45.320 align:start position:0%
let's get started so first we'll ask who
 

00:00:45.320 --> 00:00:48.389 align:start position:0%
let's get started so first we'll ask who
are<00:00:45.480><c> you</c><00:00:45.840><c> and</c><00:00:46.360><c> what</c><00:00:46.559><c> can</c><00:00:46.719><c> you</c><00:00:46.879><c> do</c><00:00:47.800><c> uh</c><00:00:47.960><c> this</c><00:00:48.160><c> way</c>

00:00:48.389 --> 00:00:48.399 align:start position:0%
are you and what can you do uh this way
 

00:00:48.399 --> 00:00:50.990 align:start position:0%
are you and what can you do uh this way
we<00:00:48.480><c> are</c><00:00:48.680><c> interacting</c><00:00:49.399><c> directly</c><00:00:50.360><c> with</c><00:00:50.600><c> our</c>

00:00:50.990 --> 00:00:51.000 align:start position:0%
we are interacting directly with our
 

00:00:51.000 --> 00:00:52.869 align:start position:0%
we are interacting directly with our
main<00:00:51.440><c> agent</c><00:00:52.120><c> it's</c><00:00:52.280><c> got</c><00:00:52.440><c> a</c><00:00:52.559><c> set</c><00:00:52.680><c> of</c>

00:00:52.869 --> 00:00:52.879 align:start position:0%
main agent it's got a set of
 

00:00:52.879 --> 00:00:54.389 align:start position:0%
main agent it's got a set of
capabilities<00:00:53.600><c> it's</c><00:00:53.719><c> got</c><00:00:53.879><c> a</c><00:00:54.039><c> bunch</c><00:00:54.239><c> of</c>

00:00:54.389 --> 00:00:54.399 align:start position:0%
capabilities it's got a bunch of
 

00:00:54.399 --> 00:00:56.950 align:start position:0%
capabilities it's got a bunch of
assistants<00:00:55.359><c> we</c><00:00:55.440><c> can</c><00:00:55.600><c> give</c><00:00:55.719><c> it</c><00:00:55.800><c> a</c><00:00:55.920><c> set</c><00:00:56.120><c> of</c><00:00:56.280><c> tasks</c>

00:00:56.950 --> 00:00:56.960 align:start position:0%
assistants we can give it a set of tasks
 

00:00:56.960 --> 00:00:58.470 align:start position:0%
assistants we can give it a set of tasks
it's<00:00:57.120><c> working</c><00:00:57.600><c> very</c><00:00:57.840><c> very</c><00:00:58.039><c> well</c><00:00:58.239><c> with</c><00:00:58.359><c> the</c>

00:00:58.470 --> 00:00:58.480 align:start position:0%
it's working very very well with the
 

00:00:58.480 --> 00:01:01.349 align:start position:0%
it's working very very well with the
gbd4<00:00:59.199><c> model</c><00:00:59.760><c> now</c><00:01:00.039><c> let's</c><00:01:00.320><c> ask</c><00:01:00.600><c> something</c><00:01:00.960><c> for</c>

00:01:01.349 --> 00:01:01.359 align:start position:0%
gbd4 model now let's ask something for
 

00:01:01.359 --> 00:01:03.549 align:start position:0%
gbd4 model now let's ask something for
for<00:01:01.559><c> which</c><00:01:01.719><c> it</c><00:01:01.840><c> needs</c><00:01:02.039><c> to</c><00:01:02.280><c> use</c><00:01:02.600><c> a</c><00:01:02.840><c> tool</c><00:01:03.160><c> it</c><00:01:03.320><c> has</c>

00:01:03.549 --> 00:01:03.559 align:start position:0%
for which it needs to use a tool it has
 

00:01:03.559 --> 00:01:05.950 align:start position:0%
for which it needs to use a tool it has
access<00:01:03.840><c> to</c><00:01:04.400><c> let's</c><00:01:04.640><c> say</c><00:01:05.119><c> what's</c><00:01:05.320><c> the</c><00:01:05.479><c> Nvidia</c>

00:01:05.950 --> 00:01:05.960 align:start position:0%
access to let's say what's the Nvidia
 

00:01:05.960 --> 00:01:08.870 align:start position:0%
access to let's say what's the Nvidia
stock<00:01:06.680><c> price</c><00:01:07.680><c> it's</c><00:01:07.960><c> got</c><00:01:08.159><c> access</c><00:01:08.360><c> to</c><00:01:08.479><c> the</c><00:01:08.720><c> V</c>

00:01:08.870 --> 00:01:08.880 align:start position:0%
stock price it's got access to the V
 

00:01:08.880 --> 00:01:10.789 align:start position:0%
stock price it's got access to the V
Yahoo<00:01:09.240><c> finance</c><00:01:09.560><c> tools</c><00:01:10.000><c> it'll</c><00:01:10.240><c> pull</c><00:01:10.479><c> that</c><00:01:10.600><c> up</c>

00:01:10.789 --> 00:01:10.799 align:start position:0%
Yahoo finance tools it'll pull that up
 

00:01:10.799 --> 00:01:14.149 align:start position:0%
Yahoo finance tools it'll pull that up
directly<00:01:11.680><c> fantastic</c><00:01:12.680><c> and</c><00:01:12.920><c> now</c><00:01:13.200><c> let's</c><00:01:13.560><c> ask</c>

00:01:14.149 --> 00:01:14.159 align:start position:0%
directly fantastic and now let's ask
 

00:01:14.159 --> 00:01:16.590 align:start position:0%
directly fantastic and now let's ask
something<00:01:14.840><c> for</c><00:01:15.119><c> which</c><00:01:15.320><c> it</c><00:01:15.479><c> needs</c><00:01:15.720><c> to</c><00:01:16.080><c> delegate</c>

00:01:16.590 --> 00:01:16.600 align:start position:0%
something for which it needs to delegate
 

00:01:16.600 --> 00:01:18.950 align:start position:0%
something for which it needs to delegate
a<00:01:16.840><c> task</c><00:01:17.080><c> to</c><00:01:17.240><c> another</c><00:01:17.520><c> assistant</c><00:01:18.400><c> or</c><00:01:18.640><c> you</c><00:01:18.759><c> know</c>

00:01:18.950 --> 00:01:18.960 align:start position:0%
a task to another assistant or you know
 

00:01:18.960 --> 00:01:21.550 align:start position:0%
a task to another assistant or you know
what<00:01:19.280><c> before</c><00:01:19.720><c> that</c><00:01:20.720><c> let's</c><00:01:20.920><c> see</c><00:01:21.079><c> how</c><00:01:21.200><c> knowledge</c>

00:01:21.550 --> 00:01:21.560 align:start position:0%
what before that let's see how knowledge
 

00:01:21.560 --> 00:01:23.950 align:start position:0%
what before that let's see how knowledge
retrieval<00:01:22.040><c> works</c><00:01:22.600><c> we'll</c><00:01:22.880><c> take</c><00:01:23.119><c> Sam</c><00:01:23.439><c> ortman's</c>

00:01:23.950 --> 00:01:23.960 align:start position:0%
retrieval works we'll take Sam ortman's
 

00:01:23.960 --> 00:01:26.590 align:start position:0%
retrieval works we'll take Sam ortman's
blog<00:01:24.360><c> post</c><00:01:25.360><c> give</c><00:01:25.479><c> it</c><00:01:25.640><c> to</c><00:01:25.759><c> its</c><00:01:25.920><c> knowledge</c><00:01:26.240><c> base</c>

00:01:26.590 --> 00:01:26.600 align:start position:0%
blog post give it to its knowledge base
 

00:01:26.600 --> 00:01:29.510 align:start position:0%
blog post give it to its knowledge base
and<00:01:26.759><c> what</c><00:01:26.880><c> you'll</c><00:01:27.079><c> see</c><00:01:27.360><c> is</c><00:01:27.520><c> that</c><00:01:27.759><c> we</c><00:01:27.960><c> want</c><00:01:28.640><c> this</c>

00:01:29.510 --> 00:01:29.520 align:start position:0%
and what you'll see is that we want this
 

00:01:29.520 --> 00:01:32.710 align:start position:0%
and what you'll see is that we want this
to<00:01:30.079><c> have</c><00:01:30.520><c> more</c><00:01:30.840><c> knowledge</c><00:01:31.400><c> than</c><00:01:32.159><c> any</c><00:01:32.399><c> of</c><00:01:32.560><c> the</c>

00:01:32.710 --> 00:01:32.720 align:start position:0%
to have more knowledge than any of the
 

00:01:32.720 --> 00:01:34.230 align:start position:0%
to have more knowledge than any of the
other<00:01:33.000><c> agents</c><00:01:33.280><c> so</c><00:01:33.439><c> we</c><00:01:33.520><c> want</c><00:01:33.680><c> us</c><00:01:33.840><c> to</c><00:01:33.960><c> have</c><00:01:34.079><c> a</c>

00:01:34.230 --> 00:01:34.240 align:start position:0%
other agents so we want us to have a
 

00:01:34.240 --> 00:01:35.910 align:start position:0%
other agents so we want us to have a
generic<00:01:34.640><c> knowledge</c><00:01:34.960><c> base</c><00:01:35.280><c> which</c><00:01:35.399><c> all</c><00:01:35.600><c> agents</c>

00:01:35.910 --> 00:01:35.920 align:start position:0%
generic knowledge base which all agents
 

00:01:35.920 --> 00:01:40.310 align:start position:0%
generic knowledge base which all agents
can<00:01:36.119><c> use</c><00:01:36.880><c> so</c><00:01:37.200><c> let's</c><00:01:37.479><c> ask</c><00:01:38.479><c> what</c>

00:01:40.310 --> 00:01:40.320 align:start position:0%
can use so let's ask what
 

00:01:40.320 --> 00:01:46.429 align:start position:0%
can use so let's ask what
did<00:01:41.600><c> Sam</c><00:01:42.600><c> Alman</c><00:01:43.240><c> wish</c><00:01:43.680><c> someone</c><00:01:44.520><c> had</c><00:01:44.759><c> told</c><00:01:45.439><c> him</c>

00:01:46.429 --> 00:01:46.439 align:start position:0%
did Sam Alman wish someone had told him
 

00:01:46.439 --> 00:01:48.950 align:start position:0%
did Sam Alman wish someone had told him
yeah<00:01:46.640><c> tyos</c><00:01:47.119><c> everything</c><00:01:47.399><c> it's</c><00:01:47.560><c> all</c><00:01:47.840><c> fine</c><00:01:48.799><c> it's</c>

00:01:48.950 --> 00:01:48.960 align:start position:0%
yeah tyos everything it's all fine it's
 

00:01:48.960 --> 00:01:50.630 align:start position:0%
yeah tyos everything it's all fine it's
going<00:01:49.079><c> to</c><00:01:49.200><c> search</c><00:01:49.520><c> its</c><00:01:49.799><c> knowledge</c><00:01:50.119><c> base</c><00:01:50.399><c> for</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
going to search its knowledge base for
 

00:01:50.640 --> 00:01:53.870 align:start position:0%
going to search its knowledge base for
that<00:01:51.119><c> information</c><00:01:52.119><c> pick</c><00:01:52.320><c> out</c><00:01:52.600><c> that</c><00:01:52.759><c> blog</c><00:01:53.159><c> post</c>

00:01:53.870 --> 00:01:53.880 align:start position:0%
that information pick out that blog post
 

00:01:53.880 --> 00:01:56.469 align:start position:0%
that information pick out that blog post
and<00:01:54.159><c> answer</c><00:01:54.560><c> that</c><00:01:54.799><c> question</c><00:01:55.600><c> fantastic</c><00:01:56.200><c> so</c><00:01:56.360><c> we</c>

00:01:56.469 --> 00:01:56.479 align:start position:0%
and answer that question fantastic so we
 

00:01:56.479 --> 00:02:00.389 align:start position:0%
and answer that question fantastic so we
can<00:01:56.640><c> see</c><00:01:57.000><c> this</c><00:01:57.759><c> agent</c><00:01:58.079><c> of</c><00:01:58.399><c> ours</c><00:01:59.399><c> can</c><00:01:59.560><c> run</c><00:01:59.960><c> tools</c>

00:02:00.389 --> 00:02:00.399 align:start position:0%
can see this agent of ours can run tools
 

00:02:00.399 --> 00:02:02.630 align:start position:0%
can see this agent of ours can run tools
answer<00:02:00.759><c> questions</c><00:02:01.119><c> directly</c><00:02:02.119><c> access</c><00:02:02.439><c> its</c>

00:02:02.630 --> 00:02:02.640 align:start position:0%
answer questions directly access its
 

00:02:02.640 --> 00:02:04.510 align:start position:0%
answer questions directly access its
knowledge<00:02:03.000><c> base</c><00:02:03.560><c> now</c><00:02:03.759><c> let's</c><00:02:03.960><c> see</c><00:02:04.119><c> if</c><00:02:04.200><c> it</c><00:02:04.360><c> can</c>

00:02:04.510 --> 00:02:04.520 align:start position:0%
knowledge base now let's see if it can
 

00:02:04.520 --> 00:02:06.350 align:start position:0%
knowledge base now let's see if it can
delegate<00:02:04.920><c> a</c><00:02:05.039><c> task</c><00:02:05.320><c> to</c><00:02:05.479><c> another</c><00:02:05.719><c> agent</c><00:02:06.000><c> and</c><00:02:06.159><c> its</c>

00:02:06.350 --> 00:02:06.360 align:start position:0%
delegate a task to another agent and its
 

00:02:06.360 --> 00:02:10.150 align:start position:0%
delegate a task to another agent and its
team<00:02:07.079><c> so</c><00:02:07.280><c> let's</c><00:02:07.479><c> say</c><00:02:07.920><c> uh</c><00:02:08.920><c> write</c><00:02:09.119><c> a</c><00:02:09.319><c> report</c><00:02:09.879><c> on</c>

00:02:10.150 --> 00:02:10.160 align:start position:0%
team so let's say uh write a report on
 

00:02:10.160 --> 00:02:11.869 align:start position:0%
team so let's say uh write a report on
the

00:02:11.869 --> 00:02:11.879 align:start position:0%
the
 

00:02:11.879 --> 00:02:15.229 align:start position:0%
the
IBM<00:02:12.879><c> Hashi</c>

00:02:15.229 --> 00:02:15.239 align:start position:0%
IBM Hashi
 

00:02:15.239 --> 00:02:18.270 align:start position:0%
IBM Hashi
Corp<00:02:16.239><c> acquisition</c><00:02:17.120><c> typos</c><00:02:17.599><c> everything</c><00:02:17.879><c> is</c><00:02:18.000><c> all</c>

00:02:18.270 --> 00:02:18.280 align:start position:0%
Corp acquisition typos everything is all
 

00:02:18.280 --> 00:02:20.949 align:start position:0%
Corp acquisition typos everything is all
okay<00:02:18.599><c> because</c><00:02:19.280><c> the</c><00:02:19.440><c> advantage</c><00:02:19.840><c> of</c><00:02:19.959><c> using</c><00:02:20.599><c> this</c>

00:02:20.949 --> 00:02:20.959 align:start position:0%
okay because the advantage of using this
 

00:02:20.959 --> 00:02:22.790 align:start position:0%
okay because the advantage of using this
driver<00:02:21.400><c> assistant</c><00:02:21.879><c> is</c><00:02:22.000><c> that</c><00:02:22.120><c> it'll</c><00:02:22.400><c> format</c>

00:02:22.790 --> 00:02:22.800 align:start position:0%
driver assistant is that it'll format
 

00:02:22.800 --> 00:02:25.390 align:start position:0%
driver assistant is that it'll format
this<00:02:22.959><c> neatly</c><00:02:23.400><c> and</c><00:02:23.519><c> send</c><00:02:23.760><c> it</c><00:02:23.920><c> to</c><00:02:24.120><c> the</c><00:02:24.840><c> other</c><00:02:25.239><c> uh</c>

00:02:25.390 --> 00:02:25.400 align:start position:0%
this neatly and send it to the other uh
 

00:02:25.400 --> 00:02:27.309 align:start position:0%
this neatly and send it to the other uh
delegated<00:02:25.959><c> to</c><00:02:26.160><c> its</c><00:02:26.319><c> team</c><00:02:26.560><c> members</c><00:02:27.000><c> in</c><00:02:27.120><c> the</c>

00:02:27.309 --> 00:02:27.319 align:start position:0%
delegated to its team members in the
 

00:02:27.319 --> 00:02:29.350 align:start position:0%
delegated to its team members in the
right<00:02:27.640><c> text</c><00:02:28.319><c> okay</c><00:02:28.440><c> so</c><00:02:28.640><c> while</c><00:02:28.800><c> this</c><00:02:28.959><c> is</c><00:02:29.120><c> going</c>

00:02:29.350 --> 00:02:29.360 align:start position:0%
right text okay so while this is going
 

00:02:29.360 --> 00:02:31.390 align:start position:0%
right text okay so while this is going
on<00:02:30.280><c> let's</c><00:02:30.480><c> see</c><00:02:30.640><c> where</c><00:02:30.760><c> the</c><00:02:30.879><c> code</c><00:02:31.080><c> for</c><00:02:31.239><c> this</c>

00:02:31.390 --> 00:02:31.400 align:start position:0%
on let's see where the code for this
 

00:02:31.400 --> 00:02:33.670 align:start position:0%
on let's see where the code for this
application<00:02:31.800><c> is</c><00:02:32.080><c> under</c><00:02:32.280><c> the</c><00:02:32.360><c> five</c><00:02:32.800><c> repo</c><00:02:33.400><c> uh</c><00:02:33.480><c> to</c>

00:02:33.670 --> 00:02:33.680 align:start position:0%
application is under the five repo uh to
 

00:02:33.680 --> 00:02:35.710 align:start position:0%
application is under the five repo uh to
customize<00:02:34.080><c> it</c><00:02:34.360><c> fork</c><00:02:34.959><c> and</c><00:02:35.160><c> clone</c><00:02:35.480><c> this</c>

00:02:35.710 --> 00:02:35.720 align:start position:0%
customize it fork and clone this
 

00:02:35.720 --> 00:02:37.710 align:start position:0%
customize it fork and clone this
repository<00:02:36.640><c> and</c><00:02:36.800><c> Under</c><00:02:37.000><c> The</c><00:02:37.120><c> cookbook</c><00:02:37.519><c> you'll</c>

00:02:37.710 --> 00:02:37.720 align:start position:0%
repository and Under The cookbook you'll
 

00:02:37.720 --> 00:02:40.309 align:start position:0%
repository and Under The cookbook you'll
find<00:02:37.920><c> it</c><00:02:38.080><c> under</c><00:02:38.360><c> the</c><00:02:38.680><c> agents</c><00:02:39.159><c> folder</c><00:02:40.120><c> after</c>

00:02:40.309 --> 00:02:40.319 align:start position:0%
find it under the agents folder after
 

00:02:40.319 --> 00:02:43.190 align:start position:0%
find it under the agents folder after
you've<00:02:40.519><c> cloned</c><00:02:40.800><c> the</c><00:02:40.920><c> repo</c><00:02:41.360><c> open</c><00:02:41.560><c> it</c><00:02:41.760><c> up</c><00:02:42.080><c> in</c><00:02:42.319><c> the</c>

00:02:43.190 --> 00:02:43.200 align:start position:0%
you've cloned the repo open it up in the
 

00:02:43.200 --> 00:02:46.509 align:start position:0%
you've cloned the repo open it up in the
codor<00:02:43.760><c> of</c><00:02:43.879><c> your</c><00:02:44.040><c> choice</c><00:02:44.840><c> cookbook</c><00:02:45.280><c> agents</c><00:02:46.280><c> I</c>

00:02:46.509 --> 00:02:46.519 align:start position:0%
codor of your choice cookbook agents I
 

00:02:46.519 --> 00:02:50.070 align:start position:0%
codor of your choice cookbook agents I
also<00:02:47.360><c> have</c><00:02:47.680><c> added</c><00:02:48.200><c> a</c><00:02:48.879><c> some</c><00:02:49.120><c> examples</c><00:02:49.560><c> of</c><00:02:49.840><c> very</c>

00:02:50.070 --> 00:02:50.080 align:start position:0%
also have added a some examples of very
 

00:02:50.080 --> 00:02:52.470 align:start position:0%
also have added a some examples of very
simple<00:02:50.400><c> agents</c><00:02:50.840><c> like</c><00:02:51.000><c> the</c><00:02:51.159><c> web</c><00:02:51.400><c> search</c><00:02:51.680><c> agent</c>

00:02:52.470 --> 00:02:52.480 align:start position:0%
simple agents like the web search agent
 

00:02:52.480 --> 00:02:54.710 align:start position:0%
simple agents like the web search agent
the<00:02:52.640><c> web</c><00:02:52.879><c> search</c><00:02:53.159><c> Agent</c><00:02:53.879><c> shows</c><00:02:54.239><c> you</c><00:02:54.440><c> how</c><00:02:54.560><c> to</c>

00:02:54.710 --> 00:02:54.720 align:start position:0%
the web search Agent shows you how to
 

00:02:54.720 --> 00:02:57.869 align:start position:0%
the web search Agent shows you how to
build<00:02:54.959><c> a</c><00:02:55.159><c> very</c><00:02:55.720><c> basic</c><00:02:56.200><c> agent</c><00:02:56.519><c> with</c><00:02:56.680><c> Fi</c><00:02:56.920><c> data</c><00:02:57.720><c> uh</c>

00:02:57.869 --> 00:02:57.879 align:start position:0%
build a very basic agent with Fi data uh
 

00:02:57.879 --> 00:03:00.470 align:start position:0%
build a very basic agent with Fi data uh
we<00:02:58.080><c> call</c><00:02:58.319><c> them</c><00:02:58.840><c> assistants</c><00:02:59.879><c> because</c><00:03:00.120><c> I</c><00:03:00.200><c> don't</c>

00:03:00.470 --> 00:03:00.480 align:start position:0%
we call them assistants because I don't
 

00:03:00.480 --> 00:03:02.830 align:start position:0%
we call them assistants because I don't
like<00:03:00.680><c> the</c><00:03:00.840><c> word</c><00:03:01.080><c> agent</c><00:03:01.519><c> it</c><00:03:01.640><c> feels</c><00:03:02.080><c> like</c><00:03:02.720><c> you</c>

00:03:02.830 --> 00:03:02.840 align:start position:0%
like the word agent it feels like you
 

00:03:02.840 --> 00:03:05.229 align:start position:0%
like the word agent it feels like you
know<00:03:03.000><c> it's</c><00:03:03.120><c> an</c><00:03:03.280><c> intelligence</c><00:03:03.799><c> like</c><00:03:04.080><c> FBI</c><00:03:04.680><c> agent</c>

00:03:05.229 --> 00:03:05.239 align:start position:0%
know it's an intelligence like FBI agent
 

00:03:05.239 --> 00:03:06.710 align:start position:0%
know it's an intelligence like FBI agent
that<00:03:05.360><c> I'm</c><00:03:05.480><c> building</c><00:03:06.000><c> we</c><00:03:06.120><c> don't</c><00:03:06.319><c> want</c><00:03:06.440><c> to</c><00:03:06.560><c> get</c>

00:03:06.710 --> 00:03:06.720 align:start position:0%
that I'm building we don't want to get
 

00:03:06.720 --> 00:03:08.750 align:start position:0%
that I'm building we don't want to get
into<00:03:06.920><c> that</c><00:03:07.159><c> business</c><00:03:07.879><c> it's</c><00:03:08.000><c> an</c><00:03:08.239><c> assistant</c>

00:03:08.750 --> 00:03:08.760 align:start position:0%
into that business it's an assistant
 

00:03:08.760 --> 00:03:11.270 align:start position:0%
into that business it's an assistant
because<00:03:09.000><c> it</c><00:03:09.280><c> assists</c><00:03:09.720><c> us</c><00:03:09.920><c> with</c><00:03:10.120><c> tasks</c><00:03:10.560><c> so</c><00:03:11.040><c> for</c>

00:03:11.270 --> 00:03:11.280 align:start position:0%
because it assists us with tasks so for
 

00:03:11.280 --> 00:03:13.589 align:start position:0%
because it assists us with tasks so for
us<00:03:11.959><c> agents</c><00:03:12.319><c> equal</c><00:03:12.560><c> to</c><00:03:12.680><c> assistants</c><00:03:13.319><c> so</c><00:03:13.480><c> we're</c>

00:03:13.589 --> 00:03:13.599 align:start position:0%
us agents equal to assistants so we're
 

00:03:13.599 --> 00:03:15.309 align:start position:0%
us agents equal to assistants so we're
going<00:03:13.680><c> to</c><00:03:13.760><c> build</c><00:03:13.959><c> an</c><00:03:14.120><c> assistant</c><00:03:14.959><c> give</c><00:03:15.120><c> it</c><00:03:15.239><c> the</c>

00:03:15.309 --> 00:03:15.319 align:start position:0%
going to build an assistant give it the
 

00:03:15.319 --> 00:03:17.509 align:start position:0%
going to build an assistant give it the
llm<00:03:15.720><c> the</c><00:03:15.799><c> gbd4</c><00:03:16.599><c> give</c><00:03:16.720><c> it</c><00:03:16.840><c> a</c><00:03:16.920><c> set</c><00:03:17.040><c> of</c><00:03:17.159><c> tools</c><00:03:17.360><c> and</c>

00:03:17.509 --> 00:03:17.519 align:start position:0%
llm the gbd4 give it a set of tools and
 

00:03:17.519 --> 00:03:19.869 align:start position:0%
llm the gbd4 give it a set of tools and
ask<00:03:17.760><c> questions</c><00:03:18.319><c> it's</c><00:03:18.519><c> pretty</c><00:03:18.720><c> much</c><00:03:18.920><c> it</c><00:03:19.680><c> if</c><00:03:19.760><c> you</c>

00:03:19.869 --> 00:03:19.879 align:start position:0%
ask questions it's pretty much it if you
 

00:03:19.879 --> 00:03:22.630 align:start position:0%
ask questions it's pretty much it if you
want<00:03:20.000><c> to</c><00:03:20.280><c> add</c><00:03:21.080><c> Financial</c><00:03:21.519><c> knowledge</c><00:03:21.879><c> to</c><00:03:22.080><c> it</c>

00:03:22.630 --> 00:03:22.640 align:start position:0%
want to add Financial knowledge to it
 

00:03:22.640 --> 00:03:24.110 align:start position:0%
want to add Financial knowledge to it
add<00:03:22.920><c> the</c><00:03:23.159><c> finance</c>

00:03:24.110 --> 00:03:24.120 align:start position:0%
add the finance
 

00:03:24.120 --> 00:03:26.229 align:start position:0%
add the finance
tools<00:03:25.120><c> and</c><00:03:25.239><c> then</c><00:03:25.400><c> you</c><00:03:25.519><c> can</c><00:03:25.760><c> it</c><00:03:25.879><c> can</c><00:03:26.000><c> run</c>

00:03:26.229 --> 00:03:26.239 align:start position:0%
tools and then you can it can run
 

00:03:26.239 --> 00:03:28.789 align:start position:0%
tools and then you can it can run
multiple<00:03:26.560><c> tools</c><00:03:26.840><c> at</c><00:03:26.959><c> the</c><00:03:27.080><c> same</c><00:03:27.319><c> time</c><00:03:28.239><c> uh</c><00:03:28.400><c> so</c>

00:03:28.789 --> 00:03:28.799 align:start position:0%
multiple tools at the same time uh so
 

00:03:28.799 --> 00:03:31.910 align:start position:0%
multiple tools at the same time uh so
it's<00:03:29.319><c> that</c><00:03:29.840><c> easy</c><00:03:30.159><c> to</c><00:03:30.319><c> build</c><00:03:30.640><c> an</c><00:03:30.920><c> assistant</c>

00:03:31.910 --> 00:03:31.920 align:start position:0%
it's that easy to build an assistant
 

00:03:31.920 --> 00:03:35.270 align:start position:0%
it's that easy to build an assistant
with<00:03:32.200><c> Fi</c><00:03:32.480><c> data</c><00:03:32.920><c> which</c><00:03:33.120><c> has</c><00:03:33.480><c> access</c><00:03:33.799><c> to</c><00:03:34.360><c> tools</c>

00:03:35.270 --> 00:03:35.280 align:start position:0%
with Fi data which has access to tools
 

00:03:35.280 --> 00:03:38.030 align:start position:0%
with Fi data which has access to tools
now<00:03:35.560><c> let's</c><00:03:36.239><c> see</c><00:03:36.599><c> the</c><00:03:36.799><c> more</c><00:03:37.200><c> complex</c><00:03:37.760><c> agent</c>

00:03:38.030 --> 00:03:38.040 align:start position:0%
now let's see the more complex agent
 

00:03:38.040 --> 00:03:41.350 align:start position:0%
now let's see the more complex agent
which<00:03:38.200><c> we're</c><00:03:38.439><c> building</c><00:03:39.439><c> so</c><00:03:39.720><c> in</c><00:03:39.879><c> this</c><00:03:40.360><c> cookbook</c>

00:03:41.350 --> 00:03:41.360 align:start position:0%
which we're building so in this cookbook
 

00:03:41.360 --> 00:03:43.830 align:start position:0%
which we're building so in this cookbook
as<00:03:41.560><c> always</c><00:03:41.959><c> the</c><00:03:42.239><c> app</c><00:03:42.640><c> is</c><00:03:42.799><c> the</c><00:03:43.080><c> front</c><00:03:43.360><c> end</c><00:03:43.720><c> which</c>

00:03:43.830 --> 00:03:43.840 align:start position:0%
as always the app is the front end which
 

00:03:43.840 --> 00:03:46.390 align:start position:0%
as always the app is the front end which
is<00:03:43.959><c> the</c><00:03:44.120><c> streamlet</c><00:03:44.799><c> application</c><00:03:45.319><c> which</c><00:03:45.439><c> is</c>

00:03:46.390 --> 00:03:46.400 align:start position:0%
is the streamlet application which is
 

00:03:46.400 --> 00:03:48.830 align:start position:0%
is the streamlet application which is
running<00:03:47.400><c> I'm</c><00:03:47.519><c> going</c><00:03:47.640><c> to</c><00:03:47.840><c> let</c><00:03:48.000><c> you</c><00:03:48.239><c> go</c><00:03:48.480><c> through</c>

00:03:48.830 --> 00:03:48.840 align:start position:0%
running I'm going to let you go through
 

00:03:48.840 --> 00:03:51.030 align:start position:0%
running I'm going to let you go through
this<00:03:49.400><c> uh</c><00:03:49.560><c> yourself</c><00:03:50.040><c> it</c><00:03:50.159><c> is</c><00:03:50.319><c> generic</c><00:03:50.760><c> python</c>

00:03:51.030 --> 00:03:51.040 align:start position:0%
this uh yourself it is generic python
 

00:03:51.040 --> 00:03:53.190 align:start position:0%
this uh yourself it is generic python
streamlet<00:03:51.519><c> code</c><00:03:52.120><c> the</c><00:03:52.239><c> magic</c><00:03:52.560><c> really</c><00:03:52.840><c> happens</c>

00:03:53.190 --> 00:03:53.200 align:start position:0%
streamlet code the magic really happens
 

00:03:53.200 --> 00:03:56.069 align:start position:0%
streamlet code the magic really happens
in<00:03:53.360><c> the</c><00:03:53.560><c> agent</c><00:03:54.040><c> file</c><00:03:54.879><c> the</c><00:03:55.120><c> agent</c><00:03:55.560><c> file</c><00:03:55.879><c> is</c>

00:03:56.069 --> 00:03:56.079 align:start position:0%
in the agent file the agent file is
 

00:03:56.079 --> 00:03:58.710 align:start position:0%
in the agent file the agent file is
where<00:03:56.280><c> we</c><00:03:56.560><c> Define</c><00:03:57.239><c> our</c><00:03:57.720><c> agent</c><00:03:58.319><c> that</c><00:03:58.480><c> is</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
where we Define our agent that is
 

00:03:58.720 --> 00:04:01.869 align:start position:0%
where we Define our agent that is
running<00:03:59.799><c> now</c><00:03:59.959><c> to</c><00:04:00.120><c> run</c><00:04:00.360><c> this</c><00:04:00.560><c> agent</c><00:04:01.519><c> open</c><00:04:01.760><c> the</c>

00:04:01.869 --> 00:04:01.879 align:start position:0%
running now to run this agent open the
 

00:04:01.879 --> 00:04:03.910 align:start position:0%
running now to run this agent open the
read<00:04:02.120><c> me</c><00:04:02.760><c> it's</c><00:04:02.920><c> got</c><00:04:03.159><c> step-by-step</c>

00:04:03.910 --> 00:04:03.920 align:start position:0%
read me it's got step-by-step
 

00:04:03.920 --> 00:04:06.350 align:start position:0%
read me it's got step-by-step
instructions<00:04:04.920><c> very</c><00:04:05.159><c> similar</c><00:04:05.439><c> to</c><00:04:05.560><c> the</c><00:04:05.680><c> llm</c><00:04:06.040><c> OS</c>

00:04:06.350 --> 00:04:06.360 align:start position:0%
instructions very similar to the llm OS
 

00:04:06.360 --> 00:04:07.910 align:start position:0%
instructions very similar to the llm OS
which<00:04:06.480><c> we've</c><00:04:06.680><c> been</c><00:04:06.840><c> building</c><00:04:07.239><c> so</c><00:04:07.439><c> you</c><00:04:07.560><c> can</c>

00:04:07.910 --> 00:04:07.920 align:start position:0%
which we've been building so you can
 

00:04:07.920 --> 00:04:09.949 align:start position:0%
which we've been building so you can
copy<00:04:08.280><c> that</c><00:04:08.640><c> check</c><00:04:08.920><c> that</c><00:04:09.079><c> video</c><00:04:09.319><c> out</c><00:04:09.560><c> and</c><00:04:09.720><c> run</c>

00:04:09.949 --> 00:04:09.959 align:start position:0%
copy that check that video out and run
 

00:04:09.959 --> 00:04:12.149 align:start position:0%
copy that check that video out and run
this<00:04:10.159><c> file</c><00:04:11.079><c> um</c><00:04:11.439><c> and</c><00:04:11.599><c> you</c><00:04:11.680><c> should</c><00:04:11.879><c> get</c><00:04:12.040><c> the</c>

00:04:12.149 --> 00:04:12.159 align:start position:0%
this file um and you should get the
 

00:04:12.159 --> 00:04:14.670 align:start position:0%
this file um and you should get the
application<00:04:12.760><c> running</c><00:04:13.760><c> but</c><00:04:13.920><c> I</c><00:04:14.159><c> do</c><00:04:14.319><c> want</c><00:04:14.480><c> to</c>

00:04:14.670 --> 00:04:14.680 align:start position:0%
application running but I do want to
 

00:04:14.680 --> 00:04:18.110 align:start position:0%
application running but I do want to
talk<00:04:14.879><c> about</c><00:04:15.120><c> this</c><00:04:15.359><c> agent</c><00:04:16.359><c> so</c><00:04:16.959><c> this</c><00:04:17.199><c> agent</c><00:04:17.759><c> as</c><00:04:17.959><c> I</c>

00:04:18.110 --> 00:04:18.120 align:start position:0%
talk about this agent so this agent as I
 

00:04:18.120 --> 00:04:20.430 align:start position:0%
talk about this agent so this agent as I
mentioned<00:04:18.479><c> is</c><00:04:18.639><c> a</c><00:04:18.799><c> f</c><00:04:19.040><c> dat</c><00:04:19.280><c> assistant</c><00:04:19.959><c> it</c><00:04:20.079><c> uses</c>

00:04:20.430 --> 00:04:20.440 align:start position:0%
mentioned is a f dat assistant it uses
 

00:04:20.440 --> 00:04:24.070 align:start position:0%
mentioned is a f dat assistant it uses
the<00:04:20.600><c> gp40</c><00:04:21.560><c> model</c><00:04:22.440><c> we</c><00:04:22.680><c> give</c><00:04:22.960><c> our</c><00:04:23.320><c> assistant</c><00:04:23.840><c> a</c>

00:04:24.070 --> 00:04:24.080 align:start position:0%
the gp40 model we give our assistant a
 

00:04:24.080 --> 00:04:25.790 align:start position:0%
the gp40 model we give our assistant a
description<00:04:24.520><c> and</c><00:04:24.759><c> instructions</c><00:04:25.440><c> which</c><00:04:25.520><c> is</c>

00:04:25.790 --> 00:04:25.800 align:start position:0%
description and instructions which is
 

00:04:25.800 --> 00:04:29.070 align:start position:0%
description and instructions which is
just<00:04:25.960><c> a</c><00:04:26.199><c> way</c><00:04:26.400><c> to</c><00:04:26.919><c> write</c><00:04:27.280><c> the</c><00:04:27.600><c> system</c><00:04:28.080><c> prompt</c>

00:04:29.070 --> 00:04:29.080 align:start position:0%
just a way to write the system prompt
 

00:04:29.080 --> 00:04:30.990 align:start position:0%
just a way to write the system prompt
the<00:04:29.320><c> instru</c><00:04:29.680><c> instructions</c><00:04:30.160><c> are</c><00:04:30.400><c> added</c><00:04:30.680><c> at</c><00:04:30.800><c> a</c>

00:04:30.990 --> 00:04:31.000 align:start position:0%
the instru instructions are added at a
 

00:04:31.000 --> 00:04:33.830 align:start position:0%
the instru instructions are added at a
numbered<00:04:31.479><c> list</c><00:04:31.919><c> which</c><00:04:32.280><c> we</c><00:04:32.520><c> found</c><00:04:32.919><c> llms</c><00:04:33.440><c> follow</c>

00:04:33.830 --> 00:04:33.840 align:start position:0%
numbered list which we found llms follow
 

00:04:33.840 --> 00:04:36.189 align:start position:0%
numbered list which we found llms follow
better<00:04:34.600><c> you</c><00:04:34.800><c> found</c><00:04:35.000><c> a</c><00:04:35.400><c> you</c><00:04:35.520><c> find</c><00:04:35.720><c> a</c><00:04:35.840><c> better</c><00:04:36.080><c> way</c>

00:04:36.189 --> 00:04:36.199 align:start position:0%
better you found a you find a better way
 

00:04:36.199 --> 00:04:37.749 align:start position:0%
better you found a you find a better way
you're<00:04:36.400><c> free</c><00:04:36.639><c> to</c><00:04:36.840><c> just</c><00:04:37.000><c> use</c><00:04:37.240><c> the</c><00:04:37.400><c> system</c>

00:04:37.749 --> 00:04:37.759 align:start position:0%
you're free to just use the system
 

00:04:37.759 --> 00:04:40.469 align:start position:0%
you're free to just use the system
prompt<00:04:38.120><c> directly</c><00:04:39.000><c> you're</c><00:04:39.199><c> free</c><00:04:39.400><c> to</c><00:04:39.560><c> just</c><00:04:39.840><c> add</c>

00:04:40.469 --> 00:04:40.479 align:start position:0%
prompt directly you're free to just add
 

00:04:40.479 --> 00:04:44.469 align:start position:0%
prompt directly you're free to just add
messages<00:04:41.440><c> yourself</c><00:04:42.240><c> even</c><00:04:43.000><c> it's</c><00:04:43.160><c> up</c><00:04:43.320><c> to</c><00:04:43.479><c> you</c>

00:04:44.469 --> 00:04:44.479 align:start position:0%
messages yourself even it's up to you
 

00:04:44.479 --> 00:04:46.469 align:start position:0%
messages yourself even it's up to you
but<00:04:44.720><c> what</c><00:04:44.880><c> we</c><00:04:45.000><c> are</c><00:04:45.840><c> the</c><00:04:45.960><c> way</c><00:04:46.120><c> we</c><00:04:46.240><c> are</c>

00:04:46.469 --> 00:04:46.479 align:start position:0%
but what we are the way we are
 

00:04:46.479 --> 00:04:47.790 align:start position:0%
but what we are the way we are
programming<00:04:47.000><c> this</c>

00:04:47.790 --> 00:04:47.800 align:start position:0%
programming this
 

00:04:47.800 --> 00:04:49.469 align:start position:0%
programming this
assistant<00:04:48.800><c> we</c><00:04:48.919><c> are</c><00:04:49.039><c> saying</c><00:04:49.240><c> you're</c><00:04:49.400><c> a</c>

00:04:49.469 --> 00:04:49.479 align:start position:0%
assistant we are saying you're a
 

00:04:49.479 --> 00:04:52.710 align:start position:0%
assistant we are saying you're a
powerful<00:04:49.840><c> a</c><00:04:50.000><c> agent</c><00:04:50.280><c> called</c><00:04:50.479><c> Optimus</c><00:04:50.919><c> Prime</c><00:04:51.720><c> V7</c>

00:04:52.710 --> 00:04:52.720 align:start position:0%
powerful a agent called Optimus Prime V7
 

00:04:52.720 --> 00:04:54.310 align:start position:0%
powerful a agent called Optimus Prime V7
you<00:04:52.880><c> have</c><00:04:53.039><c> access</c><00:04:53.240><c> to</c><00:04:53.360><c> a</c><00:04:53.440><c> set</c><00:04:53.600><c> of</c><00:04:53.759><c> tools</c><00:04:54.120><c> and</c>

00:04:54.310 --> 00:04:54.320 align:start position:0%
you have access to a set of tools and
 

00:04:54.320 --> 00:04:56.350 align:start position:0%
you have access to a set of tools and
asss<00:04:54.720><c> inser</c><00:04:55.039><c> your</c><00:04:55.199><c> disposal</c><00:04:55.800><c> very</c><00:04:56.000><c> similar</c><00:04:56.280><c> to</c>

00:04:56.350 --> 00:04:56.360 align:start position:0%
asss inser your disposal very similar to
 

00:04:56.360 --> 00:04:58.710 align:start position:0%
asss inser your disposal very similar to
the<00:04:56.440><c> lmos</c><00:04:57.080><c> but</c><00:04:57.320><c> focused</c><00:04:57.680><c> on</c><00:04:58.120><c> focused</c><00:04:58.479><c> on</c>

00:04:58.710 --> 00:04:58.720 align:start position:0%
the lmos but focused on focused on
 

00:04:58.720 --> 00:05:00.550 align:start position:0%
the lmos but focused on focused on
really<00:04:59.000><c> using</c><00:04:59.240><c> it</c>

00:05:00.550 --> 00:05:00.560 align:start position:0%
really using it
 

00:05:00.560 --> 00:05:02.390 align:start position:0%
really using it
so<00:05:00.680><c> we</c><00:05:00.800><c> say</c><00:05:01.000><c> when</c><00:05:01.120><c> the</c><00:05:01.199><c> user</c><00:05:01.400><c> sends</c><00:05:01.639><c> a</c><00:05:01.840><c> message</c>

00:05:02.390 --> 00:05:02.400 align:start position:0%
so we say when the user sends a message
 

00:05:02.400 --> 00:05:03.830 align:start position:0%
so we say when the user sends a message
think<00:05:02.600><c> and</c><00:05:02.840><c> determine</c><00:05:03.440><c> whether</c><00:05:03.560><c> you</c><00:05:03.680><c> want</c><00:05:03.759><c> to</c>

00:05:03.830 --> 00:05:03.840 align:start position:0%
think and determine whether you want to
 

00:05:03.840 --> 00:05:05.189 align:start position:0%
think and determine whether you want to
use<00:05:03.960><c> a</c><00:05:04.080><c> tool</c><00:05:04.560><c> whether</c><00:05:04.720><c> you</c><00:05:04.800><c> want</c><00:05:04.880><c> to</c><00:05:05.000><c> search</c>

00:05:05.189 --> 00:05:05.199 align:start position:0%
use a tool whether you want to search
 

00:05:05.199 --> 00:05:07.749 align:start position:0%
use a tool whether you want to search
your<00:05:05.360><c> knowledge</c><00:05:05.800><c> search</c><00:05:06.120><c> the</c><00:05:06.400><c> internet</c><00:05:07.400><c> or</c>

00:05:07.749 --> 00:05:07.759 align:start position:0%
your knowledge search the internet or
 

00:05:07.759 --> 00:05:09.590 align:start position:0%
your knowledge search the internet or
ask<00:05:07.919><c> a</c><00:05:08.080><c> clarify</c><00:05:08.600><c> question</c><00:05:08.960><c> or</c><00:05:09.120><c> answer</c><00:05:09.360><c> it</c>

00:05:09.590 --> 00:05:09.600 align:start position:0%
ask a clarify question or answer it
 

00:05:09.600 --> 00:05:11.790 align:start position:0%
ask a clarify question or answer it
yourself<00:05:10.600><c> the</c><00:05:10.759><c> more</c><00:05:10.960><c> powerful</c><00:05:11.320><c> models</c><00:05:11.639><c> like</c>

00:05:11.790 --> 00:05:11.800 align:start position:0%
yourself the more powerful models like
 

00:05:11.800 --> 00:05:14.990 align:start position:0%
yourself the more powerful models like
gb4<00:05:12.320><c> turbo</c><00:05:12.680><c> and</c><00:05:12.800><c> gb4</c><00:05:13.320><c> O</c><00:05:13.680><c> are</c><00:05:13.880><c> able</c><00:05:14.080><c> to</c><00:05:14.320><c> just</c><00:05:14.639><c> use</c>

00:05:14.990 --> 00:05:15.000 align:start position:0%
gb4 turbo and gb4 O are able to just use
 

00:05:15.000 --> 00:05:18.070 align:start position:0%
gb4 turbo and gb4 O are able to just use
these<00:05:15.280><c> instructions</c><00:05:16.240><c> and</c><00:05:17.240><c> interact</c><00:05:17.759><c> with</c><00:05:17.919><c> us</c>

00:05:18.070 --> 00:05:18.080 align:start position:0%
these instructions and interact with us
 

00:05:18.080 --> 00:05:20.710 align:start position:0%
these instructions and interact with us
in<00:05:18.199><c> a</c><00:05:18.400><c> very</c><00:05:19.280><c> humanlike</c><00:05:19.919><c> manner</c><00:05:20.240><c> we</c><00:05:20.360><c> don't</c><00:05:20.560><c> need</c>

00:05:20.710 --> 00:05:20.720 align:start position:0%
in a very humanlike manner we don't need
 

00:05:20.720 --> 00:05:23.390 align:start position:0%
in a very humanlike manner we don't need
to<00:05:21.080><c> tell</c><00:05:21.319><c> them</c><00:05:21.520><c> a</c><00:05:21.680><c> lot</c><00:05:22.080><c> we</c><00:05:22.199><c> can</c><00:05:22.400><c> just</c><00:05:22.639><c> say</c><00:05:23.280><c> these</c>

00:05:23.390 --> 00:05:23.400 align:start position:0%
to tell them a lot we can just say these
 

00:05:23.400 --> 00:05:25.590 align:start position:0%
to tell them a lot we can just say these
are<00:05:23.560><c> the</c><00:05:23.680><c> few</c><00:05:23.880><c> things</c><00:05:24.080><c> you</c><00:05:24.280><c> have</c><00:05:24.520><c> access</c><00:05:24.800><c> to</c><00:05:25.360><c> go</c>

00:05:25.590 --> 00:05:25.600 align:start position:0%
are the few things you have access to go
 

00:05:25.600 --> 00:05:29.189 align:start position:0%
are the few things you have access to go
figure<00:05:25.919><c> it</c><00:05:26.039><c> out</c><00:05:26.600><c> and</c><00:05:26.800><c> they</c><00:05:26.960><c> do</c><00:05:27.240><c> pretty</c><00:05:28.000><c> well</c><00:05:29.000><c> we</c>

00:05:29.189 --> 00:05:29.199 align:start position:0%
figure it out and they do pretty well we
 

00:05:29.199 --> 00:05:33.189 align:start position:0%
figure it out and they do pretty well we
also<00:05:29.800><c> add</c><00:05:30.440><c> storage</c><00:05:31.360><c> ped</c><00:05:31.759><c> by</c><00:05:32.080><c> postest</c><00:05:32.800><c> meaning</c>

00:05:33.189 --> 00:05:33.199 align:start position:0%
also add storage ped by postest meaning
 

00:05:33.199 --> 00:05:35.309 align:start position:0%
also add storage ped by postest meaning
all<00:05:33.360><c> the</c><00:05:33.520><c> runs</c><00:05:33.919><c> that</c><00:05:34.039><c> are</c><00:05:34.280><c> happening</c><00:05:34.639><c> are</c><00:05:34.919><c> in</c>

00:05:35.309 --> 00:05:35.319 align:start position:0%
all the runs that are happening are in
 

00:05:35.319 --> 00:05:37.189 align:start position:0%
all the runs that are happening are in
this<00:05:35.520><c> database</c><00:05:36.000><c> we</c><00:05:36.120><c> add</c><00:05:36.240><c> a</c><00:05:36.400><c> knowledge</c><00:05:36.759><c> base</c>

00:05:37.189 --> 00:05:37.199 align:start position:0%
this database we add a knowledge base
 

00:05:37.199 --> 00:05:39.150 align:start position:0%
this database we add a knowledge base
which<00:05:37.360><c> which</c><00:05:37.479><c> is</c><00:05:37.680><c> the</c><00:05:37.759><c> vector</c><00:05:38.039><c> DB</c><00:05:38.680><c> it</c><00:05:38.840><c> can</c><00:05:39.000><c> use</c>

00:05:39.150 --> 00:05:39.160 align:start position:0%
which which is the vector DB it can use
 

00:05:39.160 --> 00:05:39.909 align:start position:0%
which which is the vector DB it can use
for

00:05:39.909 --> 00:05:39.919 align:start position:0%
for
 

00:05:39.919 --> 00:05:43.110 align:start position:0%
for
retrieval<00:05:40.919><c> then</c><00:05:41.120><c> we</c><00:05:41.440><c> add</c><00:05:41.680><c> a</c><00:05:41.840><c> set</c><00:05:42.000><c> of</c><00:05:42.160><c> tools</c><00:05:42.680><c> and</c>

00:05:43.110 --> 00:05:43.120 align:start position:0%
retrieval then we add a set of tools and
 

00:05:43.120 --> 00:05:45.670 align:start position:0%
retrieval then we add a set of tools and
give<00:05:43.280><c> it</c><00:05:43.400><c> a</c><00:05:43.560><c> team</c><00:05:44.120><c> this</c><00:05:44.240><c> is</c><00:05:44.400><c> the</c><00:05:44.520><c> fun</c><00:05:45.319><c> so</c><00:05:45.479><c> I'm</c>

00:05:45.670 --> 00:05:45.680 align:start position:0%
give it a team this is the fun so I'm
 

00:05:45.680 --> 00:05:47.550 align:start position:0%
give it a team this is the fun so I'm
going<00:05:45.759><c> to</c><00:05:45.919><c> go</c><00:05:46.080><c> up</c><00:05:46.319><c> Scroll</c><00:05:46.680><c> up</c><00:05:46.919><c> for</c><00:05:47.120><c> you</c><00:05:47.400><c> and</c>

00:05:47.550 --> 00:05:47.560 align:start position:0%
going to go up Scroll up for you and
 

00:05:47.560 --> 00:05:50.430 align:start position:0%
going to go up Scroll up for you and
show<00:05:47.759><c> you</c><00:05:47.960><c> the</c><00:05:48.120><c> tools</c><00:05:48.479><c> that</c><00:05:48.639><c> it</c><00:05:48.800><c> has</c><00:05:49.039><c> access</c>

00:05:50.430 --> 00:05:50.440 align:start position:0%
show you the tools that it has access
 

00:05:50.440 --> 00:05:52.830 align:start position:0%
show you the tools that it has access
to<00:05:51.440><c> so</c><00:05:51.639><c> when</c><00:05:51.759><c> we</c><00:05:51.919><c> select</c><00:05:52.199><c> the</c><00:05:52.280><c> tools</c><00:05:52.560><c> from</c><00:05:52.680><c> the</c>

00:05:52.830 --> 00:05:52.840 align:start position:0%
to so when we select the tools from the
 

00:05:52.840 --> 00:05:55.230 align:start position:0%
to so when we select the tools from the
UI<00:05:53.680><c> it'll</c><00:05:54.000><c> enable</c><00:05:54.360><c> those</c><00:05:54.520><c> tools</c><00:05:54.800><c> here</c><00:05:55.000><c> added</c>

00:05:55.230 --> 00:05:55.240 align:start position:0%
UI it'll enable those tools here added
 

00:05:55.240 --> 00:05:56.870 align:start position:0%
UI it'll enable those tools here added
to<00:05:55.360><c> the</c><00:05:55.520><c> list</c><00:05:55.800><c> which</c><00:05:55.880><c> are</c><00:05:56.160><c> then</c><00:05:56.360><c> added</c><00:05:56.600><c> to</c><00:05:56.759><c> the</c>

00:05:56.870 --> 00:05:56.880 align:start position:0%
to the list which are then added to the
 

00:05:56.880 --> 00:05:58.830 align:start position:0%
to the list which are then added to the
assistant<00:05:57.800><c> along</c><00:05:58.039><c> with</c><00:05:58.160><c> a</c><00:05:58.280><c> set</c><00:05:58.400><c> of</c><00:05:58.520><c> extra</c>

00:05:58.830 --> 00:05:58.840 align:start position:0%
assistant along with a set of extra
 

00:05:58.840 --> 00:05:59.870 align:start position:0%
assistant along with a set of extra
instructions<00:05:59.280><c> for</c><00:05:59.400><c> example</c><00:05:59.600><c> example</c><00:05:59.800><c> if</c>

00:05:59.870 --> 00:05:59.880 align:start position:0%
instructions for example example if
 

00:05:59.880 --> 00:06:02.070 align:start position:0%
instructions for example example if
you're<00:05:59.960><c> using</c><00:06:00.199><c> the</c><00:06:00.400><c> file</c><00:06:00.600><c> tool</c><00:06:01.520><c> we</c><00:06:01.680><c> tell</c><00:06:01.880><c> the</c>

00:06:02.070 --> 00:06:02.080 align:start position:0%
you're using the file tool we tell the
 

00:06:02.080 --> 00:06:04.150 align:start position:0%
you're using the file tool we tell the
assistant<00:06:02.720><c> you</c><00:06:02.800><c> can</c><00:06:02.960><c> use</c><00:06:03.160><c> the</c><00:06:03.360><c> read</c><00:06:03.680><c> file</c><00:06:03.919><c> tool</c>

00:06:04.150 --> 00:06:04.160 align:start position:0%
assistant you can use the read file tool
 

00:06:04.160 --> 00:06:07.110 align:start position:0%
assistant you can use the read file tool
to<00:06:04.280><c> read</c><00:06:04.520><c> a</c><00:06:04.960><c> file</c><00:06:05.960><c> this</c><00:06:06.120><c> steers</c><00:06:06.560><c> it</c><00:06:06.759><c> in</c><00:06:06.919><c> that</c>

00:06:07.110 --> 00:06:07.120 align:start position:0%
to read a file this steers it in that
 

00:06:07.120 --> 00:06:08.990 align:start position:0%
to read a file this steers it in that
direction<00:06:07.520><c> like</c><00:06:07.720><c> if</c><00:06:07.840><c> I'm</c><00:06:07.960><c> saying</c><00:06:08.639><c> what's</c><00:06:08.880><c> in</c>

00:06:08.990 --> 00:06:09.000 align:start position:0%
direction like if I'm saying what's in
 

00:06:09.000 --> 00:06:11.430 align:start position:0%
direction like if I'm saying what's in
the<00:06:09.120><c> readme</c><00:06:09.599><c> file</c><00:06:10.000><c> it'll</c><00:06:10.400><c> call</c><00:06:10.759><c> this</c><00:06:10.919><c> tool</c>

00:06:11.430 --> 00:06:11.440 align:start position:0%
the readme file it'll call this tool
 

00:06:11.440 --> 00:06:13.710 align:start position:0%
the readme file it'll call this tool
this<00:06:11.919><c> this</c><00:06:12.080><c> gives</c><00:06:12.280><c> the</c><00:06:12.919><c> assistant</c><00:06:13.360><c> extra</c>

00:06:13.710 --> 00:06:13.720 align:start position:0%
this this gives the assistant extra
 

00:06:13.720 --> 00:06:15.870 align:start position:0%
this this gives the assistant extra
information<00:06:14.680><c> on</c><00:06:14.880><c> how</c><00:06:15.000><c> to</c><00:06:15.160><c> achieve</c><00:06:15.400><c> a</c><00:06:15.520><c> task</c><00:06:15.759><c> for</c>

00:06:15.870 --> 00:06:15.880 align:start position:0%
information on how to achieve a task for
 

00:06:15.880 --> 00:06:18.029 align:start position:0%
information on how to achieve a task for
the<00:06:15.960><c> user</c><00:06:16.280><c> this</c><00:06:16.400><c> is</c><00:06:16.639><c> kind</c><00:06:16.800><c> of</c><00:06:16.919><c> sort</c><00:06:17.120><c> of</c><00:06:17.720><c> we're</c>

00:06:18.029 --> 00:06:18.039 align:start position:0%
the user this is kind of sort of we're
 

00:06:18.039 --> 00:06:21.110 align:start position:0%
the user this is kind of sort of we're
programming<00:06:18.680><c> for</c><00:06:19.039><c> AI</c><00:06:19.360><c> agents</c><00:06:19.720><c> has</c><00:06:20.120><c> let</c><00:06:20.319><c> us</c><00:06:20.560><c> we</c>

00:06:21.110 --> 00:06:21.120 align:start position:0%
programming for AI agents has let us we
 

00:06:21.120 --> 00:06:23.029 align:start position:0%
programming for AI agents has let us we
write<00:06:21.400><c> what</c><00:06:21.520><c> it</c><00:06:21.639><c> needs</c><00:06:21.840><c> to</c><00:06:22.000><c> do</c><00:06:22.599><c> we</c><00:06:22.759><c> tell</c><00:06:22.919><c> it</c>

00:06:23.029 --> 00:06:23.039 align:start position:0%
write what it needs to do we tell it
 

00:06:23.039 --> 00:06:25.189 align:start position:0%
write what it needs to do we tell it
okay<00:06:23.240><c> use</c><00:06:23.440><c> this</c><00:06:23.599><c> tool</c><00:06:23.800><c> for</c><00:06:23.960><c> this</c><00:06:24.160><c> task</c><00:06:24.599><c> and</c><00:06:25.080><c> it</c>

00:06:25.189 --> 00:06:25.199 align:start position:0%
okay use this tool for this task and it
 

00:06:25.199 --> 00:06:26.670 align:start position:0%
okay use this tool for this task and it
follows<00:06:25.639><c> that</c><00:06:25.800><c> instruction</c><00:06:26.240><c> pretty</c><00:06:26.479><c> well</c>

00:06:26.670 --> 00:06:26.680 align:start position:0%
follows that instruction pretty well
 

00:06:26.680 --> 00:06:28.350 align:start position:0%
follows that instruction pretty well
especially<00:06:27.000><c> with</c><00:06:27.120><c> the</c><00:06:27.240><c> good</c>

00:06:28.350 --> 00:06:28.360 align:start position:0%
especially with the good
 

00:06:28.360 --> 00:06:30.430 align:start position:0%
especially with the good
models<00:06:29.360><c> then</c><00:06:29.639><c> we're</c><00:06:29.759><c> going</c><00:06:29.840><c> to</c><00:06:30.000><c> give</c><00:06:30.120><c> it</c><00:06:30.240><c> some</c>

00:06:30.430 --> 00:06:30.440 align:start position:0%
models then we're going to give it some
 

00:06:30.440 --> 00:06:32.070 align:start position:0%
models then we're going to give it some
team<00:06:30.720><c> members</c><00:06:31.360><c> we're</c><00:06:31.520><c> going</c><00:06:31.639><c> to</c><00:06:31.720><c> give</c><00:06:31.840><c> it</c><00:06:31.919><c> a</c>

00:06:32.070 --> 00:06:32.080 align:start position:0%
team members we're going to give it a
 

00:06:32.080 --> 00:06:34.870 align:start position:0%
team members we're going to give it a
data<00:06:32.560><c> analyst</c><00:06:33.560><c> fire</c><00:06:33.880><c> data</c><00:06:34.120><c> comes</c><00:06:34.440><c> with</c>

00:06:34.870 --> 00:06:34.880 align:start position:0%
data analyst fire data comes with
 

00:06:34.880 --> 00:06:37.309 align:start position:0%
data analyst fire data comes with
pre-built<00:06:35.400><c> customer</c><00:06:35.800><c> assistance</c><00:06:36.639><c> as</c><00:06:36.840><c> well</c><00:06:37.160><c> so</c>

00:06:37.309 --> 00:06:37.319 align:start position:0%
pre-built customer assistance as well so
 

00:06:37.319 --> 00:06:39.029 align:start position:0%
pre-built customer assistance as well so
we<00:06:37.440><c> have</c><00:06:37.599><c> a</c><00:06:37.720><c> duck</c><00:06:37.960><c> TB</c><00:06:38.240><c> assistant</c><00:06:38.599><c> that</c><00:06:38.759><c> is</c>

00:06:39.029 --> 00:06:39.039 align:start position:0%
we have a duck TB assistant that is
 

00:06:39.039 --> 00:06:41.350 align:start position:0%
we have a duck TB assistant that is
fantastic<00:06:39.599><c> for</c><00:06:39.759><c> data</c><00:06:40.000><c> analysis</c><00:06:41.000><c> give</c><00:06:41.120><c> it</c><00:06:41.240><c> a</c>

00:06:41.350 --> 00:06:41.360 align:start position:0%
fantastic for data analysis give it a
 

00:06:41.360 --> 00:06:43.950 align:start position:0%
fantastic for data analysis give it a
set<00:06:41.479><c> of</c><00:06:41.599><c> CSV</c><00:06:42.120><c> files</c><00:06:42.479><c> Park</c><00:06:42.919><c> files</c><00:06:43.240><c> Json</c><00:06:43.639><c> files</c>

00:06:43.950 --> 00:06:43.960 align:start position:0%
set of CSV files Park files Json files
 

00:06:43.960 --> 00:06:46.550 align:start position:0%
set of CSV files Park files Json files
anything<00:06:44.280><c> which</c><00:06:44.440><c> duck</c><00:06:44.680><c> DB</c><00:06:44.960><c> can</c><00:06:45.319><c> process</c><00:06:46.319><c> and</c>

00:06:46.550 --> 00:06:46.560 align:start position:0%
anything which duck DB can process and
 

00:06:46.560 --> 00:06:49.629 align:start position:0%
anything which duck DB can process and
it'll<00:06:47.160><c> use</c><00:06:47.520><c> that</c><00:06:47.759><c> information</c><00:06:48.759><c> to</c><00:06:49.160><c> analyze</c>

00:06:49.629 --> 00:06:49.639 align:start position:0%
it'll use that information to analyze
 

00:06:49.639 --> 00:06:52.390 align:start position:0%
it'll use that information to analyze
data<00:06:49.840><c> and</c><00:06:50.120><c> return</c><00:06:50.680><c> results</c><00:06:51.680><c> similarly</c><00:06:52.160><c> a</c>

00:06:52.390 --> 00:06:52.400 align:start position:0%
data and return results similarly a
 

00:06:52.400 --> 00:06:54.270 align:start position:0%
data and return results similarly a
python<00:06:52.840><c> assistant</c><00:06:53.280><c> can</c><00:06:53.560><c> write</c><00:06:53.800><c> and</c><00:06:54.000><c> run</c>

00:06:54.270 --> 00:06:54.280 align:start position:0%
python assistant can write and run
 

00:06:54.280 --> 00:06:57.110 align:start position:0%
python assistant can write and run
python<00:06:54.599><c> code</c><00:06:55.080><c> a</c><00:06:55.319><c> research</c><00:06:55.879><c> assistant</c><00:06:56.879><c> will</c>

00:06:57.110 --> 00:06:57.120 align:start position:0%
python code a research assistant will
 

00:06:57.120 --> 00:06:59.029 align:start position:0%
python code a research assistant will
generate<00:06:57.520><c> a</c><00:06:57.680><c> research</c><00:06:58.120><c> report</c><00:06:58.440><c> so</c><00:06:58.639><c> when</c><00:06:58.879><c> we</c>

00:06:59.029 --> 00:06:59.039 align:start position:0%
generate a research report so when we
 

00:06:59.039 --> 00:07:01.950 align:start position:0%
generate a research report so when we
ask<00:06:59.720><c> Now</c><00:07:00.199><c> give</c><00:07:00.319><c> me</c><00:07:00.400><c> a</c><00:07:00.520><c> report</c><00:07:00.759><c> on</c><00:07:00.879><c> IBM</c><00:07:01.319><c> hap</c>

00:07:01.950 --> 00:07:01.960 align:start position:0%
ask Now give me a report on IBM hap
 

00:07:01.960 --> 00:07:05.430 align:start position:0%
ask Now give me a report on IBM hap
acquisition<00:07:02.960><c> it'll</c><00:07:03.280><c> use</c><00:07:03.639><c> the</c><00:07:04.240><c> exer</c><00:07:04.759><c> tools</c>

00:07:05.430 --> 00:07:05.440 align:start position:0%
acquisition it'll use the exer tools
 

00:07:05.440 --> 00:07:07.230 align:start position:0%
acquisition it'll use the exer tools
which<00:07:05.599><c> is</c><00:07:05.759><c> the</c><00:07:05.960><c> exer</c><00:07:06.319><c> search</c><00:07:06.560><c> a</c><00:07:06.800><c> fantastic</c>

00:07:07.230 --> 00:07:07.240 align:start position:0%
which is the exer search a fantastic
 

00:07:07.240 --> 00:07:10.390 align:start position:0%
which is the exer search a fantastic
search<00:07:07.599><c> engine</c><00:07:08.400><c> to</c><00:07:08.759><c> get</c><00:07:08.960><c> all</c><00:07:09.160><c> the</c><00:07:09.319><c> results</c><00:07:10.120><c> and</c>

00:07:10.390 --> 00:07:10.400 align:start position:0%
search engine to get all the results and
 

00:07:10.400 --> 00:07:12.350 align:start position:0%
search engine to get all the results and
answer<00:07:10.800><c> that</c><00:07:11.000><c> question</c><00:07:11.280><c> for</c>

00:07:12.350 --> 00:07:12.360 align:start position:0%
answer that question for
 

00:07:12.360 --> 00:07:15.070 align:start position:0%
answer that question for
us<00:07:13.360><c> finally</c><00:07:13.720><c> it</c><00:07:13.879><c> also</c><00:07:14.080><c> has</c><00:07:14.240><c> an</c><00:07:14.479><c> investment</c>

00:07:15.070 --> 00:07:15.080 align:start position:0%
us finally it also has an investment
 

00:07:15.080 --> 00:07:17.270 align:start position:0%
us finally it also has an investment
assistant<00:07:15.560><c> so</c><00:07:15.680><c> if</c><00:07:15.840><c> we</c><00:07:16.120><c> want</c><00:07:16.319><c> to</c><00:07:16.599><c> ask</c><00:07:16.879><c> questions</c>

00:07:17.270 --> 00:07:17.280 align:start position:0%
assistant so if we want to ask questions
 

00:07:17.280 --> 00:07:20.110 align:start position:0%
assistant so if we want to ask questions
like<00:07:17.560><c> hey</c><00:07:17.879><c> uh</c><00:07:17.960><c> shall</c><00:07:18.160><c> I</c><00:07:18.280><c> invest</c><00:07:18.520><c> in</c><00:07:18.680><c> IM</c><00:07:19.680><c> it</c>

00:07:20.110 --> 00:07:20.120 align:start position:0%
like hey uh shall I invest in IM it
 

00:07:20.120 --> 00:07:21.589 align:start position:0%
like hey uh shall I invest in IM it
it'll<00:07:20.440><c> reach</c><00:07:20.639><c> out</c><00:07:20.840><c> to</c><00:07:20.960><c> the</c><00:07:21.120><c> investment</c>

00:07:21.589 --> 00:07:21.599 align:start position:0%
it'll reach out to the investment
 

00:07:21.599 --> 00:07:23.550 align:start position:0%
it'll reach out to the investment
assistant<00:07:22.080><c> generate</c><00:07:22.400><c> a</c><00:07:22.560><c> report</c><00:07:22.840><c> for</c><00:07:23.000><c> us</c><00:07:23.199><c> and</c>

00:07:23.550 --> 00:07:23.560 align:start position:0%
assistant generate a report for us and
 

00:07:23.560 --> 00:07:27.070 align:start position:0%
assistant generate a report for us and
then<00:07:24.400><c> return</c><00:07:24.639><c> the</c><00:07:24.840><c> answer</c><00:07:25.160><c> for</c><00:07:25.479><c> us</c><00:07:26.479><c> uh</c><00:07:26.599><c> so</c><00:07:26.919><c> this</c>

00:07:27.070 --> 00:07:27.080 align:start position:0%
then return the answer for us uh so this
 

00:07:27.080 --> 00:07:30.950 align:start position:0%
then return the answer for us uh so this
way<00:07:27.319><c> we</c><00:07:27.479><c> give</c><00:07:27.680><c> our</c><00:07:28.280><c> main</c><00:07:28.639><c> AI</c><00:07:29.000><c> agent</c><00:07:29.360><c> a</c><00:07:29.759><c> set</c>

00:07:30.950 --> 00:07:30.960 align:start position:0%
way we give our main AI agent a set
 

00:07:30.960 --> 00:07:36.150 align:start position:0%
way we give our main AI agent a set
of<00:07:31.960><c> tools</c><00:07:32.720><c> and</c><00:07:33.400><c> team</c><00:07:33.720><c> members</c><00:07:34.560><c> to</c><00:07:34.800><c> achieve</c>

00:07:36.150 --> 00:07:36.160 align:start position:0%
of tools and team members to achieve
 

00:07:36.160 --> 00:07:38.909 align:start position:0%
of tools and team members to achieve
tasks<00:07:37.160><c> finally</c><00:07:37.599><c> when</c><00:07:37.720><c> you</c><00:07:37.840><c> run</c><00:07:38.120><c> that</c><00:07:38.680><c> this</c><00:07:38.800><c> is</c>

00:07:38.909 --> 00:07:38.919 align:start position:0%
tasks finally when you run that this is
 

00:07:38.919 --> 00:07:40.790 align:start position:0%
tasks finally when you run that this is
what<00:07:39.039><c> it</c><00:07:39.199><c> looks</c><00:07:39.479><c> like</c><00:07:39.919><c> when</c><00:07:40.240><c> we</c><00:07:40.400><c> asked</c><00:07:40.639><c> a</c>

00:07:40.790 --> 00:07:40.800 align:start position:0%
what it looks like when we asked a
 

00:07:40.800 --> 00:07:42.110 align:start position:0%
what it looks like when we asked a
question<00:07:41.080><c> now</c><00:07:41.240><c> let's</c><00:07:41.400><c> check</c><00:07:41.599><c> write</c><00:07:41.759><c> a</c><00:07:41.879><c> report</c>

00:07:42.110 --> 00:07:42.120 align:start position:0%
question now let's check write a report
 

00:07:42.120 --> 00:07:45.230 align:start position:0%
question now let's check write a report
on<00:07:42.199><c> the</c><00:07:42.319><c> IBM</c><00:07:42.639><c> hop</c><00:07:43.199><c> acquisition</c><00:07:44.199><c> delegated</c><00:07:44.720><c> the</c>

00:07:45.230 --> 00:07:45.240 align:start position:0%
on the IBM hop acquisition delegated the
 

00:07:45.240 --> 00:07:49.909 align:start position:0%
on the IBM hop acquisition delegated the
task<00:07:46.240><c> ran</c><00:07:46.520><c> the</c><00:07:46.680><c> task</c><00:07:47.000><c> for</c><00:07:47.199><c> us</c><00:07:47.840><c> gave</c><00:07:48.080><c> us</c><00:07:48.919><c> details</c>

00:07:49.909 --> 00:07:49.919 align:start position:0%
task ran the task for us gave us details
 

00:07:49.919 --> 00:07:51.550 align:start position:0%
task ran the task for us gave us details
wrote<00:07:50.159><c> a</c><00:07:50.319><c> very</c><00:07:50.520><c> nice</c><00:07:50.720><c> report</c><00:07:50.960><c> for</c><00:07:51.120><c> us</c><00:07:51.280><c> and</c><00:07:51.400><c> gave</c>

00:07:51.550 --> 00:07:51.560 align:start position:0%
wrote a very nice report for us and gave
 

00:07:51.560 --> 00:07:54.990 align:start position:0%
wrote a very nice report for us and gave
us<00:07:51.720><c> referen</c><00:07:52.199><c> as</c><00:07:52.360><c> well</c><00:07:53.159><c> so</c><00:07:53.840><c> uh</c><00:07:54.000><c> that's</c><00:07:54.159><c> it</c><00:07:54.360><c> folks</c>

00:07:54.990 --> 00:07:55.000 align:start position:0%
us referen as well so uh that's it folks
 

00:07:55.000 --> 00:07:58.029 align:start position:0%
us referen as well so uh that's it folks
this<00:07:55.240><c> was</c><00:07:55.759><c> an</c><00:07:56.039><c> introduction</c><00:07:56.520><c> to</c><00:07:56.720><c> building</c><00:07:57.520><c> AI</c>

00:07:58.029 --> 00:07:58.039 align:start position:0%
this was an introduction to building AI
 

00:07:58.039 --> 00:08:02.309 align:start position:0%
this was an introduction to building AI
agents<00:07:58.520><c> a</c><00:07:58.680><c> team</c><00:07:58.960><c> of</c><00:07:59.080><c> AI</c><00:07:59.520><c> agents</c><00:08:00.199><c> using</c><00:08:00.560><c> GPT</c><00:08:01.319><c> 4</c>

00:08:02.309 --> 00:08:02.319 align:start position:0%
agents a team of AI agents using GPT 4
 

00:08:02.319 --> 00:08:04.350 align:start position:0%
agents a team of AI agents using GPT 4
if<00:08:02.440><c> you</c><00:08:02.560><c> have</c><00:08:02.720><c> any</c><00:08:02.960><c> questions</c><00:08:03.599><c> drop</c><00:08:03.919><c> by</c><00:08:04.080><c> in</c><00:08:04.199><c> the</c>

00:08:04.350 --> 00:08:04.360 align:start position:0%
if you have any questions drop by in the
 

00:08:04.360 --> 00:08:07.270 align:start position:0%
if you have any questions drop by in the
Discord<00:08:04.960><c> open</c><00:08:05.199><c> a</c><00:08:05.400><c> GitHub</c><00:08:05.759><c> issue</c><00:08:06.680><c> and</c><00:08:07.039><c> have</c><00:08:07.159><c> a</c>

00:08:07.270 --> 00:08:07.280 align:start position:0%
Discord open a GitHub issue and have a
 

00:08:07.280 --> 00:08:09.680 align:start position:0%
Discord open a GitHub issue and have a
great<00:08:07.520><c> day</c>

