WEBVTT
Kind: captions
Language: en

00:00:00.000 --> 00:00:01.520 align:start position:0%
 
is<00:00:00.060><c> the</c><00:00:00.210><c> very</c><00:00:00.420><c> end</c><00:00:00.570><c> of</c><00:00:00.719><c> the</c><00:00:00.840><c> meta</c><00:00:01.020><c> base</c><00:00:01.199><c> series</c>

00:00:01.520 --> 00:00:01.530 align:start position:0%
is the very end of the meta base series
 

00:00:01.530 --> 00:00:03.970 align:start position:0%
is the very end of the meta base series
meta<00:00:01.949><c> based</c><00:00:02.190><c> tutorial</c><00:00:02.850><c> number</c><00:00:03.000><c> seven</c><00:00:03.600><c> and</c>

00:00:03.970 --> 00:00:03.980 align:start position:0%
meta based tutorial number seven and
 

00:00:03.980 --> 00:00:06.200 align:start position:0%
meta based tutorial number seven and
finally<00:00:04.980><c> we</c><00:00:05.100><c> have</c><00:00:05.250><c> written</c><00:00:05.430><c> a</c><00:00:05.640><c> question</c><00:00:06.060><c> over</c>

00:00:06.200 --> 00:00:06.210 align:start position:0%
finally we have written a question over
 

00:00:06.210 --> 00:00:07.940 align:start position:0%
finally we have written a question over
here<00:00:06.330><c> and</c><00:00:06.600><c> we</c><00:00:06.690><c> want</c><00:00:06.899><c> to</c><00:00:06.960><c> add</c><00:00:07.109><c> at</c><00:00:07.259><c> our</c><00:00:07.500><c> data</c><00:00:07.710><c> base</c>

00:00:07.940 --> 00:00:07.950 align:start position:0%
here and we want to add at our data base
 

00:00:07.950 --> 00:00:11.110 align:start position:0%
here and we want to add at our data base
a<00:00:08.189><c> data</c><00:00:08.580><c> dashboard</c><00:00:08.820><c> called</c><00:00:09.570><c> clients</c><00:00:10.290><c> and</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
a data dashboard called clients and
 

00:00:11.120 --> 00:00:15.350 align:start position:0%
a data dashboard called clients and
there<00:00:12.120><c> it</c><00:00:12.300><c> is</c><00:00:12.980><c> count</c><00:00:13.980><c> of</c><00:00:14.160><c> clients</c><00:00:14.700><c> count</c><00:00:15.210><c> of</c>

00:00:15.350 --> 00:00:15.360 align:start position:0%
there it is count of clients count of
 

00:00:15.360 --> 00:00:17.060 align:start position:0%
there it is count of clients count of
clients<00:00:15.660><c> is</c><00:00:15.750><c> already</c><00:00:16.139><c> on</c><00:00:16.289><c> here</c><00:00:16.650><c> and</c><00:00:16.770><c> as</c><00:00:16.890><c> you</c>

00:00:17.060 --> 00:00:17.070 align:start position:0%
clients is already on here and as you
 

00:00:17.070 --> 00:00:19.609 align:start position:0%
clients is already on here and as you
can<00:00:17.220><c> see</c><00:00:17.460><c> we</c><00:00:18.390><c> added</c><00:00:18.840><c> count</c><00:00:19.080><c> of</c><00:00:19.230><c> clients</c>

00:00:19.609 --> 00:00:19.619 align:start position:0%
can see we added count of clients
 

00:00:19.619 --> 00:00:23.599 align:start position:0%
can see we added count of clients
beforehand<00:00:20.240><c> and</c><00:00:21.240><c> it</c><00:00:21.390><c> got</c><00:00:21.600><c> saved</c><00:00:22.279><c> this</c><00:00:23.279><c> is</c><00:00:23.430><c> just</c>

00:00:23.599 --> 00:00:23.609 align:start position:0%
beforehand and it got saved this is just
 

00:00:23.609 --> 00:00:24.830 align:start position:0%
beforehand and it got saved this is just
a<00:00:23.699><c> bigger</c><00:00:23.880><c> version</c><00:00:24.000><c> of</c><00:00:24.449><c> it</c>

00:00:24.830 --> 00:00:24.840 align:start position:0%
a bigger version of it
 

00:00:24.840 --> 00:00:27.560 align:start position:0%
a bigger version of it
ok<00:00:25.619><c> and</c><00:00:25.890><c> over</c><00:00:26.340><c> here</c><00:00:26.490><c> again</c><00:00:26.789><c> now</c><00:00:27.000><c> we</c><00:00:27.060><c> have</c><00:00:27.240><c> a</c><00:00:27.269><c> bar</c>

00:00:27.560 --> 00:00:27.570 align:start position:0%
ok and over here again now we have a bar
 

00:00:27.570 --> 00:00:29.300 align:start position:0%
ok and over here again now we have a bar
chart<00:00:27.840><c> we're</c><00:00:28.230><c> gonna</c><00:00:28.320><c> press</c><00:00:28.650><c> save</c><00:00:28.890><c> because</c><00:00:29.099><c> we</c>

00:00:29.300 --> 00:00:29.310 align:start position:0%
chart we're gonna press save because we
 

00:00:29.310 --> 00:00:31.580 align:start position:0%
chart we're gonna press save because we
added<00:00:29.519><c> a</c><00:00:29.760><c> new</c><00:00:29.880><c> question</c><00:00:30.179><c> here</c><00:00:30.599><c> and</c><00:00:30.869><c> whichever</c>

00:00:31.580 --> 00:00:31.590 align:start position:0%
added a new question here and whichever
 

00:00:31.590 --> 00:00:34.250 align:start position:0%
added a new question here and whichever
questions<00:00:32.070><c> you</c><00:00:32.189><c> want</c><00:00:32.430><c> if</c><00:00:32.669><c> you</c><00:00:32.910><c> want</c><00:00:33.149><c> a</c><00:00:33.450><c> filter</c>

00:00:34.250 --> 00:00:34.260 align:start position:0%
questions you want if you want a filter
 

00:00:34.260 --> 00:00:37.549 align:start position:0%
questions you want if you want a filter
to<00:00:34.290><c> act</c><00:00:34.590><c> on</c><00:00:34.829><c> two</c><00:00:35.010><c> of</c><00:00:35.370><c> them</c><00:00:35.579><c> that's</c><00:00:35.969><c> fine</c><00:00:36.300><c> too</c><00:00:36.630><c> um</c>

00:00:37.549 --> 00:00:37.559 align:start position:0%
to act on two of them that's fine too um
 

00:00:37.559 --> 00:00:41.979 align:start position:0%
to act on two of them that's fine too um
let's<00:00:38.340><c> see</c>

00:00:41.979 --> 00:00:41.989 align:start position:0%
 
 

00:00:41.989 --> 00:00:44.360 align:start position:0%
 
one<00:00:42.989><c> show</c><00:00:43.230><c> this</c><00:00:43.500><c> one</c><00:00:43.710><c> was</c><00:00:43.890><c> just</c><00:00:44.100><c> three</c>

00:00:44.360 --> 00:00:44.370 align:start position:0%
one show this one was just three
 

00:00:44.370 --> 00:00:48.860 align:start position:0%
one show this one was just three
Thursday<00:00:45.059><c> January</c><00:00:45.329><c> 18</c><00:00:46.550><c> let's</c><00:00:47.550><c> do</c><00:00:47.760><c> an</c><00:00:48.059><c> uary</c><00:00:48.329><c> 18</c>

00:00:48.860 --> 00:00:48.870 align:start position:0%
Thursday January 18 let's do an uary 18
 

00:00:48.870 --> 00:00:55.130 align:start position:0%
Thursday January 18 let's do an uary 18
February<00:00:50.420><c> January</c><00:00:51.420><c> 18</c><00:00:52.260><c> Update</c><00:00:53.039><c> filter</c><00:00:54.140><c> and</c>

00:00:55.130 --> 00:00:55.140 align:start position:0%
February January 18 Update filter and
 

00:00:55.140 --> 00:00:58.580 align:start position:0%
February January 18 Update filter and
it's<00:00:55.440><c> gonna</c><00:00:55.590><c> work</c><00:00:55.829><c> oh</c>

00:00:58.580 --> 00:00:58.590 align:start position:0%
it's gonna work oh
 

00:00:58.590 --> 00:01:00.439 align:start position:0%
it's gonna work oh
oh<00:00:58.620><c> yeah</c><00:00:59.280><c> cuz</c><00:00:59.460><c> I</c><00:00:59.550><c> didn't</c><00:00:59.850><c> tie</c><00:01:00.030><c> it</c><00:01:00.059><c> to</c><00:01:00.420><c> that</c>

00:01:00.439 --> 00:01:00.449 align:start position:0%
oh yeah cuz I didn't tie it to that
 

00:01:00.449 --> 00:01:02.060 align:start position:0%
oh yeah cuz I didn't tie it to that
specific<00:01:01.050><c> day</c><00:01:01.199><c> but</c><00:01:01.379><c> this</c><00:01:01.530><c> specific</c><00:01:01.710><c> question</c>

00:01:02.060 --> 00:01:02.070 align:start position:0%
specific day but this specific question
 

00:01:02.070 --> 00:01:04.280 align:start position:0%
specific day but this specific question
I<00:01:02.399><c> tied</c><00:01:02.699><c> it</c><00:01:02.730><c> remember</c><00:01:03.629><c> you</c><00:01:03.750><c> need</c><00:01:03.870><c> to</c><00:01:03.930><c> go</c><00:01:04.110><c> up</c>

00:01:04.280 --> 00:01:04.290 align:start position:0%
I tied it remember you need to go up
 

00:01:04.290 --> 00:01:07.039 align:start position:0%
I tied it remember you need to go up
here<00:01:04.589><c> you</c><00:01:04.710><c> need</c><00:01:04.830><c> to</c><00:01:04.949><c> press</c><00:01:05.220><c> you</c><00:01:06.119><c> need</c><00:01:06.270><c> to</c><00:01:06.540><c> every</c>

00:01:07.039 --> 00:01:07.049 align:start position:0%
here you need to press you need to every
 

00:01:07.049 --> 00:01:09.490 align:start position:0%
here you need to press you need to every
time<00:01:07.200><c> you</c><00:01:07.320><c> add</c><00:01:07.439><c> a</c><00:01:07.470><c> new</c><00:01:07.560><c> question</c><00:01:08.240><c> you</c><00:01:09.240><c> need</c><00:01:09.330><c> to</c>

00:01:09.490 --> 00:01:09.500 align:start position:0%
time you add a new question you need to
 

00:01:09.500 --> 00:01:13.070 align:start position:0%
time you add a new question you need to
tie<00:01:10.500><c> that</c><00:01:11.189><c> filter</c><00:01:11.939><c> with</c><00:01:12.270><c> a</c><00:01:12.299><c> specific</c><00:01:12.600><c> value</c>

00:01:13.070 --> 00:01:13.080 align:start position:0%
tie that filter with a specific value
 

00:01:13.080 --> 00:01:15.560 align:start position:0%
tie that filter with a specific value
within<00:01:13.680><c> this</c><00:01:13.830><c> question</c><00:01:14.460><c> so</c><00:01:15.270><c> now</c><00:01:15.299><c> I</c><00:01:15.360><c> have</c>

00:01:15.560 --> 00:01:15.570 align:start position:0%
within this question so now I have
 

00:01:15.570 --> 00:01:17.300 align:start position:0%
within this question so now I have
client<00:01:15.900><c> creation</c><00:01:16.290><c> data</c><00:01:16.500><c> saved</c><00:01:16.799><c> for</c><00:01:17.009><c> both</c><00:01:17.159><c> of</c>

00:01:17.300 --> 00:01:17.310 align:start position:0%
client creation data saved for both of
 

00:01:17.310 --> 00:01:21.950 align:start position:0%
client creation data saved for both of
them<00:01:17.430><c> done</c><00:01:17.939><c> and</c><00:01:18.259><c> save</c><00:01:19.259><c> this</c><00:01:19.560><c> and</c><00:01:20.390><c> I</c><00:01:21.390><c> go</c><00:01:21.540><c> back</c><00:01:21.720><c> in</c>

00:01:21.950 --> 00:01:21.960 align:start position:0%
them done and save this and I go back in
 

00:01:21.960 --> 00:01:25.130 align:start position:0%
them done and save this and I go back in
here<00:01:22.170><c> client</c><00:01:22.590><c> creation</c><00:01:23.009><c> date</c><00:01:23.220><c> is</c><00:01:23.369><c> January</c><00:01:24.140><c> is</c>

00:01:25.130 --> 00:01:25.140 align:start position:0%
here client creation date is January is
 

00:01:25.140 --> 00:01:27.490 align:start position:0%
here client creation date is January is
on<00:01:25.500><c> January</c><00:01:26.189><c> 21st</c>

00:01:27.490 --> 00:01:27.500 align:start position:0%
on January 21st
 

00:01:27.500 --> 00:01:31.130 align:start position:0%
on January 21st
so<00:01:28.500><c> you</c><00:01:28.680><c> January</c><00:01:29.070><c> 21</c><00:01:29.880><c> the</c><00:01:30.450><c> answer</c><00:01:30.780><c> should</c><00:01:30.930><c> be</c>

00:01:31.130 --> 00:01:31.140 align:start position:0%
so you January 21 the answer should be
 

00:01:31.140 --> 00:01:33.859 align:start position:0%
so you January 21 the answer should be
three<00:01:31.640><c> both</c><00:01:32.640><c> of</c><00:01:32.820><c> these</c><00:01:32.909><c> questions</c><00:01:33.180><c> here</c><00:01:33.780><c> and</c>

00:01:33.859 --> 00:01:33.869 align:start position:0%
three both of these questions here and
 

00:01:33.869 --> 00:01:45.710 align:start position:0%
three both of these questions here and
there<00:01:36.470><c> no</c><00:01:37.470><c> answer</c><00:01:38.840><c> why</c><00:01:41.780><c> it's</c><00:01:42.780><c> advanced</c><00:01:44.540><c> sorry</c>

00:01:45.710 --> 00:01:45.720 align:start position:0%
there no answer why it's advanced sorry
 

00:01:45.720 --> 00:01:58.880 align:start position:0%
there no answer why it's advanced sorry
January<00:01:46.619><c> 18</c><00:01:52.820><c> January</c><00:01:55.070><c> 18</c><00:01:56.070><c> Update</c><00:01:56.820><c> filter</c><00:01:57.890><c> and</c>

00:01:58.880 --> 00:01:58.890 align:start position:0%
January 18 January 18 Update filter and
 

00:01:58.890 --> 00:02:00.560 align:start position:0%
January 18 January 18 Update filter and
see<00:01:59.430><c> if</c><00:01:59.549><c> it</c><00:01:59.640><c> works</c><00:01:59.820><c> no</c><00:02:00.000><c> yes</c>

00:02:00.560 --> 00:02:00.570 align:start position:0%
see if it works no yes
 

00:02:00.570 --> 00:02:02.030 align:start position:0%
see if it works no yes
we<00:02:00.750><c> got</c><00:02:00.930><c> three</c><00:02:01.170><c> over</c><00:02:01.409><c> here</c><00:02:01.439><c> and</c><00:02:01.740><c> three</c><00:02:01.920><c> over</c>

00:02:02.030 --> 00:02:02.040 align:start position:0%
we got three over here and three over
 

00:02:02.040 --> 00:02:05.300 align:start position:0%
we got three over here and three over
here<00:02:02.189><c> it</c><00:02:02.939><c> filters</c><00:02:03.360><c> the</c><00:02:03.390><c> chart</c><00:02:03.899><c> according</c><00:02:04.590><c> to</c>

00:02:05.300 --> 00:02:05.310 align:start position:0%
here it filters the chart according to
 

00:02:05.310 --> 00:02:07.010 align:start position:0%
here it filters the chart according to
your<00:02:05.700><c> needs</c><00:02:06.000><c> and</c><00:02:06.180><c> that's</c><00:02:06.329><c> really</c><00:02:06.509><c> awesome</c><00:02:06.719><c> you</c>

00:02:07.010 --> 00:02:07.020 align:start position:0%
your needs and that's really awesome you
 

00:02:07.020 --> 00:02:08.690 align:start position:0%
your needs and that's really awesome you
can<00:02:07.170><c> share</c><00:02:07.380><c> this</c><00:02:07.530><c> with</c><00:02:07.590><c> anybody</c><00:02:07.950><c> you</c><00:02:08.220><c> want</c><00:02:08.489><c> by</c>

00:02:08.690 --> 00:02:08.700 align:start position:0%
can share this with anybody you want by
 

00:02:08.700 --> 00:02:12.120 align:start position:0%
can share this with anybody you want by
the<00:02:08.729><c> way</c><00:02:08.849><c> so</c><00:02:09.810><c> let's</c><00:02:10.229><c> do</c><00:02:10.470><c> turn</c><00:02:10.950><c> sharing</c><00:02:11.489><c> on</c>

00:02:12.120 --> 00:02:12.130 align:start position:0%
the way so let's do turn sharing on
 

00:02:12.130 --> 00:02:14.880 align:start position:0%
the way so let's do turn sharing on
sharing<00:02:12.400><c> and</c><00:02:12.520><c> embedding</c><00:02:12.940><c> and</c><00:02:13.410><c> yes</c><00:02:14.410><c> I</c><00:02:14.650><c> want</c><00:02:14.830><c> to</c>

00:02:14.880 --> 00:02:14.890 align:start position:0%
sharing and embedding and yes I want to
 

00:02:14.890 --> 00:02:16.860 align:start position:0%
sharing and embedding and yes I want to
be<00:02:14.980><c> able</c><00:02:15.040><c> to</c><00:02:15.190><c> share</c><00:02:15.460><c> this</c><00:02:15.670><c> I</c><00:02:15.910><c> can</c><00:02:16.060><c> bet</c><00:02:16.480><c> it</c><00:02:16.630><c> in</c><00:02:16.750><c> an</c>

00:02:16.860 --> 00:02:16.870 align:start position:0%
be able to share this I can bet it in an
 

00:02:16.870 --> 00:02:19.710 align:start position:0%
be able to share this I can bet it in an
app<00:02:17.020><c> I</c><00:02:17.290><c> can</c><00:02:17.470><c> share</c><00:02:17.740><c> it</c><00:02:17.770><c> via</c><00:02:18.210><c> public</c><00:02:19.210><c> link</c><00:02:19.420><c> and</c><00:02:19.660><c> I</c>

00:02:19.710 --> 00:02:19.720 align:start position:0%
app I can share it via public link and I
 

00:02:19.720 --> 00:02:22.470 align:start position:0%
app I can share it via public link and I
copy<00:02:19.960><c> this</c><00:02:20.260><c> link</c><00:02:20.530><c> but</c><00:02:20.800><c> the</c><00:02:20.920><c> only</c><00:02:21.040><c> problem</c><00:02:21.490><c> once</c>

00:02:22.470 --> 00:02:22.480 align:start position:0%
copy this link but the only problem once
 

00:02:22.480 --> 00:02:28.800 align:start position:0%
copy this link but the only problem once
I<00:02:22.630><c> copy</c><00:02:22.870><c> the</c><00:02:23.110><c> link</c><00:02:23.350><c> is</c><00:02:24.180><c> I</c><00:02:25.180><c> also</c><00:02:25.330><c> oh</c><00:02:27.300><c> yeah</c><00:02:28.300><c> that's</c>

00:02:28.800 --> 00:02:28.810 align:start position:0%
I copy the link is I also oh yeah that's
 

00:02:28.810 --> 00:02:31.200 align:start position:0%
I copy the link is I also oh yeah that's
all<00:02:28.990><c> there</c><00:02:29.170><c> is</c><00:02:29.200><c> to</c><00:02:29.500><c> it</c><00:02:29.620><c> pretty</c><00:02:29.770><c> much</c><00:02:30.120><c> if</c><00:02:31.120><c> you</c>

00:02:31.200 --> 00:02:31.210 align:start position:0%
all there is to it pretty much if you
 

00:02:31.210 --> 00:02:34.110 align:start position:0%
all there is to it pretty much if you
want<00:02:31.300><c> to</c><00:02:31.540><c> paste</c><00:02:31.750><c> in</c><00:02:31.960><c> a</c><00:02:32.050><c> filter</c><00:02:32.290><c> write</c><00:02:32.950><c> a</c><00:02:33.220><c> filter</c>

00:02:34.110 --> 00:02:34.120 align:start position:0%
want to paste in a filter write a filter
 

00:02:34.120 --> 00:02:36.420 align:start position:0%
want to paste in a filter write a filter
for<00:02:34.300><c> example</c><00:02:34.330><c> of</c><00:02:34.810><c> that</c><00:02:34.960><c> specific</c><00:02:35.470><c> day</c><00:02:35.680><c> January</c>

00:02:36.420 --> 00:02:36.430 align:start position:0%
for example of that specific day January
 

00:02:36.430 --> 00:02:39.270 align:start position:0%
for example of that specific day January
18<00:02:36.940><c> you</c><00:02:37.720><c> just</c><00:02:38.020><c> passed</c><00:02:38.320><c> somebody</c><00:02:38.800><c> this</c><00:02:39.070><c> whole</c>

00:02:39.270 --> 00:02:39.280 align:start position:0%
18 you just passed somebody this whole
 

00:02:39.280 --> 00:02:40.800 align:start position:0%
18 you just passed somebody this whole
question<00:02:39.580><c> and</c><00:02:40.030><c> that's</c><00:02:40.150><c> how</c><00:02:40.270><c> you</c><00:02:40.330><c> can</c><00:02:40.600><c> give</c>

00:02:40.800 --> 00:02:40.810 align:start position:0%
question and that's how you can give
 

00:02:40.810 --> 00:02:42.840 align:start position:0%
question and that's how you can give
people<00:02:41.050><c> your</c><00:02:41.380><c> business</c><00:02:41.590><c> analytics</c><00:02:42.400><c> take</c><00:02:42.640><c> on</c>

00:02:42.840 --> 00:02:42.850 align:start position:0%
people your business analytics take on
 

00:02:42.850 --> 00:02:45.330 align:start position:0%
people your business analytics take on
things<00:02:43.060><c> some</c><00:02:43.960><c> of</c><00:02:43.990><c> the</c><00:02:44.170><c> meetings</c><00:02:44.740><c> prepared</c>

00:02:45.330 --> 00:02:45.340 align:start position:0%
things some of the meetings prepared
 

00:02:45.340 --> 00:02:46.980 align:start position:0%
things some of the meetings prepared
with<00:02:45.520><c> data</c><00:02:45.760><c> about</c><00:02:46.150><c> what</c><00:02:46.390><c> exactly</c><00:02:46.870><c> is</c>

00:02:46.980 --> 00:02:46.990 align:start position:0%
with data about what exactly is
 

00:02:46.990 --> 00:02:49.590 align:start position:0%
with data about what exactly is
happening<00:02:47.430><c> as</c><00:02:48.430><c> you</c><00:02:48.550><c> see</c><00:02:48.760><c> I</c><00:02:48.790><c> said</c><00:02:49.180><c> client</c>

00:02:49.590 --> 00:02:49.600 align:start position:0%
happening as you see I said client
 

00:02:49.600 --> 00:02:53.450 align:start position:0%
happening as you see I said client
creation<00:02:50.110><c> date</c><00:02:51.000><c> over</c><00:02:52.000><c> here</c>

00:02:53.450 --> 00:02:53.460 align:start position:0%
creation date over here
 

00:02:53.460 --> 00:02:57.060 align:start position:0%
creation date over here
it's<00:02:54.460><c> really</c><00:02:54.730><c> awesome</c><00:02:55.530><c> now</c><00:02:56.530><c> that</c><00:02:56.710><c> I</c><00:02:56.800><c> pasted</c>

00:02:57.060 --> 00:02:57.070 align:start position:0%
it's really awesome now that I pasted
 

00:02:57.070 --> 00:02:59.250 align:start position:0%
it's really awesome now that I pasted
client<00:02:57.460><c> creation</c><00:02:57.820><c> did</c><00:02:57.970><c> within</c><00:02:58.150><c> the</c><00:02:58.360><c> URL</c><00:02:58.840><c> it's</c>

00:02:59.250 --> 00:02:59.260 align:start position:0%
client creation did within the URL it's
 

00:02:59.260 --> 00:03:00.540 align:start position:0%
client creation did within the URL it's
gonna<00:02:59.470><c> automatically</c><00:02:59.950><c> and</c><00:03:00.130><c> put</c><00:03:00.250><c> that</c><00:03:00.370><c> into</c>

00:03:00.540 --> 00:03:00.550 align:start position:0%
gonna automatically and put that into
 

00:03:00.550 --> 00:03:02.550 align:start position:0%
gonna automatically and put that into
the<00:03:00.730><c> filter</c><00:03:01.120><c> and</c><00:03:01.750><c> it's</c><00:03:01.930><c> gonna</c><00:03:02.050><c> automatically</c>

00:03:02.550 --> 00:03:02.560 align:start position:0%
the filter and it's gonna automatically
 

00:03:02.560 --> 00:03:04.380 align:start position:0%
the filter and it's gonna automatically
do<00:03:02.800><c> this</c><00:03:02.980><c> and</c><00:03:03.280><c> don't</c><00:03:03.670><c> this</c><00:03:03.910><c> gonna</c><00:03:04.060><c> be</c><00:03:04.180><c> shared</c>

00:03:04.380 --> 00:03:04.390 align:start position:0%
do this and don't this gonna be shared
 

00:03:04.390 --> 00:03:06.210 align:start position:0%
do this and don't this gonna be shared
with<00:03:04.510><c> anybody</c><00:03:04.750><c> even</c><00:03:05.200><c> a</c><00:03:05.290><c> non</c><00:03:05.500><c> meta</c><00:03:05.740><c> based</c><00:03:05.980><c> user</c>

00:03:06.210 --> 00:03:06.220 align:start position:0%
with anybody even a non meta based user
 

00:03:06.220 --> 00:03:08.730 align:start position:0%
with anybody even a non meta based user
so<00:03:07.000><c> let's</c><00:03:07.450><c> summarize</c><00:03:07.720><c> over</c><00:03:08.320><c> here</c><00:03:08.470><c> because</c><00:03:08.620><c> we</c>

00:03:08.730 --> 00:03:08.740 align:start position:0%
so let's summarize over here because we
 

00:03:08.740 --> 00:03:10.290 align:start position:0%
so let's summarize over here because we
only<00:03:08.890><c> have</c><00:03:09.040><c> two</c><00:03:09.070><c> minutes</c><00:03:09.430><c> left</c><00:03:09.730><c> in</c><00:03:09.910><c> order</c><00:03:10.060><c> to</c>

00:03:10.290 --> 00:03:10.300 align:start position:0%
only have two minutes left in order to
 

00:03:10.300 --> 00:03:12.720 align:start position:0%
only have two minutes left in order to
wrap<00:03:10.480><c> up</c><00:03:10.690><c> this</c><00:03:11.410><c> is</c><00:03:11.650><c> not</c><00:03:11.860><c> a</c><00:03:11.890><c> base</c><00:03:12.100><c> open</c><00:03:12.520><c> source</c>

00:03:12.720 --> 00:03:12.730 align:start position:0%
wrap up this is not a base open source
 

00:03:12.730 --> 00:03:14.970 align:start position:0%
wrap up this is not a base open source
data<00:03:13.000><c> visualization</c><00:03:13.900><c> gives</c><00:03:14.170><c> you</c><00:03:14.260><c> both</c><00:03:14.470><c> charts</c>

00:03:14.970 --> 00:03:14.980 align:start position:0%
data visualization gives you both charts
 

00:03:14.980 --> 00:03:18.360 align:start position:0%
data visualization gives you both charts
gives<00:03:15.250><c> you</c><00:03:15.490><c> easy</c><00:03:16.420><c> easy</c><00:03:16.960><c> making</c><00:03:17.860><c> a</c><00:03:17.950><c> question</c>

00:03:18.360 --> 00:03:18.370 align:start position:0%
gives you easy easy making a question
 

00:03:18.370 --> 00:03:20.130 align:start position:0%
gives you easy easy making a question
adding<00:03:18.730><c> that</c><00:03:18.820><c> question</c><00:03:19.210><c> to</c><00:03:19.390><c> a</c><00:03:19.420><c> dashboard</c>

00:03:20.130 --> 00:03:20.140 align:start position:0%
adding that question to a dashboard
 

00:03:20.140 --> 00:03:22.620 align:start position:0%
adding that question to a dashboard
everybody<00:03:21.040><c> on</c><00:03:21.220><c> the</c><00:03:21.340><c> team</c><00:03:21.370><c> can</c><00:03:21.940><c> now</c><00:03:22.090><c> see</c><00:03:22.390><c> all</c><00:03:22.600><c> of</c>

00:03:22.620 --> 00:03:22.630 align:start position:0%
everybody on the team can now see all of
 

00:03:22.630 --> 00:03:25.470 align:start position:0%
everybody on the team can now see all of
my<00:03:22.900><c> activity</c><00:03:23.710><c> from</c><00:03:23.890><c> today</c><00:03:24.250><c> these</c><00:03:25.060><c> are</c><00:03:25.240><c> all</c><00:03:25.330><c> the</c>

00:03:25.470 --> 00:03:25.480 align:start position:0%
my activity from today these are all the
 

00:03:25.480 --> 00:03:27.030 align:start position:0%
my activity from today these are all the
things<00:03:25.510><c> I</c><00:03:25.900><c> did</c><00:03:26.140><c> okay</c>

00:03:27.030 --> 00:03:27.040 align:start position:0%
things I did okay
 

00:03:27.040 --> 00:03:29.100 align:start position:0%
things I did okay
I<00:03:27.070><c> created</c><00:03:28.030><c> a</c><00:03:28.150><c> dashboard</c><00:03:28.330><c> I</c><00:03:28.720><c> created</c><00:03:29.020><c> a</c>

00:03:29.100 --> 00:03:29.110 align:start position:0%
I created a dashboard I created a
 

00:03:29.110 --> 00:03:31.320 align:start position:0%
I created a dashboard I created a
question<00:03:29.530><c> and</c><00:03:29.770><c> people</c><00:03:29.920><c> can</c><00:03:30.250><c> go</c><00:03:30.430><c> in</c><00:03:30.610><c> and</c><00:03:30.760><c> look</c>

00:03:31.320 --> 00:03:31.330 align:start position:0%
question and people can go in and look
 

00:03:31.330 --> 00:03:33.060 align:start position:0%
question and people can go in and look
at<00:03:31.450><c> that</c><00:03:31.480><c> my</c><00:03:31.930><c> daily</c><00:03:32.290><c> work</c><00:03:32.500><c> just</c><00:03:32.830><c> for</c><00:03:32.950><c> that</c>

00:03:33.060 --> 00:03:33.070 align:start position:0%
at that my daily work just for that
 

00:03:33.070 --> 00:03:34.800 align:start position:0%
at that my daily work just for that
itself<00:03:33.490><c> just</c><00:03:33.790><c> the</c><00:03:33.910><c> CTO</c><00:03:34.270><c> being</c><00:03:34.600><c> able</c><00:03:34.690><c> to</c>

00:03:34.800 --> 00:03:34.810 align:start position:0%
itself just the CTO being able to
 

00:03:34.810 --> 00:03:36.960 align:start position:0%
itself just the CTO being able to
communicate<00:03:35.380><c> with</c><00:03:36.010><c> the</c><00:03:36.130><c> other</c><00:03:36.250><c> developers</c><00:03:36.790><c> on</c>

00:03:36.960 --> 00:03:36.970 align:start position:0%
communicate with the other developers on
 

00:03:36.970 --> 00:03:38.370 align:start position:0%
communicate with the other developers on
the<00:03:37.030><c> team</c><00:03:37.120><c> somebody</c><00:03:37.600><c> who's</c><00:03:37.840><c> working</c><00:03:38.020><c> on</c><00:03:38.230><c> the</c>

00:03:38.370 --> 00:03:38.380 align:start position:0%
the team somebody who's working on the
 

00:03:38.380 --> 00:03:39.930 align:start position:0%
the team somebody who's working on the
backend<00:03:38.590><c> just</c><00:03:38.980><c> being</c><00:03:39.190><c> able</c><00:03:39.280><c> to</c><00:03:39.430><c> share</c><00:03:39.700><c> data</c>

00:03:39.930 --> 00:03:39.940 align:start position:0%
backend just being able to share data
 

00:03:39.940 --> 00:03:43.440 align:start position:0%
backend just being able to share data
like<00:03:40.240><c> this</c><00:03:40.420><c> and</c><00:03:40.560><c> having</c><00:03:41.560><c> a</c><00:03:41.650><c> discussion</c><00:03:42.300><c> to</c><00:03:43.300><c> see</c>

00:03:43.440 --> 00:03:43.450 align:start position:0%
like this and having a discussion to see
 

00:03:43.450 --> 00:03:44.910 align:start position:0%
like this and having a discussion to see
whether<00:03:43.630><c> you're</c><00:03:43.840><c> on</c><00:03:43.930><c> the</c><00:03:44.050><c> same</c><00:03:44.230><c> page</c><00:03:44.500><c> in</c><00:03:44.709><c> terms</c>

00:03:44.910 --> 00:03:44.920 align:start position:0%
whether you're on the same page in terms
 

00:03:44.920 --> 00:03:47.730 align:start position:0%
whether you're on the same page in terms
of<00:03:45.040><c> the</c><00:03:45.130><c> UX</c><00:03:45.459><c> and</c><00:03:45.700><c> the</c><00:03:45.760><c> goals</c><00:03:46.000><c> and</c><00:03:46.380><c> pulses</c><00:03:47.380><c> again</c>

00:03:47.730 --> 00:03:47.740 align:start position:0%
of the UX and the goals and pulses again
 

00:03:47.740 --> 00:03:50.490 align:start position:0%
of the UX and the goals and pulses again
if<00:03:47.920><c> you</c><00:03:48.480><c> want</c><00:03:49.480><c> to</c><00:03:49.540><c> create</c><00:03:49.750><c> a</c><00:03:49.840><c> pulse</c><00:03:50.080><c> you</c><00:03:50.200><c> just</c>

00:03:50.490 --> 00:03:50.500 align:start position:0%
if you want to create a pulse you just
 

00:03:50.500 --> 00:03:52.590 align:start position:0%
if you want to create a pulse you just
go<00:03:50.620><c> over</c><00:03:50.800><c> here</c><00:03:50.920><c> and</c><00:03:51.340><c> I'm</c><00:03:51.580><c> gonna</c><00:03:51.730><c> go</c><00:03:51.910><c> to</c><00:03:51.970><c> clients</c>

00:03:52.590 --> 00:03:52.600 align:start position:0%
go over here and I'm gonna go to clients
 

00:03:52.600 --> 00:03:56.340 align:start position:0%
go over here and I'm gonna go to clients
write<00:03:53.230><c> all</c><00:03:53.470><c> clients</c><00:03:53.890><c> and</c><00:03:54.870><c> this</c><00:03:55.870><c> way</c><00:03:56.020><c> I</c><00:03:56.050><c> can</c>

00:03:56.340 --> 00:03:56.350 align:start position:0%
write all clients and this way I can
 

00:03:56.350 --> 00:03:57.870 align:start position:0%
write all clients and this way I can
have<00:03:56.380><c> this</c><00:03:56.920><c> sent</c><00:03:57.430><c> to</c><00:03:57.550><c> me</c>

00:03:57.870 --> 00:03:57.880 align:start position:0%
have this sent to me
 

00:03:57.880 --> 00:04:01.560 align:start position:0%
have this sent to me
Alicia<00:03:58.830><c> clients</c><00:03:59.830><c> update</c><00:04:00.370><c> okay</c><00:04:01.090><c> for</c><00:04:01.480><c> that</c>

00:04:01.560 --> 00:04:01.570 align:start position:0%
Alicia clients update okay for that
 

00:04:01.570 --> 00:04:04.020 align:start position:0%
Alicia clients update okay for that
specific<00:04:02.140><c> time</c><00:04:02.410><c> I</c><00:04:02.709><c> don't</c><00:04:03.610><c> know</c><00:04:03.730><c> why</c><00:04:03.880><c> it's</c>

00:04:04.020 --> 00:04:04.030 align:start position:0%
specific time I don't know why it's
 

00:04:04.030 --> 00:04:07.890 align:start position:0%
specific time I don't know why it's
doing<00:04:04.090><c> this</c><00:04:04.420><c> all</c><00:04:04.750><c> clients</c><00:04:05.260><c> orders</c><00:04:06.690><c> see</c><00:04:07.690><c> if</c><00:04:07.780><c> I</c>

00:04:07.890 --> 00:04:07.900 align:start position:0%
doing this all clients orders see if I
 

00:04:07.900 --> 00:04:11.430 align:start position:0%
doing this all clients orders see if I
do<00:04:07.959><c> count</c><00:04:08.410><c> of</c><00:04:08.740><c> clients</c><00:04:09.250><c> no</c><00:04:09.430><c> I</c><00:04:09.840><c> want</c><00:04:10.840><c> the</c><00:04:11.050><c> count</c>

00:04:11.430 --> 00:04:11.440 align:start position:0%
do count of clients no I want the count
 

00:04:11.440 --> 00:04:16.289 align:start position:0%
do count of clients no I want the count
of<00:04:11.620><c> clients</c><00:04:12.420><c> what</c><00:04:13.420><c> does</c><00:04:13.510><c> that</c><00:04:13.540><c> give</c><00:04:13.900><c> me</c><00:04:15.299><c> an</c>

00:04:16.289 --> 00:04:16.299 align:start position:0%
of clients what does that give me an
 

00:04:16.299 --> 00:04:18.320 align:start position:0%
of clients what does that give me an
error<00:04:16.540><c> occurred</c><00:04:16.840><c> while</c><00:04:17.049><c> displaying</c><00:04:17.260><c> the</c><00:04:17.739><c> card</c>

00:04:18.320 --> 00:04:18.330 align:start position:0%
error occurred while displaying the card
 

00:04:18.330 --> 00:04:20.580 align:start position:0%
error occurred while displaying the card
used<00:04:19.330><c> to</c><00:04:19.450><c> be</c><00:04:19.540><c> that</c><00:04:19.690><c> you</c><00:04:19.750><c> didn't</c><00:04:19.989><c> need</c><00:04:20.200><c> it</c><00:04:20.320><c> to</c><00:04:20.440><c> be</c>

00:04:20.580 --> 00:04:20.590 align:start position:0%
used to be that you didn't need it to be
 

00:04:20.590 --> 00:04:22.380 align:start position:0%
used to be that you didn't need it to be
this<00:04:20.830><c> other</c><00:04:21.280><c> stuff</c><00:04:21.640><c> but</c><00:04:21.820><c> it's</c><00:04:22.000><c> pretty</c><00:04:22.150><c> good</c>

00:04:22.380 --> 00:04:22.390 align:start position:0%
this other stuff but it's pretty good
 

00:04:22.390 --> 00:04:23.660 align:start position:0%
this other stuff but it's pretty good
anyways<00:04:22.780><c> okay</c>

00:04:23.660 --> 00:04:23.670 align:start position:0%
anyways okay
 

00:04:23.670 --> 00:04:25.550 align:start position:0%
anyways okay
I<00:04:23.760><c> can</c><00:04:24.060><c> set</c><00:04:24.240><c> it</c><00:04:24.270><c> to</c><00:04:24.390><c> slack</c><00:04:24.720><c> also</c><00:04:25.110><c> if</c><00:04:25.230><c> I</c><00:04:25.380><c> want</c>

00:04:25.550 --> 00:04:25.560 align:start position:0%
I can set it to slack also if I want
 

00:04:25.560 --> 00:04:27.700 align:start position:0%
I can set it to slack also if I want
that's<00:04:25.800><c> pretty</c><00:04:26.070><c> cool</c><00:04:26.310><c> configure</c><00:04:26.910><c> slack</c><00:04:27.180><c> and</c>

00:04:27.700 --> 00:04:27.710 align:start position:0%
that's pretty cool configure slack and
 

00:04:27.710 --> 00:04:29.900 align:start position:0%
that's pretty cool configure slack and
that's<00:04:28.710><c> pretty</c><00:04:28.890><c> much</c><00:04:29.010><c> the</c><00:04:29.160><c> gist</c><00:04:29.520><c> of</c><00:04:29.610><c> it</c><00:04:29.730><c> guys</c>

00:04:29.900 --> 00:04:29.910 align:start position:0%
that's pretty much the gist of it guys
 

00:04:29.910 --> 00:04:31.820 align:start position:0%
that's pretty much the gist of it guys
won't<00:04:30.240><c> click</c><00:04:30.540><c> deploy</c><00:04:30.870><c> you</c><00:04:31.440><c> let</c><00:04:31.650><c> the</c>

00:04:31.820 --> 00:04:31.830 align:start position:0%
won't click deploy you let the
 

00:04:31.830 --> 00:04:34.640 align:start position:0%
won't click deploy you let the
questioned<00:04:32.360><c> you</c><00:04:33.360><c> bet</c><00:04:33.660><c> you've</c><00:04:34.230><c> got</c><00:04:34.260><c> it</c><00:04:34.530><c> up</c>

00:04:34.640 --> 00:04:34.650 align:start position:0%
questioned you bet you've got it up
 

00:04:34.650 --> 00:04:37.100 align:start position:0%
questioned you bet you've got it up
dashboards<00:04:35.550><c> questions</c><00:04:36.360><c> that</c><00:04:36.720><c> you</c><00:04:36.810><c> place</c>

00:04:37.100 --> 00:04:37.110 align:start position:0%
dashboards questions that you place
 

00:04:37.110 --> 00:04:38.690 align:start position:0%
dashboards questions that you place
within<00:04:37.320><c> dashboards</c><00:04:38.190><c> everything</c><00:04:38.520><c> here</c><00:04:38.670><c> is</c>

00:04:38.690 --> 00:04:38.700 align:start position:0%
within dashboards everything here is
 

00:04:38.700 --> 00:04:41.180 align:start position:0%
within dashboards everything here is
saved<00:04:39.240><c> by</c><00:04:39.570><c> the</c><00:04:39.630><c> way</c><00:04:39.810><c> that's</c><00:04:40.680><c> a</c><00:04:40.830><c> really</c><00:04:41.070><c> nice</c>

00:04:41.180 --> 00:04:41.190 align:start position:0%
saved by the way that's a really nice
 

00:04:41.190 --> 00:04:43.130 align:start position:0%
saved by the way that's a really nice
feature<00:04:41.250><c> about</c><00:04:41.760><c> searching</c><00:04:42.420><c> through</c><00:04:42.690><c> here</c>

00:04:43.130 --> 00:04:43.140 align:start position:0%
feature about searching through here
 

00:04:43.140 --> 00:04:44.720 align:start position:0%
feature about searching through here
but<00:04:43.770><c> once</c><00:04:44.010><c> you've</c><00:04:44.160><c> got</c><00:04:44.190><c> about</c><00:04:44.370><c> a</c><00:04:44.520><c> hundred</c>

00:04:44.720 --> 00:04:44.730 align:start position:0%
but once you've got about a hundred
 

00:04:44.730 --> 00:04:47.570 align:start position:0%
but once you've got about a hundred
questions<00:04:45.180><c> in</c><00:04:45.540><c> here</c><00:04:45.690><c> just</c><00:04:46.220><c> search</c><00:04:47.220><c> for</c><00:04:47.250><c> it</c>

00:04:47.570 --> 00:04:47.580 align:start position:0%
questions in here just search for it
 

00:04:47.580 --> 00:04:50.030 align:start position:0%
questions in here just search for it
orders<00:04:48.360><c> and</c><00:04:48.810><c> it'll</c><00:04:49.080><c> go</c><00:04:49.230><c> there</c><00:04:49.500><c> it's</c><00:04:49.890><c> really</c>

00:04:50.030 --> 00:04:50.040 align:start position:0%
orders and it'll go there it's really
 

00:04:50.040 --> 00:04:51.860 align:start position:0%
orders and it'll go there it's really
nice<00:04:50.280><c> so</c><00:04:50.490><c> I</c><00:04:50.520><c> hope</c><00:04:51.120><c> you</c><00:04:51.240><c> guys</c><00:04:51.360><c> enjoyed</c><00:04:51.720><c> my</c>

00:04:51.860 --> 00:04:51.870 align:start position:0%
nice so I hope you guys enjoyed my
 

00:04:51.870 --> 00:04:53.540 align:start position:0%
nice so I hope you guys enjoyed my
medevaced<00:04:52.440><c> dashboard</c><00:04:52.980><c> hopefully</c><00:04:53.340><c> you</c><00:04:53.430><c> can</c>

00:04:53.540 --> 00:04:53.550 align:start position:0%
medevaced dashboard hopefully you can
 

00:04:53.550 --> 00:04:55.970 align:start position:0%
medevaced dashboard hopefully you can
use<00:04:53.610><c> it</c><00:04:53.880><c> within</c><00:04:54.030><c> your</c><00:04:54.360><c> workflow</c><00:04:54.930><c> it's</c><00:04:55.800><c> a</c><00:04:55.830><c> great</c>

00:04:55.970 --> 00:04:55.980 align:start position:0%
use it within your workflow it's a great
 

00:04:55.980 --> 00:04:57.830 align:start position:0%
use it within your workflow it's a great
thing<00:04:56.130><c> for</c><00:04:56.190><c> QA</c><00:04:56.580><c> also</c><00:04:57.030><c> if</c><00:04:57.120><c> you</c><00:04:57.210><c> need</c><00:04:57.330><c> to</c><00:04:57.420><c> manager</c>

00:04:57.830 --> 00:04:57.840 align:start position:0%
thing for QA also if you need to manager
 

00:04:57.840 --> 00:04:59.480 align:start position:0%
thing for QA also if you need to manager
a<00:04:57.870><c> dashboard</c><00:04:58.470><c> or</c><00:04:58.620><c> if</c><00:04:58.710><c> you</c><00:04:58.770><c> just</c><00:04:58.830><c> want</c><00:04:59.160><c> to</c><00:04:59.220><c> send</c>

00:04:59.480 --> 00:04:59.490 align:start position:0%
a dashboard or if you just want to send
 

00:04:59.490 --> 00:05:02.120 align:start position:0%
a dashboard or if you just want to send
data<00:04:59.700><c> to</c><00:04:59.880><c> you</c>

